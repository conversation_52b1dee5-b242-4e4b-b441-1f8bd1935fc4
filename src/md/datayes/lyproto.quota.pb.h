// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: lyproto.quota.proto

#ifndef PROTOBUF_lyproto_2equota_2eproto__INCLUDED
#define PROTOBUF_lyproto_2equota_2eproto__INCLUDED

#include <string>

#include <google/protobuf/stubs/common.h>

#if GOOGLE_PROTOBUF_VERSION < 3001000
#error This file was generated by a newer version of protoc which is
#error incompatible with your Protocol Buffer headers.  Please update
#error your headers.
#endif
#if 3001000 < GOOGLE_PROTOBUF_MIN_PROTOC_VERSION
#error This file was generated by an older version of protoc which is
#error incompatible with your Protocol Buffer headers.  Please
#error regenerate this file with a newer version of protoc.
#endif

#include <google/protobuf/arena.h>
#include <google/protobuf/arenastring.h>
#include <google/protobuf/generated_message_util.h>
#include <google/protobuf/metadata.h>
#include <google/protobuf/message.h>
#include <google/protobuf/repeated_field.h>
#include <google/protobuf/extension_set.h>
#include <google/protobuf/unknown_field_set.h>
// @@protoc_insertion_point(includes)

namespace LYPROTO {
namespace QUOTA {

// Internal implementation detail -- do not call these.
void protobuf_AddDesc_lyproto_2equota_2eproto();
void protobuf_InitDefaults_lyproto_2equota_2eproto();
void protobuf_AssignDesc_lyproto_2equota_2eproto();
void protobuf_ShutdownFile_lyproto_2equota_2eproto();

class FutureMarketData;
class MarketData;
class Transaction;

// ===================================================================

class MarketData : public ::google::protobuf::Message /* @@protoc_insertion_point(class_definition:LYPROTO.QUOTA.MarketData) */ {
 public:
  MarketData();
  virtual ~MarketData();

  MarketData(const MarketData& from);

  inline MarketData& operator=(const MarketData& from) {
    CopyFrom(from);
    return *this;
  }

  inline const ::google::protobuf::UnknownFieldSet& unknown_fields() const {
    return _internal_metadata_.unknown_fields();
  }

  inline ::google::protobuf::UnknownFieldSet* mutable_unknown_fields() {
    return _internal_metadata_.mutable_unknown_fields();
  }

  static const ::google::protobuf::Descriptor* descriptor();
  static const MarketData& default_instance();

  static const MarketData* internal_default_instance();

  void Swap(MarketData* other);

  // implements Message ----------------------------------------------

  inline MarketData* New() const { return New(NULL); }

  MarketData* New(::google::protobuf::Arena* arena) const;
  void CopyFrom(const ::google::protobuf::Message& from);
  void MergeFrom(const ::google::protobuf::Message& from);
  void CopyFrom(const MarketData& from);
  void MergeFrom(const MarketData& from);
  void Clear();
  bool IsInitialized() const;

  size_t ByteSizeLong() const;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input);
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const;
  ::google::protobuf::uint8* InternalSerializeWithCachedSizesToArray(
      bool deterministic, ::google::protobuf::uint8* output) const;
  ::google::protobuf::uint8* SerializeWithCachedSizesToArray(::google::protobuf::uint8* output) const {
    return InternalSerializeWithCachedSizesToArray(false, output);
  }
  int GetCachedSize() const { return _cached_size_; }
  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const;
  void InternalSwap(MarketData* other);
  void UnsafeMergeFrom(const MarketData& from);
  private:
  inline ::google::protobuf::Arena* GetArenaNoVirtual() const {
    return _internal_metadata_.arena();
  }
  inline void* MaybeArenaPtr() const {
    return _internal_metadata_.raw_arena_ptr();
  }
  public:

  ::google::protobuf::Metadata GetMetadata() const;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // required string ExchId = 1;
  bool has_exchid() const;
  void clear_exchid();
  static const int kExchIdFieldNumber = 1;
  const ::std::string& exchid() const;
  void set_exchid(const ::std::string& value);
  void set_exchid(const char* value);
  void set_exchid(const char* value, size_t size);
  ::std::string* mutable_exchid();
  ::std::string* release_exchid();
  void set_allocated_exchid(::std::string* exchid);

  // required string Category = 2;
  bool has_category() const;
  void clear_category();
  static const int kCategoryFieldNumber = 2;
  const ::std::string& category() const;
  void set_category(const ::std::string& value);
  void set_category(const char* value);
  void set_category(const char* value, size_t size);
  ::std::string* mutable_category();
  ::std::string* release_category();
  void set_allocated_category(::std::string* category);

  // required string stkId = 3;
  bool has_stkid() const;
  void clear_stkid();
  static const int kStkIdFieldNumber = 3;
  const ::std::string& stkid() const;
  void set_stkid(const ::std::string& value);
  void set_stkid(const char* value);
  void set_stkid(const char* value, size_t size);
  ::std::string* mutable_stkid();
  ::std::string* release_stkid();
  void set_allocated_stkid(::std::string* stkid);

  // optional string RcvSvrTime = 4;
  bool has_rcvsvrtime() const;
  void clear_rcvsvrtime();
  static const int kRcvSvrTimeFieldNumber = 4;
  const ::std::string& rcvsvrtime() const;
  void set_rcvsvrtime(const ::std::string& value);
  void set_rcvsvrtime(const char* value);
  void set_rcvsvrtime(const char* value, size_t size);
  ::std::string* mutable_rcvsvrtime();
  ::std::string* release_rcvsvrtime();
  void set_allocated_rcvsvrtime(::std::string* rcvsvrtime);

  // optional string PubSvrTime = 5;
  bool has_pubsvrtime() const;
  void clear_pubsvrtime();
  static const int kPubSvrTimeFieldNumber = 5;
  const ::std::string& pubsvrtime() const;
  void set_pubsvrtime(const ::std::string& value);
  void set_pubsvrtime(const char* value);
  void set_pubsvrtime(const char* value, size_t size);
  ::std::string* mutable_pubsvrtime();
  ::std::string* release_pubsvrtime();
  void set_allocated_pubsvrtime(::std::string* pubsvrtime);

  // optional string Status = 6;
  bool has_status() const;
  void clear_status();
  static const int kStatusFieldNumber = 6;
  const ::std::string& status() const;
  void set_status(const ::std::string& value);
  void set_status(const char* value);
  void set_status(const char* value, size_t size);
  ::std::string* mutable_status();
  ::std::string* release_status();
  void set_allocated_status(::std::string* status);

  // optional string ExchTime = 7;
  bool has_exchtime() const;
  void clear_exchtime();
  static const int kExchTimeFieldNumber = 7;
  const ::std::string& exchtime() const;
  void set_exchtime(const ::std::string& value);
  void set_exchtime(const char* value);
  void set_exchtime(const char* value, size_t size);
  ::std::string* mutable_exchtime();
  ::std::string* release_exchtime();
  void set_allocated_exchtime(::std::string* exchtime);

  // optional int64 PreClose = 8;
  bool has_preclose() const;
  void clear_preclose();
  static const int kPreCloseFieldNumber = 8;
  ::google::protobuf::int64 preclose() const;
  void set_preclose(::google::protobuf::int64 value);

  // optional int64 HighLimit = 9;
  bool has_highlimit() const;
  void clear_highlimit();
  static const int kHighLimitFieldNumber = 9;
  ::google::protobuf::int64 highlimit() const;
  void set_highlimit(::google::protobuf::int64 value);

  // optional int64 LowLimit = 10;
  bool has_lowlimit() const;
  void clear_lowlimit();
  static const int kLowLimitFieldNumber = 10;
  ::google::protobuf::int64 lowlimit() const;
  void set_lowlimit(::google::protobuf::int64 value);

  // optional int64 Open = 11;
  bool has_open() const;
  void clear_open();
  static const int kOpenFieldNumber = 11;
  ::google::protobuf::int64 open() const;
  void set_open(::google::protobuf::int64 value);

  // optional int64 High = 12;
  bool has_high() const;
  void clear_high();
  static const int kHighFieldNumber = 12;
  ::google::protobuf::int64 high() const;
  void set_high(::google::protobuf::int64 value);

  // optional int64 Low = 13;
  bool has_low() const;
  void clear_low();
  static const int kLowFieldNumber = 13;
  ::google::protobuf::int64 low() const;
  void set_low(::google::protobuf::int64 value);

  // optional int64 Latest = 14;
  bool has_latest() const;
  void clear_latest();
  static const int kLatestFieldNumber = 14;
  ::google::protobuf::int64 latest() const;
  void set_latest(::google::protobuf::int64 value);

  // optional int32 Knock = 15;
  bool has_knock() const;
  void clear_knock();
  static const int kKnockFieldNumber = 15;
  ::google::protobuf::int32 knock() const;
  void set_knock(::google::protobuf::int32 value);

  // optional int64 Volume = 16;
  bool has_volume() const;
  void clear_volume();
  static const int kVolumeFieldNumber = 16;
  ::google::protobuf::int64 volume() const;
  void set_volume(::google::protobuf::int64 value);

  // optional int64 Value = 17;
  bool has_value() const;
  void clear_value();
  static const int kValueFieldNumber = 17;
  ::google::protobuf::int64 value() const;
  void set_value(::google::protobuf::int64 value);

  // repeated int64 BA = 18 [packed = true];
  int ba_size() const;
  void clear_ba();
  static const int kBAFieldNumber = 18;
  ::google::protobuf::int64 ba(int index) const;
  void set_ba(int index, ::google::protobuf::int64 value);
  void add_ba(::google::protobuf::int64 value);
  const ::google::protobuf::RepeatedField< ::google::protobuf::int64 >&
      ba() const;
  ::google::protobuf::RepeatedField< ::google::protobuf::int64 >*
      mutable_ba();

  // repeated int64 BP = 19 [packed = true];
  int bp_size() const;
  void clear_bp();
  static const int kBPFieldNumber = 19;
  ::google::protobuf::int64 bp(int index) const;
  void set_bp(int index, ::google::protobuf::int64 value);
  void add_bp(::google::protobuf::int64 value);
  const ::google::protobuf::RepeatedField< ::google::protobuf::int64 >&
      bp() const;
  ::google::protobuf::RepeatedField< ::google::protobuf::int64 >*
      mutable_bp();

  // repeated int64 SA = 20 [packed = true];
  int sa_size() const;
  void clear_sa();
  static const int kSAFieldNumber = 20;
  ::google::protobuf::int64 sa(int index) const;
  void set_sa(int index, ::google::protobuf::int64 value);
  void add_sa(::google::protobuf::int64 value);
  const ::google::protobuf::RepeatedField< ::google::protobuf::int64 >&
      sa() const;
  ::google::protobuf::RepeatedField< ::google::protobuf::int64 >*
      mutable_sa();

  // repeated int64 SP = 21 [packed = true];
  int sp_size() const;
  void clear_sp();
  static const int kSPFieldNumber = 21;
  ::google::protobuf::int64 sp(int index) const;
  void set_sp(int index, ::google::protobuf::int64 value);
  void add_sp(::google::protobuf::int64 value);
  const ::google::protobuf::RepeatedField< ::google::protobuf::int64 >&
      sp() const;
  ::google::protobuf::RepeatedField< ::google::protobuf::int64 >*
      mutable_sp();

  // optional int64 TotalBA = 22;
  bool has_totalba() const;
  void clear_totalba();
  static const int kTotalBAFieldNumber = 22;
  ::google::protobuf::int64 totalba() const;
  void set_totalba(::google::protobuf::int64 value);

  // optional int64 WeightedAvgBidPx = 23;
  bool has_weightedavgbidpx() const;
  void clear_weightedavgbidpx();
  static const int kWeightedAvgBidPxFieldNumber = 23;
  ::google::protobuf::int64 weightedavgbidpx() const;
  void set_weightedavgbidpx(::google::protobuf::int64 value);

  // optional int64 TotalSA = 24;
  bool has_totalsa() const;
  void clear_totalsa();
  static const int kTotalSAFieldNumber = 24;
  ::google::protobuf::int64 totalsa() const;
  void set_totalsa(::google::protobuf::int64 value);

  // optional int64 WeightedAvgOfferPx = 25;
  bool has_weightedavgofferpx() const;
  void clear_weightedavgofferpx();
  static const int kWeightedAvgOfferPxFieldNumber = 25;
  ::google::protobuf::int64 weightedavgofferpx() const;
  void set_weightedavgofferpx(::google::protobuf::int64 value);

  // optional int32 IOPV = 26;
  bool has_iopv() const;
  void clear_iopv();
  static const int kIOPVFieldNumber = 26;
  ::google::protobuf::int32 iopv() const;
  void set_iopv(::google::protobuf::int32 value);

  // optional int32 YieldToMaturity = 27;
  bool has_yieldtomaturity() const;
  void clear_yieldtomaturity();
  static const int kYieldToMaturityFieldNumber = 27;
  ::google::protobuf::int32 yieldtomaturity() const;
  void set_yieldtomaturity(::google::protobuf::int32 value);

  // optional int64 TotalWarrantExecQty = 28;
  bool has_totalwarrantexecqty() const;
  void clear_totalwarrantexecqty();
  static const int kTotalWarrantExecQtyFieldNumber = 28;
  ::google::protobuf::int64 totalwarrantexecqty() const;
  void set_totalwarrantexecqty(::google::protobuf::int64 value);

  // optional int64 WarLowerPx = 29;
  bool has_warlowerpx() const;
  void clear_warlowerpx();
  static const int kWarLowerPxFieldNumber = 29;
  ::google::protobuf::int64 warlowerpx() const;
  void set_warlowerpx(::google::protobuf::int64 value);

  // optional int64 WarUpperPx = 30;
  bool has_warupperpx() const;
  void clear_warupperpx();
  static const int kWarUpperPxFieldNumber = 30;
  ::google::protobuf::int64 warupperpx() const;
  void set_warupperpx(::google::protobuf::int64 value);

  // @@protoc_insertion_point(class_scope:LYPROTO.QUOTA.MarketData)
 private:
  inline void set_has_exchid();
  inline void clear_has_exchid();
  inline void set_has_category();
  inline void clear_has_category();
  inline void set_has_stkid();
  inline void clear_has_stkid();
  inline void set_has_rcvsvrtime();
  inline void clear_has_rcvsvrtime();
  inline void set_has_pubsvrtime();
  inline void clear_has_pubsvrtime();
  inline void set_has_status();
  inline void clear_has_status();
  inline void set_has_exchtime();
  inline void clear_has_exchtime();
  inline void set_has_preclose();
  inline void clear_has_preclose();
  inline void set_has_highlimit();
  inline void clear_has_highlimit();
  inline void set_has_lowlimit();
  inline void clear_has_lowlimit();
  inline void set_has_open();
  inline void clear_has_open();
  inline void set_has_high();
  inline void clear_has_high();
  inline void set_has_low();
  inline void clear_has_low();
  inline void set_has_latest();
  inline void clear_has_latest();
  inline void set_has_knock();
  inline void clear_has_knock();
  inline void set_has_volume();
  inline void clear_has_volume();
  inline void set_has_value();
  inline void clear_has_value();
  inline void set_has_totalba();
  inline void clear_has_totalba();
  inline void set_has_weightedavgbidpx();
  inline void clear_has_weightedavgbidpx();
  inline void set_has_totalsa();
  inline void clear_has_totalsa();
  inline void set_has_weightedavgofferpx();
  inline void clear_has_weightedavgofferpx();
  inline void set_has_iopv();
  inline void clear_has_iopv();
  inline void set_has_yieldtomaturity();
  inline void clear_has_yieldtomaturity();
  inline void set_has_totalwarrantexecqty();
  inline void clear_has_totalwarrantexecqty();
  inline void set_has_warlowerpx();
  inline void clear_has_warlowerpx();
  inline void set_has_warupperpx();
  inline void clear_has_warupperpx();

  // helper for ByteSizeLong()
  size_t RequiredFieldsByteSizeFallback() const;

  ::google::protobuf::internal::InternalMetadataWithArena _internal_metadata_;
  ::google::protobuf::internal::HasBits<1> _has_bits_;
  mutable int _cached_size_;
  ::google::protobuf::RepeatedField< ::google::protobuf::int64 > ba_;
  mutable int _ba_cached_byte_size_;
  ::google::protobuf::RepeatedField< ::google::protobuf::int64 > bp_;
  mutable int _bp_cached_byte_size_;
  ::google::protobuf::RepeatedField< ::google::protobuf::int64 > sa_;
  mutable int _sa_cached_byte_size_;
  ::google::protobuf::RepeatedField< ::google::protobuf::int64 > sp_;
  mutable int _sp_cached_byte_size_;
  ::google::protobuf::internal::ArenaStringPtr exchid_;
  ::google::protobuf::internal::ArenaStringPtr category_;
  ::google::protobuf::internal::ArenaStringPtr stkid_;
  ::google::protobuf::internal::ArenaStringPtr rcvsvrtime_;
  ::google::protobuf::internal::ArenaStringPtr pubsvrtime_;
  ::google::protobuf::internal::ArenaStringPtr status_;
  ::google::protobuf::internal::ArenaStringPtr exchtime_;
  ::google::protobuf::int64 preclose_;
  ::google::protobuf::int64 highlimit_;
  ::google::protobuf::int64 lowlimit_;
  ::google::protobuf::int64 open_;
  ::google::protobuf::int64 high_;
  ::google::protobuf::int64 low_;
  ::google::protobuf::int64 latest_;
  ::google::protobuf::int64 volume_;
  ::google::protobuf::int64 value_;
  ::google::protobuf::int32 knock_;
  ::google::protobuf::int32 iopv_;
  ::google::protobuf::int64 totalba_;
  ::google::protobuf::int64 weightedavgbidpx_;
  ::google::protobuf::int64 totalsa_;
  ::google::protobuf::int64 weightedavgofferpx_;
  ::google::protobuf::int64 totalwarrantexecqty_;
  ::google::protobuf::int64 warlowerpx_;
  ::google::protobuf::int64 warupperpx_;
  ::google::protobuf::int32 yieldtomaturity_;
  friend void  protobuf_InitDefaults_lyproto_2equota_2eproto_impl();
  friend void  protobuf_AddDesc_lyproto_2equota_2eproto_impl();
  friend void protobuf_AssignDesc_lyproto_2equota_2eproto();
  friend void protobuf_ShutdownFile_lyproto_2equota_2eproto();

  void InitAsDefaultInstance();
};
extern ::google::protobuf::internal::ExplicitlyConstructed<MarketData> MarketData_default_instance_;

// -------------------------------------------------------------------

class Transaction : public ::google::protobuf::Message /* @@protoc_insertion_point(class_definition:LYPROTO.QUOTA.Transaction) */ {
 public:
  Transaction();
  virtual ~Transaction();

  Transaction(const Transaction& from);

  inline Transaction& operator=(const Transaction& from) {
    CopyFrom(from);
    return *this;
  }

  inline const ::google::protobuf::UnknownFieldSet& unknown_fields() const {
    return _internal_metadata_.unknown_fields();
  }

  inline ::google::protobuf::UnknownFieldSet* mutable_unknown_fields() {
    return _internal_metadata_.mutable_unknown_fields();
  }

  static const ::google::protobuf::Descriptor* descriptor();
  static const Transaction& default_instance();

  static const Transaction* internal_default_instance();

  void Swap(Transaction* other);

  // implements Message ----------------------------------------------

  inline Transaction* New() const { return New(NULL); }

  Transaction* New(::google::protobuf::Arena* arena) const;
  void CopyFrom(const ::google::protobuf::Message& from);
  void MergeFrom(const ::google::protobuf::Message& from);
  void CopyFrom(const Transaction& from);
  void MergeFrom(const Transaction& from);
  void Clear();
  bool IsInitialized() const;

  size_t ByteSizeLong() const;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input);
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const;
  ::google::protobuf::uint8* InternalSerializeWithCachedSizesToArray(
      bool deterministic, ::google::protobuf::uint8* output) const;
  ::google::protobuf::uint8* SerializeWithCachedSizesToArray(::google::protobuf::uint8* output) const {
    return InternalSerializeWithCachedSizesToArray(false, output);
  }
  int GetCachedSize() const { return _cached_size_; }
  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const;
  void InternalSwap(Transaction* other);
  void UnsafeMergeFrom(const Transaction& from);
  private:
  inline ::google::protobuf::Arena* GetArenaNoVirtual() const {
    return _internal_metadata_.arena();
  }
  inline void* MaybeArenaPtr() const {
    return _internal_metadata_.raw_arena_ptr();
  }
  public:

  ::google::protobuf::Metadata GetMetadata() const;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // required string ExchangeID = 1;
  bool has_exchangeid() const;
  void clear_exchangeid();
  static const int kExchangeIDFieldNumber = 1;
  const ::std::string& exchangeid() const;
  void set_exchangeid(const ::std::string& value);
  void set_exchangeid(const char* value);
  void set_exchangeid(const char* value, size_t size);
  ::std::string* mutable_exchangeid();
  ::std::string* release_exchangeid();
  void set_allocated_exchangeid(::std::string* exchangeid);

  // required string Category = 2;
  bool has_category() const;
  void clear_category();
  static const int kCategoryFieldNumber = 2;
  const ::std::string& category() const;
  void set_category(const ::std::string& value);
  void set_category(const char* value);
  void set_category(const char* value, size_t size);
  ::std::string* mutable_category();
  ::std::string* release_category();
  void set_allocated_category(::std::string* category);

  // required string SecurityID = 3;
  bool has_securityid() const;
  void clear_securityid();
  static const int kSecurityIDFieldNumber = 3;
  const ::std::string& securityid() const;
  void set_securityid(const ::std::string& value);
  void set_securityid(const char* value);
  void set_securityid(const char* value, size_t size);
  ::std::string* mutable_securityid();
  ::std::string* release_securityid();
  void set_allocated_securityid(::std::string* securityid);

  // optional string PubSvrTime = 4;
  bool has_pubsvrtime() const;
  void clear_pubsvrtime();
  static const int kPubSvrTimeFieldNumber = 4;
  const ::std::string& pubsvrtime() const;
  void set_pubsvrtime(const ::std::string& value);
  void set_pubsvrtime(const char* value);
  void set_pubsvrtime(const char* value, size_t size);
  ::std::string* mutable_pubsvrtime();
  ::std::string* release_pubsvrtime();
  void set_allocated_pubsvrtime(::std::string* pubsvrtime);

  // optional string TradeIndex = 5;
  bool has_tradeindex() const;
  void clear_tradeindex();
  static const int kTradeIndexFieldNumber = 5;
  const ::std::string& tradeindex() const;
  void set_tradeindex(const ::std::string& value);
  void set_tradeindex(const char* value);
  void set_tradeindex(const char* value, size_t size);
  ::std::string* mutable_tradeindex();
  ::std::string* release_tradeindex();
  void set_allocated_tradeindex(::std::string* tradeindex);

  // optional string TradeTime = 6;
  bool has_tradetime() const;
  void clear_tradetime();
  static const int kTradeTimeFieldNumber = 6;
  const ::std::string& tradetime() const;
  void set_tradetime(const ::std::string& value);
  void set_tradetime(const char* value);
  void set_tradetime(const char* value, size_t size);
  ::std::string* mutable_tradetime();
  ::std::string* release_tradetime();
  void set_allocated_tradetime(::std::string* tradetime);

  // optional int64 TradePrice = 7;
  bool has_tradeprice() const;
  void clear_tradeprice();
  static const int kTradePriceFieldNumber = 7;
  ::google::protobuf::int64 tradeprice() const;
  void set_tradeprice(::google::protobuf::int64 value);

  // optional int32 TradeQty = 8;
  bool has_tradeqty() const;
  void clear_tradeqty();
  static const int kTradeQtyFieldNumber = 8;
  ::google::protobuf::int32 tradeqty() const;
  void set_tradeqty(::google::protobuf::int32 value);

  // optional int64 TradeMoney = 9;
  bool has_trademoney() const;
  void clear_trademoney();
  static const int kTradeMoneyFieldNumber = 9;
  ::google::protobuf::int64 trademoney() const;
  void set_trademoney(::google::protobuf::int64 value);

  // optional string Side = 10;
  bool has_side() const;
  void clear_side();
  static const int kSideFieldNumber = 10;
  const ::std::string& side() const;
  void set_side(const ::std::string& value);
  void set_side(const char* value);
  void set_side(const char* value, size_t size);
  ::std::string* mutable_side();
  ::std::string* release_side();
  void set_allocated_side(::std::string* side);

  // optional string TradeType = 11;
  bool has_tradetype() const;
  void clear_tradetype();
  static const int kTradeTypeFieldNumber = 11;
  const ::std::string& tradetype() const;
  void set_tradetype(const ::std::string& value);
  void set_tradetype(const char* value);
  void set_tradetype(const char* value, size_t size);
  ::std::string* mutable_tradetype();
  ::std::string* release_tradetype();
  void set_allocated_tradetype(::std::string* tradetype);

  // optional string TradeCode = 12;
  bool has_tradecode() const;
  void clear_tradecode();
  static const int kTradeCodeFieldNumber = 12;
  const ::std::string& tradecode() const;
  void set_tradecode(const ::std::string& value);
  void set_tradecode(const char* value);
  void set_tradecode(const char* value, size_t size);
  ::std::string* mutable_tradecode();
  ::std::string* release_tradecode();
  void set_allocated_tradecode(::std::string* tradecode);

  // optional string OfferIndex = 13;
  bool has_offerindex() const;
  void clear_offerindex();
  static const int kOfferIndexFieldNumber = 13;
  const ::std::string& offerindex() const;
  void set_offerindex(const ::std::string& value);
  void set_offerindex(const char* value);
  void set_offerindex(const char* value, size_t size);
  ::std::string* mutable_offerindex();
  ::std::string* release_offerindex();
  void set_allocated_offerindex(::std::string* offerindex);

  // optional string BidIndex = 14;
  bool has_bidindex() const;
  void clear_bidindex();
  static const int kBidIndexFieldNumber = 14;
  const ::std::string& bidindex() const;
  void set_bidindex(const ::std::string& value);
  void set_bidindex(const char* value);
  void set_bidindex(const char* value, size_t size);
  ::std::string* mutable_bidindex();
  ::std::string* release_bidindex();
  void set_allocated_bidindex(::std::string* bidindex);

  // optional string reserve = 15;
  bool has_reserve() const;
  void clear_reserve();
  static const int kReserveFieldNumber = 15;
  const ::std::string& reserve() const;
  void set_reserve(const ::std::string& value);
  void set_reserve(const char* value);
  void set_reserve(const char* value, size_t size);
  ::std::string* mutable_reserve();
  ::std::string* release_reserve();
  void set_allocated_reserve(::std::string* reserve);

  // @@protoc_insertion_point(class_scope:LYPROTO.QUOTA.Transaction)
 private:
  inline void set_has_exchangeid();
  inline void clear_has_exchangeid();
  inline void set_has_category();
  inline void clear_has_category();
  inline void set_has_securityid();
  inline void clear_has_securityid();
  inline void set_has_pubsvrtime();
  inline void clear_has_pubsvrtime();
  inline void set_has_tradeindex();
  inline void clear_has_tradeindex();
  inline void set_has_tradetime();
  inline void clear_has_tradetime();
  inline void set_has_tradeprice();
  inline void clear_has_tradeprice();
  inline void set_has_tradeqty();
  inline void clear_has_tradeqty();
  inline void set_has_trademoney();
  inline void clear_has_trademoney();
  inline void set_has_side();
  inline void clear_has_side();
  inline void set_has_tradetype();
  inline void clear_has_tradetype();
  inline void set_has_tradecode();
  inline void clear_has_tradecode();
  inline void set_has_offerindex();
  inline void clear_has_offerindex();
  inline void set_has_bidindex();
  inline void clear_has_bidindex();
  inline void set_has_reserve();
  inline void clear_has_reserve();

  // helper for ByteSizeLong()
  size_t RequiredFieldsByteSizeFallback() const;

  ::google::protobuf::internal::InternalMetadataWithArena _internal_metadata_;
  ::google::protobuf::internal::HasBits<1> _has_bits_;
  mutable int _cached_size_;
  ::google::protobuf::internal::ArenaStringPtr exchangeid_;
  ::google::protobuf::internal::ArenaStringPtr category_;
  ::google::protobuf::internal::ArenaStringPtr securityid_;
  ::google::protobuf::internal::ArenaStringPtr pubsvrtime_;
  ::google::protobuf::internal::ArenaStringPtr tradeindex_;
  ::google::protobuf::internal::ArenaStringPtr tradetime_;
  ::google::protobuf::internal::ArenaStringPtr side_;
  ::google::protobuf::internal::ArenaStringPtr tradetype_;
  ::google::protobuf::internal::ArenaStringPtr tradecode_;
  ::google::protobuf::internal::ArenaStringPtr offerindex_;
  ::google::protobuf::internal::ArenaStringPtr bidindex_;
  ::google::protobuf::internal::ArenaStringPtr reserve_;
  ::google::protobuf::int64 tradeprice_;
  ::google::protobuf::int64 trademoney_;
  ::google::protobuf::int32 tradeqty_;
  friend void  protobuf_InitDefaults_lyproto_2equota_2eproto_impl();
  friend void  protobuf_AddDesc_lyproto_2equota_2eproto_impl();
  friend void protobuf_AssignDesc_lyproto_2equota_2eproto();
  friend void protobuf_ShutdownFile_lyproto_2equota_2eproto();

  void InitAsDefaultInstance();
};
extern ::google::protobuf::internal::ExplicitlyConstructed<Transaction> Transaction_default_instance_;

// -------------------------------------------------------------------

class FutureMarketData : public ::google::protobuf::Message /* @@protoc_insertion_point(class_definition:LYPROTO.QUOTA.FutureMarketData) */ {
 public:
  FutureMarketData();
  virtual ~FutureMarketData();

  FutureMarketData(const FutureMarketData& from);

  inline FutureMarketData& operator=(const FutureMarketData& from) {
    CopyFrom(from);
    return *this;
  }

  inline const ::google::protobuf::UnknownFieldSet& unknown_fields() const {
    return _internal_metadata_.unknown_fields();
  }

  inline ::google::protobuf::UnknownFieldSet* mutable_unknown_fields() {
    return _internal_metadata_.mutable_unknown_fields();
  }

  static const ::google::protobuf::Descriptor* descriptor();
  static const FutureMarketData& default_instance();

  static const FutureMarketData* internal_default_instance();

  void Swap(FutureMarketData* other);

  // implements Message ----------------------------------------------

  inline FutureMarketData* New() const { return New(NULL); }

  FutureMarketData* New(::google::protobuf::Arena* arena) const;
  void CopyFrom(const ::google::protobuf::Message& from);
  void MergeFrom(const ::google::protobuf::Message& from);
  void CopyFrom(const FutureMarketData& from);
  void MergeFrom(const FutureMarketData& from);
  void Clear();
  bool IsInitialized() const;

  size_t ByteSizeLong() const;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input);
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const;
  ::google::protobuf::uint8* InternalSerializeWithCachedSizesToArray(
      bool deterministic, ::google::protobuf::uint8* output) const;
  ::google::protobuf::uint8* SerializeWithCachedSizesToArray(::google::protobuf::uint8* output) const {
    return InternalSerializeWithCachedSizesToArray(false, output);
  }
  int GetCachedSize() const { return _cached_size_; }
  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const;
  void InternalSwap(FutureMarketData* other);
  void UnsafeMergeFrom(const FutureMarketData& from);
  private:
  inline ::google::protobuf::Arena* GetArenaNoVirtual() const {
    return _internal_metadata_.arena();
  }
  inline void* MaybeArenaPtr() const {
    return _internal_metadata_.raw_arena_ptr();
  }
  public:

  ::google::protobuf::Metadata GetMetadata() const;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // required string ExchId = 1;
  bool has_exchid() const;
  void clear_exchid();
  static const int kExchIdFieldNumber = 1;
  const ::std::string& exchid() const;
  void set_exchid(const ::std::string& value);
  void set_exchid(const char* value);
  void set_exchid(const char* value, size_t size);
  ::std::string* mutable_exchid();
  ::std::string* release_exchid();
  void set_allocated_exchid(::std::string* exchid);

  // required string Category = 2;
  bool has_category() const;
  void clear_category();
  static const int kCategoryFieldNumber = 2;
  const ::std::string& category() const;
  void set_category(const ::std::string& value);
  void set_category(const char* value);
  void set_category(const char* value, size_t size);
  ::std::string* mutable_category();
  ::std::string* release_category();
  void set_allocated_category(::std::string* category);

  // required string stkId = 3;
  bool has_stkid() const;
  void clear_stkid();
  static const int kStkIdFieldNumber = 3;
  const ::std::string& stkid() const;
  void set_stkid(const ::std::string& value);
  void set_stkid(const char* value);
  void set_stkid(const char* value, size_t size);
  ::std::string* mutable_stkid();
  ::std::string* release_stkid();
  void set_allocated_stkid(::std::string* stkid);

  // optional string RcvSvrTime = 4;
  bool has_rcvsvrtime() const;
  void clear_rcvsvrtime();
  static const int kRcvSvrTimeFieldNumber = 4;
  const ::std::string& rcvsvrtime() const;
  void set_rcvsvrtime(const ::std::string& value);
  void set_rcvsvrtime(const char* value);
  void set_rcvsvrtime(const char* value, size_t size);
  ::std::string* mutable_rcvsvrtime();
  ::std::string* release_rcvsvrtime();
  void set_allocated_rcvsvrtime(::std::string* rcvsvrtime);

  // optional string PubSvrTime = 5;
  bool has_pubsvrtime() const;
  void clear_pubsvrtime();
  static const int kPubSvrTimeFieldNumber = 5;
  const ::std::string& pubsvrtime() const;
  void set_pubsvrtime(const ::std::string& value);
  void set_pubsvrtime(const char* value);
  void set_pubsvrtime(const char* value, size_t size);
  ::std::string* mutable_pubsvrtime();
  ::std::string* release_pubsvrtime();
  void set_allocated_pubsvrtime(::std::string* pubsvrtime);

  // optional string Status = 6;
  bool has_status() const;
  void clear_status();
  static const int kStatusFieldNumber = 6;
  const ::std::string& status() const;
  void set_status(const ::std::string& value);
  void set_status(const char* value);
  void set_status(const char* value, size_t size);
  ::std::string* mutable_status();
  ::std::string* release_status();
  void set_allocated_status(::std::string* status);

  // optional string ExchTime = 7;
  bool has_exchtime() const;
  void clear_exchtime();
  static const int kExchTimeFieldNumber = 7;
  const ::std::string& exchtime() const;
  void set_exchtime(const ::std::string& value);
  void set_exchtime(const char* value);
  void set_exchtime(const char* value, size_t size);
  ::std::string* mutable_exchtime();
  ::std::string* release_exchtime();
  void set_allocated_exchtime(::std::string* exchtime);

  // optional string TradeDate = 8;
  bool has_tradedate() const;
  void clear_tradedate();
  static const int kTradeDateFieldNumber = 8;
  const ::std::string& tradedate() const;
  void set_tradedate(const ::std::string& value);
  void set_tradedate(const char* value);
  void set_tradedate(const char* value, size_t size);
  ::std::string* mutable_tradedate();
  ::std::string* release_tradedate();
  void set_allocated_tradedate(::std::string* tradedate);

  // optional int64 PreClose = 9;
  bool has_preclose() const;
  void clear_preclose();
  static const int kPreCloseFieldNumber = 9;
  ::google::protobuf::int64 preclose() const;
  void set_preclose(::google::protobuf::int64 value);

  // optional int64 PreSettle = 10;
  bool has_presettle() const;
  void clear_presettle();
  static const int kPreSettleFieldNumber = 10;
  ::google::protobuf::int64 presettle() const;
  void set_presettle(::google::protobuf::int64 value);

  // optional int32 PreOpenPos = 11;
  bool has_preopenpos() const;
  void clear_preopenpos();
  static const int kPreOpenPosFieldNumber = 11;
  ::google::protobuf::int32 preopenpos() const;
  void set_preopenpos(::google::protobuf::int32 value);

  // optional int64 HighLimit = 12;
  bool has_highlimit() const;
  void clear_highlimit();
  static const int kHighLimitFieldNumber = 12;
  ::google::protobuf::int64 highlimit() const;
  void set_highlimit(::google::protobuf::int64 value);

  // optional int64 LowLimit = 13;
  bool has_lowlimit() const;
  void clear_lowlimit();
  static const int kLowLimitFieldNumber = 13;
  ::google::protobuf::int64 lowlimit() const;
  void set_lowlimit(::google::protobuf::int64 value);

  // optional int64 Open = 14;
  bool has_open() const;
  void clear_open();
  static const int kOpenFieldNumber = 14;
  ::google::protobuf::int64 open() const;
  void set_open(::google::protobuf::int64 value);

  // optional int64 Latest = 15;
  bool has_latest() const;
  void clear_latest();
  static const int kLatestFieldNumber = 15;
  ::google::protobuf::int64 latest() const;
  void set_latest(::google::protobuf::int64 value);

  // optional int64 High = 16;
  bool has_high() const;
  void clear_high();
  static const int kHighFieldNumber = 16;
  ::google::protobuf::int64 high() const;
  void set_high(::google::protobuf::int64 value);

  // optional int64 Low = 17;
  bool has_low() const;
  void clear_low();
  static const int kLowFieldNumber = 17;
  ::google::protobuf::int64 low() const;
  void set_low(::google::protobuf::int64 value);

  // optional int64 Settle = 18;
  bool has_settle() const;
  void clear_settle();
  static const int kSettleFieldNumber = 18;
  ::google::protobuf::int64 settle() const;
  void set_settle(::google::protobuf::int64 value);

  // optional int32 LatestVolume = 19;
  bool has_latestvolume() const;
  void clear_latestvolume();
  static const int kLatestVolumeFieldNumber = 19;
  ::google::protobuf::int32 latestvolume() const;
  void set_latestvolume(::google::protobuf::int32 value);

  // optional int32 Volume = 20;
  bool has_volume() const;
  void clear_volume();
  static const int kVolumeFieldNumber = 20;
  ::google::protobuf::int32 volume() const;
  void set_volume(::google::protobuf::int32 value);

  // optional int32 OpenPos = 21;
  bool has_openpos() const;
  void clear_openpos();
  static const int kOpenPosFieldNumber = 21;
  ::google::protobuf::int32 openpos() const;
  void set_openpos(::google::protobuf::int32 value);

  // optional int64 Value = 22;
  bool has_value() const;
  void clear_value();
  static const int kValueFieldNumber = 22;
  ::google::protobuf::int64 value() const;
  void set_value(::google::protobuf::int64 value);

  // repeated int32 BA = 23 [packed = true];
  int ba_size() const;
  void clear_ba();
  static const int kBAFieldNumber = 23;
  ::google::protobuf::int32 ba(int index) const;
  void set_ba(int index, ::google::protobuf::int32 value);
  void add_ba(::google::protobuf::int32 value);
  const ::google::protobuf::RepeatedField< ::google::protobuf::int32 >&
      ba() const;
  ::google::protobuf::RepeatedField< ::google::protobuf::int32 >*
      mutable_ba();

  // repeated int64 BP = 24 [packed = true];
  int bp_size() const;
  void clear_bp();
  static const int kBPFieldNumber = 24;
  ::google::protobuf::int64 bp(int index) const;
  void set_bp(int index, ::google::protobuf::int64 value);
  void add_bp(::google::protobuf::int64 value);
  const ::google::protobuf::RepeatedField< ::google::protobuf::int64 >&
      bp() const;
  ::google::protobuf::RepeatedField< ::google::protobuf::int64 >*
      mutable_bp();

  // repeated int32 SA = 25 [packed = true];
  int sa_size() const;
  void clear_sa();
  static const int kSAFieldNumber = 25;
  ::google::protobuf::int32 sa(int index) const;
  void set_sa(int index, ::google::protobuf::int32 value);
  void add_sa(::google::protobuf::int32 value);
  const ::google::protobuf::RepeatedField< ::google::protobuf::int32 >&
      sa() const;
  ::google::protobuf::RepeatedField< ::google::protobuf::int32 >*
      mutable_sa();

  // repeated int64 SP = 26 [packed = true];
  int sp_size() const;
  void clear_sp();
  static const int kSPFieldNumber = 26;
  ::google::protobuf::int64 sp(int index) const;
  void set_sp(int index, ::google::protobuf::int64 value);
  void add_sp(::google::protobuf::int64 value);
  const ::google::protobuf::RepeatedField< ::google::protobuf::int64 >&
      sp() const;
  ::google::protobuf::RepeatedField< ::google::protobuf::int64 >*
      mutable_sp();

  // optional int32 PreDelta = 27;
  bool has_predelta() const;
  void clear_predelta();
  static const int kPreDeltaFieldNumber = 27;
  ::google::protobuf::int32 predelta() const;
  void set_predelta(::google::protobuf::int32 value);

  // optional int32 CurDelta = 28;
  bool has_curdelta() const;
  void clear_curdelta();
  static const int kCurDeltaFieldNumber = 28;
  ::google::protobuf::int32 curdelta() const;
  void set_curdelta(::google::protobuf::int32 value);

  // @@protoc_insertion_point(class_scope:LYPROTO.QUOTA.FutureMarketData)
 private:
  inline void set_has_exchid();
  inline void clear_has_exchid();
  inline void set_has_category();
  inline void clear_has_category();
  inline void set_has_stkid();
  inline void clear_has_stkid();
  inline void set_has_rcvsvrtime();
  inline void clear_has_rcvsvrtime();
  inline void set_has_pubsvrtime();
  inline void clear_has_pubsvrtime();
  inline void set_has_status();
  inline void clear_has_status();
  inline void set_has_exchtime();
  inline void clear_has_exchtime();
  inline void set_has_tradedate();
  inline void clear_has_tradedate();
  inline void set_has_preclose();
  inline void clear_has_preclose();
  inline void set_has_presettle();
  inline void clear_has_presettle();
  inline void set_has_preopenpos();
  inline void clear_has_preopenpos();
  inline void set_has_highlimit();
  inline void clear_has_highlimit();
  inline void set_has_lowlimit();
  inline void clear_has_lowlimit();
  inline void set_has_open();
  inline void clear_has_open();
  inline void set_has_latest();
  inline void clear_has_latest();
  inline void set_has_high();
  inline void clear_has_high();
  inline void set_has_low();
  inline void clear_has_low();
  inline void set_has_settle();
  inline void clear_has_settle();
  inline void set_has_latestvolume();
  inline void clear_has_latestvolume();
  inline void set_has_volume();
  inline void clear_has_volume();
  inline void set_has_openpos();
  inline void clear_has_openpos();
  inline void set_has_value();
  inline void clear_has_value();
  inline void set_has_predelta();
  inline void clear_has_predelta();
  inline void set_has_curdelta();
  inline void clear_has_curdelta();

  // helper for ByteSizeLong()
  size_t RequiredFieldsByteSizeFallback() const;

  ::google::protobuf::internal::InternalMetadataWithArena _internal_metadata_;
  ::google::protobuf::internal::HasBits<1> _has_bits_;
  mutable int _cached_size_;
  ::google::protobuf::RepeatedField< ::google::protobuf::int32 > ba_;
  mutable int _ba_cached_byte_size_;
  ::google::protobuf::RepeatedField< ::google::protobuf::int64 > bp_;
  mutable int _bp_cached_byte_size_;
  ::google::protobuf::RepeatedField< ::google::protobuf::int32 > sa_;
  mutable int _sa_cached_byte_size_;
  ::google::protobuf::RepeatedField< ::google::protobuf::int64 > sp_;
  mutable int _sp_cached_byte_size_;
  ::google::protobuf::internal::ArenaStringPtr exchid_;
  ::google::protobuf::internal::ArenaStringPtr category_;
  ::google::protobuf::internal::ArenaStringPtr stkid_;
  ::google::protobuf::internal::ArenaStringPtr rcvsvrtime_;
  ::google::protobuf::internal::ArenaStringPtr pubsvrtime_;
  ::google::protobuf::internal::ArenaStringPtr status_;
  ::google::protobuf::internal::ArenaStringPtr exchtime_;
  ::google::protobuf::internal::ArenaStringPtr tradedate_;
  ::google::protobuf::int64 preclose_;
  ::google::protobuf::int64 presettle_;
  ::google::protobuf::int64 highlimit_;
  ::google::protobuf::int64 lowlimit_;
  ::google::protobuf::int64 open_;
  ::google::protobuf::int64 latest_;
  ::google::protobuf::int32 preopenpos_;
  ::google::protobuf::int32 latestvolume_;
  ::google::protobuf::int64 high_;
  ::google::protobuf::int64 low_;
  ::google::protobuf::int64 settle_;
  ::google::protobuf::int32 volume_;
  ::google::protobuf::int32 openpos_;
  ::google::protobuf::int64 value_;
  ::google::protobuf::int32 predelta_;
  ::google::protobuf::int32 curdelta_;
  friend void  protobuf_InitDefaults_lyproto_2equota_2eproto_impl();
  friend void  protobuf_AddDesc_lyproto_2equota_2eproto_impl();
  friend void protobuf_AssignDesc_lyproto_2equota_2eproto();
  friend void protobuf_ShutdownFile_lyproto_2equota_2eproto();

  void InitAsDefaultInstance();
};
extern ::google::protobuf::internal::ExplicitlyConstructed<FutureMarketData> FutureMarketData_default_instance_;

// ===================================================================


// ===================================================================

#if !PROTOBUF_INLINE_NOT_IN_HEADERS
// MarketData

// required string ExchId = 1;
inline bool MarketData::has_exchid() const {
  return (_has_bits_[0] & 0x00000001u) != 0;
}
inline void MarketData::set_has_exchid() {
  _has_bits_[0] |= 0x00000001u;
}
inline void MarketData::clear_has_exchid() {
  _has_bits_[0] &= ~0x00000001u;
}
inline void MarketData::clear_exchid() {
  exchid_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  clear_has_exchid();
}
inline const ::std::string& MarketData::exchid() const {
  // @@protoc_insertion_point(field_get:LYPROTO.QUOTA.MarketData.ExchId)
  return exchid_.GetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void MarketData::set_exchid(const ::std::string& value) {
  set_has_exchid();
  exchid_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:LYPROTO.QUOTA.MarketData.ExchId)
}
inline void MarketData::set_exchid(const char* value) {
  set_has_exchid();
  exchid_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:LYPROTO.QUOTA.MarketData.ExchId)
}
inline void MarketData::set_exchid(const char* value, size_t size) {
  set_has_exchid();
  exchid_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:LYPROTO.QUOTA.MarketData.ExchId)
}
inline ::std::string* MarketData::mutable_exchid() {
  set_has_exchid();
  // @@protoc_insertion_point(field_mutable:LYPROTO.QUOTA.MarketData.ExchId)
  return exchid_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline ::std::string* MarketData::release_exchid() {
  // @@protoc_insertion_point(field_release:LYPROTO.QUOTA.MarketData.ExchId)
  clear_has_exchid();
  return exchid_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void MarketData::set_allocated_exchid(::std::string* exchid) {
  if (exchid != NULL) {
    set_has_exchid();
  } else {
    clear_has_exchid();
  }
  exchid_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), exchid);
  // @@protoc_insertion_point(field_set_allocated:LYPROTO.QUOTA.MarketData.ExchId)
}

// required string Category = 2;
inline bool MarketData::has_category() const {
  return (_has_bits_[0] & 0x00000002u) != 0;
}
inline void MarketData::set_has_category() {
  _has_bits_[0] |= 0x00000002u;
}
inline void MarketData::clear_has_category() {
  _has_bits_[0] &= ~0x00000002u;
}
inline void MarketData::clear_category() {
  category_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  clear_has_category();
}
inline const ::std::string& MarketData::category() const {
  // @@protoc_insertion_point(field_get:LYPROTO.QUOTA.MarketData.Category)
  return category_.GetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void MarketData::set_category(const ::std::string& value) {
  set_has_category();
  category_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:LYPROTO.QUOTA.MarketData.Category)
}
inline void MarketData::set_category(const char* value) {
  set_has_category();
  category_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:LYPROTO.QUOTA.MarketData.Category)
}
inline void MarketData::set_category(const char* value, size_t size) {
  set_has_category();
  category_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:LYPROTO.QUOTA.MarketData.Category)
}
inline ::std::string* MarketData::mutable_category() {
  set_has_category();
  // @@protoc_insertion_point(field_mutable:LYPROTO.QUOTA.MarketData.Category)
  return category_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline ::std::string* MarketData::release_category() {
  // @@protoc_insertion_point(field_release:LYPROTO.QUOTA.MarketData.Category)
  clear_has_category();
  return category_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void MarketData::set_allocated_category(::std::string* category) {
  if (category != NULL) {
    set_has_category();
  } else {
    clear_has_category();
  }
  category_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), category);
  // @@protoc_insertion_point(field_set_allocated:LYPROTO.QUOTA.MarketData.Category)
}

// required string stkId = 3;
inline bool MarketData::has_stkid() const {
  return (_has_bits_[0] & 0x00000004u) != 0;
}
inline void MarketData::set_has_stkid() {
  _has_bits_[0] |= 0x00000004u;
}
inline void MarketData::clear_has_stkid() {
  _has_bits_[0] &= ~0x00000004u;
}
inline void MarketData::clear_stkid() {
  stkid_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  clear_has_stkid();
}
inline const ::std::string& MarketData::stkid() const {
  // @@protoc_insertion_point(field_get:LYPROTO.QUOTA.MarketData.stkId)
  return stkid_.GetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void MarketData::set_stkid(const ::std::string& value) {
  set_has_stkid();
  stkid_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:LYPROTO.QUOTA.MarketData.stkId)
}
inline void MarketData::set_stkid(const char* value) {
  set_has_stkid();
  stkid_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:LYPROTO.QUOTA.MarketData.stkId)
}
inline void MarketData::set_stkid(const char* value, size_t size) {
  set_has_stkid();
  stkid_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:LYPROTO.QUOTA.MarketData.stkId)
}
inline ::std::string* MarketData::mutable_stkid() {
  set_has_stkid();
  // @@protoc_insertion_point(field_mutable:LYPROTO.QUOTA.MarketData.stkId)
  return stkid_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline ::std::string* MarketData::release_stkid() {
  // @@protoc_insertion_point(field_release:LYPROTO.QUOTA.MarketData.stkId)
  clear_has_stkid();
  return stkid_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void MarketData::set_allocated_stkid(::std::string* stkid) {
  if (stkid != NULL) {
    set_has_stkid();
  } else {
    clear_has_stkid();
  }
  stkid_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), stkid);
  // @@protoc_insertion_point(field_set_allocated:LYPROTO.QUOTA.MarketData.stkId)
}

// optional string RcvSvrTime = 4;
inline bool MarketData::has_rcvsvrtime() const {
  return (_has_bits_[0] & 0x00000008u) != 0;
}
inline void MarketData::set_has_rcvsvrtime() {
  _has_bits_[0] |= 0x00000008u;
}
inline void MarketData::clear_has_rcvsvrtime() {
  _has_bits_[0] &= ~0x00000008u;
}
inline void MarketData::clear_rcvsvrtime() {
  rcvsvrtime_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  clear_has_rcvsvrtime();
}
inline const ::std::string& MarketData::rcvsvrtime() const {
  // @@protoc_insertion_point(field_get:LYPROTO.QUOTA.MarketData.RcvSvrTime)
  return rcvsvrtime_.GetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void MarketData::set_rcvsvrtime(const ::std::string& value) {
  set_has_rcvsvrtime();
  rcvsvrtime_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:LYPROTO.QUOTA.MarketData.RcvSvrTime)
}
inline void MarketData::set_rcvsvrtime(const char* value) {
  set_has_rcvsvrtime();
  rcvsvrtime_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:LYPROTO.QUOTA.MarketData.RcvSvrTime)
}
inline void MarketData::set_rcvsvrtime(const char* value, size_t size) {
  set_has_rcvsvrtime();
  rcvsvrtime_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:LYPROTO.QUOTA.MarketData.RcvSvrTime)
}
inline ::std::string* MarketData::mutable_rcvsvrtime() {
  set_has_rcvsvrtime();
  // @@protoc_insertion_point(field_mutable:LYPROTO.QUOTA.MarketData.RcvSvrTime)
  return rcvsvrtime_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline ::std::string* MarketData::release_rcvsvrtime() {
  // @@protoc_insertion_point(field_release:LYPROTO.QUOTA.MarketData.RcvSvrTime)
  clear_has_rcvsvrtime();
  return rcvsvrtime_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void MarketData::set_allocated_rcvsvrtime(::std::string* rcvsvrtime) {
  if (rcvsvrtime != NULL) {
    set_has_rcvsvrtime();
  } else {
    clear_has_rcvsvrtime();
  }
  rcvsvrtime_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), rcvsvrtime);
  // @@protoc_insertion_point(field_set_allocated:LYPROTO.QUOTA.MarketData.RcvSvrTime)
}

// optional string PubSvrTime = 5;
inline bool MarketData::has_pubsvrtime() const {
  return (_has_bits_[0] & 0x00000010u) != 0;
}
inline void MarketData::set_has_pubsvrtime() {
  _has_bits_[0] |= 0x00000010u;
}
inline void MarketData::clear_has_pubsvrtime() {
  _has_bits_[0] &= ~0x00000010u;
}
inline void MarketData::clear_pubsvrtime() {
  pubsvrtime_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  clear_has_pubsvrtime();
}
inline const ::std::string& MarketData::pubsvrtime() const {
  // @@protoc_insertion_point(field_get:LYPROTO.QUOTA.MarketData.PubSvrTime)
  return pubsvrtime_.GetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void MarketData::set_pubsvrtime(const ::std::string& value) {
  set_has_pubsvrtime();
  pubsvrtime_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:LYPROTO.QUOTA.MarketData.PubSvrTime)
}
inline void MarketData::set_pubsvrtime(const char* value) {
  set_has_pubsvrtime();
  pubsvrtime_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:LYPROTO.QUOTA.MarketData.PubSvrTime)
}
inline void MarketData::set_pubsvrtime(const char* value, size_t size) {
  set_has_pubsvrtime();
  pubsvrtime_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:LYPROTO.QUOTA.MarketData.PubSvrTime)
}
inline ::std::string* MarketData::mutable_pubsvrtime() {
  set_has_pubsvrtime();
  // @@protoc_insertion_point(field_mutable:LYPROTO.QUOTA.MarketData.PubSvrTime)
  return pubsvrtime_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline ::std::string* MarketData::release_pubsvrtime() {
  // @@protoc_insertion_point(field_release:LYPROTO.QUOTA.MarketData.PubSvrTime)
  clear_has_pubsvrtime();
  return pubsvrtime_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void MarketData::set_allocated_pubsvrtime(::std::string* pubsvrtime) {
  if (pubsvrtime != NULL) {
    set_has_pubsvrtime();
  } else {
    clear_has_pubsvrtime();
  }
  pubsvrtime_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), pubsvrtime);
  // @@protoc_insertion_point(field_set_allocated:LYPROTO.QUOTA.MarketData.PubSvrTime)
}

// optional string Status = 6;
inline bool MarketData::has_status() const {
  return (_has_bits_[0] & 0x00000020u) != 0;
}
inline void MarketData::set_has_status() {
  _has_bits_[0] |= 0x00000020u;
}
inline void MarketData::clear_has_status() {
  _has_bits_[0] &= ~0x00000020u;
}
inline void MarketData::clear_status() {
  status_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  clear_has_status();
}
inline const ::std::string& MarketData::status() const {
  // @@protoc_insertion_point(field_get:LYPROTO.QUOTA.MarketData.Status)
  return status_.GetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void MarketData::set_status(const ::std::string& value) {
  set_has_status();
  status_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:LYPROTO.QUOTA.MarketData.Status)
}
inline void MarketData::set_status(const char* value) {
  set_has_status();
  status_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:LYPROTO.QUOTA.MarketData.Status)
}
inline void MarketData::set_status(const char* value, size_t size) {
  set_has_status();
  status_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:LYPROTO.QUOTA.MarketData.Status)
}
inline ::std::string* MarketData::mutable_status() {
  set_has_status();
  // @@protoc_insertion_point(field_mutable:LYPROTO.QUOTA.MarketData.Status)
  return status_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline ::std::string* MarketData::release_status() {
  // @@protoc_insertion_point(field_release:LYPROTO.QUOTA.MarketData.Status)
  clear_has_status();
  return status_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void MarketData::set_allocated_status(::std::string* status) {
  if (status != NULL) {
    set_has_status();
  } else {
    clear_has_status();
  }
  status_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), status);
  // @@protoc_insertion_point(field_set_allocated:LYPROTO.QUOTA.MarketData.Status)
}

// optional string ExchTime = 7;
inline bool MarketData::has_exchtime() const {
  return (_has_bits_[0] & 0x00000040u) != 0;
}
inline void MarketData::set_has_exchtime() {
  _has_bits_[0] |= 0x00000040u;
}
inline void MarketData::clear_has_exchtime() {
  _has_bits_[0] &= ~0x00000040u;
}
inline void MarketData::clear_exchtime() {
  exchtime_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  clear_has_exchtime();
}
inline const ::std::string& MarketData::exchtime() const {
  // @@protoc_insertion_point(field_get:LYPROTO.QUOTA.MarketData.ExchTime)
  return exchtime_.GetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void MarketData::set_exchtime(const ::std::string& value) {
  set_has_exchtime();
  exchtime_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:LYPROTO.QUOTA.MarketData.ExchTime)
}
inline void MarketData::set_exchtime(const char* value) {
  set_has_exchtime();
  exchtime_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:LYPROTO.QUOTA.MarketData.ExchTime)
}
inline void MarketData::set_exchtime(const char* value, size_t size) {
  set_has_exchtime();
  exchtime_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:LYPROTO.QUOTA.MarketData.ExchTime)
}
inline ::std::string* MarketData::mutable_exchtime() {
  set_has_exchtime();
  // @@protoc_insertion_point(field_mutable:LYPROTO.QUOTA.MarketData.ExchTime)
  return exchtime_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline ::std::string* MarketData::release_exchtime() {
  // @@protoc_insertion_point(field_release:LYPROTO.QUOTA.MarketData.ExchTime)
  clear_has_exchtime();
  return exchtime_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void MarketData::set_allocated_exchtime(::std::string* exchtime) {
  if (exchtime != NULL) {
    set_has_exchtime();
  } else {
    clear_has_exchtime();
  }
  exchtime_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), exchtime);
  // @@protoc_insertion_point(field_set_allocated:LYPROTO.QUOTA.MarketData.ExchTime)
}

// optional int64 PreClose = 8;
inline bool MarketData::has_preclose() const {
  return (_has_bits_[0] & 0x00000080u) != 0;
}
inline void MarketData::set_has_preclose() {
  _has_bits_[0] |= 0x00000080u;
}
inline void MarketData::clear_has_preclose() {
  _has_bits_[0] &= ~0x00000080u;
}
inline void MarketData::clear_preclose() {
  preclose_ = GOOGLE_LONGLONG(0);
  clear_has_preclose();
}
inline ::google::protobuf::int64 MarketData::preclose() const {
  // @@protoc_insertion_point(field_get:LYPROTO.QUOTA.MarketData.PreClose)
  return preclose_;
}
inline void MarketData::set_preclose(::google::protobuf::int64 value) {
  set_has_preclose();
  preclose_ = value;
  // @@protoc_insertion_point(field_set:LYPROTO.QUOTA.MarketData.PreClose)
}

// optional int64 HighLimit = 9;
inline bool MarketData::has_highlimit() const {
  return (_has_bits_[0] & 0x00000100u) != 0;
}
inline void MarketData::set_has_highlimit() {
  _has_bits_[0] |= 0x00000100u;
}
inline void MarketData::clear_has_highlimit() {
  _has_bits_[0] &= ~0x00000100u;
}
inline void MarketData::clear_highlimit() {
  highlimit_ = GOOGLE_LONGLONG(0);
  clear_has_highlimit();
}
inline ::google::protobuf::int64 MarketData::highlimit() const {
  // @@protoc_insertion_point(field_get:LYPROTO.QUOTA.MarketData.HighLimit)
  return highlimit_;
}
inline void MarketData::set_highlimit(::google::protobuf::int64 value) {
  set_has_highlimit();
  highlimit_ = value;
  // @@protoc_insertion_point(field_set:LYPROTO.QUOTA.MarketData.HighLimit)
}

// optional int64 LowLimit = 10;
inline bool MarketData::has_lowlimit() const {
  return (_has_bits_[0] & 0x00000200u) != 0;
}
inline void MarketData::set_has_lowlimit() {
  _has_bits_[0] |= 0x00000200u;
}
inline void MarketData::clear_has_lowlimit() {
  _has_bits_[0] &= ~0x00000200u;
}
inline void MarketData::clear_lowlimit() {
  lowlimit_ = GOOGLE_LONGLONG(0);
  clear_has_lowlimit();
}
inline ::google::protobuf::int64 MarketData::lowlimit() const {
  // @@protoc_insertion_point(field_get:LYPROTO.QUOTA.MarketData.LowLimit)
  return lowlimit_;
}
inline void MarketData::set_lowlimit(::google::protobuf::int64 value) {
  set_has_lowlimit();
  lowlimit_ = value;
  // @@protoc_insertion_point(field_set:LYPROTO.QUOTA.MarketData.LowLimit)
}

// optional int64 Open = 11;
inline bool MarketData::has_open() const {
  return (_has_bits_[0] & 0x00000400u) != 0;
}
inline void MarketData::set_has_open() {
  _has_bits_[0] |= 0x00000400u;
}
inline void MarketData::clear_has_open() {
  _has_bits_[0] &= ~0x00000400u;
}
inline void MarketData::clear_open() {
  open_ = GOOGLE_LONGLONG(0);
  clear_has_open();
}
inline ::google::protobuf::int64 MarketData::open() const {
  // @@protoc_insertion_point(field_get:LYPROTO.QUOTA.MarketData.Open)
  return open_;
}
inline void MarketData::set_open(::google::protobuf::int64 value) {
  set_has_open();
  open_ = value;
  // @@protoc_insertion_point(field_set:LYPROTO.QUOTA.MarketData.Open)
}

// optional int64 High = 12;
inline bool MarketData::has_high() const {
  return (_has_bits_[0] & 0x00000800u) != 0;
}
inline void MarketData::set_has_high() {
  _has_bits_[0] |= 0x00000800u;
}
inline void MarketData::clear_has_high() {
  _has_bits_[0] &= ~0x00000800u;
}
inline void MarketData::clear_high() {
  high_ = GOOGLE_LONGLONG(0);
  clear_has_high();
}
inline ::google::protobuf::int64 MarketData::high() const {
  // @@protoc_insertion_point(field_get:LYPROTO.QUOTA.MarketData.High)
  return high_;
}
inline void MarketData::set_high(::google::protobuf::int64 value) {
  set_has_high();
  high_ = value;
  // @@protoc_insertion_point(field_set:LYPROTO.QUOTA.MarketData.High)
}

// optional int64 Low = 13;
inline bool MarketData::has_low() const {
  return (_has_bits_[0] & 0x00001000u) != 0;
}
inline void MarketData::set_has_low() {
  _has_bits_[0] |= 0x00001000u;
}
inline void MarketData::clear_has_low() {
  _has_bits_[0] &= ~0x00001000u;
}
inline void MarketData::clear_low() {
  low_ = GOOGLE_LONGLONG(0);
  clear_has_low();
}
inline ::google::protobuf::int64 MarketData::low() const {
  // @@protoc_insertion_point(field_get:LYPROTO.QUOTA.MarketData.Low)
  return low_;
}
inline void MarketData::set_low(::google::protobuf::int64 value) {
  set_has_low();
  low_ = value;
  // @@protoc_insertion_point(field_set:LYPROTO.QUOTA.MarketData.Low)
}

// optional int64 Latest = 14;
inline bool MarketData::has_latest() const {
  return (_has_bits_[0] & 0x00002000u) != 0;
}
inline void MarketData::set_has_latest() {
  _has_bits_[0] |= 0x00002000u;
}
inline void MarketData::clear_has_latest() {
  _has_bits_[0] &= ~0x00002000u;
}
inline void MarketData::clear_latest() {
  latest_ = GOOGLE_LONGLONG(0);
  clear_has_latest();
}
inline ::google::protobuf::int64 MarketData::latest() const {
  // @@protoc_insertion_point(field_get:LYPROTO.QUOTA.MarketData.Latest)
  return latest_;
}
inline void MarketData::set_latest(::google::protobuf::int64 value) {
  set_has_latest();
  latest_ = value;
  // @@protoc_insertion_point(field_set:LYPROTO.QUOTA.MarketData.Latest)
}

// optional int32 Knock = 15;
inline bool MarketData::has_knock() const {
  return (_has_bits_[0] & 0x00004000u) != 0;
}
inline void MarketData::set_has_knock() {
  _has_bits_[0] |= 0x00004000u;
}
inline void MarketData::clear_has_knock() {
  _has_bits_[0] &= ~0x00004000u;
}
inline void MarketData::clear_knock() {
  knock_ = 0;
  clear_has_knock();
}
inline ::google::protobuf::int32 MarketData::knock() const {
  // @@protoc_insertion_point(field_get:LYPROTO.QUOTA.MarketData.Knock)
  return knock_;
}
inline void MarketData::set_knock(::google::protobuf::int32 value) {
  set_has_knock();
  knock_ = value;
  // @@protoc_insertion_point(field_set:LYPROTO.QUOTA.MarketData.Knock)
}

// optional int64 Volume = 16;
inline bool MarketData::has_volume() const {
  return (_has_bits_[0] & 0x00008000u) != 0;
}
inline void MarketData::set_has_volume() {
  _has_bits_[0] |= 0x00008000u;
}
inline void MarketData::clear_has_volume() {
  _has_bits_[0] &= ~0x00008000u;
}
inline void MarketData::clear_volume() {
  volume_ = GOOGLE_LONGLONG(0);
  clear_has_volume();
}
inline ::google::protobuf::int64 MarketData::volume() const {
  // @@protoc_insertion_point(field_get:LYPROTO.QUOTA.MarketData.Volume)
  return volume_;
}
inline void MarketData::set_volume(::google::protobuf::int64 value) {
  set_has_volume();
  volume_ = value;
  // @@protoc_insertion_point(field_set:LYPROTO.QUOTA.MarketData.Volume)
}

// optional int64 Value = 17;
inline bool MarketData::has_value() const {
  return (_has_bits_[0] & 0x00010000u) != 0;
}
inline void MarketData::set_has_value() {
  _has_bits_[0] |= 0x00010000u;
}
inline void MarketData::clear_has_value() {
  _has_bits_[0] &= ~0x00010000u;
}
inline void MarketData::clear_value() {
  value_ = GOOGLE_LONGLONG(0);
  clear_has_value();
}
inline ::google::protobuf::int64 MarketData::value() const {
  // @@protoc_insertion_point(field_get:LYPROTO.QUOTA.MarketData.Value)
  return value_;
}
inline void MarketData::set_value(::google::protobuf::int64 value) {
  set_has_value();
  value_ = value;
  // @@protoc_insertion_point(field_set:LYPROTO.QUOTA.MarketData.Value)
}

// repeated int64 BA = 18 [packed = true];
inline int MarketData::ba_size() const {
  return ba_.size();
}
inline void MarketData::clear_ba() {
  ba_.Clear();
}
inline ::google::protobuf::int64 MarketData::ba(int index) const {
  // @@protoc_insertion_point(field_get:LYPROTO.QUOTA.MarketData.BA)
  return ba_.Get(index);
}
inline void MarketData::set_ba(int index, ::google::protobuf::int64 value) {
  ba_.Set(index, value);
  // @@protoc_insertion_point(field_set:LYPROTO.QUOTA.MarketData.BA)
}
inline void MarketData::add_ba(::google::protobuf::int64 value) {
  ba_.Add(value);
  // @@protoc_insertion_point(field_add:LYPROTO.QUOTA.MarketData.BA)
}
inline const ::google::protobuf::RepeatedField< ::google::protobuf::int64 >&
MarketData::ba() const {
  // @@protoc_insertion_point(field_list:LYPROTO.QUOTA.MarketData.BA)
  return ba_;
}
inline ::google::protobuf::RepeatedField< ::google::protobuf::int64 >*
MarketData::mutable_ba() {
  // @@protoc_insertion_point(field_mutable_list:LYPROTO.QUOTA.MarketData.BA)
  return &ba_;
}

// repeated int64 BP = 19 [packed = true];
inline int MarketData::bp_size() const {
  return bp_.size();
}
inline void MarketData::clear_bp() {
  bp_.Clear();
}
inline ::google::protobuf::int64 MarketData::bp(int index) const {
  // @@protoc_insertion_point(field_get:LYPROTO.QUOTA.MarketData.BP)
  return bp_.Get(index);
}
inline void MarketData::set_bp(int index, ::google::protobuf::int64 value) {
  bp_.Set(index, value);
  // @@protoc_insertion_point(field_set:LYPROTO.QUOTA.MarketData.BP)
}
inline void MarketData::add_bp(::google::protobuf::int64 value) {
  bp_.Add(value);
  // @@protoc_insertion_point(field_add:LYPROTO.QUOTA.MarketData.BP)
}
inline const ::google::protobuf::RepeatedField< ::google::protobuf::int64 >&
MarketData::bp() const {
  // @@protoc_insertion_point(field_list:LYPROTO.QUOTA.MarketData.BP)
  return bp_;
}
inline ::google::protobuf::RepeatedField< ::google::protobuf::int64 >*
MarketData::mutable_bp() {
  // @@protoc_insertion_point(field_mutable_list:LYPROTO.QUOTA.MarketData.BP)
  return &bp_;
}

// repeated int64 SA = 20 [packed = true];
inline int MarketData::sa_size() const {
  return sa_.size();
}
inline void MarketData::clear_sa() {
  sa_.Clear();
}
inline ::google::protobuf::int64 MarketData::sa(int index) const {
  // @@protoc_insertion_point(field_get:LYPROTO.QUOTA.MarketData.SA)
  return sa_.Get(index);
}
inline void MarketData::set_sa(int index, ::google::protobuf::int64 value) {
  sa_.Set(index, value);
  // @@protoc_insertion_point(field_set:LYPROTO.QUOTA.MarketData.SA)
}
inline void MarketData::add_sa(::google::protobuf::int64 value) {
  sa_.Add(value);
  // @@protoc_insertion_point(field_add:LYPROTO.QUOTA.MarketData.SA)
}
inline const ::google::protobuf::RepeatedField< ::google::protobuf::int64 >&
MarketData::sa() const {
  // @@protoc_insertion_point(field_list:LYPROTO.QUOTA.MarketData.SA)
  return sa_;
}
inline ::google::protobuf::RepeatedField< ::google::protobuf::int64 >*
MarketData::mutable_sa() {
  // @@protoc_insertion_point(field_mutable_list:LYPROTO.QUOTA.MarketData.SA)
  return &sa_;
}

// repeated int64 SP = 21 [packed = true];
inline int MarketData::sp_size() const {
  return sp_.size();
}
inline void MarketData::clear_sp() {
  sp_.Clear();
}
inline ::google::protobuf::int64 MarketData::sp(int index) const {
  // @@protoc_insertion_point(field_get:LYPROTO.QUOTA.MarketData.SP)
  return sp_.Get(index);
}
inline void MarketData::set_sp(int index, ::google::protobuf::int64 value) {
  sp_.Set(index, value);
  // @@protoc_insertion_point(field_set:LYPROTO.QUOTA.MarketData.SP)
}
inline void MarketData::add_sp(::google::protobuf::int64 value) {
  sp_.Add(value);
  // @@protoc_insertion_point(field_add:LYPROTO.QUOTA.MarketData.SP)
}
inline const ::google::protobuf::RepeatedField< ::google::protobuf::int64 >&
MarketData::sp() const {
  // @@protoc_insertion_point(field_list:LYPROTO.QUOTA.MarketData.SP)
  return sp_;
}
inline ::google::protobuf::RepeatedField< ::google::protobuf::int64 >*
MarketData::mutable_sp() {
  // @@protoc_insertion_point(field_mutable_list:LYPROTO.QUOTA.MarketData.SP)
  return &sp_;
}

// optional int64 TotalBA = 22;
inline bool MarketData::has_totalba() const {
  return (_has_bits_[0] & 0x00200000u) != 0;
}
inline void MarketData::set_has_totalba() {
  _has_bits_[0] |= 0x00200000u;
}
inline void MarketData::clear_has_totalba() {
  _has_bits_[0] &= ~0x00200000u;
}
inline void MarketData::clear_totalba() {
  totalba_ = GOOGLE_LONGLONG(0);
  clear_has_totalba();
}
inline ::google::protobuf::int64 MarketData::totalba() const {
  // @@protoc_insertion_point(field_get:LYPROTO.QUOTA.MarketData.TotalBA)
  return totalba_;
}
inline void MarketData::set_totalba(::google::protobuf::int64 value) {
  set_has_totalba();
  totalba_ = value;
  // @@protoc_insertion_point(field_set:LYPROTO.QUOTA.MarketData.TotalBA)
}

// optional int64 WeightedAvgBidPx = 23;
inline bool MarketData::has_weightedavgbidpx() const {
  return (_has_bits_[0] & 0x00400000u) != 0;
}
inline void MarketData::set_has_weightedavgbidpx() {
  _has_bits_[0] |= 0x00400000u;
}
inline void MarketData::clear_has_weightedavgbidpx() {
  _has_bits_[0] &= ~0x00400000u;
}
inline void MarketData::clear_weightedavgbidpx() {
  weightedavgbidpx_ = GOOGLE_LONGLONG(0);
  clear_has_weightedavgbidpx();
}
inline ::google::protobuf::int64 MarketData::weightedavgbidpx() const {
  // @@protoc_insertion_point(field_get:LYPROTO.QUOTA.MarketData.WeightedAvgBidPx)
  return weightedavgbidpx_;
}
inline void MarketData::set_weightedavgbidpx(::google::protobuf::int64 value) {
  set_has_weightedavgbidpx();
  weightedavgbidpx_ = value;
  // @@protoc_insertion_point(field_set:LYPROTO.QUOTA.MarketData.WeightedAvgBidPx)
}

// optional int64 TotalSA = 24;
inline bool MarketData::has_totalsa() const {
  return (_has_bits_[0] & 0x00800000u) != 0;
}
inline void MarketData::set_has_totalsa() {
  _has_bits_[0] |= 0x00800000u;
}
inline void MarketData::clear_has_totalsa() {
  _has_bits_[0] &= ~0x00800000u;
}
inline void MarketData::clear_totalsa() {
  totalsa_ = GOOGLE_LONGLONG(0);
  clear_has_totalsa();
}
inline ::google::protobuf::int64 MarketData::totalsa() const {
  // @@protoc_insertion_point(field_get:LYPROTO.QUOTA.MarketData.TotalSA)
  return totalsa_;
}
inline void MarketData::set_totalsa(::google::protobuf::int64 value) {
  set_has_totalsa();
  totalsa_ = value;
  // @@protoc_insertion_point(field_set:LYPROTO.QUOTA.MarketData.TotalSA)
}

// optional int64 WeightedAvgOfferPx = 25;
inline bool MarketData::has_weightedavgofferpx() const {
  return (_has_bits_[0] & 0x01000000u) != 0;
}
inline void MarketData::set_has_weightedavgofferpx() {
  _has_bits_[0] |= 0x01000000u;
}
inline void MarketData::clear_has_weightedavgofferpx() {
  _has_bits_[0] &= ~0x01000000u;
}
inline void MarketData::clear_weightedavgofferpx() {
  weightedavgofferpx_ = GOOGLE_LONGLONG(0);
  clear_has_weightedavgofferpx();
}
inline ::google::protobuf::int64 MarketData::weightedavgofferpx() const {
  // @@protoc_insertion_point(field_get:LYPROTO.QUOTA.MarketData.WeightedAvgOfferPx)
  return weightedavgofferpx_;
}
inline void MarketData::set_weightedavgofferpx(::google::protobuf::int64 value) {
  set_has_weightedavgofferpx();
  weightedavgofferpx_ = value;
  // @@protoc_insertion_point(field_set:LYPROTO.QUOTA.MarketData.WeightedAvgOfferPx)
}

// optional int32 IOPV = 26;
inline bool MarketData::has_iopv() const {
  return (_has_bits_[0] & 0x02000000u) != 0;
}
inline void MarketData::set_has_iopv() {
  _has_bits_[0] |= 0x02000000u;
}
inline void MarketData::clear_has_iopv() {
  _has_bits_[0] &= ~0x02000000u;
}
inline void MarketData::clear_iopv() {
  iopv_ = 0;
  clear_has_iopv();
}
inline ::google::protobuf::int32 MarketData::iopv() const {
  // @@protoc_insertion_point(field_get:LYPROTO.QUOTA.MarketData.IOPV)
  return iopv_;
}
inline void MarketData::set_iopv(::google::protobuf::int32 value) {
  set_has_iopv();
  iopv_ = value;
  // @@protoc_insertion_point(field_set:LYPROTO.QUOTA.MarketData.IOPV)
}

// optional int32 YieldToMaturity = 27;
inline bool MarketData::has_yieldtomaturity() const {
  return (_has_bits_[0] & 0x04000000u) != 0;
}
inline void MarketData::set_has_yieldtomaturity() {
  _has_bits_[0] |= 0x04000000u;
}
inline void MarketData::clear_has_yieldtomaturity() {
  _has_bits_[0] &= ~0x04000000u;
}
inline void MarketData::clear_yieldtomaturity() {
  yieldtomaturity_ = 0;
  clear_has_yieldtomaturity();
}
inline ::google::protobuf::int32 MarketData::yieldtomaturity() const {
  // @@protoc_insertion_point(field_get:LYPROTO.QUOTA.MarketData.YieldToMaturity)
  return yieldtomaturity_;
}
inline void MarketData::set_yieldtomaturity(::google::protobuf::int32 value) {
  set_has_yieldtomaturity();
  yieldtomaturity_ = value;
  // @@protoc_insertion_point(field_set:LYPROTO.QUOTA.MarketData.YieldToMaturity)
}

// optional int64 TotalWarrantExecQty = 28;
inline bool MarketData::has_totalwarrantexecqty() const {
  return (_has_bits_[0] & 0x08000000u) != 0;
}
inline void MarketData::set_has_totalwarrantexecqty() {
  _has_bits_[0] |= 0x08000000u;
}
inline void MarketData::clear_has_totalwarrantexecqty() {
  _has_bits_[0] &= ~0x08000000u;
}
inline void MarketData::clear_totalwarrantexecqty() {
  totalwarrantexecqty_ = GOOGLE_LONGLONG(0);
  clear_has_totalwarrantexecqty();
}
inline ::google::protobuf::int64 MarketData::totalwarrantexecqty() const {
  // @@protoc_insertion_point(field_get:LYPROTO.QUOTA.MarketData.TotalWarrantExecQty)
  return totalwarrantexecqty_;
}
inline void MarketData::set_totalwarrantexecqty(::google::protobuf::int64 value) {
  set_has_totalwarrantexecqty();
  totalwarrantexecqty_ = value;
  // @@protoc_insertion_point(field_set:LYPROTO.QUOTA.MarketData.TotalWarrantExecQty)
}

// optional int64 WarLowerPx = 29;
inline bool MarketData::has_warlowerpx() const {
  return (_has_bits_[0] & 0x10000000u) != 0;
}
inline void MarketData::set_has_warlowerpx() {
  _has_bits_[0] |= 0x10000000u;
}
inline void MarketData::clear_has_warlowerpx() {
  _has_bits_[0] &= ~0x10000000u;
}
inline void MarketData::clear_warlowerpx() {
  warlowerpx_ = GOOGLE_LONGLONG(0);
  clear_has_warlowerpx();
}
inline ::google::protobuf::int64 MarketData::warlowerpx() const {
  // @@protoc_insertion_point(field_get:LYPROTO.QUOTA.MarketData.WarLowerPx)
  return warlowerpx_;
}
inline void MarketData::set_warlowerpx(::google::protobuf::int64 value) {
  set_has_warlowerpx();
  warlowerpx_ = value;
  // @@protoc_insertion_point(field_set:LYPROTO.QUOTA.MarketData.WarLowerPx)
}

// optional int64 WarUpperPx = 30;
inline bool MarketData::has_warupperpx() const {
  return (_has_bits_[0] & 0x20000000u) != 0;
}
inline void MarketData::set_has_warupperpx() {
  _has_bits_[0] |= 0x20000000u;
}
inline void MarketData::clear_has_warupperpx() {
  _has_bits_[0] &= ~0x20000000u;
}
inline void MarketData::clear_warupperpx() {
  warupperpx_ = GOOGLE_LONGLONG(0);
  clear_has_warupperpx();
}
inline ::google::protobuf::int64 MarketData::warupperpx() const {
  // @@protoc_insertion_point(field_get:LYPROTO.QUOTA.MarketData.WarUpperPx)
  return warupperpx_;
}
inline void MarketData::set_warupperpx(::google::protobuf::int64 value) {
  set_has_warupperpx();
  warupperpx_ = value;
  // @@protoc_insertion_point(field_set:LYPROTO.QUOTA.MarketData.WarUpperPx)
}

inline const MarketData* MarketData::internal_default_instance() {
  return &MarketData_default_instance_.get();
}
// -------------------------------------------------------------------

// Transaction

// required string ExchangeID = 1;
inline bool Transaction::has_exchangeid() const {
  return (_has_bits_[0] & 0x00000001u) != 0;
}
inline void Transaction::set_has_exchangeid() {
  _has_bits_[0] |= 0x00000001u;
}
inline void Transaction::clear_has_exchangeid() {
  _has_bits_[0] &= ~0x00000001u;
}
inline void Transaction::clear_exchangeid() {
  exchangeid_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  clear_has_exchangeid();
}
inline const ::std::string& Transaction::exchangeid() const {
  // @@protoc_insertion_point(field_get:LYPROTO.QUOTA.Transaction.ExchangeID)
  return exchangeid_.GetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void Transaction::set_exchangeid(const ::std::string& value) {
  set_has_exchangeid();
  exchangeid_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:LYPROTO.QUOTA.Transaction.ExchangeID)
}
inline void Transaction::set_exchangeid(const char* value) {
  set_has_exchangeid();
  exchangeid_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:LYPROTO.QUOTA.Transaction.ExchangeID)
}
inline void Transaction::set_exchangeid(const char* value, size_t size) {
  set_has_exchangeid();
  exchangeid_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:LYPROTO.QUOTA.Transaction.ExchangeID)
}
inline ::std::string* Transaction::mutable_exchangeid() {
  set_has_exchangeid();
  // @@protoc_insertion_point(field_mutable:LYPROTO.QUOTA.Transaction.ExchangeID)
  return exchangeid_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline ::std::string* Transaction::release_exchangeid() {
  // @@protoc_insertion_point(field_release:LYPROTO.QUOTA.Transaction.ExchangeID)
  clear_has_exchangeid();
  return exchangeid_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void Transaction::set_allocated_exchangeid(::std::string* exchangeid) {
  if (exchangeid != NULL) {
    set_has_exchangeid();
  } else {
    clear_has_exchangeid();
  }
  exchangeid_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), exchangeid);
  // @@protoc_insertion_point(field_set_allocated:LYPROTO.QUOTA.Transaction.ExchangeID)
}

// required string Category = 2;
inline bool Transaction::has_category() const {
  return (_has_bits_[0] & 0x00000002u) != 0;
}
inline void Transaction::set_has_category() {
  _has_bits_[0] |= 0x00000002u;
}
inline void Transaction::clear_has_category() {
  _has_bits_[0] &= ~0x00000002u;
}
inline void Transaction::clear_category() {
  category_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  clear_has_category();
}
inline const ::std::string& Transaction::category() const {
  // @@protoc_insertion_point(field_get:LYPROTO.QUOTA.Transaction.Category)
  return category_.GetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void Transaction::set_category(const ::std::string& value) {
  set_has_category();
  category_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:LYPROTO.QUOTA.Transaction.Category)
}
inline void Transaction::set_category(const char* value) {
  set_has_category();
  category_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:LYPROTO.QUOTA.Transaction.Category)
}
inline void Transaction::set_category(const char* value, size_t size) {
  set_has_category();
  category_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:LYPROTO.QUOTA.Transaction.Category)
}
inline ::std::string* Transaction::mutable_category() {
  set_has_category();
  // @@protoc_insertion_point(field_mutable:LYPROTO.QUOTA.Transaction.Category)
  return category_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline ::std::string* Transaction::release_category() {
  // @@protoc_insertion_point(field_release:LYPROTO.QUOTA.Transaction.Category)
  clear_has_category();
  return category_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void Transaction::set_allocated_category(::std::string* category) {
  if (category != NULL) {
    set_has_category();
  } else {
    clear_has_category();
  }
  category_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), category);
  // @@protoc_insertion_point(field_set_allocated:LYPROTO.QUOTA.Transaction.Category)
}

// required string SecurityID = 3;
inline bool Transaction::has_securityid() const {
  return (_has_bits_[0] & 0x00000004u) != 0;
}
inline void Transaction::set_has_securityid() {
  _has_bits_[0] |= 0x00000004u;
}
inline void Transaction::clear_has_securityid() {
  _has_bits_[0] &= ~0x00000004u;
}
inline void Transaction::clear_securityid() {
  securityid_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  clear_has_securityid();
}
inline const ::std::string& Transaction::securityid() const {
  // @@protoc_insertion_point(field_get:LYPROTO.QUOTA.Transaction.SecurityID)
  return securityid_.GetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void Transaction::set_securityid(const ::std::string& value) {
  set_has_securityid();
  securityid_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:LYPROTO.QUOTA.Transaction.SecurityID)
}
inline void Transaction::set_securityid(const char* value) {
  set_has_securityid();
  securityid_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:LYPROTO.QUOTA.Transaction.SecurityID)
}
inline void Transaction::set_securityid(const char* value, size_t size) {
  set_has_securityid();
  securityid_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:LYPROTO.QUOTA.Transaction.SecurityID)
}
inline ::std::string* Transaction::mutable_securityid() {
  set_has_securityid();
  // @@protoc_insertion_point(field_mutable:LYPROTO.QUOTA.Transaction.SecurityID)
  return securityid_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline ::std::string* Transaction::release_securityid() {
  // @@protoc_insertion_point(field_release:LYPROTO.QUOTA.Transaction.SecurityID)
  clear_has_securityid();
  return securityid_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void Transaction::set_allocated_securityid(::std::string* securityid) {
  if (securityid != NULL) {
    set_has_securityid();
  } else {
    clear_has_securityid();
  }
  securityid_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), securityid);
  // @@protoc_insertion_point(field_set_allocated:LYPROTO.QUOTA.Transaction.SecurityID)
}

// optional string PubSvrTime = 4;
inline bool Transaction::has_pubsvrtime() const {
  return (_has_bits_[0] & 0x00000008u) != 0;
}
inline void Transaction::set_has_pubsvrtime() {
  _has_bits_[0] |= 0x00000008u;
}
inline void Transaction::clear_has_pubsvrtime() {
  _has_bits_[0] &= ~0x00000008u;
}
inline void Transaction::clear_pubsvrtime() {
  pubsvrtime_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  clear_has_pubsvrtime();
}
inline const ::std::string& Transaction::pubsvrtime() const {
  // @@protoc_insertion_point(field_get:LYPROTO.QUOTA.Transaction.PubSvrTime)
  return pubsvrtime_.GetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void Transaction::set_pubsvrtime(const ::std::string& value) {
  set_has_pubsvrtime();
  pubsvrtime_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:LYPROTO.QUOTA.Transaction.PubSvrTime)
}
inline void Transaction::set_pubsvrtime(const char* value) {
  set_has_pubsvrtime();
  pubsvrtime_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:LYPROTO.QUOTA.Transaction.PubSvrTime)
}
inline void Transaction::set_pubsvrtime(const char* value, size_t size) {
  set_has_pubsvrtime();
  pubsvrtime_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:LYPROTO.QUOTA.Transaction.PubSvrTime)
}
inline ::std::string* Transaction::mutable_pubsvrtime() {
  set_has_pubsvrtime();
  // @@protoc_insertion_point(field_mutable:LYPROTO.QUOTA.Transaction.PubSvrTime)
  return pubsvrtime_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline ::std::string* Transaction::release_pubsvrtime() {
  // @@protoc_insertion_point(field_release:LYPROTO.QUOTA.Transaction.PubSvrTime)
  clear_has_pubsvrtime();
  return pubsvrtime_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void Transaction::set_allocated_pubsvrtime(::std::string* pubsvrtime) {
  if (pubsvrtime != NULL) {
    set_has_pubsvrtime();
  } else {
    clear_has_pubsvrtime();
  }
  pubsvrtime_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), pubsvrtime);
  // @@protoc_insertion_point(field_set_allocated:LYPROTO.QUOTA.Transaction.PubSvrTime)
}

// optional string TradeIndex = 5;
inline bool Transaction::has_tradeindex() const {
  return (_has_bits_[0] & 0x00000010u) != 0;
}
inline void Transaction::set_has_tradeindex() {
  _has_bits_[0] |= 0x00000010u;
}
inline void Transaction::clear_has_tradeindex() {
  _has_bits_[0] &= ~0x00000010u;
}
inline void Transaction::clear_tradeindex() {
  tradeindex_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  clear_has_tradeindex();
}
inline const ::std::string& Transaction::tradeindex() const {
  // @@protoc_insertion_point(field_get:LYPROTO.QUOTA.Transaction.TradeIndex)
  return tradeindex_.GetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void Transaction::set_tradeindex(const ::std::string& value) {
  set_has_tradeindex();
  tradeindex_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:LYPROTO.QUOTA.Transaction.TradeIndex)
}
inline void Transaction::set_tradeindex(const char* value) {
  set_has_tradeindex();
  tradeindex_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:LYPROTO.QUOTA.Transaction.TradeIndex)
}
inline void Transaction::set_tradeindex(const char* value, size_t size) {
  set_has_tradeindex();
  tradeindex_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:LYPROTO.QUOTA.Transaction.TradeIndex)
}
inline ::std::string* Transaction::mutable_tradeindex() {
  set_has_tradeindex();
  // @@protoc_insertion_point(field_mutable:LYPROTO.QUOTA.Transaction.TradeIndex)
  return tradeindex_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline ::std::string* Transaction::release_tradeindex() {
  // @@protoc_insertion_point(field_release:LYPROTO.QUOTA.Transaction.TradeIndex)
  clear_has_tradeindex();
  return tradeindex_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void Transaction::set_allocated_tradeindex(::std::string* tradeindex) {
  if (tradeindex != NULL) {
    set_has_tradeindex();
  } else {
    clear_has_tradeindex();
  }
  tradeindex_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), tradeindex);
  // @@protoc_insertion_point(field_set_allocated:LYPROTO.QUOTA.Transaction.TradeIndex)
}

// optional string TradeTime = 6;
inline bool Transaction::has_tradetime() const {
  return (_has_bits_[0] & 0x00000020u) != 0;
}
inline void Transaction::set_has_tradetime() {
  _has_bits_[0] |= 0x00000020u;
}
inline void Transaction::clear_has_tradetime() {
  _has_bits_[0] &= ~0x00000020u;
}
inline void Transaction::clear_tradetime() {
  tradetime_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  clear_has_tradetime();
}
inline const ::std::string& Transaction::tradetime() const {
  // @@protoc_insertion_point(field_get:LYPROTO.QUOTA.Transaction.TradeTime)
  return tradetime_.GetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void Transaction::set_tradetime(const ::std::string& value) {
  set_has_tradetime();
  tradetime_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:LYPROTO.QUOTA.Transaction.TradeTime)
}
inline void Transaction::set_tradetime(const char* value) {
  set_has_tradetime();
  tradetime_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:LYPROTO.QUOTA.Transaction.TradeTime)
}
inline void Transaction::set_tradetime(const char* value, size_t size) {
  set_has_tradetime();
  tradetime_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:LYPROTO.QUOTA.Transaction.TradeTime)
}
inline ::std::string* Transaction::mutable_tradetime() {
  set_has_tradetime();
  // @@protoc_insertion_point(field_mutable:LYPROTO.QUOTA.Transaction.TradeTime)
  return tradetime_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline ::std::string* Transaction::release_tradetime() {
  // @@protoc_insertion_point(field_release:LYPROTO.QUOTA.Transaction.TradeTime)
  clear_has_tradetime();
  return tradetime_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void Transaction::set_allocated_tradetime(::std::string* tradetime) {
  if (tradetime != NULL) {
    set_has_tradetime();
  } else {
    clear_has_tradetime();
  }
  tradetime_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), tradetime);
  // @@protoc_insertion_point(field_set_allocated:LYPROTO.QUOTA.Transaction.TradeTime)
}

// optional int64 TradePrice = 7;
inline bool Transaction::has_tradeprice() const {
  return (_has_bits_[0] & 0x00000040u) != 0;
}
inline void Transaction::set_has_tradeprice() {
  _has_bits_[0] |= 0x00000040u;
}
inline void Transaction::clear_has_tradeprice() {
  _has_bits_[0] &= ~0x00000040u;
}
inline void Transaction::clear_tradeprice() {
  tradeprice_ = GOOGLE_LONGLONG(0);
  clear_has_tradeprice();
}
inline ::google::protobuf::int64 Transaction::tradeprice() const {
  // @@protoc_insertion_point(field_get:LYPROTO.QUOTA.Transaction.TradePrice)
  return tradeprice_;
}
inline void Transaction::set_tradeprice(::google::protobuf::int64 value) {
  set_has_tradeprice();
  tradeprice_ = value;
  // @@protoc_insertion_point(field_set:LYPROTO.QUOTA.Transaction.TradePrice)
}

// optional int32 TradeQty = 8;
inline bool Transaction::has_tradeqty() const {
  return (_has_bits_[0] & 0x00000080u) != 0;
}
inline void Transaction::set_has_tradeqty() {
  _has_bits_[0] |= 0x00000080u;
}
inline void Transaction::clear_has_tradeqty() {
  _has_bits_[0] &= ~0x00000080u;
}
inline void Transaction::clear_tradeqty() {
  tradeqty_ = 0;
  clear_has_tradeqty();
}
inline ::google::protobuf::int32 Transaction::tradeqty() const {
  // @@protoc_insertion_point(field_get:LYPROTO.QUOTA.Transaction.TradeQty)
  return tradeqty_;
}
inline void Transaction::set_tradeqty(::google::protobuf::int32 value) {
  set_has_tradeqty();
  tradeqty_ = value;
  // @@protoc_insertion_point(field_set:LYPROTO.QUOTA.Transaction.TradeQty)
}

// optional int64 TradeMoney = 9;
inline bool Transaction::has_trademoney() const {
  return (_has_bits_[0] & 0x00000100u) != 0;
}
inline void Transaction::set_has_trademoney() {
  _has_bits_[0] |= 0x00000100u;
}
inline void Transaction::clear_has_trademoney() {
  _has_bits_[0] &= ~0x00000100u;
}
inline void Transaction::clear_trademoney() {
  trademoney_ = GOOGLE_LONGLONG(0);
  clear_has_trademoney();
}
inline ::google::protobuf::int64 Transaction::trademoney() const {
  // @@protoc_insertion_point(field_get:LYPROTO.QUOTA.Transaction.TradeMoney)
  return trademoney_;
}
inline void Transaction::set_trademoney(::google::protobuf::int64 value) {
  set_has_trademoney();
  trademoney_ = value;
  // @@protoc_insertion_point(field_set:LYPROTO.QUOTA.Transaction.TradeMoney)
}

// optional string Side = 10;
inline bool Transaction::has_side() const {
  return (_has_bits_[0] & 0x00000200u) != 0;
}
inline void Transaction::set_has_side() {
  _has_bits_[0] |= 0x00000200u;
}
inline void Transaction::clear_has_side() {
  _has_bits_[0] &= ~0x00000200u;
}
inline void Transaction::clear_side() {
  side_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  clear_has_side();
}
inline const ::std::string& Transaction::side() const {
  // @@protoc_insertion_point(field_get:LYPROTO.QUOTA.Transaction.Side)
  return side_.GetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void Transaction::set_side(const ::std::string& value) {
  set_has_side();
  side_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:LYPROTO.QUOTA.Transaction.Side)
}
inline void Transaction::set_side(const char* value) {
  set_has_side();
  side_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:LYPROTO.QUOTA.Transaction.Side)
}
inline void Transaction::set_side(const char* value, size_t size) {
  set_has_side();
  side_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:LYPROTO.QUOTA.Transaction.Side)
}
inline ::std::string* Transaction::mutable_side() {
  set_has_side();
  // @@protoc_insertion_point(field_mutable:LYPROTO.QUOTA.Transaction.Side)
  return side_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline ::std::string* Transaction::release_side() {
  // @@protoc_insertion_point(field_release:LYPROTO.QUOTA.Transaction.Side)
  clear_has_side();
  return side_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void Transaction::set_allocated_side(::std::string* side) {
  if (side != NULL) {
    set_has_side();
  } else {
    clear_has_side();
  }
  side_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), side);
  // @@protoc_insertion_point(field_set_allocated:LYPROTO.QUOTA.Transaction.Side)
}

// optional string TradeType = 11;
inline bool Transaction::has_tradetype() const {
  return (_has_bits_[0] & 0x00000400u) != 0;
}
inline void Transaction::set_has_tradetype() {
  _has_bits_[0] |= 0x00000400u;
}
inline void Transaction::clear_has_tradetype() {
  _has_bits_[0] &= ~0x00000400u;
}
inline void Transaction::clear_tradetype() {
  tradetype_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  clear_has_tradetype();
}
inline const ::std::string& Transaction::tradetype() const {
  // @@protoc_insertion_point(field_get:LYPROTO.QUOTA.Transaction.TradeType)
  return tradetype_.GetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void Transaction::set_tradetype(const ::std::string& value) {
  set_has_tradetype();
  tradetype_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:LYPROTO.QUOTA.Transaction.TradeType)
}
inline void Transaction::set_tradetype(const char* value) {
  set_has_tradetype();
  tradetype_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:LYPROTO.QUOTA.Transaction.TradeType)
}
inline void Transaction::set_tradetype(const char* value, size_t size) {
  set_has_tradetype();
  tradetype_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:LYPROTO.QUOTA.Transaction.TradeType)
}
inline ::std::string* Transaction::mutable_tradetype() {
  set_has_tradetype();
  // @@protoc_insertion_point(field_mutable:LYPROTO.QUOTA.Transaction.TradeType)
  return tradetype_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline ::std::string* Transaction::release_tradetype() {
  // @@protoc_insertion_point(field_release:LYPROTO.QUOTA.Transaction.TradeType)
  clear_has_tradetype();
  return tradetype_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void Transaction::set_allocated_tradetype(::std::string* tradetype) {
  if (tradetype != NULL) {
    set_has_tradetype();
  } else {
    clear_has_tradetype();
  }
  tradetype_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), tradetype);
  // @@protoc_insertion_point(field_set_allocated:LYPROTO.QUOTA.Transaction.TradeType)
}

// optional string TradeCode = 12;
inline bool Transaction::has_tradecode() const {
  return (_has_bits_[0] & 0x00000800u) != 0;
}
inline void Transaction::set_has_tradecode() {
  _has_bits_[0] |= 0x00000800u;
}
inline void Transaction::clear_has_tradecode() {
  _has_bits_[0] &= ~0x00000800u;
}
inline void Transaction::clear_tradecode() {
  tradecode_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  clear_has_tradecode();
}
inline const ::std::string& Transaction::tradecode() const {
  // @@protoc_insertion_point(field_get:LYPROTO.QUOTA.Transaction.TradeCode)
  return tradecode_.GetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void Transaction::set_tradecode(const ::std::string& value) {
  set_has_tradecode();
  tradecode_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:LYPROTO.QUOTA.Transaction.TradeCode)
}
inline void Transaction::set_tradecode(const char* value) {
  set_has_tradecode();
  tradecode_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:LYPROTO.QUOTA.Transaction.TradeCode)
}
inline void Transaction::set_tradecode(const char* value, size_t size) {
  set_has_tradecode();
  tradecode_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:LYPROTO.QUOTA.Transaction.TradeCode)
}
inline ::std::string* Transaction::mutable_tradecode() {
  set_has_tradecode();
  // @@protoc_insertion_point(field_mutable:LYPROTO.QUOTA.Transaction.TradeCode)
  return tradecode_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline ::std::string* Transaction::release_tradecode() {
  // @@protoc_insertion_point(field_release:LYPROTO.QUOTA.Transaction.TradeCode)
  clear_has_tradecode();
  return tradecode_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void Transaction::set_allocated_tradecode(::std::string* tradecode) {
  if (tradecode != NULL) {
    set_has_tradecode();
  } else {
    clear_has_tradecode();
  }
  tradecode_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), tradecode);
  // @@protoc_insertion_point(field_set_allocated:LYPROTO.QUOTA.Transaction.TradeCode)
}

// optional string OfferIndex = 13;
inline bool Transaction::has_offerindex() const {
  return (_has_bits_[0] & 0x00001000u) != 0;
}
inline void Transaction::set_has_offerindex() {
  _has_bits_[0] |= 0x00001000u;
}
inline void Transaction::clear_has_offerindex() {
  _has_bits_[0] &= ~0x00001000u;
}
inline void Transaction::clear_offerindex() {
  offerindex_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  clear_has_offerindex();
}
inline const ::std::string& Transaction::offerindex() const {
  // @@protoc_insertion_point(field_get:LYPROTO.QUOTA.Transaction.OfferIndex)
  return offerindex_.GetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void Transaction::set_offerindex(const ::std::string& value) {
  set_has_offerindex();
  offerindex_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:LYPROTO.QUOTA.Transaction.OfferIndex)
}
inline void Transaction::set_offerindex(const char* value) {
  set_has_offerindex();
  offerindex_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:LYPROTO.QUOTA.Transaction.OfferIndex)
}
inline void Transaction::set_offerindex(const char* value, size_t size) {
  set_has_offerindex();
  offerindex_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:LYPROTO.QUOTA.Transaction.OfferIndex)
}
inline ::std::string* Transaction::mutable_offerindex() {
  set_has_offerindex();
  // @@protoc_insertion_point(field_mutable:LYPROTO.QUOTA.Transaction.OfferIndex)
  return offerindex_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline ::std::string* Transaction::release_offerindex() {
  // @@protoc_insertion_point(field_release:LYPROTO.QUOTA.Transaction.OfferIndex)
  clear_has_offerindex();
  return offerindex_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void Transaction::set_allocated_offerindex(::std::string* offerindex) {
  if (offerindex != NULL) {
    set_has_offerindex();
  } else {
    clear_has_offerindex();
  }
  offerindex_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), offerindex);
  // @@protoc_insertion_point(field_set_allocated:LYPROTO.QUOTA.Transaction.OfferIndex)
}

// optional string BidIndex = 14;
inline bool Transaction::has_bidindex() const {
  return (_has_bits_[0] & 0x00002000u) != 0;
}
inline void Transaction::set_has_bidindex() {
  _has_bits_[0] |= 0x00002000u;
}
inline void Transaction::clear_has_bidindex() {
  _has_bits_[0] &= ~0x00002000u;
}
inline void Transaction::clear_bidindex() {
  bidindex_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  clear_has_bidindex();
}
inline const ::std::string& Transaction::bidindex() const {
  // @@protoc_insertion_point(field_get:LYPROTO.QUOTA.Transaction.BidIndex)
  return bidindex_.GetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void Transaction::set_bidindex(const ::std::string& value) {
  set_has_bidindex();
  bidindex_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:LYPROTO.QUOTA.Transaction.BidIndex)
}
inline void Transaction::set_bidindex(const char* value) {
  set_has_bidindex();
  bidindex_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:LYPROTO.QUOTA.Transaction.BidIndex)
}
inline void Transaction::set_bidindex(const char* value, size_t size) {
  set_has_bidindex();
  bidindex_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:LYPROTO.QUOTA.Transaction.BidIndex)
}
inline ::std::string* Transaction::mutable_bidindex() {
  set_has_bidindex();
  // @@protoc_insertion_point(field_mutable:LYPROTO.QUOTA.Transaction.BidIndex)
  return bidindex_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline ::std::string* Transaction::release_bidindex() {
  // @@protoc_insertion_point(field_release:LYPROTO.QUOTA.Transaction.BidIndex)
  clear_has_bidindex();
  return bidindex_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void Transaction::set_allocated_bidindex(::std::string* bidindex) {
  if (bidindex != NULL) {
    set_has_bidindex();
  } else {
    clear_has_bidindex();
  }
  bidindex_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), bidindex);
  // @@protoc_insertion_point(field_set_allocated:LYPROTO.QUOTA.Transaction.BidIndex)
}

// optional string reserve = 15;
inline bool Transaction::has_reserve() const {
  return (_has_bits_[0] & 0x00004000u) != 0;
}
inline void Transaction::set_has_reserve() {
  _has_bits_[0] |= 0x00004000u;
}
inline void Transaction::clear_has_reserve() {
  _has_bits_[0] &= ~0x00004000u;
}
inline void Transaction::clear_reserve() {
  reserve_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  clear_has_reserve();
}
inline const ::std::string& Transaction::reserve() const {
  // @@protoc_insertion_point(field_get:LYPROTO.QUOTA.Transaction.reserve)
  return reserve_.GetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void Transaction::set_reserve(const ::std::string& value) {
  set_has_reserve();
  reserve_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:LYPROTO.QUOTA.Transaction.reserve)
}
inline void Transaction::set_reserve(const char* value) {
  set_has_reserve();
  reserve_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:LYPROTO.QUOTA.Transaction.reserve)
}
inline void Transaction::set_reserve(const char* value, size_t size) {
  set_has_reserve();
  reserve_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:LYPROTO.QUOTA.Transaction.reserve)
}
inline ::std::string* Transaction::mutable_reserve() {
  set_has_reserve();
  // @@protoc_insertion_point(field_mutable:LYPROTO.QUOTA.Transaction.reserve)
  return reserve_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline ::std::string* Transaction::release_reserve() {
  // @@protoc_insertion_point(field_release:LYPROTO.QUOTA.Transaction.reserve)
  clear_has_reserve();
  return reserve_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void Transaction::set_allocated_reserve(::std::string* reserve) {
  if (reserve != NULL) {
    set_has_reserve();
  } else {
    clear_has_reserve();
  }
  reserve_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), reserve);
  // @@protoc_insertion_point(field_set_allocated:LYPROTO.QUOTA.Transaction.reserve)
}

inline const Transaction* Transaction::internal_default_instance() {
  return &Transaction_default_instance_.get();
}
// -------------------------------------------------------------------

// FutureMarketData

// required string ExchId = 1;
inline bool FutureMarketData::has_exchid() const {
  return (_has_bits_[0] & 0x00000001u) != 0;
}
inline void FutureMarketData::set_has_exchid() {
  _has_bits_[0] |= 0x00000001u;
}
inline void FutureMarketData::clear_has_exchid() {
  _has_bits_[0] &= ~0x00000001u;
}
inline void FutureMarketData::clear_exchid() {
  exchid_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  clear_has_exchid();
}
inline const ::std::string& FutureMarketData::exchid() const {
  // @@protoc_insertion_point(field_get:LYPROTO.QUOTA.FutureMarketData.ExchId)
  return exchid_.GetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void FutureMarketData::set_exchid(const ::std::string& value) {
  set_has_exchid();
  exchid_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:LYPROTO.QUOTA.FutureMarketData.ExchId)
}
inline void FutureMarketData::set_exchid(const char* value) {
  set_has_exchid();
  exchid_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:LYPROTO.QUOTA.FutureMarketData.ExchId)
}
inline void FutureMarketData::set_exchid(const char* value, size_t size) {
  set_has_exchid();
  exchid_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:LYPROTO.QUOTA.FutureMarketData.ExchId)
}
inline ::std::string* FutureMarketData::mutable_exchid() {
  set_has_exchid();
  // @@protoc_insertion_point(field_mutable:LYPROTO.QUOTA.FutureMarketData.ExchId)
  return exchid_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline ::std::string* FutureMarketData::release_exchid() {
  // @@protoc_insertion_point(field_release:LYPROTO.QUOTA.FutureMarketData.ExchId)
  clear_has_exchid();
  return exchid_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void FutureMarketData::set_allocated_exchid(::std::string* exchid) {
  if (exchid != NULL) {
    set_has_exchid();
  } else {
    clear_has_exchid();
  }
  exchid_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), exchid);
  // @@protoc_insertion_point(field_set_allocated:LYPROTO.QUOTA.FutureMarketData.ExchId)
}

// required string Category = 2;
inline bool FutureMarketData::has_category() const {
  return (_has_bits_[0] & 0x00000002u) != 0;
}
inline void FutureMarketData::set_has_category() {
  _has_bits_[0] |= 0x00000002u;
}
inline void FutureMarketData::clear_has_category() {
  _has_bits_[0] &= ~0x00000002u;
}
inline void FutureMarketData::clear_category() {
  category_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  clear_has_category();
}
inline const ::std::string& FutureMarketData::category() const {
  // @@protoc_insertion_point(field_get:LYPROTO.QUOTA.FutureMarketData.Category)
  return category_.GetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void FutureMarketData::set_category(const ::std::string& value) {
  set_has_category();
  category_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:LYPROTO.QUOTA.FutureMarketData.Category)
}
inline void FutureMarketData::set_category(const char* value) {
  set_has_category();
  category_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:LYPROTO.QUOTA.FutureMarketData.Category)
}
inline void FutureMarketData::set_category(const char* value, size_t size) {
  set_has_category();
  category_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:LYPROTO.QUOTA.FutureMarketData.Category)
}
inline ::std::string* FutureMarketData::mutable_category() {
  set_has_category();
  // @@protoc_insertion_point(field_mutable:LYPROTO.QUOTA.FutureMarketData.Category)
  return category_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline ::std::string* FutureMarketData::release_category() {
  // @@protoc_insertion_point(field_release:LYPROTO.QUOTA.FutureMarketData.Category)
  clear_has_category();
  return category_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void FutureMarketData::set_allocated_category(::std::string* category) {
  if (category != NULL) {
    set_has_category();
  } else {
    clear_has_category();
  }
  category_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), category);
  // @@protoc_insertion_point(field_set_allocated:LYPROTO.QUOTA.FutureMarketData.Category)
}

// required string stkId = 3;
inline bool FutureMarketData::has_stkid() const {
  return (_has_bits_[0] & 0x00000004u) != 0;
}
inline void FutureMarketData::set_has_stkid() {
  _has_bits_[0] |= 0x00000004u;
}
inline void FutureMarketData::clear_has_stkid() {
  _has_bits_[0] &= ~0x00000004u;
}
inline void FutureMarketData::clear_stkid() {
  stkid_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  clear_has_stkid();
}
inline const ::std::string& FutureMarketData::stkid() const {
  // @@protoc_insertion_point(field_get:LYPROTO.QUOTA.FutureMarketData.stkId)
  return stkid_.GetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void FutureMarketData::set_stkid(const ::std::string& value) {
  set_has_stkid();
  stkid_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:LYPROTO.QUOTA.FutureMarketData.stkId)
}
inline void FutureMarketData::set_stkid(const char* value) {
  set_has_stkid();
  stkid_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:LYPROTO.QUOTA.FutureMarketData.stkId)
}
inline void FutureMarketData::set_stkid(const char* value, size_t size) {
  set_has_stkid();
  stkid_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:LYPROTO.QUOTA.FutureMarketData.stkId)
}
inline ::std::string* FutureMarketData::mutable_stkid() {
  set_has_stkid();
  // @@protoc_insertion_point(field_mutable:LYPROTO.QUOTA.FutureMarketData.stkId)
  return stkid_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline ::std::string* FutureMarketData::release_stkid() {
  // @@protoc_insertion_point(field_release:LYPROTO.QUOTA.FutureMarketData.stkId)
  clear_has_stkid();
  return stkid_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void FutureMarketData::set_allocated_stkid(::std::string* stkid) {
  if (stkid != NULL) {
    set_has_stkid();
  } else {
    clear_has_stkid();
  }
  stkid_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), stkid);
  // @@protoc_insertion_point(field_set_allocated:LYPROTO.QUOTA.FutureMarketData.stkId)
}

// optional string RcvSvrTime = 4;
inline bool FutureMarketData::has_rcvsvrtime() const {
  return (_has_bits_[0] & 0x00000008u) != 0;
}
inline void FutureMarketData::set_has_rcvsvrtime() {
  _has_bits_[0] |= 0x00000008u;
}
inline void FutureMarketData::clear_has_rcvsvrtime() {
  _has_bits_[0] &= ~0x00000008u;
}
inline void FutureMarketData::clear_rcvsvrtime() {
  rcvsvrtime_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  clear_has_rcvsvrtime();
}
inline const ::std::string& FutureMarketData::rcvsvrtime() const {
  // @@protoc_insertion_point(field_get:LYPROTO.QUOTA.FutureMarketData.RcvSvrTime)
  return rcvsvrtime_.GetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void FutureMarketData::set_rcvsvrtime(const ::std::string& value) {
  set_has_rcvsvrtime();
  rcvsvrtime_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:LYPROTO.QUOTA.FutureMarketData.RcvSvrTime)
}
inline void FutureMarketData::set_rcvsvrtime(const char* value) {
  set_has_rcvsvrtime();
  rcvsvrtime_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:LYPROTO.QUOTA.FutureMarketData.RcvSvrTime)
}
inline void FutureMarketData::set_rcvsvrtime(const char* value, size_t size) {
  set_has_rcvsvrtime();
  rcvsvrtime_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:LYPROTO.QUOTA.FutureMarketData.RcvSvrTime)
}
inline ::std::string* FutureMarketData::mutable_rcvsvrtime() {
  set_has_rcvsvrtime();
  // @@protoc_insertion_point(field_mutable:LYPROTO.QUOTA.FutureMarketData.RcvSvrTime)
  return rcvsvrtime_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline ::std::string* FutureMarketData::release_rcvsvrtime() {
  // @@protoc_insertion_point(field_release:LYPROTO.QUOTA.FutureMarketData.RcvSvrTime)
  clear_has_rcvsvrtime();
  return rcvsvrtime_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void FutureMarketData::set_allocated_rcvsvrtime(::std::string* rcvsvrtime) {
  if (rcvsvrtime != NULL) {
    set_has_rcvsvrtime();
  } else {
    clear_has_rcvsvrtime();
  }
  rcvsvrtime_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), rcvsvrtime);
  // @@protoc_insertion_point(field_set_allocated:LYPROTO.QUOTA.FutureMarketData.RcvSvrTime)
}

// optional string PubSvrTime = 5;
inline bool FutureMarketData::has_pubsvrtime() const {
  return (_has_bits_[0] & 0x00000010u) != 0;
}
inline void FutureMarketData::set_has_pubsvrtime() {
  _has_bits_[0] |= 0x00000010u;
}
inline void FutureMarketData::clear_has_pubsvrtime() {
  _has_bits_[0] &= ~0x00000010u;
}
inline void FutureMarketData::clear_pubsvrtime() {
  pubsvrtime_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  clear_has_pubsvrtime();
}
inline const ::std::string& FutureMarketData::pubsvrtime() const {
  // @@protoc_insertion_point(field_get:LYPROTO.QUOTA.FutureMarketData.PubSvrTime)
  return pubsvrtime_.GetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void FutureMarketData::set_pubsvrtime(const ::std::string& value) {
  set_has_pubsvrtime();
  pubsvrtime_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:LYPROTO.QUOTA.FutureMarketData.PubSvrTime)
}
inline void FutureMarketData::set_pubsvrtime(const char* value) {
  set_has_pubsvrtime();
  pubsvrtime_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:LYPROTO.QUOTA.FutureMarketData.PubSvrTime)
}
inline void FutureMarketData::set_pubsvrtime(const char* value, size_t size) {
  set_has_pubsvrtime();
  pubsvrtime_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:LYPROTO.QUOTA.FutureMarketData.PubSvrTime)
}
inline ::std::string* FutureMarketData::mutable_pubsvrtime() {
  set_has_pubsvrtime();
  // @@protoc_insertion_point(field_mutable:LYPROTO.QUOTA.FutureMarketData.PubSvrTime)
  return pubsvrtime_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline ::std::string* FutureMarketData::release_pubsvrtime() {
  // @@protoc_insertion_point(field_release:LYPROTO.QUOTA.FutureMarketData.PubSvrTime)
  clear_has_pubsvrtime();
  return pubsvrtime_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void FutureMarketData::set_allocated_pubsvrtime(::std::string* pubsvrtime) {
  if (pubsvrtime != NULL) {
    set_has_pubsvrtime();
  } else {
    clear_has_pubsvrtime();
  }
  pubsvrtime_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), pubsvrtime);
  // @@protoc_insertion_point(field_set_allocated:LYPROTO.QUOTA.FutureMarketData.PubSvrTime)
}

// optional string Status = 6;
inline bool FutureMarketData::has_status() const {
  return (_has_bits_[0] & 0x00000020u) != 0;
}
inline void FutureMarketData::set_has_status() {
  _has_bits_[0] |= 0x00000020u;
}
inline void FutureMarketData::clear_has_status() {
  _has_bits_[0] &= ~0x00000020u;
}
inline void FutureMarketData::clear_status() {
  status_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  clear_has_status();
}
inline const ::std::string& FutureMarketData::status() const {
  // @@protoc_insertion_point(field_get:LYPROTO.QUOTA.FutureMarketData.Status)
  return status_.GetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void FutureMarketData::set_status(const ::std::string& value) {
  set_has_status();
  status_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:LYPROTO.QUOTA.FutureMarketData.Status)
}
inline void FutureMarketData::set_status(const char* value) {
  set_has_status();
  status_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:LYPROTO.QUOTA.FutureMarketData.Status)
}
inline void FutureMarketData::set_status(const char* value, size_t size) {
  set_has_status();
  status_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:LYPROTO.QUOTA.FutureMarketData.Status)
}
inline ::std::string* FutureMarketData::mutable_status() {
  set_has_status();
  // @@protoc_insertion_point(field_mutable:LYPROTO.QUOTA.FutureMarketData.Status)
  return status_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline ::std::string* FutureMarketData::release_status() {
  // @@protoc_insertion_point(field_release:LYPROTO.QUOTA.FutureMarketData.Status)
  clear_has_status();
  return status_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void FutureMarketData::set_allocated_status(::std::string* status) {
  if (status != NULL) {
    set_has_status();
  } else {
    clear_has_status();
  }
  status_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), status);
  // @@protoc_insertion_point(field_set_allocated:LYPROTO.QUOTA.FutureMarketData.Status)
}

// optional string ExchTime = 7;
inline bool FutureMarketData::has_exchtime() const {
  return (_has_bits_[0] & 0x00000040u) != 0;
}
inline void FutureMarketData::set_has_exchtime() {
  _has_bits_[0] |= 0x00000040u;
}
inline void FutureMarketData::clear_has_exchtime() {
  _has_bits_[0] &= ~0x00000040u;
}
inline void FutureMarketData::clear_exchtime() {
  exchtime_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  clear_has_exchtime();
}
inline const ::std::string& FutureMarketData::exchtime() const {
  // @@protoc_insertion_point(field_get:LYPROTO.QUOTA.FutureMarketData.ExchTime)
  return exchtime_.GetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void FutureMarketData::set_exchtime(const ::std::string& value) {
  set_has_exchtime();
  exchtime_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:LYPROTO.QUOTA.FutureMarketData.ExchTime)
}
inline void FutureMarketData::set_exchtime(const char* value) {
  set_has_exchtime();
  exchtime_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:LYPROTO.QUOTA.FutureMarketData.ExchTime)
}
inline void FutureMarketData::set_exchtime(const char* value, size_t size) {
  set_has_exchtime();
  exchtime_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:LYPROTO.QUOTA.FutureMarketData.ExchTime)
}
inline ::std::string* FutureMarketData::mutable_exchtime() {
  set_has_exchtime();
  // @@protoc_insertion_point(field_mutable:LYPROTO.QUOTA.FutureMarketData.ExchTime)
  return exchtime_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline ::std::string* FutureMarketData::release_exchtime() {
  // @@protoc_insertion_point(field_release:LYPROTO.QUOTA.FutureMarketData.ExchTime)
  clear_has_exchtime();
  return exchtime_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void FutureMarketData::set_allocated_exchtime(::std::string* exchtime) {
  if (exchtime != NULL) {
    set_has_exchtime();
  } else {
    clear_has_exchtime();
  }
  exchtime_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), exchtime);
  // @@protoc_insertion_point(field_set_allocated:LYPROTO.QUOTA.FutureMarketData.ExchTime)
}

// optional string TradeDate = 8;
inline bool FutureMarketData::has_tradedate() const {
  return (_has_bits_[0] & 0x00000080u) != 0;
}
inline void FutureMarketData::set_has_tradedate() {
  _has_bits_[0] |= 0x00000080u;
}
inline void FutureMarketData::clear_has_tradedate() {
  _has_bits_[0] &= ~0x00000080u;
}
inline void FutureMarketData::clear_tradedate() {
  tradedate_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  clear_has_tradedate();
}
inline const ::std::string& FutureMarketData::tradedate() const {
  // @@protoc_insertion_point(field_get:LYPROTO.QUOTA.FutureMarketData.TradeDate)
  return tradedate_.GetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void FutureMarketData::set_tradedate(const ::std::string& value) {
  set_has_tradedate();
  tradedate_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:LYPROTO.QUOTA.FutureMarketData.TradeDate)
}
inline void FutureMarketData::set_tradedate(const char* value) {
  set_has_tradedate();
  tradedate_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:LYPROTO.QUOTA.FutureMarketData.TradeDate)
}
inline void FutureMarketData::set_tradedate(const char* value, size_t size) {
  set_has_tradedate();
  tradedate_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:LYPROTO.QUOTA.FutureMarketData.TradeDate)
}
inline ::std::string* FutureMarketData::mutable_tradedate() {
  set_has_tradedate();
  // @@protoc_insertion_point(field_mutable:LYPROTO.QUOTA.FutureMarketData.TradeDate)
  return tradedate_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline ::std::string* FutureMarketData::release_tradedate() {
  // @@protoc_insertion_point(field_release:LYPROTO.QUOTA.FutureMarketData.TradeDate)
  clear_has_tradedate();
  return tradedate_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void FutureMarketData::set_allocated_tradedate(::std::string* tradedate) {
  if (tradedate != NULL) {
    set_has_tradedate();
  } else {
    clear_has_tradedate();
  }
  tradedate_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), tradedate);
  // @@protoc_insertion_point(field_set_allocated:LYPROTO.QUOTA.FutureMarketData.TradeDate)
}

// optional int64 PreClose = 9;
inline bool FutureMarketData::has_preclose() const {
  return (_has_bits_[0] & 0x00000100u) != 0;
}
inline void FutureMarketData::set_has_preclose() {
  _has_bits_[0] |= 0x00000100u;
}
inline void FutureMarketData::clear_has_preclose() {
  _has_bits_[0] &= ~0x00000100u;
}
inline void FutureMarketData::clear_preclose() {
  preclose_ = GOOGLE_LONGLONG(0);
  clear_has_preclose();
}
inline ::google::protobuf::int64 FutureMarketData::preclose() const {
  // @@protoc_insertion_point(field_get:LYPROTO.QUOTA.FutureMarketData.PreClose)
  return preclose_;
}
inline void FutureMarketData::set_preclose(::google::protobuf::int64 value) {
  set_has_preclose();
  preclose_ = value;
  // @@protoc_insertion_point(field_set:LYPROTO.QUOTA.FutureMarketData.PreClose)
}

// optional int64 PreSettle = 10;
inline bool FutureMarketData::has_presettle() const {
  return (_has_bits_[0] & 0x00000200u) != 0;
}
inline void FutureMarketData::set_has_presettle() {
  _has_bits_[0] |= 0x00000200u;
}
inline void FutureMarketData::clear_has_presettle() {
  _has_bits_[0] &= ~0x00000200u;
}
inline void FutureMarketData::clear_presettle() {
  presettle_ = GOOGLE_LONGLONG(0);
  clear_has_presettle();
}
inline ::google::protobuf::int64 FutureMarketData::presettle() const {
  // @@protoc_insertion_point(field_get:LYPROTO.QUOTA.FutureMarketData.PreSettle)
  return presettle_;
}
inline void FutureMarketData::set_presettle(::google::protobuf::int64 value) {
  set_has_presettle();
  presettle_ = value;
  // @@protoc_insertion_point(field_set:LYPROTO.QUOTA.FutureMarketData.PreSettle)
}

// optional int32 PreOpenPos = 11;
inline bool FutureMarketData::has_preopenpos() const {
  return (_has_bits_[0] & 0x00000400u) != 0;
}
inline void FutureMarketData::set_has_preopenpos() {
  _has_bits_[0] |= 0x00000400u;
}
inline void FutureMarketData::clear_has_preopenpos() {
  _has_bits_[0] &= ~0x00000400u;
}
inline void FutureMarketData::clear_preopenpos() {
  preopenpos_ = 0;
  clear_has_preopenpos();
}
inline ::google::protobuf::int32 FutureMarketData::preopenpos() const {
  // @@protoc_insertion_point(field_get:LYPROTO.QUOTA.FutureMarketData.PreOpenPos)
  return preopenpos_;
}
inline void FutureMarketData::set_preopenpos(::google::protobuf::int32 value) {
  set_has_preopenpos();
  preopenpos_ = value;
  // @@protoc_insertion_point(field_set:LYPROTO.QUOTA.FutureMarketData.PreOpenPos)
}

// optional int64 HighLimit = 12;
inline bool FutureMarketData::has_highlimit() const {
  return (_has_bits_[0] & 0x00000800u) != 0;
}
inline void FutureMarketData::set_has_highlimit() {
  _has_bits_[0] |= 0x00000800u;
}
inline void FutureMarketData::clear_has_highlimit() {
  _has_bits_[0] &= ~0x00000800u;
}
inline void FutureMarketData::clear_highlimit() {
  highlimit_ = GOOGLE_LONGLONG(0);
  clear_has_highlimit();
}
inline ::google::protobuf::int64 FutureMarketData::highlimit() const {
  // @@protoc_insertion_point(field_get:LYPROTO.QUOTA.FutureMarketData.HighLimit)
  return highlimit_;
}
inline void FutureMarketData::set_highlimit(::google::protobuf::int64 value) {
  set_has_highlimit();
  highlimit_ = value;
  // @@protoc_insertion_point(field_set:LYPROTO.QUOTA.FutureMarketData.HighLimit)
}

// optional int64 LowLimit = 13;
inline bool FutureMarketData::has_lowlimit() const {
  return (_has_bits_[0] & 0x00001000u) != 0;
}
inline void FutureMarketData::set_has_lowlimit() {
  _has_bits_[0] |= 0x00001000u;
}
inline void FutureMarketData::clear_has_lowlimit() {
  _has_bits_[0] &= ~0x00001000u;
}
inline void FutureMarketData::clear_lowlimit() {
  lowlimit_ = GOOGLE_LONGLONG(0);
  clear_has_lowlimit();
}
inline ::google::protobuf::int64 FutureMarketData::lowlimit() const {
  // @@protoc_insertion_point(field_get:LYPROTO.QUOTA.FutureMarketData.LowLimit)
  return lowlimit_;
}
inline void FutureMarketData::set_lowlimit(::google::protobuf::int64 value) {
  set_has_lowlimit();
  lowlimit_ = value;
  // @@protoc_insertion_point(field_set:LYPROTO.QUOTA.FutureMarketData.LowLimit)
}

// optional int64 Open = 14;
inline bool FutureMarketData::has_open() const {
  return (_has_bits_[0] & 0x00002000u) != 0;
}
inline void FutureMarketData::set_has_open() {
  _has_bits_[0] |= 0x00002000u;
}
inline void FutureMarketData::clear_has_open() {
  _has_bits_[0] &= ~0x00002000u;
}
inline void FutureMarketData::clear_open() {
  open_ = GOOGLE_LONGLONG(0);
  clear_has_open();
}
inline ::google::protobuf::int64 FutureMarketData::open() const {
  // @@protoc_insertion_point(field_get:LYPROTO.QUOTA.FutureMarketData.Open)
  return open_;
}
inline void FutureMarketData::set_open(::google::protobuf::int64 value) {
  set_has_open();
  open_ = value;
  // @@protoc_insertion_point(field_set:LYPROTO.QUOTA.FutureMarketData.Open)
}

// optional int64 Latest = 15;
inline bool FutureMarketData::has_latest() const {
  return (_has_bits_[0] & 0x00004000u) != 0;
}
inline void FutureMarketData::set_has_latest() {
  _has_bits_[0] |= 0x00004000u;
}
inline void FutureMarketData::clear_has_latest() {
  _has_bits_[0] &= ~0x00004000u;
}
inline void FutureMarketData::clear_latest() {
  latest_ = GOOGLE_LONGLONG(0);
  clear_has_latest();
}
inline ::google::protobuf::int64 FutureMarketData::latest() const {
  // @@protoc_insertion_point(field_get:LYPROTO.QUOTA.FutureMarketData.Latest)
  return latest_;
}
inline void FutureMarketData::set_latest(::google::protobuf::int64 value) {
  set_has_latest();
  latest_ = value;
  // @@protoc_insertion_point(field_set:LYPROTO.QUOTA.FutureMarketData.Latest)
}

// optional int64 High = 16;
inline bool FutureMarketData::has_high() const {
  return (_has_bits_[0] & 0x00008000u) != 0;
}
inline void FutureMarketData::set_has_high() {
  _has_bits_[0] |= 0x00008000u;
}
inline void FutureMarketData::clear_has_high() {
  _has_bits_[0] &= ~0x00008000u;
}
inline void FutureMarketData::clear_high() {
  high_ = GOOGLE_LONGLONG(0);
  clear_has_high();
}
inline ::google::protobuf::int64 FutureMarketData::high() const {
  // @@protoc_insertion_point(field_get:LYPROTO.QUOTA.FutureMarketData.High)
  return high_;
}
inline void FutureMarketData::set_high(::google::protobuf::int64 value) {
  set_has_high();
  high_ = value;
  // @@protoc_insertion_point(field_set:LYPROTO.QUOTA.FutureMarketData.High)
}

// optional int64 Low = 17;
inline bool FutureMarketData::has_low() const {
  return (_has_bits_[0] & 0x00010000u) != 0;
}
inline void FutureMarketData::set_has_low() {
  _has_bits_[0] |= 0x00010000u;
}
inline void FutureMarketData::clear_has_low() {
  _has_bits_[0] &= ~0x00010000u;
}
inline void FutureMarketData::clear_low() {
  low_ = GOOGLE_LONGLONG(0);
  clear_has_low();
}
inline ::google::protobuf::int64 FutureMarketData::low() const {
  // @@protoc_insertion_point(field_get:LYPROTO.QUOTA.FutureMarketData.Low)
  return low_;
}
inline void FutureMarketData::set_low(::google::protobuf::int64 value) {
  set_has_low();
  low_ = value;
  // @@protoc_insertion_point(field_set:LYPROTO.QUOTA.FutureMarketData.Low)
}

// optional int64 Settle = 18;
inline bool FutureMarketData::has_settle() const {
  return (_has_bits_[0] & 0x00020000u) != 0;
}
inline void FutureMarketData::set_has_settle() {
  _has_bits_[0] |= 0x00020000u;
}
inline void FutureMarketData::clear_has_settle() {
  _has_bits_[0] &= ~0x00020000u;
}
inline void FutureMarketData::clear_settle() {
  settle_ = GOOGLE_LONGLONG(0);
  clear_has_settle();
}
inline ::google::protobuf::int64 FutureMarketData::settle() const {
  // @@protoc_insertion_point(field_get:LYPROTO.QUOTA.FutureMarketData.Settle)
  return settle_;
}
inline void FutureMarketData::set_settle(::google::protobuf::int64 value) {
  set_has_settle();
  settle_ = value;
  // @@protoc_insertion_point(field_set:LYPROTO.QUOTA.FutureMarketData.Settle)
}

// optional int32 LatestVolume = 19;
inline bool FutureMarketData::has_latestvolume() const {
  return (_has_bits_[0] & 0x00040000u) != 0;
}
inline void FutureMarketData::set_has_latestvolume() {
  _has_bits_[0] |= 0x00040000u;
}
inline void FutureMarketData::clear_has_latestvolume() {
  _has_bits_[0] &= ~0x00040000u;
}
inline void FutureMarketData::clear_latestvolume() {
  latestvolume_ = 0;
  clear_has_latestvolume();
}
inline ::google::protobuf::int32 FutureMarketData::latestvolume() const {
  // @@protoc_insertion_point(field_get:LYPROTO.QUOTA.FutureMarketData.LatestVolume)
  return latestvolume_;
}
inline void FutureMarketData::set_latestvolume(::google::protobuf::int32 value) {
  set_has_latestvolume();
  latestvolume_ = value;
  // @@protoc_insertion_point(field_set:LYPROTO.QUOTA.FutureMarketData.LatestVolume)
}

// optional int32 Volume = 20;
inline bool FutureMarketData::has_volume() const {
  return (_has_bits_[0] & 0x00080000u) != 0;
}
inline void FutureMarketData::set_has_volume() {
  _has_bits_[0] |= 0x00080000u;
}
inline void FutureMarketData::clear_has_volume() {
  _has_bits_[0] &= ~0x00080000u;
}
inline void FutureMarketData::clear_volume() {
  volume_ = 0;
  clear_has_volume();
}
inline ::google::protobuf::int32 FutureMarketData::volume() const {
  // @@protoc_insertion_point(field_get:LYPROTO.QUOTA.FutureMarketData.Volume)
  return volume_;
}
inline void FutureMarketData::set_volume(::google::protobuf::int32 value) {
  set_has_volume();
  volume_ = value;
  // @@protoc_insertion_point(field_set:LYPROTO.QUOTA.FutureMarketData.Volume)
}

// optional int32 OpenPos = 21;
inline bool FutureMarketData::has_openpos() const {
  return (_has_bits_[0] & 0x00100000u) != 0;
}
inline void FutureMarketData::set_has_openpos() {
  _has_bits_[0] |= 0x00100000u;
}
inline void FutureMarketData::clear_has_openpos() {
  _has_bits_[0] &= ~0x00100000u;
}
inline void FutureMarketData::clear_openpos() {
  openpos_ = 0;
  clear_has_openpos();
}
inline ::google::protobuf::int32 FutureMarketData::openpos() const {
  // @@protoc_insertion_point(field_get:LYPROTO.QUOTA.FutureMarketData.OpenPos)
  return openpos_;
}
inline void FutureMarketData::set_openpos(::google::protobuf::int32 value) {
  set_has_openpos();
  openpos_ = value;
  // @@protoc_insertion_point(field_set:LYPROTO.QUOTA.FutureMarketData.OpenPos)
}

// optional int64 Value = 22;
inline bool FutureMarketData::has_value() const {
  return (_has_bits_[0] & 0x00200000u) != 0;
}
inline void FutureMarketData::set_has_value() {
  _has_bits_[0] |= 0x00200000u;
}
inline void FutureMarketData::clear_has_value() {
  _has_bits_[0] &= ~0x00200000u;
}
inline void FutureMarketData::clear_value() {
  value_ = GOOGLE_LONGLONG(0);
  clear_has_value();
}
inline ::google::protobuf::int64 FutureMarketData::value() const {
  // @@protoc_insertion_point(field_get:LYPROTO.QUOTA.FutureMarketData.Value)
  return value_;
}
inline void FutureMarketData::set_value(::google::protobuf::int64 value) {
  set_has_value();
  value_ = value;
  // @@protoc_insertion_point(field_set:LYPROTO.QUOTA.FutureMarketData.Value)
}

// repeated int32 BA = 23 [packed = true];
inline int FutureMarketData::ba_size() const {
  return ba_.size();
}
inline void FutureMarketData::clear_ba() {
  ba_.Clear();
}
inline ::google::protobuf::int32 FutureMarketData::ba(int index) const {
  // @@protoc_insertion_point(field_get:LYPROTO.QUOTA.FutureMarketData.BA)
  return ba_.Get(index);
}
inline void FutureMarketData::set_ba(int index, ::google::protobuf::int32 value) {
  ba_.Set(index, value);
  // @@protoc_insertion_point(field_set:LYPROTO.QUOTA.FutureMarketData.BA)
}
inline void FutureMarketData::add_ba(::google::protobuf::int32 value) {
  ba_.Add(value);
  // @@protoc_insertion_point(field_add:LYPROTO.QUOTA.FutureMarketData.BA)
}
inline const ::google::protobuf::RepeatedField< ::google::protobuf::int32 >&
FutureMarketData::ba() const {
  // @@protoc_insertion_point(field_list:LYPROTO.QUOTA.FutureMarketData.BA)
  return ba_;
}
inline ::google::protobuf::RepeatedField< ::google::protobuf::int32 >*
FutureMarketData::mutable_ba() {
  // @@protoc_insertion_point(field_mutable_list:LYPROTO.QUOTA.FutureMarketData.BA)
  return &ba_;
}

// repeated int64 BP = 24 [packed = true];
inline int FutureMarketData::bp_size() const {
  return bp_.size();
}
inline void FutureMarketData::clear_bp() {
  bp_.Clear();
}
inline ::google::protobuf::int64 FutureMarketData::bp(int index) const {
  // @@protoc_insertion_point(field_get:LYPROTO.QUOTA.FutureMarketData.BP)
  return bp_.Get(index);
}
inline void FutureMarketData::set_bp(int index, ::google::protobuf::int64 value) {
  bp_.Set(index, value);
  // @@protoc_insertion_point(field_set:LYPROTO.QUOTA.FutureMarketData.BP)
}
inline void FutureMarketData::add_bp(::google::protobuf::int64 value) {
  bp_.Add(value);
  // @@protoc_insertion_point(field_add:LYPROTO.QUOTA.FutureMarketData.BP)
}
inline const ::google::protobuf::RepeatedField< ::google::protobuf::int64 >&
FutureMarketData::bp() const {
  // @@protoc_insertion_point(field_list:LYPROTO.QUOTA.FutureMarketData.BP)
  return bp_;
}
inline ::google::protobuf::RepeatedField< ::google::protobuf::int64 >*
FutureMarketData::mutable_bp() {
  // @@protoc_insertion_point(field_mutable_list:LYPROTO.QUOTA.FutureMarketData.BP)
  return &bp_;
}

// repeated int32 SA = 25 [packed = true];
inline int FutureMarketData::sa_size() const {
  return sa_.size();
}
inline void FutureMarketData::clear_sa() {
  sa_.Clear();
}
inline ::google::protobuf::int32 FutureMarketData::sa(int index) const {
  // @@protoc_insertion_point(field_get:LYPROTO.QUOTA.FutureMarketData.SA)
  return sa_.Get(index);
}
inline void FutureMarketData::set_sa(int index, ::google::protobuf::int32 value) {
  sa_.Set(index, value);
  // @@protoc_insertion_point(field_set:LYPROTO.QUOTA.FutureMarketData.SA)
}
inline void FutureMarketData::add_sa(::google::protobuf::int32 value) {
  sa_.Add(value);
  // @@protoc_insertion_point(field_add:LYPROTO.QUOTA.FutureMarketData.SA)
}
inline const ::google::protobuf::RepeatedField< ::google::protobuf::int32 >&
FutureMarketData::sa() const {
  // @@protoc_insertion_point(field_list:LYPROTO.QUOTA.FutureMarketData.SA)
  return sa_;
}
inline ::google::protobuf::RepeatedField< ::google::protobuf::int32 >*
FutureMarketData::mutable_sa() {
  // @@protoc_insertion_point(field_mutable_list:LYPROTO.QUOTA.FutureMarketData.SA)
  return &sa_;
}

// repeated int64 SP = 26 [packed = true];
inline int FutureMarketData::sp_size() const {
  return sp_.size();
}
inline void FutureMarketData::clear_sp() {
  sp_.Clear();
}
inline ::google::protobuf::int64 FutureMarketData::sp(int index) const {
  // @@protoc_insertion_point(field_get:LYPROTO.QUOTA.FutureMarketData.SP)
  return sp_.Get(index);
}
inline void FutureMarketData::set_sp(int index, ::google::protobuf::int64 value) {
  sp_.Set(index, value);
  // @@protoc_insertion_point(field_set:LYPROTO.QUOTA.FutureMarketData.SP)
}
inline void FutureMarketData::add_sp(::google::protobuf::int64 value) {
  sp_.Add(value);
  // @@protoc_insertion_point(field_add:LYPROTO.QUOTA.FutureMarketData.SP)
}
inline const ::google::protobuf::RepeatedField< ::google::protobuf::int64 >&
FutureMarketData::sp() const {
  // @@protoc_insertion_point(field_list:LYPROTO.QUOTA.FutureMarketData.SP)
  return sp_;
}
inline ::google::protobuf::RepeatedField< ::google::protobuf::int64 >*
FutureMarketData::mutable_sp() {
  // @@protoc_insertion_point(field_mutable_list:LYPROTO.QUOTA.FutureMarketData.SP)
  return &sp_;
}

// optional int32 PreDelta = 27;
inline bool FutureMarketData::has_predelta() const {
  return (_has_bits_[0] & 0x04000000u) != 0;
}
inline void FutureMarketData::set_has_predelta() {
  _has_bits_[0] |= 0x04000000u;
}
inline void FutureMarketData::clear_has_predelta() {
  _has_bits_[0] &= ~0x04000000u;
}
inline void FutureMarketData::clear_predelta() {
  predelta_ = 0;
  clear_has_predelta();
}
inline ::google::protobuf::int32 FutureMarketData::predelta() const {
  // @@protoc_insertion_point(field_get:LYPROTO.QUOTA.FutureMarketData.PreDelta)
  return predelta_;
}
inline void FutureMarketData::set_predelta(::google::protobuf::int32 value) {
  set_has_predelta();
  predelta_ = value;
  // @@protoc_insertion_point(field_set:LYPROTO.QUOTA.FutureMarketData.PreDelta)
}

// optional int32 CurDelta = 28;
inline bool FutureMarketData::has_curdelta() const {
  return (_has_bits_[0] & 0x08000000u) != 0;
}
inline void FutureMarketData::set_has_curdelta() {
  _has_bits_[0] |= 0x08000000u;
}
inline void FutureMarketData::clear_has_curdelta() {
  _has_bits_[0] &= ~0x08000000u;
}
inline void FutureMarketData::clear_curdelta() {
  curdelta_ = 0;
  clear_has_curdelta();
}
inline ::google::protobuf::int32 FutureMarketData::curdelta() const {
  // @@protoc_insertion_point(field_get:LYPROTO.QUOTA.FutureMarketData.CurDelta)
  return curdelta_;
}
inline void FutureMarketData::set_curdelta(::google::protobuf::int32 value) {
  set_has_curdelta();
  curdelta_ = value;
  // @@protoc_insertion_point(field_set:LYPROTO.QUOTA.FutureMarketData.CurDelta)
}

inline const FutureMarketData* FutureMarketData::internal_default_instance() {
  return &FutureMarketData_default_instance_.get();
}
#endif  // !PROTOBUF_INLINE_NOT_IN_HEADERS
// -------------------------------------------------------------------

// -------------------------------------------------------------------


// @@protoc_insertion_point(namespace_scope)

}  // namespace QUOTA
}  // namespace LYPROTO

// @@protoc_insertion_point(global_scope)

#endif  // PROTOBUF_lyproto_2equota_2eproto__INCLUDED

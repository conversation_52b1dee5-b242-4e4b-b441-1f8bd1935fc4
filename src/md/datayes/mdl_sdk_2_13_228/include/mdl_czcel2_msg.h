// Generated by the code_gen tool.  DO NOT EDIT!
#pragma once

#include "mdl_api_types.h"

namespace datayes {
namespace mdl {
namespace mdl_czcel2_msg {

static const uint16_t MDLVID_MDL_CZCEL2 = 101;

enum MDL_CZCEL2MessageID {
	MDLMID_MDL_CZCEL2_CTPFuture = 1,
	MDLMID_MDL_CZCEL2_CTPOption = 2,
	MDLMID_MDL_CZCEL2_FutureTrdQtyStatus = 3,
	MDLMID_MDL_CZCEL2_OptionTrdQtyStatus = 4,
	MDLMID_MDL_CZCEL2_CmbTypeInfo = 5,
	MDLMID_MDL_CZCEL2_OptionParam = 6,
	MDLMID_MDL_CZCEL2_TradeStatus = 7
};

#pragma pack(1)

struct CTPFuture {
	enum {
		ServiceID = MDLSID_MDL_CZCEL2,
		ServiceVer = MDLVID_MDL_CZCEL2,
		MessageID = MDLMID_MDL_CZCEL2_CTPFuture
	};
	MDLDate ActionDay;
	MDLDate TradDay;
	MDLTime UpdateTime;
	MDLAnsiString InstruID;
	MDLDoubleT<3> LastPrice;
	MDLDoubleT<3> HighPrice;
	MDLDoubleT<3> LowPrice;
	MDLDoubleT<3> OpenPrice;
	int64_t Volume;
	MDLDoubleT<3> Turnover;
	int64_t OpenInt;
	MDLDoubleT<3> AveragePrice;
	MDLDoubleT<3> ClosePrice;
	MDLDoubleT<3> SetPrice;
	int64_t PreOpenInt;
	MDLDoubleT<3> PreCloPrice;
	MDLDoubleT<3> PreSetPrice;
	int64_t BuyVolume;
	int64_t SellVolume;
	MDLDoubleT<3> AvgBuyPrice;
	MDLDoubleT<3> AvgSellPrice;
	int64_t DerBuyVolume;
	int64_t DerSellVolume;
	struct BidBookItem {
		int64_t Volume;
		MDLDoubleT<3> Price;
		int32_t Num;
	};
	MDLListT<BidBookItem> BidBook;
	struct AskBookItem {
		int64_t Volume;
		MDLDoubleT<3> Price;
		int32_t Num;
	};
	MDLListT<AskBookItem> AskBook;
	MDLDoubleT<3> ULimitPrice;
	MDLDoubleT<3> LLimitPrice;
	MDLDoubleT<3> LifeHighPrice;
	MDLDoubleT<3> LifeLowPrice;
};

struct CTPOption {
	enum {
		ServiceID = MDLSID_MDL_CZCEL2,
		ServiceVer = MDLVID_MDL_CZCEL2,
		MessageID = MDLMID_MDL_CZCEL2_CTPOption
	};
	MDLDate ActionDay;
	MDLDate TradDay;
	MDLTime UpdateTime;
	MDLAnsiString InstruID;
	MDLDoubleT<3> LastPrice;
	MDLDoubleT<3> HighPrice;
	MDLDoubleT<3> LowPrice;
	MDLDoubleT<3> OpenPrice;
	int64_t Volume;
	MDLDoubleT<3> Turnover;
	int64_t OpenInt;
	MDLDoubleT<3> AveragePrice;
	MDLDoubleT<3> ClosePrice;
	MDLDoubleT<3> SetPrice;
	int64_t PreOpenInt;
	MDLDoubleT<3> PreCloPrice;
	MDLDoubleT<3> PreSetPrice;
	int64_t BuyVolume;
	int64_t SellVolume;
	MDLDoubleT<3> AvgBuyPrice;
	MDLDoubleT<3> AvgSellPrice;
	int64_t DerBuyVolume;
	int64_t DerSellVolume;
	struct BidBookItem {
		int64_t Volume;
		MDLDoubleT<3> Price;
		int32_t Num;
	};
	MDLListT<BidBookItem> BidBook;
	struct AskBookItem {
		int64_t Volume;
		MDLDoubleT<3> Price;
		int32_t Num;
	};
	MDLListT<AskBookItem> AskBook;
	MDLDoubleT<3> ULimitPrice;
	MDLDoubleT<3> LLimitPrice;
	MDLDoubleT<3> LifeHighPrice;
	MDLDoubleT<3> LifeLowPrice;
};

struct FutureTrdQtyStatus {
	enum {
		ServiceID = MDLSID_MDL_CZCEL2,
		ServiceVer = MDLVID_MDL_CZCEL2,
		MessageID = MDLMID_MDL_CZCEL2_FutureTrdQtyStatus
	};
	MDLDate ActionDay;
	MDLDate TradDay;
	MDLTime UpdateTime;
	MDLAnsiString InstruID;
	struct StatusFieldItem {
		MDLDoubleT<3> PriceBegin;
		MDLDoubleT<3> PriceEnd;
		int64_t BuyOpenVol;
		int64_t BuyClsVol;
		int64_t SellOpenVol;
		int64_t SellClsVol;
	};
	MDLListT<StatusFieldItem> StatusField;
};

struct OptionTrdQtyStatus {
	enum {
		ServiceID = MDLSID_MDL_CZCEL2,
		ServiceVer = MDLVID_MDL_CZCEL2,
		MessageID = MDLMID_MDL_CZCEL2_OptionTrdQtyStatus
	};
	MDLDate ActionDay;
	MDLDate TradDay;
	MDLTime UpdateTime;
	MDLAnsiString InstruID;
	struct StatusFieldItem {
		MDLDoubleT<3> PriceBegin;
		MDLDoubleT<3> PriceEnd;
		int64_t BuyOpenVol;
		int64_t BuyClsVol;
		int64_t SellOpenVol;
		int64_t SellClsVol;
	};
	MDLListT<StatusFieldItem> StatusField;
};

struct CmbTypeInfo {
	enum {
		ServiceID = MDLSID_MDL_CZCEL2,
		ServiceVer = MDLVID_MDL_CZCEL2,
		MessageID = MDLMID_MDL_CZCEL2_CmbTypeInfo
	};
	MDLDate ActionDay;
	MDLDate TradDay;
	MDLTime UpdateTime;
	int32_t Cmbtyp;
	MDLAnsiString CmbtypeId;
	MDLUTF8String CmbtypeName;
	MDLAnsiString FirstInstruID;
	MDLAnsiString SecondInstruID;
	MDLDoubleT<3> ULimitPrice;
	MDLDoubleT<3> LLimitPrice;
	int64_t BuyVolume;
	int64_t SellVolume;
	MDLDoubleT<3> AvgBuyPrice;
	MDLDoubleT<3> AvgSellPrice;
	int64_t DerBuyVolume;
	int64_t DerSellVolume;
	struct BidBookItem {
		int64_t Volume;
		MDLDoubleT<3> Price;
		int32_t Num;
	};
	MDLListT<BidBookItem> BidBook;
	struct AskBookItem {
		int64_t Volume;
		MDLDoubleT<3> Price;
		int32_t Num;
	};
	MDLListT<AskBookItem> AskBook;
};

struct OptionParam {
	enum {
		ServiceID = MDLSID_MDL_CZCEL2,
		ServiceVer = MDLVID_MDL_CZCEL2,
		MessageID = MDLMID_MDL_CZCEL2_OptionParam
	};
	MDLDate ActionDay;
	MDLDate TradDay;
	MDLTime UpdateTime;
	MDLAnsiString InstruID;
	MDLDoubleT<2> Sigma;
	MDLDoubleT<4> Delta;
	MDLDoubleT<4> Gama;
	MDLDoubleT<4> Theta;
	MDLDoubleT<4> Vega;
	MDLDoubleT<4> Rho;
};

struct TradeStatus {
	enum {
		ServiceID = MDLSID_MDL_CZCEL2,
		ServiceVer = MDLVID_MDL_CZCEL2,
		MessageID = MDLMID_MDL_CZCEL2_TradeStatus
	};
	MDLDate ActionDay;
	MDLDate TradDay;
	int32_t Index;
	MDLAnsiString InstruCode;
	int32_t InstruStatus;
	int32_t MarketStatus;
};

#pragma pack()

} // namespace mdl_czcel2_msg
} // namespace mdl
} // namespace datayes

// Generated by the code_gen tool.  DO NOT EDIT!
#pragma once

#include "mdl_api_types.h"

namespace datayes {
namespace mdl {
namespace mdl_szl2_msg {

static const uint16_t MDLVID_MDL_SZL2 = 101;

enum MDL_SZL2MessageID {
	MDLMID_MDL_SZL2_Trade = 1,
	MDLMID_MDL_SZL2_Order = 2,
	MDLMID_MDL_SZL2_Index = 3,
	MDLMID_MDL_SZL2_MarketData = 4,
	MDLMID_MDL_SZL2_StockStatus = 5,
	MDLMID_MDL_SZL2_StockInfo = 6,
	MDLMID_MDL_SZL2_SysParam = 7,
	MDLMID_MDL_SZL2_Snapshot300111 = 8,
	MDLMID_MDL_SZL2_Snapshot309011 = 9,
	MDLMID_MDL_SZL2_Snapshot309111 = 10,
	MDLMID_MDL_SZL2_Snapshot300611 = 11,
	MDLMID_MDL_SZL2_SecurityStatus = 12,
	MD<PERSON><PERSON>_MDL_SZL2_Order300192 = 13,
	MDLMID_MDL_SZL2_Order300592 = 14,
	MDLMID_MDL_SZL2_Order300792 = 15,
	MDLMID_MDL_SZL2_Transaction300191 = 16,
	MDLMID_MDL_SZL2_Transaction300591 = 17,
	MDLMID_MDL_SZL2_Transaction300791 = 18,
	MDLMID_MDL_SZL2_Bulletin = 19,
	MDLMID_MDL_SZL2_Snapshot300111_v2 = 28,
	MDLMID_MDL_SZL2_Snapshot309011_v2 = 29,
	MDLMID_MDL_SZL2_Snapshot309111_v2 = 30,
	MDLMID_MDL_SZL2_Snapshot300611_v2 = 31,
	MDLMID_MDL_SZL2_Order300192_v2 = 33,
	MDLMID_MDL_SZL2_Order300592_v2 = 34,
	MDLMID_MDL_SZL2_Order300792_v2 = 35,
	MDLMID_MDL_SZL2_Transaction300191_v2 = 36,
	MDLMID_MDL_SZL2_Transaction300591_v2 = 37,
	MDLMID_MDL_SZL2_Transaction300791_v2 = 38,
	MDLMID_MDL_SZL2_Snapshot300111_v3 = 39,
	MDLMID_MDL_SZL2_RealQuota = 40,
	MDLMID_MDL_SZL2_Snapshot306311 = 41,
	MDLMID_MDL_SZL2_Snapshot300211 = 42,
	MDLMID_MDL_SZL2_Order300292 = 43,
	MDLMID_MDL_SZL2_Transaction300291 = 44,
	MDLMID_MDL_SZL2_Order300392 = 45,
	MDLMID_MDL_SZL2_Transaction300391 = 46,
	MDLMID_MDL_SZL2_BondOrderQueue = 47,
	MDLMID_MDL_SZL2_BondDistribution = 48,
	MDLMID_MDL_SZL2_Snapshot309211 = 49,
	MDLMID_MDL_SZL2_ETFData = 50,
	MDLMID_MDL_SZL2_MarketInfo = 51,
	MDLMID_MDL_SZL2_DerivAuction = 52
};

#pragma pack(1)

struct Trade {
	enum {
		ServiceID = MDLSID_MDL_SZL2,
		ServiceVer = MDLVID_MDL_SZL2,
		MessageID = MDLMID_MDL_SZL2_Trade
	};
	uint32_t RecNo;
	uint32_t BuyOrderRecNo;
	uint32_t SellOrderRecNo;
	MDLAnsiString SecurityID;
	MDLTime TradTime;
	MDLDoubleT<3> Price;
	uint32_t TradeQty;
	MDLAnsiString OrderKind;
	MDLAnsiString FunctionCode;
	uint32_t SetNo;
};

struct Order {
	enum {
		ServiceID = MDLSID_MDL_SZL2,
		ServiceVer = MDLVID_MDL_SZL2,
		MessageID = MDLMID_MDL_SZL2_Order
	};
	uint32_t RecNo;
	MDLAnsiString SecurityID;
	MDLTime OrderEntryTime;
	MDLDoubleT<3> Price;
	uint32_t OrderQty;
	MDLAnsiString OrderKind;
	MDLAnsiString FunctionCode;
	uint32_t SetNo;
};

struct Index {
	enum {
		ServiceID = MDLSID_MDL_SZL2,
		ServiceVer = MDLVID_MDL_SZL2,
		MessageID = MDLMID_MDL_SZL2_Index
	};
	MDLAnsiString SecurityID;
	MDLDoubleT<4> OpenIndex;
	MDLDoubleT<3> Turnover;
	MDLDoubleT<4> HighIndex;
	MDLDoubleT<4> LowIndex;
	MDLDoubleT<4> LastIndex;
	MDLTime DataTimeStamp;
	int64_t NumTrades;
	int64_t TotalVolumeTraded;
	MDLDoubleT<4> CloseIndex;
	MDLAnsiString EndOfDayMaker;
};

struct MarketData {
	enum {
		ServiceID = MDLSID_MDL_SZL2,
		ServiceVer = MDLVID_MDL_SZL2,
		MessageID = MDLMID_MDL_SZL2_MarketData
	};
	MDLTime DataTimeStamp;
	MDLAnsiString SecurityID;
	MDLDoubleT<3> PreClosePx;
	MDLDoubleT<3> OpenPx;
	MDLDoubleT<3> HighPx;
	MDLDoubleT<3> LowPx;
	MDLDoubleT<3> LastPx;
	uint64_t NumTrades;
	uint64_t TotalVolumeTrade;
	MDLDoubleT<3> TotalValueTrade;
	uint64_t TotalLongPosition;
	MDLDoubleT<2> PERatio1;
	MDLDoubleT<2> PERatio2;
	MDLAnsiString EndOfDayMaker;
	uint64_t TotalOfferQty;
	MDLDoubleT<3> WeightedAvgOfferPx;
	struct OfferPriceLevelItem {
		MDLDoubleT<3> OfferPx;
		uint64_t OfferSize;
		uint32_t NumOrders;
		struct OrdersItem {
			uint32_t OrderQty;
		};
		MDLListT<OrdersItem> Orders;
	};
	MDLListT<OfferPriceLevelItem> OfferPriceLevel;
	uint64_t TotalBidQty;
	MDLDoubleT<3> WeightedAvgBidPx;
	struct BidPriceLevelItem {
		MDLDoubleT<3> BidPx;
		uint64_t BidSize;
		uint32_t NumOrders;
		struct OrdersItem {
			uint32_t OrderQty;
		};
		MDLListT<OrdersItem> Orders;
	};
	MDLListT<BidPriceLevelItem> BidPriceLevel;
};

struct StockStatus {
	enum {
		ServiceID = MDLSID_MDL_SZL2,
		ServiceVer = MDLVID_MDL_SZL2,
		MessageID = MDLMID_MDL_SZL2_StockStatus
	};
	MDLAnsiString SecurityID;
	MDLAnsiString SecurityPreName;
	MDLAnsiString TradingPhaseCode;
	MDLAnsiString CrdBuyStatus;
	MDLAnsiString CrdSellStatus;
	MDLAnsiString SubScribeStatus;
	MDLAnsiString RedemptionStatus;
	MDLAnsiString WarrantCreated;
	MDLAnsiString WarrantDropped;
};

struct StockInfo {
	enum {
		ServiceID = MDLSID_MDL_SZL2,
		ServiceVer = MDLVID_MDL_SZL2,
		MessageID = MDLMID_MDL_SZL2_StockInfo
	};
	uint32_t RecNo;
	MDLAnsiString SecurityID;
	MDLUTF8String Symbol;
	MDLAnsiString SecurityDesc;
	MDLAnsiString UnderlyingSecurityID;
	MDLAnsiString SecurityIDSource;
	struct SecurityAltIDsItem {
		MDLAnsiString SecurityAltID;
		MDLAnsiString SecurityAltIDSource;
	};
	MDLListT<SecurityAltIDsItem> SecurityAltIDs;
	MDLAnsiString IndustryClassification;
	MDLAnsiString Currency;
	MDLDoubleT<3> ShareFaceValue;
	uint64_t OutstandingShare;
	uint64_t PublicFloatShareQuantity;
	MDLDoubleT<4> PreviousYearProfitPerShare;
	MDLDoubleT<4> CurrentYearProfitPerShare;
	MDLDoubleT<4> NAV;
	struct MiscFeesItem {
		MDLAnsiString MiscFeeType;
		uint32_t MiscFeeBasis;
		MDLDoubleT<6> MiscFeeAmt;
	};
	MDLListT<MiscFeesItem> MiscFees;
	MDLDate IssueDate;
	MDLAnsiString BondType;
	MDLDoubleT<6> CouponRate;
	MDLDoubleT<3> ConversionPrice;
	MDLAnsiString American_European;
	MDLAnsiString CallOrPut;
	MDLAnsiString WarrantClearingType;
	MDLDoubleT<4> CVRatio;
	MDLDate ConversionBeginDate;
	MDLDate ConversionEndDate;
	MDLDate InterestAccrualDate;
	MDLDate MaturityDate;
	uint32_t RoundLot;
	uint32_t BidLotSize;
	uint32_t AskLotSize;
	uint32_t MaxFloor;
	MDLAnsiString TradeMethod;
	MDLDoubleT<3> PriceTickSize;
	MDLAnsiString PriceLimitType;
	MDLDoubleT<3> AuctionPriceLimit;
	MDLDoubleT<3> ContinuousTradePriceLimit;
	MDLDoubleT<3> DailyPriceUpLimit;
	MDLDoubleT<3> DailyPriceLowLimit;
	MDLDoubleT<3> DailyPriceUpLimit2;
	MDLDoubleT<3> DailyPriceLowLimit2;
	MDLDoubleT<3> ContractMultiplier;
	MDLDoubleT<3> GageRate;
	MDLAnsiString CrdBuyUnderlying;
	MDLAnsiString CrdSellUnderlying;
	struct IndicesParticipatedsItem {
		MDLAnsiString participatingIndexID;
	};
	MDLListT<IndicesParticipatedsItem> IndicesParticipateds;
	MDLAnsiString MarketMakerFlag;
	MDLAnsiString SecurityExchange;
	MDLAnsiString CFICode;
	MDLAnsiString SecuritySubType;
	MDLAnsiString SecurityProperties;
	uint32_t SecurityTradingStatus;
	MDLAnsiString CorporateAction;
	MDLAnsiString TradingMechanism;
	MDLAnsiString CrdPriceCheckType;
	MDLAnsiString NetVotingFlag;
	MDLAnsiString ShrstrurefmFlag;
	MDLAnsiString OfferingFlag;
};

struct SysParam {
	enum {
		ServiceID = MDLSID_MDL_SZL2,
		ServiceVer = MDLVID_MDL_SZL2,
		MessageID = MDLMID_MDL_SZL2_SysParam
	};
	uint32_t StockNum;
	uint32_t IndexNum;
	uint32_t SetNum;
	MDLTime SysOpenTime;
	MDLTime SysCloseTime;
};

struct Snapshot300111 {
	enum {
		ServiceID = MDLSID_MDL_SZL2,
		ServiceVer = MDLVID_MDL_SZL2,
		MessageID = MDLMID_MDL_SZL2_Snapshot300111
	};
	MDLTime UpdateTime;
	uint32_t ChannelNo;
	MDLAnsiString MDStreamID;
	MDLAnsiString SecurityID;
	MDLAnsiString SecurityIDSource;
	MDLAnsiString TradingPhaseCode;
	MDLDoubleT<3> PreCloPrice;
	int64_t TurnNum;
	int64_t Volume;
	MDLDoubleT<3> Turnover;
	MDLDoubleT<3> LastPrice;
	MDLDoubleT<3> OpenPrice;
	MDLDoubleT<3> HighPrice;
	MDLDoubleT<3> LowPrice;
	MDLDoubleT<3> DifPrice1;
	MDLDoubleT<3> DifPrice2;
	MDLDoubleT<3> PE1;
	MDLDoubleT<3> PE2;
	MDLDoubleT<3> PreCloseIOPV;
	MDLDoubleT<3> IOPV;
	int64_t TotalOfferQty;
	MDLDoubleT<3> WeightedAvgOfferPx;
	int64_t TotalBidQty;
	MDLDoubleT<3> WeightedAvgBidPx;
	MDLDoubleT<3> HighLimitPrice;
	MDLDoubleT<3> LowLimitPrice;
	int64_t OpenInt;
	MDLDoubleT<3> OptPremiumRatio;
	struct BidPriceLevelItem {
		int64_t Volume;
		MDLDoubleT<3> Price;
		uint32_t NumOrders;
		struct OrdersItem {
			int64_t OrderQty;
		};
		MDLListT<OrdersItem> Orders;
	};
	MDLListT<BidPriceLevelItem> BidPriceLevel;
	struct AskPriceLevelItem {
		int64_t Volume;
		MDLDoubleT<3> Price;
		uint32_t NumOrders;
		struct OrdersItem {
			int64_t OrderQty;
		};
		MDLListT<OrdersItem> Orders;
	};
	MDLListT<AskPriceLevelItem> AskPriceLevel;
};

struct Snapshot309011 {
	enum {
		ServiceID = MDLSID_MDL_SZL2,
		ServiceVer = MDLVID_MDL_SZL2,
		MessageID = MDLMID_MDL_SZL2_Snapshot309011
	};
	MDLTime UpdateTime;
	uint32_t ChannelNo;
	MDLAnsiString MDStreamID;
	MDLAnsiString SecurityID;
	MDLAnsiString SecurityIDSource;
	MDLAnsiString TradingPhaseCode;
	MDLDoubleT<3> PreCloPrice;
	int64_t TurnNum;
	int64_t Volume;
	MDLDoubleT<3> Turnover;
	MDLDoubleT<3> LastIndex;
	MDLDoubleT<3> PreCloIndex;
	MDLDoubleT<3> OpenIndex;
	MDLDoubleT<3> HighIndex;
	MDLDoubleT<3> LowIndex;
};

struct Snapshot309111 {
	enum {
		ServiceID = MDLSID_MDL_SZL2,
		ServiceVer = MDLVID_MDL_SZL2,
		MessageID = MDLMID_MDL_SZL2_Snapshot309111
	};
	MDLTime UpdateTime;
	uint32_t ChannelNo;
	MDLAnsiString MDStreamID;
	MDLAnsiString SecurityID;
	MDLAnsiString SecurityIDSource;
	MDLAnsiString TradingPhaseCode;
	MDLDoubleT<3> PreCloPrice;
	int64_t TurnNum;
	int64_t Volume;
	MDLDoubleT<3> Turnover;
	uint32_t StockNum;
};

struct Snapshot300611 {
	enum {
		ServiceID = MDLSID_MDL_SZL2,
		ServiceVer = MDLVID_MDL_SZL2,
		MessageID = MDLMID_MDL_SZL2_Snapshot300611
	};
	MDLTime UpdateTime;
	uint32_t ChannelNo;
	MDLAnsiString MDStreamID;
	MDLAnsiString SecurityID;
	MDLAnsiString SecurityIDSource;
	MDLAnsiString TradingPhaseCode;
	MDLDoubleT<3> PreCloPrice;
	int64_t TurnNum;
	int64_t Volume;
	MDLDoubleT<3> Turnover;
	struct BidPriceLevelItem {
		int64_t Volume;
		MDLDoubleT<3> Price;
	};
	MDLListT<BidPriceLevelItem> BidPriceLevel;
	struct AskPriceLevelItem {
		int64_t Volume;
		MDLDoubleT<3> Price;
	};
	MDLListT<AskPriceLevelItem> AskPriceLevel;
};

struct SecurityStatus {
	enum {
		ServiceID = MDLSID_MDL_SZL2,
		ServiceVer = MDLVID_MDL_SZL2,
		MessageID = MDLMID_MDL_SZL2_SecurityStatus
	};
	MDLTime UpdateTime;
	uint32_t ChannelNo;
	MDLAnsiString SecurityID;
	MDLAnsiString SecurityIDSource;
	MDLAnsiString FinancialStatus;
	struct SwitchListItem {
		uint32_t SecuritySwitchType;
		uint32_t SecuritySwitchStatus;
	};
	MDLListT<SwitchListItem> SwitchList;
};

struct Order300192 {
	enum {
		ServiceID = MDLSID_MDL_SZL2,
		ServiceVer = MDLVID_MDL_SZL2,
		MessageID = MDLMID_MDL_SZL2_Order300192
	};
	uint32_t ChannelNo;
	int64_t ApplSeqNum;
	MDLAnsiString MDStreamID;
	MDLAnsiString SecurityID;
	MDLAnsiString SecurityIDSource;
	MDLDoubleT<3> Price;
	int64_t OrderQty;
	int32_t Side;
	MDLTime TransactTime;
	int32_t OrdType;
};

struct Order300592 {
	enum {
		ServiceID = MDLSID_MDL_SZL2,
		ServiceVer = MDLVID_MDL_SZL2,
		MessageID = MDLMID_MDL_SZL2_Order300592
	};
	uint32_t ChannelNo;
	int64_t ApplSeqNum;
	MDLAnsiString MDStreamID;
	MDLAnsiString SecurityID;
	MDLAnsiString SecurityIDSource;
	MDLDoubleT<3> Price;
	int64_t OrderQty;
	int32_t Side;
	MDLTime TransactTime;
	MDLAnsiString ConfirmID;
	MDLAnsiString Contactor;
	MDLAnsiString ContactInfo;
};

struct Order300792 {
	enum {
		ServiceID = MDLSID_MDL_SZL2,
		ServiceVer = MDLVID_MDL_SZL2,
		MessageID = MDLMID_MDL_SZL2_Order300792
	};
	uint32_t ChannelNo;
	int64_t ApplSeqNum;
	MDLAnsiString MDStreamID;
	MDLAnsiString SecurityID;
	MDLAnsiString SecurityIDSource;
	MDLDoubleT<3> Price;
	int64_t OrderQty;
	int32_t Side;
	MDLTime TransactTime;
	uint32_t ExpirationDays;
	uint32_t ExpirationType;
};

struct Transaction300191 {
	enum {
		ServiceID = MDLSID_MDL_SZL2,
		ServiceVer = MDLVID_MDL_SZL2,
		MessageID = MDLMID_MDL_SZL2_Transaction300191
	};
	uint32_t ChannelNo;
	int64_t ApplSeqNum;
	MDLAnsiString MDStreamID;
	int64_t BidApplSeqNum;
	int64_t OfferApplSeqNum;
	MDLAnsiString SecurityID;
	MDLAnsiString SecurityIDSource;
	MDLDoubleT<3> LastPx;
	int64_t LastQty;
	int32_t ExecType;
	MDLTime TransactTime;
};

struct Transaction300591 {
	enum {
		ServiceID = MDLSID_MDL_SZL2,
		ServiceVer = MDLVID_MDL_SZL2,
		MessageID = MDLMID_MDL_SZL2_Transaction300591
	};
	uint32_t ChannelNo;
	int64_t ApplSeqNum;
	MDLAnsiString MDStreamID;
	int64_t BidApplSeqNum;
	int64_t OfferApplSeqNum;
	MDLAnsiString SecurityID;
	MDLAnsiString SecurityIDSource;
	MDLDoubleT<3> LastPx;
	int64_t LastQty;
	int32_t ExecType;
	MDLTime TransactTime;
};

struct Transaction300791 {
	enum {
		ServiceID = MDLSID_MDL_SZL2,
		ServiceVer = MDLVID_MDL_SZL2,
		MessageID = MDLMID_MDL_SZL2_Transaction300791
	};
	uint32_t ChannelNo;
	int64_t ApplSeqNum;
	MDLAnsiString MDStreamID;
	int64_t BidApplSeqNum;
	int64_t OfferApplSeqNum;
	MDLAnsiString SecurityID;
	MDLAnsiString SecurityIDSource;
	MDLDoubleT<3> LastPx;
	int64_t LastQty;
	int32_t ExecType;
	MDLTime TransactTime;
};

struct Bulletin {
	enum {
		ServiceID = MDLSID_MDL_SZL2,
		ServiceVer = MDLVID_MDL_SZL2,
		MessageID = MDLMID_MDL_SZL2_Bulletin
	};
	MDLTime OrigTime;
	uint32_t ChannelNo;
	MDLAnsiString NewsID;
	MDLUTF8String Headline;
	MDLAnsiString RawDataFormat;
	MDLUTF8String RawData;
};

struct Snapshot300111_v2 {
	enum {
		ServiceID = MDLSID_MDL_SZL2,
		ServiceVer = MDLVID_MDL_SZL2,
		MessageID = MDLMID_MDL_SZL2_Snapshot300111_v2
	};
	MDLTime UpdateTime;
	uint32_t ChannelNo;
	MDLAnsiString MDStreamID;
	MDLAnsiString SecurityID;
	MDLAnsiString SecurityIDSource;
	MDLAnsiString TradingPhaseCode;
	MDLDoubleT<4> PreCloPrice;
	int64_t TurnNum;
	int64_t Volume;
	MDLDoubleT<4> Turnover;
	MDLDoubleT<6> LastPrice;
	MDLDoubleT<6> OpenPrice;
	MDLDoubleT<6> HighPrice;
	MDLDoubleT<6> LowPrice;
	MDLDoubleT<6> DifPrice1;
	MDLDoubleT<6> DifPrice2;
	MDLDoubleT<6> PE1;
	MDLDoubleT<6> PE2;
	MDLDoubleT<6> PreCloseIOPV;
	MDLDoubleT<6> IOPV;
	int64_t TotalOfferQty;
	MDLDoubleT<6> WeightedAvgOfferPx;
	int64_t TotalBidQty;
	MDLDoubleT<6> WeightedAvgBidPx;
	MDLDoubleT<6> HighLimitPrice;
	MDLDoubleT<6> LowLimitPrice;
	int64_t OpenInt;
	MDLDoubleT<6> OptPremiumRatio;
	struct BidPriceLevelItem {
		int64_t Volume;
		MDLDoubleT<6> Price;
		uint32_t NumOrders;
		struct OrdersItem {
			int64_t OrderQty;
		};
		MDLListT<OrdersItem> Orders;
	};
	MDLListT<BidPriceLevelItem> BidPriceLevel;
	struct AskPriceLevelItem {
		int64_t Volume;
		MDLDoubleT<6> Price;
		uint32_t NumOrders;
		struct OrdersItem {
			int64_t OrderQty;
		};
		MDLListT<OrdersItem> Orders;
	};
	MDLListT<AskPriceLevelItem> AskPriceLevel;
};

struct Snapshot309011_v2 {
	enum {
		ServiceID = MDLSID_MDL_SZL2,
		ServiceVer = MDLVID_MDL_SZL2,
		MessageID = MDLMID_MDL_SZL2_Snapshot309011_v2
	};
	MDLTime UpdateTime;
	uint32_t ChannelNo;
	MDLAnsiString MDStreamID;
	MDLAnsiString SecurityID;
	MDLAnsiString SecurityIDSource;
	MDLAnsiString TradingPhaseCode;
	int64_t TurnNum;
	int64_t Volume;
	MDLDoubleT<4> Turnover;
	MDLDoubleT<6> LastIndex;
	MDLDoubleT<6> PreCloIndex;
	MDLDoubleT<6> OpenIndex;
	MDLDoubleT<6> HighIndex;
	MDLDoubleT<6> LowIndex;
};

struct Snapshot309111_v2 {
	enum {
		ServiceID = MDLSID_MDL_SZL2,
		ServiceVer = MDLVID_MDL_SZL2,
		MessageID = MDLMID_MDL_SZL2_Snapshot309111_v2
	};
	MDLTime UpdateTime;
	uint32_t ChannelNo;
	MDLAnsiString MDStreamID;
	MDLAnsiString SecurityID;
	MDLAnsiString SecurityIDSource;
	MDLAnsiString TradingPhaseCode;
	MDLDoubleT<4> PreCloPrice;
	int64_t TurnNum;
	int64_t Volume;
	MDLDoubleT<4> Turnover;
	uint32_t StockNum;
};

struct Snapshot300611_v2 {
	enum {
		ServiceID = MDLSID_MDL_SZL2,
		ServiceVer = MDLVID_MDL_SZL2,
		MessageID = MDLMID_MDL_SZL2_Snapshot300611_v2
	};
	MDLTime UpdateTime;
	uint32_t ChannelNo;
	MDLAnsiString MDStreamID;
	MDLAnsiString SecurityID;
	MDLAnsiString SecurityIDSource;
	MDLAnsiString TradingPhaseCode;
	MDLDoubleT<4> PreCloPrice;
	int64_t TurnNum;
	int64_t Volume;
	MDLDoubleT<4> Turnover;
	struct BidPriceLevelItem {
		int64_t Volume;
		MDLDoubleT<6> Price;
	};
	MDLListT<BidPriceLevelItem> BidPriceLevel;
	struct AskPriceLevelItem {
		int64_t Volume;
		MDLDoubleT<6> Price;
	};
	MDLListT<AskPriceLevelItem> AskPriceLevel;
};

struct Order300192_v2 {
	enum {
		ServiceID = MDLSID_MDL_SZL2,
		ServiceVer = MDLVID_MDL_SZL2,
		MessageID = MDLMID_MDL_SZL2_Order300192_v2
	};
	uint32_t ChannelNo;
	int64_t ApplSeqNum;
	MDLAnsiString MDStreamID;
	MDLAnsiString SecurityID;
	MDLAnsiString SecurityIDSource;
	MDLDoubleT<4> Price;
	int64_t OrderQty;
	int32_t Side;
	MDLTime TransactTime;
	int32_t OrdType;
};

struct Order300592_v2 {
	enum {
		ServiceID = MDLSID_MDL_SZL2,
		ServiceVer = MDLVID_MDL_SZL2,
		MessageID = MDLMID_MDL_SZL2_Order300592_v2
	};
	uint32_t ChannelNo;
	int64_t ApplSeqNum;
	MDLAnsiString MDStreamID;
	MDLAnsiString SecurityID;
	MDLAnsiString SecurityIDSource;
	MDLDoubleT<4> Price;
	int64_t OrderQty;
	int32_t Side;
	MDLTime TransactTime;
	MDLAnsiString ConfirmID;
	MDLAnsiString Contactor;
	MDLAnsiString ContactInfo;
};

struct Order300792_v2 {
	enum {
		ServiceID = MDLSID_MDL_SZL2,
		ServiceVer = MDLVID_MDL_SZL2,
		MessageID = MDLMID_MDL_SZL2_Order300792_v2
	};
	uint32_t ChannelNo;
	int64_t ApplSeqNum;
	MDLAnsiString MDStreamID;
	MDLAnsiString SecurityID;
	MDLAnsiString SecurityIDSource;
	MDLDoubleT<4> Price;
	int64_t OrderQty;
	int32_t Side;
	MDLTime TransactTime;
	uint32_t ExpirationDays;
	uint32_t ExpirationType;
};

struct Transaction300191_v2 {
	enum {
		ServiceID = MDLSID_MDL_SZL2,
		ServiceVer = MDLVID_MDL_SZL2,
		MessageID = MDLMID_MDL_SZL2_Transaction300191_v2
	};
	uint32_t ChannelNo;
	int64_t ApplSeqNum;
	MDLAnsiString MDStreamID;
	int64_t BidApplSeqNum;
	int64_t OfferApplSeqNum;
	MDLAnsiString SecurityID;
	MDLAnsiString SecurityIDSource;
	MDLDoubleT<4> LastPx;
	int64_t LastQty;
	int32_t ExecType;
	MDLTime TransactTime;
};

struct Transaction300591_v2 {
	enum {
		ServiceID = MDLSID_MDL_SZL2,
		ServiceVer = MDLVID_MDL_SZL2,
		MessageID = MDLMID_MDL_SZL2_Transaction300591_v2
	};
	uint32_t ChannelNo;
	int64_t ApplSeqNum;
	MDLAnsiString MDStreamID;
	int64_t BidApplSeqNum;
	int64_t OfferApplSeqNum;
	MDLAnsiString SecurityID;
	MDLAnsiString SecurityIDSource;
	MDLDoubleT<4> LastPx;
	int64_t LastQty;
	int32_t ExecType;
	MDLTime TransactTime;
};

struct Transaction300791_v2 {
	enum {
		ServiceID = MDLSID_MDL_SZL2,
		ServiceVer = MDLVID_MDL_SZL2,
		MessageID = MDLMID_MDL_SZL2_Transaction300791_v2
	};
	uint32_t ChannelNo;
	int64_t ApplSeqNum;
	MDLAnsiString MDStreamID;
	int64_t BidApplSeqNum;
	int64_t OfferApplSeqNum;
	MDLAnsiString SecurityID;
	MDLAnsiString SecurityIDSource;
	MDLDoubleT<4> LastPx;
	int64_t LastQty;
	int32_t ExecType;
	MDLTime TransactTime;
};

struct Snapshot300111_v3 {
	enum {
		ServiceID = MDLSID_MDL_SZL2,
		ServiceVer = MDLVID_MDL_SZL2,
		MessageID = MDLMID_MDL_SZL2_Snapshot300111_v3
	};
	MDLTime UpdateTime;
	uint32_t ChannelNo;
	MDLAnsiString MDStreamID;
	MDLAnsiString SecurityID;
	MDLAnsiString SecurityIDSource;
	MDLAnsiString TradingPhaseCode;
	MDLDoubleT<4> PreCloPrice;
	int64_t TurnNum;
	int64_t Volume;
	MDLDoubleT<4> Turnover;
	MDLDoubleT<6> LastPrice;
	MDLDoubleT<6> OpenPrice;
	MDLDoubleT<6> HighPrice;
	MDLDoubleT<6> LowPrice;
	MDLDoubleT<6> DifPrice1;
	MDLDoubleT<6> DifPrice2;
	MDLDoubleT<6> PE1;
	MDLDoubleT<6> PE2;
	MDLDoubleT<6> PreCloseIOPV;
	MDLDoubleT<6> IOPV;
	int64_t TotalOfferQty;
	MDLDoubleT<6> WeightedAvgOfferPx;
	int64_t TotalBidQty;
	MDLDoubleT<6> WeightedAvgBidPx;
	MDLDoubleT<6> HighLimitPrice;
	MDLDoubleT<6> LowLimitPrice;
	int64_t OpenInt;
	MDLDoubleT<6> OptPremiumRatio;
	MDLDoubleT<6> WeightedAvgPx;
	MDLDoubleT<6> WeightedAvgPxPreClose;
	MDLDoubleT<6> WeightedAvgPxChangeBP;
	struct BidPriceLevelItem {
		int64_t Volume;
		MDLDoubleT<6> Price;
		uint32_t NumOrders;
		struct OrdersItem {
			int64_t OrderQty;
		};
		MDLListT<OrdersItem> Orders;
	};
	MDLListT<BidPriceLevelItem> BidPriceLevel;
	struct AskPriceLevelItem {
		int64_t Volume;
		MDLDoubleT<6> Price;
		uint32_t NumOrders;
		struct OrdersItem {
			int64_t OrderQty;
		};
		MDLListT<OrdersItem> Orders;
	};
	MDLListT<AskPriceLevelItem> AskPriceLevel;
	struct ExtendFieldsItem {
		uint32_t Type;
		int64_t Value;
	};
	MDLListT<ExtendFieldsItem> ExtendFields;
};

struct RealQuota {
	enum {
		ServiceID = MDLSID_MDL_SZL2,
		ServiceVer = MDLVID_MDL_SZL2,
		MessageID = MDLMID_MDL_SZL2_RealQuota
	};
	MDLTime DataTime;
	MDLAnsiString Market;
	MDLAnsiString Direction;
	int64_t InitQuota;
	int64_t CurrentQuota;
	int64_t UsedQuota;
	MDLAnsiString Status;
};

struct Snapshot306311 {
	enum {
		ServiceID = MDLSID_MDL_SZL2,
		ServiceVer = MDLVID_MDL_SZL2,
		MessageID = MDLMID_MDL_SZL2_Snapshot306311
	};
	MDLTime UpdateTime;
	uint32_t ChannelNo;
	MDLAnsiString MDStreamID;
	MDLAnsiString SecurityID;
	MDLAnsiString SecurityIDSource;
	MDLAnsiString TradingPhaseCode;
	MDLDoubleT<4> PreCloPrice;
	int64_t TurnNum;
	int64_t Volume;
	MDLDoubleT<4> Turnover;
	MDLDoubleT<6> LastPrice;
	MDLDoubleT<6> HighPrice;
	MDLDoubleT<6> LowPrice;
	MDLDoubleT<6> HighLimitPrice;
	MDLDoubleT<6> LowLimitPrice;
	MDLDoubleT<6> NominalPrice;
	MDLDoubleT<6> ReferencePrice;
	struct BidPriceLevelItem {
		int64_t Volume;
		MDLDoubleT<6> Price;
	};
	MDLListT<BidPriceLevelItem> BidPriceLevel;
	struct AskPriceLevelItem {
		int64_t Volume;
		MDLDoubleT<6> Price;
	};
	MDLListT<AskPriceLevelItem> AskPriceLevel;
	MDLTime ComplexEventStartTime;
	MDLTime ComplexEventEndTime;
	MDLDoubleT<6> BidULimitPrice;
	MDLDoubleT<6> BidLLimitPrice;
	MDLDoubleT<6> AskULimitPrice;
	MDLDoubleT<6> AskLLimitPrice;
};

struct Snapshot300211 {
	enum {
		ServiceID = MDLSID_MDL_SZL2,
		ServiceVer = MDLVID_MDL_SZL2,
		MessageID = MDLMID_MDL_SZL2_Snapshot300211
	};
	MDLTime UpdateTime;
	uint32_t ChannelNo;
	MDLAnsiString MDStreamID;
	MDLAnsiString SecurityID;
	MDLAnsiString SecurityIDSource;
	MDLAnsiString TradingPhaseCode;
	MDLDoubleT<4> PreCloPrice;
	int64_t TurnNum;
	int64_t Volume;
	MDLDoubleT<4> Turnover;
	uint32_t TradeType;
	MDLDoubleT<4> LastPrice;
	MDLDoubleT<4> OpenPrice;
	MDLDoubleT<4> ClosePrice;
	MDLDoubleT<4> HighPrice;
	MDLDoubleT<4> LowPrice;
	MDLDoubleT<5> WAvgPx;
	MDLDoubleT<4> DifPrice1;
	MDLDoubleT<4> DifPrice2;
	int64_t TotalBidQty;
	MDLDoubleT<4> AltWAvgBidPx;
	int64_t TotalOfferQty;
	MDLDoubleT<4> AltWAvgOfferPx;
	MDLDoubleT<5> PreWAvgPx;
	int64_t WAvgPxChgBP;
	MDLDoubleT<4> AuctionPrice;
	int64_t AuctionVolume;
	MDLDoubleT<4> AuctionValue;
	struct BidLevelsItem {
		MDLDoubleT<4> Price;
		int64_t OrderQty;
		uint32_t NumOrders;
	};
	MDLListT<BidLevelsItem> BidLevels;
	struct OfferLevelsItem {
		MDLDoubleT<4> Price;
		int64_t OrderQty;
		uint32_t NumOrders;
	};
	MDLListT<OfferLevelsItem> OfferLevels;
	struct SubTrdPhaseCodeItem {
		MDLAnsiString SubTradPhase;
	};
	MDLListT<SubTrdPhaseCodeItem> SubTrdPhaseCode;
};

struct Order300292 {
	enum {
		ServiceID = MDLSID_MDL_SZL2,
		ServiceVer = MDLVID_MDL_SZL2,
		MessageID = MDLMID_MDL_SZL2_Order300292
	};
	uint32_t ChannelNo;
	int64_t ApplSeqNum;
	MDLAnsiString MDStreamID;
	MDLAnsiString SecurityID;
	MDLAnsiString SecurityIDSource;
	MDLDoubleT<4> Price;
	int64_t OrderQty;
	int32_t Side;
	MDLTime TransactTime;
	int32_t OrdType;
};

struct Transaction300291 {
	enum {
		ServiceID = MDLSID_MDL_SZL2,
		ServiceVer = MDLVID_MDL_SZL2,
		MessageID = MDLMID_MDL_SZL2_Transaction300291
	};
	uint32_t ChannelNo;
	int64_t ApplSeqNum;
	MDLAnsiString MDStreamID;
	int64_t BidApplSeqNum;
	int64_t OfferApplSeqNum;
	MDLAnsiString SecurityID;
	MDLAnsiString SecurityIDSource;
	MDLDoubleT<4> LastPx;
	int64_t LastQty;
	int32_t ExecType;
	MDLTime TransactTime;
};

struct Order300392 {
	enum {
		ServiceID = MDLSID_MDL_SZL2,
		ServiceVer = MDLVID_MDL_SZL2,
		MessageID = MDLMID_MDL_SZL2_Order300392
	};
	uint32_t ChannelNo;
	int64_t ApplSeqNum;
	MDLAnsiString MDStreamID;
	MDLAnsiString SecurityID;
	MDLAnsiString SecurityIDSource;
	MDLDoubleT<4> Price;
	int64_t OrderQty;
	int32_t Side;
	MDLTime TransactTime;
	MDLAnsiString QuoteID;
	MDLAnsiString MemberID;
	MDLAnsiString InvestorType;
	MDLAnsiString InvestorID;
	MDLUTF8String InvestorName;
	MDLAnsiString TraderCode;
	uint32_t SettlPeriod;
	uint32_t SettlType;
	MDLUTF8String Memo;
	MDLAnsiString SecondaryOrderID;
	uint32_t BidTransType;
	uint32_t BidExecInstType;
	MDLDoubleT<4> LowLimitPrice;
	MDLDoubleT<4> HighLimitPrice;
	int64_t MinQty;
	MDLDate TradeDate;
};

struct Transaction300391 {
	enum {
		ServiceID = MDLSID_MDL_SZL2,
		ServiceVer = MDLVID_MDL_SZL2,
		MessageID = MDLMID_MDL_SZL2_Transaction300391
	};
	uint32_t ChannelNo;
	int64_t ApplSeqNum;
	MDLAnsiString MDStreamID;
	int64_t BidApplSeqNum;
	int64_t OfferApplSeqNum;
	MDLAnsiString SecurityID;
	MDLAnsiString SecurityIDSource;
	MDLDoubleT<4> LastPx;
	int64_t LastQty;
	int32_t ExecType;
	MDLTime TransactTime;
	uint32_t SettlPeriod;
	uint32_t SettlType;
	MDLAnsiString SecondaryOrderID;
	uint32_t BidExecInstType;
	MDLDoubleT<4> MarginPrice;
};

struct BondOrderQueue {
	enum {
		ServiceID = MDLSID_MDL_SZL2,
		ServiceVer = MDLVID_MDL_SZL2,
		MessageID = MDLMID_MDL_SZL2_BondOrderQueue
	};
	MDLTime UpdateTime;
	MDLAnsiString SecurityID;
	MDLAnsiString Side;
	MDLDoubleT<4> Price;
	uint32_t NoOrders;
	struct OrdersItem {
		int64_t OrderQty;
	};
	MDLListT<OrdersItem> Orders;
};

struct BondDistribution {
	enum {
		ServiceID = MDLSID_MDL_SZL2,
		ServiceVer = MDLVID_MDL_SZL2,
		MessageID = MDLMID_MDL_SZL2_BondDistribution
	};
	MDLTime UpdateTime;
	uint32_t ChannelNo;
	MDLAnsiString MDStreamID;
	MDLAnsiString SecurityID;
	MDLAnsiString SecurityIDSource;
	MDLAnsiString TradingPhaseCode;
	MDLDoubleT<4> PreCloPrice;
	int64_t TurnNum;
	int64_t Volume;
	MDLDoubleT<4> Turnover;
	MDLDoubleT<4> LastPrice;
	MDLDoubleT<4> OpenPrice;
	MDLDoubleT<4> ClosePrice;
	MDLDoubleT<4> HighPrice;
	MDLDoubleT<4> LowPrice;
	MDLDoubleT<4> DifPrice1;
	MDLDoubleT<4> DifPrice2;
	int64_t TotalBidQty;
	MDLDoubleT<4> AltWAvgBidPx;
	int64_t TotalOfferQty;
	MDLDoubleT<4> AltWAvgOfferPx;
	struct BidLevelsItem {
		MDLDoubleT<4> Price;
		int64_t OrderQty;
		uint32_t NumOrders;
	};
	MDLListT<BidLevelsItem> BidLevels;
	struct OfferLevelsItem {
		MDLDoubleT<4> Price;
		int64_t OrderQty;
		uint32_t NumOrders;
	};
	MDLListT<OfferLevelsItem> OfferLevels;
};

struct Snapshot309211 {
	enum {
		ServiceID = MDLSID_MDL_SZL2,
		ServiceVer = MDLVID_MDL_SZL2,
		MessageID = MDLMID_MDL_SZL2_Snapshot309211
	};
	MDLTime UpdateTime;
	uint32_t ChannelNo;
	MDLAnsiString MDStreamID;
	MDLAnsiString SecurityID;
	MDLAnsiString SecurityIDSource;
	MDLAnsiString TradingPhaseCode;
	MDLDoubleT<6> IOPV;
};

struct ETFData {
	enum {
		ServiceID = MDLSID_MDL_SZL2,
		ServiceVer = MDLVID_MDL_SZL2,
		MessageID = MDLMID_MDL_SZL2_ETFData
	};
	MDLTime DataTime;
	MDLAnsiString SecurityID;
	uint32_t ETFBuyNumber;
	MDLDoubleT<2> ETFBuyAmount;
	uint32_t ETFSellNumber;
	MDLDoubleT<2> ETFSellAmount;
};

struct MarketInfo {
	enum {
		ServiceID = MDLSID_MDL_SZL2,
		ServiceVer = MDLVID_MDL_SZL2,
		MessageID = MDLMID_MDL_SZL2_MarketInfo
	};
	MDLAnsiString SecurityID;
	MDLUTF8String SecurityName;
	MDLUTF8String SymbolEx;
	MDLAnsiString EnglishName;
	MDLTime UpdateTime;
	MDLAnsiString UnderlyingSecurityID;
	MDLDate ListDate;
	uint32_t SecurityType;
	MDLAnsiString Currency;
	MDLDoubleT<2> QtyUnit;
	MDLAnsiString DayTrading;
	MDLDoubleT<4> PrevClosePx;
	struct SecurityStatusItem {
		uint32_t Status;
	};
	MDLListT<SecurityStatusItem> SecurityStatus;
	MDLDoubleT<2> OutstandingShare;
	MDLDoubleT<2> PublicFloatShareQuantity;
	MDLDoubleT<4> ParValue;
	MDLAnsiString GageFlag;
	MDLFloatT<2> GageRatio;
	MDLAnsiString CrdBuyUnderlying;
	MDLAnsiString CrdSellUnderlying;
	int32_t PriceCheckMode;
	MDLAnsiString PledgeFlag;
	MDLFloatT<4> ContractMultiplier;
	MDLAnsiString QualificationFlag;
	int32_t QualificationClass;
	MDLAnsiString RegularShare;
	MDLDoubleT<4> PreviousYearProfitPerShare;
	MDLDoubleT<4> CurrentYearProfitPerShare;
	int32_t Attribute;
	MDLAnsiString NoProfit;
	MDLAnsiString WeightedVotingRights;
	MDLAnsiString IsRegistration;
	MDLAnsiString IsVIE;
	MDLDoubleT<4> NAV;
	MDLFloatT<4> CouponRate;
	MDLDoubleT<4> IssuePrice;
	MDLDoubleT<8> Interest;
	MDLDate InterestAccrualDate;
	MDLDate MaturityDate;
	MDLAnsiString OfferingFlag;
	MDLAnsiString SwapFlag;
	MDLAnsiString PutbackFlag;
	MDLAnsiString PutbackCancelFlag;
	MDLAnsiString PutbackResellFlag;
	uint32_t PricingMethod;
	uint32_t ExpirationDays;
	MDLAnsiString CallOrPut;
	uint32_t ListType;
	MDLDate DeliveryDay;
	MDLAnsiString DeliveryMonth;
	MDLAnsiString DeliveryType;
	MDLDate ExerciseBeginDate;
	MDLDate ExerciseEndDate;
	MDLDoubleT<4> ExercisePrice;
	MDLAnsiString ExerciseType;
	MDLDate LastTradeDay;
	uint32_t AdjustTimes;
	MDLDoubleT<2> ContractUnit;
	MDLDoubleT<4> PrevClearingPrice;
	MDLDoubleT<4> ContractPosition;
	MDLAnsiString IndustryClassification;
};

struct DerivAuction {
	enum {
		ServiceID = MDLSID_MDL_SZL2,
		ServiceVer = MDLVID_MDL_SZL2,
		MessageID = MDLMID_MDL_SZL2_DerivAuction
	};
	MDLAnsiString SecurityID;
	MDLDoubleT<2> BuyQtyUL;
	MDLDoubleT<2> SellQtyUL;
	MDLDoubleT<2> MktOrderBuyQtyUL;
	MDLDoubleT<2> MktOrderSellQtyUL;
	MDLDoubleT<2> QuoteOrderBuyQtyUL;
	MDLDoubleT<2> QuoteOrderSellQtyUL;
	MDLDoubleT<2> BuyQtyUnit;
	MDLDoubleT<2> SellQtyUnit;
	MDLDoubleT<4> PriceTick;
	MDLDoubleT<4> PriceUL;
	MDLDoubleT<4> PriceLL;
	MDLDoubleT<4> LastSellMargin;
	MDLDoubleT<4> SellMargin;
	MDLFloatT<2> MarginRatioPar1;
	MDLFloatT<2> MarginRatioPar2;
	MDLAnsiString MktMakerFlag;
};

#pragma pack()

} // namespace mdl_szl2_msg
} // namespace mdl
} // namespace datayes

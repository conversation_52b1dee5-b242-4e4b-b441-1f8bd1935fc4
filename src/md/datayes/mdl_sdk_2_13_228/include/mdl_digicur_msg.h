// Generated by the code_gen tool.  DO NOT EDIT!
#pragma once

#include "mdl_api_types.h"

namespace datayes {
namespace mdl {
namespace mdl_digicur_msg {

static const uint16_t MDLVID_MDL_DIGICUR = 101;

enum MDL_DIGICURMessageID {
	MDLMID_MDL_DIGICUR_GeminiAuction = 1,
	MDLMID_MDL_DIGICUR_GeminiOrder = 2,
	MDLMID_MDL_DIGICUR_GeminiTick = 3,
	MDLMID_MDL_DIGICUR_CoinMarketCapTick = 4,
	MDLMID_MDL_DIGICUR_CoinMarketCapGlobal = 5,
	MDLMID_MDL_DIGICUR_KrakenOrder = 6,
	MDLMID_MDL_DIGICUR_KrakenTick = 7,
	MDLMID_MDL_DIGICUR_KrakenTrans = 8,
	MDLMID_MDL_DIGICUR_BitstampFullOrder = 9,
	MDLMID_MDL_DIGICUR_BitstampTick = 10,
	MDLMID_MDL_DIGICUR_BitstampTrans = 11,
	MD<PERSON><PERSON>_MDL_DIGICUR_GdaxLevel2Order = 12,
	MDLMID_MDL_DIGICUR_GdaxTick = 13,
	MDLMID_MDL_DIGICUR_GdaxTrans = 14,
	MDLMID_MDL_DIGICUR_ItbitOrder = 15,
	MDLMID_MDL_DIGICUR_ItbitTick = 16,
	MDLMID_MDL_DIGICUR_ItbitTrans = 17,
	MDLMID_MDL_DIGICUR_CryFacilitiesOrder = 18,
	MDLMID_MDL_DIGICUR_CryFacilitiesTick = 19
};

#pragma pack(1)

struct GeminiAuction {
	enum {
		ServiceID = MDLSID_MDL_DIGICUR,
		ServiceVer = MDLVID_MDL_DIGICUR,
		MessageID = MDLMID_MDL_DIGICUR_GeminiAuction
	};
	MDLAnsiString CurrencyPair;
	MDLAnsiString Type;
	int64_t Id;
	int64_t EId;
	MDLDate DataDate;
	MDLTime DataTime;
	MDLAnsiString Result;
	MDLDoubleT<6> AuctionPrice;
	MDLDoubleT<6> AuctionQuantity;
	MDLDoubleT<6> HighestBidPrice;
	MDLDoubleT<6> LowestAskPrice;
	MDLDoubleT<6> CollarPrice;
};

struct GeminiOrder {
	enum {
		ServiceID = MDLSID_MDL_DIGICUR,
		ServiceVer = MDLVID_MDL_DIGICUR,
		MessageID = MDLMID_MDL_DIGICUR_GeminiOrder
	};
	MDLAnsiString CurrencyPair;
	MDLDate DataDate;
	MDLTime DataTime;
	struct BidBookItem {
		MDLDoubleT<3> Volume;
		MDLDoubleT<6> Price;
	};
	MDLListT<BidBookItem> BidBook;
	struct AskBookItem {
		MDLDoubleT<3> Volume;
		MDLDoubleT<6> Price;
	};
	MDLListT<AskBookItem> AskBook;
};

struct GeminiTick {
	enum {
		ServiceID = MDLSID_MDL_DIGICUR,
		ServiceVer = MDLVID_MDL_DIGICUR,
		MessageID = MDLMID_MDL_DIGICUR_GeminiTick
	};
	MDLAnsiString CurrencyPair;
	MDLDate DataDate;
	MDLTime DataTime;
	int64_t EventId;
	MDLDoubleT<6> LastPrice;
	MDLDoubleT<6> OpenPrice;
	MDLDoubleT<6> HighPrice;
	MDLDoubleT<6> LowPrice;
	MDLDoubleT<6> TradeVolume;
	MDLDoubleT<6> Price;
	MDLDoubleT<6> Volume;
	MDLAnsiString Side;
};

struct CoinMarketCapTick {
	enum {
		ServiceID = MDLSID_MDL_DIGICUR,
		ServiceVer = MDLVID_MDL_DIGICUR,
		MessageID = MDLMID_MDL_DIGICUR_CoinMarketCapTick
	};
	MDLAnsiString CoinName;
	MDLAnsiString Ticker;
	MDLDate DataDate;
	MDLTime DataTime;
	int32_t Rank;
	MDLDoubleT<4> PriceUSD;
	MDLDoubleT<6> PriceBTC;
	MDLDoubleT<2> Volume24hUSD;
	MDLDoubleT<2> MarketCapUSD;
	MDLDoubleT<2> AvailSupply;
	MDLDoubleT<2> TotalSupply;
	MDLDoubleT<2> MaxSupply;
	MDLFloatT<4> Pct1h;
	MDLFloatT<4> Pct24h;
	MDLFloatT<4> Pct7d;
};

struct CoinMarketCapGlobal {
	enum {
		ServiceID = MDLSID_MDL_DIGICUR,
		ServiceVer = MDLVID_MDL_DIGICUR,
		MessageID = MDLMID_MDL_DIGICUR_CoinMarketCapGlobal
	};
	MDLDate DataDate;
	MDLTime DataTime;
	MDLDoubleT<2> TotalMarketCapUSD;
	MDLDoubleT<2> TotalVolume24hUSD;
	MDLFloatT<4> BtcPctMarketCap;
	int32_t ActiveCurrencies;
	int32_t ActiveAssets;
	int32_t ActiveMarkets;
};

struct KrakenOrder {
	enum {
		ServiceID = MDLSID_MDL_DIGICUR,
		ServiceVer = MDLVID_MDL_DIGICUR,
		MessageID = MDLMID_MDL_DIGICUR_KrakenOrder
	};
	MDLAnsiString CurrencyPair;
	MDLDate DataDate;
	MDLTime DataTime;
	struct BidBookItem {
		MDLDoubleT<3> Volume;
		MDLDoubleT<6> Price;
	};
	MDLListT<BidBookItem> BidBook;
	struct AskBookItem {
		MDLDoubleT<3> Volume;
		MDLDoubleT<6> Price;
	};
	MDLListT<AskBookItem> AskBook;
};

struct KrakenTick {
	enum {
		ServiceID = MDLSID_MDL_DIGICUR,
		ServiceVer = MDLVID_MDL_DIGICUR,
		MessageID = MDLMID_MDL_DIGICUR_KrakenTick
	};
	MDLAnsiString CurrencyPair;
	MDLDate DataDate;
	MDLTime DataTime;
	MDLDoubleT<6> OpenPrice;
	MDLDoubleT<6> HighPrice;
	MDLDoubleT<6> High24h;
	MDLDoubleT<6> LowPrice;
	MDLDoubleT<6> Low24h;
	MDLDoubleT<6> LastPrice;
	MDLDoubleT<6> LastVolume;
	MDLDoubleT<6> VWAP;
	MDLDoubleT<6> VWAP24h;
	MDLDoubleT<6> Volume;
	MDLDoubleT<6> Volume24h;
	int64_t Deal;
	int64_t Deal24h;
	MDLDoubleT<6> B1;
	MDLDoubleT<6> BV1;
	MDLDoubleT<6> S1;
	MDLDoubleT<6> SV1;
};

struct KrakenTrans {
	enum {
		ServiceID = MDLSID_MDL_DIGICUR,
		ServiceVer = MDLVID_MDL_DIGICUR,
		MessageID = MDLMID_MDL_DIGICUR_KrakenTrans
	};
	MDLAnsiString CurrencyPair;
	MDLDate DataDate;
	MDLTime DataTime;
	MDLDoubleT<6> Price;
	MDLDoubleT<6> Volume;
	MDLAnsiString Side;
	MDLAnsiString PriceType;
};

struct BitstampFullOrder {
	enum {
		ServiceID = MDLSID_MDL_DIGICUR,
		ServiceVer = MDLVID_MDL_DIGICUR,
		MessageID = MDLMID_MDL_DIGICUR_BitstampFullOrder
	};
	MDLAnsiString CurrencyPair;
	MDLDate DataDate;
	MDLTime DataTime;
	struct BidBookItem {
		MDLDoubleT<3> Volume;
		MDLDoubleT<6> Price;
	};
	MDLListT<BidBookItem> BidBook;
	struct AskBookItem {
		MDLDoubleT<3> Volume;
		MDLDoubleT<6> Price;
	};
	MDLListT<AskBookItem> AskBook;
};

struct BitstampTick {
	enum {
		ServiceID = MDLSID_MDL_DIGICUR,
		ServiceVer = MDLVID_MDL_DIGICUR,
		MessageID = MDLMID_MDL_DIGICUR_BitstampTick
	};
	MDLAnsiString CurrencyPair;
	MDLDate DataDate;
	MDLTime DataTime;
	int64_t TradeId;
	MDLDoubleT<6> OpenPrice;
	MDLDoubleT<6> HighPrice;
	MDLDoubleT<6> LowPrice;
	MDLDoubleT<6> LastPrice;
	MDLAnsiString Side;
	MDLDoubleT<6> Volume;
	int64_t BidOrderId;
	int64_t AskOrderId;
};

struct BitstampTrans {
	enum {
		ServiceID = MDLSID_MDL_DIGICUR,
		ServiceVer = MDLVID_MDL_DIGICUR,
		MessageID = MDLMID_MDL_DIGICUR_BitstampTrans
	};
	MDLAnsiString CurrencyPair;
	MDLDate DataDate;
	MDLTime DataTime;
	int64_t Id;
	MDLDoubleT<6> Price;
	MDLDoubleT<6> Volume;
	MDLAnsiString Side;
};

struct GdaxLevel2Order {
	enum {
		ServiceID = MDLSID_MDL_DIGICUR,
		ServiceVer = MDLVID_MDL_DIGICUR,
		MessageID = MDLMID_MDL_DIGICUR_GdaxLevel2Order
	};
	MDLAnsiString CurrencyPair;
	MDLDate DataDate;
	MDLTime DataTime;
	struct BidBookItem {
		MDLDoubleT<3> Volume;
		MDLDoubleT<6> Price;
	};
	MDLListT<BidBookItem> BidBook;
	struct AskBookItem {
		MDLDoubleT<3> Volume;
		MDLDoubleT<6> Price;
	};
	MDLListT<AskBookItem> AskBook;
};

struct GdaxTick {
	enum {
		ServiceID = MDLSID_MDL_DIGICUR,
		ServiceVer = MDLVID_MDL_DIGICUR,
		MessageID = MDLMID_MDL_DIGICUR_GdaxTick
	};
	MDLAnsiString CurrencyPair;
	MDLDate DataDate;
	MDLTime DataTime;
	MDLDoubleT<6> LastPrice;
	MDLDoubleT<6> OpenPrice;
	MDLDoubleT<6> HighPrice;
	MDLDoubleT<6> LowPrice;
	MDLDoubleT<6> LastVolume;
	MDLDoubleT<6> Volume24h;
	MDLDoubleT<6> Volume30d;
	MDLDoubleT<6> BestBid;
	MDLDoubleT<6> BestAsk;
	MDLAnsiString Side;
};

struct GdaxTrans {
	enum {
		ServiceID = MDLSID_MDL_DIGICUR,
		ServiceVer = MDLVID_MDL_DIGICUR,
		MessageID = MDLMID_MDL_DIGICUR_GdaxTrans
	};
	MDLAnsiString CurrencyPair;
	MDLDate DataDate;
	MDLTime DataTime;
	MDLDoubleT<6> Price;
	MDLDoubleT<6> Volume;
	MDLAnsiString Side;
};

struct ItbitOrder {
	enum {
		ServiceID = MDLSID_MDL_DIGICUR,
		ServiceVer = MDLVID_MDL_DIGICUR,
		MessageID = MDLMID_MDL_DIGICUR_ItbitOrder
	};
	MDLAnsiString CurrencyPair;
	MDLDate DataDate;
	MDLTime DataTime;
	struct BidBookItem {
		MDLDoubleT<3> Volume;
		MDLDoubleT<6> Price;
	};
	MDLListT<BidBookItem> BidBook;
	struct AskBookItem {
		MDLDoubleT<3> Volume;
		MDLDoubleT<6> Price;
	};
	MDLListT<AskBookItem> AskBook;
};

struct ItbitTick {
	enum {
		ServiceID = MDLSID_MDL_DIGICUR,
		ServiceVer = MDLVID_MDL_DIGICUR,
		MessageID = MDLMID_MDL_DIGICUR_ItbitTick
	};
	MDLAnsiString CurrencyPair;
	MDLDate DataDate;
	MDLTime DataTime;
	MDLDoubleT<6> OpenPrice;
	MDLDoubleT<6> HighPrice;
	MDLDoubleT<6> HighPrice24h;
	MDLDoubleT<6> LowPrice;
	MDLDoubleT<6> LowPrice24h;
	MDLDoubleT<6> LastPrice;
	MDLDoubleT<6> LastVolume;
	MDLDoubleT<6> Volume;
	MDLDoubleT<6> Volume24h;
	MDLDoubleT<6> VWAP;
	MDLDoubleT<6> VWAP24h;
	MDLDoubleT<6> B1;
	MDLDoubleT<6> BV1;
	MDLDoubleT<6> S1;
	MDLDoubleT<6> SV1;
};

struct ItbitTrans {
	enum {
		ServiceID = MDLSID_MDL_DIGICUR,
		ServiceVer = MDLVID_MDL_DIGICUR,
		MessageID = MDLMID_MDL_DIGICUR_ItbitTrans
	};
	MDLAnsiString CurrencyPair;
	MDLDate DataDate;
	MDLTime DataTime;
	int64_t TradeId;
	MDLDoubleT<6> Price;
	MDLDoubleT<6> Volume;
};

struct CryFacilitiesOrder {
	enum {
		ServiceID = MDLSID_MDL_DIGICUR,
		ServiceVer = MDLVID_MDL_DIGICUR,
		MessageID = MDLMID_MDL_DIGICUR_CryFacilitiesOrder
	};
	MDLAnsiString Ticker;
	MDLDate DataDate;
	MDLTime DataTime;
	struct BidBookItem {
		MDLDoubleT<3> Volume;
		MDLDoubleT<6> Price;
	};
	MDLListT<BidBookItem> BidBook;
	struct AskBookItem {
		MDLDoubleT<3> Volume;
		MDLDoubleT<6> Price;
	};
	MDLListT<AskBookItem> AskBook;
};

struct CryFacilitiesTick {
	enum {
		ServiceID = MDLSID_MDL_DIGICUR,
		ServiceVer = MDLVID_MDL_DIGICUR,
		MessageID = MDLMID_MDL_DIGICUR_CryFacilitiesTick
	};
	MDLAnsiString Ticker;
	MDLDate DataDate;
	MDLTime DataTime;
	MDLDoubleT<6> OpenPrice;
	MDLDoubleT<6> HighPrice;
	MDLDoubleT<6> LowPrice;
	MDLDoubleT<6> LastPrice;
	MDLDoubleT<6> B1;
	MDLDoubleT<6> BV1;
	MDLDoubleT<6> S1;
	MDLDoubleT<6> SV1;
	MDLDoubleT<6> LastVolume;
	MDLDoubleT<6> Volume;
	MDLDoubleT<6> DTM;
	int32_t Leverage;
	MDLFloatT<4> Premium;
	MDLFloatT<4> Change;
};

#pragma pack()

} // namespace mdl_digicur_msg
} // namespace mdl
} // namespace datayes

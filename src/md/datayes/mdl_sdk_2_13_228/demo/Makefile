DEMOS:=$(basename $(wildcard *.cpp))
OBJS:=$(subst .cpp,.o,$(wildcard *.cpp))
LIBDIR:=-L../libs/linux
SODIR:=-Wl,-rpath=./ 
CPPFLAGS:=-g -O2 -std=c++11 -Wall
INCS:=-I../include -I../3rd -I../../fmt-11.2.0/include
LIBS:=-lmdl_api -ldl -lpthread -lrt -ljson
OUTDIR:=../bin

all: $(DEMOS)

%.o: %.cpp
	g++ $(INCS) $(CPPFLAGS) -c "$<" -o "$(OUTDIR)/$@"

$(DEMOS): % : %.o
	g++ $(SODIR) $(OUTDIR)/$@.o -o "$(OUTDIR)/$@" $(LIBDIR) $(LIBS)
	ln -sf ../libs/linux/libmdl_api.so $(OUTDIR)/libmdl_api.so

.PHONY: clean
clean:
	-cd $(OUTDIR) && rm -f $(OBJS) $(DEMOS)


#include <stdio.h>
#include "mdl_api.h"
#include "mdl_shl1_msg.h"
#include "mdl_shl2_msg.h"
#include "mdl_szl2_msg.h"
#include "mdl_szl1_msg.h"
#include <set>
#include <iostream>
#include <thread>
#include <chrono>
using namespace datayes::mdl;

class MyMessageHandler : public MessageHandler {
 public:
     template <class T>
    std::string DoubleToString(const T& mdlDouble) {
        if (mdlDouble.IsNull()) {
            return std::string("null");
        }
        char strBuf[100];
#if defined(__linux__)
        sprintf(strBuf, "%.2f", mdlDouble.GetDouble());
#else
        sprintf_s(strBuf, sizeof(strBuf), "%.2f", mdlDouble.GetDouble());
#endif
        return std::string(strBuf);
    }

    template <class T>
    std::string FloatToString(const T& mdlFloat) {
        if (mdlFloat.IsNull()) {
            return std::string("null");
        }
        char strBuf[100];
#if defined(__linux__)
        sprintf(strBuf, "%.2f", mdlFloat.GetFloat());
#else
        sprintf_s(strBuf, sizeof(strBuf), "%.2f", mdlFloat.GetFloat());
#endif
        return std::string(strBuf);
    }
    void OnMDLSysMessage(const MDLMessage* msg) {
        MDLMessageHead* head = msg->GetHead();
        if (head->MessageID == mdl_sys_msg::LogonResponse::MessageID) {
            mdl_sys_msg::LogonResponse* resp = (mdl_sys_msg::LogonResponse*)msg->GetBody();
            printf("LogonResponse: ReturnCode(%d)", resp->ReturnCode);
            for (uint32_t i = 0; i < resp->Services.Length; ++i) {
                for (uint32_t j = 0; j < resp->Services[i]->Messages.Length; ++j) {
                    printf(" %d.%d.%d(subscribe %s)", 
                        resp->Services[i]->ServiceID, resp->Services[i]->ServiceVersion, resp->Services[i]->Messages[j]->MessageID,
                        resp->Services[i]->Messages[j]->MessageStatus == MDLEC_OK ? "ok" : "fail");
                }
            }
            printf("\n");
        }
        else if (head->MessageID == mdl_sys_msg::ServiceStatus::MessageID) {
            mdl_sys_msg::ServiceStatus* resp = (mdl_sys_msg::ServiceStatus*)msg->GetBody();
            printf("status: version[%d]client[%d]\n"
                "    start time[%d-%d]send[%.2fkb/s]\n"
                "    memory rss[%.2fmb]peak[%.2fmb]delay[%.2fkb]\n", 
                resp->Version,
                resp->ClientCount, resp->StartDate.m_Value, resp->StartTime.m_Value,
                (double)resp->SendRate / (double)1024,
                (double)resp->MemoryRSS / (double)(1024 * 1024), 
                (double)resp->MemoryPeak / (double)(1024 * 1024),
                (double)resp->BytesDelayed / (double)(1024));
            for (uint32_t i = 0; i < resp->Services.Length; ++i) {
                printf("    service[%d]recv[%.2fkb/s]idle[%d]\n",
                    resp->Services[i]->ServiceID, 
                    (double)resp->Services[i]->ReceiveRate / (double)1024, 
                    resp->Services[i]->IdleTime);
            }
        }
        else if (head->MessageID == mdl_sys_msg::SessionStatus::MessageID) {
            mdl_sys_msg::SessionStatus* resp = (mdl_sys_msg::SessionStatus*)msg->GetBody();
            printf("SessionStatus: client[%d]\n", resp->Clients.Length);
            for (uint32_t i = 0; i < resp->Clients.Length; ++i) {
                printf("    [%s]ver[%d]codec[%d,%d]sub[%s]delay[%.2fkb]\n",
                    resp->Clients[i]->Address.c_str(), 
                    resp->Clients[i]->Version,
                    resp->Clients[i]->EncodeType,
                    resp->Clients[i]->DecodeType,
                    resp->Clients[i]->SubscriptionList.c_str(),
                    (double)resp->Clients[i]->BytesDelayed / (double)(1024));
            }
        }
        else if (head->MessageID == mdl_sys_msg::FeederStatus::MessageID) {
            mdl_sys_msg::FeederStatus* resp = (mdl_sys_msg::FeederStatus*)msg->GetBody();
            printf("[%d]%04d-%02d-%02d, fc %d, fs %lld, df %lld, ec %d, wc %d, us %d, mc %d [%s]\n",
                resp->ServiceNum,
                resp->StartDate.GetYear(),resp->StartDate.GetMonth(),resp->StartDate.GetDay(),
                resp->FileCount, resp->FileSize, resp->DiskFree, resp->ErrorCount, resp->WarnCount, resp->UpStreamStatus, resp->Messages.Length,
                resp->Notes.c_str());
            size_t sum = 0;
            for (size_t i = 0; i < resp->Messages.Length; ++i) {
                printf("\tmsg %d, c %d, %d:%d:%d\n", 
                    resp->Messages[i]->MessageID, 
                    resp->Messages[i]->MessageCount,
                    resp->Messages[i]->LastTime.GetHour(),
                    resp->Messages[i]->LastTime.GetMinute(),
                    resp->Messages[i]->LastTime.GetSecond());
            }
        }
        else if (head->MessageID == mdl_sys_msg::SubscribeResponse::MessageID) {
            const mdl_sys_msg::SubscribeResponse* reply = (const mdl_sys_msg::SubscribeResponse*)msg->GetBody();
            printf("SubscribeResponse: ");
            for (uint32_t i = 0; i < reply->Services.Length; ++i) {
                for (uint32_t j = 0; j < reply->Services[i]->Messages.Length; ++j) {
                    printf(" %d.%d.%d(subscribe %s)", 
                        reply->Services[i]->ServiceID, reply->Services[i]->ServiceVersion, reply->Services[i]->Messages[j]->MessageID,
                        reply->Services[i]->Messages[j]->MessageStatus == MDLEC_OK ? "ok" : "fail");
                }
            }
            printf("\n");
        }
    }

    // print shanghai level1 message
    virtual void OnMDLSHL1Message(const MDLMessage* msg) {
        MDLMessageHead* head = msg->GetHead();
        if (head->MessageID == mdl_shl1_msg::Equity2::MessageID) {
            mdl_shl1_msg::Equity2* body = (mdl_shl1_msg::Equity2*)msg->GetBody(); 
            PrintNewStockMessage(body);
        }
        else if (head->MessageID == mdl_shl1_msg::Indexes::MessageID) {
            mdl_shl1_msg::Indexes* body = (mdl_shl1_msg::Indexes*)msg->GetBody(); 
            PrintIndexMessage(body); 
        }
        else if (head->MessageID == mdl_shl1_msg::Bond2::MessageID) {
            mdl_shl1_msg::Bond2* body = (mdl_shl1_msg::Bond2*)msg->GetBody(); 
            PrintNewStockMessage(body);
        }
        else if (head->MessageID == mdl_shl1_msg::Fund2::MessageID) {
            mdl_shl1_msg::Fund2* body = (mdl_shl1_msg::Fund2*)msg->GetBody(); 
            PrintNewStockMessage(body);
        }
    }

    // print shenzhen level1 message
    virtual void OnMDLSZL1Message(const MDLMessage* msg) {
        MDLMessageHead* head = msg->GetHead();
        if (head->MessageID == mdl_szl1_msg::SZL1Index::MessageID) {
            mdl_szl1_msg::SZL1Index* body = (mdl_szl1_msg::SZL1Index*)msg->GetBody();
            PrintIndexMessage(body); 
        }
        else if (head->MessageID == mdl_szl1_msg::SZL1Stock::MessageID) {
            mdl_szl1_msg::SZL1Stock* body = (mdl_szl1_msg::SZL1Stock*)msg->GetBody();
            PrintStockMessage(body); 
        }
    }

    template <class T> 
    void PrintIndexMessage(const T* msgBody) {
        printf("%d:%02d:%02d %s %s HighIndex:%s LowIndex:%s LastIndex:%s\n",
            msgBody->UpdateTime.GetHour(), msgBody->UpdateTime.GetMinute(), msgBody->UpdateTime.GetSecond(),
            msgBody->IndexID.std_str().c_str(), msgBody->IndexName.std_str().c_str(), 
            DoubleToString(msgBody->HighIndex).c_str(), 
            DoubleToString(msgBody->LowIndex).c_str(), 
            DoubleToString(msgBody->LastIndex).c_str());
    }
    template <class T> 
    void PrintNewStockMessage(const T* msgBody) {
        printf("%d:%02d:%02d %s %s HighPrice:%s LowPrice:%s LastPrice:%s\n",
            msgBody->UpdateTime.GetHour(), msgBody->UpdateTime.GetMinute(), msgBody->UpdateTime.GetSecond(),
            msgBody->SecurityID.std_str().c_str(), msgBody->SecurityName.std_str().c_str(), 
            DoubleToString(msgBody->HighPrice).c_str(), 
            DoubleToString(msgBody->LowPrice).c_str(), 
            DoubleToString(msgBody->LastPrice).c_str());
    }
    template <class T> 
    void PrintStockMessage(const T* msgBody) {
        printf("%d:%02d:%02d %s %s HighPrice:%s LowPrice:%s LastPrice:%s\n",
            msgBody->UpdateTime.GetHour(), msgBody->UpdateTime.GetMinute(), msgBody->UpdateTime.GetSecond(),
            msgBody->SecurityID.std_str().c_str(), msgBody->SecurityName.std_str().c_str(), 
            FloatToString(msgBody->HighPrice).c_str(), 
            FloatToString(msgBody->LowPrice).c_str(), 
            FloatToString(msgBody->LastPrice).c_str());
    }
    
 private:
};

///////////////////////////////////////////////////////////////////////////////////////////////////////////

int main(int argc, char* argv[]) { 
    if (argc != 2) {
        printf("usage: mdl_api_demo server_hostname port\n");
        return 1;
    }

    IOManagerPtr m_IOManager = CreateIOManager(1);
    if (m_IOManager.IsNull()) {
        printf("Incompatible API lib version.\n");
        return false;
    }

    MyMessageHandler handler;
    SubscriberPtr sub = m_IOManager->CreateSubscriber(&handler);

    const char*  securityIDs[]  = {"600000", "600010", "600020"};
    sub->SubcribeMessageByFieldValues<mdl_shl1_msg::Equity2>("SecurityID", securityIDs, 3);
    sub->SubcribeMessage<mdl_shl1_msg::Indexes>();
    sub->SubcribeMessage<mdl_shl1_msg::Bond2>();
    sub->SubcribeMessage<mdl_shl1_msg::Fund2>();
    sub->SetServerAddress(argv[1]);
    sub->SetMessageEncoding(MDLEID_MKTPRO);
    std::string err = sub->Connect();
    if (!err.empty()) {
        printf("Connect to server failed: %s.\n", err.c_str());
        m_IOManager->Shutdown();
        m_IOManager.Reset();
        return -1;
    }

    printf("Connect to server successfully, subscripitions:%s\n", sub->GetSubscription());

    std::this_thread::sleep_for(std::chrono::seconds(10));

    const char*  delIDs[]  = {"600010", "600020"};
    sub->UnSubcribeMessageByFieldValues<mdl_shl1_msg::Equity2>("SecurityID", delIDs, 2);
    sub->UnSubcribeMessage<mdl_shl1_msg::Indexes>();
    sub->UnSubcribeMessage<mdl_shl1_msg::Bond2>();
    sub->UnSubcribeMessage<mdl_shl1_msg::Fund2>();
    sub->ReSubscribe();
    printf("UnsubcribeMesage shl1_Equity(600010, 600020), subscripitions:%s\n", sub->GetSubscription());
    std::this_thread::sleep_for(std::chrono::seconds(10));

    sub->ClearSubscriptions();
    sub->ReSubscribe();
    printf("Delete all subscripitions, subscripitions:%s\n", sub->GetSubscription());
    std::this_thread::sleep_for(std::chrono::seconds(10));

    const char*  securityIDs1[]  = {"600010"};
    sub->SubcribeMessageByFieldValues<mdl_shl1_msg::Equity2>("SecurityID", securityIDs1, 1);
    sub->SubcribeMessage<mdl_shl1_msg::Fund2>();
    sub->ReSubscribe();
    printf("resubscibe message, shl1_equity(600010), shl1_index, subscripitions:%s\n", sub->GetSubscription());

    printf("Press enter to exit.\n");
    getchar();
    m_IOManager->Shutdown();
    m_IOManager.Reset();

    return 0;
}


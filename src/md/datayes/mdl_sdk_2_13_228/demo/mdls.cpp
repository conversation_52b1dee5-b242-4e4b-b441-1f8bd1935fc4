/*
 * 保存到文件中
 */

#include <sys/types.h>
#include <sys/stat.h>
#include <fcntl.h>
#include <stdio.h>
#include <string.h>
#include <pthread.h>
#include <fstream>
#include <string>
#include <time.h>
#include "mdl_api.h"
#include "mdl_shl1_msg.h"
#include "mdl_shl2_msg.h"
#include "mdl_szl2_msg.h"
#include "mdl_szl1_msg.h"
#include "mdl_cffex_msg.h"
#include "mdl_czce_msg.h"
#include "mdl_dce_msg.h"
#include "mdl_shfe_msg.h"
#include "mdl_hkex_msg.h"
#include "mdl_bar_msg.h"
#ifdef __linux__
#include <unistd.h>
#include <sys/time.h>
#include <errno.h>
#include <sys/timeb.h>
#endif
#include <getopt.h>

#define FMT_HEADER_ONLY
#include "fmt/base.h"
#include "fmt/chrono.h"

using namespace datayes::mdl;

class MyMessageHandler : public MessageHandler {
private:
    std::ofstream m_file_szl2_order;        // 深圳L2逐笔委托文件
    std::ofstream m_file_szl2_trans;        // 深圳L2逐笔成交文件
    std::ofstream m_file_shl2_trans_order;  // 上海L2竞价逐笔合并行情文件

public:
	bool Open(const char* address, const char * token, const char * filep) {
		// 初始化互斥锁
		pthread_mutex_init(&m_lock_szl2_order, NULL);
		pthread_mutex_init(&m_lock_szl2_trans, NULL);
		pthread_mutex_init(&m_lock_shl2_trans_order, NULL);
		
		// 打开文件用于保存行情数据
		std::string filename;
		
		// 打开文件用于保存深圳L2逐笔委托(6.33)
		filename = std::string(filep) + "sz_order.csv";
		// m_file_szl2_order = fmt::output_file(filename);
		m_file_szl2_order.open(filename, std::ios::out | std::ios::trunc);

		// 打开文件用于保存深圳L2逐笔成交(6.36)
		filename = std::string(filep) + "sz_trans.csv";
		m_file_szl2_trans.open(filename, std::ios::out | std::ios::trunc);
		
		// 打开文件用于保存上海L2竞价逐笔合并行情(4.24)
		filename = std::string(filep) + "sh_transorder.csv";
		m_file_shl2_trans_order.open(filename, std::ios::out | std::ios::trunc);
		
		// 写入CSV文件头
		m_file_szl2_order << "RecvTime,ChannelNo,ApplSeqNum,StreamID,SecurityID,SecurityIDSource,Price,OrderQty,Side,TransactTime,OrdType\n";
		m_file_szl2_trans << "RecvTime,ChannelNo,ApplSeqNum,StreamID,BidApplSeqNum,OfferApplSeqNum,SecurityID,SecurityIDSource,LastPx,LastQty,ExecType,TransactTime\n";
		m_file_shl2_trans_order << "RecvTime,BizIndex,Channel,SecurityID,TickTime,Type,BuyOrderNO,SellOrderNO,Price,Qty,TradeMoney,TickBSFlag\n";
		
		int num_work_threads = 4;
		m_IOManager = CreateIOManager(num_work_threads);
		if (m_IOManager.IsNull()) {
			m_file_szl2_order.close();
			m_file_szl2_trans.close();
			m_file_shl2_trans_order.close();
			printf("Incompatible API lib version.\n");
			return false;
		}
		
		// auto-subscriber queries server to get latest config, then subscribes messages from servers in the config
		// config will be saved to autoconf.json, the file will be used as backup if query failed on next startup

		bool multi_thread_callback = true; // set to true if num_work_threads > 1 and OnXXXMessage() is thread-safe
		m_autosub = m_IOManager->CreateAutoSubscriber(this, multi_thread_callback);
		m_autosub->SetURL(address);
		m_autosub->SetToken(token);

		// Options:
		//   Network: string, network type ("internet" or "d-line")
		//   ExMsgs: string, messages NOT to subscribe, comma delimited
		//   ExSvrs: string, servers NOT to connect, comma delimited
		//   Role: string, type of data ("primary" or "secondary")
		// m_autosub->SetOptionsJson("{\"ExMsgs\": \"3.8,3.9,3.10,5.2\",\"ExSvrs\":\"mdl-cloud-sz.datayes.com\"}");
		//m_autosub->SetOptionsJson("{\"ExMsgs\": \"3.3,3.8,3.9,3.10,4.16,4.17,4.23,4.20,4.21,4.22,4.9,4.29,5.11,5.2,5.6,5.7,5.8,6.30,6.31,6.51,6.42,6.43,6.44,6.45,6.46,6.47,5.5,5.10,3.13,7.1,7.6,21.1,21.2\"}");
		// 先只考虑逐笔，比速度
		m_autosub->SetOptionsJson("{\"ExMsgs\": \"3.3,3.8,3.9,3.10,4.4,4.6,4.16,4.17,4.23,4.20,4.21,4.22,4.9,4.29,5.11,5.2,5.6,5.7,5.8,6.28,6.29,6.30,6.31,6.51,6.42,6.43,6.44,6.45,6.46,6.47,6.50,5.5,5.10,3.13,7.1,7.6,21.1,21.2\"}");

		// set time to check server config every day, default 520 (05:20)
		//   if server config changes, existing autoconf.json will be renamed to autoconf.json.0
		// m_autosub->SetDailyCheckTime(520); 

		// connect to servers and subscribe messages
		std::string err = m_autosub->Start();
		if (err.empty()) {
			printf("auto-subscriber started successfully.\n");
			return true;
		}
		printf("auto-subscriber start failed: %s\n", err.c_str());
		return false; 
	}
 
	void Close() {
		if (!m_IOManager.IsNull()) {
			m_IOManager->Shutdown();
		}
		
		// 关闭文件
		m_file_szl2_order.close();
		m_file_szl2_trans.close();
		m_file_shl2_trans_order.close();
		
		// 销毁互斥锁
		pthread_mutex_destroy(&m_lock_szl2_order);
		pthread_mutex_destroy(&m_lock_szl2_trans);
		pthread_mutex_destroy(&m_lock_shl2_trans_order);
	}

	// handle network failure
	virtual void OnMDLAPIMessage(const MDLMessage* msg) {
		MDLMessageHead* head = msg->GetHead();
		if (head->MessageID == mdl_api_msg::ConnectingEvent::MessageID) {
			mdl_api_msg::ConnectingEvent* resp = (mdl_api_msg::ConnectingEvent*)msg->GetBody();
			printf("Connect to %s ...\n", resp->Address.c_str());
		}
		else if (head->MessageID == mdl_api_msg::ConnectErrorEvent::MessageID) {
			mdl_api_msg::ConnectErrorEvent* resp = (mdl_api_msg::ConnectErrorEvent*)msg->GetBody();
			printf("Connect to %s failed %s.\n", resp->Address.c_str(), resp->ErrorMessage.c_str());
		}
		else if (head->MessageID == mdl_api_msg::DisconnectedEvent::MessageID) {
			mdl_api_msg::DisconnectedEvent* resp = (mdl_api_msg::DisconnectedEvent*)msg->GetBody();
			printf("Disconnected from %s: %s.\n", resp->Address.c_str(), resp->ErrorMessage.c_str());
		}
	}

	// handle server response
	virtual void OnMDLSysMessage(const MDLMessage* msg) {
		MDLMessageHead* head = msg->GetHead();
		if (head->MessageID == mdl_sys_msg::LogonResponse::MessageID) {
			mdl_sys_msg::LogonResponse* resp = (mdl_sys_msg::LogonResponse*)msg->GetBody();
			if (resp->ReturnCode != MDLEC_OK) {
				printf("Logon failed: return code %d.\n", resp->ReturnCode);
			}
			for (uint32_t i = 0; i < resp->Services.Length; ++i) {
				for (uint32_t j = 0; j < resp->Services[i]->Messages.Length; ++j) {
					if (resp->Services[i]->Messages[j]->MessageStatus != MDLEC_OK) {
						printf("The server doesn't publish message (service id %d message id %d)\n", 
							resp->Services[i]->ServiceID,
							resp->Services[i]->Messages[j]->MessageID);
					}
				}
			}
		}
	}

	// print shfe future message
	virtual void OnMDLSHFEMessage(const MDLMessage* msg) {
		MDLMessageHead* head = msg->GetHead();
		if (head->MessageID == mdl_shfe_msg::CTPFuture::MessageID) {
			mdl_shfe_msg::CTPFuture* body = (mdl_shfe_msg::CTPFuture*)msg->GetBody();
			PrintFutureMessage(body);
		}
	}

	// print czce future message
	virtual void OnMDLCZCEMessage(const MDLMessage* msg) {
		MDLMessageHead* head = msg->GetHead();
		if (head->MessageID == mdl_czce_msg::CTPFuture::MessageID) {
			mdl_czce_msg::CTPFuture* body = (mdl_czce_msg::CTPFuture*)msg->GetBody();
			PrintFutureMessage(body);
		}
	}

	// print cffex future message
	virtual void OnMDLCFFEXMessage(const MDLMessage* msg) {
		MDLMessageHead* head = msg->GetHead();
		if (head->MessageID == mdl_cffex_msg::CTPFuture::MessageID) {
			mdl_cffex_msg::CTPFuture* body = (mdl_cffex_msg::CTPFuture*)msg->GetBody();
			PrintFutureMessage(body);
		}
	}

	// print dce future message
	virtual void OnMDLDCEMessage(const MDLMessage* msg) {
		MDLMessageHead* head = msg->GetHead();
		if (head->MessageID == mdl_dce_msg::CTPFuture::MessageID) {
			mdl_dce_msg::CTPFuture* body = (mdl_dce_msg::CTPFuture*)msg->GetBody();
			PrintFutureMessage(body);
		}
	}

	// print shanghai level1 message
	virtual void OnMDLSHL1Message(const MDLMessage* msg) {
		MDLMessageHead* head = msg->GetHead();
		if (head->MessageID == mdl_shl1_msg::Equity2::MessageID) {
			mdl_shl1_msg::Equity2* body = (mdl_shl1_msg::Equity2*)msg->GetBody(); 
			PrintNewStockMessage(body);
		}
		else if (head->MessageID == mdl_shl1_msg::Indexes::MessageID) {
			mdl_shl1_msg::Indexes* body = (mdl_shl1_msg::Indexes*)msg->GetBody(); 
			PrintIndexMessage(body);
		}
		else if (head->MessageID == mdl_shl1_msg::Bond2::MessageID) {
			mdl_shl1_msg::Bond2* body = (mdl_shl1_msg::Bond2*)msg->GetBody(); 
			PrintNewStockMessage(body);
		}
		else if (head->MessageID == mdl_shl1_msg::Fund2::MessageID) {
			mdl_shl1_msg::Fund2* body = (mdl_shl1_msg::Fund2*)msg->GetBody(); 
			PrintNewStockMessage(body);
		}
	}

	// print shenzhen level1 message
	virtual void OnMDLSZL1Message(const MDLMessage* msg) {
		MDLMessageHead* head = msg->GetHead();
		if (head->MessageID == mdl_szl1_msg::Index2::MessageID) {
			mdl_szl1_msg::Index2* body = (mdl_szl1_msg::Index2*)msg->GetBody();
			PrintIndexMessage(body); 
		}
		else if (head->MessageID == mdl_szl1_msg::SZL1Stock::MessageID) {
			mdl_szl1_msg::SZL1Stock* body = (mdl_szl1_msg::SZL1Stock*)msg->GetBody();
			PrintStockMessage(body); 
		}
	}

	template <class T> 
	void PrintIndexMessage(const T* msgBody) {
		printf("%d:%02d:%02d %s %s HighIndex:%s LowIndex:%s LastIndex:%s\n",
			msgBody->UpdateTime.GetHour(), msgBody->UpdateTime.GetMinute(), msgBody->UpdateTime.GetSecond(),
			msgBody->IndexID.std_str().c_str(), msgBody->IndexName.std_str().c_str(), 
			DoubleToString(msgBody->HighIndex).c_str(), 
			DoubleToString(msgBody->LowIndex).c_str(), 
			DoubleToString(msgBody->LastIndex).c_str());
	}
	template <class T> 
	void PrintNewStockMessage(const T* msgBody) {
		printf("%d:%02d:%02d %s %s HighPrice:%s LowPrice:%s LastPrice:%s\n",
			msgBody->UpdateTime.GetHour(), msgBody->UpdateTime.GetMinute(), msgBody->UpdateTime.GetSecond(),
			msgBody->SecurityID.std_str().c_str(), msgBody->SecurityName.std_str().c_str(), 
			DoubleToString(msgBody->HighPrice).c_str(), 
			DoubleToString(msgBody->LowPrice).c_str(), 
			DoubleToString(msgBody->LastPrice).c_str());
	}
	template <class T> 
	void PrintStockMessage(const T* msgBody) {
		printf("%d:%02d:%02d %s %s HighPrice:%s LowPrice:%s LastPrice:%s\n",
			msgBody->UpdateTime.GetHour(), msgBody->UpdateTime.GetMinute(), msgBody->UpdateTime.GetSecond(),
			msgBody->SecurityID.std_str().c_str(), msgBody->SecurityName.std_str().c_str(), 
			FloatToString(msgBody->HighPrice).c_str(), 
			FloatToString(msgBody->LowPrice).c_str(), 
			FloatToString(msgBody->LastPrice).c_str());
	}

	template <class T> 
	void PrintFutureMessage(const T* msgBody) {
		printf("%d:%02d:%02d %s HighPrice:%s LowPrice:%s LastPrice:%s\n",
			msgBody->UpdateTime.GetHour(), msgBody->UpdateTime.GetMinute(), msgBody->UpdateTime.GetSecond(),
			msgBody->InstruID.std_str().c_str(),
			DoubleToString(msgBody->HighPrice).c_str(),
			DoubleToString(msgBody->LowPrice).c_str(),
			DoubleToString(msgBody->LastPrice).c_str());
	}

	template <class T>
	std::string DoubleToString(const T& mdlDouble) {
		if (mdlDouble.IsNull()) {
			return std::string("null");
		}
		char strBuf[100];
#if defined(__linux__)
        sprintf(strBuf, "%.2f", mdlDouble.GetDouble());
#else
		sprintf_s(strBuf, sizeof(strBuf), "%.2f", mdlDouble.GetDouble());
#endif
		return std::string(strBuf);
	}

	template <class T>
	std::string FloatToString(const T& mdlFloat) {
		if (mdlFloat.IsNull()) {
			return std::string("null");
		}
		char strBuf[100];
#if defined(__linux__)
        sprintf(strBuf, "%.2f", mdlFloat.GetFloat());
#else
		sprintf_s(strBuf, sizeof(strBuf), "%.2f", mdlFloat.GetFloat());
#endif
		return std::string(strBuf);
	}

	// print hkex message
	virtual void OnMDLHKExMessage(const MDLMessage* msg) {
		MDLMessageHead* head = msg->GetHead();
		if (head->MessageID == mdl_hkex_msg::OMDMarketData::MessageID) {
			mdl_hkex_msg::OMDMarketData* body = (mdl_hkex_msg::OMDMarketData*)msg->GetBody();
			printf("[%d:%02d:%02d.%d] %s ",
				body->TradTime.GetHour(), body->TradTime.GetMinute(), body->TradTime.GetSecond(), body->TradTime.GetMilliSec(),
				body->TickerSymbol.c_str());
			if (body->LastPrice.IsNull()) {
				printf("LastPrice:null");
			}
			else {
				printf("LastPrice:%.2f", body->LastPrice.GetFloat());
			}	
			if (body->ChangePct.IsNull()) {
				printf("(),");
			}
			else {
				printf("(%.3f%%),", body->ChangePct.GetFloat() * 100);
			}	
			printf("Volume:%.3fM,", (float)body->Quantity / 1000000.0);
			if (body->Turnover.IsNull()) {
				printf("Turnover:null\n");
			}
			else {
				printf("Turnover:%.3fM\n", body->Turnover.GetDouble() / 1000000.0);
			}	

			if (body->AskBook.Length > 0) {
				if (body->AskBook[0]->Price.IsNull()) {
					printf("	Ask1:null,0,");
				}
				else {
					printf("	Ask1:%.3f,%ld,", body->AskBook[0]->Price.GetFloat(), body->AskBook[0]->Volume);
				}
			}
			if (body->BidBook.Length > 0) {
				if (body->BidBook[0]->Price.IsNull()) {
					printf("	Bid1:null,0");
				}
				else {
					printf("	Bid1:%.3f,%ld", body->BidBook[0]->Price.GetFloat(), body->BidBook[0]->Volume);
				}
			} 
			printf("\n");
		}
	}

	// Print current time in HH:MM:SS.uuuuuu format
	void PrintCurrentTime(const char* prefix = "") {
		struct timeval tv;
		gettimeofday(&tv, NULL);
		struct tm timeinfo;
		localtime_r(&tv.tv_sec, &timeinfo);
		
		printf("%s%02d:%02d:%02d.%06d <%04ld>\n", 
			prefix,
			timeinfo.tm_hour, 
			timeinfo.tm_min, 
			timeinfo.tm_sec, 
			(int)tv.tv_usec, pthread_self() % 10000);
	}

	// handle shenzhen level2 message
	void OnMDLSZL2Message(const MDLMessage* msg) {
		auto now = std::chrono::system_clock::now();

		MDLMessageHead* head = msg->GetHead();
		if (head->MessageID == mdl_szl2_msg::Snapshot300111_v2::MessageID) {  // 6.28 市场行情
			mdl_szl2_msg::Snapshot300111_v2 * resp = (mdl_szl2_msg::Snapshot300111_v2*)msg->GetBody();
			printf("[%d:%02d:%02d.%03d] SZL2 MarketData %s %s Hi:%.6f Lo:%.6f Last:%.6f Vol:%ld Tun:%.4f\n",
				resp->UpdateTime.GetHour(), resp->UpdateTime.GetMinute(), resp->UpdateTime.GetSecond(), resp->UpdateTime.GetMilliSec(),
				resp->SecurityID.std_str().c_str(), 
				resp->TradingPhaseCode.c_str(),
				resp->HighPrice.GetDouble(), resp->LowPrice.GetDouble(), resp->LastPrice.GetDouble(),
				resp->Volume,
				resp->Turnover.GetDouble());

			for (size_t i = 0; i < 10; ++i) {
				double askPrice = 0.0;
				int64_t askVolumn = 0.0;
				double bidPrice = 0.0;
				int64_t bidVolumn = 0.0;
				if (i < resp->AskPriceLevel.Length) {
					askPrice = resp->AskPriceLevel[i]->Price.GetDouble();
					askVolumn = resp->AskPriceLevel[i]->Volume;
				}
				if (i < resp->BidPriceLevel.Length) {
					bidPrice = resp->BidPriceLevel[i]->Price.GetDouble();
					bidVolumn = resp->BidPriceLevel[i]->Volume;
				}
				printf("%.6f %ld\t\t%.6f %ld\n", askPrice, askVolumn, bidPrice, bidVolumn);
			}
		}
		else if (head->MessageID == mdl_szl2_msg::Order300192_v2::MessageID) {  // 6.33 逐笔委托
			mdl_szl2_msg::Order300192_v2 * resp = (mdl_szl2_msg::Order300192_v2*)msg->GetBody();

			(void)pthread_mutex_lock(&m_lock_szl2_order);
			m_file_szl2_order << fmt::format("{},{},{},{},{},{},{},{},{},{:02d}:{:02d}:{:02d}.{:03d},{}\n", now, resp->ChannelNo, resp->ApplSeqNum, resp->MDStreamID.std_str().c_str(),
				resp->SecurityID.std_str().c_str(), resp->SecurityIDSource.std_str().c_str(), resp->Price.GetDouble(), resp->OrderQty, resp->Side,
				resp->TransactTime.GetHour(), resp->TransactTime.GetMinute(), resp->TransactTime.GetSecond(), resp->TransactTime.GetMilliSec(), resp->OrdType);
			(void)pthread_mutex_unlock(&m_lock_szl2_order);
		}
		else if (head->MessageID == mdl_szl2_msg::Transaction300191_v2::MessageID) {  // 6.36 逐笔成交
			mdl_szl2_msg::Transaction300191_v2 * resp = (mdl_szl2_msg::Transaction300191_v2*)msg->GetBody();

			(void)pthread_mutex_lock(&m_lock_szl2_trans);
			m_file_szl2_trans << fmt::format("{},{},{},{},{},{},{},{},{},{},{},{:02d}:{:02d}:{:02d}.{:03d}\n", now, resp->ChannelNo, resp->ApplSeqNum, resp->MDStreamID.std_str().c_str(),
				resp->BidApplSeqNum, resp->OfferApplSeqNum, resp->SecurityID.std_str().c_str(), resp->SecurityIDSource.std_str().c_str(), resp->LastPx.GetDouble(), resp->LastQty, resp->ExecType,
				resp->TransactTime.GetHour(), resp->TransactTime.GetMinute(), resp->TransactTime.GetSecond(), resp->TransactTime.GetMilliSec());
			(void)pthread_mutex_unlock(&m_lock_szl2_trans);
		}
		else if (head->MessageID == mdl_szl2_msg::Snapshot309011_v2::MessageID) {  // 6.29 指数行情
			mdl_szl2_msg::Snapshot309011_v2 * resp = (mdl_szl2_msg::Snapshot309011_v2*)msg->GetBody();
			printf("[%d:%02d:%02d.%03d] SZL2 Index %s %s\n",
				resp->UpdateTime.GetHour(), resp->UpdateTime.GetMinute(), resp->UpdateTime.GetSecond(), resp->UpdateTime.GetMilliSec(),
				resp->SecurityID.std_str().c_str(), 
				resp->TradingPhaseCode.c_str());
		}
		else {
			printf("Unknow SZL2 message %d\n", head->MessageID);
		}
	}

	// handle shanghai level2 message
	void OnMDLSHL2Message(const MDLMessage* msg) {
		auto now = std::chrono::system_clock::now();

		if (msg->GetHead()->MessageID == mdl_shl2_msg::SHL2Index::MessageID) {  // 4.6 指数行情
			mdl_shl2_msg::SHL2Index * resp = (mdl_shl2_msg::SHL2Index*)msg->GetBody();
			printf("#%ld [%d:%02d:%02d.%03d] SHL2 Index %s Hi:%.2f Lo:%.2f Last:%.2f\n",
				msg->GetHead()->SequenceID,
				resp->UpdateTime.GetHour(), resp->UpdateTime.GetMinute(), resp->UpdateTime.GetSecond(), resp->UpdateTime.GetMilliSec(),
				resp->SecurityID.std_str().c_str(),  
				resp->HighIndex.GetDouble(), resp->LowIndex.GetDouble(), resp->LastIndex.GetDouble());
		}	
		else if (msg->GetHead()->MessageID == mdl_shl2_msg::SHL2MarketData::MessageID) {  // 4.4 个股行情
			mdl_shl2_msg::SHL2MarketData * resp = (mdl_shl2_msg::SHL2MarketData*)msg->GetBody();
 			printf("#%ld [%d:%02d:%02d.%03d] SHL2 MarketData %s Hi:%.2f Lo:%.2f Last:%.2f\n",
				msg->GetHead()->SequenceID,
				resp->UpdateTime.GetHour(), resp->UpdateTime.GetMinute(), resp->UpdateTime.GetSecond(), resp->UpdateTime.GetMilliSec(),
				resp->SecurityID.std_str().c_str(), 
				resp->HighPrice.GetFloat(),	resp->LowPrice.GetFloat(), resp->LastPrice.GetFloat());
		}
		else if (msg->GetHead()->MessageID == mdl_shl2_msg::NGTSTick::MessageID) {  // 4.24 竞价逐笔合并行情
			mdl_shl2_msg::NGTSTick * resp = (mdl_shl2_msg::NGTSTick*)msg->GetBody();
			(void)pthread_mutex_lock(&m_lock_shl2_trans_order);
			m_file_shl2_trans_order << fmt::format("{},{},{},{},{:02d}:{:02d}:{:02d}.{:03d},{},{},{},{},{},{},{}\n", now, resp->BizIndex, resp->Channel, resp->SecurityID.std_str().c_str(),
				resp->TickTime.GetHour(), resp->TickTime.GetMinute(), resp->TickTime.GetSecond(), resp->TickTime.GetMilliSec(), resp->Type.c_str(),
				resp->BuyOrderNO, resp->SellOrderNO, resp->Price.GetFloat(), resp->Qty, resp->TradeMoney.GetDouble(), resp->TickBSFlag.c_str());
			(void)pthread_mutex_unlock(&m_lock_shl2_trans_order);
		}
		else if (msg->GetHead()->MessageID == mdl_shl2_msg::OPTLevel1::MessageID) {
			mdl_shl2_msg::OPTLevel1 * resp = (mdl_shl2_msg::OPTLevel1*)msg->GetBody();
 			printf("#%ld [%d:%02d:%02d.%03d] SHL2 OPTLevel1 %s Hi:%.2f Lo:%.2f Last:%.2f\n",
				msg->GetHead()->SequenceID,
				resp->UpdateTime.GetHour(), resp->UpdateTime.GetMinute(), resp->UpdateTime.GetSecond(), resp->UpdateTime.GetMilliSec(),
				resp->SecurityID.std_str().c_str(), 
				resp->HighPx.GetDouble(),	resp->LowPx.GetDouble(), resp->LastPx.GetDouble());
		}
		else {
			printf("Unknow SHL2 message %d\n", msg->GetHead()->MessageID);
		}
	}

	virtual void OnMDLBARMessage(const MDLMessage* msg) {
		if (msg->GetHead()->MessageID == mdl_bar_msg::XSHGStockMinuteBar::MessageID) {
			mdl_bar_msg::XSHGStockMinuteBar * resp = (mdl_bar_msg::XSHGStockMinuteBar*)msg->GetBody();
 			printf("#%ld [%d:%02d:%02d] %s Hi:%.2f Lo:%.2f Last:%.2f\n",
				msg->GetHead()->SequenceID,
				resp->BarTime.GetHour(), resp->BarTime.GetMinute(), resp->BarTime.GetSecond(),
				resp->SecurityID.std_str().c_str(), 
				resp->HighPrice.GetDouble(), resp->LowPrice.GetDouble(), resp->LowPrice.GetDouble());
		}
		else if (msg->GetHead()->MessageID == mdl_bar_msg::XSHGCapitalFlow::MessageID) {
			mdl_bar_msg::XSHGCapitalFlow * resp = (mdl_bar_msg::XSHGCapitalFlow*)msg->GetBody();
 			printf("#%ld [%d:%02d:%02d] %s %s Px:%.2f Vol:%ld NetIn:%.2f\n",
				msg->GetHead()->SequenceID,
				resp->TradeTime.GetHour(), resp->TradeTime.GetMinute(), resp->TradeTime.GetSecond(),
				resp->SecurityID.std_str().c_str(), 
				resp->BSFlag == 1 ? "B" : (resp->BSFlag == 2 ? "S" : "NA"),
				resp->Price.GetDouble(), resp->Volume, resp->NetCapitalInflow.GetDouble());
		}
		else if (msg->GetHead()->MessageID == mdl_bar_msg::IndustryCapitalFlow::MessageID) {
			mdl_bar_msg::IndustryCapitalFlow * resp = (mdl_bar_msg::IndustryCapitalFlow*)msg->GetBody();
 			printf("#%ld [%d:%02d:%02d] %s %s In:%.2f OutIn:%.2f NetIn:%.2f\n",
				msg->GetHead()->SequenceID,
				resp->TradeTime.GetHour(), resp->TradeTime.GetMinute(), resp->TradeTime.GetSecond(),
				resp->SecurityID.std_str().c_str(), 
				resp->SecurityName.std_str().c_str(),  
				resp->CapitalInFlow.GetDouble(),
				resp->CapitalOutFlow.GetDouble(),
				resp->NetCapitalInflow.GetDouble());
		}
	}

private:
	IOManagerPtr m_IOManager;
	AutoSubscriberPtr m_autosub;
	pthread_mutex_t m_lock_szl2_order;
	pthread_mutex_t m_lock_szl2_trans;
	pthread_mutex_t m_lock_shl2_trans_order;
	// fmt::ostream m_file_szl2_order;      // 保存 szl2 逐笔委托
	// fmt::ostream m_file_szl2_trans;      // 保存 szl2 逐笔成交
	// fmt::ostream m_file_shl2_trans_order; // 保存 shl2 竞价逐笔合并行情
};

///////////////////////////////////////////////////////////////////////////////////////////////////////////
// 定义命令行选项结构
struct ProgramOptions {
    const char* server;
    const char* token;
	const char* filep;  // file prefix
    bool help;
};

// 解析命令行参数
static ProgramOptions
ParseCommandLine(int argc, char* argv[]) {
    ProgramOptions options = {
        "https://mdl01.datayes.com:19000/subscribe",  // 默认服务器地址
        "4EEB86544CA6F15C44F63118F55224CC",          // 默认令牌
        "dy_",                                        // 默认文件前缀
        false                                         // 不显示帮助
    };

    static struct option long_options[] = {
        {"server", required_argument, 0, 's'},
        {"token",  required_argument, 0, 't'},
        {"filep",  required_argument, 0, 'f'},
        {"help",   no_argument,       0, 'h'},
        {0, 0, 0, 0}
    };

    int opt;
    int option_index = 0;

    while ((opt = getopt_long(argc, argv, "s:t:f:h", long_options, &option_index)) != -1) {
        switch (opt) {
            case 's':
                options.server = optarg;
                break;
            case 't':
                options.token = optarg;
                break;
            case 'f':
                options.filep = optarg;
                break;
            case 'h':
                options.help = true;
                break;
            default:
                break;
        }
    }

    return options;
}

// 显示帮助信息
static void
PrintUsage(const char* program_name) {
    printf("Usage: %s [OPTIONS]\n", program_name);
    printf("Options:\n");
    printf("  -s, --server=URL    Server URL (default: https://mdl01.datayes.com:19000/subscribe)\n");
    printf("  -t, --token=TOKEN   Authentication token (default: 4EEB86544CA6F15C44F63118F55224CC)\n");
    printf("  -f, --filep=FILEP   File prefix (default: dy_)\n");
    printf("  -h, --help          Display this help and exit\n");
}

int main(int argc, char* argv[]) { 
    // 解析命令行参数
    ProgramOptions options = ParseCommandLine(argc, argv);

    // 如果请求帮助，显示帮助信息并退出
    if (options.help) {
        PrintUsage(argv[0]);
        return 0;
    }

	MyMessageHandler msgHandler;
    if (msgHandler.Open(options.server, options.token, options.filep)) {
        printf("Connected to server: %s\n", options.server);
        printf("Using token: %s\n", options.token);
        printf("Receiving message, press enter to stop.\n");
        pause();
    }

    msgHandler.Close(); 
    printf("Press enter to exit.\n");
    getchar();
    return 0;
}


DEMO:=md2
OBJS:=md2.o
LIBDIR:=-L../libs/linux
SODIR:=-Wl,-rpath=./ 
CPPFLAGS:=-g -O2 -std=c++11 -Wall
INCS:=-I../include -I../3rd -I../../fmt-11.2.0/include
LIBS:=-lmdl_api -ldl -lpthread -lrt
OUTDIR:=../bin

all: $(OUTDIR)/$(DEMO)

$(OUTDIR)/$(DEMO): $(DEMO)
	cp $(DEMO) $(OUTDIR)/$(DEMO)

%.o: %.cpp
	g++ $(INCS) $(CPPFLAGS) -c "$<" -o $@

$(DEMO): % : %.o
	g++ $(SODIR) $@.o -o $@ $(LIBDIR) $(LIBS)
	ln -sf ../libs/linux/libmdl_api.so $(OUTDIR)/libmdl_api.so

.PHONY: clean
clean:
	rm -rf $(OUTDIR)/$(DEMO) && rm -f $(OBJS) $(DEMO)


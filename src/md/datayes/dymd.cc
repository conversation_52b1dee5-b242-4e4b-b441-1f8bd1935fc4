// Based on mdl_api_demo.cpp, manual connect/subscribe to both markets (Shenzhen and Shanghai) at the same time.
// Based on md2.cpp

#include <stdio.h>
#include <pthread.h>
#include <fstream>
#include <getopt.h>
#include <sys/time.h>
#include <sys/types.h>
#include <sys/stat.h>
#include <fcntl.h>
#include <signal.h>

#include "mdl_api.h"
#include "mdl_shl1_msg.h"
#include "mdl_shl2_msg.h"
#include "mdl_szl2_msg.h"
#include "mdl_szl1_msg.h"
#include "mdl_cffex_msg.h"
#include "mdl_czce_msg.h"
#include "mdl_dce_msg.h"
#include "mdl_shfe_msg.h"
#include "mdl_hkex_msg.h"
#include "mdl_bar_msg.h"

#define FMT_HEADER_ONLY
#include "fmt/base.h"
#include "fmt/chrono.h"

#include "lyproto.quota.pb.h"

#include <zmq.h>
#include <mutex>          // std::mutex, std::lock_guard

void *g_zmq_ctx = NULL;
void *g_zmq = NULL;
std::mutex mtx;

int num_work_threads = 6;
IOManagerPtr g_IOManager;

int fd_transaction = -1;

struct timeval  g_now;
struct tm       g_nowtm;

bool dymd_init()
{
	g_zmq_ctx = zmq_ctx_new();
	g_zmq = zmq_socket(g_zmq_ctx, ZMQ_PUB);
	zmq_bind(g_zmq, "tcp://*:9870");

	g_IOManager = CreateIOManager(num_work_threads);
	if (g_IOManager.IsNull()) {
		printf("Incompatible API lib version.\n");
		return false;
	}
	g_IOManager->EnableLog("/dev/shm/mdl");

	gettimeofday(&g_now, NULL); 
	localtime_r((time_t *)&g_now.tv_sec, &g_nowtm);

	char fn[200];
	snprintf(fn, sizeof(fn), "/s/archives/mdtr_%04d%02d%02d_%02d%02d%02d.dat", g_nowtm.tm_year+1900, g_nowtm.tm_mon+1, g_nowtm.tm_mday, g_nowtm.tm_hour, g_nowtm.tm_min, g_nowtm.tm_sec);
	fd_transaction = open(fn, O_WRONLY|O_CREAT, 0666);

	return true;
}

void dymd_fini()
{
	if (!g_IOManager.IsNull()) {
		g_IOManager->Shutdown();
	}
	if (fd_transaction >= 0) {
		close(fd_transaction);
		fd_transaction = -1;
	}
	if (g_zmq) {
		zmq_close(g_zmq);
		g_zmq = NULL;
	}
	if (g_zmq_ctx) {
		zmq_ctx_destroy(g_zmq_ctx);
		g_zmq_ctx = NULL;
	}
}


#define MARKET_SZ 1  // Shenzhen
#define MARKET_SH 2  // Shanghai

#define SERVER_SZ "mdl-cloud-bj.datayes.com:19012"
//#define SERVER_SZ "mdl-cloud-sz.datayes.com:19012"
#define SERVER_SH "mdl-cloud-bj.datayes.com:19014"
//#define SERVER_SH "mdl-cloud-sh.datayes.com:19014"

// 这个是不行的
//#define SERVER_SH "mdl-sse01.datayes.com:19010"

#define DATA_TICK 1  // Tick data
#define DATA_TRANSORD 2   // Transaction and Order data

using namespace datayes::mdl;

class MyMessageHandler : public MessageHandler {
	private:
    std::ofstream m_file_szl2_order;        // 深圳L2逐笔委托文件
    std::ofstream m_file_szl2_trans;        // 深圳L2逐笔成交文件
    std::ofstream m_file_shl2_trans_order;  // 上海L2竞价逐笔合并行情文件
	std::ofstream m_file_szl2_tick;         // 深圳L2 tick文件
	std::ofstream m_file_shl2_tick;         // 上海L2 tick文件

	pthread_mutex_t m_lock_szl2_order;
	pthread_mutex_t m_lock_szl2_trans;
	pthread_mutex_t m_lock_shl2_trans_order;
	pthread_mutex_t m_lock_szl2_tick;
	pthread_mutex_t m_lock_shl2_tick;

	public:
	bool Open(int market, int data_type, const char * filep) {
		// 初始化互斥锁
		pthread_mutex_init(&m_lock_szl2_order, NULL);
		pthread_mutex_init(&m_lock_szl2_trans, NULL);
		pthread_mutex_init(&m_lock_shl2_trans_order, NULL);
		pthread_mutex_init(&m_lock_szl2_tick, NULL);
		pthread_mutex_init(&m_lock_shl2_tick, NULL);
				
		// 用于保存文件名
		std::string filename;

		bool multi_thread_callback = true; // set to true if num_work_threads > 1 and OnXXXMessage() is thread-safe
		SubscriberPtr sub = g_IOManager->CreateSubscriber(this, multi_thread_callback);
		if (market == MARKET_SZ) {
			sub->SetServerAddress(SERVER_SZ);
			if (data_type == DATA_TICK) {
				// subscribe Shenzhen L2 tick data
				sub->SubcribeMessage<mdl_szl2_msg::Snapshot300111_v2>();
				filename = std::string(filep) + "sz_tick.csv";
				m_file_szl2_tick.open(filename, std::ios::out | std::ios::trunc);
				if (!m_file_szl2_tick.is_open()) {
					printf("Failed to open file %s for saving Shenzhen L2 tick data\n", filename.c_str());
					return false;
				}
				// 写入CSV文件头
				m_file_szl2_tick << "RecvTime,UpdateTime,ChannelNo,StreamID,SecurityID,SecurityIDSource,TradingPhase,PreCloPrice,TurnNum,Volume,Turnover,LastPrice,"
					"OpenPrice,HighPrice,LowPrice,DifPrice1,DifPrice2,PE1,PE2,PreCloseIOPV,IOPV,TotalOfferQty,WeightedAvgOfferPx,TotalBidQty,WeightedAvgBidPx,HighLimitPrice,"
					"LowLimitPrice,OpenInt,OptPremiumRatio,BuyPrice1,BuyVolume1,BuyNumOrders1,BuyPrice2,BuyVolume2,BuyNumOrders2,BuyPrice3,BuyVolume3,BuyNumOrders3,"
					"BuyPrice4,BuyVolume4,BuyNumOrders4,BuyPrice5,BuyVolume5,BuyNumOrders5,BuyPrice6,BuyVolume6,BuyNumOrders6,BuyPrice7,BuyVolume7,BuyNumOrders7,"
					"BuyPrice8,BuyVolume8,BuyNumOrders8,BuyPrice9,BuyVolume9,BuyNumOrders9,BuyPrice10,BuyVolume10,BuyNumOrders10,"
					"SellPrice1,SellVolume1,SellNumOrders1,SellPrice2,SellVolume2,SellNumOrders2,SellPrice3,SellVolume3,SellNumOrders3,SellPrice4,SellVolume4,SellNumOrders4,"
					"SellPrice5,SellVolume5,SellNumOrders5,SellPrice6,SellVolume6,SellNumOrders6,SellPrice7,SellVolume7,SellNumOrders7,SellPrice8,SellVolume8,SellNumOrders8,"
					"SellPrice9,SellVolume9,SellNumOrders9,SellPrice10,SellVolume10,SellNumOrders10,Delay" << std::endl;
			} else if (data_type == DATA_TRANSORD) {
				// subscribe Shenzhen order and transaction data
				sub->SubcribeMessage<mdl_szl2_msg::Order300192_v2>();
				sub->SubcribeMessage<mdl_szl2_msg::Transaction300191_v2>();
				filename = std::string(filep) + "sz_order.csv";
				m_file_szl2_order.open(filename, std::ios::out | std::ios::trunc);
				if (!m_file_szl2_order.is_open()) {
					printf("Failed to open file %s for saving Shenzhen L2 order data\n", filename.c_str());
					return false;
				}
				// 写入CSV文件头
				m_file_szl2_order << "RecvTime,ChannelNo,ApplSeqNum,StreamID,SecurityID,SecurityIDSource,Price,OrderQty,Side,TransactTime,OrdType,Delay" << std::endl;
				filename = std::string(filep) + "sz_trans.csv";
				m_file_szl2_trans.open(filename, std::ios::out | std::ios::trunc);
				if (!m_file_szl2_trans.is_open()) {
					printf("Failed to open file %s for saving Shenzhen L2 transaction data\n", filename.c_str());
					m_file_szl2_order.close();
					return false;
				}
				// 写入CSV文件头
				m_file_szl2_trans << "RecvTime,ChannelNo,ApplSeqNum,StreamID,BidApplSeqNum,OfferApplSeqNum,SecurityID,SecurityIDSource,LastPx,LastQty,ExecType,TransactTime,Delay" << std::endl;
			} else {
				printf("Invalid data type\n");
				return false;
			}
		} else if (market == MARKET_SH) {
			sub->SetServerAddress(SERVER_SH);
			if (data_type == DATA_TICK) {
				// subscribe Shanghai L2 tick data
				sub->SubcribeMessage<mdl_shl2_msg::SHL2MarketData>();
				filename = std::string(filep) + "sh_tick.csv";
				m_file_shl2_tick.open(filename, std::ios::out | std::ios::trunc);
				if (!m_file_shl2_tick.is_open()) {
					printf("Failed to open file %s for saving Shanghai L2 tick data\n", filename.c_str());
					return false;
				}
				// 写入CSV文件头
				m_file_shl2_tick << "RecvTime,UpdateTime,SecurityID,ImageStatus,PreCloPrice,OpenPrice,HighPrice,LowPrice,LastPrice,ClosePrice,InstruStatus,TradNumber,TradVolume,Turnover,"
					"TotalBidVol,WAvgBidPri,AltWAvgBidPri,TotalAskVol,WAvgAskPri,AltWAvgAskPri,EtfBuyNumber,EtfBuyVolume,EtfBuyMoney,EtfSellNumber,EtfSellVolume,ETFSellMoney,YieldToMatu,"
					"TotWarExNum,WarLowerPri,WarUpperPri,WiDBuyNum,WiDBuyVol,WiDBuyMon,WiDSellNum,WiDSellVol,WiDSellMon,TotBidNum,TotSellNum,MaxBidDur,MaxSellDur,BidNum,SellNum,IOPV,"
					"BuyPrice1,BuyVolume1,BuyNumOrders1,BuyPrice2,BuyVolume2,BuyNumOrders2,BuyPrice3,BuyVolume3,BuyNumOrders3,BuyPrice4,BuyVolume4,BuyNumOrders4,BuyPrice5,BuyVolume5,BuyNumOrders5,"
					"BuyPrice6,BuyVolume6,BuyNumOrders6,BuyPrice7,BuyVolume7,BuyNumOrders7,BuyPrice8,BuyVolume8,BuyNumOrders8,BuyPrice9,BuyVolume9,BuyNumOrders9,BuyPrice10,BuyVolume10,BuyNumOrders10,"
					"SellPrice1,SellVolume1,SellNumOrders1,SellPrice2,SellVolume2,SellNumOrders2,SellPrice3,SellVolume3,SellNumOrders3,SellPrice4,SellVolume4,SellNumOrders4,"
					"SellPrice5,SellVolume5,SellNumOrders5,SellPrice6,SellVolume6,SellNumOrders6,SellPrice7,SellVolume7,SellNumOrders7,SellPrice8,SellVolume8,SellNumOrders8,"
					"SellPrice9,SellVolume9,SellNumOrders9,SellPrice10,SellVolume10,SellNumOrders10,Delay" << std::endl;
			} else if (data_type == DATA_TRANSORD) {
				// subscribe Shanghai order and transaction data
				sub->SubcribeMessage<mdl_shl2_msg::NGTSTick>();
				filename = std::string(filep) + "sh_transorder.csv";
				m_file_shl2_trans_order.open(filename, std::ios::out | std::ios::trunc);
				if (!m_file_shl2_trans_order.is_open()) {
					printf("Failed to open file %s for saving Shanghai L2 transaction and order data\n", filename.c_str());
					m_file_shl2_tick.close();
					return false;
				}
				// 写入CSV文件头
				m_file_shl2_trans_order << "RecvTime,BizIndex,Channel,SecurityID,TickTime,Type,BuyOrderNO,SellOrderNO,Price,Qty,TradeMoney,TickBSFlag,Delay" << std::endl;
			} else {
				printf("Invalid data type\n");
				return false;
			}
		} else {
			printf("Wrong market\n");
			return false;
		}
		sub->SetUserName("4EEB86544CA6F15C44F63118F55224CC"); // Set to YOUR TOKEN!!!
		// MDLEID_MKTPRO (7) 应该比 MDLEID_BINARY (1) 快
		sub->SetMessageEncoding(MDLEID_MKTPRO);
		//sub->SetMessageEncoding(MDLEID_BINARY);
		printf("Heartbeat interval is %d, heartbeat timeout is %d\n", sub->GetHeartbeatInterval(), sub->GetHeartbeatTimeout());
		
		// connect to server
		std::string err = sub->Connect();
		if (err.empty()) {
			printf("Connect to server successfully.\n");
			return true;
		}
		printf("Connect to server failed: %s.\n", err.c_str());
		return false; 
	}
 
	void Close() {
		// 关闭文件
		if (m_file_szl2_order.is_open()) {
			m_file_szl2_order.close();
		}
		if (m_file_szl2_trans.is_open()) {
			m_file_szl2_trans.close();
		}
		if (m_file_shl2_trans_order.is_open()) {
			m_file_shl2_trans_order.close();
		}
		if (m_file_szl2_tick.is_open()) {
			m_file_szl2_tick.close();
		}
		if (m_file_shl2_tick.is_open()) {
			m_file_shl2_tick.close();
		}
		// 销毁互斥锁
		pthread_mutex_destroy(&m_lock_szl2_order);
		pthread_mutex_destroy(&m_lock_szl2_trans);
		pthread_mutex_destroy(&m_lock_shl2_trans_order);
		pthread_mutex_destroy(&m_lock_szl2_tick);
		pthread_mutex_destroy(&m_lock_shl2_tick);
	}

	// handle network failure
	virtual void OnMDLAPIMessage(const MDLMessage* msg) {
		MDLMessageHead* head = msg->GetHead();
		if (head->MessageID == mdl_api_msg::ConnectingEvent::MessageID) {
			mdl_api_msg::ConnectingEvent* resp = (mdl_api_msg::ConnectingEvent*)msg->GetBody();
			printf("Connect to %s ...\n", resp->Address.c_str());
		}
		else if (head->MessageID == mdl_api_msg::ConnectErrorEvent::MessageID) {
			mdl_api_msg::ConnectErrorEvent* resp = (mdl_api_msg::ConnectErrorEvent*)msg->GetBody();
			printf("Connect to %s failed %s.\n", resp->Address.c_str(), resp->ErrorMessage.c_str());
		}
		else if (head->MessageID == mdl_api_msg::DisconnectedEvent::MessageID) {
			mdl_api_msg::DisconnectedEvent* resp = (mdl_api_msg::DisconnectedEvent*)msg->GetBody();
			printf("Disconnected from %s: %s.\n", resp->Address.c_str(), resp->ErrorMessage.c_str());
		}
	}

	// handle server response
	virtual void OnMDLSysMessage(const MDLMessage* msg) {
		MDLMessageHead* head = msg->GetHead();
		if (head->MessageID == mdl_sys_msg::LogonResponse::MessageID) {
			mdl_sys_msg::LogonResponse* resp = (mdl_sys_msg::LogonResponse*)msg->GetBody();
			if (resp->ReturnCode != MDLEC_OK) {
				printf("Logon failed: return code %d.\n", resp->ReturnCode);
			}
			for (uint32_t i = 0; i < resp->Services.Length; ++i) {
				for (uint32_t j = 0; j < resp->Services[i]->Messages.Length; ++j) {
					if (resp->Services[i]->Messages[j]->MessageStatus != MDLEC_OK) {
						printf("The server doesn't publish message (service id %d message id %d)\n", 
							resp->Services[i]->ServiceID,
							resp->Services[i]->Messages[j]->MessageID);
					}
				}
			}
		}
	}

	// print shfe future message
	virtual void OnMDLSHFEMessage(const MDLMessage* msg) {
		MDLMessageHead* head = msg->GetHead();
		if (head->MessageID == mdl_shfe_msg::CTPFuture::MessageID) {
			mdl_shfe_msg::CTPFuture* body = (mdl_shfe_msg::CTPFuture*)msg->GetBody();
			PrintFutureMessage(body);
		}
	}

	// print czce future message
	virtual void OnMDLCZCEMessage(const MDLMessage* msg) {
		MDLMessageHead* head = msg->GetHead();
		if (head->MessageID == mdl_czce_msg::CTPFuture::MessageID) {
			mdl_czce_msg::CTPFuture* body = (mdl_czce_msg::CTPFuture*)msg->GetBody();
			PrintFutureMessage(body);
		}
	}

	// print cffex future message
	virtual void OnMDLCFFEXMessage(const MDLMessage* msg) {
		MDLMessageHead* head = msg->GetHead();
		if (head->MessageID == mdl_cffex_msg::CTPFuture::MessageID) {
			mdl_cffex_msg::CTPFuture* body = (mdl_cffex_msg::CTPFuture*)msg->GetBody();
			PrintFutureMessage(body);
		}
	}

	// print dce future message
	virtual void OnMDLDCEMessage(const MDLMessage* msg) {
		MDLMessageHead* head = msg->GetHead();
		if (head->MessageID == mdl_dce_msg::CTPFuture::MessageID) {
			mdl_dce_msg::CTPFuture* body = (mdl_dce_msg::CTPFuture*)msg->GetBody();
			PrintFutureMessage(body);
		}
	}

	// print shanghai level1 message
	virtual void OnMDLSHL1Message(const MDLMessage* msg) {
		MDLMessageHead* head = msg->GetHead();
		if (head->MessageID == mdl_shl1_msg::Equity2::MessageID) {
			mdl_shl1_msg::Equity2* body = (mdl_shl1_msg::Equity2*)msg->GetBody(); 
			PrintNewStockMessage(body);
		}
		else if (head->MessageID == mdl_shl1_msg::Indexes::MessageID) {
			mdl_shl1_msg::Indexes* body = (mdl_shl1_msg::Indexes*)msg->GetBody(); 
			PrintIndexMessage(body); 
		}
		else if (head->MessageID == mdl_shl1_msg::Bond2::MessageID) {
			mdl_shl1_msg::Bond2* body = (mdl_shl1_msg::Bond2*)msg->GetBody(); 
			PrintNewStockMessage(body);
		}
		else if (head->MessageID == mdl_shl1_msg::Fund2::MessageID) {
			mdl_shl1_msg::Fund2* body = (mdl_shl1_msg::Fund2*)msg->GetBody(); 
			PrintNewStockMessage(body);
		}
	}

	// print shenzhen level1 message
	virtual void OnMDLSZL1Message(const MDLMessage* msg) {
		MDLMessageHead* head = msg->GetHead();
		if (head->MessageID == mdl_szl1_msg::Index2::MessageID) {
			mdl_szl1_msg::Index2* body = (mdl_szl1_msg::Index2*)msg->GetBody();
			PrintIndexMessage(body); 
		}
		else if (head->MessageID == mdl_szl1_msg::SZL1Stock::MessageID) {
			mdl_szl1_msg::SZL1Stock* body = (mdl_szl1_msg::SZL1Stock*)msg->GetBody();
			PrintStockMessage(body); 
		}
	}

	template <class T> 
	void PrintIndexMessage(const T* msgBody) {
		printf("%d:%02d:%02d %s %s HighIndex:%s LowIndex:%s LastIndex:%s\n",
			msgBody->UpdateTime.GetHour(), msgBody->UpdateTime.GetMinute(), msgBody->UpdateTime.GetSecond(),
			msgBody->IndexID.std_str().c_str(), msgBody->IndexName.std_str().c_str(), 
			DoubleToString(msgBody->HighIndex).c_str(), 
			DoubleToString(msgBody->LowIndex).c_str(), 
			DoubleToString(msgBody->LastIndex).c_str());
	}
	template <class T> 
	void PrintNewStockMessage(const T* msgBody) {
		printf("%d:%02d:%02d %s %s HighPrice:%s LowPrice:%s LastPrice:%s\n",
			msgBody->UpdateTime.GetHour(), msgBody->UpdateTime.GetMinute(), msgBody->UpdateTime.GetSecond(),
			msgBody->SecurityID.std_str().c_str(), msgBody->SecurityName.std_str().c_str(), 
			DoubleToString(msgBody->HighPrice).c_str(), 
			DoubleToString(msgBody->LowPrice).c_str(), 
			DoubleToString(msgBody->LastPrice).c_str());
	}
	template <class T> 
	void PrintStockMessage(const T* msgBody) {
		printf("%d:%02d:%02d %s %s HighPrice:%s LowPrice:%s LastPrice:%s\n",
			msgBody->UpdateTime.GetHour(), msgBody->UpdateTime.GetMinute(), msgBody->UpdateTime.GetSecond(),
			msgBody->SecurityID.std_str().c_str(), msgBody->SecurityName.std_str().c_str(), 
			FloatToString(msgBody->HighPrice).c_str(), 
			FloatToString(msgBody->LowPrice).c_str(), 
			FloatToString(msgBody->LastPrice).c_str());
	}

	template <class T> 
	void PrintFutureMessage(const T* msgBody) {
		printf("%d:%02d:%02d %s HighPrice:%s LowPrice:%s LastPrice:%s\n",
			msgBody->UpdateTime.GetHour(), msgBody->UpdateTime.GetMinute(), msgBody->UpdateTime.GetSecond(),
			msgBody->InstruID.std_str().c_str(),
			DoubleToString(msgBody->HighPrice).c_str(),
			DoubleToString(msgBody->LowPrice).c_str(),
			DoubleToString(msgBody->LastPrice).c_str());
	}

	template <class T>
	std::string DoubleToString(const T& mdlDouble) {
		if (mdlDouble.IsNull()) {
			return std::string("null");
		}
		char strBuf[100];
#if defined(__linux__)
        sprintf(strBuf, "%.2f", mdlDouble.GetDouble());
#else
		sprintf_s(strBuf, sizeof(strBuf), "%.2f", mdlDouble.GetDouble());
#endif
		return std::string(strBuf);
	}

	template <class T>
	std::string FloatToString(const T& mdlFloat) {
		if (mdlFloat.IsNull()) {
			return std::string("null");
		}
		char strBuf[100];
#if defined(__linux__)
        sprintf(strBuf, "%.2f", mdlFloat.GetFloat());
#else
		sprintf_s(strBuf, sizeof(strBuf), "%.2f", mdlFloat.GetFloat());
#endif
		return std::string(strBuf);
	}

	// print hkex message
	virtual void OnMDLHKExMessage(const MDLMessage* msg) {
		MDLMessageHead* head = msg->GetHead();
		if (head->MessageID == mdl_hkex_msg::OMDMarketData::MessageID) {
			mdl_hkex_msg::OMDMarketData* body = (mdl_hkex_msg::OMDMarketData*)msg->GetBody();
			printf("[%d:%02d:%02d.%d] %s ",
				body->TradTime.GetHour(), body->TradTime.GetMinute(), body->TradTime.GetSecond(), body->TradTime.GetMilliSec(),
				body->TickerSymbol.c_str());
			if (body->LastPrice.IsNull()) {
				printf("LastPrice:null");
			}
			else {
				printf("LastPrice:%.2f", body->LastPrice.GetFloat());
			}	
			if (body->ChangePct.IsNull()) {
				printf("(),");
			}
			else {
				printf("(%.3f%%),", body->ChangePct.GetFloat() * 100);
			}	
			printf("Volume:%.3fM,", (float)body->Quantity / 1000000.0);
			if (body->Turnover.IsNull()) {
				printf("Turnover:null\n");
			}
			else {
				printf("Turnover:%.3fM\n", body->Turnover.GetDouble() / 1000000.0);
			}	

			if (body->AskBook.Length > 0) {
				if (body->AskBook[0]->Price.IsNull()) {
					printf("	Ask1:null,0,");
				}
				else {
					printf("	Ask1:%.3f,%ld,", body->AskBook[0]->Price.GetFloat(), body->AskBook[0]->Volume);
				}
			}
			if (body->BidBook.Length > 0) {
				if (body->BidBook[0]->Price.IsNull()) {
					printf("	Bid1:null,0");
				}
				else {
					printf("	Bid1:%.3f,%ld", body->BidBook[0]->Price.GetFloat(), body->BidBook[0]->Volume);
				}
			} 
			printf("\n");
		}
	}
	// handle shenzhen level2 message
	void OnMDLSZL2Message(const MDLMessage* msg) {
		struct timeval tv;
		gettimeofday(&tv, NULL);
		struct tm timeinfo;
		localtime_r(&tv.tv_sec, &timeinfo);
		long long recvtime = tv.tv_sec * 1000000 + tv.tv_usec;
		
		MDLMessageHead* head = msg->GetHead();
		if (head->MessageID == mdl_szl2_msg::Snapshot300111_v2::MessageID) {  // 6.28 市场行情
			mdl_szl2_msg::Snapshot300111_v2 * resp = (mdl_szl2_msg::Snapshot300111_v2*)msg->GetBody();
			struct tm tm1;
			tm1.tm_year = timeinfo.tm_year;
			tm1.tm_mon = timeinfo.tm_mon;
			tm1.tm_mday = timeinfo.tm_mday;
			tm1.tm_hour = resp->UpdateTime.GetHour();
			tm1.tm_min = resp->UpdateTime.GetMinute();
			tm1.tm_sec = resp->UpdateTime.GetSecond();
			tm1.tm_isdst = 0;
			long long ticktime = mktime(&tm1) * 1000000 + resp->UpdateTime.GetMilliSec() * 1000;

			LYPROTO::QUOTA::MarketData o;

			o.set_category("S");   // 股票、基金
			o.set_stkid(resp->SecurityID.std_str().c_str());

			// printf("[%d:%02d:%02d] %s %s Hi:%.6f Lo:%.6f Last:%.6f Vol:%ld Tun:%.4f\n",
			// 	resp->UpdateTime.GetHour(), resp->UpdateTime.GetMinute(), resp->UpdateTime.GetSecond(),
			// 	resp->SecurityID.std_str().c_str(), 
			// 	resp->TradingPhaseCode.c_str(),
			// 	resp->HighPrice.GetDouble(), resp->LowPrice.GetDouble(), resp->LastPrice.GetDouble(),
			// 	resp->Volume,
			// 	resp->Turnover.GetDouble());

			// for (size_t i = 0; i < 10; ++i) {
			// 	double askPrice = 0.0;
			// 	int64_t askVolumn = 0.0;
			// 	double bidPrice = 0.0;
			// 	int64_t bidVolumn = 0.0;
			// 	if (i < resp->AskPriceLevel.Length) {
			// 		askPrice = resp->AskPriceLevel[i]->Price.GetDouble();
			// 		askVolumn = resp->AskPriceLevel[i]->Volume;
			// 	}
			// 	if (i < resp->BidPriceLevel.Length) {
			// 		bidPrice = resp->BidPriceLevel[i]->Price.GetDouble();
			// 		bidVolumn = resp->BidPriceLevel[i]->Volume;
			// 	}
			// 	printf("%.6f %ld\t\t%.6f %ld\n", askPrice, askVolumn, bidPrice, bidVolumn);
			// }
			(void)pthread_mutex_lock(&m_lock_szl2_tick);
			m_file_szl2_tick << fmt::format("{:04d}-{:02d}-{:02d} {:02d}:{:02d}:{:02d}.{:06d},{:04d}-{:02d}-{:02d} {:02d}:{:02d}:{:02d}.{:03d},"
				"{},{},{},{},"
				"{},{},{},{},{},"
				"{},{},{},{},{},"
				"{},{},{},{},{},{},"
				"{},{},{},{},"
				"{},{},{}",
				timeinfo.tm_year + 1900, timeinfo.tm_mon + 1, timeinfo.tm_mday, timeinfo.tm_hour, timeinfo.tm_min, timeinfo.tm_sec, (int)tv.tv_usec,
				tm1.tm_year + 1900, tm1.tm_mon + 1, tm1.tm_mday, tm1.tm_hour, tm1.tm_min, tm1.tm_sec, resp->UpdateTime.GetMilliSec(),
				resp->ChannelNo, resp->MDStreamID.std_str().c_str(), resp->SecurityID.std_str().c_str(), resp->SecurityIDSource.std_str().c_str(),
				resp->TradingPhaseCode.std_str().c_str(), resp->PreCloPrice.GetDouble(), resp->TurnNum, resp->Volume, resp->Turnover.GetDouble(),
				resp->LastPrice.GetDouble(), resp->OpenPrice.GetDouble(), resp->HighPrice.GetDouble(), resp->LowPrice.GetDouble(), resp->DifPrice1.GetDouble(),
				resp->DifPrice2.GetDouble(), resp->PE1.GetDouble(), resp->PE2.GetDouble(), resp->PreCloseIOPV.GetDouble(), resp->IOPV.GetDouble(), resp->TotalOfferQty,
				resp->WeightedAvgOfferPx.GetDouble(), resp->TotalBidQty, resp->WeightedAvgBidPx.GetDouble(), resp->HighLimitPrice.GetDouble(),
				resp->LowLimitPrice.GetDouble(), resp->OpenInt, resp->OptPremiumRatio.GetDouble());
				for (unsigned int i = 0; i < 10; ++i) {
					if (i < resp->BidPriceLevel.Length) {
						m_file_szl2_tick << fmt::format(",{},{},{}", resp->BidPriceLevel[i]->Price.GetDouble(), resp->BidPriceLevel[i]->Volume,resp->BidPriceLevel[i]->NumOrders);
					}
					else {
						m_file_szl2_tick << ",,,";
					}
					if (i < resp->AskPriceLevel.Length) {
						m_file_szl2_tick << fmt::format(",{},{},{}", resp->AskPriceLevel[i]->Price.GetDouble(), resp->AskPriceLevel[i]->Volume,resp->AskPriceLevel[i]->NumOrders);
					}
					else {
						m_file_szl2_tick << ",,,";
					}
				}
				m_file_szl2_tick << "," << recvtime - ticktime << std::endl;
			(void)pthread_mutex_unlock(&m_lock_szl2_tick);
		}
		else if (head->MessageID == mdl_szl2_msg::Order300192_v2::MessageID) {  // 6.33 逐笔委托
			mdl_szl2_msg::Order300192_v2 * resp = (mdl_szl2_msg::Order300192_v2*)msg->GetBody();
			struct tm tm1;
			tm1.tm_year = timeinfo.tm_year;
			tm1.tm_mon = timeinfo.tm_mon;
			tm1.tm_mday = timeinfo.tm_mday;
			tm1.tm_hour = resp->TransactTime.GetHour();
			tm1.tm_min = resp->TransactTime.GetMinute();
			tm1.tm_sec = resp->TransactTime.GetSecond();
			tm1.tm_isdst = 0;
			long long ticktime = mktime(&tm1) * 1000000 + resp->TransactTime.GetMilliSec() * 1000;

			(void)pthread_mutex_lock(&m_lock_szl2_order);
			m_file_szl2_order << fmt::format("{:04d}-{:02d}-{:02d} {:02d}:{:02d}:{:02d}.{:06d},{},{},{},{},{},{},{},{},{:02d}:{:02d}:{:02d}.{:03d},{},{}", 
				timeinfo.tm_year + 1900, timeinfo.tm_mon + 1, timeinfo.tm_mday,
                timeinfo.tm_hour, timeinfo.tm_min, timeinfo.tm_sec, (int)tv.tv_usec,
				resp->ChannelNo, resp->ApplSeqNum, resp->MDStreamID.std_str().c_str(),
				resp->SecurityID.std_str().c_str(), resp->SecurityIDSource.std_str().c_str(), resp->Price.GetDouble(), resp->OrderQty, resp->Side,
				resp->TransactTime.GetHour(), resp->TransactTime.GetMinute(), resp->TransactTime.GetSecond(), resp->TransactTime.GetMilliSec(), resp->OrdType, recvtime - ticktime) << std::endl;
			(void)pthread_mutex_unlock(&m_lock_szl2_order);
		}
		else if (head->MessageID == mdl_szl2_msg::Transaction300191_v2::MessageID) {  // 6.36 逐笔成交
			mdl_szl2_msg::Transaction300191_v2 * resp = (mdl_szl2_msg::Transaction300191_v2*)msg->GetBody();
			struct tm tm1;
			tm1.tm_year = timeinfo.tm_year;
			tm1.tm_mon = timeinfo.tm_mon;
			tm1.tm_mday = timeinfo.tm_mday;
			tm1.tm_hour = resp->TransactTime.GetHour();
			tm1.tm_min = resp->TransactTime.GetMinute();
			tm1.tm_sec = resp->TransactTime.GetSecond();
			tm1.tm_isdst = 0;
			long long ticktime = mktime(&tm1) * 1000000 + resp->TransactTime.GetMilliSec() * 1000;

			(void)pthread_mutex_lock(&m_lock_szl2_order);
			m_file_szl2_trans << fmt::format("{:04d}-{:02d}-{:02d} {:02d}:{:02d}:{:02d}.{:06d},{},{},{},{},{},{},{},{},{},{},{:02d}:{:02d}:{:02d}.{:03d},{}",
				timeinfo.tm_year + 1900, timeinfo.tm_mon + 1, timeinfo.tm_mday,
                timeinfo.tm_hour, timeinfo.tm_min, timeinfo.tm_sec, (int)tv.tv_usec,
				resp->ChannelNo, resp->ApplSeqNum, resp->MDStreamID.std_str().c_str(),
				resp->BidApplSeqNum, resp->OfferApplSeqNum, resp->SecurityID.std_str().c_str(), resp->SecurityIDSource.std_str().c_str(), resp->LastPx.GetDouble(), resp->LastQty, resp->ExecType,
				resp->TransactTime.GetHour(), resp->TransactTime.GetMinute(), resp->TransactTime.GetSecond(), resp->TransactTime.GetMilliSec(), recvtime - ticktime) << std::endl;
			(void)pthread_mutex_unlock(&m_lock_szl2_order);
		}
		else if (head->MessageID == mdl_szl2_msg::Snapshot309011_v2::MessageID) {  // 6.29 指数行情
			return;  // 暂不处理
			mdl_szl2_msg::Snapshot309011_v2 * resp = (mdl_szl2_msg::Snapshot309011_v2*)msg->GetBody();
			printf("[%d:%02d:%02d.%03d] SZL2 Index %s %s\n",
				resp->UpdateTime.GetHour(), resp->UpdateTime.GetMinute(), resp->UpdateTime.GetSecond(), resp->UpdateTime.GetMilliSec(),
				resp->SecurityID.std_str().c_str(), 
				resp->TradingPhaseCode.c_str());
		}
	}



	// handle shanghai level2 message
	void OnMDLSHL2Message(const MDLMessage* msg) {
		struct timeval tv;
		gettimeofday(&tv, NULL);
		struct tm timeinfo;
		localtime_r(&tv.tv_sec, &timeinfo);
		long long recvtime = tv.tv_sec * 1000000 + tv.tv_usec;

		if (msg->GetHead()->MessageID == mdl_shl2_msg::SHL2Index::MessageID) {  // 4.6 指数行情
			mdl_shl2_msg::SHL2Index * resp = (mdl_shl2_msg::SHL2Index*)msg->GetBody();
			printf("#%ld [%d:%02d:%02d] %s Hi:%.2f Lo:%.2f Last:%.2f\n",
				msg->GetHead()->SequenceID,
				resp->UpdateTime.GetHour(), resp->UpdateTime.GetMinute(), resp->UpdateTime.GetSecond(),
				resp->SecurityID.std_str().c_str(),  
				resp->HighIndex.GetDouble(), resp->LowIndex.GetDouble(), resp->LastIndex.GetDouble());
		}	
		else if (msg->GetHead()->MessageID == mdl_shl2_msg::SHL2MarketData::MessageID) {  // 4.4 个股行情
			mdl_shl2_msg::SHL2MarketData * resp = (mdl_shl2_msg::SHL2MarketData*)msg->GetBody();
			struct tm tm1;
			tm1.tm_year = timeinfo.tm_year;
			tm1.tm_mon = timeinfo.tm_mon;
			tm1.tm_mday = timeinfo.tm_mday;
			tm1.tm_hour = resp->UpdateTime.GetHour();
			tm1.tm_min = resp->UpdateTime.GetMinute();
			tm1.tm_sec = resp->UpdateTime.GetSecond();
			tm1.tm_isdst = 0;
			long long ticktime = mktime(&tm1) * 1000000 + resp->UpdateTime.GetMilliSec() * 1000;

 			// printf("#%ld [%d:%02d:%02d] %s Hi:%.2f Lo:%.2f Last:%.2f\n",
			// 	msg->GetHead()->SequenceID,
			// 	resp->UpdateTime.GetHour(), resp->UpdateTime.GetMinute(), resp->UpdateTime.GetSecond(),
			// 	resp->SecurityID.std_str().c_str(), 
			// 	resp->HighPrice.GetFloat(),	resp->LowPrice.GetFloat(), resp->LastPrice.GetFloat());
			(void)pthread_mutex_lock(&m_lock_shl2_tick);
			m_file_shl2_tick << fmt::format("{:04d}-{:02d}-{:02d} {:02d}:{:02d}:{:02d}.{:06d},{:04d}-{:02d}-{:02d} {:02d}:{:02d}:{:02d}.{:03d},"
				"{},{},{},{},{},"
				"{},{},{},{},{},"
				"{},{},{},{},{},"
				"{},{},{},{},{},"
				"{},{},{},{},{},"
				"{},{},{},{},{},{},"
				"{},{},{},{},{},{},{},"
				"{},{},{}",
				timeinfo.tm_year + 1900, timeinfo.tm_mon + 1, timeinfo.tm_mday, timeinfo.tm_hour, timeinfo.tm_min, timeinfo.tm_sec, (int)tv.tv_usec,
				tm1.tm_year + 1900, tm1.tm_mon + 1, tm1.tm_mday, tm1.tm_hour, tm1.tm_min, tm1.tm_sec, resp->UpdateTime.GetMilliSec(),
				resp->SecurityID.std_str().c_str(), resp->ImageStatus, resp->PreCloPrice.GetFloat(), resp->OpenPrice.GetFloat(), resp->HighPrice.GetFloat(), 
				resp->LowPrice.GetFloat(), resp->LastPrice.GetFloat(), resp->ClosePrice.GetFloat(), resp->InstruStatus.c_str(), resp->TradNumber, 
				resp->TradVolume.GetDouble(), resp->Turnover.GetDouble(), resp->TotalBidVol.GetDouble(), resp->WAvgBidPri.GetFloat(), resp->AltWAvgBidPri.GetFloat(),
				resp->TotalAskVol.GetDouble(), resp->WAvgAskPri.GetFloat(), resp->AltWAvgAskPri.GetFloat(), resp->EtfBuyNumber, resp->EtfBuyVolume.GetDouble(),
				resp->EtfBuyMoney.GetDouble(), resp->EtfSellNumber, resp->EtfSellVolume.GetDouble(), resp->ETFSellMoney.GetDouble(), resp->YieldToMatu.GetFloat(),
				resp->TotWarExNum.GetDouble(), resp->WarLowerPri.GetDouble(), resp->WarUpperPri.GetDouble(), resp->WiDBuyNum, resp->WiDBuyVol.GetDouble(), resp->WiDBuyMon.GetDouble(),
				resp->WiDSellNum, resp->WiDSellVol.GetDouble(), resp->WiDSellMon.GetDouble(), resp->TotBidNum, resp->TotSellNum, resp->MaxBidDur, resp->MaxSellDur,
				resp->BidNum, resp->SellNum, resp->IOPV.GetFloat());
				for (unsigned int i = 0; i < 10; ++i) {
					if (i < resp->BidLevels.Length) {
						m_file_shl2_tick << fmt::format(",{},{},{}", resp->BidLevels[i]->OrderPrice.GetFloat(), resp->BidLevels[i]->OrderVol.GetDouble(), resp->BidLevels[i]->OrderNum);
					}
					else {
						m_file_shl2_tick << ",,,";
					}
					if (i < resp->SellLevels.Length) {
						m_file_shl2_tick << fmt::format(",{},{},{}", resp->SellLevels[i]->OrderPrice.GetFloat(), resp->SellLevels[i]->OrderVol.GetDouble(), resp->SellLevels[i]->OrderNum);
					}
					else {
						m_file_shl2_tick << ",,,";
					}
				}
				m_file_shl2_tick << "," << recvtime - ticktime << std::endl;
			(void)pthread_mutex_unlock(&m_lock_shl2_tick);
		}
		else if (msg->GetHead()->MessageID == mdl_shl2_msg::NGTSTick::MessageID) {  // 4.24 竞价逐笔合并行情
			mdl_shl2_msg::NGTSTick * resp = (mdl_shl2_msg::NGTSTick*)msg->GetBody();
			struct tm tm1;
			tm1.tm_year = timeinfo.tm_year;
			tm1.tm_mon = timeinfo.tm_mon;
			tm1.tm_mday = timeinfo.tm_mday;
			tm1.tm_hour = resp->TickTime.GetHour();
			tm1.tm_min = resp->TickTime.GetMinute();
			tm1.tm_sec = resp->TickTime.GetSecond();
			tm1.tm_isdst = 0;
			long long ticktime = mktime(&tm1) * 1000000 + resp->TickTime.GetMilliSec() * 1000;
			(void)pthread_mutex_lock(&m_lock_shl2_trans_order);
			m_file_shl2_trans_order << fmt::format("{:04d}-{:02d}-{:02d} {:02d}:{:02d}:{:02d}.{:06d},{},{},{},{:02d}:{:02d}:{:02d}.{:03d},{},{},{},{},{},{},{},{}", 
				timeinfo.tm_year + 1900, timeinfo.tm_mon + 1, timeinfo.tm_mday,
                timeinfo.tm_hour, timeinfo.tm_min, timeinfo.tm_sec, (int)tv.tv_usec,
				resp->BizIndex, resp->Channel, resp->SecurityID.std_str().c_str(),
				resp->TickTime.GetHour(), resp->TickTime.GetMinute(), resp->TickTime.GetSecond(), resp->TickTime.GetMilliSec(),
				resp->Type.c_str(), resp->BuyOrderNO, resp->SellOrderNO, resp->Price.GetFloat(), resp->Qty, resp->TradeMoney.GetDouble(), resp->TickBSFlag.c_str(), recvtime - ticktime) << std::endl;
			(void)pthread_mutex_unlock(&m_lock_shl2_trans_order);
		}
		else if (msg->GetHead()->MessageID == mdl_shl2_msg::SHL2MarketOverview::MessageID) {
			// mdl_shl2_msg::SHL2MarketOverview * resp = (mdl_shl2_msg::SHL2MarketOverview*)msg->GetBody();
		}
 		else if (msg->GetHead()->MessageID == mdl_shl2_msg::SHL2Transaction::MessageID) {
			// mdl_shl2_msg::SHL2Transaction * resp = (mdl_shl2_msg::SHL2Transaction*)msg->GetBody();
		}
 		else if (msg->GetHead()->MessageID == mdl_shl2_msg::SHL2VirtualAuctionPrice::MessageID) {
			// mdl_shl2_msg::SHL2VirtualAuctionPrice * resp = (mdl_shl2_msg::SHL2VirtualAuctionPrice*)msg->GetBody();
		}
		else if (msg->GetHead()->MessageID == mdl_shl2_msg::OPTLevel1::MessageID) {
			mdl_shl2_msg::OPTLevel1 * resp = (mdl_shl2_msg::OPTLevel1*)msg->GetBody();
 			printf("#%ld [%d:%02d:%02d] %s Hi:%.2f Lo:%.2f Last:%.2f\n",
				msg->GetHead()->SequenceID,
				resp->UpdateTime.GetHour(), resp->UpdateTime.GetMinute(), resp->UpdateTime.GetSecond(),
				resp->SecurityID.std_str().c_str(), 
				resp->HighPx.GetDouble(),	resp->LowPx.GetDouble(), resp->LastPx.GetDouble());
		}
	}

	virtual void OnMDLBARMessage(const MDLMessage* msg) {
		if (msg->GetHead()->MessageID == mdl_bar_msg::XSHGStockMinuteBar::MessageID) {
			mdl_bar_msg::XSHGStockMinuteBar * resp = (mdl_bar_msg::XSHGStockMinuteBar*)msg->GetBody();
 			printf("#%ld [%d:%02d:%02d] %s Hi:%.2f Lo:%.2f Last:%.2f\n",
				msg->GetHead()->SequenceID,
				resp->BarTime.GetHour(), resp->BarTime.GetMinute(), resp->BarTime.GetSecond(),
				resp->SecurityID.std_str().c_str(), 
				resp->HighPrice.GetDouble(), resp->LowPrice.GetDouble(), resp->LowPrice.GetDouble());
		}
		else if (msg->GetHead()->MessageID == mdl_bar_msg::XSHGCapitalFlow::MessageID) {
			mdl_bar_msg::XSHGCapitalFlow * resp = (mdl_bar_msg::XSHGCapitalFlow*)msg->GetBody();
 			printf("#%ld [%d:%02d:%02d] %s %s Px:%.2f Vol:%ld NetIn:%.2f\n",
				msg->GetHead()->SequenceID,
				resp->TradeTime.GetHour(), resp->TradeTime.GetMinute(), resp->TradeTime.GetSecond(),
				resp->SecurityID.std_str().c_str(), 
				resp->BSFlag == 1 ? "B" : (resp->BSFlag == 2 ? "S" : "NA"),
				resp->Price.GetDouble(), resp->Volume, resp->NetCapitalInflow.GetDouble());
		}
		else if (msg->GetHead()->MessageID == mdl_bar_msg::IndustryCapitalFlow::MessageID) {
			mdl_bar_msg::IndustryCapitalFlow * resp = (mdl_bar_msg::IndustryCapitalFlow*)msg->GetBody();
 			printf("#%ld [%d:%02d:%02d] %s %s In:%.2f OutIn:%.2f NetIn:%.2f\n",
				msg->GetHead()->SequenceID,
				resp->TradeTime.GetHour(), resp->TradeTime.GetMinute(), resp->TradeTime.GetSecond(),
				resp->SecurityID.std_str().c_str(), 
				resp->SecurityName.std_str().c_str(),  
				resp->CapitalInFlow.GetDouble(),
				resp->CapitalOutFlow.GetDouble(),
				resp->NetCapitalInflow.GetDouble());
		}
	}

};

///////////////////////////////////////////////////////////////////////////////////////////////////////////

// Define program options structure
struct ProgramOptions {
    int data_type;
    const char* filep;
    bool help;
};

// Parse command line arguments
static ProgramOptions
ParseCommandLine(int argc, char* argv[]) {
    ProgramOptions options = {
        DATA_TRANSORD,  // default to transaction and order data (z)
        "dy_",          // default file prefix
        false           // don't show help by default
    };

    static struct option long_options[] = {
        {"filep", required_argument, 0, 'f'},
        {"data",  required_argument, 0, 'd'},
        {"help",  no_argument,       0, 'h'},
        {0, 0, 0, 0}
    };

    int opt;
    int option_index = 0;

    while ((opt = getopt_long(argc, argv, "f:d:h", long_options, &option_index)) != -1) {
        switch (opt) {
            case 'f':
                options.filep = optarg;
                break;
            case 'd':
                if (optarg[0] == 'm') {
                    options.data_type = DATA_TICK;  // m for market data (tick)
                } else if (optarg[0] == 'z') {
                    options.data_type = DATA_TRANSORD;  // z for transaction and order
                } else {
                    fprintf(stderr, "Invalid data type: %s. Using default 'z'.\n", optarg);
                }
                break;
            case 'h':
                options.help = true;
                break;
            default:
                break;
        }
    }

    return options;
}

// Display help information
static void
PrintUsage(const char* program_name) {
    printf("Usage: %s [OPTIONS]\n", program_name);
    printf("Options:\n");
    printf("  -f, --filep=FILEP   File prefix for saving data (default: dy_)\n");
    printf("  -d, --data=TYPE     Data type to download: m (tick) or z (transaction) (default: z)\n");
    printf("  -h, --help          Display this help and exit\n");
    printf("\nNote: This program connects to both Shenzhen and Shanghai markets simultaneously.\n");
}

// Global variables for signal handler
static MyMessageHandler* g_szHandler = nullptr;
static MyMessageHandler* g_shHandler = nullptr;
static bool g_running = true;

// Signal handler function
static void signal_handler(int signum) {
    if (signum == SIGTERM || signum == SIGINT) {
        printf("\nReceived termination signal. Closing connections...\n");
        g_running = false;
        
        // Close connections
        if (g_szHandler != nullptr) {
            g_szHandler->Close();
            printf("Shenzhen market connection closed.\n");
        }
        
        if (g_shHandler != nullptr) {
            g_shHandler->Close();
            printf("Shanghai market connection closed.\n");
        }
        
        printf("Exiting...\n");
        exit(0);
    }
}

int main(int argc, char* argv[]) { 
    // Parse command line arguments
    ProgramOptions options = ParseCommandLine(argc, argv);

    // If help requested, show usage and exit
    if (options.help) {
        PrintUsage(argv[0]);
        return 0;
    }

    // Set up signal handler
    struct sigaction sa;
    memset(&sa, 0, sizeof(sa));
    sa.sa_handler = signal_handler;
    sigaction(SIGTERM, &sa, NULL);
    sigaction(SIGINT, &sa, NULL);

    // Create handlers for both markets
    MyMessageHandler szHandler;
    MyMessageHandler shHandler;
    
    // Set global pointers for signal handler
    g_szHandler = &szHandler;
    g_shHandler = &shHandler;
    
    bool szConnected = false;
    bool shConnected = false;

    // Connect to Shanghai market
    printf("Connecting to Shanghai market...\n");
    if (shHandler.Open(MARKET_SH, options.data_type, options.filep)) {
        printf("Connected to Shanghai market server: %s\n", SERVER_SH);
        printf("Data type: %s\n", options.data_type == DATA_TICK ? "Tick data" : "Transaction and Order data");
        printf("File prefix: %s\n", options.filep);
        shConnected = true;
    } else {
        printf("Failed to connect to Shanghai market.\n");
    }

    // Connect to Shenzhen market
    printf("Connecting to Shenzhen market...\n");
    if (szHandler.Open(MARKET_SZ, options.data_type, options.filep)) {
        printf("Connected to Shenzhen market server: %s\n", SERVER_SZ);
        printf("Data type: %s\n", options.data_type == DATA_TICK ? "Tick data" : "Transaction and Order data");
        printf("File prefix: %s\n", options.filep);
        szConnected = true;
    } else {
        printf("Failed to connect to Shenzhen market.\n");
    }
    
    // Check if at least one connection was successful
    if (szConnected || shConnected) {
        printf("Receiving messages from %s%s markets.\n", 
               szConnected ? "Shenzhen" : "", 
               (szConnected && shConnected) ? " and Shanghai" : (shConnected ? "Shanghai" : ""));
        printf("Or send SIGTERM to gracefully terminate the program.\n");
        
        // Wait for user input or signal
        (void)pause();
    } else {
        printf("Failed to connect to any market.\n");
        return 1;
    }

    // Reset global pointers
    g_szHandler = nullptr;
    g_shHandler = nullptr;

    // Close connections
    if (szConnected) {
        szHandler.Close();
    }
    if (shConnected) {
        shHandler.Close();
    }
    
    return 0;
}

#if 0


char pub111_stock_buf[200000];
char pub111_fund_buf[200000];

void pub111_stock(const ::com::htsc::mdc::insight::model::MDStock& md)
{
	// md.PrintDebugString();

	const char *csid = md.htscsecurityid().c_str(); // "510050.SH"
	// FFFmd("%s %d\n", csid, md.lastpx());
	char stkid[8];
	memcpy(stkid, csid, 6);
	stkid[6] = 0;

	LYPROTO::QUOTA::MarketData o;

	if (csid[8] == 'H') o.set_exchid("1"); else o.set_exchid("0");
	o.set_category("S");
	o.set_stkid(stkid);

	char tmpbuf[32];
	
	//snprintf(tmpbuf, sizeof(tmpbuf), "%lld", md.datatimestamp()); // 20170531133903000  insight 2.x removed
	int mdtime = md.mdtime(); // 133903000
	int exchangetime = md.exchangetime(); // 0
	if (exchangetime == 0) exchangetime = mdtime;
	//long long ComplexEventStartTime = md.complexeventstarttime();
	//long long ComplexEventEndTime = md.complexeventendtime();
	
	snprintf(tmpbuf, sizeof(tmpbuf), "%d", mdtime);
	o.set_rcvsvrtime(tmpbuf);
	o.set_pubsvrtime(tmpbuf);
	o.set_exchtime(tmpbuf);


	o.set_status(md.tradingphasecode());

	o.set_preclose(md.preclosepx());
	o.set_highlimit(md.maxpx()); // xxx?
	o.set_lowlimit(md.minpx()); // xxx?


	o.set_open(md.openpx());
	o.set_high(md.highpx());
	o.set_low(md.lowpx());
	o.set_latest(md.lastpx());

	o.set_knock(md.numtrades());
	o.set_volume(md.totalvolumetrade());
	o.set_value(md.totalvaluetrade());

	for (int i = 0; i < md.buypricequeue_size(); i++) {
		o.add_bp(md.buypricequeue(i));
	}
	for (int i = 0; i < md.buyorderqtyqueue_size(); i++) {
		o.add_ba(md.buyorderqtyqueue(i));
	}
	for (int i = 0; i < md.sellpricequeue_size(); i++) {
		o.add_sp(md.sellpricequeue(i));
	}
	for (int i = 0; i < md.sellorderqtyqueue_size(); i++) {
		o.add_sa(md.sellorderqtyqueue(i));
	}

/*
	o.add_bp(md.buy1price());
	o.add_bp(md.buy2price());
	o.add_bp(md.buy3price());
	o.add_bp(md.buy4price());
	o.add_bp(md.buy5price());
	o.add_bp(md.buy6price());
	o.add_bp(md.buy7price());
	o.add_bp(md.buy8price());
	o.add_bp(md.buy9price());
	o.add_bp(md.buy10price());

	o.add_ba(md.buy1orderqty());
	o.add_ba(md.buy2orderqty());
	o.add_ba(md.buy3orderqty());
	o.add_ba(md.buy4orderqty());
	o.add_ba(md.buy5orderqty());
	o.add_ba(md.buy5orderqty());
	o.add_ba(md.buy7orderqty());
	o.add_ba(md.buy8orderqty());
	o.add_ba(md.buy9orderqty());
	o.add_ba(md.buy10orderqty());

	o.add_sp(md.sell1price());
	o.add_sp(md.sell2price());
	o.add_sp(md.sell3price());
	o.add_sp(md.sell4price());
	o.add_sp(md.sell5price());
	o.add_sp(md.sell6price());
	o.add_sp(md.sell7price());
	o.add_sp(md.sell8price());
	o.add_sp(md.sell9price());
	o.add_sp(md.sell10price());

	o.add_sa(md.sell1orderqty());
	o.add_sa(md.sell2orderqty());
	o.add_sa(md.sell3orderqty());
	o.add_sa(md.sell4orderqty());
	o.add_sa(md.sell5orderqty());
	o.add_sa(md.sell6orderqty());
	o.add_sa(md.sell7orderqty());
	o.add_sa(md.sell8orderqty());
	o.add_sa(md.sell9orderqty());
	o.add_sa(md.sell10orderqty());
*/

	o.set_totalba(md.totalbuyqty());
	o.set_totalsa(md.totalsellqty());
	o.set_weightedavgbidpx(md.weightedavgbuypx());
	o.set_weightedavgofferpx(md.weightedavgsellpx());

	o.set_iopv(md.norminalpx());

	// YieldToMaturity: 0

	time_t now = time(NULL);
	struct tm *tm = localtime(&now);
	char stm[16];
	snprintf(stm, sizeof(stm), "%02d:%02d:%02d", tm->tm_hour, tm->tm_min, tm->tm_sec);
	// o.set_pubtime(stm);   // N/A PubTime	

	int pbsize = o.ByteSize();
	int pktsize = 15 + pbsize;
	if (pktsize > sizeof(pub111_stock_buf)) return;
	//char *pb = (char *)malloc(pktsize);
	char *pb = pub111_stock_buf;
	if (pb) {
		std::lock_guard<std::mutex> lck (mtx);

		snprintf(pb, pktsize, "S%s%08d:", stkid, pbsize);
		o.SerializeToArray(pb + 15, pbsize);
		int r = zmq_send(g_zmq, pb, pktsize, 0);
		//printf("zmq_send %d\n", r);
		// free(pb);
	}
}


void pub111_index(const ::com::htsc::mdc::insight::model::MDIndex& md)
{
	const char *csid = md.htscsecurityid().c_str(); // "000833.SH", "395032.SZ"

	char stkid[8];
	memcpy(stkid, csid, 6);
	stkid[6] = 0;

	LYPROTO::QUOTA::MarketData o;

	if (csid[8] == 'H') o.set_exchid("1"); else o.set_exchid("0");
	o.set_category("I");
	o.set_stkid(stkid);

	char tmpbuf[32];
	
	//snprintf(tmpbuf, sizeof(tmpbuf), "%lld", md.datatimestamp()); // 20170531133903000  insight 2.x removed
	int mdtime = md.mdtime(); // 133903000
	int exchangetime = md.exchangetime(); // 0
	if (exchangetime == 0) exchangetime = mdtime;
	//long long ComplexEventStartTime = md.complexeventstarttime();
	//long long ComplexEventEndTime = md.complexeventendtime();
	
	snprintf(tmpbuf, sizeof(tmpbuf), "%d", mdtime);
	o.set_rcvsvrtime(tmpbuf);
	o.set_pubsvrtime(tmpbuf);
	o.set_exchtime(tmpbuf);

	o.set_high(md.highpx());
	o.set_latest(md.lastpx());
	o.set_low(md.lowpx());
	o.set_open(md.openpx());
	o.set_preclose(md.preclosepx());

	o.set_value(md.totalvaluetrade());
	o.set_volume(md.totalvolumetrade());

	// YieldToMaturity: 0

	time_t now = time(NULL);
	struct tm *tm = localtime(&now);
	char stm[16];
	snprintf(stm, sizeof(stm), "%02d:%02d:%02d", tm->tm_hour, tm->tm_min, tm->tm_sec);
	// o.set_pubtime(stm);   // N/A PubTime

	int pbsize = o.ByteSize();
	int pktsize = 15 + pbsize;
	if (pktsize > sizeof(pub111_stock_buf)) return;
	//char *pb = (char *)malloc(pktsize);
	char *pb = pub111_stock_buf;
	if (pb) {
		std::lock_guard<std::mutex> lck (mtx);

		snprintf(pb, pktsize, "I%s%08d:", stkid, pbsize);
		o.SerializeToArray(pb + 15, pbsize);
		int r = zmq_send(g_zmq, pb, pktsize, 0);
		//printf("zmq_send %d\n", r);
		// free(pb);
	}
}


void pub111_fund(const ::com::htsc::mdc::insight::model::MDFund& md)
{
	// md.PrintDebugString();

	const char *csid = md.htscsecurityid().c_str(); // "510050.SH"
	// FFFmd("%s %d\n", csid, md.lastpx());
	char stkid[8];
	memcpy(stkid, csid, 6);
	stkid[6] = 0;

/*
	if (strcmp(stkid, "510050") == 0) {
		char strnow[20];
		time_t now = time(NULL);
		strftime(strnow, sizeof(strnow), "%Y-%m-%d %H:%M:%S", localtime(&now));

		printf("fund: %s <%s> %d %lld\n", strnow, csid, md.lastpx(), md.datatimestamp());
	}
*/

	LYPROTO::QUOTA::MarketData o;


	if (csid[8] == 'H') o.set_exchid("1"); else o.set_exchid("0");
	o.set_category("S");
	o.set_stkid(stkid);

	

	char tmpbuf[32];
	
	//snprintf(tmpbuf, sizeof(tmpbuf), "%lld", md.datatimestamp()); // 20170531133903000  insight 2.x removed
	int mdtime = md.mdtime(); // 133903000
	int exchangetime = md.exchangetime(); // 0
	if (exchangetime == 0) exchangetime = mdtime;
	//long long ComplexEventStartTime = md.complexeventstarttime();
	//long long ComplexEventEndTime = md.complexeventendtime();
	
	snprintf(tmpbuf, sizeof(tmpbuf), "%d", mdtime);
	o.set_rcvsvrtime(tmpbuf);
	o.set_pubsvrtime(tmpbuf);
	o.set_exchtime(tmpbuf);


	o.set_status(md.tradingphasecode());

	o.set_preclose(md.preclosepx());
	o.set_highlimit(md.maxpx()); // xxx?
	o.set_lowlimit(md.minpx()); // xxx?


	o.set_open(md.openpx());
	o.set_high(md.highpx());
	o.set_low(md.lowpx());
	o.set_latest(md.lastpx());

	o.set_knock(md.numtrades());
	o.set_volume(md.totalvolumetrade());
	o.set_value(md.totalvaluetrade());

	for (int i = 0; i < md.buypricequeue_size(); i++) {
		o.add_bp(md.buypricequeue(i));
	}
	for (int i = 0; i < md.buyorderqtyqueue_size(); i++) {
		o.add_ba(md.buyorderqtyqueue(i));
	}
	for (int i = 0; i < md.sellpricequeue_size(); i++) {
		o.add_sp(md.sellpricequeue(i));
	}
	for (int i = 0; i < md.sellorderqtyqueue_size(); i++) {
		o.add_sa(md.sellorderqtyqueue(i));
	}

/*
	o.add_bp(md.buy1price());
	o.add_bp(md.buy2price());
	o.add_bp(md.buy3price());
	o.add_bp(md.buy4price());
	o.add_bp(md.buy5price());
	o.add_bp(md.buy6price());
	o.add_bp(md.buy7price());
	o.add_bp(md.buy8price());
	o.add_bp(md.buy9price());
	o.add_bp(md.buy10price());

	o.add_ba(md.buy1orderqty());
	o.add_ba(md.buy2orderqty());
	o.add_ba(md.buy3orderqty());
	o.add_ba(md.buy4orderqty());
	o.add_ba(md.buy5orderqty());
	o.add_ba(md.buy5orderqty());
	o.add_ba(md.buy7orderqty());
	o.add_ba(md.buy8orderqty());
	o.add_ba(md.buy9orderqty());
	o.add_ba(md.buy10orderqty());

	o.add_sp(md.sell1price());
	o.add_sp(md.sell2price());
	o.add_sp(md.sell3price());
	o.add_sp(md.sell4price());
	o.add_sp(md.sell5price());
	o.add_sp(md.sell6price());
	o.add_sp(md.sell7price());
	o.add_sp(md.sell8price());
	o.add_sp(md.sell9price());
	o.add_sp(md.sell10price());

	o.add_sa(md.sell1orderqty());
	o.add_sa(md.sell2orderqty());
	o.add_sa(md.sell3orderqty());
	o.add_sa(md.sell4orderqty());
	o.add_sa(md.sell5orderqty());
	o.add_sa(md.sell6orderqty());
	o.add_sa(md.sell7orderqty());
	o.add_sa(md.sell8orderqty());
	o.add_sa(md.sell9orderqty());
	o.add_sa(md.sell10orderqty());
*/

	o.set_totalba(md.totalbuyqty());
	o.set_totalsa(md.totalsellqty());
	o.set_weightedavgbidpx(md.weightedavgbuypx());
	o.set_weightedavgofferpx(md.weightedavgsellpx());

	o.set_iopv(md.iopv());

	// YieldToMaturity: 0

	time_t now = time(NULL);
	struct tm *tm = localtime(&now);
	char stm[16];
	snprintf(stm, sizeof(stm), "%02d:%02d:%02d", tm->tm_hour, tm->tm_min, tm->tm_sec);
	// o.set_pubtime(stm);   // N/A PubTime	

	int pbsize = o.ByteSize();
	int pktsize = 15 + pbsize;
	if (pktsize > sizeof(pub111_fund_buf)) return;
	//char *pb = (char *)malloc(pktsize);
	char *pb = pub111_fund_buf;
	if (pb) {
		std::lock_guard<std::mutex> lck (mtx);

		snprintf(pb, pktsize, "S%s%08d:", stkid, pbsize);
		o.SerializeToArray(pb + 15, pbsize);

		int r = zmq_send(g_zmq, pb, pktsize, 0);
		//printf("zmq_send %d\n", r);
		// free(pb);
	}
}


void pub111_bond(const ::com::htsc::mdc::insight::model::MDBond& md)
{
	// md.PrintDebugString();

	const char *csid = md.htscsecurityid().c_str(); // "510050.SH"
	// FFFmd("%s %d\n", csid, md.lastpx());
	char stkid[8];
	memcpy(stkid, csid, 6);
	stkid[6] = 0;

/*
	if (strcmp(stkid, "204001") == 0) {
		char strnow[20];
		time_t now = time(NULL);
		strftime(strnow, sizeof(strnow), "%Y-%m-%d %H:%M:%S", localtime(&now));

		printf("bbbbbbbbbbbbbbbbbbbbbbbbbbbbbbb: %s <%s> %d %lld\n", strnow, csid, md.lastpx(), md.datatimestamp());
	}

*/
	LYPROTO::QUOTA::MarketData o;


	if (csid[8] == 'H') o.set_exchid("1"); else o.set_exchid("0");
	o.set_category("S");
	o.set_stkid(stkid);

	char tmpbuf[32];
	
	//snprintf(tmpbuf, sizeof(tmpbuf), "%lld", md.datatimestamp()); // 20170531133903000  insight 2.x removed
	int mdtime = md.mdtime(); // 133903000
	int exchangetime = md.exchangetime(); // 0
	if (exchangetime == 0) exchangetime = mdtime;
	//long long ComplexEventStartTime = md.complexeventstarttime();
	//long long ComplexEventEndTime = md.complexeventendtime();
	
	snprintf(tmpbuf, sizeof(tmpbuf), "%d", mdtime);
	o.set_rcvsvrtime(tmpbuf);
	o.set_pubsvrtime(tmpbuf);
	o.set_exchtime(tmpbuf);


	o.set_status(md.tradingphasecode());

	o.set_preclose(md.preclosepx());
	o.set_highlimit(md.maxpx()); // xxx?
	o.set_lowlimit(md.minpx()); // xxx?


	o.set_open(md.openpx());
	o.set_high(md.highpx());
	o.set_low(md.lowpx());
	o.set_latest(md.lastpx());

	o.set_knock(md.numtrades());
	o.set_volume(md.totalvolumetrade());
	o.set_value(md.totalvaluetrade());

	for (int i = 0; i < md.buypricequeue_size(); i++) {
		o.add_bp(md.buypricequeue(i));
	}
	for (int i = 0; i < md.buyorderqtyqueue_size(); i++) {
		o.add_ba(md.buyorderqtyqueue(i));
	}
	for (int i = 0; i < md.sellpricequeue_size(); i++) {
		o.add_sp(md.sellpricequeue(i));
	}
	for (int i = 0; i < md.sellorderqtyqueue_size(); i++) {
		o.add_sa(md.sellorderqtyqueue(i));
	}

/*
	o.add_bp(md.buy1price());
	o.add_bp(md.buy2price());
	o.add_bp(md.buy3price());
	o.add_bp(md.buy4price());
	o.add_bp(md.buy5price());
	o.add_bp(md.buy6price());
	o.add_bp(md.buy7price());
	o.add_bp(md.buy8price());
	o.add_bp(md.buy9price());
	o.add_bp(md.buy10price());

	o.add_ba(md.buy1orderqty());
	o.add_ba(md.buy2orderqty());
	o.add_ba(md.buy3orderqty());
	o.add_ba(md.buy4orderqty());
	o.add_ba(md.buy5orderqty());
	o.add_ba(md.buy5orderqty());
	o.add_ba(md.buy7orderqty());
	o.add_ba(md.buy8orderqty());
	o.add_ba(md.buy9orderqty());
	o.add_ba(md.buy10orderqty());

	o.add_sp(md.sell1price());
	o.add_sp(md.sell2price());
	o.add_sp(md.sell3price());
	o.add_sp(md.sell4price());
	o.add_sp(md.sell5price());
	o.add_sp(md.sell6price());
	o.add_sp(md.sell7price());
	o.add_sp(md.sell8price());
	o.add_sp(md.sell9price());
	o.add_sp(md.sell10price());

	o.add_sa(md.sell1orderqty());
	o.add_sa(md.sell2orderqty());
	o.add_sa(md.sell3orderqty());
	o.add_sa(md.sell4orderqty());
	o.add_sa(md.sell5orderqty());
	o.add_sa(md.sell6orderqty());
	o.add_sa(md.sell7orderqty());
	o.add_sa(md.sell8orderqty());
	o.add_sa(md.sell9orderqty());
	o.add_sa(md.sell10orderqty());

*/
	o.set_totalba(md.totalbuyqty());
	o.set_totalsa(md.totalsellqty());
	o.set_weightedavgbidpx(md.weightedavgbuypx());
	o.set_weightedavgofferpx(md.weightedavgsellpx());

	// o.set_iopv(md.iopv());

	// YieldToMaturity: 0

	time_t now = time(NULL);
	struct tm *tm = localtime(&now);
	char stm[16];
	snprintf(stm, sizeof(stm), "%02d:%02d:%02d", tm->tm_hour, tm->tm_min, tm->tm_sec);
	// o.set_pubtime(stm);   // N/A PubTime

	int pbsize = o.ByteSize();
	int pktsize = 15 + pbsize;
	if (pktsize > sizeof(pub111_fund_buf)) return;
	//char *pb = (char *)malloc(pktsize);
	char *pb = pub111_fund_buf;
	if (pb) {
		std::lock_guard<std::mutex> lck (mtx);

		snprintf(pb, pktsize, "S%s%08d:", stkid, pbsize);
		o.SerializeToArray(pb + 15, pbsize);

		int r = zmq_send(g_zmq, pb, pktsize, 0);
		//printf("zmq_send %d\n", r);
		// free(pb);
	}
}


typedef struct t_mdtransaction {
	char header[16];  // "T510050"
	int MDDate;
	int MDTime;
	long long DataTimestamp;
	long long TradeIndex;
	long long TradeBuyNo;
	long long TradeSellNo;
	int TradeType;
	int TradeBSFlag;
	long long TradePrice;
	long long TradeQty;
	long long TradeMoney;
	long long NumTrades;
	char ts[16];
} mdtransaction_t;


//YZWMARK pub111_transaction
void pub111_transaction(const ::com::htsc::mdc::insight::model::MDTransaction& md)
{
	// md.PrintDebugString();

	const char *csid = md.htscsecurityid().c_str(); // "510050.SH"
	// FFFmd("%s %d\n", csid, md.lastpx());
	char stkid[8];
	memcpy(stkid, csid, 6);
	stkid[6] = 0;

#if 0
	if (strcmp(stkid, "204001") == 0) {
		char strnow[20];
		time_t now = time(NULL);
		strftime(strnow, sizeof(strnow), "%Y-%m-%d %H:%M:%S", localtime(&now));

		printf("bbbbbbbbbbbbbbbbbbbbbbbbbbbbbbb: %s <%s> %d %lld\n", strnow, csid, md.lastpx(), md.datatimestamp());
	}

#endif
	mdtransaction_t o;

	bzero(&o, sizeof(o));
	snprintf(o.header, sizeof(o.header), "T%s", stkid);
	o.MDDate = md.mddate();
	o.MDTime = md.mdtime();
	o.DataTimestamp = md.datatimestamp();
	o.TradeIndex = md.tradeindex();
	o.TradeBuyNo = md.tradebuyno();
	o.TradeSellNo = md.tradesellno();
	o.TradeType = md.tradetype();
	o.TradeBSFlag = md.tradebsflag();
	o.TradePrice = md.tradeprice();
	o.TradeQty = md.tradeqty();
	o.TradeMoney = md.trademoney();
	// o.NumTrades = md.numtrades();
	o.NumTrades = 0; // 2.1 removed this field


	time_t now = time(NULL);
	struct tm *tm = localtime(&now);
	snprintf(o.ts, sizeof(o.ts), "%02d:%02d:%02d", tm->tm_hour, tm->tm_min, tm->tm_sec);

	{
		std::lock_guard<std::mutex> lck (mtx);

		int r = zmq_send(g_zmq, &o, sizeof(o), 0);
		write(fd_transaction, &o, sizeof(o));
	}
}

#endif
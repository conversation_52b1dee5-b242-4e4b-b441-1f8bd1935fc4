// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: google/protobuf/map_lite_unittest.proto

#define INTERNAL_SUPPRESS_PROTOBUF_FIELD_DEPRECATION
#include <google/protobuf/map_lite_unittest.pb.h>

#include <algorithm>

#include <google/protobuf/stubs/common.h>
#include <google/protobuf/stubs/port.h>
#include <google/protobuf/stubs/once.h>
#include <google/protobuf/io/coded_stream.h>
#include <google/protobuf/wire_format_lite_inl.h>
#include <google/protobuf/io/zero_copy_stream_impl_lite.h>
// @@protoc_insertion_point(includes)

namespace protobuf_unittest {

void protobuf_ShutdownFile_google_2fprotobuf_2fmap_5flite_5funittest_2eproto() {
  TestMapLite_default_instance_.Shutdown();
  TestArenaMapLite_default_instance_.Shutdown();
  TestRequiredMessageMapLite_default_instance_.Shutdown();
  TestEnumMapLite_default_instance_.Shutdown();
  TestEnumMapPlusExtraLite_default_instance_.Shutdown();
  TestMessageMapLite_default_instance_.Shutdown();
  TestRequiredLite_default_instance_.Shutdown();
  ForeignMessageArenaLite_default_instance_.Shutdown();
}

void protobuf_InitDefaults_google_2fprotobuf_2fmap_5flite_5funittest_2eproto_impl() {
  GOOGLE_PROTOBUF_VERIFY_VERSION;

  ::protobuf_unittest::protobuf_InitDefaults_google_2fprotobuf_2funittest_5flite_2eproto();
  ::protobuf_unittest_no_arena::protobuf_InitDefaults_google_2fprotobuf_2funittest_5fno_5farena_5flite_2eproto();
  ::google::protobuf::internal::GetEmptyString();
  TestMapLite_default_instance_.DefaultConstruct();
  ::google::protobuf::internal::GetEmptyString();
  ::google::protobuf::internal::GetEmptyString();
  ::google::protobuf::internal::GetEmptyString();
  ::google::protobuf::internal::GetEmptyString();
  ::google::protobuf::internal::GetEmptyString();
  ::google::protobuf::internal::GetEmptyString();
  ::google::protobuf::internal::GetEmptyString();
  ::google::protobuf::internal::GetEmptyString();
  ::google::protobuf::internal::GetEmptyString();
  ::google::protobuf::internal::GetEmptyString();
  ::google::protobuf::internal::GetEmptyString();
  ::google::protobuf::internal::GetEmptyString();
  ::google::protobuf::internal::GetEmptyString();
  TestArenaMapLite_default_instance_.DefaultConstruct();
  ::google::protobuf::internal::GetEmptyString();
  ::google::protobuf::internal::GetEmptyString();
  ::google::protobuf::internal::GetEmptyString();
  ::google::protobuf::internal::GetEmptyString();
  ::google::protobuf::internal::GetEmptyString();
  ::google::protobuf::internal::GetEmptyString();
  ::google::protobuf::internal::GetEmptyString();
  ::google::protobuf::internal::GetEmptyString();
  ::google::protobuf::internal::GetEmptyString();
  ::google::protobuf::internal::GetEmptyString();
  TestRequiredMessageMapLite_default_instance_.DefaultConstruct();
  ::google::protobuf::internal::GetEmptyString();
  ::google::protobuf::internal::GetEmptyString();
  TestEnumMapLite_default_instance_.DefaultConstruct();
  ::google::protobuf::internal::GetEmptyString();
  ::google::protobuf::internal::GetEmptyString();
  TestEnumMapPlusExtraLite_default_instance_.DefaultConstruct();
  ::google::protobuf::internal::GetEmptyString();
  ::google::protobuf::internal::GetEmptyString();
  TestMessageMapLite_default_instance_.DefaultConstruct();
  ::google::protobuf::internal::GetEmptyString();
  ::google::protobuf::internal::GetEmptyString();
  TestRequiredLite_default_instance_.DefaultConstruct();
  ::google::protobuf::internal::GetEmptyString();
  ForeignMessageArenaLite_default_instance_.DefaultConstruct();
  TestMapLite_default_instance_.get_mutable()->InitAsDefaultInstance();
  TestArenaMapLite_default_instance_.get_mutable()->InitAsDefaultInstance();
  TestRequiredMessageMapLite_default_instance_.get_mutable()->InitAsDefaultInstance();
  TestEnumMapLite_default_instance_.get_mutable()->InitAsDefaultInstance();
  TestEnumMapPlusExtraLite_default_instance_.get_mutable()->InitAsDefaultInstance();
  TestMessageMapLite_default_instance_.get_mutable()->InitAsDefaultInstance();
  TestRequiredLite_default_instance_.get_mutable()->InitAsDefaultInstance();
  ForeignMessageArenaLite_default_instance_.get_mutable()->InitAsDefaultInstance();
}

GOOGLE_PROTOBUF_DECLARE_ONCE(protobuf_InitDefaults_google_2fprotobuf_2fmap_5flite_5funittest_2eproto_once_);
void protobuf_InitDefaults_google_2fprotobuf_2fmap_5flite_5funittest_2eproto() {
  ::google::protobuf::GoogleOnceInit(&protobuf_InitDefaults_google_2fprotobuf_2fmap_5flite_5funittest_2eproto_once_,
                 &protobuf_InitDefaults_google_2fprotobuf_2fmap_5flite_5funittest_2eproto_impl);
}
void protobuf_AddDesc_google_2fprotobuf_2fmap_5flite_5funittest_2eproto_impl() {
  GOOGLE_PROTOBUF_VERIFY_VERSION;

  protobuf_InitDefaults_google_2fprotobuf_2fmap_5flite_5funittest_2eproto();
  ::protobuf_unittest::protobuf_AddDesc_google_2fprotobuf_2funittest_5flite_2eproto();
  ::protobuf_unittest_no_arena::protobuf_AddDesc_google_2fprotobuf_2funittest_5fno_5farena_5flite_2eproto();
  ::google::protobuf::internal::OnShutdown(&protobuf_ShutdownFile_google_2fprotobuf_2fmap_5flite_5funittest_2eproto);
}

GOOGLE_PROTOBUF_DECLARE_ONCE(protobuf_AddDesc_google_2fprotobuf_2fmap_5flite_5funittest_2eproto_once_);
void protobuf_AddDesc_google_2fprotobuf_2fmap_5flite_5funittest_2eproto() {
  ::google::protobuf::GoogleOnceInit(&protobuf_AddDesc_google_2fprotobuf_2fmap_5flite_5funittest_2eproto_once_,
                 &protobuf_AddDesc_google_2fprotobuf_2fmap_5flite_5funittest_2eproto_impl);
}
#ifdef GOOGLE_PROTOBUF_NO_STATIC_INITIALIZER
// Force AddDescriptors() to be called at static initialization time.
struct StaticDescriptorInitializer_google_2fprotobuf_2fmap_5flite_5funittest_2eproto {
  StaticDescriptorInitializer_google_2fprotobuf_2fmap_5flite_5funittest_2eproto() {
    protobuf_AddDesc_google_2fprotobuf_2fmap_5flite_5funittest_2eproto();
  }
} static_descriptor_initializer_google_2fprotobuf_2fmap_5flite_5funittest_2eproto_;
#endif  // GOOGLE_PROTOBUF_NO_STATIC_INITIALIZER
bool Proto2MapEnumLite_IsValid(int value) {
  switch (value) {
    case 0:
    case 1:
    case 2:
      return true;
    default:
      return false;
  }
}

bool Proto2MapEnumPlusExtraLite_IsValid(int value) {
  switch (value) {
    case 0:
    case 1:
    case 2:
    case 3:
      return true;
    default:
      return false;
  }
}

bool MapEnumLite_IsValid(int value) {
  switch (value) {
    case 0:
    case 1:
    case 2:
      return true;
    default:
      return false;
  }
}


namespace {

static void MergeFromFail(int line) GOOGLE_ATTRIBUTE_COLD GOOGLE_ATTRIBUTE_NORETURN;
static void MergeFromFail(int line) {
  ::google::protobuf::internal::MergeFromFail(__FILE__, line);
}

}  // namespace


// ===================================================================

static ::std::string* MutableUnknownFieldsForTestMapLite(
    TestMapLite* ptr) {
  return ptr->mutable_unknown_fields();
}

#if !defined(_MSC_VER) || _MSC_VER >= 1900
const int TestMapLite::kMapInt32Int32FieldNumber;
const int TestMapLite::kMapInt64Int64FieldNumber;
const int TestMapLite::kMapUint32Uint32FieldNumber;
const int TestMapLite::kMapUint64Uint64FieldNumber;
const int TestMapLite::kMapSint32Sint32FieldNumber;
const int TestMapLite::kMapSint64Sint64FieldNumber;
const int TestMapLite::kMapFixed32Fixed32FieldNumber;
const int TestMapLite::kMapFixed64Fixed64FieldNumber;
const int TestMapLite::kMapSfixed32Sfixed32FieldNumber;
const int TestMapLite::kMapSfixed64Sfixed64FieldNumber;
const int TestMapLite::kMapInt32FloatFieldNumber;
const int TestMapLite::kMapInt32DoubleFieldNumber;
const int TestMapLite::kMapBoolBoolFieldNumber;
const int TestMapLite::kMapStringStringFieldNumber;
const int TestMapLite::kMapInt32BytesFieldNumber;
const int TestMapLite::kMapInt32EnumFieldNumber;
const int TestMapLite::kMapInt32ForeignMessageFieldNumber;
const int TestMapLite::kTeboringFieldNumber;
#endif  // !defined(_MSC_VER) || _MSC_VER >= 1900

TestMapLite::TestMapLite()
  : ::google::protobuf::MessageLite(), _arena_ptr_(NULL) {
  if (this != internal_default_instance()) protobuf_InitDefaults_google_2fprotobuf_2fmap_5flite_5funittest_2eproto();
  SharedCtor();
  // @@protoc_insertion_point(constructor:protobuf_unittest.TestMapLite)
}
TestMapLite::TestMapLite(::google::protobuf::Arena* arena)
  : ::google::protobuf::MessageLite(),
  _arena_ptr_(arena),
  map_int32_int32_(arena),
  map_int64_int64_(arena),
  map_uint32_uint32_(arena),
  map_uint64_uint64_(arena),
  map_sint32_sint32_(arena),
  map_sint64_sint64_(arena),
  map_fixed32_fixed32_(arena),
  map_fixed64_fixed64_(arena),
  map_sfixed32_sfixed32_(arena),
  map_sfixed64_sfixed64_(arena),
  map_int32_float_(arena),
  map_int32_double_(arena),
  map_bool_bool_(arena),
  map_string_string_(arena),
  map_int32_bytes_(arena),
  map_int32_enum_(arena),
  map_int32_foreign_message_(arena),
  teboring_(arena) {
#ifdef GOOGLE_PROTOBUF_NO_STATIC_INITIALIZER
  protobuf_InitDefaults_google_2fprotobuf_2fmap_5flite_5funittest_2eproto();
#endif  // GOOGLE_PROTOBUF_NO_STATIC_INITIALIZER
  SharedCtor();
  RegisterArenaDtor(arena);
  // @@protoc_insertion_point(arena_constructor:protobuf_unittest.TestMapLite)
}

void TestMapLite::InitAsDefaultInstance() {
}

TestMapLite::TestMapLite(const TestMapLite& from)
  : ::google::protobuf::MessageLite(),
    _arena_ptr_(NULL) {
  SharedCtor();
  UnsafeMergeFrom(from);
  // @@protoc_insertion_point(copy_constructor:protobuf_unittest.TestMapLite)
}

void TestMapLite::SharedCtor() {
  _cached_size_ = 0;
  _unknown_fields_.UnsafeSetDefault(
      &::google::protobuf::internal::GetEmptyStringAlreadyInited());
}

TestMapLite::~TestMapLite() {
  // @@protoc_insertion_point(destructor:protobuf_unittest.TestMapLite)
  SharedDtor();
}

void TestMapLite::SharedDtor() {
  ::google::protobuf::Arena* arena = GetArenaNoVirtual();
  if (arena != NULL) {
    return;
  }

  _unknown_fields_.Destroy(
      &::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      arena);
}

void TestMapLite::ArenaDtor(void* object) {
  TestMapLite* _this = reinterpret_cast< TestMapLite* >(object);
  (void)_this;
}
void TestMapLite::RegisterArenaDtor(::google::protobuf::Arena* arena) {
}
void TestMapLite::SetCachedSize(int size) const {
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
}
const TestMapLite& TestMapLite::default_instance() {
  protobuf_InitDefaults_google_2fprotobuf_2fmap_5flite_5funittest_2eproto();
  return *internal_default_instance();
}

::google::protobuf::internal::ExplicitlyConstructed<TestMapLite> TestMapLite_default_instance_;

TestMapLite* TestMapLite::New(::google::protobuf::Arena* arena) const {
  return ::google::protobuf::Arena::CreateMessage<TestMapLite>(arena);
}

void TestMapLite::Clear() {
// @@protoc_insertion_point(message_clear_start:protobuf_unittest.TestMapLite)
  map_int32_int32_.Clear();
  map_int64_int64_.Clear();
  map_uint32_uint32_.Clear();
  map_uint64_uint64_.Clear();
  map_sint32_sint32_.Clear();
  map_sint64_sint64_.Clear();
  map_fixed32_fixed32_.Clear();
  map_fixed64_fixed64_.Clear();
  map_sfixed32_sfixed32_.Clear();
  map_sfixed64_sfixed64_.Clear();
  map_int32_float_.Clear();
  map_int32_double_.Clear();
  map_bool_bool_.Clear();
  map_string_string_.Clear();
  map_int32_bytes_.Clear();
  map_int32_enum_.Clear();
  map_int32_foreign_message_.Clear();
  teboring_.Clear();
  _has_bits_.Clear();
  _unknown_fields_.ClearToEmpty(
      &::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      GetArenaNoVirtual());
}

bool TestMapLite::MergePartialFromCodedStream(
    ::google::protobuf::io::CodedInputStream* input) {
#define DO_(EXPRESSION) if (!GOOGLE_PREDICT_TRUE(EXPRESSION)) goto failure
  ::google::protobuf::uint32 tag;
  ::google::protobuf::io::LazyStringOutputStream unknown_fields_string(
      ::google::protobuf::NewPermanentCallback(
          &MutableUnknownFieldsForTestMapLite, this));
  ::google::protobuf::io::CodedOutputStream unknown_fields_stream(
      &unknown_fields_string, false);
  // @@protoc_insertion_point(parse_start:protobuf_unittest.TestMapLite)
  for (;;) {
    ::std::pair< ::google::protobuf::uint32, bool> p = input->ReadTagWithCutoff(16383);
    tag = p.first;
    if (!p.second) goto handle_unusual;
    switch (::google::protobuf::internal::WireFormatLite::GetTagFieldNumber(tag)) {
      // map<int32, int32> map_int32_int32 = 1;
      case 1: {
        if (tag == 10) {
          DO_(input->IncrementRecursionDepth());
         parse_loop_map_int32_int32:
          TestMapLite_MapInt32Int32Entry::Parser< ::google::protobuf::internal::MapFieldLite<
              ::google::protobuf::int32, ::google::protobuf::int32,
              ::google::protobuf::internal::WireFormatLite::TYPE_INT32,
              ::google::protobuf::internal::WireFormatLite::TYPE_INT32,
              0 >,
            ::google::protobuf::Map< ::google::protobuf::int32, ::google::protobuf::int32 > > parser(&map_int32_int32_);
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessageNoVirtual(
              input, &parser));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(10)) goto parse_loop_map_int32_int32;
        if (input->ExpectTag(18)) goto parse_loop_map_int64_int64;
        input->UnsafeDecrementRecursionDepth();
        break;
      }

      // map<int64, int64> map_int64_int64 = 2;
      case 2: {
        if (tag == 18) {
          DO_(input->IncrementRecursionDepth());
         parse_loop_map_int64_int64:
          TestMapLite_MapInt64Int64Entry::Parser< ::google::protobuf::internal::MapFieldLite<
              ::google::protobuf::int64, ::google::protobuf::int64,
              ::google::protobuf::internal::WireFormatLite::TYPE_INT64,
              ::google::protobuf::internal::WireFormatLite::TYPE_INT64,
              0 >,
            ::google::protobuf::Map< ::google::protobuf::int64, ::google::protobuf::int64 > > parser(&map_int64_int64_);
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessageNoVirtual(
              input, &parser));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(18)) goto parse_loop_map_int64_int64;
        if (input->ExpectTag(26)) goto parse_loop_map_uint32_uint32;
        input->UnsafeDecrementRecursionDepth();
        break;
      }

      // map<uint32, uint32> map_uint32_uint32 = 3;
      case 3: {
        if (tag == 26) {
          DO_(input->IncrementRecursionDepth());
         parse_loop_map_uint32_uint32:
          TestMapLite_MapUint32Uint32Entry::Parser< ::google::protobuf::internal::MapFieldLite<
              ::google::protobuf::uint32, ::google::protobuf::uint32,
              ::google::protobuf::internal::WireFormatLite::TYPE_UINT32,
              ::google::protobuf::internal::WireFormatLite::TYPE_UINT32,
              0 >,
            ::google::protobuf::Map< ::google::protobuf::uint32, ::google::protobuf::uint32 > > parser(&map_uint32_uint32_);
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessageNoVirtual(
              input, &parser));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(26)) goto parse_loop_map_uint32_uint32;
        if (input->ExpectTag(34)) goto parse_loop_map_uint64_uint64;
        input->UnsafeDecrementRecursionDepth();
        break;
      }

      // map<uint64, uint64> map_uint64_uint64 = 4;
      case 4: {
        if (tag == 34) {
          DO_(input->IncrementRecursionDepth());
         parse_loop_map_uint64_uint64:
          TestMapLite_MapUint64Uint64Entry::Parser< ::google::protobuf::internal::MapFieldLite<
              ::google::protobuf::uint64, ::google::protobuf::uint64,
              ::google::protobuf::internal::WireFormatLite::TYPE_UINT64,
              ::google::protobuf::internal::WireFormatLite::TYPE_UINT64,
              0 >,
            ::google::protobuf::Map< ::google::protobuf::uint64, ::google::protobuf::uint64 > > parser(&map_uint64_uint64_);
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessageNoVirtual(
              input, &parser));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(34)) goto parse_loop_map_uint64_uint64;
        if (input->ExpectTag(42)) goto parse_loop_map_sint32_sint32;
        input->UnsafeDecrementRecursionDepth();
        break;
      }

      // map<sint32, sint32> map_sint32_sint32 = 5;
      case 5: {
        if (tag == 42) {
          DO_(input->IncrementRecursionDepth());
         parse_loop_map_sint32_sint32:
          TestMapLite_MapSint32Sint32Entry::Parser< ::google::protobuf::internal::MapFieldLite<
              ::google::protobuf::int32, ::google::protobuf::int32,
              ::google::protobuf::internal::WireFormatLite::TYPE_SINT32,
              ::google::protobuf::internal::WireFormatLite::TYPE_SINT32,
              0 >,
            ::google::protobuf::Map< ::google::protobuf::int32, ::google::protobuf::int32 > > parser(&map_sint32_sint32_);
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessageNoVirtual(
              input, &parser));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(42)) goto parse_loop_map_sint32_sint32;
        if (input->ExpectTag(50)) goto parse_loop_map_sint64_sint64;
        input->UnsafeDecrementRecursionDepth();
        break;
      }

      // map<sint64, sint64> map_sint64_sint64 = 6;
      case 6: {
        if (tag == 50) {
          DO_(input->IncrementRecursionDepth());
         parse_loop_map_sint64_sint64:
          TestMapLite_MapSint64Sint64Entry::Parser< ::google::protobuf::internal::MapFieldLite<
              ::google::protobuf::int64, ::google::protobuf::int64,
              ::google::protobuf::internal::WireFormatLite::TYPE_SINT64,
              ::google::protobuf::internal::WireFormatLite::TYPE_SINT64,
              0 >,
            ::google::protobuf::Map< ::google::protobuf::int64, ::google::protobuf::int64 > > parser(&map_sint64_sint64_);
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessageNoVirtual(
              input, &parser));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(50)) goto parse_loop_map_sint64_sint64;
        if (input->ExpectTag(58)) goto parse_loop_map_fixed32_fixed32;
        input->UnsafeDecrementRecursionDepth();
        break;
      }

      // map<fixed32, fixed32> map_fixed32_fixed32 = 7;
      case 7: {
        if (tag == 58) {
          DO_(input->IncrementRecursionDepth());
         parse_loop_map_fixed32_fixed32:
          TestMapLite_MapFixed32Fixed32Entry::Parser< ::google::protobuf::internal::MapFieldLite<
              ::google::protobuf::uint32, ::google::protobuf::uint32,
              ::google::protobuf::internal::WireFormatLite::TYPE_FIXED32,
              ::google::protobuf::internal::WireFormatLite::TYPE_FIXED32,
              0 >,
            ::google::protobuf::Map< ::google::protobuf::uint32, ::google::protobuf::uint32 > > parser(&map_fixed32_fixed32_);
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessageNoVirtual(
              input, &parser));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(58)) goto parse_loop_map_fixed32_fixed32;
        if (input->ExpectTag(66)) goto parse_loop_map_fixed64_fixed64;
        input->UnsafeDecrementRecursionDepth();
        break;
      }

      // map<fixed64, fixed64> map_fixed64_fixed64 = 8;
      case 8: {
        if (tag == 66) {
          DO_(input->IncrementRecursionDepth());
         parse_loop_map_fixed64_fixed64:
          TestMapLite_MapFixed64Fixed64Entry::Parser< ::google::protobuf::internal::MapFieldLite<
              ::google::protobuf::uint64, ::google::protobuf::uint64,
              ::google::protobuf::internal::WireFormatLite::TYPE_FIXED64,
              ::google::protobuf::internal::WireFormatLite::TYPE_FIXED64,
              0 >,
            ::google::protobuf::Map< ::google::protobuf::uint64, ::google::protobuf::uint64 > > parser(&map_fixed64_fixed64_);
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessageNoVirtual(
              input, &parser));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(66)) goto parse_loop_map_fixed64_fixed64;
        if (input->ExpectTag(74)) goto parse_loop_map_sfixed32_sfixed32;
        input->UnsafeDecrementRecursionDepth();
        break;
      }

      // map<sfixed32, sfixed32> map_sfixed32_sfixed32 = 9;
      case 9: {
        if (tag == 74) {
          DO_(input->IncrementRecursionDepth());
         parse_loop_map_sfixed32_sfixed32:
          TestMapLite_MapSfixed32Sfixed32Entry::Parser< ::google::protobuf::internal::MapFieldLite<
              ::google::protobuf::int32, ::google::protobuf::int32,
              ::google::protobuf::internal::WireFormatLite::TYPE_SFIXED32,
              ::google::protobuf::internal::WireFormatLite::TYPE_SFIXED32,
              0 >,
            ::google::protobuf::Map< ::google::protobuf::int32, ::google::protobuf::int32 > > parser(&map_sfixed32_sfixed32_);
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessageNoVirtual(
              input, &parser));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(74)) goto parse_loop_map_sfixed32_sfixed32;
        if (input->ExpectTag(82)) goto parse_loop_map_sfixed64_sfixed64;
        input->UnsafeDecrementRecursionDepth();
        break;
      }

      // map<sfixed64, sfixed64> map_sfixed64_sfixed64 = 10;
      case 10: {
        if (tag == 82) {
          DO_(input->IncrementRecursionDepth());
         parse_loop_map_sfixed64_sfixed64:
          TestMapLite_MapSfixed64Sfixed64Entry::Parser< ::google::protobuf::internal::MapFieldLite<
              ::google::protobuf::int64, ::google::protobuf::int64,
              ::google::protobuf::internal::WireFormatLite::TYPE_SFIXED64,
              ::google::protobuf::internal::WireFormatLite::TYPE_SFIXED64,
              0 >,
            ::google::protobuf::Map< ::google::protobuf::int64, ::google::protobuf::int64 > > parser(&map_sfixed64_sfixed64_);
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessageNoVirtual(
              input, &parser));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(82)) goto parse_loop_map_sfixed64_sfixed64;
        if (input->ExpectTag(90)) goto parse_loop_map_int32_float;
        input->UnsafeDecrementRecursionDepth();
        break;
      }

      // map<int32, float> map_int32_float = 11;
      case 11: {
        if (tag == 90) {
          DO_(input->IncrementRecursionDepth());
         parse_loop_map_int32_float:
          TestMapLite_MapInt32FloatEntry::Parser< ::google::protobuf::internal::MapFieldLite<
              ::google::protobuf::int32, float,
              ::google::protobuf::internal::WireFormatLite::TYPE_INT32,
              ::google::protobuf::internal::WireFormatLite::TYPE_FLOAT,
              0 >,
            ::google::protobuf::Map< ::google::protobuf::int32, float > > parser(&map_int32_float_);
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessageNoVirtual(
              input, &parser));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(90)) goto parse_loop_map_int32_float;
        if (input->ExpectTag(98)) goto parse_loop_map_int32_double;
        input->UnsafeDecrementRecursionDepth();
        break;
      }

      // map<int32, double> map_int32_double = 12;
      case 12: {
        if (tag == 98) {
          DO_(input->IncrementRecursionDepth());
         parse_loop_map_int32_double:
          TestMapLite_MapInt32DoubleEntry::Parser< ::google::protobuf::internal::MapFieldLite<
              ::google::protobuf::int32, double,
              ::google::protobuf::internal::WireFormatLite::TYPE_INT32,
              ::google::protobuf::internal::WireFormatLite::TYPE_DOUBLE,
              0 >,
            ::google::protobuf::Map< ::google::protobuf::int32, double > > parser(&map_int32_double_);
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessageNoVirtual(
              input, &parser));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(98)) goto parse_loop_map_int32_double;
        if (input->ExpectTag(106)) goto parse_loop_map_bool_bool;
        input->UnsafeDecrementRecursionDepth();
        break;
      }

      // map<bool, bool> map_bool_bool = 13;
      case 13: {
        if (tag == 106) {
          DO_(input->IncrementRecursionDepth());
         parse_loop_map_bool_bool:
          TestMapLite_MapBoolBoolEntry::Parser< ::google::protobuf::internal::MapFieldLite<
              bool, bool,
              ::google::protobuf::internal::WireFormatLite::TYPE_BOOL,
              ::google::protobuf::internal::WireFormatLite::TYPE_BOOL,
              0 >,
            ::google::protobuf::Map< bool, bool > > parser(&map_bool_bool_);
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessageNoVirtual(
              input, &parser));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(106)) goto parse_loop_map_bool_bool;
        if (input->ExpectTag(114)) goto parse_loop_map_string_string;
        input->UnsafeDecrementRecursionDepth();
        break;
      }

      // map<string, string> map_string_string = 14;
      case 14: {
        if (tag == 114) {
          DO_(input->IncrementRecursionDepth());
         parse_loop_map_string_string:
          TestMapLite_MapStringStringEntry::Parser< ::google::protobuf::internal::MapFieldLite<
              ::std::string, ::std::string,
              ::google::protobuf::internal::WireFormatLite::TYPE_STRING,
              ::google::protobuf::internal::WireFormatLite::TYPE_STRING,
              0 >,
            ::google::protobuf::Map< ::std::string, ::std::string > > parser(&map_string_string_);
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessageNoVirtual(
              input, &parser));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(114)) goto parse_loop_map_string_string;
        if (input->ExpectTag(122)) goto parse_loop_map_int32_bytes;
        input->UnsafeDecrementRecursionDepth();
        break;
      }

      // map<int32, bytes> map_int32_bytes = 15;
      case 15: {
        if (tag == 122) {
          DO_(input->IncrementRecursionDepth());
         parse_loop_map_int32_bytes:
          TestMapLite_MapInt32BytesEntry::Parser< ::google::protobuf::internal::MapFieldLite<
              ::google::protobuf::int32, ::std::string,
              ::google::protobuf::internal::WireFormatLite::TYPE_INT32,
              ::google::protobuf::internal::WireFormatLite::TYPE_BYTES,
              0 >,
            ::google::protobuf::Map< ::google::protobuf::int32, ::std::string > > parser(&map_int32_bytes_);
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessageNoVirtual(
              input, &parser));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(122)) goto parse_loop_map_int32_bytes;
        if (input->ExpectTag(130)) goto parse_loop_map_int32_enum;
        input->UnsafeDecrementRecursionDepth();
        break;
      }

      // map<int32, .protobuf_unittest.MapEnumLite> map_int32_enum = 16;
      case 16: {
        if (tag == 130) {
          DO_(input->IncrementRecursionDepth());
         parse_loop_map_int32_enum:
          ::google::protobuf::scoped_ptr<TestMapLite_MapInt32EnumEntry> entry(map_int32_enum_.NewEntry());
          {
            ::std::string data;
            DO_(::google::protobuf::internal::WireFormatLite::ReadString(input, &data));
            DO_(entry->ParseFromString(data));
            if (::protobuf_unittest::MapEnumLite_IsValid(*entry->mutable_value())) {
              (*mutable_map_int32_enum())[entry->key()] =
                  static_cast< ::protobuf_unittest::MapEnumLite >(*entry->mutable_value());
            } else {
              unknown_fields_stream.WriteVarint32(130);
              unknown_fields_stream.WriteVarint32(data.size());
              unknown_fields_stream.WriteString(data);
            }
          }
          if (entry->GetArena() != NULL) entry.release();
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(130)) goto parse_loop_map_int32_enum;
        if (input->ExpectTag(138)) goto parse_loop_map_int32_foreign_message;
        input->UnsafeDecrementRecursionDepth();
        break;
      }

      // map<int32, .protobuf_unittest.ForeignMessageLite> map_int32_foreign_message = 17;
      case 17: {
        if (tag == 138) {
          DO_(input->IncrementRecursionDepth());
         parse_loop_map_int32_foreign_message:
          TestMapLite_MapInt32ForeignMessageEntry::Parser< ::google::protobuf::internal::MapFieldLite<
              ::google::protobuf::int32, ::protobuf_unittest::ForeignMessageLite,
              ::google::protobuf::internal::WireFormatLite::TYPE_INT32,
              ::google::protobuf::internal::WireFormatLite::TYPE_MESSAGE,
              0 >,
            ::google::protobuf::Map< ::google::protobuf::int32, ::protobuf_unittest::ForeignMessageLite > > parser(&map_int32_foreign_message_);
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessageNoVirtual(
              input, &parser));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(138)) goto parse_loop_map_int32_foreign_message;
        if (input->ExpectTag(146)) goto parse_loop_teboring;
        input->UnsafeDecrementRecursionDepth();
        break;
      }

      // map<int32, int32> teboring = 18;
      case 18: {
        if (tag == 146) {
          DO_(input->IncrementRecursionDepth());
         parse_loop_teboring:
          TestMapLite_TeboringEntry::Parser< ::google::protobuf::internal::MapFieldLite<
              ::google::protobuf::int32, ::google::protobuf::int32,
              ::google::protobuf::internal::WireFormatLite::TYPE_INT32,
              ::google::protobuf::internal::WireFormatLite::TYPE_INT32,
              0 >,
            ::google::protobuf::Map< ::google::protobuf::int32, ::google::protobuf::int32 > > parser(&teboring_);
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessageNoVirtual(
              input, &parser));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(146)) goto parse_loop_teboring;
        input->UnsafeDecrementRecursionDepth();
        if (input->ExpectAtEnd()) goto success;
        break;
      }

      default: {
      handle_unusual:
        if (tag == 0 ||
            ::google::protobuf::internal::WireFormatLite::GetTagWireType(tag) ==
            ::google::protobuf::internal::WireFormatLite::WIRETYPE_END_GROUP) {
          goto success;
        }
        DO_(::google::protobuf::internal::WireFormatLite::SkipField(
            input, tag, &unknown_fields_stream));
        break;
      }
    }
  }
success:
  // @@protoc_insertion_point(parse_success:protobuf_unittest.TestMapLite)
  return true;
failure:
  // @@protoc_insertion_point(parse_failure:protobuf_unittest.TestMapLite)
  return false;
#undef DO_
}

void TestMapLite::SerializeWithCachedSizes(
    ::google::protobuf::io::CodedOutputStream* output) const {
  // @@protoc_insertion_point(serialize_start:protobuf_unittest.TestMapLite)
  // map<int32, int32> map_int32_int32 = 1;
  if (!this->map_int32_int32().empty()) {
    typedef ::google::protobuf::Map< ::google::protobuf::int32, ::google::protobuf::int32 >::const_pointer
        ConstPtr;
    typedef ::google::protobuf::internal::SortItem< ::google::protobuf::int32, ConstPtr > SortItem;
    typedef ::google::protobuf::internal::CompareByFirstField<SortItem> Less;

    if (output->IsSerializationDeterminstic() &&
        this->map_int32_int32().size() > 1) {
      ::google::protobuf::scoped_array<SortItem> items(
          new SortItem[this->map_int32_int32().size()]);
      typedef ::google::protobuf::Map< ::google::protobuf::int32, ::google::protobuf::int32 >::size_type size_type;
      size_type n = 0;
      for (::google::protobuf::Map< ::google::protobuf::int32, ::google::protobuf::int32 >::const_iterator
          it = this->map_int32_int32().begin();
          it != this->map_int32_int32().end(); ++it, ++n) {
        items[n] = SortItem(&*it);
      }
      ::std::sort(&items[0], &items[n], Less());
      ::google::protobuf::scoped_ptr<TestMapLite_MapInt32Int32Entry> entry;
      for (size_type i = 0; i < n; i++) {
        entry.reset(map_int32_int32_.NewEntryWrapper(
            items[i].second->first, items[i].second->second));
        ::google::protobuf::internal::WireFormatLite::WriteMessage(
            1, *entry, output);
        if (entry->GetArena() != NULL) {
          entry.release();
        }
      }
    } else {
      ::google::protobuf::scoped_ptr<TestMapLite_MapInt32Int32Entry> entry;
      for (::google::protobuf::Map< ::google::protobuf::int32, ::google::protobuf::int32 >::const_iterator
          it = this->map_int32_int32().begin();
          it != this->map_int32_int32().end(); ++it) {
        entry.reset(map_int32_int32_.NewEntryWrapper(
            it->first, it->second));
        ::google::protobuf::internal::WireFormatLite::WriteMessage(
            1, *entry, output);
        if (entry->GetArena() != NULL) {
          entry.release();
        }
      }
    }
  }

  // map<int64, int64> map_int64_int64 = 2;
  if (!this->map_int64_int64().empty()) {
    typedef ::google::protobuf::Map< ::google::protobuf::int64, ::google::protobuf::int64 >::const_pointer
        ConstPtr;
    typedef ::google::protobuf::internal::SortItem< ::google::protobuf::int64, ConstPtr > SortItem;
    typedef ::google::protobuf::internal::CompareByFirstField<SortItem> Less;

    if (output->IsSerializationDeterminstic() &&
        this->map_int64_int64().size() > 1) {
      ::google::protobuf::scoped_array<SortItem> items(
          new SortItem[this->map_int64_int64().size()]);
      typedef ::google::protobuf::Map< ::google::protobuf::int64, ::google::protobuf::int64 >::size_type size_type;
      size_type n = 0;
      for (::google::protobuf::Map< ::google::protobuf::int64, ::google::protobuf::int64 >::const_iterator
          it = this->map_int64_int64().begin();
          it != this->map_int64_int64().end(); ++it, ++n) {
        items[n] = SortItem(&*it);
      }
      ::std::sort(&items[0], &items[n], Less());
      ::google::protobuf::scoped_ptr<TestMapLite_MapInt64Int64Entry> entry;
      for (size_type i = 0; i < n; i++) {
        entry.reset(map_int64_int64_.NewEntryWrapper(
            items[i].second->first, items[i].second->second));
        ::google::protobuf::internal::WireFormatLite::WriteMessage(
            2, *entry, output);
        if (entry->GetArena() != NULL) {
          entry.release();
        }
      }
    } else {
      ::google::protobuf::scoped_ptr<TestMapLite_MapInt64Int64Entry> entry;
      for (::google::protobuf::Map< ::google::protobuf::int64, ::google::protobuf::int64 >::const_iterator
          it = this->map_int64_int64().begin();
          it != this->map_int64_int64().end(); ++it) {
        entry.reset(map_int64_int64_.NewEntryWrapper(
            it->first, it->second));
        ::google::protobuf::internal::WireFormatLite::WriteMessage(
            2, *entry, output);
        if (entry->GetArena() != NULL) {
          entry.release();
        }
      }
    }
  }

  // map<uint32, uint32> map_uint32_uint32 = 3;
  if (!this->map_uint32_uint32().empty()) {
    typedef ::google::protobuf::Map< ::google::protobuf::uint32, ::google::protobuf::uint32 >::const_pointer
        ConstPtr;
    typedef ::google::protobuf::internal::SortItem< ::google::protobuf::uint32, ConstPtr > SortItem;
    typedef ::google::protobuf::internal::CompareByFirstField<SortItem> Less;

    if (output->IsSerializationDeterminstic() &&
        this->map_uint32_uint32().size() > 1) {
      ::google::protobuf::scoped_array<SortItem> items(
          new SortItem[this->map_uint32_uint32().size()]);
      typedef ::google::protobuf::Map< ::google::protobuf::uint32, ::google::protobuf::uint32 >::size_type size_type;
      size_type n = 0;
      for (::google::protobuf::Map< ::google::protobuf::uint32, ::google::protobuf::uint32 >::const_iterator
          it = this->map_uint32_uint32().begin();
          it != this->map_uint32_uint32().end(); ++it, ++n) {
        items[n] = SortItem(&*it);
      }
      ::std::sort(&items[0], &items[n], Less());
      ::google::protobuf::scoped_ptr<TestMapLite_MapUint32Uint32Entry> entry;
      for (size_type i = 0; i < n; i++) {
        entry.reset(map_uint32_uint32_.NewEntryWrapper(
            items[i].second->first, items[i].second->second));
        ::google::protobuf::internal::WireFormatLite::WriteMessage(
            3, *entry, output);
        if (entry->GetArena() != NULL) {
          entry.release();
        }
      }
    } else {
      ::google::protobuf::scoped_ptr<TestMapLite_MapUint32Uint32Entry> entry;
      for (::google::protobuf::Map< ::google::protobuf::uint32, ::google::protobuf::uint32 >::const_iterator
          it = this->map_uint32_uint32().begin();
          it != this->map_uint32_uint32().end(); ++it) {
        entry.reset(map_uint32_uint32_.NewEntryWrapper(
            it->first, it->second));
        ::google::protobuf::internal::WireFormatLite::WriteMessage(
            3, *entry, output);
        if (entry->GetArena() != NULL) {
          entry.release();
        }
      }
    }
  }

  // map<uint64, uint64> map_uint64_uint64 = 4;
  if (!this->map_uint64_uint64().empty()) {
    typedef ::google::protobuf::Map< ::google::protobuf::uint64, ::google::protobuf::uint64 >::const_pointer
        ConstPtr;
    typedef ::google::protobuf::internal::SortItem< ::google::protobuf::uint64, ConstPtr > SortItem;
    typedef ::google::protobuf::internal::CompareByFirstField<SortItem> Less;

    if (output->IsSerializationDeterminstic() &&
        this->map_uint64_uint64().size() > 1) {
      ::google::protobuf::scoped_array<SortItem> items(
          new SortItem[this->map_uint64_uint64().size()]);
      typedef ::google::protobuf::Map< ::google::protobuf::uint64, ::google::protobuf::uint64 >::size_type size_type;
      size_type n = 0;
      for (::google::protobuf::Map< ::google::protobuf::uint64, ::google::protobuf::uint64 >::const_iterator
          it = this->map_uint64_uint64().begin();
          it != this->map_uint64_uint64().end(); ++it, ++n) {
        items[n] = SortItem(&*it);
      }
      ::std::sort(&items[0], &items[n], Less());
      ::google::protobuf::scoped_ptr<TestMapLite_MapUint64Uint64Entry> entry;
      for (size_type i = 0; i < n; i++) {
        entry.reset(map_uint64_uint64_.NewEntryWrapper(
            items[i].second->first, items[i].second->second));
        ::google::protobuf::internal::WireFormatLite::WriteMessage(
            4, *entry, output);
        if (entry->GetArena() != NULL) {
          entry.release();
        }
      }
    } else {
      ::google::protobuf::scoped_ptr<TestMapLite_MapUint64Uint64Entry> entry;
      for (::google::protobuf::Map< ::google::protobuf::uint64, ::google::protobuf::uint64 >::const_iterator
          it = this->map_uint64_uint64().begin();
          it != this->map_uint64_uint64().end(); ++it) {
        entry.reset(map_uint64_uint64_.NewEntryWrapper(
            it->first, it->second));
        ::google::protobuf::internal::WireFormatLite::WriteMessage(
            4, *entry, output);
        if (entry->GetArena() != NULL) {
          entry.release();
        }
      }
    }
  }

  // map<sint32, sint32> map_sint32_sint32 = 5;
  if (!this->map_sint32_sint32().empty()) {
    typedef ::google::protobuf::Map< ::google::protobuf::int32, ::google::protobuf::int32 >::const_pointer
        ConstPtr;
    typedef ::google::protobuf::internal::SortItem< ::google::protobuf::int32, ConstPtr > SortItem;
    typedef ::google::protobuf::internal::CompareByFirstField<SortItem> Less;

    if (output->IsSerializationDeterminstic() &&
        this->map_sint32_sint32().size() > 1) {
      ::google::protobuf::scoped_array<SortItem> items(
          new SortItem[this->map_sint32_sint32().size()]);
      typedef ::google::protobuf::Map< ::google::protobuf::int32, ::google::protobuf::int32 >::size_type size_type;
      size_type n = 0;
      for (::google::protobuf::Map< ::google::protobuf::int32, ::google::protobuf::int32 >::const_iterator
          it = this->map_sint32_sint32().begin();
          it != this->map_sint32_sint32().end(); ++it, ++n) {
        items[n] = SortItem(&*it);
      }
      ::std::sort(&items[0], &items[n], Less());
      ::google::protobuf::scoped_ptr<TestMapLite_MapSint32Sint32Entry> entry;
      for (size_type i = 0; i < n; i++) {
        entry.reset(map_sint32_sint32_.NewEntryWrapper(
            items[i].second->first, items[i].second->second));
        ::google::protobuf::internal::WireFormatLite::WriteMessage(
            5, *entry, output);
        if (entry->GetArena() != NULL) {
          entry.release();
        }
      }
    } else {
      ::google::protobuf::scoped_ptr<TestMapLite_MapSint32Sint32Entry> entry;
      for (::google::protobuf::Map< ::google::protobuf::int32, ::google::protobuf::int32 >::const_iterator
          it = this->map_sint32_sint32().begin();
          it != this->map_sint32_sint32().end(); ++it) {
        entry.reset(map_sint32_sint32_.NewEntryWrapper(
            it->first, it->second));
        ::google::protobuf::internal::WireFormatLite::WriteMessage(
            5, *entry, output);
        if (entry->GetArena() != NULL) {
          entry.release();
        }
      }
    }
  }

  // map<sint64, sint64> map_sint64_sint64 = 6;
  if (!this->map_sint64_sint64().empty()) {
    typedef ::google::protobuf::Map< ::google::protobuf::int64, ::google::protobuf::int64 >::const_pointer
        ConstPtr;
    typedef ::google::protobuf::internal::SortItem< ::google::protobuf::int64, ConstPtr > SortItem;
    typedef ::google::protobuf::internal::CompareByFirstField<SortItem> Less;

    if (output->IsSerializationDeterminstic() &&
        this->map_sint64_sint64().size() > 1) {
      ::google::protobuf::scoped_array<SortItem> items(
          new SortItem[this->map_sint64_sint64().size()]);
      typedef ::google::protobuf::Map< ::google::protobuf::int64, ::google::protobuf::int64 >::size_type size_type;
      size_type n = 0;
      for (::google::protobuf::Map< ::google::protobuf::int64, ::google::protobuf::int64 >::const_iterator
          it = this->map_sint64_sint64().begin();
          it != this->map_sint64_sint64().end(); ++it, ++n) {
        items[n] = SortItem(&*it);
      }
      ::std::sort(&items[0], &items[n], Less());
      ::google::protobuf::scoped_ptr<TestMapLite_MapSint64Sint64Entry> entry;
      for (size_type i = 0; i < n; i++) {
        entry.reset(map_sint64_sint64_.NewEntryWrapper(
            items[i].second->first, items[i].second->second));
        ::google::protobuf::internal::WireFormatLite::WriteMessage(
            6, *entry, output);
        if (entry->GetArena() != NULL) {
          entry.release();
        }
      }
    } else {
      ::google::protobuf::scoped_ptr<TestMapLite_MapSint64Sint64Entry> entry;
      for (::google::protobuf::Map< ::google::protobuf::int64, ::google::protobuf::int64 >::const_iterator
          it = this->map_sint64_sint64().begin();
          it != this->map_sint64_sint64().end(); ++it) {
        entry.reset(map_sint64_sint64_.NewEntryWrapper(
            it->first, it->second));
        ::google::protobuf::internal::WireFormatLite::WriteMessage(
            6, *entry, output);
        if (entry->GetArena() != NULL) {
          entry.release();
        }
      }
    }
  }

  // map<fixed32, fixed32> map_fixed32_fixed32 = 7;
  if (!this->map_fixed32_fixed32().empty()) {
    typedef ::google::protobuf::Map< ::google::protobuf::uint32, ::google::protobuf::uint32 >::const_pointer
        ConstPtr;
    typedef ::google::protobuf::internal::SortItem< ::google::protobuf::uint32, ConstPtr > SortItem;
    typedef ::google::protobuf::internal::CompareByFirstField<SortItem> Less;

    if (output->IsSerializationDeterminstic() &&
        this->map_fixed32_fixed32().size() > 1) {
      ::google::protobuf::scoped_array<SortItem> items(
          new SortItem[this->map_fixed32_fixed32().size()]);
      typedef ::google::protobuf::Map< ::google::protobuf::uint32, ::google::protobuf::uint32 >::size_type size_type;
      size_type n = 0;
      for (::google::protobuf::Map< ::google::protobuf::uint32, ::google::protobuf::uint32 >::const_iterator
          it = this->map_fixed32_fixed32().begin();
          it != this->map_fixed32_fixed32().end(); ++it, ++n) {
        items[n] = SortItem(&*it);
      }
      ::std::sort(&items[0], &items[n], Less());
      ::google::protobuf::scoped_ptr<TestMapLite_MapFixed32Fixed32Entry> entry;
      for (size_type i = 0; i < n; i++) {
        entry.reset(map_fixed32_fixed32_.NewEntryWrapper(
            items[i].second->first, items[i].second->second));
        ::google::protobuf::internal::WireFormatLite::WriteMessage(
            7, *entry, output);
        if (entry->GetArena() != NULL) {
          entry.release();
        }
      }
    } else {
      ::google::protobuf::scoped_ptr<TestMapLite_MapFixed32Fixed32Entry> entry;
      for (::google::protobuf::Map< ::google::protobuf::uint32, ::google::protobuf::uint32 >::const_iterator
          it = this->map_fixed32_fixed32().begin();
          it != this->map_fixed32_fixed32().end(); ++it) {
        entry.reset(map_fixed32_fixed32_.NewEntryWrapper(
            it->first, it->second));
        ::google::protobuf::internal::WireFormatLite::WriteMessage(
            7, *entry, output);
        if (entry->GetArena() != NULL) {
          entry.release();
        }
      }
    }
  }

  // map<fixed64, fixed64> map_fixed64_fixed64 = 8;
  if (!this->map_fixed64_fixed64().empty()) {
    typedef ::google::protobuf::Map< ::google::protobuf::uint64, ::google::protobuf::uint64 >::const_pointer
        ConstPtr;
    typedef ::google::protobuf::internal::SortItem< ::google::protobuf::uint64, ConstPtr > SortItem;
    typedef ::google::protobuf::internal::CompareByFirstField<SortItem> Less;

    if (output->IsSerializationDeterminstic() &&
        this->map_fixed64_fixed64().size() > 1) {
      ::google::protobuf::scoped_array<SortItem> items(
          new SortItem[this->map_fixed64_fixed64().size()]);
      typedef ::google::protobuf::Map< ::google::protobuf::uint64, ::google::protobuf::uint64 >::size_type size_type;
      size_type n = 0;
      for (::google::protobuf::Map< ::google::protobuf::uint64, ::google::protobuf::uint64 >::const_iterator
          it = this->map_fixed64_fixed64().begin();
          it != this->map_fixed64_fixed64().end(); ++it, ++n) {
        items[n] = SortItem(&*it);
      }
      ::std::sort(&items[0], &items[n], Less());
      ::google::protobuf::scoped_ptr<TestMapLite_MapFixed64Fixed64Entry> entry;
      for (size_type i = 0; i < n; i++) {
        entry.reset(map_fixed64_fixed64_.NewEntryWrapper(
            items[i].second->first, items[i].second->second));
        ::google::protobuf::internal::WireFormatLite::WriteMessage(
            8, *entry, output);
        if (entry->GetArena() != NULL) {
          entry.release();
        }
      }
    } else {
      ::google::protobuf::scoped_ptr<TestMapLite_MapFixed64Fixed64Entry> entry;
      for (::google::protobuf::Map< ::google::protobuf::uint64, ::google::protobuf::uint64 >::const_iterator
          it = this->map_fixed64_fixed64().begin();
          it != this->map_fixed64_fixed64().end(); ++it) {
        entry.reset(map_fixed64_fixed64_.NewEntryWrapper(
            it->first, it->second));
        ::google::protobuf::internal::WireFormatLite::WriteMessage(
            8, *entry, output);
        if (entry->GetArena() != NULL) {
          entry.release();
        }
      }
    }
  }

  // map<sfixed32, sfixed32> map_sfixed32_sfixed32 = 9;
  if (!this->map_sfixed32_sfixed32().empty()) {
    typedef ::google::protobuf::Map< ::google::protobuf::int32, ::google::protobuf::int32 >::const_pointer
        ConstPtr;
    typedef ::google::protobuf::internal::SortItem< ::google::protobuf::int32, ConstPtr > SortItem;
    typedef ::google::protobuf::internal::CompareByFirstField<SortItem> Less;

    if (output->IsSerializationDeterminstic() &&
        this->map_sfixed32_sfixed32().size() > 1) {
      ::google::protobuf::scoped_array<SortItem> items(
          new SortItem[this->map_sfixed32_sfixed32().size()]);
      typedef ::google::protobuf::Map< ::google::protobuf::int32, ::google::protobuf::int32 >::size_type size_type;
      size_type n = 0;
      for (::google::protobuf::Map< ::google::protobuf::int32, ::google::protobuf::int32 >::const_iterator
          it = this->map_sfixed32_sfixed32().begin();
          it != this->map_sfixed32_sfixed32().end(); ++it, ++n) {
        items[n] = SortItem(&*it);
      }
      ::std::sort(&items[0], &items[n], Less());
      ::google::protobuf::scoped_ptr<TestMapLite_MapSfixed32Sfixed32Entry> entry;
      for (size_type i = 0; i < n; i++) {
        entry.reset(map_sfixed32_sfixed32_.NewEntryWrapper(
            items[i].second->first, items[i].second->second));
        ::google::protobuf::internal::WireFormatLite::WriteMessage(
            9, *entry, output);
        if (entry->GetArena() != NULL) {
          entry.release();
        }
      }
    } else {
      ::google::protobuf::scoped_ptr<TestMapLite_MapSfixed32Sfixed32Entry> entry;
      for (::google::protobuf::Map< ::google::protobuf::int32, ::google::protobuf::int32 >::const_iterator
          it = this->map_sfixed32_sfixed32().begin();
          it != this->map_sfixed32_sfixed32().end(); ++it) {
        entry.reset(map_sfixed32_sfixed32_.NewEntryWrapper(
            it->first, it->second));
        ::google::protobuf::internal::WireFormatLite::WriteMessage(
            9, *entry, output);
        if (entry->GetArena() != NULL) {
          entry.release();
        }
      }
    }
  }

  // map<sfixed64, sfixed64> map_sfixed64_sfixed64 = 10;
  if (!this->map_sfixed64_sfixed64().empty()) {
    typedef ::google::protobuf::Map< ::google::protobuf::int64, ::google::protobuf::int64 >::const_pointer
        ConstPtr;
    typedef ::google::protobuf::internal::SortItem< ::google::protobuf::int64, ConstPtr > SortItem;
    typedef ::google::protobuf::internal::CompareByFirstField<SortItem> Less;

    if (output->IsSerializationDeterminstic() &&
        this->map_sfixed64_sfixed64().size() > 1) {
      ::google::protobuf::scoped_array<SortItem> items(
          new SortItem[this->map_sfixed64_sfixed64().size()]);
      typedef ::google::protobuf::Map< ::google::protobuf::int64, ::google::protobuf::int64 >::size_type size_type;
      size_type n = 0;
      for (::google::protobuf::Map< ::google::protobuf::int64, ::google::protobuf::int64 >::const_iterator
          it = this->map_sfixed64_sfixed64().begin();
          it != this->map_sfixed64_sfixed64().end(); ++it, ++n) {
        items[n] = SortItem(&*it);
      }
      ::std::sort(&items[0], &items[n], Less());
      ::google::protobuf::scoped_ptr<TestMapLite_MapSfixed64Sfixed64Entry> entry;
      for (size_type i = 0; i < n; i++) {
        entry.reset(map_sfixed64_sfixed64_.NewEntryWrapper(
            items[i].second->first, items[i].second->second));
        ::google::protobuf::internal::WireFormatLite::WriteMessage(
            10, *entry, output);
        if (entry->GetArena() != NULL) {
          entry.release();
        }
      }
    } else {
      ::google::protobuf::scoped_ptr<TestMapLite_MapSfixed64Sfixed64Entry> entry;
      for (::google::protobuf::Map< ::google::protobuf::int64, ::google::protobuf::int64 >::const_iterator
          it = this->map_sfixed64_sfixed64().begin();
          it != this->map_sfixed64_sfixed64().end(); ++it) {
        entry.reset(map_sfixed64_sfixed64_.NewEntryWrapper(
            it->first, it->second));
        ::google::protobuf::internal::WireFormatLite::WriteMessage(
            10, *entry, output);
        if (entry->GetArena() != NULL) {
          entry.release();
        }
      }
    }
  }

  // map<int32, float> map_int32_float = 11;
  if (!this->map_int32_float().empty()) {
    typedef ::google::protobuf::Map< ::google::protobuf::int32, float >::const_pointer
        ConstPtr;
    typedef ::google::protobuf::internal::SortItem< ::google::protobuf::int32, ConstPtr > SortItem;
    typedef ::google::protobuf::internal::CompareByFirstField<SortItem> Less;

    if (output->IsSerializationDeterminstic() &&
        this->map_int32_float().size() > 1) {
      ::google::protobuf::scoped_array<SortItem> items(
          new SortItem[this->map_int32_float().size()]);
      typedef ::google::protobuf::Map< ::google::protobuf::int32, float >::size_type size_type;
      size_type n = 0;
      for (::google::protobuf::Map< ::google::protobuf::int32, float >::const_iterator
          it = this->map_int32_float().begin();
          it != this->map_int32_float().end(); ++it, ++n) {
        items[n] = SortItem(&*it);
      }
      ::std::sort(&items[0], &items[n], Less());
      ::google::protobuf::scoped_ptr<TestMapLite_MapInt32FloatEntry> entry;
      for (size_type i = 0; i < n; i++) {
        entry.reset(map_int32_float_.NewEntryWrapper(
            items[i].second->first, items[i].second->second));
        ::google::protobuf::internal::WireFormatLite::WriteMessage(
            11, *entry, output);
        if (entry->GetArena() != NULL) {
          entry.release();
        }
      }
    } else {
      ::google::protobuf::scoped_ptr<TestMapLite_MapInt32FloatEntry> entry;
      for (::google::protobuf::Map< ::google::protobuf::int32, float >::const_iterator
          it = this->map_int32_float().begin();
          it != this->map_int32_float().end(); ++it) {
        entry.reset(map_int32_float_.NewEntryWrapper(
            it->first, it->second));
        ::google::protobuf::internal::WireFormatLite::WriteMessage(
            11, *entry, output);
        if (entry->GetArena() != NULL) {
          entry.release();
        }
      }
    }
  }

  // map<int32, double> map_int32_double = 12;
  if (!this->map_int32_double().empty()) {
    typedef ::google::protobuf::Map< ::google::protobuf::int32, double >::const_pointer
        ConstPtr;
    typedef ::google::protobuf::internal::SortItem< ::google::protobuf::int32, ConstPtr > SortItem;
    typedef ::google::protobuf::internal::CompareByFirstField<SortItem> Less;

    if (output->IsSerializationDeterminstic() &&
        this->map_int32_double().size() > 1) {
      ::google::protobuf::scoped_array<SortItem> items(
          new SortItem[this->map_int32_double().size()]);
      typedef ::google::protobuf::Map< ::google::protobuf::int32, double >::size_type size_type;
      size_type n = 0;
      for (::google::protobuf::Map< ::google::protobuf::int32, double >::const_iterator
          it = this->map_int32_double().begin();
          it != this->map_int32_double().end(); ++it, ++n) {
        items[n] = SortItem(&*it);
      }
      ::std::sort(&items[0], &items[n], Less());
      ::google::protobuf::scoped_ptr<TestMapLite_MapInt32DoubleEntry> entry;
      for (size_type i = 0; i < n; i++) {
        entry.reset(map_int32_double_.NewEntryWrapper(
            items[i].second->first, items[i].second->second));
        ::google::protobuf::internal::WireFormatLite::WriteMessage(
            12, *entry, output);
        if (entry->GetArena() != NULL) {
          entry.release();
        }
      }
    } else {
      ::google::protobuf::scoped_ptr<TestMapLite_MapInt32DoubleEntry> entry;
      for (::google::protobuf::Map< ::google::protobuf::int32, double >::const_iterator
          it = this->map_int32_double().begin();
          it != this->map_int32_double().end(); ++it) {
        entry.reset(map_int32_double_.NewEntryWrapper(
            it->first, it->second));
        ::google::protobuf::internal::WireFormatLite::WriteMessage(
            12, *entry, output);
        if (entry->GetArena() != NULL) {
          entry.release();
        }
      }
    }
  }

  // map<bool, bool> map_bool_bool = 13;
  if (!this->map_bool_bool().empty()) {
    typedef ::google::protobuf::Map< bool, bool >::const_pointer
        ConstPtr;
    typedef ::google::protobuf::internal::SortItem< bool, ConstPtr > SortItem;
    typedef ::google::protobuf::internal::CompareByFirstField<SortItem> Less;

    if (output->IsSerializationDeterminstic() &&
        this->map_bool_bool().size() > 1) {
      ::google::protobuf::scoped_array<SortItem> items(
          new SortItem[this->map_bool_bool().size()]);
      typedef ::google::protobuf::Map< bool, bool >::size_type size_type;
      size_type n = 0;
      for (::google::protobuf::Map< bool, bool >::const_iterator
          it = this->map_bool_bool().begin();
          it != this->map_bool_bool().end(); ++it, ++n) {
        items[n] = SortItem(&*it);
      }
      ::std::sort(&items[0], &items[n], Less());
      ::google::protobuf::scoped_ptr<TestMapLite_MapBoolBoolEntry> entry;
      for (size_type i = 0; i < n; i++) {
        entry.reset(map_bool_bool_.NewEntryWrapper(
            items[i].second->first, items[i].second->second));
        ::google::protobuf::internal::WireFormatLite::WriteMessage(
            13, *entry, output);
        if (entry->GetArena() != NULL) {
          entry.release();
        }
      }
    } else {
      ::google::protobuf::scoped_ptr<TestMapLite_MapBoolBoolEntry> entry;
      for (::google::protobuf::Map< bool, bool >::const_iterator
          it = this->map_bool_bool().begin();
          it != this->map_bool_bool().end(); ++it) {
        entry.reset(map_bool_bool_.NewEntryWrapper(
            it->first, it->second));
        ::google::protobuf::internal::WireFormatLite::WriteMessage(
            13, *entry, output);
        if (entry->GetArena() != NULL) {
          entry.release();
        }
      }
    }
  }

  // map<string, string> map_string_string = 14;
  if (!this->map_string_string().empty()) {
    typedef ::google::protobuf::Map< ::std::string, ::std::string >::const_pointer
        ConstPtr;
    typedef ConstPtr SortItem;
    typedef ::google::protobuf::internal::CompareByDerefFirst<SortItem> Less;
    struct Utf8Check {
      static void Check(ConstPtr p) {
      }
    };

    if (output->IsSerializationDeterminstic() &&
        this->map_string_string().size() > 1) {
      ::google::protobuf::scoped_array<SortItem> items(
          new SortItem[this->map_string_string().size()]);
      typedef ::google::protobuf::Map< ::std::string, ::std::string >::size_type size_type;
      size_type n = 0;
      for (::google::protobuf::Map< ::std::string, ::std::string >::const_iterator
          it = this->map_string_string().begin();
          it != this->map_string_string().end(); ++it, ++n) {
        items[n] = SortItem(&*it);
      }
      ::std::sort(&items[0], &items[n], Less());
      ::google::protobuf::scoped_ptr<TestMapLite_MapStringStringEntry> entry;
      for (size_type i = 0; i < n; i++) {
        entry.reset(map_string_string_.NewEntryWrapper(
            items[i]->first, items[i]->second));
        ::google::protobuf::internal::WireFormatLite::WriteMessage(
            14, *entry, output);
        if (entry->GetArena() != NULL) {
          entry.release();
        }
        Utf8Check::Check(items[i]);
      }
    } else {
      ::google::protobuf::scoped_ptr<TestMapLite_MapStringStringEntry> entry;
      for (::google::protobuf::Map< ::std::string, ::std::string >::const_iterator
          it = this->map_string_string().begin();
          it != this->map_string_string().end(); ++it) {
        entry.reset(map_string_string_.NewEntryWrapper(
            it->first, it->second));
        ::google::protobuf::internal::WireFormatLite::WriteMessage(
            14, *entry, output);
        if (entry->GetArena() != NULL) {
          entry.release();
        }
        Utf8Check::Check(&*it);
      }
    }
  }

  // map<int32, bytes> map_int32_bytes = 15;
  if (!this->map_int32_bytes().empty()) {
    typedef ::google::protobuf::Map< ::google::protobuf::int32, ::std::string >::const_pointer
        ConstPtr;
    typedef ::google::protobuf::internal::SortItem< ::google::protobuf::int32, ConstPtr > SortItem;
    typedef ::google::protobuf::internal::CompareByFirstField<SortItem> Less;

    if (output->IsSerializationDeterminstic() &&
        this->map_int32_bytes().size() > 1) {
      ::google::protobuf::scoped_array<SortItem> items(
          new SortItem[this->map_int32_bytes().size()]);
      typedef ::google::protobuf::Map< ::google::protobuf::int32, ::std::string >::size_type size_type;
      size_type n = 0;
      for (::google::protobuf::Map< ::google::protobuf::int32, ::std::string >::const_iterator
          it = this->map_int32_bytes().begin();
          it != this->map_int32_bytes().end(); ++it, ++n) {
        items[n] = SortItem(&*it);
      }
      ::std::sort(&items[0], &items[n], Less());
      ::google::protobuf::scoped_ptr<TestMapLite_MapInt32BytesEntry> entry;
      for (size_type i = 0; i < n; i++) {
        entry.reset(map_int32_bytes_.NewEntryWrapper(
            items[i].second->first, items[i].second->second));
        ::google::protobuf::internal::WireFormatLite::WriteMessage(
            15, *entry, output);
        if (entry->GetArena() != NULL) {
          entry.release();
        }
      }
    } else {
      ::google::protobuf::scoped_ptr<TestMapLite_MapInt32BytesEntry> entry;
      for (::google::protobuf::Map< ::google::protobuf::int32, ::std::string >::const_iterator
          it = this->map_int32_bytes().begin();
          it != this->map_int32_bytes().end(); ++it) {
        entry.reset(map_int32_bytes_.NewEntryWrapper(
            it->first, it->second));
        ::google::protobuf::internal::WireFormatLite::WriteMessage(
            15, *entry, output);
        if (entry->GetArena() != NULL) {
          entry.release();
        }
      }
    }
  }

  // map<int32, .protobuf_unittest.MapEnumLite> map_int32_enum = 16;
  if (!this->map_int32_enum().empty()) {
    typedef ::google::protobuf::Map< ::google::protobuf::int32, ::protobuf_unittest::MapEnumLite >::const_pointer
        ConstPtr;
    typedef ::google::protobuf::internal::SortItem< ::google::protobuf::int32, ConstPtr > SortItem;
    typedef ::google::protobuf::internal::CompareByFirstField<SortItem> Less;

    if (output->IsSerializationDeterminstic() &&
        this->map_int32_enum().size() > 1) {
      ::google::protobuf::scoped_array<SortItem> items(
          new SortItem[this->map_int32_enum().size()]);
      typedef ::google::protobuf::Map< ::google::protobuf::int32, ::protobuf_unittest::MapEnumLite >::size_type size_type;
      size_type n = 0;
      for (::google::protobuf::Map< ::google::protobuf::int32, ::protobuf_unittest::MapEnumLite >::const_iterator
          it = this->map_int32_enum().begin();
          it != this->map_int32_enum().end(); ++it, ++n) {
        items[n] = SortItem(&*it);
      }
      ::std::sort(&items[0], &items[n], Less());
      ::google::protobuf::scoped_ptr<TestMapLite_MapInt32EnumEntry> entry;
      for (size_type i = 0; i < n; i++) {
        entry.reset(map_int32_enum_.NewEnumEntryWrapper(
            items[i].second->first, items[i].second->second));
        ::google::protobuf::internal::WireFormatLite::WriteMessage(
            16, *entry, output);
        if (entry->GetArena() != NULL) {
          entry.release();
        }
      }
    } else {
      ::google::protobuf::scoped_ptr<TestMapLite_MapInt32EnumEntry> entry;
      for (::google::protobuf::Map< ::google::protobuf::int32, ::protobuf_unittest::MapEnumLite >::const_iterator
          it = this->map_int32_enum().begin();
          it != this->map_int32_enum().end(); ++it) {
        entry.reset(map_int32_enum_.NewEnumEntryWrapper(
            it->first, it->second));
        ::google::protobuf::internal::WireFormatLite::WriteMessage(
            16, *entry, output);
        if (entry->GetArena() != NULL) {
          entry.release();
        }
      }
    }
  }

  // map<int32, .protobuf_unittest.ForeignMessageLite> map_int32_foreign_message = 17;
  if (!this->map_int32_foreign_message().empty()) {
    typedef ::google::protobuf::Map< ::google::protobuf::int32, ::protobuf_unittest::ForeignMessageLite >::const_pointer
        ConstPtr;
    typedef ::google::protobuf::internal::SortItem< ::google::protobuf::int32, ConstPtr > SortItem;
    typedef ::google::protobuf::internal::CompareByFirstField<SortItem> Less;

    if (output->IsSerializationDeterminstic() &&
        this->map_int32_foreign_message().size() > 1) {
      ::google::protobuf::scoped_array<SortItem> items(
          new SortItem[this->map_int32_foreign_message().size()]);
      typedef ::google::protobuf::Map< ::google::protobuf::int32, ::protobuf_unittest::ForeignMessageLite >::size_type size_type;
      size_type n = 0;
      for (::google::protobuf::Map< ::google::protobuf::int32, ::protobuf_unittest::ForeignMessageLite >::const_iterator
          it = this->map_int32_foreign_message().begin();
          it != this->map_int32_foreign_message().end(); ++it, ++n) {
        items[n] = SortItem(&*it);
      }
      ::std::sort(&items[0], &items[n], Less());
      ::google::protobuf::scoped_ptr<TestMapLite_MapInt32ForeignMessageEntry> entry;
      for (size_type i = 0; i < n; i++) {
        entry.reset(map_int32_foreign_message_.NewEntryWrapper(
            items[i].second->first, items[i].second->second));
        ::google::protobuf::internal::WireFormatLite::WriteMessage(
            17, *entry, output);
        if (entry->GetArena() != NULL) {
          entry.release();
        }
      }
    } else {
      ::google::protobuf::scoped_ptr<TestMapLite_MapInt32ForeignMessageEntry> entry;
      for (::google::protobuf::Map< ::google::protobuf::int32, ::protobuf_unittest::ForeignMessageLite >::const_iterator
          it = this->map_int32_foreign_message().begin();
          it != this->map_int32_foreign_message().end(); ++it) {
        entry.reset(map_int32_foreign_message_.NewEntryWrapper(
            it->first, it->second));
        ::google::protobuf::internal::WireFormatLite::WriteMessage(
            17, *entry, output);
        if (entry->GetArena() != NULL) {
          entry.release();
        }
      }
    }
  }

  // map<int32, int32> teboring = 18;
  if (!this->teboring().empty()) {
    typedef ::google::protobuf::Map< ::google::protobuf::int32, ::google::protobuf::int32 >::const_pointer
        ConstPtr;
    typedef ::google::protobuf::internal::SortItem< ::google::protobuf::int32, ConstPtr > SortItem;
    typedef ::google::protobuf::internal::CompareByFirstField<SortItem> Less;

    if (output->IsSerializationDeterminstic() &&
        this->teboring().size() > 1) {
      ::google::protobuf::scoped_array<SortItem> items(
          new SortItem[this->teboring().size()]);
      typedef ::google::protobuf::Map< ::google::protobuf::int32, ::google::protobuf::int32 >::size_type size_type;
      size_type n = 0;
      for (::google::protobuf::Map< ::google::protobuf::int32, ::google::protobuf::int32 >::const_iterator
          it = this->teboring().begin();
          it != this->teboring().end(); ++it, ++n) {
        items[n] = SortItem(&*it);
      }
      ::std::sort(&items[0], &items[n], Less());
      ::google::protobuf::scoped_ptr<TestMapLite_TeboringEntry> entry;
      for (size_type i = 0; i < n; i++) {
        entry.reset(teboring_.NewEntryWrapper(
            items[i].second->first, items[i].second->second));
        ::google::protobuf::internal::WireFormatLite::WriteMessage(
            18, *entry, output);
        if (entry->GetArena() != NULL) {
          entry.release();
        }
      }
    } else {
      ::google::protobuf::scoped_ptr<TestMapLite_TeboringEntry> entry;
      for (::google::protobuf::Map< ::google::protobuf::int32, ::google::protobuf::int32 >::const_iterator
          it = this->teboring().begin();
          it != this->teboring().end(); ++it) {
        entry.reset(teboring_.NewEntryWrapper(
            it->first, it->second));
        ::google::protobuf::internal::WireFormatLite::WriteMessage(
            18, *entry, output);
        if (entry->GetArena() != NULL) {
          entry.release();
        }
      }
    }
  }

  output->WriteRaw(unknown_fields().data(),
                   static_cast<int>(unknown_fields().size()));
  // @@protoc_insertion_point(serialize_end:protobuf_unittest.TestMapLite)
}

size_t TestMapLite::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:protobuf_unittest.TestMapLite)
  size_t total_size = 0;

  // map<int32, int32> map_int32_int32 = 1;
  total_size += 1 *
      ::google::protobuf::internal::FromIntSize(this->map_int32_int32_size());
  {
    ::google::protobuf::scoped_ptr<TestMapLite_MapInt32Int32Entry> entry;
    for (::google::protobuf::Map< ::google::protobuf::int32, ::google::protobuf::int32 >::const_iterator
        it = this->map_int32_int32().begin();
        it != this->map_int32_int32().end(); ++it) {
      if (entry.get() != NULL && entry->GetArena() != NULL) {
        entry.release();
      }
      entry.reset(map_int32_int32_.NewEntryWrapper(it->first, it->second));
      total_size += ::google::protobuf::internal::WireFormatLite::
          MessageSizeNoVirtual(*entry);
    }
    if (entry.get() != NULL && entry->GetArena() != NULL) {
      entry.release();
    }
  }

  // map<int64, int64> map_int64_int64 = 2;
  total_size += 1 *
      ::google::protobuf::internal::FromIntSize(this->map_int64_int64_size());
  {
    ::google::protobuf::scoped_ptr<TestMapLite_MapInt64Int64Entry> entry;
    for (::google::protobuf::Map< ::google::protobuf::int64, ::google::protobuf::int64 >::const_iterator
        it = this->map_int64_int64().begin();
        it != this->map_int64_int64().end(); ++it) {
      if (entry.get() != NULL && entry->GetArena() != NULL) {
        entry.release();
      }
      entry.reset(map_int64_int64_.NewEntryWrapper(it->first, it->second));
      total_size += ::google::protobuf::internal::WireFormatLite::
          MessageSizeNoVirtual(*entry);
    }
    if (entry.get() != NULL && entry->GetArena() != NULL) {
      entry.release();
    }
  }

  // map<uint32, uint32> map_uint32_uint32 = 3;
  total_size += 1 *
      ::google::protobuf::internal::FromIntSize(this->map_uint32_uint32_size());
  {
    ::google::protobuf::scoped_ptr<TestMapLite_MapUint32Uint32Entry> entry;
    for (::google::protobuf::Map< ::google::protobuf::uint32, ::google::protobuf::uint32 >::const_iterator
        it = this->map_uint32_uint32().begin();
        it != this->map_uint32_uint32().end(); ++it) {
      if (entry.get() != NULL && entry->GetArena() != NULL) {
        entry.release();
      }
      entry.reset(map_uint32_uint32_.NewEntryWrapper(it->first, it->second));
      total_size += ::google::protobuf::internal::WireFormatLite::
          MessageSizeNoVirtual(*entry);
    }
    if (entry.get() != NULL && entry->GetArena() != NULL) {
      entry.release();
    }
  }

  // map<uint64, uint64> map_uint64_uint64 = 4;
  total_size += 1 *
      ::google::protobuf::internal::FromIntSize(this->map_uint64_uint64_size());
  {
    ::google::protobuf::scoped_ptr<TestMapLite_MapUint64Uint64Entry> entry;
    for (::google::protobuf::Map< ::google::protobuf::uint64, ::google::protobuf::uint64 >::const_iterator
        it = this->map_uint64_uint64().begin();
        it != this->map_uint64_uint64().end(); ++it) {
      if (entry.get() != NULL && entry->GetArena() != NULL) {
        entry.release();
      }
      entry.reset(map_uint64_uint64_.NewEntryWrapper(it->first, it->second));
      total_size += ::google::protobuf::internal::WireFormatLite::
          MessageSizeNoVirtual(*entry);
    }
    if (entry.get() != NULL && entry->GetArena() != NULL) {
      entry.release();
    }
  }

  // map<sint32, sint32> map_sint32_sint32 = 5;
  total_size += 1 *
      ::google::protobuf::internal::FromIntSize(this->map_sint32_sint32_size());
  {
    ::google::protobuf::scoped_ptr<TestMapLite_MapSint32Sint32Entry> entry;
    for (::google::protobuf::Map< ::google::protobuf::int32, ::google::protobuf::int32 >::const_iterator
        it = this->map_sint32_sint32().begin();
        it != this->map_sint32_sint32().end(); ++it) {
      if (entry.get() != NULL && entry->GetArena() != NULL) {
        entry.release();
      }
      entry.reset(map_sint32_sint32_.NewEntryWrapper(it->first, it->second));
      total_size += ::google::protobuf::internal::WireFormatLite::
          MessageSizeNoVirtual(*entry);
    }
    if (entry.get() != NULL && entry->GetArena() != NULL) {
      entry.release();
    }
  }

  // map<sint64, sint64> map_sint64_sint64 = 6;
  total_size += 1 *
      ::google::protobuf::internal::FromIntSize(this->map_sint64_sint64_size());
  {
    ::google::protobuf::scoped_ptr<TestMapLite_MapSint64Sint64Entry> entry;
    for (::google::protobuf::Map< ::google::protobuf::int64, ::google::protobuf::int64 >::const_iterator
        it = this->map_sint64_sint64().begin();
        it != this->map_sint64_sint64().end(); ++it) {
      if (entry.get() != NULL && entry->GetArena() != NULL) {
        entry.release();
      }
      entry.reset(map_sint64_sint64_.NewEntryWrapper(it->first, it->second));
      total_size += ::google::protobuf::internal::WireFormatLite::
          MessageSizeNoVirtual(*entry);
    }
    if (entry.get() != NULL && entry->GetArena() != NULL) {
      entry.release();
    }
  }

  // map<fixed32, fixed32> map_fixed32_fixed32 = 7;
  total_size += 1 *
      ::google::protobuf::internal::FromIntSize(this->map_fixed32_fixed32_size());
  {
    ::google::protobuf::scoped_ptr<TestMapLite_MapFixed32Fixed32Entry> entry;
    for (::google::protobuf::Map< ::google::protobuf::uint32, ::google::protobuf::uint32 >::const_iterator
        it = this->map_fixed32_fixed32().begin();
        it != this->map_fixed32_fixed32().end(); ++it) {
      if (entry.get() != NULL && entry->GetArena() != NULL) {
        entry.release();
      }
      entry.reset(map_fixed32_fixed32_.NewEntryWrapper(it->first, it->second));
      total_size += ::google::protobuf::internal::WireFormatLite::
          MessageSizeNoVirtual(*entry);
    }
    if (entry.get() != NULL && entry->GetArena() != NULL) {
      entry.release();
    }
  }

  // map<fixed64, fixed64> map_fixed64_fixed64 = 8;
  total_size += 1 *
      ::google::protobuf::internal::FromIntSize(this->map_fixed64_fixed64_size());
  {
    ::google::protobuf::scoped_ptr<TestMapLite_MapFixed64Fixed64Entry> entry;
    for (::google::protobuf::Map< ::google::protobuf::uint64, ::google::protobuf::uint64 >::const_iterator
        it = this->map_fixed64_fixed64().begin();
        it != this->map_fixed64_fixed64().end(); ++it) {
      if (entry.get() != NULL && entry->GetArena() != NULL) {
        entry.release();
      }
      entry.reset(map_fixed64_fixed64_.NewEntryWrapper(it->first, it->second));
      total_size += ::google::protobuf::internal::WireFormatLite::
          MessageSizeNoVirtual(*entry);
    }
    if (entry.get() != NULL && entry->GetArena() != NULL) {
      entry.release();
    }
  }

  // map<sfixed32, sfixed32> map_sfixed32_sfixed32 = 9;
  total_size += 1 *
      ::google::protobuf::internal::FromIntSize(this->map_sfixed32_sfixed32_size());
  {
    ::google::protobuf::scoped_ptr<TestMapLite_MapSfixed32Sfixed32Entry> entry;
    for (::google::protobuf::Map< ::google::protobuf::int32, ::google::protobuf::int32 >::const_iterator
        it = this->map_sfixed32_sfixed32().begin();
        it != this->map_sfixed32_sfixed32().end(); ++it) {
      if (entry.get() != NULL && entry->GetArena() != NULL) {
        entry.release();
      }
      entry.reset(map_sfixed32_sfixed32_.NewEntryWrapper(it->first, it->second));
      total_size += ::google::protobuf::internal::WireFormatLite::
          MessageSizeNoVirtual(*entry);
    }
    if (entry.get() != NULL && entry->GetArena() != NULL) {
      entry.release();
    }
  }

  // map<sfixed64, sfixed64> map_sfixed64_sfixed64 = 10;
  total_size += 1 *
      ::google::protobuf::internal::FromIntSize(this->map_sfixed64_sfixed64_size());
  {
    ::google::protobuf::scoped_ptr<TestMapLite_MapSfixed64Sfixed64Entry> entry;
    for (::google::protobuf::Map< ::google::protobuf::int64, ::google::protobuf::int64 >::const_iterator
        it = this->map_sfixed64_sfixed64().begin();
        it != this->map_sfixed64_sfixed64().end(); ++it) {
      if (entry.get() != NULL && entry->GetArena() != NULL) {
        entry.release();
      }
      entry.reset(map_sfixed64_sfixed64_.NewEntryWrapper(it->first, it->second));
      total_size += ::google::protobuf::internal::WireFormatLite::
          MessageSizeNoVirtual(*entry);
    }
    if (entry.get() != NULL && entry->GetArena() != NULL) {
      entry.release();
    }
  }

  // map<int32, float> map_int32_float = 11;
  total_size += 1 *
      ::google::protobuf::internal::FromIntSize(this->map_int32_float_size());
  {
    ::google::protobuf::scoped_ptr<TestMapLite_MapInt32FloatEntry> entry;
    for (::google::protobuf::Map< ::google::protobuf::int32, float >::const_iterator
        it = this->map_int32_float().begin();
        it != this->map_int32_float().end(); ++it) {
      if (entry.get() != NULL && entry->GetArena() != NULL) {
        entry.release();
      }
      entry.reset(map_int32_float_.NewEntryWrapper(it->first, it->second));
      total_size += ::google::protobuf::internal::WireFormatLite::
          MessageSizeNoVirtual(*entry);
    }
    if (entry.get() != NULL && entry->GetArena() != NULL) {
      entry.release();
    }
  }

  // map<int32, double> map_int32_double = 12;
  total_size += 1 *
      ::google::protobuf::internal::FromIntSize(this->map_int32_double_size());
  {
    ::google::protobuf::scoped_ptr<TestMapLite_MapInt32DoubleEntry> entry;
    for (::google::protobuf::Map< ::google::protobuf::int32, double >::const_iterator
        it = this->map_int32_double().begin();
        it != this->map_int32_double().end(); ++it) {
      if (entry.get() != NULL && entry->GetArena() != NULL) {
        entry.release();
      }
      entry.reset(map_int32_double_.NewEntryWrapper(it->first, it->second));
      total_size += ::google::protobuf::internal::WireFormatLite::
          MessageSizeNoVirtual(*entry);
    }
    if (entry.get() != NULL && entry->GetArena() != NULL) {
      entry.release();
    }
  }

  // map<bool, bool> map_bool_bool = 13;
  total_size += 1 *
      ::google::protobuf::internal::FromIntSize(this->map_bool_bool_size());
  {
    ::google::protobuf::scoped_ptr<TestMapLite_MapBoolBoolEntry> entry;
    for (::google::protobuf::Map< bool, bool >::const_iterator
        it = this->map_bool_bool().begin();
        it != this->map_bool_bool().end(); ++it) {
      if (entry.get() != NULL && entry->GetArena() != NULL) {
        entry.release();
      }
      entry.reset(map_bool_bool_.NewEntryWrapper(it->first, it->second));
      total_size += ::google::protobuf::internal::WireFormatLite::
          MessageSizeNoVirtual(*entry);
    }
    if (entry.get() != NULL && entry->GetArena() != NULL) {
      entry.release();
    }
  }

  // map<string, string> map_string_string = 14;
  total_size += 1 *
      ::google::protobuf::internal::FromIntSize(this->map_string_string_size());
  {
    ::google::protobuf::scoped_ptr<TestMapLite_MapStringStringEntry> entry;
    for (::google::protobuf::Map< ::std::string, ::std::string >::const_iterator
        it = this->map_string_string().begin();
        it != this->map_string_string().end(); ++it) {
      if (entry.get() != NULL && entry->GetArena() != NULL) {
        entry.release();
      }
      entry.reset(map_string_string_.NewEntryWrapper(it->first, it->second));
      total_size += ::google::protobuf::internal::WireFormatLite::
          MessageSizeNoVirtual(*entry);
    }
    if (entry.get() != NULL && entry->GetArena() != NULL) {
      entry.release();
    }
  }

  // map<int32, bytes> map_int32_bytes = 15;
  total_size += 1 *
      ::google::protobuf::internal::FromIntSize(this->map_int32_bytes_size());
  {
    ::google::protobuf::scoped_ptr<TestMapLite_MapInt32BytesEntry> entry;
    for (::google::protobuf::Map< ::google::protobuf::int32, ::std::string >::const_iterator
        it = this->map_int32_bytes().begin();
        it != this->map_int32_bytes().end(); ++it) {
      if (entry.get() != NULL && entry->GetArena() != NULL) {
        entry.release();
      }
      entry.reset(map_int32_bytes_.NewEntryWrapper(it->first, it->second));
      total_size += ::google::protobuf::internal::WireFormatLite::
          MessageSizeNoVirtual(*entry);
    }
    if (entry.get() != NULL && entry->GetArena() != NULL) {
      entry.release();
    }
  }

  // map<int32, .protobuf_unittest.MapEnumLite> map_int32_enum = 16;
  total_size += 2 *
      ::google::protobuf::internal::FromIntSize(this->map_int32_enum_size());
  {
    ::google::protobuf::scoped_ptr<TestMapLite_MapInt32EnumEntry> entry;
    for (::google::protobuf::Map< ::google::protobuf::int32, ::protobuf_unittest::MapEnumLite >::const_iterator
        it = this->map_int32_enum().begin();
        it != this->map_int32_enum().end(); ++it) {
      if (entry.get() != NULL && entry->GetArena() != NULL) {
        entry.release();
      }
      entry.reset(map_int32_enum_.NewEnumEntryWrapper(it->first, it->second));
      total_size += ::google::protobuf::internal::WireFormatLite::
          MessageSizeNoVirtual(*entry);
    }
    if (entry.get() != NULL && entry->GetArena() != NULL) {
      entry.release();
    }
  }

  // map<int32, .protobuf_unittest.ForeignMessageLite> map_int32_foreign_message = 17;
  total_size += 2 *
      ::google::protobuf::internal::FromIntSize(this->map_int32_foreign_message_size());
  {
    ::google::protobuf::scoped_ptr<TestMapLite_MapInt32ForeignMessageEntry> entry;
    for (::google::protobuf::Map< ::google::protobuf::int32, ::protobuf_unittest::ForeignMessageLite >::const_iterator
        it = this->map_int32_foreign_message().begin();
        it != this->map_int32_foreign_message().end(); ++it) {
      if (entry.get() != NULL && entry->GetArena() != NULL) {
        entry.release();
      }
      entry.reset(map_int32_foreign_message_.NewEntryWrapper(it->first, it->second));
      total_size += ::google::protobuf::internal::WireFormatLite::
          MessageSizeNoVirtual(*entry);
    }
    if (entry.get() != NULL && entry->GetArena() != NULL) {
      entry.release();
    }
  }

  // map<int32, int32> teboring = 18;
  total_size += 2 *
      ::google::protobuf::internal::FromIntSize(this->teboring_size());
  {
    ::google::protobuf::scoped_ptr<TestMapLite_TeboringEntry> entry;
    for (::google::protobuf::Map< ::google::protobuf::int32, ::google::protobuf::int32 >::const_iterator
        it = this->teboring().begin();
        it != this->teboring().end(); ++it) {
      if (entry.get() != NULL && entry->GetArena() != NULL) {
        entry.release();
      }
      entry.reset(teboring_.NewEntryWrapper(it->first, it->second));
      total_size += ::google::protobuf::internal::WireFormatLite::
          MessageSizeNoVirtual(*entry);
    }
    if (entry.get() != NULL && entry->GetArena() != NULL) {
      entry.release();
    }
  }

  total_size += unknown_fields().size();

  int cached_size = ::google::protobuf::internal::ToCachedSize(total_size);
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = cached_size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
  return total_size;
}

void TestMapLite::CheckTypeAndMergeFrom(
    const ::google::protobuf::MessageLite& from) {
  MergeFrom(*::google::protobuf::down_cast<const TestMapLite*>(&from));
}

void TestMapLite::MergeFrom(const TestMapLite& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:protobuf_unittest.TestMapLite)
  if (GOOGLE_PREDICT_TRUE(&from != this)) {
    UnsafeMergeFrom(from);
  } else {
    MergeFromFail(__LINE__);
  }
}

void TestMapLite::UnsafeMergeFrom(const TestMapLite& from) {
  GOOGLE_DCHECK(&from != this);
  map_int32_int32_.MergeFrom(from.map_int32_int32_);
  map_int64_int64_.MergeFrom(from.map_int64_int64_);
  map_uint32_uint32_.MergeFrom(from.map_uint32_uint32_);
  map_uint64_uint64_.MergeFrom(from.map_uint64_uint64_);
  map_sint32_sint32_.MergeFrom(from.map_sint32_sint32_);
  map_sint64_sint64_.MergeFrom(from.map_sint64_sint64_);
  map_fixed32_fixed32_.MergeFrom(from.map_fixed32_fixed32_);
  map_fixed64_fixed64_.MergeFrom(from.map_fixed64_fixed64_);
  map_sfixed32_sfixed32_.MergeFrom(from.map_sfixed32_sfixed32_);
  map_sfixed64_sfixed64_.MergeFrom(from.map_sfixed64_sfixed64_);
  map_int32_float_.MergeFrom(from.map_int32_float_);
  map_int32_double_.MergeFrom(from.map_int32_double_);
  map_bool_bool_.MergeFrom(from.map_bool_bool_);
  map_string_string_.MergeFrom(from.map_string_string_);
  map_int32_bytes_.MergeFrom(from.map_int32_bytes_);
  map_int32_enum_.MergeFrom(from.map_int32_enum_);
  map_int32_foreign_message_.MergeFrom(from.map_int32_foreign_message_);
  teboring_.MergeFrom(from.teboring_);
  if (!from.unknown_fields().empty()) {
    mutable_unknown_fields()->append(from.unknown_fields());
  }
}

void TestMapLite::CopyFrom(const TestMapLite& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:protobuf_unittest.TestMapLite)
  if (&from == this) return;
  Clear();
  UnsafeMergeFrom(from);
}

bool TestMapLite::IsInitialized() const {

  return true;
}

void TestMapLite::Swap(TestMapLite* other) {
  if (other == this) return;
  if (GetArenaNoVirtual() == other->GetArenaNoVirtual()) {
    InternalSwap(other);
  } else {
    TestMapLite temp;
    temp.UnsafeMergeFrom(*this);
    CopyFrom(*other);
    other->CopyFrom(temp);
  }
}
void TestMapLite::UnsafeArenaSwap(TestMapLite* other) {
  if (other == this) return;
  GOOGLE_DCHECK(GetArenaNoVirtual() == other->GetArenaNoVirtual());
  InternalSwap(other);
}
void TestMapLite::InternalSwap(TestMapLite* other) {
  map_int32_int32_.Swap(&other->map_int32_int32_);
  map_int64_int64_.Swap(&other->map_int64_int64_);
  map_uint32_uint32_.Swap(&other->map_uint32_uint32_);
  map_uint64_uint64_.Swap(&other->map_uint64_uint64_);
  map_sint32_sint32_.Swap(&other->map_sint32_sint32_);
  map_sint64_sint64_.Swap(&other->map_sint64_sint64_);
  map_fixed32_fixed32_.Swap(&other->map_fixed32_fixed32_);
  map_fixed64_fixed64_.Swap(&other->map_fixed64_fixed64_);
  map_sfixed32_sfixed32_.Swap(&other->map_sfixed32_sfixed32_);
  map_sfixed64_sfixed64_.Swap(&other->map_sfixed64_sfixed64_);
  map_int32_float_.Swap(&other->map_int32_float_);
  map_int32_double_.Swap(&other->map_int32_double_);
  map_bool_bool_.Swap(&other->map_bool_bool_);
  map_string_string_.Swap(&other->map_string_string_);
  map_int32_bytes_.Swap(&other->map_int32_bytes_);
  map_int32_enum_.Swap(&other->map_int32_enum_);
  map_int32_foreign_message_.Swap(&other->map_int32_foreign_message_);
  teboring_.Swap(&other->teboring_);
  std::swap(_has_bits_[0], other->_has_bits_[0]);
  _unknown_fields_.Swap(&other->_unknown_fields_);
  std::swap(_cached_size_, other->_cached_size_);
}

::std::string TestMapLite::GetTypeName() const {
  return "protobuf_unittest.TestMapLite";
}

#if PROTOBUF_INLINE_NOT_IN_HEADERS
// TestMapLite

// map<int32, int32> map_int32_int32 = 1;
int TestMapLite::map_int32_int32_size() const {
  return map_int32_int32_.size();
}
void TestMapLite::clear_map_int32_int32() {
  map_int32_int32_.Clear();
}
 const ::google::protobuf::Map< ::google::protobuf::int32, ::google::protobuf::int32 >&
TestMapLite::map_int32_int32() const {
  // @@protoc_insertion_point(field_map:protobuf_unittest.TestMapLite.map_int32_int32)
  return map_int32_int32_.GetMap();
}
 ::google::protobuf::Map< ::google::protobuf::int32, ::google::protobuf::int32 >*
TestMapLite::mutable_map_int32_int32() {
  // @@protoc_insertion_point(field_mutable_map:protobuf_unittest.TestMapLite.map_int32_int32)
  return map_int32_int32_.MutableMap();
}

// map<int64, int64> map_int64_int64 = 2;
int TestMapLite::map_int64_int64_size() const {
  return map_int64_int64_.size();
}
void TestMapLite::clear_map_int64_int64() {
  map_int64_int64_.Clear();
}
 const ::google::protobuf::Map< ::google::protobuf::int64, ::google::protobuf::int64 >&
TestMapLite::map_int64_int64() const {
  // @@protoc_insertion_point(field_map:protobuf_unittest.TestMapLite.map_int64_int64)
  return map_int64_int64_.GetMap();
}
 ::google::protobuf::Map< ::google::protobuf::int64, ::google::protobuf::int64 >*
TestMapLite::mutable_map_int64_int64() {
  // @@protoc_insertion_point(field_mutable_map:protobuf_unittest.TestMapLite.map_int64_int64)
  return map_int64_int64_.MutableMap();
}

// map<uint32, uint32> map_uint32_uint32 = 3;
int TestMapLite::map_uint32_uint32_size() const {
  return map_uint32_uint32_.size();
}
void TestMapLite::clear_map_uint32_uint32() {
  map_uint32_uint32_.Clear();
}
 const ::google::protobuf::Map< ::google::protobuf::uint32, ::google::protobuf::uint32 >&
TestMapLite::map_uint32_uint32() const {
  // @@protoc_insertion_point(field_map:protobuf_unittest.TestMapLite.map_uint32_uint32)
  return map_uint32_uint32_.GetMap();
}
 ::google::protobuf::Map< ::google::protobuf::uint32, ::google::protobuf::uint32 >*
TestMapLite::mutable_map_uint32_uint32() {
  // @@protoc_insertion_point(field_mutable_map:protobuf_unittest.TestMapLite.map_uint32_uint32)
  return map_uint32_uint32_.MutableMap();
}

// map<uint64, uint64> map_uint64_uint64 = 4;
int TestMapLite::map_uint64_uint64_size() const {
  return map_uint64_uint64_.size();
}
void TestMapLite::clear_map_uint64_uint64() {
  map_uint64_uint64_.Clear();
}
 const ::google::protobuf::Map< ::google::protobuf::uint64, ::google::protobuf::uint64 >&
TestMapLite::map_uint64_uint64() const {
  // @@protoc_insertion_point(field_map:protobuf_unittest.TestMapLite.map_uint64_uint64)
  return map_uint64_uint64_.GetMap();
}
 ::google::protobuf::Map< ::google::protobuf::uint64, ::google::protobuf::uint64 >*
TestMapLite::mutable_map_uint64_uint64() {
  // @@protoc_insertion_point(field_mutable_map:protobuf_unittest.TestMapLite.map_uint64_uint64)
  return map_uint64_uint64_.MutableMap();
}

// map<sint32, sint32> map_sint32_sint32 = 5;
int TestMapLite::map_sint32_sint32_size() const {
  return map_sint32_sint32_.size();
}
void TestMapLite::clear_map_sint32_sint32() {
  map_sint32_sint32_.Clear();
}
 const ::google::protobuf::Map< ::google::protobuf::int32, ::google::protobuf::int32 >&
TestMapLite::map_sint32_sint32() const {
  // @@protoc_insertion_point(field_map:protobuf_unittest.TestMapLite.map_sint32_sint32)
  return map_sint32_sint32_.GetMap();
}
 ::google::protobuf::Map< ::google::protobuf::int32, ::google::protobuf::int32 >*
TestMapLite::mutable_map_sint32_sint32() {
  // @@protoc_insertion_point(field_mutable_map:protobuf_unittest.TestMapLite.map_sint32_sint32)
  return map_sint32_sint32_.MutableMap();
}

// map<sint64, sint64> map_sint64_sint64 = 6;
int TestMapLite::map_sint64_sint64_size() const {
  return map_sint64_sint64_.size();
}
void TestMapLite::clear_map_sint64_sint64() {
  map_sint64_sint64_.Clear();
}
 const ::google::protobuf::Map< ::google::protobuf::int64, ::google::protobuf::int64 >&
TestMapLite::map_sint64_sint64() const {
  // @@protoc_insertion_point(field_map:protobuf_unittest.TestMapLite.map_sint64_sint64)
  return map_sint64_sint64_.GetMap();
}
 ::google::protobuf::Map< ::google::protobuf::int64, ::google::protobuf::int64 >*
TestMapLite::mutable_map_sint64_sint64() {
  // @@protoc_insertion_point(field_mutable_map:protobuf_unittest.TestMapLite.map_sint64_sint64)
  return map_sint64_sint64_.MutableMap();
}

// map<fixed32, fixed32> map_fixed32_fixed32 = 7;
int TestMapLite::map_fixed32_fixed32_size() const {
  return map_fixed32_fixed32_.size();
}
void TestMapLite::clear_map_fixed32_fixed32() {
  map_fixed32_fixed32_.Clear();
}
 const ::google::protobuf::Map< ::google::protobuf::uint32, ::google::protobuf::uint32 >&
TestMapLite::map_fixed32_fixed32() const {
  // @@protoc_insertion_point(field_map:protobuf_unittest.TestMapLite.map_fixed32_fixed32)
  return map_fixed32_fixed32_.GetMap();
}
 ::google::protobuf::Map< ::google::protobuf::uint32, ::google::protobuf::uint32 >*
TestMapLite::mutable_map_fixed32_fixed32() {
  // @@protoc_insertion_point(field_mutable_map:protobuf_unittest.TestMapLite.map_fixed32_fixed32)
  return map_fixed32_fixed32_.MutableMap();
}

// map<fixed64, fixed64> map_fixed64_fixed64 = 8;
int TestMapLite::map_fixed64_fixed64_size() const {
  return map_fixed64_fixed64_.size();
}
void TestMapLite::clear_map_fixed64_fixed64() {
  map_fixed64_fixed64_.Clear();
}
 const ::google::protobuf::Map< ::google::protobuf::uint64, ::google::protobuf::uint64 >&
TestMapLite::map_fixed64_fixed64() const {
  // @@protoc_insertion_point(field_map:protobuf_unittest.TestMapLite.map_fixed64_fixed64)
  return map_fixed64_fixed64_.GetMap();
}
 ::google::protobuf::Map< ::google::protobuf::uint64, ::google::protobuf::uint64 >*
TestMapLite::mutable_map_fixed64_fixed64() {
  // @@protoc_insertion_point(field_mutable_map:protobuf_unittest.TestMapLite.map_fixed64_fixed64)
  return map_fixed64_fixed64_.MutableMap();
}

// map<sfixed32, sfixed32> map_sfixed32_sfixed32 = 9;
int TestMapLite::map_sfixed32_sfixed32_size() const {
  return map_sfixed32_sfixed32_.size();
}
void TestMapLite::clear_map_sfixed32_sfixed32() {
  map_sfixed32_sfixed32_.Clear();
}
 const ::google::protobuf::Map< ::google::protobuf::int32, ::google::protobuf::int32 >&
TestMapLite::map_sfixed32_sfixed32() const {
  // @@protoc_insertion_point(field_map:protobuf_unittest.TestMapLite.map_sfixed32_sfixed32)
  return map_sfixed32_sfixed32_.GetMap();
}
 ::google::protobuf::Map< ::google::protobuf::int32, ::google::protobuf::int32 >*
TestMapLite::mutable_map_sfixed32_sfixed32() {
  // @@protoc_insertion_point(field_mutable_map:protobuf_unittest.TestMapLite.map_sfixed32_sfixed32)
  return map_sfixed32_sfixed32_.MutableMap();
}

// map<sfixed64, sfixed64> map_sfixed64_sfixed64 = 10;
int TestMapLite::map_sfixed64_sfixed64_size() const {
  return map_sfixed64_sfixed64_.size();
}
void TestMapLite::clear_map_sfixed64_sfixed64() {
  map_sfixed64_sfixed64_.Clear();
}
 const ::google::protobuf::Map< ::google::protobuf::int64, ::google::protobuf::int64 >&
TestMapLite::map_sfixed64_sfixed64() const {
  // @@protoc_insertion_point(field_map:protobuf_unittest.TestMapLite.map_sfixed64_sfixed64)
  return map_sfixed64_sfixed64_.GetMap();
}
 ::google::protobuf::Map< ::google::protobuf::int64, ::google::protobuf::int64 >*
TestMapLite::mutable_map_sfixed64_sfixed64() {
  // @@protoc_insertion_point(field_mutable_map:protobuf_unittest.TestMapLite.map_sfixed64_sfixed64)
  return map_sfixed64_sfixed64_.MutableMap();
}

// map<int32, float> map_int32_float = 11;
int TestMapLite::map_int32_float_size() const {
  return map_int32_float_.size();
}
void TestMapLite::clear_map_int32_float() {
  map_int32_float_.Clear();
}
 const ::google::protobuf::Map< ::google::protobuf::int32, float >&
TestMapLite::map_int32_float() const {
  // @@protoc_insertion_point(field_map:protobuf_unittest.TestMapLite.map_int32_float)
  return map_int32_float_.GetMap();
}
 ::google::protobuf::Map< ::google::protobuf::int32, float >*
TestMapLite::mutable_map_int32_float() {
  // @@protoc_insertion_point(field_mutable_map:protobuf_unittest.TestMapLite.map_int32_float)
  return map_int32_float_.MutableMap();
}

// map<int32, double> map_int32_double = 12;
int TestMapLite::map_int32_double_size() const {
  return map_int32_double_.size();
}
void TestMapLite::clear_map_int32_double() {
  map_int32_double_.Clear();
}
 const ::google::protobuf::Map< ::google::protobuf::int32, double >&
TestMapLite::map_int32_double() const {
  // @@protoc_insertion_point(field_map:protobuf_unittest.TestMapLite.map_int32_double)
  return map_int32_double_.GetMap();
}
 ::google::protobuf::Map< ::google::protobuf::int32, double >*
TestMapLite::mutable_map_int32_double() {
  // @@protoc_insertion_point(field_mutable_map:protobuf_unittest.TestMapLite.map_int32_double)
  return map_int32_double_.MutableMap();
}

// map<bool, bool> map_bool_bool = 13;
int TestMapLite::map_bool_bool_size() const {
  return map_bool_bool_.size();
}
void TestMapLite::clear_map_bool_bool() {
  map_bool_bool_.Clear();
}
 const ::google::protobuf::Map< bool, bool >&
TestMapLite::map_bool_bool() const {
  // @@protoc_insertion_point(field_map:protobuf_unittest.TestMapLite.map_bool_bool)
  return map_bool_bool_.GetMap();
}
 ::google::protobuf::Map< bool, bool >*
TestMapLite::mutable_map_bool_bool() {
  // @@protoc_insertion_point(field_mutable_map:protobuf_unittest.TestMapLite.map_bool_bool)
  return map_bool_bool_.MutableMap();
}

// map<string, string> map_string_string = 14;
int TestMapLite::map_string_string_size() const {
  return map_string_string_.size();
}
void TestMapLite::clear_map_string_string() {
  map_string_string_.Clear();
}
 const ::google::protobuf::Map< ::std::string, ::std::string >&
TestMapLite::map_string_string() const {
  // @@protoc_insertion_point(field_map:protobuf_unittest.TestMapLite.map_string_string)
  return map_string_string_.GetMap();
}
 ::google::protobuf::Map< ::std::string, ::std::string >*
TestMapLite::mutable_map_string_string() {
  // @@protoc_insertion_point(field_mutable_map:protobuf_unittest.TestMapLite.map_string_string)
  return map_string_string_.MutableMap();
}

// map<int32, bytes> map_int32_bytes = 15;
int TestMapLite::map_int32_bytes_size() const {
  return map_int32_bytes_.size();
}
void TestMapLite::clear_map_int32_bytes() {
  map_int32_bytes_.Clear();
}
 const ::google::protobuf::Map< ::google::protobuf::int32, ::std::string >&
TestMapLite::map_int32_bytes() const {
  // @@protoc_insertion_point(field_map:protobuf_unittest.TestMapLite.map_int32_bytes)
  return map_int32_bytes_.GetMap();
}
 ::google::protobuf::Map< ::google::protobuf::int32, ::std::string >*
TestMapLite::mutable_map_int32_bytes() {
  // @@protoc_insertion_point(field_mutable_map:protobuf_unittest.TestMapLite.map_int32_bytes)
  return map_int32_bytes_.MutableMap();
}

// map<int32, .protobuf_unittest.MapEnumLite> map_int32_enum = 16;
int TestMapLite::map_int32_enum_size() const {
  return map_int32_enum_.size();
}
void TestMapLite::clear_map_int32_enum() {
  map_int32_enum_.Clear();
}
 const ::google::protobuf::Map< ::google::protobuf::int32, ::protobuf_unittest::MapEnumLite >&
TestMapLite::map_int32_enum() const {
  // @@protoc_insertion_point(field_map:protobuf_unittest.TestMapLite.map_int32_enum)
  return map_int32_enum_.GetMap();
}
 ::google::protobuf::Map< ::google::protobuf::int32, ::protobuf_unittest::MapEnumLite >*
TestMapLite::mutable_map_int32_enum() {
  // @@protoc_insertion_point(field_mutable_map:protobuf_unittest.TestMapLite.map_int32_enum)
  return map_int32_enum_.MutableMap();
}

// map<int32, .protobuf_unittest.ForeignMessageLite> map_int32_foreign_message = 17;
int TestMapLite::map_int32_foreign_message_size() const {
  return map_int32_foreign_message_.size();
}
void TestMapLite::clear_map_int32_foreign_message() {
  map_int32_foreign_message_.Clear();
}
 const ::google::protobuf::Map< ::google::protobuf::int32, ::protobuf_unittest::ForeignMessageLite >&
TestMapLite::map_int32_foreign_message() const {
  // @@protoc_insertion_point(field_map:protobuf_unittest.TestMapLite.map_int32_foreign_message)
  return map_int32_foreign_message_.GetMap();
}
 ::google::protobuf::Map< ::google::protobuf::int32, ::protobuf_unittest::ForeignMessageLite >*
TestMapLite::mutable_map_int32_foreign_message() {
  // @@protoc_insertion_point(field_mutable_map:protobuf_unittest.TestMapLite.map_int32_foreign_message)
  return map_int32_foreign_message_.MutableMap();
}

// map<int32, int32> teboring = 18;
int TestMapLite::teboring_size() const {
  return teboring_.size();
}
void TestMapLite::clear_teboring() {
  teboring_.Clear();
}
 const ::google::protobuf::Map< ::google::protobuf::int32, ::google::protobuf::int32 >&
TestMapLite::teboring() const {
  // @@protoc_insertion_point(field_map:protobuf_unittest.TestMapLite.teboring)
  return teboring_.GetMap();
}
 ::google::protobuf::Map< ::google::protobuf::int32, ::google::protobuf::int32 >*
TestMapLite::mutable_teboring() {
  // @@protoc_insertion_point(field_mutable_map:protobuf_unittest.TestMapLite.teboring)
  return teboring_.MutableMap();
}

inline const TestMapLite* TestMapLite::internal_default_instance() {
  return &TestMapLite_default_instance_.get();
}
#endif  // PROTOBUF_INLINE_NOT_IN_HEADERS

// ===================================================================

static ::std::string* MutableUnknownFieldsForTestArenaMapLite(
    TestArenaMapLite* ptr) {
  return ptr->mutable_unknown_fields();
}

#if !defined(_MSC_VER) || _MSC_VER >= 1900
const int TestArenaMapLite::kMapInt32Int32FieldNumber;
const int TestArenaMapLite::kMapInt64Int64FieldNumber;
const int TestArenaMapLite::kMapUint32Uint32FieldNumber;
const int TestArenaMapLite::kMapUint64Uint64FieldNumber;
const int TestArenaMapLite::kMapSint32Sint32FieldNumber;
const int TestArenaMapLite::kMapSint64Sint64FieldNumber;
const int TestArenaMapLite::kMapFixed32Fixed32FieldNumber;
const int TestArenaMapLite::kMapFixed64Fixed64FieldNumber;
const int TestArenaMapLite::kMapSfixed32Sfixed32FieldNumber;
const int TestArenaMapLite::kMapSfixed64Sfixed64FieldNumber;
const int TestArenaMapLite::kMapInt32FloatFieldNumber;
const int TestArenaMapLite::kMapInt32DoubleFieldNumber;
const int TestArenaMapLite::kMapBoolBoolFieldNumber;
const int TestArenaMapLite::kMapStringStringFieldNumber;
const int TestArenaMapLite::kMapInt32BytesFieldNumber;
const int TestArenaMapLite::kMapInt32EnumFieldNumber;
const int TestArenaMapLite::kMapInt32ForeignMessageFieldNumber;
const int TestArenaMapLite::kMapInt32ForeignMessageNoArenaFieldNumber;
#endif  // !defined(_MSC_VER) || _MSC_VER >= 1900

TestArenaMapLite::TestArenaMapLite()
  : ::google::protobuf::MessageLite(), _arena_ptr_(NULL) {
  if (this != internal_default_instance()) protobuf_InitDefaults_google_2fprotobuf_2fmap_5flite_5funittest_2eproto();
  SharedCtor();
  // @@protoc_insertion_point(constructor:protobuf_unittest.TestArenaMapLite)
}
TestArenaMapLite::TestArenaMapLite(::google::protobuf::Arena* arena)
  : ::google::protobuf::MessageLite(),
  _arena_ptr_(arena),
  map_int32_int32_(arena),
  map_int64_int64_(arena),
  map_uint32_uint32_(arena),
  map_uint64_uint64_(arena),
  map_sint32_sint32_(arena),
  map_sint64_sint64_(arena),
  map_fixed32_fixed32_(arena),
  map_fixed64_fixed64_(arena),
  map_sfixed32_sfixed32_(arena),
  map_sfixed64_sfixed64_(arena),
  map_int32_float_(arena),
  map_int32_double_(arena),
  map_bool_bool_(arena),
  map_string_string_(arena),
  map_int32_bytes_(arena),
  map_int32_enum_(arena),
  map_int32_foreign_message_(arena),
  map_int32_foreign_message_no_arena_(arena) {
#ifdef GOOGLE_PROTOBUF_NO_STATIC_INITIALIZER
  protobuf_InitDefaults_google_2fprotobuf_2fmap_5flite_5funittest_2eproto();
#endif  // GOOGLE_PROTOBUF_NO_STATIC_INITIALIZER
  SharedCtor();
  RegisterArenaDtor(arena);
  // @@protoc_insertion_point(arena_constructor:protobuf_unittest.TestArenaMapLite)
}

void TestArenaMapLite::InitAsDefaultInstance() {
}

TestArenaMapLite::TestArenaMapLite(const TestArenaMapLite& from)
  : ::google::protobuf::MessageLite(),
    _arena_ptr_(NULL) {
  SharedCtor();
  UnsafeMergeFrom(from);
  // @@protoc_insertion_point(copy_constructor:protobuf_unittest.TestArenaMapLite)
}

void TestArenaMapLite::SharedCtor() {
  _cached_size_ = 0;
  _unknown_fields_.UnsafeSetDefault(
      &::google::protobuf::internal::GetEmptyStringAlreadyInited());
}

TestArenaMapLite::~TestArenaMapLite() {
  // @@protoc_insertion_point(destructor:protobuf_unittest.TestArenaMapLite)
  SharedDtor();
}

void TestArenaMapLite::SharedDtor() {
  ::google::protobuf::Arena* arena = GetArenaNoVirtual();
  if (arena != NULL) {
    return;
  }

  _unknown_fields_.Destroy(
      &::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      arena);
}

void TestArenaMapLite::ArenaDtor(void* object) {
  TestArenaMapLite* _this = reinterpret_cast< TestArenaMapLite* >(object);
  (void)_this;
}
void TestArenaMapLite::RegisterArenaDtor(::google::protobuf::Arena* arena) {
}
void TestArenaMapLite::SetCachedSize(int size) const {
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
}
const TestArenaMapLite& TestArenaMapLite::default_instance() {
  protobuf_InitDefaults_google_2fprotobuf_2fmap_5flite_5funittest_2eproto();
  return *internal_default_instance();
}

::google::protobuf::internal::ExplicitlyConstructed<TestArenaMapLite> TestArenaMapLite_default_instance_;

TestArenaMapLite* TestArenaMapLite::New(::google::protobuf::Arena* arena) const {
  return ::google::protobuf::Arena::CreateMessage<TestArenaMapLite>(arena);
}

void TestArenaMapLite::Clear() {
// @@protoc_insertion_point(message_clear_start:protobuf_unittest.TestArenaMapLite)
  map_int32_int32_.Clear();
  map_int64_int64_.Clear();
  map_uint32_uint32_.Clear();
  map_uint64_uint64_.Clear();
  map_sint32_sint32_.Clear();
  map_sint64_sint64_.Clear();
  map_fixed32_fixed32_.Clear();
  map_fixed64_fixed64_.Clear();
  map_sfixed32_sfixed32_.Clear();
  map_sfixed64_sfixed64_.Clear();
  map_int32_float_.Clear();
  map_int32_double_.Clear();
  map_bool_bool_.Clear();
  map_string_string_.Clear();
  map_int32_bytes_.Clear();
  map_int32_enum_.Clear();
  map_int32_foreign_message_.Clear();
  map_int32_foreign_message_no_arena_.Clear();
  _has_bits_.Clear();
  _unknown_fields_.ClearToEmpty(
      &::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      GetArenaNoVirtual());
}

bool TestArenaMapLite::MergePartialFromCodedStream(
    ::google::protobuf::io::CodedInputStream* input) {
#define DO_(EXPRESSION) if (!GOOGLE_PREDICT_TRUE(EXPRESSION)) goto failure
  ::google::protobuf::uint32 tag;
  ::google::protobuf::io::LazyStringOutputStream unknown_fields_string(
      ::google::protobuf::NewPermanentCallback(
          &MutableUnknownFieldsForTestArenaMapLite, this));
  ::google::protobuf::io::CodedOutputStream unknown_fields_stream(
      &unknown_fields_string, false);
  // @@protoc_insertion_point(parse_start:protobuf_unittest.TestArenaMapLite)
  for (;;) {
    ::std::pair< ::google::protobuf::uint32, bool> p = input->ReadTagWithCutoff(16383);
    tag = p.first;
    if (!p.second) goto handle_unusual;
    switch (::google::protobuf::internal::WireFormatLite::GetTagFieldNumber(tag)) {
      // map<int32, int32> map_int32_int32 = 1;
      case 1: {
        if (tag == 10) {
          DO_(input->IncrementRecursionDepth());
         parse_loop_map_int32_int32:
          TestArenaMapLite_MapInt32Int32Entry::Parser< ::google::protobuf::internal::MapFieldLite<
              ::google::protobuf::int32, ::google::protobuf::int32,
              ::google::protobuf::internal::WireFormatLite::TYPE_INT32,
              ::google::protobuf::internal::WireFormatLite::TYPE_INT32,
              0 >,
            ::google::protobuf::Map< ::google::protobuf::int32, ::google::protobuf::int32 > > parser(&map_int32_int32_);
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessageNoVirtual(
              input, &parser));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(10)) goto parse_loop_map_int32_int32;
        if (input->ExpectTag(18)) goto parse_loop_map_int64_int64;
        input->UnsafeDecrementRecursionDepth();
        break;
      }

      // map<int64, int64> map_int64_int64 = 2;
      case 2: {
        if (tag == 18) {
          DO_(input->IncrementRecursionDepth());
         parse_loop_map_int64_int64:
          TestArenaMapLite_MapInt64Int64Entry::Parser< ::google::protobuf::internal::MapFieldLite<
              ::google::protobuf::int64, ::google::protobuf::int64,
              ::google::protobuf::internal::WireFormatLite::TYPE_INT64,
              ::google::protobuf::internal::WireFormatLite::TYPE_INT64,
              0 >,
            ::google::protobuf::Map< ::google::protobuf::int64, ::google::protobuf::int64 > > parser(&map_int64_int64_);
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessageNoVirtual(
              input, &parser));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(18)) goto parse_loop_map_int64_int64;
        if (input->ExpectTag(26)) goto parse_loop_map_uint32_uint32;
        input->UnsafeDecrementRecursionDepth();
        break;
      }

      // map<uint32, uint32> map_uint32_uint32 = 3;
      case 3: {
        if (tag == 26) {
          DO_(input->IncrementRecursionDepth());
         parse_loop_map_uint32_uint32:
          TestArenaMapLite_MapUint32Uint32Entry::Parser< ::google::protobuf::internal::MapFieldLite<
              ::google::protobuf::uint32, ::google::protobuf::uint32,
              ::google::protobuf::internal::WireFormatLite::TYPE_UINT32,
              ::google::protobuf::internal::WireFormatLite::TYPE_UINT32,
              0 >,
            ::google::protobuf::Map< ::google::protobuf::uint32, ::google::protobuf::uint32 > > parser(&map_uint32_uint32_);
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessageNoVirtual(
              input, &parser));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(26)) goto parse_loop_map_uint32_uint32;
        if (input->ExpectTag(34)) goto parse_loop_map_uint64_uint64;
        input->UnsafeDecrementRecursionDepth();
        break;
      }

      // map<uint64, uint64> map_uint64_uint64 = 4;
      case 4: {
        if (tag == 34) {
          DO_(input->IncrementRecursionDepth());
         parse_loop_map_uint64_uint64:
          TestArenaMapLite_MapUint64Uint64Entry::Parser< ::google::protobuf::internal::MapFieldLite<
              ::google::protobuf::uint64, ::google::protobuf::uint64,
              ::google::protobuf::internal::WireFormatLite::TYPE_UINT64,
              ::google::protobuf::internal::WireFormatLite::TYPE_UINT64,
              0 >,
            ::google::protobuf::Map< ::google::protobuf::uint64, ::google::protobuf::uint64 > > parser(&map_uint64_uint64_);
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessageNoVirtual(
              input, &parser));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(34)) goto parse_loop_map_uint64_uint64;
        if (input->ExpectTag(42)) goto parse_loop_map_sint32_sint32;
        input->UnsafeDecrementRecursionDepth();
        break;
      }

      // map<sint32, sint32> map_sint32_sint32 = 5;
      case 5: {
        if (tag == 42) {
          DO_(input->IncrementRecursionDepth());
         parse_loop_map_sint32_sint32:
          TestArenaMapLite_MapSint32Sint32Entry::Parser< ::google::protobuf::internal::MapFieldLite<
              ::google::protobuf::int32, ::google::protobuf::int32,
              ::google::protobuf::internal::WireFormatLite::TYPE_SINT32,
              ::google::protobuf::internal::WireFormatLite::TYPE_SINT32,
              0 >,
            ::google::protobuf::Map< ::google::protobuf::int32, ::google::protobuf::int32 > > parser(&map_sint32_sint32_);
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessageNoVirtual(
              input, &parser));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(42)) goto parse_loop_map_sint32_sint32;
        if (input->ExpectTag(50)) goto parse_loop_map_sint64_sint64;
        input->UnsafeDecrementRecursionDepth();
        break;
      }

      // map<sint64, sint64> map_sint64_sint64 = 6;
      case 6: {
        if (tag == 50) {
          DO_(input->IncrementRecursionDepth());
         parse_loop_map_sint64_sint64:
          TestArenaMapLite_MapSint64Sint64Entry::Parser< ::google::protobuf::internal::MapFieldLite<
              ::google::protobuf::int64, ::google::protobuf::int64,
              ::google::protobuf::internal::WireFormatLite::TYPE_SINT64,
              ::google::protobuf::internal::WireFormatLite::TYPE_SINT64,
              0 >,
            ::google::protobuf::Map< ::google::protobuf::int64, ::google::protobuf::int64 > > parser(&map_sint64_sint64_);
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessageNoVirtual(
              input, &parser));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(50)) goto parse_loop_map_sint64_sint64;
        if (input->ExpectTag(58)) goto parse_loop_map_fixed32_fixed32;
        input->UnsafeDecrementRecursionDepth();
        break;
      }

      // map<fixed32, fixed32> map_fixed32_fixed32 = 7;
      case 7: {
        if (tag == 58) {
          DO_(input->IncrementRecursionDepth());
         parse_loop_map_fixed32_fixed32:
          TestArenaMapLite_MapFixed32Fixed32Entry::Parser< ::google::protobuf::internal::MapFieldLite<
              ::google::protobuf::uint32, ::google::protobuf::uint32,
              ::google::protobuf::internal::WireFormatLite::TYPE_FIXED32,
              ::google::protobuf::internal::WireFormatLite::TYPE_FIXED32,
              0 >,
            ::google::protobuf::Map< ::google::protobuf::uint32, ::google::protobuf::uint32 > > parser(&map_fixed32_fixed32_);
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessageNoVirtual(
              input, &parser));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(58)) goto parse_loop_map_fixed32_fixed32;
        if (input->ExpectTag(66)) goto parse_loop_map_fixed64_fixed64;
        input->UnsafeDecrementRecursionDepth();
        break;
      }

      // map<fixed64, fixed64> map_fixed64_fixed64 = 8;
      case 8: {
        if (tag == 66) {
          DO_(input->IncrementRecursionDepth());
         parse_loop_map_fixed64_fixed64:
          TestArenaMapLite_MapFixed64Fixed64Entry::Parser< ::google::protobuf::internal::MapFieldLite<
              ::google::protobuf::uint64, ::google::protobuf::uint64,
              ::google::protobuf::internal::WireFormatLite::TYPE_FIXED64,
              ::google::protobuf::internal::WireFormatLite::TYPE_FIXED64,
              0 >,
            ::google::protobuf::Map< ::google::protobuf::uint64, ::google::protobuf::uint64 > > parser(&map_fixed64_fixed64_);
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessageNoVirtual(
              input, &parser));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(66)) goto parse_loop_map_fixed64_fixed64;
        if (input->ExpectTag(74)) goto parse_loop_map_sfixed32_sfixed32;
        input->UnsafeDecrementRecursionDepth();
        break;
      }

      // map<sfixed32, sfixed32> map_sfixed32_sfixed32 = 9;
      case 9: {
        if (tag == 74) {
          DO_(input->IncrementRecursionDepth());
         parse_loop_map_sfixed32_sfixed32:
          TestArenaMapLite_MapSfixed32Sfixed32Entry::Parser< ::google::protobuf::internal::MapFieldLite<
              ::google::protobuf::int32, ::google::protobuf::int32,
              ::google::protobuf::internal::WireFormatLite::TYPE_SFIXED32,
              ::google::protobuf::internal::WireFormatLite::TYPE_SFIXED32,
              0 >,
            ::google::protobuf::Map< ::google::protobuf::int32, ::google::protobuf::int32 > > parser(&map_sfixed32_sfixed32_);
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessageNoVirtual(
              input, &parser));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(74)) goto parse_loop_map_sfixed32_sfixed32;
        if (input->ExpectTag(82)) goto parse_loop_map_sfixed64_sfixed64;
        input->UnsafeDecrementRecursionDepth();
        break;
      }

      // map<sfixed64, sfixed64> map_sfixed64_sfixed64 = 10;
      case 10: {
        if (tag == 82) {
          DO_(input->IncrementRecursionDepth());
         parse_loop_map_sfixed64_sfixed64:
          TestArenaMapLite_MapSfixed64Sfixed64Entry::Parser< ::google::protobuf::internal::MapFieldLite<
              ::google::protobuf::int64, ::google::protobuf::int64,
              ::google::protobuf::internal::WireFormatLite::TYPE_SFIXED64,
              ::google::protobuf::internal::WireFormatLite::TYPE_SFIXED64,
              0 >,
            ::google::protobuf::Map< ::google::protobuf::int64, ::google::protobuf::int64 > > parser(&map_sfixed64_sfixed64_);
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessageNoVirtual(
              input, &parser));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(82)) goto parse_loop_map_sfixed64_sfixed64;
        if (input->ExpectTag(90)) goto parse_loop_map_int32_float;
        input->UnsafeDecrementRecursionDepth();
        break;
      }

      // map<int32, float> map_int32_float = 11;
      case 11: {
        if (tag == 90) {
          DO_(input->IncrementRecursionDepth());
         parse_loop_map_int32_float:
          TestArenaMapLite_MapInt32FloatEntry::Parser< ::google::protobuf::internal::MapFieldLite<
              ::google::protobuf::int32, float,
              ::google::protobuf::internal::WireFormatLite::TYPE_INT32,
              ::google::protobuf::internal::WireFormatLite::TYPE_FLOAT,
              0 >,
            ::google::protobuf::Map< ::google::protobuf::int32, float > > parser(&map_int32_float_);
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessageNoVirtual(
              input, &parser));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(90)) goto parse_loop_map_int32_float;
        if (input->ExpectTag(98)) goto parse_loop_map_int32_double;
        input->UnsafeDecrementRecursionDepth();
        break;
      }

      // map<int32, double> map_int32_double = 12;
      case 12: {
        if (tag == 98) {
          DO_(input->IncrementRecursionDepth());
         parse_loop_map_int32_double:
          TestArenaMapLite_MapInt32DoubleEntry::Parser< ::google::protobuf::internal::MapFieldLite<
              ::google::protobuf::int32, double,
              ::google::protobuf::internal::WireFormatLite::TYPE_INT32,
              ::google::protobuf::internal::WireFormatLite::TYPE_DOUBLE,
              0 >,
            ::google::protobuf::Map< ::google::protobuf::int32, double > > parser(&map_int32_double_);
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessageNoVirtual(
              input, &parser));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(98)) goto parse_loop_map_int32_double;
        if (input->ExpectTag(106)) goto parse_loop_map_bool_bool;
        input->UnsafeDecrementRecursionDepth();
        break;
      }

      // map<bool, bool> map_bool_bool = 13;
      case 13: {
        if (tag == 106) {
          DO_(input->IncrementRecursionDepth());
         parse_loop_map_bool_bool:
          TestArenaMapLite_MapBoolBoolEntry::Parser< ::google::protobuf::internal::MapFieldLite<
              bool, bool,
              ::google::protobuf::internal::WireFormatLite::TYPE_BOOL,
              ::google::protobuf::internal::WireFormatLite::TYPE_BOOL,
              0 >,
            ::google::protobuf::Map< bool, bool > > parser(&map_bool_bool_);
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessageNoVirtual(
              input, &parser));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(106)) goto parse_loop_map_bool_bool;
        if (input->ExpectTag(114)) goto parse_loop_map_string_string;
        input->UnsafeDecrementRecursionDepth();
        break;
      }

      // map<string, string> map_string_string = 14;
      case 14: {
        if (tag == 114) {
          DO_(input->IncrementRecursionDepth());
         parse_loop_map_string_string:
          TestArenaMapLite_MapStringStringEntry::Parser< ::google::protobuf::internal::MapFieldLite<
              ::std::string, ::std::string,
              ::google::protobuf::internal::WireFormatLite::TYPE_STRING,
              ::google::protobuf::internal::WireFormatLite::TYPE_STRING,
              0 >,
            ::google::protobuf::Map< ::std::string, ::std::string > > parser(&map_string_string_);
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessageNoVirtual(
              input, &parser));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(114)) goto parse_loop_map_string_string;
        if (input->ExpectTag(122)) goto parse_loop_map_int32_bytes;
        input->UnsafeDecrementRecursionDepth();
        break;
      }

      // map<int32, bytes> map_int32_bytes = 15;
      case 15: {
        if (tag == 122) {
          DO_(input->IncrementRecursionDepth());
         parse_loop_map_int32_bytes:
          TestArenaMapLite_MapInt32BytesEntry::Parser< ::google::protobuf::internal::MapFieldLite<
              ::google::protobuf::int32, ::std::string,
              ::google::protobuf::internal::WireFormatLite::TYPE_INT32,
              ::google::protobuf::internal::WireFormatLite::TYPE_BYTES,
              0 >,
            ::google::protobuf::Map< ::google::protobuf::int32, ::std::string > > parser(&map_int32_bytes_);
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessageNoVirtual(
              input, &parser));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(122)) goto parse_loop_map_int32_bytes;
        if (input->ExpectTag(130)) goto parse_loop_map_int32_enum;
        input->UnsafeDecrementRecursionDepth();
        break;
      }

      // map<int32, .protobuf_unittest.MapEnumLite> map_int32_enum = 16;
      case 16: {
        if (tag == 130) {
          DO_(input->IncrementRecursionDepth());
         parse_loop_map_int32_enum:
          ::google::protobuf::scoped_ptr<TestArenaMapLite_MapInt32EnumEntry> entry(map_int32_enum_.NewEntry());
          {
            ::std::string data;
            DO_(::google::protobuf::internal::WireFormatLite::ReadString(input, &data));
            DO_(entry->ParseFromString(data));
            if (::protobuf_unittest::MapEnumLite_IsValid(*entry->mutable_value())) {
              (*mutable_map_int32_enum())[entry->key()] =
                  static_cast< ::protobuf_unittest::MapEnumLite >(*entry->mutable_value());
            } else {
              unknown_fields_stream.WriteVarint32(130);
              unknown_fields_stream.WriteVarint32(data.size());
              unknown_fields_stream.WriteString(data);
            }
          }
          if (entry->GetArena() != NULL) entry.release();
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(130)) goto parse_loop_map_int32_enum;
        if (input->ExpectTag(138)) goto parse_loop_map_int32_foreign_message;
        input->UnsafeDecrementRecursionDepth();
        break;
      }

      // map<int32, .protobuf_unittest.ForeignMessageArenaLite> map_int32_foreign_message = 17;
      case 17: {
        if (tag == 138) {
          DO_(input->IncrementRecursionDepth());
         parse_loop_map_int32_foreign_message:
          TestArenaMapLite_MapInt32ForeignMessageEntry::Parser< ::google::protobuf::internal::MapFieldLite<
              ::google::protobuf::int32, ::protobuf_unittest::ForeignMessageArenaLite,
              ::google::protobuf::internal::WireFormatLite::TYPE_INT32,
              ::google::protobuf::internal::WireFormatLite::TYPE_MESSAGE,
              0 >,
            ::google::protobuf::Map< ::google::protobuf::int32, ::protobuf_unittest::ForeignMessageArenaLite > > parser(&map_int32_foreign_message_);
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessageNoVirtual(
              input, &parser));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(138)) goto parse_loop_map_int32_foreign_message;
        if (input->ExpectTag(146)) goto parse_loop_map_int32_foreign_message_no_arena;
        input->UnsafeDecrementRecursionDepth();
        break;
      }

      // map<int32, .protobuf_unittest_no_arena.ForeignMessageLite> map_int32_foreign_message_no_arena = 18;
      case 18: {
        if (tag == 146) {
          DO_(input->IncrementRecursionDepth());
         parse_loop_map_int32_foreign_message_no_arena:
          TestArenaMapLite_MapInt32ForeignMessageNoArenaEntry::Parser< ::google::protobuf::internal::MapFieldLite<
              ::google::protobuf::int32, ::protobuf_unittest_no_arena::ForeignMessageLite,
              ::google::protobuf::internal::WireFormatLite::TYPE_INT32,
              ::google::protobuf::internal::WireFormatLite::TYPE_MESSAGE,
              0 >,
            ::google::protobuf::Map< ::google::protobuf::int32, ::protobuf_unittest_no_arena::ForeignMessageLite > > parser(&map_int32_foreign_message_no_arena_);
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessageNoVirtual(
              input, &parser));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(146)) goto parse_loop_map_int32_foreign_message_no_arena;
        input->UnsafeDecrementRecursionDepth();
        if (input->ExpectAtEnd()) goto success;
        break;
      }

      default: {
      handle_unusual:
        if (tag == 0 ||
            ::google::protobuf::internal::WireFormatLite::GetTagWireType(tag) ==
            ::google::protobuf::internal::WireFormatLite::WIRETYPE_END_GROUP) {
          goto success;
        }
        DO_(::google::protobuf::internal::WireFormatLite::SkipField(
            input, tag, &unknown_fields_stream));
        break;
      }
    }
  }
success:
  // @@protoc_insertion_point(parse_success:protobuf_unittest.TestArenaMapLite)
  return true;
failure:
  // @@protoc_insertion_point(parse_failure:protobuf_unittest.TestArenaMapLite)
  return false;
#undef DO_
}

void TestArenaMapLite::SerializeWithCachedSizes(
    ::google::protobuf::io::CodedOutputStream* output) const {
  // @@protoc_insertion_point(serialize_start:protobuf_unittest.TestArenaMapLite)
  // map<int32, int32> map_int32_int32 = 1;
  if (!this->map_int32_int32().empty()) {
    typedef ::google::protobuf::Map< ::google::protobuf::int32, ::google::protobuf::int32 >::const_pointer
        ConstPtr;
    typedef ::google::protobuf::internal::SortItem< ::google::protobuf::int32, ConstPtr > SortItem;
    typedef ::google::protobuf::internal::CompareByFirstField<SortItem> Less;

    if (output->IsSerializationDeterminstic() &&
        this->map_int32_int32().size() > 1) {
      ::google::protobuf::scoped_array<SortItem> items(
          new SortItem[this->map_int32_int32().size()]);
      typedef ::google::protobuf::Map< ::google::protobuf::int32, ::google::protobuf::int32 >::size_type size_type;
      size_type n = 0;
      for (::google::protobuf::Map< ::google::protobuf::int32, ::google::protobuf::int32 >::const_iterator
          it = this->map_int32_int32().begin();
          it != this->map_int32_int32().end(); ++it, ++n) {
        items[n] = SortItem(&*it);
      }
      ::std::sort(&items[0], &items[n], Less());
      ::google::protobuf::scoped_ptr<TestArenaMapLite_MapInt32Int32Entry> entry;
      for (size_type i = 0; i < n; i++) {
        entry.reset(map_int32_int32_.NewEntryWrapper(
            items[i].second->first, items[i].second->second));
        ::google::protobuf::internal::WireFormatLite::WriteMessage(
            1, *entry, output);
        if (entry->GetArena() != NULL) {
          entry.release();
        }
      }
    } else {
      ::google::protobuf::scoped_ptr<TestArenaMapLite_MapInt32Int32Entry> entry;
      for (::google::protobuf::Map< ::google::protobuf::int32, ::google::protobuf::int32 >::const_iterator
          it = this->map_int32_int32().begin();
          it != this->map_int32_int32().end(); ++it) {
        entry.reset(map_int32_int32_.NewEntryWrapper(
            it->first, it->second));
        ::google::protobuf::internal::WireFormatLite::WriteMessage(
            1, *entry, output);
        if (entry->GetArena() != NULL) {
          entry.release();
        }
      }
    }
  }

  // map<int64, int64> map_int64_int64 = 2;
  if (!this->map_int64_int64().empty()) {
    typedef ::google::protobuf::Map< ::google::protobuf::int64, ::google::protobuf::int64 >::const_pointer
        ConstPtr;
    typedef ::google::protobuf::internal::SortItem< ::google::protobuf::int64, ConstPtr > SortItem;
    typedef ::google::protobuf::internal::CompareByFirstField<SortItem> Less;

    if (output->IsSerializationDeterminstic() &&
        this->map_int64_int64().size() > 1) {
      ::google::protobuf::scoped_array<SortItem> items(
          new SortItem[this->map_int64_int64().size()]);
      typedef ::google::protobuf::Map< ::google::protobuf::int64, ::google::protobuf::int64 >::size_type size_type;
      size_type n = 0;
      for (::google::protobuf::Map< ::google::protobuf::int64, ::google::protobuf::int64 >::const_iterator
          it = this->map_int64_int64().begin();
          it != this->map_int64_int64().end(); ++it, ++n) {
        items[n] = SortItem(&*it);
      }
      ::std::sort(&items[0], &items[n], Less());
      ::google::protobuf::scoped_ptr<TestArenaMapLite_MapInt64Int64Entry> entry;
      for (size_type i = 0; i < n; i++) {
        entry.reset(map_int64_int64_.NewEntryWrapper(
            items[i].second->first, items[i].second->second));
        ::google::protobuf::internal::WireFormatLite::WriteMessage(
            2, *entry, output);
        if (entry->GetArena() != NULL) {
          entry.release();
        }
      }
    } else {
      ::google::protobuf::scoped_ptr<TestArenaMapLite_MapInt64Int64Entry> entry;
      for (::google::protobuf::Map< ::google::protobuf::int64, ::google::protobuf::int64 >::const_iterator
          it = this->map_int64_int64().begin();
          it != this->map_int64_int64().end(); ++it) {
        entry.reset(map_int64_int64_.NewEntryWrapper(
            it->first, it->second));
        ::google::protobuf::internal::WireFormatLite::WriteMessage(
            2, *entry, output);
        if (entry->GetArena() != NULL) {
          entry.release();
        }
      }
    }
  }

  // map<uint32, uint32> map_uint32_uint32 = 3;
  if (!this->map_uint32_uint32().empty()) {
    typedef ::google::protobuf::Map< ::google::protobuf::uint32, ::google::protobuf::uint32 >::const_pointer
        ConstPtr;
    typedef ::google::protobuf::internal::SortItem< ::google::protobuf::uint32, ConstPtr > SortItem;
    typedef ::google::protobuf::internal::CompareByFirstField<SortItem> Less;

    if (output->IsSerializationDeterminstic() &&
        this->map_uint32_uint32().size() > 1) {
      ::google::protobuf::scoped_array<SortItem> items(
          new SortItem[this->map_uint32_uint32().size()]);
      typedef ::google::protobuf::Map< ::google::protobuf::uint32, ::google::protobuf::uint32 >::size_type size_type;
      size_type n = 0;
      for (::google::protobuf::Map< ::google::protobuf::uint32, ::google::protobuf::uint32 >::const_iterator
          it = this->map_uint32_uint32().begin();
          it != this->map_uint32_uint32().end(); ++it, ++n) {
        items[n] = SortItem(&*it);
      }
      ::std::sort(&items[0], &items[n], Less());
      ::google::protobuf::scoped_ptr<TestArenaMapLite_MapUint32Uint32Entry> entry;
      for (size_type i = 0; i < n; i++) {
        entry.reset(map_uint32_uint32_.NewEntryWrapper(
            items[i].second->first, items[i].second->second));
        ::google::protobuf::internal::WireFormatLite::WriteMessage(
            3, *entry, output);
        if (entry->GetArena() != NULL) {
          entry.release();
        }
      }
    } else {
      ::google::protobuf::scoped_ptr<TestArenaMapLite_MapUint32Uint32Entry> entry;
      for (::google::protobuf::Map< ::google::protobuf::uint32, ::google::protobuf::uint32 >::const_iterator
          it = this->map_uint32_uint32().begin();
          it != this->map_uint32_uint32().end(); ++it) {
        entry.reset(map_uint32_uint32_.NewEntryWrapper(
            it->first, it->second));
        ::google::protobuf::internal::WireFormatLite::WriteMessage(
            3, *entry, output);
        if (entry->GetArena() != NULL) {
          entry.release();
        }
      }
    }
  }

  // map<uint64, uint64> map_uint64_uint64 = 4;
  if (!this->map_uint64_uint64().empty()) {
    typedef ::google::protobuf::Map< ::google::protobuf::uint64, ::google::protobuf::uint64 >::const_pointer
        ConstPtr;
    typedef ::google::protobuf::internal::SortItem< ::google::protobuf::uint64, ConstPtr > SortItem;
    typedef ::google::protobuf::internal::CompareByFirstField<SortItem> Less;

    if (output->IsSerializationDeterminstic() &&
        this->map_uint64_uint64().size() > 1) {
      ::google::protobuf::scoped_array<SortItem> items(
          new SortItem[this->map_uint64_uint64().size()]);
      typedef ::google::protobuf::Map< ::google::protobuf::uint64, ::google::protobuf::uint64 >::size_type size_type;
      size_type n = 0;
      for (::google::protobuf::Map< ::google::protobuf::uint64, ::google::protobuf::uint64 >::const_iterator
          it = this->map_uint64_uint64().begin();
          it != this->map_uint64_uint64().end(); ++it, ++n) {
        items[n] = SortItem(&*it);
      }
      ::std::sort(&items[0], &items[n], Less());
      ::google::protobuf::scoped_ptr<TestArenaMapLite_MapUint64Uint64Entry> entry;
      for (size_type i = 0; i < n; i++) {
        entry.reset(map_uint64_uint64_.NewEntryWrapper(
            items[i].second->first, items[i].second->second));
        ::google::protobuf::internal::WireFormatLite::WriteMessage(
            4, *entry, output);
        if (entry->GetArena() != NULL) {
          entry.release();
        }
      }
    } else {
      ::google::protobuf::scoped_ptr<TestArenaMapLite_MapUint64Uint64Entry> entry;
      for (::google::protobuf::Map< ::google::protobuf::uint64, ::google::protobuf::uint64 >::const_iterator
          it = this->map_uint64_uint64().begin();
          it != this->map_uint64_uint64().end(); ++it) {
        entry.reset(map_uint64_uint64_.NewEntryWrapper(
            it->first, it->second));
        ::google::protobuf::internal::WireFormatLite::WriteMessage(
            4, *entry, output);
        if (entry->GetArena() != NULL) {
          entry.release();
        }
      }
    }
  }

  // map<sint32, sint32> map_sint32_sint32 = 5;
  if (!this->map_sint32_sint32().empty()) {
    typedef ::google::protobuf::Map< ::google::protobuf::int32, ::google::protobuf::int32 >::const_pointer
        ConstPtr;
    typedef ::google::protobuf::internal::SortItem< ::google::protobuf::int32, ConstPtr > SortItem;
    typedef ::google::protobuf::internal::CompareByFirstField<SortItem> Less;

    if (output->IsSerializationDeterminstic() &&
        this->map_sint32_sint32().size() > 1) {
      ::google::protobuf::scoped_array<SortItem> items(
          new SortItem[this->map_sint32_sint32().size()]);
      typedef ::google::protobuf::Map< ::google::protobuf::int32, ::google::protobuf::int32 >::size_type size_type;
      size_type n = 0;
      for (::google::protobuf::Map< ::google::protobuf::int32, ::google::protobuf::int32 >::const_iterator
          it = this->map_sint32_sint32().begin();
          it != this->map_sint32_sint32().end(); ++it, ++n) {
        items[n] = SortItem(&*it);
      }
      ::std::sort(&items[0], &items[n], Less());
      ::google::protobuf::scoped_ptr<TestArenaMapLite_MapSint32Sint32Entry> entry;
      for (size_type i = 0; i < n; i++) {
        entry.reset(map_sint32_sint32_.NewEntryWrapper(
            items[i].second->first, items[i].second->second));
        ::google::protobuf::internal::WireFormatLite::WriteMessage(
            5, *entry, output);
        if (entry->GetArena() != NULL) {
          entry.release();
        }
      }
    } else {
      ::google::protobuf::scoped_ptr<TestArenaMapLite_MapSint32Sint32Entry> entry;
      for (::google::protobuf::Map< ::google::protobuf::int32, ::google::protobuf::int32 >::const_iterator
          it = this->map_sint32_sint32().begin();
          it != this->map_sint32_sint32().end(); ++it) {
        entry.reset(map_sint32_sint32_.NewEntryWrapper(
            it->first, it->second));
        ::google::protobuf::internal::WireFormatLite::WriteMessage(
            5, *entry, output);
        if (entry->GetArena() != NULL) {
          entry.release();
        }
      }
    }
  }

  // map<sint64, sint64> map_sint64_sint64 = 6;
  if (!this->map_sint64_sint64().empty()) {
    typedef ::google::protobuf::Map< ::google::protobuf::int64, ::google::protobuf::int64 >::const_pointer
        ConstPtr;
    typedef ::google::protobuf::internal::SortItem< ::google::protobuf::int64, ConstPtr > SortItem;
    typedef ::google::protobuf::internal::CompareByFirstField<SortItem> Less;

    if (output->IsSerializationDeterminstic() &&
        this->map_sint64_sint64().size() > 1) {
      ::google::protobuf::scoped_array<SortItem> items(
          new SortItem[this->map_sint64_sint64().size()]);
      typedef ::google::protobuf::Map< ::google::protobuf::int64, ::google::protobuf::int64 >::size_type size_type;
      size_type n = 0;
      for (::google::protobuf::Map< ::google::protobuf::int64, ::google::protobuf::int64 >::const_iterator
          it = this->map_sint64_sint64().begin();
          it != this->map_sint64_sint64().end(); ++it, ++n) {
        items[n] = SortItem(&*it);
      }
      ::std::sort(&items[0], &items[n], Less());
      ::google::protobuf::scoped_ptr<TestArenaMapLite_MapSint64Sint64Entry> entry;
      for (size_type i = 0; i < n; i++) {
        entry.reset(map_sint64_sint64_.NewEntryWrapper(
            items[i].second->first, items[i].second->second));
        ::google::protobuf::internal::WireFormatLite::WriteMessage(
            6, *entry, output);
        if (entry->GetArena() != NULL) {
          entry.release();
        }
      }
    } else {
      ::google::protobuf::scoped_ptr<TestArenaMapLite_MapSint64Sint64Entry> entry;
      for (::google::protobuf::Map< ::google::protobuf::int64, ::google::protobuf::int64 >::const_iterator
          it = this->map_sint64_sint64().begin();
          it != this->map_sint64_sint64().end(); ++it) {
        entry.reset(map_sint64_sint64_.NewEntryWrapper(
            it->first, it->second));
        ::google::protobuf::internal::WireFormatLite::WriteMessage(
            6, *entry, output);
        if (entry->GetArena() != NULL) {
          entry.release();
        }
      }
    }
  }

  // map<fixed32, fixed32> map_fixed32_fixed32 = 7;
  if (!this->map_fixed32_fixed32().empty()) {
    typedef ::google::protobuf::Map< ::google::protobuf::uint32, ::google::protobuf::uint32 >::const_pointer
        ConstPtr;
    typedef ::google::protobuf::internal::SortItem< ::google::protobuf::uint32, ConstPtr > SortItem;
    typedef ::google::protobuf::internal::CompareByFirstField<SortItem> Less;

    if (output->IsSerializationDeterminstic() &&
        this->map_fixed32_fixed32().size() > 1) {
      ::google::protobuf::scoped_array<SortItem> items(
          new SortItem[this->map_fixed32_fixed32().size()]);
      typedef ::google::protobuf::Map< ::google::protobuf::uint32, ::google::protobuf::uint32 >::size_type size_type;
      size_type n = 0;
      for (::google::protobuf::Map< ::google::protobuf::uint32, ::google::protobuf::uint32 >::const_iterator
          it = this->map_fixed32_fixed32().begin();
          it != this->map_fixed32_fixed32().end(); ++it, ++n) {
        items[n] = SortItem(&*it);
      }
      ::std::sort(&items[0], &items[n], Less());
      ::google::protobuf::scoped_ptr<TestArenaMapLite_MapFixed32Fixed32Entry> entry;
      for (size_type i = 0; i < n; i++) {
        entry.reset(map_fixed32_fixed32_.NewEntryWrapper(
            items[i].second->first, items[i].second->second));
        ::google::protobuf::internal::WireFormatLite::WriteMessage(
            7, *entry, output);
        if (entry->GetArena() != NULL) {
          entry.release();
        }
      }
    } else {
      ::google::protobuf::scoped_ptr<TestArenaMapLite_MapFixed32Fixed32Entry> entry;
      for (::google::protobuf::Map< ::google::protobuf::uint32, ::google::protobuf::uint32 >::const_iterator
          it = this->map_fixed32_fixed32().begin();
          it != this->map_fixed32_fixed32().end(); ++it) {
        entry.reset(map_fixed32_fixed32_.NewEntryWrapper(
            it->first, it->second));
        ::google::protobuf::internal::WireFormatLite::WriteMessage(
            7, *entry, output);
        if (entry->GetArena() != NULL) {
          entry.release();
        }
      }
    }
  }

  // map<fixed64, fixed64> map_fixed64_fixed64 = 8;
  if (!this->map_fixed64_fixed64().empty()) {
    typedef ::google::protobuf::Map< ::google::protobuf::uint64, ::google::protobuf::uint64 >::const_pointer
        ConstPtr;
    typedef ::google::protobuf::internal::SortItem< ::google::protobuf::uint64, ConstPtr > SortItem;
    typedef ::google::protobuf::internal::CompareByFirstField<SortItem> Less;

    if (output->IsSerializationDeterminstic() &&
        this->map_fixed64_fixed64().size() > 1) {
      ::google::protobuf::scoped_array<SortItem> items(
          new SortItem[this->map_fixed64_fixed64().size()]);
      typedef ::google::protobuf::Map< ::google::protobuf::uint64, ::google::protobuf::uint64 >::size_type size_type;
      size_type n = 0;
      for (::google::protobuf::Map< ::google::protobuf::uint64, ::google::protobuf::uint64 >::const_iterator
          it = this->map_fixed64_fixed64().begin();
          it != this->map_fixed64_fixed64().end(); ++it, ++n) {
        items[n] = SortItem(&*it);
      }
      ::std::sort(&items[0], &items[n], Less());
      ::google::protobuf::scoped_ptr<TestArenaMapLite_MapFixed64Fixed64Entry> entry;
      for (size_type i = 0; i < n; i++) {
        entry.reset(map_fixed64_fixed64_.NewEntryWrapper(
            items[i].second->first, items[i].second->second));
        ::google::protobuf::internal::WireFormatLite::WriteMessage(
            8, *entry, output);
        if (entry->GetArena() != NULL) {
          entry.release();
        }
      }
    } else {
      ::google::protobuf::scoped_ptr<TestArenaMapLite_MapFixed64Fixed64Entry> entry;
      for (::google::protobuf::Map< ::google::protobuf::uint64, ::google::protobuf::uint64 >::const_iterator
          it = this->map_fixed64_fixed64().begin();
          it != this->map_fixed64_fixed64().end(); ++it) {
        entry.reset(map_fixed64_fixed64_.NewEntryWrapper(
            it->first, it->second));
        ::google::protobuf::internal::WireFormatLite::WriteMessage(
            8, *entry, output);
        if (entry->GetArena() != NULL) {
          entry.release();
        }
      }
    }
  }

  // map<sfixed32, sfixed32> map_sfixed32_sfixed32 = 9;
  if (!this->map_sfixed32_sfixed32().empty()) {
    typedef ::google::protobuf::Map< ::google::protobuf::int32, ::google::protobuf::int32 >::const_pointer
        ConstPtr;
    typedef ::google::protobuf::internal::SortItem< ::google::protobuf::int32, ConstPtr > SortItem;
    typedef ::google::protobuf::internal::CompareByFirstField<SortItem> Less;

    if (output->IsSerializationDeterminstic() &&
        this->map_sfixed32_sfixed32().size() > 1) {
      ::google::protobuf::scoped_array<SortItem> items(
          new SortItem[this->map_sfixed32_sfixed32().size()]);
      typedef ::google::protobuf::Map< ::google::protobuf::int32, ::google::protobuf::int32 >::size_type size_type;
      size_type n = 0;
      for (::google::protobuf::Map< ::google::protobuf::int32, ::google::protobuf::int32 >::const_iterator
          it = this->map_sfixed32_sfixed32().begin();
          it != this->map_sfixed32_sfixed32().end(); ++it, ++n) {
        items[n] = SortItem(&*it);
      }
      ::std::sort(&items[0], &items[n], Less());
      ::google::protobuf::scoped_ptr<TestArenaMapLite_MapSfixed32Sfixed32Entry> entry;
      for (size_type i = 0; i < n; i++) {
        entry.reset(map_sfixed32_sfixed32_.NewEntryWrapper(
            items[i].second->first, items[i].second->second));
        ::google::protobuf::internal::WireFormatLite::WriteMessage(
            9, *entry, output);
        if (entry->GetArena() != NULL) {
          entry.release();
        }
      }
    } else {
      ::google::protobuf::scoped_ptr<TestArenaMapLite_MapSfixed32Sfixed32Entry> entry;
      for (::google::protobuf::Map< ::google::protobuf::int32, ::google::protobuf::int32 >::const_iterator
          it = this->map_sfixed32_sfixed32().begin();
          it != this->map_sfixed32_sfixed32().end(); ++it) {
        entry.reset(map_sfixed32_sfixed32_.NewEntryWrapper(
            it->first, it->second));
        ::google::protobuf::internal::WireFormatLite::WriteMessage(
            9, *entry, output);
        if (entry->GetArena() != NULL) {
          entry.release();
        }
      }
    }
  }

  // map<sfixed64, sfixed64> map_sfixed64_sfixed64 = 10;
  if (!this->map_sfixed64_sfixed64().empty()) {
    typedef ::google::protobuf::Map< ::google::protobuf::int64, ::google::protobuf::int64 >::const_pointer
        ConstPtr;
    typedef ::google::protobuf::internal::SortItem< ::google::protobuf::int64, ConstPtr > SortItem;
    typedef ::google::protobuf::internal::CompareByFirstField<SortItem> Less;

    if (output->IsSerializationDeterminstic() &&
        this->map_sfixed64_sfixed64().size() > 1) {
      ::google::protobuf::scoped_array<SortItem> items(
          new SortItem[this->map_sfixed64_sfixed64().size()]);
      typedef ::google::protobuf::Map< ::google::protobuf::int64, ::google::protobuf::int64 >::size_type size_type;
      size_type n = 0;
      for (::google::protobuf::Map< ::google::protobuf::int64, ::google::protobuf::int64 >::const_iterator
          it = this->map_sfixed64_sfixed64().begin();
          it != this->map_sfixed64_sfixed64().end(); ++it, ++n) {
        items[n] = SortItem(&*it);
      }
      ::std::sort(&items[0], &items[n], Less());
      ::google::protobuf::scoped_ptr<TestArenaMapLite_MapSfixed64Sfixed64Entry> entry;
      for (size_type i = 0; i < n; i++) {
        entry.reset(map_sfixed64_sfixed64_.NewEntryWrapper(
            items[i].second->first, items[i].second->second));
        ::google::protobuf::internal::WireFormatLite::WriteMessage(
            10, *entry, output);
        if (entry->GetArena() != NULL) {
          entry.release();
        }
      }
    } else {
      ::google::protobuf::scoped_ptr<TestArenaMapLite_MapSfixed64Sfixed64Entry> entry;
      for (::google::protobuf::Map< ::google::protobuf::int64, ::google::protobuf::int64 >::const_iterator
          it = this->map_sfixed64_sfixed64().begin();
          it != this->map_sfixed64_sfixed64().end(); ++it) {
        entry.reset(map_sfixed64_sfixed64_.NewEntryWrapper(
            it->first, it->second));
        ::google::protobuf::internal::WireFormatLite::WriteMessage(
            10, *entry, output);
        if (entry->GetArena() != NULL) {
          entry.release();
        }
      }
    }
  }

  // map<int32, float> map_int32_float = 11;
  if (!this->map_int32_float().empty()) {
    typedef ::google::protobuf::Map< ::google::protobuf::int32, float >::const_pointer
        ConstPtr;
    typedef ::google::protobuf::internal::SortItem< ::google::protobuf::int32, ConstPtr > SortItem;
    typedef ::google::protobuf::internal::CompareByFirstField<SortItem> Less;

    if (output->IsSerializationDeterminstic() &&
        this->map_int32_float().size() > 1) {
      ::google::protobuf::scoped_array<SortItem> items(
          new SortItem[this->map_int32_float().size()]);
      typedef ::google::protobuf::Map< ::google::protobuf::int32, float >::size_type size_type;
      size_type n = 0;
      for (::google::protobuf::Map< ::google::protobuf::int32, float >::const_iterator
          it = this->map_int32_float().begin();
          it != this->map_int32_float().end(); ++it, ++n) {
        items[n] = SortItem(&*it);
      }
      ::std::sort(&items[0], &items[n], Less());
      ::google::protobuf::scoped_ptr<TestArenaMapLite_MapInt32FloatEntry> entry;
      for (size_type i = 0; i < n; i++) {
        entry.reset(map_int32_float_.NewEntryWrapper(
            items[i].second->first, items[i].second->second));
        ::google::protobuf::internal::WireFormatLite::WriteMessage(
            11, *entry, output);
        if (entry->GetArena() != NULL) {
          entry.release();
        }
      }
    } else {
      ::google::protobuf::scoped_ptr<TestArenaMapLite_MapInt32FloatEntry> entry;
      for (::google::protobuf::Map< ::google::protobuf::int32, float >::const_iterator
          it = this->map_int32_float().begin();
          it != this->map_int32_float().end(); ++it) {
        entry.reset(map_int32_float_.NewEntryWrapper(
            it->first, it->second));
        ::google::protobuf::internal::WireFormatLite::WriteMessage(
            11, *entry, output);
        if (entry->GetArena() != NULL) {
          entry.release();
        }
      }
    }
  }

  // map<int32, double> map_int32_double = 12;
  if (!this->map_int32_double().empty()) {
    typedef ::google::protobuf::Map< ::google::protobuf::int32, double >::const_pointer
        ConstPtr;
    typedef ::google::protobuf::internal::SortItem< ::google::protobuf::int32, ConstPtr > SortItem;
    typedef ::google::protobuf::internal::CompareByFirstField<SortItem> Less;

    if (output->IsSerializationDeterminstic() &&
        this->map_int32_double().size() > 1) {
      ::google::protobuf::scoped_array<SortItem> items(
          new SortItem[this->map_int32_double().size()]);
      typedef ::google::protobuf::Map< ::google::protobuf::int32, double >::size_type size_type;
      size_type n = 0;
      for (::google::protobuf::Map< ::google::protobuf::int32, double >::const_iterator
          it = this->map_int32_double().begin();
          it != this->map_int32_double().end(); ++it, ++n) {
        items[n] = SortItem(&*it);
      }
      ::std::sort(&items[0], &items[n], Less());
      ::google::protobuf::scoped_ptr<TestArenaMapLite_MapInt32DoubleEntry> entry;
      for (size_type i = 0; i < n; i++) {
        entry.reset(map_int32_double_.NewEntryWrapper(
            items[i].second->first, items[i].second->second));
        ::google::protobuf::internal::WireFormatLite::WriteMessage(
            12, *entry, output);
        if (entry->GetArena() != NULL) {
          entry.release();
        }
      }
    } else {
      ::google::protobuf::scoped_ptr<TestArenaMapLite_MapInt32DoubleEntry> entry;
      for (::google::protobuf::Map< ::google::protobuf::int32, double >::const_iterator
          it = this->map_int32_double().begin();
          it != this->map_int32_double().end(); ++it) {
        entry.reset(map_int32_double_.NewEntryWrapper(
            it->first, it->second));
        ::google::protobuf::internal::WireFormatLite::WriteMessage(
            12, *entry, output);
        if (entry->GetArena() != NULL) {
          entry.release();
        }
      }
    }
  }

  // map<bool, bool> map_bool_bool = 13;
  if (!this->map_bool_bool().empty()) {
    typedef ::google::protobuf::Map< bool, bool >::const_pointer
        ConstPtr;
    typedef ::google::protobuf::internal::SortItem< bool, ConstPtr > SortItem;
    typedef ::google::protobuf::internal::CompareByFirstField<SortItem> Less;

    if (output->IsSerializationDeterminstic() &&
        this->map_bool_bool().size() > 1) {
      ::google::protobuf::scoped_array<SortItem> items(
          new SortItem[this->map_bool_bool().size()]);
      typedef ::google::protobuf::Map< bool, bool >::size_type size_type;
      size_type n = 0;
      for (::google::protobuf::Map< bool, bool >::const_iterator
          it = this->map_bool_bool().begin();
          it != this->map_bool_bool().end(); ++it, ++n) {
        items[n] = SortItem(&*it);
      }
      ::std::sort(&items[0], &items[n], Less());
      ::google::protobuf::scoped_ptr<TestArenaMapLite_MapBoolBoolEntry> entry;
      for (size_type i = 0; i < n; i++) {
        entry.reset(map_bool_bool_.NewEntryWrapper(
            items[i].second->first, items[i].second->second));
        ::google::protobuf::internal::WireFormatLite::WriteMessage(
            13, *entry, output);
        if (entry->GetArena() != NULL) {
          entry.release();
        }
      }
    } else {
      ::google::protobuf::scoped_ptr<TestArenaMapLite_MapBoolBoolEntry> entry;
      for (::google::protobuf::Map< bool, bool >::const_iterator
          it = this->map_bool_bool().begin();
          it != this->map_bool_bool().end(); ++it) {
        entry.reset(map_bool_bool_.NewEntryWrapper(
            it->first, it->second));
        ::google::protobuf::internal::WireFormatLite::WriteMessage(
            13, *entry, output);
        if (entry->GetArena() != NULL) {
          entry.release();
        }
      }
    }
  }

  // map<string, string> map_string_string = 14;
  if (!this->map_string_string().empty()) {
    typedef ::google::protobuf::Map< ::std::string, ::std::string >::const_pointer
        ConstPtr;
    typedef ConstPtr SortItem;
    typedef ::google::protobuf::internal::CompareByDerefFirst<SortItem> Less;
    struct Utf8Check {
      static void Check(ConstPtr p) {
      }
    };

    if (output->IsSerializationDeterminstic() &&
        this->map_string_string().size() > 1) {
      ::google::protobuf::scoped_array<SortItem> items(
          new SortItem[this->map_string_string().size()]);
      typedef ::google::protobuf::Map< ::std::string, ::std::string >::size_type size_type;
      size_type n = 0;
      for (::google::protobuf::Map< ::std::string, ::std::string >::const_iterator
          it = this->map_string_string().begin();
          it != this->map_string_string().end(); ++it, ++n) {
        items[n] = SortItem(&*it);
      }
      ::std::sort(&items[0], &items[n], Less());
      ::google::protobuf::scoped_ptr<TestArenaMapLite_MapStringStringEntry> entry;
      for (size_type i = 0; i < n; i++) {
        entry.reset(map_string_string_.NewEntryWrapper(
            items[i]->first, items[i]->second));
        ::google::protobuf::internal::WireFormatLite::WriteMessage(
            14, *entry, output);
        if (entry->GetArena() != NULL) {
          entry.release();
        }
        Utf8Check::Check(items[i]);
      }
    } else {
      ::google::protobuf::scoped_ptr<TestArenaMapLite_MapStringStringEntry> entry;
      for (::google::protobuf::Map< ::std::string, ::std::string >::const_iterator
          it = this->map_string_string().begin();
          it != this->map_string_string().end(); ++it) {
        entry.reset(map_string_string_.NewEntryWrapper(
            it->first, it->second));
        ::google::protobuf::internal::WireFormatLite::WriteMessage(
            14, *entry, output);
        if (entry->GetArena() != NULL) {
          entry.release();
        }
        Utf8Check::Check(&*it);
      }
    }
  }

  // map<int32, bytes> map_int32_bytes = 15;
  if (!this->map_int32_bytes().empty()) {
    typedef ::google::protobuf::Map< ::google::protobuf::int32, ::std::string >::const_pointer
        ConstPtr;
    typedef ::google::protobuf::internal::SortItem< ::google::protobuf::int32, ConstPtr > SortItem;
    typedef ::google::protobuf::internal::CompareByFirstField<SortItem> Less;

    if (output->IsSerializationDeterminstic() &&
        this->map_int32_bytes().size() > 1) {
      ::google::protobuf::scoped_array<SortItem> items(
          new SortItem[this->map_int32_bytes().size()]);
      typedef ::google::protobuf::Map< ::google::protobuf::int32, ::std::string >::size_type size_type;
      size_type n = 0;
      for (::google::protobuf::Map< ::google::protobuf::int32, ::std::string >::const_iterator
          it = this->map_int32_bytes().begin();
          it != this->map_int32_bytes().end(); ++it, ++n) {
        items[n] = SortItem(&*it);
      }
      ::std::sort(&items[0], &items[n], Less());
      ::google::protobuf::scoped_ptr<TestArenaMapLite_MapInt32BytesEntry> entry;
      for (size_type i = 0; i < n; i++) {
        entry.reset(map_int32_bytes_.NewEntryWrapper(
            items[i].second->first, items[i].second->second));
        ::google::protobuf::internal::WireFormatLite::WriteMessage(
            15, *entry, output);
        if (entry->GetArena() != NULL) {
          entry.release();
        }
      }
    } else {
      ::google::protobuf::scoped_ptr<TestArenaMapLite_MapInt32BytesEntry> entry;
      for (::google::protobuf::Map< ::google::protobuf::int32, ::std::string >::const_iterator
          it = this->map_int32_bytes().begin();
          it != this->map_int32_bytes().end(); ++it) {
        entry.reset(map_int32_bytes_.NewEntryWrapper(
            it->first, it->second));
        ::google::protobuf::internal::WireFormatLite::WriteMessage(
            15, *entry, output);
        if (entry->GetArena() != NULL) {
          entry.release();
        }
      }
    }
  }

  // map<int32, .protobuf_unittest.MapEnumLite> map_int32_enum = 16;
  if (!this->map_int32_enum().empty()) {
    typedef ::google::protobuf::Map< ::google::protobuf::int32, ::protobuf_unittest::MapEnumLite >::const_pointer
        ConstPtr;
    typedef ::google::protobuf::internal::SortItem< ::google::protobuf::int32, ConstPtr > SortItem;
    typedef ::google::protobuf::internal::CompareByFirstField<SortItem> Less;

    if (output->IsSerializationDeterminstic() &&
        this->map_int32_enum().size() > 1) {
      ::google::protobuf::scoped_array<SortItem> items(
          new SortItem[this->map_int32_enum().size()]);
      typedef ::google::protobuf::Map< ::google::protobuf::int32, ::protobuf_unittest::MapEnumLite >::size_type size_type;
      size_type n = 0;
      for (::google::protobuf::Map< ::google::protobuf::int32, ::protobuf_unittest::MapEnumLite >::const_iterator
          it = this->map_int32_enum().begin();
          it != this->map_int32_enum().end(); ++it, ++n) {
        items[n] = SortItem(&*it);
      }
      ::std::sort(&items[0], &items[n], Less());
      ::google::protobuf::scoped_ptr<TestArenaMapLite_MapInt32EnumEntry> entry;
      for (size_type i = 0; i < n; i++) {
        entry.reset(map_int32_enum_.NewEnumEntryWrapper(
            items[i].second->first, items[i].second->second));
        ::google::protobuf::internal::WireFormatLite::WriteMessage(
            16, *entry, output);
        if (entry->GetArena() != NULL) {
          entry.release();
        }
      }
    } else {
      ::google::protobuf::scoped_ptr<TestArenaMapLite_MapInt32EnumEntry> entry;
      for (::google::protobuf::Map< ::google::protobuf::int32, ::protobuf_unittest::MapEnumLite >::const_iterator
          it = this->map_int32_enum().begin();
          it != this->map_int32_enum().end(); ++it) {
        entry.reset(map_int32_enum_.NewEnumEntryWrapper(
            it->first, it->second));
        ::google::protobuf::internal::WireFormatLite::WriteMessage(
            16, *entry, output);
        if (entry->GetArena() != NULL) {
          entry.release();
        }
      }
    }
  }

  // map<int32, .protobuf_unittest.ForeignMessageArenaLite> map_int32_foreign_message = 17;
  if (!this->map_int32_foreign_message().empty()) {
    typedef ::google::protobuf::Map< ::google::protobuf::int32, ::protobuf_unittest::ForeignMessageArenaLite >::const_pointer
        ConstPtr;
    typedef ::google::protobuf::internal::SortItem< ::google::protobuf::int32, ConstPtr > SortItem;
    typedef ::google::protobuf::internal::CompareByFirstField<SortItem> Less;

    if (output->IsSerializationDeterminstic() &&
        this->map_int32_foreign_message().size() > 1) {
      ::google::protobuf::scoped_array<SortItem> items(
          new SortItem[this->map_int32_foreign_message().size()]);
      typedef ::google::protobuf::Map< ::google::protobuf::int32, ::protobuf_unittest::ForeignMessageArenaLite >::size_type size_type;
      size_type n = 0;
      for (::google::protobuf::Map< ::google::protobuf::int32, ::protobuf_unittest::ForeignMessageArenaLite >::const_iterator
          it = this->map_int32_foreign_message().begin();
          it != this->map_int32_foreign_message().end(); ++it, ++n) {
        items[n] = SortItem(&*it);
      }
      ::std::sort(&items[0], &items[n], Less());
      ::google::protobuf::scoped_ptr<TestArenaMapLite_MapInt32ForeignMessageEntry> entry;
      for (size_type i = 0; i < n; i++) {
        entry.reset(map_int32_foreign_message_.NewEntryWrapper(
            items[i].second->first, items[i].second->second));
        ::google::protobuf::internal::WireFormatLite::WriteMessage(
            17, *entry, output);
        if (entry->GetArena() != NULL) {
          entry.release();
        }
      }
    } else {
      ::google::protobuf::scoped_ptr<TestArenaMapLite_MapInt32ForeignMessageEntry> entry;
      for (::google::protobuf::Map< ::google::protobuf::int32, ::protobuf_unittest::ForeignMessageArenaLite >::const_iterator
          it = this->map_int32_foreign_message().begin();
          it != this->map_int32_foreign_message().end(); ++it) {
        entry.reset(map_int32_foreign_message_.NewEntryWrapper(
            it->first, it->second));
        ::google::protobuf::internal::WireFormatLite::WriteMessage(
            17, *entry, output);
        if (entry->GetArena() != NULL) {
          entry.release();
        }
      }
    }
  }

  // map<int32, .protobuf_unittest_no_arena.ForeignMessageLite> map_int32_foreign_message_no_arena = 18;
  if (!this->map_int32_foreign_message_no_arena().empty()) {
    typedef ::google::protobuf::Map< ::google::protobuf::int32, ::protobuf_unittest_no_arena::ForeignMessageLite >::const_pointer
        ConstPtr;
    typedef ::google::protobuf::internal::SortItem< ::google::protobuf::int32, ConstPtr > SortItem;
    typedef ::google::protobuf::internal::CompareByFirstField<SortItem> Less;

    if (output->IsSerializationDeterminstic() &&
        this->map_int32_foreign_message_no_arena().size() > 1) {
      ::google::protobuf::scoped_array<SortItem> items(
          new SortItem[this->map_int32_foreign_message_no_arena().size()]);
      typedef ::google::protobuf::Map< ::google::protobuf::int32, ::protobuf_unittest_no_arena::ForeignMessageLite >::size_type size_type;
      size_type n = 0;
      for (::google::protobuf::Map< ::google::protobuf::int32, ::protobuf_unittest_no_arena::ForeignMessageLite >::const_iterator
          it = this->map_int32_foreign_message_no_arena().begin();
          it != this->map_int32_foreign_message_no_arena().end(); ++it, ++n) {
        items[n] = SortItem(&*it);
      }
      ::std::sort(&items[0], &items[n], Less());
      ::google::protobuf::scoped_ptr<TestArenaMapLite_MapInt32ForeignMessageNoArenaEntry> entry;
      for (size_type i = 0; i < n; i++) {
        entry.reset(map_int32_foreign_message_no_arena_.NewEntryWrapper(
            items[i].second->first, items[i].second->second));
        ::google::protobuf::internal::WireFormatLite::WriteMessage(
            18, *entry, output);
        if (entry->GetArena() != NULL) {
          entry.release();
        }
      }
    } else {
      ::google::protobuf::scoped_ptr<TestArenaMapLite_MapInt32ForeignMessageNoArenaEntry> entry;
      for (::google::protobuf::Map< ::google::protobuf::int32, ::protobuf_unittest_no_arena::ForeignMessageLite >::const_iterator
          it = this->map_int32_foreign_message_no_arena().begin();
          it != this->map_int32_foreign_message_no_arena().end(); ++it) {
        entry.reset(map_int32_foreign_message_no_arena_.NewEntryWrapper(
            it->first, it->second));
        ::google::protobuf::internal::WireFormatLite::WriteMessage(
            18, *entry, output);
        if (entry->GetArena() != NULL) {
          entry.release();
        }
      }
    }
  }

  output->WriteRaw(unknown_fields().data(),
                   static_cast<int>(unknown_fields().size()));
  // @@protoc_insertion_point(serialize_end:protobuf_unittest.TestArenaMapLite)
}

size_t TestArenaMapLite::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:protobuf_unittest.TestArenaMapLite)
  size_t total_size = 0;

  // map<int32, int32> map_int32_int32 = 1;
  total_size += 1 *
      ::google::protobuf::internal::FromIntSize(this->map_int32_int32_size());
  {
    ::google::protobuf::scoped_ptr<TestArenaMapLite_MapInt32Int32Entry> entry;
    for (::google::protobuf::Map< ::google::protobuf::int32, ::google::protobuf::int32 >::const_iterator
        it = this->map_int32_int32().begin();
        it != this->map_int32_int32().end(); ++it) {
      if (entry.get() != NULL && entry->GetArena() != NULL) {
        entry.release();
      }
      entry.reset(map_int32_int32_.NewEntryWrapper(it->first, it->second));
      total_size += ::google::protobuf::internal::WireFormatLite::
          MessageSizeNoVirtual(*entry);
    }
    if (entry.get() != NULL && entry->GetArena() != NULL) {
      entry.release();
    }
  }

  // map<int64, int64> map_int64_int64 = 2;
  total_size += 1 *
      ::google::protobuf::internal::FromIntSize(this->map_int64_int64_size());
  {
    ::google::protobuf::scoped_ptr<TestArenaMapLite_MapInt64Int64Entry> entry;
    for (::google::protobuf::Map< ::google::protobuf::int64, ::google::protobuf::int64 >::const_iterator
        it = this->map_int64_int64().begin();
        it != this->map_int64_int64().end(); ++it) {
      if (entry.get() != NULL && entry->GetArena() != NULL) {
        entry.release();
      }
      entry.reset(map_int64_int64_.NewEntryWrapper(it->first, it->second));
      total_size += ::google::protobuf::internal::WireFormatLite::
          MessageSizeNoVirtual(*entry);
    }
    if (entry.get() != NULL && entry->GetArena() != NULL) {
      entry.release();
    }
  }

  // map<uint32, uint32> map_uint32_uint32 = 3;
  total_size += 1 *
      ::google::protobuf::internal::FromIntSize(this->map_uint32_uint32_size());
  {
    ::google::protobuf::scoped_ptr<TestArenaMapLite_MapUint32Uint32Entry> entry;
    for (::google::protobuf::Map< ::google::protobuf::uint32, ::google::protobuf::uint32 >::const_iterator
        it = this->map_uint32_uint32().begin();
        it != this->map_uint32_uint32().end(); ++it) {
      if (entry.get() != NULL && entry->GetArena() != NULL) {
        entry.release();
      }
      entry.reset(map_uint32_uint32_.NewEntryWrapper(it->first, it->second));
      total_size += ::google::protobuf::internal::WireFormatLite::
          MessageSizeNoVirtual(*entry);
    }
    if (entry.get() != NULL && entry->GetArena() != NULL) {
      entry.release();
    }
  }

  // map<uint64, uint64> map_uint64_uint64 = 4;
  total_size += 1 *
      ::google::protobuf::internal::FromIntSize(this->map_uint64_uint64_size());
  {
    ::google::protobuf::scoped_ptr<TestArenaMapLite_MapUint64Uint64Entry> entry;
    for (::google::protobuf::Map< ::google::protobuf::uint64, ::google::protobuf::uint64 >::const_iterator
        it = this->map_uint64_uint64().begin();
        it != this->map_uint64_uint64().end(); ++it) {
      if (entry.get() != NULL && entry->GetArena() != NULL) {
        entry.release();
      }
      entry.reset(map_uint64_uint64_.NewEntryWrapper(it->first, it->second));
      total_size += ::google::protobuf::internal::WireFormatLite::
          MessageSizeNoVirtual(*entry);
    }
    if (entry.get() != NULL && entry->GetArena() != NULL) {
      entry.release();
    }
  }

  // map<sint32, sint32> map_sint32_sint32 = 5;
  total_size += 1 *
      ::google::protobuf::internal::FromIntSize(this->map_sint32_sint32_size());
  {
    ::google::protobuf::scoped_ptr<TestArenaMapLite_MapSint32Sint32Entry> entry;
    for (::google::protobuf::Map< ::google::protobuf::int32, ::google::protobuf::int32 >::const_iterator
        it = this->map_sint32_sint32().begin();
        it != this->map_sint32_sint32().end(); ++it) {
      if (entry.get() != NULL && entry->GetArena() != NULL) {
        entry.release();
      }
      entry.reset(map_sint32_sint32_.NewEntryWrapper(it->first, it->second));
      total_size += ::google::protobuf::internal::WireFormatLite::
          MessageSizeNoVirtual(*entry);
    }
    if (entry.get() != NULL && entry->GetArena() != NULL) {
      entry.release();
    }
  }

  // map<sint64, sint64> map_sint64_sint64 = 6;
  total_size += 1 *
      ::google::protobuf::internal::FromIntSize(this->map_sint64_sint64_size());
  {
    ::google::protobuf::scoped_ptr<TestArenaMapLite_MapSint64Sint64Entry> entry;
    for (::google::protobuf::Map< ::google::protobuf::int64, ::google::protobuf::int64 >::const_iterator
        it = this->map_sint64_sint64().begin();
        it != this->map_sint64_sint64().end(); ++it) {
      if (entry.get() != NULL && entry->GetArena() != NULL) {
        entry.release();
      }
      entry.reset(map_sint64_sint64_.NewEntryWrapper(it->first, it->second));
      total_size += ::google::protobuf::internal::WireFormatLite::
          MessageSizeNoVirtual(*entry);
    }
    if (entry.get() != NULL && entry->GetArena() != NULL) {
      entry.release();
    }
  }

  // map<fixed32, fixed32> map_fixed32_fixed32 = 7;
  total_size += 1 *
      ::google::protobuf::internal::FromIntSize(this->map_fixed32_fixed32_size());
  {
    ::google::protobuf::scoped_ptr<TestArenaMapLite_MapFixed32Fixed32Entry> entry;
    for (::google::protobuf::Map< ::google::protobuf::uint32, ::google::protobuf::uint32 >::const_iterator
        it = this->map_fixed32_fixed32().begin();
        it != this->map_fixed32_fixed32().end(); ++it) {
      if (entry.get() != NULL && entry->GetArena() != NULL) {
        entry.release();
      }
      entry.reset(map_fixed32_fixed32_.NewEntryWrapper(it->first, it->second));
      total_size += ::google::protobuf::internal::WireFormatLite::
          MessageSizeNoVirtual(*entry);
    }
    if (entry.get() != NULL && entry->GetArena() != NULL) {
      entry.release();
    }
  }

  // map<fixed64, fixed64> map_fixed64_fixed64 = 8;
  total_size += 1 *
      ::google::protobuf::internal::FromIntSize(this->map_fixed64_fixed64_size());
  {
    ::google::protobuf::scoped_ptr<TestArenaMapLite_MapFixed64Fixed64Entry> entry;
    for (::google::protobuf::Map< ::google::protobuf::uint64, ::google::protobuf::uint64 >::const_iterator
        it = this->map_fixed64_fixed64().begin();
        it != this->map_fixed64_fixed64().end(); ++it) {
      if (entry.get() != NULL && entry->GetArena() != NULL) {
        entry.release();
      }
      entry.reset(map_fixed64_fixed64_.NewEntryWrapper(it->first, it->second));
      total_size += ::google::protobuf::internal::WireFormatLite::
          MessageSizeNoVirtual(*entry);
    }
    if (entry.get() != NULL && entry->GetArena() != NULL) {
      entry.release();
    }
  }

  // map<sfixed32, sfixed32> map_sfixed32_sfixed32 = 9;
  total_size += 1 *
      ::google::protobuf::internal::FromIntSize(this->map_sfixed32_sfixed32_size());
  {
    ::google::protobuf::scoped_ptr<TestArenaMapLite_MapSfixed32Sfixed32Entry> entry;
    for (::google::protobuf::Map< ::google::protobuf::int32, ::google::protobuf::int32 >::const_iterator
        it = this->map_sfixed32_sfixed32().begin();
        it != this->map_sfixed32_sfixed32().end(); ++it) {
      if (entry.get() != NULL && entry->GetArena() != NULL) {
        entry.release();
      }
      entry.reset(map_sfixed32_sfixed32_.NewEntryWrapper(it->first, it->second));
      total_size += ::google::protobuf::internal::WireFormatLite::
          MessageSizeNoVirtual(*entry);
    }
    if (entry.get() != NULL && entry->GetArena() != NULL) {
      entry.release();
    }
  }

  // map<sfixed64, sfixed64> map_sfixed64_sfixed64 = 10;
  total_size += 1 *
      ::google::protobuf::internal::FromIntSize(this->map_sfixed64_sfixed64_size());
  {
    ::google::protobuf::scoped_ptr<TestArenaMapLite_MapSfixed64Sfixed64Entry> entry;
    for (::google::protobuf::Map< ::google::protobuf::int64, ::google::protobuf::int64 >::const_iterator
        it = this->map_sfixed64_sfixed64().begin();
        it != this->map_sfixed64_sfixed64().end(); ++it) {
      if (entry.get() != NULL && entry->GetArena() != NULL) {
        entry.release();
      }
      entry.reset(map_sfixed64_sfixed64_.NewEntryWrapper(it->first, it->second));
      total_size += ::google::protobuf::internal::WireFormatLite::
          MessageSizeNoVirtual(*entry);
    }
    if (entry.get() != NULL && entry->GetArena() != NULL) {
      entry.release();
    }
  }

  // map<int32, float> map_int32_float = 11;
  total_size += 1 *
      ::google::protobuf::internal::FromIntSize(this->map_int32_float_size());
  {
    ::google::protobuf::scoped_ptr<TestArenaMapLite_MapInt32FloatEntry> entry;
    for (::google::protobuf::Map< ::google::protobuf::int32, float >::const_iterator
        it = this->map_int32_float().begin();
        it != this->map_int32_float().end(); ++it) {
      if (entry.get() != NULL && entry->GetArena() != NULL) {
        entry.release();
      }
      entry.reset(map_int32_float_.NewEntryWrapper(it->first, it->second));
      total_size += ::google::protobuf::internal::WireFormatLite::
          MessageSizeNoVirtual(*entry);
    }
    if (entry.get() != NULL && entry->GetArena() != NULL) {
      entry.release();
    }
  }

  // map<int32, double> map_int32_double = 12;
  total_size += 1 *
      ::google::protobuf::internal::FromIntSize(this->map_int32_double_size());
  {
    ::google::protobuf::scoped_ptr<TestArenaMapLite_MapInt32DoubleEntry> entry;
    for (::google::protobuf::Map< ::google::protobuf::int32, double >::const_iterator
        it = this->map_int32_double().begin();
        it != this->map_int32_double().end(); ++it) {
      if (entry.get() != NULL && entry->GetArena() != NULL) {
        entry.release();
      }
      entry.reset(map_int32_double_.NewEntryWrapper(it->first, it->second));
      total_size += ::google::protobuf::internal::WireFormatLite::
          MessageSizeNoVirtual(*entry);
    }
    if (entry.get() != NULL && entry->GetArena() != NULL) {
      entry.release();
    }
  }

  // map<bool, bool> map_bool_bool = 13;
  total_size += 1 *
      ::google::protobuf::internal::FromIntSize(this->map_bool_bool_size());
  {
    ::google::protobuf::scoped_ptr<TestArenaMapLite_MapBoolBoolEntry> entry;
    for (::google::protobuf::Map< bool, bool >::const_iterator
        it = this->map_bool_bool().begin();
        it != this->map_bool_bool().end(); ++it) {
      if (entry.get() != NULL && entry->GetArena() != NULL) {
        entry.release();
      }
      entry.reset(map_bool_bool_.NewEntryWrapper(it->first, it->second));
      total_size += ::google::protobuf::internal::WireFormatLite::
          MessageSizeNoVirtual(*entry);
    }
    if (entry.get() != NULL && entry->GetArena() != NULL) {
      entry.release();
    }
  }

  // map<string, string> map_string_string = 14;
  total_size += 1 *
      ::google::protobuf::internal::FromIntSize(this->map_string_string_size());
  {
    ::google::protobuf::scoped_ptr<TestArenaMapLite_MapStringStringEntry> entry;
    for (::google::protobuf::Map< ::std::string, ::std::string >::const_iterator
        it = this->map_string_string().begin();
        it != this->map_string_string().end(); ++it) {
      if (entry.get() != NULL && entry->GetArena() != NULL) {
        entry.release();
      }
      entry.reset(map_string_string_.NewEntryWrapper(it->first, it->second));
      total_size += ::google::protobuf::internal::WireFormatLite::
          MessageSizeNoVirtual(*entry);
    }
    if (entry.get() != NULL && entry->GetArena() != NULL) {
      entry.release();
    }
  }

  // map<int32, bytes> map_int32_bytes = 15;
  total_size += 1 *
      ::google::protobuf::internal::FromIntSize(this->map_int32_bytes_size());
  {
    ::google::protobuf::scoped_ptr<TestArenaMapLite_MapInt32BytesEntry> entry;
    for (::google::protobuf::Map< ::google::protobuf::int32, ::std::string >::const_iterator
        it = this->map_int32_bytes().begin();
        it != this->map_int32_bytes().end(); ++it) {
      if (entry.get() != NULL && entry->GetArena() != NULL) {
        entry.release();
      }
      entry.reset(map_int32_bytes_.NewEntryWrapper(it->first, it->second));
      total_size += ::google::protobuf::internal::WireFormatLite::
          MessageSizeNoVirtual(*entry);
    }
    if (entry.get() != NULL && entry->GetArena() != NULL) {
      entry.release();
    }
  }

  // map<int32, .protobuf_unittest.MapEnumLite> map_int32_enum = 16;
  total_size += 2 *
      ::google::protobuf::internal::FromIntSize(this->map_int32_enum_size());
  {
    ::google::protobuf::scoped_ptr<TestArenaMapLite_MapInt32EnumEntry> entry;
    for (::google::protobuf::Map< ::google::protobuf::int32, ::protobuf_unittest::MapEnumLite >::const_iterator
        it = this->map_int32_enum().begin();
        it != this->map_int32_enum().end(); ++it) {
      if (entry.get() != NULL && entry->GetArena() != NULL) {
        entry.release();
      }
      entry.reset(map_int32_enum_.NewEnumEntryWrapper(it->first, it->second));
      total_size += ::google::protobuf::internal::WireFormatLite::
          MessageSizeNoVirtual(*entry);
    }
    if (entry.get() != NULL && entry->GetArena() != NULL) {
      entry.release();
    }
  }

  // map<int32, .protobuf_unittest.ForeignMessageArenaLite> map_int32_foreign_message = 17;
  total_size += 2 *
      ::google::protobuf::internal::FromIntSize(this->map_int32_foreign_message_size());
  {
    ::google::protobuf::scoped_ptr<TestArenaMapLite_MapInt32ForeignMessageEntry> entry;
    for (::google::protobuf::Map< ::google::protobuf::int32, ::protobuf_unittest::ForeignMessageArenaLite >::const_iterator
        it = this->map_int32_foreign_message().begin();
        it != this->map_int32_foreign_message().end(); ++it) {
      if (entry.get() != NULL && entry->GetArena() != NULL) {
        entry.release();
      }
      entry.reset(map_int32_foreign_message_.NewEntryWrapper(it->first, it->second));
      total_size += ::google::protobuf::internal::WireFormatLite::
          MessageSizeNoVirtual(*entry);
    }
    if (entry.get() != NULL && entry->GetArena() != NULL) {
      entry.release();
    }
  }

  // map<int32, .protobuf_unittest_no_arena.ForeignMessageLite> map_int32_foreign_message_no_arena = 18;
  total_size += 2 *
      ::google::protobuf::internal::FromIntSize(this->map_int32_foreign_message_no_arena_size());
  {
    ::google::protobuf::scoped_ptr<TestArenaMapLite_MapInt32ForeignMessageNoArenaEntry> entry;
    for (::google::protobuf::Map< ::google::protobuf::int32, ::protobuf_unittest_no_arena::ForeignMessageLite >::const_iterator
        it = this->map_int32_foreign_message_no_arena().begin();
        it != this->map_int32_foreign_message_no_arena().end(); ++it) {
      if (entry.get() != NULL && entry->GetArena() != NULL) {
        entry.release();
      }
      entry.reset(map_int32_foreign_message_no_arena_.NewEntryWrapper(it->first, it->second));
      total_size += ::google::protobuf::internal::WireFormatLite::
          MessageSizeNoVirtual(*entry);
    }
    if (entry.get() != NULL && entry->GetArena() != NULL) {
      entry.release();
    }
  }

  total_size += unknown_fields().size();

  int cached_size = ::google::protobuf::internal::ToCachedSize(total_size);
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = cached_size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
  return total_size;
}

void TestArenaMapLite::CheckTypeAndMergeFrom(
    const ::google::protobuf::MessageLite& from) {
  MergeFrom(*::google::protobuf::down_cast<const TestArenaMapLite*>(&from));
}

void TestArenaMapLite::MergeFrom(const TestArenaMapLite& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:protobuf_unittest.TestArenaMapLite)
  if (GOOGLE_PREDICT_TRUE(&from != this)) {
    UnsafeMergeFrom(from);
  } else {
    MergeFromFail(__LINE__);
  }
}

void TestArenaMapLite::UnsafeMergeFrom(const TestArenaMapLite& from) {
  GOOGLE_DCHECK(&from != this);
  map_int32_int32_.MergeFrom(from.map_int32_int32_);
  map_int64_int64_.MergeFrom(from.map_int64_int64_);
  map_uint32_uint32_.MergeFrom(from.map_uint32_uint32_);
  map_uint64_uint64_.MergeFrom(from.map_uint64_uint64_);
  map_sint32_sint32_.MergeFrom(from.map_sint32_sint32_);
  map_sint64_sint64_.MergeFrom(from.map_sint64_sint64_);
  map_fixed32_fixed32_.MergeFrom(from.map_fixed32_fixed32_);
  map_fixed64_fixed64_.MergeFrom(from.map_fixed64_fixed64_);
  map_sfixed32_sfixed32_.MergeFrom(from.map_sfixed32_sfixed32_);
  map_sfixed64_sfixed64_.MergeFrom(from.map_sfixed64_sfixed64_);
  map_int32_float_.MergeFrom(from.map_int32_float_);
  map_int32_double_.MergeFrom(from.map_int32_double_);
  map_bool_bool_.MergeFrom(from.map_bool_bool_);
  map_string_string_.MergeFrom(from.map_string_string_);
  map_int32_bytes_.MergeFrom(from.map_int32_bytes_);
  map_int32_enum_.MergeFrom(from.map_int32_enum_);
  map_int32_foreign_message_.MergeFrom(from.map_int32_foreign_message_);
  map_int32_foreign_message_no_arena_.MergeFrom(from.map_int32_foreign_message_no_arena_);
  if (!from.unknown_fields().empty()) {
    mutable_unknown_fields()->append(from.unknown_fields());
  }
}

void TestArenaMapLite::CopyFrom(const TestArenaMapLite& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:protobuf_unittest.TestArenaMapLite)
  if (&from == this) return;
  Clear();
  UnsafeMergeFrom(from);
}

bool TestArenaMapLite::IsInitialized() const {

  return true;
}

void TestArenaMapLite::Swap(TestArenaMapLite* other) {
  if (other == this) return;
  if (GetArenaNoVirtual() == other->GetArenaNoVirtual()) {
    InternalSwap(other);
  } else {
    TestArenaMapLite temp;
    temp.UnsafeMergeFrom(*this);
    CopyFrom(*other);
    other->CopyFrom(temp);
  }
}
void TestArenaMapLite::UnsafeArenaSwap(TestArenaMapLite* other) {
  if (other == this) return;
  GOOGLE_DCHECK(GetArenaNoVirtual() == other->GetArenaNoVirtual());
  InternalSwap(other);
}
void TestArenaMapLite::InternalSwap(TestArenaMapLite* other) {
  map_int32_int32_.Swap(&other->map_int32_int32_);
  map_int64_int64_.Swap(&other->map_int64_int64_);
  map_uint32_uint32_.Swap(&other->map_uint32_uint32_);
  map_uint64_uint64_.Swap(&other->map_uint64_uint64_);
  map_sint32_sint32_.Swap(&other->map_sint32_sint32_);
  map_sint64_sint64_.Swap(&other->map_sint64_sint64_);
  map_fixed32_fixed32_.Swap(&other->map_fixed32_fixed32_);
  map_fixed64_fixed64_.Swap(&other->map_fixed64_fixed64_);
  map_sfixed32_sfixed32_.Swap(&other->map_sfixed32_sfixed32_);
  map_sfixed64_sfixed64_.Swap(&other->map_sfixed64_sfixed64_);
  map_int32_float_.Swap(&other->map_int32_float_);
  map_int32_double_.Swap(&other->map_int32_double_);
  map_bool_bool_.Swap(&other->map_bool_bool_);
  map_string_string_.Swap(&other->map_string_string_);
  map_int32_bytes_.Swap(&other->map_int32_bytes_);
  map_int32_enum_.Swap(&other->map_int32_enum_);
  map_int32_foreign_message_.Swap(&other->map_int32_foreign_message_);
  map_int32_foreign_message_no_arena_.Swap(&other->map_int32_foreign_message_no_arena_);
  std::swap(_has_bits_[0], other->_has_bits_[0]);
  _unknown_fields_.Swap(&other->_unknown_fields_);
  std::swap(_cached_size_, other->_cached_size_);
}

::std::string TestArenaMapLite::GetTypeName() const {
  return "protobuf_unittest.TestArenaMapLite";
}

#if PROTOBUF_INLINE_NOT_IN_HEADERS
// TestArenaMapLite

// map<int32, int32> map_int32_int32 = 1;
int TestArenaMapLite::map_int32_int32_size() const {
  return map_int32_int32_.size();
}
void TestArenaMapLite::clear_map_int32_int32() {
  map_int32_int32_.Clear();
}
 const ::google::protobuf::Map< ::google::protobuf::int32, ::google::protobuf::int32 >&
TestArenaMapLite::map_int32_int32() const {
  // @@protoc_insertion_point(field_map:protobuf_unittest.TestArenaMapLite.map_int32_int32)
  return map_int32_int32_.GetMap();
}
 ::google::protobuf::Map< ::google::protobuf::int32, ::google::protobuf::int32 >*
TestArenaMapLite::mutable_map_int32_int32() {
  // @@protoc_insertion_point(field_mutable_map:protobuf_unittest.TestArenaMapLite.map_int32_int32)
  return map_int32_int32_.MutableMap();
}

// map<int64, int64> map_int64_int64 = 2;
int TestArenaMapLite::map_int64_int64_size() const {
  return map_int64_int64_.size();
}
void TestArenaMapLite::clear_map_int64_int64() {
  map_int64_int64_.Clear();
}
 const ::google::protobuf::Map< ::google::protobuf::int64, ::google::protobuf::int64 >&
TestArenaMapLite::map_int64_int64() const {
  // @@protoc_insertion_point(field_map:protobuf_unittest.TestArenaMapLite.map_int64_int64)
  return map_int64_int64_.GetMap();
}
 ::google::protobuf::Map< ::google::protobuf::int64, ::google::protobuf::int64 >*
TestArenaMapLite::mutable_map_int64_int64() {
  // @@protoc_insertion_point(field_mutable_map:protobuf_unittest.TestArenaMapLite.map_int64_int64)
  return map_int64_int64_.MutableMap();
}

// map<uint32, uint32> map_uint32_uint32 = 3;
int TestArenaMapLite::map_uint32_uint32_size() const {
  return map_uint32_uint32_.size();
}
void TestArenaMapLite::clear_map_uint32_uint32() {
  map_uint32_uint32_.Clear();
}
 const ::google::protobuf::Map< ::google::protobuf::uint32, ::google::protobuf::uint32 >&
TestArenaMapLite::map_uint32_uint32() const {
  // @@protoc_insertion_point(field_map:protobuf_unittest.TestArenaMapLite.map_uint32_uint32)
  return map_uint32_uint32_.GetMap();
}
 ::google::protobuf::Map< ::google::protobuf::uint32, ::google::protobuf::uint32 >*
TestArenaMapLite::mutable_map_uint32_uint32() {
  // @@protoc_insertion_point(field_mutable_map:protobuf_unittest.TestArenaMapLite.map_uint32_uint32)
  return map_uint32_uint32_.MutableMap();
}

// map<uint64, uint64> map_uint64_uint64 = 4;
int TestArenaMapLite::map_uint64_uint64_size() const {
  return map_uint64_uint64_.size();
}
void TestArenaMapLite::clear_map_uint64_uint64() {
  map_uint64_uint64_.Clear();
}
 const ::google::protobuf::Map< ::google::protobuf::uint64, ::google::protobuf::uint64 >&
TestArenaMapLite::map_uint64_uint64() const {
  // @@protoc_insertion_point(field_map:protobuf_unittest.TestArenaMapLite.map_uint64_uint64)
  return map_uint64_uint64_.GetMap();
}
 ::google::protobuf::Map< ::google::protobuf::uint64, ::google::protobuf::uint64 >*
TestArenaMapLite::mutable_map_uint64_uint64() {
  // @@protoc_insertion_point(field_mutable_map:protobuf_unittest.TestArenaMapLite.map_uint64_uint64)
  return map_uint64_uint64_.MutableMap();
}

// map<sint32, sint32> map_sint32_sint32 = 5;
int TestArenaMapLite::map_sint32_sint32_size() const {
  return map_sint32_sint32_.size();
}
void TestArenaMapLite::clear_map_sint32_sint32() {
  map_sint32_sint32_.Clear();
}
 const ::google::protobuf::Map< ::google::protobuf::int32, ::google::protobuf::int32 >&
TestArenaMapLite::map_sint32_sint32() const {
  // @@protoc_insertion_point(field_map:protobuf_unittest.TestArenaMapLite.map_sint32_sint32)
  return map_sint32_sint32_.GetMap();
}
 ::google::protobuf::Map< ::google::protobuf::int32, ::google::protobuf::int32 >*
TestArenaMapLite::mutable_map_sint32_sint32() {
  // @@protoc_insertion_point(field_mutable_map:protobuf_unittest.TestArenaMapLite.map_sint32_sint32)
  return map_sint32_sint32_.MutableMap();
}

// map<sint64, sint64> map_sint64_sint64 = 6;
int TestArenaMapLite::map_sint64_sint64_size() const {
  return map_sint64_sint64_.size();
}
void TestArenaMapLite::clear_map_sint64_sint64() {
  map_sint64_sint64_.Clear();
}
 const ::google::protobuf::Map< ::google::protobuf::int64, ::google::protobuf::int64 >&
TestArenaMapLite::map_sint64_sint64() const {
  // @@protoc_insertion_point(field_map:protobuf_unittest.TestArenaMapLite.map_sint64_sint64)
  return map_sint64_sint64_.GetMap();
}
 ::google::protobuf::Map< ::google::protobuf::int64, ::google::protobuf::int64 >*
TestArenaMapLite::mutable_map_sint64_sint64() {
  // @@protoc_insertion_point(field_mutable_map:protobuf_unittest.TestArenaMapLite.map_sint64_sint64)
  return map_sint64_sint64_.MutableMap();
}

// map<fixed32, fixed32> map_fixed32_fixed32 = 7;
int TestArenaMapLite::map_fixed32_fixed32_size() const {
  return map_fixed32_fixed32_.size();
}
void TestArenaMapLite::clear_map_fixed32_fixed32() {
  map_fixed32_fixed32_.Clear();
}
 const ::google::protobuf::Map< ::google::protobuf::uint32, ::google::protobuf::uint32 >&
TestArenaMapLite::map_fixed32_fixed32() const {
  // @@protoc_insertion_point(field_map:protobuf_unittest.TestArenaMapLite.map_fixed32_fixed32)
  return map_fixed32_fixed32_.GetMap();
}
 ::google::protobuf::Map< ::google::protobuf::uint32, ::google::protobuf::uint32 >*
TestArenaMapLite::mutable_map_fixed32_fixed32() {
  // @@protoc_insertion_point(field_mutable_map:protobuf_unittest.TestArenaMapLite.map_fixed32_fixed32)
  return map_fixed32_fixed32_.MutableMap();
}

// map<fixed64, fixed64> map_fixed64_fixed64 = 8;
int TestArenaMapLite::map_fixed64_fixed64_size() const {
  return map_fixed64_fixed64_.size();
}
void TestArenaMapLite::clear_map_fixed64_fixed64() {
  map_fixed64_fixed64_.Clear();
}
 const ::google::protobuf::Map< ::google::protobuf::uint64, ::google::protobuf::uint64 >&
TestArenaMapLite::map_fixed64_fixed64() const {
  // @@protoc_insertion_point(field_map:protobuf_unittest.TestArenaMapLite.map_fixed64_fixed64)
  return map_fixed64_fixed64_.GetMap();
}
 ::google::protobuf::Map< ::google::protobuf::uint64, ::google::protobuf::uint64 >*
TestArenaMapLite::mutable_map_fixed64_fixed64() {
  // @@protoc_insertion_point(field_mutable_map:protobuf_unittest.TestArenaMapLite.map_fixed64_fixed64)
  return map_fixed64_fixed64_.MutableMap();
}

// map<sfixed32, sfixed32> map_sfixed32_sfixed32 = 9;
int TestArenaMapLite::map_sfixed32_sfixed32_size() const {
  return map_sfixed32_sfixed32_.size();
}
void TestArenaMapLite::clear_map_sfixed32_sfixed32() {
  map_sfixed32_sfixed32_.Clear();
}
 const ::google::protobuf::Map< ::google::protobuf::int32, ::google::protobuf::int32 >&
TestArenaMapLite::map_sfixed32_sfixed32() const {
  // @@protoc_insertion_point(field_map:protobuf_unittest.TestArenaMapLite.map_sfixed32_sfixed32)
  return map_sfixed32_sfixed32_.GetMap();
}
 ::google::protobuf::Map< ::google::protobuf::int32, ::google::protobuf::int32 >*
TestArenaMapLite::mutable_map_sfixed32_sfixed32() {
  // @@protoc_insertion_point(field_mutable_map:protobuf_unittest.TestArenaMapLite.map_sfixed32_sfixed32)
  return map_sfixed32_sfixed32_.MutableMap();
}

// map<sfixed64, sfixed64> map_sfixed64_sfixed64 = 10;
int TestArenaMapLite::map_sfixed64_sfixed64_size() const {
  return map_sfixed64_sfixed64_.size();
}
void TestArenaMapLite::clear_map_sfixed64_sfixed64() {
  map_sfixed64_sfixed64_.Clear();
}
 const ::google::protobuf::Map< ::google::protobuf::int64, ::google::protobuf::int64 >&
TestArenaMapLite::map_sfixed64_sfixed64() const {
  // @@protoc_insertion_point(field_map:protobuf_unittest.TestArenaMapLite.map_sfixed64_sfixed64)
  return map_sfixed64_sfixed64_.GetMap();
}
 ::google::protobuf::Map< ::google::protobuf::int64, ::google::protobuf::int64 >*
TestArenaMapLite::mutable_map_sfixed64_sfixed64() {
  // @@protoc_insertion_point(field_mutable_map:protobuf_unittest.TestArenaMapLite.map_sfixed64_sfixed64)
  return map_sfixed64_sfixed64_.MutableMap();
}

// map<int32, float> map_int32_float = 11;
int TestArenaMapLite::map_int32_float_size() const {
  return map_int32_float_.size();
}
void TestArenaMapLite::clear_map_int32_float() {
  map_int32_float_.Clear();
}
 const ::google::protobuf::Map< ::google::protobuf::int32, float >&
TestArenaMapLite::map_int32_float() const {
  // @@protoc_insertion_point(field_map:protobuf_unittest.TestArenaMapLite.map_int32_float)
  return map_int32_float_.GetMap();
}
 ::google::protobuf::Map< ::google::protobuf::int32, float >*
TestArenaMapLite::mutable_map_int32_float() {
  // @@protoc_insertion_point(field_mutable_map:protobuf_unittest.TestArenaMapLite.map_int32_float)
  return map_int32_float_.MutableMap();
}

// map<int32, double> map_int32_double = 12;
int TestArenaMapLite::map_int32_double_size() const {
  return map_int32_double_.size();
}
void TestArenaMapLite::clear_map_int32_double() {
  map_int32_double_.Clear();
}
 const ::google::protobuf::Map< ::google::protobuf::int32, double >&
TestArenaMapLite::map_int32_double() const {
  // @@protoc_insertion_point(field_map:protobuf_unittest.TestArenaMapLite.map_int32_double)
  return map_int32_double_.GetMap();
}
 ::google::protobuf::Map< ::google::protobuf::int32, double >*
TestArenaMapLite::mutable_map_int32_double() {
  // @@protoc_insertion_point(field_mutable_map:protobuf_unittest.TestArenaMapLite.map_int32_double)
  return map_int32_double_.MutableMap();
}

// map<bool, bool> map_bool_bool = 13;
int TestArenaMapLite::map_bool_bool_size() const {
  return map_bool_bool_.size();
}
void TestArenaMapLite::clear_map_bool_bool() {
  map_bool_bool_.Clear();
}
 const ::google::protobuf::Map< bool, bool >&
TestArenaMapLite::map_bool_bool() const {
  // @@protoc_insertion_point(field_map:protobuf_unittest.TestArenaMapLite.map_bool_bool)
  return map_bool_bool_.GetMap();
}
 ::google::protobuf::Map< bool, bool >*
TestArenaMapLite::mutable_map_bool_bool() {
  // @@protoc_insertion_point(field_mutable_map:protobuf_unittest.TestArenaMapLite.map_bool_bool)
  return map_bool_bool_.MutableMap();
}

// map<string, string> map_string_string = 14;
int TestArenaMapLite::map_string_string_size() const {
  return map_string_string_.size();
}
void TestArenaMapLite::clear_map_string_string() {
  map_string_string_.Clear();
}
 const ::google::protobuf::Map< ::std::string, ::std::string >&
TestArenaMapLite::map_string_string() const {
  // @@protoc_insertion_point(field_map:protobuf_unittest.TestArenaMapLite.map_string_string)
  return map_string_string_.GetMap();
}
 ::google::protobuf::Map< ::std::string, ::std::string >*
TestArenaMapLite::mutable_map_string_string() {
  // @@protoc_insertion_point(field_mutable_map:protobuf_unittest.TestArenaMapLite.map_string_string)
  return map_string_string_.MutableMap();
}

// map<int32, bytes> map_int32_bytes = 15;
int TestArenaMapLite::map_int32_bytes_size() const {
  return map_int32_bytes_.size();
}
void TestArenaMapLite::clear_map_int32_bytes() {
  map_int32_bytes_.Clear();
}
 const ::google::protobuf::Map< ::google::protobuf::int32, ::std::string >&
TestArenaMapLite::map_int32_bytes() const {
  // @@protoc_insertion_point(field_map:protobuf_unittest.TestArenaMapLite.map_int32_bytes)
  return map_int32_bytes_.GetMap();
}
 ::google::protobuf::Map< ::google::protobuf::int32, ::std::string >*
TestArenaMapLite::mutable_map_int32_bytes() {
  // @@protoc_insertion_point(field_mutable_map:protobuf_unittest.TestArenaMapLite.map_int32_bytes)
  return map_int32_bytes_.MutableMap();
}

// map<int32, .protobuf_unittest.MapEnumLite> map_int32_enum = 16;
int TestArenaMapLite::map_int32_enum_size() const {
  return map_int32_enum_.size();
}
void TestArenaMapLite::clear_map_int32_enum() {
  map_int32_enum_.Clear();
}
 const ::google::protobuf::Map< ::google::protobuf::int32, ::protobuf_unittest::MapEnumLite >&
TestArenaMapLite::map_int32_enum() const {
  // @@protoc_insertion_point(field_map:protobuf_unittest.TestArenaMapLite.map_int32_enum)
  return map_int32_enum_.GetMap();
}
 ::google::protobuf::Map< ::google::protobuf::int32, ::protobuf_unittest::MapEnumLite >*
TestArenaMapLite::mutable_map_int32_enum() {
  // @@protoc_insertion_point(field_mutable_map:protobuf_unittest.TestArenaMapLite.map_int32_enum)
  return map_int32_enum_.MutableMap();
}

// map<int32, .protobuf_unittest.ForeignMessageArenaLite> map_int32_foreign_message = 17;
int TestArenaMapLite::map_int32_foreign_message_size() const {
  return map_int32_foreign_message_.size();
}
void TestArenaMapLite::clear_map_int32_foreign_message() {
  map_int32_foreign_message_.Clear();
}
 const ::google::protobuf::Map< ::google::protobuf::int32, ::protobuf_unittest::ForeignMessageArenaLite >&
TestArenaMapLite::map_int32_foreign_message() const {
  // @@protoc_insertion_point(field_map:protobuf_unittest.TestArenaMapLite.map_int32_foreign_message)
  return map_int32_foreign_message_.GetMap();
}
 ::google::protobuf::Map< ::google::protobuf::int32, ::protobuf_unittest::ForeignMessageArenaLite >*
TestArenaMapLite::mutable_map_int32_foreign_message() {
  // @@protoc_insertion_point(field_mutable_map:protobuf_unittest.TestArenaMapLite.map_int32_foreign_message)
  return map_int32_foreign_message_.MutableMap();
}

// map<int32, .protobuf_unittest_no_arena.ForeignMessageLite> map_int32_foreign_message_no_arena = 18;
int TestArenaMapLite::map_int32_foreign_message_no_arena_size() const {
  return map_int32_foreign_message_no_arena_.size();
}
void TestArenaMapLite::clear_map_int32_foreign_message_no_arena() {
  map_int32_foreign_message_no_arena_.Clear();
}
 const ::google::protobuf::Map< ::google::protobuf::int32, ::protobuf_unittest_no_arena::ForeignMessageLite >&
TestArenaMapLite::map_int32_foreign_message_no_arena() const {
  // @@protoc_insertion_point(field_map:protobuf_unittest.TestArenaMapLite.map_int32_foreign_message_no_arena)
  return map_int32_foreign_message_no_arena_.GetMap();
}
 ::google::protobuf::Map< ::google::protobuf::int32, ::protobuf_unittest_no_arena::ForeignMessageLite >*
TestArenaMapLite::mutable_map_int32_foreign_message_no_arena() {
  // @@protoc_insertion_point(field_mutable_map:protobuf_unittest.TestArenaMapLite.map_int32_foreign_message_no_arena)
  return map_int32_foreign_message_no_arena_.MutableMap();
}

inline const TestArenaMapLite* TestArenaMapLite::internal_default_instance() {
  return &TestArenaMapLite_default_instance_.get();
}
#endif  // PROTOBUF_INLINE_NOT_IN_HEADERS

// ===================================================================

static ::std::string* MutableUnknownFieldsForTestRequiredMessageMapLite(
    TestRequiredMessageMapLite* ptr) {
  return ptr->mutable_unknown_fields();
}

#if !defined(_MSC_VER) || _MSC_VER >= 1900
const int TestRequiredMessageMapLite::kMapFieldFieldNumber;
#endif  // !defined(_MSC_VER) || _MSC_VER >= 1900

TestRequiredMessageMapLite::TestRequiredMessageMapLite()
  : ::google::protobuf::MessageLite(), _arena_ptr_(NULL) {
  if (this != internal_default_instance()) protobuf_InitDefaults_google_2fprotobuf_2fmap_5flite_5funittest_2eproto();
  SharedCtor();
  // @@protoc_insertion_point(constructor:protobuf_unittest.TestRequiredMessageMapLite)
}
TestRequiredMessageMapLite::TestRequiredMessageMapLite(::google::protobuf::Arena* arena)
  : ::google::protobuf::MessageLite(),
  _arena_ptr_(arena),
  map_field_(arena) {
#ifdef GOOGLE_PROTOBUF_NO_STATIC_INITIALIZER
  protobuf_InitDefaults_google_2fprotobuf_2fmap_5flite_5funittest_2eproto();
#endif  // GOOGLE_PROTOBUF_NO_STATIC_INITIALIZER
  SharedCtor();
  RegisterArenaDtor(arena);
  // @@protoc_insertion_point(arena_constructor:protobuf_unittest.TestRequiredMessageMapLite)
}

void TestRequiredMessageMapLite::InitAsDefaultInstance() {
}

TestRequiredMessageMapLite::TestRequiredMessageMapLite(const TestRequiredMessageMapLite& from)
  : ::google::protobuf::MessageLite(),
    _arena_ptr_(NULL) {
  SharedCtor();
  UnsafeMergeFrom(from);
  // @@protoc_insertion_point(copy_constructor:protobuf_unittest.TestRequiredMessageMapLite)
}

void TestRequiredMessageMapLite::SharedCtor() {
  _cached_size_ = 0;
  _unknown_fields_.UnsafeSetDefault(
      &::google::protobuf::internal::GetEmptyStringAlreadyInited());
}

TestRequiredMessageMapLite::~TestRequiredMessageMapLite() {
  // @@protoc_insertion_point(destructor:protobuf_unittest.TestRequiredMessageMapLite)
  SharedDtor();
}

void TestRequiredMessageMapLite::SharedDtor() {
  ::google::protobuf::Arena* arena = GetArenaNoVirtual();
  if (arena != NULL) {
    return;
  }

  _unknown_fields_.Destroy(
      &::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      arena);
}

void TestRequiredMessageMapLite::ArenaDtor(void* object) {
  TestRequiredMessageMapLite* _this = reinterpret_cast< TestRequiredMessageMapLite* >(object);
  (void)_this;
}
void TestRequiredMessageMapLite::RegisterArenaDtor(::google::protobuf::Arena* arena) {
}
void TestRequiredMessageMapLite::SetCachedSize(int size) const {
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
}
const TestRequiredMessageMapLite& TestRequiredMessageMapLite::default_instance() {
  protobuf_InitDefaults_google_2fprotobuf_2fmap_5flite_5funittest_2eproto();
  return *internal_default_instance();
}

::google::protobuf::internal::ExplicitlyConstructed<TestRequiredMessageMapLite> TestRequiredMessageMapLite_default_instance_;

TestRequiredMessageMapLite* TestRequiredMessageMapLite::New(::google::protobuf::Arena* arena) const {
  return ::google::protobuf::Arena::CreateMessage<TestRequiredMessageMapLite>(arena);
}

void TestRequiredMessageMapLite::Clear() {
// @@protoc_insertion_point(message_clear_start:protobuf_unittest.TestRequiredMessageMapLite)
  map_field_.Clear();
  _has_bits_.Clear();
  _unknown_fields_.ClearToEmpty(
      &::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      GetArenaNoVirtual());
}

bool TestRequiredMessageMapLite::MergePartialFromCodedStream(
    ::google::protobuf::io::CodedInputStream* input) {
#define DO_(EXPRESSION) if (!GOOGLE_PREDICT_TRUE(EXPRESSION)) goto failure
  ::google::protobuf::uint32 tag;
  ::google::protobuf::io::LazyStringOutputStream unknown_fields_string(
      ::google::protobuf::NewPermanentCallback(
          &MutableUnknownFieldsForTestRequiredMessageMapLite, this));
  ::google::protobuf::io::CodedOutputStream unknown_fields_stream(
      &unknown_fields_string, false);
  // @@protoc_insertion_point(parse_start:protobuf_unittest.TestRequiredMessageMapLite)
  for (;;) {
    ::std::pair< ::google::protobuf::uint32, bool> p = input->ReadTagWithCutoff(127);
    tag = p.first;
    if (!p.second) goto handle_unusual;
    switch (::google::protobuf::internal::WireFormatLite::GetTagFieldNumber(tag)) {
      // map<int32, .protobuf_unittest.TestRequiredLite> map_field = 1;
      case 1: {
        if (tag == 10) {
          DO_(input->IncrementRecursionDepth());
         parse_loop_map_field:
          TestRequiredMessageMapLite_MapFieldEntry::Parser< ::google::protobuf::internal::MapFieldLite<
              ::google::protobuf::int32, ::protobuf_unittest::TestRequiredLite,
              ::google::protobuf::internal::WireFormatLite::TYPE_INT32,
              ::google::protobuf::internal::WireFormatLite::TYPE_MESSAGE,
              0 >,
            ::google::protobuf::Map< ::google::protobuf::int32, ::protobuf_unittest::TestRequiredLite > > parser(&map_field_);
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessageNoVirtual(
              input, &parser));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(10)) goto parse_loop_map_field;
        input->UnsafeDecrementRecursionDepth();
        if (input->ExpectAtEnd()) goto success;
        break;
      }

      default: {
      handle_unusual:
        if (tag == 0 ||
            ::google::protobuf::internal::WireFormatLite::GetTagWireType(tag) ==
            ::google::protobuf::internal::WireFormatLite::WIRETYPE_END_GROUP) {
          goto success;
        }
        DO_(::google::protobuf::internal::WireFormatLite::SkipField(
            input, tag, &unknown_fields_stream));
        break;
      }
    }
  }
success:
  // @@protoc_insertion_point(parse_success:protobuf_unittest.TestRequiredMessageMapLite)
  return true;
failure:
  // @@protoc_insertion_point(parse_failure:protobuf_unittest.TestRequiredMessageMapLite)
  return false;
#undef DO_
}

void TestRequiredMessageMapLite::SerializeWithCachedSizes(
    ::google::protobuf::io::CodedOutputStream* output) const {
  // @@protoc_insertion_point(serialize_start:protobuf_unittest.TestRequiredMessageMapLite)
  // map<int32, .protobuf_unittest.TestRequiredLite> map_field = 1;
  if (!this->map_field().empty()) {
    typedef ::google::protobuf::Map< ::google::protobuf::int32, ::protobuf_unittest::TestRequiredLite >::const_pointer
        ConstPtr;
    typedef ::google::protobuf::internal::SortItem< ::google::protobuf::int32, ConstPtr > SortItem;
    typedef ::google::protobuf::internal::CompareByFirstField<SortItem> Less;

    if (output->IsSerializationDeterminstic() &&
        this->map_field().size() > 1) {
      ::google::protobuf::scoped_array<SortItem> items(
          new SortItem[this->map_field().size()]);
      typedef ::google::protobuf::Map< ::google::protobuf::int32, ::protobuf_unittest::TestRequiredLite >::size_type size_type;
      size_type n = 0;
      for (::google::protobuf::Map< ::google::protobuf::int32, ::protobuf_unittest::TestRequiredLite >::const_iterator
          it = this->map_field().begin();
          it != this->map_field().end(); ++it, ++n) {
        items[n] = SortItem(&*it);
      }
      ::std::sort(&items[0], &items[n], Less());
      ::google::protobuf::scoped_ptr<TestRequiredMessageMapLite_MapFieldEntry> entry;
      for (size_type i = 0; i < n; i++) {
        entry.reset(map_field_.NewEntryWrapper(
            items[i].second->first, items[i].second->second));
        ::google::protobuf::internal::WireFormatLite::WriteMessage(
            1, *entry, output);
        if (entry->GetArena() != NULL) {
          entry.release();
        }
      }
    } else {
      ::google::protobuf::scoped_ptr<TestRequiredMessageMapLite_MapFieldEntry> entry;
      for (::google::protobuf::Map< ::google::protobuf::int32, ::protobuf_unittest::TestRequiredLite >::const_iterator
          it = this->map_field().begin();
          it != this->map_field().end(); ++it) {
        entry.reset(map_field_.NewEntryWrapper(
            it->first, it->second));
        ::google::protobuf::internal::WireFormatLite::WriteMessage(
            1, *entry, output);
        if (entry->GetArena() != NULL) {
          entry.release();
        }
      }
    }
  }

  output->WriteRaw(unknown_fields().data(),
                   static_cast<int>(unknown_fields().size()));
  // @@protoc_insertion_point(serialize_end:protobuf_unittest.TestRequiredMessageMapLite)
}

size_t TestRequiredMessageMapLite::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:protobuf_unittest.TestRequiredMessageMapLite)
  size_t total_size = 0;

  // map<int32, .protobuf_unittest.TestRequiredLite> map_field = 1;
  total_size += 1 *
      ::google::protobuf::internal::FromIntSize(this->map_field_size());
  {
    ::google::protobuf::scoped_ptr<TestRequiredMessageMapLite_MapFieldEntry> entry;
    for (::google::protobuf::Map< ::google::protobuf::int32, ::protobuf_unittest::TestRequiredLite >::const_iterator
        it = this->map_field().begin();
        it != this->map_field().end(); ++it) {
      if (entry.get() != NULL && entry->GetArena() != NULL) {
        entry.release();
      }
      entry.reset(map_field_.NewEntryWrapper(it->first, it->second));
      total_size += ::google::protobuf::internal::WireFormatLite::
          MessageSizeNoVirtual(*entry);
    }
    if (entry.get() != NULL && entry->GetArena() != NULL) {
      entry.release();
    }
  }

  total_size += unknown_fields().size();

  int cached_size = ::google::protobuf::internal::ToCachedSize(total_size);
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = cached_size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
  return total_size;
}

void TestRequiredMessageMapLite::CheckTypeAndMergeFrom(
    const ::google::protobuf::MessageLite& from) {
  MergeFrom(*::google::protobuf::down_cast<const TestRequiredMessageMapLite*>(&from));
}

void TestRequiredMessageMapLite::MergeFrom(const TestRequiredMessageMapLite& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:protobuf_unittest.TestRequiredMessageMapLite)
  if (GOOGLE_PREDICT_TRUE(&from != this)) {
    UnsafeMergeFrom(from);
  } else {
    MergeFromFail(__LINE__);
  }
}

void TestRequiredMessageMapLite::UnsafeMergeFrom(const TestRequiredMessageMapLite& from) {
  GOOGLE_DCHECK(&from != this);
  map_field_.MergeFrom(from.map_field_);
  if (!from.unknown_fields().empty()) {
    mutable_unknown_fields()->append(from.unknown_fields());
  }
}

void TestRequiredMessageMapLite::CopyFrom(const TestRequiredMessageMapLite& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:protobuf_unittest.TestRequiredMessageMapLite)
  if (&from == this) return;
  Clear();
  UnsafeMergeFrom(from);
}

bool TestRequiredMessageMapLite::IsInitialized() const {

  if (!::google::protobuf::internal::AllAreInitialized(this->map_field())) return false;
  return true;
}

void TestRequiredMessageMapLite::Swap(TestRequiredMessageMapLite* other) {
  if (other == this) return;
  if (GetArenaNoVirtual() == other->GetArenaNoVirtual()) {
    InternalSwap(other);
  } else {
    TestRequiredMessageMapLite temp;
    temp.UnsafeMergeFrom(*this);
    CopyFrom(*other);
    other->CopyFrom(temp);
  }
}
void TestRequiredMessageMapLite::UnsafeArenaSwap(TestRequiredMessageMapLite* other) {
  if (other == this) return;
  GOOGLE_DCHECK(GetArenaNoVirtual() == other->GetArenaNoVirtual());
  InternalSwap(other);
}
void TestRequiredMessageMapLite::InternalSwap(TestRequiredMessageMapLite* other) {
  map_field_.Swap(&other->map_field_);
  std::swap(_has_bits_[0], other->_has_bits_[0]);
  _unknown_fields_.Swap(&other->_unknown_fields_);
  std::swap(_cached_size_, other->_cached_size_);
}

::std::string TestRequiredMessageMapLite::GetTypeName() const {
  return "protobuf_unittest.TestRequiredMessageMapLite";
}

#if PROTOBUF_INLINE_NOT_IN_HEADERS
// TestRequiredMessageMapLite

// map<int32, .protobuf_unittest.TestRequiredLite> map_field = 1;
int TestRequiredMessageMapLite::map_field_size() const {
  return map_field_.size();
}
void TestRequiredMessageMapLite::clear_map_field() {
  map_field_.Clear();
}
 const ::google::protobuf::Map< ::google::protobuf::int32, ::protobuf_unittest::TestRequiredLite >&
TestRequiredMessageMapLite::map_field() const {
  // @@protoc_insertion_point(field_map:protobuf_unittest.TestRequiredMessageMapLite.map_field)
  return map_field_.GetMap();
}
 ::google::protobuf::Map< ::google::protobuf::int32, ::protobuf_unittest::TestRequiredLite >*
TestRequiredMessageMapLite::mutable_map_field() {
  // @@protoc_insertion_point(field_mutable_map:protobuf_unittest.TestRequiredMessageMapLite.map_field)
  return map_field_.MutableMap();
}

inline const TestRequiredMessageMapLite* TestRequiredMessageMapLite::internal_default_instance() {
  return &TestRequiredMessageMapLite_default_instance_.get();
}
#endif  // PROTOBUF_INLINE_NOT_IN_HEADERS

// ===================================================================

static ::std::string* MutableUnknownFieldsForTestEnumMapLite(
    TestEnumMapLite* ptr) {
  return ptr->mutable_unknown_fields();
}

#if !defined(_MSC_VER) || _MSC_VER >= 1900
const int TestEnumMapLite::kKnownMapFieldFieldNumber;
const int TestEnumMapLite::kUnknownMapFieldFieldNumber;
#endif  // !defined(_MSC_VER) || _MSC_VER >= 1900

TestEnumMapLite::TestEnumMapLite()
  : ::google::protobuf::MessageLite(), _arena_ptr_(NULL) {
  if (this != internal_default_instance()) protobuf_InitDefaults_google_2fprotobuf_2fmap_5flite_5funittest_2eproto();
  SharedCtor();
  // @@protoc_insertion_point(constructor:protobuf_unittest.TestEnumMapLite)
}
TestEnumMapLite::TestEnumMapLite(::google::protobuf::Arena* arena)
  : ::google::protobuf::MessageLite(),
  _arena_ptr_(arena),
  known_map_field_(arena),
  unknown_map_field_(arena) {
#ifdef GOOGLE_PROTOBUF_NO_STATIC_INITIALIZER
  protobuf_InitDefaults_google_2fprotobuf_2fmap_5flite_5funittest_2eproto();
#endif  // GOOGLE_PROTOBUF_NO_STATIC_INITIALIZER
  SharedCtor();
  RegisterArenaDtor(arena);
  // @@protoc_insertion_point(arena_constructor:protobuf_unittest.TestEnumMapLite)
}

void TestEnumMapLite::InitAsDefaultInstance() {
}

TestEnumMapLite::TestEnumMapLite(const TestEnumMapLite& from)
  : ::google::protobuf::MessageLite(),
    _arena_ptr_(NULL) {
  SharedCtor();
  UnsafeMergeFrom(from);
  // @@protoc_insertion_point(copy_constructor:protobuf_unittest.TestEnumMapLite)
}

void TestEnumMapLite::SharedCtor() {
  _cached_size_ = 0;
  _unknown_fields_.UnsafeSetDefault(
      &::google::protobuf::internal::GetEmptyStringAlreadyInited());
}

TestEnumMapLite::~TestEnumMapLite() {
  // @@protoc_insertion_point(destructor:protobuf_unittest.TestEnumMapLite)
  SharedDtor();
}

void TestEnumMapLite::SharedDtor() {
  ::google::protobuf::Arena* arena = GetArenaNoVirtual();
  if (arena != NULL) {
    return;
  }

  _unknown_fields_.Destroy(
      &::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      arena);
}

void TestEnumMapLite::ArenaDtor(void* object) {
  TestEnumMapLite* _this = reinterpret_cast< TestEnumMapLite* >(object);
  (void)_this;
}
void TestEnumMapLite::RegisterArenaDtor(::google::protobuf::Arena* arena) {
}
void TestEnumMapLite::SetCachedSize(int size) const {
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
}
const TestEnumMapLite& TestEnumMapLite::default_instance() {
  protobuf_InitDefaults_google_2fprotobuf_2fmap_5flite_5funittest_2eproto();
  return *internal_default_instance();
}

::google::protobuf::internal::ExplicitlyConstructed<TestEnumMapLite> TestEnumMapLite_default_instance_;

TestEnumMapLite* TestEnumMapLite::New(::google::protobuf::Arena* arena) const {
  return ::google::protobuf::Arena::CreateMessage<TestEnumMapLite>(arena);
}

void TestEnumMapLite::Clear() {
// @@protoc_insertion_point(message_clear_start:protobuf_unittest.TestEnumMapLite)
  known_map_field_.Clear();
  unknown_map_field_.Clear();
  _has_bits_.Clear();
  _unknown_fields_.ClearToEmpty(
      &::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      GetArenaNoVirtual());
}

bool TestEnumMapLite::MergePartialFromCodedStream(
    ::google::protobuf::io::CodedInputStream* input) {
#define DO_(EXPRESSION) if (!GOOGLE_PREDICT_TRUE(EXPRESSION)) goto failure
  ::google::protobuf::uint32 tag;
  ::google::protobuf::io::LazyStringOutputStream unknown_fields_string(
      ::google::protobuf::NewPermanentCallback(
          &MutableUnknownFieldsForTestEnumMapLite, this));
  ::google::protobuf::io::CodedOutputStream unknown_fields_stream(
      &unknown_fields_string, false);
  // @@protoc_insertion_point(parse_start:protobuf_unittest.TestEnumMapLite)
  for (;;) {
    ::std::pair< ::google::protobuf::uint32, bool> p = input->ReadTagWithCutoff(16383);
    tag = p.first;
    if (!p.second) goto handle_unusual;
    switch (::google::protobuf::internal::WireFormatLite::GetTagFieldNumber(tag)) {
      // map<int32, .protobuf_unittest.Proto2MapEnumLite> known_map_field = 101;
      case 101: {
        if (tag == 810) {
          DO_(input->IncrementRecursionDepth());
         parse_loop_known_map_field:
          ::google::protobuf::scoped_ptr<TestEnumMapLite_KnownMapFieldEntry> entry(known_map_field_.NewEntry());
          {
            ::std::string data;
            DO_(::google::protobuf::internal::WireFormatLite::ReadString(input, &data));
            DO_(entry->ParseFromString(data));
            if (::protobuf_unittest::Proto2MapEnumLite_IsValid(*entry->mutable_value())) {
              (*mutable_known_map_field())[entry->key()] =
                  static_cast< ::protobuf_unittest::Proto2MapEnumLite >(*entry->mutable_value());
            } else {
              unknown_fields_stream.WriteVarint32(810);
              unknown_fields_stream.WriteVarint32(data.size());
              unknown_fields_stream.WriteString(data);
            }
          }
          if (entry->GetArena() != NULL) entry.release();
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(810)) goto parse_loop_known_map_field;
        if (input->ExpectTag(818)) goto parse_loop_unknown_map_field;
        input->UnsafeDecrementRecursionDepth();
        break;
      }

      // map<int32, .protobuf_unittest.Proto2MapEnumLite> unknown_map_field = 102;
      case 102: {
        if (tag == 818) {
          DO_(input->IncrementRecursionDepth());
         parse_loop_unknown_map_field:
          ::google::protobuf::scoped_ptr<TestEnumMapLite_UnknownMapFieldEntry> entry(unknown_map_field_.NewEntry());
          {
            ::std::string data;
            DO_(::google::protobuf::internal::WireFormatLite::ReadString(input, &data));
            DO_(entry->ParseFromString(data));
            if (::protobuf_unittest::Proto2MapEnumLite_IsValid(*entry->mutable_value())) {
              (*mutable_unknown_map_field())[entry->key()] =
                  static_cast< ::protobuf_unittest::Proto2MapEnumLite >(*entry->mutable_value());
            } else {
              unknown_fields_stream.WriteVarint32(818);
              unknown_fields_stream.WriteVarint32(data.size());
              unknown_fields_stream.WriteString(data);
            }
          }
          if (entry->GetArena() != NULL) entry.release();
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(818)) goto parse_loop_unknown_map_field;
        input->UnsafeDecrementRecursionDepth();
        if (input->ExpectAtEnd()) goto success;
        break;
      }

      default: {
      handle_unusual:
        if (tag == 0 ||
            ::google::protobuf::internal::WireFormatLite::GetTagWireType(tag) ==
            ::google::protobuf::internal::WireFormatLite::WIRETYPE_END_GROUP) {
          goto success;
        }
        DO_(::google::protobuf::internal::WireFormatLite::SkipField(
            input, tag, &unknown_fields_stream));
        break;
      }
    }
  }
success:
  // @@protoc_insertion_point(parse_success:protobuf_unittest.TestEnumMapLite)
  return true;
failure:
  // @@protoc_insertion_point(parse_failure:protobuf_unittest.TestEnumMapLite)
  return false;
#undef DO_
}

void TestEnumMapLite::SerializeWithCachedSizes(
    ::google::protobuf::io::CodedOutputStream* output) const {
  // @@protoc_insertion_point(serialize_start:protobuf_unittest.TestEnumMapLite)
  // map<int32, .protobuf_unittest.Proto2MapEnumLite> known_map_field = 101;
  if (!this->known_map_field().empty()) {
    typedef ::google::protobuf::Map< ::google::protobuf::int32, ::protobuf_unittest::Proto2MapEnumLite >::const_pointer
        ConstPtr;
    typedef ::google::protobuf::internal::SortItem< ::google::protobuf::int32, ConstPtr > SortItem;
    typedef ::google::protobuf::internal::CompareByFirstField<SortItem> Less;

    if (output->IsSerializationDeterminstic() &&
        this->known_map_field().size() > 1) {
      ::google::protobuf::scoped_array<SortItem> items(
          new SortItem[this->known_map_field().size()]);
      typedef ::google::protobuf::Map< ::google::protobuf::int32, ::protobuf_unittest::Proto2MapEnumLite >::size_type size_type;
      size_type n = 0;
      for (::google::protobuf::Map< ::google::protobuf::int32, ::protobuf_unittest::Proto2MapEnumLite >::const_iterator
          it = this->known_map_field().begin();
          it != this->known_map_field().end(); ++it, ++n) {
        items[n] = SortItem(&*it);
      }
      ::std::sort(&items[0], &items[n], Less());
      ::google::protobuf::scoped_ptr<TestEnumMapLite_KnownMapFieldEntry> entry;
      for (size_type i = 0; i < n; i++) {
        entry.reset(known_map_field_.NewEnumEntryWrapper(
            items[i].second->first, items[i].second->second));
        ::google::protobuf::internal::WireFormatLite::WriteMessage(
            101, *entry, output);
        if (entry->GetArena() != NULL) {
          entry.release();
        }
      }
    } else {
      ::google::protobuf::scoped_ptr<TestEnumMapLite_KnownMapFieldEntry> entry;
      for (::google::protobuf::Map< ::google::protobuf::int32, ::protobuf_unittest::Proto2MapEnumLite >::const_iterator
          it = this->known_map_field().begin();
          it != this->known_map_field().end(); ++it) {
        entry.reset(known_map_field_.NewEnumEntryWrapper(
            it->first, it->second));
        ::google::protobuf::internal::WireFormatLite::WriteMessage(
            101, *entry, output);
        if (entry->GetArena() != NULL) {
          entry.release();
        }
      }
    }
  }

  // map<int32, .protobuf_unittest.Proto2MapEnumLite> unknown_map_field = 102;
  if (!this->unknown_map_field().empty()) {
    typedef ::google::protobuf::Map< ::google::protobuf::int32, ::protobuf_unittest::Proto2MapEnumLite >::const_pointer
        ConstPtr;
    typedef ::google::protobuf::internal::SortItem< ::google::protobuf::int32, ConstPtr > SortItem;
    typedef ::google::protobuf::internal::CompareByFirstField<SortItem> Less;

    if (output->IsSerializationDeterminstic() &&
        this->unknown_map_field().size() > 1) {
      ::google::protobuf::scoped_array<SortItem> items(
          new SortItem[this->unknown_map_field().size()]);
      typedef ::google::protobuf::Map< ::google::protobuf::int32, ::protobuf_unittest::Proto2MapEnumLite >::size_type size_type;
      size_type n = 0;
      for (::google::protobuf::Map< ::google::protobuf::int32, ::protobuf_unittest::Proto2MapEnumLite >::const_iterator
          it = this->unknown_map_field().begin();
          it != this->unknown_map_field().end(); ++it, ++n) {
        items[n] = SortItem(&*it);
      }
      ::std::sort(&items[0], &items[n], Less());
      ::google::protobuf::scoped_ptr<TestEnumMapLite_UnknownMapFieldEntry> entry;
      for (size_type i = 0; i < n; i++) {
        entry.reset(unknown_map_field_.NewEnumEntryWrapper(
            items[i].second->first, items[i].second->second));
        ::google::protobuf::internal::WireFormatLite::WriteMessage(
            102, *entry, output);
        if (entry->GetArena() != NULL) {
          entry.release();
        }
      }
    } else {
      ::google::protobuf::scoped_ptr<TestEnumMapLite_UnknownMapFieldEntry> entry;
      for (::google::protobuf::Map< ::google::protobuf::int32, ::protobuf_unittest::Proto2MapEnumLite >::const_iterator
          it = this->unknown_map_field().begin();
          it != this->unknown_map_field().end(); ++it) {
        entry.reset(unknown_map_field_.NewEnumEntryWrapper(
            it->first, it->second));
        ::google::protobuf::internal::WireFormatLite::WriteMessage(
            102, *entry, output);
        if (entry->GetArena() != NULL) {
          entry.release();
        }
      }
    }
  }

  output->WriteRaw(unknown_fields().data(),
                   static_cast<int>(unknown_fields().size()));
  // @@protoc_insertion_point(serialize_end:protobuf_unittest.TestEnumMapLite)
}

size_t TestEnumMapLite::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:protobuf_unittest.TestEnumMapLite)
  size_t total_size = 0;

  // map<int32, .protobuf_unittest.Proto2MapEnumLite> known_map_field = 101;
  total_size += 2 *
      ::google::protobuf::internal::FromIntSize(this->known_map_field_size());
  {
    ::google::protobuf::scoped_ptr<TestEnumMapLite_KnownMapFieldEntry> entry;
    for (::google::protobuf::Map< ::google::protobuf::int32, ::protobuf_unittest::Proto2MapEnumLite >::const_iterator
        it = this->known_map_field().begin();
        it != this->known_map_field().end(); ++it) {
      if (entry.get() != NULL && entry->GetArena() != NULL) {
        entry.release();
      }
      entry.reset(known_map_field_.NewEnumEntryWrapper(it->first, it->second));
      total_size += ::google::protobuf::internal::WireFormatLite::
          MessageSizeNoVirtual(*entry);
    }
    if (entry.get() != NULL && entry->GetArena() != NULL) {
      entry.release();
    }
  }

  // map<int32, .protobuf_unittest.Proto2MapEnumLite> unknown_map_field = 102;
  total_size += 2 *
      ::google::protobuf::internal::FromIntSize(this->unknown_map_field_size());
  {
    ::google::protobuf::scoped_ptr<TestEnumMapLite_UnknownMapFieldEntry> entry;
    for (::google::protobuf::Map< ::google::protobuf::int32, ::protobuf_unittest::Proto2MapEnumLite >::const_iterator
        it = this->unknown_map_field().begin();
        it != this->unknown_map_field().end(); ++it) {
      if (entry.get() != NULL && entry->GetArena() != NULL) {
        entry.release();
      }
      entry.reset(unknown_map_field_.NewEnumEntryWrapper(it->first, it->second));
      total_size += ::google::protobuf::internal::WireFormatLite::
          MessageSizeNoVirtual(*entry);
    }
    if (entry.get() != NULL && entry->GetArena() != NULL) {
      entry.release();
    }
  }

  total_size += unknown_fields().size();

  int cached_size = ::google::protobuf::internal::ToCachedSize(total_size);
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = cached_size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
  return total_size;
}

void TestEnumMapLite::CheckTypeAndMergeFrom(
    const ::google::protobuf::MessageLite& from) {
  MergeFrom(*::google::protobuf::down_cast<const TestEnumMapLite*>(&from));
}

void TestEnumMapLite::MergeFrom(const TestEnumMapLite& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:protobuf_unittest.TestEnumMapLite)
  if (GOOGLE_PREDICT_TRUE(&from != this)) {
    UnsafeMergeFrom(from);
  } else {
    MergeFromFail(__LINE__);
  }
}

void TestEnumMapLite::UnsafeMergeFrom(const TestEnumMapLite& from) {
  GOOGLE_DCHECK(&from != this);
  known_map_field_.MergeFrom(from.known_map_field_);
  unknown_map_field_.MergeFrom(from.unknown_map_field_);
  if (!from.unknown_fields().empty()) {
    mutable_unknown_fields()->append(from.unknown_fields());
  }
}

void TestEnumMapLite::CopyFrom(const TestEnumMapLite& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:protobuf_unittest.TestEnumMapLite)
  if (&from == this) return;
  Clear();
  UnsafeMergeFrom(from);
}

bool TestEnumMapLite::IsInitialized() const {

  return true;
}

void TestEnumMapLite::Swap(TestEnumMapLite* other) {
  if (other == this) return;
  if (GetArenaNoVirtual() == other->GetArenaNoVirtual()) {
    InternalSwap(other);
  } else {
    TestEnumMapLite temp;
    temp.UnsafeMergeFrom(*this);
    CopyFrom(*other);
    other->CopyFrom(temp);
  }
}
void TestEnumMapLite::UnsafeArenaSwap(TestEnumMapLite* other) {
  if (other == this) return;
  GOOGLE_DCHECK(GetArenaNoVirtual() == other->GetArenaNoVirtual());
  InternalSwap(other);
}
void TestEnumMapLite::InternalSwap(TestEnumMapLite* other) {
  known_map_field_.Swap(&other->known_map_field_);
  unknown_map_field_.Swap(&other->unknown_map_field_);
  std::swap(_has_bits_[0], other->_has_bits_[0]);
  _unknown_fields_.Swap(&other->_unknown_fields_);
  std::swap(_cached_size_, other->_cached_size_);
}

::std::string TestEnumMapLite::GetTypeName() const {
  return "protobuf_unittest.TestEnumMapLite";
}

#if PROTOBUF_INLINE_NOT_IN_HEADERS
// TestEnumMapLite

// map<int32, .protobuf_unittest.Proto2MapEnumLite> known_map_field = 101;
int TestEnumMapLite::known_map_field_size() const {
  return known_map_field_.size();
}
void TestEnumMapLite::clear_known_map_field() {
  known_map_field_.Clear();
}
 const ::google::protobuf::Map< ::google::protobuf::int32, ::protobuf_unittest::Proto2MapEnumLite >&
TestEnumMapLite::known_map_field() const {
  // @@protoc_insertion_point(field_map:protobuf_unittest.TestEnumMapLite.known_map_field)
  return known_map_field_.GetMap();
}
 ::google::protobuf::Map< ::google::protobuf::int32, ::protobuf_unittest::Proto2MapEnumLite >*
TestEnumMapLite::mutable_known_map_field() {
  // @@protoc_insertion_point(field_mutable_map:protobuf_unittest.TestEnumMapLite.known_map_field)
  return known_map_field_.MutableMap();
}

// map<int32, .protobuf_unittest.Proto2MapEnumLite> unknown_map_field = 102;
int TestEnumMapLite::unknown_map_field_size() const {
  return unknown_map_field_.size();
}
void TestEnumMapLite::clear_unknown_map_field() {
  unknown_map_field_.Clear();
}
 const ::google::protobuf::Map< ::google::protobuf::int32, ::protobuf_unittest::Proto2MapEnumLite >&
TestEnumMapLite::unknown_map_field() const {
  // @@protoc_insertion_point(field_map:protobuf_unittest.TestEnumMapLite.unknown_map_field)
  return unknown_map_field_.GetMap();
}
 ::google::protobuf::Map< ::google::protobuf::int32, ::protobuf_unittest::Proto2MapEnumLite >*
TestEnumMapLite::mutable_unknown_map_field() {
  // @@protoc_insertion_point(field_mutable_map:protobuf_unittest.TestEnumMapLite.unknown_map_field)
  return unknown_map_field_.MutableMap();
}

inline const TestEnumMapLite* TestEnumMapLite::internal_default_instance() {
  return &TestEnumMapLite_default_instance_.get();
}
#endif  // PROTOBUF_INLINE_NOT_IN_HEADERS

// ===================================================================

static ::std::string* MutableUnknownFieldsForTestEnumMapPlusExtraLite(
    TestEnumMapPlusExtraLite* ptr) {
  return ptr->mutable_unknown_fields();
}

#if !defined(_MSC_VER) || _MSC_VER >= 1900
const int TestEnumMapPlusExtraLite::kKnownMapFieldFieldNumber;
const int TestEnumMapPlusExtraLite::kUnknownMapFieldFieldNumber;
#endif  // !defined(_MSC_VER) || _MSC_VER >= 1900

TestEnumMapPlusExtraLite::TestEnumMapPlusExtraLite()
  : ::google::protobuf::MessageLite(), _arena_ptr_(NULL) {
  if (this != internal_default_instance()) protobuf_InitDefaults_google_2fprotobuf_2fmap_5flite_5funittest_2eproto();
  SharedCtor();
  // @@protoc_insertion_point(constructor:protobuf_unittest.TestEnumMapPlusExtraLite)
}
TestEnumMapPlusExtraLite::TestEnumMapPlusExtraLite(::google::protobuf::Arena* arena)
  : ::google::protobuf::MessageLite(),
  _arena_ptr_(arena),
  known_map_field_(arena),
  unknown_map_field_(arena) {
#ifdef GOOGLE_PROTOBUF_NO_STATIC_INITIALIZER
  protobuf_InitDefaults_google_2fprotobuf_2fmap_5flite_5funittest_2eproto();
#endif  // GOOGLE_PROTOBUF_NO_STATIC_INITIALIZER
  SharedCtor();
  RegisterArenaDtor(arena);
  // @@protoc_insertion_point(arena_constructor:protobuf_unittest.TestEnumMapPlusExtraLite)
}

void TestEnumMapPlusExtraLite::InitAsDefaultInstance() {
}

TestEnumMapPlusExtraLite::TestEnumMapPlusExtraLite(const TestEnumMapPlusExtraLite& from)
  : ::google::protobuf::MessageLite(),
    _arena_ptr_(NULL) {
  SharedCtor();
  UnsafeMergeFrom(from);
  // @@protoc_insertion_point(copy_constructor:protobuf_unittest.TestEnumMapPlusExtraLite)
}

void TestEnumMapPlusExtraLite::SharedCtor() {
  _cached_size_ = 0;
  _unknown_fields_.UnsafeSetDefault(
      &::google::protobuf::internal::GetEmptyStringAlreadyInited());
}

TestEnumMapPlusExtraLite::~TestEnumMapPlusExtraLite() {
  // @@protoc_insertion_point(destructor:protobuf_unittest.TestEnumMapPlusExtraLite)
  SharedDtor();
}

void TestEnumMapPlusExtraLite::SharedDtor() {
  ::google::protobuf::Arena* arena = GetArenaNoVirtual();
  if (arena != NULL) {
    return;
  }

  _unknown_fields_.Destroy(
      &::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      arena);
}

void TestEnumMapPlusExtraLite::ArenaDtor(void* object) {
  TestEnumMapPlusExtraLite* _this = reinterpret_cast< TestEnumMapPlusExtraLite* >(object);
  (void)_this;
}
void TestEnumMapPlusExtraLite::RegisterArenaDtor(::google::protobuf::Arena* arena) {
}
void TestEnumMapPlusExtraLite::SetCachedSize(int size) const {
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
}
const TestEnumMapPlusExtraLite& TestEnumMapPlusExtraLite::default_instance() {
  protobuf_InitDefaults_google_2fprotobuf_2fmap_5flite_5funittest_2eproto();
  return *internal_default_instance();
}

::google::protobuf::internal::ExplicitlyConstructed<TestEnumMapPlusExtraLite> TestEnumMapPlusExtraLite_default_instance_;

TestEnumMapPlusExtraLite* TestEnumMapPlusExtraLite::New(::google::protobuf::Arena* arena) const {
  return ::google::protobuf::Arena::CreateMessage<TestEnumMapPlusExtraLite>(arena);
}

void TestEnumMapPlusExtraLite::Clear() {
// @@protoc_insertion_point(message_clear_start:protobuf_unittest.TestEnumMapPlusExtraLite)
  known_map_field_.Clear();
  unknown_map_field_.Clear();
  _has_bits_.Clear();
  _unknown_fields_.ClearToEmpty(
      &::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      GetArenaNoVirtual());
}

bool TestEnumMapPlusExtraLite::MergePartialFromCodedStream(
    ::google::protobuf::io::CodedInputStream* input) {
#define DO_(EXPRESSION) if (!GOOGLE_PREDICT_TRUE(EXPRESSION)) goto failure
  ::google::protobuf::uint32 tag;
  ::google::protobuf::io::LazyStringOutputStream unknown_fields_string(
      ::google::protobuf::NewPermanentCallback(
          &MutableUnknownFieldsForTestEnumMapPlusExtraLite, this));
  ::google::protobuf::io::CodedOutputStream unknown_fields_stream(
      &unknown_fields_string, false);
  // @@protoc_insertion_point(parse_start:protobuf_unittest.TestEnumMapPlusExtraLite)
  for (;;) {
    ::std::pair< ::google::protobuf::uint32, bool> p = input->ReadTagWithCutoff(16383);
    tag = p.first;
    if (!p.second) goto handle_unusual;
    switch (::google::protobuf::internal::WireFormatLite::GetTagFieldNumber(tag)) {
      // map<int32, .protobuf_unittest.Proto2MapEnumPlusExtraLite> known_map_field = 101;
      case 101: {
        if (tag == 810) {
          DO_(input->IncrementRecursionDepth());
         parse_loop_known_map_field:
          ::google::protobuf::scoped_ptr<TestEnumMapPlusExtraLite_KnownMapFieldEntry> entry(known_map_field_.NewEntry());
          {
            ::std::string data;
            DO_(::google::protobuf::internal::WireFormatLite::ReadString(input, &data));
            DO_(entry->ParseFromString(data));
            if (::protobuf_unittest::Proto2MapEnumPlusExtraLite_IsValid(*entry->mutable_value())) {
              (*mutable_known_map_field())[entry->key()] =
                  static_cast< ::protobuf_unittest::Proto2MapEnumPlusExtraLite >(*entry->mutable_value());
            } else {
              unknown_fields_stream.WriteVarint32(810);
              unknown_fields_stream.WriteVarint32(data.size());
              unknown_fields_stream.WriteString(data);
            }
          }
          if (entry->GetArena() != NULL) entry.release();
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(810)) goto parse_loop_known_map_field;
        if (input->ExpectTag(818)) goto parse_loop_unknown_map_field;
        input->UnsafeDecrementRecursionDepth();
        break;
      }

      // map<int32, .protobuf_unittest.Proto2MapEnumPlusExtraLite> unknown_map_field = 102;
      case 102: {
        if (tag == 818) {
          DO_(input->IncrementRecursionDepth());
         parse_loop_unknown_map_field:
          ::google::protobuf::scoped_ptr<TestEnumMapPlusExtraLite_UnknownMapFieldEntry> entry(unknown_map_field_.NewEntry());
          {
            ::std::string data;
            DO_(::google::protobuf::internal::WireFormatLite::ReadString(input, &data));
            DO_(entry->ParseFromString(data));
            if (::protobuf_unittest::Proto2MapEnumPlusExtraLite_IsValid(*entry->mutable_value())) {
              (*mutable_unknown_map_field())[entry->key()] =
                  static_cast< ::protobuf_unittest::Proto2MapEnumPlusExtraLite >(*entry->mutable_value());
            } else {
              unknown_fields_stream.WriteVarint32(818);
              unknown_fields_stream.WriteVarint32(data.size());
              unknown_fields_stream.WriteString(data);
            }
          }
          if (entry->GetArena() != NULL) entry.release();
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(818)) goto parse_loop_unknown_map_field;
        input->UnsafeDecrementRecursionDepth();
        if (input->ExpectAtEnd()) goto success;
        break;
      }

      default: {
      handle_unusual:
        if (tag == 0 ||
            ::google::protobuf::internal::WireFormatLite::GetTagWireType(tag) ==
            ::google::protobuf::internal::WireFormatLite::WIRETYPE_END_GROUP) {
          goto success;
        }
        DO_(::google::protobuf::internal::WireFormatLite::SkipField(
            input, tag, &unknown_fields_stream));
        break;
      }
    }
  }
success:
  // @@protoc_insertion_point(parse_success:protobuf_unittest.TestEnumMapPlusExtraLite)
  return true;
failure:
  // @@protoc_insertion_point(parse_failure:protobuf_unittest.TestEnumMapPlusExtraLite)
  return false;
#undef DO_
}

void TestEnumMapPlusExtraLite::SerializeWithCachedSizes(
    ::google::protobuf::io::CodedOutputStream* output) const {
  // @@protoc_insertion_point(serialize_start:protobuf_unittest.TestEnumMapPlusExtraLite)
  // map<int32, .protobuf_unittest.Proto2MapEnumPlusExtraLite> known_map_field = 101;
  if (!this->known_map_field().empty()) {
    typedef ::google::protobuf::Map< ::google::protobuf::int32, ::protobuf_unittest::Proto2MapEnumPlusExtraLite >::const_pointer
        ConstPtr;
    typedef ::google::protobuf::internal::SortItem< ::google::protobuf::int32, ConstPtr > SortItem;
    typedef ::google::protobuf::internal::CompareByFirstField<SortItem> Less;

    if (output->IsSerializationDeterminstic() &&
        this->known_map_field().size() > 1) {
      ::google::protobuf::scoped_array<SortItem> items(
          new SortItem[this->known_map_field().size()]);
      typedef ::google::protobuf::Map< ::google::protobuf::int32, ::protobuf_unittest::Proto2MapEnumPlusExtraLite >::size_type size_type;
      size_type n = 0;
      for (::google::protobuf::Map< ::google::protobuf::int32, ::protobuf_unittest::Proto2MapEnumPlusExtraLite >::const_iterator
          it = this->known_map_field().begin();
          it != this->known_map_field().end(); ++it, ++n) {
        items[n] = SortItem(&*it);
      }
      ::std::sort(&items[0], &items[n], Less());
      ::google::protobuf::scoped_ptr<TestEnumMapPlusExtraLite_KnownMapFieldEntry> entry;
      for (size_type i = 0; i < n; i++) {
        entry.reset(known_map_field_.NewEnumEntryWrapper(
            items[i].second->first, items[i].second->second));
        ::google::protobuf::internal::WireFormatLite::WriteMessage(
            101, *entry, output);
        if (entry->GetArena() != NULL) {
          entry.release();
        }
      }
    } else {
      ::google::protobuf::scoped_ptr<TestEnumMapPlusExtraLite_KnownMapFieldEntry> entry;
      for (::google::protobuf::Map< ::google::protobuf::int32, ::protobuf_unittest::Proto2MapEnumPlusExtraLite >::const_iterator
          it = this->known_map_field().begin();
          it != this->known_map_field().end(); ++it) {
        entry.reset(known_map_field_.NewEnumEntryWrapper(
            it->first, it->second));
        ::google::protobuf::internal::WireFormatLite::WriteMessage(
            101, *entry, output);
        if (entry->GetArena() != NULL) {
          entry.release();
        }
      }
    }
  }

  // map<int32, .protobuf_unittest.Proto2MapEnumPlusExtraLite> unknown_map_field = 102;
  if (!this->unknown_map_field().empty()) {
    typedef ::google::protobuf::Map< ::google::protobuf::int32, ::protobuf_unittest::Proto2MapEnumPlusExtraLite >::const_pointer
        ConstPtr;
    typedef ::google::protobuf::internal::SortItem< ::google::protobuf::int32, ConstPtr > SortItem;
    typedef ::google::protobuf::internal::CompareByFirstField<SortItem> Less;

    if (output->IsSerializationDeterminstic() &&
        this->unknown_map_field().size() > 1) {
      ::google::protobuf::scoped_array<SortItem> items(
          new SortItem[this->unknown_map_field().size()]);
      typedef ::google::protobuf::Map< ::google::protobuf::int32, ::protobuf_unittest::Proto2MapEnumPlusExtraLite >::size_type size_type;
      size_type n = 0;
      for (::google::protobuf::Map< ::google::protobuf::int32, ::protobuf_unittest::Proto2MapEnumPlusExtraLite >::const_iterator
          it = this->unknown_map_field().begin();
          it != this->unknown_map_field().end(); ++it, ++n) {
        items[n] = SortItem(&*it);
      }
      ::std::sort(&items[0], &items[n], Less());
      ::google::protobuf::scoped_ptr<TestEnumMapPlusExtraLite_UnknownMapFieldEntry> entry;
      for (size_type i = 0; i < n; i++) {
        entry.reset(unknown_map_field_.NewEnumEntryWrapper(
            items[i].second->first, items[i].second->second));
        ::google::protobuf::internal::WireFormatLite::WriteMessage(
            102, *entry, output);
        if (entry->GetArena() != NULL) {
          entry.release();
        }
      }
    } else {
      ::google::protobuf::scoped_ptr<TestEnumMapPlusExtraLite_UnknownMapFieldEntry> entry;
      for (::google::protobuf::Map< ::google::protobuf::int32, ::protobuf_unittest::Proto2MapEnumPlusExtraLite >::const_iterator
          it = this->unknown_map_field().begin();
          it != this->unknown_map_field().end(); ++it) {
        entry.reset(unknown_map_field_.NewEnumEntryWrapper(
            it->first, it->second));
        ::google::protobuf::internal::WireFormatLite::WriteMessage(
            102, *entry, output);
        if (entry->GetArena() != NULL) {
          entry.release();
        }
      }
    }
  }

  output->WriteRaw(unknown_fields().data(),
                   static_cast<int>(unknown_fields().size()));
  // @@protoc_insertion_point(serialize_end:protobuf_unittest.TestEnumMapPlusExtraLite)
}

size_t TestEnumMapPlusExtraLite::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:protobuf_unittest.TestEnumMapPlusExtraLite)
  size_t total_size = 0;

  // map<int32, .protobuf_unittest.Proto2MapEnumPlusExtraLite> known_map_field = 101;
  total_size += 2 *
      ::google::protobuf::internal::FromIntSize(this->known_map_field_size());
  {
    ::google::protobuf::scoped_ptr<TestEnumMapPlusExtraLite_KnownMapFieldEntry> entry;
    for (::google::protobuf::Map< ::google::protobuf::int32, ::protobuf_unittest::Proto2MapEnumPlusExtraLite >::const_iterator
        it = this->known_map_field().begin();
        it != this->known_map_field().end(); ++it) {
      if (entry.get() != NULL && entry->GetArena() != NULL) {
        entry.release();
      }
      entry.reset(known_map_field_.NewEnumEntryWrapper(it->first, it->second));
      total_size += ::google::protobuf::internal::WireFormatLite::
          MessageSizeNoVirtual(*entry);
    }
    if (entry.get() != NULL && entry->GetArena() != NULL) {
      entry.release();
    }
  }

  // map<int32, .protobuf_unittest.Proto2MapEnumPlusExtraLite> unknown_map_field = 102;
  total_size += 2 *
      ::google::protobuf::internal::FromIntSize(this->unknown_map_field_size());
  {
    ::google::protobuf::scoped_ptr<TestEnumMapPlusExtraLite_UnknownMapFieldEntry> entry;
    for (::google::protobuf::Map< ::google::protobuf::int32, ::protobuf_unittest::Proto2MapEnumPlusExtraLite >::const_iterator
        it = this->unknown_map_field().begin();
        it != this->unknown_map_field().end(); ++it) {
      if (entry.get() != NULL && entry->GetArena() != NULL) {
        entry.release();
      }
      entry.reset(unknown_map_field_.NewEnumEntryWrapper(it->first, it->second));
      total_size += ::google::protobuf::internal::WireFormatLite::
          MessageSizeNoVirtual(*entry);
    }
    if (entry.get() != NULL && entry->GetArena() != NULL) {
      entry.release();
    }
  }

  total_size += unknown_fields().size();

  int cached_size = ::google::protobuf::internal::ToCachedSize(total_size);
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = cached_size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
  return total_size;
}

void TestEnumMapPlusExtraLite::CheckTypeAndMergeFrom(
    const ::google::protobuf::MessageLite& from) {
  MergeFrom(*::google::protobuf::down_cast<const TestEnumMapPlusExtraLite*>(&from));
}

void TestEnumMapPlusExtraLite::MergeFrom(const TestEnumMapPlusExtraLite& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:protobuf_unittest.TestEnumMapPlusExtraLite)
  if (GOOGLE_PREDICT_TRUE(&from != this)) {
    UnsafeMergeFrom(from);
  } else {
    MergeFromFail(__LINE__);
  }
}

void TestEnumMapPlusExtraLite::UnsafeMergeFrom(const TestEnumMapPlusExtraLite& from) {
  GOOGLE_DCHECK(&from != this);
  known_map_field_.MergeFrom(from.known_map_field_);
  unknown_map_field_.MergeFrom(from.unknown_map_field_);
  if (!from.unknown_fields().empty()) {
    mutable_unknown_fields()->append(from.unknown_fields());
  }
}

void TestEnumMapPlusExtraLite::CopyFrom(const TestEnumMapPlusExtraLite& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:protobuf_unittest.TestEnumMapPlusExtraLite)
  if (&from == this) return;
  Clear();
  UnsafeMergeFrom(from);
}

bool TestEnumMapPlusExtraLite::IsInitialized() const {

  return true;
}

void TestEnumMapPlusExtraLite::Swap(TestEnumMapPlusExtraLite* other) {
  if (other == this) return;
  if (GetArenaNoVirtual() == other->GetArenaNoVirtual()) {
    InternalSwap(other);
  } else {
    TestEnumMapPlusExtraLite temp;
    temp.UnsafeMergeFrom(*this);
    CopyFrom(*other);
    other->CopyFrom(temp);
  }
}
void TestEnumMapPlusExtraLite::UnsafeArenaSwap(TestEnumMapPlusExtraLite* other) {
  if (other == this) return;
  GOOGLE_DCHECK(GetArenaNoVirtual() == other->GetArenaNoVirtual());
  InternalSwap(other);
}
void TestEnumMapPlusExtraLite::InternalSwap(TestEnumMapPlusExtraLite* other) {
  known_map_field_.Swap(&other->known_map_field_);
  unknown_map_field_.Swap(&other->unknown_map_field_);
  std::swap(_has_bits_[0], other->_has_bits_[0]);
  _unknown_fields_.Swap(&other->_unknown_fields_);
  std::swap(_cached_size_, other->_cached_size_);
}

::std::string TestEnumMapPlusExtraLite::GetTypeName() const {
  return "protobuf_unittest.TestEnumMapPlusExtraLite";
}

#if PROTOBUF_INLINE_NOT_IN_HEADERS
// TestEnumMapPlusExtraLite

// map<int32, .protobuf_unittest.Proto2MapEnumPlusExtraLite> known_map_field = 101;
int TestEnumMapPlusExtraLite::known_map_field_size() const {
  return known_map_field_.size();
}
void TestEnumMapPlusExtraLite::clear_known_map_field() {
  known_map_field_.Clear();
}
 const ::google::protobuf::Map< ::google::protobuf::int32, ::protobuf_unittest::Proto2MapEnumPlusExtraLite >&
TestEnumMapPlusExtraLite::known_map_field() const {
  // @@protoc_insertion_point(field_map:protobuf_unittest.TestEnumMapPlusExtraLite.known_map_field)
  return known_map_field_.GetMap();
}
 ::google::protobuf::Map< ::google::protobuf::int32, ::protobuf_unittest::Proto2MapEnumPlusExtraLite >*
TestEnumMapPlusExtraLite::mutable_known_map_field() {
  // @@protoc_insertion_point(field_mutable_map:protobuf_unittest.TestEnumMapPlusExtraLite.known_map_field)
  return known_map_field_.MutableMap();
}

// map<int32, .protobuf_unittest.Proto2MapEnumPlusExtraLite> unknown_map_field = 102;
int TestEnumMapPlusExtraLite::unknown_map_field_size() const {
  return unknown_map_field_.size();
}
void TestEnumMapPlusExtraLite::clear_unknown_map_field() {
  unknown_map_field_.Clear();
}
 const ::google::protobuf::Map< ::google::protobuf::int32, ::protobuf_unittest::Proto2MapEnumPlusExtraLite >&
TestEnumMapPlusExtraLite::unknown_map_field() const {
  // @@protoc_insertion_point(field_map:protobuf_unittest.TestEnumMapPlusExtraLite.unknown_map_field)
  return unknown_map_field_.GetMap();
}
 ::google::protobuf::Map< ::google::protobuf::int32, ::protobuf_unittest::Proto2MapEnumPlusExtraLite >*
TestEnumMapPlusExtraLite::mutable_unknown_map_field() {
  // @@protoc_insertion_point(field_mutable_map:protobuf_unittest.TestEnumMapPlusExtraLite.unknown_map_field)
  return unknown_map_field_.MutableMap();
}

inline const TestEnumMapPlusExtraLite* TestEnumMapPlusExtraLite::internal_default_instance() {
  return &TestEnumMapPlusExtraLite_default_instance_.get();
}
#endif  // PROTOBUF_INLINE_NOT_IN_HEADERS

// ===================================================================

static ::std::string* MutableUnknownFieldsForTestMessageMapLite(
    TestMessageMapLite* ptr) {
  return ptr->mutable_unknown_fields();
}

#if !defined(_MSC_VER) || _MSC_VER >= 1900
const int TestMessageMapLite::kMapInt32MessageFieldNumber;
#endif  // !defined(_MSC_VER) || _MSC_VER >= 1900

TestMessageMapLite::TestMessageMapLite()
  : ::google::protobuf::MessageLite(), _arena_ptr_(NULL) {
  if (this != internal_default_instance()) protobuf_InitDefaults_google_2fprotobuf_2fmap_5flite_5funittest_2eproto();
  SharedCtor();
  // @@protoc_insertion_point(constructor:protobuf_unittest.TestMessageMapLite)
}
TestMessageMapLite::TestMessageMapLite(::google::protobuf::Arena* arena)
  : ::google::protobuf::MessageLite(),
  _arena_ptr_(arena),
  map_int32_message_(arena) {
#ifdef GOOGLE_PROTOBUF_NO_STATIC_INITIALIZER
  protobuf_InitDefaults_google_2fprotobuf_2fmap_5flite_5funittest_2eproto();
#endif  // GOOGLE_PROTOBUF_NO_STATIC_INITIALIZER
  SharedCtor();
  RegisterArenaDtor(arena);
  // @@protoc_insertion_point(arena_constructor:protobuf_unittest.TestMessageMapLite)
}

void TestMessageMapLite::InitAsDefaultInstance() {
}

TestMessageMapLite::TestMessageMapLite(const TestMessageMapLite& from)
  : ::google::protobuf::MessageLite(),
    _arena_ptr_(NULL) {
  SharedCtor();
  UnsafeMergeFrom(from);
  // @@protoc_insertion_point(copy_constructor:protobuf_unittest.TestMessageMapLite)
}

void TestMessageMapLite::SharedCtor() {
  _cached_size_ = 0;
  _unknown_fields_.UnsafeSetDefault(
      &::google::protobuf::internal::GetEmptyStringAlreadyInited());
}

TestMessageMapLite::~TestMessageMapLite() {
  // @@protoc_insertion_point(destructor:protobuf_unittest.TestMessageMapLite)
  SharedDtor();
}

void TestMessageMapLite::SharedDtor() {
  ::google::protobuf::Arena* arena = GetArenaNoVirtual();
  if (arena != NULL) {
    return;
  }

  _unknown_fields_.Destroy(
      &::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      arena);
}

void TestMessageMapLite::ArenaDtor(void* object) {
  TestMessageMapLite* _this = reinterpret_cast< TestMessageMapLite* >(object);
  (void)_this;
}
void TestMessageMapLite::RegisterArenaDtor(::google::protobuf::Arena* arena) {
}
void TestMessageMapLite::SetCachedSize(int size) const {
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
}
const TestMessageMapLite& TestMessageMapLite::default_instance() {
  protobuf_InitDefaults_google_2fprotobuf_2fmap_5flite_5funittest_2eproto();
  return *internal_default_instance();
}

::google::protobuf::internal::ExplicitlyConstructed<TestMessageMapLite> TestMessageMapLite_default_instance_;

TestMessageMapLite* TestMessageMapLite::New(::google::protobuf::Arena* arena) const {
  return ::google::protobuf::Arena::CreateMessage<TestMessageMapLite>(arena);
}

void TestMessageMapLite::Clear() {
// @@protoc_insertion_point(message_clear_start:protobuf_unittest.TestMessageMapLite)
  map_int32_message_.Clear();
  _has_bits_.Clear();
  _unknown_fields_.ClearToEmpty(
      &::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      GetArenaNoVirtual());
}

bool TestMessageMapLite::MergePartialFromCodedStream(
    ::google::protobuf::io::CodedInputStream* input) {
#define DO_(EXPRESSION) if (!GOOGLE_PREDICT_TRUE(EXPRESSION)) goto failure
  ::google::protobuf::uint32 tag;
  ::google::protobuf::io::LazyStringOutputStream unknown_fields_string(
      ::google::protobuf::NewPermanentCallback(
          &MutableUnknownFieldsForTestMessageMapLite, this));
  ::google::protobuf::io::CodedOutputStream unknown_fields_stream(
      &unknown_fields_string, false);
  // @@protoc_insertion_point(parse_start:protobuf_unittest.TestMessageMapLite)
  for (;;) {
    ::std::pair< ::google::protobuf::uint32, bool> p = input->ReadTagWithCutoff(127);
    tag = p.first;
    if (!p.second) goto handle_unusual;
    switch (::google::protobuf::internal::WireFormatLite::GetTagFieldNumber(tag)) {
      // map<int32, .protobuf_unittest.TestAllTypesLite> map_int32_message = 1;
      case 1: {
        if (tag == 10) {
          DO_(input->IncrementRecursionDepth());
         parse_loop_map_int32_message:
          TestMessageMapLite_MapInt32MessageEntry::Parser< ::google::protobuf::internal::MapFieldLite<
              ::google::protobuf::int32, ::protobuf_unittest::TestAllTypesLite,
              ::google::protobuf::internal::WireFormatLite::TYPE_INT32,
              ::google::protobuf::internal::WireFormatLite::TYPE_MESSAGE,
              0 >,
            ::google::protobuf::Map< ::google::protobuf::int32, ::protobuf_unittest::TestAllTypesLite > > parser(&map_int32_message_);
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessageNoVirtual(
              input, &parser));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(10)) goto parse_loop_map_int32_message;
        input->UnsafeDecrementRecursionDepth();
        if (input->ExpectAtEnd()) goto success;
        break;
      }

      default: {
      handle_unusual:
        if (tag == 0 ||
            ::google::protobuf::internal::WireFormatLite::GetTagWireType(tag) ==
            ::google::protobuf::internal::WireFormatLite::WIRETYPE_END_GROUP) {
          goto success;
        }
        DO_(::google::protobuf::internal::WireFormatLite::SkipField(
            input, tag, &unknown_fields_stream));
        break;
      }
    }
  }
success:
  // @@protoc_insertion_point(parse_success:protobuf_unittest.TestMessageMapLite)
  return true;
failure:
  // @@protoc_insertion_point(parse_failure:protobuf_unittest.TestMessageMapLite)
  return false;
#undef DO_
}

void TestMessageMapLite::SerializeWithCachedSizes(
    ::google::protobuf::io::CodedOutputStream* output) const {
  // @@protoc_insertion_point(serialize_start:protobuf_unittest.TestMessageMapLite)
  // map<int32, .protobuf_unittest.TestAllTypesLite> map_int32_message = 1;
  if (!this->map_int32_message().empty()) {
    typedef ::google::protobuf::Map< ::google::protobuf::int32, ::protobuf_unittest::TestAllTypesLite >::const_pointer
        ConstPtr;
    typedef ::google::protobuf::internal::SortItem< ::google::protobuf::int32, ConstPtr > SortItem;
    typedef ::google::protobuf::internal::CompareByFirstField<SortItem> Less;

    if (output->IsSerializationDeterminstic() &&
        this->map_int32_message().size() > 1) {
      ::google::protobuf::scoped_array<SortItem> items(
          new SortItem[this->map_int32_message().size()]);
      typedef ::google::protobuf::Map< ::google::protobuf::int32, ::protobuf_unittest::TestAllTypesLite >::size_type size_type;
      size_type n = 0;
      for (::google::protobuf::Map< ::google::protobuf::int32, ::protobuf_unittest::TestAllTypesLite >::const_iterator
          it = this->map_int32_message().begin();
          it != this->map_int32_message().end(); ++it, ++n) {
        items[n] = SortItem(&*it);
      }
      ::std::sort(&items[0], &items[n], Less());
      ::google::protobuf::scoped_ptr<TestMessageMapLite_MapInt32MessageEntry> entry;
      for (size_type i = 0; i < n; i++) {
        entry.reset(map_int32_message_.NewEntryWrapper(
            items[i].second->first, items[i].second->second));
        ::google::protobuf::internal::WireFormatLite::WriteMessage(
            1, *entry, output);
        if (entry->GetArena() != NULL) {
          entry.release();
        }
      }
    } else {
      ::google::protobuf::scoped_ptr<TestMessageMapLite_MapInt32MessageEntry> entry;
      for (::google::protobuf::Map< ::google::protobuf::int32, ::protobuf_unittest::TestAllTypesLite >::const_iterator
          it = this->map_int32_message().begin();
          it != this->map_int32_message().end(); ++it) {
        entry.reset(map_int32_message_.NewEntryWrapper(
            it->first, it->second));
        ::google::protobuf::internal::WireFormatLite::WriteMessage(
            1, *entry, output);
        if (entry->GetArena() != NULL) {
          entry.release();
        }
      }
    }
  }

  output->WriteRaw(unknown_fields().data(),
                   static_cast<int>(unknown_fields().size()));
  // @@protoc_insertion_point(serialize_end:protobuf_unittest.TestMessageMapLite)
}

size_t TestMessageMapLite::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:protobuf_unittest.TestMessageMapLite)
  size_t total_size = 0;

  // map<int32, .protobuf_unittest.TestAllTypesLite> map_int32_message = 1;
  total_size += 1 *
      ::google::protobuf::internal::FromIntSize(this->map_int32_message_size());
  {
    ::google::protobuf::scoped_ptr<TestMessageMapLite_MapInt32MessageEntry> entry;
    for (::google::protobuf::Map< ::google::protobuf::int32, ::protobuf_unittest::TestAllTypesLite >::const_iterator
        it = this->map_int32_message().begin();
        it != this->map_int32_message().end(); ++it) {
      if (entry.get() != NULL && entry->GetArena() != NULL) {
        entry.release();
      }
      entry.reset(map_int32_message_.NewEntryWrapper(it->first, it->second));
      total_size += ::google::protobuf::internal::WireFormatLite::
          MessageSizeNoVirtual(*entry);
    }
    if (entry.get() != NULL && entry->GetArena() != NULL) {
      entry.release();
    }
  }

  total_size += unknown_fields().size();

  int cached_size = ::google::protobuf::internal::ToCachedSize(total_size);
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = cached_size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
  return total_size;
}

void TestMessageMapLite::CheckTypeAndMergeFrom(
    const ::google::protobuf::MessageLite& from) {
  MergeFrom(*::google::protobuf::down_cast<const TestMessageMapLite*>(&from));
}

void TestMessageMapLite::MergeFrom(const TestMessageMapLite& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:protobuf_unittest.TestMessageMapLite)
  if (GOOGLE_PREDICT_TRUE(&from != this)) {
    UnsafeMergeFrom(from);
  } else {
    MergeFromFail(__LINE__);
  }
}

void TestMessageMapLite::UnsafeMergeFrom(const TestMessageMapLite& from) {
  GOOGLE_DCHECK(&from != this);
  map_int32_message_.MergeFrom(from.map_int32_message_);
  if (!from.unknown_fields().empty()) {
    mutable_unknown_fields()->append(from.unknown_fields());
  }
}

void TestMessageMapLite::CopyFrom(const TestMessageMapLite& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:protobuf_unittest.TestMessageMapLite)
  if (&from == this) return;
  Clear();
  UnsafeMergeFrom(from);
}

bool TestMessageMapLite::IsInitialized() const {

  return true;
}

void TestMessageMapLite::Swap(TestMessageMapLite* other) {
  if (other == this) return;
  if (GetArenaNoVirtual() == other->GetArenaNoVirtual()) {
    InternalSwap(other);
  } else {
    TestMessageMapLite temp;
    temp.UnsafeMergeFrom(*this);
    CopyFrom(*other);
    other->CopyFrom(temp);
  }
}
void TestMessageMapLite::UnsafeArenaSwap(TestMessageMapLite* other) {
  if (other == this) return;
  GOOGLE_DCHECK(GetArenaNoVirtual() == other->GetArenaNoVirtual());
  InternalSwap(other);
}
void TestMessageMapLite::InternalSwap(TestMessageMapLite* other) {
  map_int32_message_.Swap(&other->map_int32_message_);
  std::swap(_has_bits_[0], other->_has_bits_[0]);
  _unknown_fields_.Swap(&other->_unknown_fields_);
  std::swap(_cached_size_, other->_cached_size_);
}

::std::string TestMessageMapLite::GetTypeName() const {
  return "protobuf_unittest.TestMessageMapLite";
}

#if PROTOBUF_INLINE_NOT_IN_HEADERS
// TestMessageMapLite

// map<int32, .protobuf_unittest.TestAllTypesLite> map_int32_message = 1;
int TestMessageMapLite::map_int32_message_size() const {
  return map_int32_message_.size();
}
void TestMessageMapLite::clear_map_int32_message() {
  map_int32_message_.Clear();
}
 const ::google::protobuf::Map< ::google::protobuf::int32, ::protobuf_unittest::TestAllTypesLite >&
TestMessageMapLite::map_int32_message() const {
  // @@protoc_insertion_point(field_map:protobuf_unittest.TestMessageMapLite.map_int32_message)
  return map_int32_message_.GetMap();
}
 ::google::protobuf::Map< ::google::protobuf::int32, ::protobuf_unittest::TestAllTypesLite >*
TestMessageMapLite::mutable_map_int32_message() {
  // @@protoc_insertion_point(field_mutable_map:protobuf_unittest.TestMessageMapLite.map_int32_message)
  return map_int32_message_.MutableMap();
}

inline const TestMessageMapLite* TestMessageMapLite::internal_default_instance() {
  return &TestMessageMapLite_default_instance_.get();
}
#endif  // PROTOBUF_INLINE_NOT_IN_HEADERS

// ===================================================================

static ::std::string* MutableUnknownFieldsForTestRequiredLite(
    TestRequiredLite* ptr) {
  return ptr->mutable_unknown_fields();
}

#if !defined(_MSC_VER) || _MSC_VER >= 1900
const int TestRequiredLite::kAFieldNumber;
const int TestRequiredLite::kBFieldNumber;
const int TestRequiredLite::kCFieldNumber;
#endif  // !defined(_MSC_VER) || _MSC_VER >= 1900

TestRequiredLite::TestRequiredLite()
  : ::google::protobuf::MessageLite(), _arena_ptr_(NULL) {
  if (this != internal_default_instance()) protobuf_InitDefaults_google_2fprotobuf_2fmap_5flite_5funittest_2eproto();
  SharedCtor();
  // @@protoc_insertion_point(constructor:protobuf_unittest.TestRequiredLite)
}
TestRequiredLite::TestRequiredLite(::google::protobuf::Arena* arena)
  : ::google::protobuf::MessageLite(),
  _arena_ptr_(arena) {
#ifdef GOOGLE_PROTOBUF_NO_STATIC_INITIALIZER
  protobuf_InitDefaults_google_2fprotobuf_2fmap_5flite_5funittest_2eproto();
#endif  // GOOGLE_PROTOBUF_NO_STATIC_INITIALIZER
  SharedCtor();
  RegisterArenaDtor(arena);
  // @@protoc_insertion_point(arena_constructor:protobuf_unittest.TestRequiredLite)
}

void TestRequiredLite::InitAsDefaultInstance() {
}

TestRequiredLite::TestRequiredLite(const TestRequiredLite& from)
  : ::google::protobuf::MessageLite(),
    _arena_ptr_(NULL) {
  SharedCtor();
  UnsafeMergeFrom(from);
  // @@protoc_insertion_point(copy_constructor:protobuf_unittest.TestRequiredLite)
}

void TestRequiredLite::SharedCtor() {
  _cached_size_ = 0;
  _unknown_fields_.UnsafeSetDefault(
      &::google::protobuf::internal::GetEmptyStringAlreadyInited());
  ::memset(&a_, 0, reinterpret_cast<char*>(&c_) -
    reinterpret_cast<char*>(&a_) + sizeof(c_));
}

TestRequiredLite::~TestRequiredLite() {
  // @@protoc_insertion_point(destructor:protobuf_unittest.TestRequiredLite)
  SharedDtor();
}

void TestRequiredLite::SharedDtor() {
  ::google::protobuf::Arena* arena = GetArenaNoVirtual();
  if (arena != NULL) {
    return;
  }

  _unknown_fields_.Destroy(
      &::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      arena);
}

void TestRequiredLite::ArenaDtor(void* object) {
  TestRequiredLite* _this = reinterpret_cast< TestRequiredLite* >(object);
  (void)_this;
}
void TestRequiredLite::RegisterArenaDtor(::google::protobuf::Arena* arena) {
}
void TestRequiredLite::SetCachedSize(int size) const {
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
}
const TestRequiredLite& TestRequiredLite::default_instance() {
  protobuf_InitDefaults_google_2fprotobuf_2fmap_5flite_5funittest_2eproto();
  return *internal_default_instance();
}

::google::protobuf::internal::ExplicitlyConstructed<TestRequiredLite> TestRequiredLite_default_instance_;

TestRequiredLite* TestRequiredLite::New(::google::protobuf::Arena* arena) const {
  return ::google::protobuf::Arena::CreateMessage<TestRequiredLite>(arena);
}

void TestRequiredLite::Clear() {
// @@protoc_insertion_point(message_clear_start:protobuf_unittest.TestRequiredLite)
#if defined(__clang__)
#define ZR_HELPER_(f) \
  _Pragma("clang diagnostic push") \
  _Pragma("clang diagnostic ignored \"-Winvalid-offsetof\"") \
  __builtin_offsetof(TestRequiredLite, f) \
  _Pragma("clang diagnostic pop")
#else
#define ZR_HELPER_(f) reinterpret_cast<char*>(\
  &reinterpret_cast<TestRequiredLite*>(16)->f)
#endif

#define ZR_(first, last) do {\
  ::memset(&(first), 0,\
           ZR_HELPER_(last) - ZR_HELPER_(first) + sizeof(last));\
} while (0)

  ZR_(a_, c_);

#undef ZR_HELPER_
#undef ZR_

  _has_bits_.Clear();
  _unknown_fields_.ClearToEmpty(
      &::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      GetArenaNoVirtual());
}

bool TestRequiredLite::MergePartialFromCodedStream(
    ::google::protobuf::io::CodedInputStream* input) {
#define DO_(EXPRESSION) if (!GOOGLE_PREDICT_TRUE(EXPRESSION)) goto failure
  ::google::protobuf::uint32 tag;
  ::google::protobuf::io::LazyStringOutputStream unknown_fields_string(
      ::google::protobuf::NewPermanentCallback(
          &MutableUnknownFieldsForTestRequiredLite, this));
  ::google::protobuf::io::CodedOutputStream unknown_fields_stream(
      &unknown_fields_string, false);
  // @@protoc_insertion_point(parse_start:protobuf_unittest.TestRequiredLite)
  for (;;) {
    ::std::pair< ::google::protobuf::uint32, bool> p = input->ReadTagWithCutoff(127);
    tag = p.first;
    if (!p.second) goto handle_unusual;
    switch (::google::protobuf::internal::WireFormatLite::GetTagFieldNumber(tag)) {
      // required int32 a = 1;
      case 1: {
        if (tag == 8) {
          set_has_a();
          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int32, ::google::protobuf::internal::WireFormatLite::TYPE_INT32>(
                 input, &a_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(16)) goto parse_b;
        break;
      }

      // required int32 b = 2;
      case 2: {
        if (tag == 16) {
         parse_b:
          set_has_b();
          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int32, ::google::protobuf::internal::WireFormatLite::TYPE_INT32>(
                 input, &b_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(24)) goto parse_c;
        break;
      }

      // required int32 c = 3;
      case 3: {
        if (tag == 24) {
         parse_c:
          set_has_c();
          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int32, ::google::protobuf::internal::WireFormatLite::TYPE_INT32>(
                 input, &c_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectAtEnd()) goto success;
        break;
      }

      default: {
      handle_unusual:
        if (tag == 0 ||
            ::google::protobuf::internal::WireFormatLite::GetTagWireType(tag) ==
            ::google::protobuf::internal::WireFormatLite::WIRETYPE_END_GROUP) {
          goto success;
        }
        DO_(::google::protobuf::internal::WireFormatLite::SkipField(
            input, tag, &unknown_fields_stream));
        break;
      }
    }
  }
success:
  // @@protoc_insertion_point(parse_success:protobuf_unittest.TestRequiredLite)
  return true;
failure:
  // @@protoc_insertion_point(parse_failure:protobuf_unittest.TestRequiredLite)
  return false;
#undef DO_
}

void TestRequiredLite::SerializeWithCachedSizes(
    ::google::protobuf::io::CodedOutputStream* output) const {
  // @@protoc_insertion_point(serialize_start:protobuf_unittest.TestRequiredLite)
  // required int32 a = 1;
  if (has_a()) {
    ::google::protobuf::internal::WireFormatLite::WriteInt32(1, this->a(), output);
  }

  // required int32 b = 2;
  if (has_b()) {
    ::google::protobuf::internal::WireFormatLite::WriteInt32(2, this->b(), output);
  }

  // required int32 c = 3;
  if (has_c()) {
    ::google::protobuf::internal::WireFormatLite::WriteInt32(3, this->c(), output);
  }

  output->WriteRaw(unknown_fields().data(),
                   static_cast<int>(unknown_fields().size()));
  // @@protoc_insertion_point(serialize_end:protobuf_unittest.TestRequiredLite)
}

size_t TestRequiredLite::RequiredFieldsByteSizeFallback() const {
// @@protoc_insertion_point(required_fields_byte_size_fallback_start:protobuf_unittest.TestRequiredLite)
  size_t total_size = 0;

  if (has_a()) {
    // required int32 a = 1;
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::Int32Size(
        this->a());
  }

  if (has_b()) {
    // required int32 b = 2;
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::Int32Size(
        this->b());
  }

  if (has_c()) {
    // required int32 c = 3;
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::Int32Size(
        this->c());
  }

  return total_size;
}
size_t TestRequiredLite::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:protobuf_unittest.TestRequiredLite)
  size_t total_size = 0;

  if (((_has_bits_[0] & 0x00000007) ^ 0x00000007) == 0) {  // All required fields are present.
    // required int32 a = 1;
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::Int32Size(
        this->a());

    // required int32 b = 2;
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::Int32Size(
        this->b());

    // required int32 c = 3;
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::Int32Size(
        this->c());

  } else {
    total_size += RequiredFieldsByteSizeFallback();
  }
  total_size += unknown_fields().size();

  int cached_size = ::google::protobuf::internal::ToCachedSize(total_size);
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = cached_size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
  return total_size;
}

void TestRequiredLite::CheckTypeAndMergeFrom(
    const ::google::protobuf::MessageLite& from) {
  MergeFrom(*::google::protobuf::down_cast<const TestRequiredLite*>(&from));
}

void TestRequiredLite::MergeFrom(const TestRequiredLite& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:protobuf_unittest.TestRequiredLite)
  if (GOOGLE_PREDICT_TRUE(&from != this)) {
    UnsafeMergeFrom(from);
  } else {
    MergeFromFail(__LINE__);
  }
}

void TestRequiredLite::UnsafeMergeFrom(const TestRequiredLite& from) {
  GOOGLE_DCHECK(&from != this);
  if (from._has_bits_[0 / 32] & (0xffu << (0 % 32))) {
    if (from.has_a()) {
      set_a(from.a());
    }
    if (from.has_b()) {
      set_b(from.b());
    }
    if (from.has_c()) {
      set_c(from.c());
    }
  }
  if (!from.unknown_fields().empty()) {
    mutable_unknown_fields()->append(from.unknown_fields());
  }
}

void TestRequiredLite::CopyFrom(const TestRequiredLite& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:protobuf_unittest.TestRequiredLite)
  if (&from == this) return;
  Clear();
  UnsafeMergeFrom(from);
}

bool TestRequiredLite::IsInitialized() const {
  if ((_has_bits_[0] & 0x00000007) != 0x00000007) return false;

  return true;
}

void TestRequiredLite::Swap(TestRequiredLite* other) {
  if (other == this) return;
  if (GetArenaNoVirtual() == other->GetArenaNoVirtual()) {
    InternalSwap(other);
  } else {
    TestRequiredLite temp;
    temp.UnsafeMergeFrom(*this);
    CopyFrom(*other);
    other->CopyFrom(temp);
  }
}
void TestRequiredLite::UnsafeArenaSwap(TestRequiredLite* other) {
  if (other == this) return;
  GOOGLE_DCHECK(GetArenaNoVirtual() == other->GetArenaNoVirtual());
  InternalSwap(other);
}
void TestRequiredLite::InternalSwap(TestRequiredLite* other) {
  std::swap(a_, other->a_);
  std::swap(b_, other->b_);
  std::swap(c_, other->c_);
  std::swap(_has_bits_[0], other->_has_bits_[0]);
  _unknown_fields_.Swap(&other->_unknown_fields_);
  std::swap(_cached_size_, other->_cached_size_);
}

::std::string TestRequiredLite::GetTypeName() const {
  return "protobuf_unittest.TestRequiredLite";
}

#if PROTOBUF_INLINE_NOT_IN_HEADERS
// TestRequiredLite

// required int32 a = 1;
bool TestRequiredLite::has_a() const {
  return (_has_bits_[0] & 0x00000001u) != 0;
}
void TestRequiredLite::set_has_a() {
  _has_bits_[0] |= 0x00000001u;
}
void TestRequiredLite::clear_has_a() {
  _has_bits_[0] &= ~0x00000001u;
}
void TestRequiredLite::clear_a() {
  a_ = 0;
  clear_has_a();
}
::google::protobuf::int32 TestRequiredLite::a() const {
  // @@protoc_insertion_point(field_get:protobuf_unittest.TestRequiredLite.a)
  return a_;
}
void TestRequiredLite::set_a(::google::protobuf::int32 value) {
  set_has_a();
  a_ = value;
  // @@protoc_insertion_point(field_set:protobuf_unittest.TestRequiredLite.a)
}

// required int32 b = 2;
bool TestRequiredLite::has_b() const {
  return (_has_bits_[0] & 0x00000002u) != 0;
}
void TestRequiredLite::set_has_b() {
  _has_bits_[0] |= 0x00000002u;
}
void TestRequiredLite::clear_has_b() {
  _has_bits_[0] &= ~0x00000002u;
}
void TestRequiredLite::clear_b() {
  b_ = 0;
  clear_has_b();
}
::google::protobuf::int32 TestRequiredLite::b() const {
  // @@protoc_insertion_point(field_get:protobuf_unittest.TestRequiredLite.b)
  return b_;
}
void TestRequiredLite::set_b(::google::protobuf::int32 value) {
  set_has_b();
  b_ = value;
  // @@protoc_insertion_point(field_set:protobuf_unittest.TestRequiredLite.b)
}

// required int32 c = 3;
bool TestRequiredLite::has_c() const {
  return (_has_bits_[0] & 0x00000004u) != 0;
}
void TestRequiredLite::set_has_c() {
  _has_bits_[0] |= 0x00000004u;
}
void TestRequiredLite::clear_has_c() {
  _has_bits_[0] &= ~0x00000004u;
}
void TestRequiredLite::clear_c() {
  c_ = 0;
  clear_has_c();
}
::google::protobuf::int32 TestRequiredLite::c() const {
  // @@protoc_insertion_point(field_get:protobuf_unittest.TestRequiredLite.c)
  return c_;
}
void TestRequiredLite::set_c(::google::protobuf::int32 value) {
  set_has_c();
  c_ = value;
  // @@protoc_insertion_point(field_set:protobuf_unittest.TestRequiredLite.c)
}

inline const TestRequiredLite* TestRequiredLite::internal_default_instance() {
  return &TestRequiredLite_default_instance_.get();
}
#endif  // PROTOBUF_INLINE_NOT_IN_HEADERS

// ===================================================================

static ::std::string* MutableUnknownFieldsForForeignMessageArenaLite(
    ForeignMessageArenaLite* ptr) {
  return ptr->mutable_unknown_fields();
}

#if !defined(_MSC_VER) || _MSC_VER >= 1900
const int ForeignMessageArenaLite::kCFieldNumber;
#endif  // !defined(_MSC_VER) || _MSC_VER >= 1900

ForeignMessageArenaLite::ForeignMessageArenaLite()
  : ::google::protobuf::MessageLite(), _arena_ptr_(NULL) {
  if (this != internal_default_instance()) protobuf_InitDefaults_google_2fprotobuf_2fmap_5flite_5funittest_2eproto();
  SharedCtor();
  // @@protoc_insertion_point(constructor:protobuf_unittest.ForeignMessageArenaLite)
}
ForeignMessageArenaLite::ForeignMessageArenaLite(::google::protobuf::Arena* arena)
  : ::google::protobuf::MessageLite(),
  _arena_ptr_(arena) {
#ifdef GOOGLE_PROTOBUF_NO_STATIC_INITIALIZER
  protobuf_InitDefaults_google_2fprotobuf_2fmap_5flite_5funittest_2eproto();
#endif  // GOOGLE_PROTOBUF_NO_STATIC_INITIALIZER
  SharedCtor();
  RegisterArenaDtor(arena);
  // @@protoc_insertion_point(arena_constructor:protobuf_unittest.ForeignMessageArenaLite)
}

void ForeignMessageArenaLite::InitAsDefaultInstance() {
}

ForeignMessageArenaLite::ForeignMessageArenaLite(const ForeignMessageArenaLite& from)
  : ::google::protobuf::MessageLite(),
    _arena_ptr_(NULL) {
  SharedCtor();
  UnsafeMergeFrom(from);
  // @@protoc_insertion_point(copy_constructor:protobuf_unittest.ForeignMessageArenaLite)
}

void ForeignMessageArenaLite::SharedCtor() {
  _cached_size_ = 0;
  _unknown_fields_.UnsafeSetDefault(
      &::google::protobuf::internal::GetEmptyStringAlreadyInited());
  c_ = 0;
}

ForeignMessageArenaLite::~ForeignMessageArenaLite() {
  // @@protoc_insertion_point(destructor:protobuf_unittest.ForeignMessageArenaLite)
  SharedDtor();
}

void ForeignMessageArenaLite::SharedDtor() {
  ::google::protobuf::Arena* arena = GetArenaNoVirtual();
  if (arena != NULL) {
    return;
  }

  _unknown_fields_.Destroy(
      &::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      arena);
}

void ForeignMessageArenaLite::ArenaDtor(void* object) {
  ForeignMessageArenaLite* _this = reinterpret_cast< ForeignMessageArenaLite* >(object);
  (void)_this;
}
void ForeignMessageArenaLite::RegisterArenaDtor(::google::protobuf::Arena* arena) {
}
void ForeignMessageArenaLite::SetCachedSize(int size) const {
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
}
const ForeignMessageArenaLite& ForeignMessageArenaLite::default_instance() {
  protobuf_InitDefaults_google_2fprotobuf_2fmap_5flite_5funittest_2eproto();
  return *internal_default_instance();
}

::google::protobuf::internal::ExplicitlyConstructed<ForeignMessageArenaLite> ForeignMessageArenaLite_default_instance_;

ForeignMessageArenaLite* ForeignMessageArenaLite::New(::google::protobuf::Arena* arena) const {
  return ::google::protobuf::Arena::CreateMessage<ForeignMessageArenaLite>(arena);
}

void ForeignMessageArenaLite::Clear() {
// @@protoc_insertion_point(message_clear_start:protobuf_unittest.ForeignMessageArenaLite)
  c_ = 0;
  _has_bits_.Clear();
  _unknown_fields_.ClearToEmpty(
      &::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      GetArenaNoVirtual());
}

bool ForeignMessageArenaLite::MergePartialFromCodedStream(
    ::google::protobuf::io::CodedInputStream* input) {
#define DO_(EXPRESSION) if (!GOOGLE_PREDICT_TRUE(EXPRESSION)) goto failure
  ::google::protobuf::uint32 tag;
  ::google::protobuf::io::LazyStringOutputStream unknown_fields_string(
      ::google::protobuf::NewPermanentCallback(
          &MutableUnknownFieldsForForeignMessageArenaLite, this));
  ::google::protobuf::io::CodedOutputStream unknown_fields_stream(
      &unknown_fields_string, false);
  // @@protoc_insertion_point(parse_start:protobuf_unittest.ForeignMessageArenaLite)
  for (;;) {
    ::std::pair< ::google::protobuf::uint32, bool> p = input->ReadTagWithCutoff(127);
    tag = p.first;
    if (!p.second) goto handle_unusual;
    switch (::google::protobuf::internal::WireFormatLite::GetTagFieldNumber(tag)) {
      // optional int32 c = 1;
      case 1: {
        if (tag == 8) {
          set_has_c();
          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int32, ::google::protobuf::internal::WireFormatLite::TYPE_INT32>(
                 input, &c_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectAtEnd()) goto success;
        break;
      }

      default: {
      handle_unusual:
        if (tag == 0 ||
            ::google::protobuf::internal::WireFormatLite::GetTagWireType(tag) ==
            ::google::protobuf::internal::WireFormatLite::WIRETYPE_END_GROUP) {
          goto success;
        }
        DO_(::google::protobuf::internal::WireFormatLite::SkipField(
            input, tag, &unknown_fields_stream));
        break;
      }
    }
  }
success:
  // @@protoc_insertion_point(parse_success:protobuf_unittest.ForeignMessageArenaLite)
  return true;
failure:
  // @@protoc_insertion_point(parse_failure:protobuf_unittest.ForeignMessageArenaLite)
  return false;
#undef DO_
}

void ForeignMessageArenaLite::SerializeWithCachedSizes(
    ::google::protobuf::io::CodedOutputStream* output) const {
  // @@protoc_insertion_point(serialize_start:protobuf_unittest.ForeignMessageArenaLite)
  // optional int32 c = 1;
  if (has_c()) {
    ::google::protobuf::internal::WireFormatLite::WriteInt32(1, this->c(), output);
  }

  output->WriteRaw(unknown_fields().data(),
                   static_cast<int>(unknown_fields().size()));
  // @@protoc_insertion_point(serialize_end:protobuf_unittest.ForeignMessageArenaLite)
}

size_t ForeignMessageArenaLite::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:protobuf_unittest.ForeignMessageArenaLite)
  size_t total_size = 0;

  // optional int32 c = 1;
  if (has_c()) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::Int32Size(
        this->c());
  }

  total_size += unknown_fields().size();

  int cached_size = ::google::protobuf::internal::ToCachedSize(total_size);
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = cached_size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
  return total_size;
}

void ForeignMessageArenaLite::CheckTypeAndMergeFrom(
    const ::google::protobuf::MessageLite& from) {
  MergeFrom(*::google::protobuf::down_cast<const ForeignMessageArenaLite*>(&from));
}

void ForeignMessageArenaLite::MergeFrom(const ForeignMessageArenaLite& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:protobuf_unittest.ForeignMessageArenaLite)
  if (GOOGLE_PREDICT_TRUE(&from != this)) {
    UnsafeMergeFrom(from);
  } else {
    MergeFromFail(__LINE__);
  }
}

void ForeignMessageArenaLite::UnsafeMergeFrom(const ForeignMessageArenaLite& from) {
  GOOGLE_DCHECK(&from != this);
  if (from._has_bits_[0 / 32] & (0xffu << (0 % 32))) {
    if (from.has_c()) {
      set_c(from.c());
    }
  }
  if (!from.unknown_fields().empty()) {
    mutable_unknown_fields()->append(from.unknown_fields());
  }
}

void ForeignMessageArenaLite::CopyFrom(const ForeignMessageArenaLite& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:protobuf_unittest.ForeignMessageArenaLite)
  if (&from == this) return;
  Clear();
  UnsafeMergeFrom(from);
}

bool ForeignMessageArenaLite::IsInitialized() const {

  return true;
}

void ForeignMessageArenaLite::Swap(ForeignMessageArenaLite* other) {
  if (other == this) return;
  if (GetArenaNoVirtual() == other->GetArenaNoVirtual()) {
    InternalSwap(other);
  } else {
    ForeignMessageArenaLite temp;
    temp.UnsafeMergeFrom(*this);
    CopyFrom(*other);
    other->CopyFrom(temp);
  }
}
void ForeignMessageArenaLite::UnsafeArenaSwap(ForeignMessageArenaLite* other) {
  if (other == this) return;
  GOOGLE_DCHECK(GetArenaNoVirtual() == other->GetArenaNoVirtual());
  InternalSwap(other);
}
void ForeignMessageArenaLite::InternalSwap(ForeignMessageArenaLite* other) {
  std::swap(c_, other->c_);
  std::swap(_has_bits_[0], other->_has_bits_[0]);
  _unknown_fields_.Swap(&other->_unknown_fields_);
  std::swap(_cached_size_, other->_cached_size_);
}

::std::string ForeignMessageArenaLite::GetTypeName() const {
  return "protobuf_unittest.ForeignMessageArenaLite";
}

#if PROTOBUF_INLINE_NOT_IN_HEADERS
// ForeignMessageArenaLite

// optional int32 c = 1;
bool ForeignMessageArenaLite::has_c() const {
  return (_has_bits_[0] & 0x00000001u) != 0;
}
void ForeignMessageArenaLite::set_has_c() {
  _has_bits_[0] |= 0x00000001u;
}
void ForeignMessageArenaLite::clear_has_c() {
  _has_bits_[0] &= ~0x00000001u;
}
void ForeignMessageArenaLite::clear_c() {
  c_ = 0;
  clear_has_c();
}
::google::protobuf::int32 ForeignMessageArenaLite::c() const {
  // @@protoc_insertion_point(field_get:protobuf_unittest.ForeignMessageArenaLite.c)
  return c_;
}
void ForeignMessageArenaLite::set_c(::google::protobuf::int32 value) {
  set_has_c();
  c_ = value;
  // @@protoc_insertion_point(field_set:protobuf_unittest.ForeignMessageArenaLite.c)
}

inline const ForeignMessageArenaLite* ForeignMessageArenaLite::internal_default_instance() {
  return &ForeignMessageArenaLite_default_instance_.get();
}
#endif  // PROTOBUF_INLINE_NOT_IN_HEADERS

// @@protoc_insertion_point(namespace_scope)

}  // namespace protobuf_unittest

// @@protoc_insertion_point(global_scope)

// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: google/protobuf/map_proto2_unittest.proto

#define INTERNAL_SUPPRESS_PROTOBUF_FIELD_DEPRECATION
#include <google/protobuf/map_proto2_unittest.pb.h>

#include <algorithm>

#include <google/protobuf/stubs/common.h>
#include <google/protobuf/stubs/port.h>
#include <google/protobuf/stubs/once.h>
#include <google/protobuf/io/coded_stream.h>
#include <google/protobuf/wire_format_lite_inl.h>
#include <google/protobuf/descriptor.h>
#include <google/protobuf/generated_message_reflection.h>
#include <google/protobuf/reflection_ops.h>
#include <google/protobuf/wire_format.h>
// @@protoc_insertion_point(includes)

namespace protobuf_unittest {

namespace {

const ::google::protobuf::Descriptor* TestEnumMap_descriptor_ = NULL;
const ::google::protobuf::internal::GeneratedMessageReflection*
  TestEnumMap_reflection_ = NULL;
const ::google::protobuf::Descriptor* TestEnumMap_KnownMapFieldEntry_descriptor_ = NULL;
const ::google::protobuf::Descriptor* TestEnumMap_UnknownMapFieldEntry_descriptor_ = NULL;
const ::google::protobuf::Descriptor* TestEnumMapPlusExtra_descriptor_ = NULL;
const ::google::protobuf::internal::GeneratedMessageReflection*
  TestEnumMapPlusExtra_reflection_ = NULL;
const ::google::protobuf::Descriptor* TestEnumMapPlusExtra_KnownMapFieldEntry_descriptor_ = NULL;
const ::google::protobuf::Descriptor* TestEnumMapPlusExtra_UnknownMapFieldEntry_descriptor_ = NULL;
const ::google::protobuf::Descriptor* TestImportEnumMap_descriptor_ = NULL;
const ::google::protobuf::internal::GeneratedMessageReflection*
  TestImportEnumMap_reflection_ = NULL;
const ::google::protobuf::Descriptor* TestImportEnumMap_ImportEnumAmpEntry_descriptor_ = NULL;
const ::google::protobuf::Descriptor* TestIntIntMap_descriptor_ = NULL;
const ::google::protobuf::internal::GeneratedMessageReflection*
  TestIntIntMap_reflection_ = NULL;
const ::google::protobuf::Descriptor* TestIntIntMap_MEntry_descriptor_ = NULL;
const ::google::protobuf::Descriptor* TestMaps_descriptor_ = NULL;
const ::google::protobuf::internal::GeneratedMessageReflection*
  TestMaps_reflection_ = NULL;
const ::google::protobuf::Descriptor* TestMaps_MInt32Entry_descriptor_ = NULL;
const ::google::protobuf::Descriptor* TestMaps_MInt64Entry_descriptor_ = NULL;
const ::google::protobuf::Descriptor* TestMaps_MUint32Entry_descriptor_ = NULL;
const ::google::protobuf::Descriptor* TestMaps_MUint64Entry_descriptor_ = NULL;
const ::google::protobuf::Descriptor* TestMaps_MSint32Entry_descriptor_ = NULL;
const ::google::protobuf::Descriptor* TestMaps_MSint64Entry_descriptor_ = NULL;
const ::google::protobuf::Descriptor* TestMaps_MFixed32Entry_descriptor_ = NULL;
const ::google::protobuf::Descriptor* TestMaps_MFixed64Entry_descriptor_ = NULL;
const ::google::protobuf::Descriptor* TestMaps_MSfixed32Entry_descriptor_ = NULL;
const ::google::protobuf::Descriptor* TestMaps_MSfixed64Entry_descriptor_ = NULL;
const ::google::protobuf::Descriptor* TestMaps_MBoolEntry_descriptor_ = NULL;
const ::google::protobuf::Descriptor* TestMaps_MStringEntry_descriptor_ = NULL;
const ::google::protobuf::EnumDescriptor* Proto2MapEnum_descriptor_ = NULL;
const ::google::protobuf::EnumDescriptor* Proto2MapEnumPlusExtra_descriptor_ = NULL;

}  // namespace


void protobuf_AssignDesc_google_2fprotobuf_2fmap_5fproto2_5funittest_2eproto() GOOGLE_ATTRIBUTE_COLD;
void protobuf_AssignDesc_google_2fprotobuf_2fmap_5fproto2_5funittest_2eproto() {
  protobuf_AddDesc_google_2fprotobuf_2fmap_5fproto2_5funittest_2eproto();
  const ::google::protobuf::FileDescriptor* file =
    ::google::protobuf::DescriptorPool::generated_pool()->FindFileByName(
      "google/protobuf/map_proto2_unittest.proto");
  GOOGLE_CHECK(file != NULL);
  TestEnumMap_descriptor_ = file->message_type(0);
  static const int TestEnumMap_offsets_[2] = {
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(TestEnumMap, known_map_field_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(TestEnumMap, unknown_map_field_),
  };
  TestEnumMap_reflection_ =
    ::google::protobuf::internal::GeneratedMessageReflection::NewGeneratedMessageReflection(
      TestEnumMap_descriptor_,
      TestEnumMap::internal_default_instance(),
      TestEnumMap_offsets_,
      GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(TestEnumMap, _has_bits_),
      -1,
      -1,
      sizeof(TestEnumMap),
      GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(TestEnumMap, _internal_metadata_));
  TestEnumMap_KnownMapFieldEntry_descriptor_ = TestEnumMap_descriptor_->nested_type(0);
  TestEnumMap_UnknownMapFieldEntry_descriptor_ = TestEnumMap_descriptor_->nested_type(1);
  TestEnumMapPlusExtra_descriptor_ = file->message_type(1);
  static const int TestEnumMapPlusExtra_offsets_[2] = {
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(TestEnumMapPlusExtra, known_map_field_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(TestEnumMapPlusExtra, unknown_map_field_),
  };
  TestEnumMapPlusExtra_reflection_ =
    ::google::protobuf::internal::GeneratedMessageReflection::NewGeneratedMessageReflection(
      TestEnumMapPlusExtra_descriptor_,
      TestEnumMapPlusExtra::internal_default_instance(),
      TestEnumMapPlusExtra_offsets_,
      GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(TestEnumMapPlusExtra, _has_bits_),
      -1,
      -1,
      sizeof(TestEnumMapPlusExtra),
      GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(TestEnumMapPlusExtra, _internal_metadata_));
  TestEnumMapPlusExtra_KnownMapFieldEntry_descriptor_ = TestEnumMapPlusExtra_descriptor_->nested_type(0);
  TestEnumMapPlusExtra_UnknownMapFieldEntry_descriptor_ = TestEnumMapPlusExtra_descriptor_->nested_type(1);
  TestImportEnumMap_descriptor_ = file->message_type(2);
  static const int TestImportEnumMap_offsets_[1] = {
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(TestImportEnumMap, import_enum_amp_),
  };
  TestImportEnumMap_reflection_ =
    ::google::protobuf::internal::GeneratedMessageReflection::NewGeneratedMessageReflection(
      TestImportEnumMap_descriptor_,
      TestImportEnumMap::internal_default_instance(),
      TestImportEnumMap_offsets_,
      GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(TestImportEnumMap, _has_bits_),
      -1,
      -1,
      sizeof(TestImportEnumMap),
      GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(TestImportEnumMap, _internal_metadata_));
  TestImportEnumMap_ImportEnumAmpEntry_descriptor_ = TestImportEnumMap_descriptor_->nested_type(0);
  TestIntIntMap_descriptor_ = file->message_type(3);
  static const int TestIntIntMap_offsets_[1] = {
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(TestIntIntMap, m_),
  };
  TestIntIntMap_reflection_ =
    ::google::protobuf::internal::GeneratedMessageReflection::NewGeneratedMessageReflection(
      TestIntIntMap_descriptor_,
      TestIntIntMap::internal_default_instance(),
      TestIntIntMap_offsets_,
      GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(TestIntIntMap, _has_bits_),
      -1,
      -1,
      sizeof(TestIntIntMap),
      GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(TestIntIntMap, _internal_metadata_));
  TestIntIntMap_MEntry_descriptor_ = TestIntIntMap_descriptor_->nested_type(0);
  TestMaps_descriptor_ = file->message_type(4);
  static const int TestMaps_offsets_[12] = {
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(TestMaps, m_int32_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(TestMaps, m_int64_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(TestMaps, m_uint32_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(TestMaps, m_uint64_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(TestMaps, m_sint32_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(TestMaps, m_sint64_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(TestMaps, m_fixed32_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(TestMaps, m_fixed64_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(TestMaps, m_sfixed32_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(TestMaps, m_sfixed64_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(TestMaps, m_bool_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(TestMaps, m_string_),
  };
  TestMaps_reflection_ =
    ::google::protobuf::internal::GeneratedMessageReflection::NewGeneratedMessageReflection(
      TestMaps_descriptor_,
      TestMaps::internal_default_instance(),
      TestMaps_offsets_,
      GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(TestMaps, _has_bits_),
      -1,
      -1,
      sizeof(TestMaps),
      GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(TestMaps, _internal_metadata_));
  TestMaps_MInt32Entry_descriptor_ = TestMaps_descriptor_->nested_type(0);
  TestMaps_MInt64Entry_descriptor_ = TestMaps_descriptor_->nested_type(1);
  TestMaps_MUint32Entry_descriptor_ = TestMaps_descriptor_->nested_type(2);
  TestMaps_MUint64Entry_descriptor_ = TestMaps_descriptor_->nested_type(3);
  TestMaps_MSint32Entry_descriptor_ = TestMaps_descriptor_->nested_type(4);
  TestMaps_MSint64Entry_descriptor_ = TestMaps_descriptor_->nested_type(5);
  TestMaps_MFixed32Entry_descriptor_ = TestMaps_descriptor_->nested_type(6);
  TestMaps_MFixed64Entry_descriptor_ = TestMaps_descriptor_->nested_type(7);
  TestMaps_MSfixed32Entry_descriptor_ = TestMaps_descriptor_->nested_type(8);
  TestMaps_MSfixed64Entry_descriptor_ = TestMaps_descriptor_->nested_type(9);
  TestMaps_MBoolEntry_descriptor_ = TestMaps_descriptor_->nested_type(10);
  TestMaps_MStringEntry_descriptor_ = TestMaps_descriptor_->nested_type(11);
  Proto2MapEnum_descriptor_ = file->enum_type(0);
  Proto2MapEnumPlusExtra_descriptor_ = file->enum_type(1);
}

namespace {

GOOGLE_PROTOBUF_DECLARE_ONCE(protobuf_AssignDescriptors_once_);
void protobuf_AssignDescriptorsOnce() {
  ::google::protobuf::GoogleOnceInit(&protobuf_AssignDescriptors_once_,
                 &protobuf_AssignDesc_google_2fprotobuf_2fmap_5fproto2_5funittest_2eproto);
}

void protobuf_RegisterTypes(const ::std::string&) GOOGLE_ATTRIBUTE_COLD;
void protobuf_RegisterTypes(const ::std::string&) {
  protobuf_AssignDescriptorsOnce();
  ::google::protobuf::MessageFactory::InternalRegisterGeneratedMessage(
      TestEnumMap_descriptor_, TestEnumMap::internal_default_instance());
  ::google::protobuf::MessageFactory::InternalRegisterGeneratedMessage(
        TestEnumMap_KnownMapFieldEntry_descriptor_,
        ::google::protobuf::internal::MapEntry<
            ::google::protobuf::int32,
            ::protobuf_unittest::Proto2MapEnum,
            ::google::protobuf::internal::WireFormatLite::TYPE_INT32,
            ::google::protobuf::internal::WireFormatLite::TYPE_ENUM,
            0>::CreateDefaultInstance(
                TestEnumMap_KnownMapFieldEntry_descriptor_));
  ::google::protobuf::MessageFactory::InternalRegisterGeneratedMessage(
        TestEnumMap_UnknownMapFieldEntry_descriptor_,
        ::google::protobuf::internal::MapEntry<
            ::google::protobuf::int32,
            ::protobuf_unittest::Proto2MapEnum,
            ::google::protobuf::internal::WireFormatLite::TYPE_INT32,
            ::google::protobuf::internal::WireFormatLite::TYPE_ENUM,
            0>::CreateDefaultInstance(
                TestEnumMap_UnknownMapFieldEntry_descriptor_));
  ::google::protobuf::MessageFactory::InternalRegisterGeneratedMessage(
      TestEnumMapPlusExtra_descriptor_, TestEnumMapPlusExtra::internal_default_instance());
  ::google::protobuf::MessageFactory::InternalRegisterGeneratedMessage(
        TestEnumMapPlusExtra_KnownMapFieldEntry_descriptor_,
        ::google::protobuf::internal::MapEntry<
            ::google::protobuf::int32,
            ::protobuf_unittest::Proto2MapEnumPlusExtra,
            ::google::protobuf::internal::WireFormatLite::TYPE_INT32,
            ::google::protobuf::internal::WireFormatLite::TYPE_ENUM,
            0>::CreateDefaultInstance(
                TestEnumMapPlusExtra_KnownMapFieldEntry_descriptor_));
  ::google::protobuf::MessageFactory::InternalRegisterGeneratedMessage(
        TestEnumMapPlusExtra_UnknownMapFieldEntry_descriptor_,
        ::google::protobuf::internal::MapEntry<
            ::google::protobuf::int32,
            ::protobuf_unittest::Proto2MapEnumPlusExtra,
            ::google::protobuf::internal::WireFormatLite::TYPE_INT32,
            ::google::protobuf::internal::WireFormatLite::TYPE_ENUM,
            0>::CreateDefaultInstance(
                TestEnumMapPlusExtra_UnknownMapFieldEntry_descriptor_));
  ::google::protobuf::MessageFactory::InternalRegisterGeneratedMessage(
      TestImportEnumMap_descriptor_, TestImportEnumMap::internal_default_instance());
  ::google::protobuf::MessageFactory::InternalRegisterGeneratedMessage(
        TestImportEnumMap_ImportEnumAmpEntry_descriptor_,
        ::google::protobuf::internal::MapEntry<
            ::google::protobuf::int32,
            ::protobuf_unittest_import::ImportEnumForMap,
            ::google::protobuf::internal::WireFormatLite::TYPE_INT32,
            ::google::protobuf::internal::WireFormatLite::TYPE_ENUM,
            0>::CreateDefaultInstance(
                TestImportEnumMap_ImportEnumAmpEntry_descriptor_));
  ::google::protobuf::MessageFactory::InternalRegisterGeneratedMessage(
      TestIntIntMap_descriptor_, TestIntIntMap::internal_default_instance());
  ::google::protobuf::MessageFactory::InternalRegisterGeneratedMessage(
        TestIntIntMap_MEntry_descriptor_,
        ::google::protobuf::internal::MapEntry<
            ::google::protobuf::int32,
            ::google::protobuf::int32,
            ::google::protobuf::internal::WireFormatLite::TYPE_INT32,
            ::google::protobuf::internal::WireFormatLite::TYPE_INT32,
            0>::CreateDefaultInstance(
                TestIntIntMap_MEntry_descriptor_));
  ::google::protobuf::MessageFactory::InternalRegisterGeneratedMessage(
      TestMaps_descriptor_, TestMaps::internal_default_instance());
  ::google::protobuf::MessageFactory::InternalRegisterGeneratedMessage(
        TestMaps_MInt32Entry_descriptor_,
        ::google::protobuf::internal::MapEntry<
            ::google::protobuf::int32,
            ::protobuf_unittest::TestIntIntMap,
            ::google::protobuf::internal::WireFormatLite::TYPE_INT32,
            ::google::protobuf::internal::WireFormatLite::TYPE_MESSAGE,
            0>::CreateDefaultInstance(
                TestMaps_MInt32Entry_descriptor_));
  ::google::protobuf::MessageFactory::InternalRegisterGeneratedMessage(
        TestMaps_MInt64Entry_descriptor_,
        ::google::protobuf::internal::MapEntry<
            ::google::protobuf::int64,
            ::protobuf_unittest::TestIntIntMap,
            ::google::protobuf::internal::WireFormatLite::TYPE_INT64,
            ::google::protobuf::internal::WireFormatLite::TYPE_MESSAGE,
            0>::CreateDefaultInstance(
                TestMaps_MInt64Entry_descriptor_));
  ::google::protobuf::MessageFactory::InternalRegisterGeneratedMessage(
        TestMaps_MUint32Entry_descriptor_,
        ::google::protobuf::internal::MapEntry<
            ::google::protobuf::uint32,
            ::protobuf_unittest::TestIntIntMap,
            ::google::protobuf::internal::WireFormatLite::TYPE_UINT32,
            ::google::protobuf::internal::WireFormatLite::TYPE_MESSAGE,
            0>::CreateDefaultInstance(
                TestMaps_MUint32Entry_descriptor_));
  ::google::protobuf::MessageFactory::InternalRegisterGeneratedMessage(
        TestMaps_MUint64Entry_descriptor_,
        ::google::protobuf::internal::MapEntry<
            ::google::protobuf::uint64,
            ::protobuf_unittest::TestIntIntMap,
            ::google::protobuf::internal::WireFormatLite::TYPE_UINT64,
            ::google::protobuf::internal::WireFormatLite::TYPE_MESSAGE,
            0>::CreateDefaultInstance(
                TestMaps_MUint64Entry_descriptor_));
  ::google::protobuf::MessageFactory::InternalRegisterGeneratedMessage(
        TestMaps_MSint32Entry_descriptor_,
        ::google::protobuf::internal::MapEntry<
            ::google::protobuf::int32,
            ::protobuf_unittest::TestIntIntMap,
            ::google::protobuf::internal::WireFormatLite::TYPE_SINT32,
            ::google::protobuf::internal::WireFormatLite::TYPE_MESSAGE,
            0>::CreateDefaultInstance(
                TestMaps_MSint32Entry_descriptor_));
  ::google::protobuf::MessageFactory::InternalRegisterGeneratedMessage(
        TestMaps_MSint64Entry_descriptor_,
        ::google::protobuf::internal::MapEntry<
            ::google::protobuf::int64,
            ::protobuf_unittest::TestIntIntMap,
            ::google::protobuf::internal::WireFormatLite::TYPE_SINT64,
            ::google::protobuf::internal::WireFormatLite::TYPE_MESSAGE,
            0>::CreateDefaultInstance(
                TestMaps_MSint64Entry_descriptor_));
  ::google::protobuf::MessageFactory::InternalRegisterGeneratedMessage(
        TestMaps_MFixed32Entry_descriptor_,
        ::google::protobuf::internal::MapEntry<
            ::google::protobuf::uint32,
            ::protobuf_unittest::TestIntIntMap,
            ::google::protobuf::internal::WireFormatLite::TYPE_FIXED32,
            ::google::protobuf::internal::WireFormatLite::TYPE_MESSAGE,
            0>::CreateDefaultInstance(
                TestMaps_MFixed32Entry_descriptor_));
  ::google::protobuf::MessageFactory::InternalRegisterGeneratedMessage(
        TestMaps_MFixed64Entry_descriptor_,
        ::google::protobuf::internal::MapEntry<
            ::google::protobuf::uint64,
            ::protobuf_unittest::TestIntIntMap,
            ::google::protobuf::internal::WireFormatLite::TYPE_FIXED64,
            ::google::protobuf::internal::WireFormatLite::TYPE_MESSAGE,
            0>::CreateDefaultInstance(
                TestMaps_MFixed64Entry_descriptor_));
  ::google::protobuf::MessageFactory::InternalRegisterGeneratedMessage(
        TestMaps_MSfixed32Entry_descriptor_,
        ::google::protobuf::internal::MapEntry<
            ::google::protobuf::int32,
            ::protobuf_unittest::TestIntIntMap,
            ::google::protobuf::internal::WireFormatLite::TYPE_SFIXED32,
            ::google::protobuf::internal::WireFormatLite::TYPE_MESSAGE,
            0>::CreateDefaultInstance(
                TestMaps_MSfixed32Entry_descriptor_));
  ::google::protobuf::MessageFactory::InternalRegisterGeneratedMessage(
        TestMaps_MSfixed64Entry_descriptor_,
        ::google::protobuf::internal::MapEntry<
            ::google::protobuf::int64,
            ::protobuf_unittest::TestIntIntMap,
            ::google::protobuf::internal::WireFormatLite::TYPE_SFIXED64,
            ::google::protobuf::internal::WireFormatLite::TYPE_MESSAGE,
            0>::CreateDefaultInstance(
                TestMaps_MSfixed64Entry_descriptor_));
  ::google::protobuf::MessageFactory::InternalRegisterGeneratedMessage(
        TestMaps_MBoolEntry_descriptor_,
        ::google::protobuf::internal::MapEntry<
            bool,
            ::protobuf_unittest::TestIntIntMap,
            ::google::protobuf::internal::WireFormatLite::TYPE_BOOL,
            ::google::protobuf::internal::WireFormatLite::TYPE_MESSAGE,
            0>::CreateDefaultInstance(
                TestMaps_MBoolEntry_descriptor_));
  ::google::protobuf::MessageFactory::InternalRegisterGeneratedMessage(
        TestMaps_MStringEntry_descriptor_,
        ::google::protobuf::internal::MapEntry<
            ::std::string,
            ::protobuf_unittest::TestIntIntMap,
            ::google::protobuf::internal::WireFormatLite::TYPE_STRING,
            ::google::protobuf::internal::WireFormatLite::TYPE_MESSAGE,
            0>::CreateDefaultInstance(
                TestMaps_MStringEntry_descriptor_));
}

}  // namespace

void protobuf_ShutdownFile_google_2fprotobuf_2fmap_5fproto2_5funittest_2eproto() {
  TestEnumMap_default_instance_.Shutdown();
  delete TestEnumMap_reflection_;
  TestEnumMapPlusExtra_default_instance_.Shutdown();
  delete TestEnumMapPlusExtra_reflection_;
  TestImportEnumMap_default_instance_.Shutdown();
  delete TestImportEnumMap_reflection_;
  TestIntIntMap_default_instance_.Shutdown();
  delete TestIntIntMap_reflection_;
  TestMaps_default_instance_.Shutdown();
  delete TestMaps_reflection_;
}

void protobuf_InitDefaults_google_2fprotobuf_2fmap_5fproto2_5funittest_2eproto_impl() {
  GOOGLE_PROTOBUF_VERIFY_VERSION;

  ::protobuf_unittest_import::protobuf_InitDefaults_google_2fprotobuf_2funittest_5fimport_2eproto();
  TestEnumMap_default_instance_.DefaultConstruct();
  ::google::protobuf::internal::GetEmptyString();
  TestEnumMapPlusExtra_default_instance_.DefaultConstruct();
  ::google::protobuf::internal::GetEmptyString();
  ::google::protobuf::internal::GetEmptyString();
  TestImportEnumMap_default_instance_.DefaultConstruct();
  ::google::protobuf::internal::GetEmptyString();
  TestIntIntMap_default_instance_.DefaultConstruct();
  ::google::protobuf::internal::GetEmptyString();
  TestMaps_default_instance_.DefaultConstruct();
  TestEnumMap_default_instance_.get_mutable()->InitAsDefaultInstance();
  TestEnumMapPlusExtra_default_instance_.get_mutable()->InitAsDefaultInstance();
  TestImportEnumMap_default_instance_.get_mutable()->InitAsDefaultInstance();
  TestIntIntMap_default_instance_.get_mutable()->InitAsDefaultInstance();
  TestMaps_default_instance_.get_mutable()->InitAsDefaultInstance();
}

GOOGLE_PROTOBUF_DECLARE_ONCE(protobuf_InitDefaults_google_2fprotobuf_2fmap_5fproto2_5funittest_2eproto_once_);
void protobuf_InitDefaults_google_2fprotobuf_2fmap_5fproto2_5funittest_2eproto() {
  ::google::protobuf::GoogleOnceInit(&protobuf_InitDefaults_google_2fprotobuf_2fmap_5fproto2_5funittest_2eproto_once_,
                 &protobuf_InitDefaults_google_2fprotobuf_2fmap_5fproto2_5funittest_2eproto_impl);
}
void protobuf_AddDesc_google_2fprotobuf_2fmap_5fproto2_5funittest_2eproto_impl() {
  GOOGLE_PROTOBUF_VERIFY_VERSION;

  protobuf_InitDefaults_google_2fprotobuf_2fmap_5fproto2_5funittest_2eproto();
  ::google::protobuf::DescriptorPool::InternalAddGeneratedFile(
    "\n)google/protobuf/map_proto2_unittest.pr"
    "oto\022\021protobuf_unittest\032%google/protobuf/"
    "unittest_import.proto\"\333\002\n\013TestEnumMap\022J\n"
    "\017known_map_field\030e \003(\01321.protobuf_unitte"
    "st.TestEnumMap.KnownMapFieldEntry\022N\n\021unk"
    "nown_map_field\030f \003(\01323.protobuf_unittest"
    ".TestEnumMap.UnknownMapFieldEntry\032V\n\022Kno"
    "wnMapFieldEntry\022\013\n\003key\030\001 \001(\005\022/\n\005value\030\002 "
    "\001(\0162 .protobuf_unittest.Proto2MapEnum:\0028"
    "\001\032X\n\024UnknownMapFieldEntry\022\013\n\003key\030\001 \001(\005\022/"
    "\n\005value\030\002 \001(\0162 .protobuf_unittest.Proto2"
    "MapEnum:\0028\001\"\210\003\n\024TestEnumMapPlusExtra\022S\n\017"
    "known_map_field\030e \003(\0132:.protobuf_unittes"
    "t.TestEnumMapPlusExtra.KnownMapFieldEntr"
    "y\022W\n\021unknown_map_field\030f \003(\0132<.protobuf_"
    "unittest.TestEnumMapPlusExtra.UnknownMap"
    "FieldEntry\032_\n\022KnownMapFieldEntry\022\013\n\003key\030"
    "\001 \001(\005\0228\n\005value\030\002 \001(\0162).protobuf_unittest"
    ".Proto2MapEnumPlusExtra:\0028\001\032a\n\024UnknownMa"
    "pFieldEntry\022\013\n\003key\030\001 \001(\005\0228\n\005value\030\002 \001(\0162"
    ").protobuf_unittest.Proto2MapEnumPlusExt"
    "ra:\0028\001\"\307\001\n\021TestImportEnumMap\022P\n\017import_e"
    "num_amp\030\001 \003(\01327.protobuf_unittest.TestIm"
    "portEnumMap.ImportEnumAmpEntry\032`\n\022Import"
    "EnumAmpEntry\022\013\n\003key\030\001 \001(\005\0229\n\005value\030\002 \001(\016"
    "2*.protobuf_unittest_import.ImportEnumFo"
    "rMap:\0028\001\"m\n\rTestIntIntMap\0222\n\001m\030\001 \003(\0132\'.p"
    "rotobuf_unittest.TestIntIntMap.MEntry\032(\n"
    "\006MEntry\022\013\n\003key\030\001 \001(\005\022\r\n\005value\030\002 \001(\005:\0028\001\""
    "\270\r\n\010TestMaps\0228\n\007m_int32\030\001 \003(\0132\'.protobuf"
    "_unittest.TestMaps.MInt32Entry\0228\n\007m_int6"
    "4\030\002 \003(\0132\'.protobuf_unittest.TestMaps.MIn"
    "t64Entry\022:\n\010m_uint32\030\003 \003(\0132(.protobuf_un"
    "ittest.TestMaps.MUint32Entry\022:\n\010m_uint64"
    "\030\004 \003(\0132(.protobuf_unittest.TestMaps.MUin"
    "t64Entry\022:\n\010m_sint32\030\005 \003(\0132(.protobuf_un"
    "ittest.TestMaps.MSint32Entry\022:\n\010m_sint64"
    "\030\006 \003(\0132(.protobuf_unittest.TestMaps.MSin"
    "t64Entry\022<\n\tm_fixed32\030\007 \003(\0132).protobuf_u"
    "nittest.TestMaps.MFixed32Entry\022<\n\tm_fixe"
    "d64\030\010 \003(\0132).protobuf_unittest.TestMaps.M"
    "Fixed64Entry\022>\n\nm_sfixed32\030\t \003(\0132*.proto"
    "buf_unittest.TestMaps.MSfixed32Entry\022>\n\n"
    "m_sfixed64\030\n \003(\0132*.protobuf_unittest.Tes"
    "tMaps.MSfixed64Entry\0226\n\006m_bool\030\013 \003(\0132&.p"
    "rotobuf_unittest.TestMaps.MBoolEntry\022:\n\010"
    "m_string\030\014 \003(\0132(.protobuf_unittest.TestM"
    "aps.MStringEntry\032O\n\013MInt32Entry\022\013\n\003key\030\001"
    " \001(\005\022/\n\005value\030\002 \001(\0132 .protobuf_unittest."
    "TestIntIntMap:\0028\001\032O\n\013MInt64Entry\022\013\n\003key\030"
    "\001 \001(\003\022/\n\005value\030\002 \001(\0132 .protobuf_unittest"
    ".TestIntIntMap:\0028\001\032P\n\014MUint32Entry\022\013\n\003ke"
    "y\030\001 \001(\r\022/\n\005value\030\002 \001(\0132 .protobuf_unitte"
    "st.TestIntIntMap:\0028\001\032P\n\014MUint64Entry\022\013\n\003"
    "key\030\001 \001(\004\022/\n\005value\030\002 \001(\0132 .protobuf_unit"
    "test.TestIntIntMap:\0028\001\032P\n\014MSint32Entry\022\013"
    "\n\003key\030\001 \001(\021\022/\n\005value\030\002 \001(\0132 .protobuf_un"
    "ittest.TestIntIntMap:\0028\001\032P\n\014MSint64Entry"
    "\022\013\n\003key\030\001 \001(\022\022/\n\005value\030\002 \001(\0132 .protobuf_"
    "unittest.TestIntIntMap:\0028\001\032Q\n\rMFixed32En"
    "try\022\013\n\003key\030\001 \001(\007\022/\n\005value\030\002 \001(\0132 .protob"
    "uf_unittest.TestIntIntMap:\0028\001\032Q\n\rMFixed6"
    "4Entry\022\013\n\003key\030\001 \001(\006\022/\n\005value\030\002 \001(\0132 .pro"
    "tobuf_unittest.TestIntIntMap:\0028\001\032R\n\016MSfi"
    "xed32Entry\022\013\n\003key\030\001 \001(\017\022/\n\005value\030\002 \001(\0132 "
    ".protobuf_unittest.TestIntIntMap:\0028\001\032R\n\016"
    "MSfixed64Entry\022\013\n\003key\030\001 \001(\020\022/\n\005value\030\002 \001"
    "(\0132 .protobuf_unittest.TestIntIntMap:\0028\001"
    "\032N\n\nMBoolEntry\022\013\n\003key\030\001 \001(\010\022/\n\005value\030\002 \001"
    "(\0132 .protobuf_unittest.TestIntIntMap:\0028\001"
    "\032P\n\014MStringEntry\022\013\n\003key\030\001 \001(\t\022/\n\005value\030\002"
    " \001(\0132 .protobuf_unittest.TestIntIntMap:\002"
    "8\001*Z\n\rProto2MapEnum\022\027\n\023PROTO2_MAP_ENUM_F"
    "OO\020\000\022\027\n\023PROTO2_MAP_ENUM_BAR\020\001\022\027\n\023PROTO2_"
    "MAP_ENUM_BAZ\020\002*\206\001\n\026Proto2MapEnumPlusExtr"
    "a\022\031\n\025E_PROTO2_MAP_ENUM_FOO\020\000\022\031\n\025E_PROTO2"
    "_MAP_ENUM_BAR\020\001\022\031\n\025E_PROTO2_MAP_ENUM_BAZ"
    "\020\002\022\033\n\027E_PROTO2_MAP_ENUM_EXTRA\020\003", 3111);
  ::google::protobuf::MessageFactory::InternalRegisterGeneratedFile(
    "google/protobuf/map_proto2_unittest.proto", &protobuf_RegisterTypes);
  ::protobuf_unittest_import::protobuf_AddDesc_google_2fprotobuf_2funittest_5fimport_2eproto();
  ::google::protobuf::internal::OnShutdown(&protobuf_ShutdownFile_google_2fprotobuf_2fmap_5fproto2_5funittest_2eproto);
}

GOOGLE_PROTOBUF_DECLARE_ONCE(protobuf_AddDesc_google_2fprotobuf_2fmap_5fproto2_5funittest_2eproto_once_);
void protobuf_AddDesc_google_2fprotobuf_2fmap_5fproto2_5funittest_2eproto() {
  ::google::protobuf::GoogleOnceInit(&protobuf_AddDesc_google_2fprotobuf_2fmap_5fproto2_5funittest_2eproto_once_,
                 &protobuf_AddDesc_google_2fprotobuf_2fmap_5fproto2_5funittest_2eproto_impl);
}
// Force AddDescriptors() to be called at static initialization time.
struct StaticDescriptorInitializer_google_2fprotobuf_2fmap_5fproto2_5funittest_2eproto {
  StaticDescriptorInitializer_google_2fprotobuf_2fmap_5fproto2_5funittest_2eproto() {
    protobuf_AddDesc_google_2fprotobuf_2fmap_5fproto2_5funittest_2eproto();
  }
} static_descriptor_initializer_google_2fprotobuf_2fmap_5fproto2_5funittest_2eproto_;
const ::google::protobuf::EnumDescriptor* Proto2MapEnum_descriptor() {
  protobuf_AssignDescriptorsOnce();
  return Proto2MapEnum_descriptor_;
}
bool Proto2MapEnum_IsValid(int value) {
  switch (value) {
    case 0:
    case 1:
    case 2:
      return true;
    default:
      return false;
  }
}

const ::google::protobuf::EnumDescriptor* Proto2MapEnumPlusExtra_descriptor() {
  protobuf_AssignDescriptorsOnce();
  return Proto2MapEnumPlusExtra_descriptor_;
}
bool Proto2MapEnumPlusExtra_IsValid(int value) {
  switch (value) {
    case 0:
    case 1:
    case 2:
    case 3:
      return true;
    default:
      return false;
  }
}


namespace {

static void MergeFromFail(int line) GOOGLE_ATTRIBUTE_COLD GOOGLE_ATTRIBUTE_NORETURN;
static void MergeFromFail(int line) {
  ::google::protobuf::internal::MergeFromFail(__FILE__, line);
}

}  // namespace


// ===================================================================

#if !defined(_MSC_VER) || _MSC_VER >= 1900
const int TestEnumMap::kKnownMapFieldFieldNumber;
const int TestEnumMap::kUnknownMapFieldFieldNumber;
#endif  // !defined(_MSC_VER) || _MSC_VER >= 1900

TestEnumMap::TestEnumMap()
  : ::google::protobuf::Message(), _internal_metadata_(NULL) {
  if (this != internal_default_instance()) protobuf_InitDefaults_google_2fprotobuf_2fmap_5fproto2_5funittest_2eproto();
  SharedCtor();
  // @@protoc_insertion_point(constructor:protobuf_unittest.TestEnumMap)
}

void TestEnumMap::InitAsDefaultInstance() {
}

TestEnumMap::TestEnumMap(const TestEnumMap& from)
  : ::google::protobuf::Message(),
    _internal_metadata_(NULL) {
  SharedCtor();
  UnsafeMergeFrom(from);
  // @@protoc_insertion_point(copy_constructor:protobuf_unittest.TestEnumMap)
}

void TestEnumMap::SharedCtor() {
  _cached_size_ = 0;
  known_map_field_.SetAssignDescriptorCallback(
      protobuf_AssignDescriptorsOnce);
  known_map_field_.SetEntryDescriptor(
      &::protobuf_unittest::TestEnumMap_KnownMapFieldEntry_descriptor_);
  unknown_map_field_.SetAssignDescriptorCallback(
      protobuf_AssignDescriptorsOnce);
  unknown_map_field_.SetEntryDescriptor(
      &::protobuf_unittest::TestEnumMap_UnknownMapFieldEntry_descriptor_);
}

TestEnumMap::~TestEnumMap() {
  // @@protoc_insertion_point(destructor:protobuf_unittest.TestEnumMap)
  SharedDtor();
}

void TestEnumMap::SharedDtor() {
}

void TestEnumMap::SetCachedSize(int size) const {
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
}
const ::google::protobuf::Descriptor* TestEnumMap::descriptor() {
  protobuf_AssignDescriptorsOnce();
  return TestEnumMap_descriptor_;
}

const TestEnumMap& TestEnumMap::default_instance() {
  protobuf_InitDefaults_google_2fprotobuf_2fmap_5fproto2_5funittest_2eproto();
  return *internal_default_instance();
}

::google::protobuf::internal::ExplicitlyConstructed<TestEnumMap> TestEnumMap_default_instance_;

TestEnumMap* TestEnumMap::New(::google::protobuf::Arena* arena) const {
  TestEnumMap* n = new TestEnumMap;
  if (arena != NULL) {
    arena->Own(n);
  }
  return n;
}

void TestEnumMap::Clear() {
// @@protoc_insertion_point(message_clear_start:protobuf_unittest.TestEnumMap)
  known_map_field_.Clear();
  unknown_map_field_.Clear();
  _has_bits_.Clear();
  if (_internal_metadata_.have_unknown_fields()) {
    mutable_unknown_fields()->Clear();
  }
}

bool TestEnumMap::MergePartialFromCodedStream(
    ::google::protobuf::io::CodedInputStream* input) {
#define DO_(EXPRESSION) if (!GOOGLE_PREDICT_TRUE(EXPRESSION)) goto failure
  ::google::protobuf::uint32 tag;
  // @@protoc_insertion_point(parse_start:protobuf_unittest.TestEnumMap)
  for (;;) {
    ::std::pair< ::google::protobuf::uint32, bool> p = input->ReadTagWithCutoff(16383);
    tag = p.first;
    if (!p.second) goto handle_unusual;
    switch (::google::protobuf::internal::WireFormatLite::GetTagFieldNumber(tag)) {
      // map<int32, .protobuf_unittest.Proto2MapEnum> known_map_field = 101;
      case 101: {
        if (tag == 810) {
          DO_(input->IncrementRecursionDepth());
         parse_loop_known_map_field:
          ::google::protobuf::scoped_ptr<TestEnumMap_KnownMapFieldEntry> entry(known_map_field_.NewEntry());
          {
            ::std::string data;
            DO_(::google::protobuf::internal::WireFormatLite::ReadString(input, &data));
            DO_(entry->ParseFromString(data));
            if (::protobuf_unittest::Proto2MapEnum_IsValid(*entry->mutable_value())) {
              (*mutable_known_map_field())[entry->key()] =
                  static_cast< ::protobuf_unittest::Proto2MapEnum >(*entry->mutable_value());
            } else {
              mutable_unknown_fields()->AddLengthDelimited(101, data);
            }
          }
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(810)) goto parse_loop_known_map_field;
        if (input->ExpectTag(818)) goto parse_loop_unknown_map_field;
        input->UnsafeDecrementRecursionDepth();
        break;
      }

      // map<int32, .protobuf_unittest.Proto2MapEnum> unknown_map_field = 102;
      case 102: {
        if (tag == 818) {
          DO_(input->IncrementRecursionDepth());
         parse_loop_unknown_map_field:
          ::google::protobuf::scoped_ptr<TestEnumMap_UnknownMapFieldEntry> entry(unknown_map_field_.NewEntry());
          {
            ::std::string data;
            DO_(::google::protobuf::internal::WireFormatLite::ReadString(input, &data));
            DO_(entry->ParseFromString(data));
            if (::protobuf_unittest::Proto2MapEnum_IsValid(*entry->mutable_value())) {
              (*mutable_unknown_map_field())[entry->key()] =
                  static_cast< ::protobuf_unittest::Proto2MapEnum >(*entry->mutable_value());
            } else {
              mutable_unknown_fields()->AddLengthDelimited(102, data);
            }
          }
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(818)) goto parse_loop_unknown_map_field;
        input->UnsafeDecrementRecursionDepth();
        if (input->ExpectAtEnd()) goto success;
        break;
      }

      default: {
      handle_unusual:
        if (tag == 0 ||
            ::google::protobuf::internal::WireFormatLite::GetTagWireType(tag) ==
            ::google::protobuf::internal::WireFormatLite::WIRETYPE_END_GROUP) {
          goto success;
        }
        DO_(::google::protobuf::internal::WireFormat::SkipField(
              input, tag, mutable_unknown_fields()));
        break;
      }
    }
  }
success:
  // @@protoc_insertion_point(parse_success:protobuf_unittest.TestEnumMap)
  return true;
failure:
  // @@protoc_insertion_point(parse_failure:protobuf_unittest.TestEnumMap)
  return false;
#undef DO_
}

void TestEnumMap::SerializeWithCachedSizes(
    ::google::protobuf::io::CodedOutputStream* output) const {
  // @@protoc_insertion_point(serialize_start:protobuf_unittest.TestEnumMap)
  // map<int32, .protobuf_unittest.Proto2MapEnum> known_map_field = 101;
  if (!this->known_map_field().empty()) {
    typedef ::google::protobuf::Map< ::google::protobuf::int32, ::protobuf_unittest::Proto2MapEnum >::const_pointer
        ConstPtr;
    typedef ::google::protobuf::internal::SortItem< ::google::protobuf::int32, ConstPtr > SortItem;
    typedef ::google::protobuf::internal::CompareByFirstField<SortItem> Less;

    if (output->IsSerializationDeterminstic() &&
        this->known_map_field().size() > 1) {
      ::google::protobuf::scoped_array<SortItem> items(
          new SortItem[this->known_map_field().size()]);
      typedef ::google::protobuf::Map< ::google::protobuf::int32, ::protobuf_unittest::Proto2MapEnum >::size_type size_type;
      size_type n = 0;
      for (::google::protobuf::Map< ::google::protobuf::int32, ::protobuf_unittest::Proto2MapEnum >::const_iterator
          it = this->known_map_field().begin();
          it != this->known_map_field().end(); ++it, ++n) {
        items[n] = SortItem(&*it);
      }
      ::std::sort(&items[0], &items[n], Less());
      ::google::protobuf::scoped_ptr<TestEnumMap_KnownMapFieldEntry> entry;
      for (size_type i = 0; i < n; i++) {
        entry.reset(known_map_field_.NewEnumEntryWrapper(
            items[i].second->first, items[i].second->second));
        ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
            101, *entry, output);
      }
    } else {
      ::google::protobuf::scoped_ptr<TestEnumMap_KnownMapFieldEntry> entry;
      for (::google::protobuf::Map< ::google::protobuf::int32, ::protobuf_unittest::Proto2MapEnum >::const_iterator
          it = this->known_map_field().begin();
          it != this->known_map_field().end(); ++it) {
        entry.reset(known_map_field_.NewEnumEntryWrapper(
            it->first, it->second));
        ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
            101, *entry, output);
      }
    }
  }

  // map<int32, .protobuf_unittest.Proto2MapEnum> unknown_map_field = 102;
  if (!this->unknown_map_field().empty()) {
    typedef ::google::protobuf::Map< ::google::protobuf::int32, ::protobuf_unittest::Proto2MapEnum >::const_pointer
        ConstPtr;
    typedef ::google::protobuf::internal::SortItem< ::google::protobuf::int32, ConstPtr > SortItem;
    typedef ::google::protobuf::internal::CompareByFirstField<SortItem> Less;

    if (output->IsSerializationDeterminstic() &&
        this->unknown_map_field().size() > 1) {
      ::google::protobuf::scoped_array<SortItem> items(
          new SortItem[this->unknown_map_field().size()]);
      typedef ::google::protobuf::Map< ::google::protobuf::int32, ::protobuf_unittest::Proto2MapEnum >::size_type size_type;
      size_type n = 0;
      for (::google::protobuf::Map< ::google::protobuf::int32, ::protobuf_unittest::Proto2MapEnum >::const_iterator
          it = this->unknown_map_field().begin();
          it != this->unknown_map_field().end(); ++it, ++n) {
        items[n] = SortItem(&*it);
      }
      ::std::sort(&items[0], &items[n], Less());
      ::google::protobuf::scoped_ptr<TestEnumMap_UnknownMapFieldEntry> entry;
      for (size_type i = 0; i < n; i++) {
        entry.reset(unknown_map_field_.NewEnumEntryWrapper(
            items[i].second->first, items[i].second->second));
        ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
            102, *entry, output);
      }
    } else {
      ::google::protobuf::scoped_ptr<TestEnumMap_UnknownMapFieldEntry> entry;
      for (::google::protobuf::Map< ::google::protobuf::int32, ::protobuf_unittest::Proto2MapEnum >::const_iterator
          it = this->unknown_map_field().begin();
          it != this->unknown_map_field().end(); ++it) {
        entry.reset(unknown_map_field_.NewEnumEntryWrapper(
            it->first, it->second));
        ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
            102, *entry, output);
      }
    }
  }

  if (_internal_metadata_.have_unknown_fields()) {
    ::google::protobuf::internal::WireFormat::SerializeUnknownFields(
        unknown_fields(), output);
  }
  // @@protoc_insertion_point(serialize_end:protobuf_unittest.TestEnumMap)
}

::google::protobuf::uint8* TestEnumMap::InternalSerializeWithCachedSizesToArray(
    bool deterministic, ::google::protobuf::uint8* target) const {
  (void)deterministic; // Unused
  // @@protoc_insertion_point(serialize_to_array_start:protobuf_unittest.TestEnumMap)
  // map<int32, .protobuf_unittest.Proto2MapEnum> known_map_field = 101;
  if (!this->known_map_field().empty()) {
    typedef ::google::protobuf::Map< ::google::protobuf::int32, ::protobuf_unittest::Proto2MapEnum >::const_pointer
        ConstPtr;
    typedef ::google::protobuf::internal::SortItem< ::google::protobuf::int32, ConstPtr > SortItem;
    typedef ::google::protobuf::internal::CompareByFirstField<SortItem> Less;

    if (deterministic &&
        this->known_map_field().size() > 1) {
      ::google::protobuf::scoped_array<SortItem> items(
          new SortItem[this->known_map_field().size()]);
      typedef ::google::protobuf::Map< ::google::protobuf::int32, ::protobuf_unittest::Proto2MapEnum >::size_type size_type;
      size_type n = 0;
      for (::google::protobuf::Map< ::google::protobuf::int32, ::protobuf_unittest::Proto2MapEnum >::const_iterator
          it = this->known_map_field().begin();
          it != this->known_map_field().end(); ++it, ++n) {
        items[n] = SortItem(&*it);
      }
      ::std::sort(&items[0], &items[n], Less());
      ::google::protobuf::scoped_ptr<TestEnumMap_KnownMapFieldEntry> entry;
      for (size_type i = 0; i < n; i++) {
        entry.reset(known_map_field_.NewEnumEntryWrapper(
            items[i].second->first, items[i].second->second));
        target = ::google::protobuf::internal::WireFormatLite::
                   InternalWriteMessageNoVirtualToArray(
                       101, *entry, deterministic, target);
;
      }
    } else {
      ::google::protobuf::scoped_ptr<TestEnumMap_KnownMapFieldEntry> entry;
      for (::google::protobuf::Map< ::google::protobuf::int32, ::protobuf_unittest::Proto2MapEnum >::const_iterator
          it = this->known_map_field().begin();
          it != this->known_map_field().end(); ++it) {
        entry.reset(known_map_field_.NewEnumEntryWrapper(
            it->first, it->second));
        target = ::google::protobuf::internal::WireFormatLite::
                   InternalWriteMessageNoVirtualToArray(
                       101, *entry, deterministic, target);
;
      }
    }
  }

  // map<int32, .protobuf_unittest.Proto2MapEnum> unknown_map_field = 102;
  if (!this->unknown_map_field().empty()) {
    typedef ::google::protobuf::Map< ::google::protobuf::int32, ::protobuf_unittest::Proto2MapEnum >::const_pointer
        ConstPtr;
    typedef ::google::protobuf::internal::SortItem< ::google::protobuf::int32, ConstPtr > SortItem;
    typedef ::google::protobuf::internal::CompareByFirstField<SortItem> Less;

    if (deterministic &&
        this->unknown_map_field().size() > 1) {
      ::google::protobuf::scoped_array<SortItem> items(
          new SortItem[this->unknown_map_field().size()]);
      typedef ::google::protobuf::Map< ::google::protobuf::int32, ::protobuf_unittest::Proto2MapEnum >::size_type size_type;
      size_type n = 0;
      for (::google::protobuf::Map< ::google::protobuf::int32, ::protobuf_unittest::Proto2MapEnum >::const_iterator
          it = this->unknown_map_field().begin();
          it != this->unknown_map_field().end(); ++it, ++n) {
        items[n] = SortItem(&*it);
      }
      ::std::sort(&items[0], &items[n], Less());
      ::google::protobuf::scoped_ptr<TestEnumMap_UnknownMapFieldEntry> entry;
      for (size_type i = 0; i < n; i++) {
        entry.reset(unknown_map_field_.NewEnumEntryWrapper(
            items[i].second->first, items[i].second->second));
        target = ::google::protobuf::internal::WireFormatLite::
                   InternalWriteMessageNoVirtualToArray(
                       102, *entry, deterministic, target);
;
      }
    } else {
      ::google::protobuf::scoped_ptr<TestEnumMap_UnknownMapFieldEntry> entry;
      for (::google::protobuf::Map< ::google::protobuf::int32, ::protobuf_unittest::Proto2MapEnum >::const_iterator
          it = this->unknown_map_field().begin();
          it != this->unknown_map_field().end(); ++it) {
        entry.reset(unknown_map_field_.NewEnumEntryWrapper(
            it->first, it->second));
        target = ::google::protobuf::internal::WireFormatLite::
                   InternalWriteMessageNoVirtualToArray(
                       102, *entry, deterministic, target);
;
      }
    }
  }

  if (_internal_metadata_.have_unknown_fields()) {
    target = ::google::protobuf::internal::WireFormat::SerializeUnknownFieldsToArray(
        unknown_fields(), target);
  }
  // @@protoc_insertion_point(serialize_to_array_end:protobuf_unittest.TestEnumMap)
  return target;
}

size_t TestEnumMap::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:protobuf_unittest.TestEnumMap)
  size_t total_size = 0;

  // map<int32, .protobuf_unittest.Proto2MapEnum> known_map_field = 101;
  total_size += 2 *
      ::google::protobuf::internal::FromIntSize(this->known_map_field_size());
  {
    ::google::protobuf::scoped_ptr<TestEnumMap_KnownMapFieldEntry> entry;
    for (::google::protobuf::Map< ::google::protobuf::int32, ::protobuf_unittest::Proto2MapEnum >::const_iterator
        it = this->known_map_field().begin();
        it != this->known_map_field().end(); ++it) {
      entry.reset(known_map_field_.NewEnumEntryWrapper(it->first, it->second));
      total_size += ::google::protobuf::internal::WireFormatLite::
          MessageSizeNoVirtual(*entry);
    }
  }

  // map<int32, .protobuf_unittest.Proto2MapEnum> unknown_map_field = 102;
  total_size += 2 *
      ::google::protobuf::internal::FromIntSize(this->unknown_map_field_size());
  {
    ::google::protobuf::scoped_ptr<TestEnumMap_UnknownMapFieldEntry> entry;
    for (::google::protobuf::Map< ::google::protobuf::int32, ::protobuf_unittest::Proto2MapEnum >::const_iterator
        it = this->unknown_map_field().begin();
        it != this->unknown_map_field().end(); ++it) {
      entry.reset(unknown_map_field_.NewEnumEntryWrapper(it->first, it->second));
      total_size += ::google::protobuf::internal::WireFormatLite::
          MessageSizeNoVirtual(*entry);
    }
  }

  if (_internal_metadata_.have_unknown_fields()) {
    total_size +=
      ::google::protobuf::internal::WireFormat::ComputeUnknownFieldsSize(
        unknown_fields());
  }
  int cached_size = ::google::protobuf::internal::ToCachedSize(total_size);
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = cached_size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
  return total_size;
}

void TestEnumMap::MergeFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_merge_from_start:protobuf_unittest.TestEnumMap)
  if (GOOGLE_PREDICT_FALSE(&from == this)) MergeFromFail(__LINE__);
  const TestEnumMap* source =
      ::google::protobuf::internal::DynamicCastToGenerated<const TestEnumMap>(
          &from);
  if (source == NULL) {
  // @@protoc_insertion_point(generalized_merge_from_cast_fail:protobuf_unittest.TestEnumMap)
    ::google::protobuf::internal::ReflectionOps::Merge(from, this);
  } else {
  // @@protoc_insertion_point(generalized_merge_from_cast_success:protobuf_unittest.TestEnumMap)
    UnsafeMergeFrom(*source);
  }
}

void TestEnumMap::MergeFrom(const TestEnumMap& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:protobuf_unittest.TestEnumMap)
  if (GOOGLE_PREDICT_TRUE(&from != this)) {
    UnsafeMergeFrom(from);
  } else {
    MergeFromFail(__LINE__);
  }
}

void TestEnumMap::UnsafeMergeFrom(const TestEnumMap& from) {
  GOOGLE_DCHECK(&from != this);
  known_map_field_.MergeFrom(from.known_map_field_);
  unknown_map_field_.MergeFrom(from.unknown_map_field_);
  if (from._internal_metadata_.have_unknown_fields()) {
    ::google::protobuf::UnknownFieldSet::MergeToInternalMetdata(
      from.unknown_fields(), &_internal_metadata_);
  }
}

void TestEnumMap::CopyFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_copy_from_start:protobuf_unittest.TestEnumMap)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

void TestEnumMap::CopyFrom(const TestEnumMap& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:protobuf_unittest.TestEnumMap)
  if (&from == this) return;
  Clear();
  UnsafeMergeFrom(from);
}

bool TestEnumMap::IsInitialized() const {

  return true;
}

void TestEnumMap::Swap(TestEnumMap* other) {
  if (other == this) return;
  InternalSwap(other);
}
void TestEnumMap::InternalSwap(TestEnumMap* other) {
  known_map_field_.Swap(&other->known_map_field_);
  unknown_map_field_.Swap(&other->unknown_map_field_);
  std::swap(_has_bits_[0], other->_has_bits_[0]);
  _internal_metadata_.Swap(&other->_internal_metadata_);
  std::swap(_cached_size_, other->_cached_size_);
}

::google::protobuf::Metadata TestEnumMap::GetMetadata() const {
  protobuf_AssignDescriptorsOnce();
  ::google::protobuf::Metadata metadata;
  metadata.descriptor = TestEnumMap_descriptor_;
  metadata.reflection = TestEnumMap_reflection_;
  return metadata;
}

#if PROTOBUF_INLINE_NOT_IN_HEADERS
// TestEnumMap

// map<int32, .protobuf_unittest.Proto2MapEnum> known_map_field = 101;
int TestEnumMap::known_map_field_size() const {
  return known_map_field_.size();
}
void TestEnumMap::clear_known_map_field() {
  known_map_field_.Clear();
}
 const ::google::protobuf::Map< ::google::protobuf::int32, ::protobuf_unittest::Proto2MapEnum >&
TestEnumMap::known_map_field() const {
  // @@protoc_insertion_point(field_map:protobuf_unittest.TestEnumMap.known_map_field)
  return known_map_field_.GetMap();
}
 ::google::protobuf::Map< ::google::protobuf::int32, ::protobuf_unittest::Proto2MapEnum >*
TestEnumMap::mutable_known_map_field() {
  // @@protoc_insertion_point(field_mutable_map:protobuf_unittest.TestEnumMap.known_map_field)
  return known_map_field_.MutableMap();
}

// map<int32, .protobuf_unittest.Proto2MapEnum> unknown_map_field = 102;
int TestEnumMap::unknown_map_field_size() const {
  return unknown_map_field_.size();
}
void TestEnumMap::clear_unknown_map_field() {
  unknown_map_field_.Clear();
}
 const ::google::protobuf::Map< ::google::protobuf::int32, ::protobuf_unittest::Proto2MapEnum >&
TestEnumMap::unknown_map_field() const {
  // @@protoc_insertion_point(field_map:protobuf_unittest.TestEnumMap.unknown_map_field)
  return unknown_map_field_.GetMap();
}
 ::google::protobuf::Map< ::google::protobuf::int32, ::protobuf_unittest::Proto2MapEnum >*
TestEnumMap::mutable_unknown_map_field() {
  // @@protoc_insertion_point(field_mutable_map:protobuf_unittest.TestEnumMap.unknown_map_field)
  return unknown_map_field_.MutableMap();
}

inline const TestEnumMap* TestEnumMap::internal_default_instance() {
  return &TestEnumMap_default_instance_.get();
}
#endif  // PROTOBUF_INLINE_NOT_IN_HEADERS

// ===================================================================

#if !defined(_MSC_VER) || _MSC_VER >= 1900
const int TestEnumMapPlusExtra::kKnownMapFieldFieldNumber;
const int TestEnumMapPlusExtra::kUnknownMapFieldFieldNumber;
#endif  // !defined(_MSC_VER) || _MSC_VER >= 1900

TestEnumMapPlusExtra::TestEnumMapPlusExtra()
  : ::google::protobuf::Message(), _internal_metadata_(NULL) {
  if (this != internal_default_instance()) protobuf_InitDefaults_google_2fprotobuf_2fmap_5fproto2_5funittest_2eproto();
  SharedCtor();
  // @@protoc_insertion_point(constructor:protobuf_unittest.TestEnumMapPlusExtra)
}

void TestEnumMapPlusExtra::InitAsDefaultInstance() {
}

TestEnumMapPlusExtra::TestEnumMapPlusExtra(const TestEnumMapPlusExtra& from)
  : ::google::protobuf::Message(),
    _internal_metadata_(NULL) {
  SharedCtor();
  UnsafeMergeFrom(from);
  // @@protoc_insertion_point(copy_constructor:protobuf_unittest.TestEnumMapPlusExtra)
}

void TestEnumMapPlusExtra::SharedCtor() {
  _cached_size_ = 0;
  known_map_field_.SetAssignDescriptorCallback(
      protobuf_AssignDescriptorsOnce);
  known_map_field_.SetEntryDescriptor(
      &::protobuf_unittest::TestEnumMapPlusExtra_KnownMapFieldEntry_descriptor_);
  unknown_map_field_.SetAssignDescriptorCallback(
      protobuf_AssignDescriptorsOnce);
  unknown_map_field_.SetEntryDescriptor(
      &::protobuf_unittest::TestEnumMapPlusExtra_UnknownMapFieldEntry_descriptor_);
}

TestEnumMapPlusExtra::~TestEnumMapPlusExtra() {
  // @@protoc_insertion_point(destructor:protobuf_unittest.TestEnumMapPlusExtra)
  SharedDtor();
}

void TestEnumMapPlusExtra::SharedDtor() {
}

void TestEnumMapPlusExtra::SetCachedSize(int size) const {
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
}
const ::google::protobuf::Descriptor* TestEnumMapPlusExtra::descriptor() {
  protobuf_AssignDescriptorsOnce();
  return TestEnumMapPlusExtra_descriptor_;
}

const TestEnumMapPlusExtra& TestEnumMapPlusExtra::default_instance() {
  protobuf_InitDefaults_google_2fprotobuf_2fmap_5fproto2_5funittest_2eproto();
  return *internal_default_instance();
}

::google::protobuf::internal::ExplicitlyConstructed<TestEnumMapPlusExtra> TestEnumMapPlusExtra_default_instance_;

TestEnumMapPlusExtra* TestEnumMapPlusExtra::New(::google::protobuf::Arena* arena) const {
  TestEnumMapPlusExtra* n = new TestEnumMapPlusExtra;
  if (arena != NULL) {
    arena->Own(n);
  }
  return n;
}

void TestEnumMapPlusExtra::Clear() {
// @@protoc_insertion_point(message_clear_start:protobuf_unittest.TestEnumMapPlusExtra)
  known_map_field_.Clear();
  unknown_map_field_.Clear();
  _has_bits_.Clear();
  if (_internal_metadata_.have_unknown_fields()) {
    mutable_unknown_fields()->Clear();
  }
}

bool TestEnumMapPlusExtra::MergePartialFromCodedStream(
    ::google::protobuf::io::CodedInputStream* input) {
#define DO_(EXPRESSION) if (!GOOGLE_PREDICT_TRUE(EXPRESSION)) goto failure
  ::google::protobuf::uint32 tag;
  // @@protoc_insertion_point(parse_start:protobuf_unittest.TestEnumMapPlusExtra)
  for (;;) {
    ::std::pair< ::google::protobuf::uint32, bool> p = input->ReadTagWithCutoff(16383);
    tag = p.first;
    if (!p.second) goto handle_unusual;
    switch (::google::protobuf::internal::WireFormatLite::GetTagFieldNumber(tag)) {
      // map<int32, .protobuf_unittest.Proto2MapEnumPlusExtra> known_map_field = 101;
      case 101: {
        if (tag == 810) {
          DO_(input->IncrementRecursionDepth());
         parse_loop_known_map_field:
          ::google::protobuf::scoped_ptr<TestEnumMapPlusExtra_KnownMapFieldEntry> entry(known_map_field_.NewEntry());
          {
            ::std::string data;
            DO_(::google::protobuf::internal::WireFormatLite::ReadString(input, &data));
            DO_(entry->ParseFromString(data));
            if (::protobuf_unittest::Proto2MapEnumPlusExtra_IsValid(*entry->mutable_value())) {
              (*mutable_known_map_field())[entry->key()] =
                  static_cast< ::protobuf_unittest::Proto2MapEnumPlusExtra >(*entry->mutable_value());
            } else {
              mutable_unknown_fields()->AddLengthDelimited(101, data);
            }
          }
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(810)) goto parse_loop_known_map_field;
        if (input->ExpectTag(818)) goto parse_loop_unknown_map_field;
        input->UnsafeDecrementRecursionDepth();
        break;
      }

      // map<int32, .protobuf_unittest.Proto2MapEnumPlusExtra> unknown_map_field = 102;
      case 102: {
        if (tag == 818) {
          DO_(input->IncrementRecursionDepth());
         parse_loop_unknown_map_field:
          ::google::protobuf::scoped_ptr<TestEnumMapPlusExtra_UnknownMapFieldEntry> entry(unknown_map_field_.NewEntry());
          {
            ::std::string data;
            DO_(::google::protobuf::internal::WireFormatLite::ReadString(input, &data));
            DO_(entry->ParseFromString(data));
            if (::protobuf_unittest::Proto2MapEnumPlusExtra_IsValid(*entry->mutable_value())) {
              (*mutable_unknown_map_field())[entry->key()] =
                  static_cast< ::protobuf_unittest::Proto2MapEnumPlusExtra >(*entry->mutable_value());
            } else {
              mutable_unknown_fields()->AddLengthDelimited(102, data);
            }
          }
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(818)) goto parse_loop_unknown_map_field;
        input->UnsafeDecrementRecursionDepth();
        if (input->ExpectAtEnd()) goto success;
        break;
      }

      default: {
      handle_unusual:
        if (tag == 0 ||
            ::google::protobuf::internal::WireFormatLite::GetTagWireType(tag) ==
            ::google::protobuf::internal::WireFormatLite::WIRETYPE_END_GROUP) {
          goto success;
        }
        DO_(::google::protobuf::internal::WireFormat::SkipField(
              input, tag, mutable_unknown_fields()));
        break;
      }
    }
  }
success:
  // @@protoc_insertion_point(parse_success:protobuf_unittest.TestEnumMapPlusExtra)
  return true;
failure:
  // @@protoc_insertion_point(parse_failure:protobuf_unittest.TestEnumMapPlusExtra)
  return false;
#undef DO_
}

void TestEnumMapPlusExtra::SerializeWithCachedSizes(
    ::google::protobuf::io::CodedOutputStream* output) const {
  // @@protoc_insertion_point(serialize_start:protobuf_unittest.TestEnumMapPlusExtra)
  // map<int32, .protobuf_unittest.Proto2MapEnumPlusExtra> known_map_field = 101;
  if (!this->known_map_field().empty()) {
    typedef ::google::protobuf::Map< ::google::protobuf::int32, ::protobuf_unittest::Proto2MapEnumPlusExtra >::const_pointer
        ConstPtr;
    typedef ::google::protobuf::internal::SortItem< ::google::protobuf::int32, ConstPtr > SortItem;
    typedef ::google::protobuf::internal::CompareByFirstField<SortItem> Less;

    if (output->IsSerializationDeterminstic() &&
        this->known_map_field().size() > 1) {
      ::google::protobuf::scoped_array<SortItem> items(
          new SortItem[this->known_map_field().size()]);
      typedef ::google::protobuf::Map< ::google::protobuf::int32, ::protobuf_unittest::Proto2MapEnumPlusExtra >::size_type size_type;
      size_type n = 0;
      for (::google::protobuf::Map< ::google::protobuf::int32, ::protobuf_unittest::Proto2MapEnumPlusExtra >::const_iterator
          it = this->known_map_field().begin();
          it != this->known_map_field().end(); ++it, ++n) {
        items[n] = SortItem(&*it);
      }
      ::std::sort(&items[0], &items[n], Less());
      ::google::protobuf::scoped_ptr<TestEnumMapPlusExtra_KnownMapFieldEntry> entry;
      for (size_type i = 0; i < n; i++) {
        entry.reset(known_map_field_.NewEnumEntryWrapper(
            items[i].second->first, items[i].second->second));
        ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
            101, *entry, output);
      }
    } else {
      ::google::protobuf::scoped_ptr<TestEnumMapPlusExtra_KnownMapFieldEntry> entry;
      for (::google::protobuf::Map< ::google::protobuf::int32, ::protobuf_unittest::Proto2MapEnumPlusExtra >::const_iterator
          it = this->known_map_field().begin();
          it != this->known_map_field().end(); ++it) {
        entry.reset(known_map_field_.NewEnumEntryWrapper(
            it->first, it->second));
        ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
            101, *entry, output);
      }
    }
  }

  // map<int32, .protobuf_unittest.Proto2MapEnumPlusExtra> unknown_map_field = 102;
  if (!this->unknown_map_field().empty()) {
    typedef ::google::protobuf::Map< ::google::protobuf::int32, ::protobuf_unittest::Proto2MapEnumPlusExtra >::const_pointer
        ConstPtr;
    typedef ::google::protobuf::internal::SortItem< ::google::protobuf::int32, ConstPtr > SortItem;
    typedef ::google::protobuf::internal::CompareByFirstField<SortItem> Less;

    if (output->IsSerializationDeterminstic() &&
        this->unknown_map_field().size() > 1) {
      ::google::protobuf::scoped_array<SortItem> items(
          new SortItem[this->unknown_map_field().size()]);
      typedef ::google::protobuf::Map< ::google::protobuf::int32, ::protobuf_unittest::Proto2MapEnumPlusExtra >::size_type size_type;
      size_type n = 0;
      for (::google::protobuf::Map< ::google::protobuf::int32, ::protobuf_unittest::Proto2MapEnumPlusExtra >::const_iterator
          it = this->unknown_map_field().begin();
          it != this->unknown_map_field().end(); ++it, ++n) {
        items[n] = SortItem(&*it);
      }
      ::std::sort(&items[0], &items[n], Less());
      ::google::protobuf::scoped_ptr<TestEnumMapPlusExtra_UnknownMapFieldEntry> entry;
      for (size_type i = 0; i < n; i++) {
        entry.reset(unknown_map_field_.NewEnumEntryWrapper(
            items[i].second->first, items[i].second->second));
        ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
            102, *entry, output);
      }
    } else {
      ::google::protobuf::scoped_ptr<TestEnumMapPlusExtra_UnknownMapFieldEntry> entry;
      for (::google::protobuf::Map< ::google::protobuf::int32, ::protobuf_unittest::Proto2MapEnumPlusExtra >::const_iterator
          it = this->unknown_map_field().begin();
          it != this->unknown_map_field().end(); ++it) {
        entry.reset(unknown_map_field_.NewEnumEntryWrapper(
            it->first, it->second));
        ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
            102, *entry, output);
      }
    }
  }

  if (_internal_metadata_.have_unknown_fields()) {
    ::google::protobuf::internal::WireFormat::SerializeUnknownFields(
        unknown_fields(), output);
  }
  // @@protoc_insertion_point(serialize_end:protobuf_unittest.TestEnumMapPlusExtra)
}

::google::protobuf::uint8* TestEnumMapPlusExtra::InternalSerializeWithCachedSizesToArray(
    bool deterministic, ::google::protobuf::uint8* target) const {
  (void)deterministic; // Unused
  // @@protoc_insertion_point(serialize_to_array_start:protobuf_unittest.TestEnumMapPlusExtra)
  // map<int32, .protobuf_unittest.Proto2MapEnumPlusExtra> known_map_field = 101;
  if (!this->known_map_field().empty()) {
    typedef ::google::protobuf::Map< ::google::protobuf::int32, ::protobuf_unittest::Proto2MapEnumPlusExtra >::const_pointer
        ConstPtr;
    typedef ::google::protobuf::internal::SortItem< ::google::protobuf::int32, ConstPtr > SortItem;
    typedef ::google::protobuf::internal::CompareByFirstField<SortItem> Less;

    if (deterministic &&
        this->known_map_field().size() > 1) {
      ::google::protobuf::scoped_array<SortItem> items(
          new SortItem[this->known_map_field().size()]);
      typedef ::google::protobuf::Map< ::google::protobuf::int32, ::protobuf_unittest::Proto2MapEnumPlusExtra >::size_type size_type;
      size_type n = 0;
      for (::google::protobuf::Map< ::google::protobuf::int32, ::protobuf_unittest::Proto2MapEnumPlusExtra >::const_iterator
          it = this->known_map_field().begin();
          it != this->known_map_field().end(); ++it, ++n) {
        items[n] = SortItem(&*it);
      }
      ::std::sort(&items[0], &items[n], Less());
      ::google::protobuf::scoped_ptr<TestEnumMapPlusExtra_KnownMapFieldEntry> entry;
      for (size_type i = 0; i < n; i++) {
        entry.reset(known_map_field_.NewEnumEntryWrapper(
            items[i].second->first, items[i].second->second));
        target = ::google::protobuf::internal::WireFormatLite::
                   InternalWriteMessageNoVirtualToArray(
                       101, *entry, deterministic, target);
;
      }
    } else {
      ::google::protobuf::scoped_ptr<TestEnumMapPlusExtra_KnownMapFieldEntry> entry;
      for (::google::protobuf::Map< ::google::protobuf::int32, ::protobuf_unittest::Proto2MapEnumPlusExtra >::const_iterator
          it = this->known_map_field().begin();
          it != this->known_map_field().end(); ++it) {
        entry.reset(known_map_field_.NewEnumEntryWrapper(
            it->first, it->second));
        target = ::google::protobuf::internal::WireFormatLite::
                   InternalWriteMessageNoVirtualToArray(
                       101, *entry, deterministic, target);
;
      }
    }
  }

  // map<int32, .protobuf_unittest.Proto2MapEnumPlusExtra> unknown_map_field = 102;
  if (!this->unknown_map_field().empty()) {
    typedef ::google::protobuf::Map< ::google::protobuf::int32, ::protobuf_unittest::Proto2MapEnumPlusExtra >::const_pointer
        ConstPtr;
    typedef ::google::protobuf::internal::SortItem< ::google::protobuf::int32, ConstPtr > SortItem;
    typedef ::google::protobuf::internal::CompareByFirstField<SortItem> Less;

    if (deterministic &&
        this->unknown_map_field().size() > 1) {
      ::google::protobuf::scoped_array<SortItem> items(
          new SortItem[this->unknown_map_field().size()]);
      typedef ::google::protobuf::Map< ::google::protobuf::int32, ::protobuf_unittest::Proto2MapEnumPlusExtra >::size_type size_type;
      size_type n = 0;
      for (::google::protobuf::Map< ::google::protobuf::int32, ::protobuf_unittest::Proto2MapEnumPlusExtra >::const_iterator
          it = this->unknown_map_field().begin();
          it != this->unknown_map_field().end(); ++it, ++n) {
        items[n] = SortItem(&*it);
      }
      ::std::sort(&items[0], &items[n], Less());
      ::google::protobuf::scoped_ptr<TestEnumMapPlusExtra_UnknownMapFieldEntry> entry;
      for (size_type i = 0; i < n; i++) {
        entry.reset(unknown_map_field_.NewEnumEntryWrapper(
            items[i].second->first, items[i].second->second));
        target = ::google::protobuf::internal::WireFormatLite::
                   InternalWriteMessageNoVirtualToArray(
                       102, *entry, deterministic, target);
;
      }
    } else {
      ::google::protobuf::scoped_ptr<TestEnumMapPlusExtra_UnknownMapFieldEntry> entry;
      for (::google::protobuf::Map< ::google::protobuf::int32, ::protobuf_unittest::Proto2MapEnumPlusExtra >::const_iterator
          it = this->unknown_map_field().begin();
          it != this->unknown_map_field().end(); ++it) {
        entry.reset(unknown_map_field_.NewEnumEntryWrapper(
            it->first, it->second));
        target = ::google::protobuf::internal::WireFormatLite::
                   InternalWriteMessageNoVirtualToArray(
                       102, *entry, deterministic, target);
;
      }
    }
  }

  if (_internal_metadata_.have_unknown_fields()) {
    target = ::google::protobuf::internal::WireFormat::SerializeUnknownFieldsToArray(
        unknown_fields(), target);
  }
  // @@protoc_insertion_point(serialize_to_array_end:protobuf_unittest.TestEnumMapPlusExtra)
  return target;
}

size_t TestEnumMapPlusExtra::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:protobuf_unittest.TestEnumMapPlusExtra)
  size_t total_size = 0;

  // map<int32, .protobuf_unittest.Proto2MapEnumPlusExtra> known_map_field = 101;
  total_size += 2 *
      ::google::protobuf::internal::FromIntSize(this->known_map_field_size());
  {
    ::google::protobuf::scoped_ptr<TestEnumMapPlusExtra_KnownMapFieldEntry> entry;
    for (::google::protobuf::Map< ::google::protobuf::int32, ::protobuf_unittest::Proto2MapEnumPlusExtra >::const_iterator
        it = this->known_map_field().begin();
        it != this->known_map_field().end(); ++it) {
      entry.reset(known_map_field_.NewEnumEntryWrapper(it->first, it->second));
      total_size += ::google::protobuf::internal::WireFormatLite::
          MessageSizeNoVirtual(*entry);
    }
  }

  // map<int32, .protobuf_unittest.Proto2MapEnumPlusExtra> unknown_map_field = 102;
  total_size += 2 *
      ::google::protobuf::internal::FromIntSize(this->unknown_map_field_size());
  {
    ::google::protobuf::scoped_ptr<TestEnumMapPlusExtra_UnknownMapFieldEntry> entry;
    for (::google::protobuf::Map< ::google::protobuf::int32, ::protobuf_unittest::Proto2MapEnumPlusExtra >::const_iterator
        it = this->unknown_map_field().begin();
        it != this->unknown_map_field().end(); ++it) {
      entry.reset(unknown_map_field_.NewEnumEntryWrapper(it->first, it->second));
      total_size += ::google::protobuf::internal::WireFormatLite::
          MessageSizeNoVirtual(*entry);
    }
  }

  if (_internal_metadata_.have_unknown_fields()) {
    total_size +=
      ::google::protobuf::internal::WireFormat::ComputeUnknownFieldsSize(
        unknown_fields());
  }
  int cached_size = ::google::protobuf::internal::ToCachedSize(total_size);
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = cached_size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
  return total_size;
}

void TestEnumMapPlusExtra::MergeFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_merge_from_start:protobuf_unittest.TestEnumMapPlusExtra)
  if (GOOGLE_PREDICT_FALSE(&from == this)) MergeFromFail(__LINE__);
  const TestEnumMapPlusExtra* source =
      ::google::protobuf::internal::DynamicCastToGenerated<const TestEnumMapPlusExtra>(
          &from);
  if (source == NULL) {
  // @@protoc_insertion_point(generalized_merge_from_cast_fail:protobuf_unittest.TestEnumMapPlusExtra)
    ::google::protobuf::internal::ReflectionOps::Merge(from, this);
  } else {
  // @@protoc_insertion_point(generalized_merge_from_cast_success:protobuf_unittest.TestEnumMapPlusExtra)
    UnsafeMergeFrom(*source);
  }
}

void TestEnumMapPlusExtra::MergeFrom(const TestEnumMapPlusExtra& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:protobuf_unittest.TestEnumMapPlusExtra)
  if (GOOGLE_PREDICT_TRUE(&from != this)) {
    UnsafeMergeFrom(from);
  } else {
    MergeFromFail(__LINE__);
  }
}

void TestEnumMapPlusExtra::UnsafeMergeFrom(const TestEnumMapPlusExtra& from) {
  GOOGLE_DCHECK(&from != this);
  known_map_field_.MergeFrom(from.known_map_field_);
  unknown_map_field_.MergeFrom(from.unknown_map_field_);
  if (from._internal_metadata_.have_unknown_fields()) {
    ::google::protobuf::UnknownFieldSet::MergeToInternalMetdata(
      from.unknown_fields(), &_internal_metadata_);
  }
}

void TestEnumMapPlusExtra::CopyFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_copy_from_start:protobuf_unittest.TestEnumMapPlusExtra)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

void TestEnumMapPlusExtra::CopyFrom(const TestEnumMapPlusExtra& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:protobuf_unittest.TestEnumMapPlusExtra)
  if (&from == this) return;
  Clear();
  UnsafeMergeFrom(from);
}

bool TestEnumMapPlusExtra::IsInitialized() const {

  return true;
}

void TestEnumMapPlusExtra::Swap(TestEnumMapPlusExtra* other) {
  if (other == this) return;
  InternalSwap(other);
}
void TestEnumMapPlusExtra::InternalSwap(TestEnumMapPlusExtra* other) {
  known_map_field_.Swap(&other->known_map_field_);
  unknown_map_field_.Swap(&other->unknown_map_field_);
  std::swap(_has_bits_[0], other->_has_bits_[0]);
  _internal_metadata_.Swap(&other->_internal_metadata_);
  std::swap(_cached_size_, other->_cached_size_);
}

::google::protobuf::Metadata TestEnumMapPlusExtra::GetMetadata() const {
  protobuf_AssignDescriptorsOnce();
  ::google::protobuf::Metadata metadata;
  metadata.descriptor = TestEnumMapPlusExtra_descriptor_;
  metadata.reflection = TestEnumMapPlusExtra_reflection_;
  return metadata;
}

#if PROTOBUF_INLINE_NOT_IN_HEADERS
// TestEnumMapPlusExtra

// map<int32, .protobuf_unittest.Proto2MapEnumPlusExtra> known_map_field = 101;
int TestEnumMapPlusExtra::known_map_field_size() const {
  return known_map_field_.size();
}
void TestEnumMapPlusExtra::clear_known_map_field() {
  known_map_field_.Clear();
}
 const ::google::protobuf::Map< ::google::protobuf::int32, ::protobuf_unittest::Proto2MapEnumPlusExtra >&
TestEnumMapPlusExtra::known_map_field() const {
  // @@protoc_insertion_point(field_map:protobuf_unittest.TestEnumMapPlusExtra.known_map_field)
  return known_map_field_.GetMap();
}
 ::google::protobuf::Map< ::google::protobuf::int32, ::protobuf_unittest::Proto2MapEnumPlusExtra >*
TestEnumMapPlusExtra::mutable_known_map_field() {
  // @@protoc_insertion_point(field_mutable_map:protobuf_unittest.TestEnumMapPlusExtra.known_map_field)
  return known_map_field_.MutableMap();
}

// map<int32, .protobuf_unittest.Proto2MapEnumPlusExtra> unknown_map_field = 102;
int TestEnumMapPlusExtra::unknown_map_field_size() const {
  return unknown_map_field_.size();
}
void TestEnumMapPlusExtra::clear_unknown_map_field() {
  unknown_map_field_.Clear();
}
 const ::google::protobuf::Map< ::google::protobuf::int32, ::protobuf_unittest::Proto2MapEnumPlusExtra >&
TestEnumMapPlusExtra::unknown_map_field() const {
  // @@protoc_insertion_point(field_map:protobuf_unittest.TestEnumMapPlusExtra.unknown_map_field)
  return unknown_map_field_.GetMap();
}
 ::google::protobuf::Map< ::google::protobuf::int32, ::protobuf_unittest::Proto2MapEnumPlusExtra >*
TestEnumMapPlusExtra::mutable_unknown_map_field() {
  // @@protoc_insertion_point(field_mutable_map:protobuf_unittest.TestEnumMapPlusExtra.unknown_map_field)
  return unknown_map_field_.MutableMap();
}

inline const TestEnumMapPlusExtra* TestEnumMapPlusExtra::internal_default_instance() {
  return &TestEnumMapPlusExtra_default_instance_.get();
}
#endif  // PROTOBUF_INLINE_NOT_IN_HEADERS

// ===================================================================

#if !defined(_MSC_VER) || _MSC_VER >= 1900
const int TestImportEnumMap::kImportEnumAmpFieldNumber;
#endif  // !defined(_MSC_VER) || _MSC_VER >= 1900

TestImportEnumMap::TestImportEnumMap()
  : ::google::protobuf::Message(), _internal_metadata_(NULL) {
  if (this != internal_default_instance()) protobuf_InitDefaults_google_2fprotobuf_2fmap_5fproto2_5funittest_2eproto();
  SharedCtor();
  // @@protoc_insertion_point(constructor:protobuf_unittest.TestImportEnumMap)
}

void TestImportEnumMap::InitAsDefaultInstance() {
}

TestImportEnumMap::TestImportEnumMap(const TestImportEnumMap& from)
  : ::google::protobuf::Message(),
    _internal_metadata_(NULL) {
  SharedCtor();
  UnsafeMergeFrom(from);
  // @@protoc_insertion_point(copy_constructor:protobuf_unittest.TestImportEnumMap)
}

void TestImportEnumMap::SharedCtor() {
  _cached_size_ = 0;
  import_enum_amp_.SetAssignDescriptorCallback(
      protobuf_AssignDescriptorsOnce);
  import_enum_amp_.SetEntryDescriptor(
      &::protobuf_unittest::TestImportEnumMap_ImportEnumAmpEntry_descriptor_);
}

TestImportEnumMap::~TestImportEnumMap() {
  // @@protoc_insertion_point(destructor:protobuf_unittest.TestImportEnumMap)
  SharedDtor();
}

void TestImportEnumMap::SharedDtor() {
}

void TestImportEnumMap::SetCachedSize(int size) const {
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
}
const ::google::protobuf::Descriptor* TestImportEnumMap::descriptor() {
  protobuf_AssignDescriptorsOnce();
  return TestImportEnumMap_descriptor_;
}

const TestImportEnumMap& TestImportEnumMap::default_instance() {
  protobuf_InitDefaults_google_2fprotobuf_2fmap_5fproto2_5funittest_2eproto();
  return *internal_default_instance();
}

::google::protobuf::internal::ExplicitlyConstructed<TestImportEnumMap> TestImportEnumMap_default_instance_;

TestImportEnumMap* TestImportEnumMap::New(::google::protobuf::Arena* arena) const {
  TestImportEnumMap* n = new TestImportEnumMap;
  if (arena != NULL) {
    arena->Own(n);
  }
  return n;
}

void TestImportEnumMap::Clear() {
// @@protoc_insertion_point(message_clear_start:protobuf_unittest.TestImportEnumMap)
  import_enum_amp_.Clear();
  _has_bits_.Clear();
  if (_internal_metadata_.have_unknown_fields()) {
    mutable_unknown_fields()->Clear();
  }
}

bool TestImportEnumMap::MergePartialFromCodedStream(
    ::google::protobuf::io::CodedInputStream* input) {
#define DO_(EXPRESSION) if (!GOOGLE_PREDICT_TRUE(EXPRESSION)) goto failure
  ::google::protobuf::uint32 tag;
  // @@protoc_insertion_point(parse_start:protobuf_unittest.TestImportEnumMap)
  for (;;) {
    ::std::pair< ::google::protobuf::uint32, bool> p = input->ReadTagWithCutoff(127);
    tag = p.first;
    if (!p.second) goto handle_unusual;
    switch (::google::protobuf::internal::WireFormatLite::GetTagFieldNumber(tag)) {
      // map<int32, .protobuf_unittest_import.ImportEnumForMap> import_enum_amp = 1;
      case 1: {
        if (tag == 10) {
          DO_(input->IncrementRecursionDepth());
         parse_loop_import_enum_amp:
          ::google::protobuf::scoped_ptr<TestImportEnumMap_ImportEnumAmpEntry> entry(import_enum_amp_.NewEntry());
          {
            ::std::string data;
            DO_(::google::protobuf::internal::WireFormatLite::ReadString(input, &data));
            DO_(entry->ParseFromString(data));
            if (::protobuf_unittest_import::ImportEnumForMap_IsValid(*entry->mutable_value())) {
              (*mutable_import_enum_amp())[entry->key()] =
                  static_cast< ::protobuf_unittest_import::ImportEnumForMap >(*entry->mutable_value());
            } else {
              mutable_unknown_fields()->AddLengthDelimited(1, data);
            }
          }
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(10)) goto parse_loop_import_enum_amp;
        input->UnsafeDecrementRecursionDepth();
        if (input->ExpectAtEnd()) goto success;
        break;
      }

      default: {
      handle_unusual:
        if (tag == 0 ||
            ::google::protobuf::internal::WireFormatLite::GetTagWireType(tag) ==
            ::google::protobuf::internal::WireFormatLite::WIRETYPE_END_GROUP) {
          goto success;
        }
        DO_(::google::protobuf::internal::WireFormat::SkipField(
              input, tag, mutable_unknown_fields()));
        break;
      }
    }
  }
success:
  // @@protoc_insertion_point(parse_success:protobuf_unittest.TestImportEnumMap)
  return true;
failure:
  // @@protoc_insertion_point(parse_failure:protobuf_unittest.TestImportEnumMap)
  return false;
#undef DO_
}

void TestImportEnumMap::SerializeWithCachedSizes(
    ::google::protobuf::io::CodedOutputStream* output) const {
  // @@protoc_insertion_point(serialize_start:protobuf_unittest.TestImportEnumMap)
  // map<int32, .protobuf_unittest_import.ImportEnumForMap> import_enum_amp = 1;
  if (!this->import_enum_amp().empty()) {
    typedef ::google::protobuf::Map< ::google::protobuf::int32, ::protobuf_unittest_import::ImportEnumForMap >::const_pointer
        ConstPtr;
    typedef ::google::protobuf::internal::SortItem< ::google::protobuf::int32, ConstPtr > SortItem;
    typedef ::google::protobuf::internal::CompareByFirstField<SortItem> Less;

    if (output->IsSerializationDeterminstic() &&
        this->import_enum_amp().size() > 1) {
      ::google::protobuf::scoped_array<SortItem> items(
          new SortItem[this->import_enum_amp().size()]);
      typedef ::google::protobuf::Map< ::google::protobuf::int32, ::protobuf_unittest_import::ImportEnumForMap >::size_type size_type;
      size_type n = 0;
      for (::google::protobuf::Map< ::google::protobuf::int32, ::protobuf_unittest_import::ImportEnumForMap >::const_iterator
          it = this->import_enum_amp().begin();
          it != this->import_enum_amp().end(); ++it, ++n) {
        items[n] = SortItem(&*it);
      }
      ::std::sort(&items[0], &items[n], Less());
      ::google::protobuf::scoped_ptr<TestImportEnumMap_ImportEnumAmpEntry> entry;
      for (size_type i = 0; i < n; i++) {
        entry.reset(import_enum_amp_.NewEnumEntryWrapper(
            items[i].second->first, items[i].second->second));
        ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
            1, *entry, output);
      }
    } else {
      ::google::protobuf::scoped_ptr<TestImportEnumMap_ImportEnumAmpEntry> entry;
      for (::google::protobuf::Map< ::google::protobuf::int32, ::protobuf_unittest_import::ImportEnumForMap >::const_iterator
          it = this->import_enum_amp().begin();
          it != this->import_enum_amp().end(); ++it) {
        entry.reset(import_enum_amp_.NewEnumEntryWrapper(
            it->first, it->second));
        ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
            1, *entry, output);
      }
    }
  }

  if (_internal_metadata_.have_unknown_fields()) {
    ::google::protobuf::internal::WireFormat::SerializeUnknownFields(
        unknown_fields(), output);
  }
  // @@protoc_insertion_point(serialize_end:protobuf_unittest.TestImportEnumMap)
}

::google::protobuf::uint8* TestImportEnumMap::InternalSerializeWithCachedSizesToArray(
    bool deterministic, ::google::protobuf::uint8* target) const {
  (void)deterministic; // Unused
  // @@protoc_insertion_point(serialize_to_array_start:protobuf_unittest.TestImportEnumMap)
  // map<int32, .protobuf_unittest_import.ImportEnumForMap> import_enum_amp = 1;
  if (!this->import_enum_amp().empty()) {
    typedef ::google::protobuf::Map< ::google::protobuf::int32, ::protobuf_unittest_import::ImportEnumForMap >::const_pointer
        ConstPtr;
    typedef ::google::protobuf::internal::SortItem< ::google::protobuf::int32, ConstPtr > SortItem;
    typedef ::google::protobuf::internal::CompareByFirstField<SortItem> Less;

    if (deterministic &&
        this->import_enum_amp().size() > 1) {
      ::google::protobuf::scoped_array<SortItem> items(
          new SortItem[this->import_enum_amp().size()]);
      typedef ::google::protobuf::Map< ::google::protobuf::int32, ::protobuf_unittest_import::ImportEnumForMap >::size_type size_type;
      size_type n = 0;
      for (::google::protobuf::Map< ::google::protobuf::int32, ::protobuf_unittest_import::ImportEnumForMap >::const_iterator
          it = this->import_enum_amp().begin();
          it != this->import_enum_amp().end(); ++it, ++n) {
        items[n] = SortItem(&*it);
      }
      ::std::sort(&items[0], &items[n], Less());
      ::google::protobuf::scoped_ptr<TestImportEnumMap_ImportEnumAmpEntry> entry;
      for (size_type i = 0; i < n; i++) {
        entry.reset(import_enum_amp_.NewEnumEntryWrapper(
            items[i].second->first, items[i].second->second));
        target = ::google::protobuf::internal::WireFormatLite::
                   InternalWriteMessageNoVirtualToArray(
                       1, *entry, deterministic, target);
;
      }
    } else {
      ::google::protobuf::scoped_ptr<TestImportEnumMap_ImportEnumAmpEntry> entry;
      for (::google::protobuf::Map< ::google::protobuf::int32, ::protobuf_unittest_import::ImportEnumForMap >::const_iterator
          it = this->import_enum_amp().begin();
          it != this->import_enum_amp().end(); ++it) {
        entry.reset(import_enum_amp_.NewEnumEntryWrapper(
            it->first, it->second));
        target = ::google::protobuf::internal::WireFormatLite::
                   InternalWriteMessageNoVirtualToArray(
                       1, *entry, deterministic, target);
;
      }
    }
  }

  if (_internal_metadata_.have_unknown_fields()) {
    target = ::google::protobuf::internal::WireFormat::SerializeUnknownFieldsToArray(
        unknown_fields(), target);
  }
  // @@protoc_insertion_point(serialize_to_array_end:protobuf_unittest.TestImportEnumMap)
  return target;
}

size_t TestImportEnumMap::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:protobuf_unittest.TestImportEnumMap)
  size_t total_size = 0;

  // map<int32, .protobuf_unittest_import.ImportEnumForMap> import_enum_amp = 1;
  total_size += 1 *
      ::google::protobuf::internal::FromIntSize(this->import_enum_amp_size());
  {
    ::google::protobuf::scoped_ptr<TestImportEnumMap_ImportEnumAmpEntry> entry;
    for (::google::protobuf::Map< ::google::protobuf::int32, ::protobuf_unittest_import::ImportEnumForMap >::const_iterator
        it = this->import_enum_amp().begin();
        it != this->import_enum_amp().end(); ++it) {
      entry.reset(import_enum_amp_.NewEnumEntryWrapper(it->first, it->second));
      total_size += ::google::protobuf::internal::WireFormatLite::
          MessageSizeNoVirtual(*entry);
    }
  }

  if (_internal_metadata_.have_unknown_fields()) {
    total_size +=
      ::google::protobuf::internal::WireFormat::ComputeUnknownFieldsSize(
        unknown_fields());
  }
  int cached_size = ::google::protobuf::internal::ToCachedSize(total_size);
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = cached_size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
  return total_size;
}

void TestImportEnumMap::MergeFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_merge_from_start:protobuf_unittest.TestImportEnumMap)
  if (GOOGLE_PREDICT_FALSE(&from == this)) MergeFromFail(__LINE__);
  const TestImportEnumMap* source =
      ::google::protobuf::internal::DynamicCastToGenerated<const TestImportEnumMap>(
          &from);
  if (source == NULL) {
  // @@protoc_insertion_point(generalized_merge_from_cast_fail:protobuf_unittest.TestImportEnumMap)
    ::google::protobuf::internal::ReflectionOps::Merge(from, this);
  } else {
  // @@protoc_insertion_point(generalized_merge_from_cast_success:protobuf_unittest.TestImportEnumMap)
    UnsafeMergeFrom(*source);
  }
}

void TestImportEnumMap::MergeFrom(const TestImportEnumMap& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:protobuf_unittest.TestImportEnumMap)
  if (GOOGLE_PREDICT_TRUE(&from != this)) {
    UnsafeMergeFrom(from);
  } else {
    MergeFromFail(__LINE__);
  }
}

void TestImportEnumMap::UnsafeMergeFrom(const TestImportEnumMap& from) {
  GOOGLE_DCHECK(&from != this);
  import_enum_amp_.MergeFrom(from.import_enum_amp_);
  if (from._internal_metadata_.have_unknown_fields()) {
    ::google::protobuf::UnknownFieldSet::MergeToInternalMetdata(
      from.unknown_fields(), &_internal_metadata_);
  }
}

void TestImportEnumMap::CopyFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_copy_from_start:protobuf_unittest.TestImportEnumMap)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

void TestImportEnumMap::CopyFrom(const TestImportEnumMap& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:protobuf_unittest.TestImportEnumMap)
  if (&from == this) return;
  Clear();
  UnsafeMergeFrom(from);
}

bool TestImportEnumMap::IsInitialized() const {

  return true;
}

void TestImportEnumMap::Swap(TestImportEnumMap* other) {
  if (other == this) return;
  InternalSwap(other);
}
void TestImportEnumMap::InternalSwap(TestImportEnumMap* other) {
  import_enum_amp_.Swap(&other->import_enum_amp_);
  std::swap(_has_bits_[0], other->_has_bits_[0]);
  _internal_metadata_.Swap(&other->_internal_metadata_);
  std::swap(_cached_size_, other->_cached_size_);
}

::google::protobuf::Metadata TestImportEnumMap::GetMetadata() const {
  protobuf_AssignDescriptorsOnce();
  ::google::protobuf::Metadata metadata;
  metadata.descriptor = TestImportEnumMap_descriptor_;
  metadata.reflection = TestImportEnumMap_reflection_;
  return metadata;
}

#if PROTOBUF_INLINE_NOT_IN_HEADERS
// TestImportEnumMap

// map<int32, .protobuf_unittest_import.ImportEnumForMap> import_enum_amp = 1;
int TestImportEnumMap::import_enum_amp_size() const {
  return import_enum_amp_.size();
}
void TestImportEnumMap::clear_import_enum_amp() {
  import_enum_amp_.Clear();
}
 const ::google::protobuf::Map< ::google::protobuf::int32, ::protobuf_unittest_import::ImportEnumForMap >&
TestImportEnumMap::import_enum_amp() const {
  // @@protoc_insertion_point(field_map:protobuf_unittest.TestImportEnumMap.import_enum_amp)
  return import_enum_amp_.GetMap();
}
 ::google::protobuf::Map< ::google::protobuf::int32, ::protobuf_unittest_import::ImportEnumForMap >*
TestImportEnumMap::mutable_import_enum_amp() {
  // @@protoc_insertion_point(field_mutable_map:protobuf_unittest.TestImportEnumMap.import_enum_amp)
  return import_enum_amp_.MutableMap();
}

inline const TestImportEnumMap* TestImportEnumMap::internal_default_instance() {
  return &TestImportEnumMap_default_instance_.get();
}
#endif  // PROTOBUF_INLINE_NOT_IN_HEADERS

// ===================================================================

#if !defined(_MSC_VER) || _MSC_VER >= 1900
const int TestIntIntMap::kMFieldNumber;
#endif  // !defined(_MSC_VER) || _MSC_VER >= 1900

TestIntIntMap::TestIntIntMap()
  : ::google::protobuf::Message(), _internal_metadata_(NULL) {
  if (this != internal_default_instance()) protobuf_InitDefaults_google_2fprotobuf_2fmap_5fproto2_5funittest_2eproto();
  SharedCtor();
  // @@protoc_insertion_point(constructor:protobuf_unittest.TestIntIntMap)
}

void TestIntIntMap::InitAsDefaultInstance() {
}

TestIntIntMap::TestIntIntMap(const TestIntIntMap& from)
  : ::google::protobuf::Message(),
    _internal_metadata_(NULL) {
  SharedCtor();
  UnsafeMergeFrom(from);
  // @@protoc_insertion_point(copy_constructor:protobuf_unittest.TestIntIntMap)
}

void TestIntIntMap::SharedCtor() {
  _cached_size_ = 0;
  m_.SetAssignDescriptorCallback(
      protobuf_AssignDescriptorsOnce);
  m_.SetEntryDescriptor(
      &::protobuf_unittest::TestIntIntMap_MEntry_descriptor_);
}

TestIntIntMap::~TestIntIntMap() {
  // @@protoc_insertion_point(destructor:protobuf_unittest.TestIntIntMap)
  SharedDtor();
}

void TestIntIntMap::SharedDtor() {
}

void TestIntIntMap::SetCachedSize(int size) const {
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
}
const ::google::protobuf::Descriptor* TestIntIntMap::descriptor() {
  protobuf_AssignDescriptorsOnce();
  return TestIntIntMap_descriptor_;
}

const TestIntIntMap& TestIntIntMap::default_instance() {
  protobuf_InitDefaults_google_2fprotobuf_2fmap_5fproto2_5funittest_2eproto();
  return *internal_default_instance();
}

::google::protobuf::internal::ExplicitlyConstructed<TestIntIntMap> TestIntIntMap_default_instance_;

TestIntIntMap* TestIntIntMap::New(::google::protobuf::Arena* arena) const {
  TestIntIntMap* n = new TestIntIntMap;
  if (arena != NULL) {
    arena->Own(n);
  }
  return n;
}

void TestIntIntMap::Clear() {
// @@protoc_insertion_point(message_clear_start:protobuf_unittest.TestIntIntMap)
  m_.Clear();
  _has_bits_.Clear();
  if (_internal_metadata_.have_unknown_fields()) {
    mutable_unknown_fields()->Clear();
  }
}

bool TestIntIntMap::MergePartialFromCodedStream(
    ::google::protobuf::io::CodedInputStream* input) {
#define DO_(EXPRESSION) if (!GOOGLE_PREDICT_TRUE(EXPRESSION)) goto failure
  ::google::protobuf::uint32 tag;
  // @@protoc_insertion_point(parse_start:protobuf_unittest.TestIntIntMap)
  for (;;) {
    ::std::pair< ::google::protobuf::uint32, bool> p = input->ReadTagWithCutoff(127);
    tag = p.first;
    if (!p.second) goto handle_unusual;
    switch (::google::protobuf::internal::WireFormatLite::GetTagFieldNumber(tag)) {
      // map<int32, int32> m = 1;
      case 1: {
        if (tag == 10) {
          DO_(input->IncrementRecursionDepth());
         parse_loop_m:
          TestIntIntMap_MEntry::Parser< ::google::protobuf::internal::MapField<
              ::google::protobuf::int32, ::google::protobuf::int32,
              ::google::protobuf::internal::WireFormatLite::TYPE_INT32,
              ::google::protobuf::internal::WireFormatLite::TYPE_INT32,
              0 >,
            ::google::protobuf::Map< ::google::protobuf::int32, ::google::protobuf::int32 > > parser(&m_);
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessageNoVirtual(
              input, &parser));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(10)) goto parse_loop_m;
        input->UnsafeDecrementRecursionDepth();
        if (input->ExpectAtEnd()) goto success;
        break;
      }

      default: {
      handle_unusual:
        if (tag == 0 ||
            ::google::protobuf::internal::WireFormatLite::GetTagWireType(tag) ==
            ::google::protobuf::internal::WireFormatLite::WIRETYPE_END_GROUP) {
          goto success;
        }
        DO_(::google::protobuf::internal::WireFormat::SkipField(
              input, tag, mutable_unknown_fields()));
        break;
      }
    }
  }
success:
  // @@protoc_insertion_point(parse_success:protobuf_unittest.TestIntIntMap)
  return true;
failure:
  // @@protoc_insertion_point(parse_failure:protobuf_unittest.TestIntIntMap)
  return false;
#undef DO_
}

void TestIntIntMap::SerializeWithCachedSizes(
    ::google::protobuf::io::CodedOutputStream* output) const {
  // @@protoc_insertion_point(serialize_start:protobuf_unittest.TestIntIntMap)
  // map<int32, int32> m = 1;
  if (!this->m().empty()) {
    typedef ::google::protobuf::Map< ::google::protobuf::int32, ::google::protobuf::int32 >::const_pointer
        ConstPtr;
    typedef ::google::protobuf::internal::SortItem< ::google::protobuf::int32, ConstPtr > SortItem;
    typedef ::google::protobuf::internal::CompareByFirstField<SortItem> Less;

    if (output->IsSerializationDeterminstic() &&
        this->m().size() > 1) {
      ::google::protobuf::scoped_array<SortItem> items(
          new SortItem[this->m().size()]);
      typedef ::google::protobuf::Map< ::google::protobuf::int32, ::google::protobuf::int32 >::size_type size_type;
      size_type n = 0;
      for (::google::protobuf::Map< ::google::protobuf::int32, ::google::protobuf::int32 >::const_iterator
          it = this->m().begin();
          it != this->m().end(); ++it, ++n) {
        items[n] = SortItem(&*it);
      }
      ::std::sort(&items[0], &items[n], Less());
      ::google::protobuf::scoped_ptr<TestIntIntMap_MEntry> entry;
      for (size_type i = 0; i < n; i++) {
        entry.reset(m_.NewEntryWrapper(
            items[i].second->first, items[i].second->second));
        ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
            1, *entry, output);
      }
    } else {
      ::google::protobuf::scoped_ptr<TestIntIntMap_MEntry> entry;
      for (::google::protobuf::Map< ::google::protobuf::int32, ::google::protobuf::int32 >::const_iterator
          it = this->m().begin();
          it != this->m().end(); ++it) {
        entry.reset(m_.NewEntryWrapper(
            it->first, it->second));
        ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
            1, *entry, output);
      }
    }
  }

  if (_internal_metadata_.have_unknown_fields()) {
    ::google::protobuf::internal::WireFormat::SerializeUnknownFields(
        unknown_fields(), output);
  }
  // @@protoc_insertion_point(serialize_end:protobuf_unittest.TestIntIntMap)
}

::google::protobuf::uint8* TestIntIntMap::InternalSerializeWithCachedSizesToArray(
    bool deterministic, ::google::protobuf::uint8* target) const {
  (void)deterministic; // Unused
  // @@protoc_insertion_point(serialize_to_array_start:protobuf_unittest.TestIntIntMap)
  // map<int32, int32> m = 1;
  if (!this->m().empty()) {
    typedef ::google::protobuf::Map< ::google::protobuf::int32, ::google::protobuf::int32 >::const_pointer
        ConstPtr;
    typedef ::google::protobuf::internal::SortItem< ::google::protobuf::int32, ConstPtr > SortItem;
    typedef ::google::protobuf::internal::CompareByFirstField<SortItem> Less;

    if (deterministic &&
        this->m().size() > 1) {
      ::google::protobuf::scoped_array<SortItem> items(
          new SortItem[this->m().size()]);
      typedef ::google::protobuf::Map< ::google::protobuf::int32, ::google::protobuf::int32 >::size_type size_type;
      size_type n = 0;
      for (::google::protobuf::Map< ::google::protobuf::int32, ::google::protobuf::int32 >::const_iterator
          it = this->m().begin();
          it != this->m().end(); ++it, ++n) {
        items[n] = SortItem(&*it);
      }
      ::std::sort(&items[0], &items[n], Less());
      ::google::protobuf::scoped_ptr<TestIntIntMap_MEntry> entry;
      for (size_type i = 0; i < n; i++) {
        entry.reset(m_.NewEntryWrapper(
            items[i].second->first, items[i].second->second));
        target = ::google::protobuf::internal::WireFormatLite::
                   InternalWriteMessageNoVirtualToArray(
                       1, *entry, deterministic, target);
;
      }
    } else {
      ::google::protobuf::scoped_ptr<TestIntIntMap_MEntry> entry;
      for (::google::protobuf::Map< ::google::protobuf::int32, ::google::protobuf::int32 >::const_iterator
          it = this->m().begin();
          it != this->m().end(); ++it) {
        entry.reset(m_.NewEntryWrapper(
            it->first, it->second));
        target = ::google::protobuf::internal::WireFormatLite::
                   InternalWriteMessageNoVirtualToArray(
                       1, *entry, deterministic, target);
;
      }
    }
  }

  if (_internal_metadata_.have_unknown_fields()) {
    target = ::google::protobuf::internal::WireFormat::SerializeUnknownFieldsToArray(
        unknown_fields(), target);
  }
  // @@protoc_insertion_point(serialize_to_array_end:protobuf_unittest.TestIntIntMap)
  return target;
}

size_t TestIntIntMap::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:protobuf_unittest.TestIntIntMap)
  size_t total_size = 0;

  // map<int32, int32> m = 1;
  total_size += 1 *
      ::google::protobuf::internal::FromIntSize(this->m_size());
  {
    ::google::protobuf::scoped_ptr<TestIntIntMap_MEntry> entry;
    for (::google::protobuf::Map< ::google::protobuf::int32, ::google::protobuf::int32 >::const_iterator
        it = this->m().begin();
        it != this->m().end(); ++it) {
      entry.reset(m_.NewEntryWrapper(it->first, it->second));
      total_size += ::google::protobuf::internal::WireFormatLite::
          MessageSizeNoVirtual(*entry);
    }
  }

  if (_internal_metadata_.have_unknown_fields()) {
    total_size +=
      ::google::protobuf::internal::WireFormat::ComputeUnknownFieldsSize(
        unknown_fields());
  }
  int cached_size = ::google::protobuf::internal::ToCachedSize(total_size);
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = cached_size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
  return total_size;
}

void TestIntIntMap::MergeFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_merge_from_start:protobuf_unittest.TestIntIntMap)
  if (GOOGLE_PREDICT_FALSE(&from == this)) MergeFromFail(__LINE__);
  const TestIntIntMap* source =
      ::google::protobuf::internal::DynamicCastToGenerated<const TestIntIntMap>(
          &from);
  if (source == NULL) {
  // @@protoc_insertion_point(generalized_merge_from_cast_fail:protobuf_unittest.TestIntIntMap)
    ::google::protobuf::internal::ReflectionOps::Merge(from, this);
  } else {
  // @@protoc_insertion_point(generalized_merge_from_cast_success:protobuf_unittest.TestIntIntMap)
    UnsafeMergeFrom(*source);
  }
}

void TestIntIntMap::MergeFrom(const TestIntIntMap& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:protobuf_unittest.TestIntIntMap)
  if (GOOGLE_PREDICT_TRUE(&from != this)) {
    UnsafeMergeFrom(from);
  } else {
    MergeFromFail(__LINE__);
  }
}

void TestIntIntMap::UnsafeMergeFrom(const TestIntIntMap& from) {
  GOOGLE_DCHECK(&from != this);
  m_.MergeFrom(from.m_);
  if (from._internal_metadata_.have_unknown_fields()) {
    ::google::protobuf::UnknownFieldSet::MergeToInternalMetdata(
      from.unknown_fields(), &_internal_metadata_);
  }
}

void TestIntIntMap::CopyFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_copy_from_start:protobuf_unittest.TestIntIntMap)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

void TestIntIntMap::CopyFrom(const TestIntIntMap& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:protobuf_unittest.TestIntIntMap)
  if (&from == this) return;
  Clear();
  UnsafeMergeFrom(from);
}

bool TestIntIntMap::IsInitialized() const {

  return true;
}

void TestIntIntMap::Swap(TestIntIntMap* other) {
  if (other == this) return;
  InternalSwap(other);
}
void TestIntIntMap::InternalSwap(TestIntIntMap* other) {
  m_.Swap(&other->m_);
  std::swap(_has_bits_[0], other->_has_bits_[0]);
  _internal_metadata_.Swap(&other->_internal_metadata_);
  std::swap(_cached_size_, other->_cached_size_);
}

::google::protobuf::Metadata TestIntIntMap::GetMetadata() const {
  protobuf_AssignDescriptorsOnce();
  ::google::protobuf::Metadata metadata;
  metadata.descriptor = TestIntIntMap_descriptor_;
  metadata.reflection = TestIntIntMap_reflection_;
  return metadata;
}

#if PROTOBUF_INLINE_NOT_IN_HEADERS
// TestIntIntMap

// map<int32, int32> m = 1;
int TestIntIntMap::m_size() const {
  return m_.size();
}
void TestIntIntMap::clear_m() {
  m_.Clear();
}
 const ::google::protobuf::Map< ::google::protobuf::int32, ::google::protobuf::int32 >&
TestIntIntMap::m() const {
  // @@protoc_insertion_point(field_map:protobuf_unittest.TestIntIntMap.m)
  return m_.GetMap();
}
 ::google::protobuf::Map< ::google::protobuf::int32, ::google::protobuf::int32 >*
TestIntIntMap::mutable_m() {
  // @@protoc_insertion_point(field_mutable_map:protobuf_unittest.TestIntIntMap.m)
  return m_.MutableMap();
}

inline const TestIntIntMap* TestIntIntMap::internal_default_instance() {
  return &TestIntIntMap_default_instance_.get();
}
#endif  // PROTOBUF_INLINE_NOT_IN_HEADERS

// ===================================================================

#if !defined(_MSC_VER) || _MSC_VER >= 1900
const int TestMaps::kMInt32FieldNumber;
const int TestMaps::kMInt64FieldNumber;
const int TestMaps::kMUint32FieldNumber;
const int TestMaps::kMUint64FieldNumber;
const int TestMaps::kMSint32FieldNumber;
const int TestMaps::kMSint64FieldNumber;
const int TestMaps::kMFixed32FieldNumber;
const int TestMaps::kMFixed64FieldNumber;
const int TestMaps::kMSfixed32FieldNumber;
const int TestMaps::kMSfixed64FieldNumber;
const int TestMaps::kMBoolFieldNumber;
const int TestMaps::kMStringFieldNumber;
#endif  // !defined(_MSC_VER) || _MSC_VER >= 1900

TestMaps::TestMaps()
  : ::google::protobuf::Message(), _internal_metadata_(NULL) {
  if (this != internal_default_instance()) protobuf_InitDefaults_google_2fprotobuf_2fmap_5fproto2_5funittest_2eproto();
  SharedCtor();
  // @@protoc_insertion_point(constructor:protobuf_unittest.TestMaps)
}

void TestMaps::InitAsDefaultInstance() {
}

TestMaps::TestMaps(const TestMaps& from)
  : ::google::protobuf::Message(),
    _internal_metadata_(NULL) {
  SharedCtor();
  UnsafeMergeFrom(from);
  // @@protoc_insertion_point(copy_constructor:protobuf_unittest.TestMaps)
}

void TestMaps::SharedCtor() {
  _cached_size_ = 0;
  m_int32_.SetAssignDescriptorCallback(
      protobuf_AssignDescriptorsOnce);
  m_int32_.SetEntryDescriptor(
      &::protobuf_unittest::TestMaps_MInt32Entry_descriptor_);
  m_int64_.SetAssignDescriptorCallback(
      protobuf_AssignDescriptorsOnce);
  m_int64_.SetEntryDescriptor(
      &::protobuf_unittest::TestMaps_MInt64Entry_descriptor_);
  m_uint32_.SetAssignDescriptorCallback(
      protobuf_AssignDescriptorsOnce);
  m_uint32_.SetEntryDescriptor(
      &::protobuf_unittest::TestMaps_MUint32Entry_descriptor_);
  m_uint64_.SetAssignDescriptorCallback(
      protobuf_AssignDescriptorsOnce);
  m_uint64_.SetEntryDescriptor(
      &::protobuf_unittest::TestMaps_MUint64Entry_descriptor_);
  m_sint32_.SetAssignDescriptorCallback(
      protobuf_AssignDescriptorsOnce);
  m_sint32_.SetEntryDescriptor(
      &::protobuf_unittest::TestMaps_MSint32Entry_descriptor_);
  m_sint64_.SetAssignDescriptorCallback(
      protobuf_AssignDescriptorsOnce);
  m_sint64_.SetEntryDescriptor(
      &::protobuf_unittest::TestMaps_MSint64Entry_descriptor_);
  m_fixed32_.SetAssignDescriptorCallback(
      protobuf_AssignDescriptorsOnce);
  m_fixed32_.SetEntryDescriptor(
      &::protobuf_unittest::TestMaps_MFixed32Entry_descriptor_);
  m_fixed64_.SetAssignDescriptorCallback(
      protobuf_AssignDescriptorsOnce);
  m_fixed64_.SetEntryDescriptor(
      &::protobuf_unittest::TestMaps_MFixed64Entry_descriptor_);
  m_sfixed32_.SetAssignDescriptorCallback(
      protobuf_AssignDescriptorsOnce);
  m_sfixed32_.SetEntryDescriptor(
      &::protobuf_unittest::TestMaps_MSfixed32Entry_descriptor_);
  m_sfixed64_.SetAssignDescriptorCallback(
      protobuf_AssignDescriptorsOnce);
  m_sfixed64_.SetEntryDescriptor(
      &::protobuf_unittest::TestMaps_MSfixed64Entry_descriptor_);
  m_bool_.SetAssignDescriptorCallback(
      protobuf_AssignDescriptorsOnce);
  m_bool_.SetEntryDescriptor(
      &::protobuf_unittest::TestMaps_MBoolEntry_descriptor_);
  m_string_.SetAssignDescriptorCallback(
      protobuf_AssignDescriptorsOnce);
  m_string_.SetEntryDescriptor(
      &::protobuf_unittest::TestMaps_MStringEntry_descriptor_);
}

TestMaps::~TestMaps() {
  // @@protoc_insertion_point(destructor:protobuf_unittest.TestMaps)
  SharedDtor();
}

void TestMaps::SharedDtor() {
}

void TestMaps::SetCachedSize(int size) const {
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
}
const ::google::protobuf::Descriptor* TestMaps::descriptor() {
  protobuf_AssignDescriptorsOnce();
  return TestMaps_descriptor_;
}

const TestMaps& TestMaps::default_instance() {
  protobuf_InitDefaults_google_2fprotobuf_2fmap_5fproto2_5funittest_2eproto();
  return *internal_default_instance();
}

::google::protobuf::internal::ExplicitlyConstructed<TestMaps> TestMaps_default_instance_;

TestMaps* TestMaps::New(::google::protobuf::Arena* arena) const {
  TestMaps* n = new TestMaps;
  if (arena != NULL) {
    arena->Own(n);
  }
  return n;
}

void TestMaps::Clear() {
// @@protoc_insertion_point(message_clear_start:protobuf_unittest.TestMaps)
  m_int32_.Clear();
  m_int64_.Clear();
  m_uint32_.Clear();
  m_uint64_.Clear();
  m_sint32_.Clear();
  m_sint64_.Clear();
  m_fixed32_.Clear();
  m_fixed64_.Clear();
  m_sfixed32_.Clear();
  m_sfixed64_.Clear();
  m_bool_.Clear();
  m_string_.Clear();
  _has_bits_.Clear();
  if (_internal_metadata_.have_unknown_fields()) {
    mutable_unknown_fields()->Clear();
  }
}

bool TestMaps::MergePartialFromCodedStream(
    ::google::protobuf::io::CodedInputStream* input) {
#define DO_(EXPRESSION) if (!GOOGLE_PREDICT_TRUE(EXPRESSION)) goto failure
  ::google::protobuf::uint32 tag;
  // @@protoc_insertion_point(parse_start:protobuf_unittest.TestMaps)
  for (;;) {
    ::std::pair< ::google::protobuf::uint32, bool> p = input->ReadTagWithCutoff(127);
    tag = p.first;
    if (!p.second) goto handle_unusual;
    switch (::google::protobuf::internal::WireFormatLite::GetTagFieldNumber(tag)) {
      // map<int32, .protobuf_unittest.TestIntIntMap> m_int32 = 1;
      case 1: {
        if (tag == 10) {
          DO_(input->IncrementRecursionDepth());
         parse_loop_m_int32:
          TestMaps_MInt32Entry::Parser< ::google::protobuf::internal::MapField<
              ::google::protobuf::int32, ::protobuf_unittest::TestIntIntMap,
              ::google::protobuf::internal::WireFormatLite::TYPE_INT32,
              ::google::protobuf::internal::WireFormatLite::TYPE_MESSAGE,
              0 >,
            ::google::protobuf::Map< ::google::protobuf::int32, ::protobuf_unittest::TestIntIntMap > > parser(&m_int32_);
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessageNoVirtual(
              input, &parser));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(10)) goto parse_loop_m_int32;
        if (input->ExpectTag(18)) goto parse_loop_m_int64;
        input->UnsafeDecrementRecursionDepth();
        break;
      }

      // map<int64, .protobuf_unittest.TestIntIntMap> m_int64 = 2;
      case 2: {
        if (tag == 18) {
          DO_(input->IncrementRecursionDepth());
         parse_loop_m_int64:
          TestMaps_MInt64Entry::Parser< ::google::protobuf::internal::MapField<
              ::google::protobuf::int64, ::protobuf_unittest::TestIntIntMap,
              ::google::protobuf::internal::WireFormatLite::TYPE_INT64,
              ::google::protobuf::internal::WireFormatLite::TYPE_MESSAGE,
              0 >,
            ::google::protobuf::Map< ::google::protobuf::int64, ::protobuf_unittest::TestIntIntMap > > parser(&m_int64_);
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessageNoVirtual(
              input, &parser));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(18)) goto parse_loop_m_int64;
        if (input->ExpectTag(26)) goto parse_loop_m_uint32;
        input->UnsafeDecrementRecursionDepth();
        break;
      }

      // map<uint32, .protobuf_unittest.TestIntIntMap> m_uint32 = 3;
      case 3: {
        if (tag == 26) {
          DO_(input->IncrementRecursionDepth());
         parse_loop_m_uint32:
          TestMaps_MUint32Entry::Parser< ::google::protobuf::internal::MapField<
              ::google::protobuf::uint32, ::protobuf_unittest::TestIntIntMap,
              ::google::protobuf::internal::WireFormatLite::TYPE_UINT32,
              ::google::protobuf::internal::WireFormatLite::TYPE_MESSAGE,
              0 >,
            ::google::protobuf::Map< ::google::protobuf::uint32, ::protobuf_unittest::TestIntIntMap > > parser(&m_uint32_);
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessageNoVirtual(
              input, &parser));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(26)) goto parse_loop_m_uint32;
        if (input->ExpectTag(34)) goto parse_loop_m_uint64;
        input->UnsafeDecrementRecursionDepth();
        break;
      }

      // map<uint64, .protobuf_unittest.TestIntIntMap> m_uint64 = 4;
      case 4: {
        if (tag == 34) {
          DO_(input->IncrementRecursionDepth());
         parse_loop_m_uint64:
          TestMaps_MUint64Entry::Parser< ::google::protobuf::internal::MapField<
              ::google::protobuf::uint64, ::protobuf_unittest::TestIntIntMap,
              ::google::protobuf::internal::WireFormatLite::TYPE_UINT64,
              ::google::protobuf::internal::WireFormatLite::TYPE_MESSAGE,
              0 >,
            ::google::protobuf::Map< ::google::protobuf::uint64, ::protobuf_unittest::TestIntIntMap > > parser(&m_uint64_);
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessageNoVirtual(
              input, &parser));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(34)) goto parse_loop_m_uint64;
        if (input->ExpectTag(42)) goto parse_loop_m_sint32;
        input->UnsafeDecrementRecursionDepth();
        break;
      }

      // map<sint32, .protobuf_unittest.TestIntIntMap> m_sint32 = 5;
      case 5: {
        if (tag == 42) {
          DO_(input->IncrementRecursionDepth());
         parse_loop_m_sint32:
          TestMaps_MSint32Entry::Parser< ::google::protobuf::internal::MapField<
              ::google::protobuf::int32, ::protobuf_unittest::TestIntIntMap,
              ::google::protobuf::internal::WireFormatLite::TYPE_SINT32,
              ::google::protobuf::internal::WireFormatLite::TYPE_MESSAGE,
              0 >,
            ::google::protobuf::Map< ::google::protobuf::int32, ::protobuf_unittest::TestIntIntMap > > parser(&m_sint32_);
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessageNoVirtual(
              input, &parser));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(42)) goto parse_loop_m_sint32;
        if (input->ExpectTag(50)) goto parse_loop_m_sint64;
        input->UnsafeDecrementRecursionDepth();
        break;
      }

      // map<sint64, .protobuf_unittest.TestIntIntMap> m_sint64 = 6;
      case 6: {
        if (tag == 50) {
          DO_(input->IncrementRecursionDepth());
         parse_loop_m_sint64:
          TestMaps_MSint64Entry::Parser< ::google::protobuf::internal::MapField<
              ::google::protobuf::int64, ::protobuf_unittest::TestIntIntMap,
              ::google::protobuf::internal::WireFormatLite::TYPE_SINT64,
              ::google::protobuf::internal::WireFormatLite::TYPE_MESSAGE,
              0 >,
            ::google::protobuf::Map< ::google::protobuf::int64, ::protobuf_unittest::TestIntIntMap > > parser(&m_sint64_);
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessageNoVirtual(
              input, &parser));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(50)) goto parse_loop_m_sint64;
        if (input->ExpectTag(58)) goto parse_loop_m_fixed32;
        input->UnsafeDecrementRecursionDepth();
        break;
      }

      // map<fixed32, .protobuf_unittest.TestIntIntMap> m_fixed32 = 7;
      case 7: {
        if (tag == 58) {
          DO_(input->IncrementRecursionDepth());
         parse_loop_m_fixed32:
          TestMaps_MFixed32Entry::Parser< ::google::protobuf::internal::MapField<
              ::google::protobuf::uint32, ::protobuf_unittest::TestIntIntMap,
              ::google::protobuf::internal::WireFormatLite::TYPE_FIXED32,
              ::google::protobuf::internal::WireFormatLite::TYPE_MESSAGE,
              0 >,
            ::google::protobuf::Map< ::google::protobuf::uint32, ::protobuf_unittest::TestIntIntMap > > parser(&m_fixed32_);
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessageNoVirtual(
              input, &parser));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(58)) goto parse_loop_m_fixed32;
        if (input->ExpectTag(66)) goto parse_loop_m_fixed64;
        input->UnsafeDecrementRecursionDepth();
        break;
      }

      // map<fixed64, .protobuf_unittest.TestIntIntMap> m_fixed64 = 8;
      case 8: {
        if (tag == 66) {
          DO_(input->IncrementRecursionDepth());
         parse_loop_m_fixed64:
          TestMaps_MFixed64Entry::Parser< ::google::protobuf::internal::MapField<
              ::google::protobuf::uint64, ::protobuf_unittest::TestIntIntMap,
              ::google::protobuf::internal::WireFormatLite::TYPE_FIXED64,
              ::google::protobuf::internal::WireFormatLite::TYPE_MESSAGE,
              0 >,
            ::google::protobuf::Map< ::google::protobuf::uint64, ::protobuf_unittest::TestIntIntMap > > parser(&m_fixed64_);
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessageNoVirtual(
              input, &parser));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(66)) goto parse_loop_m_fixed64;
        if (input->ExpectTag(74)) goto parse_loop_m_sfixed32;
        input->UnsafeDecrementRecursionDepth();
        break;
      }

      // map<sfixed32, .protobuf_unittest.TestIntIntMap> m_sfixed32 = 9;
      case 9: {
        if (tag == 74) {
          DO_(input->IncrementRecursionDepth());
         parse_loop_m_sfixed32:
          TestMaps_MSfixed32Entry::Parser< ::google::protobuf::internal::MapField<
              ::google::protobuf::int32, ::protobuf_unittest::TestIntIntMap,
              ::google::protobuf::internal::WireFormatLite::TYPE_SFIXED32,
              ::google::protobuf::internal::WireFormatLite::TYPE_MESSAGE,
              0 >,
            ::google::protobuf::Map< ::google::protobuf::int32, ::protobuf_unittest::TestIntIntMap > > parser(&m_sfixed32_);
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessageNoVirtual(
              input, &parser));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(74)) goto parse_loop_m_sfixed32;
        if (input->ExpectTag(82)) goto parse_loop_m_sfixed64;
        input->UnsafeDecrementRecursionDepth();
        break;
      }

      // map<sfixed64, .protobuf_unittest.TestIntIntMap> m_sfixed64 = 10;
      case 10: {
        if (tag == 82) {
          DO_(input->IncrementRecursionDepth());
         parse_loop_m_sfixed64:
          TestMaps_MSfixed64Entry::Parser< ::google::protobuf::internal::MapField<
              ::google::protobuf::int64, ::protobuf_unittest::TestIntIntMap,
              ::google::protobuf::internal::WireFormatLite::TYPE_SFIXED64,
              ::google::protobuf::internal::WireFormatLite::TYPE_MESSAGE,
              0 >,
            ::google::protobuf::Map< ::google::protobuf::int64, ::protobuf_unittest::TestIntIntMap > > parser(&m_sfixed64_);
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessageNoVirtual(
              input, &parser));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(82)) goto parse_loop_m_sfixed64;
        if (input->ExpectTag(90)) goto parse_loop_m_bool;
        input->UnsafeDecrementRecursionDepth();
        break;
      }

      // map<bool, .protobuf_unittest.TestIntIntMap> m_bool = 11;
      case 11: {
        if (tag == 90) {
          DO_(input->IncrementRecursionDepth());
         parse_loop_m_bool:
          TestMaps_MBoolEntry::Parser< ::google::protobuf::internal::MapField<
              bool, ::protobuf_unittest::TestIntIntMap,
              ::google::protobuf::internal::WireFormatLite::TYPE_BOOL,
              ::google::protobuf::internal::WireFormatLite::TYPE_MESSAGE,
              0 >,
            ::google::protobuf::Map< bool, ::protobuf_unittest::TestIntIntMap > > parser(&m_bool_);
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessageNoVirtual(
              input, &parser));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(90)) goto parse_loop_m_bool;
        if (input->ExpectTag(98)) goto parse_loop_m_string;
        input->UnsafeDecrementRecursionDepth();
        break;
      }

      // map<string, .protobuf_unittest.TestIntIntMap> m_string = 12;
      case 12: {
        if (tag == 98) {
          DO_(input->IncrementRecursionDepth());
         parse_loop_m_string:
          TestMaps_MStringEntry::Parser< ::google::protobuf::internal::MapField<
              ::std::string, ::protobuf_unittest::TestIntIntMap,
              ::google::protobuf::internal::WireFormatLite::TYPE_STRING,
              ::google::protobuf::internal::WireFormatLite::TYPE_MESSAGE,
              0 >,
            ::google::protobuf::Map< ::std::string, ::protobuf_unittest::TestIntIntMap > > parser(&m_string_);
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessageNoVirtual(
              input, &parser));
          ::google::protobuf::internal::WireFormat::VerifyUTF8StringNamedField(
            parser.key().data(), parser.key().length(),
            ::google::protobuf::internal::WireFormat::PARSE,
            "protobuf_unittest.TestMaps.MStringEntry.key");
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(98)) goto parse_loop_m_string;
        input->UnsafeDecrementRecursionDepth();
        if (input->ExpectAtEnd()) goto success;
        break;
      }

      default: {
      handle_unusual:
        if (tag == 0 ||
            ::google::protobuf::internal::WireFormatLite::GetTagWireType(tag) ==
            ::google::protobuf::internal::WireFormatLite::WIRETYPE_END_GROUP) {
          goto success;
        }
        DO_(::google::protobuf::internal::WireFormat::SkipField(
              input, tag, mutable_unknown_fields()));
        break;
      }
    }
  }
success:
  // @@protoc_insertion_point(parse_success:protobuf_unittest.TestMaps)
  return true;
failure:
  // @@protoc_insertion_point(parse_failure:protobuf_unittest.TestMaps)
  return false;
#undef DO_
}

void TestMaps::SerializeWithCachedSizes(
    ::google::protobuf::io::CodedOutputStream* output) const {
  // @@protoc_insertion_point(serialize_start:protobuf_unittest.TestMaps)
  // map<int32, .protobuf_unittest.TestIntIntMap> m_int32 = 1;
  if (!this->m_int32().empty()) {
    typedef ::google::protobuf::Map< ::google::protobuf::int32, ::protobuf_unittest::TestIntIntMap >::const_pointer
        ConstPtr;
    typedef ::google::protobuf::internal::SortItem< ::google::protobuf::int32, ConstPtr > SortItem;
    typedef ::google::protobuf::internal::CompareByFirstField<SortItem> Less;

    if (output->IsSerializationDeterminstic() &&
        this->m_int32().size() > 1) {
      ::google::protobuf::scoped_array<SortItem> items(
          new SortItem[this->m_int32().size()]);
      typedef ::google::protobuf::Map< ::google::protobuf::int32, ::protobuf_unittest::TestIntIntMap >::size_type size_type;
      size_type n = 0;
      for (::google::protobuf::Map< ::google::protobuf::int32, ::protobuf_unittest::TestIntIntMap >::const_iterator
          it = this->m_int32().begin();
          it != this->m_int32().end(); ++it, ++n) {
        items[n] = SortItem(&*it);
      }
      ::std::sort(&items[0], &items[n], Less());
      ::google::protobuf::scoped_ptr<TestMaps_MInt32Entry> entry;
      for (size_type i = 0; i < n; i++) {
        entry.reset(m_int32_.NewEntryWrapper(
            items[i].second->first, items[i].second->second));
        ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
            1, *entry, output);
      }
    } else {
      ::google::protobuf::scoped_ptr<TestMaps_MInt32Entry> entry;
      for (::google::protobuf::Map< ::google::protobuf::int32, ::protobuf_unittest::TestIntIntMap >::const_iterator
          it = this->m_int32().begin();
          it != this->m_int32().end(); ++it) {
        entry.reset(m_int32_.NewEntryWrapper(
            it->first, it->second));
        ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
            1, *entry, output);
      }
    }
  }

  // map<int64, .protobuf_unittest.TestIntIntMap> m_int64 = 2;
  if (!this->m_int64().empty()) {
    typedef ::google::protobuf::Map< ::google::protobuf::int64, ::protobuf_unittest::TestIntIntMap >::const_pointer
        ConstPtr;
    typedef ::google::protobuf::internal::SortItem< ::google::protobuf::int64, ConstPtr > SortItem;
    typedef ::google::protobuf::internal::CompareByFirstField<SortItem> Less;

    if (output->IsSerializationDeterminstic() &&
        this->m_int64().size() > 1) {
      ::google::protobuf::scoped_array<SortItem> items(
          new SortItem[this->m_int64().size()]);
      typedef ::google::protobuf::Map< ::google::protobuf::int64, ::protobuf_unittest::TestIntIntMap >::size_type size_type;
      size_type n = 0;
      for (::google::protobuf::Map< ::google::protobuf::int64, ::protobuf_unittest::TestIntIntMap >::const_iterator
          it = this->m_int64().begin();
          it != this->m_int64().end(); ++it, ++n) {
        items[n] = SortItem(&*it);
      }
      ::std::sort(&items[0], &items[n], Less());
      ::google::protobuf::scoped_ptr<TestMaps_MInt64Entry> entry;
      for (size_type i = 0; i < n; i++) {
        entry.reset(m_int64_.NewEntryWrapper(
            items[i].second->first, items[i].second->second));
        ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
            2, *entry, output);
      }
    } else {
      ::google::protobuf::scoped_ptr<TestMaps_MInt64Entry> entry;
      for (::google::protobuf::Map< ::google::protobuf::int64, ::protobuf_unittest::TestIntIntMap >::const_iterator
          it = this->m_int64().begin();
          it != this->m_int64().end(); ++it) {
        entry.reset(m_int64_.NewEntryWrapper(
            it->first, it->second));
        ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
            2, *entry, output);
      }
    }
  }

  // map<uint32, .protobuf_unittest.TestIntIntMap> m_uint32 = 3;
  if (!this->m_uint32().empty()) {
    typedef ::google::protobuf::Map< ::google::protobuf::uint32, ::protobuf_unittest::TestIntIntMap >::const_pointer
        ConstPtr;
    typedef ::google::protobuf::internal::SortItem< ::google::protobuf::uint32, ConstPtr > SortItem;
    typedef ::google::protobuf::internal::CompareByFirstField<SortItem> Less;

    if (output->IsSerializationDeterminstic() &&
        this->m_uint32().size() > 1) {
      ::google::protobuf::scoped_array<SortItem> items(
          new SortItem[this->m_uint32().size()]);
      typedef ::google::protobuf::Map< ::google::protobuf::uint32, ::protobuf_unittest::TestIntIntMap >::size_type size_type;
      size_type n = 0;
      for (::google::protobuf::Map< ::google::protobuf::uint32, ::protobuf_unittest::TestIntIntMap >::const_iterator
          it = this->m_uint32().begin();
          it != this->m_uint32().end(); ++it, ++n) {
        items[n] = SortItem(&*it);
      }
      ::std::sort(&items[0], &items[n], Less());
      ::google::protobuf::scoped_ptr<TestMaps_MUint32Entry> entry;
      for (size_type i = 0; i < n; i++) {
        entry.reset(m_uint32_.NewEntryWrapper(
            items[i].second->first, items[i].second->second));
        ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
            3, *entry, output);
      }
    } else {
      ::google::protobuf::scoped_ptr<TestMaps_MUint32Entry> entry;
      for (::google::protobuf::Map< ::google::protobuf::uint32, ::protobuf_unittest::TestIntIntMap >::const_iterator
          it = this->m_uint32().begin();
          it != this->m_uint32().end(); ++it) {
        entry.reset(m_uint32_.NewEntryWrapper(
            it->first, it->second));
        ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
            3, *entry, output);
      }
    }
  }

  // map<uint64, .protobuf_unittest.TestIntIntMap> m_uint64 = 4;
  if (!this->m_uint64().empty()) {
    typedef ::google::protobuf::Map< ::google::protobuf::uint64, ::protobuf_unittest::TestIntIntMap >::const_pointer
        ConstPtr;
    typedef ::google::protobuf::internal::SortItem< ::google::protobuf::uint64, ConstPtr > SortItem;
    typedef ::google::protobuf::internal::CompareByFirstField<SortItem> Less;

    if (output->IsSerializationDeterminstic() &&
        this->m_uint64().size() > 1) {
      ::google::protobuf::scoped_array<SortItem> items(
          new SortItem[this->m_uint64().size()]);
      typedef ::google::protobuf::Map< ::google::protobuf::uint64, ::protobuf_unittest::TestIntIntMap >::size_type size_type;
      size_type n = 0;
      for (::google::protobuf::Map< ::google::protobuf::uint64, ::protobuf_unittest::TestIntIntMap >::const_iterator
          it = this->m_uint64().begin();
          it != this->m_uint64().end(); ++it, ++n) {
        items[n] = SortItem(&*it);
      }
      ::std::sort(&items[0], &items[n], Less());
      ::google::protobuf::scoped_ptr<TestMaps_MUint64Entry> entry;
      for (size_type i = 0; i < n; i++) {
        entry.reset(m_uint64_.NewEntryWrapper(
            items[i].second->first, items[i].second->second));
        ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
            4, *entry, output);
      }
    } else {
      ::google::protobuf::scoped_ptr<TestMaps_MUint64Entry> entry;
      for (::google::protobuf::Map< ::google::protobuf::uint64, ::protobuf_unittest::TestIntIntMap >::const_iterator
          it = this->m_uint64().begin();
          it != this->m_uint64().end(); ++it) {
        entry.reset(m_uint64_.NewEntryWrapper(
            it->first, it->second));
        ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
            4, *entry, output);
      }
    }
  }

  // map<sint32, .protobuf_unittest.TestIntIntMap> m_sint32 = 5;
  if (!this->m_sint32().empty()) {
    typedef ::google::protobuf::Map< ::google::protobuf::int32, ::protobuf_unittest::TestIntIntMap >::const_pointer
        ConstPtr;
    typedef ::google::protobuf::internal::SortItem< ::google::protobuf::int32, ConstPtr > SortItem;
    typedef ::google::protobuf::internal::CompareByFirstField<SortItem> Less;

    if (output->IsSerializationDeterminstic() &&
        this->m_sint32().size() > 1) {
      ::google::protobuf::scoped_array<SortItem> items(
          new SortItem[this->m_sint32().size()]);
      typedef ::google::protobuf::Map< ::google::protobuf::int32, ::protobuf_unittest::TestIntIntMap >::size_type size_type;
      size_type n = 0;
      for (::google::protobuf::Map< ::google::protobuf::int32, ::protobuf_unittest::TestIntIntMap >::const_iterator
          it = this->m_sint32().begin();
          it != this->m_sint32().end(); ++it, ++n) {
        items[n] = SortItem(&*it);
      }
      ::std::sort(&items[0], &items[n], Less());
      ::google::protobuf::scoped_ptr<TestMaps_MSint32Entry> entry;
      for (size_type i = 0; i < n; i++) {
        entry.reset(m_sint32_.NewEntryWrapper(
            items[i].second->first, items[i].second->second));
        ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
            5, *entry, output);
      }
    } else {
      ::google::protobuf::scoped_ptr<TestMaps_MSint32Entry> entry;
      for (::google::protobuf::Map< ::google::protobuf::int32, ::protobuf_unittest::TestIntIntMap >::const_iterator
          it = this->m_sint32().begin();
          it != this->m_sint32().end(); ++it) {
        entry.reset(m_sint32_.NewEntryWrapper(
            it->first, it->second));
        ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
            5, *entry, output);
      }
    }
  }

  // map<sint64, .protobuf_unittest.TestIntIntMap> m_sint64 = 6;
  if (!this->m_sint64().empty()) {
    typedef ::google::protobuf::Map< ::google::protobuf::int64, ::protobuf_unittest::TestIntIntMap >::const_pointer
        ConstPtr;
    typedef ::google::protobuf::internal::SortItem< ::google::protobuf::int64, ConstPtr > SortItem;
    typedef ::google::protobuf::internal::CompareByFirstField<SortItem> Less;

    if (output->IsSerializationDeterminstic() &&
        this->m_sint64().size() > 1) {
      ::google::protobuf::scoped_array<SortItem> items(
          new SortItem[this->m_sint64().size()]);
      typedef ::google::protobuf::Map< ::google::protobuf::int64, ::protobuf_unittest::TestIntIntMap >::size_type size_type;
      size_type n = 0;
      for (::google::protobuf::Map< ::google::protobuf::int64, ::protobuf_unittest::TestIntIntMap >::const_iterator
          it = this->m_sint64().begin();
          it != this->m_sint64().end(); ++it, ++n) {
        items[n] = SortItem(&*it);
      }
      ::std::sort(&items[0], &items[n], Less());
      ::google::protobuf::scoped_ptr<TestMaps_MSint64Entry> entry;
      for (size_type i = 0; i < n; i++) {
        entry.reset(m_sint64_.NewEntryWrapper(
            items[i].second->first, items[i].second->second));
        ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
            6, *entry, output);
      }
    } else {
      ::google::protobuf::scoped_ptr<TestMaps_MSint64Entry> entry;
      for (::google::protobuf::Map< ::google::protobuf::int64, ::protobuf_unittest::TestIntIntMap >::const_iterator
          it = this->m_sint64().begin();
          it != this->m_sint64().end(); ++it) {
        entry.reset(m_sint64_.NewEntryWrapper(
            it->first, it->second));
        ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
            6, *entry, output);
      }
    }
  }

  // map<fixed32, .protobuf_unittest.TestIntIntMap> m_fixed32 = 7;
  if (!this->m_fixed32().empty()) {
    typedef ::google::protobuf::Map< ::google::protobuf::uint32, ::protobuf_unittest::TestIntIntMap >::const_pointer
        ConstPtr;
    typedef ::google::protobuf::internal::SortItem< ::google::protobuf::uint32, ConstPtr > SortItem;
    typedef ::google::protobuf::internal::CompareByFirstField<SortItem> Less;

    if (output->IsSerializationDeterminstic() &&
        this->m_fixed32().size() > 1) {
      ::google::protobuf::scoped_array<SortItem> items(
          new SortItem[this->m_fixed32().size()]);
      typedef ::google::protobuf::Map< ::google::protobuf::uint32, ::protobuf_unittest::TestIntIntMap >::size_type size_type;
      size_type n = 0;
      for (::google::protobuf::Map< ::google::protobuf::uint32, ::protobuf_unittest::TestIntIntMap >::const_iterator
          it = this->m_fixed32().begin();
          it != this->m_fixed32().end(); ++it, ++n) {
        items[n] = SortItem(&*it);
      }
      ::std::sort(&items[0], &items[n], Less());
      ::google::protobuf::scoped_ptr<TestMaps_MFixed32Entry> entry;
      for (size_type i = 0; i < n; i++) {
        entry.reset(m_fixed32_.NewEntryWrapper(
            items[i].second->first, items[i].second->second));
        ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
            7, *entry, output);
      }
    } else {
      ::google::protobuf::scoped_ptr<TestMaps_MFixed32Entry> entry;
      for (::google::protobuf::Map< ::google::protobuf::uint32, ::protobuf_unittest::TestIntIntMap >::const_iterator
          it = this->m_fixed32().begin();
          it != this->m_fixed32().end(); ++it) {
        entry.reset(m_fixed32_.NewEntryWrapper(
            it->first, it->second));
        ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
            7, *entry, output);
      }
    }
  }

  // map<fixed64, .protobuf_unittest.TestIntIntMap> m_fixed64 = 8;
  if (!this->m_fixed64().empty()) {
    typedef ::google::protobuf::Map< ::google::protobuf::uint64, ::protobuf_unittest::TestIntIntMap >::const_pointer
        ConstPtr;
    typedef ::google::protobuf::internal::SortItem< ::google::protobuf::uint64, ConstPtr > SortItem;
    typedef ::google::protobuf::internal::CompareByFirstField<SortItem> Less;

    if (output->IsSerializationDeterminstic() &&
        this->m_fixed64().size() > 1) {
      ::google::protobuf::scoped_array<SortItem> items(
          new SortItem[this->m_fixed64().size()]);
      typedef ::google::protobuf::Map< ::google::protobuf::uint64, ::protobuf_unittest::TestIntIntMap >::size_type size_type;
      size_type n = 0;
      for (::google::protobuf::Map< ::google::protobuf::uint64, ::protobuf_unittest::TestIntIntMap >::const_iterator
          it = this->m_fixed64().begin();
          it != this->m_fixed64().end(); ++it, ++n) {
        items[n] = SortItem(&*it);
      }
      ::std::sort(&items[0], &items[n], Less());
      ::google::protobuf::scoped_ptr<TestMaps_MFixed64Entry> entry;
      for (size_type i = 0; i < n; i++) {
        entry.reset(m_fixed64_.NewEntryWrapper(
            items[i].second->first, items[i].second->second));
        ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
            8, *entry, output);
      }
    } else {
      ::google::protobuf::scoped_ptr<TestMaps_MFixed64Entry> entry;
      for (::google::protobuf::Map< ::google::protobuf::uint64, ::protobuf_unittest::TestIntIntMap >::const_iterator
          it = this->m_fixed64().begin();
          it != this->m_fixed64().end(); ++it) {
        entry.reset(m_fixed64_.NewEntryWrapper(
            it->first, it->second));
        ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
            8, *entry, output);
      }
    }
  }

  // map<sfixed32, .protobuf_unittest.TestIntIntMap> m_sfixed32 = 9;
  if (!this->m_sfixed32().empty()) {
    typedef ::google::protobuf::Map< ::google::protobuf::int32, ::protobuf_unittest::TestIntIntMap >::const_pointer
        ConstPtr;
    typedef ::google::protobuf::internal::SortItem< ::google::protobuf::int32, ConstPtr > SortItem;
    typedef ::google::protobuf::internal::CompareByFirstField<SortItem> Less;

    if (output->IsSerializationDeterminstic() &&
        this->m_sfixed32().size() > 1) {
      ::google::protobuf::scoped_array<SortItem> items(
          new SortItem[this->m_sfixed32().size()]);
      typedef ::google::protobuf::Map< ::google::protobuf::int32, ::protobuf_unittest::TestIntIntMap >::size_type size_type;
      size_type n = 0;
      for (::google::protobuf::Map< ::google::protobuf::int32, ::protobuf_unittest::TestIntIntMap >::const_iterator
          it = this->m_sfixed32().begin();
          it != this->m_sfixed32().end(); ++it, ++n) {
        items[n] = SortItem(&*it);
      }
      ::std::sort(&items[0], &items[n], Less());
      ::google::protobuf::scoped_ptr<TestMaps_MSfixed32Entry> entry;
      for (size_type i = 0; i < n; i++) {
        entry.reset(m_sfixed32_.NewEntryWrapper(
            items[i].second->first, items[i].second->second));
        ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
            9, *entry, output);
      }
    } else {
      ::google::protobuf::scoped_ptr<TestMaps_MSfixed32Entry> entry;
      for (::google::protobuf::Map< ::google::protobuf::int32, ::protobuf_unittest::TestIntIntMap >::const_iterator
          it = this->m_sfixed32().begin();
          it != this->m_sfixed32().end(); ++it) {
        entry.reset(m_sfixed32_.NewEntryWrapper(
            it->first, it->second));
        ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
            9, *entry, output);
      }
    }
  }

  // map<sfixed64, .protobuf_unittest.TestIntIntMap> m_sfixed64 = 10;
  if (!this->m_sfixed64().empty()) {
    typedef ::google::protobuf::Map< ::google::protobuf::int64, ::protobuf_unittest::TestIntIntMap >::const_pointer
        ConstPtr;
    typedef ::google::protobuf::internal::SortItem< ::google::protobuf::int64, ConstPtr > SortItem;
    typedef ::google::protobuf::internal::CompareByFirstField<SortItem> Less;

    if (output->IsSerializationDeterminstic() &&
        this->m_sfixed64().size() > 1) {
      ::google::protobuf::scoped_array<SortItem> items(
          new SortItem[this->m_sfixed64().size()]);
      typedef ::google::protobuf::Map< ::google::protobuf::int64, ::protobuf_unittest::TestIntIntMap >::size_type size_type;
      size_type n = 0;
      for (::google::protobuf::Map< ::google::protobuf::int64, ::protobuf_unittest::TestIntIntMap >::const_iterator
          it = this->m_sfixed64().begin();
          it != this->m_sfixed64().end(); ++it, ++n) {
        items[n] = SortItem(&*it);
      }
      ::std::sort(&items[0], &items[n], Less());
      ::google::protobuf::scoped_ptr<TestMaps_MSfixed64Entry> entry;
      for (size_type i = 0; i < n; i++) {
        entry.reset(m_sfixed64_.NewEntryWrapper(
            items[i].second->first, items[i].second->second));
        ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
            10, *entry, output);
      }
    } else {
      ::google::protobuf::scoped_ptr<TestMaps_MSfixed64Entry> entry;
      for (::google::protobuf::Map< ::google::protobuf::int64, ::protobuf_unittest::TestIntIntMap >::const_iterator
          it = this->m_sfixed64().begin();
          it != this->m_sfixed64().end(); ++it) {
        entry.reset(m_sfixed64_.NewEntryWrapper(
            it->first, it->second));
        ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
            10, *entry, output);
      }
    }
  }

  // map<bool, .protobuf_unittest.TestIntIntMap> m_bool = 11;
  if (!this->m_bool().empty()) {
    typedef ::google::protobuf::Map< bool, ::protobuf_unittest::TestIntIntMap >::const_pointer
        ConstPtr;
    typedef ::google::protobuf::internal::SortItem< bool, ConstPtr > SortItem;
    typedef ::google::protobuf::internal::CompareByFirstField<SortItem> Less;

    if (output->IsSerializationDeterminstic() &&
        this->m_bool().size() > 1) {
      ::google::protobuf::scoped_array<SortItem> items(
          new SortItem[this->m_bool().size()]);
      typedef ::google::protobuf::Map< bool, ::protobuf_unittest::TestIntIntMap >::size_type size_type;
      size_type n = 0;
      for (::google::protobuf::Map< bool, ::protobuf_unittest::TestIntIntMap >::const_iterator
          it = this->m_bool().begin();
          it != this->m_bool().end(); ++it, ++n) {
        items[n] = SortItem(&*it);
      }
      ::std::sort(&items[0], &items[n], Less());
      ::google::protobuf::scoped_ptr<TestMaps_MBoolEntry> entry;
      for (size_type i = 0; i < n; i++) {
        entry.reset(m_bool_.NewEntryWrapper(
            items[i].second->first, items[i].second->second));
        ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
            11, *entry, output);
      }
    } else {
      ::google::protobuf::scoped_ptr<TestMaps_MBoolEntry> entry;
      for (::google::protobuf::Map< bool, ::protobuf_unittest::TestIntIntMap >::const_iterator
          it = this->m_bool().begin();
          it != this->m_bool().end(); ++it) {
        entry.reset(m_bool_.NewEntryWrapper(
            it->first, it->second));
        ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
            11, *entry, output);
      }
    }
  }

  // map<string, .protobuf_unittest.TestIntIntMap> m_string = 12;
  if (!this->m_string().empty()) {
    typedef ::google::protobuf::Map< ::std::string, ::protobuf_unittest::TestIntIntMap >::const_pointer
        ConstPtr;
    typedef ConstPtr SortItem;
    typedef ::google::protobuf::internal::CompareByDerefFirst<SortItem> Less;
    struct Utf8Check {
      static void Check(ConstPtr p) {
        ::google::protobuf::internal::WireFormat::VerifyUTF8StringNamedField(
          p->first.data(), p->first.length(),
          ::google::protobuf::internal::WireFormat::SERIALIZE,
          "protobuf_unittest.TestMaps.MStringEntry.key");
      }
    };

    if (output->IsSerializationDeterminstic() &&
        this->m_string().size() > 1) {
      ::google::protobuf::scoped_array<SortItem> items(
          new SortItem[this->m_string().size()]);
      typedef ::google::protobuf::Map< ::std::string, ::protobuf_unittest::TestIntIntMap >::size_type size_type;
      size_type n = 0;
      for (::google::protobuf::Map< ::std::string, ::protobuf_unittest::TestIntIntMap >::const_iterator
          it = this->m_string().begin();
          it != this->m_string().end(); ++it, ++n) {
        items[n] = SortItem(&*it);
      }
      ::std::sort(&items[0], &items[n], Less());
      ::google::protobuf::scoped_ptr<TestMaps_MStringEntry> entry;
      for (size_type i = 0; i < n; i++) {
        entry.reset(m_string_.NewEntryWrapper(
            items[i]->first, items[i]->second));
        ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
            12, *entry, output);
        Utf8Check::Check(items[i]);
      }
    } else {
      ::google::protobuf::scoped_ptr<TestMaps_MStringEntry> entry;
      for (::google::protobuf::Map< ::std::string, ::protobuf_unittest::TestIntIntMap >::const_iterator
          it = this->m_string().begin();
          it != this->m_string().end(); ++it) {
        entry.reset(m_string_.NewEntryWrapper(
            it->first, it->second));
        ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
            12, *entry, output);
        Utf8Check::Check(&*it);
      }
    }
  }

  if (_internal_metadata_.have_unknown_fields()) {
    ::google::protobuf::internal::WireFormat::SerializeUnknownFields(
        unknown_fields(), output);
  }
  // @@protoc_insertion_point(serialize_end:protobuf_unittest.TestMaps)
}

::google::protobuf::uint8* TestMaps::InternalSerializeWithCachedSizesToArray(
    bool deterministic, ::google::protobuf::uint8* target) const {
  (void)deterministic; // Unused
  // @@protoc_insertion_point(serialize_to_array_start:protobuf_unittest.TestMaps)
  // map<int32, .protobuf_unittest.TestIntIntMap> m_int32 = 1;
  if (!this->m_int32().empty()) {
    typedef ::google::protobuf::Map< ::google::protobuf::int32, ::protobuf_unittest::TestIntIntMap >::const_pointer
        ConstPtr;
    typedef ::google::protobuf::internal::SortItem< ::google::protobuf::int32, ConstPtr > SortItem;
    typedef ::google::protobuf::internal::CompareByFirstField<SortItem> Less;

    if (deterministic &&
        this->m_int32().size() > 1) {
      ::google::protobuf::scoped_array<SortItem> items(
          new SortItem[this->m_int32().size()]);
      typedef ::google::protobuf::Map< ::google::protobuf::int32, ::protobuf_unittest::TestIntIntMap >::size_type size_type;
      size_type n = 0;
      for (::google::protobuf::Map< ::google::protobuf::int32, ::protobuf_unittest::TestIntIntMap >::const_iterator
          it = this->m_int32().begin();
          it != this->m_int32().end(); ++it, ++n) {
        items[n] = SortItem(&*it);
      }
      ::std::sort(&items[0], &items[n], Less());
      ::google::protobuf::scoped_ptr<TestMaps_MInt32Entry> entry;
      for (size_type i = 0; i < n; i++) {
        entry.reset(m_int32_.NewEntryWrapper(
            items[i].second->first, items[i].second->second));
        target = ::google::protobuf::internal::WireFormatLite::
                   InternalWriteMessageNoVirtualToArray(
                       1, *entry, deterministic, target);
;
      }
    } else {
      ::google::protobuf::scoped_ptr<TestMaps_MInt32Entry> entry;
      for (::google::protobuf::Map< ::google::protobuf::int32, ::protobuf_unittest::TestIntIntMap >::const_iterator
          it = this->m_int32().begin();
          it != this->m_int32().end(); ++it) {
        entry.reset(m_int32_.NewEntryWrapper(
            it->first, it->second));
        target = ::google::protobuf::internal::WireFormatLite::
                   InternalWriteMessageNoVirtualToArray(
                       1, *entry, deterministic, target);
;
      }
    }
  }

  // map<int64, .protobuf_unittest.TestIntIntMap> m_int64 = 2;
  if (!this->m_int64().empty()) {
    typedef ::google::protobuf::Map< ::google::protobuf::int64, ::protobuf_unittest::TestIntIntMap >::const_pointer
        ConstPtr;
    typedef ::google::protobuf::internal::SortItem< ::google::protobuf::int64, ConstPtr > SortItem;
    typedef ::google::protobuf::internal::CompareByFirstField<SortItem> Less;

    if (deterministic &&
        this->m_int64().size() > 1) {
      ::google::protobuf::scoped_array<SortItem> items(
          new SortItem[this->m_int64().size()]);
      typedef ::google::protobuf::Map< ::google::protobuf::int64, ::protobuf_unittest::TestIntIntMap >::size_type size_type;
      size_type n = 0;
      for (::google::protobuf::Map< ::google::protobuf::int64, ::protobuf_unittest::TestIntIntMap >::const_iterator
          it = this->m_int64().begin();
          it != this->m_int64().end(); ++it, ++n) {
        items[n] = SortItem(&*it);
      }
      ::std::sort(&items[0], &items[n], Less());
      ::google::protobuf::scoped_ptr<TestMaps_MInt64Entry> entry;
      for (size_type i = 0; i < n; i++) {
        entry.reset(m_int64_.NewEntryWrapper(
            items[i].second->first, items[i].second->second));
        target = ::google::protobuf::internal::WireFormatLite::
                   InternalWriteMessageNoVirtualToArray(
                       2, *entry, deterministic, target);
;
      }
    } else {
      ::google::protobuf::scoped_ptr<TestMaps_MInt64Entry> entry;
      for (::google::protobuf::Map< ::google::protobuf::int64, ::protobuf_unittest::TestIntIntMap >::const_iterator
          it = this->m_int64().begin();
          it != this->m_int64().end(); ++it) {
        entry.reset(m_int64_.NewEntryWrapper(
            it->first, it->second));
        target = ::google::protobuf::internal::WireFormatLite::
                   InternalWriteMessageNoVirtualToArray(
                       2, *entry, deterministic, target);
;
      }
    }
  }

  // map<uint32, .protobuf_unittest.TestIntIntMap> m_uint32 = 3;
  if (!this->m_uint32().empty()) {
    typedef ::google::protobuf::Map< ::google::protobuf::uint32, ::protobuf_unittest::TestIntIntMap >::const_pointer
        ConstPtr;
    typedef ::google::protobuf::internal::SortItem< ::google::protobuf::uint32, ConstPtr > SortItem;
    typedef ::google::protobuf::internal::CompareByFirstField<SortItem> Less;

    if (deterministic &&
        this->m_uint32().size() > 1) {
      ::google::protobuf::scoped_array<SortItem> items(
          new SortItem[this->m_uint32().size()]);
      typedef ::google::protobuf::Map< ::google::protobuf::uint32, ::protobuf_unittest::TestIntIntMap >::size_type size_type;
      size_type n = 0;
      for (::google::protobuf::Map< ::google::protobuf::uint32, ::protobuf_unittest::TestIntIntMap >::const_iterator
          it = this->m_uint32().begin();
          it != this->m_uint32().end(); ++it, ++n) {
        items[n] = SortItem(&*it);
      }
      ::std::sort(&items[0], &items[n], Less());
      ::google::protobuf::scoped_ptr<TestMaps_MUint32Entry> entry;
      for (size_type i = 0; i < n; i++) {
        entry.reset(m_uint32_.NewEntryWrapper(
            items[i].second->first, items[i].second->second));
        target = ::google::protobuf::internal::WireFormatLite::
                   InternalWriteMessageNoVirtualToArray(
                       3, *entry, deterministic, target);
;
      }
    } else {
      ::google::protobuf::scoped_ptr<TestMaps_MUint32Entry> entry;
      for (::google::protobuf::Map< ::google::protobuf::uint32, ::protobuf_unittest::TestIntIntMap >::const_iterator
          it = this->m_uint32().begin();
          it != this->m_uint32().end(); ++it) {
        entry.reset(m_uint32_.NewEntryWrapper(
            it->first, it->second));
        target = ::google::protobuf::internal::WireFormatLite::
                   InternalWriteMessageNoVirtualToArray(
                       3, *entry, deterministic, target);
;
      }
    }
  }

  // map<uint64, .protobuf_unittest.TestIntIntMap> m_uint64 = 4;
  if (!this->m_uint64().empty()) {
    typedef ::google::protobuf::Map< ::google::protobuf::uint64, ::protobuf_unittest::TestIntIntMap >::const_pointer
        ConstPtr;
    typedef ::google::protobuf::internal::SortItem< ::google::protobuf::uint64, ConstPtr > SortItem;
    typedef ::google::protobuf::internal::CompareByFirstField<SortItem> Less;

    if (deterministic &&
        this->m_uint64().size() > 1) {
      ::google::protobuf::scoped_array<SortItem> items(
          new SortItem[this->m_uint64().size()]);
      typedef ::google::protobuf::Map< ::google::protobuf::uint64, ::protobuf_unittest::TestIntIntMap >::size_type size_type;
      size_type n = 0;
      for (::google::protobuf::Map< ::google::protobuf::uint64, ::protobuf_unittest::TestIntIntMap >::const_iterator
          it = this->m_uint64().begin();
          it != this->m_uint64().end(); ++it, ++n) {
        items[n] = SortItem(&*it);
      }
      ::std::sort(&items[0], &items[n], Less());
      ::google::protobuf::scoped_ptr<TestMaps_MUint64Entry> entry;
      for (size_type i = 0; i < n; i++) {
        entry.reset(m_uint64_.NewEntryWrapper(
            items[i].second->first, items[i].second->second));
        target = ::google::protobuf::internal::WireFormatLite::
                   InternalWriteMessageNoVirtualToArray(
                       4, *entry, deterministic, target);
;
      }
    } else {
      ::google::protobuf::scoped_ptr<TestMaps_MUint64Entry> entry;
      for (::google::protobuf::Map< ::google::protobuf::uint64, ::protobuf_unittest::TestIntIntMap >::const_iterator
          it = this->m_uint64().begin();
          it != this->m_uint64().end(); ++it) {
        entry.reset(m_uint64_.NewEntryWrapper(
            it->first, it->second));
        target = ::google::protobuf::internal::WireFormatLite::
                   InternalWriteMessageNoVirtualToArray(
                       4, *entry, deterministic, target);
;
      }
    }
  }

  // map<sint32, .protobuf_unittest.TestIntIntMap> m_sint32 = 5;
  if (!this->m_sint32().empty()) {
    typedef ::google::protobuf::Map< ::google::protobuf::int32, ::protobuf_unittest::TestIntIntMap >::const_pointer
        ConstPtr;
    typedef ::google::protobuf::internal::SortItem< ::google::protobuf::int32, ConstPtr > SortItem;
    typedef ::google::protobuf::internal::CompareByFirstField<SortItem> Less;

    if (deterministic &&
        this->m_sint32().size() > 1) {
      ::google::protobuf::scoped_array<SortItem> items(
          new SortItem[this->m_sint32().size()]);
      typedef ::google::protobuf::Map< ::google::protobuf::int32, ::protobuf_unittest::TestIntIntMap >::size_type size_type;
      size_type n = 0;
      for (::google::protobuf::Map< ::google::protobuf::int32, ::protobuf_unittest::TestIntIntMap >::const_iterator
          it = this->m_sint32().begin();
          it != this->m_sint32().end(); ++it, ++n) {
        items[n] = SortItem(&*it);
      }
      ::std::sort(&items[0], &items[n], Less());
      ::google::protobuf::scoped_ptr<TestMaps_MSint32Entry> entry;
      for (size_type i = 0; i < n; i++) {
        entry.reset(m_sint32_.NewEntryWrapper(
            items[i].second->first, items[i].second->second));
        target = ::google::protobuf::internal::WireFormatLite::
                   InternalWriteMessageNoVirtualToArray(
                       5, *entry, deterministic, target);
;
      }
    } else {
      ::google::protobuf::scoped_ptr<TestMaps_MSint32Entry> entry;
      for (::google::protobuf::Map< ::google::protobuf::int32, ::protobuf_unittest::TestIntIntMap >::const_iterator
          it = this->m_sint32().begin();
          it != this->m_sint32().end(); ++it) {
        entry.reset(m_sint32_.NewEntryWrapper(
            it->first, it->second));
        target = ::google::protobuf::internal::WireFormatLite::
                   InternalWriteMessageNoVirtualToArray(
                       5, *entry, deterministic, target);
;
      }
    }
  }

  // map<sint64, .protobuf_unittest.TestIntIntMap> m_sint64 = 6;
  if (!this->m_sint64().empty()) {
    typedef ::google::protobuf::Map< ::google::protobuf::int64, ::protobuf_unittest::TestIntIntMap >::const_pointer
        ConstPtr;
    typedef ::google::protobuf::internal::SortItem< ::google::protobuf::int64, ConstPtr > SortItem;
    typedef ::google::protobuf::internal::CompareByFirstField<SortItem> Less;

    if (deterministic &&
        this->m_sint64().size() > 1) {
      ::google::protobuf::scoped_array<SortItem> items(
          new SortItem[this->m_sint64().size()]);
      typedef ::google::protobuf::Map< ::google::protobuf::int64, ::protobuf_unittest::TestIntIntMap >::size_type size_type;
      size_type n = 0;
      for (::google::protobuf::Map< ::google::protobuf::int64, ::protobuf_unittest::TestIntIntMap >::const_iterator
          it = this->m_sint64().begin();
          it != this->m_sint64().end(); ++it, ++n) {
        items[n] = SortItem(&*it);
      }
      ::std::sort(&items[0], &items[n], Less());
      ::google::protobuf::scoped_ptr<TestMaps_MSint64Entry> entry;
      for (size_type i = 0; i < n; i++) {
        entry.reset(m_sint64_.NewEntryWrapper(
            items[i].second->first, items[i].second->second));
        target = ::google::protobuf::internal::WireFormatLite::
                   InternalWriteMessageNoVirtualToArray(
                       6, *entry, deterministic, target);
;
      }
    } else {
      ::google::protobuf::scoped_ptr<TestMaps_MSint64Entry> entry;
      for (::google::protobuf::Map< ::google::protobuf::int64, ::protobuf_unittest::TestIntIntMap >::const_iterator
          it = this->m_sint64().begin();
          it != this->m_sint64().end(); ++it) {
        entry.reset(m_sint64_.NewEntryWrapper(
            it->first, it->second));
        target = ::google::protobuf::internal::WireFormatLite::
                   InternalWriteMessageNoVirtualToArray(
                       6, *entry, deterministic, target);
;
      }
    }
  }

  // map<fixed32, .protobuf_unittest.TestIntIntMap> m_fixed32 = 7;
  if (!this->m_fixed32().empty()) {
    typedef ::google::protobuf::Map< ::google::protobuf::uint32, ::protobuf_unittest::TestIntIntMap >::const_pointer
        ConstPtr;
    typedef ::google::protobuf::internal::SortItem< ::google::protobuf::uint32, ConstPtr > SortItem;
    typedef ::google::protobuf::internal::CompareByFirstField<SortItem> Less;

    if (deterministic &&
        this->m_fixed32().size() > 1) {
      ::google::protobuf::scoped_array<SortItem> items(
          new SortItem[this->m_fixed32().size()]);
      typedef ::google::protobuf::Map< ::google::protobuf::uint32, ::protobuf_unittest::TestIntIntMap >::size_type size_type;
      size_type n = 0;
      for (::google::protobuf::Map< ::google::protobuf::uint32, ::protobuf_unittest::TestIntIntMap >::const_iterator
          it = this->m_fixed32().begin();
          it != this->m_fixed32().end(); ++it, ++n) {
        items[n] = SortItem(&*it);
      }
      ::std::sort(&items[0], &items[n], Less());
      ::google::protobuf::scoped_ptr<TestMaps_MFixed32Entry> entry;
      for (size_type i = 0; i < n; i++) {
        entry.reset(m_fixed32_.NewEntryWrapper(
            items[i].second->first, items[i].second->second));
        target = ::google::protobuf::internal::WireFormatLite::
                   InternalWriteMessageNoVirtualToArray(
                       7, *entry, deterministic, target);
;
      }
    } else {
      ::google::protobuf::scoped_ptr<TestMaps_MFixed32Entry> entry;
      for (::google::protobuf::Map< ::google::protobuf::uint32, ::protobuf_unittest::TestIntIntMap >::const_iterator
          it = this->m_fixed32().begin();
          it != this->m_fixed32().end(); ++it) {
        entry.reset(m_fixed32_.NewEntryWrapper(
            it->first, it->second));
        target = ::google::protobuf::internal::WireFormatLite::
                   InternalWriteMessageNoVirtualToArray(
                       7, *entry, deterministic, target);
;
      }
    }
  }

  // map<fixed64, .protobuf_unittest.TestIntIntMap> m_fixed64 = 8;
  if (!this->m_fixed64().empty()) {
    typedef ::google::protobuf::Map< ::google::protobuf::uint64, ::protobuf_unittest::TestIntIntMap >::const_pointer
        ConstPtr;
    typedef ::google::protobuf::internal::SortItem< ::google::protobuf::uint64, ConstPtr > SortItem;
    typedef ::google::protobuf::internal::CompareByFirstField<SortItem> Less;

    if (deterministic &&
        this->m_fixed64().size() > 1) {
      ::google::protobuf::scoped_array<SortItem> items(
          new SortItem[this->m_fixed64().size()]);
      typedef ::google::protobuf::Map< ::google::protobuf::uint64, ::protobuf_unittest::TestIntIntMap >::size_type size_type;
      size_type n = 0;
      for (::google::protobuf::Map< ::google::protobuf::uint64, ::protobuf_unittest::TestIntIntMap >::const_iterator
          it = this->m_fixed64().begin();
          it != this->m_fixed64().end(); ++it, ++n) {
        items[n] = SortItem(&*it);
      }
      ::std::sort(&items[0], &items[n], Less());
      ::google::protobuf::scoped_ptr<TestMaps_MFixed64Entry> entry;
      for (size_type i = 0; i < n; i++) {
        entry.reset(m_fixed64_.NewEntryWrapper(
            items[i].second->first, items[i].second->second));
        target = ::google::protobuf::internal::WireFormatLite::
                   InternalWriteMessageNoVirtualToArray(
                       8, *entry, deterministic, target);
;
      }
    } else {
      ::google::protobuf::scoped_ptr<TestMaps_MFixed64Entry> entry;
      for (::google::protobuf::Map< ::google::protobuf::uint64, ::protobuf_unittest::TestIntIntMap >::const_iterator
          it = this->m_fixed64().begin();
          it != this->m_fixed64().end(); ++it) {
        entry.reset(m_fixed64_.NewEntryWrapper(
            it->first, it->second));
        target = ::google::protobuf::internal::WireFormatLite::
                   InternalWriteMessageNoVirtualToArray(
                       8, *entry, deterministic, target);
;
      }
    }
  }

  // map<sfixed32, .protobuf_unittest.TestIntIntMap> m_sfixed32 = 9;
  if (!this->m_sfixed32().empty()) {
    typedef ::google::protobuf::Map< ::google::protobuf::int32, ::protobuf_unittest::TestIntIntMap >::const_pointer
        ConstPtr;
    typedef ::google::protobuf::internal::SortItem< ::google::protobuf::int32, ConstPtr > SortItem;
    typedef ::google::protobuf::internal::CompareByFirstField<SortItem> Less;

    if (deterministic &&
        this->m_sfixed32().size() > 1) {
      ::google::protobuf::scoped_array<SortItem> items(
          new SortItem[this->m_sfixed32().size()]);
      typedef ::google::protobuf::Map< ::google::protobuf::int32, ::protobuf_unittest::TestIntIntMap >::size_type size_type;
      size_type n = 0;
      for (::google::protobuf::Map< ::google::protobuf::int32, ::protobuf_unittest::TestIntIntMap >::const_iterator
          it = this->m_sfixed32().begin();
          it != this->m_sfixed32().end(); ++it, ++n) {
        items[n] = SortItem(&*it);
      }
      ::std::sort(&items[0], &items[n], Less());
      ::google::protobuf::scoped_ptr<TestMaps_MSfixed32Entry> entry;
      for (size_type i = 0; i < n; i++) {
        entry.reset(m_sfixed32_.NewEntryWrapper(
            items[i].second->first, items[i].second->second));
        target = ::google::protobuf::internal::WireFormatLite::
                   InternalWriteMessageNoVirtualToArray(
                       9, *entry, deterministic, target);
;
      }
    } else {
      ::google::protobuf::scoped_ptr<TestMaps_MSfixed32Entry> entry;
      for (::google::protobuf::Map< ::google::protobuf::int32, ::protobuf_unittest::TestIntIntMap >::const_iterator
          it = this->m_sfixed32().begin();
          it != this->m_sfixed32().end(); ++it) {
        entry.reset(m_sfixed32_.NewEntryWrapper(
            it->first, it->second));
        target = ::google::protobuf::internal::WireFormatLite::
                   InternalWriteMessageNoVirtualToArray(
                       9, *entry, deterministic, target);
;
      }
    }
  }

  // map<sfixed64, .protobuf_unittest.TestIntIntMap> m_sfixed64 = 10;
  if (!this->m_sfixed64().empty()) {
    typedef ::google::protobuf::Map< ::google::protobuf::int64, ::protobuf_unittest::TestIntIntMap >::const_pointer
        ConstPtr;
    typedef ::google::protobuf::internal::SortItem< ::google::protobuf::int64, ConstPtr > SortItem;
    typedef ::google::protobuf::internal::CompareByFirstField<SortItem> Less;

    if (deterministic &&
        this->m_sfixed64().size() > 1) {
      ::google::protobuf::scoped_array<SortItem> items(
          new SortItem[this->m_sfixed64().size()]);
      typedef ::google::protobuf::Map< ::google::protobuf::int64, ::protobuf_unittest::TestIntIntMap >::size_type size_type;
      size_type n = 0;
      for (::google::protobuf::Map< ::google::protobuf::int64, ::protobuf_unittest::TestIntIntMap >::const_iterator
          it = this->m_sfixed64().begin();
          it != this->m_sfixed64().end(); ++it, ++n) {
        items[n] = SortItem(&*it);
      }
      ::std::sort(&items[0], &items[n], Less());
      ::google::protobuf::scoped_ptr<TestMaps_MSfixed64Entry> entry;
      for (size_type i = 0; i < n; i++) {
        entry.reset(m_sfixed64_.NewEntryWrapper(
            items[i].second->first, items[i].second->second));
        target = ::google::protobuf::internal::WireFormatLite::
                   InternalWriteMessageNoVirtualToArray(
                       10, *entry, deterministic, target);
;
      }
    } else {
      ::google::protobuf::scoped_ptr<TestMaps_MSfixed64Entry> entry;
      for (::google::protobuf::Map< ::google::protobuf::int64, ::protobuf_unittest::TestIntIntMap >::const_iterator
          it = this->m_sfixed64().begin();
          it != this->m_sfixed64().end(); ++it) {
        entry.reset(m_sfixed64_.NewEntryWrapper(
            it->first, it->second));
        target = ::google::protobuf::internal::WireFormatLite::
                   InternalWriteMessageNoVirtualToArray(
                       10, *entry, deterministic, target);
;
      }
    }
  }

  // map<bool, .protobuf_unittest.TestIntIntMap> m_bool = 11;
  if (!this->m_bool().empty()) {
    typedef ::google::protobuf::Map< bool, ::protobuf_unittest::TestIntIntMap >::const_pointer
        ConstPtr;
    typedef ::google::protobuf::internal::SortItem< bool, ConstPtr > SortItem;
    typedef ::google::protobuf::internal::CompareByFirstField<SortItem> Less;

    if (deterministic &&
        this->m_bool().size() > 1) {
      ::google::protobuf::scoped_array<SortItem> items(
          new SortItem[this->m_bool().size()]);
      typedef ::google::protobuf::Map< bool, ::protobuf_unittest::TestIntIntMap >::size_type size_type;
      size_type n = 0;
      for (::google::protobuf::Map< bool, ::protobuf_unittest::TestIntIntMap >::const_iterator
          it = this->m_bool().begin();
          it != this->m_bool().end(); ++it, ++n) {
        items[n] = SortItem(&*it);
      }
      ::std::sort(&items[0], &items[n], Less());
      ::google::protobuf::scoped_ptr<TestMaps_MBoolEntry> entry;
      for (size_type i = 0; i < n; i++) {
        entry.reset(m_bool_.NewEntryWrapper(
            items[i].second->first, items[i].second->second));
        target = ::google::protobuf::internal::WireFormatLite::
                   InternalWriteMessageNoVirtualToArray(
                       11, *entry, deterministic, target);
;
      }
    } else {
      ::google::protobuf::scoped_ptr<TestMaps_MBoolEntry> entry;
      for (::google::protobuf::Map< bool, ::protobuf_unittest::TestIntIntMap >::const_iterator
          it = this->m_bool().begin();
          it != this->m_bool().end(); ++it) {
        entry.reset(m_bool_.NewEntryWrapper(
            it->first, it->second));
        target = ::google::protobuf::internal::WireFormatLite::
                   InternalWriteMessageNoVirtualToArray(
                       11, *entry, deterministic, target);
;
      }
    }
  }

  // map<string, .protobuf_unittest.TestIntIntMap> m_string = 12;
  if (!this->m_string().empty()) {
    typedef ::google::protobuf::Map< ::std::string, ::protobuf_unittest::TestIntIntMap >::const_pointer
        ConstPtr;
    typedef ConstPtr SortItem;
    typedef ::google::protobuf::internal::CompareByDerefFirst<SortItem> Less;
    struct Utf8Check {
      static void Check(ConstPtr p) {
        ::google::protobuf::internal::WireFormat::VerifyUTF8StringNamedField(
          p->first.data(), p->first.length(),
          ::google::protobuf::internal::WireFormat::SERIALIZE,
          "protobuf_unittest.TestMaps.MStringEntry.key");
      }
    };

    if (deterministic &&
        this->m_string().size() > 1) {
      ::google::protobuf::scoped_array<SortItem> items(
          new SortItem[this->m_string().size()]);
      typedef ::google::protobuf::Map< ::std::string, ::protobuf_unittest::TestIntIntMap >::size_type size_type;
      size_type n = 0;
      for (::google::protobuf::Map< ::std::string, ::protobuf_unittest::TestIntIntMap >::const_iterator
          it = this->m_string().begin();
          it != this->m_string().end(); ++it, ++n) {
        items[n] = SortItem(&*it);
      }
      ::std::sort(&items[0], &items[n], Less());
      ::google::protobuf::scoped_ptr<TestMaps_MStringEntry> entry;
      for (size_type i = 0; i < n; i++) {
        entry.reset(m_string_.NewEntryWrapper(
            items[i]->first, items[i]->second));
        target = ::google::protobuf::internal::WireFormatLite::
                   InternalWriteMessageNoVirtualToArray(
                       12, *entry, deterministic, target);
;
        Utf8Check::Check(items[i]);
      }
    } else {
      ::google::protobuf::scoped_ptr<TestMaps_MStringEntry> entry;
      for (::google::protobuf::Map< ::std::string, ::protobuf_unittest::TestIntIntMap >::const_iterator
          it = this->m_string().begin();
          it != this->m_string().end(); ++it) {
        entry.reset(m_string_.NewEntryWrapper(
            it->first, it->second));
        target = ::google::protobuf::internal::WireFormatLite::
                   InternalWriteMessageNoVirtualToArray(
                       12, *entry, deterministic, target);
;
        Utf8Check::Check(&*it);
      }
    }
  }

  if (_internal_metadata_.have_unknown_fields()) {
    target = ::google::protobuf::internal::WireFormat::SerializeUnknownFieldsToArray(
        unknown_fields(), target);
  }
  // @@protoc_insertion_point(serialize_to_array_end:protobuf_unittest.TestMaps)
  return target;
}

size_t TestMaps::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:protobuf_unittest.TestMaps)
  size_t total_size = 0;

  // map<int32, .protobuf_unittest.TestIntIntMap> m_int32 = 1;
  total_size += 1 *
      ::google::protobuf::internal::FromIntSize(this->m_int32_size());
  {
    ::google::protobuf::scoped_ptr<TestMaps_MInt32Entry> entry;
    for (::google::protobuf::Map< ::google::protobuf::int32, ::protobuf_unittest::TestIntIntMap >::const_iterator
        it = this->m_int32().begin();
        it != this->m_int32().end(); ++it) {
      entry.reset(m_int32_.NewEntryWrapper(it->first, it->second));
      total_size += ::google::protobuf::internal::WireFormatLite::
          MessageSizeNoVirtual(*entry);
    }
  }

  // map<int64, .protobuf_unittest.TestIntIntMap> m_int64 = 2;
  total_size += 1 *
      ::google::protobuf::internal::FromIntSize(this->m_int64_size());
  {
    ::google::protobuf::scoped_ptr<TestMaps_MInt64Entry> entry;
    for (::google::protobuf::Map< ::google::protobuf::int64, ::protobuf_unittest::TestIntIntMap >::const_iterator
        it = this->m_int64().begin();
        it != this->m_int64().end(); ++it) {
      entry.reset(m_int64_.NewEntryWrapper(it->first, it->second));
      total_size += ::google::protobuf::internal::WireFormatLite::
          MessageSizeNoVirtual(*entry);
    }
  }

  // map<uint32, .protobuf_unittest.TestIntIntMap> m_uint32 = 3;
  total_size += 1 *
      ::google::protobuf::internal::FromIntSize(this->m_uint32_size());
  {
    ::google::protobuf::scoped_ptr<TestMaps_MUint32Entry> entry;
    for (::google::protobuf::Map< ::google::protobuf::uint32, ::protobuf_unittest::TestIntIntMap >::const_iterator
        it = this->m_uint32().begin();
        it != this->m_uint32().end(); ++it) {
      entry.reset(m_uint32_.NewEntryWrapper(it->first, it->second));
      total_size += ::google::protobuf::internal::WireFormatLite::
          MessageSizeNoVirtual(*entry);
    }
  }

  // map<uint64, .protobuf_unittest.TestIntIntMap> m_uint64 = 4;
  total_size += 1 *
      ::google::protobuf::internal::FromIntSize(this->m_uint64_size());
  {
    ::google::protobuf::scoped_ptr<TestMaps_MUint64Entry> entry;
    for (::google::protobuf::Map< ::google::protobuf::uint64, ::protobuf_unittest::TestIntIntMap >::const_iterator
        it = this->m_uint64().begin();
        it != this->m_uint64().end(); ++it) {
      entry.reset(m_uint64_.NewEntryWrapper(it->first, it->second));
      total_size += ::google::protobuf::internal::WireFormatLite::
          MessageSizeNoVirtual(*entry);
    }
  }

  // map<sint32, .protobuf_unittest.TestIntIntMap> m_sint32 = 5;
  total_size += 1 *
      ::google::protobuf::internal::FromIntSize(this->m_sint32_size());
  {
    ::google::protobuf::scoped_ptr<TestMaps_MSint32Entry> entry;
    for (::google::protobuf::Map< ::google::protobuf::int32, ::protobuf_unittest::TestIntIntMap >::const_iterator
        it = this->m_sint32().begin();
        it != this->m_sint32().end(); ++it) {
      entry.reset(m_sint32_.NewEntryWrapper(it->first, it->second));
      total_size += ::google::protobuf::internal::WireFormatLite::
          MessageSizeNoVirtual(*entry);
    }
  }

  // map<sint64, .protobuf_unittest.TestIntIntMap> m_sint64 = 6;
  total_size += 1 *
      ::google::protobuf::internal::FromIntSize(this->m_sint64_size());
  {
    ::google::protobuf::scoped_ptr<TestMaps_MSint64Entry> entry;
    for (::google::protobuf::Map< ::google::protobuf::int64, ::protobuf_unittest::TestIntIntMap >::const_iterator
        it = this->m_sint64().begin();
        it != this->m_sint64().end(); ++it) {
      entry.reset(m_sint64_.NewEntryWrapper(it->first, it->second));
      total_size += ::google::protobuf::internal::WireFormatLite::
          MessageSizeNoVirtual(*entry);
    }
  }

  // map<fixed32, .protobuf_unittest.TestIntIntMap> m_fixed32 = 7;
  total_size += 1 *
      ::google::protobuf::internal::FromIntSize(this->m_fixed32_size());
  {
    ::google::protobuf::scoped_ptr<TestMaps_MFixed32Entry> entry;
    for (::google::protobuf::Map< ::google::protobuf::uint32, ::protobuf_unittest::TestIntIntMap >::const_iterator
        it = this->m_fixed32().begin();
        it != this->m_fixed32().end(); ++it) {
      entry.reset(m_fixed32_.NewEntryWrapper(it->first, it->second));
      total_size += ::google::protobuf::internal::WireFormatLite::
          MessageSizeNoVirtual(*entry);
    }
  }

  // map<fixed64, .protobuf_unittest.TestIntIntMap> m_fixed64 = 8;
  total_size += 1 *
      ::google::protobuf::internal::FromIntSize(this->m_fixed64_size());
  {
    ::google::protobuf::scoped_ptr<TestMaps_MFixed64Entry> entry;
    for (::google::protobuf::Map< ::google::protobuf::uint64, ::protobuf_unittest::TestIntIntMap >::const_iterator
        it = this->m_fixed64().begin();
        it != this->m_fixed64().end(); ++it) {
      entry.reset(m_fixed64_.NewEntryWrapper(it->first, it->second));
      total_size += ::google::protobuf::internal::WireFormatLite::
          MessageSizeNoVirtual(*entry);
    }
  }

  // map<sfixed32, .protobuf_unittest.TestIntIntMap> m_sfixed32 = 9;
  total_size += 1 *
      ::google::protobuf::internal::FromIntSize(this->m_sfixed32_size());
  {
    ::google::protobuf::scoped_ptr<TestMaps_MSfixed32Entry> entry;
    for (::google::protobuf::Map< ::google::protobuf::int32, ::protobuf_unittest::TestIntIntMap >::const_iterator
        it = this->m_sfixed32().begin();
        it != this->m_sfixed32().end(); ++it) {
      entry.reset(m_sfixed32_.NewEntryWrapper(it->first, it->second));
      total_size += ::google::protobuf::internal::WireFormatLite::
          MessageSizeNoVirtual(*entry);
    }
  }

  // map<sfixed64, .protobuf_unittest.TestIntIntMap> m_sfixed64 = 10;
  total_size += 1 *
      ::google::protobuf::internal::FromIntSize(this->m_sfixed64_size());
  {
    ::google::protobuf::scoped_ptr<TestMaps_MSfixed64Entry> entry;
    for (::google::protobuf::Map< ::google::protobuf::int64, ::protobuf_unittest::TestIntIntMap >::const_iterator
        it = this->m_sfixed64().begin();
        it != this->m_sfixed64().end(); ++it) {
      entry.reset(m_sfixed64_.NewEntryWrapper(it->first, it->second));
      total_size += ::google::protobuf::internal::WireFormatLite::
          MessageSizeNoVirtual(*entry);
    }
  }

  // map<bool, .protobuf_unittest.TestIntIntMap> m_bool = 11;
  total_size += 1 *
      ::google::protobuf::internal::FromIntSize(this->m_bool_size());
  {
    ::google::protobuf::scoped_ptr<TestMaps_MBoolEntry> entry;
    for (::google::protobuf::Map< bool, ::protobuf_unittest::TestIntIntMap >::const_iterator
        it = this->m_bool().begin();
        it != this->m_bool().end(); ++it) {
      entry.reset(m_bool_.NewEntryWrapper(it->first, it->second));
      total_size += ::google::protobuf::internal::WireFormatLite::
          MessageSizeNoVirtual(*entry);
    }
  }

  // map<string, .protobuf_unittest.TestIntIntMap> m_string = 12;
  total_size += 1 *
      ::google::protobuf::internal::FromIntSize(this->m_string_size());
  {
    ::google::protobuf::scoped_ptr<TestMaps_MStringEntry> entry;
    for (::google::protobuf::Map< ::std::string, ::protobuf_unittest::TestIntIntMap >::const_iterator
        it = this->m_string().begin();
        it != this->m_string().end(); ++it) {
      entry.reset(m_string_.NewEntryWrapper(it->first, it->second));
      total_size += ::google::protobuf::internal::WireFormatLite::
          MessageSizeNoVirtual(*entry);
    }
  }

  if (_internal_metadata_.have_unknown_fields()) {
    total_size +=
      ::google::protobuf::internal::WireFormat::ComputeUnknownFieldsSize(
        unknown_fields());
  }
  int cached_size = ::google::protobuf::internal::ToCachedSize(total_size);
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = cached_size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
  return total_size;
}

void TestMaps::MergeFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_merge_from_start:protobuf_unittest.TestMaps)
  if (GOOGLE_PREDICT_FALSE(&from == this)) MergeFromFail(__LINE__);
  const TestMaps* source =
      ::google::protobuf::internal::DynamicCastToGenerated<const TestMaps>(
          &from);
  if (source == NULL) {
  // @@protoc_insertion_point(generalized_merge_from_cast_fail:protobuf_unittest.TestMaps)
    ::google::protobuf::internal::ReflectionOps::Merge(from, this);
  } else {
  // @@protoc_insertion_point(generalized_merge_from_cast_success:protobuf_unittest.TestMaps)
    UnsafeMergeFrom(*source);
  }
}

void TestMaps::MergeFrom(const TestMaps& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:protobuf_unittest.TestMaps)
  if (GOOGLE_PREDICT_TRUE(&from != this)) {
    UnsafeMergeFrom(from);
  } else {
    MergeFromFail(__LINE__);
  }
}

void TestMaps::UnsafeMergeFrom(const TestMaps& from) {
  GOOGLE_DCHECK(&from != this);
  m_int32_.MergeFrom(from.m_int32_);
  m_int64_.MergeFrom(from.m_int64_);
  m_uint32_.MergeFrom(from.m_uint32_);
  m_uint64_.MergeFrom(from.m_uint64_);
  m_sint32_.MergeFrom(from.m_sint32_);
  m_sint64_.MergeFrom(from.m_sint64_);
  m_fixed32_.MergeFrom(from.m_fixed32_);
  m_fixed64_.MergeFrom(from.m_fixed64_);
  m_sfixed32_.MergeFrom(from.m_sfixed32_);
  m_sfixed64_.MergeFrom(from.m_sfixed64_);
  m_bool_.MergeFrom(from.m_bool_);
  m_string_.MergeFrom(from.m_string_);
  if (from._internal_metadata_.have_unknown_fields()) {
    ::google::protobuf::UnknownFieldSet::MergeToInternalMetdata(
      from.unknown_fields(), &_internal_metadata_);
  }
}

void TestMaps::CopyFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_copy_from_start:protobuf_unittest.TestMaps)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

void TestMaps::CopyFrom(const TestMaps& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:protobuf_unittest.TestMaps)
  if (&from == this) return;
  Clear();
  UnsafeMergeFrom(from);
}

bool TestMaps::IsInitialized() const {

  return true;
}

void TestMaps::Swap(TestMaps* other) {
  if (other == this) return;
  InternalSwap(other);
}
void TestMaps::InternalSwap(TestMaps* other) {
  m_int32_.Swap(&other->m_int32_);
  m_int64_.Swap(&other->m_int64_);
  m_uint32_.Swap(&other->m_uint32_);
  m_uint64_.Swap(&other->m_uint64_);
  m_sint32_.Swap(&other->m_sint32_);
  m_sint64_.Swap(&other->m_sint64_);
  m_fixed32_.Swap(&other->m_fixed32_);
  m_fixed64_.Swap(&other->m_fixed64_);
  m_sfixed32_.Swap(&other->m_sfixed32_);
  m_sfixed64_.Swap(&other->m_sfixed64_);
  m_bool_.Swap(&other->m_bool_);
  m_string_.Swap(&other->m_string_);
  std::swap(_has_bits_[0], other->_has_bits_[0]);
  _internal_metadata_.Swap(&other->_internal_metadata_);
  std::swap(_cached_size_, other->_cached_size_);
}

::google::protobuf::Metadata TestMaps::GetMetadata() const {
  protobuf_AssignDescriptorsOnce();
  ::google::protobuf::Metadata metadata;
  metadata.descriptor = TestMaps_descriptor_;
  metadata.reflection = TestMaps_reflection_;
  return metadata;
}

#if PROTOBUF_INLINE_NOT_IN_HEADERS
// TestMaps

// map<int32, .protobuf_unittest.TestIntIntMap> m_int32 = 1;
int TestMaps::m_int32_size() const {
  return m_int32_.size();
}
void TestMaps::clear_m_int32() {
  m_int32_.Clear();
}
 const ::google::protobuf::Map< ::google::protobuf::int32, ::protobuf_unittest::TestIntIntMap >&
TestMaps::m_int32() const {
  // @@protoc_insertion_point(field_map:protobuf_unittest.TestMaps.m_int32)
  return m_int32_.GetMap();
}
 ::google::protobuf::Map< ::google::protobuf::int32, ::protobuf_unittest::TestIntIntMap >*
TestMaps::mutable_m_int32() {
  // @@protoc_insertion_point(field_mutable_map:protobuf_unittest.TestMaps.m_int32)
  return m_int32_.MutableMap();
}

// map<int64, .protobuf_unittest.TestIntIntMap> m_int64 = 2;
int TestMaps::m_int64_size() const {
  return m_int64_.size();
}
void TestMaps::clear_m_int64() {
  m_int64_.Clear();
}
 const ::google::protobuf::Map< ::google::protobuf::int64, ::protobuf_unittest::TestIntIntMap >&
TestMaps::m_int64() const {
  // @@protoc_insertion_point(field_map:protobuf_unittest.TestMaps.m_int64)
  return m_int64_.GetMap();
}
 ::google::protobuf::Map< ::google::protobuf::int64, ::protobuf_unittest::TestIntIntMap >*
TestMaps::mutable_m_int64() {
  // @@protoc_insertion_point(field_mutable_map:protobuf_unittest.TestMaps.m_int64)
  return m_int64_.MutableMap();
}

// map<uint32, .protobuf_unittest.TestIntIntMap> m_uint32 = 3;
int TestMaps::m_uint32_size() const {
  return m_uint32_.size();
}
void TestMaps::clear_m_uint32() {
  m_uint32_.Clear();
}
 const ::google::protobuf::Map< ::google::protobuf::uint32, ::protobuf_unittest::TestIntIntMap >&
TestMaps::m_uint32() const {
  // @@protoc_insertion_point(field_map:protobuf_unittest.TestMaps.m_uint32)
  return m_uint32_.GetMap();
}
 ::google::protobuf::Map< ::google::protobuf::uint32, ::protobuf_unittest::TestIntIntMap >*
TestMaps::mutable_m_uint32() {
  // @@protoc_insertion_point(field_mutable_map:protobuf_unittest.TestMaps.m_uint32)
  return m_uint32_.MutableMap();
}

// map<uint64, .protobuf_unittest.TestIntIntMap> m_uint64 = 4;
int TestMaps::m_uint64_size() const {
  return m_uint64_.size();
}
void TestMaps::clear_m_uint64() {
  m_uint64_.Clear();
}
 const ::google::protobuf::Map< ::google::protobuf::uint64, ::protobuf_unittest::TestIntIntMap >&
TestMaps::m_uint64() const {
  // @@protoc_insertion_point(field_map:protobuf_unittest.TestMaps.m_uint64)
  return m_uint64_.GetMap();
}
 ::google::protobuf::Map< ::google::protobuf::uint64, ::protobuf_unittest::TestIntIntMap >*
TestMaps::mutable_m_uint64() {
  // @@protoc_insertion_point(field_mutable_map:protobuf_unittest.TestMaps.m_uint64)
  return m_uint64_.MutableMap();
}

// map<sint32, .protobuf_unittest.TestIntIntMap> m_sint32 = 5;
int TestMaps::m_sint32_size() const {
  return m_sint32_.size();
}
void TestMaps::clear_m_sint32() {
  m_sint32_.Clear();
}
 const ::google::protobuf::Map< ::google::protobuf::int32, ::protobuf_unittest::TestIntIntMap >&
TestMaps::m_sint32() const {
  // @@protoc_insertion_point(field_map:protobuf_unittest.TestMaps.m_sint32)
  return m_sint32_.GetMap();
}
 ::google::protobuf::Map< ::google::protobuf::int32, ::protobuf_unittest::TestIntIntMap >*
TestMaps::mutable_m_sint32() {
  // @@protoc_insertion_point(field_mutable_map:protobuf_unittest.TestMaps.m_sint32)
  return m_sint32_.MutableMap();
}

// map<sint64, .protobuf_unittest.TestIntIntMap> m_sint64 = 6;
int TestMaps::m_sint64_size() const {
  return m_sint64_.size();
}
void TestMaps::clear_m_sint64() {
  m_sint64_.Clear();
}
 const ::google::protobuf::Map< ::google::protobuf::int64, ::protobuf_unittest::TestIntIntMap >&
TestMaps::m_sint64() const {
  // @@protoc_insertion_point(field_map:protobuf_unittest.TestMaps.m_sint64)
  return m_sint64_.GetMap();
}
 ::google::protobuf::Map< ::google::protobuf::int64, ::protobuf_unittest::TestIntIntMap >*
TestMaps::mutable_m_sint64() {
  // @@protoc_insertion_point(field_mutable_map:protobuf_unittest.TestMaps.m_sint64)
  return m_sint64_.MutableMap();
}

// map<fixed32, .protobuf_unittest.TestIntIntMap> m_fixed32 = 7;
int TestMaps::m_fixed32_size() const {
  return m_fixed32_.size();
}
void TestMaps::clear_m_fixed32() {
  m_fixed32_.Clear();
}
 const ::google::protobuf::Map< ::google::protobuf::uint32, ::protobuf_unittest::TestIntIntMap >&
TestMaps::m_fixed32() const {
  // @@protoc_insertion_point(field_map:protobuf_unittest.TestMaps.m_fixed32)
  return m_fixed32_.GetMap();
}
 ::google::protobuf::Map< ::google::protobuf::uint32, ::protobuf_unittest::TestIntIntMap >*
TestMaps::mutable_m_fixed32() {
  // @@protoc_insertion_point(field_mutable_map:protobuf_unittest.TestMaps.m_fixed32)
  return m_fixed32_.MutableMap();
}

// map<fixed64, .protobuf_unittest.TestIntIntMap> m_fixed64 = 8;
int TestMaps::m_fixed64_size() const {
  return m_fixed64_.size();
}
void TestMaps::clear_m_fixed64() {
  m_fixed64_.Clear();
}
 const ::google::protobuf::Map< ::google::protobuf::uint64, ::protobuf_unittest::TestIntIntMap >&
TestMaps::m_fixed64() const {
  // @@protoc_insertion_point(field_map:protobuf_unittest.TestMaps.m_fixed64)
  return m_fixed64_.GetMap();
}
 ::google::protobuf::Map< ::google::protobuf::uint64, ::protobuf_unittest::TestIntIntMap >*
TestMaps::mutable_m_fixed64() {
  // @@protoc_insertion_point(field_mutable_map:protobuf_unittest.TestMaps.m_fixed64)
  return m_fixed64_.MutableMap();
}

// map<sfixed32, .protobuf_unittest.TestIntIntMap> m_sfixed32 = 9;
int TestMaps::m_sfixed32_size() const {
  return m_sfixed32_.size();
}
void TestMaps::clear_m_sfixed32() {
  m_sfixed32_.Clear();
}
 const ::google::protobuf::Map< ::google::protobuf::int32, ::protobuf_unittest::TestIntIntMap >&
TestMaps::m_sfixed32() const {
  // @@protoc_insertion_point(field_map:protobuf_unittest.TestMaps.m_sfixed32)
  return m_sfixed32_.GetMap();
}
 ::google::protobuf::Map< ::google::protobuf::int32, ::protobuf_unittest::TestIntIntMap >*
TestMaps::mutable_m_sfixed32() {
  // @@protoc_insertion_point(field_mutable_map:protobuf_unittest.TestMaps.m_sfixed32)
  return m_sfixed32_.MutableMap();
}

// map<sfixed64, .protobuf_unittest.TestIntIntMap> m_sfixed64 = 10;
int TestMaps::m_sfixed64_size() const {
  return m_sfixed64_.size();
}
void TestMaps::clear_m_sfixed64() {
  m_sfixed64_.Clear();
}
 const ::google::protobuf::Map< ::google::protobuf::int64, ::protobuf_unittest::TestIntIntMap >&
TestMaps::m_sfixed64() const {
  // @@protoc_insertion_point(field_map:protobuf_unittest.TestMaps.m_sfixed64)
  return m_sfixed64_.GetMap();
}
 ::google::protobuf::Map< ::google::protobuf::int64, ::protobuf_unittest::TestIntIntMap >*
TestMaps::mutable_m_sfixed64() {
  // @@protoc_insertion_point(field_mutable_map:protobuf_unittest.TestMaps.m_sfixed64)
  return m_sfixed64_.MutableMap();
}

// map<bool, .protobuf_unittest.TestIntIntMap> m_bool = 11;
int TestMaps::m_bool_size() const {
  return m_bool_.size();
}
void TestMaps::clear_m_bool() {
  m_bool_.Clear();
}
 const ::google::protobuf::Map< bool, ::protobuf_unittest::TestIntIntMap >&
TestMaps::m_bool() const {
  // @@protoc_insertion_point(field_map:protobuf_unittest.TestMaps.m_bool)
  return m_bool_.GetMap();
}
 ::google::protobuf::Map< bool, ::protobuf_unittest::TestIntIntMap >*
TestMaps::mutable_m_bool() {
  // @@protoc_insertion_point(field_mutable_map:protobuf_unittest.TestMaps.m_bool)
  return m_bool_.MutableMap();
}

// map<string, .protobuf_unittest.TestIntIntMap> m_string = 12;
int TestMaps::m_string_size() const {
  return m_string_.size();
}
void TestMaps::clear_m_string() {
  m_string_.Clear();
}
 const ::google::protobuf::Map< ::std::string, ::protobuf_unittest::TestIntIntMap >&
TestMaps::m_string() const {
  // @@protoc_insertion_point(field_map:protobuf_unittest.TestMaps.m_string)
  return m_string_.GetMap();
}
 ::google::protobuf::Map< ::std::string, ::protobuf_unittest::TestIntIntMap >*
TestMaps::mutable_m_string() {
  // @@protoc_insertion_point(field_mutable_map:protobuf_unittest.TestMaps.m_string)
  return m_string_.MutableMap();
}

inline const TestMaps* TestMaps::internal_default_instance() {
  return &TestMaps_default_instance_.get();
}
#endif  // PROTOBUF_INLINE_NOT_IN_HEADERS

// @@protoc_insertion_point(namespace_scope)

}  // namespace protobuf_unittest

// @@protoc_insertion_point(global_scope)

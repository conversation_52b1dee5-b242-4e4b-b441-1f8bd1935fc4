syntax = "proto3";

package protobuf_unittest;

option csharp_namespace = "Google.Protobuf.TestProtos";
option java_multiple_files = true;
option java_package = "com.google.protobuf.test";

import "google/protobuf/any.proto";
import "google/protobuf/api.proto";
import "google/protobuf/duration.proto";
import "google/protobuf/empty.proto";
import "google/protobuf/field_mask.proto";
import "google/protobuf/source_context.proto";
import "google/protobuf/struct.proto";
import "google/protobuf/timestamp.proto";
import "google/protobuf/type.proto";
import "google/protobuf/wrappers.proto";

// Test that we can include all well-known types.
// Each wrapper type is included separately, as languages
// map handle different wrappers in different ways.
message TestWellKnownTypes {
  google.protobuf.Any any_field = 1;
  google.protobuf.Api api_field = 2;
  google.protobuf.Duration duration_field = 3;
  google.protobuf.Empty empty_field = 4;
  google.protobuf.FieldMask field_mask_field = 5;
  google.protobuf.SourceContext source_context_field = 6;
  google.protobuf.Struct struct_field = 7;
  google.protobuf.Timestamp timestamp_field = 8;
  google.protobuf.Type type_field = 9;
  google.protobuf.DoubleValue double_field = 10;
  google.protobuf.FloatValue float_field = 11;
  google.protobuf.Int64Value int64_field = 12;
  google.protobuf.UInt64Value uint64_field = 13;
  google.protobuf.Int32Value int32_field = 14;
  google.protobuf.UInt32Value uint32_field = 15;
  google.protobuf.BoolValue bool_field = 16;
  google.protobuf.StringValue string_field = 17;
  google.protobuf.BytesValue bytes_field = 18;
  // Part of struct, but useful to be able to test separately
  google.protobuf.Value value_field = 19;
}

// A repeated field for each well-known type.
message RepeatedWellKnownTypes {
  repeated google.protobuf.Any any_field = 1;
  repeated google.protobuf.Api api_field = 2;
  repeated google.protobuf.Duration duration_field = 3;
  repeated google.protobuf.Empty empty_field = 4;
  repeated google.protobuf.FieldMask field_mask_field = 5;
  repeated google.protobuf.SourceContext source_context_field = 6;
  repeated google.protobuf.Struct struct_field = 7;
  repeated google.protobuf.Timestamp timestamp_field = 8;
  repeated google.protobuf.Type type_field = 9;
  // These don't actually make a lot of sense, but they're not prohibited...
  repeated google.protobuf.DoubleValue double_field = 10;
  repeated google.protobuf.FloatValue float_field = 11;
  repeated google.protobuf.Int64Value int64_field = 12;
  repeated google.protobuf.UInt64Value uint64_field = 13;
  repeated google.protobuf.Int32Value int32_field = 14;
  repeated google.protobuf.UInt32Value uint32_field = 15;
  repeated google.protobuf.BoolValue bool_field = 16;
  repeated google.protobuf.StringValue string_field = 17;
  repeated google.protobuf.BytesValue bytes_field = 18;
}

message OneofWellKnownTypes {
  oneof oneof_field {
    google.protobuf.Any any_field = 1;
    google.protobuf.Api api_field = 2;
    google.protobuf.Duration duration_field = 3;
    google.protobuf.Empty empty_field = 4;
    google.protobuf.FieldMask field_mask_field = 5;
    google.protobuf.SourceContext source_context_field = 6;
    google.protobuf.Struct struct_field = 7;
    google.protobuf.Timestamp timestamp_field = 8;
    google.protobuf.Type type_field = 9;
    google.protobuf.DoubleValue double_field = 10;
    google.protobuf.FloatValue float_field = 11;
    google.protobuf.Int64Value int64_field = 12;
    google.protobuf.UInt64Value uint64_field = 13;
    google.protobuf.Int32Value int32_field = 14;
    google.protobuf.UInt32Value uint32_field = 15;
    google.protobuf.BoolValue bool_field = 16;
    google.protobuf.StringValue string_field = 17;
    google.protobuf.BytesValue bytes_field = 18;
  }
}

// A map field for each well-known type. We only
// need to worry about the value part of the map being the
// well-known types, as messages can't be map keys.
message MapWellKnownTypes {
  map<int32,google.protobuf.Any> any_field = 1;
  map<int32,google.protobuf.Api> api_field = 2;
  map<int32,google.protobuf.Duration> duration_field = 3;
  map<int32,google.protobuf.Empty> empty_field = 4;
  map<int32,google.protobuf.FieldMask> field_mask_field = 5;
  map<int32,google.protobuf.SourceContext> source_context_field = 6;
  map<int32,google.protobuf.Struct> struct_field = 7;
  map<int32,google.protobuf.Timestamp> timestamp_field = 8;
  map<int32,google.protobuf.Type> type_field = 9;
  map<int32,google.protobuf.DoubleValue> double_field = 10;
  map<int32,google.protobuf.FloatValue> float_field = 11;
  map<int32,google.protobuf.Int64Value> int64_field = 12;
  map<int32,google.protobuf.UInt64Value> uint64_field = 13;
  map<int32,google.protobuf.Int32Value> int32_field = 14;
  map<int32,google.protobuf.UInt32Value> uint32_field = 15;
  map<int32,google.protobuf.BoolValue> bool_field = 16;
  map<int32,google.protobuf.StringValue> string_field = 17;
  map<int32,google.protobuf.BytesValue> bytes_field = 18;
}

// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: google/protobuf/unittest_no_arena.proto

#define INTERNAL_SUPPRESS_PROTOBUF_FIELD_DEPRECATION
#include <google/protobuf/unittest_no_arena.pb.h>

#include <algorithm>

#include <google/protobuf/stubs/common.h>
#include <google/protobuf/stubs/port.h>
#include <google/protobuf/stubs/once.h>
#include <google/protobuf/io/coded_stream.h>
#include <google/protobuf/wire_format_lite_inl.h>
#include <google/protobuf/descriptor.h>
#include <google/protobuf/generated_message_reflection.h>
#include <google/protobuf/reflection_ops.h>
#include <google/protobuf/wire_format.h>
// @@protoc_insertion_point(includes)

namespace protobuf_unittest_no_arena {

namespace {

const ::google::protobuf::Descriptor* TestAllTypes_descriptor_ = NULL;
const ::google::protobuf::internal::GeneratedMessageReflection*
  TestAllTypes_reflection_ = NULL;
struct TestAllTypesOneofInstance {
  ::google::protobuf::uint32 oneof_uint32_;
  const ::protobuf_unittest_no_arena::TestAllTypes_NestedMessage* oneof_nested_message_;
  ::google::protobuf::internal::ArenaStringPtr oneof_string_;
  ::google::protobuf::internal::ArenaStringPtr oneof_bytes_;
  const ::protobuf_unittest_no_arena::TestAllTypes_NestedMessage* lazy_oneof_nested_message_;
}* TestAllTypes_default_oneof_instance_ = NULL;
const ::google::protobuf::Descriptor* TestAllTypes_NestedMessage_descriptor_ = NULL;
const ::google::protobuf::internal::GeneratedMessageReflection*
  TestAllTypes_NestedMessage_reflection_ = NULL;
const ::google::protobuf::Descriptor* TestAllTypes_OptionalGroup_descriptor_ = NULL;
const ::google::protobuf::internal::GeneratedMessageReflection*
  TestAllTypes_OptionalGroup_reflection_ = NULL;
const ::google::protobuf::Descriptor* TestAllTypes_RepeatedGroup_descriptor_ = NULL;
const ::google::protobuf::internal::GeneratedMessageReflection*
  TestAllTypes_RepeatedGroup_reflection_ = NULL;
const ::google::protobuf::EnumDescriptor* TestAllTypes_NestedEnum_descriptor_ = NULL;
const ::google::protobuf::Descriptor* ForeignMessage_descriptor_ = NULL;
const ::google::protobuf::internal::GeneratedMessageReflection*
  ForeignMessage_reflection_ = NULL;
const ::google::protobuf::Descriptor* TestNoArenaMessage_descriptor_ = NULL;
const ::google::protobuf::internal::GeneratedMessageReflection*
  TestNoArenaMessage_reflection_ = NULL;
const ::google::protobuf::EnumDescriptor* ForeignEnum_descriptor_ = NULL;

}  // namespace


void protobuf_AssignDesc_google_2fprotobuf_2funittest_5fno_5farena_2eproto() GOOGLE_ATTRIBUTE_COLD;
void protobuf_AssignDesc_google_2fprotobuf_2funittest_5fno_5farena_2eproto() {
  protobuf_AddDesc_google_2fprotobuf_2funittest_5fno_5farena_2eproto();
  const ::google::protobuf::FileDescriptor* file =
    ::google::protobuf::DescriptorPool::generated_pool()->FindFileByName(
      "google/protobuf/unittest_no_arena.proto");
  GOOGLE_CHECK(file != NULL);
  TestAllTypes_descriptor_ = file->message_type(0);
  static const int TestAllTypes_offsets_[77] = {
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(TestAllTypes, optional_int32_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(TestAllTypes, optional_int64_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(TestAllTypes, optional_uint32_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(TestAllTypes, optional_uint64_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(TestAllTypes, optional_sint32_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(TestAllTypes, optional_sint64_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(TestAllTypes, optional_fixed32_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(TestAllTypes, optional_fixed64_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(TestAllTypes, optional_sfixed32_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(TestAllTypes, optional_sfixed64_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(TestAllTypes, optional_float_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(TestAllTypes, optional_double_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(TestAllTypes, optional_bool_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(TestAllTypes, optional_string_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(TestAllTypes, optional_bytes_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(TestAllTypes, optionalgroup_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(TestAllTypes, optional_nested_message_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(TestAllTypes, optional_foreign_message_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(TestAllTypes, optional_import_message_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(TestAllTypes, optional_nested_enum_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(TestAllTypes, optional_foreign_enum_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(TestAllTypes, optional_import_enum_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(TestAllTypes, optional_string_piece_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(TestAllTypes, optional_cord_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(TestAllTypes, optional_public_import_message_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(TestAllTypes, optional_message_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(TestAllTypes, repeated_int32_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(TestAllTypes, repeated_int64_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(TestAllTypes, repeated_uint32_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(TestAllTypes, repeated_uint64_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(TestAllTypes, repeated_sint32_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(TestAllTypes, repeated_sint64_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(TestAllTypes, repeated_fixed32_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(TestAllTypes, repeated_fixed64_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(TestAllTypes, repeated_sfixed32_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(TestAllTypes, repeated_sfixed64_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(TestAllTypes, repeated_float_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(TestAllTypes, repeated_double_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(TestAllTypes, repeated_bool_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(TestAllTypes, repeated_string_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(TestAllTypes, repeated_bytes_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(TestAllTypes, repeatedgroup_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(TestAllTypes, repeated_nested_message_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(TestAllTypes, repeated_foreign_message_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(TestAllTypes, repeated_import_message_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(TestAllTypes, repeated_nested_enum_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(TestAllTypes, repeated_foreign_enum_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(TestAllTypes, repeated_import_enum_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(TestAllTypes, repeated_string_piece_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(TestAllTypes, repeated_cord_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(TestAllTypes, repeated_lazy_message_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(TestAllTypes, default_int32_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(TestAllTypes, default_int64_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(TestAllTypes, default_uint32_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(TestAllTypes, default_uint64_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(TestAllTypes, default_sint32_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(TestAllTypes, default_sint64_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(TestAllTypes, default_fixed32_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(TestAllTypes, default_fixed64_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(TestAllTypes, default_sfixed32_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(TestAllTypes, default_sfixed64_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(TestAllTypes, default_float_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(TestAllTypes, default_double_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(TestAllTypes, default_bool_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(TestAllTypes, default_string_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(TestAllTypes, default_bytes_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(TestAllTypes, default_nested_enum_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(TestAllTypes, default_foreign_enum_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(TestAllTypes, default_import_enum_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(TestAllTypes, default_string_piece_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(TestAllTypes, default_cord_),
    PROTO2_GENERATED_DEFAULT_ONEOF_FIELD_OFFSET(TestAllTypes_default_oneof_instance_, oneof_uint32_),
    PROTO2_GENERATED_DEFAULT_ONEOF_FIELD_OFFSET(TestAllTypes_default_oneof_instance_, oneof_nested_message_),
    PROTO2_GENERATED_DEFAULT_ONEOF_FIELD_OFFSET(TestAllTypes_default_oneof_instance_, oneof_string_),
    PROTO2_GENERATED_DEFAULT_ONEOF_FIELD_OFFSET(TestAllTypes_default_oneof_instance_, oneof_bytes_),
    PROTO2_GENERATED_DEFAULT_ONEOF_FIELD_OFFSET(TestAllTypes_default_oneof_instance_, lazy_oneof_nested_message_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(TestAllTypes, oneof_field_),
  };
  TestAllTypes_reflection_ =
    ::google::protobuf::internal::GeneratedMessageReflection::NewGeneratedMessageReflection(
      TestAllTypes_descriptor_,
      TestAllTypes::internal_default_instance(),
      TestAllTypes_offsets_,
      GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(TestAllTypes, _has_bits_),
      -1,
      -1,
      TestAllTypes_default_oneof_instance_,
      GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(TestAllTypes, _oneof_case_[0]),
      sizeof(TestAllTypes),
      GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(TestAllTypes, _internal_metadata_));
  TestAllTypes_NestedMessage_descriptor_ = TestAllTypes_descriptor_->nested_type(0);
  static const int TestAllTypes_NestedMessage_offsets_[1] = {
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(TestAllTypes_NestedMessage, bb_),
  };
  TestAllTypes_NestedMessage_reflection_ =
    ::google::protobuf::internal::GeneratedMessageReflection::NewGeneratedMessageReflection(
      TestAllTypes_NestedMessage_descriptor_,
      TestAllTypes_NestedMessage::internal_default_instance(),
      TestAllTypes_NestedMessage_offsets_,
      GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(TestAllTypes_NestedMessage, _has_bits_),
      -1,
      -1,
      sizeof(TestAllTypes_NestedMessage),
      GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(TestAllTypes_NestedMessage, _internal_metadata_));
  TestAllTypes_OptionalGroup_descriptor_ = TestAllTypes_descriptor_->nested_type(1);
  static const int TestAllTypes_OptionalGroup_offsets_[1] = {
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(TestAllTypes_OptionalGroup, a_),
  };
  TestAllTypes_OptionalGroup_reflection_ =
    ::google::protobuf::internal::GeneratedMessageReflection::NewGeneratedMessageReflection(
      TestAllTypes_OptionalGroup_descriptor_,
      TestAllTypes_OptionalGroup::internal_default_instance(),
      TestAllTypes_OptionalGroup_offsets_,
      GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(TestAllTypes_OptionalGroup, _has_bits_),
      -1,
      -1,
      sizeof(TestAllTypes_OptionalGroup),
      GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(TestAllTypes_OptionalGroup, _internal_metadata_));
  TestAllTypes_RepeatedGroup_descriptor_ = TestAllTypes_descriptor_->nested_type(2);
  static const int TestAllTypes_RepeatedGroup_offsets_[1] = {
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(TestAllTypes_RepeatedGroup, a_),
  };
  TestAllTypes_RepeatedGroup_reflection_ =
    ::google::protobuf::internal::GeneratedMessageReflection::NewGeneratedMessageReflection(
      TestAllTypes_RepeatedGroup_descriptor_,
      TestAllTypes_RepeatedGroup::internal_default_instance(),
      TestAllTypes_RepeatedGroup_offsets_,
      GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(TestAllTypes_RepeatedGroup, _has_bits_),
      -1,
      -1,
      sizeof(TestAllTypes_RepeatedGroup),
      GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(TestAllTypes_RepeatedGroup, _internal_metadata_));
  TestAllTypes_NestedEnum_descriptor_ = TestAllTypes_descriptor_->enum_type(0);
  ForeignMessage_descriptor_ = file->message_type(1);
  static const int ForeignMessage_offsets_[1] = {
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(ForeignMessage, c_),
  };
  ForeignMessage_reflection_ =
    ::google::protobuf::internal::GeneratedMessageReflection::NewGeneratedMessageReflection(
      ForeignMessage_descriptor_,
      ForeignMessage::internal_default_instance(),
      ForeignMessage_offsets_,
      GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(ForeignMessage, _has_bits_),
      -1,
      -1,
      sizeof(ForeignMessage),
      GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(ForeignMessage, _internal_metadata_));
  TestNoArenaMessage_descriptor_ = file->message_type(2);
  static const int TestNoArenaMessage_offsets_[1] = {
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(TestNoArenaMessage, arena_message_),
  };
  TestNoArenaMessage_reflection_ =
    ::google::protobuf::internal::GeneratedMessageReflection::NewGeneratedMessageReflection(
      TestNoArenaMessage_descriptor_,
      TestNoArenaMessage::internal_default_instance(),
      TestNoArenaMessage_offsets_,
      GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(TestNoArenaMessage, _has_bits_),
      -1,
      -1,
      sizeof(TestNoArenaMessage),
      GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(TestNoArenaMessage, _internal_metadata_));
  ForeignEnum_descriptor_ = file->enum_type(0);
}

namespace {

GOOGLE_PROTOBUF_DECLARE_ONCE(protobuf_AssignDescriptors_once_);
void protobuf_AssignDescriptorsOnce() {
  ::google::protobuf::GoogleOnceInit(&protobuf_AssignDescriptors_once_,
                 &protobuf_AssignDesc_google_2fprotobuf_2funittest_5fno_5farena_2eproto);
}

void protobuf_RegisterTypes(const ::std::string&) GOOGLE_ATTRIBUTE_COLD;
void protobuf_RegisterTypes(const ::std::string&) {
  protobuf_AssignDescriptorsOnce();
  ::google::protobuf::MessageFactory::InternalRegisterGeneratedMessage(
      TestAllTypes_descriptor_, TestAllTypes::internal_default_instance());
  ::google::protobuf::MessageFactory::InternalRegisterGeneratedMessage(
      TestAllTypes_NestedMessage_descriptor_, TestAllTypes_NestedMessage::internal_default_instance());
  ::google::protobuf::MessageFactory::InternalRegisterGeneratedMessage(
      TestAllTypes_OptionalGroup_descriptor_, TestAllTypes_OptionalGroup::internal_default_instance());
  ::google::protobuf::MessageFactory::InternalRegisterGeneratedMessage(
      TestAllTypes_RepeatedGroup_descriptor_, TestAllTypes_RepeatedGroup::internal_default_instance());
  ::google::protobuf::MessageFactory::InternalRegisterGeneratedMessage(
      ForeignMessage_descriptor_, ForeignMessage::internal_default_instance());
  ::google::protobuf::MessageFactory::InternalRegisterGeneratedMessage(
      TestNoArenaMessage_descriptor_, TestNoArenaMessage::internal_default_instance());
}

}  // namespace

void protobuf_ShutdownFile_google_2fprotobuf_2funittest_5fno_5farena_2eproto() {
  TestAllTypes_default_instance_.Shutdown();
  delete TestAllTypes_default_oneof_instance_;
  delete TestAllTypes_reflection_;
  delete TestAllTypes::_default_default_string_;
  delete TestAllTypes::_default_default_bytes_;
  delete TestAllTypes::_default_default_string_piece_;
  delete TestAllTypes::_default_default_cord_;
  TestAllTypes_NestedMessage_default_instance_.Shutdown();
  delete TestAllTypes_NestedMessage_reflection_;
  TestAllTypes_OptionalGroup_default_instance_.Shutdown();
  delete TestAllTypes_OptionalGroup_reflection_;
  TestAllTypes_RepeatedGroup_default_instance_.Shutdown();
  delete TestAllTypes_RepeatedGroup_reflection_;
  ForeignMessage_default_instance_.Shutdown();
  delete ForeignMessage_reflection_;
  TestNoArenaMessage_default_instance_.Shutdown();
  delete TestNoArenaMessage_reflection_;
}

void protobuf_InitDefaults_google_2fprotobuf_2funittest_5fno_5farena_2eproto_impl() {
  GOOGLE_PROTOBUF_VERIFY_VERSION;

  ::protobuf_unittest_import::protobuf_InitDefaults_google_2fprotobuf_2funittest_5fimport_2eproto();
  ::proto2_arena_unittest::protobuf_InitDefaults_google_2fprotobuf_2funittest_5farena_2eproto();
  ::google::protobuf::internal::GetEmptyString();
  TestAllTypes::_default_default_string_ =
      new ::std::string("hello", 5);
  TestAllTypes::_default_default_bytes_ =
      new ::std::string("world", 5);
  TestAllTypes::_default_default_string_piece_ =
      new ::std::string("abc", 3);
  TestAllTypes::_default_default_cord_ =
      new ::std::string("123", 3);
  TestAllTypes_default_instance_.DefaultConstruct();
  TestAllTypes_default_oneof_instance_ = new TestAllTypesOneofInstance();
  TestAllTypes_NestedMessage_default_instance_.DefaultConstruct();
  TestAllTypes_OptionalGroup_default_instance_.DefaultConstruct();
  TestAllTypes_RepeatedGroup_default_instance_.DefaultConstruct();
  ForeignMessage_default_instance_.DefaultConstruct();
  TestNoArenaMessage_default_instance_.DefaultConstruct();
  TestAllTypes_default_instance_.get_mutable()->InitAsDefaultInstance();
  TestAllTypes_NestedMessage_default_instance_.get_mutable()->InitAsDefaultInstance();
  TestAllTypes_OptionalGroup_default_instance_.get_mutable()->InitAsDefaultInstance();
  TestAllTypes_RepeatedGroup_default_instance_.get_mutable()->InitAsDefaultInstance();
  ForeignMessage_default_instance_.get_mutable()->InitAsDefaultInstance();
  TestNoArenaMessage_default_instance_.get_mutable()->InitAsDefaultInstance();
}

GOOGLE_PROTOBUF_DECLARE_ONCE(protobuf_InitDefaults_google_2fprotobuf_2funittest_5fno_5farena_2eproto_once_);
void protobuf_InitDefaults_google_2fprotobuf_2funittest_5fno_5farena_2eproto() {
  ::google::protobuf::GoogleOnceInit(&protobuf_InitDefaults_google_2fprotobuf_2funittest_5fno_5farena_2eproto_once_,
                 &protobuf_InitDefaults_google_2fprotobuf_2funittest_5fno_5farena_2eproto_impl);
}
void protobuf_AddDesc_google_2fprotobuf_2funittest_5fno_5farena_2eproto_impl() {
  GOOGLE_PROTOBUF_VERIFY_VERSION;

  protobuf_InitDefaults_google_2fprotobuf_2funittest_5fno_5farena_2eproto();
  ::google::protobuf::DescriptorPool::InternalAddGeneratedFile(
    "\n\'google/protobuf/unittest_no_arena.prot"
    "o\022\032protobuf_unittest_no_arena\032%google/pr"
    "otobuf/unittest_import.proto\032$google/pro"
    "tobuf/unittest_arena.proto\"\320\032\n\014TestAllTy"
    "pes\022\026\n\016optional_int32\030\001 \001(\005\022\026\n\016optional_"
    "int64\030\002 \001(\003\022\027\n\017optional_uint32\030\003 \001(\r\022\027\n\017"
    "optional_uint64\030\004 \001(\004\022\027\n\017optional_sint32"
    "\030\005 \001(\021\022\027\n\017optional_sint64\030\006 \001(\022\022\030\n\020optio"
    "nal_fixed32\030\007 \001(\007\022\030\n\020optional_fixed64\030\010 "
    "\001(\006\022\031\n\021optional_sfixed32\030\t \001(\017\022\031\n\021option"
    "al_sfixed64\030\n \001(\020\022\026\n\016optional_float\030\013 \001("
    "\002\022\027\n\017optional_double\030\014 \001(\001\022\025\n\roptional_b"
    "ool\030\r \001(\010\022\027\n\017optional_string\030\016 \001(\t\022\026\n\016op"
    "tional_bytes\030\017 \001(\014\022M\n\roptionalgroup\030\020 \001("
    "\n26.protobuf_unittest_no_arena.TestAllTy"
    "pes.OptionalGroup\022W\n\027optional_nested_mes"
    "sage\030\022 \001(\01326.protobuf_unittest_no_arena."
    "TestAllTypes.NestedMessage\022L\n\030optional_f"
    "oreign_message\030\023 \001(\0132*.protobuf_unittest"
    "_no_arena.ForeignMessage\022H\n\027optional_imp"
    "ort_message\030\024 \001(\0132\'.protobuf_unittest_im"
    "port.ImportMessage\022Q\n\024optional_nested_en"
    "um\030\025 \001(\01623.protobuf_unittest_no_arena.Te"
    "stAllTypes.NestedEnum\022F\n\025optional_foreig"
    "n_enum\030\026 \001(\0162\'.protobuf_unittest_no_aren"
    "a.ForeignEnum\022B\n\024optional_import_enum\030\027 "
    "\001(\0162$.protobuf_unittest_import.ImportEnu"
    "m\022!\n\025optional_string_piece\030\030 \001(\tB\002\010\002\022\031\n\r"
    "optional_cord\030\031 \001(\tB\002\010\001\022U\n\036optional_publ"
    "ic_import_message\030\032 \001(\0132-.protobuf_unitt"
    "est_import.PublicImportMessage\022T\n\020option"
    "al_message\030\033 \001(\01326.protobuf_unittest_no_"
    "arena.TestAllTypes.NestedMessageB\002(\001\022\026\n\016"
    "repeated_int32\030\037 \003(\005\022\026\n\016repeated_int64\030 "
    " \003(\003\022\027\n\017repeated_uint32\030! \003(\r\022\027\n\017repeate"
    "d_uint64\030\" \003(\004\022\027\n\017repeated_sint32\030# \003(\021\022"
    "\027\n\017repeated_sint64\030$ \003(\022\022\030\n\020repeated_fix"
    "ed32\030% \003(\007\022\030\n\020repeated_fixed64\030& \003(\006\022\031\n\021"
    "repeated_sfixed32\030\' \003(\017\022\031\n\021repeated_sfix"
    "ed64\030( \003(\020\022\026\n\016repeated_float\030) \003(\002\022\027\n\017re"
    "peated_double\030* \003(\001\022\025\n\rrepeated_bool\030+ \003"
    "(\010\022\027\n\017repeated_string\030, \003(\t\022\026\n\016repeated_"
    "bytes\030- \003(\014\022M\n\rrepeatedgroup\030. \003(\n26.pro"
    "tobuf_unittest_no_arena.TestAllTypes.Rep"
    "eatedGroup\022W\n\027repeated_nested_message\0300 "
    "\003(\01326.protobuf_unittest_no_arena.TestAll"
    "Types.NestedMessage\022L\n\030repeated_foreign_"
    "message\0301 \003(\0132*.protobuf_unittest_no_are"
    "na.ForeignMessage\022H\n\027repeated_import_mes"
    "sage\0302 \003(\0132\'.protobuf_unittest_import.Im"
    "portMessage\022Q\n\024repeated_nested_enum\0303 \003("
    "\01623.protobuf_unittest_no_arena.TestAllTy"
    "pes.NestedEnum\022F\n\025repeated_foreign_enum\030"
    "4 \003(\0162\'.protobuf_unittest_no_arena.Forei"
    "gnEnum\022B\n\024repeated_import_enum\0305 \003(\0162$.p"
    "rotobuf_unittest_import.ImportEnum\022!\n\025re"
    "peated_string_piece\0306 \003(\tB\002\010\002\022\031\n\rrepeate"
    "d_cord\0307 \003(\tB\002\010\001\022Y\n\025repeated_lazy_messag"
    "e\0309 \003(\01326.protobuf_unittest_no_arena.Tes"
    "tAllTypes.NestedMessageB\002(\001\022\031\n\rdefault_i"
    "nt32\030= \001(\005:\00241\022\031\n\rdefault_int64\030> \001(\003:\0024"
    "2\022\032\n\016default_uint32\030\? \001(\r:\00243\022\032\n\016default"
    "_uint64\030@ \001(\004:\00244\022\033\n\016default_sint32\030A \001("
    "\021:\003-45\022\032\n\016default_sint64\030B \001(\022:\00246\022\033\n\017de"
    "fault_fixed32\030C \001(\007:\00247\022\033\n\017default_fixed"
    "64\030D \001(\006:\00248\022\034\n\020default_sfixed32\030E \001(\017:\002"
    "49\022\035\n\020default_sfixed64\030F \001(\020:\003-50\022\033\n\rdef"
    "ault_float\030G \001(\002:\00451.5\022\035\n\016default_double"
    "\030H \001(\001:\00552000\022\032\n\014default_bool\030I \001(\010:\004tru"
    "e\022\035\n\016default_string\030J \001(\t:\005hello\022\034\n\rdefa"
    "ult_bytes\030K \001(\014:\005world\022U\n\023default_nested"
    "_enum\030Q \001(\01623.protobuf_unittest_no_arena"
    ".TestAllTypes.NestedEnum:\003BAR\022R\n\024default"
    "_foreign_enum\030R \001(\0162\'.protobuf_unittest_"
    "no_arena.ForeignEnum:\013FOREIGN_BAR\022M\n\023def"
    "ault_import_enum\030S \001(\0162$.protobuf_unitte"
    "st_import.ImportEnum:\nIMPORT_BAR\022%\n\024defa"
    "ult_string_piece\030T \001(\t:\003abcB\002\010\002\022\035\n\014defau"
    "lt_cord\030U \001(\t:\003123B\002\010\001\022\026\n\014oneof_uint32\030o"
    " \001(\rH\000\022V\n\024oneof_nested_message\030p \001(\01326.p"
    "rotobuf_unittest_no_arena.TestAllTypes.N"
    "estedMessageH\000\022\026\n\014oneof_string\030q \001(\tH\000\022\025"
    "\n\013oneof_bytes\030r \001(\014H\000\022_\n\031lazy_oneof_nest"
    "ed_message\030s \001(\01326.protobuf_unittest_no_"
    "arena.TestAllTypes.NestedMessageB\002(\001H\000\032\033"
    "\n\rNestedMessage\022\n\n\002bb\030\001 \001(\005\032\032\n\rOptionalG"
    "roup\022\t\n\001a\030\021 \001(\005\032\032\n\rRepeatedGroup\022\t\n\001a\030/ "
    "\001(\005\"9\n\nNestedEnum\022\007\n\003FOO\020\001\022\007\n\003BAR\020\002\022\007\n\003B"
    "AZ\020\003\022\020\n\003NEG\020\377\377\377\377\377\377\377\377\377\001B\r\n\013oneof_field\"\033\n"
    "\016ForeignMessage\022\t\n\001c\030\001 \001(\005\"P\n\022TestNoAren"
    "aMessage\022:\n\rarena_message\030\001 \001(\0132#.proto2"
    "_arena_unittest.ArenaMessage*@\n\013ForeignE"
    "num\022\017\n\013FOREIGN_FOO\020\004\022\017\n\013FOREIGN_BAR\020\005\022\017\n"
    "\013FOREIGN_BAZ\020\006B%B\rUnittestProtoH\001\200\001\001\210\001\001\220"
    "\001\001\370\001\000\242\002\005NOARN", 3773);
  ::google::protobuf::MessageFactory::InternalRegisterGeneratedFile(
    "google/protobuf/unittest_no_arena.proto", &protobuf_RegisterTypes);
  ::protobuf_unittest_import::protobuf_AddDesc_google_2fprotobuf_2funittest_5fimport_2eproto();
  ::proto2_arena_unittest::protobuf_AddDesc_google_2fprotobuf_2funittest_5farena_2eproto();
  ::google::protobuf::internal::OnShutdown(&protobuf_ShutdownFile_google_2fprotobuf_2funittest_5fno_5farena_2eproto);
}

GOOGLE_PROTOBUF_DECLARE_ONCE(protobuf_AddDesc_google_2fprotobuf_2funittest_5fno_5farena_2eproto_once_);
void protobuf_AddDesc_google_2fprotobuf_2funittest_5fno_5farena_2eproto() {
  ::google::protobuf::GoogleOnceInit(&protobuf_AddDesc_google_2fprotobuf_2funittest_5fno_5farena_2eproto_once_,
                 &protobuf_AddDesc_google_2fprotobuf_2funittest_5fno_5farena_2eproto_impl);
}
// Force AddDescriptors() to be called at static initialization time.
struct StaticDescriptorInitializer_google_2fprotobuf_2funittest_5fno_5farena_2eproto {
  StaticDescriptorInitializer_google_2fprotobuf_2funittest_5fno_5farena_2eproto() {
    protobuf_AddDesc_google_2fprotobuf_2funittest_5fno_5farena_2eproto();
  }
} static_descriptor_initializer_google_2fprotobuf_2funittest_5fno_5farena_2eproto_;
const ::google::protobuf::EnumDescriptor* ForeignEnum_descriptor() {
  protobuf_AssignDescriptorsOnce();
  return ForeignEnum_descriptor_;
}
bool ForeignEnum_IsValid(int value) {
  switch (value) {
    case 4:
    case 5:
    case 6:
      return true;
    default:
      return false;
  }
}


namespace {

static void MergeFromFail(int line) GOOGLE_ATTRIBUTE_COLD GOOGLE_ATTRIBUTE_NORETURN;
static void MergeFromFail(int line) {
  ::google::protobuf::internal::MergeFromFail(__FILE__, line);
}

}  // namespace


// ===================================================================

const ::google::protobuf::EnumDescriptor* TestAllTypes_NestedEnum_descriptor() {
  protobuf_AssignDescriptorsOnce();
  return TestAllTypes_NestedEnum_descriptor_;
}
bool TestAllTypes_NestedEnum_IsValid(int value) {
  switch (value) {
    case -1:
    case 1:
    case 2:
    case 3:
      return true;
    default:
      return false;
  }
}

#if !defined(_MSC_VER) || _MSC_VER >= 1900
const TestAllTypes_NestedEnum TestAllTypes::FOO;
const TestAllTypes_NestedEnum TestAllTypes::BAR;
const TestAllTypes_NestedEnum TestAllTypes::BAZ;
const TestAllTypes_NestedEnum TestAllTypes::NEG;
const TestAllTypes_NestedEnum TestAllTypes::NestedEnum_MIN;
const TestAllTypes_NestedEnum TestAllTypes::NestedEnum_MAX;
const int TestAllTypes::NestedEnum_ARRAYSIZE;
#endif  // !defined(_MSC_VER) || _MSC_VER >= 1900
#if !defined(_MSC_VER) || _MSC_VER >= 1900
const int TestAllTypes_NestedMessage::kBbFieldNumber;
#endif  // !defined(_MSC_VER) || _MSC_VER >= 1900

TestAllTypes_NestedMessage::TestAllTypes_NestedMessage()
  : ::google::protobuf::Message(), _internal_metadata_(NULL) {
  if (this != internal_default_instance()) protobuf_InitDefaults_google_2fprotobuf_2funittest_5fno_5farena_2eproto();
  SharedCtor();
  // @@protoc_insertion_point(constructor:protobuf_unittest_no_arena.TestAllTypes.NestedMessage)
}

void TestAllTypes_NestedMessage::InitAsDefaultInstance() {
}

TestAllTypes_NestedMessage::TestAllTypes_NestedMessage(const TestAllTypes_NestedMessage& from)
  : ::google::protobuf::Message(),
    _internal_metadata_(NULL) {
  SharedCtor();
  UnsafeMergeFrom(from);
  // @@protoc_insertion_point(copy_constructor:protobuf_unittest_no_arena.TestAllTypes.NestedMessage)
}

void TestAllTypes_NestedMessage::SharedCtor() {
  _cached_size_ = 0;
  bb_ = 0;
}

TestAllTypes_NestedMessage::~TestAllTypes_NestedMessage() {
  // @@protoc_insertion_point(destructor:protobuf_unittest_no_arena.TestAllTypes.NestedMessage)
  SharedDtor();
}

void TestAllTypes_NestedMessage::SharedDtor() {
}

void TestAllTypes_NestedMessage::SetCachedSize(int size) const {
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
}
const ::google::protobuf::Descriptor* TestAllTypes_NestedMessage::descriptor() {
  protobuf_AssignDescriptorsOnce();
  return TestAllTypes_NestedMessage_descriptor_;
}

const TestAllTypes_NestedMessage& TestAllTypes_NestedMessage::default_instance() {
  protobuf_InitDefaults_google_2fprotobuf_2funittest_5fno_5farena_2eproto();
  return *internal_default_instance();
}

::google::protobuf::internal::ExplicitlyConstructed<TestAllTypes_NestedMessage> TestAllTypes_NestedMessage_default_instance_;

TestAllTypes_NestedMessage* TestAllTypes_NestedMessage::New(::google::protobuf::Arena* arena) const {
  TestAllTypes_NestedMessage* n = new TestAllTypes_NestedMessage;
  if (arena != NULL) {
    arena->Own(n);
  }
  return n;
}

void TestAllTypes_NestedMessage::Clear() {
// @@protoc_insertion_point(message_clear_start:protobuf_unittest_no_arena.TestAllTypes.NestedMessage)
  bb_ = 0;
  _has_bits_.Clear();
  if (_internal_metadata_.have_unknown_fields()) {
    mutable_unknown_fields()->Clear();
  }
}

bool TestAllTypes_NestedMessage::MergePartialFromCodedStream(
    ::google::protobuf::io::CodedInputStream* input) {
#define DO_(EXPRESSION) if (!GOOGLE_PREDICT_TRUE(EXPRESSION)) goto failure
  ::google::protobuf::uint32 tag;
  // @@protoc_insertion_point(parse_start:protobuf_unittest_no_arena.TestAllTypes.NestedMessage)
  for (;;) {
    ::std::pair< ::google::protobuf::uint32, bool> p = input->ReadTagWithCutoff(127);
    tag = p.first;
    if (!p.second) goto handle_unusual;
    switch (::google::protobuf::internal::WireFormatLite::GetTagFieldNumber(tag)) {
      // optional int32 bb = 1;
      case 1: {
        if (tag == 8) {
          set_has_bb();
          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int32, ::google::protobuf::internal::WireFormatLite::TYPE_INT32>(
                 input, &bb_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectAtEnd()) goto success;
        break;
      }

      default: {
      handle_unusual:
        if (tag == 0 ||
            ::google::protobuf::internal::WireFormatLite::GetTagWireType(tag) ==
            ::google::protobuf::internal::WireFormatLite::WIRETYPE_END_GROUP) {
          goto success;
        }
        DO_(::google::protobuf::internal::WireFormat::SkipField(
              input, tag, mutable_unknown_fields()));
        break;
      }
    }
  }
success:
  // @@protoc_insertion_point(parse_success:protobuf_unittest_no_arena.TestAllTypes.NestedMessage)
  return true;
failure:
  // @@protoc_insertion_point(parse_failure:protobuf_unittest_no_arena.TestAllTypes.NestedMessage)
  return false;
#undef DO_
}

void TestAllTypes_NestedMessage::SerializeWithCachedSizes(
    ::google::protobuf::io::CodedOutputStream* output) const {
  // @@protoc_insertion_point(serialize_start:protobuf_unittest_no_arena.TestAllTypes.NestedMessage)
  // optional int32 bb = 1;
  if (has_bb()) {
    ::google::protobuf::internal::WireFormatLite::WriteInt32(1, this->bb(), output);
  }

  if (_internal_metadata_.have_unknown_fields()) {
    ::google::protobuf::internal::WireFormat::SerializeUnknownFields(
        unknown_fields(), output);
  }
  // @@protoc_insertion_point(serialize_end:protobuf_unittest_no_arena.TestAllTypes.NestedMessage)
}

::google::protobuf::uint8* TestAllTypes_NestedMessage::InternalSerializeWithCachedSizesToArray(
    bool deterministic, ::google::protobuf::uint8* target) const {
  (void)deterministic; // Unused
  // @@protoc_insertion_point(serialize_to_array_start:protobuf_unittest_no_arena.TestAllTypes.NestedMessage)
  // optional int32 bb = 1;
  if (has_bb()) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt32ToArray(1, this->bb(), target);
  }

  if (_internal_metadata_.have_unknown_fields()) {
    target = ::google::protobuf::internal::WireFormat::SerializeUnknownFieldsToArray(
        unknown_fields(), target);
  }
  // @@protoc_insertion_point(serialize_to_array_end:protobuf_unittest_no_arena.TestAllTypes.NestedMessage)
  return target;
}

size_t TestAllTypes_NestedMessage::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:protobuf_unittest_no_arena.TestAllTypes.NestedMessage)
  size_t total_size = 0;

  // optional int32 bb = 1;
  if (has_bb()) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::Int32Size(
        this->bb());
  }

  if (_internal_metadata_.have_unknown_fields()) {
    total_size +=
      ::google::protobuf::internal::WireFormat::ComputeUnknownFieldsSize(
        unknown_fields());
  }
  int cached_size = ::google::protobuf::internal::ToCachedSize(total_size);
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = cached_size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
  return total_size;
}

void TestAllTypes_NestedMessage::MergeFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_merge_from_start:protobuf_unittest_no_arena.TestAllTypes.NestedMessage)
  if (GOOGLE_PREDICT_FALSE(&from == this)) MergeFromFail(__LINE__);
  const TestAllTypes_NestedMessage* source =
      ::google::protobuf::internal::DynamicCastToGenerated<const TestAllTypes_NestedMessage>(
          &from);
  if (source == NULL) {
  // @@protoc_insertion_point(generalized_merge_from_cast_fail:protobuf_unittest_no_arena.TestAllTypes.NestedMessage)
    ::google::protobuf::internal::ReflectionOps::Merge(from, this);
  } else {
  // @@protoc_insertion_point(generalized_merge_from_cast_success:protobuf_unittest_no_arena.TestAllTypes.NestedMessage)
    UnsafeMergeFrom(*source);
  }
}

void TestAllTypes_NestedMessage::MergeFrom(const TestAllTypes_NestedMessage& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:protobuf_unittest_no_arena.TestAllTypes.NestedMessage)
  if (GOOGLE_PREDICT_TRUE(&from != this)) {
    UnsafeMergeFrom(from);
  } else {
    MergeFromFail(__LINE__);
  }
}

void TestAllTypes_NestedMessage::UnsafeMergeFrom(const TestAllTypes_NestedMessage& from) {
  GOOGLE_DCHECK(&from != this);
  if (from._has_bits_[0 / 32] & (0xffu << (0 % 32))) {
    if (from.has_bb()) {
      set_bb(from.bb());
    }
  }
  if (from._internal_metadata_.have_unknown_fields()) {
    ::google::protobuf::UnknownFieldSet::MergeToInternalMetdata(
      from.unknown_fields(), &_internal_metadata_);
  }
}

void TestAllTypes_NestedMessage::CopyFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_copy_from_start:protobuf_unittest_no_arena.TestAllTypes.NestedMessage)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

void TestAllTypes_NestedMessage::CopyFrom(const TestAllTypes_NestedMessage& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:protobuf_unittest_no_arena.TestAllTypes.NestedMessage)
  if (&from == this) return;
  Clear();
  UnsafeMergeFrom(from);
}

bool TestAllTypes_NestedMessage::IsInitialized() const {

  return true;
}

void TestAllTypes_NestedMessage::Swap(TestAllTypes_NestedMessage* other) {
  if (other == this) return;
  InternalSwap(other);
}
void TestAllTypes_NestedMessage::InternalSwap(TestAllTypes_NestedMessage* other) {
  std::swap(bb_, other->bb_);
  std::swap(_has_bits_[0], other->_has_bits_[0]);
  _internal_metadata_.Swap(&other->_internal_metadata_);
  std::swap(_cached_size_, other->_cached_size_);
}

::google::protobuf::Metadata TestAllTypes_NestedMessage::GetMetadata() const {
  protobuf_AssignDescriptorsOnce();
  ::google::protobuf::Metadata metadata;
  metadata.descriptor = TestAllTypes_NestedMessage_descriptor_;
  metadata.reflection = TestAllTypes_NestedMessage_reflection_;
  return metadata;
}


// -------------------------------------------------------------------

#if !defined(_MSC_VER) || _MSC_VER >= 1900
const int TestAllTypes_OptionalGroup::kAFieldNumber;
#endif  // !defined(_MSC_VER) || _MSC_VER >= 1900

TestAllTypes_OptionalGroup::TestAllTypes_OptionalGroup()
  : ::google::protobuf::Message(), _internal_metadata_(NULL) {
  if (this != internal_default_instance()) protobuf_InitDefaults_google_2fprotobuf_2funittest_5fno_5farena_2eproto();
  SharedCtor();
  // @@protoc_insertion_point(constructor:protobuf_unittest_no_arena.TestAllTypes.OptionalGroup)
}

void TestAllTypes_OptionalGroup::InitAsDefaultInstance() {
}

TestAllTypes_OptionalGroup::TestAllTypes_OptionalGroup(const TestAllTypes_OptionalGroup& from)
  : ::google::protobuf::Message(),
    _internal_metadata_(NULL) {
  SharedCtor();
  UnsafeMergeFrom(from);
  // @@protoc_insertion_point(copy_constructor:protobuf_unittest_no_arena.TestAllTypes.OptionalGroup)
}

void TestAllTypes_OptionalGroup::SharedCtor() {
  _cached_size_ = 0;
  a_ = 0;
}

TestAllTypes_OptionalGroup::~TestAllTypes_OptionalGroup() {
  // @@protoc_insertion_point(destructor:protobuf_unittest_no_arena.TestAllTypes.OptionalGroup)
  SharedDtor();
}

void TestAllTypes_OptionalGroup::SharedDtor() {
}

void TestAllTypes_OptionalGroup::SetCachedSize(int size) const {
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
}
const ::google::protobuf::Descriptor* TestAllTypes_OptionalGroup::descriptor() {
  protobuf_AssignDescriptorsOnce();
  return TestAllTypes_OptionalGroup_descriptor_;
}

const TestAllTypes_OptionalGroup& TestAllTypes_OptionalGroup::default_instance() {
  protobuf_InitDefaults_google_2fprotobuf_2funittest_5fno_5farena_2eproto();
  return *internal_default_instance();
}

::google::protobuf::internal::ExplicitlyConstructed<TestAllTypes_OptionalGroup> TestAllTypes_OptionalGroup_default_instance_;

TestAllTypes_OptionalGroup* TestAllTypes_OptionalGroup::New(::google::protobuf::Arena* arena) const {
  TestAllTypes_OptionalGroup* n = new TestAllTypes_OptionalGroup;
  if (arena != NULL) {
    arena->Own(n);
  }
  return n;
}

void TestAllTypes_OptionalGroup::Clear() {
// @@protoc_insertion_point(message_clear_start:protobuf_unittest_no_arena.TestAllTypes.OptionalGroup)
  a_ = 0;
  _has_bits_.Clear();
  if (_internal_metadata_.have_unknown_fields()) {
    mutable_unknown_fields()->Clear();
  }
}

bool TestAllTypes_OptionalGroup::MergePartialFromCodedStream(
    ::google::protobuf::io::CodedInputStream* input) {
#define DO_(EXPRESSION) if (!GOOGLE_PREDICT_TRUE(EXPRESSION)) goto failure
  ::google::protobuf::uint32 tag;
  // @@protoc_insertion_point(parse_start:protobuf_unittest_no_arena.TestAllTypes.OptionalGroup)
  for (;;) {
    ::std::pair< ::google::protobuf::uint32, bool> p = input->ReadTagWithCutoff(16383);
    tag = p.first;
    if (!p.second) goto handle_unusual;
    switch (::google::protobuf::internal::WireFormatLite::GetTagFieldNumber(tag)) {
      // optional int32 a = 17;
      case 17: {
        if (tag == 136) {
          set_has_a();
          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int32, ::google::protobuf::internal::WireFormatLite::TYPE_INT32>(
                 input, &a_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectAtEnd()) goto success;
        break;
      }

      default: {
      handle_unusual:
        if (tag == 0 ||
            ::google::protobuf::internal::WireFormatLite::GetTagWireType(tag) ==
            ::google::protobuf::internal::WireFormatLite::WIRETYPE_END_GROUP) {
          goto success;
        }
        DO_(::google::protobuf::internal::WireFormat::SkipField(
              input, tag, mutable_unknown_fields()));
        break;
      }
    }
  }
success:
  // @@protoc_insertion_point(parse_success:protobuf_unittest_no_arena.TestAllTypes.OptionalGroup)
  return true;
failure:
  // @@protoc_insertion_point(parse_failure:protobuf_unittest_no_arena.TestAllTypes.OptionalGroup)
  return false;
#undef DO_
}

void TestAllTypes_OptionalGroup::SerializeWithCachedSizes(
    ::google::protobuf::io::CodedOutputStream* output) const {
  // @@protoc_insertion_point(serialize_start:protobuf_unittest_no_arena.TestAllTypes.OptionalGroup)
  // optional int32 a = 17;
  if (has_a()) {
    ::google::protobuf::internal::WireFormatLite::WriteInt32(17, this->a(), output);
  }

  if (_internal_metadata_.have_unknown_fields()) {
    ::google::protobuf::internal::WireFormat::SerializeUnknownFields(
        unknown_fields(), output);
  }
  // @@protoc_insertion_point(serialize_end:protobuf_unittest_no_arena.TestAllTypes.OptionalGroup)
}

::google::protobuf::uint8* TestAllTypes_OptionalGroup::InternalSerializeWithCachedSizesToArray(
    bool deterministic, ::google::protobuf::uint8* target) const {
  (void)deterministic; // Unused
  // @@protoc_insertion_point(serialize_to_array_start:protobuf_unittest_no_arena.TestAllTypes.OptionalGroup)
  // optional int32 a = 17;
  if (has_a()) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt32ToArray(17, this->a(), target);
  }

  if (_internal_metadata_.have_unknown_fields()) {
    target = ::google::protobuf::internal::WireFormat::SerializeUnknownFieldsToArray(
        unknown_fields(), target);
  }
  // @@protoc_insertion_point(serialize_to_array_end:protobuf_unittest_no_arena.TestAllTypes.OptionalGroup)
  return target;
}

size_t TestAllTypes_OptionalGroup::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:protobuf_unittest_no_arena.TestAllTypes.OptionalGroup)
  size_t total_size = 0;

  // optional int32 a = 17;
  if (has_a()) {
    total_size += 2 +
      ::google::protobuf::internal::WireFormatLite::Int32Size(
        this->a());
  }

  if (_internal_metadata_.have_unknown_fields()) {
    total_size +=
      ::google::protobuf::internal::WireFormat::ComputeUnknownFieldsSize(
        unknown_fields());
  }
  int cached_size = ::google::protobuf::internal::ToCachedSize(total_size);
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = cached_size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
  return total_size;
}

void TestAllTypes_OptionalGroup::MergeFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_merge_from_start:protobuf_unittest_no_arena.TestAllTypes.OptionalGroup)
  if (GOOGLE_PREDICT_FALSE(&from == this)) MergeFromFail(__LINE__);
  const TestAllTypes_OptionalGroup* source =
      ::google::protobuf::internal::DynamicCastToGenerated<const TestAllTypes_OptionalGroup>(
          &from);
  if (source == NULL) {
  // @@protoc_insertion_point(generalized_merge_from_cast_fail:protobuf_unittest_no_arena.TestAllTypes.OptionalGroup)
    ::google::protobuf::internal::ReflectionOps::Merge(from, this);
  } else {
  // @@protoc_insertion_point(generalized_merge_from_cast_success:protobuf_unittest_no_arena.TestAllTypes.OptionalGroup)
    UnsafeMergeFrom(*source);
  }
}

void TestAllTypes_OptionalGroup::MergeFrom(const TestAllTypes_OptionalGroup& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:protobuf_unittest_no_arena.TestAllTypes.OptionalGroup)
  if (GOOGLE_PREDICT_TRUE(&from != this)) {
    UnsafeMergeFrom(from);
  } else {
    MergeFromFail(__LINE__);
  }
}

void TestAllTypes_OptionalGroup::UnsafeMergeFrom(const TestAllTypes_OptionalGroup& from) {
  GOOGLE_DCHECK(&from != this);
  if (from._has_bits_[0 / 32] & (0xffu << (0 % 32))) {
    if (from.has_a()) {
      set_a(from.a());
    }
  }
  if (from._internal_metadata_.have_unknown_fields()) {
    ::google::protobuf::UnknownFieldSet::MergeToInternalMetdata(
      from.unknown_fields(), &_internal_metadata_);
  }
}

void TestAllTypes_OptionalGroup::CopyFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_copy_from_start:protobuf_unittest_no_arena.TestAllTypes.OptionalGroup)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

void TestAllTypes_OptionalGroup::CopyFrom(const TestAllTypes_OptionalGroup& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:protobuf_unittest_no_arena.TestAllTypes.OptionalGroup)
  if (&from == this) return;
  Clear();
  UnsafeMergeFrom(from);
}

bool TestAllTypes_OptionalGroup::IsInitialized() const {

  return true;
}

void TestAllTypes_OptionalGroup::Swap(TestAllTypes_OptionalGroup* other) {
  if (other == this) return;
  InternalSwap(other);
}
void TestAllTypes_OptionalGroup::InternalSwap(TestAllTypes_OptionalGroup* other) {
  std::swap(a_, other->a_);
  std::swap(_has_bits_[0], other->_has_bits_[0]);
  _internal_metadata_.Swap(&other->_internal_metadata_);
  std::swap(_cached_size_, other->_cached_size_);
}

::google::protobuf::Metadata TestAllTypes_OptionalGroup::GetMetadata() const {
  protobuf_AssignDescriptorsOnce();
  ::google::protobuf::Metadata metadata;
  metadata.descriptor = TestAllTypes_OptionalGroup_descriptor_;
  metadata.reflection = TestAllTypes_OptionalGroup_reflection_;
  return metadata;
}


// -------------------------------------------------------------------

#if !defined(_MSC_VER) || _MSC_VER >= 1900
const int TestAllTypes_RepeatedGroup::kAFieldNumber;
#endif  // !defined(_MSC_VER) || _MSC_VER >= 1900

TestAllTypes_RepeatedGroup::TestAllTypes_RepeatedGroup()
  : ::google::protobuf::Message(), _internal_metadata_(NULL) {
  if (this != internal_default_instance()) protobuf_InitDefaults_google_2fprotobuf_2funittest_5fno_5farena_2eproto();
  SharedCtor();
  // @@protoc_insertion_point(constructor:protobuf_unittest_no_arena.TestAllTypes.RepeatedGroup)
}

void TestAllTypes_RepeatedGroup::InitAsDefaultInstance() {
}

TestAllTypes_RepeatedGroup::TestAllTypes_RepeatedGroup(const TestAllTypes_RepeatedGroup& from)
  : ::google::protobuf::Message(),
    _internal_metadata_(NULL) {
  SharedCtor();
  UnsafeMergeFrom(from);
  // @@protoc_insertion_point(copy_constructor:protobuf_unittest_no_arena.TestAllTypes.RepeatedGroup)
}

void TestAllTypes_RepeatedGroup::SharedCtor() {
  _cached_size_ = 0;
  a_ = 0;
}

TestAllTypes_RepeatedGroup::~TestAllTypes_RepeatedGroup() {
  // @@protoc_insertion_point(destructor:protobuf_unittest_no_arena.TestAllTypes.RepeatedGroup)
  SharedDtor();
}

void TestAllTypes_RepeatedGroup::SharedDtor() {
}

void TestAllTypes_RepeatedGroup::SetCachedSize(int size) const {
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
}
const ::google::protobuf::Descriptor* TestAllTypes_RepeatedGroup::descriptor() {
  protobuf_AssignDescriptorsOnce();
  return TestAllTypes_RepeatedGroup_descriptor_;
}

const TestAllTypes_RepeatedGroup& TestAllTypes_RepeatedGroup::default_instance() {
  protobuf_InitDefaults_google_2fprotobuf_2funittest_5fno_5farena_2eproto();
  return *internal_default_instance();
}

::google::protobuf::internal::ExplicitlyConstructed<TestAllTypes_RepeatedGroup> TestAllTypes_RepeatedGroup_default_instance_;

TestAllTypes_RepeatedGroup* TestAllTypes_RepeatedGroup::New(::google::protobuf::Arena* arena) const {
  TestAllTypes_RepeatedGroup* n = new TestAllTypes_RepeatedGroup;
  if (arena != NULL) {
    arena->Own(n);
  }
  return n;
}

void TestAllTypes_RepeatedGroup::Clear() {
// @@protoc_insertion_point(message_clear_start:protobuf_unittest_no_arena.TestAllTypes.RepeatedGroup)
  a_ = 0;
  _has_bits_.Clear();
  if (_internal_metadata_.have_unknown_fields()) {
    mutable_unknown_fields()->Clear();
  }
}

bool TestAllTypes_RepeatedGroup::MergePartialFromCodedStream(
    ::google::protobuf::io::CodedInputStream* input) {
#define DO_(EXPRESSION) if (!GOOGLE_PREDICT_TRUE(EXPRESSION)) goto failure
  ::google::protobuf::uint32 tag;
  // @@protoc_insertion_point(parse_start:protobuf_unittest_no_arena.TestAllTypes.RepeatedGroup)
  for (;;) {
    ::std::pair< ::google::protobuf::uint32, bool> p = input->ReadTagWithCutoff(16383);
    tag = p.first;
    if (!p.second) goto handle_unusual;
    switch (::google::protobuf::internal::WireFormatLite::GetTagFieldNumber(tag)) {
      // optional int32 a = 47;
      case 47: {
        if (tag == 376) {
          set_has_a();
          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int32, ::google::protobuf::internal::WireFormatLite::TYPE_INT32>(
                 input, &a_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectAtEnd()) goto success;
        break;
      }

      default: {
      handle_unusual:
        if (tag == 0 ||
            ::google::protobuf::internal::WireFormatLite::GetTagWireType(tag) ==
            ::google::protobuf::internal::WireFormatLite::WIRETYPE_END_GROUP) {
          goto success;
        }
        DO_(::google::protobuf::internal::WireFormat::SkipField(
              input, tag, mutable_unknown_fields()));
        break;
      }
    }
  }
success:
  // @@protoc_insertion_point(parse_success:protobuf_unittest_no_arena.TestAllTypes.RepeatedGroup)
  return true;
failure:
  // @@protoc_insertion_point(parse_failure:protobuf_unittest_no_arena.TestAllTypes.RepeatedGroup)
  return false;
#undef DO_
}

void TestAllTypes_RepeatedGroup::SerializeWithCachedSizes(
    ::google::protobuf::io::CodedOutputStream* output) const {
  // @@protoc_insertion_point(serialize_start:protobuf_unittest_no_arena.TestAllTypes.RepeatedGroup)
  // optional int32 a = 47;
  if (has_a()) {
    ::google::protobuf::internal::WireFormatLite::WriteInt32(47, this->a(), output);
  }

  if (_internal_metadata_.have_unknown_fields()) {
    ::google::protobuf::internal::WireFormat::SerializeUnknownFields(
        unknown_fields(), output);
  }
  // @@protoc_insertion_point(serialize_end:protobuf_unittest_no_arena.TestAllTypes.RepeatedGroup)
}

::google::protobuf::uint8* TestAllTypes_RepeatedGroup::InternalSerializeWithCachedSizesToArray(
    bool deterministic, ::google::protobuf::uint8* target) const {
  (void)deterministic; // Unused
  // @@protoc_insertion_point(serialize_to_array_start:protobuf_unittest_no_arena.TestAllTypes.RepeatedGroup)
  // optional int32 a = 47;
  if (has_a()) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt32ToArray(47, this->a(), target);
  }

  if (_internal_metadata_.have_unknown_fields()) {
    target = ::google::protobuf::internal::WireFormat::SerializeUnknownFieldsToArray(
        unknown_fields(), target);
  }
  // @@protoc_insertion_point(serialize_to_array_end:protobuf_unittest_no_arena.TestAllTypes.RepeatedGroup)
  return target;
}

size_t TestAllTypes_RepeatedGroup::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:protobuf_unittest_no_arena.TestAllTypes.RepeatedGroup)
  size_t total_size = 0;

  // optional int32 a = 47;
  if (has_a()) {
    total_size += 2 +
      ::google::protobuf::internal::WireFormatLite::Int32Size(
        this->a());
  }

  if (_internal_metadata_.have_unknown_fields()) {
    total_size +=
      ::google::protobuf::internal::WireFormat::ComputeUnknownFieldsSize(
        unknown_fields());
  }
  int cached_size = ::google::protobuf::internal::ToCachedSize(total_size);
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = cached_size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
  return total_size;
}

void TestAllTypes_RepeatedGroup::MergeFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_merge_from_start:protobuf_unittest_no_arena.TestAllTypes.RepeatedGroup)
  if (GOOGLE_PREDICT_FALSE(&from == this)) MergeFromFail(__LINE__);
  const TestAllTypes_RepeatedGroup* source =
      ::google::protobuf::internal::DynamicCastToGenerated<const TestAllTypes_RepeatedGroup>(
          &from);
  if (source == NULL) {
  // @@protoc_insertion_point(generalized_merge_from_cast_fail:protobuf_unittest_no_arena.TestAllTypes.RepeatedGroup)
    ::google::protobuf::internal::ReflectionOps::Merge(from, this);
  } else {
  // @@protoc_insertion_point(generalized_merge_from_cast_success:protobuf_unittest_no_arena.TestAllTypes.RepeatedGroup)
    UnsafeMergeFrom(*source);
  }
}

void TestAllTypes_RepeatedGroup::MergeFrom(const TestAllTypes_RepeatedGroup& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:protobuf_unittest_no_arena.TestAllTypes.RepeatedGroup)
  if (GOOGLE_PREDICT_TRUE(&from != this)) {
    UnsafeMergeFrom(from);
  } else {
    MergeFromFail(__LINE__);
  }
}

void TestAllTypes_RepeatedGroup::UnsafeMergeFrom(const TestAllTypes_RepeatedGroup& from) {
  GOOGLE_DCHECK(&from != this);
  if (from._has_bits_[0 / 32] & (0xffu << (0 % 32))) {
    if (from.has_a()) {
      set_a(from.a());
    }
  }
  if (from._internal_metadata_.have_unknown_fields()) {
    ::google::protobuf::UnknownFieldSet::MergeToInternalMetdata(
      from.unknown_fields(), &_internal_metadata_);
  }
}

void TestAllTypes_RepeatedGroup::CopyFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_copy_from_start:protobuf_unittest_no_arena.TestAllTypes.RepeatedGroup)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

void TestAllTypes_RepeatedGroup::CopyFrom(const TestAllTypes_RepeatedGroup& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:protobuf_unittest_no_arena.TestAllTypes.RepeatedGroup)
  if (&from == this) return;
  Clear();
  UnsafeMergeFrom(from);
}

bool TestAllTypes_RepeatedGroup::IsInitialized() const {

  return true;
}

void TestAllTypes_RepeatedGroup::Swap(TestAllTypes_RepeatedGroup* other) {
  if (other == this) return;
  InternalSwap(other);
}
void TestAllTypes_RepeatedGroup::InternalSwap(TestAllTypes_RepeatedGroup* other) {
  std::swap(a_, other->a_);
  std::swap(_has_bits_[0], other->_has_bits_[0]);
  _internal_metadata_.Swap(&other->_internal_metadata_);
  std::swap(_cached_size_, other->_cached_size_);
}

::google::protobuf::Metadata TestAllTypes_RepeatedGroup::GetMetadata() const {
  protobuf_AssignDescriptorsOnce();
  ::google::protobuf::Metadata metadata;
  metadata.descriptor = TestAllTypes_RepeatedGroup_descriptor_;
  metadata.reflection = TestAllTypes_RepeatedGroup_reflection_;
  return metadata;
}


// -------------------------------------------------------------------

::std::string* TestAllTypes::_default_default_string_ = NULL;
::std::string* TestAllTypes::_default_default_bytes_ = NULL;
::std::string* TestAllTypes::_default_default_string_piece_ = NULL;
::std::string* TestAllTypes::_default_default_cord_ = NULL;
#if !defined(_MSC_VER) || _MSC_VER >= 1900
const int TestAllTypes::kOptionalInt32FieldNumber;
const int TestAllTypes::kOptionalInt64FieldNumber;
const int TestAllTypes::kOptionalUint32FieldNumber;
const int TestAllTypes::kOptionalUint64FieldNumber;
const int TestAllTypes::kOptionalSint32FieldNumber;
const int TestAllTypes::kOptionalSint64FieldNumber;
const int TestAllTypes::kOptionalFixed32FieldNumber;
const int TestAllTypes::kOptionalFixed64FieldNumber;
const int TestAllTypes::kOptionalSfixed32FieldNumber;
const int TestAllTypes::kOptionalSfixed64FieldNumber;
const int TestAllTypes::kOptionalFloatFieldNumber;
const int TestAllTypes::kOptionalDoubleFieldNumber;
const int TestAllTypes::kOptionalBoolFieldNumber;
const int TestAllTypes::kOptionalStringFieldNumber;
const int TestAllTypes::kOptionalBytesFieldNumber;
const int TestAllTypes::kOptionalgroupFieldNumber;
const int TestAllTypes::kOptionalNestedMessageFieldNumber;
const int TestAllTypes::kOptionalForeignMessageFieldNumber;
const int TestAllTypes::kOptionalImportMessageFieldNumber;
const int TestAllTypes::kOptionalNestedEnumFieldNumber;
const int TestAllTypes::kOptionalForeignEnumFieldNumber;
const int TestAllTypes::kOptionalImportEnumFieldNumber;
const int TestAllTypes::kOptionalStringPieceFieldNumber;
const int TestAllTypes::kOptionalCordFieldNumber;
const int TestAllTypes::kOptionalPublicImportMessageFieldNumber;
const int TestAllTypes::kOptionalMessageFieldNumber;
const int TestAllTypes::kRepeatedInt32FieldNumber;
const int TestAllTypes::kRepeatedInt64FieldNumber;
const int TestAllTypes::kRepeatedUint32FieldNumber;
const int TestAllTypes::kRepeatedUint64FieldNumber;
const int TestAllTypes::kRepeatedSint32FieldNumber;
const int TestAllTypes::kRepeatedSint64FieldNumber;
const int TestAllTypes::kRepeatedFixed32FieldNumber;
const int TestAllTypes::kRepeatedFixed64FieldNumber;
const int TestAllTypes::kRepeatedSfixed32FieldNumber;
const int TestAllTypes::kRepeatedSfixed64FieldNumber;
const int TestAllTypes::kRepeatedFloatFieldNumber;
const int TestAllTypes::kRepeatedDoubleFieldNumber;
const int TestAllTypes::kRepeatedBoolFieldNumber;
const int TestAllTypes::kRepeatedStringFieldNumber;
const int TestAllTypes::kRepeatedBytesFieldNumber;
const int TestAllTypes::kRepeatedgroupFieldNumber;
const int TestAllTypes::kRepeatedNestedMessageFieldNumber;
const int TestAllTypes::kRepeatedForeignMessageFieldNumber;
const int TestAllTypes::kRepeatedImportMessageFieldNumber;
const int TestAllTypes::kRepeatedNestedEnumFieldNumber;
const int TestAllTypes::kRepeatedForeignEnumFieldNumber;
const int TestAllTypes::kRepeatedImportEnumFieldNumber;
const int TestAllTypes::kRepeatedStringPieceFieldNumber;
const int TestAllTypes::kRepeatedCordFieldNumber;
const int TestAllTypes::kRepeatedLazyMessageFieldNumber;
const int TestAllTypes::kDefaultInt32FieldNumber;
const int TestAllTypes::kDefaultInt64FieldNumber;
const int TestAllTypes::kDefaultUint32FieldNumber;
const int TestAllTypes::kDefaultUint64FieldNumber;
const int TestAllTypes::kDefaultSint32FieldNumber;
const int TestAllTypes::kDefaultSint64FieldNumber;
const int TestAllTypes::kDefaultFixed32FieldNumber;
const int TestAllTypes::kDefaultFixed64FieldNumber;
const int TestAllTypes::kDefaultSfixed32FieldNumber;
const int TestAllTypes::kDefaultSfixed64FieldNumber;
const int TestAllTypes::kDefaultFloatFieldNumber;
const int TestAllTypes::kDefaultDoubleFieldNumber;
const int TestAllTypes::kDefaultBoolFieldNumber;
const int TestAllTypes::kDefaultStringFieldNumber;
const int TestAllTypes::kDefaultBytesFieldNumber;
const int TestAllTypes::kDefaultNestedEnumFieldNumber;
const int TestAllTypes::kDefaultForeignEnumFieldNumber;
const int TestAllTypes::kDefaultImportEnumFieldNumber;
const int TestAllTypes::kDefaultStringPieceFieldNumber;
const int TestAllTypes::kDefaultCordFieldNumber;
const int TestAllTypes::kOneofUint32FieldNumber;
const int TestAllTypes::kOneofNestedMessageFieldNumber;
const int TestAllTypes::kOneofStringFieldNumber;
const int TestAllTypes::kOneofBytesFieldNumber;
const int TestAllTypes::kLazyOneofNestedMessageFieldNumber;
#endif  // !defined(_MSC_VER) || _MSC_VER >= 1900

TestAllTypes::TestAllTypes()
  : ::google::protobuf::Message(), _internal_metadata_(NULL) {
  if (this != internal_default_instance()) protobuf_InitDefaults_google_2fprotobuf_2funittest_5fno_5farena_2eproto();
  SharedCtor();
  // @@protoc_insertion_point(constructor:protobuf_unittest_no_arena.TestAllTypes)
}

void TestAllTypes::InitAsDefaultInstance() {
  optionalgroup_ = const_cast< ::protobuf_unittest_no_arena::TestAllTypes_OptionalGroup*>(
      ::protobuf_unittest_no_arena::TestAllTypes_OptionalGroup::internal_default_instance());
  optional_nested_message_ = const_cast< ::protobuf_unittest_no_arena::TestAllTypes_NestedMessage*>(
      ::protobuf_unittest_no_arena::TestAllTypes_NestedMessage::internal_default_instance());
  optional_foreign_message_ = const_cast< ::protobuf_unittest_no_arena::ForeignMessage*>(
      ::protobuf_unittest_no_arena::ForeignMessage::internal_default_instance());
  optional_import_message_ = const_cast< ::protobuf_unittest_import::ImportMessage*>(
      ::protobuf_unittest_import::ImportMessage::internal_default_instance());
  optional_public_import_message_ = const_cast< ::protobuf_unittest_import::PublicImportMessage*>(
      ::protobuf_unittest_import::PublicImportMessage::internal_default_instance());
  optional_message_ = const_cast< ::protobuf_unittest_no_arena::TestAllTypes_NestedMessage*>(
      ::protobuf_unittest_no_arena::TestAllTypes_NestedMessage::internal_default_instance());
  TestAllTypes_default_oneof_instance_->oneof_uint32_ = 0u;
  TestAllTypes_default_oneof_instance_->oneof_nested_message_ = const_cast< ::protobuf_unittest_no_arena::TestAllTypes_NestedMessage*>(
      ::protobuf_unittest_no_arena::TestAllTypes_NestedMessage::internal_default_instance());
  TestAllTypes_default_oneof_instance_->oneof_string_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  TestAllTypes_default_oneof_instance_->oneof_bytes_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  TestAllTypes_default_oneof_instance_->lazy_oneof_nested_message_ = const_cast< ::protobuf_unittest_no_arena::TestAllTypes_NestedMessage*>(
      ::protobuf_unittest_no_arena::TestAllTypes_NestedMessage::internal_default_instance());
}

TestAllTypes::TestAllTypes(const TestAllTypes& from)
  : ::google::protobuf::Message(),
    _internal_metadata_(NULL) {
  SharedCtor();
  UnsafeMergeFrom(from);
  // @@protoc_insertion_point(copy_constructor:protobuf_unittest_no_arena.TestAllTypes)
}

void TestAllTypes::SharedCtor() {
  _cached_size_ = 0;
  optional_string_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  optional_bytes_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  optional_string_piece_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  optional_cord_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  default_string_.UnsafeSetDefault(_default_default_string_);
  default_bytes_.UnsafeSetDefault(_default_default_bytes_);
  default_string_piece_.UnsafeSetDefault(_default_default_string_piece_);
  default_cord_.UnsafeSetDefault(_default_default_cord_);
  optionalgroup_ = NULL;
  optional_nested_message_ = NULL;
  optional_foreign_message_ = NULL;
  optional_import_message_ = NULL;
  optional_public_import_message_ = NULL;
  optional_message_ = NULL;
  ::memset(&optional_int64_, 0, reinterpret_cast<char*>(&optional_bool_) -
    reinterpret_cast<char*>(&optional_int64_) + sizeof(optional_bool_));
  default_import_enum_ = 8;
  optional_nested_enum_ = 1;
  optional_foreign_enum_ = 4;
  optional_import_enum_ = 7;
  default_int32_ = 41;
  default_int64_ = GOOGLE_LONGLONG(42);
  default_uint64_ = GOOGLE_ULONGLONG(44);
  default_uint32_ = 43u;
  default_sint32_ = -45;
  default_sint64_ = GOOGLE_LONGLONG(46);
  default_fixed64_ = GOOGLE_ULONGLONG(48);
  default_fixed32_ = 47u;
  default_sfixed32_ = 49;
  default_sfixed64_ = GOOGLE_LONGLONG(-50);
  default_double_ = 52000;
  default_float_ = 51.5f;
  default_bool_ = true;
  default_nested_enum_ = 2;
  default_foreign_enum_ = 5;
  clear_has_oneof_field();
}

TestAllTypes::~TestAllTypes() {
  // @@protoc_insertion_point(destructor:protobuf_unittest_no_arena.TestAllTypes)
  SharedDtor();
}

void TestAllTypes::SharedDtor() {
  optional_string_.DestroyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  optional_bytes_.DestroyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  optional_string_piece_.DestroyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  optional_cord_.DestroyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  default_string_.DestroyNoArena(_default_default_string_);
  default_bytes_.DestroyNoArena(_default_default_bytes_);
  default_string_piece_.DestroyNoArena(_default_default_string_piece_);
  default_cord_.DestroyNoArena(_default_default_cord_);
  if (has_oneof_field()) {
    clear_oneof_field();
  }
  if (this != &TestAllTypes_default_instance_.get()) {
    delete optionalgroup_;
    delete optional_nested_message_;
    delete optional_foreign_message_;
    delete optional_import_message_;
    delete optional_public_import_message_;
    delete optional_message_;
  }
}

void TestAllTypes::SetCachedSize(int size) const {
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
}
const ::google::protobuf::Descriptor* TestAllTypes::descriptor() {
  protobuf_AssignDescriptorsOnce();
  return TestAllTypes_descriptor_;
}

const TestAllTypes& TestAllTypes::default_instance() {
  protobuf_InitDefaults_google_2fprotobuf_2funittest_5fno_5farena_2eproto();
  return *internal_default_instance();
}

::google::protobuf::internal::ExplicitlyConstructed<TestAllTypes> TestAllTypes_default_instance_;

TestAllTypes* TestAllTypes::New(::google::protobuf::Arena* arena) const {
  TestAllTypes* n = new TestAllTypes;
  if (arena != NULL) {
    arena->Own(n);
  }
  return n;
}

void TestAllTypes::clear_oneof_field() {
// @@protoc_insertion_point(one_of_clear_start:protobuf_unittest_no_arena.TestAllTypes)
  switch (oneof_field_case()) {
    case kOneofUint32: {
      // No need to clear
      break;
    }
    case kOneofNestedMessage: {
      delete oneof_field_.oneof_nested_message_;
      break;
    }
    case kOneofString: {
      oneof_field_.oneof_string_.DestroyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
      break;
    }
    case kOneofBytes: {
      oneof_field_.oneof_bytes_.DestroyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
      break;
    }
    case kLazyOneofNestedMessage: {
      delete oneof_field_.lazy_oneof_nested_message_;
      break;
    }
    case ONEOF_FIELD_NOT_SET: {
      break;
    }
  }
  _oneof_case_[0] = ONEOF_FIELD_NOT_SET;
}


void TestAllTypes::Clear() {
// @@protoc_insertion_point(message_clear_start:protobuf_unittest_no_arena.TestAllTypes)
#if defined(__clang__)
#define ZR_HELPER_(f) \
  _Pragma("clang diagnostic push") \
  _Pragma("clang diagnostic ignored \"-Winvalid-offsetof\"") \
  __builtin_offsetof(TestAllTypes, f) \
  _Pragma("clang diagnostic pop")
#else
#define ZR_HELPER_(f) reinterpret_cast<char*>(\
  &reinterpret_cast<TestAllTypes*>(16)->f)
#endif

#define ZR_(first, last) do {\
  ::memset(&(first), 0,\
           ZR_HELPER_(last) - ZR_HELPER_(first) + sizeof(last));\
} while (0)

  ZR_(optional_int64_, optional_fixed64_);
  if (_has_bits_[8 / 32] & 65280u) {
    ZR_(optional_sfixed64_, optional_bool_);
    if (has_optional_string()) {
      optional_string_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
    }
    if (has_optional_bytes()) {
      optional_bytes_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
    }
    if (has_optionalgroup()) {
      if (optionalgroup_ != NULL) optionalgroup_->::protobuf_unittest_no_arena::TestAllTypes_OptionalGroup::Clear();
    }
  }
  if (_has_bits_[16 / 32] & 16711680u) {
    if (has_optional_nested_message()) {
      if (optional_nested_message_ != NULL) optional_nested_message_->::protobuf_unittest_no_arena::TestAllTypes_NestedMessage::Clear();
    }
    if (has_optional_foreign_message()) {
      if (optional_foreign_message_ != NULL) optional_foreign_message_->::protobuf_unittest_no_arena::ForeignMessage::Clear();
    }
    if (has_optional_import_message()) {
      if (optional_import_message_ != NULL) optional_import_message_->::protobuf_unittest_import::ImportMessage::Clear();
    }
    optional_nested_enum_ = 1;
    optional_foreign_enum_ = 4;
    optional_import_enum_ = 7;
    if (has_optional_string_piece()) {
      optional_string_piece_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
    }
    if (has_optional_cord()) {
      optional_cord_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
    }
  }
  if (_has_bits_[24 / 32] & 50331648u) {
    if (has_optional_public_import_message()) {
      if (optional_public_import_message_ != NULL) optional_public_import_message_->::protobuf_unittest_import::PublicImportMessage::Clear();
    }
    if (has_optional_message()) {
      if (optional_message_ != NULL) optional_message_->::protobuf_unittest_no_arena::TestAllTypes_NestedMessage::Clear();
    }
  }
  if (_has_bits_[48 / 32] & 16252928u) {
    default_int32_ = 41;
    default_int64_ = GOOGLE_LONGLONG(42);
    default_uint32_ = 43u;
    default_uint64_ = GOOGLE_ULONGLONG(44);
    default_sint32_ = -45;
  }
  if (_has_bits_[56 / 32] & 4278190080u) {
    default_sint64_ = GOOGLE_LONGLONG(46);
    default_fixed32_ = 47u;
    default_fixed64_ = GOOGLE_ULONGLONG(48);
    default_sfixed32_ = 49;
    default_sfixed64_ = GOOGLE_LONGLONG(-50);
    default_float_ = 51.5f;
    default_double_ = 52000;
    default_bool_ = true;
  }
  if (_has_bits_[64 / 32] & 127u) {
    if (has_default_string()) {
      default_string_.ClearToDefaultNoArena(_default_default_string_);
    }
    if (has_default_bytes()) {
      default_bytes_.ClearToDefaultNoArena(_default_default_bytes_);
    }
    default_nested_enum_ = 2;
    default_foreign_enum_ = 5;
    default_import_enum_ = 8;
    if (has_default_string_piece()) {
      default_string_piece_.ClearToDefaultNoArena(_default_default_string_piece_);
    }
    if (has_default_cord()) {
      default_cord_.ClearToDefaultNoArena(_default_default_cord_);
    }
  }

#undef ZR_HELPER_
#undef ZR_

  repeated_int32_.Clear();
  repeated_int64_.Clear();
  repeated_uint32_.Clear();
  repeated_uint64_.Clear();
  repeated_sint32_.Clear();
  repeated_sint64_.Clear();
  repeated_fixed32_.Clear();
  repeated_fixed64_.Clear();
  repeated_sfixed32_.Clear();
  repeated_sfixed64_.Clear();
  repeated_float_.Clear();
  repeated_double_.Clear();
  repeated_bool_.Clear();
  repeated_string_.Clear();
  repeated_bytes_.Clear();
  repeatedgroup_.Clear();
  repeated_nested_message_.Clear();
  repeated_foreign_message_.Clear();
  repeated_import_message_.Clear();
  repeated_nested_enum_.Clear();
  repeated_foreign_enum_.Clear();
  repeated_import_enum_.Clear();
  repeated_string_piece_.Clear();
  repeated_cord_.Clear();
  repeated_lazy_message_.Clear();
  clear_oneof_field();
  _has_bits_.Clear();
  if (_internal_metadata_.have_unknown_fields()) {
    mutable_unknown_fields()->Clear();
  }
}

bool TestAllTypes::MergePartialFromCodedStream(
    ::google::protobuf::io::CodedInputStream* input) {
#define DO_(EXPRESSION) if (!GOOGLE_PREDICT_TRUE(EXPRESSION)) goto failure
  ::google::protobuf::uint32 tag;
  // @@protoc_insertion_point(parse_start:protobuf_unittest_no_arena.TestAllTypes)
  for (;;) {
    ::std::pair< ::google::protobuf::uint32, bool> p = input->ReadTagWithCutoff(16383);
    tag = p.first;
    if (!p.second) goto handle_unusual;
    switch (::google::protobuf::internal::WireFormatLite::GetTagFieldNumber(tag)) {
      // optional int32 optional_int32 = 1;
      case 1: {
        if (tag == 8) {
          set_has_optional_int32();
          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int32, ::google::protobuf::internal::WireFormatLite::TYPE_INT32>(
                 input, &optional_int32_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(16)) goto parse_optional_int64;
        break;
      }

      // optional int64 optional_int64 = 2;
      case 2: {
        if (tag == 16) {
         parse_optional_int64:
          set_has_optional_int64();
          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_INT64>(
                 input, &optional_int64_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(24)) goto parse_optional_uint32;
        break;
      }

      // optional uint32 optional_uint32 = 3;
      case 3: {
        if (tag == 24) {
         parse_optional_uint32:
          set_has_optional_uint32();
          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::uint32, ::google::protobuf::internal::WireFormatLite::TYPE_UINT32>(
                 input, &optional_uint32_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(32)) goto parse_optional_uint64;
        break;
      }

      // optional uint64 optional_uint64 = 4;
      case 4: {
        if (tag == 32) {
         parse_optional_uint64:
          set_has_optional_uint64();
          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::uint64, ::google::protobuf::internal::WireFormatLite::TYPE_UINT64>(
                 input, &optional_uint64_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(40)) goto parse_optional_sint32;
        break;
      }

      // optional sint32 optional_sint32 = 5;
      case 5: {
        if (tag == 40) {
         parse_optional_sint32:
          set_has_optional_sint32();
          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int32, ::google::protobuf::internal::WireFormatLite::TYPE_SINT32>(
                 input, &optional_sint32_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(48)) goto parse_optional_sint64;
        break;
      }

      // optional sint64 optional_sint64 = 6;
      case 6: {
        if (tag == 48) {
         parse_optional_sint64:
          set_has_optional_sint64();
          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_SINT64>(
                 input, &optional_sint64_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(61)) goto parse_optional_fixed32;
        break;
      }

      // optional fixed32 optional_fixed32 = 7;
      case 7: {
        if (tag == 61) {
         parse_optional_fixed32:
          set_has_optional_fixed32();
          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::uint32, ::google::protobuf::internal::WireFormatLite::TYPE_FIXED32>(
                 input, &optional_fixed32_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(65)) goto parse_optional_fixed64;
        break;
      }

      // optional fixed64 optional_fixed64 = 8;
      case 8: {
        if (tag == 65) {
         parse_optional_fixed64:
          set_has_optional_fixed64();
          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::uint64, ::google::protobuf::internal::WireFormatLite::TYPE_FIXED64>(
                 input, &optional_fixed64_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(77)) goto parse_optional_sfixed32;
        break;
      }

      // optional sfixed32 optional_sfixed32 = 9;
      case 9: {
        if (tag == 77) {
         parse_optional_sfixed32:
          set_has_optional_sfixed32();
          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int32, ::google::protobuf::internal::WireFormatLite::TYPE_SFIXED32>(
                 input, &optional_sfixed32_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(81)) goto parse_optional_sfixed64;
        break;
      }

      // optional sfixed64 optional_sfixed64 = 10;
      case 10: {
        if (tag == 81) {
         parse_optional_sfixed64:
          set_has_optional_sfixed64();
          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_SFIXED64>(
                 input, &optional_sfixed64_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(93)) goto parse_optional_float;
        break;
      }

      // optional float optional_float = 11;
      case 11: {
        if (tag == 93) {
         parse_optional_float:
          set_has_optional_float();
          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   float, ::google::protobuf::internal::WireFormatLite::TYPE_FLOAT>(
                 input, &optional_float_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(97)) goto parse_optional_double;
        break;
      }

      // optional double optional_double = 12;
      case 12: {
        if (tag == 97) {
         parse_optional_double:
          set_has_optional_double();
          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   double, ::google::protobuf::internal::WireFormatLite::TYPE_DOUBLE>(
                 input, &optional_double_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(104)) goto parse_optional_bool;
        break;
      }

      // optional bool optional_bool = 13;
      case 13: {
        if (tag == 104) {
         parse_optional_bool:
          set_has_optional_bool();
          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   bool, ::google::protobuf::internal::WireFormatLite::TYPE_BOOL>(
                 input, &optional_bool_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(114)) goto parse_optional_string;
        break;
      }

      // optional string optional_string = 14;
      case 14: {
        if (tag == 114) {
         parse_optional_string:
          DO_(::google::protobuf::internal::WireFormatLite::ReadString(
                input, this->mutable_optional_string()));
          ::google::protobuf::internal::WireFormat::VerifyUTF8StringNamedField(
            this->optional_string().data(), this->optional_string().length(),
            ::google::protobuf::internal::WireFormat::PARSE,
            "protobuf_unittest_no_arena.TestAllTypes.optional_string");
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(122)) goto parse_optional_bytes;
        break;
      }

      // optional bytes optional_bytes = 15;
      case 15: {
        if (tag == 122) {
         parse_optional_bytes:
          DO_(::google::protobuf::internal::WireFormatLite::ReadBytes(
                input, this->mutable_optional_bytes()));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(131)) goto parse_optionalgroup;
        break;
      }

      // optional group OptionalGroup = 16 { ... };
      case 16: {
        if (tag == 131) {
         parse_optionalgroup:
          DO_(::google::protobuf::internal::WireFormatLite::ReadGroupNoVirtual(
                16, input, mutable_optionalgroup()));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(146)) goto parse_optional_nested_message;
        break;
      }

      // optional .protobuf_unittest_no_arena.TestAllTypes.NestedMessage optional_nested_message = 18;
      case 18: {
        if (tag == 146) {
         parse_optional_nested_message:
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessageNoVirtual(
               input, mutable_optional_nested_message()));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(154)) goto parse_optional_foreign_message;
        break;
      }

      // optional .protobuf_unittest_no_arena.ForeignMessage optional_foreign_message = 19;
      case 19: {
        if (tag == 154) {
         parse_optional_foreign_message:
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessageNoVirtual(
               input, mutable_optional_foreign_message()));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(162)) goto parse_optional_import_message;
        break;
      }

      // optional .protobuf_unittest_import.ImportMessage optional_import_message = 20;
      case 20: {
        if (tag == 162) {
         parse_optional_import_message:
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessageNoVirtual(
               input, mutable_optional_import_message()));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(168)) goto parse_optional_nested_enum;
        break;
      }

      // optional .protobuf_unittest_no_arena.TestAllTypes.NestedEnum optional_nested_enum = 21;
      case 21: {
        if (tag == 168) {
         parse_optional_nested_enum:
          int value;
          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   int, ::google::protobuf::internal::WireFormatLite::TYPE_ENUM>(
                 input, &value)));
          if (::protobuf_unittest_no_arena::TestAllTypes_NestedEnum_IsValid(value)) {
            set_optional_nested_enum(static_cast< ::protobuf_unittest_no_arena::TestAllTypes_NestedEnum >(value));
          } else {
            mutable_unknown_fields()->AddVarint(21, value);
          }
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(176)) goto parse_optional_foreign_enum;
        break;
      }

      // optional .protobuf_unittest_no_arena.ForeignEnum optional_foreign_enum = 22;
      case 22: {
        if (tag == 176) {
         parse_optional_foreign_enum:
          int value;
          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   int, ::google::protobuf::internal::WireFormatLite::TYPE_ENUM>(
                 input, &value)));
          if (::protobuf_unittest_no_arena::ForeignEnum_IsValid(value)) {
            set_optional_foreign_enum(static_cast< ::protobuf_unittest_no_arena::ForeignEnum >(value));
          } else {
            mutable_unknown_fields()->AddVarint(22, value);
          }
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(184)) goto parse_optional_import_enum;
        break;
      }

      // optional .protobuf_unittest_import.ImportEnum optional_import_enum = 23;
      case 23: {
        if (tag == 184) {
         parse_optional_import_enum:
          int value;
          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   int, ::google::protobuf::internal::WireFormatLite::TYPE_ENUM>(
                 input, &value)));
          if (::protobuf_unittest_import::ImportEnum_IsValid(value)) {
            set_optional_import_enum(static_cast< ::protobuf_unittest_import::ImportEnum >(value));
          } else {
            mutable_unknown_fields()->AddVarint(23, value);
          }
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(194)) goto parse_optional_string_piece;
        break;
      }

      // optional string optional_string_piece = 24 [ctype = STRING_PIECE];
      case 24: {
        if (tag == 194) {
         parse_optional_string_piece:
          DO_(::google::protobuf::internal::WireFormatLite::ReadString(
                input, this->mutable_optional_string_piece()));
          ::google::protobuf::internal::WireFormat::VerifyUTF8StringNamedField(
            this->optional_string_piece().data(), this->optional_string_piece().length(),
            ::google::protobuf::internal::WireFormat::PARSE,
            "protobuf_unittest_no_arena.TestAllTypes.optional_string_piece");
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(202)) goto parse_optional_cord;
        break;
      }

      // optional string optional_cord = 25 [ctype = CORD];
      case 25: {
        if (tag == 202) {
         parse_optional_cord:
          DO_(::google::protobuf::internal::WireFormatLite::ReadString(
                input, this->mutable_optional_cord()));
          ::google::protobuf::internal::WireFormat::VerifyUTF8StringNamedField(
            this->optional_cord().data(), this->optional_cord().length(),
            ::google::protobuf::internal::WireFormat::PARSE,
            "protobuf_unittest_no_arena.TestAllTypes.optional_cord");
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(210)) goto parse_optional_public_import_message;
        break;
      }

      // optional .protobuf_unittest_import.PublicImportMessage optional_public_import_message = 26;
      case 26: {
        if (tag == 210) {
         parse_optional_public_import_message:
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessageNoVirtual(
               input, mutable_optional_public_import_message()));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(218)) goto parse_optional_message;
        break;
      }

      // optional .protobuf_unittest_no_arena.TestAllTypes.NestedMessage optional_message = 27 [lazy = true];
      case 27: {
        if (tag == 218) {
         parse_optional_message:
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessageNoVirtual(
               input, mutable_optional_message()));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(248)) goto parse_repeated_int32;
        break;
      }

      // repeated int32 repeated_int32 = 31;
      case 31: {
        if (tag == 248) {
         parse_repeated_int32:
          DO_((::google::protobuf::internal::WireFormatLite::ReadRepeatedPrimitive<
                   ::google::protobuf::int32, ::google::protobuf::internal::WireFormatLite::TYPE_INT32>(
                 2, 248, input, this->mutable_repeated_int32())));
        } else if (tag == 250) {
          DO_((::google::protobuf::internal::WireFormatLite::ReadPackedPrimitiveNoInline<
                   ::google::protobuf::int32, ::google::protobuf::internal::WireFormatLite::TYPE_INT32>(
                 input, this->mutable_repeated_int32())));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(248)) goto parse_repeated_int32;
        if (input->ExpectTag(256)) goto parse_repeated_int64;
        break;
      }

      // repeated int64 repeated_int64 = 32;
      case 32: {
        if (tag == 256) {
         parse_repeated_int64:
          DO_((::google::protobuf::internal::WireFormatLite::ReadRepeatedPrimitive<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_INT64>(
                 2, 256, input, this->mutable_repeated_int64())));
        } else if (tag == 258) {
          DO_((::google::protobuf::internal::WireFormatLite::ReadPackedPrimitiveNoInline<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_INT64>(
                 input, this->mutable_repeated_int64())));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(256)) goto parse_repeated_int64;
        if (input->ExpectTag(264)) goto parse_repeated_uint32;
        break;
      }

      // repeated uint32 repeated_uint32 = 33;
      case 33: {
        if (tag == 264) {
         parse_repeated_uint32:
          DO_((::google::protobuf::internal::WireFormatLite::ReadRepeatedPrimitive<
                   ::google::protobuf::uint32, ::google::protobuf::internal::WireFormatLite::TYPE_UINT32>(
                 2, 264, input, this->mutable_repeated_uint32())));
        } else if (tag == 266) {
          DO_((::google::protobuf::internal::WireFormatLite::ReadPackedPrimitiveNoInline<
                   ::google::protobuf::uint32, ::google::protobuf::internal::WireFormatLite::TYPE_UINT32>(
                 input, this->mutable_repeated_uint32())));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(264)) goto parse_repeated_uint32;
        if (input->ExpectTag(272)) goto parse_repeated_uint64;
        break;
      }

      // repeated uint64 repeated_uint64 = 34;
      case 34: {
        if (tag == 272) {
         parse_repeated_uint64:
          DO_((::google::protobuf::internal::WireFormatLite::ReadRepeatedPrimitive<
                   ::google::protobuf::uint64, ::google::protobuf::internal::WireFormatLite::TYPE_UINT64>(
                 2, 272, input, this->mutable_repeated_uint64())));
        } else if (tag == 274) {
          DO_((::google::protobuf::internal::WireFormatLite::ReadPackedPrimitiveNoInline<
                   ::google::protobuf::uint64, ::google::protobuf::internal::WireFormatLite::TYPE_UINT64>(
                 input, this->mutable_repeated_uint64())));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(272)) goto parse_repeated_uint64;
        if (input->ExpectTag(280)) goto parse_repeated_sint32;
        break;
      }

      // repeated sint32 repeated_sint32 = 35;
      case 35: {
        if (tag == 280) {
         parse_repeated_sint32:
          DO_((::google::protobuf::internal::WireFormatLite::ReadRepeatedPrimitive<
                   ::google::protobuf::int32, ::google::protobuf::internal::WireFormatLite::TYPE_SINT32>(
                 2, 280, input, this->mutable_repeated_sint32())));
        } else if (tag == 282) {
          DO_((::google::protobuf::internal::WireFormatLite::ReadPackedPrimitiveNoInline<
                   ::google::protobuf::int32, ::google::protobuf::internal::WireFormatLite::TYPE_SINT32>(
                 input, this->mutable_repeated_sint32())));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(280)) goto parse_repeated_sint32;
        if (input->ExpectTag(288)) goto parse_repeated_sint64;
        break;
      }

      // repeated sint64 repeated_sint64 = 36;
      case 36: {
        if (tag == 288) {
         parse_repeated_sint64:
          DO_((::google::protobuf::internal::WireFormatLite::ReadRepeatedPrimitive<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_SINT64>(
                 2, 288, input, this->mutable_repeated_sint64())));
        } else if (tag == 290) {
          DO_((::google::protobuf::internal::WireFormatLite::ReadPackedPrimitiveNoInline<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_SINT64>(
                 input, this->mutable_repeated_sint64())));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(288)) goto parse_repeated_sint64;
        if (input->ExpectTag(301)) goto parse_repeated_fixed32;
        break;
      }

      // repeated fixed32 repeated_fixed32 = 37;
      case 37: {
        if (tag == 301) {
         parse_repeated_fixed32:
          DO_((::google::protobuf::internal::WireFormatLite::ReadRepeatedPrimitive<
                   ::google::protobuf::uint32, ::google::protobuf::internal::WireFormatLite::TYPE_FIXED32>(
                 2, 301, input, this->mutable_repeated_fixed32())));
        } else if (tag == 298) {
          DO_((::google::protobuf::internal::WireFormatLite::ReadPackedPrimitiveNoInline<
                   ::google::protobuf::uint32, ::google::protobuf::internal::WireFormatLite::TYPE_FIXED32>(
                 input, this->mutable_repeated_fixed32())));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(301)) goto parse_repeated_fixed32;
        if (input->ExpectTag(305)) goto parse_repeated_fixed64;
        break;
      }

      // repeated fixed64 repeated_fixed64 = 38;
      case 38: {
        if (tag == 305) {
         parse_repeated_fixed64:
          DO_((::google::protobuf::internal::WireFormatLite::ReadRepeatedPrimitive<
                   ::google::protobuf::uint64, ::google::protobuf::internal::WireFormatLite::TYPE_FIXED64>(
                 2, 305, input, this->mutable_repeated_fixed64())));
        } else if (tag == 306) {
          DO_((::google::protobuf::internal::WireFormatLite::ReadPackedPrimitiveNoInline<
                   ::google::protobuf::uint64, ::google::protobuf::internal::WireFormatLite::TYPE_FIXED64>(
                 input, this->mutable_repeated_fixed64())));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(305)) goto parse_repeated_fixed64;
        if (input->ExpectTag(317)) goto parse_repeated_sfixed32;
        break;
      }

      // repeated sfixed32 repeated_sfixed32 = 39;
      case 39: {
        if (tag == 317) {
         parse_repeated_sfixed32:
          DO_((::google::protobuf::internal::WireFormatLite::ReadRepeatedPrimitive<
                   ::google::protobuf::int32, ::google::protobuf::internal::WireFormatLite::TYPE_SFIXED32>(
                 2, 317, input, this->mutable_repeated_sfixed32())));
        } else if (tag == 314) {
          DO_((::google::protobuf::internal::WireFormatLite::ReadPackedPrimitiveNoInline<
                   ::google::protobuf::int32, ::google::protobuf::internal::WireFormatLite::TYPE_SFIXED32>(
                 input, this->mutable_repeated_sfixed32())));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(317)) goto parse_repeated_sfixed32;
        if (input->ExpectTag(321)) goto parse_repeated_sfixed64;
        break;
      }

      // repeated sfixed64 repeated_sfixed64 = 40;
      case 40: {
        if (tag == 321) {
         parse_repeated_sfixed64:
          DO_((::google::protobuf::internal::WireFormatLite::ReadRepeatedPrimitive<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_SFIXED64>(
                 2, 321, input, this->mutable_repeated_sfixed64())));
        } else if (tag == 322) {
          DO_((::google::protobuf::internal::WireFormatLite::ReadPackedPrimitiveNoInline<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_SFIXED64>(
                 input, this->mutable_repeated_sfixed64())));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(321)) goto parse_repeated_sfixed64;
        if (input->ExpectTag(333)) goto parse_repeated_float;
        break;
      }

      // repeated float repeated_float = 41;
      case 41: {
        if (tag == 333) {
         parse_repeated_float:
          DO_((::google::protobuf::internal::WireFormatLite::ReadRepeatedPrimitive<
                   float, ::google::protobuf::internal::WireFormatLite::TYPE_FLOAT>(
                 2, 333, input, this->mutable_repeated_float())));
        } else if (tag == 330) {
          DO_((::google::protobuf::internal::WireFormatLite::ReadPackedPrimitiveNoInline<
                   float, ::google::protobuf::internal::WireFormatLite::TYPE_FLOAT>(
                 input, this->mutable_repeated_float())));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(333)) goto parse_repeated_float;
        if (input->ExpectTag(337)) goto parse_repeated_double;
        break;
      }

      // repeated double repeated_double = 42;
      case 42: {
        if (tag == 337) {
         parse_repeated_double:
          DO_((::google::protobuf::internal::WireFormatLite::ReadRepeatedPrimitive<
                   double, ::google::protobuf::internal::WireFormatLite::TYPE_DOUBLE>(
                 2, 337, input, this->mutable_repeated_double())));
        } else if (tag == 338) {
          DO_((::google::protobuf::internal::WireFormatLite::ReadPackedPrimitiveNoInline<
                   double, ::google::protobuf::internal::WireFormatLite::TYPE_DOUBLE>(
                 input, this->mutable_repeated_double())));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(337)) goto parse_repeated_double;
        if (input->ExpectTag(344)) goto parse_repeated_bool;
        break;
      }

      // repeated bool repeated_bool = 43;
      case 43: {
        if (tag == 344) {
         parse_repeated_bool:
          DO_((::google::protobuf::internal::WireFormatLite::ReadRepeatedPrimitive<
                   bool, ::google::protobuf::internal::WireFormatLite::TYPE_BOOL>(
                 2, 344, input, this->mutable_repeated_bool())));
        } else if (tag == 346) {
          DO_((::google::protobuf::internal::WireFormatLite::ReadPackedPrimitiveNoInline<
                   bool, ::google::protobuf::internal::WireFormatLite::TYPE_BOOL>(
                 input, this->mutable_repeated_bool())));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(344)) goto parse_repeated_bool;
        if (input->ExpectTag(354)) goto parse_repeated_string;
        break;
      }

      // repeated string repeated_string = 44;
      case 44: {
        if (tag == 354) {
         parse_repeated_string:
          DO_(::google::protobuf::internal::WireFormatLite::ReadString(
                input, this->add_repeated_string()));
          ::google::protobuf::internal::WireFormat::VerifyUTF8StringNamedField(
            this->repeated_string(this->repeated_string_size() - 1).data(),
            this->repeated_string(this->repeated_string_size() - 1).length(),
            ::google::protobuf::internal::WireFormat::PARSE,
            "protobuf_unittest_no_arena.TestAllTypes.repeated_string");
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(354)) goto parse_repeated_string;
        if (input->ExpectTag(362)) goto parse_repeated_bytes;
        break;
      }

      // repeated bytes repeated_bytes = 45;
      case 45: {
        if (tag == 362) {
         parse_repeated_bytes:
          DO_(::google::protobuf::internal::WireFormatLite::ReadBytes(
                input, this->add_repeated_bytes()));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(362)) goto parse_repeated_bytes;
        if (input->ExpectTag(371)) goto parse_repeatedgroup;
        break;
      }

      // repeated group RepeatedGroup = 46 { ... };
      case 46: {
        if (tag == 371) {
         parse_repeatedgroup:
          DO_(input->IncrementRecursionDepth());
         parse_loop_repeatedgroup:
          DO_(::google::protobuf::internal::WireFormatLite::ReadGroupNoVirtualNoRecursionDepth(
                46, input, add_repeatedgroup()));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(371)) goto parse_loop_repeatedgroup;
        if (input->ExpectTag(386)) goto parse_loop_repeated_nested_message;
        input->UnsafeDecrementRecursionDepth();
        break;
      }

      // repeated .protobuf_unittest_no_arena.TestAllTypes.NestedMessage repeated_nested_message = 48;
      case 48: {
        if (tag == 386) {
          DO_(input->IncrementRecursionDepth());
         parse_loop_repeated_nested_message:
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessageNoVirtualNoRecursionDepth(
                input, add_repeated_nested_message()));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(386)) goto parse_loop_repeated_nested_message;
        if (input->ExpectTag(394)) goto parse_loop_repeated_foreign_message;
        input->UnsafeDecrementRecursionDepth();
        break;
      }

      // repeated .protobuf_unittest_no_arena.ForeignMessage repeated_foreign_message = 49;
      case 49: {
        if (tag == 394) {
          DO_(input->IncrementRecursionDepth());
         parse_loop_repeated_foreign_message:
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessageNoVirtualNoRecursionDepth(
                input, add_repeated_foreign_message()));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(394)) goto parse_loop_repeated_foreign_message;
        if (input->ExpectTag(402)) goto parse_loop_repeated_import_message;
        input->UnsafeDecrementRecursionDepth();
        break;
      }

      // repeated .protobuf_unittest_import.ImportMessage repeated_import_message = 50;
      case 50: {
        if (tag == 402) {
          DO_(input->IncrementRecursionDepth());
         parse_loop_repeated_import_message:
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessageNoVirtualNoRecursionDepth(
                input, add_repeated_import_message()));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(402)) goto parse_loop_repeated_import_message;
        input->UnsafeDecrementRecursionDepth();
        if (input->ExpectTag(408)) goto parse_repeated_nested_enum;
        break;
      }

      // repeated .protobuf_unittest_no_arena.TestAllTypes.NestedEnum repeated_nested_enum = 51;
      case 51: {
        if (tag == 408) {
         parse_repeated_nested_enum:
          int value;
          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   int, ::google::protobuf::internal::WireFormatLite::TYPE_ENUM>(
                 input, &value)));
          if (::protobuf_unittest_no_arena::TestAllTypes_NestedEnum_IsValid(value)) {
            add_repeated_nested_enum(static_cast< ::protobuf_unittest_no_arena::TestAllTypes_NestedEnum >(value));
          } else {
            mutable_unknown_fields()->AddVarint(51, value);
          }
        } else if (tag == 410) {
          DO_((::google::protobuf::internal::WireFormat::ReadPackedEnumPreserveUnknowns(
                 input,
                 51,
                 ::protobuf_unittest_no_arena::TestAllTypes_NestedEnum_IsValid,
                 mutable_unknown_fields(),
                 this->mutable_repeated_nested_enum())));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(408)) goto parse_repeated_nested_enum;
        if (input->ExpectTag(416)) goto parse_repeated_foreign_enum;
        break;
      }

      // repeated .protobuf_unittest_no_arena.ForeignEnum repeated_foreign_enum = 52;
      case 52: {
        if (tag == 416) {
         parse_repeated_foreign_enum:
          int value;
          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   int, ::google::protobuf::internal::WireFormatLite::TYPE_ENUM>(
                 input, &value)));
          if (::protobuf_unittest_no_arena::ForeignEnum_IsValid(value)) {
            add_repeated_foreign_enum(static_cast< ::protobuf_unittest_no_arena::ForeignEnum >(value));
          } else {
            mutable_unknown_fields()->AddVarint(52, value);
          }
        } else if (tag == 418) {
          DO_((::google::protobuf::internal::WireFormat::ReadPackedEnumPreserveUnknowns(
                 input,
                 52,
                 ::protobuf_unittest_no_arena::ForeignEnum_IsValid,
                 mutable_unknown_fields(),
                 this->mutable_repeated_foreign_enum())));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(416)) goto parse_repeated_foreign_enum;
        if (input->ExpectTag(424)) goto parse_repeated_import_enum;
        break;
      }

      // repeated .protobuf_unittest_import.ImportEnum repeated_import_enum = 53;
      case 53: {
        if (tag == 424) {
         parse_repeated_import_enum:
          int value;
          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   int, ::google::protobuf::internal::WireFormatLite::TYPE_ENUM>(
                 input, &value)));
          if (::protobuf_unittest_import::ImportEnum_IsValid(value)) {
            add_repeated_import_enum(static_cast< ::protobuf_unittest_import::ImportEnum >(value));
          } else {
            mutable_unknown_fields()->AddVarint(53, value);
          }
        } else if (tag == 426) {
          DO_((::google::protobuf::internal::WireFormat::ReadPackedEnumPreserveUnknowns(
                 input,
                 53,
                 ::protobuf_unittest_import::ImportEnum_IsValid,
                 mutable_unknown_fields(),
                 this->mutable_repeated_import_enum())));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(424)) goto parse_repeated_import_enum;
        if (input->ExpectTag(434)) goto parse_repeated_string_piece;
        break;
      }

      // repeated string repeated_string_piece = 54 [ctype = STRING_PIECE];
      case 54: {
        if (tag == 434) {
         parse_repeated_string_piece:
          DO_(::google::protobuf::internal::WireFormatLite::ReadString(
                input, this->add_repeated_string_piece()));
          ::google::protobuf::internal::WireFormat::VerifyUTF8StringNamedField(
            this->repeated_string_piece(this->repeated_string_piece_size() - 1).data(),
            this->repeated_string_piece(this->repeated_string_piece_size() - 1).length(),
            ::google::protobuf::internal::WireFormat::PARSE,
            "protobuf_unittest_no_arena.TestAllTypes.repeated_string_piece");
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(434)) goto parse_repeated_string_piece;
        if (input->ExpectTag(442)) goto parse_repeated_cord;
        break;
      }

      // repeated string repeated_cord = 55 [ctype = CORD];
      case 55: {
        if (tag == 442) {
         parse_repeated_cord:
          DO_(::google::protobuf::internal::WireFormatLite::ReadString(
                input, this->add_repeated_cord()));
          ::google::protobuf::internal::WireFormat::VerifyUTF8StringNamedField(
            this->repeated_cord(this->repeated_cord_size() - 1).data(),
            this->repeated_cord(this->repeated_cord_size() - 1).length(),
            ::google::protobuf::internal::WireFormat::PARSE,
            "protobuf_unittest_no_arena.TestAllTypes.repeated_cord");
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(442)) goto parse_repeated_cord;
        if (input->ExpectTag(458)) goto parse_repeated_lazy_message;
        break;
      }

      // repeated .protobuf_unittest_no_arena.TestAllTypes.NestedMessage repeated_lazy_message = 57 [lazy = true];
      case 57: {
        if (tag == 458) {
         parse_repeated_lazy_message:
          DO_(input->IncrementRecursionDepth());
         parse_loop_repeated_lazy_message:
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessageNoVirtualNoRecursionDepth(
                input, add_repeated_lazy_message()));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(458)) goto parse_loop_repeated_lazy_message;
        input->UnsafeDecrementRecursionDepth();
        if (input->ExpectTag(488)) goto parse_default_int32;
        break;
      }

      // optional int32 default_int32 = 61 [default = 41];
      case 61: {
        if (tag == 488) {
         parse_default_int32:
          set_has_default_int32();
          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int32, ::google::protobuf::internal::WireFormatLite::TYPE_INT32>(
                 input, &default_int32_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(496)) goto parse_default_int64;
        break;
      }

      // optional int64 default_int64 = 62 [default = 42];
      case 62: {
        if (tag == 496) {
         parse_default_int64:
          set_has_default_int64();
          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_INT64>(
                 input, &default_int64_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(504)) goto parse_default_uint32;
        break;
      }

      // optional uint32 default_uint32 = 63 [default = 43];
      case 63: {
        if (tag == 504) {
         parse_default_uint32:
          set_has_default_uint32();
          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::uint32, ::google::protobuf::internal::WireFormatLite::TYPE_UINT32>(
                 input, &default_uint32_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(512)) goto parse_default_uint64;
        break;
      }

      // optional uint64 default_uint64 = 64 [default = 44];
      case 64: {
        if (tag == 512) {
         parse_default_uint64:
          set_has_default_uint64();
          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::uint64, ::google::protobuf::internal::WireFormatLite::TYPE_UINT64>(
                 input, &default_uint64_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(520)) goto parse_default_sint32;
        break;
      }

      // optional sint32 default_sint32 = 65 [default = -45];
      case 65: {
        if (tag == 520) {
         parse_default_sint32:
          set_has_default_sint32();
          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int32, ::google::protobuf::internal::WireFormatLite::TYPE_SINT32>(
                 input, &default_sint32_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(528)) goto parse_default_sint64;
        break;
      }

      // optional sint64 default_sint64 = 66 [default = 46];
      case 66: {
        if (tag == 528) {
         parse_default_sint64:
          set_has_default_sint64();
          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_SINT64>(
                 input, &default_sint64_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(541)) goto parse_default_fixed32;
        break;
      }

      // optional fixed32 default_fixed32 = 67 [default = 47];
      case 67: {
        if (tag == 541) {
         parse_default_fixed32:
          set_has_default_fixed32();
          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::uint32, ::google::protobuf::internal::WireFormatLite::TYPE_FIXED32>(
                 input, &default_fixed32_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(545)) goto parse_default_fixed64;
        break;
      }

      // optional fixed64 default_fixed64 = 68 [default = 48];
      case 68: {
        if (tag == 545) {
         parse_default_fixed64:
          set_has_default_fixed64();
          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::uint64, ::google::protobuf::internal::WireFormatLite::TYPE_FIXED64>(
                 input, &default_fixed64_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(557)) goto parse_default_sfixed32;
        break;
      }

      // optional sfixed32 default_sfixed32 = 69 [default = 49];
      case 69: {
        if (tag == 557) {
         parse_default_sfixed32:
          set_has_default_sfixed32();
          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int32, ::google::protobuf::internal::WireFormatLite::TYPE_SFIXED32>(
                 input, &default_sfixed32_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(561)) goto parse_default_sfixed64;
        break;
      }

      // optional sfixed64 default_sfixed64 = 70 [default = -50];
      case 70: {
        if (tag == 561) {
         parse_default_sfixed64:
          set_has_default_sfixed64();
          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_SFIXED64>(
                 input, &default_sfixed64_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(573)) goto parse_default_float;
        break;
      }

      // optional float default_float = 71 [default = 51.5];
      case 71: {
        if (tag == 573) {
         parse_default_float:
          set_has_default_float();
          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   float, ::google::protobuf::internal::WireFormatLite::TYPE_FLOAT>(
                 input, &default_float_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(577)) goto parse_default_double;
        break;
      }

      // optional double default_double = 72 [default = 52000];
      case 72: {
        if (tag == 577) {
         parse_default_double:
          set_has_default_double();
          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   double, ::google::protobuf::internal::WireFormatLite::TYPE_DOUBLE>(
                 input, &default_double_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(584)) goto parse_default_bool;
        break;
      }

      // optional bool default_bool = 73 [default = true];
      case 73: {
        if (tag == 584) {
         parse_default_bool:
          set_has_default_bool();
          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   bool, ::google::protobuf::internal::WireFormatLite::TYPE_BOOL>(
                 input, &default_bool_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(594)) goto parse_default_string;
        break;
      }

      // optional string default_string = 74 [default = "hello"];
      case 74: {
        if (tag == 594) {
         parse_default_string:
          DO_(::google::protobuf::internal::WireFormatLite::ReadString(
                input, this->mutable_default_string()));
          ::google::protobuf::internal::WireFormat::VerifyUTF8StringNamedField(
            this->default_string().data(), this->default_string().length(),
            ::google::protobuf::internal::WireFormat::PARSE,
            "protobuf_unittest_no_arena.TestAllTypes.default_string");
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(602)) goto parse_default_bytes;
        break;
      }

      // optional bytes default_bytes = 75 [default = "world"];
      case 75: {
        if (tag == 602) {
         parse_default_bytes:
          DO_(::google::protobuf::internal::WireFormatLite::ReadBytes(
                input, this->mutable_default_bytes()));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(648)) goto parse_default_nested_enum;
        break;
      }

      // optional .protobuf_unittest_no_arena.TestAllTypes.NestedEnum default_nested_enum = 81 [default = BAR];
      case 81: {
        if (tag == 648) {
         parse_default_nested_enum:
          int value;
          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   int, ::google::protobuf::internal::WireFormatLite::TYPE_ENUM>(
                 input, &value)));
          if (::protobuf_unittest_no_arena::TestAllTypes_NestedEnum_IsValid(value)) {
            set_default_nested_enum(static_cast< ::protobuf_unittest_no_arena::TestAllTypes_NestedEnum >(value));
          } else {
            mutable_unknown_fields()->AddVarint(81, value);
          }
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(656)) goto parse_default_foreign_enum;
        break;
      }

      // optional .protobuf_unittest_no_arena.ForeignEnum default_foreign_enum = 82 [default = FOREIGN_BAR];
      case 82: {
        if (tag == 656) {
         parse_default_foreign_enum:
          int value;
          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   int, ::google::protobuf::internal::WireFormatLite::TYPE_ENUM>(
                 input, &value)));
          if (::protobuf_unittest_no_arena::ForeignEnum_IsValid(value)) {
            set_default_foreign_enum(static_cast< ::protobuf_unittest_no_arena::ForeignEnum >(value));
          } else {
            mutable_unknown_fields()->AddVarint(82, value);
          }
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(664)) goto parse_default_import_enum;
        break;
      }

      // optional .protobuf_unittest_import.ImportEnum default_import_enum = 83 [default = IMPORT_BAR];
      case 83: {
        if (tag == 664) {
         parse_default_import_enum:
          int value;
          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   int, ::google::protobuf::internal::WireFormatLite::TYPE_ENUM>(
                 input, &value)));
          if (::protobuf_unittest_import::ImportEnum_IsValid(value)) {
            set_default_import_enum(static_cast< ::protobuf_unittest_import::ImportEnum >(value));
          } else {
            mutable_unknown_fields()->AddVarint(83, value);
          }
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(674)) goto parse_default_string_piece;
        break;
      }

      // optional string default_string_piece = 84 [default = "abc", ctype = STRING_PIECE];
      case 84: {
        if (tag == 674) {
         parse_default_string_piece:
          DO_(::google::protobuf::internal::WireFormatLite::ReadString(
                input, this->mutable_default_string_piece()));
          ::google::protobuf::internal::WireFormat::VerifyUTF8StringNamedField(
            this->default_string_piece().data(), this->default_string_piece().length(),
            ::google::protobuf::internal::WireFormat::PARSE,
            "protobuf_unittest_no_arena.TestAllTypes.default_string_piece");
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(682)) goto parse_default_cord;
        break;
      }

      // optional string default_cord = 85 [default = "123", ctype = CORD];
      case 85: {
        if (tag == 682) {
         parse_default_cord:
          DO_(::google::protobuf::internal::WireFormatLite::ReadString(
                input, this->mutable_default_cord()));
          ::google::protobuf::internal::WireFormat::VerifyUTF8StringNamedField(
            this->default_cord().data(), this->default_cord().length(),
            ::google::protobuf::internal::WireFormat::PARSE,
            "protobuf_unittest_no_arena.TestAllTypes.default_cord");
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(888)) goto parse_oneof_uint32;
        break;
      }

      // optional uint32 oneof_uint32 = 111;
      case 111: {
        if (tag == 888) {
         parse_oneof_uint32:
          clear_oneof_field();
          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::uint32, ::google::protobuf::internal::WireFormatLite::TYPE_UINT32>(
                 input, &oneof_field_.oneof_uint32_)));
          set_has_oneof_uint32();
        } else {
          goto handle_unusual;
        }
        goto after_lazy_oneof_nested_message;
        break;
      }

      // optional .protobuf_unittest_no_arena.TestAllTypes.NestedMessage oneof_nested_message = 112;
      case 112: {
        if (tag == 898) {
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessageNoVirtual(
               input, mutable_oneof_nested_message()));
        } else {
          goto handle_unusual;
        }
        goto after_lazy_oneof_nested_message;
        break;
      }

      // optional string oneof_string = 113;
      case 113: {
        if (tag == 906) {
          DO_(::google::protobuf::internal::WireFormatLite::ReadString(
                input, this->mutable_oneof_string()));
          ::google::protobuf::internal::WireFormat::VerifyUTF8StringNamedField(
            this->oneof_string().data(), this->oneof_string().length(),
            ::google::protobuf::internal::WireFormat::PARSE,
            "protobuf_unittest_no_arena.TestAllTypes.oneof_string");
        } else {
          goto handle_unusual;
        }
        goto after_lazy_oneof_nested_message;
        break;
      }

      // optional bytes oneof_bytes = 114;
      case 114: {
        if (tag == 914) {
          DO_(::google::protobuf::internal::WireFormatLite::ReadBytes(
                input, this->mutable_oneof_bytes()));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(922)) goto parse_lazy_oneof_nested_message;
        break;
      }

      // optional .protobuf_unittest_no_arena.TestAllTypes.NestedMessage lazy_oneof_nested_message = 115 [lazy = true];
      case 115: {
        if (tag == 922) {
         parse_lazy_oneof_nested_message:
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessageNoVirtual(
               input, mutable_lazy_oneof_nested_message()));
        } else {
          goto handle_unusual;
        }
       after_lazy_oneof_nested_message:
        if (input->ExpectAtEnd()) goto success;
        break;
      }

      default: {
      handle_unusual:
        if (tag == 0 ||
            ::google::protobuf::internal::WireFormatLite::GetTagWireType(tag) ==
            ::google::protobuf::internal::WireFormatLite::WIRETYPE_END_GROUP) {
          goto success;
        }
        DO_(::google::protobuf::internal::WireFormat::SkipField(
              input, tag, mutable_unknown_fields()));
        break;
      }
    }
  }
success:
  // @@protoc_insertion_point(parse_success:protobuf_unittest_no_arena.TestAllTypes)
  return true;
failure:
  // @@protoc_insertion_point(parse_failure:protobuf_unittest_no_arena.TestAllTypes)
  return false;
#undef DO_
}

void TestAllTypes::SerializeWithCachedSizes(
    ::google::protobuf::io::CodedOutputStream* output) const {
  // @@protoc_insertion_point(serialize_start:protobuf_unittest_no_arena.TestAllTypes)
  // optional int32 optional_int32 = 1;
  if (has_optional_int32()) {
    ::google::protobuf::internal::WireFormatLite::WriteInt32(1, this->optional_int32(), output);
  }

  // optional int64 optional_int64 = 2;
  if (has_optional_int64()) {
    ::google::protobuf::internal::WireFormatLite::WriteInt64(2, this->optional_int64(), output);
  }

  // optional uint32 optional_uint32 = 3;
  if (has_optional_uint32()) {
    ::google::protobuf::internal::WireFormatLite::WriteUInt32(3, this->optional_uint32(), output);
  }

  // optional uint64 optional_uint64 = 4;
  if (has_optional_uint64()) {
    ::google::protobuf::internal::WireFormatLite::WriteUInt64(4, this->optional_uint64(), output);
  }

  // optional sint32 optional_sint32 = 5;
  if (has_optional_sint32()) {
    ::google::protobuf::internal::WireFormatLite::WriteSInt32(5, this->optional_sint32(), output);
  }

  // optional sint64 optional_sint64 = 6;
  if (has_optional_sint64()) {
    ::google::protobuf::internal::WireFormatLite::WriteSInt64(6, this->optional_sint64(), output);
  }

  // optional fixed32 optional_fixed32 = 7;
  if (has_optional_fixed32()) {
    ::google::protobuf::internal::WireFormatLite::WriteFixed32(7, this->optional_fixed32(), output);
  }

  // optional fixed64 optional_fixed64 = 8;
  if (has_optional_fixed64()) {
    ::google::protobuf::internal::WireFormatLite::WriteFixed64(8, this->optional_fixed64(), output);
  }

  // optional sfixed32 optional_sfixed32 = 9;
  if (has_optional_sfixed32()) {
    ::google::protobuf::internal::WireFormatLite::WriteSFixed32(9, this->optional_sfixed32(), output);
  }

  // optional sfixed64 optional_sfixed64 = 10;
  if (has_optional_sfixed64()) {
    ::google::protobuf::internal::WireFormatLite::WriteSFixed64(10, this->optional_sfixed64(), output);
  }

  // optional float optional_float = 11;
  if (has_optional_float()) {
    ::google::protobuf::internal::WireFormatLite::WriteFloat(11, this->optional_float(), output);
  }

  // optional double optional_double = 12;
  if (has_optional_double()) {
    ::google::protobuf::internal::WireFormatLite::WriteDouble(12, this->optional_double(), output);
  }

  // optional bool optional_bool = 13;
  if (has_optional_bool()) {
    ::google::protobuf::internal::WireFormatLite::WriteBool(13, this->optional_bool(), output);
  }

  // optional string optional_string = 14;
  if (has_optional_string()) {
    ::google::protobuf::internal::WireFormat::VerifyUTF8StringNamedField(
      this->optional_string().data(), this->optional_string().length(),
      ::google::protobuf::internal::WireFormat::SERIALIZE,
      "protobuf_unittest_no_arena.TestAllTypes.optional_string");
    ::google::protobuf::internal::WireFormatLite::WriteStringMaybeAliased(
      14, this->optional_string(), output);
  }

  // optional bytes optional_bytes = 15;
  if (has_optional_bytes()) {
    ::google::protobuf::internal::WireFormatLite::WriteBytesMaybeAliased(
      15, this->optional_bytes(), output);
  }

  // optional group OptionalGroup = 16 { ... };
  if (has_optionalgroup()) {
    ::google::protobuf::internal::WireFormatLite::WriteGroupMaybeToArray(
      16, *this->optionalgroup_, output);
  }

  // optional .protobuf_unittest_no_arena.TestAllTypes.NestedMessage optional_nested_message = 18;
  if (has_optional_nested_message()) {
    ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
      18, *this->optional_nested_message_, output);
  }

  // optional .protobuf_unittest_no_arena.ForeignMessage optional_foreign_message = 19;
  if (has_optional_foreign_message()) {
    ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
      19, *this->optional_foreign_message_, output);
  }

  // optional .protobuf_unittest_import.ImportMessage optional_import_message = 20;
  if (has_optional_import_message()) {
    ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
      20, *this->optional_import_message_, output);
  }

  // optional .protobuf_unittest_no_arena.TestAllTypes.NestedEnum optional_nested_enum = 21;
  if (has_optional_nested_enum()) {
    ::google::protobuf::internal::WireFormatLite::WriteEnum(
      21, this->optional_nested_enum(), output);
  }

  // optional .protobuf_unittest_no_arena.ForeignEnum optional_foreign_enum = 22;
  if (has_optional_foreign_enum()) {
    ::google::protobuf::internal::WireFormatLite::WriteEnum(
      22, this->optional_foreign_enum(), output);
  }

  // optional .protobuf_unittest_import.ImportEnum optional_import_enum = 23;
  if (has_optional_import_enum()) {
    ::google::protobuf::internal::WireFormatLite::WriteEnum(
      23, this->optional_import_enum(), output);
  }

  // optional string optional_string_piece = 24 [ctype = STRING_PIECE];
  if (has_optional_string_piece()) {
    ::google::protobuf::internal::WireFormat::VerifyUTF8StringNamedField(
      this->optional_string_piece().data(), this->optional_string_piece().length(),
      ::google::protobuf::internal::WireFormat::SERIALIZE,
      "protobuf_unittest_no_arena.TestAllTypes.optional_string_piece");
    ::google::protobuf::internal::WireFormatLite::WriteStringMaybeAliased(
      24, this->optional_string_piece(), output);
  }

  // optional string optional_cord = 25 [ctype = CORD];
  if (has_optional_cord()) {
    ::google::protobuf::internal::WireFormat::VerifyUTF8StringNamedField(
      this->optional_cord().data(), this->optional_cord().length(),
      ::google::protobuf::internal::WireFormat::SERIALIZE,
      "protobuf_unittest_no_arena.TestAllTypes.optional_cord");
    ::google::protobuf::internal::WireFormatLite::WriteStringMaybeAliased(
      25, this->optional_cord(), output);
  }

  // optional .protobuf_unittest_import.PublicImportMessage optional_public_import_message = 26;
  if (has_optional_public_import_message()) {
    ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
      26, *this->optional_public_import_message_, output);
  }

  // optional .protobuf_unittest_no_arena.TestAllTypes.NestedMessage optional_message = 27 [lazy = true];
  if (has_optional_message()) {
    ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
      27, *this->optional_message_, output);
  }

  // repeated int32 repeated_int32 = 31;
  for (int i = 0; i < this->repeated_int32_size(); i++) {
    ::google::protobuf::internal::WireFormatLite::WriteInt32(
      31, this->repeated_int32(i), output);
  }

  // repeated int64 repeated_int64 = 32;
  for (int i = 0; i < this->repeated_int64_size(); i++) {
    ::google::protobuf::internal::WireFormatLite::WriteInt64(
      32, this->repeated_int64(i), output);
  }

  // repeated uint32 repeated_uint32 = 33;
  for (int i = 0; i < this->repeated_uint32_size(); i++) {
    ::google::protobuf::internal::WireFormatLite::WriteUInt32(
      33, this->repeated_uint32(i), output);
  }

  // repeated uint64 repeated_uint64 = 34;
  for (int i = 0; i < this->repeated_uint64_size(); i++) {
    ::google::protobuf::internal::WireFormatLite::WriteUInt64(
      34, this->repeated_uint64(i), output);
  }

  // repeated sint32 repeated_sint32 = 35;
  for (int i = 0; i < this->repeated_sint32_size(); i++) {
    ::google::protobuf::internal::WireFormatLite::WriteSInt32(
      35, this->repeated_sint32(i), output);
  }

  // repeated sint64 repeated_sint64 = 36;
  for (int i = 0; i < this->repeated_sint64_size(); i++) {
    ::google::protobuf::internal::WireFormatLite::WriteSInt64(
      36, this->repeated_sint64(i), output);
  }

  // repeated fixed32 repeated_fixed32 = 37;
  for (int i = 0; i < this->repeated_fixed32_size(); i++) {
    ::google::protobuf::internal::WireFormatLite::WriteFixed32(
      37, this->repeated_fixed32(i), output);
  }

  // repeated fixed64 repeated_fixed64 = 38;
  for (int i = 0; i < this->repeated_fixed64_size(); i++) {
    ::google::protobuf::internal::WireFormatLite::WriteFixed64(
      38, this->repeated_fixed64(i), output);
  }

  // repeated sfixed32 repeated_sfixed32 = 39;
  for (int i = 0; i < this->repeated_sfixed32_size(); i++) {
    ::google::protobuf::internal::WireFormatLite::WriteSFixed32(
      39, this->repeated_sfixed32(i), output);
  }

  // repeated sfixed64 repeated_sfixed64 = 40;
  for (int i = 0; i < this->repeated_sfixed64_size(); i++) {
    ::google::protobuf::internal::WireFormatLite::WriteSFixed64(
      40, this->repeated_sfixed64(i), output);
  }

  // repeated float repeated_float = 41;
  for (int i = 0; i < this->repeated_float_size(); i++) {
    ::google::protobuf::internal::WireFormatLite::WriteFloat(
      41, this->repeated_float(i), output);
  }

  // repeated double repeated_double = 42;
  for (int i = 0; i < this->repeated_double_size(); i++) {
    ::google::protobuf::internal::WireFormatLite::WriteDouble(
      42, this->repeated_double(i), output);
  }

  // repeated bool repeated_bool = 43;
  for (int i = 0; i < this->repeated_bool_size(); i++) {
    ::google::protobuf::internal::WireFormatLite::WriteBool(
      43, this->repeated_bool(i), output);
  }

  // repeated string repeated_string = 44;
  for (int i = 0; i < this->repeated_string_size(); i++) {
    ::google::protobuf::internal::WireFormat::VerifyUTF8StringNamedField(
      this->repeated_string(i).data(), this->repeated_string(i).length(),
      ::google::protobuf::internal::WireFormat::SERIALIZE,
      "protobuf_unittest_no_arena.TestAllTypes.repeated_string");
    ::google::protobuf::internal::WireFormatLite::WriteString(
      44, this->repeated_string(i), output);
  }

  // repeated bytes repeated_bytes = 45;
  for (int i = 0; i < this->repeated_bytes_size(); i++) {
    ::google::protobuf::internal::WireFormatLite::WriteBytes(
      45, this->repeated_bytes(i), output);
  }

  // repeated group RepeatedGroup = 46 { ... };
  for (unsigned int i = 0, n = this->repeatedgroup_size(); i < n; i++) {
    ::google::protobuf::internal::WireFormatLite::WriteGroupMaybeToArray(
      46, this->repeatedgroup(i), output);
  }

  // repeated .protobuf_unittest_no_arena.TestAllTypes.NestedMessage repeated_nested_message = 48;
  for (unsigned int i = 0, n = this->repeated_nested_message_size(); i < n; i++) {
    ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
      48, this->repeated_nested_message(i), output);
  }

  // repeated .protobuf_unittest_no_arena.ForeignMessage repeated_foreign_message = 49;
  for (unsigned int i = 0, n = this->repeated_foreign_message_size(); i < n; i++) {
    ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
      49, this->repeated_foreign_message(i), output);
  }

  // repeated .protobuf_unittest_import.ImportMessage repeated_import_message = 50;
  for (unsigned int i = 0, n = this->repeated_import_message_size(); i < n; i++) {
    ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
      50, this->repeated_import_message(i), output);
  }

  // repeated .protobuf_unittest_no_arena.TestAllTypes.NestedEnum repeated_nested_enum = 51;
  for (int i = 0; i < this->repeated_nested_enum_size(); i++) {
    ::google::protobuf::internal::WireFormatLite::WriteEnum(
      51, this->repeated_nested_enum(i), output);
  }

  // repeated .protobuf_unittest_no_arena.ForeignEnum repeated_foreign_enum = 52;
  for (int i = 0; i < this->repeated_foreign_enum_size(); i++) {
    ::google::protobuf::internal::WireFormatLite::WriteEnum(
      52, this->repeated_foreign_enum(i), output);
  }

  // repeated .protobuf_unittest_import.ImportEnum repeated_import_enum = 53;
  for (int i = 0; i < this->repeated_import_enum_size(); i++) {
    ::google::protobuf::internal::WireFormatLite::WriteEnum(
      53, this->repeated_import_enum(i), output);
  }

  // repeated string repeated_string_piece = 54 [ctype = STRING_PIECE];
  for (int i = 0; i < this->repeated_string_piece_size(); i++) {
    ::google::protobuf::internal::WireFormat::VerifyUTF8StringNamedField(
      this->repeated_string_piece(i).data(), this->repeated_string_piece(i).length(),
      ::google::protobuf::internal::WireFormat::SERIALIZE,
      "protobuf_unittest_no_arena.TestAllTypes.repeated_string_piece");
    ::google::protobuf::internal::WireFormatLite::WriteString(
      54, this->repeated_string_piece(i), output);
  }

  // repeated string repeated_cord = 55 [ctype = CORD];
  for (int i = 0; i < this->repeated_cord_size(); i++) {
    ::google::protobuf::internal::WireFormat::VerifyUTF8StringNamedField(
      this->repeated_cord(i).data(), this->repeated_cord(i).length(),
      ::google::protobuf::internal::WireFormat::SERIALIZE,
      "protobuf_unittest_no_arena.TestAllTypes.repeated_cord");
    ::google::protobuf::internal::WireFormatLite::WriteString(
      55, this->repeated_cord(i), output);
  }

  // repeated .protobuf_unittest_no_arena.TestAllTypes.NestedMessage repeated_lazy_message = 57 [lazy = true];
  for (unsigned int i = 0, n = this->repeated_lazy_message_size(); i < n; i++) {
    ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
      57, this->repeated_lazy_message(i), output);
  }

  // optional int32 default_int32 = 61 [default = 41];
  if (has_default_int32()) {
    ::google::protobuf::internal::WireFormatLite::WriteInt32(61, this->default_int32(), output);
  }

  // optional int64 default_int64 = 62 [default = 42];
  if (has_default_int64()) {
    ::google::protobuf::internal::WireFormatLite::WriteInt64(62, this->default_int64(), output);
  }

  // optional uint32 default_uint32 = 63 [default = 43];
  if (has_default_uint32()) {
    ::google::protobuf::internal::WireFormatLite::WriteUInt32(63, this->default_uint32(), output);
  }

  // optional uint64 default_uint64 = 64 [default = 44];
  if (has_default_uint64()) {
    ::google::protobuf::internal::WireFormatLite::WriteUInt64(64, this->default_uint64(), output);
  }

  // optional sint32 default_sint32 = 65 [default = -45];
  if (has_default_sint32()) {
    ::google::protobuf::internal::WireFormatLite::WriteSInt32(65, this->default_sint32(), output);
  }

  // optional sint64 default_sint64 = 66 [default = 46];
  if (has_default_sint64()) {
    ::google::protobuf::internal::WireFormatLite::WriteSInt64(66, this->default_sint64(), output);
  }

  // optional fixed32 default_fixed32 = 67 [default = 47];
  if (has_default_fixed32()) {
    ::google::protobuf::internal::WireFormatLite::WriteFixed32(67, this->default_fixed32(), output);
  }

  // optional fixed64 default_fixed64 = 68 [default = 48];
  if (has_default_fixed64()) {
    ::google::protobuf::internal::WireFormatLite::WriteFixed64(68, this->default_fixed64(), output);
  }

  // optional sfixed32 default_sfixed32 = 69 [default = 49];
  if (has_default_sfixed32()) {
    ::google::protobuf::internal::WireFormatLite::WriteSFixed32(69, this->default_sfixed32(), output);
  }

  // optional sfixed64 default_sfixed64 = 70 [default = -50];
  if (has_default_sfixed64()) {
    ::google::protobuf::internal::WireFormatLite::WriteSFixed64(70, this->default_sfixed64(), output);
  }

  // optional float default_float = 71 [default = 51.5];
  if (has_default_float()) {
    ::google::protobuf::internal::WireFormatLite::WriteFloat(71, this->default_float(), output);
  }

  // optional double default_double = 72 [default = 52000];
  if (has_default_double()) {
    ::google::protobuf::internal::WireFormatLite::WriteDouble(72, this->default_double(), output);
  }

  // optional bool default_bool = 73 [default = true];
  if (has_default_bool()) {
    ::google::protobuf::internal::WireFormatLite::WriteBool(73, this->default_bool(), output);
  }

  // optional string default_string = 74 [default = "hello"];
  if (has_default_string()) {
    ::google::protobuf::internal::WireFormat::VerifyUTF8StringNamedField(
      this->default_string().data(), this->default_string().length(),
      ::google::protobuf::internal::WireFormat::SERIALIZE,
      "protobuf_unittest_no_arena.TestAllTypes.default_string");
    ::google::protobuf::internal::WireFormatLite::WriteStringMaybeAliased(
      74, this->default_string(), output);
  }

  // optional bytes default_bytes = 75 [default = "world"];
  if (has_default_bytes()) {
    ::google::protobuf::internal::WireFormatLite::WriteBytesMaybeAliased(
      75, this->default_bytes(), output);
  }

  // optional .protobuf_unittest_no_arena.TestAllTypes.NestedEnum default_nested_enum = 81 [default = BAR];
  if (has_default_nested_enum()) {
    ::google::protobuf::internal::WireFormatLite::WriteEnum(
      81, this->default_nested_enum(), output);
  }

  // optional .protobuf_unittest_no_arena.ForeignEnum default_foreign_enum = 82 [default = FOREIGN_BAR];
  if (has_default_foreign_enum()) {
    ::google::protobuf::internal::WireFormatLite::WriteEnum(
      82, this->default_foreign_enum(), output);
  }

  // optional .protobuf_unittest_import.ImportEnum default_import_enum = 83 [default = IMPORT_BAR];
  if (has_default_import_enum()) {
    ::google::protobuf::internal::WireFormatLite::WriteEnum(
      83, this->default_import_enum(), output);
  }

  // optional string default_string_piece = 84 [default = "abc", ctype = STRING_PIECE];
  if (has_default_string_piece()) {
    ::google::protobuf::internal::WireFormat::VerifyUTF8StringNamedField(
      this->default_string_piece().data(), this->default_string_piece().length(),
      ::google::protobuf::internal::WireFormat::SERIALIZE,
      "protobuf_unittest_no_arena.TestAllTypes.default_string_piece");
    ::google::protobuf::internal::WireFormatLite::WriteStringMaybeAliased(
      84, this->default_string_piece(), output);
  }

  // optional string default_cord = 85 [default = "123", ctype = CORD];
  if (has_default_cord()) {
    ::google::protobuf::internal::WireFormat::VerifyUTF8StringNamedField(
      this->default_cord().data(), this->default_cord().length(),
      ::google::protobuf::internal::WireFormat::SERIALIZE,
      "protobuf_unittest_no_arena.TestAllTypes.default_cord");
    ::google::protobuf::internal::WireFormatLite::WriteStringMaybeAliased(
      85, this->default_cord(), output);
  }

  switch (oneof_field_case()) {
    case kOneofUint32:
      ::google::protobuf::internal::WireFormatLite::WriteUInt32(111, this->oneof_uint32(), output);
      break;
    case kOneofNestedMessage:
      ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
        112, *oneof_field_.oneof_nested_message_, output);
      break;
    case kOneofString:
      ::google::protobuf::internal::WireFormat::VerifyUTF8StringNamedField(
        this->oneof_string().data(), this->oneof_string().length(),
        ::google::protobuf::internal::WireFormat::SERIALIZE,
        "protobuf_unittest_no_arena.TestAllTypes.oneof_string");
      ::google::protobuf::internal::WireFormatLite::WriteStringMaybeAliased(
        113, this->oneof_string(), output);
      break;
    case kOneofBytes:
      ::google::protobuf::internal::WireFormatLite::WriteBytesMaybeAliased(
        114, this->oneof_bytes(), output);
      break;
    case kLazyOneofNestedMessage:
      ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
        115, *oneof_field_.lazy_oneof_nested_message_, output);
      break;
    default: ;
  }
  if (_internal_metadata_.have_unknown_fields()) {
    ::google::protobuf::internal::WireFormat::SerializeUnknownFields(
        unknown_fields(), output);
  }
  // @@protoc_insertion_point(serialize_end:protobuf_unittest_no_arena.TestAllTypes)
}

::google::protobuf::uint8* TestAllTypes::InternalSerializeWithCachedSizesToArray(
    bool deterministic, ::google::protobuf::uint8* target) const {
  (void)deterministic; // Unused
  // @@protoc_insertion_point(serialize_to_array_start:protobuf_unittest_no_arena.TestAllTypes)
  // optional int32 optional_int32 = 1;
  if (has_optional_int32()) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt32ToArray(1, this->optional_int32(), target);
  }

  // optional int64 optional_int64 = 2;
  if (has_optional_int64()) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt64ToArray(2, this->optional_int64(), target);
  }

  // optional uint32 optional_uint32 = 3;
  if (has_optional_uint32()) {
    target = ::google::protobuf::internal::WireFormatLite::WriteUInt32ToArray(3, this->optional_uint32(), target);
  }

  // optional uint64 optional_uint64 = 4;
  if (has_optional_uint64()) {
    target = ::google::protobuf::internal::WireFormatLite::WriteUInt64ToArray(4, this->optional_uint64(), target);
  }

  // optional sint32 optional_sint32 = 5;
  if (has_optional_sint32()) {
    target = ::google::protobuf::internal::WireFormatLite::WriteSInt32ToArray(5, this->optional_sint32(), target);
  }

  // optional sint64 optional_sint64 = 6;
  if (has_optional_sint64()) {
    target = ::google::protobuf::internal::WireFormatLite::WriteSInt64ToArray(6, this->optional_sint64(), target);
  }

  // optional fixed32 optional_fixed32 = 7;
  if (has_optional_fixed32()) {
    target = ::google::protobuf::internal::WireFormatLite::WriteFixed32ToArray(7, this->optional_fixed32(), target);
  }

  // optional fixed64 optional_fixed64 = 8;
  if (has_optional_fixed64()) {
    target = ::google::protobuf::internal::WireFormatLite::WriteFixed64ToArray(8, this->optional_fixed64(), target);
  }

  // optional sfixed32 optional_sfixed32 = 9;
  if (has_optional_sfixed32()) {
    target = ::google::protobuf::internal::WireFormatLite::WriteSFixed32ToArray(9, this->optional_sfixed32(), target);
  }

  // optional sfixed64 optional_sfixed64 = 10;
  if (has_optional_sfixed64()) {
    target = ::google::protobuf::internal::WireFormatLite::WriteSFixed64ToArray(10, this->optional_sfixed64(), target);
  }

  // optional float optional_float = 11;
  if (has_optional_float()) {
    target = ::google::protobuf::internal::WireFormatLite::WriteFloatToArray(11, this->optional_float(), target);
  }

  // optional double optional_double = 12;
  if (has_optional_double()) {
    target = ::google::protobuf::internal::WireFormatLite::WriteDoubleToArray(12, this->optional_double(), target);
  }

  // optional bool optional_bool = 13;
  if (has_optional_bool()) {
    target = ::google::protobuf::internal::WireFormatLite::WriteBoolToArray(13, this->optional_bool(), target);
  }

  // optional string optional_string = 14;
  if (has_optional_string()) {
    ::google::protobuf::internal::WireFormat::VerifyUTF8StringNamedField(
      this->optional_string().data(), this->optional_string().length(),
      ::google::protobuf::internal::WireFormat::SERIALIZE,
      "protobuf_unittest_no_arena.TestAllTypes.optional_string");
    target =
      ::google::protobuf::internal::WireFormatLite::WriteStringToArray(
        14, this->optional_string(), target);
  }

  // optional bytes optional_bytes = 15;
  if (has_optional_bytes()) {
    target =
      ::google::protobuf::internal::WireFormatLite::WriteBytesToArray(
        15, this->optional_bytes(), target);
  }

  // optional group OptionalGroup = 16 { ... };
  if (has_optionalgroup()) {
    target = ::google::protobuf::internal::WireFormatLite::
      InternalWriteGroupNoVirtualToArray(
        16, *this->optionalgroup_, false, target);
  }

  // optional .protobuf_unittest_no_arena.TestAllTypes.NestedMessage optional_nested_message = 18;
  if (has_optional_nested_message()) {
    target = ::google::protobuf::internal::WireFormatLite::
      InternalWriteMessageNoVirtualToArray(
        18, *this->optional_nested_message_, false, target);
  }

  // optional .protobuf_unittest_no_arena.ForeignMessage optional_foreign_message = 19;
  if (has_optional_foreign_message()) {
    target = ::google::protobuf::internal::WireFormatLite::
      InternalWriteMessageNoVirtualToArray(
        19, *this->optional_foreign_message_, false, target);
  }

  // optional .protobuf_unittest_import.ImportMessage optional_import_message = 20;
  if (has_optional_import_message()) {
    target = ::google::protobuf::internal::WireFormatLite::
      InternalWriteMessageNoVirtualToArray(
        20, *this->optional_import_message_, false, target);
  }

  // optional .protobuf_unittest_no_arena.TestAllTypes.NestedEnum optional_nested_enum = 21;
  if (has_optional_nested_enum()) {
    target = ::google::protobuf::internal::WireFormatLite::WriteEnumToArray(
      21, this->optional_nested_enum(), target);
  }

  // optional .protobuf_unittest_no_arena.ForeignEnum optional_foreign_enum = 22;
  if (has_optional_foreign_enum()) {
    target = ::google::protobuf::internal::WireFormatLite::WriteEnumToArray(
      22, this->optional_foreign_enum(), target);
  }

  // optional .protobuf_unittest_import.ImportEnum optional_import_enum = 23;
  if (has_optional_import_enum()) {
    target = ::google::protobuf::internal::WireFormatLite::WriteEnumToArray(
      23, this->optional_import_enum(), target);
  }

  // optional string optional_string_piece = 24 [ctype = STRING_PIECE];
  if (has_optional_string_piece()) {
    ::google::protobuf::internal::WireFormat::VerifyUTF8StringNamedField(
      this->optional_string_piece().data(), this->optional_string_piece().length(),
      ::google::protobuf::internal::WireFormat::SERIALIZE,
      "protobuf_unittest_no_arena.TestAllTypes.optional_string_piece");
    target =
      ::google::protobuf::internal::WireFormatLite::WriteStringToArray(
        24, this->optional_string_piece(), target);
  }

  // optional string optional_cord = 25 [ctype = CORD];
  if (has_optional_cord()) {
    ::google::protobuf::internal::WireFormat::VerifyUTF8StringNamedField(
      this->optional_cord().data(), this->optional_cord().length(),
      ::google::protobuf::internal::WireFormat::SERIALIZE,
      "protobuf_unittest_no_arena.TestAllTypes.optional_cord");
    target =
      ::google::protobuf::internal::WireFormatLite::WriteStringToArray(
        25, this->optional_cord(), target);
  }

  // optional .protobuf_unittest_import.PublicImportMessage optional_public_import_message = 26;
  if (has_optional_public_import_message()) {
    target = ::google::protobuf::internal::WireFormatLite::
      InternalWriteMessageNoVirtualToArray(
        26, *this->optional_public_import_message_, false, target);
  }

  // optional .protobuf_unittest_no_arena.TestAllTypes.NestedMessage optional_message = 27 [lazy = true];
  if (has_optional_message()) {
    target = ::google::protobuf::internal::WireFormatLite::
      InternalWriteMessageNoVirtualToArray(
        27, *this->optional_message_, false, target);
  }

  // repeated int32 repeated_int32 = 31;
  for (int i = 0; i < this->repeated_int32_size(); i++) {
    target = ::google::protobuf::internal::WireFormatLite::
      WriteInt32ToArray(31, this->repeated_int32(i), target);
  }

  // repeated int64 repeated_int64 = 32;
  for (int i = 0; i < this->repeated_int64_size(); i++) {
    target = ::google::protobuf::internal::WireFormatLite::
      WriteInt64ToArray(32, this->repeated_int64(i), target);
  }

  // repeated uint32 repeated_uint32 = 33;
  for (int i = 0; i < this->repeated_uint32_size(); i++) {
    target = ::google::protobuf::internal::WireFormatLite::
      WriteUInt32ToArray(33, this->repeated_uint32(i), target);
  }

  // repeated uint64 repeated_uint64 = 34;
  for (int i = 0; i < this->repeated_uint64_size(); i++) {
    target = ::google::protobuf::internal::WireFormatLite::
      WriteUInt64ToArray(34, this->repeated_uint64(i), target);
  }

  // repeated sint32 repeated_sint32 = 35;
  for (int i = 0; i < this->repeated_sint32_size(); i++) {
    target = ::google::protobuf::internal::WireFormatLite::
      WriteSInt32ToArray(35, this->repeated_sint32(i), target);
  }

  // repeated sint64 repeated_sint64 = 36;
  for (int i = 0; i < this->repeated_sint64_size(); i++) {
    target = ::google::protobuf::internal::WireFormatLite::
      WriteSInt64ToArray(36, this->repeated_sint64(i), target);
  }

  // repeated fixed32 repeated_fixed32 = 37;
  for (int i = 0; i < this->repeated_fixed32_size(); i++) {
    target = ::google::protobuf::internal::WireFormatLite::
      WriteFixed32ToArray(37, this->repeated_fixed32(i), target);
  }

  // repeated fixed64 repeated_fixed64 = 38;
  for (int i = 0; i < this->repeated_fixed64_size(); i++) {
    target = ::google::protobuf::internal::WireFormatLite::
      WriteFixed64ToArray(38, this->repeated_fixed64(i), target);
  }

  // repeated sfixed32 repeated_sfixed32 = 39;
  for (int i = 0; i < this->repeated_sfixed32_size(); i++) {
    target = ::google::protobuf::internal::WireFormatLite::
      WriteSFixed32ToArray(39, this->repeated_sfixed32(i), target);
  }

  // repeated sfixed64 repeated_sfixed64 = 40;
  for (int i = 0; i < this->repeated_sfixed64_size(); i++) {
    target = ::google::protobuf::internal::WireFormatLite::
      WriteSFixed64ToArray(40, this->repeated_sfixed64(i), target);
  }

  // repeated float repeated_float = 41;
  for (int i = 0; i < this->repeated_float_size(); i++) {
    target = ::google::protobuf::internal::WireFormatLite::
      WriteFloatToArray(41, this->repeated_float(i), target);
  }

  // repeated double repeated_double = 42;
  for (int i = 0; i < this->repeated_double_size(); i++) {
    target = ::google::protobuf::internal::WireFormatLite::
      WriteDoubleToArray(42, this->repeated_double(i), target);
  }

  // repeated bool repeated_bool = 43;
  for (int i = 0; i < this->repeated_bool_size(); i++) {
    target = ::google::protobuf::internal::WireFormatLite::
      WriteBoolToArray(43, this->repeated_bool(i), target);
  }

  // repeated string repeated_string = 44;
  for (int i = 0; i < this->repeated_string_size(); i++) {
    ::google::protobuf::internal::WireFormat::VerifyUTF8StringNamedField(
      this->repeated_string(i).data(), this->repeated_string(i).length(),
      ::google::protobuf::internal::WireFormat::SERIALIZE,
      "protobuf_unittest_no_arena.TestAllTypes.repeated_string");
    target = ::google::protobuf::internal::WireFormatLite::
      WriteStringToArray(44, this->repeated_string(i), target);
  }

  // repeated bytes repeated_bytes = 45;
  for (int i = 0; i < this->repeated_bytes_size(); i++) {
    target = ::google::protobuf::internal::WireFormatLite::
      WriteBytesToArray(45, this->repeated_bytes(i), target);
  }

  // repeated group RepeatedGroup = 46 { ... };
  for (unsigned int i = 0, n = this->repeatedgroup_size(); i < n; i++) {
    target = ::google::protobuf::internal::WireFormatLite::
      InternalWriteGroupNoVirtualToArray(
        46, this->repeatedgroup(i), false, target);
  }

  // repeated .protobuf_unittest_no_arena.TestAllTypes.NestedMessage repeated_nested_message = 48;
  for (unsigned int i = 0, n = this->repeated_nested_message_size(); i < n; i++) {
    target = ::google::protobuf::internal::WireFormatLite::
      InternalWriteMessageNoVirtualToArray(
        48, this->repeated_nested_message(i), false, target);
  }

  // repeated .protobuf_unittest_no_arena.ForeignMessage repeated_foreign_message = 49;
  for (unsigned int i = 0, n = this->repeated_foreign_message_size(); i < n; i++) {
    target = ::google::protobuf::internal::WireFormatLite::
      InternalWriteMessageNoVirtualToArray(
        49, this->repeated_foreign_message(i), false, target);
  }

  // repeated .protobuf_unittest_import.ImportMessage repeated_import_message = 50;
  for (unsigned int i = 0, n = this->repeated_import_message_size(); i < n; i++) {
    target = ::google::protobuf::internal::WireFormatLite::
      InternalWriteMessageNoVirtualToArray(
        50, this->repeated_import_message(i), false, target);
  }

  // repeated .protobuf_unittest_no_arena.TestAllTypes.NestedEnum repeated_nested_enum = 51;
  for (int i = 0; i < this->repeated_nested_enum_size(); i++) {
    target = ::google::protobuf::internal::WireFormatLite::WriteEnumToArray(
      51, this->repeated_nested_enum(i), target);
  }

  // repeated .protobuf_unittest_no_arena.ForeignEnum repeated_foreign_enum = 52;
  for (int i = 0; i < this->repeated_foreign_enum_size(); i++) {
    target = ::google::protobuf::internal::WireFormatLite::WriteEnumToArray(
      52, this->repeated_foreign_enum(i), target);
  }

  // repeated .protobuf_unittest_import.ImportEnum repeated_import_enum = 53;
  for (int i = 0; i < this->repeated_import_enum_size(); i++) {
    target = ::google::protobuf::internal::WireFormatLite::WriteEnumToArray(
      53, this->repeated_import_enum(i), target);
  }

  // repeated string repeated_string_piece = 54 [ctype = STRING_PIECE];
  for (int i = 0; i < this->repeated_string_piece_size(); i++) {
    ::google::protobuf::internal::WireFormat::VerifyUTF8StringNamedField(
      this->repeated_string_piece(i).data(), this->repeated_string_piece(i).length(),
      ::google::protobuf::internal::WireFormat::SERIALIZE,
      "protobuf_unittest_no_arena.TestAllTypes.repeated_string_piece");
    target = ::google::protobuf::internal::WireFormatLite::
      WriteStringToArray(54, this->repeated_string_piece(i), target);
  }

  // repeated string repeated_cord = 55 [ctype = CORD];
  for (int i = 0; i < this->repeated_cord_size(); i++) {
    ::google::protobuf::internal::WireFormat::VerifyUTF8StringNamedField(
      this->repeated_cord(i).data(), this->repeated_cord(i).length(),
      ::google::protobuf::internal::WireFormat::SERIALIZE,
      "protobuf_unittest_no_arena.TestAllTypes.repeated_cord");
    target = ::google::protobuf::internal::WireFormatLite::
      WriteStringToArray(55, this->repeated_cord(i), target);
  }

  // repeated .protobuf_unittest_no_arena.TestAllTypes.NestedMessage repeated_lazy_message = 57 [lazy = true];
  for (unsigned int i = 0, n = this->repeated_lazy_message_size(); i < n; i++) {
    target = ::google::protobuf::internal::WireFormatLite::
      InternalWriteMessageNoVirtualToArray(
        57, this->repeated_lazy_message(i), false, target);
  }

  // optional int32 default_int32 = 61 [default = 41];
  if (has_default_int32()) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt32ToArray(61, this->default_int32(), target);
  }

  // optional int64 default_int64 = 62 [default = 42];
  if (has_default_int64()) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt64ToArray(62, this->default_int64(), target);
  }

  // optional uint32 default_uint32 = 63 [default = 43];
  if (has_default_uint32()) {
    target = ::google::protobuf::internal::WireFormatLite::WriteUInt32ToArray(63, this->default_uint32(), target);
  }

  // optional uint64 default_uint64 = 64 [default = 44];
  if (has_default_uint64()) {
    target = ::google::protobuf::internal::WireFormatLite::WriteUInt64ToArray(64, this->default_uint64(), target);
  }

  // optional sint32 default_sint32 = 65 [default = -45];
  if (has_default_sint32()) {
    target = ::google::protobuf::internal::WireFormatLite::WriteSInt32ToArray(65, this->default_sint32(), target);
  }

  // optional sint64 default_sint64 = 66 [default = 46];
  if (has_default_sint64()) {
    target = ::google::protobuf::internal::WireFormatLite::WriteSInt64ToArray(66, this->default_sint64(), target);
  }

  // optional fixed32 default_fixed32 = 67 [default = 47];
  if (has_default_fixed32()) {
    target = ::google::protobuf::internal::WireFormatLite::WriteFixed32ToArray(67, this->default_fixed32(), target);
  }

  // optional fixed64 default_fixed64 = 68 [default = 48];
  if (has_default_fixed64()) {
    target = ::google::protobuf::internal::WireFormatLite::WriteFixed64ToArray(68, this->default_fixed64(), target);
  }

  // optional sfixed32 default_sfixed32 = 69 [default = 49];
  if (has_default_sfixed32()) {
    target = ::google::protobuf::internal::WireFormatLite::WriteSFixed32ToArray(69, this->default_sfixed32(), target);
  }

  // optional sfixed64 default_sfixed64 = 70 [default = -50];
  if (has_default_sfixed64()) {
    target = ::google::protobuf::internal::WireFormatLite::WriteSFixed64ToArray(70, this->default_sfixed64(), target);
  }

  // optional float default_float = 71 [default = 51.5];
  if (has_default_float()) {
    target = ::google::protobuf::internal::WireFormatLite::WriteFloatToArray(71, this->default_float(), target);
  }

  // optional double default_double = 72 [default = 52000];
  if (has_default_double()) {
    target = ::google::protobuf::internal::WireFormatLite::WriteDoubleToArray(72, this->default_double(), target);
  }

  // optional bool default_bool = 73 [default = true];
  if (has_default_bool()) {
    target = ::google::protobuf::internal::WireFormatLite::WriteBoolToArray(73, this->default_bool(), target);
  }

  // optional string default_string = 74 [default = "hello"];
  if (has_default_string()) {
    ::google::protobuf::internal::WireFormat::VerifyUTF8StringNamedField(
      this->default_string().data(), this->default_string().length(),
      ::google::protobuf::internal::WireFormat::SERIALIZE,
      "protobuf_unittest_no_arena.TestAllTypes.default_string");
    target =
      ::google::protobuf::internal::WireFormatLite::WriteStringToArray(
        74, this->default_string(), target);
  }

  // optional bytes default_bytes = 75 [default = "world"];
  if (has_default_bytes()) {
    target =
      ::google::protobuf::internal::WireFormatLite::WriteBytesToArray(
        75, this->default_bytes(), target);
  }

  // optional .protobuf_unittest_no_arena.TestAllTypes.NestedEnum default_nested_enum = 81 [default = BAR];
  if (has_default_nested_enum()) {
    target = ::google::protobuf::internal::WireFormatLite::WriteEnumToArray(
      81, this->default_nested_enum(), target);
  }

  // optional .protobuf_unittest_no_arena.ForeignEnum default_foreign_enum = 82 [default = FOREIGN_BAR];
  if (has_default_foreign_enum()) {
    target = ::google::protobuf::internal::WireFormatLite::WriteEnumToArray(
      82, this->default_foreign_enum(), target);
  }

  // optional .protobuf_unittest_import.ImportEnum default_import_enum = 83 [default = IMPORT_BAR];
  if (has_default_import_enum()) {
    target = ::google::protobuf::internal::WireFormatLite::WriteEnumToArray(
      83, this->default_import_enum(), target);
  }

  // optional string default_string_piece = 84 [default = "abc", ctype = STRING_PIECE];
  if (has_default_string_piece()) {
    ::google::protobuf::internal::WireFormat::VerifyUTF8StringNamedField(
      this->default_string_piece().data(), this->default_string_piece().length(),
      ::google::protobuf::internal::WireFormat::SERIALIZE,
      "protobuf_unittest_no_arena.TestAllTypes.default_string_piece");
    target =
      ::google::protobuf::internal::WireFormatLite::WriteStringToArray(
        84, this->default_string_piece(), target);
  }

  // optional string default_cord = 85 [default = "123", ctype = CORD];
  if (has_default_cord()) {
    ::google::protobuf::internal::WireFormat::VerifyUTF8StringNamedField(
      this->default_cord().data(), this->default_cord().length(),
      ::google::protobuf::internal::WireFormat::SERIALIZE,
      "protobuf_unittest_no_arena.TestAllTypes.default_cord");
    target =
      ::google::protobuf::internal::WireFormatLite::WriteStringToArray(
        85, this->default_cord(), target);
  }

  switch (oneof_field_case()) {
    case kOneofUint32:
      target = ::google::protobuf::internal::WireFormatLite::WriteUInt32ToArray(111, this->oneof_uint32(), target);
      break;
    case kOneofNestedMessage:
      target = ::google::protobuf::internal::WireFormatLite::
        InternalWriteMessageNoVirtualToArray(
          112, *oneof_field_.oneof_nested_message_, false, target);
      break;
    case kOneofString:
      ::google::protobuf::internal::WireFormat::VerifyUTF8StringNamedField(
        this->oneof_string().data(), this->oneof_string().length(),
        ::google::protobuf::internal::WireFormat::SERIALIZE,
        "protobuf_unittest_no_arena.TestAllTypes.oneof_string");
      target =
        ::google::protobuf::internal::WireFormatLite::WriteStringToArray(
          113, this->oneof_string(), target);
      break;
    case kOneofBytes:
      target =
        ::google::protobuf::internal::WireFormatLite::WriteBytesToArray(
          114, this->oneof_bytes(), target);
      break;
    case kLazyOneofNestedMessage:
      target = ::google::protobuf::internal::WireFormatLite::
        InternalWriteMessageNoVirtualToArray(
          115, *oneof_field_.lazy_oneof_nested_message_, false, target);
      break;
    default: ;
  }
  if (_internal_metadata_.have_unknown_fields()) {
    target = ::google::protobuf::internal::WireFormat::SerializeUnknownFieldsToArray(
        unknown_fields(), target);
  }
  // @@protoc_insertion_point(serialize_to_array_end:protobuf_unittest_no_arena.TestAllTypes)
  return target;
}

size_t TestAllTypes::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:protobuf_unittest_no_arena.TestAllTypes)
  size_t total_size = 0;

  if (_has_bits_[0 / 32] & 255u) {
    // optional int32 optional_int32 = 1;
    if (has_optional_int32()) {
      total_size += 1 +
        ::google::protobuf::internal::WireFormatLite::Int32Size(
          this->optional_int32());
    }

    // optional int64 optional_int64 = 2;
    if (has_optional_int64()) {
      total_size += 1 +
        ::google::protobuf::internal::WireFormatLite::Int64Size(
          this->optional_int64());
    }

    // optional uint32 optional_uint32 = 3;
    if (has_optional_uint32()) {
      total_size += 1 +
        ::google::protobuf::internal::WireFormatLite::UInt32Size(
          this->optional_uint32());
    }

    // optional uint64 optional_uint64 = 4;
    if (has_optional_uint64()) {
      total_size += 1 +
        ::google::protobuf::internal::WireFormatLite::UInt64Size(
          this->optional_uint64());
    }

    // optional sint32 optional_sint32 = 5;
    if (has_optional_sint32()) {
      total_size += 1 +
        ::google::protobuf::internal::WireFormatLite::SInt32Size(
          this->optional_sint32());
    }

    // optional sint64 optional_sint64 = 6;
    if (has_optional_sint64()) {
      total_size += 1 +
        ::google::protobuf::internal::WireFormatLite::SInt64Size(
          this->optional_sint64());
    }

    // optional fixed32 optional_fixed32 = 7;
    if (has_optional_fixed32()) {
      total_size += 1 + 4;
    }

    // optional fixed64 optional_fixed64 = 8;
    if (has_optional_fixed64()) {
      total_size += 1 + 8;
    }

  }
  if (_has_bits_[8 / 32] & 65280u) {
    // optional sfixed32 optional_sfixed32 = 9;
    if (has_optional_sfixed32()) {
      total_size += 1 + 4;
    }

    // optional sfixed64 optional_sfixed64 = 10;
    if (has_optional_sfixed64()) {
      total_size += 1 + 8;
    }

    // optional float optional_float = 11;
    if (has_optional_float()) {
      total_size += 1 + 4;
    }

    // optional double optional_double = 12;
    if (has_optional_double()) {
      total_size += 1 + 8;
    }

    // optional bool optional_bool = 13;
    if (has_optional_bool()) {
      total_size += 1 + 1;
    }

    // optional string optional_string = 14;
    if (has_optional_string()) {
      total_size += 1 +
        ::google::protobuf::internal::WireFormatLite::StringSize(
          this->optional_string());
    }

    // optional bytes optional_bytes = 15;
    if (has_optional_bytes()) {
      total_size += 1 +
        ::google::protobuf::internal::WireFormatLite::BytesSize(
          this->optional_bytes());
    }

    // optional group OptionalGroup = 16 { ... };
    if (has_optionalgroup()) {
      total_size += 4 +
        ::google::protobuf::internal::WireFormatLite::GroupSizeNoVirtual(
          *this->optionalgroup_);
    }

  }
  if (_has_bits_[16 / 32] & 16711680u) {
    // optional .protobuf_unittest_no_arena.TestAllTypes.NestedMessage optional_nested_message = 18;
    if (has_optional_nested_message()) {
      total_size += 2 +
        ::google::protobuf::internal::WireFormatLite::MessageSizeNoVirtual(
          *this->optional_nested_message_);
    }

    // optional .protobuf_unittest_no_arena.ForeignMessage optional_foreign_message = 19;
    if (has_optional_foreign_message()) {
      total_size += 2 +
        ::google::protobuf::internal::WireFormatLite::MessageSizeNoVirtual(
          *this->optional_foreign_message_);
    }

    // optional .protobuf_unittest_import.ImportMessage optional_import_message = 20;
    if (has_optional_import_message()) {
      total_size += 2 +
        ::google::protobuf::internal::WireFormatLite::MessageSizeNoVirtual(
          *this->optional_import_message_);
    }

    // optional .protobuf_unittest_no_arena.TestAllTypes.NestedEnum optional_nested_enum = 21;
    if (has_optional_nested_enum()) {
      total_size += 2 +
        ::google::protobuf::internal::WireFormatLite::EnumSize(this->optional_nested_enum());
    }

    // optional .protobuf_unittest_no_arena.ForeignEnum optional_foreign_enum = 22;
    if (has_optional_foreign_enum()) {
      total_size += 2 +
        ::google::protobuf::internal::WireFormatLite::EnumSize(this->optional_foreign_enum());
    }

    // optional .protobuf_unittest_import.ImportEnum optional_import_enum = 23;
    if (has_optional_import_enum()) {
      total_size += 2 +
        ::google::protobuf::internal::WireFormatLite::EnumSize(this->optional_import_enum());
    }

    // optional string optional_string_piece = 24 [ctype = STRING_PIECE];
    if (has_optional_string_piece()) {
      total_size += 2 +
        ::google::protobuf::internal::WireFormatLite::StringSize(
          this->optional_string_piece());
    }

    // optional string optional_cord = 25 [ctype = CORD];
    if (has_optional_cord()) {
      total_size += 2 +
        ::google::protobuf::internal::WireFormatLite::StringSize(
          this->optional_cord());
    }

  }
  if (_has_bits_[24 / 32] & 50331648u) {
    // optional .protobuf_unittest_import.PublicImportMessage optional_public_import_message = 26;
    if (has_optional_public_import_message()) {
      total_size += 2 +
        ::google::protobuf::internal::WireFormatLite::MessageSizeNoVirtual(
          *this->optional_public_import_message_);
    }

    // optional .protobuf_unittest_no_arena.TestAllTypes.NestedMessage optional_message = 27 [lazy = true];
    if (has_optional_message()) {
      total_size += 2 +
        ::google::protobuf::internal::WireFormatLite::MessageSizeNoVirtual(
          *this->optional_message_);
    }

  }
  if (_has_bits_[51 / 32] & 16252928u) {
    // optional int32 default_int32 = 61 [default = 41];
    if (has_default_int32()) {
      total_size += 2 +
        ::google::protobuf::internal::WireFormatLite::Int32Size(
          this->default_int32());
    }

    // optional int64 default_int64 = 62 [default = 42];
    if (has_default_int64()) {
      total_size += 2 +
        ::google::protobuf::internal::WireFormatLite::Int64Size(
          this->default_int64());
    }

    // optional uint32 default_uint32 = 63 [default = 43];
    if (has_default_uint32()) {
      total_size += 2 +
        ::google::protobuf::internal::WireFormatLite::UInt32Size(
          this->default_uint32());
    }

    // optional uint64 default_uint64 = 64 [default = 44];
    if (has_default_uint64()) {
      total_size += 2 +
        ::google::protobuf::internal::WireFormatLite::UInt64Size(
          this->default_uint64());
    }

    // optional sint32 default_sint32 = 65 [default = -45];
    if (has_default_sint32()) {
      total_size += 2 +
        ::google::protobuf::internal::WireFormatLite::SInt32Size(
          this->default_sint32());
    }

  }
  if (_has_bits_[56 / 32] & 4278190080u) {
    // optional sint64 default_sint64 = 66 [default = 46];
    if (has_default_sint64()) {
      total_size += 2 +
        ::google::protobuf::internal::WireFormatLite::SInt64Size(
          this->default_sint64());
    }

    // optional fixed32 default_fixed32 = 67 [default = 47];
    if (has_default_fixed32()) {
      total_size += 2 + 4;
    }

    // optional fixed64 default_fixed64 = 68 [default = 48];
    if (has_default_fixed64()) {
      total_size += 2 + 8;
    }

    // optional sfixed32 default_sfixed32 = 69 [default = 49];
    if (has_default_sfixed32()) {
      total_size += 2 + 4;
    }

    // optional sfixed64 default_sfixed64 = 70 [default = -50];
    if (has_default_sfixed64()) {
      total_size += 2 + 8;
    }

    // optional float default_float = 71 [default = 51.5];
    if (has_default_float()) {
      total_size += 2 + 4;
    }

    // optional double default_double = 72 [default = 52000];
    if (has_default_double()) {
      total_size += 2 + 8;
    }

    // optional bool default_bool = 73 [default = true];
    if (has_default_bool()) {
      total_size += 2 + 1;
    }

  }
  if (_has_bits_[64 / 32] & 127u) {
    // optional string default_string = 74 [default = "hello"];
    if (has_default_string()) {
      total_size += 2 +
        ::google::protobuf::internal::WireFormatLite::StringSize(
          this->default_string());
    }

    // optional bytes default_bytes = 75 [default = "world"];
    if (has_default_bytes()) {
      total_size += 2 +
        ::google::protobuf::internal::WireFormatLite::BytesSize(
          this->default_bytes());
    }

    // optional .protobuf_unittest_no_arena.TestAllTypes.NestedEnum default_nested_enum = 81 [default = BAR];
    if (has_default_nested_enum()) {
      total_size += 2 +
        ::google::protobuf::internal::WireFormatLite::EnumSize(this->default_nested_enum());
    }

    // optional .protobuf_unittest_no_arena.ForeignEnum default_foreign_enum = 82 [default = FOREIGN_BAR];
    if (has_default_foreign_enum()) {
      total_size += 2 +
        ::google::protobuf::internal::WireFormatLite::EnumSize(this->default_foreign_enum());
    }

    // optional .protobuf_unittest_import.ImportEnum default_import_enum = 83 [default = IMPORT_BAR];
    if (has_default_import_enum()) {
      total_size += 2 +
        ::google::protobuf::internal::WireFormatLite::EnumSize(this->default_import_enum());
    }

    // optional string default_string_piece = 84 [default = "abc", ctype = STRING_PIECE];
    if (has_default_string_piece()) {
      total_size += 2 +
        ::google::protobuf::internal::WireFormatLite::StringSize(
          this->default_string_piece());
    }

    // optional string default_cord = 85 [default = "123", ctype = CORD];
    if (has_default_cord()) {
      total_size += 2 +
        ::google::protobuf::internal::WireFormatLite::StringSize(
          this->default_cord());
    }

  }
  // repeated int32 repeated_int32 = 31;
  {
    size_t data_size = 0;
    unsigned int count = this->repeated_int32_size();
    for (unsigned int i = 0; i < count; i++) {
      data_size += ::google::protobuf::internal::WireFormatLite::
        Int32Size(this->repeated_int32(i));
    }
    total_size += 2 *
                  ::google::protobuf::internal::FromIntSize(this->repeated_int32_size());
    total_size += data_size;
  }

  // repeated int64 repeated_int64 = 32;
  {
    size_t data_size = 0;
    unsigned int count = this->repeated_int64_size();
    for (unsigned int i = 0; i < count; i++) {
      data_size += ::google::protobuf::internal::WireFormatLite::
        Int64Size(this->repeated_int64(i));
    }
    total_size += 2 *
                  ::google::protobuf::internal::FromIntSize(this->repeated_int64_size());
    total_size += data_size;
  }

  // repeated uint32 repeated_uint32 = 33;
  {
    size_t data_size = 0;
    unsigned int count = this->repeated_uint32_size();
    for (unsigned int i = 0; i < count; i++) {
      data_size += ::google::protobuf::internal::WireFormatLite::
        UInt32Size(this->repeated_uint32(i));
    }
    total_size += 2 *
                  ::google::protobuf::internal::FromIntSize(this->repeated_uint32_size());
    total_size += data_size;
  }

  // repeated uint64 repeated_uint64 = 34;
  {
    size_t data_size = 0;
    unsigned int count = this->repeated_uint64_size();
    for (unsigned int i = 0; i < count; i++) {
      data_size += ::google::protobuf::internal::WireFormatLite::
        UInt64Size(this->repeated_uint64(i));
    }
    total_size += 2 *
                  ::google::protobuf::internal::FromIntSize(this->repeated_uint64_size());
    total_size += data_size;
  }

  // repeated sint32 repeated_sint32 = 35;
  {
    size_t data_size = 0;
    unsigned int count = this->repeated_sint32_size();
    for (unsigned int i = 0; i < count; i++) {
      data_size += ::google::protobuf::internal::WireFormatLite::
        SInt32Size(this->repeated_sint32(i));
    }
    total_size += 2 *
                  ::google::protobuf::internal::FromIntSize(this->repeated_sint32_size());
    total_size += data_size;
  }

  // repeated sint64 repeated_sint64 = 36;
  {
    size_t data_size = 0;
    unsigned int count = this->repeated_sint64_size();
    for (unsigned int i = 0; i < count; i++) {
      data_size += ::google::protobuf::internal::WireFormatLite::
        SInt64Size(this->repeated_sint64(i));
    }
    total_size += 2 *
                  ::google::protobuf::internal::FromIntSize(this->repeated_sint64_size());
    total_size += data_size;
  }

  // repeated fixed32 repeated_fixed32 = 37;
  {
    size_t data_size = 0;
    unsigned int count = this->repeated_fixed32_size();
    data_size = 4UL * count;
    total_size += 2 *
                  ::google::protobuf::internal::FromIntSize(this->repeated_fixed32_size());
    total_size += data_size;
  }

  // repeated fixed64 repeated_fixed64 = 38;
  {
    size_t data_size = 0;
    unsigned int count = this->repeated_fixed64_size();
    data_size = 8UL * count;
    total_size += 2 *
                  ::google::protobuf::internal::FromIntSize(this->repeated_fixed64_size());
    total_size += data_size;
  }

  // repeated sfixed32 repeated_sfixed32 = 39;
  {
    size_t data_size = 0;
    unsigned int count = this->repeated_sfixed32_size();
    data_size = 4UL * count;
    total_size += 2 *
                  ::google::protobuf::internal::FromIntSize(this->repeated_sfixed32_size());
    total_size += data_size;
  }

  // repeated sfixed64 repeated_sfixed64 = 40;
  {
    size_t data_size = 0;
    unsigned int count = this->repeated_sfixed64_size();
    data_size = 8UL * count;
    total_size += 2 *
                  ::google::protobuf::internal::FromIntSize(this->repeated_sfixed64_size());
    total_size += data_size;
  }

  // repeated float repeated_float = 41;
  {
    size_t data_size = 0;
    unsigned int count = this->repeated_float_size();
    data_size = 4UL * count;
    total_size += 2 *
                  ::google::protobuf::internal::FromIntSize(this->repeated_float_size());
    total_size += data_size;
  }

  // repeated double repeated_double = 42;
  {
    size_t data_size = 0;
    unsigned int count = this->repeated_double_size();
    data_size = 8UL * count;
    total_size += 2 *
                  ::google::protobuf::internal::FromIntSize(this->repeated_double_size());
    total_size += data_size;
  }

  // repeated bool repeated_bool = 43;
  {
    size_t data_size = 0;
    unsigned int count = this->repeated_bool_size();
    data_size = 1UL * count;
    total_size += 2 *
                  ::google::protobuf::internal::FromIntSize(this->repeated_bool_size());
    total_size += data_size;
  }

  // repeated string repeated_string = 44;
  total_size += 2 *
      ::google::protobuf::internal::FromIntSize(this->repeated_string_size());
  for (int i = 0; i < this->repeated_string_size(); i++) {
    total_size += ::google::protobuf::internal::WireFormatLite::StringSize(
      this->repeated_string(i));
  }

  // repeated bytes repeated_bytes = 45;
  total_size += 2 *
      ::google::protobuf::internal::FromIntSize(this->repeated_bytes_size());
  for (int i = 0; i < this->repeated_bytes_size(); i++) {
    total_size += ::google::protobuf::internal::WireFormatLite::BytesSize(
      this->repeated_bytes(i));
  }

  // repeated group RepeatedGroup = 46 { ... };
  {
    unsigned int count = this->repeatedgroup_size();
    total_size += 4UL * count;
    for (unsigned int i = 0; i < count; i++) {
      total_size +=
        ::google::protobuf::internal::WireFormatLite::GroupSizeNoVirtual(
          this->repeatedgroup(i));
    }
  }

  // repeated .protobuf_unittest_no_arena.TestAllTypes.NestedMessage repeated_nested_message = 48;
  {
    unsigned int count = this->repeated_nested_message_size();
    total_size += 2UL * count;
    for (unsigned int i = 0; i < count; i++) {
      total_size +=
        ::google::protobuf::internal::WireFormatLite::MessageSizeNoVirtual(
          this->repeated_nested_message(i));
    }
  }

  // repeated .protobuf_unittest_no_arena.ForeignMessage repeated_foreign_message = 49;
  {
    unsigned int count = this->repeated_foreign_message_size();
    total_size += 2UL * count;
    for (unsigned int i = 0; i < count; i++) {
      total_size +=
        ::google::protobuf::internal::WireFormatLite::MessageSizeNoVirtual(
          this->repeated_foreign_message(i));
    }
  }

  // repeated .protobuf_unittest_import.ImportMessage repeated_import_message = 50;
  {
    unsigned int count = this->repeated_import_message_size();
    total_size += 2UL * count;
    for (unsigned int i = 0; i < count; i++) {
      total_size +=
        ::google::protobuf::internal::WireFormatLite::MessageSizeNoVirtual(
          this->repeated_import_message(i));
    }
  }

  // repeated .protobuf_unittest_no_arena.TestAllTypes.NestedEnum repeated_nested_enum = 51;
  {
    size_t data_size = 0;
    unsigned int count = this->repeated_nested_enum_size();for (unsigned int i = 0; i < count; i++) {
      data_size += ::google::protobuf::internal::WireFormatLite::EnumSize(
        this->repeated_nested_enum(i));
    }
    total_size += (2UL * count) + data_size;
  }

  // repeated .protobuf_unittest_no_arena.ForeignEnum repeated_foreign_enum = 52;
  {
    size_t data_size = 0;
    unsigned int count = this->repeated_foreign_enum_size();for (unsigned int i = 0; i < count; i++) {
      data_size += ::google::protobuf::internal::WireFormatLite::EnumSize(
        this->repeated_foreign_enum(i));
    }
    total_size += (2UL * count) + data_size;
  }

  // repeated .protobuf_unittest_import.ImportEnum repeated_import_enum = 53;
  {
    size_t data_size = 0;
    unsigned int count = this->repeated_import_enum_size();for (unsigned int i = 0; i < count; i++) {
      data_size += ::google::protobuf::internal::WireFormatLite::EnumSize(
        this->repeated_import_enum(i));
    }
    total_size += (2UL * count) + data_size;
  }

  // repeated string repeated_string_piece = 54 [ctype = STRING_PIECE];
  total_size += 2 *
      ::google::protobuf::internal::FromIntSize(this->repeated_string_piece_size());
  for (int i = 0; i < this->repeated_string_piece_size(); i++) {
    total_size += ::google::protobuf::internal::WireFormatLite::StringSize(
      this->repeated_string_piece(i));
  }

  // repeated string repeated_cord = 55 [ctype = CORD];
  total_size += 2 *
      ::google::protobuf::internal::FromIntSize(this->repeated_cord_size());
  for (int i = 0; i < this->repeated_cord_size(); i++) {
    total_size += ::google::protobuf::internal::WireFormatLite::StringSize(
      this->repeated_cord(i));
  }

  // repeated .protobuf_unittest_no_arena.TestAllTypes.NestedMessage repeated_lazy_message = 57 [lazy = true];
  {
    unsigned int count = this->repeated_lazy_message_size();
    total_size += 2UL * count;
    for (unsigned int i = 0; i < count; i++) {
      total_size +=
        ::google::protobuf::internal::WireFormatLite::MessageSizeNoVirtual(
          this->repeated_lazy_message(i));
    }
  }

  switch (oneof_field_case()) {
    // optional uint32 oneof_uint32 = 111;
    case kOneofUint32: {
      total_size += 2 +
        ::google::protobuf::internal::WireFormatLite::UInt32Size(
          this->oneof_uint32());
      break;
    }
    // optional .protobuf_unittest_no_arena.TestAllTypes.NestedMessage oneof_nested_message = 112;
    case kOneofNestedMessage: {
      total_size += 2 +
        ::google::protobuf::internal::WireFormatLite::MessageSizeNoVirtual(
          *oneof_field_.oneof_nested_message_);
      break;
    }
    // optional string oneof_string = 113;
    case kOneofString: {
      total_size += 2 +
        ::google::protobuf::internal::WireFormatLite::StringSize(
          this->oneof_string());
      break;
    }
    // optional bytes oneof_bytes = 114;
    case kOneofBytes: {
      total_size += 2 +
        ::google::protobuf::internal::WireFormatLite::BytesSize(
          this->oneof_bytes());
      break;
    }
    // optional .protobuf_unittest_no_arena.TestAllTypes.NestedMessage lazy_oneof_nested_message = 115 [lazy = true];
    case kLazyOneofNestedMessage: {
      total_size += 2 +
        ::google::protobuf::internal::WireFormatLite::MessageSizeNoVirtual(
          *oneof_field_.lazy_oneof_nested_message_);
      break;
    }
    case ONEOF_FIELD_NOT_SET: {
      break;
    }
  }
  if (_internal_metadata_.have_unknown_fields()) {
    total_size +=
      ::google::protobuf::internal::WireFormat::ComputeUnknownFieldsSize(
        unknown_fields());
  }
  int cached_size = ::google::protobuf::internal::ToCachedSize(total_size);
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = cached_size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
  return total_size;
}

void TestAllTypes::MergeFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_merge_from_start:protobuf_unittest_no_arena.TestAllTypes)
  if (GOOGLE_PREDICT_FALSE(&from == this)) MergeFromFail(__LINE__);
  const TestAllTypes* source =
      ::google::protobuf::internal::DynamicCastToGenerated<const TestAllTypes>(
          &from);
  if (source == NULL) {
  // @@protoc_insertion_point(generalized_merge_from_cast_fail:protobuf_unittest_no_arena.TestAllTypes)
    ::google::protobuf::internal::ReflectionOps::Merge(from, this);
  } else {
  // @@protoc_insertion_point(generalized_merge_from_cast_success:protobuf_unittest_no_arena.TestAllTypes)
    UnsafeMergeFrom(*source);
  }
}

void TestAllTypes::MergeFrom(const TestAllTypes& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:protobuf_unittest_no_arena.TestAllTypes)
  if (GOOGLE_PREDICT_TRUE(&from != this)) {
    UnsafeMergeFrom(from);
  } else {
    MergeFromFail(__LINE__);
  }
}

void TestAllTypes::UnsafeMergeFrom(const TestAllTypes& from) {
  GOOGLE_DCHECK(&from != this);
  repeated_int32_.UnsafeMergeFrom(from.repeated_int32_);
  repeated_int64_.UnsafeMergeFrom(from.repeated_int64_);
  repeated_uint32_.UnsafeMergeFrom(from.repeated_uint32_);
  repeated_uint64_.UnsafeMergeFrom(from.repeated_uint64_);
  repeated_sint32_.UnsafeMergeFrom(from.repeated_sint32_);
  repeated_sint64_.UnsafeMergeFrom(from.repeated_sint64_);
  repeated_fixed32_.UnsafeMergeFrom(from.repeated_fixed32_);
  repeated_fixed64_.UnsafeMergeFrom(from.repeated_fixed64_);
  repeated_sfixed32_.UnsafeMergeFrom(from.repeated_sfixed32_);
  repeated_sfixed64_.UnsafeMergeFrom(from.repeated_sfixed64_);
  repeated_float_.UnsafeMergeFrom(from.repeated_float_);
  repeated_double_.UnsafeMergeFrom(from.repeated_double_);
  repeated_bool_.UnsafeMergeFrom(from.repeated_bool_);
  repeated_string_.UnsafeMergeFrom(from.repeated_string_);
  repeated_bytes_.UnsafeMergeFrom(from.repeated_bytes_);
  repeatedgroup_.MergeFrom(from.repeatedgroup_);
  repeated_nested_message_.MergeFrom(from.repeated_nested_message_);
  repeated_foreign_message_.MergeFrom(from.repeated_foreign_message_);
  repeated_import_message_.MergeFrom(from.repeated_import_message_);
  repeated_nested_enum_.UnsafeMergeFrom(from.repeated_nested_enum_);
  repeated_foreign_enum_.UnsafeMergeFrom(from.repeated_foreign_enum_);
  repeated_import_enum_.UnsafeMergeFrom(from.repeated_import_enum_);
  repeated_string_piece_.UnsafeMergeFrom(from.repeated_string_piece_);
  repeated_cord_.UnsafeMergeFrom(from.repeated_cord_);
  repeated_lazy_message_.MergeFrom(from.repeated_lazy_message_);
  switch (from.oneof_field_case()) {
    case kOneofUint32: {
      set_oneof_uint32(from.oneof_uint32());
      break;
    }
    case kOneofNestedMessage: {
      mutable_oneof_nested_message()->::protobuf_unittest_no_arena::TestAllTypes_NestedMessage::MergeFrom(from.oneof_nested_message());
      break;
    }
    case kOneofString: {
      set_oneof_string(from.oneof_string());
      break;
    }
    case kOneofBytes: {
      set_oneof_bytes(from.oneof_bytes());
      break;
    }
    case kLazyOneofNestedMessage: {
      mutable_lazy_oneof_nested_message()->::protobuf_unittest_no_arena::TestAllTypes_NestedMessage::MergeFrom(from.lazy_oneof_nested_message());
      break;
    }
    case ONEOF_FIELD_NOT_SET: {
      break;
    }
  }
  if (from._has_bits_[0 / 32] & (0xffu << (0 % 32))) {
    if (from.has_optional_int32()) {
      set_optional_int32(from.optional_int32());
    }
    if (from.has_optional_int64()) {
      set_optional_int64(from.optional_int64());
    }
    if (from.has_optional_uint32()) {
      set_optional_uint32(from.optional_uint32());
    }
    if (from.has_optional_uint64()) {
      set_optional_uint64(from.optional_uint64());
    }
    if (from.has_optional_sint32()) {
      set_optional_sint32(from.optional_sint32());
    }
    if (from.has_optional_sint64()) {
      set_optional_sint64(from.optional_sint64());
    }
    if (from.has_optional_fixed32()) {
      set_optional_fixed32(from.optional_fixed32());
    }
    if (from.has_optional_fixed64()) {
      set_optional_fixed64(from.optional_fixed64());
    }
  }
  if (from._has_bits_[8 / 32] & (0xffu << (8 % 32))) {
    if (from.has_optional_sfixed32()) {
      set_optional_sfixed32(from.optional_sfixed32());
    }
    if (from.has_optional_sfixed64()) {
      set_optional_sfixed64(from.optional_sfixed64());
    }
    if (from.has_optional_float()) {
      set_optional_float(from.optional_float());
    }
    if (from.has_optional_double()) {
      set_optional_double(from.optional_double());
    }
    if (from.has_optional_bool()) {
      set_optional_bool(from.optional_bool());
    }
    if (from.has_optional_string()) {
      set_has_optional_string();
      optional_string_.AssignWithDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), from.optional_string_);
    }
    if (from.has_optional_bytes()) {
      set_has_optional_bytes();
      optional_bytes_.AssignWithDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), from.optional_bytes_);
    }
    if (from.has_optionalgroup()) {
      mutable_optionalgroup()->::protobuf_unittest_no_arena::TestAllTypes_OptionalGroup::MergeFrom(from.optionalgroup());
    }
  }
  if (from._has_bits_[16 / 32] & (0xffu << (16 % 32))) {
    if (from.has_optional_nested_message()) {
      mutable_optional_nested_message()->::protobuf_unittest_no_arena::TestAllTypes_NestedMessage::MergeFrom(from.optional_nested_message());
    }
    if (from.has_optional_foreign_message()) {
      mutable_optional_foreign_message()->::protobuf_unittest_no_arena::ForeignMessage::MergeFrom(from.optional_foreign_message());
    }
    if (from.has_optional_import_message()) {
      mutable_optional_import_message()->::protobuf_unittest_import::ImportMessage::MergeFrom(from.optional_import_message());
    }
    if (from.has_optional_nested_enum()) {
      set_optional_nested_enum(from.optional_nested_enum());
    }
    if (from.has_optional_foreign_enum()) {
      set_optional_foreign_enum(from.optional_foreign_enum());
    }
    if (from.has_optional_import_enum()) {
      set_optional_import_enum(from.optional_import_enum());
    }
    if (from.has_optional_string_piece()) {
      set_has_optional_string_piece();
      optional_string_piece_.AssignWithDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), from.optional_string_piece_);
    }
    if (from.has_optional_cord()) {
      set_has_optional_cord();
      optional_cord_.AssignWithDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), from.optional_cord_);
    }
  }
  if (from._has_bits_[24 / 32] & (0xffu << (24 % 32))) {
    if (from.has_optional_public_import_message()) {
      mutable_optional_public_import_message()->::protobuf_unittest_import::PublicImportMessage::MergeFrom(from.optional_public_import_message());
    }
    if (from.has_optional_message()) {
      mutable_optional_message()->::protobuf_unittest_no_arena::TestAllTypes_NestedMessage::MergeFrom(from.optional_message());
    }
  }
  if (from._has_bits_[51 / 32] & (0xffu << (51 % 32))) {
    if (from.has_default_int32()) {
      set_default_int32(from.default_int32());
    }
    if (from.has_default_int64()) {
      set_default_int64(from.default_int64());
    }
    if (from.has_default_uint32()) {
      set_default_uint32(from.default_uint32());
    }
    if (from.has_default_uint64()) {
      set_default_uint64(from.default_uint64());
    }
    if (from.has_default_sint32()) {
      set_default_sint32(from.default_sint32());
    }
  }
  if (from._has_bits_[56 / 32] & (0xffu << (56 % 32))) {
    if (from.has_default_sint64()) {
      set_default_sint64(from.default_sint64());
    }
    if (from.has_default_fixed32()) {
      set_default_fixed32(from.default_fixed32());
    }
    if (from.has_default_fixed64()) {
      set_default_fixed64(from.default_fixed64());
    }
    if (from.has_default_sfixed32()) {
      set_default_sfixed32(from.default_sfixed32());
    }
    if (from.has_default_sfixed64()) {
      set_default_sfixed64(from.default_sfixed64());
    }
    if (from.has_default_float()) {
      set_default_float(from.default_float());
    }
    if (from.has_default_double()) {
      set_default_double(from.default_double());
    }
    if (from.has_default_bool()) {
      set_default_bool(from.default_bool());
    }
  }
  if (from._has_bits_[64 / 32] & (0xffu << (64 % 32))) {
    if (from.has_default_string()) {
      set_has_default_string();
      default_string_.AssignWithDefault(_default_default_string_, from.default_string_);
    }
    if (from.has_default_bytes()) {
      set_has_default_bytes();
      default_bytes_.AssignWithDefault(_default_default_bytes_, from.default_bytes_);
    }
    if (from.has_default_nested_enum()) {
      set_default_nested_enum(from.default_nested_enum());
    }
    if (from.has_default_foreign_enum()) {
      set_default_foreign_enum(from.default_foreign_enum());
    }
    if (from.has_default_import_enum()) {
      set_default_import_enum(from.default_import_enum());
    }
    if (from.has_default_string_piece()) {
      set_has_default_string_piece();
      default_string_piece_.AssignWithDefault(_default_default_string_piece_, from.default_string_piece_);
    }
    if (from.has_default_cord()) {
      set_has_default_cord();
      default_cord_.AssignWithDefault(_default_default_cord_, from.default_cord_);
    }
  }
  if (from._internal_metadata_.have_unknown_fields()) {
    ::google::protobuf::UnknownFieldSet::MergeToInternalMetdata(
      from.unknown_fields(), &_internal_metadata_);
  }
}

void TestAllTypes::CopyFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_copy_from_start:protobuf_unittest_no_arena.TestAllTypes)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

void TestAllTypes::CopyFrom(const TestAllTypes& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:protobuf_unittest_no_arena.TestAllTypes)
  if (&from == this) return;
  Clear();
  UnsafeMergeFrom(from);
}

bool TestAllTypes::IsInitialized() const {

  return true;
}

void TestAllTypes::Swap(TestAllTypes* other) {
  if (other == this) return;
  InternalSwap(other);
}
void TestAllTypes::InternalSwap(TestAllTypes* other) {
  std::swap(optional_int32_, other->optional_int32_);
  std::swap(optional_int64_, other->optional_int64_);
  std::swap(optional_uint32_, other->optional_uint32_);
  std::swap(optional_uint64_, other->optional_uint64_);
  std::swap(optional_sint32_, other->optional_sint32_);
  std::swap(optional_sint64_, other->optional_sint64_);
  std::swap(optional_fixed32_, other->optional_fixed32_);
  std::swap(optional_fixed64_, other->optional_fixed64_);
  std::swap(optional_sfixed32_, other->optional_sfixed32_);
  std::swap(optional_sfixed64_, other->optional_sfixed64_);
  std::swap(optional_float_, other->optional_float_);
  std::swap(optional_double_, other->optional_double_);
  std::swap(optional_bool_, other->optional_bool_);
  optional_string_.Swap(&other->optional_string_);
  optional_bytes_.Swap(&other->optional_bytes_);
  std::swap(optionalgroup_, other->optionalgroup_);
  std::swap(optional_nested_message_, other->optional_nested_message_);
  std::swap(optional_foreign_message_, other->optional_foreign_message_);
  std::swap(optional_import_message_, other->optional_import_message_);
  std::swap(optional_nested_enum_, other->optional_nested_enum_);
  std::swap(optional_foreign_enum_, other->optional_foreign_enum_);
  std::swap(optional_import_enum_, other->optional_import_enum_);
  optional_string_piece_.Swap(&other->optional_string_piece_);
  optional_cord_.Swap(&other->optional_cord_);
  std::swap(optional_public_import_message_, other->optional_public_import_message_);
  std::swap(optional_message_, other->optional_message_);
  repeated_int32_.UnsafeArenaSwap(&other->repeated_int32_);
  repeated_int64_.UnsafeArenaSwap(&other->repeated_int64_);
  repeated_uint32_.UnsafeArenaSwap(&other->repeated_uint32_);
  repeated_uint64_.UnsafeArenaSwap(&other->repeated_uint64_);
  repeated_sint32_.UnsafeArenaSwap(&other->repeated_sint32_);
  repeated_sint64_.UnsafeArenaSwap(&other->repeated_sint64_);
  repeated_fixed32_.UnsafeArenaSwap(&other->repeated_fixed32_);
  repeated_fixed64_.UnsafeArenaSwap(&other->repeated_fixed64_);
  repeated_sfixed32_.UnsafeArenaSwap(&other->repeated_sfixed32_);
  repeated_sfixed64_.UnsafeArenaSwap(&other->repeated_sfixed64_);
  repeated_float_.UnsafeArenaSwap(&other->repeated_float_);
  repeated_double_.UnsafeArenaSwap(&other->repeated_double_);
  repeated_bool_.UnsafeArenaSwap(&other->repeated_bool_);
  repeated_string_.UnsafeArenaSwap(&other->repeated_string_);
  repeated_bytes_.UnsafeArenaSwap(&other->repeated_bytes_);
  repeatedgroup_.UnsafeArenaSwap(&other->repeatedgroup_);
  repeated_nested_message_.UnsafeArenaSwap(&other->repeated_nested_message_);
  repeated_foreign_message_.UnsafeArenaSwap(&other->repeated_foreign_message_);
  repeated_import_message_.UnsafeArenaSwap(&other->repeated_import_message_);
  repeated_nested_enum_.UnsafeArenaSwap(&other->repeated_nested_enum_);
  repeated_foreign_enum_.UnsafeArenaSwap(&other->repeated_foreign_enum_);
  repeated_import_enum_.UnsafeArenaSwap(&other->repeated_import_enum_);
  repeated_string_piece_.UnsafeArenaSwap(&other->repeated_string_piece_);
  repeated_cord_.UnsafeArenaSwap(&other->repeated_cord_);
  repeated_lazy_message_.UnsafeArenaSwap(&other->repeated_lazy_message_);
  std::swap(default_int32_, other->default_int32_);
  std::swap(default_int64_, other->default_int64_);
  std::swap(default_uint32_, other->default_uint32_);
  std::swap(default_uint64_, other->default_uint64_);
  std::swap(default_sint32_, other->default_sint32_);
  std::swap(default_sint64_, other->default_sint64_);
  std::swap(default_fixed32_, other->default_fixed32_);
  std::swap(default_fixed64_, other->default_fixed64_);
  std::swap(default_sfixed32_, other->default_sfixed32_);
  std::swap(default_sfixed64_, other->default_sfixed64_);
  std::swap(default_float_, other->default_float_);
  std::swap(default_double_, other->default_double_);
  std::swap(default_bool_, other->default_bool_);
  default_string_.Swap(&other->default_string_);
  default_bytes_.Swap(&other->default_bytes_);
  std::swap(default_nested_enum_, other->default_nested_enum_);
  std::swap(default_foreign_enum_, other->default_foreign_enum_);
  std::swap(default_import_enum_, other->default_import_enum_);
  default_string_piece_.Swap(&other->default_string_piece_);
  default_cord_.Swap(&other->default_cord_);
  std::swap(oneof_field_, other->oneof_field_);
  std::swap(_oneof_case_[0], other->_oneof_case_[0]);
  std::swap(_has_bits_[0], other->_has_bits_[0]);
  std::swap(_has_bits_[1], other->_has_bits_[1]);
  std::swap(_has_bits_[2], other->_has_bits_[2]);
  _internal_metadata_.Swap(&other->_internal_metadata_);
  std::swap(_cached_size_, other->_cached_size_);
}

::google::protobuf::Metadata TestAllTypes::GetMetadata() const {
  protobuf_AssignDescriptorsOnce();
  ::google::protobuf::Metadata metadata;
  metadata.descriptor = TestAllTypes_descriptor_;
  metadata.reflection = TestAllTypes_reflection_;
  return metadata;
}

#if PROTOBUF_INLINE_NOT_IN_HEADERS
// TestAllTypes_NestedMessage

// optional int32 bb = 1;
bool TestAllTypes_NestedMessage::has_bb() const {
  return (_has_bits_[0] & 0x00000001u) != 0;
}
void TestAllTypes_NestedMessage::set_has_bb() {
  _has_bits_[0] |= 0x00000001u;
}
void TestAllTypes_NestedMessage::clear_has_bb() {
  _has_bits_[0] &= ~0x00000001u;
}
void TestAllTypes_NestedMessage::clear_bb() {
  bb_ = 0;
  clear_has_bb();
}
::google::protobuf::int32 TestAllTypes_NestedMessage::bb() const {
  // @@protoc_insertion_point(field_get:protobuf_unittest_no_arena.TestAllTypes.NestedMessage.bb)
  return bb_;
}
void TestAllTypes_NestedMessage::set_bb(::google::protobuf::int32 value) {
  set_has_bb();
  bb_ = value;
  // @@protoc_insertion_point(field_set:protobuf_unittest_no_arena.TestAllTypes.NestedMessage.bb)
}

inline const TestAllTypes_NestedMessage* TestAllTypes_NestedMessage::internal_default_instance() {
  return &TestAllTypes_NestedMessage_default_instance_.get();
}
// -------------------------------------------------------------------

// TestAllTypes_OptionalGroup

// optional int32 a = 17;
bool TestAllTypes_OptionalGroup::has_a() const {
  return (_has_bits_[0] & 0x00000001u) != 0;
}
void TestAllTypes_OptionalGroup::set_has_a() {
  _has_bits_[0] |= 0x00000001u;
}
void TestAllTypes_OptionalGroup::clear_has_a() {
  _has_bits_[0] &= ~0x00000001u;
}
void TestAllTypes_OptionalGroup::clear_a() {
  a_ = 0;
  clear_has_a();
}
::google::protobuf::int32 TestAllTypes_OptionalGroup::a() const {
  // @@protoc_insertion_point(field_get:protobuf_unittest_no_arena.TestAllTypes.OptionalGroup.a)
  return a_;
}
void TestAllTypes_OptionalGroup::set_a(::google::protobuf::int32 value) {
  set_has_a();
  a_ = value;
  // @@protoc_insertion_point(field_set:protobuf_unittest_no_arena.TestAllTypes.OptionalGroup.a)
}

inline const TestAllTypes_OptionalGroup* TestAllTypes_OptionalGroup::internal_default_instance() {
  return &TestAllTypes_OptionalGroup_default_instance_.get();
}
// -------------------------------------------------------------------

// TestAllTypes_RepeatedGroup

// optional int32 a = 47;
bool TestAllTypes_RepeatedGroup::has_a() const {
  return (_has_bits_[0] & 0x00000001u) != 0;
}
void TestAllTypes_RepeatedGroup::set_has_a() {
  _has_bits_[0] |= 0x00000001u;
}
void TestAllTypes_RepeatedGroup::clear_has_a() {
  _has_bits_[0] &= ~0x00000001u;
}
void TestAllTypes_RepeatedGroup::clear_a() {
  a_ = 0;
  clear_has_a();
}
::google::protobuf::int32 TestAllTypes_RepeatedGroup::a() const {
  // @@protoc_insertion_point(field_get:protobuf_unittest_no_arena.TestAllTypes.RepeatedGroup.a)
  return a_;
}
void TestAllTypes_RepeatedGroup::set_a(::google::protobuf::int32 value) {
  set_has_a();
  a_ = value;
  // @@protoc_insertion_point(field_set:protobuf_unittest_no_arena.TestAllTypes.RepeatedGroup.a)
}

inline const TestAllTypes_RepeatedGroup* TestAllTypes_RepeatedGroup::internal_default_instance() {
  return &TestAllTypes_RepeatedGroup_default_instance_.get();
}
// -------------------------------------------------------------------

// TestAllTypes

// optional int32 optional_int32 = 1;
bool TestAllTypes::has_optional_int32() const {
  return (_has_bits_[0] & 0x00000001u) != 0;
}
void TestAllTypes::set_has_optional_int32() {
  _has_bits_[0] |= 0x00000001u;
}
void TestAllTypes::clear_has_optional_int32() {
  _has_bits_[0] &= ~0x00000001u;
}
void TestAllTypes::clear_optional_int32() {
  optional_int32_ = 0;
  clear_has_optional_int32();
}
::google::protobuf::int32 TestAllTypes::optional_int32() const {
  // @@protoc_insertion_point(field_get:protobuf_unittest_no_arena.TestAllTypes.optional_int32)
  return optional_int32_;
}
void TestAllTypes::set_optional_int32(::google::protobuf::int32 value) {
  set_has_optional_int32();
  optional_int32_ = value;
  // @@protoc_insertion_point(field_set:protobuf_unittest_no_arena.TestAllTypes.optional_int32)
}

// optional int64 optional_int64 = 2;
bool TestAllTypes::has_optional_int64() const {
  return (_has_bits_[0] & 0x00000002u) != 0;
}
void TestAllTypes::set_has_optional_int64() {
  _has_bits_[0] |= 0x00000002u;
}
void TestAllTypes::clear_has_optional_int64() {
  _has_bits_[0] &= ~0x00000002u;
}
void TestAllTypes::clear_optional_int64() {
  optional_int64_ = GOOGLE_LONGLONG(0);
  clear_has_optional_int64();
}
::google::protobuf::int64 TestAllTypes::optional_int64() const {
  // @@protoc_insertion_point(field_get:protobuf_unittest_no_arena.TestAllTypes.optional_int64)
  return optional_int64_;
}
void TestAllTypes::set_optional_int64(::google::protobuf::int64 value) {
  set_has_optional_int64();
  optional_int64_ = value;
  // @@protoc_insertion_point(field_set:protobuf_unittest_no_arena.TestAllTypes.optional_int64)
}

// optional uint32 optional_uint32 = 3;
bool TestAllTypes::has_optional_uint32() const {
  return (_has_bits_[0] & 0x00000004u) != 0;
}
void TestAllTypes::set_has_optional_uint32() {
  _has_bits_[0] |= 0x00000004u;
}
void TestAllTypes::clear_has_optional_uint32() {
  _has_bits_[0] &= ~0x00000004u;
}
void TestAllTypes::clear_optional_uint32() {
  optional_uint32_ = 0u;
  clear_has_optional_uint32();
}
::google::protobuf::uint32 TestAllTypes::optional_uint32() const {
  // @@protoc_insertion_point(field_get:protobuf_unittest_no_arena.TestAllTypes.optional_uint32)
  return optional_uint32_;
}
void TestAllTypes::set_optional_uint32(::google::protobuf::uint32 value) {
  set_has_optional_uint32();
  optional_uint32_ = value;
  // @@protoc_insertion_point(field_set:protobuf_unittest_no_arena.TestAllTypes.optional_uint32)
}

// optional uint64 optional_uint64 = 4;
bool TestAllTypes::has_optional_uint64() const {
  return (_has_bits_[0] & 0x00000008u) != 0;
}
void TestAllTypes::set_has_optional_uint64() {
  _has_bits_[0] |= 0x00000008u;
}
void TestAllTypes::clear_has_optional_uint64() {
  _has_bits_[0] &= ~0x00000008u;
}
void TestAllTypes::clear_optional_uint64() {
  optional_uint64_ = GOOGLE_ULONGLONG(0);
  clear_has_optional_uint64();
}
::google::protobuf::uint64 TestAllTypes::optional_uint64() const {
  // @@protoc_insertion_point(field_get:protobuf_unittest_no_arena.TestAllTypes.optional_uint64)
  return optional_uint64_;
}
void TestAllTypes::set_optional_uint64(::google::protobuf::uint64 value) {
  set_has_optional_uint64();
  optional_uint64_ = value;
  // @@protoc_insertion_point(field_set:protobuf_unittest_no_arena.TestAllTypes.optional_uint64)
}

// optional sint32 optional_sint32 = 5;
bool TestAllTypes::has_optional_sint32() const {
  return (_has_bits_[0] & 0x00000010u) != 0;
}
void TestAllTypes::set_has_optional_sint32() {
  _has_bits_[0] |= 0x00000010u;
}
void TestAllTypes::clear_has_optional_sint32() {
  _has_bits_[0] &= ~0x00000010u;
}
void TestAllTypes::clear_optional_sint32() {
  optional_sint32_ = 0;
  clear_has_optional_sint32();
}
::google::protobuf::int32 TestAllTypes::optional_sint32() const {
  // @@protoc_insertion_point(field_get:protobuf_unittest_no_arena.TestAllTypes.optional_sint32)
  return optional_sint32_;
}
void TestAllTypes::set_optional_sint32(::google::protobuf::int32 value) {
  set_has_optional_sint32();
  optional_sint32_ = value;
  // @@protoc_insertion_point(field_set:protobuf_unittest_no_arena.TestAllTypes.optional_sint32)
}

// optional sint64 optional_sint64 = 6;
bool TestAllTypes::has_optional_sint64() const {
  return (_has_bits_[0] & 0x00000020u) != 0;
}
void TestAllTypes::set_has_optional_sint64() {
  _has_bits_[0] |= 0x00000020u;
}
void TestAllTypes::clear_has_optional_sint64() {
  _has_bits_[0] &= ~0x00000020u;
}
void TestAllTypes::clear_optional_sint64() {
  optional_sint64_ = GOOGLE_LONGLONG(0);
  clear_has_optional_sint64();
}
::google::protobuf::int64 TestAllTypes::optional_sint64() const {
  // @@protoc_insertion_point(field_get:protobuf_unittest_no_arena.TestAllTypes.optional_sint64)
  return optional_sint64_;
}
void TestAllTypes::set_optional_sint64(::google::protobuf::int64 value) {
  set_has_optional_sint64();
  optional_sint64_ = value;
  // @@protoc_insertion_point(field_set:protobuf_unittest_no_arena.TestAllTypes.optional_sint64)
}

// optional fixed32 optional_fixed32 = 7;
bool TestAllTypes::has_optional_fixed32() const {
  return (_has_bits_[0] & 0x00000040u) != 0;
}
void TestAllTypes::set_has_optional_fixed32() {
  _has_bits_[0] |= 0x00000040u;
}
void TestAllTypes::clear_has_optional_fixed32() {
  _has_bits_[0] &= ~0x00000040u;
}
void TestAllTypes::clear_optional_fixed32() {
  optional_fixed32_ = 0u;
  clear_has_optional_fixed32();
}
::google::protobuf::uint32 TestAllTypes::optional_fixed32() const {
  // @@protoc_insertion_point(field_get:protobuf_unittest_no_arena.TestAllTypes.optional_fixed32)
  return optional_fixed32_;
}
void TestAllTypes::set_optional_fixed32(::google::protobuf::uint32 value) {
  set_has_optional_fixed32();
  optional_fixed32_ = value;
  // @@protoc_insertion_point(field_set:protobuf_unittest_no_arena.TestAllTypes.optional_fixed32)
}

// optional fixed64 optional_fixed64 = 8;
bool TestAllTypes::has_optional_fixed64() const {
  return (_has_bits_[0] & 0x00000080u) != 0;
}
void TestAllTypes::set_has_optional_fixed64() {
  _has_bits_[0] |= 0x00000080u;
}
void TestAllTypes::clear_has_optional_fixed64() {
  _has_bits_[0] &= ~0x00000080u;
}
void TestAllTypes::clear_optional_fixed64() {
  optional_fixed64_ = GOOGLE_ULONGLONG(0);
  clear_has_optional_fixed64();
}
::google::protobuf::uint64 TestAllTypes::optional_fixed64() const {
  // @@protoc_insertion_point(field_get:protobuf_unittest_no_arena.TestAllTypes.optional_fixed64)
  return optional_fixed64_;
}
void TestAllTypes::set_optional_fixed64(::google::protobuf::uint64 value) {
  set_has_optional_fixed64();
  optional_fixed64_ = value;
  // @@protoc_insertion_point(field_set:protobuf_unittest_no_arena.TestAllTypes.optional_fixed64)
}

// optional sfixed32 optional_sfixed32 = 9;
bool TestAllTypes::has_optional_sfixed32() const {
  return (_has_bits_[0] & 0x00000100u) != 0;
}
void TestAllTypes::set_has_optional_sfixed32() {
  _has_bits_[0] |= 0x00000100u;
}
void TestAllTypes::clear_has_optional_sfixed32() {
  _has_bits_[0] &= ~0x00000100u;
}
void TestAllTypes::clear_optional_sfixed32() {
  optional_sfixed32_ = 0;
  clear_has_optional_sfixed32();
}
::google::protobuf::int32 TestAllTypes::optional_sfixed32() const {
  // @@protoc_insertion_point(field_get:protobuf_unittest_no_arena.TestAllTypes.optional_sfixed32)
  return optional_sfixed32_;
}
void TestAllTypes::set_optional_sfixed32(::google::protobuf::int32 value) {
  set_has_optional_sfixed32();
  optional_sfixed32_ = value;
  // @@protoc_insertion_point(field_set:protobuf_unittest_no_arena.TestAllTypes.optional_sfixed32)
}

// optional sfixed64 optional_sfixed64 = 10;
bool TestAllTypes::has_optional_sfixed64() const {
  return (_has_bits_[0] & 0x00000200u) != 0;
}
void TestAllTypes::set_has_optional_sfixed64() {
  _has_bits_[0] |= 0x00000200u;
}
void TestAllTypes::clear_has_optional_sfixed64() {
  _has_bits_[0] &= ~0x00000200u;
}
void TestAllTypes::clear_optional_sfixed64() {
  optional_sfixed64_ = GOOGLE_LONGLONG(0);
  clear_has_optional_sfixed64();
}
::google::protobuf::int64 TestAllTypes::optional_sfixed64() const {
  // @@protoc_insertion_point(field_get:protobuf_unittest_no_arena.TestAllTypes.optional_sfixed64)
  return optional_sfixed64_;
}
void TestAllTypes::set_optional_sfixed64(::google::protobuf::int64 value) {
  set_has_optional_sfixed64();
  optional_sfixed64_ = value;
  // @@protoc_insertion_point(field_set:protobuf_unittest_no_arena.TestAllTypes.optional_sfixed64)
}

// optional float optional_float = 11;
bool TestAllTypes::has_optional_float() const {
  return (_has_bits_[0] & 0x00000400u) != 0;
}
void TestAllTypes::set_has_optional_float() {
  _has_bits_[0] |= 0x00000400u;
}
void TestAllTypes::clear_has_optional_float() {
  _has_bits_[0] &= ~0x00000400u;
}
void TestAllTypes::clear_optional_float() {
  optional_float_ = 0;
  clear_has_optional_float();
}
float TestAllTypes::optional_float() const {
  // @@protoc_insertion_point(field_get:protobuf_unittest_no_arena.TestAllTypes.optional_float)
  return optional_float_;
}
void TestAllTypes::set_optional_float(float value) {
  set_has_optional_float();
  optional_float_ = value;
  // @@protoc_insertion_point(field_set:protobuf_unittest_no_arena.TestAllTypes.optional_float)
}

// optional double optional_double = 12;
bool TestAllTypes::has_optional_double() const {
  return (_has_bits_[0] & 0x00000800u) != 0;
}
void TestAllTypes::set_has_optional_double() {
  _has_bits_[0] |= 0x00000800u;
}
void TestAllTypes::clear_has_optional_double() {
  _has_bits_[0] &= ~0x00000800u;
}
void TestAllTypes::clear_optional_double() {
  optional_double_ = 0;
  clear_has_optional_double();
}
double TestAllTypes::optional_double() const {
  // @@protoc_insertion_point(field_get:protobuf_unittest_no_arena.TestAllTypes.optional_double)
  return optional_double_;
}
void TestAllTypes::set_optional_double(double value) {
  set_has_optional_double();
  optional_double_ = value;
  // @@protoc_insertion_point(field_set:protobuf_unittest_no_arena.TestAllTypes.optional_double)
}

// optional bool optional_bool = 13;
bool TestAllTypes::has_optional_bool() const {
  return (_has_bits_[0] & 0x00001000u) != 0;
}
void TestAllTypes::set_has_optional_bool() {
  _has_bits_[0] |= 0x00001000u;
}
void TestAllTypes::clear_has_optional_bool() {
  _has_bits_[0] &= ~0x00001000u;
}
void TestAllTypes::clear_optional_bool() {
  optional_bool_ = false;
  clear_has_optional_bool();
}
bool TestAllTypes::optional_bool() const {
  // @@protoc_insertion_point(field_get:protobuf_unittest_no_arena.TestAllTypes.optional_bool)
  return optional_bool_;
}
void TestAllTypes::set_optional_bool(bool value) {
  set_has_optional_bool();
  optional_bool_ = value;
  // @@protoc_insertion_point(field_set:protobuf_unittest_no_arena.TestAllTypes.optional_bool)
}

// optional string optional_string = 14;
bool TestAllTypes::has_optional_string() const {
  return (_has_bits_[0] & 0x00002000u) != 0;
}
void TestAllTypes::set_has_optional_string() {
  _has_bits_[0] |= 0x00002000u;
}
void TestAllTypes::clear_has_optional_string() {
  _has_bits_[0] &= ~0x00002000u;
}
void TestAllTypes::clear_optional_string() {
  optional_string_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  clear_has_optional_string();
}
const ::std::string& TestAllTypes::optional_string() const {
  // @@protoc_insertion_point(field_get:protobuf_unittest_no_arena.TestAllTypes.optional_string)
  return optional_string_.GetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
void TestAllTypes::set_optional_string(const ::std::string& value) {
  set_has_optional_string();
  optional_string_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:protobuf_unittest_no_arena.TestAllTypes.optional_string)
}
void TestAllTypes::set_optional_string(const char* value) {
  set_has_optional_string();
  optional_string_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:protobuf_unittest_no_arena.TestAllTypes.optional_string)
}
void TestAllTypes::set_optional_string(const char* value, size_t size) {
  set_has_optional_string();
  optional_string_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:protobuf_unittest_no_arena.TestAllTypes.optional_string)
}
::std::string* TestAllTypes::mutable_optional_string() {
  set_has_optional_string();
  // @@protoc_insertion_point(field_mutable:protobuf_unittest_no_arena.TestAllTypes.optional_string)
  return optional_string_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
::std::string* TestAllTypes::release_optional_string() {
  // @@protoc_insertion_point(field_release:protobuf_unittest_no_arena.TestAllTypes.optional_string)
  clear_has_optional_string();
  return optional_string_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
void TestAllTypes::set_allocated_optional_string(::std::string* optional_string) {
  if (optional_string != NULL) {
    set_has_optional_string();
  } else {
    clear_has_optional_string();
  }
  optional_string_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), optional_string);
  // @@protoc_insertion_point(field_set_allocated:protobuf_unittest_no_arena.TestAllTypes.optional_string)
}

// optional bytes optional_bytes = 15;
bool TestAllTypes::has_optional_bytes() const {
  return (_has_bits_[0] & 0x00004000u) != 0;
}
void TestAllTypes::set_has_optional_bytes() {
  _has_bits_[0] |= 0x00004000u;
}
void TestAllTypes::clear_has_optional_bytes() {
  _has_bits_[0] &= ~0x00004000u;
}
void TestAllTypes::clear_optional_bytes() {
  optional_bytes_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  clear_has_optional_bytes();
}
const ::std::string& TestAllTypes::optional_bytes() const {
  // @@protoc_insertion_point(field_get:protobuf_unittest_no_arena.TestAllTypes.optional_bytes)
  return optional_bytes_.GetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
void TestAllTypes::set_optional_bytes(const ::std::string& value) {
  set_has_optional_bytes();
  optional_bytes_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:protobuf_unittest_no_arena.TestAllTypes.optional_bytes)
}
void TestAllTypes::set_optional_bytes(const char* value) {
  set_has_optional_bytes();
  optional_bytes_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:protobuf_unittest_no_arena.TestAllTypes.optional_bytes)
}
void TestAllTypes::set_optional_bytes(const void* value, size_t size) {
  set_has_optional_bytes();
  optional_bytes_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:protobuf_unittest_no_arena.TestAllTypes.optional_bytes)
}
::std::string* TestAllTypes::mutable_optional_bytes() {
  set_has_optional_bytes();
  // @@protoc_insertion_point(field_mutable:protobuf_unittest_no_arena.TestAllTypes.optional_bytes)
  return optional_bytes_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
::std::string* TestAllTypes::release_optional_bytes() {
  // @@protoc_insertion_point(field_release:protobuf_unittest_no_arena.TestAllTypes.optional_bytes)
  clear_has_optional_bytes();
  return optional_bytes_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
void TestAllTypes::set_allocated_optional_bytes(::std::string* optional_bytes) {
  if (optional_bytes != NULL) {
    set_has_optional_bytes();
  } else {
    clear_has_optional_bytes();
  }
  optional_bytes_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), optional_bytes);
  // @@protoc_insertion_point(field_set_allocated:protobuf_unittest_no_arena.TestAllTypes.optional_bytes)
}

// optional group OptionalGroup = 16 { ... };
bool TestAllTypes::has_optionalgroup() const {
  return (_has_bits_[0] & 0x00008000u) != 0;
}
void TestAllTypes::set_has_optionalgroup() {
  _has_bits_[0] |= 0x00008000u;
}
void TestAllTypes::clear_has_optionalgroup() {
  _has_bits_[0] &= ~0x00008000u;
}
void TestAllTypes::clear_optionalgroup() {
  if (optionalgroup_ != NULL) optionalgroup_->::protobuf_unittest_no_arena::TestAllTypes_OptionalGroup::Clear();
  clear_has_optionalgroup();
}
const ::protobuf_unittest_no_arena::TestAllTypes_OptionalGroup& TestAllTypes::optionalgroup() const {
  // @@protoc_insertion_point(field_get:protobuf_unittest_no_arena.TestAllTypes.optionalgroup)
  return optionalgroup_ != NULL ? *optionalgroup_
                         : *::protobuf_unittest_no_arena::TestAllTypes_OptionalGroup::internal_default_instance();
}
::protobuf_unittest_no_arena::TestAllTypes_OptionalGroup* TestAllTypes::mutable_optionalgroup() {
  set_has_optionalgroup();
  if (optionalgroup_ == NULL) {
    optionalgroup_ = new ::protobuf_unittest_no_arena::TestAllTypes_OptionalGroup;
  }
  // @@protoc_insertion_point(field_mutable:protobuf_unittest_no_arena.TestAllTypes.optionalgroup)
  return optionalgroup_;
}
::protobuf_unittest_no_arena::TestAllTypes_OptionalGroup* TestAllTypes::release_optionalgroup() {
  // @@protoc_insertion_point(field_release:protobuf_unittest_no_arena.TestAllTypes.optionalgroup)
  clear_has_optionalgroup();
  ::protobuf_unittest_no_arena::TestAllTypes_OptionalGroup* temp = optionalgroup_;
  optionalgroup_ = NULL;
  return temp;
}
void TestAllTypes::set_allocated_optionalgroup(::protobuf_unittest_no_arena::TestAllTypes_OptionalGroup* optionalgroup) {
  delete optionalgroup_;
  optionalgroup_ = optionalgroup;
  if (optionalgroup) {
    set_has_optionalgroup();
  } else {
    clear_has_optionalgroup();
  }
  // @@protoc_insertion_point(field_set_allocated:protobuf_unittest_no_arena.TestAllTypes.optionalgroup)
}

// optional .protobuf_unittest_no_arena.TestAllTypes.NestedMessage optional_nested_message = 18;
bool TestAllTypes::has_optional_nested_message() const {
  return (_has_bits_[0] & 0x00010000u) != 0;
}
void TestAllTypes::set_has_optional_nested_message() {
  _has_bits_[0] |= 0x00010000u;
}
void TestAllTypes::clear_has_optional_nested_message() {
  _has_bits_[0] &= ~0x00010000u;
}
void TestAllTypes::clear_optional_nested_message() {
  if (optional_nested_message_ != NULL) optional_nested_message_->::protobuf_unittest_no_arena::TestAllTypes_NestedMessage::Clear();
  clear_has_optional_nested_message();
}
const ::protobuf_unittest_no_arena::TestAllTypes_NestedMessage& TestAllTypes::optional_nested_message() const {
  // @@protoc_insertion_point(field_get:protobuf_unittest_no_arena.TestAllTypes.optional_nested_message)
  return optional_nested_message_ != NULL ? *optional_nested_message_
                         : *::protobuf_unittest_no_arena::TestAllTypes_NestedMessage::internal_default_instance();
}
::protobuf_unittest_no_arena::TestAllTypes_NestedMessage* TestAllTypes::mutable_optional_nested_message() {
  set_has_optional_nested_message();
  if (optional_nested_message_ == NULL) {
    optional_nested_message_ = new ::protobuf_unittest_no_arena::TestAllTypes_NestedMessage;
  }
  // @@protoc_insertion_point(field_mutable:protobuf_unittest_no_arena.TestAllTypes.optional_nested_message)
  return optional_nested_message_;
}
::protobuf_unittest_no_arena::TestAllTypes_NestedMessage* TestAllTypes::release_optional_nested_message() {
  // @@protoc_insertion_point(field_release:protobuf_unittest_no_arena.TestAllTypes.optional_nested_message)
  clear_has_optional_nested_message();
  ::protobuf_unittest_no_arena::TestAllTypes_NestedMessage* temp = optional_nested_message_;
  optional_nested_message_ = NULL;
  return temp;
}
void TestAllTypes::set_allocated_optional_nested_message(::protobuf_unittest_no_arena::TestAllTypes_NestedMessage* optional_nested_message) {
  delete optional_nested_message_;
  optional_nested_message_ = optional_nested_message;
  if (optional_nested_message) {
    set_has_optional_nested_message();
  } else {
    clear_has_optional_nested_message();
  }
  // @@protoc_insertion_point(field_set_allocated:protobuf_unittest_no_arena.TestAllTypes.optional_nested_message)
}

// optional .protobuf_unittest_no_arena.ForeignMessage optional_foreign_message = 19;
bool TestAllTypes::has_optional_foreign_message() const {
  return (_has_bits_[0] & 0x00020000u) != 0;
}
void TestAllTypes::set_has_optional_foreign_message() {
  _has_bits_[0] |= 0x00020000u;
}
void TestAllTypes::clear_has_optional_foreign_message() {
  _has_bits_[0] &= ~0x00020000u;
}
void TestAllTypes::clear_optional_foreign_message() {
  if (optional_foreign_message_ != NULL) optional_foreign_message_->::protobuf_unittest_no_arena::ForeignMessage::Clear();
  clear_has_optional_foreign_message();
}
const ::protobuf_unittest_no_arena::ForeignMessage& TestAllTypes::optional_foreign_message() const {
  // @@protoc_insertion_point(field_get:protobuf_unittest_no_arena.TestAllTypes.optional_foreign_message)
  return optional_foreign_message_ != NULL ? *optional_foreign_message_
                         : *::protobuf_unittest_no_arena::ForeignMessage::internal_default_instance();
}
::protobuf_unittest_no_arena::ForeignMessage* TestAllTypes::mutable_optional_foreign_message() {
  set_has_optional_foreign_message();
  if (optional_foreign_message_ == NULL) {
    optional_foreign_message_ = new ::protobuf_unittest_no_arena::ForeignMessage;
  }
  // @@protoc_insertion_point(field_mutable:protobuf_unittest_no_arena.TestAllTypes.optional_foreign_message)
  return optional_foreign_message_;
}
::protobuf_unittest_no_arena::ForeignMessage* TestAllTypes::release_optional_foreign_message() {
  // @@protoc_insertion_point(field_release:protobuf_unittest_no_arena.TestAllTypes.optional_foreign_message)
  clear_has_optional_foreign_message();
  ::protobuf_unittest_no_arena::ForeignMessage* temp = optional_foreign_message_;
  optional_foreign_message_ = NULL;
  return temp;
}
void TestAllTypes::set_allocated_optional_foreign_message(::protobuf_unittest_no_arena::ForeignMessage* optional_foreign_message) {
  delete optional_foreign_message_;
  optional_foreign_message_ = optional_foreign_message;
  if (optional_foreign_message) {
    set_has_optional_foreign_message();
  } else {
    clear_has_optional_foreign_message();
  }
  // @@protoc_insertion_point(field_set_allocated:protobuf_unittest_no_arena.TestAllTypes.optional_foreign_message)
}

// optional .protobuf_unittest_import.ImportMessage optional_import_message = 20;
bool TestAllTypes::has_optional_import_message() const {
  return (_has_bits_[0] & 0x00040000u) != 0;
}
void TestAllTypes::set_has_optional_import_message() {
  _has_bits_[0] |= 0x00040000u;
}
void TestAllTypes::clear_has_optional_import_message() {
  _has_bits_[0] &= ~0x00040000u;
}
void TestAllTypes::clear_optional_import_message() {
  if (optional_import_message_ != NULL) optional_import_message_->::protobuf_unittest_import::ImportMessage::Clear();
  clear_has_optional_import_message();
}
const ::protobuf_unittest_import::ImportMessage& TestAllTypes::optional_import_message() const {
  // @@protoc_insertion_point(field_get:protobuf_unittest_no_arena.TestAllTypes.optional_import_message)
  return optional_import_message_ != NULL ? *optional_import_message_
                         : *::protobuf_unittest_import::ImportMessage::internal_default_instance();
}
::protobuf_unittest_import::ImportMessage* TestAllTypes::mutable_optional_import_message() {
  set_has_optional_import_message();
  if (optional_import_message_ == NULL) {
    optional_import_message_ = new ::protobuf_unittest_import::ImportMessage;
  }
  // @@protoc_insertion_point(field_mutable:protobuf_unittest_no_arena.TestAllTypes.optional_import_message)
  return optional_import_message_;
}
::protobuf_unittest_import::ImportMessage* TestAllTypes::release_optional_import_message() {
  // @@protoc_insertion_point(field_release:protobuf_unittest_no_arena.TestAllTypes.optional_import_message)
  clear_has_optional_import_message();
  ::protobuf_unittest_import::ImportMessage* temp = optional_import_message_;
  optional_import_message_ = NULL;
  return temp;
}
void TestAllTypes::set_allocated_optional_import_message(::protobuf_unittest_import::ImportMessage* optional_import_message) {
  delete optional_import_message_;
  if (optional_import_message != NULL && optional_import_message->GetArena() != NULL) {
    ::protobuf_unittest_import::ImportMessage* new_optional_import_message = new ::protobuf_unittest_import::ImportMessage;
    new_optional_import_message->CopyFrom(*optional_import_message);
    optional_import_message = new_optional_import_message;
  }
  optional_import_message_ = optional_import_message;
  if (optional_import_message) {
    set_has_optional_import_message();
  } else {
    clear_has_optional_import_message();
  }
  // @@protoc_insertion_point(field_set_allocated:protobuf_unittest_no_arena.TestAllTypes.optional_import_message)
}

// optional .protobuf_unittest_no_arena.TestAllTypes.NestedEnum optional_nested_enum = 21;
bool TestAllTypes::has_optional_nested_enum() const {
  return (_has_bits_[0] & 0x00080000u) != 0;
}
void TestAllTypes::set_has_optional_nested_enum() {
  _has_bits_[0] |= 0x00080000u;
}
void TestAllTypes::clear_has_optional_nested_enum() {
  _has_bits_[0] &= ~0x00080000u;
}
void TestAllTypes::clear_optional_nested_enum() {
  optional_nested_enum_ = 1;
  clear_has_optional_nested_enum();
}
::protobuf_unittest_no_arena::TestAllTypes_NestedEnum TestAllTypes::optional_nested_enum() const {
  // @@protoc_insertion_point(field_get:protobuf_unittest_no_arena.TestAllTypes.optional_nested_enum)
  return static_cast< ::protobuf_unittest_no_arena::TestAllTypes_NestedEnum >(optional_nested_enum_);
}
void TestAllTypes::set_optional_nested_enum(::protobuf_unittest_no_arena::TestAllTypes_NestedEnum value) {
  assert(::protobuf_unittest_no_arena::TestAllTypes_NestedEnum_IsValid(value));
  set_has_optional_nested_enum();
  optional_nested_enum_ = value;
  // @@protoc_insertion_point(field_set:protobuf_unittest_no_arena.TestAllTypes.optional_nested_enum)
}

// optional .protobuf_unittest_no_arena.ForeignEnum optional_foreign_enum = 22;
bool TestAllTypes::has_optional_foreign_enum() const {
  return (_has_bits_[0] & 0x00100000u) != 0;
}
void TestAllTypes::set_has_optional_foreign_enum() {
  _has_bits_[0] |= 0x00100000u;
}
void TestAllTypes::clear_has_optional_foreign_enum() {
  _has_bits_[0] &= ~0x00100000u;
}
void TestAllTypes::clear_optional_foreign_enum() {
  optional_foreign_enum_ = 4;
  clear_has_optional_foreign_enum();
}
::protobuf_unittest_no_arena::ForeignEnum TestAllTypes::optional_foreign_enum() const {
  // @@protoc_insertion_point(field_get:protobuf_unittest_no_arena.TestAllTypes.optional_foreign_enum)
  return static_cast< ::protobuf_unittest_no_arena::ForeignEnum >(optional_foreign_enum_);
}
void TestAllTypes::set_optional_foreign_enum(::protobuf_unittest_no_arena::ForeignEnum value) {
  assert(::protobuf_unittest_no_arena::ForeignEnum_IsValid(value));
  set_has_optional_foreign_enum();
  optional_foreign_enum_ = value;
  // @@protoc_insertion_point(field_set:protobuf_unittest_no_arena.TestAllTypes.optional_foreign_enum)
}

// optional .protobuf_unittest_import.ImportEnum optional_import_enum = 23;
bool TestAllTypes::has_optional_import_enum() const {
  return (_has_bits_[0] & 0x00200000u) != 0;
}
void TestAllTypes::set_has_optional_import_enum() {
  _has_bits_[0] |= 0x00200000u;
}
void TestAllTypes::clear_has_optional_import_enum() {
  _has_bits_[0] &= ~0x00200000u;
}
void TestAllTypes::clear_optional_import_enum() {
  optional_import_enum_ = 7;
  clear_has_optional_import_enum();
}
::protobuf_unittest_import::ImportEnum TestAllTypes::optional_import_enum() const {
  // @@protoc_insertion_point(field_get:protobuf_unittest_no_arena.TestAllTypes.optional_import_enum)
  return static_cast< ::protobuf_unittest_import::ImportEnum >(optional_import_enum_);
}
void TestAllTypes::set_optional_import_enum(::protobuf_unittest_import::ImportEnum value) {
  assert(::protobuf_unittest_import::ImportEnum_IsValid(value));
  set_has_optional_import_enum();
  optional_import_enum_ = value;
  // @@protoc_insertion_point(field_set:protobuf_unittest_no_arena.TestAllTypes.optional_import_enum)
}

// optional string optional_string_piece = 24 [ctype = STRING_PIECE];
bool TestAllTypes::has_optional_string_piece() const {
  return (_has_bits_[0] & 0x00400000u) != 0;
}
void TestAllTypes::set_has_optional_string_piece() {
  _has_bits_[0] |= 0x00400000u;
}
void TestAllTypes::clear_has_optional_string_piece() {
  _has_bits_[0] &= ~0x00400000u;
}
void TestAllTypes::clear_optional_string_piece() {
  optional_string_piece_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  clear_has_optional_string_piece();
}
const ::std::string& TestAllTypes::optional_string_piece() const {
  // @@protoc_insertion_point(field_get:protobuf_unittest_no_arena.TestAllTypes.optional_string_piece)
  return optional_string_piece_.GetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
void TestAllTypes::set_optional_string_piece(const ::std::string& value) {
  set_has_optional_string_piece();
  optional_string_piece_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:protobuf_unittest_no_arena.TestAllTypes.optional_string_piece)
}
void TestAllTypes::set_optional_string_piece(const char* value) {
  set_has_optional_string_piece();
  optional_string_piece_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:protobuf_unittest_no_arena.TestAllTypes.optional_string_piece)
}
void TestAllTypes::set_optional_string_piece(const char* value, size_t size) {
  set_has_optional_string_piece();
  optional_string_piece_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:protobuf_unittest_no_arena.TestAllTypes.optional_string_piece)
}
::std::string* TestAllTypes::mutable_optional_string_piece() {
  set_has_optional_string_piece();
  // @@protoc_insertion_point(field_mutable:protobuf_unittest_no_arena.TestAllTypes.optional_string_piece)
  return optional_string_piece_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
::std::string* TestAllTypes::release_optional_string_piece() {
  // @@protoc_insertion_point(field_release:protobuf_unittest_no_arena.TestAllTypes.optional_string_piece)
  clear_has_optional_string_piece();
  return optional_string_piece_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
void TestAllTypes::set_allocated_optional_string_piece(::std::string* optional_string_piece) {
  if (optional_string_piece != NULL) {
    set_has_optional_string_piece();
  } else {
    clear_has_optional_string_piece();
  }
  optional_string_piece_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), optional_string_piece);
  // @@protoc_insertion_point(field_set_allocated:protobuf_unittest_no_arena.TestAllTypes.optional_string_piece)
}

// optional string optional_cord = 25 [ctype = CORD];
bool TestAllTypes::has_optional_cord() const {
  return (_has_bits_[0] & 0x00800000u) != 0;
}
void TestAllTypes::set_has_optional_cord() {
  _has_bits_[0] |= 0x00800000u;
}
void TestAllTypes::clear_has_optional_cord() {
  _has_bits_[0] &= ~0x00800000u;
}
void TestAllTypes::clear_optional_cord() {
  optional_cord_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  clear_has_optional_cord();
}
const ::std::string& TestAllTypes::optional_cord() const {
  // @@protoc_insertion_point(field_get:protobuf_unittest_no_arena.TestAllTypes.optional_cord)
  return optional_cord_.GetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
void TestAllTypes::set_optional_cord(const ::std::string& value) {
  set_has_optional_cord();
  optional_cord_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:protobuf_unittest_no_arena.TestAllTypes.optional_cord)
}
void TestAllTypes::set_optional_cord(const char* value) {
  set_has_optional_cord();
  optional_cord_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:protobuf_unittest_no_arena.TestAllTypes.optional_cord)
}
void TestAllTypes::set_optional_cord(const char* value, size_t size) {
  set_has_optional_cord();
  optional_cord_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:protobuf_unittest_no_arena.TestAllTypes.optional_cord)
}
::std::string* TestAllTypes::mutable_optional_cord() {
  set_has_optional_cord();
  // @@protoc_insertion_point(field_mutable:protobuf_unittest_no_arena.TestAllTypes.optional_cord)
  return optional_cord_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
::std::string* TestAllTypes::release_optional_cord() {
  // @@protoc_insertion_point(field_release:protobuf_unittest_no_arena.TestAllTypes.optional_cord)
  clear_has_optional_cord();
  return optional_cord_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
void TestAllTypes::set_allocated_optional_cord(::std::string* optional_cord) {
  if (optional_cord != NULL) {
    set_has_optional_cord();
  } else {
    clear_has_optional_cord();
  }
  optional_cord_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), optional_cord);
  // @@protoc_insertion_point(field_set_allocated:protobuf_unittest_no_arena.TestAllTypes.optional_cord)
}

// optional .protobuf_unittest_import.PublicImportMessage optional_public_import_message = 26;
bool TestAllTypes::has_optional_public_import_message() const {
  return (_has_bits_[0] & 0x01000000u) != 0;
}
void TestAllTypes::set_has_optional_public_import_message() {
  _has_bits_[0] |= 0x01000000u;
}
void TestAllTypes::clear_has_optional_public_import_message() {
  _has_bits_[0] &= ~0x01000000u;
}
void TestAllTypes::clear_optional_public_import_message() {
  if (optional_public_import_message_ != NULL) optional_public_import_message_->::protobuf_unittest_import::PublicImportMessage::Clear();
  clear_has_optional_public_import_message();
}
const ::protobuf_unittest_import::PublicImportMessage& TestAllTypes::optional_public_import_message() const {
  // @@protoc_insertion_point(field_get:protobuf_unittest_no_arena.TestAllTypes.optional_public_import_message)
  return optional_public_import_message_ != NULL ? *optional_public_import_message_
                         : *::protobuf_unittest_import::PublicImportMessage::internal_default_instance();
}
::protobuf_unittest_import::PublicImportMessage* TestAllTypes::mutable_optional_public_import_message() {
  set_has_optional_public_import_message();
  if (optional_public_import_message_ == NULL) {
    optional_public_import_message_ = new ::protobuf_unittest_import::PublicImportMessage;
  }
  // @@protoc_insertion_point(field_mutable:protobuf_unittest_no_arena.TestAllTypes.optional_public_import_message)
  return optional_public_import_message_;
}
::protobuf_unittest_import::PublicImportMessage* TestAllTypes::release_optional_public_import_message() {
  // @@protoc_insertion_point(field_release:protobuf_unittest_no_arena.TestAllTypes.optional_public_import_message)
  clear_has_optional_public_import_message();
  ::protobuf_unittest_import::PublicImportMessage* temp = optional_public_import_message_;
  optional_public_import_message_ = NULL;
  return temp;
}
void TestAllTypes::set_allocated_optional_public_import_message(::protobuf_unittest_import::PublicImportMessage* optional_public_import_message) {
  delete optional_public_import_message_;
  optional_public_import_message_ = optional_public_import_message;
  if (optional_public_import_message) {
    set_has_optional_public_import_message();
  } else {
    clear_has_optional_public_import_message();
  }
  // @@protoc_insertion_point(field_set_allocated:protobuf_unittest_no_arena.TestAllTypes.optional_public_import_message)
}

// optional .protobuf_unittest_no_arena.TestAllTypes.NestedMessage optional_message = 27 [lazy = true];
bool TestAllTypes::has_optional_message() const {
  return (_has_bits_[0] & 0x02000000u) != 0;
}
void TestAllTypes::set_has_optional_message() {
  _has_bits_[0] |= 0x02000000u;
}
void TestAllTypes::clear_has_optional_message() {
  _has_bits_[0] &= ~0x02000000u;
}
void TestAllTypes::clear_optional_message() {
  if (optional_message_ != NULL) optional_message_->::protobuf_unittest_no_arena::TestAllTypes_NestedMessage::Clear();
  clear_has_optional_message();
}
const ::protobuf_unittest_no_arena::TestAllTypes_NestedMessage& TestAllTypes::optional_message() const {
  // @@protoc_insertion_point(field_get:protobuf_unittest_no_arena.TestAllTypes.optional_message)
  return optional_message_ != NULL ? *optional_message_
                         : *::protobuf_unittest_no_arena::TestAllTypes_NestedMessage::internal_default_instance();
}
::protobuf_unittest_no_arena::TestAllTypes_NestedMessage* TestAllTypes::mutable_optional_message() {
  set_has_optional_message();
  if (optional_message_ == NULL) {
    optional_message_ = new ::protobuf_unittest_no_arena::TestAllTypes_NestedMessage;
  }
  // @@protoc_insertion_point(field_mutable:protobuf_unittest_no_arena.TestAllTypes.optional_message)
  return optional_message_;
}
::protobuf_unittest_no_arena::TestAllTypes_NestedMessage* TestAllTypes::release_optional_message() {
  // @@protoc_insertion_point(field_release:protobuf_unittest_no_arena.TestAllTypes.optional_message)
  clear_has_optional_message();
  ::protobuf_unittest_no_arena::TestAllTypes_NestedMessage* temp = optional_message_;
  optional_message_ = NULL;
  return temp;
}
void TestAllTypes::set_allocated_optional_message(::protobuf_unittest_no_arena::TestAllTypes_NestedMessage* optional_message) {
  delete optional_message_;
  optional_message_ = optional_message;
  if (optional_message) {
    set_has_optional_message();
  } else {
    clear_has_optional_message();
  }
  // @@protoc_insertion_point(field_set_allocated:protobuf_unittest_no_arena.TestAllTypes.optional_message)
}

// repeated int32 repeated_int32 = 31;
int TestAllTypes::repeated_int32_size() const {
  return repeated_int32_.size();
}
void TestAllTypes::clear_repeated_int32() {
  repeated_int32_.Clear();
}
::google::protobuf::int32 TestAllTypes::repeated_int32(int index) const {
  // @@protoc_insertion_point(field_get:protobuf_unittest_no_arena.TestAllTypes.repeated_int32)
  return repeated_int32_.Get(index);
}
void TestAllTypes::set_repeated_int32(int index, ::google::protobuf::int32 value) {
  repeated_int32_.Set(index, value);
  // @@protoc_insertion_point(field_set:protobuf_unittest_no_arena.TestAllTypes.repeated_int32)
}
void TestAllTypes::add_repeated_int32(::google::protobuf::int32 value) {
  repeated_int32_.Add(value);
  // @@protoc_insertion_point(field_add:protobuf_unittest_no_arena.TestAllTypes.repeated_int32)
}
const ::google::protobuf::RepeatedField< ::google::protobuf::int32 >&
TestAllTypes::repeated_int32() const {
  // @@protoc_insertion_point(field_list:protobuf_unittest_no_arena.TestAllTypes.repeated_int32)
  return repeated_int32_;
}
::google::protobuf::RepeatedField< ::google::protobuf::int32 >*
TestAllTypes::mutable_repeated_int32() {
  // @@protoc_insertion_point(field_mutable_list:protobuf_unittest_no_arena.TestAllTypes.repeated_int32)
  return &repeated_int32_;
}

// repeated int64 repeated_int64 = 32;
int TestAllTypes::repeated_int64_size() const {
  return repeated_int64_.size();
}
void TestAllTypes::clear_repeated_int64() {
  repeated_int64_.Clear();
}
::google::protobuf::int64 TestAllTypes::repeated_int64(int index) const {
  // @@protoc_insertion_point(field_get:protobuf_unittest_no_arena.TestAllTypes.repeated_int64)
  return repeated_int64_.Get(index);
}
void TestAllTypes::set_repeated_int64(int index, ::google::protobuf::int64 value) {
  repeated_int64_.Set(index, value);
  // @@protoc_insertion_point(field_set:protobuf_unittest_no_arena.TestAllTypes.repeated_int64)
}
void TestAllTypes::add_repeated_int64(::google::protobuf::int64 value) {
  repeated_int64_.Add(value);
  // @@protoc_insertion_point(field_add:protobuf_unittest_no_arena.TestAllTypes.repeated_int64)
}
const ::google::protobuf::RepeatedField< ::google::protobuf::int64 >&
TestAllTypes::repeated_int64() const {
  // @@protoc_insertion_point(field_list:protobuf_unittest_no_arena.TestAllTypes.repeated_int64)
  return repeated_int64_;
}
::google::protobuf::RepeatedField< ::google::protobuf::int64 >*
TestAllTypes::mutable_repeated_int64() {
  // @@protoc_insertion_point(field_mutable_list:protobuf_unittest_no_arena.TestAllTypes.repeated_int64)
  return &repeated_int64_;
}

// repeated uint32 repeated_uint32 = 33;
int TestAllTypes::repeated_uint32_size() const {
  return repeated_uint32_.size();
}
void TestAllTypes::clear_repeated_uint32() {
  repeated_uint32_.Clear();
}
::google::protobuf::uint32 TestAllTypes::repeated_uint32(int index) const {
  // @@protoc_insertion_point(field_get:protobuf_unittest_no_arena.TestAllTypes.repeated_uint32)
  return repeated_uint32_.Get(index);
}
void TestAllTypes::set_repeated_uint32(int index, ::google::protobuf::uint32 value) {
  repeated_uint32_.Set(index, value);
  // @@protoc_insertion_point(field_set:protobuf_unittest_no_arena.TestAllTypes.repeated_uint32)
}
void TestAllTypes::add_repeated_uint32(::google::protobuf::uint32 value) {
  repeated_uint32_.Add(value);
  // @@protoc_insertion_point(field_add:protobuf_unittest_no_arena.TestAllTypes.repeated_uint32)
}
const ::google::protobuf::RepeatedField< ::google::protobuf::uint32 >&
TestAllTypes::repeated_uint32() const {
  // @@protoc_insertion_point(field_list:protobuf_unittest_no_arena.TestAllTypes.repeated_uint32)
  return repeated_uint32_;
}
::google::protobuf::RepeatedField< ::google::protobuf::uint32 >*
TestAllTypes::mutable_repeated_uint32() {
  // @@protoc_insertion_point(field_mutable_list:protobuf_unittest_no_arena.TestAllTypes.repeated_uint32)
  return &repeated_uint32_;
}

// repeated uint64 repeated_uint64 = 34;
int TestAllTypes::repeated_uint64_size() const {
  return repeated_uint64_.size();
}
void TestAllTypes::clear_repeated_uint64() {
  repeated_uint64_.Clear();
}
::google::protobuf::uint64 TestAllTypes::repeated_uint64(int index) const {
  // @@protoc_insertion_point(field_get:protobuf_unittest_no_arena.TestAllTypes.repeated_uint64)
  return repeated_uint64_.Get(index);
}
void TestAllTypes::set_repeated_uint64(int index, ::google::protobuf::uint64 value) {
  repeated_uint64_.Set(index, value);
  // @@protoc_insertion_point(field_set:protobuf_unittest_no_arena.TestAllTypes.repeated_uint64)
}
void TestAllTypes::add_repeated_uint64(::google::protobuf::uint64 value) {
  repeated_uint64_.Add(value);
  // @@protoc_insertion_point(field_add:protobuf_unittest_no_arena.TestAllTypes.repeated_uint64)
}
const ::google::protobuf::RepeatedField< ::google::protobuf::uint64 >&
TestAllTypes::repeated_uint64() const {
  // @@protoc_insertion_point(field_list:protobuf_unittest_no_arena.TestAllTypes.repeated_uint64)
  return repeated_uint64_;
}
::google::protobuf::RepeatedField< ::google::protobuf::uint64 >*
TestAllTypes::mutable_repeated_uint64() {
  // @@protoc_insertion_point(field_mutable_list:protobuf_unittest_no_arena.TestAllTypes.repeated_uint64)
  return &repeated_uint64_;
}

// repeated sint32 repeated_sint32 = 35;
int TestAllTypes::repeated_sint32_size() const {
  return repeated_sint32_.size();
}
void TestAllTypes::clear_repeated_sint32() {
  repeated_sint32_.Clear();
}
::google::protobuf::int32 TestAllTypes::repeated_sint32(int index) const {
  // @@protoc_insertion_point(field_get:protobuf_unittest_no_arena.TestAllTypes.repeated_sint32)
  return repeated_sint32_.Get(index);
}
void TestAllTypes::set_repeated_sint32(int index, ::google::protobuf::int32 value) {
  repeated_sint32_.Set(index, value);
  // @@protoc_insertion_point(field_set:protobuf_unittest_no_arena.TestAllTypes.repeated_sint32)
}
void TestAllTypes::add_repeated_sint32(::google::protobuf::int32 value) {
  repeated_sint32_.Add(value);
  // @@protoc_insertion_point(field_add:protobuf_unittest_no_arena.TestAllTypes.repeated_sint32)
}
const ::google::protobuf::RepeatedField< ::google::protobuf::int32 >&
TestAllTypes::repeated_sint32() const {
  // @@protoc_insertion_point(field_list:protobuf_unittest_no_arena.TestAllTypes.repeated_sint32)
  return repeated_sint32_;
}
::google::protobuf::RepeatedField< ::google::protobuf::int32 >*
TestAllTypes::mutable_repeated_sint32() {
  // @@protoc_insertion_point(field_mutable_list:protobuf_unittest_no_arena.TestAllTypes.repeated_sint32)
  return &repeated_sint32_;
}

// repeated sint64 repeated_sint64 = 36;
int TestAllTypes::repeated_sint64_size() const {
  return repeated_sint64_.size();
}
void TestAllTypes::clear_repeated_sint64() {
  repeated_sint64_.Clear();
}
::google::protobuf::int64 TestAllTypes::repeated_sint64(int index) const {
  // @@protoc_insertion_point(field_get:protobuf_unittest_no_arena.TestAllTypes.repeated_sint64)
  return repeated_sint64_.Get(index);
}
void TestAllTypes::set_repeated_sint64(int index, ::google::protobuf::int64 value) {
  repeated_sint64_.Set(index, value);
  // @@protoc_insertion_point(field_set:protobuf_unittest_no_arena.TestAllTypes.repeated_sint64)
}
void TestAllTypes::add_repeated_sint64(::google::protobuf::int64 value) {
  repeated_sint64_.Add(value);
  // @@protoc_insertion_point(field_add:protobuf_unittest_no_arena.TestAllTypes.repeated_sint64)
}
const ::google::protobuf::RepeatedField< ::google::protobuf::int64 >&
TestAllTypes::repeated_sint64() const {
  // @@protoc_insertion_point(field_list:protobuf_unittest_no_arena.TestAllTypes.repeated_sint64)
  return repeated_sint64_;
}
::google::protobuf::RepeatedField< ::google::protobuf::int64 >*
TestAllTypes::mutable_repeated_sint64() {
  // @@protoc_insertion_point(field_mutable_list:protobuf_unittest_no_arena.TestAllTypes.repeated_sint64)
  return &repeated_sint64_;
}

// repeated fixed32 repeated_fixed32 = 37;
int TestAllTypes::repeated_fixed32_size() const {
  return repeated_fixed32_.size();
}
void TestAllTypes::clear_repeated_fixed32() {
  repeated_fixed32_.Clear();
}
::google::protobuf::uint32 TestAllTypes::repeated_fixed32(int index) const {
  // @@protoc_insertion_point(field_get:protobuf_unittest_no_arena.TestAllTypes.repeated_fixed32)
  return repeated_fixed32_.Get(index);
}
void TestAllTypes::set_repeated_fixed32(int index, ::google::protobuf::uint32 value) {
  repeated_fixed32_.Set(index, value);
  // @@protoc_insertion_point(field_set:protobuf_unittest_no_arena.TestAllTypes.repeated_fixed32)
}
void TestAllTypes::add_repeated_fixed32(::google::protobuf::uint32 value) {
  repeated_fixed32_.Add(value);
  // @@protoc_insertion_point(field_add:protobuf_unittest_no_arena.TestAllTypes.repeated_fixed32)
}
const ::google::protobuf::RepeatedField< ::google::protobuf::uint32 >&
TestAllTypes::repeated_fixed32() const {
  // @@protoc_insertion_point(field_list:protobuf_unittest_no_arena.TestAllTypes.repeated_fixed32)
  return repeated_fixed32_;
}
::google::protobuf::RepeatedField< ::google::protobuf::uint32 >*
TestAllTypes::mutable_repeated_fixed32() {
  // @@protoc_insertion_point(field_mutable_list:protobuf_unittest_no_arena.TestAllTypes.repeated_fixed32)
  return &repeated_fixed32_;
}

// repeated fixed64 repeated_fixed64 = 38;
int TestAllTypes::repeated_fixed64_size() const {
  return repeated_fixed64_.size();
}
void TestAllTypes::clear_repeated_fixed64() {
  repeated_fixed64_.Clear();
}
::google::protobuf::uint64 TestAllTypes::repeated_fixed64(int index) const {
  // @@protoc_insertion_point(field_get:protobuf_unittest_no_arena.TestAllTypes.repeated_fixed64)
  return repeated_fixed64_.Get(index);
}
void TestAllTypes::set_repeated_fixed64(int index, ::google::protobuf::uint64 value) {
  repeated_fixed64_.Set(index, value);
  // @@protoc_insertion_point(field_set:protobuf_unittest_no_arena.TestAllTypes.repeated_fixed64)
}
void TestAllTypes::add_repeated_fixed64(::google::protobuf::uint64 value) {
  repeated_fixed64_.Add(value);
  // @@protoc_insertion_point(field_add:protobuf_unittest_no_arena.TestAllTypes.repeated_fixed64)
}
const ::google::protobuf::RepeatedField< ::google::protobuf::uint64 >&
TestAllTypes::repeated_fixed64() const {
  // @@protoc_insertion_point(field_list:protobuf_unittest_no_arena.TestAllTypes.repeated_fixed64)
  return repeated_fixed64_;
}
::google::protobuf::RepeatedField< ::google::protobuf::uint64 >*
TestAllTypes::mutable_repeated_fixed64() {
  // @@protoc_insertion_point(field_mutable_list:protobuf_unittest_no_arena.TestAllTypes.repeated_fixed64)
  return &repeated_fixed64_;
}

// repeated sfixed32 repeated_sfixed32 = 39;
int TestAllTypes::repeated_sfixed32_size() const {
  return repeated_sfixed32_.size();
}
void TestAllTypes::clear_repeated_sfixed32() {
  repeated_sfixed32_.Clear();
}
::google::protobuf::int32 TestAllTypes::repeated_sfixed32(int index) const {
  // @@protoc_insertion_point(field_get:protobuf_unittest_no_arena.TestAllTypes.repeated_sfixed32)
  return repeated_sfixed32_.Get(index);
}
void TestAllTypes::set_repeated_sfixed32(int index, ::google::protobuf::int32 value) {
  repeated_sfixed32_.Set(index, value);
  // @@protoc_insertion_point(field_set:protobuf_unittest_no_arena.TestAllTypes.repeated_sfixed32)
}
void TestAllTypes::add_repeated_sfixed32(::google::protobuf::int32 value) {
  repeated_sfixed32_.Add(value);
  // @@protoc_insertion_point(field_add:protobuf_unittest_no_arena.TestAllTypes.repeated_sfixed32)
}
const ::google::protobuf::RepeatedField< ::google::protobuf::int32 >&
TestAllTypes::repeated_sfixed32() const {
  // @@protoc_insertion_point(field_list:protobuf_unittest_no_arena.TestAllTypes.repeated_sfixed32)
  return repeated_sfixed32_;
}
::google::protobuf::RepeatedField< ::google::protobuf::int32 >*
TestAllTypes::mutable_repeated_sfixed32() {
  // @@protoc_insertion_point(field_mutable_list:protobuf_unittest_no_arena.TestAllTypes.repeated_sfixed32)
  return &repeated_sfixed32_;
}

// repeated sfixed64 repeated_sfixed64 = 40;
int TestAllTypes::repeated_sfixed64_size() const {
  return repeated_sfixed64_.size();
}
void TestAllTypes::clear_repeated_sfixed64() {
  repeated_sfixed64_.Clear();
}
::google::protobuf::int64 TestAllTypes::repeated_sfixed64(int index) const {
  // @@protoc_insertion_point(field_get:protobuf_unittest_no_arena.TestAllTypes.repeated_sfixed64)
  return repeated_sfixed64_.Get(index);
}
void TestAllTypes::set_repeated_sfixed64(int index, ::google::protobuf::int64 value) {
  repeated_sfixed64_.Set(index, value);
  // @@protoc_insertion_point(field_set:protobuf_unittest_no_arena.TestAllTypes.repeated_sfixed64)
}
void TestAllTypes::add_repeated_sfixed64(::google::protobuf::int64 value) {
  repeated_sfixed64_.Add(value);
  // @@protoc_insertion_point(field_add:protobuf_unittest_no_arena.TestAllTypes.repeated_sfixed64)
}
const ::google::protobuf::RepeatedField< ::google::protobuf::int64 >&
TestAllTypes::repeated_sfixed64() const {
  // @@protoc_insertion_point(field_list:protobuf_unittest_no_arena.TestAllTypes.repeated_sfixed64)
  return repeated_sfixed64_;
}
::google::protobuf::RepeatedField< ::google::protobuf::int64 >*
TestAllTypes::mutable_repeated_sfixed64() {
  // @@protoc_insertion_point(field_mutable_list:protobuf_unittest_no_arena.TestAllTypes.repeated_sfixed64)
  return &repeated_sfixed64_;
}

// repeated float repeated_float = 41;
int TestAllTypes::repeated_float_size() const {
  return repeated_float_.size();
}
void TestAllTypes::clear_repeated_float() {
  repeated_float_.Clear();
}
float TestAllTypes::repeated_float(int index) const {
  // @@protoc_insertion_point(field_get:protobuf_unittest_no_arena.TestAllTypes.repeated_float)
  return repeated_float_.Get(index);
}
void TestAllTypes::set_repeated_float(int index, float value) {
  repeated_float_.Set(index, value);
  // @@protoc_insertion_point(field_set:protobuf_unittest_no_arena.TestAllTypes.repeated_float)
}
void TestAllTypes::add_repeated_float(float value) {
  repeated_float_.Add(value);
  // @@protoc_insertion_point(field_add:protobuf_unittest_no_arena.TestAllTypes.repeated_float)
}
const ::google::protobuf::RepeatedField< float >&
TestAllTypes::repeated_float() const {
  // @@protoc_insertion_point(field_list:protobuf_unittest_no_arena.TestAllTypes.repeated_float)
  return repeated_float_;
}
::google::protobuf::RepeatedField< float >*
TestAllTypes::mutable_repeated_float() {
  // @@protoc_insertion_point(field_mutable_list:protobuf_unittest_no_arena.TestAllTypes.repeated_float)
  return &repeated_float_;
}

// repeated double repeated_double = 42;
int TestAllTypes::repeated_double_size() const {
  return repeated_double_.size();
}
void TestAllTypes::clear_repeated_double() {
  repeated_double_.Clear();
}
double TestAllTypes::repeated_double(int index) const {
  // @@protoc_insertion_point(field_get:protobuf_unittest_no_arena.TestAllTypes.repeated_double)
  return repeated_double_.Get(index);
}
void TestAllTypes::set_repeated_double(int index, double value) {
  repeated_double_.Set(index, value);
  // @@protoc_insertion_point(field_set:protobuf_unittest_no_arena.TestAllTypes.repeated_double)
}
void TestAllTypes::add_repeated_double(double value) {
  repeated_double_.Add(value);
  // @@protoc_insertion_point(field_add:protobuf_unittest_no_arena.TestAllTypes.repeated_double)
}
const ::google::protobuf::RepeatedField< double >&
TestAllTypes::repeated_double() const {
  // @@protoc_insertion_point(field_list:protobuf_unittest_no_arena.TestAllTypes.repeated_double)
  return repeated_double_;
}
::google::protobuf::RepeatedField< double >*
TestAllTypes::mutable_repeated_double() {
  // @@protoc_insertion_point(field_mutable_list:protobuf_unittest_no_arena.TestAllTypes.repeated_double)
  return &repeated_double_;
}

// repeated bool repeated_bool = 43;
int TestAllTypes::repeated_bool_size() const {
  return repeated_bool_.size();
}
void TestAllTypes::clear_repeated_bool() {
  repeated_bool_.Clear();
}
bool TestAllTypes::repeated_bool(int index) const {
  // @@protoc_insertion_point(field_get:protobuf_unittest_no_arena.TestAllTypes.repeated_bool)
  return repeated_bool_.Get(index);
}
void TestAllTypes::set_repeated_bool(int index, bool value) {
  repeated_bool_.Set(index, value);
  // @@protoc_insertion_point(field_set:protobuf_unittest_no_arena.TestAllTypes.repeated_bool)
}
void TestAllTypes::add_repeated_bool(bool value) {
  repeated_bool_.Add(value);
  // @@protoc_insertion_point(field_add:protobuf_unittest_no_arena.TestAllTypes.repeated_bool)
}
const ::google::protobuf::RepeatedField< bool >&
TestAllTypes::repeated_bool() const {
  // @@protoc_insertion_point(field_list:protobuf_unittest_no_arena.TestAllTypes.repeated_bool)
  return repeated_bool_;
}
::google::protobuf::RepeatedField< bool >*
TestAllTypes::mutable_repeated_bool() {
  // @@protoc_insertion_point(field_mutable_list:protobuf_unittest_no_arena.TestAllTypes.repeated_bool)
  return &repeated_bool_;
}

// repeated string repeated_string = 44;
int TestAllTypes::repeated_string_size() const {
  return repeated_string_.size();
}
void TestAllTypes::clear_repeated_string() {
  repeated_string_.Clear();
}
const ::std::string& TestAllTypes::repeated_string(int index) const {
  // @@protoc_insertion_point(field_get:protobuf_unittest_no_arena.TestAllTypes.repeated_string)
  return repeated_string_.Get(index);
}
::std::string* TestAllTypes::mutable_repeated_string(int index) {
  // @@protoc_insertion_point(field_mutable:protobuf_unittest_no_arena.TestAllTypes.repeated_string)
  return repeated_string_.Mutable(index);
}
void TestAllTypes::set_repeated_string(int index, const ::std::string& value) {
  // @@protoc_insertion_point(field_set:protobuf_unittest_no_arena.TestAllTypes.repeated_string)
  repeated_string_.Mutable(index)->assign(value);
}
void TestAllTypes::set_repeated_string(int index, const char* value) {
  repeated_string_.Mutable(index)->assign(value);
  // @@protoc_insertion_point(field_set_char:protobuf_unittest_no_arena.TestAllTypes.repeated_string)
}
void TestAllTypes::set_repeated_string(int index, const char* value, size_t size) {
  repeated_string_.Mutable(index)->assign(
    reinterpret_cast<const char*>(value), size);
  // @@protoc_insertion_point(field_set_pointer:protobuf_unittest_no_arena.TestAllTypes.repeated_string)
}
::std::string* TestAllTypes::add_repeated_string() {
  // @@protoc_insertion_point(field_add_mutable:protobuf_unittest_no_arena.TestAllTypes.repeated_string)
  return repeated_string_.Add();
}
void TestAllTypes::add_repeated_string(const ::std::string& value) {
  repeated_string_.Add()->assign(value);
  // @@protoc_insertion_point(field_add:protobuf_unittest_no_arena.TestAllTypes.repeated_string)
}
void TestAllTypes::add_repeated_string(const char* value) {
  repeated_string_.Add()->assign(value);
  // @@protoc_insertion_point(field_add_char:protobuf_unittest_no_arena.TestAllTypes.repeated_string)
}
void TestAllTypes::add_repeated_string(const char* value, size_t size) {
  repeated_string_.Add()->assign(reinterpret_cast<const char*>(value), size);
  // @@protoc_insertion_point(field_add_pointer:protobuf_unittest_no_arena.TestAllTypes.repeated_string)
}
const ::google::protobuf::RepeatedPtrField< ::std::string>&
TestAllTypes::repeated_string() const {
  // @@protoc_insertion_point(field_list:protobuf_unittest_no_arena.TestAllTypes.repeated_string)
  return repeated_string_;
}
::google::protobuf::RepeatedPtrField< ::std::string>*
TestAllTypes::mutable_repeated_string() {
  // @@protoc_insertion_point(field_mutable_list:protobuf_unittest_no_arena.TestAllTypes.repeated_string)
  return &repeated_string_;
}

// repeated bytes repeated_bytes = 45;
int TestAllTypes::repeated_bytes_size() const {
  return repeated_bytes_.size();
}
void TestAllTypes::clear_repeated_bytes() {
  repeated_bytes_.Clear();
}
const ::std::string& TestAllTypes::repeated_bytes(int index) const {
  // @@protoc_insertion_point(field_get:protobuf_unittest_no_arena.TestAllTypes.repeated_bytes)
  return repeated_bytes_.Get(index);
}
::std::string* TestAllTypes::mutable_repeated_bytes(int index) {
  // @@protoc_insertion_point(field_mutable:protobuf_unittest_no_arena.TestAllTypes.repeated_bytes)
  return repeated_bytes_.Mutable(index);
}
void TestAllTypes::set_repeated_bytes(int index, const ::std::string& value) {
  // @@protoc_insertion_point(field_set:protobuf_unittest_no_arena.TestAllTypes.repeated_bytes)
  repeated_bytes_.Mutable(index)->assign(value);
}
void TestAllTypes::set_repeated_bytes(int index, const char* value) {
  repeated_bytes_.Mutable(index)->assign(value);
  // @@protoc_insertion_point(field_set_char:protobuf_unittest_no_arena.TestAllTypes.repeated_bytes)
}
void TestAllTypes::set_repeated_bytes(int index, const void* value, size_t size) {
  repeated_bytes_.Mutable(index)->assign(
    reinterpret_cast<const char*>(value), size);
  // @@protoc_insertion_point(field_set_pointer:protobuf_unittest_no_arena.TestAllTypes.repeated_bytes)
}
::std::string* TestAllTypes::add_repeated_bytes() {
  // @@protoc_insertion_point(field_add_mutable:protobuf_unittest_no_arena.TestAllTypes.repeated_bytes)
  return repeated_bytes_.Add();
}
void TestAllTypes::add_repeated_bytes(const ::std::string& value) {
  repeated_bytes_.Add()->assign(value);
  // @@protoc_insertion_point(field_add:protobuf_unittest_no_arena.TestAllTypes.repeated_bytes)
}
void TestAllTypes::add_repeated_bytes(const char* value) {
  repeated_bytes_.Add()->assign(value);
  // @@protoc_insertion_point(field_add_char:protobuf_unittest_no_arena.TestAllTypes.repeated_bytes)
}
void TestAllTypes::add_repeated_bytes(const void* value, size_t size) {
  repeated_bytes_.Add()->assign(reinterpret_cast<const char*>(value), size);
  // @@protoc_insertion_point(field_add_pointer:protobuf_unittest_no_arena.TestAllTypes.repeated_bytes)
}
const ::google::protobuf::RepeatedPtrField< ::std::string>&
TestAllTypes::repeated_bytes() const {
  // @@protoc_insertion_point(field_list:protobuf_unittest_no_arena.TestAllTypes.repeated_bytes)
  return repeated_bytes_;
}
::google::protobuf::RepeatedPtrField< ::std::string>*
TestAllTypes::mutable_repeated_bytes() {
  // @@protoc_insertion_point(field_mutable_list:protobuf_unittest_no_arena.TestAllTypes.repeated_bytes)
  return &repeated_bytes_;
}

// repeated group RepeatedGroup = 46 { ... };
int TestAllTypes::repeatedgroup_size() const {
  return repeatedgroup_.size();
}
void TestAllTypes::clear_repeatedgroup() {
  repeatedgroup_.Clear();
}
const ::protobuf_unittest_no_arena::TestAllTypes_RepeatedGroup& TestAllTypes::repeatedgroup(int index) const {
  // @@protoc_insertion_point(field_get:protobuf_unittest_no_arena.TestAllTypes.repeatedgroup)
  return repeatedgroup_.Get(index);
}
::protobuf_unittest_no_arena::TestAllTypes_RepeatedGroup* TestAllTypes::mutable_repeatedgroup(int index) {
  // @@protoc_insertion_point(field_mutable:protobuf_unittest_no_arena.TestAllTypes.repeatedgroup)
  return repeatedgroup_.Mutable(index);
}
::protobuf_unittest_no_arena::TestAllTypes_RepeatedGroup* TestAllTypes::add_repeatedgroup() {
  // @@protoc_insertion_point(field_add:protobuf_unittest_no_arena.TestAllTypes.repeatedgroup)
  return repeatedgroup_.Add();
}
::google::protobuf::RepeatedPtrField< ::protobuf_unittest_no_arena::TestAllTypes_RepeatedGroup >*
TestAllTypes::mutable_repeatedgroup() {
  // @@protoc_insertion_point(field_mutable_list:protobuf_unittest_no_arena.TestAllTypes.repeatedgroup)
  return &repeatedgroup_;
}
const ::google::protobuf::RepeatedPtrField< ::protobuf_unittest_no_arena::TestAllTypes_RepeatedGroup >&
TestAllTypes::repeatedgroup() const {
  // @@protoc_insertion_point(field_list:protobuf_unittest_no_arena.TestAllTypes.repeatedgroup)
  return repeatedgroup_;
}

// repeated .protobuf_unittest_no_arena.TestAllTypes.NestedMessage repeated_nested_message = 48;
int TestAllTypes::repeated_nested_message_size() const {
  return repeated_nested_message_.size();
}
void TestAllTypes::clear_repeated_nested_message() {
  repeated_nested_message_.Clear();
}
const ::protobuf_unittest_no_arena::TestAllTypes_NestedMessage& TestAllTypes::repeated_nested_message(int index) const {
  // @@protoc_insertion_point(field_get:protobuf_unittest_no_arena.TestAllTypes.repeated_nested_message)
  return repeated_nested_message_.Get(index);
}
::protobuf_unittest_no_arena::TestAllTypes_NestedMessage* TestAllTypes::mutable_repeated_nested_message(int index) {
  // @@protoc_insertion_point(field_mutable:protobuf_unittest_no_arena.TestAllTypes.repeated_nested_message)
  return repeated_nested_message_.Mutable(index);
}
::protobuf_unittest_no_arena::TestAllTypes_NestedMessage* TestAllTypes::add_repeated_nested_message() {
  // @@protoc_insertion_point(field_add:protobuf_unittest_no_arena.TestAllTypes.repeated_nested_message)
  return repeated_nested_message_.Add();
}
::google::protobuf::RepeatedPtrField< ::protobuf_unittest_no_arena::TestAllTypes_NestedMessage >*
TestAllTypes::mutable_repeated_nested_message() {
  // @@protoc_insertion_point(field_mutable_list:protobuf_unittest_no_arena.TestAllTypes.repeated_nested_message)
  return &repeated_nested_message_;
}
const ::google::protobuf::RepeatedPtrField< ::protobuf_unittest_no_arena::TestAllTypes_NestedMessage >&
TestAllTypes::repeated_nested_message() const {
  // @@protoc_insertion_point(field_list:protobuf_unittest_no_arena.TestAllTypes.repeated_nested_message)
  return repeated_nested_message_;
}

// repeated .protobuf_unittest_no_arena.ForeignMessage repeated_foreign_message = 49;
int TestAllTypes::repeated_foreign_message_size() const {
  return repeated_foreign_message_.size();
}
void TestAllTypes::clear_repeated_foreign_message() {
  repeated_foreign_message_.Clear();
}
const ::protobuf_unittest_no_arena::ForeignMessage& TestAllTypes::repeated_foreign_message(int index) const {
  // @@protoc_insertion_point(field_get:protobuf_unittest_no_arena.TestAllTypes.repeated_foreign_message)
  return repeated_foreign_message_.Get(index);
}
::protobuf_unittest_no_arena::ForeignMessage* TestAllTypes::mutable_repeated_foreign_message(int index) {
  // @@protoc_insertion_point(field_mutable:protobuf_unittest_no_arena.TestAllTypes.repeated_foreign_message)
  return repeated_foreign_message_.Mutable(index);
}
::protobuf_unittest_no_arena::ForeignMessage* TestAllTypes::add_repeated_foreign_message() {
  // @@protoc_insertion_point(field_add:protobuf_unittest_no_arena.TestAllTypes.repeated_foreign_message)
  return repeated_foreign_message_.Add();
}
::google::protobuf::RepeatedPtrField< ::protobuf_unittest_no_arena::ForeignMessage >*
TestAllTypes::mutable_repeated_foreign_message() {
  // @@protoc_insertion_point(field_mutable_list:protobuf_unittest_no_arena.TestAllTypes.repeated_foreign_message)
  return &repeated_foreign_message_;
}
const ::google::protobuf::RepeatedPtrField< ::protobuf_unittest_no_arena::ForeignMessage >&
TestAllTypes::repeated_foreign_message() const {
  // @@protoc_insertion_point(field_list:protobuf_unittest_no_arena.TestAllTypes.repeated_foreign_message)
  return repeated_foreign_message_;
}

// repeated .protobuf_unittest_import.ImportMessage repeated_import_message = 50;
int TestAllTypes::repeated_import_message_size() const {
  return repeated_import_message_.size();
}
void TestAllTypes::clear_repeated_import_message() {
  repeated_import_message_.Clear();
}
const ::protobuf_unittest_import::ImportMessage& TestAllTypes::repeated_import_message(int index) const {
  // @@protoc_insertion_point(field_get:protobuf_unittest_no_arena.TestAllTypes.repeated_import_message)
  return repeated_import_message_.Get(index);
}
::protobuf_unittest_import::ImportMessage* TestAllTypes::mutable_repeated_import_message(int index) {
  // @@protoc_insertion_point(field_mutable:protobuf_unittest_no_arena.TestAllTypes.repeated_import_message)
  return repeated_import_message_.Mutable(index);
}
::protobuf_unittest_import::ImportMessage* TestAllTypes::add_repeated_import_message() {
  // @@protoc_insertion_point(field_add:protobuf_unittest_no_arena.TestAllTypes.repeated_import_message)
  return repeated_import_message_.Add();
}
::google::protobuf::RepeatedPtrField< ::protobuf_unittest_import::ImportMessage >*
TestAllTypes::mutable_repeated_import_message() {
  // @@protoc_insertion_point(field_mutable_list:protobuf_unittest_no_arena.TestAllTypes.repeated_import_message)
  return &repeated_import_message_;
}
const ::google::protobuf::RepeatedPtrField< ::protobuf_unittest_import::ImportMessage >&
TestAllTypes::repeated_import_message() const {
  // @@protoc_insertion_point(field_list:protobuf_unittest_no_arena.TestAllTypes.repeated_import_message)
  return repeated_import_message_;
}

// repeated .protobuf_unittest_no_arena.TestAllTypes.NestedEnum repeated_nested_enum = 51;
int TestAllTypes::repeated_nested_enum_size() const {
  return repeated_nested_enum_.size();
}
void TestAllTypes::clear_repeated_nested_enum() {
  repeated_nested_enum_.Clear();
}
::protobuf_unittest_no_arena::TestAllTypes_NestedEnum TestAllTypes::repeated_nested_enum(int index) const {
  // @@protoc_insertion_point(field_get:protobuf_unittest_no_arena.TestAllTypes.repeated_nested_enum)
  return static_cast< ::protobuf_unittest_no_arena::TestAllTypes_NestedEnum >(repeated_nested_enum_.Get(index));
}
void TestAllTypes::set_repeated_nested_enum(int index, ::protobuf_unittest_no_arena::TestAllTypes_NestedEnum value) {
  assert(::protobuf_unittest_no_arena::TestAllTypes_NestedEnum_IsValid(value));
  repeated_nested_enum_.Set(index, value);
  // @@protoc_insertion_point(field_set:protobuf_unittest_no_arena.TestAllTypes.repeated_nested_enum)
}
void TestAllTypes::add_repeated_nested_enum(::protobuf_unittest_no_arena::TestAllTypes_NestedEnum value) {
  assert(::protobuf_unittest_no_arena::TestAllTypes_NestedEnum_IsValid(value));
  repeated_nested_enum_.Add(value);
  // @@protoc_insertion_point(field_add:protobuf_unittest_no_arena.TestAllTypes.repeated_nested_enum)
}
const ::google::protobuf::RepeatedField<int>&
TestAllTypes::repeated_nested_enum() const {
  // @@protoc_insertion_point(field_list:protobuf_unittest_no_arena.TestAllTypes.repeated_nested_enum)
  return repeated_nested_enum_;
}
::google::protobuf::RepeatedField<int>*
TestAllTypes::mutable_repeated_nested_enum() {
  // @@protoc_insertion_point(field_mutable_list:protobuf_unittest_no_arena.TestAllTypes.repeated_nested_enum)
  return &repeated_nested_enum_;
}

// repeated .protobuf_unittest_no_arena.ForeignEnum repeated_foreign_enum = 52;
int TestAllTypes::repeated_foreign_enum_size() const {
  return repeated_foreign_enum_.size();
}
void TestAllTypes::clear_repeated_foreign_enum() {
  repeated_foreign_enum_.Clear();
}
::protobuf_unittest_no_arena::ForeignEnum TestAllTypes::repeated_foreign_enum(int index) const {
  // @@protoc_insertion_point(field_get:protobuf_unittest_no_arena.TestAllTypes.repeated_foreign_enum)
  return static_cast< ::protobuf_unittest_no_arena::ForeignEnum >(repeated_foreign_enum_.Get(index));
}
void TestAllTypes::set_repeated_foreign_enum(int index, ::protobuf_unittest_no_arena::ForeignEnum value) {
  assert(::protobuf_unittest_no_arena::ForeignEnum_IsValid(value));
  repeated_foreign_enum_.Set(index, value);
  // @@protoc_insertion_point(field_set:protobuf_unittest_no_arena.TestAllTypes.repeated_foreign_enum)
}
void TestAllTypes::add_repeated_foreign_enum(::protobuf_unittest_no_arena::ForeignEnum value) {
  assert(::protobuf_unittest_no_arena::ForeignEnum_IsValid(value));
  repeated_foreign_enum_.Add(value);
  // @@protoc_insertion_point(field_add:protobuf_unittest_no_arena.TestAllTypes.repeated_foreign_enum)
}
const ::google::protobuf::RepeatedField<int>&
TestAllTypes::repeated_foreign_enum() const {
  // @@protoc_insertion_point(field_list:protobuf_unittest_no_arena.TestAllTypes.repeated_foreign_enum)
  return repeated_foreign_enum_;
}
::google::protobuf::RepeatedField<int>*
TestAllTypes::mutable_repeated_foreign_enum() {
  // @@protoc_insertion_point(field_mutable_list:protobuf_unittest_no_arena.TestAllTypes.repeated_foreign_enum)
  return &repeated_foreign_enum_;
}

// repeated .protobuf_unittest_import.ImportEnum repeated_import_enum = 53;
int TestAllTypes::repeated_import_enum_size() const {
  return repeated_import_enum_.size();
}
void TestAllTypes::clear_repeated_import_enum() {
  repeated_import_enum_.Clear();
}
::protobuf_unittest_import::ImportEnum TestAllTypes::repeated_import_enum(int index) const {
  // @@protoc_insertion_point(field_get:protobuf_unittest_no_arena.TestAllTypes.repeated_import_enum)
  return static_cast< ::protobuf_unittest_import::ImportEnum >(repeated_import_enum_.Get(index));
}
void TestAllTypes::set_repeated_import_enum(int index, ::protobuf_unittest_import::ImportEnum value) {
  assert(::protobuf_unittest_import::ImportEnum_IsValid(value));
  repeated_import_enum_.Set(index, value);
  // @@protoc_insertion_point(field_set:protobuf_unittest_no_arena.TestAllTypes.repeated_import_enum)
}
void TestAllTypes::add_repeated_import_enum(::protobuf_unittest_import::ImportEnum value) {
  assert(::protobuf_unittest_import::ImportEnum_IsValid(value));
  repeated_import_enum_.Add(value);
  // @@protoc_insertion_point(field_add:protobuf_unittest_no_arena.TestAllTypes.repeated_import_enum)
}
const ::google::protobuf::RepeatedField<int>&
TestAllTypes::repeated_import_enum() const {
  // @@protoc_insertion_point(field_list:protobuf_unittest_no_arena.TestAllTypes.repeated_import_enum)
  return repeated_import_enum_;
}
::google::protobuf::RepeatedField<int>*
TestAllTypes::mutable_repeated_import_enum() {
  // @@protoc_insertion_point(field_mutable_list:protobuf_unittest_no_arena.TestAllTypes.repeated_import_enum)
  return &repeated_import_enum_;
}

// repeated string repeated_string_piece = 54 [ctype = STRING_PIECE];
int TestAllTypes::repeated_string_piece_size() const {
  return repeated_string_piece_.size();
}
void TestAllTypes::clear_repeated_string_piece() {
  repeated_string_piece_.Clear();
}
const ::std::string& TestAllTypes::repeated_string_piece(int index) const {
  // @@protoc_insertion_point(field_get:protobuf_unittest_no_arena.TestAllTypes.repeated_string_piece)
  return repeated_string_piece_.Get(index);
}
::std::string* TestAllTypes::mutable_repeated_string_piece(int index) {
  // @@protoc_insertion_point(field_mutable:protobuf_unittest_no_arena.TestAllTypes.repeated_string_piece)
  return repeated_string_piece_.Mutable(index);
}
void TestAllTypes::set_repeated_string_piece(int index, const ::std::string& value) {
  // @@protoc_insertion_point(field_set:protobuf_unittest_no_arena.TestAllTypes.repeated_string_piece)
  repeated_string_piece_.Mutable(index)->assign(value);
}
void TestAllTypes::set_repeated_string_piece(int index, const char* value) {
  repeated_string_piece_.Mutable(index)->assign(value);
  // @@protoc_insertion_point(field_set_char:protobuf_unittest_no_arena.TestAllTypes.repeated_string_piece)
}
void TestAllTypes::set_repeated_string_piece(int index, const char* value, size_t size) {
  repeated_string_piece_.Mutable(index)->assign(
    reinterpret_cast<const char*>(value), size);
  // @@protoc_insertion_point(field_set_pointer:protobuf_unittest_no_arena.TestAllTypes.repeated_string_piece)
}
::std::string* TestAllTypes::add_repeated_string_piece() {
  // @@protoc_insertion_point(field_add_mutable:protobuf_unittest_no_arena.TestAllTypes.repeated_string_piece)
  return repeated_string_piece_.Add();
}
void TestAllTypes::add_repeated_string_piece(const ::std::string& value) {
  repeated_string_piece_.Add()->assign(value);
  // @@protoc_insertion_point(field_add:protobuf_unittest_no_arena.TestAllTypes.repeated_string_piece)
}
void TestAllTypes::add_repeated_string_piece(const char* value) {
  repeated_string_piece_.Add()->assign(value);
  // @@protoc_insertion_point(field_add_char:protobuf_unittest_no_arena.TestAllTypes.repeated_string_piece)
}
void TestAllTypes::add_repeated_string_piece(const char* value, size_t size) {
  repeated_string_piece_.Add()->assign(reinterpret_cast<const char*>(value), size);
  // @@protoc_insertion_point(field_add_pointer:protobuf_unittest_no_arena.TestAllTypes.repeated_string_piece)
}
const ::google::protobuf::RepeatedPtrField< ::std::string>&
TestAllTypes::repeated_string_piece() const {
  // @@protoc_insertion_point(field_list:protobuf_unittest_no_arena.TestAllTypes.repeated_string_piece)
  return repeated_string_piece_;
}
::google::protobuf::RepeatedPtrField< ::std::string>*
TestAllTypes::mutable_repeated_string_piece() {
  // @@protoc_insertion_point(field_mutable_list:protobuf_unittest_no_arena.TestAllTypes.repeated_string_piece)
  return &repeated_string_piece_;
}

// repeated string repeated_cord = 55 [ctype = CORD];
int TestAllTypes::repeated_cord_size() const {
  return repeated_cord_.size();
}
void TestAllTypes::clear_repeated_cord() {
  repeated_cord_.Clear();
}
const ::std::string& TestAllTypes::repeated_cord(int index) const {
  // @@protoc_insertion_point(field_get:protobuf_unittest_no_arena.TestAllTypes.repeated_cord)
  return repeated_cord_.Get(index);
}
::std::string* TestAllTypes::mutable_repeated_cord(int index) {
  // @@protoc_insertion_point(field_mutable:protobuf_unittest_no_arena.TestAllTypes.repeated_cord)
  return repeated_cord_.Mutable(index);
}
void TestAllTypes::set_repeated_cord(int index, const ::std::string& value) {
  // @@protoc_insertion_point(field_set:protobuf_unittest_no_arena.TestAllTypes.repeated_cord)
  repeated_cord_.Mutable(index)->assign(value);
}
void TestAllTypes::set_repeated_cord(int index, const char* value) {
  repeated_cord_.Mutable(index)->assign(value);
  // @@protoc_insertion_point(field_set_char:protobuf_unittest_no_arena.TestAllTypes.repeated_cord)
}
void TestAllTypes::set_repeated_cord(int index, const char* value, size_t size) {
  repeated_cord_.Mutable(index)->assign(
    reinterpret_cast<const char*>(value), size);
  // @@protoc_insertion_point(field_set_pointer:protobuf_unittest_no_arena.TestAllTypes.repeated_cord)
}
::std::string* TestAllTypes::add_repeated_cord() {
  // @@protoc_insertion_point(field_add_mutable:protobuf_unittest_no_arena.TestAllTypes.repeated_cord)
  return repeated_cord_.Add();
}
void TestAllTypes::add_repeated_cord(const ::std::string& value) {
  repeated_cord_.Add()->assign(value);
  // @@protoc_insertion_point(field_add:protobuf_unittest_no_arena.TestAllTypes.repeated_cord)
}
void TestAllTypes::add_repeated_cord(const char* value) {
  repeated_cord_.Add()->assign(value);
  // @@protoc_insertion_point(field_add_char:protobuf_unittest_no_arena.TestAllTypes.repeated_cord)
}
void TestAllTypes::add_repeated_cord(const char* value, size_t size) {
  repeated_cord_.Add()->assign(reinterpret_cast<const char*>(value), size);
  // @@protoc_insertion_point(field_add_pointer:protobuf_unittest_no_arena.TestAllTypes.repeated_cord)
}
const ::google::protobuf::RepeatedPtrField< ::std::string>&
TestAllTypes::repeated_cord() const {
  // @@protoc_insertion_point(field_list:protobuf_unittest_no_arena.TestAllTypes.repeated_cord)
  return repeated_cord_;
}
::google::protobuf::RepeatedPtrField< ::std::string>*
TestAllTypes::mutable_repeated_cord() {
  // @@protoc_insertion_point(field_mutable_list:protobuf_unittest_no_arena.TestAllTypes.repeated_cord)
  return &repeated_cord_;
}

// repeated .protobuf_unittest_no_arena.TestAllTypes.NestedMessage repeated_lazy_message = 57 [lazy = true];
int TestAllTypes::repeated_lazy_message_size() const {
  return repeated_lazy_message_.size();
}
void TestAllTypes::clear_repeated_lazy_message() {
  repeated_lazy_message_.Clear();
}
const ::protobuf_unittest_no_arena::TestAllTypes_NestedMessage& TestAllTypes::repeated_lazy_message(int index) const {
  // @@protoc_insertion_point(field_get:protobuf_unittest_no_arena.TestAllTypes.repeated_lazy_message)
  return repeated_lazy_message_.Get(index);
}
::protobuf_unittest_no_arena::TestAllTypes_NestedMessage* TestAllTypes::mutable_repeated_lazy_message(int index) {
  // @@protoc_insertion_point(field_mutable:protobuf_unittest_no_arena.TestAllTypes.repeated_lazy_message)
  return repeated_lazy_message_.Mutable(index);
}
::protobuf_unittest_no_arena::TestAllTypes_NestedMessage* TestAllTypes::add_repeated_lazy_message() {
  // @@protoc_insertion_point(field_add:protobuf_unittest_no_arena.TestAllTypes.repeated_lazy_message)
  return repeated_lazy_message_.Add();
}
::google::protobuf::RepeatedPtrField< ::protobuf_unittest_no_arena::TestAllTypes_NestedMessage >*
TestAllTypes::mutable_repeated_lazy_message() {
  // @@protoc_insertion_point(field_mutable_list:protobuf_unittest_no_arena.TestAllTypes.repeated_lazy_message)
  return &repeated_lazy_message_;
}
const ::google::protobuf::RepeatedPtrField< ::protobuf_unittest_no_arena::TestAllTypes_NestedMessage >&
TestAllTypes::repeated_lazy_message() const {
  // @@protoc_insertion_point(field_list:protobuf_unittest_no_arena.TestAllTypes.repeated_lazy_message)
  return repeated_lazy_message_;
}

// optional int32 default_int32 = 61 [default = 41];
bool TestAllTypes::has_default_int32() const {
  return (_has_bits_[1] & 0x00080000u) != 0;
}
void TestAllTypes::set_has_default_int32() {
  _has_bits_[1] |= 0x00080000u;
}
void TestAllTypes::clear_has_default_int32() {
  _has_bits_[1] &= ~0x00080000u;
}
void TestAllTypes::clear_default_int32() {
  default_int32_ = 41;
  clear_has_default_int32();
}
::google::protobuf::int32 TestAllTypes::default_int32() const {
  // @@protoc_insertion_point(field_get:protobuf_unittest_no_arena.TestAllTypes.default_int32)
  return default_int32_;
}
void TestAllTypes::set_default_int32(::google::protobuf::int32 value) {
  set_has_default_int32();
  default_int32_ = value;
  // @@protoc_insertion_point(field_set:protobuf_unittest_no_arena.TestAllTypes.default_int32)
}

// optional int64 default_int64 = 62 [default = 42];
bool TestAllTypes::has_default_int64() const {
  return (_has_bits_[1] & 0x00100000u) != 0;
}
void TestAllTypes::set_has_default_int64() {
  _has_bits_[1] |= 0x00100000u;
}
void TestAllTypes::clear_has_default_int64() {
  _has_bits_[1] &= ~0x00100000u;
}
void TestAllTypes::clear_default_int64() {
  default_int64_ = GOOGLE_LONGLONG(42);
  clear_has_default_int64();
}
::google::protobuf::int64 TestAllTypes::default_int64() const {
  // @@protoc_insertion_point(field_get:protobuf_unittest_no_arena.TestAllTypes.default_int64)
  return default_int64_;
}
void TestAllTypes::set_default_int64(::google::protobuf::int64 value) {
  set_has_default_int64();
  default_int64_ = value;
  // @@protoc_insertion_point(field_set:protobuf_unittest_no_arena.TestAllTypes.default_int64)
}

// optional uint32 default_uint32 = 63 [default = 43];
bool TestAllTypes::has_default_uint32() const {
  return (_has_bits_[1] & 0x00200000u) != 0;
}
void TestAllTypes::set_has_default_uint32() {
  _has_bits_[1] |= 0x00200000u;
}
void TestAllTypes::clear_has_default_uint32() {
  _has_bits_[1] &= ~0x00200000u;
}
void TestAllTypes::clear_default_uint32() {
  default_uint32_ = 43u;
  clear_has_default_uint32();
}
::google::protobuf::uint32 TestAllTypes::default_uint32() const {
  // @@protoc_insertion_point(field_get:protobuf_unittest_no_arena.TestAllTypes.default_uint32)
  return default_uint32_;
}
void TestAllTypes::set_default_uint32(::google::protobuf::uint32 value) {
  set_has_default_uint32();
  default_uint32_ = value;
  // @@protoc_insertion_point(field_set:protobuf_unittest_no_arena.TestAllTypes.default_uint32)
}

// optional uint64 default_uint64 = 64 [default = 44];
bool TestAllTypes::has_default_uint64() const {
  return (_has_bits_[1] & 0x00400000u) != 0;
}
void TestAllTypes::set_has_default_uint64() {
  _has_bits_[1] |= 0x00400000u;
}
void TestAllTypes::clear_has_default_uint64() {
  _has_bits_[1] &= ~0x00400000u;
}
void TestAllTypes::clear_default_uint64() {
  default_uint64_ = GOOGLE_ULONGLONG(44);
  clear_has_default_uint64();
}
::google::protobuf::uint64 TestAllTypes::default_uint64() const {
  // @@protoc_insertion_point(field_get:protobuf_unittest_no_arena.TestAllTypes.default_uint64)
  return default_uint64_;
}
void TestAllTypes::set_default_uint64(::google::protobuf::uint64 value) {
  set_has_default_uint64();
  default_uint64_ = value;
  // @@protoc_insertion_point(field_set:protobuf_unittest_no_arena.TestAllTypes.default_uint64)
}

// optional sint32 default_sint32 = 65 [default = -45];
bool TestAllTypes::has_default_sint32() const {
  return (_has_bits_[1] & 0x00800000u) != 0;
}
void TestAllTypes::set_has_default_sint32() {
  _has_bits_[1] |= 0x00800000u;
}
void TestAllTypes::clear_has_default_sint32() {
  _has_bits_[1] &= ~0x00800000u;
}
void TestAllTypes::clear_default_sint32() {
  default_sint32_ = -45;
  clear_has_default_sint32();
}
::google::protobuf::int32 TestAllTypes::default_sint32() const {
  // @@protoc_insertion_point(field_get:protobuf_unittest_no_arena.TestAllTypes.default_sint32)
  return default_sint32_;
}
void TestAllTypes::set_default_sint32(::google::protobuf::int32 value) {
  set_has_default_sint32();
  default_sint32_ = value;
  // @@protoc_insertion_point(field_set:protobuf_unittest_no_arena.TestAllTypes.default_sint32)
}

// optional sint64 default_sint64 = 66 [default = 46];
bool TestAllTypes::has_default_sint64() const {
  return (_has_bits_[1] & 0x01000000u) != 0;
}
void TestAllTypes::set_has_default_sint64() {
  _has_bits_[1] |= 0x01000000u;
}
void TestAllTypes::clear_has_default_sint64() {
  _has_bits_[1] &= ~0x01000000u;
}
void TestAllTypes::clear_default_sint64() {
  default_sint64_ = GOOGLE_LONGLONG(46);
  clear_has_default_sint64();
}
::google::protobuf::int64 TestAllTypes::default_sint64() const {
  // @@protoc_insertion_point(field_get:protobuf_unittest_no_arena.TestAllTypes.default_sint64)
  return default_sint64_;
}
void TestAllTypes::set_default_sint64(::google::protobuf::int64 value) {
  set_has_default_sint64();
  default_sint64_ = value;
  // @@protoc_insertion_point(field_set:protobuf_unittest_no_arena.TestAllTypes.default_sint64)
}

// optional fixed32 default_fixed32 = 67 [default = 47];
bool TestAllTypes::has_default_fixed32() const {
  return (_has_bits_[1] & 0x02000000u) != 0;
}
void TestAllTypes::set_has_default_fixed32() {
  _has_bits_[1] |= 0x02000000u;
}
void TestAllTypes::clear_has_default_fixed32() {
  _has_bits_[1] &= ~0x02000000u;
}
void TestAllTypes::clear_default_fixed32() {
  default_fixed32_ = 47u;
  clear_has_default_fixed32();
}
::google::protobuf::uint32 TestAllTypes::default_fixed32() const {
  // @@protoc_insertion_point(field_get:protobuf_unittest_no_arena.TestAllTypes.default_fixed32)
  return default_fixed32_;
}
void TestAllTypes::set_default_fixed32(::google::protobuf::uint32 value) {
  set_has_default_fixed32();
  default_fixed32_ = value;
  // @@protoc_insertion_point(field_set:protobuf_unittest_no_arena.TestAllTypes.default_fixed32)
}

// optional fixed64 default_fixed64 = 68 [default = 48];
bool TestAllTypes::has_default_fixed64() const {
  return (_has_bits_[1] & 0x04000000u) != 0;
}
void TestAllTypes::set_has_default_fixed64() {
  _has_bits_[1] |= 0x04000000u;
}
void TestAllTypes::clear_has_default_fixed64() {
  _has_bits_[1] &= ~0x04000000u;
}
void TestAllTypes::clear_default_fixed64() {
  default_fixed64_ = GOOGLE_ULONGLONG(48);
  clear_has_default_fixed64();
}
::google::protobuf::uint64 TestAllTypes::default_fixed64() const {
  // @@protoc_insertion_point(field_get:protobuf_unittest_no_arena.TestAllTypes.default_fixed64)
  return default_fixed64_;
}
void TestAllTypes::set_default_fixed64(::google::protobuf::uint64 value) {
  set_has_default_fixed64();
  default_fixed64_ = value;
  // @@protoc_insertion_point(field_set:protobuf_unittest_no_arena.TestAllTypes.default_fixed64)
}

// optional sfixed32 default_sfixed32 = 69 [default = 49];
bool TestAllTypes::has_default_sfixed32() const {
  return (_has_bits_[1] & 0x08000000u) != 0;
}
void TestAllTypes::set_has_default_sfixed32() {
  _has_bits_[1] |= 0x08000000u;
}
void TestAllTypes::clear_has_default_sfixed32() {
  _has_bits_[1] &= ~0x08000000u;
}
void TestAllTypes::clear_default_sfixed32() {
  default_sfixed32_ = 49;
  clear_has_default_sfixed32();
}
::google::protobuf::int32 TestAllTypes::default_sfixed32() const {
  // @@protoc_insertion_point(field_get:protobuf_unittest_no_arena.TestAllTypes.default_sfixed32)
  return default_sfixed32_;
}
void TestAllTypes::set_default_sfixed32(::google::protobuf::int32 value) {
  set_has_default_sfixed32();
  default_sfixed32_ = value;
  // @@protoc_insertion_point(field_set:protobuf_unittest_no_arena.TestAllTypes.default_sfixed32)
}

// optional sfixed64 default_sfixed64 = 70 [default = -50];
bool TestAllTypes::has_default_sfixed64() const {
  return (_has_bits_[1] & 0x10000000u) != 0;
}
void TestAllTypes::set_has_default_sfixed64() {
  _has_bits_[1] |= 0x10000000u;
}
void TestAllTypes::clear_has_default_sfixed64() {
  _has_bits_[1] &= ~0x10000000u;
}
void TestAllTypes::clear_default_sfixed64() {
  default_sfixed64_ = GOOGLE_LONGLONG(-50);
  clear_has_default_sfixed64();
}
::google::protobuf::int64 TestAllTypes::default_sfixed64() const {
  // @@protoc_insertion_point(field_get:protobuf_unittest_no_arena.TestAllTypes.default_sfixed64)
  return default_sfixed64_;
}
void TestAllTypes::set_default_sfixed64(::google::protobuf::int64 value) {
  set_has_default_sfixed64();
  default_sfixed64_ = value;
  // @@protoc_insertion_point(field_set:protobuf_unittest_no_arena.TestAllTypes.default_sfixed64)
}

// optional float default_float = 71 [default = 51.5];
bool TestAllTypes::has_default_float() const {
  return (_has_bits_[1] & 0x20000000u) != 0;
}
void TestAllTypes::set_has_default_float() {
  _has_bits_[1] |= 0x20000000u;
}
void TestAllTypes::clear_has_default_float() {
  _has_bits_[1] &= ~0x20000000u;
}
void TestAllTypes::clear_default_float() {
  default_float_ = 51.5f;
  clear_has_default_float();
}
float TestAllTypes::default_float() const {
  // @@protoc_insertion_point(field_get:protobuf_unittest_no_arena.TestAllTypes.default_float)
  return default_float_;
}
void TestAllTypes::set_default_float(float value) {
  set_has_default_float();
  default_float_ = value;
  // @@protoc_insertion_point(field_set:protobuf_unittest_no_arena.TestAllTypes.default_float)
}

// optional double default_double = 72 [default = 52000];
bool TestAllTypes::has_default_double() const {
  return (_has_bits_[1] & 0x40000000u) != 0;
}
void TestAllTypes::set_has_default_double() {
  _has_bits_[1] |= 0x40000000u;
}
void TestAllTypes::clear_has_default_double() {
  _has_bits_[1] &= ~0x40000000u;
}
void TestAllTypes::clear_default_double() {
  default_double_ = 52000;
  clear_has_default_double();
}
double TestAllTypes::default_double() const {
  // @@protoc_insertion_point(field_get:protobuf_unittest_no_arena.TestAllTypes.default_double)
  return default_double_;
}
void TestAllTypes::set_default_double(double value) {
  set_has_default_double();
  default_double_ = value;
  // @@protoc_insertion_point(field_set:protobuf_unittest_no_arena.TestAllTypes.default_double)
}

// optional bool default_bool = 73 [default = true];
bool TestAllTypes::has_default_bool() const {
  return (_has_bits_[1] & 0x80000000u) != 0;
}
void TestAllTypes::set_has_default_bool() {
  _has_bits_[1] |= 0x80000000u;
}
void TestAllTypes::clear_has_default_bool() {
  _has_bits_[1] &= ~0x80000000u;
}
void TestAllTypes::clear_default_bool() {
  default_bool_ = true;
  clear_has_default_bool();
}
bool TestAllTypes::default_bool() const {
  // @@protoc_insertion_point(field_get:protobuf_unittest_no_arena.TestAllTypes.default_bool)
  return default_bool_;
}
void TestAllTypes::set_default_bool(bool value) {
  set_has_default_bool();
  default_bool_ = value;
  // @@protoc_insertion_point(field_set:protobuf_unittest_no_arena.TestAllTypes.default_bool)
}

// optional string default_string = 74 [default = "hello"];
bool TestAllTypes::has_default_string() const {
  return (_has_bits_[2] & 0x00000001u) != 0;
}
void TestAllTypes::set_has_default_string() {
  _has_bits_[2] |= 0x00000001u;
}
void TestAllTypes::clear_has_default_string() {
  _has_bits_[2] &= ~0x00000001u;
}
void TestAllTypes::clear_default_string() {
  default_string_.ClearToDefaultNoArena(_default_default_string_);
  clear_has_default_string();
}
const ::std::string& TestAllTypes::default_string() const {
  // @@protoc_insertion_point(field_get:protobuf_unittest_no_arena.TestAllTypes.default_string)
  return default_string_.GetNoArena(_default_default_string_);
}
void TestAllTypes::set_default_string(const ::std::string& value) {
  set_has_default_string();
  default_string_.SetNoArena(_default_default_string_, value);
  // @@protoc_insertion_point(field_set:protobuf_unittest_no_arena.TestAllTypes.default_string)
}
void TestAllTypes::set_default_string(const char* value) {
  set_has_default_string();
  default_string_.SetNoArena(_default_default_string_, ::std::string(value));
  // @@protoc_insertion_point(field_set_char:protobuf_unittest_no_arena.TestAllTypes.default_string)
}
void TestAllTypes::set_default_string(const char* value, size_t size) {
  set_has_default_string();
  default_string_.SetNoArena(_default_default_string_,
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:protobuf_unittest_no_arena.TestAllTypes.default_string)
}
::std::string* TestAllTypes::mutable_default_string() {
  set_has_default_string();
  // @@protoc_insertion_point(field_mutable:protobuf_unittest_no_arena.TestAllTypes.default_string)
  return default_string_.MutableNoArena(_default_default_string_);
}
::std::string* TestAllTypes::release_default_string() {
  // @@protoc_insertion_point(field_release:protobuf_unittest_no_arena.TestAllTypes.default_string)
  clear_has_default_string();
  return default_string_.ReleaseNoArena(_default_default_string_);
}
void TestAllTypes::set_allocated_default_string(::std::string* default_string) {
  if (default_string != NULL) {
    set_has_default_string();
  } else {
    clear_has_default_string();
  }
  default_string_.SetAllocatedNoArena(_default_default_string_, default_string);
  // @@protoc_insertion_point(field_set_allocated:protobuf_unittest_no_arena.TestAllTypes.default_string)
}

// optional bytes default_bytes = 75 [default = "world"];
bool TestAllTypes::has_default_bytes() const {
  return (_has_bits_[2] & 0x00000002u) != 0;
}
void TestAllTypes::set_has_default_bytes() {
  _has_bits_[2] |= 0x00000002u;
}
void TestAllTypes::clear_has_default_bytes() {
  _has_bits_[2] &= ~0x00000002u;
}
void TestAllTypes::clear_default_bytes() {
  default_bytes_.ClearToDefaultNoArena(_default_default_bytes_);
  clear_has_default_bytes();
}
const ::std::string& TestAllTypes::default_bytes() const {
  // @@protoc_insertion_point(field_get:protobuf_unittest_no_arena.TestAllTypes.default_bytes)
  return default_bytes_.GetNoArena(_default_default_bytes_);
}
void TestAllTypes::set_default_bytes(const ::std::string& value) {
  set_has_default_bytes();
  default_bytes_.SetNoArena(_default_default_bytes_, value);
  // @@protoc_insertion_point(field_set:protobuf_unittest_no_arena.TestAllTypes.default_bytes)
}
void TestAllTypes::set_default_bytes(const char* value) {
  set_has_default_bytes();
  default_bytes_.SetNoArena(_default_default_bytes_, ::std::string(value));
  // @@protoc_insertion_point(field_set_char:protobuf_unittest_no_arena.TestAllTypes.default_bytes)
}
void TestAllTypes::set_default_bytes(const void* value, size_t size) {
  set_has_default_bytes();
  default_bytes_.SetNoArena(_default_default_bytes_,
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:protobuf_unittest_no_arena.TestAllTypes.default_bytes)
}
::std::string* TestAllTypes::mutable_default_bytes() {
  set_has_default_bytes();
  // @@protoc_insertion_point(field_mutable:protobuf_unittest_no_arena.TestAllTypes.default_bytes)
  return default_bytes_.MutableNoArena(_default_default_bytes_);
}
::std::string* TestAllTypes::release_default_bytes() {
  // @@protoc_insertion_point(field_release:protobuf_unittest_no_arena.TestAllTypes.default_bytes)
  clear_has_default_bytes();
  return default_bytes_.ReleaseNoArena(_default_default_bytes_);
}
void TestAllTypes::set_allocated_default_bytes(::std::string* default_bytes) {
  if (default_bytes != NULL) {
    set_has_default_bytes();
  } else {
    clear_has_default_bytes();
  }
  default_bytes_.SetAllocatedNoArena(_default_default_bytes_, default_bytes);
  // @@protoc_insertion_point(field_set_allocated:protobuf_unittest_no_arena.TestAllTypes.default_bytes)
}

// optional .protobuf_unittest_no_arena.TestAllTypes.NestedEnum default_nested_enum = 81 [default = BAR];
bool TestAllTypes::has_default_nested_enum() const {
  return (_has_bits_[2] & 0x00000004u) != 0;
}
void TestAllTypes::set_has_default_nested_enum() {
  _has_bits_[2] |= 0x00000004u;
}
void TestAllTypes::clear_has_default_nested_enum() {
  _has_bits_[2] &= ~0x00000004u;
}
void TestAllTypes::clear_default_nested_enum() {
  default_nested_enum_ = 2;
  clear_has_default_nested_enum();
}
::protobuf_unittest_no_arena::TestAllTypes_NestedEnum TestAllTypes::default_nested_enum() const {
  // @@protoc_insertion_point(field_get:protobuf_unittest_no_arena.TestAllTypes.default_nested_enum)
  return static_cast< ::protobuf_unittest_no_arena::TestAllTypes_NestedEnum >(default_nested_enum_);
}
void TestAllTypes::set_default_nested_enum(::protobuf_unittest_no_arena::TestAllTypes_NestedEnum value) {
  assert(::protobuf_unittest_no_arena::TestAllTypes_NestedEnum_IsValid(value));
  set_has_default_nested_enum();
  default_nested_enum_ = value;
  // @@protoc_insertion_point(field_set:protobuf_unittest_no_arena.TestAllTypes.default_nested_enum)
}

// optional .protobuf_unittest_no_arena.ForeignEnum default_foreign_enum = 82 [default = FOREIGN_BAR];
bool TestAllTypes::has_default_foreign_enum() const {
  return (_has_bits_[2] & 0x00000008u) != 0;
}
void TestAllTypes::set_has_default_foreign_enum() {
  _has_bits_[2] |= 0x00000008u;
}
void TestAllTypes::clear_has_default_foreign_enum() {
  _has_bits_[2] &= ~0x00000008u;
}
void TestAllTypes::clear_default_foreign_enum() {
  default_foreign_enum_ = 5;
  clear_has_default_foreign_enum();
}
::protobuf_unittest_no_arena::ForeignEnum TestAllTypes::default_foreign_enum() const {
  // @@protoc_insertion_point(field_get:protobuf_unittest_no_arena.TestAllTypes.default_foreign_enum)
  return static_cast< ::protobuf_unittest_no_arena::ForeignEnum >(default_foreign_enum_);
}
void TestAllTypes::set_default_foreign_enum(::protobuf_unittest_no_arena::ForeignEnum value) {
  assert(::protobuf_unittest_no_arena::ForeignEnum_IsValid(value));
  set_has_default_foreign_enum();
  default_foreign_enum_ = value;
  // @@protoc_insertion_point(field_set:protobuf_unittest_no_arena.TestAllTypes.default_foreign_enum)
}

// optional .protobuf_unittest_import.ImportEnum default_import_enum = 83 [default = IMPORT_BAR];
bool TestAllTypes::has_default_import_enum() const {
  return (_has_bits_[2] & 0x00000010u) != 0;
}
void TestAllTypes::set_has_default_import_enum() {
  _has_bits_[2] |= 0x00000010u;
}
void TestAllTypes::clear_has_default_import_enum() {
  _has_bits_[2] &= ~0x00000010u;
}
void TestAllTypes::clear_default_import_enum() {
  default_import_enum_ = 8;
  clear_has_default_import_enum();
}
::protobuf_unittest_import::ImportEnum TestAllTypes::default_import_enum() const {
  // @@protoc_insertion_point(field_get:protobuf_unittest_no_arena.TestAllTypes.default_import_enum)
  return static_cast< ::protobuf_unittest_import::ImportEnum >(default_import_enum_);
}
void TestAllTypes::set_default_import_enum(::protobuf_unittest_import::ImportEnum value) {
  assert(::protobuf_unittest_import::ImportEnum_IsValid(value));
  set_has_default_import_enum();
  default_import_enum_ = value;
  // @@protoc_insertion_point(field_set:protobuf_unittest_no_arena.TestAllTypes.default_import_enum)
}

// optional string default_string_piece = 84 [default = "abc", ctype = STRING_PIECE];
bool TestAllTypes::has_default_string_piece() const {
  return (_has_bits_[2] & 0x00000020u) != 0;
}
void TestAllTypes::set_has_default_string_piece() {
  _has_bits_[2] |= 0x00000020u;
}
void TestAllTypes::clear_has_default_string_piece() {
  _has_bits_[2] &= ~0x00000020u;
}
void TestAllTypes::clear_default_string_piece() {
  default_string_piece_.ClearToDefaultNoArena(_default_default_string_piece_);
  clear_has_default_string_piece();
}
const ::std::string& TestAllTypes::default_string_piece() const {
  // @@protoc_insertion_point(field_get:protobuf_unittest_no_arena.TestAllTypes.default_string_piece)
  return default_string_piece_.GetNoArena(_default_default_string_piece_);
}
void TestAllTypes::set_default_string_piece(const ::std::string& value) {
  set_has_default_string_piece();
  default_string_piece_.SetNoArena(_default_default_string_piece_, value);
  // @@protoc_insertion_point(field_set:protobuf_unittest_no_arena.TestAllTypes.default_string_piece)
}
void TestAllTypes::set_default_string_piece(const char* value) {
  set_has_default_string_piece();
  default_string_piece_.SetNoArena(_default_default_string_piece_, ::std::string(value));
  // @@protoc_insertion_point(field_set_char:protobuf_unittest_no_arena.TestAllTypes.default_string_piece)
}
void TestAllTypes::set_default_string_piece(const char* value, size_t size) {
  set_has_default_string_piece();
  default_string_piece_.SetNoArena(_default_default_string_piece_,
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:protobuf_unittest_no_arena.TestAllTypes.default_string_piece)
}
::std::string* TestAllTypes::mutable_default_string_piece() {
  set_has_default_string_piece();
  // @@protoc_insertion_point(field_mutable:protobuf_unittest_no_arena.TestAllTypes.default_string_piece)
  return default_string_piece_.MutableNoArena(_default_default_string_piece_);
}
::std::string* TestAllTypes::release_default_string_piece() {
  // @@protoc_insertion_point(field_release:protobuf_unittest_no_arena.TestAllTypes.default_string_piece)
  clear_has_default_string_piece();
  return default_string_piece_.ReleaseNoArena(_default_default_string_piece_);
}
void TestAllTypes::set_allocated_default_string_piece(::std::string* default_string_piece) {
  if (default_string_piece != NULL) {
    set_has_default_string_piece();
  } else {
    clear_has_default_string_piece();
  }
  default_string_piece_.SetAllocatedNoArena(_default_default_string_piece_, default_string_piece);
  // @@protoc_insertion_point(field_set_allocated:protobuf_unittest_no_arena.TestAllTypes.default_string_piece)
}

// optional string default_cord = 85 [default = "123", ctype = CORD];
bool TestAllTypes::has_default_cord() const {
  return (_has_bits_[2] & 0x00000040u) != 0;
}
void TestAllTypes::set_has_default_cord() {
  _has_bits_[2] |= 0x00000040u;
}
void TestAllTypes::clear_has_default_cord() {
  _has_bits_[2] &= ~0x00000040u;
}
void TestAllTypes::clear_default_cord() {
  default_cord_.ClearToDefaultNoArena(_default_default_cord_);
  clear_has_default_cord();
}
const ::std::string& TestAllTypes::default_cord() const {
  // @@protoc_insertion_point(field_get:protobuf_unittest_no_arena.TestAllTypes.default_cord)
  return default_cord_.GetNoArena(_default_default_cord_);
}
void TestAllTypes::set_default_cord(const ::std::string& value) {
  set_has_default_cord();
  default_cord_.SetNoArena(_default_default_cord_, value);
  // @@protoc_insertion_point(field_set:protobuf_unittest_no_arena.TestAllTypes.default_cord)
}
void TestAllTypes::set_default_cord(const char* value) {
  set_has_default_cord();
  default_cord_.SetNoArena(_default_default_cord_, ::std::string(value));
  // @@protoc_insertion_point(field_set_char:protobuf_unittest_no_arena.TestAllTypes.default_cord)
}
void TestAllTypes::set_default_cord(const char* value, size_t size) {
  set_has_default_cord();
  default_cord_.SetNoArena(_default_default_cord_,
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:protobuf_unittest_no_arena.TestAllTypes.default_cord)
}
::std::string* TestAllTypes::mutable_default_cord() {
  set_has_default_cord();
  // @@protoc_insertion_point(field_mutable:protobuf_unittest_no_arena.TestAllTypes.default_cord)
  return default_cord_.MutableNoArena(_default_default_cord_);
}
::std::string* TestAllTypes::release_default_cord() {
  // @@protoc_insertion_point(field_release:protobuf_unittest_no_arena.TestAllTypes.default_cord)
  clear_has_default_cord();
  return default_cord_.ReleaseNoArena(_default_default_cord_);
}
void TestAllTypes::set_allocated_default_cord(::std::string* default_cord) {
  if (default_cord != NULL) {
    set_has_default_cord();
  } else {
    clear_has_default_cord();
  }
  default_cord_.SetAllocatedNoArena(_default_default_cord_, default_cord);
  // @@protoc_insertion_point(field_set_allocated:protobuf_unittest_no_arena.TestAllTypes.default_cord)
}

// optional uint32 oneof_uint32 = 111;
bool TestAllTypes::has_oneof_uint32() const {
  return oneof_field_case() == kOneofUint32;
}
void TestAllTypes::set_has_oneof_uint32() {
  _oneof_case_[0] = kOneofUint32;
}
void TestAllTypes::clear_oneof_uint32() {
  if (has_oneof_uint32()) {
    oneof_field_.oneof_uint32_ = 0u;
    clear_has_oneof_field();
  }
}
::google::protobuf::uint32 TestAllTypes::oneof_uint32() const {
  // @@protoc_insertion_point(field_get:protobuf_unittest_no_arena.TestAllTypes.oneof_uint32)
  if (has_oneof_uint32()) {
    return oneof_field_.oneof_uint32_;
  }
  return 0u;
}
void TestAllTypes::set_oneof_uint32(::google::protobuf::uint32 value) {
  if (!has_oneof_uint32()) {
    clear_oneof_field();
    set_has_oneof_uint32();
  }
  oneof_field_.oneof_uint32_ = value;
  // @@protoc_insertion_point(field_set:protobuf_unittest_no_arena.TestAllTypes.oneof_uint32)
}

// optional .protobuf_unittest_no_arena.TestAllTypes.NestedMessage oneof_nested_message = 112;
bool TestAllTypes::has_oneof_nested_message() const {
  return oneof_field_case() == kOneofNestedMessage;
}
void TestAllTypes::set_has_oneof_nested_message() {
  _oneof_case_[0] = kOneofNestedMessage;
}
void TestAllTypes::clear_oneof_nested_message() {
  if (has_oneof_nested_message()) {
    delete oneof_field_.oneof_nested_message_;
    clear_has_oneof_field();
  }
}
 const ::protobuf_unittest_no_arena::TestAllTypes_NestedMessage& TestAllTypes::oneof_nested_message() const {
  // @@protoc_insertion_point(field_get:protobuf_unittest_no_arena.TestAllTypes.oneof_nested_message)
  return has_oneof_nested_message()
      ? *oneof_field_.oneof_nested_message_
      : ::protobuf_unittest_no_arena::TestAllTypes_NestedMessage::default_instance();
}
::protobuf_unittest_no_arena::TestAllTypes_NestedMessage* TestAllTypes::mutable_oneof_nested_message() {
  if (!has_oneof_nested_message()) {
    clear_oneof_field();
    set_has_oneof_nested_message();
    oneof_field_.oneof_nested_message_ = new ::protobuf_unittest_no_arena::TestAllTypes_NestedMessage;
  }
  // @@protoc_insertion_point(field_mutable:protobuf_unittest_no_arena.TestAllTypes.oneof_nested_message)
  return oneof_field_.oneof_nested_message_;
}
::protobuf_unittest_no_arena::TestAllTypes_NestedMessage* TestAllTypes::release_oneof_nested_message() {
  // @@protoc_insertion_point(field_release:protobuf_unittest_no_arena.TestAllTypes.oneof_nested_message)
  if (has_oneof_nested_message()) {
    clear_has_oneof_field();
    ::protobuf_unittest_no_arena::TestAllTypes_NestedMessage* temp = oneof_field_.oneof_nested_message_;
    oneof_field_.oneof_nested_message_ = NULL;
    return temp;
  } else {
    return NULL;
  }
}
void TestAllTypes::set_allocated_oneof_nested_message(::protobuf_unittest_no_arena::TestAllTypes_NestedMessage* oneof_nested_message) {
  clear_oneof_field();
  if (oneof_nested_message) {
    set_has_oneof_nested_message();
    oneof_field_.oneof_nested_message_ = oneof_nested_message;
  }
  // @@protoc_insertion_point(field_set_allocated:protobuf_unittest_no_arena.TestAllTypes.oneof_nested_message)
}

// optional string oneof_string = 113;
bool TestAllTypes::has_oneof_string() const {
  return oneof_field_case() == kOneofString;
}
void TestAllTypes::set_has_oneof_string() {
  _oneof_case_[0] = kOneofString;
}
void TestAllTypes::clear_oneof_string() {
  if (has_oneof_string()) {
    oneof_field_.oneof_string_.DestroyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
    clear_has_oneof_field();
  }
}
const ::std::string& TestAllTypes::oneof_string() const {
  // @@protoc_insertion_point(field_get:protobuf_unittest_no_arena.TestAllTypes.oneof_string)
  if (has_oneof_string()) {
    return oneof_field_.oneof_string_.GetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  }
  return *&::google::protobuf::internal::GetEmptyStringAlreadyInited();
}
void TestAllTypes::set_oneof_string(const ::std::string& value) {
  // @@protoc_insertion_point(field_set:protobuf_unittest_no_arena.TestAllTypes.oneof_string)
  if (!has_oneof_string()) {
    clear_oneof_field();
    set_has_oneof_string();
    oneof_field_.oneof_string_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  }
  oneof_field_.oneof_string_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:protobuf_unittest_no_arena.TestAllTypes.oneof_string)
}
void TestAllTypes::set_oneof_string(const char* value) {
  if (!has_oneof_string()) {
    clear_oneof_field();
    set_has_oneof_string();
    oneof_field_.oneof_string_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  }
  oneof_field_.oneof_string_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(value));
  // @@protoc_insertion_point(field_set_char:protobuf_unittest_no_arena.TestAllTypes.oneof_string)
}
void TestAllTypes::set_oneof_string(const char* value, size_t size) {
  if (!has_oneof_string()) {
    clear_oneof_field();
    set_has_oneof_string();
    oneof_field_.oneof_string_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  }
  oneof_field_.oneof_string_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(
      reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:protobuf_unittest_no_arena.TestAllTypes.oneof_string)
}
::std::string* TestAllTypes::mutable_oneof_string() {
  if (!has_oneof_string()) {
    clear_oneof_field();
    set_has_oneof_string();
    oneof_field_.oneof_string_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  }
  // @@protoc_insertion_point(field_mutable:protobuf_unittest_no_arena.TestAllTypes.oneof_string)
  return oneof_field_.oneof_string_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
::std::string* TestAllTypes::release_oneof_string() {
  // @@protoc_insertion_point(field_release:protobuf_unittest_no_arena.TestAllTypes.oneof_string)
  if (has_oneof_string()) {
    clear_has_oneof_field();
    return oneof_field_.oneof_string_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  } else {
    return NULL;
  }
}
void TestAllTypes::set_allocated_oneof_string(::std::string* oneof_string) {
  if (!has_oneof_string()) {
    oneof_field_.oneof_string_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  }
  clear_oneof_field();
  if (oneof_string != NULL) {
    set_has_oneof_string();
    oneof_field_.oneof_string_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
        oneof_string);
  }
  // @@protoc_insertion_point(field_set_allocated:protobuf_unittest_no_arena.TestAllTypes.oneof_string)
}

// optional bytes oneof_bytes = 114;
bool TestAllTypes::has_oneof_bytes() const {
  return oneof_field_case() == kOneofBytes;
}
void TestAllTypes::set_has_oneof_bytes() {
  _oneof_case_[0] = kOneofBytes;
}
void TestAllTypes::clear_oneof_bytes() {
  if (has_oneof_bytes()) {
    oneof_field_.oneof_bytes_.DestroyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
    clear_has_oneof_field();
  }
}
const ::std::string& TestAllTypes::oneof_bytes() const {
  // @@protoc_insertion_point(field_get:protobuf_unittest_no_arena.TestAllTypes.oneof_bytes)
  if (has_oneof_bytes()) {
    return oneof_field_.oneof_bytes_.GetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  }
  return *&::google::protobuf::internal::GetEmptyStringAlreadyInited();
}
void TestAllTypes::set_oneof_bytes(const ::std::string& value) {
  // @@protoc_insertion_point(field_set:protobuf_unittest_no_arena.TestAllTypes.oneof_bytes)
  if (!has_oneof_bytes()) {
    clear_oneof_field();
    set_has_oneof_bytes();
    oneof_field_.oneof_bytes_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  }
  oneof_field_.oneof_bytes_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:protobuf_unittest_no_arena.TestAllTypes.oneof_bytes)
}
void TestAllTypes::set_oneof_bytes(const char* value) {
  if (!has_oneof_bytes()) {
    clear_oneof_field();
    set_has_oneof_bytes();
    oneof_field_.oneof_bytes_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  }
  oneof_field_.oneof_bytes_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(value));
  // @@protoc_insertion_point(field_set_char:protobuf_unittest_no_arena.TestAllTypes.oneof_bytes)
}
void TestAllTypes::set_oneof_bytes(const void* value, size_t size) {
  if (!has_oneof_bytes()) {
    clear_oneof_field();
    set_has_oneof_bytes();
    oneof_field_.oneof_bytes_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  }
  oneof_field_.oneof_bytes_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(
      reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:protobuf_unittest_no_arena.TestAllTypes.oneof_bytes)
}
::std::string* TestAllTypes::mutable_oneof_bytes() {
  if (!has_oneof_bytes()) {
    clear_oneof_field();
    set_has_oneof_bytes();
    oneof_field_.oneof_bytes_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  }
  // @@protoc_insertion_point(field_mutable:protobuf_unittest_no_arena.TestAllTypes.oneof_bytes)
  return oneof_field_.oneof_bytes_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
::std::string* TestAllTypes::release_oneof_bytes() {
  // @@protoc_insertion_point(field_release:protobuf_unittest_no_arena.TestAllTypes.oneof_bytes)
  if (has_oneof_bytes()) {
    clear_has_oneof_field();
    return oneof_field_.oneof_bytes_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  } else {
    return NULL;
  }
}
void TestAllTypes::set_allocated_oneof_bytes(::std::string* oneof_bytes) {
  if (!has_oneof_bytes()) {
    oneof_field_.oneof_bytes_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  }
  clear_oneof_field();
  if (oneof_bytes != NULL) {
    set_has_oneof_bytes();
    oneof_field_.oneof_bytes_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
        oneof_bytes);
  }
  // @@protoc_insertion_point(field_set_allocated:protobuf_unittest_no_arena.TestAllTypes.oneof_bytes)
}

// optional .protobuf_unittest_no_arena.TestAllTypes.NestedMessage lazy_oneof_nested_message = 115 [lazy = true];
bool TestAllTypes::has_lazy_oneof_nested_message() const {
  return oneof_field_case() == kLazyOneofNestedMessage;
}
void TestAllTypes::set_has_lazy_oneof_nested_message() {
  _oneof_case_[0] = kLazyOneofNestedMessage;
}
void TestAllTypes::clear_lazy_oneof_nested_message() {
  if (has_lazy_oneof_nested_message()) {
    delete oneof_field_.lazy_oneof_nested_message_;
    clear_has_oneof_field();
  }
}
 const ::protobuf_unittest_no_arena::TestAllTypes_NestedMessage& TestAllTypes::lazy_oneof_nested_message() const {
  // @@protoc_insertion_point(field_get:protobuf_unittest_no_arena.TestAllTypes.lazy_oneof_nested_message)
  return has_lazy_oneof_nested_message()
      ? *oneof_field_.lazy_oneof_nested_message_
      : ::protobuf_unittest_no_arena::TestAllTypes_NestedMessage::default_instance();
}
::protobuf_unittest_no_arena::TestAllTypes_NestedMessage* TestAllTypes::mutable_lazy_oneof_nested_message() {
  if (!has_lazy_oneof_nested_message()) {
    clear_oneof_field();
    set_has_lazy_oneof_nested_message();
    oneof_field_.lazy_oneof_nested_message_ = new ::protobuf_unittest_no_arena::TestAllTypes_NestedMessage;
  }
  // @@protoc_insertion_point(field_mutable:protobuf_unittest_no_arena.TestAllTypes.lazy_oneof_nested_message)
  return oneof_field_.lazy_oneof_nested_message_;
}
::protobuf_unittest_no_arena::TestAllTypes_NestedMessage* TestAllTypes::release_lazy_oneof_nested_message() {
  // @@protoc_insertion_point(field_release:protobuf_unittest_no_arena.TestAllTypes.lazy_oneof_nested_message)
  if (has_lazy_oneof_nested_message()) {
    clear_has_oneof_field();
    ::protobuf_unittest_no_arena::TestAllTypes_NestedMessage* temp = oneof_field_.lazy_oneof_nested_message_;
    oneof_field_.lazy_oneof_nested_message_ = NULL;
    return temp;
  } else {
    return NULL;
  }
}
void TestAllTypes::set_allocated_lazy_oneof_nested_message(::protobuf_unittest_no_arena::TestAllTypes_NestedMessage* lazy_oneof_nested_message) {
  clear_oneof_field();
  if (lazy_oneof_nested_message) {
    set_has_lazy_oneof_nested_message();
    oneof_field_.lazy_oneof_nested_message_ = lazy_oneof_nested_message;
  }
  // @@protoc_insertion_point(field_set_allocated:protobuf_unittest_no_arena.TestAllTypes.lazy_oneof_nested_message)
}

bool TestAllTypes::has_oneof_field() const {
  return oneof_field_case() != ONEOF_FIELD_NOT_SET;
}
void TestAllTypes::clear_has_oneof_field() {
  _oneof_case_[0] = ONEOF_FIELD_NOT_SET;
}
TestAllTypes::OneofFieldCase TestAllTypes::oneof_field_case() const {
  return TestAllTypes::OneofFieldCase(_oneof_case_[0]);
}
inline const TestAllTypes* TestAllTypes::internal_default_instance() {
  return &TestAllTypes_default_instance_.get();
}
#endif  // PROTOBUF_INLINE_NOT_IN_HEADERS

// ===================================================================

#if !defined(_MSC_VER) || _MSC_VER >= 1900
const int ForeignMessage::kCFieldNumber;
#endif  // !defined(_MSC_VER) || _MSC_VER >= 1900

ForeignMessage::ForeignMessage()
  : ::google::protobuf::Message(), _internal_metadata_(NULL) {
  if (this != internal_default_instance()) protobuf_InitDefaults_google_2fprotobuf_2funittest_5fno_5farena_2eproto();
  SharedCtor();
  // @@protoc_insertion_point(constructor:protobuf_unittest_no_arena.ForeignMessage)
}

void ForeignMessage::InitAsDefaultInstance() {
}

ForeignMessage::ForeignMessage(const ForeignMessage& from)
  : ::google::protobuf::Message(),
    _internal_metadata_(NULL) {
  SharedCtor();
  UnsafeMergeFrom(from);
  // @@protoc_insertion_point(copy_constructor:protobuf_unittest_no_arena.ForeignMessage)
}

void ForeignMessage::SharedCtor() {
  _cached_size_ = 0;
  c_ = 0;
}

ForeignMessage::~ForeignMessage() {
  // @@protoc_insertion_point(destructor:protobuf_unittest_no_arena.ForeignMessage)
  SharedDtor();
}

void ForeignMessage::SharedDtor() {
}

void ForeignMessage::SetCachedSize(int size) const {
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
}
const ::google::protobuf::Descriptor* ForeignMessage::descriptor() {
  protobuf_AssignDescriptorsOnce();
  return ForeignMessage_descriptor_;
}

const ForeignMessage& ForeignMessage::default_instance() {
  protobuf_InitDefaults_google_2fprotobuf_2funittest_5fno_5farena_2eproto();
  return *internal_default_instance();
}

::google::protobuf::internal::ExplicitlyConstructed<ForeignMessage> ForeignMessage_default_instance_;

ForeignMessage* ForeignMessage::New(::google::protobuf::Arena* arena) const {
  ForeignMessage* n = new ForeignMessage;
  if (arena != NULL) {
    arena->Own(n);
  }
  return n;
}

void ForeignMessage::Clear() {
// @@protoc_insertion_point(message_clear_start:protobuf_unittest_no_arena.ForeignMessage)
  c_ = 0;
  _has_bits_.Clear();
  if (_internal_metadata_.have_unknown_fields()) {
    mutable_unknown_fields()->Clear();
  }
}

bool ForeignMessage::MergePartialFromCodedStream(
    ::google::protobuf::io::CodedInputStream* input) {
#define DO_(EXPRESSION) if (!GOOGLE_PREDICT_TRUE(EXPRESSION)) goto failure
  ::google::protobuf::uint32 tag;
  // @@protoc_insertion_point(parse_start:protobuf_unittest_no_arena.ForeignMessage)
  for (;;) {
    ::std::pair< ::google::protobuf::uint32, bool> p = input->ReadTagWithCutoff(127);
    tag = p.first;
    if (!p.second) goto handle_unusual;
    switch (::google::protobuf::internal::WireFormatLite::GetTagFieldNumber(tag)) {
      // optional int32 c = 1;
      case 1: {
        if (tag == 8) {
          set_has_c();
          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int32, ::google::protobuf::internal::WireFormatLite::TYPE_INT32>(
                 input, &c_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectAtEnd()) goto success;
        break;
      }

      default: {
      handle_unusual:
        if (tag == 0 ||
            ::google::protobuf::internal::WireFormatLite::GetTagWireType(tag) ==
            ::google::protobuf::internal::WireFormatLite::WIRETYPE_END_GROUP) {
          goto success;
        }
        DO_(::google::protobuf::internal::WireFormat::SkipField(
              input, tag, mutable_unknown_fields()));
        break;
      }
    }
  }
success:
  // @@protoc_insertion_point(parse_success:protobuf_unittest_no_arena.ForeignMessage)
  return true;
failure:
  // @@protoc_insertion_point(parse_failure:protobuf_unittest_no_arena.ForeignMessage)
  return false;
#undef DO_
}

void ForeignMessage::SerializeWithCachedSizes(
    ::google::protobuf::io::CodedOutputStream* output) const {
  // @@protoc_insertion_point(serialize_start:protobuf_unittest_no_arena.ForeignMessage)
  // optional int32 c = 1;
  if (has_c()) {
    ::google::protobuf::internal::WireFormatLite::WriteInt32(1, this->c(), output);
  }

  if (_internal_metadata_.have_unknown_fields()) {
    ::google::protobuf::internal::WireFormat::SerializeUnknownFields(
        unknown_fields(), output);
  }
  // @@protoc_insertion_point(serialize_end:protobuf_unittest_no_arena.ForeignMessage)
}

::google::protobuf::uint8* ForeignMessage::InternalSerializeWithCachedSizesToArray(
    bool deterministic, ::google::protobuf::uint8* target) const {
  (void)deterministic; // Unused
  // @@protoc_insertion_point(serialize_to_array_start:protobuf_unittest_no_arena.ForeignMessage)
  // optional int32 c = 1;
  if (has_c()) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt32ToArray(1, this->c(), target);
  }

  if (_internal_metadata_.have_unknown_fields()) {
    target = ::google::protobuf::internal::WireFormat::SerializeUnknownFieldsToArray(
        unknown_fields(), target);
  }
  // @@protoc_insertion_point(serialize_to_array_end:protobuf_unittest_no_arena.ForeignMessage)
  return target;
}

size_t ForeignMessage::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:protobuf_unittest_no_arena.ForeignMessage)
  size_t total_size = 0;

  // optional int32 c = 1;
  if (has_c()) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::Int32Size(
        this->c());
  }

  if (_internal_metadata_.have_unknown_fields()) {
    total_size +=
      ::google::protobuf::internal::WireFormat::ComputeUnknownFieldsSize(
        unknown_fields());
  }
  int cached_size = ::google::protobuf::internal::ToCachedSize(total_size);
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = cached_size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
  return total_size;
}

void ForeignMessage::MergeFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_merge_from_start:protobuf_unittest_no_arena.ForeignMessage)
  if (GOOGLE_PREDICT_FALSE(&from == this)) MergeFromFail(__LINE__);
  const ForeignMessage* source =
      ::google::protobuf::internal::DynamicCastToGenerated<const ForeignMessage>(
          &from);
  if (source == NULL) {
  // @@protoc_insertion_point(generalized_merge_from_cast_fail:protobuf_unittest_no_arena.ForeignMessage)
    ::google::protobuf::internal::ReflectionOps::Merge(from, this);
  } else {
  // @@protoc_insertion_point(generalized_merge_from_cast_success:protobuf_unittest_no_arena.ForeignMessage)
    UnsafeMergeFrom(*source);
  }
}

void ForeignMessage::MergeFrom(const ForeignMessage& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:protobuf_unittest_no_arena.ForeignMessage)
  if (GOOGLE_PREDICT_TRUE(&from != this)) {
    UnsafeMergeFrom(from);
  } else {
    MergeFromFail(__LINE__);
  }
}

void ForeignMessage::UnsafeMergeFrom(const ForeignMessage& from) {
  GOOGLE_DCHECK(&from != this);
  if (from._has_bits_[0 / 32] & (0xffu << (0 % 32))) {
    if (from.has_c()) {
      set_c(from.c());
    }
  }
  if (from._internal_metadata_.have_unknown_fields()) {
    ::google::protobuf::UnknownFieldSet::MergeToInternalMetdata(
      from.unknown_fields(), &_internal_metadata_);
  }
}

void ForeignMessage::CopyFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_copy_from_start:protobuf_unittest_no_arena.ForeignMessage)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

void ForeignMessage::CopyFrom(const ForeignMessage& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:protobuf_unittest_no_arena.ForeignMessage)
  if (&from == this) return;
  Clear();
  UnsafeMergeFrom(from);
}

bool ForeignMessage::IsInitialized() const {

  return true;
}

void ForeignMessage::Swap(ForeignMessage* other) {
  if (other == this) return;
  InternalSwap(other);
}
void ForeignMessage::InternalSwap(ForeignMessage* other) {
  std::swap(c_, other->c_);
  std::swap(_has_bits_[0], other->_has_bits_[0]);
  _internal_metadata_.Swap(&other->_internal_metadata_);
  std::swap(_cached_size_, other->_cached_size_);
}

::google::protobuf::Metadata ForeignMessage::GetMetadata() const {
  protobuf_AssignDescriptorsOnce();
  ::google::protobuf::Metadata metadata;
  metadata.descriptor = ForeignMessage_descriptor_;
  metadata.reflection = ForeignMessage_reflection_;
  return metadata;
}

#if PROTOBUF_INLINE_NOT_IN_HEADERS
// ForeignMessage

// optional int32 c = 1;
bool ForeignMessage::has_c() const {
  return (_has_bits_[0] & 0x00000001u) != 0;
}
void ForeignMessage::set_has_c() {
  _has_bits_[0] |= 0x00000001u;
}
void ForeignMessage::clear_has_c() {
  _has_bits_[0] &= ~0x00000001u;
}
void ForeignMessage::clear_c() {
  c_ = 0;
  clear_has_c();
}
::google::protobuf::int32 ForeignMessage::c() const {
  // @@protoc_insertion_point(field_get:protobuf_unittest_no_arena.ForeignMessage.c)
  return c_;
}
void ForeignMessage::set_c(::google::protobuf::int32 value) {
  set_has_c();
  c_ = value;
  // @@protoc_insertion_point(field_set:protobuf_unittest_no_arena.ForeignMessage.c)
}

inline const ForeignMessage* ForeignMessage::internal_default_instance() {
  return &ForeignMessage_default_instance_.get();
}
#endif  // PROTOBUF_INLINE_NOT_IN_HEADERS

// ===================================================================

#if !defined(_MSC_VER) || _MSC_VER >= 1900
const int TestNoArenaMessage::kArenaMessageFieldNumber;
#endif  // !defined(_MSC_VER) || _MSC_VER >= 1900

TestNoArenaMessage::TestNoArenaMessage()
  : ::google::protobuf::Message(), _internal_metadata_(NULL) {
  if (this != internal_default_instance()) protobuf_InitDefaults_google_2fprotobuf_2funittest_5fno_5farena_2eproto();
  SharedCtor();
  // @@protoc_insertion_point(constructor:protobuf_unittest_no_arena.TestNoArenaMessage)
}

void TestNoArenaMessage::InitAsDefaultInstance() {
  arena_message_ = const_cast< ::proto2_arena_unittest::ArenaMessage*>(
      ::proto2_arena_unittest::ArenaMessage::internal_default_instance());
}

TestNoArenaMessage::TestNoArenaMessage(const TestNoArenaMessage& from)
  : ::google::protobuf::Message(),
    _internal_metadata_(NULL) {
  SharedCtor();
  UnsafeMergeFrom(from);
  // @@protoc_insertion_point(copy_constructor:protobuf_unittest_no_arena.TestNoArenaMessage)
}

void TestNoArenaMessage::SharedCtor() {
  _cached_size_ = 0;
  arena_message_ = NULL;
}

TestNoArenaMessage::~TestNoArenaMessage() {
  // @@protoc_insertion_point(destructor:protobuf_unittest_no_arena.TestNoArenaMessage)
  SharedDtor();
}

void TestNoArenaMessage::SharedDtor() {
  if (this != &TestNoArenaMessage_default_instance_.get()) {
    delete arena_message_;
  }
}

void TestNoArenaMessage::SetCachedSize(int size) const {
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
}
const ::google::protobuf::Descriptor* TestNoArenaMessage::descriptor() {
  protobuf_AssignDescriptorsOnce();
  return TestNoArenaMessage_descriptor_;
}

const TestNoArenaMessage& TestNoArenaMessage::default_instance() {
  protobuf_InitDefaults_google_2fprotobuf_2funittest_5fno_5farena_2eproto();
  return *internal_default_instance();
}

::google::protobuf::internal::ExplicitlyConstructed<TestNoArenaMessage> TestNoArenaMessage_default_instance_;

TestNoArenaMessage* TestNoArenaMessage::New(::google::protobuf::Arena* arena) const {
  TestNoArenaMessage* n = new TestNoArenaMessage;
  if (arena != NULL) {
    arena->Own(n);
  }
  return n;
}

void TestNoArenaMessage::Clear() {
// @@protoc_insertion_point(message_clear_start:protobuf_unittest_no_arena.TestNoArenaMessage)
  if (has_arena_message()) {
    if (arena_message_ != NULL) arena_message_->::proto2_arena_unittest::ArenaMessage::Clear();
  }
  _has_bits_.Clear();
  if (_internal_metadata_.have_unknown_fields()) {
    mutable_unknown_fields()->Clear();
  }
}

bool TestNoArenaMessage::MergePartialFromCodedStream(
    ::google::protobuf::io::CodedInputStream* input) {
#define DO_(EXPRESSION) if (!GOOGLE_PREDICT_TRUE(EXPRESSION)) goto failure
  ::google::protobuf::uint32 tag;
  // @@protoc_insertion_point(parse_start:protobuf_unittest_no_arena.TestNoArenaMessage)
  for (;;) {
    ::std::pair< ::google::protobuf::uint32, bool> p = input->ReadTagWithCutoff(127);
    tag = p.first;
    if (!p.second) goto handle_unusual;
    switch (::google::protobuf::internal::WireFormatLite::GetTagFieldNumber(tag)) {
      // optional .proto2_arena_unittest.ArenaMessage arena_message = 1;
      case 1: {
        if (tag == 10) {
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessageNoVirtual(
               input, mutable_arena_message()));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectAtEnd()) goto success;
        break;
      }

      default: {
      handle_unusual:
        if (tag == 0 ||
            ::google::protobuf::internal::WireFormatLite::GetTagWireType(tag) ==
            ::google::protobuf::internal::WireFormatLite::WIRETYPE_END_GROUP) {
          goto success;
        }
        DO_(::google::protobuf::internal::WireFormat::SkipField(
              input, tag, mutable_unknown_fields()));
        break;
      }
    }
  }
success:
  // @@protoc_insertion_point(parse_success:protobuf_unittest_no_arena.TestNoArenaMessage)
  return true;
failure:
  // @@protoc_insertion_point(parse_failure:protobuf_unittest_no_arena.TestNoArenaMessage)
  return false;
#undef DO_
}

void TestNoArenaMessage::SerializeWithCachedSizes(
    ::google::protobuf::io::CodedOutputStream* output) const {
  // @@protoc_insertion_point(serialize_start:protobuf_unittest_no_arena.TestNoArenaMessage)
  // optional .proto2_arena_unittest.ArenaMessage arena_message = 1;
  if (has_arena_message()) {
    ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
      1, *this->arena_message_, output);
  }

  if (_internal_metadata_.have_unknown_fields()) {
    ::google::protobuf::internal::WireFormat::SerializeUnknownFields(
        unknown_fields(), output);
  }
  // @@protoc_insertion_point(serialize_end:protobuf_unittest_no_arena.TestNoArenaMessage)
}

::google::protobuf::uint8* TestNoArenaMessage::InternalSerializeWithCachedSizesToArray(
    bool deterministic, ::google::protobuf::uint8* target) const {
  (void)deterministic; // Unused
  // @@protoc_insertion_point(serialize_to_array_start:protobuf_unittest_no_arena.TestNoArenaMessage)
  // optional .proto2_arena_unittest.ArenaMessage arena_message = 1;
  if (has_arena_message()) {
    target = ::google::protobuf::internal::WireFormatLite::
      InternalWriteMessageNoVirtualToArray(
        1, *this->arena_message_, false, target);
  }

  if (_internal_metadata_.have_unknown_fields()) {
    target = ::google::protobuf::internal::WireFormat::SerializeUnknownFieldsToArray(
        unknown_fields(), target);
  }
  // @@protoc_insertion_point(serialize_to_array_end:protobuf_unittest_no_arena.TestNoArenaMessage)
  return target;
}

size_t TestNoArenaMessage::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:protobuf_unittest_no_arena.TestNoArenaMessage)
  size_t total_size = 0;

  // optional .proto2_arena_unittest.ArenaMessage arena_message = 1;
  if (has_arena_message()) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::MessageSizeNoVirtual(
        *this->arena_message_);
  }

  if (_internal_metadata_.have_unknown_fields()) {
    total_size +=
      ::google::protobuf::internal::WireFormat::ComputeUnknownFieldsSize(
        unknown_fields());
  }
  int cached_size = ::google::protobuf::internal::ToCachedSize(total_size);
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = cached_size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
  return total_size;
}

void TestNoArenaMessage::MergeFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_merge_from_start:protobuf_unittest_no_arena.TestNoArenaMessage)
  if (GOOGLE_PREDICT_FALSE(&from == this)) MergeFromFail(__LINE__);
  const TestNoArenaMessage* source =
      ::google::protobuf::internal::DynamicCastToGenerated<const TestNoArenaMessage>(
          &from);
  if (source == NULL) {
  // @@protoc_insertion_point(generalized_merge_from_cast_fail:protobuf_unittest_no_arena.TestNoArenaMessage)
    ::google::protobuf::internal::ReflectionOps::Merge(from, this);
  } else {
  // @@protoc_insertion_point(generalized_merge_from_cast_success:protobuf_unittest_no_arena.TestNoArenaMessage)
    UnsafeMergeFrom(*source);
  }
}

void TestNoArenaMessage::MergeFrom(const TestNoArenaMessage& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:protobuf_unittest_no_arena.TestNoArenaMessage)
  if (GOOGLE_PREDICT_TRUE(&from != this)) {
    UnsafeMergeFrom(from);
  } else {
    MergeFromFail(__LINE__);
  }
}

void TestNoArenaMessage::UnsafeMergeFrom(const TestNoArenaMessage& from) {
  GOOGLE_DCHECK(&from != this);
  if (from._has_bits_[0 / 32] & (0xffu << (0 % 32))) {
    if (from.has_arena_message()) {
      mutable_arena_message()->::proto2_arena_unittest::ArenaMessage::MergeFrom(from.arena_message());
    }
  }
  if (from._internal_metadata_.have_unknown_fields()) {
    ::google::protobuf::UnknownFieldSet::MergeToInternalMetdata(
      from.unknown_fields(), &_internal_metadata_);
  }
}

void TestNoArenaMessage::CopyFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_copy_from_start:protobuf_unittest_no_arena.TestNoArenaMessage)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

void TestNoArenaMessage::CopyFrom(const TestNoArenaMessage& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:protobuf_unittest_no_arena.TestNoArenaMessage)
  if (&from == this) return;
  Clear();
  UnsafeMergeFrom(from);
}

bool TestNoArenaMessage::IsInitialized() const {

  return true;
}

void TestNoArenaMessage::Swap(TestNoArenaMessage* other) {
  if (other == this) return;
  InternalSwap(other);
}
void TestNoArenaMessage::InternalSwap(TestNoArenaMessage* other) {
  std::swap(arena_message_, other->arena_message_);
  std::swap(_has_bits_[0], other->_has_bits_[0]);
  _internal_metadata_.Swap(&other->_internal_metadata_);
  std::swap(_cached_size_, other->_cached_size_);
}

::google::protobuf::Metadata TestNoArenaMessage::GetMetadata() const {
  protobuf_AssignDescriptorsOnce();
  ::google::protobuf::Metadata metadata;
  metadata.descriptor = TestNoArenaMessage_descriptor_;
  metadata.reflection = TestNoArenaMessage_reflection_;
  return metadata;
}

#if PROTOBUF_INLINE_NOT_IN_HEADERS
// TestNoArenaMessage

// optional .proto2_arena_unittest.ArenaMessage arena_message = 1;
bool TestNoArenaMessage::has_arena_message() const {
  return (_has_bits_[0] & 0x00000001u) != 0;
}
void TestNoArenaMessage::set_has_arena_message() {
  _has_bits_[0] |= 0x00000001u;
}
void TestNoArenaMessage::clear_has_arena_message() {
  _has_bits_[0] &= ~0x00000001u;
}
void TestNoArenaMessage::clear_arena_message() {
  if (arena_message_ != NULL) arena_message_->::proto2_arena_unittest::ArenaMessage::Clear();
  clear_has_arena_message();
}
const ::proto2_arena_unittest::ArenaMessage& TestNoArenaMessage::arena_message() const {
  // @@protoc_insertion_point(field_get:protobuf_unittest_no_arena.TestNoArenaMessage.arena_message)
  return arena_message_ != NULL ? *arena_message_
                         : *::proto2_arena_unittest::ArenaMessage::internal_default_instance();
}
::proto2_arena_unittest::ArenaMessage* TestNoArenaMessage::mutable_arena_message() {
  set_has_arena_message();
  if (arena_message_ == NULL) {
    arena_message_ = new ::proto2_arena_unittest::ArenaMessage;
  }
  // @@protoc_insertion_point(field_mutable:protobuf_unittest_no_arena.TestNoArenaMessage.arena_message)
  return arena_message_;
}
::proto2_arena_unittest::ArenaMessage* TestNoArenaMessage::release_arena_message() {
  // @@protoc_insertion_point(field_release:protobuf_unittest_no_arena.TestNoArenaMessage.arena_message)
  clear_has_arena_message();
  ::proto2_arena_unittest::ArenaMessage* temp = arena_message_;
  arena_message_ = NULL;
  return temp;
}
void TestNoArenaMessage::set_allocated_arena_message(::proto2_arena_unittest::ArenaMessage* arena_message) {
  delete arena_message_;
  if (arena_message != NULL && arena_message->GetArena() != NULL) {
    ::proto2_arena_unittest::ArenaMessage* new_arena_message = new ::proto2_arena_unittest::ArenaMessage;
    new_arena_message->CopyFrom(*arena_message);
    arena_message = new_arena_message;
  }
  arena_message_ = arena_message;
  if (arena_message) {
    set_has_arena_message();
  } else {
    clear_has_arena_message();
  }
  // @@protoc_insertion_point(field_set_allocated:protobuf_unittest_no_arena.TestNoArenaMessage.arena_message)
}

inline const TestNoArenaMessage* TestNoArenaMessage::internal_default_instance() {
  return &TestNoArenaMessage_default_instance_.get();
}
#endif  // PROTOBUF_INLINE_NOT_IN_HEADERS

// @@protoc_insertion_point(namespace_scope)

}  // namespace protobuf_unittest_no_arena

// @@protoc_insertion_point(global_scope)

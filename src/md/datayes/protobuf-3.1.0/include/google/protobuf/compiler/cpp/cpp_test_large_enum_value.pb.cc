// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: google/protobuf/compiler/cpp/cpp_test_large_enum_value.proto

#define INTERNAL_SUPPRESS_PROTOBUF_FIELD_DEPRECATION
#include <google/protobuf/compiler/cpp/cpp_test_large_enum_value.pb.h>

#include <algorithm>

#include <google/protobuf/stubs/common.h>
#include <google/protobuf/stubs/port.h>
#include <google/protobuf/stubs/once.h>
#include <google/protobuf/io/coded_stream.h>
#include <google/protobuf/wire_format_lite_inl.h>
#include <google/protobuf/descriptor.h>
#include <google/protobuf/generated_message_reflection.h>
#include <google/protobuf/reflection_ops.h>
#include <google/protobuf/wire_format.h>
// @@protoc_insertion_point(includes)

namespace protobuf_unittest {

namespace {

const ::google::protobuf::Descriptor* TestLargeEnumValue_descriptor_ = NULL;
const ::google::protobuf::internal::GeneratedMessageReflection*
  TestLargeEnumValue_reflection_ = NULL;
const ::google::protobuf::EnumDescriptor* TestLargeEnumValue_EnumWithLargeValue_descriptor_ = NULL;

}  // namespace


void protobuf_AssignDesc_google_2fprotobuf_2fcompiler_2fcpp_2fcpp_5ftest_5flarge_5fenum_5fvalue_2eproto() GOOGLE_ATTRIBUTE_COLD;
void protobuf_AssignDesc_google_2fprotobuf_2fcompiler_2fcpp_2fcpp_5ftest_5flarge_5fenum_5fvalue_2eproto() {
  protobuf_AddDesc_google_2fprotobuf_2fcompiler_2fcpp_2fcpp_5ftest_5flarge_5fenum_5fvalue_2eproto();
  const ::google::protobuf::FileDescriptor* file =
    ::google::protobuf::DescriptorPool::generated_pool()->FindFileByName(
      "google/protobuf/compiler/cpp/cpp_test_large_enum_value.proto");
  GOOGLE_CHECK(file != NULL);
  TestLargeEnumValue_descriptor_ = file->message_type(0);
  static const int TestLargeEnumValue_offsets_[1] = {
  };
  TestLargeEnumValue_reflection_ =
    ::google::protobuf::internal::GeneratedMessageReflection::NewGeneratedMessageReflection(
      TestLargeEnumValue_descriptor_,
      TestLargeEnumValue::internal_default_instance(),
      TestLargeEnumValue_offsets_,
      GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(TestLargeEnumValue, _has_bits_),
      -1,
      -1,
      sizeof(TestLargeEnumValue),
      GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(TestLargeEnumValue, _internal_metadata_));
  TestLargeEnumValue_EnumWithLargeValue_descriptor_ = TestLargeEnumValue_descriptor_->enum_type(0);
}

namespace {

GOOGLE_PROTOBUF_DECLARE_ONCE(protobuf_AssignDescriptors_once_);
void protobuf_AssignDescriptorsOnce() {
  ::google::protobuf::GoogleOnceInit(&protobuf_AssignDescriptors_once_,
                 &protobuf_AssignDesc_google_2fprotobuf_2fcompiler_2fcpp_2fcpp_5ftest_5flarge_5fenum_5fvalue_2eproto);
}

void protobuf_RegisterTypes(const ::std::string&) GOOGLE_ATTRIBUTE_COLD;
void protobuf_RegisterTypes(const ::std::string&) {
  protobuf_AssignDescriptorsOnce();
  ::google::protobuf::MessageFactory::InternalRegisterGeneratedMessage(
      TestLargeEnumValue_descriptor_, TestLargeEnumValue::internal_default_instance());
}

}  // namespace

void protobuf_ShutdownFile_google_2fprotobuf_2fcompiler_2fcpp_2fcpp_5ftest_5flarge_5fenum_5fvalue_2eproto() {
  TestLargeEnumValue_default_instance_.Shutdown();
  delete TestLargeEnumValue_reflection_;
}

void protobuf_InitDefaults_google_2fprotobuf_2fcompiler_2fcpp_2fcpp_5ftest_5flarge_5fenum_5fvalue_2eproto_impl() {
  GOOGLE_PROTOBUF_VERIFY_VERSION;

  TestLargeEnumValue_default_instance_.DefaultConstruct();
  TestLargeEnumValue_default_instance_.get_mutable()->InitAsDefaultInstance();
}

GOOGLE_PROTOBUF_DECLARE_ONCE(protobuf_InitDefaults_google_2fprotobuf_2fcompiler_2fcpp_2fcpp_5ftest_5flarge_5fenum_5fvalue_2eproto_once_);
void protobuf_InitDefaults_google_2fprotobuf_2fcompiler_2fcpp_2fcpp_5ftest_5flarge_5fenum_5fvalue_2eproto() {
  ::google::protobuf::GoogleOnceInit(&protobuf_InitDefaults_google_2fprotobuf_2fcompiler_2fcpp_2fcpp_5ftest_5flarge_5fenum_5fvalue_2eproto_once_,
                 &protobuf_InitDefaults_google_2fprotobuf_2fcompiler_2fcpp_2fcpp_5ftest_5flarge_5fenum_5fvalue_2eproto_impl);
}
void protobuf_AddDesc_google_2fprotobuf_2fcompiler_2fcpp_2fcpp_5ftest_5flarge_5fenum_5fvalue_2eproto_impl() {
  GOOGLE_PROTOBUF_VERIFY_VERSION;

  protobuf_InitDefaults_google_2fprotobuf_2fcompiler_2fcpp_2fcpp_5ftest_5flarge_5fenum_5fvalue_2eproto();
  ::google::protobuf::DescriptorPool::InternalAddGeneratedFile(
    "\n<google/protobuf/compiler/cpp/cpp_test_"
    "large_enum_value.proto\022\021protobuf_unittes"
    "t\"J\n\022TestLargeEnumValue\"4\n\022EnumWithLarge"
    "Value\022\013\n\007VALUE_1\020\001\022\021\n\tVALUE_MAX\020\377\377\377\377\007", 157);
  ::google::protobuf::MessageFactory::InternalRegisterGeneratedFile(
    "google/protobuf/compiler/cpp/cpp_test_large_enum_value.proto", &protobuf_RegisterTypes);
  ::google::protobuf::internal::OnShutdown(&protobuf_ShutdownFile_google_2fprotobuf_2fcompiler_2fcpp_2fcpp_5ftest_5flarge_5fenum_5fvalue_2eproto);
}

GOOGLE_PROTOBUF_DECLARE_ONCE(protobuf_AddDesc_google_2fprotobuf_2fcompiler_2fcpp_2fcpp_5ftest_5flarge_5fenum_5fvalue_2eproto_once_);
void protobuf_AddDesc_google_2fprotobuf_2fcompiler_2fcpp_2fcpp_5ftest_5flarge_5fenum_5fvalue_2eproto() {
  ::google::protobuf::GoogleOnceInit(&protobuf_AddDesc_google_2fprotobuf_2fcompiler_2fcpp_2fcpp_5ftest_5flarge_5fenum_5fvalue_2eproto_once_,
                 &protobuf_AddDesc_google_2fprotobuf_2fcompiler_2fcpp_2fcpp_5ftest_5flarge_5fenum_5fvalue_2eproto_impl);
}
// Force AddDescriptors() to be called at static initialization time.
struct StaticDescriptorInitializer_google_2fprotobuf_2fcompiler_2fcpp_2fcpp_5ftest_5flarge_5fenum_5fvalue_2eproto {
  StaticDescriptorInitializer_google_2fprotobuf_2fcompiler_2fcpp_2fcpp_5ftest_5flarge_5fenum_5fvalue_2eproto() {
    protobuf_AddDesc_google_2fprotobuf_2fcompiler_2fcpp_2fcpp_5ftest_5flarge_5fenum_5fvalue_2eproto();
  }
} static_descriptor_initializer_google_2fprotobuf_2fcompiler_2fcpp_2fcpp_5ftest_5flarge_5fenum_5fvalue_2eproto_;

namespace {

static void MergeFromFail(int line) GOOGLE_ATTRIBUTE_COLD GOOGLE_ATTRIBUTE_NORETURN;
static void MergeFromFail(int line) {
  ::google::protobuf::internal::MergeFromFail(__FILE__, line);
}

}  // namespace


// ===================================================================

const ::google::protobuf::EnumDescriptor* TestLargeEnumValue_EnumWithLargeValue_descriptor() {
  protobuf_AssignDescriptorsOnce();
  return TestLargeEnumValue_EnumWithLargeValue_descriptor_;
}
bool TestLargeEnumValue_EnumWithLargeValue_IsValid(int value) {
  switch (value) {
    case 1:
    case 2147483647:
      return true;
    default:
      return false;
  }
}

#if !defined(_MSC_VER) || _MSC_VER >= 1900
const TestLargeEnumValue_EnumWithLargeValue TestLargeEnumValue::VALUE_1;
const TestLargeEnumValue_EnumWithLargeValue TestLargeEnumValue::VALUE_MAX;
const TestLargeEnumValue_EnumWithLargeValue TestLargeEnumValue::EnumWithLargeValue_MIN;
const TestLargeEnumValue_EnumWithLargeValue TestLargeEnumValue::EnumWithLargeValue_MAX;
#endif  // !defined(_MSC_VER) || _MSC_VER >= 1900
#if !defined(_MSC_VER) || _MSC_VER >= 1900
#endif  // !defined(_MSC_VER) || _MSC_VER >= 1900

TestLargeEnumValue::TestLargeEnumValue()
  : ::google::protobuf::Message(), _internal_metadata_(NULL) {
  if (this != internal_default_instance()) protobuf_InitDefaults_google_2fprotobuf_2fcompiler_2fcpp_2fcpp_5ftest_5flarge_5fenum_5fvalue_2eproto();
  SharedCtor();
  // @@protoc_insertion_point(constructor:protobuf_unittest.TestLargeEnumValue)
}

void TestLargeEnumValue::InitAsDefaultInstance() {
}

TestLargeEnumValue::TestLargeEnumValue(const TestLargeEnumValue& from)
  : ::google::protobuf::Message(),
    _internal_metadata_(NULL) {
  SharedCtor();
  UnsafeMergeFrom(from);
  // @@protoc_insertion_point(copy_constructor:protobuf_unittest.TestLargeEnumValue)
}

void TestLargeEnumValue::SharedCtor() {
  _cached_size_ = 0;
}

TestLargeEnumValue::~TestLargeEnumValue() {
  // @@protoc_insertion_point(destructor:protobuf_unittest.TestLargeEnumValue)
  SharedDtor();
}

void TestLargeEnumValue::SharedDtor() {
}

void TestLargeEnumValue::SetCachedSize(int size) const {
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
}
const ::google::protobuf::Descriptor* TestLargeEnumValue::descriptor() {
  protobuf_AssignDescriptorsOnce();
  return TestLargeEnumValue_descriptor_;
}

const TestLargeEnumValue& TestLargeEnumValue::default_instance() {
  protobuf_InitDefaults_google_2fprotobuf_2fcompiler_2fcpp_2fcpp_5ftest_5flarge_5fenum_5fvalue_2eproto();
  return *internal_default_instance();
}

::google::protobuf::internal::ExplicitlyConstructed<TestLargeEnumValue> TestLargeEnumValue_default_instance_;

TestLargeEnumValue* TestLargeEnumValue::New(::google::protobuf::Arena* arena) const {
  TestLargeEnumValue* n = new TestLargeEnumValue;
  if (arena != NULL) {
    arena->Own(n);
  }
  return n;
}

void TestLargeEnumValue::Clear() {
// @@protoc_insertion_point(message_clear_start:protobuf_unittest.TestLargeEnumValue)
  _has_bits_.Clear();
  if (_internal_metadata_.have_unknown_fields()) {
    mutable_unknown_fields()->Clear();
  }
}

bool TestLargeEnumValue::MergePartialFromCodedStream(
    ::google::protobuf::io::CodedInputStream* input) {
#define DO_(EXPRESSION) if (!GOOGLE_PREDICT_TRUE(EXPRESSION)) goto failure
  ::google::protobuf::uint32 tag;
  // @@protoc_insertion_point(parse_start:protobuf_unittest.TestLargeEnumValue)
  for (;;) {
    ::std::pair< ::google::protobuf::uint32, bool> p = input->ReadTagWithCutoff(127);
    tag = p.first;
    if (!p.second) goto handle_unusual;
  handle_unusual:
    if (tag == 0 ||
        ::google::protobuf::internal::WireFormatLite::GetTagWireType(tag) ==
        ::google::protobuf::internal::WireFormatLite::WIRETYPE_END_GROUP) {
      goto success;
    }
    DO_(::google::protobuf::internal::WireFormat::SkipField(
          input, tag, mutable_unknown_fields()));
  }
success:
  // @@protoc_insertion_point(parse_success:protobuf_unittest.TestLargeEnumValue)
  return true;
failure:
  // @@protoc_insertion_point(parse_failure:protobuf_unittest.TestLargeEnumValue)
  return false;
#undef DO_
}

void TestLargeEnumValue::SerializeWithCachedSizes(
    ::google::protobuf::io::CodedOutputStream* output) const {
  // @@protoc_insertion_point(serialize_start:protobuf_unittest.TestLargeEnumValue)
  if (_internal_metadata_.have_unknown_fields()) {
    ::google::protobuf::internal::WireFormat::SerializeUnknownFields(
        unknown_fields(), output);
  }
  // @@protoc_insertion_point(serialize_end:protobuf_unittest.TestLargeEnumValue)
}

::google::protobuf::uint8* TestLargeEnumValue::InternalSerializeWithCachedSizesToArray(
    bool deterministic, ::google::protobuf::uint8* target) const {
  (void)deterministic; // Unused
  // @@protoc_insertion_point(serialize_to_array_start:protobuf_unittest.TestLargeEnumValue)
  if (_internal_metadata_.have_unknown_fields()) {
    target = ::google::protobuf::internal::WireFormat::SerializeUnknownFieldsToArray(
        unknown_fields(), target);
  }
  // @@protoc_insertion_point(serialize_to_array_end:protobuf_unittest.TestLargeEnumValue)
  return target;
}

size_t TestLargeEnumValue::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:protobuf_unittest.TestLargeEnumValue)
  size_t total_size = 0;

  if (_internal_metadata_.have_unknown_fields()) {
    total_size +=
      ::google::protobuf::internal::WireFormat::ComputeUnknownFieldsSize(
        unknown_fields());
  }
  int cached_size = ::google::protobuf::internal::ToCachedSize(total_size);
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = cached_size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
  return total_size;
}

void TestLargeEnumValue::MergeFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_merge_from_start:protobuf_unittest.TestLargeEnumValue)
  if (GOOGLE_PREDICT_FALSE(&from == this)) MergeFromFail(__LINE__);
  const TestLargeEnumValue* source =
      ::google::protobuf::internal::DynamicCastToGenerated<const TestLargeEnumValue>(
          &from);
  if (source == NULL) {
  // @@protoc_insertion_point(generalized_merge_from_cast_fail:protobuf_unittest.TestLargeEnumValue)
    ::google::protobuf::internal::ReflectionOps::Merge(from, this);
  } else {
  // @@protoc_insertion_point(generalized_merge_from_cast_success:protobuf_unittest.TestLargeEnumValue)
    UnsafeMergeFrom(*source);
  }
}

void TestLargeEnumValue::MergeFrom(const TestLargeEnumValue& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:protobuf_unittest.TestLargeEnumValue)
  if (GOOGLE_PREDICT_TRUE(&from != this)) {
    UnsafeMergeFrom(from);
  } else {
    MergeFromFail(__LINE__);
  }
}

void TestLargeEnumValue::UnsafeMergeFrom(const TestLargeEnumValue& from) {
  GOOGLE_DCHECK(&from != this);
  if (from._internal_metadata_.have_unknown_fields()) {
    ::google::protobuf::UnknownFieldSet::MergeToInternalMetdata(
      from.unknown_fields(), &_internal_metadata_);
  }
}

void TestLargeEnumValue::CopyFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_copy_from_start:protobuf_unittest.TestLargeEnumValue)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

void TestLargeEnumValue::CopyFrom(const TestLargeEnumValue& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:protobuf_unittest.TestLargeEnumValue)
  if (&from == this) return;
  Clear();
  UnsafeMergeFrom(from);
}

bool TestLargeEnumValue::IsInitialized() const {

  return true;
}

void TestLargeEnumValue::Swap(TestLargeEnumValue* other) {
  if (other == this) return;
  InternalSwap(other);
}
void TestLargeEnumValue::InternalSwap(TestLargeEnumValue* other) {
  _internal_metadata_.Swap(&other->_internal_metadata_);
  std::swap(_cached_size_, other->_cached_size_);
}

::google::protobuf::Metadata TestLargeEnumValue::GetMetadata() const {
  protobuf_AssignDescriptorsOnce();
  ::google::protobuf::Metadata metadata;
  metadata.descriptor = TestLargeEnumValue_descriptor_;
  metadata.reflection = TestLargeEnumValue_reflection_;
  return metadata;
}

#if PROTOBUF_INLINE_NOT_IN_HEADERS
// TestLargeEnumValue

inline const TestLargeEnumValue* TestLargeEnumValue::internal_default_instance() {
  return &TestLargeEnumValue_default_instance_.get();
}
#endif  // PROTOBUF_INLINE_NOT_IN_HEADERS

// @@protoc_insertion_point(namespace_scope)

}  // namespace protobuf_unittest

// @@protoc_insertion_point(global_scope)

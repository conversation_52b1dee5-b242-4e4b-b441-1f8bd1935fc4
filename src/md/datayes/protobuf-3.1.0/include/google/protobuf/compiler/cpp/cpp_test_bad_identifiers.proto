// Protocol Buffers - Google's data interchange format
// Copyright 2008 Google Inc.  All rights reserved.
// https://developers.google.com/protocol-buffers/
//
// Redistribution and use in source and binary forms, with or without
// modification, are permitted provided that the following conditions are
// met:
//
//     * Redistributions of source code must retain the above copyright
// notice, this list of conditions and the following disclaimer.
//     * Redistributions in binary form must reproduce the above
// copyright notice, this list of conditions and the following disclaimer
// in the documentation and/or other materials provided with the
// distribution.
//     * Neither the name of Google Inc. nor the names of its
// contributors may be used to endorse or promote products derived from
// this software without specific prior written permission.
//
// THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS
// "AS IS" AND ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT
// LIMITED TO, THE IMPLIED WARRANTIES OF MERCHANTABILITY AND FITNESS FOR
// A PARTICULAR PURPOSE ARE DISCLAIMED. IN NO EVENT SHALL THE COPYRIGHT
// OWNER OR CONTRIBUTORS BE LIABLE FOR ANY DIRECT, INDIRECT, INCIDENTAL,
// SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES (INCLUDING, BUT NOT
// LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES; LOSS OF USE,
// DATA, OR PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND ON ANY
// THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT
// (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT OF THE USE
// OF THIS SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.

// Author: <EMAIL> (Kenton Varda)
//  Based on original Protocol Buffers design by
//  Sanjay Ghemawat, Jeff Dean, and others.
//
// This file tests that various identifiers work as field and type names even
// though the same identifiers are used internally by the C++ code generator.

syntax = "proto2";

// Some generic_services option(s) added automatically.
// See:  http://go/proto2-generic-services-default
option cc_generic_services = true;     // auto-added

// We don't put this in a package within proto2 because we need to make sure
// that the generated code doesn't depend on being in the proto2 namespace.
package protobuf_unittest;

// Test that fields can have names like "input" and "i" which are also used
// internally by the code generator for local variables.
message TestConflictingSymbolNames {
  message BuildDescriptors {}
  message TypeTraits {}

  optional int32 input = 1;
  optional int32 output = 2;
  optional string length = 3;
  repeated int32 i = 4;
  repeated string new_element = 5 [ctype=STRING_PIECE];
  optional int32 total_size = 6;
  optional int32 tag = 7;

  enum TestEnum { FOO = 0; }
  message Data1 { repeated int32 data = 1; }
  message Data2 { repeated TestEnum data = 1; }
  message Data3 { repeated string data = 1; }
  message Data4 { repeated Data4 data = 1; }
  message Data5 { repeated string data = 1 [ctype=STRING_PIECE]; }
  message Data6 { repeated string data = 1 [ctype=CORD]; }

  optional int32 source = 8;
  optional int32 value = 9;
  optional int32 file = 10;
  optional int32 from = 11;
  optional int32 handle_uninterpreted = 12;
  repeated int32 index = 13;
  optional int32 controller = 14;
  optional int32 already_here = 15;

  optional uint32 uint32 = 16;
  optional uint64 uint64 = 17;
  optional string string = 18;
  optional int32 memset = 19;
  optional int32 int32 = 20;
  optional int64 int64 = 21;

  optional uint32 cached_size = 22;
  optional uint32 extensions = 23;
  optional uint32 bit = 24;
  optional uint32 bits = 25;
  optional uint32 offsets = 26;
  optional uint32 reflection = 27;

  message Cord {}
  optional string some_cord = 28 [ctype=CORD];

  message StringPiece {}
  optional string some_string_piece = 29 [ctype=STRING_PIECE];

  // Some keywords.
  optional uint32 int = 30;
  optional uint32 friend = 31;
  optional uint32 class = 37;
  optional uint32 typedecl = 39;
  optional uint32 auto = 40;

  // The generator used to #define a macro called "DO" inside the .cc file.
  message DO {}
  optional DO do = 32;

  // Some template parameter names for extensions.
  optional int32 field_type = 33;
  optional bool is_packed = 34;

  // test conflicting release_$name$. "length" and "do" field in this message
  // must remain string or message fields to make the test valid.
  optional string release_length = 35;
  // A more extreme case, the field name "do" here is a keyword, which will be
  // escaped to "do_" already. Test there is no conflict even with escaped field
  // names.
  optional DO release_do = 36;

  // For clashing local variables in Serialize and ByteSize calculation.
  optional string target = 38;

  extensions 1000 to max;  // NO_PROTO3
}

message TestConflictingSymbolNamesExtension {                    // NO_PROTO3
  extend TestConflictingSymbolNames {                            // NO_PROTO3
    repeated int32 repeated_int32_ext = 20423638 [packed=true];  // NO_PROTO3
  }                                                              // NO_PROTO3
}                                                                // NO_PROTO3

message TestConflictingEnumNames {  // NO_PROTO3
  enum NestedConflictingEnum {      // NO_PROTO3
    and = 1;                        // NO_PROTO3
    class = 2;                      // NO_PROTO3
    int = 3;                        // NO_PROTO3
    typedef = 4;                    // NO_PROTO3
    XOR = 5;                        // NO_PROTO3
  }                                 // NO_PROTO3

  optional NestedConflictingEnum conflicting_enum = 1;  // NO_PROTO3
}  // NO_PROTO3

enum ConflictingEnum {  // NO_PROTO3
  NOT_EQ = 1;           // NO_PROTO3
  volatile = 2;         // NO_PROTO3
  return = 3;           // NO_PROTO3
}  // NO_PROTO3

message DummyMessage {}

service TestConflictingMethodNames {
  rpc Closure(DummyMessage) returns (DummyMessage);
}

// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: google/protobuf/compiler/cpp/cpp_test_bad_identifiers.proto

#define INTERNAL_SUPPRESS_PROTOBUF_FIELD_DEPRECATION
#include <google/protobuf/compiler/cpp/cpp_test_bad_identifiers.pb.h>

#include <algorithm>

#include <google/protobuf/stubs/common.h>
#include <google/protobuf/stubs/port.h>
#include <google/protobuf/stubs/once.h>
#include <google/protobuf/io/coded_stream.h>
#include <google/protobuf/wire_format_lite_inl.h>
#include <google/protobuf/descriptor.h>
#include <google/protobuf/generated_message_reflection.h>
#include <google/protobuf/reflection_ops.h>
#include <google/protobuf/wire_format.h>
// @@protoc_insertion_point(includes)

namespace protobuf_unittest {

namespace {

const ::google::protobuf::Descriptor* TestConflictingSymbolNames_descriptor_ = NULL;
const ::google::protobuf::internal::GeneratedMessageReflection*
  TestConflictingSymbolNames_reflection_ = NULL;
const ::google::protobuf::Descriptor* TestConflictingSymbolNames_BuildDescriptors_descriptor_ = NULL;
const ::google::protobuf::internal::GeneratedMessageReflection*
  TestConflictingSymbolNames_BuildDescriptors_reflection_ = NULL;
const ::google::protobuf::Descriptor* TestConflictingSymbolNames_TypeTraits_descriptor_ = NULL;
const ::google::protobuf::internal::GeneratedMessageReflection*
  TestConflictingSymbolNames_TypeTraits_reflection_ = NULL;
const ::google::protobuf::Descriptor* TestConflictingSymbolNames_Data1_descriptor_ = NULL;
const ::google::protobuf::internal::GeneratedMessageReflection*
  TestConflictingSymbolNames_Data1_reflection_ = NULL;
const ::google::protobuf::Descriptor* TestConflictingSymbolNames_Data2_descriptor_ = NULL;
const ::google::protobuf::internal::GeneratedMessageReflection*
  TestConflictingSymbolNames_Data2_reflection_ = NULL;
const ::google::protobuf::Descriptor* TestConflictingSymbolNames_Data3_descriptor_ = NULL;
const ::google::protobuf::internal::GeneratedMessageReflection*
  TestConflictingSymbolNames_Data3_reflection_ = NULL;
const ::google::protobuf::Descriptor* TestConflictingSymbolNames_Data4_descriptor_ = NULL;
const ::google::protobuf::internal::GeneratedMessageReflection*
  TestConflictingSymbolNames_Data4_reflection_ = NULL;
const ::google::protobuf::Descriptor* TestConflictingSymbolNames_Data5_descriptor_ = NULL;
const ::google::protobuf::internal::GeneratedMessageReflection*
  TestConflictingSymbolNames_Data5_reflection_ = NULL;
const ::google::protobuf::Descriptor* TestConflictingSymbolNames_Data6_descriptor_ = NULL;
const ::google::protobuf::internal::GeneratedMessageReflection*
  TestConflictingSymbolNames_Data6_reflection_ = NULL;
const ::google::protobuf::Descriptor* TestConflictingSymbolNames_Cord_descriptor_ = NULL;
const ::google::protobuf::internal::GeneratedMessageReflection*
  TestConflictingSymbolNames_Cord_reflection_ = NULL;
const ::google::protobuf::Descriptor* TestConflictingSymbolNames_StringPiece_descriptor_ = NULL;
const ::google::protobuf::internal::GeneratedMessageReflection*
  TestConflictingSymbolNames_StringPiece_reflection_ = NULL;
const ::google::protobuf::Descriptor* TestConflictingSymbolNames_DO_descriptor_ = NULL;
const ::google::protobuf::internal::GeneratedMessageReflection*
  TestConflictingSymbolNames_DO_reflection_ = NULL;
const ::google::protobuf::EnumDescriptor* TestConflictingSymbolNames_TestEnum_descriptor_ = NULL;
const ::google::protobuf::Descriptor* TestConflictingSymbolNamesExtension_descriptor_ = NULL;
const ::google::protobuf::internal::GeneratedMessageReflection*
  TestConflictingSymbolNamesExtension_reflection_ = NULL;
const ::google::protobuf::Descriptor* TestConflictingEnumNames_descriptor_ = NULL;
const ::google::protobuf::internal::GeneratedMessageReflection*
  TestConflictingEnumNames_reflection_ = NULL;
const ::google::protobuf::EnumDescriptor* TestConflictingEnumNames_NestedConflictingEnum_descriptor_ = NULL;
const ::google::protobuf::Descriptor* DummyMessage_descriptor_ = NULL;
const ::google::protobuf::internal::GeneratedMessageReflection*
  DummyMessage_reflection_ = NULL;
const ::google::protobuf::EnumDescriptor* ConflictingEnum_descriptor_ = NULL;
const ::google::protobuf::ServiceDescriptor* TestConflictingMethodNames_descriptor_ = NULL;

}  // namespace


void protobuf_AssignDesc_google_2fprotobuf_2fcompiler_2fcpp_2fcpp_5ftest_5fbad_5fidentifiers_2eproto() GOOGLE_ATTRIBUTE_COLD;
void protobuf_AssignDesc_google_2fprotobuf_2fcompiler_2fcpp_2fcpp_5ftest_5fbad_5fidentifiers_2eproto() {
  protobuf_AddDesc_google_2fprotobuf_2fcompiler_2fcpp_2fcpp_5ftest_5fbad_5fidentifiers_2eproto();
  const ::google::protobuf::FileDescriptor* file =
    ::google::protobuf::DescriptorPool::generated_pool()->FindFileByName(
      "google/protobuf/compiler/cpp/cpp_test_bad_identifiers.proto");
  GOOGLE_CHECK(file != NULL);
  TestConflictingSymbolNames_descriptor_ = file->message_type(0);
  static const int TestConflictingSymbolNames_offsets_[40] = {
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(TestConflictingSymbolNames, input_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(TestConflictingSymbolNames, output_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(TestConflictingSymbolNames, length_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(TestConflictingSymbolNames, i_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(TestConflictingSymbolNames, new_element_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(TestConflictingSymbolNames, total_size_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(TestConflictingSymbolNames, tag_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(TestConflictingSymbolNames, source_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(TestConflictingSymbolNames, value_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(TestConflictingSymbolNames, file_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(TestConflictingSymbolNames, from_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(TestConflictingSymbolNames, handle_uninterpreted_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(TestConflictingSymbolNames, index_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(TestConflictingSymbolNames, controller_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(TestConflictingSymbolNames, already_here_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(TestConflictingSymbolNames, uint32_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(TestConflictingSymbolNames, uint64_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(TestConflictingSymbolNames, string_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(TestConflictingSymbolNames, memset_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(TestConflictingSymbolNames, int32_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(TestConflictingSymbolNames, int64_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(TestConflictingSymbolNames, cached_size_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(TestConflictingSymbolNames, extensions_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(TestConflictingSymbolNames, bit_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(TestConflictingSymbolNames, bits_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(TestConflictingSymbolNames, offsets_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(TestConflictingSymbolNames, reflection_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(TestConflictingSymbolNames, some_cord_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(TestConflictingSymbolNames, some_string_piece_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(TestConflictingSymbolNames, int__),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(TestConflictingSymbolNames, friend__),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(TestConflictingSymbolNames, class__),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(TestConflictingSymbolNames, typedecl_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(TestConflictingSymbolNames, auto__),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(TestConflictingSymbolNames, do__),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(TestConflictingSymbolNames, field_type_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(TestConflictingSymbolNames, is_packed_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(TestConflictingSymbolNames, release_length_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(TestConflictingSymbolNames, release_do_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(TestConflictingSymbolNames, target_),
  };
  TestConflictingSymbolNames_reflection_ =
    ::google::protobuf::internal::GeneratedMessageReflection::NewGeneratedMessageReflection(
      TestConflictingSymbolNames_descriptor_,
      TestConflictingSymbolNames::internal_default_instance(),
      TestConflictingSymbolNames_offsets_,
      GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(TestConflictingSymbolNames, _has_bits_),
      -1,
      GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(TestConflictingSymbolNames, _extensions_),
      sizeof(TestConflictingSymbolNames),
      GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(TestConflictingSymbolNames, _internal_metadata_));
  TestConflictingSymbolNames_BuildDescriptors_descriptor_ = TestConflictingSymbolNames_descriptor_->nested_type(0);
  static const int TestConflictingSymbolNames_BuildDescriptors_offsets_[1] = {
  };
  TestConflictingSymbolNames_BuildDescriptors_reflection_ =
    ::google::protobuf::internal::GeneratedMessageReflection::NewGeneratedMessageReflection(
      TestConflictingSymbolNames_BuildDescriptors_descriptor_,
      TestConflictingSymbolNames_BuildDescriptors::internal_default_instance(),
      TestConflictingSymbolNames_BuildDescriptors_offsets_,
      GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(TestConflictingSymbolNames_BuildDescriptors, _has_bits_),
      -1,
      -1,
      sizeof(TestConflictingSymbolNames_BuildDescriptors),
      GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(TestConflictingSymbolNames_BuildDescriptors, _internal_metadata_));
  TestConflictingSymbolNames_TypeTraits_descriptor_ = TestConflictingSymbolNames_descriptor_->nested_type(1);
  static const int TestConflictingSymbolNames_TypeTraits_offsets_[1] = {
  };
  TestConflictingSymbolNames_TypeTraits_reflection_ =
    ::google::protobuf::internal::GeneratedMessageReflection::NewGeneratedMessageReflection(
      TestConflictingSymbolNames_TypeTraits_descriptor_,
      TestConflictingSymbolNames_TypeTraits::internal_default_instance(),
      TestConflictingSymbolNames_TypeTraits_offsets_,
      GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(TestConflictingSymbolNames_TypeTraits, _has_bits_),
      -1,
      -1,
      sizeof(TestConflictingSymbolNames_TypeTraits),
      GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(TestConflictingSymbolNames_TypeTraits, _internal_metadata_));
  TestConflictingSymbolNames_Data1_descriptor_ = TestConflictingSymbolNames_descriptor_->nested_type(2);
  static const int TestConflictingSymbolNames_Data1_offsets_[1] = {
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(TestConflictingSymbolNames_Data1, data_),
  };
  TestConflictingSymbolNames_Data1_reflection_ =
    ::google::protobuf::internal::GeneratedMessageReflection::NewGeneratedMessageReflection(
      TestConflictingSymbolNames_Data1_descriptor_,
      TestConflictingSymbolNames_Data1::internal_default_instance(),
      TestConflictingSymbolNames_Data1_offsets_,
      GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(TestConflictingSymbolNames_Data1, _has_bits_),
      -1,
      -1,
      sizeof(TestConflictingSymbolNames_Data1),
      GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(TestConflictingSymbolNames_Data1, _internal_metadata_));
  TestConflictingSymbolNames_Data2_descriptor_ = TestConflictingSymbolNames_descriptor_->nested_type(3);
  static const int TestConflictingSymbolNames_Data2_offsets_[1] = {
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(TestConflictingSymbolNames_Data2, data_),
  };
  TestConflictingSymbolNames_Data2_reflection_ =
    ::google::protobuf::internal::GeneratedMessageReflection::NewGeneratedMessageReflection(
      TestConflictingSymbolNames_Data2_descriptor_,
      TestConflictingSymbolNames_Data2::internal_default_instance(),
      TestConflictingSymbolNames_Data2_offsets_,
      GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(TestConflictingSymbolNames_Data2, _has_bits_),
      -1,
      -1,
      sizeof(TestConflictingSymbolNames_Data2),
      GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(TestConflictingSymbolNames_Data2, _internal_metadata_));
  TestConflictingSymbolNames_Data3_descriptor_ = TestConflictingSymbolNames_descriptor_->nested_type(4);
  static const int TestConflictingSymbolNames_Data3_offsets_[1] = {
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(TestConflictingSymbolNames_Data3, data_),
  };
  TestConflictingSymbolNames_Data3_reflection_ =
    ::google::protobuf::internal::GeneratedMessageReflection::NewGeneratedMessageReflection(
      TestConflictingSymbolNames_Data3_descriptor_,
      TestConflictingSymbolNames_Data3::internal_default_instance(),
      TestConflictingSymbolNames_Data3_offsets_,
      GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(TestConflictingSymbolNames_Data3, _has_bits_),
      -1,
      -1,
      sizeof(TestConflictingSymbolNames_Data3),
      GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(TestConflictingSymbolNames_Data3, _internal_metadata_));
  TestConflictingSymbolNames_Data4_descriptor_ = TestConflictingSymbolNames_descriptor_->nested_type(5);
  static const int TestConflictingSymbolNames_Data4_offsets_[1] = {
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(TestConflictingSymbolNames_Data4, data_),
  };
  TestConflictingSymbolNames_Data4_reflection_ =
    ::google::protobuf::internal::GeneratedMessageReflection::NewGeneratedMessageReflection(
      TestConflictingSymbolNames_Data4_descriptor_,
      TestConflictingSymbolNames_Data4::internal_default_instance(),
      TestConflictingSymbolNames_Data4_offsets_,
      GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(TestConflictingSymbolNames_Data4, _has_bits_),
      -1,
      -1,
      sizeof(TestConflictingSymbolNames_Data4),
      GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(TestConflictingSymbolNames_Data4, _internal_metadata_));
  TestConflictingSymbolNames_Data5_descriptor_ = TestConflictingSymbolNames_descriptor_->nested_type(6);
  static const int TestConflictingSymbolNames_Data5_offsets_[1] = {
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(TestConflictingSymbolNames_Data5, data_),
  };
  TestConflictingSymbolNames_Data5_reflection_ =
    ::google::protobuf::internal::GeneratedMessageReflection::NewGeneratedMessageReflection(
      TestConflictingSymbolNames_Data5_descriptor_,
      TestConflictingSymbolNames_Data5::internal_default_instance(),
      TestConflictingSymbolNames_Data5_offsets_,
      GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(TestConflictingSymbolNames_Data5, _has_bits_),
      -1,
      -1,
      sizeof(TestConflictingSymbolNames_Data5),
      GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(TestConflictingSymbolNames_Data5, _internal_metadata_));
  TestConflictingSymbolNames_Data6_descriptor_ = TestConflictingSymbolNames_descriptor_->nested_type(7);
  static const int TestConflictingSymbolNames_Data6_offsets_[1] = {
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(TestConflictingSymbolNames_Data6, data_),
  };
  TestConflictingSymbolNames_Data6_reflection_ =
    ::google::protobuf::internal::GeneratedMessageReflection::NewGeneratedMessageReflection(
      TestConflictingSymbolNames_Data6_descriptor_,
      TestConflictingSymbolNames_Data6::internal_default_instance(),
      TestConflictingSymbolNames_Data6_offsets_,
      GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(TestConflictingSymbolNames_Data6, _has_bits_),
      -1,
      -1,
      sizeof(TestConflictingSymbolNames_Data6),
      GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(TestConflictingSymbolNames_Data6, _internal_metadata_));
  TestConflictingSymbolNames_Cord_descriptor_ = TestConflictingSymbolNames_descriptor_->nested_type(8);
  static const int TestConflictingSymbolNames_Cord_offsets_[1] = {
  };
  TestConflictingSymbolNames_Cord_reflection_ =
    ::google::protobuf::internal::GeneratedMessageReflection::NewGeneratedMessageReflection(
      TestConflictingSymbolNames_Cord_descriptor_,
      TestConflictingSymbolNames_Cord::internal_default_instance(),
      TestConflictingSymbolNames_Cord_offsets_,
      GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(TestConflictingSymbolNames_Cord, _has_bits_),
      -1,
      -1,
      sizeof(TestConflictingSymbolNames_Cord),
      GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(TestConflictingSymbolNames_Cord, _internal_metadata_));
  TestConflictingSymbolNames_StringPiece_descriptor_ = TestConflictingSymbolNames_descriptor_->nested_type(9);
  static const int TestConflictingSymbolNames_StringPiece_offsets_[1] = {
  };
  TestConflictingSymbolNames_StringPiece_reflection_ =
    ::google::protobuf::internal::GeneratedMessageReflection::NewGeneratedMessageReflection(
      TestConflictingSymbolNames_StringPiece_descriptor_,
      TestConflictingSymbolNames_StringPiece::internal_default_instance(),
      TestConflictingSymbolNames_StringPiece_offsets_,
      GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(TestConflictingSymbolNames_StringPiece, _has_bits_),
      -1,
      -1,
      sizeof(TestConflictingSymbolNames_StringPiece),
      GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(TestConflictingSymbolNames_StringPiece, _internal_metadata_));
  TestConflictingSymbolNames_DO_descriptor_ = TestConflictingSymbolNames_descriptor_->nested_type(10);
  static const int TestConflictingSymbolNames_DO_offsets_[1] = {
  };
  TestConflictingSymbolNames_DO_reflection_ =
    ::google::protobuf::internal::GeneratedMessageReflection::NewGeneratedMessageReflection(
      TestConflictingSymbolNames_DO_descriptor_,
      TestConflictingSymbolNames_DO::internal_default_instance(),
      TestConflictingSymbolNames_DO_offsets_,
      GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(TestConflictingSymbolNames_DO, _has_bits_),
      -1,
      -1,
      sizeof(TestConflictingSymbolNames_DO),
      GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(TestConflictingSymbolNames_DO, _internal_metadata_));
  TestConflictingSymbolNames_TestEnum_descriptor_ = TestConflictingSymbolNames_descriptor_->enum_type(0);
  TestConflictingSymbolNamesExtension_descriptor_ = file->message_type(1);
  static const int TestConflictingSymbolNamesExtension_offsets_[1] = {
  };
  TestConflictingSymbolNamesExtension_reflection_ =
    ::google::protobuf::internal::GeneratedMessageReflection::NewGeneratedMessageReflection(
      TestConflictingSymbolNamesExtension_descriptor_,
      TestConflictingSymbolNamesExtension::internal_default_instance(),
      TestConflictingSymbolNamesExtension_offsets_,
      GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(TestConflictingSymbolNamesExtension, _has_bits_),
      -1,
      -1,
      sizeof(TestConflictingSymbolNamesExtension),
      GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(TestConflictingSymbolNamesExtension, _internal_metadata_));
  TestConflictingEnumNames_descriptor_ = file->message_type(2);
  static const int TestConflictingEnumNames_offsets_[1] = {
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(TestConflictingEnumNames, conflicting_enum_),
  };
  TestConflictingEnumNames_reflection_ =
    ::google::protobuf::internal::GeneratedMessageReflection::NewGeneratedMessageReflection(
      TestConflictingEnumNames_descriptor_,
      TestConflictingEnumNames::internal_default_instance(),
      TestConflictingEnumNames_offsets_,
      GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(TestConflictingEnumNames, _has_bits_),
      -1,
      -1,
      sizeof(TestConflictingEnumNames),
      GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(TestConflictingEnumNames, _internal_metadata_));
  TestConflictingEnumNames_NestedConflictingEnum_descriptor_ = TestConflictingEnumNames_descriptor_->enum_type(0);
  DummyMessage_descriptor_ = file->message_type(3);
  static const int DummyMessage_offsets_[1] = {
  };
  DummyMessage_reflection_ =
    ::google::protobuf::internal::GeneratedMessageReflection::NewGeneratedMessageReflection(
      DummyMessage_descriptor_,
      DummyMessage::internal_default_instance(),
      DummyMessage_offsets_,
      GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(DummyMessage, _has_bits_),
      -1,
      -1,
      sizeof(DummyMessage),
      GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(DummyMessage, _internal_metadata_));
  ConflictingEnum_descriptor_ = file->enum_type(0);
  TestConflictingMethodNames_descriptor_ = file->service(0);
}

namespace {

GOOGLE_PROTOBUF_DECLARE_ONCE(protobuf_AssignDescriptors_once_);
void protobuf_AssignDescriptorsOnce() {
  ::google::protobuf::GoogleOnceInit(&protobuf_AssignDescriptors_once_,
                 &protobuf_AssignDesc_google_2fprotobuf_2fcompiler_2fcpp_2fcpp_5ftest_5fbad_5fidentifiers_2eproto);
}

void protobuf_RegisterTypes(const ::std::string&) GOOGLE_ATTRIBUTE_COLD;
void protobuf_RegisterTypes(const ::std::string&) {
  protobuf_AssignDescriptorsOnce();
  ::google::protobuf::MessageFactory::InternalRegisterGeneratedMessage(
      TestConflictingSymbolNames_descriptor_, TestConflictingSymbolNames::internal_default_instance());
  ::google::protobuf::MessageFactory::InternalRegisterGeneratedMessage(
      TestConflictingSymbolNames_BuildDescriptors_descriptor_, TestConflictingSymbolNames_BuildDescriptors::internal_default_instance());
  ::google::protobuf::MessageFactory::InternalRegisterGeneratedMessage(
      TestConflictingSymbolNames_TypeTraits_descriptor_, TestConflictingSymbolNames_TypeTraits::internal_default_instance());
  ::google::protobuf::MessageFactory::InternalRegisterGeneratedMessage(
      TestConflictingSymbolNames_Data1_descriptor_, TestConflictingSymbolNames_Data1::internal_default_instance());
  ::google::protobuf::MessageFactory::InternalRegisterGeneratedMessage(
      TestConflictingSymbolNames_Data2_descriptor_, TestConflictingSymbolNames_Data2::internal_default_instance());
  ::google::protobuf::MessageFactory::InternalRegisterGeneratedMessage(
      TestConflictingSymbolNames_Data3_descriptor_, TestConflictingSymbolNames_Data3::internal_default_instance());
  ::google::protobuf::MessageFactory::InternalRegisterGeneratedMessage(
      TestConflictingSymbolNames_Data4_descriptor_, TestConflictingSymbolNames_Data4::internal_default_instance());
  ::google::protobuf::MessageFactory::InternalRegisterGeneratedMessage(
      TestConflictingSymbolNames_Data5_descriptor_, TestConflictingSymbolNames_Data5::internal_default_instance());
  ::google::protobuf::MessageFactory::InternalRegisterGeneratedMessage(
      TestConflictingSymbolNames_Data6_descriptor_, TestConflictingSymbolNames_Data6::internal_default_instance());
  ::google::protobuf::MessageFactory::InternalRegisterGeneratedMessage(
      TestConflictingSymbolNames_Cord_descriptor_, TestConflictingSymbolNames_Cord::internal_default_instance());
  ::google::protobuf::MessageFactory::InternalRegisterGeneratedMessage(
      TestConflictingSymbolNames_StringPiece_descriptor_, TestConflictingSymbolNames_StringPiece::internal_default_instance());
  ::google::protobuf::MessageFactory::InternalRegisterGeneratedMessage(
      TestConflictingSymbolNames_DO_descriptor_, TestConflictingSymbolNames_DO::internal_default_instance());
  ::google::protobuf::MessageFactory::InternalRegisterGeneratedMessage(
      TestConflictingSymbolNamesExtension_descriptor_, TestConflictingSymbolNamesExtension::internal_default_instance());
  ::google::protobuf::MessageFactory::InternalRegisterGeneratedMessage(
      TestConflictingEnumNames_descriptor_, TestConflictingEnumNames::internal_default_instance());
  ::google::protobuf::MessageFactory::InternalRegisterGeneratedMessage(
      DummyMessage_descriptor_, DummyMessage::internal_default_instance());
}

}  // namespace

void protobuf_ShutdownFile_google_2fprotobuf_2fcompiler_2fcpp_2fcpp_5ftest_5fbad_5fidentifiers_2eproto() {
  TestConflictingSymbolNames_default_instance_.Shutdown();
  delete TestConflictingSymbolNames_reflection_;
  TestConflictingSymbolNames_BuildDescriptors_default_instance_.Shutdown();
  delete TestConflictingSymbolNames_BuildDescriptors_reflection_;
  TestConflictingSymbolNames_TypeTraits_default_instance_.Shutdown();
  delete TestConflictingSymbolNames_TypeTraits_reflection_;
  TestConflictingSymbolNames_Data1_default_instance_.Shutdown();
  delete TestConflictingSymbolNames_Data1_reflection_;
  TestConflictingSymbolNames_Data2_default_instance_.Shutdown();
  delete TestConflictingSymbolNames_Data2_reflection_;
  TestConflictingSymbolNames_Data3_default_instance_.Shutdown();
  delete TestConflictingSymbolNames_Data3_reflection_;
  TestConflictingSymbolNames_Data4_default_instance_.Shutdown();
  delete TestConflictingSymbolNames_Data4_reflection_;
  TestConflictingSymbolNames_Data5_default_instance_.Shutdown();
  delete TestConflictingSymbolNames_Data5_reflection_;
  TestConflictingSymbolNames_Data6_default_instance_.Shutdown();
  delete TestConflictingSymbolNames_Data6_reflection_;
  TestConflictingSymbolNames_Cord_default_instance_.Shutdown();
  delete TestConflictingSymbolNames_Cord_reflection_;
  TestConflictingSymbolNames_StringPiece_default_instance_.Shutdown();
  delete TestConflictingSymbolNames_StringPiece_reflection_;
  TestConflictingSymbolNames_DO_default_instance_.Shutdown();
  delete TestConflictingSymbolNames_DO_reflection_;
  TestConflictingSymbolNamesExtension_default_instance_.Shutdown();
  delete TestConflictingSymbolNamesExtension_reflection_;
  TestConflictingEnumNames_default_instance_.Shutdown();
  delete TestConflictingEnumNames_reflection_;
  DummyMessage_default_instance_.Shutdown();
  delete DummyMessage_reflection_;
}

void protobuf_InitDefaults_google_2fprotobuf_2fcompiler_2fcpp_2fcpp_5ftest_5fbad_5fidentifiers_2eproto_impl() {
  GOOGLE_PROTOBUF_VERIFY_VERSION;

  ::google::protobuf::internal::GetEmptyString();
  TestConflictingSymbolNames_default_instance_.DefaultConstruct();
  TestConflictingSymbolNames_BuildDescriptors_default_instance_.DefaultConstruct();
  TestConflictingSymbolNames_TypeTraits_default_instance_.DefaultConstruct();
  TestConflictingSymbolNames_Data1_default_instance_.DefaultConstruct();
  TestConflictingSymbolNames_Data2_default_instance_.DefaultConstruct();
  ::google::protobuf::internal::GetEmptyString();
  TestConflictingSymbolNames_Data3_default_instance_.DefaultConstruct();
  TestConflictingSymbolNames_Data4_default_instance_.DefaultConstruct();
  ::google::protobuf::internal::GetEmptyString();
  TestConflictingSymbolNames_Data5_default_instance_.DefaultConstruct();
  ::google::protobuf::internal::GetEmptyString();
  TestConflictingSymbolNames_Data6_default_instance_.DefaultConstruct();
  TestConflictingSymbolNames_Cord_default_instance_.DefaultConstruct();
  TestConflictingSymbolNames_StringPiece_default_instance_.DefaultConstruct();
  TestConflictingSymbolNames_DO_default_instance_.DefaultConstruct();
  TestConflictingSymbolNamesExtension_default_instance_.DefaultConstruct();
  TestConflictingEnumNames_default_instance_.DefaultConstruct();
  DummyMessage_default_instance_.DefaultConstruct();
  TestConflictingSymbolNames_default_instance_.get_mutable()->InitAsDefaultInstance();
  TestConflictingSymbolNames_BuildDescriptors_default_instance_.get_mutable()->InitAsDefaultInstance();
  TestConflictingSymbolNames_TypeTraits_default_instance_.get_mutable()->InitAsDefaultInstance();
  TestConflictingSymbolNames_Data1_default_instance_.get_mutable()->InitAsDefaultInstance();
  TestConflictingSymbolNames_Data2_default_instance_.get_mutable()->InitAsDefaultInstance();
  TestConflictingSymbolNames_Data3_default_instance_.get_mutable()->InitAsDefaultInstance();
  TestConflictingSymbolNames_Data4_default_instance_.get_mutable()->InitAsDefaultInstance();
  TestConflictingSymbolNames_Data5_default_instance_.get_mutable()->InitAsDefaultInstance();
  TestConflictingSymbolNames_Data6_default_instance_.get_mutable()->InitAsDefaultInstance();
  TestConflictingSymbolNames_Cord_default_instance_.get_mutable()->InitAsDefaultInstance();
  TestConflictingSymbolNames_StringPiece_default_instance_.get_mutable()->InitAsDefaultInstance();
  TestConflictingSymbolNames_DO_default_instance_.get_mutable()->InitAsDefaultInstance();
  TestConflictingSymbolNamesExtension_default_instance_.get_mutable()->InitAsDefaultInstance();
  ::google::protobuf::internal::ExtensionSet::RegisterExtension(
    ::protobuf_unittest::TestConflictingSymbolNames::internal_default_instance(),
    20423638, 5, true, true);
  TestConflictingEnumNames_default_instance_.get_mutable()->InitAsDefaultInstance();
  DummyMessage_default_instance_.get_mutable()->InitAsDefaultInstance();
}

GOOGLE_PROTOBUF_DECLARE_ONCE(protobuf_InitDefaults_google_2fprotobuf_2fcompiler_2fcpp_2fcpp_5ftest_5fbad_5fidentifiers_2eproto_once_);
void protobuf_InitDefaults_google_2fprotobuf_2fcompiler_2fcpp_2fcpp_5ftest_5fbad_5fidentifiers_2eproto() {
  ::google::protobuf::GoogleOnceInit(&protobuf_InitDefaults_google_2fprotobuf_2fcompiler_2fcpp_2fcpp_5ftest_5fbad_5fidentifiers_2eproto_once_,
                 &protobuf_InitDefaults_google_2fprotobuf_2fcompiler_2fcpp_2fcpp_5ftest_5fbad_5fidentifiers_2eproto_impl);
}
void protobuf_AddDesc_google_2fprotobuf_2fcompiler_2fcpp_2fcpp_5ftest_5fbad_5fidentifiers_2eproto_impl() {
  GOOGLE_PROTOBUF_VERIFY_VERSION;

  protobuf_InitDefaults_google_2fprotobuf_2fcompiler_2fcpp_2fcpp_5ftest_5fbad_5fidentifiers_2eproto();
  ::google::protobuf::DescriptorPool::InternalAddGeneratedFile(
    "\n;google/protobuf/compiler/cpp/cpp_test_"
    "bad_identifiers.proto\022\021protobuf_unittest"
    "\"\234\t\n\032TestConflictingSymbolNames\022\r\n\005input"
    "\030\001 \001(\005\022\016\n\006output\030\002 \001(\005\022\016\n\006length\030\003 \001(\t\022\t"
    "\n\001i\030\004 \003(\005\022\027\n\013new_element\030\005 \003(\tB\002\010\002\022\022\n\nto"
    "tal_size\030\006 \001(\005\022\013\n\003tag\030\007 \001(\005\022\016\n\006source\030\010 "
    "\001(\005\022\r\n\005value\030\t \001(\005\022\014\n\004file\030\n \001(\005\022\014\n\004from"
    "\030\013 \001(\005\022\034\n\024handle_uninterpreted\030\014 \001(\005\022\r\n\005"
    "index\030\r \003(\005\022\022\n\ncontroller\030\016 \001(\005\022\024\n\014alrea"
    "dy_here\030\017 \001(\005\022\016\n\006uint32\030\020 \001(\r\022\016\n\006uint64\030"
    "\021 \001(\004\022\016\n\006string\030\022 \001(\t\022\016\n\006memset\030\023 \001(\005\022\r\n"
    "\005int32\030\024 \001(\005\022\r\n\005int64\030\025 \001(\003\022\023\n\013cached_si"
    "ze\030\026 \001(\r\022\022\n\nextensions\030\027 \001(\r\022\013\n\003bit\030\030 \001("
    "\r\022\014\n\004bits\030\031 \001(\r\022\017\n\007offsets\030\032 \001(\r\022\022\n\nrefl"
    "ection\030\033 \001(\r\022\025\n\tsome_cord\030\034 \001(\tB\002\010\001\022\035\n\021s"
    "ome_string_piece\030\035 \001(\tB\002\010\002\022\013\n\003int\030\036 \001(\r\022"
    "\016\n\006friend\030\037 \001(\r\022\r\n\005class\030% \001(\r\022\020\n\010typede"
    "cl\030\' \001(\r\022\014\n\004auto\030( \001(\r\022<\n\002do\030  \001(\01320.pro"
    "tobuf_unittest.TestConflictingSymbolName"
    "s.DO\022\022\n\nfield_type\030! \001(\005\022\021\n\tis_packed\030\" "
    "\001(\010\022\026\n\016release_length\030# \001(\t\022D\n\nrelease_d"
    "o\030$ \001(\01320.protobuf_unittest.TestConflict"
    "ingSymbolNames.DO\022\016\n\006target\030& \001(\t\032\022\n\020Bui"
    "ldDescriptors\032\014\n\nTypeTraits\032\025\n\005Data1\022\014\n\004"
    "data\030\001 \003(\005\032M\n\005Data2\022D\n\004data\030\001 \003(\01626.prot"
    "obuf_unittest.TestConflictingSymbolNames"
    ".TestEnum\032\025\n\005Data3\022\014\n\004data\030\001 \003(\t\032J\n\005Data"
    "4\022A\n\004data\030\001 \003(\01323.protobuf_unittest.Test"
    "ConflictingSymbolNames.Data4\032\031\n\005Data5\022\020\n"
    "\004data\030\001 \003(\tB\002\010\002\032\031\n\005Data6\022\020\n\004data\030\001 \003(\tB\002"
    "\010\001\032\006\n\004Cord\032\r\n\013StringPiece\032\004\n\002DO\"\023\n\010TestE"
    "num\022\007\n\003FOO\020\000*\t\010\350\007\020\200\200\200\200\002\"w\n#TestConflicti"
    "ngSymbolNamesExtension2P\n\022repeated_int32"
    "_ext\022-.protobuf_unittest.TestConflicting"
    "SymbolNames\030\326\307\336\t \003(\005B\002\020\001\"\303\001\n\030TestConflic"
    "tingEnumNames\022[\n\020conflicting_enum\030\001 \001(\0162"
    "A.protobuf_unittest.TestConflictingEnumN"
    "ames.NestedConflictingEnum\"J\n\025NestedConf"
    "lictingEnum\022\007\n\003and\020\001\022\t\n\005class\020\002\022\007\n\003int\020\003"
    "\022\013\n\007typedef\020\004\022\007\n\003XOR\020\005\"\016\n\014DummyMessage*7"
    "\n\017ConflictingEnum\022\n\n\006NOT_EQ\020\001\022\014\n\010volatil"
    "e\020\002\022\n\n\006return\020\0032i\n\032TestConflictingMethod"
    "Names\022K\n\007Closure\022\037.protobuf_unittest.Dum"
    "myMessage\032\037.protobuf_unittest.DummyMessa"
    "geB\003\200\001\001", 1767);
  ::google::protobuf::MessageFactory::InternalRegisterGeneratedFile(
    "google/protobuf/compiler/cpp/cpp_test_bad_identifiers.proto", &protobuf_RegisterTypes);
  ::google::protobuf::internal::OnShutdown(&protobuf_ShutdownFile_google_2fprotobuf_2fcompiler_2fcpp_2fcpp_5ftest_5fbad_5fidentifiers_2eproto);
}

GOOGLE_PROTOBUF_DECLARE_ONCE(protobuf_AddDesc_google_2fprotobuf_2fcompiler_2fcpp_2fcpp_5ftest_5fbad_5fidentifiers_2eproto_once_);
void protobuf_AddDesc_google_2fprotobuf_2fcompiler_2fcpp_2fcpp_5ftest_5fbad_5fidentifiers_2eproto() {
  ::google::protobuf::GoogleOnceInit(&protobuf_AddDesc_google_2fprotobuf_2fcompiler_2fcpp_2fcpp_5ftest_5fbad_5fidentifiers_2eproto_once_,
                 &protobuf_AddDesc_google_2fprotobuf_2fcompiler_2fcpp_2fcpp_5ftest_5fbad_5fidentifiers_2eproto_impl);
}
// Force AddDescriptors() to be called at static initialization time.
struct StaticDescriptorInitializer_google_2fprotobuf_2fcompiler_2fcpp_2fcpp_5ftest_5fbad_5fidentifiers_2eproto {
  StaticDescriptorInitializer_google_2fprotobuf_2fcompiler_2fcpp_2fcpp_5ftest_5fbad_5fidentifiers_2eproto() {
    protobuf_AddDesc_google_2fprotobuf_2fcompiler_2fcpp_2fcpp_5ftest_5fbad_5fidentifiers_2eproto();
  }
} static_descriptor_initializer_google_2fprotobuf_2fcompiler_2fcpp_2fcpp_5ftest_5fbad_5fidentifiers_2eproto_;
const ::google::protobuf::EnumDescriptor* ConflictingEnum_descriptor() {
  protobuf_AssignDescriptorsOnce();
  return ConflictingEnum_descriptor_;
}
bool ConflictingEnum_IsValid(int value) {
  switch (value) {
    case 1:
    case 2:
    case 3:
      return true;
    default:
      return false;
  }
}


namespace {

static void MergeFromFail(int line) GOOGLE_ATTRIBUTE_COLD GOOGLE_ATTRIBUTE_NORETURN;
static void MergeFromFail(int line) {
  ::google::protobuf::internal::MergeFromFail(__FILE__, line);
}

}  // namespace


// ===================================================================

const ::google::protobuf::EnumDescriptor* TestConflictingSymbolNames_TestEnum_descriptor() {
  protobuf_AssignDescriptorsOnce();
  return TestConflictingSymbolNames_TestEnum_descriptor_;
}
bool TestConflictingSymbolNames_TestEnum_IsValid(int value) {
  switch (value) {
    case 0:
      return true;
    default:
      return false;
  }
}

#if !defined(_MSC_VER) || _MSC_VER >= 1900
const TestConflictingSymbolNames_TestEnum TestConflictingSymbolNames::FOO;
const TestConflictingSymbolNames_TestEnum TestConflictingSymbolNames::TestEnum_MIN;
const TestConflictingSymbolNames_TestEnum TestConflictingSymbolNames::TestEnum_MAX;
const int TestConflictingSymbolNames::TestEnum_ARRAYSIZE;
#endif  // !defined(_MSC_VER) || _MSC_VER >= 1900
#if !defined(_MSC_VER) || _MSC_VER >= 1900
#endif  // !defined(_MSC_VER) || _MSC_VER >= 1900

TestConflictingSymbolNames_BuildDescriptors::TestConflictingSymbolNames_BuildDescriptors()
  : ::google::protobuf::Message(), _internal_metadata_(NULL) {
  if (this != internal_default_instance()) protobuf_InitDefaults_google_2fprotobuf_2fcompiler_2fcpp_2fcpp_5ftest_5fbad_5fidentifiers_2eproto();
  SharedCtor();
  // @@protoc_insertion_point(constructor:protobuf_unittest.TestConflictingSymbolNames.BuildDescriptors)
}

void TestConflictingSymbolNames_BuildDescriptors::InitAsDefaultInstance() {
}

TestConflictingSymbolNames_BuildDescriptors::TestConflictingSymbolNames_BuildDescriptors(const TestConflictingSymbolNames_BuildDescriptors& from)
  : ::google::protobuf::Message(),
    _internal_metadata_(NULL) {
  SharedCtor();
  UnsafeMergeFrom(from);
  // @@protoc_insertion_point(copy_constructor:protobuf_unittest.TestConflictingSymbolNames.BuildDescriptors)
}

void TestConflictingSymbolNames_BuildDescriptors::SharedCtor() {
  _cached_size_ = 0;
}

TestConflictingSymbolNames_BuildDescriptors::~TestConflictingSymbolNames_BuildDescriptors() {
  // @@protoc_insertion_point(destructor:protobuf_unittest.TestConflictingSymbolNames.BuildDescriptors)
  SharedDtor();
}

void TestConflictingSymbolNames_BuildDescriptors::SharedDtor() {
}

void TestConflictingSymbolNames_BuildDescriptors::SetCachedSize(int size) const {
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
}
const ::google::protobuf::Descriptor* TestConflictingSymbolNames_BuildDescriptors::descriptor() {
  protobuf_AssignDescriptorsOnce();
  return TestConflictingSymbolNames_BuildDescriptors_descriptor_;
}

const TestConflictingSymbolNames_BuildDescriptors& TestConflictingSymbolNames_BuildDescriptors::default_instance() {
  protobuf_InitDefaults_google_2fprotobuf_2fcompiler_2fcpp_2fcpp_5ftest_5fbad_5fidentifiers_2eproto();
  return *internal_default_instance();
}

::google::protobuf::internal::ExplicitlyConstructed<TestConflictingSymbolNames_BuildDescriptors> TestConflictingSymbolNames_BuildDescriptors_default_instance_;

TestConflictingSymbolNames_BuildDescriptors* TestConflictingSymbolNames_BuildDescriptors::New(::google::protobuf::Arena* arena) const {
  TestConflictingSymbolNames_BuildDescriptors* n = new TestConflictingSymbolNames_BuildDescriptors;
  if (arena != NULL) {
    arena->Own(n);
  }
  return n;
}

void TestConflictingSymbolNames_BuildDescriptors::Clear() {
// @@protoc_insertion_point(message_clear_start:protobuf_unittest.TestConflictingSymbolNames.BuildDescriptors)
  _has_bits_.Clear();
  if (_internal_metadata_.have_unknown_fields()) {
    mutable_unknown_fields()->Clear();
  }
}

bool TestConflictingSymbolNames_BuildDescriptors::MergePartialFromCodedStream(
    ::google::protobuf::io::CodedInputStream* input) {
#define DO_(EXPRESSION) if (!GOOGLE_PREDICT_TRUE(EXPRESSION)) goto failure
  ::google::protobuf::uint32 tag;
  // @@protoc_insertion_point(parse_start:protobuf_unittest.TestConflictingSymbolNames.BuildDescriptors)
  for (;;) {
    ::std::pair< ::google::protobuf::uint32, bool> p = input->ReadTagWithCutoff(127);
    tag = p.first;
    if (!p.second) goto handle_unusual;
  handle_unusual:
    if (tag == 0 ||
        ::google::protobuf::internal::WireFormatLite::GetTagWireType(tag) ==
        ::google::protobuf::internal::WireFormatLite::WIRETYPE_END_GROUP) {
      goto success;
    }
    DO_(::google::protobuf::internal::WireFormat::SkipField(
          input, tag, mutable_unknown_fields()));
  }
success:
  // @@protoc_insertion_point(parse_success:protobuf_unittest.TestConflictingSymbolNames.BuildDescriptors)
  return true;
failure:
  // @@protoc_insertion_point(parse_failure:protobuf_unittest.TestConflictingSymbolNames.BuildDescriptors)
  return false;
#undef DO_
}

void TestConflictingSymbolNames_BuildDescriptors::SerializeWithCachedSizes(
    ::google::protobuf::io::CodedOutputStream* output) const {
  // @@protoc_insertion_point(serialize_start:protobuf_unittest.TestConflictingSymbolNames.BuildDescriptors)
  if (_internal_metadata_.have_unknown_fields()) {
    ::google::protobuf::internal::WireFormat::SerializeUnknownFields(
        unknown_fields(), output);
  }
  // @@protoc_insertion_point(serialize_end:protobuf_unittest.TestConflictingSymbolNames.BuildDescriptors)
}

::google::protobuf::uint8* TestConflictingSymbolNames_BuildDescriptors::InternalSerializeWithCachedSizesToArray(
    bool deterministic, ::google::protobuf::uint8* target) const {
  (void)deterministic; // Unused
  // @@protoc_insertion_point(serialize_to_array_start:protobuf_unittest.TestConflictingSymbolNames.BuildDescriptors)
  if (_internal_metadata_.have_unknown_fields()) {
    target = ::google::protobuf::internal::WireFormat::SerializeUnknownFieldsToArray(
        unknown_fields(), target);
  }
  // @@protoc_insertion_point(serialize_to_array_end:protobuf_unittest.TestConflictingSymbolNames.BuildDescriptors)
  return target;
}

size_t TestConflictingSymbolNames_BuildDescriptors::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:protobuf_unittest.TestConflictingSymbolNames.BuildDescriptors)
  size_t total_size = 0;

  if (_internal_metadata_.have_unknown_fields()) {
    total_size +=
      ::google::protobuf::internal::WireFormat::ComputeUnknownFieldsSize(
        unknown_fields());
  }
  int cached_size = ::google::protobuf::internal::ToCachedSize(total_size);
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = cached_size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
  return total_size;
}

void TestConflictingSymbolNames_BuildDescriptors::MergeFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_merge_from_start:protobuf_unittest.TestConflictingSymbolNames.BuildDescriptors)
  if (GOOGLE_PREDICT_FALSE(&from == this)) MergeFromFail(__LINE__);
  const TestConflictingSymbolNames_BuildDescriptors* source =
      ::google::protobuf::internal::DynamicCastToGenerated<const TestConflictingSymbolNames_BuildDescriptors>(
          &from);
  if (source == NULL) {
  // @@protoc_insertion_point(generalized_merge_from_cast_fail:protobuf_unittest.TestConflictingSymbolNames.BuildDescriptors)
    ::google::protobuf::internal::ReflectionOps::Merge(from, this);
  } else {
  // @@protoc_insertion_point(generalized_merge_from_cast_success:protobuf_unittest.TestConflictingSymbolNames.BuildDescriptors)
    UnsafeMergeFrom(*source);
  }
}

void TestConflictingSymbolNames_BuildDescriptors::MergeFrom(const TestConflictingSymbolNames_BuildDescriptors& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:protobuf_unittest.TestConflictingSymbolNames.BuildDescriptors)
  if (GOOGLE_PREDICT_TRUE(&from != this)) {
    UnsafeMergeFrom(from);
  } else {
    MergeFromFail(__LINE__);
  }
}

void TestConflictingSymbolNames_BuildDescriptors::UnsafeMergeFrom(const TestConflictingSymbolNames_BuildDescriptors& from) {
  GOOGLE_DCHECK(&from != this);
  if (from._internal_metadata_.have_unknown_fields()) {
    ::google::protobuf::UnknownFieldSet::MergeToInternalMetdata(
      from.unknown_fields(), &_internal_metadata_);
  }
}

void TestConflictingSymbolNames_BuildDescriptors::CopyFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_copy_from_start:protobuf_unittest.TestConflictingSymbolNames.BuildDescriptors)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

void TestConflictingSymbolNames_BuildDescriptors::CopyFrom(const TestConflictingSymbolNames_BuildDescriptors& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:protobuf_unittest.TestConflictingSymbolNames.BuildDescriptors)
  if (&from == this) return;
  Clear();
  UnsafeMergeFrom(from);
}

bool TestConflictingSymbolNames_BuildDescriptors::IsInitialized() const {

  return true;
}

void TestConflictingSymbolNames_BuildDescriptors::Swap(TestConflictingSymbolNames_BuildDescriptors* other) {
  if (other == this) return;
  InternalSwap(other);
}
void TestConflictingSymbolNames_BuildDescriptors::InternalSwap(TestConflictingSymbolNames_BuildDescriptors* other) {
  _internal_metadata_.Swap(&other->_internal_metadata_);
  std::swap(_cached_size_, other->_cached_size_);
}

::google::protobuf::Metadata TestConflictingSymbolNames_BuildDescriptors::GetMetadata() const {
  protobuf_AssignDescriptorsOnce();
  ::google::protobuf::Metadata metadata;
  metadata.descriptor = TestConflictingSymbolNames_BuildDescriptors_descriptor_;
  metadata.reflection = TestConflictingSymbolNames_BuildDescriptors_reflection_;
  return metadata;
}


// -------------------------------------------------------------------

#if !defined(_MSC_VER) || _MSC_VER >= 1900
#endif  // !defined(_MSC_VER) || _MSC_VER >= 1900

TestConflictingSymbolNames_TypeTraits::TestConflictingSymbolNames_TypeTraits()
  : ::google::protobuf::Message(), _internal_metadata_(NULL) {
  if (this != internal_default_instance()) protobuf_InitDefaults_google_2fprotobuf_2fcompiler_2fcpp_2fcpp_5ftest_5fbad_5fidentifiers_2eproto();
  SharedCtor();
  // @@protoc_insertion_point(constructor:protobuf_unittest.TestConflictingSymbolNames.TypeTraits)
}

void TestConflictingSymbolNames_TypeTraits::InitAsDefaultInstance() {
}

TestConflictingSymbolNames_TypeTraits::TestConflictingSymbolNames_TypeTraits(const TestConflictingSymbolNames_TypeTraits& from)
  : ::google::protobuf::Message(),
    _internal_metadata_(NULL) {
  SharedCtor();
  UnsafeMergeFrom(from);
  // @@protoc_insertion_point(copy_constructor:protobuf_unittest.TestConflictingSymbolNames.TypeTraits)
}

void TestConflictingSymbolNames_TypeTraits::SharedCtor() {
  _cached_size_ = 0;
}

TestConflictingSymbolNames_TypeTraits::~TestConflictingSymbolNames_TypeTraits() {
  // @@protoc_insertion_point(destructor:protobuf_unittest.TestConflictingSymbolNames.TypeTraits)
  SharedDtor();
}

void TestConflictingSymbolNames_TypeTraits::SharedDtor() {
}

void TestConflictingSymbolNames_TypeTraits::SetCachedSize(int size) const {
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
}
const ::google::protobuf::Descriptor* TestConflictingSymbolNames_TypeTraits::descriptor() {
  protobuf_AssignDescriptorsOnce();
  return TestConflictingSymbolNames_TypeTraits_descriptor_;
}

const TestConflictingSymbolNames_TypeTraits& TestConflictingSymbolNames_TypeTraits::default_instance() {
  protobuf_InitDefaults_google_2fprotobuf_2fcompiler_2fcpp_2fcpp_5ftest_5fbad_5fidentifiers_2eproto();
  return *internal_default_instance();
}

::google::protobuf::internal::ExplicitlyConstructed<TestConflictingSymbolNames_TypeTraits> TestConflictingSymbolNames_TypeTraits_default_instance_;

TestConflictingSymbolNames_TypeTraits* TestConflictingSymbolNames_TypeTraits::New(::google::protobuf::Arena* arena) const {
  TestConflictingSymbolNames_TypeTraits* n = new TestConflictingSymbolNames_TypeTraits;
  if (arena != NULL) {
    arena->Own(n);
  }
  return n;
}

void TestConflictingSymbolNames_TypeTraits::Clear() {
// @@protoc_insertion_point(message_clear_start:protobuf_unittest.TestConflictingSymbolNames.TypeTraits)
  _has_bits_.Clear();
  if (_internal_metadata_.have_unknown_fields()) {
    mutable_unknown_fields()->Clear();
  }
}

bool TestConflictingSymbolNames_TypeTraits::MergePartialFromCodedStream(
    ::google::protobuf::io::CodedInputStream* input) {
#define DO_(EXPRESSION) if (!GOOGLE_PREDICT_TRUE(EXPRESSION)) goto failure
  ::google::protobuf::uint32 tag;
  // @@protoc_insertion_point(parse_start:protobuf_unittest.TestConflictingSymbolNames.TypeTraits)
  for (;;) {
    ::std::pair< ::google::protobuf::uint32, bool> p = input->ReadTagWithCutoff(127);
    tag = p.first;
    if (!p.second) goto handle_unusual;
  handle_unusual:
    if (tag == 0 ||
        ::google::protobuf::internal::WireFormatLite::GetTagWireType(tag) ==
        ::google::protobuf::internal::WireFormatLite::WIRETYPE_END_GROUP) {
      goto success;
    }
    DO_(::google::protobuf::internal::WireFormat::SkipField(
          input, tag, mutable_unknown_fields()));
  }
success:
  // @@protoc_insertion_point(parse_success:protobuf_unittest.TestConflictingSymbolNames.TypeTraits)
  return true;
failure:
  // @@protoc_insertion_point(parse_failure:protobuf_unittest.TestConflictingSymbolNames.TypeTraits)
  return false;
#undef DO_
}

void TestConflictingSymbolNames_TypeTraits::SerializeWithCachedSizes(
    ::google::protobuf::io::CodedOutputStream* output) const {
  // @@protoc_insertion_point(serialize_start:protobuf_unittest.TestConflictingSymbolNames.TypeTraits)
  if (_internal_metadata_.have_unknown_fields()) {
    ::google::protobuf::internal::WireFormat::SerializeUnknownFields(
        unknown_fields(), output);
  }
  // @@protoc_insertion_point(serialize_end:protobuf_unittest.TestConflictingSymbolNames.TypeTraits)
}

::google::protobuf::uint8* TestConflictingSymbolNames_TypeTraits::InternalSerializeWithCachedSizesToArray(
    bool deterministic, ::google::protobuf::uint8* target) const {
  (void)deterministic; // Unused
  // @@protoc_insertion_point(serialize_to_array_start:protobuf_unittest.TestConflictingSymbolNames.TypeTraits)
  if (_internal_metadata_.have_unknown_fields()) {
    target = ::google::protobuf::internal::WireFormat::SerializeUnknownFieldsToArray(
        unknown_fields(), target);
  }
  // @@protoc_insertion_point(serialize_to_array_end:protobuf_unittest.TestConflictingSymbolNames.TypeTraits)
  return target;
}

size_t TestConflictingSymbolNames_TypeTraits::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:protobuf_unittest.TestConflictingSymbolNames.TypeTraits)
  size_t total_size = 0;

  if (_internal_metadata_.have_unknown_fields()) {
    total_size +=
      ::google::protobuf::internal::WireFormat::ComputeUnknownFieldsSize(
        unknown_fields());
  }
  int cached_size = ::google::protobuf::internal::ToCachedSize(total_size);
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = cached_size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
  return total_size;
}

void TestConflictingSymbolNames_TypeTraits::MergeFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_merge_from_start:protobuf_unittest.TestConflictingSymbolNames.TypeTraits)
  if (GOOGLE_PREDICT_FALSE(&from == this)) MergeFromFail(__LINE__);
  const TestConflictingSymbolNames_TypeTraits* source =
      ::google::protobuf::internal::DynamicCastToGenerated<const TestConflictingSymbolNames_TypeTraits>(
          &from);
  if (source == NULL) {
  // @@protoc_insertion_point(generalized_merge_from_cast_fail:protobuf_unittest.TestConflictingSymbolNames.TypeTraits)
    ::google::protobuf::internal::ReflectionOps::Merge(from, this);
  } else {
  // @@protoc_insertion_point(generalized_merge_from_cast_success:protobuf_unittest.TestConflictingSymbolNames.TypeTraits)
    UnsafeMergeFrom(*source);
  }
}

void TestConflictingSymbolNames_TypeTraits::MergeFrom(const TestConflictingSymbolNames_TypeTraits& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:protobuf_unittest.TestConflictingSymbolNames.TypeTraits)
  if (GOOGLE_PREDICT_TRUE(&from != this)) {
    UnsafeMergeFrom(from);
  } else {
    MergeFromFail(__LINE__);
  }
}

void TestConflictingSymbolNames_TypeTraits::UnsafeMergeFrom(const TestConflictingSymbolNames_TypeTraits& from) {
  GOOGLE_DCHECK(&from != this);
  if (from._internal_metadata_.have_unknown_fields()) {
    ::google::protobuf::UnknownFieldSet::MergeToInternalMetdata(
      from.unknown_fields(), &_internal_metadata_);
  }
}

void TestConflictingSymbolNames_TypeTraits::CopyFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_copy_from_start:protobuf_unittest.TestConflictingSymbolNames.TypeTraits)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

void TestConflictingSymbolNames_TypeTraits::CopyFrom(const TestConflictingSymbolNames_TypeTraits& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:protobuf_unittest.TestConflictingSymbolNames.TypeTraits)
  if (&from == this) return;
  Clear();
  UnsafeMergeFrom(from);
}

bool TestConflictingSymbolNames_TypeTraits::IsInitialized() const {

  return true;
}

void TestConflictingSymbolNames_TypeTraits::Swap(TestConflictingSymbolNames_TypeTraits* other) {
  if (other == this) return;
  InternalSwap(other);
}
void TestConflictingSymbolNames_TypeTraits::InternalSwap(TestConflictingSymbolNames_TypeTraits* other) {
  _internal_metadata_.Swap(&other->_internal_metadata_);
  std::swap(_cached_size_, other->_cached_size_);
}

::google::protobuf::Metadata TestConflictingSymbolNames_TypeTraits::GetMetadata() const {
  protobuf_AssignDescriptorsOnce();
  ::google::protobuf::Metadata metadata;
  metadata.descriptor = TestConflictingSymbolNames_TypeTraits_descriptor_;
  metadata.reflection = TestConflictingSymbolNames_TypeTraits_reflection_;
  return metadata;
}


// -------------------------------------------------------------------

#if !defined(_MSC_VER) || _MSC_VER >= 1900
const int TestConflictingSymbolNames_Data1::kDataFieldNumber;
#endif  // !defined(_MSC_VER) || _MSC_VER >= 1900

TestConflictingSymbolNames_Data1::TestConflictingSymbolNames_Data1()
  : ::google::protobuf::Message(), _internal_metadata_(NULL) {
  if (this != internal_default_instance()) protobuf_InitDefaults_google_2fprotobuf_2fcompiler_2fcpp_2fcpp_5ftest_5fbad_5fidentifiers_2eproto();
  SharedCtor();
  // @@protoc_insertion_point(constructor:protobuf_unittest.TestConflictingSymbolNames.Data1)
}

void TestConflictingSymbolNames_Data1::InitAsDefaultInstance() {
}

TestConflictingSymbolNames_Data1::TestConflictingSymbolNames_Data1(const TestConflictingSymbolNames_Data1& from)
  : ::google::protobuf::Message(),
    _internal_metadata_(NULL) {
  SharedCtor();
  UnsafeMergeFrom(from);
  // @@protoc_insertion_point(copy_constructor:protobuf_unittest.TestConflictingSymbolNames.Data1)
}

void TestConflictingSymbolNames_Data1::SharedCtor() {
  _cached_size_ = 0;
}

TestConflictingSymbolNames_Data1::~TestConflictingSymbolNames_Data1() {
  // @@protoc_insertion_point(destructor:protobuf_unittest.TestConflictingSymbolNames.Data1)
  SharedDtor();
}

void TestConflictingSymbolNames_Data1::SharedDtor() {
}

void TestConflictingSymbolNames_Data1::SetCachedSize(int size) const {
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
}
const ::google::protobuf::Descriptor* TestConflictingSymbolNames_Data1::descriptor() {
  protobuf_AssignDescriptorsOnce();
  return TestConflictingSymbolNames_Data1_descriptor_;
}

const TestConflictingSymbolNames_Data1& TestConflictingSymbolNames_Data1::default_instance() {
  protobuf_InitDefaults_google_2fprotobuf_2fcompiler_2fcpp_2fcpp_5ftest_5fbad_5fidentifiers_2eproto();
  return *internal_default_instance();
}

::google::protobuf::internal::ExplicitlyConstructed<TestConflictingSymbolNames_Data1> TestConflictingSymbolNames_Data1_default_instance_;

TestConflictingSymbolNames_Data1* TestConflictingSymbolNames_Data1::New(::google::protobuf::Arena* arena) const {
  TestConflictingSymbolNames_Data1* n = new TestConflictingSymbolNames_Data1;
  if (arena != NULL) {
    arena->Own(n);
  }
  return n;
}

void TestConflictingSymbolNames_Data1::Clear() {
// @@protoc_insertion_point(message_clear_start:protobuf_unittest.TestConflictingSymbolNames.Data1)
  data_.Clear();
  _has_bits_.Clear();
  if (_internal_metadata_.have_unknown_fields()) {
    mutable_unknown_fields()->Clear();
  }
}

bool TestConflictingSymbolNames_Data1::MergePartialFromCodedStream(
    ::google::protobuf::io::CodedInputStream* input) {
#define DO_(EXPRESSION) if (!GOOGLE_PREDICT_TRUE(EXPRESSION)) goto failure
  ::google::protobuf::uint32 tag;
  // @@protoc_insertion_point(parse_start:protobuf_unittest.TestConflictingSymbolNames.Data1)
  for (;;) {
    ::std::pair< ::google::protobuf::uint32, bool> p = input->ReadTagWithCutoff(127);
    tag = p.first;
    if (!p.second) goto handle_unusual;
    switch (::google::protobuf::internal::WireFormatLite::GetTagFieldNumber(tag)) {
      // repeated int32 data = 1;
      case 1: {
        if (tag == 8) {
         parse_data:
          DO_((::google::protobuf::internal::WireFormatLite::ReadRepeatedPrimitive<
                   ::google::protobuf::int32, ::google::protobuf::internal::WireFormatLite::TYPE_INT32>(
                 1, 8, input, this->mutable_data())));
        } else if (tag == 10) {
          DO_((::google::protobuf::internal::WireFormatLite::ReadPackedPrimitiveNoInline<
                   ::google::protobuf::int32, ::google::protobuf::internal::WireFormatLite::TYPE_INT32>(
                 input, this->mutable_data())));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(8)) goto parse_data;
        if (input->ExpectAtEnd()) goto success;
        break;
      }

      default: {
      handle_unusual:
        if (tag == 0 ||
            ::google::protobuf::internal::WireFormatLite::GetTagWireType(tag) ==
            ::google::protobuf::internal::WireFormatLite::WIRETYPE_END_GROUP) {
          goto success;
        }
        DO_(::google::protobuf::internal::WireFormat::SkipField(
              input, tag, mutable_unknown_fields()));
        break;
      }
    }
  }
success:
  // @@protoc_insertion_point(parse_success:protobuf_unittest.TestConflictingSymbolNames.Data1)
  return true;
failure:
  // @@protoc_insertion_point(parse_failure:protobuf_unittest.TestConflictingSymbolNames.Data1)
  return false;
#undef DO_
}

void TestConflictingSymbolNames_Data1::SerializeWithCachedSizes(
    ::google::protobuf::io::CodedOutputStream* output) const {
  // @@protoc_insertion_point(serialize_start:protobuf_unittest.TestConflictingSymbolNames.Data1)
  // repeated int32 data = 1;
  for (int i = 0; i < this->data_size(); i++) {
    ::google::protobuf::internal::WireFormatLite::WriteInt32(
      1, this->data(i), output);
  }

  if (_internal_metadata_.have_unknown_fields()) {
    ::google::protobuf::internal::WireFormat::SerializeUnknownFields(
        unknown_fields(), output);
  }
  // @@protoc_insertion_point(serialize_end:protobuf_unittest.TestConflictingSymbolNames.Data1)
}

::google::protobuf::uint8* TestConflictingSymbolNames_Data1::InternalSerializeWithCachedSizesToArray(
    bool deterministic, ::google::protobuf::uint8* target) const {
  (void)deterministic; // Unused
  // @@protoc_insertion_point(serialize_to_array_start:protobuf_unittest.TestConflictingSymbolNames.Data1)
  // repeated int32 data = 1;
  for (int i = 0; i < this->data_size(); i++) {
    target = ::google::protobuf::internal::WireFormatLite::
      WriteInt32ToArray(1, this->data(i), target);
  }

  if (_internal_metadata_.have_unknown_fields()) {
    target = ::google::protobuf::internal::WireFormat::SerializeUnknownFieldsToArray(
        unknown_fields(), target);
  }
  // @@protoc_insertion_point(serialize_to_array_end:protobuf_unittest.TestConflictingSymbolNames.Data1)
  return target;
}

size_t TestConflictingSymbolNames_Data1::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:protobuf_unittest.TestConflictingSymbolNames.Data1)
  size_t total_size = 0;

  // repeated int32 data = 1;
  {
    size_t data_size = 0;
    unsigned int count = this->data_size();
    for (unsigned int i = 0; i < count; i++) {
      data_size += ::google::protobuf::internal::WireFormatLite::
        Int32Size(this->data(i));
    }
    total_size += 1 *
                  ::google::protobuf::internal::FromIntSize(this->data_size());
    total_size += data_size;
  }

  if (_internal_metadata_.have_unknown_fields()) {
    total_size +=
      ::google::protobuf::internal::WireFormat::ComputeUnknownFieldsSize(
        unknown_fields());
  }
  int cached_size = ::google::protobuf::internal::ToCachedSize(total_size);
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = cached_size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
  return total_size;
}

void TestConflictingSymbolNames_Data1::MergeFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_merge_from_start:protobuf_unittest.TestConflictingSymbolNames.Data1)
  if (GOOGLE_PREDICT_FALSE(&from == this)) MergeFromFail(__LINE__);
  const TestConflictingSymbolNames_Data1* source =
      ::google::protobuf::internal::DynamicCastToGenerated<const TestConflictingSymbolNames_Data1>(
          &from);
  if (source == NULL) {
  // @@protoc_insertion_point(generalized_merge_from_cast_fail:protobuf_unittest.TestConflictingSymbolNames.Data1)
    ::google::protobuf::internal::ReflectionOps::Merge(from, this);
  } else {
  // @@protoc_insertion_point(generalized_merge_from_cast_success:protobuf_unittest.TestConflictingSymbolNames.Data1)
    UnsafeMergeFrom(*source);
  }
}

void TestConflictingSymbolNames_Data1::MergeFrom(const TestConflictingSymbolNames_Data1& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:protobuf_unittest.TestConflictingSymbolNames.Data1)
  if (GOOGLE_PREDICT_TRUE(&from != this)) {
    UnsafeMergeFrom(from);
  } else {
    MergeFromFail(__LINE__);
  }
}

void TestConflictingSymbolNames_Data1::UnsafeMergeFrom(const TestConflictingSymbolNames_Data1& from) {
  GOOGLE_DCHECK(&from != this);
  data_.UnsafeMergeFrom(from.data_);
  if (from._internal_metadata_.have_unknown_fields()) {
    ::google::protobuf::UnknownFieldSet::MergeToInternalMetdata(
      from.unknown_fields(), &_internal_metadata_);
  }
}

void TestConflictingSymbolNames_Data1::CopyFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_copy_from_start:protobuf_unittest.TestConflictingSymbolNames.Data1)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

void TestConflictingSymbolNames_Data1::CopyFrom(const TestConflictingSymbolNames_Data1& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:protobuf_unittest.TestConflictingSymbolNames.Data1)
  if (&from == this) return;
  Clear();
  UnsafeMergeFrom(from);
}

bool TestConflictingSymbolNames_Data1::IsInitialized() const {

  return true;
}

void TestConflictingSymbolNames_Data1::Swap(TestConflictingSymbolNames_Data1* other) {
  if (other == this) return;
  InternalSwap(other);
}
void TestConflictingSymbolNames_Data1::InternalSwap(TestConflictingSymbolNames_Data1* other) {
  data_.UnsafeArenaSwap(&other->data_);
  std::swap(_has_bits_[0], other->_has_bits_[0]);
  _internal_metadata_.Swap(&other->_internal_metadata_);
  std::swap(_cached_size_, other->_cached_size_);
}

::google::protobuf::Metadata TestConflictingSymbolNames_Data1::GetMetadata() const {
  protobuf_AssignDescriptorsOnce();
  ::google::protobuf::Metadata metadata;
  metadata.descriptor = TestConflictingSymbolNames_Data1_descriptor_;
  metadata.reflection = TestConflictingSymbolNames_Data1_reflection_;
  return metadata;
}


// -------------------------------------------------------------------

#if !defined(_MSC_VER) || _MSC_VER >= 1900
const int TestConflictingSymbolNames_Data2::kDataFieldNumber;
#endif  // !defined(_MSC_VER) || _MSC_VER >= 1900

TestConflictingSymbolNames_Data2::TestConflictingSymbolNames_Data2()
  : ::google::protobuf::Message(), _internal_metadata_(NULL) {
  if (this != internal_default_instance()) protobuf_InitDefaults_google_2fprotobuf_2fcompiler_2fcpp_2fcpp_5ftest_5fbad_5fidentifiers_2eproto();
  SharedCtor();
  // @@protoc_insertion_point(constructor:protobuf_unittest.TestConflictingSymbolNames.Data2)
}

void TestConflictingSymbolNames_Data2::InitAsDefaultInstance() {
}

TestConflictingSymbolNames_Data2::TestConflictingSymbolNames_Data2(const TestConflictingSymbolNames_Data2& from)
  : ::google::protobuf::Message(),
    _internal_metadata_(NULL) {
  SharedCtor();
  UnsafeMergeFrom(from);
  // @@protoc_insertion_point(copy_constructor:protobuf_unittest.TestConflictingSymbolNames.Data2)
}

void TestConflictingSymbolNames_Data2::SharedCtor() {
  _cached_size_ = 0;
}

TestConflictingSymbolNames_Data2::~TestConflictingSymbolNames_Data2() {
  // @@protoc_insertion_point(destructor:protobuf_unittest.TestConflictingSymbolNames.Data2)
  SharedDtor();
}

void TestConflictingSymbolNames_Data2::SharedDtor() {
}

void TestConflictingSymbolNames_Data2::SetCachedSize(int size) const {
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
}
const ::google::protobuf::Descriptor* TestConflictingSymbolNames_Data2::descriptor() {
  protobuf_AssignDescriptorsOnce();
  return TestConflictingSymbolNames_Data2_descriptor_;
}

const TestConflictingSymbolNames_Data2& TestConflictingSymbolNames_Data2::default_instance() {
  protobuf_InitDefaults_google_2fprotobuf_2fcompiler_2fcpp_2fcpp_5ftest_5fbad_5fidentifiers_2eproto();
  return *internal_default_instance();
}

::google::protobuf::internal::ExplicitlyConstructed<TestConflictingSymbolNames_Data2> TestConflictingSymbolNames_Data2_default_instance_;

TestConflictingSymbolNames_Data2* TestConflictingSymbolNames_Data2::New(::google::protobuf::Arena* arena) const {
  TestConflictingSymbolNames_Data2* n = new TestConflictingSymbolNames_Data2;
  if (arena != NULL) {
    arena->Own(n);
  }
  return n;
}

void TestConflictingSymbolNames_Data2::Clear() {
// @@protoc_insertion_point(message_clear_start:protobuf_unittest.TestConflictingSymbolNames.Data2)
  data_.Clear();
  _has_bits_.Clear();
  if (_internal_metadata_.have_unknown_fields()) {
    mutable_unknown_fields()->Clear();
  }
}

bool TestConflictingSymbolNames_Data2::MergePartialFromCodedStream(
    ::google::protobuf::io::CodedInputStream* input) {
#define DO_(EXPRESSION) if (!GOOGLE_PREDICT_TRUE(EXPRESSION)) goto failure
  ::google::protobuf::uint32 tag;
  // @@protoc_insertion_point(parse_start:protobuf_unittest.TestConflictingSymbolNames.Data2)
  for (;;) {
    ::std::pair< ::google::protobuf::uint32, bool> p = input->ReadTagWithCutoff(127);
    tag = p.first;
    if (!p.second) goto handle_unusual;
    switch (::google::protobuf::internal::WireFormatLite::GetTagFieldNumber(tag)) {
      // repeated .protobuf_unittest.TestConflictingSymbolNames.TestEnum data = 1;
      case 1: {
        if (tag == 8) {
         parse_data:
          int value;
          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   int, ::google::protobuf::internal::WireFormatLite::TYPE_ENUM>(
                 input, &value)));
          if (::protobuf_unittest::TestConflictingSymbolNames_TestEnum_IsValid(value)) {
            add_data(static_cast< ::protobuf_unittest::TestConflictingSymbolNames_TestEnum >(value));
          } else {
            mutable_unknown_fields()->AddVarint(1, value);
          }
        } else if (tag == 10) {
          DO_((::google::protobuf::internal::WireFormat::ReadPackedEnumPreserveUnknowns(
                 input,
                 1,
                 ::protobuf_unittest::TestConflictingSymbolNames_TestEnum_IsValid,
                 mutable_unknown_fields(),
                 this->mutable_data())));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(8)) goto parse_data;
        if (input->ExpectAtEnd()) goto success;
        break;
      }

      default: {
      handle_unusual:
        if (tag == 0 ||
            ::google::protobuf::internal::WireFormatLite::GetTagWireType(tag) ==
            ::google::protobuf::internal::WireFormatLite::WIRETYPE_END_GROUP) {
          goto success;
        }
        DO_(::google::protobuf::internal::WireFormat::SkipField(
              input, tag, mutable_unknown_fields()));
        break;
      }
    }
  }
success:
  // @@protoc_insertion_point(parse_success:protobuf_unittest.TestConflictingSymbolNames.Data2)
  return true;
failure:
  // @@protoc_insertion_point(parse_failure:protobuf_unittest.TestConflictingSymbolNames.Data2)
  return false;
#undef DO_
}

void TestConflictingSymbolNames_Data2::SerializeWithCachedSizes(
    ::google::protobuf::io::CodedOutputStream* output) const {
  // @@protoc_insertion_point(serialize_start:protobuf_unittest.TestConflictingSymbolNames.Data2)
  // repeated .protobuf_unittest.TestConflictingSymbolNames.TestEnum data = 1;
  for (int i = 0; i < this->data_size(); i++) {
    ::google::protobuf::internal::WireFormatLite::WriteEnum(
      1, this->data(i), output);
  }

  if (_internal_metadata_.have_unknown_fields()) {
    ::google::protobuf::internal::WireFormat::SerializeUnknownFields(
        unknown_fields(), output);
  }
  // @@protoc_insertion_point(serialize_end:protobuf_unittest.TestConflictingSymbolNames.Data2)
}

::google::protobuf::uint8* TestConflictingSymbolNames_Data2::InternalSerializeWithCachedSizesToArray(
    bool deterministic, ::google::protobuf::uint8* target) const {
  (void)deterministic; // Unused
  // @@protoc_insertion_point(serialize_to_array_start:protobuf_unittest.TestConflictingSymbolNames.Data2)
  // repeated .protobuf_unittest.TestConflictingSymbolNames.TestEnum data = 1;
  for (int i = 0; i < this->data_size(); i++) {
    target = ::google::protobuf::internal::WireFormatLite::WriteEnumToArray(
      1, this->data(i), target);
  }

  if (_internal_metadata_.have_unknown_fields()) {
    target = ::google::protobuf::internal::WireFormat::SerializeUnknownFieldsToArray(
        unknown_fields(), target);
  }
  // @@protoc_insertion_point(serialize_to_array_end:protobuf_unittest.TestConflictingSymbolNames.Data2)
  return target;
}

size_t TestConflictingSymbolNames_Data2::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:protobuf_unittest.TestConflictingSymbolNames.Data2)
  size_t total_size = 0;

  // repeated .protobuf_unittest.TestConflictingSymbolNames.TestEnum data = 1;
  {
    size_t data_size = 0;
    unsigned int count = this->data_size();for (unsigned int i = 0; i < count; i++) {
      data_size += ::google::protobuf::internal::WireFormatLite::EnumSize(
        this->data(i));
    }
    total_size += (1UL * count) + data_size;
  }

  if (_internal_metadata_.have_unknown_fields()) {
    total_size +=
      ::google::protobuf::internal::WireFormat::ComputeUnknownFieldsSize(
        unknown_fields());
  }
  int cached_size = ::google::protobuf::internal::ToCachedSize(total_size);
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = cached_size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
  return total_size;
}

void TestConflictingSymbolNames_Data2::MergeFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_merge_from_start:protobuf_unittest.TestConflictingSymbolNames.Data2)
  if (GOOGLE_PREDICT_FALSE(&from == this)) MergeFromFail(__LINE__);
  const TestConflictingSymbolNames_Data2* source =
      ::google::protobuf::internal::DynamicCastToGenerated<const TestConflictingSymbolNames_Data2>(
          &from);
  if (source == NULL) {
  // @@protoc_insertion_point(generalized_merge_from_cast_fail:protobuf_unittest.TestConflictingSymbolNames.Data2)
    ::google::protobuf::internal::ReflectionOps::Merge(from, this);
  } else {
  // @@protoc_insertion_point(generalized_merge_from_cast_success:protobuf_unittest.TestConflictingSymbolNames.Data2)
    UnsafeMergeFrom(*source);
  }
}

void TestConflictingSymbolNames_Data2::MergeFrom(const TestConflictingSymbolNames_Data2& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:protobuf_unittest.TestConflictingSymbolNames.Data2)
  if (GOOGLE_PREDICT_TRUE(&from != this)) {
    UnsafeMergeFrom(from);
  } else {
    MergeFromFail(__LINE__);
  }
}

void TestConflictingSymbolNames_Data2::UnsafeMergeFrom(const TestConflictingSymbolNames_Data2& from) {
  GOOGLE_DCHECK(&from != this);
  data_.UnsafeMergeFrom(from.data_);
  if (from._internal_metadata_.have_unknown_fields()) {
    ::google::protobuf::UnknownFieldSet::MergeToInternalMetdata(
      from.unknown_fields(), &_internal_metadata_);
  }
}

void TestConflictingSymbolNames_Data2::CopyFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_copy_from_start:protobuf_unittest.TestConflictingSymbolNames.Data2)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

void TestConflictingSymbolNames_Data2::CopyFrom(const TestConflictingSymbolNames_Data2& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:protobuf_unittest.TestConflictingSymbolNames.Data2)
  if (&from == this) return;
  Clear();
  UnsafeMergeFrom(from);
}

bool TestConflictingSymbolNames_Data2::IsInitialized() const {

  return true;
}

void TestConflictingSymbolNames_Data2::Swap(TestConflictingSymbolNames_Data2* other) {
  if (other == this) return;
  InternalSwap(other);
}
void TestConflictingSymbolNames_Data2::InternalSwap(TestConflictingSymbolNames_Data2* other) {
  data_.UnsafeArenaSwap(&other->data_);
  std::swap(_has_bits_[0], other->_has_bits_[0]);
  _internal_metadata_.Swap(&other->_internal_metadata_);
  std::swap(_cached_size_, other->_cached_size_);
}

::google::protobuf::Metadata TestConflictingSymbolNames_Data2::GetMetadata() const {
  protobuf_AssignDescriptorsOnce();
  ::google::protobuf::Metadata metadata;
  metadata.descriptor = TestConflictingSymbolNames_Data2_descriptor_;
  metadata.reflection = TestConflictingSymbolNames_Data2_reflection_;
  return metadata;
}


// -------------------------------------------------------------------

#if !defined(_MSC_VER) || _MSC_VER >= 1900
const int TestConflictingSymbolNames_Data3::kDataFieldNumber;
#endif  // !defined(_MSC_VER) || _MSC_VER >= 1900

TestConflictingSymbolNames_Data3::TestConflictingSymbolNames_Data3()
  : ::google::protobuf::Message(), _internal_metadata_(NULL) {
  if (this != internal_default_instance()) protobuf_InitDefaults_google_2fprotobuf_2fcompiler_2fcpp_2fcpp_5ftest_5fbad_5fidentifiers_2eproto();
  SharedCtor();
  // @@protoc_insertion_point(constructor:protobuf_unittest.TestConflictingSymbolNames.Data3)
}

void TestConflictingSymbolNames_Data3::InitAsDefaultInstance() {
}

TestConflictingSymbolNames_Data3::TestConflictingSymbolNames_Data3(const TestConflictingSymbolNames_Data3& from)
  : ::google::protobuf::Message(),
    _internal_metadata_(NULL) {
  SharedCtor();
  UnsafeMergeFrom(from);
  // @@protoc_insertion_point(copy_constructor:protobuf_unittest.TestConflictingSymbolNames.Data3)
}

void TestConflictingSymbolNames_Data3::SharedCtor() {
  _cached_size_ = 0;
}

TestConflictingSymbolNames_Data3::~TestConflictingSymbolNames_Data3() {
  // @@protoc_insertion_point(destructor:protobuf_unittest.TestConflictingSymbolNames.Data3)
  SharedDtor();
}

void TestConflictingSymbolNames_Data3::SharedDtor() {
}

void TestConflictingSymbolNames_Data3::SetCachedSize(int size) const {
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
}
const ::google::protobuf::Descriptor* TestConflictingSymbolNames_Data3::descriptor() {
  protobuf_AssignDescriptorsOnce();
  return TestConflictingSymbolNames_Data3_descriptor_;
}

const TestConflictingSymbolNames_Data3& TestConflictingSymbolNames_Data3::default_instance() {
  protobuf_InitDefaults_google_2fprotobuf_2fcompiler_2fcpp_2fcpp_5ftest_5fbad_5fidentifiers_2eproto();
  return *internal_default_instance();
}

::google::protobuf::internal::ExplicitlyConstructed<TestConflictingSymbolNames_Data3> TestConflictingSymbolNames_Data3_default_instance_;

TestConflictingSymbolNames_Data3* TestConflictingSymbolNames_Data3::New(::google::protobuf::Arena* arena) const {
  TestConflictingSymbolNames_Data3* n = new TestConflictingSymbolNames_Data3;
  if (arena != NULL) {
    arena->Own(n);
  }
  return n;
}

void TestConflictingSymbolNames_Data3::Clear() {
// @@protoc_insertion_point(message_clear_start:protobuf_unittest.TestConflictingSymbolNames.Data3)
  data_.Clear();
  _has_bits_.Clear();
  if (_internal_metadata_.have_unknown_fields()) {
    mutable_unknown_fields()->Clear();
  }
}

bool TestConflictingSymbolNames_Data3::MergePartialFromCodedStream(
    ::google::protobuf::io::CodedInputStream* input) {
#define DO_(EXPRESSION) if (!GOOGLE_PREDICT_TRUE(EXPRESSION)) goto failure
  ::google::protobuf::uint32 tag;
  // @@protoc_insertion_point(parse_start:protobuf_unittest.TestConflictingSymbolNames.Data3)
  for (;;) {
    ::std::pair< ::google::protobuf::uint32, bool> p = input->ReadTagWithCutoff(127);
    tag = p.first;
    if (!p.second) goto handle_unusual;
    switch (::google::protobuf::internal::WireFormatLite::GetTagFieldNumber(tag)) {
      // repeated string data = 1;
      case 1: {
        if (tag == 10) {
         parse_data:
          DO_(::google::protobuf::internal::WireFormatLite::ReadString(
                input, this->add_data()));
          ::google::protobuf::internal::WireFormat::VerifyUTF8StringNamedField(
            this->data(this->data_size() - 1).data(),
            this->data(this->data_size() - 1).length(),
            ::google::protobuf::internal::WireFormat::PARSE,
            "protobuf_unittest.TestConflictingSymbolNames.Data3.data");
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(10)) goto parse_data;
        if (input->ExpectAtEnd()) goto success;
        break;
      }

      default: {
      handle_unusual:
        if (tag == 0 ||
            ::google::protobuf::internal::WireFormatLite::GetTagWireType(tag) ==
            ::google::protobuf::internal::WireFormatLite::WIRETYPE_END_GROUP) {
          goto success;
        }
        DO_(::google::protobuf::internal::WireFormat::SkipField(
              input, tag, mutable_unknown_fields()));
        break;
      }
    }
  }
success:
  // @@protoc_insertion_point(parse_success:protobuf_unittest.TestConflictingSymbolNames.Data3)
  return true;
failure:
  // @@protoc_insertion_point(parse_failure:protobuf_unittest.TestConflictingSymbolNames.Data3)
  return false;
#undef DO_
}

void TestConflictingSymbolNames_Data3::SerializeWithCachedSizes(
    ::google::protobuf::io::CodedOutputStream* output) const {
  // @@protoc_insertion_point(serialize_start:protobuf_unittest.TestConflictingSymbolNames.Data3)
  // repeated string data = 1;
  for (int i = 0; i < this->data_size(); i++) {
    ::google::protobuf::internal::WireFormat::VerifyUTF8StringNamedField(
      this->data(i).data(), this->data(i).length(),
      ::google::protobuf::internal::WireFormat::SERIALIZE,
      "protobuf_unittest.TestConflictingSymbolNames.Data3.data");
    ::google::protobuf::internal::WireFormatLite::WriteString(
      1, this->data(i), output);
  }

  if (_internal_metadata_.have_unknown_fields()) {
    ::google::protobuf::internal::WireFormat::SerializeUnknownFields(
        unknown_fields(), output);
  }
  // @@protoc_insertion_point(serialize_end:protobuf_unittest.TestConflictingSymbolNames.Data3)
}

::google::protobuf::uint8* TestConflictingSymbolNames_Data3::InternalSerializeWithCachedSizesToArray(
    bool deterministic, ::google::protobuf::uint8* target) const {
  (void)deterministic; // Unused
  // @@protoc_insertion_point(serialize_to_array_start:protobuf_unittest.TestConflictingSymbolNames.Data3)
  // repeated string data = 1;
  for (int i = 0; i < this->data_size(); i++) {
    ::google::protobuf::internal::WireFormat::VerifyUTF8StringNamedField(
      this->data(i).data(), this->data(i).length(),
      ::google::protobuf::internal::WireFormat::SERIALIZE,
      "protobuf_unittest.TestConflictingSymbolNames.Data3.data");
    target = ::google::protobuf::internal::WireFormatLite::
      WriteStringToArray(1, this->data(i), target);
  }

  if (_internal_metadata_.have_unknown_fields()) {
    target = ::google::protobuf::internal::WireFormat::SerializeUnknownFieldsToArray(
        unknown_fields(), target);
  }
  // @@protoc_insertion_point(serialize_to_array_end:protobuf_unittest.TestConflictingSymbolNames.Data3)
  return target;
}

size_t TestConflictingSymbolNames_Data3::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:protobuf_unittest.TestConflictingSymbolNames.Data3)
  size_t total_size = 0;

  // repeated string data = 1;
  total_size += 1 *
      ::google::protobuf::internal::FromIntSize(this->data_size());
  for (int i = 0; i < this->data_size(); i++) {
    total_size += ::google::protobuf::internal::WireFormatLite::StringSize(
      this->data(i));
  }

  if (_internal_metadata_.have_unknown_fields()) {
    total_size +=
      ::google::protobuf::internal::WireFormat::ComputeUnknownFieldsSize(
        unknown_fields());
  }
  int cached_size = ::google::protobuf::internal::ToCachedSize(total_size);
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = cached_size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
  return total_size;
}

void TestConflictingSymbolNames_Data3::MergeFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_merge_from_start:protobuf_unittest.TestConflictingSymbolNames.Data3)
  if (GOOGLE_PREDICT_FALSE(&from == this)) MergeFromFail(__LINE__);
  const TestConflictingSymbolNames_Data3* source =
      ::google::protobuf::internal::DynamicCastToGenerated<const TestConflictingSymbolNames_Data3>(
          &from);
  if (source == NULL) {
  // @@protoc_insertion_point(generalized_merge_from_cast_fail:protobuf_unittest.TestConflictingSymbolNames.Data3)
    ::google::protobuf::internal::ReflectionOps::Merge(from, this);
  } else {
  // @@protoc_insertion_point(generalized_merge_from_cast_success:protobuf_unittest.TestConflictingSymbolNames.Data3)
    UnsafeMergeFrom(*source);
  }
}

void TestConflictingSymbolNames_Data3::MergeFrom(const TestConflictingSymbolNames_Data3& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:protobuf_unittest.TestConflictingSymbolNames.Data3)
  if (GOOGLE_PREDICT_TRUE(&from != this)) {
    UnsafeMergeFrom(from);
  } else {
    MergeFromFail(__LINE__);
  }
}

void TestConflictingSymbolNames_Data3::UnsafeMergeFrom(const TestConflictingSymbolNames_Data3& from) {
  GOOGLE_DCHECK(&from != this);
  data_.UnsafeMergeFrom(from.data_);
  if (from._internal_metadata_.have_unknown_fields()) {
    ::google::protobuf::UnknownFieldSet::MergeToInternalMetdata(
      from.unknown_fields(), &_internal_metadata_);
  }
}

void TestConflictingSymbolNames_Data3::CopyFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_copy_from_start:protobuf_unittest.TestConflictingSymbolNames.Data3)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

void TestConflictingSymbolNames_Data3::CopyFrom(const TestConflictingSymbolNames_Data3& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:protobuf_unittest.TestConflictingSymbolNames.Data3)
  if (&from == this) return;
  Clear();
  UnsafeMergeFrom(from);
}

bool TestConflictingSymbolNames_Data3::IsInitialized() const {

  return true;
}

void TestConflictingSymbolNames_Data3::Swap(TestConflictingSymbolNames_Data3* other) {
  if (other == this) return;
  InternalSwap(other);
}
void TestConflictingSymbolNames_Data3::InternalSwap(TestConflictingSymbolNames_Data3* other) {
  data_.UnsafeArenaSwap(&other->data_);
  std::swap(_has_bits_[0], other->_has_bits_[0]);
  _internal_metadata_.Swap(&other->_internal_metadata_);
  std::swap(_cached_size_, other->_cached_size_);
}

::google::protobuf::Metadata TestConflictingSymbolNames_Data3::GetMetadata() const {
  protobuf_AssignDescriptorsOnce();
  ::google::protobuf::Metadata metadata;
  metadata.descriptor = TestConflictingSymbolNames_Data3_descriptor_;
  metadata.reflection = TestConflictingSymbolNames_Data3_reflection_;
  return metadata;
}


// -------------------------------------------------------------------

#if !defined(_MSC_VER) || _MSC_VER >= 1900
const int TestConflictingSymbolNames_Data4::kDataFieldNumber;
#endif  // !defined(_MSC_VER) || _MSC_VER >= 1900

TestConflictingSymbolNames_Data4::TestConflictingSymbolNames_Data4()
  : ::google::protobuf::Message(), _internal_metadata_(NULL) {
  if (this != internal_default_instance()) protobuf_InitDefaults_google_2fprotobuf_2fcompiler_2fcpp_2fcpp_5ftest_5fbad_5fidentifiers_2eproto();
  SharedCtor();
  // @@protoc_insertion_point(constructor:protobuf_unittest.TestConflictingSymbolNames.Data4)
}

void TestConflictingSymbolNames_Data4::InitAsDefaultInstance() {
}

TestConflictingSymbolNames_Data4::TestConflictingSymbolNames_Data4(const TestConflictingSymbolNames_Data4& from)
  : ::google::protobuf::Message(),
    _internal_metadata_(NULL) {
  SharedCtor();
  UnsafeMergeFrom(from);
  // @@protoc_insertion_point(copy_constructor:protobuf_unittest.TestConflictingSymbolNames.Data4)
}

void TestConflictingSymbolNames_Data4::SharedCtor() {
  _cached_size_ = 0;
}

TestConflictingSymbolNames_Data4::~TestConflictingSymbolNames_Data4() {
  // @@protoc_insertion_point(destructor:protobuf_unittest.TestConflictingSymbolNames.Data4)
  SharedDtor();
}

void TestConflictingSymbolNames_Data4::SharedDtor() {
}

void TestConflictingSymbolNames_Data4::SetCachedSize(int size) const {
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
}
const ::google::protobuf::Descriptor* TestConflictingSymbolNames_Data4::descriptor() {
  protobuf_AssignDescriptorsOnce();
  return TestConflictingSymbolNames_Data4_descriptor_;
}

const TestConflictingSymbolNames_Data4& TestConflictingSymbolNames_Data4::default_instance() {
  protobuf_InitDefaults_google_2fprotobuf_2fcompiler_2fcpp_2fcpp_5ftest_5fbad_5fidentifiers_2eproto();
  return *internal_default_instance();
}

::google::protobuf::internal::ExplicitlyConstructed<TestConflictingSymbolNames_Data4> TestConflictingSymbolNames_Data4_default_instance_;

TestConflictingSymbolNames_Data4* TestConflictingSymbolNames_Data4::New(::google::protobuf::Arena* arena) const {
  TestConflictingSymbolNames_Data4* n = new TestConflictingSymbolNames_Data4;
  if (arena != NULL) {
    arena->Own(n);
  }
  return n;
}

void TestConflictingSymbolNames_Data4::Clear() {
// @@protoc_insertion_point(message_clear_start:protobuf_unittest.TestConflictingSymbolNames.Data4)
  data_.Clear();
  _has_bits_.Clear();
  if (_internal_metadata_.have_unknown_fields()) {
    mutable_unknown_fields()->Clear();
  }
}

bool TestConflictingSymbolNames_Data4::MergePartialFromCodedStream(
    ::google::protobuf::io::CodedInputStream* input) {
#define DO_(EXPRESSION) if (!GOOGLE_PREDICT_TRUE(EXPRESSION)) goto failure
  ::google::protobuf::uint32 tag;
  // @@protoc_insertion_point(parse_start:protobuf_unittest.TestConflictingSymbolNames.Data4)
  for (;;) {
    ::std::pair< ::google::protobuf::uint32, bool> p = input->ReadTagWithCutoff(127);
    tag = p.first;
    if (!p.second) goto handle_unusual;
    switch (::google::protobuf::internal::WireFormatLite::GetTagFieldNumber(tag)) {
      // repeated .protobuf_unittest.TestConflictingSymbolNames.Data4 data = 1;
      case 1: {
        if (tag == 10) {
          DO_(input->IncrementRecursionDepth());
         parse_loop_data:
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessageNoVirtualNoRecursionDepth(
                input, add_data()));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(10)) goto parse_loop_data;
        input->UnsafeDecrementRecursionDepth();
        if (input->ExpectAtEnd()) goto success;
        break;
      }

      default: {
      handle_unusual:
        if (tag == 0 ||
            ::google::protobuf::internal::WireFormatLite::GetTagWireType(tag) ==
            ::google::protobuf::internal::WireFormatLite::WIRETYPE_END_GROUP) {
          goto success;
        }
        DO_(::google::protobuf::internal::WireFormat::SkipField(
              input, tag, mutable_unknown_fields()));
        break;
      }
    }
  }
success:
  // @@protoc_insertion_point(parse_success:protobuf_unittest.TestConflictingSymbolNames.Data4)
  return true;
failure:
  // @@protoc_insertion_point(parse_failure:protobuf_unittest.TestConflictingSymbolNames.Data4)
  return false;
#undef DO_
}

void TestConflictingSymbolNames_Data4::SerializeWithCachedSizes(
    ::google::protobuf::io::CodedOutputStream* output) const {
  // @@protoc_insertion_point(serialize_start:protobuf_unittest.TestConflictingSymbolNames.Data4)
  // repeated .protobuf_unittest.TestConflictingSymbolNames.Data4 data = 1;
  for (unsigned int i = 0, n = this->data_size(); i < n; i++) {
    ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
      1, this->data(i), output);
  }

  if (_internal_metadata_.have_unknown_fields()) {
    ::google::protobuf::internal::WireFormat::SerializeUnknownFields(
        unknown_fields(), output);
  }
  // @@protoc_insertion_point(serialize_end:protobuf_unittest.TestConflictingSymbolNames.Data4)
}

::google::protobuf::uint8* TestConflictingSymbolNames_Data4::InternalSerializeWithCachedSizesToArray(
    bool deterministic, ::google::protobuf::uint8* target) const {
  (void)deterministic; // Unused
  // @@protoc_insertion_point(serialize_to_array_start:protobuf_unittest.TestConflictingSymbolNames.Data4)
  // repeated .protobuf_unittest.TestConflictingSymbolNames.Data4 data = 1;
  for (unsigned int i = 0, n = this->data_size(); i < n; i++) {
    target = ::google::protobuf::internal::WireFormatLite::
      InternalWriteMessageNoVirtualToArray(
        1, this->data(i), false, target);
  }

  if (_internal_metadata_.have_unknown_fields()) {
    target = ::google::protobuf::internal::WireFormat::SerializeUnknownFieldsToArray(
        unknown_fields(), target);
  }
  // @@protoc_insertion_point(serialize_to_array_end:protobuf_unittest.TestConflictingSymbolNames.Data4)
  return target;
}

size_t TestConflictingSymbolNames_Data4::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:protobuf_unittest.TestConflictingSymbolNames.Data4)
  size_t total_size = 0;

  // repeated .protobuf_unittest.TestConflictingSymbolNames.Data4 data = 1;
  {
    unsigned int count = this->data_size();
    total_size += 1UL * count;
    for (unsigned int i = 0; i < count; i++) {
      total_size +=
        ::google::protobuf::internal::WireFormatLite::MessageSizeNoVirtual(
          this->data(i));
    }
  }

  if (_internal_metadata_.have_unknown_fields()) {
    total_size +=
      ::google::protobuf::internal::WireFormat::ComputeUnknownFieldsSize(
        unknown_fields());
  }
  int cached_size = ::google::protobuf::internal::ToCachedSize(total_size);
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = cached_size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
  return total_size;
}

void TestConflictingSymbolNames_Data4::MergeFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_merge_from_start:protobuf_unittest.TestConflictingSymbolNames.Data4)
  if (GOOGLE_PREDICT_FALSE(&from == this)) MergeFromFail(__LINE__);
  const TestConflictingSymbolNames_Data4* source =
      ::google::protobuf::internal::DynamicCastToGenerated<const TestConflictingSymbolNames_Data4>(
          &from);
  if (source == NULL) {
  // @@protoc_insertion_point(generalized_merge_from_cast_fail:protobuf_unittest.TestConflictingSymbolNames.Data4)
    ::google::protobuf::internal::ReflectionOps::Merge(from, this);
  } else {
  // @@protoc_insertion_point(generalized_merge_from_cast_success:protobuf_unittest.TestConflictingSymbolNames.Data4)
    UnsafeMergeFrom(*source);
  }
}

void TestConflictingSymbolNames_Data4::MergeFrom(const TestConflictingSymbolNames_Data4& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:protobuf_unittest.TestConflictingSymbolNames.Data4)
  if (GOOGLE_PREDICT_TRUE(&from != this)) {
    UnsafeMergeFrom(from);
  } else {
    MergeFromFail(__LINE__);
  }
}

void TestConflictingSymbolNames_Data4::UnsafeMergeFrom(const TestConflictingSymbolNames_Data4& from) {
  GOOGLE_DCHECK(&from != this);
  data_.MergeFrom(from.data_);
  if (from._internal_metadata_.have_unknown_fields()) {
    ::google::protobuf::UnknownFieldSet::MergeToInternalMetdata(
      from.unknown_fields(), &_internal_metadata_);
  }
}

void TestConflictingSymbolNames_Data4::CopyFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_copy_from_start:protobuf_unittest.TestConflictingSymbolNames.Data4)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

void TestConflictingSymbolNames_Data4::CopyFrom(const TestConflictingSymbolNames_Data4& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:protobuf_unittest.TestConflictingSymbolNames.Data4)
  if (&from == this) return;
  Clear();
  UnsafeMergeFrom(from);
}

bool TestConflictingSymbolNames_Data4::IsInitialized() const {

  return true;
}

void TestConflictingSymbolNames_Data4::Swap(TestConflictingSymbolNames_Data4* other) {
  if (other == this) return;
  InternalSwap(other);
}
void TestConflictingSymbolNames_Data4::InternalSwap(TestConflictingSymbolNames_Data4* other) {
  data_.UnsafeArenaSwap(&other->data_);
  std::swap(_has_bits_[0], other->_has_bits_[0]);
  _internal_metadata_.Swap(&other->_internal_metadata_);
  std::swap(_cached_size_, other->_cached_size_);
}

::google::protobuf::Metadata TestConflictingSymbolNames_Data4::GetMetadata() const {
  protobuf_AssignDescriptorsOnce();
  ::google::protobuf::Metadata metadata;
  metadata.descriptor = TestConflictingSymbolNames_Data4_descriptor_;
  metadata.reflection = TestConflictingSymbolNames_Data4_reflection_;
  return metadata;
}


// -------------------------------------------------------------------

#if !defined(_MSC_VER) || _MSC_VER >= 1900
const int TestConflictingSymbolNames_Data5::kDataFieldNumber;
#endif  // !defined(_MSC_VER) || _MSC_VER >= 1900

TestConflictingSymbolNames_Data5::TestConflictingSymbolNames_Data5()
  : ::google::protobuf::Message(), _internal_metadata_(NULL) {
  if (this != internal_default_instance()) protobuf_InitDefaults_google_2fprotobuf_2fcompiler_2fcpp_2fcpp_5ftest_5fbad_5fidentifiers_2eproto();
  SharedCtor();
  // @@protoc_insertion_point(constructor:protobuf_unittest.TestConflictingSymbolNames.Data5)
}

void TestConflictingSymbolNames_Data5::InitAsDefaultInstance() {
}

TestConflictingSymbolNames_Data5::TestConflictingSymbolNames_Data5(const TestConflictingSymbolNames_Data5& from)
  : ::google::protobuf::Message(),
    _internal_metadata_(NULL) {
  SharedCtor();
  UnsafeMergeFrom(from);
  // @@protoc_insertion_point(copy_constructor:protobuf_unittest.TestConflictingSymbolNames.Data5)
}

void TestConflictingSymbolNames_Data5::SharedCtor() {
  _cached_size_ = 0;
}

TestConflictingSymbolNames_Data5::~TestConflictingSymbolNames_Data5() {
  // @@protoc_insertion_point(destructor:protobuf_unittest.TestConflictingSymbolNames.Data5)
  SharedDtor();
}

void TestConflictingSymbolNames_Data5::SharedDtor() {
}

void TestConflictingSymbolNames_Data5::SetCachedSize(int size) const {
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
}
const ::google::protobuf::Descriptor* TestConflictingSymbolNames_Data5::descriptor() {
  protobuf_AssignDescriptorsOnce();
  return TestConflictingSymbolNames_Data5_descriptor_;
}

const TestConflictingSymbolNames_Data5& TestConflictingSymbolNames_Data5::default_instance() {
  protobuf_InitDefaults_google_2fprotobuf_2fcompiler_2fcpp_2fcpp_5ftest_5fbad_5fidentifiers_2eproto();
  return *internal_default_instance();
}

::google::protobuf::internal::ExplicitlyConstructed<TestConflictingSymbolNames_Data5> TestConflictingSymbolNames_Data5_default_instance_;

TestConflictingSymbolNames_Data5* TestConflictingSymbolNames_Data5::New(::google::protobuf::Arena* arena) const {
  TestConflictingSymbolNames_Data5* n = new TestConflictingSymbolNames_Data5;
  if (arena != NULL) {
    arena->Own(n);
  }
  return n;
}

void TestConflictingSymbolNames_Data5::Clear() {
// @@protoc_insertion_point(message_clear_start:protobuf_unittest.TestConflictingSymbolNames.Data5)
  data_.Clear();
  _has_bits_.Clear();
  if (_internal_metadata_.have_unknown_fields()) {
    mutable_unknown_fields()->Clear();
  }
}

bool TestConflictingSymbolNames_Data5::MergePartialFromCodedStream(
    ::google::protobuf::io::CodedInputStream* input) {
#define DO_(EXPRESSION) if (!GOOGLE_PREDICT_TRUE(EXPRESSION)) goto failure
  ::google::protobuf::uint32 tag;
  // @@protoc_insertion_point(parse_start:protobuf_unittest.TestConflictingSymbolNames.Data5)
  for (;;) {
    ::std::pair< ::google::protobuf::uint32, bool> p = input->ReadTagWithCutoff(127);
    tag = p.first;
    if (!p.second) goto handle_unusual;
    switch (::google::protobuf::internal::WireFormatLite::GetTagFieldNumber(tag)) {
      // repeated string data = 1 [ctype = STRING_PIECE];
      case 1: {
        if (tag == 10) {
         parse_data:
          DO_(::google::protobuf::internal::WireFormatLite::ReadString(
                input, this->add_data()));
          ::google::protobuf::internal::WireFormat::VerifyUTF8StringNamedField(
            this->data(this->data_size() - 1).data(),
            this->data(this->data_size() - 1).length(),
            ::google::protobuf::internal::WireFormat::PARSE,
            "protobuf_unittest.TestConflictingSymbolNames.Data5.data");
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(10)) goto parse_data;
        if (input->ExpectAtEnd()) goto success;
        break;
      }

      default: {
      handle_unusual:
        if (tag == 0 ||
            ::google::protobuf::internal::WireFormatLite::GetTagWireType(tag) ==
            ::google::protobuf::internal::WireFormatLite::WIRETYPE_END_GROUP) {
          goto success;
        }
        DO_(::google::protobuf::internal::WireFormat::SkipField(
              input, tag, mutable_unknown_fields()));
        break;
      }
    }
  }
success:
  // @@protoc_insertion_point(parse_success:protobuf_unittest.TestConflictingSymbolNames.Data5)
  return true;
failure:
  // @@protoc_insertion_point(parse_failure:protobuf_unittest.TestConflictingSymbolNames.Data5)
  return false;
#undef DO_
}

void TestConflictingSymbolNames_Data5::SerializeWithCachedSizes(
    ::google::protobuf::io::CodedOutputStream* output) const {
  // @@protoc_insertion_point(serialize_start:protobuf_unittest.TestConflictingSymbolNames.Data5)
  // repeated string data = 1 [ctype = STRING_PIECE];
  for (int i = 0; i < this->data_size(); i++) {
    ::google::protobuf::internal::WireFormat::VerifyUTF8StringNamedField(
      this->data(i).data(), this->data(i).length(),
      ::google::protobuf::internal::WireFormat::SERIALIZE,
      "protobuf_unittest.TestConflictingSymbolNames.Data5.data");
    ::google::protobuf::internal::WireFormatLite::WriteString(
      1, this->data(i), output);
  }

  if (_internal_metadata_.have_unknown_fields()) {
    ::google::protobuf::internal::WireFormat::SerializeUnknownFields(
        unknown_fields(), output);
  }
  // @@protoc_insertion_point(serialize_end:protobuf_unittest.TestConflictingSymbolNames.Data5)
}

::google::protobuf::uint8* TestConflictingSymbolNames_Data5::InternalSerializeWithCachedSizesToArray(
    bool deterministic, ::google::protobuf::uint8* target) const {
  (void)deterministic; // Unused
  // @@protoc_insertion_point(serialize_to_array_start:protobuf_unittest.TestConflictingSymbolNames.Data5)
  // repeated string data = 1 [ctype = STRING_PIECE];
  for (int i = 0; i < this->data_size(); i++) {
    ::google::protobuf::internal::WireFormat::VerifyUTF8StringNamedField(
      this->data(i).data(), this->data(i).length(),
      ::google::protobuf::internal::WireFormat::SERIALIZE,
      "protobuf_unittest.TestConflictingSymbolNames.Data5.data");
    target = ::google::protobuf::internal::WireFormatLite::
      WriteStringToArray(1, this->data(i), target);
  }

  if (_internal_metadata_.have_unknown_fields()) {
    target = ::google::protobuf::internal::WireFormat::SerializeUnknownFieldsToArray(
        unknown_fields(), target);
  }
  // @@protoc_insertion_point(serialize_to_array_end:protobuf_unittest.TestConflictingSymbolNames.Data5)
  return target;
}

size_t TestConflictingSymbolNames_Data5::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:protobuf_unittest.TestConflictingSymbolNames.Data5)
  size_t total_size = 0;

  // repeated string data = 1 [ctype = STRING_PIECE];
  total_size += 1 *
      ::google::protobuf::internal::FromIntSize(this->data_size());
  for (int i = 0; i < this->data_size(); i++) {
    total_size += ::google::protobuf::internal::WireFormatLite::StringSize(
      this->data(i));
  }

  if (_internal_metadata_.have_unknown_fields()) {
    total_size +=
      ::google::protobuf::internal::WireFormat::ComputeUnknownFieldsSize(
        unknown_fields());
  }
  int cached_size = ::google::protobuf::internal::ToCachedSize(total_size);
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = cached_size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
  return total_size;
}

void TestConflictingSymbolNames_Data5::MergeFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_merge_from_start:protobuf_unittest.TestConflictingSymbolNames.Data5)
  if (GOOGLE_PREDICT_FALSE(&from == this)) MergeFromFail(__LINE__);
  const TestConflictingSymbolNames_Data5* source =
      ::google::protobuf::internal::DynamicCastToGenerated<const TestConflictingSymbolNames_Data5>(
          &from);
  if (source == NULL) {
  // @@protoc_insertion_point(generalized_merge_from_cast_fail:protobuf_unittest.TestConflictingSymbolNames.Data5)
    ::google::protobuf::internal::ReflectionOps::Merge(from, this);
  } else {
  // @@protoc_insertion_point(generalized_merge_from_cast_success:protobuf_unittest.TestConflictingSymbolNames.Data5)
    UnsafeMergeFrom(*source);
  }
}

void TestConflictingSymbolNames_Data5::MergeFrom(const TestConflictingSymbolNames_Data5& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:protobuf_unittest.TestConflictingSymbolNames.Data5)
  if (GOOGLE_PREDICT_TRUE(&from != this)) {
    UnsafeMergeFrom(from);
  } else {
    MergeFromFail(__LINE__);
  }
}

void TestConflictingSymbolNames_Data5::UnsafeMergeFrom(const TestConflictingSymbolNames_Data5& from) {
  GOOGLE_DCHECK(&from != this);
  data_.UnsafeMergeFrom(from.data_);
  if (from._internal_metadata_.have_unknown_fields()) {
    ::google::protobuf::UnknownFieldSet::MergeToInternalMetdata(
      from.unknown_fields(), &_internal_metadata_);
  }
}

void TestConflictingSymbolNames_Data5::CopyFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_copy_from_start:protobuf_unittest.TestConflictingSymbolNames.Data5)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

void TestConflictingSymbolNames_Data5::CopyFrom(const TestConflictingSymbolNames_Data5& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:protobuf_unittest.TestConflictingSymbolNames.Data5)
  if (&from == this) return;
  Clear();
  UnsafeMergeFrom(from);
}

bool TestConflictingSymbolNames_Data5::IsInitialized() const {

  return true;
}

void TestConflictingSymbolNames_Data5::Swap(TestConflictingSymbolNames_Data5* other) {
  if (other == this) return;
  InternalSwap(other);
}
void TestConflictingSymbolNames_Data5::InternalSwap(TestConflictingSymbolNames_Data5* other) {
  data_.UnsafeArenaSwap(&other->data_);
  std::swap(_has_bits_[0], other->_has_bits_[0]);
  _internal_metadata_.Swap(&other->_internal_metadata_);
  std::swap(_cached_size_, other->_cached_size_);
}

::google::protobuf::Metadata TestConflictingSymbolNames_Data5::GetMetadata() const {
  protobuf_AssignDescriptorsOnce();
  ::google::protobuf::Metadata metadata;
  metadata.descriptor = TestConflictingSymbolNames_Data5_descriptor_;
  metadata.reflection = TestConflictingSymbolNames_Data5_reflection_;
  return metadata;
}


// -------------------------------------------------------------------

#if !defined(_MSC_VER) || _MSC_VER >= 1900
const int TestConflictingSymbolNames_Data6::kDataFieldNumber;
#endif  // !defined(_MSC_VER) || _MSC_VER >= 1900

TestConflictingSymbolNames_Data6::TestConflictingSymbolNames_Data6()
  : ::google::protobuf::Message(), _internal_metadata_(NULL) {
  if (this != internal_default_instance()) protobuf_InitDefaults_google_2fprotobuf_2fcompiler_2fcpp_2fcpp_5ftest_5fbad_5fidentifiers_2eproto();
  SharedCtor();
  // @@protoc_insertion_point(constructor:protobuf_unittest.TestConflictingSymbolNames.Data6)
}

void TestConflictingSymbolNames_Data6::InitAsDefaultInstance() {
}

TestConflictingSymbolNames_Data6::TestConflictingSymbolNames_Data6(const TestConflictingSymbolNames_Data6& from)
  : ::google::protobuf::Message(),
    _internal_metadata_(NULL) {
  SharedCtor();
  UnsafeMergeFrom(from);
  // @@protoc_insertion_point(copy_constructor:protobuf_unittest.TestConflictingSymbolNames.Data6)
}

void TestConflictingSymbolNames_Data6::SharedCtor() {
  _cached_size_ = 0;
}

TestConflictingSymbolNames_Data6::~TestConflictingSymbolNames_Data6() {
  // @@protoc_insertion_point(destructor:protobuf_unittest.TestConflictingSymbolNames.Data6)
  SharedDtor();
}

void TestConflictingSymbolNames_Data6::SharedDtor() {
}

void TestConflictingSymbolNames_Data6::SetCachedSize(int size) const {
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
}
const ::google::protobuf::Descriptor* TestConflictingSymbolNames_Data6::descriptor() {
  protobuf_AssignDescriptorsOnce();
  return TestConflictingSymbolNames_Data6_descriptor_;
}

const TestConflictingSymbolNames_Data6& TestConflictingSymbolNames_Data6::default_instance() {
  protobuf_InitDefaults_google_2fprotobuf_2fcompiler_2fcpp_2fcpp_5ftest_5fbad_5fidentifiers_2eproto();
  return *internal_default_instance();
}

::google::protobuf::internal::ExplicitlyConstructed<TestConflictingSymbolNames_Data6> TestConflictingSymbolNames_Data6_default_instance_;

TestConflictingSymbolNames_Data6* TestConflictingSymbolNames_Data6::New(::google::protobuf::Arena* arena) const {
  TestConflictingSymbolNames_Data6* n = new TestConflictingSymbolNames_Data6;
  if (arena != NULL) {
    arena->Own(n);
  }
  return n;
}

void TestConflictingSymbolNames_Data6::Clear() {
// @@protoc_insertion_point(message_clear_start:protobuf_unittest.TestConflictingSymbolNames.Data6)
  data_.Clear();
  _has_bits_.Clear();
  if (_internal_metadata_.have_unknown_fields()) {
    mutable_unknown_fields()->Clear();
  }
}

bool TestConflictingSymbolNames_Data6::MergePartialFromCodedStream(
    ::google::protobuf::io::CodedInputStream* input) {
#define DO_(EXPRESSION) if (!GOOGLE_PREDICT_TRUE(EXPRESSION)) goto failure
  ::google::protobuf::uint32 tag;
  // @@protoc_insertion_point(parse_start:protobuf_unittest.TestConflictingSymbolNames.Data6)
  for (;;) {
    ::std::pair< ::google::protobuf::uint32, bool> p = input->ReadTagWithCutoff(127);
    tag = p.first;
    if (!p.second) goto handle_unusual;
    switch (::google::protobuf::internal::WireFormatLite::GetTagFieldNumber(tag)) {
      // repeated string data = 1 [ctype = CORD];
      case 1: {
        if (tag == 10) {
         parse_data:
          DO_(::google::protobuf::internal::WireFormatLite::ReadString(
                input, this->add_data()));
          ::google::protobuf::internal::WireFormat::VerifyUTF8StringNamedField(
            this->data(this->data_size() - 1).data(),
            this->data(this->data_size() - 1).length(),
            ::google::protobuf::internal::WireFormat::PARSE,
            "protobuf_unittest.TestConflictingSymbolNames.Data6.data");
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(10)) goto parse_data;
        if (input->ExpectAtEnd()) goto success;
        break;
      }

      default: {
      handle_unusual:
        if (tag == 0 ||
            ::google::protobuf::internal::WireFormatLite::GetTagWireType(tag) ==
            ::google::protobuf::internal::WireFormatLite::WIRETYPE_END_GROUP) {
          goto success;
        }
        DO_(::google::protobuf::internal::WireFormat::SkipField(
              input, tag, mutable_unknown_fields()));
        break;
      }
    }
  }
success:
  // @@protoc_insertion_point(parse_success:protobuf_unittest.TestConflictingSymbolNames.Data6)
  return true;
failure:
  // @@protoc_insertion_point(parse_failure:protobuf_unittest.TestConflictingSymbolNames.Data6)
  return false;
#undef DO_
}

void TestConflictingSymbolNames_Data6::SerializeWithCachedSizes(
    ::google::protobuf::io::CodedOutputStream* output) const {
  // @@protoc_insertion_point(serialize_start:protobuf_unittest.TestConflictingSymbolNames.Data6)
  // repeated string data = 1 [ctype = CORD];
  for (int i = 0; i < this->data_size(); i++) {
    ::google::protobuf::internal::WireFormat::VerifyUTF8StringNamedField(
      this->data(i).data(), this->data(i).length(),
      ::google::protobuf::internal::WireFormat::SERIALIZE,
      "protobuf_unittest.TestConflictingSymbolNames.Data6.data");
    ::google::protobuf::internal::WireFormatLite::WriteString(
      1, this->data(i), output);
  }

  if (_internal_metadata_.have_unknown_fields()) {
    ::google::protobuf::internal::WireFormat::SerializeUnknownFields(
        unknown_fields(), output);
  }
  // @@protoc_insertion_point(serialize_end:protobuf_unittest.TestConflictingSymbolNames.Data6)
}

::google::protobuf::uint8* TestConflictingSymbolNames_Data6::InternalSerializeWithCachedSizesToArray(
    bool deterministic, ::google::protobuf::uint8* target) const {
  (void)deterministic; // Unused
  // @@protoc_insertion_point(serialize_to_array_start:protobuf_unittest.TestConflictingSymbolNames.Data6)
  // repeated string data = 1 [ctype = CORD];
  for (int i = 0; i < this->data_size(); i++) {
    ::google::protobuf::internal::WireFormat::VerifyUTF8StringNamedField(
      this->data(i).data(), this->data(i).length(),
      ::google::protobuf::internal::WireFormat::SERIALIZE,
      "protobuf_unittest.TestConflictingSymbolNames.Data6.data");
    target = ::google::protobuf::internal::WireFormatLite::
      WriteStringToArray(1, this->data(i), target);
  }

  if (_internal_metadata_.have_unknown_fields()) {
    target = ::google::protobuf::internal::WireFormat::SerializeUnknownFieldsToArray(
        unknown_fields(), target);
  }
  // @@protoc_insertion_point(serialize_to_array_end:protobuf_unittest.TestConflictingSymbolNames.Data6)
  return target;
}

size_t TestConflictingSymbolNames_Data6::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:protobuf_unittest.TestConflictingSymbolNames.Data6)
  size_t total_size = 0;

  // repeated string data = 1 [ctype = CORD];
  total_size += 1 *
      ::google::protobuf::internal::FromIntSize(this->data_size());
  for (int i = 0; i < this->data_size(); i++) {
    total_size += ::google::protobuf::internal::WireFormatLite::StringSize(
      this->data(i));
  }

  if (_internal_metadata_.have_unknown_fields()) {
    total_size +=
      ::google::protobuf::internal::WireFormat::ComputeUnknownFieldsSize(
        unknown_fields());
  }
  int cached_size = ::google::protobuf::internal::ToCachedSize(total_size);
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = cached_size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
  return total_size;
}

void TestConflictingSymbolNames_Data6::MergeFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_merge_from_start:protobuf_unittest.TestConflictingSymbolNames.Data6)
  if (GOOGLE_PREDICT_FALSE(&from == this)) MergeFromFail(__LINE__);
  const TestConflictingSymbolNames_Data6* source =
      ::google::protobuf::internal::DynamicCastToGenerated<const TestConflictingSymbolNames_Data6>(
          &from);
  if (source == NULL) {
  // @@protoc_insertion_point(generalized_merge_from_cast_fail:protobuf_unittest.TestConflictingSymbolNames.Data6)
    ::google::protobuf::internal::ReflectionOps::Merge(from, this);
  } else {
  // @@protoc_insertion_point(generalized_merge_from_cast_success:protobuf_unittest.TestConflictingSymbolNames.Data6)
    UnsafeMergeFrom(*source);
  }
}

void TestConflictingSymbolNames_Data6::MergeFrom(const TestConflictingSymbolNames_Data6& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:protobuf_unittest.TestConflictingSymbolNames.Data6)
  if (GOOGLE_PREDICT_TRUE(&from != this)) {
    UnsafeMergeFrom(from);
  } else {
    MergeFromFail(__LINE__);
  }
}

void TestConflictingSymbolNames_Data6::UnsafeMergeFrom(const TestConflictingSymbolNames_Data6& from) {
  GOOGLE_DCHECK(&from != this);
  data_.UnsafeMergeFrom(from.data_);
  if (from._internal_metadata_.have_unknown_fields()) {
    ::google::protobuf::UnknownFieldSet::MergeToInternalMetdata(
      from.unknown_fields(), &_internal_metadata_);
  }
}

void TestConflictingSymbolNames_Data6::CopyFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_copy_from_start:protobuf_unittest.TestConflictingSymbolNames.Data6)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

void TestConflictingSymbolNames_Data6::CopyFrom(const TestConflictingSymbolNames_Data6& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:protobuf_unittest.TestConflictingSymbolNames.Data6)
  if (&from == this) return;
  Clear();
  UnsafeMergeFrom(from);
}

bool TestConflictingSymbolNames_Data6::IsInitialized() const {

  return true;
}

void TestConflictingSymbolNames_Data6::Swap(TestConflictingSymbolNames_Data6* other) {
  if (other == this) return;
  InternalSwap(other);
}
void TestConflictingSymbolNames_Data6::InternalSwap(TestConflictingSymbolNames_Data6* other) {
  data_.UnsafeArenaSwap(&other->data_);
  std::swap(_has_bits_[0], other->_has_bits_[0]);
  _internal_metadata_.Swap(&other->_internal_metadata_);
  std::swap(_cached_size_, other->_cached_size_);
}

::google::protobuf::Metadata TestConflictingSymbolNames_Data6::GetMetadata() const {
  protobuf_AssignDescriptorsOnce();
  ::google::protobuf::Metadata metadata;
  metadata.descriptor = TestConflictingSymbolNames_Data6_descriptor_;
  metadata.reflection = TestConflictingSymbolNames_Data6_reflection_;
  return metadata;
}


// -------------------------------------------------------------------

#if !defined(_MSC_VER) || _MSC_VER >= 1900
#endif  // !defined(_MSC_VER) || _MSC_VER >= 1900

TestConflictingSymbolNames_Cord::TestConflictingSymbolNames_Cord()
  : ::google::protobuf::Message(), _internal_metadata_(NULL) {
  if (this != internal_default_instance()) protobuf_InitDefaults_google_2fprotobuf_2fcompiler_2fcpp_2fcpp_5ftest_5fbad_5fidentifiers_2eproto();
  SharedCtor();
  // @@protoc_insertion_point(constructor:protobuf_unittest.TestConflictingSymbolNames.Cord)
}

void TestConflictingSymbolNames_Cord::InitAsDefaultInstance() {
}

TestConflictingSymbolNames_Cord::TestConflictingSymbolNames_Cord(const TestConflictingSymbolNames_Cord& from)
  : ::google::protobuf::Message(),
    _internal_metadata_(NULL) {
  SharedCtor();
  UnsafeMergeFrom(from);
  // @@protoc_insertion_point(copy_constructor:protobuf_unittest.TestConflictingSymbolNames.Cord)
}

void TestConflictingSymbolNames_Cord::SharedCtor() {
  _cached_size_ = 0;
}

TestConflictingSymbolNames_Cord::~TestConflictingSymbolNames_Cord() {
  // @@protoc_insertion_point(destructor:protobuf_unittest.TestConflictingSymbolNames.Cord)
  SharedDtor();
}

void TestConflictingSymbolNames_Cord::SharedDtor() {
}

void TestConflictingSymbolNames_Cord::SetCachedSize(int size) const {
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
}
const ::google::protobuf::Descriptor* TestConflictingSymbolNames_Cord::descriptor() {
  protobuf_AssignDescriptorsOnce();
  return TestConflictingSymbolNames_Cord_descriptor_;
}

const TestConflictingSymbolNames_Cord& TestConflictingSymbolNames_Cord::default_instance() {
  protobuf_InitDefaults_google_2fprotobuf_2fcompiler_2fcpp_2fcpp_5ftest_5fbad_5fidentifiers_2eproto();
  return *internal_default_instance();
}

::google::protobuf::internal::ExplicitlyConstructed<TestConflictingSymbolNames_Cord> TestConflictingSymbolNames_Cord_default_instance_;

TestConflictingSymbolNames_Cord* TestConflictingSymbolNames_Cord::New(::google::protobuf::Arena* arena) const {
  TestConflictingSymbolNames_Cord* n = new TestConflictingSymbolNames_Cord;
  if (arena != NULL) {
    arena->Own(n);
  }
  return n;
}

void TestConflictingSymbolNames_Cord::Clear() {
// @@protoc_insertion_point(message_clear_start:protobuf_unittest.TestConflictingSymbolNames.Cord)
  _has_bits_.Clear();
  if (_internal_metadata_.have_unknown_fields()) {
    mutable_unknown_fields()->Clear();
  }
}

bool TestConflictingSymbolNames_Cord::MergePartialFromCodedStream(
    ::google::protobuf::io::CodedInputStream* input) {
#define DO_(EXPRESSION) if (!GOOGLE_PREDICT_TRUE(EXPRESSION)) goto failure
  ::google::protobuf::uint32 tag;
  // @@protoc_insertion_point(parse_start:protobuf_unittest.TestConflictingSymbolNames.Cord)
  for (;;) {
    ::std::pair< ::google::protobuf::uint32, bool> p = input->ReadTagWithCutoff(127);
    tag = p.first;
    if (!p.second) goto handle_unusual;
  handle_unusual:
    if (tag == 0 ||
        ::google::protobuf::internal::WireFormatLite::GetTagWireType(tag) ==
        ::google::protobuf::internal::WireFormatLite::WIRETYPE_END_GROUP) {
      goto success;
    }
    DO_(::google::protobuf::internal::WireFormat::SkipField(
          input, tag, mutable_unknown_fields()));
  }
success:
  // @@protoc_insertion_point(parse_success:protobuf_unittest.TestConflictingSymbolNames.Cord)
  return true;
failure:
  // @@protoc_insertion_point(parse_failure:protobuf_unittest.TestConflictingSymbolNames.Cord)
  return false;
#undef DO_
}

void TestConflictingSymbolNames_Cord::SerializeWithCachedSizes(
    ::google::protobuf::io::CodedOutputStream* output) const {
  // @@protoc_insertion_point(serialize_start:protobuf_unittest.TestConflictingSymbolNames.Cord)
  if (_internal_metadata_.have_unknown_fields()) {
    ::google::protobuf::internal::WireFormat::SerializeUnknownFields(
        unknown_fields(), output);
  }
  // @@protoc_insertion_point(serialize_end:protobuf_unittest.TestConflictingSymbolNames.Cord)
}

::google::protobuf::uint8* TestConflictingSymbolNames_Cord::InternalSerializeWithCachedSizesToArray(
    bool deterministic, ::google::protobuf::uint8* target) const {
  (void)deterministic; // Unused
  // @@protoc_insertion_point(serialize_to_array_start:protobuf_unittest.TestConflictingSymbolNames.Cord)
  if (_internal_metadata_.have_unknown_fields()) {
    target = ::google::protobuf::internal::WireFormat::SerializeUnknownFieldsToArray(
        unknown_fields(), target);
  }
  // @@protoc_insertion_point(serialize_to_array_end:protobuf_unittest.TestConflictingSymbolNames.Cord)
  return target;
}

size_t TestConflictingSymbolNames_Cord::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:protobuf_unittest.TestConflictingSymbolNames.Cord)
  size_t total_size = 0;

  if (_internal_metadata_.have_unknown_fields()) {
    total_size +=
      ::google::protobuf::internal::WireFormat::ComputeUnknownFieldsSize(
        unknown_fields());
  }
  int cached_size = ::google::protobuf::internal::ToCachedSize(total_size);
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = cached_size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
  return total_size;
}

void TestConflictingSymbolNames_Cord::MergeFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_merge_from_start:protobuf_unittest.TestConflictingSymbolNames.Cord)
  if (GOOGLE_PREDICT_FALSE(&from == this)) MergeFromFail(__LINE__);
  const TestConflictingSymbolNames_Cord* source =
      ::google::protobuf::internal::DynamicCastToGenerated<const TestConflictingSymbolNames_Cord>(
          &from);
  if (source == NULL) {
  // @@protoc_insertion_point(generalized_merge_from_cast_fail:protobuf_unittest.TestConflictingSymbolNames.Cord)
    ::google::protobuf::internal::ReflectionOps::Merge(from, this);
  } else {
  // @@protoc_insertion_point(generalized_merge_from_cast_success:protobuf_unittest.TestConflictingSymbolNames.Cord)
    UnsafeMergeFrom(*source);
  }
}

void TestConflictingSymbolNames_Cord::MergeFrom(const TestConflictingSymbolNames_Cord& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:protobuf_unittest.TestConflictingSymbolNames.Cord)
  if (GOOGLE_PREDICT_TRUE(&from != this)) {
    UnsafeMergeFrom(from);
  } else {
    MergeFromFail(__LINE__);
  }
}

void TestConflictingSymbolNames_Cord::UnsafeMergeFrom(const TestConflictingSymbolNames_Cord& from) {
  GOOGLE_DCHECK(&from != this);
  if (from._internal_metadata_.have_unknown_fields()) {
    ::google::protobuf::UnknownFieldSet::MergeToInternalMetdata(
      from.unknown_fields(), &_internal_metadata_);
  }
}

void TestConflictingSymbolNames_Cord::CopyFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_copy_from_start:protobuf_unittest.TestConflictingSymbolNames.Cord)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

void TestConflictingSymbolNames_Cord::CopyFrom(const TestConflictingSymbolNames_Cord& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:protobuf_unittest.TestConflictingSymbolNames.Cord)
  if (&from == this) return;
  Clear();
  UnsafeMergeFrom(from);
}

bool TestConflictingSymbolNames_Cord::IsInitialized() const {

  return true;
}

void TestConflictingSymbolNames_Cord::Swap(TestConflictingSymbolNames_Cord* other) {
  if (other == this) return;
  InternalSwap(other);
}
void TestConflictingSymbolNames_Cord::InternalSwap(TestConflictingSymbolNames_Cord* other) {
  _internal_metadata_.Swap(&other->_internal_metadata_);
  std::swap(_cached_size_, other->_cached_size_);
}

::google::protobuf::Metadata TestConflictingSymbolNames_Cord::GetMetadata() const {
  protobuf_AssignDescriptorsOnce();
  ::google::protobuf::Metadata metadata;
  metadata.descriptor = TestConflictingSymbolNames_Cord_descriptor_;
  metadata.reflection = TestConflictingSymbolNames_Cord_reflection_;
  return metadata;
}


// -------------------------------------------------------------------

#if !defined(_MSC_VER) || _MSC_VER >= 1900
#endif  // !defined(_MSC_VER) || _MSC_VER >= 1900

TestConflictingSymbolNames_StringPiece::TestConflictingSymbolNames_StringPiece()
  : ::google::protobuf::Message(), _internal_metadata_(NULL) {
  if (this != internal_default_instance()) protobuf_InitDefaults_google_2fprotobuf_2fcompiler_2fcpp_2fcpp_5ftest_5fbad_5fidentifiers_2eproto();
  SharedCtor();
  // @@protoc_insertion_point(constructor:protobuf_unittest.TestConflictingSymbolNames.StringPiece)
}

void TestConflictingSymbolNames_StringPiece::InitAsDefaultInstance() {
}

TestConflictingSymbolNames_StringPiece::TestConflictingSymbolNames_StringPiece(const TestConflictingSymbolNames_StringPiece& from)
  : ::google::protobuf::Message(),
    _internal_metadata_(NULL) {
  SharedCtor();
  UnsafeMergeFrom(from);
  // @@protoc_insertion_point(copy_constructor:protobuf_unittest.TestConflictingSymbolNames.StringPiece)
}

void TestConflictingSymbolNames_StringPiece::SharedCtor() {
  _cached_size_ = 0;
}

TestConflictingSymbolNames_StringPiece::~TestConflictingSymbolNames_StringPiece() {
  // @@protoc_insertion_point(destructor:protobuf_unittest.TestConflictingSymbolNames.StringPiece)
  SharedDtor();
}

void TestConflictingSymbolNames_StringPiece::SharedDtor() {
}

void TestConflictingSymbolNames_StringPiece::SetCachedSize(int size) const {
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
}
const ::google::protobuf::Descriptor* TestConflictingSymbolNames_StringPiece::descriptor() {
  protobuf_AssignDescriptorsOnce();
  return TestConflictingSymbolNames_StringPiece_descriptor_;
}

const TestConflictingSymbolNames_StringPiece& TestConflictingSymbolNames_StringPiece::default_instance() {
  protobuf_InitDefaults_google_2fprotobuf_2fcompiler_2fcpp_2fcpp_5ftest_5fbad_5fidentifiers_2eproto();
  return *internal_default_instance();
}

::google::protobuf::internal::ExplicitlyConstructed<TestConflictingSymbolNames_StringPiece> TestConflictingSymbolNames_StringPiece_default_instance_;

TestConflictingSymbolNames_StringPiece* TestConflictingSymbolNames_StringPiece::New(::google::protobuf::Arena* arena) const {
  TestConflictingSymbolNames_StringPiece* n = new TestConflictingSymbolNames_StringPiece;
  if (arena != NULL) {
    arena->Own(n);
  }
  return n;
}

void TestConflictingSymbolNames_StringPiece::Clear() {
// @@protoc_insertion_point(message_clear_start:protobuf_unittest.TestConflictingSymbolNames.StringPiece)
  _has_bits_.Clear();
  if (_internal_metadata_.have_unknown_fields()) {
    mutable_unknown_fields()->Clear();
  }
}

bool TestConflictingSymbolNames_StringPiece::MergePartialFromCodedStream(
    ::google::protobuf::io::CodedInputStream* input) {
#define DO_(EXPRESSION) if (!GOOGLE_PREDICT_TRUE(EXPRESSION)) goto failure
  ::google::protobuf::uint32 tag;
  // @@protoc_insertion_point(parse_start:protobuf_unittest.TestConflictingSymbolNames.StringPiece)
  for (;;) {
    ::std::pair< ::google::protobuf::uint32, bool> p = input->ReadTagWithCutoff(127);
    tag = p.first;
    if (!p.second) goto handle_unusual;
  handle_unusual:
    if (tag == 0 ||
        ::google::protobuf::internal::WireFormatLite::GetTagWireType(tag) ==
        ::google::protobuf::internal::WireFormatLite::WIRETYPE_END_GROUP) {
      goto success;
    }
    DO_(::google::protobuf::internal::WireFormat::SkipField(
          input, tag, mutable_unknown_fields()));
  }
success:
  // @@protoc_insertion_point(parse_success:protobuf_unittest.TestConflictingSymbolNames.StringPiece)
  return true;
failure:
  // @@protoc_insertion_point(parse_failure:protobuf_unittest.TestConflictingSymbolNames.StringPiece)
  return false;
#undef DO_
}

void TestConflictingSymbolNames_StringPiece::SerializeWithCachedSizes(
    ::google::protobuf::io::CodedOutputStream* output) const {
  // @@protoc_insertion_point(serialize_start:protobuf_unittest.TestConflictingSymbolNames.StringPiece)
  if (_internal_metadata_.have_unknown_fields()) {
    ::google::protobuf::internal::WireFormat::SerializeUnknownFields(
        unknown_fields(), output);
  }
  // @@protoc_insertion_point(serialize_end:protobuf_unittest.TestConflictingSymbolNames.StringPiece)
}

::google::protobuf::uint8* TestConflictingSymbolNames_StringPiece::InternalSerializeWithCachedSizesToArray(
    bool deterministic, ::google::protobuf::uint8* target) const {
  (void)deterministic; // Unused
  // @@protoc_insertion_point(serialize_to_array_start:protobuf_unittest.TestConflictingSymbolNames.StringPiece)
  if (_internal_metadata_.have_unknown_fields()) {
    target = ::google::protobuf::internal::WireFormat::SerializeUnknownFieldsToArray(
        unknown_fields(), target);
  }
  // @@protoc_insertion_point(serialize_to_array_end:protobuf_unittest.TestConflictingSymbolNames.StringPiece)
  return target;
}

size_t TestConflictingSymbolNames_StringPiece::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:protobuf_unittest.TestConflictingSymbolNames.StringPiece)
  size_t total_size = 0;

  if (_internal_metadata_.have_unknown_fields()) {
    total_size +=
      ::google::protobuf::internal::WireFormat::ComputeUnknownFieldsSize(
        unknown_fields());
  }
  int cached_size = ::google::protobuf::internal::ToCachedSize(total_size);
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = cached_size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
  return total_size;
}

void TestConflictingSymbolNames_StringPiece::MergeFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_merge_from_start:protobuf_unittest.TestConflictingSymbolNames.StringPiece)
  if (GOOGLE_PREDICT_FALSE(&from == this)) MergeFromFail(__LINE__);
  const TestConflictingSymbolNames_StringPiece* source =
      ::google::protobuf::internal::DynamicCastToGenerated<const TestConflictingSymbolNames_StringPiece>(
          &from);
  if (source == NULL) {
  // @@protoc_insertion_point(generalized_merge_from_cast_fail:protobuf_unittest.TestConflictingSymbolNames.StringPiece)
    ::google::protobuf::internal::ReflectionOps::Merge(from, this);
  } else {
  // @@protoc_insertion_point(generalized_merge_from_cast_success:protobuf_unittest.TestConflictingSymbolNames.StringPiece)
    UnsafeMergeFrom(*source);
  }
}

void TestConflictingSymbolNames_StringPiece::MergeFrom(const TestConflictingSymbolNames_StringPiece& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:protobuf_unittest.TestConflictingSymbolNames.StringPiece)
  if (GOOGLE_PREDICT_TRUE(&from != this)) {
    UnsafeMergeFrom(from);
  } else {
    MergeFromFail(__LINE__);
  }
}

void TestConflictingSymbolNames_StringPiece::UnsafeMergeFrom(const TestConflictingSymbolNames_StringPiece& from) {
  GOOGLE_DCHECK(&from != this);
  if (from._internal_metadata_.have_unknown_fields()) {
    ::google::protobuf::UnknownFieldSet::MergeToInternalMetdata(
      from.unknown_fields(), &_internal_metadata_);
  }
}

void TestConflictingSymbolNames_StringPiece::CopyFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_copy_from_start:protobuf_unittest.TestConflictingSymbolNames.StringPiece)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

void TestConflictingSymbolNames_StringPiece::CopyFrom(const TestConflictingSymbolNames_StringPiece& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:protobuf_unittest.TestConflictingSymbolNames.StringPiece)
  if (&from == this) return;
  Clear();
  UnsafeMergeFrom(from);
}

bool TestConflictingSymbolNames_StringPiece::IsInitialized() const {

  return true;
}

void TestConflictingSymbolNames_StringPiece::Swap(TestConflictingSymbolNames_StringPiece* other) {
  if (other == this) return;
  InternalSwap(other);
}
void TestConflictingSymbolNames_StringPiece::InternalSwap(TestConflictingSymbolNames_StringPiece* other) {
  _internal_metadata_.Swap(&other->_internal_metadata_);
  std::swap(_cached_size_, other->_cached_size_);
}

::google::protobuf::Metadata TestConflictingSymbolNames_StringPiece::GetMetadata() const {
  protobuf_AssignDescriptorsOnce();
  ::google::protobuf::Metadata metadata;
  metadata.descriptor = TestConflictingSymbolNames_StringPiece_descriptor_;
  metadata.reflection = TestConflictingSymbolNames_StringPiece_reflection_;
  return metadata;
}


// -------------------------------------------------------------------

#if !defined(_MSC_VER) || _MSC_VER >= 1900
#endif  // !defined(_MSC_VER) || _MSC_VER >= 1900

TestConflictingSymbolNames_DO::TestConflictingSymbolNames_DO()
  : ::google::protobuf::Message(), _internal_metadata_(NULL) {
  if (this != internal_default_instance()) protobuf_InitDefaults_google_2fprotobuf_2fcompiler_2fcpp_2fcpp_5ftest_5fbad_5fidentifiers_2eproto();
  SharedCtor();
  // @@protoc_insertion_point(constructor:protobuf_unittest.TestConflictingSymbolNames.DO)
}

void TestConflictingSymbolNames_DO::InitAsDefaultInstance() {
}

TestConflictingSymbolNames_DO::TestConflictingSymbolNames_DO(const TestConflictingSymbolNames_DO& from)
  : ::google::protobuf::Message(),
    _internal_metadata_(NULL) {
  SharedCtor();
  UnsafeMergeFrom(from);
  // @@protoc_insertion_point(copy_constructor:protobuf_unittest.TestConflictingSymbolNames.DO)
}

void TestConflictingSymbolNames_DO::SharedCtor() {
  _cached_size_ = 0;
}

TestConflictingSymbolNames_DO::~TestConflictingSymbolNames_DO() {
  // @@protoc_insertion_point(destructor:protobuf_unittest.TestConflictingSymbolNames.DO)
  SharedDtor();
}

void TestConflictingSymbolNames_DO::SharedDtor() {
}

void TestConflictingSymbolNames_DO::SetCachedSize(int size) const {
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
}
const ::google::protobuf::Descriptor* TestConflictingSymbolNames_DO::descriptor() {
  protobuf_AssignDescriptorsOnce();
  return TestConflictingSymbolNames_DO_descriptor_;
}

const TestConflictingSymbolNames_DO& TestConflictingSymbolNames_DO::default_instance() {
  protobuf_InitDefaults_google_2fprotobuf_2fcompiler_2fcpp_2fcpp_5ftest_5fbad_5fidentifiers_2eproto();
  return *internal_default_instance();
}

::google::protobuf::internal::ExplicitlyConstructed<TestConflictingSymbolNames_DO> TestConflictingSymbolNames_DO_default_instance_;

TestConflictingSymbolNames_DO* TestConflictingSymbolNames_DO::New(::google::protobuf::Arena* arena) const {
  TestConflictingSymbolNames_DO* n = new TestConflictingSymbolNames_DO;
  if (arena != NULL) {
    arena->Own(n);
  }
  return n;
}

void TestConflictingSymbolNames_DO::Clear() {
// @@protoc_insertion_point(message_clear_start:protobuf_unittest.TestConflictingSymbolNames.DO)
  _has_bits_.Clear();
  if (_internal_metadata_.have_unknown_fields()) {
    mutable_unknown_fields()->Clear();
  }
}

bool TestConflictingSymbolNames_DO::MergePartialFromCodedStream(
    ::google::protobuf::io::CodedInputStream* input) {
#define DO_(EXPRESSION) if (!GOOGLE_PREDICT_TRUE(EXPRESSION)) goto failure
  ::google::protobuf::uint32 tag;
  // @@protoc_insertion_point(parse_start:protobuf_unittest.TestConflictingSymbolNames.DO)
  for (;;) {
    ::std::pair< ::google::protobuf::uint32, bool> p = input->ReadTagWithCutoff(127);
    tag = p.first;
    if (!p.second) goto handle_unusual;
  handle_unusual:
    if (tag == 0 ||
        ::google::protobuf::internal::WireFormatLite::GetTagWireType(tag) ==
        ::google::protobuf::internal::WireFormatLite::WIRETYPE_END_GROUP) {
      goto success;
    }
    DO_(::google::protobuf::internal::WireFormat::SkipField(
          input, tag, mutable_unknown_fields()));
  }
success:
  // @@protoc_insertion_point(parse_success:protobuf_unittest.TestConflictingSymbolNames.DO)
  return true;
failure:
  // @@protoc_insertion_point(parse_failure:protobuf_unittest.TestConflictingSymbolNames.DO)
  return false;
#undef DO_
}

void TestConflictingSymbolNames_DO::SerializeWithCachedSizes(
    ::google::protobuf::io::CodedOutputStream* output) const {
  // @@protoc_insertion_point(serialize_start:protobuf_unittest.TestConflictingSymbolNames.DO)
  if (_internal_metadata_.have_unknown_fields()) {
    ::google::protobuf::internal::WireFormat::SerializeUnknownFields(
        unknown_fields(), output);
  }
  // @@protoc_insertion_point(serialize_end:protobuf_unittest.TestConflictingSymbolNames.DO)
}

::google::protobuf::uint8* TestConflictingSymbolNames_DO::InternalSerializeWithCachedSizesToArray(
    bool deterministic, ::google::protobuf::uint8* target) const {
  (void)deterministic; // Unused
  // @@protoc_insertion_point(serialize_to_array_start:protobuf_unittest.TestConflictingSymbolNames.DO)
  if (_internal_metadata_.have_unknown_fields()) {
    target = ::google::protobuf::internal::WireFormat::SerializeUnknownFieldsToArray(
        unknown_fields(), target);
  }
  // @@protoc_insertion_point(serialize_to_array_end:protobuf_unittest.TestConflictingSymbolNames.DO)
  return target;
}

size_t TestConflictingSymbolNames_DO::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:protobuf_unittest.TestConflictingSymbolNames.DO)
  size_t total_size = 0;

  if (_internal_metadata_.have_unknown_fields()) {
    total_size +=
      ::google::protobuf::internal::WireFormat::ComputeUnknownFieldsSize(
        unknown_fields());
  }
  int cached_size = ::google::protobuf::internal::ToCachedSize(total_size);
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = cached_size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
  return total_size;
}

void TestConflictingSymbolNames_DO::MergeFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_merge_from_start:protobuf_unittest.TestConflictingSymbolNames.DO)
  if (GOOGLE_PREDICT_FALSE(&from == this)) MergeFromFail(__LINE__);
  const TestConflictingSymbolNames_DO* source =
      ::google::protobuf::internal::DynamicCastToGenerated<const TestConflictingSymbolNames_DO>(
          &from);
  if (source == NULL) {
  // @@protoc_insertion_point(generalized_merge_from_cast_fail:protobuf_unittest.TestConflictingSymbolNames.DO)
    ::google::protobuf::internal::ReflectionOps::Merge(from, this);
  } else {
  // @@protoc_insertion_point(generalized_merge_from_cast_success:protobuf_unittest.TestConflictingSymbolNames.DO)
    UnsafeMergeFrom(*source);
  }
}

void TestConflictingSymbolNames_DO::MergeFrom(const TestConflictingSymbolNames_DO& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:protobuf_unittest.TestConflictingSymbolNames.DO)
  if (GOOGLE_PREDICT_TRUE(&from != this)) {
    UnsafeMergeFrom(from);
  } else {
    MergeFromFail(__LINE__);
  }
}

void TestConflictingSymbolNames_DO::UnsafeMergeFrom(const TestConflictingSymbolNames_DO& from) {
  GOOGLE_DCHECK(&from != this);
  if (from._internal_metadata_.have_unknown_fields()) {
    ::google::protobuf::UnknownFieldSet::MergeToInternalMetdata(
      from.unknown_fields(), &_internal_metadata_);
  }
}

void TestConflictingSymbolNames_DO::CopyFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_copy_from_start:protobuf_unittest.TestConflictingSymbolNames.DO)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

void TestConflictingSymbolNames_DO::CopyFrom(const TestConflictingSymbolNames_DO& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:protobuf_unittest.TestConflictingSymbolNames.DO)
  if (&from == this) return;
  Clear();
  UnsafeMergeFrom(from);
}

bool TestConflictingSymbolNames_DO::IsInitialized() const {

  return true;
}

void TestConflictingSymbolNames_DO::Swap(TestConflictingSymbolNames_DO* other) {
  if (other == this) return;
  InternalSwap(other);
}
void TestConflictingSymbolNames_DO::InternalSwap(TestConflictingSymbolNames_DO* other) {
  _internal_metadata_.Swap(&other->_internal_metadata_);
  std::swap(_cached_size_, other->_cached_size_);
}

::google::protobuf::Metadata TestConflictingSymbolNames_DO::GetMetadata() const {
  protobuf_AssignDescriptorsOnce();
  ::google::protobuf::Metadata metadata;
  metadata.descriptor = TestConflictingSymbolNames_DO_descriptor_;
  metadata.reflection = TestConflictingSymbolNames_DO_reflection_;
  return metadata;
}


// -------------------------------------------------------------------

#if !defined(_MSC_VER) || _MSC_VER >= 1900
const int TestConflictingSymbolNames::kInputFieldNumber;
const int TestConflictingSymbolNames::kOutputFieldNumber;
const int TestConflictingSymbolNames::kLengthFieldNumber;
const int TestConflictingSymbolNames::kIFieldNumber;
const int TestConflictingSymbolNames::kNewElementFieldNumber;
const int TestConflictingSymbolNames::kTotalSizeFieldNumber;
const int TestConflictingSymbolNames::kTagFieldNumber;
const int TestConflictingSymbolNames::kSourceFieldNumber;
const int TestConflictingSymbolNames::kValueFieldNumber;
const int TestConflictingSymbolNames::kFileFieldNumber;
const int TestConflictingSymbolNames::kFromFieldNumber;
const int TestConflictingSymbolNames::kHandleUninterpretedFieldNumber;
const int TestConflictingSymbolNames::kIndexFieldNumber;
const int TestConflictingSymbolNames::kControllerFieldNumber;
const int TestConflictingSymbolNames::kAlreadyHereFieldNumber;
const int TestConflictingSymbolNames::kUint32FieldNumber;
const int TestConflictingSymbolNames::kUint64FieldNumber;
const int TestConflictingSymbolNames::kStringFieldNumber;
const int TestConflictingSymbolNames::kMemsetFieldNumber;
const int TestConflictingSymbolNames::kInt32FieldNumber;
const int TestConflictingSymbolNames::kInt64FieldNumber;
const int TestConflictingSymbolNames::kCachedSizeFieldNumber;
const int TestConflictingSymbolNames::kExtensionsFieldNumber;
const int TestConflictingSymbolNames::kBitFieldNumber;
const int TestConflictingSymbolNames::kBitsFieldNumber;
const int TestConflictingSymbolNames::kOffsetsFieldNumber;
const int TestConflictingSymbolNames::kReflectionFieldNumber;
const int TestConflictingSymbolNames::kSomeCordFieldNumber;
const int TestConflictingSymbolNames::kSomeStringPieceFieldNumber;
const int TestConflictingSymbolNames::kIntFieldNumber;
const int TestConflictingSymbolNames::kFriendFieldNumber;
const int TestConflictingSymbolNames::kClassFieldNumber;
const int TestConflictingSymbolNames::kTypedeclFieldNumber;
const int TestConflictingSymbolNames::kAutoFieldNumber;
const int TestConflictingSymbolNames::kDoFieldNumber;
const int TestConflictingSymbolNames::kFieldTypeFieldNumber;
const int TestConflictingSymbolNames::kIsPackedFieldNumber;
const int TestConflictingSymbolNames::kReleaseLengthFieldNumber;
const int TestConflictingSymbolNames::kReleaseDoFieldNumber;
const int TestConflictingSymbolNames::kTargetFieldNumber;
#endif  // !defined(_MSC_VER) || _MSC_VER >= 1900

TestConflictingSymbolNames::TestConflictingSymbolNames()
  : ::google::protobuf::Message(), _internal_metadata_(NULL) {
  if (this != internal_default_instance()) protobuf_InitDefaults_google_2fprotobuf_2fcompiler_2fcpp_2fcpp_5ftest_5fbad_5fidentifiers_2eproto();
  SharedCtor();
  // @@protoc_insertion_point(constructor:protobuf_unittest.TestConflictingSymbolNames)
}

void TestConflictingSymbolNames::InitAsDefaultInstance() {
  do__ = const_cast< ::protobuf_unittest::TestConflictingSymbolNames_DO*>(
      ::protobuf_unittest::TestConflictingSymbolNames_DO::internal_default_instance());
  release_do_ = const_cast< ::protobuf_unittest::TestConflictingSymbolNames_DO*>(
      ::protobuf_unittest::TestConflictingSymbolNames_DO::internal_default_instance());
}

TestConflictingSymbolNames::TestConflictingSymbolNames(const TestConflictingSymbolNames& from)
  : ::google::protobuf::Message(),
    _internal_metadata_(NULL) {
  SharedCtor();
  UnsafeMergeFrom(from);
  // @@protoc_insertion_point(copy_constructor:protobuf_unittest.TestConflictingSymbolNames)
}

void TestConflictingSymbolNames::SharedCtor() {
  length_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  string_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  some_cord_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  some_string_piece_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  release_length_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  target_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  do__ = NULL;
  release_do_ = NULL;
  ::memset(&input_, 0, reinterpret_cast<char*>(&is_packed_) -
    reinterpret_cast<char*>(&input_) + sizeof(is_packed_));
  _cached_size_ = 0;
}

TestConflictingSymbolNames::~TestConflictingSymbolNames() {
  // @@protoc_insertion_point(destructor:protobuf_unittest.TestConflictingSymbolNames)
  SharedDtor();
}

void TestConflictingSymbolNames::SharedDtor() {
  length_.DestroyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  string_.DestroyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  some_cord_.DestroyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  some_string_piece_.DestroyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  release_length_.DestroyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  target_.DestroyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  if (this != &TestConflictingSymbolNames_default_instance_.get()) {
    delete do__;
    delete release_do_;
  }
}

void TestConflictingSymbolNames::SetCachedSize(int size) const {
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
}
const ::google::protobuf::Descriptor* TestConflictingSymbolNames::descriptor() {
  protobuf_AssignDescriptorsOnce();
  return TestConflictingSymbolNames_descriptor_;
}

const TestConflictingSymbolNames& TestConflictingSymbolNames::default_instance() {
  protobuf_InitDefaults_google_2fprotobuf_2fcompiler_2fcpp_2fcpp_5ftest_5fbad_5fidentifiers_2eproto();
  return *internal_default_instance();
}

::google::protobuf::internal::ExplicitlyConstructed<TestConflictingSymbolNames> TestConflictingSymbolNames_default_instance_;

TestConflictingSymbolNames* TestConflictingSymbolNames::New(::google::protobuf::Arena* arena) const {
  TestConflictingSymbolNames* n = new TestConflictingSymbolNames;
  if (arena != NULL) {
    arena->Own(n);
  }
  return n;
}

void TestConflictingSymbolNames::Clear() {
// @@protoc_insertion_point(message_clear_start:protobuf_unittest.TestConflictingSymbolNames)
  _extensions_.Clear();
#if defined(__clang__)
#define ZR_HELPER_(f) \
  _Pragma("clang diagnostic push") \
  _Pragma("clang diagnostic ignored \"-Winvalid-offsetof\"") \
  __builtin_offsetof(TestConflictingSymbolNames, f) \
  _Pragma("clang diagnostic pop")
#else
#define ZR_HELPER_(f) reinterpret_cast<char*>(\
  &reinterpret_cast<TestConflictingSymbolNames*>(16)->f)
#endif

#define ZR_(first, last) do {\
  ::memset(&(first), 0,\
           ZR_HELPER_(last) - ZR_HELPER_(first) + sizeof(last));\
} while (0)

  if (_has_bits_[0 / 32] & 231u) {
    ZR_(input_, source_);
    if (has_length()) {
      length_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
    }
  }
  ZR_(value_, uint32_);
  if (_has_bits_[16 / 32] & 16711680u) {
    ZR_(uint64_, bit_);
    if (has_string()) {
      string_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
    }
  }
  if (_has_bits_[24 / 32] & 4278190080u) {
    ZR_(bits_, class__);
    if (has_some_cord()) {
      some_cord_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
    }
    if (has_some_string_piece()) {
      some_string_piece_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
    }
  }
  if (_has_bits_[32 / 32] & 255u) {
    ZR_(typedecl_, is_packed_);
    if (has_do_()) {
      if (do__ != NULL) do__->::protobuf_unittest::TestConflictingSymbolNames_DO::Clear();
    }
    if (has_release_length()) {
      release_length_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
    }
    if (has_release_do()) {
      if (release_do_ != NULL) release_do_->::protobuf_unittest::TestConflictingSymbolNames_DO::Clear();
    }
    if (has_target()) {
      target_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
    }
  }

#undef ZR_HELPER_
#undef ZR_

  i_.Clear();
  new_element_.Clear();
  index_.Clear();
  _has_bits_.Clear();
  if (_internal_metadata_.have_unknown_fields()) {
    mutable_unknown_fields()->Clear();
  }
}

bool TestConflictingSymbolNames::MergePartialFromCodedStream(
    ::google::protobuf::io::CodedInputStream* input) {
#define DO_(EXPRESSION) if (!GOOGLE_PREDICT_TRUE(EXPRESSION)) goto failure
  ::google::protobuf::uint32 tag;
  // @@protoc_insertion_point(parse_start:protobuf_unittest.TestConflictingSymbolNames)
  for (;;) {
    ::std::pair< ::google::protobuf::uint32, bool> p = input->ReadTagWithCutoff(16383);
    tag = p.first;
    if (!p.second) goto handle_unusual;
    switch (::google::protobuf::internal::WireFormatLite::GetTagFieldNumber(tag)) {
      // optional int32 input = 1;
      case 1: {
        if (tag == 8) {
          set_has_input();
          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int32, ::google::protobuf::internal::WireFormatLite::TYPE_INT32>(
                 input, &input_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(16)) goto parse_output;
        break;
      }

      // optional int32 output = 2;
      case 2: {
        if (tag == 16) {
         parse_output:
          set_has_output();
          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int32, ::google::protobuf::internal::WireFormatLite::TYPE_INT32>(
                 input, &output_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(26)) goto parse_length;
        break;
      }

      // optional string length = 3;
      case 3: {
        if (tag == 26) {
         parse_length:
          DO_(::google::protobuf::internal::WireFormatLite::ReadString(
                input, this->mutable_length()));
          ::google::protobuf::internal::WireFormat::VerifyUTF8StringNamedField(
            this->length().data(), this->length().length(),
            ::google::protobuf::internal::WireFormat::PARSE,
            "protobuf_unittest.TestConflictingSymbolNames.length");
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(32)) goto parse_i;
        break;
      }

      // repeated int32 i = 4;
      case 4: {
        if (tag == 32) {
         parse_i:
          DO_((::google::protobuf::internal::WireFormatLite::ReadRepeatedPrimitive<
                   ::google::protobuf::int32, ::google::protobuf::internal::WireFormatLite::TYPE_INT32>(
                 1, 32, input, this->mutable_i())));
        } else if (tag == 34) {
          DO_((::google::protobuf::internal::WireFormatLite::ReadPackedPrimitiveNoInline<
                   ::google::protobuf::int32, ::google::protobuf::internal::WireFormatLite::TYPE_INT32>(
                 input, this->mutable_i())));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(32)) goto parse_i;
        if (input->ExpectTag(42)) goto parse_new_element;
        break;
      }

      // repeated string new_element = 5 [ctype = STRING_PIECE];
      case 5: {
        if (tag == 42) {
         parse_new_element:
          DO_(::google::protobuf::internal::WireFormatLite::ReadString(
                input, this->add_new_element()));
          ::google::protobuf::internal::WireFormat::VerifyUTF8StringNamedField(
            this->new_element(this->new_element_size() - 1).data(),
            this->new_element(this->new_element_size() - 1).length(),
            ::google::protobuf::internal::WireFormat::PARSE,
            "protobuf_unittest.TestConflictingSymbolNames.new_element");
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(42)) goto parse_new_element;
        if (input->ExpectTag(48)) goto parse_total_size;
        break;
      }

      // optional int32 total_size = 6;
      case 6: {
        if (tag == 48) {
         parse_total_size:
          set_has_total_size();
          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int32, ::google::protobuf::internal::WireFormatLite::TYPE_INT32>(
                 input, &total_size_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(56)) goto parse_tag;
        break;
      }

      // optional int32 tag = 7;
      case 7: {
        if (tag == 56) {
         parse_tag:
          set_has_tag();
          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int32, ::google::protobuf::internal::WireFormatLite::TYPE_INT32>(
                 input, &tag_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(64)) goto parse_source;
        break;
      }

      // optional int32 source = 8;
      case 8: {
        if (tag == 64) {
         parse_source:
          set_has_source();
          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int32, ::google::protobuf::internal::WireFormatLite::TYPE_INT32>(
                 input, &source_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(72)) goto parse_value;
        break;
      }

      // optional int32 value = 9;
      case 9: {
        if (tag == 72) {
         parse_value:
          set_has_value();
          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int32, ::google::protobuf::internal::WireFormatLite::TYPE_INT32>(
                 input, &value_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(80)) goto parse_file;
        break;
      }

      // optional int32 file = 10;
      case 10: {
        if (tag == 80) {
         parse_file:
          set_has_file();
          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int32, ::google::protobuf::internal::WireFormatLite::TYPE_INT32>(
                 input, &file_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(88)) goto parse_from;
        break;
      }

      // optional int32 from = 11;
      case 11: {
        if (tag == 88) {
         parse_from:
          set_has_from();
          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int32, ::google::protobuf::internal::WireFormatLite::TYPE_INT32>(
                 input, &from_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(96)) goto parse_handle_uninterpreted;
        break;
      }

      // optional int32 handle_uninterpreted = 12;
      case 12: {
        if (tag == 96) {
         parse_handle_uninterpreted:
          set_has_handle_uninterpreted();
          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int32, ::google::protobuf::internal::WireFormatLite::TYPE_INT32>(
                 input, &handle_uninterpreted_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(104)) goto parse_index;
        break;
      }

      // repeated int32 index = 13;
      case 13: {
        if (tag == 104) {
         parse_index:
          DO_((::google::protobuf::internal::WireFormatLite::ReadRepeatedPrimitive<
                   ::google::protobuf::int32, ::google::protobuf::internal::WireFormatLite::TYPE_INT32>(
                 1, 104, input, this->mutable_index())));
        } else if (tag == 106) {
          DO_((::google::protobuf::internal::WireFormatLite::ReadPackedPrimitiveNoInline<
                   ::google::protobuf::int32, ::google::protobuf::internal::WireFormatLite::TYPE_INT32>(
                 input, this->mutable_index())));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(104)) goto parse_index;
        if (input->ExpectTag(112)) goto parse_controller;
        break;
      }

      // optional int32 controller = 14;
      case 14: {
        if (tag == 112) {
         parse_controller:
          set_has_controller();
          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int32, ::google::protobuf::internal::WireFormatLite::TYPE_INT32>(
                 input, &controller_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(120)) goto parse_already_here;
        break;
      }

      // optional int32 already_here = 15;
      case 15: {
        if (tag == 120) {
         parse_already_here:
          set_has_already_here();
          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int32, ::google::protobuf::internal::WireFormatLite::TYPE_INT32>(
                 input, &already_here_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(128)) goto parse_uint32;
        break;
      }

      // optional uint32 uint32 = 16;
      case 16: {
        if (tag == 128) {
         parse_uint32:
          set_has_uint32();
          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::uint32, ::google::protobuf::internal::WireFormatLite::TYPE_UINT32>(
                 input, &uint32_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(136)) goto parse_uint64;
        break;
      }

      // optional uint64 uint64 = 17;
      case 17: {
        if (tag == 136) {
         parse_uint64:
          set_has_uint64();
          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::uint64, ::google::protobuf::internal::WireFormatLite::TYPE_UINT64>(
                 input, &uint64_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(146)) goto parse_string;
        break;
      }

      // optional string string = 18;
      case 18: {
        if (tag == 146) {
         parse_string:
          DO_(::google::protobuf::internal::WireFormatLite::ReadString(
                input, this->mutable_string()));
          ::google::protobuf::internal::WireFormat::VerifyUTF8StringNamedField(
            this->string().data(), this->string().length(),
            ::google::protobuf::internal::WireFormat::PARSE,
            "protobuf_unittest.TestConflictingSymbolNames.string");
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(152)) goto parse_memset;
        break;
      }

      // optional int32 memset = 19;
      case 19: {
        if (tag == 152) {
         parse_memset:
          set_has_memset();
          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int32, ::google::protobuf::internal::WireFormatLite::TYPE_INT32>(
                 input, &memset_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(160)) goto parse_int32;
        break;
      }

      // optional int32 int32 = 20;
      case 20: {
        if (tag == 160) {
         parse_int32:
          set_has_int32();
          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int32, ::google::protobuf::internal::WireFormatLite::TYPE_INT32>(
                 input, &int32_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(168)) goto parse_int64;
        break;
      }

      // optional int64 int64 = 21;
      case 21: {
        if (tag == 168) {
         parse_int64:
          set_has_int64();
          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_INT64>(
                 input, &int64_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(176)) goto parse_cached_size;
        break;
      }

      // optional uint32 cached_size = 22;
      case 22: {
        if (tag == 176) {
         parse_cached_size:
          set_has_cached_size();
          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::uint32, ::google::protobuf::internal::WireFormatLite::TYPE_UINT32>(
                 input, &cached_size_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(184)) goto parse_extensions;
        break;
      }

      // optional uint32 extensions = 23;
      case 23: {
        if (tag == 184) {
         parse_extensions:
          set_has_extensions();
          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::uint32, ::google::protobuf::internal::WireFormatLite::TYPE_UINT32>(
                 input, &extensions_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(192)) goto parse_bit;
        break;
      }

      // optional uint32 bit = 24;
      case 24: {
        if (tag == 192) {
         parse_bit:
          set_has_bit();
          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::uint32, ::google::protobuf::internal::WireFormatLite::TYPE_UINT32>(
                 input, &bit_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(200)) goto parse_bits;
        break;
      }

      // optional uint32 bits = 25;
      case 25: {
        if (tag == 200) {
         parse_bits:
          set_has_bits();
          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::uint32, ::google::protobuf::internal::WireFormatLite::TYPE_UINT32>(
                 input, &bits_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(208)) goto parse_offsets;
        break;
      }

      // optional uint32 offsets = 26;
      case 26: {
        if (tag == 208) {
         parse_offsets:
          set_has_offsets();
          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::uint32, ::google::protobuf::internal::WireFormatLite::TYPE_UINT32>(
                 input, &offsets_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(216)) goto parse_reflection;
        break;
      }

      // optional uint32 reflection = 27;
      case 27: {
        if (tag == 216) {
         parse_reflection:
          set_has_reflection();
          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::uint32, ::google::protobuf::internal::WireFormatLite::TYPE_UINT32>(
                 input, &reflection_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(226)) goto parse_some_cord;
        break;
      }

      // optional string some_cord = 28 [ctype = CORD];
      case 28: {
        if (tag == 226) {
         parse_some_cord:
          DO_(::google::protobuf::internal::WireFormatLite::ReadString(
                input, this->mutable_some_cord()));
          ::google::protobuf::internal::WireFormat::VerifyUTF8StringNamedField(
            this->some_cord().data(), this->some_cord().length(),
            ::google::protobuf::internal::WireFormat::PARSE,
            "protobuf_unittest.TestConflictingSymbolNames.some_cord");
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(234)) goto parse_some_string_piece;
        break;
      }

      // optional string some_string_piece = 29 [ctype = STRING_PIECE];
      case 29: {
        if (tag == 234) {
         parse_some_string_piece:
          DO_(::google::protobuf::internal::WireFormatLite::ReadString(
                input, this->mutable_some_string_piece()));
          ::google::protobuf::internal::WireFormat::VerifyUTF8StringNamedField(
            this->some_string_piece().data(), this->some_string_piece().length(),
            ::google::protobuf::internal::WireFormat::PARSE,
            "protobuf_unittest.TestConflictingSymbolNames.some_string_piece");
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(240)) goto parse_int;
        break;
      }

      // optional uint32 int = 30;
      case 30: {
        if (tag == 240) {
         parse_int:
          set_has_int_();
          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::uint32, ::google::protobuf::internal::WireFormatLite::TYPE_UINT32>(
                 input, &int__)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(248)) goto parse_friend;
        break;
      }

      // optional uint32 friend = 31;
      case 31: {
        if (tag == 248) {
         parse_friend:
          set_has_friend_();
          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::uint32, ::google::protobuf::internal::WireFormatLite::TYPE_UINT32>(
                 input, &friend__)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(258)) goto parse_do;
        break;
      }

      // optional .protobuf_unittest.TestConflictingSymbolNames.DO do = 32;
      case 32: {
        if (tag == 258) {
         parse_do:
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessageNoVirtual(
               input, mutable_do_()));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(264)) goto parse_field_type;
        break;
      }

      // optional int32 field_type = 33;
      case 33: {
        if (tag == 264) {
         parse_field_type:
          set_has_field_type();
          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int32, ::google::protobuf::internal::WireFormatLite::TYPE_INT32>(
                 input, &field_type_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(272)) goto parse_is_packed;
        break;
      }

      // optional bool is_packed = 34;
      case 34: {
        if (tag == 272) {
         parse_is_packed:
          set_has_is_packed();
          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   bool, ::google::protobuf::internal::WireFormatLite::TYPE_BOOL>(
                 input, &is_packed_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(282)) goto parse_release_length;
        break;
      }

      // optional string release_length = 35;
      case 35: {
        if (tag == 282) {
         parse_release_length:
          DO_(::google::protobuf::internal::WireFormatLite::ReadString(
                input, this->mutable_release_length()));
          ::google::protobuf::internal::WireFormat::VerifyUTF8StringNamedField(
            this->release_length().data(), this->release_length().length(),
            ::google::protobuf::internal::WireFormat::PARSE,
            "protobuf_unittest.TestConflictingSymbolNames.release_length");
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(290)) goto parse_release_do;
        break;
      }

      // optional .protobuf_unittest.TestConflictingSymbolNames.DO release_do = 36;
      case 36: {
        if (tag == 290) {
         parse_release_do:
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessageNoVirtual(
               input, mutable_release_do()));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(296)) goto parse_class;
        break;
      }

      // optional uint32 class = 37;
      case 37: {
        if (tag == 296) {
         parse_class:
          set_has_class_();
          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::uint32, ::google::protobuf::internal::WireFormatLite::TYPE_UINT32>(
                 input, &class__)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(306)) goto parse_target;
        break;
      }

      // optional string target = 38;
      case 38: {
        if (tag == 306) {
         parse_target:
          DO_(::google::protobuf::internal::WireFormatLite::ReadString(
                input, this->mutable_target()));
          ::google::protobuf::internal::WireFormat::VerifyUTF8StringNamedField(
            this->target().data(), this->target().length(),
            ::google::protobuf::internal::WireFormat::PARSE,
            "protobuf_unittest.TestConflictingSymbolNames.target");
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(312)) goto parse_typedecl;
        break;
      }

      // optional uint32 typedecl = 39;
      case 39: {
        if (tag == 312) {
         parse_typedecl:
          set_has_typedecl();
          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::uint32, ::google::protobuf::internal::WireFormatLite::TYPE_UINT32>(
                 input, &typedecl_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(320)) goto parse_auto;
        break;
      }

      // optional uint32 auto = 40;
      case 40: {
        if (tag == 320) {
         parse_auto:
          set_has_auto_();
          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::uint32, ::google::protobuf::internal::WireFormatLite::TYPE_UINT32>(
                 input, &auto__)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectAtEnd()) goto success;
        break;
      }

      default: {
      handle_unusual:
        if (tag == 0 ||
            ::google::protobuf::internal::WireFormatLite::GetTagWireType(tag) ==
            ::google::protobuf::internal::WireFormatLite::WIRETYPE_END_GROUP) {
          goto success;
        }
        if ((8000u <= tag)) {
          DO_(_extensions_.ParseField(tag, input, internal_default_instance(),
                                      mutable_unknown_fields()));
          continue;
        }
        DO_(::google::protobuf::internal::WireFormat::SkipField(
              input, tag, mutable_unknown_fields()));
        break;
      }
    }
  }
success:
  // @@protoc_insertion_point(parse_success:protobuf_unittest.TestConflictingSymbolNames)
  return true;
failure:
  // @@protoc_insertion_point(parse_failure:protobuf_unittest.TestConflictingSymbolNames)
  return false;
#undef DO_
}

void TestConflictingSymbolNames::SerializeWithCachedSizes(
    ::google::protobuf::io::CodedOutputStream* output) const {
  // @@protoc_insertion_point(serialize_start:protobuf_unittest.TestConflictingSymbolNames)
  // optional int32 input = 1;
  if (has_input()) {
    ::google::protobuf::internal::WireFormatLite::WriteInt32(1, this->input(), output);
  }

  // optional int32 output = 2;
  if (has_output()) {
    ::google::protobuf::internal::WireFormatLite::WriteInt32(2, this->output(), output);
  }

  // optional string length = 3;
  if (has_length()) {
    ::google::protobuf::internal::WireFormat::VerifyUTF8StringNamedField(
      this->length().data(), this->length().length(),
      ::google::protobuf::internal::WireFormat::SERIALIZE,
      "protobuf_unittest.TestConflictingSymbolNames.length");
    ::google::protobuf::internal::WireFormatLite::WriteStringMaybeAliased(
      3, this->length(), output);
  }

  // repeated int32 i = 4;
  for (int i = 0; i < this->i_size(); i++) {
    ::google::protobuf::internal::WireFormatLite::WriteInt32(
      4, this->i(i), output);
  }

  // repeated string new_element = 5 [ctype = STRING_PIECE];
  for (int i = 0; i < this->new_element_size(); i++) {
    ::google::protobuf::internal::WireFormat::VerifyUTF8StringNamedField(
      this->new_element(i).data(), this->new_element(i).length(),
      ::google::protobuf::internal::WireFormat::SERIALIZE,
      "protobuf_unittest.TestConflictingSymbolNames.new_element");
    ::google::protobuf::internal::WireFormatLite::WriteString(
      5, this->new_element(i), output);
  }

  // optional int32 total_size = 6;
  if (has_total_size()) {
    ::google::protobuf::internal::WireFormatLite::WriteInt32(6, this->total_size(), output);
  }

  // optional int32 tag = 7;
  if (has_tag()) {
    ::google::protobuf::internal::WireFormatLite::WriteInt32(7, this->tag(), output);
  }

  // optional int32 source = 8;
  if (has_source()) {
    ::google::protobuf::internal::WireFormatLite::WriteInt32(8, this->source(), output);
  }

  // optional int32 value = 9;
  if (has_value()) {
    ::google::protobuf::internal::WireFormatLite::WriteInt32(9, this->value(), output);
  }

  // optional int32 file = 10;
  if (has_file()) {
    ::google::protobuf::internal::WireFormatLite::WriteInt32(10, this->file(), output);
  }

  // optional int32 from = 11;
  if (has_from()) {
    ::google::protobuf::internal::WireFormatLite::WriteInt32(11, this->from(), output);
  }

  // optional int32 handle_uninterpreted = 12;
  if (has_handle_uninterpreted()) {
    ::google::protobuf::internal::WireFormatLite::WriteInt32(12, this->handle_uninterpreted(), output);
  }

  // repeated int32 index = 13;
  for (int i = 0; i < this->index_size(); i++) {
    ::google::protobuf::internal::WireFormatLite::WriteInt32(
      13, this->index(i), output);
  }

  // optional int32 controller = 14;
  if (has_controller()) {
    ::google::protobuf::internal::WireFormatLite::WriteInt32(14, this->controller(), output);
  }

  // optional int32 already_here = 15;
  if (has_already_here()) {
    ::google::protobuf::internal::WireFormatLite::WriteInt32(15, this->already_here(), output);
  }

  // optional uint32 uint32 = 16;
  if (has_uint32()) {
    ::google::protobuf::internal::WireFormatLite::WriteUInt32(16, this->uint32(), output);
  }

  // optional uint64 uint64 = 17;
  if (has_uint64()) {
    ::google::protobuf::internal::WireFormatLite::WriteUInt64(17, this->uint64(), output);
  }

  // optional string string = 18;
  if (has_string()) {
    ::google::protobuf::internal::WireFormat::VerifyUTF8StringNamedField(
      this->string().data(), this->string().length(),
      ::google::protobuf::internal::WireFormat::SERIALIZE,
      "protobuf_unittest.TestConflictingSymbolNames.string");
    ::google::protobuf::internal::WireFormatLite::WriteStringMaybeAliased(
      18, this->string(), output);
  }

  // optional int32 memset = 19;
  if (has_memset()) {
    ::google::protobuf::internal::WireFormatLite::WriteInt32(19, this->memset(), output);
  }

  // optional int32 int32 = 20;
  if (has_int32()) {
    ::google::protobuf::internal::WireFormatLite::WriteInt32(20, this->int32(), output);
  }

  // optional int64 int64 = 21;
  if (has_int64()) {
    ::google::protobuf::internal::WireFormatLite::WriteInt64(21, this->int64(), output);
  }

  // optional uint32 cached_size = 22;
  if (has_cached_size()) {
    ::google::protobuf::internal::WireFormatLite::WriteUInt32(22, this->cached_size(), output);
  }

  // optional uint32 extensions = 23;
  if (has_extensions()) {
    ::google::protobuf::internal::WireFormatLite::WriteUInt32(23, this->extensions(), output);
  }

  // optional uint32 bit = 24;
  if (has_bit()) {
    ::google::protobuf::internal::WireFormatLite::WriteUInt32(24, this->bit(), output);
  }

  // optional uint32 bits = 25;
  if (has_bits()) {
    ::google::protobuf::internal::WireFormatLite::WriteUInt32(25, this->bits(), output);
  }

  // optional uint32 offsets = 26;
  if (has_offsets()) {
    ::google::protobuf::internal::WireFormatLite::WriteUInt32(26, this->offsets(), output);
  }

  // optional uint32 reflection = 27;
  if (has_reflection()) {
    ::google::protobuf::internal::WireFormatLite::WriteUInt32(27, this->reflection(), output);
  }

  // optional string some_cord = 28 [ctype = CORD];
  if (has_some_cord()) {
    ::google::protobuf::internal::WireFormat::VerifyUTF8StringNamedField(
      this->some_cord().data(), this->some_cord().length(),
      ::google::protobuf::internal::WireFormat::SERIALIZE,
      "protobuf_unittest.TestConflictingSymbolNames.some_cord");
    ::google::protobuf::internal::WireFormatLite::WriteStringMaybeAliased(
      28, this->some_cord(), output);
  }

  // optional string some_string_piece = 29 [ctype = STRING_PIECE];
  if (has_some_string_piece()) {
    ::google::protobuf::internal::WireFormat::VerifyUTF8StringNamedField(
      this->some_string_piece().data(), this->some_string_piece().length(),
      ::google::protobuf::internal::WireFormat::SERIALIZE,
      "protobuf_unittest.TestConflictingSymbolNames.some_string_piece");
    ::google::protobuf::internal::WireFormatLite::WriteStringMaybeAliased(
      29, this->some_string_piece(), output);
  }

  // optional uint32 int = 30;
  if (has_int_()) {
    ::google::protobuf::internal::WireFormatLite::WriteUInt32(30, this->int_(), output);
  }

  // optional uint32 friend = 31;
  if (has_friend_()) {
    ::google::protobuf::internal::WireFormatLite::WriteUInt32(31, this->friend_(), output);
  }

  // optional .protobuf_unittest.TestConflictingSymbolNames.DO do = 32;
  if (has_do_()) {
    ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
      32, *this->do__, output);
  }

  // optional int32 field_type = 33;
  if (has_field_type()) {
    ::google::protobuf::internal::WireFormatLite::WriteInt32(33, this->field_type(), output);
  }

  // optional bool is_packed = 34;
  if (has_is_packed()) {
    ::google::protobuf::internal::WireFormatLite::WriteBool(34, this->is_packed(), output);
  }

  // optional string release_length = 35;
  if (has_release_length()) {
    ::google::protobuf::internal::WireFormat::VerifyUTF8StringNamedField(
      this->release_length().data(), this->release_length().length(),
      ::google::protobuf::internal::WireFormat::SERIALIZE,
      "protobuf_unittest.TestConflictingSymbolNames.release_length");
    ::google::protobuf::internal::WireFormatLite::WriteStringMaybeAliased(
      35, this->release_length(), output);
  }

  // optional .protobuf_unittest.TestConflictingSymbolNames.DO release_do = 36;
  if (has_release_do()) {
    ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
      36, *this->release_do_, output);
  }

  // optional uint32 class = 37;
  if (has_class_()) {
    ::google::protobuf::internal::WireFormatLite::WriteUInt32(37, this->class_(), output);
  }

  // optional string target = 38;
  if (has_target()) {
    ::google::protobuf::internal::WireFormat::VerifyUTF8StringNamedField(
      this->target().data(), this->target().length(),
      ::google::protobuf::internal::WireFormat::SERIALIZE,
      "protobuf_unittest.TestConflictingSymbolNames.target");
    ::google::protobuf::internal::WireFormatLite::WriteStringMaybeAliased(
      38, this->target(), output);
  }

  // optional uint32 typedecl = 39;
  if (has_typedecl()) {
    ::google::protobuf::internal::WireFormatLite::WriteUInt32(39, this->typedecl(), output);
  }

  // optional uint32 auto = 40;
  if (has_auto_()) {
    ::google::protobuf::internal::WireFormatLite::WriteUInt32(40, this->auto_(), output);
  }

  // Extension range [1000, 536870912)
  _extensions_.SerializeWithCachedSizes(
      1000, 536870912, output);

  if (_internal_metadata_.have_unknown_fields()) {
    ::google::protobuf::internal::WireFormat::SerializeUnknownFields(
        unknown_fields(), output);
  }
  // @@protoc_insertion_point(serialize_end:protobuf_unittest.TestConflictingSymbolNames)
}

::google::protobuf::uint8* TestConflictingSymbolNames::InternalSerializeWithCachedSizesToArray(
    bool deterministic, ::google::protobuf::uint8* target) const {
  (void)deterministic; // Unused
  // @@protoc_insertion_point(serialize_to_array_start:protobuf_unittest.TestConflictingSymbolNames)
  // optional int32 input = 1;
  if (has_input()) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt32ToArray(1, this->input(), target);
  }

  // optional int32 output = 2;
  if (has_output()) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt32ToArray(2, this->output(), target);
  }

  // optional string length = 3;
  if (has_length()) {
    ::google::protobuf::internal::WireFormat::VerifyUTF8StringNamedField(
      this->length().data(), this->length().length(),
      ::google::protobuf::internal::WireFormat::SERIALIZE,
      "protobuf_unittest.TestConflictingSymbolNames.length");
    target =
      ::google::protobuf::internal::WireFormatLite::WriteStringToArray(
        3, this->length(), target);
  }

  // repeated int32 i = 4;
  for (int i = 0; i < this->i_size(); i++) {
    target = ::google::protobuf::internal::WireFormatLite::
      WriteInt32ToArray(4, this->i(i), target);
  }

  // repeated string new_element = 5 [ctype = STRING_PIECE];
  for (int i = 0; i < this->new_element_size(); i++) {
    ::google::protobuf::internal::WireFormat::VerifyUTF8StringNamedField(
      this->new_element(i).data(), this->new_element(i).length(),
      ::google::protobuf::internal::WireFormat::SERIALIZE,
      "protobuf_unittest.TestConflictingSymbolNames.new_element");
    target = ::google::protobuf::internal::WireFormatLite::
      WriteStringToArray(5, this->new_element(i), target);
  }

  // optional int32 total_size = 6;
  if (has_total_size()) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt32ToArray(6, this->total_size(), target);
  }

  // optional int32 tag = 7;
  if (has_tag()) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt32ToArray(7, this->tag(), target);
  }

  // optional int32 source = 8;
  if (has_source()) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt32ToArray(8, this->source(), target);
  }

  // optional int32 value = 9;
  if (has_value()) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt32ToArray(9, this->value(), target);
  }

  // optional int32 file = 10;
  if (has_file()) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt32ToArray(10, this->file(), target);
  }

  // optional int32 from = 11;
  if (has_from()) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt32ToArray(11, this->from(), target);
  }

  // optional int32 handle_uninterpreted = 12;
  if (has_handle_uninterpreted()) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt32ToArray(12, this->handle_uninterpreted(), target);
  }

  // repeated int32 index = 13;
  for (int i = 0; i < this->index_size(); i++) {
    target = ::google::protobuf::internal::WireFormatLite::
      WriteInt32ToArray(13, this->index(i), target);
  }

  // optional int32 controller = 14;
  if (has_controller()) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt32ToArray(14, this->controller(), target);
  }

  // optional int32 already_here = 15;
  if (has_already_here()) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt32ToArray(15, this->already_here(), target);
  }

  // optional uint32 uint32 = 16;
  if (has_uint32()) {
    target = ::google::protobuf::internal::WireFormatLite::WriteUInt32ToArray(16, this->uint32(), target);
  }

  // optional uint64 uint64 = 17;
  if (has_uint64()) {
    target = ::google::protobuf::internal::WireFormatLite::WriteUInt64ToArray(17, this->uint64(), target);
  }

  // optional string string = 18;
  if (has_string()) {
    ::google::protobuf::internal::WireFormat::VerifyUTF8StringNamedField(
      this->string().data(), this->string().length(),
      ::google::protobuf::internal::WireFormat::SERIALIZE,
      "protobuf_unittest.TestConflictingSymbolNames.string");
    target =
      ::google::protobuf::internal::WireFormatLite::WriteStringToArray(
        18, this->string(), target);
  }

  // optional int32 memset = 19;
  if (has_memset()) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt32ToArray(19, this->memset(), target);
  }

  // optional int32 int32 = 20;
  if (has_int32()) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt32ToArray(20, this->int32(), target);
  }

  // optional int64 int64 = 21;
  if (has_int64()) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt64ToArray(21, this->int64(), target);
  }

  // optional uint32 cached_size = 22;
  if (has_cached_size()) {
    target = ::google::protobuf::internal::WireFormatLite::WriteUInt32ToArray(22, this->cached_size(), target);
  }

  // optional uint32 extensions = 23;
  if (has_extensions()) {
    target = ::google::protobuf::internal::WireFormatLite::WriteUInt32ToArray(23, this->extensions(), target);
  }

  // optional uint32 bit = 24;
  if (has_bit()) {
    target = ::google::protobuf::internal::WireFormatLite::WriteUInt32ToArray(24, this->bit(), target);
  }

  // optional uint32 bits = 25;
  if (has_bits()) {
    target = ::google::protobuf::internal::WireFormatLite::WriteUInt32ToArray(25, this->bits(), target);
  }

  // optional uint32 offsets = 26;
  if (has_offsets()) {
    target = ::google::protobuf::internal::WireFormatLite::WriteUInt32ToArray(26, this->offsets(), target);
  }

  // optional uint32 reflection = 27;
  if (has_reflection()) {
    target = ::google::protobuf::internal::WireFormatLite::WriteUInt32ToArray(27, this->reflection(), target);
  }

  // optional string some_cord = 28 [ctype = CORD];
  if (has_some_cord()) {
    ::google::protobuf::internal::WireFormat::VerifyUTF8StringNamedField(
      this->some_cord().data(), this->some_cord().length(),
      ::google::protobuf::internal::WireFormat::SERIALIZE,
      "protobuf_unittest.TestConflictingSymbolNames.some_cord");
    target =
      ::google::protobuf::internal::WireFormatLite::WriteStringToArray(
        28, this->some_cord(), target);
  }

  // optional string some_string_piece = 29 [ctype = STRING_PIECE];
  if (has_some_string_piece()) {
    ::google::protobuf::internal::WireFormat::VerifyUTF8StringNamedField(
      this->some_string_piece().data(), this->some_string_piece().length(),
      ::google::protobuf::internal::WireFormat::SERIALIZE,
      "protobuf_unittest.TestConflictingSymbolNames.some_string_piece");
    target =
      ::google::protobuf::internal::WireFormatLite::WriteStringToArray(
        29, this->some_string_piece(), target);
  }

  // optional uint32 int = 30;
  if (has_int_()) {
    target = ::google::protobuf::internal::WireFormatLite::WriteUInt32ToArray(30, this->int_(), target);
  }

  // optional uint32 friend = 31;
  if (has_friend_()) {
    target = ::google::protobuf::internal::WireFormatLite::WriteUInt32ToArray(31, this->friend_(), target);
  }

  // optional .protobuf_unittest.TestConflictingSymbolNames.DO do = 32;
  if (has_do_()) {
    target = ::google::protobuf::internal::WireFormatLite::
      InternalWriteMessageNoVirtualToArray(
        32, *this->do__, false, target);
  }

  // optional int32 field_type = 33;
  if (has_field_type()) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt32ToArray(33, this->field_type(), target);
  }

  // optional bool is_packed = 34;
  if (has_is_packed()) {
    target = ::google::protobuf::internal::WireFormatLite::WriteBoolToArray(34, this->is_packed(), target);
  }

  // optional string release_length = 35;
  if (has_release_length()) {
    ::google::protobuf::internal::WireFormat::VerifyUTF8StringNamedField(
      this->release_length().data(), this->release_length().length(),
      ::google::protobuf::internal::WireFormat::SERIALIZE,
      "protobuf_unittest.TestConflictingSymbolNames.release_length");
    target =
      ::google::protobuf::internal::WireFormatLite::WriteStringToArray(
        35, this->release_length(), target);
  }

  // optional .protobuf_unittest.TestConflictingSymbolNames.DO release_do = 36;
  if (has_release_do()) {
    target = ::google::protobuf::internal::WireFormatLite::
      InternalWriteMessageNoVirtualToArray(
        36, *this->release_do_, false, target);
  }

  // optional uint32 class = 37;
  if (has_class_()) {
    target = ::google::protobuf::internal::WireFormatLite::WriteUInt32ToArray(37, this->class_(), target);
  }

  // optional string target = 38;
  if (has_target()) {
    ::google::protobuf::internal::WireFormat::VerifyUTF8StringNamedField(
      this->target().data(), this->target().length(),
      ::google::protobuf::internal::WireFormat::SERIALIZE,
      "protobuf_unittest.TestConflictingSymbolNames.target");
    target =
      ::google::protobuf::internal::WireFormatLite::WriteStringToArray(
        38, this->target(), target);
  }

  // optional uint32 typedecl = 39;
  if (has_typedecl()) {
    target = ::google::protobuf::internal::WireFormatLite::WriteUInt32ToArray(39, this->typedecl(), target);
  }

  // optional uint32 auto = 40;
  if (has_auto_()) {
    target = ::google::protobuf::internal::WireFormatLite::WriteUInt32ToArray(40, this->auto_(), target);
  }

  // Extension range [1000, 536870912)
  target = _extensions_.InternalSerializeWithCachedSizesToArray(
      1000, 536870912, false, target);

  if (_internal_metadata_.have_unknown_fields()) {
    target = ::google::protobuf::internal::WireFormat::SerializeUnknownFieldsToArray(
        unknown_fields(), target);
  }
  // @@protoc_insertion_point(serialize_to_array_end:protobuf_unittest.TestConflictingSymbolNames)
  return target;
}

size_t TestConflictingSymbolNames::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:protobuf_unittest.TestConflictingSymbolNames)
  size_t total_size = 0;

  if (_has_bits_[0 / 32] & 231u) {
    // optional int32 input = 1;
    if (has_input()) {
      total_size += 1 +
        ::google::protobuf::internal::WireFormatLite::Int32Size(
          this->input());
    }

    // optional int32 output = 2;
    if (has_output()) {
      total_size += 1 +
        ::google::protobuf::internal::WireFormatLite::Int32Size(
          this->output());
    }

    // optional string length = 3;
    if (has_length()) {
      total_size += 1 +
        ::google::protobuf::internal::WireFormatLite::StringSize(
          this->length());
    }

    // optional int32 total_size = 6;
    if (has_total_size()) {
      total_size += 1 +
        ::google::protobuf::internal::WireFormatLite::Int32Size(
          this->total_size());
    }

    // optional int32 tag = 7;
    if (has_tag()) {
      total_size += 1 +
        ::google::protobuf::internal::WireFormatLite::Int32Size(
          this->tag());
    }

    // optional int32 source = 8;
    if (has_source()) {
      total_size += 1 +
        ::google::protobuf::internal::WireFormatLite::Int32Size(
          this->source());
    }

  }
  if (_has_bits_[8 / 32] & 61184u) {
    // optional int32 value = 9;
    if (has_value()) {
      total_size += 1 +
        ::google::protobuf::internal::WireFormatLite::Int32Size(
          this->value());
    }

    // optional int32 file = 10;
    if (has_file()) {
      total_size += 1 +
        ::google::protobuf::internal::WireFormatLite::Int32Size(
          this->file());
    }

    // optional int32 from = 11;
    if (has_from()) {
      total_size += 1 +
        ::google::protobuf::internal::WireFormatLite::Int32Size(
          this->from());
    }

    // optional int32 handle_uninterpreted = 12;
    if (has_handle_uninterpreted()) {
      total_size += 1 +
        ::google::protobuf::internal::WireFormatLite::Int32Size(
          this->handle_uninterpreted());
    }

    // optional int32 controller = 14;
    if (has_controller()) {
      total_size += 1 +
        ::google::protobuf::internal::WireFormatLite::Int32Size(
          this->controller());
    }

    // optional int32 already_here = 15;
    if (has_already_here()) {
      total_size += 1 +
        ::google::protobuf::internal::WireFormatLite::Int32Size(
          this->already_here());
    }

    // optional uint32 uint32 = 16;
    if (has_uint32()) {
      total_size += 2 +
        ::google::protobuf::internal::WireFormatLite::UInt32Size(
          this->uint32());
    }

  }
  if (_has_bits_[16 / 32] & 16711680u) {
    // optional uint64 uint64 = 17;
    if (has_uint64()) {
      total_size += 2 +
        ::google::protobuf::internal::WireFormatLite::UInt64Size(
          this->uint64());
    }

    // optional string string = 18;
    if (has_string()) {
      total_size += 2 +
        ::google::protobuf::internal::WireFormatLite::StringSize(
          this->string());
    }

    // optional int32 memset = 19;
    if (has_memset()) {
      total_size += 2 +
        ::google::protobuf::internal::WireFormatLite::Int32Size(
          this->memset());
    }

    // optional int32 int32 = 20;
    if (has_int32()) {
      total_size += 2 +
        ::google::protobuf::internal::WireFormatLite::Int32Size(
          this->int32());
    }

    // optional int64 int64 = 21;
    if (has_int64()) {
      total_size += 2 +
        ::google::protobuf::internal::WireFormatLite::Int64Size(
          this->int64());
    }

    // optional uint32 cached_size = 22;
    if (has_cached_size()) {
      total_size += 2 +
        ::google::protobuf::internal::WireFormatLite::UInt32Size(
          this->cached_size());
    }

    // optional uint32 extensions = 23;
    if (has_extensions()) {
      total_size += 2 +
        ::google::protobuf::internal::WireFormatLite::UInt32Size(
          this->extensions());
    }

    // optional uint32 bit = 24;
    if (has_bit()) {
      total_size += 2 +
        ::google::protobuf::internal::WireFormatLite::UInt32Size(
          this->bit());
    }

  }
  if (_has_bits_[24 / 32] & 4278190080u) {
    // optional uint32 bits = 25;
    if (has_bits()) {
      total_size += 2 +
        ::google::protobuf::internal::WireFormatLite::UInt32Size(
          this->bits());
    }

    // optional uint32 offsets = 26;
    if (has_offsets()) {
      total_size += 2 +
        ::google::protobuf::internal::WireFormatLite::UInt32Size(
          this->offsets());
    }

    // optional uint32 reflection = 27;
    if (has_reflection()) {
      total_size += 2 +
        ::google::protobuf::internal::WireFormatLite::UInt32Size(
          this->reflection());
    }

    // optional string some_cord = 28 [ctype = CORD];
    if (has_some_cord()) {
      total_size += 2 +
        ::google::protobuf::internal::WireFormatLite::StringSize(
          this->some_cord());
    }

    // optional string some_string_piece = 29 [ctype = STRING_PIECE];
    if (has_some_string_piece()) {
      total_size += 2 +
        ::google::protobuf::internal::WireFormatLite::StringSize(
          this->some_string_piece());
    }

    // optional uint32 int = 30;
    if (has_int_()) {
      total_size += 2 +
        ::google::protobuf::internal::WireFormatLite::UInt32Size(
          this->int_());
    }

    // optional uint32 friend = 31;
    if (has_friend_()) {
      total_size += 2 +
        ::google::protobuf::internal::WireFormatLite::UInt32Size(
          this->friend_());
    }

    // optional uint32 class = 37;
    if (has_class_()) {
      total_size += 2 +
        ::google::protobuf::internal::WireFormatLite::UInt32Size(
          this->class_());
    }

  }
  if (_has_bits_[32 / 32] & 255u) {
    // optional uint32 typedecl = 39;
    if (has_typedecl()) {
      total_size += 2 +
        ::google::protobuf::internal::WireFormatLite::UInt32Size(
          this->typedecl());
    }

    // optional uint32 auto = 40;
    if (has_auto_()) {
      total_size += 2 +
        ::google::protobuf::internal::WireFormatLite::UInt32Size(
          this->auto_());
    }

    // optional .protobuf_unittest.TestConflictingSymbolNames.DO do = 32;
    if (has_do_()) {
      total_size += 2 +
        ::google::protobuf::internal::WireFormatLite::MessageSizeNoVirtual(
          *this->do__);
    }

    // optional int32 field_type = 33;
    if (has_field_type()) {
      total_size += 2 +
        ::google::protobuf::internal::WireFormatLite::Int32Size(
          this->field_type());
    }

    // optional bool is_packed = 34;
    if (has_is_packed()) {
      total_size += 2 + 1;
    }

    // optional string release_length = 35;
    if (has_release_length()) {
      total_size += 2 +
        ::google::protobuf::internal::WireFormatLite::StringSize(
          this->release_length());
    }

    // optional .protobuf_unittest.TestConflictingSymbolNames.DO release_do = 36;
    if (has_release_do()) {
      total_size += 2 +
        ::google::protobuf::internal::WireFormatLite::MessageSizeNoVirtual(
          *this->release_do_);
    }

    // optional string target = 38;
    if (has_target()) {
      total_size += 2 +
        ::google::protobuf::internal::WireFormatLite::StringSize(
          this->target());
    }

  }
  // repeated int32 i = 4;
  {
    size_t data_size = 0;
    unsigned int count = this->i_size();
    for (unsigned int i = 0; i < count; i++) {
      data_size += ::google::protobuf::internal::WireFormatLite::
        Int32Size(this->i(i));
    }
    total_size += 1 *
                  ::google::protobuf::internal::FromIntSize(this->i_size());
    total_size += data_size;
  }

  // repeated string new_element = 5 [ctype = STRING_PIECE];
  total_size += 1 *
      ::google::protobuf::internal::FromIntSize(this->new_element_size());
  for (int i = 0; i < this->new_element_size(); i++) {
    total_size += ::google::protobuf::internal::WireFormatLite::StringSize(
      this->new_element(i));
  }

  // repeated int32 index = 13;
  {
    size_t data_size = 0;
    unsigned int count = this->index_size();
    for (unsigned int i = 0; i < count; i++) {
      data_size += ::google::protobuf::internal::WireFormatLite::
        Int32Size(this->index(i));
    }
    total_size += 1 *
                  ::google::protobuf::internal::FromIntSize(this->index_size());
    total_size += data_size;
  }

  total_size += _extensions_.ByteSize();

  if (_internal_metadata_.have_unknown_fields()) {
    total_size +=
      ::google::protobuf::internal::WireFormat::ComputeUnknownFieldsSize(
        unknown_fields());
  }
  int cached_size = ::google::protobuf::internal::ToCachedSize(total_size);
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = cached_size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
  return total_size;
}

void TestConflictingSymbolNames::MergeFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_merge_from_start:protobuf_unittest.TestConflictingSymbolNames)
  if (GOOGLE_PREDICT_FALSE(&from == this)) MergeFromFail(__LINE__);
  const TestConflictingSymbolNames* source =
      ::google::protobuf::internal::DynamicCastToGenerated<const TestConflictingSymbolNames>(
          &from);
  if (source == NULL) {
  // @@protoc_insertion_point(generalized_merge_from_cast_fail:protobuf_unittest.TestConflictingSymbolNames)
    ::google::protobuf::internal::ReflectionOps::Merge(from, this);
  } else {
  // @@protoc_insertion_point(generalized_merge_from_cast_success:protobuf_unittest.TestConflictingSymbolNames)
    UnsafeMergeFrom(*source);
  }
}

void TestConflictingSymbolNames::MergeFrom(const TestConflictingSymbolNames& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:protobuf_unittest.TestConflictingSymbolNames)
  if (GOOGLE_PREDICT_TRUE(&from != this)) {
    UnsafeMergeFrom(from);
  } else {
    MergeFromFail(__LINE__);
  }
}

void TestConflictingSymbolNames::UnsafeMergeFrom(const TestConflictingSymbolNames& from) {
  GOOGLE_DCHECK(&from != this);
  i_.UnsafeMergeFrom(from.i_);
  new_element_.UnsafeMergeFrom(from.new_element_);
  index_.UnsafeMergeFrom(from.index_);
  if (from._has_bits_[0 / 32] & (0xffu << (0 % 32))) {
    if (from.has_input()) {
      set_input(from.input());
    }
    if (from.has_output()) {
      set_output(from.output());
    }
    if (from.has_length()) {
      set_has_length();
      length_.AssignWithDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), from.length_);
    }
    if (from.has_total_size()) {
      set_total_size(from.total_size());
    }
    if (from.has_tag()) {
      set_tag(from.tag());
    }
    if (from.has_source()) {
      set_source(from.source());
    }
  }
  if (from._has_bits_[8 / 32] & (0xffu << (8 % 32))) {
    if (from.has_value()) {
      set_value(from.value());
    }
    if (from.has_file()) {
      set_file(from.file());
    }
    if (from.has_from()) {
      set_from(from.from());
    }
    if (from.has_handle_uninterpreted()) {
      set_handle_uninterpreted(from.handle_uninterpreted());
    }
    if (from.has_controller()) {
      set_controller(from.controller());
    }
    if (from.has_already_here()) {
      set_already_here(from.already_here());
    }
    if (from.has_uint32()) {
      set_uint32(from.uint32());
    }
  }
  if (from._has_bits_[16 / 32] & (0xffu << (16 % 32))) {
    if (from.has_uint64()) {
      set_uint64(from.uint64());
    }
    if (from.has_string()) {
      set_has_string();
      string_.AssignWithDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), from.string_);
    }
    if (from.has_memset()) {
      set_memset(from.memset());
    }
    if (from.has_int32()) {
      set_int32(from.int32());
    }
    if (from.has_int64()) {
      set_int64(from.int64());
    }
    if (from.has_cached_size()) {
      set_cached_size(from.cached_size());
    }
    if (from.has_extensions()) {
      set_extensions(from.extensions());
    }
    if (from.has_bit()) {
      set_bit(from.bit());
    }
  }
  if (from._has_bits_[24 / 32] & (0xffu << (24 % 32))) {
    if (from.has_bits()) {
      set_bits(from.bits());
    }
    if (from.has_offsets()) {
      set_offsets(from.offsets());
    }
    if (from.has_reflection()) {
      set_reflection(from.reflection());
    }
    if (from.has_some_cord()) {
      set_has_some_cord();
      some_cord_.AssignWithDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), from.some_cord_);
    }
    if (from.has_some_string_piece()) {
      set_has_some_string_piece();
      some_string_piece_.AssignWithDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), from.some_string_piece_);
    }
    if (from.has_int_()) {
      set_int_(from.int_());
    }
    if (from.has_friend_()) {
      set_friend_(from.friend_());
    }
    if (from.has_class_()) {
      set_class_(from.class_());
    }
  }
  if (from._has_bits_[32 / 32] & (0xffu << (32 % 32))) {
    if (from.has_typedecl()) {
      set_typedecl(from.typedecl());
    }
    if (from.has_auto_()) {
      set_auto_(from.auto_());
    }
    if (from.has_do_()) {
      mutable_do_()->::protobuf_unittest::TestConflictingSymbolNames_DO::MergeFrom(from.do_());
    }
    if (from.has_field_type()) {
      set_field_type(from.field_type());
    }
    if (from.has_is_packed()) {
      set_is_packed(from.is_packed());
    }
    if (from.has_release_length()) {
      set_has_release_length();
      release_length_.AssignWithDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), from.release_length_);
    }
    if (from.has_release_do()) {
      mutable_release_do()->::protobuf_unittest::TestConflictingSymbolNames_DO::MergeFrom(from.release_do());
    }
    if (from.has_target()) {
      set_has_target();
      target_.AssignWithDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), from.target_);
    }
  }
  _extensions_.MergeFrom(from._extensions_);
  if (from._internal_metadata_.have_unknown_fields()) {
    ::google::protobuf::UnknownFieldSet::MergeToInternalMetdata(
      from.unknown_fields(), &_internal_metadata_);
  }
}

void TestConflictingSymbolNames::CopyFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_copy_from_start:protobuf_unittest.TestConflictingSymbolNames)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

void TestConflictingSymbolNames::CopyFrom(const TestConflictingSymbolNames& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:protobuf_unittest.TestConflictingSymbolNames)
  if (&from == this) return;
  Clear();
  UnsafeMergeFrom(from);
}

bool TestConflictingSymbolNames::IsInitialized() const {


  if (!_extensions_.IsInitialized()) {
    return false;
  }
  return true;
}

void TestConflictingSymbolNames::Swap(TestConflictingSymbolNames* other) {
  if (other == this) return;
  InternalSwap(other);
}
void TestConflictingSymbolNames::InternalSwap(TestConflictingSymbolNames* other) {
  std::swap(input_, other->input_);
  std::swap(output_, other->output_);
  length_.Swap(&other->length_);
  i_.UnsafeArenaSwap(&other->i_);
  new_element_.UnsafeArenaSwap(&other->new_element_);
  std::swap(total_size_, other->total_size_);
  std::swap(tag_, other->tag_);
  std::swap(source_, other->source_);
  std::swap(value_, other->value_);
  std::swap(file_, other->file_);
  std::swap(from_, other->from_);
  std::swap(handle_uninterpreted_, other->handle_uninterpreted_);
  index_.UnsafeArenaSwap(&other->index_);
  std::swap(controller_, other->controller_);
  std::swap(already_here_, other->already_here_);
  std::swap(uint32_, other->uint32_);
  std::swap(uint64_, other->uint64_);
  string_.Swap(&other->string_);
  std::swap(memset_, other->memset_);
  std::swap(int32_, other->int32_);
  std::swap(int64_, other->int64_);
  std::swap(cached_size_, other->cached_size_);
  std::swap(extensions_, other->extensions_);
  std::swap(bit_, other->bit_);
  std::swap(bits_, other->bits_);
  std::swap(offsets_, other->offsets_);
  std::swap(reflection_, other->reflection_);
  some_cord_.Swap(&other->some_cord_);
  some_string_piece_.Swap(&other->some_string_piece_);
  std::swap(int__, other->int__);
  std::swap(friend__, other->friend__);
  std::swap(class__, other->class__);
  std::swap(typedecl_, other->typedecl_);
  std::swap(auto__, other->auto__);
  std::swap(do__, other->do__);
  std::swap(field_type_, other->field_type_);
  std::swap(is_packed_, other->is_packed_);
  release_length_.Swap(&other->release_length_);
  std::swap(release_do_, other->release_do_);
  target_.Swap(&other->target_);
  std::swap(_has_bits_[0], other->_has_bits_[0]);
  std::swap(_has_bits_[1], other->_has_bits_[1]);
  _internal_metadata_.Swap(&other->_internal_metadata_);
  std::swap(_cached_size_, other->_cached_size_);
  _extensions_.Swap(&other->_extensions_);
}

::google::protobuf::Metadata TestConflictingSymbolNames::GetMetadata() const {
  protobuf_AssignDescriptorsOnce();
  ::google::protobuf::Metadata metadata;
  metadata.descriptor = TestConflictingSymbolNames_descriptor_;
  metadata.reflection = TestConflictingSymbolNames_reflection_;
  return metadata;
}

#if PROTOBUF_INLINE_NOT_IN_HEADERS
// TestConflictingSymbolNames_BuildDescriptors

inline const TestConflictingSymbolNames_BuildDescriptors* TestConflictingSymbolNames_BuildDescriptors::internal_default_instance() {
  return &TestConflictingSymbolNames_BuildDescriptors_default_instance_.get();
}
// -------------------------------------------------------------------

// TestConflictingSymbolNames_TypeTraits

inline const TestConflictingSymbolNames_TypeTraits* TestConflictingSymbolNames_TypeTraits::internal_default_instance() {
  return &TestConflictingSymbolNames_TypeTraits_default_instance_.get();
}
// -------------------------------------------------------------------

// TestConflictingSymbolNames_Data1

// repeated int32 data = 1;
int TestConflictingSymbolNames_Data1::data_size() const {
  return data_.size();
}
void TestConflictingSymbolNames_Data1::clear_data() {
  data_.Clear();
}
::google::protobuf::int32 TestConflictingSymbolNames_Data1::data(int index) const {
  // @@protoc_insertion_point(field_get:protobuf_unittest.TestConflictingSymbolNames.Data1.data)
  return data_.Get(index);
}
void TestConflictingSymbolNames_Data1::set_data(int index, ::google::protobuf::int32 value) {
  data_.Set(index, value);
  // @@protoc_insertion_point(field_set:protobuf_unittest.TestConflictingSymbolNames.Data1.data)
}
void TestConflictingSymbolNames_Data1::add_data(::google::protobuf::int32 value) {
  data_.Add(value);
  // @@protoc_insertion_point(field_add:protobuf_unittest.TestConflictingSymbolNames.Data1.data)
}
const ::google::protobuf::RepeatedField< ::google::protobuf::int32 >&
TestConflictingSymbolNames_Data1::data() const {
  // @@protoc_insertion_point(field_list:protobuf_unittest.TestConflictingSymbolNames.Data1.data)
  return data_;
}
::google::protobuf::RepeatedField< ::google::protobuf::int32 >*
TestConflictingSymbolNames_Data1::mutable_data() {
  // @@protoc_insertion_point(field_mutable_list:protobuf_unittest.TestConflictingSymbolNames.Data1.data)
  return &data_;
}

inline const TestConflictingSymbolNames_Data1* TestConflictingSymbolNames_Data1::internal_default_instance() {
  return &TestConflictingSymbolNames_Data1_default_instance_.get();
}
// -------------------------------------------------------------------

// TestConflictingSymbolNames_Data2

// repeated .protobuf_unittest.TestConflictingSymbolNames.TestEnum data = 1;
int TestConflictingSymbolNames_Data2::data_size() const {
  return data_.size();
}
void TestConflictingSymbolNames_Data2::clear_data() {
  data_.Clear();
}
::protobuf_unittest::TestConflictingSymbolNames_TestEnum TestConflictingSymbolNames_Data2::data(int index) const {
  // @@protoc_insertion_point(field_get:protobuf_unittest.TestConflictingSymbolNames.Data2.data)
  return static_cast< ::protobuf_unittest::TestConflictingSymbolNames_TestEnum >(data_.Get(index));
}
void TestConflictingSymbolNames_Data2::set_data(int index, ::protobuf_unittest::TestConflictingSymbolNames_TestEnum value) {
  assert(::protobuf_unittest::TestConflictingSymbolNames_TestEnum_IsValid(value));
  data_.Set(index, value);
  // @@protoc_insertion_point(field_set:protobuf_unittest.TestConflictingSymbolNames.Data2.data)
}
void TestConflictingSymbolNames_Data2::add_data(::protobuf_unittest::TestConflictingSymbolNames_TestEnum value) {
  assert(::protobuf_unittest::TestConflictingSymbolNames_TestEnum_IsValid(value));
  data_.Add(value);
  // @@protoc_insertion_point(field_add:protobuf_unittest.TestConflictingSymbolNames.Data2.data)
}
const ::google::protobuf::RepeatedField<int>&
TestConflictingSymbolNames_Data2::data() const {
  // @@protoc_insertion_point(field_list:protobuf_unittest.TestConflictingSymbolNames.Data2.data)
  return data_;
}
::google::protobuf::RepeatedField<int>*
TestConflictingSymbolNames_Data2::mutable_data() {
  // @@protoc_insertion_point(field_mutable_list:protobuf_unittest.TestConflictingSymbolNames.Data2.data)
  return &data_;
}

inline const TestConflictingSymbolNames_Data2* TestConflictingSymbolNames_Data2::internal_default_instance() {
  return &TestConflictingSymbolNames_Data2_default_instance_.get();
}
// -------------------------------------------------------------------

// TestConflictingSymbolNames_Data3

// repeated string data = 1;
int TestConflictingSymbolNames_Data3::data_size() const {
  return data_.size();
}
void TestConflictingSymbolNames_Data3::clear_data() {
  data_.Clear();
}
const ::std::string& TestConflictingSymbolNames_Data3::data(int index) const {
  // @@protoc_insertion_point(field_get:protobuf_unittest.TestConflictingSymbolNames.Data3.data)
  return data_.Get(index);
}
::std::string* TestConflictingSymbolNames_Data3::mutable_data(int index) {
  // @@protoc_insertion_point(field_mutable:protobuf_unittest.TestConflictingSymbolNames.Data3.data)
  return data_.Mutable(index);
}
void TestConflictingSymbolNames_Data3::set_data(int index, const ::std::string& value) {
  // @@protoc_insertion_point(field_set:protobuf_unittest.TestConflictingSymbolNames.Data3.data)
  data_.Mutable(index)->assign(value);
}
void TestConflictingSymbolNames_Data3::set_data(int index, const char* value) {
  data_.Mutable(index)->assign(value);
  // @@protoc_insertion_point(field_set_char:protobuf_unittest.TestConflictingSymbolNames.Data3.data)
}
void TestConflictingSymbolNames_Data3::set_data(int index, const char* value, size_t size) {
  data_.Mutable(index)->assign(
    reinterpret_cast<const char*>(value), size);
  // @@protoc_insertion_point(field_set_pointer:protobuf_unittest.TestConflictingSymbolNames.Data3.data)
}
::std::string* TestConflictingSymbolNames_Data3::add_data() {
  // @@protoc_insertion_point(field_add_mutable:protobuf_unittest.TestConflictingSymbolNames.Data3.data)
  return data_.Add();
}
void TestConflictingSymbolNames_Data3::add_data(const ::std::string& value) {
  data_.Add()->assign(value);
  // @@protoc_insertion_point(field_add:protobuf_unittest.TestConflictingSymbolNames.Data3.data)
}
void TestConflictingSymbolNames_Data3::add_data(const char* value) {
  data_.Add()->assign(value);
  // @@protoc_insertion_point(field_add_char:protobuf_unittest.TestConflictingSymbolNames.Data3.data)
}
void TestConflictingSymbolNames_Data3::add_data(const char* value, size_t size) {
  data_.Add()->assign(reinterpret_cast<const char*>(value), size);
  // @@protoc_insertion_point(field_add_pointer:protobuf_unittest.TestConflictingSymbolNames.Data3.data)
}
const ::google::protobuf::RepeatedPtrField< ::std::string>&
TestConflictingSymbolNames_Data3::data() const {
  // @@protoc_insertion_point(field_list:protobuf_unittest.TestConflictingSymbolNames.Data3.data)
  return data_;
}
::google::protobuf::RepeatedPtrField< ::std::string>*
TestConflictingSymbolNames_Data3::mutable_data() {
  // @@protoc_insertion_point(field_mutable_list:protobuf_unittest.TestConflictingSymbolNames.Data3.data)
  return &data_;
}

inline const TestConflictingSymbolNames_Data3* TestConflictingSymbolNames_Data3::internal_default_instance() {
  return &TestConflictingSymbolNames_Data3_default_instance_.get();
}
// -------------------------------------------------------------------

// TestConflictingSymbolNames_Data4

// repeated .protobuf_unittest.TestConflictingSymbolNames.Data4 data = 1;
int TestConflictingSymbolNames_Data4::data_size() const {
  return data_.size();
}
void TestConflictingSymbolNames_Data4::clear_data() {
  data_.Clear();
}
const ::protobuf_unittest::TestConflictingSymbolNames_Data4& TestConflictingSymbolNames_Data4::data(int index) const {
  // @@protoc_insertion_point(field_get:protobuf_unittest.TestConflictingSymbolNames.Data4.data)
  return data_.Get(index);
}
::protobuf_unittest::TestConflictingSymbolNames_Data4* TestConflictingSymbolNames_Data4::mutable_data(int index) {
  // @@protoc_insertion_point(field_mutable:protobuf_unittest.TestConflictingSymbolNames.Data4.data)
  return data_.Mutable(index);
}
::protobuf_unittest::TestConflictingSymbolNames_Data4* TestConflictingSymbolNames_Data4::add_data() {
  // @@protoc_insertion_point(field_add:protobuf_unittest.TestConflictingSymbolNames.Data4.data)
  return data_.Add();
}
::google::protobuf::RepeatedPtrField< ::protobuf_unittest::TestConflictingSymbolNames_Data4 >*
TestConflictingSymbolNames_Data4::mutable_data() {
  // @@protoc_insertion_point(field_mutable_list:protobuf_unittest.TestConflictingSymbolNames.Data4.data)
  return &data_;
}
const ::google::protobuf::RepeatedPtrField< ::protobuf_unittest::TestConflictingSymbolNames_Data4 >&
TestConflictingSymbolNames_Data4::data() const {
  // @@protoc_insertion_point(field_list:protobuf_unittest.TestConflictingSymbolNames.Data4.data)
  return data_;
}

inline const TestConflictingSymbolNames_Data4* TestConflictingSymbolNames_Data4::internal_default_instance() {
  return &TestConflictingSymbolNames_Data4_default_instance_.get();
}
// -------------------------------------------------------------------

// TestConflictingSymbolNames_Data5

// repeated string data = 1 [ctype = STRING_PIECE];
int TestConflictingSymbolNames_Data5::data_size() const {
  return data_.size();
}
void TestConflictingSymbolNames_Data5::clear_data() {
  data_.Clear();
}
const ::std::string& TestConflictingSymbolNames_Data5::data(int index) const {
  // @@protoc_insertion_point(field_get:protobuf_unittest.TestConflictingSymbolNames.Data5.data)
  return data_.Get(index);
}
::std::string* TestConflictingSymbolNames_Data5::mutable_data(int index) {
  // @@protoc_insertion_point(field_mutable:protobuf_unittest.TestConflictingSymbolNames.Data5.data)
  return data_.Mutable(index);
}
void TestConflictingSymbolNames_Data5::set_data(int index, const ::std::string& value) {
  // @@protoc_insertion_point(field_set:protobuf_unittest.TestConflictingSymbolNames.Data5.data)
  data_.Mutable(index)->assign(value);
}
void TestConflictingSymbolNames_Data5::set_data(int index, const char* value) {
  data_.Mutable(index)->assign(value);
  // @@protoc_insertion_point(field_set_char:protobuf_unittest.TestConflictingSymbolNames.Data5.data)
}
void TestConflictingSymbolNames_Data5::set_data(int index, const char* value, size_t size) {
  data_.Mutable(index)->assign(
    reinterpret_cast<const char*>(value), size);
  // @@protoc_insertion_point(field_set_pointer:protobuf_unittest.TestConflictingSymbolNames.Data5.data)
}
::std::string* TestConflictingSymbolNames_Data5::add_data() {
  // @@protoc_insertion_point(field_add_mutable:protobuf_unittest.TestConflictingSymbolNames.Data5.data)
  return data_.Add();
}
void TestConflictingSymbolNames_Data5::add_data(const ::std::string& value) {
  data_.Add()->assign(value);
  // @@protoc_insertion_point(field_add:protobuf_unittest.TestConflictingSymbolNames.Data5.data)
}
void TestConflictingSymbolNames_Data5::add_data(const char* value) {
  data_.Add()->assign(value);
  // @@protoc_insertion_point(field_add_char:protobuf_unittest.TestConflictingSymbolNames.Data5.data)
}
void TestConflictingSymbolNames_Data5::add_data(const char* value, size_t size) {
  data_.Add()->assign(reinterpret_cast<const char*>(value), size);
  // @@protoc_insertion_point(field_add_pointer:protobuf_unittest.TestConflictingSymbolNames.Data5.data)
}
const ::google::protobuf::RepeatedPtrField< ::std::string>&
TestConflictingSymbolNames_Data5::data() const {
  // @@protoc_insertion_point(field_list:protobuf_unittest.TestConflictingSymbolNames.Data5.data)
  return data_;
}
::google::protobuf::RepeatedPtrField< ::std::string>*
TestConflictingSymbolNames_Data5::mutable_data() {
  // @@protoc_insertion_point(field_mutable_list:protobuf_unittest.TestConflictingSymbolNames.Data5.data)
  return &data_;
}

inline const TestConflictingSymbolNames_Data5* TestConflictingSymbolNames_Data5::internal_default_instance() {
  return &TestConflictingSymbolNames_Data5_default_instance_.get();
}
// -------------------------------------------------------------------

// TestConflictingSymbolNames_Data6

// repeated string data = 1 [ctype = CORD];
int TestConflictingSymbolNames_Data6::data_size() const {
  return data_.size();
}
void TestConflictingSymbolNames_Data6::clear_data() {
  data_.Clear();
}
const ::std::string& TestConflictingSymbolNames_Data6::data(int index) const {
  // @@protoc_insertion_point(field_get:protobuf_unittest.TestConflictingSymbolNames.Data6.data)
  return data_.Get(index);
}
::std::string* TestConflictingSymbolNames_Data6::mutable_data(int index) {
  // @@protoc_insertion_point(field_mutable:protobuf_unittest.TestConflictingSymbolNames.Data6.data)
  return data_.Mutable(index);
}
void TestConflictingSymbolNames_Data6::set_data(int index, const ::std::string& value) {
  // @@protoc_insertion_point(field_set:protobuf_unittest.TestConflictingSymbolNames.Data6.data)
  data_.Mutable(index)->assign(value);
}
void TestConflictingSymbolNames_Data6::set_data(int index, const char* value) {
  data_.Mutable(index)->assign(value);
  // @@protoc_insertion_point(field_set_char:protobuf_unittest.TestConflictingSymbolNames.Data6.data)
}
void TestConflictingSymbolNames_Data6::set_data(int index, const char* value, size_t size) {
  data_.Mutable(index)->assign(
    reinterpret_cast<const char*>(value), size);
  // @@protoc_insertion_point(field_set_pointer:protobuf_unittest.TestConflictingSymbolNames.Data6.data)
}
::std::string* TestConflictingSymbolNames_Data6::add_data() {
  // @@protoc_insertion_point(field_add_mutable:protobuf_unittest.TestConflictingSymbolNames.Data6.data)
  return data_.Add();
}
void TestConflictingSymbolNames_Data6::add_data(const ::std::string& value) {
  data_.Add()->assign(value);
  // @@protoc_insertion_point(field_add:protobuf_unittest.TestConflictingSymbolNames.Data6.data)
}
void TestConflictingSymbolNames_Data6::add_data(const char* value) {
  data_.Add()->assign(value);
  // @@protoc_insertion_point(field_add_char:protobuf_unittest.TestConflictingSymbolNames.Data6.data)
}
void TestConflictingSymbolNames_Data6::add_data(const char* value, size_t size) {
  data_.Add()->assign(reinterpret_cast<const char*>(value), size);
  // @@protoc_insertion_point(field_add_pointer:protobuf_unittest.TestConflictingSymbolNames.Data6.data)
}
const ::google::protobuf::RepeatedPtrField< ::std::string>&
TestConflictingSymbolNames_Data6::data() const {
  // @@protoc_insertion_point(field_list:protobuf_unittest.TestConflictingSymbolNames.Data6.data)
  return data_;
}
::google::protobuf::RepeatedPtrField< ::std::string>*
TestConflictingSymbolNames_Data6::mutable_data() {
  // @@protoc_insertion_point(field_mutable_list:protobuf_unittest.TestConflictingSymbolNames.Data6.data)
  return &data_;
}

inline const TestConflictingSymbolNames_Data6* TestConflictingSymbolNames_Data6::internal_default_instance() {
  return &TestConflictingSymbolNames_Data6_default_instance_.get();
}
// -------------------------------------------------------------------

// TestConflictingSymbolNames_Cord

inline const TestConflictingSymbolNames_Cord* TestConflictingSymbolNames_Cord::internal_default_instance() {
  return &TestConflictingSymbolNames_Cord_default_instance_.get();
}
// -------------------------------------------------------------------

// TestConflictingSymbolNames_StringPiece

inline const TestConflictingSymbolNames_StringPiece* TestConflictingSymbolNames_StringPiece::internal_default_instance() {
  return &TestConflictingSymbolNames_StringPiece_default_instance_.get();
}
// -------------------------------------------------------------------

// TestConflictingSymbolNames_DO

inline const TestConflictingSymbolNames_DO* TestConflictingSymbolNames_DO::internal_default_instance() {
  return &TestConflictingSymbolNames_DO_default_instance_.get();
}
// -------------------------------------------------------------------

// TestConflictingSymbolNames

// optional int32 input = 1;
bool TestConflictingSymbolNames::has_input() const {
  return (_has_bits_[0] & 0x00000001u) != 0;
}
void TestConflictingSymbolNames::set_has_input() {
  _has_bits_[0] |= 0x00000001u;
}
void TestConflictingSymbolNames::clear_has_input() {
  _has_bits_[0] &= ~0x00000001u;
}
void TestConflictingSymbolNames::clear_input() {
  input_ = 0;
  clear_has_input();
}
::google::protobuf::int32 TestConflictingSymbolNames::input() const {
  // @@protoc_insertion_point(field_get:protobuf_unittest.TestConflictingSymbolNames.input)
  return input_;
}
void TestConflictingSymbolNames::set_input(::google::protobuf::int32 value) {
  set_has_input();
  input_ = value;
  // @@protoc_insertion_point(field_set:protobuf_unittest.TestConflictingSymbolNames.input)
}

// optional int32 output = 2;
bool TestConflictingSymbolNames::has_output() const {
  return (_has_bits_[0] & 0x00000002u) != 0;
}
void TestConflictingSymbolNames::set_has_output() {
  _has_bits_[0] |= 0x00000002u;
}
void TestConflictingSymbolNames::clear_has_output() {
  _has_bits_[0] &= ~0x00000002u;
}
void TestConflictingSymbolNames::clear_output() {
  output_ = 0;
  clear_has_output();
}
::google::protobuf::int32 TestConflictingSymbolNames::output() const {
  // @@protoc_insertion_point(field_get:protobuf_unittest.TestConflictingSymbolNames.output)
  return output_;
}
void TestConflictingSymbolNames::set_output(::google::protobuf::int32 value) {
  set_has_output();
  output_ = value;
  // @@protoc_insertion_point(field_set:protobuf_unittest.TestConflictingSymbolNames.output)
}

// optional string length = 3;
bool TestConflictingSymbolNames::has_length() const {
  return (_has_bits_[0] & 0x00000004u) != 0;
}
void TestConflictingSymbolNames::set_has_length() {
  _has_bits_[0] |= 0x00000004u;
}
void TestConflictingSymbolNames::clear_has_length() {
  _has_bits_[0] &= ~0x00000004u;
}
void TestConflictingSymbolNames::clear_length() {
  length_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  clear_has_length();
}
const ::std::string& TestConflictingSymbolNames::length() const {
  // @@protoc_insertion_point(field_get:protobuf_unittest.TestConflictingSymbolNames.length)
  return length_.GetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
void TestConflictingSymbolNames::set_length(const ::std::string& value) {
  set_has_length();
  length_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:protobuf_unittest.TestConflictingSymbolNames.length)
}
void TestConflictingSymbolNames::set_length(const char* value) {
  set_has_length();
  length_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:protobuf_unittest.TestConflictingSymbolNames.length)
}
void TestConflictingSymbolNames::set_length(const char* value, size_t size) {
  set_has_length();
  length_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:protobuf_unittest.TestConflictingSymbolNames.length)
}
::std::string* TestConflictingSymbolNames::mutable_length() {
  set_has_length();
  // @@protoc_insertion_point(field_mutable:protobuf_unittest.TestConflictingSymbolNames.length)
  return length_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
::std::string* TestConflictingSymbolNames::release_length__() {
  // @@protoc_insertion_point(field_release:protobuf_unittest.TestConflictingSymbolNames.length)
  clear_has_length();
  return length_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
void TestConflictingSymbolNames::set_allocated_length(::std::string* length) {
  if (length != NULL) {
    set_has_length();
  } else {
    clear_has_length();
  }
  length_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), length);
  // @@protoc_insertion_point(field_set_allocated:protobuf_unittest.TestConflictingSymbolNames.length)
}

// repeated int32 i = 4;
int TestConflictingSymbolNames::i_size() const {
  return i_.size();
}
void TestConflictingSymbolNames::clear_i() {
  i_.Clear();
}
::google::protobuf::int32 TestConflictingSymbolNames::i(int index) const {
  // @@protoc_insertion_point(field_get:protobuf_unittest.TestConflictingSymbolNames.i)
  return i_.Get(index);
}
void TestConflictingSymbolNames::set_i(int index, ::google::protobuf::int32 value) {
  i_.Set(index, value);
  // @@protoc_insertion_point(field_set:protobuf_unittest.TestConflictingSymbolNames.i)
}
void TestConflictingSymbolNames::add_i(::google::protobuf::int32 value) {
  i_.Add(value);
  // @@protoc_insertion_point(field_add:protobuf_unittest.TestConflictingSymbolNames.i)
}
const ::google::protobuf::RepeatedField< ::google::protobuf::int32 >&
TestConflictingSymbolNames::i() const {
  // @@protoc_insertion_point(field_list:protobuf_unittest.TestConflictingSymbolNames.i)
  return i_;
}
::google::protobuf::RepeatedField< ::google::protobuf::int32 >*
TestConflictingSymbolNames::mutable_i() {
  // @@protoc_insertion_point(field_mutable_list:protobuf_unittest.TestConflictingSymbolNames.i)
  return &i_;
}

// repeated string new_element = 5 [ctype = STRING_PIECE];
int TestConflictingSymbolNames::new_element_size() const {
  return new_element_.size();
}
void TestConflictingSymbolNames::clear_new_element() {
  new_element_.Clear();
}
const ::std::string& TestConflictingSymbolNames::new_element(int index) const {
  // @@protoc_insertion_point(field_get:protobuf_unittest.TestConflictingSymbolNames.new_element)
  return new_element_.Get(index);
}
::std::string* TestConflictingSymbolNames::mutable_new_element(int index) {
  // @@protoc_insertion_point(field_mutable:protobuf_unittest.TestConflictingSymbolNames.new_element)
  return new_element_.Mutable(index);
}
void TestConflictingSymbolNames::set_new_element(int index, const ::std::string& value) {
  // @@protoc_insertion_point(field_set:protobuf_unittest.TestConflictingSymbolNames.new_element)
  new_element_.Mutable(index)->assign(value);
}
void TestConflictingSymbolNames::set_new_element(int index, const char* value) {
  new_element_.Mutable(index)->assign(value);
  // @@protoc_insertion_point(field_set_char:protobuf_unittest.TestConflictingSymbolNames.new_element)
}
void TestConflictingSymbolNames::set_new_element(int index, const char* value, size_t size) {
  new_element_.Mutable(index)->assign(
    reinterpret_cast<const char*>(value), size);
  // @@protoc_insertion_point(field_set_pointer:protobuf_unittest.TestConflictingSymbolNames.new_element)
}
::std::string* TestConflictingSymbolNames::add_new_element() {
  // @@protoc_insertion_point(field_add_mutable:protobuf_unittest.TestConflictingSymbolNames.new_element)
  return new_element_.Add();
}
void TestConflictingSymbolNames::add_new_element(const ::std::string& value) {
  new_element_.Add()->assign(value);
  // @@protoc_insertion_point(field_add:protobuf_unittest.TestConflictingSymbolNames.new_element)
}
void TestConflictingSymbolNames::add_new_element(const char* value) {
  new_element_.Add()->assign(value);
  // @@protoc_insertion_point(field_add_char:protobuf_unittest.TestConflictingSymbolNames.new_element)
}
void TestConflictingSymbolNames::add_new_element(const char* value, size_t size) {
  new_element_.Add()->assign(reinterpret_cast<const char*>(value), size);
  // @@protoc_insertion_point(field_add_pointer:protobuf_unittest.TestConflictingSymbolNames.new_element)
}
const ::google::protobuf::RepeatedPtrField< ::std::string>&
TestConflictingSymbolNames::new_element() const {
  // @@protoc_insertion_point(field_list:protobuf_unittest.TestConflictingSymbolNames.new_element)
  return new_element_;
}
::google::protobuf::RepeatedPtrField< ::std::string>*
TestConflictingSymbolNames::mutable_new_element() {
  // @@protoc_insertion_point(field_mutable_list:protobuf_unittest.TestConflictingSymbolNames.new_element)
  return &new_element_;
}

// optional int32 total_size = 6;
bool TestConflictingSymbolNames::has_total_size() const {
  return (_has_bits_[0] & 0x00000020u) != 0;
}
void TestConflictingSymbolNames::set_has_total_size() {
  _has_bits_[0] |= 0x00000020u;
}
void TestConflictingSymbolNames::clear_has_total_size() {
  _has_bits_[0] &= ~0x00000020u;
}
void TestConflictingSymbolNames::clear_total_size() {
  total_size_ = 0;
  clear_has_total_size();
}
::google::protobuf::int32 TestConflictingSymbolNames::total_size() const {
  // @@protoc_insertion_point(field_get:protobuf_unittest.TestConflictingSymbolNames.total_size)
  return total_size_;
}
void TestConflictingSymbolNames::set_total_size(::google::protobuf::int32 value) {
  set_has_total_size();
  total_size_ = value;
  // @@protoc_insertion_point(field_set:protobuf_unittest.TestConflictingSymbolNames.total_size)
}

// optional int32 tag = 7;
bool TestConflictingSymbolNames::has_tag() const {
  return (_has_bits_[0] & 0x00000040u) != 0;
}
void TestConflictingSymbolNames::set_has_tag() {
  _has_bits_[0] |= 0x00000040u;
}
void TestConflictingSymbolNames::clear_has_tag() {
  _has_bits_[0] &= ~0x00000040u;
}
void TestConflictingSymbolNames::clear_tag() {
  tag_ = 0;
  clear_has_tag();
}
::google::protobuf::int32 TestConflictingSymbolNames::tag() const {
  // @@protoc_insertion_point(field_get:protobuf_unittest.TestConflictingSymbolNames.tag)
  return tag_;
}
void TestConflictingSymbolNames::set_tag(::google::protobuf::int32 value) {
  set_has_tag();
  tag_ = value;
  // @@protoc_insertion_point(field_set:protobuf_unittest.TestConflictingSymbolNames.tag)
}

// optional int32 source = 8;
bool TestConflictingSymbolNames::has_source() const {
  return (_has_bits_[0] & 0x00000080u) != 0;
}
void TestConflictingSymbolNames::set_has_source() {
  _has_bits_[0] |= 0x00000080u;
}
void TestConflictingSymbolNames::clear_has_source() {
  _has_bits_[0] &= ~0x00000080u;
}
void TestConflictingSymbolNames::clear_source() {
  source_ = 0;
  clear_has_source();
}
::google::protobuf::int32 TestConflictingSymbolNames::source() const {
  // @@protoc_insertion_point(field_get:protobuf_unittest.TestConflictingSymbolNames.source)
  return source_;
}
void TestConflictingSymbolNames::set_source(::google::protobuf::int32 value) {
  set_has_source();
  source_ = value;
  // @@protoc_insertion_point(field_set:protobuf_unittest.TestConflictingSymbolNames.source)
}

// optional int32 value = 9;
bool TestConflictingSymbolNames::has_value() const {
  return (_has_bits_[0] & 0x00000100u) != 0;
}
void TestConflictingSymbolNames::set_has_value() {
  _has_bits_[0] |= 0x00000100u;
}
void TestConflictingSymbolNames::clear_has_value() {
  _has_bits_[0] &= ~0x00000100u;
}
void TestConflictingSymbolNames::clear_value() {
  value_ = 0;
  clear_has_value();
}
::google::protobuf::int32 TestConflictingSymbolNames::value() const {
  // @@protoc_insertion_point(field_get:protobuf_unittest.TestConflictingSymbolNames.value)
  return value_;
}
void TestConflictingSymbolNames::set_value(::google::protobuf::int32 value) {
  set_has_value();
  value_ = value;
  // @@protoc_insertion_point(field_set:protobuf_unittest.TestConflictingSymbolNames.value)
}

// optional int32 file = 10;
bool TestConflictingSymbolNames::has_file() const {
  return (_has_bits_[0] & 0x00000200u) != 0;
}
void TestConflictingSymbolNames::set_has_file() {
  _has_bits_[0] |= 0x00000200u;
}
void TestConflictingSymbolNames::clear_has_file() {
  _has_bits_[0] &= ~0x00000200u;
}
void TestConflictingSymbolNames::clear_file() {
  file_ = 0;
  clear_has_file();
}
::google::protobuf::int32 TestConflictingSymbolNames::file() const {
  // @@protoc_insertion_point(field_get:protobuf_unittest.TestConflictingSymbolNames.file)
  return file_;
}
void TestConflictingSymbolNames::set_file(::google::protobuf::int32 value) {
  set_has_file();
  file_ = value;
  // @@protoc_insertion_point(field_set:protobuf_unittest.TestConflictingSymbolNames.file)
}

// optional int32 from = 11;
bool TestConflictingSymbolNames::has_from() const {
  return (_has_bits_[0] & 0x00000400u) != 0;
}
void TestConflictingSymbolNames::set_has_from() {
  _has_bits_[0] |= 0x00000400u;
}
void TestConflictingSymbolNames::clear_has_from() {
  _has_bits_[0] &= ~0x00000400u;
}
void TestConflictingSymbolNames::clear_from() {
  from_ = 0;
  clear_has_from();
}
::google::protobuf::int32 TestConflictingSymbolNames::from() const {
  // @@protoc_insertion_point(field_get:protobuf_unittest.TestConflictingSymbolNames.from)
  return from_;
}
void TestConflictingSymbolNames::set_from(::google::protobuf::int32 value) {
  set_has_from();
  from_ = value;
  // @@protoc_insertion_point(field_set:protobuf_unittest.TestConflictingSymbolNames.from)
}

// optional int32 handle_uninterpreted = 12;
bool TestConflictingSymbolNames::has_handle_uninterpreted() const {
  return (_has_bits_[0] & 0x00000800u) != 0;
}
void TestConflictingSymbolNames::set_has_handle_uninterpreted() {
  _has_bits_[0] |= 0x00000800u;
}
void TestConflictingSymbolNames::clear_has_handle_uninterpreted() {
  _has_bits_[0] &= ~0x00000800u;
}
void TestConflictingSymbolNames::clear_handle_uninterpreted() {
  handle_uninterpreted_ = 0;
  clear_has_handle_uninterpreted();
}
::google::protobuf::int32 TestConflictingSymbolNames::handle_uninterpreted() const {
  // @@protoc_insertion_point(field_get:protobuf_unittest.TestConflictingSymbolNames.handle_uninterpreted)
  return handle_uninterpreted_;
}
void TestConflictingSymbolNames::set_handle_uninterpreted(::google::protobuf::int32 value) {
  set_has_handle_uninterpreted();
  handle_uninterpreted_ = value;
  // @@protoc_insertion_point(field_set:protobuf_unittest.TestConflictingSymbolNames.handle_uninterpreted)
}

// repeated int32 index = 13;
int TestConflictingSymbolNames::index_size() const {
  return index_.size();
}
void TestConflictingSymbolNames::clear_index() {
  index_.Clear();
}
::google::protobuf::int32 TestConflictingSymbolNames::index(int index) const {
  // @@protoc_insertion_point(field_get:protobuf_unittest.TestConflictingSymbolNames.index)
  return index_.Get(index);
}
void TestConflictingSymbolNames::set_index(int index, ::google::protobuf::int32 value) {
  index_.Set(index, value);
  // @@protoc_insertion_point(field_set:protobuf_unittest.TestConflictingSymbolNames.index)
}
void TestConflictingSymbolNames::add_index(::google::protobuf::int32 value) {
  index_.Add(value);
  // @@protoc_insertion_point(field_add:protobuf_unittest.TestConflictingSymbolNames.index)
}
const ::google::protobuf::RepeatedField< ::google::protobuf::int32 >&
TestConflictingSymbolNames::index() const {
  // @@protoc_insertion_point(field_list:protobuf_unittest.TestConflictingSymbolNames.index)
  return index_;
}
::google::protobuf::RepeatedField< ::google::protobuf::int32 >*
TestConflictingSymbolNames::mutable_index() {
  // @@protoc_insertion_point(field_mutable_list:protobuf_unittest.TestConflictingSymbolNames.index)
  return &index_;
}

// optional int32 controller = 14;
bool TestConflictingSymbolNames::has_controller() const {
  return (_has_bits_[0] & 0x00002000u) != 0;
}
void TestConflictingSymbolNames::set_has_controller() {
  _has_bits_[0] |= 0x00002000u;
}
void TestConflictingSymbolNames::clear_has_controller() {
  _has_bits_[0] &= ~0x00002000u;
}
void TestConflictingSymbolNames::clear_controller() {
  controller_ = 0;
  clear_has_controller();
}
::google::protobuf::int32 TestConflictingSymbolNames::controller() const {
  // @@protoc_insertion_point(field_get:protobuf_unittest.TestConflictingSymbolNames.controller)
  return controller_;
}
void TestConflictingSymbolNames::set_controller(::google::protobuf::int32 value) {
  set_has_controller();
  controller_ = value;
  // @@protoc_insertion_point(field_set:protobuf_unittest.TestConflictingSymbolNames.controller)
}

// optional int32 already_here = 15;
bool TestConflictingSymbolNames::has_already_here() const {
  return (_has_bits_[0] & 0x00004000u) != 0;
}
void TestConflictingSymbolNames::set_has_already_here() {
  _has_bits_[0] |= 0x00004000u;
}
void TestConflictingSymbolNames::clear_has_already_here() {
  _has_bits_[0] &= ~0x00004000u;
}
void TestConflictingSymbolNames::clear_already_here() {
  already_here_ = 0;
  clear_has_already_here();
}
::google::protobuf::int32 TestConflictingSymbolNames::already_here() const {
  // @@protoc_insertion_point(field_get:protobuf_unittest.TestConflictingSymbolNames.already_here)
  return already_here_;
}
void TestConflictingSymbolNames::set_already_here(::google::protobuf::int32 value) {
  set_has_already_here();
  already_here_ = value;
  // @@protoc_insertion_point(field_set:protobuf_unittest.TestConflictingSymbolNames.already_here)
}

// optional uint32 uint32 = 16;
bool TestConflictingSymbolNames::has_uint32() const {
  return (_has_bits_[0] & 0x00008000u) != 0;
}
void TestConflictingSymbolNames::set_has_uint32() {
  _has_bits_[0] |= 0x00008000u;
}
void TestConflictingSymbolNames::clear_has_uint32() {
  _has_bits_[0] &= ~0x00008000u;
}
void TestConflictingSymbolNames::clear_uint32() {
  uint32_ = 0u;
  clear_has_uint32();
}
::google::protobuf::uint32 TestConflictingSymbolNames::uint32() const {
  // @@protoc_insertion_point(field_get:protobuf_unittest.TestConflictingSymbolNames.uint32)
  return uint32_;
}
void TestConflictingSymbolNames::set_uint32(::google::protobuf::uint32 value) {
  set_has_uint32();
  uint32_ = value;
  // @@protoc_insertion_point(field_set:protobuf_unittest.TestConflictingSymbolNames.uint32)
}

// optional uint64 uint64 = 17;
bool TestConflictingSymbolNames::has_uint64() const {
  return (_has_bits_[0] & 0x00010000u) != 0;
}
void TestConflictingSymbolNames::set_has_uint64() {
  _has_bits_[0] |= 0x00010000u;
}
void TestConflictingSymbolNames::clear_has_uint64() {
  _has_bits_[0] &= ~0x00010000u;
}
void TestConflictingSymbolNames::clear_uint64() {
  uint64_ = GOOGLE_ULONGLONG(0);
  clear_has_uint64();
}
::google::protobuf::uint64 TestConflictingSymbolNames::uint64() const {
  // @@protoc_insertion_point(field_get:protobuf_unittest.TestConflictingSymbolNames.uint64)
  return uint64_;
}
void TestConflictingSymbolNames::set_uint64(::google::protobuf::uint64 value) {
  set_has_uint64();
  uint64_ = value;
  // @@protoc_insertion_point(field_set:protobuf_unittest.TestConflictingSymbolNames.uint64)
}

// optional string string = 18;
bool TestConflictingSymbolNames::has_string() const {
  return (_has_bits_[0] & 0x00020000u) != 0;
}
void TestConflictingSymbolNames::set_has_string() {
  _has_bits_[0] |= 0x00020000u;
}
void TestConflictingSymbolNames::clear_has_string() {
  _has_bits_[0] &= ~0x00020000u;
}
void TestConflictingSymbolNames::clear_string() {
  string_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  clear_has_string();
}
const ::std::string& TestConflictingSymbolNames::string() const {
  // @@protoc_insertion_point(field_get:protobuf_unittest.TestConflictingSymbolNames.string)
  return string_.GetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
void TestConflictingSymbolNames::set_string(const ::std::string& value) {
  set_has_string();
  string_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:protobuf_unittest.TestConflictingSymbolNames.string)
}
void TestConflictingSymbolNames::set_string(const char* value) {
  set_has_string();
  string_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:protobuf_unittest.TestConflictingSymbolNames.string)
}
void TestConflictingSymbolNames::set_string(const char* value, size_t size) {
  set_has_string();
  string_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:protobuf_unittest.TestConflictingSymbolNames.string)
}
::std::string* TestConflictingSymbolNames::mutable_string() {
  set_has_string();
  // @@protoc_insertion_point(field_mutable:protobuf_unittest.TestConflictingSymbolNames.string)
  return string_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
::std::string* TestConflictingSymbolNames::release_string() {
  // @@protoc_insertion_point(field_release:protobuf_unittest.TestConflictingSymbolNames.string)
  clear_has_string();
  return string_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
void TestConflictingSymbolNames::set_allocated_string(::std::string* string) {
  if (string != NULL) {
    set_has_string();
  } else {
    clear_has_string();
  }
  string_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), string);
  // @@protoc_insertion_point(field_set_allocated:protobuf_unittest.TestConflictingSymbolNames.string)
}

// optional int32 memset = 19;
bool TestConflictingSymbolNames::has_memset() const {
  return (_has_bits_[0] & 0x00040000u) != 0;
}
void TestConflictingSymbolNames::set_has_memset() {
  _has_bits_[0] |= 0x00040000u;
}
void TestConflictingSymbolNames::clear_has_memset() {
  _has_bits_[0] &= ~0x00040000u;
}
void TestConflictingSymbolNames::clear_memset() {
  memset_ = 0;
  clear_has_memset();
}
::google::protobuf::int32 TestConflictingSymbolNames::memset() const {
  // @@protoc_insertion_point(field_get:protobuf_unittest.TestConflictingSymbolNames.memset)
  return memset_;
}
void TestConflictingSymbolNames::set_memset(::google::protobuf::int32 value) {
  set_has_memset();
  memset_ = value;
  // @@protoc_insertion_point(field_set:protobuf_unittest.TestConflictingSymbolNames.memset)
}

// optional int32 int32 = 20;
bool TestConflictingSymbolNames::has_int32() const {
  return (_has_bits_[0] & 0x00080000u) != 0;
}
void TestConflictingSymbolNames::set_has_int32() {
  _has_bits_[0] |= 0x00080000u;
}
void TestConflictingSymbolNames::clear_has_int32() {
  _has_bits_[0] &= ~0x00080000u;
}
void TestConflictingSymbolNames::clear_int32() {
  int32_ = 0;
  clear_has_int32();
}
::google::protobuf::int32 TestConflictingSymbolNames::int32() const {
  // @@protoc_insertion_point(field_get:protobuf_unittest.TestConflictingSymbolNames.int32)
  return int32_;
}
void TestConflictingSymbolNames::set_int32(::google::protobuf::int32 value) {
  set_has_int32();
  int32_ = value;
  // @@protoc_insertion_point(field_set:protobuf_unittest.TestConflictingSymbolNames.int32)
}

// optional int64 int64 = 21;
bool TestConflictingSymbolNames::has_int64() const {
  return (_has_bits_[0] & 0x00100000u) != 0;
}
void TestConflictingSymbolNames::set_has_int64() {
  _has_bits_[0] |= 0x00100000u;
}
void TestConflictingSymbolNames::clear_has_int64() {
  _has_bits_[0] &= ~0x00100000u;
}
void TestConflictingSymbolNames::clear_int64() {
  int64_ = GOOGLE_LONGLONG(0);
  clear_has_int64();
}
::google::protobuf::int64 TestConflictingSymbolNames::int64() const {
  // @@protoc_insertion_point(field_get:protobuf_unittest.TestConflictingSymbolNames.int64)
  return int64_;
}
void TestConflictingSymbolNames::set_int64(::google::protobuf::int64 value) {
  set_has_int64();
  int64_ = value;
  // @@protoc_insertion_point(field_set:protobuf_unittest.TestConflictingSymbolNames.int64)
}

// optional uint32 cached_size = 22;
bool TestConflictingSymbolNames::has_cached_size() const {
  return (_has_bits_[0] & 0x00200000u) != 0;
}
void TestConflictingSymbolNames::set_has_cached_size() {
  _has_bits_[0] |= 0x00200000u;
}
void TestConflictingSymbolNames::clear_has_cached_size() {
  _has_bits_[0] &= ~0x00200000u;
}
void TestConflictingSymbolNames::clear_cached_size() {
  cached_size_ = 0u;
  clear_has_cached_size();
}
::google::protobuf::uint32 TestConflictingSymbolNames::cached_size() const {
  // @@protoc_insertion_point(field_get:protobuf_unittest.TestConflictingSymbolNames.cached_size)
  return cached_size_;
}
void TestConflictingSymbolNames::set_cached_size(::google::protobuf::uint32 value) {
  set_has_cached_size();
  cached_size_ = value;
  // @@protoc_insertion_point(field_set:protobuf_unittest.TestConflictingSymbolNames.cached_size)
}

// optional uint32 extensions = 23;
bool TestConflictingSymbolNames::has_extensions() const {
  return (_has_bits_[0] & 0x00400000u) != 0;
}
void TestConflictingSymbolNames::set_has_extensions() {
  _has_bits_[0] |= 0x00400000u;
}
void TestConflictingSymbolNames::clear_has_extensions() {
  _has_bits_[0] &= ~0x00400000u;
}
void TestConflictingSymbolNames::clear_extensions() {
  extensions_ = 0u;
  clear_has_extensions();
}
::google::protobuf::uint32 TestConflictingSymbolNames::extensions() const {
  // @@protoc_insertion_point(field_get:protobuf_unittest.TestConflictingSymbolNames.extensions)
  return extensions_;
}
void TestConflictingSymbolNames::set_extensions(::google::protobuf::uint32 value) {
  set_has_extensions();
  extensions_ = value;
  // @@protoc_insertion_point(field_set:protobuf_unittest.TestConflictingSymbolNames.extensions)
}

// optional uint32 bit = 24;
bool TestConflictingSymbolNames::has_bit() const {
  return (_has_bits_[0] & 0x00800000u) != 0;
}
void TestConflictingSymbolNames::set_has_bit() {
  _has_bits_[0] |= 0x00800000u;
}
void TestConflictingSymbolNames::clear_has_bit() {
  _has_bits_[0] &= ~0x00800000u;
}
void TestConflictingSymbolNames::clear_bit() {
  bit_ = 0u;
  clear_has_bit();
}
::google::protobuf::uint32 TestConflictingSymbolNames::bit() const {
  // @@protoc_insertion_point(field_get:protobuf_unittest.TestConflictingSymbolNames.bit)
  return bit_;
}
void TestConflictingSymbolNames::set_bit(::google::protobuf::uint32 value) {
  set_has_bit();
  bit_ = value;
  // @@protoc_insertion_point(field_set:protobuf_unittest.TestConflictingSymbolNames.bit)
}

// optional uint32 bits = 25;
bool TestConflictingSymbolNames::has_bits() const {
  return (_has_bits_[0] & 0x01000000u) != 0;
}
void TestConflictingSymbolNames::set_has_bits() {
  _has_bits_[0] |= 0x01000000u;
}
void TestConflictingSymbolNames::clear_has_bits() {
  _has_bits_[0] &= ~0x01000000u;
}
void TestConflictingSymbolNames::clear_bits() {
  bits_ = 0u;
  clear_has_bits();
}
::google::protobuf::uint32 TestConflictingSymbolNames::bits() const {
  // @@protoc_insertion_point(field_get:protobuf_unittest.TestConflictingSymbolNames.bits)
  return bits_;
}
void TestConflictingSymbolNames::set_bits(::google::protobuf::uint32 value) {
  set_has_bits();
  bits_ = value;
  // @@protoc_insertion_point(field_set:protobuf_unittest.TestConflictingSymbolNames.bits)
}

// optional uint32 offsets = 26;
bool TestConflictingSymbolNames::has_offsets() const {
  return (_has_bits_[0] & 0x02000000u) != 0;
}
void TestConflictingSymbolNames::set_has_offsets() {
  _has_bits_[0] |= 0x02000000u;
}
void TestConflictingSymbolNames::clear_has_offsets() {
  _has_bits_[0] &= ~0x02000000u;
}
void TestConflictingSymbolNames::clear_offsets() {
  offsets_ = 0u;
  clear_has_offsets();
}
::google::protobuf::uint32 TestConflictingSymbolNames::offsets() const {
  // @@protoc_insertion_point(field_get:protobuf_unittest.TestConflictingSymbolNames.offsets)
  return offsets_;
}
void TestConflictingSymbolNames::set_offsets(::google::protobuf::uint32 value) {
  set_has_offsets();
  offsets_ = value;
  // @@protoc_insertion_point(field_set:protobuf_unittest.TestConflictingSymbolNames.offsets)
}

// optional uint32 reflection = 27;
bool TestConflictingSymbolNames::has_reflection() const {
  return (_has_bits_[0] & 0x04000000u) != 0;
}
void TestConflictingSymbolNames::set_has_reflection() {
  _has_bits_[0] |= 0x04000000u;
}
void TestConflictingSymbolNames::clear_has_reflection() {
  _has_bits_[0] &= ~0x04000000u;
}
void TestConflictingSymbolNames::clear_reflection() {
  reflection_ = 0u;
  clear_has_reflection();
}
::google::protobuf::uint32 TestConflictingSymbolNames::reflection() const {
  // @@protoc_insertion_point(field_get:protobuf_unittest.TestConflictingSymbolNames.reflection)
  return reflection_;
}
void TestConflictingSymbolNames::set_reflection(::google::protobuf::uint32 value) {
  set_has_reflection();
  reflection_ = value;
  // @@protoc_insertion_point(field_set:protobuf_unittest.TestConflictingSymbolNames.reflection)
}

// optional string some_cord = 28 [ctype = CORD];
bool TestConflictingSymbolNames::has_some_cord() const {
  return (_has_bits_[0] & 0x08000000u) != 0;
}
void TestConflictingSymbolNames::set_has_some_cord() {
  _has_bits_[0] |= 0x08000000u;
}
void TestConflictingSymbolNames::clear_has_some_cord() {
  _has_bits_[0] &= ~0x08000000u;
}
void TestConflictingSymbolNames::clear_some_cord() {
  some_cord_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  clear_has_some_cord();
}
const ::std::string& TestConflictingSymbolNames::some_cord() const {
  // @@protoc_insertion_point(field_get:protobuf_unittest.TestConflictingSymbolNames.some_cord)
  return some_cord_.GetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
void TestConflictingSymbolNames::set_some_cord(const ::std::string& value) {
  set_has_some_cord();
  some_cord_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:protobuf_unittest.TestConflictingSymbolNames.some_cord)
}
void TestConflictingSymbolNames::set_some_cord(const char* value) {
  set_has_some_cord();
  some_cord_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:protobuf_unittest.TestConflictingSymbolNames.some_cord)
}
void TestConflictingSymbolNames::set_some_cord(const char* value, size_t size) {
  set_has_some_cord();
  some_cord_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:protobuf_unittest.TestConflictingSymbolNames.some_cord)
}
::std::string* TestConflictingSymbolNames::mutable_some_cord() {
  set_has_some_cord();
  // @@protoc_insertion_point(field_mutable:protobuf_unittest.TestConflictingSymbolNames.some_cord)
  return some_cord_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
::std::string* TestConflictingSymbolNames::release_some_cord() {
  // @@protoc_insertion_point(field_release:protobuf_unittest.TestConflictingSymbolNames.some_cord)
  clear_has_some_cord();
  return some_cord_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
void TestConflictingSymbolNames::set_allocated_some_cord(::std::string* some_cord) {
  if (some_cord != NULL) {
    set_has_some_cord();
  } else {
    clear_has_some_cord();
  }
  some_cord_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), some_cord);
  // @@protoc_insertion_point(field_set_allocated:protobuf_unittest.TestConflictingSymbolNames.some_cord)
}

// optional string some_string_piece = 29 [ctype = STRING_PIECE];
bool TestConflictingSymbolNames::has_some_string_piece() const {
  return (_has_bits_[0] & 0x10000000u) != 0;
}
void TestConflictingSymbolNames::set_has_some_string_piece() {
  _has_bits_[0] |= 0x10000000u;
}
void TestConflictingSymbolNames::clear_has_some_string_piece() {
  _has_bits_[0] &= ~0x10000000u;
}
void TestConflictingSymbolNames::clear_some_string_piece() {
  some_string_piece_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  clear_has_some_string_piece();
}
const ::std::string& TestConflictingSymbolNames::some_string_piece() const {
  // @@protoc_insertion_point(field_get:protobuf_unittest.TestConflictingSymbolNames.some_string_piece)
  return some_string_piece_.GetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
void TestConflictingSymbolNames::set_some_string_piece(const ::std::string& value) {
  set_has_some_string_piece();
  some_string_piece_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:protobuf_unittest.TestConflictingSymbolNames.some_string_piece)
}
void TestConflictingSymbolNames::set_some_string_piece(const char* value) {
  set_has_some_string_piece();
  some_string_piece_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:protobuf_unittest.TestConflictingSymbolNames.some_string_piece)
}
void TestConflictingSymbolNames::set_some_string_piece(const char* value, size_t size) {
  set_has_some_string_piece();
  some_string_piece_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:protobuf_unittest.TestConflictingSymbolNames.some_string_piece)
}
::std::string* TestConflictingSymbolNames::mutable_some_string_piece() {
  set_has_some_string_piece();
  // @@protoc_insertion_point(field_mutable:protobuf_unittest.TestConflictingSymbolNames.some_string_piece)
  return some_string_piece_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
::std::string* TestConflictingSymbolNames::release_some_string_piece() {
  // @@protoc_insertion_point(field_release:protobuf_unittest.TestConflictingSymbolNames.some_string_piece)
  clear_has_some_string_piece();
  return some_string_piece_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
void TestConflictingSymbolNames::set_allocated_some_string_piece(::std::string* some_string_piece) {
  if (some_string_piece != NULL) {
    set_has_some_string_piece();
  } else {
    clear_has_some_string_piece();
  }
  some_string_piece_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), some_string_piece);
  // @@protoc_insertion_point(field_set_allocated:protobuf_unittest.TestConflictingSymbolNames.some_string_piece)
}

// optional uint32 int = 30;
bool TestConflictingSymbolNames::has_int_() const {
  return (_has_bits_[0] & 0x20000000u) != 0;
}
void TestConflictingSymbolNames::set_has_int_() {
  _has_bits_[0] |= 0x20000000u;
}
void TestConflictingSymbolNames::clear_has_int_() {
  _has_bits_[0] &= ~0x20000000u;
}
void TestConflictingSymbolNames::clear_int_() {
  int__ = 0u;
  clear_has_int_();
}
::google::protobuf::uint32 TestConflictingSymbolNames::int_() const {
  // @@protoc_insertion_point(field_get:protobuf_unittest.TestConflictingSymbolNames.int)
  return int__;
}
void TestConflictingSymbolNames::set_int_(::google::protobuf::uint32 value) {
  set_has_int_();
  int__ = value;
  // @@protoc_insertion_point(field_set:protobuf_unittest.TestConflictingSymbolNames.int)
}

// optional uint32 friend = 31;
bool TestConflictingSymbolNames::has_friend_() const {
  return (_has_bits_[0] & 0x40000000u) != 0;
}
void TestConflictingSymbolNames::set_has_friend_() {
  _has_bits_[0] |= 0x40000000u;
}
void TestConflictingSymbolNames::clear_has_friend_() {
  _has_bits_[0] &= ~0x40000000u;
}
void TestConflictingSymbolNames::clear_friend_() {
  friend__ = 0u;
  clear_has_friend_();
}
::google::protobuf::uint32 TestConflictingSymbolNames::friend_() const {
  // @@protoc_insertion_point(field_get:protobuf_unittest.TestConflictingSymbolNames.friend)
  return friend__;
}
void TestConflictingSymbolNames::set_friend_(::google::protobuf::uint32 value) {
  set_has_friend_();
  friend__ = value;
  // @@protoc_insertion_point(field_set:protobuf_unittest.TestConflictingSymbolNames.friend)
}

// optional uint32 class = 37;
bool TestConflictingSymbolNames::has_class_() const {
  return (_has_bits_[0] & 0x80000000u) != 0;
}
void TestConflictingSymbolNames::set_has_class_() {
  _has_bits_[0] |= 0x80000000u;
}
void TestConflictingSymbolNames::clear_has_class_() {
  _has_bits_[0] &= ~0x80000000u;
}
void TestConflictingSymbolNames::clear_class_() {
  class__ = 0u;
  clear_has_class_();
}
::google::protobuf::uint32 TestConflictingSymbolNames::class_() const {
  // @@protoc_insertion_point(field_get:protobuf_unittest.TestConflictingSymbolNames.class)
  return class__;
}
void TestConflictingSymbolNames::set_class_(::google::protobuf::uint32 value) {
  set_has_class_();
  class__ = value;
  // @@protoc_insertion_point(field_set:protobuf_unittest.TestConflictingSymbolNames.class)
}

// optional uint32 typedecl = 39;
bool TestConflictingSymbolNames::has_typedecl() const {
  return (_has_bits_[1] & 0x00000001u) != 0;
}
void TestConflictingSymbolNames::set_has_typedecl() {
  _has_bits_[1] |= 0x00000001u;
}
void TestConflictingSymbolNames::clear_has_typedecl() {
  _has_bits_[1] &= ~0x00000001u;
}
void TestConflictingSymbolNames::clear_typedecl() {
  typedecl_ = 0u;
  clear_has_typedecl();
}
::google::protobuf::uint32 TestConflictingSymbolNames::typedecl() const {
  // @@protoc_insertion_point(field_get:protobuf_unittest.TestConflictingSymbolNames.typedecl)
  return typedecl_;
}
void TestConflictingSymbolNames::set_typedecl(::google::protobuf::uint32 value) {
  set_has_typedecl();
  typedecl_ = value;
  // @@protoc_insertion_point(field_set:protobuf_unittest.TestConflictingSymbolNames.typedecl)
}

// optional uint32 auto = 40;
bool TestConflictingSymbolNames::has_auto_() const {
  return (_has_bits_[1] & 0x00000002u) != 0;
}
void TestConflictingSymbolNames::set_has_auto_() {
  _has_bits_[1] |= 0x00000002u;
}
void TestConflictingSymbolNames::clear_has_auto_() {
  _has_bits_[1] &= ~0x00000002u;
}
void TestConflictingSymbolNames::clear_auto_() {
  auto__ = 0u;
  clear_has_auto_();
}
::google::protobuf::uint32 TestConflictingSymbolNames::auto_() const {
  // @@protoc_insertion_point(field_get:protobuf_unittest.TestConflictingSymbolNames.auto)
  return auto__;
}
void TestConflictingSymbolNames::set_auto_(::google::protobuf::uint32 value) {
  set_has_auto_();
  auto__ = value;
  // @@protoc_insertion_point(field_set:protobuf_unittest.TestConflictingSymbolNames.auto)
}

// optional .protobuf_unittest.TestConflictingSymbolNames.DO do = 32;
bool TestConflictingSymbolNames::has_do_() const {
  return (_has_bits_[1] & 0x00000004u) != 0;
}
void TestConflictingSymbolNames::set_has_do_() {
  _has_bits_[1] |= 0x00000004u;
}
void TestConflictingSymbolNames::clear_has_do_() {
  _has_bits_[1] &= ~0x00000004u;
}
void TestConflictingSymbolNames::clear_do_() {
  if (do__ != NULL) do__->::protobuf_unittest::TestConflictingSymbolNames_DO::Clear();
  clear_has_do_();
}
const ::protobuf_unittest::TestConflictingSymbolNames_DO& TestConflictingSymbolNames::do_() const {
  // @@protoc_insertion_point(field_get:protobuf_unittest.TestConflictingSymbolNames.do)
  return do__ != NULL ? *do__
                         : *::protobuf_unittest::TestConflictingSymbolNames_DO::internal_default_instance();
}
::protobuf_unittest::TestConflictingSymbolNames_DO* TestConflictingSymbolNames::mutable_do_() {
  set_has_do_();
  if (do__ == NULL) {
    do__ = new ::protobuf_unittest::TestConflictingSymbolNames_DO;
  }
  // @@protoc_insertion_point(field_mutable:protobuf_unittest.TestConflictingSymbolNames.do)
  return do__;
}
::protobuf_unittest::TestConflictingSymbolNames_DO* TestConflictingSymbolNames::release_do__() {
  // @@protoc_insertion_point(field_release:protobuf_unittest.TestConflictingSymbolNames.do)
  clear_has_do_();
  ::protobuf_unittest::TestConflictingSymbolNames_DO* temp = do__;
  do__ = NULL;
  return temp;
}
void TestConflictingSymbolNames::set_allocated_do_(::protobuf_unittest::TestConflictingSymbolNames_DO* do_) {
  delete do__;
  do__ = do_;
  if (do_) {
    set_has_do_();
  } else {
    clear_has_do_();
  }
  // @@protoc_insertion_point(field_set_allocated:protobuf_unittest.TestConflictingSymbolNames.do)
}

// optional int32 field_type = 33;
bool TestConflictingSymbolNames::has_field_type() const {
  return (_has_bits_[1] & 0x00000008u) != 0;
}
void TestConflictingSymbolNames::set_has_field_type() {
  _has_bits_[1] |= 0x00000008u;
}
void TestConflictingSymbolNames::clear_has_field_type() {
  _has_bits_[1] &= ~0x00000008u;
}
void TestConflictingSymbolNames::clear_field_type() {
  field_type_ = 0;
  clear_has_field_type();
}
::google::protobuf::int32 TestConflictingSymbolNames::field_type() const {
  // @@protoc_insertion_point(field_get:protobuf_unittest.TestConflictingSymbolNames.field_type)
  return field_type_;
}
void TestConflictingSymbolNames::set_field_type(::google::protobuf::int32 value) {
  set_has_field_type();
  field_type_ = value;
  // @@protoc_insertion_point(field_set:protobuf_unittest.TestConflictingSymbolNames.field_type)
}

// optional bool is_packed = 34;
bool TestConflictingSymbolNames::has_is_packed() const {
  return (_has_bits_[1] & 0x00000010u) != 0;
}
void TestConflictingSymbolNames::set_has_is_packed() {
  _has_bits_[1] |= 0x00000010u;
}
void TestConflictingSymbolNames::clear_has_is_packed() {
  _has_bits_[1] &= ~0x00000010u;
}
void TestConflictingSymbolNames::clear_is_packed() {
  is_packed_ = false;
  clear_has_is_packed();
}
bool TestConflictingSymbolNames::is_packed() const {
  // @@protoc_insertion_point(field_get:protobuf_unittest.TestConflictingSymbolNames.is_packed)
  return is_packed_;
}
void TestConflictingSymbolNames::set_is_packed(bool value) {
  set_has_is_packed();
  is_packed_ = value;
  // @@protoc_insertion_point(field_set:protobuf_unittest.TestConflictingSymbolNames.is_packed)
}

// optional string release_length = 35;
bool TestConflictingSymbolNames::has_release_length() const {
  return (_has_bits_[1] & 0x00000020u) != 0;
}
void TestConflictingSymbolNames::set_has_release_length() {
  _has_bits_[1] |= 0x00000020u;
}
void TestConflictingSymbolNames::clear_has_release_length() {
  _has_bits_[1] &= ~0x00000020u;
}
void TestConflictingSymbolNames::clear_release_length() {
  release_length_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  clear_has_release_length();
}
const ::std::string& TestConflictingSymbolNames::release_length() const {
  // @@protoc_insertion_point(field_get:protobuf_unittest.TestConflictingSymbolNames.release_length)
  return release_length_.GetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
void TestConflictingSymbolNames::set_release_length(const ::std::string& value) {
  set_has_release_length();
  release_length_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:protobuf_unittest.TestConflictingSymbolNames.release_length)
}
void TestConflictingSymbolNames::set_release_length(const char* value) {
  set_has_release_length();
  release_length_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:protobuf_unittest.TestConflictingSymbolNames.release_length)
}
void TestConflictingSymbolNames::set_release_length(const char* value, size_t size) {
  set_has_release_length();
  release_length_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:protobuf_unittest.TestConflictingSymbolNames.release_length)
}
::std::string* TestConflictingSymbolNames::mutable_release_length() {
  set_has_release_length();
  // @@protoc_insertion_point(field_mutable:protobuf_unittest.TestConflictingSymbolNames.release_length)
  return release_length_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
::std::string* TestConflictingSymbolNames::release_release_length() {
  // @@protoc_insertion_point(field_release:protobuf_unittest.TestConflictingSymbolNames.release_length)
  clear_has_release_length();
  return release_length_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
void TestConflictingSymbolNames::set_allocated_release_length(::std::string* release_length) {
  if (release_length != NULL) {
    set_has_release_length();
  } else {
    clear_has_release_length();
  }
  release_length_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), release_length);
  // @@protoc_insertion_point(field_set_allocated:protobuf_unittest.TestConflictingSymbolNames.release_length)
}

// optional .protobuf_unittest.TestConflictingSymbolNames.DO release_do = 36;
bool TestConflictingSymbolNames::has_release_do() const {
  return (_has_bits_[1] & 0x00000040u) != 0;
}
void TestConflictingSymbolNames::set_has_release_do() {
  _has_bits_[1] |= 0x00000040u;
}
void TestConflictingSymbolNames::clear_has_release_do() {
  _has_bits_[1] &= ~0x00000040u;
}
void TestConflictingSymbolNames::clear_release_do() {
  if (release_do_ != NULL) release_do_->::protobuf_unittest::TestConflictingSymbolNames_DO::Clear();
  clear_has_release_do();
}
const ::protobuf_unittest::TestConflictingSymbolNames_DO& TestConflictingSymbolNames::release_do() const {
  // @@protoc_insertion_point(field_get:protobuf_unittest.TestConflictingSymbolNames.release_do)
  return release_do_ != NULL ? *release_do_
                         : *::protobuf_unittest::TestConflictingSymbolNames_DO::internal_default_instance();
}
::protobuf_unittest::TestConflictingSymbolNames_DO* TestConflictingSymbolNames::mutable_release_do() {
  set_has_release_do();
  if (release_do_ == NULL) {
    release_do_ = new ::protobuf_unittest::TestConflictingSymbolNames_DO;
  }
  // @@protoc_insertion_point(field_mutable:protobuf_unittest.TestConflictingSymbolNames.release_do)
  return release_do_;
}
::protobuf_unittest::TestConflictingSymbolNames_DO* TestConflictingSymbolNames::release_release_do() {
  // @@protoc_insertion_point(field_release:protobuf_unittest.TestConflictingSymbolNames.release_do)
  clear_has_release_do();
  ::protobuf_unittest::TestConflictingSymbolNames_DO* temp = release_do_;
  release_do_ = NULL;
  return temp;
}
void TestConflictingSymbolNames::set_allocated_release_do(::protobuf_unittest::TestConflictingSymbolNames_DO* release_do) {
  delete release_do_;
  release_do_ = release_do;
  if (release_do) {
    set_has_release_do();
  } else {
    clear_has_release_do();
  }
  // @@protoc_insertion_point(field_set_allocated:protobuf_unittest.TestConflictingSymbolNames.release_do)
}

// optional string target = 38;
bool TestConflictingSymbolNames::has_target() const {
  return (_has_bits_[1] & 0x00000080u) != 0;
}
void TestConflictingSymbolNames::set_has_target() {
  _has_bits_[1] |= 0x00000080u;
}
void TestConflictingSymbolNames::clear_has_target() {
  _has_bits_[1] &= ~0x00000080u;
}
void TestConflictingSymbolNames::clear_target() {
  target_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  clear_has_target();
}
const ::std::string& TestConflictingSymbolNames::target() const {
  // @@protoc_insertion_point(field_get:protobuf_unittest.TestConflictingSymbolNames.target)
  return target_.GetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
void TestConflictingSymbolNames::set_target(const ::std::string& value) {
  set_has_target();
  target_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:protobuf_unittest.TestConflictingSymbolNames.target)
}
void TestConflictingSymbolNames::set_target(const char* value) {
  set_has_target();
  target_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:protobuf_unittest.TestConflictingSymbolNames.target)
}
void TestConflictingSymbolNames::set_target(const char* value, size_t size) {
  set_has_target();
  target_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:protobuf_unittest.TestConflictingSymbolNames.target)
}
::std::string* TestConflictingSymbolNames::mutable_target() {
  set_has_target();
  // @@protoc_insertion_point(field_mutable:protobuf_unittest.TestConflictingSymbolNames.target)
  return target_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
::std::string* TestConflictingSymbolNames::release_target() {
  // @@protoc_insertion_point(field_release:protobuf_unittest.TestConflictingSymbolNames.target)
  clear_has_target();
  return target_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
void TestConflictingSymbolNames::set_allocated_target(::std::string* target) {
  if (target != NULL) {
    set_has_target();
  } else {
    clear_has_target();
  }
  target_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), target);
  // @@protoc_insertion_point(field_set_allocated:protobuf_unittest.TestConflictingSymbolNames.target)
}

inline const TestConflictingSymbolNames* TestConflictingSymbolNames::internal_default_instance() {
  return &TestConflictingSymbolNames_default_instance_.get();
}
#endif  // PROTOBUF_INLINE_NOT_IN_HEADERS

// ===================================================================

#if !defined(_MSC_VER) || _MSC_VER >= 1900
#endif  // !defined(_MSC_VER) || _MSC_VER >= 1900

#if !defined(_MSC_VER) || _MSC_VER >= 1900
const int TestConflictingSymbolNamesExtension::kRepeatedInt32ExtFieldNumber;
#endif
::google::protobuf::internal::ExtensionIdentifier< ::protobuf_unittest::TestConflictingSymbolNames,
    ::google::protobuf::internal::RepeatedPrimitiveTypeTraits< ::google::protobuf::int32 >, 5, true >
  TestConflictingSymbolNamesExtension::repeated_int32_ext(kRepeatedInt32ExtFieldNumber, 0);
TestConflictingSymbolNamesExtension::TestConflictingSymbolNamesExtension()
  : ::google::protobuf::Message(), _internal_metadata_(NULL) {
  if (this != internal_default_instance()) protobuf_InitDefaults_google_2fprotobuf_2fcompiler_2fcpp_2fcpp_5ftest_5fbad_5fidentifiers_2eproto();
  SharedCtor();
  // @@protoc_insertion_point(constructor:protobuf_unittest.TestConflictingSymbolNamesExtension)
}

void TestConflictingSymbolNamesExtension::InitAsDefaultInstance() {
}

TestConflictingSymbolNamesExtension::TestConflictingSymbolNamesExtension(const TestConflictingSymbolNamesExtension& from)
  : ::google::protobuf::Message(),
    _internal_metadata_(NULL) {
  SharedCtor();
  UnsafeMergeFrom(from);
  // @@protoc_insertion_point(copy_constructor:protobuf_unittest.TestConflictingSymbolNamesExtension)
}

void TestConflictingSymbolNamesExtension::SharedCtor() {
  _cached_size_ = 0;
}

TestConflictingSymbolNamesExtension::~TestConflictingSymbolNamesExtension() {
  // @@protoc_insertion_point(destructor:protobuf_unittest.TestConflictingSymbolNamesExtension)
  SharedDtor();
}

void TestConflictingSymbolNamesExtension::SharedDtor() {
}

void TestConflictingSymbolNamesExtension::SetCachedSize(int size) const {
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
}
const ::google::protobuf::Descriptor* TestConflictingSymbolNamesExtension::descriptor() {
  protobuf_AssignDescriptorsOnce();
  return TestConflictingSymbolNamesExtension_descriptor_;
}

const TestConflictingSymbolNamesExtension& TestConflictingSymbolNamesExtension::default_instance() {
  protobuf_InitDefaults_google_2fprotobuf_2fcompiler_2fcpp_2fcpp_5ftest_5fbad_5fidentifiers_2eproto();
  return *internal_default_instance();
}

::google::protobuf::internal::ExplicitlyConstructed<TestConflictingSymbolNamesExtension> TestConflictingSymbolNamesExtension_default_instance_;

TestConflictingSymbolNamesExtension* TestConflictingSymbolNamesExtension::New(::google::protobuf::Arena* arena) const {
  TestConflictingSymbolNamesExtension* n = new TestConflictingSymbolNamesExtension;
  if (arena != NULL) {
    arena->Own(n);
  }
  return n;
}

void TestConflictingSymbolNamesExtension::Clear() {
// @@protoc_insertion_point(message_clear_start:protobuf_unittest.TestConflictingSymbolNamesExtension)
  _has_bits_.Clear();
  if (_internal_metadata_.have_unknown_fields()) {
    mutable_unknown_fields()->Clear();
  }
}

bool TestConflictingSymbolNamesExtension::MergePartialFromCodedStream(
    ::google::protobuf::io::CodedInputStream* input) {
#define DO_(EXPRESSION) if (!GOOGLE_PREDICT_TRUE(EXPRESSION)) goto failure
  ::google::protobuf::uint32 tag;
  // @@protoc_insertion_point(parse_start:protobuf_unittest.TestConflictingSymbolNamesExtension)
  for (;;) {
    ::std::pair< ::google::protobuf::uint32, bool> p = input->ReadTagWithCutoff(127);
    tag = p.first;
    if (!p.second) goto handle_unusual;
  handle_unusual:
    if (tag == 0 ||
        ::google::protobuf::internal::WireFormatLite::GetTagWireType(tag) ==
        ::google::protobuf::internal::WireFormatLite::WIRETYPE_END_GROUP) {
      goto success;
    }
    DO_(::google::protobuf::internal::WireFormat::SkipField(
          input, tag, mutable_unknown_fields()));
  }
success:
  // @@protoc_insertion_point(parse_success:protobuf_unittest.TestConflictingSymbolNamesExtension)
  return true;
failure:
  // @@protoc_insertion_point(parse_failure:protobuf_unittest.TestConflictingSymbolNamesExtension)
  return false;
#undef DO_
}

void TestConflictingSymbolNamesExtension::SerializeWithCachedSizes(
    ::google::protobuf::io::CodedOutputStream* output) const {
  // @@protoc_insertion_point(serialize_start:protobuf_unittest.TestConflictingSymbolNamesExtension)
  if (_internal_metadata_.have_unknown_fields()) {
    ::google::protobuf::internal::WireFormat::SerializeUnknownFields(
        unknown_fields(), output);
  }
  // @@protoc_insertion_point(serialize_end:protobuf_unittest.TestConflictingSymbolNamesExtension)
}

::google::protobuf::uint8* TestConflictingSymbolNamesExtension::InternalSerializeWithCachedSizesToArray(
    bool deterministic, ::google::protobuf::uint8* target) const {
  (void)deterministic; // Unused
  // @@protoc_insertion_point(serialize_to_array_start:protobuf_unittest.TestConflictingSymbolNamesExtension)
  if (_internal_metadata_.have_unknown_fields()) {
    target = ::google::protobuf::internal::WireFormat::SerializeUnknownFieldsToArray(
        unknown_fields(), target);
  }
  // @@protoc_insertion_point(serialize_to_array_end:protobuf_unittest.TestConflictingSymbolNamesExtension)
  return target;
}

size_t TestConflictingSymbolNamesExtension::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:protobuf_unittest.TestConflictingSymbolNamesExtension)
  size_t total_size = 0;

  if (_internal_metadata_.have_unknown_fields()) {
    total_size +=
      ::google::protobuf::internal::WireFormat::ComputeUnknownFieldsSize(
        unknown_fields());
  }
  int cached_size = ::google::protobuf::internal::ToCachedSize(total_size);
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = cached_size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
  return total_size;
}

void TestConflictingSymbolNamesExtension::MergeFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_merge_from_start:protobuf_unittest.TestConflictingSymbolNamesExtension)
  if (GOOGLE_PREDICT_FALSE(&from == this)) MergeFromFail(__LINE__);
  const TestConflictingSymbolNamesExtension* source =
      ::google::protobuf::internal::DynamicCastToGenerated<const TestConflictingSymbolNamesExtension>(
          &from);
  if (source == NULL) {
  // @@protoc_insertion_point(generalized_merge_from_cast_fail:protobuf_unittest.TestConflictingSymbolNamesExtension)
    ::google::protobuf::internal::ReflectionOps::Merge(from, this);
  } else {
  // @@protoc_insertion_point(generalized_merge_from_cast_success:protobuf_unittest.TestConflictingSymbolNamesExtension)
    UnsafeMergeFrom(*source);
  }
}

void TestConflictingSymbolNamesExtension::MergeFrom(const TestConflictingSymbolNamesExtension& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:protobuf_unittest.TestConflictingSymbolNamesExtension)
  if (GOOGLE_PREDICT_TRUE(&from != this)) {
    UnsafeMergeFrom(from);
  } else {
    MergeFromFail(__LINE__);
  }
}

void TestConflictingSymbolNamesExtension::UnsafeMergeFrom(const TestConflictingSymbolNamesExtension& from) {
  GOOGLE_DCHECK(&from != this);
  if (from._internal_metadata_.have_unknown_fields()) {
    ::google::protobuf::UnknownFieldSet::MergeToInternalMetdata(
      from.unknown_fields(), &_internal_metadata_);
  }
}

void TestConflictingSymbolNamesExtension::CopyFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_copy_from_start:protobuf_unittest.TestConflictingSymbolNamesExtension)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

void TestConflictingSymbolNamesExtension::CopyFrom(const TestConflictingSymbolNamesExtension& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:protobuf_unittest.TestConflictingSymbolNamesExtension)
  if (&from == this) return;
  Clear();
  UnsafeMergeFrom(from);
}

bool TestConflictingSymbolNamesExtension::IsInitialized() const {

  return true;
}

void TestConflictingSymbolNamesExtension::Swap(TestConflictingSymbolNamesExtension* other) {
  if (other == this) return;
  InternalSwap(other);
}
void TestConflictingSymbolNamesExtension::InternalSwap(TestConflictingSymbolNamesExtension* other) {
  _internal_metadata_.Swap(&other->_internal_metadata_);
  std::swap(_cached_size_, other->_cached_size_);
}

::google::protobuf::Metadata TestConflictingSymbolNamesExtension::GetMetadata() const {
  protobuf_AssignDescriptorsOnce();
  ::google::protobuf::Metadata metadata;
  metadata.descriptor = TestConflictingSymbolNamesExtension_descriptor_;
  metadata.reflection = TestConflictingSymbolNamesExtension_reflection_;
  return metadata;
}

#if PROTOBUF_INLINE_NOT_IN_HEADERS
// TestConflictingSymbolNamesExtension

inline const TestConflictingSymbolNamesExtension* TestConflictingSymbolNamesExtension::internal_default_instance() {
  return &TestConflictingSymbolNamesExtension_default_instance_.get();
}
#endif  // PROTOBUF_INLINE_NOT_IN_HEADERS

// ===================================================================

const ::google::protobuf::EnumDescriptor* TestConflictingEnumNames_NestedConflictingEnum_descriptor() {
  protobuf_AssignDescriptorsOnce();
  return TestConflictingEnumNames_NestedConflictingEnum_descriptor_;
}
bool TestConflictingEnumNames_NestedConflictingEnum_IsValid(int value) {
  switch (value) {
    case 1:
    case 2:
    case 3:
    case 4:
    case 5:
      return true;
    default:
      return false;
  }
}

#if !defined(_MSC_VER) || _MSC_VER >= 1900
const TestConflictingEnumNames_NestedConflictingEnum TestConflictingEnumNames::and_;
const TestConflictingEnumNames_NestedConflictingEnum TestConflictingEnumNames::class_;
const TestConflictingEnumNames_NestedConflictingEnum TestConflictingEnumNames::int_;
const TestConflictingEnumNames_NestedConflictingEnum TestConflictingEnumNames::typedef_;
const TestConflictingEnumNames_NestedConflictingEnum TestConflictingEnumNames::XOR;
const TestConflictingEnumNames_NestedConflictingEnum TestConflictingEnumNames::NestedConflictingEnum_MIN;
const TestConflictingEnumNames_NestedConflictingEnum TestConflictingEnumNames::NestedConflictingEnum_MAX;
const int TestConflictingEnumNames::NestedConflictingEnum_ARRAYSIZE;
#endif  // !defined(_MSC_VER) || _MSC_VER >= 1900
#if !defined(_MSC_VER) || _MSC_VER >= 1900
const int TestConflictingEnumNames::kConflictingEnumFieldNumber;
#endif  // !defined(_MSC_VER) || _MSC_VER >= 1900

TestConflictingEnumNames::TestConflictingEnumNames()
  : ::google::protobuf::Message(), _internal_metadata_(NULL) {
  if (this != internal_default_instance()) protobuf_InitDefaults_google_2fprotobuf_2fcompiler_2fcpp_2fcpp_5ftest_5fbad_5fidentifiers_2eproto();
  SharedCtor();
  // @@protoc_insertion_point(constructor:protobuf_unittest.TestConflictingEnumNames)
}

void TestConflictingEnumNames::InitAsDefaultInstance() {
}

TestConflictingEnumNames::TestConflictingEnumNames(const TestConflictingEnumNames& from)
  : ::google::protobuf::Message(),
    _internal_metadata_(NULL) {
  SharedCtor();
  UnsafeMergeFrom(from);
  // @@protoc_insertion_point(copy_constructor:protobuf_unittest.TestConflictingEnumNames)
}

void TestConflictingEnumNames::SharedCtor() {
  _cached_size_ = 0;
  conflicting_enum_ = 1;
}

TestConflictingEnumNames::~TestConflictingEnumNames() {
  // @@protoc_insertion_point(destructor:protobuf_unittest.TestConflictingEnumNames)
  SharedDtor();
}

void TestConflictingEnumNames::SharedDtor() {
}

void TestConflictingEnumNames::SetCachedSize(int size) const {
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
}
const ::google::protobuf::Descriptor* TestConflictingEnumNames::descriptor() {
  protobuf_AssignDescriptorsOnce();
  return TestConflictingEnumNames_descriptor_;
}

const TestConflictingEnumNames& TestConflictingEnumNames::default_instance() {
  protobuf_InitDefaults_google_2fprotobuf_2fcompiler_2fcpp_2fcpp_5ftest_5fbad_5fidentifiers_2eproto();
  return *internal_default_instance();
}

::google::protobuf::internal::ExplicitlyConstructed<TestConflictingEnumNames> TestConflictingEnumNames_default_instance_;

TestConflictingEnumNames* TestConflictingEnumNames::New(::google::protobuf::Arena* arena) const {
  TestConflictingEnumNames* n = new TestConflictingEnumNames;
  if (arena != NULL) {
    arena->Own(n);
  }
  return n;
}

void TestConflictingEnumNames::Clear() {
// @@protoc_insertion_point(message_clear_start:protobuf_unittest.TestConflictingEnumNames)
  conflicting_enum_ = 1;
  _has_bits_.Clear();
  if (_internal_metadata_.have_unknown_fields()) {
    mutable_unknown_fields()->Clear();
  }
}

bool TestConflictingEnumNames::MergePartialFromCodedStream(
    ::google::protobuf::io::CodedInputStream* input) {
#define DO_(EXPRESSION) if (!GOOGLE_PREDICT_TRUE(EXPRESSION)) goto failure
  ::google::protobuf::uint32 tag;
  // @@protoc_insertion_point(parse_start:protobuf_unittest.TestConflictingEnumNames)
  for (;;) {
    ::std::pair< ::google::protobuf::uint32, bool> p = input->ReadTagWithCutoff(127);
    tag = p.first;
    if (!p.second) goto handle_unusual;
    switch (::google::protobuf::internal::WireFormatLite::GetTagFieldNumber(tag)) {
      // optional .protobuf_unittest.TestConflictingEnumNames.NestedConflictingEnum conflicting_enum = 1;
      case 1: {
        if (tag == 8) {
          int value;
          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   int, ::google::protobuf::internal::WireFormatLite::TYPE_ENUM>(
                 input, &value)));
          if (::protobuf_unittest::TestConflictingEnumNames_NestedConflictingEnum_IsValid(value)) {
            set_conflicting_enum(static_cast< ::protobuf_unittest::TestConflictingEnumNames_NestedConflictingEnum >(value));
          } else {
            mutable_unknown_fields()->AddVarint(1, value);
          }
        } else {
          goto handle_unusual;
        }
        if (input->ExpectAtEnd()) goto success;
        break;
      }

      default: {
      handle_unusual:
        if (tag == 0 ||
            ::google::protobuf::internal::WireFormatLite::GetTagWireType(tag) ==
            ::google::protobuf::internal::WireFormatLite::WIRETYPE_END_GROUP) {
          goto success;
        }
        DO_(::google::protobuf::internal::WireFormat::SkipField(
              input, tag, mutable_unknown_fields()));
        break;
      }
    }
  }
success:
  // @@protoc_insertion_point(parse_success:protobuf_unittest.TestConflictingEnumNames)
  return true;
failure:
  // @@protoc_insertion_point(parse_failure:protobuf_unittest.TestConflictingEnumNames)
  return false;
#undef DO_
}

void TestConflictingEnumNames::SerializeWithCachedSizes(
    ::google::protobuf::io::CodedOutputStream* output) const {
  // @@protoc_insertion_point(serialize_start:protobuf_unittest.TestConflictingEnumNames)
  // optional .protobuf_unittest.TestConflictingEnumNames.NestedConflictingEnum conflicting_enum = 1;
  if (has_conflicting_enum()) {
    ::google::protobuf::internal::WireFormatLite::WriteEnum(
      1, this->conflicting_enum(), output);
  }

  if (_internal_metadata_.have_unknown_fields()) {
    ::google::protobuf::internal::WireFormat::SerializeUnknownFields(
        unknown_fields(), output);
  }
  // @@protoc_insertion_point(serialize_end:protobuf_unittest.TestConflictingEnumNames)
}

::google::protobuf::uint8* TestConflictingEnumNames::InternalSerializeWithCachedSizesToArray(
    bool deterministic, ::google::protobuf::uint8* target) const {
  (void)deterministic; // Unused
  // @@protoc_insertion_point(serialize_to_array_start:protobuf_unittest.TestConflictingEnumNames)
  // optional .protobuf_unittest.TestConflictingEnumNames.NestedConflictingEnum conflicting_enum = 1;
  if (has_conflicting_enum()) {
    target = ::google::protobuf::internal::WireFormatLite::WriteEnumToArray(
      1, this->conflicting_enum(), target);
  }

  if (_internal_metadata_.have_unknown_fields()) {
    target = ::google::protobuf::internal::WireFormat::SerializeUnknownFieldsToArray(
        unknown_fields(), target);
  }
  // @@protoc_insertion_point(serialize_to_array_end:protobuf_unittest.TestConflictingEnumNames)
  return target;
}

size_t TestConflictingEnumNames::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:protobuf_unittest.TestConflictingEnumNames)
  size_t total_size = 0;

  // optional .protobuf_unittest.TestConflictingEnumNames.NestedConflictingEnum conflicting_enum = 1;
  if (has_conflicting_enum()) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::EnumSize(this->conflicting_enum());
  }

  if (_internal_metadata_.have_unknown_fields()) {
    total_size +=
      ::google::protobuf::internal::WireFormat::ComputeUnknownFieldsSize(
        unknown_fields());
  }
  int cached_size = ::google::protobuf::internal::ToCachedSize(total_size);
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = cached_size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
  return total_size;
}

void TestConflictingEnumNames::MergeFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_merge_from_start:protobuf_unittest.TestConflictingEnumNames)
  if (GOOGLE_PREDICT_FALSE(&from == this)) MergeFromFail(__LINE__);
  const TestConflictingEnumNames* source =
      ::google::protobuf::internal::DynamicCastToGenerated<const TestConflictingEnumNames>(
          &from);
  if (source == NULL) {
  // @@protoc_insertion_point(generalized_merge_from_cast_fail:protobuf_unittest.TestConflictingEnumNames)
    ::google::protobuf::internal::ReflectionOps::Merge(from, this);
  } else {
  // @@protoc_insertion_point(generalized_merge_from_cast_success:protobuf_unittest.TestConflictingEnumNames)
    UnsafeMergeFrom(*source);
  }
}

void TestConflictingEnumNames::MergeFrom(const TestConflictingEnumNames& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:protobuf_unittest.TestConflictingEnumNames)
  if (GOOGLE_PREDICT_TRUE(&from != this)) {
    UnsafeMergeFrom(from);
  } else {
    MergeFromFail(__LINE__);
  }
}

void TestConflictingEnumNames::UnsafeMergeFrom(const TestConflictingEnumNames& from) {
  GOOGLE_DCHECK(&from != this);
  if (from._has_bits_[0 / 32] & (0xffu << (0 % 32))) {
    if (from.has_conflicting_enum()) {
      set_conflicting_enum(from.conflicting_enum());
    }
  }
  if (from._internal_metadata_.have_unknown_fields()) {
    ::google::protobuf::UnknownFieldSet::MergeToInternalMetdata(
      from.unknown_fields(), &_internal_metadata_);
  }
}

void TestConflictingEnumNames::CopyFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_copy_from_start:protobuf_unittest.TestConflictingEnumNames)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

void TestConflictingEnumNames::CopyFrom(const TestConflictingEnumNames& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:protobuf_unittest.TestConflictingEnumNames)
  if (&from == this) return;
  Clear();
  UnsafeMergeFrom(from);
}

bool TestConflictingEnumNames::IsInitialized() const {

  return true;
}

void TestConflictingEnumNames::Swap(TestConflictingEnumNames* other) {
  if (other == this) return;
  InternalSwap(other);
}
void TestConflictingEnumNames::InternalSwap(TestConflictingEnumNames* other) {
  std::swap(conflicting_enum_, other->conflicting_enum_);
  std::swap(_has_bits_[0], other->_has_bits_[0]);
  _internal_metadata_.Swap(&other->_internal_metadata_);
  std::swap(_cached_size_, other->_cached_size_);
}

::google::protobuf::Metadata TestConflictingEnumNames::GetMetadata() const {
  protobuf_AssignDescriptorsOnce();
  ::google::protobuf::Metadata metadata;
  metadata.descriptor = TestConflictingEnumNames_descriptor_;
  metadata.reflection = TestConflictingEnumNames_reflection_;
  return metadata;
}

#if PROTOBUF_INLINE_NOT_IN_HEADERS
// TestConflictingEnumNames

// optional .protobuf_unittest.TestConflictingEnumNames.NestedConflictingEnum conflicting_enum = 1;
bool TestConflictingEnumNames::has_conflicting_enum() const {
  return (_has_bits_[0] & 0x00000001u) != 0;
}
void TestConflictingEnumNames::set_has_conflicting_enum() {
  _has_bits_[0] |= 0x00000001u;
}
void TestConflictingEnumNames::clear_has_conflicting_enum() {
  _has_bits_[0] &= ~0x00000001u;
}
void TestConflictingEnumNames::clear_conflicting_enum() {
  conflicting_enum_ = 1;
  clear_has_conflicting_enum();
}
::protobuf_unittest::TestConflictingEnumNames_NestedConflictingEnum TestConflictingEnumNames::conflicting_enum() const {
  // @@protoc_insertion_point(field_get:protobuf_unittest.TestConflictingEnumNames.conflicting_enum)
  return static_cast< ::protobuf_unittest::TestConflictingEnumNames_NestedConflictingEnum >(conflicting_enum_);
}
void TestConflictingEnumNames::set_conflicting_enum(::protobuf_unittest::TestConflictingEnumNames_NestedConflictingEnum value) {
  assert(::protobuf_unittest::TestConflictingEnumNames_NestedConflictingEnum_IsValid(value));
  set_has_conflicting_enum();
  conflicting_enum_ = value;
  // @@protoc_insertion_point(field_set:protobuf_unittest.TestConflictingEnumNames.conflicting_enum)
}

inline const TestConflictingEnumNames* TestConflictingEnumNames::internal_default_instance() {
  return &TestConflictingEnumNames_default_instance_.get();
}
#endif  // PROTOBUF_INLINE_NOT_IN_HEADERS

// ===================================================================

#if !defined(_MSC_VER) || _MSC_VER >= 1900
#endif  // !defined(_MSC_VER) || _MSC_VER >= 1900

DummyMessage::DummyMessage()
  : ::google::protobuf::Message(), _internal_metadata_(NULL) {
  if (this != internal_default_instance()) protobuf_InitDefaults_google_2fprotobuf_2fcompiler_2fcpp_2fcpp_5ftest_5fbad_5fidentifiers_2eproto();
  SharedCtor();
  // @@protoc_insertion_point(constructor:protobuf_unittest.DummyMessage)
}

void DummyMessage::InitAsDefaultInstance() {
}

DummyMessage::DummyMessage(const DummyMessage& from)
  : ::google::protobuf::Message(),
    _internal_metadata_(NULL) {
  SharedCtor();
  UnsafeMergeFrom(from);
  // @@protoc_insertion_point(copy_constructor:protobuf_unittest.DummyMessage)
}

void DummyMessage::SharedCtor() {
  _cached_size_ = 0;
}

DummyMessage::~DummyMessage() {
  // @@protoc_insertion_point(destructor:protobuf_unittest.DummyMessage)
  SharedDtor();
}

void DummyMessage::SharedDtor() {
}

void DummyMessage::SetCachedSize(int size) const {
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
}
const ::google::protobuf::Descriptor* DummyMessage::descriptor() {
  protobuf_AssignDescriptorsOnce();
  return DummyMessage_descriptor_;
}

const DummyMessage& DummyMessage::default_instance() {
  protobuf_InitDefaults_google_2fprotobuf_2fcompiler_2fcpp_2fcpp_5ftest_5fbad_5fidentifiers_2eproto();
  return *internal_default_instance();
}

::google::protobuf::internal::ExplicitlyConstructed<DummyMessage> DummyMessage_default_instance_;

DummyMessage* DummyMessage::New(::google::protobuf::Arena* arena) const {
  DummyMessage* n = new DummyMessage;
  if (arena != NULL) {
    arena->Own(n);
  }
  return n;
}

void DummyMessage::Clear() {
// @@protoc_insertion_point(message_clear_start:protobuf_unittest.DummyMessage)
  _has_bits_.Clear();
  if (_internal_metadata_.have_unknown_fields()) {
    mutable_unknown_fields()->Clear();
  }
}

bool DummyMessage::MergePartialFromCodedStream(
    ::google::protobuf::io::CodedInputStream* input) {
#define DO_(EXPRESSION) if (!GOOGLE_PREDICT_TRUE(EXPRESSION)) goto failure
  ::google::protobuf::uint32 tag;
  // @@protoc_insertion_point(parse_start:protobuf_unittest.DummyMessage)
  for (;;) {
    ::std::pair< ::google::protobuf::uint32, bool> p = input->ReadTagWithCutoff(127);
    tag = p.first;
    if (!p.second) goto handle_unusual;
  handle_unusual:
    if (tag == 0 ||
        ::google::protobuf::internal::WireFormatLite::GetTagWireType(tag) ==
        ::google::protobuf::internal::WireFormatLite::WIRETYPE_END_GROUP) {
      goto success;
    }
    DO_(::google::protobuf::internal::WireFormat::SkipField(
          input, tag, mutable_unknown_fields()));
  }
success:
  // @@protoc_insertion_point(parse_success:protobuf_unittest.DummyMessage)
  return true;
failure:
  // @@protoc_insertion_point(parse_failure:protobuf_unittest.DummyMessage)
  return false;
#undef DO_
}

void DummyMessage::SerializeWithCachedSizes(
    ::google::protobuf::io::CodedOutputStream* output) const {
  // @@protoc_insertion_point(serialize_start:protobuf_unittest.DummyMessage)
  if (_internal_metadata_.have_unknown_fields()) {
    ::google::protobuf::internal::WireFormat::SerializeUnknownFields(
        unknown_fields(), output);
  }
  // @@protoc_insertion_point(serialize_end:protobuf_unittest.DummyMessage)
}

::google::protobuf::uint8* DummyMessage::InternalSerializeWithCachedSizesToArray(
    bool deterministic, ::google::protobuf::uint8* target) const {
  (void)deterministic; // Unused
  // @@protoc_insertion_point(serialize_to_array_start:protobuf_unittest.DummyMessage)
  if (_internal_metadata_.have_unknown_fields()) {
    target = ::google::protobuf::internal::WireFormat::SerializeUnknownFieldsToArray(
        unknown_fields(), target);
  }
  // @@protoc_insertion_point(serialize_to_array_end:protobuf_unittest.DummyMessage)
  return target;
}

size_t DummyMessage::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:protobuf_unittest.DummyMessage)
  size_t total_size = 0;

  if (_internal_metadata_.have_unknown_fields()) {
    total_size +=
      ::google::protobuf::internal::WireFormat::ComputeUnknownFieldsSize(
        unknown_fields());
  }
  int cached_size = ::google::protobuf::internal::ToCachedSize(total_size);
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = cached_size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
  return total_size;
}

void DummyMessage::MergeFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_merge_from_start:protobuf_unittest.DummyMessage)
  if (GOOGLE_PREDICT_FALSE(&from == this)) MergeFromFail(__LINE__);
  const DummyMessage* source =
      ::google::protobuf::internal::DynamicCastToGenerated<const DummyMessage>(
          &from);
  if (source == NULL) {
  // @@protoc_insertion_point(generalized_merge_from_cast_fail:protobuf_unittest.DummyMessage)
    ::google::protobuf::internal::ReflectionOps::Merge(from, this);
  } else {
  // @@protoc_insertion_point(generalized_merge_from_cast_success:protobuf_unittest.DummyMessage)
    UnsafeMergeFrom(*source);
  }
}

void DummyMessage::MergeFrom(const DummyMessage& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:protobuf_unittest.DummyMessage)
  if (GOOGLE_PREDICT_TRUE(&from != this)) {
    UnsafeMergeFrom(from);
  } else {
    MergeFromFail(__LINE__);
  }
}

void DummyMessage::UnsafeMergeFrom(const DummyMessage& from) {
  GOOGLE_DCHECK(&from != this);
  if (from._internal_metadata_.have_unknown_fields()) {
    ::google::protobuf::UnknownFieldSet::MergeToInternalMetdata(
      from.unknown_fields(), &_internal_metadata_);
  }
}

void DummyMessage::CopyFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_copy_from_start:protobuf_unittest.DummyMessage)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

void DummyMessage::CopyFrom(const DummyMessage& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:protobuf_unittest.DummyMessage)
  if (&from == this) return;
  Clear();
  UnsafeMergeFrom(from);
}

bool DummyMessage::IsInitialized() const {

  return true;
}

void DummyMessage::Swap(DummyMessage* other) {
  if (other == this) return;
  InternalSwap(other);
}
void DummyMessage::InternalSwap(DummyMessage* other) {
  _internal_metadata_.Swap(&other->_internal_metadata_);
  std::swap(_cached_size_, other->_cached_size_);
}

::google::protobuf::Metadata DummyMessage::GetMetadata() const {
  protobuf_AssignDescriptorsOnce();
  ::google::protobuf::Metadata metadata;
  metadata.descriptor = DummyMessage_descriptor_;
  metadata.reflection = DummyMessage_reflection_;
  return metadata;
}

#if PROTOBUF_INLINE_NOT_IN_HEADERS
// DummyMessage

inline const DummyMessage* DummyMessage::internal_default_instance() {
  return &DummyMessage_default_instance_.get();
}
#endif  // PROTOBUF_INLINE_NOT_IN_HEADERS

// ===================================================================

TestConflictingMethodNames::~TestConflictingMethodNames() {}

const ::google::protobuf::ServiceDescriptor* TestConflictingMethodNames::descriptor() {
  protobuf_AssignDescriptorsOnce();
  return TestConflictingMethodNames_descriptor_;
}

const ::google::protobuf::ServiceDescriptor* TestConflictingMethodNames::GetDescriptor() {
  protobuf_AssignDescriptorsOnce();
  return TestConflictingMethodNames_descriptor_;
}

void TestConflictingMethodNames::Closure(::google::protobuf::RpcController* controller,
                         const ::protobuf_unittest::DummyMessage*,
                         ::protobuf_unittest::DummyMessage*,
                         ::google::protobuf::Closure* done) {
  controller->SetFailed("Method Closure() not implemented.");
  done->Run();
}

void TestConflictingMethodNames::CallMethod(const ::google::protobuf::MethodDescriptor* method,
                             ::google::protobuf::RpcController* controller,
                             const ::google::protobuf::Message* request,
                             ::google::protobuf::Message* response,
                             ::google::protobuf::Closure* done) {
  GOOGLE_DCHECK_EQ(method->service(), TestConflictingMethodNames_descriptor_);
  switch(method->index()) {
    case 0:
      Closure(controller,
             ::google::protobuf::down_cast<const ::protobuf_unittest::DummyMessage*>(request),
             ::google::protobuf::down_cast< ::protobuf_unittest::DummyMessage*>(response),
             done);
      break;
    default:
      GOOGLE_LOG(FATAL) << "Bad method index; this should never happen.";
      break;
  }
}

const ::google::protobuf::Message& TestConflictingMethodNames::GetRequestPrototype(
    const ::google::protobuf::MethodDescriptor* method) const {
  GOOGLE_DCHECK_EQ(method->service(), descriptor());
  switch(method->index()) {
    case 0:
      return ::protobuf_unittest::DummyMessage::default_instance();
    default:
      GOOGLE_LOG(FATAL) << "Bad method index; this should never happen.";
      return *::google::protobuf::MessageFactory::generated_factory()
          ->GetPrototype(method->input_type());
  }
}

const ::google::protobuf::Message& TestConflictingMethodNames::GetResponsePrototype(
    const ::google::protobuf::MethodDescriptor* method) const {
  GOOGLE_DCHECK_EQ(method->service(), descriptor());
  switch(method->index()) {
    case 0:
      return ::protobuf_unittest::DummyMessage::default_instance();
    default:
      GOOGLE_LOG(FATAL) << "Bad method index; this should never happen.";
      return *::google::protobuf::MessageFactory::generated_factory()
          ->GetPrototype(method->output_type());
  }
}

TestConflictingMethodNames_Stub::TestConflictingMethodNames_Stub(::google::protobuf::RpcChannel* channel)
  : channel_(channel), owns_channel_(false) {}
TestConflictingMethodNames_Stub::TestConflictingMethodNames_Stub(
    ::google::protobuf::RpcChannel* channel,
    ::google::protobuf::Service::ChannelOwnership ownership)
  : channel_(channel),
    owns_channel_(ownership == ::google::protobuf::Service::STUB_OWNS_CHANNEL) {}
TestConflictingMethodNames_Stub::~TestConflictingMethodNames_Stub() {
  if (owns_channel_) delete channel_;
}

void TestConflictingMethodNames_Stub::Closure(::google::protobuf::RpcController* controller,
                              const ::protobuf_unittest::DummyMessage* request,
                              ::protobuf_unittest::DummyMessage* response,
                              ::google::protobuf::Closure* done) {
  channel_->CallMethod(descriptor()->method(0),
                       controller, request, response, done);
}

// @@protoc_insertion_point(namespace_scope)

}  // namespace protobuf_unittest

// @@protoc_insertion_point(global_scope)

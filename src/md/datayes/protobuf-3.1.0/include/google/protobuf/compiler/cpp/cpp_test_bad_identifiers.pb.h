// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: google/protobuf/compiler/cpp/cpp_test_bad_identifiers.proto

#ifndef PROTOBUF_google_2fprotobuf_2fcompiler_2fcpp_2fcpp_5ftest_5fbad_5fidentifiers_2eproto__INCLUDED
#define PROTOBUF_google_2fprotobuf_2fcompiler_2fcpp_2fcpp_5ftest_5fbad_5fidentifiers_2eproto__INCLUDED

#include <string>

#include <google/protobuf/stubs/common.h>

#if GOOGLE_PROTOBUF_VERSION < 3001000
#error This file was generated by a newer version of protoc which is
#error incompatible with your Protocol Buffer headers.  Please update
#error your headers.
#endif
#if 3001000 < GOOGLE_PROTOBUF_MIN_PROTOC_VERSION
#error This file was generated by an older version of protoc which is
#error incompatible with your Protocol Buffer headers.  Please
#error regenerate this file with a newer version of protoc.
#endif

#include <google/protobuf/arena.h>
#include <google/protobuf/arenastring.h>
#include <google/protobuf/generated_message_util.h>
#include <google/protobuf/metadata.h>
#include <google/protobuf/message.h>
#include <google/protobuf/repeated_field.h>
#include <google/protobuf/extension_set.h>
#include <google/protobuf/generated_enum_reflection.h>
#include <google/protobuf/service.h>
#include <google/protobuf/unknown_field_set.h>
// @@protoc_insertion_point(includes)

namespace protobuf_unittest {

// Internal implementation detail -- do not call these.
void protobuf_AddDesc_google_2fprotobuf_2fcompiler_2fcpp_2fcpp_5ftest_5fbad_5fidentifiers_2eproto();
void protobuf_InitDefaults_google_2fprotobuf_2fcompiler_2fcpp_2fcpp_5ftest_5fbad_5fidentifiers_2eproto();
void protobuf_AssignDesc_google_2fprotobuf_2fcompiler_2fcpp_2fcpp_5ftest_5fbad_5fidentifiers_2eproto();
void protobuf_ShutdownFile_google_2fprotobuf_2fcompiler_2fcpp_2fcpp_5ftest_5fbad_5fidentifiers_2eproto();

class DummyMessage;
class TestConflictingEnumNames;
class TestConflictingSymbolNames;
class TestConflictingSymbolNamesExtension;
class TestConflictingSymbolNames_BuildDescriptors;
class TestConflictingSymbolNames_Cord;
class TestConflictingSymbolNames_DO;
class TestConflictingSymbolNames_Data1;
class TestConflictingSymbolNames_Data2;
class TestConflictingSymbolNames_Data3;
class TestConflictingSymbolNames_Data4;
class TestConflictingSymbolNames_Data5;
class TestConflictingSymbolNames_Data6;
class TestConflictingSymbolNames_StringPiece;
class TestConflictingSymbolNames_TypeTraits;

enum TestConflictingSymbolNames_TestEnum {
  TestConflictingSymbolNames_TestEnum_FOO = 0
};
bool TestConflictingSymbolNames_TestEnum_IsValid(int value);
const TestConflictingSymbolNames_TestEnum TestConflictingSymbolNames_TestEnum_TestEnum_MIN = TestConflictingSymbolNames_TestEnum_FOO;
const TestConflictingSymbolNames_TestEnum TestConflictingSymbolNames_TestEnum_TestEnum_MAX = TestConflictingSymbolNames_TestEnum_FOO;
const int TestConflictingSymbolNames_TestEnum_TestEnum_ARRAYSIZE = TestConflictingSymbolNames_TestEnum_TestEnum_MAX + 1;

const ::google::protobuf::EnumDescriptor* TestConflictingSymbolNames_TestEnum_descriptor();
inline const ::std::string& TestConflictingSymbolNames_TestEnum_Name(TestConflictingSymbolNames_TestEnum value) {
  return ::google::protobuf::internal::NameOfEnum(
    TestConflictingSymbolNames_TestEnum_descriptor(), value);
}
inline bool TestConflictingSymbolNames_TestEnum_Parse(
    const ::std::string& name, TestConflictingSymbolNames_TestEnum* value) {
  return ::google::protobuf::internal::ParseNamedEnum<TestConflictingSymbolNames_TestEnum>(
    TestConflictingSymbolNames_TestEnum_descriptor(), name, value);
}
enum TestConflictingEnumNames_NestedConflictingEnum {
  TestConflictingEnumNames_NestedConflictingEnum_and_ = 1,
  TestConflictingEnumNames_NestedConflictingEnum_class_ = 2,
  TestConflictingEnumNames_NestedConflictingEnum_int_ = 3,
  TestConflictingEnumNames_NestedConflictingEnum_typedef_ = 4,
  TestConflictingEnumNames_NestedConflictingEnum_XOR = 5
};
bool TestConflictingEnumNames_NestedConflictingEnum_IsValid(int value);
const TestConflictingEnumNames_NestedConflictingEnum TestConflictingEnumNames_NestedConflictingEnum_NestedConflictingEnum_MIN = TestConflictingEnumNames_NestedConflictingEnum_and_;
const TestConflictingEnumNames_NestedConflictingEnum TestConflictingEnumNames_NestedConflictingEnum_NestedConflictingEnum_MAX = TestConflictingEnumNames_NestedConflictingEnum_XOR;
const int TestConflictingEnumNames_NestedConflictingEnum_NestedConflictingEnum_ARRAYSIZE = TestConflictingEnumNames_NestedConflictingEnum_NestedConflictingEnum_MAX + 1;

const ::google::protobuf::EnumDescriptor* TestConflictingEnumNames_NestedConflictingEnum_descriptor();
inline const ::std::string& TestConflictingEnumNames_NestedConflictingEnum_Name(TestConflictingEnumNames_NestedConflictingEnum value) {
  return ::google::protobuf::internal::NameOfEnum(
    TestConflictingEnumNames_NestedConflictingEnum_descriptor(), value);
}
inline bool TestConflictingEnumNames_NestedConflictingEnum_Parse(
    const ::std::string& name, TestConflictingEnumNames_NestedConflictingEnum* value) {
  return ::google::protobuf::internal::ParseNamedEnum<TestConflictingEnumNames_NestedConflictingEnum>(
    TestConflictingEnumNames_NestedConflictingEnum_descriptor(), name, value);
}
enum ConflictingEnum {
  NOT_EQ = 1,
  volatile_ = 2,
  return_ = 3
};
bool ConflictingEnum_IsValid(int value);
const ConflictingEnum ConflictingEnum_MIN = NOT_EQ;
const ConflictingEnum ConflictingEnum_MAX = return_;
const int ConflictingEnum_ARRAYSIZE = ConflictingEnum_MAX + 1;

const ::google::protobuf::EnumDescriptor* ConflictingEnum_descriptor();
inline const ::std::string& ConflictingEnum_Name(ConflictingEnum value) {
  return ::google::protobuf::internal::NameOfEnum(
    ConflictingEnum_descriptor(), value);
}
inline bool ConflictingEnum_Parse(
    const ::std::string& name, ConflictingEnum* value) {
  return ::google::protobuf::internal::ParseNamedEnum<ConflictingEnum>(
    ConflictingEnum_descriptor(), name, value);
}
// ===================================================================

class TestConflictingSymbolNames_BuildDescriptors : public ::google::protobuf::Message /* @@protoc_insertion_point(class_definition:protobuf_unittest.TestConflictingSymbolNames.BuildDescriptors) */ {
 public:
  TestConflictingSymbolNames_BuildDescriptors();
  virtual ~TestConflictingSymbolNames_BuildDescriptors();

  TestConflictingSymbolNames_BuildDescriptors(const TestConflictingSymbolNames_BuildDescriptors& from);

  inline TestConflictingSymbolNames_BuildDescriptors& operator=(const TestConflictingSymbolNames_BuildDescriptors& from) {
    CopyFrom(from);
    return *this;
  }

  inline const ::google::protobuf::UnknownFieldSet& unknown_fields() const {
    return _internal_metadata_.unknown_fields();
  }

  inline ::google::protobuf::UnknownFieldSet* mutable_unknown_fields() {
    return _internal_metadata_.mutable_unknown_fields();
  }

  static const ::google::protobuf::Descriptor* descriptor();
  static const TestConflictingSymbolNames_BuildDescriptors& default_instance();

  static const TestConflictingSymbolNames_BuildDescriptors* internal_default_instance();

  void Swap(TestConflictingSymbolNames_BuildDescriptors* other);

  // implements Message ----------------------------------------------

  inline TestConflictingSymbolNames_BuildDescriptors* New() const { return New(NULL); }

  TestConflictingSymbolNames_BuildDescriptors* New(::google::protobuf::Arena* arena) const;
  void CopyFrom(const ::google::protobuf::Message& from);
  void MergeFrom(const ::google::protobuf::Message& from);
  void CopyFrom(const TestConflictingSymbolNames_BuildDescriptors& from);
  void MergeFrom(const TestConflictingSymbolNames_BuildDescriptors& from);
  void Clear();
  bool IsInitialized() const;

  size_t ByteSizeLong() const;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input);
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const;
  ::google::protobuf::uint8* InternalSerializeWithCachedSizesToArray(
      bool deterministic, ::google::protobuf::uint8* output) const;
  ::google::protobuf::uint8* SerializeWithCachedSizesToArray(::google::protobuf::uint8* output) const {
    return InternalSerializeWithCachedSizesToArray(false, output);
  }
  int GetCachedSize() const { return _cached_size_; }
  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const;
  void InternalSwap(TestConflictingSymbolNames_BuildDescriptors* other);
  void UnsafeMergeFrom(const TestConflictingSymbolNames_BuildDescriptors& from);
  private:
  inline ::google::protobuf::Arena* GetArenaNoVirtual() const {
    return _internal_metadata_.arena();
  }
  inline void* MaybeArenaPtr() const {
    return _internal_metadata_.raw_arena_ptr();
  }
  public:

  ::google::protobuf::Metadata GetMetadata() const;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // @@protoc_insertion_point(class_scope:protobuf_unittest.TestConflictingSymbolNames.BuildDescriptors)
 private:

  ::google::protobuf::internal::InternalMetadataWithArena _internal_metadata_;
  ::google::protobuf::internal::HasBits<1> _has_bits_;
  mutable int _cached_size_;
  friend void  protobuf_InitDefaults_google_2fprotobuf_2fcompiler_2fcpp_2fcpp_5ftest_5fbad_5fidentifiers_2eproto_impl();
  friend void  protobuf_AddDesc_google_2fprotobuf_2fcompiler_2fcpp_2fcpp_5ftest_5fbad_5fidentifiers_2eproto_impl();
  friend void protobuf_AssignDesc_google_2fprotobuf_2fcompiler_2fcpp_2fcpp_5ftest_5fbad_5fidentifiers_2eproto();
  friend void protobuf_ShutdownFile_google_2fprotobuf_2fcompiler_2fcpp_2fcpp_5ftest_5fbad_5fidentifiers_2eproto();

  void InitAsDefaultInstance();
};
extern ::google::protobuf::internal::ExplicitlyConstructed<TestConflictingSymbolNames_BuildDescriptors> TestConflictingSymbolNames_BuildDescriptors_default_instance_;

// -------------------------------------------------------------------

class TestConflictingSymbolNames_TypeTraits : public ::google::protobuf::Message /* @@protoc_insertion_point(class_definition:protobuf_unittest.TestConflictingSymbolNames.TypeTraits) */ {
 public:
  TestConflictingSymbolNames_TypeTraits();
  virtual ~TestConflictingSymbolNames_TypeTraits();

  TestConflictingSymbolNames_TypeTraits(const TestConflictingSymbolNames_TypeTraits& from);

  inline TestConflictingSymbolNames_TypeTraits& operator=(const TestConflictingSymbolNames_TypeTraits& from) {
    CopyFrom(from);
    return *this;
  }

  inline const ::google::protobuf::UnknownFieldSet& unknown_fields() const {
    return _internal_metadata_.unknown_fields();
  }

  inline ::google::protobuf::UnknownFieldSet* mutable_unknown_fields() {
    return _internal_metadata_.mutable_unknown_fields();
  }

  static const ::google::protobuf::Descriptor* descriptor();
  static const TestConflictingSymbolNames_TypeTraits& default_instance();

  static const TestConflictingSymbolNames_TypeTraits* internal_default_instance();

  void Swap(TestConflictingSymbolNames_TypeTraits* other);

  // implements Message ----------------------------------------------

  inline TestConflictingSymbolNames_TypeTraits* New() const { return New(NULL); }

  TestConflictingSymbolNames_TypeTraits* New(::google::protobuf::Arena* arena) const;
  void CopyFrom(const ::google::protobuf::Message& from);
  void MergeFrom(const ::google::protobuf::Message& from);
  void CopyFrom(const TestConflictingSymbolNames_TypeTraits& from);
  void MergeFrom(const TestConflictingSymbolNames_TypeTraits& from);
  void Clear();
  bool IsInitialized() const;

  size_t ByteSizeLong() const;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input);
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const;
  ::google::protobuf::uint8* InternalSerializeWithCachedSizesToArray(
      bool deterministic, ::google::protobuf::uint8* output) const;
  ::google::protobuf::uint8* SerializeWithCachedSizesToArray(::google::protobuf::uint8* output) const {
    return InternalSerializeWithCachedSizesToArray(false, output);
  }
  int GetCachedSize() const { return _cached_size_; }
  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const;
  void InternalSwap(TestConflictingSymbolNames_TypeTraits* other);
  void UnsafeMergeFrom(const TestConflictingSymbolNames_TypeTraits& from);
  private:
  inline ::google::protobuf::Arena* GetArenaNoVirtual() const {
    return _internal_metadata_.arena();
  }
  inline void* MaybeArenaPtr() const {
    return _internal_metadata_.raw_arena_ptr();
  }
  public:

  ::google::protobuf::Metadata GetMetadata() const;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // @@protoc_insertion_point(class_scope:protobuf_unittest.TestConflictingSymbolNames.TypeTraits)
 private:

  ::google::protobuf::internal::InternalMetadataWithArena _internal_metadata_;
  ::google::protobuf::internal::HasBits<1> _has_bits_;
  mutable int _cached_size_;
  friend void  protobuf_InitDefaults_google_2fprotobuf_2fcompiler_2fcpp_2fcpp_5ftest_5fbad_5fidentifiers_2eproto_impl();
  friend void  protobuf_AddDesc_google_2fprotobuf_2fcompiler_2fcpp_2fcpp_5ftest_5fbad_5fidentifiers_2eproto_impl();
  friend void protobuf_AssignDesc_google_2fprotobuf_2fcompiler_2fcpp_2fcpp_5ftest_5fbad_5fidentifiers_2eproto();
  friend void protobuf_ShutdownFile_google_2fprotobuf_2fcompiler_2fcpp_2fcpp_5ftest_5fbad_5fidentifiers_2eproto();

  void InitAsDefaultInstance();
};
extern ::google::protobuf::internal::ExplicitlyConstructed<TestConflictingSymbolNames_TypeTraits> TestConflictingSymbolNames_TypeTraits_default_instance_;

// -------------------------------------------------------------------

class TestConflictingSymbolNames_Data1 : public ::google::protobuf::Message /* @@protoc_insertion_point(class_definition:protobuf_unittest.TestConflictingSymbolNames.Data1) */ {
 public:
  TestConflictingSymbolNames_Data1();
  virtual ~TestConflictingSymbolNames_Data1();

  TestConflictingSymbolNames_Data1(const TestConflictingSymbolNames_Data1& from);

  inline TestConflictingSymbolNames_Data1& operator=(const TestConflictingSymbolNames_Data1& from) {
    CopyFrom(from);
    return *this;
  }

  inline const ::google::protobuf::UnknownFieldSet& unknown_fields() const {
    return _internal_metadata_.unknown_fields();
  }

  inline ::google::protobuf::UnknownFieldSet* mutable_unknown_fields() {
    return _internal_metadata_.mutable_unknown_fields();
  }

  static const ::google::protobuf::Descriptor* descriptor();
  static const TestConflictingSymbolNames_Data1& default_instance();

  static const TestConflictingSymbolNames_Data1* internal_default_instance();

  void Swap(TestConflictingSymbolNames_Data1* other);

  // implements Message ----------------------------------------------

  inline TestConflictingSymbolNames_Data1* New() const { return New(NULL); }

  TestConflictingSymbolNames_Data1* New(::google::protobuf::Arena* arena) const;
  void CopyFrom(const ::google::protobuf::Message& from);
  void MergeFrom(const ::google::protobuf::Message& from);
  void CopyFrom(const TestConflictingSymbolNames_Data1& from);
  void MergeFrom(const TestConflictingSymbolNames_Data1& from);
  void Clear();
  bool IsInitialized() const;

  size_t ByteSizeLong() const;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input);
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const;
  ::google::protobuf::uint8* InternalSerializeWithCachedSizesToArray(
      bool deterministic, ::google::protobuf::uint8* output) const;
  ::google::protobuf::uint8* SerializeWithCachedSizesToArray(::google::protobuf::uint8* output) const {
    return InternalSerializeWithCachedSizesToArray(false, output);
  }
  int GetCachedSize() const { return _cached_size_; }
  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const;
  void InternalSwap(TestConflictingSymbolNames_Data1* other);
  void UnsafeMergeFrom(const TestConflictingSymbolNames_Data1& from);
  private:
  inline ::google::protobuf::Arena* GetArenaNoVirtual() const {
    return _internal_metadata_.arena();
  }
  inline void* MaybeArenaPtr() const {
    return _internal_metadata_.raw_arena_ptr();
  }
  public:

  ::google::protobuf::Metadata GetMetadata() const;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // repeated int32 data = 1;
  int data_size() const;
  void clear_data();
  static const int kDataFieldNumber = 1;
  ::google::protobuf::int32 data(int index) const;
  void set_data(int index, ::google::protobuf::int32 value);
  void add_data(::google::protobuf::int32 value);
  const ::google::protobuf::RepeatedField< ::google::protobuf::int32 >&
      data() const;
  ::google::protobuf::RepeatedField< ::google::protobuf::int32 >*
      mutable_data();

  // @@protoc_insertion_point(class_scope:protobuf_unittest.TestConflictingSymbolNames.Data1)
 private:

  ::google::protobuf::internal::InternalMetadataWithArena _internal_metadata_;
  ::google::protobuf::internal::HasBits<1> _has_bits_;
  mutable int _cached_size_;
  ::google::protobuf::RepeatedField< ::google::protobuf::int32 > data_;
  friend void  protobuf_InitDefaults_google_2fprotobuf_2fcompiler_2fcpp_2fcpp_5ftest_5fbad_5fidentifiers_2eproto_impl();
  friend void  protobuf_AddDesc_google_2fprotobuf_2fcompiler_2fcpp_2fcpp_5ftest_5fbad_5fidentifiers_2eproto_impl();
  friend void protobuf_AssignDesc_google_2fprotobuf_2fcompiler_2fcpp_2fcpp_5ftest_5fbad_5fidentifiers_2eproto();
  friend void protobuf_ShutdownFile_google_2fprotobuf_2fcompiler_2fcpp_2fcpp_5ftest_5fbad_5fidentifiers_2eproto();

  void InitAsDefaultInstance();
};
extern ::google::protobuf::internal::ExplicitlyConstructed<TestConflictingSymbolNames_Data1> TestConflictingSymbolNames_Data1_default_instance_;

// -------------------------------------------------------------------

class TestConflictingSymbolNames_Data2 : public ::google::protobuf::Message /* @@protoc_insertion_point(class_definition:protobuf_unittest.TestConflictingSymbolNames.Data2) */ {
 public:
  TestConflictingSymbolNames_Data2();
  virtual ~TestConflictingSymbolNames_Data2();

  TestConflictingSymbolNames_Data2(const TestConflictingSymbolNames_Data2& from);

  inline TestConflictingSymbolNames_Data2& operator=(const TestConflictingSymbolNames_Data2& from) {
    CopyFrom(from);
    return *this;
  }

  inline const ::google::protobuf::UnknownFieldSet& unknown_fields() const {
    return _internal_metadata_.unknown_fields();
  }

  inline ::google::protobuf::UnknownFieldSet* mutable_unknown_fields() {
    return _internal_metadata_.mutable_unknown_fields();
  }

  static const ::google::protobuf::Descriptor* descriptor();
  static const TestConflictingSymbolNames_Data2& default_instance();

  static const TestConflictingSymbolNames_Data2* internal_default_instance();

  void Swap(TestConflictingSymbolNames_Data2* other);

  // implements Message ----------------------------------------------

  inline TestConflictingSymbolNames_Data2* New() const { return New(NULL); }

  TestConflictingSymbolNames_Data2* New(::google::protobuf::Arena* arena) const;
  void CopyFrom(const ::google::protobuf::Message& from);
  void MergeFrom(const ::google::protobuf::Message& from);
  void CopyFrom(const TestConflictingSymbolNames_Data2& from);
  void MergeFrom(const TestConflictingSymbolNames_Data2& from);
  void Clear();
  bool IsInitialized() const;

  size_t ByteSizeLong() const;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input);
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const;
  ::google::protobuf::uint8* InternalSerializeWithCachedSizesToArray(
      bool deterministic, ::google::protobuf::uint8* output) const;
  ::google::protobuf::uint8* SerializeWithCachedSizesToArray(::google::protobuf::uint8* output) const {
    return InternalSerializeWithCachedSizesToArray(false, output);
  }
  int GetCachedSize() const { return _cached_size_; }
  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const;
  void InternalSwap(TestConflictingSymbolNames_Data2* other);
  void UnsafeMergeFrom(const TestConflictingSymbolNames_Data2& from);
  private:
  inline ::google::protobuf::Arena* GetArenaNoVirtual() const {
    return _internal_metadata_.arena();
  }
  inline void* MaybeArenaPtr() const {
    return _internal_metadata_.raw_arena_ptr();
  }
  public:

  ::google::protobuf::Metadata GetMetadata() const;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // repeated .protobuf_unittest.TestConflictingSymbolNames.TestEnum data = 1;
  int data_size() const;
  void clear_data();
  static const int kDataFieldNumber = 1;
  ::protobuf_unittest::TestConflictingSymbolNames_TestEnum data(int index) const;
  void set_data(int index, ::protobuf_unittest::TestConflictingSymbolNames_TestEnum value);
  void add_data(::protobuf_unittest::TestConflictingSymbolNames_TestEnum value);
  const ::google::protobuf::RepeatedField<int>& data() const;
  ::google::protobuf::RepeatedField<int>* mutable_data();

  // @@protoc_insertion_point(class_scope:protobuf_unittest.TestConflictingSymbolNames.Data2)
 private:

  ::google::protobuf::internal::InternalMetadataWithArena _internal_metadata_;
  ::google::protobuf::internal::HasBits<1> _has_bits_;
  mutable int _cached_size_;
  ::google::protobuf::RepeatedField<int> data_;
  friend void  protobuf_InitDefaults_google_2fprotobuf_2fcompiler_2fcpp_2fcpp_5ftest_5fbad_5fidentifiers_2eproto_impl();
  friend void  protobuf_AddDesc_google_2fprotobuf_2fcompiler_2fcpp_2fcpp_5ftest_5fbad_5fidentifiers_2eproto_impl();
  friend void protobuf_AssignDesc_google_2fprotobuf_2fcompiler_2fcpp_2fcpp_5ftest_5fbad_5fidentifiers_2eproto();
  friend void protobuf_ShutdownFile_google_2fprotobuf_2fcompiler_2fcpp_2fcpp_5ftest_5fbad_5fidentifiers_2eproto();

  void InitAsDefaultInstance();
};
extern ::google::protobuf::internal::ExplicitlyConstructed<TestConflictingSymbolNames_Data2> TestConflictingSymbolNames_Data2_default_instance_;

// -------------------------------------------------------------------

class TestConflictingSymbolNames_Data3 : public ::google::protobuf::Message /* @@protoc_insertion_point(class_definition:protobuf_unittest.TestConflictingSymbolNames.Data3) */ {
 public:
  TestConflictingSymbolNames_Data3();
  virtual ~TestConflictingSymbolNames_Data3();

  TestConflictingSymbolNames_Data3(const TestConflictingSymbolNames_Data3& from);

  inline TestConflictingSymbolNames_Data3& operator=(const TestConflictingSymbolNames_Data3& from) {
    CopyFrom(from);
    return *this;
  }

  inline const ::google::protobuf::UnknownFieldSet& unknown_fields() const {
    return _internal_metadata_.unknown_fields();
  }

  inline ::google::protobuf::UnknownFieldSet* mutable_unknown_fields() {
    return _internal_metadata_.mutable_unknown_fields();
  }

  static const ::google::protobuf::Descriptor* descriptor();
  static const TestConflictingSymbolNames_Data3& default_instance();

  static const TestConflictingSymbolNames_Data3* internal_default_instance();

  void Swap(TestConflictingSymbolNames_Data3* other);

  // implements Message ----------------------------------------------

  inline TestConflictingSymbolNames_Data3* New() const { return New(NULL); }

  TestConflictingSymbolNames_Data3* New(::google::protobuf::Arena* arena) const;
  void CopyFrom(const ::google::protobuf::Message& from);
  void MergeFrom(const ::google::protobuf::Message& from);
  void CopyFrom(const TestConflictingSymbolNames_Data3& from);
  void MergeFrom(const TestConflictingSymbolNames_Data3& from);
  void Clear();
  bool IsInitialized() const;

  size_t ByteSizeLong() const;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input);
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const;
  ::google::protobuf::uint8* InternalSerializeWithCachedSizesToArray(
      bool deterministic, ::google::protobuf::uint8* output) const;
  ::google::protobuf::uint8* SerializeWithCachedSizesToArray(::google::protobuf::uint8* output) const {
    return InternalSerializeWithCachedSizesToArray(false, output);
  }
  int GetCachedSize() const { return _cached_size_; }
  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const;
  void InternalSwap(TestConflictingSymbolNames_Data3* other);
  void UnsafeMergeFrom(const TestConflictingSymbolNames_Data3& from);
  private:
  inline ::google::protobuf::Arena* GetArenaNoVirtual() const {
    return _internal_metadata_.arena();
  }
  inline void* MaybeArenaPtr() const {
    return _internal_metadata_.raw_arena_ptr();
  }
  public:

  ::google::protobuf::Metadata GetMetadata() const;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // repeated string data = 1;
  int data_size() const;
  void clear_data();
  static const int kDataFieldNumber = 1;
  const ::std::string& data(int index) const;
  ::std::string* mutable_data(int index);
  void set_data(int index, const ::std::string& value);
  void set_data(int index, const char* value);
  void set_data(int index, const char* value, size_t size);
  ::std::string* add_data();
  void add_data(const ::std::string& value);
  void add_data(const char* value);
  void add_data(const char* value, size_t size);
  const ::google::protobuf::RepeatedPtrField< ::std::string>& data() const;
  ::google::protobuf::RepeatedPtrField< ::std::string>* mutable_data();

  // @@protoc_insertion_point(class_scope:protobuf_unittest.TestConflictingSymbolNames.Data3)
 private:

  ::google::protobuf::internal::InternalMetadataWithArena _internal_metadata_;
  ::google::protobuf::internal::HasBits<1> _has_bits_;
  mutable int _cached_size_;
  ::google::protobuf::RepeatedPtrField< ::std::string> data_;
  friend void  protobuf_InitDefaults_google_2fprotobuf_2fcompiler_2fcpp_2fcpp_5ftest_5fbad_5fidentifiers_2eproto_impl();
  friend void  protobuf_AddDesc_google_2fprotobuf_2fcompiler_2fcpp_2fcpp_5ftest_5fbad_5fidentifiers_2eproto_impl();
  friend void protobuf_AssignDesc_google_2fprotobuf_2fcompiler_2fcpp_2fcpp_5ftest_5fbad_5fidentifiers_2eproto();
  friend void protobuf_ShutdownFile_google_2fprotobuf_2fcompiler_2fcpp_2fcpp_5ftest_5fbad_5fidentifiers_2eproto();

  void InitAsDefaultInstance();
};
extern ::google::protobuf::internal::ExplicitlyConstructed<TestConflictingSymbolNames_Data3> TestConflictingSymbolNames_Data3_default_instance_;

// -------------------------------------------------------------------

class TestConflictingSymbolNames_Data4 : public ::google::protobuf::Message /* @@protoc_insertion_point(class_definition:protobuf_unittest.TestConflictingSymbolNames.Data4) */ {
 public:
  TestConflictingSymbolNames_Data4();
  virtual ~TestConflictingSymbolNames_Data4();

  TestConflictingSymbolNames_Data4(const TestConflictingSymbolNames_Data4& from);

  inline TestConflictingSymbolNames_Data4& operator=(const TestConflictingSymbolNames_Data4& from) {
    CopyFrom(from);
    return *this;
  }

  inline const ::google::protobuf::UnknownFieldSet& unknown_fields() const {
    return _internal_metadata_.unknown_fields();
  }

  inline ::google::protobuf::UnknownFieldSet* mutable_unknown_fields() {
    return _internal_metadata_.mutable_unknown_fields();
  }

  static const ::google::protobuf::Descriptor* descriptor();
  static const TestConflictingSymbolNames_Data4& default_instance();

  static const TestConflictingSymbolNames_Data4* internal_default_instance();

  void Swap(TestConflictingSymbolNames_Data4* other);

  // implements Message ----------------------------------------------

  inline TestConflictingSymbolNames_Data4* New() const { return New(NULL); }

  TestConflictingSymbolNames_Data4* New(::google::protobuf::Arena* arena) const;
  void CopyFrom(const ::google::protobuf::Message& from);
  void MergeFrom(const ::google::protobuf::Message& from);
  void CopyFrom(const TestConflictingSymbolNames_Data4& from);
  void MergeFrom(const TestConflictingSymbolNames_Data4& from);
  void Clear();
  bool IsInitialized() const;

  size_t ByteSizeLong() const;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input);
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const;
  ::google::protobuf::uint8* InternalSerializeWithCachedSizesToArray(
      bool deterministic, ::google::protobuf::uint8* output) const;
  ::google::protobuf::uint8* SerializeWithCachedSizesToArray(::google::protobuf::uint8* output) const {
    return InternalSerializeWithCachedSizesToArray(false, output);
  }
  int GetCachedSize() const { return _cached_size_; }
  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const;
  void InternalSwap(TestConflictingSymbolNames_Data4* other);
  void UnsafeMergeFrom(const TestConflictingSymbolNames_Data4& from);
  private:
  inline ::google::protobuf::Arena* GetArenaNoVirtual() const {
    return _internal_metadata_.arena();
  }
  inline void* MaybeArenaPtr() const {
    return _internal_metadata_.raw_arena_ptr();
  }
  public:

  ::google::protobuf::Metadata GetMetadata() const;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // repeated .protobuf_unittest.TestConflictingSymbolNames.Data4 data = 1;
  int data_size() const;
  void clear_data();
  static const int kDataFieldNumber = 1;
  const ::protobuf_unittest::TestConflictingSymbolNames_Data4& data(int index) const;
  ::protobuf_unittest::TestConflictingSymbolNames_Data4* mutable_data(int index);
  ::protobuf_unittest::TestConflictingSymbolNames_Data4* add_data();
  ::google::protobuf::RepeatedPtrField< ::protobuf_unittest::TestConflictingSymbolNames_Data4 >*
      mutable_data();
  const ::google::protobuf::RepeatedPtrField< ::protobuf_unittest::TestConflictingSymbolNames_Data4 >&
      data() const;

  // @@protoc_insertion_point(class_scope:protobuf_unittest.TestConflictingSymbolNames.Data4)
 private:

  ::google::protobuf::internal::InternalMetadataWithArena _internal_metadata_;
  ::google::protobuf::internal::HasBits<1> _has_bits_;
  mutable int _cached_size_;
  ::google::protobuf::RepeatedPtrField< ::protobuf_unittest::TestConflictingSymbolNames_Data4 > data_;
  friend void  protobuf_InitDefaults_google_2fprotobuf_2fcompiler_2fcpp_2fcpp_5ftest_5fbad_5fidentifiers_2eproto_impl();
  friend void  protobuf_AddDesc_google_2fprotobuf_2fcompiler_2fcpp_2fcpp_5ftest_5fbad_5fidentifiers_2eproto_impl();
  friend void protobuf_AssignDesc_google_2fprotobuf_2fcompiler_2fcpp_2fcpp_5ftest_5fbad_5fidentifiers_2eproto();
  friend void protobuf_ShutdownFile_google_2fprotobuf_2fcompiler_2fcpp_2fcpp_5ftest_5fbad_5fidentifiers_2eproto();

  void InitAsDefaultInstance();
};
extern ::google::protobuf::internal::ExplicitlyConstructed<TestConflictingSymbolNames_Data4> TestConflictingSymbolNames_Data4_default_instance_;

// -------------------------------------------------------------------

class TestConflictingSymbolNames_Data5 : public ::google::protobuf::Message /* @@protoc_insertion_point(class_definition:protobuf_unittest.TestConflictingSymbolNames.Data5) */ {
 public:
  TestConflictingSymbolNames_Data5();
  virtual ~TestConflictingSymbolNames_Data5();

  TestConflictingSymbolNames_Data5(const TestConflictingSymbolNames_Data5& from);

  inline TestConflictingSymbolNames_Data5& operator=(const TestConflictingSymbolNames_Data5& from) {
    CopyFrom(from);
    return *this;
  }

  inline const ::google::protobuf::UnknownFieldSet& unknown_fields() const {
    return _internal_metadata_.unknown_fields();
  }

  inline ::google::protobuf::UnknownFieldSet* mutable_unknown_fields() {
    return _internal_metadata_.mutable_unknown_fields();
  }

  static const ::google::protobuf::Descriptor* descriptor();
  static const TestConflictingSymbolNames_Data5& default_instance();

  static const TestConflictingSymbolNames_Data5* internal_default_instance();

  void Swap(TestConflictingSymbolNames_Data5* other);

  // implements Message ----------------------------------------------

  inline TestConflictingSymbolNames_Data5* New() const { return New(NULL); }

  TestConflictingSymbolNames_Data5* New(::google::protobuf::Arena* arena) const;
  void CopyFrom(const ::google::protobuf::Message& from);
  void MergeFrom(const ::google::protobuf::Message& from);
  void CopyFrom(const TestConflictingSymbolNames_Data5& from);
  void MergeFrom(const TestConflictingSymbolNames_Data5& from);
  void Clear();
  bool IsInitialized() const;

  size_t ByteSizeLong() const;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input);
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const;
  ::google::protobuf::uint8* InternalSerializeWithCachedSizesToArray(
      bool deterministic, ::google::protobuf::uint8* output) const;
  ::google::protobuf::uint8* SerializeWithCachedSizesToArray(::google::protobuf::uint8* output) const {
    return InternalSerializeWithCachedSizesToArray(false, output);
  }
  int GetCachedSize() const { return _cached_size_; }
  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const;
  void InternalSwap(TestConflictingSymbolNames_Data5* other);
  void UnsafeMergeFrom(const TestConflictingSymbolNames_Data5& from);
  private:
  inline ::google::protobuf::Arena* GetArenaNoVirtual() const {
    return _internal_metadata_.arena();
  }
  inline void* MaybeArenaPtr() const {
    return _internal_metadata_.raw_arena_ptr();
  }
  public:

  ::google::protobuf::Metadata GetMetadata() const;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // repeated string data = 1 [ctype = STRING_PIECE];
  int data_size() const;
  void clear_data();
  static const int kDataFieldNumber = 1;
 private:
  // Hidden due to unknown ctype option.
  const ::std::string& data(int index) const;
  ::std::string* mutable_data(int index);
  void set_data(int index, const ::std::string& value);
  void set_data(int index, const char* value);
  void set_data(int index, const char* value, size_t size);
  ::std::string* add_data();
  void add_data(const ::std::string& value);
  void add_data(const char* value);
  void add_data(const char* value, size_t size);
  const ::google::protobuf::RepeatedPtrField< ::std::string>& data() const;
  ::google::protobuf::RepeatedPtrField< ::std::string>* mutable_data();
 public:

  // @@protoc_insertion_point(class_scope:protobuf_unittest.TestConflictingSymbolNames.Data5)
 private:

  ::google::protobuf::internal::InternalMetadataWithArena _internal_metadata_;
  ::google::protobuf::internal::HasBits<1> _has_bits_;
  mutable int _cached_size_;
  ::google::protobuf::RepeatedPtrField< ::std::string> data_;
  friend void  protobuf_InitDefaults_google_2fprotobuf_2fcompiler_2fcpp_2fcpp_5ftest_5fbad_5fidentifiers_2eproto_impl();
  friend void  protobuf_AddDesc_google_2fprotobuf_2fcompiler_2fcpp_2fcpp_5ftest_5fbad_5fidentifiers_2eproto_impl();
  friend void protobuf_AssignDesc_google_2fprotobuf_2fcompiler_2fcpp_2fcpp_5ftest_5fbad_5fidentifiers_2eproto();
  friend void protobuf_ShutdownFile_google_2fprotobuf_2fcompiler_2fcpp_2fcpp_5ftest_5fbad_5fidentifiers_2eproto();

  void InitAsDefaultInstance();
};
extern ::google::protobuf::internal::ExplicitlyConstructed<TestConflictingSymbolNames_Data5> TestConflictingSymbolNames_Data5_default_instance_;

// -------------------------------------------------------------------

class TestConflictingSymbolNames_Data6 : public ::google::protobuf::Message /* @@protoc_insertion_point(class_definition:protobuf_unittest.TestConflictingSymbolNames.Data6) */ {
 public:
  TestConflictingSymbolNames_Data6();
  virtual ~TestConflictingSymbolNames_Data6();

  TestConflictingSymbolNames_Data6(const TestConflictingSymbolNames_Data6& from);

  inline TestConflictingSymbolNames_Data6& operator=(const TestConflictingSymbolNames_Data6& from) {
    CopyFrom(from);
    return *this;
  }

  inline const ::google::protobuf::UnknownFieldSet& unknown_fields() const {
    return _internal_metadata_.unknown_fields();
  }

  inline ::google::protobuf::UnknownFieldSet* mutable_unknown_fields() {
    return _internal_metadata_.mutable_unknown_fields();
  }

  static const ::google::protobuf::Descriptor* descriptor();
  static const TestConflictingSymbolNames_Data6& default_instance();

  static const TestConflictingSymbolNames_Data6* internal_default_instance();

  void Swap(TestConflictingSymbolNames_Data6* other);

  // implements Message ----------------------------------------------

  inline TestConflictingSymbolNames_Data6* New() const { return New(NULL); }

  TestConflictingSymbolNames_Data6* New(::google::protobuf::Arena* arena) const;
  void CopyFrom(const ::google::protobuf::Message& from);
  void MergeFrom(const ::google::protobuf::Message& from);
  void CopyFrom(const TestConflictingSymbolNames_Data6& from);
  void MergeFrom(const TestConflictingSymbolNames_Data6& from);
  void Clear();
  bool IsInitialized() const;

  size_t ByteSizeLong() const;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input);
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const;
  ::google::protobuf::uint8* InternalSerializeWithCachedSizesToArray(
      bool deterministic, ::google::protobuf::uint8* output) const;
  ::google::protobuf::uint8* SerializeWithCachedSizesToArray(::google::protobuf::uint8* output) const {
    return InternalSerializeWithCachedSizesToArray(false, output);
  }
  int GetCachedSize() const { return _cached_size_; }
  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const;
  void InternalSwap(TestConflictingSymbolNames_Data6* other);
  void UnsafeMergeFrom(const TestConflictingSymbolNames_Data6& from);
  private:
  inline ::google::protobuf::Arena* GetArenaNoVirtual() const {
    return _internal_metadata_.arena();
  }
  inline void* MaybeArenaPtr() const {
    return _internal_metadata_.raw_arena_ptr();
  }
  public:

  ::google::protobuf::Metadata GetMetadata() const;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // repeated string data = 1 [ctype = CORD];
  int data_size() const;
  void clear_data();
  static const int kDataFieldNumber = 1;
 private:
  // Hidden due to unknown ctype option.
  const ::std::string& data(int index) const;
  ::std::string* mutable_data(int index);
  void set_data(int index, const ::std::string& value);
  void set_data(int index, const char* value);
  void set_data(int index, const char* value, size_t size);
  ::std::string* add_data();
  void add_data(const ::std::string& value);
  void add_data(const char* value);
  void add_data(const char* value, size_t size);
  const ::google::protobuf::RepeatedPtrField< ::std::string>& data() const;
  ::google::protobuf::RepeatedPtrField< ::std::string>* mutable_data();
 public:

  // @@protoc_insertion_point(class_scope:protobuf_unittest.TestConflictingSymbolNames.Data6)
 private:

  ::google::protobuf::internal::InternalMetadataWithArena _internal_metadata_;
  ::google::protobuf::internal::HasBits<1> _has_bits_;
  mutable int _cached_size_;
  ::google::protobuf::RepeatedPtrField< ::std::string> data_;
  friend void  protobuf_InitDefaults_google_2fprotobuf_2fcompiler_2fcpp_2fcpp_5ftest_5fbad_5fidentifiers_2eproto_impl();
  friend void  protobuf_AddDesc_google_2fprotobuf_2fcompiler_2fcpp_2fcpp_5ftest_5fbad_5fidentifiers_2eproto_impl();
  friend void protobuf_AssignDesc_google_2fprotobuf_2fcompiler_2fcpp_2fcpp_5ftest_5fbad_5fidentifiers_2eproto();
  friend void protobuf_ShutdownFile_google_2fprotobuf_2fcompiler_2fcpp_2fcpp_5ftest_5fbad_5fidentifiers_2eproto();

  void InitAsDefaultInstance();
};
extern ::google::protobuf::internal::ExplicitlyConstructed<TestConflictingSymbolNames_Data6> TestConflictingSymbolNames_Data6_default_instance_;

// -------------------------------------------------------------------

class TestConflictingSymbolNames_Cord : public ::google::protobuf::Message /* @@protoc_insertion_point(class_definition:protobuf_unittest.TestConflictingSymbolNames.Cord) */ {
 public:
  TestConflictingSymbolNames_Cord();
  virtual ~TestConflictingSymbolNames_Cord();

  TestConflictingSymbolNames_Cord(const TestConflictingSymbolNames_Cord& from);

  inline TestConflictingSymbolNames_Cord& operator=(const TestConflictingSymbolNames_Cord& from) {
    CopyFrom(from);
    return *this;
  }

  inline const ::google::protobuf::UnknownFieldSet& unknown_fields() const {
    return _internal_metadata_.unknown_fields();
  }

  inline ::google::protobuf::UnknownFieldSet* mutable_unknown_fields() {
    return _internal_metadata_.mutable_unknown_fields();
  }

  static const ::google::protobuf::Descriptor* descriptor();
  static const TestConflictingSymbolNames_Cord& default_instance();

  static const TestConflictingSymbolNames_Cord* internal_default_instance();

  void Swap(TestConflictingSymbolNames_Cord* other);

  // implements Message ----------------------------------------------

  inline TestConflictingSymbolNames_Cord* New() const { return New(NULL); }

  TestConflictingSymbolNames_Cord* New(::google::protobuf::Arena* arena) const;
  void CopyFrom(const ::google::protobuf::Message& from);
  void MergeFrom(const ::google::protobuf::Message& from);
  void CopyFrom(const TestConflictingSymbolNames_Cord& from);
  void MergeFrom(const TestConflictingSymbolNames_Cord& from);
  void Clear();
  bool IsInitialized() const;

  size_t ByteSizeLong() const;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input);
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const;
  ::google::protobuf::uint8* InternalSerializeWithCachedSizesToArray(
      bool deterministic, ::google::protobuf::uint8* output) const;
  ::google::protobuf::uint8* SerializeWithCachedSizesToArray(::google::protobuf::uint8* output) const {
    return InternalSerializeWithCachedSizesToArray(false, output);
  }
  int GetCachedSize() const { return _cached_size_; }
  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const;
  void InternalSwap(TestConflictingSymbolNames_Cord* other);
  void UnsafeMergeFrom(const TestConflictingSymbolNames_Cord& from);
  private:
  inline ::google::protobuf::Arena* GetArenaNoVirtual() const {
    return _internal_metadata_.arena();
  }
  inline void* MaybeArenaPtr() const {
    return _internal_metadata_.raw_arena_ptr();
  }
  public:

  ::google::protobuf::Metadata GetMetadata() const;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // @@protoc_insertion_point(class_scope:protobuf_unittest.TestConflictingSymbolNames.Cord)
 private:

  ::google::protobuf::internal::InternalMetadataWithArena _internal_metadata_;
  ::google::protobuf::internal::HasBits<1> _has_bits_;
  mutable int _cached_size_;
  friend void  protobuf_InitDefaults_google_2fprotobuf_2fcompiler_2fcpp_2fcpp_5ftest_5fbad_5fidentifiers_2eproto_impl();
  friend void  protobuf_AddDesc_google_2fprotobuf_2fcompiler_2fcpp_2fcpp_5ftest_5fbad_5fidentifiers_2eproto_impl();
  friend void protobuf_AssignDesc_google_2fprotobuf_2fcompiler_2fcpp_2fcpp_5ftest_5fbad_5fidentifiers_2eproto();
  friend void protobuf_ShutdownFile_google_2fprotobuf_2fcompiler_2fcpp_2fcpp_5ftest_5fbad_5fidentifiers_2eproto();

  void InitAsDefaultInstance();
};
extern ::google::protobuf::internal::ExplicitlyConstructed<TestConflictingSymbolNames_Cord> TestConflictingSymbolNames_Cord_default_instance_;

// -------------------------------------------------------------------

class TestConflictingSymbolNames_StringPiece : public ::google::protobuf::Message /* @@protoc_insertion_point(class_definition:protobuf_unittest.TestConflictingSymbolNames.StringPiece) */ {
 public:
  TestConflictingSymbolNames_StringPiece();
  virtual ~TestConflictingSymbolNames_StringPiece();

  TestConflictingSymbolNames_StringPiece(const TestConflictingSymbolNames_StringPiece& from);

  inline TestConflictingSymbolNames_StringPiece& operator=(const TestConflictingSymbolNames_StringPiece& from) {
    CopyFrom(from);
    return *this;
  }

  inline const ::google::protobuf::UnknownFieldSet& unknown_fields() const {
    return _internal_metadata_.unknown_fields();
  }

  inline ::google::protobuf::UnknownFieldSet* mutable_unknown_fields() {
    return _internal_metadata_.mutable_unknown_fields();
  }

  static const ::google::protobuf::Descriptor* descriptor();
  static const TestConflictingSymbolNames_StringPiece& default_instance();

  static const TestConflictingSymbolNames_StringPiece* internal_default_instance();

  void Swap(TestConflictingSymbolNames_StringPiece* other);

  // implements Message ----------------------------------------------

  inline TestConflictingSymbolNames_StringPiece* New() const { return New(NULL); }

  TestConflictingSymbolNames_StringPiece* New(::google::protobuf::Arena* arena) const;
  void CopyFrom(const ::google::protobuf::Message& from);
  void MergeFrom(const ::google::protobuf::Message& from);
  void CopyFrom(const TestConflictingSymbolNames_StringPiece& from);
  void MergeFrom(const TestConflictingSymbolNames_StringPiece& from);
  void Clear();
  bool IsInitialized() const;

  size_t ByteSizeLong() const;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input);
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const;
  ::google::protobuf::uint8* InternalSerializeWithCachedSizesToArray(
      bool deterministic, ::google::protobuf::uint8* output) const;
  ::google::protobuf::uint8* SerializeWithCachedSizesToArray(::google::protobuf::uint8* output) const {
    return InternalSerializeWithCachedSizesToArray(false, output);
  }
  int GetCachedSize() const { return _cached_size_; }
  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const;
  void InternalSwap(TestConflictingSymbolNames_StringPiece* other);
  void UnsafeMergeFrom(const TestConflictingSymbolNames_StringPiece& from);
  private:
  inline ::google::protobuf::Arena* GetArenaNoVirtual() const {
    return _internal_metadata_.arena();
  }
  inline void* MaybeArenaPtr() const {
    return _internal_metadata_.raw_arena_ptr();
  }
  public:

  ::google::protobuf::Metadata GetMetadata() const;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // @@protoc_insertion_point(class_scope:protobuf_unittest.TestConflictingSymbolNames.StringPiece)
 private:

  ::google::protobuf::internal::InternalMetadataWithArena _internal_metadata_;
  ::google::protobuf::internal::HasBits<1> _has_bits_;
  mutable int _cached_size_;
  friend void  protobuf_InitDefaults_google_2fprotobuf_2fcompiler_2fcpp_2fcpp_5ftest_5fbad_5fidentifiers_2eproto_impl();
  friend void  protobuf_AddDesc_google_2fprotobuf_2fcompiler_2fcpp_2fcpp_5ftest_5fbad_5fidentifiers_2eproto_impl();
  friend void protobuf_AssignDesc_google_2fprotobuf_2fcompiler_2fcpp_2fcpp_5ftest_5fbad_5fidentifiers_2eproto();
  friend void protobuf_ShutdownFile_google_2fprotobuf_2fcompiler_2fcpp_2fcpp_5ftest_5fbad_5fidentifiers_2eproto();

  void InitAsDefaultInstance();
};
extern ::google::protobuf::internal::ExplicitlyConstructed<TestConflictingSymbolNames_StringPiece> TestConflictingSymbolNames_StringPiece_default_instance_;

// -------------------------------------------------------------------

class TestConflictingSymbolNames_DO : public ::google::protobuf::Message /* @@protoc_insertion_point(class_definition:protobuf_unittest.TestConflictingSymbolNames.DO) */ {
 public:
  TestConflictingSymbolNames_DO();
  virtual ~TestConflictingSymbolNames_DO();

  TestConflictingSymbolNames_DO(const TestConflictingSymbolNames_DO& from);

  inline TestConflictingSymbolNames_DO& operator=(const TestConflictingSymbolNames_DO& from) {
    CopyFrom(from);
    return *this;
  }

  inline const ::google::protobuf::UnknownFieldSet& unknown_fields() const {
    return _internal_metadata_.unknown_fields();
  }

  inline ::google::protobuf::UnknownFieldSet* mutable_unknown_fields() {
    return _internal_metadata_.mutable_unknown_fields();
  }

  static const ::google::protobuf::Descriptor* descriptor();
  static const TestConflictingSymbolNames_DO& default_instance();

  static const TestConflictingSymbolNames_DO* internal_default_instance();

  void Swap(TestConflictingSymbolNames_DO* other);

  // implements Message ----------------------------------------------

  inline TestConflictingSymbolNames_DO* New() const { return New(NULL); }

  TestConflictingSymbolNames_DO* New(::google::protobuf::Arena* arena) const;
  void CopyFrom(const ::google::protobuf::Message& from);
  void MergeFrom(const ::google::protobuf::Message& from);
  void CopyFrom(const TestConflictingSymbolNames_DO& from);
  void MergeFrom(const TestConflictingSymbolNames_DO& from);
  void Clear();
  bool IsInitialized() const;

  size_t ByteSizeLong() const;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input);
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const;
  ::google::protobuf::uint8* InternalSerializeWithCachedSizesToArray(
      bool deterministic, ::google::protobuf::uint8* output) const;
  ::google::protobuf::uint8* SerializeWithCachedSizesToArray(::google::protobuf::uint8* output) const {
    return InternalSerializeWithCachedSizesToArray(false, output);
  }
  int GetCachedSize() const { return _cached_size_; }
  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const;
  void InternalSwap(TestConflictingSymbolNames_DO* other);
  void UnsafeMergeFrom(const TestConflictingSymbolNames_DO& from);
  private:
  inline ::google::protobuf::Arena* GetArenaNoVirtual() const {
    return _internal_metadata_.arena();
  }
  inline void* MaybeArenaPtr() const {
    return _internal_metadata_.raw_arena_ptr();
  }
  public:

  ::google::protobuf::Metadata GetMetadata() const;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // @@protoc_insertion_point(class_scope:protobuf_unittest.TestConflictingSymbolNames.DO)
 private:

  ::google::protobuf::internal::InternalMetadataWithArena _internal_metadata_;
  ::google::protobuf::internal::HasBits<1> _has_bits_;
  mutable int _cached_size_;
  friend void  protobuf_InitDefaults_google_2fprotobuf_2fcompiler_2fcpp_2fcpp_5ftest_5fbad_5fidentifiers_2eproto_impl();
  friend void  protobuf_AddDesc_google_2fprotobuf_2fcompiler_2fcpp_2fcpp_5ftest_5fbad_5fidentifiers_2eproto_impl();
  friend void protobuf_AssignDesc_google_2fprotobuf_2fcompiler_2fcpp_2fcpp_5ftest_5fbad_5fidentifiers_2eproto();
  friend void protobuf_ShutdownFile_google_2fprotobuf_2fcompiler_2fcpp_2fcpp_5ftest_5fbad_5fidentifiers_2eproto();

  void InitAsDefaultInstance();
};
extern ::google::protobuf::internal::ExplicitlyConstructed<TestConflictingSymbolNames_DO> TestConflictingSymbolNames_DO_default_instance_;

// -------------------------------------------------------------------

class TestConflictingSymbolNames : public ::google::protobuf::Message /* @@protoc_insertion_point(class_definition:protobuf_unittest.TestConflictingSymbolNames) */ {
 public:
  TestConflictingSymbolNames();
  virtual ~TestConflictingSymbolNames();

  TestConflictingSymbolNames(const TestConflictingSymbolNames& from);

  inline TestConflictingSymbolNames& operator=(const TestConflictingSymbolNames& from) {
    CopyFrom(from);
    return *this;
  }

  inline const ::google::protobuf::UnknownFieldSet& unknown_fields() const {
    return _internal_metadata_.unknown_fields();
  }

  inline ::google::protobuf::UnknownFieldSet* mutable_unknown_fields() {
    return _internal_metadata_.mutable_unknown_fields();
  }

  static const ::google::protobuf::Descriptor* descriptor();
  static const TestConflictingSymbolNames& default_instance();

  static const TestConflictingSymbolNames* internal_default_instance();

  void Swap(TestConflictingSymbolNames* other);

  // implements Message ----------------------------------------------

  inline TestConflictingSymbolNames* New() const { return New(NULL); }

  TestConflictingSymbolNames* New(::google::protobuf::Arena* arena) const;
  void CopyFrom(const ::google::protobuf::Message& from);
  void MergeFrom(const ::google::protobuf::Message& from);
  void CopyFrom(const TestConflictingSymbolNames& from);
  void MergeFrom(const TestConflictingSymbolNames& from);
  void Clear();
  bool IsInitialized() const;

  size_t ByteSizeLong() const;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input);
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const;
  ::google::protobuf::uint8* InternalSerializeWithCachedSizesToArray(
      bool deterministic, ::google::protobuf::uint8* output) const;
  ::google::protobuf::uint8* SerializeWithCachedSizesToArray(::google::protobuf::uint8* output) const {
    return InternalSerializeWithCachedSizesToArray(false, output);
  }
  int GetCachedSize() const { return _cached_size_; }
  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const;
  void InternalSwap(TestConflictingSymbolNames* other);
  void UnsafeMergeFrom(const TestConflictingSymbolNames& from);
  private:
  inline ::google::protobuf::Arena* GetArenaNoVirtual() const {
    return _internal_metadata_.arena();
  }
  inline void* MaybeArenaPtr() const {
    return _internal_metadata_.raw_arena_ptr();
  }
  public:

  ::google::protobuf::Metadata GetMetadata() const;

  // nested types ----------------------------------------------------

  typedef TestConflictingSymbolNames_BuildDescriptors BuildDescriptors;
  typedef TestConflictingSymbolNames_TypeTraits TypeTraits;
  typedef TestConflictingSymbolNames_Data1 Data1;
  typedef TestConflictingSymbolNames_Data2 Data2;
  typedef TestConflictingSymbolNames_Data3 Data3;
  typedef TestConflictingSymbolNames_Data4 Data4;
  typedef TestConflictingSymbolNames_Data5 Data5;
  typedef TestConflictingSymbolNames_Data6 Data6;
  typedef TestConflictingSymbolNames_Cord Cord;
  typedef TestConflictingSymbolNames_StringPiece StringPiece;
  typedef TestConflictingSymbolNames_DO DO;

  typedef TestConflictingSymbolNames_TestEnum TestEnum;
  static const TestEnum FOO =
    TestConflictingSymbolNames_TestEnum_FOO;
  static inline bool TestEnum_IsValid(int value) {
    return TestConflictingSymbolNames_TestEnum_IsValid(value);
  }
  static const TestEnum TestEnum_MIN =
    TestConflictingSymbolNames_TestEnum_TestEnum_MIN;
  static const TestEnum TestEnum_MAX =
    TestConflictingSymbolNames_TestEnum_TestEnum_MAX;
  static const int TestEnum_ARRAYSIZE =
    TestConflictingSymbolNames_TestEnum_TestEnum_ARRAYSIZE;
  static inline const ::google::protobuf::EnumDescriptor*
  TestEnum_descriptor() {
    return TestConflictingSymbolNames_TestEnum_descriptor();
  }
  static inline const ::std::string& TestEnum_Name(TestEnum value) {
    return TestConflictingSymbolNames_TestEnum_Name(value);
  }
  static inline bool TestEnum_Parse(const ::std::string& name,
      TestEnum* value) {
    return TestConflictingSymbolNames_TestEnum_Parse(name, value);
  }

  // accessors -------------------------------------------------------

  // optional int32 input = 1;
  bool has_input() const;
  void clear_input();
  static const int kInputFieldNumber = 1;
  ::google::protobuf::int32 input() const;
  void set_input(::google::protobuf::int32 value);

  // optional int32 output = 2;
  bool has_output() const;
  void clear_output();
  static const int kOutputFieldNumber = 2;
  ::google::protobuf::int32 output() const;
  void set_output(::google::protobuf::int32 value);

  // optional string length = 3;
  bool has_length() const;
  void clear_length();
  static const int kLengthFieldNumber = 3;
  const ::std::string& length() const;
  void set_length(const ::std::string& value);
  void set_length(const char* value);
  void set_length(const char* value, size_t size);
  ::std::string* mutable_length();
  ::std::string* release_length__();
  void set_allocated_length(::std::string* length);

  // repeated int32 i = 4;
  int i_size() const;
  void clear_i();
  static const int kIFieldNumber = 4;
  ::google::protobuf::int32 i(int index) const;
  void set_i(int index, ::google::protobuf::int32 value);
  void add_i(::google::protobuf::int32 value);
  const ::google::protobuf::RepeatedField< ::google::protobuf::int32 >&
      i() const;
  ::google::protobuf::RepeatedField< ::google::protobuf::int32 >*
      mutable_i();

  // repeated string new_element = 5 [ctype = STRING_PIECE];
  int new_element_size() const;
  void clear_new_element();
  static const int kNewElementFieldNumber = 5;
 private:
  // Hidden due to unknown ctype option.
  const ::std::string& new_element(int index) const;
  ::std::string* mutable_new_element(int index);
  void set_new_element(int index, const ::std::string& value);
  void set_new_element(int index, const char* value);
  void set_new_element(int index, const char* value, size_t size);
  ::std::string* add_new_element();
  void add_new_element(const ::std::string& value);
  void add_new_element(const char* value);
  void add_new_element(const char* value, size_t size);
  const ::google::protobuf::RepeatedPtrField< ::std::string>& new_element() const;
  ::google::protobuf::RepeatedPtrField< ::std::string>* mutable_new_element();
 public:

  // optional int32 total_size = 6;
  bool has_total_size() const;
  void clear_total_size();
  static const int kTotalSizeFieldNumber = 6;
  ::google::protobuf::int32 total_size() const;
  void set_total_size(::google::protobuf::int32 value);

  // optional int32 tag = 7;
  bool has_tag() const;
  void clear_tag();
  static const int kTagFieldNumber = 7;
  ::google::protobuf::int32 tag() const;
  void set_tag(::google::protobuf::int32 value);

  // optional int32 source = 8;
  bool has_source() const;
  void clear_source();
  static const int kSourceFieldNumber = 8;
  ::google::protobuf::int32 source() const;
  void set_source(::google::protobuf::int32 value);

  // optional int32 value = 9;
  bool has_value() const;
  void clear_value();
  static const int kValueFieldNumber = 9;
  ::google::protobuf::int32 value() const;
  void set_value(::google::protobuf::int32 value);

  // optional int32 file = 10;
  bool has_file() const;
  void clear_file();
  static const int kFileFieldNumber = 10;
  ::google::protobuf::int32 file() const;
  void set_file(::google::protobuf::int32 value);

  // optional int32 from = 11;
  bool has_from() const;
  void clear_from();
  static const int kFromFieldNumber = 11;
  ::google::protobuf::int32 from() const;
  void set_from(::google::protobuf::int32 value);

  // optional int32 handle_uninterpreted = 12;
  bool has_handle_uninterpreted() const;
  void clear_handle_uninterpreted();
  static const int kHandleUninterpretedFieldNumber = 12;
  ::google::protobuf::int32 handle_uninterpreted() const;
  void set_handle_uninterpreted(::google::protobuf::int32 value);

  // repeated int32 index = 13;
  int index_size() const;
  void clear_index();
  static const int kIndexFieldNumber = 13;
  ::google::protobuf::int32 index(int index) const;
  void set_index(int index, ::google::protobuf::int32 value);
  void add_index(::google::protobuf::int32 value);
  const ::google::protobuf::RepeatedField< ::google::protobuf::int32 >&
      index() const;
  ::google::protobuf::RepeatedField< ::google::protobuf::int32 >*
      mutable_index();

  // optional int32 controller = 14;
  bool has_controller() const;
  void clear_controller();
  static const int kControllerFieldNumber = 14;
  ::google::protobuf::int32 controller() const;
  void set_controller(::google::protobuf::int32 value);

  // optional int32 already_here = 15;
  bool has_already_here() const;
  void clear_already_here();
  static const int kAlreadyHereFieldNumber = 15;
  ::google::protobuf::int32 already_here() const;
  void set_already_here(::google::protobuf::int32 value);

  // optional uint32 uint32 = 16;
  bool has_uint32() const;
  void clear_uint32();
  static const int kUint32FieldNumber = 16;
  ::google::protobuf::uint32 uint32() const;
  void set_uint32(::google::protobuf::uint32 value);

  // optional uint64 uint64 = 17;
  bool has_uint64() const;
  void clear_uint64();
  static const int kUint64FieldNumber = 17;
  ::google::protobuf::uint64 uint64() const;
  void set_uint64(::google::protobuf::uint64 value);

  // optional string string = 18;
  bool has_string() const;
  void clear_string();
  static const int kStringFieldNumber = 18;
  const ::std::string& string() const;
  void set_string(const ::std::string& value);
  void set_string(const char* value);
  void set_string(const char* value, size_t size);
  ::std::string* mutable_string();
  ::std::string* release_string();
  void set_allocated_string(::std::string* string);

  // optional int32 memset = 19;
  bool has_memset() const;
  void clear_memset();
  static const int kMemsetFieldNumber = 19;
  ::google::protobuf::int32 memset() const;
  void set_memset(::google::protobuf::int32 value);

  // optional int32 int32 = 20;
  bool has_int32() const;
  void clear_int32();
  static const int kInt32FieldNumber = 20;
  ::google::protobuf::int32 int32() const;
  void set_int32(::google::protobuf::int32 value);

  // optional int64 int64 = 21;
  bool has_int64() const;
  void clear_int64();
  static const int kInt64FieldNumber = 21;
  ::google::protobuf::int64 int64() const;
  void set_int64(::google::protobuf::int64 value);

  // optional uint32 cached_size = 22;
  bool has_cached_size() const;
  void clear_cached_size();
  static const int kCachedSizeFieldNumber = 22;
  ::google::protobuf::uint32 cached_size() const;
  void set_cached_size(::google::protobuf::uint32 value);

  // optional uint32 extensions = 23;
  bool has_extensions() const;
  void clear_extensions();
  static const int kExtensionsFieldNumber = 23;
  ::google::protobuf::uint32 extensions() const;
  void set_extensions(::google::protobuf::uint32 value);

  // optional uint32 bit = 24;
  bool has_bit() const;
  void clear_bit();
  static const int kBitFieldNumber = 24;
  ::google::protobuf::uint32 bit() const;
  void set_bit(::google::protobuf::uint32 value);

  // optional uint32 bits = 25;
  bool has_bits() const;
  void clear_bits();
  static const int kBitsFieldNumber = 25;
  ::google::protobuf::uint32 bits() const;
  void set_bits(::google::protobuf::uint32 value);

  // optional uint32 offsets = 26;
  bool has_offsets() const;
  void clear_offsets();
  static const int kOffsetsFieldNumber = 26;
  ::google::protobuf::uint32 offsets() const;
  void set_offsets(::google::protobuf::uint32 value);

  // optional uint32 reflection = 27;
  bool has_reflection() const;
  void clear_reflection();
  static const int kReflectionFieldNumber = 27;
  ::google::protobuf::uint32 reflection() const;
  void set_reflection(::google::protobuf::uint32 value);

  // optional string some_cord = 28 [ctype = CORD];
  bool has_some_cord() const;
  void clear_some_cord();
  static const int kSomeCordFieldNumber = 28;
 private:
  // Hidden due to unknown ctype option.
  const ::std::string& some_cord() const;
  void set_some_cord(const ::std::string& value);
  void set_some_cord(const char* value);
  void set_some_cord(const char* value, size_t size);
  ::std::string* mutable_some_cord();
  ::std::string* release_some_cord();
  void set_allocated_some_cord(::std::string* some_cord);
 public:

  // optional string some_string_piece = 29 [ctype = STRING_PIECE];
  bool has_some_string_piece() const;
  void clear_some_string_piece();
  static const int kSomeStringPieceFieldNumber = 29;
 private:
  // Hidden due to unknown ctype option.
  const ::std::string& some_string_piece() const;
  void set_some_string_piece(const ::std::string& value);
  void set_some_string_piece(const char* value);
  void set_some_string_piece(const char* value, size_t size);
  ::std::string* mutable_some_string_piece();
  ::std::string* release_some_string_piece();
  void set_allocated_some_string_piece(::std::string* some_string_piece);
 public:

  // optional uint32 int = 30;
  bool has_int_() const;
  void clear_int_();
  static const int kIntFieldNumber = 30;
  ::google::protobuf::uint32 int_() const;
  void set_int_(::google::protobuf::uint32 value);

  // optional uint32 friend = 31;
  bool has_friend_() const;
  void clear_friend_();
  static const int kFriendFieldNumber = 31;
  ::google::protobuf::uint32 friend_() const;
  void set_friend_(::google::protobuf::uint32 value);

  // optional uint32 class = 37;
  bool has_class_() const;
  void clear_class_();
  static const int kClassFieldNumber = 37;
  ::google::protobuf::uint32 class_() const;
  void set_class_(::google::protobuf::uint32 value);

  // optional uint32 typedecl = 39;
  bool has_typedecl() const;
  void clear_typedecl();
  static const int kTypedeclFieldNumber = 39;
  ::google::protobuf::uint32 typedecl() const;
  void set_typedecl(::google::protobuf::uint32 value);

  // optional uint32 auto = 40;
  bool has_auto_() const;
  void clear_auto_();
  static const int kAutoFieldNumber = 40;
  ::google::protobuf::uint32 auto_() const;
  void set_auto_(::google::protobuf::uint32 value);

  // optional .protobuf_unittest.TestConflictingSymbolNames.DO do = 32;
  bool has_do_() const;
  void clear_do_();
  static const int kDoFieldNumber = 32;
  const ::protobuf_unittest::TestConflictingSymbolNames_DO& do_() const;
  ::protobuf_unittest::TestConflictingSymbolNames_DO* mutable_do_();
  ::protobuf_unittest::TestConflictingSymbolNames_DO* release_do__();
  void set_allocated_do_(::protobuf_unittest::TestConflictingSymbolNames_DO* do_);

  // optional int32 field_type = 33;
  bool has_field_type() const;
  void clear_field_type();
  static const int kFieldTypeFieldNumber = 33;
  ::google::protobuf::int32 field_type() const;
  void set_field_type(::google::protobuf::int32 value);

  // optional bool is_packed = 34;
  bool has_is_packed() const;
  void clear_is_packed();
  static const int kIsPackedFieldNumber = 34;
  bool is_packed() const;
  void set_is_packed(bool value);

  // optional string release_length = 35;
  bool has_release_length() const;
  void clear_release_length();
  static const int kReleaseLengthFieldNumber = 35;
  const ::std::string& release_length() const;
  void set_release_length(const ::std::string& value);
  void set_release_length(const char* value);
  void set_release_length(const char* value, size_t size);
  ::std::string* mutable_release_length();
  ::std::string* release_release_length();
  void set_allocated_release_length(::std::string* release_length);

  // optional .protobuf_unittest.TestConflictingSymbolNames.DO release_do = 36;
  bool has_release_do() const;
  void clear_release_do();
  static const int kReleaseDoFieldNumber = 36;
  const ::protobuf_unittest::TestConflictingSymbolNames_DO& release_do() const;
  ::protobuf_unittest::TestConflictingSymbolNames_DO* mutable_release_do();
  ::protobuf_unittest::TestConflictingSymbolNames_DO* release_release_do();
  void set_allocated_release_do(::protobuf_unittest::TestConflictingSymbolNames_DO* release_do);

  // optional string target = 38;
  bool has_target() const;
  void clear_target();
  static const int kTargetFieldNumber = 38;
  const ::std::string& target() const;
  void set_target(const ::std::string& value);
  void set_target(const char* value);
  void set_target(const char* value, size_t size);
  ::std::string* mutable_target();
  ::std::string* release_target();
  void set_allocated_target(::std::string* target);

  GOOGLE_PROTOBUF_EXTENSION_ACCESSORS(TestConflictingSymbolNames)
  // @@protoc_insertion_point(class_scope:protobuf_unittest.TestConflictingSymbolNames)
 private:
  inline void set_has_input();
  inline void clear_has_input();
  inline void set_has_output();
  inline void clear_has_output();
  inline void set_has_length();
  inline void clear_has_length();
  inline void set_has_total_size();
  inline void clear_has_total_size();
  inline void set_has_tag();
  inline void clear_has_tag();
  inline void set_has_source();
  inline void clear_has_source();
  inline void set_has_value();
  inline void clear_has_value();
  inline void set_has_file();
  inline void clear_has_file();
  inline void set_has_from();
  inline void clear_has_from();
  inline void set_has_handle_uninterpreted();
  inline void clear_has_handle_uninterpreted();
  inline void set_has_controller();
  inline void clear_has_controller();
  inline void set_has_already_here();
  inline void clear_has_already_here();
  inline void set_has_uint32();
  inline void clear_has_uint32();
  inline void set_has_uint64();
  inline void clear_has_uint64();
  inline void set_has_string();
  inline void clear_has_string();
  inline void set_has_memset();
  inline void clear_has_memset();
  inline void set_has_int32();
  inline void clear_has_int32();
  inline void set_has_int64();
  inline void clear_has_int64();
  inline void set_has_cached_size();
  inline void clear_has_cached_size();
  inline void set_has_extensions();
  inline void clear_has_extensions();
  inline void set_has_bit();
  inline void clear_has_bit();
  inline void set_has_bits();
  inline void clear_has_bits();
  inline void set_has_offsets();
  inline void clear_has_offsets();
  inline void set_has_reflection();
  inline void clear_has_reflection();
  inline void set_has_some_cord();
  inline void clear_has_some_cord();
  inline void set_has_some_string_piece();
  inline void clear_has_some_string_piece();
  inline void set_has_int_();
  inline void clear_has_int_();
  inline void set_has_friend_();
  inline void clear_has_friend_();
  inline void set_has_class_();
  inline void clear_has_class_();
  inline void set_has_typedecl();
  inline void clear_has_typedecl();
  inline void set_has_auto_();
  inline void clear_has_auto_();
  inline void set_has_do_();
  inline void clear_has_do_();
  inline void set_has_field_type();
  inline void clear_has_field_type();
  inline void set_has_is_packed();
  inline void clear_has_is_packed();
  inline void set_has_release_length();
  inline void clear_has_release_length();
  inline void set_has_release_do();
  inline void clear_has_release_do();
  inline void set_has_target();
  inline void clear_has_target();

  ::google::protobuf::internal::ExtensionSet _extensions_;

  ::google::protobuf::internal::InternalMetadataWithArena _internal_metadata_;
  ::google::protobuf::internal::HasBits<2> _has_bits_;
  ::google::protobuf::RepeatedField< ::google::protobuf::int32 > i_;
  ::google::protobuf::RepeatedPtrField< ::std::string> new_element_;
  ::google::protobuf::RepeatedField< ::google::protobuf::int32 > index_;
  ::google::protobuf::internal::ArenaStringPtr length_;
  ::google::protobuf::internal::ArenaStringPtr string_;
  ::google::protobuf::internal::ArenaStringPtr some_cord_;
  ::google::protobuf::internal::ArenaStringPtr some_string_piece_;
  ::google::protobuf::internal::ArenaStringPtr release_length_;
  ::google::protobuf::internal::ArenaStringPtr target_;
  ::protobuf_unittest::TestConflictingSymbolNames_DO* do__;
  ::protobuf_unittest::TestConflictingSymbolNames_DO* release_do_;
  ::google::protobuf::int32 input_;
  ::google::protobuf::int32 output_;
  ::google::protobuf::int32 total_size_;
  ::google::protobuf::int32 tag_;
  ::google::protobuf::int32 source_;
  ::google::protobuf::int32 value_;
  ::google::protobuf::int32 file_;
  ::google::protobuf::int32 from_;
  ::google::protobuf::int32 handle_uninterpreted_;
  ::google::protobuf::int32 controller_;
  ::google::protobuf::int32 already_here_;
  ::google::protobuf::uint32 uint32_;
  ::google::protobuf::uint64 uint64_;
  ::google::protobuf::int32 memset_;
  ::google::protobuf::int32 int32_;
  ::google::protobuf::int64 int64_;
  ::google::protobuf::uint32 cached_size_;
  ::google::protobuf::uint32 extensions_;
  ::google::protobuf::uint32 bit_;
  ::google::protobuf::uint32 bits_;
  ::google::protobuf::uint32 offsets_;
  ::google::protobuf::uint32 reflection_;
  ::google::protobuf::uint32 int__;
  ::google::protobuf::uint32 friend__;
  ::google::protobuf::uint32 class__;
  ::google::protobuf::uint32 typedecl_;
  ::google::protobuf::uint32 auto__;
  ::google::protobuf::int32 field_type_;
  bool is_packed_;
  mutable int _cached_size_;
  friend void  protobuf_InitDefaults_google_2fprotobuf_2fcompiler_2fcpp_2fcpp_5ftest_5fbad_5fidentifiers_2eproto_impl();
  friend void  protobuf_AddDesc_google_2fprotobuf_2fcompiler_2fcpp_2fcpp_5ftest_5fbad_5fidentifiers_2eproto_impl();
  friend void protobuf_AssignDesc_google_2fprotobuf_2fcompiler_2fcpp_2fcpp_5ftest_5fbad_5fidentifiers_2eproto();
  friend void protobuf_ShutdownFile_google_2fprotobuf_2fcompiler_2fcpp_2fcpp_5ftest_5fbad_5fidentifiers_2eproto();

  void InitAsDefaultInstance();
};
extern ::google::protobuf::internal::ExplicitlyConstructed<TestConflictingSymbolNames> TestConflictingSymbolNames_default_instance_;

// -------------------------------------------------------------------

class TestConflictingSymbolNamesExtension : public ::google::protobuf::Message /* @@protoc_insertion_point(class_definition:protobuf_unittest.TestConflictingSymbolNamesExtension) */ {
 public:
  TestConflictingSymbolNamesExtension();
  virtual ~TestConflictingSymbolNamesExtension();

  TestConflictingSymbolNamesExtension(const TestConflictingSymbolNamesExtension& from);

  inline TestConflictingSymbolNamesExtension& operator=(const TestConflictingSymbolNamesExtension& from) {
    CopyFrom(from);
    return *this;
  }

  inline const ::google::protobuf::UnknownFieldSet& unknown_fields() const {
    return _internal_metadata_.unknown_fields();
  }

  inline ::google::protobuf::UnknownFieldSet* mutable_unknown_fields() {
    return _internal_metadata_.mutable_unknown_fields();
  }

  static const ::google::protobuf::Descriptor* descriptor();
  static const TestConflictingSymbolNamesExtension& default_instance();

  static const TestConflictingSymbolNamesExtension* internal_default_instance();

  void Swap(TestConflictingSymbolNamesExtension* other);

  // implements Message ----------------------------------------------

  inline TestConflictingSymbolNamesExtension* New() const { return New(NULL); }

  TestConflictingSymbolNamesExtension* New(::google::protobuf::Arena* arena) const;
  void CopyFrom(const ::google::protobuf::Message& from);
  void MergeFrom(const ::google::protobuf::Message& from);
  void CopyFrom(const TestConflictingSymbolNamesExtension& from);
  void MergeFrom(const TestConflictingSymbolNamesExtension& from);
  void Clear();
  bool IsInitialized() const;

  size_t ByteSizeLong() const;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input);
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const;
  ::google::protobuf::uint8* InternalSerializeWithCachedSizesToArray(
      bool deterministic, ::google::protobuf::uint8* output) const;
  ::google::protobuf::uint8* SerializeWithCachedSizesToArray(::google::protobuf::uint8* output) const {
    return InternalSerializeWithCachedSizesToArray(false, output);
  }
  int GetCachedSize() const { return _cached_size_; }
  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const;
  void InternalSwap(TestConflictingSymbolNamesExtension* other);
  void UnsafeMergeFrom(const TestConflictingSymbolNamesExtension& from);
  private:
  inline ::google::protobuf::Arena* GetArenaNoVirtual() const {
    return _internal_metadata_.arena();
  }
  inline void* MaybeArenaPtr() const {
    return _internal_metadata_.raw_arena_ptr();
  }
  public:

  ::google::protobuf::Metadata GetMetadata() const;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  static const int kRepeatedInt32ExtFieldNumber = 20423638;
  static ::google::protobuf::internal::ExtensionIdentifier< ::protobuf_unittest::TestConflictingSymbolNames,
      ::google::protobuf::internal::RepeatedPrimitiveTypeTraits< ::google::protobuf::int32 >, 5, true >
    repeated_int32_ext;
  // @@protoc_insertion_point(class_scope:protobuf_unittest.TestConflictingSymbolNamesExtension)
 private:

  ::google::protobuf::internal::InternalMetadataWithArena _internal_metadata_;
  ::google::protobuf::internal::HasBits<1> _has_bits_;
  mutable int _cached_size_;
  friend void  protobuf_InitDefaults_google_2fprotobuf_2fcompiler_2fcpp_2fcpp_5ftest_5fbad_5fidentifiers_2eproto_impl();
  friend void  protobuf_AddDesc_google_2fprotobuf_2fcompiler_2fcpp_2fcpp_5ftest_5fbad_5fidentifiers_2eproto_impl();
  friend void protobuf_AssignDesc_google_2fprotobuf_2fcompiler_2fcpp_2fcpp_5ftest_5fbad_5fidentifiers_2eproto();
  friend void protobuf_ShutdownFile_google_2fprotobuf_2fcompiler_2fcpp_2fcpp_5ftest_5fbad_5fidentifiers_2eproto();

  void InitAsDefaultInstance();
};
extern ::google::protobuf::internal::ExplicitlyConstructed<TestConflictingSymbolNamesExtension> TestConflictingSymbolNamesExtension_default_instance_;

// -------------------------------------------------------------------

class TestConflictingEnumNames : public ::google::protobuf::Message /* @@protoc_insertion_point(class_definition:protobuf_unittest.TestConflictingEnumNames) */ {
 public:
  TestConflictingEnumNames();
  virtual ~TestConflictingEnumNames();

  TestConflictingEnumNames(const TestConflictingEnumNames& from);

  inline TestConflictingEnumNames& operator=(const TestConflictingEnumNames& from) {
    CopyFrom(from);
    return *this;
  }

  inline const ::google::protobuf::UnknownFieldSet& unknown_fields() const {
    return _internal_metadata_.unknown_fields();
  }

  inline ::google::protobuf::UnknownFieldSet* mutable_unknown_fields() {
    return _internal_metadata_.mutable_unknown_fields();
  }

  static const ::google::protobuf::Descriptor* descriptor();
  static const TestConflictingEnumNames& default_instance();

  static const TestConflictingEnumNames* internal_default_instance();

  void Swap(TestConflictingEnumNames* other);

  // implements Message ----------------------------------------------

  inline TestConflictingEnumNames* New() const { return New(NULL); }

  TestConflictingEnumNames* New(::google::protobuf::Arena* arena) const;
  void CopyFrom(const ::google::protobuf::Message& from);
  void MergeFrom(const ::google::protobuf::Message& from);
  void CopyFrom(const TestConflictingEnumNames& from);
  void MergeFrom(const TestConflictingEnumNames& from);
  void Clear();
  bool IsInitialized() const;

  size_t ByteSizeLong() const;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input);
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const;
  ::google::protobuf::uint8* InternalSerializeWithCachedSizesToArray(
      bool deterministic, ::google::protobuf::uint8* output) const;
  ::google::protobuf::uint8* SerializeWithCachedSizesToArray(::google::protobuf::uint8* output) const {
    return InternalSerializeWithCachedSizesToArray(false, output);
  }
  int GetCachedSize() const { return _cached_size_; }
  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const;
  void InternalSwap(TestConflictingEnumNames* other);
  void UnsafeMergeFrom(const TestConflictingEnumNames& from);
  private:
  inline ::google::protobuf::Arena* GetArenaNoVirtual() const {
    return _internal_metadata_.arena();
  }
  inline void* MaybeArenaPtr() const {
    return _internal_metadata_.raw_arena_ptr();
  }
  public:

  ::google::protobuf::Metadata GetMetadata() const;

  // nested types ----------------------------------------------------

  typedef TestConflictingEnumNames_NestedConflictingEnum NestedConflictingEnum;
  static const NestedConflictingEnum and_ =
    TestConflictingEnumNames_NestedConflictingEnum_and_;
  static const NestedConflictingEnum class_ =
    TestConflictingEnumNames_NestedConflictingEnum_class_;
  static const NestedConflictingEnum int_ =
    TestConflictingEnumNames_NestedConflictingEnum_int_;
  static const NestedConflictingEnum typedef_ =
    TestConflictingEnumNames_NestedConflictingEnum_typedef_;
  static const NestedConflictingEnum XOR =
    TestConflictingEnumNames_NestedConflictingEnum_XOR;
  static inline bool NestedConflictingEnum_IsValid(int value) {
    return TestConflictingEnumNames_NestedConflictingEnum_IsValid(value);
  }
  static const NestedConflictingEnum NestedConflictingEnum_MIN =
    TestConflictingEnumNames_NestedConflictingEnum_NestedConflictingEnum_MIN;
  static const NestedConflictingEnum NestedConflictingEnum_MAX =
    TestConflictingEnumNames_NestedConflictingEnum_NestedConflictingEnum_MAX;
  static const int NestedConflictingEnum_ARRAYSIZE =
    TestConflictingEnumNames_NestedConflictingEnum_NestedConflictingEnum_ARRAYSIZE;
  static inline const ::google::protobuf::EnumDescriptor*
  NestedConflictingEnum_descriptor() {
    return TestConflictingEnumNames_NestedConflictingEnum_descriptor();
  }
  static inline const ::std::string& NestedConflictingEnum_Name(NestedConflictingEnum value) {
    return TestConflictingEnumNames_NestedConflictingEnum_Name(value);
  }
  static inline bool NestedConflictingEnum_Parse(const ::std::string& name,
      NestedConflictingEnum* value) {
    return TestConflictingEnumNames_NestedConflictingEnum_Parse(name, value);
  }

  // accessors -------------------------------------------------------

  // optional .protobuf_unittest.TestConflictingEnumNames.NestedConflictingEnum conflicting_enum = 1;
  bool has_conflicting_enum() const;
  void clear_conflicting_enum();
  static const int kConflictingEnumFieldNumber = 1;
  ::protobuf_unittest::TestConflictingEnumNames_NestedConflictingEnum conflicting_enum() const;
  void set_conflicting_enum(::protobuf_unittest::TestConflictingEnumNames_NestedConflictingEnum value);

  // @@protoc_insertion_point(class_scope:protobuf_unittest.TestConflictingEnumNames)
 private:
  inline void set_has_conflicting_enum();
  inline void clear_has_conflicting_enum();

  ::google::protobuf::internal::InternalMetadataWithArena _internal_metadata_;
  ::google::protobuf::internal::HasBits<1> _has_bits_;
  mutable int _cached_size_;
  int conflicting_enum_;
  friend void  protobuf_InitDefaults_google_2fprotobuf_2fcompiler_2fcpp_2fcpp_5ftest_5fbad_5fidentifiers_2eproto_impl();
  friend void  protobuf_AddDesc_google_2fprotobuf_2fcompiler_2fcpp_2fcpp_5ftest_5fbad_5fidentifiers_2eproto_impl();
  friend void protobuf_AssignDesc_google_2fprotobuf_2fcompiler_2fcpp_2fcpp_5ftest_5fbad_5fidentifiers_2eproto();
  friend void protobuf_ShutdownFile_google_2fprotobuf_2fcompiler_2fcpp_2fcpp_5ftest_5fbad_5fidentifiers_2eproto();

  void InitAsDefaultInstance();
};
extern ::google::protobuf::internal::ExplicitlyConstructed<TestConflictingEnumNames> TestConflictingEnumNames_default_instance_;

// -------------------------------------------------------------------

class DummyMessage : public ::google::protobuf::Message /* @@protoc_insertion_point(class_definition:protobuf_unittest.DummyMessage) */ {
 public:
  DummyMessage();
  virtual ~DummyMessage();

  DummyMessage(const DummyMessage& from);

  inline DummyMessage& operator=(const DummyMessage& from) {
    CopyFrom(from);
    return *this;
  }

  inline const ::google::protobuf::UnknownFieldSet& unknown_fields() const {
    return _internal_metadata_.unknown_fields();
  }

  inline ::google::protobuf::UnknownFieldSet* mutable_unknown_fields() {
    return _internal_metadata_.mutable_unknown_fields();
  }

  static const ::google::protobuf::Descriptor* descriptor();
  static const DummyMessage& default_instance();

  static const DummyMessage* internal_default_instance();

  void Swap(DummyMessage* other);

  // implements Message ----------------------------------------------

  inline DummyMessage* New() const { return New(NULL); }

  DummyMessage* New(::google::protobuf::Arena* arena) const;
  void CopyFrom(const ::google::protobuf::Message& from);
  void MergeFrom(const ::google::protobuf::Message& from);
  void CopyFrom(const DummyMessage& from);
  void MergeFrom(const DummyMessage& from);
  void Clear();
  bool IsInitialized() const;

  size_t ByteSizeLong() const;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input);
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const;
  ::google::protobuf::uint8* InternalSerializeWithCachedSizesToArray(
      bool deterministic, ::google::protobuf::uint8* output) const;
  ::google::protobuf::uint8* SerializeWithCachedSizesToArray(::google::protobuf::uint8* output) const {
    return InternalSerializeWithCachedSizesToArray(false, output);
  }
  int GetCachedSize() const { return _cached_size_; }
  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const;
  void InternalSwap(DummyMessage* other);
  void UnsafeMergeFrom(const DummyMessage& from);
  private:
  inline ::google::protobuf::Arena* GetArenaNoVirtual() const {
    return _internal_metadata_.arena();
  }
  inline void* MaybeArenaPtr() const {
    return _internal_metadata_.raw_arena_ptr();
  }
  public:

  ::google::protobuf::Metadata GetMetadata() const;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // @@protoc_insertion_point(class_scope:protobuf_unittest.DummyMessage)
 private:

  ::google::protobuf::internal::InternalMetadataWithArena _internal_metadata_;
  ::google::protobuf::internal::HasBits<1> _has_bits_;
  mutable int _cached_size_;
  friend void  protobuf_InitDefaults_google_2fprotobuf_2fcompiler_2fcpp_2fcpp_5ftest_5fbad_5fidentifiers_2eproto_impl();
  friend void  protobuf_AddDesc_google_2fprotobuf_2fcompiler_2fcpp_2fcpp_5ftest_5fbad_5fidentifiers_2eproto_impl();
  friend void protobuf_AssignDesc_google_2fprotobuf_2fcompiler_2fcpp_2fcpp_5ftest_5fbad_5fidentifiers_2eproto();
  friend void protobuf_ShutdownFile_google_2fprotobuf_2fcompiler_2fcpp_2fcpp_5ftest_5fbad_5fidentifiers_2eproto();

  void InitAsDefaultInstance();
};
extern ::google::protobuf::internal::ExplicitlyConstructed<DummyMessage> DummyMessage_default_instance_;

// ===================================================================

class TestConflictingMethodNames_Stub;

class TestConflictingMethodNames : public ::google::protobuf::Service {
 protected:
  // This class should be treated as an abstract interface.
  inline TestConflictingMethodNames() {};
 public:
  virtual ~TestConflictingMethodNames();

  typedef TestConflictingMethodNames_Stub Stub;

  static const ::google::protobuf::ServiceDescriptor* descriptor();

  virtual void Closure(::google::protobuf::RpcController* controller,
                       const ::protobuf_unittest::DummyMessage* request,
                       ::protobuf_unittest::DummyMessage* response,
                       ::google::protobuf::Closure* done);

  // implements Service ----------------------------------------------

  const ::google::protobuf::ServiceDescriptor* GetDescriptor();
  void CallMethod(const ::google::protobuf::MethodDescriptor* method,
                  ::google::protobuf::RpcController* controller,
                  const ::google::protobuf::Message* request,
                  ::google::protobuf::Message* response,
                  ::google::protobuf::Closure* done);
  const ::google::protobuf::Message& GetRequestPrototype(
    const ::google::protobuf::MethodDescriptor* method) const;
  const ::google::protobuf::Message& GetResponsePrototype(
    const ::google::protobuf::MethodDescriptor* method) const;

 private:
  GOOGLE_DISALLOW_EVIL_CONSTRUCTORS(TestConflictingMethodNames);
};

class TestConflictingMethodNames_Stub : public TestConflictingMethodNames {
 public:
  TestConflictingMethodNames_Stub(::google::protobuf::RpcChannel* channel);
  TestConflictingMethodNames_Stub(::google::protobuf::RpcChannel* channel,
                   ::google::protobuf::Service::ChannelOwnership ownership);
  ~TestConflictingMethodNames_Stub();

  inline ::google::protobuf::RpcChannel* channel() { return channel_; }

  // implements TestConflictingMethodNames ------------------------------------------

  void Closure(::google::protobuf::RpcController* controller,
                       const ::protobuf_unittest::DummyMessage* request,
                       ::protobuf_unittest::DummyMessage* response,
                       ::google::protobuf::Closure* done);
 private:
  ::google::protobuf::RpcChannel* channel_;
  bool owns_channel_;
  GOOGLE_DISALLOW_EVIL_CONSTRUCTORS(TestConflictingMethodNames_Stub);
};


// ===================================================================


// ===================================================================

#if !PROTOBUF_INLINE_NOT_IN_HEADERS
// TestConflictingSymbolNames_BuildDescriptors

inline const TestConflictingSymbolNames_BuildDescriptors* TestConflictingSymbolNames_BuildDescriptors::internal_default_instance() {
  return &TestConflictingSymbolNames_BuildDescriptors_default_instance_.get();
}
// -------------------------------------------------------------------

// TestConflictingSymbolNames_TypeTraits

inline const TestConflictingSymbolNames_TypeTraits* TestConflictingSymbolNames_TypeTraits::internal_default_instance() {
  return &TestConflictingSymbolNames_TypeTraits_default_instance_.get();
}
// -------------------------------------------------------------------

// TestConflictingSymbolNames_Data1

// repeated int32 data = 1;
inline int TestConflictingSymbolNames_Data1::data_size() const {
  return data_.size();
}
inline void TestConflictingSymbolNames_Data1::clear_data() {
  data_.Clear();
}
inline ::google::protobuf::int32 TestConflictingSymbolNames_Data1::data(int index) const {
  // @@protoc_insertion_point(field_get:protobuf_unittest.TestConflictingSymbolNames.Data1.data)
  return data_.Get(index);
}
inline void TestConflictingSymbolNames_Data1::set_data(int index, ::google::protobuf::int32 value) {
  data_.Set(index, value);
  // @@protoc_insertion_point(field_set:protobuf_unittest.TestConflictingSymbolNames.Data1.data)
}
inline void TestConflictingSymbolNames_Data1::add_data(::google::protobuf::int32 value) {
  data_.Add(value);
  // @@protoc_insertion_point(field_add:protobuf_unittest.TestConflictingSymbolNames.Data1.data)
}
inline const ::google::protobuf::RepeatedField< ::google::protobuf::int32 >&
TestConflictingSymbolNames_Data1::data() const {
  // @@protoc_insertion_point(field_list:protobuf_unittest.TestConflictingSymbolNames.Data1.data)
  return data_;
}
inline ::google::protobuf::RepeatedField< ::google::protobuf::int32 >*
TestConflictingSymbolNames_Data1::mutable_data() {
  // @@protoc_insertion_point(field_mutable_list:protobuf_unittest.TestConflictingSymbolNames.Data1.data)
  return &data_;
}

inline const TestConflictingSymbolNames_Data1* TestConflictingSymbolNames_Data1::internal_default_instance() {
  return &TestConflictingSymbolNames_Data1_default_instance_.get();
}
// -------------------------------------------------------------------

// TestConflictingSymbolNames_Data2

// repeated .protobuf_unittest.TestConflictingSymbolNames.TestEnum data = 1;
inline int TestConflictingSymbolNames_Data2::data_size() const {
  return data_.size();
}
inline void TestConflictingSymbolNames_Data2::clear_data() {
  data_.Clear();
}
inline ::protobuf_unittest::TestConflictingSymbolNames_TestEnum TestConflictingSymbolNames_Data2::data(int index) const {
  // @@protoc_insertion_point(field_get:protobuf_unittest.TestConflictingSymbolNames.Data2.data)
  return static_cast< ::protobuf_unittest::TestConflictingSymbolNames_TestEnum >(data_.Get(index));
}
inline void TestConflictingSymbolNames_Data2::set_data(int index, ::protobuf_unittest::TestConflictingSymbolNames_TestEnum value) {
  assert(::protobuf_unittest::TestConflictingSymbolNames_TestEnum_IsValid(value));
  data_.Set(index, value);
  // @@protoc_insertion_point(field_set:protobuf_unittest.TestConflictingSymbolNames.Data2.data)
}
inline void TestConflictingSymbolNames_Data2::add_data(::protobuf_unittest::TestConflictingSymbolNames_TestEnum value) {
  assert(::protobuf_unittest::TestConflictingSymbolNames_TestEnum_IsValid(value));
  data_.Add(value);
  // @@protoc_insertion_point(field_add:protobuf_unittest.TestConflictingSymbolNames.Data2.data)
}
inline const ::google::protobuf::RepeatedField<int>&
TestConflictingSymbolNames_Data2::data() const {
  // @@protoc_insertion_point(field_list:protobuf_unittest.TestConflictingSymbolNames.Data2.data)
  return data_;
}
inline ::google::protobuf::RepeatedField<int>*
TestConflictingSymbolNames_Data2::mutable_data() {
  // @@protoc_insertion_point(field_mutable_list:protobuf_unittest.TestConflictingSymbolNames.Data2.data)
  return &data_;
}

inline const TestConflictingSymbolNames_Data2* TestConflictingSymbolNames_Data2::internal_default_instance() {
  return &TestConflictingSymbolNames_Data2_default_instance_.get();
}
// -------------------------------------------------------------------

// TestConflictingSymbolNames_Data3

// repeated string data = 1;
inline int TestConflictingSymbolNames_Data3::data_size() const {
  return data_.size();
}
inline void TestConflictingSymbolNames_Data3::clear_data() {
  data_.Clear();
}
inline const ::std::string& TestConflictingSymbolNames_Data3::data(int index) const {
  // @@protoc_insertion_point(field_get:protobuf_unittest.TestConflictingSymbolNames.Data3.data)
  return data_.Get(index);
}
inline ::std::string* TestConflictingSymbolNames_Data3::mutable_data(int index) {
  // @@protoc_insertion_point(field_mutable:protobuf_unittest.TestConflictingSymbolNames.Data3.data)
  return data_.Mutable(index);
}
inline void TestConflictingSymbolNames_Data3::set_data(int index, const ::std::string& value) {
  // @@protoc_insertion_point(field_set:protobuf_unittest.TestConflictingSymbolNames.Data3.data)
  data_.Mutable(index)->assign(value);
}
inline void TestConflictingSymbolNames_Data3::set_data(int index, const char* value) {
  data_.Mutable(index)->assign(value);
  // @@protoc_insertion_point(field_set_char:protobuf_unittest.TestConflictingSymbolNames.Data3.data)
}
inline void TestConflictingSymbolNames_Data3::set_data(int index, const char* value, size_t size) {
  data_.Mutable(index)->assign(
    reinterpret_cast<const char*>(value), size);
  // @@protoc_insertion_point(field_set_pointer:protobuf_unittest.TestConflictingSymbolNames.Data3.data)
}
inline ::std::string* TestConflictingSymbolNames_Data3::add_data() {
  // @@protoc_insertion_point(field_add_mutable:protobuf_unittest.TestConflictingSymbolNames.Data3.data)
  return data_.Add();
}
inline void TestConflictingSymbolNames_Data3::add_data(const ::std::string& value) {
  data_.Add()->assign(value);
  // @@protoc_insertion_point(field_add:protobuf_unittest.TestConflictingSymbolNames.Data3.data)
}
inline void TestConflictingSymbolNames_Data3::add_data(const char* value) {
  data_.Add()->assign(value);
  // @@protoc_insertion_point(field_add_char:protobuf_unittest.TestConflictingSymbolNames.Data3.data)
}
inline void TestConflictingSymbolNames_Data3::add_data(const char* value, size_t size) {
  data_.Add()->assign(reinterpret_cast<const char*>(value), size);
  // @@protoc_insertion_point(field_add_pointer:protobuf_unittest.TestConflictingSymbolNames.Data3.data)
}
inline const ::google::protobuf::RepeatedPtrField< ::std::string>&
TestConflictingSymbolNames_Data3::data() const {
  // @@protoc_insertion_point(field_list:protobuf_unittest.TestConflictingSymbolNames.Data3.data)
  return data_;
}
inline ::google::protobuf::RepeatedPtrField< ::std::string>*
TestConflictingSymbolNames_Data3::mutable_data() {
  // @@protoc_insertion_point(field_mutable_list:protobuf_unittest.TestConflictingSymbolNames.Data3.data)
  return &data_;
}

inline const TestConflictingSymbolNames_Data3* TestConflictingSymbolNames_Data3::internal_default_instance() {
  return &TestConflictingSymbolNames_Data3_default_instance_.get();
}
// -------------------------------------------------------------------

// TestConflictingSymbolNames_Data4

// repeated .protobuf_unittest.TestConflictingSymbolNames.Data4 data = 1;
inline int TestConflictingSymbolNames_Data4::data_size() const {
  return data_.size();
}
inline void TestConflictingSymbolNames_Data4::clear_data() {
  data_.Clear();
}
inline const ::protobuf_unittest::TestConflictingSymbolNames_Data4& TestConflictingSymbolNames_Data4::data(int index) const {
  // @@protoc_insertion_point(field_get:protobuf_unittest.TestConflictingSymbolNames.Data4.data)
  return data_.Get(index);
}
inline ::protobuf_unittest::TestConflictingSymbolNames_Data4* TestConflictingSymbolNames_Data4::mutable_data(int index) {
  // @@protoc_insertion_point(field_mutable:protobuf_unittest.TestConflictingSymbolNames.Data4.data)
  return data_.Mutable(index);
}
inline ::protobuf_unittest::TestConflictingSymbolNames_Data4* TestConflictingSymbolNames_Data4::add_data() {
  // @@protoc_insertion_point(field_add:protobuf_unittest.TestConflictingSymbolNames.Data4.data)
  return data_.Add();
}
inline ::google::protobuf::RepeatedPtrField< ::protobuf_unittest::TestConflictingSymbolNames_Data4 >*
TestConflictingSymbolNames_Data4::mutable_data() {
  // @@protoc_insertion_point(field_mutable_list:protobuf_unittest.TestConflictingSymbolNames.Data4.data)
  return &data_;
}
inline const ::google::protobuf::RepeatedPtrField< ::protobuf_unittest::TestConflictingSymbolNames_Data4 >&
TestConflictingSymbolNames_Data4::data() const {
  // @@protoc_insertion_point(field_list:protobuf_unittest.TestConflictingSymbolNames.Data4.data)
  return data_;
}

inline const TestConflictingSymbolNames_Data4* TestConflictingSymbolNames_Data4::internal_default_instance() {
  return &TestConflictingSymbolNames_Data4_default_instance_.get();
}
// -------------------------------------------------------------------

// TestConflictingSymbolNames_Data5

// repeated string data = 1 [ctype = STRING_PIECE];
inline int TestConflictingSymbolNames_Data5::data_size() const {
  return data_.size();
}
inline void TestConflictingSymbolNames_Data5::clear_data() {
  data_.Clear();
}
inline const ::std::string& TestConflictingSymbolNames_Data5::data(int index) const {
  // @@protoc_insertion_point(field_get:protobuf_unittest.TestConflictingSymbolNames.Data5.data)
  return data_.Get(index);
}
inline ::std::string* TestConflictingSymbolNames_Data5::mutable_data(int index) {
  // @@protoc_insertion_point(field_mutable:protobuf_unittest.TestConflictingSymbolNames.Data5.data)
  return data_.Mutable(index);
}
inline void TestConflictingSymbolNames_Data5::set_data(int index, const ::std::string& value) {
  // @@protoc_insertion_point(field_set:protobuf_unittest.TestConflictingSymbolNames.Data5.data)
  data_.Mutable(index)->assign(value);
}
inline void TestConflictingSymbolNames_Data5::set_data(int index, const char* value) {
  data_.Mutable(index)->assign(value);
  // @@protoc_insertion_point(field_set_char:protobuf_unittest.TestConflictingSymbolNames.Data5.data)
}
inline void TestConflictingSymbolNames_Data5::set_data(int index, const char* value, size_t size) {
  data_.Mutable(index)->assign(
    reinterpret_cast<const char*>(value), size);
  // @@protoc_insertion_point(field_set_pointer:protobuf_unittest.TestConflictingSymbolNames.Data5.data)
}
inline ::std::string* TestConflictingSymbolNames_Data5::add_data() {
  // @@protoc_insertion_point(field_add_mutable:protobuf_unittest.TestConflictingSymbolNames.Data5.data)
  return data_.Add();
}
inline void TestConflictingSymbolNames_Data5::add_data(const ::std::string& value) {
  data_.Add()->assign(value);
  // @@protoc_insertion_point(field_add:protobuf_unittest.TestConflictingSymbolNames.Data5.data)
}
inline void TestConflictingSymbolNames_Data5::add_data(const char* value) {
  data_.Add()->assign(value);
  // @@protoc_insertion_point(field_add_char:protobuf_unittest.TestConflictingSymbolNames.Data5.data)
}
inline void TestConflictingSymbolNames_Data5::add_data(const char* value, size_t size) {
  data_.Add()->assign(reinterpret_cast<const char*>(value), size);
  // @@protoc_insertion_point(field_add_pointer:protobuf_unittest.TestConflictingSymbolNames.Data5.data)
}
inline const ::google::protobuf::RepeatedPtrField< ::std::string>&
TestConflictingSymbolNames_Data5::data() const {
  // @@protoc_insertion_point(field_list:protobuf_unittest.TestConflictingSymbolNames.Data5.data)
  return data_;
}
inline ::google::protobuf::RepeatedPtrField< ::std::string>*
TestConflictingSymbolNames_Data5::mutable_data() {
  // @@protoc_insertion_point(field_mutable_list:protobuf_unittest.TestConflictingSymbolNames.Data5.data)
  return &data_;
}

inline const TestConflictingSymbolNames_Data5* TestConflictingSymbolNames_Data5::internal_default_instance() {
  return &TestConflictingSymbolNames_Data5_default_instance_.get();
}
// -------------------------------------------------------------------

// TestConflictingSymbolNames_Data6

// repeated string data = 1 [ctype = CORD];
inline int TestConflictingSymbolNames_Data6::data_size() const {
  return data_.size();
}
inline void TestConflictingSymbolNames_Data6::clear_data() {
  data_.Clear();
}
inline const ::std::string& TestConflictingSymbolNames_Data6::data(int index) const {
  // @@protoc_insertion_point(field_get:protobuf_unittest.TestConflictingSymbolNames.Data6.data)
  return data_.Get(index);
}
inline ::std::string* TestConflictingSymbolNames_Data6::mutable_data(int index) {
  // @@protoc_insertion_point(field_mutable:protobuf_unittest.TestConflictingSymbolNames.Data6.data)
  return data_.Mutable(index);
}
inline void TestConflictingSymbolNames_Data6::set_data(int index, const ::std::string& value) {
  // @@protoc_insertion_point(field_set:protobuf_unittest.TestConflictingSymbolNames.Data6.data)
  data_.Mutable(index)->assign(value);
}
inline void TestConflictingSymbolNames_Data6::set_data(int index, const char* value) {
  data_.Mutable(index)->assign(value);
  // @@protoc_insertion_point(field_set_char:protobuf_unittest.TestConflictingSymbolNames.Data6.data)
}
inline void TestConflictingSymbolNames_Data6::set_data(int index, const char* value, size_t size) {
  data_.Mutable(index)->assign(
    reinterpret_cast<const char*>(value), size);
  // @@protoc_insertion_point(field_set_pointer:protobuf_unittest.TestConflictingSymbolNames.Data6.data)
}
inline ::std::string* TestConflictingSymbolNames_Data6::add_data() {
  // @@protoc_insertion_point(field_add_mutable:protobuf_unittest.TestConflictingSymbolNames.Data6.data)
  return data_.Add();
}
inline void TestConflictingSymbolNames_Data6::add_data(const ::std::string& value) {
  data_.Add()->assign(value);
  // @@protoc_insertion_point(field_add:protobuf_unittest.TestConflictingSymbolNames.Data6.data)
}
inline void TestConflictingSymbolNames_Data6::add_data(const char* value) {
  data_.Add()->assign(value);
  // @@protoc_insertion_point(field_add_char:protobuf_unittest.TestConflictingSymbolNames.Data6.data)
}
inline void TestConflictingSymbolNames_Data6::add_data(const char* value, size_t size) {
  data_.Add()->assign(reinterpret_cast<const char*>(value), size);
  // @@protoc_insertion_point(field_add_pointer:protobuf_unittest.TestConflictingSymbolNames.Data6.data)
}
inline const ::google::protobuf::RepeatedPtrField< ::std::string>&
TestConflictingSymbolNames_Data6::data() const {
  // @@protoc_insertion_point(field_list:protobuf_unittest.TestConflictingSymbolNames.Data6.data)
  return data_;
}
inline ::google::protobuf::RepeatedPtrField< ::std::string>*
TestConflictingSymbolNames_Data6::mutable_data() {
  // @@protoc_insertion_point(field_mutable_list:protobuf_unittest.TestConflictingSymbolNames.Data6.data)
  return &data_;
}

inline const TestConflictingSymbolNames_Data6* TestConflictingSymbolNames_Data6::internal_default_instance() {
  return &TestConflictingSymbolNames_Data6_default_instance_.get();
}
// -------------------------------------------------------------------

// TestConflictingSymbolNames_Cord

inline const TestConflictingSymbolNames_Cord* TestConflictingSymbolNames_Cord::internal_default_instance() {
  return &TestConflictingSymbolNames_Cord_default_instance_.get();
}
// -------------------------------------------------------------------

// TestConflictingSymbolNames_StringPiece

inline const TestConflictingSymbolNames_StringPiece* TestConflictingSymbolNames_StringPiece::internal_default_instance() {
  return &TestConflictingSymbolNames_StringPiece_default_instance_.get();
}
// -------------------------------------------------------------------

// TestConflictingSymbolNames_DO

inline const TestConflictingSymbolNames_DO* TestConflictingSymbolNames_DO::internal_default_instance() {
  return &TestConflictingSymbolNames_DO_default_instance_.get();
}
// -------------------------------------------------------------------

// TestConflictingSymbolNames

// optional int32 input = 1;
inline bool TestConflictingSymbolNames::has_input() const {
  return (_has_bits_[0] & 0x00000001u) != 0;
}
inline void TestConflictingSymbolNames::set_has_input() {
  _has_bits_[0] |= 0x00000001u;
}
inline void TestConflictingSymbolNames::clear_has_input() {
  _has_bits_[0] &= ~0x00000001u;
}
inline void TestConflictingSymbolNames::clear_input() {
  input_ = 0;
  clear_has_input();
}
inline ::google::protobuf::int32 TestConflictingSymbolNames::input() const {
  // @@protoc_insertion_point(field_get:protobuf_unittest.TestConflictingSymbolNames.input)
  return input_;
}
inline void TestConflictingSymbolNames::set_input(::google::protobuf::int32 value) {
  set_has_input();
  input_ = value;
  // @@protoc_insertion_point(field_set:protobuf_unittest.TestConflictingSymbolNames.input)
}

// optional int32 output = 2;
inline bool TestConflictingSymbolNames::has_output() const {
  return (_has_bits_[0] & 0x00000002u) != 0;
}
inline void TestConflictingSymbolNames::set_has_output() {
  _has_bits_[0] |= 0x00000002u;
}
inline void TestConflictingSymbolNames::clear_has_output() {
  _has_bits_[0] &= ~0x00000002u;
}
inline void TestConflictingSymbolNames::clear_output() {
  output_ = 0;
  clear_has_output();
}
inline ::google::protobuf::int32 TestConflictingSymbolNames::output() const {
  // @@protoc_insertion_point(field_get:protobuf_unittest.TestConflictingSymbolNames.output)
  return output_;
}
inline void TestConflictingSymbolNames::set_output(::google::protobuf::int32 value) {
  set_has_output();
  output_ = value;
  // @@protoc_insertion_point(field_set:protobuf_unittest.TestConflictingSymbolNames.output)
}

// optional string length = 3;
inline bool TestConflictingSymbolNames::has_length() const {
  return (_has_bits_[0] & 0x00000004u) != 0;
}
inline void TestConflictingSymbolNames::set_has_length() {
  _has_bits_[0] |= 0x00000004u;
}
inline void TestConflictingSymbolNames::clear_has_length() {
  _has_bits_[0] &= ~0x00000004u;
}
inline void TestConflictingSymbolNames::clear_length() {
  length_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  clear_has_length();
}
inline const ::std::string& TestConflictingSymbolNames::length() const {
  // @@protoc_insertion_point(field_get:protobuf_unittest.TestConflictingSymbolNames.length)
  return length_.GetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void TestConflictingSymbolNames::set_length(const ::std::string& value) {
  set_has_length();
  length_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:protobuf_unittest.TestConflictingSymbolNames.length)
}
inline void TestConflictingSymbolNames::set_length(const char* value) {
  set_has_length();
  length_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:protobuf_unittest.TestConflictingSymbolNames.length)
}
inline void TestConflictingSymbolNames::set_length(const char* value, size_t size) {
  set_has_length();
  length_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:protobuf_unittest.TestConflictingSymbolNames.length)
}
inline ::std::string* TestConflictingSymbolNames::mutable_length() {
  set_has_length();
  // @@protoc_insertion_point(field_mutable:protobuf_unittest.TestConflictingSymbolNames.length)
  return length_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline ::std::string* TestConflictingSymbolNames::release_length__() {
  // @@protoc_insertion_point(field_release:protobuf_unittest.TestConflictingSymbolNames.length)
  clear_has_length();
  return length_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void TestConflictingSymbolNames::set_allocated_length(::std::string* length) {
  if (length != NULL) {
    set_has_length();
  } else {
    clear_has_length();
  }
  length_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), length);
  // @@protoc_insertion_point(field_set_allocated:protobuf_unittest.TestConflictingSymbolNames.length)
}

// repeated int32 i = 4;
inline int TestConflictingSymbolNames::i_size() const {
  return i_.size();
}
inline void TestConflictingSymbolNames::clear_i() {
  i_.Clear();
}
inline ::google::protobuf::int32 TestConflictingSymbolNames::i(int index) const {
  // @@protoc_insertion_point(field_get:protobuf_unittest.TestConflictingSymbolNames.i)
  return i_.Get(index);
}
inline void TestConflictingSymbolNames::set_i(int index, ::google::protobuf::int32 value) {
  i_.Set(index, value);
  // @@protoc_insertion_point(field_set:protobuf_unittest.TestConflictingSymbolNames.i)
}
inline void TestConflictingSymbolNames::add_i(::google::protobuf::int32 value) {
  i_.Add(value);
  // @@protoc_insertion_point(field_add:protobuf_unittest.TestConflictingSymbolNames.i)
}
inline const ::google::protobuf::RepeatedField< ::google::protobuf::int32 >&
TestConflictingSymbolNames::i() const {
  // @@protoc_insertion_point(field_list:protobuf_unittest.TestConflictingSymbolNames.i)
  return i_;
}
inline ::google::protobuf::RepeatedField< ::google::protobuf::int32 >*
TestConflictingSymbolNames::mutable_i() {
  // @@protoc_insertion_point(field_mutable_list:protobuf_unittest.TestConflictingSymbolNames.i)
  return &i_;
}

// repeated string new_element = 5 [ctype = STRING_PIECE];
inline int TestConflictingSymbolNames::new_element_size() const {
  return new_element_.size();
}
inline void TestConflictingSymbolNames::clear_new_element() {
  new_element_.Clear();
}
inline const ::std::string& TestConflictingSymbolNames::new_element(int index) const {
  // @@protoc_insertion_point(field_get:protobuf_unittest.TestConflictingSymbolNames.new_element)
  return new_element_.Get(index);
}
inline ::std::string* TestConflictingSymbolNames::mutable_new_element(int index) {
  // @@protoc_insertion_point(field_mutable:protobuf_unittest.TestConflictingSymbolNames.new_element)
  return new_element_.Mutable(index);
}
inline void TestConflictingSymbolNames::set_new_element(int index, const ::std::string& value) {
  // @@protoc_insertion_point(field_set:protobuf_unittest.TestConflictingSymbolNames.new_element)
  new_element_.Mutable(index)->assign(value);
}
inline void TestConflictingSymbolNames::set_new_element(int index, const char* value) {
  new_element_.Mutable(index)->assign(value);
  // @@protoc_insertion_point(field_set_char:protobuf_unittest.TestConflictingSymbolNames.new_element)
}
inline void TestConflictingSymbolNames::set_new_element(int index, const char* value, size_t size) {
  new_element_.Mutable(index)->assign(
    reinterpret_cast<const char*>(value), size);
  // @@protoc_insertion_point(field_set_pointer:protobuf_unittest.TestConflictingSymbolNames.new_element)
}
inline ::std::string* TestConflictingSymbolNames::add_new_element() {
  // @@protoc_insertion_point(field_add_mutable:protobuf_unittest.TestConflictingSymbolNames.new_element)
  return new_element_.Add();
}
inline void TestConflictingSymbolNames::add_new_element(const ::std::string& value) {
  new_element_.Add()->assign(value);
  // @@protoc_insertion_point(field_add:protobuf_unittest.TestConflictingSymbolNames.new_element)
}
inline void TestConflictingSymbolNames::add_new_element(const char* value) {
  new_element_.Add()->assign(value);
  // @@protoc_insertion_point(field_add_char:protobuf_unittest.TestConflictingSymbolNames.new_element)
}
inline void TestConflictingSymbolNames::add_new_element(const char* value, size_t size) {
  new_element_.Add()->assign(reinterpret_cast<const char*>(value), size);
  // @@protoc_insertion_point(field_add_pointer:protobuf_unittest.TestConflictingSymbolNames.new_element)
}
inline const ::google::protobuf::RepeatedPtrField< ::std::string>&
TestConflictingSymbolNames::new_element() const {
  // @@protoc_insertion_point(field_list:protobuf_unittest.TestConflictingSymbolNames.new_element)
  return new_element_;
}
inline ::google::protobuf::RepeatedPtrField< ::std::string>*
TestConflictingSymbolNames::mutable_new_element() {
  // @@protoc_insertion_point(field_mutable_list:protobuf_unittest.TestConflictingSymbolNames.new_element)
  return &new_element_;
}

// optional int32 total_size = 6;
inline bool TestConflictingSymbolNames::has_total_size() const {
  return (_has_bits_[0] & 0x00000020u) != 0;
}
inline void TestConflictingSymbolNames::set_has_total_size() {
  _has_bits_[0] |= 0x00000020u;
}
inline void TestConflictingSymbolNames::clear_has_total_size() {
  _has_bits_[0] &= ~0x00000020u;
}
inline void TestConflictingSymbolNames::clear_total_size() {
  total_size_ = 0;
  clear_has_total_size();
}
inline ::google::protobuf::int32 TestConflictingSymbolNames::total_size() const {
  // @@protoc_insertion_point(field_get:protobuf_unittest.TestConflictingSymbolNames.total_size)
  return total_size_;
}
inline void TestConflictingSymbolNames::set_total_size(::google::protobuf::int32 value) {
  set_has_total_size();
  total_size_ = value;
  // @@protoc_insertion_point(field_set:protobuf_unittest.TestConflictingSymbolNames.total_size)
}

// optional int32 tag = 7;
inline bool TestConflictingSymbolNames::has_tag() const {
  return (_has_bits_[0] & 0x00000040u) != 0;
}
inline void TestConflictingSymbolNames::set_has_tag() {
  _has_bits_[0] |= 0x00000040u;
}
inline void TestConflictingSymbolNames::clear_has_tag() {
  _has_bits_[0] &= ~0x00000040u;
}
inline void TestConflictingSymbolNames::clear_tag() {
  tag_ = 0;
  clear_has_tag();
}
inline ::google::protobuf::int32 TestConflictingSymbolNames::tag() const {
  // @@protoc_insertion_point(field_get:protobuf_unittest.TestConflictingSymbolNames.tag)
  return tag_;
}
inline void TestConflictingSymbolNames::set_tag(::google::protobuf::int32 value) {
  set_has_tag();
  tag_ = value;
  // @@protoc_insertion_point(field_set:protobuf_unittest.TestConflictingSymbolNames.tag)
}

// optional int32 source = 8;
inline bool TestConflictingSymbolNames::has_source() const {
  return (_has_bits_[0] & 0x00000080u) != 0;
}
inline void TestConflictingSymbolNames::set_has_source() {
  _has_bits_[0] |= 0x00000080u;
}
inline void TestConflictingSymbolNames::clear_has_source() {
  _has_bits_[0] &= ~0x00000080u;
}
inline void TestConflictingSymbolNames::clear_source() {
  source_ = 0;
  clear_has_source();
}
inline ::google::protobuf::int32 TestConflictingSymbolNames::source() const {
  // @@protoc_insertion_point(field_get:protobuf_unittest.TestConflictingSymbolNames.source)
  return source_;
}
inline void TestConflictingSymbolNames::set_source(::google::protobuf::int32 value) {
  set_has_source();
  source_ = value;
  // @@protoc_insertion_point(field_set:protobuf_unittest.TestConflictingSymbolNames.source)
}

// optional int32 value = 9;
inline bool TestConflictingSymbolNames::has_value() const {
  return (_has_bits_[0] & 0x00000100u) != 0;
}
inline void TestConflictingSymbolNames::set_has_value() {
  _has_bits_[0] |= 0x00000100u;
}
inline void TestConflictingSymbolNames::clear_has_value() {
  _has_bits_[0] &= ~0x00000100u;
}
inline void TestConflictingSymbolNames::clear_value() {
  value_ = 0;
  clear_has_value();
}
inline ::google::protobuf::int32 TestConflictingSymbolNames::value() const {
  // @@protoc_insertion_point(field_get:protobuf_unittest.TestConflictingSymbolNames.value)
  return value_;
}
inline void TestConflictingSymbolNames::set_value(::google::protobuf::int32 value) {
  set_has_value();
  value_ = value;
  // @@protoc_insertion_point(field_set:protobuf_unittest.TestConflictingSymbolNames.value)
}

// optional int32 file = 10;
inline bool TestConflictingSymbolNames::has_file() const {
  return (_has_bits_[0] & 0x00000200u) != 0;
}
inline void TestConflictingSymbolNames::set_has_file() {
  _has_bits_[0] |= 0x00000200u;
}
inline void TestConflictingSymbolNames::clear_has_file() {
  _has_bits_[0] &= ~0x00000200u;
}
inline void TestConflictingSymbolNames::clear_file() {
  file_ = 0;
  clear_has_file();
}
inline ::google::protobuf::int32 TestConflictingSymbolNames::file() const {
  // @@protoc_insertion_point(field_get:protobuf_unittest.TestConflictingSymbolNames.file)
  return file_;
}
inline void TestConflictingSymbolNames::set_file(::google::protobuf::int32 value) {
  set_has_file();
  file_ = value;
  // @@protoc_insertion_point(field_set:protobuf_unittest.TestConflictingSymbolNames.file)
}

// optional int32 from = 11;
inline bool TestConflictingSymbolNames::has_from() const {
  return (_has_bits_[0] & 0x00000400u) != 0;
}
inline void TestConflictingSymbolNames::set_has_from() {
  _has_bits_[0] |= 0x00000400u;
}
inline void TestConflictingSymbolNames::clear_has_from() {
  _has_bits_[0] &= ~0x00000400u;
}
inline void TestConflictingSymbolNames::clear_from() {
  from_ = 0;
  clear_has_from();
}
inline ::google::protobuf::int32 TestConflictingSymbolNames::from() const {
  // @@protoc_insertion_point(field_get:protobuf_unittest.TestConflictingSymbolNames.from)
  return from_;
}
inline void TestConflictingSymbolNames::set_from(::google::protobuf::int32 value) {
  set_has_from();
  from_ = value;
  // @@protoc_insertion_point(field_set:protobuf_unittest.TestConflictingSymbolNames.from)
}

// optional int32 handle_uninterpreted = 12;
inline bool TestConflictingSymbolNames::has_handle_uninterpreted() const {
  return (_has_bits_[0] & 0x00000800u) != 0;
}
inline void TestConflictingSymbolNames::set_has_handle_uninterpreted() {
  _has_bits_[0] |= 0x00000800u;
}
inline void TestConflictingSymbolNames::clear_has_handle_uninterpreted() {
  _has_bits_[0] &= ~0x00000800u;
}
inline void TestConflictingSymbolNames::clear_handle_uninterpreted() {
  handle_uninterpreted_ = 0;
  clear_has_handle_uninterpreted();
}
inline ::google::protobuf::int32 TestConflictingSymbolNames::handle_uninterpreted() const {
  // @@protoc_insertion_point(field_get:protobuf_unittest.TestConflictingSymbolNames.handle_uninterpreted)
  return handle_uninterpreted_;
}
inline void TestConflictingSymbolNames::set_handle_uninterpreted(::google::protobuf::int32 value) {
  set_has_handle_uninterpreted();
  handle_uninterpreted_ = value;
  // @@protoc_insertion_point(field_set:protobuf_unittest.TestConflictingSymbolNames.handle_uninterpreted)
}

// repeated int32 index = 13;
inline int TestConflictingSymbolNames::index_size() const {
  return index_.size();
}
inline void TestConflictingSymbolNames::clear_index() {
  index_.Clear();
}
inline ::google::protobuf::int32 TestConflictingSymbolNames::index(int index) const {
  // @@protoc_insertion_point(field_get:protobuf_unittest.TestConflictingSymbolNames.index)
  return index_.Get(index);
}
inline void TestConflictingSymbolNames::set_index(int index, ::google::protobuf::int32 value) {
  index_.Set(index, value);
  // @@protoc_insertion_point(field_set:protobuf_unittest.TestConflictingSymbolNames.index)
}
inline void TestConflictingSymbolNames::add_index(::google::protobuf::int32 value) {
  index_.Add(value);
  // @@protoc_insertion_point(field_add:protobuf_unittest.TestConflictingSymbolNames.index)
}
inline const ::google::protobuf::RepeatedField< ::google::protobuf::int32 >&
TestConflictingSymbolNames::index() const {
  // @@protoc_insertion_point(field_list:protobuf_unittest.TestConflictingSymbolNames.index)
  return index_;
}
inline ::google::protobuf::RepeatedField< ::google::protobuf::int32 >*
TestConflictingSymbolNames::mutable_index() {
  // @@protoc_insertion_point(field_mutable_list:protobuf_unittest.TestConflictingSymbolNames.index)
  return &index_;
}

// optional int32 controller = 14;
inline bool TestConflictingSymbolNames::has_controller() const {
  return (_has_bits_[0] & 0x00002000u) != 0;
}
inline void TestConflictingSymbolNames::set_has_controller() {
  _has_bits_[0] |= 0x00002000u;
}
inline void TestConflictingSymbolNames::clear_has_controller() {
  _has_bits_[0] &= ~0x00002000u;
}
inline void TestConflictingSymbolNames::clear_controller() {
  controller_ = 0;
  clear_has_controller();
}
inline ::google::protobuf::int32 TestConflictingSymbolNames::controller() const {
  // @@protoc_insertion_point(field_get:protobuf_unittest.TestConflictingSymbolNames.controller)
  return controller_;
}
inline void TestConflictingSymbolNames::set_controller(::google::protobuf::int32 value) {
  set_has_controller();
  controller_ = value;
  // @@protoc_insertion_point(field_set:protobuf_unittest.TestConflictingSymbolNames.controller)
}

// optional int32 already_here = 15;
inline bool TestConflictingSymbolNames::has_already_here() const {
  return (_has_bits_[0] & 0x00004000u) != 0;
}
inline void TestConflictingSymbolNames::set_has_already_here() {
  _has_bits_[0] |= 0x00004000u;
}
inline void TestConflictingSymbolNames::clear_has_already_here() {
  _has_bits_[0] &= ~0x00004000u;
}
inline void TestConflictingSymbolNames::clear_already_here() {
  already_here_ = 0;
  clear_has_already_here();
}
inline ::google::protobuf::int32 TestConflictingSymbolNames::already_here() const {
  // @@protoc_insertion_point(field_get:protobuf_unittest.TestConflictingSymbolNames.already_here)
  return already_here_;
}
inline void TestConflictingSymbolNames::set_already_here(::google::protobuf::int32 value) {
  set_has_already_here();
  already_here_ = value;
  // @@protoc_insertion_point(field_set:protobuf_unittest.TestConflictingSymbolNames.already_here)
}

// optional uint32 uint32 = 16;
inline bool TestConflictingSymbolNames::has_uint32() const {
  return (_has_bits_[0] & 0x00008000u) != 0;
}
inline void TestConflictingSymbolNames::set_has_uint32() {
  _has_bits_[0] |= 0x00008000u;
}
inline void TestConflictingSymbolNames::clear_has_uint32() {
  _has_bits_[0] &= ~0x00008000u;
}
inline void TestConflictingSymbolNames::clear_uint32() {
  uint32_ = 0u;
  clear_has_uint32();
}
inline ::google::protobuf::uint32 TestConflictingSymbolNames::uint32() const {
  // @@protoc_insertion_point(field_get:protobuf_unittest.TestConflictingSymbolNames.uint32)
  return uint32_;
}
inline void TestConflictingSymbolNames::set_uint32(::google::protobuf::uint32 value) {
  set_has_uint32();
  uint32_ = value;
  // @@protoc_insertion_point(field_set:protobuf_unittest.TestConflictingSymbolNames.uint32)
}

// optional uint64 uint64 = 17;
inline bool TestConflictingSymbolNames::has_uint64() const {
  return (_has_bits_[0] & 0x00010000u) != 0;
}
inline void TestConflictingSymbolNames::set_has_uint64() {
  _has_bits_[0] |= 0x00010000u;
}
inline void TestConflictingSymbolNames::clear_has_uint64() {
  _has_bits_[0] &= ~0x00010000u;
}
inline void TestConflictingSymbolNames::clear_uint64() {
  uint64_ = GOOGLE_ULONGLONG(0);
  clear_has_uint64();
}
inline ::google::protobuf::uint64 TestConflictingSymbolNames::uint64() const {
  // @@protoc_insertion_point(field_get:protobuf_unittest.TestConflictingSymbolNames.uint64)
  return uint64_;
}
inline void TestConflictingSymbolNames::set_uint64(::google::protobuf::uint64 value) {
  set_has_uint64();
  uint64_ = value;
  // @@protoc_insertion_point(field_set:protobuf_unittest.TestConflictingSymbolNames.uint64)
}

// optional string string = 18;
inline bool TestConflictingSymbolNames::has_string() const {
  return (_has_bits_[0] & 0x00020000u) != 0;
}
inline void TestConflictingSymbolNames::set_has_string() {
  _has_bits_[0] |= 0x00020000u;
}
inline void TestConflictingSymbolNames::clear_has_string() {
  _has_bits_[0] &= ~0x00020000u;
}
inline void TestConflictingSymbolNames::clear_string() {
  string_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  clear_has_string();
}
inline const ::std::string& TestConflictingSymbolNames::string() const {
  // @@protoc_insertion_point(field_get:protobuf_unittest.TestConflictingSymbolNames.string)
  return string_.GetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void TestConflictingSymbolNames::set_string(const ::std::string& value) {
  set_has_string();
  string_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:protobuf_unittest.TestConflictingSymbolNames.string)
}
inline void TestConflictingSymbolNames::set_string(const char* value) {
  set_has_string();
  string_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:protobuf_unittest.TestConflictingSymbolNames.string)
}
inline void TestConflictingSymbolNames::set_string(const char* value, size_t size) {
  set_has_string();
  string_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:protobuf_unittest.TestConflictingSymbolNames.string)
}
inline ::std::string* TestConflictingSymbolNames::mutable_string() {
  set_has_string();
  // @@protoc_insertion_point(field_mutable:protobuf_unittest.TestConflictingSymbolNames.string)
  return string_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline ::std::string* TestConflictingSymbolNames::release_string() {
  // @@protoc_insertion_point(field_release:protobuf_unittest.TestConflictingSymbolNames.string)
  clear_has_string();
  return string_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void TestConflictingSymbolNames::set_allocated_string(::std::string* string) {
  if (string != NULL) {
    set_has_string();
  } else {
    clear_has_string();
  }
  string_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), string);
  // @@protoc_insertion_point(field_set_allocated:protobuf_unittest.TestConflictingSymbolNames.string)
}

// optional int32 memset = 19;
inline bool TestConflictingSymbolNames::has_memset() const {
  return (_has_bits_[0] & 0x00040000u) != 0;
}
inline void TestConflictingSymbolNames::set_has_memset() {
  _has_bits_[0] |= 0x00040000u;
}
inline void TestConflictingSymbolNames::clear_has_memset() {
  _has_bits_[0] &= ~0x00040000u;
}
inline void TestConflictingSymbolNames::clear_memset() {
  memset_ = 0;
  clear_has_memset();
}
inline ::google::protobuf::int32 TestConflictingSymbolNames::memset() const {
  // @@protoc_insertion_point(field_get:protobuf_unittest.TestConflictingSymbolNames.memset)
  return memset_;
}
inline void TestConflictingSymbolNames::set_memset(::google::protobuf::int32 value) {
  set_has_memset();
  memset_ = value;
  // @@protoc_insertion_point(field_set:protobuf_unittest.TestConflictingSymbolNames.memset)
}

// optional int32 int32 = 20;
inline bool TestConflictingSymbolNames::has_int32() const {
  return (_has_bits_[0] & 0x00080000u) != 0;
}
inline void TestConflictingSymbolNames::set_has_int32() {
  _has_bits_[0] |= 0x00080000u;
}
inline void TestConflictingSymbolNames::clear_has_int32() {
  _has_bits_[0] &= ~0x00080000u;
}
inline void TestConflictingSymbolNames::clear_int32() {
  int32_ = 0;
  clear_has_int32();
}
inline ::google::protobuf::int32 TestConflictingSymbolNames::int32() const {
  // @@protoc_insertion_point(field_get:protobuf_unittest.TestConflictingSymbolNames.int32)
  return int32_;
}
inline void TestConflictingSymbolNames::set_int32(::google::protobuf::int32 value) {
  set_has_int32();
  int32_ = value;
  // @@protoc_insertion_point(field_set:protobuf_unittest.TestConflictingSymbolNames.int32)
}

// optional int64 int64 = 21;
inline bool TestConflictingSymbolNames::has_int64() const {
  return (_has_bits_[0] & 0x00100000u) != 0;
}
inline void TestConflictingSymbolNames::set_has_int64() {
  _has_bits_[0] |= 0x00100000u;
}
inline void TestConflictingSymbolNames::clear_has_int64() {
  _has_bits_[0] &= ~0x00100000u;
}
inline void TestConflictingSymbolNames::clear_int64() {
  int64_ = GOOGLE_LONGLONG(0);
  clear_has_int64();
}
inline ::google::protobuf::int64 TestConflictingSymbolNames::int64() const {
  // @@protoc_insertion_point(field_get:protobuf_unittest.TestConflictingSymbolNames.int64)
  return int64_;
}
inline void TestConflictingSymbolNames::set_int64(::google::protobuf::int64 value) {
  set_has_int64();
  int64_ = value;
  // @@protoc_insertion_point(field_set:protobuf_unittest.TestConflictingSymbolNames.int64)
}

// optional uint32 cached_size = 22;
inline bool TestConflictingSymbolNames::has_cached_size() const {
  return (_has_bits_[0] & 0x00200000u) != 0;
}
inline void TestConflictingSymbolNames::set_has_cached_size() {
  _has_bits_[0] |= 0x00200000u;
}
inline void TestConflictingSymbolNames::clear_has_cached_size() {
  _has_bits_[0] &= ~0x00200000u;
}
inline void TestConflictingSymbolNames::clear_cached_size() {
  cached_size_ = 0u;
  clear_has_cached_size();
}
inline ::google::protobuf::uint32 TestConflictingSymbolNames::cached_size() const {
  // @@protoc_insertion_point(field_get:protobuf_unittest.TestConflictingSymbolNames.cached_size)
  return cached_size_;
}
inline void TestConflictingSymbolNames::set_cached_size(::google::protobuf::uint32 value) {
  set_has_cached_size();
  cached_size_ = value;
  // @@protoc_insertion_point(field_set:protobuf_unittest.TestConflictingSymbolNames.cached_size)
}

// optional uint32 extensions = 23;
inline bool TestConflictingSymbolNames::has_extensions() const {
  return (_has_bits_[0] & 0x00400000u) != 0;
}
inline void TestConflictingSymbolNames::set_has_extensions() {
  _has_bits_[0] |= 0x00400000u;
}
inline void TestConflictingSymbolNames::clear_has_extensions() {
  _has_bits_[0] &= ~0x00400000u;
}
inline void TestConflictingSymbolNames::clear_extensions() {
  extensions_ = 0u;
  clear_has_extensions();
}
inline ::google::protobuf::uint32 TestConflictingSymbolNames::extensions() const {
  // @@protoc_insertion_point(field_get:protobuf_unittest.TestConflictingSymbolNames.extensions)
  return extensions_;
}
inline void TestConflictingSymbolNames::set_extensions(::google::protobuf::uint32 value) {
  set_has_extensions();
  extensions_ = value;
  // @@protoc_insertion_point(field_set:protobuf_unittest.TestConflictingSymbolNames.extensions)
}

// optional uint32 bit = 24;
inline bool TestConflictingSymbolNames::has_bit() const {
  return (_has_bits_[0] & 0x00800000u) != 0;
}
inline void TestConflictingSymbolNames::set_has_bit() {
  _has_bits_[0] |= 0x00800000u;
}
inline void TestConflictingSymbolNames::clear_has_bit() {
  _has_bits_[0] &= ~0x00800000u;
}
inline void TestConflictingSymbolNames::clear_bit() {
  bit_ = 0u;
  clear_has_bit();
}
inline ::google::protobuf::uint32 TestConflictingSymbolNames::bit() const {
  // @@protoc_insertion_point(field_get:protobuf_unittest.TestConflictingSymbolNames.bit)
  return bit_;
}
inline void TestConflictingSymbolNames::set_bit(::google::protobuf::uint32 value) {
  set_has_bit();
  bit_ = value;
  // @@protoc_insertion_point(field_set:protobuf_unittest.TestConflictingSymbolNames.bit)
}

// optional uint32 bits = 25;
inline bool TestConflictingSymbolNames::has_bits() const {
  return (_has_bits_[0] & 0x01000000u) != 0;
}
inline void TestConflictingSymbolNames::set_has_bits() {
  _has_bits_[0] |= 0x01000000u;
}
inline void TestConflictingSymbolNames::clear_has_bits() {
  _has_bits_[0] &= ~0x01000000u;
}
inline void TestConflictingSymbolNames::clear_bits() {
  bits_ = 0u;
  clear_has_bits();
}
inline ::google::protobuf::uint32 TestConflictingSymbolNames::bits() const {
  // @@protoc_insertion_point(field_get:protobuf_unittest.TestConflictingSymbolNames.bits)
  return bits_;
}
inline void TestConflictingSymbolNames::set_bits(::google::protobuf::uint32 value) {
  set_has_bits();
  bits_ = value;
  // @@protoc_insertion_point(field_set:protobuf_unittest.TestConflictingSymbolNames.bits)
}

// optional uint32 offsets = 26;
inline bool TestConflictingSymbolNames::has_offsets() const {
  return (_has_bits_[0] & 0x02000000u) != 0;
}
inline void TestConflictingSymbolNames::set_has_offsets() {
  _has_bits_[0] |= 0x02000000u;
}
inline void TestConflictingSymbolNames::clear_has_offsets() {
  _has_bits_[0] &= ~0x02000000u;
}
inline void TestConflictingSymbolNames::clear_offsets() {
  offsets_ = 0u;
  clear_has_offsets();
}
inline ::google::protobuf::uint32 TestConflictingSymbolNames::offsets() const {
  // @@protoc_insertion_point(field_get:protobuf_unittest.TestConflictingSymbolNames.offsets)
  return offsets_;
}
inline void TestConflictingSymbolNames::set_offsets(::google::protobuf::uint32 value) {
  set_has_offsets();
  offsets_ = value;
  // @@protoc_insertion_point(field_set:protobuf_unittest.TestConflictingSymbolNames.offsets)
}

// optional uint32 reflection = 27;
inline bool TestConflictingSymbolNames::has_reflection() const {
  return (_has_bits_[0] & 0x04000000u) != 0;
}
inline void TestConflictingSymbolNames::set_has_reflection() {
  _has_bits_[0] |= 0x04000000u;
}
inline void TestConflictingSymbolNames::clear_has_reflection() {
  _has_bits_[0] &= ~0x04000000u;
}
inline void TestConflictingSymbolNames::clear_reflection() {
  reflection_ = 0u;
  clear_has_reflection();
}
inline ::google::protobuf::uint32 TestConflictingSymbolNames::reflection() const {
  // @@protoc_insertion_point(field_get:protobuf_unittest.TestConflictingSymbolNames.reflection)
  return reflection_;
}
inline void TestConflictingSymbolNames::set_reflection(::google::protobuf::uint32 value) {
  set_has_reflection();
  reflection_ = value;
  // @@protoc_insertion_point(field_set:protobuf_unittest.TestConflictingSymbolNames.reflection)
}

// optional string some_cord = 28 [ctype = CORD];
inline bool TestConflictingSymbolNames::has_some_cord() const {
  return (_has_bits_[0] & 0x08000000u) != 0;
}
inline void TestConflictingSymbolNames::set_has_some_cord() {
  _has_bits_[0] |= 0x08000000u;
}
inline void TestConflictingSymbolNames::clear_has_some_cord() {
  _has_bits_[0] &= ~0x08000000u;
}
inline void TestConflictingSymbolNames::clear_some_cord() {
  some_cord_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  clear_has_some_cord();
}
inline const ::std::string& TestConflictingSymbolNames::some_cord() const {
  // @@protoc_insertion_point(field_get:protobuf_unittest.TestConflictingSymbolNames.some_cord)
  return some_cord_.GetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void TestConflictingSymbolNames::set_some_cord(const ::std::string& value) {
  set_has_some_cord();
  some_cord_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:protobuf_unittest.TestConflictingSymbolNames.some_cord)
}
inline void TestConflictingSymbolNames::set_some_cord(const char* value) {
  set_has_some_cord();
  some_cord_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:protobuf_unittest.TestConflictingSymbolNames.some_cord)
}
inline void TestConflictingSymbolNames::set_some_cord(const char* value, size_t size) {
  set_has_some_cord();
  some_cord_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:protobuf_unittest.TestConflictingSymbolNames.some_cord)
}
inline ::std::string* TestConflictingSymbolNames::mutable_some_cord() {
  set_has_some_cord();
  // @@protoc_insertion_point(field_mutable:protobuf_unittest.TestConflictingSymbolNames.some_cord)
  return some_cord_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline ::std::string* TestConflictingSymbolNames::release_some_cord() {
  // @@protoc_insertion_point(field_release:protobuf_unittest.TestConflictingSymbolNames.some_cord)
  clear_has_some_cord();
  return some_cord_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void TestConflictingSymbolNames::set_allocated_some_cord(::std::string* some_cord) {
  if (some_cord != NULL) {
    set_has_some_cord();
  } else {
    clear_has_some_cord();
  }
  some_cord_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), some_cord);
  // @@protoc_insertion_point(field_set_allocated:protobuf_unittest.TestConflictingSymbolNames.some_cord)
}

// optional string some_string_piece = 29 [ctype = STRING_PIECE];
inline bool TestConflictingSymbolNames::has_some_string_piece() const {
  return (_has_bits_[0] & 0x10000000u) != 0;
}
inline void TestConflictingSymbolNames::set_has_some_string_piece() {
  _has_bits_[0] |= 0x10000000u;
}
inline void TestConflictingSymbolNames::clear_has_some_string_piece() {
  _has_bits_[0] &= ~0x10000000u;
}
inline void TestConflictingSymbolNames::clear_some_string_piece() {
  some_string_piece_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  clear_has_some_string_piece();
}
inline const ::std::string& TestConflictingSymbolNames::some_string_piece() const {
  // @@protoc_insertion_point(field_get:protobuf_unittest.TestConflictingSymbolNames.some_string_piece)
  return some_string_piece_.GetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void TestConflictingSymbolNames::set_some_string_piece(const ::std::string& value) {
  set_has_some_string_piece();
  some_string_piece_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:protobuf_unittest.TestConflictingSymbolNames.some_string_piece)
}
inline void TestConflictingSymbolNames::set_some_string_piece(const char* value) {
  set_has_some_string_piece();
  some_string_piece_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:protobuf_unittest.TestConflictingSymbolNames.some_string_piece)
}
inline void TestConflictingSymbolNames::set_some_string_piece(const char* value, size_t size) {
  set_has_some_string_piece();
  some_string_piece_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:protobuf_unittest.TestConflictingSymbolNames.some_string_piece)
}
inline ::std::string* TestConflictingSymbolNames::mutable_some_string_piece() {
  set_has_some_string_piece();
  // @@protoc_insertion_point(field_mutable:protobuf_unittest.TestConflictingSymbolNames.some_string_piece)
  return some_string_piece_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline ::std::string* TestConflictingSymbolNames::release_some_string_piece() {
  // @@protoc_insertion_point(field_release:protobuf_unittest.TestConflictingSymbolNames.some_string_piece)
  clear_has_some_string_piece();
  return some_string_piece_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void TestConflictingSymbolNames::set_allocated_some_string_piece(::std::string* some_string_piece) {
  if (some_string_piece != NULL) {
    set_has_some_string_piece();
  } else {
    clear_has_some_string_piece();
  }
  some_string_piece_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), some_string_piece);
  // @@protoc_insertion_point(field_set_allocated:protobuf_unittest.TestConflictingSymbolNames.some_string_piece)
}

// optional uint32 int = 30;
inline bool TestConflictingSymbolNames::has_int_() const {
  return (_has_bits_[0] & 0x20000000u) != 0;
}
inline void TestConflictingSymbolNames::set_has_int_() {
  _has_bits_[0] |= 0x20000000u;
}
inline void TestConflictingSymbolNames::clear_has_int_() {
  _has_bits_[0] &= ~0x20000000u;
}
inline void TestConflictingSymbolNames::clear_int_() {
  int__ = 0u;
  clear_has_int_();
}
inline ::google::protobuf::uint32 TestConflictingSymbolNames::int_() const {
  // @@protoc_insertion_point(field_get:protobuf_unittest.TestConflictingSymbolNames.int)
  return int__;
}
inline void TestConflictingSymbolNames::set_int_(::google::protobuf::uint32 value) {
  set_has_int_();
  int__ = value;
  // @@protoc_insertion_point(field_set:protobuf_unittest.TestConflictingSymbolNames.int)
}

// optional uint32 friend = 31;
inline bool TestConflictingSymbolNames::has_friend_() const {
  return (_has_bits_[0] & 0x40000000u) != 0;
}
inline void TestConflictingSymbolNames::set_has_friend_() {
  _has_bits_[0] |= 0x40000000u;
}
inline void TestConflictingSymbolNames::clear_has_friend_() {
  _has_bits_[0] &= ~0x40000000u;
}
inline void TestConflictingSymbolNames::clear_friend_() {
  friend__ = 0u;
  clear_has_friend_();
}
inline ::google::protobuf::uint32 TestConflictingSymbolNames::friend_() const {
  // @@protoc_insertion_point(field_get:protobuf_unittest.TestConflictingSymbolNames.friend)
  return friend__;
}
inline void TestConflictingSymbolNames::set_friend_(::google::protobuf::uint32 value) {
  set_has_friend_();
  friend__ = value;
  // @@protoc_insertion_point(field_set:protobuf_unittest.TestConflictingSymbolNames.friend)
}

// optional uint32 class = 37;
inline bool TestConflictingSymbolNames::has_class_() const {
  return (_has_bits_[0] & 0x80000000u) != 0;
}
inline void TestConflictingSymbolNames::set_has_class_() {
  _has_bits_[0] |= 0x80000000u;
}
inline void TestConflictingSymbolNames::clear_has_class_() {
  _has_bits_[0] &= ~0x80000000u;
}
inline void TestConflictingSymbolNames::clear_class_() {
  class__ = 0u;
  clear_has_class_();
}
inline ::google::protobuf::uint32 TestConflictingSymbolNames::class_() const {
  // @@protoc_insertion_point(field_get:protobuf_unittest.TestConflictingSymbolNames.class)
  return class__;
}
inline void TestConflictingSymbolNames::set_class_(::google::protobuf::uint32 value) {
  set_has_class_();
  class__ = value;
  // @@protoc_insertion_point(field_set:protobuf_unittest.TestConflictingSymbolNames.class)
}

// optional uint32 typedecl = 39;
inline bool TestConflictingSymbolNames::has_typedecl() const {
  return (_has_bits_[1] & 0x00000001u) != 0;
}
inline void TestConflictingSymbolNames::set_has_typedecl() {
  _has_bits_[1] |= 0x00000001u;
}
inline void TestConflictingSymbolNames::clear_has_typedecl() {
  _has_bits_[1] &= ~0x00000001u;
}
inline void TestConflictingSymbolNames::clear_typedecl() {
  typedecl_ = 0u;
  clear_has_typedecl();
}
inline ::google::protobuf::uint32 TestConflictingSymbolNames::typedecl() const {
  // @@protoc_insertion_point(field_get:protobuf_unittest.TestConflictingSymbolNames.typedecl)
  return typedecl_;
}
inline void TestConflictingSymbolNames::set_typedecl(::google::protobuf::uint32 value) {
  set_has_typedecl();
  typedecl_ = value;
  // @@protoc_insertion_point(field_set:protobuf_unittest.TestConflictingSymbolNames.typedecl)
}

// optional uint32 auto = 40;
inline bool TestConflictingSymbolNames::has_auto_() const {
  return (_has_bits_[1] & 0x00000002u) != 0;
}
inline void TestConflictingSymbolNames::set_has_auto_() {
  _has_bits_[1] |= 0x00000002u;
}
inline void TestConflictingSymbolNames::clear_has_auto_() {
  _has_bits_[1] &= ~0x00000002u;
}
inline void TestConflictingSymbolNames::clear_auto_() {
  auto__ = 0u;
  clear_has_auto_();
}
inline ::google::protobuf::uint32 TestConflictingSymbolNames::auto_() const {
  // @@protoc_insertion_point(field_get:protobuf_unittest.TestConflictingSymbolNames.auto)
  return auto__;
}
inline void TestConflictingSymbolNames::set_auto_(::google::protobuf::uint32 value) {
  set_has_auto_();
  auto__ = value;
  // @@protoc_insertion_point(field_set:protobuf_unittest.TestConflictingSymbolNames.auto)
}

// optional .protobuf_unittest.TestConflictingSymbolNames.DO do = 32;
inline bool TestConflictingSymbolNames::has_do_() const {
  return (_has_bits_[1] & 0x00000004u) != 0;
}
inline void TestConflictingSymbolNames::set_has_do_() {
  _has_bits_[1] |= 0x00000004u;
}
inline void TestConflictingSymbolNames::clear_has_do_() {
  _has_bits_[1] &= ~0x00000004u;
}
inline void TestConflictingSymbolNames::clear_do_() {
  if (do__ != NULL) do__->::protobuf_unittest::TestConflictingSymbolNames_DO::Clear();
  clear_has_do_();
}
inline const ::protobuf_unittest::TestConflictingSymbolNames_DO& TestConflictingSymbolNames::do_() const {
  // @@protoc_insertion_point(field_get:protobuf_unittest.TestConflictingSymbolNames.do)
  return do__ != NULL ? *do__
                         : *::protobuf_unittest::TestConflictingSymbolNames_DO::internal_default_instance();
}
inline ::protobuf_unittest::TestConflictingSymbolNames_DO* TestConflictingSymbolNames::mutable_do_() {
  set_has_do_();
  if (do__ == NULL) {
    do__ = new ::protobuf_unittest::TestConflictingSymbolNames_DO;
  }
  // @@protoc_insertion_point(field_mutable:protobuf_unittest.TestConflictingSymbolNames.do)
  return do__;
}
inline ::protobuf_unittest::TestConflictingSymbolNames_DO* TestConflictingSymbolNames::release_do__() {
  // @@protoc_insertion_point(field_release:protobuf_unittest.TestConflictingSymbolNames.do)
  clear_has_do_();
  ::protobuf_unittest::TestConflictingSymbolNames_DO* temp = do__;
  do__ = NULL;
  return temp;
}
inline void TestConflictingSymbolNames::set_allocated_do_(::protobuf_unittest::TestConflictingSymbolNames_DO* do_) {
  delete do__;
  do__ = do_;
  if (do_) {
    set_has_do_();
  } else {
    clear_has_do_();
  }
  // @@protoc_insertion_point(field_set_allocated:protobuf_unittest.TestConflictingSymbolNames.do)
}

// optional int32 field_type = 33;
inline bool TestConflictingSymbolNames::has_field_type() const {
  return (_has_bits_[1] & 0x00000008u) != 0;
}
inline void TestConflictingSymbolNames::set_has_field_type() {
  _has_bits_[1] |= 0x00000008u;
}
inline void TestConflictingSymbolNames::clear_has_field_type() {
  _has_bits_[1] &= ~0x00000008u;
}
inline void TestConflictingSymbolNames::clear_field_type() {
  field_type_ = 0;
  clear_has_field_type();
}
inline ::google::protobuf::int32 TestConflictingSymbolNames::field_type() const {
  // @@protoc_insertion_point(field_get:protobuf_unittest.TestConflictingSymbolNames.field_type)
  return field_type_;
}
inline void TestConflictingSymbolNames::set_field_type(::google::protobuf::int32 value) {
  set_has_field_type();
  field_type_ = value;
  // @@protoc_insertion_point(field_set:protobuf_unittest.TestConflictingSymbolNames.field_type)
}

// optional bool is_packed = 34;
inline bool TestConflictingSymbolNames::has_is_packed() const {
  return (_has_bits_[1] & 0x00000010u) != 0;
}
inline void TestConflictingSymbolNames::set_has_is_packed() {
  _has_bits_[1] |= 0x00000010u;
}
inline void TestConflictingSymbolNames::clear_has_is_packed() {
  _has_bits_[1] &= ~0x00000010u;
}
inline void TestConflictingSymbolNames::clear_is_packed() {
  is_packed_ = false;
  clear_has_is_packed();
}
inline bool TestConflictingSymbolNames::is_packed() const {
  // @@protoc_insertion_point(field_get:protobuf_unittest.TestConflictingSymbolNames.is_packed)
  return is_packed_;
}
inline void TestConflictingSymbolNames::set_is_packed(bool value) {
  set_has_is_packed();
  is_packed_ = value;
  // @@protoc_insertion_point(field_set:protobuf_unittest.TestConflictingSymbolNames.is_packed)
}

// optional string release_length = 35;
inline bool TestConflictingSymbolNames::has_release_length() const {
  return (_has_bits_[1] & 0x00000020u) != 0;
}
inline void TestConflictingSymbolNames::set_has_release_length() {
  _has_bits_[1] |= 0x00000020u;
}
inline void TestConflictingSymbolNames::clear_has_release_length() {
  _has_bits_[1] &= ~0x00000020u;
}
inline void TestConflictingSymbolNames::clear_release_length() {
  release_length_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  clear_has_release_length();
}
inline const ::std::string& TestConflictingSymbolNames::release_length() const {
  // @@protoc_insertion_point(field_get:protobuf_unittest.TestConflictingSymbolNames.release_length)
  return release_length_.GetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void TestConflictingSymbolNames::set_release_length(const ::std::string& value) {
  set_has_release_length();
  release_length_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:protobuf_unittest.TestConflictingSymbolNames.release_length)
}
inline void TestConflictingSymbolNames::set_release_length(const char* value) {
  set_has_release_length();
  release_length_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:protobuf_unittest.TestConflictingSymbolNames.release_length)
}
inline void TestConflictingSymbolNames::set_release_length(const char* value, size_t size) {
  set_has_release_length();
  release_length_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:protobuf_unittest.TestConflictingSymbolNames.release_length)
}
inline ::std::string* TestConflictingSymbolNames::mutable_release_length() {
  set_has_release_length();
  // @@protoc_insertion_point(field_mutable:protobuf_unittest.TestConflictingSymbolNames.release_length)
  return release_length_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline ::std::string* TestConflictingSymbolNames::release_release_length() {
  // @@protoc_insertion_point(field_release:protobuf_unittest.TestConflictingSymbolNames.release_length)
  clear_has_release_length();
  return release_length_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void TestConflictingSymbolNames::set_allocated_release_length(::std::string* release_length) {
  if (release_length != NULL) {
    set_has_release_length();
  } else {
    clear_has_release_length();
  }
  release_length_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), release_length);
  // @@protoc_insertion_point(field_set_allocated:protobuf_unittest.TestConflictingSymbolNames.release_length)
}

// optional .protobuf_unittest.TestConflictingSymbolNames.DO release_do = 36;
inline bool TestConflictingSymbolNames::has_release_do() const {
  return (_has_bits_[1] & 0x00000040u) != 0;
}
inline void TestConflictingSymbolNames::set_has_release_do() {
  _has_bits_[1] |= 0x00000040u;
}
inline void TestConflictingSymbolNames::clear_has_release_do() {
  _has_bits_[1] &= ~0x00000040u;
}
inline void TestConflictingSymbolNames::clear_release_do() {
  if (release_do_ != NULL) release_do_->::protobuf_unittest::TestConflictingSymbolNames_DO::Clear();
  clear_has_release_do();
}
inline const ::protobuf_unittest::TestConflictingSymbolNames_DO& TestConflictingSymbolNames::release_do() const {
  // @@protoc_insertion_point(field_get:protobuf_unittest.TestConflictingSymbolNames.release_do)
  return release_do_ != NULL ? *release_do_
                         : *::protobuf_unittest::TestConflictingSymbolNames_DO::internal_default_instance();
}
inline ::protobuf_unittest::TestConflictingSymbolNames_DO* TestConflictingSymbolNames::mutable_release_do() {
  set_has_release_do();
  if (release_do_ == NULL) {
    release_do_ = new ::protobuf_unittest::TestConflictingSymbolNames_DO;
  }
  // @@protoc_insertion_point(field_mutable:protobuf_unittest.TestConflictingSymbolNames.release_do)
  return release_do_;
}
inline ::protobuf_unittest::TestConflictingSymbolNames_DO* TestConflictingSymbolNames::release_release_do() {
  // @@protoc_insertion_point(field_release:protobuf_unittest.TestConflictingSymbolNames.release_do)
  clear_has_release_do();
  ::protobuf_unittest::TestConflictingSymbolNames_DO* temp = release_do_;
  release_do_ = NULL;
  return temp;
}
inline void TestConflictingSymbolNames::set_allocated_release_do(::protobuf_unittest::TestConflictingSymbolNames_DO* release_do) {
  delete release_do_;
  release_do_ = release_do;
  if (release_do) {
    set_has_release_do();
  } else {
    clear_has_release_do();
  }
  // @@protoc_insertion_point(field_set_allocated:protobuf_unittest.TestConflictingSymbolNames.release_do)
}

// optional string target = 38;
inline bool TestConflictingSymbolNames::has_target() const {
  return (_has_bits_[1] & 0x00000080u) != 0;
}
inline void TestConflictingSymbolNames::set_has_target() {
  _has_bits_[1] |= 0x00000080u;
}
inline void TestConflictingSymbolNames::clear_has_target() {
  _has_bits_[1] &= ~0x00000080u;
}
inline void TestConflictingSymbolNames::clear_target() {
  target_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  clear_has_target();
}
inline const ::std::string& TestConflictingSymbolNames::target() const {
  // @@protoc_insertion_point(field_get:protobuf_unittest.TestConflictingSymbolNames.target)
  return target_.GetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void TestConflictingSymbolNames::set_target(const ::std::string& value) {
  set_has_target();
  target_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:protobuf_unittest.TestConflictingSymbolNames.target)
}
inline void TestConflictingSymbolNames::set_target(const char* value) {
  set_has_target();
  target_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:protobuf_unittest.TestConflictingSymbolNames.target)
}
inline void TestConflictingSymbolNames::set_target(const char* value, size_t size) {
  set_has_target();
  target_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:protobuf_unittest.TestConflictingSymbolNames.target)
}
inline ::std::string* TestConflictingSymbolNames::mutable_target() {
  set_has_target();
  // @@protoc_insertion_point(field_mutable:protobuf_unittest.TestConflictingSymbolNames.target)
  return target_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline ::std::string* TestConflictingSymbolNames::release_target() {
  // @@protoc_insertion_point(field_release:protobuf_unittest.TestConflictingSymbolNames.target)
  clear_has_target();
  return target_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void TestConflictingSymbolNames::set_allocated_target(::std::string* target) {
  if (target != NULL) {
    set_has_target();
  } else {
    clear_has_target();
  }
  target_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), target);
  // @@protoc_insertion_point(field_set_allocated:protobuf_unittest.TestConflictingSymbolNames.target)
}

inline const TestConflictingSymbolNames* TestConflictingSymbolNames::internal_default_instance() {
  return &TestConflictingSymbolNames_default_instance_.get();
}
// -------------------------------------------------------------------

// TestConflictingSymbolNamesExtension

inline const TestConflictingSymbolNamesExtension* TestConflictingSymbolNamesExtension::internal_default_instance() {
  return &TestConflictingSymbolNamesExtension_default_instance_.get();
}
// -------------------------------------------------------------------

// TestConflictingEnumNames

// optional .protobuf_unittest.TestConflictingEnumNames.NestedConflictingEnum conflicting_enum = 1;
inline bool TestConflictingEnumNames::has_conflicting_enum() const {
  return (_has_bits_[0] & 0x00000001u) != 0;
}
inline void TestConflictingEnumNames::set_has_conflicting_enum() {
  _has_bits_[0] |= 0x00000001u;
}
inline void TestConflictingEnumNames::clear_has_conflicting_enum() {
  _has_bits_[0] &= ~0x00000001u;
}
inline void TestConflictingEnumNames::clear_conflicting_enum() {
  conflicting_enum_ = 1;
  clear_has_conflicting_enum();
}
inline ::protobuf_unittest::TestConflictingEnumNames_NestedConflictingEnum TestConflictingEnumNames::conflicting_enum() const {
  // @@protoc_insertion_point(field_get:protobuf_unittest.TestConflictingEnumNames.conflicting_enum)
  return static_cast< ::protobuf_unittest::TestConflictingEnumNames_NestedConflictingEnum >(conflicting_enum_);
}
inline void TestConflictingEnumNames::set_conflicting_enum(::protobuf_unittest::TestConflictingEnumNames_NestedConflictingEnum value) {
  assert(::protobuf_unittest::TestConflictingEnumNames_NestedConflictingEnum_IsValid(value));
  set_has_conflicting_enum();
  conflicting_enum_ = value;
  // @@protoc_insertion_point(field_set:protobuf_unittest.TestConflictingEnumNames.conflicting_enum)
}

inline const TestConflictingEnumNames* TestConflictingEnumNames::internal_default_instance() {
  return &TestConflictingEnumNames_default_instance_.get();
}
// -------------------------------------------------------------------

// DummyMessage

inline const DummyMessage* DummyMessage::internal_default_instance() {
  return &DummyMessage_default_instance_.get();
}
#endif  // !PROTOBUF_INLINE_NOT_IN_HEADERS
// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------


// @@protoc_insertion_point(namespace_scope)

}  // namespace protobuf_unittest

#ifndef SWIG
namespace google {
namespace protobuf {

template <> struct is_proto_enum< ::protobuf_unittest::TestConflictingSymbolNames_TestEnum> : ::google::protobuf::internal::true_type {};
template <>
inline const EnumDescriptor* GetEnumDescriptor< ::protobuf_unittest::TestConflictingSymbolNames_TestEnum>() {
  return ::protobuf_unittest::TestConflictingSymbolNames_TestEnum_descriptor();
}
template <> struct is_proto_enum< ::protobuf_unittest::TestConflictingEnumNames_NestedConflictingEnum> : ::google::protobuf::internal::true_type {};
template <>
inline const EnumDescriptor* GetEnumDescriptor< ::protobuf_unittest::TestConflictingEnumNames_NestedConflictingEnum>() {
  return ::protobuf_unittest::TestConflictingEnumNames_NestedConflictingEnum_descriptor();
}
template <> struct is_proto_enum< ::protobuf_unittest::ConflictingEnum> : ::google::protobuf::internal::true_type {};
template <>
inline const EnumDescriptor* GetEnumDescriptor< ::protobuf_unittest::ConflictingEnum>() {
  return ::protobuf_unittest::ConflictingEnum_descriptor();
}

}  // namespace protobuf
}  // namespace google
#endif  // SWIG

// @@protoc_insertion_point(global_scope)

#endif  // PROTOBUF_google_2fprotobuf_2fcompiler_2fcpp_2fcpp_5ftest_5fbad_5fidentifiers_2eproto__INCLUDED

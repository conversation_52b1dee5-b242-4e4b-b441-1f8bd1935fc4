// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: google/protobuf/unittest_proto3_arena.proto

#ifndef PROTOBUF_google_2fprotobuf_2funittest_5fproto3_5farena_2eproto__INCLUDED
#define PROTOBUF_google_2fprotobuf_2funittest_5fproto3_5farena_2eproto__INCLUDED

#include <string>

#include <google/protobuf/stubs/common.h>

#if GOOGLE_PROTOBUF_VERSION < 3001000
#error This file was generated by a newer version of protoc which is
#error incompatible with your Protocol Buffer headers.  Please update
#error your headers.
#endif
#if 3001000 < GOOGLE_PROTOBUF_MIN_PROTOC_VERSION
#error This file was generated by an older version of protoc which is
#error incompatible with your Protocol Buffer headers.  Please
#error regenerate this file with a newer version of protoc.
#endif

#include <google/protobuf/arena.h>
#include <google/protobuf/arenastring.h>
#include <google/protobuf/generated_message_util.h>
#include <google/protobuf/metadata.h>
#include <google/protobuf/message.h>
#include <google/protobuf/repeated_field.h>
#include <google/protobuf/extension_set.h>
#include <google/protobuf/generated_enum_reflection.h>
#include <google/protobuf/unknown_field_set.h>
#include <google/protobuf/unittest_import.pb.h>
// @@protoc_insertion_point(includes)

namespace proto3_arena_unittest {

// Internal implementation detail -- do not call these.
void protobuf_AddDesc_google_2fprotobuf_2funittest_5fproto3_5farena_2eproto();
void protobuf_InitDefaults_google_2fprotobuf_2funittest_5fproto3_5farena_2eproto();
void protobuf_AssignDesc_google_2fprotobuf_2funittest_5fproto3_5farena_2eproto();
void protobuf_ShutdownFile_google_2fprotobuf_2funittest_5fproto3_5farena_2eproto();

class ForeignMessage;
class NestedTestAllTypes;
class TestAllTypes;
class TestAllTypes_NestedMessage;
class TestEmptyMessage;
class TestPackedTypes;
class TestUnpackedTypes;

enum TestAllTypes_NestedEnum {
  TestAllTypes_NestedEnum_ZERO = 0,
  TestAllTypes_NestedEnum_FOO = 1,
  TestAllTypes_NestedEnum_BAR = 2,
  TestAllTypes_NestedEnum_BAZ = 3,
  TestAllTypes_NestedEnum_NEG = -1,
  TestAllTypes_NestedEnum_TestAllTypes_NestedEnum_INT_MIN_SENTINEL_DO_NOT_USE_ = ::google::protobuf::kint32min,
  TestAllTypes_NestedEnum_TestAllTypes_NestedEnum_INT_MAX_SENTINEL_DO_NOT_USE_ = ::google::protobuf::kint32max
};
bool TestAllTypes_NestedEnum_IsValid(int value);
const TestAllTypes_NestedEnum TestAllTypes_NestedEnum_NestedEnum_MIN = TestAllTypes_NestedEnum_NEG;
const TestAllTypes_NestedEnum TestAllTypes_NestedEnum_NestedEnum_MAX = TestAllTypes_NestedEnum_BAZ;
const int TestAllTypes_NestedEnum_NestedEnum_ARRAYSIZE = TestAllTypes_NestedEnum_NestedEnum_MAX + 1;

const ::google::protobuf::EnumDescriptor* TestAllTypes_NestedEnum_descriptor();
inline const ::std::string& TestAllTypes_NestedEnum_Name(TestAllTypes_NestedEnum value) {
  return ::google::protobuf::internal::NameOfEnum(
    TestAllTypes_NestedEnum_descriptor(), value);
}
inline bool TestAllTypes_NestedEnum_Parse(
    const ::std::string& name, TestAllTypes_NestedEnum* value) {
  return ::google::protobuf::internal::ParseNamedEnum<TestAllTypes_NestedEnum>(
    TestAllTypes_NestedEnum_descriptor(), name, value);
}
enum ForeignEnum {
  FOREIGN_ZERO = 0,
  FOREIGN_FOO = 4,
  FOREIGN_BAR = 5,
  FOREIGN_BAZ = 6,
  ForeignEnum_INT_MIN_SENTINEL_DO_NOT_USE_ = ::google::protobuf::kint32min,
  ForeignEnum_INT_MAX_SENTINEL_DO_NOT_USE_ = ::google::protobuf::kint32max
};
bool ForeignEnum_IsValid(int value);
const ForeignEnum ForeignEnum_MIN = FOREIGN_ZERO;
const ForeignEnum ForeignEnum_MAX = FOREIGN_BAZ;
const int ForeignEnum_ARRAYSIZE = ForeignEnum_MAX + 1;

const ::google::protobuf::EnumDescriptor* ForeignEnum_descriptor();
inline const ::std::string& ForeignEnum_Name(ForeignEnum value) {
  return ::google::protobuf::internal::NameOfEnum(
    ForeignEnum_descriptor(), value);
}
inline bool ForeignEnum_Parse(
    const ::std::string& name, ForeignEnum* value) {
  return ::google::protobuf::internal::ParseNamedEnum<ForeignEnum>(
    ForeignEnum_descriptor(), name, value);
}
// ===================================================================

class TestAllTypes_NestedMessage : public ::google::protobuf::Message /* @@protoc_insertion_point(class_definition:proto3_arena_unittest.TestAllTypes.NestedMessage) */ {
 public:
  TestAllTypes_NestedMessage();
  virtual ~TestAllTypes_NestedMessage();

  TestAllTypes_NestedMessage(const TestAllTypes_NestedMessage& from);

  inline TestAllTypes_NestedMessage& operator=(const TestAllTypes_NestedMessage& from) {
    CopyFrom(from);
    return *this;
  }

  inline ::google::protobuf::Arena* GetArena() const { return GetArenaNoVirtual(); }
  inline void* GetMaybeArenaPointer() const {
    return MaybeArenaPtr();
  }
  static const ::google::protobuf::Descriptor* descriptor();
  static const TestAllTypes_NestedMessage& default_instance();

  static const TestAllTypes_NestedMessage* internal_default_instance();

  void UnsafeArenaSwap(TestAllTypes_NestedMessage* other);
  void Swap(TestAllTypes_NestedMessage* other);

  // implements Message ----------------------------------------------

  inline TestAllTypes_NestedMessage* New() const { return New(NULL); }

  TestAllTypes_NestedMessage* New(::google::protobuf::Arena* arena) const;
  void CopyFrom(const ::google::protobuf::Message& from);
  void MergeFrom(const ::google::protobuf::Message& from);
  void CopyFrom(const TestAllTypes_NestedMessage& from);
  void MergeFrom(const TestAllTypes_NestedMessage& from);
  void Clear();
  bool IsInitialized() const;

  size_t ByteSizeLong() const;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input);
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const;
  ::google::protobuf::uint8* InternalSerializeWithCachedSizesToArray(
      bool deterministic, ::google::protobuf::uint8* output) const;
  ::google::protobuf::uint8* SerializeWithCachedSizesToArray(::google::protobuf::uint8* output) const {
    return InternalSerializeWithCachedSizesToArray(false, output);
  }
  int GetCachedSize() const { return _cached_size_; }
  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const;
  void InternalSwap(TestAllTypes_NestedMessage* other);
  void UnsafeMergeFrom(const TestAllTypes_NestedMessage& from);
  protected:
  explicit TestAllTypes_NestedMessage(::google::protobuf::Arena* arena);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::google::protobuf::Arena* arena);
  private:
  inline ::google::protobuf::Arena* GetArenaNoVirtual() const {
    return _internal_metadata_.arena();
  }
  inline void* MaybeArenaPtr() const {
    return _internal_metadata_.raw_arena_ptr();
  }
  public:

  ::google::protobuf::Metadata GetMetadata() const;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // optional int32 bb = 1;
  void clear_bb();
  static const int kBbFieldNumber = 1;
  ::google::protobuf::int32 bb() const;
  void set_bb(::google::protobuf::int32 value);

  // @@protoc_insertion_point(class_scope:proto3_arena_unittest.TestAllTypes.NestedMessage)
 private:

  ::google::protobuf::internal::InternalMetadataWithArena _internal_metadata_;
  friend class ::google::protobuf::Arena;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  ::google::protobuf::int32 bb_;
  mutable int _cached_size_;
  friend void  protobuf_InitDefaults_google_2fprotobuf_2funittest_5fproto3_5farena_2eproto_impl();
  friend void  protobuf_AddDesc_google_2fprotobuf_2funittest_5fproto3_5farena_2eproto_impl();
  friend void protobuf_AssignDesc_google_2fprotobuf_2funittest_5fproto3_5farena_2eproto();
  friend void protobuf_ShutdownFile_google_2fprotobuf_2funittest_5fproto3_5farena_2eproto();

  void InitAsDefaultInstance();
};
extern ::google::protobuf::internal::ExplicitlyConstructed<TestAllTypes_NestedMessage> TestAllTypes_NestedMessage_default_instance_;

// -------------------------------------------------------------------

class TestAllTypes : public ::google::protobuf::Message /* @@protoc_insertion_point(class_definition:proto3_arena_unittest.TestAllTypes) */ {
 public:
  TestAllTypes();
  virtual ~TestAllTypes();

  TestAllTypes(const TestAllTypes& from);

  inline TestAllTypes& operator=(const TestAllTypes& from) {
    CopyFrom(from);
    return *this;
  }

  inline ::google::protobuf::Arena* GetArena() const { return GetArenaNoVirtual(); }
  inline void* GetMaybeArenaPointer() const {
    return MaybeArenaPtr();
  }
  static const ::google::protobuf::Descriptor* descriptor();
  static const TestAllTypes& default_instance();

  enum OneofFieldCase {
    kOneofUint32 = 111,
    kOneofNestedMessage = 112,
    kOneofString = 113,
    kOneofBytes = 114,
    ONEOF_FIELD_NOT_SET = 0,
  };

  static const TestAllTypes* internal_default_instance();

  void UnsafeArenaSwap(TestAllTypes* other);
  void Swap(TestAllTypes* other);

  // implements Message ----------------------------------------------

  inline TestAllTypes* New() const { return New(NULL); }

  TestAllTypes* New(::google::protobuf::Arena* arena) const;
  void CopyFrom(const ::google::protobuf::Message& from);
  void MergeFrom(const ::google::protobuf::Message& from);
  void CopyFrom(const TestAllTypes& from);
  void MergeFrom(const TestAllTypes& from);
  void Clear();
  bool IsInitialized() const;

  size_t ByteSizeLong() const;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input);
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const;
  ::google::protobuf::uint8* InternalSerializeWithCachedSizesToArray(
      bool deterministic, ::google::protobuf::uint8* output) const;
  ::google::protobuf::uint8* SerializeWithCachedSizesToArray(::google::protobuf::uint8* output) const {
    return InternalSerializeWithCachedSizesToArray(false, output);
  }
  int GetCachedSize() const { return _cached_size_; }
  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const;
  void InternalSwap(TestAllTypes* other);
  void UnsafeMergeFrom(const TestAllTypes& from);
  protected:
  explicit TestAllTypes(::google::protobuf::Arena* arena);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::google::protobuf::Arena* arena);
  private:
  inline ::google::protobuf::Arena* GetArenaNoVirtual() const {
    return _internal_metadata_.arena();
  }
  inline void* MaybeArenaPtr() const {
    return _internal_metadata_.raw_arena_ptr();
  }
  public:

  ::google::protobuf::Metadata GetMetadata() const;

  // nested types ----------------------------------------------------

  typedef TestAllTypes_NestedMessage NestedMessage;

  typedef TestAllTypes_NestedEnum NestedEnum;
  static const NestedEnum ZERO =
    TestAllTypes_NestedEnum_ZERO;
  static const NestedEnum FOO =
    TestAllTypes_NestedEnum_FOO;
  static const NestedEnum BAR =
    TestAllTypes_NestedEnum_BAR;
  static const NestedEnum BAZ =
    TestAllTypes_NestedEnum_BAZ;
  static const NestedEnum NEG =
    TestAllTypes_NestedEnum_NEG;
  static inline bool NestedEnum_IsValid(int value) {
    return TestAllTypes_NestedEnum_IsValid(value);
  }
  static const NestedEnum NestedEnum_MIN =
    TestAllTypes_NestedEnum_NestedEnum_MIN;
  static const NestedEnum NestedEnum_MAX =
    TestAllTypes_NestedEnum_NestedEnum_MAX;
  static const int NestedEnum_ARRAYSIZE =
    TestAllTypes_NestedEnum_NestedEnum_ARRAYSIZE;
  static inline const ::google::protobuf::EnumDescriptor*
  NestedEnum_descriptor() {
    return TestAllTypes_NestedEnum_descriptor();
  }
  static inline const ::std::string& NestedEnum_Name(NestedEnum value) {
    return TestAllTypes_NestedEnum_Name(value);
  }
  static inline bool NestedEnum_Parse(const ::std::string& name,
      NestedEnum* value) {
    return TestAllTypes_NestedEnum_Parse(name, value);
  }

  // accessors -------------------------------------------------------

  // optional int32 optional_int32 = 1;
  void clear_optional_int32();
  static const int kOptionalInt32FieldNumber = 1;
  ::google::protobuf::int32 optional_int32() const;
  void set_optional_int32(::google::protobuf::int32 value);

  // optional int64 optional_int64 = 2;
  void clear_optional_int64();
  static const int kOptionalInt64FieldNumber = 2;
  ::google::protobuf::int64 optional_int64() const;
  void set_optional_int64(::google::protobuf::int64 value);

  // optional uint32 optional_uint32 = 3;
  void clear_optional_uint32();
  static const int kOptionalUint32FieldNumber = 3;
  ::google::protobuf::uint32 optional_uint32() const;
  void set_optional_uint32(::google::protobuf::uint32 value);

  // optional uint64 optional_uint64 = 4;
  void clear_optional_uint64();
  static const int kOptionalUint64FieldNumber = 4;
  ::google::protobuf::uint64 optional_uint64() const;
  void set_optional_uint64(::google::protobuf::uint64 value);

  // optional sint32 optional_sint32 = 5;
  void clear_optional_sint32();
  static const int kOptionalSint32FieldNumber = 5;
  ::google::protobuf::int32 optional_sint32() const;
  void set_optional_sint32(::google::protobuf::int32 value);

  // optional sint64 optional_sint64 = 6;
  void clear_optional_sint64();
  static const int kOptionalSint64FieldNumber = 6;
  ::google::protobuf::int64 optional_sint64() const;
  void set_optional_sint64(::google::protobuf::int64 value);

  // optional fixed32 optional_fixed32 = 7;
  void clear_optional_fixed32();
  static const int kOptionalFixed32FieldNumber = 7;
  ::google::protobuf::uint32 optional_fixed32() const;
  void set_optional_fixed32(::google::protobuf::uint32 value);

  // optional fixed64 optional_fixed64 = 8;
  void clear_optional_fixed64();
  static const int kOptionalFixed64FieldNumber = 8;
  ::google::protobuf::uint64 optional_fixed64() const;
  void set_optional_fixed64(::google::protobuf::uint64 value);

  // optional sfixed32 optional_sfixed32 = 9;
  void clear_optional_sfixed32();
  static const int kOptionalSfixed32FieldNumber = 9;
  ::google::protobuf::int32 optional_sfixed32() const;
  void set_optional_sfixed32(::google::protobuf::int32 value);

  // optional sfixed64 optional_sfixed64 = 10;
  void clear_optional_sfixed64();
  static const int kOptionalSfixed64FieldNumber = 10;
  ::google::protobuf::int64 optional_sfixed64() const;
  void set_optional_sfixed64(::google::protobuf::int64 value);

  // optional float optional_float = 11;
  void clear_optional_float();
  static const int kOptionalFloatFieldNumber = 11;
  float optional_float() const;
  void set_optional_float(float value);

  // optional double optional_double = 12;
  void clear_optional_double();
  static const int kOptionalDoubleFieldNumber = 12;
  double optional_double() const;
  void set_optional_double(double value);

  // optional bool optional_bool = 13;
  void clear_optional_bool();
  static const int kOptionalBoolFieldNumber = 13;
  bool optional_bool() const;
  void set_optional_bool(bool value);

  // optional string optional_string = 14;
  void clear_optional_string();
  static const int kOptionalStringFieldNumber = 14;
  const ::std::string& optional_string() const;
  void set_optional_string(const ::std::string& value);
  void set_optional_string(const char* value);
  void set_optional_string(const char* value, size_t size);
  ::std::string* mutable_optional_string();
  ::std::string* release_optional_string();
  void set_allocated_optional_string(::std::string* optional_string);
  ::std::string* unsafe_arena_release_optional_string();
  void unsafe_arena_set_allocated_optional_string(
      ::std::string* optional_string);

  // optional bytes optional_bytes = 15;
  void clear_optional_bytes();
  static const int kOptionalBytesFieldNumber = 15;
  const ::std::string& optional_bytes() const;
  void set_optional_bytes(const ::std::string& value);
  void set_optional_bytes(const char* value);
  void set_optional_bytes(const void* value, size_t size);
  ::std::string* mutable_optional_bytes();
  ::std::string* release_optional_bytes();
  void set_allocated_optional_bytes(::std::string* optional_bytes);
  ::std::string* unsafe_arena_release_optional_bytes();
  void unsafe_arena_set_allocated_optional_bytes(
      ::std::string* optional_bytes);

  // optional .proto3_arena_unittest.TestAllTypes.NestedMessage optional_nested_message = 18;
  bool has_optional_nested_message() const;
  void clear_optional_nested_message();
  static const int kOptionalNestedMessageFieldNumber = 18;
  private:
  void _slow_mutable_optional_nested_message();
  void _slow_set_allocated_optional_nested_message(
      ::google::protobuf::Arena* message_arena, ::proto3_arena_unittest::TestAllTypes_NestedMessage** optional_nested_message);
  ::proto3_arena_unittest::TestAllTypes_NestedMessage* _slow_release_optional_nested_message();
  public:
  const ::proto3_arena_unittest::TestAllTypes_NestedMessage& optional_nested_message() const;
  ::proto3_arena_unittest::TestAllTypes_NestedMessage* mutable_optional_nested_message();
  ::proto3_arena_unittest::TestAllTypes_NestedMessage* release_optional_nested_message();
  void set_allocated_optional_nested_message(::proto3_arena_unittest::TestAllTypes_NestedMessage* optional_nested_message);
  ::proto3_arena_unittest::TestAllTypes_NestedMessage* unsafe_arena_release_optional_nested_message();
  void unsafe_arena_set_allocated_optional_nested_message(
      ::proto3_arena_unittest::TestAllTypes_NestedMessage* optional_nested_message);

  // optional .proto3_arena_unittest.ForeignMessage optional_foreign_message = 19;
  bool has_optional_foreign_message() const;
  void clear_optional_foreign_message();
  static const int kOptionalForeignMessageFieldNumber = 19;
  private:
  void _slow_mutable_optional_foreign_message();
  void _slow_set_allocated_optional_foreign_message(
      ::google::protobuf::Arena* message_arena, ::proto3_arena_unittest::ForeignMessage** optional_foreign_message);
  ::proto3_arena_unittest::ForeignMessage* _slow_release_optional_foreign_message();
  public:
  const ::proto3_arena_unittest::ForeignMessage& optional_foreign_message() const;
  ::proto3_arena_unittest::ForeignMessage* mutable_optional_foreign_message();
  ::proto3_arena_unittest::ForeignMessage* release_optional_foreign_message();
  void set_allocated_optional_foreign_message(::proto3_arena_unittest::ForeignMessage* optional_foreign_message);
  ::proto3_arena_unittest::ForeignMessage* unsafe_arena_release_optional_foreign_message();
  void unsafe_arena_set_allocated_optional_foreign_message(
      ::proto3_arena_unittest::ForeignMessage* optional_foreign_message);

  // optional .protobuf_unittest_import.ImportMessage optional_import_message = 20;
  bool has_optional_import_message() const;
  void clear_optional_import_message();
  static const int kOptionalImportMessageFieldNumber = 20;
  private:
  void _slow_mutable_optional_import_message();
  void _slow_set_allocated_optional_import_message(
      ::google::protobuf::Arena* message_arena, ::protobuf_unittest_import::ImportMessage** optional_import_message);
  ::protobuf_unittest_import::ImportMessage* _slow_release_optional_import_message();
  public:
  const ::protobuf_unittest_import::ImportMessage& optional_import_message() const;
  ::protobuf_unittest_import::ImportMessage* mutable_optional_import_message();
  ::protobuf_unittest_import::ImportMessage* release_optional_import_message();
  void set_allocated_optional_import_message(::protobuf_unittest_import::ImportMessage* optional_import_message);
  ::protobuf_unittest_import::ImportMessage* unsafe_arena_release_optional_import_message();
  void unsafe_arena_set_allocated_optional_import_message(
      ::protobuf_unittest_import::ImportMessage* optional_import_message);

  // optional .proto3_arena_unittest.TestAllTypes.NestedEnum optional_nested_enum = 21;
  void clear_optional_nested_enum();
  static const int kOptionalNestedEnumFieldNumber = 21;
  ::proto3_arena_unittest::TestAllTypes_NestedEnum optional_nested_enum() const;
  void set_optional_nested_enum(::proto3_arena_unittest::TestAllTypes_NestedEnum value);

  // optional .proto3_arena_unittest.ForeignEnum optional_foreign_enum = 22;
  void clear_optional_foreign_enum();
  static const int kOptionalForeignEnumFieldNumber = 22;
  ::proto3_arena_unittest::ForeignEnum optional_foreign_enum() const;
  void set_optional_foreign_enum(::proto3_arena_unittest::ForeignEnum value);

  // optional string optional_string_piece = 24 [ctype = STRING_PIECE];
  void clear_optional_string_piece();
  static const int kOptionalStringPieceFieldNumber = 24;
 private:
  // Hidden due to unknown ctype option.
  const ::std::string& optional_string_piece() const;
  void set_optional_string_piece(const ::std::string& value);
  void set_optional_string_piece(const char* value);
  void set_optional_string_piece(const char* value, size_t size);
  ::std::string* mutable_optional_string_piece();
  ::std::string* release_optional_string_piece();
  void set_allocated_optional_string_piece(::std::string* optional_string_piece);
  ::std::string* unsafe_arena_release_optional_string_piece();
  void unsafe_arena_set_allocated_optional_string_piece(
      ::std::string* optional_string_piece);
 public:

  // optional string optional_cord = 25 [ctype = CORD];
  void clear_optional_cord();
  static const int kOptionalCordFieldNumber = 25;
 private:
  // Hidden due to unknown ctype option.
  const ::std::string& optional_cord() const;
  void set_optional_cord(const ::std::string& value);
  void set_optional_cord(const char* value);
  void set_optional_cord(const char* value, size_t size);
  ::std::string* mutable_optional_cord();
  ::std::string* release_optional_cord();
  void set_allocated_optional_cord(::std::string* optional_cord);
  ::std::string* unsafe_arena_release_optional_cord();
  void unsafe_arena_set_allocated_optional_cord(
      ::std::string* optional_cord);
 public:

  // optional .protobuf_unittest_import.PublicImportMessage optional_public_import_message = 26;
  bool has_optional_public_import_message() const;
  void clear_optional_public_import_message();
  static const int kOptionalPublicImportMessageFieldNumber = 26;
  private:
  void _slow_mutable_optional_public_import_message();
  ::protobuf_unittest_import::PublicImportMessage* _slow_release_optional_public_import_message();
  public:
  const ::protobuf_unittest_import::PublicImportMessage& optional_public_import_message() const;
  ::protobuf_unittest_import::PublicImportMessage* mutable_optional_public_import_message();
  ::protobuf_unittest_import::PublicImportMessage* release_optional_public_import_message();
  void set_allocated_optional_public_import_message(::protobuf_unittest_import::PublicImportMessage* optional_public_import_message);
  ::protobuf_unittest_import::PublicImportMessage* unsafe_arena_release_optional_public_import_message();
  void unsafe_arena_set_allocated_optional_public_import_message(
      ::protobuf_unittest_import::PublicImportMessage* optional_public_import_message);

  // optional .proto3_arena_unittest.TestAllTypes.NestedMessage optional_lazy_message = 27 [lazy = true];
  bool has_optional_lazy_message() const;
  void clear_optional_lazy_message();
  static const int kOptionalLazyMessageFieldNumber = 27;
  private:
  void _slow_mutable_optional_lazy_message();
  void _slow_set_allocated_optional_lazy_message(
      ::google::protobuf::Arena* message_arena, ::proto3_arena_unittest::TestAllTypes_NestedMessage** optional_lazy_message);
  ::proto3_arena_unittest::TestAllTypes_NestedMessage* _slow_release_optional_lazy_message();
  public:
  const ::proto3_arena_unittest::TestAllTypes_NestedMessage& optional_lazy_message() const;
  ::proto3_arena_unittest::TestAllTypes_NestedMessage* mutable_optional_lazy_message();
  ::proto3_arena_unittest::TestAllTypes_NestedMessage* release_optional_lazy_message();
  void set_allocated_optional_lazy_message(::proto3_arena_unittest::TestAllTypes_NestedMessage* optional_lazy_message);
  ::proto3_arena_unittest::TestAllTypes_NestedMessage* unsafe_arena_release_optional_lazy_message();
  void unsafe_arena_set_allocated_optional_lazy_message(
      ::proto3_arena_unittest::TestAllTypes_NestedMessage* optional_lazy_message);

  // repeated int32 repeated_int32 = 31;
  int repeated_int32_size() const;
  void clear_repeated_int32();
  static const int kRepeatedInt32FieldNumber = 31;
  ::google::protobuf::int32 repeated_int32(int index) const;
  void set_repeated_int32(int index, ::google::protobuf::int32 value);
  void add_repeated_int32(::google::protobuf::int32 value);
  const ::google::protobuf::RepeatedField< ::google::protobuf::int32 >&
      repeated_int32() const;
  ::google::protobuf::RepeatedField< ::google::protobuf::int32 >*
      mutable_repeated_int32();

  // repeated int64 repeated_int64 = 32;
  int repeated_int64_size() const;
  void clear_repeated_int64();
  static const int kRepeatedInt64FieldNumber = 32;
  ::google::protobuf::int64 repeated_int64(int index) const;
  void set_repeated_int64(int index, ::google::protobuf::int64 value);
  void add_repeated_int64(::google::protobuf::int64 value);
  const ::google::protobuf::RepeatedField< ::google::protobuf::int64 >&
      repeated_int64() const;
  ::google::protobuf::RepeatedField< ::google::protobuf::int64 >*
      mutable_repeated_int64();

  // repeated uint32 repeated_uint32 = 33;
  int repeated_uint32_size() const;
  void clear_repeated_uint32();
  static const int kRepeatedUint32FieldNumber = 33;
  ::google::protobuf::uint32 repeated_uint32(int index) const;
  void set_repeated_uint32(int index, ::google::protobuf::uint32 value);
  void add_repeated_uint32(::google::protobuf::uint32 value);
  const ::google::protobuf::RepeatedField< ::google::protobuf::uint32 >&
      repeated_uint32() const;
  ::google::protobuf::RepeatedField< ::google::protobuf::uint32 >*
      mutable_repeated_uint32();

  // repeated uint64 repeated_uint64 = 34;
  int repeated_uint64_size() const;
  void clear_repeated_uint64();
  static const int kRepeatedUint64FieldNumber = 34;
  ::google::protobuf::uint64 repeated_uint64(int index) const;
  void set_repeated_uint64(int index, ::google::protobuf::uint64 value);
  void add_repeated_uint64(::google::protobuf::uint64 value);
  const ::google::protobuf::RepeatedField< ::google::protobuf::uint64 >&
      repeated_uint64() const;
  ::google::protobuf::RepeatedField< ::google::protobuf::uint64 >*
      mutable_repeated_uint64();

  // repeated sint32 repeated_sint32 = 35;
  int repeated_sint32_size() const;
  void clear_repeated_sint32();
  static const int kRepeatedSint32FieldNumber = 35;
  ::google::protobuf::int32 repeated_sint32(int index) const;
  void set_repeated_sint32(int index, ::google::protobuf::int32 value);
  void add_repeated_sint32(::google::protobuf::int32 value);
  const ::google::protobuf::RepeatedField< ::google::protobuf::int32 >&
      repeated_sint32() const;
  ::google::protobuf::RepeatedField< ::google::protobuf::int32 >*
      mutable_repeated_sint32();

  // repeated sint64 repeated_sint64 = 36;
  int repeated_sint64_size() const;
  void clear_repeated_sint64();
  static const int kRepeatedSint64FieldNumber = 36;
  ::google::protobuf::int64 repeated_sint64(int index) const;
  void set_repeated_sint64(int index, ::google::protobuf::int64 value);
  void add_repeated_sint64(::google::protobuf::int64 value);
  const ::google::protobuf::RepeatedField< ::google::protobuf::int64 >&
      repeated_sint64() const;
  ::google::protobuf::RepeatedField< ::google::protobuf::int64 >*
      mutable_repeated_sint64();

  // repeated fixed32 repeated_fixed32 = 37;
  int repeated_fixed32_size() const;
  void clear_repeated_fixed32();
  static const int kRepeatedFixed32FieldNumber = 37;
  ::google::protobuf::uint32 repeated_fixed32(int index) const;
  void set_repeated_fixed32(int index, ::google::protobuf::uint32 value);
  void add_repeated_fixed32(::google::protobuf::uint32 value);
  const ::google::protobuf::RepeatedField< ::google::protobuf::uint32 >&
      repeated_fixed32() const;
  ::google::protobuf::RepeatedField< ::google::protobuf::uint32 >*
      mutable_repeated_fixed32();

  // repeated fixed64 repeated_fixed64 = 38;
  int repeated_fixed64_size() const;
  void clear_repeated_fixed64();
  static const int kRepeatedFixed64FieldNumber = 38;
  ::google::protobuf::uint64 repeated_fixed64(int index) const;
  void set_repeated_fixed64(int index, ::google::protobuf::uint64 value);
  void add_repeated_fixed64(::google::protobuf::uint64 value);
  const ::google::protobuf::RepeatedField< ::google::protobuf::uint64 >&
      repeated_fixed64() const;
  ::google::protobuf::RepeatedField< ::google::protobuf::uint64 >*
      mutable_repeated_fixed64();

  // repeated sfixed32 repeated_sfixed32 = 39;
  int repeated_sfixed32_size() const;
  void clear_repeated_sfixed32();
  static const int kRepeatedSfixed32FieldNumber = 39;
  ::google::protobuf::int32 repeated_sfixed32(int index) const;
  void set_repeated_sfixed32(int index, ::google::protobuf::int32 value);
  void add_repeated_sfixed32(::google::protobuf::int32 value);
  const ::google::protobuf::RepeatedField< ::google::protobuf::int32 >&
      repeated_sfixed32() const;
  ::google::protobuf::RepeatedField< ::google::protobuf::int32 >*
      mutable_repeated_sfixed32();

  // repeated sfixed64 repeated_sfixed64 = 40;
  int repeated_sfixed64_size() const;
  void clear_repeated_sfixed64();
  static const int kRepeatedSfixed64FieldNumber = 40;
  ::google::protobuf::int64 repeated_sfixed64(int index) const;
  void set_repeated_sfixed64(int index, ::google::protobuf::int64 value);
  void add_repeated_sfixed64(::google::protobuf::int64 value);
  const ::google::protobuf::RepeatedField< ::google::protobuf::int64 >&
      repeated_sfixed64() const;
  ::google::protobuf::RepeatedField< ::google::protobuf::int64 >*
      mutable_repeated_sfixed64();

  // repeated float repeated_float = 41;
  int repeated_float_size() const;
  void clear_repeated_float();
  static const int kRepeatedFloatFieldNumber = 41;
  float repeated_float(int index) const;
  void set_repeated_float(int index, float value);
  void add_repeated_float(float value);
  const ::google::protobuf::RepeatedField< float >&
      repeated_float() const;
  ::google::protobuf::RepeatedField< float >*
      mutable_repeated_float();

  // repeated double repeated_double = 42;
  int repeated_double_size() const;
  void clear_repeated_double();
  static const int kRepeatedDoubleFieldNumber = 42;
  double repeated_double(int index) const;
  void set_repeated_double(int index, double value);
  void add_repeated_double(double value);
  const ::google::protobuf::RepeatedField< double >&
      repeated_double() const;
  ::google::protobuf::RepeatedField< double >*
      mutable_repeated_double();

  // repeated bool repeated_bool = 43;
  int repeated_bool_size() const;
  void clear_repeated_bool();
  static const int kRepeatedBoolFieldNumber = 43;
  bool repeated_bool(int index) const;
  void set_repeated_bool(int index, bool value);
  void add_repeated_bool(bool value);
  const ::google::protobuf::RepeatedField< bool >&
      repeated_bool() const;
  ::google::protobuf::RepeatedField< bool >*
      mutable_repeated_bool();

  // repeated string repeated_string = 44;
  int repeated_string_size() const;
  void clear_repeated_string();
  static const int kRepeatedStringFieldNumber = 44;
  const ::std::string& repeated_string(int index) const;
  ::std::string* mutable_repeated_string(int index);
  void set_repeated_string(int index, const ::std::string& value);
  void set_repeated_string(int index, const char* value);
  void set_repeated_string(int index, const char* value, size_t size);
  ::std::string* add_repeated_string();
  void add_repeated_string(const ::std::string& value);
  void add_repeated_string(const char* value);
  void add_repeated_string(const char* value, size_t size);
  const ::google::protobuf::RepeatedPtrField< ::std::string>& repeated_string() const;
  ::google::protobuf::RepeatedPtrField< ::std::string>* mutable_repeated_string();

  // repeated bytes repeated_bytes = 45;
  int repeated_bytes_size() const;
  void clear_repeated_bytes();
  static const int kRepeatedBytesFieldNumber = 45;
  const ::std::string& repeated_bytes(int index) const;
  ::std::string* mutable_repeated_bytes(int index);
  void set_repeated_bytes(int index, const ::std::string& value);
  void set_repeated_bytes(int index, const char* value);
  void set_repeated_bytes(int index, const void* value, size_t size);
  ::std::string* add_repeated_bytes();
  void add_repeated_bytes(const ::std::string& value);
  void add_repeated_bytes(const char* value);
  void add_repeated_bytes(const void* value, size_t size);
  const ::google::protobuf::RepeatedPtrField< ::std::string>& repeated_bytes() const;
  ::google::protobuf::RepeatedPtrField< ::std::string>* mutable_repeated_bytes();

  // repeated .proto3_arena_unittest.TestAllTypes.NestedMessage repeated_nested_message = 48;
  int repeated_nested_message_size() const;
  void clear_repeated_nested_message();
  static const int kRepeatedNestedMessageFieldNumber = 48;
  const ::proto3_arena_unittest::TestAllTypes_NestedMessage& repeated_nested_message(int index) const;
  ::proto3_arena_unittest::TestAllTypes_NestedMessage* mutable_repeated_nested_message(int index);
  ::proto3_arena_unittest::TestAllTypes_NestedMessage* add_repeated_nested_message();
  ::google::protobuf::RepeatedPtrField< ::proto3_arena_unittest::TestAllTypes_NestedMessage >*
      mutable_repeated_nested_message();
  const ::google::protobuf::RepeatedPtrField< ::proto3_arena_unittest::TestAllTypes_NestedMessage >&
      repeated_nested_message() const;

  // repeated .proto3_arena_unittest.ForeignMessage repeated_foreign_message = 49;
  int repeated_foreign_message_size() const;
  void clear_repeated_foreign_message();
  static const int kRepeatedForeignMessageFieldNumber = 49;
  const ::proto3_arena_unittest::ForeignMessage& repeated_foreign_message(int index) const;
  ::proto3_arena_unittest::ForeignMessage* mutable_repeated_foreign_message(int index);
  ::proto3_arena_unittest::ForeignMessage* add_repeated_foreign_message();
  ::google::protobuf::RepeatedPtrField< ::proto3_arena_unittest::ForeignMessage >*
      mutable_repeated_foreign_message();
  const ::google::protobuf::RepeatedPtrField< ::proto3_arena_unittest::ForeignMessage >&
      repeated_foreign_message() const;

  // repeated .protobuf_unittest_import.ImportMessage repeated_import_message = 50;
  int repeated_import_message_size() const;
  void clear_repeated_import_message();
  static const int kRepeatedImportMessageFieldNumber = 50;
  const ::protobuf_unittest_import::ImportMessage& repeated_import_message(int index) const;
  ::protobuf_unittest_import::ImportMessage* mutable_repeated_import_message(int index);
  ::protobuf_unittest_import::ImportMessage* add_repeated_import_message();
  ::google::protobuf::RepeatedPtrField< ::protobuf_unittest_import::ImportMessage >*
      mutable_repeated_import_message();
  const ::google::protobuf::RepeatedPtrField< ::protobuf_unittest_import::ImportMessage >&
      repeated_import_message() const;

  // repeated .proto3_arena_unittest.TestAllTypes.NestedEnum repeated_nested_enum = 51;
  int repeated_nested_enum_size() const;
  void clear_repeated_nested_enum();
  static const int kRepeatedNestedEnumFieldNumber = 51;
  ::proto3_arena_unittest::TestAllTypes_NestedEnum repeated_nested_enum(int index) const;
  void set_repeated_nested_enum(int index, ::proto3_arena_unittest::TestAllTypes_NestedEnum value);
  void add_repeated_nested_enum(::proto3_arena_unittest::TestAllTypes_NestedEnum value);
  const ::google::protobuf::RepeatedField<int>& repeated_nested_enum() const;
  ::google::protobuf::RepeatedField<int>* mutable_repeated_nested_enum();

  // repeated .proto3_arena_unittest.ForeignEnum repeated_foreign_enum = 52;
  int repeated_foreign_enum_size() const;
  void clear_repeated_foreign_enum();
  static const int kRepeatedForeignEnumFieldNumber = 52;
  ::proto3_arena_unittest::ForeignEnum repeated_foreign_enum(int index) const;
  void set_repeated_foreign_enum(int index, ::proto3_arena_unittest::ForeignEnum value);
  void add_repeated_foreign_enum(::proto3_arena_unittest::ForeignEnum value);
  const ::google::protobuf::RepeatedField<int>& repeated_foreign_enum() const;
  ::google::protobuf::RepeatedField<int>* mutable_repeated_foreign_enum();

  // repeated string repeated_string_piece = 54 [ctype = STRING_PIECE];
  int repeated_string_piece_size() const;
  void clear_repeated_string_piece();
  static const int kRepeatedStringPieceFieldNumber = 54;
 private:
  // Hidden due to unknown ctype option.
  const ::std::string& repeated_string_piece(int index) const;
  ::std::string* mutable_repeated_string_piece(int index);
  void set_repeated_string_piece(int index, const ::std::string& value);
  void set_repeated_string_piece(int index, const char* value);
  void set_repeated_string_piece(int index, const char* value, size_t size);
  ::std::string* add_repeated_string_piece();
  void add_repeated_string_piece(const ::std::string& value);
  void add_repeated_string_piece(const char* value);
  void add_repeated_string_piece(const char* value, size_t size);
  const ::google::protobuf::RepeatedPtrField< ::std::string>& repeated_string_piece() const;
  ::google::protobuf::RepeatedPtrField< ::std::string>* mutable_repeated_string_piece();
 public:

  // repeated string repeated_cord = 55 [ctype = CORD];
  int repeated_cord_size() const;
  void clear_repeated_cord();
  static const int kRepeatedCordFieldNumber = 55;
 private:
  // Hidden due to unknown ctype option.
  const ::std::string& repeated_cord(int index) const;
  ::std::string* mutable_repeated_cord(int index);
  void set_repeated_cord(int index, const ::std::string& value);
  void set_repeated_cord(int index, const char* value);
  void set_repeated_cord(int index, const char* value, size_t size);
  ::std::string* add_repeated_cord();
  void add_repeated_cord(const ::std::string& value);
  void add_repeated_cord(const char* value);
  void add_repeated_cord(const char* value, size_t size);
  const ::google::protobuf::RepeatedPtrField< ::std::string>& repeated_cord() const;
  ::google::protobuf::RepeatedPtrField< ::std::string>* mutable_repeated_cord();
 public:

  // repeated .proto3_arena_unittest.TestAllTypes.NestedMessage repeated_lazy_message = 57 [lazy = true];
  int repeated_lazy_message_size() const;
  void clear_repeated_lazy_message();
  static const int kRepeatedLazyMessageFieldNumber = 57;
  const ::proto3_arena_unittest::TestAllTypes_NestedMessage& repeated_lazy_message(int index) const;
  ::proto3_arena_unittest::TestAllTypes_NestedMessage* mutable_repeated_lazy_message(int index);
  ::proto3_arena_unittest::TestAllTypes_NestedMessage* add_repeated_lazy_message();
  ::google::protobuf::RepeatedPtrField< ::proto3_arena_unittest::TestAllTypes_NestedMessage >*
      mutable_repeated_lazy_message();
  const ::google::protobuf::RepeatedPtrField< ::proto3_arena_unittest::TestAllTypes_NestedMessage >&
      repeated_lazy_message() const;

  // optional uint32 oneof_uint32 = 111;
  private:
  bool has_oneof_uint32() const;
  public:
  void clear_oneof_uint32();
  static const int kOneofUint32FieldNumber = 111;
  ::google::protobuf::uint32 oneof_uint32() const;
  void set_oneof_uint32(::google::protobuf::uint32 value);

  // optional .proto3_arena_unittest.TestAllTypes.NestedMessage oneof_nested_message = 112;
  bool has_oneof_nested_message() const;
  void clear_oneof_nested_message();
  static const int kOneofNestedMessageFieldNumber = 112;
  private:
  void _slow_mutable_oneof_nested_message();
  void _slow_set_allocated_oneof_nested_message(
      ::google::protobuf::Arena* message_arena, ::proto3_arena_unittest::TestAllTypes_NestedMessage** oneof_nested_message);
  ::proto3_arena_unittest::TestAllTypes_NestedMessage* _slow_release_oneof_nested_message();
  public:
  const ::proto3_arena_unittest::TestAllTypes_NestedMessage& oneof_nested_message() const;
  ::proto3_arena_unittest::TestAllTypes_NestedMessage* mutable_oneof_nested_message();
  ::proto3_arena_unittest::TestAllTypes_NestedMessage* release_oneof_nested_message();
  void set_allocated_oneof_nested_message(::proto3_arena_unittest::TestAllTypes_NestedMessage* oneof_nested_message);
  ::proto3_arena_unittest::TestAllTypes_NestedMessage* unsafe_arena_release_oneof_nested_message();
  void unsafe_arena_set_allocated_oneof_nested_message(
      ::proto3_arena_unittest::TestAllTypes_NestedMessage* oneof_nested_message);

  // optional string oneof_string = 113;
  private:
  bool has_oneof_string() const;
  public:
  void clear_oneof_string();
  static const int kOneofStringFieldNumber = 113;
  const ::std::string& oneof_string() const;
  void set_oneof_string(const ::std::string& value);
  void set_oneof_string(const char* value);
  void set_oneof_string(const char* value, size_t size);
  ::std::string* mutable_oneof_string();
  ::std::string* release_oneof_string();
  void set_allocated_oneof_string(::std::string* oneof_string);
  ::std::string* unsafe_arena_release_oneof_string();
  void unsafe_arena_set_allocated_oneof_string(
      ::std::string* oneof_string);

  // optional bytes oneof_bytes = 114;
  private:
  bool has_oneof_bytes() const;
  public:
  void clear_oneof_bytes();
  static const int kOneofBytesFieldNumber = 114;
  const ::std::string& oneof_bytes() const;
  void set_oneof_bytes(const ::std::string& value);
  void set_oneof_bytes(const char* value);
  void set_oneof_bytes(const void* value, size_t size);
  ::std::string* mutable_oneof_bytes();
  ::std::string* release_oneof_bytes();
  void set_allocated_oneof_bytes(::std::string* oneof_bytes);
  ::std::string* unsafe_arena_release_oneof_bytes();
  void unsafe_arena_set_allocated_oneof_bytes(
      ::std::string* oneof_bytes);

  OneofFieldCase oneof_field_case() const;
  // @@protoc_insertion_point(class_scope:proto3_arena_unittest.TestAllTypes)
 private:
  inline void set_has_oneof_uint32();
  inline void set_has_oneof_nested_message();
  inline void set_has_oneof_string();
  inline void set_has_oneof_bytes();

  inline bool has_oneof_field() const;
  void clear_oneof_field();
  inline void clear_has_oneof_field();

  ::google::protobuf::internal::InternalMetadataWithArena _internal_metadata_;
  friend class ::google::protobuf::Arena;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  ::google::protobuf::RepeatedField< ::google::protobuf::int32 > repeated_int32_;
  mutable int _repeated_int32_cached_byte_size_;
  ::google::protobuf::RepeatedField< ::google::protobuf::int64 > repeated_int64_;
  mutable int _repeated_int64_cached_byte_size_;
  ::google::protobuf::RepeatedField< ::google::protobuf::uint32 > repeated_uint32_;
  mutable int _repeated_uint32_cached_byte_size_;
  ::google::protobuf::RepeatedField< ::google::protobuf::uint64 > repeated_uint64_;
  mutable int _repeated_uint64_cached_byte_size_;
  ::google::protobuf::RepeatedField< ::google::protobuf::int32 > repeated_sint32_;
  mutable int _repeated_sint32_cached_byte_size_;
  ::google::protobuf::RepeatedField< ::google::protobuf::int64 > repeated_sint64_;
  mutable int _repeated_sint64_cached_byte_size_;
  ::google::protobuf::RepeatedField< ::google::protobuf::uint32 > repeated_fixed32_;
  mutable int _repeated_fixed32_cached_byte_size_;
  ::google::protobuf::RepeatedField< ::google::protobuf::uint64 > repeated_fixed64_;
  mutable int _repeated_fixed64_cached_byte_size_;
  ::google::protobuf::RepeatedField< ::google::protobuf::int32 > repeated_sfixed32_;
  mutable int _repeated_sfixed32_cached_byte_size_;
  ::google::protobuf::RepeatedField< ::google::protobuf::int64 > repeated_sfixed64_;
  mutable int _repeated_sfixed64_cached_byte_size_;
  ::google::protobuf::RepeatedField< float > repeated_float_;
  mutable int _repeated_float_cached_byte_size_;
  ::google::protobuf::RepeatedField< double > repeated_double_;
  mutable int _repeated_double_cached_byte_size_;
  ::google::protobuf::RepeatedField< bool > repeated_bool_;
  mutable int _repeated_bool_cached_byte_size_;
  ::google::protobuf::RepeatedPtrField< ::std::string> repeated_string_;
  ::google::protobuf::RepeatedPtrField< ::std::string> repeated_bytes_;
  ::google::protobuf::RepeatedPtrField< ::proto3_arena_unittest::TestAllTypes_NestedMessage > repeated_nested_message_;
  ::google::protobuf::RepeatedPtrField< ::proto3_arena_unittest::ForeignMessage > repeated_foreign_message_;
  ::google::protobuf::RepeatedPtrField< ::protobuf_unittest_import::ImportMessage > repeated_import_message_;
  ::google::protobuf::RepeatedField<int> repeated_nested_enum_;
  mutable int _repeated_nested_enum_cached_byte_size_;
  ::google::protobuf::RepeatedField<int> repeated_foreign_enum_;
  mutable int _repeated_foreign_enum_cached_byte_size_;
  ::google::protobuf::RepeatedPtrField< ::std::string> repeated_string_piece_;
  ::google::protobuf::RepeatedPtrField< ::std::string> repeated_cord_;
  ::google::protobuf::RepeatedPtrField< ::proto3_arena_unittest::TestAllTypes_NestedMessage > repeated_lazy_message_;
  ::google::protobuf::internal::ArenaStringPtr optional_string_;
  ::google::protobuf::internal::ArenaStringPtr optional_bytes_;
  ::google::protobuf::internal::ArenaStringPtr optional_string_piece_;
  ::google::protobuf::internal::ArenaStringPtr optional_cord_;
  ::proto3_arena_unittest::TestAllTypes_NestedMessage* optional_nested_message_;
  ::proto3_arena_unittest::ForeignMessage* optional_foreign_message_;
  ::protobuf_unittest_import::ImportMessage* optional_import_message_;
  ::protobuf_unittest_import::PublicImportMessage* optional_public_import_message_;
  ::proto3_arena_unittest::TestAllTypes_NestedMessage* optional_lazy_message_;
  ::google::protobuf::int64 optional_int64_;
  ::google::protobuf::int32 optional_int32_;
  ::google::protobuf::uint32 optional_uint32_;
  ::google::protobuf::uint64 optional_uint64_;
  ::google::protobuf::int64 optional_sint64_;
  ::google::protobuf::int32 optional_sint32_;
  ::google::protobuf::uint32 optional_fixed32_;
  ::google::protobuf::uint64 optional_fixed64_;
  ::google::protobuf::int64 optional_sfixed64_;
  ::google::protobuf::int32 optional_sfixed32_;
  float optional_float_;
  double optional_double_;
  bool optional_bool_;
  int optional_nested_enum_;
  int optional_foreign_enum_;
  union OneofFieldUnion {
    OneofFieldUnion() {}
    ::google::protobuf::uint32 oneof_uint32_;
    ::proto3_arena_unittest::TestAllTypes_NestedMessage* oneof_nested_message_;
    ::google::protobuf::internal::ArenaStringPtr oneof_string_;
    ::google::protobuf::internal::ArenaStringPtr oneof_bytes_;
  } oneof_field_;
  mutable int _cached_size_;
  ::google::protobuf::uint32 _oneof_case_[1];

  friend void  protobuf_InitDefaults_google_2fprotobuf_2funittest_5fproto3_5farena_2eproto_impl();
  friend void  protobuf_AddDesc_google_2fprotobuf_2funittest_5fproto3_5farena_2eproto_impl();
  friend void protobuf_AssignDesc_google_2fprotobuf_2funittest_5fproto3_5farena_2eproto();
  friend void protobuf_ShutdownFile_google_2fprotobuf_2funittest_5fproto3_5farena_2eproto();

  void InitAsDefaultInstance();
};
extern ::google::protobuf::internal::ExplicitlyConstructed<TestAllTypes> TestAllTypes_default_instance_;

// -------------------------------------------------------------------

class TestPackedTypes : public ::google::protobuf::Message /* @@protoc_insertion_point(class_definition:proto3_arena_unittest.TestPackedTypes) */ {
 public:
  TestPackedTypes();
  virtual ~TestPackedTypes();

  TestPackedTypes(const TestPackedTypes& from);

  inline TestPackedTypes& operator=(const TestPackedTypes& from) {
    CopyFrom(from);
    return *this;
  }

  inline ::google::protobuf::Arena* GetArena() const { return GetArenaNoVirtual(); }
  inline void* GetMaybeArenaPointer() const {
    return MaybeArenaPtr();
  }
  static const ::google::protobuf::Descriptor* descriptor();
  static const TestPackedTypes& default_instance();

  static const TestPackedTypes* internal_default_instance();

  void UnsafeArenaSwap(TestPackedTypes* other);
  void Swap(TestPackedTypes* other);

  // implements Message ----------------------------------------------

  inline TestPackedTypes* New() const { return New(NULL); }

  TestPackedTypes* New(::google::protobuf::Arena* arena) const;
  void CopyFrom(const ::google::protobuf::Message& from);
  void MergeFrom(const ::google::protobuf::Message& from);
  void CopyFrom(const TestPackedTypes& from);
  void MergeFrom(const TestPackedTypes& from);
  void Clear();
  bool IsInitialized() const;

  size_t ByteSizeLong() const;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input);
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const;
  ::google::protobuf::uint8* InternalSerializeWithCachedSizesToArray(
      bool deterministic, ::google::protobuf::uint8* output) const;
  ::google::protobuf::uint8* SerializeWithCachedSizesToArray(::google::protobuf::uint8* output) const {
    return InternalSerializeWithCachedSizesToArray(false, output);
  }
  int GetCachedSize() const { return _cached_size_; }
  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const;
  void InternalSwap(TestPackedTypes* other);
  void UnsafeMergeFrom(const TestPackedTypes& from);
  protected:
  explicit TestPackedTypes(::google::protobuf::Arena* arena);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::google::protobuf::Arena* arena);
  private:
  inline ::google::protobuf::Arena* GetArenaNoVirtual() const {
    return _internal_metadata_.arena();
  }
  inline void* MaybeArenaPtr() const {
    return _internal_metadata_.raw_arena_ptr();
  }
  public:

  ::google::protobuf::Metadata GetMetadata() const;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // repeated int32 packed_int32 = 90 [packed = true];
  int packed_int32_size() const;
  void clear_packed_int32();
  static const int kPackedInt32FieldNumber = 90;
  ::google::protobuf::int32 packed_int32(int index) const;
  void set_packed_int32(int index, ::google::protobuf::int32 value);
  void add_packed_int32(::google::protobuf::int32 value);
  const ::google::protobuf::RepeatedField< ::google::protobuf::int32 >&
      packed_int32() const;
  ::google::protobuf::RepeatedField< ::google::protobuf::int32 >*
      mutable_packed_int32();

  // repeated int64 packed_int64 = 91 [packed = true];
  int packed_int64_size() const;
  void clear_packed_int64();
  static const int kPackedInt64FieldNumber = 91;
  ::google::protobuf::int64 packed_int64(int index) const;
  void set_packed_int64(int index, ::google::protobuf::int64 value);
  void add_packed_int64(::google::protobuf::int64 value);
  const ::google::protobuf::RepeatedField< ::google::protobuf::int64 >&
      packed_int64() const;
  ::google::protobuf::RepeatedField< ::google::protobuf::int64 >*
      mutable_packed_int64();

  // repeated uint32 packed_uint32 = 92 [packed = true];
  int packed_uint32_size() const;
  void clear_packed_uint32();
  static const int kPackedUint32FieldNumber = 92;
  ::google::protobuf::uint32 packed_uint32(int index) const;
  void set_packed_uint32(int index, ::google::protobuf::uint32 value);
  void add_packed_uint32(::google::protobuf::uint32 value);
  const ::google::protobuf::RepeatedField< ::google::protobuf::uint32 >&
      packed_uint32() const;
  ::google::protobuf::RepeatedField< ::google::protobuf::uint32 >*
      mutable_packed_uint32();

  // repeated uint64 packed_uint64 = 93 [packed = true];
  int packed_uint64_size() const;
  void clear_packed_uint64();
  static const int kPackedUint64FieldNumber = 93;
  ::google::protobuf::uint64 packed_uint64(int index) const;
  void set_packed_uint64(int index, ::google::protobuf::uint64 value);
  void add_packed_uint64(::google::protobuf::uint64 value);
  const ::google::protobuf::RepeatedField< ::google::protobuf::uint64 >&
      packed_uint64() const;
  ::google::protobuf::RepeatedField< ::google::protobuf::uint64 >*
      mutable_packed_uint64();

  // repeated sint32 packed_sint32 = 94 [packed = true];
  int packed_sint32_size() const;
  void clear_packed_sint32();
  static const int kPackedSint32FieldNumber = 94;
  ::google::protobuf::int32 packed_sint32(int index) const;
  void set_packed_sint32(int index, ::google::protobuf::int32 value);
  void add_packed_sint32(::google::protobuf::int32 value);
  const ::google::protobuf::RepeatedField< ::google::protobuf::int32 >&
      packed_sint32() const;
  ::google::protobuf::RepeatedField< ::google::protobuf::int32 >*
      mutable_packed_sint32();

  // repeated sint64 packed_sint64 = 95 [packed = true];
  int packed_sint64_size() const;
  void clear_packed_sint64();
  static const int kPackedSint64FieldNumber = 95;
  ::google::protobuf::int64 packed_sint64(int index) const;
  void set_packed_sint64(int index, ::google::protobuf::int64 value);
  void add_packed_sint64(::google::protobuf::int64 value);
  const ::google::protobuf::RepeatedField< ::google::protobuf::int64 >&
      packed_sint64() const;
  ::google::protobuf::RepeatedField< ::google::protobuf::int64 >*
      mutable_packed_sint64();

  // repeated fixed32 packed_fixed32 = 96 [packed = true];
  int packed_fixed32_size() const;
  void clear_packed_fixed32();
  static const int kPackedFixed32FieldNumber = 96;
  ::google::protobuf::uint32 packed_fixed32(int index) const;
  void set_packed_fixed32(int index, ::google::protobuf::uint32 value);
  void add_packed_fixed32(::google::protobuf::uint32 value);
  const ::google::protobuf::RepeatedField< ::google::protobuf::uint32 >&
      packed_fixed32() const;
  ::google::protobuf::RepeatedField< ::google::protobuf::uint32 >*
      mutable_packed_fixed32();

  // repeated fixed64 packed_fixed64 = 97 [packed = true];
  int packed_fixed64_size() const;
  void clear_packed_fixed64();
  static const int kPackedFixed64FieldNumber = 97;
  ::google::protobuf::uint64 packed_fixed64(int index) const;
  void set_packed_fixed64(int index, ::google::protobuf::uint64 value);
  void add_packed_fixed64(::google::protobuf::uint64 value);
  const ::google::protobuf::RepeatedField< ::google::protobuf::uint64 >&
      packed_fixed64() const;
  ::google::protobuf::RepeatedField< ::google::protobuf::uint64 >*
      mutable_packed_fixed64();

  // repeated sfixed32 packed_sfixed32 = 98 [packed = true];
  int packed_sfixed32_size() const;
  void clear_packed_sfixed32();
  static const int kPackedSfixed32FieldNumber = 98;
  ::google::protobuf::int32 packed_sfixed32(int index) const;
  void set_packed_sfixed32(int index, ::google::protobuf::int32 value);
  void add_packed_sfixed32(::google::protobuf::int32 value);
  const ::google::protobuf::RepeatedField< ::google::protobuf::int32 >&
      packed_sfixed32() const;
  ::google::protobuf::RepeatedField< ::google::protobuf::int32 >*
      mutable_packed_sfixed32();

  // repeated sfixed64 packed_sfixed64 = 99 [packed = true];
  int packed_sfixed64_size() const;
  void clear_packed_sfixed64();
  static const int kPackedSfixed64FieldNumber = 99;
  ::google::protobuf::int64 packed_sfixed64(int index) const;
  void set_packed_sfixed64(int index, ::google::protobuf::int64 value);
  void add_packed_sfixed64(::google::protobuf::int64 value);
  const ::google::protobuf::RepeatedField< ::google::protobuf::int64 >&
      packed_sfixed64() const;
  ::google::protobuf::RepeatedField< ::google::protobuf::int64 >*
      mutable_packed_sfixed64();

  // repeated float packed_float = 100 [packed = true];
  int packed_float_size() const;
  void clear_packed_float();
  static const int kPackedFloatFieldNumber = 100;
  float packed_float(int index) const;
  void set_packed_float(int index, float value);
  void add_packed_float(float value);
  const ::google::protobuf::RepeatedField< float >&
      packed_float() const;
  ::google::protobuf::RepeatedField< float >*
      mutable_packed_float();

  // repeated double packed_double = 101 [packed = true];
  int packed_double_size() const;
  void clear_packed_double();
  static const int kPackedDoubleFieldNumber = 101;
  double packed_double(int index) const;
  void set_packed_double(int index, double value);
  void add_packed_double(double value);
  const ::google::protobuf::RepeatedField< double >&
      packed_double() const;
  ::google::protobuf::RepeatedField< double >*
      mutable_packed_double();

  // repeated bool packed_bool = 102 [packed = true];
  int packed_bool_size() const;
  void clear_packed_bool();
  static const int kPackedBoolFieldNumber = 102;
  bool packed_bool(int index) const;
  void set_packed_bool(int index, bool value);
  void add_packed_bool(bool value);
  const ::google::protobuf::RepeatedField< bool >&
      packed_bool() const;
  ::google::protobuf::RepeatedField< bool >*
      mutable_packed_bool();

  // repeated .proto3_arena_unittest.ForeignEnum packed_enum = 103 [packed = true];
  int packed_enum_size() const;
  void clear_packed_enum();
  static const int kPackedEnumFieldNumber = 103;
  ::proto3_arena_unittest::ForeignEnum packed_enum(int index) const;
  void set_packed_enum(int index, ::proto3_arena_unittest::ForeignEnum value);
  void add_packed_enum(::proto3_arena_unittest::ForeignEnum value);
  const ::google::protobuf::RepeatedField<int>& packed_enum() const;
  ::google::protobuf::RepeatedField<int>* mutable_packed_enum();

  // @@protoc_insertion_point(class_scope:proto3_arena_unittest.TestPackedTypes)
 private:

  ::google::protobuf::internal::InternalMetadataWithArena _internal_metadata_;
  friend class ::google::protobuf::Arena;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  ::google::protobuf::RepeatedField< ::google::protobuf::int32 > packed_int32_;
  mutable int _packed_int32_cached_byte_size_;
  ::google::protobuf::RepeatedField< ::google::protobuf::int64 > packed_int64_;
  mutable int _packed_int64_cached_byte_size_;
  ::google::protobuf::RepeatedField< ::google::protobuf::uint32 > packed_uint32_;
  mutable int _packed_uint32_cached_byte_size_;
  ::google::protobuf::RepeatedField< ::google::protobuf::uint64 > packed_uint64_;
  mutable int _packed_uint64_cached_byte_size_;
  ::google::protobuf::RepeatedField< ::google::protobuf::int32 > packed_sint32_;
  mutable int _packed_sint32_cached_byte_size_;
  ::google::protobuf::RepeatedField< ::google::protobuf::int64 > packed_sint64_;
  mutable int _packed_sint64_cached_byte_size_;
  ::google::protobuf::RepeatedField< ::google::protobuf::uint32 > packed_fixed32_;
  mutable int _packed_fixed32_cached_byte_size_;
  ::google::protobuf::RepeatedField< ::google::protobuf::uint64 > packed_fixed64_;
  mutable int _packed_fixed64_cached_byte_size_;
  ::google::protobuf::RepeatedField< ::google::protobuf::int32 > packed_sfixed32_;
  mutable int _packed_sfixed32_cached_byte_size_;
  ::google::protobuf::RepeatedField< ::google::protobuf::int64 > packed_sfixed64_;
  mutable int _packed_sfixed64_cached_byte_size_;
  ::google::protobuf::RepeatedField< float > packed_float_;
  mutable int _packed_float_cached_byte_size_;
  ::google::protobuf::RepeatedField< double > packed_double_;
  mutable int _packed_double_cached_byte_size_;
  ::google::protobuf::RepeatedField< bool > packed_bool_;
  mutable int _packed_bool_cached_byte_size_;
  ::google::protobuf::RepeatedField<int> packed_enum_;
  mutable int _packed_enum_cached_byte_size_;
  mutable int _cached_size_;
  friend void  protobuf_InitDefaults_google_2fprotobuf_2funittest_5fproto3_5farena_2eproto_impl();
  friend void  protobuf_AddDesc_google_2fprotobuf_2funittest_5fproto3_5farena_2eproto_impl();
  friend void protobuf_AssignDesc_google_2fprotobuf_2funittest_5fproto3_5farena_2eproto();
  friend void protobuf_ShutdownFile_google_2fprotobuf_2funittest_5fproto3_5farena_2eproto();

  void InitAsDefaultInstance();
};
extern ::google::protobuf::internal::ExplicitlyConstructed<TestPackedTypes> TestPackedTypes_default_instance_;

// -------------------------------------------------------------------

class TestUnpackedTypes : public ::google::protobuf::Message /* @@protoc_insertion_point(class_definition:proto3_arena_unittest.TestUnpackedTypes) */ {
 public:
  TestUnpackedTypes();
  virtual ~TestUnpackedTypes();

  TestUnpackedTypes(const TestUnpackedTypes& from);

  inline TestUnpackedTypes& operator=(const TestUnpackedTypes& from) {
    CopyFrom(from);
    return *this;
  }

  inline ::google::protobuf::Arena* GetArena() const { return GetArenaNoVirtual(); }
  inline void* GetMaybeArenaPointer() const {
    return MaybeArenaPtr();
  }
  static const ::google::protobuf::Descriptor* descriptor();
  static const TestUnpackedTypes& default_instance();

  static const TestUnpackedTypes* internal_default_instance();

  void UnsafeArenaSwap(TestUnpackedTypes* other);
  void Swap(TestUnpackedTypes* other);

  // implements Message ----------------------------------------------

  inline TestUnpackedTypes* New() const { return New(NULL); }

  TestUnpackedTypes* New(::google::protobuf::Arena* arena) const;
  void CopyFrom(const ::google::protobuf::Message& from);
  void MergeFrom(const ::google::protobuf::Message& from);
  void CopyFrom(const TestUnpackedTypes& from);
  void MergeFrom(const TestUnpackedTypes& from);
  void Clear();
  bool IsInitialized() const;

  size_t ByteSizeLong() const;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input);
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const;
  ::google::protobuf::uint8* InternalSerializeWithCachedSizesToArray(
      bool deterministic, ::google::protobuf::uint8* output) const;
  ::google::protobuf::uint8* SerializeWithCachedSizesToArray(::google::protobuf::uint8* output) const {
    return InternalSerializeWithCachedSizesToArray(false, output);
  }
  int GetCachedSize() const { return _cached_size_; }
  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const;
  void InternalSwap(TestUnpackedTypes* other);
  void UnsafeMergeFrom(const TestUnpackedTypes& from);
  protected:
  explicit TestUnpackedTypes(::google::protobuf::Arena* arena);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::google::protobuf::Arena* arena);
  private:
  inline ::google::protobuf::Arena* GetArenaNoVirtual() const {
    return _internal_metadata_.arena();
  }
  inline void* MaybeArenaPtr() const {
    return _internal_metadata_.raw_arena_ptr();
  }
  public:

  ::google::protobuf::Metadata GetMetadata() const;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // repeated int32 repeated_int32 = 1 [packed = false];
  int repeated_int32_size() const;
  void clear_repeated_int32();
  static const int kRepeatedInt32FieldNumber = 1;
  ::google::protobuf::int32 repeated_int32(int index) const;
  void set_repeated_int32(int index, ::google::protobuf::int32 value);
  void add_repeated_int32(::google::protobuf::int32 value);
  const ::google::protobuf::RepeatedField< ::google::protobuf::int32 >&
      repeated_int32() const;
  ::google::protobuf::RepeatedField< ::google::protobuf::int32 >*
      mutable_repeated_int32();

  // repeated int64 repeated_int64 = 2 [packed = false];
  int repeated_int64_size() const;
  void clear_repeated_int64();
  static const int kRepeatedInt64FieldNumber = 2;
  ::google::protobuf::int64 repeated_int64(int index) const;
  void set_repeated_int64(int index, ::google::protobuf::int64 value);
  void add_repeated_int64(::google::protobuf::int64 value);
  const ::google::protobuf::RepeatedField< ::google::protobuf::int64 >&
      repeated_int64() const;
  ::google::protobuf::RepeatedField< ::google::protobuf::int64 >*
      mutable_repeated_int64();

  // repeated uint32 repeated_uint32 = 3 [packed = false];
  int repeated_uint32_size() const;
  void clear_repeated_uint32();
  static const int kRepeatedUint32FieldNumber = 3;
  ::google::protobuf::uint32 repeated_uint32(int index) const;
  void set_repeated_uint32(int index, ::google::protobuf::uint32 value);
  void add_repeated_uint32(::google::protobuf::uint32 value);
  const ::google::protobuf::RepeatedField< ::google::protobuf::uint32 >&
      repeated_uint32() const;
  ::google::protobuf::RepeatedField< ::google::protobuf::uint32 >*
      mutable_repeated_uint32();

  // repeated uint64 repeated_uint64 = 4 [packed = false];
  int repeated_uint64_size() const;
  void clear_repeated_uint64();
  static const int kRepeatedUint64FieldNumber = 4;
  ::google::protobuf::uint64 repeated_uint64(int index) const;
  void set_repeated_uint64(int index, ::google::protobuf::uint64 value);
  void add_repeated_uint64(::google::protobuf::uint64 value);
  const ::google::protobuf::RepeatedField< ::google::protobuf::uint64 >&
      repeated_uint64() const;
  ::google::protobuf::RepeatedField< ::google::protobuf::uint64 >*
      mutable_repeated_uint64();

  // repeated sint32 repeated_sint32 = 5 [packed = false];
  int repeated_sint32_size() const;
  void clear_repeated_sint32();
  static const int kRepeatedSint32FieldNumber = 5;
  ::google::protobuf::int32 repeated_sint32(int index) const;
  void set_repeated_sint32(int index, ::google::protobuf::int32 value);
  void add_repeated_sint32(::google::protobuf::int32 value);
  const ::google::protobuf::RepeatedField< ::google::protobuf::int32 >&
      repeated_sint32() const;
  ::google::protobuf::RepeatedField< ::google::protobuf::int32 >*
      mutable_repeated_sint32();

  // repeated sint64 repeated_sint64 = 6 [packed = false];
  int repeated_sint64_size() const;
  void clear_repeated_sint64();
  static const int kRepeatedSint64FieldNumber = 6;
  ::google::protobuf::int64 repeated_sint64(int index) const;
  void set_repeated_sint64(int index, ::google::protobuf::int64 value);
  void add_repeated_sint64(::google::protobuf::int64 value);
  const ::google::protobuf::RepeatedField< ::google::protobuf::int64 >&
      repeated_sint64() const;
  ::google::protobuf::RepeatedField< ::google::protobuf::int64 >*
      mutable_repeated_sint64();

  // repeated fixed32 repeated_fixed32 = 7 [packed = false];
  int repeated_fixed32_size() const;
  void clear_repeated_fixed32();
  static const int kRepeatedFixed32FieldNumber = 7;
  ::google::protobuf::uint32 repeated_fixed32(int index) const;
  void set_repeated_fixed32(int index, ::google::protobuf::uint32 value);
  void add_repeated_fixed32(::google::protobuf::uint32 value);
  const ::google::protobuf::RepeatedField< ::google::protobuf::uint32 >&
      repeated_fixed32() const;
  ::google::protobuf::RepeatedField< ::google::protobuf::uint32 >*
      mutable_repeated_fixed32();

  // repeated fixed64 repeated_fixed64 = 8 [packed = false];
  int repeated_fixed64_size() const;
  void clear_repeated_fixed64();
  static const int kRepeatedFixed64FieldNumber = 8;
  ::google::protobuf::uint64 repeated_fixed64(int index) const;
  void set_repeated_fixed64(int index, ::google::protobuf::uint64 value);
  void add_repeated_fixed64(::google::protobuf::uint64 value);
  const ::google::protobuf::RepeatedField< ::google::protobuf::uint64 >&
      repeated_fixed64() const;
  ::google::protobuf::RepeatedField< ::google::protobuf::uint64 >*
      mutable_repeated_fixed64();

  // repeated sfixed32 repeated_sfixed32 = 9 [packed = false];
  int repeated_sfixed32_size() const;
  void clear_repeated_sfixed32();
  static const int kRepeatedSfixed32FieldNumber = 9;
  ::google::protobuf::int32 repeated_sfixed32(int index) const;
  void set_repeated_sfixed32(int index, ::google::protobuf::int32 value);
  void add_repeated_sfixed32(::google::protobuf::int32 value);
  const ::google::protobuf::RepeatedField< ::google::protobuf::int32 >&
      repeated_sfixed32() const;
  ::google::protobuf::RepeatedField< ::google::protobuf::int32 >*
      mutable_repeated_sfixed32();

  // repeated sfixed64 repeated_sfixed64 = 10 [packed = false];
  int repeated_sfixed64_size() const;
  void clear_repeated_sfixed64();
  static const int kRepeatedSfixed64FieldNumber = 10;
  ::google::protobuf::int64 repeated_sfixed64(int index) const;
  void set_repeated_sfixed64(int index, ::google::protobuf::int64 value);
  void add_repeated_sfixed64(::google::protobuf::int64 value);
  const ::google::protobuf::RepeatedField< ::google::protobuf::int64 >&
      repeated_sfixed64() const;
  ::google::protobuf::RepeatedField< ::google::protobuf::int64 >*
      mutable_repeated_sfixed64();

  // repeated float repeated_float = 11 [packed = false];
  int repeated_float_size() const;
  void clear_repeated_float();
  static const int kRepeatedFloatFieldNumber = 11;
  float repeated_float(int index) const;
  void set_repeated_float(int index, float value);
  void add_repeated_float(float value);
  const ::google::protobuf::RepeatedField< float >&
      repeated_float() const;
  ::google::protobuf::RepeatedField< float >*
      mutable_repeated_float();

  // repeated double repeated_double = 12 [packed = false];
  int repeated_double_size() const;
  void clear_repeated_double();
  static const int kRepeatedDoubleFieldNumber = 12;
  double repeated_double(int index) const;
  void set_repeated_double(int index, double value);
  void add_repeated_double(double value);
  const ::google::protobuf::RepeatedField< double >&
      repeated_double() const;
  ::google::protobuf::RepeatedField< double >*
      mutable_repeated_double();

  // repeated bool repeated_bool = 13 [packed = false];
  int repeated_bool_size() const;
  void clear_repeated_bool();
  static const int kRepeatedBoolFieldNumber = 13;
  bool repeated_bool(int index) const;
  void set_repeated_bool(int index, bool value);
  void add_repeated_bool(bool value);
  const ::google::protobuf::RepeatedField< bool >&
      repeated_bool() const;
  ::google::protobuf::RepeatedField< bool >*
      mutable_repeated_bool();

  // repeated .proto3_arena_unittest.TestAllTypes.NestedEnum repeated_nested_enum = 14 [packed = false];
  int repeated_nested_enum_size() const;
  void clear_repeated_nested_enum();
  static const int kRepeatedNestedEnumFieldNumber = 14;
  ::proto3_arena_unittest::TestAllTypes_NestedEnum repeated_nested_enum(int index) const;
  void set_repeated_nested_enum(int index, ::proto3_arena_unittest::TestAllTypes_NestedEnum value);
  void add_repeated_nested_enum(::proto3_arena_unittest::TestAllTypes_NestedEnum value);
  const ::google::protobuf::RepeatedField<int>& repeated_nested_enum() const;
  ::google::protobuf::RepeatedField<int>* mutable_repeated_nested_enum();

  // @@protoc_insertion_point(class_scope:proto3_arena_unittest.TestUnpackedTypes)
 private:

  ::google::protobuf::internal::InternalMetadataWithArena _internal_metadata_;
  friend class ::google::protobuf::Arena;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  ::google::protobuf::RepeatedField< ::google::protobuf::int32 > repeated_int32_;
  ::google::protobuf::RepeatedField< ::google::protobuf::int64 > repeated_int64_;
  ::google::protobuf::RepeatedField< ::google::protobuf::uint32 > repeated_uint32_;
  ::google::protobuf::RepeatedField< ::google::protobuf::uint64 > repeated_uint64_;
  ::google::protobuf::RepeatedField< ::google::protobuf::int32 > repeated_sint32_;
  ::google::protobuf::RepeatedField< ::google::protobuf::int64 > repeated_sint64_;
  ::google::protobuf::RepeatedField< ::google::protobuf::uint32 > repeated_fixed32_;
  ::google::protobuf::RepeatedField< ::google::protobuf::uint64 > repeated_fixed64_;
  ::google::protobuf::RepeatedField< ::google::protobuf::int32 > repeated_sfixed32_;
  ::google::protobuf::RepeatedField< ::google::protobuf::int64 > repeated_sfixed64_;
  ::google::protobuf::RepeatedField< float > repeated_float_;
  ::google::protobuf::RepeatedField< double > repeated_double_;
  ::google::protobuf::RepeatedField< bool > repeated_bool_;
  ::google::protobuf::RepeatedField<int> repeated_nested_enum_;
  mutable int _cached_size_;
  friend void  protobuf_InitDefaults_google_2fprotobuf_2funittest_5fproto3_5farena_2eproto_impl();
  friend void  protobuf_AddDesc_google_2fprotobuf_2funittest_5fproto3_5farena_2eproto_impl();
  friend void protobuf_AssignDesc_google_2fprotobuf_2funittest_5fproto3_5farena_2eproto();
  friend void protobuf_ShutdownFile_google_2fprotobuf_2funittest_5fproto3_5farena_2eproto();

  void InitAsDefaultInstance();
};
extern ::google::protobuf::internal::ExplicitlyConstructed<TestUnpackedTypes> TestUnpackedTypes_default_instance_;

// -------------------------------------------------------------------

class NestedTestAllTypes : public ::google::protobuf::Message /* @@protoc_insertion_point(class_definition:proto3_arena_unittest.NestedTestAllTypes) */ {
 public:
  NestedTestAllTypes();
  virtual ~NestedTestAllTypes();

  NestedTestAllTypes(const NestedTestAllTypes& from);

  inline NestedTestAllTypes& operator=(const NestedTestAllTypes& from) {
    CopyFrom(from);
    return *this;
  }

  inline ::google::protobuf::Arena* GetArena() const { return GetArenaNoVirtual(); }
  inline void* GetMaybeArenaPointer() const {
    return MaybeArenaPtr();
  }
  static const ::google::protobuf::Descriptor* descriptor();
  static const NestedTestAllTypes& default_instance();

  static const NestedTestAllTypes* internal_default_instance();

  void UnsafeArenaSwap(NestedTestAllTypes* other);
  void Swap(NestedTestAllTypes* other);

  // implements Message ----------------------------------------------

  inline NestedTestAllTypes* New() const { return New(NULL); }

  NestedTestAllTypes* New(::google::protobuf::Arena* arena) const;
  void CopyFrom(const ::google::protobuf::Message& from);
  void MergeFrom(const ::google::protobuf::Message& from);
  void CopyFrom(const NestedTestAllTypes& from);
  void MergeFrom(const NestedTestAllTypes& from);
  void Clear();
  bool IsInitialized() const;

  size_t ByteSizeLong() const;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input);
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const;
  ::google::protobuf::uint8* InternalSerializeWithCachedSizesToArray(
      bool deterministic, ::google::protobuf::uint8* output) const;
  ::google::protobuf::uint8* SerializeWithCachedSizesToArray(::google::protobuf::uint8* output) const {
    return InternalSerializeWithCachedSizesToArray(false, output);
  }
  int GetCachedSize() const { return _cached_size_; }
  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const;
  void InternalSwap(NestedTestAllTypes* other);
  void UnsafeMergeFrom(const NestedTestAllTypes& from);
  protected:
  explicit NestedTestAllTypes(::google::protobuf::Arena* arena);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::google::protobuf::Arena* arena);
  private:
  inline ::google::protobuf::Arena* GetArenaNoVirtual() const {
    return _internal_metadata_.arena();
  }
  inline void* MaybeArenaPtr() const {
    return _internal_metadata_.raw_arena_ptr();
  }
  public:

  ::google::protobuf::Metadata GetMetadata() const;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // optional .proto3_arena_unittest.NestedTestAllTypes child = 1;
  bool has_child() const;
  void clear_child();
  static const int kChildFieldNumber = 1;
  private:
  void _slow_mutable_child();
  void _slow_set_allocated_child(
      ::google::protobuf::Arena* message_arena, ::proto3_arena_unittest::NestedTestAllTypes** child);
  ::proto3_arena_unittest::NestedTestAllTypes* _slow_release_child();
  public:
  const ::proto3_arena_unittest::NestedTestAllTypes& child() const;
  ::proto3_arena_unittest::NestedTestAllTypes* mutable_child();
  ::proto3_arena_unittest::NestedTestAllTypes* release_child();
  void set_allocated_child(::proto3_arena_unittest::NestedTestAllTypes* child);
  ::proto3_arena_unittest::NestedTestAllTypes* unsafe_arena_release_child();
  void unsafe_arena_set_allocated_child(
      ::proto3_arena_unittest::NestedTestAllTypes* child);

  // optional .proto3_arena_unittest.TestAllTypes payload = 2;
  bool has_payload() const;
  void clear_payload();
  static const int kPayloadFieldNumber = 2;
  private:
  void _slow_mutable_payload();
  void _slow_set_allocated_payload(
      ::google::protobuf::Arena* message_arena, ::proto3_arena_unittest::TestAllTypes** payload);
  ::proto3_arena_unittest::TestAllTypes* _slow_release_payload();
  public:
  const ::proto3_arena_unittest::TestAllTypes& payload() const;
  ::proto3_arena_unittest::TestAllTypes* mutable_payload();
  ::proto3_arena_unittest::TestAllTypes* release_payload();
  void set_allocated_payload(::proto3_arena_unittest::TestAllTypes* payload);
  ::proto3_arena_unittest::TestAllTypes* unsafe_arena_release_payload();
  void unsafe_arena_set_allocated_payload(
      ::proto3_arena_unittest::TestAllTypes* payload);

  // @@protoc_insertion_point(class_scope:proto3_arena_unittest.NestedTestAllTypes)
 private:

  ::google::protobuf::internal::InternalMetadataWithArena _internal_metadata_;
  friend class ::google::protobuf::Arena;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  ::proto3_arena_unittest::NestedTestAllTypes* child_;
  ::proto3_arena_unittest::TestAllTypes* payload_;
  mutable int _cached_size_;
  friend void  protobuf_InitDefaults_google_2fprotobuf_2funittest_5fproto3_5farena_2eproto_impl();
  friend void  protobuf_AddDesc_google_2fprotobuf_2funittest_5fproto3_5farena_2eproto_impl();
  friend void protobuf_AssignDesc_google_2fprotobuf_2funittest_5fproto3_5farena_2eproto();
  friend void protobuf_ShutdownFile_google_2fprotobuf_2funittest_5fproto3_5farena_2eproto();

  void InitAsDefaultInstance();
};
extern ::google::protobuf::internal::ExplicitlyConstructed<NestedTestAllTypes> NestedTestAllTypes_default_instance_;

// -------------------------------------------------------------------

class ForeignMessage : public ::google::protobuf::Message /* @@protoc_insertion_point(class_definition:proto3_arena_unittest.ForeignMessage) */ {
 public:
  ForeignMessage();
  virtual ~ForeignMessage();

  ForeignMessage(const ForeignMessage& from);

  inline ForeignMessage& operator=(const ForeignMessage& from) {
    CopyFrom(from);
    return *this;
  }

  inline ::google::protobuf::Arena* GetArena() const { return GetArenaNoVirtual(); }
  inline void* GetMaybeArenaPointer() const {
    return MaybeArenaPtr();
  }
  static const ::google::protobuf::Descriptor* descriptor();
  static const ForeignMessage& default_instance();

  static const ForeignMessage* internal_default_instance();

  void UnsafeArenaSwap(ForeignMessage* other);
  void Swap(ForeignMessage* other);

  // implements Message ----------------------------------------------

  inline ForeignMessage* New() const { return New(NULL); }

  ForeignMessage* New(::google::protobuf::Arena* arena) const;
  void CopyFrom(const ::google::protobuf::Message& from);
  void MergeFrom(const ::google::protobuf::Message& from);
  void CopyFrom(const ForeignMessage& from);
  void MergeFrom(const ForeignMessage& from);
  void Clear();
  bool IsInitialized() const;

  size_t ByteSizeLong() const;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input);
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const;
  ::google::protobuf::uint8* InternalSerializeWithCachedSizesToArray(
      bool deterministic, ::google::protobuf::uint8* output) const;
  ::google::protobuf::uint8* SerializeWithCachedSizesToArray(::google::protobuf::uint8* output) const {
    return InternalSerializeWithCachedSizesToArray(false, output);
  }
  int GetCachedSize() const { return _cached_size_; }
  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const;
  void InternalSwap(ForeignMessage* other);
  void UnsafeMergeFrom(const ForeignMessage& from);
  protected:
  explicit ForeignMessage(::google::protobuf::Arena* arena);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::google::protobuf::Arena* arena);
  private:
  inline ::google::protobuf::Arena* GetArenaNoVirtual() const {
    return _internal_metadata_.arena();
  }
  inline void* MaybeArenaPtr() const {
    return _internal_metadata_.raw_arena_ptr();
  }
  public:

  ::google::protobuf::Metadata GetMetadata() const;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // optional int32 c = 1;
  void clear_c();
  static const int kCFieldNumber = 1;
  ::google::protobuf::int32 c() const;
  void set_c(::google::protobuf::int32 value);

  // @@protoc_insertion_point(class_scope:proto3_arena_unittest.ForeignMessage)
 private:

  ::google::protobuf::internal::InternalMetadataWithArena _internal_metadata_;
  friend class ::google::protobuf::Arena;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  ::google::protobuf::int32 c_;
  mutable int _cached_size_;
  friend void  protobuf_InitDefaults_google_2fprotobuf_2funittest_5fproto3_5farena_2eproto_impl();
  friend void  protobuf_AddDesc_google_2fprotobuf_2funittest_5fproto3_5farena_2eproto_impl();
  friend void protobuf_AssignDesc_google_2fprotobuf_2funittest_5fproto3_5farena_2eproto();
  friend void protobuf_ShutdownFile_google_2fprotobuf_2funittest_5fproto3_5farena_2eproto();

  void InitAsDefaultInstance();
};
extern ::google::protobuf::internal::ExplicitlyConstructed<ForeignMessage> ForeignMessage_default_instance_;

// -------------------------------------------------------------------

class TestEmptyMessage : public ::google::protobuf::Message /* @@protoc_insertion_point(class_definition:proto3_arena_unittest.TestEmptyMessage) */ {
 public:
  TestEmptyMessage();
  virtual ~TestEmptyMessage();

  TestEmptyMessage(const TestEmptyMessage& from);

  inline TestEmptyMessage& operator=(const TestEmptyMessage& from) {
    CopyFrom(from);
    return *this;
  }

  inline ::google::protobuf::Arena* GetArena() const { return GetArenaNoVirtual(); }
  inline void* GetMaybeArenaPointer() const {
    return MaybeArenaPtr();
  }
  static const ::google::protobuf::Descriptor* descriptor();
  static const TestEmptyMessage& default_instance();

  static const TestEmptyMessage* internal_default_instance();

  void UnsafeArenaSwap(TestEmptyMessage* other);
  void Swap(TestEmptyMessage* other);

  // implements Message ----------------------------------------------

  inline TestEmptyMessage* New() const { return New(NULL); }

  TestEmptyMessage* New(::google::protobuf::Arena* arena) const;
  void CopyFrom(const ::google::protobuf::Message& from);
  void MergeFrom(const ::google::protobuf::Message& from);
  void CopyFrom(const TestEmptyMessage& from);
  void MergeFrom(const TestEmptyMessage& from);
  void Clear();
  bool IsInitialized() const;

  size_t ByteSizeLong() const;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input);
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const;
  ::google::protobuf::uint8* InternalSerializeWithCachedSizesToArray(
      bool deterministic, ::google::protobuf::uint8* output) const;
  ::google::protobuf::uint8* SerializeWithCachedSizesToArray(::google::protobuf::uint8* output) const {
    return InternalSerializeWithCachedSizesToArray(false, output);
  }
  int GetCachedSize() const { return _cached_size_; }
  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const;
  void InternalSwap(TestEmptyMessage* other);
  void UnsafeMergeFrom(const TestEmptyMessage& from);
  protected:
  explicit TestEmptyMessage(::google::protobuf::Arena* arena);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::google::protobuf::Arena* arena);
  private:
  inline ::google::protobuf::Arena* GetArenaNoVirtual() const {
    return _internal_metadata_.arena();
  }
  inline void* MaybeArenaPtr() const {
    return _internal_metadata_.raw_arena_ptr();
  }
  public:

  ::google::protobuf::Metadata GetMetadata() const;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // @@protoc_insertion_point(class_scope:proto3_arena_unittest.TestEmptyMessage)
 private:

  ::google::protobuf::internal::InternalMetadataWithArena _internal_metadata_;
  friend class ::google::protobuf::Arena;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  mutable int _cached_size_;
  friend void  protobuf_InitDefaults_google_2fprotobuf_2funittest_5fproto3_5farena_2eproto_impl();
  friend void  protobuf_AddDesc_google_2fprotobuf_2funittest_5fproto3_5farena_2eproto_impl();
  friend void protobuf_AssignDesc_google_2fprotobuf_2funittest_5fproto3_5farena_2eproto();
  friend void protobuf_ShutdownFile_google_2fprotobuf_2funittest_5fproto3_5farena_2eproto();

  void InitAsDefaultInstance();
};
extern ::google::protobuf::internal::ExplicitlyConstructed<TestEmptyMessage> TestEmptyMessage_default_instance_;

// ===================================================================


// ===================================================================

#if !PROTOBUF_INLINE_NOT_IN_HEADERS
// TestAllTypes_NestedMessage

// optional int32 bb = 1;
inline void TestAllTypes_NestedMessage::clear_bb() {
  bb_ = 0;
}
inline ::google::protobuf::int32 TestAllTypes_NestedMessage::bb() const {
  // @@protoc_insertion_point(field_get:proto3_arena_unittest.TestAllTypes.NestedMessage.bb)
  return bb_;
}
inline void TestAllTypes_NestedMessage::set_bb(::google::protobuf::int32 value) {
  
  bb_ = value;
  // @@protoc_insertion_point(field_set:proto3_arena_unittest.TestAllTypes.NestedMessage.bb)
}

inline const TestAllTypes_NestedMessage* TestAllTypes_NestedMessage::internal_default_instance() {
  return &TestAllTypes_NestedMessage_default_instance_.get();
}
// -------------------------------------------------------------------

// TestAllTypes

// optional int32 optional_int32 = 1;
inline void TestAllTypes::clear_optional_int32() {
  optional_int32_ = 0;
}
inline ::google::protobuf::int32 TestAllTypes::optional_int32() const {
  // @@protoc_insertion_point(field_get:proto3_arena_unittest.TestAllTypes.optional_int32)
  return optional_int32_;
}
inline void TestAllTypes::set_optional_int32(::google::protobuf::int32 value) {
  
  optional_int32_ = value;
  // @@protoc_insertion_point(field_set:proto3_arena_unittest.TestAllTypes.optional_int32)
}

// optional int64 optional_int64 = 2;
inline void TestAllTypes::clear_optional_int64() {
  optional_int64_ = GOOGLE_LONGLONG(0);
}
inline ::google::protobuf::int64 TestAllTypes::optional_int64() const {
  // @@protoc_insertion_point(field_get:proto3_arena_unittest.TestAllTypes.optional_int64)
  return optional_int64_;
}
inline void TestAllTypes::set_optional_int64(::google::protobuf::int64 value) {
  
  optional_int64_ = value;
  // @@protoc_insertion_point(field_set:proto3_arena_unittest.TestAllTypes.optional_int64)
}

// optional uint32 optional_uint32 = 3;
inline void TestAllTypes::clear_optional_uint32() {
  optional_uint32_ = 0u;
}
inline ::google::protobuf::uint32 TestAllTypes::optional_uint32() const {
  // @@protoc_insertion_point(field_get:proto3_arena_unittest.TestAllTypes.optional_uint32)
  return optional_uint32_;
}
inline void TestAllTypes::set_optional_uint32(::google::protobuf::uint32 value) {
  
  optional_uint32_ = value;
  // @@protoc_insertion_point(field_set:proto3_arena_unittest.TestAllTypes.optional_uint32)
}

// optional uint64 optional_uint64 = 4;
inline void TestAllTypes::clear_optional_uint64() {
  optional_uint64_ = GOOGLE_ULONGLONG(0);
}
inline ::google::protobuf::uint64 TestAllTypes::optional_uint64() const {
  // @@protoc_insertion_point(field_get:proto3_arena_unittest.TestAllTypes.optional_uint64)
  return optional_uint64_;
}
inline void TestAllTypes::set_optional_uint64(::google::protobuf::uint64 value) {
  
  optional_uint64_ = value;
  // @@protoc_insertion_point(field_set:proto3_arena_unittest.TestAllTypes.optional_uint64)
}

// optional sint32 optional_sint32 = 5;
inline void TestAllTypes::clear_optional_sint32() {
  optional_sint32_ = 0;
}
inline ::google::protobuf::int32 TestAllTypes::optional_sint32() const {
  // @@protoc_insertion_point(field_get:proto3_arena_unittest.TestAllTypes.optional_sint32)
  return optional_sint32_;
}
inline void TestAllTypes::set_optional_sint32(::google::protobuf::int32 value) {
  
  optional_sint32_ = value;
  // @@protoc_insertion_point(field_set:proto3_arena_unittest.TestAllTypes.optional_sint32)
}

// optional sint64 optional_sint64 = 6;
inline void TestAllTypes::clear_optional_sint64() {
  optional_sint64_ = GOOGLE_LONGLONG(0);
}
inline ::google::protobuf::int64 TestAllTypes::optional_sint64() const {
  // @@protoc_insertion_point(field_get:proto3_arena_unittest.TestAllTypes.optional_sint64)
  return optional_sint64_;
}
inline void TestAllTypes::set_optional_sint64(::google::protobuf::int64 value) {
  
  optional_sint64_ = value;
  // @@protoc_insertion_point(field_set:proto3_arena_unittest.TestAllTypes.optional_sint64)
}

// optional fixed32 optional_fixed32 = 7;
inline void TestAllTypes::clear_optional_fixed32() {
  optional_fixed32_ = 0u;
}
inline ::google::protobuf::uint32 TestAllTypes::optional_fixed32() const {
  // @@protoc_insertion_point(field_get:proto3_arena_unittest.TestAllTypes.optional_fixed32)
  return optional_fixed32_;
}
inline void TestAllTypes::set_optional_fixed32(::google::protobuf::uint32 value) {
  
  optional_fixed32_ = value;
  // @@protoc_insertion_point(field_set:proto3_arena_unittest.TestAllTypes.optional_fixed32)
}

// optional fixed64 optional_fixed64 = 8;
inline void TestAllTypes::clear_optional_fixed64() {
  optional_fixed64_ = GOOGLE_ULONGLONG(0);
}
inline ::google::protobuf::uint64 TestAllTypes::optional_fixed64() const {
  // @@protoc_insertion_point(field_get:proto3_arena_unittest.TestAllTypes.optional_fixed64)
  return optional_fixed64_;
}
inline void TestAllTypes::set_optional_fixed64(::google::protobuf::uint64 value) {
  
  optional_fixed64_ = value;
  // @@protoc_insertion_point(field_set:proto3_arena_unittest.TestAllTypes.optional_fixed64)
}

// optional sfixed32 optional_sfixed32 = 9;
inline void TestAllTypes::clear_optional_sfixed32() {
  optional_sfixed32_ = 0;
}
inline ::google::protobuf::int32 TestAllTypes::optional_sfixed32() const {
  // @@protoc_insertion_point(field_get:proto3_arena_unittest.TestAllTypes.optional_sfixed32)
  return optional_sfixed32_;
}
inline void TestAllTypes::set_optional_sfixed32(::google::protobuf::int32 value) {
  
  optional_sfixed32_ = value;
  // @@protoc_insertion_point(field_set:proto3_arena_unittest.TestAllTypes.optional_sfixed32)
}

// optional sfixed64 optional_sfixed64 = 10;
inline void TestAllTypes::clear_optional_sfixed64() {
  optional_sfixed64_ = GOOGLE_LONGLONG(0);
}
inline ::google::protobuf::int64 TestAllTypes::optional_sfixed64() const {
  // @@protoc_insertion_point(field_get:proto3_arena_unittest.TestAllTypes.optional_sfixed64)
  return optional_sfixed64_;
}
inline void TestAllTypes::set_optional_sfixed64(::google::protobuf::int64 value) {
  
  optional_sfixed64_ = value;
  // @@protoc_insertion_point(field_set:proto3_arena_unittest.TestAllTypes.optional_sfixed64)
}

// optional float optional_float = 11;
inline void TestAllTypes::clear_optional_float() {
  optional_float_ = 0;
}
inline float TestAllTypes::optional_float() const {
  // @@protoc_insertion_point(field_get:proto3_arena_unittest.TestAllTypes.optional_float)
  return optional_float_;
}
inline void TestAllTypes::set_optional_float(float value) {
  
  optional_float_ = value;
  // @@protoc_insertion_point(field_set:proto3_arena_unittest.TestAllTypes.optional_float)
}

// optional double optional_double = 12;
inline void TestAllTypes::clear_optional_double() {
  optional_double_ = 0;
}
inline double TestAllTypes::optional_double() const {
  // @@protoc_insertion_point(field_get:proto3_arena_unittest.TestAllTypes.optional_double)
  return optional_double_;
}
inline void TestAllTypes::set_optional_double(double value) {
  
  optional_double_ = value;
  // @@protoc_insertion_point(field_set:proto3_arena_unittest.TestAllTypes.optional_double)
}

// optional bool optional_bool = 13;
inline void TestAllTypes::clear_optional_bool() {
  optional_bool_ = false;
}
inline bool TestAllTypes::optional_bool() const {
  // @@protoc_insertion_point(field_get:proto3_arena_unittest.TestAllTypes.optional_bool)
  return optional_bool_;
}
inline void TestAllTypes::set_optional_bool(bool value) {
  
  optional_bool_ = value;
  // @@protoc_insertion_point(field_set:proto3_arena_unittest.TestAllTypes.optional_bool)
}

// optional string optional_string = 14;
inline void TestAllTypes::clear_optional_string() {
  optional_string_.ClearToEmpty(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline const ::std::string& TestAllTypes::optional_string() const {
  // @@protoc_insertion_point(field_get:proto3_arena_unittest.TestAllTypes.optional_string)
  return optional_string_.Get(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void TestAllTypes::set_optional_string(const ::std::string& value) {
  
  optional_string_.Set(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value, GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set:proto3_arena_unittest.TestAllTypes.optional_string)
}
inline void TestAllTypes::set_optional_string(const char* value) {
  
  optional_string_.Set(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value),
              GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_char:proto3_arena_unittest.TestAllTypes.optional_string)
}
inline void TestAllTypes::set_optional_string(const char* value,
    size_t size) {
  
  optional_string_.Set(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(
      reinterpret_cast<const char*>(value), size), GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_pointer:proto3_arena_unittest.TestAllTypes.optional_string)
}
inline ::std::string* TestAllTypes::mutable_optional_string() {
  
  // @@protoc_insertion_point(field_mutable:proto3_arena_unittest.TestAllTypes.optional_string)
  return optional_string_.Mutable(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline ::std::string* TestAllTypes::release_optional_string() {
  // @@protoc_insertion_point(field_release:proto3_arena_unittest.TestAllTypes.optional_string)
  
  return optional_string_.Release(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline ::std::string* TestAllTypes::unsafe_arena_release_optional_string() {
  // @@protoc_insertion_point(field_unsafe_arena_release:proto3_arena_unittest.TestAllTypes.optional_string)
  GOOGLE_DCHECK(GetArenaNoVirtual() != NULL);
  
  return optional_string_.UnsafeArenaRelease(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      GetArenaNoVirtual());
}
inline void TestAllTypes::set_allocated_optional_string(::std::string* optional_string) {
  if (optional_string != NULL) {
    
  } else {
    
  }
  optional_string_.SetAllocated(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), optional_string,
      GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_allocated:proto3_arena_unittest.TestAllTypes.optional_string)
}
inline void TestAllTypes::unsafe_arena_set_allocated_optional_string(
    ::std::string* optional_string) {
  GOOGLE_DCHECK(GetArenaNoVirtual() != NULL);
  if (optional_string != NULL) {
    
  } else {
    
  }
  optional_string_.UnsafeArenaSetAllocated(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      optional_string, GetArenaNoVirtual());
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:proto3_arena_unittest.TestAllTypes.optional_string)
}

// optional bytes optional_bytes = 15;
inline void TestAllTypes::clear_optional_bytes() {
  optional_bytes_.ClearToEmpty(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline const ::std::string& TestAllTypes::optional_bytes() const {
  // @@protoc_insertion_point(field_get:proto3_arena_unittest.TestAllTypes.optional_bytes)
  return optional_bytes_.Get(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void TestAllTypes::set_optional_bytes(const ::std::string& value) {
  
  optional_bytes_.Set(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value, GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set:proto3_arena_unittest.TestAllTypes.optional_bytes)
}
inline void TestAllTypes::set_optional_bytes(const char* value) {
  
  optional_bytes_.Set(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value),
              GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_char:proto3_arena_unittest.TestAllTypes.optional_bytes)
}
inline void TestAllTypes::set_optional_bytes(const void* value,
    size_t size) {
  
  optional_bytes_.Set(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(
      reinterpret_cast<const char*>(value), size), GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_pointer:proto3_arena_unittest.TestAllTypes.optional_bytes)
}
inline ::std::string* TestAllTypes::mutable_optional_bytes() {
  
  // @@protoc_insertion_point(field_mutable:proto3_arena_unittest.TestAllTypes.optional_bytes)
  return optional_bytes_.Mutable(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline ::std::string* TestAllTypes::release_optional_bytes() {
  // @@protoc_insertion_point(field_release:proto3_arena_unittest.TestAllTypes.optional_bytes)
  
  return optional_bytes_.Release(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline ::std::string* TestAllTypes::unsafe_arena_release_optional_bytes() {
  // @@protoc_insertion_point(field_unsafe_arena_release:proto3_arena_unittest.TestAllTypes.optional_bytes)
  GOOGLE_DCHECK(GetArenaNoVirtual() != NULL);
  
  return optional_bytes_.UnsafeArenaRelease(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      GetArenaNoVirtual());
}
inline void TestAllTypes::set_allocated_optional_bytes(::std::string* optional_bytes) {
  if (optional_bytes != NULL) {
    
  } else {
    
  }
  optional_bytes_.SetAllocated(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), optional_bytes,
      GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_allocated:proto3_arena_unittest.TestAllTypes.optional_bytes)
}
inline void TestAllTypes::unsafe_arena_set_allocated_optional_bytes(
    ::std::string* optional_bytes) {
  GOOGLE_DCHECK(GetArenaNoVirtual() != NULL);
  if (optional_bytes != NULL) {
    
  } else {
    
  }
  optional_bytes_.UnsafeArenaSetAllocated(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      optional_bytes, GetArenaNoVirtual());
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:proto3_arena_unittest.TestAllTypes.optional_bytes)
}

// optional .proto3_arena_unittest.TestAllTypes.NestedMessage optional_nested_message = 18;
inline bool TestAllTypes::has_optional_nested_message() const {
  return this != internal_default_instance() && optional_nested_message_ != NULL;
}
inline void TestAllTypes::clear_optional_nested_message() {
  if (GetArenaNoVirtual() == NULL && optional_nested_message_ != NULL) delete optional_nested_message_;
  optional_nested_message_ = NULL;
}
inline const ::proto3_arena_unittest::TestAllTypes_NestedMessage& TestAllTypes::optional_nested_message() const {
  // @@protoc_insertion_point(field_get:proto3_arena_unittest.TestAllTypes.optional_nested_message)
  return optional_nested_message_ != NULL ? *optional_nested_message_
                         : *::proto3_arena_unittest::TestAllTypes_NestedMessage::internal_default_instance();
}
inline ::proto3_arena_unittest::TestAllTypes_NestedMessage* TestAllTypes::mutable_optional_nested_message() {
  
  if (optional_nested_message_ == NULL) {
    _slow_mutable_optional_nested_message();
  }
  // @@protoc_insertion_point(field_mutable:proto3_arena_unittest.TestAllTypes.optional_nested_message)
  return optional_nested_message_;
}
inline ::proto3_arena_unittest::TestAllTypes_NestedMessage* TestAllTypes::release_optional_nested_message() {
  // @@protoc_insertion_point(field_release:proto3_arena_unittest.TestAllTypes.optional_nested_message)
  
  if (GetArenaNoVirtual() != NULL) {
    return _slow_release_optional_nested_message();
  } else {
    ::proto3_arena_unittest::TestAllTypes_NestedMessage* temp = optional_nested_message_;
    optional_nested_message_ = NULL;
    return temp;
  }
}
inline  void TestAllTypes::set_allocated_optional_nested_message(::proto3_arena_unittest::TestAllTypes_NestedMessage* optional_nested_message) {
  ::google::protobuf::Arena* message_arena = GetArenaNoVirtual();
  if (message_arena == NULL) {
    delete optional_nested_message_;
  }
  if (optional_nested_message != NULL) {
    _slow_set_allocated_optional_nested_message(message_arena, &optional_nested_message);
  }
  optional_nested_message_ = optional_nested_message;
  if (optional_nested_message) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_set_allocated:proto3_arena_unittest.TestAllTypes.optional_nested_message)
}

// optional .proto3_arena_unittest.ForeignMessage optional_foreign_message = 19;
inline bool TestAllTypes::has_optional_foreign_message() const {
  return this != internal_default_instance() && optional_foreign_message_ != NULL;
}
inline void TestAllTypes::clear_optional_foreign_message() {
  if (GetArenaNoVirtual() == NULL && optional_foreign_message_ != NULL) delete optional_foreign_message_;
  optional_foreign_message_ = NULL;
}
inline const ::proto3_arena_unittest::ForeignMessage& TestAllTypes::optional_foreign_message() const {
  // @@protoc_insertion_point(field_get:proto3_arena_unittest.TestAllTypes.optional_foreign_message)
  return optional_foreign_message_ != NULL ? *optional_foreign_message_
                         : *::proto3_arena_unittest::ForeignMessage::internal_default_instance();
}
inline ::proto3_arena_unittest::ForeignMessage* TestAllTypes::mutable_optional_foreign_message() {
  
  if (optional_foreign_message_ == NULL) {
    _slow_mutable_optional_foreign_message();
  }
  // @@protoc_insertion_point(field_mutable:proto3_arena_unittest.TestAllTypes.optional_foreign_message)
  return optional_foreign_message_;
}
inline ::proto3_arena_unittest::ForeignMessage* TestAllTypes::release_optional_foreign_message() {
  // @@protoc_insertion_point(field_release:proto3_arena_unittest.TestAllTypes.optional_foreign_message)
  
  if (GetArenaNoVirtual() != NULL) {
    return _slow_release_optional_foreign_message();
  } else {
    ::proto3_arena_unittest::ForeignMessage* temp = optional_foreign_message_;
    optional_foreign_message_ = NULL;
    return temp;
  }
}
inline  void TestAllTypes::set_allocated_optional_foreign_message(::proto3_arena_unittest::ForeignMessage* optional_foreign_message) {
  ::google::protobuf::Arena* message_arena = GetArenaNoVirtual();
  if (message_arena == NULL) {
    delete optional_foreign_message_;
  }
  if (optional_foreign_message != NULL) {
    _slow_set_allocated_optional_foreign_message(message_arena, &optional_foreign_message);
  }
  optional_foreign_message_ = optional_foreign_message;
  if (optional_foreign_message) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_set_allocated:proto3_arena_unittest.TestAllTypes.optional_foreign_message)
}

// optional .protobuf_unittest_import.ImportMessage optional_import_message = 20;
inline bool TestAllTypes::has_optional_import_message() const {
  return this != internal_default_instance() && optional_import_message_ != NULL;
}
inline void TestAllTypes::clear_optional_import_message() {
  if (GetArenaNoVirtual() == NULL && optional_import_message_ != NULL) delete optional_import_message_;
  optional_import_message_ = NULL;
}
inline const ::protobuf_unittest_import::ImportMessage& TestAllTypes::optional_import_message() const {
  // @@protoc_insertion_point(field_get:proto3_arena_unittest.TestAllTypes.optional_import_message)
  return optional_import_message_ != NULL ? *optional_import_message_
                         : *::protobuf_unittest_import::ImportMessage::internal_default_instance();
}
inline ::protobuf_unittest_import::ImportMessage* TestAllTypes::mutable_optional_import_message() {
  
  if (optional_import_message_ == NULL) {
    _slow_mutable_optional_import_message();
  }
  // @@protoc_insertion_point(field_mutable:proto3_arena_unittest.TestAllTypes.optional_import_message)
  return optional_import_message_;
}
inline ::protobuf_unittest_import::ImportMessage* TestAllTypes::release_optional_import_message() {
  // @@protoc_insertion_point(field_release:proto3_arena_unittest.TestAllTypes.optional_import_message)
  
  if (GetArenaNoVirtual() != NULL) {
    return _slow_release_optional_import_message();
  } else {
    ::protobuf_unittest_import::ImportMessage* temp = optional_import_message_;
    optional_import_message_ = NULL;
    return temp;
  }
}
inline  void TestAllTypes::set_allocated_optional_import_message(::protobuf_unittest_import::ImportMessage* optional_import_message) {
  ::google::protobuf::Arena* message_arena = GetArenaNoVirtual();
  if (message_arena == NULL) {
    delete optional_import_message_;
  }
  if (optional_import_message != NULL) {
    _slow_set_allocated_optional_import_message(message_arena, &optional_import_message);
  }
  optional_import_message_ = optional_import_message;
  if (optional_import_message) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_set_allocated:proto3_arena_unittest.TestAllTypes.optional_import_message)
}

// optional .proto3_arena_unittest.TestAllTypes.NestedEnum optional_nested_enum = 21;
inline void TestAllTypes::clear_optional_nested_enum() {
  optional_nested_enum_ = 0;
}
inline ::proto3_arena_unittest::TestAllTypes_NestedEnum TestAllTypes::optional_nested_enum() const {
  // @@protoc_insertion_point(field_get:proto3_arena_unittest.TestAllTypes.optional_nested_enum)
  return static_cast< ::proto3_arena_unittest::TestAllTypes_NestedEnum >(optional_nested_enum_);
}
inline void TestAllTypes::set_optional_nested_enum(::proto3_arena_unittest::TestAllTypes_NestedEnum value) {
  
  optional_nested_enum_ = value;
  // @@protoc_insertion_point(field_set:proto3_arena_unittest.TestAllTypes.optional_nested_enum)
}

// optional .proto3_arena_unittest.ForeignEnum optional_foreign_enum = 22;
inline void TestAllTypes::clear_optional_foreign_enum() {
  optional_foreign_enum_ = 0;
}
inline ::proto3_arena_unittest::ForeignEnum TestAllTypes::optional_foreign_enum() const {
  // @@protoc_insertion_point(field_get:proto3_arena_unittest.TestAllTypes.optional_foreign_enum)
  return static_cast< ::proto3_arena_unittest::ForeignEnum >(optional_foreign_enum_);
}
inline void TestAllTypes::set_optional_foreign_enum(::proto3_arena_unittest::ForeignEnum value) {
  
  optional_foreign_enum_ = value;
  // @@protoc_insertion_point(field_set:proto3_arena_unittest.TestAllTypes.optional_foreign_enum)
}

// optional string optional_string_piece = 24 [ctype = STRING_PIECE];
inline void TestAllTypes::clear_optional_string_piece() {
  optional_string_piece_.ClearToEmpty(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline const ::std::string& TestAllTypes::optional_string_piece() const {
  // @@protoc_insertion_point(field_get:proto3_arena_unittest.TestAllTypes.optional_string_piece)
  return optional_string_piece_.Get(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void TestAllTypes::set_optional_string_piece(const ::std::string& value) {
  
  optional_string_piece_.Set(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value, GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set:proto3_arena_unittest.TestAllTypes.optional_string_piece)
}
inline void TestAllTypes::set_optional_string_piece(const char* value) {
  
  optional_string_piece_.Set(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value),
              GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_char:proto3_arena_unittest.TestAllTypes.optional_string_piece)
}
inline void TestAllTypes::set_optional_string_piece(const char* value,
    size_t size) {
  
  optional_string_piece_.Set(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(
      reinterpret_cast<const char*>(value), size), GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_pointer:proto3_arena_unittest.TestAllTypes.optional_string_piece)
}
inline ::std::string* TestAllTypes::mutable_optional_string_piece() {
  
  // @@protoc_insertion_point(field_mutable:proto3_arena_unittest.TestAllTypes.optional_string_piece)
  return optional_string_piece_.Mutable(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline ::std::string* TestAllTypes::release_optional_string_piece() {
  // @@protoc_insertion_point(field_release:proto3_arena_unittest.TestAllTypes.optional_string_piece)
  
  return optional_string_piece_.Release(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline ::std::string* TestAllTypes::unsafe_arena_release_optional_string_piece() {
  // @@protoc_insertion_point(field_unsafe_arena_release:proto3_arena_unittest.TestAllTypes.optional_string_piece)
  GOOGLE_DCHECK(GetArenaNoVirtual() != NULL);
  
  return optional_string_piece_.UnsafeArenaRelease(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      GetArenaNoVirtual());
}
inline void TestAllTypes::set_allocated_optional_string_piece(::std::string* optional_string_piece) {
  if (optional_string_piece != NULL) {
    
  } else {
    
  }
  optional_string_piece_.SetAllocated(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), optional_string_piece,
      GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_allocated:proto3_arena_unittest.TestAllTypes.optional_string_piece)
}
inline void TestAllTypes::unsafe_arena_set_allocated_optional_string_piece(
    ::std::string* optional_string_piece) {
  GOOGLE_DCHECK(GetArenaNoVirtual() != NULL);
  if (optional_string_piece != NULL) {
    
  } else {
    
  }
  optional_string_piece_.UnsafeArenaSetAllocated(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      optional_string_piece, GetArenaNoVirtual());
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:proto3_arena_unittest.TestAllTypes.optional_string_piece)
}

// optional string optional_cord = 25 [ctype = CORD];
inline void TestAllTypes::clear_optional_cord() {
  optional_cord_.ClearToEmpty(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline const ::std::string& TestAllTypes::optional_cord() const {
  // @@protoc_insertion_point(field_get:proto3_arena_unittest.TestAllTypes.optional_cord)
  return optional_cord_.Get(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void TestAllTypes::set_optional_cord(const ::std::string& value) {
  
  optional_cord_.Set(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value, GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set:proto3_arena_unittest.TestAllTypes.optional_cord)
}
inline void TestAllTypes::set_optional_cord(const char* value) {
  
  optional_cord_.Set(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value),
              GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_char:proto3_arena_unittest.TestAllTypes.optional_cord)
}
inline void TestAllTypes::set_optional_cord(const char* value,
    size_t size) {
  
  optional_cord_.Set(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(
      reinterpret_cast<const char*>(value), size), GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_pointer:proto3_arena_unittest.TestAllTypes.optional_cord)
}
inline ::std::string* TestAllTypes::mutable_optional_cord() {
  
  // @@protoc_insertion_point(field_mutable:proto3_arena_unittest.TestAllTypes.optional_cord)
  return optional_cord_.Mutable(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline ::std::string* TestAllTypes::release_optional_cord() {
  // @@protoc_insertion_point(field_release:proto3_arena_unittest.TestAllTypes.optional_cord)
  
  return optional_cord_.Release(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline ::std::string* TestAllTypes::unsafe_arena_release_optional_cord() {
  // @@protoc_insertion_point(field_unsafe_arena_release:proto3_arena_unittest.TestAllTypes.optional_cord)
  GOOGLE_DCHECK(GetArenaNoVirtual() != NULL);
  
  return optional_cord_.UnsafeArenaRelease(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      GetArenaNoVirtual());
}
inline void TestAllTypes::set_allocated_optional_cord(::std::string* optional_cord) {
  if (optional_cord != NULL) {
    
  } else {
    
  }
  optional_cord_.SetAllocated(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), optional_cord,
      GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_allocated:proto3_arena_unittest.TestAllTypes.optional_cord)
}
inline void TestAllTypes::unsafe_arena_set_allocated_optional_cord(
    ::std::string* optional_cord) {
  GOOGLE_DCHECK(GetArenaNoVirtual() != NULL);
  if (optional_cord != NULL) {
    
  } else {
    
  }
  optional_cord_.UnsafeArenaSetAllocated(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      optional_cord, GetArenaNoVirtual());
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:proto3_arena_unittest.TestAllTypes.optional_cord)
}

// optional .protobuf_unittest_import.PublicImportMessage optional_public_import_message = 26;
inline bool TestAllTypes::has_optional_public_import_message() const {
  return this != internal_default_instance() && optional_public_import_message_ != NULL;
}
inline void TestAllTypes::clear_optional_public_import_message() {
  if (GetArenaNoVirtual() == NULL && optional_public_import_message_ != NULL) delete optional_public_import_message_;
  optional_public_import_message_ = NULL;
}
inline const ::protobuf_unittest_import::PublicImportMessage& TestAllTypes::optional_public_import_message() const {
  // @@protoc_insertion_point(field_get:proto3_arena_unittest.TestAllTypes.optional_public_import_message)
  return optional_public_import_message_ != NULL ? *optional_public_import_message_
                         : *::protobuf_unittest_import::PublicImportMessage::internal_default_instance();
}
inline ::protobuf_unittest_import::PublicImportMessage* TestAllTypes::mutable_optional_public_import_message() {
  
  if (optional_public_import_message_ == NULL) {
    _slow_mutable_optional_public_import_message();
  }
  // @@protoc_insertion_point(field_mutable:proto3_arena_unittest.TestAllTypes.optional_public_import_message)
  return optional_public_import_message_;
}
inline ::protobuf_unittest_import::PublicImportMessage* TestAllTypes::release_optional_public_import_message() {
  // @@protoc_insertion_point(field_release:proto3_arena_unittest.TestAllTypes.optional_public_import_message)
  
  if (GetArenaNoVirtual() != NULL) {
    return _slow_release_optional_public_import_message();
  } else {
    ::protobuf_unittest_import::PublicImportMessage* temp = optional_public_import_message_;
    optional_public_import_message_ = NULL;
    return temp;
  }
}
inline  void TestAllTypes::set_allocated_optional_public_import_message(::protobuf_unittest_import::PublicImportMessage* optional_public_import_message) {
  ::google::protobuf::Arena* message_arena = GetArenaNoVirtual();
  if (message_arena == NULL) {
    delete optional_public_import_message_;
  }
  if (optional_public_import_message != NULL) {
    if (message_arena != NULL) {
      message_arena->Own(optional_public_import_message);
    }
  }
  optional_public_import_message_ = optional_public_import_message;
  if (optional_public_import_message) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_set_allocated:proto3_arena_unittest.TestAllTypes.optional_public_import_message)
}

// optional .proto3_arena_unittest.TestAllTypes.NestedMessage optional_lazy_message = 27 [lazy = true];
inline bool TestAllTypes::has_optional_lazy_message() const {
  return this != internal_default_instance() && optional_lazy_message_ != NULL;
}
inline void TestAllTypes::clear_optional_lazy_message() {
  if (GetArenaNoVirtual() == NULL && optional_lazy_message_ != NULL) delete optional_lazy_message_;
  optional_lazy_message_ = NULL;
}
inline const ::proto3_arena_unittest::TestAllTypes_NestedMessage& TestAllTypes::optional_lazy_message() const {
  // @@protoc_insertion_point(field_get:proto3_arena_unittest.TestAllTypes.optional_lazy_message)
  return optional_lazy_message_ != NULL ? *optional_lazy_message_
                         : *::proto3_arena_unittest::TestAllTypes_NestedMessage::internal_default_instance();
}
inline ::proto3_arena_unittest::TestAllTypes_NestedMessage* TestAllTypes::mutable_optional_lazy_message() {
  
  if (optional_lazy_message_ == NULL) {
    _slow_mutable_optional_lazy_message();
  }
  // @@protoc_insertion_point(field_mutable:proto3_arena_unittest.TestAllTypes.optional_lazy_message)
  return optional_lazy_message_;
}
inline ::proto3_arena_unittest::TestAllTypes_NestedMessage* TestAllTypes::release_optional_lazy_message() {
  // @@protoc_insertion_point(field_release:proto3_arena_unittest.TestAllTypes.optional_lazy_message)
  
  if (GetArenaNoVirtual() != NULL) {
    return _slow_release_optional_lazy_message();
  } else {
    ::proto3_arena_unittest::TestAllTypes_NestedMessage* temp = optional_lazy_message_;
    optional_lazy_message_ = NULL;
    return temp;
  }
}
inline  void TestAllTypes::set_allocated_optional_lazy_message(::proto3_arena_unittest::TestAllTypes_NestedMessage* optional_lazy_message) {
  ::google::protobuf::Arena* message_arena = GetArenaNoVirtual();
  if (message_arena == NULL) {
    delete optional_lazy_message_;
  }
  if (optional_lazy_message != NULL) {
    _slow_set_allocated_optional_lazy_message(message_arena, &optional_lazy_message);
  }
  optional_lazy_message_ = optional_lazy_message;
  if (optional_lazy_message) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_set_allocated:proto3_arena_unittest.TestAllTypes.optional_lazy_message)
}

// repeated int32 repeated_int32 = 31;
inline int TestAllTypes::repeated_int32_size() const {
  return repeated_int32_.size();
}
inline void TestAllTypes::clear_repeated_int32() {
  repeated_int32_.Clear();
}
inline ::google::protobuf::int32 TestAllTypes::repeated_int32(int index) const {
  // @@protoc_insertion_point(field_get:proto3_arena_unittest.TestAllTypes.repeated_int32)
  return repeated_int32_.Get(index);
}
inline void TestAllTypes::set_repeated_int32(int index, ::google::protobuf::int32 value) {
  repeated_int32_.Set(index, value);
  // @@protoc_insertion_point(field_set:proto3_arena_unittest.TestAllTypes.repeated_int32)
}
inline void TestAllTypes::add_repeated_int32(::google::protobuf::int32 value) {
  repeated_int32_.Add(value);
  // @@protoc_insertion_point(field_add:proto3_arena_unittest.TestAllTypes.repeated_int32)
}
inline const ::google::protobuf::RepeatedField< ::google::protobuf::int32 >&
TestAllTypes::repeated_int32() const {
  // @@protoc_insertion_point(field_list:proto3_arena_unittest.TestAllTypes.repeated_int32)
  return repeated_int32_;
}
inline ::google::protobuf::RepeatedField< ::google::protobuf::int32 >*
TestAllTypes::mutable_repeated_int32() {
  // @@protoc_insertion_point(field_mutable_list:proto3_arena_unittest.TestAllTypes.repeated_int32)
  return &repeated_int32_;
}

// repeated int64 repeated_int64 = 32;
inline int TestAllTypes::repeated_int64_size() const {
  return repeated_int64_.size();
}
inline void TestAllTypes::clear_repeated_int64() {
  repeated_int64_.Clear();
}
inline ::google::protobuf::int64 TestAllTypes::repeated_int64(int index) const {
  // @@protoc_insertion_point(field_get:proto3_arena_unittest.TestAllTypes.repeated_int64)
  return repeated_int64_.Get(index);
}
inline void TestAllTypes::set_repeated_int64(int index, ::google::protobuf::int64 value) {
  repeated_int64_.Set(index, value);
  // @@protoc_insertion_point(field_set:proto3_arena_unittest.TestAllTypes.repeated_int64)
}
inline void TestAllTypes::add_repeated_int64(::google::protobuf::int64 value) {
  repeated_int64_.Add(value);
  // @@protoc_insertion_point(field_add:proto3_arena_unittest.TestAllTypes.repeated_int64)
}
inline const ::google::protobuf::RepeatedField< ::google::protobuf::int64 >&
TestAllTypes::repeated_int64() const {
  // @@protoc_insertion_point(field_list:proto3_arena_unittest.TestAllTypes.repeated_int64)
  return repeated_int64_;
}
inline ::google::protobuf::RepeatedField< ::google::protobuf::int64 >*
TestAllTypes::mutable_repeated_int64() {
  // @@protoc_insertion_point(field_mutable_list:proto3_arena_unittest.TestAllTypes.repeated_int64)
  return &repeated_int64_;
}

// repeated uint32 repeated_uint32 = 33;
inline int TestAllTypes::repeated_uint32_size() const {
  return repeated_uint32_.size();
}
inline void TestAllTypes::clear_repeated_uint32() {
  repeated_uint32_.Clear();
}
inline ::google::protobuf::uint32 TestAllTypes::repeated_uint32(int index) const {
  // @@protoc_insertion_point(field_get:proto3_arena_unittest.TestAllTypes.repeated_uint32)
  return repeated_uint32_.Get(index);
}
inline void TestAllTypes::set_repeated_uint32(int index, ::google::protobuf::uint32 value) {
  repeated_uint32_.Set(index, value);
  // @@protoc_insertion_point(field_set:proto3_arena_unittest.TestAllTypes.repeated_uint32)
}
inline void TestAllTypes::add_repeated_uint32(::google::protobuf::uint32 value) {
  repeated_uint32_.Add(value);
  // @@protoc_insertion_point(field_add:proto3_arena_unittest.TestAllTypes.repeated_uint32)
}
inline const ::google::protobuf::RepeatedField< ::google::protobuf::uint32 >&
TestAllTypes::repeated_uint32() const {
  // @@protoc_insertion_point(field_list:proto3_arena_unittest.TestAllTypes.repeated_uint32)
  return repeated_uint32_;
}
inline ::google::protobuf::RepeatedField< ::google::protobuf::uint32 >*
TestAllTypes::mutable_repeated_uint32() {
  // @@protoc_insertion_point(field_mutable_list:proto3_arena_unittest.TestAllTypes.repeated_uint32)
  return &repeated_uint32_;
}

// repeated uint64 repeated_uint64 = 34;
inline int TestAllTypes::repeated_uint64_size() const {
  return repeated_uint64_.size();
}
inline void TestAllTypes::clear_repeated_uint64() {
  repeated_uint64_.Clear();
}
inline ::google::protobuf::uint64 TestAllTypes::repeated_uint64(int index) const {
  // @@protoc_insertion_point(field_get:proto3_arena_unittest.TestAllTypes.repeated_uint64)
  return repeated_uint64_.Get(index);
}
inline void TestAllTypes::set_repeated_uint64(int index, ::google::protobuf::uint64 value) {
  repeated_uint64_.Set(index, value);
  // @@protoc_insertion_point(field_set:proto3_arena_unittest.TestAllTypes.repeated_uint64)
}
inline void TestAllTypes::add_repeated_uint64(::google::protobuf::uint64 value) {
  repeated_uint64_.Add(value);
  // @@protoc_insertion_point(field_add:proto3_arena_unittest.TestAllTypes.repeated_uint64)
}
inline const ::google::protobuf::RepeatedField< ::google::protobuf::uint64 >&
TestAllTypes::repeated_uint64() const {
  // @@protoc_insertion_point(field_list:proto3_arena_unittest.TestAllTypes.repeated_uint64)
  return repeated_uint64_;
}
inline ::google::protobuf::RepeatedField< ::google::protobuf::uint64 >*
TestAllTypes::mutable_repeated_uint64() {
  // @@protoc_insertion_point(field_mutable_list:proto3_arena_unittest.TestAllTypes.repeated_uint64)
  return &repeated_uint64_;
}

// repeated sint32 repeated_sint32 = 35;
inline int TestAllTypes::repeated_sint32_size() const {
  return repeated_sint32_.size();
}
inline void TestAllTypes::clear_repeated_sint32() {
  repeated_sint32_.Clear();
}
inline ::google::protobuf::int32 TestAllTypes::repeated_sint32(int index) const {
  // @@protoc_insertion_point(field_get:proto3_arena_unittest.TestAllTypes.repeated_sint32)
  return repeated_sint32_.Get(index);
}
inline void TestAllTypes::set_repeated_sint32(int index, ::google::protobuf::int32 value) {
  repeated_sint32_.Set(index, value);
  // @@protoc_insertion_point(field_set:proto3_arena_unittest.TestAllTypes.repeated_sint32)
}
inline void TestAllTypes::add_repeated_sint32(::google::protobuf::int32 value) {
  repeated_sint32_.Add(value);
  // @@protoc_insertion_point(field_add:proto3_arena_unittest.TestAllTypes.repeated_sint32)
}
inline const ::google::protobuf::RepeatedField< ::google::protobuf::int32 >&
TestAllTypes::repeated_sint32() const {
  // @@protoc_insertion_point(field_list:proto3_arena_unittest.TestAllTypes.repeated_sint32)
  return repeated_sint32_;
}
inline ::google::protobuf::RepeatedField< ::google::protobuf::int32 >*
TestAllTypes::mutable_repeated_sint32() {
  // @@protoc_insertion_point(field_mutable_list:proto3_arena_unittest.TestAllTypes.repeated_sint32)
  return &repeated_sint32_;
}

// repeated sint64 repeated_sint64 = 36;
inline int TestAllTypes::repeated_sint64_size() const {
  return repeated_sint64_.size();
}
inline void TestAllTypes::clear_repeated_sint64() {
  repeated_sint64_.Clear();
}
inline ::google::protobuf::int64 TestAllTypes::repeated_sint64(int index) const {
  // @@protoc_insertion_point(field_get:proto3_arena_unittest.TestAllTypes.repeated_sint64)
  return repeated_sint64_.Get(index);
}
inline void TestAllTypes::set_repeated_sint64(int index, ::google::protobuf::int64 value) {
  repeated_sint64_.Set(index, value);
  // @@protoc_insertion_point(field_set:proto3_arena_unittest.TestAllTypes.repeated_sint64)
}
inline void TestAllTypes::add_repeated_sint64(::google::protobuf::int64 value) {
  repeated_sint64_.Add(value);
  // @@protoc_insertion_point(field_add:proto3_arena_unittest.TestAllTypes.repeated_sint64)
}
inline const ::google::protobuf::RepeatedField< ::google::protobuf::int64 >&
TestAllTypes::repeated_sint64() const {
  // @@protoc_insertion_point(field_list:proto3_arena_unittest.TestAllTypes.repeated_sint64)
  return repeated_sint64_;
}
inline ::google::protobuf::RepeatedField< ::google::protobuf::int64 >*
TestAllTypes::mutable_repeated_sint64() {
  // @@protoc_insertion_point(field_mutable_list:proto3_arena_unittest.TestAllTypes.repeated_sint64)
  return &repeated_sint64_;
}

// repeated fixed32 repeated_fixed32 = 37;
inline int TestAllTypes::repeated_fixed32_size() const {
  return repeated_fixed32_.size();
}
inline void TestAllTypes::clear_repeated_fixed32() {
  repeated_fixed32_.Clear();
}
inline ::google::protobuf::uint32 TestAllTypes::repeated_fixed32(int index) const {
  // @@protoc_insertion_point(field_get:proto3_arena_unittest.TestAllTypes.repeated_fixed32)
  return repeated_fixed32_.Get(index);
}
inline void TestAllTypes::set_repeated_fixed32(int index, ::google::protobuf::uint32 value) {
  repeated_fixed32_.Set(index, value);
  // @@protoc_insertion_point(field_set:proto3_arena_unittest.TestAllTypes.repeated_fixed32)
}
inline void TestAllTypes::add_repeated_fixed32(::google::protobuf::uint32 value) {
  repeated_fixed32_.Add(value);
  // @@protoc_insertion_point(field_add:proto3_arena_unittest.TestAllTypes.repeated_fixed32)
}
inline const ::google::protobuf::RepeatedField< ::google::protobuf::uint32 >&
TestAllTypes::repeated_fixed32() const {
  // @@protoc_insertion_point(field_list:proto3_arena_unittest.TestAllTypes.repeated_fixed32)
  return repeated_fixed32_;
}
inline ::google::protobuf::RepeatedField< ::google::protobuf::uint32 >*
TestAllTypes::mutable_repeated_fixed32() {
  // @@protoc_insertion_point(field_mutable_list:proto3_arena_unittest.TestAllTypes.repeated_fixed32)
  return &repeated_fixed32_;
}

// repeated fixed64 repeated_fixed64 = 38;
inline int TestAllTypes::repeated_fixed64_size() const {
  return repeated_fixed64_.size();
}
inline void TestAllTypes::clear_repeated_fixed64() {
  repeated_fixed64_.Clear();
}
inline ::google::protobuf::uint64 TestAllTypes::repeated_fixed64(int index) const {
  // @@protoc_insertion_point(field_get:proto3_arena_unittest.TestAllTypes.repeated_fixed64)
  return repeated_fixed64_.Get(index);
}
inline void TestAllTypes::set_repeated_fixed64(int index, ::google::protobuf::uint64 value) {
  repeated_fixed64_.Set(index, value);
  // @@protoc_insertion_point(field_set:proto3_arena_unittest.TestAllTypes.repeated_fixed64)
}
inline void TestAllTypes::add_repeated_fixed64(::google::protobuf::uint64 value) {
  repeated_fixed64_.Add(value);
  // @@protoc_insertion_point(field_add:proto3_arena_unittest.TestAllTypes.repeated_fixed64)
}
inline const ::google::protobuf::RepeatedField< ::google::protobuf::uint64 >&
TestAllTypes::repeated_fixed64() const {
  // @@protoc_insertion_point(field_list:proto3_arena_unittest.TestAllTypes.repeated_fixed64)
  return repeated_fixed64_;
}
inline ::google::protobuf::RepeatedField< ::google::protobuf::uint64 >*
TestAllTypes::mutable_repeated_fixed64() {
  // @@protoc_insertion_point(field_mutable_list:proto3_arena_unittest.TestAllTypes.repeated_fixed64)
  return &repeated_fixed64_;
}

// repeated sfixed32 repeated_sfixed32 = 39;
inline int TestAllTypes::repeated_sfixed32_size() const {
  return repeated_sfixed32_.size();
}
inline void TestAllTypes::clear_repeated_sfixed32() {
  repeated_sfixed32_.Clear();
}
inline ::google::protobuf::int32 TestAllTypes::repeated_sfixed32(int index) const {
  // @@protoc_insertion_point(field_get:proto3_arena_unittest.TestAllTypes.repeated_sfixed32)
  return repeated_sfixed32_.Get(index);
}
inline void TestAllTypes::set_repeated_sfixed32(int index, ::google::protobuf::int32 value) {
  repeated_sfixed32_.Set(index, value);
  // @@protoc_insertion_point(field_set:proto3_arena_unittest.TestAllTypes.repeated_sfixed32)
}
inline void TestAllTypes::add_repeated_sfixed32(::google::protobuf::int32 value) {
  repeated_sfixed32_.Add(value);
  // @@protoc_insertion_point(field_add:proto3_arena_unittest.TestAllTypes.repeated_sfixed32)
}
inline const ::google::protobuf::RepeatedField< ::google::protobuf::int32 >&
TestAllTypes::repeated_sfixed32() const {
  // @@protoc_insertion_point(field_list:proto3_arena_unittest.TestAllTypes.repeated_sfixed32)
  return repeated_sfixed32_;
}
inline ::google::protobuf::RepeatedField< ::google::protobuf::int32 >*
TestAllTypes::mutable_repeated_sfixed32() {
  // @@protoc_insertion_point(field_mutable_list:proto3_arena_unittest.TestAllTypes.repeated_sfixed32)
  return &repeated_sfixed32_;
}

// repeated sfixed64 repeated_sfixed64 = 40;
inline int TestAllTypes::repeated_sfixed64_size() const {
  return repeated_sfixed64_.size();
}
inline void TestAllTypes::clear_repeated_sfixed64() {
  repeated_sfixed64_.Clear();
}
inline ::google::protobuf::int64 TestAllTypes::repeated_sfixed64(int index) const {
  // @@protoc_insertion_point(field_get:proto3_arena_unittest.TestAllTypes.repeated_sfixed64)
  return repeated_sfixed64_.Get(index);
}
inline void TestAllTypes::set_repeated_sfixed64(int index, ::google::protobuf::int64 value) {
  repeated_sfixed64_.Set(index, value);
  // @@protoc_insertion_point(field_set:proto3_arena_unittest.TestAllTypes.repeated_sfixed64)
}
inline void TestAllTypes::add_repeated_sfixed64(::google::protobuf::int64 value) {
  repeated_sfixed64_.Add(value);
  // @@protoc_insertion_point(field_add:proto3_arena_unittest.TestAllTypes.repeated_sfixed64)
}
inline const ::google::protobuf::RepeatedField< ::google::protobuf::int64 >&
TestAllTypes::repeated_sfixed64() const {
  // @@protoc_insertion_point(field_list:proto3_arena_unittest.TestAllTypes.repeated_sfixed64)
  return repeated_sfixed64_;
}
inline ::google::protobuf::RepeatedField< ::google::protobuf::int64 >*
TestAllTypes::mutable_repeated_sfixed64() {
  // @@protoc_insertion_point(field_mutable_list:proto3_arena_unittest.TestAllTypes.repeated_sfixed64)
  return &repeated_sfixed64_;
}

// repeated float repeated_float = 41;
inline int TestAllTypes::repeated_float_size() const {
  return repeated_float_.size();
}
inline void TestAllTypes::clear_repeated_float() {
  repeated_float_.Clear();
}
inline float TestAllTypes::repeated_float(int index) const {
  // @@protoc_insertion_point(field_get:proto3_arena_unittest.TestAllTypes.repeated_float)
  return repeated_float_.Get(index);
}
inline void TestAllTypes::set_repeated_float(int index, float value) {
  repeated_float_.Set(index, value);
  // @@protoc_insertion_point(field_set:proto3_arena_unittest.TestAllTypes.repeated_float)
}
inline void TestAllTypes::add_repeated_float(float value) {
  repeated_float_.Add(value);
  // @@protoc_insertion_point(field_add:proto3_arena_unittest.TestAllTypes.repeated_float)
}
inline const ::google::protobuf::RepeatedField< float >&
TestAllTypes::repeated_float() const {
  // @@protoc_insertion_point(field_list:proto3_arena_unittest.TestAllTypes.repeated_float)
  return repeated_float_;
}
inline ::google::protobuf::RepeatedField< float >*
TestAllTypes::mutable_repeated_float() {
  // @@protoc_insertion_point(field_mutable_list:proto3_arena_unittest.TestAllTypes.repeated_float)
  return &repeated_float_;
}

// repeated double repeated_double = 42;
inline int TestAllTypes::repeated_double_size() const {
  return repeated_double_.size();
}
inline void TestAllTypes::clear_repeated_double() {
  repeated_double_.Clear();
}
inline double TestAllTypes::repeated_double(int index) const {
  // @@protoc_insertion_point(field_get:proto3_arena_unittest.TestAllTypes.repeated_double)
  return repeated_double_.Get(index);
}
inline void TestAllTypes::set_repeated_double(int index, double value) {
  repeated_double_.Set(index, value);
  // @@protoc_insertion_point(field_set:proto3_arena_unittest.TestAllTypes.repeated_double)
}
inline void TestAllTypes::add_repeated_double(double value) {
  repeated_double_.Add(value);
  // @@protoc_insertion_point(field_add:proto3_arena_unittest.TestAllTypes.repeated_double)
}
inline const ::google::protobuf::RepeatedField< double >&
TestAllTypes::repeated_double() const {
  // @@protoc_insertion_point(field_list:proto3_arena_unittest.TestAllTypes.repeated_double)
  return repeated_double_;
}
inline ::google::protobuf::RepeatedField< double >*
TestAllTypes::mutable_repeated_double() {
  // @@protoc_insertion_point(field_mutable_list:proto3_arena_unittest.TestAllTypes.repeated_double)
  return &repeated_double_;
}

// repeated bool repeated_bool = 43;
inline int TestAllTypes::repeated_bool_size() const {
  return repeated_bool_.size();
}
inline void TestAllTypes::clear_repeated_bool() {
  repeated_bool_.Clear();
}
inline bool TestAllTypes::repeated_bool(int index) const {
  // @@protoc_insertion_point(field_get:proto3_arena_unittest.TestAllTypes.repeated_bool)
  return repeated_bool_.Get(index);
}
inline void TestAllTypes::set_repeated_bool(int index, bool value) {
  repeated_bool_.Set(index, value);
  // @@protoc_insertion_point(field_set:proto3_arena_unittest.TestAllTypes.repeated_bool)
}
inline void TestAllTypes::add_repeated_bool(bool value) {
  repeated_bool_.Add(value);
  // @@protoc_insertion_point(field_add:proto3_arena_unittest.TestAllTypes.repeated_bool)
}
inline const ::google::protobuf::RepeatedField< bool >&
TestAllTypes::repeated_bool() const {
  // @@protoc_insertion_point(field_list:proto3_arena_unittest.TestAllTypes.repeated_bool)
  return repeated_bool_;
}
inline ::google::protobuf::RepeatedField< bool >*
TestAllTypes::mutable_repeated_bool() {
  // @@protoc_insertion_point(field_mutable_list:proto3_arena_unittest.TestAllTypes.repeated_bool)
  return &repeated_bool_;
}

// repeated string repeated_string = 44;
inline int TestAllTypes::repeated_string_size() const {
  return repeated_string_.size();
}
inline void TestAllTypes::clear_repeated_string() {
  repeated_string_.Clear();
}
inline const ::std::string& TestAllTypes::repeated_string(int index) const {
  // @@protoc_insertion_point(field_get:proto3_arena_unittest.TestAllTypes.repeated_string)
  return repeated_string_.Get(index);
}
inline ::std::string* TestAllTypes::mutable_repeated_string(int index) {
  // @@protoc_insertion_point(field_mutable:proto3_arena_unittest.TestAllTypes.repeated_string)
  return repeated_string_.Mutable(index);
}
inline void TestAllTypes::set_repeated_string(int index, const ::std::string& value) {
  // @@protoc_insertion_point(field_set:proto3_arena_unittest.TestAllTypes.repeated_string)
  repeated_string_.Mutable(index)->assign(value);
}
inline void TestAllTypes::set_repeated_string(int index, const char* value) {
  repeated_string_.Mutable(index)->assign(value);
  // @@protoc_insertion_point(field_set_char:proto3_arena_unittest.TestAllTypes.repeated_string)
}
inline void TestAllTypes::set_repeated_string(int index, const char* value, size_t size) {
  repeated_string_.Mutable(index)->assign(
    reinterpret_cast<const char*>(value), size);
  // @@protoc_insertion_point(field_set_pointer:proto3_arena_unittest.TestAllTypes.repeated_string)
}
inline ::std::string* TestAllTypes::add_repeated_string() {
  // @@protoc_insertion_point(field_add_mutable:proto3_arena_unittest.TestAllTypes.repeated_string)
  return repeated_string_.Add();
}
inline void TestAllTypes::add_repeated_string(const ::std::string& value) {
  repeated_string_.Add()->assign(value);
  // @@protoc_insertion_point(field_add:proto3_arena_unittest.TestAllTypes.repeated_string)
}
inline void TestAllTypes::add_repeated_string(const char* value) {
  repeated_string_.Add()->assign(value);
  // @@protoc_insertion_point(field_add_char:proto3_arena_unittest.TestAllTypes.repeated_string)
}
inline void TestAllTypes::add_repeated_string(const char* value, size_t size) {
  repeated_string_.Add()->assign(reinterpret_cast<const char*>(value), size);
  // @@protoc_insertion_point(field_add_pointer:proto3_arena_unittest.TestAllTypes.repeated_string)
}
inline const ::google::protobuf::RepeatedPtrField< ::std::string>&
TestAllTypes::repeated_string() const {
  // @@protoc_insertion_point(field_list:proto3_arena_unittest.TestAllTypes.repeated_string)
  return repeated_string_;
}
inline ::google::protobuf::RepeatedPtrField< ::std::string>*
TestAllTypes::mutable_repeated_string() {
  // @@protoc_insertion_point(field_mutable_list:proto3_arena_unittest.TestAllTypes.repeated_string)
  return &repeated_string_;
}

// repeated bytes repeated_bytes = 45;
inline int TestAllTypes::repeated_bytes_size() const {
  return repeated_bytes_.size();
}
inline void TestAllTypes::clear_repeated_bytes() {
  repeated_bytes_.Clear();
}
inline const ::std::string& TestAllTypes::repeated_bytes(int index) const {
  // @@protoc_insertion_point(field_get:proto3_arena_unittest.TestAllTypes.repeated_bytes)
  return repeated_bytes_.Get(index);
}
inline ::std::string* TestAllTypes::mutable_repeated_bytes(int index) {
  // @@protoc_insertion_point(field_mutable:proto3_arena_unittest.TestAllTypes.repeated_bytes)
  return repeated_bytes_.Mutable(index);
}
inline void TestAllTypes::set_repeated_bytes(int index, const ::std::string& value) {
  // @@protoc_insertion_point(field_set:proto3_arena_unittest.TestAllTypes.repeated_bytes)
  repeated_bytes_.Mutable(index)->assign(value);
}
inline void TestAllTypes::set_repeated_bytes(int index, const char* value) {
  repeated_bytes_.Mutable(index)->assign(value);
  // @@protoc_insertion_point(field_set_char:proto3_arena_unittest.TestAllTypes.repeated_bytes)
}
inline void TestAllTypes::set_repeated_bytes(int index, const void* value, size_t size) {
  repeated_bytes_.Mutable(index)->assign(
    reinterpret_cast<const char*>(value), size);
  // @@protoc_insertion_point(field_set_pointer:proto3_arena_unittest.TestAllTypes.repeated_bytes)
}
inline ::std::string* TestAllTypes::add_repeated_bytes() {
  // @@protoc_insertion_point(field_add_mutable:proto3_arena_unittest.TestAllTypes.repeated_bytes)
  return repeated_bytes_.Add();
}
inline void TestAllTypes::add_repeated_bytes(const ::std::string& value) {
  repeated_bytes_.Add()->assign(value);
  // @@protoc_insertion_point(field_add:proto3_arena_unittest.TestAllTypes.repeated_bytes)
}
inline void TestAllTypes::add_repeated_bytes(const char* value) {
  repeated_bytes_.Add()->assign(value);
  // @@protoc_insertion_point(field_add_char:proto3_arena_unittest.TestAllTypes.repeated_bytes)
}
inline void TestAllTypes::add_repeated_bytes(const void* value, size_t size) {
  repeated_bytes_.Add()->assign(reinterpret_cast<const char*>(value), size);
  // @@protoc_insertion_point(field_add_pointer:proto3_arena_unittest.TestAllTypes.repeated_bytes)
}
inline const ::google::protobuf::RepeatedPtrField< ::std::string>&
TestAllTypes::repeated_bytes() const {
  // @@protoc_insertion_point(field_list:proto3_arena_unittest.TestAllTypes.repeated_bytes)
  return repeated_bytes_;
}
inline ::google::protobuf::RepeatedPtrField< ::std::string>*
TestAllTypes::mutable_repeated_bytes() {
  // @@protoc_insertion_point(field_mutable_list:proto3_arena_unittest.TestAllTypes.repeated_bytes)
  return &repeated_bytes_;
}

// repeated .proto3_arena_unittest.TestAllTypes.NestedMessage repeated_nested_message = 48;
inline int TestAllTypes::repeated_nested_message_size() const {
  return repeated_nested_message_.size();
}
inline void TestAllTypes::clear_repeated_nested_message() {
  repeated_nested_message_.Clear();
}
inline const ::proto3_arena_unittest::TestAllTypes_NestedMessage& TestAllTypes::repeated_nested_message(int index) const {
  // @@protoc_insertion_point(field_get:proto3_arena_unittest.TestAllTypes.repeated_nested_message)
  return repeated_nested_message_.Get(index);
}
inline ::proto3_arena_unittest::TestAllTypes_NestedMessage* TestAllTypes::mutable_repeated_nested_message(int index) {
  // @@protoc_insertion_point(field_mutable:proto3_arena_unittest.TestAllTypes.repeated_nested_message)
  return repeated_nested_message_.Mutable(index);
}
inline ::proto3_arena_unittest::TestAllTypes_NestedMessage* TestAllTypes::add_repeated_nested_message() {
  // @@protoc_insertion_point(field_add:proto3_arena_unittest.TestAllTypes.repeated_nested_message)
  return repeated_nested_message_.Add();
}
inline ::google::protobuf::RepeatedPtrField< ::proto3_arena_unittest::TestAllTypes_NestedMessage >*
TestAllTypes::mutable_repeated_nested_message() {
  // @@protoc_insertion_point(field_mutable_list:proto3_arena_unittest.TestAllTypes.repeated_nested_message)
  return &repeated_nested_message_;
}
inline const ::google::protobuf::RepeatedPtrField< ::proto3_arena_unittest::TestAllTypes_NestedMessage >&
TestAllTypes::repeated_nested_message() const {
  // @@protoc_insertion_point(field_list:proto3_arena_unittest.TestAllTypes.repeated_nested_message)
  return repeated_nested_message_;
}

// repeated .proto3_arena_unittest.ForeignMessage repeated_foreign_message = 49;
inline int TestAllTypes::repeated_foreign_message_size() const {
  return repeated_foreign_message_.size();
}
inline void TestAllTypes::clear_repeated_foreign_message() {
  repeated_foreign_message_.Clear();
}
inline const ::proto3_arena_unittest::ForeignMessage& TestAllTypes::repeated_foreign_message(int index) const {
  // @@protoc_insertion_point(field_get:proto3_arena_unittest.TestAllTypes.repeated_foreign_message)
  return repeated_foreign_message_.Get(index);
}
inline ::proto3_arena_unittest::ForeignMessage* TestAllTypes::mutable_repeated_foreign_message(int index) {
  // @@protoc_insertion_point(field_mutable:proto3_arena_unittest.TestAllTypes.repeated_foreign_message)
  return repeated_foreign_message_.Mutable(index);
}
inline ::proto3_arena_unittest::ForeignMessage* TestAllTypes::add_repeated_foreign_message() {
  // @@protoc_insertion_point(field_add:proto3_arena_unittest.TestAllTypes.repeated_foreign_message)
  return repeated_foreign_message_.Add();
}
inline ::google::protobuf::RepeatedPtrField< ::proto3_arena_unittest::ForeignMessage >*
TestAllTypes::mutable_repeated_foreign_message() {
  // @@protoc_insertion_point(field_mutable_list:proto3_arena_unittest.TestAllTypes.repeated_foreign_message)
  return &repeated_foreign_message_;
}
inline const ::google::protobuf::RepeatedPtrField< ::proto3_arena_unittest::ForeignMessage >&
TestAllTypes::repeated_foreign_message() const {
  // @@protoc_insertion_point(field_list:proto3_arena_unittest.TestAllTypes.repeated_foreign_message)
  return repeated_foreign_message_;
}

// repeated .protobuf_unittest_import.ImportMessage repeated_import_message = 50;
inline int TestAllTypes::repeated_import_message_size() const {
  return repeated_import_message_.size();
}
inline void TestAllTypes::clear_repeated_import_message() {
  repeated_import_message_.Clear();
}
inline const ::protobuf_unittest_import::ImportMessage& TestAllTypes::repeated_import_message(int index) const {
  // @@protoc_insertion_point(field_get:proto3_arena_unittest.TestAllTypes.repeated_import_message)
  return repeated_import_message_.Get(index);
}
inline ::protobuf_unittest_import::ImportMessage* TestAllTypes::mutable_repeated_import_message(int index) {
  // @@protoc_insertion_point(field_mutable:proto3_arena_unittest.TestAllTypes.repeated_import_message)
  return repeated_import_message_.Mutable(index);
}
inline ::protobuf_unittest_import::ImportMessage* TestAllTypes::add_repeated_import_message() {
  // @@protoc_insertion_point(field_add:proto3_arena_unittest.TestAllTypes.repeated_import_message)
  return repeated_import_message_.Add();
}
inline ::google::protobuf::RepeatedPtrField< ::protobuf_unittest_import::ImportMessage >*
TestAllTypes::mutable_repeated_import_message() {
  // @@protoc_insertion_point(field_mutable_list:proto3_arena_unittest.TestAllTypes.repeated_import_message)
  return &repeated_import_message_;
}
inline const ::google::protobuf::RepeatedPtrField< ::protobuf_unittest_import::ImportMessage >&
TestAllTypes::repeated_import_message() const {
  // @@protoc_insertion_point(field_list:proto3_arena_unittest.TestAllTypes.repeated_import_message)
  return repeated_import_message_;
}

// repeated .proto3_arena_unittest.TestAllTypes.NestedEnum repeated_nested_enum = 51;
inline int TestAllTypes::repeated_nested_enum_size() const {
  return repeated_nested_enum_.size();
}
inline void TestAllTypes::clear_repeated_nested_enum() {
  repeated_nested_enum_.Clear();
}
inline ::proto3_arena_unittest::TestAllTypes_NestedEnum TestAllTypes::repeated_nested_enum(int index) const {
  // @@protoc_insertion_point(field_get:proto3_arena_unittest.TestAllTypes.repeated_nested_enum)
  return static_cast< ::proto3_arena_unittest::TestAllTypes_NestedEnum >(repeated_nested_enum_.Get(index));
}
inline void TestAllTypes::set_repeated_nested_enum(int index, ::proto3_arena_unittest::TestAllTypes_NestedEnum value) {
  repeated_nested_enum_.Set(index, value);
  // @@protoc_insertion_point(field_set:proto3_arena_unittest.TestAllTypes.repeated_nested_enum)
}
inline void TestAllTypes::add_repeated_nested_enum(::proto3_arena_unittest::TestAllTypes_NestedEnum value) {
  repeated_nested_enum_.Add(value);
  // @@protoc_insertion_point(field_add:proto3_arena_unittest.TestAllTypes.repeated_nested_enum)
}
inline const ::google::protobuf::RepeatedField<int>&
TestAllTypes::repeated_nested_enum() const {
  // @@protoc_insertion_point(field_list:proto3_arena_unittest.TestAllTypes.repeated_nested_enum)
  return repeated_nested_enum_;
}
inline ::google::protobuf::RepeatedField<int>*
TestAllTypes::mutable_repeated_nested_enum() {
  // @@protoc_insertion_point(field_mutable_list:proto3_arena_unittest.TestAllTypes.repeated_nested_enum)
  return &repeated_nested_enum_;
}

// repeated .proto3_arena_unittest.ForeignEnum repeated_foreign_enum = 52;
inline int TestAllTypes::repeated_foreign_enum_size() const {
  return repeated_foreign_enum_.size();
}
inline void TestAllTypes::clear_repeated_foreign_enum() {
  repeated_foreign_enum_.Clear();
}
inline ::proto3_arena_unittest::ForeignEnum TestAllTypes::repeated_foreign_enum(int index) const {
  // @@protoc_insertion_point(field_get:proto3_arena_unittest.TestAllTypes.repeated_foreign_enum)
  return static_cast< ::proto3_arena_unittest::ForeignEnum >(repeated_foreign_enum_.Get(index));
}
inline void TestAllTypes::set_repeated_foreign_enum(int index, ::proto3_arena_unittest::ForeignEnum value) {
  repeated_foreign_enum_.Set(index, value);
  // @@protoc_insertion_point(field_set:proto3_arena_unittest.TestAllTypes.repeated_foreign_enum)
}
inline void TestAllTypes::add_repeated_foreign_enum(::proto3_arena_unittest::ForeignEnum value) {
  repeated_foreign_enum_.Add(value);
  // @@protoc_insertion_point(field_add:proto3_arena_unittest.TestAllTypes.repeated_foreign_enum)
}
inline const ::google::protobuf::RepeatedField<int>&
TestAllTypes::repeated_foreign_enum() const {
  // @@protoc_insertion_point(field_list:proto3_arena_unittest.TestAllTypes.repeated_foreign_enum)
  return repeated_foreign_enum_;
}
inline ::google::protobuf::RepeatedField<int>*
TestAllTypes::mutable_repeated_foreign_enum() {
  // @@protoc_insertion_point(field_mutable_list:proto3_arena_unittest.TestAllTypes.repeated_foreign_enum)
  return &repeated_foreign_enum_;
}

// repeated string repeated_string_piece = 54 [ctype = STRING_PIECE];
inline int TestAllTypes::repeated_string_piece_size() const {
  return repeated_string_piece_.size();
}
inline void TestAllTypes::clear_repeated_string_piece() {
  repeated_string_piece_.Clear();
}
inline const ::std::string& TestAllTypes::repeated_string_piece(int index) const {
  // @@protoc_insertion_point(field_get:proto3_arena_unittest.TestAllTypes.repeated_string_piece)
  return repeated_string_piece_.Get(index);
}
inline ::std::string* TestAllTypes::mutable_repeated_string_piece(int index) {
  // @@protoc_insertion_point(field_mutable:proto3_arena_unittest.TestAllTypes.repeated_string_piece)
  return repeated_string_piece_.Mutable(index);
}
inline void TestAllTypes::set_repeated_string_piece(int index, const ::std::string& value) {
  // @@protoc_insertion_point(field_set:proto3_arena_unittest.TestAllTypes.repeated_string_piece)
  repeated_string_piece_.Mutable(index)->assign(value);
}
inline void TestAllTypes::set_repeated_string_piece(int index, const char* value) {
  repeated_string_piece_.Mutable(index)->assign(value);
  // @@protoc_insertion_point(field_set_char:proto3_arena_unittest.TestAllTypes.repeated_string_piece)
}
inline void TestAllTypes::set_repeated_string_piece(int index, const char* value, size_t size) {
  repeated_string_piece_.Mutable(index)->assign(
    reinterpret_cast<const char*>(value), size);
  // @@protoc_insertion_point(field_set_pointer:proto3_arena_unittest.TestAllTypes.repeated_string_piece)
}
inline ::std::string* TestAllTypes::add_repeated_string_piece() {
  // @@protoc_insertion_point(field_add_mutable:proto3_arena_unittest.TestAllTypes.repeated_string_piece)
  return repeated_string_piece_.Add();
}
inline void TestAllTypes::add_repeated_string_piece(const ::std::string& value) {
  repeated_string_piece_.Add()->assign(value);
  // @@protoc_insertion_point(field_add:proto3_arena_unittest.TestAllTypes.repeated_string_piece)
}
inline void TestAllTypes::add_repeated_string_piece(const char* value) {
  repeated_string_piece_.Add()->assign(value);
  // @@protoc_insertion_point(field_add_char:proto3_arena_unittest.TestAllTypes.repeated_string_piece)
}
inline void TestAllTypes::add_repeated_string_piece(const char* value, size_t size) {
  repeated_string_piece_.Add()->assign(reinterpret_cast<const char*>(value), size);
  // @@protoc_insertion_point(field_add_pointer:proto3_arena_unittest.TestAllTypes.repeated_string_piece)
}
inline const ::google::protobuf::RepeatedPtrField< ::std::string>&
TestAllTypes::repeated_string_piece() const {
  // @@protoc_insertion_point(field_list:proto3_arena_unittest.TestAllTypes.repeated_string_piece)
  return repeated_string_piece_;
}
inline ::google::protobuf::RepeatedPtrField< ::std::string>*
TestAllTypes::mutable_repeated_string_piece() {
  // @@protoc_insertion_point(field_mutable_list:proto3_arena_unittest.TestAllTypes.repeated_string_piece)
  return &repeated_string_piece_;
}

// repeated string repeated_cord = 55 [ctype = CORD];
inline int TestAllTypes::repeated_cord_size() const {
  return repeated_cord_.size();
}
inline void TestAllTypes::clear_repeated_cord() {
  repeated_cord_.Clear();
}
inline const ::std::string& TestAllTypes::repeated_cord(int index) const {
  // @@protoc_insertion_point(field_get:proto3_arena_unittest.TestAllTypes.repeated_cord)
  return repeated_cord_.Get(index);
}
inline ::std::string* TestAllTypes::mutable_repeated_cord(int index) {
  // @@protoc_insertion_point(field_mutable:proto3_arena_unittest.TestAllTypes.repeated_cord)
  return repeated_cord_.Mutable(index);
}
inline void TestAllTypes::set_repeated_cord(int index, const ::std::string& value) {
  // @@protoc_insertion_point(field_set:proto3_arena_unittest.TestAllTypes.repeated_cord)
  repeated_cord_.Mutable(index)->assign(value);
}
inline void TestAllTypes::set_repeated_cord(int index, const char* value) {
  repeated_cord_.Mutable(index)->assign(value);
  // @@protoc_insertion_point(field_set_char:proto3_arena_unittest.TestAllTypes.repeated_cord)
}
inline void TestAllTypes::set_repeated_cord(int index, const char* value, size_t size) {
  repeated_cord_.Mutable(index)->assign(
    reinterpret_cast<const char*>(value), size);
  // @@protoc_insertion_point(field_set_pointer:proto3_arena_unittest.TestAllTypes.repeated_cord)
}
inline ::std::string* TestAllTypes::add_repeated_cord() {
  // @@protoc_insertion_point(field_add_mutable:proto3_arena_unittest.TestAllTypes.repeated_cord)
  return repeated_cord_.Add();
}
inline void TestAllTypes::add_repeated_cord(const ::std::string& value) {
  repeated_cord_.Add()->assign(value);
  // @@protoc_insertion_point(field_add:proto3_arena_unittest.TestAllTypes.repeated_cord)
}
inline void TestAllTypes::add_repeated_cord(const char* value) {
  repeated_cord_.Add()->assign(value);
  // @@protoc_insertion_point(field_add_char:proto3_arena_unittest.TestAllTypes.repeated_cord)
}
inline void TestAllTypes::add_repeated_cord(const char* value, size_t size) {
  repeated_cord_.Add()->assign(reinterpret_cast<const char*>(value), size);
  // @@protoc_insertion_point(field_add_pointer:proto3_arena_unittest.TestAllTypes.repeated_cord)
}
inline const ::google::protobuf::RepeatedPtrField< ::std::string>&
TestAllTypes::repeated_cord() const {
  // @@protoc_insertion_point(field_list:proto3_arena_unittest.TestAllTypes.repeated_cord)
  return repeated_cord_;
}
inline ::google::protobuf::RepeatedPtrField< ::std::string>*
TestAllTypes::mutable_repeated_cord() {
  // @@protoc_insertion_point(field_mutable_list:proto3_arena_unittest.TestAllTypes.repeated_cord)
  return &repeated_cord_;
}

// repeated .proto3_arena_unittest.TestAllTypes.NestedMessage repeated_lazy_message = 57 [lazy = true];
inline int TestAllTypes::repeated_lazy_message_size() const {
  return repeated_lazy_message_.size();
}
inline void TestAllTypes::clear_repeated_lazy_message() {
  repeated_lazy_message_.Clear();
}
inline const ::proto3_arena_unittest::TestAllTypes_NestedMessage& TestAllTypes::repeated_lazy_message(int index) const {
  // @@protoc_insertion_point(field_get:proto3_arena_unittest.TestAllTypes.repeated_lazy_message)
  return repeated_lazy_message_.Get(index);
}
inline ::proto3_arena_unittest::TestAllTypes_NestedMessage* TestAllTypes::mutable_repeated_lazy_message(int index) {
  // @@protoc_insertion_point(field_mutable:proto3_arena_unittest.TestAllTypes.repeated_lazy_message)
  return repeated_lazy_message_.Mutable(index);
}
inline ::proto3_arena_unittest::TestAllTypes_NestedMessage* TestAllTypes::add_repeated_lazy_message() {
  // @@protoc_insertion_point(field_add:proto3_arena_unittest.TestAllTypes.repeated_lazy_message)
  return repeated_lazy_message_.Add();
}
inline ::google::protobuf::RepeatedPtrField< ::proto3_arena_unittest::TestAllTypes_NestedMessage >*
TestAllTypes::mutable_repeated_lazy_message() {
  // @@protoc_insertion_point(field_mutable_list:proto3_arena_unittest.TestAllTypes.repeated_lazy_message)
  return &repeated_lazy_message_;
}
inline const ::google::protobuf::RepeatedPtrField< ::proto3_arena_unittest::TestAllTypes_NestedMessage >&
TestAllTypes::repeated_lazy_message() const {
  // @@protoc_insertion_point(field_list:proto3_arena_unittest.TestAllTypes.repeated_lazy_message)
  return repeated_lazy_message_;
}

// optional uint32 oneof_uint32 = 111;
inline bool TestAllTypes::has_oneof_uint32() const {
  return oneof_field_case() == kOneofUint32;
}
inline void TestAllTypes::set_has_oneof_uint32() {
  _oneof_case_[0] = kOneofUint32;
}
inline void TestAllTypes::clear_oneof_uint32() {
  if (has_oneof_uint32()) {
    oneof_field_.oneof_uint32_ = 0u;
    clear_has_oneof_field();
  }
}
inline ::google::protobuf::uint32 TestAllTypes::oneof_uint32() const {
  // @@protoc_insertion_point(field_get:proto3_arena_unittest.TestAllTypes.oneof_uint32)
  if (has_oneof_uint32()) {
    return oneof_field_.oneof_uint32_;
  }
  return 0u;
}
inline void TestAllTypes::set_oneof_uint32(::google::protobuf::uint32 value) {
  if (!has_oneof_uint32()) {
    clear_oneof_field();
    set_has_oneof_uint32();
  }
  oneof_field_.oneof_uint32_ = value;
  // @@protoc_insertion_point(field_set:proto3_arena_unittest.TestAllTypes.oneof_uint32)
}

// optional .proto3_arena_unittest.TestAllTypes.NestedMessage oneof_nested_message = 112;
inline bool TestAllTypes::has_oneof_nested_message() const {
  return oneof_field_case() == kOneofNestedMessage;
}
inline void TestAllTypes::set_has_oneof_nested_message() {
  _oneof_case_[0] = kOneofNestedMessage;
}
inline void TestAllTypes::clear_oneof_nested_message() {
  if (has_oneof_nested_message()) {
    if (GetArenaNoVirtual() == NULL) {
      delete oneof_field_.oneof_nested_message_;
    }
    clear_has_oneof_field();
  }
}
inline  const ::proto3_arena_unittest::TestAllTypes_NestedMessage& TestAllTypes::oneof_nested_message() const {
  // @@protoc_insertion_point(field_get:proto3_arena_unittest.TestAllTypes.oneof_nested_message)
  return has_oneof_nested_message()
      ? *oneof_field_.oneof_nested_message_
      : ::proto3_arena_unittest::TestAllTypes_NestedMessage::default_instance();
}
inline ::proto3_arena_unittest::TestAllTypes_NestedMessage* TestAllTypes::mutable_oneof_nested_message() {
  if (!has_oneof_nested_message()) {
    clear_oneof_field();
    set_has_oneof_nested_message();
    oneof_field_.oneof_nested_message_ = 
      ::google::protobuf::Arena::CreateMessage< ::proto3_arena_unittest::TestAllTypes_NestedMessage >(
      GetArenaNoVirtual());
  }
  // @@protoc_insertion_point(field_mutable:proto3_arena_unittest.TestAllTypes.oneof_nested_message)
  return oneof_field_.oneof_nested_message_;
}
inline ::proto3_arena_unittest::TestAllTypes_NestedMessage* TestAllTypes::release_oneof_nested_message() {
  // @@protoc_insertion_point(field_release:proto3_arena_unittest.TestAllTypes.oneof_nested_message)
  if (has_oneof_nested_message()) {
    clear_has_oneof_field();
    if (GetArenaNoVirtual() != NULL) {
      ::proto3_arena_unittest::TestAllTypes_NestedMessage* temp = new ::proto3_arena_unittest::TestAllTypes_NestedMessage(*oneof_field_.oneof_nested_message_);
      oneof_field_.oneof_nested_message_ = NULL;
      return temp;
    } else {
      ::proto3_arena_unittest::TestAllTypes_NestedMessage* temp = oneof_field_.oneof_nested_message_;
      oneof_field_.oneof_nested_message_ = NULL;
      return temp;
    }
  } else {
    return NULL;
  }
}
inline void TestAllTypes::set_allocated_oneof_nested_message(::proto3_arena_unittest::TestAllTypes_NestedMessage* oneof_nested_message) {
  clear_oneof_field();
  if (oneof_nested_message) {
    if (GetArenaNoVirtual() != NULL &&
        ::google::protobuf::Arena::GetArena(oneof_nested_message) == NULL) {
      GetArenaNoVirtual()->Own(oneof_nested_message);
    } else if (GetArenaNoVirtual() !=
               ::google::protobuf::Arena::GetArena(oneof_nested_message)) {
      ::proto3_arena_unittest::TestAllTypes_NestedMessage* new_oneof_nested_message = 
          ::google::protobuf::Arena::CreateMessage< ::proto3_arena_unittest::TestAllTypes_NestedMessage >(
          GetArenaNoVirtual());
      new_oneof_nested_message->CopyFrom(*oneof_nested_message);
      oneof_nested_message = new_oneof_nested_message;
    }
    set_has_oneof_nested_message();
    oneof_field_.oneof_nested_message_ = oneof_nested_message;
  }
  // @@protoc_insertion_point(field_set_allocated:proto3_arena_unittest.TestAllTypes.oneof_nested_message)
}
inline  ::proto3_arena_unittest::TestAllTypes_NestedMessage* TestAllTypes::unsafe_arena_release_oneof_nested_message() {
  // @@protoc_insertion_point(field_unsafe_arena_release:proto3_arena_unittest.TestAllTypes.oneof_nested_message)
  if (has_oneof_nested_message()) {
    clear_has_oneof_field();
    ::proto3_arena_unittest::TestAllTypes_NestedMessage* temp = oneof_field_.oneof_nested_message_;
    oneof_field_.oneof_nested_message_ = NULL;
    return temp;
  } else {
    return NULL;
  }
}
inline  void TestAllTypes::unsafe_arena_set_allocated_oneof_nested_message(::proto3_arena_unittest::TestAllTypes_NestedMessage* oneof_nested_message) {
  clear_oneof_field();
  if (oneof_nested_message) {
    set_has_oneof_nested_message();
    oneof_field_.oneof_nested_message_ = oneof_nested_message;
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:proto3_arena_unittest.TestAllTypes.oneof_nested_message)
}

// optional string oneof_string = 113;
inline bool TestAllTypes::has_oneof_string() const {
  return oneof_field_case() == kOneofString;
}
inline void TestAllTypes::set_has_oneof_string() {
  _oneof_case_[0] = kOneofString;
}
inline void TestAllTypes::clear_oneof_string() {
  if (has_oneof_string()) {
    oneof_field_.oneof_string_.Destroy(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
        GetArenaNoVirtual());
    clear_has_oneof_field();
  }
}
inline const ::std::string& TestAllTypes::oneof_string() const {
  // @@protoc_insertion_point(field_get:proto3_arena_unittest.TestAllTypes.oneof_string)
  if (has_oneof_string()) {
    return oneof_field_.oneof_string_.Get(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  }
  return *&::google::protobuf::internal::GetEmptyStringAlreadyInited();
}
inline void TestAllTypes::set_oneof_string(const ::std::string& value) {
  if (!has_oneof_string()) {
    clear_oneof_field();
    set_has_oneof_string();
    oneof_field_.oneof_string_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  }
  oneof_field_.oneof_string_.Set(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value,
      GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set:proto3_arena_unittest.TestAllTypes.oneof_string)
}
inline void TestAllTypes::set_oneof_string(const char* value) {
  if (!has_oneof_string()) {
    clear_oneof_field();
    set_has_oneof_string();
    oneof_field_.oneof_string_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  }
  oneof_field_.oneof_string_.Set(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(value), GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_char:proto3_arena_unittest.TestAllTypes.oneof_string)
}
inline void TestAllTypes::set_oneof_string(const char* value,
                             size_t size) {
  if (!has_oneof_string()) {
    clear_oneof_field();
    set_has_oneof_string();
    oneof_field_.oneof_string_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  }
  oneof_field_.oneof_string_.Set(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(
      reinterpret_cast<const char*>(value), size),
      GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_pointer:proto3_arena_unittest.TestAllTypes.oneof_string)
}
inline ::std::string* TestAllTypes::mutable_oneof_string() {
  if (!has_oneof_string()) {
    clear_oneof_field();
    set_has_oneof_string();
    oneof_field_.oneof_string_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  }
  return oneof_field_.oneof_string_.Mutable(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      GetArenaNoVirtual());
  // @@protoc_insertion_point(field_mutable:proto3_arena_unittest.TestAllTypes.oneof_string)
}
inline ::std::string* TestAllTypes::release_oneof_string() {
  // @@protoc_insertion_point(field_release:proto3_arena_unittest.TestAllTypes.oneof_string)
  if (has_oneof_string()) {
    clear_has_oneof_field();
    return oneof_field_.oneof_string_.Release(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
        GetArenaNoVirtual());
  } else {
    return NULL;
  }
}
inline ::std::string* TestAllTypes::unsafe_arena_release_oneof_string() {
  // @@protoc_insertion_point(field_unsafe_arena_release:proto3_arena_unittest.TestAllTypes.oneof_string)
  GOOGLE_DCHECK(GetArenaNoVirtual() != NULL);
  if (has_oneof_string()) {
    clear_has_oneof_field();
    return oneof_field_.oneof_string_.UnsafeArenaRelease(
        &::google::protobuf::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
  } else {
    return NULL;
  }
}
inline void TestAllTypes::set_allocated_oneof_string(::std::string* oneof_string) {
  if (!has_oneof_string()) {
    oneof_field_.oneof_string_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  }
  clear_oneof_field();
  if (oneof_string != NULL) {
    set_has_oneof_string();
    oneof_field_.oneof_string_.SetAllocated(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), oneof_string,
        GetArenaNoVirtual());
  }
  // @@protoc_insertion_point(field_set_allocated:proto3_arena_unittest.TestAllTypes.oneof_string)
}
inline void TestAllTypes::unsafe_arena_set_allocated_oneof_string(::std::string* oneof_string) {
  GOOGLE_DCHECK(GetArenaNoVirtual() != NULL);
  if (!has_oneof_string()) {
    oneof_field_.oneof_string_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  }
  clear_oneof_field();
  if (oneof_string) {
    set_has_oneof_string();
    oneof_field_.oneof_string_.UnsafeArenaSetAllocated(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), oneof_string, GetArenaNoVirtual());
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:proto3_arena_unittest.TestAllTypes.oneof_string)
}

// optional bytes oneof_bytes = 114;
inline bool TestAllTypes::has_oneof_bytes() const {
  return oneof_field_case() == kOneofBytes;
}
inline void TestAllTypes::set_has_oneof_bytes() {
  _oneof_case_[0] = kOneofBytes;
}
inline void TestAllTypes::clear_oneof_bytes() {
  if (has_oneof_bytes()) {
    oneof_field_.oneof_bytes_.Destroy(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
        GetArenaNoVirtual());
    clear_has_oneof_field();
  }
}
inline const ::std::string& TestAllTypes::oneof_bytes() const {
  // @@protoc_insertion_point(field_get:proto3_arena_unittest.TestAllTypes.oneof_bytes)
  if (has_oneof_bytes()) {
    return oneof_field_.oneof_bytes_.Get(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  }
  return *&::google::protobuf::internal::GetEmptyStringAlreadyInited();
}
inline void TestAllTypes::set_oneof_bytes(const ::std::string& value) {
  if (!has_oneof_bytes()) {
    clear_oneof_field();
    set_has_oneof_bytes();
    oneof_field_.oneof_bytes_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  }
  oneof_field_.oneof_bytes_.Set(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value,
      GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set:proto3_arena_unittest.TestAllTypes.oneof_bytes)
}
inline void TestAllTypes::set_oneof_bytes(const char* value) {
  if (!has_oneof_bytes()) {
    clear_oneof_field();
    set_has_oneof_bytes();
    oneof_field_.oneof_bytes_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  }
  oneof_field_.oneof_bytes_.Set(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(value), GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_char:proto3_arena_unittest.TestAllTypes.oneof_bytes)
}
inline void TestAllTypes::set_oneof_bytes(const void* value,
                             size_t size) {
  if (!has_oneof_bytes()) {
    clear_oneof_field();
    set_has_oneof_bytes();
    oneof_field_.oneof_bytes_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  }
  oneof_field_.oneof_bytes_.Set(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(
      reinterpret_cast<const char*>(value), size),
      GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_pointer:proto3_arena_unittest.TestAllTypes.oneof_bytes)
}
inline ::std::string* TestAllTypes::mutable_oneof_bytes() {
  if (!has_oneof_bytes()) {
    clear_oneof_field();
    set_has_oneof_bytes();
    oneof_field_.oneof_bytes_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  }
  return oneof_field_.oneof_bytes_.Mutable(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      GetArenaNoVirtual());
  // @@protoc_insertion_point(field_mutable:proto3_arena_unittest.TestAllTypes.oneof_bytes)
}
inline ::std::string* TestAllTypes::release_oneof_bytes() {
  // @@protoc_insertion_point(field_release:proto3_arena_unittest.TestAllTypes.oneof_bytes)
  if (has_oneof_bytes()) {
    clear_has_oneof_field();
    return oneof_field_.oneof_bytes_.Release(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
        GetArenaNoVirtual());
  } else {
    return NULL;
  }
}
inline ::std::string* TestAllTypes::unsafe_arena_release_oneof_bytes() {
  // @@protoc_insertion_point(field_unsafe_arena_release:proto3_arena_unittest.TestAllTypes.oneof_bytes)
  GOOGLE_DCHECK(GetArenaNoVirtual() != NULL);
  if (has_oneof_bytes()) {
    clear_has_oneof_field();
    return oneof_field_.oneof_bytes_.UnsafeArenaRelease(
        &::google::protobuf::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
  } else {
    return NULL;
  }
}
inline void TestAllTypes::set_allocated_oneof_bytes(::std::string* oneof_bytes) {
  if (!has_oneof_bytes()) {
    oneof_field_.oneof_bytes_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  }
  clear_oneof_field();
  if (oneof_bytes != NULL) {
    set_has_oneof_bytes();
    oneof_field_.oneof_bytes_.SetAllocated(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), oneof_bytes,
        GetArenaNoVirtual());
  }
  // @@protoc_insertion_point(field_set_allocated:proto3_arena_unittest.TestAllTypes.oneof_bytes)
}
inline void TestAllTypes::unsafe_arena_set_allocated_oneof_bytes(::std::string* oneof_bytes) {
  GOOGLE_DCHECK(GetArenaNoVirtual() != NULL);
  if (!has_oneof_bytes()) {
    oneof_field_.oneof_bytes_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  }
  clear_oneof_field();
  if (oneof_bytes) {
    set_has_oneof_bytes();
    oneof_field_.oneof_bytes_.UnsafeArenaSetAllocated(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), oneof_bytes, GetArenaNoVirtual());
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:proto3_arena_unittest.TestAllTypes.oneof_bytes)
}

inline bool TestAllTypes::has_oneof_field() const {
  return oneof_field_case() != ONEOF_FIELD_NOT_SET;
}
inline void TestAllTypes::clear_has_oneof_field() {
  _oneof_case_[0] = ONEOF_FIELD_NOT_SET;
}
inline TestAllTypes::OneofFieldCase TestAllTypes::oneof_field_case() const {
  return TestAllTypes::OneofFieldCase(_oneof_case_[0]);
}
inline const TestAllTypes* TestAllTypes::internal_default_instance() {
  return &TestAllTypes_default_instance_.get();
}
// -------------------------------------------------------------------

// TestPackedTypes

// repeated int32 packed_int32 = 90 [packed = true];
inline int TestPackedTypes::packed_int32_size() const {
  return packed_int32_.size();
}
inline void TestPackedTypes::clear_packed_int32() {
  packed_int32_.Clear();
}
inline ::google::protobuf::int32 TestPackedTypes::packed_int32(int index) const {
  // @@protoc_insertion_point(field_get:proto3_arena_unittest.TestPackedTypes.packed_int32)
  return packed_int32_.Get(index);
}
inline void TestPackedTypes::set_packed_int32(int index, ::google::protobuf::int32 value) {
  packed_int32_.Set(index, value);
  // @@protoc_insertion_point(field_set:proto3_arena_unittest.TestPackedTypes.packed_int32)
}
inline void TestPackedTypes::add_packed_int32(::google::protobuf::int32 value) {
  packed_int32_.Add(value);
  // @@protoc_insertion_point(field_add:proto3_arena_unittest.TestPackedTypes.packed_int32)
}
inline const ::google::protobuf::RepeatedField< ::google::protobuf::int32 >&
TestPackedTypes::packed_int32() const {
  // @@protoc_insertion_point(field_list:proto3_arena_unittest.TestPackedTypes.packed_int32)
  return packed_int32_;
}
inline ::google::protobuf::RepeatedField< ::google::protobuf::int32 >*
TestPackedTypes::mutable_packed_int32() {
  // @@protoc_insertion_point(field_mutable_list:proto3_arena_unittest.TestPackedTypes.packed_int32)
  return &packed_int32_;
}

// repeated int64 packed_int64 = 91 [packed = true];
inline int TestPackedTypes::packed_int64_size() const {
  return packed_int64_.size();
}
inline void TestPackedTypes::clear_packed_int64() {
  packed_int64_.Clear();
}
inline ::google::protobuf::int64 TestPackedTypes::packed_int64(int index) const {
  // @@protoc_insertion_point(field_get:proto3_arena_unittest.TestPackedTypes.packed_int64)
  return packed_int64_.Get(index);
}
inline void TestPackedTypes::set_packed_int64(int index, ::google::protobuf::int64 value) {
  packed_int64_.Set(index, value);
  // @@protoc_insertion_point(field_set:proto3_arena_unittest.TestPackedTypes.packed_int64)
}
inline void TestPackedTypes::add_packed_int64(::google::protobuf::int64 value) {
  packed_int64_.Add(value);
  // @@protoc_insertion_point(field_add:proto3_arena_unittest.TestPackedTypes.packed_int64)
}
inline const ::google::protobuf::RepeatedField< ::google::protobuf::int64 >&
TestPackedTypes::packed_int64() const {
  // @@protoc_insertion_point(field_list:proto3_arena_unittest.TestPackedTypes.packed_int64)
  return packed_int64_;
}
inline ::google::protobuf::RepeatedField< ::google::protobuf::int64 >*
TestPackedTypes::mutable_packed_int64() {
  // @@protoc_insertion_point(field_mutable_list:proto3_arena_unittest.TestPackedTypes.packed_int64)
  return &packed_int64_;
}

// repeated uint32 packed_uint32 = 92 [packed = true];
inline int TestPackedTypes::packed_uint32_size() const {
  return packed_uint32_.size();
}
inline void TestPackedTypes::clear_packed_uint32() {
  packed_uint32_.Clear();
}
inline ::google::protobuf::uint32 TestPackedTypes::packed_uint32(int index) const {
  // @@protoc_insertion_point(field_get:proto3_arena_unittest.TestPackedTypes.packed_uint32)
  return packed_uint32_.Get(index);
}
inline void TestPackedTypes::set_packed_uint32(int index, ::google::protobuf::uint32 value) {
  packed_uint32_.Set(index, value);
  // @@protoc_insertion_point(field_set:proto3_arena_unittest.TestPackedTypes.packed_uint32)
}
inline void TestPackedTypes::add_packed_uint32(::google::protobuf::uint32 value) {
  packed_uint32_.Add(value);
  // @@protoc_insertion_point(field_add:proto3_arena_unittest.TestPackedTypes.packed_uint32)
}
inline const ::google::protobuf::RepeatedField< ::google::protobuf::uint32 >&
TestPackedTypes::packed_uint32() const {
  // @@protoc_insertion_point(field_list:proto3_arena_unittest.TestPackedTypes.packed_uint32)
  return packed_uint32_;
}
inline ::google::protobuf::RepeatedField< ::google::protobuf::uint32 >*
TestPackedTypes::mutable_packed_uint32() {
  // @@protoc_insertion_point(field_mutable_list:proto3_arena_unittest.TestPackedTypes.packed_uint32)
  return &packed_uint32_;
}

// repeated uint64 packed_uint64 = 93 [packed = true];
inline int TestPackedTypes::packed_uint64_size() const {
  return packed_uint64_.size();
}
inline void TestPackedTypes::clear_packed_uint64() {
  packed_uint64_.Clear();
}
inline ::google::protobuf::uint64 TestPackedTypes::packed_uint64(int index) const {
  // @@protoc_insertion_point(field_get:proto3_arena_unittest.TestPackedTypes.packed_uint64)
  return packed_uint64_.Get(index);
}
inline void TestPackedTypes::set_packed_uint64(int index, ::google::protobuf::uint64 value) {
  packed_uint64_.Set(index, value);
  // @@protoc_insertion_point(field_set:proto3_arena_unittest.TestPackedTypes.packed_uint64)
}
inline void TestPackedTypes::add_packed_uint64(::google::protobuf::uint64 value) {
  packed_uint64_.Add(value);
  // @@protoc_insertion_point(field_add:proto3_arena_unittest.TestPackedTypes.packed_uint64)
}
inline const ::google::protobuf::RepeatedField< ::google::protobuf::uint64 >&
TestPackedTypes::packed_uint64() const {
  // @@protoc_insertion_point(field_list:proto3_arena_unittest.TestPackedTypes.packed_uint64)
  return packed_uint64_;
}
inline ::google::protobuf::RepeatedField< ::google::protobuf::uint64 >*
TestPackedTypes::mutable_packed_uint64() {
  // @@protoc_insertion_point(field_mutable_list:proto3_arena_unittest.TestPackedTypes.packed_uint64)
  return &packed_uint64_;
}

// repeated sint32 packed_sint32 = 94 [packed = true];
inline int TestPackedTypes::packed_sint32_size() const {
  return packed_sint32_.size();
}
inline void TestPackedTypes::clear_packed_sint32() {
  packed_sint32_.Clear();
}
inline ::google::protobuf::int32 TestPackedTypes::packed_sint32(int index) const {
  // @@protoc_insertion_point(field_get:proto3_arena_unittest.TestPackedTypes.packed_sint32)
  return packed_sint32_.Get(index);
}
inline void TestPackedTypes::set_packed_sint32(int index, ::google::protobuf::int32 value) {
  packed_sint32_.Set(index, value);
  // @@protoc_insertion_point(field_set:proto3_arena_unittest.TestPackedTypes.packed_sint32)
}
inline void TestPackedTypes::add_packed_sint32(::google::protobuf::int32 value) {
  packed_sint32_.Add(value);
  // @@protoc_insertion_point(field_add:proto3_arena_unittest.TestPackedTypes.packed_sint32)
}
inline const ::google::protobuf::RepeatedField< ::google::protobuf::int32 >&
TestPackedTypes::packed_sint32() const {
  // @@protoc_insertion_point(field_list:proto3_arena_unittest.TestPackedTypes.packed_sint32)
  return packed_sint32_;
}
inline ::google::protobuf::RepeatedField< ::google::protobuf::int32 >*
TestPackedTypes::mutable_packed_sint32() {
  // @@protoc_insertion_point(field_mutable_list:proto3_arena_unittest.TestPackedTypes.packed_sint32)
  return &packed_sint32_;
}

// repeated sint64 packed_sint64 = 95 [packed = true];
inline int TestPackedTypes::packed_sint64_size() const {
  return packed_sint64_.size();
}
inline void TestPackedTypes::clear_packed_sint64() {
  packed_sint64_.Clear();
}
inline ::google::protobuf::int64 TestPackedTypes::packed_sint64(int index) const {
  // @@protoc_insertion_point(field_get:proto3_arena_unittest.TestPackedTypes.packed_sint64)
  return packed_sint64_.Get(index);
}
inline void TestPackedTypes::set_packed_sint64(int index, ::google::protobuf::int64 value) {
  packed_sint64_.Set(index, value);
  // @@protoc_insertion_point(field_set:proto3_arena_unittest.TestPackedTypes.packed_sint64)
}
inline void TestPackedTypes::add_packed_sint64(::google::protobuf::int64 value) {
  packed_sint64_.Add(value);
  // @@protoc_insertion_point(field_add:proto3_arena_unittest.TestPackedTypes.packed_sint64)
}
inline const ::google::protobuf::RepeatedField< ::google::protobuf::int64 >&
TestPackedTypes::packed_sint64() const {
  // @@protoc_insertion_point(field_list:proto3_arena_unittest.TestPackedTypes.packed_sint64)
  return packed_sint64_;
}
inline ::google::protobuf::RepeatedField< ::google::protobuf::int64 >*
TestPackedTypes::mutable_packed_sint64() {
  // @@protoc_insertion_point(field_mutable_list:proto3_arena_unittest.TestPackedTypes.packed_sint64)
  return &packed_sint64_;
}

// repeated fixed32 packed_fixed32 = 96 [packed = true];
inline int TestPackedTypes::packed_fixed32_size() const {
  return packed_fixed32_.size();
}
inline void TestPackedTypes::clear_packed_fixed32() {
  packed_fixed32_.Clear();
}
inline ::google::protobuf::uint32 TestPackedTypes::packed_fixed32(int index) const {
  // @@protoc_insertion_point(field_get:proto3_arena_unittest.TestPackedTypes.packed_fixed32)
  return packed_fixed32_.Get(index);
}
inline void TestPackedTypes::set_packed_fixed32(int index, ::google::protobuf::uint32 value) {
  packed_fixed32_.Set(index, value);
  // @@protoc_insertion_point(field_set:proto3_arena_unittest.TestPackedTypes.packed_fixed32)
}
inline void TestPackedTypes::add_packed_fixed32(::google::protobuf::uint32 value) {
  packed_fixed32_.Add(value);
  // @@protoc_insertion_point(field_add:proto3_arena_unittest.TestPackedTypes.packed_fixed32)
}
inline const ::google::protobuf::RepeatedField< ::google::protobuf::uint32 >&
TestPackedTypes::packed_fixed32() const {
  // @@protoc_insertion_point(field_list:proto3_arena_unittest.TestPackedTypes.packed_fixed32)
  return packed_fixed32_;
}
inline ::google::protobuf::RepeatedField< ::google::protobuf::uint32 >*
TestPackedTypes::mutable_packed_fixed32() {
  // @@protoc_insertion_point(field_mutable_list:proto3_arena_unittest.TestPackedTypes.packed_fixed32)
  return &packed_fixed32_;
}

// repeated fixed64 packed_fixed64 = 97 [packed = true];
inline int TestPackedTypes::packed_fixed64_size() const {
  return packed_fixed64_.size();
}
inline void TestPackedTypes::clear_packed_fixed64() {
  packed_fixed64_.Clear();
}
inline ::google::protobuf::uint64 TestPackedTypes::packed_fixed64(int index) const {
  // @@protoc_insertion_point(field_get:proto3_arena_unittest.TestPackedTypes.packed_fixed64)
  return packed_fixed64_.Get(index);
}
inline void TestPackedTypes::set_packed_fixed64(int index, ::google::protobuf::uint64 value) {
  packed_fixed64_.Set(index, value);
  // @@protoc_insertion_point(field_set:proto3_arena_unittest.TestPackedTypes.packed_fixed64)
}
inline void TestPackedTypes::add_packed_fixed64(::google::protobuf::uint64 value) {
  packed_fixed64_.Add(value);
  // @@protoc_insertion_point(field_add:proto3_arena_unittest.TestPackedTypes.packed_fixed64)
}
inline const ::google::protobuf::RepeatedField< ::google::protobuf::uint64 >&
TestPackedTypes::packed_fixed64() const {
  // @@protoc_insertion_point(field_list:proto3_arena_unittest.TestPackedTypes.packed_fixed64)
  return packed_fixed64_;
}
inline ::google::protobuf::RepeatedField< ::google::protobuf::uint64 >*
TestPackedTypes::mutable_packed_fixed64() {
  // @@protoc_insertion_point(field_mutable_list:proto3_arena_unittest.TestPackedTypes.packed_fixed64)
  return &packed_fixed64_;
}

// repeated sfixed32 packed_sfixed32 = 98 [packed = true];
inline int TestPackedTypes::packed_sfixed32_size() const {
  return packed_sfixed32_.size();
}
inline void TestPackedTypes::clear_packed_sfixed32() {
  packed_sfixed32_.Clear();
}
inline ::google::protobuf::int32 TestPackedTypes::packed_sfixed32(int index) const {
  // @@protoc_insertion_point(field_get:proto3_arena_unittest.TestPackedTypes.packed_sfixed32)
  return packed_sfixed32_.Get(index);
}
inline void TestPackedTypes::set_packed_sfixed32(int index, ::google::protobuf::int32 value) {
  packed_sfixed32_.Set(index, value);
  // @@protoc_insertion_point(field_set:proto3_arena_unittest.TestPackedTypes.packed_sfixed32)
}
inline void TestPackedTypes::add_packed_sfixed32(::google::protobuf::int32 value) {
  packed_sfixed32_.Add(value);
  // @@protoc_insertion_point(field_add:proto3_arena_unittest.TestPackedTypes.packed_sfixed32)
}
inline const ::google::protobuf::RepeatedField< ::google::protobuf::int32 >&
TestPackedTypes::packed_sfixed32() const {
  // @@protoc_insertion_point(field_list:proto3_arena_unittest.TestPackedTypes.packed_sfixed32)
  return packed_sfixed32_;
}
inline ::google::protobuf::RepeatedField< ::google::protobuf::int32 >*
TestPackedTypes::mutable_packed_sfixed32() {
  // @@protoc_insertion_point(field_mutable_list:proto3_arena_unittest.TestPackedTypes.packed_sfixed32)
  return &packed_sfixed32_;
}

// repeated sfixed64 packed_sfixed64 = 99 [packed = true];
inline int TestPackedTypes::packed_sfixed64_size() const {
  return packed_sfixed64_.size();
}
inline void TestPackedTypes::clear_packed_sfixed64() {
  packed_sfixed64_.Clear();
}
inline ::google::protobuf::int64 TestPackedTypes::packed_sfixed64(int index) const {
  // @@protoc_insertion_point(field_get:proto3_arena_unittest.TestPackedTypes.packed_sfixed64)
  return packed_sfixed64_.Get(index);
}
inline void TestPackedTypes::set_packed_sfixed64(int index, ::google::protobuf::int64 value) {
  packed_sfixed64_.Set(index, value);
  // @@protoc_insertion_point(field_set:proto3_arena_unittest.TestPackedTypes.packed_sfixed64)
}
inline void TestPackedTypes::add_packed_sfixed64(::google::protobuf::int64 value) {
  packed_sfixed64_.Add(value);
  // @@protoc_insertion_point(field_add:proto3_arena_unittest.TestPackedTypes.packed_sfixed64)
}
inline const ::google::protobuf::RepeatedField< ::google::protobuf::int64 >&
TestPackedTypes::packed_sfixed64() const {
  // @@protoc_insertion_point(field_list:proto3_arena_unittest.TestPackedTypes.packed_sfixed64)
  return packed_sfixed64_;
}
inline ::google::protobuf::RepeatedField< ::google::protobuf::int64 >*
TestPackedTypes::mutable_packed_sfixed64() {
  // @@protoc_insertion_point(field_mutable_list:proto3_arena_unittest.TestPackedTypes.packed_sfixed64)
  return &packed_sfixed64_;
}

// repeated float packed_float = 100 [packed = true];
inline int TestPackedTypes::packed_float_size() const {
  return packed_float_.size();
}
inline void TestPackedTypes::clear_packed_float() {
  packed_float_.Clear();
}
inline float TestPackedTypes::packed_float(int index) const {
  // @@protoc_insertion_point(field_get:proto3_arena_unittest.TestPackedTypes.packed_float)
  return packed_float_.Get(index);
}
inline void TestPackedTypes::set_packed_float(int index, float value) {
  packed_float_.Set(index, value);
  // @@protoc_insertion_point(field_set:proto3_arena_unittest.TestPackedTypes.packed_float)
}
inline void TestPackedTypes::add_packed_float(float value) {
  packed_float_.Add(value);
  // @@protoc_insertion_point(field_add:proto3_arena_unittest.TestPackedTypes.packed_float)
}
inline const ::google::protobuf::RepeatedField< float >&
TestPackedTypes::packed_float() const {
  // @@protoc_insertion_point(field_list:proto3_arena_unittest.TestPackedTypes.packed_float)
  return packed_float_;
}
inline ::google::protobuf::RepeatedField< float >*
TestPackedTypes::mutable_packed_float() {
  // @@protoc_insertion_point(field_mutable_list:proto3_arena_unittest.TestPackedTypes.packed_float)
  return &packed_float_;
}

// repeated double packed_double = 101 [packed = true];
inline int TestPackedTypes::packed_double_size() const {
  return packed_double_.size();
}
inline void TestPackedTypes::clear_packed_double() {
  packed_double_.Clear();
}
inline double TestPackedTypes::packed_double(int index) const {
  // @@protoc_insertion_point(field_get:proto3_arena_unittest.TestPackedTypes.packed_double)
  return packed_double_.Get(index);
}
inline void TestPackedTypes::set_packed_double(int index, double value) {
  packed_double_.Set(index, value);
  // @@protoc_insertion_point(field_set:proto3_arena_unittest.TestPackedTypes.packed_double)
}
inline void TestPackedTypes::add_packed_double(double value) {
  packed_double_.Add(value);
  // @@protoc_insertion_point(field_add:proto3_arena_unittest.TestPackedTypes.packed_double)
}
inline const ::google::protobuf::RepeatedField< double >&
TestPackedTypes::packed_double() const {
  // @@protoc_insertion_point(field_list:proto3_arena_unittest.TestPackedTypes.packed_double)
  return packed_double_;
}
inline ::google::protobuf::RepeatedField< double >*
TestPackedTypes::mutable_packed_double() {
  // @@protoc_insertion_point(field_mutable_list:proto3_arena_unittest.TestPackedTypes.packed_double)
  return &packed_double_;
}

// repeated bool packed_bool = 102 [packed = true];
inline int TestPackedTypes::packed_bool_size() const {
  return packed_bool_.size();
}
inline void TestPackedTypes::clear_packed_bool() {
  packed_bool_.Clear();
}
inline bool TestPackedTypes::packed_bool(int index) const {
  // @@protoc_insertion_point(field_get:proto3_arena_unittest.TestPackedTypes.packed_bool)
  return packed_bool_.Get(index);
}
inline void TestPackedTypes::set_packed_bool(int index, bool value) {
  packed_bool_.Set(index, value);
  // @@protoc_insertion_point(field_set:proto3_arena_unittest.TestPackedTypes.packed_bool)
}
inline void TestPackedTypes::add_packed_bool(bool value) {
  packed_bool_.Add(value);
  // @@protoc_insertion_point(field_add:proto3_arena_unittest.TestPackedTypes.packed_bool)
}
inline const ::google::protobuf::RepeatedField< bool >&
TestPackedTypes::packed_bool() const {
  // @@protoc_insertion_point(field_list:proto3_arena_unittest.TestPackedTypes.packed_bool)
  return packed_bool_;
}
inline ::google::protobuf::RepeatedField< bool >*
TestPackedTypes::mutable_packed_bool() {
  // @@protoc_insertion_point(field_mutable_list:proto3_arena_unittest.TestPackedTypes.packed_bool)
  return &packed_bool_;
}

// repeated .proto3_arena_unittest.ForeignEnum packed_enum = 103 [packed = true];
inline int TestPackedTypes::packed_enum_size() const {
  return packed_enum_.size();
}
inline void TestPackedTypes::clear_packed_enum() {
  packed_enum_.Clear();
}
inline ::proto3_arena_unittest::ForeignEnum TestPackedTypes::packed_enum(int index) const {
  // @@protoc_insertion_point(field_get:proto3_arena_unittest.TestPackedTypes.packed_enum)
  return static_cast< ::proto3_arena_unittest::ForeignEnum >(packed_enum_.Get(index));
}
inline void TestPackedTypes::set_packed_enum(int index, ::proto3_arena_unittest::ForeignEnum value) {
  packed_enum_.Set(index, value);
  // @@protoc_insertion_point(field_set:proto3_arena_unittest.TestPackedTypes.packed_enum)
}
inline void TestPackedTypes::add_packed_enum(::proto3_arena_unittest::ForeignEnum value) {
  packed_enum_.Add(value);
  // @@protoc_insertion_point(field_add:proto3_arena_unittest.TestPackedTypes.packed_enum)
}
inline const ::google::protobuf::RepeatedField<int>&
TestPackedTypes::packed_enum() const {
  // @@protoc_insertion_point(field_list:proto3_arena_unittest.TestPackedTypes.packed_enum)
  return packed_enum_;
}
inline ::google::protobuf::RepeatedField<int>*
TestPackedTypes::mutable_packed_enum() {
  // @@protoc_insertion_point(field_mutable_list:proto3_arena_unittest.TestPackedTypes.packed_enum)
  return &packed_enum_;
}

inline const TestPackedTypes* TestPackedTypes::internal_default_instance() {
  return &TestPackedTypes_default_instance_.get();
}
// -------------------------------------------------------------------

// TestUnpackedTypes

// repeated int32 repeated_int32 = 1 [packed = false];
inline int TestUnpackedTypes::repeated_int32_size() const {
  return repeated_int32_.size();
}
inline void TestUnpackedTypes::clear_repeated_int32() {
  repeated_int32_.Clear();
}
inline ::google::protobuf::int32 TestUnpackedTypes::repeated_int32(int index) const {
  // @@protoc_insertion_point(field_get:proto3_arena_unittest.TestUnpackedTypes.repeated_int32)
  return repeated_int32_.Get(index);
}
inline void TestUnpackedTypes::set_repeated_int32(int index, ::google::protobuf::int32 value) {
  repeated_int32_.Set(index, value);
  // @@protoc_insertion_point(field_set:proto3_arena_unittest.TestUnpackedTypes.repeated_int32)
}
inline void TestUnpackedTypes::add_repeated_int32(::google::protobuf::int32 value) {
  repeated_int32_.Add(value);
  // @@protoc_insertion_point(field_add:proto3_arena_unittest.TestUnpackedTypes.repeated_int32)
}
inline const ::google::protobuf::RepeatedField< ::google::protobuf::int32 >&
TestUnpackedTypes::repeated_int32() const {
  // @@protoc_insertion_point(field_list:proto3_arena_unittest.TestUnpackedTypes.repeated_int32)
  return repeated_int32_;
}
inline ::google::protobuf::RepeatedField< ::google::protobuf::int32 >*
TestUnpackedTypes::mutable_repeated_int32() {
  // @@protoc_insertion_point(field_mutable_list:proto3_arena_unittest.TestUnpackedTypes.repeated_int32)
  return &repeated_int32_;
}

// repeated int64 repeated_int64 = 2 [packed = false];
inline int TestUnpackedTypes::repeated_int64_size() const {
  return repeated_int64_.size();
}
inline void TestUnpackedTypes::clear_repeated_int64() {
  repeated_int64_.Clear();
}
inline ::google::protobuf::int64 TestUnpackedTypes::repeated_int64(int index) const {
  // @@protoc_insertion_point(field_get:proto3_arena_unittest.TestUnpackedTypes.repeated_int64)
  return repeated_int64_.Get(index);
}
inline void TestUnpackedTypes::set_repeated_int64(int index, ::google::protobuf::int64 value) {
  repeated_int64_.Set(index, value);
  // @@protoc_insertion_point(field_set:proto3_arena_unittest.TestUnpackedTypes.repeated_int64)
}
inline void TestUnpackedTypes::add_repeated_int64(::google::protobuf::int64 value) {
  repeated_int64_.Add(value);
  // @@protoc_insertion_point(field_add:proto3_arena_unittest.TestUnpackedTypes.repeated_int64)
}
inline const ::google::protobuf::RepeatedField< ::google::protobuf::int64 >&
TestUnpackedTypes::repeated_int64() const {
  // @@protoc_insertion_point(field_list:proto3_arena_unittest.TestUnpackedTypes.repeated_int64)
  return repeated_int64_;
}
inline ::google::protobuf::RepeatedField< ::google::protobuf::int64 >*
TestUnpackedTypes::mutable_repeated_int64() {
  // @@protoc_insertion_point(field_mutable_list:proto3_arena_unittest.TestUnpackedTypes.repeated_int64)
  return &repeated_int64_;
}

// repeated uint32 repeated_uint32 = 3 [packed = false];
inline int TestUnpackedTypes::repeated_uint32_size() const {
  return repeated_uint32_.size();
}
inline void TestUnpackedTypes::clear_repeated_uint32() {
  repeated_uint32_.Clear();
}
inline ::google::protobuf::uint32 TestUnpackedTypes::repeated_uint32(int index) const {
  // @@protoc_insertion_point(field_get:proto3_arena_unittest.TestUnpackedTypes.repeated_uint32)
  return repeated_uint32_.Get(index);
}
inline void TestUnpackedTypes::set_repeated_uint32(int index, ::google::protobuf::uint32 value) {
  repeated_uint32_.Set(index, value);
  // @@protoc_insertion_point(field_set:proto3_arena_unittest.TestUnpackedTypes.repeated_uint32)
}
inline void TestUnpackedTypes::add_repeated_uint32(::google::protobuf::uint32 value) {
  repeated_uint32_.Add(value);
  // @@protoc_insertion_point(field_add:proto3_arena_unittest.TestUnpackedTypes.repeated_uint32)
}
inline const ::google::protobuf::RepeatedField< ::google::protobuf::uint32 >&
TestUnpackedTypes::repeated_uint32() const {
  // @@protoc_insertion_point(field_list:proto3_arena_unittest.TestUnpackedTypes.repeated_uint32)
  return repeated_uint32_;
}
inline ::google::protobuf::RepeatedField< ::google::protobuf::uint32 >*
TestUnpackedTypes::mutable_repeated_uint32() {
  // @@protoc_insertion_point(field_mutable_list:proto3_arena_unittest.TestUnpackedTypes.repeated_uint32)
  return &repeated_uint32_;
}

// repeated uint64 repeated_uint64 = 4 [packed = false];
inline int TestUnpackedTypes::repeated_uint64_size() const {
  return repeated_uint64_.size();
}
inline void TestUnpackedTypes::clear_repeated_uint64() {
  repeated_uint64_.Clear();
}
inline ::google::protobuf::uint64 TestUnpackedTypes::repeated_uint64(int index) const {
  // @@protoc_insertion_point(field_get:proto3_arena_unittest.TestUnpackedTypes.repeated_uint64)
  return repeated_uint64_.Get(index);
}
inline void TestUnpackedTypes::set_repeated_uint64(int index, ::google::protobuf::uint64 value) {
  repeated_uint64_.Set(index, value);
  // @@protoc_insertion_point(field_set:proto3_arena_unittest.TestUnpackedTypes.repeated_uint64)
}
inline void TestUnpackedTypes::add_repeated_uint64(::google::protobuf::uint64 value) {
  repeated_uint64_.Add(value);
  // @@protoc_insertion_point(field_add:proto3_arena_unittest.TestUnpackedTypes.repeated_uint64)
}
inline const ::google::protobuf::RepeatedField< ::google::protobuf::uint64 >&
TestUnpackedTypes::repeated_uint64() const {
  // @@protoc_insertion_point(field_list:proto3_arena_unittest.TestUnpackedTypes.repeated_uint64)
  return repeated_uint64_;
}
inline ::google::protobuf::RepeatedField< ::google::protobuf::uint64 >*
TestUnpackedTypes::mutable_repeated_uint64() {
  // @@protoc_insertion_point(field_mutable_list:proto3_arena_unittest.TestUnpackedTypes.repeated_uint64)
  return &repeated_uint64_;
}

// repeated sint32 repeated_sint32 = 5 [packed = false];
inline int TestUnpackedTypes::repeated_sint32_size() const {
  return repeated_sint32_.size();
}
inline void TestUnpackedTypes::clear_repeated_sint32() {
  repeated_sint32_.Clear();
}
inline ::google::protobuf::int32 TestUnpackedTypes::repeated_sint32(int index) const {
  // @@protoc_insertion_point(field_get:proto3_arena_unittest.TestUnpackedTypes.repeated_sint32)
  return repeated_sint32_.Get(index);
}
inline void TestUnpackedTypes::set_repeated_sint32(int index, ::google::protobuf::int32 value) {
  repeated_sint32_.Set(index, value);
  // @@protoc_insertion_point(field_set:proto3_arena_unittest.TestUnpackedTypes.repeated_sint32)
}
inline void TestUnpackedTypes::add_repeated_sint32(::google::protobuf::int32 value) {
  repeated_sint32_.Add(value);
  // @@protoc_insertion_point(field_add:proto3_arena_unittest.TestUnpackedTypes.repeated_sint32)
}
inline const ::google::protobuf::RepeatedField< ::google::protobuf::int32 >&
TestUnpackedTypes::repeated_sint32() const {
  // @@protoc_insertion_point(field_list:proto3_arena_unittest.TestUnpackedTypes.repeated_sint32)
  return repeated_sint32_;
}
inline ::google::protobuf::RepeatedField< ::google::protobuf::int32 >*
TestUnpackedTypes::mutable_repeated_sint32() {
  // @@protoc_insertion_point(field_mutable_list:proto3_arena_unittest.TestUnpackedTypes.repeated_sint32)
  return &repeated_sint32_;
}

// repeated sint64 repeated_sint64 = 6 [packed = false];
inline int TestUnpackedTypes::repeated_sint64_size() const {
  return repeated_sint64_.size();
}
inline void TestUnpackedTypes::clear_repeated_sint64() {
  repeated_sint64_.Clear();
}
inline ::google::protobuf::int64 TestUnpackedTypes::repeated_sint64(int index) const {
  // @@protoc_insertion_point(field_get:proto3_arena_unittest.TestUnpackedTypes.repeated_sint64)
  return repeated_sint64_.Get(index);
}
inline void TestUnpackedTypes::set_repeated_sint64(int index, ::google::protobuf::int64 value) {
  repeated_sint64_.Set(index, value);
  // @@protoc_insertion_point(field_set:proto3_arena_unittest.TestUnpackedTypes.repeated_sint64)
}
inline void TestUnpackedTypes::add_repeated_sint64(::google::protobuf::int64 value) {
  repeated_sint64_.Add(value);
  // @@protoc_insertion_point(field_add:proto3_arena_unittest.TestUnpackedTypes.repeated_sint64)
}
inline const ::google::protobuf::RepeatedField< ::google::protobuf::int64 >&
TestUnpackedTypes::repeated_sint64() const {
  // @@protoc_insertion_point(field_list:proto3_arena_unittest.TestUnpackedTypes.repeated_sint64)
  return repeated_sint64_;
}
inline ::google::protobuf::RepeatedField< ::google::protobuf::int64 >*
TestUnpackedTypes::mutable_repeated_sint64() {
  // @@protoc_insertion_point(field_mutable_list:proto3_arena_unittest.TestUnpackedTypes.repeated_sint64)
  return &repeated_sint64_;
}

// repeated fixed32 repeated_fixed32 = 7 [packed = false];
inline int TestUnpackedTypes::repeated_fixed32_size() const {
  return repeated_fixed32_.size();
}
inline void TestUnpackedTypes::clear_repeated_fixed32() {
  repeated_fixed32_.Clear();
}
inline ::google::protobuf::uint32 TestUnpackedTypes::repeated_fixed32(int index) const {
  // @@protoc_insertion_point(field_get:proto3_arena_unittest.TestUnpackedTypes.repeated_fixed32)
  return repeated_fixed32_.Get(index);
}
inline void TestUnpackedTypes::set_repeated_fixed32(int index, ::google::protobuf::uint32 value) {
  repeated_fixed32_.Set(index, value);
  // @@protoc_insertion_point(field_set:proto3_arena_unittest.TestUnpackedTypes.repeated_fixed32)
}
inline void TestUnpackedTypes::add_repeated_fixed32(::google::protobuf::uint32 value) {
  repeated_fixed32_.Add(value);
  // @@protoc_insertion_point(field_add:proto3_arena_unittest.TestUnpackedTypes.repeated_fixed32)
}
inline const ::google::protobuf::RepeatedField< ::google::protobuf::uint32 >&
TestUnpackedTypes::repeated_fixed32() const {
  // @@protoc_insertion_point(field_list:proto3_arena_unittest.TestUnpackedTypes.repeated_fixed32)
  return repeated_fixed32_;
}
inline ::google::protobuf::RepeatedField< ::google::protobuf::uint32 >*
TestUnpackedTypes::mutable_repeated_fixed32() {
  // @@protoc_insertion_point(field_mutable_list:proto3_arena_unittest.TestUnpackedTypes.repeated_fixed32)
  return &repeated_fixed32_;
}

// repeated fixed64 repeated_fixed64 = 8 [packed = false];
inline int TestUnpackedTypes::repeated_fixed64_size() const {
  return repeated_fixed64_.size();
}
inline void TestUnpackedTypes::clear_repeated_fixed64() {
  repeated_fixed64_.Clear();
}
inline ::google::protobuf::uint64 TestUnpackedTypes::repeated_fixed64(int index) const {
  // @@protoc_insertion_point(field_get:proto3_arena_unittest.TestUnpackedTypes.repeated_fixed64)
  return repeated_fixed64_.Get(index);
}
inline void TestUnpackedTypes::set_repeated_fixed64(int index, ::google::protobuf::uint64 value) {
  repeated_fixed64_.Set(index, value);
  // @@protoc_insertion_point(field_set:proto3_arena_unittest.TestUnpackedTypes.repeated_fixed64)
}
inline void TestUnpackedTypes::add_repeated_fixed64(::google::protobuf::uint64 value) {
  repeated_fixed64_.Add(value);
  // @@protoc_insertion_point(field_add:proto3_arena_unittest.TestUnpackedTypes.repeated_fixed64)
}
inline const ::google::protobuf::RepeatedField< ::google::protobuf::uint64 >&
TestUnpackedTypes::repeated_fixed64() const {
  // @@protoc_insertion_point(field_list:proto3_arena_unittest.TestUnpackedTypes.repeated_fixed64)
  return repeated_fixed64_;
}
inline ::google::protobuf::RepeatedField< ::google::protobuf::uint64 >*
TestUnpackedTypes::mutable_repeated_fixed64() {
  // @@protoc_insertion_point(field_mutable_list:proto3_arena_unittest.TestUnpackedTypes.repeated_fixed64)
  return &repeated_fixed64_;
}

// repeated sfixed32 repeated_sfixed32 = 9 [packed = false];
inline int TestUnpackedTypes::repeated_sfixed32_size() const {
  return repeated_sfixed32_.size();
}
inline void TestUnpackedTypes::clear_repeated_sfixed32() {
  repeated_sfixed32_.Clear();
}
inline ::google::protobuf::int32 TestUnpackedTypes::repeated_sfixed32(int index) const {
  // @@protoc_insertion_point(field_get:proto3_arena_unittest.TestUnpackedTypes.repeated_sfixed32)
  return repeated_sfixed32_.Get(index);
}
inline void TestUnpackedTypes::set_repeated_sfixed32(int index, ::google::protobuf::int32 value) {
  repeated_sfixed32_.Set(index, value);
  // @@protoc_insertion_point(field_set:proto3_arena_unittest.TestUnpackedTypes.repeated_sfixed32)
}
inline void TestUnpackedTypes::add_repeated_sfixed32(::google::protobuf::int32 value) {
  repeated_sfixed32_.Add(value);
  // @@protoc_insertion_point(field_add:proto3_arena_unittest.TestUnpackedTypes.repeated_sfixed32)
}
inline const ::google::protobuf::RepeatedField< ::google::protobuf::int32 >&
TestUnpackedTypes::repeated_sfixed32() const {
  // @@protoc_insertion_point(field_list:proto3_arena_unittest.TestUnpackedTypes.repeated_sfixed32)
  return repeated_sfixed32_;
}
inline ::google::protobuf::RepeatedField< ::google::protobuf::int32 >*
TestUnpackedTypes::mutable_repeated_sfixed32() {
  // @@protoc_insertion_point(field_mutable_list:proto3_arena_unittest.TestUnpackedTypes.repeated_sfixed32)
  return &repeated_sfixed32_;
}

// repeated sfixed64 repeated_sfixed64 = 10 [packed = false];
inline int TestUnpackedTypes::repeated_sfixed64_size() const {
  return repeated_sfixed64_.size();
}
inline void TestUnpackedTypes::clear_repeated_sfixed64() {
  repeated_sfixed64_.Clear();
}
inline ::google::protobuf::int64 TestUnpackedTypes::repeated_sfixed64(int index) const {
  // @@protoc_insertion_point(field_get:proto3_arena_unittest.TestUnpackedTypes.repeated_sfixed64)
  return repeated_sfixed64_.Get(index);
}
inline void TestUnpackedTypes::set_repeated_sfixed64(int index, ::google::protobuf::int64 value) {
  repeated_sfixed64_.Set(index, value);
  // @@protoc_insertion_point(field_set:proto3_arena_unittest.TestUnpackedTypes.repeated_sfixed64)
}
inline void TestUnpackedTypes::add_repeated_sfixed64(::google::protobuf::int64 value) {
  repeated_sfixed64_.Add(value);
  // @@protoc_insertion_point(field_add:proto3_arena_unittest.TestUnpackedTypes.repeated_sfixed64)
}
inline const ::google::protobuf::RepeatedField< ::google::protobuf::int64 >&
TestUnpackedTypes::repeated_sfixed64() const {
  // @@protoc_insertion_point(field_list:proto3_arena_unittest.TestUnpackedTypes.repeated_sfixed64)
  return repeated_sfixed64_;
}
inline ::google::protobuf::RepeatedField< ::google::protobuf::int64 >*
TestUnpackedTypes::mutable_repeated_sfixed64() {
  // @@protoc_insertion_point(field_mutable_list:proto3_arena_unittest.TestUnpackedTypes.repeated_sfixed64)
  return &repeated_sfixed64_;
}

// repeated float repeated_float = 11 [packed = false];
inline int TestUnpackedTypes::repeated_float_size() const {
  return repeated_float_.size();
}
inline void TestUnpackedTypes::clear_repeated_float() {
  repeated_float_.Clear();
}
inline float TestUnpackedTypes::repeated_float(int index) const {
  // @@protoc_insertion_point(field_get:proto3_arena_unittest.TestUnpackedTypes.repeated_float)
  return repeated_float_.Get(index);
}
inline void TestUnpackedTypes::set_repeated_float(int index, float value) {
  repeated_float_.Set(index, value);
  // @@protoc_insertion_point(field_set:proto3_arena_unittest.TestUnpackedTypes.repeated_float)
}
inline void TestUnpackedTypes::add_repeated_float(float value) {
  repeated_float_.Add(value);
  // @@protoc_insertion_point(field_add:proto3_arena_unittest.TestUnpackedTypes.repeated_float)
}
inline const ::google::protobuf::RepeatedField< float >&
TestUnpackedTypes::repeated_float() const {
  // @@protoc_insertion_point(field_list:proto3_arena_unittest.TestUnpackedTypes.repeated_float)
  return repeated_float_;
}
inline ::google::protobuf::RepeatedField< float >*
TestUnpackedTypes::mutable_repeated_float() {
  // @@protoc_insertion_point(field_mutable_list:proto3_arena_unittest.TestUnpackedTypes.repeated_float)
  return &repeated_float_;
}

// repeated double repeated_double = 12 [packed = false];
inline int TestUnpackedTypes::repeated_double_size() const {
  return repeated_double_.size();
}
inline void TestUnpackedTypes::clear_repeated_double() {
  repeated_double_.Clear();
}
inline double TestUnpackedTypes::repeated_double(int index) const {
  // @@protoc_insertion_point(field_get:proto3_arena_unittest.TestUnpackedTypes.repeated_double)
  return repeated_double_.Get(index);
}
inline void TestUnpackedTypes::set_repeated_double(int index, double value) {
  repeated_double_.Set(index, value);
  // @@protoc_insertion_point(field_set:proto3_arena_unittest.TestUnpackedTypes.repeated_double)
}
inline void TestUnpackedTypes::add_repeated_double(double value) {
  repeated_double_.Add(value);
  // @@protoc_insertion_point(field_add:proto3_arena_unittest.TestUnpackedTypes.repeated_double)
}
inline const ::google::protobuf::RepeatedField< double >&
TestUnpackedTypes::repeated_double() const {
  // @@protoc_insertion_point(field_list:proto3_arena_unittest.TestUnpackedTypes.repeated_double)
  return repeated_double_;
}
inline ::google::protobuf::RepeatedField< double >*
TestUnpackedTypes::mutable_repeated_double() {
  // @@protoc_insertion_point(field_mutable_list:proto3_arena_unittest.TestUnpackedTypes.repeated_double)
  return &repeated_double_;
}

// repeated bool repeated_bool = 13 [packed = false];
inline int TestUnpackedTypes::repeated_bool_size() const {
  return repeated_bool_.size();
}
inline void TestUnpackedTypes::clear_repeated_bool() {
  repeated_bool_.Clear();
}
inline bool TestUnpackedTypes::repeated_bool(int index) const {
  // @@protoc_insertion_point(field_get:proto3_arena_unittest.TestUnpackedTypes.repeated_bool)
  return repeated_bool_.Get(index);
}
inline void TestUnpackedTypes::set_repeated_bool(int index, bool value) {
  repeated_bool_.Set(index, value);
  // @@protoc_insertion_point(field_set:proto3_arena_unittest.TestUnpackedTypes.repeated_bool)
}
inline void TestUnpackedTypes::add_repeated_bool(bool value) {
  repeated_bool_.Add(value);
  // @@protoc_insertion_point(field_add:proto3_arena_unittest.TestUnpackedTypes.repeated_bool)
}
inline const ::google::protobuf::RepeatedField< bool >&
TestUnpackedTypes::repeated_bool() const {
  // @@protoc_insertion_point(field_list:proto3_arena_unittest.TestUnpackedTypes.repeated_bool)
  return repeated_bool_;
}
inline ::google::protobuf::RepeatedField< bool >*
TestUnpackedTypes::mutable_repeated_bool() {
  // @@protoc_insertion_point(field_mutable_list:proto3_arena_unittest.TestUnpackedTypes.repeated_bool)
  return &repeated_bool_;
}

// repeated .proto3_arena_unittest.TestAllTypes.NestedEnum repeated_nested_enum = 14 [packed = false];
inline int TestUnpackedTypes::repeated_nested_enum_size() const {
  return repeated_nested_enum_.size();
}
inline void TestUnpackedTypes::clear_repeated_nested_enum() {
  repeated_nested_enum_.Clear();
}
inline ::proto3_arena_unittest::TestAllTypes_NestedEnum TestUnpackedTypes::repeated_nested_enum(int index) const {
  // @@protoc_insertion_point(field_get:proto3_arena_unittest.TestUnpackedTypes.repeated_nested_enum)
  return static_cast< ::proto3_arena_unittest::TestAllTypes_NestedEnum >(repeated_nested_enum_.Get(index));
}
inline void TestUnpackedTypes::set_repeated_nested_enum(int index, ::proto3_arena_unittest::TestAllTypes_NestedEnum value) {
  repeated_nested_enum_.Set(index, value);
  // @@protoc_insertion_point(field_set:proto3_arena_unittest.TestUnpackedTypes.repeated_nested_enum)
}
inline void TestUnpackedTypes::add_repeated_nested_enum(::proto3_arena_unittest::TestAllTypes_NestedEnum value) {
  repeated_nested_enum_.Add(value);
  // @@protoc_insertion_point(field_add:proto3_arena_unittest.TestUnpackedTypes.repeated_nested_enum)
}
inline const ::google::protobuf::RepeatedField<int>&
TestUnpackedTypes::repeated_nested_enum() const {
  // @@protoc_insertion_point(field_list:proto3_arena_unittest.TestUnpackedTypes.repeated_nested_enum)
  return repeated_nested_enum_;
}
inline ::google::protobuf::RepeatedField<int>*
TestUnpackedTypes::mutable_repeated_nested_enum() {
  // @@protoc_insertion_point(field_mutable_list:proto3_arena_unittest.TestUnpackedTypes.repeated_nested_enum)
  return &repeated_nested_enum_;
}

inline const TestUnpackedTypes* TestUnpackedTypes::internal_default_instance() {
  return &TestUnpackedTypes_default_instance_.get();
}
// -------------------------------------------------------------------

// NestedTestAllTypes

// optional .proto3_arena_unittest.NestedTestAllTypes child = 1;
inline bool NestedTestAllTypes::has_child() const {
  return this != internal_default_instance() && child_ != NULL;
}
inline void NestedTestAllTypes::clear_child() {
  if (GetArenaNoVirtual() == NULL && child_ != NULL) delete child_;
  child_ = NULL;
}
inline const ::proto3_arena_unittest::NestedTestAllTypes& NestedTestAllTypes::child() const {
  // @@protoc_insertion_point(field_get:proto3_arena_unittest.NestedTestAllTypes.child)
  return child_ != NULL ? *child_
                         : *::proto3_arena_unittest::NestedTestAllTypes::internal_default_instance();
}
inline ::proto3_arena_unittest::NestedTestAllTypes* NestedTestAllTypes::mutable_child() {
  
  if (child_ == NULL) {
    _slow_mutable_child();
  }
  // @@protoc_insertion_point(field_mutable:proto3_arena_unittest.NestedTestAllTypes.child)
  return child_;
}
inline ::proto3_arena_unittest::NestedTestAllTypes* NestedTestAllTypes::release_child() {
  // @@protoc_insertion_point(field_release:proto3_arena_unittest.NestedTestAllTypes.child)
  
  if (GetArenaNoVirtual() != NULL) {
    return _slow_release_child();
  } else {
    ::proto3_arena_unittest::NestedTestAllTypes* temp = child_;
    child_ = NULL;
    return temp;
  }
}
inline  void NestedTestAllTypes::set_allocated_child(::proto3_arena_unittest::NestedTestAllTypes* child) {
  ::google::protobuf::Arena* message_arena = GetArenaNoVirtual();
  if (message_arena == NULL) {
    delete child_;
  }
  if (child != NULL) {
    _slow_set_allocated_child(message_arena, &child);
  }
  child_ = child;
  if (child) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_set_allocated:proto3_arena_unittest.NestedTestAllTypes.child)
}

// optional .proto3_arena_unittest.TestAllTypes payload = 2;
inline bool NestedTestAllTypes::has_payload() const {
  return this != internal_default_instance() && payload_ != NULL;
}
inline void NestedTestAllTypes::clear_payload() {
  if (GetArenaNoVirtual() == NULL && payload_ != NULL) delete payload_;
  payload_ = NULL;
}
inline const ::proto3_arena_unittest::TestAllTypes& NestedTestAllTypes::payload() const {
  // @@protoc_insertion_point(field_get:proto3_arena_unittest.NestedTestAllTypes.payload)
  return payload_ != NULL ? *payload_
                         : *::proto3_arena_unittest::TestAllTypes::internal_default_instance();
}
inline ::proto3_arena_unittest::TestAllTypes* NestedTestAllTypes::mutable_payload() {
  
  if (payload_ == NULL) {
    _slow_mutable_payload();
  }
  // @@protoc_insertion_point(field_mutable:proto3_arena_unittest.NestedTestAllTypes.payload)
  return payload_;
}
inline ::proto3_arena_unittest::TestAllTypes* NestedTestAllTypes::release_payload() {
  // @@protoc_insertion_point(field_release:proto3_arena_unittest.NestedTestAllTypes.payload)
  
  if (GetArenaNoVirtual() != NULL) {
    return _slow_release_payload();
  } else {
    ::proto3_arena_unittest::TestAllTypes* temp = payload_;
    payload_ = NULL;
    return temp;
  }
}
inline  void NestedTestAllTypes::set_allocated_payload(::proto3_arena_unittest::TestAllTypes* payload) {
  ::google::protobuf::Arena* message_arena = GetArenaNoVirtual();
  if (message_arena == NULL) {
    delete payload_;
  }
  if (payload != NULL) {
    _slow_set_allocated_payload(message_arena, &payload);
  }
  payload_ = payload;
  if (payload) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_set_allocated:proto3_arena_unittest.NestedTestAllTypes.payload)
}

inline const NestedTestAllTypes* NestedTestAllTypes::internal_default_instance() {
  return &NestedTestAllTypes_default_instance_.get();
}
// -------------------------------------------------------------------

// ForeignMessage

// optional int32 c = 1;
inline void ForeignMessage::clear_c() {
  c_ = 0;
}
inline ::google::protobuf::int32 ForeignMessage::c() const {
  // @@protoc_insertion_point(field_get:proto3_arena_unittest.ForeignMessage.c)
  return c_;
}
inline void ForeignMessage::set_c(::google::protobuf::int32 value) {
  
  c_ = value;
  // @@protoc_insertion_point(field_set:proto3_arena_unittest.ForeignMessage.c)
}

inline const ForeignMessage* ForeignMessage::internal_default_instance() {
  return &ForeignMessage_default_instance_.get();
}
// -------------------------------------------------------------------

// TestEmptyMessage

inline const TestEmptyMessage* TestEmptyMessage::internal_default_instance() {
  return &TestEmptyMessage_default_instance_.get();
}
#endif  // !PROTOBUF_INLINE_NOT_IN_HEADERS
// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------


// @@protoc_insertion_point(namespace_scope)

}  // namespace proto3_arena_unittest

#ifndef SWIG
namespace google {
namespace protobuf {

template <> struct is_proto_enum< ::proto3_arena_unittest::TestAllTypes_NestedEnum> : ::google::protobuf::internal::true_type {};
template <>
inline const EnumDescriptor* GetEnumDescriptor< ::proto3_arena_unittest::TestAllTypes_NestedEnum>() {
  return ::proto3_arena_unittest::TestAllTypes_NestedEnum_descriptor();
}
template <> struct is_proto_enum< ::proto3_arena_unittest::ForeignEnum> : ::google::protobuf::internal::true_type {};
template <>
inline const EnumDescriptor* GetEnumDescriptor< ::proto3_arena_unittest::ForeignEnum>() {
  return ::proto3_arena_unittest::ForeignEnum_descriptor();
}

}  // namespace protobuf
}  // namespace google
#endif  // SWIG

// @@protoc_insertion_point(global_scope)

#endif  // PROTOBUF_google_2fprotobuf_2funittest_5fproto3_5farena_2eproto__INCLUDED

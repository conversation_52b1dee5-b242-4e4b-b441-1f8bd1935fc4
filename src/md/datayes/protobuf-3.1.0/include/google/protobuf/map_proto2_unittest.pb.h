// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: google/protobuf/map_proto2_unittest.proto

#ifndef PROTOBUF_google_2fprotobuf_2fmap_5fproto2_5funittest_2eproto__INCLUDED
#define PROTOBUF_google_2fprotobuf_2fmap_5fproto2_5funittest_2eproto__INCLUDED

#include <string>

#include <google/protobuf/stubs/common.h>

#if GOOGLE_PROTOBUF_VERSION < 3001000
#error This file was generated by a newer version of protoc which is
#error incompatible with your Protocol Buffer headers.  Please update
#error your headers.
#endif
#if 3001000 < GOOGLE_PROTOBUF_MIN_PROTOC_VERSION
#error This file was generated by an older version of protoc which is
#error incompatible with your Protocol Buffer headers.  Please
#error regenerate this file with a newer version of protoc.
#endif

#include <google/protobuf/arena.h>
#include <google/protobuf/arenastring.h>
#include <google/protobuf/generated_message_util.h>
#include <google/protobuf/metadata.h>
#include <google/protobuf/message.h>
#include <google/protobuf/repeated_field.h>
#include <google/protobuf/extension_set.h>
#include <google/protobuf/map.h>
#include <google/protobuf/map_field_inl.h>
#include <google/protobuf/generated_enum_reflection.h>
#include <google/protobuf/unknown_field_set.h>
#include <google/protobuf/unittest_import.pb.h>
// @@protoc_insertion_point(includes)

namespace protobuf_unittest {

// Internal implementation detail -- do not call these.
void protobuf_AddDesc_google_2fprotobuf_2fmap_5fproto2_5funittest_2eproto();
void protobuf_InitDefaults_google_2fprotobuf_2fmap_5fproto2_5funittest_2eproto();
void protobuf_AssignDesc_google_2fprotobuf_2fmap_5fproto2_5funittest_2eproto();
void protobuf_ShutdownFile_google_2fprotobuf_2fmap_5fproto2_5funittest_2eproto();

class TestEnumMap;
class TestEnumMapPlusExtra;
class TestImportEnumMap;
class TestIntIntMap;
class TestMaps;

enum Proto2MapEnum {
  PROTO2_MAP_ENUM_FOO = 0,
  PROTO2_MAP_ENUM_BAR = 1,
  PROTO2_MAP_ENUM_BAZ = 2
};
bool Proto2MapEnum_IsValid(int value);
const Proto2MapEnum Proto2MapEnum_MIN = PROTO2_MAP_ENUM_FOO;
const Proto2MapEnum Proto2MapEnum_MAX = PROTO2_MAP_ENUM_BAZ;
const int Proto2MapEnum_ARRAYSIZE = Proto2MapEnum_MAX + 1;

const ::google::protobuf::EnumDescriptor* Proto2MapEnum_descriptor();
inline const ::std::string& Proto2MapEnum_Name(Proto2MapEnum value) {
  return ::google::protobuf::internal::NameOfEnum(
    Proto2MapEnum_descriptor(), value);
}
inline bool Proto2MapEnum_Parse(
    const ::std::string& name, Proto2MapEnum* value) {
  return ::google::protobuf::internal::ParseNamedEnum<Proto2MapEnum>(
    Proto2MapEnum_descriptor(), name, value);
}
enum Proto2MapEnumPlusExtra {
  E_PROTO2_MAP_ENUM_FOO = 0,
  E_PROTO2_MAP_ENUM_BAR = 1,
  E_PROTO2_MAP_ENUM_BAZ = 2,
  E_PROTO2_MAP_ENUM_EXTRA = 3
};
bool Proto2MapEnumPlusExtra_IsValid(int value);
const Proto2MapEnumPlusExtra Proto2MapEnumPlusExtra_MIN = E_PROTO2_MAP_ENUM_FOO;
const Proto2MapEnumPlusExtra Proto2MapEnumPlusExtra_MAX = E_PROTO2_MAP_ENUM_EXTRA;
const int Proto2MapEnumPlusExtra_ARRAYSIZE = Proto2MapEnumPlusExtra_MAX + 1;

const ::google::protobuf::EnumDescriptor* Proto2MapEnumPlusExtra_descriptor();
inline const ::std::string& Proto2MapEnumPlusExtra_Name(Proto2MapEnumPlusExtra value) {
  return ::google::protobuf::internal::NameOfEnum(
    Proto2MapEnumPlusExtra_descriptor(), value);
}
inline bool Proto2MapEnumPlusExtra_Parse(
    const ::std::string& name, Proto2MapEnumPlusExtra* value) {
  return ::google::protobuf::internal::ParseNamedEnum<Proto2MapEnumPlusExtra>(
    Proto2MapEnumPlusExtra_descriptor(), name, value);
}
// ===================================================================

class TestEnumMap : public ::google::protobuf::Message /* @@protoc_insertion_point(class_definition:protobuf_unittest.TestEnumMap) */ {
 public:
  TestEnumMap();
  virtual ~TestEnumMap();

  TestEnumMap(const TestEnumMap& from);

  inline TestEnumMap& operator=(const TestEnumMap& from) {
    CopyFrom(from);
    return *this;
  }

  inline const ::google::protobuf::UnknownFieldSet& unknown_fields() const {
    return _internal_metadata_.unknown_fields();
  }

  inline ::google::protobuf::UnknownFieldSet* mutable_unknown_fields() {
    return _internal_metadata_.mutable_unknown_fields();
  }

  static const ::google::protobuf::Descriptor* descriptor();
  static const TestEnumMap& default_instance();

  static const TestEnumMap* internal_default_instance();

  void Swap(TestEnumMap* other);

  // implements Message ----------------------------------------------

  inline TestEnumMap* New() const { return New(NULL); }

  TestEnumMap* New(::google::protobuf::Arena* arena) const;
  void CopyFrom(const ::google::protobuf::Message& from);
  void MergeFrom(const ::google::protobuf::Message& from);
  void CopyFrom(const TestEnumMap& from);
  void MergeFrom(const TestEnumMap& from);
  void Clear();
  bool IsInitialized() const;

  size_t ByteSizeLong() const;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input);
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const;
  ::google::protobuf::uint8* InternalSerializeWithCachedSizesToArray(
      bool deterministic, ::google::protobuf::uint8* output) const;
  ::google::protobuf::uint8* SerializeWithCachedSizesToArray(::google::protobuf::uint8* output) const {
    return InternalSerializeWithCachedSizesToArray(false, output);
  }
  int GetCachedSize() const { return _cached_size_; }
  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const;
  void InternalSwap(TestEnumMap* other);
  void UnsafeMergeFrom(const TestEnumMap& from);
  private:
  inline ::google::protobuf::Arena* GetArenaNoVirtual() const {
    return _internal_metadata_.arena();
  }
  inline void* MaybeArenaPtr() const {
    return _internal_metadata_.raw_arena_ptr();
  }
  public:

  ::google::protobuf::Metadata GetMetadata() const;

  // nested types ----------------------------------------------------


  // accessors -------------------------------------------------------

  // map<int32, .protobuf_unittest.Proto2MapEnum> known_map_field = 101;
  int known_map_field_size() const;
  void clear_known_map_field();
  static const int kKnownMapFieldFieldNumber = 101;
  const ::google::protobuf::Map< ::google::protobuf::int32, ::protobuf_unittest::Proto2MapEnum >&
      known_map_field() const;
  ::google::protobuf::Map< ::google::protobuf::int32, ::protobuf_unittest::Proto2MapEnum >*
      mutable_known_map_field();

  // map<int32, .protobuf_unittest.Proto2MapEnum> unknown_map_field = 102;
  int unknown_map_field_size() const;
  void clear_unknown_map_field();
  static const int kUnknownMapFieldFieldNumber = 102;
  const ::google::protobuf::Map< ::google::protobuf::int32, ::protobuf_unittest::Proto2MapEnum >&
      unknown_map_field() const;
  ::google::protobuf::Map< ::google::protobuf::int32, ::protobuf_unittest::Proto2MapEnum >*
      mutable_unknown_map_field();

  // @@protoc_insertion_point(class_scope:protobuf_unittest.TestEnumMap)
 private:

  ::google::protobuf::internal::InternalMetadataWithArena _internal_metadata_;
  ::google::protobuf::internal::HasBits<1> _has_bits_;
  mutable int _cached_size_;
  typedef ::google::protobuf::internal::MapEntryLite<
      ::google::protobuf::int32, ::protobuf_unittest::Proto2MapEnum,
      ::google::protobuf::internal::WireFormatLite::TYPE_INT32,
      ::google::protobuf::internal::WireFormatLite::TYPE_ENUM,
      0 >
      TestEnumMap_KnownMapFieldEntry;
  ::google::protobuf::internal::MapField<
      ::google::protobuf::int32, ::protobuf_unittest::Proto2MapEnum,
      ::google::protobuf::internal::WireFormatLite::TYPE_INT32,
      ::google::protobuf::internal::WireFormatLite::TYPE_ENUM,
      0 > known_map_field_;
  typedef ::google::protobuf::internal::MapEntryLite<
      ::google::protobuf::int32, ::protobuf_unittest::Proto2MapEnum,
      ::google::protobuf::internal::WireFormatLite::TYPE_INT32,
      ::google::protobuf::internal::WireFormatLite::TYPE_ENUM,
      0 >
      TestEnumMap_UnknownMapFieldEntry;
  ::google::protobuf::internal::MapField<
      ::google::protobuf::int32, ::protobuf_unittest::Proto2MapEnum,
      ::google::protobuf::internal::WireFormatLite::TYPE_INT32,
      ::google::protobuf::internal::WireFormatLite::TYPE_ENUM,
      0 > unknown_map_field_;
  friend void  protobuf_InitDefaults_google_2fprotobuf_2fmap_5fproto2_5funittest_2eproto_impl();
  friend void  protobuf_AddDesc_google_2fprotobuf_2fmap_5fproto2_5funittest_2eproto_impl();
  friend void protobuf_AssignDesc_google_2fprotobuf_2fmap_5fproto2_5funittest_2eproto();
  friend void protobuf_ShutdownFile_google_2fprotobuf_2fmap_5fproto2_5funittest_2eproto();

  void InitAsDefaultInstance();
};
extern ::google::protobuf::internal::ExplicitlyConstructed<TestEnumMap> TestEnumMap_default_instance_;

// -------------------------------------------------------------------

class TestEnumMapPlusExtra : public ::google::protobuf::Message /* @@protoc_insertion_point(class_definition:protobuf_unittest.TestEnumMapPlusExtra) */ {
 public:
  TestEnumMapPlusExtra();
  virtual ~TestEnumMapPlusExtra();

  TestEnumMapPlusExtra(const TestEnumMapPlusExtra& from);

  inline TestEnumMapPlusExtra& operator=(const TestEnumMapPlusExtra& from) {
    CopyFrom(from);
    return *this;
  }

  inline const ::google::protobuf::UnknownFieldSet& unknown_fields() const {
    return _internal_metadata_.unknown_fields();
  }

  inline ::google::protobuf::UnknownFieldSet* mutable_unknown_fields() {
    return _internal_metadata_.mutable_unknown_fields();
  }

  static const ::google::protobuf::Descriptor* descriptor();
  static const TestEnumMapPlusExtra& default_instance();

  static const TestEnumMapPlusExtra* internal_default_instance();

  void Swap(TestEnumMapPlusExtra* other);

  // implements Message ----------------------------------------------

  inline TestEnumMapPlusExtra* New() const { return New(NULL); }

  TestEnumMapPlusExtra* New(::google::protobuf::Arena* arena) const;
  void CopyFrom(const ::google::protobuf::Message& from);
  void MergeFrom(const ::google::protobuf::Message& from);
  void CopyFrom(const TestEnumMapPlusExtra& from);
  void MergeFrom(const TestEnumMapPlusExtra& from);
  void Clear();
  bool IsInitialized() const;

  size_t ByteSizeLong() const;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input);
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const;
  ::google::protobuf::uint8* InternalSerializeWithCachedSizesToArray(
      bool deterministic, ::google::protobuf::uint8* output) const;
  ::google::protobuf::uint8* SerializeWithCachedSizesToArray(::google::protobuf::uint8* output) const {
    return InternalSerializeWithCachedSizesToArray(false, output);
  }
  int GetCachedSize() const { return _cached_size_; }
  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const;
  void InternalSwap(TestEnumMapPlusExtra* other);
  void UnsafeMergeFrom(const TestEnumMapPlusExtra& from);
  private:
  inline ::google::protobuf::Arena* GetArenaNoVirtual() const {
    return _internal_metadata_.arena();
  }
  inline void* MaybeArenaPtr() const {
    return _internal_metadata_.raw_arena_ptr();
  }
  public:

  ::google::protobuf::Metadata GetMetadata() const;

  // nested types ----------------------------------------------------


  // accessors -------------------------------------------------------

  // map<int32, .protobuf_unittest.Proto2MapEnumPlusExtra> known_map_field = 101;
  int known_map_field_size() const;
  void clear_known_map_field();
  static const int kKnownMapFieldFieldNumber = 101;
  const ::google::protobuf::Map< ::google::protobuf::int32, ::protobuf_unittest::Proto2MapEnumPlusExtra >&
      known_map_field() const;
  ::google::protobuf::Map< ::google::protobuf::int32, ::protobuf_unittest::Proto2MapEnumPlusExtra >*
      mutable_known_map_field();

  // map<int32, .protobuf_unittest.Proto2MapEnumPlusExtra> unknown_map_field = 102;
  int unknown_map_field_size() const;
  void clear_unknown_map_field();
  static const int kUnknownMapFieldFieldNumber = 102;
  const ::google::protobuf::Map< ::google::protobuf::int32, ::protobuf_unittest::Proto2MapEnumPlusExtra >&
      unknown_map_field() const;
  ::google::protobuf::Map< ::google::protobuf::int32, ::protobuf_unittest::Proto2MapEnumPlusExtra >*
      mutable_unknown_map_field();

  // @@protoc_insertion_point(class_scope:protobuf_unittest.TestEnumMapPlusExtra)
 private:

  ::google::protobuf::internal::InternalMetadataWithArena _internal_metadata_;
  ::google::protobuf::internal::HasBits<1> _has_bits_;
  mutable int _cached_size_;
  typedef ::google::protobuf::internal::MapEntryLite<
      ::google::protobuf::int32, ::protobuf_unittest::Proto2MapEnumPlusExtra,
      ::google::protobuf::internal::WireFormatLite::TYPE_INT32,
      ::google::protobuf::internal::WireFormatLite::TYPE_ENUM,
      0 >
      TestEnumMapPlusExtra_KnownMapFieldEntry;
  ::google::protobuf::internal::MapField<
      ::google::protobuf::int32, ::protobuf_unittest::Proto2MapEnumPlusExtra,
      ::google::protobuf::internal::WireFormatLite::TYPE_INT32,
      ::google::protobuf::internal::WireFormatLite::TYPE_ENUM,
      0 > known_map_field_;
  typedef ::google::protobuf::internal::MapEntryLite<
      ::google::protobuf::int32, ::protobuf_unittest::Proto2MapEnumPlusExtra,
      ::google::protobuf::internal::WireFormatLite::TYPE_INT32,
      ::google::protobuf::internal::WireFormatLite::TYPE_ENUM,
      0 >
      TestEnumMapPlusExtra_UnknownMapFieldEntry;
  ::google::protobuf::internal::MapField<
      ::google::protobuf::int32, ::protobuf_unittest::Proto2MapEnumPlusExtra,
      ::google::protobuf::internal::WireFormatLite::TYPE_INT32,
      ::google::protobuf::internal::WireFormatLite::TYPE_ENUM,
      0 > unknown_map_field_;
  friend void  protobuf_InitDefaults_google_2fprotobuf_2fmap_5fproto2_5funittest_2eproto_impl();
  friend void  protobuf_AddDesc_google_2fprotobuf_2fmap_5fproto2_5funittest_2eproto_impl();
  friend void protobuf_AssignDesc_google_2fprotobuf_2fmap_5fproto2_5funittest_2eproto();
  friend void protobuf_ShutdownFile_google_2fprotobuf_2fmap_5fproto2_5funittest_2eproto();

  void InitAsDefaultInstance();
};
extern ::google::protobuf::internal::ExplicitlyConstructed<TestEnumMapPlusExtra> TestEnumMapPlusExtra_default_instance_;

// -------------------------------------------------------------------

class TestImportEnumMap : public ::google::protobuf::Message /* @@protoc_insertion_point(class_definition:protobuf_unittest.TestImportEnumMap) */ {
 public:
  TestImportEnumMap();
  virtual ~TestImportEnumMap();

  TestImportEnumMap(const TestImportEnumMap& from);

  inline TestImportEnumMap& operator=(const TestImportEnumMap& from) {
    CopyFrom(from);
    return *this;
  }

  inline const ::google::protobuf::UnknownFieldSet& unknown_fields() const {
    return _internal_metadata_.unknown_fields();
  }

  inline ::google::protobuf::UnknownFieldSet* mutable_unknown_fields() {
    return _internal_metadata_.mutable_unknown_fields();
  }

  static const ::google::protobuf::Descriptor* descriptor();
  static const TestImportEnumMap& default_instance();

  static const TestImportEnumMap* internal_default_instance();

  void Swap(TestImportEnumMap* other);

  // implements Message ----------------------------------------------

  inline TestImportEnumMap* New() const { return New(NULL); }

  TestImportEnumMap* New(::google::protobuf::Arena* arena) const;
  void CopyFrom(const ::google::protobuf::Message& from);
  void MergeFrom(const ::google::protobuf::Message& from);
  void CopyFrom(const TestImportEnumMap& from);
  void MergeFrom(const TestImportEnumMap& from);
  void Clear();
  bool IsInitialized() const;

  size_t ByteSizeLong() const;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input);
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const;
  ::google::protobuf::uint8* InternalSerializeWithCachedSizesToArray(
      bool deterministic, ::google::protobuf::uint8* output) const;
  ::google::protobuf::uint8* SerializeWithCachedSizesToArray(::google::protobuf::uint8* output) const {
    return InternalSerializeWithCachedSizesToArray(false, output);
  }
  int GetCachedSize() const { return _cached_size_; }
  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const;
  void InternalSwap(TestImportEnumMap* other);
  void UnsafeMergeFrom(const TestImportEnumMap& from);
  private:
  inline ::google::protobuf::Arena* GetArenaNoVirtual() const {
    return _internal_metadata_.arena();
  }
  inline void* MaybeArenaPtr() const {
    return _internal_metadata_.raw_arena_ptr();
  }
  public:

  ::google::protobuf::Metadata GetMetadata() const;

  // nested types ----------------------------------------------------


  // accessors -------------------------------------------------------

  // map<int32, .protobuf_unittest_import.ImportEnumForMap> import_enum_amp = 1;
  int import_enum_amp_size() const;
  void clear_import_enum_amp();
  static const int kImportEnumAmpFieldNumber = 1;
  const ::google::protobuf::Map< ::google::protobuf::int32, ::protobuf_unittest_import::ImportEnumForMap >&
      import_enum_amp() const;
  ::google::protobuf::Map< ::google::protobuf::int32, ::protobuf_unittest_import::ImportEnumForMap >*
      mutable_import_enum_amp();

  // @@protoc_insertion_point(class_scope:protobuf_unittest.TestImportEnumMap)
 private:

  ::google::protobuf::internal::InternalMetadataWithArena _internal_metadata_;
  ::google::protobuf::internal::HasBits<1> _has_bits_;
  mutable int _cached_size_;
  typedef ::google::protobuf::internal::MapEntryLite<
      ::google::protobuf::int32, ::protobuf_unittest_import::ImportEnumForMap,
      ::google::protobuf::internal::WireFormatLite::TYPE_INT32,
      ::google::protobuf::internal::WireFormatLite::TYPE_ENUM,
      0 >
      TestImportEnumMap_ImportEnumAmpEntry;
  ::google::protobuf::internal::MapField<
      ::google::protobuf::int32, ::protobuf_unittest_import::ImportEnumForMap,
      ::google::protobuf::internal::WireFormatLite::TYPE_INT32,
      ::google::protobuf::internal::WireFormatLite::TYPE_ENUM,
      0 > import_enum_amp_;
  friend void  protobuf_InitDefaults_google_2fprotobuf_2fmap_5fproto2_5funittest_2eproto_impl();
  friend void  protobuf_AddDesc_google_2fprotobuf_2fmap_5fproto2_5funittest_2eproto_impl();
  friend void protobuf_AssignDesc_google_2fprotobuf_2fmap_5fproto2_5funittest_2eproto();
  friend void protobuf_ShutdownFile_google_2fprotobuf_2fmap_5fproto2_5funittest_2eproto();

  void InitAsDefaultInstance();
};
extern ::google::protobuf::internal::ExplicitlyConstructed<TestImportEnumMap> TestImportEnumMap_default_instance_;

// -------------------------------------------------------------------

class TestIntIntMap : public ::google::protobuf::Message /* @@protoc_insertion_point(class_definition:protobuf_unittest.TestIntIntMap) */ {
 public:
  TestIntIntMap();
  virtual ~TestIntIntMap();

  TestIntIntMap(const TestIntIntMap& from);

  inline TestIntIntMap& operator=(const TestIntIntMap& from) {
    CopyFrom(from);
    return *this;
  }

  inline const ::google::protobuf::UnknownFieldSet& unknown_fields() const {
    return _internal_metadata_.unknown_fields();
  }

  inline ::google::protobuf::UnknownFieldSet* mutable_unknown_fields() {
    return _internal_metadata_.mutable_unknown_fields();
  }

  static const ::google::protobuf::Descriptor* descriptor();
  static const TestIntIntMap& default_instance();

  static const TestIntIntMap* internal_default_instance();

  void Swap(TestIntIntMap* other);

  // implements Message ----------------------------------------------

  inline TestIntIntMap* New() const { return New(NULL); }

  TestIntIntMap* New(::google::protobuf::Arena* arena) const;
  void CopyFrom(const ::google::protobuf::Message& from);
  void MergeFrom(const ::google::protobuf::Message& from);
  void CopyFrom(const TestIntIntMap& from);
  void MergeFrom(const TestIntIntMap& from);
  void Clear();
  bool IsInitialized() const;

  size_t ByteSizeLong() const;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input);
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const;
  ::google::protobuf::uint8* InternalSerializeWithCachedSizesToArray(
      bool deterministic, ::google::protobuf::uint8* output) const;
  ::google::protobuf::uint8* SerializeWithCachedSizesToArray(::google::protobuf::uint8* output) const {
    return InternalSerializeWithCachedSizesToArray(false, output);
  }
  int GetCachedSize() const { return _cached_size_; }
  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const;
  void InternalSwap(TestIntIntMap* other);
  void UnsafeMergeFrom(const TestIntIntMap& from);
  private:
  inline ::google::protobuf::Arena* GetArenaNoVirtual() const {
    return _internal_metadata_.arena();
  }
  inline void* MaybeArenaPtr() const {
    return _internal_metadata_.raw_arena_ptr();
  }
  public:

  ::google::protobuf::Metadata GetMetadata() const;

  // nested types ----------------------------------------------------


  // accessors -------------------------------------------------------

  // map<int32, int32> m = 1;
  int m_size() const;
  void clear_m();
  static const int kMFieldNumber = 1;
  const ::google::protobuf::Map< ::google::protobuf::int32, ::google::protobuf::int32 >&
      m() const;
  ::google::protobuf::Map< ::google::protobuf::int32, ::google::protobuf::int32 >*
      mutable_m();

  // @@protoc_insertion_point(class_scope:protobuf_unittest.TestIntIntMap)
 private:

  ::google::protobuf::internal::InternalMetadataWithArena _internal_metadata_;
  ::google::protobuf::internal::HasBits<1> _has_bits_;
  mutable int _cached_size_;
  typedef ::google::protobuf::internal::MapEntryLite<
      ::google::protobuf::int32, ::google::protobuf::int32,
      ::google::protobuf::internal::WireFormatLite::TYPE_INT32,
      ::google::protobuf::internal::WireFormatLite::TYPE_INT32,
      0 >
      TestIntIntMap_MEntry;
  ::google::protobuf::internal::MapField<
      ::google::protobuf::int32, ::google::protobuf::int32,
      ::google::protobuf::internal::WireFormatLite::TYPE_INT32,
      ::google::protobuf::internal::WireFormatLite::TYPE_INT32,
      0 > m_;
  friend void  protobuf_InitDefaults_google_2fprotobuf_2fmap_5fproto2_5funittest_2eproto_impl();
  friend void  protobuf_AddDesc_google_2fprotobuf_2fmap_5fproto2_5funittest_2eproto_impl();
  friend void protobuf_AssignDesc_google_2fprotobuf_2fmap_5fproto2_5funittest_2eproto();
  friend void protobuf_ShutdownFile_google_2fprotobuf_2fmap_5fproto2_5funittest_2eproto();

  void InitAsDefaultInstance();
};
extern ::google::protobuf::internal::ExplicitlyConstructed<TestIntIntMap> TestIntIntMap_default_instance_;

// -------------------------------------------------------------------

class TestMaps : public ::google::protobuf::Message /* @@protoc_insertion_point(class_definition:protobuf_unittest.TestMaps) */ {
 public:
  TestMaps();
  virtual ~TestMaps();

  TestMaps(const TestMaps& from);

  inline TestMaps& operator=(const TestMaps& from) {
    CopyFrom(from);
    return *this;
  }

  inline const ::google::protobuf::UnknownFieldSet& unknown_fields() const {
    return _internal_metadata_.unknown_fields();
  }

  inline ::google::protobuf::UnknownFieldSet* mutable_unknown_fields() {
    return _internal_metadata_.mutable_unknown_fields();
  }

  static const ::google::protobuf::Descriptor* descriptor();
  static const TestMaps& default_instance();

  static const TestMaps* internal_default_instance();

  void Swap(TestMaps* other);

  // implements Message ----------------------------------------------

  inline TestMaps* New() const { return New(NULL); }

  TestMaps* New(::google::protobuf::Arena* arena) const;
  void CopyFrom(const ::google::protobuf::Message& from);
  void MergeFrom(const ::google::protobuf::Message& from);
  void CopyFrom(const TestMaps& from);
  void MergeFrom(const TestMaps& from);
  void Clear();
  bool IsInitialized() const;

  size_t ByteSizeLong() const;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input);
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const;
  ::google::protobuf::uint8* InternalSerializeWithCachedSizesToArray(
      bool deterministic, ::google::protobuf::uint8* output) const;
  ::google::protobuf::uint8* SerializeWithCachedSizesToArray(::google::protobuf::uint8* output) const {
    return InternalSerializeWithCachedSizesToArray(false, output);
  }
  int GetCachedSize() const { return _cached_size_; }
  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const;
  void InternalSwap(TestMaps* other);
  void UnsafeMergeFrom(const TestMaps& from);
  private:
  inline ::google::protobuf::Arena* GetArenaNoVirtual() const {
    return _internal_metadata_.arena();
  }
  inline void* MaybeArenaPtr() const {
    return _internal_metadata_.raw_arena_ptr();
  }
  public:

  ::google::protobuf::Metadata GetMetadata() const;

  // nested types ----------------------------------------------------


  // accessors -------------------------------------------------------

  // map<int32, .protobuf_unittest.TestIntIntMap> m_int32 = 1;
  int m_int32_size() const;
  void clear_m_int32();
  static const int kMInt32FieldNumber = 1;
  const ::google::protobuf::Map< ::google::protobuf::int32, ::protobuf_unittest::TestIntIntMap >&
      m_int32() const;
  ::google::protobuf::Map< ::google::protobuf::int32, ::protobuf_unittest::TestIntIntMap >*
      mutable_m_int32();

  // map<int64, .protobuf_unittest.TestIntIntMap> m_int64 = 2;
  int m_int64_size() const;
  void clear_m_int64();
  static const int kMInt64FieldNumber = 2;
  const ::google::protobuf::Map< ::google::protobuf::int64, ::protobuf_unittest::TestIntIntMap >&
      m_int64() const;
  ::google::protobuf::Map< ::google::protobuf::int64, ::protobuf_unittest::TestIntIntMap >*
      mutable_m_int64();

  // map<uint32, .protobuf_unittest.TestIntIntMap> m_uint32 = 3;
  int m_uint32_size() const;
  void clear_m_uint32();
  static const int kMUint32FieldNumber = 3;
  const ::google::protobuf::Map< ::google::protobuf::uint32, ::protobuf_unittest::TestIntIntMap >&
      m_uint32() const;
  ::google::protobuf::Map< ::google::protobuf::uint32, ::protobuf_unittest::TestIntIntMap >*
      mutable_m_uint32();

  // map<uint64, .protobuf_unittest.TestIntIntMap> m_uint64 = 4;
  int m_uint64_size() const;
  void clear_m_uint64();
  static const int kMUint64FieldNumber = 4;
  const ::google::protobuf::Map< ::google::protobuf::uint64, ::protobuf_unittest::TestIntIntMap >&
      m_uint64() const;
  ::google::protobuf::Map< ::google::protobuf::uint64, ::protobuf_unittest::TestIntIntMap >*
      mutable_m_uint64();

  // map<sint32, .protobuf_unittest.TestIntIntMap> m_sint32 = 5;
  int m_sint32_size() const;
  void clear_m_sint32();
  static const int kMSint32FieldNumber = 5;
  const ::google::protobuf::Map< ::google::protobuf::int32, ::protobuf_unittest::TestIntIntMap >&
      m_sint32() const;
  ::google::protobuf::Map< ::google::protobuf::int32, ::protobuf_unittest::TestIntIntMap >*
      mutable_m_sint32();

  // map<sint64, .protobuf_unittest.TestIntIntMap> m_sint64 = 6;
  int m_sint64_size() const;
  void clear_m_sint64();
  static const int kMSint64FieldNumber = 6;
  const ::google::protobuf::Map< ::google::protobuf::int64, ::protobuf_unittest::TestIntIntMap >&
      m_sint64() const;
  ::google::protobuf::Map< ::google::protobuf::int64, ::protobuf_unittest::TestIntIntMap >*
      mutable_m_sint64();

  // map<fixed32, .protobuf_unittest.TestIntIntMap> m_fixed32 = 7;
  int m_fixed32_size() const;
  void clear_m_fixed32();
  static const int kMFixed32FieldNumber = 7;
  const ::google::protobuf::Map< ::google::protobuf::uint32, ::protobuf_unittest::TestIntIntMap >&
      m_fixed32() const;
  ::google::protobuf::Map< ::google::protobuf::uint32, ::protobuf_unittest::TestIntIntMap >*
      mutable_m_fixed32();

  // map<fixed64, .protobuf_unittest.TestIntIntMap> m_fixed64 = 8;
  int m_fixed64_size() const;
  void clear_m_fixed64();
  static const int kMFixed64FieldNumber = 8;
  const ::google::protobuf::Map< ::google::protobuf::uint64, ::protobuf_unittest::TestIntIntMap >&
      m_fixed64() const;
  ::google::protobuf::Map< ::google::protobuf::uint64, ::protobuf_unittest::TestIntIntMap >*
      mutable_m_fixed64();

  // map<sfixed32, .protobuf_unittest.TestIntIntMap> m_sfixed32 = 9;
  int m_sfixed32_size() const;
  void clear_m_sfixed32();
  static const int kMSfixed32FieldNumber = 9;
  const ::google::protobuf::Map< ::google::protobuf::int32, ::protobuf_unittest::TestIntIntMap >&
      m_sfixed32() const;
  ::google::protobuf::Map< ::google::protobuf::int32, ::protobuf_unittest::TestIntIntMap >*
      mutable_m_sfixed32();

  // map<sfixed64, .protobuf_unittest.TestIntIntMap> m_sfixed64 = 10;
  int m_sfixed64_size() const;
  void clear_m_sfixed64();
  static const int kMSfixed64FieldNumber = 10;
  const ::google::protobuf::Map< ::google::protobuf::int64, ::protobuf_unittest::TestIntIntMap >&
      m_sfixed64() const;
  ::google::protobuf::Map< ::google::protobuf::int64, ::protobuf_unittest::TestIntIntMap >*
      mutable_m_sfixed64();

  // map<bool, .protobuf_unittest.TestIntIntMap> m_bool = 11;
  int m_bool_size() const;
  void clear_m_bool();
  static const int kMBoolFieldNumber = 11;
  const ::google::protobuf::Map< bool, ::protobuf_unittest::TestIntIntMap >&
      m_bool() const;
  ::google::protobuf::Map< bool, ::protobuf_unittest::TestIntIntMap >*
      mutable_m_bool();

  // map<string, .protobuf_unittest.TestIntIntMap> m_string = 12;
  int m_string_size() const;
  void clear_m_string();
  static const int kMStringFieldNumber = 12;
  const ::google::protobuf::Map< ::std::string, ::protobuf_unittest::TestIntIntMap >&
      m_string() const;
  ::google::protobuf::Map< ::std::string, ::protobuf_unittest::TestIntIntMap >*
      mutable_m_string();

  // @@protoc_insertion_point(class_scope:protobuf_unittest.TestMaps)
 private:

  ::google::protobuf::internal::InternalMetadataWithArena _internal_metadata_;
  ::google::protobuf::internal::HasBits<1> _has_bits_;
  mutable int _cached_size_;
  typedef ::google::protobuf::internal::MapEntryLite<
      ::google::protobuf::int32, ::protobuf_unittest::TestIntIntMap,
      ::google::protobuf::internal::WireFormatLite::TYPE_INT32,
      ::google::protobuf::internal::WireFormatLite::TYPE_MESSAGE,
      0 >
      TestMaps_MInt32Entry;
  ::google::protobuf::internal::MapField<
      ::google::protobuf::int32, ::protobuf_unittest::TestIntIntMap,
      ::google::protobuf::internal::WireFormatLite::TYPE_INT32,
      ::google::protobuf::internal::WireFormatLite::TYPE_MESSAGE,
      0 > m_int32_;
  typedef ::google::protobuf::internal::MapEntryLite<
      ::google::protobuf::int64, ::protobuf_unittest::TestIntIntMap,
      ::google::protobuf::internal::WireFormatLite::TYPE_INT64,
      ::google::protobuf::internal::WireFormatLite::TYPE_MESSAGE,
      0 >
      TestMaps_MInt64Entry;
  ::google::protobuf::internal::MapField<
      ::google::protobuf::int64, ::protobuf_unittest::TestIntIntMap,
      ::google::protobuf::internal::WireFormatLite::TYPE_INT64,
      ::google::protobuf::internal::WireFormatLite::TYPE_MESSAGE,
      0 > m_int64_;
  typedef ::google::protobuf::internal::MapEntryLite<
      ::google::protobuf::uint32, ::protobuf_unittest::TestIntIntMap,
      ::google::protobuf::internal::WireFormatLite::TYPE_UINT32,
      ::google::protobuf::internal::WireFormatLite::TYPE_MESSAGE,
      0 >
      TestMaps_MUint32Entry;
  ::google::protobuf::internal::MapField<
      ::google::protobuf::uint32, ::protobuf_unittest::TestIntIntMap,
      ::google::protobuf::internal::WireFormatLite::TYPE_UINT32,
      ::google::protobuf::internal::WireFormatLite::TYPE_MESSAGE,
      0 > m_uint32_;
  typedef ::google::protobuf::internal::MapEntryLite<
      ::google::protobuf::uint64, ::protobuf_unittest::TestIntIntMap,
      ::google::protobuf::internal::WireFormatLite::TYPE_UINT64,
      ::google::protobuf::internal::WireFormatLite::TYPE_MESSAGE,
      0 >
      TestMaps_MUint64Entry;
  ::google::protobuf::internal::MapField<
      ::google::protobuf::uint64, ::protobuf_unittest::TestIntIntMap,
      ::google::protobuf::internal::WireFormatLite::TYPE_UINT64,
      ::google::protobuf::internal::WireFormatLite::TYPE_MESSAGE,
      0 > m_uint64_;
  typedef ::google::protobuf::internal::MapEntryLite<
      ::google::protobuf::int32, ::protobuf_unittest::TestIntIntMap,
      ::google::protobuf::internal::WireFormatLite::TYPE_SINT32,
      ::google::protobuf::internal::WireFormatLite::TYPE_MESSAGE,
      0 >
      TestMaps_MSint32Entry;
  ::google::protobuf::internal::MapField<
      ::google::protobuf::int32, ::protobuf_unittest::TestIntIntMap,
      ::google::protobuf::internal::WireFormatLite::TYPE_SINT32,
      ::google::protobuf::internal::WireFormatLite::TYPE_MESSAGE,
      0 > m_sint32_;
  typedef ::google::protobuf::internal::MapEntryLite<
      ::google::protobuf::int64, ::protobuf_unittest::TestIntIntMap,
      ::google::protobuf::internal::WireFormatLite::TYPE_SINT64,
      ::google::protobuf::internal::WireFormatLite::TYPE_MESSAGE,
      0 >
      TestMaps_MSint64Entry;
  ::google::protobuf::internal::MapField<
      ::google::protobuf::int64, ::protobuf_unittest::TestIntIntMap,
      ::google::protobuf::internal::WireFormatLite::TYPE_SINT64,
      ::google::protobuf::internal::WireFormatLite::TYPE_MESSAGE,
      0 > m_sint64_;
  typedef ::google::protobuf::internal::MapEntryLite<
      ::google::protobuf::uint32, ::protobuf_unittest::TestIntIntMap,
      ::google::protobuf::internal::WireFormatLite::TYPE_FIXED32,
      ::google::protobuf::internal::WireFormatLite::TYPE_MESSAGE,
      0 >
      TestMaps_MFixed32Entry;
  ::google::protobuf::internal::MapField<
      ::google::protobuf::uint32, ::protobuf_unittest::TestIntIntMap,
      ::google::protobuf::internal::WireFormatLite::TYPE_FIXED32,
      ::google::protobuf::internal::WireFormatLite::TYPE_MESSAGE,
      0 > m_fixed32_;
  typedef ::google::protobuf::internal::MapEntryLite<
      ::google::protobuf::uint64, ::protobuf_unittest::TestIntIntMap,
      ::google::protobuf::internal::WireFormatLite::TYPE_FIXED64,
      ::google::protobuf::internal::WireFormatLite::TYPE_MESSAGE,
      0 >
      TestMaps_MFixed64Entry;
  ::google::protobuf::internal::MapField<
      ::google::protobuf::uint64, ::protobuf_unittest::TestIntIntMap,
      ::google::protobuf::internal::WireFormatLite::TYPE_FIXED64,
      ::google::protobuf::internal::WireFormatLite::TYPE_MESSAGE,
      0 > m_fixed64_;
  typedef ::google::protobuf::internal::MapEntryLite<
      ::google::protobuf::int32, ::protobuf_unittest::TestIntIntMap,
      ::google::protobuf::internal::WireFormatLite::TYPE_SFIXED32,
      ::google::protobuf::internal::WireFormatLite::TYPE_MESSAGE,
      0 >
      TestMaps_MSfixed32Entry;
  ::google::protobuf::internal::MapField<
      ::google::protobuf::int32, ::protobuf_unittest::TestIntIntMap,
      ::google::protobuf::internal::WireFormatLite::TYPE_SFIXED32,
      ::google::protobuf::internal::WireFormatLite::TYPE_MESSAGE,
      0 > m_sfixed32_;
  typedef ::google::protobuf::internal::MapEntryLite<
      ::google::protobuf::int64, ::protobuf_unittest::TestIntIntMap,
      ::google::protobuf::internal::WireFormatLite::TYPE_SFIXED64,
      ::google::protobuf::internal::WireFormatLite::TYPE_MESSAGE,
      0 >
      TestMaps_MSfixed64Entry;
  ::google::protobuf::internal::MapField<
      ::google::protobuf::int64, ::protobuf_unittest::TestIntIntMap,
      ::google::protobuf::internal::WireFormatLite::TYPE_SFIXED64,
      ::google::protobuf::internal::WireFormatLite::TYPE_MESSAGE,
      0 > m_sfixed64_;
  typedef ::google::protobuf::internal::MapEntryLite<
      bool, ::protobuf_unittest::TestIntIntMap,
      ::google::protobuf::internal::WireFormatLite::TYPE_BOOL,
      ::google::protobuf::internal::WireFormatLite::TYPE_MESSAGE,
      0 >
      TestMaps_MBoolEntry;
  ::google::protobuf::internal::MapField<
      bool, ::protobuf_unittest::TestIntIntMap,
      ::google::protobuf::internal::WireFormatLite::TYPE_BOOL,
      ::google::protobuf::internal::WireFormatLite::TYPE_MESSAGE,
      0 > m_bool_;
  typedef ::google::protobuf::internal::MapEntryLite<
      ::std::string, ::protobuf_unittest::TestIntIntMap,
      ::google::protobuf::internal::WireFormatLite::TYPE_STRING,
      ::google::protobuf::internal::WireFormatLite::TYPE_MESSAGE,
      0 >
      TestMaps_MStringEntry;
  ::google::protobuf::internal::MapField<
      ::std::string, ::protobuf_unittest::TestIntIntMap,
      ::google::protobuf::internal::WireFormatLite::TYPE_STRING,
      ::google::protobuf::internal::WireFormatLite::TYPE_MESSAGE,
      0 > m_string_;
  friend void  protobuf_InitDefaults_google_2fprotobuf_2fmap_5fproto2_5funittest_2eproto_impl();
  friend void  protobuf_AddDesc_google_2fprotobuf_2fmap_5fproto2_5funittest_2eproto_impl();
  friend void protobuf_AssignDesc_google_2fprotobuf_2fmap_5fproto2_5funittest_2eproto();
  friend void protobuf_ShutdownFile_google_2fprotobuf_2fmap_5fproto2_5funittest_2eproto();

  void InitAsDefaultInstance();
};
extern ::google::protobuf::internal::ExplicitlyConstructed<TestMaps> TestMaps_default_instance_;

// ===================================================================


// ===================================================================

#if !PROTOBUF_INLINE_NOT_IN_HEADERS
// TestEnumMap

// map<int32, .protobuf_unittest.Proto2MapEnum> known_map_field = 101;
inline int TestEnumMap::known_map_field_size() const {
  return known_map_field_.size();
}
inline void TestEnumMap::clear_known_map_field() {
  known_map_field_.Clear();
}
inline const ::google::protobuf::Map< ::google::protobuf::int32, ::protobuf_unittest::Proto2MapEnum >&
TestEnumMap::known_map_field() const {
  // @@protoc_insertion_point(field_map:protobuf_unittest.TestEnumMap.known_map_field)
  return known_map_field_.GetMap();
}
inline ::google::protobuf::Map< ::google::protobuf::int32, ::protobuf_unittest::Proto2MapEnum >*
TestEnumMap::mutable_known_map_field() {
  // @@protoc_insertion_point(field_mutable_map:protobuf_unittest.TestEnumMap.known_map_field)
  return known_map_field_.MutableMap();
}

// map<int32, .protobuf_unittest.Proto2MapEnum> unknown_map_field = 102;
inline int TestEnumMap::unknown_map_field_size() const {
  return unknown_map_field_.size();
}
inline void TestEnumMap::clear_unknown_map_field() {
  unknown_map_field_.Clear();
}
inline const ::google::protobuf::Map< ::google::protobuf::int32, ::protobuf_unittest::Proto2MapEnum >&
TestEnumMap::unknown_map_field() const {
  // @@protoc_insertion_point(field_map:protobuf_unittest.TestEnumMap.unknown_map_field)
  return unknown_map_field_.GetMap();
}
inline ::google::protobuf::Map< ::google::protobuf::int32, ::protobuf_unittest::Proto2MapEnum >*
TestEnumMap::mutable_unknown_map_field() {
  // @@protoc_insertion_point(field_mutable_map:protobuf_unittest.TestEnumMap.unknown_map_field)
  return unknown_map_field_.MutableMap();
}

inline const TestEnumMap* TestEnumMap::internal_default_instance() {
  return &TestEnumMap_default_instance_.get();
}
// -------------------------------------------------------------------

// TestEnumMapPlusExtra

// map<int32, .protobuf_unittest.Proto2MapEnumPlusExtra> known_map_field = 101;
inline int TestEnumMapPlusExtra::known_map_field_size() const {
  return known_map_field_.size();
}
inline void TestEnumMapPlusExtra::clear_known_map_field() {
  known_map_field_.Clear();
}
inline const ::google::protobuf::Map< ::google::protobuf::int32, ::protobuf_unittest::Proto2MapEnumPlusExtra >&
TestEnumMapPlusExtra::known_map_field() const {
  // @@protoc_insertion_point(field_map:protobuf_unittest.TestEnumMapPlusExtra.known_map_field)
  return known_map_field_.GetMap();
}
inline ::google::protobuf::Map< ::google::protobuf::int32, ::protobuf_unittest::Proto2MapEnumPlusExtra >*
TestEnumMapPlusExtra::mutable_known_map_field() {
  // @@protoc_insertion_point(field_mutable_map:protobuf_unittest.TestEnumMapPlusExtra.known_map_field)
  return known_map_field_.MutableMap();
}

// map<int32, .protobuf_unittest.Proto2MapEnumPlusExtra> unknown_map_field = 102;
inline int TestEnumMapPlusExtra::unknown_map_field_size() const {
  return unknown_map_field_.size();
}
inline void TestEnumMapPlusExtra::clear_unknown_map_field() {
  unknown_map_field_.Clear();
}
inline const ::google::protobuf::Map< ::google::protobuf::int32, ::protobuf_unittest::Proto2MapEnumPlusExtra >&
TestEnumMapPlusExtra::unknown_map_field() const {
  // @@protoc_insertion_point(field_map:protobuf_unittest.TestEnumMapPlusExtra.unknown_map_field)
  return unknown_map_field_.GetMap();
}
inline ::google::protobuf::Map< ::google::protobuf::int32, ::protobuf_unittest::Proto2MapEnumPlusExtra >*
TestEnumMapPlusExtra::mutable_unknown_map_field() {
  // @@protoc_insertion_point(field_mutable_map:protobuf_unittest.TestEnumMapPlusExtra.unknown_map_field)
  return unknown_map_field_.MutableMap();
}

inline const TestEnumMapPlusExtra* TestEnumMapPlusExtra::internal_default_instance() {
  return &TestEnumMapPlusExtra_default_instance_.get();
}
// -------------------------------------------------------------------

// TestImportEnumMap

// map<int32, .protobuf_unittest_import.ImportEnumForMap> import_enum_amp = 1;
inline int TestImportEnumMap::import_enum_amp_size() const {
  return import_enum_amp_.size();
}
inline void TestImportEnumMap::clear_import_enum_amp() {
  import_enum_amp_.Clear();
}
inline const ::google::protobuf::Map< ::google::protobuf::int32, ::protobuf_unittest_import::ImportEnumForMap >&
TestImportEnumMap::import_enum_amp() const {
  // @@protoc_insertion_point(field_map:protobuf_unittest.TestImportEnumMap.import_enum_amp)
  return import_enum_amp_.GetMap();
}
inline ::google::protobuf::Map< ::google::protobuf::int32, ::protobuf_unittest_import::ImportEnumForMap >*
TestImportEnumMap::mutable_import_enum_amp() {
  // @@protoc_insertion_point(field_mutable_map:protobuf_unittest.TestImportEnumMap.import_enum_amp)
  return import_enum_amp_.MutableMap();
}

inline const TestImportEnumMap* TestImportEnumMap::internal_default_instance() {
  return &TestImportEnumMap_default_instance_.get();
}
// -------------------------------------------------------------------

// TestIntIntMap

// map<int32, int32> m = 1;
inline int TestIntIntMap::m_size() const {
  return m_.size();
}
inline void TestIntIntMap::clear_m() {
  m_.Clear();
}
inline const ::google::protobuf::Map< ::google::protobuf::int32, ::google::protobuf::int32 >&
TestIntIntMap::m() const {
  // @@protoc_insertion_point(field_map:protobuf_unittest.TestIntIntMap.m)
  return m_.GetMap();
}
inline ::google::protobuf::Map< ::google::protobuf::int32, ::google::protobuf::int32 >*
TestIntIntMap::mutable_m() {
  // @@protoc_insertion_point(field_mutable_map:protobuf_unittest.TestIntIntMap.m)
  return m_.MutableMap();
}

inline const TestIntIntMap* TestIntIntMap::internal_default_instance() {
  return &TestIntIntMap_default_instance_.get();
}
// -------------------------------------------------------------------

// TestMaps

// map<int32, .protobuf_unittest.TestIntIntMap> m_int32 = 1;
inline int TestMaps::m_int32_size() const {
  return m_int32_.size();
}
inline void TestMaps::clear_m_int32() {
  m_int32_.Clear();
}
inline const ::google::protobuf::Map< ::google::protobuf::int32, ::protobuf_unittest::TestIntIntMap >&
TestMaps::m_int32() const {
  // @@protoc_insertion_point(field_map:protobuf_unittest.TestMaps.m_int32)
  return m_int32_.GetMap();
}
inline ::google::protobuf::Map< ::google::protobuf::int32, ::protobuf_unittest::TestIntIntMap >*
TestMaps::mutable_m_int32() {
  // @@protoc_insertion_point(field_mutable_map:protobuf_unittest.TestMaps.m_int32)
  return m_int32_.MutableMap();
}

// map<int64, .protobuf_unittest.TestIntIntMap> m_int64 = 2;
inline int TestMaps::m_int64_size() const {
  return m_int64_.size();
}
inline void TestMaps::clear_m_int64() {
  m_int64_.Clear();
}
inline const ::google::protobuf::Map< ::google::protobuf::int64, ::protobuf_unittest::TestIntIntMap >&
TestMaps::m_int64() const {
  // @@protoc_insertion_point(field_map:protobuf_unittest.TestMaps.m_int64)
  return m_int64_.GetMap();
}
inline ::google::protobuf::Map< ::google::protobuf::int64, ::protobuf_unittest::TestIntIntMap >*
TestMaps::mutable_m_int64() {
  // @@protoc_insertion_point(field_mutable_map:protobuf_unittest.TestMaps.m_int64)
  return m_int64_.MutableMap();
}

// map<uint32, .protobuf_unittest.TestIntIntMap> m_uint32 = 3;
inline int TestMaps::m_uint32_size() const {
  return m_uint32_.size();
}
inline void TestMaps::clear_m_uint32() {
  m_uint32_.Clear();
}
inline const ::google::protobuf::Map< ::google::protobuf::uint32, ::protobuf_unittest::TestIntIntMap >&
TestMaps::m_uint32() const {
  // @@protoc_insertion_point(field_map:protobuf_unittest.TestMaps.m_uint32)
  return m_uint32_.GetMap();
}
inline ::google::protobuf::Map< ::google::protobuf::uint32, ::protobuf_unittest::TestIntIntMap >*
TestMaps::mutable_m_uint32() {
  // @@protoc_insertion_point(field_mutable_map:protobuf_unittest.TestMaps.m_uint32)
  return m_uint32_.MutableMap();
}

// map<uint64, .protobuf_unittest.TestIntIntMap> m_uint64 = 4;
inline int TestMaps::m_uint64_size() const {
  return m_uint64_.size();
}
inline void TestMaps::clear_m_uint64() {
  m_uint64_.Clear();
}
inline const ::google::protobuf::Map< ::google::protobuf::uint64, ::protobuf_unittest::TestIntIntMap >&
TestMaps::m_uint64() const {
  // @@protoc_insertion_point(field_map:protobuf_unittest.TestMaps.m_uint64)
  return m_uint64_.GetMap();
}
inline ::google::protobuf::Map< ::google::protobuf::uint64, ::protobuf_unittest::TestIntIntMap >*
TestMaps::mutable_m_uint64() {
  // @@protoc_insertion_point(field_mutable_map:protobuf_unittest.TestMaps.m_uint64)
  return m_uint64_.MutableMap();
}

// map<sint32, .protobuf_unittest.TestIntIntMap> m_sint32 = 5;
inline int TestMaps::m_sint32_size() const {
  return m_sint32_.size();
}
inline void TestMaps::clear_m_sint32() {
  m_sint32_.Clear();
}
inline const ::google::protobuf::Map< ::google::protobuf::int32, ::protobuf_unittest::TestIntIntMap >&
TestMaps::m_sint32() const {
  // @@protoc_insertion_point(field_map:protobuf_unittest.TestMaps.m_sint32)
  return m_sint32_.GetMap();
}
inline ::google::protobuf::Map< ::google::protobuf::int32, ::protobuf_unittest::TestIntIntMap >*
TestMaps::mutable_m_sint32() {
  // @@protoc_insertion_point(field_mutable_map:protobuf_unittest.TestMaps.m_sint32)
  return m_sint32_.MutableMap();
}

// map<sint64, .protobuf_unittest.TestIntIntMap> m_sint64 = 6;
inline int TestMaps::m_sint64_size() const {
  return m_sint64_.size();
}
inline void TestMaps::clear_m_sint64() {
  m_sint64_.Clear();
}
inline const ::google::protobuf::Map< ::google::protobuf::int64, ::protobuf_unittest::TestIntIntMap >&
TestMaps::m_sint64() const {
  // @@protoc_insertion_point(field_map:protobuf_unittest.TestMaps.m_sint64)
  return m_sint64_.GetMap();
}
inline ::google::protobuf::Map< ::google::protobuf::int64, ::protobuf_unittest::TestIntIntMap >*
TestMaps::mutable_m_sint64() {
  // @@protoc_insertion_point(field_mutable_map:protobuf_unittest.TestMaps.m_sint64)
  return m_sint64_.MutableMap();
}

// map<fixed32, .protobuf_unittest.TestIntIntMap> m_fixed32 = 7;
inline int TestMaps::m_fixed32_size() const {
  return m_fixed32_.size();
}
inline void TestMaps::clear_m_fixed32() {
  m_fixed32_.Clear();
}
inline const ::google::protobuf::Map< ::google::protobuf::uint32, ::protobuf_unittest::TestIntIntMap >&
TestMaps::m_fixed32() const {
  // @@protoc_insertion_point(field_map:protobuf_unittest.TestMaps.m_fixed32)
  return m_fixed32_.GetMap();
}
inline ::google::protobuf::Map< ::google::protobuf::uint32, ::protobuf_unittest::TestIntIntMap >*
TestMaps::mutable_m_fixed32() {
  // @@protoc_insertion_point(field_mutable_map:protobuf_unittest.TestMaps.m_fixed32)
  return m_fixed32_.MutableMap();
}

// map<fixed64, .protobuf_unittest.TestIntIntMap> m_fixed64 = 8;
inline int TestMaps::m_fixed64_size() const {
  return m_fixed64_.size();
}
inline void TestMaps::clear_m_fixed64() {
  m_fixed64_.Clear();
}
inline const ::google::protobuf::Map< ::google::protobuf::uint64, ::protobuf_unittest::TestIntIntMap >&
TestMaps::m_fixed64() const {
  // @@protoc_insertion_point(field_map:protobuf_unittest.TestMaps.m_fixed64)
  return m_fixed64_.GetMap();
}
inline ::google::protobuf::Map< ::google::protobuf::uint64, ::protobuf_unittest::TestIntIntMap >*
TestMaps::mutable_m_fixed64() {
  // @@protoc_insertion_point(field_mutable_map:protobuf_unittest.TestMaps.m_fixed64)
  return m_fixed64_.MutableMap();
}

// map<sfixed32, .protobuf_unittest.TestIntIntMap> m_sfixed32 = 9;
inline int TestMaps::m_sfixed32_size() const {
  return m_sfixed32_.size();
}
inline void TestMaps::clear_m_sfixed32() {
  m_sfixed32_.Clear();
}
inline const ::google::protobuf::Map< ::google::protobuf::int32, ::protobuf_unittest::TestIntIntMap >&
TestMaps::m_sfixed32() const {
  // @@protoc_insertion_point(field_map:protobuf_unittest.TestMaps.m_sfixed32)
  return m_sfixed32_.GetMap();
}
inline ::google::protobuf::Map< ::google::protobuf::int32, ::protobuf_unittest::TestIntIntMap >*
TestMaps::mutable_m_sfixed32() {
  // @@protoc_insertion_point(field_mutable_map:protobuf_unittest.TestMaps.m_sfixed32)
  return m_sfixed32_.MutableMap();
}

// map<sfixed64, .protobuf_unittest.TestIntIntMap> m_sfixed64 = 10;
inline int TestMaps::m_sfixed64_size() const {
  return m_sfixed64_.size();
}
inline void TestMaps::clear_m_sfixed64() {
  m_sfixed64_.Clear();
}
inline const ::google::protobuf::Map< ::google::protobuf::int64, ::protobuf_unittest::TestIntIntMap >&
TestMaps::m_sfixed64() const {
  // @@protoc_insertion_point(field_map:protobuf_unittest.TestMaps.m_sfixed64)
  return m_sfixed64_.GetMap();
}
inline ::google::protobuf::Map< ::google::protobuf::int64, ::protobuf_unittest::TestIntIntMap >*
TestMaps::mutable_m_sfixed64() {
  // @@protoc_insertion_point(field_mutable_map:protobuf_unittest.TestMaps.m_sfixed64)
  return m_sfixed64_.MutableMap();
}

// map<bool, .protobuf_unittest.TestIntIntMap> m_bool = 11;
inline int TestMaps::m_bool_size() const {
  return m_bool_.size();
}
inline void TestMaps::clear_m_bool() {
  m_bool_.Clear();
}
inline const ::google::protobuf::Map< bool, ::protobuf_unittest::TestIntIntMap >&
TestMaps::m_bool() const {
  // @@protoc_insertion_point(field_map:protobuf_unittest.TestMaps.m_bool)
  return m_bool_.GetMap();
}
inline ::google::protobuf::Map< bool, ::protobuf_unittest::TestIntIntMap >*
TestMaps::mutable_m_bool() {
  // @@protoc_insertion_point(field_mutable_map:protobuf_unittest.TestMaps.m_bool)
  return m_bool_.MutableMap();
}

// map<string, .protobuf_unittest.TestIntIntMap> m_string = 12;
inline int TestMaps::m_string_size() const {
  return m_string_.size();
}
inline void TestMaps::clear_m_string() {
  m_string_.Clear();
}
inline const ::google::protobuf::Map< ::std::string, ::protobuf_unittest::TestIntIntMap >&
TestMaps::m_string() const {
  // @@protoc_insertion_point(field_map:protobuf_unittest.TestMaps.m_string)
  return m_string_.GetMap();
}
inline ::google::protobuf::Map< ::std::string, ::protobuf_unittest::TestIntIntMap >*
TestMaps::mutable_m_string() {
  // @@protoc_insertion_point(field_mutable_map:protobuf_unittest.TestMaps.m_string)
  return m_string_.MutableMap();
}

inline const TestMaps* TestMaps::internal_default_instance() {
  return &TestMaps_default_instance_.get();
}
#endif  // !PROTOBUF_INLINE_NOT_IN_HEADERS
// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------


// @@protoc_insertion_point(namespace_scope)

}  // namespace protobuf_unittest

#ifndef SWIG
namespace google {
namespace protobuf {

template <> struct is_proto_enum< ::protobuf_unittest::Proto2MapEnum> : ::google::protobuf::internal::true_type {};
template <>
inline const EnumDescriptor* GetEnumDescriptor< ::protobuf_unittest::Proto2MapEnum>() {
  return ::protobuf_unittest::Proto2MapEnum_descriptor();
}
template <> struct is_proto_enum< ::protobuf_unittest::Proto2MapEnumPlusExtra> : ::google::protobuf::internal::true_type {};
template <>
inline const EnumDescriptor* GetEnumDescriptor< ::protobuf_unittest::Proto2MapEnumPlusExtra>() {
  return ::protobuf_unittest::Proto2MapEnumPlusExtra_descriptor();
}

}  // namespace protobuf
}  // namespace google
#endif  // SWIG

// @@protoc_insertion_point(global_scope)

#endif  // PROTOBUF_google_2fprotobuf_2fmap_5fproto2_5funittest_2eproto__INCLUDED

// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: google/protobuf/unittest_import_public_lite.proto

#ifndef PROTOBUF_google_2fprotobuf_2funittest_5fimport_5fpublic_5flite_2eproto__INCLUDED
#define PROTOBUF_google_2fprotobuf_2funittest_5fimport_5fpublic_5flite_2eproto__INCLUDED

#include <string>

#include <google/protobuf/stubs/common.h>

#if GOOGLE_PROTOBUF_VERSION < 3001000
#error This file was generated by a newer version of protoc which is
#error incompatible with your Protocol Buffer headers.  Please update
#error your headers.
#endif
#if 3001000 < GOOGLE_PROTOBUF_MIN_PROTOC_VERSION
#error This file was generated by an older version of protoc which is
#error incompatible with your Protocol Buffer headers.  Please
#error regenerate this file with a newer version of protoc.
#endif

#include <google/protobuf/arena.h>
#include <google/protobuf/arenastring.h>
#include <google/protobuf/generated_message_util.h>
#include <google/protobuf/message_lite.h>
#include <google/protobuf/repeated_field.h>
#include <google/protobuf/extension_set.h>
// @@protoc_insertion_point(includes)

namespace protobuf_unittest_import {

// Internal implementation detail -- do not call these.
void protobuf_AddDesc_google_2fprotobuf_2funittest_5fimport_5fpublic_5flite_2eproto();
void protobuf_InitDefaults_google_2fprotobuf_2funittest_5fimport_5fpublic_5flite_2eproto();
void protobuf_AssignDesc_google_2fprotobuf_2funittest_5fimport_5fpublic_5flite_2eproto();
void protobuf_ShutdownFile_google_2fprotobuf_2funittest_5fimport_5fpublic_5flite_2eproto();

class PublicImportMessageLite;

// ===================================================================

class PublicImportMessageLite : public ::google::protobuf::MessageLite /* @@protoc_insertion_point(class_definition:protobuf_unittest_import.PublicImportMessageLite) */ {
 public:
  PublicImportMessageLite();
  virtual ~PublicImportMessageLite();

  PublicImportMessageLite(const PublicImportMessageLite& from);

  inline PublicImportMessageLite& operator=(const PublicImportMessageLite& from) {
    CopyFrom(from);
    return *this;
  }

  inline const ::std::string& unknown_fields() const {
    return _unknown_fields_.GetNoArena(
        &::google::protobuf::internal::GetEmptyStringAlreadyInited());
  }

  inline ::std::string* mutable_unknown_fields() {
    return _unknown_fields_.MutableNoArena(
        &::google::protobuf::internal::GetEmptyStringAlreadyInited());
  }

  static const PublicImportMessageLite& default_instance();

  static const PublicImportMessageLite* internal_default_instance();

  void Swap(PublicImportMessageLite* other);

  // implements Message ----------------------------------------------

  inline PublicImportMessageLite* New() const { return New(NULL); }

  PublicImportMessageLite* New(::google::protobuf::Arena* arena) const;
  void CheckTypeAndMergeFrom(const ::google::protobuf::MessageLite& from);
  void CopyFrom(const PublicImportMessageLite& from);
  void MergeFrom(const PublicImportMessageLite& from);
  void Clear();
  bool IsInitialized() const;

  size_t ByteSizeLong() const;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input);
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const;
  void DiscardUnknownFields();
  int GetCachedSize() const { return _cached_size_; }
  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const;
  void InternalSwap(PublicImportMessageLite* other);
  void UnsafeMergeFrom(const PublicImportMessageLite& from);
  private:
  inline ::google::protobuf::Arena* GetArenaNoVirtual() const {
    return _arena_ptr_;
  }
  inline ::google::protobuf::Arena* MaybeArenaPtr() const {
    return _arena_ptr_;
  }
  public:

  ::std::string GetTypeName() const;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // optional int32 e = 1;
  bool has_e() const;
  void clear_e();
  static const int kEFieldNumber = 1;
  ::google::protobuf::int32 e() const;
  void set_e(::google::protobuf::int32 value);

  // @@protoc_insertion_point(class_scope:protobuf_unittest_import.PublicImportMessageLite)
 private:
  inline void set_has_e();
  inline void clear_has_e();

  ::google::protobuf::internal::ArenaStringPtr _unknown_fields_;
  ::google::protobuf::Arena* _arena_ptr_;

  ::google::protobuf::internal::HasBits<1> _has_bits_;
  mutable int _cached_size_;
  ::google::protobuf::int32 e_;
  friend void  protobuf_InitDefaults_google_2fprotobuf_2funittest_5fimport_5fpublic_5flite_2eproto_impl();
  friend void  protobuf_AddDesc_google_2fprotobuf_2funittest_5fimport_5fpublic_5flite_2eproto_impl();
  friend void protobuf_AssignDesc_google_2fprotobuf_2funittest_5fimport_5fpublic_5flite_2eproto();
  friend void protobuf_ShutdownFile_google_2fprotobuf_2funittest_5fimport_5fpublic_5flite_2eproto();

  void InitAsDefaultInstance();
};
extern ::google::protobuf::internal::ExplicitlyConstructed<PublicImportMessageLite> PublicImportMessageLite_default_instance_;

// ===================================================================


// ===================================================================

#if !PROTOBUF_INLINE_NOT_IN_HEADERS
// PublicImportMessageLite

// optional int32 e = 1;
inline bool PublicImportMessageLite::has_e() const {
  return (_has_bits_[0] & 0x00000001u) != 0;
}
inline void PublicImportMessageLite::set_has_e() {
  _has_bits_[0] |= 0x00000001u;
}
inline void PublicImportMessageLite::clear_has_e() {
  _has_bits_[0] &= ~0x00000001u;
}
inline void PublicImportMessageLite::clear_e() {
  e_ = 0;
  clear_has_e();
}
inline ::google::protobuf::int32 PublicImportMessageLite::e() const {
  // @@protoc_insertion_point(field_get:protobuf_unittest_import.PublicImportMessageLite.e)
  return e_;
}
inline void PublicImportMessageLite::set_e(::google::protobuf::int32 value) {
  set_has_e();
  e_ = value;
  // @@protoc_insertion_point(field_set:protobuf_unittest_import.PublicImportMessageLite.e)
}

inline const PublicImportMessageLite* PublicImportMessageLite::internal_default_instance() {
  return &PublicImportMessageLite_default_instance_.get();
}
#endif  // !PROTOBUF_INLINE_NOT_IN_HEADERS

// @@protoc_insertion_point(namespace_scope)

}  // namespace protobuf_unittest_import

// @@protoc_insertion_point(global_scope)

#endif  // PROTOBUF_google_2fprotobuf_2funittest_5fimport_5fpublic_5flite_2eproto__INCLUDED

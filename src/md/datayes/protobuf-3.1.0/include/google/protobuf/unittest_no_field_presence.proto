// Protocol Buffers - Google's data interchange format
// Copyright 2008 Google Inc.  All rights reserved.
// https://developers.google.com/protocol-buffers/
//
// Redistribution and use in source and binary forms, with or without
// modification, are permitted provided that the following conditions are
// met:
//
//     * Redistributions of source code must retain the above copyright
// notice, this list of conditions and the following disclaimer.
//     * Redistributions in binary form must reproduce the above
// copyright notice, this list of conditions and the following disclaimer
// in the documentation and/or other materials provided with the
// distribution.
//     * Neither the name of Google Inc. nor the names of its
// contributors may be used to endorse or promote products derived from
// this software without specific prior written permission.
//
// THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS
// "AS IS" AND ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT
// LIMITED TO, THE IMPLIED WARRANTIES OF MERCHANTABILITY AND FITNESS FOR
// A PARTICULAR PURPOSE ARE DISCLAIMED. IN NO EVENT SHALL THE COPYRIGHT
// OWNER OR CONTRIBUTORS BE LIABLE FOR ANY DIRECT, INDIRECT, INCIDENTAL,
// SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES (INCLUDING, BUT NOT
// LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES; LOSS OF USE,
// DATA, OR PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND ON ANY
// THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT
// (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT OF THE USE
// OF THIS SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.

// A proto file used to test a message type with no explicit field presence.

syntax = "proto3";

// We want to test embedded proto2 messages, so include some proto2 types.
import "google/protobuf/unittest.proto";

package proto2_nofieldpresence_unittest;

// This proto includes every type of field in both singular and repeated
// forms.
message TestAllTypes {
  message NestedMessage {
    int32 bb = 1;
  }

  enum NestedEnum {
    FOO = 0;
    BAR = 1;
    BAZ = 2;
  }

  // Singular
  // TODO: remove 'optional' labels as soon as CL 69188077 is LGTM'd to make
  // 'optional' optional.
     int32 optional_int32    =  1;
     int64 optional_int64    =  2;
    uint32 optional_uint32   =  3;
    uint64 optional_uint64   =  4;
    sint32 optional_sint32   =  5;
    sint64 optional_sint64   =  6;
   fixed32 optional_fixed32  =  7;
   fixed64 optional_fixed64  =  8;
  sfixed32 optional_sfixed32 =  9;
  sfixed64 optional_sfixed64 = 10;
     float optional_float    = 11;
    double optional_double   = 12;
      bool optional_bool     = 13;
    string optional_string   = 14;
     bytes optional_bytes    = 15;

  NestedMessage                        optional_nested_message  = 18;
  ForeignMessage                       optional_foreign_message = 19;
  protobuf_unittest.TestAllTypes         optional_proto2_message = 20;

  NestedEnum                           optional_nested_enum     = 21;
  ForeignEnum                          optional_foreign_enum    = 22;
  // N.B.: proto2-enum-type fields not allowed, because their default values
  // might not be zero.
  //optional protobuf_unittest.ForeignEnum          optional_proto2_enum     = 23;

  string optional_string_piece = 24 [ctype=STRING_PIECE];
  string optional_cord = 25 [ctype=CORD];

  NestedMessage optional_lazy_message = 30 [lazy=true];

  // Repeated
  repeated    int32 repeated_int32    = 31;
  repeated    int64 repeated_int64    = 32;
  repeated   uint32 repeated_uint32   = 33;
  repeated   uint64 repeated_uint64   = 34;
  repeated   sint32 repeated_sint32   = 35;
  repeated   sint64 repeated_sint64   = 36;
  repeated  fixed32 repeated_fixed32  = 37;
  repeated  fixed64 repeated_fixed64  = 38;
  repeated sfixed32 repeated_sfixed32 = 39;
  repeated sfixed64 repeated_sfixed64 = 40;
  repeated    float repeated_float    = 41;
  repeated   double repeated_double   = 42;
  repeated     bool repeated_bool     = 43;
  repeated   string repeated_string   = 44;
  repeated    bytes repeated_bytes    = 45;

  repeated NestedMessage                        repeated_nested_message  = 48;
  repeated ForeignMessage                       repeated_foreign_message = 49;
  repeated protobuf_unittest.TestAllTypes         repeated_proto2_message  = 50;

  repeated NestedEnum                           repeated_nested_enum     = 51;
  repeated ForeignEnum                          repeated_foreign_enum    = 52;

  repeated string repeated_string_piece = 54 [ctype=STRING_PIECE];
  repeated string repeated_cord = 55 [ctype=CORD];

  repeated NestedMessage repeated_lazy_message = 57 [lazy=true];

  oneof oneof_field {
    uint32 oneof_uint32 = 111;
    NestedMessage oneof_nested_message = 112;
    string oneof_string = 113;
    NestedEnum oneof_enum = 114;
  }
}

message TestProto2Required {
  protobuf_unittest.TestRequired proto2 = 1;
}

// Define these after TestAllTypes to make sure the compiler can handle
// that.
message ForeignMessage {
  int32 c = 1;
}

enum ForeignEnum {
  FOREIGN_FOO = 0;
  FOREIGN_BAR = 1;
  FOREIGN_BAZ = 2;
}

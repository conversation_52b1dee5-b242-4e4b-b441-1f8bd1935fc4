// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: google/protobuf/unittest_no_field_presence.proto

#ifndef PROTOBUF_google_2fprotobuf_2funittest_5fno_5ffield_5fpresence_2eproto__INCLUDED
#define PROTOBUF_google_2fprotobuf_2funittest_5fno_5ffield_5fpresence_2eproto__INCLUDED

#include <string>

#include <google/protobuf/stubs/common.h>

#if GOOGLE_PROTOBUF_VERSION < 3001000
#error This file was generated by a newer version of protoc which is
#error incompatible with your Protocol Buffer headers.  Please update
#error your headers.
#endif
#if 3001000 < GOOGLE_PROTOBUF_MIN_PROTOC_VERSION
#error This file was generated by an older version of protoc which is
#error incompatible with your Protocol Buffer headers.  Please
#error regenerate this file with a newer version of protoc.
#endif

#include <google/protobuf/arena.h>
#include <google/protobuf/arenastring.h>
#include <google/protobuf/generated_message_util.h>
#include <google/protobuf/metadata.h>
#include <google/protobuf/message.h>
#include <google/protobuf/repeated_field.h>
#include <google/protobuf/extension_set.h>
#include <google/protobuf/generated_enum_reflection.h>
#include <google/protobuf/unknown_field_set.h>
#include <google/protobuf/unittest.pb.h>
// @@protoc_insertion_point(includes)

namespace proto2_nofieldpresence_unittest {

// Internal implementation detail -- do not call these.
void protobuf_AddDesc_google_2fprotobuf_2funittest_5fno_5ffield_5fpresence_2eproto();
void protobuf_InitDefaults_google_2fprotobuf_2funittest_5fno_5ffield_5fpresence_2eproto();
void protobuf_AssignDesc_google_2fprotobuf_2funittest_5fno_5ffield_5fpresence_2eproto();
void protobuf_ShutdownFile_google_2fprotobuf_2funittest_5fno_5ffield_5fpresence_2eproto();

class ForeignMessage;
class TestAllTypes;
class TestAllTypes_NestedMessage;
class TestProto2Required;

enum TestAllTypes_NestedEnum {
  TestAllTypes_NestedEnum_FOO = 0,
  TestAllTypes_NestedEnum_BAR = 1,
  TestAllTypes_NestedEnum_BAZ = 2,
  TestAllTypes_NestedEnum_TestAllTypes_NestedEnum_INT_MIN_SENTINEL_DO_NOT_USE_ = ::google::protobuf::kint32min,
  TestAllTypes_NestedEnum_TestAllTypes_NestedEnum_INT_MAX_SENTINEL_DO_NOT_USE_ = ::google::protobuf::kint32max
};
bool TestAllTypes_NestedEnum_IsValid(int value);
const TestAllTypes_NestedEnum TestAllTypes_NestedEnum_NestedEnum_MIN = TestAllTypes_NestedEnum_FOO;
const TestAllTypes_NestedEnum TestAllTypes_NestedEnum_NestedEnum_MAX = TestAllTypes_NestedEnum_BAZ;
const int TestAllTypes_NestedEnum_NestedEnum_ARRAYSIZE = TestAllTypes_NestedEnum_NestedEnum_MAX + 1;

const ::google::protobuf::EnumDescriptor* TestAllTypes_NestedEnum_descriptor();
inline const ::std::string& TestAllTypes_NestedEnum_Name(TestAllTypes_NestedEnum value) {
  return ::google::protobuf::internal::NameOfEnum(
    TestAllTypes_NestedEnum_descriptor(), value);
}
inline bool TestAllTypes_NestedEnum_Parse(
    const ::std::string& name, TestAllTypes_NestedEnum* value) {
  return ::google::protobuf::internal::ParseNamedEnum<TestAllTypes_NestedEnum>(
    TestAllTypes_NestedEnum_descriptor(), name, value);
}
enum ForeignEnum {
  FOREIGN_FOO = 0,
  FOREIGN_BAR = 1,
  FOREIGN_BAZ = 2,
  ForeignEnum_INT_MIN_SENTINEL_DO_NOT_USE_ = ::google::protobuf::kint32min,
  ForeignEnum_INT_MAX_SENTINEL_DO_NOT_USE_ = ::google::protobuf::kint32max
};
bool ForeignEnum_IsValid(int value);
const ForeignEnum ForeignEnum_MIN = FOREIGN_FOO;
const ForeignEnum ForeignEnum_MAX = FOREIGN_BAZ;
const int ForeignEnum_ARRAYSIZE = ForeignEnum_MAX + 1;

const ::google::protobuf::EnumDescriptor* ForeignEnum_descriptor();
inline const ::std::string& ForeignEnum_Name(ForeignEnum value) {
  return ::google::protobuf::internal::NameOfEnum(
    ForeignEnum_descriptor(), value);
}
inline bool ForeignEnum_Parse(
    const ::std::string& name, ForeignEnum* value) {
  return ::google::protobuf::internal::ParseNamedEnum<ForeignEnum>(
    ForeignEnum_descriptor(), name, value);
}
// ===================================================================

class TestAllTypes_NestedMessage : public ::google::protobuf::Message /* @@protoc_insertion_point(class_definition:proto2_nofieldpresence_unittest.TestAllTypes.NestedMessage) */ {
 public:
  TestAllTypes_NestedMessage();
  virtual ~TestAllTypes_NestedMessage();

  TestAllTypes_NestedMessage(const TestAllTypes_NestedMessage& from);

  inline TestAllTypes_NestedMessage& operator=(const TestAllTypes_NestedMessage& from) {
    CopyFrom(from);
    return *this;
  }

  static const ::google::protobuf::Descriptor* descriptor();
  static const TestAllTypes_NestedMessage& default_instance();

  static const TestAllTypes_NestedMessage* internal_default_instance();

  void Swap(TestAllTypes_NestedMessage* other);

  // implements Message ----------------------------------------------

  inline TestAllTypes_NestedMessage* New() const { return New(NULL); }

  TestAllTypes_NestedMessage* New(::google::protobuf::Arena* arena) const;
  void CopyFrom(const ::google::protobuf::Message& from);
  void MergeFrom(const ::google::protobuf::Message& from);
  void CopyFrom(const TestAllTypes_NestedMessage& from);
  void MergeFrom(const TestAllTypes_NestedMessage& from);
  void Clear();
  bool IsInitialized() const;

  size_t ByteSizeLong() const;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input);
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const;
  ::google::protobuf::uint8* InternalSerializeWithCachedSizesToArray(
      bool deterministic, ::google::protobuf::uint8* output) const;
  ::google::protobuf::uint8* SerializeWithCachedSizesToArray(::google::protobuf::uint8* output) const {
    return InternalSerializeWithCachedSizesToArray(false, output);
  }
  int GetCachedSize() const { return _cached_size_; }
  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const;
  void InternalSwap(TestAllTypes_NestedMessage* other);
  void UnsafeMergeFrom(const TestAllTypes_NestedMessage& from);
  private:
  inline ::google::protobuf::Arena* GetArenaNoVirtual() const {
    return _internal_metadata_.arena();
  }
  inline void* MaybeArenaPtr() const {
    return _internal_metadata_.raw_arena_ptr();
  }
  public:

  ::google::protobuf::Metadata GetMetadata() const;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // optional int32 bb = 1;
  void clear_bb();
  static const int kBbFieldNumber = 1;
  ::google::protobuf::int32 bb() const;
  void set_bb(::google::protobuf::int32 value);

  // @@protoc_insertion_point(class_scope:proto2_nofieldpresence_unittest.TestAllTypes.NestedMessage)
 private:

  ::google::protobuf::internal::InternalMetadataWithArena _internal_metadata_;
  ::google::protobuf::int32 bb_;
  mutable int _cached_size_;
  friend void  protobuf_InitDefaults_google_2fprotobuf_2funittest_5fno_5ffield_5fpresence_2eproto_impl();
  friend void  protobuf_AddDesc_google_2fprotobuf_2funittest_5fno_5ffield_5fpresence_2eproto_impl();
  friend void protobuf_AssignDesc_google_2fprotobuf_2funittest_5fno_5ffield_5fpresence_2eproto();
  friend void protobuf_ShutdownFile_google_2fprotobuf_2funittest_5fno_5ffield_5fpresence_2eproto();

  void InitAsDefaultInstance();
};
extern ::google::protobuf::internal::ExplicitlyConstructed<TestAllTypes_NestedMessage> TestAllTypes_NestedMessage_default_instance_;

// -------------------------------------------------------------------

class TestAllTypes : public ::google::protobuf::Message /* @@protoc_insertion_point(class_definition:proto2_nofieldpresence_unittest.TestAllTypes) */ {
 public:
  TestAllTypes();
  virtual ~TestAllTypes();

  TestAllTypes(const TestAllTypes& from);

  inline TestAllTypes& operator=(const TestAllTypes& from) {
    CopyFrom(from);
    return *this;
  }

  static const ::google::protobuf::Descriptor* descriptor();
  static const TestAllTypes& default_instance();

  enum OneofFieldCase {
    kOneofUint32 = 111,
    kOneofNestedMessage = 112,
    kOneofString = 113,
    kOneofEnum = 114,
    ONEOF_FIELD_NOT_SET = 0,
  };

  static const TestAllTypes* internal_default_instance();

  void Swap(TestAllTypes* other);

  // implements Message ----------------------------------------------

  inline TestAllTypes* New() const { return New(NULL); }

  TestAllTypes* New(::google::protobuf::Arena* arena) const;
  void CopyFrom(const ::google::protobuf::Message& from);
  void MergeFrom(const ::google::protobuf::Message& from);
  void CopyFrom(const TestAllTypes& from);
  void MergeFrom(const TestAllTypes& from);
  void Clear();
  bool IsInitialized() const;

  size_t ByteSizeLong() const;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input);
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const;
  ::google::protobuf::uint8* InternalSerializeWithCachedSizesToArray(
      bool deterministic, ::google::protobuf::uint8* output) const;
  ::google::protobuf::uint8* SerializeWithCachedSizesToArray(::google::protobuf::uint8* output) const {
    return InternalSerializeWithCachedSizesToArray(false, output);
  }
  int GetCachedSize() const { return _cached_size_; }
  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const;
  void InternalSwap(TestAllTypes* other);
  void UnsafeMergeFrom(const TestAllTypes& from);
  private:
  inline ::google::protobuf::Arena* GetArenaNoVirtual() const {
    return _internal_metadata_.arena();
  }
  inline void* MaybeArenaPtr() const {
    return _internal_metadata_.raw_arena_ptr();
  }
  public:

  ::google::protobuf::Metadata GetMetadata() const;

  // nested types ----------------------------------------------------

  typedef TestAllTypes_NestedMessage NestedMessage;

  typedef TestAllTypes_NestedEnum NestedEnum;
  static const NestedEnum FOO =
    TestAllTypes_NestedEnum_FOO;
  static const NestedEnum BAR =
    TestAllTypes_NestedEnum_BAR;
  static const NestedEnum BAZ =
    TestAllTypes_NestedEnum_BAZ;
  static inline bool NestedEnum_IsValid(int value) {
    return TestAllTypes_NestedEnum_IsValid(value);
  }
  static const NestedEnum NestedEnum_MIN =
    TestAllTypes_NestedEnum_NestedEnum_MIN;
  static const NestedEnum NestedEnum_MAX =
    TestAllTypes_NestedEnum_NestedEnum_MAX;
  static const int NestedEnum_ARRAYSIZE =
    TestAllTypes_NestedEnum_NestedEnum_ARRAYSIZE;
  static inline const ::google::protobuf::EnumDescriptor*
  NestedEnum_descriptor() {
    return TestAllTypes_NestedEnum_descriptor();
  }
  static inline const ::std::string& NestedEnum_Name(NestedEnum value) {
    return TestAllTypes_NestedEnum_Name(value);
  }
  static inline bool NestedEnum_Parse(const ::std::string& name,
      NestedEnum* value) {
    return TestAllTypes_NestedEnum_Parse(name, value);
  }

  // accessors -------------------------------------------------------

  // optional int32 optional_int32 = 1;
  void clear_optional_int32();
  static const int kOptionalInt32FieldNumber = 1;
  ::google::protobuf::int32 optional_int32() const;
  void set_optional_int32(::google::protobuf::int32 value);

  // optional int64 optional_int64 = 2;
  void clear_optional_int64();
  static const int kOptionalInt64FieldNumber = 2;
  ::google::protobuf::int64 optional_int64() const;
  void set_optional_int64(::google::protobuf::int64 value);

  // optional uint32 optional_uint32 = 3;
  void clear_optional_uint32();
  static const int kOptionalUint32FieldNumber = 3;
  ::google::protobuf::uint32 optional_uint32() const;
  void set_optional_uint32(::google::protobuf::uint32 value);

  // optional uint64 optional_uint64 = 4;
  void clear_optional_uint64();
  static const int kOptionalUint64FieldNumber = 4;
  ::google::protobuf::uint64 optional_uint64() const;
  void set_optional_uint64(::google::protobuf::uint64 value);

  // optional sint32 optional_sint32 = 5;
  void clear_optional_sint32();
  static const int kOptionalSint32FieldNumber = 5;
  ::google::protobuf::int32 optional_sint32() const;
  void set_optional_sint32(::google::protobuf::int32 value);

  // optional sint64 optional_sint64 = 6;
  void clear_optional_sint64();
  static const int kOptionalSint64FieldNumber = 6;
  ::google::protobuf::int64 optional_sint64() const;
  void set_optional_sint64(::google::protobuf::int64 value);

  // optional fixed32 optional_fixed32 = 7;
  void clear_optional_fixed32();
  static const int kOptionalFixed32FieldNumber = 7;
  ::google::protobuf::uint32 optional_fixed32() const;
  void set_optional_fixed32(::google::protobuf::uint32 value);

  // optional fixed64 optional_fixed64 = 8;
  void clear_optional_fixed64();
  static const int kOptionalFixed64FieldNumber = 8;
  ::google::protobuf::uint64 optional_fixed64() const;
  void set_optional_fixed64(::google::protobuf::uint64 value);

  // optional sfixed32 optional_sfixed32 = 9;
  void clear_optional_sfixed32();
  static const int kOptionalSfixed32FieldNumber = 9;
  ::google::protobuf::int32 optional_sfixed32() const;
  void set_optional_sfixed32(::google::protobuf::int32 value);

  // optional sfixed64 optional_sfixed64 = 10;
  void clear_optional_sfixed64();
  static const int kOptionalSfixed64FieldNumber = 10;
  ::google::protobuf::int64 optional_sfixed64() const;
  void set_optional_sfixed64(::google::protobuf::int64 value);

  // optional float optional_float = 11;
  void clear_optional_float();
  static const int kOptionalFloatFieldNumber = 11;
  float optional_float() const;
  void set_optional_float(float value);

  // optional double optional_double = 12;
  void clear_optional_double();
  static const int kOptionalDoubleFieldNumber = 12;
  double optional_double() const;
  void set_optional_double(double value);

  // optional bool optional_bool = 13;
  void clear_optional_bool();
  static const int kOptionalBoolFieldNumber = 13;
  bool optional_bool() const;
  void set_optional_bool(bool value);

  // optional string optional_string = 14;
  void clear_optional_string();
  static const int kOptionalStringFieldNumber = 14;
  const ::std::string& optional_string() const;
  void set_optional_string(const ::std::string& value);
  void set_optional_string(const char* value);
  void set_optional_string(const char* value, size_t size);
  ::std::string* mutable_optional_string();
  ::std::string* release_optional_string();
  void set_allocated_optional_string(::std::string* optional_string);

  // optional bytes optional_bytes = 15;
  void clear_optional_bytes();
  static const int kOptionalBytesFieldNumber = 15;
  const ::std::string& optional_bytes() const;
  void set_optional_bytes(const ::std::string& value);
  void set_optional_bytes(const char* value);
  void set_optional_bytes(const void* value, size_t size);
  ::std::string* mutable_optional_bytes();
  ::std::string* release_optional_bytes();
  void set_allocated_optional_bytes(::std::string* optional_bytes);

  // optional .proto2_nofieldpresence_unittest.TestAllTypes.NestedMessage optional_nested_message = 18;
  bool has_optional_nested_message() const;
  void clear_optional_nested_message();
  static const int kOptionalNestedMessageFieldNumber = 18;
  const ::proto2_nofieldpresence_unittest::TestAllTypes_NestedMessage& optional_nested_message() const;
  ::proto2_nofieldpresence_unittest::TestAllTypes_NestedMessage* mutable_optional_nested_message();
  ::proto2_nofieldpresence_unittest::TestAllTypes_NestedMessage* release_optional_nested_message();
  void set_allocated_optional_nested_message(::proto2_nofieldpresence_unittest::TestAllTypes_NestedMessage* optional_nested_message);

  // optional .proto2_nofieldpresence_unittest.ForeignMessage optional_foreign_message = 19;
  bool has_optional_foreign_message() const;
  void clear_optional_foreign_message();
  static const int kOptionalForeignMessageFieldNumber = 19;
  const ::proto2_nofieldpresence_unittest::ForeignMessage& optional_foreign_message() const;
  ::proto2_nofieldpresence_unittest::ForeignMessage* mutable_optional_foreign_message();
  ::proto2_nofieldpresence_unittest::ForeignMessage* release_optional_foreign_message();
  void set_allocated_optional_foreign_message(::proto2_nofieldpresence_unittest::ForeignMessage* optional_foreign_message);

  // optional .protobuf_unittest.TestAllTypes optional_proto2_message = 20;
  bool has_optional_proto2_message() const;
  void clear_optional_proto2_message();
  static const int kOptionalProto2MessageFieldNumber = 20;
  const ::protobuf_unittest::TestAllTypes& optional_proto2_message() const;
  ::protobuf_unittest::TestAllTypes* mutable_optional_proto2_message();
  ::protobuf_unittest::TestAllTypes* release_optional_proto2_message();
  void set_allocated_optional_proto2_message(::protobuf_unittest::TestAllTypes* optional_proto2_message);

  // optional .proto2_nofieldpresence_unittest.TestAllTypes.NestedEnum optional_nested_enum = 21;
  void clear_optional_nested_enum();
  static const int kOptionalNestedEnumFieldNumber = 21;
  ::proto2_nofieldpresence_unittest::TestAllTypes_NestedEnum optional_nested_enum() const;
  void set_optional_nested_enum(::proto2_nofieldpresence_unittest::TestAllTypes_NestedEnum value);

  // optional .proto2_nofieldpresence_unittest.ForeignEnum optional_foreign_enum = 22;
  void clear_optional_foreign_enum();
  static const int kOptionalForeignEnumFieldNumber = 22;
  ::proto2_nofieldpresence_unittest::ForeignEnum optional_foreign_enum() const;
  void set_optional_foreign_enum(::proto2_nofieldpresence_unittest::ForeignEnum value);

  // optional string optional_string_piece = 24 [ctype = STRING_PIECE];
  void clear_optional_string_piece();
  static const int kOptionalStringPieceFieldNumber = 24;
 private:
  // Hidden due to unknown ctype option.
  const ::std::string& optional_string_piece() const;
  void set_optional_string_piece(const ::std::string& value);
  void set_optional_string_piece(const char* value);
  void set_optional_string_piece(const char* value, size_t size);
  ::std::string* mutable_optional_string_piece();
  ::std::string* release_optional_string_piece();
  void set_allocated_optional_string_piece(::std::string* optional_string_piece);
 public:

  // optional string optional_cord = 25 [ctype = CORD];
  void clear_optional_cord();
  static const int kOptionalCordFieldNumber = 25;
 private:
  // Hidden due to unknown ctype option.
  const ::std::string& optional_cord() const;
  void set_optional_cord(const ::std::string& value);
  void set_optional_cord(const char* value);
  void set_optional_cord(const char* value, size_t size);
  ::std::string* mutable_optional_cord();
  ::std::string* release_optional_cord();
  void set_allocated_optional_cord(::std::string* optional_cord);
 public:

  // optional .proto2_nofieldpresence_unittest.TestAllTypes.NestedMessage optional_lazy_message = 30 [lazy = true];
  bool has_optional_lazy_message() const;
  void clear_optional_lazy_message();
  static const int kOptionalLazyMessageFieldNumber = 30;
  const ::proto2_nofieldpresence_unittest::TestAllTypes_NestedMessage& optional_lazy_message() const;
  ::proto2_nofieldpresence_unittest::TestAllTypes_NestedMessage* mutable_optional_lazy_message();
  ::proto2_nofieldpresence_unittest::TestAllTypes_NestedMessage* release_optional_lazy_message();
  void set_allocated_optional_lazy_message(::proto2_nofieldpresence_unittest::TestAllTypes_NestedMessage* optional_lazy_message);

  // repeated int32 repeated_int32 = 31;
  int repeated_int32_size() const;
  void clear_repeated_int32();
  static const int kRepeatedInt32FieldNumber = 31;
  ::google::protobuf::int32 repeated_int32(int index) const;
  void set_repeated_int32(int index, ::google::protobuf::int32 value);
  void add_repeated_int32(::google::protobuf::int32 value);
  const ::google::protobuf::RepeatedField< ::google::protobuf::int32 >&
      repeated_int32() const;
  ::google::protobuf::RepeatedField< ::google::protobuf::int32 >*
      mutable_repeated_int32();

  // repeated int64 repeated_int64 = 32;
  int repeated_int64_size() const;
  void clear_repeated_int64();
  static const int kRepeatedInt64FieldNumber = 32;
  ::google::protobuf::int64 repeated_int64(int index) const;
  void set_repeated_int64(int index, ::google::protobuf::int64 value);
  void add_repeated_int64(::google::protobuf::int64 value);
  const ::google::protobuf::RepeatedField< ::google::protobuf::int64 >&
      repeated_int64() const;
  ::google::protobuf::RepeatedField< ::google::protobuf::int64 >*
      mutable_repeated_int64();

  // repeated uint32 repeated_uint32 = 33;
  int repeated_uint32_size() const;
  void clear_repeated_uint32();
  static const int kRepeatedUint32FieldNumber = 33;
  ::google::protobuf::uint32 repeated_uint32(int index) const;
  void set_repeated_uint32(int index, ::google::protobuf::uint32 value);
  void add_repeated_uint32(::google::protobuf::uint32 value);
  const ::google::protobuf::RepeatedField< ::google::protobuf::uint32 >&
      repeated_uint32() const;
  ::google::protobuf::RepeatedField< ::google::protobuf::uint32 >*
      mutable_repeated_uint32();

  // repeated uint64 repeated_uint64 = 34;
  int repeated_uint64_size() const;
  void clear_repeated_uint64();
  static const int kRepeatedUint64FieldNumber = 34;
  ::google::protobuf::uint64 repeated_uint64(int index) const;
  void set_repeated_uint64(int index, ::google::protobuf::uint64 value);
  void add_repeated_uint64(::google::protobuf::uint64 value);
  const ::google::protobuf::RepeatedField< ::google::protobuf::uint64 >&
      repeated_uint64() const;
  ::google::protobuf::RepeatedField< ::google::protobuf::uint64 >*
      mutable_repeated_uint64();

  // repeated sint32 repeated_sint32 = 35;
  int repeated_sint32_size() const;
  void clear_repeated_sint32();
  static const int kRepeatedSint32FieldNumber = 35;
  ::google::protobuf::int32 repeated_sint32(int index) const;
  void set_repeated_sint32(int index, ::google::protobuf::int32 value);
  void add_repeated_sint32(::google::protobuf::int32 value);
  const ::google::protobuf::RepeatedField< ::google::protobuf::int32 >&
      repeated_sint32() const;
  ::google::protobuf::RepeatedField< ::google::protobuf::int32 >*
      mutable_repeated_sint32();

  // repeated sint64 repeated_sint64 = 36;
  int repeated_sint64_size() const;
  void clear_repeated_sint64();
  static const int kRepeatedSint64FieldNumber = 36;
  ::google::protobuf::int64 repeated_sint64(int index) const;
  void set_repeated_sint64(int index, ::google::protobuf::int64 value);
  void add_repeated_sint64(::google::protobuf::int64 value);
  const ::google::protobuf::RepeatedField< ::google::protobuf::int64 >&
      repeated_sint64() const;
  ::google::protobuf::RepeatedField< ::google::protobuf::int64 >*
      mutable_repeated_sint64();

  // repeated fixed32 repeated_fixed32 = 37;
  int repeated_fixed32_size() const;
  void clear_repeated_fixed32();
  static const int kRepeatedFixed32FieldNumber = 37;
  ::google::protobuf::uint32 repeated_fixed32(int index) const;
  void set_repeated_fixed32(int index, ::google::protobuf::uint32 value);
  void add_repeated_fixed32(::google::protobuf::uint32 value);
  const ::google::protobuf::RepeatedField< ::google::protobuf::uint32 >&
      repeated_fixed32() const;
  ::google::protobuf::RepeatedField< ::google::protobuf::uint32 >*
      mutable_repeated_fixed32();

  // repeated fixed64 repeated_fixed64 = 38;
  int repeated_fixed64_size() const;
  void clear_repeated_fixed64();
  static const int kRepeatedFixed64FieldNumber = 38;
  ::google::protobuf::uint64 repeated_fixed64(int index) const;
  void set_repeated_fixed64(int index, ::google::protobuf::uint64 value);
  void add_repeated_fixed64(::google::protobuf::uint64 value);
  const ::google::protobuf::RepeatedField< ::google::protobuf::uint64 >&
      repeated_fixed64() const;
  ::google::protobuf::RepeatedField< ::google::protobuf::uint64 >*
      mutable_repeated_fixed64();

  // repeated sfixed32 repeated_sfixed32 = 39;
  int repeated_sfixed32_size() const;
  void clear_repeated_sfixed32();
  static const int kRepeatedSfixed32FieldNumber = 39;
  ::google::protobuf::int32 repeated_sfixed32(int index) const;
  void set_repeated_sfixed32(int index, ::google::protobuf::int32 value);
  void add_repeated_sfixed32(::google::protobuf::int32 value);
  const ::google::protobuf::RepeatedField< ::google::protobuf::int32 >&
      repeated_sfixed32() const;
  ::google::protobuf::RepeatedField< ::google::protobuf::int32 >*
      mutable_repeated_sfixed32();

  // repeated sfixed64 repeated_sfixed64 = 40;
  int repeated_sfixed64_size() const;
  void clear_repeated_sfixed64();
  static const int kRepeatedSfixed64FieldNumber = 40;
  ::google::protobuf::int64 repeated_sfixed64(int index) const;
  void set_repeated_sfixed64(int index, ::google::protobuf::int64 value);
  void add_repeated_sfixed64(::google::protobuf::int64 value);
  const ::google::protobuf::RepeatedField< ::google::protobuf::int64 >&
      repeated_sfixed64() const;
  ::google::protobuf::RepeatedField< ::google::protobuf::int64 >*
      mutable_repeated_sfixed64();

  // repeated float repeated_float = 41;
  int repeated_float_size() const;
  void clear_repeated_float();
  static const int kRepeatedFloatFieldNumber = 41;
  float repeated_float(int index) const;
  void set_repeated_float(int index, float value);
  void add_repeated_float(float value);
  const ::google::protobuf::RepeatedField< float >&
      repeated_float() const;
  ::google::protobuf::RepeatedField< float >*
      mutable_repeated_float();

  // repeated double repeated_double = 42;
  int repeated_double_size() const;
  void clear_repeated_double();
  static const int kRepeatedDoubleFieldNumber = 42;
  double repeated_double(int index) const;
  void set_repeated_double(int index, double value);
  void add_repeated_double(double value);
  const ::google::protobuf::RepeatedField< double >&
      repeated_double() const;
  ::google::protobuf::RepeatedField< double >*
      mutable_repeated_double();

  // repeated bool repeated_bool = 43;
  int repeated_bool_size() const;
  void clear_repeated_bool();
  static const int kRepeatedBoolFieldNumber = 43;
  bool repeated_bool(int index) const;
  void set_repeated_bool(int index, bool value);
  void add_repeated_bool(bool value);
  const ::google::protobuf::RepeatedField< bool >&
      repeated_bool() const;
  ::google::protobuf::RepeatedField< bool >*
      mutable_repeated_bool();

  // repeated string repeated_string = 44;
  int repeated_string_size() const;
  void clear_repeated_string();
  static const int kRepeatedStringFieldNumber = 44;
  const ::std::string& repeated_string(int index) const;
  ::std::string* mutable_repeated_string(int index);
  void set_repeated_string(int index, const ::std::string& value);
  void set_repeated_string(int index, const char* value);
  void set_repeated_string(int index, const char* value, size_t size);
  ::std::string* add_repeated_string();
  void add_repeated_string(const ::std::string& value);
  void add_repeated_string(const char* value);
  void add_repeated_string(const char* value, size_t size);
  const ::google::protobuf::RepeatedPtrField< ::std::string>& repeated_string() const;
  ::google::protobuf::RepeatedPtrField< ::std::string>* mutable_repeated_string();

  // repeated bytes repeated_bytes = 45;
  int repeated_bytes_size() const;
  void clear_repeated_bytes();
  static const int kRepeatedBytesFieldNumber = 45;
  const ::std::string& repeated_bytes(int index) const;
  ::std::string* mutable_repeated_bytes(int index);
  void set_repeated_bytes(int index, const ::std::string& value);
  void set_repeated_bytes(int index, const char* value);
  void set_repeated_bytes(int index, const void* value, size_t size);
  ::std::string* add_repeated_bytes();
  void add_repeated_bytes(const ::std::string& value);
  void add_repeated_bytes(const char* value);
  void add_repeated_bytes(const void* value, size_t size);
  const ::google::protobuf::RepeatedPtrField< ::std::string>& repeated_bytes() const;
  ::google::protobuf::RepeatedPtrField< ::std::string>* mutable_repeated_bytes();

  // repeated .proto2_nofieldpresence_unittest.TestAllTypes.NestedMessage repeated_nested_message = 48;
  int repeated_nested_message_size() const;
  void clear_repeated_nested_message();
  static const int kRepeatedNestedMessageFieldNumber = 48;
  const ::proto2_nofieldpresence_unittest::TestAllTypes_NestedMessage& repeated_nested_message(int index) const;
  ::proto2_nofieldpresence_unittest::TestAllTypes_NestedMessage* mutable_repeated_nested_message(int index);
  ::proto2_nofieldpresence_unittest::TestAllTypes_NestedMessage* add_repeated_nested_message();
  ::google::protobuf::RepeatedPtrField< ::proto2_nofieldpresence_unittest::TestAllTypes_NestedMessage >*
      mutable_repeated_nested_message();
  const ::google::protobuf::RepeatedPtrField< ::proto2_nofieldpresence_unittest::TestAllTypes_NestedMessage >&
      repeated_nested_message() const;

  // repeated .proto2_nofieldpresence_unittest.ForeignMessage repeated_foreign_message = 49;
  int repeated_foreign_message_size() const;
  void clear_repeated_foreign_message();
  static const int kRepeatedForeignMessageFieldNumber = 49;
  const ::proto2_nofieldpresence_unittest::ForeignMessage& repeated_foreign_message(int index) const;
  ::proto2_nofieldpresence_unittest::ForeignMessage* mutable_repeated_foreign_message(int index);
  ::proto2_nofieldpresence_unittest::ForeignMessage* add_repeated_foreign_message();
  ::google::protobuf::RepeatedPtrField< ::proto2_nofieldpresence_unittest::ForeignMessage >*
      mutable_repeated_foreign_message();
  const ::google::protobuf::RepeatedPtrField< ::proto2_nofieldpresence_unittest::ForeignMessage >&
      repeated_foreign_message() const;

  // repeated .protobuf_unittest.TestAllTypes repeated_proto2_message = 50;
  int repeated_proto2_message_size() const;
  void clear_repeated_proto2_message();
  static const int kRepeatedProto2MessageFieldNumber = 50;
  const ::protobuf_unittest::TestAllTypes& repeated_proto2_message(int index) const;
  ::protobuf_unittest::TestAllTypes* mutable_repeated_proto2_message(int index);
  ::protobuf_unittest::TestAllTypes* add_repeated_proto2_message();
  ::google::protobuf::RepeatedPtrField< ::protobuf_unittest::TestAllTypes >*
      mutable_repeated_proto2_message();
  const ::google::protobuf::RepeatedPtrField< ::protobuf_unittest::TestAllTypes >&
      repeated_proto2_message() const;

  // repeated .proto2_nofieldpresence_unittest.TestAllTypes.NestedEnum repeated_nested_enum = 51;
  int repeated_nested_enum_size() const;
  void clear_repeated_nested_enum();
  static const int kRepeatedNestedEnumFieldNumber = 51;
  ::proto2_nofieldpresence_unittest::TestAllTypes_NestedEnum repeated_nested_enum(int index) const;
  void set_repeated_nested_enum(int index, ::proto2_nofieldpresence_unittest::TestAllTypes_NestedEnum value);
  void add_repeated_nested_enum(::proto2_nofieldpresence_unittest::TestAllTypes_NestedEnum value);
  const ::google::protobuf::RepeatedField<int>& repeated_nested_enum() const;
  ::google::protobuf::RepeatedField<int>* mutable_repeated_nested_enum();

  // repeated .proto2_nofieldpresence_unittest.ForeignEnum repeated_foreign_enum = 52;
  int repeated_foreign_enum_size() const;
  void clear_repeated_foreign_enum();
  static const int kRepeatedForeignEnumFieldNumber = 52;
  ::proto2_nofieldpresence_unittest::ForeignEnum repeated_foreign_enum(int index) const;
  void set_repeated_foreign_enum(int index, ::proto2_nofieldpresence_unittest::ForeignEnum value);
  void add_repeated_foreign_enum(::proto2_nofieldpresence_unittest::ForeignEnum value);
  const ::google::protobuf::RepeatedField<int>& repeated_foreign_enum() const;
  ::google::protobuf::RepeatedField<int>* mutable_repeated_foreign_enum();

  // repeated string repeated_string_piece = 54 [ctype = STRING_PIECE];
  int repeated_string_piece_size() const;
  void clear_repeated_string_piece();
  static const int kRepeatedStringPieceFieldNumber = 54;
 private:
  // Hidden due to unknown ctype option.
  const ::std::string& repeated_string_piece(int index) const;
  ::std::string* mutable_repeated_string_piece(int index);
  void set_repeated_string_piece(int index, const ::std::string& value);
  void set_repeated_string_piece(int index, const char* value);
  void set_repeated_string_piece(int index, const char* value, size_t size);
  ::std::string* add_repeated_string_piece();
  void add_repeated_string_piece(const ::std::string& value);
  void add_repeated_string_piece(const char* value);
  void add_repeated_string_piece(const char* value, size_t size);
  const ::google::protobuf::RepeatedPtrField< ::std::string>& repeated_string_piece() const;
  ::google::protobuf::RepeatedPtrField< ::std::string>* mutable_repeated_string_piece();
 public:

  // repeated string repeated_cord = 55 [ctype = CORD];
  int repeated_cord_size() const;
  void clear_repeated_cord();
  static const int kRepeatedCordFieldNumber = 55;
 private:
  // Hidden due to unknown ctype option.
  const ::std::string& repeated_cord(int index) const;
  ::std::string* mutable_repeated_cord(int index);
  void set_repeated_cord(int index, const ::std::string& value);
  void set_repeated_cord(int index, const char* value);
  void set_repeated_cord(int index, const char* value, size_t size);
  ::std::string* add_repeated_cord();
  void add_repeated_cord(const ::std::string& value);
  void add_repeated_cord(const char* value);
  void add_repeated_cord(const char* value, size_t size);
  const ::google::protobuf::RepeatedPtrField< ::std::string>& repeated_cord() const;
  ::google::protobuf::RepeatedPtrField< ::std::string>* mutable_repeated_cord();
 public:

  // repeated .proto2_nofieldpresence_unittest.TestAllTypes.NestedMessage repeated_lazy_message = 57 [lazy = true];
  int repeated_lazy_message_size() const;
  void clear_repeated_lazy_message();
  static const int kRepeatedLazyMessageFieldNumber = 57;
  const ::proto2_nofieldpresence_unittest::TestAllTypes_NestedMessage& repeated_lazy_message(int index) const;
  ::proto2_nofieldpresence_unittest::TestAllTypes_NestedMessage* mutable_repeated_lazy_message(int index);
  ::proto2_nofieldpresence_unittest::TestAllTypes_NestedMessage* add_repeated_lazy_message();
  ::google::protobuf::RepeatedPtrField< ::proto2_nofieldpresence_unittest::TestAllTypes_NestedMessage >*
      mutable_repeated_lazy_message();
  const ::google::protobuf::RepeatedPtrField< ::proto2_nofieldpresence_unittest::TestAllTypes_NestedMessage >&
      repeated_lazy_message() const;

  // optional uint32 oneof_uint32 = 111;
  private:
  bool has_oneof_uint32() const;
  public:
  void clear_oneof_uint32();
  static const int kOneofUint32FieldNumber = 111;
  ::google::protobuf::uint32 oneof_uint32() const;
  void set_oneof_uint32(::google::protobuf::uint32 value);

  // optional .proto2_nofieldpresence_unittest.TestAllTypes.NestedMessage oneof_nested_message = 112;
  bool has_oneof_nested_message() const;
  void clear_oneof_nested_message();
  static const int kOneofNestedMessageFieldNumber = 112;
  const ::proto2_nofieldpresence_unittest::TestAllTypes_NestedMessage& oneof_nested_message() const;
  ::proto2_nofieldpresence_unittest::TestAllTypes_NestedMessage* mutable_oneof_nested_message();
  ::proto2_nofieldpresence_unittest::TestAllTypes_NestedMessage* release_oneof_nested_message();
  void set_allocated_oneof_nested_message(::proto2_nofieldpresence_unittest::TestAllTypes_NestedMessage* oneof_nested_message);

  // optional string oneof_string = 113;
  private:
  bool has_oneof_string() const;
  public:
  void clear_oneof_string();
  static const int kOneofStringFieldNumber = 113;
  const ::std::string& oneof_string() const;
  void set_oneof_string(const ::std::string& value);
  void set_oneof_string(const char* value);
  void set_oneof_string(const char* value, size_t size);
  ::std::string* mutable_oneof_string();
  ::std::string* release_oneof_string();
  void set_allocated_oneof_string(::std::string* oneof_string);

  // optional .proto2_nofieldpresence_unittest.TestAllTypes.NestedEnum oneof_enum = 114;
  private:
  bool has_oneof_enum() const;
  public:
  void clear_oneof_enum();
  static const int kOneofEnumFieldNumber = 114;
  ::proto2_nofieldpresence_unittest::TestAllTypes_NestedEnum oneof_enum() const;
  void set_oneof_enum(::proto2_nofieldpresence_unittest::TestAllTypes_NestedEnum value);

  OneofFieldCase oneof_field_case() const;
  // @@protoc_insertion_point(class_scope:proto2_nofieldpresence_unittest.TestAllTypes)
 private:
  inline void set_has_oneof_uint32();
  inline void set_has_oneof_nested_message();
  inline void set_has_oneof_string();
  inline void set_has_oneof_enum();

  inline bool has_oneof_field() const;
  void clear_oneof_field();
  inline void clear_has_oneof_field();

  ::google::protobuf::internal::InternalMetadataWithArena _internal_metadata_;
  ::google::protobuf::RepeatedField< ::google::protobuf::int32 > repeated_int32_;
  mutable int _repeated_int32_cached_byte_size_;
  ::google::protobuf::RepeatedField< ::google::protobuf::int64 > repeated_int64_;
  mutable int _repeated_int64_cached_byte_size_;
  ::google::protobuf::RepeatedField< ::google::protobuf::uint32 > repeated_uint32_;
  mutable int _repeated_uint32_cached_byte_size_;
  ::google::protobuf::RepeatedField< ::google::protobuf::uint64 > repeated_uint64_;
  mutable int _repeated_uint64_cached_byte_size_;
  ::google::protobuf::RepeatedField< ::google::protobuf::int32 > repeated_sint32_;
  mutable int _repeated_sint32_cached_byte_size_;
  ::google::protobuf::RepeatedField< ::google::protobuf::int64 > repeated_sint64_;
  mutable int _repeated_sint64_cached_byte_size_;
  ::google::protobuf::RepeatedField< ::google::protobuf::uint32 > repeated_fixed32_;
  mutable int _repeated_fixed32_cached_byte_size_;
  ::google::protobuf::RepeatedField< ::google::protobuf::uint64 > repeated_fixed64_;
  mutable int _repeated_fixed64_cached_byte_size_;
  ::google::protobuf::RepeatedField< ::google::protobuf::int32 > repeated_sfixed32_;
  mutable int _repeated_sfixed32_cached_byte_size_;
  ::google::protobuf::RepeatedField< ::google::protobuf::int64 > repeated_sfixed64_;
  mutable int _repeated_sfixed64_cached_byte_size_;
  ::google::protobuf::RepeatedField< float > repeated_float_;
  mutable int _repeated_float_cached_byte_size_;
  ::google::protobuf::RepeatedField< double > repeated_double_;
  mutable int _repeated_double_cached_byte_size_;
  ::google::protobuf::RepeatedField< bool > repeated_bool_;
  mutable int _repeated_bool_cached_byte_size_;
  ::google::protobuf::RepeatedPtrField< ::std::string> repeated_string_;
  ::google::protobuf::RepeatedPtrField< ::std::string> repeated_bytes_;
  ::google::protobuf::RepeatedPtrField< ::proto2_nofieldpresence_unittest::TestAllTypes_NestedMessage > repeated_nested_message_;
  ::google::protobuf::RepeatedPtrField< ::proto2_nofieldpresence_unittest::ForeignMessage > repeated_foreign_message_;
  ::google::protobuf::RepeatedPtrField< ::protobuf_unittest::TestAllTypes > repeated_proto2_message_;
  ::google::protobuf::RepeatedField<int> repeated_nested_enum_;
  mutable int _repeated_nested_enum_cached_byte_size_;
  ::google::protobuf::RepeatedField<int> repeated_foreign_enum_;
  mutable int _repeated_foreign_enum_cached_byte_size_;
  ::google::protobuf::RepeatedPtrField< ::std::string> repeated_string_piece_;
  ::google::protobuf::RepeatedPtrField< ::std::string> repeated_cord_;
  ::google::protobuf::RepeatedPtrField< ::proto2_nofieldpresence_unittest::TestAllTypes_NestedMessage > repeated_lazy_message_;
  ::google::protobuf::internal::ArenaStringPtr optional_string_;
  ::google::protobuf::internal::ArenaStringPtr optional_bytes_;
  ::google::protobuf::internal::ArenaStringPtr optional_string_piece_;
  ::google::protobuf::internal::ArenaStringPtr optional_cord_;
  ::proto2_nofieldpresence_unittest::TestAllTypes_NestedMessage* optional_nested_message_;
  ::proto2_nofieldpresence_unittest::ForeignMessage* optional_foreign_message_;
  ::protobuf_unittest::TestAllTypes* optional_proto2_message_;
  ::proto2_nofieldpresence_unittest::TestAllTypes_NestedMessage* optional_lazy_message_;
  ::google::protobuf::int64 optional_int64_;
  ::google::protobuf::int32 optional_int32_;
  ::google::protobuf::uint32 optional_uint32_;
  ::google::protobuf::uint64 optional_uint64_;
  ::google::protobuf::int64 optional_sint64_;
  ::google::protobuf::int32 optional_sint32_;
  ::google::protobuf::uint32 optional_fixed32_;
  ::google::protobuf::uint64 optional_fixed64_;
  ::google::protobuf::int64 optional_sfixed64_;
  ::google::protobuf::int32 optional_sfixed32_;
  float optional_float_;
  double optional_double_;
  bool optional_bool_;
  int optional_nested_enum_;
  int optional_foreign_enum_;
  union OneofFieldUnion {
    OneofFieldUnion() {}
    ::google::protobuf::uint32 oneof_uint32_;
    ::proto2_nofieldpresence_unittest::TestAllTypes_NestedMessage* oneof_nested_message_;
    ::google::protobuf::internal::ArenaStringPtr oneof_string_;
    int oneof_enum_;
  } oneof_field_;
  mutable int _cached_size_;
  ::google::protobuf::uint32 _oneof_case_[1];

  friend void  protobuf_InitDefaults_google_2fprotobuf_2funittest_5fno_5ffield_5fpresence_2eproto_impl();
  friend void  protobuf_AddDesc_google_2fprotobuf_2funittest_5fno_5ffield_5fpresence_2eproto_impl();
  friend void protobuf_AssignDesc_google_2fprotobuf_2funittest_5fno_5ffield_5fpresence_2eproto();
  friend void protobuf_ShutdownFile_google_2fprotobuf_2funittest_5fno_5ffield_5fpresence_2eproto();

  void InitAsDefaultInstance();
};
extern ::google::protobuf::internal::ExplicitlyConstructed<TestAllTypes> TestAllTypes_default_instance_;

// -------------------------------------------------------------------

class TestProto2Required : public ::google::protobuf::Message /* @@protoc_insertion_point(class_definition:proto2_nofieldpresence_unittest.TestProto2Required) */ {
 public:
  TestProto2Required();
  virtual ~TestProto2Required();

  TestProto2Required(const TestProto2Required& from);

  inline TestProto2Required& operator=(const TestProto2Required& from) {
    CopyFrom(from);
    return *this;
  }

  static const ::google::protobuf::Descriptor* descriptor();
  static const TestProto2Required& default_instance();

  static const TestProto2Required* internal_default_instance();

  void Swap(TestProto2Required* other);

  // implements Message ----------------------------------------------

  inline TestProto2Required* New() const { return New(NULL); }

  TestProto2Required* New(::google::protobuf::Arena* arena) const;
  void CopyFrom(const ::google::protobuf::Message& from);
  void MergeFrom(const ::google::protobuf::Message& from);
  void CopyFrom(const TestProto2Required& from);
  void MergeFrom(const TestProto2Required& from);
  void Clear();
  bool IsInitialized() const;

  size_t ByteSizeLong() const;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input);
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const;
  ::google::protobuf::uint8* InternalSerializeWithCachedSizesToArray(
      bool deterministic, ::google::protobuf::uint8* output) const;
  ::google::protobuf::uint8* SerializeWithCachedSizesToArray(::google::protobuf::uint8* output) const {
    return InternalSerializeWithCachedSizesToArray(false, output);
  }
  int GetCachedSize() const { return _cached_size_; }
  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const;
  void InternalSwap(TestProto2Required* other);
  void UnsafeMergeFrom(const TestProto2Required& from);
  private:
  inline ::google::protobuf::Arena* GetArenaNoVirtual() const {
    return _internal_metadata_.arena();
  }
  inline void* MaybeArenaPtr() const {
    return _internal_metadata_.raw_arena_ptr();
  }
  public:

  ::google::protobuf::Metadata GetMetadata() const;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // optional .protobuf_unittest.TestRequired proto2 = 1;
  bool has_proto2() const;
  void clear_proto2();
  static const int kProto2FieldNumber = 1;
  const ::protobuf_unittest::TestRequired& proto2() const;
  ::protobuf_unittest::TestRequired* mutable_proto2();
  ::protobuf_unittest::TestRequired* release_proto2();
  void set_allocated_proto2(::protobuf_unittest::TestRequired* proto2);

  // @@protoc_insertion_point(class_scope:proto2_nofieldpresence_unittest.TestProto2Required)
 private:

  ::google::protobuf::internal::InternalMetadataWithArena _internal_metadata_;
  ::protobuf_unittest::TestRequired* proto2_;
  mutable int _cached_size_;
  friend void  protobuf_InitDefaults_google_2fprotobuf_2funittest_5fno_5ffield_5fpresence_2eproto_impl();
  friend void  protobuf_AddDesc_google_2fprotobuf_2funittest_5fno_5ffield_5fpresence_2eproto_impl();
  friend void protobuf_AssignDesc_google_2fprotobuf_2funittest_5fno_5ffield_5fpresence_2eproto();
  friend void protobuf_ShutdownFile_google_2fprotobuf_2funittest_5fno_5ffield_5fpresence_2eproto();

  void InitAsDefaultInstance();
};
extern ::google::protobuf::internal::ExplicitlyConstructed<TestProto2Required> TestProto2Required_default_instance_;

// -------------------------------------------------------------------

class ForeignMessage : public ::google::protobuf::Message /* @@protoc_insertion_point(class_definition:proto2_nofieldpresence_unittest.ForeignMessage) */ {
 public:
  ForeignMessage();
  virtual ~ForeignMessage();

  ForeignMessage(const ForeignMessage& from);

  inline ForeignMessage& operator=(const ForeignMessage& from) {
    CopyFrom(from);
    return *this;
  }

  static const ::google::protobuf::Descriptor* descriptor();
  static const ForeignMessage& default_instance();

  static const ForeignMessage* internal_default_instance();

  void Swap(ForeignMessage* other);

  // implements Message ----------------------------------------------

  inline ForeignMessage* New() const { return New(NULL); }

  ForeignMessage* New(::google::protobuf::Arena* arena) const;
  void CopyFrom(const ::google::protobuf::Message& from);
  void MergeFrom(const ::google::protobuf::Message& from);
  void CopyFrom(const ForeignMessage& from);
  void MergeFrom(const ForeignMessage& from);
  void Clear();
  bool IsInitialized() const;

  size_t ByteSizeLong() const;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input);
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const;
  ::google::protobuf::uint8* InternalSerializeWithCachedSizesToArray(
      bool deterministic, ::google::protobuf::uint8* output) const;
  ::google::protobuf::uint8* SerializeWithCachedSizesToArray(::google::protobuf::uint8* output) const {
    return InternalSerializeWithCachedSizesToArray(false, output);
  }
  int GetCachedSize() const { return _cached_size_; }
  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const;
  void InternalSwap(ForeignMessage* other);
  void UnsafeMergeFrom(const ForeignMessage& from);
  private:
  inline ::google::protobuf::Arena* GetArenaNoVirtual() const {
    return _internal_metadata_.arena();
  }
  inline void* MaybeArenaPtr() const {
    return _internal_metadata_.raw_arena_ptr();
  }
  public:

  ::google::protobuf::Metadata GetMetadata() const;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // optional int32 c = 1;
  void clear_c();
  static const int kCFieldNumber = 1;
  ::google::protobuf::int32 c() const;
  void set_c(::google::protobuf::int32 value);

  // @@protoc_insertion_point(class_scope:proto2_nofieldpresence_unittest.ForeignMessage)
 private:

  ::google::protobuf::internal::InternalMetadataWithArena _internal_metadata_;
  ::google::protobuf::int32 c_;
  mutable int _cached_size_;
  friend void  protobuf_InitDefaults_google_2fprotobuf_2funittest_5fno_5ffield_5fpresence_2eproto_impl();
  friend void  protobuf_AddDesc_google_2fprotobuf_2funittest_5fno_5ffield_5fpresence_2eproto_impl();
  friend void protobuf_AssignDesc_google_2fprotobuf_2funittest_5fno_5ffield_5fpresence_2eproto();
  friend void protobuf_ShutdownFile_google_2fprotobuf_2funittest_5fno_5ffield_5fpresence_2eproto();

  void InitAsDefaultInstance();
};
extern ::google::protobuf::internal::ExplicitlyConstructed<ForeignMessage> ForeignMessage_default_instance_;

// ===================================================================


// ===================================================================

#if !PROTOBUF_INLINE_NOT_IN_HEADERS
// TestAllTypes_NestedMessage

// optional int32 bb = 1;
inline void TestAllTypes_NestedMessage::clear_bb() {
  bb_ = 0;
}
inline ::google::protobuf::int32 TestAllTypes_NestedMessage::bb() const {
  // @@protoc_insertion_point(field_get:proto2_nofieldpresence_unittest.TestAllTypes.NestedMessage.bb)
  return bb_;
}
inline void TestAllTypes_NestedMessage::set_bb(::google::protobuf::int32 value) {
  
  bb_ = value;
  // @@protoc_insertion_point(field_set:proto2_nofieldpresence_unittest.TestAllTypes.NestedMessage.bb)
}

inline const TestAllTypes_NestedMessage* TestAllTypes_NestedMessage::internal_default_instance() {
  return &TestAllTypes_NestedMessage_default_instance_.get();
}
// -------------------------------------------------------------------

// TestAllTypes

// optional int32 optional_int32 = 1;
inline void TestAllTypes::clear_optional_int32() {
  optional_int32_ = 0;
}
inline ::google::protobuf::int32 TestAllTypes::optional_int32() const {
  // @@protoc_insertion_point(field_get:proto2_nofieldpresence_unittest.TestAllTypes.optional_int32)
  return optional_int32_;
}
inline void TestAllTypes::set_optional_int32(::google::protobuf::int32 value) {
  
  optional_int32_ = value;
  // @@protoc_insertion_point(field_set:proto2_nofieldpresence_unittest.TestAllTypes.optional_int32)
}

// optional int64 optional_int64 = 2;
inline void TestAllTypes::clear_optional_int64() {
  optional_int64_ = GOOGLE_LONGLONG(0);
}
inline ::google::protobuf::int64 TestAllTypes::optional_int64() const {
  // @@protoc_insertion_point(field_get:proto2_nofieldpresence_unittest.TestAllTypes.optional_int64)
  return optional_int64_;
}
inline void TestAllTypes::set_optional_int64(::google::protobuf::int64 value) {
  
  optional_int64_ = value;
  // @@protoc_insertion_point(field_set:proto2_nofieldpresence_unittest.TestAllTypes.optional_int64)
}

// optional uint32 optional_uint32 = 3;
inline void TestAllTypes::clear_optional_uint32() {
  optional_uint32_ = 0u;
}
inline ::google::protobuf::uint32 TestAllTypes::optional_uint32() const {
  // @@protoc_insertion_point(field_get:proto2_nofieldpresence_unittest.TestAllTypes.optional_uint32)
  return optional_uint32_;
}
inline void TestAllTypes::set_optional_uint32(::google::protobuf::uint32 value) {
  
  optional_uint32_ = value;
  // @@protoc_insertion_point(field_set:proto2_nofieldpresence_unittest.TestAllTypes.optional_uint32)
}

// optional uint64 optional_uint64 = 4;
inline void TestAllTypes::clear_optional_uint64() {
  optional_uint64_ = GOOGLE_ULONGLONG(0);
}
inline ::google::protobuf::uint64 TestAllTypes::optional_uint64() const {
  // @@protoc_insertion_point(field_get:proto2_nofieldpresence_unittest.TestAllTypes.optional_uint64)
  return optional_uint64_;
}
inline void TestAllTypes::set_optional_uint64(::google::protobuf::uint64 value) {
  
  optional_uint64_ = value;
  // @@protoc_insertion_point(field_set:proto2_nofieldpresence_unittest.TestAllTypes.optional_uint64)
}

// optional sint32 optional_sint32 = 5;
inline void TestAllTypes::clear_optional_sint32() {
  optional_sint32_ = 0;
}
inline ::google::protobuf::int32 TestAllTypes::optional_sint32() const {
  // @@protoc_insertion_point(field_get:proto2_nofieldpresence_unittest.TestAllTypes.optional_sint32)
  return optional_sint32_;
}
inline void TestAllTypes::set_optional_sint32(::google::protobuf::int32 value) {
  
  optional_sint32_ = value;
  // @@protoc_insertion_point(field_set:proto2_nofieldpresence_unittest.TestAllTypes.optional_sint32)
}

// optional sint64 optional_sint64 = 6;
inline void TestAllTypes::clear_optional_sint64() {
  optional_sint64_ = GOOGLE_LONGLONG(0);
}
inline ::google::protobuf::int64 TestAllTypes::optional_sint64() const {
  // @@protoc_insertion_point(field_get:proto2_nofieldpresence_unittest.TestAllTypes.optional_sint64)
  return optional_sint64_;
}
inline void TestAllTypes::set_optional_sint64(::google::protobuf::int64 value) {
  
  optional_sint64_ = value;
  // @@protoc_insertion_point(field_set:proto2_nofieldpresence_unittest.TestAllTypes.optional_sint64)
}

// optional fixed32 optional_fixed32 = 7;
inline void TestAllTypes::clear_optional_fixed32() {
  optional_fixed32_ = 0u;
}
inline ::google::protobuf::uint32 TestAllTypes::optional_fixed32() const {
  // @@protoc_insertion_point(field_get:proto2_nofieldpresence_unittest.TestAllTypes.optional_fixed32)
  return optional_fixed32_;
}
inline void TestAllTypes::set_optional_fixed32(::google::protobuf::uint32 value) {
  
  optional_fixed32_ = value;
  // @@protoc_insertion_point(field_set:proto2_nofieldpresence_unittest.TestAllTypes.optional_fixed32)
}

// optional fixed64 optional_fixed64 = 8;
inline void TestAllTypes::clear_optional_fixed64() {
  optional_fixed64_ = GOOGLE_ULONGLONG(0);
}
inline ::google::protobuf::uint64 TestAllTypes::optional_fixed64() const {
  // @@protoc_insertion_point(field_get:proto2_nofieldpresence_unittest.TestAllTypes.optional_fixed64)
  return optional_fixed64_;
}
inline void TestAllTypes::set_optional_fixed64(::google::protobuf::uint64 value) {
  
  optional_fixed64_ = value;
  // @@protoc_insertion_point(field_set:proto2_nofieldpresence_unittest.TestAllTypes.optional_fixed64)
}

// optional sfixed32 optional_sfixed32 = 9;
inline void TestAllTypes::clear_optional_sfixed32() {
  optional_sfixed32_ = 0;
}
inline ::google::protobuf::int32 TestAllTypes::optional_sfixed32() const {
  // @@protoc_insertion_point(field_get:proto2_nofieldpresence_unittest.TestAllTypes.optional_sfixed32)
  return optional_sfixed32_;
}
inline void TestAllTypes::set_optional_sfixed32(::google::protobuf::int32 value) {
  
  optional_sfixed32_ = value;
  // @@protoc_insertion_point(field_set:proto2_nofieldpresence_unittest.TestAllTypes.optional_sfixed32)
}

// optional sfixed64 optional_sfixed64 = 10;
inline void TestAllTypes::clear_optional_sfixed64() {
  optional_sfixed64_ = GOOGLE_LONGLONG(0);
}
inline ::google::protobuf::int64 TestAllTypes::optional_sfixed64() const {
  // @@protoc_insertion_point(field_get:proto2_nofieldpresence_unittest.TestAllTypes.optional_sfixed64)
  return optional_sfixed64_;
}
inline void TestAllTypes::set_optional_sfixed64(::google::protobuf::int64 value) {
  
  optional_sfixed64_ = value;
  // @@protoc_insertion_point(field_set:proto2_nofieldpresence_unittest.TestAllTypes.optional_sfixed64)
}

// optional float optional_float = 11;
inline void TestAllTypes::clear_optional_float() {
  optional_float_ = 0;
}
inline float TestAllTypes::optional_float() const {
  // @@protoc_insertion_point(field_get:proto2_nofieldpresence_unittest.TestAllTypes.optional_float)
  return optional_float_;
}
inline void TestAllTypes::set_optional_float(float value) {
  
  optional_float_ = value;
  // @@protoc_insertion_point(field_set:proto2_nofieldpresence_unittest.TestAllTypes.optional_float)
}

// optional double optional_double = 12;
inline void TestAllTypes::clear_optional_double() {
  optional_double_ = 0;
}
inline double TestAllTypes::optional_double() const {
  // @@protoc_insertion_point(field_get:proto2_nofieldpresence_unittest.TestAllTypes.optional_double)
  return optional_double_;
}
inline void TestAllTypes::set_optional_double(double value) {
  
  optional_double_ = value;
  // @@protoc_insertion_point(field_set:proto2_nofieldpresence_unittest.TestAllTypes.optional_double)
}

// optional bool optional_bool = 13;
inline void TestAllTypes::clear_optional_bool() {
  optional_bool_ = false;
}
inline bool TestAllTypes::optional_bool() const {
  // @@protoc_insertion_point(field_get:proto2_nofieldpresence_unittest.TestAllTypes.optional_bool)
  return optional_bool_;
}
inline void TestAllTypes::set_optional_bool(bool value) {
  
  optional_bool_ = value;
  // @@protoc_insertion_point(field_set:proto2_nofieldpresence_unittest.TestAllTypes.optional_bool)
}

// optional string optional_string = 14;
inline void TestAllTypes::clear_optional_string() {
  optional_string_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline const ::std::string& TestAllTypes::optional_string() const {
  // @@protoc_insertion_point(field_get:proto2_nofieldpresence_unittest.TestAllTypes.optional_string)
  return optional_string_.GetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void TestAllTypes::set_optional_string(const ::std::string& value) {
  
  optional_string_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:proto2_nofieldpresence_unittest.TestAllTypes.optional_string)
}
inline void TestAllTypes::set_optional_string(const char* value) {
  
  optional_string_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:proto2_nofieldpresence_unittest.TestAllTypes.optional_string)
}
inline void TestAllTypes::set_optional_string(const char* value, size_t size) {
  
  optional_string_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:proto2_nofieldpresence_unittest.TestAllTypes.optional_string)
}
inline ::std::string* TestAllTypes::mutable_optional_string() {
  
  // @@protoc_insertion_point(field_mutable:proto2_nofieldpresence_unittest.TestAllTypes.optional_string)
  return optional_string_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline ::std::string* TestAllTypes::release_optional_string() {
  // @@protoc_insertion_point(field_release:proto2_nofieldpresence_unittest.TestAllTypes.optional_string)
  
  return optional_string_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void TestAllTypes::set_allocated_optional_string(::std::string* optional_string) {
  if (optional_string != NULL) {
    
  } else {
    
  }
  optional_string_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), optional_string);
  // @@protoc_insertion_point(field_set_allocated:proto2_nofieldpresence_unittest.TestAllTypes.optional_string)
}

// optional bytes optional_bytes = 15;
inline void TestAllTypes::clear_optional_bytes() {
  optional_bytes_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline const ::std::string& TestAllTypes::optional_bytes() const {
  // @@protoc_insertion_point(field_get:proto2_nofieldpresence_unittest.TestAllTypes.optional_bytes)
  return optional_bytes_.GetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void TestAllTypes::set_optional_bytes(const ::std::string& value) {
  
  optional_bytes_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:proto2_nofieldpresence_unittest.TestAllTypes.optional_bytes)
}
inline void TestAllTypes::set_optional_bytes(const char* value) {
  
  optional_bytes_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:proto2_nofieldpresence_unittest.TestAllTypes.optional_bytes)
}
inline void TestAllTypes::set_optional_bytes(const void* value, size_t size) {
  
  optional_bytes_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:proto2_nofieldpresence_unittest.TestAllTypes.optional_bytes)
}
inline ::std::string* TestAllTypes::mutable_optional_bytes() {
  
  // @@protoc_insertion_point(field_mutable:proto2_nofieldpresence_unittest.TestAllTypes.optional_bytes)
  return optional_bytes_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline ::std::string* TestAllTypes::release_optional_bytes() {
  // @@protoc_insertion_point(field_release:proto2_nofieldpresence_unittest.TestAllTypes.optional_bytes)
  
  return optional_bytes_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void TestAllTypes::set_allocated_optional_bytes(::std::string* optional_bytes) {
  if (optional_bytes != NULL) {
    
  } else {
    
  }
  optional_bytes_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), optional_bytes);
  // @@protoc_insertion_point(field_set_allocated:proto2_nofieldpresence_unittest.TestAllTypes.optional_bytes)
}

// optional .proto2_nofieldpresence_unittest.TestAllTypes.NestedMessage optional_nested_message = 18;
inline bool TestAllTypes::has_optional_nested_message() const {
  return this != internal_default_instance() && optional_nested_message_ != NULL;
}
inline void TestAllTypes::clear_optional_nested_message() {
  if (GetArenaNoVirtual() == NULL && optional_nested_message_ != NULL) delete optional_nested_message_;
  optional_nested_message_ = NULL;
}
inline const ::proto2_nofieldpresence_unittest::TestAllTypes_NestedMessage& TestAllTypes::optional_nested_message() const {
  // @@protoc_insertion_point(field_get:proto2_nofieldpresence_unittest.TestAllTypes.optional_nested_message)
  return optional_nested_message_ != NULL ? *optional_nested_message_
                         : *::proto2_nofieldpresence_unittest::TestAllTypes_NestedMessage::internal_default_instance();
}
inline ::proto2_nofieldpresence_unittest::TestAllTypes_NestedMessage* TestAllTypes::mutable_optional_nested_message() {
  
  if (optional_nested_message_ == NULL) {
    optional_nested_message_ = new ::proto2_nofieldpresence_unittest::TestAllTypes_NestedMessage;
  }
  // @@protoc_insertion_point(field_mutable:proto2_nofieldpresence_unittest.TestAllTypes.optional_nested_message)
  return optional_nested_message_;
}
inline ::proto2_nofieldpresence_unittest::TestAllTypes_NestedMessage* TestAllTypes::release_optional_nested_message() {
  // @@protoc_insertion_point(field_release:proto2_nofieldpresence_unittest.TestAllTypes.optional_nested_message)
  
  ::proto2_nofieldpresence_unittest::TestAllTypes_NestedMessage* temp = optional_nested_message_;
  optional_nested_message_ = NULL;
  return temp;
}
inline void TestAllTypes::set_allocated_optional_nested_message(::proto2_nofieldpresence_unittest::TestAllTypes_NestedMessage* optional_nested_message) {
  delete optional_nested_message_;
  optional_nested_message_ = optional_nested_message;
  if (optional_nested_message) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_set_allocated:proto2_nofieldpresence_unittest.TestAllTypes.optional_nested_message)
}

// optional .proto2_nofieldpresence_unittest.ForeignMessage optional_foreign_message = 19;
inline bool TestAllTypes::has_optional_foreign_message() const {
  return this != internal_default_instance() && optional_foreign_message_ != NULL;
}
inline void TestAllTypes::clear_optional_foreign_message() {
  if (GetArenaNoVirtual() == NULL && optional_foreign_message_ != NULL) delete optional_foreign_message_;
  optional_foreign_message_ = NULL;
}
inline const ::proto2_nofieldpresence_unittest::ForeignMessage& TestAllTypes::optional_foreign_message() const {
  // @@protoc_insertion_point(field_get:proto2_nofieldpresence_unittest.TestAllTypes.optional_foreign_message)
  return optional_foreign_message_ != NULL ? *optional_foreign_message_
                         : *::proto2_nofieldpresence_unittest::ForeignMessage::internal_default_instance();
}
inline ::proto2_nofieldpresence_unittest::ForeignMessage* TestAllTypes::mutable_optional_foreign_message() {
  
  if (optional_foreign_message_ == NULL) {
    optional_foreign_message_ = new ::proto2_nofieldpresence_unittest::ForeignMessage;
  }
  // @@protoc_insertion_point(field_mutable:proto2_nofieldpresence_unittest.TestAllTypes.optional_foreign_message)
  return optional_foreign_message_;
}
inline ::proto2_nofieldpresence_unittest::ForeignMessage* TestAllTypes::release_optional_foreign_message() {
  // @@protoc_insertion_point(field_release:proto2_nofieldpresence_unittest.TestAllTypes.optional_foreign_message)
  
  ::proto2_nofieldpresence_unittest::ForeignMessage* temp = optional_foreign_message_;
  optional_foreign_message_ = NULL;
  return temp;
}
inline void TestAllTypes::set_allocated_optional_foreign_message(::proto2_nofieldpresence_unittest::ForeignMessage* optional_foreign_message) {
  delete optional_foreign_message_;
  optional_foreign_message_ = optional_foreign_message;
  if (optional_foreign_message) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_set_allocated:proto2_nofieldpresence_unittest.TestAllTypes.optional_foreign_message)
}

// optional .protobuf_unittest.TestAllTypes optional_proto2_message = 20;
inline bool TestAllTypes::has_optional_proto2_message() const {
  return this != internal_default_instance() && optional_proto2_message_ != NULL;
}
inline void TestAllTypes::clear_optional_proto2_message() {
  if (GetArenaNoVirtual() == NULL && optional_proto2_message_ != NULL) delete optional_proto2_message_;
  optional_proto2_message_ = NULL;
}
inline const ::protobuf_unittest::TestAllTypes& TestAllTypes::optional_proto2_message() const {
  // @@protoc_insertion_point(field_get:proto2_nofieldpresence_unittest.TestAllTypes.optional_proto2_message)
  return optional_proto2_message_ != NULL ? *optional_proto2_message_
                         : *::protobuf_unittest::TestAllTypes::internal_default_instance();
}
inline ::protobuf_unittest::TestAllTypes* TestAllTypes::mutable_optional_proto2_message() {
  
  if (optional_proto2_message_ == NULL) {
    optional_proto2_message_ = new ::protobuf_unittest::TestAllTypes;
  }
  // @@protoc_insertion_point(field_mutable:proto2_nofieldpresence_unittest.TestAllTypes.optional_proto2_message)
  return optional_proto2_message_;
}
inline ::protobuf_unittest::TestAllTypes* TestAllTypes::release_optional_proto2_message() {
  // @@protoc_insertion_point(field_release:proto2_nofieldpresence_unittest.TestAllTypes.optional_proto2_message)
  
  ::protobuf_unittest::TestAllTypes* temp = optional_proto2_message_;
  optional_proto2_message_ = NULL;
  return temp;
}
inline void TestAllTypes::set_allocated_optional_proto2_message(::protobuf_unittest::TestAllTypes* optional_proto2_message) {
  delete optional_proto2_message_;
  if (optional_proto2_message != NULL && optional_proto2_message->GetArena() != NULL) {
    ::protobuf_unittest::TestAllTypes* new_optional_proto2_message = new ::protobuf_unittest::TestAllTypes;
    new_optional_proto2_message->CopyFrom(*optional_proto2_message);
    optional_proto2_message = new_optional_proto2_message;
  }
  optional_proto2_message_ = optional_proto2_message;
  if (optional_proto2_message) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_set_allocated:proto2_nofieldpresence_unittest.TestAllTypes.optional_proto2_message)
}

// optional .proto2_nofieldpresence_unittest.TestAllTypes.NestedEnum optional_nested_enum = 21;
inline void TestAllTypes::clear_optional_nested_enum() {
  optional_nested_enum_ = 0;
}
inline ::proto2_nofieldpresence_unittest::TestAllTypes_NestedEnum TestAllTypes::optional_nested_enum() const {
  // @@protoc_insertion_point(field_get:proto2_nofieldpresence_unittest.TestAllTypes.optional_nested_enum)
  return static_cast< ::proto2_nofieldpresence_unittest::TestAllTypes_NestedEnum >(optional_nested_enum_);
}
inline void TestAllTypes::set_optional_nested_enum(::proto2_nofieldpresence_unittest::TestAllTypes_NestedEnum value) {
  
  optional_nested_enum_ = value;
  // @@protoc_insertion_point(field_set:proto2_nofieldpresence_unittest.TestAllTypes.optional_nested_enum)
}

// optional .proto2_nofieldpresence_unittest.ForeignEnum optional_foreign_enum = 22;
inline void TestAllTypes::clear_optional_foreign_enum() {
  optional_foreign_enum_ = 0;
}
inline ::proto2_nofieldpresence_unittest::ForeignEnum TestAllTypes::optional_foreign_enum() const {
  // @@protoc_insertion_point(field_get:proto2_nofieldpresence_unittest.TestAllTypes.optional_foreign_enum)
  return static_cast< ::proto2_nofieldpresence_unittest::ForeignEnum >(optional_foreign_enum_);
}
inline void TestAllTypes::set_optional_foreign_enum(::proto2_nofieldpresence_unittest::ForeignEnum value) {
  
  optional_foreign_enum_ = value;
  // @@protoc_insertion_point(field_set:proto2_nofieldpresence_unittest.TestAllTypes.optional_foreign_enum)
}

// optional string optional_string_piece = 24 [ctype = STRING_PIECE];
inline void TestAllTypes::clear_optional_string_piece() {
  optional_string_piece_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline const ::std::string& TestAllTypes::optional_string_piece() const {
  // @@protoc_insertion_point(field_get:proto2_nofieldpresence_unittest.TestAllTypes.optional_string_piece)
  return optional_string_piece_.GetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void TestAllTypes::set_optional_string_piece(const ::std::string& value) {
  
  optional_string_piece_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:proto2_nofieldpresence_unittest.TestAllTypes.optional_string_piece)
}
inline void TestAllTypes::set_optional_string_piece(const char* value) {
  
  optional_string_piece_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:proto2_nofieldpresence_unittest.TestAllTypes.optional_string_piece)
}
inline void TestAllTypes::set_optional_string_piece(const char* value, size_t size) {
  
  optional_string_piece_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:proto2_nofieldpresence_unittest.TestAllTypes.optional_string_piece)
}
inline ::std::string* TestAllTypes::mutable_optional_string_piece() {
  
  // @@protoc_insertion_point(field_mutable:proto2_nofieldpresence_unittest.TestAllTypes.optional_string_piece)
  return optional_string_piece_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline ::std::string* TestAllTypes::release_optional_string_piece() {
  // @@protoc_insertion_point(field_release:proto2_nofieldpresence_unittest.TestAllTypes.optional_string_piece)
  
  return optional_string_piece_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void TestAllTypes::set_allocated_optional_string_piece(::std::string* optional_string_piece) {
  if (optional_string_piece != NULL) {
    
  } else {
    
  }
  optional_string_piece_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), optional_string_piece);
  // @@protoc_insertion_point(field_set_allocated:proto2_nofieldpresence_unittest.TestAllTypes.optional_string_piece)
}

// optional string optional_cord = 25 [ctype = CORD];
inline void TestAllTypes::clear_optional_cord() {
  optional_cord_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline const ::std::string& TestAllTypes::optional_cord() const {
  // @@protoc_insertion_point(field_get:proto2_nofieldpresence_unittest.TestAllTypes.optional_cord)
  return optional_cord_.GetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void TestAllTypes::set_optional_cord(const ::std::string& value) {
  
  optional_cord_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:proto2_nofieldpresence_unittest.TestAllTypes.optional_cord)
}
inline void TestAllTypes::set_optional_cord(const char* value) {
  
  optional_cord_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:proto2_nofieldpresence_unittest.TestAllTypes.optional_cord)
}
inline void TestAllTypes::set_optional_cord(const char* value, size_t size) {
  
  optional_cord_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:proto2_nofieldpresence_unittest.TestAllTypes.optional_cord)
}
inline ::std::string* TestAllTypes::mutable_optional_cord() {
  
  // @@protoc_insertion_point(field_mutable:proto2_nofieldpresence_unittest.TestAllTypes.optional_cord)
  return optional_cord_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline ::std::string* TestAllTypes::release_optional_cord() {
  // @@protoc_insertion_point(field_release:proto2_nofieldpresence_unittest.TestAllTypes.optional_cord)
  
  return optional_cord_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void TestAllTypes::set_allocated_optional_cord(::std::string* optional_cord) {
  if (optional_cord != NULL) {
    
  } else {
    
  }
  optional_cord_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), optional_cord);
  // @@protoc_insertion_point(field_set_allocated:proto2_nofieldpresence_unittest.TestAllTypes.optional_cord)
}

// optional .proto2_nofieldpresence_unittest.TestAllTypes.NestedMessage optional_lazy_message = 30 [lazy = true];
inline bool TestAllTypes::has_optional_lazy_message() const {
  return this != internal_default_instance() && optional_lazy_message_ != NULL;
}
inline void TestAllTypes::clear_optional_lazy_message() {
  if (GetArenaNoVirtual() == NULL && optional_lazy_message_ != NULL) delete optional_lazy_message_;
  optional_lazy_message_ = NULL;
}
inline const ::proto2_nofieldpresence_unittest::TestAllTypes_NestedMessage& TestAllTypes::optional_lazy_message() const {
  // @@protoc_insertion_point(field_get:proto2_nofieldpresence_unittest.TestAllTypes.optional_lazy_message)
  return optional_lazy_message_ != NULL ? *optional_lazy_message_
                         : *::proto2_nofieldpresence_unittest::TestAllTypes_NestedMessage::internal_default_instance();
}
inline ::proto2_nofieldpresence_unittest::TestAllTypes_NestedMessage* TestAllTypes::mutable_optional_lazy_message() {
  
  if (optional_lazy_message_ == NULL) {
    optional_lazy_message_ = new ::proto2_nofieldpresence_unittest::TestAllTypes_NestedMessage;
  }
  // @@protoc_insertion_point(field_mutable:proto2_nofieldpresence_unittest.TestAllTypes.optional_lazy_message)
  return optional_lazy_message_;
}
inline ::proto2_nofieldpresence_unittest::TestAllTypes_NestedMessage* TestAllTypes::release_optional_lazy_message() {
  // @@protoc_insertion_point(field_release:proto2_nofieldpresence_unittest.TestAllTypes.optional_lazy_message)
  
  ::proto2_nofieldpresence_unittest::TestAllTypes_NestedMessage* temp = optional_lazy_message_;
  optional_lazy_message_ = NULL;
  return temp;
}
inline void TestAllTypes::set_allocated_optional_lazy_message(::proto2_nofieldpresence_unittest::TestAllTypes_NestedMessage* optional_lazy_message) {
  delete optional_lazy_message_;
  optional_lazy_message_ = optional_lazy_message;
  if (optional_lazy_message) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_set_allocated:proto2_nofieldpresence_unittest.TestAllTypes.optional_lazy_message)
}

// repeated int32 repeated_int32 = 31;
inline int TestAllTypes::repeated_int32_size() const {
  return repeated_int32_.size();
}
inline void TestAllTypes::clear_repeated_int32() {
  repeated_int32_.Clear();
}
inline ::google::protobuf::int32 TestAllTypes::repeated_int32(int index) const {
  // @@protoc_insertion_point(field_get:proto2_nofieldpresence_unittest.TestAllTypes.repeated_int32)
  return repeated_int32_.Get(index);
}
inline void TestAllTypes::set_repeated_int32(int index, ::google::protobuf::int32 value) {
  repeated_int32_.Set(index, value);
  // @@protoc_insertion_point(field_set:proto2_nofieldpresence_unittest.TestAllTypes.repeated_int32)
}
inline void TestAllTypes::add_repeated_int32(::google::protobuf::int32 value) {
  repeated_int32_.Add(value);
  // @@protoc_insertion_point(field_add:proto2_nofieldpresence_unittest.TestAllTypes.repeated_int32)
}
inline const ::google::protobuf::RepeatedField< ::google::protobuf::int32 >&
TestAllTypes::repeated_int32() const {
  // @@protoc_insertion_point(field_list:proto2_nofieldpresence_unittest.TestAllTypes.repeated_int32)
  return repeated_int32_;
}
inline ::google::protobuf::RepeatedField< ::google::protobuf::int32 >*
TestAllTypes::mutable_repeated_int32() {
  // @@protoc_insertion_point(field_mutable_list:proto2_nofieldpresence_unittest.TestAllTypes.repeated_int32)
  return &repeated_int32_;
}

// repeated int64 repeated_int64 = 32;
inline int TestAllTypes::repeated_int64_size() const {
  return repeated_int64_.size();
}
inline void TestAllTypes::clear_repeated_int64() {
  repeated_int64_.Clear();
}
inline ::google::protobuf::int64 TestAllTypes::repeated_int64(int index) const {
  // @@protoc_insertion_point(field_get:proto2_nofieldpresence_unittest.TestAllTypes.repeated_int64)
  return repeated_int64_.Get(index);
}
inline void TestAllTypes::set_repeated_int64(int index, ::google::protobuf::int64 value) {
  repeated_int64_.Set(index, value);
  // @@protoc_insertion_point(field_set:proto2_nofieldpresence_unittest.TestAllTypes.repeated_int64)
}
inline void TestAllTypes::add_repeated_int64(::google::protobuf::int64 value) {
  repeated_int64_.Add(value);
  // @@protoc_insertion_point(field_add:proto2_nofieldpresence_unittest.TestAllTypes.repeated_int64)
}
inline const ::google::protobuf::RepeatedField< ::google::protobuf::int64 >&
TestAllTypes::repeated_int64() const {
  // @@protoc_insertion_point(field_list:proto2_nofieldpresence_unittest.TestAllTypes.repeated_int64)
  return repeated_int64_;
}
inline ::google::protobuf::RepeatedField< ::google::protobuf::int64 >*
TestAllTypes::mutable_repeated_int64() {
  // @@protoc_insertion_point(field_mutable_list:proto2_nofieldpresence_unittest.TestAllTypes.repeated_int64)
  return &repeated_int64_;
}

// repeated uint32 repeated_uint32 = 33;
inline int TestAllTypes::repeated_uint32_size() const {
  return repeated_uint32_.size();
}
inline void TestAllTypes::clear_repeated_uint32() {
  repeated_uint32_.Clear();
}
inline ::google::protobuf::uint32 TestAllTypes::repeated_uint32(int index) const {
  // @@protoc_insertion_point(field_get:proto2_nofieldpresence_unittest.TestAllTypes.repeated_uint32)
  return repeated_uint32_.Get(index);
}
inline void TestAllTypes::set_repeated_uint32(int index, ::google::protobuf::uint32 value) {
  repeated_uint32_.Set(index, value);
  // @@protoc_insertion_point(field_set:proto2_nofieldpresence_unittest.TestAllTypes.repeated_uint32)
}
inline void TestAllTypes::add_repeated_uint32(::google::protobuf::uint32 value) {
  repeated_uint32_.Add(value);
  // @@protoc_insertion_point(field_add:proto2_nofieldpresence_unittest.TestAllTypes.repeated_uint32)
}
inline const ::google::protobuf::RepeatedField< ::google::protobuf::uint32 >&
TestAllTypes::repeated_uint32() const {
  // @@protoc_insertion_point(field_list:proto2_nofieldpresence_unittest.TestAllTypes.repeated_uint32)
  return repeated_uint32_;
}
inline ::google::protobuf::RepeatedField< ::google::protobuf::uint32 >*
TestAllTypes::mutable_repeated_uint32() {
  // @@protoc_insertion_point(field_mutable_list:proto2_nofieldpresence_unittest.TestAllTypes.repeated_uint32)
  return &repeated_uint32_;
}

// repeated uint64 repeated_uint64 = 34;
inline int TestAllTypes::repeated_uint64_size() const {
  return repeated_uint64_.size();
}
inline void TestAllTypes::clear_repeated_uint64() {
  repeated_uint64_.Clear();
}
inline ::google::protobuf::uint64 TestAllTypes::repeated_uint64(int index) const {
  // @@protoc_insertion_point(field_get:proto2_nofieldpresence_unittest.TestAllTypes.repeated_uint64)
  return repeated_uint64_.Get(index);
}
inline void TestAllTypes::set_repeated_uint64(int index, ::google::protobuf::uint64 value) {
  repeated_uint64_.Set(index, value);
  // @@protoc_insertion_point(field_set:proto2_nofieldpresence_unittest.TestAllTypes.repeated_uint64)
}
inline void TestAllTypes::add_repeated_uint64(::google::protobuf::uint64 value) {
  repeated_uint64_.Add(value);
  // @@protoc_insertion_point(field_add:proto2_nofieldpresence_unittest.TestAllTypes.repeated_uint64)
}
inline const ::google::protobuf::RepeatedField< ::google::protobuf::uint64 >&
TestAllTypes::repeated_uint64() const {
  // @@protoc_insertion_point(field_list:proto2_nofieldpresence_unittest.TestAllTypes.repeated_uint64)
  return repeated_uint64_;
}
inline ::google::protobuf::RepeatedField< ::google::protobuf::uint64 >*
TestAllTypes::mutable_repeated_uint64() {
  // @@protoc_insertion_point(field_mutable_list:proto2_nofieldpresence_unittest.TestAllTypes.repeated_uint64)
  return &repeated_uint64_;
}

// repeated sint32 repeated_sint32 = 35;
inline int TestAllTypes::repeated_sint32_size() const {
  return repeated_sint32_.size();
}
inline void TestAllTypes::clear_repeated_sint32() {
  repeated_sint32_.Clear();
}
inline ::google::protobuf::int32 TestAllTypes::repeated_sint32(int index) const {
  // @@protoc_insertion_point(field_get:proto2_nofieldpresence_unittest.TestAllTypes.repeated_sint32)
  return repeated_sint32_.Get(index);
}
inline void TestAllTypes::set_repeated_sint32(int index, ::google::protobuf::int32 value) {
  repeated_sint32_.Set(index, value);
  // @@protoc_insertion_point(field_set:proto2_nofieldpresence_unittest.TestAllTypes.repeated_sint32)
}
inline void TestAllTypes::add_repeated_sint32(::google::protobuf::int32 value) {
  repeated_sint32_.Add(value);
  // @@protoc_insertion_point(field_add:proto2_nofieldpresence_unittest.TestAllTypes.repeated_sint32)
}
inline const ::google::protobuf::RepeatedField< ::google::protobuf::int32 >&
TestAllTypes::repeated_sint32() const {
  // @@protoc_insertion_point(field_list:proto2_nofieldpresence_unittest.TestAllTypes.repeated_sint32)
  return repeated_sint32_;
}
inline ::google::protobuf::RepeatedField< ::google::protobuf::int32 >*
TestAllTypes::mutable_repeated_sint32() {
  // @@protoc_insertion_point(field_mutable_list:proto2_nofieldpresence_unittest.TestAllTypes.repeated_sint32)
  return &repeated_sint32_;
}

// repeated sint64 repeated_sint64 = 36;
inline int TestAllTypes::repeated_sint64_size() const {
  return repeated_sint64_.size();
}
inline void TestAllTypes::clear_repeated_sint64() {
  repeated_sint64_.Clear();
}
inline ::google::protobuf::int64 TestAllTypes::repeated_sint64(int index) const {
  // @@protoc_insertion_point(field_get:proto2_nofieldpresence_unittest.TestAllTypes.repeated_sint64)
  return repeated_sint64_.Get(index);
}
inline void TestAllTypes::set_repeated_sint64(int index, ::google::protobuf::int64 value) {
  repeated_sint64_.Set(index, value);
  // @@protoc_insertion_point(field_set:proto2_nofieldpresence_unittest.TestAllTypes.repeated_sint64)
}
inline void TestAllTypes::add_repeated_sint64(::google::protobuf::int64 value) {
  repeated_sint64_.Add(value);
  // @@protoc_insertion_point(field_add:proto2_nofieldpresence_unittest.TestAllTypes.repeated_sint64)
}
inline const ::google::protobuf::RepeatedField< ::google::protobuf::int64 >&
TestAllTypes::repeated_sint64() const {
  // @@protoc_insertion_point(field_list:proto2_nofieldpresence_unittest.TestAllTypes.repeated_sint64)
  return repeated_sint64_;
}
inline ::google::protobuf::RepeatedField< ::google::protobuf::int64 >*
TestAllTypes::mutable_repeated_sint64() {
  // @@protoc_insertion_point(field_mutable_list:proto2_nofieldpresence_unittest.TestAllTypes.repeated_sint64)
  return &repeated_sint64_;
}

// repeated fixed32 repeated_fixed32 = 37;
inline int TestAllTypes::repeated_fixed32_size() const {
  return repeated_fixed32_.size();
}
inline void TestAllTypes::clear_repeated_fixed32() {
  repeated_fixed32_.Clear();
}
inline ::google::protobuf::uint32 TestAllTypes::repeated_fixed32(int index) const {
  // @@protoc_insertion_point(field_get:proto2_nofieldpresence_unittest.TestAllTypes.repeated_fixed32)
  return repeated_fixed32_.Get(index);
}
inline void TestAllTypes::set_repeated_fixed32(int index, ::google::protobuf::uint32 value) {
  repeated_fixed32_.Set(index, value);
  // @@protoc_insertion_point(field_set:proto2_nofieldpresence_unittest.TestAllTypes.repeated_fixed32)
}
inline void TestAllTypes::add_repeated_fixed32(::google::protobuf::uint32 value) {
  repeated_fixed32_.Add(value);
  // @@protoc_insertion_point(field_add:proto2_nofieldpresence_unittest.TestAllTypes.repeated_fixed32)
}
inline const ::google::protobuf::RepeatedField< ::google::protobuf::uint32 >&
TestAllTypes::repeated_fixed32() const {
  // @@protoc_insertion_point(field_list:proto2_nofieldpresence_unittest.TestAllTypes.repeated_fixed32)
  return repeated_fixed32_;
}
inline ::google::protobuf::RepeatedField< ::google::protobuf::uint32 >*
TestAllTypes::mutable_repeated_fixed32() {
  // @@protoc_insertion_point(field_mutable_list:proto2_nofieldpresence_unittest.TestAllTypes.repeated_fixed32)
  return &repeated_fixed32_;
}

// repeated fixed64 repeated_fixed64 = 38;
inline int TestAllTypes::repeated_fixed64_size() const {
  return repeated_fixed64_.size();
}
inline void TestAllTypes::clear_repeated_fixed64() {
  repeated_fixed64_.Clear();
}
inline ::google::protobuf::uint64 TestAllTypes::repeated_fixed64(int index) const {
  // @@protoc_insertion_point(field_get:proto2_nofieldpresence_unittest.TestAllTypes.repeated_fixed64)
  return repeated_fixed64_.Get(index);
}
inline void TestAllTypes::set_repeated_fixed64(int index, ::google::protobuf::uint64 value) {
  repeated_fixed64_.Set(index, value);
  // @@protoc_insertion_point(field_set:proto2_nofieldpresence_unittest.TestAllTypes.repeated_fixed64)
}
inline void TestAllTypes::add_repeated_fixed64(::google::protobuf::uint64 value) {
  repeated_fixed64_.Add(value);
  // @@protoc_insertion_point(field_add:proto2_nofieldpresence_unittest.TestAllTypes.repeated_fixed64)
}
inline const ::google::protobuf::RepeatedField< ::google::protobuf::uint64 >&
TestAllTypes::repeated_fixed64() const {
  // @@protoc_insertion_point(field_list:proto2_nofieldpresence_unittest.TestAllTypes.repeated_fixed64)
  return repeated_fixed64_;
}
inline ::google::protobuf::RepeatedField< ::google::protobuf::uint64 >*
TestAllTypes::mutable_repeated_fixed64() {
  // @@protoc_insertion_point(field_mutable_list:proto2_nofieldpresence_unittest.TestAllTypes.repeated_fixed64)
  return &repeated_fixed64_;
}

// repeated sfixed32 repeated_sfixed32 = 39;
inline int TestAllTypes::repeated_sfixed32_size() const {
  return repeated_sfixed32_.size();
}
inline void TestAllTypes::clear_repeated_sfixed32() {
  repeated_sfixed32_.Clear();
}
inline ::google::protobuf::int32 TestAllTypes::repeated_sfixed32(int index) const {
  // @@protoc_insertion_point(field_get:proto2_nofieldpresence_unittest.TestAllTypes.repeated_sfixed32)
  return repeated_sfixed32_.Get(index);
}
inline void TestAllTypes::set_repeated_sfixed32(int index, ::google::protobuf::int32 value) {
  repeated_sfixed32_.Set(index, value);
  // @@protoc_insertion_point(field_set:proto2_nofieldpresence_unittest.TestAllTypes.repeated_sfixed32)
}
inline void TestAllTypes::add_repeated_sfixed32(::google::protobuf::int32 value) {
  repeated_sfixed32_.Add(value);
  // @@protoc_insertion_point(field_add:proto2_nofieldpresence_unittest.TestAllTypes.repeated_sfixed32)
}
inline const ::google::protobuf::RepeatedField< ::google::protobuf::int32 >&
TestAllTypes::repeated_sfixed32() const {
  // @@protoc_insertion_point(field_list:proto2_nofieldpresence_unittest.TestAllTypes.repeated_sfixed32)
  return repeated_sfixed32_;
}
inline ::google::protobuf::RepeatedField< ::google::protobuf::int32 >*
TestAllTypes::mutable_repeated_sfixed32() {
  // @@protoc_insertion_point(field_mutable_list:proto2_nofieldpresence_unittest.TestAllTypes.repeated_sfixed32)
  return &repeated_sfixed32_;
}

// repeated sfixed64 repeated_sfixed64 = 40;
inline int TestAllTypes::repeated_sfixed64_size() const {
  return repeated_sfixed64_.size();
}
inline void TestAllTypes::clear_repeated_sfixed64() {
  repeated_sfixed64_.Clear();
}
inline ::google::protobuf::int64 TestAllTypes::repeated_sfixed64(int index) const {
  // @@protoc_insertion_point(field_get:proto2_nofieldpresence_unittest.TestAllTypes.repeated_sfixed64)
  return repeated_sfixed64_.Get(index);
}
inline void TestAllTypes::set_repeated_sfixed64(int index, ::google::protobuf::int64 value) {
  repeated_sfixed64_.Set(index, value);
  // @@protoc_insertion_point(field_set:proto2_nofieldpresence_unittest.TestAllTypes.repeated_sfixed64)
}
inline void TestAllTypes::add_repeated_sfixed64(::google::protobuf::int64 value) {
  repeated_sfixed64_.Add(value);
  // @@protoc_insertion_point(field_add:proto2_nofieldpresence_unittest.TestAllTypes.repeated_sfixed64)
}
inline const ::google::protobuf::RepeatedField< ::google::protobuf::int64 >&
TestAllTypes::repeated_sfixed64() const {
  // @@protoc_insertion_point(field_list:proto2_nofieldpresence_unittest.TestAllTypes.repeated_sfixed64)
  return repeated_sfixed64_;
}
inline ::google::protobuf::RepeatedField< ::google::protobuf::int64 >*
TestAllTypes::mutable_repeated_sfixed64() {
  // @@protoc_insertion_point(field_mutable_list:proto2_nofieldpresence_unittest.TestAllTypes.repeated_sfixed64)
  return &repeated_sfixed64_;
}

// repeated float repeated_float = 41;
inline int TestAllTypes::repeated_float_size() const {
  return repeated_float_.size();
}
inline void TestAllTypes::clear_repeated_float() {
  repeated_float_.Clear();
}
inline float TestAllTypes::repeated_float(int index) const {
  // @@protoc_insertion_point(field_get:proto2_nofieldpresence_unittest.TestAllTypes.repeated_float)
  return repeated_float_.Get(index);
}
inline void TestAllTypes::set_repeated_float(int index, float value) {
  repeated_float_.Set(index, value);
  // @@protoc_insertion_point(field_set:proto2_nofieldpresence_unittest.TestAllTypes.repeated_float)
}
inline void TestAllTypes::add_repeated_float(float value) {
  repeated_float_.Add(value);
  // @@protoc_insertion_point(field_add:proto2_nofieldpresence_unittest.TestAllTypes.repeated_float)
}
inline const ::google::protobuf::RepeatedField< float >&
TestAllTypes::repeated_float() const {
  // @@protoc_insertion_point(field_list:proto2_nofieldpresence_unittest.TestAllTypes.repeated_float)
  return repeated_float_;
}
inline ::google::protobuf::RepeatedField< float >*
TestAllTypes::mutable_repeated_float() {
  // @@protoc_insertion_point(field_mutable_list:proto2_nofieldpresence_unittest.TestAllTypes.repeated_float)
  return &repeated_float_;
}

// repeated double repeated_double = 42;
inline int TestAllTypes::repeated_double_size() const {
  return repeated_double_.size();
}
inline void TestAllTypes::clear_repeated_double() {
  repeated_double_.Clear();
}
inline double TestAllTypes::repeated_double(int index) const {
  // @@protoc_insertion_point(field_get:proto2_nofieldpresence_unittest.TestAllTypes.repeated_double)
  return repeated_double_.Get(index);
}
inline void TestAllTypes::set_repeated_double(int index, double value) {
  repeated_double_.Set(index, value);
  // @@protoc_insertion_point(field_set:proto2_nofieldpresence_unittest.TestAllTypes.repeated_double)
}
inline void TestAllTypes::add_repeated_double(double value) {
  repeated_double_.Add(value);
  // @@protoc_insertion_point(field_add:proto2_nofieldpresence_unittest.TestAllTypes.repeated_double)
}
inline const ::google::protobuf::RepeatedField< double >&
TestAllTypes::repeated_double() const {
  // @@protoc_insertion_point(field_list:proto2_nofieldpresence_unittest.TestAllTypes.repeated_double)
  return repeated_double_;
}
inline ::google::protobuf::RepeatedField< double >*
TestAllTypes::mutable_repeated_double() {
  // @@protoc_insertion_point(field_mutable_list:proto2_nofieldpresence_unittest.TestAllTypes.repeated_double)
  return &repeated_double_;
}

// repeated bool repeated_bool = 43;
inline int TestAllTypes::repeated_bool_size() const {
  return repeated_bool_.size();
}
inline void TestAllTypes::clear_repeated_bool() {
  repeated_bool_.Clear();
}
inline bool TestAllTypes::repeated_bool(int index) const {
  // @@protoc_insertion_point(field_get:proto2_nofieldpresence_unittest.TestAllTypes.repeated_bool)
  return repeated_bool_.Get(index);
}
inline void TestAllTypes::set_repeated_bool(int index, bool value) {
  repeated_bool_.Set(index, value);
  // @@protoc_insertion_point(field_set:proto2_nofieldpresence_unittest.TestAllTypes.repeated_bool)
}
inline void TestAllTypes::add_repeated_bool(bool value) {
  repeated_bool_.Add(value);
  // @@protoc_insertion_point(field_add:proto2_nofieldpresence_unittest.TestAllTypes.repeated_bool)
}
inline const ::google::protobuf::RepeatedField< bool >&
TestAllTypes::repeated_bool() const {
  // @@protoc_insertion_point(field_list:proto2_nofieldpresence_unittest.TestAllTypes.repeated_bool)
  return repeated_bool_;
}
inline ::google::protobuf::RepeatedField< bool >*
TestAllTypes::mutable_repeated_bool() {
  // @@protoc_insertion_point(field_mutable_list:proto2_nofieldpresence_unittest.TestAllTypes.repeated_bool)
  return &repeated_bool_;
}

// repeated string repeated_string = 44;
inline int TestAllTypes::repeated_string_size() const {
  return repeated_string_.size();
}
inline void TestAllTypes::clear_repeated_string() {
  repeated_string_.Clear();
}
inline const ::std::string& TestAllTypes::repeated_string(int index) const {
  // @@protoc_insertion_point(field_get:proto2_nofieldpresence_unittest.TestAllTypes.repeated_string)
  return repeated_string_.Get(index);
}
inline ::std::string* TestAllTypes::mutable_repeated_string(int index) {
  // @@protoc_insertion_point(field_mutable:proto2_nofieldpresence_unittest.TestAllTypes.repeated_string)
  return repeated_string_.Mutable(index);
}
inline void TestAllTypes::set_repeated_string(int index, const ::std::string& value) {
  // @@protoc_insertion_point(field_set:proto2_nofieldpresence_unittest.TestAllTypes.repeated_string)
  repeated_string_.Mutable(index)->assign(value);
}
inline void TestAllTypes::set_repeated_string(int index, const char* value) {
  repeated_string_.Mutable(index)->assign(value);
  // @@protoc_insertion_point(field_set_char:proto2_nofieldpresence_unittest.TestAllTypes.repeated_string)
}
inline void TestAllTypes::set_repeated_string(int index, const char* value, size_t size) {
  repeated_string_.Mutable(index)->assign(
    reinterpret_cast<const char*>(value), size);
  // @@protoc_insertion_point(field_set_pointer:proto2_nofieldpresence_unittest.TestAllTypes.repeated_string)
}
inline ::std::string* TestAllTypes::add_repeated_string() {
  // @@protoc_insertion_point(field_add_mutable:proto2_nofieldpresence_unittest.TestAllTypes.repeated_string)
  return repeated_string_.Add();
}
inline void TestAllTypes::add_repeated_string(const ::std::string& value) {
  repeated_string_.Add()->assign(value);
  // @@protoc_insertion_point(field_add:proto2_nofieldpresence_unittest.TestAllTypes.repeated_string)
}
inline void TestAllTypes::add_repeated_string(const char* value) {
  repeated_string_.Add()->assign(value);
  // @@protoc_insertion_point(field_add_char:proto2_nofieldpresence_unittest.TestAllTypes.repeated_string)
}
inline void TestAllTypes::add_repeated_string(const char* value, size_t size) {
  repeated_string_.Add()->assign(reinterpret_cast<const char*>(value), size);
  // @@protoc_insertion_point(field_add_pointer:proto2_nofieldpresence_unittest.TestAllTypes.repeated_string)
}
inline const ::google::protobuf::RepeatedPtrField< ::std::string>&
TestAllTypes::repeated_string() const {
  // @@protoc_insertion_point(field_list:proto2_nofieldpresence_unittest.TestAllTypes.repeated_string)
  return repeated_string_;
}
inline ::google::protobuf::RepeatedPtrField< ::std::string>*
TestAllTypes::mutable_repeated_string() {
  // @@protoc_insertion_point(field_mutable_list:proto2_nofieldpresence_unittest.TestAllTypes.repeated_string)
  return &repeated_string_;
}

// repeated bytes repeated_bytes = 45;
inline int TestAllTypes::repeated_bytes_size() const {
  return repeated_bytes_.size();
}
inline void TestAllTypes::clear_repeated_bytes() {
  repeated_bytes_.Clear();
}
inline const ::std::string& TestAllTypes::repeated_bytes(int index) const {
  // @@protoc_insertion_point(field_get:proto2_nofieldpresence_unittest.TestAllTypes.repeated_bytes)
  return repeated_bytes_.Get(index);
}
inline ::std::string* TestAllTypes::mutable_repeated_bytes(int index) {
  // @@protoc_insertion_point(field_mutable:proto2_nofieldpresence_unittest.TestAllTypes.repeated_bytes)
  return repeated_bytes_.Mutable(index);
}
inline void TestAllTypes::set_repeated_bytes(int index, const ::std::string& value) {
  // @@protoc_insertion_point(field_set:proto2_nofieldpresence_unittest.TestAllTypes.repeated_bytes)
  repeated_bytes_.Mutable(index)->assign(value);
}
inline void TestAllTypes::set_repeated_bytes(int index, const char* value) {
  repeated_bytes_.Mutable(index)->assign(value);
  // @@protoc_insertion_point(field_set_char:proto2_nofieldpresence_unittest.TestAllTypes.repeated_bytes)
}
inline void TestAllTypes::set_repeated_bytes(int index, const void* value, size_t size) {
  repeated_bytes_.Mutable(index)->assign(
    reinterpret_cast<const char*>(value), size);
  // @@protoc_insertion_point(field_set_pointer:proto2_nofieldpresence_unittest.TestAllTypes.repeated_bytes)
}
inline ::std::string* TestAllTypes::add_repeated_bytes() {
  // @@protoc_insertion_point(field_add_mutable:proto2_nofieldpresence_unittest.TestAllTypes.repeated_bytes)
  return repeated_bytes_.Add();
}
inline void TestAllTypes::add_repeated_bytes(const ::std::string& value) {
  repeated_bytes_.Add()->assign(value);
  // @@protoc_insertion_point(field_add:proto2_nofieldpresence_unittest.TestAllTypes.repeated_bytes)
}
inline void TestAllTypes::add_repeated_bytes(const char* value) {
  repeated_bytes_.Add()->assign(value);
  // @@protoc_insertion_point(field_add_char:proto2_nofieldpresence_unittest.TestAllTypes.repeated_bytes)
}
inline void TestAllTypes::add_repeated_bytes(const void* value, size_t size) {
  repeated_bytes_.Add()->assign(reinterpret_cast<const char*>(value), size);
  // @@protoc_insertion_point(field_add_pointer:proto2_nofieldpresence_unittest.TestAllTypes.repeated_bytes)
}
inline const ::google::protobuf::RepeatedPtrField< ::std::string>&
TestAllTypes::repeated_bytes() const {
  // @@protoc_insertion_point(field_list:proto2_nofieldpresence_unittest.TestAllTypes.repeated_bytes)
  return repeated_bytes_;
}
inline ::google::protobuf::RepeatedPtrField< ::std::string>*
TestAllTypes::mutable_repeated_bytes() {
  // @@protoc_insertion_point(field_mutable_list:proto2_nofieldpresence_unittest.TestAllTypes.repeated_bytes)
  return &repeated_bytes_;
}

// repeated .proto2_nofieldpresence_unittest.TestAllTypes.NestedMessage repeated_nested_message = 48;
inline int TestAllTypes::repeated_nested_message_size() const {
  return repeated_nested_message_.size();
}
inline void TestAllTypes::clear_repeated_nested_message() {
  repeated_nested_message_.Clear();
}
inline const ::proto2_nofieldpresence_unittest::TestAllTypes_NestedMessage& TestAllTypes::repeated_nested_message(int index) const {
  // @@protoc_insertion_point(field_get:proto2_nofieldpresence_unittest.TestAllTypes.repeated_nested_message)
  return repeated_nested_message_.Get(index);
}
inline ::proto2_nofieldpresence_unittest::TestAllTypes_NestedMessage* TestAllTypes::mutable_repeated_nested_message(int index) {
  // @@protoc_insertion_point(field_mutable:proto2_nofieldpresence_unittest.TestAllTypes.repeated_nested_message)
  return repeated_nested_message_.Mutable(index);
}
inline ::proto2_nofieldpresence_unittest::TestAllTypes_NestedMessage* TestAllTypes::add_repeated_nested_message() {
  // @@protoc_insertion_point(field_add:proto2_nofieldpresence_unittest.TestAllTypes.repeated_nested_message)
  return repeated_nested_message_.Add();
}
inline ::google::protobuf::RepeatedPtrField< ::proto2_nofieldpresence_unittest::TestAllTypes_NestedMessage >*
TestAllTypes::mutable_repeated_nested_message() {
  // @@protoc_insertion_point(field_mutable_list:proto2_nofieldpresence_unittest.TestAllTypes.repeated_nested_message)
  return &repeated_nested_message_;
}
inline const ::google::protobuf::RepeatedPtrField< ::proto2_nofieldpresence_unittest::TestAllTypes_NestedMessage >&
TestAllTypes::repeated_nested_message() const {
  // @@protoc_insertion_point(field_list:proto2_nofieldpresence_unittest.TestAllTypes.repeated_nested_message)
  return repeated_nested_message_;
}

// repeated .proto2_nofieldpresence_unittest.ForeignMessage repeated_foreign_message = 49;
inline int TestAllTypes::repeated_foreign_message_size() const {
  return repeated_foreign_message_.size();
}
inline void TestAllTypes::clear_repeated_foreign_message() {
  repeated_foreign_message_.Clear();
}
inline const ::proto2_nofieldpresence_unittest::ForeignMessage& TestAllTypes::repeated_foreign_message(int index) const {
  // @@protoc_insertion_point(field_get:proto2_nofieldpresence_unittest.TestAllTypes.repeated_foreign_message)
  return repeated_foreign_message_.Get(index);
}
inline ::proto2_nofieldpresence_unittest::ForeignMessage* TestAllTypes::mutable_repeated_foreign_message(int index) {
  // @@protoc_insertion_point(field_mutable:proto2_nofieldpresence_unittest.TestAllTypes.repeated_foreign_message)
  return repeated_foreign_message_.Mutable(index);
}
inline ::proto2_nofieldpresence_unittest::ForeignMessage* TestAllTypes::add_repeated_foreign_message() {
  // @@protoc_insertion_point(field_add:proto2_nofieldpresence_unittest.TestAllTypes.repeated_foreign_message)
  return repeated_foreign_message_.Add();
}
inline ::google::protobuf::RepeatedPtrField< ::proto2_nofieldpresence_unittest::ForeignMessage >*
TestAllTypes::mutable_repeated_foreign_message() {
  // @@protoc_insertion_point(field_mutable_list:proto2_nofieldpresence_unittest.TestAllTypes.repeated_foreign_message)
  return &repeated_foreign_message_;
}
inline const ::google::protobuf::RepeatedPtrField< ::proto2_nofieldpresence_unittest::ForeignMessage >&
TestAllTypes::repeated_foreign_message() const {
  // @@protoc_insertion_point(field_list:proto2_nofieldpresence_unittest.TestAllTypes.repeated_foreign_message)
  return repeated_foreign_message_;
}

// repeated .protobuf_unittest.TestAllTypes repeated_proto2_message = 50;
inline int TestAllTypes::repeated_proto2_message_size() const {
  return repeated_proto2_message_.size();
}
inline void TestAllTypes::clear_repeated_proto2_message() {
  repeated_proto2_message_.Clear();
}
inline const ::protobuf_unittest::TestAllTypes& TestAllTypes::repeated_proto2_message(int index) const {
  // @@protoc_insertion_point(field_get:proto2_nofieldpresence_unittest.TestAllTypes.repeated_proto2_message)
  return repeated_proto2_message_.Get(index);
}
inline ::protobuf_unittest::TestAllTypes* TestAllTypes::mutable_repeated_proto2_message(int index) {
  // @@protoc_insertion_point(field_mutable:proto2_nofieldpresence_unittest.TestAllTypes.repeated_proto2_message)
  return repeated_proto2_message_.Mutable(index);
}
inline ::protobuf_unittest::TestAllTypes* TestAllTypes::add_repeated_proto2_message() {
  // @@protoc_insertion_point(field_add:proto2_nofieldpresence_unittest.TestAllTypes.repeated_proto2_message)
  return repeated_proto2_message_.Add();
}
inline ::google::protobuf::RepeatedPtrField< ::protobuf_unittest::TestAllTypes >*
TestAllTypes::mutable_repeated_proto2_message() {
  // @@protoc_insertion_point(field_mutable_list:proto2_nofieldpresence_unittest.TestAllTypes.repeated_proto2_message)
  return &repeated_proto2_message_;
}
inline const ::google::protobuf::RepeatedPtrField< ::protobuf_unittest::TestAllTypes >&
TestAllTypes::repeated_proto2_message() const {
  // @@protoc_insertion_point(field_list:proto2_nofieldpresence_unittest.TestAllTypes.repeated_proto2_message)
  return repeated_proto2_message_;
}

// repeated .proto2_nofieldpresence_unittest.TestAllTypes.NestedEnum repeated_nested_enum = 51;
inline int TestAllTypes::repeated_nested_enum_size() const {
  return repeated_nested_enum_.size();
}
inline void TestAllTypes::clear_repeated_nested_enum() {
  repeated_nested_enum_.Clear();
}
inline ::proto2_nofieldpresence_unittest::TestAllTypes_NestedEnum TestAllTypes::repeated_nested_enum(int index) const {
  // @@protoc_insertion_point(field_get:proto2_nofieldpresence_unittest.TestAllTypes.repeated_nested_enum)
  return static_cast< ::proto2_nofieldpresence_unittest::TestAllTypes_NestedEnum >(repeated_nested_enum_.Get(index));
}
inline void TestAllTypes::set_repeated_nested_enum(int index, ::proto2_nofieldpresence_unittest::TestAllTypes_NestedEnum value) {
  repeated_nested_enum_.Set(index, value);
  // @@protoc_insertion_point(field_set:proto2_nofieldpresence_unittest.TestAllTypes.repeated_nested_enum)
}
inline void TestAllTypes::add_repeated_nested_enum(::proto2_nofieldpresence_unittest::TestAllTypes_NestedEnum value) {
  repeated_nested_enum_.Add(value);
  // @@protoc_insertion_point(field_add:proto2_nofieldpresence_unittest.TestAllTypes.repeated_nested_enum)
}
inline const ::google::protobuf::RepeatedField<int>&
TestAllTypes::repeated_nested_enum() const {
  // @@protoc_insertion_point(field_list:proto2_nofieldpresence_unittest.TestAllTypes.repeated_nested_enum)
  return repeated_nested_enum_;
}
inline ::google::protobuf::RepeatedField<int>*
TestAllTypes::mutable_repeated_nested_enum() {
  // @@protoc_insertion_point(field_mutable_list:proto2_nofieldpresence_unittest.TestAllTypes.repeated_nested_enum)
  return &repeated_nested_enum_;
}

// repeated .proto2_nofieldpresence_unittest.ForeignEnum repeated_foreign_enum = 52;
inline int TestAllTypes::repeated_foreign_enum_size() const {
  return repeated_foreign_enum_.size();
}
inline void TestAllTypes::clear_repeated_foreign_enum() {
  repeated_foreign_enum_.Clear();
}
inline ::proto2_nofieldpresence_unittest::ForeignEnum TestAllTypes::repeated_foreign_enum(int index) const {
  // @@protoc_insertion_point(field_get:proto2_nofieldpresence_unittest.TestAllTypes.repeated_foreign_enum)
  return static_cast< ::proto2_nofieldpresence_unittest::ForeignEnum >(repeated_foreign_enum_.Get(index));
}
inline void TestAllTypes::set_repeated_foreign_enum(int index, ::proto2_nofieldpresence_unittest::ForeignEnum value) {
  repeated_foreign_enum_.Set(index, value);
  // @@protoc_insertion_point(field_set:proto2_nofieldpresence_unittest.TestAllTypes.repeated_foreign_enum)
}
inline void TestAllTypes::add_repeated_foreign_enum(::proto2_nofieldpresence_unittest::ForeignEnum value) {
  repeated_foreign_enum_.Add(value);
  // @@protoc_insertion_point(field_add:proto2_nofieldpresence_unittest.TestAllTypes.repeated_foreign_enum)
}
inline const ::google::protobuf::RepeatedField<int>&
TestAllTypes::repeated_foreign_enum() const {
  // @@protoc_insertion_point(field_list:proto2_nofieldpresence_unittest.TestAllTypes.repeated_foreign_enum)
  return repeated_foreign_enum_;
}
inline ::google::protobuf::RepeatedField<int>*
TestAllTypes::mutable_repeated_foreign_enum() {
  // @@protoc_insertion_point(field_mutable_list:proto2_nofieldpresence_unittest.TestAllTypes.repeated_foreign_enum)
  return &repeated_foreign_enum_;
}

// repeated string repeated_string_piece = 54 [ctype = STRING_PIECE];
inline int TestAllTypes::repeated_string_piece_size() const {
  return repeated_string_piece_.size();
}
inline void TestAllTypes::clear_repeated_string_piece() {
  repeated_string_piece_.Clear();
}
inline const ::std::string& TestAllTypes::repeated_string_piece(int index) const {
  // @@protoc_insertion_point(field_get:proto2_nofieldpresence_unittest.TestAllTypes.repeated_string_piece)
  return repeated_string_piece_.Get(index);
}
inline ::std::string* TestAllTypes::mutable_repeated_string_piece(int index) {
  // @@protoc_insertion_point(field_mutable:proto2_nofieldpresence_unittest.TestAllTypes.repeated_string_piece)
  return repeated_string_piece_.Mutable(index);
}
inline void TestAllTypes::set_repeated_string_piece(int index, const ::std::string& value) {
  // @@protoc_insertion_point(field_set:proto2_nofieldpresence_unittest.TestAllTypes.repeated_string_piece)
  repeated_string_piece_.Mutable(index)->assign(value);
}
inline void TestAllTypes::set_repeated_string_piece(int index, const char* value) {
  repeated_string_piece_.Mutable(index)->assign(value);
  // @@protoc_insertion_point(field_set_char:proto2_nofieldpresence_unittest.TestAllTypes.repeated_string_piece)
}
inline void TestAllTypes::set_repeated_string_piece(int index, const char* value, size_t size) {
  repeated_string_piece_.Mutable(index)->assign(
    reinterpret_cast<const char*>(value), size);
  // @@protoc_insertion_point(field_set_pointer:proto2_nofieldpresence_unittest.TestAllTypes.repeated_string_piece)
}
inline ::std::string* TestAllTypes::add_repeated_string_piece() {
  // @@protoc_insertion_point(field_add_mutable:proto2_nofieldpresence_unittest.TestAllTypes.repeated_string_piece)
  return repeated_string_piece_.Add();
}
inline void TestAllTypes::add_repeated_string_piece(const ::std::string& value) {
  repeated_string_piece_.Add()->assign(value);
  // @@protoc_insertion_point(field_add:proto2_nofieldpresence_unittest.TestAllTypes.repeated_string_piece)
}
inline void TestAllTypes::add_repeated_string_piece(const char* value) {
  repeated_string_piece_.Add()->assign(value);
  // @@protoc_insertion_point(field_add_char:proto2_nofieldpresence_unittest.TestAllTypes.repeated_string_piece)
}
inline void TestAllTypes::add_repeated_string_piece(const char* value, size_t size) {
  repeated_string_piece_.Add()->assign(reinterpret_cast<const char*>(value), size);
  // @@protoc_insertion_point(field_add_pointer:proto2_nofieldpresence_unittest.TestAllTypes.repeated_string_piece)
}
inline const ::google::protobuf::RepeatedPtrField< ::std::string>&
TestAllTypes::repeated_string_piece() const {
  // @@protoc_insertion_point(field_list:proto2_nofieldpresence_unittest.TestAllTypes.repeated_string_piece)
  return repeated_string_piece_;
}
inline ::google::protobuf::RepeatedPtrField< ::std::string>*
TestAllTypes::mutable_repeated_string_piece() {
  // @@protoc_insertion_point(field_mutable_list:proto2_nofieldpresence_unittest.TestAllTypes.repeated_string_piece)
  return &repeated_string_piece_;
}

// repeated string repeated_cord = 55 [ctype = CORD];
inline int TestAllTypes::repeated_cord_size() const {
  return repeated_cord_.size();
}
inline void TestAllTypes::clear_repeated_cord() {
  repeated_cord_.Clear();
}
inline const ::std::string& TestAllTypes::repeated_cord(int index) const {
  // @@protoc_insertion_point(field_get:proto2_nofieldpresence_unittest.TestAllTypes.repeated_cord)
  return repeated_cord_.Get(index);
}
inline ::std::string* TestAllTypes::mutable_repeated_cord(int index) {
  // @@protoc_insertion_point(field_mutable:proto2_nofieldpresence_unittest.TestAllTypes.repeated_cord)
  return repeated_cord_.Mutable(index);
}
inline void TestAllTypes::set_repeated_cord(int index, const ::std::string& value) {
  // @@protoc_insertion_point(field_set:proto2_nofieldpresence_unittest.TestAllTypes.repeated_cord)
  repeated_cord_.Mutable(index)->assign(value);
}
inline void TestAllTypes::set_repeated_cord(int index, const char* value) {
  repeated_cord_.Mutable(index)->assign(value);
  // @@protoc_insertion_point(field_set_char:proto2_nofieldpresence_unittest.TestAllTypes.repeated_cord)
}
inline void TestAllTypes::set_repeated_cord(int index, const char* value, size_t size) {
  repeated_cord_.Mutable(index)->assign(
    reinterpret_cast<const char*>(value), size);
  // @@protoc_insertion_point(field_set_pointer:proto2_nofieldpresence_unittest.TestAllTypes.repeated_cord)
}
inline ::std::string* TestAllTypes::add_repeated_cord() {
  // @@protoc_insertion_point(field_add_mutable:proto2_nofieldpresence_unittest.TestAllTypes.repeated_cord)
  return repeated_cord_.Add();
}
inline void TestAllTypes::add_repeated_cord(const ::std::string& value) {
  repeated_cord_.Add()->assign(value);
  // @@protoc_insertion_point(field_add:proto2_nofieldpresence_unittest.TestAllTypes.repeated_cord)
}
inline void TestAllTypes::add_repeated_cord(const char* value) {
  repeated_cord_.Add()->assign(value);
  // @@protoc_insertion_point(field_add_char:proto2_nofieldpresence_unittest.TestAllTypes.repeated_cord)
}
inline void TestAllTypes::add_repeated_cord(const char* value, size_t size) {
  repeated_cord_.Add()->assign(reinterpret_cast<const char*>(value), size);
  // @@protoc_insertion_point(field_add_pointer:proto2_nofieldpresence_unittest.TestAllTypes.repeated_cord)
}
inline const ::google::protobuf::RepeatedPtrField< ::std::string>&
TestAllTypes::repeated_cord() const {
  // @@protoc_insertion_point(field_list:proto2_nofieldpresence_unittest.TestAllTypes.repeated_cord)
  return repeated_cord_;
}
inline ::google::protobuf::RepeatedPtrField< ::std::string>*
TestAllTypes::mutable_repeated_cord() {
  // @@protoc_insertion_point(field_mutable_list:proto2_nofieldpresence_unittest.TestAllTypes.repeated_cord)
  return &repeated_cord_;
}

// repeated .proto2_nofieldpresence_unittest.TestAllTypes.NestedMessage repeated_lazy_message = 57 [lazy = true];
inline int TestAllTypes::repeated_lazy_message_size() const {
  return repeated_lazy_message_.size();
}
inline void TestAllTypes::clear_repeated_lazy_message() {
  repeated_lazy_message_.Clear();
}
inline const ::proto2_nofieldpresence_unittest::TestAllTypes_NestedMessage& TestAllTypes::repeated_lazy_message(int index) const {
  // @@protoc_insertion_point(field_get:proto2_nofieldpresence_unittest.TestAllTypes.repeated_lazy_message)
  return repeated_lazy_message_.Get(index);
}
inline ::proto2_nofieldpresence_unittest::TestAllTypes_NestedMessage* TestAllTypes::mutable_repeated_lazy_message(int index) {
  // @@protoc_insertion_point(field_mutable:proto2_nofieldpresence_unittest.TestAllTypes.repeated_lazy_message)
  return repeated_lazy_message_.Mutable(index);
}
inline ::proto2_nofieldpresence_unittest::TestAllTypes_NestedMessage* TestAllTypes::add_repeated_lazy_message() {
  // @@protoc_insertion_point(field_add:proto2_nofieldpresence_unittest.TestAllTypes.repeated_lazy_message)
  return repeated_lazy_message_.Add();
}
inline ::google::protobuf::RepeatedPtrField< ::proto2_nofieldpresence_unittest::TestAllTypes_NestedMessage >*
TestAllTypes::mutable_repeated_lazy_message() {
  // @@protoc_insertion_point(field_mutable_list:proto2_nofieldpresence_unittest.TestAllTypes.repeated_lazy_message)
  return &repeated_lazy_message_;
}
inline const ::google::protobuf::RepeatedPtrField< ::proto2_nofieldpresence_unittest::TestAllTypes_NestedMessage >&
TestAllTypes::repeated_lazy_message() const {
  // @@protoc_insertion_point(field_list:proto2_nofieldpresence_unittest.TestAllTypes.repeated_lazy_message)
  return repeated_lazy_message_;
}

// optional uint32 oneof_uint32 = 111;
inline bool TestAllTypes::has_oneof_uint32() const {
  return oneof_field_case() == kOneofUint32;
}
inline void TestAllTypes::set_has_oneof_uint32() {
  _oneof_case_[0] = kOneofUint32;
}
inline void TestAllTypes::clear_oneof_uint32() {
  if (has_oneof_uint32()) {
    oneof_field_.oneof_uint32_ = 0u;
    clear_has_oneof_field();
  }
}
inline ::google::protobuf::uint32 TestAllTypes::oneof_uint32() const {
  // @@protoc_insertion_point(field_get:proto2_nofieldpresence_unittest.TestAllTypes.oneof_uint32)
  if (has_oneof_uint32()) {
    return oneof_field_.oneof_uint32_;
  }
  return 0u;
}
inline void TestAllTypes::set_oneof_uint32(::google::protobuf::uint32 value) {
  if (!has_oneof_uint32()) {
    clear_oneof_field();
    set_has_oneof_uint32();
  }
  oneof_field_.oneof_uint32_ = value;
  // @@protoc_insertion_point(field_set:proto2_nofieldpresence_unittest.TestAllTypes.oneof_uint32)
}

// optional .proto2_nofieldpresence_unittest.TestAllTypes.NestedMessage oneof_nested_message = 112;
inline bool TestAllTypes::has_oneof_nested_message() const {
  return oneof_field_case() == kOneofNestedMessage;
}
inline void TestAllTypes::set_has_oneof_nested_message() {
  _oneof_case_[0] = kOneofNestedMessage;
}
inline void TestAllTypes::clear_oneof_nested_message() {
  if (has_oneof_nested_message()) {
    delete oneof_field_.oneof_nested_message_;
    clear_has_oneof_field();
  }
}
inline  const ::proto2_nofieldpresence_unittest::TestAllTypes_NestedMessage& TestAllTypes::oneof_nested_message() const {
  // @@protoc_insertion_point(field_get:proto2_nofieldpresence_unittest.TestAllTypes.oneof_nested_message)
  return has_oneof_nested_message()
      ? *oneof_field_.oneof_nested_message_
      : ::proto2_nofieldpresence_unittest::TestAllTypes_NestedMessage::default_instance();
}
inline ::proto2_nofieldpresence_unittest::TestAllTypes_NestedMessage* TestAllTypes::mutable_oneof_nested_message() {
  if (!has_oneof_nested_message()) {
    clear_oneof_field();
    set_has_oneof_nested_message();
    oneof_field_.oneof_nested_message_ = new ::proto2_nofieldpresence_unittest::TestAllTypes_NestedMessage;
  }
  // @@protoc_insertion_point(field_mutable:proto2_nofieldpresence_unittest.TestAllTypes.oneof_nested_message)
  return oneof_field_.oneof_nested_message_;
}
inline ::proto2_nofieldpresence_unittest::TestAllTypes_NestedMessage* TestAllTypes::release_oneof_nested_message() {
  // @@protoc_insertion_point(field_release:proto2_nofieldpresence_unittest.TestAllTypes.oneof_nested_message)
  if (has_oneof_nested_message()) {
    clear_has_oneof_field();
    ::proto2_nofieldpresence_unittest::TestAllTypes_NestedMessage* temp = oneof_field_.oneof_nested_message_;
    oneof_field_.oneof_nested_message_ = NULL;
    return temp;
  } else {
    return NULL;
  }
}
inline void TestAllTypes::set_allocated_oneof_nested_message(::proto2_nofieldpresence_unittest::TestAllTypes_NestedMessage* oneof_nested_message) {
  clear_oneof_field();
  if (oneof_nested_message) {
    set_has_oneof_nested_message();
    oneof_field_.oneof_nested_message_ = oneof_nested_message;
  }
  // @@protoc_insertion_point(field_set_allocated:proto2_nofieldpresence_unittest.TestAllTypes.oneof_nested_message)
}

// optional string oneof_string = 113;
inline bool TestAllTypes::has_oneof_string() const {
  return oneof_field_case() == kOneofString;
}
inline void TestAllTypes::set_has_oneof_string() {
  _oneof_case_[0] = kOneofString;
}
inline void TestAllTypes::clear_oneof_string() {
  if (has_oneof_string()) {
    oneof_field_.oneof_string_.DestroyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
    clear_has_oneof_field();
  }
}
inline const ::std::string& TestAllTypes::oneof_string() const {
  // @@protoc_insertion_point(field_get:proto2_nofieldpresence_unittest.TestAllTypes.oneof_string)
  if (has_oneof_string()) {
    return oneof_field_.oneof_string_.GetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  }
  return *&::google::protobuf::internal::GetEmptyStringAlreadyInited();
}
inline void TestAllTypes::set_oneof_string(const ::std::string& value) {
  // @@protoc_insertion_point(field_set:proto2_nofieldpresence_unittest.TestAllTypes.oneof_string)
  if (!has_oneof_string()) {
    clear_oneof_field();
    set_has_oneof_string();
    oneof_field_.oneof_string_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  }
  oneof_field_.oneof_string_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:proto2_nofieldpresence_unittest.TestAllTypes.oneof_string)
}
inline void TestAllTypes::set_oneof_string(const char* value) {
  if (!has_oneof_string()) {
    clear_oneof_field();
    set_has_oneof_string();
    oneof_field_.oneof_string_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  }
  oneof_field_.oneof_string_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(value));
  // @@protoc_insertion_point(field_set_char:proto2_nofieldpresence_unittest.TestAllTypes.oneof_string)
}
inline void TestAllTypes::set_oneof_string(const char* value, size_t size) {
  if (!has_oneof_string()) {
    clear_oneof_field();
    set_has_oneof_string();
    oneof_field_.oneof_string_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  }
  oneof_field_.oneof_string_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(
      reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:proto2_nofieldpresence_unittest.TestAllTypes.oneof_string)
}
inline ::std::string* TestAllTypes::mutable_oneof_string() {
  if (!has_oneof_string()) {
    clear_oneof_field();
    set_has_oneof_string();
    oneof_field_.oneof_string_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  }
  // @@protoc_insertion_point(field_mutable:proto2_nofieldpresence_unittest.TestAllTypes.oneof_string)
  return oneof_field_.oneof_string_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline ::std::string* TestAllTypes::release_oneof_string() {
  // @@protoc_insertion_point(field_release:proto2_nofieldpresence_unittest.TestAllTypes.oneof_string)
  if (has_oneof_string()) {
    clear_has_oneof_field();
    return oneof_field_.oneof_string_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  } else {
    return NULL;
  }
}
inline void TestAllTypes::set_allocated_oneof_string(::std::string* oneof_string) {
  if (!has_oneof_string()) {
    oneof_field_.oneof_string_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  }
  clear_oneof_field();
  if (oneof_string != NULL) {
    set_has_oneof_string();
    oneof_field_.oneof_string_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
        oneof_string);
  }
  // @@protoc_insertion_point(field_set_allocated:proto2_nofieldpresence_unittest.TestAllTypes.oneof_string)
}

// optional .proto2_nofieldpresence_unittest.TestAllTypes.NestedEnum oneof_enum = 114;
inline bool TestAllTypes::has_oneof_enum() const {
  return oneof_field_case() == kOneofEnum;
}
inline void TestAllTypes::set_has_oneof_enum() {
  _oneof_case_[0] = kOneofEnum;
}
inline void TestAllTypes::clear_oneof_enum() {
  if (has_oneof_enum()) {
    oneof_field_.oneof_enum_ = 0;
    clear_has_oneof_field();
  }
}
inline ::proto2_nofieldpresence_unittest::TestAllTypes_NestedEnum TestAllTypes::oneof_enum() const {
  // @@protoc_insertion_point(field_get:proto2_nofieldpresence_unittest.TestAllTypes.oneof_enum)
  if (has_oneof_enum()) {
    return static_cast< ::proto2_nofieldpresence_unittest::TestAllTypes_NestedEnum >(oneof_field_.oneof_enum_);
  }
  return static_cast< ::proto2_nofieldpresence_unittest::TestAllTypes_NestedEnum >(0);
}
inline void TestAllTypes::set_oneof_enum(::proto2_nofieldpresence_unittest::TestAllTypes_NestedEnum value) {
  if (!has_oneof_enum()) {
    clear_oneof_field();
    set_has_oneof_enum();
  }
  oneof_field_.oneof_enum_ = value;
  // @@protoc_insertion_point(field_set:proto2_nofieldpresence_unittest.TestAllTypes.oneof_enum)
}

inline bool TestAllTypes::has_oneof_field() const {
  return oneof_field_case() != ONEOF_FIELD_NOT_SET;
}
inline void TestAllTypes::clear_has_oneof_field() {
  _oneof_case_[0] = ONEOF_FIELD_NOT_SET;
}
inline TestAllTypes::OneofFieldCase TestAllTypes::oneof_field_case() const {
  return TestAllTypes::OneofFieldCase(_oneof_case_[0]);
}
inline const TestAllTypes* TestAllTypes::internal_default_instance() {
  return &TestAllTypes_default_instance_.get();
}
// -------------------------------------------------------------------

// TestProto2Required

// optional .protobuf_unittest.TestRequired proto2 = 1;
inline bool TestProto2Required::has_proto2() const {
  return this != internal_default_instance() && proto2_ != NULL;
}
inline void TestProto2Required::clear_proto2() {
  if (GetArenaNoVirtual() == NULL && proto2_ != NULL) delete proto2_;
  proto2_ = NULL;
}
inline const ::protobuf_unittest::TestRequired& TestProto2Required::proto2() const {
  // @@protoc_insertion_point(field_get:proto2_nofieldpresence_unittest.TestProto2Required.proto2)
  return proto2_ != NULL ? *proto2_
                         : *::protobuf_unittest::TestRequired::internal_default_instance();
}
inline ::protobuf_unittest::TestRequired* TestProto2Required::mutable_proto2() {
  
  if (proto2_ == NULL) {
    proto2_ = new ::protobuf_unittest::TestRequired;
  }
  // @@protoc_insertion_point(field_mutable:proto2_nofieldpresence_unittest.TestProto2Required.proto2)
  return proto2_;
}
inline ::protobuf_unittest::TestRequired* TestProto2Required::release_proto2() {
  // @@protoc_insertion_point(field_release:proto2_nofieldpresence_unittest.TestProto2Required.proto2)
  
  ::protobuf_unittest::TestRequired* temp = proto2_;
  proto2_ = NULL;
  return temp;
}
inline void TestProto2Required::set_allocated_proto2(::protobuf_unittest::TestRequired* proto2) {
  delete proto2_;
  if (proto2 != NULL && proto2->GetArena() != NULL) {
    ::protobuf_unittest::TestRequired* new_proto2 = new ::protobuf_unittest::TestRequired;
    new_proto2->CopyFrom(*proto2);
    proto2 = new_proto2;
  }
  proto2_ = proto2;
  if (proto2) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_set_allocated:proto2_nofieldpresence_unittest.TestProto2Required.proto2)
}

inline const TestProto2Required* TestProto2Required::internal_default_instance() {
  return &TestProto2Required_default_instance_.get();
}
// -------------------------------------------------------------------

// ForeignMessage

// optional int32 c = 1;
inline void ForeignMessage::clear_c() {
  c_ = 0;
}
inline ::google::protobuf::int32 ForeignMessage::c() const {
  // @@protoc_insertion_point(field_get:proto2_nofieldpresence_unittest.ForeignMessage.c)
  return c_;
}
inline void ForeignMessage::set_c(::google::protobuf::int32 value) {
  
  c_ = value;
  // @@protoc_insertion_point(field_set:proto2_nofieldpresence_unittest.ForeignMessage.c)
}

inline const ForeignMessage* ForeignMessage::internal_default_instance() {
  return &ForeignMessage_default_instance_.get();
}
#endif  // !PROTOBUF_INLINE_NOT_IN_HEADERS
// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------


// @@protoc_insertion_point(namespace_scope)

}  // namespace proto2_nofieldpresence_unittest

#ifndef SWIG
namespace google {
namespace protobuf {

template <> struct is_proto_enum< ::proto2_nofieldpresence_unittest::TestAllTypes_NestedEnum> : ::google::protobuf::internal::true_type {};
template <>
inline const EnumDescriptor* GetEnumDescriptor< ::proto2_nofieldpresence_unittest::TestAllTypes_NestedEnum>() {
  return ::proto2_nofieldpresence_unittest::TestAllTypes_NestedEnum_descriptor();
}
template <> struct is_proto_enum< ::proto2_nofieldpresence_unittest::ForeignEnum> : ::google::protobuf::internal::true_type {};
template <>
inline const EnumDescriptor* GetEnumDescriptor< ::proto2_nofieldpresence_unittest::ForeignEnum>() {
  return ::proto2_nofieldpresence_unittest::ForeignEnum_descriptor();
}

}  // namespace protobuf
}  // namespace google
#endif  // SWIG

// @@protoc_insertion_point(global_scope)

#endif  // PROTOBUF_google_2fprotobuf_2funittest_5fno_5ffield_5fpresence_2eproto__INCLUDED

// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: google/protobuf/any_test.proto

#ifndef PROTOBUF_google_2fprotobuf_2fany_5ftest_2eproto__INCLUDED
#define PROTOBUF_google_2fprotobuf_2fany_5ftest_2eproto__INCLUDED

#include <string>

#include <google/protobuf/stubs/common.h>

#if GOOGLE_PROTOBUF_VERSION < 3001000
#error This file was generated by a newer version of protoc which is
#error incompatible with your Protocol Buffer headers.  Please update
#error your headers.
#endif
#if 3001000 < GOOGLE_PROTOBUF_MIN_PROTOC_VERSION
#error This file was generated by an older version of protoc which is
#error incompatible with your Protocol Buffer headers.  Please
#error regenerate this file with a newer version of protoc.
#endif

#include <google/protobuf/arena.h>
#include <google/protobuf/arenastring.h>
#include <google/protobuf/generated_message_util.h>
#include <google/protobuf/metadata.h>
#include <google/protobuf/message.h>
#include <google/protobuf/repeated_field.h>
#include <google/protobuf/extension_set.h>
#include <google/protobuf/unknown_field_set.h>
#include <google/protobuf/any.pb.h>
// @@protoc_insertion_point(includes)

namespace protobuf_unittest {

// Internal implementation detail -- do not call these.
void protobuf_AddDesc_google_2fprotobuf_2fany_5ftest_2eproto();
void protobuf_InitDefaults_google_2fprotobuf_2fany_5ftest_2eproto();
void protobuf_AssignDesc_google_2fprotobuf_2fany_5ftest_2eproto();
void protobuf_ShutdownFile_google_2fprotobuf_2fany_5ftest_2eproto();

class TestAny;

// ===================================================================

class TestAny : public ::google::protobuf::Message /* @@protoc_insertion_point(class_definition:protobuf_unittest.TestAny) */ {
 public:
  TestAny();
  virtual ~TestAny();

  TestAny(const TestAny& from);

  inline TestAny& operator=(const TestAny& from) {
    CopyFrom(from);
    return *this;
  }

  static const ::google::protobuf::Descriptor* descriptor();
  static const TestAny& default_instance();

  static const TestAny* internal_default_instance();

  void Swap(TestAny* other);

  // implements Message ----------------------------------------------

  inline TestAny* New() const { return New(NULL); }

  TestAny* New(::google::protobuf::Arena* arena) const;
  void CopyFrom(const ::google::protobuf::Message& from);
  void MergeFrom(const ::google::protobuf::Message& from);
  void CopyFrom(const TestAny& from);
  void MergeFrom(const TestAny& from);
  void Clear();
  bool IsInitialized() const;

  size_t ByteSizeLong() const;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input);
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const;
  ::google::protobuf::uint8* InternalSerializeWithCachedSizesToArray(
      bool deterministic, ::google::protobuf::uint8* output) const;
  ::google::protobuf::uint8* SerializeWithCachedSizesToArray(::google::protobuf::uint8* output) const {
    return InternalSerializeWithCachedSizesToArray(false, output);
  }
  int GetCachedSize() const { return _cached_size_; }
  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const;
  void InternalSwap(TestAny* other);
  void UnsafeMergeFrom(const TestAny& from);
  private:
  inline ::google::protobuf::Arena* GetArenaNoVirtual() const {
    return _internal_metadata_.arena();
  }
  inline void* MaybeArenaPtr() const {
    return _internal_metadata_.raw_arena_ptr();
  }
  public:

  ::google::protobuf::Metadata GetMetadata() const;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // optional int32 int32_value = 1;
  void clear_int32_value();
  static const int kInt32ValueFieldNumber = 1;
  ::google::protobuf::int32 int32_value() const;
  void set_int32_value(::google::protobuf::int32 value);

  // optional .google.protobuf.Any any_value = 2;
  bool has_any_value() const;
  void clear_any_value();
  static const int kAnyValueFieldNumber = 2;
  const ::google::protobuf::Any& any_value() const;
  ::google::protobuf::Any* mutable_any_value();
  ::google::protobuf::Any* release_any_value();
  void set_allocated_any_value(::google::protobuf::Any* any_value);

  // repeated .google.protobuf.Any repeated_any_value = 3;
  int repeated_any_value_size() const;
  void clear_repeated_any_value();
  static const int kRepeatedAnyValueFieldNumber = 3;
  const ::google::protobuf::Any& repeated_any_value(int index) const;
  ::google::protobuf::Any* mutable_repeated_any_value(int index);
  ::google::protobuf::Any* add_repeated_any_value();
  ::google::protobuf::RepeatedPtrField< ::google::protobuf::Any >*
      mutable_repeated_any_value();
  const ::google::protobuf::RepeatedPtrField< ::google::protobuf::Any >&
      repeated_any_value() const;

  // @@protoc_insertion_point(class_scope:protobuf_unittest.TestAny)
 private:

  ::google::protobuf::internal::InternalMetadataWithArena _internal_metadata_;
  ::google::protobuf::RepeatedPtrField< ::google::protobuf::Any > repeated_any_value_;
  ::google::protobuf::Any* any_value_;
  ::google::protobuf::int32 int32_value_;
  mutable int _cached_size_;
  friend void  protobuf_InitDefaults_google_2fprotobuf_2fany_5ftest_2eproto_impl();
  friend void  protobuf_AddDesc_google_2fprotobuf_2fany_5ftest_2eproto_impl();
  friend void protobuf_AssignDesc_google_2fprotobuf_2fany_5ftest_2eproto();
  friend void protobuf_ShutdownFile_google_2fprotobuf_2fany_5ftest_2eproto();

  void InitAsDefaultInstance();
};
extern ::google::protobuf::internal::ExplicitlyConstructed<TestAny> TestAny_default_instance_;

// ===================================================================


// ===================================================================

#if !PROTOBUF_INLINE_NOT_IN_HEADERS
// TestAny

// optional int32 int32_value = 1;
inline void TestAny::clear_int32_value() {
  int32_value_ = 0;
}
inline ::google::protobuf::int32 TestAny::int32_value() const {
  // @@protoc_insertion_point(field_get:protobuf_unittest.TestAny.int32_value)
  return int32_value_;
}
inline void TestAny::set_int32_value(::google::protobuf::int32 value) {
  
  int32_value_ = value;
  // @@protoc_insertion_point(field_set:protobuf_unittest.TestAny.int32_value)
}

// optional .google.protobuf.Any any_value = 2;
inline bool TestAny::has_any_value() const {
  return this != internal_default_instance() && any_value_ != NULL;
}
inline void TestAny::clear_any_value() {
  if (GetArenaNoVirtual() == NULL && any_value_ != NULL) delete any_value_;
  any_value_ = NULL;
}
inline const ::google::protobuf::Any& TestAny::any_value() const {
  // @@protoc_insertion_point(field_get:protobuf_unittest.TestAny.any_value)
  return any_value_ != NULL ? *any_value_
                         : *::google::protobuf::Any::internal_default_instance();
}
inline ::google::protobuf::Any* TestAny::mutable_any_value() {
  
  if (any_value_ == NULL) {
    any_value_ = new ::google::protobuf::Any;
  }
  // @@protoc_insertion_point(field_mutable:protobuf_unittest.TestAny.any_value)
  return any_value_;
}
inline ::google::protobuf::Any* TestAny::release_any_value() {
  // @@protoc_insertion_point(field_release:protobuf_unittest.TestAny.any_value)
  
  ::google::protobuf::Any* temp = any_value_;
  any_value_ = NULL;
  return temp;
}
inline void TestAny::set_allocated_any_value(::google::protobuf::Any* any_value) {
  delete any_value_;
  any_value_ = any_value;
  if (any_value) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_set_allocated:protobuf_unittest.TestAny.any_value)
}

// repeated .google.protobuf.Any repeated_any_value = 3;
inline int TestAny::repeated_any_value_size() const {
  return repeated_any_value_.size();
}
inline void TestAny::clear_repeated_any_value() {
  repeated_any_value_.Clear();
}
inline const ::google::protobuf::Any& TestAny::repeated_any_value(int index) const {
  // @@protoc_insertion_point(field_get:protobuf_unittest.TestAny.repeated_any_value)
  return repeated_any_value_.Get(index);
}
inline ::google::protobuf::Any* TestAny::mutable_repeated_any_value(int index) {
  // @@protoc_insertion_point(field_mutable:protobuf_unittest.TestAny.repeated_any_value)
  return repeated_any_value_.Mutable(index);
}
inline ::google::protobuf::Any* TestAny::add_repeated_any_value() {
  // @@protoc_insertion_point(field_add:protobuf_unittest.TestAny.repeated_any_value)
  return repeated_any_value_.Add();
}
inline ::google::protobuf::RepeatedPtrField< ::google::protobuf::Any >*
TestAny::mutable_repeated_any_value() {
  // @@protoc_insertion_point(field_mutable_list:protobuf_unittest.TestAny.repeated_any_value)
  return &repeated_any_value_;
}
inline const ::google::protobuf::RepeatedPtrField< ::google::protobuf::Any >&
TestAny::repeated_any_value() const {
  // @@protoc_insertion_point(field_list:protobuf_unittest.TestAny.repeated_any_value)
  return repeated_any_value_;
}

inline const TestAny* TestAny::internal_default_instance() {
  return &TestAny_default_instance_.get();
}
#endif  // !PROTOBUF_INLINE_NOT_IN_HEADERS

// @@protoc_insertion_point(namespace_scope)

}  // namespace protobuf_unittest

// @@protoc_insertion_point(global_scope)

#endif  // PROTOBUF_google_2fprotobuf_2fany_5ftest_2eproto__INCLUDED

// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: google/protobuf/any_test.proto

#define INTERNAL_SUPPRESS_PROTOBUF_FIELD_DEPRECATION
#include <google/protobuf/any_test.pb.h>

#include <algorithm>

#include <google/protobuf/stubs/common.h>
#include <google/protobuf/stubs/port.h>
#include <google/protobuf/stubs/once.h>
#include <google/protobuf/io/coded_stream.h>
#include <google/protobuf/wire_format_lite_inl.h>
#include <google/protobuf/descriptor.h>
#include <google/protobuf/generated_message_reflection.h>
#include <google/protobuf/reflection_ops.h>
#include <google/protobuf/wire_format.h>
// @@protoc_insertion_point(includes)

namespace protobuf_unittest {

namespace {

const ::google::protobuf::Descriptor* TestAny_descriptor_ = NULL;
const ::google::protobuf::internal::GeneratedMessageReflection*
  TestAny_reflection_ = NULL;

}  // namespace


void protobuf_AssignDesc_google_2fprotobuf_2fany_5ftest_2eproto() GOOGLE_ATTRIBUTE_COLD;
void protobuf_AssignDesc_google_2fprotobuf_2fany_5ftest_2eproto() {
  protobuf_AddDesc_google_2fprotobuf_2fany_5ftest_2eproto();
  const ::google::protobuf::FileDescriptor* file =
    ::google::protobuf::DescriptorPool::generated_pool()->FindFileByName(
      "google/protobuf/any_test.proto");
  GOOGLE_CHECK(file != NULL);
  TestAny_descriptor_ = file->message_type(0);
  static const int TestAny_offsets_[3] = {
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(TestAny, int32_value_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(TestAny, any_value_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(TestAny, repeated_any_value_),
  };
  TestAny_reflection_ =
    ::google::protobuf::internal::GeneratedMessageReflection::NewGeneratedMessageReflection(
      TestAny_descriptor_,
      TestAny::internal_default_instance(),
      TestAny_offsets_,
      -1,
      -1,
      -1,
      sizeof(TestAny),
      GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(TestAny, _internal_metadata_));
}

namespace {

GOOGLE_PROTOBUF_DECLARE_ONCE(protobuf_AssignDescriptors_once_);
void protobuf_AssignDescriptorsOnce() {
  ::google::protobuf::GoogleOnceInit(&protobuf_AssignDescriptors_once_,
                 &protobuf_AssignDesc_google_2fprotobuf_2fany_5ftest_2eproto);
}

void protobuf_RegisterTypes(const ::std::string&) GOOGLE_ATTRIBUTE_COLD;
void protobuf_RegisterTypes(const ::std::string&) {
  protobuf_AssignDescriptorsOnce();
  ::google::protobuf::MessageFactory::InternalRegisterGeneratedMessage(
      TestAny_descriptor_, TestAny::internal_default_instance());
}

}  // namespace

void protobuf_ShutdownFile_google_2fprotobuf_2fany_5ftest_2eproto() {
  TestAny_default_instance_.Shutdown();
  delete TestAny_reflection_;
}

void protobuf_InitDefaults_google_2fprotobuf_2fany_5ftest_2eproto_impl() {
  GOOGLE_PROTOBUF_VERIFY_VERSION;

  ::google::protobuf::protobuf_InitDefaults_google_2fprotobuf_2fany_2eproto();
  TestAny_default_instance_.DefaultConstruct();
  TestAny_default_instance_.get_mutable()->InitAsDefaultInstance();
}

GOOGLE_PROTOBUF_DECLARE_ONCE(protobuf_InitDefaults_google_2fprotobuf_2fany_5ftest_2eproto_once_);
void protobuf_InitDefaults_google_2fprotobuf_2fany_5ftest_2eproto() {
  ::google::protobuf::GoogleOnceInit(&protobuf_InitDefaults_google_2fprotobuf_2fany_5ftest_2eproto_once_,
                 &protobuf_InitDefaults_google_2fprotobuf_2fany_5ftest_2eproto_impl);
}
void protobuf_AddDesc_google_2fprotobuf_2fany_5ftest_2eproto_impl() {
  GOOGLE_PROTOBUF_VERIFY_VERSION;

  protobuf_InitDefaults_google_2fprotobuf_2fany_5ftest_2eproto();
  ::google::protobuf::DescriptorPool::InternalAddGeneratedFile(
    "\n\036google/protobuf/any_test.proto\022\021protob"
    "uf_unittest\032\031google/protobuf/any.proto\"y"
    "\n\007TestAny\022\023\n\013int32_value\030\001 \001(\005\022\'\n\tany_va"
    "lue\030\002 \001(\0132\024.google.protobuf.Any\0220\n\022repea"
    "ted_any_value\030\003 \003(\0132\024.google.protobuf.An"
    "yb\006proto3", 209);
  ::google::protobuf::MessageFactory::InternalRegisterGeneratedFile(
    "google/protobuf/any_test.proto", &protobuf_RegisterTypes);
  ::google::protobuf::protobuf_AddDesc_google_2fprotobuf_2fany_2eproto();
  ::google::protobuf::internal::OnShutdown(&protobuf_ShutdownFile_google_2fprotobuf_2fany_5ftest_2eproto);
}

GOOGLE_PROTOBUF_DECLARE_ONCE(protobuf_AddDesc_google_2fprotobuf_2fany_5ftest_2eproto_once_);
void protobuf_AddDesc_google_2fprotobuf_2fany_5ftest_2eproto() {
  ::google::protobuf::GoogleOnceInit(&protobuf_AddDesc_google_2fprotobuf_2fany_5ftest_2eproto_once_,
                 &protobuf_AddDesc_google_2fprotobuf_2fany_5ftest_2eproto_impl);
}
// Force AddDescriptors() to be called at static initialization time.
struct StaticDescriptorInitializer_google_2fprotobuf_2fany_5ftest_2eproto {
  StaticDescriptorInitializer_google_2fprotobuf_2fany_5ftest_2eproto() {
    protobuf_AddDesc_google_2fprotobuf_2fany_5ftest_2eproto();
  }
} static_descriptor_initializer_google_2fprotobuf_2fany_5ftest_2eproto_;

namespace {

static void MergeFromFail(int line) GOOGLE_ATTRIBUTE_COLD GOOGLE_ATTRIBUTE_NORETURN;
static void MergeFromFail(int line) {
  ::google::protobuf::internal::MergeFromFail(__FILE__, line);
}

}  // namespace


// ===================================================================

#if !defined(_MSC_VER) || _MSC_VER >= 1900
const int TestAny::kInt32ValueFieldNumber;
const int TestAny::kAnyValueFieldNumber;
const int TestAny::kRepeatedAnyValueFieldNumber;
#endif  // !defined(_MSC_VER) || _MSC_VER >= 1900

TestAny::TestAny()
  : ::google::protobuf::Message(), _internal_metadata_(NULL) {
  if (this != internal_default_instance()) protobuf_InitDefaults_google_2fprotobuf_2fany_5ftest_2eproto();
  SharedCtor();
  // @@protoc_insertion_point(constructor:protobuf_unittest.TestAny)
}

void TestAny::InitAsDefaultInstance() {
  any_value_ = const_cast< ::google::protobuf::Any*>(
      ::google::protobuf::Any::internal_default_instance());
}

TestAny::TestAny(const TestAny& from)
  : ::google::protobuf::Message(),
    _internal_metadata_(NULL) {
  SharedCtor();
  UnsafeMergeFrom(from);
  // @@protoc_insertion_point(copy_constructor:protobuf_unittest.TestAny)
}

void TestAny::SharedCtor() {
  any_value_ = NULL;
  int32_value_ = 0;
  _cached_size_ = 0;
}

TestAny::~TestAny() {
  // @@protoc_insertion_point(destructor:protobuf_unittest.TestAny)
  SharedDtor();
}

void TestAny::SharedDtor() {
  if (this != &TestAny_default_instance_.get()) {
    delete any_value_;
  }
}

void TestAny::SetCachedSize(int size) const {
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
}
const ::google::protobuf::Descriptor* TestAny::descriptor() {
  protobuf_AssignDescriptorsOnce();
  return TestAny_descriptor_;
}

const TestAny& TestAny::default_instance() {
  protobuf_InitDefaults_google_2fprotobuf_2fany_5ftest_2eproto();
  return *internal_default_instance();
}

::google::protobuf::internal::ExplicitlyConstructed<TestAny> TestAny_default_instance_;

TestAny* TestAny::New(::google::protobuf::Arena* arena) const {
  TestAny* n = new TestAny;
  if (arena != NULL) {
    arena->Own(n);
  }
  return n;
}

void TestAny::Clear() {
// @@protoc_insertion_point(message_clear_start:protobuf_unittest.TestAny)
  int32_value_ = 0;
  if (GetArenaNoVirtual() == NULL && any_value_ != NULL) delete any_value_;
  any_value_ = NULL;
  repeated_any_value_.Clear();
}

bool TestAny::MergePartialFromCodedStream(
    ::google::protobuf::io::CodedInputStream* input) {
#define DO_(EXPRESSION) if (!GOOGLE_PREDICT_TRUE(EXPRESSION)) goto failure
  ::google::protobuf::uint32 tag;
  // @@protoc_insertion_point(parse_start:protobuf_unittest.TestAny)
  for (;;) {
    ::std::pair< ::google::protobuf::uint32, bool> p = input->ReadTagWithCutoff(127);
    tag = p.first;
    if (!p.second) goto handle_unusual;
    switch (::google::protobuf::internal::WireFormatLite::GetTagFieldNumber(tag)) {
      // optional int32 int32_value = 1;
      case 1: {
        if (tag == 8) {

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int32, ::google::protobuf::internal::WireFormatLite::TYPE_INT32>(
                 input, &int32_value_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(18)) goto parse_any_value;
        break;
      }

      // optional .google.protobuf.Any any_value = 2;
      case 2: {
        if (tag == 18) {
         parse_any_value:
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessageNoVirtual(
               input, mutable_any_value()));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(26)) goto parse_repeated_any_value;
        break;
      }

      // repeated .google.protobuf.Any repeated_any_value = 3;
      case 3: {
        if (tag == 26) {
         parse_repeated_any_value:
          DO_(input->IncrementRecursionDepth());
         parse_loop_repeated_any_value:
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessageNoVirtualNoRecursionDepth(
                input, add_repeated_any_value()));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(26)) goto parse_loop_repeated_any_value;
        input->UnsafeDecrementRecursionDepth();
        if (input->ExpectAtEnd()) goto success;
        break;
      }

      default: {
      handle_unusual:
        if (tag == 0 ||
            ::google::protobuf::internal::WireFormatLite::GetTagWireType(tag) ==
            ::google::protobuf::internal::WireFormatLite::WIRETYPE_END_GROUP) {
          goto success;
        }
        DO_(::google::protobuf::internal::WireFormatLite::SkipField(input, tag));
        break;
      }
    }
  }
success:
  // @@protoc_insertion_point(parse_success:protobuf_unittest.TestAny)
  return true;
failure:
  // @@protoc_insertion_point(parse_failure:protobuf_unittest.TestAny)
  return false;
#undef DO_
}

void TestAny::SerializeWithCachedSizes(
    ::google::protobuf::io::CodedOutputStream* output) const {
  // @@protoc_insertion_point(serialize_start:protobuf_unittest.TestAny)
  // optional int32 int32_value = 1;
  if (this->int32_value() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt32(1, this->int32_value(), output);
  }

  // optional .google.protobuf.Any any_value = 2;
  if (this->has_any_value()) {
    ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
      2, *this->any_value_, output);
  }

  // repeated .google.protobuf.Any repeated_any_value = 3;
  for (unsigned int i = 0, n = this->repeated_any_value_size(); i < n; i++) {
    ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
      3, this->repeated_any_value(i), output);
  }

  // @@protoc_insertion_point(serialize_end:protobuf_unittest.TestAny)
}

::google::protobuf::uint8* TestAny::InternalSerializeWithCachedSizesToArray(
    bool deterministic, ::google::protobuf::uint8* target) const {
  (void)deterministic; // Unused
  // @@protoc_insertion_point(serialize_to_array_start:protobuf_unittest.TestAny)
  // optional int32 int32_value = 1;
  if (this->int32_value() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt32ToArray(1, this->int32_value(), target);
  }

  // optional .google.protobuf.Any any_value = 2;
  if (this->has_any_value()) {
    target = ::google::protobuf::internal::WireFormatLite::
      InternalWriteMessageNoVirtualToArray(
        2, *this->any_value_, false, target);
  }

  // repeated .google.protobuf.Any repeated_any_value = 3;
  for (unsigned int i = 0, n = this->repeated_any_value_size(); i < n; i++) {
    target = ::google::protobuf::internal::WireFormatLite::
      InternalWriteMessageNoVirtualToArray(
        3, this->repeated_any_value(i), false, target);
  }

  // @@protoc_insertion_point(serialize_to_array_end:protobuf_unittest.TestAny)
  return target;
}

size_t TestAny::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:protobuf_unittest.TestAny)
  size_t total_size = 0;

  // optional int32 int32_value = 1;
  if (this->int32_value() != 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::Int32Size(
        this->int32_value());
  }

  // optional .google.protobuf.Any any_value = 2;
  if (this->has_any_value()) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::MessageSizeNoVirtual(
        *this->any_value_);
  }

  // repeated .google.protobuf.Any repeated_any_value = 3;
  {
    unsigned int count = this->repeated_any_value_size();
    total_size += 1UL * count;
    for (unsigned int i = 0; i < count; i++) {
      total_size +=
        ::google::protobuf::internal::WireFormatLite::MessageSizeNoVirtual(
          this->repeated_any_value(i));
    }
  }

  int cached_size = ::google::protobuf::internal::ToCachedSize(total_size);
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = cached_size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
  return total_size;
}

void TestAny::MergeFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_merge_from_start:protobuf_unittest.TestAny)
  if (GOOGLE_PREDICT_FALSE(&from == this)) MergeFromFail(__LINE__);
  const TestAny* source =
      ::google::protobuf::internal::DynamicCastToGenerated<const TestAny>(
          &from);
  if (source == NULL) {
  // @@protoc_insertion_point(generalized_merge_from_cast_fail:protobuf_unittest.TestAny)
    ::google::protobuf::internal::ReflectionOps::Merge(from, this);
  } else {
  // @@protoc_insertion_point(generalized_merge_from_cast_success:protobuf_unittest.TestAny)
    UnsafeMergeFrom(*source);
  }
}

void TestAny::MergeFrom(const TestAny& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:protobuf_unittest.TestAny)
  if (GOOGLE_PREDICT_TRUE(&from != this)) {
    UnsafeMergeFrom(from);
  } else {
    MergeFromFail(__LINE__);
  }
}

void TestAny::UnsafeMergeFrom(const TestAny& from) {
  GOOGLE_DCHECK(&from != this);
  repeated_any_value_.MergeFrom(from.repeated_any_value_);
  if (from.int32_value() != 0) {
    set_int32_value(from.int32_value());
  }
  if (from.has_any_value()) {
    mutable_any_value()->::google::protobuf::Any::MergeFrom(from.any_value());
  }
}

void TestAny::CopyFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_copy_from_start:protobuf_unittest.TestAny)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

void TestAny::CopyFrom(const TestAny& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:protobuf_unittest.TestAny)
  if (&from == this) return;
  Clear();
  UnsafeMergeFrom(from);
}

bool TestAny::IsInitialized() const {

  return true;
}

void TestAny::Swap(TestAny* other) {
  if (other == this) return;
  InternalSwap(other);
}
void TestAny::InternalSwap(TestAny* other) {
  std::swap(int32_value_, other->int32_value_);
  std::swap(any_value_, other->any_value_);
  repeated_any_value_.UnsafeArenaSwap(&other->repeated_any_value_);
  _internal_metadata_.Swap(&other->_internal_metadata_);
  std::swap(_cached_size_, other->_cached_size_);
}

::google::protobuf::Metadata TestAny::GetMetadata() const {
  protobuf_AssignDescriptorsOnce();
  ::google::protobuf::Metadata metadata;
  metadata.descriptor = TestAny_descriptor_;
  metadata.reflection = TestAny_reflection_;
  return metadata;
}

#if PROTOBUF_INLINE_NOT_IN_HEADERS
// TestAny

// optional int32 int32_value = 1;
void TestAny::clear_int32_value() {
  int32_value_ = 0;
}
::google::protobuf::int32 TestAny::int32_value() const {
  // @@protoc_insertion_point(field_get:protobuf_unittest.TestAny.int32_value)
  return int32_value_;
}
void TestAny::set_int32_value(::google::protobuf::int32 value) {
  
  int32_value_ = value;
  // @@protoc_insertion_point(field_set:protobuf_unittest.TestAny.int32_value)
}

// optional .google.protobuf.Any any_value = 2;
bool TestAny::has_any_value() const {
  return this != internal_default_instance() && any_value_ != NULL;
}
void TestAny::clear_any_value() {
  if (GetArenaNoVirtual() == NULL && any_value_ != NULL) delete any_value_;
  any_value_ = NULL;
}
const ::google::protobuf::Any& TestAny::any_value() const {
  // @@protoc_insertion_point(field_get:protobuf_unittest.TestAny.any_value)
  return any_value_ != NULL ? *any_value_
                         : *::google::protobuf::Any::internal_default_instance();
}
::google::protobuf::Any* TestAny::mutable_any_value() {
  
  if (any_value_ == NULL) {
    any_value_ = new ::google::protobuf::Any;
  }
  // @@protoc_insertion_point(field_mutable:protobuf_unittest.TestAny.any_value)
  return any_value_;
}
::google::protobuf::Any* TestAny::release_any_value() {
  // @@protoc_insertion_point(field_release:protobuf_unittest.TestAny.any_value)
  
  ::google::protobuf::Any* temp = any_value_;
  any_value_ = NULL;
  return temp;
}
void TestAny::set_allocated_any_value(::google::protobuf::Any* any_value) {
  delete any_value_;
  any_value_ = any_value;
  if (any_value) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_set_allocated:protobuf_unittest.TestAny.any_value)
}

// repeated .google.protobuf.Any repeated_any_value = 3;
int TestAny::repeated_any_value_size() const {
  return repeated_any_value_.size();
}
void TestAny::clear_repeated_any_value() {
  repeated_any_value_.Clear();
}
const ::google::protobuf::Any& TestAny::repeated_any_value(int index) const {
  // @@protoc_insertion_point(field_get:protobuf_unittest.TestAny.repeated_any_value)
  return repeated_any_value_.Get(index);
}
::google::protobuf::Any* TestAny::mutable_repeated_any_value(int index) {
  // @@protoc_insertion_point(field_mutable:protobuf_unittest.TestAny.repeated_any_value)
  return repeated_any_value_.Mutable(index);
}
::google::protobuf::Any* TestAny::add_repeated_any_value() {
  // @@protoc_insertion_point(field_add:protobuf_unittest.TestAny.repeated_any_value)
  return repeated_any_value_.Add();
}
::google::protobuf::RepeatedPtrField< ::google::protobuf::Any >*
TestAny::mutable_repeated_any_value() {
  // @@protoc_insertion_point(field_mutable_list:protobuf_unittest.TestAny.repeated_any_value)
  return &repeated_any_value_;
}
const ::google::protobuf::RepeatedPtrField< ::google::protobuf::Any >&
TestAny::repeated_any_value() const {
  // @@protoc_insertion_point(field_list:protobuf_unittest.TestAny.repeated_any_value)
  return repeated_any_value_;
}

inline const TestAny* TestAny::internal_default_instance() {
  return &TestAny_default_instance_.get();
}
#endif  // PROTOBUF_INLINE_NOT_IN_HEADERS

// @@protoc_insertion_point(namespace_scope)

}  // namespace protobuf_unittest

// @@protoc_insertion_point(global_scope)

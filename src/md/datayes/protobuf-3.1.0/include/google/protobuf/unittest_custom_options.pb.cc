// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: google/protobuf/unittest_custom_options.proto

#define INTERNAL_SUPPRESS_PROTOBUF_FIELD_DEPRECATION
#include <google/protobuf/unittest_custom_options.pb.h>

#include <algorithm>

#include <google/protobuf/stubs/common.h>
#include <google/protobuf/stubs/port.h>
#include <google/protobuf/stubs/once.h>
#include <google/protobuf/io/coded_stream.h>
#include <google/protobuf/wire_format_lite_inl.h>
#include <google/protobuf/descriptor.h>
#include <google/protobuf/generated_message_reflection.h>
#include <google/protobuf/reflection_ops.h>
#include <google/protobuf/wire_format.h>
// @@protoc_insertion_point(includes)

namespace protobuf_unittest {

namespace {

const ::google::protobuf::Descriptor* TestMessageWithCustomOptions_descriptor_ = NULL;
const ::google::protobuf::internal::GeneratedMessageReflection*
  TestMessageWithCustomOptions_reflection_ = NULL;
struct TestMessageWithCustomOptionsOneofInstance {
  ::google::protobuf::int32 oneof_field_;
}* TestMessageWithCustomOptions_default_oneof_instance_ = NULL;
const ::google::protobuf::EnumDescriptor* TestMessageWithCustomOptions_AnEnum_descriptor_ = NULL;
const ::google::protobuf::Descriptor* CustomOptionFooRequest_descriptor_ = NULL;
const ::google::protobuf::internal::GeneratedMessageReflection*
  CustomOptionFooRequest_reflection_ = NULL;
const ::google::protobuf::Descriptor* CustomOptionFooResponse_descriptor_ = NULL;
const ::google::protobuf::internal::GeneratedMessageReflection*
  CustomOptionFooResponse_reflection_ = NULL;
const ::google::protobuf::Descriptor* CustomOptionFooClientMessage_descriptor_ = NULL;
const ::google::protobuf::internal::GeneratedMessageReflection*
  CustomOptionFooClientMessage_reflection_ = NULL;
const ::google::protobuf::Descriptor* CustomOptionFooServerMessage_descriptor_ = NULL;
const ::google::protobuf::internal::GeneratedMessageReflection*
  CustomOptionFooServerMessage_reflection_ = NULL;
const ::google::protobuf::Descriptor* DummyMessageContainingEnum_descriptor_ = NULL;
const ::google::protobuf::internal::GeneratedMessageReflection*
  DummyMessageContainingEnum_reflection_ = NULL;
const ::google::protobuf::EnumDescriptor* DummyMessageContainingEnum_TestEnumType_descriptor_ = NULL;
const ::google::protobuf::Descriptor* DummyMessageInvalidAsOptionType_descriptor_ = NULL;
const ::google::protobuf::internal::GeneratedMessageReflection*
  DummyMessageInvalidAsOptionType_reflection_ = NULL;
const ::google::protobuf::Descriptor* CustomOptionMinIntegerValues_descriptor_ = NULL;
const ::google::protobuf::internal::GeneratedMessageReflection*
  CustomOptionMinIntegerValues_reflection_ = NULL;
const ::google::protobuf::Descriptor* CustomOptionMaxIntegerValues_descriptor_ = NULL;
const ::google::protobuf::internal::GeneratedMessageReflection*
  CustomOptionMaxIntegerValues_reflection_ = NULL;
const ::google::protobuf::Descriptor* CustomOptionOtherValues_descriptor_ = NULL;
const ::google::protobuf::internal::GeneratedMessageReflection*
  CustomOptionOtherValues_reflection_ = NULL;
const ::google::protobuf::Descriptor* SettingRealsFromPositiveInts_descriptor_ = NULL;
const ::google::protobuf::internal::GeneratedMessageReflection*
  SettingRealsFromPositiveInts_reflection_ = NULL;
const ::google::protobuf::Descriptor* SettingRealsFromNegativeInts_descriptor_ = NULL;
const ::google::protobuf::internal::GeneratedMessageReflection*
  SettingRealsFromNegativeInts_reflection_ = NULL;
const ::google::protobuf::Descriptor* ComplexOptionType1_descriptor_ = NULL;
const ::google::protobuf::internal::GeneratedMessageReflection*
  ComplexOptionType1_reflection_ = NULL;
const ::google::protobuf::Descriptor* ComplexOptionType2_descriptor_ = NULL;
const ::google::protobuf::internal::GeneratedMessageReflection*
  ComplexOptionType2_reflection_ = NULL;
const ::google::protobuf::Descriptor* ComplexOptionType2_ComplexOptionType4_descriptor_ = NULL;
const ::google::protobuf::internal::GeneratedMessageReflection*
  ComplexOptionType2_ComplexOptionType4_reflection_ = NULL;
const ::google::protobuf::Descriptor* ComplexOptionType3_descriptor_ = NULL;
const ::google::protobuf::internal::GeneratedMessageReflection*
  ComplexOptionType3_reflection_ = NULL;
const ::google::protobuf::Descriptor* ComplexOptionType3_ComplexOptionType5_descriptor_ = NULL;
const ::google::protobuf::internal::GeneratedMessageReflection*
  ComplexOptionType3_ComplexOptionType5_reflection_ = NULL;
const ::google::protobuf::Descriptor* ComplexOpt6_descriptor_ = NULL;
const ::google::protobuf::internal::GeneratedMessageReflection*
  ComplexOpt6_reflection_ = NULL;
const ::google::protobuf::Descriptor* VariousComplexOptions_descriptor_ = NULL;
const ::google::protobuf::internal::GeneratedMessageReflection*
  VariousComplexOptions_reflection_ = NULL;
const ::google::protobuf::Descriptor* AggregateMessageSet_descriptor_ = NULL;
const ::google::protobuf::internal::GeneratedMessageReflection*
  AggregateMessageSet_reflection_ = NULL;
const ::google::protobuf::Descriptor* AggregateMessageSetElement_descriptor_ = NULL;
const ::google::protobuf::internal::GeneratedMessageReflection*
  AggregateMessageSetElement_reflection_ = NULL;
const ::google::protobuf::Descriptor* Aggregate_descriptor_ = NULL;
const ::google::protobuf::internal::GeneratedMessageReflection*
  Aggregate_reflection_ = NULL;
const ::google::protobuf::Descriptor* AggregateMessage_descriptor_ = NULL;
const ::google::protobuf::internal::GeneratedMessageReflection*
  AggregateMessage_reflection_ = NULL;
const ::google::protobuf::Descriptor* NestedOptionType_descriptor_ = NULL;
const ::google::protobuf::internal::GeneratedMessageReflection*
  NestedOptionType_reflection_ = NULL;
const ::google::protobuf::Descriptor* NestedOptionType_NestedMessage_descriptor_ = NULL;
const ::google::protobuf::internal::GeneratedMessageReflection*
  NestedOptionType_NestedMessage_reflection_ = NULL;
const ::google::protobuf::EnumDescriptor* NestedOptionType_NestedEnum_descriptor_ = NULL;
const ::google::protobuf::Descriptor* OldOptionType_descriptor_ = NULL;
const ::google::protobuf::internal::GeneratedMessageReflection*
  OldOptionType_reflection_ = NULL;
const ::google::protobuf::EnumDescriptor* OldOptionType_TestEnum_descriptor_ = NULL;
const ::google::protobuf::Descriptor* NewOptionType_descriptor_ = NULL;
const ::google::protobuf::internal::GeneratedMessageReflection*
  NewOptionType_reflection_ = NULL;
const ::google::protobuf::EnumDescriptor* NewOptionType_TestEnum_descriptor_ = NULL;
const ::google::protobuf::Descriptor* TestMessageWithRequiredEnumOption_descriptor_ = NULL;
const ::google::protobuf::internal::GeneratedMessageReflection*
  TestMessageWithRequiredEnumOption_reflection_ = NULL;
const ::google::protobuf::EnumDescriptor* MethodOpt1_descriptor_ = NULL;
const ::google::protobuf::EnumDescriptor* AggregateEnum_descriptor_ = NULL;
const ::google::protobuf::ServiceDescriptor* TestServiceWithCustomOptions_descriptor_ = NULL;
const ::google::protobuf::ServiceDescriptor* AggregateService_descriptor_ = NULL;

}  // namespace


void protobuf_AssignDesc_google_2fprotobuf_2funittest_5fcustom_5foptions_2eproto() GOOGLE_ATTRIBUTE_COLD;
void protobuf_AssignDesc_google_2fprotobuf_2funittest_5fcustom_5foptions_2eproto() {
  protobuf_AddDesc_google_2fprotobuf_2funittest_5fcustom_5foptions_2eproto();
  const ::google::protobuf::FileDescriptor* file =
    ::google::protobuf::DescriptorPool::generated_pool()->FindFileByName(
      "google/protobuf/unittest_custom_options.proto");
  GOOGLE_CHECK(file != NULL);
  TestMessageWithCustomOptions_descriptor_ = file->message_type(0);
  static const int TestMessageWithCustomOptions_offsets_[3] = {
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(TestMessageWithCustomOptions, field1_),
    PROTO2_GENERATED_DEFAULT_ONEOF_FIELD_OFFSET(TestMessageWithCustomOptions_default_oneof_instance_, oneof_field_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(TestMessageWithCustomOptions, AnOneof_),
  };
  TestMessageWithCustomOptions_reflection_ =
    ::google::protobuf::internal::GeneratedMessageReflection::NewGeneratedMessageReflection(
      TestMessageWithCustomOptions_descriptor_,
      TestMessageWithCustomOptions::internal_default_instance(),
      TestMessageWithCustomOptions_offsets_,
      GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(TestMessageWithCustomOptions, _has_bits_),
      -1,
      -1,
      TestMessageWithCustomOptions_default_oneof_instance_,
      GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(TestMessageWithCustomOptions, _oneof_case_[0]),
      sizeof(TestMessageWithCustomOptions),
      GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(TestMessageWithCustomOptions, _internal_metadata_));
  TestMessageWithCustomOptions_AnEnum_descriptor_ = TestMessageWithCustomOptions_descriptor_->enum_type(0);
  CustomOptionFooRequest_descriptor_ = file->message_type(1);
  static const int CustomOptionFooRequest_offsets_[1] = {
  };
  CustomOptionFooRequest_reflection_ =
    ::google::protobuf::internal::GeneratedMessageReflection::NewGeneratedMessageReflection(
      CustomOptionFooRequest_descriptor_,
      CustomOptionFooRequest::internal_default_instance(),
      CustomOptionFooRequest_offsets_,
      GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(CustomOptionFooRequest, _has_bits_),
      -1,
      -1,
      sizeof(CustomOptionFooRequest),
      GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(CustomOptionFooRequest, _internal_metadata_));
  CustomOptionFooResponse_descriptor_ = file->message_type(2);
  static const int CustomOptionFooResponse_offsets_[1] = {
  };
  CustomOptionFooResponse_reflection_ =
    ::google::protobuf::internal::GeneratedMessageReflection::NewGeneratedMessageReflection(
      CustomOptionFooResponse_descriptor_,
      CustomOptionFooResponse::internal_default_instance(),
      CustomOptionFooResponse_offsets_,
      GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(CustomOptionFooResponse, _has_bits_),
      -1,
      -1,
      sizeof(CustomOptionFooResponse),
      GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(CustomOptionFooResponse, _internal_metadata_));
  CustomOptionFooClientMessage_descriptor_ = file->message_type(3);
  static const int CustomOptionFooClientMessage_offsets_[1] = {
  };
  CustomOptionFooClientMessage_reflection_ =
    ::google::protobuf::internal::GeneratedMessageReflection::NewGeneratedMessageReflection(
      CustomOptionFooClientMessage_descriptor_,
      CustomOptionFooClientMessage::internal_default_instance(),
      CustomOptionFooClientMessage_offsets_,
      GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(CustomOptionFooClientMessage, _has_bits_),
      -1,
      -1,
      sizeof(CustomOptionFooClientMessage),
      GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(CustomOptionFooClientMessage, _internal_metadata_));
  CustomOptionFooServerMessage_descriptor_ = file->message_type(4);
  static const int CustomOptionFooServerMessage_offsets_[1] = {
  };
  CustomOptionFooServerMessage_reflection_ =
    ::google::protobuf::internal::GeneratedMessageReflection::NewGeneratedMessageReflection(
      CustomOptionFooServerMessage_descriptor_,
      CustomOptionFooServerMessage::internal_default_instance(),
      CustomOptionFooServerMessage_offsets_,
      GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(CustomOptionFooServerMessage, _has_bits_),
      -1,
      -1,
      sizeof(CustomOptionFooServerMessage),
      GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(CustomOptionFooServerMessage, _internal_metadata_));
  DummyMessageContainingEnum_descriptor_ = file->message_type(5);
  static const int DummyMessageContainingEnum_offsets_[1] = {
  };
  DummyMessageContainingEnum_reflection_ =
    ::google::protobuf::internal::GeneratedMessageReflection::NewGeneratedMessageReflection(
      DummyMessageContainingEnum_descriptor_,
      DummyMessageContainingEnum::internal_default_instance(),
      DummyMessageContainingEnum_offsets_,
      GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(DummyMessageContainingEnum, _has_bits_),
      -1,
      -1,
      sizeof(DummyMessageContainingEnum),
      GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(DummyMessageContainingEnum, _internal_metadata_));
  DummyMessageContainingEnum_TestEnumType_descriptor_ = DummyMessageContainingEnum_descriptor_->enum_type(0);
  DummyMessageInvalidAsOptionType_descriptor_ = file->message_type(6);
  static const int DummyMessageInvalidAsOptionType_offsets_[1] = {
  };
  DummyMessageInvalidAsOptionType_reflection_ =
    ::google::protobuf::internal::GeneratedMessageReflection::NewGeneratedMessageReflection(
      DummyMessageInvalidAsOptionType_descriptor_,
      DummyMessageInvalidAsOptionType::internal_default_instance(),
      DummyMessageInvalidAsOptionType_offsets_,
      GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(DummyMessageInvalidAsOptionType, _has_bits_),
      -1,
      -1,
      sizeof(DummyMessageInvalidAsOptionType),
      GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(DummyMessageInvalidAsOptionType, _internal_metadata_));
  CustomOptionMinIntegerValues_descriptor_ = file->message_type(7);
  static const int CustomOptionMinIntegerValues_offsets_[1] = {
  };
  CustomOptionMinIntegerValues_reflection_ =
    ::google::protobuf::internal::GeneratedMessageReflection::NewGeneratedMessageReflection(
      CustomOptionMinIntegerValues_descriptor_,
      CustomOptionMinIntegerValues::internal_default_instance(),
      CustomOptionMinIntegerValues_offsets_,
      GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(CustomOptionMinIntegerValues, _has_bits_),
      -1,
      -1,
      sizeof(CustomOptionMinIntegerValues),
      GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(CustomOptionMinIntegerValues, _internal_metadata_));
  CustomOptionMaxIntegerValues_descriptor_ = file->message_type(8);
  static const int CustomOptionMaxIntegerValues_offsets_[1] = {
  };
  CustomOptionMaxIntegerValues_reflection_ =
    ::google::protobuf::internal::GeneratedMessageReflection::NewGeneratedMessageReflection(
      CustomOptionMaxIntegerValues_descriptor_,
      CustomOptionMaxIntegerValues::internal_default_instance(),
      CustomOptionMaxIntegerValues_offsets_,
      GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(CustomOptionMaxIntegerValues, _has_bits_),
      -1,
      -1,
      sizeof(CustomOptionMaxIntegerValues),
      GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(CustomOptionMaxIntegerValues, _internal_metadata_));
  CustomOptionOtherValues_descriptor_ = file->message_type(9);
  static const int CustomOptionOtherValues_offsets_[1] = {
  };
  CustomOptionOtherValues_reflection_ =
    ::google::protobuf::internal::GeneratedMessageReflection::NewGeneratedMessageReflection(
      CustomOptionOtherValues_descriptor_,
      CustomOptionOtherValues::internal_default_instance(),
      CustomOptionOtherValues_offsets_,
      GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(CustomOptionOtherValues, _has_bits_),
      -1,
      -1,
      sizeof(CustomOptionOtherValues),
      GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(CustomOptionOtherValues, _internal_metadata_));
  SettingRealsFromPositiveInts_descriptor_ = file->message_type(10);
  static const int SettingRealsFromPositiveInts_offsets_[1] = {
  };
  SettingRealsFromPositiveInts_reflection_ =
    ::google::protobuf::internal::GeneratedMessageReflection::NewGeneratedMessageReflection(
      SettingRealsFromPositiveInts_descriptor_,
      SettingRealsFromPositiveInts::internal_default_instance(),
      SettingRealsFromPositiveInts_offsets_,
      GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(SettingRealsFromPositiveInts, _has_bits_),
      -1,
      -1,
      sizeof(SettingRealsFromPositiveInts),
      GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(SettingRealsFromPositiveInts, _internal_metadata_));
  SettingRealsFromNegativeInts_descriptor_ = file->message_type(11);
  static const int SettingRealsFromNegativeInts_offsets_[1] = {
  };
  SettingRealsFromNegativeInts_reflection_ =
    ::google::protobuf::internal::GeneratedMessageReflection::NewGeneratedMessageReflection(
      SettingRealsFromNegativeInts_descriptor_,
      SettingRealsFromNegativeInts::internal_default_instance(),
      SettingRealsFromNegativeInts_offsets_,
      GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(SettingRealsFromNegativeInts, _has_bits_),
      -1,
      -1,
      sizeof(SettingRealsFromNegativeInts),
      GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(SettingRealsFromNegativeInts, _internal_metadata_));
  ComplexOptionType1_descriptor_ = file->message_type(12);
  static const int ComplexOptionType1_offsets_[4] = {
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(ComplexOptionType1, foo_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(ComplexOptionType1, foo2_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(ComplexOptionType1, foo3_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(ComplexOptionType1, foo4_),
  };
  ComplexOptionType1_reflection_ =
    ::google::protobuf::internal::GeneratedMessageReflection::NewGeneratedMessageReflection(
      ComplexOptionType1_descriptor_,
      ComplexOptionType1::internal_default_instance(),
      ComplexOptionType1_offsets_,
      GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(ComplexOptionType1, _has_bits_),
      -1,
      GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(ComplexOptionType1, _extensions_),
      sizeof(ComplexOptionType1),
      GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(ComplexOptionType1, _internal_metadata_));
  ComplexOptionType2_descriptor_ = file->message_type(13);
  static const int ComplexOptionType2_offsets_[4] = {
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(ComplexOptionType2, bar_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(ComplexOptionType2, baz_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(ComplexOptionType2, fred_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(ComplexOptionType2, barney_),
  };
  ComplexOptionType2_reflection_ =
    ::google::protobuf::internal::GeneratedMessageReflection::NewGeneratedMessageReflection(
      ComplexOptionType2_descriptor_,
      ComplexOptionType2::internal_default_instance(),
      ComplexOptionType2_offsets_,
      GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(ComplexOptionType2, _has_bits_),
      -1,
      GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(ComplexOptionType2, _extensions_),
      sizeof(ComplexOptionType2),
      GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(ComplexOptionType2, _internal_metadata_));
  ComplexOptionType2_ComplexOptionType4_descriptor_ = ComplexOptionType2_descriptor_->nested_type(0);
  static const int ComplexOptionType2_ComplexOptionType4_offsets_[1] = {
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(ComplexOptionType2_ComplexOptionType4, waldo_),
  };
  ComplexOptionType2_ComplexOptionType4_reflection_ =
    ::google::protobuf::internal::GeneratedMessageReflection::NewGeneratedMessageReflection(
      ComplexOptionType2_ComplexOptionType4_descriptor_,
      ComplexOptionType2_ComplexOptionType4::internal_default_instance(),
      ComplexOptionType2_ComplexOptionType4_offsets_,
      GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(ComplexOptionType2_ComplexOptionType4, _has_bits_),
      -1,
      -1,
      sizeof(ComplexOptionType2_ComplexOptionType4),
      GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(ComplexOptionType2_ComplexOptionType4, _internal_metadata_));
  ComplexOptionType3_descriptor_ = file->message_type(14);
  static const int ComplexOptionType3_offsets_[2] = {
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(ComplexOptionType3, qux_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(ComplexOptionType3, complexoptiontype5_),
  };
  ComplexOptionType3_reflection_ =
    ::google::protobuf::internal::GeneratedMessageReflection::NewGeneratedMessageReflection(
      ComplexOptionType3_descriptor_,
      ComplexOptionType3::internal_default_instance(),
      ComplexOptionType3_offsets_,
      GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(ComplexOptionType3, _has_bits_),
      -1,
      -1,
      sizeof(ComplexOptionType3),
      GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(ComplexOptionType3, _internal_metadata_));
  ComplexOptionType3_ComplexOptionType5_descriptor_ = ComplexOptionType3_descriptor_->nested_type(0);
  static const int ComplexOptionType3_ComplexOptionType5_offsets_[1] = {
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(ComplexOptionType3_ComplexOptionType5, plugh_),
  };
  ComplexOptionType3_ComplexOptionType5_reflection_ =
    ::google::protobuf::internal::GeneratedMessageReflection::NewGeneratedMessageReflection(
      ComplexOptionType3_ComplexOptionType5_descriptor_,
      ComplexOptionType3_ComplexOptionType5::internal_default_instance(),
      ComplexOptionType3_ComplexOptionType5_offsets_,
      GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(ComplexOptionType3_ComplexOptionType5, _has_bits_),
      -1,
      -1,
      sizeof(ComplexOptionType3_ComplexOptionType5),
      GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(ComplexOptionType3_ComplexOptionType5, _internal_metadata_));
  ComplexOpt6_descriptor_ = file->message_type(15);
  static const int ComplexOpt6_offsets_[1] = {
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(ComplexOpt6, xyzzy_),
  };
  ComplexOpt6_reflection_ =
    ::google::protobuf::internal::GeneratedMessageReflection::NewGeneratedMessageReflection(
      ComplexOpt6_descriptor_,
      ComplexOpt6::internal_default_instance(),
      ComplexOpt6_offsets_,
      GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(ComplexOpt6, _has_bits_),
      -1,
      -1,
      sizeof(ComplexOpt6),
      GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(ComplexOpt6, _internal_metadata_));
  VariousComplexOptions_descriptor_ = file->message_type(16);
  static const int VariousComplexOptions_offsets_[1] = {
  };
  VariousComplexOptions_reflection_ =
    ::google::protobuf::internal::GeneratedMessageReflection::NewGeneratedMessageReflection(
      VariousComplexOptions_descriptor_,
      VariousComplexOptions::internal_default_instance(),
      VariousComplexOptions_offsets_,
      GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(VariousComplexOptions, _has_bits_),
      -1,
      -1,
      sizeof(VariousComplexOptions),
      GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(VariousComplexOptions, _internal_metadata_));
  AggregateMessageSet_descriptor_ = file->message_type(17);
  static const int AggregateMessageSet_offsets_[1] = {
  };
  AggregateMessageSet_reflection_ =
    ::google::protobuf::internal::GeneratedMessageReflection::NewGeneratedMessageReflection(
      AggregateMessageSet_descriptor_,
      AggregateMessageSet::internal_default_instance(),
      AggregateMessageSet_offsets_,
      GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(AggregateMessageSet, _has_bits_),
      -1,
      GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(AggregateMessageSet, _extensions_),
      sizeof(AggregateMessageSet),
      GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(AggregateMessageSet, _internal_metadata_));
  AggregateMessageSetElement_descriptor_ = file->message_type(18);
  static const int AggregateMessageSetElement_offsets_[1] = {
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(AggregateMessageSetElement, s_),
  };
  AggregateMessageSetElement_reflection_ =
    ::google::protobuf::internal::GeneratedMessageReflection::NewGeneratedMessageReflection(
      AggregateMessageSetElement_descriptor_,
      AggregateMessageSetElement::internal_default_instance(),
      AggregateMessageSetElement_offsets_,
      GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(AggregateMessageSetElement, _has_bits_),
      -1,
      -1,
      sizeof(AggregateMessageSetElement),
      GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(AggregateMessageSetElement, _internal_metadata_));
  Aggregate_descriptor_ = file->message_type(19);
  static const int Aggregate_offsets_[5] = {
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(Aggregate, i_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(Aggregate, s_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(Aggregate, sub_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(Aggregate, file_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(Aggregate, mset_),
  };
  Aggregate_reflection_ =
    ::google::protobuf::internal::GeneratedMessageReflection::NewGeneratedMessageReflection(
      Aggregate_descriptor_,
      Aggregate::internal_default_instance(),
      Aggregate_offsets_,
      GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(Aggregate, _has_bits_),
      -1,
      -1,
      sizeof(Aggregate),
      GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(Aggregate, _internal_metadata_));
  AggregateMessage_descriptor_ = file->message_type(20);
  static const int AggregateMessage_offsets_[1] = {
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(AggregateMessage, fieldname_),
  };
  AggregateMessage_reflection_ =
    ::google::protobuf::internal::GeneratedMessageReflection::NewGeneratedMessageReflection(
      AggregateMessage_descriptor_,
      AggregateMessage::internal_default_instance(),
      AggregateMessage_offsets_,
      GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(AggregateMessage, _has_bits_),
      -1,
      -1,
      sizeof(AggregateMessage),
      GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(AggregateMessage, _internal_metadata_));
  NestedOptionType_descriptor_ = file->message_type(21);
  static const int NestedOptionType_offsets_[1] = {
  };
  NestedOptionType_reflection_ =
    ::google::protobuf::internal::GeneratedMessageReflection::NewGeneratedMessageReflection(
      NestedOptionType_descriptor_,
      NestedOptionType::internal_default_instance(),
      NestedOptionType_offsets_,
      GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(NestedOptionType, _has_bits_),
      -1,
      -1,
      sizeof(NestedOptionType),
      GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(NestedOptionType, _internal_metadata_));
  NestedOptionType_NestedMessage_descriptor_ = NestedOptionType_descriptor_->nested_type(0);
  static const int NestedOptionType_NestedMessage_offsets_[1] = {
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(NestedOptionType_NestedMessage, nested_field_),
  };
  NestedOptionType_NestedMessage_reflection_ =
    ::google::protobuf::internal::GeneratedMessageReflection::NewGeneratedMessageReflection(
      NestedOptionType_NestedMessage_descriptor_,
      NestedOptionType_NestedMessage::internal_default_instance(),
      NestedOptionType_NestedMessage_offsets_,
      GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(NestedOptionType_NestedMessage, _has_bits_),
      -1,
      -1,
      sizeof(NestedOptionType_NestedMessage),
      GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(NestedOptionType_NestedMessage, _internal_metadata_));
  NestedOptionType_NestedEnum_descriptor_ = NestedOptionType_descriptor_->enum_type(0);
  OldOptionType_descriptor_ = file->message_type(22);
  static const int OldOptionType_offsets_[1] = {
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(OldOptionType, value_),
  };
  OldOptionType_reflection_ =
    ::google::protobuf::internal::GeneratedMessageReflection::NewGeneratedMessageReflection(
      OldOptionType_descriptor_,
      OldOptionType::internal_default_instance(),
      OldOptionType_offsets_,
      GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(OldOptionType, _has_bits_),
      -1,
      -1,
      sizeof(OldOptionType),
      GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(OldOptionType, _internal_metadata_));
  OldOptionType_TestEnum_descriptor_ = OldOptionType_descriptor_->enum_type(0);
  NewOptionType_descriptor_ = file->message_type(23);
  static const int NewOptionType_offsets_[1] = {
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(NewOptionType, value_),
  };
  NewOptionType_reflection_ =
    ::google::protobuf::internal::GeneratedMessageReflection::NewGeneratedMessageReflection(
      NewOptionType_descriptor_,
      NewOptionType::internal_default_instance(),
      NewOptionType_offsets_,
      GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(NewOptionType, _has_bits_),
      -1,
      -1,
      sizeof(NewOptionType),
      GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(NewOptionType, _internal_metadata_));
  NewOptionType_TestEnum_descriptor_ = NewOptionType_descriptor_->enum_type(0);
  TestMessageWithRequiredEnumOption_descriptor_ = file->message_type(24);
  static const int TestMessageWithRequiredEnumOption_offsets_[1] = {
  };
  TestMessageWithRequiredEnumOption_reflection_ =
    ::google::protobuf::internal::GeneratedMessageReflection::NewGeneratedMessageReflection(
      TestMessageWithRequiredEnumOption_descriptor_,
      TestMessageWithRequiredEnumOption::internal_default_instance(),
      TestMessageWithRequiredEnumOption_offsets_,
      GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(TestMessageWithRequiredEnumOption, _has_bits_),
      -1,
      -1,
      sizeof(TestMessageWithRequiredEnumOption),
      GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(TestMessageWithRequiredEnumOption, _internal_metadata_));
  MethodOpt1_descriptor_ = file->enum_type(0);
  AggregateEnum_descriptor_ = file->enum_type(1);
  TestServiceWithCustomOptions_descriptor_ = file->service(0);
  AggregateService_descriptor_ = file->service(1);
}

namespace {

GOOGLE_PROTOBUF_DECLARE_ONCE(protobuf_AssignDescriptors_once_);
void protobuf_AssignDescriptorsOnce() {
  ::google::protobuf::GoogleOnceInit(&protobuf_AssignDescriptors_once_,
                 &protobuf_AssignDesc_google_2fprotobuf_2funittest_5fcustom_5foptions_2eproto);
}

void protobuf_RegisterTypes(const ::std::string&) GOOGLE_ATTRIBUTE_COLD;
void protobuf_RegisterTypes(const ::std::string&) {
  protobuf_AssignDescriptorsOnce();
  ::google::protobuf::MessageFactory::InternalRegisterGeneratedMessage(
      TestMessageWithCustomOptions_descriptor_, TestMessageWithCustomOptions::internal_default_instance());
  ::google::protobuf::MessageFactory::InternalRegisterGeneratedMessage(
      CustomOptionFooRequest_descriptor_, CustomOptionFooRequest::internal_default_instance());
  ::google::protobuf::MessageFactory::InternalRegisterGeneratedMessage(
      CustomOptionFooResponse_descriptor_, CustomOptionFooResponse::internal_default_instance());
  ::google::protobuf::MessageFactory::InternalRegisterGeneratedMessage(
      CustomOptionFooClientMessage_descriptor_, CustomOptionFooClientMessage::internal_default_instance());
  ::google::protobuf::MessageFactory::InternalRegisterGeneratedMessage(
      CustomOptionFooServerMessage_descriptor_, CustomOptionFooServerMessage::internal_default_instance());
  ::google::protobuf::MessageFactory::InternalRegisterGeneratedMessage(
      DummyMessageContainingEnum_descriptor_, DummyMessageContainingEnum::internal_default_instance());
  ::google::protobuf::MessageFactory::InternalRegisterGeneratedMessage(
      DummyMessageInvalidAsOptionType_descriptor_, DummyMessageInvalidAsOptionType::internal_default_instance());
  ::google::protobuf::MessageFactory::InternalRegisterGeneratedMessage(
      CustomOptionMinIntegerValues_descriptor_, CustomOptionMinIntegerValues::internal_default_instance());
  ::google::protobuf::MessageFactory::InternalRegisterGeneratedMessage(
      CustomOptionMaxIntegerValues_descriptor_, CustomOptionMaxIntegerValues::internal_default_instance());
  ::google::protobuf::MessageFactory::InternalRegisterGeneratedMessage(
      CustomOptionOtherValues_descriptor_, CustomOptionOtherValues::internal_default_instance());
  ::google::protobuf::MessageFactory::InternalRegisterGeneratedMessage(
      SettingRealsFromPositiveInts_descriptor_, SettingRealsFromPositiveInts::internal_default_instance());
  ::google::protobuf::MessageFactory::InternalRegisterGeneratedMessage(
      SettingRealsFromNegativeInts_descriptor_, SettingRealsFromNegativeInts::internal_default_instance());
  ::google::protobuf::MessageFactory::InternalRegisterGeneratedMessage(
      ComplexOptionType1_descriptor_, ComplexOptionType1::internal_default_instance());
  ::google::protobuf::MessageFactory::InternalRegisterGeneratedMessage(
      ComplexOptionType2_descriptor_, ComplexOptionType2::internal_default_instance());
  ::google::protobuf::MessageFactory::InternalRegisterGeneratedMessage(
      ComplexOptionType2_ComplexOptionType4_descriptor_, ComplexOptionType2_ComplexOptionType4::internal_default_instance());
  ::google::protobuf::MessageFactory::InternalRegisterGeneratedMessage(
      ComplexOptionType3_descriptor_, ComplexOptionType3::internal_default_instance());
  ::google::protobuf::MessageFactory::InternalRegisterGeneratedMessage(
      ComplexOptionType3_ComplexOptionType5_descriptor_, ComplexOptionType3_ComplexOptionType5::internal_default_instance());
  ::google::protobuf::MessageFactory::InternalRegisterGeneratedMessage(
      ComplexOpt6_descriptor_, ComplexOpt6::internal_default_instance());
  ::google::protobuf::MessageFactory::InternalRegisterGeneratedMessage(
      VariousComplexOptions_descriptor_, VariousComplexOptions::internal_default_instance());
  ::google::protobuf::MessageFactory::InternalRegisterGeneratedMessage(
      AggregateMessageSet_descriptor_, AggregateMessageSet::internal_default_instance());
  ::google::protobuf::MessageFactory::InternalRegisterGeneratedMessage(
      AggregateMessageSetElement_descriptor_, AggregateMessageSetElement::internal_default_instance());
  ::google::protobuf::MessageFactory::InternalRegisterGeneratedMessage(
      Aggregate_descriptor_, Aggregate::internal_default_instance());
  ::google::protobuf::MessageFactory::InternalRegisterGeneratedMessage(
      AggregateMessage_descriptor_, AggregateMessage::internal_default_instance());
  ::google::protobuf::MessageFactory::InternalRegisterGeneratedMessage(
      NestedOptionType_descriptor_, NestedOptionType::internal_default_instance());
  ::google::protobuf::MessageFactory::InternalRegisterGeneratedMessage(
      NestedOptionType_NestedMessage_descriptor_, NestedOptionType_NestedMessage::internal_default_instance());
  ::google::protobuf::MessageFactory::InternalRegisterGeneratedMessage(
      OldOptionType_descriptor_, OldOptionType::internal_default_instance());
  ::google::protobuf::MessageFactory::InternalRegisterGeneratedMessage(
      NewOptionType_descriptor_, NewOptionType::internal_default_instance());
  ::google::protobuf::MessageFactory::InternalRegisterGeneratedMessage(
      TestMessageWithRequiredEnumOption_descriptor_, TestMessageWithRequiredEnumOption::internal_default_instance());
}

}  // namespace

void protobuf_ShutdownFile_google_2fprotobuf_2funittest_5fcustom_5foptions_2eproto() {
  TestMessageWithCustomOptions_default_instance_.Shutdown();
  delete TestMessageWithCustomOptions_default_oneof_instance_;
  delete TestMessageWithCustomOptions_reflection_;
  CustomOptionFooRequest_default_instance_.Shutdown();
  delete CustomOptionFooRequest_reflection_;
  CustomOptionFooResponse_default_instance_.Shutdown();
  delete CustomOptionFooResponse_reflection_;
  CustomOptionFooClientMessage_default_instance_.Shutdown();
  delete CustomOptionFooClientMessage_reflection_;
  CustomOptionFooServerMessage_default_instance_.Shutdown();
  delete CustomOptionFooServerMessage_reflection_;
  DummyMessageContainingEnum_default_instance_.Shutdown();
  delete DummyMessageContainingEnum_reflection_;
  DummyMessageInvalidAsOptionType_default_instance_.Shutdown();
  delete DummyMessageInvalidAsOptionType_reflection_;
  CustomOptionMinIntegerValues_default_instance_.Shutdown();
  delete CustomOptionMinIntegerValues_reflection_;
  CustomOptionMaxIntegerValues_default_instance_.Shutdown();
  delete CustomOptionMaxIntegerValues_reflection_;
  CustomOptionOtherValues_default_instance_.Shutdown();
  delete CustomOptionOtherValues_reflection_;
  SettingRealsFromPositiveInts_default_instance_.Shutdown();
  delete SettingRealsFromPositiveInts_reflection_;
  SettingRealsFromNegativeInts_default_instance_.Shutdown();
  delete SettingRealsFromNegativeInts_reflection_;
  ComplexOptionType1_default_instance_.Shutdown();
  delete ComplexOptionType1_reflection_;
  ComplexOptionType2_default_instance_.Shutdown();
  delete ComplexOptionType2_reflection_;
  ComplexOptionType2_ComplexOptionType4_default_instance_.Shutdown();
  delete ComplexOptionType2_ComplexOptionType4_reflection_;
  ComplexOptionType3_default_instance_.Shutdown();
  delete ComplexOptionType3_reflection_;
  ComplexOptionType3_ComplexOptionType5_default_instance_.Shutdown();
  delete ComplexOptionType3_ComplexOptionType5_reflection_;
  ComplexOpt6_default_instance_.Shutdown();
  delete ComplexOpt6_reflection_;
  VariousComplexOptions_default_instance_.Shutdown();
  delete VariousComplexOptions_reflection_;
  AggregateMessageSet_default_instance_.Shutdown();
  delete AggregateMessageSet_reflection_;
  AggregateMessageSetElement_default_instance_.Shutdown();
  delete AggregateMessageSetElement_reflection_;
  Aggregate_default_instance_.Shutdown();
  delete Aggregate_reflection_;
  AggregateMessage_default_instance_.Shutdown();
  delete AggregateMessage_reflection_;
  NestedOptionType_default_instance_.Shutdown();
  delete NestedOptionType_reflection_;
  NestedOptionType_NestedMessage_default_instance_.Shutdown();
  delete NestedOptionType_NestedMessage_reflection_;
  OldOptionType_default_instance_.Shutdown();
  delete OldOptionType_reflection_;
  NewOptionType_default_instance_.Shutdown();
  delete NewOptionType_reflection_;
  TestMessageWithRequiredEnumOption_default_instance_.Shutdown();
  delete TestMessageWithRequiredEnumOption_reflection_;
}

void protobuf_InitDefaults_google_2fprotobuf_2funittest_5fcustom_5foptions_2eproto_impl() {
  GOOGLE_PROTOBUF_VERIFY_VERSION;

  ::google::protobuf::protobuf_InitDefaults_google_2fprotobuf_2fdescriptor_2eproto();
  ::google::protobuf::internal::GetEmptyString();
  TestMessageWithCustomOptions_default_instance_.DefaultConstruct();
  TestMessageWithCustomOptions_default_oneof_instance_ = new TestMessageWithCustomOptionsOneofInstance();
  CustomOptionFooRequest_default_instance_.DefaultConstruct();
  CustomOptionFooResponse_default_instance_.DefaultConstruct();
  CustomOptionFooClientMessage_default_instance_.DefaultConstruct();
  CustomOptionFooServerMessage_default_instance_.DefaultConstruct();
  DummyMessageContainingEnum_default_instance_.DefaultConstruct();
  DummyMessageInvalidAsOptionType_default_instance_.DefaultConstruct();
  CustomOptionMinIntegerValues_default_instance_.DefaultConstruct();
  CustomOptionMaxIntegerValues_default_instance_.DefaultConstruct();
  CustomOptionOtherValues_default_instance_.DefaultConstruct();
  SettingRealsFromPositiveInts_default_instance_.DefaultConstruct();
  SettingRealsFromNegativeInts_default_instance_.DefaultConstruct();
  ComplexOptionType1_default_instance_.DefaultConstruct();
  ComplexOptionType2_default_instance_.DefaultConstruct();
  ComplexOptionType2_ComplexOptionType4_default_instance_.DefaultConstruct();
  ComplexOptionType3_default_instance_.DefaultConstruct();
  ComplexOptionType3_ComplexOptionType5_default_instance_.DefaultConstruct();
  ComplexOpt6_default_instance_.DefaultConstruct();
  VariousComplexOptions_default_instance_.DefaultConstruct();
  AggregateMessageSet_default_instance_.DefaultConstruct();
  ::google::protobuf::internal::GetEmptyString();
  AggregateMessageSetElement_default_instance_.DefaultConstruct();
  ::google::protobuf::internal::GetEmptyString();
  Aggregate_default_instance_.DefaultConstruct();
  AggregateMessage_default_instance_.DefaultConstruct();
  NestedOptionType_default_instance_.DefaultConstruct();
  NestedOptionType_NestedMessage_default_instance_.DefaultConstruct();
  OldOptionType_default_instance_.DefaultConstruct();
  NewOptionType_default_instance_.DefaultConstruct();
  TestMessageWithRequiredEnumOption_default_instance_.DefaultConstruct();
  ::google::protobuf::internal::ExtensionSet::RegisterExtension(
    ::google::protobuf::FileOptions::internal_default_instance(),
    7736974, 4, false, false);
  ::google::protobuf::internal::ExtensionSet::RegisterExtension(
    ::google::protobuf::MessageOptions::internal_default_instance(),
    7739036, 5, false, false);
  ::google::protobuf::internal::ExtensionSet::RegisterExtension(
    ::google::protobuf::FieldOptions::internal_default_instance(),
    7740936, 6, false, false);
  ::google::protobuf::internal::ExtensionSet::RegisterExtension(
    ::google::protobuf::FieldOptions::internal_default_instance(),
    7753913, 5, false, false);
  ::google::protobuf::internal::ExtensionSet::RegisterExtension(
    ::google::protobuf::OneofOptions::internal_default_instance(),
    7740111, 5, false, false);
  ::google::protobuf::internal::ExtensionSet::RegisterExtension(
    ::google::protobuf::EnumOptions::internal_default_instance(),
    7753576, 15, false, false);
  ::google::protobuf::internal::ExtensionSet::RegisterExtension(
    ::google::protobuf::EnumValueOptions::internal_default_instance(),
    1560678, 5, false, false);
  ::google::protobuf::internal::ExtensionSet::RegisterExtension(
    ::google::protobuf::ServiceOptions::internal_default_instance(),
    7887650, 18, false, false);
  ::google::protobuf::internal::ExtensionSet::RegisterEnumExtension(
    ::google::protobuf::MethodOptions::internal_default_instance(),
    7890860, 14, false, false,
    &::protobuf_unittest::MethodOpt1_IsValid);
  ::google::protobuf::internal::ExtensionSet::RegisterExtension(
    ::google::protobuf::MessageOptions::internal_default_instance(),
    7706090, 8, false, false);
  ::google::protobuf::internal::ExtensionSet::RegisterExtension(
    ::google::protobuf::MessageOptions::internal_default_instance(),
    7705709, 5, false, false);
  ::google::protobuf::internal::ExtensionSet::RegisterExtension(
    ::google::protobuf::MessageOptions::internal_default_instance(),
    7705542, 3, false, false);
  ::google::protobuf::internal::ExtensionSet::RegisterExtension(
    ::google::protobuf::MessageOptions::internal_default_instance(),
    7704880, 13, false, false);
  ::google::protobuf::internal::ExtensionSet::RegisterExtension(
    ::google::protobuf::MessageOptions::internal_default_instance(),
    7702367, 4, false, false);
  ::google::protobuf::internal::ExtensionSet::RegisterExtension(
    ::google::protobuf::MessageOptions::internal_default_instance(),
    7701568, 17, false, false);
  ::google::protobuf::internal::ExtensionSet::RegisterExtension(
    ::google::protobuf::MessageOptions::internal_default_instance(),
    7700863, 18, false, false);
  ::google::protobuf::internal::ExtensionSet::RegisterExtension(
    ::google::protobuf::MessageOptions::internal_default_instance(),
    7700307, 7, false, false);
  ::google::protobuf::internal::ExtensionSet::RegisterExtension(
    ::google::protobuf::MessageOptions::internal_default_instance(),
    7700194, 6, false, false);
  ::google::protobuf::internal::ExtensionSet::RegisterExtension(
    ::google::protobuf::MessageOptions::internal_default_instance(),
    7698645, 15, false, false);
  ::google::protobuf::internal::ExtensionSet::RegisterExtension(
    ::google::protobuf::MessageOptions::internal_default_instance(),
    7685475, 16, false, false);
  ::google::protobuf::internal::ExtensionSet::RegisterExtension(
    ::google::protobuf::MessageOptions::internal_default_instance(),
    7675390, 2, false, false);
  ::google::protobuf::internal::ExtensionSet::RegisterExtension(
    ::google::protobuf::MessageOptions::internal_default_instance(),
    7673293, 1, false, false);
  ::google::protobuf::internal::ExtensionSet::RegisterExtension(
    ::google::protobuf::MessageOptions::internal_default_instance(),
    7673285, 9, false, false);
  ::google::protobuf::internal::ExtensionSet::RegisterExtension(
    ::google::protobuf::MessageOptions::internal_default_instance(),
    7673238, 12, false, false);
  ::google::protobuf::internal::ExtensionSet::RegisterEnumExtension(
    ::google::protobuf::MessageOptions::internal_default_instance(),
    7673233, 14, false, false,
    &::protobuf_unittest::DummyMessageContainingEnum_TestEnumType_IsValid);
  ::google::protobuf::internal::ExtensionSet::RegisterMessageExtension(
    ::google::protobuf::MessageOptions::internal_default_instance(),
    7665967, 11, false, false,
    ::protobuf_unittest::DummyMessageInvalidAsOptionType::internal_default_instance());
  ::google::protobuf::internal::ExtensionSet::RegisterExtension(
    ::protobuf_unittest::ComplexOptionType1::internal_default_instance(),
    7663707, 5, false, false);
  ::google::protobuf::internal::ExtensionSet::RegisterMessageExtension(
    ::protobuf_unittest::ComplexOptionType1::internal_default_instance(),
    7663442, 11, false, false,
    ::protobuf_unittest::ComplexOptionType3::internal_default_instance());
  ::google::protobuf::internal::ExtensionSet::RegisterExtension(
    ::protobuf_unittest::ComplexOptionType2::internal_default_instance(),
    7650927, 5, false, false);
  ::google::protobuf::internal::ExtensionSet::RegisterMessageExtension(
    ::protobuf_unittest::ComplexOptionType2::internal_default_instance(),
    7649992, 11, false, false,
    ::protobuf_unittest::ComplexOptionType1::internal_default_instance());
  ::google::protobuf::internal::ExtensionSet::RegisterMessageExtension(
    ::google::protobuf::MessageOptions::internal_default_instance(),
    7646756, 11, false, false,
    ::protobuf_unittest::ComplexOptionType1::internal_default_instance());
  ::google::protobuf::internal::ExtensionSet::RegisterMessageExtension(
    ::google::protobuf::MessageOptions::internal_default_instance(),
    7636949, 11, false, false,
    ::protobuf_unittest::ComplexOptionType2::internal_default_instance());
  ::google::protobuf::internal::ExtensionSet::RegisterMessageExtension(
    ::google::protobuf::MessageOptions::internal_default_instance(),
    7636463, 11, false, false,
    ::protobuf_unittest::ComplexOptionType3::internal_default_instance());
  ::google::protobuf::internal::ExtensionSet::RegisterMessageExtension(
    ::google::protobuf::MessageOptions::internal_default_instance(),
    7595468, 10, false, false,
    ::protobuf_unittest::ComplexOpt6::internal_default_instance());
  ::google::protobuf::internal::ExtensionSet::RegisterMessageExtension(
    ::google::protobuf::FileOptions::internal_default_instance(),
    15478479, 11, false, false,
    ::protobuf_unittest::Aggregate::internal_default_instance());
  ::google::protobuf::internal::ExtensionSet::RegisterMessageExtension(
    ::google::protobuf::MessageOptions::internal_default_instance(),
    15480088, 11, false, false,
    ::protobuf_unittest::Aggregate::internal_default_instance());
  ::google::protobuf::internal::ExtensionSet::RegisterMessageExtension(
    ::google::protobuf::FieldOptions::internal_default_instance(),
    15481374, 11, false, false,
    ::protobuf_unittest::Aggregate::internal_default_instance());
  ::google::protobuf::internal::ExtensionSet::RegisterMessageExtension(
    ::google::protobuf::EnumOptions::internal_default_instance(),
    15483218, 11, false, false,
    ::protobuf_unittest::Aggregate::internal_default_instance());
  ::google::protobuf::internal::ExtensionSet::RegisterMessageExtension(
    ::google::protobuf::EnumValueOptions::internal_default_instance(),
    15486921, 11, false, false,
    ::protobuf_unittest::Aggregate::internal_default_instance());
  ::google::protobuf::internal::ExtensionSet::RegisterMessageExtension(
    ::google::protobuf::ServiceOptions::internal_default_instance(),
    15497145, 11, false, false,
    ::protobuf_unittest::Aggregate::internal_default_instance());
  ::google::protobuf::internal::ExtensionSet::RegisterMessageExtension(
    ::google::protobuf::MethodOptions::internal_default_instance(),
    15512713, 11, false, false,
    ::protobuf_unittest::Aggregate::internal_default_instance());
  ::google::protobuf::internal::ExtensionSet::RegisterMessageExtension(
    ::google::protobuf::MessageOptions::internal_default_instance(),
    106161807, 11, false, false,
    ::protobuf_unittest::OldOptionType::internal_default_instance());
  TestMessageWithCustomOptions_default_instance_.get_mutable()->InitAsDefaultInstance();
  CustomOptionFooRequest_default_instance_.get_mutable()->InitAsDefaultInstance();
  CustomOptionFooResponse_default_instance_.get_mutable()->InitAsDefaultInstance();
  CustomOptionFooClientMessage_default_instance_.get_mutable()->InitAsDefaultInstance();
  CustomOptionFooServerMessage_default_instance_.get_mutable()->InitAsDefaultInstance();
  DummyMessageContainingEnum_default_instance_.get_mutable()->InitAsDefaultInstance();
  DummyMessageInvalidAsOptionType_default_instance_.get_mutable()->InitAsDefaultInstance();
  CustomOptionMinIntegerValues_default_instance_.get_mutable()->InitAsDefaultInstance();
  CustomOptionMaxIntegerValues_default_instance_.get_mutable()->InitAsDefaultInstance();
  CustomOptionOtherValues_default_instance_.get_mutable()->InitAsDefaultInstance();
  SettingRealsFromPositiveInts_default_instance_.get_mutable()->InitAsDefaultInstance();
  SettingRealsFromNegativeInts_default_instance_.get_mutable()->InitAsDefaultInstance();
  ComplexOptionType1_default_instance_.get_mutable()->InitAsDefaultInstance();
  ComplexOptionType2_default_instance_.get_mutable()->InitAsDefaultInstance();
  ComplexOptionType2_ComplexOptionType4_default_instance_.get_mutable()->InitAsDefaultInstance();
  ::google::protobuf::internal::ExtensionSet::RegisterMessageExtension(
    ::google::protobuf::MessageOptions::internal_default_instance(),
    7633546, 11, false, false,
    ::protobuf_unittest::ComplexOptionType2_ComplexOptionType4::internal_default_instance());
  ComplexOptionType3_default_instance_.get_mutable()->InitAsDefaultInstance();
  ComplexOptionType3_ComplexOptionType5_default_instance_.get_mutable()->InitAsDefaultInstance();
  ComplexOpt6_default_instance_.get_mutable()->InitAsDefaultInstance();
  VariousComplexOptions_default_instance_.get_mutable()->InitAsDefaultInstance();
  AggregateMessageSet_default_instance_.get_mutable()->InitAsDefaultInstance();
  AggregateMessageSetElement_default_instance_.get_mutable()->InitAsDefaultInstance();
  ::google::protobuf::internal::ExtensionSet::RegisterMessageExtension(
    ::protobuf_unittest::AggregateMessageSet::internal_default_instance(),
    15447542, 11, false, false,
    ::protobuf_unittest::AggregateMessageSetElement::internal_default_instance());
  Aggregate_default_instance_.get_mutable()->InitAsDefaultInstance();
  ::google::protobuf::internal::ExtensionSet::RegisterMessageExtension(
    ::google::protobuf::FileOptions::internal_default_instance(),
    15476903, 11, false, false,
    ::protobuf_unittest::Aggregate::internal_default_instance());
  AggregateMessage_default_instance_.get_mutable()->InitAsDefaultInstance();
  NestedOptionType_default_instance_.get_mutable()->InitAsDefaultInstance();
  ::google::protobuf::internal::ExtensionSet::RegisterExtension(
    ::google::protobuf::FileOptions::internal_default_instance(),
    7912573, 5, false, false);
  NestedOptionType_NestedMessage_default_instance_.get_mutable()->InitAsDefaultInstance();
  OldOptionType_default_instance_.get_mutable()->InitAsDefaultInstance();
  NewOptionType_default_instance_.get_mutable()->InitAsDefaultInstance();
  TestMessageWithRequiredEnumOption_default_instance_.get_mutable()->InitAsDefaultInstance();
}

GOOGLE_PROTOBUF_DECLARE_ONCE(protobuf_InitDefaults_google_2fprotobuf_2funittest_5fcustom_5foptions_2eproto_once_);
void protobuf_InitDefaults_google_2fprotobuf_2funittest_5fcustom_5foptions_2eproto() {
  ::google::protobuf::GoogleOnceInit(&protobuf_InitDefaults_google_2fprotobuf_2funittest_5fcustom_5foptions_2eproto_once_,
                 &protobuf_InitDefaults_google_2fprotobuf_2funittest_5fcustom_5foptions_2eproto_impl);
}
void protobuf_AddDesc_google_2fprotobuf_2funittest_5fcustom_5foptions_2eproto_impl() {
  GOOGLE_PROTOBUF_VERIFY_VERSION;

  protobuf_InitDefaults_google_2fprotobuf_2funittest_5fcustom_5foptions_2eproto();
  ::google::protobuf::DescriptorPool::InternalAddGeneratedFile(
    "\n-google/protobuf/unittest_custom_option"
    "s.proto\022\021protobuf_unittest\032 google/proto"
    "buf/descriptor.proto\"\277\001\n\034TestMessageWith"
    "CustomOptions\022\036\n\006field1\030\001 \001(\tB\016\010\001\301\340\303\035-\341u"
    "\n\002\000\000\000\022\025\n\013oneof_field\030\002 \001(\005H\000\";\n\006AnEnum\022\017"
    "\n\013ANENUM_VAL1\020\001\022\026\n\013ANENUM_VAL2\020\002\032\005\260\206\372\005{\032"
    "\010\305\366\311\035\353\374\377\377:\020\010\000\340\351\302\035\310\377\377\377\377\377\377\377\377\001B\031\n\007AnOneof\022\016"
    "\370\254\303\035\235\377\377\377\377\377\377\377\377\001\"\030\n\026CustomOptionFooRequest"
    "\"\031\n\027CustomOptionFooResponse\"\036\n\034CustomOpt"
    "ionFooClientMessage\"\036\n\034CustomOptionFooSe"
    "rverMessage\"m\n\032DummyMessageContainingEnu"
    "m\"O\n\014TestEnumType\022\032\n\026TEST_OPTION_ENUM_TY"
    "PE1\020\026\022#\n\026TEST_OPTION_ENUM_TYPE2\020\351\377\377\377\377\377\377\377"
    "\377\001\"!\n\037DummyMessageInvalidAsOptionType\"\212\001"
    "\n\034CustomOptionMinIntegerValues:j\320\336\262\035\000\350\306\262"
    "\035\200\200\200\200\370\377\377\377\377\001\260\274\262\035\200\200\200\200\200\200\200\200\200\001\200\223\262\035\000\370\365\260\035\000\200\304\260\035\377"
    "\377\377\377\017\370\227\260\035\377\377\377\377\377\377\377\377\377\001\235\365\257\035\000\000\000\000\221\356\257\035\000\000\000\000\000\000\000\000\255\215"
    "\257\035\000\000\000\200\231\326\250\035\000\000\000\000\000\000\000\200\"\221\001\n\034CustomOptionMaxIn"
    "tegerValues:q\320\336\262\035\001\350\306\262\035\377\377\377\377\007\260\274\262\035\377\377\377\377\377\377\377\377\177"
    "\200\223\262\035\377\377\377\377\017\370\365\260\035\377\377\377\377\377\377\377\377\377\001\200\304\260\035\376\377\377\377\017\370\227\260\035\376\377\377\377"
    "\377\377\377\377\377\001\235\365\257\035\377\377\377\377\221\356\257\035\377\377\377\377\377\377\377\377\255\215\257\035\377\377\377\177\231\326\250\035\377\377"
    "\377\377\377\377\377\177\"n\n\027CustomOptionOtherValues:S\350\306\262\035\234"
    "\377\377\377\377\377\377\377\377\001\365\337\243\035\347\207EA\351\334\242\035\373Y\214B\312\300\363\?\252\334\242\035\016Hello,"
    " \"World\"\262\331\242\035\013Hello\000World\210\331\242\035\351\377\377\377\377\377\377\377\377\001\"4"
    "\n\034SettingRealsFromPositiveInts:\024\365\337\243\035\000\000@A"
    "\351\334\242\035\000\000\000\000\000@c@\"4\n\034SettingRealsFromNegative"
    "Ints:\024\365\337\243\035\000\000@\301\351\334\242\035\000\000\000\000\000@c\300\"U\n\022ComplexOpt"
    "ionType1\022\013\n\003foo\030\001 \001(\005\022\014\n\004foo2\030\002 \001(\005\022\014\n\004f"
    "oo3\030\003 \001(\005\022\014\n\004foo4\030\004 \003(\005*\010\010d\020\200\200\200\200\002\"\213\003\n\022Co"
    "mplexOptionType2\0222\n\003bar\030\001 \001(\0132%.protobuf"
    "_unittest.ComplexOptionType1\022\013\n\003baz\030\002 \001("
    "\005\022F\n\004fred\030\003 \001(\01328.protobuf_unittest.Comp"
    "lexOptionType2.ComplexOptionType4\022H\n\006bar"
    "ney\030\004 \003(\01328.protobuf_unittest.ComplexOpt"
    "ionType2.ComplexOptionType4\032\227\001\n\022ComplexO"
    "ptionType4\022\r\n\005waldo\030\001 \001(\0052r\n\014complex_opt"
    "4\022\037.google.protobuf.MessageOptions\030\212\365\321\003 "
    "\001(\01328.protobuf_unittest.ComplexOptionTyp"
    "e2.ComplexOptionType4*\010\010d\020\200\200\200\200\002\"\234\001\n\022Comp"
    "lexOptionType3\022\013\n\003qux\030\001 \001(\005\022T\n\022complexop"
    "tiontype5\030\002 \001(\n28.protobuf_unittest.Comp"
    "lexOptionType3.ComplexOptionType5\032#\n\022Com"
    "plexOptionType5\022\r\n\005plugh\030\003 \001(\005\"\037\n\013Comple"
    "xOpt6\022\020\n\005xyzzy\030\337\277\317\003 \001(\005\"\361\001\n\025VariousCompl"
    "exOptions:\327\001\242\342\225\035\002\010*\242\342\225\035\006\330\205\236\035\304\002\242\342\225\035\010\222\365\235\035\003"
    "\010\354\006\242\342\225\035\002 c\242\342\225\035\002 X\252\375\220\035\003\020\333\007\252\375\220\035\006\370\346\227\035\216\005\252\375\220\035"
    "\005\n\003\010\347\005\252\375\220\035\010\n\006\330\205\236\035\317\017\252\375\220\035\n\n\010\222\365\235\035\003\010\330\017\252\375\220\035\010\302"
    "\254\227\035\003\010\345\005\252\375\220\035\013\302\254\227\035\006\330\205\236\035\316\017\252\375\220\035\r\302\254\227\035\010\222\365\235\035\003\010\311"
    "\020\322\250\217\035\003\010\263\017\252\375\220\035\005\032\003\010\301\002\252\375\220\035\004\"\002\010e\252\375\220\035\005\"\003\010\324\001\372\336"
    "\220\035\002\010\t\372\336\220\035\004\023\030\026\024\343\334\374\034\370\375\373\034\030\344\334\374\034\"#\n\023Aggregate"
    "MessageSet*\010\010\004\020\377\377\377\377\007:\002\010\001\"\240\001\n\032AggregateMe"
    "ssageSetElement\022\t\n\001s\030\001 \001(\t2w\n\025message_se"
    "t_extension\022&.protobuf_unittest.Aggregat"
    "eMessageSet\030\366\353\256\007 \001(\0132-.protobuf_unittest"
    ".AggregateMessageSetElement\"\375\001\n\tAggregat"
    "e\022\t\n\001i\030\001 \001(\005\022\t\n\001s\030\002 \001(\t\022)\n\003sub\030\003 \001(\0132\034.p"
    "rotobuf_unittest.Aggregate\022*\n\004file\030\004 \001(\013"
    "2\034.google.protobuf.FileOptions\0224\n\004mset\030\005"
    " \001(\0132&.protobuf_unittest.AggregateMessag"
    "eSet2M\n\006nested\022\034.google.protobuf.FileOpt"
    "ions\030\247\321\260\007 \001(\0132\034.protobuf_unittest.Aggreg"
    "ate\"Y\n\020AggregateMessage\022)\n\tfieldname\030\001 \001"
    "(\005B\026\362\241\207;\021\022\017FieldAnnotation:\032\302\321\206;\025\010e\022\021Mes"
    "sageAnnotation\"\311\001\n\020NestedOptionType\032;\n\rN"
    "estedMessage\022\"\n\014nested_field\030\001 \001(\005B\014\301\340\303\035"
    "\352\003\000\000\000\000\000\000:\006\340\351\302\035\351\007\"5\n\nNestedEnum\022\035\n\021NESTED"
    "_ENUM_VALUE\020\001\032\006\260\206\372\005\354\007\032\010\305\366\311\035\353\003\000\0002A\n\020neste"
    "d_extension\022\034.google.protobuf.FileOption"
    "s\030\375\370\342\003 \001(\005B\006\310\213\312\035\355\007\"d\n\rOldOptionType\0228\n\005v"
    "alue\030\001 \002(\0162).protobuf_unittest.OldOption"
    "Type.TestEnum\"\031\n\010TestEnum\022\r\n\tOLD_VALUE\020\000"
    "\"s\n\rNewOptionType\0228\n\005value\030\001 \002(\0162).proto"
    "buf_unittest.NewOptionType.TestEnum\"(\n\010T"
    "estEnum\022\r\n\tOLD_VALUE\020\000\022\r\n\tNEW_VALUE\020\001\"-\n"
    "!TestMessageWithRequiredEnumOption:\010\372\350\374\224"
    "\003\002\010\000*6\n\nMethodOpt1\022\023\n\017METHODOPT1_VAL1\020\001\022"
    "\023\n\017METHODOPT1_VAL2\020\002*M\n\rAggregateEnum\022%\n"
    "\005VALUE\020\001\032\032\312\374\211;\025\022\023EnumValueAnnotation\032\025\222\225"
    "\210;\020\022\016EnumAnnotation2\216\001\n\034TestServiceWithC"
    "ustomOptions\022c\n\003Foo\022).protobuf_unittest."
    "CustomOptionFooRequest\032*.protobuf_unitte"
    "st.CustomOptionFooResponse\"\005\340\372\214\036\002\032\t\220\262\213\036\323"
    "\333\200\313I2\231\001\n\020AggregateService\022k\n\006Method\022#.pr"
    "otobuf_unittest.AggregateMessage\032#.proto"
    "buf_unittest.AggregateMessage\"\027\312\310\226;\022\022\020Me"
    "thodAnnotation\032\030\312\373\216;\023\022\021ServiceAnnotation"
    ":2\n\tfile_opt1\022\034.google.protobuf.FileOpti"
    "ons\030\216\235\330\003 \001(\004:8\n\014message_opt1\022\037.google.pr"
    "otobuf.MessageOptions\030\234\255\330\003 \001(\005:4\n\nfield_"
    "opt1\022\035.google.protobuf.FieldOptions\030\210\274\330\003"
    " \001(\006:8\n\nfield_opt2\022\035.google.protobuf.Fie"
    "ldOptions\030\271\241\331\003 \001(\005:\00242:4\n\noneof_opt1\022\035.g"
    "oogle.protobuf.OneofOptions\030\317\265\330\003 \001(\005:2\n\t"
    "enum_opt1\022\034.google.protobuf.EnumOptions\030"
    "\350\236\331\003 \001(\017:<\n\017enum_value_opt1\022!.google.pro"
    "tobuf.EnumValueOptions\030\346\240_ \001(\005:8\n\014servic"
    "e_opt1\022\037.google.protobuf.ServiceOptions\030"
    "\242\266\341\003 \001(\022:U\n\013method_opt1\022\036.google.protobu"
    "f.MethodOptions\030\254\317\341\003 \001(\0162\035.protobuf_unit"
    "test.MethodOpt1:4\n\010bool_opt\022\037.google.pro"
    "tobuf.MessageOptions\030\352\253\326\003 \001(\010:5\n\tint32_o"
    "pt\022\037.google.protobuf.MessageOptions\030\355\250\326\003"
    " \001(\005:5\n\tint64_opt\022\037.google.protobuf.Mess"
    "ageOptions\030\306\247\326\003 \001(\003:6\n\nuint32_opt\022\037.goog"
    "le.protobuf.MessageOptions\030\260\242\326\003 \001(\r:6\n\nu"
    "int64_opt\022\037.google.protobuf.MessageOptio"
    "ns\030\337\216\326\003 \001(\004:6\n\nsint32_opt\022\037.google.proto"
    "buf.MessageOptions\030\300\210\326\003 \001(\021:6\n\nsint64_op"
    "t\022\037.google.protobuf.MessageOptions\030\377\202\326\003 "
    "\001(\022:7\n\013fixed32_opt\022\037.google.protobuf.Mes"
    "sageOptions\030\323\376\325\003 \001(\007:7\n\013fixed64_opt\022\037.go"
    "ogle.protobuf.MessageOptions\030\342\375\325\003 \001(\006:8\n"
    "\014sfixed32_opt\022\037.google.protobuf.MessageO"
    "ptions\030\325\361\325\003 \001(\017:8\n\014sfixed64_opt\022\037.google"
    ".protobuf.MessageOptions\030\343\212\325\003 \001(\020:5\n\tflo"
    "at_opt\022\037.google.protobuf.MessageOptions\030"
    "\376\273\324\003 \001(\002:6\n\ndouble_opt\022\037.google.protobuf"
    ".MessageOptions\030\315\253\324\003 \001(\001:6\n\nstring_opt\022\037"
    ".google.protobuf.MessageOptions\030\305\253\324\003 \001(\t"
    ":5\n\tbytes_opt\022\037.google.protobuf.MessageO"
    "ptions\030\226\253\324\003 \001(\014:p\n\010enum_opt\022\037.google.pro"
    "tobuf.MessageOptions\030\221\253\324\003 \001(\0162:.protobuf"
    "_unittest.DummyMessageContainingEnum.Tes"
    "tEnumType:p\n\020message_type_opt\022\037.google.p"
    "rotobuf.MessageOptions\030\257\362\323\003 \001(\01322.protob"
    "uf_unittest.DummyMessageInvalidAsOptionT"
    "ype:6\n\004quux\022%.protobuf_unittest.ComplexO"
    "ptionType1\030\333\340\323\003 \001(\005:^\n\005corge\022%.protobuf_"
    "unittest.ComplexOptionType1\030\322\336\323\003 \001(\0132%.p"
    "rotobuf_unittest.ComplexOptionType3:8\n\006g"
    "rault\022%.protobuf_unittest.ComplexOptionT"
    "ype2\030\357\374\322\003 \001(\005:_\n\006garply\022%.protobuf_unitt"
    "est.ComplexOptionType2\030\310\365\322\003 \001(\0132%.protob"
    "uf_unittest.ComplexOptionType1:_\n\014comple"
    "x_opt1\022\037.google.protobuf.MessageOptions\030"
    "\244\334\322\003 \001(\0132%.protobuf_unittest.ComplexOpti"
    "onType1:_\n\014complex_opt2\022\037.google.protobu"
    "f.MessageOptions\030\325\217\322\003 \001(\0132%.protobuf_uni"
    "ttest.ComplexOptionType2:_\n\014complex_opt3"
    "\022\037.google.protobuf.MessageOptions\030\357\213\322\003 \001"
    "(\0132%.protobuf_unittest.ComplexOptionType"
    "3:W\n\013complexopt6\022\037.google.protobuf.Messa"
    "geOptions\030\314\313\317\003 \001(\n2\036.protobuf_unittest.C"
    "omplexOpt6:N\n\007fileopt\022\034.google.protobuf."
    "FileOptions\030\317\335\260\007 \001(\0132\034.protobuf_unittest"
    ".Aggregate:P\n\006msgopt\022\037.google.protobuf.M"
    "essageOptions\030\230\352\260\007 \001(\0132\034.protobuf_unitte"
    "st.Aggregate:P\n\010fieldopt\022\035.google.protob"
    "uf.FieldOptions\030\236\364\260\007 \001(\0132\034.protobuf_unit"
    "test.Aggregate:N\n\007enumopt\022\034.google.proto"
    "buf.EnumOptions\030\322\202\261\007 \001(\0132\034.protobuf_unit"
    "test.Aggregate:V\n\nenumvalopt\022!.google.pr"
    "otobuf.EnumValueOptions\030\311\237\261\007 \001(\0132\034.proto"
    "buf_unittest.Aggregate:T\n\nserviceopt\022\037.g"
    "oogle.protobuf.ServiceOptions\030\271\357\261\007 \001(\0132\034"
    ".protobuf_unittest.Aggregate:R\n\tmethodop"
    "t\022\036.google.protobuf.MethodOptions\030\211\351\262\007 \001"
    "(\0132\034.protobuf_unittest.Aggregate:_\n\021requ"
    "ired_enum_opt\022\037.google.protobuf.MessageO"
    "ptions\030\217\315\3172 \001(\0132 .protobuf_unittest.OldO"
    "ptionTypeB\207\001\200\001\001\210\001\001\220\001\001\360\350\301\035\352\255\300\345$\372\354\205;p\010d\022\016F"
    "ileAnnotation\032\026\022\024NestedFileAnnotation\"\036\372"
    "\354\205;\031\022\027FileExtensionAnnotation*$\013\020\366\353\256\007\032\033\n"
    "\031EmbeddedMessageSetElement\014", 6547);
  ::google::protobuf::MessageFactory::InternalRegisterGeneratedFile(
    "google/protobuf/unittest_custom_options.proto", &protobuf_RegisterTypes);
  ::google::protobuf::protobuf_AddDesc_google_2fprotobuf_2fdescriptor_2eproto();
  ::google::protobuf::internal::OnShutdown(&protobuf_ShutdownFile_google_2fprotobuf_2funittest_5fcustom_5foptions_2eproto);
}

GOOGLE_PROTOBUF_DECLARE_ONCE(protobuf_AddDesc_google_2fprotobuf_2funittest_5fcustom_5foptions_2eproto_once_);
void protobuf_AddDesc_google_2fprotobuf_2funittest_5fcustom_5foptions_2eproto() {
  ::google::protobuf::GoogleOnceInit(&protobuf_AddDesc_google_2fprotobuf_2funittest_5fcustom_5foptions_2eproto_once_,
                 &protobuf_AddDesc_google_2fprotobuf_2funittest_5fcustom_5foptions_2eproto_impl);
}
// Force AddDescriptors() to be called at static initialization time.
struct StaticDescriptorInitializer_google_2fprotobuf_2funittest_5fcustom_5foptions_2eproto {
  StaticDescriptorInitializer_google_2fprotobuf_2funittest_5fcustom_5foptions_2eproto() {
    protobuf_AddDesc_google_2fprotobuf_2funittest_5fcustom_5foptions_2eproto();
  }
} static_descriptor_initializer_google_2fprotobuf_2funittest_5fcustom_5foptions_2eproto_;
const ::google::protobuf::EnumDescriptor* MethodOpt1_descriptor() {
  protobuf_AssignDescriptorsOnce();
  return MethodOpt1_descriptor_;
}
bool MethodOpt1_IsValid(int value) {
  switch (value) {
    case 1:
    case 2:
      return true;
    default:
      return false;
  }
}

const ::google::protobuf::EnumDescriptor* AggregateEnum_descriptor() {
  protobuf_AssignDescriptorsOnce();
  return AggregateEnum_descriptor_;
}
bool AggregateEnum_IsValid(int value) {
  switch (value) {
    case 1:
      return true;
    default:
      return false;
  }
}


namespace {

static void MergeFromFail(int line) GOOGLE_ATTRIBUTE_COLD GOOGLE_ATTRIBUTE_NORETURN;
static void MergeFromFail(int line) {
  ::google::protobuf::internal::MergeFromFail(__FILE__, line);
}

}  // namespace


// ===================================================================

const ::google::protobuf::EnumDescriptor* TestMessageWithCustomOptions_AnEnum_descriptor() {
  protobuf_AssignDescriptorsOnce();
  return TestMessageWithCustomOptions_AnEnum_descriptor_;
}
bool TestMessageWithCustomOptions_AnEnum_IsValid(int value) {
  switch (value) {
    case 1:
    case 2:
      return true;
    default:
      return false;
  }
}

#if !defined(_MSC_VER) || _MSC_VER >= 1900
const TestMessageWithCustomOptions_AnEnum TestMessageWithCustomOptions::ANENUM_VAL1;
const TestMessageWithCustomOptions_AnEnum TestMessageWithCustomOptions::ANENUM_VAL2;
const TestMessageWithCustomOptions_AnEnum TestMessageWithCustomOptions::AnEnum_MIN;
const TestMessageWithCustomOptions_AnEnum TestMessageWithCustomOptions::AnEnum_MAX;
const int TestMessageWithCustomOptions::AnEnum_ARRAYSIZE;
#endif  // !defined(_MSC_VER) || _MSC_VER >= 1900
#if !defined(_MSC_VER) || _MSC_VER >= 1900
const int TestMessageWithCustomOptions::kField1FieldNumber;
const int TestMessageWithCustomOptions::kOneofFieldFieldNumber;
#endif  // !defined(_MSC_VER) || _MSC_VER >= 1900

TestMessageWithCustomOptions::TestMessageWithCustomOptions()
  : ::google::protobuf::Message(), _internal_metadata_(NULL) {
  if (this != internal_default_instance()) protobuf_InitDefaults_google_2fprotobuf_2funittest_5fcustom_5foptions_2eproto();
  SharedCtor();
  // @@protoc_insertion_point(constructor:protobuf_unittest.TestMessageWithCustomOptions)
}

void TestMessageWithCustomOptions::InitAsDefaultInstance() {
  TestMessageWithCustomOptions_default_oneof_instance_->oneof_field_ = 0;
}

TestMessageWithCustomOptions::TestMessageWithCustomOptions(const TestMessageWithCustomOptions& from)
  : ::google::protobuf::Message(),
    _internal_metadata_(NULL) {
  SharedCtor();
  UnsafeMergeFrom(from);
  // @@protoc_insertion_point(copy_constructor:protobuf_unittest.TestMessageWithCustomOptions)
}

void TestMessageWithCustomOptions::SharedCtor() {
  _cached_size_ = 0;
  field1_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  clear_has_AnOneof();
}

TestMessageWithCustomOptions::~TestMessageWithCustomOptions() {
  // @@protoc_insertion_point(destructor:protobuf_unittest.TestMessageWithCustomOptions)
  SharedDtor();
}

void TestMessageWithCustomOptions::SharedDtor() {
  field1_.DestroyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  if (has_AnOneof()) {
    clear_AnOneof();
  }
}

void TestMessageWithCustomOptions::SetCachedSize(int size) const {
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
}
const ::google::protobuf::Descriptor* TestMessageWithCustomOptions::descriptor() {
  protobuf_AssignDescriptorsOnce();
  return TestMessageWithCustomOptions_descriptor_;
}

const TestMessageWithCustomOptions& TestMessageWithCustomOptions::default_instance() {
  protobuf_InitDefaults_google_2fprotobuf_2funittest_5fcustom_5foptions_2eproto();
  return *internal_default_instance();
}

::google::protobuf::internal::ExplicitlyConstructed<TestMessageWithCustomOptions> TestMessageWithCustomOptions_default_instance_;

TestMessageWithCustomOptions* TestMessageWithCustomOptions::New(::google::protobuf::Arena* arena) const {
  TestMessageWithCustomOptions* n = new TestMessageWithCustomOptions;
  if (arena != NULL) {
    arena->Own(n);
  }
  return n;
}

void TestMessageWithCustomOptions::clear_AnOneof() {
// @@protoc_insertion_point(one_of_clear_start:protobuf_unittest.TestMessageWithCustomOptions)
  switch (AnOneof_case()) {
    case kOneofField: {
      // No need to clear
      break;
    }
    case ANONEOF_NOT_SET: {
      break;
    }
  }
  _oneof_case_[0] = ANONEOF_NOT_SET;
}


void TestMessageWithCustomOptions::Clear() {
// @@protoc_insertion_point(message_clear_start:protobuf_unittest.TestMessageWithCustomOptions)
  if (has_field1()) {
    field1_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  }
  clear_AnOneof();
  _has_bits_.Clear();
  if (_internal_metadata_.have_unknown_fields()) {
    mutable_unknown_fields()->Clear();
  }
}

bool TestMessageWithCustomOptions::MergePartialFromCodedStream(
    ::google::protobuf::io::CodedInputStream* input) {
#define DO_(EXPRESSION) if (!GOOGLE_PREDICT_TRUE(EXPRESSION)) goto failure
  ::google::protobuf::uint32 tag;
  // @@protoc_insertion_point(parse_start:protobuf_unittest.TestMessageWithCustomOptions)
  for (;;) {
    ::std::pair< ::google::protobuf::uint32, bool> p = input->ReadTagWithCutoff(127);
    tag = p.first;
    if (!p.second) goto handle_unusual;
    switch (::google::protobuf::internal::WireFormatLite::GetTagFieldNumber(tag)) {
      // optional string field1 = 1 [ctype = CORD];
      case 1: {
        if (tag == 10) {
          DO_(::google::protobuf::internal::WireFormatLite::ReadString(
                input, this->mutable_field1()));
          ::google::protobuf::internal::WireFormat::VerifyUTF8StringNamedField(
            this->field1().data(), this->field1().length(),
            ::google::protobuf::internal::WireFormat::PARSE,
            "protobuf_unittest.TestMessageWithCustomOptions.field1");
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(16)) goto parse_oneof_field;
        break;
      }

      // optional int32 oneof_field = 2;
      case 2: {
        if (tag == 16) {
         parse_oneof_field:
          clear_AnOneof();
          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int32, ::google::protobuf::internal::WireFormatLite::TYPE_INT32>(
                 input, &AnOneof_.oneof_field_)));
          set_has_oneof_field();
        } else {
          goto handle_unusual;
        }
        if (input->ExpectAtEnd()) goto success;
        break;
      }

      default: {
      handle_unusual:
        if (tag == 0 ||
            ::google::protobuf::internal::WireFormatLite::GetTagWireType(tag) ==
            ::google::protobuf::internal::WireFormatLite::WIRETYPE_END_GROUP) {
          goto success;
        }
        DO_(::google::protobuf::internal::WireFormat::SkipField(
              input, tag, mutable_unknown_fields()));
        break;
      }
    }
  }
success:
  // @@protoc_insertion_point(parse_success:protobuf_unittest.TestMessageWithCustomOptions)
  return true;
failure:
  // @@protoc_insertion_point(parse_failure:protobuf_unittest.TestMessageWithCustomOptions)
  return false;
#undef DO_
}

void TestMessageWithCustomOptions::SerializeWithCachedSizes(
    ::google::protobuf::io::CodedOutputStream* output) const {
  // @@protoc_insertion_point(serialize_start:protobuf_unittest.TestMessageWithCustomOptions)
  // optional string field1 = 1 [ctype = CORD];
  if (has_field1()) {
    ::google::protobuf::internal::WireFormat::VerifyUTF8StringNamedField(
      this->field1().data(), this->field1().length(),
      ::google::protobuf::internal::WireFormat::SERIALIZE,
      "protobuf_unittest.TestMessageWithCustomOptions.field1");
    ::google::protobuf::internal::WireFormatLite::WriteStringMaybeAliased(
      1, this->field1(), output);
  }

  // optional int32 oneof_field = 2;
  if (has_oneof_field()) {
    ::google::protobuf::internal::WireFormatLite::WriteInt32(2, this->oneof_field(), output);
  }

  if (_internal_metadata_.have_unknown_fields()) {
    ::google::protobuf::internal::WireFormat::SerializeUnknownFields(
        unknown_fields(), output);
  }
  // @@protoc_insertion_point(serialize_end:protobuf_unittest.TestMessageWithCustomOptions)
}

::google::protobuf::uint8* TestMessageWithCustomOptions::InternalSerializeWithCachedSizesToArray(
    bool deterministic, ::google::protobuf::uint8* target) const {
  (void)deterministic; // Unused
  // @@protoc_insertion_point(serialize_to_array_start:protobuf_unittest.TestMessageWithCustomOptions)
  // optional string field1 = 1 [ctype = CORD];
  if (has_field1()) {
    ::google::protobuf::internal::WireFormat::VerifyUTF8StringNamedField(
      this->field1().data(), this->field1().length(),
      ::google::protobuf::internal::WireFormat::SERIALIZE,
      "protobuf_unittest.TestMessageWithCustomOptions.field1");
    target =
      ::google::protobuf::internal::WireFormatLite::WriteStringToArray(
        1, this->field1(), target);
  }

  // optional int32 oneof_field = 2;
  if (has_oneof_field()) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt32ToArray(2, this->oneof_field(), target);
  }

  if (_internal_metadata_.have_unknown_fields()) {
    target = ::google::protobuf::internal::WireFormat::SerializeUnknownFieldsToArray(
        unknown_fields(), target);
  }
  // @@protoc_insertion_point(serialize_to_array_end:protobuf_unittest.TestMessageWithCustomOptions)
  return target;
}

size_t TestMessageWithCustomOptions::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:protobuf_unittest.TestMessageWithCustomOptions)
  size_t total_size = 0;

  // optional string field1 = 1 [ctype = CORD];
  if (has_field1()) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::StringSize(
        this->field1());
  }

  switch (AnOneof_case()) {
    // optional int32 oneof_field = 2;
    case kOneofField: {
      total_size += 1 +
        ::google::protobuf::internal::WireFormatLite::Int32Size(
          this->oneof_field());
      break;
    }
    case ANONEOF_NOT_SET: {
      break;
    }
  }
  if (_internal_metadata_.have_unknown_fields()) {
    total_size +=
      ::google::protobuf::internal::WireFormat::ComputeUnknownFieldsSize(
        unknown_fields());
  }
  int cached_size = ::google::protobuf::internal::ToCachedSize(total_size);
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = cached_size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
  return total_size;
}

void TestMessageWithCustomOptions::MergeFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_merge_from_start:protobuf_unittest.TestMessageWithCustomOptions)
  if (GOOGLE_PREDICT_FALSE(&from == this)) MergeFromFail(__LINE__);
  const TestMessageWithCustomOptions* source =
      ::google::protobuf::internal::DynamicCastToGenerated<const TestMessageWithCustomOptions>(
          &from);
  if (source == NULL) {
  // @@protoc_insertion_point(generalized_merge_from_cast_fail:protobuf_unittest.TestMessageWithCustomOptions)
    ::google::protobuf::internal::ReflectionOps::Merge(from, this);
  } else {
  // @@protoc_insertion_point(generalized_merge_from_cast_success:protobuf_unittest.TestMessageWithCustomOptions)
    UnsafeMergeFrom(*source);
  }
}

void TestMessageWithCustomOptions::MergeFrom(const TestMessageWithCustomOptions& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:protobuf_unittest.TestMessageWithCustomOptions)
  if (GOOGLE_PREDICT_TRUE(&from != this)) {
    UnsafeMergeFrom(from);
  } else {
    MergeFromFail(__LINE__);
  }
}

void TestMessageWithCustomOptions::UnsafeMergeFrom(const TestMessageWithCustomOptions& from) {
  GOOGLE_DCHECK(&from != this);
  switch (from.AnOneof_case()) {
    case kOneofField: {
      set_oneof_field(from.oneof_field());
      break;
    }
    case ANONEOF_NOT_SET: {
      break;
    }
  }
  if (from._has_bits_[0 / 32] & (0xffu << (0 % 32))) {
    if (from.has_field1()) {
      set_has_field1();
      field1_.AssignWithDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), from.field1_);
    }
  }
  if (from._internal_metadata_.have_unknown_fields()) {
    ::google::protobuf::UnknownFieldSet::MergeToInternalMetdata(
      from.unknown_fields(), &_internal_metadata_);
  }
}

void TestMessageWithCustomOptions::CopyFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_copy_from_start:protobuf_unittest.TestMessageWithCustomOptions)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

void TestMessageWithCustomOptions::CopyFrom(const TestMessageWithCustomOptions& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:protobuf_unittest.TestMessageWithCustomOptions)
  if (&from == this) return;
  Clear();
  UnsafeMergeFrom(from);
}

bool TestMessageWithCustomOptions::IsInitialized() const {

  return true;
}

void TestMessageWithCustomOptions::Swap(TestMessageWithCustomOptions* other) {
  if (other == this) return;
  InternalSwap(other);
}
void TestMessageWithCustomOptions::InternalSwap(TestMessageWithCustomOptions* other) {
  field1_.Swap(&other->field1_);
  std::swap(AnOneof_, other->AnOneof_);
  std::swap(_oneof_case_[0], other->_oneof_case_[0]);
  std::swap(_has_bits_[0], other->_has_bits_[0]);
  _internal_metadata_.Swap(&other->_internal_metadata_);
  std::swap(_cached_size_, other->_cached_size_);
}

::google::protobuf::Metadata TestMessageWithCustomOptions::GetMetadata() const {
  protobuf_AssignDescriptorsOnce();
  ::google::protobuf::Metadata metadata;
  metadata.descriptor = TestMessageWithCustomOptions_descriptor_;
  metadata.reflection = TestMessageWithCustomOptions_reflection_;
  return metadata;
}

#if PROTOBUF_INLINE_NOT_IN_HEADERS
// TestMessageWithCustomOptions

// optional string field1 = 1 [ctype = CORD];
bool TestMessageWithCustomOptions::has_field1() const {
  return (_has_bits_[0] & 0x00000001u) != 0;
}
void TestMessageWithCustomOptions::set_has_field1() {
  _has_bits_[0] |= 0x00000001u;
}
void TestMessageWithCustomOptions::clear_has_field1() {
  _has_bits_[0] &= ~0x00000001u;
}
void TestMessageWithCustomOptions::clear_field1() {
  field1_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  clear_has_field1();
}
const ::std::string& TestMessageWithCustomOptions::field1() const {
  // @@protoc_insertion_point(field_get:protobuf_unittest.TestMessageWithCustomOptions.field1)
  return field1_.GetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
void TestMessageWithCustomOptions::set_field1(const ::std::string& value) {
  set_has_field1();
  field1_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:protobuf_unittest.TestMessageWithCustomOptions.field1)
}
void TestMessageWithCustomOptions::set_field1(const char* value) {
  set_has_field1();
  field1_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:protobuf_unittest.TestMessageWithCustomOptions.field1)
}
void TestMessageWithCustomOptions::set_field1(const char* value, size_t size) {
  set_has_field1();
  field1_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:protobuf_unittest.TestMessageWithCustomOptions.field1)
}
::std::string* TestMessageWithCustomOptions::mutable_field1() {
  set_has_field1();
  // @@protoc_insertion_point(field_mutable:protobuf_unittest.TestMessageWithCustomOptions.field1)
  return field1_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
::std::string* TestMessageWithCustomOptions::release_field1() {
  // @@protoc_insertion_point(field_release:protobuf_unittest.TestMessageWithCustomOptions.field1)
  clear_has_field1();
  return field1_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
void TestMessageWithCustomOptions::set_allocated_field1(::std::string* field1) {
  if (field1 != NULL) {
    set_has_field1();
  } else {
    clear_has_field1();
  }
  field1_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), field1);
  // @@protoc_insertion_point(field_set_allocated:protobuf_unittest.TestMessageWithCustomOptions.field1)
}

// optional int32 oneof_field = 2;
bool TestMessageWithCustomOptions::has_oneof_field() const {
  return AnOneof_case() == kOneofField;
}
void TestMessageWithCustomOptions::set_has_oneof_field() {
  _oneof_case_[0] = kOneofField;
}
void TestMessageWithCustomOptions::clear_oneof_field() {
  if (has_oneof_field()) {
    AnOneof_.oneof_field_ = 0;
    clear_has_AnOneof();
  }
}
::google::protobuf::int32 TestMessageWithCustomOptions::oneof_field() const {
  // @@protoc_insertion_point(field_get:protobuf_unittest.TestMessageWithCustomOptions.oneof_field)
  if (has_oneof_field()) {
    return AnOneof_.oneof_field_;
  }
  return 0;
}
void TestMessageWithCustomOptions::set_oneof_field(::google::protobuf::int32 value) {
  if (!has_oneof_field()) {
    clear_AnOneof();
    set_has_oneof_field();
  }
  AnOneof_.oneof_field_ = value;
  // @@protoc_insertion_point(field_set:protobuf_unittest.TestMessageWithCustomOptions.oneof_field)
}

bool TestMessageWithCustomOptions::has_AnOneof() const {
  return AnOneof_case() != ANONEOF_NOT_SET;
}
void TestMessageWithCustomOptions::clear_has_AnOneof() {
  _oneof_case_[0] = ANONEOF_NOT_SET;
}
TestMessageWithCustomOptions::AnOneofCase TestMessageWithCustomOptions::AnOneof_case() const {
  return TestMessageWithCustomOptions::AnOneofCase(_oneof_case_[0]);
}
inline const TestMessageWithCustomOptions* TestMessageWithCustomOptions::internal_default_instance() {
  return &TestMessageWithCustomOptions_default_instance_.get();
}
#endif  // PROTOBUF_INLINE_NOT_IN_HEADERS

// ===================================================================

#if !defined(_MSC_VER) || _MSC_VER >= 1900
#endif  // !defined(_MSC_VER) || _MSC_VER >= 1900

CustomOptionFooRequest::CustomOptionFooRequest()
  : ::google::protobuf::Message(), _internal_metadata_(NULL) {
  if (this != internal_default_instance()) protobuf_InitDefaults_google_2fprotobuf_2funittest_5fcustom_5foptions_2eproto();
  SharedCtor();
  // @@protoc_insertion_point(constructor:protobuf_unittest.CustomOptionFooRequest)
}

void CustomOptionFooRequest::InitAsDefaultInstance() {
}

CustomOptionFooRequest::CustomOptionFooRequest(const CustomOptionFooRequest& from)
  : ::google::protobuf::Message(),
    _internal_metadata_(NULL) {
  SharedCtor();
  UnsafeMergeFrom(from);
  // @@protoc_insertion_point(copy_constructor:protobuf_unittest.CustomOptionFooRequest)
}

void CustomOptionFooRequest::SharedCtor() {
  _cached_size_ = 0;
}

CustomOptionFooRequest::~CustomOptionFooRequest() {
  // @@protoc_insertion_point(destructor:protobuf_unittest.CustomOptionFooRequest)
  SharedDtor();
}

void CustomOptionFooRequest::SharedDtor() {
}

void CustomOptionFooRequest::SetCachedSize(int size) const {
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
}
const ::google::protobuf::Descriptor* CustomOptionFooRequest::descriptor() {
  protobuf_AssignDescriptorsOnce();
  return CustomOptionFooRequest_descriptor_;
}

const CustomOptionFooRequest& CustomOptionFooRequest::default_instance() {
  protobuf_InitDefaults_google_2fprotobuf_2funittest_5fcustom_5foptions_2eproto();
  return *internal_default_instance();
}

::google::protobuf::internal::ExplicitlyConstructed<CustomOptionFooRequest> CustomOptionFooRequest_default_instance_;

CustomOptionFooRequest* CustomOptionFooRequest::New(::google::protobuf::Arena* arena) const {
  CustomOptionFooRequest* n = new CustomOptionFooRequest;
  if (arena != NULL) {
    arena->Own(n);
  }
  return n;
}

void CustomOptionFooRequest::Clear() {
// @@protoc_insertion_point(message_clear_start:protobuf_unittest.CustomOptionFooRequest)
  _has_bits_.Clear();
  if (_internal_metadata_.have_unknown_fields()) {
    mutable_unknown_fields()->Clear();
  }
}

bool CustomOptionFooRequest::MergePartialFromCodedStream(
    ::google::protobuf::io::CodedInputStream* input) {
#define DO_(EXPRESSION) if (!GOOGLE_PREDICT_TRUE(EXPRESSION)) goto failure
  ::google::protobuf::uint32 tag;
  // @@protoc_insertion_point(parse_start:protobuf_unittest.CustomOptionFooRequest)
  for (;;) {
    ::std::pair< ::google::protobuf::uint32, bool> p = input->ReadTagWithCutoff(127);
    tag = p.first;
    if (!p.second) goto handle_unusual;
  handle_unusual:
    if (tag == 0 ||
        ::google::protobuf::internal::WireFormatLite::GetTagWireType(tag) ==
        ::google::protobuf::internal::WireFormatLite::WIRETYPE_END_GROUP) {
      goto success;
    }
    DO_(::google::protobuf::internal::WireFormat::SkipField(
          input, tag, mutable_unknown_fields()));
  }
success:
  // @@protoc_insertion_point(parse_success:protobuf_unittest.CustomOptionFooRequest)
  return true;
failure:
  // @@protoc_insertion_point(parse_failure:protobuf_unittest.CustomOptionFooRequest)
  return false;
#undef DO_
}

void CustomOptionFooRequest::SerializeWithCachedSizes(
    ::google::protobuf::io::CodedOutputStream* output) const {
  // @@protoc_insertion_point(serialize_start:protobuf_unittest.CustomOptionFooRequest)
  if (_internal_metadata_.have_unknown_fields()) {
    ::google::protobuf::internal::WireFormat::SerializeUnknownFields(
        unknown_fields(), output);
  }
  // @@protoc_insertion_point(serialize_end:protobuf_unittest.CustomOptionFooRequest)
}

::google::protobuf::uint8* CustomOptionFooRequest::InternalSerializeWithCachedSizesToArray(
    bool deterministic, ::google::protobuf::uint8* target) const {
  (void)deterministic; // Unused
  // @@protoc_insertion_point(serialize_to_array_start:protobuf_unittest.CustomOptionFooRequest)
  if (_internal_metadata_.have_unknown_fields()) {
    target = ::google::protobuf::internal::WireFormat::SerializeUnknownFieldsToArray(
        unknown_fields(), target);
  }
  // @@protoc_insertion_point(serialize_to_array_end:protobuf_unittest.CustomOptionFooRequest)
  return target;
}

size_t CustomOptionFooRequest::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:protobuf_unittest.CustomOptionFooRequest)
  size_t total_size = 0;

  if (_internal_metadata_.have_unknown_fields()) {
    total_size +=
      ::google::protobuf::internal::WireFormat::ComputeUnknownFieldsSize(
        unknown_fields());
  }
  int cached_size = ::google::protobuf::internal::ToCachedSize(total_size);
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = cached_size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
  return total_size;
}

void CustomOptionFooRequest::MergeFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_merge_from_start:protobuf_unittest.CustomOptionFooRequest)
  if (GOOGLE_PREDICT_FALSE(&from == this)) MergeFromFail(__LINE__);
  const CustomOptionFooRequest* source =
      ::google::protobuf::internal::DynamicCastToGenerated<const CustomOptionFooRequest>(
          &from);
  if (source == NULL) {
  // @@protoc_insertion_point(generalized_merge_from_cast_fail:protobuf_unittest.CustomOptionFooRequest)
    ::google::protobuf::internal::ReflectionOps::Merge(from, this);
  } else {
  // @@protoc_insertion_point(generalized_merge_from_cast_success:protobuf_unittest.CustomOptionFooRequest)
    UnsafeMergeFrom(*source);
  }
}

void CustomOptionFooRequest::MergeFrom(const CustomOptionFooRequest& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:protobuf_unittest.CustomOptionFooRequest)
  if (GOOGLE_PREDICT_TRUE(&from != this)) {
    UnsafeMergeFrom(from);
  } else {
    MergeFromFail(__LINE__);
  }
}

void CustomOptionFooRequest::UnsafeMergeFrom(const CustomOptionFooRequest& from) {
  GOOGLE_DCHECK(&from != this);
  if (from._internal_metadata_.have_unknown_fields()) {
    ::google::protobuf::UnknownFieldSet::MergeToInternalMetdata(
      from.unknown_fields(), &_internal_metadata_);
  }
}

void CustomOptionFooRequest::CopyFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_copy_from_start:protobuf_unittest.CustomOptionFooRequest)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

void CustomOptionFooRequest::CopyFrom(const CustomOptionFooRequest& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:protobuf_unittest.CustomOptionFooRequest)
  if (&from == this) return;
  Clear();
  UnsafeMergeFrom(from);
}

bool CustomOptionFooRequest::IsInitialized() const {

  return true;
}

void CustomOptionFooRequest::Swap(CustomOptionFooRequest* other) {
  if (other == this) return;
  InternalSwap(other);
}
void CustomOptionFooRequest::InternalSwap(CustomOptionFooRequest* other) {
  _internal_metadata_.Swap(&other->_internal_metadata_);
  std::swap(_cached_size_, other->_cached_size_);
}

::google::protobuf::Metadata CustomOptionFooRequest::GetMetadata() const {
  protobuf_AssignDescriptorsOnce();
  ::google::protobuf::Metadata metadata;
  metadata.descriptor = CustomOptionFooRequest_descriptor_;
  metadata.reflection = CustomOptionFooRequest_reflection_;
  return metadata;
}

#if PROTOBUF_INLINE_NOT_IN_HEADERS
// CustomOptionFooRequest

inline const CustomOptionFooRequest* CustomOptionFooRequest::internal_default_instance() {
  return &CustomOptionFooRequest_default_instance_.get();
}
#endif  // PROTOBUF_INLINE_NOT_IN_HEADERS

// ===================================================================

#if !defined(_MSC_VER) || _MSC_VER >= 1900
#endif  // !defined(_MSC_VER) || _MSC_VER >= 1900

CustomOptionFooResponse::CustomOptionFooResponse()
  : ::google::protobuf::Message(), _internal_metadata_(NULL) {
  if (this != internal_default_instance()) protobuf_InitDefaults_google_2fprotobuf_2funittest_5fcustom_5foptions_2eproto();
  SharedCtor();
  // @@protoc_insertion_point(constructor:protobuf_unittest.CustomOptionFooResponse)
}

void CustomOptionFooResponse::InitAsDefaultInstance() {
}

CustomOptionFooResponse::CustomOptionFooResponse(const CustomOptionFooResponse& from)
  : ::google::protobuf::Message(),
    _internal_metadata_(NULL) {
  SharedCtor();
  UnsafeMergeFrom(from);
  // @@protoc_insertion_point(copy_constructor:protobuf_unittest.CustomOptionFooResponse)
}

void CustomOptionFooResponse::SharedCtor() {
  _cached_size_ = 0;
}

CustomOptionFooResponse::~CustomOptionFooResponse() {
  // @@protoc_insertion_point(destructor:protobuf_unittest.CustomOptionFooResponse)
  SharedDtor();
}

void CustomOptionFooResponse::SharedDtor() {
}

void CustomOptionFooResponse::SetCachedSize(int size) const {
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
}
const ::google::protobuf::Descriptor* CustomOptionFooResponse::descriptor() {
  protobuf_AssignDescriptorsOnce();
  return CustomOptionFooResponse_descriptor_;
}

const CustomOptionFooResponse& CustomOptionFooResponse::default_instance() {
  protobuf_InitDefaults_google_2fprotobuf_2funittest_5fcustom_5foptions_2eproto();
  return *internal_default_instance();
}

::google::protobuf::internal::ExplicitlyConstructed<CustomOptionFooResponse> CustomOptionFooResponse_default_instance_;

CustomOptionFooResponse* CustomOptionFooResponse::New(::google::protobuf::Arena* arena) const {
  CustomOptionFooResponse* n = new CustomOptionFooResponse;
  if (arena != NULL) {
    arena->Own(n);
  }
  return n;
}

void CustomOptionFooResponse::Clear() {
// @@protoc_insertion_point(message_clear_start:protobuf_unittest.CustomOptionFooResponse)
  _has_bits_.Clear();
  if (_internal_metadata_.have_unknown_fields()) {
    mutable_unknown_fields()->Clear();
  }
}

bool CustomOptionFooResponse::MergePartialFromCodedStream(
    ::google::protobuf::io::CodedInputStream* input) {
#define DO_(EXPRESSION) if (!GOOGLE_PREDICT_TRUE(EXPRESSION)) goto failure
  ::google::protobuf::uint32 tag;
  // @@protoc_insertion_point(parse_start:protobuf_unittest.CustomOptionFooResponse)
  for (;;) {
    ::std::pair< ::google::protobuf::uint32, bool> p = input->ReadTagWithCutoff(127);
    tag = p.first;
    if (!p.second) goto handle_unusual;
  handle_unusual:
    if (tag == 0 ||
        ::google::protobuf::internal::WireFormatLite::GetTagWireType(tag) ==
        ::google::protobuf::internal::WireFormatLite::WIRETYPE_END_GROUP) {
      goto success;
    }
    DO_(::google::protobuf::internal::WireFormat::SkipField(
          input, tag, mutable_unknown_fields()));
  }
success:
  // @@protoc_insertion_point(parse_success:protobuf_unittest.CustomOptionFooResponse)
  return true;
failure:
  // @@protoc_insertion_point(parse_failure:protobuf_unittest.CustomOptionFooResponse)
  return false;
#undef DO_
}

void CustomOptionFooResponse::SerializeWithCachedSizes(
    ::google::protobuf::io::CodedOutputStream* output) const {
  // @@protoc_insertion_point(serialize_start:protobuf_unittest.CustomOptionFooResponse)
  if (_internal_metadata_.have_unknown_fields()) {
    ::google::protobuf::internal::WireFormat::SerializeUnknownFields(
        unknown_fields(), output);
  }
  // @@protoc_insertion_point(serialize_end:protobuf_unittest.CustomOptionFooResponse)
}

::google::protobuf::uint8* CustomOptionFooResponse::InternalSerializeWithCachedSizesToArray(
    bool deterministic, ::google::protobuf::uint8* target) const {
  (void)deterministic; // Unused
  // @@protoc_insertion_point(serialize_to_array_start:protobuf_unittest.CustomOptionFooResponse)
  if (_internal_metadata_.have_unknown_fields()) {
    target = ::google::protobuf::internal::WireFormat::SerializeUnknownFieldsToArray(
        unknown_fields(), target);
  }
  // @@protoc_insertion_point(serialize_to_array_end:protobuf_unittest.CustomOptionFooResponse)
  return target;
}

size_t CustomOptionFooResponse::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:protobuf_unittest.CustomOptionFooResponse)
  size_t total_size = 0;

  if (_internal_metadata_.have_unknown_fields()) {
    total_size +=
      ::google::protobuf::internal::WireFormat::ComputeUnknownFieldsSize(
        unknown_fields());
  }
  int cached_size = ::google::protobuf::internal::ToCachedSize(total_size);
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = cached_size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
  return total_size;
}

void CustomOptionFooResponse::MergeFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_merge_from_start:protobuf_unittest.CustomOptionFooResponse)
  if (GOOGLE_PREDICT_FALSE(&from == this)) MergeFromFail(__LINE__);
  const CustomOptionFooResponse* source =
      ::google::protobuf::internal::DynamicCastToGenerated<const CustomOptionFooResponse>(
          &from);
  if (source == NULL) {
  // @@protoc_insertion_point(generalized_merge_from_cast_fail:protobuf_unittest.CustomOptionFooResponse)
    ::google::protobuf::internal::ReflectionOps::Merge(from, this);
  } else {
  // @@protoc_insertion_point(generalized_merge_from_cast_success:protobuf_unittest.CustomOptionFooResponse)
    UnsafeMergeFrom(*source);
  }
}

void CustomOptionFooResponse::MergeFrom(const CustomOptionFooResponse& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:protobuf_unittest.CustomOptionFooResponse)
  if (GOOGLE_PREDICT_TRUE(&from != this)) {
    UnsafeMergeFrom(from);
  } else {
    MergeFromFail(__LINE__);
  }
}

void CustomOptionFooResponse::UnsafeMergeFrom(const CustomOptionFooResponse& from) {
  GOOGLE_DCHECK(&from != this);
  if (from._internal_metadata_.have_unknown_fields()) {
    ::google::protobuf::UnknownFieldSet::MergeToInternalMetdata(
      from.unknown_fields(), &_internal_metadata_);
  }
}

void CustomOptionFooResponse::CopyFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_copy_from_start:protobuf_unittest.CustomOptionFooResponse)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

void CustomOptionFooResponse::CopyFrom(const CustomOptionFooResponse& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:protobuf_unittest.CustomOptionFooResponse)
  if (&from == this) return;
  Clear();
  UnsafeMergeFrom(from);
}

bool CustomOptionFooResponse::IsInitialized() const {

  return true;
}

void CustomOptionFooResponse::Swap(CustomOptionFooResponse* other) {
  if (other == this) return;
  InternalSwap(other);
}
void CustomOptionFooResponse::InternalSwap(CustomOptionFooResponse* other) {
  _internal_metadata_.Swap(&other->_internal_metadata_);
  std::swap(_cached_size_, other->_cached_size_);
}

::google::protobuf::Metadata CustomOptionFooResponse::GetMetadata() const {
  protobuf_AssignDescriptorsOnce();
  ::google::protobuf::Metadata metadata;
  metadata.descriptor = CustomOptionFooResponse_descriptor_;
  metadata.reflection = CustomOptionFooResponse_reflection_;
  return metadata;
}

#if PROTOBUF_INLINE_NOT_IN_HEADERS
// CustomOptionFooResponse

inline const CustomOptionFooResponse* CustomOptionFooResponse::internal_default_instance() {
  return &CustomOptionFooResponse_default_instance_.get();
}
#endif  // PROTOBUF_INLINE_NOT_IN_HEADERS

// ===================================================================

#if !defined(_MSC_VER) || _MSC_VER >= 1900
#endif  // !defined(_MSC_VER) || _MSC_VER >= 1900

CustomOptionFooClientMessage::CustomOptionFooClientMessage()
  : ::google::protobuf::Message(), _internal_metadata_(NULL) {
  if (this != internal_default_instance()) protobuf_InitDefaults_google_2fprotobuf_2funittest_5fcustom_5foptions_2eproto();
  SharedCtor();
  // @@protoc_insertion_point(constructor:protobuf_unittest.CustomOptionFooClientMessage)
}

void CustomOptionFooClientMessage::InitAsDefaultInstance() {
}

CustomOptionFooClientMessage::CustomOptionFooClientMessage(const CustomOptionFooClientMessage& from)
  : ::google::protobuf::Message(),
    _internal_metadata_(NULL) {
  SharedCtor();
  UnsafeMergeFrom(from);
  // @@protoc_insertion_point(copy_constructor:protobuf_unittest.CustomOptionFooClientMessage)
}

void CustomOptionFooClientMessage::SharedCtor() {
  _cached_size_ = 0;
}

CustomOptionFooClientMessage::~CustomOptionFooClientMessage() {
  // @@protoc_insertion_point(destructor:protobuf_unittest.CustomOptionFooClientMessage)
  SharedDtor();
}

void CustomOptionFooClientMessage::SharedDtor() {
}

void CustomOptionFooClientMessage::SetCachedSize(int size) const {
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
}
const ::google::protobuf::Descriptor* CustomOptionFooClientMessage::descriptor() {
  protobuf_AssignDescriptorsOnce();
  return CustomOptionFooClientMessage_descriptor_;
}

const CustomOptionFooClientMessage& CustomOptionFooClientMessage::default_instance() {
  protobuf_InitDefaults_google_2fprotobuf_2funittest_5fcustom_5foptions_2eproto();
  return *internal_default_instance();
}

::google::protobuf::internal::ExplicitlyConstructed<CustomOptionFooClientMessage> CustomOptionFooClientMessage_default_instance_;

CustomOptionFooClientMessage* CustomOptionFooClientMessage::New(::google::protobuf::Arena* arena) const {
  CustomOptionFooClientMessage* n = new CustomOptionFooClientMessage;
  if (arena != NULL) {
    arena->Own(n);
  }
  return n;
}

void CustomOptionFooClientMessage::Clear() {
// @@protoc_insertion_point(message_clear_start:protobuf_unittest.CustomOptionFooClientMessage)
  _has_bits_.Clear();
  if (_internal_metadata_.have_unknown_fields()) {
    mutable_unknown_fields()->Clear();
  }
}

bool CustomOptionFooClientMessage::MergePartialFromCodedStream(
    ::google::protobuf::io::CodedInputStream* input) {
#define DO_(EXPRESSION) if (!GOOGLE_PREDICT_TRUE(EXPRESSION)) goto failure
  ::google::protobuf::uint32 tag;
  // @@protoc_insertion_point(parse_start:protobuf_unittest.CustomOptionFooClientMessage)
  for (;;) {
    ::std::pair< ::google::protobuf::uint32, bool> p = input->ReadTagWithCutoff(127);
    tag = p.first;
    if (!p.second) goto handle_unusual;
  handle_unusual:
    if (tag == 0 ||
        ::google::protobuf::internal::WireFormatLite::GetTagWireType(tag) ==
        ::google::protobuf::internal::WireFormatLite::WIRETYPE_END_GROUP) {
      goto success;
    }
    DO_(::google::protobuf::internal::WireFormat::SkipField(
          input, tag, mutable_unknown_fields()));
  }
success:
  // @@protoc_insertion_point(parse_success:protobuf_unittest.CustomOptionFooClientMessage)
  return true;
failure:
  // @@protoc_insertion_point(parse_failure:protobuf_unittest.CustomOptionFooClientMessage)
  return false;
#undef DO_
}

void CustomOptionFooClientMessage::SerializeWithCachedSizes(
    ::google::protobuf::io::CodedOutputStream* output) const {
  // @@protoc_insertion_point(serialize_start:protobuf_unittest.CustomOptionFooClientMessage)
  if (_internal_metadata_.have_unknown_fields()) {
    ::google::protobuf::internal::WireFormat::SerializeUnknownFields(
        unknown_fields(), output);
  }
  // @@protoc_insertion_point(serialize_end:protobuf_unittest.CustomOptionFooClientMessage)
}

::google::protobuf::uint8* CustomOptionFooClientMessage::InternalSerializeWithCachedSizesToArray(
    bool deterministic, ::google::protobuf::uint8* target) const {
  (void)deterministic; // Unused
  // @@protoc_insertion_point(serialize_to_array_start:protobuf_unittest.CustomOptionFooClientMessage)
  if (_internal_metadata_.have_unknown_fields()) {
    target = ::google::protobuf::internal::WireFormat::SerializeUnknownFieldsToArray(
        unknown_fields(), target);
  }
  // @@protoc_insertion_point(serialize_to_array_end:protobuf_unittest.CustomOptionFooClientMessage)
  return target;
}

size_t CustomOptionFooClientMessage::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:protobuf_unittest.CustomOptionFooClientMessage)
  size_t total_size = 0;

  if (_internal_metadata_.have_unknown_fields()) {
    total_size +=
      ::google::protobuf::internal::WireFormat::ComputeUnknownFieldsSize(
        unknown_fields());
  }
  int cached_size = ::google::protobuf::internal::ToCachedSize(total_size);
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = cached_size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
  return total_size;
}

void CustomOptionFooClientMessage::MergeFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_merge_from_start:protobuf_unittest.CustomOptionFooClientMessage)
  if (GOOGLE_PREDICT_FALSE(&from == this)) MergeFromFail(__LINE__);
  const CustomOptionFooClientMessage* source =
      ::google::protobuf::internal::DynamicCastToGenerated<const CustomOptionFooClientMessage>(
          &from);
  if (source == NULL) {
  // @@protoc_insertion_point(generalized_merge_from_cast_fail:protobuf_unittest.CustomOptionFooClientMessage)
    ::google::protobuf::internal::ReflectionOps::Merge(from, this);
  } else {
  // @@protoc_insertion_point(generalized_merge_from_cast_success:protobuf_unittest.CustomOptionFooClientMessage)
    UnsafeMergeFrom(*source);
  }
}

void CustomOptionFooClientMessage::MergeFrom(const CustomOptionFooClientMessage& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:protobuf_unittest.CustomOptionFooClientMessage)
  if (GOOGLE_PREDICT_TRUE(&from != this)) {
    UnsafeMergeFrom(from);
  } else {
    MergeFromFail(__LINE__);
  }
}

void CustomOptionFooClientMessage::UnsafeMergeFrom(const CustomOptionFooClientMessage& from) {
  GOOGLE_DCHECK(&from != this);
  if (from._internal_metadata_.have_unknown_fields()) {
    ::google::protobuf::UnknownFieldSet::MergeToInternalMetdata(
      from.unknown_fields(), &_internal_metadata_);
  }
}

void CustomOptionFooClientMessage::CopyFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_copy_from_start:protobuf_unittest.CustomOptionFooClientMessage)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

void CustomOptionFooClientMessage::CopyFrom(const CustomOptionFooClientMessage& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:protobuf_unittest.CustomOptionFooClientMessage)
  if (&from == this) return;
  Clear();
  UnsafeMergeFrom(from);
}

bool CustomOptionFooClientMessage::IsInitialized() const {

  return true;
}

void CustomOptionFooClientMessage::Swap(CustomOptionFooClientMessage* other) {
  if (other == this) return;
  InternalSwap(other);
}
void CustomOptionFooClientMessage::InternalSwap(CustomOptionFooClientMessage* other) {
  _internal_metadata_.Swap(&other->_internal_metadata_);
  std::swap(_cached_size_, other->_cached_size_);
}

::google::protobuf::Metadata CustomOptionFooClientMessage::GetMetadata() const {
  protobuf_AssignDescriptorsOnce();
  ::google::protobuf::Metadata metadata;
  metadata.descriptor = CustomOptionFooClientMessage_descriptor_;
  metadata.reflection = CustomOptionFooClientMessage_reflection_;
  return metadata;
}

#if PROTOBUF_INLINE_NOT_IN_HEADERS
// CustomOptionFooClientMessage

inline const CustomOptionFooClientMessage* CustomOptionFooClientMessage::internal_default_instance() {
  return &CustomOptionFooClientMessage_default_instance_.get();
}
#endif  // PROTOBUF_INLINE_NOT_IN_HEADERS

// ===================================================================

#if !defined(_MSC_VER) || _MSC_VER >= 1900
#endif  // !defined(_MSC_VER) || _MSC_VER >= 1900

CustomOptionFooServerMessage::CustomOptionFooServerMessage()
  : ::google::protobuf::Message(), _internal_metadata_(NULL) {
  if (this != internal_default_instance()) protobuf_InitDefaults_google_2fprotobuf_2funittest_5fcustom_5foptions_2eproto();
  SharedCtor();
  // @@protoc_insertion_point(constructor:protobuf_unittest.CustomOptionFooServerMessage)
}

void CustomOptionFooServerMessage::InitAsDefaultInstance() {
}

CustomOptionFooServerMessage::CustomOptionFooServerMessage(const CustomOptionFooServerMessage& from)
  : ::google::protobuf::Message(),
    _internal_metadata_(NULL) {
  SharedCtor();
  UnsafeMergeFrom(from);
  // @@protoc_insertion_point(copy_constructor:protobuf_unittest.CustomOptionFooServerMessage)
}

void CustomOptionFooServerMessage::SharedCtor() {
  _cached_size_ = 0;
}

CustomOptionFooServerMessage::~CustomOptionFooServerMessage() {
  // @@protoc_insertion_point(destructor:protobuf_unittest.CustomOptionFooServerMessage)
  SharedDtor();
}

void CustomOptionFooServerMessage::SharedDtor() {
}

void CustomOptionFooServerMessage::SetCachedSize(int size) const {
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
}
const ::google::protobuf::Descriptor* CustomOptionFooServerMessage::descriptor() {
  protobuf_AssignDescriptorsOnce();
  return CustomOptionFooServerMessage_descriptor_;
}

const CustomOptionFooServerMessage& CustomOptionFooServerMessage::default_instance() {
  protobuf_InitDefaults_google_2fprotobuf_2funittest_5fcustom_5foptions_2eproto();
  return *internal_default_instance();
}

::google::protobuf::internal::ExplicitlyConstructed<CustomOptionFooServerMessage> CustomOptionFooServerMessage_default_instance_;

CustomOptionFooServerMessage* CustomOptionFooServerMessage::New(::google::protobuf::Arena* arena) const {
  CustomOptionFooServerMessage* n = new CustomOptionFooServerMessage;
  if (arena != NULL) {
    arena->Own(n);
  }
  return n;
}

void CustomOptionFooServerMessage::Clear() {
// @@protoc_insertion_point(message_clear_start:protobuf_unittest.CustomOptionFooServerMessage)
  _has_bits_.Clear();
  if (_internal_metadata_.have_unknown_fields()) {
    mutable_unknown_fields()->Clear();
  }
}

bool CustomOptionFooServerMessage::MergePartialFromCodedStream(
    ::google::protobuf::io::CodedInputStream* input) {
#define DO_(EXPRESSION) if (!GOOGLE_PREDICT_TRUE(EXPRESSION)) goto failure
  ::google::protobuf::uint32 tag;
  // @@protoc_insertion_point(parse_start:protobuf_unittest.CustomOptionFooServerMessage)
  for (;;) {
    ::std::pair< ::google::protobuf::uint32, bool> p = input->ReadTagWithCutoff(127);
    tag = p.first;
    if (!p.second) goto handle_unusual;
  handle_unusual:
    if (tag == 0 ||
        ::google::protobuf::internal::WireFormatLite::GetTagWireType(tag) ==
        ::google::protobuf::internal::WireFormatLite::WIRETYPE_END_GROUP) {
      goto success;
    }
    DO_(::google::protobuf::internal::WireFormat::SkipField(
          input, tag, mutable_unknown_fields()));
  }
success:
  // @@protoc_insertion_point(parse_success:protobuf_unittest.CustomOptionFooServerMessage)
  return true;
failure:
  // @@protoc_insertion_point(parse_failure:protobuf_unittest.CustomOptionFooServerMessage)
  return false;
#undef DO_
}

void CustomOptionFooServerMessage::SerializeWithCachedSizes(
    ::google::protobuf::io::CodedOutputStream* output) const {
  // @@protoc_insertion_point(serialize_start:protobuf_unittest.CustomOptionFooServerMessage)
  if (_internal_metadata_.have_unknown_fields()) {
    ::google::protobuf::internal::WireFormat::SerializeUnknownFields(
        unknown_fields(), output);
  }
  // @@protoc_insertion_point(serialize_end:protobuf_unittest.CustomOptionFooServerMessage)
}

::google::protobuf::uint8* CustomOptionFooServerMessage::InternalSerializeWithCachedSizesToArray(
    bool deterministic, ::google::protobuf::uint8* target) const {
  (void)deterministic; // Unused
  // @@protoc_insertion_point(serialize_to_array_start:protobuf_unittest.CustomOptionFooServerMessage)
  if (_internal_metadata_.have_unknown_fields()) {
    target = ::google::protobuf::internal::WireFormat::SerializeUnknownFieldsToArray(
        unknown_fields(), target);
  }
  // @@protoc_insertion_point(serialize_to_array_end:protobuf_unittest.CustomOptionFooServerMessage)
  return target;
}

size_t CustomOptionFooServerMessage::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:protobuf_unittest.CustomOptionFooServerMessage)
  size_t total_size = 0;

  if (_internal_metadata_.have_unknown_fields()) {
    total_size +=
      ::google::protobuf::internal::WireFormat::ComputeUnknownFieldsSize(
        unknown_fields());
  }
  int cached_size = ::google::protobuf::internal::ToCachedSize(total_size);
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = cached_size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
  return total_size;
}

void CustomOptionFooServerMessage::MergeFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_merge_from_start:protobuf_unittest.CustomOptionFooServerMessage)
  if (GOOGLE_PREDICT_FALSE(&from == this)) MergeFromFail(__LINE__);
  const CustomOptionFooServerMessage* source =
      ::google::protobuf::internal::DynamicCastToGenerated<const CustomOptionFooServerMessage>(
          &from);
  if (source == NULL) {
  // @@protoc_insertion_point(generalized_merge_from_cast_fail:protobuf_unittest.CustomOptionFooServerMessage)
    ::google::protobuf::internal::ReflectionOps::Merge(from, this);
  } else {
  // @@protoc_insertion_point(generalized_merge_from_cast_success:protobuf_unittest.CustomOptionFooServerMessage)
    UnsafeMergeFrom(*source);
  }
}

void CustomOptionFooServerMessage::MergeFrom(const CustomOptionFooServerMessage& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:protobuf_unittest.CustomOptionFooServerMessage)
  if (GOOGLE_PREDICT_TRUE(&from != this)) {
    UnsafeMergeFrom(from);
  } else {
    MergeFromFail(__LINE__);
  }
}

void CustomOptionFooServerMessage::UnsafeMergeFrom(const CustomOptionFooServerMessage& from) {
  GOOGLE_DCHECK(&from != this);
  if (from._internal_metadata_.have_unknown_fields()) {
    ::google::protobuf::UnknownFieldSet::MergeToInternalMetdata(
      from.unknown_fields(), &_internal_metadata_);
  }
}

void CustomOptionFooServerMessage::CopyFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_copy_from_start:protobuf_unittest.CustomOptionFooServerMessage)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

void CustomOptionFooServerMessage::CopyFrom(const CustomOptionFooServerMessage& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:protobuf_unittest.CustomOptionFooServerMessage)
  if (&from == this) return;
  Clear();
  UnsafeMergeFrom(from);
}

bool CustomOptionFooServerMessage::IsInitialized() const {

  return true;
}

void CustomOptionFooServerMessage::Swap(CustomOptionFooServerMessage* other) {
  if (other == this) return;
  InternalSwap(other);
}
void CustomOptionFooServerMessage::InternalSwap(CustomOptionFooServerMessage* other) {
  _internal_metadata_.Swap(&other->_internal_metadata_);
  std::swap(_cached_size_, other->_cached_size_);
}

::google::protobuf::Metadata CustomOptionFooServerMessage::GetMetadata() const {
  protobuf_AssignDescriptorsOnce();
  ::google::protobuf::Metadata metadata;
  metadata.descriptor = CustomOptionFooServerMessage_descriptor_;
  metadata.reflection = CustomOptionFooServerMessage_reflection_;
  return metadata;
}

#if PROTOBUF_INLINE_NOT_IN_HEADERS
// CustomOptionFooServerMessage

inline const CustomOptionFooServerMessage* CustomOptionFooServerMessage::internal_default_instance() {
  return &CustomOptionFooServerMessage_default_instance_.get();
}
#endif  // PROTOBUF_INLINE_NOT_IN_HEADERS

// ===================================================================

const ::google::protobuf::EnumDescriptor* DummyMessageContainingEnum_TestEnumType_descriptor() {
  protobuf_AssignDescriptorsOnce();
  return DummyMessageContainingEnum_TestEnumType_descriptor_;
}
bool DummyMessageContainingEnum_TestEnumType_IsValid(int value) {
  switch (value) {
    case -23:
    case 22:
      return true;
    default:
      return false;
  }
}

#if !defined(_MSC_VER) || _MSC_VER >= 1900
const DummyMessageContainingEnum_TestEnumType DummyMessageContainingEnum::TEST_OPTION_ENUM_TYPE1;
const DummyMessageContainingEnum_TestEnumType DummyMessageContainingEnum::TEST_OPTION_ENUM_TYPE2;
const DummyMessageContainingEnum_TestEnumType DummyMessageContainingEnum::TestEnumType_MIN;
const DummyMessageContainingEnum_TestEnumType DummyMessageContainingEnum::TestEnumType_MAX;
const int DummyMessageContainingEnum::TestEnumType_ARRAYSIZE;
#endif  // !defined(_MSC_VER) || _MSC_VER >= 1900
#if !defined(_MSC_VER) || _MSC_VER >= 1900
#endif  // !defined(_MSC_VER) || _MSC_VER >= 1900

DummyMessageContainingEnum::DummyMessageContainingEnum()
  : ::google::protobuf::Message(), _internal_metadata_(NULL) {
  if (this != internal_default_instance()) protobuf_InitDefaults_google_2fprotobuf_2funittest_5fcustom_5foptions_2eproto();
  SharedCtor();
  // @@protoc_insertion_point(constructor:protobuf_unittest.DummyMessageContainingEnum)
}

void DummyMessageContainingEnum::InitAsDefaultInstance() {
}

DummyMessageContainingEnum::DummyMessageContainingEnum(const DummyMessageContainingEnum& from)
  : ::google::protobuf::Message(),
    _internal_metadata_(NULL) {
  SharedCtor();
  UnsafeMergeFrom(from);
  // @@protoc_insertion_point(copy_constructor:protobuf_unittest.DummyMessageContainingEnum)
}

void DummyMessageContainingEnum::SharedCtor() {
  _cached_size_ = 0;
}

DummyMessageContainingEnum::~DummyMessageContainingEnum() {
  // @@protoc_insertion_point(destructor:protobuf_unittest.DummyMessageContainingEnum)
  SharedDtor();
}

void DummyMessageContainingEnum::SharedDtor() {
}

void DummyMessageContainingEnum::SetCachedSize(int size) const {
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
}
const ::google::protobuf::Descriptor* DummyMessageContainingEnum::descriptor() {
  protobuf_AssignDescriptorsOnce();
  return DummyMessageContainingEnum_descriptor_;
}

const DummyMessageContainingEnum& DummyMessageContainingEnum::default_instance() {
  protobuf_InitDefaults_google_2fprotobuf_2funittest_5fcustom_5foptions_2eproto();
  return *internal_default_instance();
}

::google::protobuf::internal::ExplicitlyConstructed<DummyMessageContainingEnum> DummyMessageContainingEnum_default_instance_;

DummyMessageContainingEnum* DummyMessageContainingEnum::New(::google::protobuf::Arena* arena) const {
  DummyMessageContainingEnum* n = new DummyMessageContainingEnum;
  if (arena != NULL) {
    arena->Own(n);
  }
  return n;
}

void DummyMessageContainingEnum::Clear() {
// @@protoc_insertion_point(message_clear_start:protobuf_unittest.DummyMessageContainingEnum)
  _has_bits_.Clear();
  if (_internal_metadata_.have_unknown_fields()) {
    mutable_unknown_fields()->Clear();
  }
}

bool DummyMessageContainingEnum::MergePartialFromCodedStream(
    ::google::protobuf::io::CodedInputStream* input) {
#define DO_(EXPRESSION) if (!GOOGLE_PREDICT_TRUE(EXPRESSION)) goto failure
  ::google::protobuf::uint32 tag;
  // @@protoc_insertion_point(parse_start:protobuf_unittest.DummyMessageContainingEnum)
  for (;;) {
    ::std::pair< ::google::protobuf::uint32, bool> p = input->ReadTagWithCutoff(127);
    tag = p.first;
    if (!p.second) goto handle_unusual;
  handle_unusual:
    if (tag == 0 ||
        ::google::protobuf::internal::WireFormatLite::GetTagWireType(tag) ==
        ::google::protobuf::internal::WireFormatLite::WIRETYPE_END_GROUP) {
      goto success;
    }
    DO_(::google::protobuf::internal::WireFormat::SkipField(
          input, tag, mutable_unknown_fields()));
  }
success:
  // @@protoc_insertion_point(parse_success:protobuf_unittest.DummyMessageContainingEnum)
  return true;
failure:
  // @@protoc_insertion_point(parse_failure:protobuf_unittest.DummyMessageContainingEnum)
  return false;
#undef DO_
}

void DummyMessageContainingEnum::SerializeWithCachedSizes(
    ::google::protobuf::io::CodedOutputStream* output) const {
  // @@protoc_insertion_point(serialize_start:protobuf_unittest.DummyMessageContainingEnum)
  if (_internal_metadata_.have_unknown_fields()) {
    ::google::protobuf::internal::WireFormat::SerializeUnknownFields(
        unknown_fields(), output);
  }
  // @@protoc_insertion_point(serialize_end:protobuf_unittest.DummyMessageContainingEnum)
}

::google::protobuf::uint8* DummyMessageContainingEnum::InternalSerializeWithCachedSizesToArray(
    bool deterministic, ::google::protobuf::uint8* target) const {
  (void)deterministic; // Unused
  // @@protoc_insertion_point(serialize_to_array_start:protobuf_unittest.DummyMessageContainingEnum)
  if (_internal_metadata_.have_unknown_fields()) {
    target = ::google::protobuf::internal::WireFormat::SerializeUnknownFieldsToArray(
        unknown_fields(), target);
  }
  // @@protoc_insertion_point(serialize_to_array_end:protobuf_unittest.DummyMessageContainingEnum)
  return target;
}

size_t DummyMessageContainingEnum::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:protobuf_unittest.DummyMessageContainingEnum)
  size_t total_size = 0;

  if (_internal_metadata_.have_unknown_fields()) {
    total_size +=
      ::google::protobuf::internal::WireFormat::ComputeUnknownFieldsSize(
        unknown_fields());
  }
  int cached_size = ::google::protobuf::internal::ToCachedSize(total_size);
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = cached_size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
  return total_size;
}

void DummyMessageContainingEnum::MergeFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_merge_from_start:protobuf_unittest.DummyMessageContainingEnum)
  if (GOOGLE_PREDICT_FALSE(&from == this)) MergeFromFail(__LINE__);
  const DummyMessageContainingEnum* source =
      ::google::protobuf::internal::DynamicCastToGenerated<const DummyMessageContainingEnum>(
          &from);
  if (source == NULL) {
  // @@protoc_insertion_point(generalized_merge_from_cast_fail:protobuf_unittest.DummyMessageContainingEnum)
    ::google::protobuf::internal::ReflectionOps::Merge(from, this);
  } else {
  // @@protoc_insertion_point(generalized_merge_from_cast_success:protobuf_unittest.DummyMessageContainingEnum)
    UnsafeMergeFrom(*source);
  }
}

void DummyMessageContainingEnum::MergeFrom(const DummyMessageContainingEnum& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:protobuf_unittest.DummyMessageContainingEnum)
  if (GOOGLE_PREDICT_TRUE(&from != this)) {
    UnsafeMergeFrom(from);
  } else {
    MergeFromFail(__LINE__);
  }
}

void DummyMessageContainingEnum::UnsafeMergeFrom(const DummyMessageContainingEnum& from) {
  GOOGLE_DCHECK(&from != this);
  if (from._internal_metadata_.have_unknown_fields()) {
    ::google::protobuf::UnknownFieldSet::MergeToInternalMetdata(
      from.unknown_fields(), &_internal_metadata_);
  }
}

void DummyMessageContainingEnum::CopyFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_copy_from_start:protobuf_unittest.DummyMessageContainingEnum)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

void DummyMessageContainingEnum::CopyFrom(const DummyMessageContainingEnum& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:protobuf_unittest.DummyMessageContainingEnum)
  if (&from == this) return;
  Clear();
  UnsafeMergeFrom(from);
}

bool DummyMessageContainingEnum::IsInitialized() const {

  return true;
}

void DummyMessageContainingEnum::Swap(DummyMessageContainingEnum* other) {
  if (other == this) return;
  InternalSwap(other);
}
void DummyMessageContainingEnum::InternalSwap(DummyMessageContainingEnum* other) {
  _internal_metadata_.Swap(&other->_internal_metadata_);
  std::swap(_cached_size_, other->_cached_size_);
}

::google::protobuf::Metadata DummyMessageContainingEnum::GetMetadata() const {
  protobuf_AssignDescriptorsOnce();
  ::google::protobuf::Metadata metadata;
  metadata.descriptor = DummyMessageContainingEnum_descriptor_;
  metadata.reflection = DummyMessageContainingEnum_reflection_;
  return metadata;
}

#if PROTOBUF_INLINE_NOT_IN_HEADERS
// DummyMessageContainingEnum

inline const DummyMessageContainingEnum* DummyMessageContainingEnum::internal_default_instance() {
  return &DummyMessageContainingEnum_default_instance_.get();
}
#endif  // PROTOBUF_INLINE_NOT_IN_HEADERS

// ===================================================================

#if !defined(_MSC_VER) || _MSC_VER >= 1900
#endif  // !defined(_MSC_VER) || _MSC_VER >= 1900

DummyMessageInvalidAsOptionType::DummyMessageInvalidAsOptionType()
  : ::google::protobuf::Message(), _internal_metadata_(NULL) {
  if (this != internal_default_instance()) protobuf_InitDefaults_google_2fprotobuf_2funittest_5fcustom_5foptions_2eproto();
  SharedCtor();
  // @@protoc_insertion_point(constructor:protobuf_unittest.DummyMessageInvalidAsOptionType)
}

void DummyMessageInvalidAsOptionType::InitAsDefaultInstance() {
}

DummyMessageInvalidAsOptionType::DummyMessageInvalidAsOptionType(const DummyMessageInvalidAsOptionType& from)
  : ::google::protobuf::Message(),
    _internal_metadata_(NULL) {
  SharedCtor();
  UnsafeMergeFrom(from);
  // @@protoc_insertion_point(copy_constructor:protobuf_unittest.DummyMessageInvalidAsOptionType)
}

void DummyMessageInvalidAsOptionType::SharedCtor() {
  _cached_size_ = 0;
}

DummyMessageInvalidAsOptionType::~DummyMessageInvalidAsOptionType() {
  // @@protoc_insertion_point(destructor:protobuf_unittest.DummyMessageInvalidAsOptionType)
  SharedDtor();
}

void DummyMessageInvalidAsOptionType::SharedDtor() {
}

void DummyMessageInvalidAsOptionType::SetCachedSize(int size) const {
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
}
const ::google::protobuf::Descriptor* DummyMessageInvalidAsOptionType::descriptor() {
  protobuf_AssignDescriptorsOnce();
  return DummyMessageInvalidAsOptionType_descriptor_;
}

const DummyMessageInvalidAsOptionType& DummyMessageInvalidAsOptionType::default_instance() {
  protobuf_InitDefaults_google_2fprotobuf_2funittest_5fcustom_5foptions_2eproto();
  return *internal_default_instance();
}

::google::protobuf::internal::ExplicitlyConstructed<DummyMessageInvalidAsOptionType> DummyMessageInvalidAsOptionType_default_instance_;

DummyMessageInvalidAsOptionType* DummyMessageInvalidAsOptionType::New(::google::protobuf::Arena* arena) const {
  DummyMessageInvalidAsOptionType* n = new DummyMessageInvalidAsOptionType;
  if (arena != NULL) {
    arena->Own(n);
  }
  return n;
}

void DummyMessageInvalidAsOptionType::Clear() {
// @@protoc_insertion_point(message_clear_start:protobuf_unittest.DummyMessageInvalidAsOptionType)
  _has_bits_.Clear();
  if (_internal_metadata_.have_unknown_fields()) {
    mutable_unknown_fields()->Clear();
  }
}

bool DummyMessageInvalidAsOptionType::MergePartialFromCodedStream(
    ::google::protobuf::io::CodedInputStream* input) {
#define DO_(EXPRESSION) if (!GOOGLE_PREDICT_TRUE(EXPRESSION)) goto failure
  ::google::protobuf::uint32 tag;
  // @@protoc_insertion_point(parse_start:protobuf_unittest.DummyMessageInvalidAsOptionType)
  for (;;) {
    ::std::pair< ::google::protobuf::uint32, bool> p = input->ReadTagWithCutoff(127);
    tag = p.first;
    if (!p.second) goto handle_unusual;
  handle_unusual:
    if (tag == 0 ||
        ::google::protobuf::internal::WireFormatLite::GetTagWireType(tag) ==
        ::google::protobuf::internal::WireFormatLite::WIRETYPE_END_GROUP) {
      goto success;
    }
    DO_(::google::protobuf::internal::WireFormat::SkipField(
          input, tag, mutable_unknown_fields()));
  }
success:
  // @@protoc_insertion_point(parse_success:protobuf_unittest.DummyMessageInvalidAsOptionType)
  return true;
failure:
  // @@protoc_insertion_point(parse_failure:protobuf_unittest.DummyMessageInvalidAsOptionType)
  return false;
#undef DO_
}

void DummyMessageInvalidAsOptionType::SerializeWithCachedSizes(
    ::google::protobuf::io::CodedOutputStream* output) const {
  // @@protoc_insertion_point(serialize_start:protobuf_unittest.DummyMessageInvalidAsOptionType)
  if (_internal_metadata_.have_unknown_fields()) {
    ::google::protobuf::internal::WireFormat::SerializeUnknownFields(
        unknown_fields(), output);
  }
  // @@protoc_insertion_point(serialize_end:protobuf_unittest.DummyMessageInvalidAsOptionType)
}

::google::protobuf::uint8* DummyMessageInvalidAsOptionType::InternalSerializeWithCachedSizesToArray(
    bool deterministic, ::google::protobuf::uint8* target) const {
  (void)deterministic; // Unused
  // @@protoc_insertion_point(serialize_to_array_start:protobuf_unittest.DummyMessageInvalidAsOptionType)
  if (_internal_metadata_.have_unknown_fields()) {
    target = ::google::protobuf::internal::WireFormat::SerializeUnknownFieldsToArray(
        unknown_fields(), target);
  }
  // @@protoc_insertion_point(serialize_to_array_end:protobuf_unittest.DummyMessageInvalidAsOptionType)
  return target;
}

size_t DummyMessageInvalidAsOptionType::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:protobuf_unittest.DummyMessageInvalidAsOptionType)
  size_t total_size = 0;

  if (_internal_metadata_.have_unknown_fields()) {
    total_size +=
      ::google::protobuf::internal::WireFormat::ComputeUnknownFieldsSize(
        unknown_fields());
  }
  int cached_size = ::google::protobuf::internal::ToCachedSize(total_size);
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = cached_size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
  return total_size;
}

void DummyMessageInvalidAsOptionType::MergeFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_merge_from_start:protobuf_unittest.DummyMessageInvalidAsOptionType)
  if (GOOGLE_PREDICT_FALSE(&from == this)) MergeFromFail(__LINE__);
  const DummyMessageInvalidAsOptionType* source =
      ::google::protobuf::internal::DynamicCastToGenerated<const DummyMessageInvalidAsOptionType>(
          &from);
  if (source == NULL) {
  // @@protoc_insertion_point(generalized_merge_from_cast_fail:protobuf_unittest.DummyMessageInvalidAsOptionType)
    ::google::protobuf::internal::ReflectionOps::Merge(from, this);
  } else {
  // @@protoc_insertion_point(generalized_merge_from_cast_success:protobuf_unittest.DummyMessageInvalidAsOptionType)
    UnsafeMergeFrom(*source);
  }
}

void DummyMessageInvalidAsOptionType::MergeFrom(const DummyMessageInvalidAsOptionType& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:protobuf_unittest.DummyMessageInvalidAsOptionType)
  if (GOOGLE_PREDICT_TRUE(&from != this)) {
    UnsafeMergeFrom(from);
  } else {
    MergeFromFail(__LINE__);
  }
}

void DummyMessageInvalidAsOptionType::UnsafeMergeFrom(const DummyMessageInvalidAsOptionType& from) {
  GOOGLE_DCHECK(&from != this);
  if (from._internal_metadata_.have_unknown_fields()) {
    ::google::protobuf::UnknownFieldSet::MergeToInternalMetdata(
      from.unknown_fields(), &_internal_metadata_);
  }
}

void DummyMessageInvalidAsOptionType::CopyFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_copy_from_start:protobuf_unittest.DummyMessageInvalidAsOptionType)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

void DummyMessageInvalidAsOptionType::CopyFrom(const DummyMessageInvalidAsOptionType& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:protobuf_unittest.DummyMessageInvalidAsOptionType)
  if (&from == this) return;
  Clear();
  UnsafeMergeFrom(from);
}

bool DummyMessageInvalidAsOptionType::IsInitialized() const {

  return true;
}

void DummyMessageInvalidAsOptionType::Swap(DummyMessageInvalidAsOptionType* other) {
  if (other == this) return;
  InternalSwap(other);
}
void DummyMessageInvalidAsOptionType::InternalSwap(DummyMessageInvalidAsOptionType* other) {
  _internal_metadata_.Swap(&other->_internal_metadata_);
  std::swap(_cached_size_, other->_cached_size_);
}

::google::protobuf::Metadata DummyMessageInvalidAsOptionType::GetMetadata() const {
  protobuf_AssignDescriptorsOnce();
  ::google::protobuf::Metadata metadata;
  metadata.descriptor = DummyMessageInvalidAsOptionType_descriptor_;
  metadata.reflection = DummyMessageInvalidAsOptionType_reflection_;
  return metadata;
}

#if PROTOBUF_INLINE_NOT_IN_HEADERS
// DummyMessageInvalidAsOptionType

inline const DummyMessageInvalidAsOptionType* DummyMessageInvalidAsOptionType::internal_default_instance() {
  return &DummyMessageInvalidAsOptionType_default_instance_.get();
}
#endif  // PROTOBUF_INLINE_NOT_IN_HEADERS

// ===================================================================

#if !defined(_MSC_VER) || _MSC_VER >= 1900
#endif  // !defined(_MSC_VER) || _MSC_VER >= 1900

CustomOptionMinIntegerValues::CustomOptionMinIntegerValues()
  : ::google::protobuf::Message(), _internal_metadata_(NULL) {
  if (this != internal_default_instance()) protobuf_InitDefaults_google_2fprotobuf_2funittest_5fcustom_5foptions_2eproto();
  SharedCtor();
  // @@protoc_insertion_point(constructor:protobuf_unittest.CustomOptionMinIntegerValues)
}

void CustomOptionMinIntegerValues::InitAsDefaultInstance() {
}

CustomOptionMinIntegerValues::CustomOptionMinIntegerValues(const CustomOptionMinIntegerValues& from)
  : ::google::protobuf::Message(),
    _internal_metadata_(NULL) {
  SharedCtor();
  UnsafeMergeFrom(from);
  // @@protoc_insertion_point(copy_constructor:protobuf_unittest.CustomOptionMinIntegerValues)
}

void CustomOptionMinIntegerValues::SharedCtor() {
  _cached_size_ = 0;
}

CustomOptionMinIntegerValues::~CustomOptionMinIntegerValues() {
  // @@protoc_insertion_point(destructor:protobuf_unittest.CustomOptionMinIntegerValues)
  SharedDtor();
}

void CustomOptionMinIntegerValues::SharedDtor() {
}

void CustomOptionMinIntegerValues::SetCachedSize(int size) const {
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
}
const ::google::protobuf::Descriptor* CustomOptionMinIntegerValues::descriptor() {
  protobuf_AssignDescriptorsOnce();
  return CustomOptionMinIntegerValues_descriptor_;
}

const CustomOptionMinIntegerValues& CustomOptionMinIntegerValues::default_instance() {
  protobuf_InitDefaults_google_2fprotobuf_2funittest_5fcustom_5foptions_2eproto();
  return *internal_default_instance();
}

::google::protobuf::internal::ExplicitlyConstructed<CustomOptionMinIntegerValues> CustomOptionMinIntegerValues_default_instance_;

CustomOptionMinIntegerValues* CustomOptionMinIntegerValues::New(::google::protobuf::Arena* arena) const {
  CustomOptionMinIntegerValues* n = new CustomOptionMinIntegerValues;
  if (arena != NULL) {
    arena->Own(n);
  }
  return n;
}

void CustomOptionMinIntegerValues::Clear() {
// @@protoc_insertion_point(message_clear_start:protobuf_unittest.CustomOptionMinIntegerValues)
  _has_bits_.Clear();
  if (_internal_metadata_.have_unknown_fields()) {
    mutable_unknown_fields()->Clear();
  }
}

bool CustomOptionMinIntegerValues::MergePartialFromCodedStream(
    ::google::protobuf::io::CodedInputStream* input) {
#define DO_(EXPRESSION) if (!GOOGLE_PREDICT_TRUE(EXPRESSION)) goto failure
  ::google::protobuf::uint32 tag;
  // @@protoc_insertion_point(parse_start:protobuf_unittest.CustomOptionMinIntegerValues)
  for (;;) {
    ::std::pair< ::google::protobuf::uint32, bool> p = input->ReadTagWithCutoff(127);
    tag = p.first;
    if (!p.second) goto handle_unusual;
  handle_unusual:
    if (tag == 0 ||
        ::google::protobuf::internal::WireFormatLite::GetTagWireType(tag) ==
        ::google::protobuf::internal::WireFormatLite::WIRETYPE_END_GROUP) {
      goto success;
    }
    DO_(::google::protobuf::internal::WireFormat::SkipField(
          input, tag, mutable_unknown_fields()));
  }
success:
  // @@protoc_insertion_point(parse_success:protobuf_unittest.CustomOptionMinIntegerValues)
  return true;
failure:
  // @@protoc_insertion_point(parse_failure:protobuf_unittest.CustomOptionMinIntegerValues)
  return false;
#undef DO_
}

void CustomOptionMinIntegerValues::SerializeWithCachedSizes(
    ::google::protobuf::io::CodedOutputStream* output) const {
  // @@protoc_insertion_point(serialize_start:protobuf_unittest.CustomOptionMinIntegerValues)
  if (_internal_metadata_.have_unknown_fields()) {
    ::google::protobuf::internal::WireFormat::SerializeUnknownFields(
        unknown_fields(), output);
  }
  // @@protoc_insertion_point(serialize_end:protobuf_unittest.CustomOptionMinIntegerValues)
}

::google::protobuf::uint8* CustomOptionMinIntegerValues::InternalSerializeWithCachedSizesToArray(
    bool deterministic, ::google::protobuf::uint8* target) const {
  (void)deterministic; // Unused
  // @@protoc_insertion_point(serialize_to_array_start:protobuf_unittest.CustomOptionMinIntegerValues)
  if (_internal_metadata_.have_unknown_fields()) {
    target = ::google::protobuf::internal::WireFormat::SerializeUnknownFieldsToArray(
        unknown_fields(), target);
  }
  // @@protoc_insertion_point(serialize_to_array_end:protobuf_unittest.CustomOptionMinIntegerValues)
  return target;
}

size_t CustomOptionMinIntegerValues::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:protobuf_unittest.CustomOptionMinIntegerValues)
  size_t total_size = 0;

  if (_internal_metadata_.have_unknown_fields()) {
    total_size +=
      ::google::protobuf::internal::WireFormat::ComputeUnknownFieldsSize(
        unknown_fields());
  }
  int cached_size = ::google::protobuf::internal::ToCachedSize(total_size);
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = cached_size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
  return total_size;
}

void CustomOptionMinIntegerValues::MergeFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_merge_from_start:protobuf_unittest.CustomOptionMinIntegerValues)
  if (GOOGLE_PREDICT_FALSE(&from == this)) MergeFromFail(__LINE__);
  const CustomOptionMinIntegerValues* source =
      ::google::protobuf::internal::DynamicCastToGenerated<const CustomOptionMinIntegerValues>(
          &from);
  if (source == NULL) {
  // @@protoc_insertion_point(generalized_merge_from_cast_fail:protobuf_unittest.CustomOptionMinIntegerValues)
    ::google::protobuf::internal::ReflectionOps::Merge(from, this);
  } else {
  // @@protoc_insertion_point(generalized_merge_from_cast_success:protobuf_unittest.CustomOptionMinIntegerValues)
    UnsafeMergeFrom(*source);
  }
}

void CustomOptionMinIntegerValues::MergeFrom(const CustomOptionMinIntegerValues& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:protobuf_unittest.CustomOptionMinIntegerValues)
  if (GOOGLE_PREDICT_TRUE(&from != this)) {
    UnsafeMergeFrom(from);
  } else {
    MergeFromFail(__LINE__);
  }
}

void CustomOptionMinIntegerValues::UnsafeMergeFrom(const CustomOptionMinIntegerValues& from) {
  GOOGLE_DCHECK(&from != this);
  if (from._internal_metadata_.have_unknown_fields()) {
    ::google::protobuf::UnknownFieldSet::MergeToInternalMetdata(
      from.unknown_fields(), &_internal_metadata_);
  }
}

void CustomOptionMinIntegerValues::CopyFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_copy_from_start:protobuf_unittest.CustomOptionMinIntegerValues)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

void CustomOptionMinIntegerValues::CopyFrom(const CustomOptionMinIntegerValues& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:protobuf_unittest.CustomOptionMinIntegerValues)
  if (&from == this) return;
  Clear();
  UnsafeMergeFrom(from);
}

bool CustomOptionMinIntegerValues::IsInitialized() const {

  return true;
}

void CustomOptionMinIntegerValues::Swap(CustomOptionMinIntegerValues* other) {
  if (other == this) return;
  InternalSwap(other);
}
void CustomOptionMinIntegerValues::InternalSwap(CustomOptionMinIntegerValues* other) {
  _internal_metadata_.Swap(&other->_internal_metadata_);
  std::swap(_cached_size_, other->_cached_size_);
}

::google::protobuf::Metadata CustomOptionMinIntegerValues::GetMetadata() const {
  protobuf_AssignDescriptorsOnce();
  ::google::protobuf::Metadata metadata;
  metadata.descriptor = CustomOptionMinIntegerValues_descriptor_;
  metadata.reflection = CustomOptionMinIntegerValues_reflection_;
  return metadata;
}

#if PROTOBUF_INLINE_NOT_IN_HEADERS
// CustomOptionMinIntegerValues

inline const CustomOptionMinIntegerValues* CustomOptionMinIntegerValues::internal_default_instance() {
  return &CustomOptionMinIntegerValues_default_instance_.get();
}
#endif  // PROTOBUF_INLINE_NOT_IN_HEADERS

// ===================================================================

#if !defined(_MSC_VER) || _MSC_VER >= 1900
#endif  // !defined(_MSC_VER) || _MSC_VER >= 1900

CustomOptionMaxIntegerValues::CustomOptionMaxIntegerValues()
  : ::google::protobuf::Message(), _internal_metadata_(NULL) {
  if (this != internal_default_instance()) protobuf_InitDefaults_google_2fprotobuf_2funittest_5fcustom_5foptions_2eproto();
  SharedCtor();
  // @@protoc_insertion_point(constructor:protobuf_unittest.CustomOptionMaxIntegerValues)
}

void CustomOptionMaxIntegerValues::InitAsDefaultInstance() {
}

CustomOptionMaxIntegerValues::CustomOptionMaxIntegerValues(const CustomOptionMaxIntegerValues& from)
  : ::google::protobuf::Message(),
    _internal_metadata_(NULL) {
  SharedCtor();
  UnsafeMergeFrom(from);
  // @@protoc_insertion_point(copy_constructor:protobuf_unittest.CustomOptionMaxIntegerValues)
}

void CustomOptionMaxIntegerValues::SharedCtor() {
  _cached_size_ = 0;
}

CustomOptionMaxIntegerValues::~CustomOptionMaxIntegerValues() {
  // @@protoc_insertion_point(destructor:protobuf_unittest.CustomOptionMaxIntegerValues)
  SharedDtor();
}

void CustomOptionMaxIntegerValues::SharedDtor() {
}

void CustomOptionMaxIntegerValues::SetCachedSize(int size) const {
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
}
const ::google::protobuf::Descriptor* CustomOptionMaxIntegerValues::descriptor() {
  protobuf_AssignDescriptorsOnce();
  return CustomOptionMaxIntegerValues_descriptor_;
}

const CustomOptionMaxIntegerValues& CustomOptionMaxIntegerValues::default_instance() {
  protobuf_InitDefaults_google_2fprotobuf_2funittest_5fcustom_5foptions_2eproto();
  return *internal_default_instance();
}

::google::protobuf::internal::ExplicitlyConstructed<CustomOptionMaxIntegerValues> CustomOptionMaxIntegerValues_default_instance_;

CustomOptionMaxIntegerValues* CustomOptionMaxIntegerValues::New(::google::protobuf::Arena* arena) const {
  CustomOptionMaxIntegerValues* n = new CustomOptionMaxIntegerValues;
  if (arena != NULL) {
    arena->Own(n);
  }
  return n;
}

void CustomOptionMaxIntegerValues::Clear() {
// @@protoc_insertion_point(message_clear_start:protobuf_unittest.CustomOptionMaxIntegerValues)
  _has_bits_.Clear();
  if (_internal_metadata_.have_unknown_fields()) {
    mutable_unknown_fields()->Clear();
  }
}

bool CustomOptionMaxIntegerValues::MergePartialFromCodedStream(
    ::google::protobuf::io::CodedInputStream* input) {
#define DO_(EXPRESSION) if (!GOOGLE_PREDICT_TRUE(EXPRESSION)) goto failure
  ::google::protobuf::uint32 tag;
  // @@protoc_insertion_point(parse_start:protobuf_unittest.CustomOptionMaxIntegerValues)
  for (;;) {
    ::std::pair< ::google::protobuf::uint32, bool> p = input->ReadTagWithCutoff(127);
    tag = p.first;
    if (!p.second) goto handle_unusual;
  handle_unusual:
    if (tag == 0 ||
        ::google::protobuf::internal::WireFormatLite::GetTagWireType(tag) ==
        ::google::protobuf::internal::WireFormatLite::WIRETYPE_END_GROUP) {
      goto success;
    }
    DO_(::google::protobuf::internal::WireFormat::SkipField(
          input, tag, mutable_unknown_fields()));
  }
success:
  // @@protoc_insertion_point(parse_success:protobuf_unittest.CustomOptionMaxIntegerValues)
  return true;
failure:
  // @@protoc_insertion_point(parse_failure:protobuf_unittest.CustomOptionMaxIntegerValues)
  return false;
#undef DO_
}

void CustomOptionMaxIntegerValues::SerializeWithCachedSizes(
    ::google::protobuf::io::CodedOutputStream* output) const {
  // @@protoc_insertion_point(serialize_start:protobuf_unittest.CustomOptionMaxIntegerValues)
  if (_internal_metadata_.have_unknown_fields()) {
    ::google::protobuf::internal::WireFormat::SerializeUnknownFields(
        unknown_fields(), output);
  }
  // @@protoc_insertion_point(serialize_end:protobuf_unittest.CustomOptionMaxIntegerValues)
}

::google::protobuf::uint8* CustomOptionMaxIntegerValues::InternalSerializeWithCachedSizesToArray(
    bool deterministic, ::google::protobuf::uint8* target) const {
  (void)deterministic; // Unused
  // @@protoc_insertion_point(serialize_to_array_start:protobuf_unittest.CustomOptionMaxIntegerValues)
  if (_internal_metadata_.have_unknown_fields()) {
    target = ::google::protobuf::internal::WireFormat::SerializeUnknownFieldsToArray(
        unknown_fields(), target);
  }
  // @@protoc_insertion_point(serialize_to_array_end:protobuf_unittest.CustomOptionMaxIntegerValues)
  return target;
}

size_t CustomOptionMaxIntegerValues::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:protobuf_unittest.CustomOptionMaxIntegerValues)
  size_t total_size = 0;

  if (_internal_metadata_.have_unknown_fields()) {
    total_size +=
      ::google::protobuf::internal::WireFormat::ComputeUnknownFieldsSize(
        unknown_fields());
  }
  int cached_size = ::google::protobuf::internal::ToCachedSize(total_size);
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = cached_size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
  return total_size;
}

void CustomOptionMaxIntegerValues::MergeFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_merge_from_start:protobuf_unittest.CustomOptionMaxIntegerValues)
  if (GOOGLE_PREDICT_FALSE(&from == this)) MergeFromFail(__LINE__);
  const CustomOptionMaxIntegerValues* source =
      ::google::protobuf::internal::DynamicCastToGenerated<const CustomOptionMaxIntegerValues>(
          &from);
  if (source == NULL) {
  // @@protoc_insertion_point(generalized_merge_from_cast_fail:protobuf_unittest.CustomOptionMaxIntegerValues)
    ::google::protobuf::internal::ReflectionOps::Merge(from, this);
  } else {
  // @@protoc_insertion_point(generalized_merge_from_cast_success:protobuf_unittest.CustomOptionMaxIntegerValues)
    UnsafeMergeFrom(*source);
  }
}

void CustomOptionMaxIntegerValues::MergeFrom(const CustomOptionMaxIntegerValues& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:protobuf_unittest.CustomOptionMaxIntegerValues)
  if (GOOGLE_PREDICT_TRUE(&from != this)) {
    UnsafeMergeFrom(from);
  } else {
    MergeFromFail(__LINE__);
  }
}

void CustomOptionMaxIntegerValues::UnsafeMergeFrom(const CustomOptionMaxIntegerValues& from) {
  GOOGLE_DCHECK(&from != this);
  if (from._internal_metadata_.have_unknown_fields()) {
    ::google::protobuf::UnknownFieldSet::MergeToInternalMetdata(
      from.unknown_fields(), &_internal_metadata_);
  }
}

void CustomOptionMaxIntegerValues::CopyFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_copy_from_start:protobuf_unittest.CustomOptionMaxIntegerValues)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

void CustomOptionMaxIntegerValues::CopyFrom(const CustomOptionMaxIntegerValues& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:protobuf_unittest.CustomOptionMaxIntegerValues)
  if (&from == this) return;
  Clear();
  UnsafeMergeFrom(from);
}

bool CustomOptionMaxIntegerValues::IsInitialized() const {

  return true;
}

void CustomOptionMaxIntegerValues::Swap(CustomOptionMaxIntegerValues* other) {
  if (other == this) return;
  InternalSwap(other);
}
void CustomOptionMaxIntegerValues::InternalSwap(CustomOptionMaxIntegerValues* other) {
  _internal_metadata_.Swap(&other->_internal_metadata_);
  std::swap(_cached_size_, other->_cached_size_);
}

::google::protobuf::Metadata CustomOptionMaxIntegerValues::GetMetadata() const {
  protobuf_AssignDescriptorsOnce();
  ::google::protobuf::Metadata metadata;
  metadata.descriptor = CustomOptionMaxIntegerValues_descriptor_;
  metadata.reflection = CustomOptionMaxIntegerValues_reflection_;
  return metadata;
}

#if PROTOBUF_INLINE_NOT_IN_HEADERS
// CustomOptionMaxIntegerValues

inline const CustomOptionMaxIntegerValues* CustomOptionMaxIntegerValues::internal_default_instance() {
  return &CustomOptionMaxIntegerValues_default_instance_.get();
}
#endif  // PROTOBUF_INLINE_NOT_IN_HEADERS

// ===================================================================

#if !defined(_MSC_VER) || _MSC_VER >= 1900
#endif  // !defined(_MSC_VER) || _MSC_VER >= 1900

CustomOptionOtherValues::CustomOptionOtherValues()
  : ::google::protobuf::Message(), _internal_metadata_(NULL) {
  if (this != internal_default_instance()) protobuf_InitDefaults_google_2fprotobuf_2funittest_5fcustom_5foptions_2eproto();
  SharedCtor();
  // @@protoc_insertion_point(constructor:protobuf_unittest.CustomOptionOtherValues)
}

void CustomOptionOtherValues::InitAsDefaultInstance() {
}

CustomOptionOtherValues::CustomOptionOtherValues(const CustomOptionOtherValues& from)
  : ::google::protobuf::Message(),
    _internal_metadata_(NULL) {
  SharedCtor();
  UnsafeMergeFrom(from);
  // @@protoc_insertion_point(copy_constructor:protobuf_unittest.CustomOptionOtherValues)
}

void CustomOptionOtherValues::SharedCtor() {
  _cached_size_ = 0;
}

CustomOptionOtherValues::~CustomOptionOtherValues() {
  // @@protoc_insertion_point(destructor:protobuf_unittest.CustomOptionOtherValues)
  SharedDtor();
}

void CustomOptionOtherValues::SharedDtor() {
}

void CustomOptionOtherValues::SetCachedSize(int size) const {
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
}
const ::google::protobuf::Descriptor* CustomOptionOtherValues::descriptor() {
  protobuf_AssignDescriptorsOnce();
  return CustomOptionOtherValues_descriptor_;
}

const CustomOptionOtherValues& CustomOptionOtherValues::default_instance() {
  protobuf_InitDefaults_google_2fprotobuf_2funittest_5fcustom_5foptions_2eproto();
  return *internal_default_instance();
}

::google::protobuf::internal::ExplicitlyConstructed<CustomOptionOtherValues> CustomOptionOtherValues_default_instance_;

CustomOptionOtherValues* CustomOptionOtherValues::New(::google::protobuf::Arena* arena) const {
  CustomOptionOtherValues* n = new CustomOptionOtherValues;
  if (arena != NULL) {
    arena->Own(n);
  }
  return n;
}

void CustomOptionOtherValues::Clear() {
// @@protoc_insertion_point(message_clear_start:protobuf_unittest.CustomOptionOtherValues)
  _has_bits_.Clear();
  if (_internal_metadata_.have_unknown_fields()) {
    mutable_unknown_fields()->Clear();
  }
}

bool CustomOptionOtherValues::MergePartialFromCodedStream(
    ::google::protobuf::io::CodedInputStream* input) {
#define DO_(EXPRESSION) if (!GOOGLE_PREDICT_TRUE(EXPRESSION)) goto failure
  ::google::protobuf::uint32 tag;
  // @@protoc_insertion_point(parse_start:protobuf_unittest.CustomOptionOtherValues)
  for (;;) {
    ::std::pair< ::google::protobuf::uint32, bool> p = input->ReadTagWithCutoff(127);
    tag = p.first;
    if (!p.second) goto handle_unusual;
  handle_unusual:
    if (tag == 0 ||
        ::google::protobuf::internal::WireFormatLite::GetTagWireType(tag) ==
        ::google::protobuf::internal::WireFormatLite::WIRETYPE_END_GROUP) {
      goto success;
    }
    DO_(::google::protobuf::internal::WireFormat::SkipField(
          input, tag, mutable_unknown_fields()));
  }
success:
  // @@protoc_insertion_point(parse_success:protobuf_unittest.CustomOptionOtherValues)
  return true;
failure:
  // @@protoc_insertion_point(parse_failure:protobuf_unittest.CustomOptionOtherValues)
  return false;
#undef DO_
}

void CustomOptionOtherValues::SerializeWithCachedSizes(
    ::google::protobuf::io::CodedOutputStream* output) const {
  // @@protoc_insertion_point(serialize_start:protobuf_unittest.CustomOptionOtherValues)
  if (_internal_metadata_.have_unknown_fields()) {
    ::google::protobuf::internal::WireFormat::SerializeUnknownFields(
        unknown_fields(), output);
  }
  // @@protoc_insertion_point(serialize_end:protobuf_unittest.CustomOptionOtherValues)
}

::google::protobuf::uint8* CustomOptionOtherValues::InternalSerializeWithCachedSizesToArray(
    bool deterministic, ::google::protobuf::uint8* target) const {
  (void)deterministic; // Unused
  // @@protoc_insertion_point(serialize_to_array_start:protobuf_unittest.CustomOptionOtherValues)
  if (_internal_metadata_.have_unknown_fields()) {
    target = ::google::protobuf::internal::WireFormat::SerializeUnknownFieldsToArray(
        unknown_fields(), target);
  }
  // @@protoc_insertion_point(serialize_to_array_end:protobuf_unittest.CustomOptionOtherValues)
  return target;
}

size_t CustomOptionOtherValues::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:protobuf_unittest.CustomOptionOtherValues)
  size_t total_size = 0;

  if (_internal_metadata_.have_unknown_fields()) {
    total_size +=
      ::google::protobuf::internal::WireFormat::ComputeUnknownFieldsSize(
        unknown_fields());
  }
  int cached_size = ::google::protobuf::internal::ToCachedSize(total_size);
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = cached_size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
  return total_size;
}

void CustomOptionOtherValues::MergeFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_merge_from_start:protobuf_unittest.CustomOptionOtherValues)
  if (GOOGLE_PREDICT_FALSE(&from == this)) MergeFromFail(__LINE__);
  const CustomOptionOtherValues* source =
      ::google::protobuf::internal::DynamicCastToGenerated<const CustomOptionOtherValues>(
          &from);
  if (source == NULL) {
  // @@protoc_insertion_point(generalized_merge_from_cast_fail:protobuf_unittest.CustomOptionOtherValues)
    ::google::protobuf::internal::ReflectionOps::Merge(from, this);
  } else {
  // @@protoc_insertion_point(generalized_merge_from_cast_success:protobuf_unittest.CustomOptionOtherValues)
    UnsafeMergeFrom(*source);
  }
}

void CustomOptionOtherValues::MergeFrom(const CustomOptionOtherValues& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:protobuf_unittest.CustomOptionOtherValues)
  if (GOOGLE_PREDICT_TRUE(&from != this)) {
    UnsafeMergeFrom(from);
  } else {
    MergeFromFail(__LINE__);
  }
}

void CustomOptionOtherValues::UnsafeMergeFrom(const CustomOptionOtherValues& from) {
  GOOGLE_DCHECK(&from != this);
  if (from._internal_metadata_.have_unknown_fields()) {
    ::google::protobuf::UnknownFieldSet::MergeToInternalMetdata(
      from.unknown_fields(), &_internal_metadata_);
  }
}

void CustomOptionOtherValues::CopyFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_copy_from_start:protobuf_unittest.CustomOptionOtherValues)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

void CustomOptionOtherValues::CopyFrom(const CustomOptionOtherValues& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:protobuf_unittest.CustomOptionOtherValues)
  if (&from == this) return;
  Clear();
  UnsafeMergeFrom(from);
}

bool CustomOptionOtherValues::IsInitialized() const {

  return true;
}

void CustomOptionOtherValues::Swap(CustomOptionOtherValues* other) {
  if (other == this) return;
  InternalSwap(other);
}
void CustomOptionOtherValues::InternalSwap(CustomOptionOtherValues* other) {
  _internal_metadata_.Swap(&other->_internal_metadata_);
  std::swap(_cached_size_, other->_cached_size_);
}

::google::protobuf::Metadata CustomOptionOtherValues::GetMetadata() const {
  protobuf_AssignDescriptorsOnce();
  ::google::protobuf::Metadata metadata;
  metadata.descriptor = CustomOptionOtherValues_descriptor_;
  metadata.reflection = CustomOptionOtherValues_reflection_;
  return metadata;
}

#if PROTOBUF_INLINE_NOT_IN_HEADERS
// CustomOptionOtherValues

inline const CustomOptionOtherValues* CustomOptionOtherValues::internal_default_instance() {
  return &CustomOptionOtherValues_default_instance_.get();
}
#endif  // PROTOBUF_INLINE_NOT_IN_HEADERS

// ===================================================================

#if !defined(_MSC_VER) || _MSC_VER >= 1900
#endif  // !defined(_MSC_VER) || _MSC_VER >= 1900

SettingRealsFromPositiveInts::SettingRealsFromPositiveInts()
  : ::google::protobuf::Message(), _internal_metadata_(NULL) {
  if (this != internal_default_instance()) protobuf_InitDefaults_google_2fprotobuf_2funittest_5fcustom_5foptions_2eproto();
  SharedCtor();
  // @@protoc_insertion_point(constructor:protobuf_unittest.SettingRealsFromPositiveInts)
}

void SettingRealsFromPositiveInts::InitAsDefaultInstance() {
}

SettingRealsFromPositiveInts::SettingRealsFromPositiveInts(const SettingRealsFromPositiveInts& from)
  : ::google::protobuf::Message(),
    _internal_metadata_(NULL) {
  SharedCtor();
  UnsafeMergeFrom(from);
  // @@protoc_insertion_point(copy_constructor:protobuf_unittest.SettingRealsFromPositiveInts)
}

void SettingRealsFromPositiveInts::SharedCtor() {
  _cached_size_ = 0;
}

SettingRealsFromPositiveInts::~SettingRealsFromPositiveInts() {
  // @@protoc_insertion_point(destructor:protobuf_unittest.SettingRealsFromPositiveInts)
  SharedDtor();
}

void SettingRealsFromPositiveInts::SharedDtor() {
}

void SettingRealsFromPositiveInts::SetCachedSize(int size) const {
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
}
const ::google::protobuf::Descriptor* SettingRealsFromPositiveInts::descriptor() {
  protobuf_AssignDescriptorsOnce();
  return SettingRealsFromPositiveInts_descriptor_;
}

const SettingRealsFromPositiveInts& SettingRealsFromPositiveInts::default_instance() {
  protobuf_InitDefaults_google_2fprotobuf_2funittest_5fcustom_5foptions_2eproto();
  return *internal_default_instance();
}

::google::protobuf::internal::ExplicitlyConstructed<SettingRealsFromPositiveInts> SettingRealsFromPositiveInts_default_instance_;

SettingRealsFromPositiveInts* SettingRealsFromPositiveInts::New(::google::protobuf::Arena* arena) const {
  SettingRealsFromPositiveInts* n = new SettingRealsFromPositiveInts;
  if (arena != NULL) {
    arena->Own(n);
  }
  return n;
}

void SettingRealsFromPositiveInts::Clear() {
// @@protoc_insertion_point(message_clear_start:protobuf_unittest.SettingRealsFromPositiveInts)
  _has_bits_.Clear();
  if (_internal_metadata_.have_unknown_fields()) {
    mutable_unknown_fields()->Clear();
  }
}

bool SettingRealsFromPositiveInts::MergePartialFromCodedStream(
    ::google::protobuf::io::CodedInputStream* input) {
#define DO_(EXPRESSION) if (!GOOGLE_PREDICT_TRUE(EXPRESSION)) goto failure
  ::google::protobuf::uint32 tag;
  // @@protoc_insertion_point(parse_start:protobuf_unittest.SettingRealsFromPositiveInts)
  for (;;) {
    ::std::pair< ::google::protobuf::uint32, bool> p = input->ReadTagWithCutoff(127);
    tag = p.first;
    if (!p.second) goto handle_unusual;
  handle_unusual:
    if (tag == 0 ||
        ::google::protobuf::internal::WireFormatLite::GetTagWireType(tag) ==
        ::google::protobuf::internal::WireFormatLite::WIRETYPE_END_GROUP) {
      goto success;
    }
    DO_(::google::protobuf::internal::WireFormat::SkipField(
          input, tag, mutable_unknown_fields()));
  }
success:
  // @@protoc_insertion_point(parse_success:protobuf_unittest.SettingRealsFromPositiveInts)
  return true;
failure:
  // @@protoc_insertion_point(parse_failure:protobuf_unittest.SettingRealsFromPositiveInts)
  return false;
#undef DO_
}

void SettingRealsFromPositiveInts::SerializeWithCachedSizes(
    ::google::protobuf::io::CodedOutputStream* output) const {
  // @@protoc_insertion_point(serialize_start:protobuf_unittest.SettingRealsFromPositiveInts)
  if (_internal_metadata_.have_unknown_fields()) {
    ::google::protobuf::internal::WireFormat::SerializeUnknownFields(
        unknown_fields(), output);
  }
  // @@protoc_insertion_point(serialize_end:protobuf_unittest.SettingRealsFromPositiveInts)
}

::google::protobuf::uint8* SettingRealsFromPositiveInts::InternalSerializeWithCachedSizesToArray(
    bool deterministic, ::google::protobuf::uint8* target) const {
  (void)deterministic; // Unused
  // @@protoc_insertion_point(serialize_to_array_start:protobuf_unittest.SettingRealsFromPositiveInts)
  if (_internal_metadata_.have_unknown_fields()) {
    target = ::google::protobuf::internal::WireFormat::SerializeUnknownFieldsToArray(
        unknown_fields(), target);
  }
  // @@protoc_insertion_point(serialize_to_array_end:protobuf_unittest.SettingRealsFromPositiveInts)
  return target;
}

size_t SettingRealsFromPositiveInts::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:protobuf_unittest.SettingRealsFromPositiveInts)
  size_t total_size = 0;

  if (_internal_metadata_.have_unknown_fields()) {
    total_size +=
      ::google::protobuf::internal::WireFormat::ComputeUnknownFieldsSize(
        unknown_fields());
  }
  int cached_size = ::google::protobuf::internal::ToCachedSize(total_size);
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = cached_size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
  return total_size;
}

void SettingRealsFromPositiveInts::MergeFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_merge_from_start:protobuf_unittest.SettingRealsFromPositiveInts)
  if (GOOGLE_PREDICT_FALSE(&from == this)) MergeFromFail(__LINE__);
  const SettingRealsFromPositiveInts* source =
      ::google::protobuf::internal::DynamicCastToGenerated<const SettingRealsFromPositiveInts>(
          &from);
  if (source == NULL) {
  // @@protoc_insertion_point(generalized_merge_from_cast_fail:protobuf_unittest.SettingRealsFromPositiveInts)
    ::google::protobuf::internal::ReflectionOps::Merge(from, this);
  } else {
  // @@protoc_insertion_point(generalized_merge_from_cast_success:protobuf_unittest.SettingRealsFromPositiveInts)
    UnsafeMergeFrom(*source);
  }
}

void SettingRealsFromPositiveInts::MergeFrom(const SettingRealsFromPositiveInts& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:protobuf_unittest.SettingRealsFromPositiveInts)
  if (GOOGLE_PREDICT_TRUE(&from != this)) {
    UnsafeMergeFrom(from);
  } else {
    MergeFromFail(__LINE__);
  }
}

void SettingRealsFromPositiveInts::UnsafeMergeFrom(const SettingRealsFromPositiveInts& from) {
  GOOGLE_DCHECK(&from != this);
  if (from._internal_metadata_.have_unknown_fields()) {
    ::google::protobuf::UnknownFieldSet::MergeToInternalMetdata(
      from.unknown_fields(), &_internal_metadata_);
  }
}

void SettingRealsFromPositiveInts::CopyFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_copy_from_start:protobuf_unittest.SettingRealsFromPositiveInts)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

void SettingRealsFromPositiveInts::CopyFrom(const SettingRealsFromPositiveInts& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:protobuf_unittest.SettingRealsFromPositiveInts)
  if (&from == this) return;
  Clear();
  UnsafeMergeFrom(from);
}

bool SettingRealsFromPositiveInts::IsInitialized() const {

  return true;
}

void SettingRealsFromPositiveInts::Swap(SettingRealsFromPositiveInts* other) {
  if (other == this) return;
  InternalSwap(other);
}
void SettingRealsFromPositiveInts::InternalSwap(SettingRealsFromPositiveInts* other) {
  _internal_metadata_.Swap(&other->_internal_metadata_);
  std::swap(_cached_size_, other->_cached_size_);
}

::google::protobuf::Metadata SettingRealsFromPositiveInts::GetMetadata() const {
  protobuf_AssignDescriptorsOnce();
  ::google::protobuf::Metadata metadata;
  metadata.descriptor = SettingRealsFromPositiveInts_descriptor_;
  metadata.reflection = SettingRealsFromPositiveInts_reflection_;
  return metadata;
}

#if PROTOBUF_INLINE_NOT_IN_HEADERS
// SettingRealsFromPositiveInts

inline const SettingRealsFromPositiveInts* SettingRealsFromPositiveInts::internal_default_instance() {
  return &SettingRealsFromPositiveInts_default_instance_.get();
}
#endif  // PROTOBUF_INLINE_NOT_IN_HEADERS

// ===================================================================

#if !defined(_MSC_VER) || _MSC_VER >= 1900
#endif  // !defined(_MSC_VER) || _MSC_VER >= 1900

SettingRealsFromNegativeInts::SettingRealsFromNegativeInts()
  : ::google::protobuf::Message(), _internal_metadata_(NULL) {
  if (this != internal_default_instance()) protobuf_InitDefaults_google_2fprotobuf_2funittest_5fcustom_5foptions_2eproto();
  SharedCtor();
  // @@protoc_insertion_point(constructor:protobuf_unittest.SettingRealsFromNegativeInts)
}

void SettingRealsFromNegativeInts::InitAsDefaultInstance() {
}

SettingRealsFromNegativeInts::SettingRealsFromNegativeInts(const SettingRealsFromNegativeInts& from)
  : ::google::protobuf::Message(),
    _internal_metadata_(NULL) {
  SharedCtor();
  UnsafeMergeFrom(from);
  // @@protoc_insertion_point(copy_constructor:protobuf_unittest.SettingRealsFromNegativeInts)
}

void SettingRealsFromNegativeInts::SharedCtor() {
  _cached_size_ = 0;
}

SettingRealsFromNegativeInts::~SettingRealsFromNegativeInts() {
  // @@protoc_insertion_point(destructor:protobuf_unittest.SettingRealsFromNegativeInts)
  SharedDtor();
}

void SettingRealsFromNegativeInts::SharedDtor() {
}

void SettingRealsFromNegativeInts::SetCachedSize(int size) const {
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
}
const ::google::protobuf::Descriptor* SettingRealsFromNegativeInts::descriptor() {
  protobuf_AssignDescriptorsOnce();
  return SettingRealsFromNegativeInts_descriptor_;
}

const SettingRealsFromNegativeInts& SettingRealsFromNegativeInts::default_instance() {
  protobuf_InitDefaults_google_2fprotobuf_2funittest_5fcustom_5foptions_2eproto();
  return *internal_default_instance();
}

::google::protobuf::internal::ExplicitlyConstructed<SettingRealsFromNegativeInts> SettingRealsFromNegativeInts_default_instance_;

SettingRealsFromNegativeInts* SettingRealsFromNegativeInts::New(::google::protobuf::Arena* arena) const {
  SettingRealsFromNegativeInts* n = new SettingRealsFromNegativeInts;
  if (arena != NULL) {
    arena->Own(n);
  }
  return n;
}

void SettingRealsFromNegativeInts::Clear() {
// @@protoc_insertion_point(message_clear_start:protobuf_unittest.SettingRealsFromNegativeInts)
  _has_bits_.Clear();
  if (_internal_metadata_.have_unknown_fields()) {
    mutable_unknown_fields()->Clear();
  }
}

bool SettingRealsFromNegativeInts::MergePartialFromCodedStream(
    ::google::protobuf::io::CodedInputStream* input) {
#define DO_(EXPRESSION) if (!GOOGLE_PREDICT_TRUE(EXPRESSION)) goto failure
  ::google::protobuf::uint32 tag;
  // @@protoc_insertion_point(parse_start:protobuf_unittest.SettingRealsFromNegativeInts)
  for (;;) {
    ::std::pair< ::google::protobuf::uint32, bool> p = input->ReadTagWithCutoff(127);
    tag = p.first;
    if (!p.second) goto handle_unusual;
  handle_unusual:
    if (tag == 0 ||
        ::google::protobuf::internal::WireFormatLite::GetTagWireType(tag) ==
        ::google::protobuf::internal::WireFormatLite::WIRETYPE_END_GROUP) {
      goto success;
    }
    DO_(::google::protobuf::internal::WireFormat::SkipField(
          input, tag, mutable_unknown_fields()));
  }
success:
  // @@protoc_insertion_point(parse_success:protobuf_unittest.SettingRealsFromNegativeInts)
  return true;
failure:
  // @@protoc_insertion_point(parse_failure:protobuf_unittest.SettingRealsFromNegativeInts)
  return false;
#undef DO_
}

void SettingRealsFromNegativeInts::SerializeWithCachedSizes(
    ::google::protobuf::io::CodedOutputStream* output) const {
  // @@protoc_insertion_point(serialize_start:protobuf_unittest.SettingRealsFromNegativeInts)
  if (_internal_metadata_.have_unknown_fields()) {
    ::google::protobuf::internal::WireFormat::SerializeUnknownFields(
        unknown_fields(), output);
  }
  // @@protoc_insertion_point(serialize_end:protobuf_unittest.SettingRealsFromNegativeInts)
}

::google::protobuf::uint8* SettingRealsFromNegativeInts::InternalSerializeWithCachedSizesToArray(
    bool deterministic, ::google::protobuf::uint8* target) const {
  (void)deterministic; // Unused
  // @@protoc_insertion_point(serialize_to_array_start:protobuf_unittest.SettingRealsFromNegativeInts)
  if (_internal_metadata_.have_unknown_fields()) {
    target = ::google::protobuf::internal::WireFormat::SerializeUnknownFieldsToArray(
        unknown_fields(), target);
  }
  // @@protoc_insertion_point(serialize_to_array_end:protobuf_unittest.SettingRealsFromNegativeInts)
  return target;
}

size_t SettingRealsFromNegativeInts::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:protobuf_unittest.SettingRealsFromNegativeInts)
  size_t total_size = 0;

  if (_internal_metadata_.have_unknown_fields()) {
    total_size +=
      ::google::protobuf::internal::WireFormat::ComputeUnknownFieldsSize(
        unknown_fields());
  }
  int cached_size = ::google::protobuf::internal::ToCachedSize(total_size);
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = cached_size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
  return total_size;
}

void SettingRealsFromNegativeInts::MergeFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_merge_from_start:protobuf_unittest.SettingRealsFromNegativeInts)
  if (GOOGLE_PREDICT_FALSE(&from == this)) MergeFromFail(__LINE__);
  const SettingRealsFromNegativeInts* source =
      ::google::protobuf::internal::DynamicCastToGenerated<const SettingRealsFromNegativeInts>(
          &from);
  if (source == NULL) {
  // @@protoc_insertion_point(generalized_merge_from_cast_fail:protobuf_unittest.SettingRealsFromNegativeInts)
    ::google::protobuf::internal::ReflectionOps::Merge(from, this);
  } else {
  // @@protoc_insertion_point(generalized_merge_from_cast_success:protobuf_unittest.SettingRealsFromNegativeInts)
    UnsafeMergeFrom(*source);
  }
}

void SettingRealsFromNegativeInts::MergeFrom(const SettingRealsFromNegativeInts& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:protobuf_unittest.SettingRealsFromNegativeInts)
  if (GOOGLE_PREDICT_TRUE(&from != this)) {
    UnsafeMergeFrom(from);
  } else {
    MergeFromFail(__LINE__);
  }
}

void SettingRealsFromNegativeInts::UnsafeMergeFrom(const SettingRealsFromNegativeInts& from) {
  GOOGLE_DCHECK(&from != this);
  if (from._internal_metadata_.have_unknown_fields()) {
    ::google::protobuf::UnknownFieldSet::MergeToInternalMetdata(
      from.unknown_fields(), &_internal_metadata_);
  }
}

void SettingRealsFromNegativeInts::CopyFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_copy_from_start:protobuf_unittest.SettingRealsFromNegativeInts)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

void SettingRealsFromNegativeInts::CopyFrom(const SettingRealsFromNegativeInts& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:protobuf_unittest.SettingRealsFromNegativeInts)
  if (&from == this) return;
  Clear();
  UnsafeMergeFrom(from);
}

bool SettingRealsFromNegativeInts::IsInitialized() const {

  return true;
}

void SettingRealsFromNegativeInts::Swap(SettingRealsFromNegativeInts* other) {
  if (other == this) return;
  InternalSwap(other);
}
void SettingRealsFromNegativeInts::InternalSwap(SettingRealsFromNegativeInts* other) {
  _internal_metadata_.Swap(&other->_internal_metadata_);
  std::swap(_cached_size_, other->_cached_size_);
}

::google::protobuf::Metadata SettingRealsFromNegativeInts::GetMetadata() const {
  protobuf_AssignDescriptorsOnce();
  ::google::protobuf::Metadata metadata;
  metadata.descriptor = SettingRealsFromNegativeInts_descriptor_;
  metadata.reflection = SettingRealsFromNegativeInts_reflection_;
  return metadata;
}

#if PROTOBUF_INLINE_NOT_IN_HEADERS
// SettingRealsFromNegativeInts

inline const SettingRealsFromNegativeInts* SettingRealsFromNegativeInts::internal_default_instance() {
  return &SettingRealsFromNegativeInts_default_instance_.get();
}
#endif  // PROTOBUF_INLINE_NOT_IN_HEADERS

// ===================================================================

#if !defined(_MSC_VER) || _MSC_VER >= 1900
const int ComplexOptionType1::kFooFieldNumber;
const int ComplexOptionType1::kFoo2FieldNumber;
const int ComplexOptionType1::kFoo3FieldNumber;
const int ComplexOptionType1::kFoo4FieldNumber;
#endif  // !defined(_MSC_VER) || _MSC_VER >= 1900

ComplexOptionType1::ComplexOptionType1()
  : ::google::protobuf::Message(), _internal_metadata_(NULL) {
  if (this != internal_default_instance()) protobuf_InitDefaults_google_2fprotobuf_2funittest_5fcustom_5foptions_2eproto();
  SharedCtor();
  // @@protoc_insertion_point(constructor:protobuf_unittest.ComplexOptionType1)
}

void ComplexOptionType1::InitAsDefaultInstance() {
}

ComplexOptionType1::ComplexOptionType1(const ComplexOptionType1& from)
  : ::google::protobuf::Message(),
    _internal_metadata_(NULL) {
  SharedCtor();
  UnsafeMergeFrom(from);
  // @@protoc_insertion_point(copy_constructor:protobuf_unittest.ComplexOptionType1)
}

void ComplexOptionType1::SharedCtor() {
  _cached_size_ = 0;
  ::memset(&foo_, 0, reinterpret_cast<char*>(&foo3_) -
    reinterpret_cast<char*>(&foo_) + sizeof(foo3_));
}

ComplexOptionType1::~ComplexOptionType1() {
  // @@protoc_insertion_point(destructor:protobuf_unittest.ComplexOptionType1)
  SharedDtor();
}

void ComplexOptionType1::SharedDtor() {
}

void ComplexOptionType1::SetCachedSize(int size) const {
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
}
const ::google::protobuf::Descriptor* ComplexOptionType1::descriptor() {
  protobuf_AssignDescriptorsOnce();
  return ComplexOptionType1_descriptor_;
}

const ComplexOptionType1& ComplexOptionType1::default_instance() {
  protobuf_InitDefaults_google_2fprotobuf_2funittest_5fcustom_5foptions_2eproto();
  return *internal_default_instance();
}

::google::protobuf::internal::ExplicitlyConstructed<ComplexOptionType1> ComplexOptionType1_default_instance_;

ComplexOptionType1* ComplexOptionType1::New(::google::protobuf::Arena* arena) const {
  ComplexOptionType1* n = new ComplexOptionType1;
  if (arena != NULL) {
    arena->Own(n);
  }
  return n;
}

void ComplexOptionType1::Clear() {
// @@protoc_insertion_point(message_clear_start:protobuf_unittest.ComplexOptionType1)
  _extensions_.Clear();
#if defined(__clang__)
#define ZR_HELPER_(f) \
  _Pragma("clang diagnostic push") \
  _Pragma("clang diagnostic ignored \"-Winvalid-offsetof\"") \
  __builtin_offsetof(ComplexOptionType1, f) \
  _Pragma("clang diagnostic pop")
#else
#define ZR_HELPER_(f) reinterpret_cast<char*>(\
  &reinterpret_cast<ComplexOptionType1*>(16)->f)
#endif

#define ZR_(first, last) do {\
  ::memset(&(first), 0,\
           ZR_HELPER_(last) - ZR_HELPER_(first) + sizeof(last));\
} while (0)

  ZR_(foo_, foo3_);

#undef ZR_HELPER_
#undef ZR_

  foo4_.Clear();
  _has_bits_.Clear();
  if (_internal_metadata_.have_unknown_fields()) {
    mutable_unknown_fields()->Clear();
  }
}

bool ComplexOptionType1::MergePartialFromCodedStream(
    ::google::protobuf::io::CodedInputStream* input) {
#define DO_(EXPRESSION) if (!GOOGLE_PREDICT_TRUE(EXPRESSION)) goto failure
  ::google::protobuf::uint32 tag;
  // @@protoc_insertion_point(parse_start:protobuf_unittest.ComplexOptionType1)
  for (;;) {
    ::std::pair< ::google::protobuf::uint32, bool> p = input->ReadTagWithCutoff(127);
    tag = p.first;
    if (!p.second) goto handle_unusual;
    switch (::google::protobuf::internal::WireFormatLite::GetTagFieldNumber(tag)) {
      // optional int32 foo = 1;
      case 1: {
        if (tag == 8) {
          set_has_foo();
          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int32, ::google::protobuf::internal::WireFormatLite::TYPE_INT32>(
                 input, &foo_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(16)) goto parse_foo2;
        break;
      }

      // optional int32 foo2 = 2;
      case 2: {
        if (tag == 16) {
         parse_foo2:
          set_has_foo2();
          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int32, ::google::protobuf::internal::WireFormatLite::TYPE_INT32>(
                 input, &foo2_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(24)) goto parse_foo3;
        break;
      }

      // optional int32 foo3 = 3;
      case 3: {
        if (tag == 24) {
         parse_foo3:
          set_has_foo3();
          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int32, ::google::protobuf::internal::WireFormatLite::TYPE_INT32>(
                 input, &foo3_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(32)) goto parse_foo4;
        break;
      }

      // repeated int32 foo4 = 4;
      case 4: {
        if (tag == 32) {
         parse_foo4:
          DO_((::google::protobuf::internal::WireFormatLite::ReadRepeatedPrimitive<
                   ::google::protobuf::int32, ::google::protobuf::internal::WireFormatLite::TYPE_INT32>(
                 1, 32, input, this->mutable_foo4())));
        } else if (tag == 34) {
          DO_((::google::protobuf::internal::WireFormatLite::ReadPackedPrimitiveNoInline<
                   ::google::protobuf::int32, ::google::protobuf::internal::WireFormatLite::TYPE_INT32>(
                 input, this->mutable_foo4())));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(32)) goto parse_foo4;
        if (input->ExpectAtEnd()) goto success;
        break;
      }

      default: {
      handle_unusual:
        if (tag == 0 ||
            ::google::protobuf::internal::WireFormatLite::GetTagWireType(tag) ==
            ::google::protobuf::internal::WireFormatLite::WIRETYPE_END_GROUP) {
          goto success;
        }
        if ((800u <= tag)) {
          DO_(_extensions_.ParseField(tag, input, internal_default_instance(),
                                      mutable_unknown_fields()));
          continue;
        }
        DO_(::google::protobuf::internal::WireFormat::SkipField(
              input, tag, mutable_unknown_fields()));
        break;
      }
    }
  }
success:
  // @@protoc_insertion_point(parse_success:protobuf_unittest.ComplexOptionType1)
  return true;
failure:
  // @@protoc_insertion_point(parse_failure:protobuf_unittest.ComplexOptionType1)
  return false;
#undef DO_
}

void ComplexOptionType1::SerializeWithCachedSizes(
    ::google::protobuf::io::CodedOutputStream* output) const {
  // @@protoc_insertion_point(serialize_start:protobuf_unittest.ComplexOptionType1)
  // optional int32 foo = 1;
  if (has_foo()) {
    ::google::protobuf::internal::WireFormatLite::WriteInt32(1, this->foo(), output);
  }

  // optional int32 foo2 = 2;
  if (has_foo2()) {
    ::google::protobuf::internal::WireFormatLite::WriteInt32(2, this->foo2(), output);
  }

  // optional int32 foo3 = 3;
  if (has_foo3()) {
    ::google::protobuf::internal::WireFormatLite::WriteInt32(3, this->foo3(), output);
  }

  // repeated int32 foo4 = 4;
  for (int i = 0; i < this->foo4_size(); i++) {
    ::google::protobuf::internal::WireFormatLite::WriteInt32(
      4, this->foo4(i), output);
  }

  // Extension range [100, 536870912)
  _extensions_.SerializeWithCachedSizes(
      100, 536870912, output);

  if (_internal_metadata_.have_unknown_fields()) {
    ::google::protobuf::internal::WireFormat::SerializeUnknownFields(
        unknown_fields(), output);
  }
  // @@protoc_insertion_point(serialize_end:protobuf_unittest.ComplexOptionType1)
}

::google::protobuf::uint8* ComplexOptionType1::InternalSerializeWithCachedSizesToArray(
    bool deterministic, ::google::protobuf::uint8* target) const {
  (void)deterministic; // Unused
  // @@protoc_insertion_point(serialize_to_array_start:protobuf_unittest.ComplexOptionType1)
  // optional int32 foo = 1;
  if (has_foo()) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt32ToArray(1, this->foo(), target);
  }

  // optional int32 foo2 = 2;
  if (has_foo2()) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt32ToArray(2, this->foo2(), target);
  }

  // optional int32 foo3 = 3;
  if (has_foo3()) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt32ToArray(3, this->foo3(), target);
  }

  // repeated int32 foo4 = 4;
  for (int i = 0; i < this->foo4_size(); i++) {
    target = ::google::protobuf::internal::WireFormatLite::
      WriteInt32ToArray(4, this->foo4(i), target);
  }

  // Extension range [100, 536870912)
  target = _extensions_.InternalSerializeWithCachedSizesToArray(
      100, 536870912, false, target);

  if (_internal_metadata_.have_unknown_fields()) {
    target = ::google::protobuf::internal::WireFormat::SerializeUnknownFieldsToArray(
        unknown_fields(), target);
  }
  // @@protoc_insertion_point(serialize_to_array_end:protobuf_unittest.ComplexOptionType1)
  return target;
}

size_t ComplexOptionType1::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:protobuf_unittest.ComplexOptionType1)
  size_t total_size = 0;

  if (_has_bits_[0 / 32] & 7u) {
    // optional int32 foo = 1;
    if (has_foo()) {
      total_size += 1 +
        ::google::protobuf::internal::WireFormatLite::Int32Size(
          this->foo());
    }

    // optional int32 foo2 = 2;
    if (has_foo2()) {
      total_size += 1 +
        ::google::protobuf::internal::WireFormatLite::Int32Size(
          this->foo2());
    }

    // optional int32 foo3 = 3;
    if (has_foo3()) {
      total_size += 1 +
        ::google::protobuf::internal::WireFormatLite::Int32Size(
          this->foo3());
    }

  }
  // repeated int32 foo4 = 4;
  {
    size_t data_size = 0;
    unsigned int count = this->foo4_size();
    for (unsigned int i = 0; i < count; i++) {
      data_size += ::google::protobuf::internal::WireFormatLite::
        Int32Size(this->foo4(i));
    }
    total_size += 1 *
                  ::google::protobuf::internal::FromIntSize(this->foo4_size());
    total_size += data_size;
  }

  total_size += _extensions_.ByteSize();

  if (_internal_metadata_.have_unknown_fields()) {
    total_size +=
      ::google::protobuf::internal::WireFormat::ComputeUnknownFieldsSize(
        unknown_fields());
  }
  int cached_size = ::google::protobuf::internal::ToCachedSize(total_size);
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = cached_size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
  return total_size;
}

void ComplexOptionType1::MergeFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_merge_from_start:protobuf_unittest.ComplexOptionType1)
  if (GOOGLE_PREDICT_FALSE(&from == this)) MergeFromFail(__LINE__);
  const ComplexOptionType1* source =
      ::google::protobuf::internal::DynamicCastToGenerated<const ComplexOptionType1>(
          &from);
  if (source == NULL) {
  // @@protoc_insertion_point(generalized_merge_from_cast_fail:protobuf_unittest.ComplexOptionType1)
    ::google::protobuf::internal::ReflectionOps::Merge(from, this);
  } else {
  // @@protoc_insertion_point(generalized_merge_from_cast_success:protobuf_unittest.ComplexOptionType1)
    UnsafeMergeFrom(*source);
  }
}

void ComplexOptionType1::MergeFrom(const ComplexOptionType1& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:protobuf_unittest.ComplexOptionType1)
  if (GOOGLE_PREDICT_TRUE(&from != this)) {
    UnsafeMergeFrom(from);
  } else {
    MergeFromFail(__LINE__);
  }
}

void ComplexOptionType1::UnsafeMergeFrom(const ComplexOptionType1& from) {
  GOOGLE_DCHECK(&from != this);
  foo4_.UnsafeMergeFrom(from.foo4_);
  if (from._has_bits_[0 / 32] & (0xffu << (0 % 32))) {
    if (from.has_foo()) {
      set_foo(from.foo());
    }
    if (from.has_foo2()) {
      set_foo2(from.foo2());
    }
    if (from.has_foo3()) {
      set_foo3(from.foo3());
    }
  }
  _extensions_.MergeFrom(from._extensions_);
  if (from._internal_metadata_.have_unknown_fields()) {
    ::google::protobuf::UnknownFieldSet::MergeToInternalMetdata(
      from.unknown_fields(), &_internal_metadata_);
  }
}

void ComplexOptionType1::CopyFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_copy_from_start:protobuf_unittest.ComplexOptionType1)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

void ComplexOptionType1::CopyFrom(const ComplexOptionType1& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:protobuf_unittest.ComplexOptionType1)
  if (&from == this) return;
  Clear();
  UnsafeMergeFrom(from);
}

bool ComplexOptionType1::IsInitialized() const {


  if (!_extensions_.IsInitialized()) {
    return false;
  }
  return true;
}

void ComplexOptionType1::Swap(ComplexOptionType1* other) {
  if (other == this) return;
  InternalSwap(other);
}
void ComplexOptionType1::InternalSwap(ComplexOptionType1* other) {
  std::swap(foo_, other->foo_);
  std::swap(foo2_, other->foo2_);
  std::swap(foo3_, other->foo3_);
  foo4_.UnsafeArenaSwap(&other->foo4_);
  std::swap(_has_bits_[0], other->_has_bits_[0]);
  _internal_metadata_.Swap(&other->_internal_metadata_);
  std::swap(_cached_size_, other->_cached_size_);
  _extensions_.Swap(&other->_extensions_);
}

::google::protobuf::Metadata ComplexOptionType1::GetMetadata() const {
  protobuf_AssignDescriptorsOnce();
  ::google::protobuf::Metadata metadata;
  metadata.descriptor = ComplexOptionType1_descriptor_;
  metadata.reflection = ComplexOptionType1_reflection_;
  return metadata;
}

#if PROTOBUF_INLINE_NOT_IN_HEADERS
// ComplexOptionType1

// optional int32 foo = 1;
bool ComplexOptionType1::has_foo() const {
  return (_has_bits_[0] & 0x00000001u) != 0;
}
void ComplexOptionType1::set_has_foo() {
  _has_bits_[0] |= 0x00000001u;
}
void ComplexOptionType1::clear_has_foo() {
  _has_bits_[0] &= ~0x00000001u;
}
void ComplexOptionType1::clear_foo() {
  foo_ = 0;
  clear_has_foo();
}
::google::protobuf::int32 ComplexOptionType1::foo() const {
  // @@protoc_insertion_point(field_get:protobuf_unittest.ComplexOptionType1.foo)
  return foo_;
}
void ComplexOptionType1::set_foo(::google::protobuf::int32 value) {
  set_has_foo();
  foo_ = value;
  // @@protoc_insertion_point(field_set:protobuf_unittest.ComplexOptionType1.foo)
}

// optional int32 foo2 = 2;
bool ComplexOptionType1::has_foo2() const {
  return (_has_bits_[0] & 0x00000002u) != 0;
}
void ComplexOptionType1::set_has_foo2() {
  _has_bits_[0] |= 0x00000002u;
}
void ComplexOptionType1::clear_has_foo2() {
  _has_bits_[0] &= ~0x00000002u;
}
void ComplexOptionType1::clear_foo2() {
  foo2_ = 0;
  clear_has_foo2();
}
::google::protobuf::int32 ComplexOptionType1::foo2() const {
  // @@protoc_insertion_point(field_get:protobuf_unittest.ComplexOptionType1.foo2)
  return foo2_;
}
void ComplexOptionType1::set_foo2(::google::protobuf::int32 value) {
  set_has_foo2();
  foo2_ = value;
  // @@protoc_insertion_point(field_set:protobuf_unittest.ComplexOptionType1.foo2)
}

// optional int32 foo3 = 3;
bool ComplexOptionType1::has_foo3() const {
  return (_has_bits_[0] & 0x00000004u) != 0;
}
void ComplexOptionType1::set_has_foo3() {
  _has_bits_[0] |= 0x00000004u;
}
void ComplexOptionType1::clear_has_foo3() {
  _has_bits_[0] &= ~0x00000004u;
}
void ComplexOptionType1::clear_foo3() {
  foo3_ = 0;
  clear_has_foo3();
}
::google::protobuf::int32 ComplexOptionType1::foo3() const {
  // @@protoc_insertion_point(field_get:protobuf_unittest.ComplexOptionType1.foo3)
  return foo3_;
}
void ComplexOptionType1::set_foo3(::google::protobuf::int32 value) {
  set_has_foo3();
  foo3_ = value;
  // @@protoc_insertion_point(field_set:protobuf_unittest.ComplexOptionType1.foo3)
}

// repeated int32 foo4 = 4;
int ComplexOptionType1::foo4_size() const {
  return foo4_.size();
}
void ComplexOptionType1::clear_foo4() {
  foo4_.Clear();
}
::google::protobuf::int32 ComplexOptionType1::foo4(int index) const {
  // @@protoc_insertion_point(field_get:protobuf_unittest.ComplexOptionType1.foo4)
  return foo4_.Get(index);
}
void ComplexOptionType1::set_foo4(int index, ::google::protobuf::int32 value) {
  foo4_.Set(index, value);
  // @@protoc_insertion_point(field_set:protobuf_unittest.ComplexOptionType1.foo4)
}
void ComplexOptionType1::add_foo4(::google::protobuf::int32 value) {
  foo4_.Add(value);
  // @@protoc_insertion_point(field_add:protobuf_unittest.ComplexOptionType1.foo4)
}
const ::google::protobuf::RepeatedField< ::google::protobuf::int32 >&
ComplexOptionType1::foo4() const {
  // @@protoc_insertion_point(field_list:protobuf_unittest.ComplexOptionType1.foo4)
  return foo4_;
}
::google::protobuf::RepeatedField< ::google::protobuf::int32 >*
ComplexOptionType1::mutable_foo4() {
  // @@protoc_insertion_point(field_mutable_list:protobuf_unittest.ComplexOptionType1.foo4)
  return &foo4_;
}

inline const ComplexOptionType1* ComplexOptionType1::internal_default_instance() {
  return &ComplexOptionType1_default_instance_.get();
}
#endif  // PROTOBUF_INLINE_NOT_IN_HEADERS

// ===================================================================

#if !defined(_MSC_VER) || _MSC_VER >= 1900
const int ComplexOptionType2_ComplexOptionType4::kWaldoFieldNumber;
#endif  // !defined(_MSC_VER) || _MSC_VER >= 1900

#if !defined(_MSC_VER) || _MSC_VER >= 1900
const int ComplexOptionType2_ComplexOptionType4::kComplexOpt4FieldNumber;
#endif
::google::protobuf::internal::ExtensionIdentifier< ::google::protobuf::MessageOptions,
    ::google::protobuf::internal::MessageTypeTraits< ::protobuf_unittest::ComplexOptionType2_ComplexOptionType4 >, 11, false >
  ComplexOptionType2_ComplexOptionType4::complex_opt4(kComplexOpt4FieldNumber, *::protobuf_unittest::ComplexOptionType2_ComplexOptionType4::internal_default_instance());
ComplexOptionType2_ComplexOptionType4::ComplexOptionType2_ComplexOptionType4()
  : ::google::protobuf::Message(), _internal_metadata_(NULL) {
  if (this != internal_default_instance()) protobuf_InitDefaults_google_2fprotobuf_2funittest_5fcustom_5foptions_2eproto();
  SharedCtor();
  // @@protoc_insertion_point(constructor:protobuf_unittest.ComplexOptionType2.ComplexOptionType4)
}

void ComplexOptionType2_ComplexOptionType4::InitAsDefaultInstance() {
}

ComplexOptionType2_ComplexOptionType4::ComplexOptionType2_ComplexOptionType4(const ComplexOptionType2_ComplexOptionType4& from)
  : ::google::protobuf::Message(),
    _internal_metadata_(NULL) {
  SharedCtor();
  UnsafeMergeFrom(from);
  // @@protoc_insertion_point(copy_constructor:protobuf_unittest.ComplexOptionType2.ComplexOptionType4)
}

void ComplexOptionType2_ComplexOptionType4::SharedCtor() {
  _cached_size_ = 0;
  waldo_ = 0;
}

ComplexOptionType2_ComplexOptionType4::~ComplexOptionType2_ComplexOptionType4() {
  // @@protoc_insertion_point(destructor:protobuf_unittest.ComplexOptionType2.ComplexOptionType4)
  SharedDtor();
}

void ComplexOptionType2_ComplexOptionType4::SharedDtor() {
}

void ComplexOptionType2_ComplexOptionType4::SetCachedSize(int size) const {
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
}
const ::google::protobuf::Descriptor* ComplexOptionType2_ComplexOptionType4::descriptor() {
  protobuf_AssignDescriptorsOnce();
  return ComplexOptionType2_ComplexOptionType4_descriptor_;
}

const ComplexOptionType2_ComplexOptionType4& ComplexOptionType2_ComplexOptionType4::default_instance() {
  protobuf_InitDefaults_google_2fprotobuf_2funittest_5fcustom_5foptions_2eproto();
  return *internal_default_instance();
}

::google::protobuf::internal::ExplicitlyConstructed<ComplexOptionType2_ComplexOptionType4> ComplexOptionType2_ComplexOptionType4_default_instance_;

ComplexOptionType2_ComplexOptionType4* ComplexOptionType2_ComplexOptionType4::New(::google::protobuf::Arena* arena) const {
  ComplexOptionType2_ComplexOptionType4* n = new ComplexOptionType2_ComplexOptionType4;
  if (arena != NULL) {
    arena->Own(n);
  }
  return n;
}

void ComplexOptionType2_ComplexOptionType4::Clear() {
// @@protoc_insertion_point(message_clear_start:protobuf_unittest.ComplexOptionType2.ComplexOptionType4)
  waldo_ = 0;
  _has_bits_.Clear();
  if (_internal_metadata_.have_unknown_fields()) {
    mutable_unknown_fields()->Clear();
  }
}

bool ComplexOptionType2_ComplexOptionType4::MergePartialFromCodedStream(
    ::google::protobuf::io::CodedInputStream* input) {
#define DO_(EXPRESSION) if (!GOOGLE_PREDICT_TRUE(EXPRESSION)) goto failure
  ::google::protobuf::uint32 tag;
  // @@protoc_insertion_point(parse_start:protobuf_unittest.ComplexOptionType2.ComplexOptionType4)
  for (;;) {
    ::std::pair< ::google::protobuf::uint32, bool> p = input->ReadTagWithCutoff(127);
    tag = p.first;
    if (!p.second) goto handle_unusual;
    switch (::google::protobuf::internal::WireFormatLite::GetTagFieldNumber(tag)) {
      // optional int32 waldo = 1;
      case 1: {
        if (tag == 8) {
          set_has_waldo();
          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int32, ::google::protobuf::internal::WireFormatLite::TYPE_INT32>(
                 input, &waldo_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectAtEnd()) goto success;
        break;
      }

      default: {
      handle_unusual:
        if (tag == 0 ||
            ::google::protobuf::internal::WireFormatLite::GetTagWireType(tag) ==
            ::google::protobuf::internal::WireFormatLite::WIRETYPE_END_GROUP) {
          goto success;
        }
        DO_(::google::protobuf::internal::WireFormat::SkipField(
              input, tag, mutable_unknown_fields()));
        break;
      }
    }
  }
success:
  // @@protoc_insertion_point(parse_success:protobuf_unittest.ComplexOptionType2.ComplexOptionType4)
  return true;
failure:
  // @@protoc_insertion_point(parse_failure:protobuf_unittest.ComplexOptionType2.ComplexOptionType4)
  return false;
#undef DO_
}

void ComplexOptionType2_ComplexOptionType4::SerializeWithCachedSizes(
    ::google::protobuf::io::CodedOutputStream* output) const {
  // @@protoc_insertion_point(serialize_start:protobuf_unittest.ComplexOptionType2.ComplexOptionType4)
  // optional int32 waldo = 1;
  if (has_waldo()) {
    ::google::protobuf::internal::WireFormatLite::WriteInt32(1, this->waldo(), output);
  }

  if (_internal_metadata_.have_unknown_fields()) {
    ::google::protobuf::internal::WireFormat::SerializeUnknownFields(
        unknown_fields(), output);
  }
  // @@protoc_insertion_point(serialize_end:protobuf_unittest.ComplexOptionType2.ComplexOptionType4)
}

::google::protobuf::uint8* ComplexOptionType2_ComplexOptionType4::InternalSerializeWithCachedSizesToArray(
    bool deterministic, ::google::protobuf::uint8* target) const {
  (void)deterministic; // Unused
  // @@protoc_insertion_point(serialize_to_array_start:protobuf_unittest.ComplexOptionType2.ComplexOptionType4)
  // optional int32 waldo = 1;
  if (has_waldo()) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt32ToArray(1, this->waldo(), target);
  }

  if (_internal_metadata_.have_unknown_fields()) {
    target = ::google::protobuf::internal::WireFormat::SerializeUnknownFieldsToArray(
        unknown_fields(), target);
  }
  // @@protoc_insertion_point(serialize_to_array_end:protobuf_unittest.ComplexOptionType2.ComplexOptionType4)
  return target;
}

size_t ComplexOptionType2_ComplexOptionType4::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:protobuf_unittest.ComplexOptionType2.ComplexOptionType4)
  size_t total_size = 0;

  // optional int32 waldo = 1;
  if (has_waldo()) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::Int32Size(
        this->waldo());
  }

  if (_internal_metadata_.have_unknown_fields()) {
    total_size +=
      ::google::protobuf::internal::WireFormat::ComputeUnknownFieldsSize(
        unknown_fields());
  }
  int cached_size = ::google::protobuf::internal::ToCachedSize(total_size);
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = cached_size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
  return total_size;
}

void ComplexOptionType2_ComplexOptionType4::MergeFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_merge_from_start:protobuf_unittest.ComplexOptionType2.ComplexOptionType4)
  if (GOOGLE_PREDICT_FALSE(&from == this)) MergeFromFail(__LINE__);
  const ComplexOptionType2_ComplexOptionType4* source =
      ::google::protobuf::internal::DynamicCastToGenerated<const ComplexOptionType2_ComplexOptionType4>(
          &from);
  if (source == NULL) {
  // @@protoc_insertion_point(generalized_merge_from_cast_fail:protobuf_unittest.ComplexOptionType2.ComplexOptionType4)
    ::google::protobuf::internal::ReflectionOps::Merge(from, this);
  } else {
  // @@protoc_insertion_point(generalized_merge_from_cast_success:protobuf_unittest.ComplexOptionType2.ComplexOptionType4)
    UnsafeMergeFrom(*source);
  }
}

void ComplexOptionType2_ComplexOptionType4::MergeFrom(const ComplexOptionType2_ComplexOptionType4& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:protobuf_unittest.ComplexOptionType2.ComplexOptionType4)
  if (GOOGLE_PREDICT_TRUE(&from != this)) {
    UnsafeMergeFrom(from);
  } else {
    MergeFromFail(__LINE__);
  }
}

void ComplexOptionType2_ComplexOptionType4::UnsafeMergeFrom(const ComplexOptionType2_ComplexOptionType4& from) {
  GOOGLE_DCHECK(&from != this);
  if (from._has_bits_[0 / 32] & (0xffu << (0 % 32))) {
    if (from.has_waldo()) {
      set_waldo(from.waldo());
    }
  }
  if (from._internal_metadata_.have_unknown_fields()) {
    ::google::protobuf::UnknownFieldSet::MergeToInternalMetdata(
      from.unknown_fields(), &_internal_metadata_);
  }
}

void ComplexOptionType2_ComplexOptionType4::CopyFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_copy_from_start:protobuf_unittest.ComplexOptionType2.ComplexOptionType4)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

void ComplexOptionType2_ComplexOptionType4::CopyFrom(const ComplexOptionType2_ComplexOptionType4& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:protobuf_unittest.ComplexOptionType2.ComplexOptionType4)
  if (&from == this) return;
  Clear();
  UnsafeMergeFrom(from);
}

bool ComplexOptionType2_ComplexOptionType4::IsInitialized() const {

  return true;
}

void ComplexOptionType2_ComplexOptionType4::Swap(ComplexOptionType2_ComplexOptionType4* other) {
  if (other == this) return;
  InternalSwap(other);
}
void ComplexOptionType2_ComplexOptionType4::InternalSwap(ComplexOptionType2_ComplexOptionType4* other) {
  std::swap(waldo_, other->waldo_);
  std::swap(_has_bits_[0], other->_has_bits_[0]);
  _internal_metadata_.Swap(&other->_internal_metadata_);
  std::swap(_cached_size_, other->_cached_size_);
}

::google::protobuf::Metadata ComplexOptionType2_ComplexOptionType4::GetMetadata() const {
  protobuf_AssignDescriptorsOnce();
  ::google::protobuf::Metadata metadata;
  metadata.descriptor = ComplexOptionType2_ComplexOptionType4_descriptor_;
  metadata.reflection = ComplexOptionType2_ComplexOptionType4_reflection_;
  return metadata;
}


// -------------------------------------------------------------------

#if !defined(_MSC_VER) || _MSC_VER >= 1900
const int ComplexOptionType2::kBarFieldNumber;
const int ComplexOptionType2::kBazFieldNumber;
const int ComplexOptionType2::kFredFieldNumber;
const int ComplexOptionType2::kBarneyFieldNumber;
#endif  // !defined(_MSC_VER) || _MSC_VER >= 1900

ComplexOptionType2::ComplexOptionType2()
  : ::google::protobuf::Message(), _internal_metadata_(NULL) {
  if (this != internal_default_instance()) protobuf_InitDefaults_google_2fprotobuf_2funittest_5fcustom_5foptions_2eproto();
  SharedCtor();
  // @@protoc_insertion_point(constructor:protobuf_unittest.ComplexOptionType2)
}

void ComplexOptionType2::InitAsDefaultInstance() {
  bar_ = const_cast< ::protobuf_unittest::ComplexOptionType1*>(
      ::protobuf_unittest::ComplexOptionType1::internal_default_instance());
  fred_ = const_cast< ::protobuf_unittest::ComplexOptionType2_ComplexOptionType4*>(
      ::protobuf_unittest::ComplexOptionType2_ComplexOptionType4::internal_default_instance());
}

ComplexOptionType2::ComplexOptionType2(const ComplexOptionType2& from)
  : ::google::protobuf::Message(),
    _internal_metadata_(NULL) {
  SharedCtor();
  UnsafeMergeFrom(from);
  // @@protoc_insertion_point(copy_constructor:protobuf_unittest.ComplexOptionType2)
}

void ComplexOptionType2::SharedCtor() {
  _cached_size_ = 0;
  bar_ = NULL;
  fred_ = NULL;
  baz_ = 0;
}

ComplexOptionType2::~ComplexOptionType2() {
  // @@protoc_insertion_point(destructor:protobuf_unittest.ComplexOptionType2)
  SharedDtor();
}

void ComplexOptionType2::SharedDtor() {
  if (this != &ComplexOptionType2_default_instance_.get()) {
    delete bar_;
    delete fred_;
  }
}

void ComplexOptionType2::SetCachedSize(int size) const {
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
}
const ::google::protobuf::Descriptor* ComplexOptionType2::descriptor() {
  protobuf_AssignDescriptorsOnce();
  return ComplexOptionType2_descriptor_;
}

const ComplexOptionType2& ComplexOptionType2::default_instance() {
  protobuf_InitDefaults_google_2fprotobuf_2funittest_5fcustom_5foptions_2eproto();
  return *internal_default_instance();
}

::google::protobuf::internal::ExplicitlyConstructed<ComplexOptionType2> ComplexOptionType2_default_instance_;

ComplexOptionType2* ComplexOptionType2::New(::google::protobuf::Arena* arena) const {
  ComplexOptionType2* n = new ComplexOptionType2;
  if (arena != NULL) {
    arena->Own(n);
  }
  return n;
}

void ComplexOptionType2::Clear() {
// @@protoc_insertion_point(message_clear_start:protobuf_unittest.ComplexOptionType2)
  _extensions_.Clear();
  if (_has_bits_[0 / 32] & 7u) {
    if (has_bar()) {
      if (bar_ != NULL) bar_->::protobuf_unittest::ComplexOptionType1::Clear();
    }
    baz_ = 0;
    if (has_fred()) {
      if (fred_ != NULL) fred_->::protobuf_unittest::ComplexOptionType2_ComplexOptionType4::Clear();
    }
  }
  barney_.Clear();
  _has_bits_.Clear();
  if (_internal_metadata_.have_unknown_fields()) {
    mutable_unknown_fields()->Clear();
  }
}

bool ComplexOptionType2::MergePartialFromCodedStream(
    ::google::protobuf::io::CodedInputStream* input) {
#define DO_(EXPRESSION) if (!GOOGLE_PREDICT_TRUE(EXPRESSION)) goto failure
  ::google::protobuf::uint32 tag;
  // @@protoc_insertion_point(parse_start:protobuf_unittest.ComplexOptionType2)
  for (;;) {
    ::std::pair< ::google::protobuf::uint32, bool> p = input->ReadTagWithCutoff(127);
    tag = p.first;
    if (!p.second) goto handle_unusual;
    switch (::google::protobuf::internal::WireFormatLite::GetTagFieldNumber(tag)) {
      // optional .protobuf_unittest.ComplexOptionType1 bar = 1;
      case 1: {
        if (tag == 10) {
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessageNoVirtual(
               input, mutable_bar()));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(16)) goto parse_baz;
        break;
      }

      // optional int32 baz = 2;
      case 2: {
        if (tag == 16) {
         parse_baz:
          set_has_baz();
          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int32, ::google::protobuf::internal::WireFormatLite::TYPE_INT32>(
                 input, &baz_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(26)) goto parse_fred;
        break;
      }

      // optional .protobuf_unittest.ComplexOptionType2.ComplexOptionType4 fred = 3;
      case 3: {
        if (tag == 26) {
         parse_fred:
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessageNoVirtual(
               input, mutable_fred()));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(34)) goto parse_barney;
        break;
      }

      // repeated .protobuf_unittest.ComplexOptionType2.ComplexOptionType4 barney = 4;
      case 4: {
        if (tag == 34) {
         parse_barney:
          DO_(input->IncrementRecursionDepth());
         parse_loop_barney:
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessageNoVirtualNoRecursionDepth(
                input, add_barney()));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(34)) goto parse_loop_barney;
        input->UnsafeDecrementRecursionDepth();
        if (input->ExpectAtEnd()) goto success;
        break;
      }

      default: {
      handle_unusual:
        if (tag == 0 ||
            ::google::protobuf::internal::WireFormatLite::GetTagWireType(tag) ==
            ::google::protobuf::internal::WireFormatLite::WIRETYPE_END_GROUP) {
          goto success;
        }
        if ((800u <= tag)) {
          DO_(_extensions_.ParseField(tag, input, internal_default_instance(),
                                      mutable_unknown_fields()));
          continue;
        }
        DO_(::google::protobuf::internal::WireFormat::SkipField(
              input, tag, mutable_unknown_fields()));
        break;
      }
    }
  }
success:
  // @@protoc_insertion_point(parse_success:protobuf_unittest.ComplexOptionType2)
  return true;
failure:
  // @@protoc_insertion_point(parse_failure:protobuf_unittest.ComplexOptionType2)
  return false;
#undef DO_
}

void ComplexOptionType2::SerializeWithCachedSizes(
    ::google::protobuf::io::CodedOutputStream* output) const {
  // @@protoc_insertion_point(serialize_start:protobuf_unittest.ComplexOptionType2)
  // optional .protobuf_unittest.ComplexOptionType1 bar = 1;
  if (has_bar()) {
    ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
      1, *this->bar_, output);
  }

  // optional int32 baz = 2;
  if (has_baz()) {
    ::google::protobuf::internal::WireFormatLite::WriteInt32(2, this->baz(), output);
  }

  // optional .protobuf_unittest.ComplexOptionType2.ComplexOptionType4 fred = 3;
  if (has_fred()) {
    ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
      3, *this->fred_, output);
  }

  // repeated .protobuf_unittest.ComplexOptionType2.ComplexOptionType4 barney = 4;
  for (unsigned int i = 0, n = this->barney_size(); i < n; i++) {
    ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
      4, this->barney(i), output);
  }

  // Extension range [100, 536870912)
  _extensions_.SerializeWithCachedSizes(
      100, 536870912, output);

  if (_internal_metadata_.have_unknown_fields()) {
    ::google::protobuf::internal::WireFormat::SerializeUnknownFields(
        unknown_fields(), output);
  }
  // @@protoc_insertion_point(serialize_end:protobuf_unittest.ComplexOptionType2)
}

::google::protobuf::uint8* ComplexOptionType2::InternalSerializeWithCachedSizesToArray(
    bool deterministic, ::google::protobuf::uint8* target) const {
  (void)deterministic; // Unused
  // @@protoc_insertion_point(serialize_to_array_start:protobuf_unittest.ComplexOptionType2)
  // optional .protobuf_unittest.ComplexOptionType1 bar = 1;
  if (has_bar()) {
    target = ::google::protobuf::internal::WireFormatLite::
      InternalWriteMessageNoVirtualToArray(
        1, *this->bar_, false, target);
  }

  // optional int32 baz = 2;
  if (has_baz()) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt32ToArray(2, this->baz(), target);
  }

  // optional .protobuf_unittest.ComplexOptionType2.ComplexOptionType4 fred = 3;
  if (has_fred()) {
    target = ::google::protobuf::internal::WireFormatLite::
      InternalWriteMessageNoVirtualToArray(
        3, *this->fred_, false, target);
  }

  // repeated .protobuf_unittest.ComplexOptionType2.ComplexOptionType4 barney = 4;
  for (unsigned int i = 0, n = this->barney_size(); i < n; i++) {
    target = ::google::protobuf::internal::WireFormatLite::
      InternalWriteMessageNoVirtualToArray(
        4, this->barney(i), false, target);
  }

  // Extension range [100, 536870912)
  target = _extensions_.InternalSerializeWithCachedSizesToArray(
      100, 536870912, false, target);

  if (_internal_metadata_.have_unknown_fields()) {
    target = ::google::protobuf::internal::WireFormat::SerializeUnknownFieldsToArray(
        unknown_fields(), target);
  }
  // @@protoc_insertion_point(serialize_to_array_end:protobuf_unittest.ComplexOptionType2)
  return target;
}

size_t ComplexOptionType2::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:protobuf_unittest.ComplexOptionType2)
  size_t total_size = 0;

  if (_has_bits_[0 / 32] & 7u) {
    // optional .protobuf_unittest.ComplexOptionType1 bar = 1;
    if (has_bar()) {
      total_size += 1 +
        ::google::protobuf::internal::WireFormatLite::MessageSizeNoVirtual(
          *this->bar_);
    }

    // optional int32 baz = 2;
    if (has_baz()) {
      total_size += 1 +
        ::google::protobuf::internal::WireFormatLite::Int32Size(
          this->baz());
    }

    // optional .protobuf_unittest.ComplexOptionType2.ComplexOptionType4 fred = 3;
    if (has_fred()) {
      total_size += 1 +
        ::google::protobuf::internal::WireFormatLite::MessageSizeNoVirtual(
          *this->fred_);
    }

  }
  // repeated .protobuf_unittest.ComplexOptionType2.ComplexOptionType4 barney = 4;
  {
    unsigned int count = this->barney_size();
    total_size += 1UL * count;
    for (unsigned int i = 0; i < count; i++) {
      total_size +=
        ::google::protobuf::internal::WireFormatLite::MessageSizeNoVirtual(
          this->barney(i));
    }
  }

  total_size += _extensions_.ByteSize();

  if (_internal_metadata_.have_unknown_fields()) {
    total_size +=
      ::google::protobuf::internal::WireFormat::ComputeUnknownFieldsSize(
        unknown_fields());
  }
  int cached_size = ::google::protobuf::internal::ToCachedSize(total_size);
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = cached_size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
  return total_size;
}

void ComplexOptionType2::MergeFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_merge_from_start:protobuf_unittest.ComplexOptionType2)
  if (GOOGLE_PREDICT_FALSE(&from == this)) MergeFromFail(__LINE__);
  const ComplexOptionType2* source =
      ::google::protobuf::internal::DynamicCastToGenerated<const ComplexOptionType2>(
          &from);
  if (source == NULL) {
  // @@protoc_insertion_point(generalized_merge_from_cast_fail:protobuf_unittest.ComplexOptionType2)
    ::google::protobuf::internal::ReflectionOps::Merge(from, this);
  } else {
  // @@protoc_insertion_point(generalized_merge_from_cast_success:protobuf_unittest.ComplexOptionType2)
    UnsafeMergeFrom(*source);
  }
}

void ComplexOptionType2::MergeFrom(const ComplexOptionType2& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:protobuf_unittest.ComplexOptionType2)
  if (GOOGLE_PREDICT_TRUE(&from != this)) {
    UnsafeMergeFrom(from);
  } else {
    MergeFromFail(__LINE__);
  }
}

void ComplexOptionType2::UnsafeMergeFrom(const ComplexOptionType2& from) {
  GOOGLE_DCHECK(&from != this);
  barney_.MergeFrom(from.barney_);
  if (from._has_bits_[0 / 32] & (0xffu << (0 % 32))) {
    if (from.has_bar()) {
      mutable_bar()->::protobuf_unittest::ComplexOptionType1::MergeFrom(from.bar());
    }
    if (from.has_baz()) {
      set_baz(from.baz());
    }
    if (from.has_fred()) {
      mutable_fred()->::protobuf_unittest::ComplexOptionType2_ComplexOptionType4::MergeFrom(from.fred());
    }
  }
  _extensions_.MergeFrom(from._extensions_);
  if (from._internal_metadata_.have_unknown_fields()) {
    ::google::protobuf::UnknownFieldSet::MergeToInternalMetdata(
      from.unknown_fields(), &_internal_metadata_);
  }
}

void ComplexOptionType2::CopyFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_copy_from_start:protobuf_unittest.ComplexOptionType2)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

void ComplexOptionType2::CopyFrom(const ComplexOptionType2& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:protobuf_unittest.ComplexOptionType2)
  if (&from == this) return;
  Clear();
  UnsafeMergeFrom(from);
}

bool ComplexOptionType2::IsInitialized() const {

  if (has_bar()) {
    if (!this->bar_->IsInitialized()) return false;
  }

  if (!_extensions_.IsInitialized()) {
    return false;
  }
  return true;
}

void ComplexOptionType2::Swap(ComplexOptionType2* other) {
  if (other == this) return;
  InternalSwap(other);
}
void ComplexOptionType2::InternalSwap(ComplexOptionType2* other) {
  std::swap(bar_, other->bar_);
  std::swap(baz_, other->baz_);
  std::swap(fred_, other->fred_);
  barney_.UnsafeArenaSwap(&other->barney_);
  std::swap(_has_bits_[0], other->_has_bits_[0]);
  _internal_metadata_.Swap(&other->_internal_metadata_);
  std::swap(_cached_size_, other->_cached_size_);
  _extensions_.Swap(&other->_extensions_);
}

::google::protobuf::Metadata ComplexOptionType2::GetMetadata() const {
  protobuf_AssignDescriptorsOnce();
  ::google::protobuf::Metadata metadata;
  metadata.descriptor = ComplexOptionType2_descriptor_;
  metadata.reflection = ComplexOptionType2_reflection_;
  return metadata;
}

#if PROTOBUF_INLINE_NOT_IN_HEADERS
// ComplexOptionType2_ComplexOptionType4

// optional int32 waldo = 1;
bool ComplexOptionType2_ComplexOptionType4::has_waldo() const {
  return (_has_bits_[0] & 0x00000001u) != 0;
}
void ComplexOptionType2_ComplexOptionType4::set_has_waldo() {
  _has_bits_[0] |= 0x00000001u;
}
void ComplexOptionType2_ComplexOptionType4::clear_has_waldo() {
  _has_bits_[0] &= ~0x00000001u;
}
void ComplexOptionType2_ComplexOptionType4::clear_waldo() {
  waldo_ = 0;
  clear_has_waldo();
}
::google::protobuf::int32 ComplexOptionType2_ComplexOptionType4::waldo() const {
  // @@protoc_insertion_point(field_get:protobuf_unittest.ComplexOptionType2.ComplexOptionType4.waldo)
  return waldo_;
}
void ComplexOptionType2_ComplexOptionType4::set_waldo(::google::protobuf::int32 value) {
  set_has_waldo();
  waldo_ = value;
  // @@protoc_insertion_point(field_set:protobuf_unittest.ComplexOptionType2.ComplexOptionType4.waldo)
}

inline const ComplexOptionType2_ComplexOptionType4* ComplexOptionType2_ComplexOptionType4::internal_default_instance() {
  return &ComplexOptionType2_ComplexOptionType4_default_instance_.get();
}
// -------------------------------------------------------------------

// ComplexOptionType2

// optional .protobuf_unittest.ComplexOptionType1 bar = 1;
bool ComplexOptionType2::has_bar() const {
  return (_has_bits_[0] & 0x00000001u) != 0;
}
void ComplexOptionType2::set_has_bar() {
  _has_bits_[0] |= 0x00000001u;
}
void ComplexOptionType2::clear_has_bar() {
  _has_bits_[0] &= ~0x00000001u;
}
void ComplexOptionType2::clear_bar() {
  if (bar_ != NULL) bar_->::protobuf_unittest::ComplexOptionType1::Clear();
  clear_has_bar();
}
const ::protobuf_unittest::ComplexOptionType1& ComplexOptionType2::bar() const {
  // @@protoc_insertion_point(field_get:protobuf_unittest.ComplexOptionType2.bar)
  return bar_ != NULL ? *bar_
                         : *::protobuf_unittest::ComplexOptionType1::internal_default_instance();
}
::protobuf_unittest::ComplexOptionType1* ComplexOptionType2::mutable_bar() {
  set_has_bar();
  if (bar_ == NULL) {
    bar_ = new ::protobuf_unittest::ComplexOptionType1;
  }
  // @@protoc_insertion_point(field_mutable:protobuf_unittest.ComplexOptionType2.bar)
  return bar_;
}
::protobuf_unittest::ComplexOptionType1* ComplexOptionType2::release_bar() {
  // @@protoc_insertion_point(field_release:protobuf_unittest.ComplexOptionType2.bar)
  clear_has_bar();
  ::protobuf_unittest::ComplexOptionType1* temp = bar_;
  bar_ = NULL;
  return temp;
}
void ComplexOptionType2::set_allocated_bar(::protobuf_unittest::ComplexOptionType1* bar) {
  delete bar_;
  bar_ = bar;
  if (bar) {
    set_has_bar();
  } else {
    clear_has_bar();
  }
  // @@protoc_insertion_point(field_set_allocated:protobuf_unittest.ComplexOptionType2.bar)
}

// optional int32 baz = 2;
bool ComplexOptionType2::has_baz() const {
  return (_has_bits_[0] & 0x00000002u) != 0;
}
void ComplexOptionType2::set_has_baz() {
  _has_bits_[0] |= 0x00000002u;
}
void ComplexOptionType2::clear_has_baz() {
  _has_bits_[0] &= ~0x00000002u;
}
void ComplexOptionType2::clear_baz() {
  baz_ = 0;
  clear_has_baz();
}
::google::protobuf::int32 ComplexOptionType2::baz() const {
  // @@protoc_insertion_point(field_get:protobuf_unittest.ComplexOptionType2.baz)
  return baz_;
}
void ComplexOptionType2::set_baz(::google::protobuf::int32 value) {
  set_has_baz();
  baz_ = value;
  // @@protoc_insertion_point(field_set:protobuf_unittest.ComplexOptionType2.baz)
}

// optional .protobuf_unittest.ComplexOptionType2.ComplexOptionType4 fred = 3;
bool ComplexOptionType2::has_fred() const {
  return (_has_bits_[0] & 0x00000004u) != 0;
}
void ComplexOptionType2::set_has_fred() {
  _has_bits_[0] |= 0x00000004u;
}
void ComplexOptionType2::clear_has_fred() {
  _has_bits_[0] &= ~0x00000004u;
}
void ComplexOptionType2::clear_fred() {
  if (fred_ != NULL) fred_->::protobuf_unittest::ComplexOptionType2_ComplexOptionType4::Clear();
  clear_has_fred();
}
const ::protobuf_unittest::ComplexOptionType2_ComplexOptionType4& ComplexOptionType2::fred() const {
  // @@protoc_insertion_point(field_get:protobuf_unittest.ComplexOptionType2.fred)
  return fred_ != NULL ? *fred_
                         : *::protobuf_unittest::ComplexOptionType2_ComplexOptionType4::internal_default_instance();
}
::protobuf_unittest::ComplexOptionType2_ComplexOptionType4* ComplexOptionType2::mutable_fred() {
  set_has_fred();
  if (fred_ == NULL) {
    fred_ = new ::protobuf_unittest::ComplexOptionType2_ComplexOptionType4;
  }
  // @@protoc_insertion_point(field_mutable:protobuf_unittest.ComplexOptionType2.fred)
  return fred_;
}
::protobuf_unittest::ComplexOptionType2_ComplexOptionType4* ComplexOptionType2::release_fred() {
  // @@protoc_insertion_point(field_release:protobuf_unittest.ComplexOptionType2.fred)
  clear_has_fred();
  ::protobuf_unittest::ComplexOptionType2_ComplexOptionType4* temp = fred_;
  fred_ = NULL;
  return temp;
}
void ComplexOptionType2::set_allocated_fred(::protobuf_unittest::ComplexOptionType2_ComplexOptionType4* fred) {
  delete fred_;
  fred_ = fred;
  if (fred) {
    set_has_fred();
  } else {
    clear_has_fred();
  }
  // @@protoc_insertion_point(field_set_allocated:protobuf_unittest.ComplexOptionType2.fred)
}

// repeated .protobuf_unittest.ComplexOptionType2.ComplexOptionType4 barney = 4;
int ComplexOptionType2::barney_size() const {
  return barney_.size();
}
void ComplexOptionType2::clear_barney() {
  barney_.Clear();
}
const ::protobuf_unittest::ComplexOptionType2_ComplexOptionType4& ComplexOptionType2::barney(int index) const {
  // @@protoc_insertion_point(field_get:protobuf_unittest.ComplexOptionType2.barney)
  return barney_.Get(index);
}
::protobuf_unittest::ComplexOptionType2_ComplexOptionType4* ComplexOptionType2::mutable_barney(int index) {
  // @@protoc_insertion_point(field_mutable:protobuf_unittest.ComplexOptionType2.barney)
  return barney_.Mutable(index);
}
::protobuf_unittest::ComplexOptionType2_ComplexOptionType4* ComplexOptionType2::add_barney() {
  // @@protoc_insertion_point(field_add:protobuf_unittest.ComplexOptionType2.barney)
  return barney_.Add();
}
::google::protobuf::RepeatedPtrField< ::protobuf_unittest::ComplexOptionType2_ComplexOptionType4 >*
ComplexOptionType2::mutable_barney() {
  // @@protoc_insertion_point(field_mutable_list:protobuf_unittest.ComplexOptionType2.barney)
  return &barney_;
}
const ::google::protobuf::RepeatedPtrField< ::protobuf_unittest::ComplexOptionType2_ComplexOptionType4 >&
ComplexOptionType2::barney() const {
  // @@protoc_insertion_point(field_list:protobuf_unittest.ComplexOptionType2.barney)
  return barney_;
}

inline const ComplexOptionType2* ComplexOptionType2::internal_default_instance() {
  return &ComplexOptionType2_default_instance_.get();
}
#endif  // PROTOBUF_INLINE_NOT_IN_HEADERS

// ===================================================================

#if !defined(_MSC_VER) || _MSC_VER >= 1900
const int ComplexOptionType3_ComplexOptionType5::kPlughFieldNumber;
#endif  // !defined(_MSC_VER) || _MSC_VER >= 1900

ComplexOptionType3_ComplexOptionType5::ComplexOptionType3_ComplexOptionType5()
  : ::google::protobuf::Message(), _internal_metadata_(NULL) {
  if (this != internal_default_instance()) protobuf_InitDefaults_google_2fprotobuf_2funittest_5fcustom_5foptions_2eproto();
  SharedCtor();
  // @@protoc_insertion_point(constructor:protobuf_unittest.ComplexOptionType3.ComplexOptionType5)
}

void ComplexOptionType3_ComplexOptionType5::InitAsDefaultInstance() {
}

ComplexOptionType3_ComplexOptionType5::ComplexOptionType3_ComplexOptionType5(const ComplexOptionType3_ComplexOptionType5& from)
  : ::google::protobuf::Message(),
    _internal_metadata_(NULL) {
  SharedCtor();
  UnsafeMergeFrom(from);
  // @@protoc_insertion_point(copy_constructor:protobuf_unittest.ComplexOptionType3.ComplexOptionType5)
}

void ComplexOptionType3_ComplexOptionType5::SharedCtor() {
  _cached_size_ = 0;
  plugh_ = 0;
}

ComplexOptionType3_ComplexOptionType5::~ComplexOptionType3_ComplexOptionType5() {
  // @@protoc_insertion_point(destructor:protobuf_unittest.ComplexOptionType3.ComplexOptionType5)
  SharedDtor();
}

void ComplexOptionType3_ComplexOptionType5::SharedDtor() {
}

void ComplexOptionType3_ComplexOptionType5::SetCachedSize(int size) const {
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
}
const ::google::protobuf::Descriptor* ComplexOptionType3_ComplexOptionType5::descriptor() {
  protobuf_AssignDescriptorsOnce();
  return ComplexOptionType3_ComplexOptionType5_descriptor_;
}

const ComplexOptionType3_ComplexOptionType5& ComplexOptionType3_ComplexOptionType5::default_instance() {
  protobuf_InitDefaults_google_2fprotobuf_2funittest_5fcustom_5foptions_2eproto();
  return *internal_default_instance();
}

::google::protobuf::internal::ExplicitlyConstructed<ComplexOptionType3_ComplexOptionType5> ComplexOptionType3_ComplexOptionType5_default_instance_;

ComplexOptionType3_ComplexOptionType5* ComplexOptionType3_ComplexOptionType5::New(::google::protobuf::Arena* arena) const {
  ComplexOptionType3_ComplexOptionType5* n = new ComplexOptionType3_ComplexOptionType5;
  if (arena != NULL) {
    arena->Own(n);
  }
  return n;
}

void ComplexOptionType3_ComplexOptionType5::Clear() {
// @@protoc_insertion_point(message_clear_start:protobuf_unittest.ComplexOptionType3.ComplexOptionType5)
  plugh_ = 0;
  _has_bits_.Clear();
  if (_internal_metadata_.have_unknown_fields()) {
    mutable_unknown_fields()->Clear();
  }
}

bool ComplexOptionType3_ComplexOptionType5::MergePartialFromCodedStream(
    ::google::protobuf::io::CodedInputStream* input) {
#define DO_(EXPRESSION) if (!GOOGLE_PREDICT_TRUE(EXPRESSION)) goto failure
  ::google::protobuf::uint32 tag;
  // @@protoc_insertion_point(parse_start:protobuf_unittest.ComplexOptionType3.ComplexOptionType5)
  for (;;) {
    ::std::pair< ::google::protobuf::uint32, bool> p = input->ReadTagWithCutoff(127);
    tag = p.first;
    if (!p.second) goto handle_unusual;
    switch (::google::protobuf::internal::WireFormatLite::GetTagFieldNumber(tag)) {
      // optional int32 plugh = 3;
      case 3: {
        if (tag == 24) {
          set_has_plugh();
          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int32, ::google::protobuf::internal::WireFormatLite::TYPE_INT32>(
                 input, &plugh_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectAtEnd()) goto success;
        break;
      }

      default: {
      handle_unusual:
        if (tag == 0 ||
            ::google::protobuf::internal::WireFormatLite::GetTagWireType(tag) ==
            ::google::protobuf::internal::WireFormatLite::WIRETYPE_END_GROUP) {
          goto success;
        }
        DO_(::google::protobuf::internal::WireFormat::SkipField(
              input, tag, mutable_unknown_fields()));
        break;
      }
    }
  }
success:
  // @@protoc_insertion_point(parse_success:protobuf_unittest.ComplexOptionType3.ComplexOptionType5)
  return true;
failure:
  // @@protoc_insertion_point(parse_failure:protobuf_unittest.ComplexOptionType3.ComplexOptionType5)
  return false;
#undef DO_
}

void ComplexOptionType3_ComplexOptionType5::SerializeWithCachedSizes(
    ::google::protobuf::io::CodedOutputStream* output) const {
  // @@protoc_insertion_point(serialize_start:protobuf_unittest.ComplexOptionType3.ComplexOptionType5)
  // optional int32 plugh = 3;
  if (has_plugh()) {
    ::google::protobuf::internal::WireFormatLite::WriteInt32(3, this->plugh(), output);
  }

  if (_internal_metadata_.have_unknown_fields()) {
    ::google::protobuf::internal::WireFormat::SerializeUnknownFields(
        unknown_fields(), output);
  }
  // @@protoc_insertion_point(serialize_end:protobuf_unittest.ComplexOptionType3.ComplexOptionType5)
}

::google::protobuf::uint8* ComplexOptionType3_ComplexOptionType5::InternalSerializeWithCachedSizesToArray(
    bool deterministic, ::google::protobuf::uint8* target) const {
  (void)deterministic; // Unused
  // @@protoc_insertion_point(serialize_to_array_start:protobuf_unittest.ComplexOptionType3.ComplexOptionType5)
  // optional int32 plugh = 3;
  if (has_plugh()) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt32ToArray(3, this->plugh(), target);
  }

  if (_internal_metadata_.have_unknown_fields()) {
    target = ::google::protobuf::internal::WireFormat::SerializeUnknownFieldsToArray(
        unknown_fields(), target);
  }
  // @@protoc_insertion_point(serialize_to_array_end:protobuf_unittest.ComplexOptionType3.ComplexOptionType5)
  return target;
}

size_t ComplexOptionType3_ComplexOptionType5::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:protobuf_unittest.ComplexOptionType3.ComplexOptionType5)
  size_t total_size = 0;

  // optional int32 plugh = 3;
  if (has_plugh()) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::Int32Size(
        this->plugh());
  }

  if (_internal_metadata_.have_unknown_fields()) {
    total_size +=
      ::google::protobuf::internal::WireFormat::ComputeUnknownFieldsSize(
        unknown_fields());
  }
  int cached_size = ::google::protobuf::internal::ToCachedSize(total_size);
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = cached_size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
  return total_size;
}

void ComplexOptionType3_ComplexOptionType5::MergeFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_merge_from_start:protobuf_unittest.ComplexOptionType3.ComplexOptionType5)
  if (GOOGLE_PREDICT_FALSE(&from == this)) MergeFromFail(__LINE__);
  const ComplexOptionType3_ComplexOptionType5* source =
      ::google::protobuf::internal::DynamicCastToGenerated<const ComplexOptionType3_ComplexOptionType5>(
          &from);
  if (source == NULL) {
  // @@protoc_insertion_point(generalized_merge_from_cast_fail:protobuf_unittest.ComplexOptionType3.ComplexOptionType5)
    ::google::protobuf::internal::ReflectionOps::Merge(from, this);
  } else {
  // @@protoc_insertion_point(generalized_merge_from_cast_success:protobuf_unittest.ComplexOptionType3.ComplexOptionType5)
    UnsafeMergeFrom(*source);
  }
}

void ComplexOptionType3_ComplexOptionType5::MergeFrom(const ComplexOptionType3_ComplexOptionType5& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:protobuf_unittest.ComplexOptionType3.ComplexOptionType5)
  if (GOOGLE_PREDICT_TRUE(&from != this)) {
    UnsafeMergeFrom(from);
  } else {
    MergeFromFail(__LINE__);
  }
}

void ComplexOptionType3_ComplexOptionType5::UnsafeMergeFrom(const ComplexOptionType3_ComplexOptionType5& from) {
  GOOGLE_DCHECK(&from != this);
  if (from._has_bits_[0 / 32] & (0xffu << (0 % 32))) {
    if (from.has_plugh()) {
      set_plugh(from.plugh());
    }
  }
  if (from._internal_metadata_.have_unknown_fields()) {
    ::google::protobuf::UnknownFieldSet::MergeToInternalMetdata(
      from.unknown_fields(), &_internal_metadata_);
  }
}

void ComplexOptionType3_ComplexOptionType5::CopyFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_copy_from_start:protobuf_unittest.ComplexOptionType3.ComplexOptionType5)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

void ComplexOptionType3_ComplexOptionType5::CopyFrom(const ComplexOptionType3_ComplexOptionType5& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:protobuf_unittest.ComplexOptionType3.ComplexOptionType5)
  if (&from == this) return;
  Clear();
  UnsafeMergeFrom(from);
}

bool ComplexOptionType3_ComplexOptionType5::IsInitialized() const {

  return true;
}

void ComplexOptionType3_ComplexOptionType5::Swap(ComplexOptionType3_ComplexOptionType5* other) {
  if (other == this) return;
  InternalSwap(other);
}
void ComplexOptionType3_ComplexOptionType5::InternalSwap(ComplexOptionType3_ComplexOptionType5* other) {
  std::swap(plugh_, other->plugh_);
  std::swap(_has_bits_[0], other->_has_bits_[0]);
  _internal_metadata_.Swap(&other->_internal_metadata_);
  std::swap(_cached_size_, other->_cached_size_);
}

::google::protobuf::Metadata ComplexOptionType3_ComplexOptionType5::GetMetadata() const {
  protobuf_AssignDescriptorsOnce();
  ::google::protobuf::Metadata metadata;
  metadata.descriptor = ComplexOptionType3_ComplexOptionType5_descriptor_;
  metadata.reflection = ComplexOptionType3_ComplexOptionType5_reflection_;
  return metadata;
}


// -------------------------------------------------------------------

#if !defined(_MSC_VER) || _MSC_VER >= 1900
const int ComplexOptionType3::kQuxFieldNumber;
const int ComplexOptionType3::kComplexoptiontype5FieldNumber;
#endif  // !defined(_MSC_VER) || _MSC_VER >= 1900

ComplexOptionType3::ComplexOptionType3()
  : ::google::protobuf::Message(), _internal_metadata_(NULL) {
  if (this != internal_default_instance()) protobuf_InitDefaults_google_2fprotobuf_2funittest_5fcustom_5foptions_2eproto();
  SharedCtor();
  // @@protoc_insertion_point(constructor:protobuf_unittest.ComplexOptionType3)
}

void ComplexOptionType3::InitAsDefaultInstance() {
  complexoptiontype5_ = const_cast< ::protobuf_unittest::ComplexOptionType3_ComplexOptionType5*>(
      ::protobuf_unittest::ComplexOptionType3_ComplexOptionType5::internal_default_instance());
}

ComplexOptionType3::ComplexOptionType3(const ComplexOptionType3& from)
  : ::google::protobuf::Message(),
    _internal_metadata_(NULL) {
  SharedCtor();
  UnsafeMergeFrom(from);
  // @@protoc_insertion_point(copy_constructor:protobuf_unittest.ComplexOptionType3)
}

void ComplexOptionType3::SharedCtor() {
  _cached_size_ = 0;
  complexoptiontype5_ = NULL;
  qux_ = 0;
}

ComplexOptionType3::~ComplexOptionType3() {
  // @@protoc_insertion_point(destructor:protobuf_unittest.ComplexOptionType3)
  SharedDtor();
}

void ComplexOptionType3::SharedDtor() {
  if (this != &ComplexOptionType3_default_instance_.get()) {
    delete complexoptiontype5_;
  }
}

void ComplexOptionType3::SetCachedSize(int size) const {
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
}
const ::google::protobuf::Descriptor* ComplexOptionType3::descriptor() {
  protobuf_AssignDescriptorsOnce();
  return ComplexOptionType3_descriptor_;
}

const ComplexOptionType3& ComplexOptionType3::default_instance() {
  protobuf_InitDefaults_google_2fprotobuf_2funittest_5fcustom_5foptions_2eproto();
  return *internal_default_instance();
}

::google::protobuf::internal::ExplicitlyConstructed<ComplexOptionType3> ComplexOptionType3_default_instance_;

ComplexOptionType3* ComplexOptionType3::New(::google::protobuf::Arena* arena) const {
  ComplexOptionType3* n = new ComplexOptionType3;
  if (arena != NULL) {
    arena->Own(n);
  }
  return n;
}

void ComplexOptionType3::Clear() {
// @@protoc_insertion_point(message_clear_start:protobuf_unittest.ComplexOptionType3)
  if (_has_bits_[0 / 32] & 3u) {
    qux_ = 0;
    if (has_complexoptiontype5()) {
      if (complexoptiontype5_ != NULL) complexoptiontype5_->::protobuf_unittest::ComplexOptionType3_ComplexOptionType5::Clear();
    }
  }
  _has_bits_.Clear();
  if (_internal_metadata_.have_unknown_fields()) {
    mutable_unknown_fields()->Clear();
  }
}

bool ComplexOptionType3::MergePartialFromCodedStream(
    ::google::protobuf::io::CodedInputStream* input) {
#define DO_(EXPRESSION) if (!GOOGLE_PREDICT_TRUE(EXPRESSION)) goto failure
  ::google::protobuf::uint32 tag;
  // @@protoc_insertion_point(parse_start:protobuf_unittest.ComplexOptionType3)
  for (;;) {
    ::std::pair< ::google::protobuf::uint32, bool> p = input->ReadTagWithCutoff(127);
    tag = p.first;
    if (!p.second) goto handle_unusual;
    switch (::google::protobuf::internal::WireFormatLite::GetTagFieldNumber(tag)) {
      // optional int32 qux = 1;
      case 1: {
        if (tag == 8) {
          set_has_qux();
          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int32, ::google::protobuf::internal::WireFormatLite::TYPE_INT32>(
                 input, &qux_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(19)) goto parse_complexoptiontype5;
        break;
      }

      // optional group ComplexOptionType5 = 2 { ... };
      case 2: {
        if (tag == 19) {
         parse_complexoptiontype5:
          DO_(::google::protobuf::internal::WireFormatLite::ReadGroupNoVirtual(
                2, input, mutable_complexoptiontype5()));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectAtEnd()) goto success;
        break;
      }

      default: {
      handle_unusual:
        if (tag == 0 ||
            ::google::protobuf::internal::WireFormatLite::GetTagWireType(tag) ==
            ::google::protobuf::internal::WireFormatLite::WIRETYPE_END_GROUP) {
          goto success;
        }
        DO_(::google::protobuf::internal::WireFormat::SkipField(
              input, tag, mutable_unknown_fields()));
        break;
      }
    }
  }
success:
  // @@protoc_insertion_point(parse_success:protobuf_unittest.ComplexOptionType3)
  return true;
failure:
  // @@protoc_insertion_point(parse_failure:protobuf_unittest.ComplexOptionType3)
  return false;
#undef DO_
}

void ComplexOptionType3::SerializeWithCachedSizes(
    ::google::protobuf::io::CodedOutputStream* output) const {
  // @@protoc_insertion_point(serialize_start:protobuf_unittest.ComplexOptionType3)
  // optional int32 qux = 1;
  if (has_qux()) {
    ::google::protobuf::internal::WireFormatLite::WriteInt32(1, this->qux(), output);
  }

  // optional group ComplexOptionType5 = 2 { ... };
  if (has_complexoptiontype5()) {
    ::google::protobuf::internal::WireFormatLite::WriteGroupMaybeToArray(
      2, *this->complexoptiontype5_, output);
  }

  if (_internal_metadata_.have_unknown_fields()) {
    ::google::protobuf::internal::WireFormat::SerializeUnknownFields(
        unknown_fields(), output);
  }
  // @@protoc_insertion_point(serialize_end:protobuf_unittest.ComplexOptionType3)
}

::google::protobuf::uint8* ComplexOptionType3::InternalSerializeWithCachedSizesToArray(
    bool deterministic, ::google::protobuf::uint8* target) const {
  (void)deterministic; // Unused
  // @@protoc_insertion_point(serialize_to_array_start:protobuf_unittest.ComplexOptionType3)
  // optional int32 qux = 1;
  if (has_qux()) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt32ToArray(1, this->qux(), target);
  }

  // optional group ComplexOptionType5 = 2 { ... };
  if (has_complexoptiontype5()) {
    target = ::google::protobuf::internal::WireFormatLite::
      InternalWriteGroupNoVirtualToArray(
        2, *this->complexoptiontype5_, false, target);
  }

  if (_internal_metadata_.have_unknown_fields()) {
    target = ::google::protobuf::internal::WireFormat::SerializeUnknownFieldsToArray(
        unknown_fields(), target);
  }
  // @@protoc_insertion_point(serialize_to_array_end:protobuf_unittest.ComplexOptionType3)
  return target;
}

size_t ComplexOptionType3::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:protobuf_unittest.ComplexOptionType3)
  size_t total_size = 0;

  if (_has_bits_[0 / 32] & 3u) {
    // optional int32 qux = 1;
    if (has_qux()) {
      total_size += 1 +
        ::google::protobuf::internal::WireFormatLite::Int32Size(
          this->qux());
    }

    // optional group ComplexOptionType5 = 2 { ... };
    if (has_complexoptiontype5()) {
      total_size += 2 +
        ::google::protobuf::internal::WireFormatLite::GroupSizeNoVirtual(
          *this->complexoptiontype5_);
    }

  }
  if (_internal_metadata_.have_unknown_fields()) {
    total_size +=
      ::google::protobuf::internal::WireFormat::ComputeUnknownFieldsSize(
        unknown_fields());
  }
  int cached_size = ::google::protobuf::internal::ToCachedSize(total_size);
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = cached_size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
  return total_size;
}

void ComplexOptionType3::MergeFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_merge_from_start:protobuf_unittest.ComplexOptionType3)
  if (GOOGLE_PREDICT_FALSE(&from == this)) MergeFromFail(__LINE__);
  const ComplexOptionType3* source =
      ::google::protobuf::internal::DynamicCastToGenerated<const ComplexOptionType3>(
          &from);
  if (source == NULL) {
  // @@protoc_insertion_point(generalized_merge_from_cast_fail:protobuf_unittest.ComplexOptionType3)
    ::google::protobuf::internal::ReflectionOps::Merge(from, this);
  } else {
  // @@protoc_insertion_point(generalized_merge_from_cast_success:protobuf_unittest.ComplexOptionType3)
    UnsafeMergeFrom(*source);
  }
}

void ComplexOptionType3::MergeFrom(const ComplexOptionType3& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:protobuf_unittest.ComplexOptionType3)
  if (GOOGLE_PREDICT_TRUE(&from != this)) {
    UnsafeMergeFrom(from);
  } else {
    MergeFromFail(__LINE__);
  }
}

void ComplexOptionType3::UnsafeMergeFrom(const ComplexOptionType3& from) {
  GOOGLE_DCHECK(&from != this);
  if (from._has_bits_[0 / 32] & (0xffu << (0 % 32))) {
    if (from.has_qux()) {
      set_qux(from.qux());
    }
    if (from.has_complexoptiontype5()) {
      mutable_complexoptiontype5()->::protobuf_unittest::ComplexOptionType3_ComplexOptionType5::MergeFrom(from.complexoptiontype5());
    }
  }
  if (from._internal_metadata_.have_unknown_fields()) {
    ::google::protobuf::UnknownFieldSet::MergeToInternalMetdata(
      from.unknown_fields(), &_internal_metadata_);
  }
}

void ComplexOptionType3::CopyFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_copy_from_start:protobuf_unittest.ComplexOptionType3)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

void ComplexOptionType3::CopyFrom(const ComplexOptionType3& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:protobuf_unittest.ComplexOptionType3)
  if (&from == this) return;
  Clear();
  UnsafeMergeFrom(from);
}

bool ComplexOptionType3::IsInitialized() const {

  return true;
}

void ComplexOptionType3::Swap(ComplexOptionType3* other) {
  if (other == this) return;
  InternalSwap(other);
}
void ComplexOptionType3::InternalSwap(ComplexOptionType3* other) {
  std::swap(qux_, other->qux_);
  std::swap(complexoptiontype5_, other->complexoptiontype5_);
  std::swap(_has_bits_[0], other->_has_bits_[0]);
  _internal_metadata_.Swap(&other->_internal_metadata_);
  std::swap(_cached_size_, other->_cached_size_);
}

::google::protobuf::Metadata ComplexOptionType3::GetMetadata() const {
  protobuf_AssignDescriptorsOnce();
  ::google::protobuf::Metadata metadata;
  metadata.descriptor = ComplexOptionType3_descriptor_;
  metadata.reflection = ComplexOptionType3_reflection_;
  return metadata;
}

#if PROTOBUF_INLINE_NOT_IN_HEADERS
// ComplexOptionType3_ComplexOptionType5

// optional int32 plugh = 3;
bool ComplexOptionType3_ComplexOptionType5::has_plugh() const {
  return (_has_bits_[0] & 0x00000001u) != 0;
}
void ComplexOptionType3_ComplexOptionType5::set_has_plugh() {
  _has_bits_[0] |= 0x00000001u;
}
void ComplexOptionType3_ComplexOptionType5::clear_has_plugh() {
  _has_bits_[0] &= ~0x00000001u;
}
void ComplexOptionType3_ComplexOptionType5::clear_plugh() {
  plugh_ = 0;
  clear_has_plugh();
}
::google::protobuf::int32 ComplexOptionType3_ComplexOptionType5::plugh() const {
  // @@protoc_insertion_point(field_get:protobuf_unittest.ComplexOptionType3.ComplexOptionType5.plugh)
  return plugh_;
}
void ComplexOptionType3_ComplexOptionType5::set_plugh(::google::protobuf::int32 value) {
  set_has_plugh();
  plugh_ = value;
  // @@protoc_insertion_point(field_set:protobuf_unittest.ComplexOptionType3.ComplexOptionType5.plugh)
}

inline const ComplexOptionType3_ComplexOptionType5* ComplexOptionType3_ComplexOptionType5::internal_default_instance() {
  return &ComplexOptionType3_ComplexOptionType5_default_instance_.get();
}
// -------------------------------------------------------------------

// ComplexOptionType3

// optional int32 qux = 1;
bool ComplexOptionType3::has_qux() const {
  return (_has_bits_[0] & 0x00000001u) != 0;
}
void ComplexOptionType3::set_has_qux() {
  _has_bits_[0] |= 0x00000001u;
}
void ComplexOptionType3::clear_has_qux() {
  _has_bits_[0] &= ~0x00000001u;
}
void ComplexOptionType3::clear_qux() {
  qux_ = 0;
  clear_has_qux();
}
::google::protobuf::int32 ComplexOptionType3::qux() const {
  // @@protoc_insertion_point(field_get:protobuf_unittest.ComplexOptionType3.qux)
  return qux_;
}
void ComplexOptionType3::set_qux(::google::protobuf::int32 value) {
  set_has_qux();
  qux_ = value;
  // @@protoc_insertion_point(field_set:protobuf_unittest.ComplexOptionType3.qux)
}

// optional group ComplexOptionType5 = 2 { ... };
bool ComplexOptionType3::has_complexoptiontype5() const {
  return (_has_bits_[0] & 0x00000002u) != 0;
}
void ComplexOptionType3::set_has_complexoptiontype5() {
  _has_bits_[0] |= 0x00000002u;
}
void ComplexOptionType3::clear_has_complexoptiontype5() {
  _has_bits_[0] &= ~0x00000002u;
}
void ComplexOptionType3::clear_complexoptiontype5() {
  if (complexoptiontype5_ != NULL) complexoptiontype5_->::protobuf_unittest::ComplexOptionType3_ComplexOptionType5::Clear();
  clear_has_complexoptiontype5();
}
const ::protobuf_unittest::ComplexOptionType3_ComplexOptionType5& ComplexOptionType3::complexoptiontype5() const {
  // @@protoc_insertion_point(field_get:protobuf_unittest.ComplexOptionType3.complexoptiontype5)
  return complexoptiontype5_ != NULL ? *complexoptiontype5_
                         : *::protobuf_unittest::ComplexOptionType3_ComplexOptionType5::internal_default_instance();
}
::protobuf_unittest::ComplexOptionType3_ComplexOptionType5* ComplexOptionType3::mutable_complexoptiontype5() {
  set_has_complexoptiontype5();
  if (complexoptiontype5_ == NULL) {
    complexoptiontype5_ = new ::protobuf_unittest::ComplexOptionType3_ComplexOptionType5;
  }
  // @@protoc_insertion_point(field_mutable:protobuf_unittest.ComplexOptionType3.complexoptiontype5)
  return complexoptiontype5_;
}
::protobuf_unittest::ComplexOptionType3_ComplexOptionType5* ComplexOptionType3::release_complexoptiontype5() {
  // @@protoc_insertion_point(field_release:protobuf_unittest.ComplexOptionType3.complexoptiontype5)
  clear_has_complexoptiontype5();
  ::protobuf_unittest::ComplexOptionType3_ComplexOptionType5* temp = complexoptiontype5_;
  complexoptiontype5_ = NULL;
  return temp;
}
void ComplexOptionType3::set_allocated_complexoptiontype5(::protobuf_unittest::ComplexOptionType3_ComplexOptionType5* complexoptiontype5) {
  delete complexoptiontype5_;
  complexoptiontype5_ = complexoptiontype5;
  if (complexoptiontype5) {
    set_has_complexoptiontype5();
  } else {
    clear_has_complexoptiontype5();
  }
  // @@protoc_insertion_point(field_set_allocated:protobuf_unittest.ComplexOptionType3.complexoptiontype5)
}

inline const ComplexOptionType3* ComplexOptionType3::internal_default_instance() {
  return &ComplexOptionType3_default_instance_.get();
}
#endif  // PROTOBUF_INLINE_NOT_IN_HEADERS

// ===================================================================

#if !defined(_MSC_VER) || _MSC_VER >= 1900
const int ComplexOpt6::kXyzzyFieldNumber;
#endif  // !defined(_MSC_VER) || _MSC_VER >= 1900

ComplexOpt6::ComplexOpt6()
  : ::google::protobuf::Message(), _internal_metadata_(NULL) {
  if (this != internal_default_instance()) protobuf_InitDefaults_google_2fprotobuf_2funittest_5fcustom_5foptions_2eproto();
  SharedCtor();
  // @@protoc_insertion_point(constructor:protobuf_unittest.ComplexOpt6)
}

void ComplexOpt6::InitAsDefaultInstance() {
}

ComplexOpt6::ComplexOpt6(const ComplexOpt6& from)
  : ::google::protobuf::Message(),
    _internal_metadata_(NULL) {
  SharedCtor();
  UnsafeMergeFrom(from);
  // @@protoc_insertion_point(copy_constructor:protobuf_unittest.ComplexOpt6)
}

void ComplexOpt6::SharedCtor() {
  _cached_size_ = 0;
  xyzzy_ = 0;
}

ComplexOpt6::~ComplexOpt6() {
  // @@protoc_insertion_point(destructor:protobuf_unittest.ComplexOpt6)
  SharedDtor();
}

void ComplexOpt6::SharedDtor() {
}

void ComplexOpt6::SetCachedSize(int size) const {
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
}
const ::google::protobuf::Descriptor* ComplexOpt6::descriptor() {
  protobuf_AssignDescriptorsOnce();
  return ComplexOpt6_descriptor_;
}

const ComplexOpt6& ComplexOpt6::default_instance() {
  protobuf_InitDefaults_google_2fprotobuf_2funittest_5fcustom_5foptions_2eproto();
  return *internal_default_instance();
}

::google::protobuf::internal::ExplicitlyConstructed<ComplexOpt6> ComplexOpt6_default_instance_;

ComplexOpt6* ComplexOpt6::New(::google::protobuf::Arena* arena) const {
  ComplexOpt6* n = new ComplexOpt6;
  if (arena != NULL) {
    arena->Own(n);
  }
  return n;
}

void ComplexOpt6::Clear() {
// @@protoc_insertion_point(message_clear_start:protobuf_unittest.ComplexOpt6)
  xyzzy_ = 0;
  _has_bits_.Clear();
  if (_internal_metadata_.have_unknown_fields()) {
    mutable_unknown_fields()->Clear();
  }
}

bool ComplexOpt6::MergePartialFromCodedStream(
    ::google::protobuf::io::CodedInputStream* input) {
#define DO_(EXPRESSION) if (!GOOGLE_PREDICT_TRUE(EXPRESSION)) goto failure
  ::google::protobuf::uint32 tag;
  // @@protoc_insertion_point(parse_start:protobuf_unittest.ComplexOpt6)
  for (;;) {
    ::std::pair< ::google::protobuf::uint32, bool> p = input->ReadTagWithCutoff(60751608);
    tag = p.first;
    if (!p.second) goto handle_unusual;
    switch (::google::protobuf::internal::WireFormatLite::GetTagFieldNumber(tag)) {
      // optional int32 xyzzy = 7593951;
      case 7593951: {
        if (tag == 60751608) {
          set_has_xyzzy();
          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int32, ::google::protobuf::internal::WireFormatLite::TYPE_INT32>(
                 input, &xyzzy_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectAtEnd()) goto success;
        break;
      }

      default: {
      handle_unusual:
        if (tag == 0 ||
            ::google::protobuf::internal::WireFormatLite::GetTagWireType(tag) ==
            ::google::protobuf::internal::WireFormatLite::WIRETYPE_END_GROUP) {
          goto success;
        }
        DO_(::google::protobuf::internal::WireFormat::SkipField(
              input, tag, mutable_unknown_fields()));
        break;
      }
    }
  }
success:
  // @@protoc_insertion_point(parse_success:protobuf_unittest.ComplexOpt6)
  return true;
failure:
  // @@protoc_insertion_point(parse_failure:protobuf_unittest.ComplexOpt6)
  return false;
#undef DO_
}

void ComplexOpt6::SerializeWithCachedSizes(
    ::google::protobuf::io::CodedOutputStream* output) const {
  // @@protoc_insertion_point(serialize_start:protobuf_unittest.ComplexOpt6)
  // optional int32 xyzzy = 7593951;
  if (has_xyzzy()) {
    ::google::protobuf::internal::WireFormatLite::WriteInt32(7593951, this->xyzzy(), output);
  }

  if (_internal_metadata_.have_unknown_fields()) {
    ::google::protobuf::internal::WireFormat::SerializeUnknownFields(
        unknown_fields(), output);
  }
  // @@protoc_insertion_point(serialize_end:protobuf_unittest.ComplexOpt6)
}

::google::protobuf::uint8* ComplexOpt6::InternalSerializeWithCachedSizesToArray(
    bool deterministic, ::google::protobuf::uint8* target) const {
  (void)deterministic; // Unused
  // @@protoc_insertion_point(serialize_to_array_start:protobuf_unittest.ComplexOpt6)
  // optional int32 xyzzy = 7593951;
  if (has_xyzzy()) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt32ToArray(7593951, this->xyzzy(), target);
  }

  if (_internal_metadata_.have_unknown_fields()) {
    target = ::google::protobuf::internal::WireFormat::SerializeUnknownFieldsToArray(
        unknown_fields(), target);
  }
  // @@protoc_insertion_point(serialize_to_array_end:protobuf_unittest.ComplexOpt6)
  return target;
}

size_t ComplexOpt6::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:protobuf_unittest.ComplexOpt6)
  size_t total_size = 0;

  // optional int32 xyzzy = 7593951;
  if (has_xyzzy()) {
    total_size += 4 +
      ::google::protobuf::internal::WireFormatLite::Int32Size(
        this->xyzzy());
  }

  if (_internal_metadata_.have_unknown_fields()) {
    total_size +=
      ::google::protobuf::internal::WireFormat::ComputeUnknownFieldsSize(
        unknown_fields());
  }
  int cached_size = ::google::protobuf::internal::ToCachedSize(total_size);
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = cached_size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
  return total_size;
}

void ComplexOpt6::MergeFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_merge_from_start:protobuf_unittest.ComplexOpt6)
  if (GOOGLE_PREDICT_FALSE(&from == this)) MergeFromFail(__LINE__);
  const ComplexOpt6* source =
      ::google::protobuf::internal::DynamicCastToGenerated<const ComplexOpt6>(
          &from);
  if (source == NULL) {
  // @@protoc_insertion_point(generalized_merge_from_cast_fail:protobuf_unittest.ComplexOpt6)
    ::google::protobuf::internal::ReflectionOps::Merge(from, this);
  } else {
  // @@protoc_insertion_point(generalized_merge_from_cast_success:protobuf_unittest.ComplexOpt6)
    UnsafeMergeFrom(*source);
  }
}

void ComplexOpt6::MergeFrom(const ComplexOpt6& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:protobuf_unittest.ComplexOpt6)
  if (GOOGLE_PREDICT_TRUE(&from != this)) {
    UnsafeMergeFrom(from);
  } else {
    MergeFromFail(__LINE__);
  }
}

void ComplexOpt6::UnsafeMergeFrom(const ComplexOpt6& from) {
  GOOGLE_DCHECK(&from != this);
  if (from._has_bits_[0 / 32] & (0xffu << (0 % 32))) {
    if (from.has_xyzzy()) {
      set_xyzzy(from.xyzzy());
    }
  }
  if (from._internal_metadata_.have_unknown_fields()) {
    ::google::protobuf::UnknownFieldSet::MergeToInternalMetdata(
      from.unknown_fields(), &_internal_metadata_);
  }
}

void ComplexOpt6::CopyFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_copy_from_start:protobuf_unittest.ComplexOpt6)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

void ComplexOpt6::CopyFrom(const ComplexOpt6& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:protobuf_unittest.ComplexOpt6)
  if (&from == this) return;
  Clear();
  UnsafeMergeFrom(from);
}

bool ComplexOpt6::IsInitialized() const {

  return true;
}

void ComplexOpt6::Swap(ComplexOpt6* other) {
  if (other == this) return;
  InternalSwap(other);
}
void ComplexOpt6::InternalSwap(ComplexOpt6* other) {
  std::swap(xyzzy_, other->xyzzy_);
  std::swap(_has_bits_[0], other->_has_bits_[0]);
  _internal_metadata_.Swap(&other->_internal_metadata_);
  std::swap(_cached_size_, other->_cached_size_);
}

::google::protobuf::Metadata ComplexOpt6::GetMetadata() const {
  protobuf_AssignDescriptorsOnce();
  ::google::protobuf::Metadata metadata;
  metadata.descriptor = ComplexOpt6_descriptor_;
  metadata.reflection = ComplexOpt6_reflection_;
  return metadata;
}

#if PROTOBUF_INLINE_NOT_IN_HEADERS
// ComplexOpt6

// optional int32 xyzzy = 7593951;
bool ComplexOpt6::has_xyzzy() const {
  return (_has_bits_[0] & 0x00000001u) != 0;
}
void ComplexOpt6::set_has_xyzzy() {
  _has_bits_[0] |= 0x00000001u;
}
void ComplexOpt6::clear_has_xyzzy() {
  _has_bits_[0] &= ~0x00000001u;
}
void ComplexOpt6::clear_xyzzy() {
  xyzzy_ = 0;
  clear_has_xyzzy();
}
::google::protobuf::int32 ComplexOpt6::xyzzy() const {
  // @@protoc_insertion_point(field_get:protobuf_unittest.ComplexOpt6.xyzzy)
  return xyzzy_;
}
void ComplexOpt6::set_xyzzy(::google::protobuf::int32 value) {
  set_has_xyzzy();
  xyzzy_ = value;
  // @@protoc_insertion_point(field_set:protobuf_unittest.ComplexOpt6.xyzzy)
}

inline const ComplexOpt6* ComplexOpt6::internal_default_instance() {
  return &ComplexOpt6_default_instance_.get();
}
#endif  // PROTOBUF_INLINE_NOT_IN_HEADERS

// ===================================================================

#if !defined(_MSC_VER) || _MSC_VER >= 1900
#endif  // !defined(_MSC_VER) || _MSC_VER >= 1900

VariousComplexOptions::VariousComplexOptions()
  : ::google::protobuf::Message(), _internal_metadata_(NULL) {
  if (this != internal_default_instance()) protobuf_InitDefaults_google_2fprotobuf_2funittest_5fcustom_5foptions_2eproto();
  SharedCtor();
  // @@protoc_insertion_point(constructor:protobuf_unittest.VariousComplexOptions)
}

void VariousComplexOptions::InitAsDefaultInstance() {
}

VariousComplexOptions::VariousComplexOptions(const VariousComplexOptions& from)
  : ::google::protobuf::Message(),
    _internal_metadata_(NULL) {
  SharedCtor();
  UnsafeMergeFrom(from);
  // @@protoc_insertion_point(copy_constructor:protobuf_unittest.VariousComplexOptions)
}

void VariousComplexOptions::SharedCtor() {
  _cached_size_ = 0;
}

VariousComplexOptions::~VariousComplexOptions() {
  // @@protoc_insertion_point(destructor:protobuf_unittest.VariousComplexOptions)
  SharedDtor();
}

void VariousComplexOptions::SharedDtor() {
}

void VariousComplexOptions::SetCachedSize(int size) const {
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
}
const ::google::protobuf::Descriptor* VariousComplexOptions::descriptor() {
  protobuf_AssignDescriptorsOnce();
  return VariousComplexOptions_descriptor_;
}

const VariousComplexOptions& VariousComplexOptions::default_instance() {
  protobuf_InitDefaults_google_2fprotobuf_2funittest_5fcustom_5foptions_2eproto();
  return *internal_default_instance();
}

::google::protobuf::internal::ExplicitlyConstructed<VariousComplexOptions> VariousComplexOptions_default_instance_;

VariousComplexOptions* VariousComplexOptions::New(::google::protobuf::Arena* arena) const {
  VariousComplexOptions* n = new VariousComplexOptions;
  if (arena != NULL) {
    arena->Own(n);
  }
  return n;
}

void VariousComplexOptions::Clear() {
// @@protoc_insertion_point(message_clear_start:protobuf_unittest.VariousComplexOptions)
  _has_bits_.Clear();
  if (_internal_metadata_.have_unknown_fields()) {
    mutable_unknown_fields()->Clear();
  }
}

bool VariousComplexOptions::MergePartialFromCodedStream(
    ::google::protobuf::io::CodedInputStream* input) {
#define DO_(EXPRESSION) if (!GOOGLE_PREDICT_TRUE(EXPRESSION)) goto failure
  ::google::protobuf::uint32 tag;
  // @@protoc_insertion_point(parse_start:protobuf_unittest.VariousComplexOptions)
  for (;;) {
    ::std::pair< ::google::protobuf::uint32, bool> p = input->ReadTagWithCutoff(127);
    tag = p.first;
    if (!p.second) goto handle_unusual;
  handle_unusual:
    if (tag == 0 ||
        ::google::protobuf::internal::WireFormatLite::GetTagWireType(tag) ==
        ::google::protobuf::internal::WireFormatLite::WIRETYPE_END_GROUP) {
      goto success;
    }
    DO_(::google::protobuf::internal::WireFormat::SkipField(
          input, tag, mutable_unknown_fields()));
  }
success:
  // @@protoc_insertion_point(parse_success:protobuf_unittest.VariousComplexOptions)
  return true;
failure:
  // @@protoc_insertion_point(parse_failure:protobuf_unittest.VariousComplexOptions)
  return false;
#undef DO_
}

void VariousComplexOptions::SerializeWithCachedSizes(
    ::google::protobuf::io::CodedOutputStream* output) const {
  // @@protoc_insertion_point(serialize_start:protobuf_unittest.VariousComplexOptions)
  if (_internal_metadata_.have_unknown_fields()) {
    ::google::protobuf::internal::WireFormat::SerializeUnknownFields(
        unknown_fields(), output);
  }
  // @@protoc_insertion_point(serialize_end:protobuf_unittest.VariousComplexOptions)
}

::google::protobuf::uint8* VariousComplexOptions::InternalSerializeWithCachedSizesToArray(
    bool deterministic, ::google::protobuf::uint8* target) const {
  (void)deterministic; // Unused
  // @@protoc_insertion_point(serialize_to_array_start:protobuf_unittest.VariousComplexOptions)
  if (_internal_metadata_.have_unknown_fields()) {
    target = ::google::protobuf::internal::WireFormat::SerializeUnknownFieldsToArray(
        unknown_fields(), target);
  }
  // @@protoc_insertion_point(serialize_to_array_end:protobuf_unittest.VariousComplexOptions)
  return target;
}

size_t VariousComplexOptions::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:protobuf_unittest.VariousComplexOptions)
  size_t total_size = 0;

  if (_internal_metadata_.have_unknown_fields()) {
    total_size +=
      ::google::protobuf::internal::WireFormat::ComputeUnknownFieldsSize(
        unknown_fields());
  }
  int cached_size = ::google::protobuf::internal::ToCachedSize(total_size);
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = cached_size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
  return total_size;
}

void VariousComplexOptions::MergeFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_merge_from_start:protobuf_unittest.VariousComplexOptions)
  if (GOOGLE_PREDICT_FALSE(&from == this)) MergeFromFail(__LINE__);
  const VariousComplexOptions* source =
      ::google::protobuf::internal::DynamicCastToGenerated<const VariousComplexOptions>(
          &from);
  if (source == NULL) {
  // @@protoc_insertion_point(generalized_merge_from_cast_fail:protobuf_unittest.VariousComplexOptions)
    ::google::protobuf::internal::ReflectionOps::Merge(from, this);
  } else {
  // @@protoc_insertion_point(generalized_merge_from_cast_success:protobuf_unittest.VariousComplexOptions)
    UnsafeMergeFrom(*source);
  }
}

void VariousComplexOptions::MergeFrom(const VariousComplexOptions& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:protobuf_unittest.VariousComplexOptions)
  if (GOOGLE_PREDICT_TRUE(&from != this)) {
    UnsafeMergeFrom(from);
  } else {
    MergeFromFail(__LINE__);
  }
}

void VariousComplexOptions::UnsafeMergeFrom(const VariousComplexOptions& from) {
  GOOGLE_DCHECK(&from != this);
  if (from._internal_metadata_.have_unknown_fields()) {
    ::google::protobuf::UnknownFieldSet::MergeToInternalMetdata(
      from.unknown_fields(), &_internal_metadata_);
  }
}

void VariousComplexOptions::CopyFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_copy_from_start:protobuf_unittest.VariousComplexOptions)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

void VariousComplexOptions::CopyFrom(const VariousComplexOptions& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:protobuf_unittest.VariousComplexOptions)
  if (&from == this) return;
  Clear();
  UnsafeMergeFrom(from);
}

bool VariousComplexOptions::IsInitialized() const {

  return true;
}

void VariousComplexOptions::Swap(VariousComplexOptions* other) {
  if (other == this) return;
  InternalSwap(other);
}
void VariousComplexOptions::InternalSwap(VariousComplexOptions* other) {
  _internal_metadata_.Swap(&other->_internal_metadata_);
  std::swap(_cached_size_, other->_cached_size_);
}

::google::protobuf::Metadata VariousComplexOptions::GetMetadata() const {
  protobuf_AssignDescriptorsOnce();
  ::google::protobuf::Metadata metadata;
  metadata.descriptor = VariousComplexOptions_descriptor_;
  metadata.reflection = VariousComplexOptions_reflection_;
  return metadata;
}

#if PROTOBUF_INLINE_NOT_IN_HEADERS
// VariousComplexOptions

inline const VariousComplexOptions* VariousComplexOptions::internal_default_instance() {
  return &VariousComplexOptions_default_instance_.get();
}
#endif  // PROTOBUF_INLINE_NOT_IN_HEADERS

// ===================================================================

#if !defined(_MSC_VER) || _MSC_VER >= 1900
#endif  // !defined(_MSC_VER) || _MSC_VER >= 1900

AggregateMessageSet::AggregateMessageSet()
  : ::google::protobuf::Message(), _internal_metadata_(NULL) {
  if (this != internal_default_instance()) protobuf_InitDefaults_google_2fprotobuf_2funittest_5fcustom_5foptions_2eproto();
  SharedCtor();
  // @@protoc_insertion_point(constructor:protobuf_unittest.AggregateMessageSet)
}

void AggregateMessageSet::InitAsDefaultInstance() {
}

AggregateMessageSet::AggregateMessageSet(const AggregateMessageSet& from)
  : ::google::protobuf::Message(),
    _internal_metadata_(NULL) {
  SharedCtor();
  UnsafeMergeFrom(from);
  // @@protoc_insertion_point(copy_constructor:protobuf_unittest.AggregateMessageSet)
}

void AggregateMessageSet::SharedCtor() {
  _cached_size_ = 0;
}

AggregateMessageSet::~AggregateMessageSet() {
  // @@protoc_insertion_point(destructor:protobuf_unittest.AggregateMessageSet)
  SharedDtor();
}

void AggregateMessageSet::SharedDtor() {
}

void AggregateMessageSet::SetCachedSize(int size) const {
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
}
const ::google::protobuf::Descriptor* AggregateMessageSet::descriptor() {
  protobuf_AssignDescriptorsOnce();
  return AggregateMessageSet_descriptor_;
}

const AggregateMessageSet& AggregateMessageSet::default_instance() {
  protobuf_InitDefaults_google_2fprotobuf_2funittest_5fcustom_5foptions_2eproto();
  return *internal_default_instance();
}

::google::protobuf::internal::ExplicitlyConstructed<AggregateMessageSet> AggregateMessageSet_default_instance_;

AggregateMessageSet* AggregateMessageSet::New(::google::protobuf::Arena* arena) const {
  AggregateMessageSet* n = new AggregateMessageSet;
  if (arena != NULL) {
    arena->Own(n);
  }
  return n;
}

void AggregateMessageSet::Clear() {
// @@protoc_insertion_point(message_clear_start:protobuf_unittest.AggregateMessageSet)
  _extensions_.Clear();
  _has_bits_.Clear();
  if (_internal_metadata_.have_unknown_fields()) {
    mutable_unknown_fields()->Clear();
  }
}

bool AggregateMessageSet::MergePartialFromCodedStream(
    ::google::protobuf::io::CodedInputStream* input) {
  return _extensions_.ParseMessageSet(input, internal_default_instance(),
                                      mutable_unknown_fields());
}

void AggregateMessageSet::SerializeWithCachedSizes(
    ::google::protobuf::io::CodedOutputStream* output) const {
  _extensions_.SerializeMessageSetWithCachedSizes(output);
  ::google::protobuf::internal::WireFormat::SerializeUnknownMessageSetItems(
      unknown_fields(), output);
}

::google::protobuf::uint8* AggregateMessageSet::InternalSerializeWithCachedSizesToArray(
    bool deterministic, ::google::protobuf::uint8* target) const {
  target = _extensions_.InternalSerializeMessageSetWithCachedSizesToArray(
               deterministic, target);
  target = ::google::protobuf::internal::WireFormat::
             SerializeUnknownMessageSetItemsToArray(
               unknown_fields(), target);
  return target;
}

size_t AggregateMessageSet::ByteSizeLong() const {
// @@protoc_insertion_point(message_set_byte_size_start:protobuf_unittest.AggregateMessageSet)
  size_t total_size = _extensions_.MessageSetByteSize();
if (_internal_metadata_.have_unknown_fields()) {
  total_size += ::google::protobuf::internal::WireFormat::
      ComputeUnknownMessageSetItemsSize(unknown_fields());
}
  int cached_size = ::google::protobuf::internal::ToCachedSize(total_size);
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = cached_size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
  return total_size;
}

void AggregateMessageSet::MergeFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_merge_from_start:protobuf_unittest.AggregateMessageSet)
  if (GOOGLE_PREDICT_FALSE(&from == this)) MergeFromFail(__LINE__);
  const AggregateMessageSet* source =
      ::google::protobuf::internal::DynamicCastToGenerated<const AggregateMessageSet>(
          &from);
  if (source == NULL) {
  // @@protoc_insertion_point(generalized_merge_from_cast_fail:protobuf_unittest.AggregateMessageSet)
    ::google::protobuf::internal::ReflectionOps::Merge(from, this);
  } else {
  // @@protoc_insertion_point(generalized_merge_from_cast_success:protobuf_unittest.AggregateMessageSet)
    UnsafeMergeFrom(*source);
  }
}

void AggregateMessageSet::MergeFrom(const AggregateMessageSet& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:protobuf_unittest.AggregateMessageSet)
  if (GOOGLE_PREDICT_TRUE(&from != this)) {
    UnsafeMergeFrom(from);
  } else {
    MergeFromFail(__LINE__);
  }
}

void AggregateMessageSet::UnsafeMergeFrom(const AggregateMessageSet& from) {
  GOOGLE_DCHECK(&from != this);
  _extensions_.MergeFrom(from._extensions_);
  if (from._internal_metadata_.have_unknown_fields()) {
    ::google::protobuf::UnknownFieldSet::MergeToInternalMetdata(
      from.unknown_fields(), &_internal_metadata_);
  }
}

void AggregateMessageSet::CopyFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_copy_from_start:protobuf_unittest.AggregateMessageSet)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

void AggregateMessageSet::CopyFrom(const AggregateMessageSet& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:protobuf_unittest.AggregateMessageSet)
  if (&from == this) return;
  Clear();
  UnsafeMergeFrom(from);
}

bool AggregateMessageSet::IsInitialized() const {


  if (!_extensions_.IsInitialized()) {
    return false;
  }
  return true;
}

void AggregateMessageSet::Swap(AggregateMessageSet* other) {
  if (other == this) return;
  InternalSwap(other);
}
void AggregateMessageSet::InternalSwap(AggregateMessageSet* other) {
  _internal_metadata_.Swap(&other->_internal_metadata_);
  std::swap(_cached_size_, other->_cached_size_);
  _extensions_.Swap(&other->_extensions_);
}

::google::protobuf::Metadata AggregateMessageSet::GetMetadata() const {
  protobuf_AssignDescriptorsOnce();
  ::google::protobuf::Metadata metadata;
  metadata.descriptor = AggregateMessageSet_descriptor_;
  metadata.reflection = AggregateMessageSet_reflection_;
  return metadata;
}

#if PROTOBUF_INLINE_NOT_IN_HEADERS
// AggregateMessageSet

inline const AggregateMessageSet* AggregateMessageSet::internal_default_instance() {
  return &AggregateMessageSet_default_instance_.get();
}
#endif  // PROTOBUF_INLINE_NOT_IN_HEADERS

// ===================================================================

#if !defined(_MSC_VER) || _MSC_VER >= 1900
const int AggregateMessageSetElement::kSFieldNumber;
#endif  // !defined(_MSC_VER) || _MSC_VER >= 1900

#if !defined(_MSC_VER) || _MSC_VER >= 1900
const int AggregateMessageSetElement::kMessageSetExtensionFieldNumber;
#endif
::google::protobuf::internal::ExtensionIdentifier< ::protobuf_unittest::AggregateMessageSet,
    ::google::protobuf::internal::MessageTypeTraits< ::protobuf_unittest::AggregateMessageSetElement >, 11, false >
  AggregateMessageSetElement::message_set_extension(kMessageSetExtensionFieldNumber, *::protobuf_unittest::AggregateMessageSetElement::internal_default_instance());
AggregateMessageSetElement::AggregateMessageSetElement()
  : ::google::protobuf::Message(), _internal_metadata_(NULL) {
  if (this != internal_default_instance()) protobuf_InitDefaults_google_2fprotobuf_2funittest_5fcustom_5foptions_2eproto();
  SharedCtor();
  // @@protoc_insertion_point(constructor:protobuf_unittest.AggregateMessageSetElement)
}

void AggregateMessageSetElement::InitAsDefaultInstance() {
}

AggregateMessageSetElement::AggregateMessageSetElement(const AggregateMessageSetElement& from)
  : ::google::protobuf::Message(),
    _internal_metadata_(NULL) {
  SharedCtor();
  UnsafeMergeFrom(from);
  // @@protoc_insertion_point(copy_constructor:protobuf_unittest.AggregateMessageSetElement)
}

void AggregateMessageSetElement::SharedCtor() {
  _cached_size_ = 0;
  s_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}

AggregateMessageSetElement::~AggregateMessageSetElement() {
  // @@protoc_insertion_point(destructor:protobuf_unittest.AggregateMessageSetElement)
  SharedDtor();
}

void AggregateMessageSetElement::SharedDtor() {
  s_.DestroyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}

void AggregateMessageSetElement::SetCachedSize(int size) const {
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
}
const ::google::protobuf::Descriptor* AggregateMessageSetElement::descriptor() {
  protobuf_AssignDescriptorsOnce();
  return AggregateMessageSetElement_descriptor_;
}

const AggregateMessageSetElement& AggregateMessageSetElement::default_instance() {
  protobuf_InitDefaults_google_2fprotobuf_2funittest_5fcustom_5foptions_2eproto();
  return *internal_default_instance();
}

::google::protobuf::internal::ExplicitlyConstructed<AggregateMessageSetElement> AggregateMessageSetElement_default_instance_;

AggregateMessageSetElement* AggregateMessageSetElement::New(::google::protobuf::Arena* arena) const {
  AggregateMessageSetElement* n = new AggregateMessageSetElement;
  if (arena != NULL) {
    arena->Own(n);
  }
  return n;
}

void AggregateMessageSetElement::Clear() {
// @@protoc_insertion_point(message_clear_start:protobuf_unittest.AggregateMessageSetElement)
  if (has_s()) {
    s_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  }
  _has_bits_.Clear();
  if (_internal_metadata_.have_unknown_fields()) {
    mutable_unknown_fields()->Clear();
  }
}

bool AggregateMessageSetElement::MergePartialFromCodedStream(
    ::google::protobuf::io::CodedInputStream* input) {
#define DO_(EXPRESSION) if (!GOOGLE_PREDICT_TRUE(EXPRESSION)) goto failure
  ::google::protobuf::uint32 tag;
  // @@protoc_insertion_point(parse_start:protobuf_unittest.AggregateMessageSetElement)
  for (;;) {
    ::std::pair< ::google::protobuf::uint32, bool> p = input->ReadTagWithCutoff(127);
    tag = p.first;
    if (!p.second) goto handle_unusual;
    switch (::google::protobuf::internal::WireFormatLite::GetTagFieldNumber(tag)) {
      // optional string s = 1;
      case 1: {
        if (tag == 10) {
          DO_(::google::protobuf::internal::WireFormatLite::ReadString(
                input, this->mutable_s()));
          ::google::protobuf::internal::WireFormat::VerifyUTF8StringNamedField(
            this->s().data(), this->s().length(),
            ::google::protobuf::internal::WireFormat::PARSE,
            "protobuf_unittest.AggregateMessageSetElement.s");
        } else {
          goto handle_unusual;
        }
        if (input->ExpectAtEnd()) goto success;
        break;
      }

      default: {
      handle_unusual:
        if (tag == 0 ||
            ::google::protobuf::internal::WireFormatLite::GetTagWireType(tag) ==
            ::google::protobuf::internal::WireFormatLite::WIRETYPE_END_GROUP) {
          goto success;
        }
        DO_(::google::protobuf::internal::WireFormat::SkipField(
              input, tag, mutable_unknown_fields()));
        break;
      }
    }
  }
success:
  // @@protoc_insertion_point(parse_success:protobuf_unittest.AggregateMessageSetElement)
  return true;
failure:
  // @@protoc_insertion_point(parse_failure:protobuf_unittest.AggregateMessageSetElement)
  return false;
#undef DO_
}

void AggregateMessageSetElement::SerializeWithCachedSizes(
    ::google::protobuf::io::CodedOutputStream* output) const {
  // @@protoc_insertion_point(serialize_start:protobuf_unittest.AggregateMessageSetElement)
  // optional string s = 1;
  if (has_s()) {
    ::google::protobuf::internal::WireFormat::VerifyUTF8StringNamedField(
      this->s().data(), this->s().length(),
      ::google::protobuf::internal::WireFormat::SERIALIZE,
      "protobuf_unittest.AggregateMessageSetElement.s");
    ::google::protobuf::internal::WireFormatLite::WriteStringMaybeAliased(
      1, this->s(), output);
  }

  if (_internal_metadata_.have_unknown_fields()) {
    ::google::protobuf::internal::WireFormat::SerializeUnknownFields(
        unknown_fields(), output);
  }
  // @@protoc_insertion_point(serialize_end:protobuf_unittest.AggregateMessageSetElement)
}

::google::protobuf::uint8* AggregateMessageSetElement::InternalSerializeWithCachedSizesToArray(
    bool deterministic, ::google::protobuf::uint8* target) const {
  (void)deterministic; // Unused
  // @@protoc_insertion_point(serialize_to_array_start:protobuf_unittest.AggregateMessageSetElement)
  // optional string s = 1;
  if (has_s()) {
    ::google::protobuf::internal::WireFormat::VerifyUTF8StringNamedField(
      this->s().data(), this->s().length(),
      ::google::protobuf::internal::WireFormat::SERIALIZE,
      "protobuf_unittest.AggregateMessageSetElement.s");
    target =
      ::google::protobuf::internal::WireFormatLite::WriteStringToArray(
        1, this->s(), target);
  }

  if (_internal_metadata_.have_unknown_fields()) {
    target = ::google::protobuf::internal::WireFormat::SerializeUnknownFieldsToArray(
        unknown_fields(), target);
  }
  // @@protoc_insertion_point(serialize_to_array_end:protobuf_unittest.AggregateMessageSetElement)
  return target;
}

size_t AggregateMessageSetElement::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:protobuf_unittest.AggregateMessageSetElement)
  size_t total_size = 0;

  // optional string s = 1;
  if (has_s()) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::StringSize(
        this->s());
  }

  if (_internal_metadata_.have_unknown_fields()) {
    total_size +=
      ::google::protobuf::internal::WireFormat::ComputeUnknownFieldsSize(
        unknown_fields());
  }
  int cached_size = ::google::protobuf::internal::ToCachedSize(total_size);
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = cached_size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
  return total_size;
}

void AggregateMessageSetElement::MergeFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_merge_from_start:protobuf_unittest.AggregateMessageSetElement)
  if (GOOGLE_PREDICT_FALSE(&from == this)) MergeFromFail(__LINE__);
  const AggregateMessageSetElement* source =
      ::google::protobuf::internal::DynamicCastToGenerated<const AggregateMessageSetElement>(
          &from);
  if (source == NULL) {
  // @@protoc_insertion_point(generalized_merge_from_cast_fail:protobuf_unittest.AggregateMessageSetElement)
    ::google::protobuf::internal::ReflectionOps::Merge(from, this);
  } else {
  // @@protoc_insertion_point(generalized_merge_from_cast_success:protobuf_unittest.AggregateMessageSetElement)
    UnsafeMergeFrom(*source);
  }
}

void AggregateMessageSetElement::MergeFrom(const AggregateMessageSetElement& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:protobuf_unittest.AggregateMessageSetElement)
  if (GOOGLE_PREDICT_TRUE(&from != this)) {
    UnsafeMergeFrom(from);
  } else {
    MergeFromFail(__LINE__);
  }
}

void AggregateMessageSetElement::UnsafeMergeFrom(const AggregateMessageSetElement& from) {
  GOOGLE_DCHECK(&from != this);
  if (from._has_bits_[0 / 32] & (0xffu << (0 % 32))) {
    if (from.has_s()) {
      set_has_s();
      s_.AssignWithDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), from.s_);
    }
  }
  if (from._internal_metadata_.have_unknown_fields()) {
    ::google::protobuf::UnknownFieldSet::MergeToInternalMetdata(
      from.unknown_fields(), &_internal_metadata_);
  }
}

void AggregateMessageSetElement::CopyFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_copy_from_start:protobuf_unittest.AggregateMessageSetElement)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

void AggregateMessageSetElement::CopyFrom(const AggregateMessageSetElement& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:protobuf_unittest.AggregateMessageSetElement)
  if (&from == this) return;
  Clear();
  UnsafeMergeFrom(from);
}

bool AggregateMessageSetElement::IsInitialized() const {

  return true;
}

void AggregateMessageSetElement::Swap(AggregateMessageSetElement* other) {
  if (other == this) return;
  InternalSwap(other);
}
void AggregateMessageSetElement::InternalSwap(AggregateMessageSetElement* other) {
  s_.Swap(&other->s_);
  std::swap(_has_bits_[0], other->_has_bits_[0]);
  _internal_metadata_.Swap(&other->_internal_metadata_);
  std::swap(_cached_size_, other->_cached_size_);
}

::google::protobuf::Metadata AggregateMessageSetElement::GetMetadata() const {
  protobuf_AssignDescriptorsOnce();
  ::google::protobuf::Metadata metadata;
  metadata.descriptor = AggregateMessageSetElement_descriptor_;
  metadata.reflection = AggregateMessageSetElement_reflection_;
  return metadata;
}

#if PROTOBUF_INLINE_NOT_IN_HEADERS
// AggregateMessageSetElement

// optional string s = 1;
bool AggregateMessageSetElement::has_s() const {
  return (_has_bits_[0] & 0x00000001u) != 0;
}
void AggregateMessageSetElement::set_has_s() {
  _has_bits_[0] |= 0x00000001u;
}
void AggregateMessageSetElement::clear_has_s() {
  _has_bits_[0] &= ~0x00000001u;
}
void AggregateMessageSetElement::clear_s() {
  s_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  clear_has_s();
}
const ::std::string& AggregateMessageSetElement::s() const {
  // @@protoc_insertion_point(field_get:protobuf_unittest.AggregateMessageSetElement.s)
  return s_.GetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
void AggregateMessageSetElement::set_s(const ::std::string& value) {
  set_has_s();
  s_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:protobuf_unittest.AggregateMessageSetElement.s)
}
void AggregateMessageSetElement::set_s(const char* value) {
  set_has_s();
  s_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:protobuf_unittest.AggregateMessageSetElement.s)
}
void AggregateMessageSetElement::set_s(const char* value, size_t size) {
  set_has_s();
  s_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:protobuf_unittest.AggregateMessageSetElement.s)
}
::std::string* AggregateMessageSetElement::mutable_s() {
  set_has_s();
  // @@protoc_insertion_point(field_mutable:protobuf_unittest.AggregateMessageSetElement.s)
  return s_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
::std::string* AggregateMessageSetElement::release_s() {
  // @@protoc_insertion_point(field_release:protobuf_unittest.AggregateMessageSetElement.s)
  clear_has_s();
  return s_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
void AggregateMessageSetElement::set_allocated_s(::std::string* s) {
  if (s != NULL) {
    set_has_s();
  } else {
    clear_has_s();
  }
  s_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), s);
  // @@protoc_insertion_point(field_set_allocated:protobuf_unittest.AggregateMessageSetElement.s)
}

inline const AggregateMessageSetElement* AggregateMessageSetElement::internal_default_instance() {
  return &AggregateMessageSetElement_default_instance_.get();
}
#endif  // PROTOBUF_INLINE_NOT_IN_HEADERS

// ===================================================================

#if !defined(_MSC_VER) || _MSC_VER >= 1900
const int Aggregate::kIFieldNumber;
const int Aggregate::kSFieldNumber;
const int Aggregate::kSubFieldNumber;
const int Aggregate::kFileFieldNumber;
const int Aggregate::kMsetFieldNumber;
#endif  // !defined(_MSC_VER) || _MSC_VER >= 1900

#if !defined(_MSC_VER) || _MSC_VER >= 1900
const int Aggregate::kNestedFieldNumber;
#endif
::google::protobuf::internal::ExtensionIdentifier< ::google::protobuf::FileOptions,
    ::google::protobuf::internal::MessageTypeTraits< ::protobuf_unittest::Aggregate >, 11, false >
  Aggregate::nested(kNestedFieldNumber, *::protobuf_unittest::Aggregate::internal_default_instance());
Aggregate::Aggregate()
  : ::google::protobuf::Message(), _internal_metadata_(NULL) {
  if (this != internal_default_instance()) protobuf_InitDefaults_google_2fprotobuf_2funittest_5fcustom_5foptions_2eproto();
  SharedCtor();
  // @@protoc_insertion_point(constructor:protobuf_unittest.Aggregate)
}

void Aggregate::InitAsDefaultInstance() {
  sub_ = const_cast< ::protobuf_unittest::Aggregate*>(
      ::protobuf_unittest::Aggregate::internal_default_instance());
  file_ = const_cast< ::google::protobuf::FileOptions*>(
      ::google::protobuf::FileOptions::internal_default_instance());
  mset_ = const_cast< ::protobuf_unittest::AggregateMessageSet*>(
      ::protobuf_unittest::AggregateMessageSet::internal_default_instance());
}

Aggregate::Aggregate(const Aggregate& from)
  : ::google::protobuf::Message(),
    _internal_metadata_(NULL) {
  SharedCtor();
  UnsafeMergeFrom(from);
  // @@protoc_insertion_point(copy_constructor:protobuf_unittest.Aggregate)
}

void Aggregate::SharedCtor() {
  _cached_size_ = 0;
  s_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  sub_ = NULL;
  file_ = NULL;
  mset_ = NULL;
  i_ = 0;
}

Aggregate::~Aggregate() {
  // @@protoc_insertion_point(destructor:protobuf_unittest.Aggregate)
  SharedDtor();
}

void Aggregate::SharedDtor() {
  s_.DestroyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  if (this != &Aggregate_default_instance_.get()) {
    delete sub_;
    delete file_;
    delete mset_;
  }
}

void Aggregate::SetCachedSize(int size) const {
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
}
const ::google::protobuf::Descriptor* Aggregate::descriptor() {
  protobuf_AssignDescriptorsOnce();
  return Aggregate_descriptor_;
}

const Aggregate& Aggregate::default_instance() {
  protobuf_InitDefaults_google_2fprotobuf_2funittest_5fcustom_5foptions_2eproto();
  return *internal_default_instance();
}

::google::protobuf::internal::ExplicitlyConstructed<Aggregate> Aggregate_default_instance_;

Aggregate* Aggregate::New(::google::protobuf::Arena* arena) const {
  Aggregate* n = new Aggregate;
  if (arena != NULL) {
    arena->Own(n);
  }
  return n;
}

void Aggregate::Clear() {
// @@protoc_insertion_point(message_clear_start:protobuf_unittest.Aggregate)
  if (_has_bits_[0 / 32] & 31u) {
    i_ = 0;
    if (has_s()) {
      s_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
    }
    if (has_sub()) {
      if (sub_ != NULL) sub_->::protobuf_unittest::Aggregate::Clear();
    }
    if (has_file()) {
      if (file_ != NULL) file_->::google::protobuf::FileOptions::Clear();
    }
    if (has_mset()) {
      if (mset_ != NULL) mset_->::protobuf_unittest::AggregateMessageSet::Clear();
    }
  }
  _has_bits_.Clear();
  if (_internal_metadata_.have_unknown_fields()) {
    mutable_unknown_fields()->Clear();
  }
}

bool Aggregate::MergePartialFromCodedStream(
    ::google::protobuf::io::CodedInputStream* input) {
#define DO_(EXPRESSION) if (!GOOGLE_PREDICT_TRUE(EXPRESSION)) goto failure
  ::google::protobuf::uint32 tag;
  // @@protoc_insertion_point(parse_start:protobuf_unittest.Aggregate)
  for (;;) {
    ::std::pair< ::google::protobuf::uint32, bool> p = input->ReadTagWithCutoff(127);
    tag = p.first;
    if (!p.second) goto handle_unusual;
    switch (::google::protobuf::internal::WireFormatLite::GetTagFieldNumber(tag)) {
      // optional int32 i = 1;
      case 1: {
        if (tag == 8) {
          set_has_i();
          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int32, ::google::protobuf::internal::WireFormatLite::TYPE_INT32>(
                 input, &i_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(18)) goto parse_s;
        break;
      }

      // optional string s = 2;
      case 2: {
        if (tag == 18) {
         parse_s:
          DO_(::google::protobuf::internal::WireFormatLite::ReadString(
                input, this->mutable_s()));
          ::google::protobuf::internal::WireFormat::VerifyUTF8StringNamedField(
            this->s().data(), this->s().length(),
            ::google::protobuf::internal::WireFormat::PARSE,
            "protobuf_unittest.Aggregate.s");
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(26)) goto parse_sub;
        break;
      }

      // optional .protobuf_unittest.Aggregate sub = 3;
      case 3: {
        if (tag == 26) {
         parse_sub:
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessageNoVirtual(
               input, mutable_sub()));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(34)) goto parse_file;
        break;
      }

      // optional .google.protobuf.FileOptions file = 4;
      case 4: {
        if (tag == 34) {
         parse_file:
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessageNoVirtual(
               input, mutable_file()));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(42)) goto parse_mset;
        break;
      }

      // optional .protobuf_unittest.AggregateMessageSet mset = 5;
      case 5: {
        if (tag == 42) {
         parse_mset:
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessageNoVirtual(
               input, mutable_mset()));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectAtEnd()) goto success;
        break;
      }

      default: {
      handle_unusual:
        if (tag == 0 ||
            ::google::protobuf::internal::WireFormatLite::GetTagWireType(tag) ==
            ::google::protobuf::internal::WireFormatLite::WIRETYPE_END_GROUP) {
          goto success;
        }
        DO_(::google::protobuf::internal::WireFormat::SkipField(
              input, tag, mutable_unknown_fields()));
        break;
      }
    }
  }
success:
  // @@protoc_insertion_point(parse_success:protobuf_unittest.Aggregate)
  return true;
failure:
  // @@protoc_insertion_point(parse_failure:protobuf_unittest.Aggregate)
  return false;
#undef DO_
}

void Aggregate::SerializeWithCachedSizes(
    ::google::protobuf::io::CodedOutputStream* output) const {
  // @@protoc_insertion_point(serialize_start:protobuf_unittest.Aggregate)
  // optional int32 i = 1;
  if (has_i()) {
    ::google::protobuf::internal::WireFormatLite::WriteInt32(1, this->i(), output);
  }

  // optional string s = 2;
  if (has_s()) {
    ::google::protobuf::internal::WireFormat::VerifyUTF8StringNamedField(
      this->s().data(), this->s().length(),
      ::google::protobuf::internal::WireFormat::SERIALIZE,
      "protobuf_unittest.Aggregate.s");
    ::google::protobuf::internal::WireFormatLite::WriteStringMaybeAliased(
      2, this->s(), output);
  }

  // optional .protobuf_unittest.Aggregate sub = 3;
  if (has_sub()) {
    ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
      3, *this->sub_, output);
  }

  // optional .google.protobuf.FileOptions file = 4;
  if (has_file()) {
    ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
      4, *this->file_, output);
  }

  // optional .protobuf_unittest.AggregateMessageSet mset = 5;
  if (has_mset()) {
    ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
      5, *this->mset_, output);
  }

  if (_internal_metadata_.have_unknown_fields()) {
    ::google::protobuf::internal::WireFormat::SerializeUnknownFields(
        unknown_fields(), output);
  }
  // @@protoc_insertion_point(serialize_end:protobuf_unittest.Aggregate)
}

::google::protobuf::uint8* Aggregate::InternalSerializeWithCachedSizesToArray(
    bool deterministic, ::google::protobuf::uint8* target) const {
  (void)deterministic; // Unused
  // @@protoc_insertion_point(serialize_to_array_start:protobuf_unittest.Aggregate)
  // optional int32 i = 1;
  if (has_i()) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt32ToArray(1, this->i(), target);
  }

  // optional string s = 2;
  if (has_s()) {
    ::google::protobuf::internal::WireFormat::VerifyUTF8StringNamedField(
      this->s().data(), this->s().length(),
      ::google::protobuf::internal::WireFormat::SERIALIZE,
      "protobuf_unittest.Aggregate.s");
    target =
      ::google::protobuf::internal::WireFormatLite::WriteStringToArray(
        2, this->s(), target);
  }

  // optional .protobuf_unittest.Aggregate sub = 3;
  if (has_sub()) {
    target = ::google::protobuf::internal::WireFormatLite::
      InternalWriteMessageNoVirtualToArray(
        3, *this->sub_, false, target);
  }

  // optional .google.protobuf.FileOptions file = 4;
  if (has_file()) {
    target = ::google::protobuf::internal::WireFormatLite::
      InternalWriteMessageNoVirtualToArray(
        4, *this->file_, false, target);
  }

  // optional .protobuf_unittest.AggregateMessageSet mset = 5;
  if (has_mset()) {
    target = ::google::protobuf::internal::WireFormatLite::
      InternalWriteMessageNoVirtualToArray(
        5, *this->mset_, false, target);
  }

  if (_internal_metadata_.have_unknown_fields()) {
    target = ::google::protobuf::internal::WireFormat::SerializeUnknownFieldsToArray(
        unknown_fields(), target);
  }
  // @@protoc_insertion_point(serialize_to_array_end:protobuf_unittest.Aggregate)
  return target;
}

size_t Aggregate::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:protobuf_unittest.Aggregate)
  size_t total_size = 0;

  if (_has_bits_[0 / 32] & 31u) {
    // optional int32 i = 1;
    if (has_i()) {
      total_size += 1 +
        ::google::protobuf::internal::WireFormatLite::Int32Size(
          this->i());
    }

    // optional string s = 2;
    if (has_s()) {
      total_size += 1 +
        ::google::protobuf::internal::WireFormatLite::StringSize(
          this->s());
    }

    // optional .protobuf_unittest.Aggregate sub = 3;
    if (has_sub()) {
      total_size += 1 +
        ::google::protobuf::internal::WireFormatLite::MessageSizeNoVirtual(
          *this->sub_);
    }

    // optional .google.protobuf.FileOptions file = 4;
    if (has_file()) {
      total_size += 1 +
        ::google::protobuf::internal::WireFormatLite::MessageSizeNoVirtual(
          *this->file_);
    }

    // optional .protobuf_unittest.AggregateMessageSet mset = 5;
    if (has_mset()) {
      total_size += 1 +
        ::google::protobuf::internal::WireFormatLite::MessageSizeNoVirtual(
          *this->mset_);
    }

  }
  if (_internal_metadata_.have_unknown_fields()) {
    total_size +=
      ::google::protobuf::internal::WireFormat::ComputeUnknownFieldsSize(
        unknown_fields());
  }
  int cached_size = ::google::protobuf::internal::ToCachedSize(total_size);
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = cached_size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
  return total_size;
}

void Aggregate::MergeFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_merge_from_start:protobuf_unittest.Aggregate)
  if (GOOGLE_PREDICT_FALSE(&from == this)) MergeFromFail(__LINE__);
  const Aggregate* source =
      ::google::protobuf::internal::DynamicCastToGenerated<const Aggregate>(
          &from);
  if (source == NULL) {
  // @@protoc_insertion_point(generalized_merge_from_cast_fail:protobuf_unittest.Aggregate)
    ::google::protobuf::internal::ReflectionOps::Merge(from, this);
  } else {
  // @@protoc_insertion_point(generalized_merge_from_cast_success:protobuf_unittest.Aggregate)
    UnsafeMergeFrom(*source);
  }
}

void Aggregate::MergeFrom(const Aggregate& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:protobuf_unittest.Aggregate)
  if (GOOGLE_PREDICT_TRUE(&from != this)) {
    UnsafeMergeFrom(from);
  } else {
    MergeFromFail(__LINE__);
  }
}

void Aggregate::UnsafeMergeFrom(const Aggregate& from) {
  GOOGLE_DCHECK(&from != this);
  if (from._has_bits_[0 / 32] & (0xffu << (0 % 32))) {
    if (from.has_i()) {
      set_i(from.i());
    }
    if (from.has_s()) {
      set_has_s();
      s_.AssignWithDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), from.s_);
    }
    if (from.has_sub()) {
      mutable_sub()->::protobuf_unittest::Aggregate::MergeFrom(from.sub());
    }
    if (from.has_file()) {
      mutable_file()->::google::protobuf::FileOptions::MergeFrom(from.file());
    }
    if (from.has_mset()) {
      mutable_mset()->::protobuf_unittest::AggregateMessageSet::MergeFrom(from.mset());
    }
  }
  if (from._internal_metadata_.have_unknown_fields()) {
    ::google::protobuf::UnknownFieldSet::MergeToInternalMetdata(
      from.unknown_fields(), &_internal_metadata_);
  }
}

void Aggregate::CopyFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_copy_from_start:protobuf_unittest.Aggregate)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

void Aggregate::CopyFrom(const Aggregate& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:protobuf_unittest.Aggregate)
  if (&from == this) return;
  Clear();
  UnsafeMergeFrom(from);
}

bool Aggregate::IsInitialized() const {

  if (has_sub()) {
    if (!this->sub_->IsInitialized()) return false;
  }
  if (has_file()) {
    if (!this->file_->IsInitialized()) return false;
  }
  if (has_mset()) {
    if (!this->mset_->IsInitialized()) return false;
  }
  return true;
}

void Aggregate::Swap(Aggregate* other) {
  if (other == this) return;
  InternalSwap(other);
}
void Aggregate::InternalSwap(Aggregate* other) {
  std::swap(i_, other->i_);
  s_.Swap(&other->s_);
  std::swap(sub_, other->sub_);
  std::swap(file_, other->file_);
  std::swap(mset_, other->mset_);
  std::swap(_has_bits_[0], other->_has_bits_[0]);
  _internal_metadata_.Swap(&other->_internal_metadata_);
  std::swap(_cached_size_, other->_cached_size_);
}

::google::protobuf::Metadata Aggregate::GetMetadata() const {
  protobuf_AssignDescriptorsOnce();
  ::google::protobuf::Metadata metadata;
  metadata.descriptor = Aggregate_descriptor_;
  metadata.reflection = Aggregate_reflection_;
  return metadata;
}

#if PROTOBUF_INLINE_NOT_IN_HEADERS
// Aggregate

// optional int32 i = 1;
bool Aggregate::has_i() const {
  return (_has_bits_[0] & 0x00000001u) != 0;
}
void Aggregate::set_has_i() {
  _has_bits_[0] |= 0x00000001u;
}
void Aggregate::clear_has_i() {
  _has_bits_[0] &= ~0x00000001u;
}
void Aggregate::clear_i() {
  i_ = 0;
  clear_has_i();
}
::google::protobuf::int32 Aggregate::i() const {
  // @@protoc_insertion_point(field_get:protobuf_unittest.Aggregate.i)
  return i_;
}
void Aggregate::set_i(::google::protobuf::int32 value) {
  set_has_i();
  i_ = value;
  // @@protoc_insertion_point(field_set:protobuf_unittest.Aggregate.i)
}

// optional string s = 2;
bool Aggregate::has_s() const {
  return (_has_bits_[0] & 0x00000002u) != 0;
}
void Aggregate::set_has_s() {
  _has_bits_[0] |= 0x00000002u;
}
void Aggregate::clear_has_s() {
  _has_bits_[0] &= ~0x00000002u;
}
void Aggregate::clear_s() {
  s_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  clear_has_s();
}
const ::std::string& Aggregate::s() const {
  // @@protoc_insertion_point(field_get:protobuf_unittest.Aggregate.s)
  return s_.GetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
void Aggregate::set_s(const ::std::string& value) {
  set_has_s();
  s_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:protobuf_unittest.Aggregate.s)
}
void Aggregate::set_s(const char* value) {
  set_has_s();
  s_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:protobuf_unittest.Aggregate.s)
}
void Aggregate::set_s(const char* value, size_t size) {
  set_has_s();
  s_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:protobuf_unittest.Aggregate.s)
}
::std::string* Aggregate::mutable_s() {
  set_has_s();
  // @@protoc_insertion_point(field_mutable:protobuf_unittest.Aggregate.s)
  return s_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
::std::string* Aggregate::release_s() {
  // @@protoc_insertion_point(field_release:protobuf_unittest.Aggregate.s)
  clear_has_s();
  return s_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
void Aggregate::set_allocated_s(::std::string* s) {
  if (s != NULL) {
    set_has_s();
  } else {
    clear_has_s();
  }
  s_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), s);
  // @@protoc_insertion_point(field_set_allocated:protobuf_unittest.Aggregate.s)
}

// optional .protobuf_unittest.Aggregate sub = 3;
bool Aggregate::has_sub() const {
  return (_has_bits_[0] & 0x00000004u) != 0;
}
void Aggregate::set_has_sub() {
  _has_bits_[0] |= 0x00000004u;
}
void Aggregate::clear_has_sub() {
  _has_bits_[0] &= ~0x00000004u;
}
void Aggregate::clear_sub() {
  if (sub_ != NULL) sub_->::protobuf_unittest::Aggregate::Clear();
  clear_has_sub();
}
const ::protobuf_unittest::Aggregate& Aggregate::sub() const {
  // @@protoc_insertion_point(field_get:protobuf_unittest.Aggregate.sub)
  return sub_ != NULL ? *sub_
                         : *::protobuf_unittest::Aggregate::internal_default_instance();
}
::protobuf_unittest::Aggregate* Aggregate::mutable_sub() {
  set_has_sub();
  if (sub_ == NULL) {
    sub_ = new ::protobuf_unittest::Aggregate;
  }
  // @@protoc_insertion_point(field_mutable:protobuf_unittest.Aggregate.sub)
  return sub_;
}
::protobuf_unittest::Aggregate* Aggregate::release_sub() {
  // @@protoc_insertion_point(field_release:protobuf_unittest.Aggregate.sub)
  clear_has_sub();
  ::protobuf_unittest::Aggregate* temp = sub_;
  sub_ = NULL;
  return temp;
}
void Aggregate::set_allocated_sub(::protobuf_unittest::Aggregate* sub) {
  delete sub_;
  sub_ = sub;
  if (sub) {
    set_has_sub();
  } else {
    clear_has_sub();
  }
  // @@protoc_insertion_point(field_set_allocated:protobuf_unittest.Aggregate.sub)
}

// optional .google.protobuf.FileOptions file = 4;
bool Aggregate::has_file() const {
  return (_has_bits_[0] & 0x00000008u) != 0;
}
void Aggregate::set_has_file() {
  _has_bits_[0] |= 0x00000008u;
}
void Aggregate::clear_has_file() {
  _has_bits_[0] &= ~0x00000008u;
}
void Aggregate::clear_file() {
  if (file_ != NULL) file_->::google::protobuf::FileOptions::Clear();
  clear_has_file();
}
const ::google::protobuf::FileOptions& Aggregate::file() const {
  // @@protoc_insertion_point(field_get:protobuf_unittest.Aggregate.file)
  return file_ != NULL ? *file_
                         : *::google::protobuf::FileOptions::internal_default_instance();
}
::google::protobuf::FileOptions* Aggregate::mutable_file() {
  set_has_file();
  if (file_ == NULL) {
    file_ = new ::google::protobuf::FileOptions;
  }
  // @@protoc_insertion_point(field_mutable:protobuf_unittest.Aggregate.file)
  return file_;
}
::google::protobuf::FileOptions* Aggregate::release_file() {
  // @@protoc_insertion_point(field_release:protobuf_unittest.Aggregate.file)
  clear_has_file();
  ::google::protobuf::FileOptions* temp = file_;
  file_ = NULL;
  return temp;
}
void Aggregate::set_allocated_file(::google::protobuf::FileOptions* file) {
  delete file_;
  file_ = file;
  if (file) {
    set_has_file();
  } else {
    clear_has_file();
  }
  // @@protoc_insertion_point(field_set_allocated:protobuf_unittest.Aggregate.file)
}

// optional .protobuf_unittest.AggregateMessageSet mset = 5;
bool Aggregate::has_mset() const {
  return (_has_bits_[0] & 0x00000010u) != 0;
}
void Aggregate::set_has_mset() {
  _has_bits_[0] |= 0x00000010u;
}
void Aggregate::clear_has_mset() {
  _has_bits_[0] &= ~0x00000010u;
}
void Aggregate::clear_mset() {
  if (mset_ != NULL) mset_->::protobuf_unittest::AggregateMessageSet::Clear();
  clear_has_mset();
}
const ::protobuf_unittest::AggregateMessageSet& Aggregate::mset() const {
  // @@protoc_insertion_point(field_get:protobuf_unittest.Aggregate.mset)
  return mset_ != NULL ? *mset_
                         : *::protobuf_unittest::AggregateMessageSet::internal_default_instance();
}
::protobuf_unittest::AggregateMessageSet* Aggregate::mutable_mset() {
  set_has_mset();
  if (mset_ == NULL) {
    mset_ = new ::protobuf_unittest::AggregateMessageSet;
  }
  // @@protoc_insertion_point(field_mutable:protobuf_unittest.Aggregate.mset)
  return mset_;
}
::protobuf_unittest::AggregateMessageSet* Aggregate::release_mset() {
  // @@protoc_insertion_point(field_release:protobuf_unittest.Aggregate.mset)
  clear_has_mset();
  ::protobuf_unittest::AggregateMessageSet* temp = mset_;
  mset_ = NULL;
  return temp;
}
void Aggregate::set_allocated_mset(::protobuf_unittest::AggregateMessageSet* mset) {
  delete mset_;
  mset_ = mset;
  if (mset) {
    set_has_mset();
  } else {
    clear_has_mset();
  }
  // @@protoc_insertion_point(field_set_allocated:protobuf_unittest.Aggregate.mset)
}

inline const Aggregate* Aggregate::internal_default_instance() {
  return &Aggregate_default_instance_.get();
}
#endif  // PROTOBUF_INLINE_NOT_IN_HEADERS

// ===================================================================

#if !defined(_MSC_VER) || _MSC_VER >= 1900
const int AggregateMessage::kFieldnameFieldNumber;
#endif  // !defined(_MSC_VER) || _MSC_VER >= 1900

AggregateMessage::AggregateMessage()
  : ::google::protobuf::Message(), _internal_metadata_(NULL) {
  if (this != internal_default_instance()) protobuf_InitDefaults_google_2fprotobuf_2funittest_5fcustom_5foptions_2eproto();
  SharedCtor();
  // @@protoc_insertion_point(constructor:protobuf_unittest.AggregateMessage)
}

void AggregateMessage::InitAsDefaultInstance() {
}

AggregateMessage::AggregateMessage(const AggregateMessage& from)
  : ::google::protobuf::Message(),
    _internal_metadata_(NULL) {
  SharedCtor();
  UnsafeMergeFrom(from);
  // @@protoc_insertion_point(copy_constructor:protobuf_unittest.AggregateMessage)
}

void AggregateMessage::SharedCtor() {
  _cached_size_ = 0;
  fieldname_ = 0;
}

AggregateMessage::~AggregateMessage() {
  // @@protoc_insertion_point(destructor:protobuf_unittest.AggregateMessage)
  SharedDtor();
}

void AggregateMessage::SharedDtor() {
}

void AggregateMessage::SetCachedSize(int size) const {
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
}
const ::google::protobuf::Descriptor* AggregateMessage::descriptor() {
  protobuf_AssignDescriptorsOnce();
  return AggregateMessage_descriptor_;
}

const AggregateMessage& AggregateMessage::default_instance() {
  protobuf_InitDefaults_google_2fprotobuf_2funittest_5fcustom_5foptions_2eproto();
  return *internal_default_instance();
}

::google::protobuf::internal::ExplicitlyConstructed<AggregateMessage> AggregateMessage_default_instance_;

AggregateMessage* AggregateMessage::New(::google::protobuf::Arena* arena) const {
  AggregateMessage* n = new AggregateMessage;
  if (arena != NULL) {
    arena->Own(n);
  }
  return n;
}

void AggregateMessage::Clear() {
// @@protoc_insertion_point(message_clear_start:protobuf_unittest.AggregateMessage)
  fieldname_ = 0;
  _has_bits_.Clear();
  if (_internal_metadata_.have_unknown_fields()) {
    mutable_unknown_fields()->Clear();
  }
}

bool AggregateMessage::MergePartialFromCodedStream(
    ::google::protobuf::io::CodedInputStream* input) {
#define DO_(EXPRESSION) if (!GOOGLE_PREDICT_TRUE(EXPRESSION)) goto failure
  ::google::protobuf::uint32 tag;
  // @@protoc_insertion_point(parse_start:protobuf_unittest.AggregateMessage)
  for (;;) {
    ::std::pair< ::google::protobuf::uint32, bool> p = input->ReadTagWithCutoff(127);
    tag = p.first;
    if (!p.second) goto handle_unusual;
    switch (::google::protobuf::internal::WireFormatLite::GetTagFieldNumber(tag)) {
      // optional int32 fieldname = 1;
      case 1: {
        if (tag == 8) {
          set_has_fieldname();
          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int32, ::google::protobuf::internal::WireFormatLite::TYPE_INT32>(
                 input, &fieldname_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectAtEnd()) goto success;
        break;
      }

      default: {
      handle_unusual:
        if (tag == 0 ||
            ::google::protobuf::internal::WireFormatLite::GetTagWireType(tag) ==
            ::google::protobuf::internal::WireFormatLite::WIRETYPE_END_GROUP) {
          goto success;
        }
        DO_(::google::protobuf::internal::WireFormat::SkipField(
              input, tag, mutable_unknown_fields()));
        break;
      }
    }
  }
success:
  // @@protoc_insertion_point(parse_success:protobuf_unittest.AggregateMessage)
  return true;
failure:
  // @@protoc_insertion_point(parse_failure:protobuf_unittest.AggregateMessage)
  return false;
#undef DO_
}

void AggregateMessage::SerializeWithCachedSizes(
    ::google::protobuf::io::CodedOutputStream* output) const {
  // @@protoc_insertion_point(serialize_start:protobuf_unittest.AggregateMessage)
  // optional int32 fieldname = 1;
  if (has_fieldname()) {
    ::google::protobuf::internal::WireFormatLite::WriteInt32(1, this->fieldname(), output);
  }

  if (_internal_metadata_.have_unknown_fields()) {
    ::google::protobuf::internal::WireFormat::SerializeUnknownFields(
        unknown_fields(), output);
  }
  // @@protoc_insertion_point(serialize_end:protobuf_unittest.AggregateMessage)
}

::google::protobuf::uint8* AggregateMessage::InternalSerializeWithCachedSizesToArray(
    bool deterministic, ::google::protobuf::uint8* target) const {
  (void)deterministic; // Unused
  // @@protoc_insertion_point(serialize_to_array_start:protobuf_unittest.AggregateMessage)
  // optional int32 fieldname = 1;
  if (has_fieldname()) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt32ToArray(1, this->fieldname(), target);
  }

  if (_internal_metadata_.have_unknown_fields()) {
    target = ::google::protobuf::internal::WireFormat::SerializeUnknownFieldsToArray(
        unknown_fields(), target);
  }
  // @@protoc_insertion_point(serialize_to_array_end:protobuf_unittest.AggregateMessage)
  return target;
}

size_t AggregateMessage::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:protobuf_unittest.AggregateMessage)
  size_t total_size = 0;

  // optional int32 fieldname = 1;
  if (has_fieldname()) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::Int32Size(
        this->fieldname());
  }

  if (_internal_metadata_.have_unknown_fields()) {
    total_size +=
      ::google::protobuf::internal::WireFormat::ComputeUnknownFieldsSize(
        unknown_fields());
  }
  int cached_size = ::google::protobuf::internal::ToCachedSize(total_size);
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = cached_size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
  return total_size;
}

void AggregateMessage::MergeFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_merge_from_start:protobuf_unittest.AggregateMessage)
  if (GOOGLE_PREDICT_FALSE(&from == this)) MergeFromFail(__LINE__);
  const AggregateMessage* source =
      ::google::protobuf::internal::DynamicCastToGenerated<const AggregateMessage>(
          &from);
  if (source == NULL) {
  // @@protoc_insertion_point(generalized_merge_from_cast_fail:protobuf_unittest.AggregateMessage)
    ::google::protobuf::internal::ReflectionOps::Merge(from, this);
  } else {
  // @@protoc_insertion_point(generalized_merge_from_cast_success:protobuf_unittest.AggregateMessage)
    UnsafeMergeFrom(*source);
  }
}

void AggregateMessage::MergeFrom(const AggregateMessage& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:protobuf_unittest.AggregateMessage)
  if (GOOGLE_PREDICT_TRUE(&from != this)) {
    UnsafeMergeFrom(from);
  } else {
    MergeFromFail(__LINE__);
  }
}

void AggregateMessage::UnsafeMergeFrom(const AggregateMessage& from) {
  GOOGLE_DCHECK(&from != this);
  if (from._has_bits_[0 / 32] & (0xffu << (0 % 32))) {
    if (from.has_fieldname()) {
      set_fieldname(from.fieldname());
    }
  }
  if (from._internal_metadata_.have_unknown_fields()) {
    ::google::protobuf::UnknownFieldSet::MergeToInternalMetdata(
      from.unknown_fields(), &_internal_metadata_);
  }
}

void AggregateMessage::CopyFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_copy_from_start:protobuf_unittest.AggregateMessage)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

void AggregateMessage::CopyFrom(const AggregateMessage& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:protobuf_unittest.AggregateMessage)
  if (&from == this) return;
  Clear();
  UnsafeMergeFrom(from);
}

bool AggregateMessage::IsInitialized() const {

  return true;
}

void AggregateMessage::Swap(AggregateMessage* other) {
  if (other == this) return;
  InternalSwap(other);
}
void AggregateMessage::InternalSwap(AggregateMessage* other) {
  std::swap(fieldname_, other->fieldname_);
  std::swap(_has_bits_[0], other->_has_bits_[0]);
  _internal_metadata_.Swap(&other->_internal_metadata_);
  std::swap(_cached_size_, other->_cached_size_);
}

::google::protobuf::Metadata AggregateMessage::GetMetadata() const {
  protobuf_AssignDescriptorsOnce();
  ::google::protobuf::Metadata metadata;
  metadata.descriptor = AggregateMessage_descriptor_;
  metadata.reflection = AggregateMessage_reflection_;
  return metadata;
}

#if PROTOBUF_INLINE_NOT_IN_HEADERS
// AggregateMessage

// optional int32 fieldname = 1;
bool AggregateMessage::has_fieldname() const {
  return (_has_bits_[0] & 0x00000001u) != 0;
}
void AggregateMessage::set_has_fieldname() {
  _has_bits_[0] |= 0x00000001u;
}
void AggregateMessage::clear_has_fieldname() {
  _has_bits_[0] &= ~0x00000001u;
}
void AggregateMessage::clear_fieldname() {
  fieldname_ = 0;
  clear_has_fieldname();
}
::google::protobuf::int32 AggregateMessage::fieldname() const {
  // @@protoc_insertion_point(field_get:protobuf_unittest.AggregateMessage.fieldname)
  return fieldname_;
}
void AggregateMessage::set_fieldname(::google::protobuf::int32 value) {
  set_has_fieldname();
  fieldname_ = value;
  // @@protoc_insertion_point(field_set:protobuf_unittest.AggregateMessage.fieldname)
}

inline const AggregateMessage* AggregateMessage::internal_default_instance() {
  return &AggregateMessage_default_instance_.get();
}
#endif  // PROTOBUF_INLINE_NOT_IN_HEADERS

// ===================================================================

const ::google::protobuf::EnumDescriptor* NestedOptionType_NestedEnum_descriptor() {
  protobuf_AssignDescriptorsOnce();
  return NestedOptionType_NestedEnum_descriptor_;
}
bool NestedOptionType_NestedEnum_IsValid(int value) {
  switch (value) {
    case 1:
      return true;
    default:
      return false;
  }
}

#if !defined(_MSC_VER) || _MSC_VER >= 1900
const NestedOptionType_NestedEnum NestedOptionType::NESTED_ENUM_VALUE;
const NestedOptionType_NestedEnum NestedOptionType::NestedEnum_MIN;
const NestedOptionType_NestedEnum NestedOptionType::NestedEnum_MAX;
const int NestedOptionType::NestedEnum_ARRAYSIZE;
#endif  // !defined(_MSC_VER) || _MSC_VER >= 1900
#if !defined(_MSC_VER) || _MSC_VER >= 1900
const int NestedOptionType_NestedMessage::kNestedFieldFieldNumber;
#endif  // !defined(_MSC_VER) || _MSC_VER >= 1900

NestedOptionType_NestedMessage::NestedOptionType_NestedMessage()
  : ::google::protobuf::Message(), _internal_metadata_(NULL) {
  if (this != internal_default_instance()) protobuf_InitDefaults_google_2fprotobuf_2funittest_5fcustom_5foptions_2eproto();
  SharedCtor();
  // @@protoc_insertion_point(constructor:protobuf_unittest.NestedOptionType.NestedMessage)
}

void NestedOptionType_NestedMessage::InitAsDefaultInstance() {
}

NestedOptionType_NestedMessage::NestedOptionType_NestedMessage(const NestedOptionType_NestedMessage& from)
  : ::google::protobuf::Message(),
    _internal_metadata_(NULL) {
  SharedCtor();
  UnsafeMergeFrom(from);
  // @@protoc_insertion_point(copy_constructor:protobuf_unittest.NestedOptionType.NestedMessage)
}

void NestedOptionType_NestedMessage::SharedCtor() {
  _cached_size_ = 0;
  nested_field_ = 0;
}

NestedOptionType_NestedMessage::~NestedOptionType_NestedMessage() {
  // @@protoc_insertion_point(destructor:protobuf_unittest.NestedOptionType.NestedMessage)
  SharedDtor();
}

void NestedOptionType_NestedMessage::SharedDtor() {
}

void NestedOptionType_NestedMessage::SetCachedSize(int size) const {
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
}
const ::google::protobuf::Descriptor* NestedOptionType_NestedMessage::descriptor() {
  protobuf_AssignDescriptorsOnce();
  return NestedOptionType_NestedMessage_descriptor_;
}

const NestedOptionType_NestedMessage& NestedOptionType_NestedMessage::default_instance() {
  protobuf_InitDefaults_google_2fprotobuf_2funittest_5fcustom_5foptions_2eproto();
  return *internal_default_instance();
}

::google::protobuf::internal::ExplicitlyConstructed<NestedOptionType_NestedMessage> NestedOptionType_NestedMessage_default_instance_;

NestedOptionType_NestedMessage* NestedOptionType_NestedMessage::New(::google::protobuf::Arena* arena) const {
  NestedOptionType_NestedMessage* n = new NestedOptionType_NestedMessage;
  if (arena != NULL) {
    arena->Own(n);
  }
  return n;
}

void NestedOptionType_NestedMessage::Clear() {
// @@protoc_insertion_point(message_clear_start:protobuf_unittest.NestedOptionType.NestedMessage)
  nested_field_ = 0;
  _has_bits_.Clear();
  if (_internal_metadata_.have_unknown_fields()) {
    mutable_unknown_fields()->Clear();
  }
}

bool NestedOptionType_NestedMessage::MergePartialFromCodedStream(
    ::google::protobuf::io::CodedInputStream* input) {
#define DO_(EXPRESSION) if (!GOOGLE_PREDICT_TRUE(EXPRESSION)) goto failure
  ::google::protobuf::uint32 tag;
  // @@protoc_insertion_point(parse_start:protobuf_unittest.NestedOptionType.NestedMessage)
  for (;;) {
    ::std::pair< ::google::protobuf::uint32, bool> p = input->ReadTagWithCutoff(127);
    tag = p.first;
    if (!p.second) goto handle_unusual;
    switch (::google::protobuf::internal::WireFormatLite::GetTagFieldNumber(tag)) {
      // optional int32 nested_field = 1;
      case 1: {
        if (tag == 8) {
          set_has_nested_field();
          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int32, ::google::protobuf::internal::WireFormatLite::TYPE_INT32>(
                 input, &nested_field_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectAtEnd()) goto success;
        break;
      }

      default: {
      handle_unusual:
        if (tag == 0 ||
            ::google::protobuf::internal::WireFormatLite::GetTagWireType(tag) ==
            ::google::protobuf::internal::WireFormatLite::WIRETYPE_END_GROUP) {
          goto success;
        }
        DO_(::google::protobuf::internal::WireFormat::SkipField(
              input, tag, mutable_unknown_fields()));
        break;
      }
    }
  }
success:
  // @@protoc_insertion_point(parse_success:protobuf_unittest.NestedOptionType.NestedMessage)
  return true;
failure:
  // @@protoc_insertion_point(parse_failure:protobuf_unittest.NestedOptionType.NestedMessage)
  return false;
#undef DO_
}

void NestedOptionType_NestedMessage::SerializeWithCachedSizes(
    ::google::protobuf::io::CodedOutputStream* output) const {
  // @@protoc_insertion_point(serialize_start:protobuf_unittest.NestedOptionType.NestedMessage)
  // optional int32 nested_field = 1;
  if (has_nested_field()) {
    ::google::protobuf::internal::WireFormatLite::WriteInt32(1, this->nested_field(), output);
  }

  if (_internal_metadata_.have_unknown_fields()) {
    ::google::protobuf::internal::WireFormat::SerializeUnknownFields(
        unknown_fields(), output);
  }
  // @@protoc_insertion_point(serialize_end:protobuf_unittest.NestedOptionType.NestedMessage)
}

::google::protobuf::uint8* NestedOptionType_NestedMessage::InternalSerializeWithCachedSizesToArray(
    bool deterministic, ::google::protobuf::uint8* target) const {
  (void)deterministic; // Unused
  // @@protoc_insertion_point(serialize_to_array_start:protobuf_unittest.NestedOptionType.NestedMessage)
  // optional int32 nested_field = 1;
  if (has_nested_field()) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt32ToArray(1, this->nested_field(), target);
  }

  if (_internal_metadata_.have_unknown_fields()) {
    target = ::google::protobuf::internal::WireFormat::SerializeUnknownFieldsToArray(
        unknown_fields(), target);
  }
  // @@protoc_insertion_point(serialize_to_array_end:protobuf_unittest.NestedOptionType.NestedMessage)
  return target;
}

size_t NestedOptionType_NestedMessage::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:protobuf_unittest.NestedOptionType.NestedMessage)
  size_t total_size = 0;

  // optional int32 nested_field = 1;
  if (has_nested_field()) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::Int32Size(
        this->nested_field());
  }

  if (_internal_metadata_.have_unknown_fields()) {
    total_size +=
      ::google::protobuf::internal::WireFormat::ComputeUnknownFieldsSize(
        unknown_fields());
  }
  int cached_size = ::google::protobuf::internal::ToCachedSize(total_size);
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = cached_size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
  return total_size;
}

void NestedOptionType_NestedMessage::MergeFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_merge_from_start:protobuf_unittest.NestedOptionType.NestedMessage)
  if (GOOGLE_PREDICT_FALSE(&from == this)) MergeFromFail(__LINE__);
  const NestedOptionType_NestedMessage* source =
      ::google::protobuf::internal::DynamicCastToGenerated<const NestedOptionType_NestedMessage>(
          &from);
  if (source == NULL) {
  // @@protoc_insertion_point(generalized_merge_from_cast_fail:protobuf_unittest.NestedOptionType.NestedMessage)
    ::google::protobuf::internal::ReflectionOps::Merge(from, this);
  } else {
  // @@protoc_insertion_point(generalized_merge_from_cast_success:protobuf_unittest.NestedOptionType.NestedMessage)
    UnsafeMergeFrom(*source);
  }
}

void NestedOptionType_NestedMessage::MergeFrom(const NestedOptionType_NestedMessage& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:protobuf_unittest.NestedOptionType.NestedMessage)
  if (GOOGLE_PREDICT_TRUE(&from != this)) {
    UnsafeMergeFrom(from);
  } else {
    MergeFromFail(__LINE__);
  }
}

void NestedOptionType_NestedMessage::UnsafeMergeFrom(const NestedOptionType_NestedMessage& from) {
  GOOGLE_DCHECK(&from != this);
  if (from._has_bits_[0 / 32] & (0xffu << (0 % 32))) {
    if (from.has_nested_field()) {
      set_nested_field(from.nested_field());
    }
  }
  if (from._internal_metadata_.have_unknown_fields()) {
    ::google::protobuf::UnknownFieldSet::MergeToInternalMetdata(
      from.unknown_fields(), &_internal_metadata_);
  }
}

void NestedOptionType_NestedMessage::CopyFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_copy_from_start:protobuf_unittest.NestedOptionType.NestedMessage)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

void NestedOptionType_NestedMessage::CopyFrom(const NestedOptionType_NestedMessage& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:protobuf_unittest.NestedOptionType.NestedMessage)
  if (&from == this) return;
  Clear();
  UnsafeMergeFrom(from);
}

bool NestedOptionType_NestedMessage::IsInitialized() const {

  return true;
}

void NestedOptionType_NestedMessage::Swap(NestedOptionType_NestedMessage* other) {
  if (other == this) return;
  InternalSwap(other);
}
void NestedOptionType_NestedMessage::InternalSwap(NestedOptionType_NestedMessage* other) {
  std::swap(nested_field_, other->nested_field_);
  std::swap(_has_bits_[0], other->_has_bits_[0]);
  _internal_metadata_.Swap(&other->_internal_metadata_);
  std::swap(_cached_size_, other->_cached_size_);
}

::google::protobuf::Metadata NestedOptionType_NestedMessage::GetMetadata() const {
  protobuf_AssignDescriptorsOnce();
  ::google::protobuf::Metadata metadata;
  metadata.descriptor = NestedOptionType_NestedMessage_descriptor_;
  metadata.reflection = NestedOptionType_NestedMessage_reflection_;
  return metadata;
}


// -------------------------------------------------------------------

#if !defined(_MSC_VER) || _MSC_VER >= 1900
#endif  // !defined(_MSC_VER) || _MSC_VER >= 1900

#if !defined(_MSC_VER) || _MSC_VER >= 1900
const int NestedOptionType::kNestedExtensionFieldNumber;
#endif
::google::protobuf::internal::ExtensionIdentifier< ::google::protobuf::FileOptions,
    ::google::protobuf::internal::PrimitiveTypeTraits< ::google::protobuf::int32 >, 5, false >
  NestedOptionType::nested_extension(kNestedExtensionFieldNumber, 0);
NestedOptionType::NestedOptionType()
  : ::google::protobuf::Message(), _internal_metadata_(NULL) {
  if (this != internal_default_instance()) protobuf_InitDefaults_google_2fprotobuf_2funittest_5fcustom_5foptions_2eproto();
  SharedCtor();
  // @@protoc_insertion_point(constructor:protobuf_unittest.NestedOptionType)
}

void NestedOptionType::InitAsDefaultInstance() {
}

NestedOptionType::NestedOptionType(const NestedOptionType& from)
  : ::google::protobuf::Message(),
    _internal_metadata_(NULL) {
  SharedCtor();
  UnsafeMergeFrom(from);
  // @@protoc_insertion_point(copy_constructor:protobuf_unittest.NestedOptionType)
}

void NestedOptionType::SharedCtor() {
  _cached_size_ = 0;
}

NestedOptionType::~NestedOptionType() {
  // @@protoc_insertion_point(destructor:protobuf_unittest.NestedOptionType)
  SharedDtor();
}

void NestedOptionType::SharedDtor() {
}

void NestedOptionType::SetCachedSize(int size) const {
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
}
const ::google::protobuf::Descriptor* NestedOptionType::descriptor() {
  protobuf_AssignDescriptorsOnce();
  return NestedOptionType_descriptor_;
}

const NestedOptionType& NestedOptionType::default_instance() {
  protobuf_InitDefaults_google_2fprotobuf_2funittest_5fcustom_5foptions_2eproto();
  return *internal_default_instance();
}

::google::protobuf::internal::ExplicitlyConstructed<NestedOptionType> NestedOptionType_default_instance_;

NestedOptionType* NestedOptionType::New(::google::protobuf::Arena* arena) const {
  NestedOptionType* n = new NestedOptionType;
  if (arena != NULL) {
    arena->Own(n);
  }
  return n;
}

void NestedOptionType::Clear() {
// @@protoc_insertion_point(message_clear_start:protobuf_unittest.NestedOptionType)
  _has_bits_.Clear();
  if (_internal_metadata_.have_unknown_fields()) {
    mutable_unknown_fields()->Clear();
  }
}

bool NestedOptionType::MergePartialFromCodedStream(
    ::google::protobuf::io::CodedInputStream* input) {
#define DO_(EXPRESSION) if (!GOOGLE_PREDICT_TRUE(EXPRESSION)) goto failure
  ::google::protobuf::uint32 tag;
  // @@protoc_insertion_point(parse_start:protobuf_unittest.NestedOptionType)
  for (;;) {
    ::std::pair< ::google::protobuf::uint32, bool> p = input->ReadTagWithCutoff(127);
    tag = p.first;
    if (!p.second) goto handle_unusual;
  handle_unusual:
    if (tag == 0 ||
        ::google::protobuf::internal::WireFormatLite::GetTagWireType(tag) ==
        ::google::protobuf::internal::WireFormatLite::WIRETYPE_END_GROUP) {
      goto success;
    }
    DO_(::google::protobuf::internal::WireFormat::SkipField(
          input, tag, mutable_unknown_fields()));
  }
success:
  // @@protoc_insertion_point(parse_success:protobuf_unittest.NestedOptionType)
  return true;
failure:
  // @@protoc_insertion_point(parse_failure:protobuf_unittest.NestedOptionType)
  return false;
#undef DO_
}

void NestedOptionType::SerializeWithCachedSizes(
    ::google::protobuf::io::CodedOutputStream* output) const {
  // @@protoc_insertion_point(serialize_start:protobuf_unittest.NestedOptionType)
  if (_internal_metadata_.have_unknown_fields()) {
    ::google::protobuf::internal::WireFormat::SerializeUnknownFields(
        unknown_fields(), output);
  }
  // @@protoc_insertion_point(serialize_end:protobuf_unittest.NestedOptionType)
}

::google::protobuf::uint8* NestedOptionType::InternalSerializeWithCachedSizesToArray(
    bool deterministic, ::google::protobuf::uint8* target) const {
  (void)deterministic; // Unused
  // @@protoc_insertion_point(serialize_to_array_start:protobuf_unittest.NestedOptionType)
  if (_internal_metadata_.have_unknown_fields()) {
    target = ::google::protobuf::internal::WireFormat::SerializeUnknownFieldsToArray(
        unknown_fields(), target);
  }
  // @@protoc_insertion_point(serialize_to_array_end:protobuf_unittest.NestedOptionType)
  return target;
}

size_t NestedOptionType::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:protobuf_unittest.NestedOptionType)
  size_t total_size = 0;

  if (_internal_metadata_.have_unknown_fields()) {
    total_size +=
      ::google::protobuf::internal::WireFormat::ComputeUnknownFieldsSize(
        unknown_fields());
  }
  int cached_size = ::google::protobuf::internal::ToCachedSize(total_size);
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = cached_size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
  return total_size;
}

void NestedOptionType::MergeFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_merge_from_start:protobuf_unittest.NestedOptionType)
  if (GOOGLE_PREDICT_FALSE(&from == this)) MergeFromFail(__LINE__);
  const NestedOptionType* source =
      ::google::protobuf::internal::DynamicCastToGenerated<const NestedOptionType>(
          &from);
  if (source == NULL) {
  // @@protoc_insertion_point(generalized_merge_from_cast_fail:protobuf_unittest.NestedOptionType)
    ::google::protobuf::internal::ReflectionOps::Merge(from, this);
  } else {
  // @@protoc_insertion_point(generalized_merge_from_cast_success:protobuf_unittest.NestedOptionType)
    UnsafeMergeFrom(*source);
  }
}

void NestedOptionType::MergeFrom(const NestedOptionType& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:protobuf_unittest.NestedOptionType)
  if (GOOGLE_PREDICT_TRUE(&from != this)) {
    UnsafeMergeFrom(from);
  } else {
    MergeFromFail(__LINE__);
  }
}

void NestedOptionType::UnsafeMergeFrom(const NestedOptionType& from) {
  GOOGLE_DCHECK(&from != this);
  if (from._internal_metadata_.have_unknown_fields()) {
    ::google::protobuf::UnknownFieldSet::MergeToInternalMetdata(
      from.unknown_fields(), &_internal_metadata_);
  }
}

void NestedOptionType::CopyFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_copy_from_start:protobuf_unittest.NestedOptionType)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

void NestedOptionType::CopyFrom(const NestedOptionType& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:protobuf_unittest.NestedOptionType)
  if (&from == this) return;
  Clear();
  UnsafeMergeFrom(from);
}

bool NestedOptionType::IsInitialized() const {

  return true;
}

void NestedOptionType::Swap(NestedOptionType* other) {
  if (other == this) return;
  InternalSwap(other);
}
void NestedOptionType::InternalSwap(NestedOptionType* other) {
  _internal_metadata_.Swap(&other->_internal_metadata_);
  std::swap(_cached_size_, other->_cached_size_);
}

::google::protobuf::Metadata NestedOptionType::GetMetadata() const {
  protobuf_AssignDescriptorsOnce();
  ::google::protobuf::Metadata metadata;
  metadata.descriptor = NestedOptionType_descriptor_;
  metadata.reflection = NestedOptionType_reflection_;
  return metadata;
}

#if PROTOBUF_INLINE_NOT_IN_HEADERS
// NestedOptionType_NestedMessage

// optional int32 nested_field = 1;
bool NestedOptionType_NestedMessage::has_nested_field() const {
  return (_has_bits_[0] & 0x00000001u) != 0;
}
void NestedOptionType_NestedMessage::set_has_nested_field() {
  _has_bits_[0] |= 0x00000001u;
}
void NestedOptionType_NestedMessage::clear_has_nested_field() {
  _has_bits_[0] &= ~0x00000001u;
}
void NestedOptionType_NestedMessage::clear_nested_field() {
  nested_field_ = 0;
  clear_has_nested_field();
}
::google::protobuf::int32 NestedOptionType_NestedMessage::nested_field() const {
  // @@protoc_insertion_point(field_get:protobuf_unittest.NestedOptionType.NestedMessage.nested_field)
  return nested_field_;
}
void NestedOptionType_NestedMessage::set_nested_field(::google::protobuf::int32 value) {
  set_has_nested_field();
  nested_field_ = value;
  // @@protoc_insertion_point(field_set:protobuf_unittest.NestedOptionType.NestedMessage.nested_field)
}

inline const NestedOptionType_NestedMessage* NestedOptionType_NestedMessage::internal_default_instance() {
  return &NestedOptionType_NestedMessage_default_instance_.get();
}
// -------------------------------------------------------------------

// NestedOptionType

inline const NestedOptionType* NestedOptionType::internal_default_instance() {
  return &NestedOptionType_default_instance_.get();
}
#endif  // PROTOBUF_INLINE_NOT_IN_HEADERS

// ===================================================================

const ::google::protobuf::EnumDescriptor* OldOptionType_TestEnum_descriptor() {
  protobuf_AssignDescriptorsOnce();
  return OldOptionType_TestEnum_descriptor_;
}
bool OldOptionType_TestEnum_IsValid(int value) {
  switch (value) {
    case 0:
      return true;
    default:
      return false;
  }
}

#if !defined(_MSC_VER) || _MSC_VER >= 1900
const OldOptionType_TestEnum OldOptionType::OLD_VALUE;
const OldOptionType_TestEnum OldOptionType::TestEnum_MIN;
const OldOptionType_TestEnum OldOptionType::TestEnum_MAX;
const int OldOptionType::TestEnum_ARRAYSIZE;
#endif  // !defined(_MSC_VER) || _MSC_VER >= 1900
#if !defined(_MSC_VER) || _MSC_VER >= 1900
const int OldOptionType::kValueFieldNumber;
#endif  // !defined(_MSC_VER) || _MSC_VER >= 1900

OldOptionType::OldOptionType()
  : ::google::protobuf::Message(), _internal_metadata_(NULL) {
  if (this != internal_default_instance()) protobuf_InitDefaults_google_2fprotobuf_2funittest_5fcustom_5foptions_2eproto();
  SharedCtor();
  // @@protoc_insertion_point(constructor:protobuf_unittest.OldOptionType)
}

void OldOptionType::InitAsDefaultInstance() {
}

OldOptionType::OldOptionType(const OldOptionType& from)
  : ::google::protobuf::Message(),
    _internal_metadata_(NULL) {
  SharedCtor();
  UnsafeMergeFrom(from);
  // @@protoc_insertion_point(copy_constructor:protobuf_unittest.OldOptionType)
}

void OldOptionType::SharedCtor() {
  _cached_size_ = 0;
  value_ = 0;
}

OldOptionType::~OldOptionType() {
  // @@protoc_insertion_point(destructor:protobuf_unittest.OldOptionType)
  SharedDtor();
}

void OldOptionType::SharedDtor() {
}

void OldOptionType::SetCachedSize(int size) const {
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
}
const ::google::protobuf::Descriptor* OldOptionType::descriptor() {
  protobuf_AssignDescriptorsOnce();
  return OldOptionType_descriptor_;
}

const OldOptionType& OldOptionType::default_instance() {
  protobuf_InitDefaults_google_2fprotobuf_2funittest_5fcustom_5foptions_2eproto();
  return *internal_default_instance();
}

::google::protobuf::internal::ExplicitlyConstructed<OldOptionType> OldOptionType_default_instance_;

OldOptionType* OldOptionType::New(::google::protobuf::Arena* arena) const {
  OldOptionType* n = new OldOptionType;
  if (arena != NULL) {
    arena->Own(n);
  }
  return n;
}

void OldOptionType::Clear() {
// @@protoc_insertion_point(message_clear_start:protobuf_unittest.OldOptionType)
  value_ = 0;
  _has_bits_.Clear();
  if (_internal_metadata_.have_unknown_fields()) {
    mutable_unknown_fields()->Clear();
  }
}

bool OldOptionType::MergePartialFromCodedStream(
    ::google::protobuf::io::CodedInputStream* input) {
#define DO_(EXPRESSION) if (!GOOGLE_PREDICT_TRUE(EXPRESSION)) goto failure
  ::google::protobuf::uint32 tag;
  // @@protoc_insertion_point(parse_start:protobuf_unittest.OldOptionType)
  for (;;) {
    ::std::pair< ::google::protobuf::uint32, bool> p = input->ReadTagWithCutoff(127);
    tag = p.first;
    if (!p.second) goto handle_unusual;
    switch (::google::protobuf::internal::WireFormatLite::GetTagFieldNumber(tag)) {
      // required .protobuf_unittest.OldOptionType.TestEnum value = 1;
      case 1: {
        if (tag == 8) {
          int value;
          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   int, ::google::protobuf::internal::WireFormatLite::TYPE_ENUM>(
                 input, &value)));
          if (::protobuf_unittest::OldOptionType_TestEnum_IsValid(value)) {
            set_value(static_cast< ::protobuf_unittest::OldOptionType_TestEnum >(value));
          } else {
            mutable_unknown_fields()->AddVarint(1, value);
          }
        } else {
          goto handle_unusual;
        }
        if (input->ExpectAtEnd()) goto success;
        break;
      }

      default: {
      handle_unusual:
        if (tag == 0 ||
            ::google::protobuf::internal::WireFormatLite::GetTagWireType(tag) ==
            ::google::protobuf::internal::WireFormatLite::WIRETYPE_END_GROUP) {
          goto success;
        }
        DO_(::google::protobuf::internal::WireFormat::SkipField(
              input, tag, mutable_unknown_fields()));
        break;
      }
    }
  }
success:
  // @@protoc_insertion_point(parse_success:protobuf_unittest.OldOptionType)
  return true;
failure:
  // @@protoc_insertion_point(parse_failure:protobuf_unittest.OldOptionType)
  return false;
#undef DO_
}

void OldOptionType::SerializeWithCachedSizes(
    ::google::protobuf::io::CodedOutputStream* output) const {
  // @@protoc_insertion_point(serialize_start:protobuf_unittest.OldOptionType)
  // required .protobuf_unittest.OldOptionType.TestEnum value = 1;
  if (has_value()) {
    ::google::protobuf::internal::WireFormatLite::WriteEnum(
      1, this->value(), output);
  }

  if (_internal_metadata_.have_unknown_fields()) {
    ::google::protobuf::internal::WireFormat::SerializeUnknownFields(
        unknown_fields(), output);
  }
  // @@protoc_insertion_point(serialize_end:protobuf_unittest.OldOptionType)
}

::google::protobuf::uint8* OldOptionType::InternalSerializeWithCachedSizesToArray(
    bool deterministic, ::google::protobuf::uint8* target) const {
  (void)deterministic; // Unused
  // @@protoc_insertion_point(serialize_to_array_start:protobuf_unittest.OldOptionType)
  // required .protobuf_unittest.OldOptionType.TestEnum value = 1;
  if (has_value()) {
    target = ::google::protobuf::internal::WireFormatLite::WriteEnumToArray(
      1, this->value(), target);
  }

  if (_internal_metadata_.have_unknown_fields()) {
    target = ::google::protobuf::internal::WireFormat::SerializeUnknownFieldsToArray(
        unknown_fields(), target);
  }
  // @@protoc_insertion_point(serialize_to_array_end:protobuf_unittest.OldOptionType)
  return target;
}

size_t OldOptionType::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:protobuf_unittest.OldOptionType)
  size_t total_size = 0;

  // required .protobuf_unittest.OldOptionType.TestEnum value = 1;
  if (has_value()) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::EnumSize(this->value());
  }
  if (_internal_metadata_.have_unknown_fields()) {
    total_size +=
      ::google::protobuf::internal::WireFormat::ComputeUnknownFieldsSize(
        unknown_fields());
  }
  int cached_size = ::google::protobuf::internal::ToCachedSize(total_size);
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = cached_size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
  return total_size;
}

void OldOptionType::MergeFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_merge_from_start:protobuf_unittest.OldOptionType)
  if (GOOGLE_PREDICT_FALSE(&from == this)) MergeFromFail(__LINE__);
  const OldOptionType* source =
      ::google::protobuf::internal::DynamicCastToGenerated<const OldOptionType>(
          &from);
  if (source == NULL) {
  // @@protoc_insertion_point(generalized_merge_from_cast_fail:protobuf_unittest.OldOptionType)
    ::google::protobuf::internal::ReflectionOps::Merge(from, this);
  } else {
  // @@protoc_insertion_point(generalized_merge_from_cast_success:protobuf_unittest.OldOptionType)
    UnsafeMergeFrom(*source);
  }
}

void OldOptionType::MergeFrom(const OldOptionType& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:protobuf_unittest.OldOptionType)
  if (GOOGLE_PREDICT_TRUE(&from != this)) {
    UnsafeMergeFrom(from);
  } else {
    MergeFromFail(__LINE__);
  }
}

void OldOptionType::UnsafeMergeFrom(const OldOptionType& from) {
  GOOGLE_DCHECK(&from != this);
  if (from._has_bits_[0 / 32] & (0xffu << (0 % 32))) {
    if (from.has_value()) {
      set_value(from.value());
    }
  }
  if (from._internal_metadata_.have_unknown_fields()) {
    ::google::protobuf::UnknownFieldSet::MergeToInternalMetdata(
      from.unknown_fields(), &_internal_metadata_);
  }
}

void OldOptionType::CopyFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_copy_from_start:protobuf_unittest.OldOptionType)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

void OldOptionType::CopyFrom(const OldOptionType& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:protobuf_unittest.OldOptionType)
  if (&from == this) return;
  Clear();
  UnsafeMergeFrom(from);
}

bool OldOptionType::IsInitialized() const {
  if ((_has_bits_[0] & 0x00000001) != 0x00000001) return false;

  return true;
}

void OldOptionType::Swap(OldOptionType* other) {
  if (other == this) return;
  InternalSwap(other);
}
void OldOptionType::InternalSwap(OldOptionType* other) {
  std::swap(value_, other->value_);
  std::swap(_has_bits_[0], other->_has_bits_[0]);
  _internal_metadata_.Swap(&other->_internal_metadata_);
  std::swap(_cached_size_, other->_cached_size_);
}

::google::protobuf::Metadata OldOptionType::GetMetadata() const {
  protobuf_AssignDescriptorsOnce();
  ::google::protobuf::Metadata metadata;
  metadata.descriptor = OldOptionType_descriptor_;
  metadata.reflection = OldOptionType_reflection_;
  return metadata;
}

#if PROTOBUF_INLINE_NOT_IN_HEADERS
// OldOptionType

// required .protobuf_unittest.OldOptionType.TestEnum value = 1;
bool OldOptionType::has_value() const {
  return (_has_bits_[0] & 0x00000001u) != 0;
}
void OldOptionType::set_has_value() {
  _has_bits_[0] |= 0x00000001u;
}
void OldOptionType::clear_has_value() {
  _has_bits_[0] &= ~0x00000001u;
}
void OldOptionType::clear_value() {
  value_ = 0;
  clear_has_value();
}
::protobuf_unittest::OldOptionType_TestEnum OldOptionType::value() const {
  // @@protoc_insertion_point(field_get:protobuf_unittest.OldOptionType.value)
  return static_cast< ::protobuf_unittest::OldOptionType_TestEnum >(value_);
}
void OldOptionType::set_value(::protobuf_unittest::OldOptionType_TestEnum value) {
  assert(::protobuf_unittest::OldOptionType_TestEnum_IsValid(value));
  set_has_value();
  value_ = value;
  // @@protoc_insertion_point(field_set:protobuf_unittest.OldOptionType.value)
}

inline const OldOptionType* OldOptionType::internal_default_instance() {
  return &OldOptionType_default_instance_.get();
}
#endif  // PROTOBUF_INLINE_NOT_IN_HEADERS

// ===================================================================

const ::google::protobuf::EnumDescriptor* NewOptionType_TestEnum_descriptor() {
  protobuf_AssignDescriptorsOnce();
  return NewOptionType_TestEnum_descriptor_;
}
bool NewOptionType_TestEnum_IsValid(int value) {
  switch (value) {
    case 0:
    case 1:
      return true;
    default:
      return false;
  }
}

#if !defined(_MSC_VER) || _MSC_VER >= 1900
const NewOptionType_TestEnum NewOptionType::OLD_VALUE;
const NewOptionType_TestEnum NewOptionType::NEW_VALUE;
const NewOptionType_TestEnum NewOptionType::TestEnum_MIN;
const NewOptionType_TestEnum NewOptionType::TestEnum_MAX;
const int NewOptionType::TestEnum_ARRAYSIZE;
#endif  // !defined(_MSC_VER) || _MSC_VER >= 1900
#if !defined(_MSC_VER) || _MSC_VER >= 1900
const int NewOptionType::kValueFieldNumber;
#endif  // !defined(_MSC_VER) || _MSC_VER >= 1900

NewOptionType::NewOptionType()
  : ::google::protobuf::Message(), _internal_metadata_(NULL) {
  if (this != internal_default_instance()) protobuf_InitDefaults_google_2fprotobuf_2funittest_5fcustom_5foptions_2eproto();
  SharedCtor();
  // @@protoc_insertion_point(constructor:protobuf_unittest.NewOptionType)
}

void NewOptionType::InitAsDefaultInstance() {
}

NewOptionType::NewOptionType(const NewOptionType& from)
  : ::google::protobuf::Message(),
    _internal_metadata_(NULL) {
  SharedCtor();
  UnsafeMergeFrom(from);
  // @@protoc_insertion_point(copy_constructor:protobuf_unittest.NewOptionType)
}

void NewOptionType::SharedCtor() {
  _cached_size_ = 0;
  value_ = 0;
}

NewOptionType::~NewOptionType() {
  // @@protoc_insertion_point(destructor:protobuf_unittest.NewOptionType)
  SharedDtor();
}

void NewOptionType::SharedDtor() {
}

void NewOptionType::SetCachedSize(int size) const {
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
}
const ::google::protobuf::Descriptor* NewOptionType::descriptor() {
  protobuf_AssignDescriptorsOnce();
  return NewOptionType_descriptor_;
}

const NewOptionType& NewOptionType::default_instance() {
  protobuf_InitDefaults_google_2fprotobuf_2funittest_5fcustom_5foptions_2eproto();
  return *internal_default_instance();
}

::google::protobuf::internal::ExplicitlyConstructed<NewOptionType> NewOptionType_default_instance_;

NewOptionType* NewOptionType::New(::google::protobuf::Arena* arena) const {
  NewOptionType* n = new NewOptionType;
  if (arena != NULL) {
    arena->Own(n);
  }
  return n;
}

void NewOptionType::Clear() {
// @@protoc_insertion_point(message_clear_start:protobuf_unittest.NewOptionType)
  value_ = 0;
  _has_bits_.Clear();
  if (_internal_metadata_.have_unknown_fields()) {
    mutable_unknown_fields()->Clear();
  }
}

bool NewOptionType::MergePartialFromCodedStream(
    ::google::protobuf::io::CodedInputStream* input) {
#define DO_(EXPRESSION) if (!GOOGLE_PREDICT_TRUE(EXPRESSION)) goto failure
  ::google::protobuf::uint32 tag;
  // @@protoc_insertion_point(parse_start:protobuf_unittest.NewOptionType)
  for (;;) {
    ::std::pair< ::google::protobuf::uint32, bool> p = input->ReadTagWithCutoff(127);
    tag = p.first;
    if (!p.second) goto handle_unusual;
    switch (::google::protobuf::internal::WireFormatLite::GetTagFieldNumber(tag)) {
      // required .protobuf_unittest.NewOptionType.TestEnum value = 1;
      case 1: {
        if (tag == 8) {
          int value;
          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   int, ::google::protobuf::internal::WireFormatLite::TYPE_ENUM>(
                 input, &value)));
          if (::protobuf_unittest::NewOptionType_TestEnum_IsValid(value)) {
            set_value(static_cast< ::protobuf_unittest::NewOptionType_TestEnum >(value));
          } else {
            mutable_unknown_fields()->AddVarint(1, value);
          }
        } else {
          goto handle_unusual;
        }
        if (input->ExpectAtEnd()) goto success;
        break;
      }

      default: {
      handle_unusual:
        if (tag == 0 ||
            ::google::protobuf::internal::WireFormatLite::GetTagWireType(tag) ==
            ::google::protobuf::internal::WireFormatLite::WIRETYPE_END_GROUP) {
          goto success;
        }
        DO_(::google::protobuf::internal::WireFormat::SkipField(
              input, tag, mutable_unknown_fields()));
        break;
      }
    }
  }
success:
  // @@protoc_insertion_point(parse_success:protobuf_unittest.NewOptionType)
  return true;
failure:
  // @@protoc_insertion_point(parse_failure:protobuf_unittest.NewOptionType)
  return false;
#undef DO_
}

void NewOptionType::SerializeWithCachedSizes(
    ::google::protobuf::io::CodedOutputStream* output) const {
  // @@protoc_insertion_point(serialize_start:protobuf_unittest.NewOptionType)
  // required .protobuf_unittest.NewOptionType.TestEnum value = 1;
  if (has_value()) {
    ::google::protobuf::internal::WireFormatLite::WriteEnum(
      1, this->value(), output);
  }

  if (_internal_metadata_.have_unknown_fields()) {
    ::google::protobuf::internal::WireFormat::SerializeUnknownFields(
        unknown_fields(), output);
  }
  // @@protoc_insertion_point(serialize_end:protobuf_unittest.NewOptionType)
}

::google::protobuf::uint8* NewOptionType::InternalSerializeWithCachedSizesToArray(
    bool deterministic, ::google::protobuf::uint8* target) const {
  (void)deterministic; // Unused
  // @@protoc_insertion_point(serialize_to_array_start:protobuf_unittest.NewOptionType)
  // required .protobuf_unittest.NewOptionType.TestEnum value = 1;
  if (has_value()) {
    target = ::google::protobuf::internal::WireFormatLite::WriteEnumToArray(
      1, this->value(), target);
  }

  if (_internal_metadata_.have_unknown_fields()) {
    target = ::google::protobuf::internal::WireFormat::SerializeUnknownFieldsToArray(
        unknown_fields(), target);
  }
  // @@protoc_insertion_point(serialize_to_array_end:protobuf_unittest.NewOptionType)
  return target;
}

size_t NewOptionType::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:protobuf_unittest.NewOptionType)
  size_t total_size = 0;

  // required .protobuf_unittest.NewOptionType.TestEnum value = 1;
  if (has_value()) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::EnumSize(this->value());
  }
  if (_internal_metadata_.have_unknown_fields()) {
    total_size +=
      ::google::protobuf::internal::WireFormat::ComputeUnknownFieldsSize(
        unknown_fields());
  }
  int cached_size = ::google::protobuf::internal::ToCachedSize(total_size);
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = cached_size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
  return total_size;
}

void NewOptionType::MergeFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_merge_from_start:protobuf_unittest.NewOptionType)
  if (GOOGLE_PREDICT_FALSE(&from == this)) MergeFromFail(__LINE__);
  const NewOptionType* source =
      ::google::protobuf::internal::DynamicCastToGenerated<const NewOptionType>(
          &from);
  if (source == NULL) {
  // @@protoc_insertion_point(generalized_merge_from_cast_fail:protobuf_unittest.NewOptionType)
    ::google::protobuf::internal::ReflectionOps::Merge(from, this);
  } else {
  // @@protoc_insertion_point(generalized_merge_from_cast_success:protobuf_unittest.NewOptionType)
    UnsafeMergeFrom(*source);
  }
}

void NewOptionType::MergeFrom(const NewOptionType& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:protobuf_unittest.NewOptionType)
  if (GOOGLE_PREDICT_TRUE(&from != this)) {
    UnsafeMergeFrom(from);
  } else {
    MergeFromFail(__LINE__);
  }
}

void NewOptionType::UnsafeMergeFrom(const NewOptionType& from) {
  GOOGLE_DCHECK(&from != this);
  if (from._has_bits_[0 / 32] & (0xffu << (0 % 32))) {
    if (from.has_value()) {
      set_value(from.value());
    }
  }
  if (from._internal_metadata_.have_unknown_fields()) {
    ::google::protobuf::UnknownFieldSet::MergeToInternalMetdata(
      from.unknown_fields(), &_internal_metadata_);
  }
}

void NewOptionType::CopyFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_copy_from_start:protobuf_unittest.NewOptionType)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

void NewOptionType::CopyFrom(const NewOptionType& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:protobuf_unittest.NewOptionType)
  if (&from == this) return;
  Clear();
  UnsafeMergeFrom(from);
}

bool NewOptionType::IsInitialized() const {
  if ((_has_bits_[0] & 0x00000001) != 0x00000001) return false;

  return true;
}

void NewOptionType::Swap(NewOptionType* other) {
  if (other == this) return;
  InternalSwap(other);
}
void NewOptionType::InternalSwap(NewOptionType* other) {
  std::swap(value_, other->value_);
  std::swap(_has_bits_[0], other->_has_bits_[0]);
  _internal_metadata_.Swap(&other->_internal_metadata_);
  std::swap(_cached_size_, other->_cached_size_);
}

::google::protobuf::Metadata NewOptionType::GetMetadata() const {
  protobuf_AssignDescriptorsOnce();
  ::google::protobuf::Metadata metadata;
  metadata.descriptor = NewOptionType_descriptor_;
  metadata.reflection = NewOptionType_reflection_;
  return metadata;
}

#if PROTOBUF_INLINE_NOT_IN_HEADERS
// NewOptionType

// required .protobuf_unittest.NewOptionType.TestEnum value = 1;
bool NewOptionType::has_value() const {
  return (_has_bits_[0] & 0x00000001u) != 0;
}
void NewOptionType::set_has_value() {
  _has_bits_[0] |= 0x00000001u;
}
void NewOptionType::clear_has_value() {
  _has_bits_[0] &= ~0x00000001u;
}
void NewOptionType::clear_value() {
  value_ = 0;
  clear_has_value();
}
::protobuf_unittest::NewOptionType_TestEnum NewOptionType::value() const {
  // @@protoc_insertion_point(field_get:protobuf_unittest.NewOptionType.value)
  return static_cast< ::protobuf_unittest::NewOptionType_TestEnum >(value_);
}
void NewOptionType::set_value(::protobuf_unittest::NewOptionType_TestEnum value) {
  assert(::protobuf_unittest::NewOptionType_TestEnum_IsValid(value));
  set_has_value();
  value_ = value;
  // @@protoc_insertion_point(field_set:protobuf_unittest.NewOptionType.value)
}

inline const NewOptionType* NewOptionType::internal_default_instance() {
  return &NewOptionType_default_instance_.get();
}
#endif  // PROTOBUF_INLINE_NOT_IN_HEADERS

// ===================================================================

#if !defined(_MSC_VER) || _MSC_VER >= 1900
#endif  // !defined(_MSC_VER) || _MSC_VER >= 1900

TestMessageWithRequiredEnumOption::TestMessageWithRequiredEnumOption()
  : ::google::protobuf::Message(), _internal_metadata_(NULL) {
  if (this != internal_default_instance()) protobuf_InitDefaults_google_2fprotobuf_2funittest_5fcustom_5foptions_2eproto();
  SharedCtor();
  // @@protoc_insertion_point(constructor:protobuf_unittest.TestMessageWithRequiredEnumOption)
}

void TestMessageWithRequiredEnumOption::InitAsDefaultInstance() {
}

TestMessageWithRequiredEnumOption::TestMessageWithRequiredEnumOption(const TestMessageWithRequiredEnumOption& from)
  : ::google::protobuf::Message(),
    _internal_metadata_(NULL) {
  SharedCtor();
  UnsafeMergeFrom(from);
  // @@protoc_insertion_point(copy_constructor:protobuf_unittest.TestMessageWithRequiredEnumOption)
}

void TestMessageWithRequiredEnumOption::SharedCtor() {
  _cached_size_ = 0;
}

TestMessageWithRequiredEnumOption::~TestMessageWithRequiredEnumOption() {
  // @@protoc_insertion_point(destructor:protobuf_unittest.TestMessageWithRequiredEnumOption)
  SharedDtor();
}

void TestMessageWithRequiredEnumOption::SharedDtor() {
}

void TestMessageWithRequiredEnumOption::SetCachedSize(int size) const {
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
}
const ::google::protobuf::Descriptor* TestMessageWithRequiredEnumOption::descriptor() {
  protobuf_AssignDescriptorsOnce();
  return TestMessageWithRequiredEnumOption_descriptor_;
}

const TestMessageWithRequiredEnumOption& TestMessageWithRequiredEnumOption::default_instance() {
  protobuf_InitDefaults_google_2fprotobuf_2funittest_5fcustom_5foptions_2eproto();
  return *internal_default_instance();
}

::google::protobuf::internal::ExplicitlyConstructed<TestMessageWithRequiredEnumOption> TestMessageWithRequiredEnumOption_default_instance_;

TestMessageWithRequiredEnumOption* TestMessageWithRequiredEnumOption::New(::google::protobuf::Arena* arena) const {
  TestMessageWithRequiredEnumOption* n = new TestMessageWithRequiredEnumOption;
  if (arena != NULL) {
    arena->Own(n);
  }
  return n;
}

void TestMessageWithRequiredEnumOption::Clear() {
// @@protoc_insertion_point(message_clear_start:protobuf_unittest.TestMessageWithRequiredEnumOption)
  _has_bits_.Clear();
  if (_internal_metadata_.have_unknown_fields()) {
    mutable_unknown_fields()->Clear();
  }
}

bool TestMessageWithRequiredEnumOption::MergePartialFromCodedStream(
    ::google::protobuf::io::CodedInputStream* input) {
#define DO_(EXPRESSION) if (!GOOGLE_PREDICT_TRUE(EXPRESSION)) goto failure
  ::google::protobuf::uint32 tag;
  // @@protoc_insertion_point(parse_start:protobuf_unittest.TestMessageWithRequiredEnumOption)
  for (;;) {
    ::std::pair< ::google::protobuf::uint32, bool> p = input->ReadTagWithCutoff(127);
    tag = p.first;
    if (!p.second) goto handle_unusual;
  handle_unusual:
    if (tag == 0 ||
        ::google::protobuf::internal::WireFormatLite::GetTagWireType(tag) ==
        ::google::protobuf::internal::WireFormatLite::WIRETYPE_END_GROUP) {
      goto success;
    }
    DO_(::google::protobuf::internal::WireFormat::SkipField(
          input, tag, mutable_unknown_fields()));
  }
success:
  // @@protoc_insertion_point(parse_success:protobuf_unittest.TestMessageWithRequiredEnumOption)
  return true;
failure:
  // @@protoc_insertion_point(parse_failure:protobuf_unittest.TestMessageWithRequiredEnumOption)
  return false;
#undef DO_
}

void TestMessageWithRequiredEnumOption::SerializeWithCachedSizes(
    ::google::protobuf::io::CodedOutputStream* output) const {
  // @@protoc_insertion_point(serialize_start:protobuf_unittest.TestMessageWithRequiredEnumOption)
  if (_internal_metadata_.have_unknown_fields()) {
    ::google::protobuf::internal::WireFormat::SerializeUnknownFields(
        unknown_fields(), output);
  }
  // @@protoc_insertion_point(serialize_end:protobuf_unittest.TestMessageWithRequiredEnumOption)
}

::google::protobuf::uint8* TestMessageWithRequiredEnumOption::InternalSerializeWithCachedSizesToArray(
    bool deterministic, ::google::protobuf::uint8* target) const {
  (void)deterministic; // Unused
  // @@protoc_insertion_point(serialize_to_array_start:protobuf_unittest.TestMessageWithRequiredEnumOption)
  if (_internal_metadata_.have_unknown_fields()) {
    target = ::google::protobuf::internal::WireFormat::SerializeUnknownFieldsToArray(
        unknown_fields(), target);
  }
  // @@protoc_insertion_point(serialize_to_array_end:protobuf_unittest.TestMessageWithRequiredEnumOption)
  return target;
}

size_t TestMessageWithRequiredEnumOption::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:protobuf_unittest.TestMessageWithRequiredEnumOption)
  size_t total_size = 0;

  if (_internal_metadata_.have_unknown_fields()) {
    total_size +=
      ::google::protobuf::internal::WireFormat::ComputeUnknownFieldsSize(
        unknown_fields());
  }
  int cached_size = ::google::protobuf::internal::ToCachedSize(total_size);
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = cached_size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
  return total_size;
}

void TestMessageWithRequiredEnumOption::MergeFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_merge_from_start:protobuf_unittest.TestMessageWithRequiredEnumOption)
  if (GOOGLE_PREDICT_FALSE(&from == this)) MergeFromFail(__LINE__);
  const TestMessageWithRequiredEnumOption* source =
      ::google::protobuf::internal::DynamicCastToGenerated<const TestMessageWithRequiredEnumOption>(
          &from);
  if (source == NULL) {
  // @@protoc_insertion_point(generalized_merge_from_cast_fail:protobuf_unittest.TestMessageWithRequiredEnumOption)
    ::google::protobuf::internal::ReflectionOps::Merge(from, this);
  } else {
  // @@protoc_insertion_point(generalized_merge_from_cast_success:protobuf_unittest.TestMessageWithRequiredEnumOption)
    UnsafeMergeFrom(*source);
  }
}

void TestMessageWithRequiredEnumOption::MergeFrom(const TestMessageWithRequiredEnumOption& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:protobuf_unittest.TestMessageWithRequiredEnumOption)
  if (GOOGLE_PREDICT_TRUE(&from != this)) {
    UnsafeMergeFrom(from);
  } else {
    MergeFromFail(__LINE__);
  }
}

void TestMessageWithRequiredEnumOption::UnsafeMergeFrom(const TestMessageWithRequiredEnumOption& from) {
  GOOGLE_DCHECK(&from != this);
  if (from._internal_metadata_.have_unknown_fields()) {
    ::google::protobuf::UnknownFieldSet::MergeToInternalMetdata(
      from.unknown_fields(), &_internal_metadata_);
  }
}

void TestMessageWithRequiredEnumOption::CopyFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_copy_from_start:protobuf_unittest.TestMessageWithRequiredEnumOption)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

void TestMessageWithRequiredEnumOption::CopyFrom(const TestMessageWithRequiredEnumOption& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:protobuf_unittest.TestMessageWithRequiredEnumOption)
  if (&from == this) return;
  Clear();
  UnsafeMergeFrom(from);
}

bool TestMessageWithRequiredEnumOption::IsInitialized() const {

  return true;
}

void TestMessageWithRequiredEnumOption::Swap(TestMessageWithRequiredEnumOption* other) {
  if (other == this) return;
  InternalSwap(other);
}
void TestMessageWithRequiredEnumOption::InternalSwap(TestMessageWithRequiredEnumOption* other) {
  _internal_metadata_.Swap(&other->_internal_metadata_);
  std::swap(_cached_size_, other->_cached_size_);
}

::google::protobuf::Metadata TestMessageWithRequiredEnumOption::GetMetadata() const {
  protobuf_AssignDescriptorsOnce();
  ::google::protobuf::Metadata metadata;
  metadata.descriptor = TestMessageWithRequiredEnumOption_descriptor_;
  metadata.reflection = TestMessageWithRequiredEnumOption_reflection_;
  return metadata;
}

#if PROTOBUF_INLINE_NOT_IN_HEADERS
// TestMessageWithRequiredEnumOption

inline const TestMessageWithRequiredEnumOption* TestMessageWithRequiredEnumOption::internal_default_instance() {
  return &TestMessageWithRequiredEnumOption_default_instance_.get();
}
#endif  // PROTOBUF_INLINE_NOT_IN_HEADERS

// ===================================================================

TestServiceWithCustomOptions::~TestServiceWithCustomOptions() {}

const ::google::protobuf::ServiceDescriptor* TestServiceWithCustomOptions::descriptor() {
  protobuf_AssignDescriptorsOnce();
  return TestServiceWithCustomOptions_descriptor_;
}

const ::google::protobuf::ServiceDescriptor* TestServiceWithCustomOptions::GetDescriptor() {
  protobuf_AssignDescriptorsOnce();
  return TestServiceWithCustomOptions_descriptor_;
}

void TestServiceWithCustomOptions::Foo(::google::protobuf::RpcController* controller,
                         const ::protobuf_unittest::CustomOptionFooRequest*,
                         ::protobuf_unittest::CustomOptionFooResponse*,
                         ::google::protobuf::Closure* done) {
  controller->SetFailed("Method Foo() not implemented.");
  done->Run();
}

void TestServiceWithCustomOptions::CallMethod(const ::google::protobuf::MethodDescriptor* method,
                             ::google::protobuf::RpcController* controller,
                             const ::google::protobuf::Message* request,
                             ::google::protobuf::Message* response,
                             ::google::protobuf::Closure* done) {
  GOOGLE_DCHECK_EQ(method->service(), TestServiceWithCustomOptions_descriptor_);
  switch(method->index()) {
    case 0:
      Foo(controller,
             ::google::protobuf::down_cast<const ::protobuf_unittest::CustomOptionFooRequest*>(request),
             ::google::protobuf::down_cast< ::protobuf_unittest::CustomOptionFooResponse*>(response),
             done);
      break;
    default:
      GOOGLE_LOG(FATAL) << "Bad method index; this should never happen.";
      break;
  }
}

const ::google::protobuf::Message& TestServiceWithCustomOptions::GetRequestPrototype(
    const ::google::protobuf::MethodDescriptor* method) const {
  GOOGLE_DCHECK_EQ(method->service(), descriptor());
  switch(method->index()) {
    case 0:
      return ::protobuf_unittest::CustomOptionFooRequest::default_instance();
    default:
      GOOGLE_LOG(FATAL) << "Bad method index; this should never happen.";
      return *::google::protobuf::MessageFactory::generated_factory()
          ->GetPrototype(method->input_type());
  }
}

const ::google::protobuf::Message& TestServiceWithCustomOptions::GetResponsePrototype(
    const ::google::protobuf::MethodDescriptor* method) const {
  GOOGLE_DCHECK_EQ(method->service(), descriptor());
  switch(method->index()) {
    case 0:
      return ::protobuf_unittest::CustomOptionFooResponse::default_instance();
    default:
      GOOGLE_LOG(FATAL) << "Bad method index; this should never happen.";
      return *::google::protobuf::MessageFactory::generated_factory()
          ->GetPrototype(method->output_type());
  }
}

TestServiceWithCustomOptions_Stub::TestServiceWithCustomOptions_Stub(::google::protobuf::RpcChannel* channel)
  : channel_(channel), owns_channel_(false) {}
TestServiceWithCustomOptions_Stub::TestServiceWithCustomOptions_Stub(
    ::google::protobuf::RpcChannel* channel,
    ::google::protobuf::Service::ChannelOwnership ownership)
  : channel_(channel),
    owns_channel_(ownership == ::google::protobuf::Service::STUB_OWNS_CHANNEL) {}
TestServiceWithCustomOptions_Stub::~TestServiceWithCustomOptions_Stub() {
  if (owns_channel_) delete channel_;
}

void TestServiceWithCustomOptions_Stub::Foo(::google::protobuf::RpcController* controller,
                              const ::protobuf_unittest::CustomOptionFooRequest* request,
                              ::protobuf_unittest::CustomOptionFooResponse* response,
                              ::google::protobuf::Closure* done) {
  channel_->CallMethod(descriptor()->method(0),
                       controller, request, response, done);
}
// ===================================================================

AggregateService::~AggregateService() {}

const ::google::protobuf::ServiceDescriptor* AggregateService::descriptor() {
  protobuf_AssignDescriptorsOnce();
  return AggregateService_descriptor_;
}

const ::google::protobuf::ServiceDescriptor* AggregateService::GetDescriptor() {
  protobuf_AssignDescriptorsOnce();
  return AggregateService_descriptor_;
}

void AggregateService::Method(::google::protobuf::RpcController* controller,
                         const ::protobuf_unittest::AggregateMessage*,
                         ::protobuf_unittest::AggregateMessage*,
                         ::google::protobuf::Closure* done) {
  controller->SetFailed("Method Method() not implemented.");
  done->Run();
}

void AggregateService::CallMethod(const ::google::protobuf::MethodDescriptor* method,
                             ::google::protobuf::RpcController* controller,
                             const ::google::protobuf::Message* request,
                             ::google::protobuf::Message* response,
                             ::google::protobuf::Closure* done) {
  GOOGLE_DCHECK_EQ(method->service(), AggregateService_descriptor_);
  switch(method->index()) {
    case 0:
      Method(controller,
             ::google::protobuf::down_cast<const ::protobuf_unittest::AggregateMessage*>(request),
             ::google::protobuf::down_cast< ::protobuf_unittest::AggregateMessage*>(response),
             done);
      break;
    default:
      GOOGLE_LOG(FATAL) << "Bad method index; this should never happen.";
      break;
  }
}

const ::google::protobuf::Message& AggregateService::GetRequestPrototype(
    const ::google::protobuf::MethodDescriptor* method) const {
  GOOGLE_DCHECK_EQ(method->service(), descriptor());
  switch(method->index()) {
    case 0:
      return ::protobuf_unittest::AggregateMessage::default_instance();
    default:
      GOOGLE_LOG(FATAL) << "Bad method index; this should never happen.";
      return *::google::protobuf::MessageFactory::generated_factory()
          ->GetPrototype(method->input_type());
  }
}

const ::google::protobuf::Message& AggregateService::GetResponsePrototype(
    const ::google::protobuf::MethodDescriptor* method) const {
  GOOGLE_DCHECK_EQ(method->service(), descriptor());
  switch(method->index()) {
    case 0:
      return ::protobuf_unittest::AggregateMessage::default_instance();
    default:
      GOOGLE_LOG(FATAL) << "Bad method index; this should never happen.";
      return *::google::protobuf::MessageFactory::generated_factory()
          ->GetPrototype(method->output_type());
  }
}

AggregateService_Stub::AggregateService_Stub(::google::protobuf::RpcChannel* channel)
  : channel_(channel), owns_channel_(false) {}
AggregateService_Stub::AggregateService_Stub(
    ::google::protobuf::RpcChannel* channel,
    ::google::protobuf::Service::ChannelOwnership ownership)
  : channel_(channel),
    owns_channel_(ownership == ::google::protobuf::Service::STUB_OWNS_CHANNEL) {}
AggregateService_Stub::~AggregateService_Stub() {
  if (owns_channel_) delete channel_;
}

void AggregateService_Stub::Method(::google::protobuf::RpcController* controller,
                              const ::protobuf_unittest::AggregateMessage* request,
                              ::protobuf_unittest::AggregateMessage* response,
                              ::google::protobuf::Closure* done) {
  channel_->CallMethod(descriptor()->method(0),
                       controller, request, response, done);
}
::google::protobuf::internal::ExtensionIdentifier< ::google::protobuf::FileOptions,
    ::google::protobuf::internal::PrimitiveTypeTraits< ::google::protobuf::uint64 >, 4, false >
  file_opt1(kFileOpt1FieldNumber, GOOGLE_ULONGLONG(0));
::google::protobuf::internal::ExtensionIdentifier< ::google::protobuf::MessageOptions,
    ::google::protobuf::internal::PrimitiveTypeTraits< ::google::protobuf::int32 >, 5, false >
  message_opt1(kMessageOpt1FieldNumber, 0);
::google::protobuf::internal::ExtensionIdentifier< ::google::protobuf::FieldOptions,
    ::google::protobuf::internal::PrimitiveTypeTraits< ::google::protobuf::uint64 >, 6, false >
  field_opt1(kFieldOpt1FieldNumber, GOOGLE_ULONGLONG(0));
::google::protobuf::internal::ExtensionIdentifier< ::google::protobuf::FieldOptions,
    ::google::protobuf::internal::PrimitiveTypeTraits< ::google::protobuf::int32 >, 5, false >
  field_opt2(kFieldOpt2FieldNumber, 42);
::google::protobuf::internal::ExtensionIdentifier< ::google::protobuf::OneofOptions,
    ::google::protobuf::internal::PrimitiveTypeTraits< ::google::protobuf::int32 >, 5, false >
  oneof_opt1(kOneofOpt1FieldNumber, 0);
::google::protobuf::internal::ExtensionIdentifier< ::google::protobuf::EnumOptions,
    ::google::protobuf::internal::PrimitiveTypeTraits< ::google::protobuf::int32 >, 15, false >
  enum_opt1(kEnumOpt1FieldNumber, 0);
::google::protobuf::internal::ExtensionIdentifier< ::google::protobuf::EnumValueOptions,
    ::google::protobuf::internal::PrimitiveTypeTraits< ::google::protobuf::int32 >, 5, false >
  enum_value_opt1(kEnumValueOpt1FieldNumber, 0);
::google::protobuf::internal::ExtensionIdentifier< ::google::protobuf::ServiceOptions,
    ::google::protobuf::internal::PrimitiveTypeTraits< ::google::protobuf::int64 >, 18, false >
  service_opt1(kServiceOpt1FieldNumber, GOOGLE_LONGLONG(0));
::google::protobuf::internal::ExtensionIdentifier< ::google::protobuf::MethodOptions,
    ::google::protobuf::internal::EnumTypeTraits< ::protobuf_unittest::MethodOpt1, ::protobuf_unittest::MethodOpt1_IsValid>, 14, false >
  method_opt1(kMethodOpt1FieldNumber, static_cast< ::protobuf_unittest::MethodOpt1 >(1));
::google::protobuf::internal::ExtensionIdentifier< ::google::protobuf::MessageOptions,
    ::google::protobuf::internal::PrimitiveTypeTraits< bool >, 8, false >
  bool_opt(kBoolOptFieldNumber, false);
::google::protobuf::internal::ExtensionIdentifier< ::google::protobuf::MessageOptions,
    ::google::protobuf::internal::PrimitiveTypeTraits< ::google::protobuf::int32 >, 5, false >
  int32_opt(kInt32OptFieldNumber, 0);
::google::protobuf::internal::ExtensionIdentifier< ::google::protobuf::MessageOptions,
    ::google::protobuf::internal::PrimitiveTypeTraits< ::google::protobuf::int64 >, 3, false >
  int64_opt(kInt64OptFieldNumber, GOOGLE_LONGLONG(0));
::google::protobuf::internal::ExtensionIdentifier< ::google::protobuf::MessageOptions,
    ::google::protobuf::internal::PrimitiveTypeTraits< ::google::protobuf::uint32 >, 13, false >
  uint32_opt(kUint32OptFieldNumber, 0u);
::google::protobuf::internal::ExtensionIdentifier< ::google::protobuf::MessageOptions,
    ::google::protobuf::internal::PrimitiveTypeTraits< ::google::protobuf::uint64 >, 4, false >
  uint64_opt(kUint64OptFieldNumber, GOOGLE_ULONGLONG(0));
::google::protobuf::internal::ExtensionIdentifier< ::google::protobuf::MessageOptions,
    ::google::protobuf::internal::PrimitiveTypeTraits< ::google::protobuf::int32 >, 17, false >
  sint32_opt(kSint32OptFieldNumber, 0);
::google::protobuf::internal::ExtensionIdentifier< ::google::protobuf::MessageOptions,
    ::google::protobuf::internal::PrimitiveTypeTraits< ::google::protobuf::int64 >, 18, false >
  sint64_opt(kSint64OptFieldNumber, GOOGLE_LONGLONG(0));
::google::protobuf::internal::ExtensionIdentifier< ::google::protobuf::MessageOptions,
    ::google::protobuf::internal::PrimitiveTypeTraits< ::google::protobuf::uint32 >, 7, false >
  fixed32_opt(kFixed32OptFieldNumber, 0u);
::google::protobuf::internal::ExtensionIdentifier< ::google::protobuf::MessageOptions,
    ::google::protobuf::internal::PrimitiveTypeTraits< ::google::protobuf::uint64 >, 6, false >
  fixed64_opt(kFixed64OptFieldNumber, GOOGLE_ULONGLONG(0));
::google::protobuf::internal::ExtensionIdentifier< ::google::protobuf::MessageOptions,
    ::google::protobuf::internal::PrimitiveTypeTraits< ::google::protobuf::int32 >, 15, false >
  sfixed32_opt(kSfixed32OptFieldNumber, 0);
::google::protobuf::internal::ExtensionIdentifier< ::google::protobuf::MessageOptions,
    ::google::protobuf::internal::PrimitiveTypeTraits< ::google::protobuf::int64 >, 16, false >
  sfixed64_opt(kSfixed64OptFieldNumber, GOOGLE_LONGLONG(0));
::google::protobuf::internal::ExtensionIdentifier< ::google::protobuf::MessageOptions,
    ::google::protobuf::internal::PrimitiveTypeTraits< float >, 2, false >
  float_opt(kFloatOptFieldNumber, 0);
::google::protobuf::internal::ExtensionIdentifier< ::google::protobuf::MessageOptions,
    ::google::protobuf::internal::PrimitiveTypeTraits< double >, 1, false >
  double_opt(kDoubleOptFieldNumber, 0);
const ::std::string string_opt_default("");
::google::protobuf::internal::ExtensionIdentifier< ::google::protobuf::MessageOptions,
    ::google::protobuf::internal::StringTypeTraits, 9, false >
  string_opt(kStringOptFieldNumber, string_opt_default);
const ::std::string bytes_opt_default("");
::google::protobuf::internal::ExtensionIdentifier< ::google::protobuf::MessageOptions,
    ::google::protobuf::internal::StringTypeTraits, 12, false >
  bytes_opt(kBytesOptFieldNumber, bytes_opt_default);
::google::protobuf::internal::ExtensionIdentifier< ::google::protobuf::MessageOptions,
    ::google::protobuf::internal::EnumTypeTraits< ::protobuf_unittest::DummyMessageContainingEnum_TestEnumType, ::protobuf_unittest::DummyMessageContainingEnum_TestEnumType_IsValid>, 14, false >
  enum_opt(kEnumOptFieldNumber, static_cast< ::protobuf_unittest::DummyMessageContainingEnum_TestEnumType >(22));
::google::protobuf::internal::ExtensionIdentifier< ::google::protobuf::MessageOptions,
    ::google::protobuf::internal::MessageTypeTraits< ::protobuf_unittest::DummyMessageInvalidAsOptionType >, 11, false >
  message_type_opt(kMessageTypeOptFieldNumber, *::protobuf_unittest::DummyMessageInvalidAsOptionType::internal_default_instance());
::google::protobuf::internal::ExtensionIdentifier< ::protobuf_unittest::ComplexOptionType1,
    ::google::protobuf::internal::PrimitiveTypeTraits< ::google::protobuf::int32 >, 5, false >
  quux(kQuuxFieldNumber, 0);
::google::protobuf::internal::ExtensionIdentifier< ::protobuf_unittest::ComplexOptionType1,
    ::google::protobuf::internal::MessageTypeTraits< ::protobuf_unittest::ComplexOptionType3 >, 11, false >
  corge(kCorgeFieldNumber, *::protobuf_unittest::ComplexOptionType3::internal_default_instance());
::google::protobuf::internal::ExtensionIdentifier< ::protobuf_unittest::ComplexOptionType2,
    ::google::protobuf::internal::PrimitiveTypeTraits< ::google::protobuf::int32 >, 5, false >
  grault(kGraultFieldNumber, 0);
::google::protobuf::internal::ExtensionIdentifier< ::protobuf_unittest::ComplexOptionType2,
    ::google::protobuf::internal::MessageTypeTraits< ::protobuf_unittest::ComplexOptionType1 >, 11, false >
  garply(kGarplyFieldNumber, *::protobuf_unittest::ComplexOptionType1::internal_default_instance());
::google::protobuf::internal::ExtensionIdentifier< ::google::protobuf::MessageOptions,
    ::google::protobuf::internal::MessageTypeTraits< ::protobuf_unittest::ComplexOptionType1 >, 11, false >
  complex_opt1(kComplexOpt1FieldNumber, *::protobuf_unittest::ComplexOptionType1::internal_default_instance());
::google::protobuf::internal::ExtensionIdentifier< ::google::protobuf::MessageOptions,
    ::google::protobuf::internal::MessageTypeTraits< ::protobuf_unittest::ComplexOptionType2 >, 11, false >
  complex_opt2(kComplexOpt2FieldNumber, *::protobuf_unittest::ComplexOptionType2::internal_default_instance());
::google::protobuf::internal::ExtensionIdentifier< ::google::protobuf::MessageOptions,
    ::google::protobuf::internal::MessageTypeTraits< ::protobuf_unittest::ComplexOptionType3 >, 11, false >
  complex_opt3(kComplexOpt3FieldNumber, *::protobuf_unittest::ComplexOptionType3::internal_default_instance());
::google::protobuf::internal::ExtensionIdentifier< ::google::protobuf::MessageOptions,
    ::google::protobuf::internal::MessageTypeTraits< ::protobuf_unittest::ComplexOpt6 >, 10, false >
  complexopt6(kComplexopt6FieldNumber, *::protobuf_unittest::ComplexOpt6::internal_default_instance());
::google::protobuf::internal::ExtensionIdentifier< ::google::protobuf::FileOptions,
    ::google::protobuf::internal::MessageTypeTraits< ::protobuf_unittest::Aggregate >, 11, false >
  fileopt(kFileoptFieldNumber, *::protobuf_unittest::Aggregate::internal_default_instance());
::google::protobuf::internal::ExtensionIdentifier< ::google::protobuf::MessageOptions,
    ::google::protobuf::internal::MessageTypeTraits< ::protobuf_unittest::Aggregate >, 11, false >
  msgopt(kMsgoptFieldNumber, *::protobuf_unittest::Aggregate::internal_default_instance());
::google::protobuf::internal::ExtensionIdentifier< ::google::protobuf::FieldOptions,
    ::google::protobuf::internal::MessageTypeTraits< ::protobuf_unittest::Aggregate >, 11, false >
  fieldopt(kFieldoptFieldNumber, *::protobuf_unittest::Aggregate::internal_default_instance());
::google::protobuf::internal::ExtensionIdentifier< ::google::protobuf::EnumOptions,
    ::google::protobuf::internal::MessageTypeTraits< ::protobuf_unittest::Aggregate >, 11, false >
  enumopt(kEnumoptFieldNumber, *::protobuf_unittest::Aggregate::internal_default_instance());
::google::protobuf::internal::ExtensionIdentifier< ::google::protobuf::EnumValueOptions,
    ::google::protobuf::internal::MessageTypeTraits< ::protobuf_unittest::Aggregate >, 11, false >
  enumvalopt(kEnumvaloptFieldNumber, *::protobuf_unittest::Aggregate::internal_default_instance());
::google::protobuf::internal::ExtensionIdentifier< ::google::protobuf::ServiceOptions,
    ::google::protobuf::internal::MessageTypeTraits< ::protobuf_unittest::Aggregate >, 11, false >
  serviceopt(kServiceoptFieldNumber, *::protobuf_unittest::Aggregate::internal_default_instance());
::google::protobuf::internal::ExtensionIdentifier< ::google::protobuf::MethodOptions,
    ::google::protobuf::internal::MessageTypeTraits< ::protobuf_unittest::Aggregate >, 11, false >
  methodopt(kMethodoptFieldNumber, *::protobuf_unittest::Aggregate::internal_default_instance());
::google::protobuf::internal::ExtensionIdentifier< ::google::protobuf::MessageOptions,
    ::google::protobuf::internal::MessageTypeTraits< ::protobuf_unittest::OldOptionType >, 11, false >
  required_enum_opt(kRequiredEnumOptFieldNumber, *::protobuf_unittest::OldOptionType::internal_default_instance());

// @@protoc_insertion_point(namespace_scope)

}  // namespace protobuf_unittest

// @@protoc_insertion_point(global_scope)

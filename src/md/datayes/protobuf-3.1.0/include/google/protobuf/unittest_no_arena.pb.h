// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: google/protobuf/unittest_no_arena.proto

#ifndef PROTOBUF_google_2fprotobuf_2funittest_5fno_5farena_2eproto__INCLUDED
#define PROTOBUF_google_2fprotobuf_2funittest_5fno_5farena_2eproto__INCLUDED

#include <string>

#include <google/protobuf/stubs/common.h>

#if GOOGLE_PROTOBUF_VERSION < 3001000
#error This file was generated by a newer version of protoc which is
#error incompatible with your Protocol Buffer headers.  Please update
#error your headers.
#endif
#if 3001000 < GOOGLE_PROTOBUF_MIN_PROTOC_VERSION
#error This file was generated by an older version of protoc which is
#error incompatible with your Protocol Buffer headers.  Please
#error regenerate this file with a newer version of protoc.
#endif

#include <google/protobuf/arena.h>
#include <google/protobuf/arenastring.h>
#include <google/protobuf/generated_message_util.h>
#include <google/protobuf/metadata.h>
#include <google/protobuf/message.h>
#include <google/protobuf/repeated_field.h>
#include <google/protobuf/extension_set.h>
#include <google/protobuf/generated_enum_reflection.h>
#include <google/protobuf/unknown_field_set.h>
#include <google/protobuf/unittest_import.pb.h>
#include <google/protobuf/unittest_arena.pb.h>
// @@protoc_insertion_point(includes)

namespace protobuf_unittest_no_arena {

// Internal implementation detail -- do not call these.
void protobuf_AddDesc_google_2fprotobuf_2funittest_5fno_5farena_2eproto();
void protobuf_InitDefaults_google_2fprotobuf_2funittest_5fno_5farena_2eproto();
void protobuf_AssignDesc_google_2fprotobuf_2funittest_5fno_5farena_2eproto();
void protobuf_ShutdownFile_google_2fprotobuf_2funittest_5fno_5farena_2eproto();

class ForeignMessage;
class TestAllTypes;
class TestAllTypes_NestedMessage;
class TestAllTypes_OptionalGroup;
class TestAllTypes_RepeatedGroup;
class TestNoArenaMessage;

enum TestAllTypes_NestedEnum {
  TestAllTypes_NestedEnum_FOO = 1,
  TestAllTypes_NestedEnum_BAR = 2,
  TestAllTypes_NestedEnum_BAZ = 3,
  TestAllTypes_NestedEnum_NEG = -1
};
bool TestAllTypes_NestedEnum_IsValid(int value);
const TestAllTypes_NestedEnum TestAllTypes_NestedEnum_NestedEnum_MIN = TestAllTypes_NestedEnum_NEG;
const TestAllTypes_NestedEnum TestAllTypes_NestedEnum_NestedEnum_MAX = TestAllTypes_NestedEnum_BAZ;
const int TestAllTypes_NestedEnum_NestedEnum_ARRAYSIZE = TestAllTypes_NestedEnum_NestedEnum_MAX + 1;

const ::google::protobuf::EnumDescriptor* TestAllTypes_NestedEnum_descriptor();
inline const ::std::string& TestAllTypes_NestedEnum_Name(TestAllTypes_NestedEnum value) {
  return ::google::protobuf::internal::NameOfEnum(
    TestAllTypes_NestedEnum_descriptor(), value);
}
inline bool TestAllTypes_NestedEnum_Parse(
    const ::std::string& name, TestAllTypes_NestedEnum* value) {
  return ::google::protobuf::internal::ParseNamedEnum<TestAllTypes_NestedEnum>(
    TestAllTypes_NestedEnum_descriptor(), name, value);
}
enum ForeignEnum {
  FOREIGN_FOO = 4,
  FOREIGN_BAR = 5,
  FOREIGN_BAZ = 6
};
bool ForeignEnum_IsValid(int value);
const ForeignEnum ForeignEnum_MIN = FOREIGN_FOO;
const ForeignEnum ForeignEnum_MAX = FOREIGN_BAZ;
const int ForeignEnum_ARRAYSIZE = ForeignEnum_MAX + 1;

const ::google::protobuf::EnumDescriptor* ForeignEnum_descriptor();
inline const ::std::string& ForeignEnum_Name(ForeignEnum value) {
  return ::google::protobuf::internal::NameOfEnum(
    ForeignEnum_descriptor(), value);
}
inline bool ForeignEnum_Parse(
    const ::std::string& name, ForeignEnum* value) {
  return ::google::protobuf::internal::ParseNamedEnum<ForeignEnum>(
    ForeignEnum_descriptor(), name, value);
}
// ===================================================================

class TestAllTypes_NestedMessage : public ::google::protobuf::Message /* @@protoc_insertion_point(class_definition:protobuf_unittest_no_arena.TestAllTypes.NestedMessage) */ {
 public:
  TestAllTypes_NestedMessage();
  virtual ~TestAllTypes_NestedMessage();

  TestAllTypes_NestedMessage(const TestAllTypes_NestedMessage& from);

  inline TestAllTypes_NestedMessage& operator=(const TestAllTypes_NestedMessage& from) {
    CopyFrom(from);
    return *this;
  }

  inline const ::google::protobuf::UnknownFieldSet& unknown_fields() const {
    return _internal_metadata_.unknown_fields();
  }

  inline ::google::protobuf::UnknownFieldSet* mutable_unknown_fields() {
    return _internal_metadata_.mutable_unknown_fields();
  }

  static const ::google::protobuf::Descriptor* descriptor();
  static const TestAllTypes_NestedMessage& default_instance();

  static const TestAllTypes_NestedMessage* internal_default_instance();

  void Swap(TestAllTypes_NestedMessage* other);

  // implements Message ----------------------------------------------

  inline TestAllTypes_NestedMessage* New() const { return New(NULL); }

  TestAllTypes_NestedMessage* New(::google::protobuf::Arena* arena) const;
  void CopyFrom(const ::google::protobuf::Message& from);
  void MergeFrom(const ::google::protobuf::Message& from);
  void CopyFrom(const TestAllTypes_NestedMessage& from);
  void MergeFrom(const TestAllTypes_NestedMessage& from);
  void Clear();
  bool IsInitialized() const;

  size_t ByteSizeLong() const;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input);
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const;
  ::google::protobuf::uint8* InternalSerializeWithCachedSizesToArray(
      bool deterministic, ::google::protobuf::uint8* output) const;
  ::google::protobuf::uint8* SerializeWithCachedSizesToArray(::google::protobuf::uint8* output) const {
    return InternalSerializeWithCachedSizesToArray(false, output);
  }
  int GetCachedSize() const { return _cached_size_; }
  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const;
  void InternalSwap(TestAllTypes_NestedMessage* other);
  void UnsafeMergeFrom(const TestAllTypes_NestedMessage& from);
  private:
  inline ::google::protobuf::Arena* GetArenaNoVirtual() const {
    return _internal_metadata_.arena();
  }
  inline void* MaybeArenaPtr() const {
    return _internal_metadata_.raw_arena_ptr();
  }
  public:

  ::google::protobuf::Metadata GetMetadata() const;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // optional int32 bb = 1;
  bool has_bb() const;
  void clear_bb();
  static const int kBbFieldNumber = 1;
  ::google::protobuf::int32 bb() const;
  void set_bb(::google::protobuf::int32 value);

  // @@protoc_insertion_point(class_scope:protobuf_unittest_no_arena.TestAllTypes.NestedMessage)
 private:
  inline void set_has_bb();
  inline void clear_has_bb();

  ::google::protobuf::internal::InternalMetadataWithArena _internal_metadata_;
  ::google::protobuf::internal::HasBits<1> _has_bits_;
  mutable int _cached_size_;
  ::google::protobuf::int32 bb_;
  friend void  protobuf_InitDefaults_google_2fprotobuf_2funittest_5fno_5farena_2eproto_impl();
  friend void  protobuf_AddDesc_google_2fprotobuf_2funittest_5fno_5farena_2eproto_impl();
  friend void protobuf_AssignDesc_google_2fprotobuf_2funittest_5fno_5farena_2eproto();
  friend void protobuf_ShutdownFile_google_2fprotobuf_2funittest_5fno_5farena_2eproto();

  void InitAsDefaultInstance();
};
extern ::google::protobuf::internal::ExplicitlyConstructed<TestAllTypes_NestedMessage> TestAllTypes_NestedMessage_default_instance_;

// -------------------------------------------------------------------

class TestAllTypes_OptionalGroup : public ::google::protobuf::Message /* @@protoc_insertion_point(class_definition:protobuf_unittest_no_arena.TestAllTypes.OptionalGroup) */ {
 public:
  TestAllTypes_OptionalGroup();
  virtual ~TestAllTypes_OptionalGroup();

  TestAllTypes_OptionalGroup(const TestAllTypes_OptionalGroup& from);

  inline TestAllTypes_OptionalGroup& operator=(const TestAllTypes_OptionalGroup& from) {
    CopyFrom(from);
    return *this;
  }

  inline const ::google::protobuf::UnknownFieldSet& unknown_fields() const {
    return _internal_metadata_.unknown_fields();
  }

  inline ::google::protobuf::UnknownFieldSet* mutable_unknown_fields() {
    return _internal_metadata_.mutable_unknown_fields();
  }

  static const ::google::protobuf::Descriptor* descriptor();
  static const TestAllTypes_OptionalGroup& default_instance();

  static const TestAllTypes_OptionalGroup* internal_default_instance();

  void Swap(TestAllTypes_OptionalGroup* other);

  // implements Message ----------------------------------------------

  inline TestAllTypes_OptionalGroup* New() const { return New(NULL); }

  TestAllTypes_OptionalGroup* New(::google::protobuf::Arena* arena) const;
  void CopyFrom(const ::google::protobuf::Message& from);
  void MergeFrom(const ::google::protobuf::Message& from);
  void CopyFrom(const TestAllTypes_OptionalGroup& from);
  void MergeFrom(const TestAllTypes_OptionalGroup& from);
  void Clear();
  bool IsInitialized() const;

  size_t ByteSizeLong() const;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input);
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const;
  ::google::protobuf::uint8* InternalSerializeWithCachedSizesToArray(
      bool deterministic, ::google::protobuf::uint8* output) const;
  ::google::protobuf::uint8* SerializeWithCachedSizesToArray(::google::protobuf::uint8* output) const {
    return InternalSerializeWithCachedSizesToArray(false, output);
  }
  int GetCachedSize() const { return _cached_size_; }
  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const;
  void InternalSwap(TestAllTypes_OptionalGroup* other);
  void UnsafeMergeFrom(const TestAllTypes_OptionalGroup& from);
  private:
  inline ::google::protobuf::Arena* GetArenaNoVirtual() const {
    return _internal_metadata_.arena();
  }
  inline void* MaybeArenaPtr() const {
    return _internal_metadata_.raw_arena_ptr();
  }
  public:

  ::google::protobuf::Metadata GetMetadata() const;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // optional int32 a = 17;
  bool has_a() const;
  void clear_a();
  static const int kAFieldNumber = 17;
  ::google::protobuf::int32 a() const;
  void set_a(::google::protobuf::int32 value);

  // @@protoc_insertion_point(class_scope:protobuf_unittest_no_arena.TestAllTypes.OptionalGroup)
 private:
  inline void set_has_a();
  inline void clear_has_a();

  ::google::protobuf::internal::InternalMetadataWithArena _internal_metadata_;
  ::google::protobuf::internal::HasBits<1> _has_bits_;
  mutable int _cached_size_;
  ::google::protobuf::int32 a_;
  friend void  protobuf_InitDefaults_google_2fprotobuf_2funittest_5fno_5farena_2eproto_impl();
  friend void  protobuf_AddDesc_google_2fprotobuf_2funittest_5fno_5farena_2eproto_impl();
  friend void protobuf_AssignDesc_google_2fprotobuf_2funittest_5fno_5farena_2eproto();
  friend void protobuf_ShutdownFile_google_2fprotobuf_2funittest_5fno_5farena_2eproto();

  void InitAsDefaultInstance();
};
extern ::google::protobuf::internal::ExplicitlyConstructed<TestAllTypes_OptionalGroup> TestAllTypes_OptionalGroup_default_instance_;

// -------------------------------------------------------------------

class TestAllTypes_RepeatedGroup : public ::google::protobuf::Message /* @@protoc_insertion_point(class_definition:protobuf_unittest_no_arena.TestAllTypes.RepeatedGroup) */ {
 public:
  TestAllTypes_RepeatedGroup();
  virtual ~TestAllTypes_RepeatedGroup();

  TestAllTypes_RepeatedGroup(const TestAllTypes_RepeatedGroup& from);

  inline TestAllTypes_RepeatedGroup& operator=(const TestAllTypes_RepeatedGroup& from) {
    CopyFrom(from);
    return *this;
  }

  inline const ::google::protobuf::UnknownFieldSet& unknown_fields() const {
    return _internal_metadata_.unknown_fields();
  }

  inline ::google::protobuf::UnknownFieldSet* mutable_unknown_fields() {
    return _internal_metadata_.mutable_unknown_fields();
  }

  static const ::google::protobuf::Descriptor* descriptor();
  static const TestAllTypes_RepeatedGroup& default_instance();

  static const TestAllTypes_RepeatedGroup* internal_default_instance();

  void Swap(TestAllTypes_RepeatedGroup* other);

  // implements Message ----------------------------------------------

  inline TestAllTypes_RepeatedGroup* New() const { return New(NULL); }

  TestAllTypes_RepeatedGroup* New(::google::protobuf::Arena* arena) const;
  void CopyFrom(const ::google::protobuf::Message& from);
  void MergeFrom(const ::google::protobuf::Message& from);
  void CopyFrom(const TestAllTypes_RepeatedGroup& from);
  void MergeFrom(const TestAllTypes_RepeatedGroup& from);
  void Clear();
  bool IsInitialized() const;

  size_t ByteSizeLong() const;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input);
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const;
  ::google::protobuf::uint8* InternalSerializeWithCachedSizesToArray(
      bool deterministic, ::google::protobuf::uint8* output) const;
  ::google::protobuf::uint8* SerializeWithCachedSizesToArray(::google::protobuf::uint8* output) const {
    return InternalSerializeWithCachedSizesToArray(false, output);
  }
  int GetCachedSize() const { return _cached_size_; }
  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const;
  void InternalSwap(TestAllTypes_RepeatedGroup* other);
  void UnsafeMergeFrom(const TestAllTypes_RepeatedGroup& from);
  private:
  inline ::google::protobuf::Arena* GetArenaNoVirtual() const {
    return _internal_metadata_.arena();
  }
  inline void* MaybeArenaPtr() const {
    return _internal_metadata_.raw_arena_ptr();
  }
  public:

  ::google::protobuf::Metadata GetMetadata() const;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // optional int32 a = 47;
  bool has_a() const;
  void clear_a();
  static const int kAFieldNumber = 47;
  ::google::protobuf::int32 a() const;
  void set_a(::google::protobuf::int32 value);

  // @@protoc_insertion_point(class_scope:protobuf_unittest_no_arena.TestAllTypes.RepeatedGroup)
 private:
  inline void set_has_a();
  inline void clear_has_a();

  ::google::protobuf::internal::InternalMetadataWithArena _internal_metadata_;
  ::google::protobuf::internal::HasBits<1> _has_bits_;
  mutable int _cached_size_;
  ::google::protobuf::int32 a_;
  friend void  protobuf_InitDefaults_google_2fprotobuf_2funittest_5fno_5farena_2eproto_impl();
  friend void  protobuf_AddDesc_google_2fprotobuf_2funittest_5fno_5farena_2eproto_impl();
  friend void protobuf_AssignDesc_google_2fprotobuf_2funittest_5fno_5farena_2eproto();
  friend void protobuf_ShutdownFile_google_2fprotobuf_2funittest_5fno_5farena_2eproto();

  void InitAsDefaultInstance();
};
extern ::google::protobuf::internal::ExplicitlyConstructed<TestAllTypes_RepeatedGroup> TestAllTypes_RepeatedGroup_default_instance_;

// -------------------------------------------------------------------

class TestAllTypes : public ::google::protobuf::Message /* @@protoc_insertion_point(class_definition:protobuf_unittest_no_arena.TestAllTypes) */ {
 public:
  TestAllTypes();
  virtual ~TestAllTypes();

  TestAllTypes(const TestAllTypes& from);

  inline TestAllTypes& operator=(const TestAllTypes& from) {
    CopyFrom(from);
    return *this;
  }

  inline const ::google::protobuf::UnknownFieldSet& unknown_fields() const {
    return _internal_metadata_.unknown_fields();
  }

  inline ::google::protobuf::UnknownFieldSet* mutable_unknown_fields() {
    return _internal_metadata_.mutable_unknown_fields();
  }

  static const ::google::protobuf::Descriptor* descriptor();
  static const TestAllTypes& default_instance();

  enum OneofFieldCase {
    kOneofUint32 = 111,
    kOneofNestedMessage = 112,
    kOneofString = 113,
    kOneofBytes = 114,
    kLazyOneofNestedMessage = 115,
    ONEOF_FIELD_NOT_SET = 0,
  };

  static const TestAllTypes* internal_default_instance();

  void Swap(TestAllTypes* other);

  // implements Message ----------------------------------------------

  inline TestAllTypes* New() const { return New(NULL); }

  TestAllTypes* New(::google::protobuf::Arena* arena) const;
  void CopyFrom(const ::google::protobuf::Message& from);
  void MergeFrom(const ::google::protobuf::Message& from);
  void CopyFrom(const TestAllTypes& from);
  void MergeFrom(const TestAllTypes& from);
  void Clear();
  bool IsInitialized() const;

  size_t ByteSizeLong() const;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input);
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const;
  ::google::protobuf::uint8* InternalSerializeWithCachedSizesToArray(
      bool deterministic, ::google::protobuf::uint8* output) const;
  ::google::protobuf::uint8* SerializeWithCachedSizesToArray(::google::protobuf::uint8* output) const {
    return InternalSerializeWithCachedSizesToArray(false, output);
  }
  int GetCachedSize() const { return _cached_size_; }
  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const;
  void InternalSwap(TestAllTypes* other);
  void UnsafeMergeFrom(const TestAllTypes& from);
  private:
  inline ::google::protobuf::Arena* GetArenaNoVirtual() const {
    return _internal_metadata_.arena();
  }
  inline void* MaybeArenaPtr() const {
    return _internal_metadata_.raw_arena_ptr();
  }
  public:

  ::google::protobuf::Metadata GetMetadata() const;

  // nested types ----------------------------------------------------

  typedef TestAllTypes_NestedMessage NestedMessage;
  typedef TestAllTypes_OptionalGroup OptionalGroup;
  typedef TestAllTypes_RepeatedGroup RepeatedGroup;

  typedef TestAllTypes_NestedEnum NestedEnum;
  static const NestedEnum FOO =
    TestAllTypes_NestedEnum_FOO;
  static const NestedEnum BAR =
    TestAllTypes_NestedEnum_BAR;
  static const NestedEnum BAZ =
    TestAllTypes_NestedEnum_BAZ;
  static const NestedEnum NEG =
    TestAllTypes_NestedEnum_NEG;
  static inline bool NestedEnum_IsValid(int value) {
    return TestAllTypes_NestedEnum_IsValid(value);
  }
  static const NestedEnum NestedEnum_MIN =
    TestAllTypes_NestedEnum_NestedEnum_MIN;
  static const NestedEnum NestedEnum_MAX =
    TestAllTypes_NestedEnum_NestedEnum_MAX;
  static const int NestedEnum_ARRAYSIZE =
    TestAllTypes_NestedEnum_NestedEnum_ARRAYSIZE;
  static inline const ::google::protobuf::EnumDescriptor*
  NestedEnum_descriptor() {
    return TestAllTypes_NestedEnum_descriptor();
  }
  static inline const ::std::string& NestedEnum_Name(NestedEnum value) {
    return TestAllTypes_NestedEnum_Name(value);
  }
  static inline bool NestedEnum_Parse(const ::std::string& name,
      NestedEnum* value) {
    return TestAllTypes_NestedEnum_Parse(name, value);
  }

  // accessors -------------------------------------------------------

  // optional int32 optional_int32 = 1;
  bool has_optional_int32() const;
  void clear_optional_int32();
  static const int kOptionalInt32FieldNumber = 1;
  ::google::protobuf::int32 optional_int32() const;
  void set_optional_int32(::google::protobuf::int32 value);

  // optional int64 optional_int64 = 2;
  bool has_optional_int64() const;
  void clear_optional_int64();
  static const int kOptionalInt64FieldNumber = 2;
  ::google::protobuf::int64 optional_int64() const;
  void set_optional_int64(::google::protobuf::int64 value);

  // optional uint32 optional_uint32 = 3;
  bool has_optional_uint32() const;
  void clear_optional_uint32();
  static const int kOptionalUint32FieldNumber = 3;
  ::google::protobuf::uint32 optional_uint32() const;
  void set_optional_uint32(::google::protobuf::uint32 value);

  // optional uint64 optional_uint64 = 4;
  bool has_optional_uint64() const;
  void clear_optional_uint64();
  static const int kOptionalUint64FieldNumber = 4;
  ::google::protobuf::uint64 optional_uint64() const;
  void set_optional_uint64(::google::protobuf::uint64 value);

  // optional sint32 optional_sint32 = 5;
  bool has_optional_sint32() const;
  void clear_optional_sint32();
  static const int kOptionalSint32FieldNumber = 5;
  ::google::protobuf::int32 optional_sint32() const;
  void set_optional_sint32(::google::protobuf::int32 value);

  // optional sint64 optional_sint64 = 6;
  bool has_optional_sint64() const;
  void clear_optional_sint64();
  static const int kOptionalSint64FieldNumber = 6;
  ::google::protobuf::int64 optional_sint64() const;
  void set_optional_sint64(::google::protobuf::int64 value);

  // optional fixed32 optional_fixed32 = 7;
  bool has_optional_fixed32() const;
  void clear_optional_fixed32();
  static const int kOptionalFixed32FieldNumber = 7;
  ::google::protobuf::uint32 optional_fixed32() const;
  void set_optional_fixed32(::google::protobuf::uint32 value);

  // optional fixed64 optional_fixed64 = 8;
  bool has_optional_fixed64() const;
  void clear_optional_fixed64();
  static const int kOptionalFixed64FieldNumber = 8;
  ::google::protobuf::uint64 optional_fixed64() const;
  void set_optional_fixed64(::google::protobuf::uint64 value);

  // optional sfixed32 optional_sfixed32 = 9;
  bool has_optional_sfixed32() const;
  void clear_optional_sfixed32();
  static const int kOptionalSfixed32FieldNumber = 9;
  ::google::protobuf::int32 optional_sfixed32() const;
  void set_optional_sfixed32(::google::protobuf::int32 value);

  // optional sfixed64 optional_sfixed64 = 10;
  bool has_optional_sfixed64() const;
  void clear_optional_sfixed64();
  static const int kOptionalSfixed64FieldNumber = 10;
  ::google::protobuf::int64 optional_sfixed64() const;
  void set_optional_sfixed64(::google::protobuf::int64 value);

  // optional float optional_float = 11;
  bool has_optional_float() const;
  void clear_optional_float();
  static const int kOptionalFloatFieldNumber = 11;
  float optional_float() const;
  void set_optional_float(float value);

  // optional double optional_double = 12;
  bool has_optional_double() const;
  void clear_optional_double();
  static const int kOptionalDoubleFieldNumber = 12;
  double optional_double() const;
  void set_optional_double(double value);

  // optional bool optional_bool = 13;
  bool has_optional_bool() const;
  void clear_optional_bool();
  static const int kOptionalBoolFieldNumber = 13;
  bool optional_bool() const;
  void set_optional_bool(bool value);

  // optional string optional_string = 14;
  bool has_optional_string() const;
  void clear_optional_string();
  static const int kOptionalStringFieldNumber = 14;
  const ::std::string& optional_string() const;
  void set_optional_string(const ::std::string& value);
  void set_optional_string(const char* value);
  void set_optional_string(const char* value, size_t size);
  ::std::string* mutable_optional_string();
  ::std::string* release_optional_string();
  void set_allocated_optional_string(::std::string* optional_string);

  // optional bytes optional_bytes = 15;
  bool has_optional_bytes() const;
  void clear_optional_bytes();
  static const int kOptionalBytesFieldNumber = 15;
  const ::std::string& optional_bytes() const;
  void set_optional_bytes(const ::std::string& value);
  void set_optional_bytes(const char* value);
  void set_optional_bytes(const void* value, size_t size);
  ::std::string* mutable_optional_bytes();
  ::std::string* release_optional_bytes();
  void set_allocated_optional_bytes(::std::string* optional_bytes);

  // optional group OptionalGroup = 16 { ... };
  bool has_optionalgroup() const;
  void clear_optionalgroup();
  static const int kOptionalgroupFieldNumber = 16;
  const ::protobuf_unittest_no_arena::TestAllTypes_OptionalGroup& optionalgroup() const;
  ::protobuf_unittest_no_arena::TestAllTypes_OptionalGroup* mutable_optionalgroup();
  ::protobuf_unittest_no_arena::TestAllTypes_OptionalGroup* release_optionalgroup();
  void set_allocated_optionalgroup(::protobuf_unittest_no_arena::TestAllTypes_OptionalGroup* optionalgroup);

  // optional .protobuf_unittest_no_arena.TestAllTypes.NestedMessage optional_nested_message = 18;
  bool has_optional_nested_message() const;
  void clear_optional_nested_message();
  static const int kOptionalNestedMessageFieldNumber = 18;
  const ::protobuf_unittest_no_arena::TestAllTypes_NestedMessage& optional_nested_message() const;
  ::protobuf_unittest_no_arena::TestAllTypes_NestedMessage* mutable_optional_nested_message();
  ::protobuf_unittest_no_arena::TestAllTypes_NestedMessage* release_optional_nested_message();
  void set_allocated_optional_nested_message(::protobuf_unittest_no_arena::TestAllTypes_NestedMessage* optional_nested_message);

  // optional .protobuf_unittest_no_arena.ForeignMessage optional_foreign_message = 19;
  bool has_optional_foreign_message() const;
  void clear_optional_foreign_message();
  static const int kOptionalForeignMessageFieldNumber = 19;
  const ::protobuf_unittest_no_arena::ForeignMessage& optional_foreign_message() const;
  ::protobuf_unittest_no_arena::ForeignMessage* mutable_optional_foreign_message();
  ::protobuf_unittest_no_arena::ForeignMessage* release_optional_foreign_message();
  void set_allocated_optional_foreign_message(::protobuf_unittest_no_arena::ForeignMessage* optional_foreign_message);

  // optional .protobuf_unittest_import.ImportMessage optional_import_message = 20;
  bool has_optional_import_message() const;
  void clear_optional_import_message();
  static const int kOptionalImportMessageFieldNumber = 20;
  const ::protobuf_unittest_import::ImportMessage& optional_import_message() const;
  ::protobuf_unittest_import::ImportMessage* mutable_optional_import_message();
  ::protobuf_unittest_import::ImportMessage* release_optional_import_message();
  void set_allocated_optional_import_message(::protobuf_unittest_import::ImportMessage* optional_import_message);

  // optional .protobuf_unittest_no_arena.TestAllTypes.NestedEnum optional_nested_enum = 21;
  bool has_optional_nested_enum() const;
  void clear_optional_nested_enum();
  static const int kOptionalNestedEnumFieldNumber = 21;
  ::protobuf_unittest_no_arena::TestAllTypes_NestedEnum optional_nested_enum() const;
  void set_optional_nested_enum(::protobuf_unittest_no_arena::TestAllTypes_NestedEnum value);

  // optional .protobuf_unittest_no_arena.ForeignEnum optional_foreign_enum = 22;
  bool has_optional_foreign_enum() const;
  void clear_optional_foreign_enum();
  static const int kOptionalForeignEnumFieldNumber = 22;
  ::protobuf_unittest_no_arena::ForeignEnum optional_foreign_enum() const;
  void set_optional_foreign_enum(::protobuf_unittest_no_arena::ForeignEnum value);

  // optional .protobuf_unittest_import.ImportEnum optional_import_enum = 23;
  bool has_optional_import_enum() const;
  void clear_optional_import_enum();
  static const int kOptionalImportEnumFieldNumber = 23;
  ::protobuf_unittest_import::ImportEnum optional_import_enum() const;
  void set_optional_import_enum(::protobuf_unittest_import::ImportEnum value);

  // optional string optional_string_piece = 24 [ctype = STRING_PIECE];
  bool has_optional_string_piece() const;
  void clear_optional_string_piece();
  static const int kOptionalStringPieceFieldNumber = 24;
 private:
  // Hidden due to unknown ctype option.
  const ::std::string& optional_string_piece() const;
  void set_optional_string_piece(const ::std::string& value);
  void set_optional_string_piece(const char* value);
  void set_optional_string_piece(const char* value, size_t size);
  ::std::string* mutable_optional_string_piece();
  ::std::string* release_optional_string_piece();
  void set_allocated_optional_string_piece(::std::string* optional_string_piece);
 public:

  // optional string optional_cord = 25 [ctype = CORD];
  bool has_optional_cord() const;
  void clear_optional_cord();
  static const int kOptionalCordFieldNumber = 25;
 private:
  // Hidden due to unknown ctype option.
  const ::std::string& optional_cord() const;
  void set_optional_cord(const ::std::string& value);
  void set_optional_cord(const char* value);
  void set_optional_cord(const char* value, size_t size);
  ::std::string* mutable_optional_cord();
  ::std::string* release_optional_cord();
  void set_allocated_optional_cord(::std::string* optional_cord);
 public:

  // optional .protobuf_unittest_import.PublicImportMessage optional_public_import_message = 26;
  bool has_optional_public_import_message() const;
  void clear_optional_public_import_message();
  static const int kOptionalPublicImportMessageFieldNumber = 26;
  const ::protobuf_unittest_import::PublicImportMessage& optional_public_import_message() const;
  ::protobuf_unittest_import::PublicImportMessage* mutable_optional_public_import_message();
  ::protobuf_unittest_import::PublicImportMessage* release_optional_public_import_message();
  void set_allocated_optional_public_import_message(::protobuf_unittest_import::PublicImportMessage* optional_public_import_message);

  // optional .protobuf_unittest_no_arena.TestAllTypes.NestedMessage optional_message = 27 [lazy = true];
  bool has_optional_message() const;
  void clear_optional_message();
  static const int kOptionalMessageFieldNumber = 27;
  const ::protobuf_unittest_no_arena::TestAllTypes_NestedMessage& optional_message() const;
  ::protobuf_unittest_no_arena::TestAllTypes_NestedMessage* mutable_optional_message();
  ::protobuf_unittest_no_arena::TestAllTypes_NestedMessage* release_optional_message();
  void set_allocated_optional_message(::protobuf_unittest_no_arena::TestAllTypes_NestedMessage* optional_message);

  // repeated int32 repeated_int32 = 31;
  int repeated_int32_size() const;
  void clear_repeated_int32();
  static const int kRepeatedInt32FieldNumber = 31;
  ::google::protobuf::int32 repeated_int32(int index) const;
  void set_repeated_int32(int index, ::google::protobuf::int32 value);
  void add_repeated_int32(::google::protobuf::int32 value);
  const ::google::protobuf::RepeatedField< ::google::protobuf::int32 >&
      repeated_int32() const;
  ::google::protobuf::RepeatedField< ::google::protobuf::int32 >*
      mutable_repeated_int32();

  // repeated int64 repeated_int64 = 32;
  int repeated_int64_size() const;
  void clear_repeated_int64();
  static const int kRepeatedInt64FieldNumber = 32;
  ::google::protobuf::int64 repeated_int64(int index) const;
  void set_repeated_int64(int index, ::google::protobuf::int64 value);
  void add_repeated_int64(::google::protobuf::int64 value);
  const ::google::protobuf::RepeatedField< ::google::protobuf::int64 >&
      repeated_int64() const;
  ::google::protobuf::RepeatedField< ::google::protobuf::int64 >*
      mutable_repeated_int64();

  // repeated uint32 repeated_uint32 = 33;
  int repeated_uint32_size() const;
  void clear_repeated_uint32();
  static const int kRepeatedUint32FieldNumber = 33;
  ::google::protobuf::uint32 repeated_uint32(int index) const;
  void set_repeated_uint32(int index, ::google::protobuf::uint32 value);
  void add_repeated_uint32(::google::protobuf::uint32 value);
  const ::google::protobuf::RepeatedField< ::google::protobuf::uint32 >&
      repeated_uint32() const;
  ::google::protobuf::RepeatedField< ::google::protobuf::uint32 >*
      mutable_repeated_uint32();

  // repeated uint64 repeated_uint64 = 34;
  int repeated_uint64_size() const;
  void clear_repeated_uint64();
  static const int kRepeatedUint64FieldNumber = 34;
  ::google::protobuf::uint64 repeated_uint64(int index) const;
  void set_repeated_uint64(int index, ::google::protobuf::uint64 value);
  void add_repeated_uint64(::google::protobuf::uint64 value);
  const ::google::protobuf::RepeatedField< ::google::protobuf::uint64 >&
      repeated_uint64() const;
  ::google::protobuf::RepeatedField< ::google::protobuf::uint64 >*
      mutable_repeated_uint64();

  // repeated sint32 repeated_sint32 = 35;
  int repeated_sint32_size() const;
  void clear_repeated_sint32();
  static const int kRepeatedSint32FieldNumber = 35;
  ::google::protobuf::int32 repeated_sint32(int index) const;
  void set_repeated_sint32(int index, ::google::protobuf::int32 value);
  void add_repeated_sint32(::google::protobuf::int32 value);
  const ::google::protobuf::RepeatedField< ::google::protobuf::int32 >&
      repeated_sint32() const;
  ::google::protobuf::RepeatedField< ::google::protobuf::int32 >*
      mutable_repeated_sint32();

  // repeated sint64 repeated_sint64 = 36;
  int repeated_sint64_size() const;
  void clear_repeated_sint64();
  static const int kRepeatedSint64FieldNumber = 36;
  ::google::protobuf::int64 repeated_sint64(int index) const;
  void set_repeated_sint64(int index, ::google::protobuf::int64 value);
  void add_repeated_sint64(::google::protobuf::int64 value);
  const ::google::protobuf::RepeatedField< ::google::protobuf::int64 >&
      repeated_sint64() const;
  ::google::protobuf::RepeatedField< ::google::protobuf::int64 >*
      mutable_repeated_sint64();

  // repeated fixed32 repeated_fixed32 = 37;
  int repeated_fixed32_size() const;
  void clear_repeated_fixed32();
  static const int kRepeatedFixed32FieldNumber = 37;
  ::google::protobuf::uint32 repeated_fixed32(int index) const;
  void set_repeated_fixed32(int index, ::google::protobuf::uint32 value);
  void add_repeated_fixed32(::google::protobuf::uint32 value);
  const ::google::protobuf::RepeatedField< ::google::protobuf::uint32 >&
      repeated_fixed32() const;
  ::google::protobuf::RepeatedField< ::google::protobuf::uint32 >*
      mutable_repeated_fixed32();

  // repeated fixed64 repeated_fixed64 = 38;
  int repeated_fixed64_size() const;
  void clear_repeated_fixed64();
  static const int kRepeatedFixed64FieldNumber = 38;
  ::google::protobuf::uint64 repeated_fixed64(int index) const;
  void set_repeated_fixed64(int index, ::google::protobuf::uint64 value);
  void add_repeated_fixed64(::google::protobuf::uint64 value);
  const ::google::protobuf::RepeatedField< ::google::protobuf::uint64 >&
      repeated_fixed64() const;
  ::google::protobuf::RepeatedField< ::google::protobuf::uint64 >*
      mutable_repeated_fixed64();

  // repeated sfixed32 repeated_sfixed32 = 39;
  int repeated_sfixed32_size() const;
  void clear_repeated_sfixed32();
  static const int kRepeatedSfixed32FieldNumber = 39;
  ::google::protobuf::int32 repeated_sfixed32(int index) const;
  void set_repeated_sfixed32(int index, ::google::protobuf::int32 value);
  void add_repeated_sfixed32(::google::protobuf::int32 value);
  const ::google::protobuf::RepeatedField< ::google::protobuf::int32 >&
      repeated_sfixed32() const;
  ::google::protobuf::RepeatedField< ::google::protobuf::int32 >*
      mutable_repeated_sfixed32();

  // repeated sfixed64 repeated_sfixed64 = 40;
  int repeated_sfixed64_size() const;
  void clear_repeated_sfixed64();
  static const int kRepeatedSfixed64FieldNumber = 40;
  ::google::protobuf::int64 repeated_sfixed64(int index) const;
  void set_repeated_sfixed64(int index, ::google::protobuf::int64 value);
  void add_repeated_sfixed64(::google::protobuf::int64 value);
  const ::google::protobuf::RepeatedField< ::google::protobuf::int64 >&
      repeated_sfixed64() const;
  ::google::protobuf::RepeatedField< ::google::protobuf::int64 >*
      mutable_repeated_sfixed64();

  // repeated float repeated_float = 41;
  int repeated_float_size() const;
  void clear_repeated_float();
  static const int kRepeatedFloatFieldNumber = 41;
  float repeated_float(int index) const;
  void set_repeated_float(int index, float value);
  void add_repeated_float(float value);
  const ::google::protobuf::RepeatedField< float >&
      repeated_float() const;
  ::google::protobuf::RepeatedField< float >*
      mutable_repeated_float();

  // repeated double repeated_double = 42;
  int repeated_double_size() const;
  void clear_repeated_double();
  static const int kRepeatedDoubleFieldNumber = 42;
  double repeated_double(int index) const;
  void set_repeated_double(int index, double value);
  void add_repeated_double(double value);
  const ::google::protobuf::RepeatedField< double >&
      repeated_double() const;
  ::google::protobuf::RepeatedField< double >*
      mutable_repeated_double();

  // repeated bool repeated_bool = 43;
  int repeated_bool_size() const;
  void clear_repeated_bool();
  static const int kRepeatedBoolFieldNumber = 43;
  bool repeated_bool(int index) const;
  void set_repeated_bool(int index, bool value);
  void add_repeated_bool(bool value);
  const ::google::protobuf::RepeatedField< bool >&
      repeated_bool() const;
  ::google::protobuf::RepeatedField< bool >*
      mutable_repeated_bool();

  // repeated string repeated_string = 44;
  int repeated_string_size() const;
  void clear_repeated_string();
  static const int kRepeatedStringFieldNumber = 44;
  const ::std::string& repeated_string(int index) const;
  ::std::string* mutable_repeated_string(int index);
  void set_repeated_string(int index, const ::std::string& value);
  void set_repeated_string(int index, const char* value);
  void set_repeated_string(int index, const char* value, size_t size);
  ::std::string* add_repeated_string();
  void add_repeated_string(const ::std::string& value);
  void add_repeated_string(const char* value);
  void add_repeated_string(const char* value, size_t size);
  const ::google::protobuf::RepeatedPtrField< ::std::string>& repeated_string() const;
  ::google::protobuf::RepeatedPtrField< ::std::string>* mutable_repeated_string();

  // repeated bytes repeated_bytes = 45;
  int repeated_bytes_size() const;
  void clear_repeated_bytes();
  static const int kRepeatedBytesFieldNumber = 45;
  const ::std::string& repeated_bytes(int index) const;
  ::std::string* mutable_repeated_bytes(int index);
  void set_repeated_bytes(int index, const ::std::string& value);
  void set_repeated_bytes(int index, const char* value);
  void set_repeated_bytes(int index, const void* value, size_t size);
  ::std::string* add_repeated_bytes();
  void add_repeated_bytes(const ::std::string& value);
  void add_repeated_bytes(const char* value);
  void add_repeated_bytes(const void* value, size_t size);
  const ::google::protobuf::RepeatedPtrField< ::std::string>& repeated_bytes() const;
  ::google::protobuf::RepeatedPtrField< ::std::string>* mutable_repeated_bytes();

  // repeated group RepeatedGroup = 46 { ... };
  int repeatedgroup_size() const;
  void clear_repeatedgroup();
  static const int kRepeatedgroupFieldNumber = 46;
  const ::protobuf_unittest_no_arena::TestAllTypes_RepeatedGroup& repeatedgroup(int index) const;
  ::protobuf_unittest_no_arena::TestAllTypes_RepeatedGroup* mutable_repeatedgroup(int index);
  ::protobuf_unittest_no_arena::TestAllTypes_RepeatedGroup* add_repeatedgroup();
  ::google::protobuf::RepeatedPtrField< ::protobuf_unittest_no_arena::TestAllTypes_RepeatedGroup >*
      mutable_repeatedgroup();
  const ::google::protobuf::RepeatedPtrField< ::protobuf_unittest_no_arena::TestAllTypes_RepeatedGroup >&
      repeatedgroup() const;

  // repeated .protobuf_unittest_no_arena.TestAllTypes.NestedMessage repeated_nested_message = 48;
  int repeated_nested_message_size() const;
  void clear_repeated_nested_message();
  static const int kRepeatedNestedMessageFieldNumber = 48;
  const ::protobuf_unittest_no_arena::TestAllTypes_NestedMessage& repeated_nested_message(int index) const;
  ::protobuf_unittest_no_arena::TestAllTypes_NestedMessage* mutable_repeated_nested_message(int index);
  ::protobuf_unittest_no_arena::TestAllTypes_NestedMessage* add_repeated_nested_message();
  ::google::protobuf::RepeatedPtrField< ::protobuf_unittest_no_arena::TestAllTypes_NestedMessage >*
      mutable_repeated_nested_message();
  const ::google::protobuf::RepeatedPtrField< ::protobuf_unittest_no_arena::TestAllTypes_NestedMessage >&
      repeated_nested_message() const;

  // repeated .protobuf_unittest_no_arena.ForeignMessage repeated_foreign_message = 49;
  int repeated_foreign_message_size() const;
  void clear_repeated_foreign_message();
  static const int kRepeatedForeignMessageFieldNumber = 49;
  const ::protobuf_unittest_no_arena::ForeignMessage& repeated_foreign_message(int index) const;
  ::protobuf_unittest_no_arena::ForeignMessage* mutable_repeated_foreign_message(int index);
  ::protobuf_unittest_no_arena::ForeignMessage* add_repeated_foreign_message();
  ::google::protobuf::RepeatedPtrField< ::protobuf_unittest_no_arena::ForeignMessage >*
      mutable_repeated_foreign_message();
  const ::google::protobuf::RepeatedPtrField< ::protobuf_unittest_no_arena::ForeignMessage >&
      repeated_foreign_message() const;

  // repeated .protobuf_unittest_import.ImportMessage repeated_import_message = 50;
  int repeated_import_message_size() const;
  void clear_repeated_import_message();
  static const int kRepeatedImportMessageFieldNumber = 50;
  const ::protobuf_unittest_import::ImportMessage& repeated_import_message(int index) const;
  ::protobuf_unittest_import::ImportMessage* mutable_repeated_import_message(int index);
  ::protobuf_unittest_import::ImportMessage* add_repeated_import_message();
  ::google::protobuf::RepeatedPtrField< ::protobuf_unittest_import::ImportMessage >*
      mutable_repeated_import_message();
  const ::google::protobuf::RepeatedPtrField< ::protobuf_unittest_import::ImportMessage >&
      repeated_import_message() const;

  // repeated .protobuf_unittest_no_arena.TestAllTypes.NestedEnum repeated_nested_enum = 51;
  int repeated_nested_enum_size() const;
  void clear_repeated_nested_enum();
  static const int kRepeatedNestedEnumFieldNumber = 51;
  ::protobuf_unittest_no_arena::TestAllTypes_NestedEnum repeated_nested_enum(int index) const;
  void set_repeated_nested_enum(int index, ::protobuf_unittest_no_arena::TestAllTypes_NestedEnum value);
  void add_repeated_nested_enum(::protobuf_unittest_no_arena::TestAllTypes_NestedEnum value);
  const ::google::protobuf::RepeatedField<int>& repeated_nested_enum() const;
  ::google::protobuf::RepeatedField<int>* mutable_repeated_nested_enum();

  // repeated .protobuf_unittest_no_arena.ForeignEnum repeated_foreign_enum = 52;
  int repeated_foreign_enum_size() const;
  void clear_repeated_foreign_enum();
  static const int kRepeatedForeignEnumFieldNumber = 52;
  ::protobuf_unittest_no_arena::ForeignEnum repeated_foreign_enum(int index) const;
  void set_repeated_foreign_enum(int index, ::protobuf_unittest_no_arena::ForeignEnum value);
  void add_repeated_foreign_enum(::protobuf_unittest_no_arena::ForeignEnum value);
  const ::google::protobuf::RepeatedField<int>& repeated_foreign_enum() const;
  ::google::protobuf::RepeatedField<int>* mutable_repeated_foreign_enum();

  // repeated .protobuf_unittest_import.ImportEnum repeated_import_enum = 53;
  int repeated_import_enum_size() const;
  void clear_repeated_import_enum();
  static const int kRepeatedImportEnumFieldNumber = 53;
  ::protobuf_unittest_import::ImportEnum repeated_import_enum(int index) const;
  void set_repeated_import_enum(int index, ::protobuf_unittest_import::ImportEnum value);
  void add_repeated_import_enum(::protobuf_unittest_import::ImportEnum value);
  const ::google::protobuf::RepeatedField<int>& repeated_import_enum() const;
  ::google::protobuf::RepeatedField<int>* mutable_repeated_import_enum();

  // repeated string repeated_string_piece = 54 [ctype = STRING_PIECE];
  int repeated_string_piece_size() const;
  void clear_repeated_string_piece();
  static const int kRepeatedStringPieceFieldNumber = 54;
 private:
  // Hidden due to unknown ctype option.
  const ::std::string& repeated_string_piece(int index) const;
  ::std::string* mutable_repeated_string_piece(int index);
  void set_repeated_string_piece(int index, const ::std::string& value);
  void set_repeated_string_piece(int index, const char* value);
  void set_repeated_string_piece(int index, const char* value, size_t size);
  ::std::string* add_repeated_string_piece();
  void add_repeated_string_piece(const ::std::string& value);
  void add_repeated_string_piece(const char* value);
  void add_repeated_string_piece(const char* value, size_t size);
  const ::google::protobuf::RepeatedPtrField< ::std::string>& repeated_string_piece() const;
  ::google::protobuf::RepeatedPtrField< ::std::string>* mutable_repeated_string_piece();
 public:

  // repeated string repeated_cord = 55 [ctype = CORD];
  int repeated_cord_size() const;
  void clear_repeated_cord();
  static const int kRepeatedCordFieldNumber = 55;
 private:
  // Hidden due to unknown ctype option.
  const ::std::string& repeated_cord(int index) const;
  ::std::string* mutable_repeated_cord(int index);
  void set_repeated_cord(int index, const ::std::string& value);
  void set_repeated_cord(int index, const char* value);
  void set_repeated_cord(int index, const char* value, size_t size);
  ::std::string* add_repeated_cord();
  void add_repeated_cord(const ::std::string& value);
  void add_repeated_cord(const char* value);
  void add_repeated_cord(const char* value, size_t size);
  const ::google::protobuf::RepeatedPtrField< ::std::string>& repeated_cord() const;
  ::google::protobuf::RepeatedPtrField< ::std::string>* mutable_repeated_cord();
 public:

  // repeated .protobuf_unittest_no_arena.TestAllTypes.NestedMessage repeated_lazy_message = 57 [lazy = true];
  int repeated_lazy_message_size() const;
  void clear_repeated_lazy_message();
  static const int kRepeatedLazyMessageFieldNumber = 57;
  const ::protobuf_unittest_no_arena::TestAllTypes_NestedMessage& repeated_lazy_message(int index) const;
  ::protobuf_unittest_no_arena::TestAllTypes_NestedMessage* mutable_repeated_lazy_message(int index);
  ::protobuf_unittest_no_arena::TestAllTypes_NestedMessage* add_repeated_lazy_message();
  ::google::protobuf::RepeatedPtrField< ::protobuf_unittest_no_arena::TestAllTypes_NestedMessage >*
      mutable_repeated_lazy_message();
  const ::google::protobuf::RepeatedPtrField< ::protobuf_unittest_no_arena::TestAllTypes_NestedMessage >&
      repeated_lazy_message() const;

  // optional int32 default_int32 = 61 [default = 41];
  bool has_default_int32() const;
  void clear_default_int32();
  static const int kDefaultInt32FieldNumber = 61;
  ::google::protobuf::int32 default_int32() const;
  void set_default_int32(::google::protobuf::int32 value);

  // optional int64 default_int64 = 62 [default = 42];
  bool has_default_int64() const;
  void clear_default_int64();
  static const int kDefaultInt64FieldNumber = 62;
  ::google::protobuf::int64 default_int64() const;
  void set_default_int64(::google::protobuf::int64 value);

  // optional uint32 default_uint32 = 63 [default = 43];
  bool has_default_uint32() const;
  void clear_default_uint32();
  static const int kDefaultUint32FieldNumber = 63;
  ::google::protobuf::uint32 default_uint32() const;
  void set_default_uint32(::google::protobuf::uint32 value);

  // optional uint64 default_uint64 = 64 [default = 44];
  bool has_default_uint64() const;
  void clear_default_uint64();
  static const int kDefaultUint64FieldNumber = 64;
  ::google::protobuf::uint64 default_uint64() const;
  void set_default_uint64(::google::protobuf::uint64 value);

  // optional sint32 default_sint32 = 65 [default = -45];
  bool has_default_sint32() const;
  void clear_default_sint32();
  static const int kDefaultSint32FieldNumber = 65;
  ::google::protobuf::int32 default_sint32() const;
  void set_default_sint32(::google::protobuf::int32 value);

  // optional sint64 default_sint64 = 66 [default = 46];
  bool has_default_sint64() const;
  void clear_default_sint64();
  static const int kDefaultSint64FieldNumber = 66;
  ::google::protobuf::int64 default_sint64() const;
  void set_default_sint64(::google::protobuf::int64 value);

  // optional fixed32 default_fixed32 = 67 [default = 47];
  bool has_default_fixed32() const;
  void clear_default_fixed32();
  static const int kDefaultFixed32FieldNumber = 67;
  ::google::protobuf::uint32 default_fixed32() const;
  void set_default_fixed32(::google::protobuf::uint32 value);

  // optional fixed64 default_fixed64 = 68 [default = 48];
  bool has_default_fixed64() const;
  void clear_default_fixed64();
  static const int kDefaultFixed64FieldNumber = 68;
  ::google::protobuf::uint64 default_fixed64() const;
  void set_default_fixed64(::google::protobuf::uint64 value);

  // optional sfixed32 default_sfixed32 = 69 [default = 49];
  bool has_default_sfixed32() const;
  void clear_default_sfixed32();
  static const int kDefaultSfixed32FieldNumber = 69;
  ::google::protobuf::int32 default_sfixed32() const;
  void set_default_sfixed32(::google::protobuf::int32 value);

  // optional sfixed64 default_sfixed64 = 70 [default = -50];
  bool has_default_sfixed64() const;
  void clear_default_sfixed64();
  static const int kDefaultSfixed64FieldNumber = 70;
  ::google::protobuf::int64 default_sfixed64() const;
  void set_default_sfixed64(::google::protobuf::int64 value);

  // optional float default_float = 71 [default = 51.5];
  bool has_default_float() const;
  void clear_default_float();
  static const int kDefaultFloatFieldNumber = 71;
  float default_float() const;
  void set_default_float(float value);

  // optional double default_double = 72 [default = 52000];
  bool has_default_double() const;
  void clear_default_double();
  static const int kDefaultDoubleFieldNumber = 72;
  double default_double() const;
  void set_default_double(double value);

  // optional bool default_bool = 73 [default = true];
  bool has_default_bool() const;
  void clear_default_bool();
  static const int kDefaultBoolFieldNumber = 73;
  bool default_bool() const;
  void set_default_bool(bool value);

  // optional string default_string = 74 [default = "hello"];
  bool has_default_string() const;
  void clear_default_string();
  static const int kDefaultStringFieldNumber = 74;
  const ::std::string& default_string() const;
  void set_default_string(const ::std::string& value);
  void set_default_string(const char* value);
  void set_default_string(const char* value, size_t size);
  ::std::string* mutable_default_string();
  ::std::string* release_default_string();
  void set_allocated_default_string(::std::string* default_string);

  // optional bytes default_bytes = 75 [default = "world"];
  bool has_default_bytes() const;
  void clear_default_bytes();
  static const int kDefaultBytesFieldNumber = 75;
  const ::std::string& default_bytes() const;
  void set_default_bytes(const ::std::string& value);
  void set_default_bytes(const char* value);
  void set_default_bytes(const void* value, size_t size);
  ::std::string* mutable_default_bytes();
  ::std::string* release_default_bytes();
  void set_allocated_default_bytes(::std::string* default_bytes);

  // optional .protobuf_unittest_no_arena.TestAllTypes.NestedEnum default_nested_enum = 81 [default = BAR];
  bool has_default_nested_enum() const;
  void clear_default_nested_enum();
  static const int kDefaultNestedEnumFieldNumber = 81;
  ::protobuf_unittest_no_arena::TestAllTypes_NestedEnum default_nested_enum() const;
  void set_default_nested_enum(::protobuf_unittest_no_arena::TestAllTypes_NestedEnum value);

  // optional .protobuf_unittest_no_arena.ForeignEnum default_foreign_enum = 82 [default = FOREIGN_BAR];
  bool has_default_foreign_enum() const;
  void clear_default_foreign_enum();
  static const int kDefaultForeignEnumFieldNumber = 82;
  ::protobuf_unittest_no_arena::ForeignEnum default_foreign_enum() const;
  void set_default_foreign_enum(::protobuf_unittest_no_arena::ForeignEnum value);

  // optional .protobuf_unittest_import.ImportEnum default_import_enum = 83 [default = IMPORT_BAR];
  bool has_default_import_enum() const;
  void clear_default_import_enum();
  static const int kDefaultImportEnumFieldNumber = 83;
  ::protobuf_unittest_import::ImportEnum default_import_enum() const;
  void set_default_import_enum(::protobuf_unittest_import::ImportEnum value);

  // optional string default_string_piece = 84 [default = "abc", ctype = STRING_PIECE];
  bool has_default_string_piece() const;
  void clear_default_string_piece();
  static const int kDefaultStringPieceFieldNumber = 84;
 private:
  // Hidden due to unknown ctype option.
  const ::std::string& default_string_piece() const;
  void set_default_string_piece(const ::std::string& value);
  void set_default_string_piece(const char* value);
  void set_default_string_piece(const char* value, size_t size);
  ::std::string* mutable_default_string_piece();
  ::std::string* release_default_string_piece();
  void set_allocated_default_string_piece(::std::string* default_string_piece);
 public:

  // optional string default_cord = 85 [default = "123", ctype = CORD];
  bool has_default_cord() const;
  void clear_default_cord();
  static const int kDefaultCordFieldNumber = 85;
 private:
  // Hidden due to unknown ctype option.
  const ::std::string& default_cord() const;
  void set_default_cord(const ::std::string& value);
  void set_default_cord(const char* value);
  void set_default_cord(const char* value, size_t size);
  ::std::string* mutable_default_cord();
  ::std::string* release_default_cord();
  void set_allocated_default_cord(::std::string* default_cord);
 public:

  // optional uint32 oneof_uint32 = 111;
  bool has_oneof_uint32() const;
  void clear_oneof_uint32();
  static const int kOneofUint32FieldNumber = 111;
  ::google::protobuf::uint32 oneof_uint32() const;
  void set_oneof_uint32(::google::protobuf::uint32 value);

  // optional .protobuf_unittest_no_arena.TestAllTypes.NestedMessage oneof_nested_message = 112;
  bool has_oneof_nested_message() const;
  void clear_oneof_nested_message();
  static const int kOneofNestedMessageFieldNumber = 112;
  const ::protobuf_unittest_no_arena::TestAllTypes_NestedMessage& oneof_nested_message() const;
  ::protobuf_unittest_no_arena::TestAllTypes_NestedMessage* mutable_oneof_nested_message();
  ::protobuf_unittest_no_arena::TestAllTypes_NestedMessage* release_oneof_nested_message();
  void set_allocated_oneof_nested_message(::protobuf_unittest_no_arena::TestAllTypes_NestedMessage* oneof_nested_message);

  // optional string oneof_string = 113;
  bool has_oneof_string() const;
  void clear_oneof_string();
  static const int kOneofStringFieldNumber = 113;
  const ::std::string& oneof_string() const;
  void set_oneof_string(const ::std::string& value);
  void set_oneof_string(const char* value);
  void set_oneof_string(const char* value, size_t size);
  ::std::string* mutable_oneof_string();
  ::std::string* release_oneof_string();
  void set_allocated_oneof_string(::std::string* oneof_string);

  // optional bytes oneof_bytes = 114;
  bool has_oneof_bytes() const;
  void clear_oneof_bytes();
  static const int kOneofBytesFieldNumber = 114;
  const ::std::string& oneof_bytes() const;
  void set_oneof_bytes(const ::std::string& value);
  void set_oneof_bytes(const char* value);
  void set_oneof_bytes(const void* value, size_t size);
  ::std::string* mutable_oneof_bytes();
  ::std::string* release_oneof_bytes();
  void set_allocated_oneof_bytes(::std::string* oneof_bytes);

  // optional .protobuf_unittest_no_arena.TestAllTypes.NestedMessage lazy_oneof_nested_message = 115 [lazy = true];
  bool has_lazy_oneof_nested_message() const;
  void clear_lazy_oneof_nested_message();
  static const int kLazyOneofNestedMessageFieldNumber = 115;
  const ::protobuf_unittest_no_arena::TestAllTypes_NestedMessage& lazy_oneof_nested_message() const;
  ::protobuf_unittest_no_arena::TestAllTypes_NestedMessage* mutable_lazy_oneof_nested_message();
  ::protobuf_unittest_no_arena::TestAllTypes_NestedMessage* release_lazy_oneof_nested_message();
  void set_allocated_lazy_oneof_nested_message(::protobuf_unittest_no_arena::TestAllTypes_NestedMessage* lazy_oneof_nested_message);

  OneofFieldCase oneof_field_case() const;
  // @@protoc_insertion_point(class_scope:protobuf_unittest_no_arena.TestAllTypes)
 private:
  inline void set_has_optional_int32();
  inline void clear_has_optional_int32();
  inline void set_has_optional_int64();
  inline void clear_has_optional_int64();
  inline void set_has_optional_uint32();
  inline void clear_has_optional_uint32();
  inline void set_has_optional_uint64();
  inline void clear_has_optional_uint64();
  inline void set_has_optional_sint32();
  inline void clear_has_optional_sint32();
  inline void set_has_optional_sint64();
  inline void clear_has_optional_sint64();
  inline void set_has_optional_fixed32();
  inline void clear_has_optional_fixed32();
  inline void set_has_optional_fixed64();
  inline void clear_has_optional_fixed64();
  inline void set_has_optional_sfixed32();
  inline void clear_has_optional_sfixed32();
  inline void set_has_optional_sfixed64();
  inline void clear_has_optional_sfixed64();
  inline void set_has_optional_float();
  inline void clear_has_optional_float();
  inline void set_has_optional_double();
  inline void clear_has_optional_double();
  inline void set_has_optional_bool();
  inline void clear_has_optional_bool();
  inline void set_has_optional_string();
  inline void clear_has_optional_string();
  inline void set_has_optional_bytes();
  inline void clear_has_optional_bytes();
  inline void set_has_optionalgroup();
  inline void clear_has_optionalgroup();
  inline void set_has_optional_nested_message();
  inline void clear_has_optional_nested_message();
  inline void set_has_optional_foreign_message();
  inline void clear_has_optional_foreign_message();
  inline void set_has_optional_import_message();
  inline void clear_has_optional_import_message();
  inline void set_has_optional_nested_enum();
  inline void clear_has_optional_nested_enum();
  inline void set_has_optional_foreign_enum();
  inline void clear_has_optional_foreign_enum();
  inline void set_has_optional_import_enum();
  inline void clear_has_optional_import_enum();
  inline void set_has_optional_string_piece();
  inline void clear_has_optional_string_piece();
  inline void set_has_optional_cord();
  inline void clear_has_optional_cord();
  inline void set_has_optional_public_import_message();
  inline void clear_has_optional_public_import_message();
  inline void set_has_optional_message();
  inline void clear_has_optional_message();
  inline void set_has_default_int32();
  inline void clear_has_default_int32();
  inline void set_has_default_int64();
  inline void clear_has_default_int64();
  inline void set_has_default_uint32();
  inline void clear_has_default_uint32();
  inline void set_has_default_uint64();
  inline void clear_has_default_uint64();
  inline void set_has_default_sint32();
  inline void clear_has_default_sint32();
  inline void set_has_default_sint64();
  inline void clear_has_default_sint64();
  inline void set_has_default_fixed32();
  inline void clear_has_default_fixed32();
  inline void set_has_default_fixed64();
  inline void clear_has_default_fixed64();
  inline void set_has_default_sfixed32();
  inline void clear_has_default_sfixed32();
  inline void set_has_default_sfixed64();
  inline void clear_has_default_sfixed64();
  inline void set_has_default_float();
  inline void clear_has_default_float();
  inline void set_has_default_double();
  inline void clear_has_default_double();
  inline void set_has_default_bool();
  inline void clear_has_default_bool();
  inline void set_has_default_string();
  inline void clear_has_default_string();
  inline void set_has_default_bytes();
  inline void clear_has_default_bytes();
  inline void set_has_default_nested_enum();
  inline void clear_has_default_nested_enum();
  inline void set_has_default_foreign_enum();
  inline void clear_has_default_foreign_enum();
  inline void set_has_default_import_enum();
  inline void clear_has_default_import_enum();
  inline void set_has_default_string_piece();
  inline void clear_has_default_string_piece();
  inline void set_has_default_cord();
  inline void clear_has_default_cord();
  inline void set_has_oneof_uint32();
  inline void set_has_oneof_nested_message();
  inline void set_has_oneof_string();
  inline void set_has_oneof_bytes();
  inline void set_has_lazy_oneof_nested_message();

  inline bool has_oneof_field() const;
  void clear_oneof_field();
  inline void clear_has_oneof_field();

  ::google::protobuf::internal::InternalMetadataWithArena _internal_metadata_;
  ::google::protobuf::internal::HasBits<3> _has_bits_;
  mutable int _cached_size_;
  ::google::protobuf::RepeatedField< ::google::protobuf::int32 > repeated_int32_;
  ::google::protobuf::RepeatedField< ::google::protobuf::int64 > repeated_int64_;
  ::google::protobuf::RepeatedField< ::google::protobuf::uint32 > repeated_uint32_;
  ::google::protobuf::RepeatedField< ::google::protobuf::uint64 > repeated_uint64_;
  ::google::protobuf::RepeatedField< ::google::protobuf::int32 > repeated_sint32_;
  ::google::protobuf::RepeatedField< ::google::protobuf::int64 > repeated_sint64_;
  ::google::protobuf::RepeatedField< ::google::protobuf::uint32 > repeated_fixed32_;
  ::google::protobuf::RepeatedField< ::google::protobuf::uint64 > repeated_fixed64_;
  ::google::protobuf::RepeatedField< ::google::protobuf::int32 > repeated_sfixed32_;
  ::google::protobuf::RepeatedField< ::google::protobuf::int64 > repeated_sfixed64_;
  ::google::protobuf::RepeatedField< float > repeated_float_;
  ::google::protobuf::RepeatedField< double > repeated_double_;
  ::google::protobuf::RepeatedField< bool > repeated_bool_;
  ::google::protobuf::RepeatedPtrField< ::std::string> repeated_string_;
  ::google::protobuf::RepeatedPtrField< ::std::string> repeated_bytes_;
  ::google::protobuf::RepeatedPtrField< ::protobuf_unittest_no_arena::TestAllTypes_RepeatedGroup > repeatedgroup_;
  ::google::protobuf::RepeatedPtrField< ::protobuf_unittest_no_arena::TestAllTypes_NestedMessage > repeated_nested_message_;
  ::google::protobuf::RepeatedPtrField< ::protobuf_unittest_no_arena::ForeignMessage > repeated_foreign_message_;
  ::google::protobuf::RepeatedPtrField< ::protobuf_unittest_import::ImportMessage > repeated_import_message_;
  ::google::protobuf::RepeatedField<int> repeated_nested_enum_;
  ::google::protobuf::RepeatedField<int> repeated_foreign_enum_;
  ::google::protobuf::RepeatedField<int> repeated_import_enum_;
  ::google::protobuf::RepeatedPtrField< ::std::string> repeated_string_piece_;
  ::google::protobuf::RepeatedPtrField< ::std::string> repeated_cord_;
  ::google::protobuf::RepeatedPtrField< ::protobuf_unittest_no_arena::TestAllTypes_NestedMessage > repeated_lazy_message_;
  ::google::protobuf::internal::ArenaStringPtr optional_string_;
  ::google::protobuf::internal::ArenaStringPtr optional_bytes_;
  ::google::protobuf::internal::ArenaStringPtr optional_string_piece_;
  ::google::protobuf::internal::ArenaStringPtr optional_cord_;
  static ::std::string* _default_default_string_;
  ::google::protobuf::internal::ArenaStringPtr default_string_;
  static ::std::string* _default_default_bytes_;
  ::google::protobuf::internal::ArenaStringPtr default_bytes_;
  static ::std::string* _default_default_string_piece_;
  ::google::protobuf::internal::ArenaStringPtr default_string_piece_;
  static ::std::string* _default_default_cord_;
  ::google::protobuf::internal::ArenaStringPtr default_cord_;
  ::protobuf_unittest_no_arena::TestAllTypes_OptionalGroup* optionalgroup_;
  ::protobuf_unittest_no_arena::TestAllTypes_NestedMessage* optional_nested_message_;
  ::protobuf_unittest_no_arena::ForeignMessage* optional_foreign_message_;
  ::protobuf_unittest_import::ImportMessage* optional_import_message_;
  ::protobuf_unittest_import::PublicImportMessage* optional_public_import_message_;
  ::protobuf_unittest_no_arena::TestAllTypes_NestedMessage* optional_message_;
  ::google::protobuf::int64 optional_int64_;
  ::google::protobuf::int32 optional_int32_;
  ::google::protobuf::uint32 optional_uint32_;
  ::google::protobuf::uint64 optional_uint64_;
  ::google::protobuf::int64 optional_sint64_;
  ::google::protobuf::int32 optional_sint32_;
  ::google::protobuf::uint32 optional_fixed32_;
  ::google::protobuf::uint64 optional_fixed64_;
  ::google::protobuf::int64 optional_sfixed64_;
  ::google::protobuf::int32 optional_sfixed32_;
  float optional_float_;
  double optional_double_;
  bool optional_bool_;
  int default_import_enum_;
  int optional_nested_enum_;
  int optional_foreign_enum_;
  int optional_import_enum_;
  ::google::protobuf::int32 default_int32_;
  ::google::protobuf::int64 default_int64_;
  ::google::protobuf::uint64 default_uint64_;
  ::google::protobuf::uint32 default_uint32_;
  ::google::protobuf::int32 default_sint32_;
  ::google::protobuf::int64 default_sint64_;
  ::google::protobuf::uint64 default_fixed64_;
  ::google::protobuf::uint32 default_fixed32_;
  ::google::protobuf::int32 default_sfixed32_;
  ::google::protobuf::int64 default_sfixed64_;
  double default_double_;
  float default_float_;
  bool default_bool_;
  int default_nested_enum_;
  int default_foreign_enum_;
  union OneofFieldUnion {
    OneofFieldUnion() {}
    ::google::protobuf::uint32 oneof_uint32_;
    ::protobuf_unittest_no_arena::TestAllTypes_NestedMessage* oneof_nested_message_;
    ::google::protobuf::internal::ArenaStringPtr oneof_string_;
    ::google::protobuf::internal::ArenaStringPtr oneof_bytes_;
    ::protobuf_unittest_no_arena::TestAllTypes_NestedMessage* lazy_oneof_nested_message_;
  } oneof_field_;
  ::google::protobuf::uint32 _oneof_case_[1];

  friend void  protobuf_InitDefaults_google_2fprotobuf_2funittest_5fno_5farena_2eproto_impl();
  friend void  protobuf_AddDesc_google_2fprotobuf_2funittest_5fno_5farena_2eproto_impl();
  friend void protobuf_AssignDesc_google_2fprotobuf_2funittest_5fno_5farena_2eproto();
  friend void protobuf_ShutdownFile_google_2fprotobuf_2funittest_5fno_5farena_2eproto();

  void InitAsDefaultInstance();
};
extern ::google::protobuf::internal::ExplicitlyConstructed<TestAllTypes> TestAllTypes_default_instance_;

// -------------------------------------------------------------------

class ForeignMessage : public ::google::protobuf::Message /* @@protoc_insertion_point(class_definition:protobuf_unittest_no_arena.ForeignMessage) */ {
 public:
  ForeignMessage();
  virtual ~ForeignMessage();

  ForeignMessage(const ForeignMessage& from);

  inline ForeignMessage& operator=(const ForeignMessage& from) {
    CopyFrom(from);
    return *this;
  }

  inline const ::google::protobuf::UnknownFieldSet& unknown_fields() const {
    return _internal_metadata_.unknown_fields();
  }

  inline ::google::protobuf::UnknownFieldSet* mutable_unknown_fields() {
    return _internal_metadata_.mutable_unknown_fields();
  }

  static const ::google::protobuf::Descriptor* descriptor();
  static const ForeignMessage& default_instance();

  static const ForeignMessage* internal_default_instance();

  void Swap(ForeignMessage* other);

  // implements Message ----------------------------------------------

  inline ForeignMessage* New() const { return New(NULL); }

  ForeignMessage* New(::google::protobuf::Arena* arena) const;
  void CopyFrom(const ::google::protobuf::Message& from);
  void MergeFrom(const ::google::protobuf::Message& from);
  void CopyFrom(const ForeignMessage& from);
  void MergeFrom(const ForeignMessage& from);
  void Clear();
  bool IsInitialized() const;

  size_t ByteSizeLong() const;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input);
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const;
  ::google::protobuf::uint8* InternalSerializeWithCachedSizesToArray(
      bool deterministic, ::google::protobuf::uint8* output) const;
  ::google::protobuf::uint8* SerializeWithCachedSizesToArray(::google::protobuf::uint8* output) const {
    return InternalSerializeWithCachedSizesToArray(false, output);
  }
  int GetCachedSize() const { return _cached_size_; }
  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const;
  void InternalSwap(ForeignMessage* other);
  void UnsafeMergeFrom(const ForeignMessage& from);
  private:
  inline ::google::protobuf::Arena* GetArenaNoVirtual() const {
    return _internal_metadata_.arena();
  }
  inline void* MaybeArenaPtr() const {
    return _internal_metadata_.raw_arena_ptr();
  }
  public:

  ::google::protobuf::Metadata GetMetadata() const;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // optional int32 c = 1;
  bool has_c() const;
  void clear_c();
  static const int kCFieldNumber = 1;
  ::google::protobuf::int32 c() const;
  void set_c(::google::protobuf::int32 value);

  // @@protoc_insertion_point(class_scope:protobuf_unittest_no_arena.ForeignMessage)
 private:
  inline void set_has_c();
  inline void clear_has_c();

  ::google::protobuf::internal::InternalMetadataWithArena _internal_metadata_;
  ::google::protobuf::internal::HasBits<1> _has_bits_;
  mutable int _cached_size_;
  ::google::protobuf::int32 c_;
  friend void  protobuf_InitDefaults_google_2fprotobuf_2funittest_5fno_5farena_2eproto_impl();
  friend void  protobuf_AddDesc_google_2fprotobuf_2funittest_5fno_5farena_2eproto_impl();
  friend void protobuf_AssignDesc_google_2fprotobuf_2funittest_5fno_5farena_2eproto();
  friend void protobuf_ShutdownFile_google_2fprotobuf_2funittest_5fno_5farena_2eproto();

  void InitAsDefaultInstance();
};
extern ::google::protobuf::internal::ExplicitlyConstructed<ForeignMessage> ForeignMessage_default_instance_;

// -------------------------------------------------------------------

class TestNoArenaMessage : public ::google::protobuf::Message /* @@protoc_insertion_point(class_definition:protobuf_unittest_no_arena.TestNoArenaMessage) */ {
 public:
  TestNoArenaMessage();
  virtual ~TestNoArenaMessage();

  TestNoArenaMessage(const TestNoArenaMessage& from);

  inline TestNoArenaMessage& operator=(const TestNoArenaMessage& from) {
    CopyFrom(from);
    return *this;
  }

  inline const ::google::protobuf::UnknownFieldSet& unknown_fields() const {
    return _internal_metadata_.unknown_fields();
  }

  inline ::google::protobuf::UnknownFieldSet* mutable_unknown_fields() {
    return _internal_metadata_.mutable_unknown_fields();
  }

  static const ::google::protobuf::Descriptor* descriptor();
  static const TestNoArenaMessage& default_instance();

  static const TestNoArenaMessage* internal_default_instance();

  void Swap(TestNoArenaMessage* other);

  // implements Message ----------------------------------------------

  inline TestNoArenaMessage* New() const { return New(NULL); }

  TestNoArenaMessage* New(::google::protobuf::Arena* arena) const;
  void CopyFrom(const ::google::protobuf::Message& from);
  void MergeFrom(const ::google::protobuf::Message& from);
  void CopyFrom(const TestNoArenaMessage& from);
  void MergeFrom(const TestNoArenaMessage& from);
  void Clear();
  bool IsInitialized() const;

  size_t ByteSizeLong() const;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input);
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const;
  ::google::protobuf::uint8* InternalSerializeWithCachedSizesToArray(
      bool deterministic, ::google::protobuf::uint8* output) const;
  ::google::protobuf::uint8* SerializeWithCachedSizesToArray(::google::protobuf::uint8* output) const {
    return InternalSerializeWithCachedSizesToArray(false, output);
  }
  int GetCachedSize() const { return _cached_size_; }
  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const;
  void InternalSwap(TestNoArenaMessage* other);
  void UnsafeMergeFrom(const TestNoArenaMessage& from);
  private:
  inline ::google::protobuf::Arena* GetArenaNoVirtual() const {
    return _internal_metadata_.arena();
  }
  inline void* MaybeArenaPtr() const {
    return _internal_metadata_.raw_arena_ptr();
  }
  public:

  ::google::protobuf::Metadata GetMetadata() const;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // optional .proto2_arena_unittest.ArenaMessage arena_message = 1;
  bool has_arena_message() const;
  void clear_arena_message();
  static const int kArenaMessageFieldNumber = 1;
  const ::proto2_arena_unittest::ArenaMessage& arena_message() const;
  ::proto2_arena_unittest::ArenaMessage* mutable_arena_message();
  ::proto2_arena_unittest::ArenaMessage* release_arena_message();
  void set_allocated_arena_message(::proto2_arena_unittest::ArenaMessage* arena_message);

  // @@protoc_insertion_point(class_scope:protobuf_unittest_no_arena.TestNoArenaMessage)
 private:
  inline void set_has_arena_message();
  inline void clear_has_arena_message();

  ::google::protobuf::internal::InternalMetadataWithArena _internal_metadata_;
  ::google::protobuf::internal::HasBits<1> _has_bits_;
  mutable int _cached_size_;
  ::proto2_arena_unittest::ArenaMessage* arena_message_;
  friend void  protobuf_InitDefaults_google_2fprotobuf_2funittest_5fno_5farena_2eproto_impl();
  friend void  protobuf_AddDesc_google_2fprotobuf_2funittest_5fno_5farena_2eproto_impl();
  friend void protobuf_AssignDesc_google_2fprotobuf_2funittest_5fno_5farena_2eproto();
  friend void protobuf_ShutdownFile_google_2fprotobuf_2funittest_5fno_5farena_2eproto();

  void InitAsDefaultInstance();
};
extern ::google::protobuf::internal::ExplicitlyConstructed<TestNoArenaMessage> TestNoArenaMessage_default_instance_;

// ===================================================================


// ===================================================================

#if !PROTOBUF_INLINE_NOT_IN_HEADERS
// TestAllTypes_NestedMessage

// optional int32 bb = 1;
inline bool TestAllTypes_NestedMessage::has_bb() const {
  return (_has_bits_[0] & 0x00000001u) != 0;
}
inline void TestAllTypes_NestedMessage::set_has_bb() {
  _has_bits_[0] |= 0x00000001u;
}
inline void TestAllTypes_NestedMessage::clear_has_bb() {
  _has_bits_[0] &= ~0x00000001u;
}
inline void TestAllTypes_NestedMessage::clear_bb() {
  bb_ = 0;
  clear_has_bb();
}
inline ::google::protobuf::int32 TestAllTypes_NestedMessage::bb() const {
  // @@protoc_insertion_point(field_get:protobuf_unittest_no_arena.TestAllTypes.NestedMessage.bb)
  return bb_;
}
inline void TestAllTypes_NestedMessage::set_bb(::google::protobuf::int32 value) {
  set_has_bb();
  bb_ = value;
  // @@protoc_insertion_point(field_set:protobuf_unittest_no_arena.TestAllTypes.NestedMessage.bb)
}

inline const TestAllTypes_NestedMessage* TestAllTypes_NestedMessage::internal_default_instance() {
  return &TestAllTypes_NestedMessage_default_instance_.get();
}
// -------------------------------------------------------------------

// TestAllTypes_OptionalGroup

// optional int32 a = 17;
inline bool TestAllTypes_OptionalGroup::has_a() const {
  return (_has_bits_[0] & 0x00000001u) != 0;
}
inline void TestAllTypes_OptionalGroup::set_has_a() {
  _has_bits_[0] |= 0x00000001u;
}
inline void TestAllTypes_OptionalGroup::clear_has_a() {
  _has_bits_[0] &= ~0x00000001u;
}
inline void TestAllTypes_OptionalGroup::clear_a() {
  a_ = 0;
  clear_has_a();
}
inline ::google::protobuf::int32 TestAllTypes_OptionalGroup::a() const {
  // @@protoc_insertion_point(field_get:protobuf_unittest_no_arena.TestAllTypes.OptionalGroup.a)
  return a_;
}
inline void TestAllTypes_OptionalGroup::set_a(::google::protobuf::int32 value) {
  set_has_a();
  a_ = value;
  // @@protoc_insertion_point(field_set:protobuf_unittest_no_arena.TestAllTypes.OptionalGroup.a)
}

inline const TestAllTypes_OptionalGroup* TestAllTypes_OptionalGroup::internal_default_instance() {
  return &TestAllTypes_OptionalGroup_default_instance_.get();
}
// -------------------------------------------------------------------

// TestAllTypes_RepeatedGroup

// optional int32 a = 47;
inline bool TestAllTypes_RepeatedGroup::has_a() const {
  return (_has_bits_[0] & 0x00000001u) != 0;
}
inline void TestAllTypes_RepeatedGroup::set_has_a() {
  _has_bits_[0] |= 0x00000001u;
}
inline void TestAllTypes_RepeatedGroup::clear_has_a() {
  _has_bits_[0] &= ~0x00000001u;
}
inline void TestAllTypes_RepeatedGroup::clear_a() {
  a_ = 0;
  clear_has_a();
}
inline ::google::protobuf::int32 TestAllTypes_RepeatedGroup::a() const {
  // @@protoc_insertion_point(field_get:protobuf_unittest_no_arena.TestAllTypes.RepeatedGroup.a)
  return a_;
}
inline void TestAllTypes_RepeatedGroup::set_a(::google::protobuf::int32 value) {
  set_has_a();
  a_ = value;
  // @@protoc_insertion_point(field_set:protobuf_unittest_no_arena.TestAllTypes.RepeatedGroup.a)
}

inline const TestAllTypes_RepeatedGroup* TestAllTypes_RepeatedGroup::internal_default_instance() {
  return &TestAllTypes_RepeatedGroup_default_instance_.get();
}
// -------------------------------------------------------------------

// TestAllTypes

// optional int32 optional_int32 = 1;
inline bool TestAllTypes::has_optional_int32() const {
  return (_has_bits_[0] & 0x00000001u) != 0;
}
inline void TestAllTypes::set_has_optional_int32() {
  _has_bits_[0] |= 0x00000001u;
}
inline void TestAllTypes::clear_has_optional_int32() {
  _has_bits_[0] &= ~0x00000001u;
}
inline void TestAllTypes::clear_optional_int32() {
  optional_int32_ = 0;
  clear_has_optional_int32();
}
inline ::google::protobuf::int32 TestAllTypes::optional_int32() const {
  // @@protoc_insertion_point(field_get:protobuf_unittest_no_arena.TestAllTypes.optional_int32)
  return optional_int32_;
}
inline void TestAllTypes::set_optional_int32(::google::protobuf::int32 value) {
  set_has_optional_int32();
  optional_int32_ = value;
  // @@protoc_insertion_point(field_set:protobuf_unittest_no_arena.TestAllTypes.optional_int32)
}

// optional int64 optional_int64 = 2;
inline bool TestAllTypes::has_optional_int64() const {
  return (_has_bits_[0] & 0x00000002u) != 0;
}
inline void TestAllTypes::set_has_optional_int64() {
  _has_bits_[0] |= 0x00000002u;
}
inline void TestAllTypes::clear_has_optional_int64() {
  _has_bits_[0] &= ~0x00000002u;
}
inline void TestAllTypes::clear_optional_int64() {
  optional_int64_ = GOOGLE_LONGLONG(0);
  clear_has_optional_int64();
}
inline ::google::protobuf::int64 TestAllTypes::optional_int64() const {
  // @@protoc_insertion_point(field_get:protobuf_unittest_no_arena.TestAllTypes.optional_int64)
  return optional_int64_;
}
inline void TestAllTypes::set_optional_int64(::google::protobuf::int64 value) {
  set_has_optional_int64();
  optional_int64_ = value;
  // @@protoc_insertion_point(field_set:protobuf_unittest_no_arena.TestAllTypes.optional_int64)
}

// optional uint32 optional_uint32 = 3;
inline bool TestAllTypes::has_optional_uint32() const {
  return (_has_bits_[0] & 0x00000004u) != 0;
}
inline void TestAllTypes::set_has_optional_uint32() {
  _has_bits_[0] |= 0x00000004u;
}
inline void TestAllTypes::clear_has_optional_uint32() {
  _has_bits_[0] &= ~0x00000004u;
}
inline void TestAllTypes::clear_optional_uint32() {
  optional_uint32_ = 0u;
  clear_has_optional_uint32();
}
inline ::google::protobuf::uint32 TestAllTypes::optional_uint32() const {
  // @@protoc_insertion_point(field_get:protobuf_unittest_no_arena.TestAllTypes.optional_uint32)
  return optional_uint32_;
}
inline void TestAllTypes::set_optional_uint32(::google::protobuf::uint32 value) {
  set_has_optional_uint32();
  optional_uint32_ = value;
  // @@protoc_insertion_point(field_set:protobuf_unittest_no_arena.TestAllTypes.optional_uint32)
}

// optional uint64 optional_uint64 = 4;
inline bool TestAllTypes::has_optional_uint64() const {
  return (_has_bits_[0] & 0x00000008u) != 0;
}
inline void TestAllTypes::set_has_optional_uint64() {
  _has_bits_[0] |= 0x00000008u;
}
inline void TestAllTypes::clear_has_optional_uint64() {
  _has_bits_[0] &= ~0x00000008u;
}
inline void TestAllTypes::clear_optional_uint64() {
  optional_uint64_ = GOOGLE_ULONGLONG(0);
  clear_has_optional_uint64();
}
inline ::google::protobuf::uint64 TestAllTypes::optional_uint64() const {
  // @@protoc_insertion_point(field_get:protobuf_unittest_no_arena.TestAllTypes.optional_uint64)
  return optional_uint64_;
}
inline void TestAllTypes::set_optional_uint64(::google::protobuf::uint64 value) {
  set_has_optional_uint64();
  optional_uint64_ = value;
  // @@protoc_insertion_point(field_set:protobuf_unittest_no_arena.TestAllTypes.optional_uint64)
}

// optional sint32 optional_sint32 = 5;
inline bool TestAllTypes::has_optional_sint32() const {
  return (_has_bits_[0] & 0x00000010u) != 0;
}
inline void TestAllTypes::set_has_optional_sint32() {
  _has_bits_[0] |= 0x00000010u;
}
inline void TestAllTypes::clear_has_optional_sint32() {
  _has_bits_[0] &= ~0x00000010u;
}
inline void TestAllTypes::clear_optional_sint32() {
  optional_sint32_ = 0;
  clear_has_optional_sint32();
}
inline ::google::protobuf::int32 TestAllTypes::optional_sint32() const {
  // @@protoc_insertion_point(field_get:protobuf_unittest_no_arena.TestAllTypes.optional_sint32)
  return optional_sint32_;
}
inline void TestAllTypes::set_optional_sint32(::google::protobuf::int32 value) {
  set_has_optional_sint32();
  optional_sint32_ = value;
  // @@protoc_insertion_point(field_set:protobuf_unittest_no_arena.TestAllTypes.optional_sint32)
}

// optional sint64 optional_sint64 = 6;
inline bool TestAllTypes::has_optional_sint64() const {
  return (_has_bits_[0] & 0x00000020u) != 0;
}
inline void TestAllTypes::set_has_optional_sint64() {
  _has_bits_[0] |= 0x00000020u;
}
inline void TestAllTypes::clear_has_optional_sint64() {
  _has_bits_[0] &= ~0x00000020u;
}
inline void TestAllTypes::clear_optional_sint64() {
  optional_sint64_ = GOOGLE_LONGLONG(0);
  clear_has_optional_sint64();
}
inline ::google::protobuf::int64 TestAllTypes::optional_sint64() const {
  // @@protoc_insertion_point(field_get:protobuf_unittest_no_arena.TestAllTypes.optional_sint64)
  return optional_sint64_;
}
inline void TestAllTypes::set_optional_sint64(::google::protobuf::int64 value) {
  set_has_optional_sint64();
  optional_sint64_ = value;
  // @@protoc_insertion_point(field_set:protobuf_unittest_no_arena.TestAllTypes.optional_sint64)
}

// optional fixed32 optional_fixed32 = 7;
inline bool TestAllTypes::has_optional_fixed32() const {
  return (_has_bits_[0] & 0x00000040u) != 0;
}
inline void TestAllTypes::set_has_optional_fixed32() {
  _has_bits_[0] |= 0x00000040u;
}
inline void TestAllTypes::clear_has_optional_fixed32() {
  _has_bits_[0] &= ~0x00000040u;
}
inline void TestAllTypes::clear_optional_fixed32() {
  optional_fixed32_ = 0u;
  clear_has_optional_fixed32();
}
inline ::google::protobuf::uint32 TestAllTypes::optional_fixed32() const {
  // @@protoc_insertion_point(field_get:protobuf_unittest_no_arena.TestAllTypes.optional_fixed32)
  return optional_fixed32_;
}
inline void TestAllTypes::set_optional_fixed32(::google::protobuf::uint32 value) {
  set_has_optional_fixed32();
  optional_fixed32_ = value;
  // @@protoc_insertion_point(field_set:protobuf_unittest_no_arena.TestAllTypes.optional_fixed32)
}

// optional fixed64 optional_fixed64 = 8;
inline bool TestAllTypes::has_optional_fixed64() const {
  return (_has_bits_[0] & 0x00000080u) != 0;
}
inline void TestAllTypes::set_has_optional_fixed64() {
  _has_bits_[0] |= 0x00000080u;
}
inline void TestAllTypes::clear_has_optional_fixed64() {
  _has_bits_[0] &= ~0x00000080u;
}
inline void TestAllTypes::clear_optional_fixed64() {
  optional_fixed64_ = GOOGLE_ULONGLONG(0);
  clear_has_optional_fixed64();
}
inline ::google::protobuf::uint64 TestAllTypes::optional_fixed64() const {
  // @@protoc_insertion_point(field_get:protobuf_unittest_no_arena.TestAllTypes.optional_fixed64)
  return optional_fixed64_;
}
inline void TestAllTypes::set_optional_fixed64(::google::protobuf::uint64 value) {
  set_has_optional_fixed64();
  optional_fixed64_ = value;
  // @@protoc_insertion_point(field_set:protobuf_unittest_no_arena.TestAllTypes.optional_fixed64)
}

// optional sfixed32 optional_sfixed32 = 9;
inline bool TestAllTypes::has_optional_sfixed32() const {
  return (_has_bits_[0] & 0x00000100u) != 0;
}
inline void TestAllTypes::set_has_optional_sfixed32() {
  _has_bits_[0] |= 0x00000100u;
}
inline void TestAllTypes::clear_has_optional_sfixed32() {
  _has_bits_[0] &= ~0x00000100u;
}
inline void TestAllTypes::clear_optional_sfixed32() {
  optional_sfixed32_ = 0;
  clear_has_optional_sfixed32();
}
inline ::google::protobuf::int32 TestAllTypes::optional_sfixed32() const {
  // @@protoc_insertion_point(field_get:protobuf_unittest_no_arena.TestAllTypes.optional_sfixed32)
  return optional_sfixed32_;
}
inline void TestAllTypes::set_optional_sfixed32(::google::protobuf::int32 value) {
  set_has_optional_sfixed32();
  optional_sfixed32_ = value;
  // @@protoc_insertion_point(field_set:protobuf_unittest_no_arena.TestAllTypes.optional_sfixed32)
}

// optional sfixed64 optional_sfixed64 = 10;
inline bool TestAllTypes::has_optional_sfixed64() const {
  return (_has_bits_[0] & 0x00000200u) != 0;
}
inline void TestAllTypes::set_has_optional_sfixed64() {
  _has_bits_[0] |= 0x00000200u;
}
inline void TestAllTypes::clear_has_optional_sfixed64() {
  _has_bits_[0] &= ~0x00000200u;
}
inline void TestAllTypes::clear_optional_sfixed64() {
  optional_sfixed64_ = GOOGLE_LONGLONG(0);
  clear_has_optional_sfixed64();
}
inline ::google::protobuf::int64 TestAllTypes::optional_sfixed64() const {
  // @@protoc_insertion_point(field_get:protobuf_unittest_no_arena.TestAllTypes.optional_sfixed64)
  return optional_sfixed64_;
}
inline void TestAllTypes::set_optional_sfixed64(::google::protobuf::int64 value) {
  set_has_optional_sfixed64();
  optional_sfixed64_ = value;
  // @@protoc_insertion_point(field_set:protobuf_unittest_no_arena.TestAllTypes.optional_sfixed64)
}

// optional float optional_float = 11;
inline bool TestAllTypes::has_optional_float() const {
  return (_has_bits_[0] & 0x00000400u) != 0;
}
inline void TestAllTypes::set_has_optional_float() {
  _has_bits_[0] |= 0x00000400u;
}
inline void TestAllTypes::clear_has_optional_float() {
  _has_bits_[0] &= ~0x00000400u;
}
inline void TestAllTypes::clear_optional_float() {
  optional_float_ = 0;
  clear_has_optional_float();
}
inline float TestAllTypes::optional_float() const {
  // @@protoc_insertion_point(field_get:protobuf_unittest_no_arena.TestAllTypes.optional_float)
  return optional_float_;
}
inline void TestAllTypes::set_optional_float(float value) {
  set_has_optional_float();
  optional_float_ = value;
  // @@protoc_insertion_point(field_set:protobuf_unittest_no_arena.TestAllTypes.optional_float)
}

// optional double optional_double = 12;
inline bool TestAllTypes::has_optional_double() const {
  return (_has_bits_[0] & 0x00000800u) != 0;
}
inline void TestAllTypes::set_has_optional_double() {
  _has_bits_[0] |= 0x00000800u;
}
inline void TestAllTypes::clear_has_optional_double() {
  _has_bits_[0] &= ~0x00000800u;
}
inline void TestAllTypes::clear_optional_double() {
  optional_double_ = 0;
  clear_has_optional_double();
}
inline double TestAllTypes::optional_double() const {
  // @@protoc_insertion_point(field_get:protobuf_unittest_no_arena.TestAllTypes.optional_double)
  return optional_double_;
}
inline void TestAllTypes::set_optional_double(double value) {
  set_has_optional_double();
  optional_double_ = value;
  // @@protoc_insertion_point(field_set:protobuf_unittest_no_arena.TestAllTypes.optional_double)
}

// optional bool optional_bool = 13;
inline bool TestAllTypes::has_optional_bool() const {
  return (_has_bits_[0] & 0x00001000u) != 0;
}
inline void TestAllTypes::set_has_optional_bool() {
  _has_bits_[0] |= 0x00001000u;
}
inline void TestAllTypes::clear_has_optional_bool() {
  _has_bits_[0] &= ~0x00001000u;
}
inline void TestAllTypes::clear_optional_bool() {
  optional_bool_ = false;
  clear_has_optional_bool();
}
inline bool TestAllTypes::optional_bool() const {
  // @@protoc_insertion_point(field_get:protobuf_unittest_no_arena.TestAllTypes.optional_bool)
  return optional_bool_;
}
inline void TestAllTypes::set_optional_bool(bool value) {
  set_has_optional_bool();
  optional_bool_ = value;
  // @@protoc_insertion_point(field_set:protobuf_unittest_no_arena.TestAllTypes.optional_bool)
}

// optional string optional_string = 14;
inline bool TestAllTypes::has_optional_string() const {
  return (_has_bits_[0] & 0x00002000u) != 0;
}
inline void TestAllTypes::set_has_optional_string() {
  _has_bits_[0] |= 0x00002000u;
}
inline void TestAllTypes::clear_has_optional_string() {
  _has_bits_[0] &= ~0x00002000u;
}
inline void TestAllTypes::clear_optional_string() {
  optional_string_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  clear_has_optional_string();
}
inline const ::std::string& TestAllTypes::optional_string() const {
  // @@protoc_insertion_point(field_get:protobuf_unittest_no_arena.TestAllTypes.optional_string)
  return optional_string_.GetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void TestAllTypes::set_optional_string(const ::std::string& value) {
  set_has_optional_string();
  optional_string_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:protobuf_unittest_no_arena.TestAllTypes.optional_string)
}
inline void TestAllTypes::set_optional_string(const char* value) {
  set_has_optional_string();
  optional_string_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:protobuf_unittest_no_arena.TestAllTypes.optional_string)
}
inline void TestAllTypes::set_optional_string(const char* value, size_t size) {
  set_has_optional_string();
  optional_string_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:protobuf_unittest_no_arena.TestAllTypes.optional_string)
}
inline ::std::string* TestAllTypes::mutable_optional_string() {
  set_has_optional_string();
  // @@protoc_insertion_point(field_mutable:protobuf_unittest_no_arena.TestAllTypes.optional_string)
  return optional_string_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline ::std::string* TestAllTypes::release_optional_string() {
  // @@protoc_insertion_point(field_release:protobuf_unittest_no_arena.TestAllTypes.optional_string)
  clear_has_optional_string();
  return optional_string_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void TestAllTypes::set_allocated_optional_string(::std::string* optional_string) {
  if (optional_string != NULL) {
    set_has_optional_string();
  } else {
    clear_has_optional_string();
  }
  optional_string_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), optional_string);
  // @@protoc_insertion_point(field_set_allocated:protobuf_unittest_no_arena.TestAllTypes.optional_string)
}

// optional bytes optional_bytes = 15;
inline bool TestAllTypes::has_optional_bytes() const {
  return (_has_bits_[0] & 0x00004000u) != 0;
}
inline void TestAllTypes::set_has_optional_bytes() {
  _has_bits_[0] |= 0x00004000u;
}
inline void TestAllTypes::clear_has_optional_bytes() {
  _has_bits_[0] &= ~0x00004000u;
}
inline void TestAllTypes::clear_optional_bytes() {
  optional_bytes_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  clear_has_optional_bytes();
}
inline const ::std::string& TestAllTypes::optional_bytes() const {
  // @@protoc_insertion_point(field_get:protobuf_unittest_no_arena.TestAllTypes.optional_bytes)
  return optional_bytes_.GetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void TestAllTypes::set_optional_bytes(const ::std::string& value) {
  set_has_optional_bytes();
  optional_bytes_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:protobuf_unittest_no_arena.TestAllTypes.optional_bytes)
}
inline void TestAllTypes::set_optional_bytes(const char* value) {
  set_has_optional_bytes();
  optional_bytes_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:protobuf_unittest_no_arena.TestAllTypes.optional_bytes)
}
inline void TestAllTypes::set_optional_bytes(const void* value, size_t size) {
  set_has_optional_bytes();
  optional_bytes_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:protobuf_unittest_no_arena.TestAllTypes.optional_bytes)
}
inline ::std::string* TestAllTypes::mutable_optional_bytes() {
  set_has_optional_bytes();
  // @@protoc_insertion_point(field_mutable:protobuf_unittest_no_arena.TestAllTypes.optional_bytes)
  return optional_bytes_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline ::std::string* TestAllTypes::release_optional_bytes() {
  // @@protoc_insertion_point(field_release:protobuf_unittest_no_arena.TestAllTypes.optional_bytes)
  clear_has_optional_bytes();
  return optional_bytes_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void TestAllTypes::set_allocated_optional_bytes(::std::string* optional_bytes) {
  if (optional_bytes != NULL) {
    set_has_optional_bytes();
  } else {
    clear_has_optional_bytes();
  }
  optional_bytes_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), optional_bytes);
  // @@protoc_insertion_point(field_set_allocated:protobuf_unittest_no_arena.TestAllTypes.optional_bytes)
}

// optional group OptionalGroup = 16 { ... };
inline bool TestAllTypes::has_optionalgroup() const {
  return (_has_bits_[0] & 0x00008000u) != 0;
}
inline void TestAllTypes::set_has_optionalgroup() {
  _has_bits_[0] |= 0x00008000u;
}
inline void TestAllTypes::clear_has_optionalgroup() {
  _has_bits_[0] &= ~0x00008000u;
}
inline void TestAllTypes::clear_optionalgroup() {
  if (optionalgroup_ != NULL) optionalgroup_->::protobuf_unittest_no_arena::TestAllTypes_OptionalGroup::Clear();
  clear_has_optionalgroup();
}
inline const ::protobuf_unittest_no_arena::TestAllTypes_OptionalGroup& TestAllTypes::optionalgroup() const {
  // @@protoc_insertion_point(field_get:protobuf_unittest_no_arena.TestAllTypes.optionalgroup)
  return optionalgroup_ != NULL ? *optionalgroup_
                         : *::protobuf_unittest_no_arena::TestAllTypes_OptionalGroup::internal_default_instance();
}
inline ::protobuf_unittest_no_arena::TestAllTypes_OptionalGroup* TestAllTypes::mutable_optionalgroup() {
  set_has_optionalgroup();
  if (optionalgroup_ == NULL) {
    optionalgroup_ = new ::protobuf_unittest_no_arena::TestAllTypes_OptionalGroup;
  }
  // @@protoc_insertion_point(field_mutable:protobuf_unittest_no_arena.TestAllTypes.optionalgroup)
  return optionalgroup_;
}
inline ::protobuf_unittest_no_arena::TestAllTypes_OptionalGroup* TestAllTypes::release_optionalgroup() {
  // @@protoc_insertion_point(field_release:protobuf_unittest_no_arena.TestAllTypes.optionalgroup)
  clear_has_optionalgroup();
  ::protobuf_unittest_no_arena::TestAllTypes_OptionalGroup* temp = optionalgroup_;
  optionalgroup_ = NULL;
  return temp;
}
inline void TestAllTypes::set_allocated_optionalgroup(::protobuf_unittest_no_arena::TestAllTypes_OptionalGroup* optionalgroup) {
  delete optionalgroup_;
  optionalgroup_ = optionalgroup;
  if (optionalgroup) {
    set_has_optionalgroup();
  } else {
    clear_has_optionalgroup();
  }
  // @@protoc_insertion_point(field_set_allocated:protobuf_unittest_no_arena.TestAllTypes.optionalgroup)
}

// optional .protobuf_unittest_no_arena.TestAllTypes.NestedMessage optional_nested_message = 18;
inline bool TestAllTypes::has_optional_nested_message() const {
  return (_has_bits_[0] & 0x00010000u) != 0;
}
inline void TestAllTypes::set_has_optional_nested_message() {
  _has_bits_[0] |= 0x00010000u;
}
inline void TestAllTypes::clear_has_optional_nested_message() {
  _has_bits_[0] &= ~0x00010000u;
}
inline void TestAllTypes::clear_optional_nested_message() {
  if (optional_nested_message_ != NULL) optional_nested_message_->::protobuf_unittest_no_arena::TestAllTypes_NestedMessage::Clear();
  clear_has_optional_nested_message();
}
inline const ::protobuf_unittest_no_arena::TestAllTypes_NestedMessage& TestAllTypes::optional_nested_message() const {
  // @@protoc_insertion_point(field_get:protobuf_unittest_no_arena.TestAllTypes.optional_nested_message)
  return optional_nested_message_ != NULL ? *optional_nested_message_
                         : *::protobuf_unittest_no_arena::TestAllTypes_NestedMessage::internal_default_instance();
}
inline ::protobuf_unittest_no_arena::TestAllTypes_NestedMessage* TestAllTypes::mutable_optional_nested_message() {
  set_has_optional_nested_message();
  if (optional_nested_message_ == NULL) {
    optional_nested_message_ = new ::protobuf_unittest_no_arena::TestAllTypes_NestedMessage;
  }
  // @@protoc_insertion_point(field_mutable:protobuf_unittest_no_arena.TestAllTypes.optional_nested_message)
  return optional_nested_message_;
}
inline ::protobuf_unittest_no_arena::TestAllTypes_NestedMessage* TestAllTypes::release_optional_nested_message() {
  // @@protoc_insertion_point(field_release:protobuf_unittest_no_arena.TestAllTypes.optional_nested_message)
  clear_has_optional_nested_message();
  ::protobuf_unittest_no_arena::TestAllTypes_NestedMessage* temp = optional_nested_message_;
  optional_nested_message_ = NULL;
  return temp;
}
inline void TestAllTypes::set_allocated_optional_nested_message(::protobuf_unittest_no_arena::TestAllTypes_NestedMessage* optional_nested_message) {
  delete optional_nested_message_;
  optional_nested_message_ = optional_nested_message;
  if (optional_nested_message) {
    set_has_optional_nested_message();
  } else {
    clear_has_optional_nested_message();
  }
  // @@protoc_insertion_point(field_set_allocated:protobuf_unittest_no_arena.TestAllTypes.optional_nested_message)
}

// optional .protobuf_unittest_no_arena.ForeignMessage optional_foreign_message = 19;
inline bool TestAllTypes::has_optional_foreign_message() const {
  return (_has_bits_[0] & 0x00020000u) != 0;
}
inline void TestAllTypes::set_has_optional_foreign_message() {
  _has_bits_[0] |= 0x00020000u;
}
inline void TestAllTypes::clear_has_optional_foreign_message() {
  _has_bits_[0] &= ~0x00020000u;
}
inline void TestAllTypes::clear_optional_foreign_message() {
  if (optional_foreign_message_ != NULL) optional_foreign_message_->::protobuf_unittest_no_arena::ForeignMessage::Clear();
  clear_has_optional_foreign_message();
}
inline const ::protobuf_unittest_no_arena::ForeignMessage& TestAllTypes::optional_foreign_message() const {
  // @@protoc_insertion_point(field_get:protobuf_unittest_no_arena.TestAllTypes.optional_foreign_message)
  return optional_foreign_message_ != NULL ? *optional_foreign_message_
                         : *::protobuf_unittest_no_arena::ForeignMessage::internal_default_instance();
}
inline ::protobuf_unittest_no_arena::ForeignMessage* TestAllTypes::mutable_optional_foreign_message() {
  set_has_optional_foreign_message();
  if (optional_foreign_message_ == NULL) {
    optional_foreign_message_ = new ::protobuf_unittest_no_arena::ForeignMessage;
  }
  // @@protoc_insertion_point(field_mutable:protobuf_unittest_no_arena.TestAllTypes.optional_foreign_message)
  return optional_foreign_message_;
}
inline ::protobuf_unittest_no_arena::ForeignMessage* TestAllTypes::release_optional_foreign_message() {
  // @@protoc_insertion_point(field_release:protobuf_unittest_no_arena.TestAllTypes.optional_foreign_message)
  clear_has_optional_foreign_message();
  ::protobuf_unittest_no_arena::ForeignMessage* temp = optional_foreign_message_;
  optional_foreign_message_ = NULL;
  return temp;
}
inline void TestAllTypes::set_allocated_optional_foreign_message(::protobuf_unittest_no_arena::ForeignMessage* optional_foreign_message) {
  delete optional_foreign_message_;
  optional_foreign_message_ = optional_foreign_message;
  if (optional_foreign_message) {
    set_has_optional_foreign_message();
  } else {
    clear_has_optional_foreign_message();
  }
  // @@protoc_insertion_point(field_set_allocated:protobuf_unittest_no_arena.TestAllTypes.optional_foreign_message)
}

// optional .protobuf_unittest_import.ImportMessage optional_import_message = 20;
inline bool TestAllTypes::has_optional_import_message() const {
  return (_has_bits_[0] & 0x00040000u) != 0;
}
inline void TestAllTypes::set_has_optional_import_message() {
  _has_bits_[0] |= 0x00040000u;
}
inline void TestAllTypes::clear_has_optional_import_message() {
  _has_bits_[0] &= ~0x00040000u;
}
inline void TestAllTypes::clear_optional_import_message() {
  if (optional_import_message_ != NULL) optional_import_message_->::protobuf_unittest_import::ImportMessage::Clear();
  clear_has_optional_import_message();
}
inline const ::protobuf_unittest_import::ImportMessage& TestAllTypes::optional_import_message() const {
  // @@protoc_insertion_point(field_get:protobuf_unittest_no_arena.TestAllTypes.optional_import_message)
  return optional_import_message_ != NULL ? *optional_import_message_
                         : *::protobuf_unittest_import::ImportMessage::internal_default_instance();
}
inline ::protobuf_unittest_import::ImportMessage* TestAllTypes::mutable_optional_import_message() {
  set_has_optional_import_message();
  if (optional_import_message_ == NULL) {
    optional_import_message_ = new ::protobuf_unittest_import::ImportMessage;
  }
  // @@protoc_insertion_point(field_mutable:protobuf_unittest_no_arena.TestAllTypes.optional_import_message)
  return optional_import_message_;
}
inline ::protobuf_unittest_import::ImportMessage* TestAllTypes::release_optional_import_message() {
  // @@protoc_insertion_point(field_release:protobuf_unittest_no_arena.TestAllTypes.optional_import_message)
  clear_has_optional_import_message();
  ::protobuf_unittest_import::ImportMessage* temp = optional_import_message_;
  optional_import_message_ = NULL;
  return temp;
}
inline void TestAllTypes::set_allocated_optional_import_message(::protobuf_unittest_import::ImportMessage* optional_import_message) {
  delete optional_import_message_;
  if (optional_import_message != NULL && optional_import_message->GetArena() != NULL) {
    ::protobuf_unittest_import::ImportMessage* new_optional_import_message = new ::protobuf_unittest_import::ImportMessage;
    new_optional_import_message->CopyFrom(*optional_import_message);
    optional_import_message = new_optional_import_message;
  }
  optional_import_message_ = optional_import_message;
  if (optional_import_message) {
    set_has_optional_import_message();
  } else {
    clear_has_optional_import_message();
  }
  // @@protoc_insertion_point(field_set_allocated:protobuf_unittest_no_arena.TestAllTypes.optional_import_message)
}

// optional .protobuf_unittest_no_arena.TestAllTypes.NestedEnum optional_nested_enum = 21;
inline bool TestAllTypes::has_optional_nested_enum() const {
  return (_has_bits_[0] & 0x00080000u) != 0;
}
inline void TestAllTypes::set_has_optional_nested_enum() {
  _has_bits_[0] |= 0x00080000u;
}
inline void TestAllTypes::clear_has_optional_nested_enum() {
  _has_bits_[0] &= ~0x00080000u;
}
inline void TestAllTypes::clear_optional_nested_enum() {
  optional_nested_enum_ = 1;
  clear_has_optional_nested_enum();
}
inline ::protobuf_unittest_no_arena::TestAllTypes_NestedEnum TestAllTypes::optional_nested_enum() const {
  // @@protoc_insertion_point(field_get:protobuf_unittest_no_arena.TestAllTypes.optional_nested_enum)
  return static_cast< ::protobuf_unittest_no_arena::TestAllTypes_NestedEnum >(optional_nested_enum_);
}
inline void TestAllTypes::set_optional_nested_enum(::protobuf_unittest_no_arena::TestAllTypes_NestedEnum value) {
  assert(::protobuf_unittest_no_arena::TestAllTypes_NestedEnum_IsValid(value));
  set_has_optional_nested_enum();
  optional_nested_enum_ = value;
  // @@protoc_insertion_point(field_set:protobuf_unittest_no_arena.TestAllTypes.optional_nested_enum)
}

// optional .protobuf_unittest_no_arena.ForeignEnum optional_foreign_enum = 22;
inline bool TestAllTypes::has_optional_foreign_enum() const {
  return (_has_bits_[0] & 0x00100000u) != 0;
}
inline void TestAllTypes::set_has_optional_foreign_enum() {
  _has_bits_[0] |= 0x00100000u;
}
inline void TestAllTypes::clear_has_optional_foreign_enum() {
  _has_bits_[0] &= ~0x00100000u;
}
inline void TestAllTypes::clear_optional_foreign_enum() {
  optional_foreign_enum_ = 4;
  clear_has_optional_foreign_enum();
}
inline ::protobuf_unittest_no_arena::ForeignEnum TestAllTypes::optional_foreign_enum() const {
  // @@protoc_insertion_point(field_get:protobuf_unittest_no_arena.TestAllTypes.optional_foreign_enum)
  return static_cast< ::protobuf_unittest_no_arena::ForeignEnum >(optional_foreign_enum_);
}
inline void TestAllTypes::set_optional_foreign_enum(::protobuf_unittest_no_arena::ForeignEnum value) {
  assert(::protobuf_unittest_no_arena::ForeignEnum_IsValid(value));
  set_has_optional_foreign_enum();
  optional_foreign_enum_ = value;
  // @@protoc_insertion_point(field_set:protobuf_unittest_no_arena.TestAllTypes.optional_foreign_enum)
}

// optional .protobuf_unittest_import.ImportEnum optional_import_enum = 23;
inline bool TestAllTypes::has_optional_import_enum() const {
  return (_has_bits_[0] & 0x00200000u) != 0;
}
inline void TestAllTypes::set_has_optional_import_enum() {
  _has_bits_[0] |= 0x00200000u;
}
inline void TestAllTypes::clear_has_optional_import_enum() {
  _has_bits_[0] &= ~0x00200000u;
}
inline void TestAllTypes::clear_optional_import_enum() {
  optional_import_enum_ = 7;
  clear_has_optional_import_enum();
}
inline ::protobuf_unittest_import::ImportEnum TestAllTypes::optional_import_enum() const {
  // @@protoc_insertion_point(field_get:protobuf_unittest_no_arena.TestAllTypes.optional_import_enum)
  return static_cast< ::protobuf_unittest_import::ImportEnum >(optional_import_enum_);
}
inline void TestAllTypes::set_optional_import_enum(::protobuf_unittest_import::ImportEnum value) {
  assert(::protobuf_unittest_import::ImportEnum_IsValid(value));
  set_has_optional_import_enum();
  optional_import_enum_ = value;
  // @@protoc_insertion_point(field_set:protobuf_unittest_no_arena.TestAllTypes.optional_import_enum)
}

// optional string optional_string_piece = 24 [ctype = STRING_PIECE];
inline bool TestAllTypes::has_optional_string_piece() const {
  return (_has_bits_[0] & 0x00400000u) != 0;
}
inline void TestAllTypes::set_has_optional_string_piece() {
  _has_bits_[0] |= 0x00400000u;
}
inline void TestAllTypes::clear_has_optional_string_piece() {
  _has_bits_[0] &= ~0x00400000u;
}
inline void TestAllTypes::clear_optional_string_piece() {
  optional_string_piece_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  clear_has_optional_string_piece();
}
inline const ::std::string& TestAllTypes::optional_string_piece() const {
  // @@protoc_insertion_point(field_get:protobuf_unittest_no_arena.TestAllTypes.optional_string_piece)
  return optional_string_piece_.GetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void TestAllTypes::set_optional_string_piece(const ::std::string& value) {
  set_has_optional_string_piece();
  optional_string_piece_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:protobuf_unittest_no_arena.TestAllTypes.optional_string_piece)
}
inline void TestAllTypes::set_optional_string_piece(const char* value) {
  set_has_optional_string_piece();
  optional_string_piece_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:protobuf_unittest_no_arena.TestAllTypes.optional_string_piece)
}
inline void TestAllTypes::set_optional_string_piece(const char* value, size_t size) {
  set_has_optional_string_piece();
  optional_string_piece_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:protobuf_unittest_no_arena.TestAllTypes.optional_string_piece)
}
inline ::std::string* TestAllTypes::mutable_optional_string_piece() {
  set_has_optional_string_piece();
  // @@protoc_insertion_point(field_mutable:protobuf_unittest_no_arena.TestAllTypes.optional_string_piece)
  return optional_string_piece_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline ::std::string* TestAllTypes::release_optional_string_piece() {
  // @@protoc_insertion_point(field_release:protobuf_unittest_no_arena.TestAllTypes.optional_string_piece)
  clear_has_optional_string_piece();
  return optional_string_piece_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void TestAllTypes::set_allocated_optional_string_piece(::std::string* optional_string_piece) {
  if (optional_string_piece != NULL) {
    set_has_optional_string_piece();
  } else {
    clear_has_optional_string_piece();
  }
  optional_string_piece_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), optional_string_piece);
  // @@protoc_insertion_point(field_set_allocated:protobuf_unittest_no_arena.TestAllTypes.optional_string_piece)
}

// optional string optional_cord = 25 [ctype = CORD];
inline bool TestAllTypes::has_optional_cord() const {
  return (_has_bits_[0] & 0x00800000u) != 0;
}
inline void TestAllTypes::set_has_optional_cord() {
  _has_bits_[0] |= 0x00800000u;
}
inline void TestAllTypes::clear_has_optional_cord() {
  _has_bits_[0] &= ~0x00800000u;
}
inline void TestAllTypes::clear_optional_cord() {
  optional_cord_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  clear_has_optional_cord();
}
inline const ::std::string& TestAllTypes::optional_cord() const {
  // @@protoc_insertion_point(field_get:protobuf_unittest_no_arena.TestAllTypes.optional_cord)
  return optional_cord_.GetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void TestAllTypes::set_optional_cord(const ::std::string& value) {
  set_has_optional_cord();
  optional_cord_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:protobuf_unittest_no_arena.TestAllTypes.optional_cord)
}
inline void TestAllTypes::set_optional_cord(const char* value) {
  set_has_optional_cord();
  optional_cord_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:protobuf_unittest_no_arena.TestAllTypes.optional_cord)
}
inline void TestAllTypes::set_optional_cord(const char* value, size_t size) {
  set_has_optional_cord();
  optional_cord_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:protobuf_unittest_no_arena.TestAllTypes.optional_cord)
}
inline ::std::string* TestAllTypes::mutable_optional_cord() {
  set_has_optional_cord();
  // @@protoc_insertion_point(field_mutable:protobuf_unittest_no_arena.TestAllTypes.optional_cord)
  return optional_cord_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline ::std::string* TestAllTypes::release_optional_cord() {
  // @@protoc_insertion_point(field_release:protobuf_unittest_no_arena.TestAllTypes.optional_cord)
  clear_has_optional_cord();
  return optional_cord_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void TestAllTypes::set_allocated_optional_cord(::std::string* optional_cord) {
  if (optional_cord != NULL) {
    set_has_optional_cord();
  } else {
    clear_has_optional_cord();
  }
  optional_cord_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), optional_cord);
  // @@protoc_insertion_point(field_set_allocated:protobuf_unittest_no_arena.TestAllTypes.optional_cord)
}

// optional .protobuf_unittest_import.PublicImportMessage optional_public_import_message = 26;
inline bool TestAllTypes::has_optional_public_import_message() const {
  return (_has_bits_[0] & 0x01000000u) != 0;
}
inline void TestAllTypes::set_has_optional_public_import_message() {
  _has_bits_[0] |= 0x01000000u;
}
inline void TestAllTypes::clear_has_optional_public_import_message() {
  _has_bits_[0] &= ~0x01000000u;
}
inline void TestAllTypes::clear_optional_public_import_message() {
  if (optional_public_import_message_ != NULL) optional_public_import_message_->::protobuf_unittest_import::PublicImportMessage::Clear();
  clear_has_optional_public_import_message();
}
inline const ::protobuf_unittest_import::PublicImportMessage& TestAllTypes::optional_public_import_message() const {
  // @@protoc_insertion_point(field_get:protobuf_unittest_no_arena.TestAllTypes.optional_public_import_message)
  return optional_public_import_message_ != NULL ? *optional_public_import_message_
                         : *::protobuf_unittest_import::PublicImportMessage::internal_default_instance();
}
inline ::protobuf_unittest_import::PublicImportMessage* TestAllTypes::mutable_optional_public_import_message() {
  set_has_optional_public_import_message();
  if (optional_public_import_message_ == NULL) {
    optional_public_import_message_ = new ::protobuf_unittest_import::PublicImportMessage;
  }
  // @@protoc_insertion_point(field_mutable:protobuf_unittest_no_arena.TestAllTypes.optional_public_import_message)
  return optional_public_import_message_;
}
inline ::protobuf_unittest_import::PublicImportMessage* TestAllTypes::release_optional_public_import_message() {
  // @@protoc_insertion_point(field_release:protobuf_unittest_no_arena.TestAllTypes.optional_public_import_message)
  clear_has_optional_public_import_message();
  ::protobuf_unittest_import::PublicImportMessage* temp = optional_public_import_message_;
  optional_public_import_message_ = NULL;
  return temp;
}
inline void TestAllTypes::set_allocated_optional_public_import_message(::protobuf_unittest_import::PublicImportMessage* optional_public_import_message) {
  delete optional_public_import_message_;
  optional_public_import_message_ = optional_public_import_message;
  if (optional_public_import_message) {
    set_has_optional_public_import_message();
  } else {
    clear_has_optional_public_import_message();
  }
  // @@protoc_insertion_point(field_set_allocated:protobuf_unittest_no_arena.TestAllTypes.optional_public_import_message)
}

// optional .protobuf_unittest_no_arena.TestAllTypes.NestedMessage optional_message = 27 [lazy = true];
inline bool TestAllTypes::has_optional_message() const {
  return (_has_bits_[0] & 0x02000000u) != 0;
}
inline void TestAllTypes::set_has_optional_message() {
  _has_bits_[0] |= 0x02000000u;
}
inline void TestAllTypes::clear_has_optional_message() {
  _has_bits_[0] &= ~0x02000000u;
}
inline void TestAllTypes::clear_optional_message() {
  if (optional_message_ != NULL) optional_message_->::protobuf_unittest_no_arena::TestAllTypes_NestedMessage::Clear();
  clear_has_optional_message();
}
inline const ::protobuf_unittest_no_arena::TestAllTypes_NestedMessage& TestAllTypes::optional_message() const {
  // @@protoc_insertion_point(field_get:protobuf_unittest_no_arena.TestAllTypes.optional_message)
  return optional_message_ != NULL ? *optional_message_
                         : *::protobuf_unittest_no_arena::TestAllTypes_NestedMessage::internal_default_instance();
}
inline ::protobuf_unittest_no_arena::TestAllTypes_NestedMessage* TestAllTypes::mutable_optional_message() {
  set_has_optional_message();
  if (optional_message_ == NULL) {
    optional_message_ = new ::protobuf_unittest_no_arena::TestAllTypes_NestedMessage;
  }
  // @@protoc_insertion_point(field_mutable:protobuf_unittest_no_arena.TestAllTypes.optional_message)
  return optional_message_;
}
inline ::protobuf_unittest_no_arena::TestAllTypes_NestedMessage* TestAllTypes::release_optional_message() {
  // @@protoc_insertion_point(field_release:protobuf_unittest_no_arena.TestAllTypes.optional_message)
  clear_has_optional_message();
  ::protobuf_unittest_no_arena::TestAllTypes_NestedMessage* temp = optional_message_;
  optional_message_ = NULL;
  return temp;
}
inline void TestAllTypes::set_allocated_optional_message(::protobuf_unittest_no_arena::TestAllTypes_NestedMessage* optional_message) {
  delete optional_message_;
  optional_message_ = optional_message;
  if (optional_message) {
    set_has_optional_message();
  } else {
    clear_has_optional_message();
  }
  // @@protoc_insertion_point(field_set_allocated:protobuf_unittest_no_arena.TestAllTypes.optional_message)
}

// repeated int32 repeated_int32 = 31;
inline int TestAllTypes::repeated_int32_size() const {
  return repeated_int32_.size();
}
inline void TestAllTypes::clear_repeated_int32() {
  repeated_int32_.Clear();
}
inline ::google::protobuf::int32 TestAllTypes::repeated_int32(int index) const {
  // @@protoc_insertion_point(field_get:protobuf_unittest_no_arena.TestAllTypes.repeated_int32)
  return repeated_int32_.Get(index);
}
inline void TestAllTypes::set_repeated_int32(int index, ::google::protobuf::int32 value) {
  repeated_int32_.Set(index, value);
  // @@protoc_insertion_point(field_set:protobuf_unittest_no_arena.TestAllTypes.repeated_int32)
}
inline void TestAllTypes::add_repeated_int32(::google::protobuf::int32 value) {
  repeated_int32_.Add(value);
  // @@protoc_insertion_point(field_add:protobuf_unittest_no_arena.TestAllTypes.repeated_int32)
}
inline const ::google::protobuf::RepeatedField< ::google::protobuf::int32 >&
TestAllTypes::repeated_int32() const {
  // @@protoc_insertion_point(field_list:protobuf_unittest_no_arena.TestAllTypes.repeated_int32)
  return repeated_int32_;
}
inline ::google::protobuf::RepeatedField< ::google::protobuf::int32 >*
TestAllTypes::mutable_repeated_int32() {
  // @@protoc_insertion_point(field_mutable_list:protobuf_unittest_no_arena.TestAllTypes.repeated_int32)
  return &repeated_int32_;
}

// repeated int64 repeated_int64 = 32;
inline int TestAllTypes::repeated_int64_size() const {
  return repeated_int64_.size();
}
inline void TestAllTypes::clear_repeated_int64() {
  repeated_int64_.Clear();
}
inline ::google::protobuf::int64 TestAllTypes::repeated_int64(int index) const {
  // @@protoc_insertion_point(field_get:protobuf_unittest_no_arena.TestAllTypes.repeated_int64)
  return repeated_int64_.Get(index);
}
inline void TestAllTypes::set_repeated_int64(int index, ::google::protobuf::int64 value) {
  repeated_int64_.Set(index, value);
  // @@protoc_insertion_point(field_set:protobuf_unittest_no_arena.TestAllTypes.repeated_int64)
}
inline void TestAllTypes::add_repeated_int64(::google::protobuf::int64 value) {
  repeated_int64_.Add(value);
  // @@protoc_insertion_point(field_add:protobuf_unittest_no_arena.TestAllTypes.repeated_int64)
}
inline const ::google::protobuf::RepeatedField< ::google::protobuf::int64 >&
TestAllTypes::repeated_int64() const {
  // @@protoc_insertion_point(field_list:protobuf_unittest_no_arena.TestAllTypes.repeated_int64)
  return repeated_int64_;
}
inline ::google::protobuf::RepeatedField< ::google::protobuf::int64 >*
TestAllTypes::mutable_repeated_int64() {
  // @@protoc_insertion_point(field_mutable_list:protobuf_unittest_no_arena.TestAllTypes.repeated_int64)
  return &repeated_int64_;
}

// repeated uint32 repeated_uint32 = 33;
inline int TestAllTypes::repeated_uint32_size() const {
  return repeated_uint32_.size();
}
inline void TestAllTypes::clear_repeated_uint32() {
  repeated_uint32_.Clear();
}
inline ::google::protobuf::uint32 TestAllTypes::repeated_uint32(int index) const {
  // @@protoc_insertion_point(field_get:protobuf_unittest_no_arena.TestAllTypes.repeated_uint32)
  return repeated_uint32_.Get(index);
}
inline void TestAllTypes::set_repeated_uint32(int index, ::google::protobuf::uint32 value) {
  repeated_uint32_.Set(index, value);
  // @@protoc_insertion_point(field_set:protobuf_unittest_no_arena.TestAllTypes.repeated_uint32)
}
inline void TestAllTypes::add_repeated_uint32(::google::protobuf::uint32 value) {
  repeated_uint32_.Add(value);
  // @@protoc_insertion_point(field_add:protobuf_unittest_no_arena.TestAllTypes.repeated_uint32)
}
inline const ::google::protobuf::RepeatedField< ::google::protobuf::uint32 >&
TestAllTypes::repeated_uint32() const {
  // @@protoc_insertion_point(field_list:protobuf_unittest_no_arena.TestAllTypes.repeated_uint32)
  return repeated_uint32_;
}
inline ::google::protobuf::RepeatedField< ::google::protobuf::uint32 >*
TestAllTypes::mutable_repeated_uint32() {
  // @@protoc_insertion_point(field_mutable_list:protobuf_unittest_no_arena.TestAllTypes.repeated_uint32)
  return &repeated_uint32_;
}

// repeated uint64 repeated_uint64 = 34;
inline int TestAllTypes::repeated_uint64_size() const {
  return repeated_uint64_.size();
}
inline void TestAllTypes::clear_repeated_uint64() {
  repeated_uint64_.Clear();
}
inline ::google::protobuf::uint64 TestAllTypes::repeated_uint64(int index) const {
  // @@protoc_insertion_point(field_get:protobuf_unittest_no_arena.TestAllTypes.repeated_uint64)
  return repeated_uint64_.Get(index);
}
inline void TestAllTypes::set_repeated_uint64(int index, ::google::protobuf::uint64 value) {
  repeated_uint64_.Set(index, value);
  // @@protoc_insertion_point(field_set:protobuf_unittest_no_arena.TestAllTypes.repeated_uint64)
}
inline void TestAllTypes::add_repeated_uint64(::google::protobuf::uint64 value) {
  repeated_uint64_.Add(value);
  // @@protoc_insertion_point(field_add:protobuf_unittest_no_arena.TestAllTypes.repeated_uint64)
}
inline const ::google::protobuf::RepeatedField< ::google::protobuf::uint64 >&
TestAllTypes::repeated_uint64() const {
  // @@protoc_insertion_point(field_list:protobuf_unittest_no_arena.TestAllTypes.repeated_uint64)
  return repeated_uint64_;
}
inline ::google::protobuf::RepeatedField< ::google::protobuf::uint64 >*
TestAllTypes::mutable_repeated_uint64() {
  // @@protoc_insertion_point(field_mutable_list:protobuf_unittest_no_arena.TestAllTypes.repeated_uint64)
  return &repeated_uint64_;
}

// repeated sint32 repeated_sint32 = 35;
inline int TestAllTypes::repeated_sint32_size() const {
  return repeated_sint32_.size();
}
inline void TestAllTypes::clear_repeated_sint32() {
  repeated_sint32_.Clear();
}
inline ::google::protobuf::int32 TestAllTypes::repeated_sint32(int index) const {
  // @@protoc_insertion_point(field_get:protobuf_unittest_no_arena.TestAllTypes.repeated_sint32)
  return repeated_sint32_.Get(index);
}
inline void TestAllTypes::set_repeated_sint32(int index, ::google::protobuf::int32 value) {
  repeated_sint32_.Set(index, value);
  // @@protoc_insertion_point(field_set:protobuf_unittest_no_arena.TestAllTypes.repeated_sint32)
}
inline void TestAllTypes::add_repeated_sint32(::google::protobuf::int32 value) {
  repeated_sint32_.Add(value);
  // @@protoc_insertion_point(field_add:protobuf_unittest_no_arena.TestAllTypes.repeated_sint32)
}
inline const ::google::protobuf::RepeatedField< ::google::protobuf::int32 >&
TestAllTypes::repeated_sint32() const {
  // @@protoc_insertion_point(field_list:protobuf_unittest_no_arena.TestAllTypes.repeated_sint32)
  return repeated_sint32_;
}
inline ::google::protobuf::RepeatedField< ::google::protobuf::int32 >*
TestAllTypes::mutable_repeated_sint32() {
  // @@protoc_insertion_point(field_mutable_list:protobuf_unittest_no_arena.TestAllTypes.repeated_sint32)
  return &repeated_sint32_;
}

// repeated sint64 repeated_sint64 = 36;
inline int TestAllTypes::repeated_sint64_size() const {
  return repeated_sint64_.size();
}
inline void TestAllTypes::clear_repeated_sint64() {
  repeated_sint64_.Clear();
}
inline ::google::protobuf::int64 TestAllTypes::repeated_sint64(int index) const {
  // @@protoc_insertion_point(field_get:protobuf_unittest_no_arena.TestAllTypes.repeated_sint64)
  return repeated_sint64_.Get(index);
}
inline void TestAllTypes::set_repeated_sint64(int index, ::google::protobuf::int64 value) {
  repeated_sint64_.Set(index, value);
  // @@protoc_insertion_point(field_set:protobuf_unittest_no_arena.TestAllTypes.repeated_sint64)
}
inline void TestAllTypes::add_repeated_sint64(::google::protobuf::int64 value) {
  repeated_sint64_.Add(value);
  // @@protoc_insertion_point(field_add:protobuf_unittest_no_arena.TestAllTypes.repeated_sint64)
}
inline const ::google::protobuf::RepeatedField< ::google::protobuf::int64 >&
TestAllTypes::repeated_sint64() const {
  // @@protoc_insertion_point(field_list:protobuf_unittest_no_arena.TestAllTypes.repeated_sint64)
  return repeated_sint64_;
}
inline ::google::protobuf::RepeatedField< ::google::protobuf::int64 >*
TestAllTypes::mutable_repeated_sint64() {
  // @@protoc_insertion_point(field_mutable_list:protobuf_unittest_no_arena.TestAllTypes.repeated_sint64)
  return &repeated_sint64_;
}

// repeated fixed32 repeated_fixed32 = 37;
inline int TestAllTypes::repeated_fixed32_size() const {
  return repeated_fixed32_.size();
}
inline void TestAllTypes::clear_repeated_fixed32() {
  repeated_fixed32_.Clear();
}
inline ::google::protobuf::uint32 TestAllTypes::repeated_fixed32(int index) const {
  // @@protoc_insertion_point(field_get:protobuf_unittest_no_arena.TestAllTypes.repeated_fixed32)
  return repeated_fixed32_.Get(index);
}
inline void TestAllTypes::set_repeated_fixed32(int index, ::google::protobuf::uint32 value) {
  repeated_fixed32_.Set(index, value);
  // @@protoc_insertion_point(field_set:protobuf_unittest_no_arena.TestAllTypes.repeated_fixed32)
}
inline void TestAllTypes::add_repeated_fixed32(::google::protobuf::uint32 value) {
  repeated_fixed32_.Add(value);
  // @@protoc_insertion_point(field_add:protobuf_unittest_no_arena.TestAllTypes.repeated_fixed32)
}
inline const ::google::protobuf::RepeatedField< ::google::protobuf::uint32 >&
TestAllTypes::repeated_fixed32() const {
  // @@protoc_insertion_point(field_list:protobuf_unittest_no_arena.TestAllTypes.repeated_fixed32)
  return repeated_fixed32_;
}
inline ::google::protobuf::RepeatedField< ::google::protobuf::uint32 >*
TestAllTypes::mutable_repeated_fixed32() {
  // @@protoc_insertion_point(field_mutable_list:protobuf_unittest_no_arena.TestAllTypes.repeated_fixed32)
  return &repeated_fixed32_;
}

// repeated fixed64 repeated_fixed64 = 38;
inline int TestAllTypes::repeated_fixed64_size() const {
  return repeated_fixed64_.size();
}
inline void TestAllTypes::clear_repeated_fixed64() {
  repeated_fixed64_.Clear();
}
inline ::google::protobuf::uint64 TestAllTypes::repeated_fixed64(int index) const {
  // @@protoc_insertion_point(field_get:protobuf_unittest_no_arena.TestAllTypes.repeated_fixed64)
  return repeated_fixed64_.Get(index);
}
inline void TestAllTypes::set_repeated_fixed64(int index, ::google::protobuf::uint64 value) {
  repeated_fixed64_.Set(index, value);
  // @@protoc_insertion_point(field_set:protobuf_unittest_no_arena.TestAllTypes.repeated_fixed64)
}
inline void TestAllTypes::add_repeated_fixed64(::google::protobuf::uint64 value) {
  repeated_fixed64_.Add(value);
  // @@protoc_insertion_point(field_add:protobuf_unittest_no_arena.TestAllTypes.repeated_fixed64)
}
inline const ::google::protobuf::RepeatedField< ::google::protobuf::uint64 >&
TestAllTypes::repeated_fixed64() const {
  // @@protoc_insertion_point(field_list:protobuf_unittest_no_arena.TestAllTypes.repeated_fixed64)
  return repeated_fixed64_;
}
inline ::google::protobuf::RepeatedField< ::google::protobuf::uint64 >*
TestAllTypes::mutable_repeated_fixed64() {
  // @@protoc_insertion_point(field_mutable_list:protobuf_unittest_no_arena.TestAllTypes.repeated_fixed64)
  return &repeated_fixed64_;
}

// repeated sfixed32 repeated_sfixed32 = 39;
inline int TestAllTypes::repeated_sfixed32_size() const {
  return repeated_sfixed32_.size();
}
inline void TestAllTypes::clear_repeated_sfixed32() {
  repeated_sfixed32_.Clear();
}
inline ::google::protobuf::int32 TestAllTypes::repeated_sfixed32(int index) const {
  // @@protoc_insertion_point(field_get:protobuf_unittest_no_arena.TestAllTypes.repeated_sfixed32)
  return repeated_sfixed32_.Get(index);
}
inline void TestAllTypes::set_repeated_sfixed32(int index, ::google::protobuf::int32 value) {
  repeated_sfixed32_.Set(index, value);
  // @@protoc_insertion_point(field_set:protobuf_unittest_no_arena.TestAllTypes.repeated_sfixed32)
}
inline void TestAllTypes::add_repeated_sfixed32(::google::protobuf::int32 value) {
  repeated_sfixed32_.Add(value);
  // @@protoc_insertion_point(field_add:protobuf_unittest_no_arena.TestAllTypes.repeated_sfixed32)
}
inline const ::google::protobuf::RepeatedField< ::google::protobuf::int32 >&
TestAllTypes::repeated_sfixed32() const {
  // @@protoc_insertion_point(field_list:protobuf_unittest_no_arena.TestAllTypes.repeated_sfixed32)
  return repeated_sfixed32_;
}
inline ::google::protobuf::RepeatedField< ::google::protobuf::int32 >*
TestAllTypes::mutable_repeated_sfixed32() {
  // @@protoc_insertion_point(field_mutable_list:protobuf_unittest_no_arena.TestAllTypes.repeated_sfixed32)
  return &repeated_sfixed32_;
}

// repeated sfixed64 repeated_sfixed64 = 40;
inline int TestAllTypes::repeated_sfixed64_size() const {
  return repeated_sfixed64_.size();
}
inline void TestAllTypes::clear_repeated_sfixed64() {
  repeated_sfixed64_.Clear();
}
inline ::google::protobuf::int64 TestAllTypes::repeated_sfixed64(int index) const {
  // @@protoc_insertion_point(field_get:protobuf_unittest_no_arena.TestAllTypes.repeated_sfixed64)
  return repeated_sfixed64_.Get(index);
}
inline void TestAllTypes::set_repeated_sfixed64(int index, ::google::protobuf::int64 value) {
  repeated_sfixed64_.Set(index, value);
  // @@protoc_insertion_point(field_set:protobuf_unittest_no_arena.TestAllTypes.repeated_sfixed64)
}
inline void TestAllTypes::add_repeated_sfixed64(::google::protobuf::int64 value) {
  repeated_sfixed64_.Add(value);
  // @@protoc_insertion_point(field_add:protobuf_unittest_no_arena.TestAllTypes.repeated_sfixed64)
}
inline const ::google::protobuf::RepeatedField< ::google::protobuf::int64 >&
TestAllTypes::repeated_sfixed64() const {
  // @@protoc_insertion_point(field_list:protobuf_unittest_no_arena.TestAllTypes.repeated_sfixed64)
  return repeated_sfixed64_;
}
inline ::google::protobuf::RepeatedField< ::google::protobuf::int64 >*
TestAllTypes::mutable_repeated_sfixed64() {
  // @@protoc_insertion_point(field_mutable_list:protobuf_unittest_no_arena.TestAllTypes.repeated_sfixed64)
  return &repeated_sfixed64_;
}

// repeated float repeated_float = 41;
inline int TestAllTypes::repeated_float_size() const {
  return repeated_float_.size();
}
inline void TestAllTypes::clear_repeated_float() {
  repeated_float_.Clear();
}
inline float TestAllTypes::repeated_float(int index) const {
  // @@protoc_insertion_point(field_get:protobuf_unittest_no_arena.TestAllTypes.repeated_float)
  return repeated_float_.Get(index);
}
inline void TestAllTypes::set_repeated_float(int index, float value) {
  repeated_float_.Set(index, value);
  // @@protoc_insertion_point(field_set:protobuf_unittest_no_arena.TestAllTypes.repeated_float)
}
inline void TestAllTypes::add_repeated_float(float value) {
  repeated_float_.Add(value);
  // @@protoc_insertion_point(field_add:protobuf_unittest_no_arena.TestAllTypes.repeated_float)
}
inline const ::google::protobuf::RepeatedField< float >&
TestAllTypes::repeated_float() const {
  // @@protoc_insertion_point(field_list:protobuf_unittest_no_arena.TestAllTypes.repeated_float)
  return repeated_float_;
}
inline ::google::protobuf::RepeatedField< float >*
TestAllTypes::mutable_repeated_float() {
  // @@protoc_insertion_point(field_mutable_list:protobuf_unittest_no_arena.TestAllTypes.repeated_float)
  return &repeated_float_;
}

// repeated double repeated_double = 42;
inline int TestAllTypes::repeated_double_size() const {
  return repeated_double_.size();
}
inline void TestAllTypes::clear_repeated_double() {
  repeated_double_.Clear();
}
inline double TestAllTypes::repeated_double(int index) const {
  // @@protoc_insertion_point(field_get:protobuf_unittest_no_arena.TestAllTypes.repeated_double)
  return repeated_double_.Get(index);
}
inline void TestAllTypes::set_repeated_double(int index, double value) {
  repeated_double_.Set(index, value);
  // @@protoc_insertion_point(field_set:protobuf_unittest_no_arena.TestAllTypes.repeated_double)
}
inline void TestAllTypes::add_repeated_double(double value) {
  repeated_double_.Add(value);
  // @@protoc_insertion_point(field_add:protobuf_unittest_no_arena.TestAllTypes.repeated_double)
}
inline const ::google::protobuf::RepeatedField< double >&
TestAllTypes::repeated_double() const {
  // @@protoc_insertion_point(field_list:protobuf_unittest_no_arena.TestAllTypes.repeated_double)
  return repeated_double_;
}
inline ::google::protobuf::RepeatedField< double >*
TestAllTypes::mutable_repeated_double() {
  // @@protoc_insertion_point(field_mutable_list:protobuf_unittest_no_arena.TestAllTypes.repeated_double)
  return &repeated_double_;
}

// repeated bool repeated_bool = 43;
inline int TestAllTypes::repeated_bool_size() const {
  return repeated_bool_.size();
}
inline void TestAllTypes::clear_repeated_bool() {
  repeated_bool_.Clear();
}
inline bool TestAllTypes::repeated_bool(int index) const {
  // @@protoc_insertion_point(field_get:protobuf_unittest_no_arena.TestAllTypes.repeated_bool)
  return repeated_bool_.Get(index);
}
inline void TestAllTypes::set_repeated_bool(int index, bool value) {
  repeated_bool_.Set(index, value);
  // @@protoc_insertion_point(field_set:protobuf_unittest_no_arena.TestAllTypes.repeated_bool)
}
inline void TestAllTypes::add_repeated_bool(bool value) {
  repeated_bool_.Add(value);
  // @@protoc_insertion_point(field_add:protobuf_unittest_no_arena.TestAllTypes.repeated_bool)
}
inline const ::google::protobuf::RepeatedField< bool >&
TestAllTypes::repeated_bool() const {
  // @@protoc_insertion_point(field_list:protobuf_unittest_no_arena.TestAllTypes.repeated_bool)
  return repeated_bool_;
}
inline ::google::protobuf::RepeatedField< bool >*
TestAllTypes::mutable_repeated_bool() {
  // @@protoc_insertion_point(field_mutable_list:protobuf_unittest_no_arena.TestAllTypes.repeated_bool)
  return &repeated_bool_;
}

// repeated string repeated_string = 44;
inline int TestAllTypes::repeated_string_size() const {
  return repeated_string_.size();
}
inline void TestAllTypes::clear_repeated_string() {
  repeated_string_.Clear();
}
inline const ::std::string& TestAllTypes::repeated_string(int index) const {
  // @@protoc_insertion_point(field_get:protobuf_unittest_no_arena.TestAllTypes.repeated_string)
  return repeated_string_.Get(index);
}
inline ::std::string* TestAllTypes::mutable_repeated_string(int index) {
  // @@protoc_insertion_point(field_mutable:protobuf_unittest_no_arena.TestAllTypes.repeated_string)
  return repeated_string_.Mutable(index);
}
inline void TestAllTypes::set_repeated_string(int index, const ::std::string& value) {
  // @@protoc_insertion_point(field_set:protobuf_unittest_no_arena.TestAllTypes.repeated_string)
  repeated_string_.Mutable(index)->assign(value);
}
inline void TestAllTypes::set_repeated_string(int index, const char* value) {
  repeated_string_.Mutable(index)->assign(value);
  // @@protoc_insertion_point(field_set_char:protobuf_unittest_no_arena.TestAllTypes.repeated_string)
}
inline void TestAllTypes::set_repeated_string(int index, const char* value, size_t size) {
  repeated_string_.Mutable(index)->assign(
    reinterpret_cast<const char*>(value), size);
  // @@protoc_insertion_point(field_set_pointer:protobuf_unittest_no_arena.TestAllTypes.repeated_string)
}
inline ::std::string* TestAllTypes::add_repeated_string() {
  // @@protoc_insertion_point(field_add_mutable:protobuf_unittest_no_arena.TestAllTypes.repeated_string)
  return repeated_string_.Add();
}
inline void TestAllTypes::add_repeated_string(const ::std::string& value) {
  repeated_string_.Add()->assign(value);
  // @@protoc_insertion_point(field_add:protobuf_unittest_no_arena.TestAllTypes.repeated_string)
}
inline void TestAllTypes::add_repeated_string(const char* value) {
  repeated_string_.Add()->assign(value);
  // @@protoc_insertion_point(field_add_char:protobuf_unittest_no_arena.TestAllTypes.repeated_string)
}
inline void TestAllTypes::add_repeated_string(const char* value, size_t size) {
  repeated_string_.Add()->assign(reinterpret_cast<const char*>(value), size);
  // @@protoc_insertion_point(field_add_pointer:protobuf_unittest_no_arena.TestAllTypes.repeated_string)
}
inline const ::google::protobuf::RepeatedPtrField< ::std::string>&
TestAllTypes::repeated_string() const {
  // @@protoc_insertion_point(field_list:protobuf_unittest_no_arena.TestAllTypes.repeated_string)
  return repeated_string_;
}
inline ::google::protobuf::RepeatedPtrField< ::std::string>*
TestAllTypes::mutable_repeated_string() {
  // @@protoc_insertion_point(field_mutable_list:protobuf_unittest_no_arena.TestAllTypes.repeated_string)
  return &repeated_string_;
}

// repeated bytes repeated_bytes = 45;
inline int TestAllTypes::repeated_bytes_size() const {
  return repeated_bytes_.size();
}
inline void TestAllTypes::clear_repeated_bytes() {
  repeated_bytes_.Clear();
}
inline const ::std::string& TestAllTypes::repeated_bytes(int index) const {
  // @@protoc_insertion_point(field_get:protobuf_unittest_no_arena.TestAllTypes.repeated_bytes)
  return repeated_bytes_.Get(index);
}
inline ::std::string* TestAllTypes::mutable_repeated_bytes(int index) {
  // @@protoc_insertion_point(field_mutable:protobuf_unittest_no_arena.TestAllTypes.repeated_bytes)
  return repeated_bytes_.Mutable(index);
}
inline void TestAllTypes::set_repeated_bytes(int index, const ::std::string& value) {
  // @@protoc_insertion_point(field_set:protobuf_unittest_no_arena.TestAllTypes.repeated_bytes)
  repeated_bytes_.Mutable(index)->assign(value);
}
inline void TestAllTypes::set_repeated_bytes(int index, const char* value) {
  repeated_bytes_.Mutable(index)->assign(value);
  // @@protoc_insertion_point(field_set_char:protobuf_unittest_no_arena.TestAllTypes.repeated_bytes)
}
inline void TestAllTypes::set_repeated_bytes(int index, const void* value, size_t size) {
  repeated_bytes_.Mutable(index)->assign(
    reinterpret_cast<const char*>(value), size);
  // @@protoc_insertion_point(field_set_pointer:protobuf_unittest_no_arena.TestAllTypes.repeated_bytes)
}
inline ::std::string* TestAllTypes::add_repeated_bytes() {
  // @@protoc_insertion_point(field_add_mutable:protobuf_unittest_no_arena.TestAllTypes.repeated_bytes)
  return repeated_bytes_.Add();
}
inline void TestAllTypes::add_repeated_bytes(const ::std::string& value) {
  repeated_bytes_.Add()->assign(value);
  // @@protoc_insertion_point(field_add:protobuf_unittest_no_arena.TestAllTypes.repeated_bytes)
}
inline void TestAllTypes::add_repeated_bytes(const char* value) {
  repeated_bytes_.Add()->assign(value);
  // @@protoc_insertion_point(field_add_char:protobuf_unittest_no_arena.TestAllTypes.repeated_bytes)
}
inline void TestAllTypes::add_repeated_bytes(const void* value, size_t size) {
  repeated_bytes_.Add()->assign(reinterpret_cast<const char*>(value), size);
  // @@protoc_insertion_point(field_add_pointer:protobuf_unittest_no_arena.TestAllTypes.repeated_bytes)
}
inline const ::google::protobuf::RepeatedPtrField< ::std::string>&
TestAllTypes::repeated_bytes() const {
  // @@protoc_insertion_point(field_list:protobuf_unittest_no_arena.TestAllTypes.repeated_bytes)
  return repeated_bytes_;
}
inline ::google::protobuf::RepeatedPtrField< ::std::string>*
TestAllTypes::mutable_repeated_bytes() {
  // @@protoc_insertion_point(field_mutable_list:protobuf_unittest_no_arena.TestAllTypes.repeated_bytes)
  return &repeated_bytes_;
}

// repeated group RepeatedGroup = 46 { ... };
inline int TestAllTypes::repeatedgroup_size() const {
  return repeatedgroup_.size();
}
inline void TestAllTypes::clear_repeatedgroup() {
  repeatedgroup_.Clear();
}
inline const ::protobuf_unittest_no_arena::TestAllTypes_RepeatedGroup& TestAllTypes::repeatedgroup(int index) const {
  // @@protoc_insertion_point(field_get:protobuf_unittest_no_arena.TestAllTypes.repeatedgroup)
  return repeatedgroup_.Get(index);
}
inline ::protobuf_unittest_no_arena::TestAllTypes_RepeatedGroup* TestAllTypes::mutable_repeatedgroup(int index) {
  // @@protoc_insertion_point(field_mutable:protobuf_unittest_no_arena.TestAllTypes.repeatedgroup)
  return repeatedgroup_.Mutable(index);
}
inline ::protobuf_unittest_no_arena::TestAllTypes_RepeatedGroup* TestAllTypes::add_repeatedgroup() {
  // @@protoc_insertion_point(field_add:protobuf_unittest_no_arena.TestAllTypes.repeatedgroup)
  return repeatedgroup_.Add();
}
inline ::google::protobuf::RepeatedPtrField< ::protobuf_unittest_no_arena::TestAllTypes_RepeatedGroup >*
TestAllTypes::mutable_repeatedgroup() {
  // @@protoc_insertion_point(field_mutable_list:protobuf_unittest_no_arena.TestAllTypes.repeatedgroup)
  return &repeatedgroup_;
}
inline const ::google::protobuf::RepeatedPtrField< ::protobuf_unittest_no_arena::TestAllTypes_RepeatedGroup >&
TestAllTypes::repeatedgroup() const {
  // @@protoc_insertion_point(field_list:protobuf_unittest_no_arena.TestAllTypes.repeatedgroup)
  return repeatedgroup_;
}

// repeated .protobuf_unittest_no_arena.TestAllTypes.NestedMessage repeated_nested_message = 48;
inline int TestAllTypes::repeated_nested_message_size() const {
  return repeated_nested_message_.size();
}
inline void TestAllTypes::clear_repeated_nested_message() {
  repeated_nested_message_.Clear();
}
inline const ::protobuf_unittest_no_arena::TestAllTypes_NestedMessage& TestAllTypes::repeated_nested_message(int index) const {
  // @@protoc_insertion_point(field_get:protobuf_unittest_no_arena.TestAllTypes.repeated_nested_message)
  return repeated_nested_message_.Get(index);
}
inline ::protobuf_unittest_no_arena::TestAllTypes_NestedMessage* TestAllTypes::mutable_repeated_nested_message(int index) {
  // @@protoc_insertion_point(field_mutable:protobuf_unittest_no_arena.TestAllTypes.repeated_nested_message)
  return repeated_nested_message_.Mutable(index);
}
inline ::protobuf_unittest_no_arena::TestAllTypes_NestedMessage* TestAllTypes::add_repeated_nested_message() {
  // @@protoc_insertion_point(field_add:protobuf_unittest_no_arena.TestAllTypes.repeated_nested_message)
  return repeated_nested_message_.Add();
}
inline ::google::protobuf::RepeatedPtrField< ::protobuf_unittest_no_arena::TestAllTypes_NestedMessage >*
TestAllTypes::mutable_repeated_nested_message() {
  // @@protoc_insertion_point(field_mutable_list:protobuf_unittest_no_arena.TestAllTypes.repeated_nested_message)
  return &repeated_nested_message_;
}
inline const ::google::protobuf::RepeatedPtrField< ::protobuf_unittest_no_arena::TestAllTypes_NestedMessage >&
TestAllTypes::repeated_nested_message() const {
  // @@protoc_insertion_point(field_list:protobuf_unittest_no_arena.TestAllTypes.repeated_nested_message)
  return repeated_nested_message_;
}

// repeated .protobuf_unittest_no_arena.ForeignMessage repeated_foreign_message = 49;
inline int TestAllTypes::repeated_foreign_message_size() const {
  return repeated_foreign_message_.size();
}
inline void TestAllTypes::clear_repeated_foreign_message() {
  repeated_foreign_message_.Clear();
}
inline const ::protobuf_unittest_no_arena::ForeignMessage& TestAllTypes::repeated_foreign_message(int index) const {
  // @@protoc_insertion_point(field_get:protobuf_unittest_no_arena.TestAllTypes.repeated_foreign_message)
  return repeated_foreign_message_.Get(index);
}
inline ::protobuf_unittest_no_arena::ForeignMessage* TestAllTypes::mutable_repeated_foreign_message(int index) {
  // @@protoc_insertion_point(field_mutable:protobuf_unittest_no_arena.TestAllTypes.repeated_foreign_message)
  return repeated_foreign_message_.Mutable(index);
}
inline ::protobuf_unittest_no_arena::ForeignMessage* TestAllTypes::add_repeated_foreign_message() {
  // @@protoc_insertion_point(field_add:protobuf_unittest_no_arena.TestAllTypes.repeated_foreign_message)
  return repeated_foreign_message_.Add();
}
inline ::google::protobuf::RepeatedPtrField< ::protobuf_unittest_no_arena::ForeignMessage >*
TestAllTypes::mutable_repeated_foreign_message() {
  // @@protoc_insertion_point(field_mutable_list:protobuf_unittest_no_arena.TestAllTypes.repeated_foreign_message)
  return &repeated_foreign_message_;
}
inline const ::google::protobuf::RepeatedPtrField< ::protobuf_unittest_no_arena::ForeignMessage >&
TestAllTypes::repeated_foreign_message() const {
  // @@protoc_insertion_point(field_list:protobuf_unittest_no_arena.TestAllTypes.repeated_foreign_message)
  return repeated_foreign_message_;
}

// repeated .protobuf_unittest_import.ImportMessage repeated_import_message = 50;
inline int TestAllTypes::repeated_import_message_size() const {
  return repeated_import_message_.size();
}
inline void TestAllTypes::clear_repeated_import_message() {
  repeated_import_message_.Clear();
}
inline const ::protobuf_unittest_import::ImportMessage& TestAllTypes::repeated_import_message(int index) const {
  // @@protoc_insertion_point(field_get:protobuf_unittest_no_arena.TestAllTypes.repeated_import_message)
  return repeated_import_message_.Get(index);
}
inline ::protobuf_unittest_import::ImportMessage* TestAllTypes::mutable_repeated_import_message(int index) {
  // @@protoc_insertion_point(field_mutable:protobuf_unittest_no_arena.TestAllTypes.repeated_import_message)
  return repeated_import_message_.Mutable(index);
}
inline ::protobuf_unittest_import::ImportMessage* TestAllTypes::add_repeated_import_message() {
  // @@protoc_insertion_point(field_add:protobuf_unittest_no_arena.TestAllTypes.repeated_import_message)
  return repeated_import_message_.Add();
}
inline ::google::protobuf::RepeatedPtrField< ::protobuf_unittest_import::ImportMessage >*
TestAllTypes::mutable_repeated_import_message() {
  // @@protoc_insertion_point(field_mutable_list:protobuf_unittest_no_arena.TestAllTypes.repeated_import_message)
  return &repeated_import_message_;
}
inline const ::google::protobuf::RepeatedPtrField< ::protobuf_unittest_import::ImportMessage >&
TestAllTypes::repeated_import_message() const {
  // @@protoc_insertion_point(field_list:protobuf_unittest_no_arena.TestAllTypes.repeated_import_message)
  return repeated_import_message_;
}

// repeated .protobuf_unittest_no_arena.TestAllTypes.NestedEnum repeated_nested_enum = 51;
inline int TestAllTypes::repeated_nested_enum_size() const {
  return repeated_nested_enum_.size();
}
inline void TestAllTypes::clear_repeated_nested_enum() {
  repeated_nested_enum_.Clear();
}
inline ::protobuf_unittest_no_arena::TestAllTypes_NestedEnum TestAllTypes::repeated_nested_enum(int index) const {
  // @@protoc_insertion_point(field_get:protobuf_unittest_no_arena.TestAllTypes.repeated_nested_enum)
  return static_cast< ::protobuf_unittest_no_arena::TestAllTypes_NestedEnum >(repeated_nested_enum_.Get(index));
}
inline void TestAllTypes::set_repeated_nested_enum(int index, ::protobuf_unittest_no_arena::TestAllTypes_NestedEnum value) {
  assert(::protobuf_unittest_no_arena::TestAllTypes_NestedEnum_IsValid(value));
  repeated_nested_enum_.Set(index, value);
  // @@protoc_insertion_point(field_set:protobuf_unittest_no_arena.TestAllTypes.repeated_nested_enum)
}
inline void TestAllTypes::add_repeated_nested_enum(::protobuf_unittest_no_arena::TestAllTypes_NestedEnum value) {
  assert(::protobuf_unittest_no_arena::TestAllTypes_NestedEnum_IsValid(value));
  repeated_nested_enum_.Add(value);
  // @@protoc_insertion_point(field_add:protobuf_unittest_no_arena.TestAllTypes.repeated_nested_enum)
}
inline const ::google::protobuf::RepeatedField<int>&
TestAllTypes::repeated_nested_enum() const {
  // @@protoc_insertion_point(field_list:protobuf_unittest_no_arena.TestAllTypes.repeated_nested_enum)
  return repeated_nested_enum_;
}
inline ::google::protobuf::RepeatedField<int>*
TestAllTypes::mutable_repeated_nested_enum() {
  // @@protoc_insertion_point(field_mutable_list:protobuf_unittest_no_arena.TestAllTypes.repeated_nested_enum)
  return &repeated_nested_enum_;
}

// repeated .protobuf_unittest_no_arena.ForeignEnum repeated_foreign_enum = 52;
inline int TestAllTypes::repeated_foreign_enum_size() const {
  return repeated_foreign_enum_.size();
}
inline void TestAllTypes::clear_repeated_foreign_enum() {
  repeated_foreign_enum_.Clear();
}
inline ::protobuf_unittest_no_arena::ForeignEnum TestAllTypes::repeated_foreign_enum(int index) const {
  // @@protoc_insertion_point(field_get:protobuf_unittest_no_arena.TestAllTypes.repeated_foreign_enum)
  return static_cast< ::protobuf_unittest_no_arena::ForeignEnum >(repeated_foreign_enum_.Get(index));
}
inline void TestAllTypes::set_repeated_foreign_enum(int index, ::protobuf_unittest_no_arena::ForeignEnum value) {
  assert(::protobuf_unittest_no_arena::ForeignEnum_IsValid(value));
  repeated_foreign_enum_.Set(index, value);
  // @@protoc_insertion_point(field_set:protobuf_unittest_no_arena.TestAllTypes.repeated_foreign_enum)
}
inline void TestAllTypes::add_repeated_foreign_enum(::protobuf_unittest_no_arena::ForeignEnum value) {
  assert(::protobuf_unittest_no_arena::ForeignEnum_IsValid(value));
  repeated_foreign_enum_.Add(value);
  // @@protoc_insertion_point(field_add:protobuf_unittest_no_arena.TestAllTypes.repeated_foreign_enum)
}
inline const ::google::protobuf::RepeatedField<int>&
TestAllTypes::repeated_foreign_enum() const {
  // @@protoc_insertion_point(field_list:protobuf_unittest_no_arena.TestAllTypes.repeated_foreign_enum)
  return repeated_foreign_enum_;
}
inline ::google::protobuf::RepeatedField<int>*
TestAllTypes::mutable_repeated_foreign_enum() {
  // @@protoc_insertion_point(field_mutable_list:protobuf_unittest_no_arena.TestAllTypes.repeated_foreign_enum)
  return &repeated_foreign_enum_;
}

// repeated .protobuf_unittest_import.ImportEnum repeated_import_enum = 53;
inline int TestAllTypes::repeated_import_enum_size() const {
  return repeated_import_enum_.size();
}
inline void TestAllTypes::clear_repeated_import_enum() {
  repeated_import_enum_.Clear();
}
inline ::protobuf_unittest_import::ImportEnum TestAllTypes::repeated_import_enum(int index) const {
  // @@protoc_insertion_point(field_get:protobuf_unittest_no_arena.TestAllTypes.repeated_import_enum)
  return static_cast< ::protobuf_unittest_import::ImportEnum >(repeated_import_enum_.Get(index));
}
inline void TestAllTypes::set_repeated_import_enum(int index, ::protobuf_unittest_import::ImportEnum value) {
  assert(::protobuf_unittest_import::ImportEnum_IsValid(value));
  repeated_import_enum_.Set(index, value);
  // @@protoc_insertion_point(field_set:protobuf_unittest_no_arena.TestAllTypes.repeated_import_enum)
}
inline void TestAllTypes::add_repeated_import_enum(::protobuf_unittest_import::ImportEnum value) {
  assert(::protobuf_unittest_import::ImportEnum_IsValid(value));
  repeated_import_enum_.Add(value);
  // @@protoc_insertion_point(field_add:protobuf_unittest_no_arena.TestAllTypes.repeated_import_enum)
}
inline const ::google::protobuf::RepeatedField<int>&
TestAllTypes::repeated_import_enum() const {
  // @@protoc_insertion_point(field_list:protobuf_unittest_no_arena.TestAllTypes.repeated_import_enum)
  return repeated_import_enum_;
}
inline ::google::protobuf::RepeatedField<int>*
TestAllTypes::mutable_repeated_import_enum() {
  // @@protoc_insertion_point(field_mutable_list:protobuf_unittest_no_arena.TestAllTypes.repeated_import_enum)
  return &repeated_import_enum_;
}

// repeated string repeated_string_piece = 54 [ctype = STRING_PIECE];
inline int TestAllTypes::repeated_string_piece_size() const {
  return repeated_string_piece_.size();
}
inline void TestAllTypes::clear_repeated_string_piece() {
  repeated_string_piece_.Clear();
}
inline const ::std::string& TestAllTypes::repeated_string_piece(int index) const {
  // @@protoc_insertion_point(field_get:protobuf_unittest_no_arena.TestAllTypes.repeated_string_piece)
  return repeated_string_piece_.Get(index);
}
inline ::std::string* TestAllTypes::mutable_repeated_string_piece(int index) {
  // @@protoc_insertion_point(field_mutable:protobuf_unittest_no_arena.TestAllTypes.repeated_string_piece)
  return repeated_string_piece_.Mutable(index);
}
inline void TestAllTypes::set_repeated_string_piece(int index, const ::std::string& value) {
  // @@protoc_insertion_point(field_set:protobuf_unittest_no_arena.TestAllTypes.repeated_string_piece)
  repeated_string_piece_.Mutable(index)->assign(value);
}
inline void TestAllTypes::set_repeated_string_piece(int index, const char* value) {
  repeated_string_piece_.Mutable(index)->assign(value);
  // @@protoc_insertion_point(field_set_char:protobuf_unittest_no_arena.TestAllTypes.repeated_string_piece)
}
inline void TestAllTypes::set_repeated_string_piece(int index, const char* value, size_t size) {
  repeated_string_piece_.Mutable(index)->assign(
    reinterpret_cast<const char*>(value), size);
  // @@protoc_insertion_point(field_set_pointer:protobuf_unittest_no_arena.TestAllTypes.repeated_string_piece)
}
inline ::std::string* TestAllTypes::add_repeated_string_piece() {
  // @@protoc_insertion_point(field_add_mutable:protobuf_unittest_no_arena.TestAllTypes.repeated_string_piece)
  return repeated_string_piece_.Add();
}
inline void TestAllTypes::add_repeated_string_piece(const ::std::string& value) {
  repeated_string_piece_.Add()->assign(value);
  // @@protoc_insertion_point(field_add:protobuf_unittest_no_arena.TestAllTypes.repeated_string_piece)
}
inline void TestAllTypes::add_repeated_string_piece(const char* value) {
  repeated_string_piece_.Add()->assign(value);
  // @@protoc_insertion_point(field_add_char:protobuf_unittest_no_arena.TestAllTypes.repeated_string_piece)
}
inline void TestAllTypes::add_repeated_string_piece(const char* value, size_t size) {
  repeated_string_piece_.Add()->assign(reinterpret_cast<const char*>(value), size);
  // @@protoc_insertion_point(field_add_pointer:protobuf_unittest_no_arena.TestAllTypes.repeated_string_piece)
}
inline const ::google::protobuf::RepeatedPtrField< ::std::string>&
TestAllTypes::repeated_string_piece() const {
  // @@protoc_insertion_point(field_list:protobuf_unittest_no_arena.TestAllTypes.repeated_string_piece)
  return repeated_string_piece_;
}
inline ::google::protobuf::RepeatedPtrField< ::std::string>*
TestAllTypes::mutable_repeated_string_piece() {
  // @@protoc_insertion_point(field_mutable_list:protobuf_unittest_no_arena.TestAllTypes.repeated_string_piece)
  return &repeated_string_piece_;
}

// repeated string repeated_cord = 55 [ctype = CORD];
inline int TestAllTypes::repeated_cord_size() const {
  return repeated_cord_.size();
}
inline void TestAllTypes::clear_repeated_cord() {
  repeated_cord_.Clear();
}
inline const ::std::string& TestAllTypes::repeated_cord(int index) const {
  // @@protoc_insertion_point(field_get:protobuf_unittest_no_arena.TestAllTypes.repeated_cord)
  return repeated_cord_.Get(index);
}
inline ::std::string* TestAllTypes::mutable_repeated_cord(int index) {
  // @@protoc_insertion_point(field_mutable:protobuf_unittest_no_arena.TestAllTypes.repeated_cord)
  return repeated_cord_.Mutable(index);
}
inline void TestAllTypes::set_repeated_cord(int index, const ::std::string& value) {
  // @@protoc_insertion_point(field_set:protobuf_unittest_no_arena.TestAllTypes.repeated_cord)
  repeated_cord_.Mutable(index)->assign(value);
}
inline void TestAllTypes::set_repeated_cord(int index, const char* value) {
  repeated_cord_.Mutable(index)->assign(value);
  // @@protoc_insertion_point(field_set_char:protobuf_unittest_no_arena.TestAllTypes.repeated_cord)
}
inline void TestAllTypes::set_repeated_cord(int index, const char* value, size_t size) {
  repeated_cord_.Mutable(index)->assign(
    reinterpret_cast<const char*>(value), size);
  // @@protoc_insertion_point(field_set_pointer:protobuf_unittest_no_arena.TestAllTypes.repeated_cord)
}
inline ::std::string* TestAllTypes::add_repeated_cord() {
  // @@protoc_insertion_point(field_add_mutable:protobuf_unittest_no_arena.TestAllTypes.repeated_cord)
  return repeated_cord_.Add();
}
inline void TestAllTypes::add_repeated_cord(const ::std::string& value) {
  repeated_cord_.Add()->assign(value);
  // @@protoc_insertion_point(field_add:protobuf_unittest_no_arena.TestAllTypes.repeated_cord)
}
inline void TestAllTypes::add_repeated_cord(const char* value) {
  repeated_cord_.Add()->assign(value);
  // @@protoc_insertion_point(field_add_char:protobuf_unittest_no_arena.TestAllTypes.repeated_cord)
}
inline void TestAllTypes::add_repeated_cord(const char* value, size_t size) {
  repeated_cord_.Add()->assign(reinterpret_cast<const char*>(value), size);
  // @@protoc_insertion_point(field_add_pointer:protobuf_unittest_no_arena.TestAllTypes.repeated_cord)
}
inline const ::google::protobuf::RepeatedPtrField< ::std::string>&
TestAllTypes::repeated_cord() const {
  // @@protoc_insertion_point(field_list:protobuf_unittest_no_arena.TestAllTypes.repeated_cord)
  return repeated_cord_;
}
inline ::google::protobuf::RepeatedPtrField< ::std::string>*
TestAllTypes::mutable_repeated_cord() {
  // @@protoc_insertion_point(field_mutable_list:protobuf_unittest_no_arena.TestAllTypes.repeated_cord)
  return &repeated_cord_;
}

// repeated .protobuf_unittest_no_arena.TestAllTypes.NestedMessage repeated_lazy_message = 57 [lazy = true];
inline int TestAllTypes::repeated_lazy_message_size() const {
  return repeated_lazy_message_.size();
}
inline void TestAllTypes::clear_repeated_lazy_message() {
  repeated_lazy_message_.Clear();
}
inline const ::protobuf_unittest_no_arena::TestAllTypes_NestedMessage& TestAllTypes::repeated_lazy_message(int index) const {
  // @@protoc_insertion_point(field_get:protobuf_unittest_no_arena.TestAllTypes.repeated_lazy_message)
  return repeated_lazy_message_.Get(index);
}
inline ::protobuf_unittest_no_arena::TestAllTypes_NestedMessage* TestAllTypes::mutable_repeated_lazy_message(int index) {
  // @@protoc_insertion_point(field_mutable:protobuf_unittest_no_arena.TestAllTypes.repeated_lazy_message)
  return repeated_lazy_message_.Mutable(index);
}
inline ::protobuf_unittest_no_arena::TestAllTypes_NestedMessage* TestAllTypes::add_repeated_lazy_message() {
  // @@protoc_insertion_point(field_add:protobuf_unittest_no_arena.TestAllTypes.repeated_lazy_message)
  return repeated_lazy_message_.Add();
}
inline ::google::protobuf::RepeatedPtrField< ::protobuf_unittest_no_arena::TestAllTypes_NestedMessage >*
TestAllTypes::mutable_repeated_lazy_message() {
  // @@protoc_insertion_point(field_mutable_list:protobuf_unittest_no_arena.TestAllTypes.repeated_lazy_message)
  return &repeated_lazy_message_;
}
inline const ::google::protobuf::RepeatedPtrField< ::protobuf_unittest_no_arena::TestAllTypes_NestedMessage >&
TestAllTypes::repeated_lazy_message() const {
  // @@protoc_insertion_point(field_list:protobuf_unittest_no_arena.TestAllTypes.repeated_lazy_message)
  return repeated_lazy_message_;
}

// optional int32 default_int32 = 61 [default = 41];
inline bool TestAllTypes::has_default_int32() const {
  return (_has_bits_[1] & 0x00080000u) != 0;
}
inline void TestAllTypes::set_has_default_int32() {
  _has_bits_[1] |= 0x00080000u;
}
inline void TestAllTypes::clear_has_default_int32() {
  _has_bits_[1] &= ~0x00080000u;
}
inline void TestAllTypes::clear_default_int32() {
  default_int32_ = 41;
  clear_has_default_int32();
}
inline ::google::protobuf::int32 TestAllTypes::default_int32() const {
  // @@protoc_insertion_point(field_get:protobuf_unittest_no_arena.TestAllTypes.default_int32)
  return default_int32_;
}
inline void TestAllTypes::set_default_int32(::google::protobuf::int32 value) {
  set_has_default_int32();
  default_int32_ = value;
  // @@protoc_insertion_point(field_set:protobuf_unittest_no_arena.TestAllTypes.default_int32)
}

// optional int64 default_int64 = 62 [default = 42];
inline bool TestAllTypes::has_default_int64() const {
  return (_has_bits_[1] & 0x00100000u) != 0;
}
inline void TestAllTypes::set_has_default_int64() {
  _has_bits_[1] |= 0x00100000u;
}
inline void TestAllTypes::clear_has_default_int64() {
  _has_bits_[1] &= ~0x00100000u;
}
inline void TestAllTypes::clear_default_int64() {
  default_int64_ = GOOGLE_LONGLONG(42);
  clear_has_default_int64();
}
inline ::google::protobuf::int64 TestAllTypes::default_int64() const {
  // @@protoc_insertion_point(field_get:protobuf_unittest_no_arena.TestAllTypes.default_int64)
  return default_int64_;
}
inline void TestAllTypes::set_default_int64(::google::protobuf::int64 value) {
  set_has_default_int64();
  default_int64_ = value;
  // @@protoc_insertion_point(field_set:protobuf_unittest_no_arena.TestAllTypes.default_int64)
}

// optional uint32 default_uint32 = 63 [default = 43];
inline bool TestAllTypes::has_default_uint32() const {
  return (_has_bits_[1] & 0x00200000u) != 0;
}
inline void TestAllTypes::set_has_default_uint32() {
  _has_bits_[1] |= 0x00200000u;
}
inline void TestAllTypes::clear_has_default_uint32() {
  _has_bits_[1] &= ~0x00200000u;
}
inline void TestAllTypes::clear_default_uint32() {
  default_uint32_ = 43u;
  clear_has_default_uint32();
}
inline ::google::protobuf::uint32 TestAllTypes::default_uint32() const {
  // @@protoc_insertion_point(field_get:protobuf_unittest_no_arena.TestAllTypes.default_uint32)
  return default_uint32_;
}
inline void TestAllTypes::set_default_uint32(::google::protobuf::uint32 value) {
  set_has_default_uint32();
  default_uint32_ = value;
  // @@protoc_insertion_point(field_set:protobuf_unittest_no_arena.TestAllTypes.default_uint32)
}

// optional uint64 default_uint64 = 64 [default = 44];
inline bool TestAllTypes::has_default_uint64() const {
  return (_has_bits_[1] & 0x00400000u) != 0;
}
inline void TestAllTypes::set_has_default_uint64() {
  _has_bits_[1] |= 0x00400000u;
}
inline void TestAllTypes::clear_has_default_uint64() {
  _has_bits_[1] &= ~0x00400000u;
}
inline void TestAllTypes::clear_default_uint64() {
  default_uint64_ = GOOGLE_ULONGLONG(44);
  clear_has_default_uint64();
}
inline ::google::protobuf::uint64 TestAllTypes::default_uint64() const {
  // @@protoc_insertion_point(field_get:protobuf_unittest_no_arena.TestAllTypes.default_uint64)
  return default_uint64_;
}
inline void TestAllTypes::set_default_uint64(::google::protobuf::uint64 value) {
  set_has_default_uint64();
  default_uint64_ = value;
  // @@protoc_insertion_point(field_set:protobuf_unittest_no_arena.TestAllTypes.default_uint64)
}

// optional sint32 default_sint32 = 65 [default = -45];
inline bool TestAllTypes::has_default_sint32() const {
  return (_has_bits_[1] & 0x00800000u) != 0;
}
inline void TestAllTypes::set_has_default_sint32() {
  _has_bits_[1] |= 0x00800000u;
}
inline void TestAllTypes::clear_has_default_sint32() {
  _has_bits_[1] &= ~0x00800000u;
}
inline void TestAllTypes::clear_default_sint32() {
  default_sint32_ = -45;
  clear_has_default_sint32();
}
inline ::google::protobuf::int32 TestAllTypes::default_sint32() const {
  // @@protoc_insertion_point(field_get:protobuf_unittest_no_arena.TestAllTypes.default_sint32)
  return default_sint32_;
}
inline void TestAllTypes::set_default_sint32(::google::protobuf::int32 value) {
  set_has_default_sint32();
  default_sint32_ = value;
  // @@protoc_insertion_point(field_set:protobuf_unittest_no_arena.TestAllTypes.default_sint32)
}

// optional sint64 default_sint64 = 66 [default = 46];
inline bool TestAllTypes::has_default_sint64() const {
  return (_has_bits_[1] & 0x01000000u) != 0;
}
inline void TestAllTypes::set_has_default_sint64() {
  _has_bits_[1] |= 0x01000000u;
}
inline void TestAllTypes::clear_has_default_sint64() {
  _has_bits_[1] &= ~0x01000000u;
}
inline void TestAllTypes::clear_default_sint64() {
  default_sint64_ = GOOGLE_LONGLONG(46);
  clear_has_default_sint64();
}
inline ::google::protobuf::int64 TestAllTypes::default_sint64() const {
  // @@protoc_insertion_point(field_get:protobuf_unittest_no_arena.TestAllTypes.default_sint64)
  return default_sint64_;
}
inline void TestAllTypes::set_default_sint64(::google::protobuf::int64 value) {
  set_has_default_sint64();
  default_sint64_ = value;
  // @@protoc_insertion_point(field_set:protobuf_unittest_no_arena.TestAllTypes.default_sint64)
}

// optional fixed32 default_fixed32 = 67 [default = 47];
inline bool TestAllTypes::has_default_fixed32() const {
  return (_has_bits_[1] & 0x02000000u) != 0;
}
inline void TestAllTypes::set_has_default_fixed32() {
  _has_bits_[1] |= 0x02000000u;
}
inline void TestAllTypes::clear_has_default_fixed32() {
  _has_bits_[1] &= ~0x02000000u;
}
inline void TestAllTypes::clear_default_fixed32() {
  default_fixed32_ = 47u;
  clear_has_default_fixed32();
}
inline ::google::protobuf::uint32 TestAllTypes::default_fixed32() const {
  // @@protoc_insertion_point(field_get:protobuf_unittest_no_arena.TestAllTypes.default_fixed32)
  return default_fixed32_;
}
inline void TestAllTypes::set_default_fixed32(::google::protobuf::uint32 value) {
  set_has_default_fixed32();
  default_fixed32_ = value;
  // @@protoc_insertion_point(field_set:protobuf_unittest_no_arena.TestAllTypes.default_fixed32)
}

// optional fixed64 default_fixed64 = 68 [default = 48];
inline bool TestAllTypes::has_default_fixed64() const {
  return (_has_bits_[1] & 0x04000000u) != 0;
}
inline void TestAllTypes::set_has_default_fixed64() {
  _has_bits_[1] |= 0x04000000u;
}
inline void TestAllTypes::clear_has_default_fixed64() {
  _has_bits_[1] &= ~0x04000000u;
}
inline void TestAllTypes::clear_default_fixed64() {
  default_fixed64_ = GOOGLE_ULONGLONG(48);
  clear_has_default_fixed64();
}
inline ::google::protobuf::uint64 TestAllTypes::default_fixed64() const {
  // @@protoc_insertion_point(field_get:protobuf_unittest_no_arena.TestAllTypes.default_fixed64)
  return default_fixed64_;
}
inline void TestAllTypes::set_default_fixed64(::google::protobuf::uint64 value) {
  set_has_default_fixed64();
  default_fixed64_ = value;
  // @@protoc_insertion_point(field_set:protobuf_unittest_no_arena.TestAllTypes.default_fixed64)
}

// optional sfixed32 default_sfixed32 = 69 [default = 49];
inline bool TestAllTypes::has_default_sfixed32() const {
  return (_has_bits_[1] & 0x08000000u) != 0;
}
inline void TestAllTypes::set_has_default_sfixed32() {
  _has_bits_[1] |= 0x08000000u;
}
inline void TestAllTypes::clear_has_default_sfixed32() {
  _has_bits_[1] &= ~0x08000000u;
}
inline void TestAllTypes::clear_default_sfixed32() {
  default_sfixed32_ = 49;
  clear_has_default_sfixed32();
}
inline ::google::protobuf::int32 TestAllTypes::default_sfixed32() const {
  // @@protoc_insertion_point(field_get:protobuf_unittest_no_arena.TestAllTypes.default_sfixed32)
  return default_sfixed32_;
}
inline void TestAllTypes::set_default_sfixed32(::google::protobuf::int32 value) {
  set_has_default_sfixed32();
  default_sfixed32_ = value;
  // @@protoc_insertion_point(field_set:protobuf_unittest_no_arena.TestAllTypes.default_sfixed32)
}

// optional sfixed64 default_sfixed64 = 70 [default = -50];
inline bool TestAllTypes::has_default_sfixed64() const {
  return (_has_bits_[1] & 0x10000000u) != 0;
}
inline void TestAllTypes::set_has_default_sfixed64() {
  _has_bits_[1] |= 0x10000000u;
}
inline void TestAllTypes::clear_has_default_sfixed64() {
  _has_bits_[1] &= ~0x10000000u;
}
inline void TestAllTypes::clear_default_sfixed64() {
  default_sfixed64_ = GOOGLE_LONGLONG(-50);
  clear_has_default_sfixed64();
}
inline ::google::protobuf::int64 TestAllTypes::default_sfixed64() const {
  // @@protoc_insertion_point(field_get:protobuf_unittest_no_arena.TestAllTypes.default_sfixed64)
  return default_sfixed64_;
}
inline void TestAllTypes::set_default_sfixed64(::google::protobuf::int64 value) {
  set_has_default_sfixed64();
  default_sfixed64_ = value;
  // @@protoc_insertion_point(field_set:protobuf_unittest_no_arena.TestAllTypes.default_sfixed64)
}

// optional float default_float = 71 [default = 51.5];
inline bool TestAllTypes::has_default_float() const {
  return (_has_bits_[1] & 0x20000000u) != 0;
}
inline void TestAllTypes::set_has_default_float() {
  _has_bits_[1] |= 0x20000000u;
}
inline void TestAllTypes::clear_has_default_float() {
  _has_bits_[1] &= ~0x20000000u;
}
inline void TestAllTypes::clear_default_float() {
  default_float_ = 51.5f;
  clear_has_default_float();
}
inline float TestAllTypes::default_float() const {
  // @@protoc_insertion_point(field_get:protobuf_unittest_no_arena.TestAllTypes.default_float)
  return default_float_;
}
inline void TestAllTypes::set_default_float(float value) {
  set_has_default_float();
  default_float_ = value;
  // @@protoc_insertion_point(field_set:protobuf_unittest_no_arena.TestAllTypes.default_float)
}

// optional double default_double = 72 [default = 52000];
inline bool TestAllTypes::has_default_double() const {
  return (_has_bits_[1] & 0x40000000u) != 0;
}
inline void TestAllTypes::set_has_default_double() {
  _has_bits_[1] |= 0x40000000u;
}
inline void TestAllTypes::clear_has_default_double() {
  _has_bits_[1] &= ~0x40000000u;
}
inline void TestAllTypes::clear_default_double() {
  default_double_ = 52000;
  clear_has_default_double();
}
inline double TestAllTypes::default_double() const {
  // @@protoc_insertion_point(field_get:protobuf_unittest_no_arena.TestAllTypes.default_double)
  return default_double_;
}
inline void TestAllTypes::set_default_double(double value) {
  set_has_default_double();
  default_double_ = value;
  // @@protoc_insertion_point(field_set:protobuf_unittest_no_arena.TestAllTypes.default_double)
}

// optional bool default_bool = 73 [default = true];
inline bool TestAllTypes::has_default_bool() const {
  return (_has_bits_[1] & 0x80000000u) != 0;
}
inline void TestAllTypes::set_has_default_bool() {
  _has_bits_[1] |= 0x80000000u;
}
inline void TestAllTypes::clear_has_default_bool() {
  _has_bits_[1] &= ~0x80000000u;
}
inline void TestAllTypes::clear_default_bool() {
  default_bool_ = true;
  clear_has_default_bool();
}
inline bool TestAllTypes::default_bool() const {
  // @@protoc_insertion_point(field_get:protobuf_unittest_no_arena.TestAllTypes.default_bool)
  return default_bool_;
}
inline void TestAllTypes::set_default_bool(bool value) {
  set_has_default_bool();
  default_bool_ = value;
  // @@protoc_insertion_point(field_set:protobuf_unittest_no_arena.TestAllTypes.default_bool)
}

// optional string default_string = 74 [default = "hello"];
inline bool TestAllTypes::has_default_string() const {
  return (_has_bits_[2] & 0x00000001u) != 0;
}
inline void TestAllTypes::set_has_default_string() {
  _has_bits_[2] |= 0x00000001u;
}
inline void TestAllTypes::clear_has_default_string() {
  _has_bits_[2] &= ~0x00000001u;
}
inline void TestAllTypes::clear_default_string() {
  default_string_.ClearToDefaultNoArena(_default_default_string_);
  clear_has_default_string();
}
inline const ::std::string& TestAllTypes::default_string() const {
  // @@protoc_insertion_point(field_get:protobuf_unittest_no_arena.TestAllTypes.default_string)
  return default_string_.GetNoArena(_default_default_string_);
}
inline void TestAllTypes::set_default_string(const ::std::string& value) {
  set_has_default_string();
  default_string_.SetNoArena(_default_default_string_, value);
  // @@protoc_insertion_point(field_set:protobuf_unittest_no_arena.TestAllTypes.default_string)
}
inline void TestAllTypes::set_default_string(const char* value) {
  set_has_default_string();
  default_string_.SetNoArena(_default_default_string_, ::std::string(value));
  // @@protoc_insertion_point(field_set_char:protobuf_unittest_no_arena.TestAllTypes.default_string)
}
inline void TestAllTypes::set_default_string(const char* value, size_t size) {
  set_has_default_string();
  default_string_.SetNoArena(_default_default_string_,
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:protobuf_unittest_no_arena.TestAllTypes.default_string)
}
inline ::std::string* TestAllTypes::mutable_default_string() {
  set_has_default_string();
  // @@protoc_insertion_point(field_mutable:protobuf_unittest_no_arena.TestAllTypes.default_string)
  return default_string_.MutableNoArena(_default_default_string_);
}
inline ::std::string* TestAllTypes::release_default_string() {
  // @@protoc_insertion_point(field_release:protobuf_unittest_no_arena.TestAllTypes.default_string)
  clear_has_default_string();
  return default_string_.ReleaseNoArena(_default_default_string_);
}
inline void TestAllTypes::set_allocated_default_string(::std::string* default_string) {
  if (default_string != NULL) {
    set_has_default_string();
  } else {
    clear_has_default_string();
  }
  default_string_.SetAllocatedNoArena(_default_default_string_, default_string);
  // @@protoc_insertion_point(field_set_allocated:protobuf_unittest_no_arena.TestAllTypes.default_string)
}

// optional bytes default_bytes = 75 [default = "world"];
inline bool TestAllTypes::has_default_bytes() const {
  return (_has_bits_[2] & 0x00000002u) != 0;
}
inline void TestAllTypes::set_has_default_bytes() {
  _has_bits_[2] |= 0x00000002u;
}
inline void TestAllTypes::clear_has_default_bytes() {
  _has_bits_[2] &= ~0x00000002u;
}
inline void TestAllTypes::clear_default_bytes() {
  default_bytes_.ClearToDefaultNoArena(_default_default_bytes_);
  clear_has_default_bytes();
}
inline const ::std::string& TestAllTypes::default_bytes() const {
  // @@protoc_insertion_point(field_get:protobuf_unittest_no_arena.TestAllTypes.default_bytes)
  return default_bytes_.GetNoArena(_default_default_bytes_);
}
inline void TestAllTypes::set_default_bytes(const ::std::string& value) {
  set_has_default_bytes();
  default_bytes_.SetNoArena(_default_default_bytes_, value);
  // @@protoc_insertion_point(field_set:protobuf_unittest_no_arena.TestAllTypes.default_bytes)
}
inline void TestAllTypes::set_default_bytes(const char* value) {
  set_has_default_bytes();
  default_bytes_.SetNoArena(_default_default_bytes_, ::std::string(value));
  // @@protoc_insertion_point(field_set_char:protobuf_unittest_no_arena.TestAllTypes.default_bytes)
}
inline void TestAllTypes::set_default_bytes(const void* value, size_t size) {
  set_has_default_bytes();
  default_bytes_.SetNoArena(_default_default_bytes_,
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:protobuf_unittest_no_arena.TestAllTypes.default_bytes)
}
inline ::std::string* TestAllTypes::mutable_default_bytes() {
  set_has_default_bytes();
  // @@protoc_insertion_point(field_mutable:protobuf_unittest_no_arena.TestAllTypes.default_bytes)
  return default_bytes_.MutableNoArena(_default_default_bytes_);
}
inline ::std::string* TestAllTypes::release_default_bytes() {
  // @@protoc_insertion_point(field_release:protobuf_unittest_no_arena.TestAllTypes.default_bytes)
  clear_has_default_bytes();
  return default_bytes_.ReleaseNoArena(_default_default_bytes_);
}
inline void TestAllTypes::set_allocated_default_bytes(::std::string* default_bytes) {
  if (default_bytes != NULL) {
    set_has_default_bytes();
  } else {
    clear_has_default_bytes();
  }
  default_bytes_.SetAllocatedNoArena(_default_default_bytes_, default_bytes);
  // @@protoc_insertion_point(field_set_allocated:protobuf_unittest_no_arena.TestAllTypes.default_bytes)
}

// optional .protobuf_unittest_no_arena.TestAllTypes.NestedEnum default_nested_enum = 81 [default = BAR];
inline bool TestAllTypes::has_default_nested_enum() const {
  return (_has_bits_[2] & 0x00000004u) != 0;
}
inline void TestAllTypes::set_has_default_nested_enum() {
  _has_bits_[2] |= 0x00000004u;
}
inline void TestAllTypes::clear_has_default_nested_enum() {
  _has_bits_[2] &= ~0x00000004u;
}
inline void TestAllTypes::clear_default_nested_enum() {
  default_nested_enum_ = 2;
  clear_has_default_nested_enum();
}
inline ::protobuf_unittest_no_arena::TestAllTypes_NestedEnum TestAllTypes::default_nested_enum() const {
  // @@protoc_insertion_point(field_get:protobuf_unittest_no_arena.TestAllTypes.default_nested_enum)
  return static_cast< ::protobuf_unittest_no_arena::TestAllTypes_NestedEnum >(default_nested_enum_);
}
inline void TestAllTypes::set_default_nested_enum(::protobuf_unittest_no_arena::TestAllTypes_NestedEnum value) {
  assert(::protobuf_unittest_no_arena::TestAllTypes_NestedEnum_IsValid(value));
  set_has_default_nested_enum();
  default_nested_enum_ = value;
  // @@protoc_insertion_point(field_set:protobuf_unittest_no_arena.TestAllTypes.default_nested_enum)
}

// optional .protobuf_unittest_no_arena.ForeignEnum default_foreign_enum = 82 [default = FOREIGN_BAR];
inline bool TestAllTypes::has_default_foreign_enum() const {
  return (_has_bits_[2] & 0x00000008u) != 0;
}
inline void TestAllTypes::set_has_default_foreign_enum() {
  _has_bits_[2] |= 0x00000008u;
}
inline void TestAllTypes::clear_has_default_foreign_enum() {
  _has_bits_[2] &= ~0x00000008u;
}
inline void TestAllTypes::clear_default_foreign_enum() {
  default_foreign_enum_ = 5;
  clear_has_default_foreign_enum();
}
inline ::protobuf_unittest_no_arena::ForeignEnum TestAllTypes::default_foreign_enum() const {
  // @@protoc_insertion_point(field_get:protobuf_unittest_no_arena.TestAllTypes.default_foreign_enum)
  return static_cast< ::protobuf_unittest_no_arena::ForeignEnum >(default_foreign_enum_);
}
inline void TestAllTypes::set_default_foreign_enum(::protobuf_unittest_no_arena::ForeignEnum value) {
  assert(::protobuf_unittest_no_arena::ForeignEnum_IsValid(value));
  set_has_default_foreign_enum();
  default_foreign_enum_ = value;
  // @@protoc_insertion_point(field_set:protobuf_unittest_no_arena.TestAllTypes.default_foreign_enum)
}

// optional .protobuf_unittest_import.ImportEnum default_import_enum = 83 [default = IMPORT_BAR];
inline bool TestAllTypes::has_default_import_enum() const {
  return (_has_bits_[2] & 0x00000010u) != 0;
}
inline void TestAllTypes::set_has_default_import_enum() {
  _has_bits_[2] |= 0x00000010u;
}
inline void TestAllTypes::clear_has_default_import_enum() {
  _has_bits_[2] &= ~0x00000010u;
}
inline void TestAllTypes::clear_default_import_enum() {
  default_import_enum_ = 8;
  clear_has_default_import_enum();
}
inline ::protobuf_unittest_import::ImportEnum TestAllTypes::default_import_enum() const {
  // @@protoc_insertion_point(field_get:protobuf_unittest_no_arena.TestAllTypes.default_import_enum)
  return static_cast< ::protobuf_unittest_import::ImportEnum >(default_import_enum_);
}
inline void TestAllTypes::set_default_import_enum(::protobuf_unittest_import::ImportEnum value) {
  assert(::protobuf_unittest_import::ImportEnum_IsValid(value));
  set_has_default_import_enum();
  default_import_enum_ = value;
  // @@protoc_insertion_point(field_set:protobuf_unittest_no_arena.TestAllTypes.default_import_enum)
}

// optional string default_string_piece = 84 [default = "abc", ctype = STRING_PIECE];
inline bool TestAllTypes::has_default_string_piece() const {
  return (_has_bits_[2] & 0x00000020u) != 0;
}
inline void TestAllTypes::set_has_default_string_piece() {
  _has_bits_[2] |= 0x00000020u;
}
inline void TestAllTypes::clear_has_default_string_piece() {
  _has_bits_[2] &= ~0x00000020u;
}
inline void TestAllTypes::clear_default_string_piece() {
  default_string_piece_.ClearToDefaultNoArena(_default_default_string_piece_);
  clear_has_default_string_piece();
}
inline const ::std::string& TestAllTypes::default_string_piece() const {
  // @@protoc_insertion_point(field_get:protobuf_unittest_no_arena.TestAllTypes.default_string_piece)
  return default_string_piece_.GetNoArena(_default_default_string_piece_);
}
inline void TestAllTypes::set_default_string_piece(const ::std::string& value) {
  set_has_default_string_piece();
  default_string_piece_.SetNoArena(_default_default_string_piece_, value);
  // @@protoc_insertion_point(field_set:protobuf_unittest_no_arena.TestAllTypes.default_string_piece)
}
inline void TestAllTypes::set_default_string_piece(const char* value) {
  set_has_default_string_piece();
  default_string_piece_.SetNoArena(_default_default_string_piece_, ::std::string(value));
  // @@protoc_insertion_point(field_set_char:protobuf_unittest_no_arena.TestAllTypes.default_string_piece)
}
inline void TestAllTypes::set_default_string_piece(const char* value, size_t size) {
  set_has_default_string_piece();
  default_string_piece_.SetNoArena(_default_default_string_piece_,
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:protobuf_unittest_no_arena.TestAllTypes.default_string_piece)
}
inline ::std::string* TestAllTypes::mutable_default_string_piece() {
  set_has_default_string_piece();
  // @@protoc_insertion_point(field_mutable:protobuf_unittest_no_arena.TestAllTypes.default_string_piece)
  return default_string_piece_.MutableNoArena(_default_default_string_piece_);
}
inline ::std::string* TestAllTypes::release_default_string_piece() {
  // @@protoc_insertion_point(field_release:protobuf_unittest_no_arena.TestAllTypes.default_string_piece)
  clear_has_default_string_piece();
  return default_string_piece_.ReleaseNoArena(_default_default_string_piece_);
}
inline void TestAllTypes::set_allocated_default_string_piece(::std::string* default_string_piece) {
  if (default_string_piece != NULL) {
    set_has_default_string_piece();
  } else {
    clear_has_default_string_piece();
  }
  default_string_piece_.SetAllocatedNoArena(_default_default_string_piece_, default_string_piece);
  // @@protoc_insertion_point(field_set_allocated:protobuf_unittest_no_arena.TestAllTypes.default_string_piece)
}

// optional string default_cord = 85 [default = "123", ctype = CORD];
inline bool TestAllTypes::has_default_cord() const {
  return (_has_bits_[2] & 0x00000040u) != 0;
}
inline void TestAllTypes::set_has_default_cord() {
  _has_bits_[2] |= 0x00000040u;
}
inline void TestAllTypes::clear_has_default_cord() {
  _has_bits_[2] &= ~0x00000040u;
}
inline void TestAllTypes::clear_default_cord() {
  default_cord_.ClearToDefaultNoArena(_default_default_cord_);
  clear_has_default_cord();
}
inline const ::std::string& TestAllTypes::default_cord() const {
  // @@protoc_insertion_point(field_get:protobuf_unittest_no_arena.TestAllTypes.default_cord)
  return default_cord_.GetNoArena(_default_default_cord_);
}
inline void TestAllTypes::set_default_cord(const ::std::string& value) {
  set_has_default_cord();
  default_cord_.SetNoArena(_default_default_cord_, value);
  // @@protoc_insertion_point(field_set:protobuf_unittest_no_arena.TestAllTypes.default_cord)
}
inline void TestAllTypes::set_default_cord(const char* value) {
  set_has_default_cord();
  default_cord_.SetNoArena(_default_default_cord_, ::std::string(value));
  // @@protoc_insertion_point(field_set_char:protobuf_unittest_no_arena.TestAllTypes.default_cord)
}
inline void TestAllTypes::set_default_cord(const char* value, size_t size) {
  set_has_default_cord();
  default_cord_.SetNoArena(_default_default_cord_,
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:protobuf_unittest_no_arena.TestAllTypes.default_cord)
}
inline ::std::string* TestAllTypes::mutable_default_cord() {
  set_has_default_cord();
  // @@protoc_insertion_point(field_mutable:protobuf_unittest_no_arena.TestAllTypes.default_cord)
  return default_cord_.MutableNoArena(_default_default_cord_);
}
inline ::std::string* TestAllTypes::release_default_cord() {
  // @@protoc_insertion_point(field_release:protobuf_unittest_no_arena.TestAllTypes.default_cord)
  clear_has_default_cord();
  return default_cord_.ReleaseNoArena(_default_default_cord_);
}
inline void TestAllTypes::set_allocated_default_cord(::std::string* default_cord) {
  if (default_cord != NULL) {
    set_has_default_cord();
  } else {
    clear_has_default_cord();
  }
  default_cord_.SetAllocatedNoArena(_default_default_cord_, default_cord);
  // @@protoc_insertion_point(field_set_allocated:protobuf_unittest_no_arena.TestAllTypes.default_cord)
}

// optional uint32 oneof_uint32 = 111;
inline bool TestAllTypes::has_oneof_uint32() const {
  return oneof_field_case() == kOneofUint32;
}
inline void TestAllTypes::set_has_oneof_uint32() {
  _oneof_case_[0] = kOneofUint32;
}
inline void TestAllTypes::clear_oneof_uint32() {
  if (has_oneof_uint32()) {
    oneof_field_.oneof_uint32_ = 0u;
    clear_has_oneof_field();
  }
}
inline ::google::protobuf::uint32 TestAllTypes::oneof_uint32() const {
  // @@protoc_insertion_point(field_get:protobuf_unittest_no_arena.TestAllTypes.oneof_uint32)
  if (has_oneof_uint32()) {
    return oneof_field_.oneof_uint32_;
  }
  return 0u;
}
inline void TestAllTypes::set_oneof_uint32(::google::protobuf::uint32 value) {
  if (!has_oneof_uint32()) {
    clear_oneof_field();
    set_has_oneof_uint32();
  }
  oneof_field_.oneof_uint32_ = value;
  // @@protoc_insertion_point(field_set:protobuf_unittest_no_arena.TestAllTypes.oneof_uint32)
}

// optional .protobuf_unittest_no_arena.TestAllTypes.NestedMessage oneof_nested_message = 112;
inline bool TestAllTypes::has_oneof_nested_message() const {
  return oneof_field_case() == kOneofNestedMessage;
}
inline void TestAllTypes::set_has_oneof_nested_message() {
  _oneof_case_[0] = kOneofNestedMessage;
}
inline void TestAllTypes::clear_oneof_nested_message() {
  if (has_oneof_nested_message()) {
    delete oneof_field_.oneof_nested_message_;
    clear_has_oneof_field();
  }
}
inline  const ::protobuf_unittest_no_arena::TestAllTypes_NestedMessage& TestAllTypes::oneof_nested_message() const {
  // @@protoc_insertion_point(field_get:protobuf_unittest_no_arena.TestAllTypes.oneof_nested_message)
  return has_oneof_nested_message()
      ? *oneof_field_.oneof_nested_message_
      : ::protobuf_unittest_no_arena::TestAllTypes_NestedMessage::default_instance();
}
inline ::protobuf_unittest_no_arena::TestAllTypes_NestedMessage* TestAllTypes::mutable_oneof_nested_message() {
  if (!has_oneof_nested_message()) {
    clear_oneof_field();
    set_has_oneof_nested_message();
    oneof_field_.oneof_nested_message_ = new ::protobuf_unittest_no_arena::TestAllTypes_NestedMessage;
  }
  // @@protoc_insertion_point(field_mutable:protobuf_unittest_no_arena.TestAllTypes.oneof_nested_message)
  return oneof_field_.oneof_nested_message_;
}
inline ::protobuf_unittest_no_arena::TestAllTypes_NestedMessage* TestAllTypes::release_oneof_nested_message() {
  // @@protoc_insertion_point(field_release:protobuf_unittest_no_arena.TestAllTypes.oneof_nested_message)
  if (has_oneof_nested_message()) {
    clear_has_oneof_field();
    ::protobuf_unittest_no_arena::TestAllTypes_NestedMessage* temp = oneof_field_.oneof_nested_message_;
    oneof_field_.oneof_nested_message_ = NULL;
    return temp;
  } else {
    return NULL;
  }
}
inline void TestAllTypes::set_allocated_oneof_nested_message(::protobuf_unittest_no_arena::TestAllTypes_NestedMessage* oneof_nested_message) {
  clear_oneof_field();
  if (oneof_nested_message) {
    set_has_oneof_nested_message();
    oneof_field_.oneof_nested_message_ = oneof_nested_message;
  }
  // @@protoc_insertion_point(field_set_allocated:protobuf_unittest_no_arena.TestAllTypes.oneof_nested_message)
}

// optional string oneof_string = 113;
inline bool TestAllTypes::has_oneof_string() const {
  return oneof_field_case() == kOneofString;
}
inline void TestAllTypes::set_has_oneof_string() {
  _oneof_case_[0] = kOneofString;
}
inline void TestAllTypes::clear_oneof_string() {
  if (has_oneof_string()) {
    oneof_field_.oneof_string_.DestroyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
    clear_has_oneof_field();
  }
}
inline const ::std::string& TestAllTypes::oneof_string() const {
  // @@protoc_insertion_point(field_get:protobuf_unittest_no_arena.TestAllTypes.oneof_string)
  if (has_oneof_string()) {
    return oneof_field_.oneof_string_.GetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  }
  return *&::google::protobuf::internal::GetEmptyStringAlreadyInited();
}
inline void TestAllTypes::set_oneof_string(const ::std::string& value) {
  // @@protoc_insertion_point(field_set:protobuf_unittest_no_arena.TestAllTypes.oneof_string)
  if (!has_oneof_string()) {
    clear_oneof_field();
    set_has_oneof_string();
    oneof_field_.oneof_string_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  }
  oneof_field_.oneof_string_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:protobuf_unittest_no_arena.TestAllTypes.oneof_string)
}
inline void TestAllTypes::set_oneof_string(const char* value) {
  if (!has_oneof_string()) {
    clear_oneof_field();
    set_has_oneof_string();
    oneof_field_.oneof_string_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  }
  oneof_field_.oneof_string_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(value));
  // @@protoc_insertion_point(field_set_char:protobuf_unittest_no_arena.TestAllTypes.oneof_string)
}
inline void TestAllTypes::set_oneof_string(const char* value, size_t size) {
  if (!has_oneof_string()) {
    clear_oneof_field();
    set_has_oneof_string();
    oneof_field_.oneof_string_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  }
  oneof_field_.oneof_string_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(
      reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:protobuf_unittest_no_arena.TestAllTypes.oneof_string)
}
inline ::std::string* TestAllTypes::mutable_oneof_string() {
  if (!has_oneof_string()) {
    clear_oneof_field();
    set_has_oneof_string();
    oneof_field_.oneof_string_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  }
  // @@protoc_insertion_point(field_mutable:protobuf_unittest_no_arena.TestAllTypes.oneof_string)
  return oneof_field_.oneof_string_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline ::std::string* TestAllTypes::release_oneof_string() {
  // @@protoc_insertion_point(field_release:protobuf_unittest_no_arena.TestAllTypes.oneof_string)
  if (has_oneof_string()) {
    clear_has_oneof_field();
    return oneof_field_.oneof_string_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  } else {
    return NULL;
  }
}
inline void TestAllTypes::set_allocated_oneof_string(::std::string* oneof_string) {
  if (!has_oneof_string()) {
    oneof_field_.oneof_string_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  }
  clear_oneof_field();
  if (oneof_string != NULL) {
    set_has_oneof_string();
    oneof_field_.oneof_string_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
        oneof_string);
  }
  // @@protoc_insertion_point(field_set_allocated:protobuf_unittest_no_arena.TestAllTypes.oneof_string)
}

// optional bytes oneof_bytes = 114;
inline bool TestAllTypes::has_oneof_bytes() const {
  return oneof_field_case() == kOneofBytes;
}
inline void TestAllTypes::set_has_oneof_bytes() {
  _oneof_case_[0] = kOneofBytes;
}
inline void TestAllTypes::clear_oneof_bytes() {
  if (has_oneof_bytes()) {
    oneof_field_.oneof_bytes_.DestroyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
    clear_has_oneof_field();
  }
}
inline const ::std::string& TestAllTypes::oneof_bytes() const {
  // @@protoc_insertion_point(field_get:protobuf_unittest_no_arena.TestAllTypes.oneof_bytes)
  if (has_oneof_bytes()) {
    return oneof_field_.oneof_bytes_.GetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  }
  return *&::google::protobuf::internal::GetEmptyStringAlreadyInited();
}
inline void TestAllTypes::set_oneof_bytes(const ::std::string& value) {
  // @@protoc_insertion_point(field_set:protobuf_unittest_no_arena.TestAllTypes.oneof_bytes)
  if (!has_oneof_bytes()) {
    clear_oneof_field();
    set_has_oneof_bytes();
    oneof_field_.oneof_bytes_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  }
  oneof_field_.oneof_bytes_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:protobuf_unittest_no_arena.TestAllTypes.oneof_bytes)
}
inline void TestAllTypes::set_oneof_bytes(const char* value) {
  if (!has_oneof_bytes()) {
    clear_oneof_field();
    set_has_oneof_bytes();
    oneof_field_.oneof_bytes_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  }
  oneof_field_.oneof_bytes_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(value));
  // @@protoc_insertion_point(field_set_char:protobuf_unittest_no_arena.TestAllTypes.oneof_bytes)
}
inline void TestAllTypes::set_oneof_bytes(const void* value, size_t size) {
  if (!has_oneof_bytes()) {
    clear_oneof_field();
    set_has_oneof_bytes();
    oneof_field_.oneof_bytes_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  }
  oneof_field_.oneof_bytes_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(
      reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:protobuf_unittest_no_arena.TestAllTypes.oneof_bytes)
}
inline ::std::string* TestAllTypes::mutable_oneof_bytes() {
  if (!has_oneof_bytes()) {
    clear_oneof_field();
    set_has_oneof_bytes();
    oneof_field_.oneof_bytes_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  }
  // @@protoc_insertion_point(field_mutable:protobuf_unittest_no_arena.TestAllTypes.oneof_bytes)
  return oneof_field_.oneof_bytes_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline ::std::string* TestAllTypes::release_oneof_bytes() {
  // @@protoc_insertion_point(field_release:protobuf_unittest_no_arena.TestAllTypes.oneof_bytes)
  if (has_oneof_bytes()) {
    clear_has_oneof_field();
    return oneof_field_.oneof_bytes_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  } else {
    return NULL;
  }
}
inline void TestAllTypes::set_allocated_oneof_bytes(::std::string* oneof_bytes) {
  if (!has_oneof_bytes()) {
    oneof_field_.oneof_bytes_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  }
  clear_oneof_field();
  if (oneof_bytes != NULL) {
    set_has_oneof_bytes();
    oneof_field_.oneof_bytes_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
        oneof_bytes);
  }
  // @@protoc_insertion_point(field_set_allocated:protobuf_unittest_no_arena.TestAllTypes.oneof_bytes)
}

// optional .protobuf_unittest_no_arena.TestAllTypes.NestedMessage lazy_oneof_nested_message = 115 [lazy = true];
inline bool TestAllTypes::has_lazy_oneof_nested_message() const {
  return oneof_field_case() == kLazyOneofNestedMessage;
}
inline void TestAllTypes::set_has_lazy_oneof_nested_message() {
  _oneof_case_[0] = kLazyOneofNestedMessage;
}
inline void TestAllTypes::clear_lazy_oneof_nested_message() {
  if (has_lazy_oneof_nested_message()) {
    delete oneof_field_.lazy_oneof_nested_message_;
    clear_has_oneof_field();
  }
}
inline  const ::protobuf_unittest_no_arena::TestAllTypes_NestedMessage& TestAllTypes::lazy_oneof_nested_message() const {
  // @@protoc_insertion_point(field_get:protobuf_unittest_no_arena.TestAllTypes.lazy_oneof_nested_message)
  return has_lazy_oneof_nested_message()
      ? *oneof_field_.lazy_oneof_nested_message_
      : ::protobuf_unittest_no_arena::TestAllTypes_NestedMessage::default_instance();
}
inline ::protobuf_unittest_no_arena::TestAllTypes_NestedMessage* TestAllTypes::mutable_lazy_oneof_nested_message() {
  if (!has_lazy_oneof_nested_message()) {
    clear_oneof_field();
    set_has_lazy_oneof_nested_message();
    oneof_field_.lazy_oneof_nested_message_ = new ::protobuf_unittest_no_arena::TestAllTypes_NestedMessage;
  }
  // @@protoc_insertion_point(field_mutable:protobuf_unittest_no_arena.TestAllTypes.lazy_oneof_nested_message)
  return oneof_field_.lazy_oneof_nested_message_;
}
inline ::protobuf_unittest_no_arena::TestAllTypes_NestedMessage* TestAllTypes::release_lazy_oneof_nested_message() {
  // @@protoc_insertion_point(field_release:protobuf_unittest_no_arena.TestAllTypes.lazy_oneof_nested_message)
  if (has_lazy_oneof_nested_message()) {
    clear_has_oneof_field();
    ::protobuf_unittest_no_arena::TestAllTypes_NestedMessage* temp = oneof_field_.lazy_oneof_nested_message_;
    oneof_field_.lazy_oneof_nested_message_ = NULL;
    return temp;
  } else {
    return NULL;
  }
}
inline void TestAllTypes::set_allocated_lazy_oneof_nested_message(::protobuf_unittest_no_arena::TestAllTypes_NestedMessage* lazy_oneof_nested_message) {
  clear_oneof_field();
  if (lazy_oneof_nested_message) {
    set_has_lazy_oneof_nested_message();
    oneof_field_.lazy_oneof_nested_message_ = lazy_oneof_nested_message;
  }
  // @@protoc_insertion_point(field_set_allocated:protobuf_unittest_no_arena.TestAllTypes.lazy_oneof_nested_message)
}

inline bool TestAllTypes::has_oneof_field() const {
  return oneof_field_case() != ONEOF_FIELD_NOT_SET;
}
inline void TestAllTypes::clear_has_oneof_field() {
  _oneof_case_[0] = ONEOF_FIELD_NOT_SET;
}
inline TestAllTypes::OneofFieldCase TestAllTypes::oneof_field_case() const {
  return TestAllTypes::OneofFieldCase(_oneof_case_[0]);
}
inline const TestAllTypes* TestAllTypes::internal_default_instance() {
  return &TestAllTypes_default_instance_.get();
}
// -------------------------------------------------------------------

// ForeignMessage

// optional int32 c = 1;
inline bool ForeignMessage::has_c() const {
  return (_has_bits_[0] & 0x00000001u) != 0;
}
inline void ForeignMessage::set_has_c() {
  _has_bits_[0] |= 0x00000001u;
}
inline void ForeignMessage::clear_has_c() {
  _has_bits_[0] &= ~0x00000001u;
}
inline void ForeignMessage::clear_c() {
  c_ = 0;
  clear_has_c();
}
inline ::google::protobuf::int32 ForeignMessage::c() const {
  // @@protoc_insertion_point(field_get:protobuf_unittest_no_arena.ForeignMessage.c)
  return c_;
}
inline void ForeignMessage::set_c(::google::protobuf::int32 value) {
  set_has_c();
  c_ = value;
  // @@protoc_insertion_point(field_set:protobuf_unittest_no_arena.ForeignMessage.c)
}

inline const ForeignMessage* ForeignMessage::internal_default_instance() {
  return &ForeignMessage_default_instance_.get();
}
// -------------------------------------------------------------------

// TestNoArenaMessage

// optional .proto2_arena_unittest.ArenaMessage arena_message = 1;
inline bool TestNoArenaMessage::has_arena_message() const {
  return (_has_bits_[0] & 0x00000001u) != 0;
}
inline void TestNoArenaMessage::set_has_arena_message() {
  _has_bits_[0] |= 0x00000001u;
}
inline void TestNoArenaMessage::clear_has_arena_message() {
  _has_bits_[0] &= ~0x00000001u;
}
inline void TestNoArenaMessage::clear_arena_message() {
  if (arena_message_ != NULL) arena_message_->::proto2_arena_unittest::ArenaMessage::Clear();
  clear_has_arena_message();
}
inline const ::proto2_arena_unittest::ArenaMessage& TestNoArenaMessage::arena_message() const {
  // @@protoc_insertion_point(field_get:protobuf_unittest_no_arena.TestNoArenaMessage.arena_message)
  return arena_message_ != NULL ? *arena_message_
                         : *::proto2_arena_unittest::ArenaMessage::internal_default_instance();
}
inline ::proto2_arena_unittest::ArenaMessage* TestNoArenaMessage::mutable_arena_message() {
  set_has_arena_message();
  if (arena_message_ == NULL) {
    arena_message_ = new ::proto2_arena_unittest::ArenaMessage;
  }
  // @@protoc_insertion_point(field_mutable:protobuf_unittest_no_arena.TestNoArenaMessage.arena_message)
  return arena_message_;
}
inline ::proto2_arena_unittest::ArenaMessage* TestNoArenaMessage::release_arena_message() {
  // @@protoc_insertion_point(field_release:protobuf_unittest_no_arena.TestNoArenaMessage.arena_message)
  clear_has_arena_message();
  ::proto2_arena_unittest::ArenaMessage* temp = arena_message_;
  arena_message_ = NULL;
  return temp;
}
inline void TestNoArenaMessage::set_allocated_arena_message(::proto2_arena_unittest::ArenaMessage* arena_message) {
  delete arena_message_;
  if (arena_message != NULL && arena_message->GetArena() != NULL) {
    ::proto2_arena_unittest::ArenaMessage* new_arena_message = new ::proto2_arena_unittest::ArenaMessage;
    new_arena_message->CopyFrom(*arena_message);
    arena_message = new_arena_message;
  }
  arena_message_ = arena_message;
  if (arena_message) {
    set_has_arena_message();
  } else {
    clear_has_arena_message();
  }
  // @@protoc_insertion_point(field_set_allocated:protobuf_unittest_no_arena.TestNoArenaMessage.arena_message)
}

inline const TestNoArenaMessage* TestNoArenaMessage::internal_default_instance() {
  return &TestNoArenaMessage_default_instance_.get();
}
#endif  // !PROTOBUF_INLINE_NOT_IN_HEADERS
// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------


// @@protoc_insertion_point(namespace_scope)

}  // namespace protobuf_unittest_no_arena

#ifndef SWIG
namespace google {
namespace protobuf {

template <> struct is_proto_enum< ::protobuf_unittest_no_arena::TestAllTypes_NestedEnum> : ::google::protobuf::internal::true_type {};
template <>
inline const EnumDescriptor* GetEnumDescriptor< ::protobuf_unittest_no_arena::TestAllTypes_NestedEnum>() {
  return ::protobuf_unittest_no_arena::TestAllTypes_NestedEnum_descriptor();
}
template <> struct is_proto_enum< ::protobuf_unittest_no_arena::ForeignEnum> : ::google::protobuf::internal::true_type {};
template <>
inline const EnumDescriptor* GetEnumDescriptor< ::protobuf_unittest_no_arena::ForeignEnum>() {
  return ::protobuf_unittest_no_arena::ForeignEnum_descriptor();
}

}  // namespace protobuf
}  // namespace google
#endif  // SWIG

// @@protoc_insertion_point(global_scope)

#endif  // PROTOBUF_google_2fprotobuf_2funittest_5fno_5farena_2eproto__INCLUDED

// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: google/protobuf/unittest_custom_options.proto

#ifndef PROTOBUF_google_2fprotobuf_2funittest_5fcustom_5foptions_2eproto__INCLUDED
#define PROTOBUF_google_2fprotobuf_2funittest_5fcustom_5foptions_2eproto__INCLUDED

#include <string>

#include <google/protobuf/stubs/common.h>

#if GOOGLE_PROTOBUF_VERSION < 3001000
#error This file was generated by a newer version of protoc which is
#error incompatible with your Protocol Buffer headers.  Please update
#error your headers.
#endif
#if 3001000 < GOOGLE_PROTOBUF_MIN_PROTOC_VERSION
#error This file was generated by an older version of protoc which is
#error incompatible with your Protocol Buffer headers.  Please
#error regenerate this file with a newer version of protoc.
#endif

#include <google/protobuf/arena.h>
#include <google/protobuf/arenastring.h>
#include <google/protobuf/generated_message_util.h>
#include <google/protobuf/metadata.h>
#include <google/protobuf/message.h>
#include <google/protobuf/repeated_field.h>
#include <google/protobuf/extension_set.h>
#include <google/protobuf/generated_enum_reflection.h>
#include <google/protobuf/service.h>
#include <google/protobuf/unknown_field_set.h>
#include <google/protobuf/descriptor.pb.h>
// @@protoc_insertion_point(includes)

namespace protobuf_unittest {

// Internal implementation detail -- do not call these.
void protobuf_AddDesc_google_2fprotobuf_2funittest_5fcustom_5foptions_2eproto();
void protobuf_InitDefaults_google_2fprotobuf_2funittest_5fcustom_5foptions_2eproto();
void protobuf_AssignDesc_google_2fprotobuf_2funittest_5fcustom_5foptions_2eproto();
void protobuf_ShutdownFile_google_2fprotobuf_2funittest_5fcustom_5foptions_2eproto();

class Aggregate;
class AggregateMessage;
class AggregateMessageSet;
class AggregateMessageSetElement;
class ComplexOpt6;
class ComplexOptionType1;
class ComplexOptionType2;
class ComplexOptionType2_ComplexOptionType4;
class ComplexOptionType3;
class ComplexOptionType3_ComplexOptionType5;
class CustomOptionFooClientMessage;
class CustomOptionFooRequest;
class CustomOptionFooResponse;
class CustomOptionFooServerMessage;
class CustomOptionMaxIntegerValues;
class CustomOptionMinIntegerValues;
class CustomOptionOtherValues;
class DummyMessageContainingEnum;
class DummyMessageInvalidAsOptionType;
class NestedOptionType;
class NestedOptionType_NestedMessage;
class NewOptionType;
class OldOptionType;
class SettingRealsFromNegativeInts;
class SettingRealsFromPositiveInts;
class TestMessageWithCustomOptions;
class TestMessageWithRequiredEnumOption;
class VariousComplexOptions;

enum TestMessageWithCustomOptions_AnEnum {
  TestMessageWithCustomOptions_AnEnum_ANENUM_VAL1 = 1,
  TestMessageWithCustomOptions_AnEnum_ANENUM_VAL2 = 2
};
bool TestMessageWithCustomOptions_AnEnum_IsValid(int value);
const TestMessageWithCustomOptions_AnEnum TestMessageWithCustomOptions_AnEnum_AnEnum_MIN = TestMessageWithCustomOptions_AnEnum_ANENUM_VAL1;
const TestMessageWithCustomOptions_AnEnum TestMessageWithCustomOptions_AnEnum_AnEnum_MAX = TestMessageWithCustomOptions_AnEnum_ANENUM_VAL2;
const int TestMessageWithCustomOptions_AnEnum_AnEnum_ARRAYSIZE = TestMessageWithCustomOptions_AnEnum_AnEnum_MAX + 1;

const ::google::protobuf::EnumDescriptor* TestMessageWithCustomOptions_AnEnum_descriptor();
inline const ::std::string& TestMessageWithCustomOptions_AnEnum_Name(TestMessageWithCustomOptions_AnEnum value) {
  return ::google::protobuf::internal::NameOfEnum(
    TestMessageWithCustomOptions_AnEnum_descriptor(), value);
}
inline bool TestMessageWithCustomOptions_AnEnum_Parse(
    const ::std::string& name, TestMessageWithCustomOptions_AnEnum* value) {
  return ::google::protobuf::internal::ParseNamedEnum<TestMessageWithCustomOptions_AnEnum>(
    TestMessageWithCustomOptions_AnEnum_descriptor(), name, value);
}
enum DummyMessageContainingEnum_TestEnumType {
  DummyMessageContainingEnum_TestEnumType_TEST_OPTION_ENUM_TYPE1 = 22,
  DummyMessageContainingEnum_TestEnumType_TEST_OPTION_ENUM_TYPE2 = -23
};
bool DummyMessageContainingEnum_TestEnumType_IsValid(int value);
const DummyMessageContainingEnum_TestEnumType DummyMessageContainingEnum_TestEnumType_TestEnumType_MIN = DummyMessageContainingEnum_TestEnumType_TEST_OPTION_ENUM_TYPE2;
const DummyMessageContainingEnum_TestEnumType DummyMessageContainingEnum_TestEnumType_TestEnumType_MAX = DummyMessageContainingEnum_TestEnumType_TEST_OPTION_ENUM_TYPE1;
const int DummyMessageContainingEnum_TestEnumType_TestEnumType_ARRAYSIZE = DummyMessageContainingEnum_TestEnumType_TestEnumType_MAX + 1;

const ::google::protobuf::EnumDescriptor* DummyMessageContainingEnum_TestEnumType_descriptor();
inline const ::std::string& DummyMessageContainingEnum_TestEnumType_Name(DummyMessageContainingEnum_TestEnumType value) {
  return ::google::protobuf::internal::NameOfEnum(
    DummyMessageContainingEnum_TestEnumType_descriptor(), value);
}
inline bool DummyMessageContainingEnum_TestEnumType_Parse(
    const ::std::string& name, DummyMessageContainingEnum_TestEnumType* value) {
  return ::google::protobuf::internal::ParseNamedEnum<DummyMessageContainingEnum_TestEnumType>(
    DummyMessageContainingEnum_TestEnumType_descriptor(), name, value);
}
enum NestedOptionType_NestedEnum {
  NestedOptionType_NestedEnum_NESTED_ENUM_VALUE = 1
};
bool NestedOptionType_NestedEnum_IsValid(int value);
const NestedOptionType_NestedEnum NestedOptionType_NestedEnum_NestedEnum_MIN = NestedOptionType_NestedEnum_NESTED_ENUM_VALUE;
const NestedOptionType_NestedEnum NestedOptionType_NestedEnum_NestedEnum_MAX = NestedOptionType_NestedEnum_NESTED_ENUM_VALUE;
const int NestedOptionType_NestedEnum_NestedEnum_ARRAYSIZE = NestedOptionType_NestedEnum_NestedEnum_MAX + 1;

const ::google::protobuf::EnumDescriptor* NestedOptionType_NestedEnum_descriptor();
inline const ::std::string& NestedOptionType_NestedEnum_Name(NestedOptionType_NestedEnum value) {
  return ::google::protobuf::internal::NameOfEnum(
    NestedOptionType_NestedEnum_descriptor(), value);
}
inline bool NestedOptionType_NestedEnum_Parse(
    const ::std::string& name, NestedOptionType_NestedEnum* value) {
  return ::google::protobuf::internal::ParseNamedEnum<NestedOptionType_NestedEnum>(
    NestedOptionType_NestedEnum_descriptor(), name, value);
}
enum OldOptionType_TestEnum {
  OldOptionType_TestEnum_OLD_VALUE = 0
};
bool OldOptionType_TestEnum_IsValid(int value);
const OldOptionType_TestEnum OldOptionType_TestEnum_TestEnum_MIN = OldOptionType_TestEnum_OLD_VALUE;
const OldOptionType_TestEnum OldOptionType_TestEnum_TestEnum_MAX = OldOptionType_TestEnum_OLD_VALUE;
const int OldOptionType_TestEnum_TestEnum_ARRAYSIZE = OldOptionType_TestEnum_TestEnum_MAX + 1;

const ::google::protobuf::EnumDescriptor* OldOptionType_TestEnum_descriptor();
inline const ::std::string& OldOptionType_TestEnum_Name(OldOptionType_TestEnum value) {
  return ::google::protobuf::internal::NameOfEnum(
    OldOptionType_TestEnum_descriptor(), value);
}
inline bool OldOptionType_TestEnum_Parse(
    const ::std::string& name, OldOptionType_TestEnum* value) {
  return ::google::protobuf::internal::ParseNamedEnum<OldOptionType_TestEnum>(
    OldOptionType_TestEnum_descriptor(), name, value);
}
enum NewOptionType_TestEnum {
  NewOptionType_TestEnum_OLD_VALUE = 0,
  NewOptionType_TestEnum_NEW_VALUE = 1
};
bool NewOptionType_TestEnum_IsValid(int value);
const NewOptionType_TestEnum NewOptionType_TestEnum_TestEnum_MIN = NewOptionType_TestEnum_OLD_VALUE;
const NewOptionType_TestEnum NewOptionType_TestEnum_TestEnum_MAX = NewOptionType_TestEnum_NEW_VALUE;
const int NewOptionType_TestEnum_TestEnum_ARRAYSIZE = NewOptionType_TestEnum_TestEnum_MAX + 1;

const ::google::protobuf::EnumDescriptor* NewOptionType_TestEnum_descriptor();
inline const ::std::string& NewOptionType_TestEnum_Name(NewOptionType_TestEnum value) {
  return ::google::protobuf::internal::NameOfEnum(
    NewOptionType_TestEnum_descriptor(), value);
}
inline bool NewOptionType_TestEnum_Parse(
    const ::std::string& name, NewOptionType_TestEnum* value) {
  return ::google::protobuf::internal::ParseNamedEnum<NewOptionType_TestEnum>(
    NewOptionType_TestEnum_descriptor(), name, value);
}
enum MethodOpt1 {
  METHODOPT1_VAL1 = 1,
  METHODOPT1_VAL2 = 2
};
bool MethodOpt1_IsValid(int value);
const MethodOpt1 MethodOpt1_MIN = METHODOPT1_VAL1;
const MethodOpt1 MethodOpt1_MAX = METHODOPT1_VAL2;
const int MethodOpt1_ARRAYSIZE = MethodOpt1_MAX + 1;

const ::google::protobuf::EnumDescriptor* MethodOpt1_descriptor();
inline const ::std::string& MethodOpt1_Name(MethodOpt1 value) {
  return ::google::protobuf::internal::NameOfEnum(
    MethodOpt1_descriptor(), value);
}
inline bool MethodOpt1_Parse(
    const ::std::string& name, MethodOpt1* value) {
  return ::google::protobuf::internal::ParseNamedEnum<MethodOpt1>(
    MethodOpt1_descriptor(), name, value);
}
enum AggregateEnum {
  VALUE = 1
};
bool AggregateEnum_IsValid(int value);
const AggregateEnum AggregateEnum_MIN = VALUE;
const AggregateEnum AggregateEnum_MAX = VALUE;
const int AggregateEnum_ARRAYSIZE = AggregateEnum_MAX + 1;

const ::google::protobuf::EnumDescriptor* AggregateEnum_descriptor();
inline const ::std::string& AggregateEnum_Name(AggregateEnum value) {
  return ::google::protobuf::internal::NameOfEnum(
    AggregateEnum_descriptor(), value);
}
inline bool AggregateEnum_Parse(
    const ::std::string& name, AggregateEnum* value) {
  return ::google::protobuf::internal::ParseNamedEnum<AggregateEnum>(
    AggregateEnum_descriptor(), name, value);
}
// ===================================================================

class TestMessageWithCustomOptions : public ::google::protobuf::Message /* @@protoc_insertion_point(class_definition:protobuf_unittest.TestMessageWithCustomOptions) */ {
 public:
  TestMessageWithCustomOptions();
  virtual ~TestMessageWithCustomOptions();

  TestMessageWithCustomOptions(const TestMessageWithCustomOptions& from);

  inline TestMessageWithCustomOptions& operator=(const TestMessageWithCustomOptions& from) {
    CopyFrom(from);
    return *this;
  }

  inline const ::google::protobuf::UnknownFieldSet& unknown_fields() const {
    return _internal_metadata_.unknown_fields();
  }

  inline ::google::protobuf::UnknownFieldSet* mutable_unknown_fields() {
    return _internal_metadata_.mutable_unknown_fields();
  }

  static const ::google::protobuf::Descriptor* descriptor();
  static const TestMessageWithCustomOptions& default_instance();

  enum AnOneofCase {
    kOneofField = 2,
    ANONEOF_NOT_SET = 0,
  };

  static const TestMessageWithCustomOptions* internal_default_instance();

  void Swap(TestMessageWithCustomOptions* other);

  // implements Message ----------------------------------------------

  inline TestMessageWithCustomOptions* New() const { return New(NULL); }

  TestMessageWithCustomOptions* New(::google::protobuf::Arena* arena) const;
  void CopyFrom(const ::google::protobuf::Message& from);
  void MergeFrom(const ::google::protobuf::Message& from);
  void CopyFrom(const TestMessageWithCustomOptions& from);
  void MergeFrom(const TestMessageWithCustomOptions& from);
  void Clear();
  bool IsInitialized() const;

  size_t ByteSizeLong() const;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input);
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const;
  ::google::protobuf::uint8* InternalSerializeWithCachedSizesToArray(
      bool deterministic, ::google::protobuf::uint8* output) const;
  ::google::protobuf::uint8* SerializeWithCachedSizesToArray(::google::protobuf::uint8* output) const {
    return InternalSerializeWithCachedSizesToArray(false, output);
  }
  int GetCachedSize() const { return _cached_size_; }
  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const;
  void InternalSwap(TestMessageWithCustomOptions* other);
  void UnsafeMergeFrom(const TestMessageWithCustomOptions& from);
  private:
  inline ::google::protobuf::Arena* GetArenaNoVirtual() const {
    return _internal_metadata_.arena();
  }
  inline void* MaybeArenaPtr() const {
    return _internal_metadata_.raw_arena_ptr();
  }
  public:

  ::google::protobuf::Metadata GetMetadata() const;

  // nested types ----------------------------------------------------

  typedef TestMessageWithCustomOptions_AnEnum AnEnum;
  static const AnEnum ANENUM_VAL1 =
    TestMessageWithCustomOptions_AnEnum_ANENUM_VAL1;
  static const AnEnum ANENUM_VAL2 =
    TestMessageWithCustomOptions_AnEnum_ANENUM_VAL2;
  static inline bool AnEnum_IsValid(int value) {
    return TestMessageWithCustomOptions_AnEnum_IsValid(value);
  }
  static const AnEnum AnEnum_MIN =
    TestMessageWithCustomOptions_AnEnum_AnEnum_MIN;
  static const AnEnum AnEnum_MAX =
    TestMessageWithCustomOptions_AnEnum_AnEnum_MAX;
  static const int AnEnum_ARRAYSIZE =
    TestMessageWithCustomOptions_AnEnum_AnEnum_ARRAYSIZE;
  static inline const ::google::protobuf::EnumDescriptor*
  AnEnum_descriptor() {
    return TestMessageWithCustomOptions_AnEnum_descriptor();
  }
  static inline const ::std::string& AnEnum_Name(AnEnum value) {
    return TestMessageWithCustomOptions_AnEnum_Name(value);
  }
  static inline bool AnEnum_Parse(const ::std::string& name,
      AnEnum* value) {
    return TestMessageWithCustomOptions_AnEnum_Parse(name, value);
  }

  // accessors -------------------------------------------------------

  // optional string field1 = 1 [ctype = CORD];
  bool has_field1() const;
  void clear_field1();
  static const int kField1FieldNumber = 1;
 private:
  // Hidden due to unknown ctype option.
  const ::std::string& field1() const;
  void set_field1(const ::std::string& value);
  void set_field1(const char* value);
  void set_field1(const char* value, size_t size);
  ::std::string* mutable_field1();
  ::std::string* release_field1();
  void set_allocated_field1(::std::string* field1);
 public:

  // optional int32 oneof_field = 2;
  bool has_oneof_field() const;
  void clear_oneof_field();
  static const int kOneofFieldFieldNumber = 2;
  ::google::protobuf::int32 oneof_field() const;
  void set_oneof_field(::google::protobuf::int32 value);

  AnOneofCase AnOneof_case() const;
  // @@protoc_insertion_point(class_scope:protobuf_unittest.TestMessageWithCustomOptions)
 private:
  inline void set_has_field1();
  inline void clear_has_field1();
  inline void set_has_oneof_field();

  inline bool has_AnOneof() const;
  void clear_AnOneof();
  inline void clear_has_AnOneof();

  ::google::protobuf::internal::InternalMetadataWithArena _internal_metadata_;
  ::google::protobuf::internal::HasBits<1> _has_bits_;
  mutable int _cached_size_;
  ::google::protobuf::internal::ArenaStringPtr field1_;
  union AnOneofUnion {
    AnOneofUnion() {}
    ::google::protobuf::int32 oneof_field_;
  } AnOneof_;
  ::google::protobuf::uint32 _oneof_case_[1];

  friend void  protobuf_InitDefaults_google_2fprotobuf_2funittest_5fcustom_5foptions_2eproto_impl();
  friend void  protobuf_AddDesc_google_2fprotobuf_2funittest_5fcustom_5foptions_2eproto_impl();
  friend void protobuf_AssignDesc_google_2fprotobuf_2funittest_5fcustom_5foptions_2eproto();
  friend void protobuf_ShutdownFile_google_2fprotobuf_2funittest_5fcustom_5foptions_2eproto();

  void InitAsDefaultInstance();
};
extern ::google::protobuf::internal::ExplicitlyConstructed<TestMessageWithCustomOptions> TestMessageWithCustomOptions_default_instance_;

// -------------------------------------------------------------------

class CustomOptionFooRequest : public ::google::protobuf::Message /* @@protoc_insertion_point(class_definition:protobuf_unittest.CustomOptionFooRequest) */ {
 public:
  CustomOptionFooRequest();
  virtual ~CustomOptionFooRequest();

  CustomOptionFooRequest(const CustomOptionFooRequest& from);

  inline CustomOptionFooRequest& operator=(const CustomOptionFooRequest& from) {
    CopyFrom(from);
    return *this;
  }

  inline const ::google::protobuf::UnknownFieldSet& unknown_fields() const {
    return _internal_metadata_.unknown_fields();
  }

  inline ::google::protobuf::UnknownFieldSet* mutable_unknown_fields() {
    return _internal_metadata_.mutable_unknown_fields();
  }

  static const ::google::protobuf::Descriptor* descriptor();
  static const CustomOptionFooRequest& default_instance();

  static const CustomOptionFooRequest* internal_default_instance();

  void Swap(CustomOptionFooRequest* other);

  // implements Message ----------------------------------------------

  inline CustomOptionFooRequest* New() const { return New(NULL); }

  CustomOptionFooRequest* New(::google::protobuf::Arena* arena) const;
  void CopyFrom(const ::google::protobuf::Message& from);
  void MergeFrom(const ::google::protobuf::Message& from);
  void CopyFrom(const CustomOptionFooRequest& from);
  void MergeFrom(const CustomOptionFooRequest& from);
  void Clear();
  bool IsInitialized() const;

  size_t ByteSizeLong() const;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input);
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const;
  ::google::protobuf::uint8* InternalSerializeWithCachedSizesToArray(
      bool deterministic, ::google::protobuf::uint8* output) const;
  ::google::protobuf::uint8* SerializeWithCachedSizesToArray(::google::protobuf::uint8* output) const {
    return InternalSerializeWithCachedSizesToArray(false, output);
  }
  int GetCachedSize() const { return _cached_size_; }
  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const;
  void InternalSwap(CustomOptionFooRequest* other);
  void UnsafeMergeFrom(const CustomOptionFooRequest& from);
  private:
  inline ::google::protobuf::Arena* GetArenaNoVirtual() const {
    return _internal_metadata_.arena();
  }
  inline void* MaybeArenaPtr() const {
    return _internal_metadata_.raw_arena_ptr();
  }
  public:

  ::google::protobuf::Metadata GetMetadata() const;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // @@protoc_insertion_point(class_scope:protobuf_unittest.CustomOptionFooRequest)
 private:

  ::google::protobuf::internal::InternalMetadataWithArena _internal_metadata_;
  ::google::protobuf::internal::HasBits<1> _has_bits_;
  mutable int _cached_size_;
  friend void  protobuf_InitDefaults_google_2fprotobuf_2funittest_5fcustom_5foptions_2eproto_impl();
  friend void  protobuf_AddDesc_google_2fprotobuf_2funittest_5fcustom_5foptions_2eproto_impl();
  friend void protobuf_AssignDesc_google_2fprotobuf_2funittest_5fcustom_5foptions_2eproto();
  friend void protobuf_ShutdownFile_google_2fprotobuf_2funittest_5fcustom_5foptions_2eproto();

  void InitAsDefaultInstance();
};
extern ::google::protobuf::internal::ExplicitlyConstructed<CustomOptionFooRequest> CustomOptionFooRequest_default_instance_;

// -------------------------------------------------------------------

class CustomOptionFooResponse : public ::google::protobuf::Message /* @@protoc_insertion_point(class_definition:protobuf_unittest.CustomOptionFooResponse) */ {
 public:
  CustomOptionFooResponse();
  virtual ~CustomOptionFooResponse();

  CustomOptionFooResponse(const CustomOptionFooResponse& from);

  inline CustomOptionFooResponse& operator=(const CustomOptionFooResponse& from) {
    CopyFrom(from);
    return *this;
  }

  inline const ::google::protobuf::UnknownFieldSet& unknown_fields() const {
    return _internal_metadata_.unknown_fields();
  }

  inline ::google::protobuf::UnknownFieldSet* mutable_unknown_fields() {
    return _internal_metadata_.mutable_unknown_fields();
  }

  static const ::google::protobuf::Descriptor* descriptor();
  static const CustomOptionFooResponse& default_instance();

  static const CustomOptionFooResponse* internal_default_instance();

  void Swap(CustomOptionFooResponse* other);

  // implements Message ----------------------------------------------

  inline CustomOptionFooResponse* New() const { return New(NULL); }

  CustomOptionFooResponse* New(::google::protobuf::Arena* arena) const;
  void CopyFrom(const ::google::protobuf::Message& from);
  void MergeFrom(const ::google::protobuf::Message& from);
  void CopyFrom(const CustomOptionFooResponse& from);
  void MergeFrom(const CustomOptionFooResponse& from);
  void Clear();
  bool IsInitialized() const;

  size_t ByteSizeLong() const;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input);
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const;
  ::google::protobuf::uint8* InternalSerializeWithCachedSizesToArray(
      bool deterministic, ::google::protobuf::uint8* output) const;
  ::google::protobuf::uint8* SerializeWithCachedSizesToArray(::google::protobuf::uint8* output) const {
    return InternalSerializeWithCachedSizesToArray(false, output);
  }
  int GetCachedSize() const { return _cached_size_; }
  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const;
  void InternalSwap(CustomOptionFooResponse* other);
  void UnsafeMergeFrom(const CustomOptionFooResponse& from);
  private:
  inline ::google::protobuf::Arena* GetArenaNoVirtual() const {
    return _internal_metadata_.arena();
  }
  inline void* MaybeArenaPtr() const {
    return _internal_metadata_.raw_arena_ptr();
  }
  public:

  ::google::protobuf::Metadata GetMetadata() const;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // @@protoc_insertion_point(class_scope:protobuf_unittest.CustomOptionFooResponse)
 private:

  ::google::protobuf::internal::InternalMetadataWithArena _internal_metadata_;
  ::google::protobuf::internal::HasBits<1> _has_bits_;
  mutable int _cached_size_;
  friend void  protobuf_InitDefaults_google_2fprotobuf_2funittest_5fcustom_5foptions_2eproto_impl();
  friend void  protobuf_AddDesc_google_2fprotobuf_2funittest_5fcustom_5foptions_2eproto_impl();
  friend void protobuf_AssignDesc_google_2fprotobuf_2funittest_5fcustom_5foptions_2eproto();
  friend void protobuf_ShutdownFile_google_2fprotobuf_2funittest_5fcustom_5foptions_2eproto();

  void InitAsDefaultInstance();
};
extern ::google::protobuf::internal::ExplicitlyConstructed<CustomOptionFooResponse> CustomOptionFooResponse_default_instance_;

// -------------------------------------------------------------------

class CustomOptionFooClientMessage : public ::google::protobuf::Message /* @@protoc_insertion_point(class_definition:protobuf_unittest.CustomOptionFooClientMessage) */ {
 public:
  CustomOptionFooClientMessage();
  virtual ~CustomOptionFooClientMessage();

  CustomOptionFooClientMessage(const CustomOptionFooClientMessage& from);

  inline CustomOptionFooClientMessage& operator=(const CustomOptionFooClientMessage& from) {
    CopyFrom(from);
    return *this;
  }

  inline const ::google::protobuf::UnknownFieldSet& unknown_fields() const {
    return _internal_metadata_.unknown_fields();
  }

  inline ::google::protobuf::UnknownFieldSet* mutable_unknown_fields() {
    return _internal_metadata_.mutable_unknown_fields();
  }

  static const ::google::protobuf::Descriptor* descriptor();
  static const CustomOptionFooClientMessage& default_instance();

  static const CustomOptionFooClientMessage* internal_default_instance();

  void Swap(CustomOptionFooClientMessage* other);

  // implements Message ----------------------------------------------

  inline CustomOptionFooClientMessage* New() const { return New(NULL); }

  CustomOptionFooClientMessage* New(::google::protobuf::Arena* arena) const;
  void CopyFrom(const ::google::protobuf::Message& from);
  void MergeFrom(const ::google::protobuf::Message& from);
  void CopyFrom(const CustomOptionFooClientMessage& from);
  void MergeFrom(const CustomOptionFooClientMessage& from);
  void Clear();
  bool IsInitialized() const;

  size_t ByteSizeLong() const;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input);
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const;
  ::google::protobuf::uint8* InternalSerializeWithCachedSizesToArray(
      bool deterministic, ::google::protobuf::uint8* output) const;
  ::google::protobuf::uint8* SerializeWithCachedSizesToArray(::google::protobuf::uint8* output) const {
    return InternalSerializeWithCachedSizesToArray(false, output);
  }
  int GetCachedSize() const { return _cached_size_; }
  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const;
  void InternalSwap(CustomOptionFooClientMessage* other);
  void UnsafeMergeFrom(const CustomOptionFooClientMessage& from);
  private:
  inline ::google::protobuf::Arena* GetArenaNoVirtual() const {
    return _internal_metadata_.arena();
  }
  inline void* MaybeArenaPtr() const {
    return _internal_metadata_.raw_arena_ptr();
  }
  public:

  ::google::protobuf::Metadata GetMetadata() const;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // @@protoc_insertion_point(class_scope:protobuf_unittest.CustomOptionFooClientMessage)
 private:

  ::google::protobuf::internal::InternalMetadataWithArena _internal_metadata_;
  ::google::protobuf::internal::HasBits<1> _has_bits_;
  mutable int _cached_size_;
  friend void  protobuf_InitDefaults_google_2fprotobuf_2funittest_5fcustom_5foptions_2eproto_impl();
  friend void  protobuf_AddDesc_google_2fprotobuf_2funittest_5fcustom_5foptions_2eproto_impl();
  friend void protobuf_AssignDesc_google_2fprotobuf_2funittest_5fcustom_5foptions_2eproto();
  friend void protobuf_ShutdownFile_google_2fprotobuf_2funittest_5fcustom_5foptions_2eproto();

  void InitAsDefaultInstance();
};
extern ::google::protobuf::internal::ExplicitlyConstructed<CustomOptionFooClientMessage> CustomOptionFooClientMessage_default_instance_;

// -------------------------------------------------------------------

class CustomOptionFooServerMessage : public ::google::protobuf::Message /* @@protoc_insertion_point(class_definition:protobuf_unittest.CustomOptionFooServerMessage) */ {
 public:
  CustomOptionFooServerMessage();
  virtual ~CustomOptionFooServerMessage();

  CustomOptionFooServerMessage(const CustomOptionFooServerMessage& from);

  inline CustomOptionFooServerMessage& operator=(const CustomOptionFooServerMessage& from) {
    CopyFrom(from);
    return *this;
  }

  inline const ::google::protobuf::UnknownFieldSet& unknown_fields() const {
    return _internal_metadata_.unknown_fields();
  }

  inline ::google::protobuf::UnknownFieldSet* mutable_unknown_fields() {
    return _internal_metadata_.mutable_unknown_fields();
  }

  static const ::google::protobuf::Descriptor* descriptor();
  static const CustomOptionFooServerMessage& default_instance();

  static const CustomOptionFooServerMessage* internal_default_instance();

  void Swap(CustomOptionFooServerMessage* other);

  // implements Message ----------------------------------------------

  inline CustomOptionFooServerMessage* New() const { return New(NULL); }

  CustomOptionFooServerMessage* New(::google::protobuf::Arena* arena) const;
  void CopyFrom(const ::google::protobuf::Message& from);
  void MergeFrom(const ::google::protobuf::Message& from);
  void CopyFrom(const CustomOptionFooServerMessage& from);
  void MergeFrom(const CustomOptionFooServerMessage& from);
  void Clear();
  bool IsInitialized() const;

  size_t ByteSizeLong() const;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input);
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const;
  ::google::protobuf::uint8* InternalSerializeWithCachedSizesToArray(
      bool deterministic, ::google::protobuf::uint8* output) const;
  ::google::protobuf::uint8* SerializeWithCachedSizesToArray(::google::protobuf::uint8* output) const {
    return InternalSerializeWithCachedSizesToArray(false, output);
  }
  int GetCachedSize() const { return _cached_size_; }
  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const;
  void InternalSwap(CustomOptionFooServerMessage* other);
  void UnsafeMergeFrom(const CustomOptionFooServerMessage& from);
  private:
  inline ::google::protobuf::Arena* GetArenaNoVirtual() const {
    return _internal_metadata_.arena();
  }
  inline void* MaybeArenaPtr() const {
    return _internal_metadata_.raw_arena_ptr();
  }
  public:

  ::google::protobuf::Metadata GetMetadata() const;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // @@protoc_insertion_point(class_scope:protobuf_unittest.CustomOptionFooServerMessage)
 private:

  ::google::protobuf::internal::InternalMetadataWithArena _internal_metadata_;
  ::google::protobuf::internal::HasBits<1> _has_bits_;
  mutable int _cached_size_;
  friend void  protobuf_InitDefaults_google_2fprotobuf_2funittest_5fcustom_5foptions_2eproto_impl();
  friend void  protobuf_AddDesc_google_2fprotobuf_2funittest_5fcustom_5foptions_2eproto_impl();
  friend void protobuf_AssignDesc_google_2fprotobuf_2funittest_5fcustom_5foptions_2eproto();
  friend void protobuf_ShutdownFile_google_2fprotobuf_2funittest_5fcustom_5foptions_2eproto();

  void InitAsDefaultInstance();
};
extern ::google::protobuf::internal::ExplicitlyConstructed<CustomOptionFooServerMessage> CustomOptionFooServerMessage_default_instance_;

// -------------------------------------------------------------------

class DummyMessageContainingEnum : public ::google::protobuf::Message /* @@protoc_insertion_point(class_definition:protobuf_unittest.DummyMessageContainingEnum) */ {
 public:
  DummyMessageContainingEnum();
  virtual ~DummyMessageContainingEnum();

  DummyMessageContainingEnum(const DummyMessageContainingEnum& from);

  inline DummyMessageContainingEnum& operator=(const DummyMessageContainingEnum& from) {
    CopyFrom(from);
    return *this;
  }

  inline const ::google::protobuf::UnknownFieldSet& unknown_fields() const {
    return _internal_metadata_.unknown_fields();
  }

  inline ::google::protobuf::UnknownFieldSet* mutable_unknown_fields() {
    return _internal_metadata_.mutable_unknown_fields();
  }

  static const ::google::protobuf::Descriptor* descriptor();
  static const DummyMessageContainingEnum& default_instance();

  static const DummyMessageContainingEnum* internal_default_instance();

  void Swap(DummyMessageContainingEnum* other);

  // implements Message ----------------------------------------------

  inline DummyMessageContainingEnum* New() const { return New(NULL); }

  DummyMessageContainingEnum* New(::google::protobuf::Arena* arena) const;
  void CopyFrom(const ::google::protobuf::Message& from);
  void MergeFrom(const ::google::protobuf::Message& from);
  void CopyFrom(const DummyMessageContainingEnum& from);
  void MergeFrom(const DummyMessageContainingEnum& from);
  void Clear();
  bool IsInitialized() const;

  size_t ByteSizeLong() const;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input);
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const;
  ::google::protobuf::uint8* InternalSerializeWithCachedSizesToArray(
      bool deterministic, ::google::protobuf::uint8* output) const;
  ::google::protobuf::uint8* SerializeWithCachedSizesToArray(::google::protobuf::uint8* output) const {
    return InternalSerializeWithCachedSizesToArray(false, output);
  }
  int GetCachedSize() const { return _cached_size_; }
  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const;
  void InternalSwap(DummyMessageContainingEnum* other);
  void UnsafeMergeFrom(const DummyMessageContainingEnum& from);
  private:
  inline ::google::protobuf::Arena* GetArenaNoVirtual() const {
    return _internal_metadata_.arena();
  }
  inline void* MaybeArenaPtr() const {
    return _internal_metadata_.raw_arena_ptr();
  }
  public:

  ::google::protobuf::Metadata GetMetadata() const;

  // nested types ----------------------------------------------------

  typedef DummyMessageContainingEnum_TestEnumType TestEnumType;
  static const TestEnumType TEST_OPTION_ENUM_TYPE1 =
    DummyMessageContainingEnum_TestEnumType_TEST_OPTION_ENUM_TYPE1;
  static const TestEnumType TEST_OPTION_ENUM_TYPE2 =
    DummyMessageContainingEnum_TestEnumType_TEST_OPTION_ENUM_TYPE2;
  static inline bool TestEnumType_IsValid(int value) {
    return DummyMessageContainingEnum_TestEnumType_IsValid(value);
  }
  static const TestEnumType TestEnumType_MIN =
    DummyMessageContainingEnum_TestEnumType_TestEnumType_MIN;
  static const TestEnumType TestEnumType_MAX =
    DummyMessageContainingEnum_TestEnumType_TestEnumType_MAX;
  static const int TestEnumType_ARRAYSIZE =
    DummyMessageContainingEnum_TestEnumType_TestEnumType_ARRAYSIZE;
  static inline const ::google::protobuf::EnumDescriptor*
  TestEnumType_descriptor() {
    return DummyMessageContainingEnum_TestEnumType_descriptor();
  }
  static inline const ::std::string& TestEnumType_Name(TestEnumType value) {
    return DummyMessageContainingEnum_TestEnumType_Name(value);
  }
  static inline bool TestEnumType_Parse(const ::std::string& name,
      TestEnumType* value) {
    return DummyMessageContainingEnum_TestEnumType_Parse(name, value);
  }

  // accessors -------------------------------------------------------

  // @@protoc_insertion_point(class_scope:protobuf_unittest.DummyMessageContainingEnum)
 private:

  ::google::protobuf::internal::InternalMetadataWithArena _internal_metadata_;
  ::google::protobuf::internal::HasBits<1> _has_bits_;
  mutable int _cached_size_;
  friend void  protobuf_InitDefaults_google_2fprotobuf_2funittest_5fcustom_5foptions_2eproto_impl();
  friend void  protobuf_AddDesc_google_2fprotobuf_2funittest_5fcustom_5foptions_2eproto_impl();
  friend void protobuf_AssignDesc_google_2fprotobuf_2funittest_5fcustom_5foptions_2eproto();
  friend void protobuf_ShutdownFile_google_2fprotobuf_2funittest_5fcustom_5foptions_2eproto();

  void InitAsDefaultInstance();
};
extern ::google::protobuf::internal::ExplicitlyConstructed<DummyMessageContainingEnum> DummyMessageContainingEnum_default_instance_;

// -------------------------------------------------------------------

class DummyMessageInvalidAsOptionType : public ::google::protobuf::Message /* @@protoc_insertion_point(class_definition:protobuf_unittest.DummyMessageInvalidAsOptionType) */ {
 public:
  DummyMessageInvalidAsOptionType();
  virtual ~DummyMessageInvalidAsOptionType();

  DummyMessageInvalidAsOptionType(const DummyMessageInvalidAsOptionType& from);

  inline DummyMessageInvalidAsOptionType& operator=(const DummyMessageInvalidAsOptionType& from) {
    CopyFrom(from);
    return *this;
  }

  inline const ::google::protobuf::UnknownFieldSet& unknown_fields() const {
    return _internal_metadata_.unknown_fields();
  }

  inline ::google::protobuf::UnknownFieldSet* mutable_unknown_fields() {
    return _internal_metadata_.mutable_unknown_fields();
  }

  static const ::google::protobuf::Descriptor* descriptor();
  static const DummyMessageInvalidAsOptionType& default_instance();

  static const DummyMessageInvalidAsOptionType* internal_default_instance();

  void Swap(DummyMessageInvalidAsOptionType* other);

  // implements Message ----------------------------------------------

  inline DummyMessageInvalidAsOptionType* New() const { return New(NULL); }

  DummyMessageInvalidAsOptionType* New(::google::protobuf::Arena* arena) const;
  void CopyFrom(const ::google::protobuf::Message& from);
  void MergeFrom(const ::google::protobuf::Message& from);
  void CopyFrom(const DummyMessageInvalidAsOptionType& from);
  void MergeFrom(const DummyMessageInvalidAsOptionType& from);
  void Clear();
  bool IsInitialized() const;

  size_t ByteSizeLong() const;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input);
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const;
  ::google::protobuf::uint8* InternalSerializeWithCachedSizesToArray(
      bool deterministic, ::google::protobuf::uint8* output) const;
  ::google::protobuf::uint8* SerializeWithCachedSizesToArray(::google::protobuf::uint8* output) const {
    return InternalSerializeWithCachedSizesToArray(false, output);
  }
  int GetCachedSize() const { return _cached_size_; }
  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const;
  void InternalSwap(DummyMessageInvalidAsOptionType* other);
  void UnsafeMergeFrom(const DummyMessageInvalidAsOptionType& from);
  private:
  inline ::google::protobuf::Arena* GetArenaNoVirtual() const {
    return _internal_metadata_.arena();
  }
  inline void* MaybeArenaPtr() const {
    return _internal_metadata_.raw_arena_ptr();
  }
  public:

  ::google::protobuf::Metadata GetMetadata() const;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // @@protoc_insertion_point(class_scope:protobuf_unittest.DummyMessageInvalidAsOptionType)
 private:

  ::google::protobuf::internal::InternalMetadataWithArena _internal_metadata_;
  ::google::protobuf::internal::HasBits<1> _has_bits_;
  mutable int _cached_size_;
  friend void  protobuf_InitDefaults_google_2fprotobuf_2funittest_5fcustom_5foptions_2eproto_impl();
  friend void  protobuf_AddDesc_google_2fprotobuf_2funittest_5fcustom_5foptions_2eproto_impl();
  friend void protobuf_AssignDesc_google_2fprotobuf_2funittest_5fcustom_5foptions_2eproto();
  friend void protobuf_ShutdownFile_google_2fprotobuf_2funittest_5fcustom_5foptions_2eproto();

  void InitAsDefaultInstance();
};
extern ::google::protobuf::internal::ExplicitlyConstructed<DummyMessageInvalidAsOptionType> DummyMessageInvalidAsOptionType_default_instance_;

// -------------------------------------------------------------------

class CustomOptionMinIntegerValues : public ::google::protobuf::Message /* @@protoc_insertion_point(class_definition:protobuf_unittest.CustomOptionMinIntegerValues) */ {
 public:
  CustomOptionMinIntegerValues();
  virtual ~CustomOptionMinIntegerValues();

  CustomOptionMinIntegerValues(const CustomOptionMinIntegerValues& from);

  inline CustomOptionMinIntegerValues& operator=(const CustomOptionMinIntegerValues& from) {
    CopyFrom(from);
    return *this;
  }

  inline const ::google::protobuf::UnknownFieldSet& unknown_fields() const {
    return _internal_metadata_.unknown_fields();
  }

  inline ::google::protobuf::UnknownFieldSet* mutable_unknown_fields() {
    return _internal_metadata_.mutable_unknown_fields();
  }

  static const ::google::protobuf::Descriptor* descriptor();
  static const CustomOptionMinIntegerValues& default_instance();

  static const CustomOptionMinIntegerValues* internal_default_instance();

  void Swap(CustomOptionMinIntegerValues* other);

  // implements Message ----------------------------------------------

  inline CustomOptionMinIntegerValues* New() const { return New(NULL); }

  CustomOptionMinIntegerValues* New(::google::protobuf::Arena* arena) const;
  void CopyFrom(const ::google::protobuf::Message& from);
  void MergeFrom(const ::google::protobuf::Message& from);
  void CopyFrom(const CustomOptionMinIntegerValues& from);
  void MergeFrom(const CustomOptionMinIntegerValues& from);
  void Clear();
  bool IsInitialized() const;

  size_t ByteSizeLong() const;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input);
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const;
  ::google::protobuf::uint8* InternalSerializeWithCachedSizesToArray(
      bool deterministic, ::google::protobuf::uint8* output) const;
  ::google::protobuf::uint8* SerializeWithCachedSizesToArray(::google::protobuf::uint8* output) const {
    return InternalSerializeWithCachedSizesToArray(false, output);
  }
  int GetCachedSize() const { return _cached_size_; }
  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const;
  void InternalSwap(CustomOptionMinIntegerValues* other);
  void UnsafeMergeFrom(const CustomOptionMinIntegerValues& from);
  private:
  inline ::google::protobuf::Arena* GetArenaNoVirtual() const {
    return _internal_metadata_.arena();
  }
  inline void* MaybeArenaPtr() const {
    return _internal_metadata_.raw_arena_ptr();
  }
  public:

  ::google::protobuf::Metadata GetMetadata() const;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // @@protoc_insertion_point(class_scope:protobuf_unittest.CustomOptionMinIntegerValues)
 private:

  ::google::protobuf::internal::InternalMetadataWithArena _internal_metadata_;
  ::google::protobuf::internal::HasBits<1> _has_bits_;
  mutable int _cached_size_;
  friend void  protobuf_InitDefaults_google_2fprotobuf_2funittest_5fcustom_5foptions_2eproto_impl();
  friend void  protobuf_AddDesc_google_2fprotobuf_2funittest_5fcustom_5foptions_2eproto_impl();
  friend void protobuf_AssignDesc_google_2fprotobuf_2funittest_5fcustom_5foptions_2eproto();
  friend void protobuf_ShutdownFile_google_2fprotobuf_2funittest_5fcustom_5foptions_2eproto();

  void InitAsDefaultInstance();
};
extern ::google::protobuf::internal::ExplicitlyConstructed<CustomOptionMinIntegerValues> CustomOptionMinIntegerValues_default_instance_;

// -------------------------------------------------------------------

class CustomOptionMaxIntegerValues : public ::google::protobuf::Message /* @@protoc_insertion_point(class_definition:protobuf_unittest.CustomOptionMaxIntegerValues) */ {
 public:
  CustomOptionMaxIntegerValues();
  virtual ~CustomOptionMaxIntegerValues();

  CustomOptionMaxIntegerValues(const CustomOptionMaxIntegerValues& from);

  inline CustomOptionMaxIntegerValues& operator=(const CustomOptionMaxIntegerValues& from) {
    CopyFrom(from);
    return *this;
  }

  inline const ::google::protobuf::UnknownFieldSet& unknown_fields() const {
    return _internal_metadata_.unknown_fields();
  }

  inline ::google::protobuf::UnknownFieldSet* mutable_unknown_fields() {
    return _internal_metadata_.mutable_unknown_fields();
  }

  static const ::google::protobuf::Descriptor* descriptor();
  static const CustomOptionMaxIntegerValues& default_instance();

  static const CustomOptionMaxIntegerValues* internal_default_instance();

  void Swap(CustomOptionMaxIntegerValues* other);

  // implements Message ----------------------------------------------

  inline CustomOptionMaxIntegerValues* New() const { return New(NULL); }

  CustomOptionMaxIntegerValues* New(::google::protobuf::Arena* arena) const;
  void CopyFrom(const ::google::protobuf::Message& from);
  void MergeFrom(const ::google::protobuf::Message& from);
  void CopyFrom(const CustomOptionMaxIntegerValues& from);
  void MergeFrom(const CustomOptionMaxIntegerValues& from);
  void Clear();
  bool IsInitialized() const;

  size_t ByteSizeLong() const;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input);
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const;
  ::google::protobuf::uint8* InternalSerializeWithCachedSizesToArray(
      bool deterministic, ::google::protobuf::uint8* output) const;
  ::google::protobuf::uint8* SerializeWithCachedSizesToArray(::google::protobuf::uint8* output) const {
    return InternalSerializeWithCachedSizesToArray(false, output);
  }
  int GetCachedSize() const { return _cached_size_; }
  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const;
  void InternalSwap(CustomOptionMaxIntegerValues* other);
  void UnsafeMergeFrom(const CustomOptionMaxIntegerValues& from);
  private:
  inline ::google::protobuf::Arena* GetArenaNoVirtual() const {
    return _internal_metadata_.arena();
  }
  inline void* MaybeArenaPtr() const {
    return _internal_metadata_.raw_arena_ptr();
  }
  public:

  ::google::protobuf::Metadata GetMetadata() const;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // @@protoc_insertion_point(class_scope:protobuf_unittest.CustomOptionMaxIntegerValues)
 private:

  ::google::protobuf::internal::InternalMetadataWithArena _internal_metadata_;
  ::google::protobuf::internal::HasBits<1> _has_bits_;
  mutable int _cached_size_;
  friend void  protobuf_InitDefaults_google_2fprotobuf_2funittest_5fcustom_5foptions_2eproto_impl();
  friend void  protobuf_AddDesc_google_2fprotobuf_2funittest_5fcustom_5foptions_2eproto_impl();
  friend void protobuf_AssignDesc_google_2fprotobuf_2funittest_5fcustom_5foptions_2eproto();
  friend void protobuf_ShutdownFile_google_2fprotobuf_2funittest_5fcustom_5foptions_2eproto();

  void InitAsDefaultInstance();
};
extern ::google::protobuf::internal::ExplicitlyConstructed<CustomOptionMaxIntegerValues> CustomOptionMaxIntegerValues_default_instance_;

// -------------------------------------------------------------------

class CustomOptionOtherValues : public ::google::protobuf::Message /* @@protoc_insertion_point(class_definition:protobuf_unittest.CustomOptionOtherValues) */ {
 public:
  CustomOptionOtherValues();
  virtual ~CustomOptionOtherValues();

  CustomOptionOtherValues(const CustomOptionOtherValues& from);

  inline CustomOptionOtherValues& operator=(const CustomOptionOtherValues& from) {
    CopyFrom(from);
    return *this;
  }

  inline const ::google::protobuf::UnknownFieldSet& unknown_fields() const {
    return _internal_metadata_.unknown_fields();
  }

  inline ::google::protobuf::UnknownFieldSet* mutable_unknown_fields() {
    return _internal_metadata_.mutable_unknown_fields();
  }

  static const ::google::protobuf::Descriptor* descriptor();
  static const CustomOptionOtherValues& default_instance();

  static const CustomOptionOtherValues* internal_default_instance();

  void Swap(CustomOptionOtherValues* other);

  // implements Message ----------------------------------------------

  inline CustomOptionOtherValues* New() const { return New(NULL); }

  CustomOptionOtherValues* New(::google::protobuf::Arena* arena) const;
  void CopyFrom(const ::google::protobuf::Message& from);
  void MergeFrom(const ::google::protobuf::Message& from);
  void CopyFrom(const CustomOptionOtherValues& from);
  void MergeFrom(const CustomOptionOtherValues& from);
  void Clear();
  bool IsInitialized() const;

  size_t ByteSizeLong() const;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input);
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const;
  ::google::protobuf::uint8* InternalSerializeWithCachedSizesToArray(
      bool deterministic, ::google::protobuf::uint8* output) const;
  ::google::protobuf::uint8* SerializeWithCachedSizesToArray(::google::protobuf::uint8* output) const {
    return InternalSerializeWithCachedSizesToArray(false, output);
  }
  int GetCachedSize() const { return _cached_size_; }
  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const;
  void InternalSwap(CustomOptionOtherValues* other);
  void UnsafeMergeFrom(const CustomOptionOtherValues& from);
  private:
  inline ::google::protobuf::Arena* GetArenaNoVirtual() const {
    return _internal_metadata_.arena();
  }
  inline void* MaybeArenaPtr() const {
    return _internal_metadata_.raw_arena_ptr();
  }
  public:

  ::google::protobuf::Metadata GetMetadata() const;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // @@protoc_insertion_point(class_scope:protobuf_unittest.CustomOptionOtherValues)
 private:

  ::google::protobuf::internal::InternalMetadataWithArena _internal_metadata_;
  ::google::protobuf::internal::HasBits<1> _has_bits_;
  mutable int _cached_size_;
  friend void  protobuf_InitDefaults_google_2fprotobuf_2funittest_5fcustom_5foptions_2eproto_impl();
  friend void  protobuf_AddDesc_google_2fprotobuf_2funittest_5fcustom_5foptions_2eproto_impl();
  friend void protobuf_AssignDesc_google_2fprotobuf_2funittest_5fcustom_5foptions_2eproto();
  friend void protobuf_ShutdownFile_google_2fprotobuf_2funittest_5fcustom_5foptions_2eproto();

  void InitAsDefaultInstance();
};
extern ::google::protobuf::internal::ExplicitlyConstructed<CustomOptionOtherValues> CustomOptionOtherValues_default_instance_;

// -------------------------------------------------------------------

class SettingRealsFromPositiveInts : public ::google::protobuf::Message /* @@protoc_insertion_point(class_definition:protobuf_unittest.SettingRealsFromPositiveInts) */ {
 public:
  SettingRealsFromPositiveInts();
  virtual ~SettingRealsFromPositiveInts();

  SettingRealsFromPositiveInts(const SettingRealsFromPositiveInts& from);

  inline SettingRealsFromPositiveInts& operator=(const SettingRealsFromPositiveInts& from) {
    CopyFrom(from);
    return *this;
  }

  inline const ::google::protobuf::UnknownFieldSet& unknown_fields() const {
    return _internal_metadata_.unknown_fields();
  }

  inline ::google::protobuf::UnknownFieldSet* mutable_unknown_fields() {
    return _internal_metadata_.mutable_unknown_fields();
  }

  static const ::google::protobuf::Descriptor* descriptor();
  static const SettingRealsFromPositiveInts& default_instance();

  static const SettingRealsFromPositiveInts* internal_default_instance();

  void Swap(SettingRealsFromPositiveInts* other);

  // implements Message ----------------------------------------------

  inline SettingRealsFromPositiveInts* New() const { return New(NULL); }

  SettingRealsFromPositiveInts* New(::google::protobuf::Arena* arena) const;
  void CopyFrom(const ::google::protobuf::Message& from);
  void MergeFrom(const ::google::protobuf::Message& from);
  void CopyFrom(const SettingRealsFromPositiveInts& from);
  void MergeFrom(const SettingRealsFromPositiveInts& from);
  void Clear();
  bool IsInitialized() const;

  size_t ByteSizeLong() const;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input);
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const;
  ::google::protobuf::uint8* InternalSerializeWithCachedSizesToArray(
      bool deterministic, ::google::protobuf::uint8* output) const;
  ::google::protobuf::uint8* SerializeWithCachedSizesToArray(::google::protobuf::uint8* output) const {
    return InternalSerializeWithCachedSizesToArray(false, output);
  }
  int GetCachedSize() const { return _cached_size_; }
  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const;
  void InternalSwap(SettingRealsFromPositiveInts* other);
  void UnsafeMergeFrom(const SettingRealsFromPositiveInts& from);
  private:
  inline ::google::protobuf::Arena* GetArenaNoVirtual() const {
    return _internal_metadata_.arena();
  }
  inline void* MaybeArenaPtr() const {
    return _internal_metadata_.raw_arena_ptr();
  }
  public:

  ::google::protobuf::Metadata GetMetadata() const;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // @@protoc_insertion_point(class_scope:protobuf_unittest.SettingRealsFromPositiveInts)
 private:

  ::google::protobuf::internal::InternalMetadataWithArena _internal_metadata_;
  ::google::protobuf::internal::HasBits<1> _has_bits_;
  mutable int _cached_size_;
  friend void  protobuf_InitDefaults_google_2fprotobuf_2funittest_5fcustom_5foptions_2eproto_impl();
  friend void  protobuf_AddDesc_google_2fprotobuf_2funittest_5fcustom_5foptions_2eproto_impl();
  friend void protobuf_AssignDesc_google_2fprotobuf_2funittest_5fcustom_5foptions_2eproto();
  friend void protobuf_ShutdownFile_google_2fprotobuf_2funittest_5fcustom_5foptions_2eproto();

  void InitAsDefaultInstance();
};
extern ::google::protobuf::internal::ExplicitlyConstructed<SettingRealsFromPositiveInts> SettingRealsFromPositiveInts_default_instance_;

// -------------------------------------------------------------------

class SettingRealsFromNegativeInts : public ::google::protobuf::Message /* @@protoc_insertion_point(class_definition:protobuf_unittest.SettingRealsFromNegativeInts) */ {
 public:
  SettingRealsFromNegativeInts();
  virtual ~SettingRealsFromNegativeInts();

  SettingRealsFromNegativeInts(const SettingRealsFromNegativeInts& from);

  inline SettingRealsFromNegativeInts& operator=(const SettingRealsFromNegativeInts& from) {
    CopyFrom(from);
    return *this;
  }

  inline const ::google::protobuf::UnknownFieldSet& unknown_fields() const {
    return _internal_metadata_.unknown_fields();
  }

  inline ::google::protobuf::UnknownFieldSet* mutable_unknown_fields() {
    return _internal_metadata_.mutable_unknown_fields();
  }

  static const ::google::protobuf::Descriptor* descriptor();
  static const SettingRealsFromNegativeInts& default_instance();

  static const SettingRealsFromNegativeInts* internal_default_instance();

  void Swap(SettingRealsFromNegativeInts* other);

  // implements Message ----------------------------------------------

  inline SettingRealsFromNegativeInts* New() const { return New(NULL); }

  SettingRealsFromNegativeInts* New(::google::protobuf::Arena* arena) const;
  void CopyFrom(const ::google::protobuf::Message& from);
  void MergeFrom(const ::google::protobuf::Message& from);
  void CopyFrom(const SettingRealsFromNegativeInts& from);
  void MergeFrom(const SettingRealsFromNegativeInts& from);
  void Clear();
  bool IsInitialized() const;

  size_t ByteSizeLong() const;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input);
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const;
  ::google::protobuf::uint8* InternalSerializeWithCachedSizesToArray(
      bool deterministic, ::google::protobuf::uint8* output) const;
  ::google::protobuf::uint8* SerializeWithCachedSizesToArray(::google::protobuf::uint8* output) const {
    return InternalSerializeWithCachedSizesToArray(false, output);
  }
  int GetCachedSize() const { return _cached_size_; }
  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const;
  void InternalSwap(SettingRealsFromNegativeInts* other);
  void UnsafeMergeFrom(const SettingRealsFromNegativeInts& from);
  private:
  inline ::google::protobuf::Arena* GetArenaNoVirtual() const {
    return _internal_metadata_.arena();
  }
  inline void* MaybeArenaPtr() const {
    return _internal_metadata_.raw_arena_ptr();
  }
  public:

  ::google::protobuf::Metadata GetMetadata() const;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // @@protoc_insertion_point(class_scope:protobuf_unittest.SettingRealsFromNegativeInts)
 private:

  ::google::protobuf::internal::InternalMetadataWithArena _internal_metadata_;
  ::google::protobuf::internal::HasBits<1> _has_bits_;
  mutable int _cached_size_;
  friend void  protobuf_InitDefaults_google_2fprotobuf_2funittest_5fcustom_5foptions_2eproto_impl();
  friend void  protobuf_AddDesc_google_2fprotobuf_2funittest_5fcustom_5foptions_2eproto_impl();
  friend void protobuf_AssignDesc_google_2fprotobuf_2funittest_5fcustom_5foptions_2eproto();
  friend void protobuf_ShutdownFile_google_2fprotobuf_2funittest_5fcustom_5foptions_2eproto();

  void InitAsDefaultInstance();
};
extern ::google::protobuf::internal::ExplicitlyConstructed<SettingRealsFromNegativeInts> SettingRealsFromNegativeInts_default_instance_;

// -------------------------------------------------------------------

class ComplexOptionType1 : public ::google::protobuf::Message /* @@protoc_insertion_point(class_definition:protobuf_unittest.ComplexOptionType1) */ {
 public:
  ComplexOptionType1();
  virtual ~ComplexOptionType1();

  ComplexOptionType1(const ComplexOptionType1& from);

  inline ComplexOptionType1& operator=(const ComplexOptionType1& from) {
    CopyFrom(from);
    return *this;
  }

  inline const ::google::protobuf::UnknownFieldSet& unknown_fields() const {
    return _internal_metadata_.unknown_fields();
  }

  inline ::google::protobuf::UnknownFieldSet* mutable_unknown_fields() {
    return _internal_metadata_.mutable_unknown_fields();
  }

  static const ::google::protobuf::Descriptor* descriptor();
  static const ComplexOptionType1& default_instance();

  static const ComplexOptionType1* internal_default_instance();

  void Swap(ComplexOptionType1* other);

  // implements Message ----------------------------------------------

  inline ComplexOptionType1* New() const { return New(NULL); }

  ComplexOptionType1* New(::google::protobuf::Arena* arena) const;
  void CopyFrom(const ::google::protobuf::Message& from);
  void MergeFrom(const ::google::protobuf::Message& from);
  void CopyFrom(const ComplexOptionType1& from);
  void MergeFrom(const ComplexOptionType1& from);
  void Clear();
  bool IsInitialized() const;

  size_t ByteSizeLong() const;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input);
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const;
  ::google::protobuf::uint8* InternalSerializeWithCachedSizesToArray(
      bool deterministic, ::google::protobuf::uint8* output) const;
  ::google::protobuf::uint8* SerializeWithCachedSizesToArray(::google::protobuf::uint8* output) const {
    return InternalSerializeWithCachedSizesToArray(false, output);
  }
  int GetCachedSize() const { return _cached_size_; }
  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const;
  void InternalSwap(ComplexOptionType1* other);
  void UnsafeMergeFrom(const ComplexOptionType1& from);
  private:
  inline ::google::protobuf::Arena* GetArenaNoVirtual() const {
    return _internal_metadata_.arena();
  }
  inline void* MaybeArenaPtr() const {
    return _internal_metadata_.raw_arena_ptr();
  }
  public:

  ::google::protobuf::Metadata GetMetadata() const;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // optional int32 foo = 1;
  bool has_foo() const;
  void clear_foo();
  static const int kFooFieldNumber = 1;
  ::google::protobuf::int32 foo() const;
  void set_foo(::google::protobuf::int32 value);

  // optional int32 foo2 = 2;
  bool has_foo2() const;
  void clear_foo2();
  static const int kFoo2FieldNumber = 2;
  ::google::protobuf::int32 foo2() const;
  void set_foo2(::google::protobuf::int32 value);

  // optional int32 foo3 = 3;
  bool has_foo3() const;
  void clear_foo3();
  static const int kFoo3FieldNumber = 3;
  ::google::protobuf::int32 foo3() const;
  void set_foo3(::google::protobuf::int32 value);

  // repeated int32 foo4 = 4;
  int foo4_size() const;
  void clear_foo4();
  static const int kFoo4FieldNumber = 4;
  ::google::protobuf::int32 foo4(int index) const;
  void set_foo4(int index, ::google::protobuf::int32 value);
  void add_foo4(::google::protobuf::int32 value);
  const ::google::protobuf::RepeatedField< ::google::protobuf::int32 >&
      foo4() const;
  ::google::protobuf::RepeatedField< ::google::protobuf::int32 >*
      mutable_foo4();

  GOOGLE_PROTOBUF_EXTENSION_ACCESSORS(ComplexOptionType1)
  // @@protoc_insertion_point(class_scope:protobuf_unittest.ComplexOptionType1)
 private:
  inline void set_has_foo();
  inline void clear_has_foo();
  inline void set_has_foo2();
  inline void clear_has_foo2();
  inline void set_has_foo3();
  inline void clear_has_foo3();

  ::google::protobuf::internal::ExtensionSet _extensions_;

  ::google::protobuf::internal::InternalMetadataWithArena _internal_metadata_;
  ::google::protobuf::internal::HasBits<1> _has_bits_;
  mutable int _cached_size_;
  ::google::protobuf::RepeatedField< ::google::protobuf::int32 > foo4_;
  ::google::protobuf::int32 foo_;
  ::google::protobuf::int32 foo2_;
  ::google::protobuf::int32 foo3_;
  friend void  protobuf_InitDefaults_google_2fprotobuf_2funittest_5fcustom_5foptions_2eproto_impl();
  friend void  protobuf_AddDesc_google_2fprotobuf_2funittest_5fcustom_5foptions_2eproto_impl();
  friend void protobuf_AssignDesc_google_2fprotobuf_2funittest_5fcustom_5foptions_2eproto();
  friend void protobuf_ShutdownFile_google_2fprotobuf_2funittest_5fcustom_5foptions_2eproto();

  void InitAsDefaultInstance();
};
extern ::google::protobuf::internal::ExplicitlyConstructed<ComplexOptionType1> ComplexOptionType1_default_instance_;

// -------------------------------------------------------------------

class ComplexOptionType2_ComplexOptionType4 : public ::google::protobuf::Message /* @@protoc_insertion_point(class_definition:protobuf_unittest.ComplexOptionType2.ComplexOptionType4) */ {
 public:
  ComplexOptionType2_ComplexOptionType4();
  virtual ~ComplexOptionType2_ComplexOptionType4();

  ComplexOptionType2_ComplexOptionType4(const ComplexOptionType2_ComplexOptionType4& from);

  inline ComplexOptionType2_ComplexOptionType4& operator=(const ComplexOptionType2_ComplexOptionType4& from) {
    CopyFrom(from);
    return *this;
  }

  inline const ::google::protobuf::UnknownFieldSet& unknown_fields() const {
    return _internal_metadata_.unknown_fields();
  }

  inline ::google::protobuf::UnknownFieldSet* mutable_unknown_fields() {
    return _internal_metadata_.mutable_unknown_fields();
  }

  static const ::google::protobuf::Descriptor* descriptor();
  static const ComplexOptionType2_ComplexOptionType4& default_instance();

  static const ComplexOptionType2_ComplexOptionType4* internal_default_instance();

  void Swap(ComplexOptionType2_ComplexOptionType4* other);

  // implements Message ----------------------------------------------

  inline ComplexOptionType2_ComplexOptionType4* New() const { return New(NULL); }

  ComplexOptionType2_ComplexOptionType4* New(::google::protobuf::Arena* arena) const;
  void CopyFrom(const ::google::protobuf::Message& from);
  void MergeFrom(const ::google::protobuf::Message& from);
  void CopyFrom(const ComplexOptionType2_ComplexOptionType4& from);
  void MergeFrom(const ComplexOptionType2_ComplexOptionType4& from);
  void Clear();
  bool IsInitialized() const;

  size_t ByteSizeLong() const;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input);
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const;
  ::google::protobuf::uint8* InternalSerializeWithCachedSizesToArray(
      bool deterministic, ::google::protobuf::uint8* output) const;
  ::google::protobuf::uint8* SerializeWithCachedSizesToArray(::google::protobuf::uint8* output) const {
    return InternalSerializeWithCachedSizesToArray(false, output);
  }
  int GetCachedSize() const { return _cached_size_; }
  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const;
  void InternalSwap(ComplexOptionType2_ComplexOptionType4* other);
  void UnsafeMergeFrom(const ComplexOptionType2_ComplexOptionType4& from);
  private:
  inline ::google::protobuf::Arena* GetArenaNoVirtual() const {
    return _internal_metadata_.arena();
  }
  inline void* MaybeArenaPtr() const {
    return _internal_metadata_.raw_arena_ptr();
  }
  public:

  ::google::protobuf::Metadata GetMetadata() const;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // optional int32 waldo = 1;
  bool has_waldo() const;
  void clear_waldo();
  static const int kWaldoFieldNumber = 1;
  ::google::protobuf::int32 waldo() const;
  void set_waldo(::google::protobuf::int32 value);

  static const int kComplexOpt4FieldNumber = 7633546;
  static ::google::protobuf::internal::ExtensionIdentifier< ::google::protobuf::MessageOptions,
      ::google::protobuf::internal::MessageTypeTraits< ::protobuf_unittest::ComplexOptionType2_ComplexOptionType4 >, 11, false >
    complex_opt4;
  // @@protoc_insertion_point(class_scope:protobuf_unittest.ComplexOptionType2.ComplexOptionType4)
 private:
  inline void set_has_waldo();
  inline void clear_has_waldo();

  ::google::protobuf::internal::InternalMetadataWithArena _internal_metadata_;
  ::google::protobuf::internal::HasBits<1> _has_bits_;
  mutable int _cached_size_;
  ::google::protobuf::int32 waldo_;
  friend void  protobuf_InitDefaults_google_2fprotobuf_2funittest_5fcustom_5foptions_2eproto_impl();
  friend void  protobuf_AddDesc_google_2fprotobuf_2funittest_5fcustom_5foptions_2eproto_impl();
  friend void protobuf_AssignDesc_google_2fprotobuf_2funittest_5fcustom_5foptions_2eproto();
  friend void protobuf_ShutdownFile_google_2fprotobuf_2funittest_5fcustom_5foptions_2eproto();

  void InitAsDefaultInstance();
};
extern ::google::protobuf::internal::ExplicitlyConstructed<ComplexOptionType2_ComplexOptionType4> ComplexOptionType2_ComplexOptionType4_default_instance_;

// -------------------------------------------------------------------

class ComplexOptionType2 : public ::google::protobuf::Message /* @@protoc_insertion_point(class_definition:protobuf_unittest.ComplexOptionType2) */ {
 public:
  ComplexOptionType2();
  virtual ~ComplexOptionType2();

  ComplexOptionType2(const ComplexOptionType2& from);

  inline ComplexOptionType2& operator=(const ComplexOptionType2& from) {
    CopyFrom(from);
    return *this;
  }

  inline const ::google::protobuf::UnknownFieldSet& unknown_fields() const {
    return _internal_metadata_.unknown_fields();
  }

  inline ::google::protobuf::UnknownFieldSet* mutable_unknown_fields() {
    return _internal_metadata_.mutable_unknown_fields();
  }

  static const ::google::protobuf::Descriptor* descriptor();
  static const ComplexOptionType2& default_instance();

  static const ComplexOptionType2* internal_default_instance();

  void Swap(ComplexOptionType2* other);

  // implements Message ----------------------------------------------

  inline ComplexOptionType2* New() const { return New(NULL); }

  ComplexOptionType2* New(::google::protobuf::Arena* arena) const;
  void CopyFrom(const ::google::protobuf::Message& from);
  void MergeFrom(const ::google::protobuf::Message& from);
  void CopyFrom(const ComplexOptionType2& from);
  void MergeFrom(const ComplexOptionType2& from);
  void Clear();
  bool IsInitialized() const;

  size_t ByteSizeLong() const;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input);
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const;
  ::google::protobuf::uint8* InternalSerializeWithCachedSizesToArray(
      bool deterministic, ::google::protobuf::uint8* output) const;
  ::google::protobuf::uint8* SerializeWithCachedSizesToArray(::google::protobuf::uint8* output) const {
    return InternalSerializeWithCachedSizesToArray(false, output);
  }
  int GetCachedSize() const { return _cached_size_; }
  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const;
  void InternalSwap(ComplexOptionType2* other);
  void UnsafeMergeFrom(const ComplexOptionType2& from);
  private:
  inline ::google::protobuf::Arena* GetArenaNoVirtual() const {
    return _internal_metadata_.arena();
  }
  inline void* MaybeArenaPtr() const {
    return _internal_metadata_.raw_arena_ptr();
  }
  public:

  ::google::protobuf::Metadata GetMetadata() const;

  // nested types ----------------------------------------------------

  typedef ComplexOptionType2_ComplexOptionType4 ComplexOptionType4;

  // accessors -------------------------------------------------------

  // optional .protobuf_unittest.ComplexOptionType1 bar = 1;
  bool has_bar() const;
  void clear_bar();
  static const int kBarFieldNumber = 1;
  const ::protobuf_unittest::ComplexOptionType1& bar() const;
  ::protobuf_unittest::ComplexOptionType1* mutable_bar();
  ::protobuf_unittest::ComplexOptionType1* release_bar();
  void set_allocated_bar(::protobuf_unittest::ComplexOptionType1* bar);

  // optional int32 baz = 2;
  bool has_baz() const;
  void clear_baz();
  static const int kBazFieldNumber = 2;
  ::google::protobuf::int32 baz() const;
  void set_baz(::google::protobuf::int32 value);

  // optional .protobuf_unittest.ComplexOptionType2.ComplexOptionType4 fred = 3;
  bool has_fred() const;
  void clear_fred();
  static const int kFredFieldNumber = 3;
  const ::protobuf_unittest::ComplexOptionType2_ComplexOptionType4& fred() const;
  ::protobuf_unittest::ComplexOptionType2_ComplexOptionType4* mutable_fred();
  ::protobuf_unittest::ComplexOptionType2_ComplexOptionType4* release_fred();
  void set_allocated_fred(::protobuf_unittest::ComplexOptionType2_ComplexOptionType4* fred);

  // repeated .protobuf_unittest.ComplexOptionType2.ComplexOptionType4 barney = 4;
  int barney_size() const;
  void clear_barney();
  static const int kBarneyFieldNumber = 4;
  const ::protobuf_unittest::ComplexOptionType2_ComplexOptionType4& barney(int index) const;
  ::protobuf_unittest::ComplexOptionType2_ComplexOptionType4* mutable_barney(int index);
  ::protobuf_unittest::ComplexOptionType2_ComplexOptionType4* add_barney();
  ::google::protobuf::RepeatedPtrField< ::protobuf_unittest::ComplexOptionType2_ComplexOptionType4 >*
      mutable_barney();
  const ::google::protobuf::RepeatedPtrField< ::protobuf_unittest::ComplexOptionType2_ComplexOptionType4 >&
      barney() const;

  GOOGLE_PROTOBUF_EXTENSION_ACCESSORS(ComplexOptionType2)
  // @@protoc_insertion_point(class_scope:protobuf_unittest.ComplexOptionType2)
 private:
  inline void set_has_bar();
  inline void clear_has_bar();
  inline void set_has_baz();
  inline void clear_has_baz();
  inline void set_has_fred();
  inline void clear_has_fred();

  ::google::protobuf::internal::ExtensionSet _extensions_;

  ::google::protobuf::internal::InternalMetadataWithArena _internal_metadata_;
  ::google::protobuf::internal::HasBits<1> _has_bits_;
  mutable int _cached_size_;
  ::google::protobuf::RepeatedPtrField< ::protobuf_unittest::ComplexOptionType2_ComplexOptionType4 > barney_;
  ::protobuf_unittest::ComplexOptionType1* bar_;
  ::protobuf_unittest::ComplexOptionType2_ComplexOptionType4* fred_;
  ::google::protobuf::int32 baz_;
  friend void  protobuf_InitDefaults_google_2fprotobuf_2funittest_5fcustom_5foptions_2eproto_impl();
  friend void  protobuf_AddDesc_google_2fprotobuf_2funittest_5fcustom_5foptions_2eproto_impl();
  friend void protobuf_AssignDesc_google_2fprotobuf_2funittest_5fcustom_5foptions_2eproto();
  friend void protobuf_ShutdownFile_google_2fprotobuf_2funittest_5fcustom_5foptions_2eproto();

  void InitAsDefaultInstance();
};
extern ::google::protobuf::internal::ExplicitlyConstructed<ComplexOptionType2> ComplexOptionType2_default_instance_;

// -------------------------------------------------------------------

class ComplexOptionType3_ComplexOptionType5 : public ::google::protobuf::Message /* @@protoc_insertion_point(class_definition:protobuf_unittest.ComplexOptionType3.ComplexOptionType5) */ {
 public:
  ComplexOptionType3_ComplexOptionType5();
  virtual ~ComplexOptionType3_ComplexOptionType5();

  ComplexOptionType3_ComplexOptionType5(const ComplexOptionType3_ComplexOptionType5& from);

  inline ComplexOptionType3_ComplexOptionType5& operator=(const ComplexOptionType3_ComplexOptionType5& from) {
    CopyFrom(from);
    return *this;
  }

  inline const ::google::protobuf::UnknownFieldSet& unknown_fields() const {
    return _internal_metadata_.unknown_fields();
  }

  inline ::google::protobuf::UnknownFieldSet* mutable_unknown_fields() {
    return _internal_metadata_.mutable_unknown_fields();
  }

  static const ::google::protobuf::Descriptor* descriptor();
  static const ComplexOptionType3_ComplexOptionType5& default_instance();

  static const ComplexOptionType3_ComplexOptionType5* internal_default_instance();

  void Swap(ComplexOptionType3_ComplexOptionType5* other);

  // implements Message ----------------------------------------------

  inline ComplexOptionType3_ComplexOptionType5* New() const { return New(NULL); }

  ComplexOptionType3_ComplexOptionType5* New(::google::protobuf::Arena* arena) const;
  void CopyFrom(const ::google::protobuf::Message& from);
  void MergeFrom(const ::google::protobuf::Message& from);
  void CopyFrom(const ComplexOptionType3_ComplexOptionType5& from);
  void MergeFrom(const ComplexOptionType3_ComplexOptionType5& from);
  void Clear();
  bool IsInitialized() const;

  size_t ByteSizeLong() const;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input);
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const;
  ::google::protobuf::uint8* InternalSerializeWithCachedSizesToArray(
      bool deterministic, ::google::protobuf::uint8* output) const;
  ::google::protobuf::uint8* SerializeWithCachedSizesToArray(::google::protobuf::uint8* output) const {
    return InternalSerializeWithCachedSizesToArray(false, output);
  }
  int GetCachedSize() const { return _cached_size_; }
  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const;
  void InternalSwap(ComplexOptionType3_ComplexOptionType5* other);
  void UnsafeMergeFrom(const ComplexOptionType3_ComplexOptionType5& from);
  private:
  inline ::google::protobuf::Arena* GetArenaNoVirtual() const {
    return _internal_metadata_.arena();
  }
  inline void* MaybeArenaPtr() const {
    return _internal_metadata_.raw_arena_ptr();
  }
  public:

  ::google::protobuf::Metadata GetMetadata() const;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // optional int32 plugh = 3;
  bool has_plugh() const;
  void clear_plugh();
  static const int kPlughFieldNumber = 3;
  ::google::protobuf::int32 plugh() const;
  void set_plugh(::google::protobuf::int32 value);

  // @@protoc_insertion_point(class_scope:protobuf_unittest.ComplexOptionType3.ComplexOptionType5)
 private:
  inline void set_has_plugh();
  inline void clear_has_plugh();

  ::google::protobuf::internal::InternalMetadataWithArena _internal_metadata_;
  ::google::protobuf::internal::HasBits<1> _has_bits_;
  mutable int _cached_size_;
  ::google::protobuf::int32 plugh_;
  friend void  protobuf_InitDefaults_google_2fprotobuf_2funittest_5fcustom_5foptions_2eproto_impl();
  friend void  protobuf_AddDesc_google_2fprotobuf_2funittest_5fcustom_5foptions_2eproto_impl();
  friend void protobuf_AssignDesc_google_2fprotobuf_2funittest_5fcustom_5foptions_2eproto();
  friend void protobuf_ShutdownFile_google_2fprotobuf_2funittest_5fcustom_5foptions_2eproto();

  void InitAsDefaultInstance();
};
extern ::google::protobuf::internal::ExplicitlyConstructed<ComplexOptionType3_ComplexOptionType5> ComplexOptionType3_ComplexOptionType5_default_instance_;

// -------------------------------------------------------------------

class ComplexOptionType3 : public ::google::protobuf::Message /* @@protoc_insertion_point(class_definition:protobuf_unittest.ComplexOptionType3) */ {
 public:
  ComplexOptionType3();
  virtual ~ComplexOptionType3();

  ComplexOptionType3(const ComplexOptionType3& from);

  inline ComplexOptionType3& operator=(const ComplexOptionType3& from) {
    CopyFrom(from);
    return *this;
  }

  inline const ::google::protobuf::UnknownFieldSet& unknown_fields() const {
    return _internal_metadata_.unknown_fields();
  }

  inline ::google::protobuf::UnknownFieldSet* mutable_unknown_fields() {
    return _internal_metadata_.mutable_unknown_fields();
  }

  static const ::google::protobuf::Descriptor* descriptor();
  static const ComplexOptionType3& default_instance();

  static const ComplexOptionType3* internal_default_instance();

  void Swap(ComplexOptionType3* other);

  // implements Message ----------------------------------------------

  inline ComplexOptionType3* New() const { return New(NULL); }

  ComplexOptionType3* New(::google::protobuf::Arena* arena) const;
  void CopyFrom(const ::google::protobuf::Message& from);
  void MergeFrom(const ::google::protobuf::Message& from);
  void CopyFrom(const ComplexOptionType3& from);
  void MergeFrom(const ComplexOptionType3& from);
  void Clear();
  bool IsInitialized() const;

  size_t ByteSizeLong() const;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input);
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const;
  ::google::protobuf::uint8* InternalSerializeWithCachedSizesToArray(
      bool deterministic, ::google::protobuf::uint8* output) const;
  ::google::protobuf::uint8* SerializeWithCachedSizesToArray(::google::protobuf::uint8* output) const {
    return InternalSerializeWithCachedSizesToArray(false, output);
  }
  int GetCachedSize() const { return _cached_size_; }
  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const;
  void InternalSwap(ComplexOptionType3* other);
  void UnsafeMergeFrom(const ComplexOptionType3& from);
  private:
  inline ::google::protobuf::Arena* GetArenaNoVirtual() const {
    return _internal_metadata_.arena();
  }
  inline void* MaybeArenaPtr() const {
    return _internal_metadata_.raw_arena_ptr();
  }
  public:

  ::google::protobuf::Metadata GetMetadata() const;

  // nested types ----------------------------------------------------

  typedef ComplexOptionType3_ComplexOptionType5 ComplexOptionType5;

  // accessors -------------------------------------------------------

  // optional int32 qux = 1;
  bool has_qux() const;
  void clear_qux();
  static const int kQuxFieldNumber = 1;
  ::google::protobuf::int32 qux() const;
  void set_qux(::google::protobuf::int32 value);

  // optional group ComplexOptionType5 = 2 { ... };
  bool has_complexoptiontype5() const;
  void clear_complexoptiontype5();
  static const int kComplexoptiontype5FieldNumber = 2;
  const ::protobuf_unittest::ComplexOptionType3_ComplexOptionType5& complexoptiontype5() const;
  ::protobuf_unittest::ComplexOptionType3_ComplexOptionType5* mutable_complexoptiontype5();
  ::protobuf_unittest::ComplexOptionType3_ComplexOptionType5* release_complexoptiontype5();
  void set_allocated_complexoptiontype5(::protobuf_unittest::ComplexOptionType3_ComplexOptionType5* complexoptiontype5);

  // @@protoc_insertion_point(class_scope:protobuf_unittest.ComplexOptionType3)
 private:
  inline void set_has_qux();
  inline void clear_has_qux();
  inline void set_has_complexoptiontype5();
  inline void clear_has_complexoptiontype5();

  ::google::protobuf::internal::InternalMetadataWithArena _internal_metadata_;
  ::google::protobuf::internal::HasBits<1> _has_bits_;
  mutable int _cached_size_;
  ::protobuf_unittest::ComplexOptionType3_ComplexOptionType5* complexoptiontype5_;
  ::google::protobuf::int32 qux_;
  friend void  protobuf_InitDefaults_google_2fprotobuf_2funittest_5fcustom_5foptions_2eproto_impl();
  friend void  protobuf_AddDesc_google_2fprotobuf_2funittest_5fcustom_5foptions_2eproto_impl();
  friend void protobuf_AssignDesc_google_2fprotobuf_2funittest_5fcustom_5foptions_2eproto();
  friend void protobuf_ShutdownFile_google_2fprotobuf_2funittest_5fcustom_5foptions_2eproto();

  void InitAsDefaultInstance();
};
extern ::google::protobuf::internal::ExplicitlyConstructed<ComplexOptionType3> ComplexOptionType3_default_instance_;

// -------------------------------------------------------------------

class ComplexOpt6 : public ::google::protobuf::Message /* @@protoc_insertion_point(class_definition:protobuf_unittest.ComplexOpt6) */ {
 public:
  ComplexOpt6();
  virtual ~ComplexOpt6();

  ComplexOpt6(const ComplexOpt6& from);

  inline ComplexOpt6& operator=(const ComplexOpt6& from) {
    CopyFrom(from);
    return *this;
  }

  inline const ::google::protobuf::UnknownFieldSet& unknown_fields() const {
    return _internal_metadata_.unknown_fields();
  }

  inline ::google::protobuf::UnknownFieldSet* mutable_unknown_fields() {
    return _internal_metadata_.mutable_unknown_fields();
  }

  static const ::google::protobuf::Descriptor* descriptor();
  static const ComplexOpt6& default_instance();

  static const ComplexOpt6* internal_default_instance();

  void Swap(ComplexOpt6* other);

  // implements Message ----------------------------------------------

  inline ComplexOpt6* New() const { return New(NULL); }

  ComplexOpt6* New(::google::protobuf::Arena* arena) const;
  void CopyFrom(const ::google::protobuf::Message& from);
  void MergeFrom(const ::google::protobuf::Message& from);
  void CopyFrom(const ComplexOpt6& from);
  void MergeFrom(const ComplexOpt6& from);
  void Clear();
  bool IsInitialized() const;

  size_t ByteSizeLong() const;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input);
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const;
  ::google::protobuf::uint8* InternalSerializeWithCachedSizesToArray(
      bool deterministic, ::google::protobuf::uint8* output) const;
  ::google::protobuf::uint8* SerializeWithCachedSizesToArray(::google::protobuf::uint8* output) const {
    return InternalSerializeWithCachedSizesToArray(false, output);
  }
  int GetCachedSize() const { return _cached_size_; }
  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const;
  void InternalSwap(ComplexOpt6* other);
  void UnsafeMergeFrom(const ComplexOpt6& from);
  private:
  inline ::google::protobuf::Arena* GetArenaNoVirtual() const {
    return _internal_metadata_.arena();
  }
  inline void* MaybeArenaPtr() const {
    return _internal_metadata_.raw_arena_ptr();
  }
  public:

  ::google::protobuf::Metadata GetMetadata() const;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // optional int32 xyzzy = 7593951;
  bool has_xyzzy() const;
  void clear_xyzzy();
  static const int kXyzzyFieldNumber = 7593951;
  ::google::protobuf::int32 xyzzy() const;
  void set_xyzzy(::google::protobuf::int32 value);

  // @@protoc_insertion_point(class_scope:protobuf_unittest.ComplexOpt6)
 private:
  inline void set_has_xyzzy();
  inline void clear_has_xyzzy();

  ::google::protobuf::internal::InternalMetadataWithArena _internal_metadata_;
  ::google::protobuf::internal::HasBits<1> _has_bits_;
  mutable int _cached_size_;
  ::google::protobuf::int32 xyzzy_;
  friend void  protobuf_InitDefaults_google_2fprotobuf_2funittest_5fcustom_5foptions_2eproto_impl();
  friend void  protobuf_AddDesc_google_2fprotobuf_2funittest_5fcustom_5foptions_2eproto_impl();
  friend void protobuf_AssignDesc_google_2fprotobuf_2funittest_5fcustom_5foptions_2eproto();
  friend void protobuf_ShutdownFile_google_2fprotobuf_2funittest_5fcustom_5foptions_2eproto();

  void InitAsDefaultInstance();
};
extern ::google::protobuf::internal::ExplicitlyConstructed<ComplexOpt6> ComplexOpt6_default_instance_;

// -------------------------------------------------------------------

class VariousComplexOptions : public ::google::protobuf::Message /* @@protoc_insertion_point(class_definition:protobuf_unittest.VariousComplexOptions) */ {
 public:
  VariousComplexOptions();
  virtual ~VariousComplexOptions();

  VariousComplexOptions(const VariousComplexOptions& from);

  inline VariousComplexOptions& operator=(const VariousComplexOptions& from) {
    CopyFrom(from);
    return *this;
  }

  inline const ::google::protobuf::UnknownFieldSet& unknown_fields() const {
    return _internal_metadata_.unknown_fields();
  }

  inline ::google::protobuf::UnknownFieldSet* mutable_unknown_fields() {
    return _internal_metadata_.mutable_unknown_fields();
  }

  static const ::google::protobuf::Descriptor* descriptor();
  static const VariousComplexOptions& default_instance();

  static const VariousComplexOptions* internal_default_instance();

  void Swap(VariousComplexOptions* other);

  // implements Message ----------------------------------------------

  inline VariousComplexOptions* New() const { return New(NULL); }

  VariousComplexOptions* New(::google::protobuf::Arena* arena) const;
  void CopyFrom(const ::google::protobuf::Message& from);
  void MergeFrom(const ::google::protobuf::Message& from);
  void CopyFrom(const VariousComplexOptions& from);
  void MergeFrom(const VariousComplexOptions& from);
  void Clear();
  bool IsInitialized() const;

  size_t ByteSizeLong() const;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input);
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const;
  ::google::protobuf::uint8* InternalSerializeWithCachedSizesToArray(
      bool deterministic, ::google::protobuf::uint8* output) const;
  ::google::protobuf::uint8* SerializeWithCachedSizesToArray(::google::protobuf::uint8* output) const {
    return InternalSerializeWithCachedSizesToArray(false, output);
  }
  int GetCachedSize() const { return _cached_size_; }
  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const;
  void InternalSwap(VariousComplexOptions* other);
  void UnsafeMergeFrom(const VariousComplexOptions& from);
  private:
  inline ::google::protobuf::Arena* GetArenaNoVirtual() const {
    return _internal_metadata_.arena();
  }
  inline void* MaybeArenaPtr() const {
    return _internal_metadata_.raw_arena_ptr();
  }
  public:

  ::google::protobuf::Metadata GetMetadata() const;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // @@protoc_insertion_point(class_scope:protobuf_unittest.VariousComplexOptions)
 private:

  ::google::protobuf::internal::InternalMetadataWithArena _internal_metadata_;
  ::google::protobuf::internal::HasBits<1> _has_bits_;
  mutable int _cached_size_;
  friend void  protobuf_InitDefaults_google_2fprotobuf_2funittest_5fcustom_5foptions_2eproto_impl();
  friend void  protobuf_AddDesc_google_2fprotobuf_2funittest_5fcustom_5foptions_2eproto_impl();
  friend void protobuf_AssignDesc_google_2fprotobuf_2funittest_5fcustom_5foptions_2eproto();
  friend void protobuf_ShutdownFile_google_2fprotobuf_2funittest_5fcustom_5foptions_2eproto();

  void InitAsDefaultInstance();
};
extern ::google::protobuf::internal::ExplicitlyConstructed<VariousComplexOptions> VariousComplexOptions_default_instance_;

// -------------------------------------------------------------------

class AggregateMessageSet : public ::google::protobuf::Message /* @@protoc_insertion_point(class_definition:protobuf_unittest.AggregateMessageSet) */ {
 public:
  AggregateMessageSet();
  virtual ~AggregateMessageSet();

  AggregateMessageSet(const AggregateMessageSet& from);

  inline AggregateMessageSet& operator=(const AggregateMessageSet& from) {
    CopyFrom(from);
    return *this;
  }

  inline const ::google::protobuf::UnknownFieldSet& unknown_fields() const {
    return _internal_metadata_.unknown_fields();
  }

  inline ::google::protobuf::UnknownFieldSet* mutable_unknown_fields() {
    return _internal_metadata_.mutable_unknown_fields();
  }

  static const ::google::protobuf::Descriptor* descriptor();
  static const AggregateMessageSet& default_instance();

  static const AggregateMessageSet* internal_default_instance();

  void Swap(AggregateMessageSet* other);

  // implements Message ----------------------------------------------

  inline AggregateMessageSet* New() const { return New(NULL); }

  AggregateMessageSet* New(::google::protobuf::Arena* arena) const;
  void CopyFrom(const ::google::protobuf::Message& from);
  void MergeFrom(const ::google::protobuf::Message& from);
  void CopyFrom(const AggregateMessageSet& from);
  void MergeFrom(const AggregateMessageSet& from);
  void Clear();
  bool IsInitialized() const;

  size_t ByteSizeLong() const;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input);
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const;
  ::google::protobuf::uint8* InternalSerializeWithCachedSizesToArray(
      bool deterministic, ::google::protobuf::uint8* output) const;
  ::google::protobuf::uint8* SerializeWithCachedSizesToArray(::google::protobuf::uint8* output) const {
    return InternalSerializeWithCachedSizesToArray(false, output);
  }
  int GetCachedSize() const { return _cached_size_; }
  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const;
  void InternalSwap(AggregateMessageSet* other);
  void UnsafeMergeFrom(const AggregateMessageSet& from);
  private:
  inline ::google::protobuf::Arena* GetArenaNoVirtual() const {
    return _internal_metadata_.arena();
  }
  inline void* MaybeArenaPtr() const {
    return _internal_metadata_.raw_arena_ptr();
  }
  public:

  ::google::protobuf::Metadata GetMetadata() const;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  GOOGLE_PROTOBUF_EXTENSION_ACCESSORS(AggregateMessageSet)
  // @@protoc_insertion_point(class_scope:protobuf_unittest.AggregateMessageSet)
 private:

  ::google::protobuf::internal::ExtensionSet _extensions_;

  ::google::protobuf::internal::InternalMetadataWithArena _internal_metadata_;
  ::google::protobuf::internal::HasBits<1> _has_bits_;
  mutable int _cached_size_;
  friend void  protobuf_InitDefaults_google_2fprotobuf_2funittest_5fcustom_5foptions_2eproto_impl();
  friend void  protobuf_AddDesc_google_2fprotobuf_2funittest_5fcustom_5foptions_2eproto_impl();
  friend void protobuf_AssignDesc_google_2fprotobuf_2funittest_5fcustom_5foptions_2eproto();
  friend void protobuf_ShutdownFile_google_2fprotobuf_2funittest_5fcustom_5foptions_2eproto();

  void InitAsDefaultInstance();
};
extern ::google::protobuf::internal::ExplicitlyConstructed<AggregateMessageSet> AggregateMessageSet_default_instance_;

// -------------------------------------------------------------------

class AggregateMessageSetElement : public ::google::protobuf::Message /* @@protoc_insertion_point(class_definition:protobuf_unittest.AggregateMessageSetElement) */ {
 public:
  AggregateMessageSetElement();
  virtual ~AggregateMessageSetElement();

  AggregateMessageSetElement(const AggregateMessageSetElement& from);

  inline AggregateMessageSetElement& operator=(const AggregateMessageSetElement& from) {
    CopyFrom(from);
    return *this;
  }

  inline const ::google::protobuf::UnknownFieldSet& unknown_fields() const {
    return _internal_metadata_.unknown_fields();
  }

  inline ::google::protobuf::UnknownFieldSet* mutable_unknown_fields() {
    return _internal_metadata_.mutable_unknown_fields();
  }

  static const ::google::protobuf::Descriptor* descriptor();
  static const AggregateMessageSetElement& default_instance();

  static const AggregateMessageSetElement* internal_default_instance();

  void Swap(AggregateMessageSetElement* other);

  // implements Message ----------------------------------------------

  inline AggregateMessageSetElement* New() const { return New(NULL); }

  AggregateMessageSetElement* New(::google::protobuf::Arena* arena) const;
  void CopyFrom(const ::google::protobuf::Message& from);
  void MergeFrom(const ::google::protobuf::Message& from);
  void CopyFrom(const AggregateMessageSetElement& from);
  void MergeFrom(const AggregateMessageSetElement& from);
  void Clear();
  bool IsInitialized() const;

  size_t ByteSizeLong() const;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input);
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const;
  ::google::protobuf::uint8* InternalSerializeWithCachedSizesToArray(
      bool deterministic, ::google::protobuf::uint8* output) const;
  ::google::protobuf::uint8* SerializeWithCachedSizesToArray(::google::protobuf::uint8* output) const {
    return InternalSerializeWithCachedSizesToArray(false, output);
  }
  int GetCachedSize() const { return _cached_size_; }
  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const;
  void InternalSwap(AggregateMessageSetElement* other);
  void UnsafeMergeFrom(const AggregateMessageSetElement& from);
  private:
  inline ::google::protobuf::Arena* GetArenaNoVirtual() const {
    return _internal_metadata_.arena();
  }
  inline void* MaybeArenaPtr() const {
    return _internal_metadata_.raw_arena_ptr();
  }
  public:

  ::google::protobuf::Metadata GetMetadata() const;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // optional string s = 1;
  bool has_s() const;
  void clear_s();
  static const int kSFieldNumber = 1;
  const ::std::string& s() const;
  void set_s(const ::std::string& value);
  void set_s(const char* value);
  void set_s(const char* value, size_t size);
  ::std::string* mutable_s();
  ::std::string* release_s();
  void set_allocated_s(::std::string* s);

  static const int kMessageSetExtensionFieldNumber = 15447542;
  static ::google::protobuf::internal::ExtensionIdentifier< ::protobuf_unittest::AggregateMessageSet,
      ::google::protobuf::internal::MessageTypeTraits< ::protobuf_unittest::AggregateMessageSetElement >, 11, false >
    message_set_extension;
  // @@protoc_insertion_point(class_scope:protobuf_unittest.AggregateMessageSetElement)
 private:
  inline void set_has_s();
  inline void clear_has_s();

  ::google::protobuf::internal::InternalMetadataWithArena _internal_metadata_;
  ::google::protobuf::internal::HasBits<1> _has_bits_;
  mutable int _cached_size_;
  ::google::protobuf::internal::ArenaStringPtr s_;
  friend void  protobuf_InitDefaults_google_2fprotobuf_2funittest_5fcustom_5foptions_2eproto_impl();
  friend void  protobuf_AddDesc_google_2fprotobuf_2funittest_5fcustom_5foptions_2eproto_impl();
  friend void protobuf_AssignDesc_google_2fprotobuf_2funittest_5fcustom_5foptions_2eproto();
  friend void protobuf_ShutdownFile_google_2fprotobuf_2funittest_5fcustom_5foptions_2eproto();

  void InitAsDefaultInstance();
};
extern ::google::protobuf::internal::ExplicitlyConstructed<AggregateMessageSetElement> AggregateMessageSetElement_default_instance_;

// -------------------------------------------------------------------

class Aggregate : public ::google::protobuf::Message /* @@protoc_insertion_point(class_definition:protobuf_unittest.Aggregate) */ {
 public:
  Aggregate();
  virtual ~Aggregate();

  Aggregate(const Aggregate& from);

  inline Aggregate& operator=(const Aggregate& from) {
    CopyFrom(from);
    return *this;
  }

  inline const ::google::protobuf::UnknownFieldSet& unknown_fields() const {
    return _internal_metadata_.unknown_fields();
  }

  inline ::google::protobuf::UnknownFieldSet* mutable_unknown_fields() {
    return _internal_metadata_.mutable_unknown_fields();
  }

  static const ::google::protobuf::Descriptor* descriptor();
  static const Aggregate& default_instance();

  static const Aggregate* internal_default_instance();

  void Swap(Aggregate* other);

  // implements Message ----------------------------------------------

  inline Aggregate* New() const { return New(NULL); }

  Aggregate* New(::google::protobuf::Arena* arena) const;
  void CopyFrom(const ::google::protobuf::Message& from);
  void MergeFrom(const ::google::protobuf::Message& from);
  void CopyFrom(const Aggregate& from);
  void MergeFrom(const Aggregate& from);
  void Clear();
  bool IsInitialized() const;

  size_t ByteSizeLong() const;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input);
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const;
  ::google::protobuf::uint8* InternalSerializeWithCachedSizesToArray(
      bool deterministic, ::google::protobuf::uint8* output) const;
  ::google::protobuf::uint8* SerializeWithCachedSizesToArray(::google::protobuf::uint8* output) const {
    return InternalSerializeWithCachedSizesToArray(false, output);
  }
  int GetCachedSize() const { return _cached_size_; }
  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const;
  void InternalSwap(Aggregate* other);
  void UnsafeMergeFrom(const Aggregate& from);
  private:
  inline ::google::protobuf::Arena* GetArenaNoVirtual() const {
    return _internal_metadata_.arena();
  }
  inline void* MaybeArenaPtr() const {
    return _internal_metadata_.raw_arena_ptr();
  }
  public:

  ::google::protobuf::Metadata GetMetadata() const;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // optional int32 i = 1;
  bool has_i() const;
  void clear_i();
  static const int kIFieldNumber = 1;
  ::google::protobuf::int32 i() const;
  void set_i(::google::protobuf::int32 value);

  // optional string s = 2;
  bool has_s() const;
  void clear_s();
  static const int kSFieldNumber = 2;
  const ::std::string& s() const;
  void set_s(const ::std::string& value);
  void set_s(const char* value);
  void set_s(const char* value, size_t size);
  ::std::string* mutable_s();
  ::std::string* release_s();
  void set_allocated_s(::std::string* s);

  // optional .protobuf_unittest.Aggregate sub = 3;
  bool has_sub() const;
  void clear_sub();
  static const int kSubFieldNumber = 3;
  const ::protobuf_unittest::Aggregate& sub() const;
  ::protobuf_unittest::Aggregate* mutable_sub();
  ::protobuf_unittest::Aggregate* release_sub();
  void set_allocated_sub(::protobuf_unittest::Aggregate* sub);

  // optional .google.protobuf.FileOptions file = 4;
  bool has_file() const;
  void clear_file();
  static const int kFileFieldNumber = 4;
  const ::google::protobuf::FileOptions& file() const;
  ::google::protobuf::FileOptions* mutable_file();
  ::google::protobuf::FileOptions* release_file();
  void set_allocated_file(::google::protobuf::FileOptions* file);

  // optional .protobuf_unittest.AggregateMessageSet mset = 5;
  bool has_mset() const;
  void clear_mset();
  static const int kMsetFieldNumber = 5;
  const ::protobuf_unittest::AggregateMessageSet& mset() const;
  ::protobuf_unittest::AggregateMessageSet* mutable_mset();
  ::protobuf_unittest::AggregateMessageSet* release_mset();
  void set_allocated_mset(::protobuf_unittest::AggregateMessageSet* mset);

  static const int kNestedFieldNumber = 15476903;
  static ::google::protobuf::internal::ExtensionIdentifier< ::google::protobuf::FileOptions,
      ::google::protobuf::internal::MessageTypeTraits< ::protobuf_unittest::Aggregate >, 11, false >
    nested;
  // @@protoc_insertion_point(class_scope:protobuf_unittest.Aggregate)
 private:
  inline void set_has_i();
  inline void clear_has_i();
  inline void set_has_s();
  inline void clear_has_s();
  inline void set_has_sub();
  inline void clear_has_sub();
  inline void set_has_file();
  inline void clear_has_file();
  inline void set_has_mset();
  inline void clear_has_mset();

  ::google::protobuf::internal::InternalMetadataWithArena _internal_metadata_;
  ::google::protobuf::internal::HasBits<1> _has_bits_;
  mutable int _cached_size_;
  ::google::protobuf::internal::ArenaStringPtr s_;
  ::protobuf_unittest::Aggregate* sub_;
  ::google::protobuf::FileOptions* file_;
  ::protobuf_unittest::AggregateMessageSet* mset_;
  ::google::protobuf::int32 i_;
  friend void  protobuf_InitDefaults_google_2fprotobuf_2funittest_5fcustom_5foptions_2eproto_impl();
  friend void  protobuf_AddDesc_google_2fprotobuf_2funittest_5fcustom_5foptions_2eproto_impl();
  friend void protobuf_AssignDesc_google_2fprotobuf_2funittest_5fcustom_5foptions_2eproto();
  friend void protobuf_ShutdownFile_google_2fprotobuf_2funittest_5fcustom_5foptions_2eproto();

  void InitAsDefaultInstance();
};
extern ::google::protobuf::internal::ExplicitlyConstructed<Aggregate> Aggregate_default_instance_;

// -------------------------------------------------------------------

class AggregateMessage : public ::google::protobuf::Message /* @@protoc_insertion_point(class_definition:protobuf_unittest.AggregateMessage) */ {
 public:
  AggregateMessage();
  virtual ~AggregateMessage();

  AggregateMessage(const AggregateMessage& from);

  inline AggregateMessage& operator=(const AggregateMessage& from) {
    CopyFrom(from);
    return *this;
  }

  inline const ::google::protobuf::UnknownFieldSet& unknown_fields() const {
    return _internal_metadata_.unknown_fields();
  }

  inline ::google::protobuf::UnknownFieldSet* mutable_unknown_fields() {
    return _internal_metadata_.mutable_unknown_fields();
  }

  static const ::google::protobuf::Descriptor* descriptor();
  static const AggregateMessage& default_instance();

  static const AggregateMessage* internal_default_instance();

  void Swap(AggregateMessage* other);

  // implements Message ----------------------------------------------

  inline AggregateMessage* New() const { return New(NULL); }

  AggregateMessage* New(::google::protobuf::Arena* arena) const;
  void CopyFrom(const ::google::protobuf::Message& from);
  void MergeFrom(const ::google::protobuf::Message& from);
  void CopyFrom(const AggregateMessage& from);
  void MergeFrom(const AggregateMessage& from);
  void Clear();
  bool IsInitialized() const;

  size_t ByteSizeLong() const;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input);
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const;
  ::google::protobuf::uint8* InternalSerializeWithCachedSizesToArray(
      bool deterministic, ::google::protobuf::uint8* output) const;
  ::google::protobuf::uint8* SerializeWithCachedSizesToArray(::google::protobuf::uint8* output) const {
    return InternalSerializeWithCachedSizesToArray(false, output);
  }
  int GetCachedSize() const { return _cached_size_; }
  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const;
  void InternalSwap(AggregateMessage* other);
  void UnsafeMergeFrom(const AggregateMessage& from);
  private:
  inline ::google::protobuf::Arena* GetArenaNoVirtual() const {
    return _internal_metadata_.arena();
  }
  inline void* MaybeArenaPtr() const {
    return _internal_metadata_.raw_arena_ptr();
  }
  public:

  ::google::protobuf::Metadata GetMetadata() const;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // optional int32 fieldname = 1;
  bool has_fieldname() const;
  void clear_fieldname();
  static const int kFieldnameFieldNumber = 1;
  ::google::protobuf::int32 fieldname() const;
  void set_fieldname(::google::protobuf::int32 value);

  // @@protoc_insertion_point(class_scope:protobuf_unittest.AggregateMessage)
 private:
  inline void set_has_fieldname();
  inline void clear_has_fieldname();

  ::google::protobuf::internal::InternalMetadataWithArena _internal_metadata_;
  ::google::protobuf::internal::HasBits<1> _has_bits_;
  mutable int _cached_size_;
  ::google::protobuf::int32 fieldname_;
  friend void  protobuf_InitDefaults_google_2fprotobuf_2funittest_5fcustom_5foptions_2eproto_impl();
  friend void  protobuf_AddDesc_google_2fprotobuf_2funittest_5fcustom_5foptions_2eproto_impl();
  friend void protobuf_AssignDesc_google_2fprotobuf_2funittest_5fcustom_5foptions_2eproto();
  friend void protobuf_ShutdownFile_google_2fprotobuf_2funittest_5fcustom_5foptions_2eproto();

  void InitAsDefaultInstance();
};
extern ::google::protobuf::internal::ExplicitlyConstructed<AggregateMessage> AggregateMessage_default_instance_;

// -------------------------------------------------------------------

class NestedOptionType_NestedMessage : public ::google::protobuf::Message /* @@protoc_insertion_point(class_definition:protobuf_unittest.NestedOptionType.NestedMessage) */ {
 public:
  NestedOptionType_NestedMessage();
  virtual ~NestedOptionType_NestedMessage();

  NestedOptionType_NestedMessage(const NestedOptionType_NestedMessage& from);

  inline NestedOptionType_NestedMessage& operator=(const NestedOptionType_NestedMessage& from) {
    CopyFrom(from);
    return *this;
  }

  inline const ::google::protobuf::UnknownFieldSet& unknown_fields() const {
    return _internal_metadata_.unknown_fields();
  }

  inline ::google::protobuf::UnknownFieldSet* mutable_unknown_fields() {
    return _internal_metadata_.mutable_unknown_fields();
  }

  static const ::google::protobuf::Descriptor* descriptor();
  static const NestedOptionType_NestedMessage& default_instance();

  static const NestedOptionType_NestedMessage* internal_default_instance();

  void Swap(NestedOptionType_NestedMessage* other);

  // implements Message ----------------------------------------------

  inline NestedOptionType_NestedMessage* New() const { return New(NULL); }

  NestedOptionType_NestedMessage* New(::google::protobuf::Arena* arena) const;
  void CopyFrom(const ::google::protobuf::Message& from);
  void MergeFrom(const ::google::protobuf::Message& from);
  void CopyFrom(const NestedOptionType_NestedMessage& from);
  void MergeFrom(const NestedOptionType_NestedMessage& from);
  void Clear();
  bool IsInitialized() const;

  size_t ByteSizeLong() const;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input);
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const;
  ::google::protobuf::uint8* InternalSerializeWithCachedSizesToArray(
      bool deterministic, ::google::protobuf::uint8* output) const;
  ::google::protobuf::uint8* SerializeWithCachedSizesToArray(::google::protobuf::uint8* output) const {
    return InternalSerializeWithCachedSizesToArray(false, output);
  }
  int GetCachedSize() const { return _cached_size_; }
  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const;
  void InternalSwap(NestedOptionType_NestedMessage* other);
  void UnsafeMergeFrom(const NestedOptionType_NestedMessage& from);
  private:
  inline ::google::protobuf::Arena* GetArenaNoVirtual() const {
    return _internal_metadata_.arena();
  }
  inline void* MaybeArenaPtr() const {
    return _internal_metadata_.raw_arena_ptr();
  }
  public:

  ::google::protobuf::Metadata GetMetadata() const;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // optional int32 nested_field = 1;
  bool has_nested_field() const;
  void clear_nested_field();
  static const int kNestedFieldFieldNumber = 1;
  ::google::protobuf::int32 nested_field() const;
  void set_nested_field(::google::protobuf::int32 value);

  // @@protoc_insertion_point(class_scope:protobuf_unittest.NestedOptionType.NestedMessage)
 private:
  inline void set_has_nested_field();
  inline void clear_has_nested_field();

  ::google::protobuf::internal::InternalMetadataWithArena _internal_metadata_;
  ::google::protobuf::internal::HasBits<1> _has_bits_;
  mutable int _cached_size_;
  ::google::protobuf::int32 nested_field_;
  friend void  protobuf_InitDefaults_google_2fprotobuf_2funittest_5fcustom_5foptions_2eproto_impl();
  friend void  protobuf_AddDesc_google_2fprotobuf_2funittest_5fcustom_5foptions_2eproto_impl();
  friend void protobuf_AssignDesc_google_2fprotobuf_2funittest_5fcustom_5foptions_2eproto();
  friend void protobuf_ShutdownFile_google_2fprotobuf_2funittest_5fcustom_5foptions_2eproto();

  void InitAsDefaultInstance();
};
extern ::google::protobuf::internal::ExplicitlyConstructed<NestedOptionType_NestedMessage> NestedOptionType_NestedMessage_default_instance_;

// -------------------------------------------------------------------

class NestedOptionType : public ::google::protobuf::Message /* @@protoc_insertion_point(class_definition:protobuf_unittest.NestedOptionType) */ {
 public:
  NestedOptionType();
  virtual ~NestedOptionType();

  NestedOptionType(const NestedOptionType& from);

  inline NestedOptionType& operator=(const NestedOptionType& from) {
    CopyFrom(from);
    return *this;
  }

  inline const ::google::protobuf::UnknownFieldSet& unknown_fields() const {
    return _internal_metadata_.unknown_fields();
  }

  inline ::google::protobuf::UnknownFieldSet* mutable_unknown_fields() {
    return _internal_metadata_.mutable_unknown_fields();
  }

  static const ::google::protobuf::Descriptor* descriptor();
  static const NestedOptionType& default_instance();

  static const NestedOptionType* internal_default_instance();

  void Swap(NestedOptionType* other);

  // implements Message ----------------------------------------------

  inline NestedOptionType* New() const { return New(NULL); }

  NestedOptionType* New(::google::protobuf::Arena* arena) const;
  void CopyFrom(const ::google::protobuf::Message& from);
  void MergeFrom(const ::google::protobuf::Message& from);
  void CopyFrom(const NestedOptionType& from);
  void MergeFrom(const NestedOptionType& from);
  void Clear();
  bool IsInitialized() const;

  size_t ByteSizeLong() const;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input);
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const;
  ::google::protobuf::uint8* InternalSerializeWithCachedSizesToArray(
      bool deterministic, ::google::protobuf::uint8* output) const;
  ::google::protobuf::uint8* SerializeWithCachedSizesToArray(::google::protobuf::uint8* output) const {
    return InternalSerializeWithCachedSizesToArray(false, output);
  }
  int GetCachedSize() const { return _cached_size_; }
  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const;
  void InternalSwap(NestedOptionType* other);
  void UnsafeMergeFrom(const NestedOptionType& from);
  private:
  inline ::google::protobuf::Arena* GetArenaNoVirtual() const {
    return _internal_metadata_.arena();
  }
  inline void* MaybeArenaPtr() const {
    return _internal_metadata_.raw_arena_ptr();
  }
  public:

  ::google::protobuf::Metadata GetMetadata() const;

  // nested types ----------------------------------------------------

  typedef NestedOptionType_NestedMessage NestedMessage;

  typedef NestedOptionType_NestedEnum NestedEnum;
  static const NestedEnum NESTED_ENUM_VALUE =
    NestedOptionType_NestedEnum_NESTED_ENUM_VALUE;
  static inline bool NestedEnum_IsValid(int value) {
    return NestedOptionType_NestedEnum_IsValid(value);
  }
  static const NestedEnum NestedEnum_MIN =
    NestedOptionType_NestedEnum_NestedEnum_MIN;
  static const NestedEnum NestedEnum_MAX =
    NestedOptionType_NestedEnum_NestedEnum_MAX;
  static const int NestedEnum_ARRAYSIZE =
    NestedOptionType_NestedEnum_NestedEnum_ARRAYSIZE;
  static inline const ::google::protobuf::EnumDescriptor*
  NestedEnum_descriptor() {
    return NestedOptionType_NestedEnum_descriptor();
  }
  static inline const ::std::string& NestedEnum_Name(NestedEnum value) {
    return NestedOptionType_NestedEnum_Name(value);
  }
  static inline bool NestedEnum_Parse(const ::std::string& name,
      NestedEnum* value) {
    return NestedOptionType_NestedEnum_Parse(name, value);
  }

  // accessors -------------------------------------------------------

  static const int kNestedExtensionFieldNumber = 7912573;
  static ::google::protobuf::internal::ExtensionIdentifier< ::google::protobuf::FileOptions,
      ::google::protobuf::internal::PrimitiveTypeTraits< ::google::protobuf::int32 >, 5, false >
    nested_extension;
  // @@protoc_insertion_point(class_scope:protobuf_unittest.NestedOptionType)
 private:

  ::google::protobuf::internal::InternalMetadataWithArena _internal_metadata_;
  ::google::protobuf::internal::HasBits<1> _has_bits_;
  mutable int _cached_size_;
  friend void  protobuf_InitDefaults_google_2fprotobuf_2funittest_5fcustom_5foptions_2eproto_impl();
  friend void  protobuf_AddDesc_google_2fprotobuf_2funittest_5fcustom_5foptions_2eproto_impl();
  friend void protobuf_AssignDesc_google_2fprotobuf_2funittest_5fcustom_5foptions_2eproto();
  friend void protobuf_ShutdownFile_google_2fprotobuf_2funittest_5fcustom_5foptions_2eproto();

  void InitAsDefaultInstance();
};
extern ::google::protobuf::internal::ExplicitlyConstructed<NestedOptionType> NestedOptionType_default_instance_;

// -------------------------------------------------------------------

class OldOptionType : public ::google::protobuf::Message /* @@protoc_insertion_point(class_definition:protobuf_unittest.OldOptionType) */ {
 public:
  OldOptionType();
  virtual ~OldOptionType();

  OldOptionType(const OldOptionType& from);

  inline OldOptionType& operator=(const OldOptionType& from) {
    CopyFrom(from);
    return *this;
  }

  inline const ::google::protobuf::UnknownFieldSet& unknown_fields() const {
    return _internal_metadata_.unknown_fields();
  }

  inline ::google::protobuf::UnknownFieldSet* mutable_unknown_fields() {
    return _internal_metadata_.mutable_unknown_fields();
  }

  static const ::google::protobuf::Descriptor* descriptor();
  static const OldOptionType& default_instance();

  static const OldOptionType* internal_default_instance();

  void Swap(OldOptionType* other);

  // implements Message ----------------------------------------------

  inline OldOptionType* New() const { return New(NULL); }

  OldOptionType* New(::google::protobuf::Arena* arena) const;
  void CopyFrom(const ::google::protobuf::Message& from);
  void MergeFrom(const ::google::protobuf::Message& from);
  void CopyFrom(const OldOptionType& from);
  void MergeFrom(const OldOptionType& from);
  void Clear();
  bool IsInitialized() const;

  size_t ByteSizeLong() const;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input);
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const;
  ::google::protobuf::uint8* InternalSerializeWithCachedSizesToArray(
      bool deterministic, ::google::protobuf::uint8* output) const;
  ::google::protobuf::uint8* SerializeWithCachedSizesToArray(::google::protobuf::uint8* output) const {
    return InternalSerializeWithCachedSizesToArray(false, output);
  }
  int GetCachedSize() const { return _cached_size_; }
  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const;
  void InternalSwap(OldOptionType* other);
  void UnsafeMergeFrom(const OldOptionType& from);
  private:
  inline ::google::protobuf::Arena* GetArenaNoVirtual() const {
    return _internal_metadata_.arena();
  }
  inline void* MaybeArenaPtr() const {
    return _internal_metadata_.raw_arena_ptr();
  }
  public:

  ::google::protobuf::Metadata GetMetadata() const;

  // nested types ----------------------------------------------------

  typedef OldOptionType_TestEnum TestEnum;
  static const TestEnum OLD_VALUE =
    OldOptionType_TestEnum_OLD_VALUE;
  static inline bool TestEnum_IsValid(int value) {
    return OldOptionType_TestEnum_IsValid(value);
  }
  static const TestEnum TestEnum_MIN =
    OldOptionType_TestEnum_TestEnum_MIN;
  static const TestEnum TestEnum_MAX =
    OldOptionType_TestEnum_TestEnum_MAX;
  static const int TestEnum_ARRAYSIZE =
    OldOptionType_TestEnum_TestEnum_ARRAYSIZE;
  static inline const ::google::protobuf::EnumDescriptor*
  TestEnum_descriptor() {
    return OldOptionType_TestEnum_descriptor();
  }
  static inline const ::std::string& TestEnum_Name(TestEnum value) {
    return OldOptionType_TestEnum_Name(value);
  }
  static inline bool TestEnum_Parse(const ::std::string& name,
      TestEnum* value) {
    return OldOptionType_TestEnum_Parse(name, value);
  }

  // accessors -------------------------------------------------------

  // required .protobuf_unittest.OldOptionType.TestEnum value = 1;
  bool has_value() const;
  void clear_value();
  static const int kValueFieldNumber = 1;
  ::protobuf_unittest::OldOptionType_TestEnum value() const;
  void set_value(::protobuf_unittest::OldOptionType_TestEnum value);

  // @@protoc_insertion_point(class_scope:protobuf_unittest.OldOptionType)
 private:
  inline void set_has_value();
  inline void clear_has_value();

  ::google::protobuf::internal::InternalMetadataWithArena _internal_metadata_;
  ::google::protobuf::internal::HasBits<1> _has_bits_;
  mutable int _cached_size_;
  int value_;
  friend void  protobuf_InitDefaults_google_2fprotobuf_2funittest_5fcustom_5foptions_2eproto_impl();
  friend void  protobuf_AddDesc_google_2fprotobuf_2funittest_5fcustom_5foptions_2eproto_impl();
  friend void protobuf_AssignDesc_google_2fprotobuf_2funittest_5fcustom_5foptions_2eproto();
  friend void protobuf_ShutdownFile_google_2fprotobuf_2funittest_5fcustom_5foptions_2eproto();

  void InitAsDefaultInstance();
};
extern ::google::protobuf::internal::ExplicitlyConstructed<OldOptionType> OldOptionType_default_instance_;

// -------------------------------------------------------------------

class NewOptionType : public ::google::protobuf::Message /* @@protoc_insertion_point(class_definition:protobuf_unittest.NewOptionType) */ {
 public:
  NewOptionType();
  virtual ~NewOptionType();

  NewOptionType(const NewOptionType& from);

  inline NewOptionType& operator=(const NewOptionType& from) {
    CopyFrom(from);
    return *this;
  }

  inline const ::google::protobuf::UnknownFieldSet& unknown_fields() const {
    return _internal_metadata_.unknown_fields();
  }

  inline ::google::protobuf::UnknownFieldSet* mutable_unknown_fields() {
    return _internal_metadata_.mutable_unknown_fields();
  }

  static const ::google::protobuf::Descriptor* descriptor();
  static const NewOptionType& default_instance();

  static const NewOptionType* internal_default_instance();

  void Swap(NewOptionType* other);

  // implements Message ----------------------------------------------

  inline NewOptionType* New() const { return New(NULL); }

  NewOptionType* New(::google::protobuf::Arena* arena) const;
  void CopyFrom(const ::google::protobuf::Message& from);
  void MergeFrom(const ::google::protobuf::Message& from);
  void CopyFrom(const NewOptionType& from);
  void MergeFrom(const NewOptionType& from);
  void Clear();
  bool IsInitialized() const;

  size_t ByteSizeLong() const;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input);
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const;
  ::google::protobuf::uint8* InternalSerializeWithCachedSizesToArray(
      bool deterministic, ::google::protobuf::uint8* output) const;
  ::google::protobuf::uint8* SerializeWithCachedSizesToArray(::google::protobuf::uint8* output) const {
    return InternalSerializeWithCachedSizesToArray(false, output);
  }
  int GetCachedSize() const { return _cached_size_; }
  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const;
  void InternalSwap(NewOptionType* other);
  void UnsafeMergeFrom(const NewOptionType& from);
  private:
  inline ::google::protobuf::Arena* GetArenaNoVirtual() const {
    return _internal_metadata_.arena();
  }
  inline void* MaybeArenaPtr() const {
    return _internal_metadata_.raw_arena_ptr();
  }
  public:

  ::google::protobuf::Metadata GetMetadata() const;

  // nested types ----------------------------------------------------

  typedef NewOptionType_TestEnum TestEnum;
  static const TestEnum OLD_VALUE =
    NewOptionType_TestEnum_OLD_VALUE;
  static const TestEnum NEW_VALUE =
    NewOptionType_TestEnum_NEW_VALUE;
  static inline bool TestEnum_IsValid(int value) {
    return NewOptionType_TestEnum_IsValid(value);
  }
  static const TestEnum TestEnum_MIN =
    NewOptionType_TestEnum_TestEnum_MIN;
  static const TestEnum TestEnum_MAX =
    NewOptionType_TestEnum_TestEnum_MAX;
  static const int TestEnum_ARRAYSIZE =
    NewOptionType_TestEnum_TestEnum_ARRAYSIZE;
  static inline const ::google::protobuf::EnumDescriptor*
  TestEnum_descriptor() {
    return NewOptionType_TestEnum_descriptor();
  }
  static inline const ::std::string& TestEnum_Name(TestEnum value) {
    return NewOptionType_TestEnum_Name(value);
  }
  static inline bool TestEnum_Parse(const ::std::string& name,
      TestEnum* value) {
    return NewOptionType_TestEnum_Parse(name, value);
  }

  // accessors -------------------------------------------------------

  // required .protobuf_unittest.NewOptionType.TestEnum value = 1;
  bool has_value() const;
  void clear_value();
  static const int kValueFieldNumber = 1;
  ::protobuf_unittest::NewOptionType_TestEnum value() const;
  void set_value(::protobuf_unittest::NewOptionType_TestEnum value);

  // @@protoc_insertion_point(class_scope:protobuf_unittest.NewOptionType)
 private:
  inline void set_has_value();
  inline void clear_has_value();

  ::google::protobuf::internal::InternalMetadataWithArena _internal_metadata_;
  ::google::protobuf::internal::HasBits<1> _has_bits_;
  mutable int _cached_size_;
  int value_;
  friend void  protobuf_InitDefaults_google_2fprotobuf_2funittest_5fcustom_5foptions_2eproto_impl();
  friend void  protobuf_AddDesc_google_2fprotobuf_2funittest_5fcustom_5foptions_2eproto_impl();
  friend void protobuf_AssignDesc_google_2fprotobuf_2funittest_5fcustom_5foptions_2eproto();
  friend void protobuf_ShutdownFile_google_2fprotobuf_2funittest_5fcustom_5foptions_2eproto();

  void InitAsDefaultInstance();
};
extern ::google::protobuf::internal::ExplicitlyConstructed<NewOptionType> NewOptionType_default_instance_;

// -------------------------------------------------------------------

class TestMessageWithRequiredEnumOption : public ::google::protobuf::Message /* @@protoc_insertion_point(class_definition:protobuf_unittest.TestMessageWithRequiredEnumOption) */ {
 public:
  TestMessageWithRequiredEnumOption();
  virtual ~TestMessageWithRequiredEnumOption();

  TestMessageWithRequiredEnumOption(const TestMessageWithRequiredEnumOption& from);

  inline TestMessageWithRequiredEnumOption& operator=(const TestMessageWithRequiredEnumOption& from) {
    CopyFrom(from);
    return *this;
  }

  inline const ::google::protobuf::UnknownFieldSet& unknown_fields() const {
    return _internal_metadata_.unknown_fields();
  }

  inline ::google::protobuf::UnknownFieldSet* mutable_unknown_fields() {
    return _internal_metadata_.mutable_unknown_fields();
  }

  static const ::google::protobuf::Descriptor* descriptor();
  static const TestMessageWithRequiredEnumOption& default_instance();

  static const TestMessageWithRequiredEnumOption* internal_default_instance();

  void Swap(TestMessageWithRequiredEnumOption* other);

  // implements Message ----------------------------------------------

  inline TestMessageWithRequiredEnumOption* New() const { return New(NULL); }

  TestMessageWithRequiredEnumOption* New(::google::protobuf::Arena* arena) const;
  void CopyFrom(const ::google::protobuf::Message& from);
  void MergeFrom(const ::google::protobuf::Message& from);
  void CopyFrom(const TestMessageWithRequiredEnumOption& from);
  void MergeFrom(const TestMessageWithRequiredEnumOption& from);
  void Clear();
  bool IsInitialized() const;

  size_t ByteSizeLong() const;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input);
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const;
  ::google::protobuf::uint8* InternalSerializeWithCachedSizesToArray(
      bool deterministic, ::google::protobuf::uint8* output) const;
  ::google::protobuf::uint8* SerializeWithCachedSizesToArray(::google::protobuf::uint8* output) const {
    return InternalSerializeWithCachedSizesToArray(false, output);
  }
  int GetCachedSize() const { return _cached_size_; }
  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const;
  void InternalSwap(TestMessageWithRequiredEnumOption* other);
  void UnsafeMergeFrom(const TestMessageWithRequiredEnumOption& from);
  private:
  inline ::google::protobuf::Arena* GetArenaNoVirtual() const {
    return _internal_metadata_.arena();
  }
  inline void* MaybeArenaPtr() const {
    return _internal_metadata_.raw_arena_ptr();
  }
  public:

  ::google::protobuf::Metadata GetMetadata() const;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // @@protoc_insertion_point(class_scope:protobuf_unittest.TestMessageWithRequiredEnumOption)
 private:

  ::google::protobuf::internal::InternalMetadataWithArena _internal_metadata_;
  ::google::protobuf::internal::HasBits<1> _has_bits_;
  mutable int _cached_size_;
  friend void  protobuf_InitDefaults_google_2fprotobuf_2funittest_5fcustom_5foptions_2eproto_impl();
  friend void  protobuf_AddDesc_google_2fprotobuf_2funittest_5fcustom_5foptions_2eproto_impl();
  friend void protobuf_AssignDesc_google_2fprotobuf_2funittest_5fcustom_5foptions_2eproto();
  friend void protobuf_ShutdownFile_google_2fprotobuf_2funittest_5fcustom_5foptions_2eproto();

  void InitAsDefaultInstance();
};
extern ::google::protobuf::internal::ExplicitlyConstructed<TestMessageWithRequiredEnumOption> TestMessageWithRequiredEnumOption_default_instance_;

// ===================================================================

class TestServiceWithCustomOptions_Stub;

class TestServiceWithCustomOptions : public ::google::protobuf::Service {
 protected:
  // This class should be treated as an abstract interface.
  inline TestServiceWithCustomOptions() {};
 public:
  virtual ~TestServiceWithCustomOptions();

  typedef TestServiceWithCustomOptions_Stub Stub;

  static const ::google::protobuf::ServiceDescriptor* descriptor();

  virtual void Foo(::google::protobuf::RpcController* controller,
                       const ::protobuf_unittest::CustomOptionFooRequest* request,
                       ::protobuf_unittest::CustomOptionFooResponse* response,
                       ::google::protobuf::Closure* done);

  // implements Service ----------------------------------------------

  const ::google::protobuf::ServiceDescriptor* GetDescriptor();
  void CallMethod(const ::google::protobuf::MethodDescriptor* method,
                  ::google::protobuf::RpcController* controller,
                  const ::google::protobuf::Message* request,
                  ::google::protobuf::Message* response,
                  ::google::protobuf::Closure* done);
  const ::google::protobuf::Message& GetRequestPrototype(
    const ::google::protobuf::MethodDescriptor* method) const;
  const ::google::protobuf::Message& GetResponsePrototype(
    const ::google::protobuf::MethodDescriptor* method) const;

 private:
  GOOGLE_DISALLOW_EVIL_CONSTRUCTORS(TestServiceWithCustomOptions);
};

class TestServiceWithCustomOptions_Stub : public TestServiceWithCustomOptions {
 public:
  TestServiceWithCustomOptions_Stub(::google::protobuf::RpcChannel* channel);
  TestServiceWithCustomOptions_Stub(::google::protobuf::RpcChannel* channel,
                   ::google::protobuf::Service::ChannelOwnership ownership);
  ~TestServiceWithCustomOptions_Stub();

  inline ::google::protobuf::RpcChannel* channel() { return channel_; }

  // implements TestServiceWithCustomOptions ------------------------------------------

  void Foo(::google::protobuf::RpcController* controller,
                       const ::protobuf_unittest::CustomOptionFooRequest* request,
                       ::protobuf_unittest::CustomOptionFooResponse* response,
                       ::google::protobuf::Closure* done);
 private:
  ::google::protobuf::RpcChannel* channel_;
  bool owns_channel_;
  GOOGLE_DISALLOW_EVIL_CONSTRUCTORS(TestServiceWithCustomOptions_Stub);
};


// -------------------------------------------------------------------

class AggregateService_Stub;

class AggregateService : public ::google::protobuf::Service {
 protected:
  // This class should be treated as an abstract interface.
  inline AggregateService() {};
 public:
  virtual ~AggregateService();

  typedef AggregateService_Stub Stub;

  static const ::google::protobuf::ServiceDescriptor* descriptor();

  virtual void Method(::google::protobuf::RpcController* controller,
                       const ::protobuf_unittest::AggregateMessage* request,
                       ::protobuf_unittest::AggregateMessage* response,
                       ::google::protobuf::Closure* done);

  // implements Service ----------------------------------------------

  const ::google::protobuf::ServiceDescriptor* GetDescriptor();
  void CallMethod(const ::google::protobuf::MethodDescriptor* method,
                  ::google::protobuf::RpcController* controller,
                  const ::google::protobuf::Message* request,
                  ::google::protobuf::Message* response,
                  ::google::protobuf::Closure* done);
  const ::google::protobuf::Message& GetRequestPrototype(
    const ::google::protobuf::MethodDescriptor* method) const;
  const ::google::protobuf::Message& GetResponsePrototype(
    const ::google::protobuf::MethodDescriptor* method) const;

 private:
  GOOGLE_DISALLOW_EVIL_CONSTRUCTORS(AggregateService);
};

class AggregateService_Stub : public AggregateService {
 public:
  AggregateService_Stub(::google::protobuf::RpcChannel* channel);
  AggregateService_Stub(::google::protobuf::RpcChannel* channel,
                   ::google::protobuf::Service::ChannelOwnership ownership);
  ~AggregateService_Stub();

  inline ::google::protobuf::RpcChannel* channel() { return channel_; }

  // implements AggregateService ------------------------------------------

  void Method(::google::protobuf::RpcController* controller,
                       const ::protobuf_unittest::AggregateMessage* request,
                       ::protobuf_unittest::AggregateMessage* response,
                       ::google::protobuf::Closure* done);
 private:
  ::google::protobuf::RpcChannel* channel_;
  bool owns_channel_;
  GOOGLE_DISALLOW_EVIL_CONSTRUCTORS(AggregateService_Stub);
};


// ===================================================================

static const int kFileOpt1FieldNumber = 7736974;
extern ::google::protobuf::internal::ExtensionIdentifier< ::google::protobuf::FileOptions,
    ::google::protobuf::internal::PrimitiveTypeTraits< ::google::protobuf::uint64 >, 4, false >
  file_opt1;
static const int kMessageOpt1FieldNumber = 7739036;
extern ::google::protobuf::internal::ExtensionIdentifier< ::google::protobuf::MessageOptions,
    ::google::protobuf::internal::PrimitiveTypeTraits< ::google::protobuf::int32 >, 5, false >
  message_opt1;
static const int kFieldOpt1FieldNumber = 7740936;
extern ::google::protobuf::internal::ExtensionIdentifier< ::google::protobuf::FieldOptions,
    ::google::protobuf::internal::PrimitiveTypeTraits< ::google::protobuf::uint64 >, 6, false >
  field_opt1;
static const int kFieldOpt2FieldNumber = 7753913;
extern ::google::protobuf::internal::ExtensionIdentifier< ::google::protobuf::FieldOptions,
    ::google::protobuf::internal::PrimitiveTypeTraits< ::google::protobuf::int32 >, 5, false >
  field_opt2;
static const int kOneofOpt1FieldNumber = 7740111;
extern ::google::protobuf::internal::ExtensionIdentifier< ::google::protobuf::OneofOptions,
    ::google::protobuf::internal::PrimitiveTypeTraits< ::google::protobuf::int32 >, 5, false >
  oneof_opt1;
static const int kEnumOpt1FieldNumber = 7753576;
extern ::google::protobuf::internal::ExtensionIdentifier< ::google::protobuf::EnumOptions,
    ::google::protobuf::internal::PrimitiveTypeTraits< ::google::protobuf::int32 >, 15, false >
  enum_opt1;
static const int kEnumValueOpt1FieldNumber = 1560678;
extern ::google::protobuf::internal::ExtensionIdentifier< ::google::protobuf::EnumValueOptions,
    ::google::protobuf::internal::PrimitiveTypeTraits< ::google::protobuf::int32 >, 5, false >
  enum_value_opt1;
static const int kServiceOpt1FieldNumber = 7887650;
extern ::google::protobuf::internal::ExtensionIdentifier< ::google::protobuf::ServiceOptions,
    ::google::protobuf::internal::PrimitiveTypeTraits< ::google::protobuf::int64 >, 18, false >
  service_opt1;
static const int kMethodOpt1FieldNumber = 7890860;
extern ::google::protobuf::internal::ExtensionIdentifier< ::google::protobuf::MethodOptions,
    ::google::protobuf::internal::EnumTypeTraits< ::protobuf_unittest::MethodOpt1, ::protobuf_unittest::MethodOpt1_IsValid>, 14, false >
  method_opt1;
static const int kBoolOptFieldNumber = 7706090;
extern ::google::protobuf::internal::ExtensionIdentifier< ::google::protobuf::MessageOptions,
    ::google::protobuf::internal::PrimitiveTypeTraits< bool >, 8, false >
  bool_opt;
static const int kInt32OptFieldNumber = 7705709;
extern ::google::protobuf::internal::ExtensionIdentifier< ::google::protobuf::MessageOptions,
    ::google::protobuf::internal::PrimitiveTypeTraits< ::google::protobuf::int32 >, 5, false >
  int32_opt;
static const int kInt64OptFieldNumber = 7705542;
extern ::google::protobuf::internal::ExtensionIdentifier< ::google::protobuf::MessageOptions,
    ::google::protobuf::internal::PrimitiveTypeTraits< ::google::protobuf::int64 >, 3, false >
  int64_opt;
static const int kUint32OptFieldNumber = 7704880;
extern ::google::protobuf::internal::ExtensionIdentifier< ::google::protobuf::MessageOptions,
    ::google::protobuf::internal::PrimitiveTypeTraits< ::google::protobuf::uint32 >, 13, false >
  uint32_opt;
static const int kUint64OptFieldNumber = 7702367;
extern ::google::protobuf::internal::ExtensionIdentifier< ::google::protobuf::MessageOptions,
    ::google::protobuf::internal::PrimitiveTypeTraits< ::google::protobuf::uint64 >, 4, false >
  uint64_opt;
static const int kSint32OptFieldNumber = 7701568;
extern ::google::protobuf::internal::ExtensionIdentifier< ::google::protobuf::MessageOptions,
    ::google::protobuf::internal::PrimitiveTypeTraits< ::google::protobuf::int32 >, 17, false >
  sint32_opt;
static const int kSint64OptFieldNumber = 7700863;
extern ::google::protobuf::internal::ExtensionIdentifier< ::google::protobuf::MessageOptions,
    ::google::protobuf::internal::PrimitiveTypeTraits< ::google::protobuf::int64 >, 18, false >
  sint64_opt;
static const int kFixed32OptFieldNumber = 7700307;
extern ::google::protobuf::internal::ExtensionIdentifier< ::google::protobuf::MessageOptions,
    ::google::protobuf::internal::PrimitiveTypeTraits< ::google::protobuf::uint32 >, 7, false >
  fixed32_opt;
static const int kFixed64OptFieldNumber = 7700194;
extern ::google::protobuf::internal::ExtensionIdentifier< ::google::protobuf::MessageOptions,
    ::google::protobuf::internal::PrimitiveTypeTraits< ::google::protobuf::uint64 >, 6, false >
  fixed64_opt;
static const int kSfixed32OptFieldNumber = 7698645;
extern ::google::protobuf::internal::ExtensionIdentifier< ::google::protobuf::MessageOptions,
    ::google::protobuf::internal::PrimitiveTypeTraits< ::google::protobuf::int32 >, 15, false >
  sfixed32_opt;
static const int kSfixed64OptFieldNumber = 7685475;
extern ::google::protobuf::internal::ExtensionIdentifier< ::google::protobuf::MessageOptions,
    ::google::protobuf::internal::PrimitiveTypeTraits< ::google::protobuf::int64 >, 16, false >
  sfixed64_opt;
static const int kFloatOptFieldNumber = 7675390;
extern ::google::protobuf::internal::ExtensionIdentifier< ::google::protobuf::MessageOptions,
    ::google::protobuf::internal::PrimitiveTypeTraits< float >, 2, false >
  float_opt;
static const int kDoubleOptFieldNumber = 7673293;
extern ::google::protobuf::internal::ExtensionIdentifier< ::google::protobuf::MessageOptions,
    ::google::protobuf::internal::PrimitiveTypeTraits< double >, 1, false >
  double_opt;
static const int kStringOptFieldNumber = 7673285;
extern ::google::protobuf::internal::ExtensionIdentifier< ::google::protobuf::MessageOptions,
    ::google::protobuf::internal::StringTypeTraits, 9, false >
  string_opt;
static const int kBytesOptFieldNumber = 7673238;
extern ::google::protobuf::internal::ExtensionIdentifier< ::google::protobuf::MessageOptions,
    ::google::protobuf::internal::StringTypeTraits, 12, false >
  bytes_opt;
static const int kEnumOptFieldNumber = 7673233;
extern ::google::protobuf::internal::ExtensionIdentifier< ::google::protobuf::MessageOptions,
    ::google::protobuf::internal::EnumTypeTraits< ::protobuf_unittest::DummyMessageContainingEnum_TestEnumType, ::protobuf_unittest::DummyMessageContainingEnum_TestEnumType_IsValid>, 14, false >
  enum_opt;
static const int kMessageTypeOptFieldNumber = 7665967;
extern ::google::protobuf::internal::ExtensionIdentifier< ::google::protobuf::MessageOptions,
    ::google::protobuf::internal::MessageTypeTraits< ::protobuf_unittest::DummyMessageInvalidAsOptionType >, 11, false >
  message_type_opt;
static const int kQuuxFieldNumber = 7663707;
extern ::google::protobuf::internal::ExtensionIdentifier< ::protobuf_unittest::ComplexOptionType1,
    ::google::protobuf::internal::PrimitiveTypeTraits< ::google::protobuf::int32 >, 5, false >
  quux;
static const int kCorgeFieldNumber = 7663442;
extern ::google::protobuf::internal::ExtensionIdentifier< ::protobuf_unittest::ComplexOptionType1,
    ::google::protobuf::internal::MessageTypeTraits< ::protobuf_unittest::ComplexOptionType3 >, 11, false >
  corge;
static const int kGraultFieldNumber = 7650927;
extern ::google::protobuf::internal::ExtensionIdentifier< ::protobuf_unittest::ComplexOptionType2,
    ::google::protobuf::internal::PrimitiveTypeTraits< ::google::protobuf::int32 >, 5, false >
  grault;
static const int kGarplyFieldNumber = 7649992;
extern ::google::protobuf::internal::ExtensionIdentifier< ::protobuf_unittest::ComplexOptionType2,
    ::google::protobuf::internal::MessageTypeTraits< ::protobuf_unittest::ComplexOptionType1 >, 11, false >
  garply;
static const int kComplexOpt1FieldNumber = 7646756;
extern ::google::protobuf::internal::ExtensionIdentifier< ::google::protobuf::MessageOptions,
    ::google::protobuf::internal::MessageTypeTraits< ::protobuf_unittest::ComplexOptionType1 >, 11, false >
  complex_opt1;
static const int kComplexOpt2FieldNumber = 7636949;
extern ::google::protobuf::internal::ExtensionIdentifier< ::google::protobuf::MessageOptions,
    ::google::protobuf::internal::MessageTypeTraits< ::protobuf_unittest::ComplexOptionType2 >, 11, false >
  complex_opt2;
static const int kComplexOpt3FieldNumber = 7636463;
extern ::google::protobuf::internal::ExtensionIdentifier< ::google::protobuf::MessageOptions,
    ::google::protobuf::internal::MessageTypeTraits< ::protobuf_unittest::ComplexOptionType3 >, 11, false >
  complex_opt3;
static const int kComplexopt6FieldNumber = 7595468;
extern ::google::protobuf::internal::ExtensionIdentifier< ::google::protobuf::MessageOptions,
    ::google::protobuf::internal::MessageTypeTraits< ::protobuf_unittest::ComplexOpt6 >, 10, false >
  complexopt6;
static const int kFileoptFieldNumber = 15478479;
extern ::google::protobuf::internal::ExtensionIdentifier< ::google::protobuf::FileOptions,
    ::google::protobuf::internal::MessageTypeTraits< ::protobuf_unittest::Aggregate >, 11, false >
  fileopt;
static const int kMsgoptFieldNumber = 15480088;
extern ::google::protobuf::internal::ExtensionIdentifier< ::google::protobuf::MessageOptions,
    ::google::protobuf::internal::MessageTypeTraits< ::protobuf_unittest::Aggregate >, 11, false >
  msgopt;
static const int kFieldoptFieldNumber = 15481374;
extern ::google::protobuf::internal::ExtensionIdentifier< ::google::protobuf::FieldOptions,
    ::google::protobuf::internal::MessageTypeTraits< ::protobuf_unittest::Aggregate >, 11, false >
  fieldopt;
static const int kEnumoptFieldNumber = 15483218;
extern ::google::protobuf::internal::ExtensionIdentifier< ::google::protobuf::EnumOptions,
    ::google::protobuf::internal::MessageTypeTraits< ::protobuf_unittest::Aggregate >, 11, false >
  enumopt;
static const int kEnumvaloptFieldNumber = 15486921;
extern ::google::protobuf::internal::ExtensionIdentifier< ::google::protobuf::EnumValueOptions,
    ::google::protobuf::internal::MessageTypeTraits< ::protobuf_unittest::Aggregate >, 11, false >
  enumvalopt;
static const int kServiceoptFieldNumber = 15497145;
extern ::google::protobuf::internal::ExtensionIdentifier< ::google::protobuf::ServiceOptions,
    ::google::protobuf::internal::MessageTypeTraits< ::protobuf_unittest::Aggregate >, 11, false >
  serviceopt;
static const int kMethodoptFieldNumber = 15512713;
extern ::google::protobuf::internal::ExtensionIdentifier< ::google::protobuf::MethodOptions,
    ::google::protobuf::internal::MessageTypeTraits< ::protobuf_unittest::Aggregate >, 11, false >
  methodopt;
static const int kRequiredEnumOptFieldNumber = 106161807;
extern ::google::protobuf::internal::ExtensionIdentifier< ::google::protobuf::MessageOptions,
    ::google::protobuf::internal::MessageTypeTraits< ::protobuf_unittest::OldOptionType >, 11, false >
  required_enum_opt;

// ===================================================================

#if !PROTOBUF_INLINE_NOT_IN_HEADERS
// TestMessageWithCustomOptions

// optional string field1 = 1 [ctype = CORD];
inline bool TestMessageWithCustomOptions::has_field1() const {
  return (_has_bits_[0] & 0x00000001u) != 0;
}
inline void TestMessageWithCustomOptions::set_has_field1() {
  _has_bits_[0] |= 0x00000001u;
}
inline void TestMessageWithCustomOptions::clear_has_field1() {
  _has_bits_[0] &= ~0x00000001u;
}
inline void TestMessageWithCustomOptions::clear_field1() {
  field1_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  clear_has_field1();
}
inline const ::std::string& TestMessageWithCustomOptions::field1() const {
  // @@protoc_insertion_point(field_get:protobuf_unittest.TestMessageWithCustomOptions.field1)
  return field1_.GetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void TestMessageWithCustomOptions::set_field1(const ::std::string& value) {
  set_has_field1();
  field1_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:protobuf_unittest.TestMessageWithCustomOptions.field1)
}
inline void TestMessageWithCustomOptions::set_field1(const char* value) {
  set_has_field1();
  field1_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:protobuf_unittest.TestMessageWithCustomOptions.field1)
}
inline void TestMessageWithCustomOptions::set_field1(const char* value, size_t size) {
  set_has_field1();
  field1_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:protobuf_unittest.TestMessageWithCustomOptions.field1)
}
inline ::std::string* TestMessageWithCustomOptions::mutable_field1() {
  set_has_field1();
  // @@protoc_insertion_point(field_mutable:protobuf_unittest.TestMessageWithCustomOptions.field1)
  return field1_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline ::std::string* TestMessageWithCustomOptions::release_field1() {
  // @@protoc_insertion_point(field_release:protobuf_unittest.TestMessageWithCustomOptions.field1)
  clear_has_field1();
  return field1_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void TestMessageWithCustomOptions::set_allocated_field1(::std::string* field1) {
  if (field1 != NULL) {
    set_has_field1();
  } else {
    clear_has_field1();
  }
  field1_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), field1);
  // @@protoc_insertion_point(field_set_allocated:protobuf_unittest.TestMessageWithCustomOptions.field1)
}

// optional int32 oneof_field = 2;
inline bool TestMessageWithCustomOptions::has_oneof_field() const {
  return AnOneof_case() == kOneofField;
}
inline void TestMessageWithCustomOptions::set_has_oneof_field() {
  _oneof_case_[0] = kOneofField;
}
inline void TestMessageWithCustomOptions::clear_oneof_field() {
  if (has_oneof_field()) {
    AnOneof_.oneof_field_ = 0;
    clear_has_AnOneof();
  }
}
inline ::google::protobuf::int32 TestMessageWithCustomOptions::oneof_field() const {
  // @@protoc_insertion_point(field_get:protobuf_unittest.TestMessageWithCustomOptions.oneof_field)
  if (has_oneof_field()) {
    return AnOneof_.oneof_field_;
  }
  return 0;
}
inline void TestMessageWithCustomOptions::set_oneof_field(::google::protobuf::int32 value) {
  if (!has_oneof_field()) {
    clear_AnOneof();
    set_has_oneof_field();
  }
  AnOneof_.oneof_field_ = value;
  // @@protoc_insertion_point(field_set:protobuf_unittest.TestMessageWithCustomOptions.oneof_field)
}

inline bool TestMessageWithCustomOptions::has_AnOneof() const {
  return AnOneof_case() != ANONEOF_NOT_SET;
}
inline void TestMessageWithCustomOptions::clear_has_AnOneof() {
  _oneof_case_[0] = ANONEOF_NOT_SET;
}
inline TestMessageWithCustomOptions::AnOneofCase TestMessageWithCustomOptions::AnOneof_case() const {
  return TestMessageWithCustomOptions::AnOneofCase(_oneof_case_[0]);
}
inline const TestMessageWithCustomOptions* TestMessageWithCustomOptions::internal_default_instance() {
  return &TestMessageWithCustomOptions_default_instance_.get();
}
// -------------------------------------------------------------------

// CustomOptionFooRequest

inline const CustomOptionFooRequest* CustomOptionFooRequest::internal_default_instance() {
  return &CustomOptionFooRequest_default_instance_.get();
}
// -------------------------------------------------------------------

// CustomOptionFooResponse

inline const CustomOptionFooResponse* CustomOptionFooResponse::internal_default_instance() {
  return &CustomOptionFooResponse_default_instance_.get();
}
// -------------------------------------------------------------------

// CustomOptionFooClientMessage

inline const CustomOptionFooClientMessage* CustomOptionFooClientMessage::internal_default_instance() {
  return &CustomOptionFooClientMessage_default_instance_.get();
}
// -------------------------------------------------------------------

// CustomOptionFooServerMessage

inline const CustomOptionFooServerMessage* CustomOptionFooServerMessage::internal_default_instance() {
  return &CustomOptionFooServerMessage_default_instance_.get();
}
// -------------------------------------------------------------------

// DummyMessageContainingEnum

inline const DummyMessageContainingEnum* DummyMessageContainingEnum::internal_default_instance() {
  return &DummyMessageContainingEnum_default_instance_.get();
}
// -------------------------------------------------------------------

// DummyMessageInvalidAsOptionType

inline const DummyMessageInvalidAsOptionType* DummyMessageInvalidAsOptionType::internal_default_instance() {
  return &DummyMessageInvalidAsOptionType_default_instance_.get();
}
// -------------------------------------------------------------------

// CustomOptionMinIntegerValues

inline const CustomOptionMinIntegerValues* CustomOptionMinIntegerValues::internal_default_instance() {
  return &CustomOptionMinIntegerValues_default_instance_.get();
}
// -------------------------------------------------------------------

// CustomOptionMaxIntegerValues

inline const CustomOptionMaxIntegerValues* CustomOptionMaxIntegerValues::internal_default_instance() {
  return &CustomOptionMaxIntegerValues_default_instance_.get();
}
// -------------------------------------------------------------------

// CustomOptionOtherValues

inline const CustomOptionOtherValues* CustomOptionOtherValues::internal_default_instance() {
  return &CustomOptionOtherValues_default_instance_.get();
}
// -------------------------------------------------------------------

// SettingRealsFromPositiveInts

inline const SettingRealsFromPositiveInts* SettingRealsFromPositiveInts::internal_default_instance() {
  return &SettingRealsFromPositiveInts_default_instance_.get();
}
// -------------------------------------------------------------------

// SettingRealsFromNegativeInts

inline const SettingRealsFromNegativeInts* SettingRealsFromNegativeInts::internal_default_instance() {
  return &SettingRealsFromNegativeInts_default_instance_.get();
}
// -------------------------------------------------------------------

// ComplexOptionType1

// optional int32 foo = 1;
inline bool ComplexOptionType1::has_foo() const {
  return (_has_bits_[0] & 0x00000001u) != 0;
}
inline void ComplexOptionType1::set_has_foo() {
  _has_bits_[0] |= 0x00000001u;
}
inline void ComplexOptionType1::clear_has_foo() {
  _has_bits_[0] &= ~0x00000001u;
}
inline void ComplexOptionType1::clear_foo() {
  foo_ = 0;
  clear_has_foo();
}
inline ::google::protobuf::int32 ComplexOptionType1::foo() const {
  // @@protoc_insertion_point(field_get:protobuf_unittest.ComplexOptionType1.foo)
  return foo_;
}
inline void ComplexOptionType1::set_foo(::google::protobuf::int32 value) {
  set_has_foo();
  foo_ = value;
  // @@protoc_insertion_point(field_set:protobuf_unittest.ComplexOptionType1.foo)
}

// optional int32 foo2 = 2;
inline bool ComplexOptionType1::has_foo2() const {
  return (_has_bits_[0] & 0x00000002u) != 0;
}
inline void ComplexOptionType1::set_has_foo2() {
  _has_bits_[0] |= 0x00000002u;
}
inline void ComplexOptionType1::clear_has_foo2() {
  _has_bits_[0] &= ~0x00000002u;
}
inline void ComplexOptionType1::clear_foo2() {
  foo2_ = 0;
  clear_has_foo2();
}
inline ::google::protobuf::int32 ComplexOptionType1::foo2() const {
  // @@protoc_insertion_point(field_get:protobuf_unittest.ComplexOptionType1.foo2)
  return foo2_;
}
inline void ComplexOptionType1::set_foo2(::google::protobuf::int32 value) {
  set_has_foo2();
  foo2_ = value;
  // @@protoc_insertion_point(field_set:protobuf_unittest.ComplexOptionType1.foo2)
}

// optional int32 foo3 = 3;
inline bool ComplexOptionType1::has_foo3() const {
  return (_has_bits_[0] & 0x00000004u) != 0;
}
inline void ComplexOptionType1::set_has_foo3() {
  _has_bits_[0] |= 0x00000004u;
}
inline void ComplexOptionType1::clear_has_foo3() {
  _has_bits_[0] &= ~0x00000004u;
}
inline void ComplexOptionType1::clear_foo3() {
  foo3_ = 0;
  clear_has_foo3();
}
inline ::google::protobuf::int32 ComplexOptionType1::foo3() const {
  // @@protoc_insertion_point(field_get:protobuf_unittest.ComplexOptionType1.foo3)
  return foo3_;
}
inline void ComplexOptionType1::set_foo3(::google::protobuf::int32 value) {
  set_has_foo3();
  foo3_ = value;
  // @@protoc_insertion_point(field_set:protobuf_unittest.ComplexOptionType1.foo3)
}

// repeated int32 foo4 = 4;
inline int ComplexOptionType1::foo4_size() const {
  return foo4_.size();
}
inline void ComplexOptionType1::clear_foo4() {
  foo4_.Clear();
}
inline ::google::protobuf::int32 ComplexOptionType1::foo4(int index) const {
  // @@protoc_insertion_point(field_get:protobuf_unittest.ComplexOptionType1.foo4)
  return foo4_.Get(index);
}
inline void ComplexOptionType1::set_foo4(int index, ::google::protobuf::int32 value) {
  foo4_.Set(index, value);
  // @@protoc_insertion_point(field_set:protobuf_unittest.ComplexOptionType1.foo4)
}
inline void ComplexOptionType1::add_foo4(::google::protobuf::int32 value) {
  foo4_.Add(value);
  // @@protoc_insertion_point(field_add:protobuf_unittest.ComplexOptionType1.foo4)
}
inline const ::google::protobuf::RepeatedField< ::google::protobuf::int32 >&
ComplexOptionType1::foo4() const {
  // @@protoc_insertion_point(field_list:protobuf_unittest.ComplexOptionType1.foo4)
  return foo4_;
}
inline ::google::protobuf::RepeatedField< ::google::protobuf::int32 >*
ComplexOptionType1::mutable_foo4() {
  // @@protoc_insertion_point(field_mutable_list:protobuf_unittest.ComplexOptionType1.foo4)
  return &foo4_;
}

inline const ComplexOptionType1* ComplexOptionType1::internal_default_instance() {
  return &ComplexOptionType1_default_instance_.get();
}
// -------------------------------------------------------------------

// ComplexOptionType2_ComplexOptionType4

// optional int32 waldo = 1;
inline bool ComplexOptionType2_ComplexOptionType4::has_waldo() const {
  return (_has_bits_[0] & 0x00000001u) != 0;
}
inline void ComplexOptionType2_ComplexOptionType4::set_has_waldo() {
  _has_bits_[0] |= 0x00000001u;
}
inline void ComplexOptionType2_ComplexOptionType4::clear_has_waldo() {
  _has_bits_[0] &= ~0x00000001u;
}
inline void ComplexOptionType2_ComplexOptionType4::clear_waldo() {
  waldo_ = 0;
  clear_has_waldo();
}
inline ::google::protobuf::int32 ComplexOptionType2_ComplexOptionType4::waldo() const {
  // @@protoc_insertion_point(field_get:protobuf_unittest.ComplexOptionType2.ComplexOptionType4.waldo)
  return waldo_;
}
inline void ComplexOptionType2_ComplexOptionType4::set_waldo(::google::protobuf::int32 value) {
  set_has_waldo();
  waldo_ = value;
  // @@protoc_insertion_point(field_set:protobuf_unittest.ComplexOptionType2.ComplexOptionType4.waldo)
}

inline const ComplexOptionType2_ComplexOptionType4* ComplexOptionType2_ComplexOptionType4::internal_default_instance() {
  return &ComplexOptionType2_ComplexOptionType4_default_instance_.get();
}
// -------------------------------------------------------------------

// ComplexOptionType2

// optional .protobuf_unittest.ComplexOptionType1 bar = 1;
inline bool ComplexOptionType2::has_bar() const {
  return (_has_bits_[0] & 0x00000001u) != 0;
}
inline void ComplexOptionType2::set_has_bar() {
  _has_bits_[0] |= 0x00000001u;
}
inline void ComplexOptionType2::clear_has_bar() {
  _has_bits_[0] &= ~0x00000001u;
}
inline void ComplexOptionType2::clear_bar() {
  if (bar_ != NULL) bar_->::protobuf_unittest::ComplexOptionType1::Clear();
  clear_has_bar();
}
inline const ::protobuf_unittest::ComplexOptionType1& ComplexOptionType2::bar() const {
  // @@protoc_insertion_point(field_get:protobuf_unittest.ComplexOptionType2.bar)
  return bar_ != NULL ? *bar_
                         : *::protobuf_unittest::ComplexOptionType1::internal_default_instance();
}
inline ::protobuf_unittest::ComplexOptionType1* ComplexOptionType2::mutable_bar() {
  set_has_bar();
  if (bar_ == NULL) {
    bar_ = new ::protobuf_unittest::ComplexOptionType1;
  }
  // @@protoc_insertion_point(field_mutable:protobuf_unittest.ComplexOptionType2.bar)
  return bar_;
}
inline ::protobuf_unittest::ComplexOptionType1* ComplexOptionType2::release_bar() {
  // @@protoc_insertion_point(field_release:protobuf_unittest.ComplexOptionType2.bar)
  clear_has_bar();
  ::protobuf_unittest::ComplexOptionType1* temp = bar_;
  bar_ = NULL;
  return temp;
}
inline void ComplexOptionType2::set_allocated_bar(::protobuf_unittest::ComplexOptionType1* bar) {
  delete bar_;
  bar_ = bar;
  if (bar) {
    set_has_bar();
  } else {
    clear_has_bar();
  }
  // @@protoc_insertion_point(field_set_allocated:protobuf_unittest.ComplexOptionType2.bar)
}

// optional int32 baz = 2;
inline bool ComplexOptionType2::has_baz() const {
  return (_has_bits_[0] & 0x00000002u) != 0;
}
inline void ComplexOptionType2::set_has_baz() {
  _has_bits_[0] |= 0x00000002u;
}
inline void ComplexOptionType2::clear_has_baz() {
  _has_bits_[0] &= ~0x00000002u;
}
inline void ComplexOptionType2::clear_baz() {
  baz_ = 0;
  clear_has_baz();
}
inline ::google::protobuf::int32 ComplexOptionType2::baz() const {
  // @@protoc_insertion_point(field_get:protobuf_unittest.ComplexOptionType2.baz)
  return baz_;
}
inline void ComplexOptionType2::set_baz(::google::protobuf::int32 value) {
  set_has_baz();
  baz_ = value;
  // @@protoc_insertion_point(field_set:protobuf_unittest.ComplexOptionType2.baz)
}

// optional .protobuf_unittest.ComplexOptionType2.ComplexOptionType4 fred = 3;
inline bool ComplexOptionType2::has_fred() const {
  return (_has_bits_[0] & 0x00000004u) != 0;
}
inline void ComplexOptionType2::set_has_fred() {
  _has_bits_[0] |= 0x00000004u;
}
inline void ComplexOptionType2::clear_has_fred() {
  _has_bits_[0] &= ~0x00000004u;
}
inline void ComplexOptionType2::clear_fred() {
  if (fred_ != NULL) fred_->::protobuf_unittest::ComplexOptionType2_ComplexOptionType4::Clear();
  clear_has_fred();
}
inline const ::protobuf_unittest::ComplexOptionType2_ComplexOptionType4& ComplexOptionType2::fred() const {
  // @@protoc_insertion_point(field_get:protobuf_unittest.ComplexOptionType2.fred)
  return fred_ != NULL ? *fred_
                         : *::protobuf_unittest::ComplexOptionType2_ComplexOptionType4::internal_default_instance();
}
inline ::protobuf_unittest::ComplexOptionType2_ComplexOptionType4* ComplexOptionType2::mutable_fred() {
  set_has_fred();
  if (fred_ == NULL) {
    fred_ = new ::protobuf_unittest::ComplexOptionType2_ComplexOptionType4;
  }
  // @@protoc_insertion_point(field_mutable:protobuf_unittest.ComplexOptionType2.fred)
  return fred_;
}
inline ::protobuf_unittest::ComplexOptionType2_ComplexOptionType4* ComplexOptionType2::release_fred() {
  // @@protoc_insertion_point(field_release:protobuf_unittest.ComplexOptionType2.fred)
  clear_has_fred();
  ::protobuf_unittest::ComplexOptionType2_ComplexOptionType4* temp = fred_;
  fred_ = NULL;
  return temp;
}
inline void ComplexOptionType2::set_allocated_fred(::protobuf_unittest::ComplexOptionType2_ComplexOptionType4* fred) {
  delete fred_;
  fred_ = fred;
  if (fred) {
    set_has_fred();
  } else {
    clear_has_fred();
  }
  // @@protoc_insertion_point(field_set_allocated:protobuf_unittest.ComplexOptionType2.fred)
}

// repeated .protobuf_unittest.ComplexOptionType2.ComplexOptionType4 barney = 4;
inline int ComplexOptionType2::barney_size() const {
  return barney_.size();
}
inline void ComplexOptionType2::clear_barney() {
  barney_.Clear();
}
inline const ::protobuf_unittest::ComplexOptionType2_ComplexOptionType4& ComplexOptionType2::barney(int index) const {
  // @@protoc_insertion_point(field_get:protobuf_unittest.ComplexOptionType2.barney)
  return barney_.Get(index);
}
inline ::protobuf_unittest::ComplexOptionType2_ComplexOptionType4* ComplexOptionType2::mutable_barney(int index) {
  // @@protoc_insertion_point(field_mutable:protobuf_unittest.ComplexOptionType2.barney)
  return barney_.Mutable(index);
}
inline ::protobuf_unittest::ComplexOptionType2_ComplexOptionType4* ComplexOptionType2::add_barney() {
  // @@protoc_insertion_point(field_add:protobuf_unittest.ComplexOptionType2.barney)
  return barney_.Add();
}
inline ::google::protobuf::RepeatedPtrField< ::protobuf_unittest::ComplexOptionType2_ComplexOptionType4 >*
ComplexOptionType2::mutable_barney() {
  // @@protoc_insertion_point(field_mutable_list:protobuf_unittest.ComplexOptionType2.barney)
  return &barney_;
}
inline const ::google::protobuf::RepeatedPtrField< ::protobuf_unittest::ComplexOptionType2_ComplexOptionType4 >&
ComplexOptionType2::barney() const {
  // @@protoc_insertion_point(field_list:protobuf_unittest.ComplexOptionType2.barney)
  return barney_;
}

inline const ComplexOptionType2* ComplexOptionType2::internal_default_instance() {
  return &ComplexOptionType2_default_instance_.get();
}
// -------------------------------------------------------------------

// ComplexOptionType3_ComplexOptionType5

// optional int32 plugh = 3;
inline bool ComplexOptionType3_ComplexOptionType5::has_plugh() const {
  return (_has_bits_[0] & 0x00000001u) != 0;
}
inline void ComplexOptionType3_ComplexOptionType5::set_has_plugh() {
  _has_bits_[0] |= 0x00000001u;
}
inline void ComplexOptionType3_ComplexOptionType5::clear_has_plugh() {
  _has_bits_[0] &= ~0x00000001u;
}
inline void ComplexOptionType3_ComplexOptionType5::clear_plugh() {
  plugh_ = 0;
  clear_has_plugh();
}
inline ::google::protobuf::int32 ComplexOptionType3_ComplexOptionType5::plugh() const {
  // @@protoc_insertion_point(field_get:protobuf_unittest.ComplexOptionType3.ComplexOptionType5.plugh)
  return plugh_;
}
inline void ComplexOptionType3_ComplexOptionType5::set_plugh(::google::protobuf::int32 value) {
  set_has_plugh();
  plugh_ = value;
  // @@protoc_insertion_point(field_set:protobuf_unittest.ComplexOptionType3.ComplexOptionType5.plugh)
}

inline const ComplexOptionType3_ComplexOptionType5* ComplexOptionType3_ComplexOptionType5::internal_default_instance() {
  return &ComplexOptionType3_ComplexOptionType5_default_instance_.get();
}
// -------------------------------------------------------------------

// ComplexOptionType3

// optional int32 qux = 1;
inline bool ComplexOptionType3::has_qux() const {
  return (_has_bits_[0] & 0x00000001u) != 0;
}
inline void ComplexOptionType3::set_has_qux() {
  _has_bits_[0] |= 0x00000001u;
}
inline void ComplexOptionType3::clear_has_qux() {
  _has_bits_[0] &= ~0x00000001u;
}
inline void ComplexOptionType3::clear_qux() {
  qux_ = 0;
  clear_has_qux();
}
inline ::google::protobuf::int32 ComplexOptionType3::qux() const {
  // @@protoc_insertion_point(field_get:protobuf_unittest.ComplexOptionType3.qux)
  return qux_;
}
inline void ComplexOptionType3::set_qux(::google::protobuf::int32 value) {
  set_has_qux();
  qux_ = value;
  // @@protoc_insertion_point(field_set:protobuf_unittest.ComplexOptionType3.qux)
}

// optional group ComplexOptionType5 = 2 { ... };
inline bool ComplexOptionType3::has_complexoptiontype5() const {
  return (_has_bits_[0] & 0x00000002u) != 0;
}
inline void ComplexOptionType3::set_has_complexoptiontype5() {
  _has_bits_[0] |= 0x00000002u;
}
inline void ComplexOptionType3::clear_has_complexoptiontype5() {
  _has_bits_[0] &= ~0x00000002u;
}
inline void ComplexOptionType3::clear_complexoptiontype5() {
  if (complexoptiontype5_ != NULL) complexoptiontype5_->::protobuf_unittest::ComplexOptionType3_ComplexOptionType5::Clear();
  clear_has_complexoptiontype5();
}
inline const ::protobuf_unittest::ComplexOptionType3_ComplexOptionType5& ComplexOptionType3::complexoptiontype5() const {
  // @@protoc_insertion_point(field_get:protobuf_unittest.ComplexOptionType3.complexoptiontype5)
  return complexoptiontype5_ != NULL ? *complexoptiontype5_
                         : *::protobuf_unittest::ComplexOptionType3_ComplexOptionType5::internal_default_instance();
}
inline ::protobuf_unittest::ComplexOptionType3_ComplexOptionType5* ComplexOptionType3::mutable_complexoptiontype5() {
  set_has_complexoptiontype5();
  if (complexoptiontype5_ == NULL) {
    complexoptiontype5_ = new ::protobuf_unittest::ComplexOptionType3_ComplexOptionType5;
  }
  // @@protoc_insertion_point(field_mutable:protobuf_unittest.ComplexOptionType3.complexoptiontype5)
  return complexoptiontype5_;
}
inline ::protobuf_unittest::ComplexOptionType3_ComplexOptionType5* ComplexOptionType3::release_complexoptiontype5() {
  // @@protoc_insertion_point(field_release:protobuf_unittest.ComplexOptionType3.complexoptiontype5)
  clear_has_complexoptiontype5();
  ::protobuf_unittest::ComplexOptionType3_ComplexOptionType5* temp = complexoptiontype5_;
  complexoptiontype5_ = NULL;
  return temp;
}
inline void ComplexOptionType3::set_allocated_complexoptiontype5(::protobuf_unittest::ComplexOptionType3_ComplexOptionType5* complexoptiontype5) {
  delete complexoptiontype5_;
  complexoptiontype5_ = complexoptiontype5;
  if (complexoptiontype5) {
    set_has_complexoptiontype5();
  } else {
    clear_has_complexoptiontype5();
  }
  // @@protoc_insertion_point(field_set_allocated:protobuf_unittest.ComplexOptionType3.complexoptiontype5)
}

inline const ComplexOptionType3* ComplexOptionType3::internal_default_instance() {
  return &ComplexOptionType3_default_instance_.get();
}
// -------------------------------------------------------------------

// ComplexOpt6

// optional int32 xyzzy = 7593951;
inline bool ComplexOpt6::has_xyzzy() const {
  return (_has_bits_[0] & 0x00000001u) != 0;
}
inline void ComplexOpt6::set_has_xyzzy() {
  _has_bits_[0] |= 0x00000001u;
}
inline void ComplexOpt6::clear_has_xyzzy() {
  _has_bits_[0] &= ~0x00000001u;
}
inline void ComplexOpt6::clear_xyzzy() {
  xyzzy_ = 0;
  clear_has_xyzzy();
}
inline ::google::protobuf::int32 ComplexOpt6::xyzzy() const {
  // @@protoc_insertion_point(field_get:protobuf_unittest.ComplexOpt6.xyzzy)
  return xyzzy_;
}
inline void ComplexOpt6::set_xyzzy(::google::protobuf::int32 value) {
  set_has_xyzzy();
  xyzzy_ = value;
  // @@protoc_insertion_point(field_set:protobuf_unittest.ComplexOpt6.xyzzy)
}

inline const ComplexOpt6* ComplexOpt6::internal_default_instance() {
  return &ComplexOpt6_default_instance_.get();
}
// -------------------------------------------------------------------

// VariousComplexOptions

inline const VariousComplexOptions* VariousComplexOptions::internal_default_instance() {
  return &VariousComplexOptions_default_instance_.get();
}
// -------------------------------------------------------------------

// AggregateMessageSet

inline const AggregateMessageSet* AggregateMessageSet::internal_default_instance() {
  return &AggregateMessageSet_default_instance_.get();
}
// -------------------------------------------------------------------

// AggregateMessageSetElement

// optional string s = 1;
inline bool AggregateMessageSetElement::has_s() const {
  return (_has_bits_[0] & 0x00000001u) != 0;
}
inline void AggregateMessageSetElement::set_has_s() {
  _has_bits_[0] |= 0x00000001u;
}
inline void AggregateMessageSetElement::clear_has_s() {
  _has_bits_[0] &= ~0x00000001u;
}
inline void AggregateMessageSetElement::clear_s() {
  s_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  clear_has_s();
}
inline const ::std::string& AggregateMessageSetElement::s() const {
  // @@protoc_insertion_point(field_get:protobuf_unittest.AggregateMessageSetElement.s)
  return s_.GetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void AggregateMessageSetElement::set_s(const ::std::string& value) {
  set_has_s();
  s_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:protobuf_unittest.AggregateMessageSetElement.s)
}
inline void AggregateMessageSetElement::set_s(const char* value) {
  set_has_s();
  s_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:protobuf_unittest.AggregateMessageSetElement.s)
}
inline void AggregateMessageSetElement::set_s(const char* value, size_t size) {
  set_has_s();
  s_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:protobuf_unittest.AggregateMessageSetElement.s)
}
inline ::std::string* AggregateMessageSetElement::mutable_s() {
  set_has_s();
  // @@protoc_insertion_point(field_mutable:protobuf_unittest.AggregateMessageSetElement.s)
  return s_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline ::std::string* AggregateMessageSetElement::release_s() {
  // @@protoc_insertion_point(field_release:protobuf_unittest.AggregateMessageSetElement.s)
  clear_has_s();
  return s_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void AggregateMessageSetElement::set_allocated_s(::std::string* s) {
  if (s != NULL) {
    set_has_s();
  } else {
    clear_has_s();
  }
  s_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), s);
  // @@protoc_insertion_point(field_set_allocated:protobuf_unittest.AggregateMessageSetElement.s)
}

inline const AggregateMessageSetElement* AggregateMessageSetElement::internal_default_instance() {
  return &AggregateMessageSetElement_default_instance_.get();
}
// -------------------------------------------------------------------

// Aggregate

// optional int32 i = 1;
inline bool Aggregate::has_i() const {
  return (_has_bits_[0] & 0x00000001u) != 0;
}
inline void Aggregate::set_has_i() {
  _has_bits_[0] |= 0x00000001u;
}
inline void Aggregate::clear_has_i() {
  _has_bits_[0] &= ~0x00000001u;
}
inline void Aggregate::clear_i() {
  i_ = 0;
  clear_has_i();
}
inline ::google::protobuf::int32 Aggregate::i() const {
  // @@protoc_insertion_point(field_get:protobuf_unittest.Aggregate.i)
  return i_;
}
inline void Aggregate::set_i(::google::protobuf::int32 value) {
  set_has_i();
  i_ = value;
  // @@protoc_insertion_point(field_set:protobuf_unittest.Aggregate.i)
}

// optional string s = 2;
inline bool Aggregate::has_s() const {
  return (_has_bits_[0] & 0x00000002u) != 0;
}
inline void Aggregate::set_has_s() {
  _has_bits_[0] |= 0x00000002u;
}
inline void Aggregate::clear_has_s() {
  _has_bits_[0] &= ~0x00000002u;
}
inline void Aggregate::clear_s() {
  s_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  clear_has_s();
}
inline const ::std::string& Aggregate::s() const {
  // @@protoc_insertion_point(field_get:protobuf_unittest.Aggregate.s)
  return s_.GetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void Aggregate::set_s(const ::std::string& value) {
  set_has_s();
  s_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:protobuf_unittest.Aggregate.s)
}
inline void Aggregate::set_s(const char* value) {
  set_has_s();
  s_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:protobuf_unittest.Aggregate.s)
}
inline void Aggregate::set_s(const char* value, size_t size) {
  set_has_s();
  s_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:protobuf_unittest.Aggregate.s)
}
inline ::std::string* Aggregate::mutable_s() {
  set_has_s();
  // @@protoc_insertion_point(field_mutable:protobuf_unittest.Aggregate.s)
  return s_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline ::std::string* Aggregate::release_s() {
  // @@protoc_insertion_point(field_release:protobuf_unittest.Aggregate.s)
  clear_has_s();
  return s_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void Aggregate::set_allocated_s(::std::string* s) {
  if (s != NULL) {
    set_has_s();
  } else {
    clear_has_s();
  }
  s_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), s);
  // @@protoc_insertion_point(field_set_allocated:protobuf_unittest.Aggregate.s)
}

// optional .protobuf_unittest.Aggregate sub = 3;
inline bool Aggregate::has_sub() const {
  return (_has_bits_[0] & 0x00000004u) != 0;
}
inline void Aggregate::set_has_sub() {
  _has_bits_[0] |= 0x00000004u;
}
inline void Aggregate::clear_has_sub() {
  _has_bits_[0] &= ~0x00000004u;
}
inline void Aggregate::clear_sub() {
  if (sub_ != NULL) sub_->::protobuf_unittest::Aggregate::Clear();
  clear_has_sub();
}
inline const ::protobuf_unittest::Aggregate& Aggregate::sub() const {
  // @@protoc_insertion_point(field_get:protobuf_unittest.Aggregate.sub)
  return sub_ != NULL ? *sub_
                         : *::protobuf_unittest::Aggregate::internal_default_instance();
}
inline ::protobuf_unittest::Aggregate* Aggregate::mutable_sub() {
  set_has_sub();
  if (sub_ == NULL) {
    sub_ = new ::protobuf_unittest::Aggregate;
  }
  // @@protoc_insertion_point(field_mutable:protobuf_unittest.Aggregate.sub)
  return sub_;
}
inline ::protobuf_unittest::Aggregate* Aggregate::release_sub() {
  // @@protoc_insertion_point(field_release:protobuf_unittest.Aggregate.sub)
  clear_has_sub();
  ::protobuf_unittest::Aggregate* temp = sub_;
  sub_ = NULL;
  return temp;
}
inline void Aggregate::set_allocated_sub(::protobuf_unittest::Aggregate* sub) {
  delete sub_;
  sub_ = sub;
  if (sub) {
    set_has_sub();
  } else {
    clear_has_sub();
  }
  // @@protoc_insertion_point(field_set_allocated:protobuf_unittest.Aggregate.sub)
}

// optional .google.protobuf.FileOptions file = 4;
inline bool Aggregate::has_file() const {
  return (_has_bits_[0] & 0x00000008u) != 0;
}
inline void Aggregate::set_has_file() {
  _has_bits_[0] |= 0x00000008u;
}
inline void Aggregate::clear_has_file() {
  _has_bits_[0] &= ~0x00000008u;
}
inline void Aggregate::clear_file() {
  if (file_ != NULL) file_->::google::protobuf::FileOptions::Clear();
  clear_has_file();
}
inline const ::google::protobuf::FileOptions& Aggregate::file() const {
  // @@protoc_insertion_point(field_get:protobuf_unittest.Aggregate.file)
  return file_ != NULL ? *file_
                         : *::google::protobuf::FileOptions::internal_default_instance();
}
inline ::google::protobuf::FileOptions* Aggregate::mutable_file() {
  set_has_file();
  if (file_ == NULL) {
    file_ = new ::google::protobuf::FileOptions;
  }
  // @@protoc_insertion_point(field_mutable:protobuf_unittest.Aggregate.file)
  return file_;
}
inline ::google::protobuf::FileOptions* Aggregate::release_file() {
  // @@protoc_insertion_point(field_release:protobuf_unittest.Aggregate.file)
  clear_has_file();
  ::google::protobuf::FileOptions* temp = file_;
  file_ = NULL;
  return temp;
}
inline void Aggregate::set_allocated_file(::google::protobuf::FileOptions* file) {
  delete file_;
  file_ = file;
  if (file) {
    set_has_file();
  } else {
    clear_has_file();
  }
  // @@protoc_insertion_point(field_set_allocated:protobuf_unittest.Aggregate.file)
}

// optional .protobuf_unittest.AggregateMessageSet mset = 5;
inline bool Aggregate::has_mset() const {
  return (_has_bits_[0] & 0x00000010u) != 0;
}
inline void Aggregate::set_has_mset() {
  _has_bits_[0] |= 0x00000010u;
}
inline void Aggregate::clear_has_mset() {
  _has_bits_[0] &= ~0x00000010u;
}
inline void Aggregate::clear_mset() {
  if (mset_ != NULL) mset_->::protobuf_unittest::AggregateMessageSet::Clear();
  clear_has_mset();
}
inline const ::protobuf_unittest::AggregateMessageSet& Aggregate::mset() const {
  // @@protoc_insertion_point(field_get:protobuf_unittest.Aggregate.mset)
  return mset_ != NULL ? *mset_
                         : *::protobuf_unittest::AggregateMessageSet::internal_default_instance();
}
inline ::protobuf_unittest::AggregateMessageSet* Aggregate::mutable_mset() {
  set_has_mset();
  if (mset_ == NULL) {
    mset_ = new ::protobuf_unittest::AggregateMessageSet;
  }
  // @@protoc_insertion_point(field_mutable:protobuf_unittest.Aggregate.mset)
  return mset_;
}
inline ::protobuf_unittest::AggregateMessageSet* Aggregate::release_mset() {
  // @@protoc_insertion_point(field_release:protobuf_unittest.Aggregate.mset)
  clear_has_mset();
  ::protobuf_unittest::AggregateMessageSet* temp = mset_;
  mset_ = NULL;
  return temp;
}
inline void Aggregate::set_allocated_mset(::protobuf_unittest::AggregateMessageSet* mset) {
  delete mset_;
  mset_ = mset;
  if (mset) {
    set_has_mset();
  } else {
    clear_has_mset();
  }
  // @@protoc_insertion_point(field_set_allocated:protobuf_unittest.Aggregate.mset)
}

inline const Aggregate* Aggregate::internal_default_instance() {
  return &Aggregate_default_instance_.get();
}
// -------------------------------------------------------------------

// AggregateMessage

// optional int32 fieldname = 1;
inline bool AggregateMessage::has_fieldname() const {
  return (_has_bits_[0] & 0x00000001u) != 0;
}
inline void AggregateMessage::set_has_fieldname() {
  _has_bits_[0] |= 0x00000001u;
}
inline void AggregateMessage::clear_has_fieldname() {
  _has_bits_[0] &= ~0x00000001u;
}
inline void AggregateMessage::clear_fieldname() {
  fieldname_ = 0;
  clear_has_fieldname();
}
inline ::google::protobuf::int32 AggregateMessage::fieldname() const {
  // @@protoc_insertion_point(field_get:protobuf_unittest.AggregateMessage.fieldname)
  return fieldname_;
}
inline void AggregateMessage::set_fieldname(::google::protobuf::int32 value) {
  set_has_fieldname();
  fieldname_ = value;
  // @@protoc_insertion_point(field_set:protobuf_unittest.AggregateMessage.fieldname)
}

inline const AggregateMessage* AggregateMessage::internal_default_instance() {
  return &AggregateMessage_default_instance_.get();
}
// -------------------------------------------------------------------

// NestedOptionType_NestedMessage

// optional int32 nested_field = 1;
inline bool NestedOptionType_NestedMessage::has_nested_field() const {
  return (_has_bits_[0] & 0x00000001u) != 0;
}
inline void NestedOptionType_NestedMessage::set_has_nested_field() {
  _has_bits_[0] |= 0x00000001u;
}
inline void NestedOptionType_NestedMessage::clear_has_nested_field() {
  _has_bits_[0] &= ~0x00000001u;
}
inline void NestedOptionType_NestedMessage::clear_nested_field() {
  nested_field_ = 0;
  clear_has_nested_field();
}
inline ::google::protobuf::int32 NestedOptionType_NestedMessage::nested_field() const {
  // @@protoc_insertion_point(field_get:protobuf_unittest.NestedOptionType.NestedMessage.nested_field)
  return nested_field_;
}
inline void NestedOptionType_NestedMessage::set_nested_field(::google::protobuf::int32 value) {
  set_has_nested_field();
  nested_field_ = value;
  // @@protoc_insertion_point(field_set:protobuf_unittest.NestedOptionType.NestedMessage.nested_field)
}

inline const NestedOptionType_NestedMessage* NestedOptionType_NestedMessage::internal_default_instance() {
  return &NestedOptionType_NestedMessage_default_instance_.get();
}
// -------------------------------------------------------------------

// NestedOptionType

inline const NestedOptionType* NestedOptionType::internal_default_instance() {
  return &NestedOptionType_default_instance_.get();
}
// -------------------------------------------------------------------

// OldOptionType

// required .protobuf_unittest.OldOptionType.TestEnum value = 1;
inline bool OldOptionType::has_value() const {
  return (_has_bits_[0] & 0x00000001u) != 0;
}
inline void OldOptionType::set_has_value() {
  _has_bits_[0] |= 0x00000001u;
}
inline void OldOptionType::clear_has_value() {
  _has_bits_[0] &= ~0x00000001u;
}
inline void OldOptionType::clear_value() {
  value_ = 0;
  clear_has_value();
}
inline ::protobuf_unittest::OldOptionType_TestEnum OldOptionType::value() const {
  // @@protoc_insertion_point(field_get:protobuf_unittest.OldOptionType.value)
  return static_cast< ::protobuf_unittest::OldOptionType_TestEnum >(value_);
}
inline void OldOptionType::set_value(::protobuf_unittest::OldOptionType_TestEnum value) {
  assert(::protobuf_unittest::OldOptionType_TestEnum_IsValid(value));
  set_has_value();
  value_ = value;
  // @@protoc_insertion_point(field_set:protobuf_unittest.OldOptionType.value)
}

inline const OldOptionType* OldOptionType::internal_default_instance() {
  return &OldOptionType_default_instance_.get();
}
// -------------------------------------------------------------------

// NewOptionType

// required .protobuf_unittest.NewOptionType.TestEnum value = 1;
inline bool NewOptionType::has_value() const {
  return (_has_bits_[0] & 0x00000001u) != 0;
}
inline void NewOptionType::set_has_value() {
  _has_bits_[0] |= 0x00000001u;
}
inline void NewOptionType::clear_has_value() {
  _has_bits_[0] &= ~0x00000001u;
}
inline void NewOptionType::clear_value() {
  value_ = 0;
  clear_has_value();
}
inline ::protobuf_unittest::NewOptionType_TestEnum NewOptionType::value() const {
  // @@protoc_insertion_point(field_get:protobuf_unittest.NewOptionType.value)
  return static_cast< ::protobuf_unittest::NewOptionType_TestEnum >(value_);
}
inline void NewOptionType::set_value(::protobuf_unittest::NewOptionType_TestEnum value) {
  assert(::protobuf_unittest::NewOptionType_TestEnum_IsValid(value));
  set_has_value();
  value_ = value;
  // @@protoc_insertion_point(field_set:protobuf_unittest.NewOptionType.value)
}

inline const NewOptionType* NewOptionType::internal_default_instance() {
  return &NewOptionType_default_instance_.get();
}
// -------------------------------------------------------------------

// TestMessageWithRequiredEnumOption

inline const TestMessageWithRequiredEnumOption* TestMessageWithRequiredEnumOption::internal_default_instance() {
  return &TestMessageWithRequiredEnumOption_default_instance_.get();
}
#endif  // !PROTOBUF_INLINE_NOT_IN_HEADERS
// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------


// @@protoc_insertion_point(namespace_scope)

}  // namespace protobuf_unittest

#ifndef SWIG
namespace google {
namespace protobuf {

template <> struct is_proto_enum< ::protobuf_unittest::TestMessageWithCustomOptions_AnEnum> : ::google::protobuf::internal::true_type {};
template <>
inline const EnumDescriptor* GetEnumDescriptor< ::protobuf_unittest::TestMessageWithCustomOptions_AnEnum>() {
  return ::protobuf_unittest::TestMessageWithCustomOptions_AnEnum_descriptor();
}
template <> struct is_proto_enum< ::protobuf_unittest::DummyMessageContainingEnum_TestEnumType> : ::google::protobuf::internal::true_type {};
template <>
inline const EnumDescriptor* GetEnumDescriptor< ::protobuf_unittest::DummyMessageContainingEnum_TestEnumType>() {
  return ::protobuf_unittest::DummyMessageContainingEnum_TestEnumType_descriptor();
}
template <> struct is_proto_enum< ::protobuf_unittest::NestedOptionType_NestedEnum> : ::google::protobuf::internal::true_type {};
template <>
inline const EnumDescriptor* GetEnumDescriptor< ::protobuf_unittest::NestedOptionType_NestedEnum>() {
  return ::protobuf_unittest::NestedOptionType_NestedEnum_descriptor();
}
template <> struct is_proto_enum< ::protobuf_unittest::OldOptionType_TestEnum> : ::google::protobuf::internal::true_type {};
template <>
inline const EnumDescriptor* GetEnumDescriptor< ::protobuf_unittest::OldOptionType_TestEnum>() {
  return ::protobuf_unittest::OldOptionType_TestEnum_descriptor();
}
template <> struct is_proto_enum< ::protobuf_unittest::NewOptionType_TestEnum> : ::google::protobuf::internal::true_type {};
template <>
inline const EnumDescriptor* GetEnumDescriptor< ::protobuf_unittest::NewOptionType_TestEnum>() {
  return ::protobuf_unittest::NewOptionType_TestEnum_descriptor();
}
template <> struct is_proto_enum< ::protobuf_unittest::MethodOpt1> : ::google::protobuf::internal::true_type {};
template <>
inline const EnumDescriptor* GetEnumDescriptor< ::protobuf_unittest::MethodOpt1>() {
  return ::protobuf_unittest::MethodOpt1_descriptor();
}
template <> struct is_proto_enum< ::protobuf_unittest::AggregateEnum> : ::google::protobuf::internal::true_type {};
template <>
inline const EnumDescriptor* GetEnumDescriptor< ::protobuf_unittest::AggregateEnum>() {
  return ::protobuf_unittest::AggregateEnum_descriptor();
}

}  // namespace protobuf
}  // namespace google
#endif  // SWIG

// @@protoc_insertion_point(global_scope)

#endif  // PROTOBUF_google_2fprotobuf_2funittest_5fcustom_5foptions_2eproto__INCLUDED

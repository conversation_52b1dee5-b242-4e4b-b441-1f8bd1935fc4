// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: google/protobuf/unittest_empty.proto

#define INTERNAL_SUPPRESS_PROTOBUF_FIELD_DEPRECATION
#include <google/protobuf/unittest_empty.pb.h>

#include <algorithm>

#include <google/protobuf/stubs/common.h>
#include <google/protobuf/stubs/port.h>
#include <google/protobuf/stubs/once.h>
#include <google/protobuf/io/coded_stream.h>
#include <google/protobuf/wire_format_lite_inl.h>
#include <google/protobuf/descriptor.h>
#include <google/protobuf/generated_message_reflection.h>
#include <google/protobuf/reflection_ops.h>
#include <google/protobuf/wire_format.h>
// @@protoc_insertion_point(includes)

namespace {


}  // namespace


void protobuf_AssignDesc_google_2fprotobuf_2funittest_5fempty_2eproto() GOOGLE_ATTRIBUTE_COLD;
void protobuf_AssignDesc_google_2fprotobuf_2funittest_5fempty_2eproto() {
  protobuf_AddDesc_google_2fprotobuf_2funittest_5fempty_2eproto();
  const ::google::protobuf::FileDescriptor* file =
    ::google::protobuf::DescriptorPool::generated_pool()->FindFileByName(
      "google/protobuf/unittest_empty.proto");
  GOOGLE_CHECK(file != NULL);
}

namespace {

GOOGLE_PROTOBUF_DECLARE_ONCE(protobuf_AssignDescriptors_once_);
void protobuf_AssignDescriptorsOnce() {
  ::google::protobuf::GoogleOnceInit(&protobuf_AssignDescriptors_once_,
                 &protobuf_AssignDesc_google_2fprotobuf_2funittest_5fempty_2eproto);
}

void protobuf_RegisterTypes(const ::std::string&) GOOGLE_ATTRIBUTE_COLD;
void protobuf_RegisterTypes(const ::std::string&) {
  protobuf_AssignDescriptorsOnce();
}

}  // namespace

void protobuf_ShutdownFile_google_2fprotobuf_2funittest_5fempty_2eproto() {
}

void protobuf_InitDefaults_google_2fprotobuf_2funittest_5fempty_2eproto_impl() {
  GOOGLE_PROTOBUF_VERIFY_VERSION;

}

GOOGLE_PROTOBUF_DECLARE_ONCE(protobuf_InitDefaults_google_2fprotobuf_2funittest_5fempty_2eproto_once_);
void protobuf_InitDefaults_google_2fprotobuf_2funittest_5fempty_2eproto() {
  ::google::protobuf::GoogleOnceInit(&protobuf_InitDefaults_google_2fprotobuf_2funittest_5fempty_2eproto_once_,
                 &protobuf_InitDefaults_google_2fprotobuf_2funittest_5fempty_2eproto_impl);
}
void protobuf_AddDesc_google_2fprotobuf_2funittest_5fempty_2eproto_impl() {
  GOOGLE_PROTOBUF_VERIFY_VERSION;

  protobuf_InitDefaults_google_2fprotobuf_2funittest_5fempty_2eproto();
  ::google::protobuf::DescriptorPool::InternalAddGeneratedFile(
    "\n$google/protobuf/unittest_empty.proto", 38);
  ::google::protobuf::MessageFactory::InternalRegisterGeneratedFile(
    "google/protobuf/unittest_empty.proto", &protobuf_RegisterTypes);
  ::google::protobuf::internal::OnShutdown(&protobuf_ShutdownFile_google_2fprotobuf_2funittest_5fempty_2eproto);
}

GOOGLE_PROTOBUF_DECLARE_ONCE(protobuf_AddDesc_google_2fprotobuf_2funittest_5fempty_2eproto_once_);
void protobuf_AddDesc_google_2fprotobuf_2funittest_5fempty_2eproto() {
  ::google::protobuf::GoogleOnceInit(&protobuf_AddDesc_google_2fprotobuf_2funittest_5fempty_2eproto_once_,
                 &protobuf_AddDesc_google_2fprotobuf_2funittest_5fempty_2eproto_impl);
}
// Force AddDescriptors() to be called at static initialization time.
struct StaticDescriptorInitializer_google_2fprotobuf_2funittest_5fempty_2eproto {
  StaticDescriptorInitializer_google_2fprotobuf_2funittest_5fempty_2eproto() {
    protobuf_AddDesc_google_2fprotobuf_2funittest_5fempty_2eproto();
  }
} static_descriptor_initializer_google_2fprotobuf_2funittest_5fempty_2eproto_;

// @@protoc_insertion_point(namespace_scope)

// @@protoc_insertion_point(global_scope)

// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: google/protobuf/unittest_lite_imports_nonlite.proto

#define INTERNAL_SUPPRESS_PROTOBUF_FIELD_DEPRECATION
#include <google/protobuf/unittest_lite_imports_nonlite.pb.h>

#include <algorithm>

#include <google/protobuf/stubs/common.h>
#include <google/protobuf/stubs/port.h>
#include <google/protobuf/stubs/once.h>
#include <google/protobuf/io/coded_stream.h>
#include <google/protobuf/wire_format_lite_inl.h>
#include <google/protobuf/io/zero_copy_stream_impl_lite.h>
// @@protoc_insertion_point(includes)

namespace protobuf_unittest {

void protobuf_ShutdownFile_google_2fprotobuf_2funittest_5flite_5fimports_5fnonlite_2eproto() {
  TestLiteImportsNonlite_default_instance_.Shutdown();
}

void protobuf_InitDefaults_google_2fprotobuf_2funittest_5flite_5fimports_5fnonlite_2eproto_impl() {
  GOOGLE_PROTOBUF_VERIFY_VERSION;

  ::protobuf_unittest::protobuf_InitDefaults_google_2fprotobuf_2funittest_2eproto();
  ::google::protobuf::internal::GetEmptyString();
  TestLiteImportsNonlite_default_instance_.DefaultConstruct();
  TestLiteImportsNonlite_default_instance_.get_mutable()->InitAsDefaultInstance();
}

GOOGLE_PROTOBUF_DECLARE_ONCE(protobuf_InitDefaults_google_2fprotobuf_2funittest_5flite_5fimports_5fnonlite_2eproto_once_);
void protobuf_InitDefaults_google_2fprotobuf_2funittest_5flite_5fimports_5fnonlite_2eproto() {
  ::google::protobuf::GoogleOnceInit(&protobuf_InitDefaults_google_2fprotobuf_2funittest_5flite_5fimports_5fnonlite_2eproto_once_,
                 &protobuf_InitDefaults_google_2fprotobuf_2funittest_5flite_5fimports_5fnonlite_2eproto_impl);
}
void protobuf_AddDesc_google_2fprotobuf_2funittest_5flite_5fimports_5fnonlite_2eproto_impl() {
  GOOGLE_PROTOBUF_VERIFY_VERSION;

  protobuf_InitDefaults_google_2fprotobuf_2funittest_5flite_5fimports_5fnonlite_2eproto();
  ::protobuf_unittest::protobuf_AddDesc_google_2fprotobuf_2funittest_2eproto();
  ::google::protobuf::internal::OnShutdown(&protobuf_ShutdownFile_google_2fprotobuf_2funittest_5flite_5fimports_5fnonlite_2eproto);
}

GOOGLE_PROTOBUF_DECLARE_ONCE(protobuf_AddDesc_google_2fprotobuf_2funittest_5flite_5fimports_5fnonlite_2eproto_once_);
void protobuf_AddDesc_google_2fprotobuf_2funittest_5flite_5fimports_5fnonlite_2eproto() {
  ::google::protobuf::GoogleOnceInit(&protobuf_AddDesc_google_2fprotobuf_2funittest_5flite_5fimports_5fnonlite_2eproto_once_,
                 &protobuf_AddDesc_google_2fprotobuf_2funittest_5flite_5fimports_5fnonlite_2eproto_impl);
}
#ifdef GOOGLE_PROTOBUF_NO_STATIC_INITIALIZER
// Force AddDescriptors() to be called at static initialization time.
struct StaticDescriptorInitializer_google_2fprotobuf_2funittest_5flite_5fimports_5fnonlite_2eproto {
  StaticDescriptorInitializer_google_2fprotobuf_2funittest_5flite_5fimports_5fnonlite_2eproto() {
    protobuf_AddDesc_google_2fprotobuf_2funittest_5flite_5fimports_5fnonlite_2eproto();
  }
} static_descriptor_initializer_google_2fprotobuf_2funittest_5flite_5fimports_5fnonlite_2eproto_;
#endif  // GOOGLE_PROTOBUF_NO_STATIC_INITIALIZER

namespace {

static void MergeFromFail(int line) GOOGLE_ATTRIBUTE_COLD GOOGLE_ATTRIBUTE_NORETURN;
static void MergeFromFail(int line) {
  ::google::protobuf::internal::MergeFromFail(__FILE__, line);
}

}  // namespace


// ===================================================================

static ::std::string* MutableUnknownFieldsForTestLiteImportsNonlite(
    TestLiteImportsNonlite* ptr) {
  return ptr->mutable_unknown_fields();
}

#if !defined(_MSC_VER) || _MSC_VER >= 1900
const int TestLiteImportsNonlite::kMessageFieldNumber;
#endif  // !defined(_MSC_VER) || _MSC_VER >= 1900

TestLiteImportsNonlite::TestLiteImportsNonlite()
  : ::google::protobuf::MessageLite(), _arena_ptr_(NULL) {
  if (this != internal_default_instance()) protobuf_InitDefaults_google_2fprotobuf_2funittest_5flite_5fimports_5fnonlite_2eproto();
  SharedCtor();
  // @@protoc_insertion_point(constructor:protobuf_unittest.TestLiteImportsNonlite)
}

void TestLiteImportsNonlite::InitAsDefaultInstance() {
  message_ = const_cast< ::protobuf_unittest::TestAllTypes*>(
      ::protobuf_unittest::TestAllTypes::internal_default_instance());
}

TestLiteImportsNonlite::TestLiteImportsNonlite(const TestLiteImportsNonlite& from)
  : ::google::protobuf::MessageLite(),
    _arena_ptr_(NULL) {
  SharedCtor();
  UnsafeMergeFrom(from);
  // @@protoc_insertion_point(copy_constructor:protobuf_unittest.TestLiteImportsNonlite)
}

void TestLiteImportsNonlite::SharedCtor() {
  _cached_size_ = 0;
  _unknown_fields_.UnsafeSetDefault(
      &::google::protobuf::internal::GetEmptyStringAlreadyInited());
  message_ = NULL;
}

TestLiteImportsNonlite::~TestLiteImportsNonlite() {
  // @@protoc_insertion_point(destructor:protobuf_unittest.TestLiteImportsNonlite)
  SharedDtor();
}

void TestLiteImportsNonlite::SharedDtor() {
  _unknown_fields_.DestroyNoArena(
      &::google::protobuf::internal::GetEmptyStringAlreadyInited());
  if (this != &TestLiteImportsNonlite_default_instance_.get()) {
    delete message_;
  }
}

void TestLiteImportsNonlite::SetCachedSize(int size) const {
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
}
const TestLiteImportsNonlite& TestLiteImportsNonlite::default_instance() {
  protobuf_InitDefaults_google_2fprotobuf_2funittest_5flite_5fimports_5fnonlite_2eproto();
  return *internal_default_instance();
}

::google::protobuf::internal::ExplicitlyConstructed<TestLiteImportsNonlite> TestLiteImportsNonlite_default_instance_;

TestLiteImportsNonlite* TestLiteImportsNonlite::New(::google::protobuf::Arena* arena) const {
  TestLiteImportsNonlite* n = new TestLiteImportsNonlite;
  if (arena != NULL) {
    arena->Own(n);
  }
  return n;
}

void TestLiteImportsNonlite::Clear() {
// @@protoc_insertion_point(message_clear_start:protobuf_unittest.TestLiteImportsNonlite)
  if (has_message()) {
    if (message_ != NULL) message_->::protobuf_unittest::TestAllTypes::Clear();
  }
  _has_bits_.Clear();
  _unknown_fields_.ClearToEmptyNoArena(
      &::google::protobuf::internal::GetEmptyStringAlreadyInited());
}

bool TestLiteImportsNonlite::MergePartialFromCodedStream(
    ::google::protobuf::io::CodedInputStream* input) {
#define DO_(EXPRESSION) if (!GOOGLE_PREDICT_TRUE(EXPRESSION)) goto failure
  ::google::protobuf::uint32 tag;
  ::google::protobuf::io::LazyStringOutputStream unknown_fields_string(
      ::google::protobuf::NewPermanentCallback(
          &MutableUnknownFieldsForTestLiteImportsNonlite, this));
  ::google::protobuf::io::CodedOutputStream unknown_fields_stream(
      &unknown_fields_string, false);
  // @@protoc_insertion_point(parse_start:protobuf_unittest.TestLiteImportsNonlite)
  for (;;) {
    ::std::pair< ::google::protobuf::uint32, bool> p = input->ReadTagWithCutoff(127);
    tag = p.first;
    if (!p.second) goto handle_unusual;
    switch (::google::protobuf::internal::WireFormatLite::GetTagFieldNumber(tag)) {
      // optional .protobuf_unittest.TestAllTypes message = 1;
      case 1: {
        if (tag == 10) {
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessageNoVirtual(
               input, mutable_message()));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectAtEnd()) goto success;
        break;
      }

      default: {
      handle_unusual:
        if (tag == 0 ||
            ::google::protobuf::internal::WireFormatLite::GetTagWireType(tag) ==
            ::google::protobuf::internal::WireFormatLite::WIRETYPE_END_GROUP) {
          goto success;
        }
        DO_(::google::protobuf::internal::WireFormatLite::SkipField(
            input, tag, &unknown_fields_stream));
        break;
      }
    }
  }
success:
  // @@protoc_insertion_point(parse_success:protobuf_unittest.TestLiteImportsNonlite)
  return true;
failure:
  // @@protoc_insertion_point(parse_failure:protobuf_unittest.TestLiteImportsNonlite)
  return false;
#undef DO_
}

void TestLiteImportsNonlite::SerializeWithCachedSizes(
    ::google::protobuf::io::CodedOutputStream* output) const {
  // @@protoc_insertion_point(serialize_start:protobuf_unittest.TestLiteImportsNonlite)
  // optional .protobuf_unittest.TestAllTypes message = 1;
  if (has_message()) {
    ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
      1, *this->message_, output);
  }

  output->WriteRaw(unknown_fields().data(),
                   static_cast<int>(unknown_fields().size()));
  // @@protoc_insertion_point(serialize_end:protobuf_unittest.TestLiteImportsNonlite)
}

size_t TestLiteImportsNonlite::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:protobuf_unittest.TestLiteImportsNonlite)
  size_t total_size = 0;

  // optional .protobuf_unittest.TestAllTypes message = 1;
  if (has_message()) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::MessageSizeNoVirtual(
        *this->message_);
  }

  total_size += unknown_fields().size();

  int cached_size = ::google::protobuf::internal::ToCachedSize(total_size);
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = cached_size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
  return total_size;
}

void TestLiteImportsNonlite::CheckTypeAndMergeFrom(
    const ::google::protobuf::MessageLite& from) {
  MergeFrom(*::google::protobuf::down_cast<const TestLiteImportsNonlite*>(&from));
}

void TestLiteImportsNonlite::MergeFrom(const TestLiteImportsNonlite& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:protobuf_unittest.TestLiteImportsNonlite)
  if (GOOGLE_PREDICT_TRUE(&from != this)) {
    UnsafeMergeFrom(from);
  } else {
    MergeFromFail(__LINE__);
  }
}

void TestLiteImportsNonlite::UnsafeMergeFrom(const TestLiteImportsNonlite& from) {
  GOOGLE_DCHECK(&from != this);
  if (from._has_bits_[0 / 32] & (0xffu << (0 % 32))) {
    if (from.has_message()) {
      mutable_message()->::protobuf_unittest::TestAllTypes::MergeFrom(from.message());
    }
  }
  if (!from.unknown_fields().empty()) {
    mutable_unknown_fields()->append(from.unknown_fields());
  }
}

void TestLiteImportsNonlite::CopyFrom(const TestLiteImportsNonlite& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:protobuf_unittest.TestLiteImportsNonlite)
  if (&from == this) return;
  Clear();
  UnsafeMergeFrom(from);
}

bool TestLiteImportsNonlite::IsInitialized() const {

  return true;
}

void TestLiteImportsNonlite::Swap(TestLiteImportsNonlite* other) {
  if (other == this) return;
  InternalSwap(other);
}
void TestLiteImportsNonlite::InternalSwap(TestLiteImportsNonlite* other) {
  std::swap(message_, other->message_);
  std::swap(_has_bits_[0], other->_has_bits_[0]);
  _unknown_fields_.Swap(&other->_unknown_fields_);
  std::swap(_cached_size_, other->_cached_size_);
}

::std::string TestLiteImportsNonlite::GetTypeName() const {
  return "protobuf_unittest.TestLiteImportsNonlite";
}

#if PROTOBUF_INLINE_NOT_IN_HEADERS
// TestLiteImportsNonlite

// optional .protobuf_unittest.TestAllTypes message = 1;
bool TestLiteImportsNonlite::has_message() const {
  return (_has_bits_[0] & 0x00000001u) != 0;
}
void TestLiteImportsNonlite::set_has_message() {
  _has_bits_[0] |= 0x00000001u;
}
void TestLiteImportsNonlite::clear_has_message() {
  _has_bits_[0] &= ~0x00000001u;
}
void TestLiteImportsNonlite::clear_message() {
  if (message_ != NULL) message_->::protobuf_unittest::TestAllTypes::Clear();
  clear_has_message();
}
const ::protobuf_unittest::TestAllTypes& TestLiteImportsNonlite::message() const {
  // @@protoc_insertion_point(field_get:protobuf_unittest.TestLiteImportsNonlite.message)
  return message_ != NULL ? *message_
                         : *::protobuf_unittest::TestAllTypes::internal_default_instance();
}
::protobuf_unittest::TestAllTypes* TestLiteImportsNonlite::mutable_message() {
  set_has_message();
  if (message_ == NULL) {
    message_ = new ::protobuf_unittest::TestAllTypes;
  }
  // @@protoc_insertion_point(field_mutable:protobuf_unittest.TestLiteImportsNonlite.message)
  return message_;
}
::protobuf_unittest::TestAllTypes* TestLiteImportsNonlite::release_message() {
  // @@protoc_insertion_point(field_release:protobuf_unittest.TestLiteImportsNonlite.message)
  clear_has_message();
  ::protobuf_unittest::TestAllTypes* temp = message_;
  message_ = NULL;
  return temp;
}
void TestLiteImportsNonlite::set_allocated_message(::protobuf_unittest::TestAllTypes* message) {
  delete message_;
  if (message != NULL && message->GetArena() != NULL) {
    ::protobuf_unittest::TestAllTypes* new_message = new ::protobuf_unittest::TestAllTypes;
    new_message->CopyFrom(*message);
    message = new_message;
  }
  message_ = message;
  if (message) {
    set_has_message();
  } else {
    clear_has_message();
  }
  // @@protoc_insertion_point(field_set_allocated:protobuf_unittest.TestLiteImportsNonlite.message)
}

inline const TestLiteImportsNonlite* TestLiteImportsNonlite::internal_default_instance() {
  return &TestLiteImportsNonlite_default_instance_.get();
}
#endif  // PROTOBUF_INLINE_NOT_IN_HEADERS

// @@protoc_insertion_point(namespace_scope)

}  // namespace protobuf_unittest

// @@protoc_insertion_point(global_scope)

// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: google/protobuf/map_unittest.proto

#define INTERNAL_SUPPRESS_PROTOBUF_FIELD_DEPRECATION
#include <google/protobuf/map_unittest.pb.h>

#include <algorithm>

#include <google/protobuf/stubs/common.h>
#include <google/protobuf/stubs/port.h>
#include <google/protobuf/stubs/once.h>
#include <google/protobuf/io/coded_stream.h>
#include <google/protobuf/wire_format_lite_inl.h>
#include <google/protobuf/descriptor.h>
#include <google/protobuf/generated_message_reflection.h>
#include <google/protobuf/reflection_ops.h>
#include <google/protobuf/wire_format.h>
// @@protoc_insertion_point(includes)

namespace protobuf_unittest {

namespace {

const ::google::protobuf::Descriptor* TestMap_descriptor_ = NULL;
const ::google::protobuf::internal::GeneratedMessageReflection*
  TestMap_reflection_ = NULL;
const ::google::protobuf::Descriptor* TestMap_MapInt32Int32Entry_descriptor_ = NULL;
const ::google::protobuf::Descriptor* TestMap_MapInt64Int64Entry_descriptor_ = NULL;
const ::google::protobuf::Descriptor* TestMap_MapUint32Uint32Entry_descriptor_ = NULL;
const ::google::protobuf::Descriptor* TestMap_MapUint64Uint64Entry_descriptor_ = NULL;
const ::google::protobuf::Descriptor* TestMap_MapSint32Sint32Entry_descriptor_ = NULL;
const ::google::protobuf::Descriptor* TestMap_MapSint64Sint64Entry_descriptor_ = NULL;
const ::google::protobuf::Descriptor* TestMap_MapFixed32Fixed32Entry_descriptor_ = NULL;
const ::google::protobuf::Descriptor* TestMap_MapFixed64Fixed64Entry_descriptor_ = NULL;
const ::google::protobuf::Descriptor* TestMap_MapSfixed32Sfixed32Entry_descriptor_ = NULL;
const ::google::protobuf::Descriptor* TestMap_MapSfixed64Sfixed64Entry_descriptor_ = NULL;
const ::google::protobuf::Descriptor* TestMap_MapInt32FloatEntry_descriptor_ = NULL;
const ::google::protobuf::Descriptor* TestMap_MapInt32DoubleEntry_descriptor_ = NULL;
const ::google::protobuf::Descriptor* TestMap_MapBoolBoolEntry_descriptor_ = NULL;
const ::google::protobuf::Descriptor* TestMap_MapStringStringEntry_descriptor_ = NULL;
const ::google::protobuf::Descriptor* TestMap_MapInt32BytesEntry_descriptor_ = NULL;
const ::google::protobuf::Descriptor* TestMap_MapInt32EnumEntry_descriptor_ = NULL;
const ::google::protobuf::Descriptor* TestMap_MapInt32ForeignMessageEntry_descriptor_ = NULL;
const ::google::protobuf::Descriptor* TestMap_MapStringForeignMessageEntry_descriptor_ = NULL;
const ::google::protobuf::Descriptor* TestMapSubmessage_descriptor_ = NULL;
const ::google::protobuf::internal::GeneratedMessageReflection*
  TestMapSubmessage_reflection_ = NULL;
const ::google::protobuf::Descriptor* TestMessageMap_descriptor_ = NULL;
const ::google::protobuf::internal::GeneratedMessageReflection*
  TestMessageMap_reflection_ = NULL;
const ::google::protobuf::Descriptor* TestMessageMap_MapInt32MessageEntry_descriptor_ = NULL;
const ::google::protobuf::Descriptor* TestSameTypeMap_descriptor_ = NULL;
const ::google::protobuf::internal::GeneratedMessageReflection*
  TestSameTypeMap_reflection_ = NULL;
const ::google::protobuf::Descriptor* TestSameTypeMap_Map1Entry_descriptor_ = NULL;
const ::google::protobuf::Descriptor* TestSameTypeMap_Map2Entry_descriptor_ = NULL;
const ::google::protobuf::Descriptor* TestRequiredMessageMap_descriptor_ = NULL;
const ::google::protobuf::internal::GeneratedMessageReflection*
  TestRequiredMessageMap_reflection_ = NULL;
const ::google::protobuf::Descriptor* TestRequiredMessageMap_MapFieldEntry_descriptor_ = NULL;
const ::google::protobuf::Descriptor* TestArenaMap_descriptor_ = NULL;
const ::google::protobuf::internal::GeneratedMessageReflection*
  TestArenaMap_reflection_ = NULL;
const ::google::protobuf::Descriptor* TestArenaMap_MapInt32Int32Entry_descriptor_ = NULL;
const ::google::protobuf::Descriptor* TestArenaMap_MapInt64Int64Entry_descriptor_ = NULL;
const ::google::protobuf::Descriptor* TestArenaMap_MapUint32Uint32Entry_descriptor_ = NULL;
const ::google::protobuf::Descriptor* TestArenaMap_MapUint64Uint64Entry_descriptor_ = NULL;
const ::google::protobuf::Descriptor* TestArenaMap_MapSint32Sint32Entry_descriptor_ = NULL;
const ::google::protobuf::Descriptor* TestArenaMap_MapSint64Sint64Entry_descriptor_ = NULL;
const ::google::protobuf::Descriptor* TestArenaMap_MapFixed32Fixed32Entry_descriptor_ = NULL;
const ::google::protobuf::Descriptor* TestArenaMap_MapFixed64Fixed64Entry_descriptor_ = NULL;
const ::google::protobuf::Descriptor* TestArenaMap_MapSfixed32Sfixed32Entry_descriptor_ = NULL;
const ::google::protobuf::Descriptor* TestArenaMap_MapSfixed64Sfixed64Entry_descriptor_ = NULL;
const ::google::protobuf::Descriptor* TestArenaMap_MapInt32FloatEntry_descriptor_ = NULL;
const ::google::protobuf::Descriptor* TestArenaMap_MapInt32DoubleEntry_descriptor_ = NULL;
const ::google::protobuf::Descriptor* TestArenaMap_MapBoolBoolEntry_descriptor_ = NULL;
const ::google::protobuf::Descriptor* TestArenaMap_MapStringStringEntry_descriptor_ = NULL;
const ::google::protobuf::Descriptor* TestArenaMap_MapInt32BytesEntry_descriptor_ = NULL;
const ::google::protobuf::Descriptor* TestArenaMap_MapInt32EnumEntry_descriptor_ = NULL;
const ::google::protobuf::Descriptor* TestArenaMap_MapInt32ForeignMessageEntry_descriptor_ = NULL;
const ::google::protobuf::Descriptor* TestArenaMap_MapInt32ForeignMessageNoArenaEntry_descriptor_ = NULL;
const ::google::protobuf::Descriptor* MessageContainingEnumCalledType_descriptor_ = NULL;
const ::google::protobuf::internal::GeneratedMessageReflection*
  MessageContainingEnumCalledType_reflection_ = NULL;
const ::google::protobuf::Descriptor* MessageContainingEnumCalledType_TypeEntry_descriptor_ = NULL;
const ::google::protobuf::EnumDescriptor* MessageContainingEnumCalledType_Type_descriptor_ = NULL;
const ::google::protobuf::Descriptor* MessageContainingMapCalledEntry_descriptor_ = NULL;
const ::google::protobuf::internal::GeneratedMessageReflection*
  MessageContainingMapCalledEntry_reflection_ = NULL;
const ::google::protobuf::Descriptor* MessageContainingMapCalledEntry_EntryEntry_descriptor_ = NULL;
const ::google::protobuf::Descriptor* TestRecursiveMapMessage_descriptor_ = NULL;
const ::google::protobuf::internal::GeneratedMessageReflection*
  TestRecursiveMapMessage_reflection_ = NULL;
const ::google::protobuf::Descriptor* TestRecursiveMapMessage_AEntry_descriptor_ = NULL;
const ::google::protobuf::EnumDescriptor* MapEnum_descriptor_ = NULL;

}  // namespace


void protobuf_AssignDesc_google_2fprotobuf_2fmap_5funittest_2eproto() GOOGLE_ATTRIBUTE_COLD;
void protobuf_AssignDesc_google_2fprotobuf_2fmap_5funittest_2eproto() {
  protobuf_AddDesc_google_2fprotobuf_2fmap_5funittest_2eproto();
  const ::google::protobuf::FileDescriptor* file =
    ::google::protobuf::DescriptorPool::generated_pool()->FindFileByName(
      "google/protobuf/map_unittest.proto");
  GOOGLE_CHECK(file != NULL);
  TestMap_descriptor_ = file->message_type(0);
  static const int TestMap_offsets_[18] = {
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(TestMap, map_int32_int32_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(TestMap, map_int64_int64_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(TestMap, map_uint32_uint32_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(TestMap, map_uint64_uint64_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(TestMap, map_sint32_sint32_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(TestMap, map_sint64_sint64_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(TestMap, map_fixed32_fixed32_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(TestMap, map_fixed64_fixed64_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(TestMap, map_sfixed32_sfixed32_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(TestMap, map_sfixed64_sfixed64_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(TestMap, map_int32_float_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(TestMap, map_int32_double_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(TestMap, map_bool_bool_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(TestMap, map_string_string_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(TestMap, map_int32_bytes_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(TestMap, map_int32_enum_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(TestMap, map_int32_foreign_message_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(TestMap, map_string_foreign_message_),
  };
  TestMap_reflection_ =
    ::google::protobuf::internal::GeneratedMessageReflection::NewGeneratedMessageReflection(
      TestMap_descriptor_,
      TestMap::internal_default_instance(),
      TestMap_offsets_,
      -1,
      -1,
      -1,
      sizeof(TestMap),
      GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(TestMap, _internal_metadata_));
  TestMap_MapInt32Int32Entry_descriptor_ = TestMap_descriptor_->nested_type(0);
  TestMap_MapInt64Int64Entry_descriptor_ = TestMap_descriptor_->nested_type(1);
  TestMap_MapUint32Uint32Entry_descriptor_ = TestMap_descriptor_->nested_type(2);
  TestMap_MapUint64Uint64Entry_descriptor_ = TestMap_descriptor_->nested_type(3);
  TestMap_MapSint32Sint32Entry_descriptor_ = TestMap_descriptor_->nested_type(4);
  TestMap_MapSint64Sint64Entry_descriptor_ = TestMap_descriptor_->nested_type(5);
  TestMap_MapFixed32Fixed32Entry_descriptor_ = TestMap_descriptor_->nested_type(6);
  TestMap_MapFixed64Fixed64Entry_descriptor_ = TestMap_descriptor_->nested_type(7);
  TestMap_MapSfixed32Sfixed32Entry_descriptor_ = TestMap_descriptor_->nested_type(8);
  TestMap_MapSfixed64Sfixed64Entry_descriptor_ = TestMap_descriptor_->nested_type(9);
  TestMap_MapInt32FloatEntry_descriptor_ = TestMap_descriptor_->nested_type(10);
  TestMap_MapInt32DoubleEntry_descriptor_ = TestMap_descriptor_->nested_type(11);
  TestMap_MapBoolBoolEntry_descriptor_ = TestMap_descriptor_->nested_type(12);
  TestMap_MapStringStringEntry_descriptor_ = TestMap_descriptor_->nested_type(13);
  TestMap_MapInt32BytesEntry_descriptor_ = TestMap_descriptor_->nested_type(14);
  TestMap_MapInt32EnumEntry_descriptor_ = TestMap_descriptor_->nested_type(15);
  TestMap_MapInt32ForeignMessageEntry_descriptor_ = TestMap_descriptor_->nested_type(16);
  TestMap_MapStringForeignMessageEntry_descriptor_ = TestMap_descriptor_->nested_type(17);
  TestMapSubmessage_descriptor_ = file->message_type(1);
  static const int TestMapSubmessage_offsets_[1] = {
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(TestMapSubmessage, test_map_),
  };
  TestMapSubmessage_reflection_ =
    ::google::protobuf::internal::GeneratedMessageReflection::NewGeneratedMessageReflection(
      TestMapSubmessage_descriptor_,
      TestMapSubmessage::internal_default_instance(),
      TestMapSubmessage_offsets_,
      -1,
      -1,
      -1,
      sizeof(TestMapSubmessage),
      GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(TestMapSubmessage, _internal_metadata_));
  TestMessageMap_descriptor_ = file->message_type(2);
  static const int TestMessageMap_offsets_[1] = {
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(TestMessageMap, map_int32_message_),
  };
  TestMessageMap_reflection_ =
    ::google::protobuf::internal::GeneratedMessageReflection::NewGeneratedMessageReflection(
      TestMessageMap_descriptor_,
      TestMessageMap::internal_default_instance(),
      TestMessageMap_offsets_,
      -1,
      -1,
      -1,
      sizeof(TestMessageMap),
      GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(TestMessageMap, _internal_metadata_));
  TestMessageMap_MapInt32MessageEntry_descriptor_ = TestMessageMap_descriptor_->nested_type(0);
  TestSameTypeMap_descriptor_ = file->message_type(3);
  static const int TestSameTypeMap_offsets_[2] = {
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(TestSameTypeMap, map1_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(TestSameTypeMap, map2_),
  };
  TestSameTypeMap_reflection_ =
    ::google::protobuf::internal::GeneratedMessageReflection::NewGeneratedMessageReflection(
      TestSameTypeMap_descriptor_,
      TestSameTypeMap::internal_default_instance(),
      TestSameTypeMap_offsets_,
      -1,
      -1,
      -1,
      sizeof(TestSameTypeMap),
      GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(TestSameTypeMap, _internal_metadata_));
  TestSameTypeMap_Map1Entry_descriptor_ = TestSameTypeMap_descriptor_->nested_type(0);
  TestSameTypeMap_Map2Entry_descriptor_ = TestSameTypeMap_descriptor_->nested_type(1);
  TestRequiredMessageMap_descriptor_ = file->message_type(4);
  static const int TestRequiredMessageMap_offsets_[1] = {
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(TestRequiredMessageMap, map_field_),
  };
  TestRequiredMessageMap_reflection_ =
    ::google::protobuf::internal::GeneratedMessageReflection::NewGeneratedMessageReflection(
      TestRequiredMessageMap_descriptor_,
      TestRequiredMessageMap::internal_default_instance(),
      TestRequiredMessageMap_offsets_,
      -1,
      -1,
      -1,
      sizeof(TestRequiredMessageMap),
      GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(TestRequiredMessageMap, _internal_metadata_));
  TestRequiredMessageMap_MapFieldEntry_descriptor_ = TestRequiredMessageMap_descriptor_->nested_type(0);
  TestArenaMap_descriptor_ = file->message_type(5);
  static const int TestArenaMap_offsets_[18] = {
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(TestArenaMap, map_int32_int32_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(TestArenaMap, map_int64_int64_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(TestArenaMap, map_uint32_uint32_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(TestArenaMap, map_uint64_uint64_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(TestArenaMap, map_sint32_sint32_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(TestArenaMap, map_sint64_sint64_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(TestArenaMap, map_fixed32_fixed32_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(TestArenaMap, map_fixed64_fixed64_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(TestArenaMap, map_sfixed32_sfixed32_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(TestArenaMap, map_sfixed64_sfixed64_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(TestArenaMap, map_int32_float_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(TestArenaMap, map_int32_double_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(TestArenaMap, map_bool_bool_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(TestArenaMap, map_string_string_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(TestArenaMap, map_int32_bytes_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(TestArenaMap, map_int32_enum_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(TestArenaMap, map_int32_foreign_message_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(TestArenaMap, map_int32_foreign_message_no_arena_),
  };
  TestArenaMap_reflection_ =
    ::google::protobuf::internal::GeneratedMessageReflection::NewGeneratedMessageReflection(
      TestArenaMap_descriptor_,
      TestArenaMap::internal_default_instance(),
      TestArenaMap_offsets_,
      -1,
      -1,
      -1,
      sizeof(TestArenaMap),
      GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(TestArenaMap, _internal_metadata_));
  TestArenaMap_MapInt32Int32Entry_descriptor_ = TestArenaMap_descriptor_->nested_type(0);
  TestArenaMap_MapInt64Int64Entry_descriptor_ = TestArenaMap_descriptor_->nested_type(1);
  TestArenaMap_MapUint32Uint32Entry_descriptor_ = TestArenaMap_descriptor_->nested_type(2);
  TestArenaMap_MapUint64Uint64Entry_descriptor_ = TestArenaMap_descriptor_->nested_type(3);
  TestArenaMap_MapSint32Sint32Entry_descriptor_ = TestArenaMap_descriptor_->nested_type(4);
  TestArenaMap_MapSint64Sint64Entry_descriptor_ = TestArenaMap_descriptor_->nested_type(5);
  TestArenaMap_MapFixed32Fixed32Entry_descriptor_ = TestArenaMap_descriptor_->nested_type(6);
  TestArenaMap_MapFixed64Fixed64Entry_descriptor_ = TestArenaMap_descriptor_->nested_type(7);
  TestArenaMap_MapSfixed32Sfixed32Entry_descriptor_ = TestArenaMap_descriptor_->nested_type(8);
  TestArenaMap_MapSfixed64Sfixed64Entry_descriptor_ = TestArenaMap_descriptor_->nested_type(9);
  TestArenaMap_MapInt32FloatEntry_descriptor_ = TestArenaMap_descriptor_->nested_type(10);
  TestArenaMap_MapInt32DoubleEntry_descriptor_ = TestArenaMap_descriptor_->nested_type(11);
  TestArenaMap_MapBoolBoolEntry_descriptor_ = TestArenaMap_descriptor_->nested_type(12);
  TestArenaMap_MapStringStringEntry_descriptor_ = TestArenaMap_descriptor_->nested_type(13);
  TestArenaMap_MapInt32BytesEntry_descriptor_ = TestArenaMap_descriptor_->nested_type(14);
  TestArenaMap_MapInt32EnumEntry_descriptor_ = TestArenaMap_descriptor_->nested_type(15);
  TestArenaMap_MapInt32ForeignMessageEntry_descriptor_ = TestArenaMap_descriptor_->nested_type(16);
  TestArenaMap_MapInt32ForeignMessageNoArenaEntry_descriptor_ = TestArenaMap_descriptor_->nested_type(17);
  MessageContainingEnumCalledType_descriptor_ = file->message_type(6);
  static const int MessageContainingEnumCalledType_offsets_[1] = {
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MessageContainingEnumCalledType, type_),
  };
  MessageContainingEnumCalledType_reflection_ =
    ::google::protobuf::internal::GeneratedMessageReflection::NewGeneratedMessageReflection(
      MessageContainingEnumCalledType_descriptor_,
      MessageContainingEnumCalledType::internal_default_instance(),
      MessageContainingEnumCalledType_offsets_,
      -1,
      -1,
      -1,
      sizeof(MessageContainingEnumCalledType),
      GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MessageContainingEnumCalledType, _internal_metadata_));
  MessageContainingEnumCalledType_TypeEntry_descriptor_ = MessageContainingEnumCalledType_descriptor_->nested_type(0);
  MessageContainingEnumCalledType_Type_descriptor_ = MessageContainingEnumCalledType_descriptor_->enum_type(0);
  MessageContainingMapCalledEntry_descriptor_ = file->message_type(7);
  static const int MessageContainingMapCalledEntry_offsets_[1] = {
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MessageContainingMapCalledEntry, entry_),
  };
  MessageContainingMapCalledEntry_reflection_ =
    ::google::protobuf::internal::GeneratedMessageReflection::NewGeneratedMessageReflection(
      MessageContainingMapCalledEntry_descriptor_,
      MessageContainingMapCalledEntry::internal_default_instance(),
      MessageContainingMapCalledEntry_offsets_,
      -1,
      -1,
      -1,
      sizeof(MessageContainingMapCalledEntry),
      GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MessageContainingMapCalledEntry, _internal_metadata_));
  MessageContainingMapCalledEntry_EntryEntry_descriptor_ = MessageContainingMapCalledEntry_descriptor_->nested_type(0);
  TestRecursiveMapMessage_descriptor_ = file->message_type(8);
  static const int TestRecursiveMapMessage_offsets_[1] = {
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(TestRecursiveMapMessage, a_),
  };
  TestRecursiveMapMessage_reflection_ =
    ::google::protobuf::internal::GeneratedMessageReflection::NewGeneratedMessageReflection(
      TestRecursiveMapMessage_descriptor_,
      TestRecursiveMapMessage::internal_default_instance(),
      TestRecursiveMapMessage_offsets_,
      -1,
      -1,
      -1,
      sizeof(TestRecursiveMapMessage),
      GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(TestRecursiveMapMessage, _internal_metadata_));
  TestRecursiveMapMessage_AEntry_descriptor_ = TestRecursiveMapMessage_descriptor_->nested_type(0);
  MapEnum_descriptor_ = file->enum_type(0);
}

namespace {

GOOGLE_PROTOBUF_DECLARE_ONCE(protobuf_AssignDescriptors_once_);
void protobuf_AssignDescriptorsOnce() {
  ::google::protobuf::GoogleOnceInit(&protobuf_AssignDescriptors_once_,
                 &protobuf_AssignDesc_google_2fprotobuf_2fmap_5funittest_2eproto);
}

void protobuf_RegisterTypes(const ::std::string&) GOOGLE_ATTRIBUTE_COLD;
void protobuf_RegisterTypes(const ::std::string&) {
  protobuf_AssignDescriptorsOnce();
  ::google::protobuf::MessageFactory::InternalRegisterGeneratedMessage(
      TestMap_descriptor_, TestMap::internal_default_instance());
  ::google::protobuf::MessageFactory::InternalRegisterGeneratedMessage(
        TestMap_MapInt32Int32Entry_descriptor_,
        ::google::protobuf::internal::MapEntry<
            ::google::protobuf::int32,
            ::google::protobuf::int32,
            ::google::protobuf::internal::WireFormatLite::TYPE_INT32,
            ::google::protobuf::internal::WireFormatLite::TYPE_INT32,
            0>::CreateDefaultInstance(
                TestMap_MapInt32Int32Entry_descriptor_));
  ::google::protobuf::MessageFactory::InternalRegisterGeneratedMessage(
        TestMap_MapInt64Int64Entry_descriptor_,
        ::google::protobuf::internal::MapEntry<
            ::google::protobuf::int64,
            ::google::protobuf::int64,
            ::google::protobuf::internal::WireFormatLite::TYPE_INT64,
            ::google::protobuf::internal::WireFormatLite::TYPE_INT64,
            0>::CreateDefaultInstance(
                TestMap_MapInt64Int64Entry_descriptor_));
  ::google::protobuf::MessageFactory::InternalRegisterGeneratedMessage(
        TestMap_MapUint32Uint32Entry_descriptor_,
        ::google::protobuf::internal::MapEntry<
            ::google::protobuf::uint32,
            ::google::protobuf::uint32,
            ::google::protobuf::internal::WireFormatLite::TYPE_UINT32,
            ::google::protobuf::internal::WireFormatLite::TYPE_UINT32,
            0>::CreateDefaultInstance(
                TestMap_MapUint32Uint32Entry_descriptor_));
  ::google::protobuf::MessageFactory::InternalRegisterGeneratedMessage(
        TestMap_MapUint64Uint64Entry_descriptor_,
        ::google::protobuf::internal::MapEntry<
            ::google::protobuf::uint64,
            ::google::protobuf::uint64,
            ::google::protobuf::internal::WireFormatLite::TYPE_UINT64,
            ::google::protobuf::internal::WireFormatLite::TYPE_UINT64,
            0>::CreateDefaultInstance(
                TestMap_MapUint64Uint64Entry_descriptor_));
  ::google::protobuf::MessageFactory::InternalRegisterGeneratedMessage(
        TestMap_MapSint32Sint32Entry_descriptor_,
        ::google::protobuf::internal::MapEntry<
            ::google::protobuf::int32,
            ::google::protobuf::int32,
            ::google::protobuf::internal::WireFormatLite::TYPE_SINT32,
            ::google::protobuf::internal::WireFormatLite::TYPE_SINT32,
            0>::CreateDefaultInstance(
                TestMap_MapSint32Sint32Entry_descriptor_));
  ::google::protobuf::MessageFactory::InternalRegisterGeneratedMessage(
        TestMap_MapSint64Sint64Entry_descriptor_,
        ::google::protobuf::internal::MapEntry<
            ::google::protobuf::int64,
            ::google::protobuf::int64,
            ::google::protobuf::internal::WireFormatLite::TYPE_SINT64,
            ::google::protobuf::internal::WireFormatLite::TYPE_SINT64,
            0>::CreateDefaultInstance(
                TestMap_MapSint64Sint64Entry_descriptor_));
  ::google::protobuf::MessageFactory::InternalRegisterGeneratedMessage(
        TestMap_MapFixed32Fixed32Entry_descriptor_,
        ::google::protobuf::internal::MapEntry<
            ::google::protobuf::uint32,
            ::google::protobuf::uint32,
            ::google::protobuf::internal::WireFormatLite::TYPE_FIXED32,
            ::google::protobuf::internal::WireFormatLite::TYPE_FIXED32,
            0>::CreateDefaultInstance(
                TestMap_MapFixed32Fixed32Entry_descriptor_));
  ::google::protobuf::MessageFactory::InternalRegisterGeneratedMessage(
        TestMap_MapFixed64Fixed64Entry_descriptor_,
        ::google::protobuf::internal::MapEntry<
            ::google::protobuf::uint64,
            ::google::protobuf::uint64,
            ::google::protobuf::internal::WireFormatLite::TYPE_FIXED64,
            ::google::protobuf::internal::WireFormatLite::TYPE_FIXED64,
            0>::CreateDefaultInstance(
                TestMap_MapFixed64Fixed64Entry_descriptor_));
  ::google::protobuf::MessageFactory::InternalRegisterGeneratedMessage(
        TestMap_MapSfixed32Sfixed32Entry_descriptor_,
        ::google::protobuf::internal::MapEntry<
            ::google::protobuf::int32,
            ::google::protobuf::int32,
            ::google::protobuf::internal::WireFormatLite::TYPE_SFIXED32,
            ::google::protobuf::internal::WireFormatLite::TYPE_SFIXED32,
            0>::CreateDefaultInstance(
                TestMap_MapSfixed32Sfixed32Entry_descriptor_));
  ::google::protobuf::MessageFactory::InternalRegisterGeneratedMessage(
        TestMap_MapSfixed64Sfixed64Entry_descriptor_,
        ::google::protobuf::internal::MapEntry<
            ::google::protobuf::int64,
            ::google::protobuf::int64,
            ::google::protobuf::internal::WireFormatLite::TYPE_SFIXED64,
            ::google::protobuf::internal::WireFormatLite::TYPE_SFIXED64,
            0>::CreateDefaultInstance(
                TestMap_MapSfixed64Sfixed64Entry_descriptor_));
  ::google::protobuf::MessageFactory::InternalRegisterGeneratedMessage(
        TestMap_MapInt32FloatEntry_descriptor_,
        ::google::protobuf::internal::MapEntry<
            ::google::protobuf::int32,
            float,
            ::google::protobuf::internal::WireFormatLite::TYPE_INT32,
            ::google::protobuf::internal::WireFormatLite::TYPE_FLOAT,
            0>::CreateDefaultInstance(
                TestMap_MapInt32FloatEntry_descriptor_));
  ::google::protobuf::MessageFactory::InternalRegisterGeneratedMessage(
        TestMap_MapInt32DoubleEntry_descriptor_,
        ::google::protobuf::internal::MapEntry<
            ::google::protobuf::int32,
            double,
            ::google::protobuf::internal::WireFormatLite::TYPE_INT32,
            ::google::protobuf::internal::WireFormatLite::TYPE_DOUBLE,
            0>::CreateDefaultInstance(
                TestMap_MapInt32DoubleEntry_descriptor_));
  ::google::protobuf::MessageFactory::InternalRegisterGeneratedMessage(
        TestMap_MapBoolBoolEntry_descriptor_,
        ::google::protobuf::internal::MapEntry<
            bool,
            bool,
            ::google::protobuf::internal::WireFormatLite::TYPE_BOOL,
            ::google::protobuf::internal::WireFormatLite::TYPE_BOOL,
            0>::CreateDefaultInstance(
                TestMap_MapBoolBoolEntry_descriptor_));
  ::google::protobuf::MessageFactory::InternalRegisterGeneratedMessage(
        TestMap_MapStringStringEntry_descriptor_,
        ::google::protobuf::internal::MapEntry<
            ::std::string,
            ::std::string,
            ::google::protobuf::internal::WireFormatLite::TYPE_STRING,
            ::google::protobuf::internal::WireFormatLite::TYPE_STRING,
            0>::CreateDefaultInstance(
                TestMap_MapStringStringEntry_descriptor_));
  ::google::protobuf::MessageFactory::InternalRegisterGeneratedMessage(
        TestMap_MapInt32BytesEntry_descriptor_,
        ::google::protobuf::internal::MapEntry<
            ::google::protobuf::int32,
            ::std::string,
            ::google::protobuf::internal::WireFormatLite::TYPE_INT32,
            ::google::protobuf::internal::WireFormatLite::TYPE_BYTES,
            0>::CreateDefaultInstance(
                TestMap_MapInt32BytesEntry_descriptor_));
  ::google::protobuf::MessageFactory::InternalRegisterGeneratedMessage(
        TestMap_MapInt32EnumEntry_descriptor_,
        ::google::protobuf::internal::MapEntry<
            ::google::protobuf::int32,
            ::protobuf_unittest::MapEnum,
            ::google::protobuf::internal::WireFormatLite::TYPE_INT32,
            ::google::protobuf::internal::WireFormatLite::TYPE_ENUM,
            0>::CreateDefaultInstance(
                TestMap_MapInt32EnumEntry_descriptor_));
  ::google::protobuf::MessageFactory::InternalRegisterGeneratedMessage(
        TestMap_MapInt32ForeignMessageEntry_descriptor_,
        ::google::protobuf::internal::MapEntry<
            ::google::protobuf::int32,
            ::protobuf_unittest::ForeignMessage,
            ::google::protobuf::internal::WireFormatLite::TYPE_INT32,
            ::google::protobuf::internal::WireFormatLite::TYPE_MESSAGE,
            0>::CreateDefaultInstance(
                TestMap_MapInt32ForeignMessageEntry_descriptor_));
  ::google::protobuf::MessageFactory::InternalRegisterGeneratedMessage(
        TestMap_MapStringForeignMessageEntry_descriptor_,
        ::google::protobuf::internal::MapEntry<
            ::std::string,
            ::protobuf_unittest::ForeignMessage,
            ::google::protobuf::internal::WireFormatLite::TYPE_STRING,
            ::google::protobuf::internal::WireFormatLite::TYPE_MESSAGE,
            0>::CreateDefaultInstance(
                TestMap_MapStringForeignMessageEntry_descriptor_));
  ::google::protobuf::MessageFactory::InternalRegisterGeneratedMessage(
      TestMapSubmessage_descriptor_, TestMapSubmessage::internal_default_instance());
  ::google::protobuf::MessageFactory::InternalRegisterGeneratedMessage(
      TestMessageMap_descriptor_, TestMessageMap::internal_default_instance());
  ::google::protobuf::MessageFactory::InternalRegisterGeneratedMessage(
        TestMessageMap_MapInt32MessageEntry_descriptor_,
        ::google::protobuf::internal::MapEntry<
            ::google::protobuf::int32,
            ::protobuf_unittest::TestAllTypes,
            ::google::protobuf::internal::WireFormatLite::TYPE_INT32,
            ::google::protobuf::internal::WireFormatLite::TYPE_MESSAGE,
            0>::CreateDefaultInstance(
                TestMessageMap_MapInt32MessageEntry_descriptor_));
  ::google::protobuf::MessageFactory::InternalRegisterGeneratedMessage(
      TestSameTypeMap_descriptor_, TestSameTypeMap::internal_default_instance());
  ::google::protobuf::MessageFactory::InternalRegisterGeneratedMessage(
        TestSameTypeMap_Map1Entry_descriptor_,
        ::google::protobuf::internal::MapEntry<
            ::google::protobuf::int32,
            ::google::protobuf::int32,
            ::google::protobuf::internal::WireFormatLite::TYPE_INT32,
            ::google::protobuf::internal::WireFormatLite::TYPE_INT32,
            0>::CreateDefaultInstance(
                TestSameTypeMap_Map1Entry_descriptor_));
  ::google::protobuf::MessageFactory::InternalRegisterGeneratedMessage(
        TestSameTypeMap_Map2Entry_descriptor_,
        ::google::protobuf::internal::MapEntry<
            ::google::protobuf::int32,
            ::google::protobuf::int32,
            ::google::protobuf::internal::WireFormatLite::TYPE_INT32,
            ::google::protobuf::internal::WireFormatLite::TYPE_INT32,
            0>::CreateDefaultInstance(
                TestSameTypeMap_Map2Entry_descriptor_));
  ::google::protobuf::MessageFactory::InternalRegisterGeneratedMessage(
      TestRequiredMessageMap_descriptor_, TestRequiredMessageMap::internal_default_instance());
  ::google::protobuf::MessageFactory::InternalRegisterGeneratedMessage(
        TestRequiredMessageMap_MapFieldEntry_descriptor_,
        ::google::protobuf::internal::MapEntry<
            ::google::protobuf::int32,
            ::protobuf_unittest::TestRequired,
            ::google::protobuf::internal::WireFormatLite::TYPE_INT32,
            ::google::protobuf::internal::WireFormatLite::TYPE_MESSAGE,
            0>::CreateDefaultInstance(
                TestRequiredMessageMap_MapFieldEntry_descriptor_));
  ::google::protobuf::MessageFactory::InternalRegisterGeneratedMessage(
      TestArenaMap_descriptor_, TestArenaMap::internal_default_instance());
  ::google::protobuf::MessageFactory::InternalRegisterGeneratedMessage(
        TestArenaMap_MapInt32Int32Entry_descriptor_,
        ::google::protobuf::internal::MapEntry<
            ::google::protobuf::int32,
            ::google::protobuf::int32,
            ::google::protobuf::internal::WireFormatLite::TYPE_INT32,
            ::google::protobuf::internal::WireFormatLite::TYPE_INT32,
            0>::CreateDefaultInstance(
                TestArenaMap_MapInt32Int32Entry_descriptor_));
  ::google::protobuf::MessageFactory::InternalRegisterGeneratedMessage(
        TestArenaMap_MapInt64Int64Entry_descriptor_,
        ::google::protobuf::internal::MapEntry<
            ::google::protobuf::int64,
            ::google::protobuf::int64,
            ::google::protobuf::internal::WireFormatLite::TYPE_INT64,
            ::google::protobuf::internal::WireFormatLite::TYPE_INT64,
            0>::CreateDefaultInstance(
                TestArenaMap_MapInt64Int64Entry_descriptor_));
  ::google::protobuf::MessageFactory::InternalRegisterGeneratedMessage(
        TestArenaMap_MapUint32Uint32Entry_descriptor_,
        ::google::protobuf::internal::MapEntry<
            ::google::protobuf::uint32,
            ::google::protobuf::uint32,
            ::google::protobuf::internal::WireFormatLite::TYPE_UINT32,
            ::google::protobuf::internal::WireFormatLite::TYPE_UINT32,
            0>::CreateDefaultInstance(
                TestArenaMap_MapUint32Uint32Entry_descriptor_));
  ::google::protobuf::MessageFactory::InternalRegisterGeneratedMessage(
        TestArenaMap_MapUint64Uint64Entry_descriptor_,
        ::google::protobuf::internal::MapEntry<
            ::google::protobuf::uint64,
            ::google::protobuf::uint64,
            ::google::protobuf::internal::WireFormatLite::TYPE_UINT64,
            ::google::protobuf::internal::WireFormatLite::TYPE_UINT64,
            0>::CreateDefaultInstance(
                TestArenaMap_MapUint64Uint64Entry_descriptor_));
  ::google::protobuf::MessageFactory::InternalRegisterGeneratedMessage(
        TestArenaMap_MapSint32Sint32Entry_descriptor_,
        ::google::protobuf::internal::MapEntry<
            ::google::protobuf::int32,
            ::google::protobuf::int32,
            ::google::protobuf::internal::WireFormatLite::TYPE_SINT32,
            ::google::protobuf::internal::WireFormatLite::TYPE_SINT32,
            0>::CreateDefaultInstance(
                TestArenaMap_MapSint32Sint32Entry_descriptor_));
  ::google::protobuf::MessageFactory::InternalRegisterGeneratedMessage(
        TestArenaMap_MapSint64Sint64Entry_descriptor_,
        ::google::protobuf::internal::MapEntry<
            ::google::protobuf::int64,
            ::google::protobuf::int64,
            ::google::protobuf::internal::WireFormatLite::TYPE_SINT64,
            ::google::protobuf::internal::WireFormatLite::TYPE_SINT64,
            0>::CreateDefaultInstance(
                TestArenaMap_MapSint64Sint64Entry_descriptor_));
  ::google::protobuf::MessageFactory::InternalRegisterGeneratedMessage(
        TestArenaMap_MapFixed32Fixed32Entry_descriptor_,
        ::google::protobuf::internal::MapEntry<
            ::google::protobuf::uint32,
            ::google::protobuf::uint32,
            ::google::protobuf::internal::WireFormatLite::TYPE_FIXED32,
            ::google::protobuf::internal::WireFormatLite::TYPE_FIXED32,
            0>::CreateDefaultInstance(
                TestArenaMap_MapFixed32Fixed32Entry_descriptor_));
  ::google::protobuf::MessageFactory::InternalRegisterGeneratedMessage(
        TestArenaMap_MapFixed64Fixed64Entry_descriptor_,
        ::google::protobuf::internal::MapEntry<
            ::google::protobuf::uint64,
            ::google::protobuf::uint64,
            ::google::protobuf::internal::WireFormatLite::TYPE_FIXED64,
            ::google::protobuf::internal::WireFormatLite::TYPE_FIXED64,
            0>::CreateDefaultInstance(
                TestArenaMap_MapFixed64Fixed64Entry_descriptor_));
  ::google::protobuf::MessageFactory::InternalRegisterGeneratedMessage(
        TestArenaMap_MapSfixed32Sfixed32Entry_descriptor_,
        ::google::protobuf::internal::MapEntry<
            ::google::protobuf::int32,
            ::google::protobuf::int32,
            ::google::protobuf::internal::WireFormatLite::TYPE_SFIXED32,
            ::google::protobuf::internal::WireFormatLite::TYPE_SFIXED32,
            0>::CreateDefaultInstance(
                TestArenaMap_MapSfixed32Sfixed32Entry_descriptor_));
  ::google::protobuf::MessageFactory::InternalRegisterGeneratedMessage(
        TestArenaMap_MapSfixed64Sfixed64Entry_descriptor_,
        ::google::protobuf::internal::MapEntry<
            ::google::protobuf::int64,
            ::google::protobuf::int64,
            ::google::protobuf::internal::WireFormatLite::TYPE_SFIXED64,
            ::google::protobuf::internal::WireFormatLite::TYPE_SFIXED64,
            0>::CreateDefaultInstance(
                TestArenaMap_MapSfixed64Sfixed64Entry_descriptor_));
  ::google::protobuf::MessageFactory::InternalRegisterGeneratedMessage(
        TestArenaMap_MapInt32FloatEntry_descriptor_,
        ::google::protobuf::internal::MapEntry<
            ::google::protobuf::int32,
            float,
            ::google::protobuf::internal::WireFormatLite::TYPE_INT32,
            ::google::protobuf::internal::WireFormatLite::TYPE_FLOAT,
            0>::CreateDefaultInstance(
                TestArenaMap_MapInt32FloatEntry_descriptor_));
  ::google::protobuf::MessageFactory::InternalRegisterGeneratedMessage(
        TestArenaMap_MapInt32DoubleEntry_descriptor_,
        ::google::protobuf::internal::MapEntry<
            ::google::protobuf::int32,
            double,
            ::google::protobuf::internal::WireFormatLite::TYPE_INT32,
            ::google::protobuf::internal::WireFormatLite::TYPE_DOUBLE,
            0>::CreateDefaultInstance(
                TestArenaMap_MapInt32DoubleEntry_descriptor_));
  ::google::protobuf::MessageFactory::InternalRegisterGeneratedMessage(
        TestArenaMap_MapBoolBoolEntry_descriptor_,
        ::google::protobuf::internal::MapEntry<
            bool,
            bool,
            ::google::protobuf::internal::WireFormatLite::TYPE_BOOL,
            ::google::protobuf::internal::WireFormatLite::TYPE_BOOL,
            0>::CreateDefaultInstance(
                TestArenaMap_MapBoolBoolEntry_descriptor_));
  ::google::protobuf::MessageFactory::InternalRegisterGeneratedMessage(
        TestArenaMap_MapStringStringEntry_descriptor_,
        ::google::protobuf::internal::MapEntry<
            ::std::string,
            ::std::string,
            ::google::protobuf::internal::WireFormatLite::TYPE_STRING,
            ::google::protobuf::internal::WireFormatLite::TYPE_STRING,
            0>::CreateDefaultInstance(
                TestArenaMap_MapStringStringEntry_descriptor_));
  ::google::protobuf::MessageFactory::InternalRegisterGeneratedMessage(
        TestArenaMap_MapInt32BytesEntry_descriptor_,
        ::google::protobuf::internal::MapEntry<
            ::google::protobuf::int32,
            ::std::string,
            ::google::protobuf::internal::WireFormatLite::TYPE_INT32,
            ::google::protobuf::internal::WireFormatLite::TYPE_BYTES,
            0>::CreateDefaultInstance(
                TestArenaMap_MapInt32BytesEntry_descriptor_));
  ::google::protobuf::MessageFactory::InternalRegisterGeneratedMessage(
        TestArenaMap_MapInt32EnumEntry_descriptor_,
        ::google::protobuf::internal::MapEntry<
            ::google::protobuf::int32,
            ::protobuf_unittest::MapEnum,
            ::google::protobuf::internal::WireFormatLite::TYPE_INT32,
            ::google::protobuf::internal::WireFormatLite::TYPE_ENUM,
            0>::CreateDefaultInstance(
                TestArenaMap_MapInt32EnumEntry_descriptor_));
  ::google::protobuf::MessageFactory::InternalRegisterGeneratedMessage(
        TestArenaMap_MapInt32ForeignMessageEntry_descriptor_,
        ::google::protobuf::internal::MapEntry<
            ::google::protobuf::int32,
            ::protobuf_unittest::ForeignMessage,
            ::google::protobuf::internal::WireFormatLite::TYPE_INT32,
            ::google::protobuf::internal::WireFormatLite::TYPE_MESSAGE,
            0>::CreateDefaultInstance(
                TestArenaMap_MapInt32ForeignMessageEntry_descriptor_));
  ::google::protobuf::MessageFactory::InternalRegisterGeneratedMessage(
        TestArenaMap_MapInt32ForeignMessageNoArenaEntry_descriptor_,
        ::google::protobuf::internal::MapEntry<
            ::google::protobuf::int32,
            ::protobuf_unittest_no_arena::ForeignMessage,
            ::google::protobuf::internal::WireFormatLite::TYPE_INT32,
            ::google::protobuf::internal::WireFormatLite::TYPE_MESSAGE,
            0>::CreateDefaultInstance(
                TestArenaMap_MapInt32ForeignMessageNoArenaEntry_descriptor_));
  ::google::protobuf::MessageFactory::InternalRegisterGeneratedMessage(
      MessageContainingEnumCalledType_descriptor_, MessageContainingEnumCalledType::internal_default_instance());
  ::google::protobuf::MessageFactory::InternalRegisterGeneratedMessage(
        MessageContainingEnumCalledType_TypeEntry_descriptor_,
        ::google::protobuf::internal::MapEntry<
            ::std::string,
            ::protobuf_unittest::MessageContainingEnumCalledType,
            ::google::protobuf::internal::WireFormatLite::TYPE_STRING,
            ::google::protobuf::internal::WireFormatLite::TYPE_MESSAGE,
            0>::CreateDefaultInstance(
                MessageContainingEnumCalledType_TypeEntry_descriptor_));
  ::google::protobuf::MessageFactory::InternalRegisterGeneratedMessage(
      MessageContainingMapCalledEntry_descriptor_, MessageContainingMapCalledEntry::internal_default_instance());
  ::google::protobuf::MessageFactory::InternalRegisterGeneratedMessage(
        MessageContainingMapCalledEntry_EntryEntry_descriptor_,
        ::google::protobuf::internal::MapEntry<
            ::google::protobuf::int32,
            ::google::protobuf::int32,
            ::google::protobuf::internal::WireFormatLite::TYPE_INT32,
            ::google::protobuf::internal::WireFormatLite::TYPE_INT32,
            0>::CreateDefaultInstance(
                MessageContainingMapCalledEntry_EntryEntry_descriptor_));
  ::google::protobuf::MessageFactory::InternalRegisterGeneratedMessage(
      TestRecursiveMapMessage_descriptor_, TestRecursiveMapMessage::internal_default_instance());
  ::google::protobuf::MessageFactory::InternalRegisterGeneratedMessage(
        TestRecursiveMapMessage_AEntry_descriptor_,
        ::google::protobuf::internal::MapEntry<
            ::std::string,
            ::protobuf_unittest::TestRecursiveMapMessage,
            ::google::protobuf::internal::WireFormatLite::TYPE_STRING,
            ::google::protobuf::internal::WireFormatLite::TYPE_MESSAGE,
            0>::CreateDefaultInstance(
                TestRecursiveMapMessage_AEntry_descriptor_));
}

}  // namespace

void protobuf_ShutdownFile_google_2fprotobuf_2fmap_5funittest_2eproto() {
  TestMap_default_instance_.Shutdown();
  delete TestMap_reflection_;
  TestMapSubmessage_default_instance_.Shutdown();
  delete TestMapSubmessage_reflection_;
  TestMessageMap_default_instance_.Shutdown();
  delete TestMessageMap_reflection_;
  TestSameTypeMap_default_instance_.Shutdown();
  delete TestSameTypeMap_reflection_;
  TestRequiredMessageMap_default_instance_.Shutdown();
  delete TestRequiredMessageMap_reflection_;
  TestArenaMap_default_instance_.Shutdown();
  delete TestArenaMap_reflection_;
  MessageContainingEnumCalledType_default_instance_.Shutdown();
  delete MessageContainingEnumCalledType_reflection_;
  MessageContainingMapCalledEntry_default_instance_.Shutdown();
  delete MessageContainingMapCalledEntry_reflection_;
  TestRecursiveMapMessage_default_instance_.Shutdown();
  delete TestRecursiveMapMessage_reflection_;
}

void protobuf_InitDefaults_google_2fprotobuf_2fmap_5funittest_2eproto_impl() {
  GOOGLE_PROTOBUF_VERIFY_VERSION;

  ::protobuf_unittest::protobuf_InitDefaults_google_2fprotobuf_2funittest_2eproto();
  ::protobuf_unittest_no_arena::protobuf_InitDefaults_google_2fprotobuf_2funittest_5fno_5farena_2eproto();
  ::google::protobuf::internal::GetEmptyString();
  TestMap_default_instance_.DefaultConstruct();
  ::google::protobuf::internal::GetEmptyString();
  ::google::protobuf::internal::GetEmptyString();
  ::google::protobuf::internal::GetEmptyString();
  ::google::protobuf::internal::GetEmptyString();
  ::google::protobuf::internal::GetEmptyString();
  ::google::protobuf::internal::GetEmptyString();
  ::google::protobuf::internal::GetEmptyString();
  ::google::protobuf::internal::GetEmptyString();
  ::google::protobuf::internal::GetEmptyString();
  ::google::protobuf::internal::GetEmptyString();
  ::google::protobuf::internal::GetEmptyString();
  ::google::protobuf::internal::GetEmptyString();
  ::google::protobuf::internal::GetEmptyString();
  TestMapSubmessage_default_instance_.DefaultConstruct();
  TestMessageMap_default_instance_.DefaultConstruct();
  TestSameTypeMap_default_instance_.DefaultConstruct();
  ::google::protobuf::internal::GetEmptyString();
  ::google::protobuf::internal::GetEmptyString();
  TestRequiredMessageMap_default_instance_.DefaultConstruct();
  ::google::protobuf::internal::GetEmptyString();
  TestArenaMap_default_instance_.DefaultConstruct();
  ::google::protobuf::internal::GetEmptyString();
  ::google::protobuf::internal::GetEmptyString();
  ::google::protobuf::internal::GetEmptyString();
  ::google::protobuf::internal::GetEmptyString();
  ::google::protobuf::internal::GetEmptyString();
  ::google::protobuf::internal::GetEmptyString();
  ::google::protobuf::internal::GetEmptyString();
  ::google::protobuf::internal::GetEmptyString();
  ::google::protobuf::internal::GetEmptyString();
  ::google::protobuf::internal::GetEmptyString();
  ::google::protobuf::internal::GetEmptyString();
  ::google::protobuf::internal::GetEmptyString();
  ::google::protobuf::internal::GetEmptyString();
  ::google::protobuf::internal::GetEmptyString();
  MessageContainingEnumCalledType_default_instance_.DefaultConstruct();
  ::google::protobuf::internal::GetEmptyString();
  MessageContainingMapCalledEntry_default_instance_.DefaultConstruct();
  ::google::protobuf::internal::GetEmptyString();
  ::google::protobuf::internal::GetEmptyString();
  TestRecursiveMapMessage_default_instance_.DefaultConstruct();
  TestMap_default_instance_.get_mutable()->InitAsDefaultInstance();
  TestMapSubmessage_default_instance_.get_mutable()->InitAsDefaultInstance();
  TestMessageMap_default_instance_.get_mutable()->InitAsDefaultInstance();
  TestSameTypeMap_default_instance_.get_mutable()->InitAsDefaultInstance();
  TestRequiredMessageMap_default_instance_.get_mutable()->InitAsDefaultInstance();
  TestArenaMap_default_instance_.get_mutable()->InitAsDefaultInstance();
  MessageContainingEnumCalledType_default_instance_.get_mutable()->InitAsDefaultInstance();
  MessageContainingMapCalledEntry_default_instance_.get_mutable()->InitAsDefaultInstance();
  TestRecursiveMapMessage_default_instance_.get_mutable()->InitAsDefaultInstance();
}

GOOGLE_PROTOBUF_DECLARE_ONCE(protobuf_InitDefaults_google_2fprotobuf_2fmap_5funittest_2eproto_once_);
void protobuf_InitDefaults_google_2fprotobuf_2fmap_5funittest_2eproto() {
  ::google::protobuf::GoogleOnceInit(&protobuf_InitDefaults_google_2fprotobuf_2fmap_5funittest_2eproto_once_,
                 &protobuf_InitDefaults_google_2fprotobuf_2fmap_5funittest_2eproto_impl);
}
void protobuf_AddDesc_google_2fprotobuf_2fmap_5funittest_2eproto_impl() {
  GOOGLE_PROTOBUF_VERIFY_VERSION;

  protobuf_InitDefaults_google_2fprotobuf_2fmap_5funittest_2eproto();
  ::google::protobuf::DescriptorPool::InternalAddGeneratedFile(
    "\n\"google/protobuf/map_unittest.proto\022\021pr"
    "otobuf_unittest\032\036google/protobuf/unittes"
    "t.proto\032\'google/protobuf/unittest_no_are"
    "na.proto\"\326\023\n\007TestMap\022F\n\017map_int32_int32\030"
    "\001 \003(\0132-.protobuf_unittest.TestMap.MapInt"
    "32Int32Entry\022F\n\017map_int64_int64\030\002 \003(\0132-."
    "protobuf_unittest.TestMap.MapInt64Int64E"
    "ntry\022J\n\021map_uint32_uint32\030\003 \003(\0132/.protob"
    "uf_unittest.TestMap.MapUint32Uint32Entry"
    "\022J\n\021map_uint64_uint64\030\004 \003(\0132/.protobuf_u"
    "nittest.TestMap.MapUint64Uint64Entry\022J\n\021"
    "map_sint32_sint32\030\005 \003(\0132/.protobuf_unitt"
    "est.TestMap.MapSint32Sint32Entry\022J\n\021map_"
    "sint64_sint64\030\006 \003(\0132/.protobuf_unittest."
    "TestMap.MapSint64Sint64Entry\022N\n\023map_fixe"
    "d32_fixed32\030\007 \003(\01321.protobuf_unittest.Te"
    "stMap.MapFixed32Fixed32Entry\022N\n\023map_fixe"
    "d64_fixed64\030\010 \003(\01321.protobuf_unittest.Te"
    "stMap.MapFixed64Fixed64Entry\022R\n\025map_sfix"
    "ed32_sfixed32\030\t \003(\01323.protobuf_unittest."
    "TestMap.MapSfixed32Sfixed32Entry\022R\n\025map_"
    "sfixed64_sfixed64\030\n \003(\01323.protobuf_unitt"
    "est.TestMap.MapSfixed64Sfixed64Entry\022F\n\017"
    "map_int32_float\030\013 \003(\0132-.protobuf_unittes"
    "t.TestMap.MapInt32FloatEntry\022H\n\020map_int3"
    "2_double\030\014 \003(\0132..protobuf_unittest.TestM"
    "ap.MapInt32DoubleEntry\022B\n\rmap_bool_bool\030"
    "\r \003(\0132+.protobuf_unittest.TestMap.MapBoo"
    "lBoolEntry\022J\n\021map_string_string\030\016 \003(\0132/."
    "protobuf_unittest.TestMap.MapStringStrin"
    "gEntry\022F\n\017map_int32_bytes\030\017 \003(\0132-.protob"
    "uf_unittest.TestMap.MapInt32BytesEntry\022D"
    "\n\016map_int32_enum\030\020 \003(\0132,.protobuf_unitte"
    "st.TestMap.MapInt32EnumEntry\022Y\n\031map_int3"
    "2_foreign_message\030\021 \003(\01326.protobuf_unitt"
    "est.TestMap.MapInt32ForeignMessageEntry\022"
    "[\n\032map_string_foreign_message\030\022 \003(\01327.pr"
    "otobuf_unittest.TestMap.MapStringForeign"
    "MessageEntry\0324\n\022MapInt32Int32Entry\022\013\n\003ke"
    "y\030\001 \001(\005\022\r\n\005value\030\002 \001(\005:\0028\001\0324\n\022MapInt64In"
    "t64Entry\022\013\n\003key\030\001 \001(\003\022\r\n\005value\030\002 \001(\003:\0028\001"
    "\0326\n\024MapUint32Uint32Entry\022\013\n\003key\030\001 \001(\r\022\r\n"
    "\005value\030\002 \001(\r:\0028\001\0326\n\024MapUint64Uint64Entry"
    "\022\013\n\003key\030\001 \001(\004\022\r\n\005value\030\002 \001(\004:\0028\001\0326\n\024MapS"
    "int32Sint32Entry\022\013\n\003key\030\001 \001(\021\022\r\n\005value\030\002"
    " \001(\021:\0028\001\0326\n\024MapSint64Sint64Entry\022\013\n\003key\030"
    "\001 \001(\022\022\r\n\005value\030\002 \001(\022:\0028\001\0328\n\026MapFixed32Fi"
    "xed32Entry\022\013\n\003key\030\001 \001(\007\022\r\n\005value\030\002 \001(\007:\002"
    "8\001\0328\n\026MapFixed64Fixed64Entry\022\013\n\003key\030\001 \001("
    "\006\022\r\n\005value\030\002 \001(\006:\0028\001\032:\n\030MapSfixed32Sfixe"
    "d32Entry\022\013\n\003key\030\001 \001(\017\022\r\n\005value\030\002 \001(\017:\0028\001"
    "\032:\n\030MapSfixed64Sfixed64Entry\022\013\n\003key\030\001 \001("
    "\020\022\r\n\005value\030\002 \001(\020:\0028\001\0324\n\022MapInt32FloatEnt"
    "ry\022\013\n\003key\030\001 \001(\005\022\r\n\005value\030\002 \001(\002:\0028\001\0325\n\023Ma"
    "pInt32DoubleEntry\022\013\n\003key\030\001 \001(\005\022\r\n\005value\030"
    "\002 \001(\001:\0028\001\0322\n\020MapBoolBoolEntry\022\013\n\003key\030\001 \001"
    "(\010\022\r\n\005value\030\002 \001(\010:\0028\001\0326\n\024MapStringString"
    "Entry\022\013\n\003key\030\001 \001(\t\022\r\n\005value\030\002 \001(\t:\0028\001\0324\n"
    "\022MapInt32BytesEntry\022\013\n\003key\030\001 \001(\005\022\r\n\005valu"
    "e\030\002 \001(\014:\0028\001\032O\n\021MapInt32EnumEntry\022\013\n\003key\030"
    "\001 \001(\005\022)\n\005value\030\002 \001(\0162\032.protobuf_unittest"
    ".MapEnum:\0028\001\032`\n\033MapInt32ForeignMessageEn"
    "try\022\013\n\003key\030\001 \001(\005\0220\n\005value\030\002 \001(\0132!.protob"
    "uf_unittest.ForeignMessage:\0028\001\032a\n\034MapStr"
    "ingForeignMessageEntry\022\013\n\003key\030\001 \001(\t\0220\n\005v"
    "alue\030\002 \001(\0132!.protobuf_unittest.ForeignMe"
    "ssage:\0028\001\"A\n\021TestMapSubmessage\022,\n\010test_m"
    "ap\030\001 \001(\0132\032.protobuf_unittest.TestMap\"\274\001\n"
    "\016TestMessageMap\022Q\n\021map_int32_message\030\001 \003"
    "(\01326.protobuf_unittest.TestMessageMap.Ma"
    "pInt32MessageEntry\032W\n\024MapInt32MessageEnt"
    "ry\022\013\n\003key\030\001 \001(\005\022.\n\005value\030\002 \001(\0132\037.protobu"
    "f_unittest.TestAllTypes:\0028\001\"\343\001\n\017TestSame"
    "TypeMap\022:\n\004map1\030\001 \003(\0132,.protobuf_unittes"
    "t.TestSameTypeMap.Map1Entry\022:\n\004map2\030\002 \003("
    "\0132,.protobuf_unittest.TestSameTypeMap.Ma"
    "p2Entry\032+\n\tMap1Entry\022\013\n\003key\030\001 \001(\005\022\r\n\005val"
    "ue\030\002 \001(\005:\0028\001\032+\n\tMap2Entry\022\013\n\003key\030\001 \001(\005\022\r"
    "\n\005value\030\002 \001(\005:\0028\001\"\266\001\n\026TestRequiredMessag"
    "eMap\022J\n\tmap_field\030\001 \003(\01327.protobuf_unitt"
    "est.TestRequiredMessageMap.MapFieldEntry"
    "\032P\n\rMapFieldEntry\022\013\n\003key\030\001 \001(\005\022.\n\005value\030"
    "\002 \001(\0132\037.protobuf_unittest.TestRequired:\002"
    "8\001\"\322\024\n\014TestArenaMap\022K\n\017map_int32_int32\030\001"
    " \003(\01322.protobuf_unittest.TestArenaMap.Ma"
    "pInt32Int32Entry\022K\n\017map_int64_int64\030\002 \003("
    "\01322.protobuf_unittest.TestArenaMap.MapIn"
    "t64Int64Entry\022O\n\021map_uint32_uint32\030\003 \003(\013"
    "24.protobuf_unittest.TestArenaMap.MapUin"
    "t32Uint32Entry\022O\n\021map_uint64_uint64\030\004 \003("
    "\01324.protobuf_unittest.TestArenaMap.MapUi"
    "nt64Uint64Entry\022O\n\021map_sint32_sint32\030\005 \003"
    "(\01324.protobuf_unittest.TestArenaMap.MapS"
    "int32Sint32Entry\022O\n\021map_sint64_sint64\030\006 "
    "\003(\01324.protobuf_unittest.TestArenaMap.Map"
    "Sint64Sint64Entry\022S\n\023map_fixed32_fixed32"
    "\030\007 \003(\01326.protobuf_unittest.TestArenaMap."
    "MapFixed32Fixed32Entry\022S\n\023map_fixed64_fi"
    "xed64\030\010 \003(\01326.protobuf_unittest.TestAren"
    "aMap.MapFixed64Fixed64Entry\022W\n\025map_sfixe"
    "d32_sfixed32\030\t \003(\01328.protobuf_unittest.T"
    "estArenaMap.MapSfixed32Sfixed32Entry\022W\n\025"
    "map_sfixed64_sfixed64\030\n \003(\01328.protobuf_u"
    "nittest.TestArenaMap.MapSfixed64Sfixed64"
    "Entry\022K\n\017map_int32_float\030\013 \003(\01322.protobu"
    "f_unittest.TestArenaMap.MapInt32FloatEnt"
    "ry\022M\n\020map_int32_double\030\014 \003(\01323.protobuf_"
    "unittest.TestArenaMap.MapInt32DoubleEntr"
    "y\022G\n\rmap_bool_bool\030\r \003(\01320.protobuf_unit"
    "test.TestArenaMap.MapBoolBoolEntry\022O\n\021ma"
    "p_string_string\030\016 \003(\01324.protobuf_unittes"
    "t.TestArenaMap.MapStringStringEntry\022K\n\017m"
    "ap_int32_bytes\030\017 \003(\01322.protobuf_unittest"
    ".TestArenaMap.MapInt32BytesEntry\022I\n\016map_"
    "int32_enum\030\020 \003(\01321.protobuf_unittest.Tes"
    "tArenaMap.MapInt32EnumEntry\022^\n\031map_int32"
    "_foreign_message\030\021 \003(\0132;.protobuf_unitte"
    "st.TestArenaMap.MapInt32ForeignMessageEn"
    "try\022n\n\"map_int32_foreign_message_no_aren"
    "a\030\022 \003(\0132B.protobuf_unittest.TestArenaMap"
    ".MapInt32ForeignMessageNoArenaEntry\0324\n\022M"
    "apInt32Int32Entry\022\013\n\003key\030\001 \001(\005\022\r\n\005value\030"
    "\002 \001(\005:\0028\001\0324\n\022MapInt64Int64Entry\022\013\n\003key\030\001"
    " \001(\003\022\r\n\005value\030\002 \001(\003:\0028\001\0326\n\024MapUint32Uint"
    "32Entry\022\013\n\003key\030\001 \001(\r\022\r\n\005value\030\002 \001(\r:\0028\001\032"
    "6\n\024MapUint64Uint64Entry\022\013\n\003key\030\001 \001(\004\022\r\n\005"
    "value\030\002 \001(\004:\0028\001\0326\n\024MapSint32Sint32Entry\022"
    "\013\n\003key\030\001 \001(\021\022\r\n\005value\030\002 \001(\021:\0028\001\0326\n\024MapSi"
    "nt64Sint64Entry\022\013\n\003key\030\001 \001(\022\022\r\n\005value\030\002 "
    "\001(\022:\0028\001\0328\n\026MapFixed32Fixed32Entry\022\013\n\003key"
    "\030\001 \001(\007\022\r\n\005value\030\002 \001(\007:\0028\001\0328\n\026MapFixed64F"
    "ixed64Entry\022\013\n\003key\030\001 \001(\006\022\r\n\005value\030\002 \001(\006:"
    "\0028\001\032:\n\030MapSfixed32Sfixed32Entry\022\013\n\003key\030\001"
    " \001(\017\022\r\n\005value\030\002 \001(\017:\0028\001\032:\n\030MapSfixed64Sf"
    "ixed64Entry\022\013\n\003key\030\001 \001(\020\022\r\n\005value\030\002 \001(\020:"
    "\0028\001\0324\n\022MapInt32FloatEntry\022\013\n\003key\030\001 \001(\005\022\r"
    "\n\005value\030\002 \001(\002:\0028\001\0325\n\023MapInt32DoubleEntry"
    "\022\013\n\003key\030\001 \001(\005\022\r\n\005value\030\002 \001(\001:\0028\001\0322\n\020MapB"
    "oolBoolEntry\022\013\n\003key\030\001 \001(\010\022\r\n\005value\030\002 \001(\010"
    ":\0028\001\0326\n\024MapStringStringEntry\022\013\n\003key\030\001 \001("
    "\t\022\r\n\005value\030\002 \001(\t:\0028\001\0324\n\022MapInt32BytesEnt"
    "ry\022\013\n\003key\030\001 \001(\005\022\r\n\005value\030\002 \001(\014:\0028\001\032O\n\021Ma"
    "pInt32EnumEntry\022\013\n\003key\030\001 \001(\005\022)\n\005value\030\002 "
    "\001(\0162\032.protobuf_unittest.MapEnum:\0028\001\032`\n\033M"
    "apInt32ForeignMessageEntry\022\013\n\003key\030\001 \001(\005\022"
    "0\n\005value\030\002 \001(\0132!.protobuf_unittest.Forei"
    "gnMessage:\0028\001\032p\n\"MapInt32ForeignMessageN"
    "oArenaEntry\022\013\n\003key\030\001 \001(\005\0229\n\005value\030\002 \001(\0132"
    "*.protobuf_unittest_no_arena.ForeignMess"
    "age:\0028\001\"\344\001\n\037MessageContainingEnumCalledT"
    "ype\022J\n\004type\030\001 \003(\0132<.protobuf_unittest.Me"
    "ssageContainingEnumCalledType.TypeEntry\032"
    "_\n\tTypeEntry\022\013\n\003key\030\001 \001(\t\022A\n\005value\030\002 \001(\013"
    "22.protobuf_unittest.MessageContainingEn"
    "umCalledType:\0028\001\"\024\n\004Type\022\014\n\010TYPE_FOO\020\000\"\235"
    "\001\n\037MessageContainingMapCalledEntry\022L\n\005en"
    "try\030\001 \003(\0132=.protobuf_unittest.MessageCon"
    "tainingMapCalledEntry.EntryEntry\032,\n\nEntr"
    "yEntry\022\013\n\003key\030\001 \001(\005\022\r\n\005value\030\002 \001(\005:\0028\001\"\255"
    "\001\n\027TestRecursiveMapMessage\022<\n\001a\030\001 \003(\01321."
    "protobuf_unittest.TestRecursiveMapMessag"
    "e.AEntry\032T\n\006AEntry\022\013\n\003key\030\001 \001(\t\0229\n\005value"
    "\030\002 \001(\0132*.protobuf_unittest.TestRecursive"
    "MapMessage:\0028\001*\?\n\007MapEnum\022\020\n\014MAP_ENUM_FO"
    "O\020\000\022\020\n\014MAP_ENUM_BAR\020\001\022\020\n\014MAP_ENUM_BAZ\020\002B"
    "\003\370\001\001b\006proto3", 6612);
  ::google::protobuf::MessageFactory::InternalRegisterGeneratedFile(
    "google/protobuf/map_unittest.proto", &protobuf_RegisterTypes);
  ::protobuf_unittest::protobuf_AddDesc_google_2fprotobuf_2funittest_2eproto();
  ::protobuf_unittest_no_arena::protobuf_AddDesc_google_2fprotobuf_2funittest_5fno_5farena_2eproto();
  ::google::protobuf::internal::OnShutdown(&protobuf_ShutdownFile_google_2fprotobuf_2fmap_5funittest_2eproto);
}

GOOGLE_PROTOBUF_DECLARE_ONCE(protobuf_AddDesc_google_2fprotobuf_2fmap_5funittest_2eproto_once_);
void protobuf_AddDesc_google_2fprotobuf_2fmap_5funittest_2eproto() {
  ::google::protobuf::GoogleOnceInit(&protobuf_AddDesc_google_2fprotobuf_2fmap_5funittest_2eproto_once_,
                 &protobuf_AddDesc_google_2fprotobuf_2fmap_5funittest_2eproto_impl);
}
// Force AddDescriptors() to be called at static initialization time.
struct StaticDescriptorInitializer_google_2fprotobuf_2fmap_5funittest_2eproto {
  StaticDescriptorInitializer_google_2fprotobuf_2fmap_5funittest_2eproto() {
    protobuf_AddDesc_google_2fprotobuf_2fmap_5funittest_2eproto();
  }
} static_descriptor_initializer_google_2fprotobuf_2fmap_5funittest_2eproto_;
const ::google::protobuf::EnumDescriptor* MapEnum_descriptor() {
  protobuf_AssignDescriptorsOnce();
  return MapEnum_descriptor_;
}
bool MapEnum_IsValid(int value) {
  switch (value) {
    case 0:
    case 1:
    case 2:
      return true;
    default:
      return false;
  }
}


namespace {

static void MergeFromFail(int line) GOOGLE_ATTRIBUTE_COLD GOOGLE_ATTRIBUTE_NORETURN;
static void MergeFromFail(int line) {
  ::google::protobuf::internal::MergeFromFail(__FILE__, line);
}

}  // namespace


// ===================================================================

#if !defined(_MSC_VER) || _MSC_VER >= 1900
const int TestMap::kMapInt32Int32FieldNumber;
const int TestMap::kMapInt64Int64FieldNumber;
const int TestMap::kMapUint32Uint32FieldNumber;
const int TestMap::kMapUint64Uint64FieldNumber;
const int TestMap::kMapSint32Sint32FieldNumber;
const int TestMap::kMapSint64Sint64FieldNumber;
const int TestMap::kMapFixed32Fixed32FieldNumber;
const int TestMap::kMapFixed64Fixed64FieldNumber;
const int TestMap::kMapSfixed32Sfixed32FieldNumber;
const int TestMap::kMapSfixed64Sfixed64FieldNumber;
const int TestMap::kMapInt32FloatFieldNumber;
const int TestMap::kMapInt32DoubleFieldNumber;
const int TestMap::kMapBoolBoolFieldNumber;
const int TestMap::kMapStringStringFieldNumber;
const int TestMap::kMapInt32BytesFieldNumber;
const int TestMap::kMapInt32EnumFieldNumber;
const int TestMap::kMapInt32ForeignMessageFieldNumber;
const int TestMap::kMapStringForeignMessageFieldNumber;
#endif  // !defined(_MSC_VER) || _MSC_VER >= 1900

TestMap::TestMap()
  : ::google::protobuf::Message(), _internal_metadata_(NULL) {
  if (this != internal_default_instance()) protobuf_InitDefaults_google_2fprotobuf_2fmap_5funittest_2eproto();
  SharedCtor();
  // @@protoc_insertion_point(constructor:protobuf_unittest.TestMap)
}
TestMap::TestMap(::google::protobuf::Arena* arena)
  : ::google::protobuf::Message(),
  _internal_metadata_(arena),
  map_int32_int32_(arena),
  map_int64_int64_(arena),
  map_uint32_uint32_(arena),
  map_uint64_uint64_(arena),
  map_sint32_sint32_(arena),
  map_sint64_sint64_(arena),
  map_fixed32_fixed32_(arena),
  map_fixed64_fixed64_(arena),
  map_sfixed32_sfixed32_(arena),
  map_sfixed64_sfixed64_(arena),
  map_int32_float_(arena),
  map_int32_double_(arena),
  map_bool_bool_(arena),
  map_string_string_(arena),
  map_int32_bytes_(arena),
  map_int32_enum_(arena),
  map_int32_foreign_message_(arena),
  map_string_foreign_message_(arena) {
#ifdef GOOGLE_PROTOBUF_NO_STATIC_INITIALIZER
  protobuf_InitDefaults_google_2fprotobuf_2fmap_5funittest_2eproto();
#endif  // GOOGLE_PROTOBUF_NO_STATIC_INITIALIZER
  SharedCtor();
  RegisterArenaDtor(arena);
  // @@protoc_insertion_point(arena_constructor:protobuf_unittest.TestMap)
}

void TestMap::InitAsDefaultInstance() {
}

TestMap::TestMap(const TestMap& from)
  : ::google::protobuf::Message(),
    _internal_metadata_(NULL) {
  SharedCtor();
  UnsafeMergeFrom(from);
  // @@protoc_insertion_point(copy_constructor:protobuf_unittest.TestMap)
}

void TestMap::SharedCtor() {
  map_int32_int32_.SetAssignDescriptorCallback(
      protobuf_AssignDescriptorsOnce);
  map_int32_int32_.SetEntryDescriptor(
      &::protobuf_unittest::TestMap_MapInt32Int32Entry_descriptor_);
  map_int64_int64_.SetAssignDescriptorCallback(
      protobuf_AssignDescriptorsOnce);
  map_int64_int64_.SetEntryDescriptor(
      &::protobuf_unittest::TestMap_MapInt64Int64Entry_descriptor_);
  map_uint32_uint32_.SetAssignDescriptorCallback(
      protobuf_AssignDescriptorsOnce);
  map_uint32_uint32_.SetEntryDescriptor(
      &::protobuf_unittest::TestMap_MapUint32Uint32Entry_descriptor_);
  map_uint64_uint64_.SetAssignDescriptorCallback(
      protobuf_AssignDescriptorsOnce);
  map_uint64_uint64_.SetEntryDescriptor(
      &::protobuf_unittest::TestMap_MapUint64Uint64Entry_descriptor_);
  map_sint32_sint32_.SetAssignDescriptorCallback(
      protobuf_AssignDescriptorsOnce);
  map_sint32_sint32_.SetEntryDescriptor(
      &::protobuf_unittest::TestMap_MapSint32Sint32Entry_descriptor_);
  map_sint64_sint64_.SetAssignDescriptorCallback(
      protobuf_AssignDescriptorsOnce);
  map_sint64_sint64_.SetEntryDescriptor(
      &::protobuf_unittest::TestMap_MapSint64Sint64Entry_descriptor_);
  map_fixed32_fixed32_.SetAssignDescriptorCallback(
      protobuf_AssignDescriptorsOnce);
  map_fixed32_fixed32_.SetEntryDescriptor(
      &::protobuf_unittest::TestMap_MapFixed32Fixed32Entry_descriptor_);
  map_fixed64_fixed64_.SetAssignDescriptorCallback(
      protobuf_AssignDescriptorsOnce);
  map_fixed64_fixed64_.SetEntryDescriptor(
      &::protobuf_unittest::TestMap_MapFixed64Fixed64Entry_descriptor_);
  map_sfixed32_sfixed32_.SetAssignDescriptorCallback(
      protobuf_AssignDescriptorsOnce);
  map_sfixed32_sfixed32_.SetEntryDescriptor(
      &::protobuf_unittest::TestMap_MapSfixed32Sfixed32Entry_descriptor_);
  map_sfixed64_sfixed64_.SetAssignDescriptorCallback(
      protobuf_AssignDescriptorsOnce);
  map_sfixed64_sfixed64_.SetEntryDescriptor(
      &::protobuf_unittest::TestMap_MapSfixed64Sfixed64Entry_descriptor_);
  map_int32_float_.SetAssignDescriptorCallback(
      protobuf_AssignDescriptorsOnce);
  map_int32_float_.SetEntryDescriptor(
      &::protobuf_unittest::TestMap_MapInt32FloatEntry_descriptor_);
  map_int32_double_.SetAssignDescriptorCallback(
      protobuf_AssignDescriptorsOnce);
  map_int32_double_.SetEntryDescriptor(
      &::protobuf_unittest::TestMap_MapInt32DoubleEntry_descriptor_);
  map_bool_bool_.SetAssignDescriptorCallback(
      protobuf_AssignDescriptorsOnce);
  map_bool_bool_.SetEntryDescriptor(
      &::protobuf_unittest::TestMap_MapBoolBoolEntry_descriptor_);
  map_string_string_.SetAssignDescriptorCallback(
      protobuf_AssignDescriptorsOnce);
  map_string_string_.SetEntryDescriptor(
      &::protobuf_unittest::TestMap_MapStringStringEntry_descriptor_);
  map_int32_bytes_.SetAssignDescriptorCallback(
      protobuf_AssignDescriptorsOnce);
  map_int32_bytes_.SetEntryDescriptor(
      &::protobuf_unittest::TestMap_MapInt32BytesEntry_descriptor_);
  map_int32_enum_.SetAssignDescriptorCallback(
      protobuf_AssignDescriptorsOnce);
  map_int32_enum_.SetEntryDescriptor(
      &::protobuf_unittest::TestMap_MapInt32EnumEntry_descriptor_);
  map_int32_foreign_message_.SetAssignDescriptorCallback(
      protobuf_AssignDescriptorsOnce);
  map_int32_foreign_message_.SetEntryDescriptor(
      &::protobuf_unittest::TestMap_MapInt32ForeignMessageEntry_descriptor_);
  map_string_foreign_message_.SetAssignDescriptorCallback(
      protobuf_AssignDescriptorsOnce);
  map_string_foreign_message_.SetEntryDescriptor(
      &::protobuf_unittest::TestMap_MapStringForeignMessageEntry_descriptor_);
  _cached_size_ = 0;
}

TestMap::~TestMap() {
  // @@protoc_insertion_point(destructor:protobuf_unittest.TestMap)
  SharedDtor();
}

void TestMap::SharedDtor() {
  ::google::protobuf::Arena* arena = GetArenaNoVirtual();
  if (arena != NULL) {
    return;
  }

}

void TestMap::ArenaDtor(void* object) {
  TestMap* _this = reinterpret_cast< TestMap* >(object);
  (void)_this;
}
void TestMap::RegisterArenaDtor(::google::protobuf::Arena* arena) {
}
void TestMap::SetCachedSize(int size) const {
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
}
const ::google::protobuf::Descriptor* TestMap::descriptor() {
  protobuf_AssignDescriptorsOnce();
  return TestMap_descriptor_;
}

const TestMap& TestMap::default_instance() {
  protobuf_InitDefaults_google_2fprotobuf_2fmap_5funittest_2eproto();
  return *internal_default_instance();
}

::google::protobuf::internal::ExplicitlyConstructed<TestMap> TestMap_default_instance_;

TestMap* TestMap::New(::google::protobuf::Arena* arena) const {
  return ::google::protobuf::Arena::CreateMessage<TestMap>(arena);
}

void TestMap::Clear() {
// @@protoc_insertion_point(message_clear_start:protobuf_unittest.TestMap)
  map_int32_int32_.Clear();
  map_int64_int64_.Clear();
  map_uint32_uint32_.Clear();
  map_uint64_uint64_.Clear();
  map_sint32_sint32_.Clear();
  map_sint64_sint64_.Clear();
  map_fixed32_fixed32_.Clear();
  map_fixed64_fixed64_.Clear();
  map_sfixed32_sfixed32_.Clear();
  map_sfixed64_sfixed64_.Clear();
  map_int32_float_.Clear();
  map_int32_double_.Clear();
  map_bool_bool_.Clear();
  map_string_string_.Clear();
  map_int32_bytes_.Clear();
  map_int32_enum_.Clear();
  map_int32_foreign_message_.Clear();
  map_string_foreign_message_.Clear();
}

bool TestMap::MergePartialFromCodedStream(
    ::google::protobuf::io::CodedInputStream* input) {
#define DO_(EXPRESSION) if (!GOOGLE_PREDICT_TRUE(EXPRESSION)) goto failure
  ::google::protobuf::uint32 tag;
  // @@protoc_insertion_point(parse_start:protobuf_unittest.TestMap)
  for (;;) {
    ::std::pair< ::google::protobuf::uint32, bool> p = input->ReadTagWithCutoff(16383);
    tag = p.first;
    if (!p.second) goto handle_unusual;
    switch (::google::protobuf::internal::WireFormatLite::GetTagFieldNumber(tag)) {
      // map<int32, int32> map_int32_int32 = 1;
      case 1: {
        if (tag == 10) {
          DO_(input->IncrementRecursionDepth());
         parse_loop_map_int32_int32:
          TestMap_MapInt32Int32Entry::Parser< ::google::protobuf::internal::MapField<
              ::google::protobuf::int32, ::google::protobuf::int32,
              ::google::protobuf::internal::WireFormatLite::TYPE_INT32,
              ::google::protobuf::internal::WireFormatLite::TYPE_INT32,
              0 >,
            ::google::protobuf::Map< ::google::protobuf::int32, ::google::protobuf::int32 > > parser(&map_int32_int32_);
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessageNoVirtual(
              input, &parser));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(10)) goto parse_loop_map_int32_int32;
        if (input->ExpectTag(18)) goto parse_loop_map_int64_int64;
        input->UnsafeDecrementRecursionDepth();
        break;
      }

      // map<int64, int64> map_int64_int64 = 2;
      case 2: {
        if (tag == 18) {
          DO_(input->IncrementRecursionDepth());
         parse_loop_map_int64_int64:
          TestMap_MapInt64Int64Entry::Parser< ::google::protobuf::internal::MapField<
              ::google::protobuf::int64, ::google::protobuf::int64,
              ::google::protobuf::internal::WireFormatLite::TYPE_INT64,
              ::google::protobuf::internal::WireFormatLite::TYPE_INT64,
              0 >,
            ::google::protobuf::Map< ::google::protobuf::int64, ::google::protobuf::int64 > > parser(&map_int64_int64_);
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessageNoVirtual(
              input, &parser));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(18)) goto parse_loop_map_int64_int64;
        if (input->ExpectTag(26)) goto parse_loop_map_uint32_uint32;
        input->UnsafeDecrementRecursionDepth();
        break;
      }

      // map<uint32, uint32> map_uint32_uint32 = 3;
      case 3: {
        if (tag == 26) {
          DO_(input->IncrementRecursionDepth());
         parse_loop_map_uint32_uint32:
          TestMap_MapUint32Uint32Entry::Parser< ::google::protobuf::internal::MapField<
              ::google::protobuf::uint32, ::google::protobuf::uint32,
              ::google::protobuf::internal::WireFormatLite::TYPE_UINT32,
              ::google::protobuf::internal::WireFormatLite::TYPE_UINT32,
              0 >,
            ::google::protobuf::Map< ::google::protobuf::uint32, ::google::protobuf::uint32 > > parser(&map_uint32_uint32_);
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessageNoVirtual(
              input, &parser));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(26)) goto parse_loop_map_uint32_uint32;
        if (input->ExpectTag(34)) goto parse_loop_map_uint64_uint64;
        input->UnsafeDecrementRecursionDepth();
        break;
      }

      // map<uint64, uint64> map_uint64_uint64 = 4;
      case 4: {
        if (tag == 34) {
          DO_(input->IncrementRecursionDepth());
         parse_loop_map_uint64_uint64:
          TestMap_MapUint64Uint64Entry::Parser< ::google::protobuf::internal::MapField<
              ::google::protobuf::uint64, ::google::protobuf::uint64,
              ::google::protobuf::internal::WireFormatLite::TYPE_UINT64,
              ::google::protobuf::internal::WireFormatLite::TYPE_UINT64,
              0 >,
            ::google::protobuf::Map< ::google::protobuf::uint64, ::google::protobuf::uint64 > > parser(&map_uint64_uint64_);
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessageNoVirtual(
              input, &parser));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(34)) goto parse_loop_map_uint64_uint64;
        if (input->ExpectTag(42)) goto parse_loop_map_sint32_sint32;
        input->UnsafeDecrementRecursionDepth();
        break;
      }

      // map<sint32, sint32> map_sint32_sint32 = 5;
      case 5: {
        if (tag == 42) {
          DO_(input->IncrementRecursionDepth());
         parse_loop_map_sint32_sint32:
          TestMap_MapSint32Sint32Entry::Parser< ::google::protobuf::internal::MapField<
              ::google::protobuf::int32, ::google::protobuf::int32,
              ::google::protobuf::internal::WireFormatLite::TYPE_SINT32,
              ::google::protobuf::internal::WireFormatLite::TYPE_SINT32,
              0 >,
            ::google::protobuf::Map< ::google::protobuf::int32, ::google::protobuf::int32 > > parser(&map_sint32_sint32_);
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessageNoVirtual(
              input, &parser));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(42)) goto parse_loop_map_sint32_sint32;
        if (input->ExpectTag(50)) goto parse_loop_map_sint64_sint64;
        input->UnsafeDecrementRecursionDepth();
        break;
      }

      // map<sint64, sint64> map_sint64_sint64 = 6;
      case 6: {
        if (tag == 50) {
          DO_(input->IncrementRecursionDepth());
         parse_loop_map_sint64_sint64:
          TestMap_MapSint64Sint64Entry::Parser< ::google::protobuf::internal::MapField<
              ::google::protobuf::int64, ::google::protobuf::int64,
              ::google::protobuf::internal::WireFormatLite::TYPE_SINT64,
              ::google::protobuf::internal::WireFormatLite::TYPE_SINT64,
              0 >,
            ::google::protobuf::Map< ::google::protobuf::int64, ::google::protobuf::int64 > > parser(&map_sint64_sint64_);
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessageNoVirtual(
              input, &parser));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(50)) goto parse_loop_map_sint64_sint64;
        if (input->ExpectTag(58)) goto parse_loop_map_fixed32_fixed32;
        input->UnsafeDecrementRecursionDepth();
        break;
      }

      // map<fixed32, fixed32> map_fixed32_fixed32 = 7;
      case 7: {
        if (tag == 58) {
          DO_(input->IncrementRecursionDepth());
         parse_loop_map_fixed32_fixed32:
          TestMap_MapFixed32Fixed32Entry::Parser< ::google::protobuf::internal::MapField<
              ::google::protobuf::uint32, ::google::protobuf::uint32,
              ::google::protobuf::internal::WireFormatLite::TYPE_FIXED32,
              ::google::protobuf::internal::WireFormatLite::TYPE_FIXED32,
              0 >,
            ::google::protobuf::Map< ::google::protobuf::uint32, ::google::protobuf::uint32 > > parser(&map_fixed32_fixed32_);
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessageNoVirtual(
              input, &parser));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(58)) goto parse_loop_map_fixed32_fixed32;
        if (input->ExpectTag(66)) goto parse_loop_map_fixed64_fixed64;
        input->UnsafeDecrementRecursionDepth();
        break;
      }

      // map<fixed64, fixed64> map_fixed64_fixed64 = 8;
      case 8: {
        if (tag == 66) {
          DO_(input->IncrementRecursionDepth());
         parse_loop_map_fixed64_fixed64:
          TestMap_MapFixed64Fixed64Entry::Parser< ::google::protobuf::internal::MapField<
              ::google::protobuf::uint64, ::google::protobuf::uint64,
              ::google::protobuf::internal::WireFormatLite::TYPE_FIXED64,
              ::google::protobuf::internal::WireFormatLite::TYPE_FIXED64,
              0 >,
            ::google::protobuf::Map< ::google::protobuf::uint64, ::google::protobuf::uint64 > > parser(&map_fixed64_fixed64_);
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessageNoVirtual(
              input, &parser));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(66)) goto parse_loop_map_fixed64_fixed64;
        if (input->ExpectTag(74)) goto parse_loop_map_sfixed32_sfixed32;
        input->UnsafeDecrementRecursionDepth();
        break;
      }

      // map<sfixed32, sfixed32> map_sfixed32_sfixed32 = 9;
      case 9: {
        if (tag == 74) {
          DO_(input->IncrementRecursionDepth());
         parse_loop_map_sfixed32_sfixed32:
          TestMap_MapSfixed32Sfixed32Entry::Parser< ::google::protobuf::internal::MapField<
              ::google::protobuf::int32, ::google::protobuf::int32,
              ::google::protobuf::internal::WireFormatLite::TYPE_SFIXED32,
              ::google::protobuf::internal::WireFormatLite::TYPE_SFIXED32,
              0 >,
            ::google::protobuf::Map< ::google::protobuf::int32, ::google::protobuf::int32 > > parser(&map_sfixed32_sfixed32_);
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessageNoVirtual(
              input, &parser));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(74)) goto parse_loop_map_sfixed32_sfixed32;
        if (input->ExpectTag(82)) goto parse_loop_map_sfixed64_sfixed64;
        input->UnsafeDecrementRecursionDepth();
        break;
      }

      // map<sfixed64, sfixed64> map_sfixed64_sfixed64 = 10;
      case 10: {
        if (tag == 82) {
          DO_(input->IncrementRecursionDepth());
         parse_loop_map_sfixed64_sfixed64:
          TestMap_MapSfixed64Sfixed64Entry::Parser< ::google::protobuf::internal::MapField<
              ::google::protobuf::int64, ::google::protobuf::int64,
              ::google::protobuf::internal::WireFormatLite::TYPE_SFIXED64,
              ::google::protobuf::internal::WireFormatLite::TYPE_SFIXED64,
              0 >,
            ::google::protobuf::Map< ::google::protobuf::int64, ::google::protobuf::int64 > > parser(&map_sfixed64_sfixed64_);
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessageNoVirtual(
              input, &parser));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(82)) goto parse_loop_map_sfixed64_sfixed64;
        if (input->ExpectTag(90)) goto parse_loop_map_int32_float;
        input->UnsafeDecrementRecursionDepth();
        break;
      }

      // map<int32, float> map_int32_float = 11;
      case 11: {
        if (tag == 90) {
          DO_(input->IncrementRecursionDepth());
         parse_loop_map_int32_float:
          TestMap_MapInt32FloatEntry::Parser< ::google::protobuf::internal::MapField<
              ::google::protobuf::int32, float,
              ::google::protobuf::internal::WireFormatLite::TYPE_INT32,
              ::google::protobuf::internal::WireFormatLite::TYPE_FLOAT,
              0 >,
            ::google::protobuf::Map< ::google::protobuf::int32, float > > parser(&map_int32_float_);
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessageNoVirtual(
              input, &parser));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(90)) goto parse_loop_map_int32_float;
        if (input->ExpectTag(98)) goto parse_loop_map_int32_double;
        input->UnsafeDecrementRecursionDepth();
        break;
      }

      // map<int32, double> map_int32_double = 12;
      case 12: {
        if (tag == 98) {
          DO_(input->IncrementRecursionDepth());
         parse_loop_map_int32_double:
          TestMap_MapInt32DoubleEntry::Parser< ::google::protobuf::internal::MapField<
              ::google::protobuf::int32, double,
              ::google::protobuf::internal::WireFormatLite::TYPE_INT32,
              ::google::protobuf::internal::WireFormatLite::TYPE_DOUBLE,
              0 >,
            ::google::protobuf::Map< ::google::protobuf::int32, double > > parser(&map_int32_double_);
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessageNoVirtual(
              input, &parser));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(98)) goto parse_loop_map_int32_double;
        if (input->ExpectTag(106)) goto parse_loop_map_bool_bool;
        input->UnsafeDecrementRecursionDepth();
        break;
      }

      // map<bool, bool> map_bool_bool = 13;
      case 13: {
        if (tag == 106) {
          DO_(input->IncrementRecursionDepth());
         parse_loop_map_bool_bool:
          TestMap_MapBoolBoolEntry::Parser< ::google::protobuf::internal::MapField<
              bool, bool,
              ::google::protobuf::internal::WireFormatLite::TYPE_BOOL,
              ::google::protobuf::internal::WireFormatLite::TYPE_BOOL,
              0 >,
            ::google::protobuf::Map< bool, bool > > parser(&map_bool_bool_);
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessageNoVirtual(
              input, &parser));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(106)) goto parse_loop_map_bool_bool;
        if (input->ExpectTag(114)) goto parse_loop_map_string_string;
        input->UnsafeDecrementRecursionDepth();
        break;
      }

      // map<string, string> map_string_string = 14;
      case 14: {
        if (tag == 114) {
          DO_(input->IncrementRecursionDepth());
         parse_loop_map_string_string:
          TestMap_MapStringStringEntry::Parser< ::google::protobuf::internal::MapField<
              ::std::string, ::std::string,
              ::google::protobuf::internal::WireFormatLite::TYPE_STRING,
              ::google::protobuf::internal::WireFormatLite::TYPE_STRING,
              0 >,
            ::google::protobuf::Map< ::std::string, ::std::string > > parser(&map_string_string_);
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessageNoVirtual(
              input, &parser));
          DO_(::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
            parser.key().data(), parser.key().length(),
            ::google::protobuf::internal::WireFormatLite::PARSE,
            "protobuf_unittest.TestMap.MapStringStringEntry.key"));
          DO_(::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
            parser.value().data(), parser.value().length(),
            ::google::protobuf::internal::WireFormatLite::PARSE,
            "protobuf_unittest.TestMap.MapStringStringEntry.value"));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(114)) goto parse_loop_map_string_string;
        if (input->ExpectTag(122)) goto parse_loop_map_int32_bytes;
        input->UnsafeDecrementRecursionDepth();
        break;
      }

      // map<int32, bytes> map_int32_bytes = 15;
      case 15: {
        if (tag == 122) {
          DO_(input->IncrementRecursionDepth());
         parse_loop_map_int32_bytes:
          TestMap_MapInt32BytesEntry::Parser< ::google::protobuf::internal::MapField<
              ::google::protobuf::int32, ::std::string,
              ::google::protobuf::internal::WireFormatLite::TYPE_INT32,
              ::google::protobuf::internal::WireFormatLite::TYPE_BYTES,
              0 >,
            ::google::protobuf::Map< ::google::protobuf::int32, ::std::string > > parser(&map_int32_bytes_);
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessageNoVirtual(
              input, &parser));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(122)) goto parse_loop_map_int32_bytes;
        if (input->ExpectTag(130)) goto parse_loop_map_int32_enum;
        input->UnsafeDecrementRecursionDepth();
        break;
      }

      // map<int32, .protobuf_unittest.MapEnum> map_int32_enum = 16;
      case 16: {
        if (tag == 130) {
          DO_(input->IncrementRecursionDepth());
         parse_loop_map_int32_enum:
          TestMap_MapInt32EnumEntry::Parser< ::google::protobuf::internal::MapField<
              ::google::protobuf::int32, ::protobuf_unittest::MapEnum,
              ::google::protobuf::internal::WireFormatLite::TYPE_INT32,
              ::google::protobuf::internal::WireFormatLite::TYPE_ENUM,
              0 >,
            ::google::protobuf::Map< ::google::protobuf::int32, ::protobuf_unittest::MapEnum > > parser(&map_int32_enum_);
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessageNoVirtual(
              input, &parser));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(130)) goto parse_loop_map_int32_enum;
        if (input->ExpectTag(138)) goto parse_loop_map_int32_foreign_message;
        input->UnsafeDecrementRecursionDepth();
        break;
      }

      // map<int32, .protobuf_unittest.ForeignMessage> map_int32_foreign_message = 17;
      case 17: {
        if (tag == 138) {
          DO_(input->IncrementRecursionDepth());
         parse_loop_map_int32_foreign_message:
          TestMap_MapInt32ForeignMessageEntry::Parser< ::google::protobuf::internal::MapField<
              ::google::protobuf::int32, ::protobuf_unittest::ForeignMessage,
              ::google::protobuf::internal::WireFormatLite::TYPE_INT32,
              ::google::protobuf::internal::WireFormatLite::TYPE_MESSAGE,
              0 >,
            ::google::protobuf::Map< ::google::protobuf::int32, ::protobuf_unittest::ForeignMessage > > parser(&map_int32_foreign_message_);
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessageNoVirtual(
              input, &parser));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(138)) goto parse_loop_map_int32_foreign_message;
        if (input->ExpectTag(146)) goto parse_loop_map_string_foreign_message;
        input->UnsafeDecrementRecursionDepth();
        break;
      }

      // map<string, .protobuf_unittest.ForeignMessage> map_string_foreign_message = 18;
      case 18: {
        if (tag == 146) {
          DO_(input->IncrementRecursionDepth());
         parse_loop_map_string_foreign_message:
          TestMap_MapStringForeignMessageEntry::Parser< ::google::protobuf::internal::MapField<
              ::std::string, ::protobuf_unittest::ForeignMessage,
              ::google::protobuf::internal::WireFormatLite::TYPE_STRING,
              ::google::protobuf::internal::WireFormatLite::TYPE_MESSAGE,
              0 >,
            ::google::protobuf::Map< ::std::string, ::protobuf_unittest::ForeignMessage > > parser(&map_string_foreign_message_);
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessageNoVirtual(
              input, &parser));
          DO_(::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
            parser.key().data(), parser.key().length(),
            ::google::protobuf::internal::WireFormatLite::PARSE,
            "protobuf_unittest.TestMap.MapStringForeignMessageEntry.key"));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(146)) goto parse_loop_map_string_foreign_message;
        input->UnsafeDecrementRecursionDepth();
        if (input->ExpectAtEnd()) goto success;
        break;
      }

      default: {
      handle_unusual:
        if (tag == 0 ||
            ::google::protobuf::internal::WireFormatLite::GetTagWireType(tag) ==
            ::google::protobuf::internal::WireFormatLite::WIRETYPE_END_GROUP) {
          goto success;
        }
        DO_(::google::protobuf::internal::WireFormatLite::SkipField(input, tag));
        break;
      }
    }
  }
success:
  // @@protoc_insertion_point(parse_success:protobuf_unittest.TestMap)
  return true;
failure:
  // @@protoc_insertion_point(parse_failure:protobuf_unittest.TestMap)
  return false;
#undef DO_
}

void TestMap::SerializeWithCachedSizes(
    ::google::protobuf::io::CodedOutputStream* output) const {
  // @@protoc_insertion_point(serialize_start:protobuf_unittest.TestMap)
  // map<int32, int32> map_int32_int32 = 1;
  if (!this->map_int32_int32().empty()) {
    typedef ::google::protobuf::Map< ::google::protobuf::int32, ::google::protobuf::int32 >::const_pointer
        ConstPtr;
    typedef ::google::protobuf::internal::SortItem< ::google::protobuf::int32, ConstPtr > SortItem;
    typedef ::google::protobuf::internal::CompareByFirstField<SortItem> Less;

    if (output->IsSerializationDeterminstic() &&
        this->map_int32_int32().size() > 1) {
      ::google::protobuf::scoped_array<SortItem> items(
          new SortItem[this->map_int32_int32().size()]);
      typedef ::google::protobuf::Map< ::google::protobuf::int32, ::google::protobuf::int32 >::size_type size_type;
      size_type n = 0;
      for (::google::protobuf::Map< ::google::protobuf::int32, ::google::protobuf::int32 >::const_iterator
          it = this->map_int32_int32().begin();
          it != this->map_int32_int32().end(); ++it, ++n) {
        items[n] = SortItem(&*it);
      }
      ::std::sort(&items[0], &items[n], Less());
      ::google::protobuf::scoped_ptr<TestMap_MapInt32Int32Entry> entry;
      for (size_type i = 0; i < n; i++) {
        entry.reset(map_int32_int32_.NewEntryWrapper(
            items[i].second->first, items[i].second->second));
        ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
            1, *entry, output);
        if (entry->GetArena() != NULL) {
          entry.release();
        }
      }
    } else {
      ::google::protobuf::scoped_ptr<TestMap_MapInt32Int32Entry> entry;
      for (::google::protobuf::Map< ::google::protobuf::int32, ::google::protobuf::int32 >::const_iterator
          it = this->map_int32_int32().begin();
          it != this->map_int32_int32().end(); ++it) {
        entry.reset(map_int32_int32_.NewEntryWrapper(
            it->first, it->second));
        ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
            1, *entry, output);
        if (entry->GetArena() != NULL) {
          entry.release();
        }
      }
    }
  }

  // map<int64, int64> map_int64_int64 = 2;
  if (!this->map_int64_int64().empty()) {
    typedef ::google::protobuf::Map< ::google::protobuf::int64, ::google::protobuf::int64 >::const_pointer
        ConstPtr;
    typedef ::google::protobuf::internal::SortItem< ::google::protobuf::int64, ConstPtr > SortItem;
    typedef ::google::protobuf::internal::CompareByFirstField<SortItem> Less;

    if (output->IsSerializationDeterminstic() &&
        this->map_int64_int64().size() > 1) {
      ::google::protobuf::scoped_array<SortItem> items(
          new SortItem[this->map_int64_int64().size()]);
      typedef ::google::protobuf::Map< ::google::protobuf::int64, ::google::protobuf::int64 >::size_type size_type;
      size_type n = 0;
      for (::google::protobuf::Map< ::google::protobuf::int64, ::google::protobuf::int64 >::const_iterator
          it = this->map_int64_int64().begin();
          it != this->map_int64_int64().end(); ++it, ++n) {
        items[n] = SortItem(&*it);
      }
      ::std::sort(&items[0], &items[n], Less());
      ::google::protobuf::scoped_ptr<TestMap_MapInt64Int64Entry> entry;
      for (size_type i = 0; i < n; i++) {
        entry.reset(map_int64_int64_.NewEntryWrapper(
            items[i].second->first, items[i].second->second));
        ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
            2, *entry, output);
        if (entry->GetArena() != NULL) {
          entry.release();
        }
      }
    } else {
      ::google::protobuf::scoped_ptr<TestMap_MapInt64Int64Entry> entry;
      for (::google::protobuf::Map< ::google::protobuf::int64, ::google::protobuf::int64 >::const_iterator
          it = this->map_int64_int64().begin();
          it != this->map_int64_int64().end(); ++it) {
        entry.reset(map_int64_int64_.NewEntryWrapper(
            it->first, it->second));
        ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
            2, *entry, output);
        if (entry->GetArena() != NULL) {
          entry.release();
        }
      }
    }
  }

  // map<uint32, uint32> map_uint32_uint32 = 3;
  if (!this->map_uint32_uint32().empty()) {
    typedef ::google::protobuf::Map< ::google::protobuf::uint32, ::google::protobuf::uint32 >::const_pointer
        ConstPtr;
    typedef ::google::protobuf::internal::SortItem< ::google::protobuf::uint32, ConstPtr > SortItem;
    typedef ::google::protobuf::internal::CompareByFirstField<SortItem> Less;

    if (output->IsSerializationDeterminstic() &&
        this->map_uint32_uint32().size() > 1) {
      ::google::protobuf::scoped_array<SortItem> items(
          new SortItem[this->map_uint32_uint32().size()]);
      typedef ::google::protobuf::Map< ::google::protobuf::uint32, ::google::protobuf::uint32 >::size_type size_type;
      size_type n = 0;
      for (::google::protobuf::Map< ::google::protobuf::uint32, ::google::protobuf::uint32 >::const_iterator
          it = this->map_uint32_uint32().begin();
          it != this->map_uint32_uint32().end(); ++it, ++n) {
        items[n] = SortItem(&*it);
      }
      ::std::sort(&items[0], &items[n], Less());
      ::google::protobuf::scoped_ptr<TestMap_MapUint32Uint32Entry> entry;
      for (size_type i = 0; i < n; i++) {
        entry.reset(map_uint32_uint32_.NewEntryWrapper(
            items[i].second->first, items[i].second->second));
        ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
            3, *entry, output);
        if (entry->GetArena() != NULL) {
          entry.release();
        }
      }
    } else {
      ::google::protobuf::scoped_ptr<TestMap_MapUint32Uint32Entry> entry;
      for (::google::protobuf::Map< ::google::protobuf::uint32, ::google::protobuf::uint32 >::const_iterator
          it = this->map_uint32_uint32().begin();
          it != this->map_uint32_uint32().end(); ++it) {
        entry.reset(map_uint32_uint32_.NewEntryWrapper(
            it->first, it->second));
        ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
            3, *entry, output);
        if (entry->GetArena() != NULL) {
          entry.release();
        }
      }
    }
  }

  // map<uint64, uint64> map_uint64_uint64 = 4;
  if (!this->map_uint64_uint64().empty()) {
    typedef ::google::protobuf::Map< ::google::protobuf::uint64, ::google::protobuf::uint64 >::const_pointer
        ConstPtr;
    typedef ::google::protobuf::internal::SortItem< ::google::protobuf::uint64, ConstPtr > SortItem;
    typedef ::google::protobuf::internal::CompareByFirstField<SortItem> Less;

    if (output->IsSerializationDeterminstic() &&
        this->map_uint64_uint64().size() > 1) {
      ::google::protobuf::scoped_array<SortItem> items(
          new SortItem[this->map_uint64_uint64().size()]);
      typedef ::google::protobuf::Map< ::google::protobuf::uint64, ::google::protobuf::uint64 >::size_type size_type;
      size_type n = 0;
      for (::google::protobuf::Map< ::google::protobuf::uint64, ::google::protobuf::uint64 >::const_iterator
          it = this->map_uint64_uint64().begin();
          it != this->map_uint64_uint64().end(); ++it, ++n) {
        items[n] = SortItem(&*it);
      }
      ::std::sort(&items[0], &items[n], Less());
      ::google::protobuf::scoped_ptr<TestMap_MapUint64Uint64Entry> entry;
      for (size_type i = 0; i < n; i++) {
        entry.reset(map_uint64_uint64_.NewEntryWrapper(
            items[i].second->first, items[i].second->second));
        ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
            4, *entry, output);
        if (entry->GetArena() != NULL) {
          entry.release();
        }
      }
    } else {
      ::google::protobuf::scoped_ptr<TestMap_MapUint64Uint64Entry> entry;
      for (::google::protobuf::Map< ::google::protobuf::uint64, ::google::protobuf::uint64 >::const_iterator
          it = this->map_uint64_uint64().begin();
          it != this->map_uint64_uint64().end(); ++it) {
        entry.reset(map_uint64_uint64_.NewEntryWrapper(
            it->first, it->second));
        ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
            4, *entry, output);
        if (entry->GetArena() != NULL) {
          entry.release();
        }
      }
    }
  }

  // map<sint32, sint32> map_sint32_sint32 = 5;
  if (!this->map_sint32_sint32().empty()) {
    typedef ::google::protobuf::Map< ::google::protobuf::int32, ::google::protobuf::int32 >::const_pointer
        ConstPtr;
    typedef ::google::protobuf::internal::SortItem< ::google::protobuf::int32, ConstPtr > SortItem;
    typedef ::google::protobuf::internal::CompareByFirstField<SortItem> Less;

    if (output->IsSerializationDeterminstic() &&
        this->map_sint32_sint32().size() > 1) {
      ::google::protobuf::scoped_array<SortItem> items(
          new SortItem[this->map_sint32_sint32().size()]);
      typedef ::google::protobuf::Map< ::google::protobuf::int32, ::google::protobuf::int32 >::size_type size_type;
      size_type n = 0;
      for (::google::protobuf::Map< ::google::protobuf::int32, ::google::protobuf::int32 >::const_iterator
          it = this->map_sint32_sint32().begin();
          it != this->map_sint32_sint32().end(); ++it, ++n) {
        items[n] = SortItem(&*it);
      }
      ::std::sort(&items[0], &items[n], Less());
      ::google::protobuf::scoped_ptr<TestMap_MapSint32Sint32Entry> entry;
      for (size_type i = 0; i < n; i++) {
        entry.reset(map_sint32_sint32_.NewEntryWrapper(
            items[i].second->first, items[i].second->second));
        ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
            5, *entry, output);
        if (entry->GetArena() != NULL) {
          entry.release();
        }
      }
    } else {
      ::google::protobuf::scoped_ptr<TestMap_MapSint32Sint32Entry> entry;
      for (::google::protobuf::Map< ::google::protobuf::int32, ::google::protobuf::int32 >::const_iterator
          it = this->map_sint32_sint32().begin();
          it != this->map_sint32_sint32().end(); ++it) {
        entry.reset(map_sint32_sint32_.NewEntryWrapper(
            it->first, it->second));
        ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
            5, *entry, output);
        if (entry->GetArena() != NULL) {
          entry.release();
        }
      }
    }
  }

  // map<sint64, sint64> map_sint64_sint64 = 6;
  if (!this->map_sint64_sint64().empty()) {
    typedef ::google::protobuf::Map< ::google::protobuf::int64, ::google::protobuf::int64 >::const_pointer
        ConstPtr;
    typedef ::google::protobuf::internal::SortItem< ::google::protobuf::int64, ConstPtr > SortItem;
    typedef ::google::protobuf::internal::CompareByFirstField<SortItem> Less;

    if (output->IsSerializationDeterminstic() &&
        this->map_sint64_sint64().size() > 1) {
      ::google::protobuf::scoped_array<SortItem> items(
          new SortItem[this->map_sint64_sint64().size()]);
      typedef ::google::protobuf::Map< ::google::protobuf::int64, ::google::protobuf::int64 >::size_type size_type;
      size_type n = 0;
      for (::google::protobuf::Map< ::google::protobuf::int64, ::google::protobuf::int64 >::const_iterator
          it = this->map_sint64_sint64().begin();
          it != this->map_sint64_sint64().end(); ++it, ++n) {
        items[n] = SortItem(&*it);
      }
      ::std::sort(&items[0], &items[n], Less());
      ::google::protobuf::scoped_ptr<TestMap_MapSint64Sint64Entry> entry;
      for (size_type i = 0; i < n; i++) {
        entry.reset(map_sint64_sint64_.NewEntryWrapper(
            items[i].second->first, items[i].second->second));
        ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
            6, *entry, output);
        if (entry->GetArena() != NULL) {
          entry.release();
        }
      }
    } else {
      ::google::protobuf::scoped_ptr<TestMap_MapSint64Sint64Entry> entry;
      for (::google::protobuf::Map< ::google::protobuf::int64, ::google::protobuf::int64 >::const_iterator
          it = this->map_sint64_sint64().begin();
          it != this->map_sint64_sint64().end(); ++it) {
        entry.reset(map_sint64_sint64_.NewEntryWrapper(
            it->first, it->second));
        ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
            6, *entry, output);
        if (entry->GetArena() != NULL) {
          entry.release();
        }
      }
    }
  }

  // map<fixed32, fixed32> map_fixed32_fixed32 = 7;
  if (!this->map_fixed32_fixed32().empty()) {
    typedef ::google::protobuf::Map< ::google::protobuf::uint32, ::google::protobuf::uint32 >::const_pointer
        ConstPtr;
    typedef ::google::protobuf::internal::SortItem< ::google::protobuf::uint32, ConstPtr > SortItem;
    typedef ::google::protobuf::internal::CompareByFirstField<SortItem> Less;

    if (output->IsSerializationDeterminstic() &&
        this->map_fixed32_fixed32().size() > 1) {
      ::google::protobuf::scoped_array<SortItem> items(
          new SortItem[this->map_fixed32_fixed32().size()]);
      typedef ::google::protobuf::Map< ::google::protobuf::uint32, ::google::protobuf::uint32 >::size_type size_type;
      size_type n = 0;
      for (::google::protobuf::Map< ::google::protobuf::uint32, ::google::protobuf::uint32 >::const_iterator
          it = this->map_fixed32_fixed32().begin();
          it != this->map_fixed32_fixed32().end(); ++it, ++n) {
        items[n] = SortItem(&*it);
      }
      ::std::sort(&items[0], &items[n], Less());
      ::google::protobuf::scoped_ptr<TestMap_MapFixed32Fixed32Entry> entry;
      for (size_type i = 0; i < n; i++) {
        entry.reset(map_fixed32_fixed32_.NewEntryWrapper(
            items[i].second->first, items[i].second->second));
        ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
            7, *entry, output);
        if (entry->GetArena() != NULL) {
          entry.release();
        }
      }
    } else {
      ::google::protobuf::scoped_ptr<TestMap_MapFixed32Fixed32Entry> entry;
      for (::google::protobuf::Map< ::google::protobuf::uint32, ::google::protobuf::uint32 >::const_iterator
          it = this->map_fixed32_fixed32().begin();
          it != this->map_fixed32_fixed32().end(); ++it) {
        entry.reset(map_fixed32_fixed32_.NewEntryWrapper(
            it->first, it->second));
        ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
            7, *entry, output);
        if (entry->GetArena() != NULL) {
          entry.release();
        }
      }
    }
  }

  // map<fixed64, fixed64> map_fixed64_fixed64 = 8;
  if (!this->map_fixed64_fixed64().empty()) {
    typedef ::google::protobuf::Map< ::google::protobuf::uint64, ::google::protobuf::uint64 >::const_pointer
        ConstPtr;
    typedef ::google::protobuf::internal::SortItem< ::google::protobuf::uint64, ConstPtr > SortItem;
    typedef ::google::protobuf::internal::CompareByFirstField<SortItem> Less;

    if (output->IsSerializationDeterminstic() &&
        this->map_fixed64_fixed64().size() > 1) {
      ::google::protobuf::scoped_array<SortItem> items(
          new SortItem[this->map_fixed64_fixed64().size()]);
      typedef ::google::protobuf::Map< ::google::protobuf::uint64, ::google::protobuf::uint64 >::size_type size_type;
      size_type n = 0;
      for (::google::protobuf::Map< ::google::protobuf::uint64, ::google::protobuf::uint64 >::const_iterator
          it = this->map_fixed64_fixed64().begin();
          it != this->map_fixed64_fixed64().end(); ++it, ++n) {
        items[n] = SortItem(&*it);
      }
      ::std::sort(&items[0], &items[n], Less());
      ::google::protobuf::scoped_ptr<TestMap_MapFixed64Fixed64Entry> entry;
      for (size_type i = 0; i < n; i++) {
        entry.reset(map_fixed64_fixed64_.NewEntryWrapper(
            items[i].second->first, items[i].second->second));
        ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
            8, *entry, output);
        if (entry->GetArena() != NULL) {
          entry.release();
        }
      }
    } else {
      ::google::protobuf::scoped_ptr<TestMap_MapFixed64Fixed64Entry> entry;
      for (::google::protobuf::Map< ::google::protobuf::uint64, ::google::protobuf::uint64 >::const_iterator
          it = this->map_fixed64_fixed64().begin();
          it != this->map_fixed64_fixed64().end(); ++it) {
        entry.reset(map_fixed64_fixed64_.NewEntryWrapper(
            it->first, it->second));
        ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
            8, *entry, output);
        if (entry->GetArena() != NULL) {
          entry.release();
        }
      }
    }
  }

  // map<sfixed32, sfixed32> map_sfixed32_sfixed32 = 9;
  if (!this->map_sfixed32_sfixed32().empty()) {
    typedef ::google::protobuf::Map< ::google::protobuf::int32, ::google::protobuf::int32 >::const_pointer
        ConstPtr;
    typedef ::google::protobuf::internal::SortItem< ::google::protobuf::int32, ConstPtr > SortItem;
    typedef ::google::protobuf::internal::CompareByFirstField<SortItem> Less;

    if (output->IsSerializationDeterminstic() &&
        this->map_sfixed32_sfixed32().size() > 1) {
      ::google::protobuf::scoped_array<SortItem> items(
          new SortItem[this->map_sfixed32_sfixed32().size()]);
      typedef ::google::protobuf::Map< ::google::protobuf::int32, ::google::protobuf::int32 >::size_type size_type;
      size_type n = 0;
      for (::google::protobuf::Map< ::google::protobuf::int32, ::google::protobuf::int32 >::const_iterator
          it = this->map_sfixed32_sfixed32().begin();
          it != this->map_sfixed32_sfixed32().end(); ++it, ++n) {
        items[n] = SortItem(&*it);
      }
      ::std::sort(&items[0], &items[n], Less());
      ::google::protobuf::scoped_ptr<TestMap_MapSfixed32Sfixed32Entry> entry;
      for (size_type i = 0; i < n; i++) {
        entry.reset(map_sfixed32_sfixed32_.NewEntryWrapper(
            items[i].second->first, items[i].second->second));
        ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
            9, *entry, output);
        if (entry->GetArena() != NULL) {
          entry.release();
        }
      }
    } else {
      ::google::protobuf::scoped_ptr<TestMap_MapSfixed32Sfixed32Entry> entry;
      for (::google::protobuf::Map< ::google::protobuf::int32, ::google::protobuf::int32 >::const_iterator
          it = this->map_sfixed32_sfixed32().begin();
          it != this->map_sfixed32_sfixed32().end(); ++it) {
        entry.reset(map_sfixed32_sfixed32_.NewEntryWrapper(
            it->first, it->second));
        ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
            9, *entry, output);
        if (entry->GetArena() != NULL) {
          entry.release();
        }
      }
    }
  }

  // map<sfixed64, sfixed64> map_sfixed64_sfixed64 = 10;
  if (!this->map_sfixed64_sfixed64().empty()) {
    typedef ::google::protobuf::Map< ::google::protobuf::int64, ::google::protobuf::int64 >::const_pointer
        ConstPtr;
    typedef ::google::protobuf::internal::SortItem< ::google::protobuf::int64, ConstPtr > SortItem;
    typedef ::google::protobuf::internal::CompareByFirstField<SortItem> Less;

    if (output->IsSerializationDeterminstic() &&
        this->map_sfixed64_sfixed64().size() > 1) {
      ::google::protobuf::scoped_array<SortItem> items(
          new SortItem[this->map_sfixed64_sfixed64().size()]);
      typedef ::google::protobuf::Map< ::google::protobuf::int64, ::google::protobuf::int64 >::size_type size_type;
      size_type n = 0;
      for (::google::protobuf::Map< ::google::protobuf::int64, ::google::protobuf::int64 >::const_iterator
          it = this->map_sfixed64_sfixed64().begin();
          it != this->map_sfixed64_sfixed64().end(); ++it, ++n) {
        items[n] = SortItem(&*it);
      }
      ::std::sort(&items[0], &items[n], Less());
      ::google::protobuf::scoped_ptr<TestMap_MapSfixed64Sfixed64Entry> entry;
      for (size_type i = 0; i < n; i++) {
        entry.reset(map_sfixed64_sfixed64_.NewEntryWrapper(
            items[i].second->first, items[i].second->second));
        ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
            10, *entry, output);
        if (entry->GetArena() != NULL) {
          entry.release();
        }
      }
    } else {
      ::google::protobuf::scoped_ptr<TestMap_MapSfixed64Sfixed64Entry> entry;
      for (::google::protobuf::Map< ::google::protobuf::int64, ::google::protobuf::int64 >::const_iterator
          it = this->map_sfixed64_sfixed64().begin();
          it != this->map_sfixed64_sfixed64().end(); ++it) {
        entry.reset(map_sfixed64_sfixed64_.NewEntryWrapper(
            it->first, it->second));
        ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
            10, *entry, output);
        if (entry->GetArena() != NULL) {
          entry.release();
        }
      }
    }
  }

  // map<int32, float> map_int32_float = 11;
  if (!this->map_int32_float().empty()) {
    typedef ::google::protobuf::Map< ::google::protobuf::int32, float >::const_pointer
        ConstPtr;
    typedef ::google::protobuf::internal::SortItem< ::google::protobuf::int32, ConstPtr > SortItem;
    typedef ::google::protobuf::internal::CompareByFirstField<SortItem> Less;

    if (output->IsSerializationDeterminstic() &&
        this->map_int32_float().size() > 1) {
      ::google::protobuf::scoped_array<SortItem> items(
          new SortItem[this->map_int32_float().size()]);
      typedef ::google::protobuf::Map< ::google::protobuf::int32, float >::size_type size_type;
      size_type n = 0;
      for (::google::protobuf::Map< ::google::protobuf::int32, float >::const_iterator
          it = this->map_int32_float().begin();
          it != this->map_int32_float().end(); ++it, ++n) {
        items[n] = SortItem(&*it);
      }
      ::std::sort(&items[0], &items[n], Less());
      ::google::protobuf::scoped_ptr<TestMap_MapInt32FloatEntry> entry;
      for (size_type i = 0; i < n; i++) {
        entry.reset(map_int32_float_.NewEntryWrapper(
            items[i].second->first, items[i].second->second));
        ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
            11, *entry, output);
        if (entry->GetArena() != NULL) {
          entry.release();
        }
      }
    } else {
      ::google::protobuf::scoped_ptr<TestMap_MapInt32FloatEntry> entry;
      for (::google::protobuf::Map< ::google::protobuf::int32, float >::const_iterator
          it = this->map_int32_float().begin();
          it != this->map_int32_float().end(); ++it) {
        entry.reset(map_int32_float_.NewEntryWrapper(
            it->first, it->second));
        ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
            11, *entry, output);
        if (entry->GetArena() != NULL) {
          entry.release();
        }
      }
    }
  }

  // map<int32, double> map_int32_double = 12;
  if (!this->map_int32_double().empty()) {
    typedef ::google::protobuf::Map< ::google::protobuf::int32, double >::const_pointer
        ConstPtr;
    typedef ::google::protobuf::internal::SortItem< ::google::protobuf::int32, ConstPtr > SortItem;
    typedef ::google::protobuf::internal::CompareByFirstField<SortItem> Less;

    if (output->IsSerializationDeterminstic() &&
        this->map_int32_double().size() > 1) {
      ::google::protobuf::scoped_array<SortItem> items(
          new SortItem[this->map_int32_double().size()]);
      typedef ::google::protobuf::Map< ::google::protobuf::int32, double >::size_type size_type;
      size_type n = 0;
      for (::google::protobuf::Map< ::google::protobuf::int32, double >::const_iterator
          it = this->map_int32_double().begin();
          it != this->map_int32_double().end(); ++it, ++n) {
        items[n] = SortItem(&*it);
      }
      ::std::sort(&items[0], &items[n], Less());
      ::google::protobuf::scoped_ptr<TestMap_MapInt32DoubleEntry> entry;
      for (size_type i = 0; i < n; i++) {
        entry.reset(map_int32_double_.NewEntryWrapper(
            items[i].second->first, items[i].second->second));
        ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
            12, *entry, output);
        if (entry->GetArena() != NULL) {
          entry.release();
        }
      }
    } else {
      ::google::protobuf::scoped_ptr<TestMap_MapInt32DoubleEntry> entry;
      for (::google::protobuf::Map< ::google::protobuf::int32, double >::const_iterator
          it = this->map_int32_double().begin();
          it != this->map_int32_double().end(); ++it) {
        entry.reset(map_int32_double_.NewEntryWrapper(
            it->first, it->second));
        ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
            12, *entry, output);
        if (entry->GetArena() != NULL) {
          entry.release();
        }
      }
    }
  }

  // map<bool, bool> map_bool_bool = 13;
  if (!this->map_bool_bool().empty()) {
    typedef ::google::protobuf::Map< bool, bool >::const_pointer
        ConstPtr;
    typedef ::google::protobuf::internal::SortItem< bool, ConstPtr > SortItem;
    typedef ::google::protobuf::internal::CompareByFirstField<SortItem> Less;

    if (output->IsSerializationDeterminstic() &&
        this->map_bool_bool().size() > 1) {
      ::google::protobuf::scoped_array<SortItem> items(
          new SortItem[this->map_bool_bool().size()]);
      typedef ::google::protobuf::Map< bool, bool >::size_type size_type;
      size_type n = 0;
      for (::google::protobuf::Map< bool, bool >::const_iterator
          it = this->map_bool_bool().begin();
          it != this->map_bool_bool().end(); ++it, ++n) {
        items[n] = SortItem(&*it);
      }
      ::std::sort(&items[0], &items[n], Less());
      ::google::protobuf::scoped_ptr<TestMap_MapBoolBoolEntry> entry;
      for (size_type i = 0; i < n; i++) {
        entry.reset(map_bool_bool_.NewEntryWrapper(
            items[i].second->first, items[i].second->second));
        ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
            13, *entry, output);
        if (entry->GetArena() != NULL) {
          entry.release();
        }
      }
    } else {
      ::google::protobuf::scoped_ptr<TestMap_MapBoolBoolEntry> entry;
      for (::google::protobuf::Map< bool, bool >::const_iterator
          it = this->map_bool_bool().begin();
          it != this->map_bool_bool().end(); ++it) {
        entry.reset(map_bool_bool_.NewEntryWrapper(
            it->first, it->second));
        ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
            13, *entry, output);
        if (entry->GetArena() != NULL) {
          entry.release();
        }
      }
    }
  }

  // map<string, string> map_string_string = 14;
  if (!this->map_string_string().empty()) {
    typedef ::google::protobuf::Map< ::std::string, ::std::string >::const_pointer
        ConstPtr;
    typedef ConstPtr SortItem;
    typedef ::google::protobuf::internal::CompareByDerefFirst<SortItem> Less;
    struct Utf8Check {
      static void Check(ConstPtr p) {
        ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
          p->first.data(), p->first.length(),
          ::google::protobuf::internal::WireFormatLite::SERIALIZE,
          "protobuf_unittest.TestMap.MapStringStringEntry.key");
        ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
          p->second.data(), p->second.length(),
          ::google::protobuf::internal::WireFormatLite::SERIALIZE,
          "protobuf_unittest.TestMap.MapStringStringEntry.value");
      }
    };

    if (output->IsSerializationDeterminstic() &&
        this->map_string_string().size() > 1) {
      ::google::protobuf::scoped_array<SortItem> items(
          new SortItem[this->map_string_string().size()]);
      typedef ::google::protobuf::Map< ::std::string, ::std::string >::size_type size_type;
      size_type n = 0;
      for (::google::protobuf::Map< ::std::string, ::std::string >::const_iterator
          it = this->map_string_string().begin();
          it != this->map_string_string().end(); ++it, ++n) {
        items[n] = SortItem(&*it);
      }
      ::std::sort(&items[0], &items[n], Less());
      ::google::protobuf::scoped_ptr<TestMap_MapStringStringEntry> entry;
      for (size_type i = 0; i < n; i++) {
        entry.reset(map_string_string_.NewEntryWrapper(
            items[i]->first, items[i]->second));
        ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
            14, *entry, output);
        if (entry->GetArena() != NULL) {
          entry.release();
        }
        Utf8Check::Check(items[i]);
      }
    } else {
      ::google::protobuf::scoped_ptr<TestMap_MapStringStringEntry> entry;
      for (::google::protobuf::Map< ::std::string, ::std::string >::const_iterator
          it = this->map_string_string().begin();
          it != this->map_string_string().end(); ++it) {
        entry.reset(map_string_string_.NewEntryWrapper(
            it->first, it->second));
        ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
            14, *entry, output);
        if (entry->GetArena() != NULL) {
          entry.release();
        }
        Utf8Check::Check(&*it);
      }
    }
  }

  // map<int32, bytes> map_int32_bytes = 15;
  if (!this->map_int32_bytes().empty()) {
    typedef ::google::protobuf::Map< ::google::protobuf::int32, ::std::string >::const_pointer
        ConstPtr;
    typedef ::google::protobuf::internal::SortItem< ::google::protobuf::int32, ConstPtr > SortItem;
    typedef ::google::protobuf::internal::CompareByFirstField<SortItem> Less;

    if (output->IsSerializationDeterminstic() &&
        this->map_int32_bytes().size() > 1) {
      ::google::protobuf::scoped_array<SortItem> items(
          new SortItem[this->map_int32_bytes().size()]);
      typedef ::google::protobuf::Map< ::google::protobuf::int32, ::std::string >::size_type size_type;
      size_type n = 0;
      for (::google::protobuf::Map< ::google::protobuf::int32, ::std::string >::const_iterator
          it = this->map_int32_bytes().begin();
          it != this->map_int32_bytes().end(); ++it, ++n) {
        items[n] = SortItem(&*it);
      }
      ::std::sort(&items[0], &items[n], Less());
      ::google::protobuf::scoped_ptr<TestMap_MapInt32BytesEntry> entry;
      for (size_type i = 0; i < n; i++) {
        entry.reset(map_int32_bytes_.NewEntryWrapper(
            items[i].second->first, items[i].second->second));
        ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
            15, *entry, output);
        if (entry->GetArena() != NULL) {
          entry.release();
        }
      }
    } else {
      ::google::protobuf::scoped_ptr<TestMap_MapInt32BytesEntry> entry;
      for (::google::protobuf::Map< ::google::protobuf::int32, ::std::string >::const_iterator
          it = this->map_int32_bytes().begin();
          it != this->map_int32_bytes().end(); ++it) {
        entry.reset(map_int32_bytes_.NewEntryWrapper(
            it->first, it->second));
        ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
            15, *entry, output);
        if (entry->GetArena() != NULL) {
          entry.release();
        }
      }
    }
  }

  // map<int32, .protobuf_unittest.MapEnum> map_int32_enum = 16;
  if (!this->map_int32_enum().empty()) {
    typedef ::google::protobuf::Map< ::google::protobuf::int32, ::protobuf_unittest::MapEnum >::const_pointer
        ConstPtr;
    typedef ::google::protobuf::internal::SortItem< ::google::protobuf::int32, ConstPtr > SortItem;
    typedef ::google::protobuf::internal::CompareByFirstField<SortItem> Less;

    if (output->IsSerializationDeterminstic() &&
        this->map_int32_enum().size() > 1) {
      ::google::protobuf::scoped_array<SortItem> items(
          new SortItem[this->map_int32_enum().size()]);
      typedef ::google::protobuf::Map< ::google::protobuf::int32, ::protobuf_unittest::MapEnum >::size_type size_type;
      size_type n = 0;
      for (::google::protobuf::Map< ::google::protobuf::int32, ::protobuf_unittest::MapEnum >::const_iterator
          it = this->map_int32_enum().begin();
          it != this->map_int32_enum().end(); ++it, ++n) {
        items[n] = SortItem(&*it);
      }
      ::std::sort(&items[0], &items[n], Less());
      ::google::protobuf::scoped_ptr<TestMap_MapInt32EnumEntry> entry;
      for (size_type i = 0; i < n; i++) {
        entry.reset(map_int32_enum_.NewEnumEntryWrapper(
            items[i].second->first, items[i].second->second));
        ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
            16, *entry, output);
        if (entry->GetArena() != NULL) {
          entry.release();
        }
      }
    } else {
      ::google::protobuf::scoped_ptr<TestMap_MapInt32EnumEntry> entry;
      for (::google::protobuf::Map< ::google::protobuf::int32, ::protobuf_unittest::MapEnum >::const_iterator
          it = this->map_int32_enum().begin();
          it != this->map_int32_enum().end(); ++it) {
        entry.reset(map_int32_enum_.NewEnumEntryWrapper(
            it->first, it->second));
        ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
            16, *entry, output);
        if (entry->GetArena() != NULL) {
          entry.release();
        }
      }
    }
  }

  // map<int32, .protobuf_unittest.ForeignMessage> map_int32_foreign_message = 17;
  if (!this->map_int32_foreign_message().empty()) {
    typedef ::google::protobuf::Map< ::google::protobuf::int32, ::protobuf_unittest::ForeignMessage >::const_pointer
        ConstPtr;
    typedef ::google::protobuf::internal::SortItem< ::google::protobuf::int32, ConstPtr > SortItem;
    typedef ::google::protobuf::internal::CompareByFirstField<SortItem> Less;

    if (output->IsSerializationDeterminstic() &&
        this->map_int32_foreign_message().size() > 1) {
      ::google::protobuf::scoped_array<SortItem> items(
          new SortItem[this->map_int32_foreign_message().size()]);
      typedef ::google::protobuf::Map< ::google::protobuf::int32, ::protobuf_unittest::ForeignMessage >::size_type size_type;
      size_type n = 0;
      for (::google::protobuf::Map< ::google::protobuf::int32, ::protobuf_unittest::ForeignMessage >::const_iterator
          it = this->map_int32_foreign_message().begin();
          it != this->map_int32_foreign_message().end(); ++it, ++n) {
        items[n] = SortItem(&*it);
      }
      ::std::sort(&items[0], &items[n], Less());
      ::google::protobuf::scoped_ptr<TestMap_MapInt32ForeignMessageEntry> entry;
      for (size_type i = 0; i < n; i++) {
        entry.reset(map_int32_foreign_message_.NewEntryWrapper(
            items[i].second->first, items[i].second->second));
        ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
            17, *entry, output);
        if (entry->GetArena() != NULL) {
          entry.release();
        }
      }
    } else {
      ::google::protobuf::scoped_ptr<TestMap_MapInt32ForeignMessageEntry> entry;
      for (::google::protobuf::Map< ::google::protobuf::int32, ::protobuf_unittest::ForeignMessage >::const_iterator
          it = this->map_int32_foreign_message().begin();
          it != this->map_int32_foreign_message().end(); ++it) {
        entry.reset(map_int32_foreign_message_.NewEntryWrapper(
            it->first, it->second));
        ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
            17, *entry, output);
        if (entry->GetArena() != NULL) {
          entry.release();
        }
      }
    }
  }

  // map<string, .protobuf_unittest.ForeignMessage> map_string_foreign_message = 18;
  if (!this->map_string_foreign_message().empty()) {
    typedef ::google::protobuf::Map< ::std::string, ::protobuf_unittest::ForeignMessage >::const_pointer
        ConstPtr;
    typedef ConstPtr SortItem;
    typedef ::google::protobuf::internal::CompareByDerefFirst<SortItem> Less;
    struct Utf8Check {
      static void Check(ConstPtr p) {
        ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
          p->first.data(), p->first.length(),
          ::google::protobuf::internal::WireFormatLite::SERIALIZE,
          "protobuf_unittest.TestMap.MapStringForeignMessageEntry.key");
      }
    };

    if (output->IsSerializationDeterminstic() &&
        this->map_string_foreign_message().size() > 1) {
      ::google::protobuf::scoped_array<SortItem> items(
          new SortItem[this->map_string_foreign_message().size()]);
      typedef ::google::protobuf::Map< ::std::string, ::protobuf_unittest::ForeignMessage >::size_type size_type;
      size_type n = 0;
      for (::google::protobuf::Map< ::std::string, ::protobuf_unittest::ForeignMessage >::const_iterator
          it = this->map_string_foreign_message().begin();
          it != this->map_string_foreign_message().end(); ++it, ++n) {
        items[n] = SortItem(&*it);
      }
      ::std::sort(&items[0], &items[n], Less());
      ::google::protobuf::scoped_ptr<TestMap_MapStringForeignMessageEntry> entry;
      for (size_type i = 0; i < n; i++) {
        entry.reset(map_string_foreign_message_.NewEntryWrapper(
            items[i]->first, items[i]->second));
        ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
            18, *entry, output);
        if (entry->GetArena() != NULL) {
          entry.release();
        }
        Utf8Check::Check(items[i]);
      }
    } else {
      ::google::protobuf::scoped_ptr<TestMap_MapStringForeignMessageEntry> entry;
      for (::google::protobuf::Map< ::std::string, ::protobuf_unittest::ForeignMessage >::const_iterator
          it = this->map_string_foreign_message().begin();
          it != this->map_string_foreign_message().end(); ++it) {
        entry.reset(map_string_foreign_message_.NewEntryWrapper(
            it->first, it->second));
        ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
            18, *entry, output);
        if (entry->GetArena() != NULL) {
          entry.release();
        }
        Utf8Check::Check(&*it);
      }
    }
  }

  // @@protoc_insertion_point(serialize_end:protobuf_unittest.TestMap)
}

::google::protobuf::uint8* TestMap::InternalSerializeWithCachedSizesToArray(
    bool deterministic, ::google::protobuf::uint8* target) const {
  (void)deterministic; // Unused
  // @@protoc_insertion_point(serialize_to_array_start:protobuf_unittest.TestMap)
  // map<int32, int32> map_int32_int32 = 1;
  if (!this->map_int32_int32().empty()) {
    typedef ::google::protobuf::Map< ::google::protobuf::int32, ::google::protobuf::int32 >::const_pointer
        ConstPtr;
    typedef ::google::protobuf::internal::SortItem< ::google::protobuf::int32, ConstPtr > SortItem;
    typedef ::google::protobuf::internal::CompareByFirstField<SortItem> Less;

    if (deterministic &&
        this->map_int32_int32().size() > 1) {
      ::google::protobuf::scoped_array<SortItem> items(
          new SortItem[this->map_int32_int32().size()]);
      typedef ::google::protobuf::Map< ::google::protobuf::int32, ::google::protobuf::int32 >::size_type size_type;
      size_type n = 0;
      for (::google::protobuf::Map< ::google::protobuf::int32, ::google::protobuf::int32 >::const_iterator
          it = this->map_int32_int32().begin();
          it != this->map_int32_int32().end(); ++it, ++n) {
        items[n] = SortItem(&*it);
      }
      ::std::sort(&items[0], &items[n], Less());
      ::google::protobuf::scoped_ptr<TestMap_MapInt32Int32Entry> entry;
      for (size_type i = 0; i < n; i++) {
        entry.reset(map_int32_int32_.NewEntryWrapper(
            items[i].second->first, items[i].second->second));
        target = ::google::protobuf::internal::WireFormatLite::
                   InternalWriteMessageNoVirtualToArray(
                       1, *entry, deterministic, target);
;
        if (entry->GetArena() != NULL) {
          entry.release();
        }
      }
    } else {
      ::google::protobuf::scoped_ptr<TestMap_MapInt32Int32Entry> entry;
      for (::google::protobuf::Map< ::google::protobuf::int32, ::google::protobuf::int32 >::const_iterator
          it = this->map_int32_int32().begin();
          it != this->map_int32_int32().end(); ++it) {
        entry.reset(map_int32_int32_.NewEntryWrapper(
            it->first, it->second));
        target = ::google::protobuf::internal::WireFormatLite::
                   InternalWriteMessageNoVirtualToArray(
                       1, *entry, deterministic, target);
;
        if (entry->GetArena() != NULL) {
          entry.release();
        }
      }
    }
  }

  // map<int64, int64> map_int64_int64 = 2;
  if (!this->map_int64_int64().empty()) {
    typedef ::google::protobuf::Map< ::google::protobuf::int64, ::google::protobuf::int64 >::const_pointer
        ConstPtr;
    typedef ::google::protobuf::internal::SortItem< ::google::protobuf::int64, ConstPtr > SortItem;
    typedef ::google::protobuf::internal::CompareByFirstField<SortItem> Less;

    if (deterministic &&
        this->map_int64_int64().size() > 1) {
      ::google::protobuf::scoped_array<SortItem> items(
          new SortItem[this->map_int64_int64().size()]);
      typedef ::google::protobuf::Map< ::google::protobuf::int64, ::google::protobuf::int64 >::size_type size_type;
      size_type n = 0;
      for (::google::protobuf::Map< ::google::protobuf::int64, ::google::protobuf::int64 >::const_iterator
          it = this->map_int64_int64().begin();
          it != this->map_int64_int64().end(); ++it, ++n) {
        items[n] = SortItem(&*it);
      }
      ::std::sort(&items[0], &items[n], Less());
      ::google::protobuf::scoped_ptr<TestMap_MapInt64Int64Entry> entry;
      for (size_type i = 0; i < n; i++) {
        entry.reset(map_int64_int64_.NewEntryWrapper(
            items[i].second->first, items[i].second->second));
        target = ::google::protobuf::internal::WireFormatLite::
                   InternalWriteMessageNoVirtualToArray(
                       2, *entry, deterministic, target);
;
        if (entry->GetArena() != NULL) {
          entry.release();
        }
      }
    } else {
      ::google::protobuf::scoped_ptr<TestMap_MapInt64Int64Entry> entry;
      for (::google::protobuf::Map< ::google::protobuf::int64, ::google::protobuf::int64 >::const_iterator
          it = this->map_int64_int64().begin();
          it != this->map_int64_int64().end(); ++it) {
        entry.reset(map_int64_int64_.NewEntryWrapper(
            it->first, it->second));
        target = ::google::protobuf::internal::WireFormatLite::
                   InternalWriteMessageNoVirtualToArray(
                       2, *entry, deterministic, target);
;
        if (entry->GetArena() != NULL) {
          entry.release();
        }
      }
    }
  }

  // map<uint32, uint32> map_uint32_uint32 = 3;
  if (!this->map_uint32_uint32().empty()) {
    typedef ::google::protobuf::Map< ::google::protobuf::uint32, ::google::protobuf::uint32 >::const_pointer
        ConstPtr;
    typedef ::google::protobuf::internal::SortItem< ::google::protobuf::uint32, ConstPtr > SortItem;
    typedef ::google::protobuf::internal::CompareByFirstField<SortItem> Less;

    if (deterministic &&
        this->map_uint32_uint32().size() > 1) {
      ::google::protobuf::scoped_array<SortItem> items(
          new SortItem[this->map_uint32_uint32().size()]);
      typedef ::google::protobuf::Map< ::google::protobuf::uint32, ::google::protobuf::uint32 >::size_type size_type;
      size_type n = 0;
      for (::google::protobuf::Map< ::google::protobuf::uint32, ::google::protobuf::uint32 >::const_iterator
          it = this->map_uint32_uint32().begin();
          it != this->map_uint32_uint32().end(); ++it, ++n) {
        items[n] = SortItem(&*it);
      }
      ::std::sort(&items[0], &items[n], Less());
      ::google::protobuf::scoped_ptr<TestMap_MapUint32Uint32Entry> entry;
      for (size_type i = 0; i < n; i++) {
        entry.reset(map_uint32_uint32_.NewEntryWrapper(
            items[i].second->first, items[i].second->second));
        target = ::google::protobuf::internal::WireFormatLite::
                   InternalWriteMessageNoVirtualToArray(
                       3, *entry, deterministic, target);
;
        if (entry->GetArena() != NULL) {
          entry.release();
        }
      }
    } else {
      ::google::protobuf::scoped_ptr<TestMap_MapUint32Uint32Entry> entry;
      for (::google::protobuf::Map< ::google::protobuf::uint32, ::google::protobuf::uint32 >::const_iterator
          it = this->map_uint32_uint32().begin();
          it != this->map_uint32_uint32().end(); ++it) {
        entry.reset(map_uint32_uint32_.NewEntryWrapper(
            it->first, it->second));
        target = ::google::protobuf::internal::WireFormatLite::
                   InternalWriteMessageNoVirtualToArray(
                       3, *entry, deterministic, target);
;
        if (entry->GetArena() != NULL) {
          entry.release();
        }
      }
    }
  }

  // map<uint64, uint64> map_uint64_uint64 = 4;
  if (!this->map_uint64_uint64().empty()) {
    typedef ::google::protobuf::Map< ::google::protobuf::uint64, ::google::protobuf::uint64 >::const_pointer
        ConstPtr;
    typedef ::google::protobuf::internal::SortItem< ::google::protobuf::uint64, ConstPtr > SortItem;
    typedef ::google::protobuf::internal::CompareByFirstField<SortItem> Less;

    if (deterministic &&
        this->map_uint64_uint64().size() > 1) {
      ::google::protobuf::scoped_array<SortItem> items(
          new SortItem[this->map_uint64_uint64().size()]);
      typedef ::google::protobuf::Map< ::google::protobuf::uint64, ::google::protobuf::uint64 >::size_type size_type;
      size_type n = 0;
      for (::google::protobuf::Map< ::google::protobuf::uint64, ::google::protobuf::uint64 >::const_iterator
          it = this->map_uint64_uint64().begin();
          it != this->map_uint64_uint64().end(); ++it, ++n) {
        items[n] = SortItem(&*it);
      }
      ::std::sort(&items[0], &items[n], Less());
      ::google::protobuf::scoped_ptr<TestMap_MapUint64Uint64Entry> entry;
      for (size_type i = 0; i < n; i++) {
        entry.reset(map_uint64_uint64_.NewEntryWrapper(
            items[i].second->first, items[i].second->second));
        target = ::google::protobuf::internal::WireFormatLite::
                   InternalWriteMessageNoVirtualToArray(
                       4, *entry, deterministic, target);
;
        if (entry->GetArena() != NULL) {
          entry.release();
        }
      }
    } else {
      ::google::protobuf::scoped_ptr<TestMap_MapUint64Uint64Entry> entry;
      for (::google::protobuf::Map< ::google::protobuf::uint64, ::google::protobuf::uint64 >::const_iterator
          it = this->map_uint64_uint64().begin();
          it != this->map_uint64_uint64().end(); ++it) {
        entry.reset(map_uint64_uint64_.NewEntryWrapper(
            it->first, it->second));
        target = ::google::protobuf::internal::WireFormatLite::
                   InternalWriteMessageNoVirtualToArray(
                       4, *entry, deterministic, target);
;
        if (entry->GetArena() != NULL) {
          entry.release();
        }
      }
    }
  }

  // map<sint32, sint32> map_sint32_sint32 = 5;
  if (!this->map_sint32_sint32().empty()) {
    typedef ::google::protobuf::Map< ::google::protobuf::int32, ::google::protobuf::int32 >::const_pointer
        ConstPtr;
    typedef ::google::protobuf::internal::SortItem< ::google::protobuf::int32, ConstPtr > SortItem;
    typedef ::google::protobuf::internal::CompareByFirstField<SortItem> Less;

    if (deterministic &&
        this->map_sint32_sint32().size() > 1) {
      ::google::protobuf::scoped_array<SortItem> items(
          new SortItem[this->map_sint32_sint32().size()]);
      typedef ::google::protobuf::Map< ::google::protobuf::int32, ::google::protobuf::int32 >::size_type size_type;
      size_type n = 0;
      for (::google::protobuf::Map< ::google::protobuf::int32, ::google::protobuf::int32 >::const_iterator
          it = this->map_sint32_sint32().begin();
          it != this->map_sint32_sint32().end(); ++it, ++n) {
        items[n] = SortItem(&*it);
      }
      ::std::sort(&items[0], &items[n], Less());
      ::google::protobuf::scoped_ptr<TestMap_MapSint32Sint32Entry> entry;
      for (size_type i = 0; i < n; i++) {
        entry.reset(map_sint32_sint32_.NewEntryWrapper(
            items[i].second->first, items[i].second->second));
        target = ::google::protobuf::internal::WireFormatLite::
                   InternalWriteMessageNoVirtualToArray(
                       5, *entry, deterministic, target);
;
        if (entry->GetArena() != NULL) {
          entry.release();
        }
      }
    } else {
      ::google::protobuf::scoped_ptr<TestMap_MapSint32Sint32Entry> entry;
      for (::google::protobuf::Map< ::google::protobuf::int32, ::google::protobuf::int32 >::const_iterator
          it = this->map_sint32_sint32().begin();
          it != this->map_sint32_sint32().end(); ++it) {
        entry.reset(map_sint32_sint32_.NewEntryWrapper(
            it->first, it->second));
        target = ::google::protobuf::internal::WireFormatLite::
                   InternalWriteMessageNoVirtualToArray(
                       5, *entry, deterministic, target);
;
        if (entry->GetArena() != NULL) {
          entry.release();
        }
      }
    }
  }

  // map<sint64, sint64> map_sint64_sint64 = 6;
  if (!this->map_sint64_sint64().empty()) {
    typedef ::google::protobuf::Map< ::google::protobuf::int64, ::google::protobuf::int64 >::const_pointer
        ConstPtr;
    typedef ::google::protobuf::internal::SortItem< ::google::protobuf::int64, ConstPtr > SortItem;
    typedef ::google::protobuf::internal::CompareByFirstField<SortItem> Less;

    if (deterministic &&
        this->map_sint64_sint64().size() > 1) {
      ::google::protobuf::scoped_array<SortItem> items(
          new SortItem[this->map_sint64_sint64().size()]);
      typedef ::google::protobuf::Map< ::google::protobuf::int64, ::google::protobuf::int64 >::size_type size_type;
      size_type n = 0;
      for (::google::protobuf::Map< ::google::protobuf::int64, ::google::protobuf::int64 >::const_iterator
          it = this->map_sint64_sint64().begin();
          it != this->map_sint64_sint64().end(); ++it, ++n) {
        items[n] = SortItem(&*it);
      }
      ::std::sort(&items[0], &items[n], Less());
      ::google::protobuf::scoped_ptr<TestMap_MapSint64Sint64Entry> entry;
      for (size_type i = 0; i < n; i++) {
        entry.reset(map_sint64_sint64_.NewEntryWrapper(
            items[i].second->first, items[i].second->second));
        target = ::google::protobuf::internal::WireFormatLite::
                   InternalWriteMessageNoVirtualToArray(
                       6, *entry, deterministic, target);
;
        if (entry->GetArena() != NULL) {
          entry.release();
        }
      }
    } else {
      ::google::protobuf::scoped_ptr<TestMap_MapSint64Sint64Entry> entry;
      for (::google::protobuf::Map< ::google::protobuf::int64, ::google::protobuf::int64 >::const_iterator
          it = this->map_sint64_sint64().begin();
          it != this->map_sint64_sint64().end(); ++it) {
        entry.reset(map_sint64_sint64_.NewEntryWrapper(
            it->first, it->second));
        target = ::google::protobuf::internal::WireFormatLite::
                   InternalWriteMessageNoVirtualToArray(
                       6, *entry, deterministic, target);
;
        if (entry->GetArena() != NULL) {
          entry.release();
        }
      }
    }
  }

  // map<fixed32, fixed32> map_fixed32_fixed32 = 7;
  if (!this->map_fixed32_fixed32().empty()) {
    typedef ::google::protobuf::Map< ::google::protobuf::uint32, ::google::protobuf::uint32 >::const_pointer
        ConstPtr;
    typedef ::google::protobuf::internal::SortItem< ::google::protobuf::uint32, ConstPtr > SortItem;
    typedef ::google::protobuf::internal::CompareByFirstField<SortItem> Less;

    if (deterministic &&
        this->map_fixed32_fixed32().size() > 1) {
      ::google::protobuf::scoped_array<SortItem> items(
          new SortItem[this->map_fixed32_fixed32().size()]);
      typedef ::google::protobuf::Map< ::google::protobuf::uint32, ::google::protobuf::uint32 >::size_type size_type;
      size_type n = 0;
      for (::google::protobuf::Map< ::google::protobuf::uint32, ::google::protobuf::uint32 >::const_iterator
          it = this->map_fixed32_fixed32().begin();
          it != this->map_fixed32_fixed32().end(); ++it, ++n) {
        items[n] = SortItem(&*it);
      }
      ::std::sort(&items[0], &items[n], Less());
      ::google::protobuf::scoped_ptr<TestMap_MapFixed32Fixed32Entry> entry;
      for (size_type i = 0; i < n; i++) {
        entry.reset(map_fixed32_fixed32_.NewEntryWrapper(
            items[i].second->first, items[i].second->second));
        target = ::google::protobuf::internal::WireFormatLite::
                   InternalWriteMessageNoVirtualToArray(
                       7, *entry, deterministic, target);
;
        if (entry->GetArena() != NULL) {
          entry.release();
        }
      }
    } else {
      ::google::protobuf::scoped_ptr<TestMap_MapFixed32Fixed32Entry> entry;
      for (::google::protobuf::Map< ::google::protobuf::uint32, ::google::protobuf::uint32 >::const_iterator
          it = this->map_fixed32_fixed32().begin();
          it != this->map_fixed32_fixed32().end(); ++it) {
        entry.reset(map_fixed32_fixed32_.NewEntryWrapper(
            it->first, it->second));
        target = ::google::protobuf::internal::WireFormatLite::
                   InternalWriteMessageNoVirtualToArray(
                       7, *entry, deterministic, target);
;
        if (entry->GetArena() != NULL) {
          entry.release();
        }
      }
    }
  }

  // map<fixed64, fixed64> map_fixed64_fixed64 = 8;
  if (!this->map_fixed64_fixed64().empty()) {
    typedef ::google::protobuf::Map< ::google::protobuf::uint64, ::google::protobuf::uint64 >::const_pointer
        ConstPtr;
    typedef ::google::protobuf::internal::SortItem< ::google::protobuf::uint64, ConstPtr > SortItem;
    typedef ::google::protobuf::internal::CompareByFirstField<SortItem> Less;

    if (deterministic &&
        this->map_fixed64_fixed64().size() > 1) {
      ::google::protobuf::scoped_array<SortItem> items(
          new SortItem[this->map_fixed64_fixed64().size()]);
      typedef ::google::protobuf::Map< ::google::protobuf::uint64, ::google::protobuf::uint64 >::size_type size_type;
      size_type n = 0;
      for (::google::protobuf::Map< ::google::protobuf::uint64, ::google::protobuf::uint64 >::const_iterator
          it = this->map_fixed64_fixed64().begin();
          it != this->map_fixed64_fixed64().end(); ++it, ++n) {
        items[n] = SortItem(&*it);
      }
      ::std::sort(&items[0], &items[n], Less());
      ::google::protobuf::scoped_ptr<TestMap_MapFixed64Fixed64Entry> entry;
      for (size_type i = 0; i < n; i++) {
        entry.reset(map_fixed64_fixed64_.NewEntryWrapper(
            items[i].second->first, items[i].second->second));
        target = ::google::protobuf::internal::WireFormatLite::
                   InternalWriteMessageNoVirtualToArray(
                       8, *entry, deterministic, target);
;
        if (entry->GetArena() != NULL) {
          entry.release();
        }
      }
    } else {
      ::google::protobuf::scoped_ptr<TestMap_MapFixed64Fixed64Entry> entry;
      for (::google::protobuf::Map< ::google::protobuf::uint64, ::google::protobuf::uint64 >::const_iterator
          it = this->map_fixed64_fixed64().begin();
          it != this->map_fixed64_fixed64().end(); ++it) {
        entry.reset(map_fixed64_fixed64_.NewEntryWrapper(
            it->first, it->second));
        target = ::google::protobuf::internal::WireFormatLite::
                   InternalWriteMessageNoVirtualToArray(
                       8, *entry, deterministic, target);
;
        if (entry->GetArena() != NULL) {
          entry.release();
        }
      }
    }
  }

  // map<sfixed32, sfixed32> map_sfixed32_sfixed32 = 9;
  if (!this->map_sfixed32_sfixed32().empty()) {
    typedef ::google::protobuf::Map< ::google::protobuf::int32, ::google::protobuf::int32 >::const_pointer
        ConstPtr;
    typedef ::google::protobuf::internal::SortItem< ::google::protobuf::int32, ConstPtr > SortItem;
    typedef ::google::protobuf::internal::CompareByFirstField<SortItem> Less;

    if (deterministic &&
        this->map_sfixed32_sfixed32().size() > 1) {
      ::google::protobuf::scoped_array<SortItem> items(
          new SortItem[this->map_sfixed32_sfixed32().size()]);
      typedef ::google::protobuf::Map< ::google::protobuf::int32, ::google::protobuf::int32 >::size_type size_type;
      size_type n = 0;
      for (::google::protobuf::Map< ::google::protobuf::int32, ::google::protobuf::int32 >::const_iterator
          it = this->map_sfixed32_sfixed32().begin();
          it != this->map_sfixed32_sfixed32().end(); ++it, ++n) {
        items[n] = SortItem(&*it);
      }
      ::std::sort(&items[0], &items[n], Less());
      ::google::protobuf::scoped_ptr<TestMap_MapSfixed32Sfixed32Entry> entry;
      for (size_type i = 0; i < n; i++) {
        entry.reset(map_sfixed32_sfixed32_.NewEntryWrapper(
            items[i].second->first, items[i].second->second));
        target = ::google::protobuf::internal::WireFormatLite::
                   InternalWriteMessageNoVirtualToArray(
                       9, *entry, deterministic, target);
;
        if (entry->GetArena() != NULL) {
          entry.release();
        }
      }
    } else {
      ::google::protobuf::scoped_ptr<TestMap_MapSfixed32Sfixed32Entry> entry;
      for (::google::protobuf::Map< ::google::protobuf::int32, ::google::protobuf::int32 >::const_iterator
          it = this->map_sfixed32_sfixed32().begin();
          it != this->map_sfixed32_sfixed32().end(); ++it) {
        entry.reset(map_sfixed32_sfixed32_.NewEntryWrapper(
            it->first, it->second));
        target = ::google::protobuf::internal::WireFormatLite::
                   InternalWriteMessageNoVirtualToArray(
                       9, *entry, deterministic, target);
;
        if (entry->GetArena() != NULL) {
          entry.release();
        }
      }
    }
  }

  // map<sfixed64, sfixed64> map_sfixed64_sfixed64 = 10;
  if (!this->map_sfixed64_sfixed64().empty()) {
    typedef ::google::protobuf::Map< ::google::protobuf::int64, ::google::protobuf::int64 >::const_pointer
        ConstPtr;
    typedef ::google::protobuf::internal::SortItem< ::google::protobuf::int64, ConstPtr > SortItem;
    typedef ::google::protobuf::internal::CompareByFirstField<SortItem> Less;

    if (deterministic &&
        this->map_sfixed64_sfixed64().size() > 1) {
      ::google::protobuf::scoped_array<SortItem> items(
          new SortItem[this->map_sfixed64_sfixed64().size()]);
      typedef ::google::protobuf::Map< ::google::protobuf::int64, ::google::protobuf::int64 >::size_type size_type;
      size_type n = 0;
      for (::google::protobuf::Map< ::google::protobuf::int64, ::google::protobuf::int64 >::const_iterator
          it = this->map_sfixed64_sfixed64().begin();
          it != this->map_sfixed64_sfixed64().end(); ++it, ++n) {
        items[n] = SortItem(&*it);
      }
      ::std::sort(&items[0], &items[n], Less());
      ::google::protobuf::scoped_ptr<TestMap_MapSfixed64Sfixed64Entry> entry;
      for (size_type i = 0; i < n; i++) {
        entry.reset(map_sfixed64_sfixed64_.NewEntryWrapper(
            items[i].second->first, items[i].second->second));
        target = ::google::protobuf::internal::WireFormatLite::
                   InternalWriteMessageNoVirtualToArray(
                       10, *entry, deterministic, target);
;
        if (entry->GetArena() != NULL) {
          entry.release();
        }
      }
    } else {
      ::google::protobuf::scoped_ptr<TestMap_MapSfixed64Sfixed64Entry> entry;
      for (::google::protobuf::Map< ::google::protobuf::int64, ::google::protobuf::int64 >::const_iterator
          it = this->map_sfixed64_sfixed64().begin();
          it != this->map_sfixed64_sfixed64().end(); ++it) {
        entry.reset(map_sfixed64_sfixed64_.NewEntryWrapper(
            it->first, it->second));
        target = ::google::protobuf::internal::WireFormatLite::
                   InternalWriteMessageNoVirtualToArray(
                       10, *entry, deterministic, target);
;
        if (entry->GetArena() != NULL) {
          entry.release();
        }
      }
    }
  }

  // map<int32, float> map_int32_float = 11;
  if (!this->map_int32_float().empty()) {
    typedef ::google::protobuf::Map< ::google::protobuf::int32, float >::const_pointer
        ConstPtr;
    typedef ::google::protobuf::internal::SortItem< ::google::protobuf::int32, ConstPtr > SortItem;
    typedef ::google::protobuf::internal::CompareByFirstField<SortItem> Less;

    if (deterministic &&
        this->map_int32_float().size() > 1) {
      ::google::protobuf::scoped_array<SortItem> items(
          new SortItem[this->map_int32_float().size()]);
      typedef ::google::protobuf::Map< ::google::protobuf::int32, float >::size_type size_type;
      size_type n = 0;
      for (::google::protobuf::Map< ::google::protobuf::int32, float >::const_iterator
          it = this->map_int32_float().begin();
          it != this->map_int32_float().end(); ++it, ++n) {
        items[n] = SortItem(&*it);
      }
      ::std::sort(&items[0], &items[n], Less());
      ::google::protobuf::scoped_ptr<TestMap_MapInt32FloatEntry> entry;
      for (size_type i = 0; i < n; i++) {
        entry.reset(map_int32_float_.NewEntryWrapper(
            items[i].second->first, items[i].second->second));
        target = ::google::protobuf::internal::WireFormatLite::
                   InternalWriteMessageNoVirtualToArray(
                       11, *entry, deterministic, target);
;
        if (entry->GetArena() != NULL) {
          entry.release();
        }
      }
    } else {
      ::google::protobuf::scoped_ptr<TestMap_MapInt32FloatEntry> entry;
      for (::google::protobuf::Map< ::google::protobuf::int32, float >::const_iterator
          it = this->map_int32_float().begin();
          it != this->map_int32_float().end(); ++it) {
        entry.reset(map_int32_float_.NewEntryWrapper(
            it->first, it->second));
        target = ::google::protobuf::internal::WireFormatLite::
                   InternalWriteMessageNoVirtualToArray(
                       11, *entry, deterministic, target);
;
        if (entry->GetArena() != NULL) {
          entry.release();
        }
      }
    }
  }

  // map<int32, double> map_int32_double = 12;
  if (!this->map_int32_double().empty()) {
    typedef ::google::protobuf::Map< ::google::protobuf::int32, double >::const_pointer
        ConstPtr;
    typedef ::google::protobuf::internal::SortItem< ::google::protobuf::int32, ConstPtr > SortItem;
    typedef ::google::protobuf::internal::CompareByFirstField<SortItem> Less;

    if (deterministic &&
        this->map_int32_double().size() > 1) {
      ::google::protobuf::scoped_array<SortItem> items(
          new SortItem[this->map_int32_double().size()]);
      typedef ::google::protobuf::Map< ::google::protobuf::int32, double >::size_type size_type;
      size_type n = 0;
      for (::google::protobuf::Map< ::google::protobuf::int32, double >::const_iterator
          it = this->map_int32_double().begin();
          it != this->map_int32_double().end(); ++it, ++n) {
        items[n] = SortItem(&*it);
      }
      ::std::sort(&items[0], &items[n], Less());
      ::google::protobuf::scoped_ptr<TestMap_MapInt32DoubleEntry> entry;
      for (size_type i = 0; i < n; i++) {
        entry.reset(map_int32_double_.NewEntryWrapper(
            items[i].second->first, items[i].second->second));
        target = ::google::protobuf::internal::WireFormatLite::
                   InternalWriteMessageNoVirtualToArray(
                       12, *entry, deterministic, target);
;
        if (entry->GetArena() != NULL) {
          entry.release();
        }
      }
    } else {
      ::google::protobuf::scoped_ptr<TestMap_MapInt32DoubleEntry> entry;
      for (::google::protobuf::Map< ::google::protobuf::int32, double >::const_iterator
          it = this->map_int32_double().begin();
          it != this->map_int32_double().end(); ++it) {
        entry.reset(map_int32_double_.NewEntryWrapper(
            it->first, it->second));
        target = ::google::protobuf::internal::WireFormatLite::
                   InternalWriteMessageNoVirtualToArray(
                       12, *entry, deterministic, target);
;
        if (entry->GetArena() != NULL) {
          entry.release();
        }
      }
    }
  }

  // map<bool, bool> map_bool_bool = 13;
  if (!this->map_bool_bool().empty()) {
    typedef ::google::protobuf::Map< bool, bool >::const_pointer
        ConstPtr;
    typedef ::google::protobuf::internal::SortItem< bool, ConstPtr > SortItem;
    typedef ::google::protobuf::internal::CompareByFirstField<SortItem> Less;

    if (deterministic &&
        this->map_bool_bool().size() > 1) {
      ::google::protobuf::scoped_array<SortItem> items(
          new SortItem[this->map_bool_bool().size()]);
      typedef ::google::protobuf::Map< bool, bool >::size_type size_type;
      size_type n = 0;
      for (::google::protobuf::Map< bool, bool >::const_iterator
          it = this->map_bool_bool().begin();
          it != this->map_bool_bool().end(); ++it, ++n) {
        items[n] = SortItem(&*it);
      }
      ::std::sort(&items[0], &items[n], Less());
      ::google::protobuf::scoped_ptr<TestMap_MapBoolBoolEntry> entry;
      for (size_type i = 0; i < n; i++) {
        entry.reset(map_bool_bool_.NewEntryWrapper(
            items[i].second->first, items[i].second->second));
        target = ::google::protobuf::internal::WireFormatLite::
                   InternalWriteMessageNoVirtualToArray(
                       13, *entry, deterministic, target);
;
        if (entry->GetArena() != NULL) {
          entry.release();
        }
      }
    } else {
      ::google::protobuf::scoped_ptr<TestMap_MapBoolBoolEntry> entry;
      for (::google::protobuf::Map< bool, bool >::const_iterator
          it = this->map_bool_bool().begin();
          it != this->map_bool_bool().end(); ++it) {
        entry.reset(map_bool_bool_.NewEntryWrapper(
            it->first, it->second));
        target = ::google::protobuf::internal::WireFormatLite::
                   InternalWriteMessageNoVirtualToArray(
                       13, *entry, deterministic, target);
;
        if (entry->GetArena() != NULL) {
          entry.release();
        }
      }
    }
  }

  // map<string, string> map_string_string = 14;
  if (!this->map_string_string().empty()) {
    typedef ::google::protobuf::Map< ::std::string, ::std::string >::const_pointer
        ConstPtr;
    typedef ConstPtr SortItem;
    typedef ::google::protobuf::internal::CompareByDerefFirst<SortItem> Less;
    struct Utf8Check {
      static void Check(ConstPtr p) {
        ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
          p->first.data(), p->first.length(),
          ::google::protobuf::internal::WireFormatLite::SERIALIZE,
          "protobuf_unittest.TestMap.MapStringStringEntry.key");
        ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
          p->second.data(), p->second.length(),
          ::google::protobuf::internal::WireFormatLite::SERIALIZE,
          "protobuf_unittest.TestMap.MapStringStringEntry.value");
      }
    };

    if (deterministic &&
        this->map_string_string().size() > 1) {
      ::google::protobuf::scoped_array<SortItem> items(
          new SortItem[this->map_string_string().size()]);
      typedef ::google::protobuf::Map< ::std::string, ::std::string >::size_type size_type;
      size_type n = 0;
      for (::google::protobuf::Map< ::std::string, ::std::string >::const_iterator
          it = this->map_string_string().begin();
          it != this->map_string_string().end(); ++it, ++n) {
        items[n] = SortItem(&*it);
      }
      ::std::sort(&items[0], &items[n], Less());
      ::google::protobuf::scoped_ptr<TestMap_MapStringStringEntry> entry;
      for (size_type i = 0; i < n; i++) {
        entry.reset(map_string_string_.NewEntryWrapper(
            items[i]->first, items[i]->second));
        target = ::google::protobuf::internal::WireFormatLite::
                   InternalWriteMessageNoVirtualToArray(
                       14, *entry, deterministic, target);
;
        if (entry->GetArena() != NULL) {
          entry.release();
        }
        Utf8Check::Check(items[i]);
      }
    } else {
      ::google::protobuf::scoped_ptr<TestMap_MapStringStringEntry> entry;
      for (::google::protobuf::Map< ::std::string, ::std::string >::const_iterator
          it = this->map_string_string().begin();
          it != this->map_string_string().end(); ++it) {
        entry.reset(map_string_string_.NewEntryWrapper(
            it->first, it->second));
        target = ::google::protobuf::internal::WireFormatLite::
                   InternalWriteMessageNoVirtualToArray(
                       14, *entry, deterministic, target);
;
        if (entry->GetArena() != NULL) {
          entry.release();
        }
        Utf8Check::Check(&*it);
      }
    }
  }

  // map<int32, bytes> map_int32_bytes = 15;
  if (!this->map_int32_bytes().empty()) {
    typedef ::google::protobuf::Map< ::google::protobuf::int32, ::std::string >::const_pointer
        ConstPtr;
    typedef ::google::protobuf::internal::SortItem< ::google::protobuf::int32, ConstPtr > SortItem;
    typedef ::google::protobuf::internal::CompareByFirstField<SortItem> Less;

    if (deterministic &&
        this->map_int32_bytes().size() > 1) {
      ::google::protobuf::scoped_array<SortItem> items(
          new SortItem[this->map_int32_bytes().size()]);
      typedef ::google::protobuf::Map< ::google::protobuf::int32, ::std::string >::size_type size_type;
      size_type n = 0;
      for (::google::protobuf::Map< ::google::protobuf::int32, ::std::string >::const_iterator
          it = this->map_int32_bytes().begin();
          it != this->map_int32_bytes().end(); ++it, ++n) {
        items[n] = SortItem(&*it);
      }
      ::std::sort(&items[0], &items[n], Less());
      ::google::protobuf::scoped_ptr<TestMap_MapInt32BytesEntry> entry;
      for (size_type i = 0; i < n; i++) {
        entry.reset(map_int32_bytes_.NewEntryWrapper(
            items[i].second->first, items[i].second->second));
        target = ::google::protobuf::internal::WireFormatLite::
                   InternalWriteMessageNoVirtualToArray(
                       15, *entry, deterministic, target);
;
        if (entry->GetArena() != NULL) {
          entry.release();
        }
      }
    } else {
      ::google::protobuf::scoped_ptr<TestMap_MapInt32BytesEntry> entry;
      for (::google::protobuf::Map< ::google::protobuf::int32, ::std::string >::const_iterator
          it = this->map_int32_bytes().begin();
          it != this->map_int32_bytes().end(); ++it) {
        entry.reset(map_int32_bytes_.NewEntryWrapper(
            it->first, it->second));
        target = ::google::protobuf::internal::WireFormatLite::
                   InternalWriteMessageNoVirtualToArray(
                       15, *entry, deterministic, target);
;
        if (entry->GetArena() != NULL) {
          entry.release();
        }
      }
    }
  }

  // map<int32, .protobuf_unittest.MapEnum> map_int32_enum = 16;
  if (!this->map_int32_enum().empty()) {
    typedef ::google::protobuf::Map< ::google::protobuf::int32, ::protobuf_unittest::MapEnum >::const_pointer
        ConstPtr;
    typedef ::google::protobuf::internal::SortItem< ::google::protobuf::int32, ConstPtr > SortItem;
    typedef ::google::protobuf::internal::CompareByFirstField<SortItem> Less;

    if (deterministic &&
        this->map_int32_enum().size() > 1) {
      ::google::protobuf::scoped_array<SortItem> items(
          new SortItem[this->map_int32_enum().size()]);
      typedef ::google::protobuf::Map< ::google::protobuf::int32, ::protobuf_unittest::MapEnum >::size_type size_type;
      size_type n = 0;
      for (::google::protobuf::Map< ::google::protobuf::int32, ::protobuf_unittest::MapEnum >::const_iterator
          it = this->map_int32_enum().begin();
          it != this->map_int32_enum().end(); ++it, ++n) {
        items[n] = SortItem(&*it);
      }
      ::std::sort(&items[0], &items[n], Less());
      ::google::protobuf::scoped_ptr<TestMap_MapInt32EnumEntry> entry;
      for (size_type i = 0; i < n; i++) {
        entry.reset(map_int32_enum_.NewEnumEntryWrapper(
            items[i].second->first, items[i].second->second));
        target = ::google::protobuf::internal::WireFormatLite::
                   InternalWriteMessageNoVirtualToArray(
                       16, *entry, deterministic, target);
;
        if (entry->GetArena() != NULL) {
          entry.release();
        }
      }
    } else {
      ::google::protobuf::scoped_ptr<TestMap_MapInt32EnumEntry> entry;
      for (::google::protobuf::Map< ::google::protobuf::int32, ::protobuf_unittest::MapEnum >::const_iterator
          it = this->map_int32_enum().begin();
          it != this->map_int32_enum().end(); ++it) {
        entry.reset(map_int32_enum_.NewEnumEntryWrapper(
            it->first, it->second));
        target = ::google::protobuf::internal::WireFormatLite::
                   InternalWriteMessageNoVirtualToArray(
                       16, *entry, deterministic, target);
;
        if (entry->GetArena() != NULL) {
          entry.release();
        }
      }
    }
  }

  // map<int32, .protobuf_unittest.ForeignMessage> map_int32_foreign_message = 17;
  if (!this->map_int32_foreign_message().empty()) {
    typedef ::google::protobuf::Map< ::google::protobuf::int32, ::protobuf_unittest::ForeignMessage >::const_pointer
        ConstPtr;
    typedef ::google::protobuf::internal::SortItem< ::google::protobuf::int32, ConstPtr > SortItem;
    typedef ::google::protobuf::internal::CompareByFirstField<SortItem> Less;

    if (deterministic &&
        this->map_int32_foreign_message().size() > 1) {
      ::google::protobuf::scoped_array<SortItem> items(
          new SortItem[this->map_int32_foreign_message().size()]);
      typedef ::google::protobuf::Map< ::google::protobuf::int32, ::protobuf_unittest::ForeignMessage >::size_type size_type;
      size_type n = 0;
      for (::google::protobuf::Map< ::google::protobuf::int32, ::protobuf_unittest::ForeignMessage >::const_iterator
          it = this->map_int32_foreign_message().begin();
          it != this->map_int32_foreign_message().end(); ++it, ++n) {
        items[n] = SortItem(&*it);
      }
      ::std::sort(&items[0], &items[n], Less());
      ::google::protobuf::scoped_ptr<TestMap_MapInt32ForeignMessageEntry> entry;
      for (size_type i = 0; i < n; i++) {
        entry.reset(map_int32_foreign_message_.NewEntryWrapper(
            items[i].second->first, items[i].second->second));
        target = ::google::protobuf::internal::WireFormatLite::
                   InternalWriteMessageNoVirtualToArray(
                       17, *entry, deterministic, target);
;
        if (entry->GetArena() != NULL) {
          entry.release();
        }
      }
    } else {
      ::google::protobuf::scoped_ptr<TestMap_MapInt32ForeignMessageEntry> entry;
      for (::google::protobuf::Map< ::google::protobuf::int32, ::protobuf_unittest::ForeignMessage >::const_iterator
          it = this->map_int32_foreign_message().begin();
          it != this->map_int32_foreign_message().end(); ++it) {
        entry.reset(map_int32_foreign_message_.NewEntryWrapper(
            it->first, it->second));
        target = ::google::protobuf::internal::WireFormatLite::
                   InternalWriteMessageNoVirtualToArray(
                       17, *entry, deterministic, target);
;
        if (entry->GetArena() != NULL) {
          entry.release();
        }
      }
    }
  }

  // map<string, .protobuf_unittest.ForeignMessage> map_string_foreign_message = 18;
  if (!this->map_string_foreign_message().empty()) {
    typedef ::google::protobuf::Map< ::std::string, ::protobuf_unittest::ForeignMessage >::const_pointer
        ConstPtr;
    typedef ConstPtr SortItem;
    typedef ::google::protobuf::internal::CompareByDerefFirst<SortItem> Less;
    struct Utf8Check {
      static void Check(ConstPtr p) {
        ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
          p->first.data(), p->first.length(),
          ::google::protobuf::internal::WireFormatLite::SERIALIZE,
          "protobuf_unittest.TestMap.MapStringForeignMessageEntry.key");
      }
    };

    if (deterministic &&
        this->map_string_foreign_message().size() > 1) {
      ::google::protobuf::scoped_array<SortItem> items(
          new SortItem[this->map_string_foreign_message().size()]);
      typedef ::google::protobuf::Map< ::std::string, ::protobuf_unittest::ForeignMessage >::size_type size_type;
      size_type n = 0;
      for (::google::protobuf::Map< ::std::string, ::protobuf_unittest::ForeignMessage >::const_iterator
          it = this->map_string_foreign_message().begin();
          it != this->map_string_foreign_message().end(); ++it, ++n) {
        items[n] = SortItem(&*it);
      }
      ::std::sort(&items[0], &items[n], Less());
      ::google::protobuf::scoped_ptr<TestMap_MapStringForeignMessageEntry> entry;
      for (size_type i = 0; i < n; i++) {
        entry.reset(map_string_foreign_message_.NewEntryWrapper(
            items[i]->first, items[i]->second));
        target = ::google::protobuf::internal::WireFormatLite::
                   InternalWriteMessageNoVirtualToArray(
                       18, *entry, deterministic, target);
;
        if (entry->GetArena() != NULL) {
          entry.release();
        }
        Utf8Check::Check(items[i]);
      }
    } else {
      ::google::protobuf::scoped_ptr<TestMap_MapStringForeignMessageEntry> entry;
      for (::google::protobuf::Map< ::std::string, ::protobuf_unittest::ForeignMessage >::const_iterator
          it = this->map_string_foreign_message().begin();
          it != this->map_string_foreign_message().end(); ++it) {
        entry.reset(map_string_foreign_message_.NewEntryWrapper(
            it->first, it->second));
        target = ::google::protobuf::internal::WireFormatLite::
                   InternalWriteMessageNoVirtualToArray(
                       18, *entry, deterministic, target);
;
        if (entry->GetArena() != NULL) {
          entry.release();
        }
        Utf8Check::Check(&*it);
      }
    }
  }

  // @@protoc_insertion_point(serialize_to_array_end:protobuf_unittest.TestMap)
  return target;
}

size_t TestMap::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:protobuf_unittest.TestMap)
  size_t total_size = 0;

  // map<int32, int32> map_int32_int32 = 1;
  total_size += 1 *
      ::google::protobuf::internal::FromIntSize(this->map_int32_int32_size());
  {
    ::google::protobuf::scoped_ptr<TestMap_MapInt32Int32Entry> entry;
    for (::google::protobuf::Map< ::google::protobuf::int32, ::google::protobuf::int32 >::const_iterator
        it = this->map_int32_int32().begin();
        it != this->map_int32_int32().end(); ++it) {
      if (entry.get() != NULL && entry->GetArena() != NULL) {
        entry.release();
      }
      entry.reset(map_int32_int32_.NewEntryWrapper(it->first, it->second));
      total_size += ::google::protobuf::internal::WireFormatLite::
          MessageSizeNoVirtual(*entry);
    }
    if (entry.get() != NULL && entry->GetArena() != NULL) {
      entry.release();
    }
  }

  // map<int64, int64> map_int64_int64 = 2;
  total_size += 1 *
      ::google::protobuf::internal::FromIntSize(this->map_int64_int64_size());
  {
    ::google::protobuf::scoped_ptr<TestMap_MapInt64Int64Entry> entry;
    for (::google::protobuf::Map< ::google::protobuf::int64, ::google::protobuf::int64 >::const_iterator
        it = this->map_int64_int64().begin();
        it != this->map_int64_int64().end(); ++it) {
      if (entry.get() != NULL && entry->GetArena() != NULL) {
        entry.release();
      }
      entry.reset(map_int64_int64_.NewEntryWrapper(it->first, it->second));
      total_size += ::google::protobuf::internal::WireFormatLite::
          MessageSizeNoVirtual(*entry);
    }
    if (entry.get() != NULL && entry->GetArena() != NULL) {
      entry.release();
    }
  }

  // map<uint32, uint32> map_uint32_uint32 = 3;
  total_size += 1 *
      ::google::protobuf::internal::FromIntSize(this->map_uint32_uint32_size());
  {
    ::google::protobuf::scoped_ptr<TestMap_MapUint32Uint32Entry> entry;
    for (::google::protobuf::Map< ::google::protobuf::uint32, ::google::protobuf::uint32 >::const_iterator
        it = this->map_uint32_uint32().begin();
        it != this->map_uint32_uint32().end(); ++it) {
      if (entry.get() != NULL && entry->GetArena() != NULL) {
        entry.release();
      }
      entry.reset(map_uint32_uint32_.NewEntryWrapper(it->first, it->second));
      total_size += ::google::protobuf::internal::WireFormatLite::
          MessageSizeNoVirtual(*entry);
    }
    if (entry.get() != NULL && entry->GetArena() != NULL) {
      entry.release();
    }
  }

  // map<uint64, uint64> map_uint64_uint64 = 4;
  total_size += 1 *
      ::google::protobuf::internal::FromIntSize(this->map_uint64_uint64_size());
  {
    ::google::protobuf::scoped_ptr<TestMap_MapUint64Uint64Entry> entry;
    for (::google::protobuf::Map< ::google::protobuf::uint64, ::google::protobuf::uint64 >::const_iterator
        it = this->map_uint64_uint64().begin();
        it != this->map_uint64_uint64().end(); ++it) {
      if (entry.get() != NULL && entry->GetArena() != NULL) {
        entry.release();
      }
      entry.reset(map_uint64_uint64_.NewEntryWrapper(it->first, it->second));
      total_size += ::google::protobuf::internal::WireFormatLite::
          MessageSizeNoVirtual(*entry);
    }
    if (entry.get() != NULL && entry->GetArena() != NULL) {
      entry.release();
    }
  }

  // map<sint32, sint32> map_sint32_sint32 = 5;
  total_size += 1 *
      ::google::protobuf::internal::FromIntSize(this->map_sint32_sint32_size());
  {
    ::google::protobuf::scoped_ptr<TestMap_MapSint32Sint32Entry> entry;
    for (::google::protobuf::Map< ::google::protobuf::int32, ::google::protobuf::int32 >::const_iterator
        it = this->map_sint32_sint32().begin();
        it != this->map_sint32_sint32().end(); ++it) {
      if (entry.get() != NULL && entry->GetArena() != NULL) {
        entry.release();
      }
      entry.reset(map_sint32_sint32_.NewEntryWrapper(it->first, it->second));
      total_size += ::google::protobuf::internal::WireFormatLite::
          MessageSizeNoVirtual(*entry);
    }
    if (entry.get() != NULL && entry->GetArena() != NULL) {
      entry.release();
    }
  }

  // map<sint64, sint64> map_sint64_sint64 = 6;
  total_size += 1 *
      ::google::protobuf::internal::FromIntSize(this->map_sint64_sint64_size());
  {
    ::google::protobuf::scoped_ptr<TestMap_MapSint64Sint64Entry> entry;
    for (::google::protobuf::Map< ::google::protobuf::int64, ::google::protobuf::int64 >::const_iterator
        it = this->map_sint64_sint64().begin();
        it != this->map_sint64_sint64().end(); ++it) {
      if (entry.get() != NULL && entry->GetArena() != NULL) {
        entry.release();
      }
      entry.reset(map_sint64_sint64_.NewEntryWrapper(it->first, it->second));
      total_size += ::google::protobuf::internal::WireFormatLite::
          MessageSizeNoVirtual(*entry);
    }
    if (entry.get() != NULL && entry->GetArena() != NULL) {
      entry.release();
    }
  }

  // map<fixed32, fixed32> map_fixed32_fixed32 = 7;
  total_size += 1 *
      ::google::protobuf::internal::FromIntSize(this->map_fixed32_fixed32_size());
  {
    ::google::protobuf::scoped_ptr<TestMap_MapFixed32Fixed32Entry> entry;
    for (::google::protobuf::Map< ::google::protobuf::uint32, ::google::protobuf::uint32 >::const_iterator
        it = this->map_fixed32_fixed32().begin();
        it != this->map_fixed32_fixed32().end(); ++it) {
      if (entry.get() != NULL && entry->GetArena() != NULL) {
        entry.release();
      }
      entry.reset(map_fixed32_fixed32_.NewEntryWrapper(it->first, it->second));
      total_size += ::google::protobuf::internal::WireFormatLite::
          MessageSizeNoVirtual(*entry);
    }
    if (entry.get() != NULL && entry->GetArena() != NULL) {
      entry.release();
    }
  }

  // map<fixed64, fixed64> map_fixed64_fixed64 = 8;
  total_size += 1 *
      ::google::protobuf::internal::FromIntSize(this->map_fixed64_fixed64_size());
  {
    ::google::protobuf::scoped_ptr<TestMap_MapFixed64Fixed64Entry> entry;
    for (::google::protobuf::Map< ::google::protobuf::uint64, ::google::protobuf::uint64 >::const_iterator
        it = this->map_fixed64_fixed64().begin();
        it != this->map_fixed64_fixed64().end(); ++it) {
      if (entry.get() != NULL && entry->GetArena() != NULL) {
        entry.release();
      }
      entry.reset(map_fixed64_fixed64_.NewEntryWrapper(it->first, it->second));
      total_size += ::google::protobuf::internal::WireFormatLite::
          MessageSizeNoVirtual(*entry);
    }
    if (entry.get() != NULL && entry->GetArena() != NULL) {
      entry.release();
    }
  }

  // map<sfixed32, sfixed32> map_sfixed32_sfixed32 = 9;
  total_size += 1 *
      ::google::protobuf::internal::FromIntSize(this->map_sfixed32_sfixed32_size());
  {
    ::google::protobuf::scoped_ptr<TestMap_MapSfixed32Sfixed32Entry> entry;
    for (::google::protobuf::Map< ::google::protobuf::int32, ::google::protobuf::int32 >::const_iterator
        it = this->map_sfixed32_sfixed32().begin();
        it != this->map_sfixed32_sfixed32().end(); ++it) {
      if (entry.get() != NULL && entry->GetArena() != NULL) {
        entry.release();
      }
      entry.reset(map_sfixed32_sfixed32_.NewEntryWrapper(it->first, it->second));
      total_size += ::google::protobuf::internal::WireFormatLite::
          MessageSizeNoVirtual(*entry);
    }
    if (entry.get() != NULL && entry->GetArena() != NULL) {
      entry.release();
    }
  }

  // map<sfixed64, sfixed64> map_sfixed64_sfixed64 = 10;
  total_size += 1 *
      ::google::protobuf::internal::FromIntSize(this->map_sfixed64_sfixed64_size());
  {
    ::google::protobuf::scoped_ptr<TestMap_MapSfixed64Sfixed64Entry> entry;
    for (::google::protobuf::Map< ::google::protobuf::int64, ::google::protobuf::int64 >::const_iterator
        it = this->map_sfixed64_sfixed64().begin();
        it != this->map_sfixed64_sfixed64().end(); ++it) {
      if (entry.get() != NULL && entry->GetArena() != NULL) {
        entry.release();
      }
      entry.reset(map_sfixed64_sfixed64_.NewEntryWrapper(it->first, it->second));
      total_size += ::google::protobuf::internal::WireFormatLite::
          MessageSizeNoVirtual(*entry);
    }
    if (entry.get() != NULL && entry->GetArena() != NULL) {
      entry.release();
    }
  }

  // map<int32, float> map_int32_float = 11;
  total_size += 1 *
      ::google::protobuf::internal::FromIntSize(this->map_int32_float_size());
  {
    ::google::protobuf::scoped_ptr<TestMap_MapInt32FloatEntry> entry;
    for (::google::protobuf::Map< ::google::protobuf::int32, float >::const_iterator
        it = this->map_int32_float().begin();
        it != this->map_int32_float().end(); ++it) {
      if (entry.get() != NULL && entry->GetArena() != NULL) {
        entry.release();
      }
      entry.reset(map_int32_float_.NewEntryWrapper(it->first, it->second));
      total_size += ::google::protobuf::internal::WireFormatLite::
          MessageSizeNoVirtual(*entry);
    }
    if (entry.get() != NULL && entry->GetArena() != NULL) {
      entry.release();
    }
  }

  // map<int32, double> map_int32_double = 12;
  total_size += 1 *
      ::google::protobuf::internal::FromIntSize(this->map_int32_double_size());
  {
    ::google::protobuf::scoped_ptr<TestMap_MapInt32DoubleEntry> entry;
    for (::google::protobuf::Map< ::google::protobuf::int32, double >::const_iterator
        it = this->map_int32_double().begin();
        it != this->map_int32_double().end(); ++it) {
      if (entry.get() != NULL && entry->GetArena() != NULL) {
        entry.release();
      }
      entry.reset(map_int32_double_.NewEntryWrapper(it->first, it->second));
      total_size += ::google::protobuf::internal::WireFormatLite::
          MessageSizeNoVirtual(*entry);
    }
    if (entry.get() != NULL && entry->GetArena() != NULL) {
      entry.release();
    }
  }

  // map<bool, bool> map_bool_bool = 13;
  total_size += 1 *
      ::google::protobuf::internal::FromIntSize(this->map_bool_bool_size());
  {
    ::google::protobuf::scoped_ptr<TestMap_MapBoolBoolEntry> entry;
    for (::google::protobuf::Map< bool, bool >::const_iterator
        it = this->map_bool_bool().begin();
        it != this->map_bool_bool().end(); ++it) {
      if (entry.get() != NULL && entry->GetArena() != NULL) {
        entry.release();
      }
      entry.reset(map_bool_bool_.NewEntryWrapper(it->first, it->second));
      total_size += ::google::protobuf::internal::WireFormatLite::
          MessageSizeNoVirtual(*entry);
    }
    if (entry.get() != NULL && entry->GetArena() != NULL) {
      entry.release();
    }
  }

  // map<string, string> map_string_string = 14;
  total_size += 1 *
      ::google::protobuf::internal::FromIntSize(this->map_string_string_size());
  {
    ::google::protobuf::scoped_ptr<TestMap_MapStringStringEntry> entry;
    for (::google::protobuf::Map< ::std::string, ::std::string >::const_iterator
        it = this->map_string_string().begin();
        it != this->map_string_string().end(); ++it) {
      if (entry.get() != NULL && entry->GetArena() != NULL) {
        entry.release();
      }
      entry.reset(map_string_string_.NewEntryWrapper(it->first, it->second));
      total_size += ::google::protobuf::internal::WireFormatLite::
          MessageSizeNoVirtual(*entry);
    }
    if (entry.get() != NULL && entry->GetArena() != NULL) {
      entry.release();
    }
  }

  // map<int32, bytes> map_int32_bytes = 15;
  total_size += 1 *
      ::google::protobuf::internal::FromIntSize(this->map_int32_bytes_size());
  {
    ::google::protobuf::scoped_ptr<TestMap_MapInt32BytesEntry> entry;
    for (::google::protobuf::Map< ::google::protobuf::int32, ::std::string >::const_iterator
        it = this->map_int32_bytes().begin();
        it != this->map_int32_bytes().end(); ++it) {
      if (entry.get() != NULL && entry->GetArena() != NULL) {
        entry.release();
      }
      entry.reset(map_int32_bytes_.NewEntryWrapper(it->first, it->second));
      total_size += ::google::protobuf::internal::WireFormatLite::
          MessageSizeNoVirtual(*entry);
    }
    if (entry.get() != NULL && entry->GetArena() != NULL) {
      entry.release();
    }
  }

  // map<int32, .protobuf_unittest.MapEnum> map_int32_enum = 16;
  total_size += 2 *
      ::google::protobuf::internal::FromIntSize(this->map_int32_enum_size());
  {
    ::google::protobuf::scoped_ptr<TestMap_MapInt32EnumEntry> entry;
    for (::google::protobuf::Map< ::google::protobuf::int32, ::protobuf_unittest::MapEnum >::const_iterator
        it = this->map_int32_enum().begin();
        it != this->map_int32_enum().end(); ++it) {
      if (entry.get() != NULL && entry->GetArena() != NULL) {
        entry.release();
      }
      entry.reset(map_int32_enum_.NewEnumEntryWrapper(it->first, it->second));
      total_size += ::google::protobuf::internal::WireFormatLite::
          MessageSizeNoVirtual(*entry);
    }
    if (entry.get() != NULL && entry->GetArena() != NULL) {
      entry.release();
    }
  }

  // map<int32, .protobuf_unittest.ForeignMessage> map_int32_foreign_message = 17;
  total_size += 2 *
      ::google::protobuf::internal::FromIntSize(this->map_int32_foreign_message_size());
  {
    ::google::protobuf::scoped_ptr<TestMap_MapInt32ForeignMessageEntry> entry;
    for (::google::protobuf::Map< ::google::protobuf::int32, ::protobuf_unittest::ForeignMessage >::const_iterator
        it = this->map_int32_foreign_message().begin();
        it != this->map_int32_foreign_message().end(); ++it) {
      if (entry.get() != NULL && entry->GetArena() != NULL) {
        entry.release();
      }
      entry.reset(map_int32_foreign_message_.NewEntryWrapper(it->first, it->second));
      total_size += ::google::protobuf::internal::WireFormatLite::
          MessageSizeNoVirtual(*entry);
    }
    if (entry.get() != NULL && entry->GetArena() != NULL) {
      entry.release();
    }
  }

  // map<string, .protobuf_unittest.ForeignMessage> map_string_foreign_message = 18;
  total_size += 2 *
      ::google::protobuf::internal::FromIntSize(this->map_string_foreign_message_size());
  {
    ::google::protobuf::scoped_ptr<TestMap_MapStringForeignMessageEntry> entry;
    for (::google::protobuf::Map< ::std::string, ::protobuf_unittest::ForeignMessage >::const_iterator
        it = this->map_string_foreign_message().begin();
        it != this->map_string_foreign_message().end(); ++it) {
      if (entry.get() != NULL && entry->GetArena() != NULL) {
        entry.release();
      }
      entry.reset(map_string_foreign_message_.NewEntryWrapper(it->first, it->second));
      total_size += ::google::protobuf::internal::WireFormatLite::
          MessageSizeNoVirtual(*entry);
    }
    if (entry.get() != NULL && entry->GetArena() != NULL) {
      entry.release();
    }
  }

  int cached_size = ::google::protobuf::internal::ToCachedSize(total_size);
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = cached_size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
  return total_size;
}

void TestMap::MergeFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_merge_from_start:protobuf_unittest.TestMap)
  if (GOOGLE_PREDICT_FALSE(&from == this)) MergeFromFail(__LINE__);
  const TestMap* source =
      ::google::protobuf::internal::DynamicCastToGenerated<const TestMap>(
          &from);
  if (source == NULL) {
  // @@protoc_insertion_point(generalized_merge_from_cast_fail:protobuf_unittest.TestMap)
    ::google::protobuf::internal::ReflectionOps::Merge(from, this);
  } else {
  // @@protoc_insertion_point(generalized_merge_from_cast_success:protobuf_unittest.TestMap)
    UnsafeMergeFrom(*source);
  }
}

void TestMap::MergeFrom(const TestMap& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:protobuf_unittest.TestMap)
  if (GOOGLE_PREDICT_TRUE(&from != this)) {
    UnsafeMergeFrom(from);
  } else {
    MergeFromFail(__LINE__);
  }
}

void TestMap::UnsafeMergeFrom(const TestMap& from) {
  GOOGLE_DCHECK(&from != this);
  map_int32_int32_.MergeFrom(from.map_int32_int32_);
  map_int64_int64_.MergeFrom(from.map_int64_int64_);
  map_uint32_uint32_.MergeFrom(from.map_uint32_uint32_);
  map_uint64_uint64_.MergeFrom(from.map_uint64_uint64_);
  map_sint32_sint32_.MergeFrom(from.map_sint32_sint32_);
  map_sint64_sint64_.MergeFrom(from.map_sint64_sint64_);
  map_fixed32_fixed32_.MergeFrom(from.map_fixed32_fixed32_);
  map_fixed64_fixed64_.MergeFrom(from.map_fixed64_fixed64_);
  map_sfixed32_sfixed32_.MergeFrom(from.map_sfixed32_sfixed32_);
  map_sfixed64_sfixed64_.MergeFrom(from.map_sfixed64_sfixed64_);
  map_int32_float_.MergeFrom(from.map_int32_float_);
  map_int32_double_.MergeFrom(from.map_int32_double_);
  map_bool_bool_.MergeFrom(from.map_bool_bool_);
  map_string_string_.MergeFrom(from.map_string_string_);
  map_int32_bytes_.MergeFrom(from.map_int32_bytes_);
  map_int32_enum_.MergeFrom(from.map_int32_enum_);
  map_int32_foreign_message_.MergeFrom(from.map_int32_foreign_message_);
  map_string_foreign_message_.MergeFrom(from.map_string_foreign_message_);
}

void TestMap::CopyFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_copy_from_start:protobuf_unittest.TestMap)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

void TestMap::CopyFrom(const TestMap& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:protobuf_unittest.TestMap)
  if (&from == this) return;
  Clear();
  UnsafeMergeFrom(from);
}

bool TestMap::IsInitialized() const {

  return true;
}

void TestMap::Swap(TestMap* other) {
  if (other == this) return;
  if (GetArenaNoVirtual() == other->GetArenaNoVirtual()) {
    InternalSwap(other);
  } else {
    TestMap temp;
    temp.UnsafeMergeFrom(*this);
    CopyFrom(*other);
    other->CopyFrom(temp);
  }
}
void TestMap::UnsafeArenaSwap(TestMap* other) {
  if (other == this) return;
  GOOGLE_DCHECK(GetArenaNoVirtual() == other->GetArenaNoVirtual());
  InternalSwap(other);
}
void TestMap::InternalSwap(TestMap* other) {
  map_int32_int32_.Swap(&other->map_int32_int32_);
  map_int64_int64_.Swap(&other->map_int64_int64_);
  map_uint32_uint32_.Swap(&other->map_uint32_uint32_);
  map_uint64_uint64_.Swap(&other->map_uint64_uint64_);
  map_sint32_sint32_.Swap(&other->map_sint32_sint32_);
  map_sint64_sint64_.Swap(&other->map_sint64_sint64_);
  map_fixed32_fixed32_.Swap(&other->map_fixed32_fixed32_);
  map_fixed64_fixed64_.Swap(&other->map_fixed64_fixed64_);
  map_sfixed32_sfixed32_.Swap(&other->map_sfixed32_sfixed32_);
  map_sfixed64_sfixed64_.Swap(&other->map_sfixed64_sfixed64_);
  map_int32_float_.Swap(&other->map_int32_float_);
  map_int32_double_.Swap(&other->map_int32_double_);
  map_bool_bool_.Swap(&other->map_bool_bool_);
  map_string_string_.Swap(&other->map_string_string_);
  map_int32_bytes_.Swap(&other->map_int32_bytes_);
  map_int32_enum_.Swap(&other->map_int32_enum_);
  map_int32_foreign_message_.Swap(&other->map_int32_foreign_message_);
  map_string_foreign_message_.Swap(&other->map_string_foreign_message_);
  _internal_metadata_.Swap(&other->_internal_metadata_);
  std::swap(_cached_size_, other->_cached_size_);
}

::google::protobuf::Metadata TestMap::GetMetadata() const {
  protobuf_AssignDescriptorsOnce();
  ::google::protobuf::Metadata metadata;
  metadata.descriptor = TestMap_descriptor_;
  metadata.reflection = TestMap_reflection_;
  return metadata;
}

#if PROTOBUF_INLINE_NOT_IN_HEADERS
// TestMap

// map<int32, int32> map_int32_int32 = 1;
int TestMap::map_int32_int32_size() const {
  return map_int32_int32_.size();
}
void TestMap::clear_map_int32_int32() {
  map_int32_int32_.Clear();
}
 const ::google::protobuf::Map< ::google::protobuf::int32, ::google::protobuf::int32 >&
TestMap::map_int32_int32() const {
  // @@protoc_insertion_point(field_map:protobuf_unittest.TestMap.map_int32_int32)
  return map_int32_int32_.GetMap();
}
 ::google::protobuf::Map< ::google::protobuf::int32, ::google::protobuf::int32 >*
TestMap::mutable_map_int32_int32() {
  // @@protoc_insertion_point(field_mutable_map:protobuf_unittest.TestMap.map_int32_int32)
  return map_int32_int32_.MutableMap();
}

// map<int64, int64> map_int64_int64 = 2;
int TestMap::map_int64_int64_size() const {
  return map_int64_int64_.size();
}
void TestMap::clear_map_int64_int64() {
  map_int64_int64_.Clear();
}
 const ::google::protobuf::Map< ::google::protobuf::int64, ::google::protobuf::int64 >&
TestMap::map_int64_int64() const {
  // @@protoc_insertion_point(field_map:protobuf_unittest.TestMap.map_int64_int64)
  return map_int64_int64_.GetMap();
}
 ::google::protobuf::Map< ::google::protobuf::int64, ::google::protobuf::int64 >*
TestMap::mutable_map_int64_int64() {
  // @@protoc_insertion_point(field_mutable_map:protobuf_unittest.TestMap.map_int64_int64)
  return map_int64_int64_.MutableMap();
}

// map<uint32, uint32> map_uint32_uint32 = 3;
int TestMap::map_uint32_uint32_size() const {
  return map_uint32_uint32_.size();
}
void TestMap::clear_map_uint32_uint32() {
  map_uint32_uint32_.Clear();
}
 const ::google::protobuf::Map< ::google::protobuf::uint32, ::google::protobuf::uint32 >&
TestMap::map_uint32_uint32() const {
  // @@protoc_insertion_point(field_map:protobuf_unittest.TestMap.map_uint32_uint32)
  return map_uint32_uint32_.GetMap();
}
 ::google::protobuf::Map< ::google::protobuf::uint32, ::google::protobuf::uint32 >*
TestMap::mutable_map_uint32_uint32() {
  // @@protoc_insertion_point(field_mutable_map:protobuf_unittest.TestMap.map_uint32_uint32)
  return map_uint32_uint32_.MutableMap();
}

// map<uint64, uint64> map_uint64_uint64 = 4;
int TestMap::map_uint64_uint64_size() const {
  return map_uint64_uint64_.size();
}
void TestMap::clear_map_uint64_uint64() {
  map_uint64_uint64_.Clear();
}
 const ::google::protobuf::Map< ::google::protobuf::uint64, ::google::protobuf::uint64 >&
TestMap::map_uint64_uint64() const {
  // @@protoc_insertion_point(field_map:protobuf_unittest.TestMap.map_uint64_uint64)
  return map_uint64_uint64_.GetMap();
}
 ::google::protobuf::Map< ::google::protobuf::uint64, ::google::protobuf::uint64 >*
TestMap::mutable_map_uint64_uint64() {
  // @@protoc_insertion_point(field_mutable_map:protobuf_unittest.TestMap.map_uint64_uint64)
  return map_uint64_uint64_.MutableMap();
}

// map<sint32, sint32> map_sint32_sint32 = 5;
int TestMap::map_sint32_sint32_size() const {
  return map_sint32_sint32_.size();
}
void TestMap::clear_map_sint32_sint32() {
  map_sint32_sint32_.Clear();
}
 const ::google::protobuf::Map< ::google::protobuf::int32, ::google::protobuf::int32 >&
TestMap::map_sint32_sint32() const {
  // @@protoc_insertion_point(field_map:protobuf_unittest.TestMap.map_sint32_sint32)
  return map_sint32_sint32_.GetMap();
}
 ::google::protobuf::Map< ::google::protobuf::int32, ::google::protobuf::int32 >*
TestMap::mutable_map_sint32_sint32() {
  // @@protoc_insertion_point(field_mutable_map:protobuf_unittest.TestMap.map_sint32_sint32)
  return map_sint32_sint32_.MutableMap();
}

// map<sint64, sint64> map_sint64_sint64 = 6;
int TestMap::map_sint64_sint64_size() const {
  return map_sint64_sint64_.size();
}
void TestMap::clear_map_sint64_sint64() {
  map_sint64_sint64_.Clear();
}
 const ::google::protobuf::Map< ::google::protobuf::int64, ::google::protobuf::int64 >&
TestMap::map_sint64_sint64() const {
  // @@protoc_insertion_point(field_map:protobuf_unittest.TestMap.map_sint64_sint64)
  return map_sint64_sint64_.GetMap();
}
 ::google::protobuf::Map< ::google::protobuf::int64, ::google::protobuf::int64 >*
TestMap::mutable_map_sint64_sint64() {
  // @@protoc_insertion_point(field_mutable_map:protobuf_unittest.TestMap.map_sint64_sint64)
  return map_sint64_sint64_.MutableMap();
}

// map<fixed32, fixed32> map_fixed32_fixed32 = 7;
int TestMap::map_fixed32_fixed32_size() const {
  return map_fixed32_fixed32_.size();
}
void TestMap::clear_map_fixed32_fixed32() {
  map_fixed32_fixed32_.Clear();
}
 const ::google::protobuf::Map< ::google::protobuf::uint32, ::google::protobuf::uint32 >&
TestMap::map_fixed32_fixed32() const {
  // @@protoc_insertion_point(field_map:protobuf_unittest.TestMap.map_fixed32_fixed32)
  return map_fixed32_fixed32_.GetMap();
}
 ::google::protobuf::Map< ::google::protobuf::uint32, ::google::protobuf::uint32 >*
TestMap::mutable_map_fixed32_fixed32() {
  // @@protoc_insertion_point(field_mutable_map:protobuf_unittest.TestMap.map_fixed32_fixed32)
  return map_fixed32_fixed32_.MutableMap();
}

// map<fixed64, fixed64> map_fixed64_fixed64 = 8;
int TestMap::map_fixed64_fixed64_size() const {
  return map_fixed64_fixed64_.size();
}
void TestMap::clear_map_fixed64_fixed64() {
  map_fixed64_fixed64_.Clear();
}
 const ::google::protobuf::Map< ::google::protobuf::uint64, ::google::protobuf::uint64 >&
TestMap::map_fixed64_fixed64() const {
  // @@protoc_insertion_point(field_map:protobuf_unittest.TestMap.map_fixed64_fixed64)
  return map_fixed64_fixed64_.GetMap();
}
 ::google::protobuf::Map< ::google::protobuf::uint64, ::google::protobuf::uint64 >*
TestMap::mutable_map_fixed64_fixed64() {
  // @@protoc_insertion_point(field_mutable_map:protobuf_unittest.TestMap.map_fixed64_fixed64)
  return map_fixed64_fixed64_.MutableMap();
}

// map<sfixed32, sfixed32> map_sfixed32_sfixed32 = 9;
int TestMap::map_sfixed32_sfixed32_size() const {
  return map_sfixed32_sfixed32_.size();
}
void TestMap::clear_map_sfixed32_sfixed32() {
  map_sfixed32_sfixed32_.Clear();
}
 const ::google::protobuf::Map< ::google::protobuf::int32, ::google::protobuf::int32 >&
TestMap::map_sfixed32_sfixed32() const {
  // @@protoc_insertion_point(field_map:protobuf_unittest.TestMap.map_sfixed32_sfixed32)
  return map_sfixed32_sfixed32_.GetMap();
}
 ::google::protobuf::Map< ::google::protobuf::int32, ::google::protobuf::int32 >*
TestMap::mutable_map_sfixed32_sfixed32() {
  // @@protoc_insertion_point(field_mutable_map:protobuf_unittest.TestMap.map_sfixed32_sfixed32)
  return map_sfixed32_sfixed32_.MutableMap();
}

// map<sfixed64, sfixed64> map_sfixed64_sfixed64 = 10;
int TestMap::map_sfixed64_sfixed64_size() const {
  return map_sfixed64_sfixed64_.size();
}
void TestMap::clear_map_sfixed64_sfixed64() {
  map_sfixed64_sfixed64_.Clear();
}
 const ::google::protobuf::Map< ::google::protobuf::int64, ::google::protobuf::int64 >&
TestMap::map_sfixed64_sfixed64() const {
  // @@protoc_insertion_point(field_map:protobuf_unittest.TestMap.map_sfixed64_sfixed64)
  return map_sfixed64_sfixed64_.GetMap();
}
 ::google::protobuf::Map< ::google::protobuf::int64, ::google::protobuf::int64 >*
TestMap::mutable_map_sfixed64_sfixed64() {
  // @@protoc_insertion_point(field_mutable_map:protobuf_unittest.TestMap.map_sfixed64_sfixed64)
  return map_sfixed64_sfixed64_.MutableMap();
}

// map<int32, float> map_int32_float = 11;
int TestMap::map_int32_float_size() const {
  return map_int32_float_.size();
}
void TestMap::clear_map_int32_float() {
  map_int32_float_.Clear();
}
 const ::google::protobuf::Map< ::google::protobuf::int32, float >&
TestMap::map_int32_float() const {
  // @@protoc_insertion_point(field_map:protobuf_unittest.TestMap.map_int32_float)
  return map_int32_float_.GetMap();
}
 ::google::protobuf::Map< ::google::protobuf::int32, float >*
TestMap::mutable_map_int32_float() {
  // @@protoc_insertion_point(field_mutable_map:protobuf_unittest.TestMap.map_int32_float)
  return map_int32_float_.MutableMap();
}

// map<int32, double> map_int32_double = 12;
int TestMap::map_int32_double_size() const {
  return map_int32_double_.size();
}
void TestMap::clear_map_int32_double() {
  map_int32_double_.Clear();
}
 const ::google::protobuf::Map< ::google::protobuf::int32, double >&
TestMap::map_int32_double() const {
  // @@protoc_insertion_point(field_map:protobuf_unittest.TestMap.map_int32_double)
  return map_int32_double_.GetMap();
}
 ::google::protobuf::Map< ::google::protobuf::int32, double >*
TestMap::mutable_map_int32_double() {
  // @@protoc_insertion_point(field_mutable_map:protobuf_unittest.TestMap.map_int32_double)
  return map_int32_double_.MutableMap();
}

// map<bool, bool> map_bool_bool = 13;
int TestMap::map_bool_bool_size() const {
  return map_bool_bool_.size();
}
void TestMap::clear_map_bool_bool() {
  map_bool_bool_.Clear();
}
 const ::google::protobuf::Map< bool, bool >&
TestMap::map_bool_bool() const {
  // @@protoc_insertion_point(field_map:protobuf_unittest.TestMap.map_bool_bool)
  return map_bool_bool_.GetMap();
}
 ::google::protobuf::Map< bool, bool >*
TestMap::mutable_map_bool_bool() {
  // @@protoc_insertion_point(field_mutable_map:protobuf_unittest.TestMap.map_bool_bool)
  return map_bool_bool_.MutableMap();
}

// map<string, string> map_string_string = 14;
int TestMap::map_string_string_size() const {
  return map_string_string_.size();
}
void TestMap::clear_map_string_string() {
  map_string_string_.Clear();
}
 const ::google::protobuf::Map< ::std::string, ::std::string >&
TestMap::map_string_string() const {
  // @@protoc_insertion_point(field_map:protobuf_unittest.TestMap.map_string_string)
  return map_string_string_.GetMap();
}
 ::google::protobuf::Map< ::std::string, ::std::string >*
TestMap::mutable_map_string_string() {
  // @@protoc_insertion_point(field_mutable_map:protobuf_unittest.TestMap.map_string_string)
  return map_string_string_.MutableMap();
}

// map<int32, bytes> map_int32_bytes = 15;
int TestMap::map_int32_bytes_size() const {
  return map_int32_bytes_.size();
}
void TestMap::clear_map_int32_bytes() {
  map_int32_bytes_.Clear();
}
 const ::google::protobuf::Map< ::google::protobuf::int32, ::std::string >&
TestMap::map_int32_bytes() const {
  // @@protoc_insertion_point(field_map:protobuf_unittest.TestMap.map_int32_bytes)
  return map_int32_bytes_.GetMap();
}
 ::google::protobuf::Map< ::google::protobuf::int32, ::std::string >*
TestMap::mutable_map_int32_bytes() {
  // @@protoc_insertion_point(field_mutable_map:protobuf_unittest.TestMap.map_int32_bytes)
  return map_int32_bytes_.MutableMap();
}

// map<int32, .protobuf_unittest.MapEnum> map_int32_enum = 16;
int TestMap::map_int32_enum_size() const {
  return map_int32_enum_.size();
}
void TestMap::clear_map_int32_enum() {
  map_int32_enum_.Clear();
}
 const ::google::protobuf::Map< ::google::protobuf::int32, ::protobuf_unittest::MapEnum >&
TestMap::map_int32_enum() const {
  // @@protoc_insertion_point(field_map:protobuf_unittest.TestMap.map_int32_enum)
  return map_int32_enum_.GetMap();
}
 ::google::protobuf::Map< ::google::protobuf::int32, ::protobuf_unittest::MapEnum >*
TestMap::mutable_map_int32_enum() {
  // @@protoc_insertion_point(field_mutable_map:protobuf_unittest.TestMap.map_int32_enum)
  return map_int32_enum_.MutableMap();
}

// map<int32, .protobuf_unittest.ForeignMessage> map_int32_foreign_message = 17;
int TestMap::map_int32_foreign_message_size() const {
  return map_int32_foreign_message_.size();
}
void TestMap::clear_map_int32_foreign_message() {
  map_int32_foreign_message_.Clear();
}
 const ::google::protobuf::Map< ::google::protobuf::int32, ::protobuf_unittest::ForeignMessage >&
TestMap::map_int32_foreign_message() const {
  // @@protoc_insertion_point(field_map:protobuf_unittest.TestMap.map_int32_foreign_message)
  return map_int32_foreign_message_.GetMap();
}
 ::google::protobuf::Map< ::google::protobuf::int32, ::protobuf_unittest::ForeignMessage >*
TestMap::mutable_map_int32_foreign_message() {
  // @@protoc_insertion_point(field_mutable_map:protobuf_unittest.TestMap.map_int32_foreign_message)
  return map_int32_foreign_message_.MutableMap();
}

// map<string, .protobuf_unittest.ForeignMessage> map_string_foreign_message = 18;
int TestMap::map_string_foreign_message_size() const {
  return map_string_foreign_message_.size();
}
void TestMap::clear_map_string_foreign_message() {
  map_string_foreign_message_.Clear();
}
 const ::google::protobuf::Map< ::std::string, ::protobuf_unittest::ForeignMessage >&
TestMap::map_string_foreign_message() const {
  // @@protoc_insertion_point(field_map:protobuf_unittest.TestMap.map_string_foreign_message)
  return map_string_foreign_message_.GetMap();
}
 ::google::protobuf::Map< ::std::string, ::protobuf_unittest::ForeignMessage >*
TestMap::mutable_map_string_foreign_message() {
  // @@protoc_insertion_point(field_mutable_map:protobuf_unittest.TestMap.map_string_foreign_message)
  return map_string_foreign_message_.MutableMap();
}

inline const TestMap* TestMap::internal_default_instance() {
  return &TestMap_default_instance_.get();
}
#endif  // PROTOBUF_INLINE_NOT_IN_HEADERS

// ===================================================================

void TestMapSubmessage::_slow_mutable_test_map() {
  test_map_ = ::google::protobuf::Arena::CreateMessage< ::protobuf_unittest::TestMap >(
      GetArenaNoVirtual());
}
::protobuf_unittest::TestMap* TestMapSubmessage::_slow_release_test_map() {
  if (test_map_ == NULL) {
    return NULL;
  } else {
    ::protobuf_unittest::TestMap* temp = new ::protobuf_unittest::TestMap(*test_map_);
    test_map_ = NULL;
    return temp;
  }
}
::protobuf_unittest::TestMap* TestMapSubmessage::unsafe_arena_release_test_map() {
  // @@protoc_insertion_point(field_unsafe_arena_release:protobuf_unittest.TestMapSubmessage.test_map)
  
  ::protobuf_unittest::TestMap* temp = test_map_;
  test_map_ = NULL;
  return temp;
}
void TestMapSubmessage::_slow_set_allocated_test_map(
    ::google::protobuf::Arena* message_arena, ::protobuf_unittest::TestMap** test_map) {
    if (message_arena != NULL && 
        ::google::protobuf::Arena::GetArena(*test_map) == NULL) {
      message_arena->Own(*test_map);
    } else if (message_arena !=
               ::google::protobuf::Arena::GetArena(*test_map)) {
      ::protobuf_unittest::TestMap* new_test_map = 
            ::google::protobuf::Arena::CreateMessage< ::protobuf_unittest::TestMap >(
            message_arena);
      new_test_map->CopyFrom(**test_map);
      *test_map = new_test_map;
    }
}
void TestMapSubmessage::unsafe_arena_set_allocated_test_map(
    ::protobuf_unittest::TestMap* test_map) {
  if (GetArenaNoVirtual() == NULL) {
    delete test_map_;
  }
  test_map_ = test_map;
  if (test_map) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:protobuf_unittest.TestMapSubmessage.test_map)
}
#if !defined(_MSC_VER) || _MSC_VER >= 1900
const int TestMapSubmessage::kTestMapFieldNumber;
#endif  // !defined(_MSC_VER) || _MSC_VER >= 1900

TestMapSubmessage::TestMapSubmessage()
  : ::google::protobuf::Message(), _internal_metadata_(NULL) {
  if (this != internal_default_instance()) protobuf_InitDefaults_google_2fprotobuf_2fmap_5funittest_2eproto();
  SharedCtor();
  // @@protoc_insertion_point(constructor:protobuf_unittest.TestMapSubmessage)
}
TestMapSubmessage::TestMapSubmessage(::google::protobuf::Arena* arena)
  : ::google::protobuf::Message(),
  _internal_metadata_(arena) {
#ifdef GOOGLE_PROTOBUF_NO_STATIC_INITIALIZER
  protobuf_InitDefaults_google_2fprotobuf_2fmap_5funittest_2eproto();
#endif  // GOOGLE_PROTOBUF_NO_STATIC_INITIALIZER
  SharedCtor();
  RegisterArenaDtor(arena);
  // @@protoc_insertion_point(arena_constructor:protobuf_unittest.TestMapSubmessage)
}

void TestMapSubmessage::InitAsDefaultInstance() {
  test_map_ = const_cast< ::protobuf_unittest::TestMap*>(
      ::protobuf_unittest::TestMap::internal_default_instance());
}

TestMapSubmessage::TestMapSubmessage(const TestMapSubmessage& from)
  : ::google::protobuf::Message(),
    _internal_metadata_(NULL) {
  SharedCtor();
  UnsafeMergeFrom(from);
  // @@protoc_insertion_point(copy_constructor:protobuf_unittest.TestMapSubmessage)
}

void TestMapSubmessage::SharedCtor() {
  test_map_ = NULL;
  _cached_size_ = 0;
}

TestMapSubmessage::~TestMapSubmessage() {
  // @@protoc_insertion_point(destructor:protobuf_unittest.TestMapSubmessage)
  SharedDtor();
}

void TestMapSubmessage::SharedDtor() {
  ::google::protobuf::Arena* arena = GetArenaNoVirtual();
  if (arena != NULL) {
    return;
  }

  if (this != &TestMapSubmessage_default_instance_.get()) {
    delete test_map_;
  }
}

void TestMapSubmessage::ArenaDtor(void* object) {
  TestMapSubmessage* _this = reinterpret_cast< TestMapSubmessage* >(object);
  (void)_this;
}
void TestMapSubmessage::RegisterArenaDtor(::google::protobuf::Arena* arena) {
}
void TestMapSubmessage::SetCachedSize(int size) const {
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
}
const ::google::protobuf::Descriptor* TestMapSubmessage::descriptor() {
  protobuf_AssignDescriptorsOnce();
  return TestMapSubmessage_descriptor_;
}

const TestMapSubmessage& TestMapSubmessage::default_instance() {
  protobuf_InitDefaults_google_2fprotobuf_2fmap_5funittest_2eproto();
  return *internal_default_instance();
}

::google::protobuf::internal::ExplicitlyConstructed<TestMapSubmessage> TestMapSubmessage_default_instance_;

TestMapSubmessage* TestMapSubmessage::New(::google::protobuf::Arena* arena) const {
  return ::google::protobuf::Arena::CreateMessage<TestMapSubmessage>(arena);
}

void TestMapSubmessage::Clear() {
// @@protoc_insertion_point(message_clear_start:protobuf_unittest.TestMapSubmessage)
  if (GetArenaNoVirtual() == NULL && test_map_ != NULL) delete test_map_;
  test_map_ = NULL;
}

bool TestMapSubmessage::MergePartialFromCodedStream(
    ::google::protobuf::io::CodedInputStream* input) {
#define DO_(EXPRESSION) if (!GOOGLE_PREDICT_TRUE(EXPRESSION)) goto failure
  ::google::protobuf::uint32 tag;
  // @@protoc_insertion_point(parse_start:protobuf_unittest.TestMapSubmessage)
  for (;;) {
    ::std::pair< ::google::protobuf::uint32, bool> p = input->ReadTagWithCutoff(127);
    tag = p.first;
    if (!p.second) goto handle_unusual;
    switch (::google::protobuf::internal::WireFormatLite::GetTagFieldNumber(tag)) {
      // optional .protobuf_unittest.TestMap test_map = 1;
      case 1: {
        if (tag == 10) {
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessageNoVirtual(
               input, mutable_test_map()));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectAtEnd()) goto success;
        break;
      }

      default: {
      handle_unusual:
        if (tag == 0 ||
            ::google::protobuf::internal::WireFormatLite::GetTagWireType(tag) ==
            ::google::protobuf::internal::WireFormatLite::WIRETYPE_END_GROUP) {
          goto success;
        }
        DO_(::google::protobuf::internal::WireFormatLite::SkipField(input, tag));
        break;
      }
    }
  }
success:
  // @@protoc_insertion_point(parse_success:protobuf_unittest.TestMapSubmessage)
  return true;
failure:
  // @@protoc_insertion_point(parse_failure:protobuf_unittest.TestMapSubmessage)
  return false;
#undef DO_
}

void TestMapSubmessage::SerializeWithCachedSizes(
    ::google::protobuf::io::CodedOutputStream* output) const {
  // @@protoc_insertion_point(serialize_start:protobuf_unittest.TestMapSubmessage)
  // optional .protobuf_unittest.TestMap test_map = 1;
  if (this->has_test_map()) {
    ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
      1, *this->test_map_, output);
  }

  // @@protoc_insertion_point(serialize_end:protobuf_unittest.TestMapSubmessage)
}

::google::protobuf::uint8* TestMapSubmessage::InternalSerializeWithCachedSizesToArray(
    bool deterministic, ::google::protobuf::uint8* target) const {
  (void)deterministic; // Unused
  // @@protoc_insertion_point(serialize_to_array_start:protobuf_unittest.TestMapSubmessage)
  // optional .protobuf_unittest.TestMap test_map = 1;
  if (this->has_test_map()) {
    target = ::google::protobuf::internal::WireFormatLite::
      InternalWriteMessageNoVirtualToArray(
        1, *this->test_map_, false, target);
  }

  // @@protoc_insertion_point(serialize_to_array_end:protobuf_unittest.TestMapSubmessage)
  return target;
}

size_t TestMapSubmessage::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:protobuf_unittest.TestMapSubmessage)
  size_t total_size = 0;

  // optional .protobuf_unittest.TestMap test_map = 1;
  if (this->has_test_map()) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::MessageSizeNoVirtual(
        *this->test_map_);
  }

  int cached_size = ::google::protobuf::internal::ToCachedSize(total_size);
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = cached_size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
  return total_size;
}

void TestMapSubmessage::MergeFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_merge_from_start:protobuf_unittest.TestMapSubmessage)
  if (GOOGLE_PREDICT_FALSE(&from == this)) MergeFromFail(__LINE__);
  const TestMapSubmessage* source =
      ::google::protobuf::internal::DynamicCastToGenerated<const TestMapSubmessage>(
          &from);
  if (source == NULL) {
  // @@protoc_insertion_point(generalized_merge_from_cast_fail:protobuf_unittest.TestMapSubmessage)
    ::google::protobuf::internal::ReflectionOps::Merge(from, this);
  } else {
  // @@protoc_insertion_point(generalized_merge_from_cast_success:protobuf_unittest.TestMapSubmessage)
    UnsafeMergeFrom(*source);
  }
}

void TestMapSubmessage::MergeFrom(const TestMapSubmessage& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:protobuf_unittest.TestMapSubmessage)
  if (GOOGLE_PREDICT_TRUE(&from != this)) {
    UnsafeMergeFrom(from);
  } else {
    MergeFromFail(__LINE__);
  }
}

void TestMapSubmessage::UnsafeMergeFrom(const TestMapSubmessage& from) {
  GOOGLE_DCHECK(&from != this);
  if (from.has_test_map()) {
    mutable_test_map()->::protobuf_unittest::TestMap::MergeFrom(from.test_map());
  }
}

void TestMapSubmessage::CopyFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_copy_from_start:protobuf_unittest.TestMapSubmessage)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

void TestMapSubmessage::CopyFrom(const TestMapSubmessage& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:protobuf_unittest.TestMapSubmessage)
  if (&from == this) return;
  Clear();
  UnsafeMergeFrom(from);
}

bool TestMapSubmessage::IsInitialized() const {

  return true;
}

void TestMapSubmessage::Swap(TestMapSubmessage* other) {
  if (other == this) return;
  if (GetArenaNoVirtual() == other->GetArenaNoVirtual()) {
    InternalSwap(other);
  } else {
    TestMapSubmessage temp;
    temp.UnsafeMergeFrom(*this);
    CopyFrom(*other);
    other->CopyFrom(temp);
  }
}
void TestMapSubmessage::UnsafeArenaSwap(TestMapSubmessage* other) {
  if (other == this) return;
  GOOGLE_DCHECK(GetArenaNoVirtual() == other->GetArenaNoVirtual());
  InternalSwap(other);
}
void TestMapSubmessage::InternalSwap(TestMapSubmessage* other) {
  std::swap(test_map_, other->test_map_);
  _internal_metadata_.Swap(&other->_internal_metadata_);
  std::swap(_cached_size_, other->_cached_size_);
}

::google::protobuf::Metadata TestMapSubmessage::GetMetadata() const {
  protobuf_AssignDescriptorsOnce();
  ::google::protobuf::Metadata metadata;
  metadata.descriptor = TestMapSubmessage_descriptor_;
  metadata.reflection = TestMapSubmessage_reflection_;
  return metadata;
}

#if PROTOBUF_INLINE_NOT_IN_HEADERS
// TestMapSubmessage

// optional .protobuf_unittest.TestMap test_map = 1;
bool TestMapSubmessage::has_test_map() const {
  return this != internal_default_instance() && test_map_ != NULL;
}
void TestMapSubmessage::clear_test_map() {
  if (GetArenaNoVirtual() == NULL && test_map_ != NULL) delete test_map_;
  test_map_ = NULL;
}
const ::protobuf_unittest::TestMap& TestMapSubmessage::test_map() const {
  // @@protoc_insertion_point(field_get:protobuf_unittest.TestMapSubmessage.test_map)
  return test_map_ != NULL ? *test_map_
                         : *::protobuf_unittest::TestMap::internal_default_instance();
}
::protobuf_unittest::TestMap* TestMapSubmessage::mutable_test_map() {
  
  if (test_map_ == NULL) {
    _slow_mutable_test_map();
  }
  // @@protoc_insertion_point(field_mutable:protobuf_unittest.TestMapSubmessage.test_map)
  return test_map_;
}
::protobuf_unittest::TestMap* TestMapSubmessage::release_test_map() {
  // @@protoc_insertion_point(field_release:protobuf_unittest.TestMapSubmessage.test_map)
  
  if (GetArenaNoVirtual() != NULL) {
    return _slow_release_test_map();
  } else {
    ::protobuf_unittest::TestMap* temp = test_map_;
    test_map_ = NULL;
    return temp;
  }
}
 void TestMapSubmessage::set_allocated_test_map(::protobuf_unittest::TestMap* test_map) {
  ::google::protobuf::Arena* message_arena = GetArenaNoVirtual();
  if (message_arena == NULL) {
    delete test_map_;
  }
  if (test_map != NULL) {
    _slow_set_allocated_test_map(message_arena, &test_map);
  }
  test_map_ = test_map;
  if (test_map) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_set_allocated:protobuf_unittest.TestMapSubmessage.test_map)
}

inline const TestMapSubmessage* TestMapSubmessage::internal_default_instance() {
  return &TestMapSubmessage_default_instance_.get();
}
#endif  // PROTOBUF_INLINE_NOT_IN_HEADERS

// ===================================================================

#if !defined(_MSC_VER) || _MSC_VER >= 1900
const int TestMessageMap::kMapInt32MessageFieldNumber;
#endif  // !defined(_MSC_VER) || _MSC_VER >= 1900

TestMessageMap::TestMessageMap()
  : ::google::protobuf::Message(), _internal_metadata_(NULL) {
  if (this != internal_default_instance()) protobuf_InitDefaults_google_2fprotobuf_2fmap_5funittest_2eproto();
  SharedCtor();
  // @@protoc_insertion_point(constructor:protobuf_unittest.TestMessageMap)
}
TestMessageMap::TestMessageMap(::google::protobuf::Arena* arena)
  : ::google::protobuf::Message(),
  _internal_metadata_(arena),
  map_int32_message_(arena) {
#ifdef GOOGLE_PROTOBUF_NO_STATIC_INITIALIZER
  protobuf_InitDefaults_google_2fprotobuf_2fmap_5funittest_2eproto();
#endif  // GOOGLE_PROTOBUF_NO_STATIC_INITIALIZER
  SharedCtor();
  RegisterArenaDtor(arena);
  // @@protoc_insertion_point(arena_constructor:protobuf_unittest.TestMessageMap)
}

void TestMessageMap::InitAsDefaultInstance() {
}

TestMessageMap::TestMessageMap(const TestMessageMap& from)
  : ::google::protobuf::Message(),
    _internal_metadata_(NULL) {
  SharedCtor();
  UnsafeMergeFrom(from);
  // @@protoc_insertion_point(copy_constructor:protobuf_unittest.TestMessageMap)
}

void TestMessageMap::SharedCtor() {
  map_int32_message_.SetAssignDescriptorCallback(
      protobuf_AssignDescriptorsOnce);
  map_int32_message_.SetEntryDescriptor(
      &::protobuf_unittest::TestMessageMap_MapInt32MessageEntry_descriptor_);
  _cached_size_ = 0;
}

TestMessageMap::~TestMessageMap() {
  // @@protoc_insertion_point(destructor:protobuf_unittest.TestMessageMap)
  SharedDtor();
}

void TestMessageMap::SharedDtor() {
  ::google::protobuf::Arena* arena = GetArenaNoVirtual();
  if (arena != NULL) {
    return;
  }

}

void TestMessageMap::ArenaDtor(void* object) {
  TestMessageMap* _this = reinterpret_cast< TestMessageMap* >(object);
  (void)_this;
}
void TestMessageMap::RegisterArenaDtor(::google::protobuf::Arena* arena) {
}
void TestMessageMap::SetCachedSize(int size) const {
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
}
const ::google::protobuf::Descriptor* TestMessageMap::descriptor() {
  protobuf_AssignDescriptorsOnce();
  return TestMessageMap_descriptor_;
}

const TestMessageMap& TestMessageMap::default_instance() {
  protobuf_InitDefaults_google_2fprotobuf_2fmap_5funittest_2eproto();
  return *internal_default_instance();
}

::google::protobuf::internal::ExplicitlyConstructed<TestMessageMap> TestMessageMap_default_instance_;

TestMessageMap* TestMessageMap::New(::google::protobuf::Arena* arena) const {
  return ::google::protobuf::Arena::CreateMessage<TestMessageMap>(arena);
}

void TestMessageMap::Clear() {
// @@protoc_insertion_point(message_clear_start:protobuf_unittest.TestMessageMap)
  map_int32_message_.Clear();
}

bool TestMessageMap::MergePartialFromCodedStream(
    ::google::protobuf::io::CodedInputStream* input) {
#define DO_(EXPRESSION) if (!GOOGLE_PREDICT_TRUE(EXPRESSION)) goto failure
  ::google::protobuf::uint32 tag;
  // @@protoc_insertion_point(parse_start:protobuf_unittest.TestMessageMap)
  for (;;) {
    ::std::pair< ::google::protobuf::uint32, bool> p = input->ReadTagWithCutoff(127);
    tag = p.first;
    if (!p.second) goto handle_unusual;
    switch (::google::protobuf::internal::WireFormatLite::GetTagFieldNumber(tag)) {
      // map<int32, .protobuf_unittest.TestAllTypes> map_int32_message = 1;
      case 1: {
        if (tag == 10) {
          DO_(input->IncrementRecursionDepth());
         parse_loop_map_int32_message:
          TestMessageMap_MapInt32MessageEntry::Parser< ::google::protobuf::internal::MapField<
              ::google::protobuf::int32, ::protobuf_unittest::TestAllTypes,
              ::google::protobuf::internal::WireFormatLite::TYPE_INT32,
              ::google::protobuf::internal::WireFormatLite::TYPE_MESSAGE,
              0 >,
            ::google::protobuf::Map< ::google::protobuf::int32, ::protobuf_unittest::TestAllTypes > > parser(&map_int32_message_);
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessageNoVirtual(
              input, &parser));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(10)) goto parse_loop_map_int32_message;
        input->UnsafeDecrementRecursionDepth();
        if (input->ExpectAtEnd()) goto success;
        break;
      }

      default: {
      handle_unusual:
        if (tag == 0 ||
            ::google::protobuf::internal::WireFormatLite::GetTagWireType(tag) ==
            ::google::protobuf::internal::WireFormatLite::WIRETYPE_END_GROUP) {
          goto success;
        }
        DO_(::google::protobuf::internal::WireFormatLite::SkipField(input, tag));
        break;
      }
    }
  }
success:
  // @@protoc_insertion_point(parse_success:protobuf_unittest.TestMessageMap)
  return true;
failure:
  // @@protoc_insertion_point(parse_failure:protobuf_unittest.TestMessageMap)
  return false;
#undef DO_
}

void TestMessageMap::SerializeWithCachedSizes(
    ::google::protobuf::io::CodedOutputStream* output) const {
  // @@protoc_insertion_point(serialize_start:protobuf_unittest.TestMessageMap)
  // map<int32, .protobuf_unittest.TestAllTypes> map_int32_message = 1;
  if (!this->map_int32_message().empty()) {
    typedef ::google::protobuf::Map< ::google::protobuf::int32, ::protobuf_unittest::TestAllTypes >::const_pointer
        ConstPtr;
    typedef ::google::protobuf::internal::SortItem< ::google::protobuf::int32, ConstPtr > SortItem;
    typedef ::google::protobuf::internal::CompareByFirstField<SortItem> Less;

    if (output->IsSerializationDeterminstic() &&
        this->map_int32_message().size() > 1) {
      ::google::protobuf::scoped_array<SortItem> items(
          new SortItem[this->map_int32_message().size()]);
      typedef ::google::protobuf::Map< ::google::protobuf::int32, ::protobuf_unittest::TestAllTypes >::size_type size_type;
      size_type n = 0;
      for (::google::protobuf::Map< ::google::protobuf::int32, ::protobuf_unittest::TestAllTypes >::const_iterator
          it = this->map_int32_message().begin();
          it != this->map_int32_message().end(); ++it, ++n) {
        items[n] = SortItem(&*it);
      }
      ::std::sort(&items[0], &items[n], Less());
      ::google::protobuf::scoped_ptr<TestMessageMap_MapInt32MessageEntry> entry;
      for (size_type i = 0; i < n; i++) {
        entry.reset(map_int32_message_.NewEntryWrapper(
            items[i].second->first, items[i].second->second));
        ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
            1, *entry, output);
        if (entry->GetArena() != NULL) {
          entry.release();
        }
      }
    } else {
      ::google::protobuf::scoped_ptr<TestMessageMap_MapInt32MessageEntry> entry;
      for (::google::protobuf::Map< ::google::protobuf::int32, ::protobuf_unittest::TestAllTypes >::const_iterator
          it = this->map_int32_message().begin();
          it != this->map_int32_message().end(); ++it) {
        entry.reset(map_int32_message_.NewEntryWrapper(
            it->first, it->second));
        ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
            1, *entry, output);
        if (entry->GetArena() != NULL) {
          entry.release();
        }
      }
    }
  }

  // @@protoc_insertion_point(serialize_end:protobuf_unittest.TestMessageMap)
}

::google::protobuf::uint8* TestMessageMap::InternalSerializeWithCachedSizesToArray(
    bool deterministic, ::google::protobuf::uint8* target) const {
  (void)deterministic; // Unused
  // @@protoc_insertion_point(serialize_to_array_start:protobuf_unittest.TestMessageMap)
  // map<int32, .protobuf_unittest.TestAllTypes> map_int32_message = 1;
  if (!this->map_int32_message().empty()) {
    typedef ::google::protobuf::Map< ::google::protobuf::int32, ::protobuf_unittest::TestAllTypes >::const_pointer
        ConstPtr;
    typedef ::google::protobuf::internal::SortItem< ::google::protobuf::int32, ConstPtr > SortItem;
    typedef ::google::protobuf::internal::CompareByFirstField<SortItem> Less;

    if (deterministic &&
        this->map_int32_message().size() > 1) {
      ::google::protobuf::scoped_array<SortItem> items(
          new SortItem[this->map_int32_message().size()]);
      typedef ::google::protobuf::Map< ::google::protobuf::int32, ::protobuf_unittest::TestAllTypes >::size_type size_type;
      size_type n = 0;
      for (::google::protobuf::Map< ::google::protobuf::int32, ::protobuf_unittest::TestAllTypes >::const_iterator
          it = this->map_int32_message().begin();
          it != this->map_int32_message().end(); ++it, ++n) {
        items[n] = SortItem(&*it);
      }
      ::std::sort(&items[0], &items[n], Less());
      ::google::protobuf::scoped_ptr<TestMessageMap_MapInt32MessageEntry> entry;
      for (size_type i = 0; i < n; i++) {
        entry.reset(map_int32_message_.NewEntryWrapper(
            items[i].second->first, items[i].second->second));
        target = ::google::protobuf::internal::WireFormatLite::
                   InternalWriteMessageNoVirtualToArray(
                       1, *entry, deterministic, target);
;
        if (entry->GetArena() != NULL) {
          entry.release();
        }
      }
    } else {
      ::google::protobuf::scoped_ptr<TestMessageMap_MapInt32MessageEntry> entry;
      for (::google::protobuf::Map< ::google::protobuf::int32, ::protobuf_unittest::TestAllTypes >::const_iterator
          it = this->map_int32_message().begin();
          it != this->map_int32_message().end(); ++it) {
        entry.reset(map_int32_message_.NewEntryWrapper(
            it->first, it->second));
        target = ::google::protobuf::internal::WireFormatLite::
                   InternalWriteMessageNoVirtualToArray(
                       1, *entry, deterministic, target);
;
        if (entry->GetArena() != NULL) {
          entry.release();
        }
      }
    }
  }

  // @@protoc_insertion_point(serialize_to_array_end:protobuf_unittest.TestMessageMap)
  return target;
}

size_t TestMessageMap::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:protobuf_unittest.TestMessageMap)
  size_t total_size = 0;

  // map<int32, .protobuf_unittest.TestAllTypes> map_int32_message = 1;
  total_size += 1 *
      ::google::protobuf::internal::FromIntSize(this->map_int32_message_size());
  {
    ::google::protobuf::scoped_ptr<TestMessageMap_MapInt32MessageEntry> entry;
    for (::google::protobuf::Map< ::google::protobuf::int32, ::protobuf_unittest::TestAllTypes >::const_iterator
        it = this->map_int32_message().begin();
        it != this->map_int32_message().end(); ++it) {
      if (entry.get() != NULL && entry->GetArena() != NULL) {
        entry.release();
      }
      entry.reset(map_int32_message_.NewEntryWrapper(it->first, it->second));
      total_size += ::google::protobuf::internal::WireFormatLite::
          MessageSizeNoVirtual(*entry);
    }
    if (entry.get() != NULL && entry->GetArena() != NULL) {
      entry.release();
    }
  }

  int cached_size = ::google::protobuf::internal::ToCachedSize(total_size);
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = cached_size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
  return total_size;
}

void TestMessageMap::MergeFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_merge_from_start:protobuf_unittest.TestMessageMap)
  if (GOOGLE_PREDICT_FALSE(&from == this)) MergeFromFail(__LINE__);
  const TestMessageMap* source =
      ::google::protobuf::internal::DynamicCastToGenerated<const TestMessageMap>(
          &from);
  if (source == NULL) {
  // @@protoc_insertion_point(generalized_merge_from_cast_fail:protobuf_unittest.TestMessageMap)
    ::google::protobuf::internal::ReflectionOps::Merge(from, this);
  } else {
  // @@protoc_insertion_point(generalized_merge_from_cast_success:protobuf_unittest.TestMessageMap)
    UnsafeMergeFrom(*source);
  }
}

void TestMessageMap::MergeFrom(const TestMessageMap& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:protobuf_unittest.TestMessageMap)
  if (GOOGLE_PREDICT_TRUE(&from != this)) {
    UnsafeMergeFrom(from);
  } else {
    MergeFromFail(__LINE__);
  }
}

void TestMessageMap::UnsafeMergeFrom(const TestMessageMap& from) {
  GOOGLE_DCHECK(&from != this);
  map_int32_message_.MergeFrom(from.map_int32_message_);
}

void TestMessageMap::CopyFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_copy_from_start:protobuf_unittest.TestMessageMap)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

void TestMessageMap::CopyFrom(const TestMessageMap& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:protobuf_unittest.TestMessageMap)
  if (&from == this) return;
  Clear();
  UnsafeMergeFrom(from);
}

bool TestMessageMap::IsInitialized() const {

  return true;
}

void TestMessageMap::Swap(TestMessageMap* other) {
  if (other == this) return;
  if (GetArenaNoVirtual() == other->GetArenaNoVirtual()) {
    InternalSwap(other);
  } else {
    TestMessageMap temp;
    temp.UnsafeMergeFrom(*this);
    CopyFrom(*other);
    other->CopyFrom(temp);
  }
}
void TestMessageMap::UnsafeArenaSwap(TestMessageMap* other) {
  if (other == this) return;
  GOOGLE_DCHECK(GetArenaNoVirtual() == other->GetArenaNoVirtual());
  InternalSwap(other);
}
void TestMessageMap::InternalSwap(TestMessageMap* other) {
  map_int32_message_.Swap(&other->map_int32_message_);
  _internal_metadata_.Swap(&other->_internal_metadata_);
  std::swap(_cached_size_, other->_cached_size_);
}

::google::protobuf::Metadata TestMessageMap::GetMetadata() const {
  protobuf_AssignDescriptorsOnce();
  ::google::protobuf::Metadata metadata;
  metadata.descriptor = TestMessageMap_descriptor_;
  metadata.reflection = TestMessageMap_reflection_;
  return metadata;
}

#if PROTOBUF_INLINE_NOT_IN_HEADERS
// TestMessageMap

// map<int32, .protobuf_unittest.TestAllTypes> map_int32_message = 1;
int TestMessageMap::map_int32_message_size() const {
  return map_int32_message_.size();
}
void TestMessageMap::clear_map_int32_message() {
  map_int32_message_.Clear();
}
 const ::google::protobuf::Map< ::google::protobuf::int32, ::protobuf_unittest::TestAllTypes >&
TestMessageMap::map_int32_message() const {
  // @@protoc_insertion_point(field_map:protobuf_unittest.TestMessageMap.map_int32_message)
  return map_int32_message_.GetMap();
}
 ::google::protobuf::Map< ::google::protobuf::int32, ::protobuf_unittest::TestAllTypes >*
TestMessageMap::mutable_map_int32_message() {
  // @@protoc_insertion_point(field_mutable_map:protobuf_unittest.TestMessageMap.map_int32_message)
  return map_int32_message_.MutableMap();
}

inline const TestMessageMap* TestMessageMap::internal_default_instance() {
  return &TestMessageMap_default_instance_.get();
}
#endif  // PROTOBUF_INLINE_NOT_IN_HEADERS

// ===================================================================

#if !defined(_MSC_VER) || _MSC_VER >= 1900
const int TestSameTypeMap::kMap1FieldNumber;
const int TestSameTypeMap::kMap2FieldNumber;
#endif  // !defined(_MSC_VER) || _MSC_VER >= 1900

TestSameTypeMap::TestSameTypeMap()
  : ::google::protobuf::Message(), _internal_metadata_(NULL) {
  if (this != internal_default_instance()) protobuf_InitDefaults_google_2fprotobuf_2fmap_5funittest_2eproto();
  SharedCtor();
  // @@protoc_insertion_point(constructor:protobuf_unittest.TestSameTypeMap)
}
TestSameTypeMap::TestSameTypeMap(::google::protobuf::Arena* arena)
  : ::google::protobuf::Message(),
  _internal_metadata_(arena),
  map1_(arena),
  map2_(arena) {
#ifdef GOOGLE_PROTOBUF_NO_STATIC_INITIALIZER
  protobuf_InitDefaults_google_2fprotobuf_2fmap_5funittest_2eproto();
#endif  // GOOGLE_PROTOBUF_NO_STATIC_INITIALIZER
  SharedCtor();
  RegisterArenaDtor(arena);
  // @@protoc_insertion_point(arena_constructor:protobuf_unittest.TestSameTypeMap)
}

void TestSameTypeMap::InitAsDefaultInstance() {
}

TestSameTypeMap::TestSameTypeMap(const TestSameTypeMap& from)
  : ::google::protobuf::Message(),
    _internal_metadata_(NULL) {
  SharedCtor();
  UnsafeMergeFrom(from);
  // @@protoc_insertion_point(copy_constructor:protobuf_unittest.TestSameTypeMap)
}

void TestSameTypeMap::SharedCtor() {
  map1_.SetAssignDescriptorCallback(
      protobuf_AssignDescriptorsOnce);
  map1_.SetEntryDescriptor(
      &::protobuf_unittest::TestSameTypeMap_Map1Entry_descriptor_);
  map2_.SetAssignDescriptorCallback(
      protobuf_AssignDescriptorsOnce);
  map2_.SetEntryDescriptor(
      &::protobuf_unittest::TestSameTypeMap_Map2Entry_descriptor_);
  _cached_size_ = 0;
}

TestSameTypeMap::~TestSameTypeMap() {
  // @@protoc_insertion_point(destructor:protobuf_unittest.TestSameTypeMap)
  SharedDtor();
}

void TestSameTypeMap::SharedDtor() {
  ::google::protobuf::Arena* arena = GetArenaNoVirtual();
  if (arena != NULL) {
    return;
  }

}

void TestSameTypeMap::ArenaDtor(void* object) {
  TestSameTypeMap* _this = reinterpret_cast< TestSameTypeMap* >(object);
  (void)_this;
}
void TestSameTypeMap::RegisterArenaDtor(::google::protobuf::Arena* arena) {
}
void TestSameTypeMap::SetCachedSize(int size) const {
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
}
const ::google::protobuf::Descriptor* TestSameTypeMap::descriptor() {
  protobuf_AssignDescriptorsOnce();
  return TestSameTypeMap_descriptor_;
}

const TestSameTypeMap& TestSameTypeMap::default_instance() {
  protobuf_InitDefaults_google_2fprotobuf_2fmap_5funittest_2eproto();
  return *internal_default_instance();
}

::google::protobuf::internal::ExplicitlyConstructed<TestSameTypeMap> TestSameTypeMap_default_instance_;

TestSameTypeMap* TestSameTypeMap::New(::google::protobuf::Arena* arena) const {
  return ::google::protobuf::Arena::CreateMessage<TestSameTypeMap>(arena);
}

void TestSameTypeMap::Clear() {
// @@protoc_insertion_point(message_clear_start:protobuf_unittest.TestSameTypeMap)
  map1_.Clear();
  map2_.Clear();
}

bool TestSameTypeMap::MergePartialFromCodedStream(
    ::google::protobuf::io::CodedInputStream* input) {
#define DO_(EXPRESSION) if (!GOOGLE_PREDICT_TRUE(EXPRESSION)) goto failure
  ::google::protobuf::uint32 tag;
  // @@protoc_insertion_point(parse_start:protobuf_unittest.TestSameTypeMap)
  for (;;) {
    ::std::pair< ::google::protobuf::uint32, bool> p = input->ReadTagWithCutoff(127);
    tag = p.first;
    if (!p.second) goto handle_unusual;
    switch (::google::protobuf::internal::WireFormatLite::GetTagFieldNumber(tag)) {
      // map<int32, int32> map1 = 1;
      case 1: {
        if (tag == 10) {
          DO_(input->IncrementRecursionDepth());
         parse_loop_map1:
          TestSameTypeMap_Map1Entry::Parser< ::google::protobuf::internal::MapField<
              ::google::protobuf::int32, ::google::protobuf::int32,
              ::google::protobuf::internal::WireFormatLite::TYPE_INT32,
              ::google::protobuf::internal::WireFormatLite::TYPE_INT32,
              0 >,
            ::google::protobuf::Map< ::google::protobuf::int32, ::google::protobuf::int32 > > parser(&map1_);
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessageNoVirtual(
              input, &parser));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(10)) goto parse_loop_map1;
        if (input->ExpectTag(18)) goto parse_loop_map2;
        input->UnsafeDecrementRecursionDepth();
        break;
      }

      // map<int32, int32> map2 = 2;
      case 2: {
        if (tag == 18) {
          DO_(input->IncrementRecursionDepth());
         parse_loop_map2:
          TestSameTypeMap_Map2Entry::Parser< ::google::protobuf::internal::MapField<
              ::google::protobuf::int32, ::google::protobuf::int32,
              ::google::protobuf::internal::WireFormatLite::TYPE_INT32,
              ::google::protobuf::internal::WireFormatLite::TYPE_INT32,
              0 >,
            ::google::protobuf::Map< ::google::protobuf::int32, ::google::protobuf::int32 > > parser(&map2_);
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessageNoVirtual(
              input, &parser));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(18)) goto parse_loop_map2;
        input->UnsafeDecrementRecursionDepth();
        if (input->ExpectAtEnd()) goto success;
        break;
      }

      default: {
      handle_unusual:
        if (tag == 0 ||
            ::google::protobuf::internal::WireFormatLite::GetTagWireType(tag) ==
            ::google::protobuf::internal::WireFormatLite::WIRETYPE_END_GROUP) {
          goto success;
        }
        DO_(::google::protobuf::internal::WireFormatLite::SkipField(input, tag));
        break;
      }
    }
  }
success:
  // @@protoc_insertion_point(parse_success:protobuf_unittest.TestSameTypeMap)
  return true;
failure:
  // @@protoc_insertion_point(parse_failure:protobuf_unittest.TestSameTypeMap)
  return false;
#undef DO_
}

void TestSameTypeMap::SerializeWithCachedSizes(
    ::google::protobuf::io::CodedOutputStream* output) const {
  // @@protoc_insertion_point(serialize_start:protobuf_unittest.TestSameTypeMap)
  // map<int32, int32> map1 = 1;
  if (!this->map1().empty()) {
    typedef ::google::protobuf::Map< ::google::protobuf::int32, ::google::protobuf::int32 >::const_pointer
        ConstPtr;
    typedef ::google::protobuf::internal::SortItem< ::google::protobuf::int32, ConstPtr > SortItem;
    typedef ::google::protobuf::internal::CompareByFirstField<SortItem> Less;

    if (output->IsSerializationDeterminstic() &&
        this->map1().size() > 1) {
      ::google::protobuf::scoped_array<SortItem> items(
          new SortItem[this->map1().size()]);
      typedef ::google::protobuf::Map< ::google::protobuf::int32, ::google::protobuf::int32 >::size_type size_type;
      size_type n = 0;
      for (::google::protobuf::Map< ::google::protobuf::int32, ::google::protobuf::int32 >::const_iterator
          it = this->map1().begin();
          it != this->map1().end(); ++it, ++n) {
        items[n] = SortItem(&*it);
      }
      ::std::sort(&items[0], &items[n], Less());
      ::google::protobuf::scoped_ptr<TestSameTypeMap_Map1Entry> entry;
      for (size_type i = 0; i < n; i++) {
        entry.reset(map1_.NewEntryWrapper(
            items[i].second->first, items[i].second->second));
        ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
            1, *entry, output);
        if (entry->GetArena() != NULL) {
          entry.release();
        }
      }
    } else {
      ::google::protobuf::scoped_ptr<TestSameTypeMap_Map1Entry> entry;
      for (::google::protobuf::Map< ::google::protobuf::int32, ::google::protobuf::int32 >::const_iterator
          it = this->map1().begin();
          it != this->map1().end(); ++it) {
        entry.reset(map1_.NewEntryWrapper(
            it->first, it->second));
        ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
            1, *entry, output);
        if (entry->GetArena() != NULL) {
          entry.release();
        }
      }
    }
  }

  // map<int32, int32> map2 = 2;
  if (!this->map2().empty()) {
    typedef ::google::protobuf::Map< ::google::protobuf::int32, ::google::protobuf::int32 >::const_pointer
        ConstPtr;
    typedef ::google::protobuf::internal::SortItem< ::google::protobuf::int32, ConstPtr > SortItem;
    typedef ::google::protobuf::internal::CompareByFirstField<SortItem> Less;

    if (output->IsSerializationDeterminstic() &&
        this->map2().size() > 1) {
      ::google::protobuf::scoped_array<SortItem> items(
          new SortItem[this->map2().size()]);
      typedef ::google::protobuf::Map< ::google::protobuf::int32, ::google::protobuf::int32 >::size_type size_type;
      size_type n = 0;
      for (::google::protobuf::Map< ::google::protobuf::int32, ::google::protobuf::int32 >::const_iterator
          it = this->map2().begin();
          it != this->map2().end(); ++it, ++n) {
        items[n] = SortItem(&*it);
      }
      ::std::sort(&items[0], &items[n], Less());
      ::google::protobuf::scoped_ptr<TestSameTypeMap_Map2Entry> entry;
      for (size_type i = 0; i < n; i++) {
        entry.reset(map2_.NewEntryWrapper(
            items[i].second->first, items[i].second->second));
        ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
            2, *entry, output);
        if (entry->GetArena() != NULL) {
          entry.release();
        }
      }
    } else {
      ::google::protobuf::scoped_ptr<TestSameTypeMap_Map2Entry> entry;
      for (::google::protobuf::Map< ::google::protobuf::int32, ::google::protobuf::int32 >::const_iterator
          it = this->map2().begin();
          it != this->map2().end(); ++it) {
        entry.reset(map2_.NewEntryWrapper(
            it->first, it->second));
        ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
            2, *entry, output);
        if (entry->GetArena() != NULL) {
          entry.release();
        }
      }
    }
  }

  // @@protoc_insertion_point(serialize_end:protobuf_unittest.TestSameTypeMap)
}

::google::protobuf::uint8* TestSameTypeMap::InternalSerializeWithCachedSizesToArray(
    bool deterministic, ::google::protobuf::uint8* target) const {
  (void)deterministic; // Unused
  // @@protoc_insertion_point(serialize_to_array_start:protobuf_unittest.TestSameTypeMap)
  // map<int32, int32> map1 = 1;
  if (!this->map1().empty()) {
    typedef ::google::protobuf::Map< ::google::protobuf::int32, ::google::protobuf::int32 >::const_pointer
        ConstPtr;
    typedef ::google::protobuf::internal::SortItem< ::google::protobuf::int32, ConstPtr > SortItem;
    typedef ::google::protobuf::internal::CompareByFirstField<SortItem> Less;

    if (deterministic &&
        this->map1().size() > 1) {
      ::google::protobuf::scoped_array<SortItem> items(
          new SortItem[this->map1().size()]);
      typedef ::google::protobuf::Map< ::google::protobuf::int32, ::google::protobuf::int32 >::size_type size_type;
      size_type n = 0;
      for (::google::protobuf::Map< ::google::protobuf::int32, ::google::protobuf::int32 >::const_iterator
          it = this->map1().begin();
          it != this->map1().end(); ++it, ++n) {
        items[n] = SortItem(&*it);
      }
      ::std::sort(&items[0], &items[n], Less());
      ::google::protobuf::scoped_ptr<TestSameTypeMap_Map1Entry> entry;
      for (size_type i = 0; i < n; i++) {
        entry.reset(map1_.NewEntryWrapper(
            items[i].second->first, items[i].second->second));
        target = ::google::protobuf::internal::WireFormatLite::
                   InternalWriteMessageNoVirtualToArray(
                       1, *entry, deterministic, target);
;
        if (entry->GetArena() != NULL) {
          entry.release();
        }
      }
    } else {
      ::google::protobuf::scoped_ptr<TestSameTypeMap_Map1Entry> entry;
      for (::google::protobuf::Map< ::google::protobuf::int32, ::google::protobuf::int32 >::const_iterator
          it = this->map1().begin();
          it != this->map1().end(); ++it) {
        entry.reset(map1_.NewEntryWrapper(
            it->first, it->second));
        target = ::google::protobuf::internal::WireFormatLite::
                   InternalWriteMessageNoVirtualToArray(
                       1, *entry, deterministic, target);
;
        if (entry->GetArena() != NULL) {
          entry.release();
        }
      }
    }
  }

  // map<int32, int32> map2 = 2;
  if (!this->map2().empty()) {
    typedef ::google::protobuf::Map< ::google::protobuf::int32, ::google::protobuf::int32 >::const_pointer
        ConstPtr;
    typedef ::google::protobuf::internal::SortItem< ::google::protobuf::int32, ConstPtr > SortItem;
    typedef ::google::protobuf::internal::CompareByFirstField<SortItem> Less;

    if (deterministic &&
        this->map2().size() > 1) {
      ::google::protobuf::scoped_array<SortItem> items(
          new SortItem[this->map2().size()]);
      typedef ::google::protobuf::Map< ::google::protobuf::int32, ::google::protobuf::int32 >::size_type size_type;
      size_type n = 0;
      for (::google::protobuf::Map< ::google::protobuf::int32, ::google::protobuf::int32 >::const_iterator
          it = this->map2().begin();
          it != this->map2().end(); ++it, ++n) {
        items[n] = SortItem(&*it);
      }
      ::std::sort(&items[0], &items[n], Less());
      ::google::protobuf::scoped_ptr<TestSameTypeMap_Map2Entry> entry;
      for (size_type i = 0; i < n; i++) {
        entry.reset(map2_.NewEntryWrapper(
            items[i].second->first, items[i].second->second));
        target = ::google::protobuf::internal::WireFormatLite::
                   InternalWriteMessageNoVirtualToArray(
                       2, *entry, deterministic, target);
;
        if (entry->GetArena() != NULL) {
          entry.release();
        }
      }
    } else {
      ::google::protobuf::scoped_ptr<TestSameTypeMap_Map2Entry> entry;
      for (::google::protobuf::Map< ::google::protobuf::int32, ::google::protobuf::int32 >::const_iterator
          it = this->map2().begin();
          it != this->map2().end(); ++it) {
        entry.reset(map2_.NewEntryWrapper(
            it->first, it->second));
        target = ::google::protobuf::internal::WireFormatLite::
                   InternalWriteMessageNoVirtualToArray(
                       2, *entry, deterministic, target);
;
        if (entry->GetArena() != NULL) {
          entry.release();
        }
      }
    }
  }

  // @@protoc_insertion_point(serialize_to_array_end:protobuf_unittest.TestSameTypeMap)
  return target;
}

size_t TestSameTypeMap::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:protobuf_unittest.TestSameTypeMap)
  size_t total_size = 0;

  // map<int32, int32> map1 = 1;
  total_size += 1 *
      ::google::protobuf::internal::FromIntSize(this->map1_size());
  {
    ::google::protobuf::scoped_ptr<TestSameTypeMap_Map1Entry> entry;
    for (::google::protobuf::Map< ::google::protobuf::int32, ::google::protobuf::int32 >::const_iterator
        it = this->map1().begin();
        it != this->map1().end(); ++it) {
      if (entry.get() != NULL && entry->GetArena() != NULL) {
        entry.release();
      }
      entry.reset(map1_.NewEntryWrapper(it->first, it->second));
      total_size += ::google::protobuf::internal::WireFormatLite::
          MessageSizeNoVirtual(*entry);
    }
    if (entry.get() != NULL && entry->GetArena() != NULL) {
      entry.release();
    }
  }

  // map<int32, int32> map2 = 2;
  total_size += 1 *
      ::google::protobuf::internal::FromIntSize(this->map2_size());
  {
    ::google::protobuf::scoped_ptr<TestSameTypeMap_Map2Entry> entry;
    for (::google::protobuf::Map< ::google::protobuf::int32, ::google::protobuf::int32 >::const_iterator
        it = this->map2().begin();
        it != this->map2().end(); ++it) {
      if (entry.get() != NULL && entry->GetArena() != NULL) {
        entry.release();
      }
      entry.reset(map2_.NewEntryWrapper(it->first, it->second));
      total_size += ::google::protobuf::internal::WireFormatLite::
          MessageSizeNoVirtual(*entry);
    }
    if (entry.get() != NULL && entry->GetArena() != NULL) {
      entry.release();
    }
  }

  int cached_size = ::google::protobuf::internal::ToCachedSize(total_size);
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = cached_size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
  return total_size;
}

void TestSameTypeMap::MergeFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_merge_from_start:protobuf_unittest.TestSameTypeMap)
  if (GOOGLE_PREDICT_FALSE(&from == this)) MergeFromFail(__LINE__);
  const TestSameTypeMap* source =
      ::google::protobuf::internal::DynamicCastToGenerated<const TestSameTypeMap>(
          &from);
  if (source == NULL) {
  // @@protoc_insertion_point(generalized_merge_from_cast_fail:protobuf_unittest.TestSameTypeMap)
    ::google::protobuf::internal::ReflectionOps::Merge(from, this);
  } else {
  // @@protoc_insertion_point(generalized_merge_from_cast_success:protobuf_unittest.TestSameTypeMap)
    UnsafeMergeFrom(*source);
  }
}

void TestSameTypeMap::MergeFrom(const TestSameTypeMap& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:protobuf_unittest.TestSameTypeMap)
  if (GOOGLE_PREDICT_TRUE(&from != this)) {
    UnsafeMergeFrom(from);
  } else {
    MergeFromFail(__LINE__);
  }
}

void TestSameTypeMap::UnsafeMergeFrom(const TestSameTypeMap& from) {
  GOOGLE_DCHECK(&from != this);
  map1_.MergeFrom(from.map1_);
  map2_.MergeFrom(from.map2_);
}

void TestSameTypeMap::CopyFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_copy_from_start:protobuf_unittest.TestSameTypeMap)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

void TestSameTypeMap::CopyFrom(const TestSameTypeMap& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:protobuf_unittest.TestSameTypeMap)
  if (&from == this) return;
  Clear();
  UnsafeMergeFrom(from);
}

bool TestSameTypeMap::IsInitialized() const {

  return true;
}

void TestSameTypeMap::Swap(TestSameTypeMap* other) {
  if (other == this) return;
  if (GetArenaNoVirtual() == other->GetArenaNoVirtual()) {
    InternalSwap(other);
  } else {
    TestSameTypeMap temp;
    temp.UnsafeMergeFrom(*this);
    CopyFrom(*other);
    other->CopyFrom(temp);
  }
}
void TestSameTypeMap::UnsafeArenaSwap(TestSameTypeMap* other) {
  if (other == this) return;
  GOOGLE_DCHECK(GetArenaNoVirtual() == other->GetArenaNoVirtual());
  InternalSwap(other);
}
void TestSameTypeMap::InternalSwap(TestSameTypeMap* other) {
  map1_.Swap(&other->map1_);
  map2_.Swap(&other->map2_);
  _internal_metadata_.Swap(&other->_internal_metadata_);
  std::swap(_cached_size_, other->_cached_size_);
}

::google::protobuf::Metadata TestSameTypeMap::GetMetadata() const {
  protobuf_AssignDescriptorsOnce();
  ::google::protobuf::Metadata metadata;
  metadata.descriptor = TestSameTypeMap_descriptor_;
  metadata.reflection = TestSameTypeMap_reflection_;
  return metadata;
}

#if PROTOBUF_INLINE_NOT_IN_HEADERS
// TestSameTypeMap

// map<int32, int32> map1 = 1;
int TestSameTypeMap::map1_size() const {
  return map1_.size();
}
void TestSameTypeMap::clear_map1() {
  map1_.Clear();
}
 const ::google::protobuf::Map< ::google::protobuf::int32, ::google::protobuf::int32 >&
TestSameTypeMap::map1() const {
  // @@protoc_insertion_point(field_map:protobuf_unittest.TestSameTypeMap.map1)
  return map1_.GetMap();
}
 ::google::protobuf::Map< ::google::protobuf::int32, ::google::protobuf::int32 >*
TestSameTypeMap::mutable_map1() {
  // @@protoc_insertion_point(field_mutable_map:protobuf_unittest.TestSameTypeMap.map1)
  return map1_.MutableMap();
}

// map<int32, int32> map2 = 2;
int TestSameTypeMap::map2_size() const {
  return map2_.size();
}
void TestSameTypeMap::clear_map2() {
  map2_.Clear();
}
 const ::google::protobuf::Map< ::google::protobuf::int32, ::google::protobuf::int32 >&
TestSameTypeMap::map2() const {
  // @@protoc_insertion_point(field_map:protobuf_unittest.TestSameTypeMap.map2)
  return map2_.GetMap();
}
 ::google::protobuf::Map< ::google::protobuf::int32, ::google::protobuf::int32 >*
TestSameTypeMap::mutable_map2() {
  // @@protoc_insertion_point(field_mutable_map:protobuf_unittest.TestSameTypeMap.map2)
  return map2_.MutableMap();
}

inline const TestSameTypeMap* TestSameTypeMap::internal_default_instance() {
  return &TestSameTypeMap_default_instance_.get();
}
#endif  // PROTOBUF_INLINE_NOT_IN_HEADERS

// ===================================================================

#if !defined(_MSC_VER) || _MSC_VER >= 1900
const int TestRequiredMessageMap::kMapFieldFieldNumber;
#endif  // !defined(_MSC_VER) || _MSC_VER >= 1900

TestRequiredMessageMap::TestRequiredMessageMap()
  : ::google::protobuf::Message(), _internal_metadata_(NULL) {
  if (this != internal_default_instance()) protobuf_InitDefaults_google_2fprotobuf_2fmap_5funittest_2eproto();
  SharedCtor();
  // @@protoc_insertion_point(constructor:protobuf_unittest.TestRequiredMessageMap)
}
TestRequiredMessageMap::TestRequiredMessageMap(::google::protobuf::Arena* arena)
  : ::google::protobuf::Message(),
  _internal_metadata_(arena),
  map_field_(arena) {
#ifdef GOOGLE_PROTOBUF_NO_STATIC_INITIALIZER
  protobuf_InitDefaults_google_2fprotobuf_2fmap_5funittest_2eproto();
#endif  // GOOGLE_PROTOBUF_NO_STATIC_INITIALIZER
  SharedCtor();
  RegisterArenaDtor(arena);
  // @@protoc_insertion_point(arena_constructor:protobuf_unittest.TestRequiredMessageMap)
}

void TestRequiredMessageMap::InitAsDefaultInstance() {
}

TestRequiredMessageMap::TestRequiredMessageMap(const TestRequiredMessageMap& from)
  : ::google::protobuf::Message(),
    _internal_metadata_(NULL) {
  SharedCtor();
  UnsafeMergeFrom(from);
  // @@protoc_insertion_point(copy_constructor:protobuf_unittest.TestRequiredMessageMap)
}

void TestRequiredMessageMap::SharedCtor() {
  map_field_.SetAssignDescriptorCallback(
      protobuf_AssignDescriptorsOnce);
  map_field_.SetEntryDescriptor(
      &::protobuf_unittest::TestRequiredMessageMap_MapFieldEntry_descriptor_);
  _cached_size_ = 0;
}

TestRequiredMessageMap::~TestRequiredMessageMap() {
  // @@protoc_insertion_point(destructor:protobuf_unittest.TestRequiredMessageMap)
  SharedDtor();
}

void TestRequiredMessageMap::SharedDtor() {
  ::google::protobuf::Arena* arena = GetArenaNoVirtual();
  if (arena != NULL) {
    return;
  }

}

void TestRequiredMessageMap::ArenaDtor(void* object) {
  TestRequiredMessageMap* _this = reinterpret_cast< TestRequiredMessageMap* >(object);
  (void)_this;
}
void TestRequiredMessageMap::RegisterArenaDtor(::google::protobuf::Arena* arena) {
}
void TestRequiredMessageMap::SetCachedSize(int size) const {
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
}
const ::google::protobuf::Descriptor* TestRequiredMessageMap::descriptor() {
  protobuf_AssignDescriptorsOnce();
  return TestRequiredMessageMap_descriptor_;
}

const TestRequiredMessageMap& TestRequiredMessageMap::default_instance() {
  protobuf_InitDefaults_google_2fprotobuf_2fmap_5funittest_2eproto();
  return *internal_default_instance();
}

::google::protobuf::internal::ExplicitlyConstructed<TestRequiredMessageMap> TestRequiredMessageMap_default_instance_;

TestRequiredMessageMap* TestRequiredMessageMap::New(::google::protobuf::Arena* arena) const {
  return ::google::protobuf::Arena::CreateMessage<TestRequiredMessageMap>(arena);
}

void TestRequiredMessageMap::Clear() {
// @@protoc_insertion_point(message_clear_start:protobuf_unittest.TestRequiredMessageMap)
  map_field_.Clear();
}

bool TestRequiredMessageMap::MergePartialFromCodedStream(
    ::google::protobuf::io::CodedInputStream* input) {
#define DO_(EXPRESSION) if (!GOOGLE_PREDICT_TRUE(EXPRESSION)) goto failure
  ::google::protobuf::uint32 tag;
  // @@protoc_insertion_point(parse_start:protobuf_unittest.TestRequiredMessageMap)
  for (;;) {
    ::std::pair< ::google::protobuf::uint32, bool> p = input->ReadTagWithCutoff(127);
    tag = p.first;
    if (!p.second) goto handle_unusual;
    switch (::google::protobuf::internal::WireFormatLite::GetTagFieldNumber(tag)) {
      // map<int32, .protobuf_unittest.TestRequired> map_field = 1;
      case 1: {
        if (tag == 10) {
          DO_(input->IncrementRecursionDepth());
         parse_loop_map_field:
          TestRequiredMessageMap_MapFieldEntry::Parser< ::google::protobuf::internal::MapField<
              ::google::protobuf::int32, ::protobuf_unittest::TestRequired,
              ::google::protobuf::internal::WireFormatLite::TYPE_INT32,
              ::google::protobuf::internal::WireFormatLite::TYPE_MESSAGE,
              0 >,
            ::google::protobuf::Map< ::google::protobuf::int32, ::protobuf_unittest::TestRequired > > parser(&map_field_);
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessageNoVirtual(
              input, &parser));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(10)) goto parse_loop_map_field;
        input->UnsafeDecrementRecursionDepth();
        if (input->ExpectAtEnd()) goto success;
        break;
      }

      default: {
      handle_unusual:
        if (tag == 0 ||
            ::google::protobuf::internal::WireFormatLite::GetTagWireType(tag) ==
            ::google::protobuf::internal::WireFormatLite::WIRETYPE_END_GROUP) {
          goto success;
        }
        DO_(::google::protobuf::internal::WireFormatLite::SkipField(input, tag));
        break;
      }
    }
  }
success:
  // @@protoc_insertion_point(parse_success:protobuf_unittest.TestRequiredMessageMap)
  return true;
failure:
  // @@protoc_insertion_point(parse_failure:protobuf_unittest.TestRequiredMessageMap)
  return false;
#undef DO_
}

void TestRequiredMessageMap::SerializeWithCachedSizes(
    ::google::protobuf::io::CodedOutputStream* output) const {
  // @@protoc_insertion_point(serialize_start:protobuf_unittest.TestRequiredMessageMap)
  // map<int32, .protobuf_unittest.TestRequired> map_field = 1;
  if (!this->map_field().empty()) {
    typedef ::google::protobuf::Map< ::google::protobuf::int32, ::protobuf_unittest::TestRequired >::const_pointer
        ConstPtr;
    typedef ::google::protobuf::internal::SortItem< ::google::protobuf::int32, ConstPtr > SortItem;
    typedef ::google::protobuf::internal::CompareByFirstField<SortItem> Less;

    if (output->IsSerializationDeterminstic() &&
        this->map_field().size() > 1) {
      ::google::protobuf::scoped_array<SortItem> items(
          new SortItem[this->map_field().size()]);
      typedef ::google::protobuf::Map< ::google::protobuf::int32, ::protobuf_unittest::TestRequired >::size_type size_type;
      size_type n = 0;
      for (::google::protobuf::Map< ::google::protobuf::int32, ::protobuf_unittest::TestRequired >::const_iterator
          it = this->map_field().begin();
          it != this->map_field().end(); ++it, ++n) {
        items[n] = SortItem(&*it);
      }
      ::std::sort(&items[0], &items[n], Less());
      ::google::protobuf::scoped_ptr<TestRequiredMessageMap_MapFieldEntry> entry;
      for (size_type i = 0; i < n; i++) {
        entry.reset(map_field_.NewEntryWrapper(
            items[i].second->first, items[i].second->second));
        ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
            1, *entry, output);
        if (entry->GetArena() != NULL) {
          entry.release();
        }
      }
    } else {
      ::google::protobuf::scoped_ptr<TestRequiredMessageMap_MapFieldEntry> entry;
      for (::google::protobuf::Map< ::google::protobuf::int32, ::protobuf_unittest::TestRequired >::const_iterator
          it = this->map_field().begin();
          it != this->map_field().end(); ++it) {
        entry.reset(map_field_.NewEntryWrapper(
            it->first, it->second));
        ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
            1, *entry, output);
        if (entry->GetArena() != NULL) {
          entry.release();
        }
      }
    }
  }

  // @@protoc_insertion_point(serialize_end:protobuf_unittest.TestRequiredMessageMap)
}

::google::protobuf::uint8* TestRequiredMessageMap::InternalSerializeWithCachedSizesToArray(
    bool deterministic, ::google::protobuf::uint8* target) const {
  (void)deterministic; // Unused
  // @@protoc_insertion_point(serialize_to_array_start:protobuf_unittest.TestRequiredMessageMap)
  // map<int32, .protobuf_unittest.TestRequired> map_field = 1;
  if (!this->map_field().empty()) {
    typedef ::google::protobuf::Map< ::google::protobuf::int32, ::protobuf_unittest::TestRequired >::const_pointer
        ConstPtr;
    typedef ::google::protobuf::internal::SortItem< ::google::protobuf::int32, ConstPtr > SortItem;
    typedef ::google::protobuf::internal::CompareByFirstField<SortItem> Less;

    if (deterministic &&
        this->map_field().size() > 1) {
      ::google::protobuf::scoped_array<SortItem> items(
          new SortItem[this->map_field().size()]);
      typedef ::google::protobuf::Map< ::google::protobuf::int32, ::protobuf_unittest::TestRequired >::size_type size_type;
      size_type n = 0;
      for (::google::protobuf::Map< ::google::protobuf::int32, ::protobuf_unittest::TestRequired >::const_iterator
          it = this->map_field().begin();
          it != this->map_field().end(); ++it, ++n) {
        items[n] = SortItem(&*it);
      }
      ::std::sort(&items[0], &items[n], Less());
      ::google::protobuf::scoped_ptr<TestRequiredMessageMap_MapFieldEntry> entry;
      for (size_type i = 0; i < n; i++) {
        entry.reset(map_field_.NewEntryWrapper(
            items[i].second->first, items[i].second->second));
        target = ::google::protobuf::internal::WireFormatLite::
                   InternalWriteMessageNoVirtualToArray(
                       1, *entry, deterministic, target);
;
        if (entry->GetArena() != NULL) {
          entry.release();
        }
      }
    } else {
      ::google::protobuf::scoped_ptr<TestRequiredMessageMap_MapFieldEntry> entry;
      for (::google::protobuf::Map< ::google::protobuf::int32, ::protobuf_unittest::TestRequired >::const_iterator
          it = this->map_field().begin();
          it != this->map_field().end(); ++it) {
        entry.reset(map_field_.NewEntryWrapper(
            it->first, it->second));
        target = ::google::protobuf::internal::WireFormatLite::
                   InternalWriteMessageNoVirtualToArray(
                       1, *entry, deterministic, target);
;
        if (entry->GetArena() != NULL) {
          entry.release();
        }
      }
    }
  }

  // @@protoc_insertion_point(serialize_to_array_end:protobuf_unittest.TestRequiredMessageMap)
  return target;
}

size_t TestRequiredMessageMap::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:protobuf_unittest.TestRequiredMessageMap)
  size_t total_size = 0;

  // map<int32, .protobuf_unittest.TestRequired> map_field = 1;
  total_size += 1 *
      ::google::protobuf::internal::FromIntSize(this->map_field_size());
  {
    ::google::protobuf::scoped_ptr<TestRequiredMessageMap_MapFieldEntry> entry;
    for (::google::protobuf::Map< ::google::protobuf::int32, ::protobuf_unittest::TestRequired >::const_iterator
        it = this->map_field().begin();
        it != this->map_field().end(); ++it) {
      if (entry.get() != NULL && entry->GetArena() != NULL) {
        entry.release();
      }
      entry.reset(map_field_.NewEntryWrapper(it->first, it->second));
      total_size += ::google::protobuf::internal::WireFormatLite::
          MessageSizeNoVirtual(*entry);
    }
    if (entry.get() != NULL && entry->GetArena() != NULL) {
      entry.release();
    }
  }

  int cached_size = ::google::protobuf::internal::ToCachedSize(total_size);
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = cached_size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
  return total_size;
}

void TestRequiredMessageMap::MergeFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_merge_from_start:protobuf_unittest.TestRequiredMessageMap)
  if (GOOGLE_PREDICT_FALSE(&from == this)) MergeFromFail(__LINE__);
  const TestRequiredMessageMap* source =
      ::google::protobuf::internal::DynamicCastToGenerated<const TestRequiredMessageMap>(
          &from);
  if (source == NULL) {
  // @@protoc_insertion_point(generalized_merge_from_cast_fail:protobuf_unittest.TestRequiredMessageMap)
    ::google::protobuf::internal::ReflectionOps::Merge(from, this);
  } else {
  // @@protoc_insertion_point(generalized_merge_from_cast_success:protobuf_unittest.TestRequiredMessageMap)
    UnsafeMergeFrom(*source);
  }
}

void TestRequiredMessageMap::MergeFrom(const TestRequiredMessageMap& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:protobuf_unittest.TestRequiredMessageMap)
  if (GOOGLE_PREDICT_TRUE(&from != this)) {
    UnsafeMergeFrom(from);
  } else {
    MergeFromFail(__LINE__);
  }
}

void TestRequiredMessageMap::UnsafeMergeFrom(const TestRequiredMessageMap& from) {
  GOOGLE_DCHECK(&from != this);
  map_field_.MergeFrom(from.map_field_);
}

void TestRequiredMessageMap::CopyFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_copy_from_start:protobuf_unittest.TestRequiredMessageMap)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

void TestRequiredMessageMap::CopyFrom(const TestRequiredMessageMap& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:protobuf_unittest.TestRequiredMessageMap)
  if (&from == this) return;
  Clear();
  UnsafeMergeFrom(from);
}

bool TestRequiredMessageMap::IsInitialized() const {

  if (!::google::protobuf::internal::AllAreInitialized(this->map_field())) return false;
  return true;
}

void TestRequiredMessageMap::Swap(TestRequiredMessageMap* other) {
  if (other == this) return;
  if (GetArenaNoVirtual() == other->GetArenaNoVirtual()) {
    InternalSwap(other);
  } else {
    TestRequiredMessageMap temp;
    temp.UnsafeMergeFrom(*this);
    CopyFrom(*other);
    other->CopyFrom(temp);
  }
}
void TestRequiredMessageMap::UnsafeArenaSwap(TestRequiredMessageMap* other) {
  if (other == this) return;
  GOOGLE_DCHECK(GetArenaNoVirtual() == other->GetArenaNoVirtual());
  InternalSwap(other);
}
void TestRequiredMessageMap::InternalSwap(TestRequiredMessageMap* other) {
  map_field_.Swap(&other->map_field_);
  _internal_metadata_.Swap(&other->_internal_metadata_);
  std::swap(_cached_size_, other->_cached_size_);
}

::google::protobuf::Metadata TestRequiredMessageMap::GetMetadata() const {
  protobuf_AssignDescriptorsOnce();
  ::google::protobuf::Metadata metadata;
  metadata.descriptor = TestRequiredMessageMap_descriptor_;
  metadata.reflection = TestRequiredMessageMap_reflection_;
  return metadata;
}

#if PROTOBUF_INLINE_NOT_IN_HEADERS
// TestRequiredMessageMap

// map<int32, .protobuf_unittest.TestRequired> map_field = 1;
int TestRequiredMessageMap::map_field_size() const {
  return map_field_.size();
}
void TestRequiredMessageMap::clear_map_field() {
  map_field_.Clear();
}
 const ::google::protobuf::Map< ::google::protobuf::int32, ::protobuf_unittest::TestRequired >&
TestRequiredMessageMap::map_field() const {
  // @@protoc_insertion_point(field_map:protobuf_unittest.TestRequiredMessageMap.map_field)
  return map_field_.GetMap();
}
 ::google::protobuf::Map< ::google::protobuf::int32, ::protobuf_unittest::TestRequired >*
TestRequiredMessageMap::mutable_map_field() {
  // @@protoc_insertion_point(field_mutable_map:protobuf_unittest.TestRequiredMessageMap.map_field)
  return map_field_.MutableMap();
}

inline const TestRequiredMessageMap* TestRequiredMessageMap::internal_default_instance() {
  return &TestRequiredMessageMap_default_instance_.get();
}
#endif  // PROTOBUF_INLINE_NOT_IN_HEADERS

// ===================================================================

#if !defined(_MSC_VER) || _MSC_VER >= 1900
const int TestArenaMap::kMapInt32Int32FieldNumber;
const int TestArenaMap::kMapInt64Int64FieldNumber;
const int TestArenaMap::kMapUint32Uint32FieldNumber;
const int TestArenaMap::kMapUint64Uint64FieldNumber;
const int TestArenaMap::kMapSint32Sint32FieldNumber;
const int TestArenaMap::kMapSint64Sint64FieldNumber;
const int TestArenaMap::kMapFixed32Fixed32FieldNumber;
const int TestArenaMap::kMapFixed64Fixed64FieldNumber;
const int TestArenaMap::kMapSfixed32Sfixed32FieldNumber;
const int TestArenaMap::kMapSfixed64Sfixed64FieldNumber;
const int TestArenaMap::kMapInt32FloatFieldNumber;
const int TestArenaMap::kMapInt32DoubleFieldNumber;
const int TestArenaMap::kMapBoolBoolFieldNumber;
const int TestArenaMap::kMapStringStringFieldNumber;
const int TestArenaMap::kMapInt32BytesFieldNumber;
const int TestArenaMap::kMapInt32EnumFieldNumber;
const int TestArenaMap::kMapInt32ForeignMessageFieldNumber;
const int TestArenaMap::kMapInt32ForeignMessageNoArenaFieldNumber;
#endif  // !defined(_MSC_VER) || _MSC_VER >= 1900

TestArenaMap::TestArenaMap()
  : ::google::protobuf::Message(), _internal_metadata_(NULL) {
  if (this != internal_default_instance()) protobuf_InitDefaults_google_2fprotobuf_2fmap_5funittest_2eproto();
  SharedCtor();
  // @@protoc_insertion_point(constructor:protobuf_unittest.TestArenaMap)
}
TestArenaMap::TestArenaMap(::google::protobuf::Arena* arena)
  : ::google::protobuf::Message(),
  _internal_metadata_(arena),
  map_int32_int32_(arena),
  map_int64_int64_(arena),
  map_uint32_uint32_(arena),
  map_uint64_uint64_(arena),
  map_sint32_sint32_(arena),
  map_sint64_sint64_(arena),
  map_fixed32_fixed32_(arena),
  map_fixed64_fixed64_(arena),
  map_sfixed32_sfixed32_(arena),
  map_sfixed64_sfixed64_(arena),
  map_int32_float_(arena),
  map_int32_double_(arena),
  map_bool_bool_(arena),
  map_string_string_(arena),
  map_int32_bytes_(arena),
  map_int32_enum_(arena),
  map_int32_foreign_message_(arena),
  map_int32_foreign_message_no_arena_(arena) {
#ifdef GOOGLE_PROTOBUF_NO_STATIC_INITIALIZER
  protobuf_InitDefaults_google_2fprotobuf_2fmap_5funittest_2eproto();
#endif  // GOOGLE_PROTOBUF_NO_STATIC_INITIALIZER
  SharedCtor();
  RegisterArenaDtor(arena);
  // @@protoc_insertion_point(arena_constructor:protobuf_unittest.TestArenaMap)
}

void TestArenaMap::InitAsDefaultInstance() {
}

TestArenaMap::TestArenaMap(const TestArenaMap& from)
  : ::google::protobuf::Message(),
    _internal_metadata_(NULL) {
  SharedCtor();
  UnsafeMergeFrom(from);
  // @@protoc_insertion_point(copy_constructor:protobuf_unittest.TestArenaMap)
}

void TestArenaMap::SharedCtor() {
  map_int32_int32_.SetAssignDescriptorCallback(
      protobuf_AssignDescriptorsOnce);
  map_int32_int32_.SetEntryDescriptor(
      &::protobuf_unittest::TestArenaMap_MapInt32Int32Entry_descriptor_);
  map_int64_int64_.SetAssignDescriptorCallback(
      protobuf_AssignDescriptorsOnce);
  map_int64_int64_.SetEntryDescriptor(
      &::protobuf_unittest::TestArenaMap_MapInt64Int64Entry_descriptor_);
  map_uint32_uint32_.SetAssignDescriptorCallback(
      protobuf_AssignDescriptorsOnce);
  map_uint32_uint32_.SetEntryDescriptor(
      &::protobuf_unittest::TestArenaMap_MapUint32Uint32Entry_descriptor_);
  map_uint64_uint64_.SetAssignDescriptorCallback(
      protobuf_AssignDescriptorsOnce);
  map_uint64_uint64_.SetEntryDescriptor(
      &::protobuf_unittest::TestArenaMap_MapUint64Uint64Entry_descriptor_);
  map_sint32_sint32_.SetAssignDescriptorCallback(
      protobuf_AssignDescriptorsOnce);
  map_sint32_sint32_.SetEntryDescriptor(
      &::protobuf_unittest::TestArenaMap_MapSint32Sint32Entry_descriptor_);
  map_sint64_sint64_.SetAssignDescriptorCallback(
      protobuf_AssignDescriptorsOnce);
  map_sint64_sint64_.SetEntryDescriptor(
      &::protobuf_unittest::TestArenaMap_MapSint64Sint64Entry_descriptor_);
  map_fixed32_fixed32_.SetAssignDescriptorCallback(
      protobuf_AssignDescriptorsOnce);
  map_fixed32_fixed32_.SetEntryDescriptor(
      &::protobuf_unittest::TestArenaMap_MapFixed32Fixed32Entry_descriptor_);
  map_fixed64_fixed64_.SetAssignDescriptorCallback(
      protobuf_AssignDescriptorsOnce);
  map_fixed64_fixed64_.SetEntryDescriptor(
      &::protobuf_unittest::TestArenaMap_MapFixed64Fixed64Entry_descriptor_);
  map_sfixed32_sfixed32_.SetAssignDescriptorCallback(
      protobuf_AssignDescriptorsOnce);
  map_sfixed32_sfixed32_.SetEntryDescriptor(
      &::protobuf_unittest::TestArenaMap_MapSfixed32Sfixed32Entry_descriptor_);
  map_sfixed64_sfixed64_.SetAssignDescriptorCallback(
      protobuf_AssignDescriptorsOnce);
  map_sfixed64_sfixed64_.SetEntryDescriptor(
      &::protobuf_unittest::TestArenaMap_MapSfixed64Sfixed64Entry_descriptor_);
  map_int32_float_.SetAssignDescriptorCallback(
      protobuf_AssignDescriptorsOnce);
  map_int32_float_.SetEntryDescriptor(
      &::protobuf_unittest::TestArenaMap_MapInt32FloatEntry_descriptor_);
  map_int32_double_.SetAssignDescriptorCallback(
      protobuf_AssignDescriptorsOnce);
  map_int32_double_.SetEntryDescriptor(
      &::protobuf_unittest::TestArenaMap_MapInt32DoubleEntry_descriptor_);
  map_bool_bool_.SetAssignDescriptorCallback(
      protobuf_AssignDescriptorsOnce);
  map_bool_bool_.SetEntryDescriptor(
      &::protobuf_unittest::TestArenaMap_MapBoolBoolEntry_descriptor_);
  map_string_string_.SetAssignDescriptorCallback(
      protobuf_AssignDescriptorsOnce);
  map_string_string_.SetEntryDescriptor(
      &::protobuf_unittest::TestArenaMap_MapStringStringEntry_descriptor_);
  map_int32_bytes_.SetAssignDescriptorCallback(
      protobuf_AssignDescriptorsOnce);
  map_int32_bytes_.SetEntryDescriptor(
      &::protobuf_unittest::TestArenaMap_MapInt32BytesEntry_descriptor_);
  map_int32_enum_.SetAssignDescriptorCallback(
      protobuf_AssignDescriptorsOnce);
  map_int32_enum_.SetEntryDescriptor(
      &::protobuf_unittest::TestArenaMap_MapInt32EnumEntry_descriptor_);
  map_int32_foreign_message_.SetAssignDescriptorCallback(
      protobuf_AssignDescriptorsOnce);
  map_int32_foreign_message_.SetEntryDescriptor(
      &::protobuf_unittest::TestArenaMap_MapInt32ForeignMessageEntry_descriptor_);
  map_int32_foreign_message_no_arena_.SetAssignDescriptorCallback(
      protobuf_AssignDescriptorsOnce);
  map_int32_foreign_message_no_arena_.SetEntryDescriptor(
      &::protobuf_unittest::TestArenaMap_MapInt32ForeignMessageNoArenaEntry_descriptor_);
  _cached_size_ = 0;
}

TestArenaMap::~TestArenaMap() {
  // @@protoc_insertion_point(destructor:protobuf_unittest.TestArenaMap)
  SharedDtor();
}

void TestArenaMap::SharedDtor() {
  ::google::protobuf::Arena* arena = GetArenaNoVirtual();
  if (arena != NULL) {
    return;
  }

}

void TestArenaMap::ArenaDtor(void* object) {
  TestArenaMap* _this = reinterpret_cast< TestArenaMap* >(object);
  (void)_this;
}
void TestArenaMap::RegisterArenaDtor(::google::protobuf::Arena* arena) {
}
void TestArenaMap::SetCachedSize(int size) const {
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
}
const ::google::protobuf::Descriptor* TestArenaMap::descriptor() {
  protobuf_AssignDescriptorsOnce();
  return TestArenaMap_descriptor_;
}

const TestArenaMap& TestArenaMap::default_instance() {
  protobuf_InitDefaults_google_2fprotobuf_2fmap_5funittest_2eproto();
  return *internal_default_instance();
}

::google::protobuf::internal::ExplicitlyConstructed<TestArenaMap> TestArenaMap_default_instance_;

TestArenaMap* TestArenaMap::New(::google::protobuf::Arena* arena) const {
  return ::google::protobuf::Arena::CreateMessage<TestArenaMap>(arena);
}

void TestArenaMap::Clear() {
// @@protoc_insertion_point(message_clear_start:protobuf_unittest.TestArenaMap)
  map_int32_int32_.Clear();
  map_int64_int64_.Clear();
  map_uint32_uint32_.Clear();
  map_uint64_uint64_.Clear();
  map_sint32_sint32_.Clear();
  map_sint64_sint64_.Clear();
  map_fixed32_fixed32_.Clear();
  map_fixed64_fixed64_.Clear();
  map_sfixed32_sfixed32_.Clear();
  map_sfixed64_sfixed64_.Clear();
  map_int32_float_.Clear();
  map_int32_double_.Clear();
  map_bool_bool_.Clear();
  map_string_string_.Clear();
  map_int32_bytes_.Clear();
  map_int32_enum_.Clear();
  map_int32_foreign_message_.Clear();
  map_int32_foreign_message_no_arena_.Clear();
}

bool TestArenaMap::MergePartialFromCodedStream(
    ::google::protobuf::io::CodedInputStream* input) {
#define DO_(EXPRESSION) if (!GOOGLE_PREDICT_TRUE(EXPRESSION)) goto failure
  ::google::protobuf::uint32 tag;
  // @@protoc_insertion_point(parse_start:protobuf_unittest.TestArenaMap)
  for (;;) {
    ::std::pair< ::google::protobuf::uint32, bool> p = input->ReadTagWithCutoff(16383);
    tag = p.first;
    if (!p.second) goto handle_unusual;
    switch (::google::protobuf::internal::WireFormatLite::GetTagFieldNumber(tag)) {
      // map<int32, int32> map_int32_int32 = 1;
      case 1: {
        if (tag == 10) {
          DO_(input->IncrementRecursionDepth());
         parse_loop_map_int32_int32:
          TestArenaMap_MapInt32Int32Entry::Parser< ::google::protobuf::internal::MapField<
              ::google::protobuf::int32, ::google::protobuf::int32,
              ::google::protobuf::internal::WireFormatLite::TYPE_INT32,
              ::google::protobuf::internal::WireFormatLite::TYPE_INT32,
              0 >,
            ::google::protobuf::Map< ::google::protobuf::int32, ::google::protobuf::int32 > > parser(&map_int32_int32_);
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessageNoVirtual(
              input, &parser));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(10)) goto parse_loop_map_int32_int32;
        if (input->ExpectTag(18)) goto parse_loop_map_int64_int64;
        input->UnsafeDecrementRecursionDepth();
        break;
      }

      // map<int64, int64> map_int64_int64 = 2;
      case 2: {
        if (tag == 18) {
          DO_(input->IncrementRecursionDepth());
         parse_loop_map_int64_int64:
          TestArenaMap_MapInt64Int64Entry::Parser< ::google::protobuf::internal::MapField<
              ::google::protobuf::int64, ::google::protobuf::int64,
              ::google::protobuf::internal::WireFormatLite::TYPE_INT64,
              ::google::protobuf::internal::WireFormatLite::TYPE_INT64,
              0 >,
            ::google::protobuf::Map< ::google::protobuf::int64, ::google::protobuf::int64 > > parser(&map_int64_int64_);
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessageNoVirtual(
              input, &parser));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(18)) goto parse_loop_map_int64_int64;
        if (input->ExpectTag(26)) goto parse_loop_map_uint32_uint32;
        input->UnsafeDecrementRecursionDepth();
        break;
      }

      // map<uint32, uint32> map_uint32_uint32 = 3;
      case 3: {
        if (tag == 26) {
          DO_(input->IncrementRecursionDepth());
         parse_loop_map_uint32_uint32:
          TestArenaMap_MapUint32Uint32Entry::Parser< ::google::protobuf::internal::MapField<
              ::google::protobuf::uint32, ::google::protobuf::uint32,
              ::google::protobuf::internal::WireFormatLite::TYPE_UINT32,
              ::google::protobuf::internal::WireFormatLite::TYPE_UINT32,
              0 >,
            ::google::protobuf::Map< ::google::protobuf::uint32, ::google::protobuf::uint32 > > parser(&map_uint32_uint32_);
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessageNoVirtual(
              input, &parser));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(26)) goto parse_loop_map_uint32_uint32;
        if (input->ExpectTag(34)) goto parse_loop_map_uint64_uint64;
        input->UnsafeDecrementRecursionDepth();
        break;
      }

      // map<uint64, uint64> map_uint64_uint64 = 4;
      case 4: {
        if (tag == 34) {
          DO_(input->IncrementRecursionDepth());
         parse_loop_map_uint64_uint64:
          TestArenaMap_MapUint64Uint64Entry::Parser< ::google::protobuf::internal::MapField<
              ::google::protobuf::uint64, ::google::protobuf::uint64,
              ::google::protobuf::internal::WireFormatLite::TYPE_UINT64,
              ::google::protobuf::internal::WireFormatLite::TYPE_UINT64,
              0 >,
            ::google::protobuf::Map< ::google::protobuf::uint64, ::google::protobuf::uint64 > > parser(&map_uint64_uint64_);
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessageNoVirtual(
              input, &parser));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(34)) goto parse_loop_map_uint64_uint64;
        if (input->ExpectTag(42)) goto parse_loop_map_sint32_sint32;
        input->UnsafeDecrementRecursionDepth();
        break;
      }

      // map<sint32, sint32> map_sint32_sint32 = 5;
      case 5: {
        if (tag == 42) {
          DO_(input->IncrementRecursionDepth());
         parse_loop_map_sint32_sint32:
          TestArenaMap_MapSint32Sint32Entry::Parser< ::google::protobuf::internal::MapField<
              ::google::protobuf::int32, ::google::protobuf::int32,
              ::google::protobuf::internal::WireFormatLite::TYPE_SINT32,
              ::google::protobuf::internal::WireFormatLite::TYPE_SINT32,
              0 >,
            ::google::protobuf::Map< ::google::protobuf::int32, ::google::protobuf::int32 > > parser(&map_sint32_sint32_);
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessageNoVirtual(
              input, &parser));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(42)) goto parse_loop_map_sint32_sint32;
        if (input->ExpectTag(50)) goto parse_loop_map_sint64_sint64;
        input->UnsafeDecrementRecursionDepth();
        break;
      }

      // map<sint64, sint64> map_sint64_sint64 = 6;
      case 6: {
        if (tag == 50) {
          DO_(input->IncrementRecursionDepth());
         parse_loop_map_sint64_sint64:
          TestArenaMap_MapSint64Sint64Entry::Parser< ::google::protobuf::internal::MapField<
              ::google::protobuf::int64, ::google::protobuf::int64,
              ::google::protobuf::internal::WireFormatLite::TYPE_SINT64,
              ::google::protobuf::internal::WireFormatLite::TYPE_SINT64,
              0 >,
            ::google::protobuf::Map< ::google::protobuf::int64, ::google::protobuf::int64 > > parser(&map_sint64_sint64_);
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessageNoVirtual(
              input, &parser));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(50)) goto parse_loop_map_sint64_sint64;
        if (input->ExpectTag(58)) goto parse_loop_map_fixed32_fixed32;
        input->UnsafeDecrementRecursionDepth();
        break;
      }

      // map<fixed32, fixed32> map_fixed32_fixed32 = 7;
      case 7: {
        if (tag == 58) {
          DO_(input->IncrementRecursionDepth());
         parse_loop_map_fixed32_fixed32:
          TestArenaMap_MapFixed32Fixed32Entry::Parser< ::google::protobuf::internal::MapField<
              ::google::protobuf::uint32, ::google::protobuf::uint32,
              ::google::protobuf::internal::WireFormatLite::TYPE_FIXED32,
              ::google::protobuf::internal::WireFormatLite::TYPE_FIXED32,
              0 >,
            ::google::protobuf::Map< ::google::protobuf::uint32, ::google::protobuf::uint32 > > parser(&map_fixed32_fixed32_);
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessageNoVirtual(
              input, &parser));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(58)) goto parse_loop_map_fixed32_fixed32;
        if (input->ExpectTag(66)) goto parse_loop_map_fixed64_fixed64;
        input->UnsafeDecrementRecursionDepth();
        break;
      }

      // map<fixed64, fixed64> map_fixed64_fixed64 = 8;
      case 8: {
        if (tag == 66) {
          DO_(input->IncrementRecursionDepth());
         parse_loop_map_fixed64_fixed64:
          TestArenaMap_MapFixed64Fixed64Entry::Parser< ::google::protobuf::internal::MapField<
              ::google::protobuf::uint64, ::google::protobuf::uint64,
              ::google::protobuf::internal::WireFormatLite::TYPE_FIXED64,
              ::google::protobuf::internal::WireFormatLite::TYPE_FIXED64,
              0 >,
            ::google::protobuf::Map< ::google::protobuf::uint64, ::google::protobuf::uint64 > > parser(&map_fixed64_fixed64_);
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessageNoVirtual(
              input, &parser));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(66)) goto parse_loop_map_fixed64_fixed64;
        if (input->ExpectTag(74)) goto parse_loop_map_sfixed32_sfixed32;
        input->UnsafeDecrementRecursionDepth();
        break;
      }

      // map<sfixed32, sfixed32> map_sfixed32_sfixed32 = 9;
      case 9: {
        if (tag == 74) {
          DO_(input->IncrementRecursionDepth());
         parse_loop_map_sfixed32_sfixed32:
          TestArenaMap_MapSfixed32Sfixed32Entry::Parser< ::google::protobuf::internal::MapField<
              ::google::protobuf::int32, ::google::protobuf::int32,
              ::google::protobuf::internal::WireFormatLite::TYPE_SFIXED32,
              ::google::protobuf::internal::WireFormatLite::TYPE_SFIXED32,
              0 >,
            ::google::protobuf::Map< ::google::protobuf::int32, ::google::protobuf::int32 > > parser(&map_sfixed32_sfixed32_);
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessageNoVirtual(
              input, &parser));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(74)) goto parse_loop_map_sfixed32_sfixed32;
        if (input->ExpectTag(82)) goto parse_loop_map_sfixed64_sfixed64;
        input->UnsafeDecrementRecursionDepth();
        break;
      }

      // map<sfixed64, sfixed64> map_sfixed64_sfixed64 = 10;
      case 10: {
        if (tag == 82) {
          DO_(input->IncrementRecursionDepth());
         parse_loop_map_sfixed64_sfixed64:
          TestArenaMap_MapSfixed64Sfixed64Entry::Parser< ::google::protobuf::internal::MapField<
              ::google::protobuf::int64, ::google::protobuf::int64,
              ::google::protobuf::internal::WireFormatLite::TYPE_SFIXED64,
              ::google::protobuf::internal::WireFormatLite::TYPE_SFIXED64,
              0 >,
            ::google::protobuf::Map< ::google::protobuf::int64, ::google::protobuf::int64 > > parser(&map_sfixed64_sfixed64_);
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessageNoVirtual(
              input, &parser));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(82)) goto parse_loop_map_sfixed64_sfixed64;
        if (input->ExpectTag(90)) goto parse_loop_map_int32_float;
        input->UnsafeDecrementRecursionDepth();
        break;
      }

      // map<int32, float> map_int32_float = 11;
      case 11: {
        if (tag == 90) {
          DO_(input->IncrementRecursionDepth());
         parse_loop_map_int32_float:
          TestArenaMap_MapInt32FloatEntry::Parser< ::google::protobuf::internal::MapField<
              ::google::protobuf::int32, float,
              ::google::protobuf::internal::WireFormatLite::TYPE_INT32,
              ::google::protobuf::internal::WireFormatLite::TYPE_FLOAT,
              0 >,
            ::google::protobuf::Map< ::google::protobuf::int32, float > > parser(&map_int32_float_);
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessageNoVirtual(
              input, &parser));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(90)) goto parse_loop_map_int32_float;
        if (input->ExpectTag(98)) goto parse_loop_map_int32_double;
        input->UnsafeDecrementRecursionDepth();
        break;
      }

      // map<int32, double> map_int32_double = 12;
      case 12: {
        if (tag == 98) {
          DO_(input->IncrementRecursionDepth());
         parse_loop_map_int32_double:
          TestArenaMap_MapInt32DoubleEntry::Parser< ::google::protobuf::internal::MapField<
              ::google::protobuf::int32, double,
              ::google::protobuf::internal::WireFormatLite::TYPE_INT32,
              ::google::protobuf::internal::WireFormatLite::TYPE_DOUBLE,
              0 >,
            ::google::protobuf::Map< ::google::protobuf::int32, double > > parser(&map_int32_double_);
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessageNoVirtual(
              input, &parser));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(98)) goto parse_loop_map_int32_double;
        if (input->ExpectTag(106)) goto parse_loop_map_bool_bool;
        input->UnsafeDecrementRecursionDepth();
        break;
      }

      // map<bool, bool> map_bool_bool = 13;
      case 13: {
        if (tag == 106) {
          DO_(input->IncrementRecursionDepth());
         parse_loop_map_bool_bool:
          TestArenaMap_MapBoolBoolEntry::Parser< ::google::protobuf::internal::MapField<
              bool, bool,
              ::google::protobuf::internal::WireFormatLite::TYPE_BOOL,
              ::google::protobuf::internal::WireFormatLite::TYPE_BOOL,
              0 >,
            ::google::protobuf::Map< bool, bool > > parser(&map_bool_bool_);
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessageNoVirtual(
              input, &parser));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(106)) goto parse_loop_map_bool_bool;
        if (input->ExpectTag(114)) goto parse_loop_map_string_string;
        input->UnsafeDecrementRecursionDepth();
        break;
      }

      // map<string, string> map_string_string = 14;
      case 14: {
        if (tag == 114) {
          DO_(input->IncrementRecursionDepth());
         parse_loop_map_string_string:
          TestArenaMap_MapStringStringEntry::Parser< ::google::protobuf::internal::MapField<
              ::std::string, ::std::string,
              ::google::protobuf::internal::WireFormatLite::TYPE_STRING,
              ::google::protobuf::internal::WireFormatLite::TYPE_STRING,
              0 >,
            ::google::protobuf::Map< ::std::string, ::std::string > > parser(&map_string_string_);
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessageNoVirtual(
              input, &parser));
          DO_(::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
            parser.key().data(), parser.key().length(),
            ::google::protobuf::internal::WireFormatLite::PARSE,
            "protobuf_unittest.TestArenaMap.MapStringStringEntry.key"));
          DO_(::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
            parser.value().data(), parser.value().length(),
            ::google::protobuf::internal::WireFormatLite::PARSE,
            "protobuf_unittest.TestArenaMap.MapStringStringEntry.value"));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(114)) goto parse_loop_map_string_string;
        if (input->ExpectTag(122)) goto parse_loop_map_int32_bytes;
        input->UnsafeDecrementRecursionDepth();
        break;
      }

      // map<int32, bytes> map_int32_bytes = 15;
      case 15: {
        if (tag == 122) {
          DO_(input->IncrementRecursionDepth());
         parse_loop_map_int32_bytes:
          TestArenaMap_MapInt32BytesEntry::Parser< ::google::protobuf::internal::MapField<
              ::google::protobuf::int32, ::std::string,
              ::google::protobuf::internal::WireFormatLite::TYPE_INT32,
              ::google::protobuf::internal::WireFormatLite::TYPE_BYTES,
              0 >,
            ::google::protobuf::Map< ::google::protobuf::int32, ::std::string > > parser(&map_int32_bytes_);
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessageNoVirtual(
              input, &parser));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(122)) goto parse_loop_map_int32_bytes;
        if (input->ExpectTag(130)) goto parse_loop_map_int32_enum;
        input->UnsafeDecrementRecursionDepth();
        break;
      }

      // map<int32, .protobuf_unittest.MapEnum> map_int32_enum = 16;
      case 16: {
        if (tag == 130) {
          DO_(input->IncrementRecursionDepth());
         parse_loop_map_int32_enum:
          TestArenaMap_MapInt32EnumEntry::Parser< ::google::protobuf::internal::MapField<
              ::google::protobuf::int32, ::protobuf_unittest::MapEnum,
              ::google::protobuf::internal::WireFormatLite::TYPE_INT32,
              ::google::protobuf::internal::WireFormatLite::TYPE_ENUM,
              0 >,
            ::google::protobuf::Map< ::google::protobuf::int32, ::protobuf_unittest::MapEnum > > parser(&map_int32_enum_);
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessageNoVirtual(
              input, &parser));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(130)) goto parse_loop_map_int32_enum;
        if (input->ExpectTag(138)) goto parse_loop_map_int32_foreign_message;
        input->UnsafeDecrementRecursionDepth();
        break;
      }

      // map<int32, .protobuf_unittest.ForeignMessage> map_int32_foreign_message = 17;
      case 17: {
        if (tag == 138) {
          DO_(input->IncrementRecursionDepth());
         parse_loop_map_int32_foreign_message:
          TestArenaMap_MapInt32ForeignMessageEntry::Parser< ::google::protobuf::internal::MapField<
              ::google::protobuf::int32, ::protobuf_unittest::ForeignMessage,
              ::google::protobuf::internal::WireFormatLite::TYPE_INT32,
              ::google::protobuf::internal::WireFormatLite::TYPE_MESSAGE,
              0 >,
            ::google::protobuf::Map< ::google::protobuf::int32, ::protobuf_unittest::ForeignMessage > > parser(&map_int32_foreign_message_);
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessageNoVirtual(
              input, &parser));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(138)) goto parse_loop_map_int32_foreign_message;
        if (input->ExpectTag(146)) goto parse_loop_map_int32_foreign_message_no_arena;
        input->UnsafeDecrementRecursionDepth();
        break;
      }

      // map<int32, .protobuf_unittest_no_arena.ForeignMessage> map_int32_foreign_message_no_arena = 18;
      case 18: {
        if (tag == 146) {
          DO_(input->IncrementRecursionDepth());
         parse_loop_map_int32_foreign_message_no_arena:
          TestArenaMap_MapInt32ForeignMessageNoArenaEntry::Parser< ::google::protobuf::internal::MapField<
              ::google::protobuf::int32, ::protobuf_unittest_no_arena::ForeignMessage,
              ::google::protobuf::internal::WireFormatLite::TYPE_INT32,
              ::google::protobuf::internal::WireFormatLite::TYPE_MESSAGE,
              0 >,
            ::google::protobuf::Map< ::google::protobuf::int32, ::protobuf_unittest_no_arena::ForeignMessage > > parser(&map_int32_foreign_message_no_arena_);
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessageNoVirtual(
              input, &parser));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(146)) goto parse_loop_map_int32_foreign_message_no_arena;
        input->UnsafeDecrementRecursionDepth();
        if (input->ExpectAtEnd()) goto success;
        break;
      }

      default: {
      handle_unusual:
        if (tag == 0 ||
            ::google::protobuf::internal::WireFormatLite::GetTagWireType(tag) ==
            ::google::protobuf::internal::WireFormatLite::WIRETYPE_END_GROUP) {
          goto success;
        }
        DO_(::google::protobuf::internal::WireFormatLite::SkipField(input, tag));
        break;
      }
    }
  }
success:
  // @@protoc_insertion_point(parse_success:protobuf_unittest.TestArenaMap)
  return true;
failure:
  // @@protoc_insertion_point(parse_failure:protobuf_unittest.TestArenaMap)
  return false;
#undef DO_
}

void TestArenaMap::SerializeWithCachedSizes(
    ::google::protobuf::io::CodedOutputStream* output) const {
  // @@protoc_insertion_point(serialize_start:protobuf_unittest.TestArenaMap)
  // map<int32, int32> map_int32_int32 = 1;
  if (!this->map_int32_int32().empty()) {
    typedef ::google::protobuf::Map< ::google::protobuf::int32, ::google::protobuf::int32 >::const_pointer
        ConstPtr;
    typedef ::google::protobuf::internal::SortItem< ::google::protobuf::int32, ConstPtr > SortItem;
    typedef ::google::protobuf::internal::CompareByFirstField<SortItem> Less;

    if (output->IsSerializationDeterminstic() &&
        this->map_int32_int32().size() > 1) {
      ::google::protobuf::scoped_array<SortItem> items(
          new SortItem[this->map_int32_int32().size()]);
      typedef ::google::protobuf::Map< ::google::protobuf::int32, ::google::protobuf::int32 >::size_type size_type;
      size_type n = 0;
      for (::google::protobuf::Map< ::google::protobuf::int32, ::google::protobuf::int32 >::const_iterator
          it = this->map_int32_int32().begin();
          it != this->map_int32_int32().end(); ++it, ++n) {
        items[n] = SortItem(&*it);
      }
      ::std::sort(&items[0], &items[n], Less());
      ::google::protobuf::scoped_ptr<TestArenaMap_MapInt32Int32Entry> entry;
      for (size_type i = 0; i < n; i++) {
        entry.reset(map_int32_int32_.NewEntryWrapper(
            items[i].second->first, items[i].second->second));
        ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
            1, *entry, output);
        if (entry->GetArena() != NULL) {
          entry.release();
        }
      }
    } else {
      ::google::protobuf::scoped_ptr<TestArenaMap_MapInt32Int32Entry> entry;
      for (::google::protobuf::Map< ::google::protobuf::int32, ::google::protobuf::int32 >::const_iterator
          it = this->map_int32_int32().begin();
          it != this->map_int32_int32().end(); ++it) {
        entry.reset(map_int32_int32_.NewEntryWrapper(
            it->first, it->second));
        ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
            1, *entry, output);
        if (entry->GetArena() != NULL) {
          entry.release();
        }
      }
    }
  }

  // map<int64, int64> map_int64_int64 = 2;
  if (!this->map_int64_int64().empty()) {
    typedef ::google::protobuf::Map< ::google::protobuf::int64, ::google::protobuf::int64 >::const_pointer
        ConstPtr;
    typedef ::google::protobuf::internal::SortItem< ::google::protobuf::int64, ConstPtr > SortItem;
    typedef ::google::protobuf::internal::CompareByFirstField<SortItem> Less;

    if (output->IsSerializationDeterminstic() &&
        this->map_int64_int64().size() > 1) {
      ::google::protobuf::scoped_array<SortItem> items(
          new SortItem[this->map_int64_int64().size()]);
      typedef ::google::protobuf::Map< ::google::protobuf::int64, ::google::protobuf::int64 >::size_type size_type;
      size_type n = 0;
      for (::google::protobuf::Map< ::google::protobuf::int64, ::google::protobuf::int64 >::const_iterator
          it = this->map_int64_int64().begin();
          it != this->map_int64_int64().end(); ++it, ++n) {
        items[n] = SortItem(&*it);
      }
      ::std::sort(&items[0], &items[n], Less());
      ::google::protobuf::scoped_ptr<TestArenaMap_MapInt64Int64Entry> entry;
      for (size_type i = 0; i < n; i++) {
        entry.reset(map_int64_int64_.NewEntryWrapper(
            items[i].second->first, items[i].second->second));
        ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
            2, *entry, output);
        if (entry->GetArena() != NULL) {
          entry.release();
        }
      }
    } else {
      ::google::protobuf::scoped_ptr<TestArenaMap_MapInt64Int64Entry> entry;
      for (::google::protobuf::Map< ::google::protobuf::int64, ::google::protobuf::int64 >::const_iterator
          it = this->map_int64_int64().begin();
          it != this->map_int64_int64().end(); ++it) {
        entry.reset(map_int64_int64_.NewEntryWrapper(
            it->first, it->second));
        ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
            2, *entry, output);
        if (entry->GetArena() != NULL) {
          entry.release();
        }
      }
    }
  }

  // map<uint32, uint32> map_uint32_uint32 = 3;
  if (!this->map_uint32_uint32().empty()) {
    typedef ::google::protobuf::Map< ::google::protobuf::uint32, ::google::protobuf::uint32 >::const_pointer
        ConstPtr;
    typedef ::google::protobuf::internal::SortItem< ::google::protobuf::uint32, ConstPtr > SortItem;
    typedef ::google::protobuf::internal::CompareByFirstField<SortItem> Less;

    if (output->IsSerializationDeterminstic() &&
        this->map_uint32_uint32().size() > 1) {
      ::google::protobuf::scoped_array<SortItem> items(
          new SortItem[this->map_uint32_uint32().size()]);
      typedef ::google::protobuf::Map< ::google::protobuf::uint32, ::google::protobuf::uint32 >::size_type size_type;
      size_type n = 0;
      for (::google::protobuf::Map< ::google::protobuf::uint32, ::google::protobuf::uint32 >::const_iterator
          it = this->map_uint32_uint32().begin();
          it != this->map_uint32_uint32().end(); ++it, ++n) {
        items[n] = SortItem(&*it);
      }
      ::std::sort(&items[0], &items[n], Less());
      ::google::protobuf::scoped_ptr<TestArenaMap_MapUint32Uint32Entry> entry;
      for (size_type i = 0; i < n; i++) {
        entry.reset(map_uint32_uint32_.NewEntryWrapper(
            items[i].second->first, items[i].second->second));
        ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
            3, *entry, output);
        if (entry->GetArena() != NULL) {
          entry.release();
        }
      }
    } else {
      ::google::protobuf::scoped_ptr<TestArenaMap_MapUint32Uint32Entry> entry;
      for (::google::protobuf::Map< ::google::protobuf::uint32, ::google::protobuf::uint32 >::const_iterator
          it = this->map_uint32_uint32().begin();
          it != this->map_uint32_uint32().end(); ++it) {
        entry.reset(map_uint32_uint32_.NewEntryWrapper(
            it->first, it->second));
        ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
            3, *entry, output);
        if (entry->GetArena() != NULL) {
          entry.release();
        }
      }
    }
  }

  // map<uint64, uint64> map_uint64_uint64 = 4;
  if (!this->map_uint64_uint64().empty()) {
    typedef ::google::protobuf::Map< ::google::protobuf::uint64, ::google::protobuf::uint64 >::const_pointer
        ConstPtr;
    typedef ::google::protobuf::internal::SortItem< ::google::protobuf::uint64, ConstPtr > SortItem;
    typedef ::google::protobuf::internal::CompareByFirstField<SortItem> Less;

    if (output->IsSerializationDeterminstic() &&
        this->map_uint64_uint64().size() > 1) {
      ::google::protobuf::scoped_array<SortItem> items(
          new SortItem[this->map_uint64_uint64().size()]);
      typedef ::google::protobuf::Map< ::google::protobuf::uint64, ::google::protobuf::uint64 >::size_type size_type;
      size_type n = 0;
      for (::google::protobuf::Map< ::google::protobuf::uint64, ::google::protobuf::uint64 >::const_iterator
          it = this->map_uint64_uint64().begin();
          it != this->map_uint64_uint64().end(); ++it, ++n) {
        items[n] = SortItem(&*it);
      }
      ::std::sort(&items[0], &items[n], Less());
      ::google::protobuf::scoped_ptr<TestArenaMap_MapUint64Uint64Entry> entry;
      for (size_type i = 0; i < n; i++) {
        entry.reset(map_uint64_uint64_.NewEntryWrapper(
            items[i].second->first, items[i].second->second));
        ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
            4, *entry, output);
        if (entry->GetArena() != NULL) {
          entry.release();
        }
      }
    } else {
      ::google::protobuf::scoped_ptr<TestArenaMap_MapUint64Uint64Entry> entry;
      for (::google::protobuf::Map< ::google::protobuf::uint64, ::google::protobuf::uint64 >::const_iterator
          it = this->map_uint64_uint64().begin();
          it != this->map_uint64_uint64().end(); ++it) {
        entry.reset(map_uint64_uint64_.NewEntryWrapper(
            it->first, it->second));
        ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
            4, *entry, output);
        if (entry->GetArena() != NULL) {
          entry.release();
        }
      }
    }
  }

  // map<sint32, sint32> map_sint32_sint32 = 5;
  if (!this->map_sint32_sint32().empty()) {
    typedef ::google::protobuf::Map< ::google::protobuf::int32, ::google::protobuf::int32 >::const_pointer
        ConstPtr;
    typedef ::google::protobuf::internal::SortItem< ::google::protobuf::int32, ConstPtr > SortItem;
    typedef ::google::protobuf::internal::CompareByFirstField<SortItem> Less;

    if (output->IsSerializationDeterminstic() &&
        this->map_sint32_sint32().size() > 1) {
      ::google::protobuf::scoped_array<SortItem> items(
          new SortItem[this->map_sint32_sint32().size()]);
      typedef ::google::protobuf::Map< ::google::protobuf::int32, ::google::protobuf::int32 >::size_type size_type;
      size_type n = 0;
      for (::google::protobuf::Map< ::google::protobuf::int32, ::google::protobuf::int32 >::const_iterator
          it = this->map_sint32_sint32().begin();
          it != this->map_sint32_sint32().end(); ++it, ++n) {
        items[n] = SortItem(&*it);
      }
      ::std::sort(&items[0], &items[n], Less());
      ::google::protobuf::scoped_ptr<TestArenaMap_MapSint32Sint32Entry> entry;
      for (size_type i = 0; i < n; i++) {
        entry.reset(map_sint32_sint32_.NewEntryWrapper(
            items[i].second->first, items[i].second->second));
        ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
            5, *entry, output);
        if (entry->GetArena() != NULL) {
          entry.release();
        }
      }
    } else {
      ::google::protobuf::scoped_ptr<TestArenaMap_MapSint32Sint32Entry> entry;
      for (::google::protobuf::Map< ::google::protobuf::int32, ::google::protobuf::int32 >::const_iterator
          it = this->map_sint32_sint32().begin();
          it != this->map_sint32_sint32().end(); ++it) {
        entry.reset(map_sint32_sint32_.NewEntryWrapper(
            it->first, it->second));
        ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
            5, *entry, output);
        if (entry->GetArena() != NULL) {
          entry.release();
        }
      }
    }
  }

  // map<sint64, sint64> map_sint64_sint64 = 6;
  if (!this->map_sint64_sint64().empty()) {
    typedef ::google::protobuf::Map< ::google::protobuf::int64, ::google::protobuf::int64 >::const_pointer
        ConstPtr;
    typedef ::google::protobuf::internal::SortItem< ::google::protobuf::int64, ConstPtr > SortItem;
    typedef ::google::protobuf::internal::CompareByFirstField<SortItem> Less;

    if (output->IsSerializationDeterminstic() &&
        this->map_sint64_sint64().size() > 1) {
      ::google::protobuf::scoped_array<SortItem> items(
          new SortItem[this->map_sint64_sint64().size()]);
      typedef ::google::protobuf::Map< ::google::protobuf::int64, ::google::protobuf::int64 >::size_type size_type;
      size_type n = 0;
      for (::google::protobuf::Map< ::google::protobuf::int64, ::google::protobuf::int64 >::const_iterator
          it = this->map_sint64_sint64().begin();
          it != this->map_sint64_sint64().end(); ++it, ++n) {
        items[n] = SortItem(&*it);
      }
      ::std::sort(&items[0], &items[n], Less());
      ::google::protobuf::scoped_ptr<TestArenaMap_MapSint64Sint64Entry> entry;
      for (size_type i = 0; i < n; i++) {
        entry.reset(map_sint64_sint64_.NewEntryWrapper(
            items[i].second->first, items[i].second->second));
        ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
            6, *entry, output);
        if (entry->GetArena() != NULL) {
          entry.release();
        }
      }
    } else {
      ::google::protobuf::scoped_ptr<TestArenaMap_MapSint64Sint64Entry> entry;
      for (::google::protobuf::Map< ::google::protobuf::int64, ::google::protobuf::int64 >::const_iterator
          it = this->map_sint64_sint64().begin();
          it != this->map_sint64_sint64().end(); ++it) {
        entry.reset(map_sint64_sint64_.NewEntryWrapper(
            it->first, it->second));
        ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
            6, *entry, output);
        if (entry->GetArena() != NULL) {
          entry.release();
        }
      }
    }
  }

  // map<fixed32, fixed32> map_fixed32_fixed32 = 7;
  if (!this->map_fixed32_fixed32().empty()) {
    typedef ::google::protobuf::Map< ::google::protobuf::uint32, ::google::protobuf::uint32 >::const_pointer
        ConstPtr;
    typedef ::google::protobuf::internal::SortItem< ::google::protobuf::uint32, ConstPtr > SortItem;
    typedef ::google::protobuf::internal::CompareByFirstField<SortItem> Less;

    if (output->IsSerializationDeterminstic() &&
        this->map_fixed32_fixed32().size() > 1) {
      ::google::protobuf::scoped_array<SortItem> items(
          new SortItem[this->map_fixed32_fixed32().size()]);
      typedef ::google::protobuf::Map< ::google::protobuf::uint32, ::google::protobuf::uint32 >::size_type size_type;
      size_type n = 0;
      for (::google::protobuf::Map< ::google::protobuf::uint32, ::google::protobuf::uint32 >::const_iterator
          it = this->map_fixed32_fixed32().begin();
          it != this->map_fixed32_fixed32().end(); ++it, ++n) {
        items[n] = SortItem(&*it);
      }
      ::std::sort(&items[0], &items[n], Less());
      ::google::protobuf::scoped_ptr<TestArenaMap_MapFixed32Fixed32Entry> entry;
      for (size_type i = 0; i < n; i++) {
        entry.reset(map_fixed32_fixed32_.NewEntryWrapper(
            items[i].second->first, items[i].second->second));
        ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
            7, *entry, output);
        if (entry->GetArena() != NULL) {
          entry.release();
        }
      }
    } else {
      ::google::protobuf::scoped_ptr<TestArenaMap_MapFixed32Fixed32Entry> entry;
      for (::google::protobuf::Map< ::google::protobuf::uint32, ::google::protobuf::uint32 >::const_iterator
          it = this->map_fixed32_fixed32().begin();
          it != this->map_fixed32_fixed32().end(); ++it) {
        entry.reset(map_fixed32_fixed32_.NewEntryWrapper(
            it->first, it->second));
        ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
            7, *entry, output);
        if (entry->GetArena() != NULL) {
          entry.release();
        }
      }
    }
  }

  // map<fixed64, fixed64> map_fixed64_fixed64 = 8;
  if (!this->map_fixed64_fixed64().empty()) {
    typedef ::google::protobuf::Map< ::google::protobuf::uint64, ::google::protobuf::uint64 >::const_pointer
        ConstPtr;
    typedef ::google::protobuf::internal::SortItem< ::google::protobuf::uint64, ConstPtr > SortItem;
    typedef ::google::protobuf::internal::CompareByFirstField<SortItem> Less;

    if (output->IsSerializationDeterminstic() &&
        this->map_fixed64_fixed64().size() > 1) {
      ::google::protobuf::scoped_array<SortItem> items(
          new SortItem[this->map_fixed64_fixed64().size()]);
      typedef ::google::protobuf::Map< ::google::protobuf::uint64, ::google::protobuf::uint64 >::size_type size_type;
      size_type n = 0;
      for (::google::protobuf::Map< ::google::protobuf::uint64, ::google::protobuf::uint64 >::const_iterator
          it = this->map_fixed64_fixed64().begin();
          it != this->map_fixed64_fixed64().end(); ++it, ++n) {
        items[n] = SortItem(&*it);
      }
      ::std::sort(&items[0], &items[n], Less());
      ::google::protobuf::scoped_ptr<TestArenaMap_MapFixed64Fixed64Entry> entry;
      for (size_type i = 0; i < n; i++) {
        entry.reset(map_fixed64_fixed64_.NewEntryWrapper(
            items[i].second->first, items[i].second->second));
        ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
            8, *entry, output);
        if (entry->GetArena() != NULL) {
          entry.release();
        }
      }
    } else {
      ::google::protobuf::scoped_ptr<TestArenaMap_MapFixed64Fixed64Entry> entry;
      for (::google::protobuf::Map< ::google::protobuf::uint64, ::google::protobuf::uint64 >::const_iterator
          it = this->map_fixed64_fixed64().begin();
          it != this->map_fixed64_fixed64().end(); ++it) {
        entry.reset(map_fixed64_fixed64_.NewEntryWrapper(
            it->first, it->second));
        ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
            8, *entry, output);
        if (entry->GetArena() != NULL) {
          entry.release();
        }
      }
    }
  }

  // map<sfixed32, sfixed32> map_sfixed32_sfixed32 = 9;
  if (!this->map_sfixed32_sfixed32().empty()) {
    typedef ::google::protobuf::Map< ::google::protobuf::int32, ::google::protobuf::int32 >::const_pointer
        ConstPtr;
    typedef ::google::protobuf::internal::SortItem< ::google::protobuf::int32, ConstPtr > SortItem;
    typedef ::google::protobuf::internal::CompareByFirstField<SortItem> Less;

    if (output->IsSerializationDeterminstic() &&
        this->map_sfixed32_sfixed32().size() > 1) {
      ::google::protobuf::scoped_array<SortItem> items(
          new SortItem[this->map_sfixed32_sfixed32().size()]);
      typedef ::google::protobuf::Map< ::google::protobuf::int32, ::google::protobuf::int32 >::size_type size_type;
      size_type n = 0;
      for (::google::protobuf::Map< ::google::protobuf::int32, ::google::protobuf::int32 >::const_iterator
          it = this->map_sfixed32_sfixed32().begin();
          it != this->map_sfixed32_sfixed32().end(); ++it, ++n) {
        items[n] = SortItem(&*it);
      }
      ::std::sort(&items[0], &items[n], Less());
      ::google::protobuf::scoped_ptr<TestArenaMap_MapSfixed32Sfixed32Entry> entry;
      for (size_type i = 0; i < n; i++) {
        entry.reset(map_sfixed32_sfixed32_.NewEntryWrapper(
            items[i].second->first, items[i].second->second));
        ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
            9, *entry, output);
        if (entry->GetArena() != NULL) {
          entry.release();
        }
      }
    } else {
      ::google::protobuf::scoped_ptr<TestArenaMap_MapSfixed32Sfixed32Entry> entry;
      for (::google::protobuf::Map< ::google::protobuf::int32, ::google::protobuf::int32 >::const_iterator
          it = this->map_sfixed32_sfixed32().begin();
          it != this->map_sfixed32_sfixed32().end(); ++it) {
        entry.reset(map_sfixed32_sfixed32_.NewEntryWrapper(
            it->first, it->second));
        ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
            9, *entry, output);
        if (entry->GetArena() != NULL) {
          entry.release();
        }
      }
    }
  }

  // map<sfixed64, sfixed64> map_sfixed64_sfixed64 = 10;
  if (!this->map_sfixed64_sfixed64().empty()) {
    typedef ::google::protobuf::Map< ::google::protobuf::int64, ::google::protobuf::int64 >::const_pointer
        ConstPtr;
    typedef ::google::protobuf::internal::SortItem< ::google::protobuf::int64, ConstPtr > SortItem;
    typedef ::google::protobuf::internal::CompareByFirstField<SortItem> Less;

    if (output->IsSerializationDeterminstic() &&
        this->map_sfixed64_sfixed64().size() > 1) {
      ::google::protobuf::scoped_array<SortItem> items(
          new SortItem[this->map_sfixed64_sfixed64().size()]);
      typedef ::google::protobuf::Map< ::google::protobuf::int64, ::google::protobuf::int64 >::size_type size_type;
      size_type n = 0;
      for (::google::protobuf::Map< ::google::protobuf::int64, ::google::protobuf::int64 >::const_iterator
          it = this->map_sfixed64_sfixed64().begin();
          it != this->map_sfixed64_sfixed64().end(); ++it, ++n) {
        items[n] = SortItem(&*it);
      }
      ::std::sort(&items[0], &items[n], Less());
      ::google::protobuf::scoped_ptr<TestArenaMap_MapSfixed64Sfixed64Entry> entry;
      for (size_type i = 0; i < n; i++) {
        entry.reset(map_sfixed64_sfixed64_.NewEntryWrapper(
            items[i].second->first, items[i].second->second));
        ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
            10, *entry, output);
        if (entry->GetArena() != NULL) {
          entry.release();
        }
      }
    } else {
      ::google::protobuf::scoped_ptr<TestArenaMap_MapSfixed64Sfixed64Entry> entry;
      for (::google::protobuf::Map< ::google::protobuf::int64, ::google::protobuf::int64 >::const_iterator
          it = this->map_sfixed64_sfixed64().begin();
          it != this->map_sfixed64_sfixed64().end(); ++it) {
        entry.reset(map_sfixed64_sfixed64_.NewEntryWrapper(
            it->first, it->second));
        ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
            10, *entry, output);
        if (entry->GetArena() != NULL) {
          entry.release();
        }
      }
    }
  }

  // map<int32, float> map_int32_float = 11;
  if (!this->map_int32_float().empty()) {
    typedef ::google::protobuf::Map< ::google::protobuf::int32, float >::const_pointer
        ConstPtr;
    typedef ::google::protobuf::internal::SortItem< ::google::protobuf::int32, ConstPtr > SortItem;
    typedef ::google::protobuf::internal::CompareByFirstField<SortItem> Less;

    if (output->IsSerializationDeterminstic() &&
        this->map_int32_float().size() > 1) {
      ::google::protobuf::scoped_array<SortItem> items(
          new SortItem[this->map_int32_float().size()]);
      typedef ::google::protobuf::Map< ::google::protobuf::int32, float >::size_type size_type;
      size_type n = 0;
      for (::google::protobuf::Map< ::google::protobuf::int32, float >::const_iterator
          it = this->map_int32_float().begin();
          it != this->map_int32_float().end(); ++it, ++n) {
        items[n] = SortItem(&*it);
      }
      ::std::sort(&items[0], &items[n], Less());
      ::google::protobuf::scoped_ptr<TestArenaMap_MapInt32FloatEntry> entry;
      for (size_type i = 0; i < n; i++) {
        entry.reset(map_int32_float_.NewEntryWrapper(
            items[i].second->first, items[i].second->second));
        ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
            11, *entry, output);
        if (entry->GetArena() != NULL) {
          entry.release();
        }
      }
    } else {
      ::google::protobuf::scoped_ptr<TestArenaMap_MapInt32FloatEntry> entry;
      for (::google::protobuf::Map< ::google::protobuf::int32, float >::const_iterator
          it = this->map_int32_float().begin();
          it != this->map_int32_float().end(); ++it) {
        entry.reset(map_int32_float_.NewEntryWrapper(
            it->first, it->second));
        ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
            11, *entry, output);
        if (entry->GetArena() != NULL) {
          entry.release();
        }
      }
    }
  }

  // map<int32, double> map_int32_double = 12;
  if (!this->map_int32_double().empty()) {
    typedef ::google::protobuf::Map< ::google::protobuf::int32, double >::const_pointer
        ConstPtr;
    typedef ::google::protobuf::internal::SortItem< ::google::protobuf::int32, ConstPtr > SortItem;
    typedef ::google::protobuf::internal::CompareByFirstField<SortItem> Less;

    if (output->IsSerializationDeterminstic() &&
        this->map_int32_double().size() > 1) {
      ::google::protobuf::scoped_array<SortItem> items(
          new SortItem[this->map_int32_double().size()]);
      typedef ::google::protobuf::Map< ::google::protobuf::int32, double >::size_type size_type;
      size_type n = 0;
      for (::google::protobuf::Map< ::google::protobuf::int32, double >::const_iterator
          it = this->map_int32_double().begin();
          it != this->map_int32_double().end(); ++it, ++n) {
        items[n] = SortItem(&*it);
      }
      ::std::sort(&items[0], &items[n], Less());
      ::google::protobuf::scoped_ptr<TestArenaMap_MapInt32DoubleEntry> entry;
      for (size_type i = 0; i < n; i++) {
        entry.reset(map_int32_double_.NewEntryWrapper(
            items[i].second->first, items[i].second->second));
        ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
            12, *entry, output);
        if (entry->GetArena() != NULL) {
          entry.release();
        }
      }
    } else {
      ::google::protobuf::scoped_ptr<TestArenaMap_MapInt32DoubleEntry> entry;
      for (::google::protobuf::Map< ::google::protobuf::int32, double >::const_iterator
          it = this->map_int32_double().begin();
          it != this->map_int32_double().end(); ++it) {
        entry.reset(map_int32_double_.NewEntryWrapper(
            it->first, it->second));
        ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
            12, *entry, output);
        if (entry->GetArena() != NULL) {
          entry.release();
        }
      }
    }
  }

  // map<bool, bool> map_bool_bool = 13;
  if (!this->map_bool_bool().empty()) {
    typedef ::google::protobuf::Map< bool, bool >::const_pointer
        ConstPtr;
    typedef ::google::protobuf::internal::SortItem< bool, ConstPtr > SortItem;
    typedef ::google::protobuf::internal::CompareByFirstField<SortItem> Less;

    if (output->IsSerializationDeterminstic() &&
        this->map_bool_bool().size() > 1) {
      ::google::protobuf::scoped_array<SortItem> items(
          new SortItem[this->map_bool_bool().size()]);
      typedef ::google::protobuf::Map< bool, bool >::size_type size_type;
      size_type n = 0;
      for (::google::protobuf::Map< bool, bool >::const_iterator
          it = this->map_bool_bool().begin();
          it != this->map_bool_bool().end(); ++it, ++n) {
        items[n] = SortItem(&*it);
      }
      ::std::sort(&items[0], &items[n], Less());
      ::google::protobuf::scoped_ptr<TestArenaMap_MapBoolBoolEntry> entry;
      for (size_type i = 0; i < n; i++) {
        entry.reset(map_bool_bool_.NewEntryWrapper(
            items[i].second->first, items[i].second->second));
        ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
            13, *entry, output);
        if (entry->GetArena() != NULL) {
          entry.release();
        }
      }
    } else {
      ::google::protobuf::scoped_ptr<TestArenaMap_MapBoolBoolEntry> entry;
      for (::google::protobuf::Map< bool, bool >::const_iterator
          it = this->map_bool_bool().begin();
          it != this->map_bool_bool().end(); ++it) {
        entry.reset(map_bool_bool_.NewEntryWrapper(
            it->first, it->second));
        ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
            13, *entry, output);
        if (entry->GetArena() != NULL) {
          entry.release();
        }
      }
    }
  }

  // map<string, string> map_string_string = 14;
  if (!this->map_string_string().empty()) {
    typedef ::google::protobuf::Map< ::std::string, ::std::string >::const_pointer
        ConstPtr;
    typedef ConstPtr SortItem;
    typedef ::google::protobuf::internal::CompareByDerefFirst<SortItem> Less;
    struct Utf8Check {
      static void Check(ConstPtr p) {
        ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
          p->first.data(), p->first.length(),
          ::google::protobuf::internal::WireFormatLite::SERIALIZE,
          "protobuf_unittest.TestArenaMap.MapStringStringEntry.key");
        ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
          p->second.data(), p->second.length(),
          ::google::protobuf::internal::WireFormatLite::SERIALIZE,
          "protobuf_unittest.TestArenaMap.MapStringStringEntry.value");
      }
    };

    if (output->IsSerializationDeterminstic() &&
        this->map_string_string().size() > 1) {
      ::google::protobuf::scoped_array<SortItem> items(
          new SortItem[this->map_string_string().size()]);
      typedef ::google::protobuf::Map< ::std::string, ::std::string >::size_type size_type;
      size_type n = 0;
      for (::google::protobuf::Map< ::std::string, ::std::string >::const_iterator
          it = this->map_string_string().begin();
          it != this->map_string_string().end(); ++it, ++n) {
        items[n] = SortItem(&*it);
      }
      ::std::sort(&items[0], &items[n], Less());
      ::google::protobuf::scoped_ptr<TestArenaMap_MapStringStringEntry> entry;
      for (size_type i = 0; i < n; i++) {
        entry.reset(map_string_string_.NewEntryWrapper(
            items[i]->first, items[i]->second));
        ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
            14, *entry, output);
        if (entry->GetArena() != NULL) {
          entry.release();
        }
        Utf8Check::Check(items[i]);
      }
    } else {
      ::google::protobuf::scoped_ptr<TestArenaMap_MapStringStringEntry> entry;
      for (::google::protobuf::Map< ::std::string, ::std::string >::const_iterator
          it = this->map_string_string().begin();
          it != this->map_string_string().end(); ++it) {
        entry.reset(map_string_string_.NewEntryWrapper(
            it->first, it->second));
        ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
            14, *entry, output);
        if (entry->GetArena() != NULL) {
          entry.release();
        }
        Utf8Check::Check(&*it);
      }
    }
  }

  // map<int32, bytes> map_int32_bytes = 15;
  if (!this->map_int32_bytes().empty()) {
    typedef ::google::protobuf::Map< ::google::protobuf::int32, ::std::string >::const_pointer
        ConstPtr;
    typedef ::google::protobuf::internal::SortItem< ::google::protobuf::int32, ConstPtr > SortItem;
    typedef ::google::protobuf::internal::CompareByFirstField<SortItem> Less;

    if (output->IsSerializationDeterminstic() &&
        this->map_int32_bytes().size() > 1) {
      ::google::protobuf::scoped_array<SortItem> items(
          new SortItem[this->map_int32_bytes().size()]);
      typedef ::google::protobuf::Map< ::google::protobuf::int32, ::std::string >::size_type size_type;
      size_type n = 0;
      for (::google::protobuf::Map< ::google::protobuf::int32, ::std::string >::const_iterator
          it = this->map_int32_bytes().begin();
          it != this->map_int32_bytes().end(); ++it, ++n) {
        items[n] = SortItem(&*it);
      }
      ::std::sort(&items[0], &items[n], Less());
      ::google::protobuf::scoped_ptr<TestArenaMap_MapInt32BytesEntry> entry;
      for (size_type i = 0; i < n; i++) {
        entry.reset(map_int32_bytes_.NewEntryWrapper(
            items[i].second->first, items[i].second->second));
        ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
            15, *entry, output);
        if (entry->GetArena() != NULL) {
          entry.release();
        }
      }
    } else {
      ::google::protobuf::scoped_ptr<TestArenaMap_MapInt32BytesEntry> entry;
      for (::google::protobuf::Map< ::google::protobuf::int32, ::std::string >::const_iterator
          it = this->map_int32_bytes().begin();
          it != this->map_int32_bytes().end(); ++it) {
        entry.reset(map_int32_bytes_.NewEntryWrapper(
            it->first, it->second));
        ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
            15, *entry, output);
        if (entry->GetArena() != NULL) {
          entry.release();
        }
      }
    }
  }

  // map<int32, .protobuf_unittest.MapEnum> map_int32_enum = 16;
  if (!this->map_int32_enum().empty()) {
    typedef ::google::protobuf::Map< ::google::protobuf::int32, ::protobuf_unittest::MapEnum >::const_pointer
        ConstPtr;
    typedef ::google::protobuf::internal::SortItem< ::google::protobuf::int32, ConstPtr > SortItem;
    typedef ::google::protobuf::internal::CompareByFirstField<SortItem> Less;

    if (output->IsSerializationDeterminstic() &&
        this->map_int32_enum().size() > 1) {
      ::google::protobuf::scoped_array<SortItem> items(
          new SortItem[this->map_int32_enum().size()]);
      typedef ::google::protobuf::Map< ::google::protobuf::int32, ::protobuf_unittest::MapEnum >::size_type size_type;
      size_type n = 0;
      for (::google::protobuf::Map< ::google::protobuf::int32, ::protobuf_unittest::MapEnum >::const_iterator
          it = this->map_int32_enum().begin();
          it != this->map_int32_enum().end(); ++it, ++n) {
        items[n] = SortItem(&*it);
      }
      ::std::sort(&items[0], &items[n], Less());
      ::google::protobuf::scoped_ptr<TestArenaMap_MapInt32EnumEntry> entry;
      for (size_type i = 0; i < n; i++) {
        entry.reset(map_int32_enum_.NewEnumEntryWrapper(
            items[i].second->first, items[i].second->second));
        ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
            16, *entry, output);
        if (entry->GetArena() != NULL) {
          entry.release();
        }
      }
    } else {
      ::google::protobuf::scoped_ptr<TestArenaMap_MapInt32EnumEntry> entry;
      for (::google::protobuf::Map< ::google::protobuf::int32, ::protobuf_unittest::MapEnum >::const_iterator
          it = this->map_int32_enum().begin();
          it != this->map_int32_enum().end(); ++it) {
        entry.reset(map_int32_enum_.NewEnumEntryWrapper(
            it->first, it->second));
        ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
            16, *entry, output);
        if (entry->GetArena() != NULL) {
          entry.release();
        }
      }
    }
  }

  // map<int32, .protobuf_unittest.ForeignMessage> map_int32_foreign_message = 17;
  if (!this->map_int32_foreign_message().empty()) {
    typedef ::google::protobuf::Map< ::google::protobuf::int32, ::protobuf_unittest::ForeignMessage >::const_pointer
        ConstPtr;
    typedef ::google::protobuf::internal::SortItem< ::google::protobuf::int32, ConstPtr > SortItem;
    typedef ::google::protobuf::internal::CompareByFirstField<SortItem> Less;

    if (output->IsSerializationDeterminstic() &&
        this->map_int32_foreign_message().size() > 1) {
      ::google::protobuf::scoped_array<SortItem> items(
          new SortItem[this->map_int32_foreign_message().size()]);
      typedef ::google::protobuf::Map< ::google::protobuf::int32, ::protobuf_unittest::ForeignMessage >::size_type size_type;
      size_type n = 0;
      for (::google::protobuf::Map< ::google::protobuf::int32, ::protobuf_unittest::ForeignMessage >::const_iterator
          it = this->map_int32_foreign_message().begin();
          it != this->map_int32_foreign_message().end(); ++it, ++n) {
        items[n] = SortItem(&*it);
      }
      ::std::sort(&items[0], &items[n], Less());
      ::google::protobuf::scoped_ptr<TestArenaMap_MapInt32ForeignMessageEntry> entry;
      for (size_type i = 0; i < n; i++) {
        entry.reset(map_int32_foreign_message_.NewEntryWrapper(
            items[i].second->first, items[i].second->second));
        ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
            17, *entry, output);
        if (entry->GetArena() != NULL) {
          entry.release();
        }
      }
    } else {
      ::google::protobuf::scoped_ptr<TestArenaMap_MapInt32ForeignMessageEntry> entry;
      for (::google::protobuf::Map< ::google::protobuf::int32, ::protobuf_unittest::ForeignMessage >::const_iterator
          it = this->map_int32_foreign_message().begin();
          it != this->map_int32_foreign_message().end(); ++it) {
        entry.reset(map_int32_foreign_message_.NewEntryWrapper(
            it->first, it->second));
        ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
            17, *entry, output);
        if (entry->GetArena() != NULL) {
          entry.release();
        }
      }
    }
  }

  // map<int32, .protobuf_unittest_no_arena.ForeignMessage> map_int32_foreign_message_no_arena = 18;
  if (!this->map_int32_foreign_message_no_arena().empty()) {
    typedef ::google::protobuf::Map< ::google::protobuf::int32, ::protobuf_unittest_no_arena::ForeignMessage >::const_pointer
        ConstPtr;
    typedef ::google::protobuf::internal::SortItem< ::google::protobuf::int32, ConstPtr > SortItem;
    typedef ::google::protobuf::internal::CompareByFirstField<SortItem> Less;

    if (output->IsSerializationDeterminstic() &&
        this->map_int32_foreign_message_no_arena().size() > 1) {
      ::google::protobuf::scoped_array<SortItem> items(
          new SortItem[this->map_int32_foreign_message_no_arena().size()]);
      typedef ::google::protobuf::Map< ::google::protobuf::int32, ::protobuf_unittest_no_arena::ForeignMessage >::size_type size_type;
      size_type n = 0;
      for (::google::protobuf::Map< ::google::protobuf::int32, ::protobuf_unittest_no_arena::ForeignMessage >::const_iterator
          it = this->map_int32_foreign_message_no_arena().begin();
          it != this->map_int32_foreign_message_no_arena().end(); ++it, ++n) {
        items[n] = SortItem(&*it);
      }
      ::std::sort(&items[0], &items[n], Less());
      ::google::protobuf::scoped_ptr<TestArenaMap_MapInt32ForeignMessageNoArenaEntry> entry;
      for (size_type i = 0; i < n; i++) {
        entry.reset(map_int32_foreign_message_no_arena_.NewEntryWrapper(
            items[i].second->first, items[i].second->second));
        ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
            18, *entry, output);
        if (entry->GetArena() != NULL) {
          entry.release();
        }
      }
    } else {
      ::google::protobuf::scoped_ptr<TestArenaMap_MapInt32ForeignMessageNoArenaEntry> entry;
      for (::google::protobuf::Map< ::google::protobuf::int32, ::protobuf_unittest_no_arena::ForeignMessage >::const_iterator
          it = this->map_int32_foreign_message_no_arena().begin();
          it != this->map_int32_foreign_message_no_arena().end(); ++it) {
        entry.reset(map_int32_foreign_message_no_arena_.NewEntryWrapper(
            it->first, it->second));
        ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
            18, *entry, output);
        if (entry->GetArena() != NULL) {
          entry.release();
        }
      }
    }
  }

  // @@protoc_insertion_point(serialize_end:protobuf_unittest.TestArenaMap)
}

::google::protobuf::uint8* TestArenaMap::InternalSerializeWithCachedSizesToArray(
    bool deterministic, ::google::protobuf::uint8* target) const {
  (void)deterministic; // Unused
  // @@protoc_insertion_point(serialize_to_array_start:protobuf_unittest.TestArenaMap)
  // map<int32, int32> map_int32_int32 = 1;
  if (!this->map_int32_int32().empty()) {
    typedef ::google::protobuf::Map< ::google::protobuf::int32, ::google::protobuf::int32 >::const_pointer
        ConstPtr;
    typedef ::google::protobuf::internal::SortItem< ::google::protobuf::int32, ConstPtr > SortItem;
    typedef ::google::protobuf::internal::CompareByFirstField<SortItem> Less;

    if (deterministic &&
        this->map_int32_int32().size() > 1) {
      ::google::protobuf::scoped_array<SortItem> items(
          new SortItem[this->map_int32_int32().size()]);
      typedef ::google::protobuf::Map< ::google::protobuf::int32, ::google::protobuf::int32 >::size_type size_type;
      size_type n = 0;
      for (::google::protobuf::Map< ::google::protobuf::int32, ::google::protobuf::int32 >::const_iterator
          it = this->map_int32_int32().begin();
          it != this->map_int32_int32().end(); ++it, ++n) {
        items[n] = SortItem(&*it);
      }
      ::std::sort(&items[0], &items[n], Less());
      ::google::protobuf::scoped_ptr<TestArenaMap_MapInt32Int32Entry> entry;
      for (size_type i = 0; i < n; i++) {
        entry.reset(map_int32_int32_.NewEntryWrapper(
            items[i].second->first, items[i].second->second));
        target = ::google::protobuf::internal::WireFormatLite::
                   InternalWriteMessageNoVirtualToArray(
                       1, *entry, deterministic, target);
;
        if (entry->GetArena() != NULL) {
          entry.release();
        }
      }
    } else {
      ::google::protobuf::scoped_ptr<TestArenaMap_MapInt32Int32Entry> entry;
      for (::google::protobuf::Map< ::google::protobuf::int32, ::google::protobuf::int32 >::const_iterator
          it = this->map_int32_int32().begin();
          it != this->map_int32_int32().end(); ++it) {
        entry.reset(map_int32_int32_.NewEntryWrapper(
            it->first, it->second));
        target = ::google::protobuf::internal::WireFormatLite::
                   InternalWriteMessageNoVirtualToArray(
                       1, *entry, deterministic, target);
;
        if (entry->GetArena() != NULL) {
          entry.release();
        }
      }
    }
  }

  // map<int64, int64> map_int64_int64 = 2;
  if (!this->map_int64_int64().empty()) {
    typedef ::google::protobuf::Map< ::google::protobuf::int64, ::google::protobuf::int64 >::const_pointer
        ConstPtr;
    typedef ::google::protobuf::internal::SortItem< ::google::protobuf::int64, ConstPtr > SortItem;
    typedef ::google::protobuf::internal::CompareByFirstField<SortItem> Less;

    if (deterministic &&
        this->map_int64_int64().size() > 1) {
      ::google::protobuf::scoped_array<SortItem> items(
          new SortItem[this->map_int64_int64().size()]);
      typedef ::google::protobuf::Map< ::google::protobuf::int64, ::google::protobuf::int64 >::size_type size_type;
      size_type n = 0;
      for (::google::protobuf::Map< ::google::protobuf::int64, ::google::protobuf::int64 >::const_iterator
          it = this->map_int64_int64().begin();
          it != this->map_int64_int64().end(); ++it, ++n) {
        items[n] = SortItem(&*it);
      }
      ::std::sort(&items[0], &items[n], Less());
      ::google::protobuf::scoped_ptr<TestArenaMap_MapInt64Int64Entry> entry;
      for (size_type i = 0; i < n; i++) {
        entry.reset(map_int64_int64_.NewEntryWrapper(
            items[i].second->first, items[i].second->second));
        target = ::google::protobuf::internal::WireFormatLite::
                   InternalWriteMessageNoVirtualToArray(
                       2, *entry, deterministic, target);
;
        if (entry->GetArena() != NULL) {
          entry.release();
        }
      }
    } else {
      ::google::protobuf::scoped_ptr<TestArenaMap_MapInt64Int64Entry> entry;
      for (::google::protobuf::Map< ::google::protobuf::int64, ::google::protobuf::int64 >::const_iterator
          it = this->map_int64_int64().begin();
          it != this->map_int64_int64().end(); ++it) {
        entry.reset(map_int64_int64_.NewEntryWrapper(
            it->first, it->second));
        target = ::google::protobuf::internal::WireFormatLite::
                   InternalWriteMessageNoVirtualToArray(
                       2, *entry, deterministic, target);
;
        if (entry->GetArena() != NULL) {
          entry.release();
        }
      }
    }
  }

  // map<uint32, uint32> map_uint32_uint32 = 3;
  if (!this->map_uint32_uint32().empty()) {
    typedef ::google::protobuf::Map< ::google::protobuf::uint32, ::google::protobuf::uint32 >::const_pointer
        ConstPtr;
    typedef ::google::protobuf::internal::SortItem< ::google::protobuf::uint32, ConstPtr > SortItem;
    typedef ::google::protobuf::internal::CompareByFirstField<SortItem> Less;

    if (deterministic &&
        this->map_uint32_uint32().size() > 1) {
      ::google::protobuf::scoped_array<SortItem> items(
          new SortItem[this->map_uint32_uint32().size()]);
      typedef ::google::protobuf::Map< ::google::protobuf::uint32, ::google::protobuf::uint32 >::size_type size_type;
      size_type n = 0;
      for (::google::protobuf::Map< ::google::protobuf::uint32, ::google::protobuf::uint32 >::const_iterator
          it = this->map_uint32_uint32().begin();
          it != this->map_uint32_uint32().end(); ++it, ++n) {
        items[n] = SortItem(&*it);
      }
      ::std::sort(&items[0], &items[n], Less());
      ::google::protobuf::scoped_ptr<TestArenaMap_MapUint32Uint32Entry> entry;
      for (size_type i = 0; i < n; i++) {
        entry.reset(map_uint32_uint32_.NewEntryWrapper(
            items[i].second->first, items[i].second->second));
        target = ::google::protobuf::internal::WireFormatLite::
                   InternalWriteMessageNoVirtualToArray(
                       3, *entry, deterministic, target);
;
        if (entry->GetArena() != NULL) {
          entry.release();
        }
      }
    } else {
      ::google::protobuf::scoped_ptr<TestArenaMap_MapUint32Uint32Entry> entry;
      for (::google::protobuf::Map< ::google::protobuf::uint32, ::google::protobuf::uint32 >::const_iterator
          it = this->map_uint32_uint32().begin();
          it != this->map_uint32_uint32().end(); ++it) {
        entry.reset(map_uint32_uint32_.NewEntryWrapper(
            it->first, it->second));
        target = ::google::protobuf::internal::WireFormatLite::
                   InternalWriteMessageNoVirtualToArray(
                       3, *entry, deterministic, target);
;
        if (entry->GetArena() != NULL) {
          entry.release();
        }
      }
    }
  }

  // map<uint64, uint64> map_uint64_uint64 = 4;
  if (!this->map_uint64_uint64().empty()) {
    typedef ::google::protobuf::Map< ::google::protobuf::uint64, ::google::protobuf::uint64 >::const_pointer
        ConstPtr;
    typedef ::google::protobuf::internal::SortItem< ::google::protobuf::uint64, ConstPtr > SortItem;
    typedef ::google::protobuf::internal::CompareByFirstField<SortItem> Less;

    if (deterministic &&
        this->map_uint64_uint64().size() > 1) {
      ::google::protobuf::scoped_array<SortItem> items(
          new SortItem[this->map_uint64_uint64().size()]);
      typedef ::google::protobuf::Map< ::google::protobuf::uint64, ::google::protobuf::uint64 >::size_type size_type;
      size_type n = 0;
      for (::google::protobuf::Map< ::google::protobuf::uint64, ::google::protobuf::uint64 >::const_iterator
          it = this->map_uint64_uint64().begin();
          it != this->map_uint64_uint64().end(); ++it, ++n) {
        items[n] = SortItem(&*it);
      }
      ::std::sort(&items[0], &items[n], Less());
      ::google::protobuf::scoped_ptr<TestArenaMap_MapUint64Uint64Entry> entry;
      for (size_type i = 0; i < n; i++) {
        entry.reset(map_uint64_uint64_.NewEntryWrapper(
            items[i].second->first, items[i].second->second));
        target = ::google::protobuf::internal::WireFormatLite::
                   InternalWriteMessageNoVirtualToArray(
                       4, *entry, deterministic, target);
;
        if (entry->GetArena() != NULL) {
          entry.release();
        }
      }
    } else {
      ::google::protobuf::scoped_ptr<TestArenaMap_MapUint64Uint64Entry> entry;
      for (::google::protobuf::Map< ::google::protobuf::uint64, ::google::protobuf::uint64 >::const_iterator
          it = this->map_uint64_uint64().begin();
          it != this->map_uint64_uint64().end(); ++it) {
        entry.reset(map_uint64_uint64_.NewEntryWrapper(
            it->first, it->second));
        target = ::google::protobuf::internal::WireFormatLite::
                   InternalWriteMessageNoVirtualToArray(
                       4, *entry, deterministic, target);
;
        if (entry->GetArena() != NULL) {
          entry.release();
        }
      }
    }
  }

  // map<sint32, sint32> map_sint32_sint32 = 5;
  if (!this->map_sint32_sint32().empty()) {
    typedef ::google::protobuf::Map< ::google::protobuf::int32, ::google::protobuf::int32 >::const_pointer
        ConstPtr;
    typedef ::google::protobuf::internal::SortItem< ::google::protobuf::int32, ConstPtr > SortItem;
    typedef ::google::protobuf::internal::CompareByFirstField<SortItem> Less;

    if (deterministic &&
        this->map_sint32_sint32().size() > 1) {
      ::google::protobuf::scoped_array<SortItem> items(
          new SortItem[this->map_sint32_sint32().size()]);
      typedef ::google::protobuf::Map< ::google::protobuf::int32, ::google::protobuf::int32 >::size_type size_type;
      size_type n = 0;
      for (::google::protobuf::Map< ::google::protobuf::int32, ::google::protobuf::int32 >::const_iterator
          it = this->map_sint32_sint32().begin();
          it != this->map_sint32_sint32().end(); ++it, ++n) {
        items[n] = SortItem(&*it);
      }
      ::std::sort(&items[0], &items[n], Less());
      ::google::protobuf::scoped_ptr<TestArenaMap_MapSint32Sint32Entry> entry;
      for (size_type i = 0; i < n; i++) {
        entry.reset(map_sint32_sint32_.NewEntryWrapper(
            items[i].second->first, items[i].second->second));
        target = ::google::protobuf::internal::WireFormatLite::
                   InternalWriteMessageNoVirtualToArray(
                       5, *entry, deterministic, target);
;
        if (entry->GetArena() != NULL) {
          entry.release();
        }
      }
    } else {
      ::google::protobuf::scoped_ptr<TestArenaMap_MapSint32Sint32Entry> entry;
      for (::google::protobuf::Map< ::google::protobuf::int32, ::google::protobuf::int32 >::const_iterator
          it = this->map_sint32_sint32().begin();
          it != this->map_sint32_sint32().end(); ++it) {
        entry.reset(map_sint32_sint32_.NewEntryWrapper(
            it->first, it->second));
        target = ::google::protobuf::internal::WireFormatLite::
                   InternalWriteMessageNoVirtualToArray(
                       5, *entry, deterministic, target);
;
        if (entry->GetArena() != NULL) {
          entry.release();
        }
      }
    }
  }

  // map<sint64, sint64> map_sint64_sint64 = 6;
  if (!this->map_sint64_sint64().empty()) {
    typedef ::google::protobuf::Map< ::google::protobuf::int64, ::google::protobuf::int64 >::const_pointer
        ConstPtr;
    typedef ::google::protobuf::internal::SortItem< ::google::protobuf::int64, ConstPtr > SortItem;
    typedef ::google::protobuf::internal::CompareByFirstField<SortItem> Less;

    if (deterministic &&
        this->map_sint64_sint64().size() > 1) {
      ::google::protobuf::scoped_array<SortItem> items(
          new SortItem[this->map_sint64_sint64().size()]);
      typedef ::google::protobuf::Map< ::google::protobuf::int64, ::google::protobuf::int64 >::size_type size_type;
      size_type n = 0;
      for (::google::protobuf::Map< ::google::protobuf::int64, ::google::protobuf::int64 >::const_iterator
          it = this->map_sint64_sint64().begin();
          it != this->map_sint64_sint64().end(); ++it, ++n) {
        items[n] = SortItem(&*it);
      }
      ::std::sort(&items[0], &items[n], Less());
      ::google::protobuf::scoped_ptr<TestArenaMap_MapSint64Sint64Entry> entry;
      for (size_type i = 0; i < n; i++) {
        entry.reset(map_sint64_sint64_.NewEntryWrapper(
            items[i].second->first, items[i].second->second));
        target = ::google::protobuf::internal::WireFormatLite::
                   InternalWriteMessageNoVirtualToArray(
                       6, *entry, deterministic, target);
;
        if (entry->GetArena() != NULL) {
          entry.release();
        }
      }
    } else {
      ::google::protobuf::scoped_ptr<TestArenaMap_MapSint64Sint64Entry> entry;
      for (::google::protobuf::Map< ::google::protobuf::int64, ::google::protobuf::int64 >::const_iterator
          it = this->map_sint64_sint64().begin();
          it != this->map_sint64_sint64().end(); ++it) {
        entry.reset(map_sint64_sint64_.NewEntryWrapper(
            it->first, it->second));
        target = ::google::protobuf::internal::WireFormatLite::
                   InternalWriteMessageNoVirtualToArray(
                       6, *entry, deterministic, target);
;
        if (entry->GetArena() != NULL) {
          entry.release();
        }
      }
    }
  }

  // map<fixed32, fixed32> map_fixed32_fixed32 = 7;
  if (!this->map_fixed32_fixed32().empty()) {
    typedef ::google::protobuf::Map< ::google::protobuf::uint32, ::google::protobuf::uint32 >::const_pointer
        ConstPtr;
    typedef ::google::protobuf::internal::SortItem< ::google::protobuf::uint32, ConstPtr > SortItem;
    typedef ::google::protobuf::internal::CompareByFirstField<SortItem> Less;

    if (deterministic &&
        this->map_fixed32_fixed32().size() > 1) {
      ::google::protobuf::scoped_array<SortItem> items(
          new SortItem[this->map_fixed32_fixed32().size()]);
      typedef ::google::protobuf::Map< ::google::protobuf::uint32, ::google::protobuf::uint32 >::size_type size_type;
      size_type n = 0;
      for (::google::protobuf::Map< ::google::protobuf::uint32, ::google::protobuf::uint32 >::const_iterator
          it = this->map_fixed32_fixed32().begin();
          it != this->map_fixed32_fixed32().end(); ++it, ++n) {
        items[n] = SortItem(&*it);
      }
      ::std::sort(&items[0], &items[n], Less());
      ::google::protobuf::scoped_ptr<TestArenaMap_MapFixed32Fixed32Entry> entry;
      for (size_type i = 0; i < n; i++) {
        entry.reset(map_fixed32_fixed32_.NewEntryWrapper(
            items[i].second->first, items[i].second->second));
        target = ::google::protobuf::internal::WireFormatLite::
                   InternalWriteMessageNoVirtualToArray(
                       7, *entry, deterministic, target);
;
        if (entry->GetArena() != NULL) {
          entry.release();
        }
      }
    } else {
      ::google::protobuf::scoped_ptr<TestArenaMap_MapFixed32Fixed32Entry> entry;
      for (::google::protobuf::Map< ::google::protobuf::uint32, ::google::protobuf::uint32 >::const_iterator
          it = this->map_fixed32_fixed32().begin();
          it != this->map_fixed32_fixed32().end(); ++it) {
        entry.reset(map_fixed32_fixed32_.NewEntryWrapper(
            it->first, it->second));
        target = ::google::protobuf::internal::WireFormatLite::
                   InternalWriteMessageNoVirtualToArray(
                       7, *entry, deterministic, target);
;
        if (entry->GetArena() != NULL) {
          entry.release();
        }
      }
    }
  }

  // map<fixed64, fixed64> map_fixed64_fixed64 = 8;
  if (!this->map_fixed64_fixed64().empty()) {
    typedef ::google::protobuf::Map< ::google::protobuf::uint64, ::google::protobuf::uint64 >::const_pointer
        ConstPtr;
    typedef ::google::protobuf::internal::SortItem< ::google::protobuf::uint64, ConstPtr > SortItem;
    typedef ::google::protobuf::internal::CompareByFirstField<SortItem> Less;

    if (deterministic &&
        this->map_fixed64_fixed64().size() > 1) {
      ::google::protobuf::scoped_array<SortItem> items(
          new SortItem[this->map_fixed64_fixed64().size()]);
      typedef ::google::protobuf::Map< ::google::protobuf::uint64, ::google::protobuf::uint64 >::size_type size_type;
      size_type n = 0;
      for (::google::protobuf::Map< ::google::protobuf::uint64, ::google::protobuf::uint64 >::const_iterator
          it = this->map_fixed64_fixed64().begin();
          it != this->map_fixed64_fixed64().end(); ++it, ++n) {
        items[n] = SortItem(&*it);
      }
      ::std::sort(&items[0], &items[n], Less());
      ::google::protobuf::scoped_ptr<TestArenaMap_MapFixed64Fixed64Entry> entry;
      for (size_type i = 0; i < n; i++) {
        entry.reset(map_fixed64_fixed64_.NewEntryWrapper(
            items[i].second->first, items[i].second->second));
        target = ::google::protobuf::internal::WireFormatLite::
                   InternalWriteMessageNoVirtualToArray(
                       8, *entry, deterministic, target);
;
        if (entry->GetArena() != NULL) {
          entry.release();
        }
      }
    } else {
      ::google::protobuf::scoped_ptr<TestArenaMap_MapFixed64Fixed64Entry> entry;
      for (::google::protobuf::Map< ::google::protobuf::uint64, ::google::protobuf::uint64 >::const_iterator
          it = this->map_fixed64_fixed64().begin();
          it != this->map_fixed64_fixed64().end(); ++it) {
        entry.reset(map_fixed64_fixed64_.NewEntryWrapper(
            it->first, it->second));
        target = ::google::protobuf::internal::WireFormatLite::
                   InternalWriteMessageNoVirtualToArray(
                       8, *entry, deterministic, target);
;
        if (entry->GetArena() != NULL) {
          entry.release();
        }
      }
    }
  }

  // map<sfixed32, sfixed32> map_sfixed32_sfixed32 = 9;
  if (!this->map_sfixed32_sfixed32().empty()) {
    typedef ::google::protobuf::Map< ::google::protobuf::int32, ::google::protobuf::int32 >::const_pointer
        ConstPtr;
    typedef ::google::protobuf::internal::SortItem< ::google::protobuf::int32, ConstPtr > SortItem;
    typedef ::google::protobuf::internal::CompareByFirstField<SortItem> Less;

    if (deterministic &&
        this->map_sfixed32_sfixed32().size() > 1) {
      ::google::protobuf::scoped_array<SortItem> items(
          new SortItem[this->map_sfixed32_sfixed32().size()]);
      typedef ::google::protobuf::Map< ::google::protobuf::int32, ::google::protobuf::int32 >::size_type size_type;
      size_type n = 0;
      for (::google::protobuf::Map< ::google::protobuf::int32, ::google::protobuf::int32 >::const_iterator
          it = this->map_sfixed32_sfixed32().begin();
          it != this->map_sfixed32_sfixed32().end(); ++it, ++n) {
        items[n] = SortItem(&*it);
      }
      ::std::sort(&items[0], &items[n], Less());
      ::google::protobuf::scoped_ptr<TestArenaMap_MapSfixed32Sfixed32Entry> entry;
      for (size_type i = 0; i < n; i++) {
        entry.reset(map_sfixed32_sfixed32_.NewEntryWrapper(
            items[i].second->first, items[i].second->second));
        target = ::google::protobuf::internal::WireFormatLite::
                   InternalWriteMessageNoVirtualToArray(
                       9, *entry, deterministic, target);
;
        if (entry->GetArena() != NULL) {
          entry.release();
        }
      }
    } else {
      ::google::protobuf::scoped_ptr<TestArenaMap_MapSfixed32Sfixed32Entry> entry;
      for (::google::protobuf::Map< ::google::protobuf::int32, ::google::protobuf::int32 >::const_iterator
          it = this->map_sfixed32_sfixed32().begin();
          it != this->map_sfixed32_sfixed32().end(); ++it) {
        entry.reset(map_sfixed32_sfixed32_.NewEntryWrapper(
            it->first, it->second));
        target = ::google::protobuf::internal::WireFormatLite::
                   InternalWriteMessageNoVirtualToArray(
                       9, *entry, deterministic, target);
;
        if (entry->GetArena() != NULL) {
          entry.release();
        }
      }
    }
  }

  // map<sfixed64, sfixed64> map_sfixed64_sfixed64 = 10;
  if (!this->map_sfixed64_sfixed64().empty()) {
    typedef ::google::protobuf::Map< ::google::protobuf::int64, ::google::protobuf::int64 >::const_pointer
        ConstPtr;
    typedef ::google::protobuf::internal::SortItem< ::google::protobuf::int64, ConstPtr > SortItem;
    typedef ::google::protobuf::internal::CompareByFirstField<SortItem> Less;

    if (deterministic &&
        this->map_sfixed64_sfixed64().size() > 1) {
      ::google::protobuf::scoped_array<SortItem> items(
          new SortItem[this->map_sfixed64_sfixed64().size()]);
      typedef ::google::protobuf::Map< ::google::protobuf::int64, ::google::protobuf::int64 >::size_type size_type;
      size_type n = 0;
      for (::google::protobuf::Map< ::google::protobuf::int64, ::google::protobuf::int64 >::const_iterator
          it = this->map_sfixed64_sfixed64().begin();
          it != this->map_sfixed64_sfixed64().end(); ++it, ++n) {
        items[n] = SortItem(&*it);
      }
      ::std::sort(&items[0], &items[n], Less());
      ::google::protobuf::scoped_ptr<TestArenaMap_MapSfixed64Sfixed64Entry> entry;
      for (size_type i = 0; i < n; i++) {
        entry.reset(map_sfixed64_sfixed64_.NewEntryWrapper(
            items[i].second->first, items[i].second->second));
        target = ::google::protobuf::internal::WireFormatLite::
                   InternalWriteMessageNoVirtualToArray(
                       10, *entry, deterministic, target);
;
        if (entry->GetArena() != NULL) {
          entry.release();
        }
      }
    } else {
      ::google::protobuf::scoped_ptr<TestArenaMap_MapSfixed64Sfixed64Entry> entry;
      for (::google::protobuf::Map< ::google::protobuf::int64, ::google::protobuf::int64 >::const_iterator
          it = this->map_sfixed64_sfixed64().begin();
          it != this->map_sfixed64_sfixed64().end(); ++it) {
        entry.reset(map_sfixed64_sfixed64_.NewEntryWrapper(
            it->first, it->second));
        target = ::google::protobuf::internal::WireFormatLite::
                   InternalWriteMessageNoVirtualToArray(
                       10, *entry, deterministic, target);
;
        if (entry->GetArena() != NULL) {
          entry.release();
        }
      }
    }
  }

  // map<int32, float> map_int32_float = 11;
  if (!this->map_int32_float().empty()) {
    typedef ::google::protobuf::Map< ::google::protobuf::int32, float >::const_pointer
        ConstPtr;
    typedef ::google::protobuf::internal::SortItem< ::google::protobuf::int32, ConstPtr > SortItem;
    typedef ::google::protobuf::internal::CompareByFirstField<SortItem> Less;

    if (deterministic &&
        this->map_int32_float().size() > 1) {
      ::google::protobuf::scoped_array<SortItem> items(
          new SortItem[this->map_int32_float().size()]);
      typedef ::google::protobuf::Map< ::google::protobuf::int32, float >::size_type size_type;
      size_type n = 0;
      for (::google::protobuf::Map< ::google::protobuf::int32, float >::const_iterator
          it = this->map_int32_float().begin();
          it != this->map_int32_float().end(); ++it, ++n) {
        items[n] = SortItem(&*it);
      }
      ::std::sort(&items[0], &items[n], Less());
      ::google::protobuf::scoped_ptr<TestArenaMap_MapInt32FloatEntry> entry;
      for (size_type i = 0; i < n; i++) {
        entry.reset(map_int32_float_.NewEntryWrapper(
            items[i].second->first, items[i].second->second));
        target = ::google::protobuf::internal::WireFormatLite::
                   InternalWriteMessageNoVirtualToArray(
                       11, *entry, deterministic, target);
;
        if (entry->GetArena() != NULL) {
          entry.release();
        }
      }
    } else {
      ::google::protobuf::scoped_ptr<TestArenaMap_MapInt32FloatEntry> entry;
      for (::google::protobuf::Map< ::google::protobuf::int32, float >::const_iterator
          it = this->map_int32_float().begin();
          it != this->map_int32_float().end(); ++it) {
        entry.reset(map_int32_float_.NewEntryWrapper(
            it->first, it->second));
        target = ::google::protobuf::internal::WireFormatLite::
                   InternalWriteMessageNoVirtualToArray(
                       11, *entry, deterministic, target);
;
        if (entry->GetArena() != NULL) {
          entry.release();
        }
      }
    }
  }

  // map<int32, double> map_int32_double = 12;
  if (!this->map_int32_double().empty()) {
    typedef ::google::protobuf::Map< ::google::protobuf::int32, double >::const_pointer
        ConstPtr;
    typedef ::google::protobuf::internal::SortItem< ::google::protobuf::int32, ConstPtr > SortItem;
    typedef ::google::protobuf::internal::CompareByFirstField<SortItem> Less;

    if (deterministic &&
        this->map_int32_double().size() > 1) {
      ::google::protobuf::scoped_array<SortItem> items(
          new SortItem[this->map_int32_double().size()]);
      typedef ::google::protobuf::Map< ::google::protobuf::int32, double >::size_type size_type;
      size_type n = 0;
      for (::google::protobuf::Map< ::google::protobuf::int32, double >::const_iterator
          it = this->map_int32_double().begin();
          it != this->map_int32_double().end(); ++it, ++n) {
        items[n] = SortItem(&*it);
      }
      ::std::sort(&items[0], &items[n], Less());
      ::google::protobuf::scoped_ptr<TestArenaMap_MapInt32DoubleEntry> entry;
      for (size_type i = 0; i < n; i++) {
        entry.reset(map_int32_double_.NewEntryWrapper(
            items[i].second->first, items[i].second->second));
        target = ::google::protobuf::internal::WireFormatLite::
                   InternalWriteMessageNoVirtualToArray(
                       12, *entry, deterministic, target);
;
        if (entry->GetArena() != NULL) {
          entry.release();
        }
      }
    } else {
      ::google::protobuf::scoped_ptr<TestArenaMap_MapInt32DoubleEntry> entry;
      for (::google::protobuf::Map< ::google::protobuf::int32, double >::const_iterator
          it = this->map_int32_double().begin();
          it != this->map_int32_double().end(); ++it) {
        entry.reset(map_int32_double_.NewEntryWrapper(
            it->first, it->second));
        target = ::google::protobuf::internal::WireFormatLite::
                   InternalWriteMessageNoVirtualToArray(
                       12, *entry, deterministic, target);
;
        if (entry->GetArena() != NULL) {
          entry.release();
        }
      }
    }
  }

  // map<bool, bool> map_bool_bool = 13;
  if (!this->map_bool_bool().empty()) {
    typedef ::google::protobuf::Map< bool, bool >::const_pointer
        ConstPtr;
    typedef ::google::protobuf::internal::SortItem< bool, ConstPtr > SortItem;
    typedef ::google::protobuf::internal::CompareByFirstField<SortItem> Less;

    if (deterministic &&
        this->map_bool_bool().size() > 1) {
      ::google::protobuf::scoped_array<SortItem> items(
          new SortItem[this->map_bool_bool().size()]);
      typedef ::google::protobuf::Map< bool, bool >::size_type size_type;
      size_type n = 0;
      for (::google::protobuf::Map< bool, bool >::const_iterator
          it = this->map_bool_bool().begin();
          it != this->map_bool_bool().end(); ++it, ++n) {
        items[n] = SortItem(&*it);
      }
      ::std::sort(&items[0], &items[n], Less());
      ::google::protobuf::scoped_ptr<TestArenaMap_MapBoolBoolEntry> entry;
      for (size_type i = 0; i < n; i++) {
        entry.reset(map_bool_bool_.NewEntryWrapper(
            items[i].second->first, items[i].second->second));
        target = ::google::protobuf::internal::WireFormatLite::
                   InternalWriteMessageNoVirtualToArray(
                       13, *entry, deterministic, target);
;
        if (entry->GetArena() != NULL) {
          entry.release();
        }
      }
    } else {
      ::google::protobuf::scoped_ptr<TestArenaMap_MapBoolBoolEntry> entry;
      for (::google::protobuf::Map< bool, bool >::const_iterator
          it = this->map_bool_bool().begin();
          it != this->map_bool_bool().end(); ++it) {
        entry.reset(map_bool_bool_.NewEntryWrapper(
            it->first, it->second));
        target = ::google::protobuf::internal::WireFormatLite::
                   InternalWriteMessageNoVirtualToArray(
                       13, *entry, deterministic, target);
;
        if (entry->GetArena() != NULL) {
          entry.release();
        }
      }
    }
  }

  // map<string, string> map_string_string = 14;
  if (!this->map_string_string().empty()) {
    typedef ::google::protobuf::Map< ::std::string, ::std::string >::const_pointer
        ConstPtr;
    typedef ConstPtr SortItem;
    typedef ::google::protobuf::internal::CompareByDerefFirst<SortItem> Less;
    struct Utf8Check {
      static void Check(ConstPtr p) {
        ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
          p->first.data(), p->first.length(),
          ::google::protobuf::internal::WireFormatLite::SERIALIZE,
          "protobuf_unittest.TestArenaMap.MapStringStringEntry.key");
        ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
          p->second.data(), p->second.length(),
          ::google::protobuf::internal::WireFormatLite::SERIALIZE,
          "protobuf_unittest.TestArenaMap.MapStringStringEntry.value");
      }
    };

    if (deterministic &&
        this->map_string_string().size() > 1) {
      ::google::protobuf::scoped_array<SortItem> items(
          new SortItem[this->map_string_string().size()]);
      typedef ::google::protobuf::Map< ::std::string, ::std::string >::size_type size_type;
      size_type n = 0;
      for (::google::protobuf::Map< ::std::string, ::std::string >::const_iterator
          it = this->map_string_string().begin();
          it != this->map_string_string().end(); ++it, ++n) {
        items[n] = SortItem(&*it);
      }
      ::std::sort(&items[0], &items[n], Less());
      ::google::protobuf::scoped_ptr<TestArenaMap_MapStringStringEntry> entry;
      for (size_type i = 0; i < n; i++) {
        entry.reset(map_string_string_.NewEntryWrapper(
            items[i]->first, items[i]->second));
        target = ::google::protobuf::internal::WireFormatLite::
                   InternalWriteMessageNoVirtualToArray(
                       14, *entry, deterministic, target);
;
        if (entry->GetArena() != NULL) {
          entry.release();
        }
        Utf8Check::Check(items[i]);
      }
    } else {
      ::google::protobuf::scoped_ptr<TestArenaMap_MapStringStringEntry> entry;
      for (::google::protobuf::Map< ::std::string, ::std::string >::const_iterator
          it = this->map_string_string().begin();
          it != this->map_string_string().end(); ++it) {
        entry.reset(map_string_string_.NewEntryWrapper(
            it->first, it->second));
        target = ::google::protobuf::internal::WireFormatLite::
                   InternalWriteMessageNoVirtualToArray(
                       14, *entry, deterministic, target);
;
        if (entry->GetArena() != NULL) {
          entry.release();
        }
        Utf8Check::Check(&*it);
      }
    }
  }

  // map<int32, bytes> map_int32_bytes = 15;
  if (!this->map_int32_bytes().empty()) {
    typedef ::google::protobuf::Map< ::google::protobuf::int32, ::std::string >::const_pointer
        ConstPtr;
    typedef ::google::protobuf::internal::SortItem< ::google::protobuf::int32, ConstPtr > SortItem;
    typedef ::google::protobuf::internal::CompareByFirstField<SortItem> Less;

    if (deterministic &&
        this->map_int32_bytes().size() > 1) {
      ::google::protobuf::scoped_array<SortItem> items(
          new SortItem[this->map_int32_bytes().size()]);
      typedef ::google::protobuf::Map< ::google::protobuf::int32, ::std::string >::size_type size_type;
      size_type n = 0;
      for (::google::protobuf::Map< ::google::protobuf::int32, ::std::string >::const_iterator
          it = this->map_int32_bytes().begin();
          it != this->map_int32_bytes().end(); ++it, ++n) {
        items[n] = SortItem(&*it);
      }
      ::std::sort(&items[0], &items[n], Less());
      ::google::protobuf::scoped_ptr<TestArenaMap_MapInt32BytesEntry> entry;
      for (size_type i = 0; i < n; i++) {
        entry.reset(map_int32_bytes_.NewEntryWrapper(
            items[i].second->first, items[i].second->second));
        target = ::google::protobuf::internal::WireFormatLite::
                   InternalWriteMessageNoVirtualToArray(
                       15, *entry, deterministic, target);
;
        if (entry->GetArena() != NULL) {
          entry.release();
        }
      }
    } else {
      ::google::protobuf::scoped_ptr<TestArenaMap_MapInt32BytesEntry> entry;
      for (::google::protobuf::Map< ::google::protobuf::int32, ::std::string >::const_iterator
          it = this->map_int32_bytes().begin();
          it != this->map_int32_bytes().end(); ++it) {
        entry.reset(map_int32_bytes_.NewEntryWrapper(
            it->first, it->second));
        target = ::google::protobuf::internal::WireFormatLite::
                   InternalWriteMessageNoVirtualToArray(
                       15, *entry, deterministic, target);
;
        if (entry->GetArena() != NULL) {
          entry.release();
        }
      }
    }
  }

  // map<int32, .protobuf_unittest.MapEnum> map_int32_enum = 16;
  if (!this->map_int32_enum().empty()) {
    typedef ::google::protobuf::Map< ::google::protobuf::int32, ::protobuf_unittest::MapEnum >::const_pointer
        ConstPtr;
    typedef ::google::protobuf::internal::SortItem< ::google::protobuf::int32, ConstPtr > SortItem;
    typedef ::google::protobuf::internal::CompareByFirstField<SortItem> Less;

    if (deterministic &&
        this->map_int32_enum().size() > 1) {
      ::google::protobuf::scoped_array<SortItem> items(
          new SortItem[this->map_int32_enum().size()]);
      typedef ::google::protobuf::Map< ::google::protobuf::int32, ::protobuf_unittest::MapEnum >::size_type size_type;
      size_type n = 0;
      for (::google::protobuf::Map< ::google::protobuf::int32, ::protobuf_unittest::MapEnum >::const_iterator
          it = this->map_int32_enum().begin();
          it != this->map_int32_enum().end(); ++it, ++n) {
        items[n] = SortItem(&*it);
      }
      ::std::sort(&items[0], &items[n], Less());
      ::google::protobuf::scoped_ptr<TestArenaMap_MapInt32EnumEntry> entry;
      for (size_type i = 0; i < n; i++) {
        entry.reset(map_int32_enum_.NewEnumEntryWrapper(
            items[i].second->first, items[i].second->second));
        target = ::google::protobuf::internal::WireFormatLite::
                   InternalWriteMessageNoVirtualToArray(
                       16, *entry, deterministic, target);
;
        if (entry->GetArena() != NULL) {
          entry.release();
        }
      }
    } else {
      ::google::protobuf::scoped_ptr<TestArenaMap_MapInt32EnumEntry> entry;
      for (::google::protobuf::Map< ::google::protobuf::int32, ::protobuf_unittest::MapEnum >::const_iterator
          it = this->map_int32_enum().begin();
          it != this->map_int32_enum().end(); ++it) {
        entry.reset(map_int32_enum_.NewEnumEntryWrapper(
            it->first, it->second));
        target = ::google::protobuf::internal::WireFormatLite::
                   InternalWriteMessageNoVirtualToArray(
                       16, *entry, deterministic, target);
;
        if (entry->GetArena() != NULL) {
          entry.release();
        }
      }
    }
  }

  // map<int32, .protobuf_unittest.ForeignMessage> map_int32_foreign_message = 17;
  if (!this->map_int32_foreign_message().empty()) {
    typedef ::google::protobuf::Map< ::google::protobuf::int32, ::protobuf_unittest::ForeignMessage >::const_pointer
        ConstPtr;
    typedef ::google::protobuf::internal::SortItem< ::google::protobuf::int32, ConstPtr > SortItem;
    typedef ::google::protobuf::internal::CompareByFirstField<SortItem> Less;

    if (deterministic &&
        this->map_int32_foreign_message().size() > 1) {
      ::google::protobuf::scoped_array<SortItem> items(
          new SortItem[this->map_int32_foreign_message().size()]);
      typedef ::google::protobuf::Map< ::google::protobuf::int32, ::protobuf_unittest::ForeignMessage >::size_type size_type;
      size_type n = 0;
      for (::google::protobuf::Map< ::google::protobuf::int32, ::protobuf_unittest::ForeignMessage >::const_iterator
          it = this->map_int32_foreign_message().begin();
          it != this->map_int32_foreign_message().end(); ++it, ++n) {
        items[n] = SortItem(&*it);
      }
      ::std::sort(&items[0], &items[n], Less());
      ::google::protobuf::scoped_ptr<TestArenaMap_MapInt32ForeignMessageEntry> entry;
      for (size_type i = 0; i < n; i++) {
        entry.reset(map_int32_foreign_message_.NewEntryWrapper(
            items[i].second->first, items[i].second->second));
        target = ::google::protobuf::internal::WireFormatLite::
                   InternalWriteMessageNoVirtualToArray(
                       17, *entry, deterministic, target);
;
        if (entry->GetArena() != NULL) {
          entry.release();
        }
      }
    } else {
      ::google::protobuf::scoped_ptr<TestArenaMap_MapInt32ForeignMessageEntry> entry;
      for (::google::protobuf::Map< ::google::protobuf::int32, ::protobuf_unittest::ForeignMessage >::const_iterator
          it = this->map_int32_foreign_message().begin();
          it != this->map_int32_foreign_message().end(); ++it) {
        entry.reset(map_int32_foreign_message_.NewEntryWrapper(
            it->first, it->second));
        target = ::google::protobuf::internal::WireFormatLite::
                   InternalWriteMessageNoVirtualToArray(
                       17, *entry, deterministic, target);
;
        if (entry->GetArena() != NULL) {
          entry.release();
        }
      }
    }
  }

  // map<int32, .protobuf_unittest_no_arena.ForeignMessage> map_int32_foreign_message_no_arena = 18;
  if (!this->map_int32_foreign_message_no_arena().empty()) {
    typedef ::google::protobuf::Map< ::google::protobuf::int32, ::protobuf_unittest_no_arena::ForeignMessage >::const_pointer
        ConstPtr;
    typedef ::google::protobuf::internal::SortItem< ::google::protobuf::int32, ConstPtr > SortItem;
    typedef ::google::protobuf::internal::CompareByFirstField<SortItem> Less;

    if (deterministic &&
        this->map_int32_foreign_message_no_arena().size() > 1) {
      ::google::protobuf::scoped_array<SortItem> items(
          new SortItem[this->map_int32_foreign_message_no_arena().size()]);
      typedef ::google::protobuf::Map< ::google::protobuf::int32, ::protobuf_unittest_no_arena::ForeignMessage >::size_type size_type;
      size_type n = 0;
      for (::google::protobuf::Map< ::google::protobuf::int32, ::protobuf_unittest_no_arena::ForeignMessage >::const_iterator
          it = this->map_int32_foreign_message_no_arena().begin();
          it != this->map_int32_foreign_message_no_arena().end(); ++it, ++n) {
        items[n] = SortItem(&*it);
      }
      ::std::sort(&items[0], &items[n], Less());
      ::google::protobuf::scoped_ptr<TestArenaMap_MapInt32ForeignMessageNoArenaEntry> entry;
      for (size_type i = 0; i < n; i++) {
        entry.reset(map_int32_foreign_message_no_arena_.NewEntryWrapper(
            items[i].second->first, items[i].second->second));
        target = ::google::protobuf::internal::WireFormatLite::
                   InternalWriteMessageNoVirtualToArray(
                       18, *entry, deterministic, target);
;
        if (entry->GetArena() != NULL) {
          entry.release();
        }
      }
    } else {
      ::google::protobuf::scoped_ptr<TestArenaMap_MapInt32ForeignMessageNoArenaEntry> entry;
      for (::google::protobuf::Map< ::google::protobuf::int32, ::protobuf_unittest_no_arena::ForeignMessage >::const_iterator
          it = this->map_int32_foreign_message_no_arena().begin();
          it != this->map_int32_foreign_message_no_arena().end(); ++it) {
        entry.reset(map_int32_foreign_message_no_arena_.NewEntryWrapper(
            it->first, it->second));
        target = ::google::protobuf::internal::WireFormatLite::
                   InternalWriteMessageNoVirtualToArray(
                       18, *entry, deterministic, target);
;
        if (entry->GetArena() != NULL) {
          entry.release();
        }
      }
    }
  }

  // @@protoc_insertion_point(serialize_to_array_end:protobuf_unittest.TestArenaMap)
  return target;
}

size_t TestArenaMap::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:protobuf_unittest.TestArenaMap)
  size_t total_size = 0;

  // map<int32, int32> map_int32_int32 = 1;
  total_size += 1 *
      ::google::protobuf::internal::FromIntSize(this->map_int32_int32_size());
  {
    ::google::protobuf::scoped_ptr<TestArenaMap_MapInt32Int32Entry> entry;
    for (::google::protobuf::Map< ::google::protobuf::int32, ::google::protobuf::int32 >::const_iterator
        it = this->map_int32_int32().begin();
        it != this->map_int32_int32().end(); ++it) {
      if (entry.get() != NULL && entry->GetArena() != NULL) {
        entry.release();
      }
      entry.reset(map_int32_int32_.NewEntryWrapper(it->first, it->second));
      total_size += ::google::protobuf::internal::WireFormatLite::
          MessageSizeNoVirtual(*entry);
    }
    if (entry.get() != NULL && entry->GetArena() != NULL) {
      entry.release();
    }
  }

  // map<int64, int64> map_int64_int64 = 2;
  total_size += 1 *
      ::google::protobuf::internal::FromIntSize(this->map_int64_int64_size());
  {
    ::google::protobuf::scoped_ptr<TestArenaMap_MapInt64Int64Entry> entry;
    for (::google::protobuf::Map< ::google::protobuf::int64, ::google::protobuf::int64 >::const_iterator
        it = this->map_int64_int64().begin();
        it != this->map_int64_int64().end(); ++it) {
      if (entry.get() != NULL && entry->GetArena() != NULL) {
        entry.release();
      }
      entry.reset(map_int64_int64_.NewEntryWrapper(it->first, it->second));
      total_size += ::google::protobuf::internal::WireFormatLite::
          MessageSizeNoVirtual(*entry);
    }
    if (entry.get() != NULL && entry->GetArena() != NULL) {
      entry.release();
    }
  }

  // map<uint32, uint32> map_uint32_uint32 = 3;
  total_size += 1 *
      ::google::protobuf::internal::FromIntSize(this->map_uint32_uint32_size());
  {
    ::google::protobuf::scoped_ptr<TestArenaMap_MapUint32Uint32Entry> entry;
    for (::google::protobuf::Map< ::google::protobuf::uint32, ::google::protobuf::uint32 >::const_iterator
        it = this->map_uint32_uint32().begin();
        it != this->map_uint32_uint32().end(); ++it) {
      if (entry.get() != NULL && entry->GetArena() != NULL) {
        entry.release();
      }
      entry.reset(map_uint32_uint32_.NewEntryWrapper(it->first, it->second));
      total_size += ::google::protobuf::internal::WireFormatLite::
          MessageSizeNoVirtual(*entry);
    }
    if (entry.get() != NULL && entry->GetArena() != NULL) {
      entry.release();
    }
  }

  // map<uint64, uint64> map_uint64_uint64 = 4;
  total_size += 1 *
      ::google::protobuf::internal::FromIntSize(this->map_uint64_uint64_size());
  {
    ::google::protobuf::scoped_ptr<TestArenaMap_MapUint64Uint64Entry> entry;
    for (::google::protobuf::Map< ::google::protobuf::uint64, ::google::protobuf::uint64 >::const_iterator
        it = this->map_uint64_uint64().begin();
        it != this->map_uint64_uint64().end(); ++it) {
      if (entry.get() != NULL && entry->GetArena() != NULL) {
        entry.release();
      }
      entry.reset(map_uint64_uint64_.NewEntryWrapper(it->first, it->second));
      total_size += ::google::protobuf::internal::WireFormatLite::
          MessageSizeNoVirtual(*entry);
    }
    if (entry.get() != NULL && entry->GetArena() != NULL) {
      entry.release();
    }
  }

  // map<sint32, sint32> map_sint32_sint32 = 5;
  total_size += 1 *
      ::google::protobuf::internal::FromIntSize(this->map_sint32_sint32_size());
  {
    ::google::protobuf::scoped_ptr<TestArenaMap_MapSint32Sint32Entry> entry;
    for (::google::protobuf::Map< ::google::protobuf::int32, ::google::protobuf::int32 >::const_iterator
        it = this->map_sint32_sint32().begin();
        it != this->map_sint32_sint32().end(); ++it) {
      if (entry.get() != NULL && entry->GetArena() != NULL) {
        entry.release();
      }
      entry.reset(map_sint32_sint32_.NewEntryWrapper(it->first, it->second));
      total_size += ::google::protobuf::internal::WireFormatLite::
          MessageSizeNoVirtual(*entry);
    }
    if (entry.get() != NULL && entry->GetArena() != NULL) {
      entry.release();
    }
  }

  // map<sint64, sint64> map_sint64_sint64 = 6;
  total_size += 1 *
      ::google::protobuf::internal::FromIntSize(this->map_sint64_sint64_size());
  {
    ::google::protobuf::scoped_ptr<TestArenaMap_MapSint64Sint64Entry> entry;
    for (::google::protobuf::Map< ::google::protobuf::int64, ::google::protobuf::int64 >::const_iterator
        it = this->map_sint64_sint64().begin();
        it != this->map_sint64_sint64().end(); ++it) {
      if (entry.get() != NULL && entry->GetArena() != NULL) {
        entry.release();
      }
      entry.reset(map_sint64_sint64_.NewEntryWrapper(it->first, it->second));
      total_size += ::google::protobuf::internal::WireFormatLite::
          MessageSizeNoVirtual(*entry);
    }
    if (entry.get() != NULL && entry->GetArena() != NULL) {
      entry.release();
    }
  }

  // map<fixed32, fixed32> map_fixed32_fixed32 = 7;
  total_size += 1 *
      ::google::protobuf::internal::FromIntSize(this->map_fixed32_fixed32_size());
  {
    ::google::protobuf::scoped_ptr<TestArenaMap_MapFixed32Fixed32Entry> entry;
    for (::google::protobuf::Map< ::google::protobuf::uint32, ::google::protobuf::uint32 >::const_iterator
        it = this->map_fixed32_fixed32().begin();
        it != this->map_fixed32_fixed32().end(); ++it) {
      if (entry.get() != NULL && entry->GetArena() != NULL) {
        entry.release();
      }
      entry.reset(map_fixed32_fixed32_.NewEntryWrapper(it->first, it->second));
      total_size += ::google::protobuf::internal::WireFormatLite::
          MessageSizeNoVirtual(*entry);
    }
    if (entry.get() != NULL && entry->GetArena() != NULL) {
      entry.release();
    }
  }

  // map<fixed64, fixed64> map_fixed64_fixed64 = 8;
  total_size += 1 *
      ::google::protobuf::internal::FromIntSize(this->map_fixed64_fixed64_size());
  {
    ::google::protobuf::scoped_ptr<TestArenaMap_MapFixed64Fixed64Entry> entry;
    for (::google::protobuf::Map< ::google::protobuf::uint64, ::google::protobuf::uint64 >::const_iterator
        it = this->map_fixed64_fixed64().begin();
        it != this->map_fixed64_fixed64().end(); ++it) {
      if (entry.get() != NULL && entry->GetArena() != NULL) {
        entry.release();
      }
      entry.reset(map_fixed64_fixed64_.NewEntryWrapper(it->first, it->second));
      total_size += ::google::protobuf::internal::WireFormatLite::
          MessageSizeNoVirtual(*entry);
    }
    if (entry.get() != NULL && entry->GetArena() != NULL) {
      entry.release();
    }
  }

  // map<sfixed32, sfixed32> map_sfixed32_sfixed32 = 9;
  total_size += 1 *
      ::google::protobuf::internal::FromIntSize(this->map_sfixed32_sfixed32_size());
  {
    ::google::protobuf::scoped_ptr<TestArenaMap_MapSfixed32Sfixed32Entry> entry;
    for (::google::protobuf::Map< ::google::protobuf::int32, ::google::protobuf::int32 >::const_iterator
        it = this->map_sfixed32_sfixed32().begin();
        it != this->map_sfixed32_sfixed32().end(); ++it) {
      if (entry.get() != NULL && entry->GetArena() != NULL) {
        entry.release();
      }
      entry.reset(map_sfixed32_sfixed32_.NewEntryWrapper(it->first, it->second));
      total_size += ::google::protobuf::internal::WireFormatLite::
          MessageSizeNoVirtual(*entry);
    }
    if (entry.get() != NULL && entry->GetArena() != NULL) {
      entry.release();
    }
  }

  // map<sfixed64, sfixed64> map_sfixed64_sfixed64 = 10;
  total_size += 1 *
      ::google::protobuf::internal::FromIntSize(this->map_sfixed64_sfixed64_size());
  {
    ::google::protobuf::scoped_ptr<TestArenaMap_MapSfixed64Sfixed64Entry> entry;
    for (::google::protobuf::Map< ::google::protobuf::int64, ::google::protobuf::int64 >::const_iterator
        it = this->map_sfixed64_sfixed64().begin();
        it != this->map_sfixed64_sfixed64().end(); ++it) {
      if (entry.get() != NULL && entry->GetArena() != NULL) {
        entry.release();
      }
      entry.reset(map_sfixed64_sfixed64_.NewEntryWrapper(it->first, it->second));
      total_size += ::google::protobuf::internal::WireFormatLite::
          MessageSizeNoVirtual(*entry);
    }
    if (entry.get() != NULL && entry->GetArena() != NULL) {
      entry.release();
    }
  }

  // map<int32, float> map_int32_float = 11;
  total_size += 1 *
      ::google::protobuf::internal::FromIntSize(this->map_int32_float_size());
  {
    ::google::protobuf::scoped_ptr<TestArenaMap_MapInt32FloatEntry> entry;
    for (::google::protobuf::Map< ::google::protobuf::int32, float >::const_iterator
        it = this->map_int32_float().begin();
        it != this->map_int32_float().end(); ++it) {
      if (entry.get() != NULL && entry->GetArena() != NULL) {
        entry.release();
      }
      entry.reset(map_int32_float_.NewEntryWrapper(it->first, it->second));
      total_size += ::google::protobuf::internal::WireFormatLite::
          MessageSizeNoVirtual(*entry);
    }
    if (entry.get() != NULL && entry->GetArena() != NULL) {
      entry.release();
    }
  }

  // map<int32, double> map_int32_double = 12;
  total_size += 1 *
      ::google::protobuf::internal::FromIntSize(this->map_int32_double_size());
  {
    ::google::protobuf::scoped_ptr<TestArenaMap_MapInt32DoubleEntry> entry;
    for (::google::protobuf::Map< ::google::protobuf::int32, double >::const_iterator
        it = this->map_int32_double().begin();
        it != this->map_int32_double().end(); ++it) {
      if (entry.get() != NULL && entry->GetArena() != NULL) {
        entry.release();
      }
      entry.reset(map_int32_double_.NewEntryWrapper(it->first, it->second));
      total_size += ::google::protobuf::internal::WireFormatLite::
          MessageSizeNoVirtual(*entry);
    }
    if (entry.get() != NULL && entry->GetArena() != NULL) {
      entry.release();
    }
  }

  // map<bool, bool> map_bool_bool = 13;
  total_size += 1 *
      ::google::protobuf::internal::FromIntSize(this->map_bool_bool_size());
  {
    ::google::protobuf::scoped_ptr<TestArenaMap_MapBoolBoolEntry> entry;
    for (::google::protobuf::Map< bool, bool >::const_iterator
        it = this->map_bool_bool().begin();
        it != this->map_bool_bool().end(); ++it) {
      if (entry.get() != NULL && entry->GetArena() != NULL) {
        entry.release();
      }
      entry.reset(map_bool_bool_.NewEntryWrapper(it->first, it->second));
      total_size += ::google::protobuf::internal::WireFormatLite::
          MessageSizeNoVirtual(*entry);
    }
    if (entry.get() != NULL && entry->GetArena() != NULL) {
      entry.release();
    }
  }

  // map<string, string> map_string_string = 14;
  total_size += 1 *
      ::google::protobuf::internal::FromIntSize(this->map_string_string_size());
  {
    ::google::protobuf::scoped_ptr<TestArenaMap_MapStringStringEntry> entry;
    for (::google::protobuf::Map< ::std::string, ::std::string >::const_iterator
        it = this->map_string_string().begin();
        it != this->map_string_string().end(); ++it) {
      if (entry.get() != NULL && entry->GetArena() != NULL) {
        entry.release();
      }
      entry.reset(map_string_string_.NewEntryWrapper(it->first, it->second));
      total_size += ::google::protobuf::internal::WireFormatLite::
          MessageSizeNoVirtual(*entry);
    }
    if (entry.get() != NULL && entry->GetArena() != NULL) {
      entry.release();
    }
  }

  // map<int32, bytes> map_int32_bytes = 15;
  total_size += 1 *
      ::google::protobuf::internal::FromIntSize(this->map_int32_bytes_size());
  {
    ::google::protobuf::scoped_ptr<TestArenaMap_MapInt32BytesEntry> entry;
    for (::google::protobuf::Map< ::google::protobuf::int32, ::std::string >::const_iterator
        it = this->map_int32_bytes().begin();
        it != this->map_int32_bytes().end(); ++it) {
      if (entry.get() != NULL && entry->GetArena() != NULL) {
        entry.release();
      }
      entry.reset(map_int32_bytes_.NewEntryWrapper(it->first, it->second));
      total_size += ::google::protobuf::internal::WireFormatLite::
          MessageSizeNoVirtual(*entry);
    }
    if (entry.get() != NULL && entry->GetArena() != NULL) {
      entry.release();
    }
  }

  // map<int32, .protobuf_unittest.MapEnum> map_int32_enum = 16;
  total_size += 2 *
      ::google::protobuf::internal::FromIntSize(this->map_int32_enum_size());
  {
    ::google::protobuf::scoped_ptr<TestArenaMap_MapInt32EnumEntry> entry;
    for (::google::protobuf::Map< ::google::protobuf::int32, ::protobuf_unittest::MapEnum >::const_iterator
        it = this->map_int32_enum().begin();
        it != this->map_int32_enum().end(); ++it) {
      if (entry.get() != NULL && entry->GetArena() != NULL) {
        entry.release();
      }
      entry.reset(map_int32_enum_.NewEnumEntryWrapper(it->first, it->second));
      total_size += ::google::protobuf::internal::WireFormatLite::
          MessageSizeNoVirtual(*entry);
    }
    if (entry.get() != NULL && entry->GetArena() != NULL) {
      entry.release();
    }
  }

  // map<int32, .protobuf_unittest.ForeignMessage> map_int32_foreign_message = 17;
  total_size += 2 *
      ::google::protobuf::internal::FromIntSize(this->map_int32_foreign_message_size());
  {
    ::google::protobuf::scoped_ptr<TestArenaMap_MapInt32ForeignMessageEntry> entry;
    for (::google::protobuf::Map< ::google::protobuf::int32, ::protobuf_unittest::ForeignMessage >::const_iterator
        it = this->map_int32_foreign_message().begin();
        it != this->map_int32_foreign_message().end(); ++it) {
      if (entry.get() != NULL && entry->GetArena() != NULL) {
        entry.release();
      }
      entry.reset(map_int32_foreign_message_.NewEntryWrapper(it->first, it->second));
      total_size += ::google::protobuf::internal::WireFormatLite::
          MessageSizeNoVirtual(*entry);
    }
    if (entry.get() != NULL && entry->GetArena() != NULL) {
      entry.release();
    }
  }

  // map<int32, .protobuf_unittest_no_arena.ForeignMessage> map_int32_foreign_message_no_arena = 18;
  total_size += 2 *
      ::google::protobuf::internal::FromIntSize(this->map_int32_foreign_message_no_arena_size());
  {
    ::google::protobuf::scoped_ptr<TestArenaMap_MapInt32ForeignMessageNoArenaEntry> entry;
    for (::google::protobuf::Map< ::google::protobuf::int32, ::protobuf_unittest_no_arena::ForeignMessage >::const_iterator
        it = this->map_int32_foreign_message_no_arena().begin();
        it != this->map_int32_foreign_message_no_arena().end(); ++it) {
      if (entry.get() != NULL && entry->GetArena() != NULL) {
        entry.release();
      }
      entry.reset(map_int32_foreign_message_no_arena_.NewEntryWrapper(it->first, it->second));
      total_size += ::google::protobuf::internal::WireFormatLite::
          MessageSizeNoVirtual(*entry);
    }
    if (entry.get() != NULL && entry->GetArena() != NULL) {
      entry.release();
    }
  }

  int cached_size = ::google::protobuf::internal::ToCachedSize(total_size);
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = cached_size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
  return total_size;
}

void TestArenaMap::MergeFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_merge_from_start:protobuf_unittest.TestArenaMap)
  if (GOOGLE_PREDICT_FALSE(&from == this)) MergeFromFail(__LINE__);
  const TestArenaMap* source =
      ::google::protobuf::internal::DynamicCastToGenerated<const TestArenaMap>(
          &from);
  if (source == NULL) {
  // @@protoc_insertion_point(generalized_merge_from_cast_fail:protobuf_unittest.TestArenaMap)
    ::google::protobuf::internal::ReflectionOps::Merge(from, this);
  } else {
  // @@protoc_insertion_point(generalized_merge_from_cast_success:protobuf_unittest.TestArenaMap)
    UnsafeMergeFrom(*source);
  }
}

void TestArenaMap::MergeFrom(const TestArenaMap& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:protobuf_unittest.TestArenaMap)
  if (GOOGLE_PREDICT_TRUE(&from != this)) {
    UnsafeMergeFrom(from);
  } else {
    MergeFromFail(__LINE__);
  }
}

void TestArenaMap::UnsafeMergeFrom(const TestArenaMap& from) {
  GOOGLE_DCHECK(&from != this);
  map_int32_int32_.MergeFrom(from.map_int32_int32_);
  map_int64_int64_.MergeFrom(from.map_int64_int64_);
  map_uint32_uint32_.MergeFrom(from.map_uint32_uint32_);
  map_uint64_uint64_.MergeFrom(from.map_uint64_uint64_);
  map_sint32_sint32_.MergeFrom(from.map_sint32_sint32_);
  map_sint64_sint64_.MergeFrom(from.map_sint64_sint64_);
  map_fixed32_fixed32_.MergeFrom(from.map_fixed32_fixed32_);
  map_fixed64_fixed64_.MergeFrom(from.map_fixed64_fixed64_);
  map_sfixed32_sfixed32_.MergeFrom(from.map_sfixed32_sfixed32_);
  map_sfixed64_sfixed64_.MergeFrom(from.map_sfixed64_sfixed64_);
  map_int32_float_.MergeFrom(from.map_int32_float_);
  map_int32_double_.MergeFrom(from.map_int32_double_);
  map_bool_bool_.MergeFrom(from.map_bool_bool_);
  map_string_string_.MergeFrom(from.map_string_string_);
  map_int32_bytes_.MergeFrom(from.map_int32_bytes_);
  map_int32_enum_.MergeFrom(from.map_int32_enum_);
  map_int32_foreign_message_.MergeFrom(from.map_int32_foreign_message_);
  map_int32_foreign_message_no_arena_.MergeFrom(from.map_int32_foreign_message_no_arena_);
}

void TestArenaMap::CopyFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_copy_from_start:protobuf_unittest.TestArenaMap)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

void TestArenaMap::CopyFrom(const TestArenaMap& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:protobuf_unittest.TestArenaMap)
  if (&from == this) return;
  Clear();
  UnsafeMergeFrom(from);
}

bool TestArenaMap::IsInitialized() const {

  return true;
}

void TestArenaMap::Swap(TestArenaMap* other) {
  if (other == this) return;
  if (GetArenaNoVirtual() == other->GetArenaNoVirtual()) {
    InternalSwap(other);
  } else {
    TestArenaMap temp;
    temp.UnsafeMergeFrom(*this);
    CopyFrom(*other);
    other->CopyFrom(temp);
  }
}
void TestArenaMap::UnsafeArenaSwap(TestArenaMap* other) {
  if (other == this) return;
  GOOGLE_DCHECK(GetArenaNoVirtual() == other->GetArenaNoVirtual());
  InternalSwap(other);
}
void TestArenaMap::InternalSwap(TestArenaMap* other) {
  map_int32_int32_.Swap(&other->map_int32_int32_);
  map_int64_int64_.Swap(&other->map_int64_int64_);
  map_uint32_uint32_.Swap(&other->map_uint32_uint32_);
  map_uint64_uint64_.Swap(&other->map_uint64_uint64_);
  map_sint32_sint32_.Swap(&other->map_sint32_sint32_);
  map_sint64_sint64_.Swap(&other->map_sint64_sint64_);
  map_fixed32_fixed32_.Swap(&other->map_fixed32_fixed32_);
  map_fixed64_fixed64_.Swap(&other->map_fixed64_fixed64_);
  map_sfixed32_sfixed32_.Swap(&other->map_sfixed32_sfixed32_);
  map_sfixed64_sfixed64_.Swap(&other->map_sfixed64_sfixed64_);
  map_int32_float_.Swap(&other->map_int32_float_);
  map_int32_double_.Swap(&other->map_int32_double_);
  map_bool_bool_.Swap(&other->map_bool_bool_);
  map_string_string_.Swap(&other->map_string_string_);
  map_int32_bytes_.Swap(&other->map_int32_bytes_);
  map_int32_enum_.Swap(&other->map_int32_enum_);
  map_int32_foreign_message_.Swap(&other->map_int32_foreign_message_);
  map_int32_foreign_message_no_arena_.Swap(&other->map_int32_foreign_message_no_arena_);
  _internal_metadata_.Swap(&other->_internal_metadata_);
  std::swap(_cached_size_, other->_cached_size_);
}

::google::protobuf::Metadata TestArenaMap::GetMetadata() const {
  protobuf_AssignDescriptorsOnce();
  ::google::protobuf::Metadata metadata;
  metadata.descriptor = TestArenaMap_descriptor_;
  metadata.reflection = TestArenaMap_reflection_;
  return metadata;
}

#if PROTOBUF_INLINE_NOT_IN_HEADERS
// TestArenaMap

// map<int32, int32> map_int32_int32 = 1;
int TestArenaMap::map_int32_int32_size() const {
  return map_int32_int32_.size();
}
void TestArenaMap::clear_map_int32_int32() {
  map_int32_int32_.Clear();
}
 const ::google::protobuf::Map< ::google::protobuf::int32, ::google::protobuf::int32 >&
TestArenaMap::map_int32_int32() const {
  // @@protoc_insertion_point(field_map:protobuf_unittest.TestArenaMap.map_int32_int32)
  return map_int32_int32_.GetMap();
}
 ::google::protobuf::Map< ::google::protobuf::int32, ::google::protobuf::int32 >*
TestArenaMap::mutable_map_int32_int32() {
  // @@protoc_insertion_point(field_mutable_map:protobuf_unittest.TestArenaMap.map_int32_int32)
  return map_int32_int32_.MutableMap();
}

// map<int64, int64> map_int64_int64 = 2;
int TestArenaMap::map_int64_int64_size() const {
  return map_int64_int64_.size();
}
void TestArenaMap::clear_map_int64_int64() {
  map_int64_int64_.Clear();
}
 const ::google::protobuf::Map< ::google::protobuf::int64, ::google::protobuf::int64 >&
TestArenaMap::map_int64_int64() const {
  // @@protoc_insertion_point(field_map:protobuf_unittest.TestArenaMap.map_int64_int64)
  return map_int64_int64_.GetMap();
}
 ::google::protobuf::Map< ::google::protobuf::int64, ::google::protobuf::int64 >*
TestArenaMap::mutable_map_int64_int64() {
  // @@protoc_insertion_point(field_mutable_map:protobuf_unittest.TestArenaMap.map_int64_int64)
  return map_int64_int64_.MutableMap();
}

// map<uint32, uint32> map_uint32_uint32 = 3;
int TestArenaMap::map_uint32_uint32_size() const {
  return map_uint32_uint32_.size();
}
void TestArenaMap::clear_map_uint32_uint32() {
  map_uint32_uint32_.Clear();
}
 const ::google::protobuf::Map< ::google::protobuf::uint32, ::google::protobuf::uint32 >&
TestArenaMap::map_uint32_uint32() const {
  // @@protoc_insertion_point(field_map:protobuf_unittest.TestArenaMap.map_uint32_uint32)
  return map_uint32_uint32_.GetMap();
}
 ::google::protobuf::Map< ::google::protobuf::uint32, ::google::protobuf::uint32 >*
TestArenaMap::mutable_map_uint32_uint32() {
  // @@protoc_insertion_point(field_mutable_map:protobuf_unittest.TestArenaMap.map_uint32_uint32)
  return map_uint32_uint32_.MutableMap();
}

// map<uint64, uint64> map_uint64_uint64 = 4;
int TestArenaMap::map_uint64_uint64_size() const {
  return map_uint64_uint64_.size();
}
void TestArenaMap::clear_map_uint64_uint64() {
  map_uint64_uint64_.Clear();
}
 const ::google::protobuf::Map< ::google::protobuf::uint64, ::google::protobuf::uint64 >&
TestArenaMap::map_uint64_uint64() const {
  // @@protoc_insertion_point(field_map:protobuf_unittest.TestArenaMap.map_uint64_uint64)
  return map_uint64_uint64_.GetMap();
}
 ::google::protobuf::Map< ::google::protobuf::uint64, ::google::protobuf::uint64 >*
TestArenaMap::mutable_map_uint64_uint64() {
  // @@protoc_insertion_point(field_mutable_map:protobuf_unittest.TestArenaMap.map_uint64_uint64)
  return map_uint64_uint64_.MutableMap();
}

// map<sint32, sint32> map_sint32_sint32 = 5;
int TestArenaMap::map_sint32_sint32_size() const {
  return map_sint32_sint32_.size();
}
void TestArenaMap::clear_map_sint32_sint32() {
  map_sint32_sint32_.Clear();
}
 const ::google::protobuf::Map< ::google::protobuf::int32, ::google::protobuf::int32 >&
TestArenaMap::map_sint32_sint32() const {
  // @@protoc_insertion_point(field_map:protobuf_unittest.TestArenaMap.map_sint32_sint32)
  return map_sint32_sint32_.GetMap();
}
 ::google::protobuf::Map< ::google::protobuf::int32, ::google::protobuf::int32 >*
TestArenaMap::mutable_map_sint32_sint32() {
  // @@protoc_insertion_point(field_mutable_map:protobuf_unittest.TestArenaMap.map_sint32_sint32)
  return map_sint32_sint32_.MutableMap();
}

// map<sint64, sint64> map_sint64_sint64 = 6;
int TestArenaMap::map_sint64_sint64_size() const {
  return map_sint64_sint64_.size();
}
void TestArenaMap::clear_map_sint64_sint64() {
  map_sint64_sint64_.Clear();
}
 const ::google::protobuf::Map< ::google::protobuf::int64, ::google::protobuf::int64 >&
TestArenaMap::map_sint64_sint64() const {
  // @@protoc_insertion_point(field_map:protobuf_unittest.TestArenaMap.map_sint64_sint64)
  return map_sint64_sint64_.GetMap();
}
 ::google::protobuf::Map< ::google::protobuf::int64, ::google::protobuf::int64 >*
TestArenaMap::mutable_map_sint64_sint64() {
  // @@protoc_insertion_point(field_mutable_map:protobuf_unittest.TestArenaMap.map_sint64_sint64)
  return map_sint64_sint64_.MutableMap();
}

// map<fixed32, fixed32> map_fixed32_fixed32 = 7;
int TestArenaMap::map_fixed32_fixed32_size() const {
  return map_fixed32_fixed32_.size();
}
void TestArenaMap::clear_map_fixed32_fixed32() {
  map_fixed32_fixed32_.Clear();
}
 const ::google::protobuf::Map< ::google::protobuf::uint32, ::google::protobuf::uint32 >&
TestArenaMap::map_fixed32_fixed32() const {
  // @@protoc_insertion_point(field_map:protobuf_unittest.TestArenaMap.map_fixed32_fixed32)
  return map_fixed32_fixed32_.GetMap();
}
 ::google::protobuf::Map< ::google::protobuf::uint32, ::google::protobuf::uint32 >*
TestArenaMap::mutable_map_fixed32_fixed32() {
  // @@protoc_insertion_point(field_mutable_map:protobuf_unittest.TestArenaMap.map_fixed32_fixed32)
  return map_fixed32_fixed32_.MutableMap();
}

// map<fixed64, fixed64> map_fixed64_fixed64 = 8;
int TestArenaMap::map_fixed64_fixed64_size() const {
  return map_fixed64_fixed64_.size();
}
void TestArenaMap::clear_map_fixed64_fixed64() {
  map_fixed64_fixed64_.Clear();
}
 const ::google::protobuf::Map< ::google::protobuf::uint64, ::google::protobuf::uint64 >&
TestArenaMap::map_fixed64_fixed64() const {
  // @@protoc_insertion_point(field_map:protobuf_unittest.TestArenaMap.map_fixed64_fixed64)
  return map_fixed64_fixed64_.GetMap();
}
 ::google::protobuf::Map< ::google::protobuf::uint64, ::google::protobuf::uint64 >*
TestArenaMap::mutable_map_fixed64_fixed64() {
  // @@protoc_insertion_point(field_mutable_map:protobuf_unittest.TestArenaMap.map_fixed64_fixed64)
  return map_fixed64_fixed64_.MutableMap();
}

// map<sfixed32, sfixed32> map_sfixed32_sfixed32 = 9;
int TestArenaMap::map_sfixed32_sfixed32_size() const {
  return map_sfixed32_sfixed32_.size();
}
void TestArenaMap::clear_map_sfixed32_sfixed32() {
  map_sfixed32_sfixed32_.Clear();
}
 const ::google::protobuf::Map< ::google::protobuf::int32, ::google::protobuf::int32 >&
TestArenaMap::map_sfixed32_sfixed32() const {
  // @@protoc_insertion_point(field_map:protobuf_unittest.TestArenaMap.map_sfixed32_sfixed32)
  return map_sfixed32_sfixed32_.GetMap();
}
 ::google::protobuf::Map< ::google::protobuf::int32, ::google::protobuf::int32 >*
TestArenaMap::mutable_map_sfixed32_sfixed32() {
  // @@protoc_insertion_point(field_mutable_map:protobuf_unittest.TestArenaMap.map_sfixed32_sfixed32)
  return map_sfixed32_sfixed32_.MutableMap();
}

// map<sfixed64, sfixed64> map_sfixed64_sfixed64 = 10;
int TestArenaMap::map_sfixed64_sfixed64_size() const {
  return map_sfixed64_sfixed64_.size();
}
void TestArenaMap::clear_map_sfixed64_sfixed64() {
  map_sfixed64_sfixed64_.Clear();
}
 const ::google::protobuf::Map< ::google::protobuf::int64, ::google::protobuf::int64 >&
TestArenaMap::map_sfixed64_sfixed64() const {
  // @@protoc_insertion_point(field_map:protobuf_unittest.TestArenaMap.map_sfixed64_sfixed64)
  return map_sfixed64_sfixed64_.GetMap();
}
 ::google::protobuf::Map< ::google::protobuf::int64, ::google::protobuf::int64 >*
TestArenaMap::mutable_map_sfixed64_sfixed64() {
  // @@protoc_insertion_point(field_mutable_map:protobuf_unittest.TestArenaMap.map_sfixed64_sfixed64)
  return map_sfixed64_sfixed64_.MutableMap();
}

// map<int32, float> map_int32_float = 11;
int TestArenaMap::map_int32_float_size() const {
  return map_int32_float_.size();
}
void TestArenaMap::clear_map_int32_float() {
  map_int32_float_.Clear();
}
 const ::google::protobuf::Map< ::google::protobuf::int32, float >&
TestArenaMap::map_int32_float() const {
  // @@protoc_insertion_point(field_map:protobuf_unittest.TestArenaMap.map_int32_float)
  return map_int32_float_.GetMap();
}
 ::google::protobuf::Map< ::google::protobuf::int32, float >*
TestArenaMap::mutable_map_int32_float() {
  // @@protoc_insertion_point(field_mutable_map:protobuf_unittest.TestArenaMap.map_int32_float)
  return map_int32_float_.MutableMap();
}

// map<int32, double> map_int32_double = 12;
int TestArenaMap::map_int32_double_size() const {
  return map_int32_double_.size();
}
void TestArenaMap::clear_map_int32_double() {
  map_int32_double_.Clear();
}
 const ::google::protobuf::Map< ::google::protobuf::int32, double >&
TestArenaMap::map_int32_double() const {
  // @@protoc_insertion_point(field_map:protobuf_unittest.TestArenaMap.map_int32_double)
  return map_int32_double_.GetMap();
}
 ::google::protobuf::Map< ::google::protobuf::int32, double >*
TestArenaMap::mutable_map_int32_double() {
  // @@protoc_insertion_point(field_mutable_map:protobuf_unittest.TestArenaMap.map_int32_double)
  return map_int32_double_.MutableMap();
}

// map<bool, bool> map_bool_bool = 13;
int TestArenaMap::map_bool_bool_size() const {
  return map_bool_bool_.size();
}
void TestArenaMap::clear_map_bool_bool() {
  map_bool_bool_.Clear();
}
 const ::google::protobuf::Map< bool, bool >&
TestArenaMap::map_bool_bool() const {
  // @@protoc_insertion_point(field_map:protobuf_unittest.TestArenaMap.map_bool_bool)
  return map_bool_bool_.GetMap();
}
 ::google::protobuf::Map< bool, bool >*
TestArenaMap::mutable_map_bool_bool() {
  // @@protoc_insertion_point(field_mutable_map:protobuf_unittest.TestArenaMap.map_bool_bool)
  return map_bool_bool_.MutableMap();
}

// map<string, string> map_string_string = 14;
int TestArenaMap::map_string_string_size() const {
  return map_string_string_.size();
}
void TestArenaMap::clear_map_string_string() {
  map_string_string_.Clear();
}
 const ::google::protobuf::Map< ::std::string, ::std::string >&
TestArenaMap::map_string_string() const {
  // @@protoc_insertion_point(field_map:protobuf_unittest.TestArenaMap.map_string_string)
  return map_string_string_.GetMap();
}
 ::google::protobuf::Map< ::std::string, ::std::string >*
TestArenaMap::mutable_map_string_string() {
  // @@protoc_insertion_point(field_mutable_map:protobuf_unittest.TestArenaMap.map_string_string)
  return map_string_string_.MutableMap();
}

// map<int32, bytes> map_int32_bytes = 15;
int TestArenaMap::map_int32_bytes_size() const {
  return map_int32_bytes_.size();
}
void TestArenaMap::clear_map_int32_bytes() {
  map_int32_bytes_.Clear();
}
 const ::google::protobuf::Map< ::google::protobuf::int32, ::std::string >&
TestArenaMap::map_int32_bytes() const {
  // @@protoc_insertion_point(field_map:protobuf_unittest.TestArenaMap.map_int32_bytes)
  return map_int32_bytes_.GetMap();
}
 ::google::protobuf::Map< ::google::protobuf::int32, ::std::string >*
TestArenaMap::mutable_map_int32_bytes() {
  // @@protoc_insertion_point(field_mutable_map:protobuf_unittest.TestArenaMap.map_int32_bytes)
  return map_int32_bytes_.MutableMap();
}

// map<int32, .protobuf_unittest.MapEnum> map_int32_enum = 16;
int TestArenaMap::map_int32_enum_size() const {
  return map_int32_enum_.size();
}
void TestArenaMap::clear_map_int32_enum() {
  map_int32_enum_.Clear();
}
 const ::google::protobuf::Map< ::google::protobuf::int32, ::protobuf_unittest::MapEnum >&
TestArenaMap::map_int32_enum() const {
  // @@protoc_insertion_point(field_map:protobuf_unittest.TestArenaMap.map_int32_enum)
  return map_int32_enum_.GetMap();
}
 ::google::protobuf::Map< ::google::protobuf::int32, ::protobuf_unittest::MapEnum >*
TestArenaMap::mutable_map_int32_enum() {
  // @@protoc_insertion_point(field_mutable_map:protobuf_unittest.TestArenaMap.map_int32_enum)
  return map_int32_enum_.MutableMap();
}

// map<int32, .protobuf_unittest.ForeignMessage> map_int32_foreign_message = 17;
int TestArenaMap::map_int32_foreign_message_size() const {
  return map_int32_foreign_message_.size();
}
void TestArenaMap::clear_map_int32_foreign_message() {
  map_int32_foreign_message_.Clear();
}
 const ::google::protobuf::Map< ::google::protobuf::int32, ::protobuf_unittest::ForeignMessage >&
TestArenaMap::map_int32_foreign_message() const {
  // @@protoc_insertion_point(field_map:protobuf_unittest.TestArenaMap.map_int32_foreign_message)
  return map_int32_foreign_message_.GetMap();
}
 ::google::protobuf::Map< ::google::protobuf::int32, ::protobuf_unittest::ForeignMessage >*
TestArenaMap::mutable_map_int32_foreign_message() {
  // @@protoc_insertion_point(field_mutable_map:protobuf_unittest.TestArenaMap.map_int32_foreign_message)
  return map_int32_foreign_message_.MutableMap();
}

// map<int32, .protobuf_unittest_no_arena.ForeignMessage> map_int32_foreign_message_no_arena = 18;
int TestArenaMap::map_int32_foreign_message_no_arena_size() const {
  return map_int32_foreign_message_no_arena_.size();
}
void TestArenaMap::clear_map_int32_foreign_message_no_arena() {
  map_int32_foreign_message_no_arena_.Clear();
}
 const ::google::protobuf::Map< ::google::protobuf::int32, ::protobuf_unittest_no_arena::ForeignMessage >&
TestArenaMap::map_int32_foreign_message_no_arena() const {
  // @@protoc_insertion_point(field_map:protobuf_unittest.TestArenaMap.map_int32_foreign_message_no_arena)
  return map_int32_foreign_message_no_arena_.GetMap();
}
 ::google::protobuf::Map< ::google::protobuf::int32, ::protobuf_unittest_no_arena::ForeignMessage >*
TestArenaMap::mutable_map_int32_foreign_message_no_arena() {
  // @@protoc_insertion_point(field_mutable_map:protobuf_unittest.TestArenaMap.map_int32_foreign_message_no_arena)
  return map_int32_foreign_message_no_arena_.MutableMap();
}

inline const TestArenaMap* TestArenaMap::internal_default_instance() {
  return &TestArenaMap_default_instance_.get();
}
#endif  // PROTOBUF_INLINE_NOT_IN_HEADERS

// ===================================================================

const ::google::protobuf::EnumDescriptor* MessageContainingEnumCalledType_Type_descriptor() {
  protobuf_AssignDescriptorsOnce();
  return MessageContainingEnumCalledType_Type_descriptor_;
}
bool MessageContainingEnumCalledType_Type_IsValid(int value) {
  switch (value) {
    case 0:
      return true;
    default:
      return false;
  }
}

#if !defined(_MSC_VER) || _MSC_VER >= 1900
const MessageContainingEnumCalledType_Type MessageContainingEnumCalledType::TYPE_FOO;
const MessageContainingEnumCalledType_Type MessageContainingEnumCalledType::Type_MIN;
const MessageContainingEnumCalledType_Type MessageContainingEnumCalledType::Type_MAX;
const int MessageContainingEnumCalledType::Type_ARRAYSIZE;
#endif  // !defined(_MSC_VER) || _MSC_VER >= 1900
#if !defined(_MSC_VER) || _MSC_VER >= 1900
const int MessageContainingEnumCalledType::kTypeFieldNumber;
#endif  // !defined(_MSC_VER) || _MSC_VER >= 1900

MessageContainingEnumCalledType::MessageContainingEnumCalledType()
  : ::google::protobuf::Message(), _internal_metadata_(NULL) {
  if (this != internal_default_instance()) protobuf_InitDefaults_google_2fprotobuf_2fmap_5funittest_2eproto();
  SharedCtor();
  // @@protoc_insertion_point(constructor:protobuf_unittest.MessageContainingEnumCalledType)
}
MessageContainingEnumCalledType::MessageContainingEnumCalledType(::google::protobuf::Arena* arena)
  : ::google::protobuf::Message(),
  _internal_metadata_(arena),
  type_(arena) {
#ifdef GOOGLE_PROTOBUF_NO_STATIC_INITIALIZER
  protobuf_InitDefaults_google_2fprotobuf_2fmap_5funittest_2eproto();
#endif  // GOOGLE_PROTOBUF_NO_STATIC_INITIALIZER
  SharedCtor();
  RegisterArenaDtor(arena);
  // @@protoc_insertion_point(arena_constructor:protobuf_unittest.MessageContainingEnumCalledType)
}

void MessageContainingEnumCalledType::InitAsDefaultInstance() {
}

MessageContainingEnumCalledType::MessageContainingEnumCalledType(const MessageContainingEnumCalledType& from)
  : ::google::protobuf::Message(),
    _internal_metadata_(NULL) {
  SharedCtor();
  UnsafeMergeFrom(from);
  // @@protoc_insertion_point(copy_constructor:protobuf_unittest.MessageContainingEnumCalledType)
}

void MessageContainingEnumCalledType::SharedCtor() {
  type_.SetAssignDescriptorCallback(
      protobuf_AssignDescriptorsOnce);
  type_.SetEntryDescriptor(
      &::protobuf_unittest::MessageContainingEnumCalledType_TypeEntry_descriptor_);
  _cached_size_ = 0;
}

MessageContainingEnumCalledType::~MessageContainingEnumCalledType() {
  // @@protoc_insertion_point(destructor:protobuf_unittest.MessageContainingEnumCalledType)
  SharedDtor();
}

void MessageContainingEnumCalledType::SharedDtor() {
  ::google::protobuf::Arena* arena = GetArenaNoVirtual();
  if (arena != NULL) {
    return;
  }

}

void MessageContainingEnumCalledType::ArenaDtor(void* object) {
  MessageContainingEnumCalledType* _this = reinterpret_cast< MessageContainingEnumCalledType* >(object);
  (void)_this;
}
void MessageContainingEnumCalledType::RegisterArenaDtor(::google::protobuf::Arena* arena) {
}
void MessageContainingEnumCalledType::SetCachedSize(int size) const {
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
}
const ::google::protobuf::Descriptor* MessageContainingEnumCalledType::descriptor() {
  protobuf_AssignDescriptorsOnce();
  return MessageContainingEnumCalledType_descriptor_;
}

const MessageContainingEnumCalledType& MessageContainingEnumCalledType::default_instance() {
  protobuf_InitDefaults_google_2fprotobuf_2fmap_5funittest_2eproto();
  return *internal_default_instance();
}

::google::protobuf::internal::ExplicitlyConstructed<MessageContainingEnumCalledType> MessageContainingEnumCalledType_default_instance_;

MessageContainingEnumCalledType* MessageContainingEnumCalledType::New(::google::protobuf::Arena* arena) const {
  return ::google::protobuf::Arena::CreateMessage<MessageContainingEnumCalledType>(arena);
}

void MessageContainingEnumCalledType::Clear() {
// @@protoc_insertion_point(message_clear_start:protobuf_unittest.MessageContainingEnumCalledType)
  type_.Clear();
}

bool MessageContainingEnumCalledType::MergePartialFromCodedStream(
    ::google::protobuf::io::CodedInputStream* input) {
#define DO_(EXPRESSION) if (!GOOGLE_PREDICT_TRUE(EXPRESSION)) goto failure
  ::google::protobuf::uint32 tag;
  // @@protoc_insertion_point(parse_start:protobuf_unittest.MessageContainingEnumCalledType)
  for (;;) {
    ::std::pair< ::google::protobuf::uint32, bool> p = input->ReadTagWithCutoff(127);
    tag = p.first;
    if (!p.second) goto handle_unusual;
    switch (::google::protobuf::internal::WireFormatLite::GetTagFieldNumber(tag)) {
      // map<string, .protobuf_unittest.MessageContainingEnumCalledType> type = 1;
      case 1: {
        if (tag == 10) {
          DO_(input->IncrementRecursionDepth());
         parse_loop_type:
          MessageContainingEnumCalledType_TypeEntry::Parser< ::google::protobuf::internal::MapField<
              ::std::string, ::protobuf_unittest::MessageContainingEnumCalledType,
              ::google::protobuf::internal::WireFormatLite::TYPE_STRING,
              ::google::protobuf::internal::WireFormatLite::TYPE_MESSAGE,
              0 >,
            ::google::protobuf::Map< ::std::string, ::protobuf_unittest::MessageContainingEnumCalledType > > parser(&type_);
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessageNoVirtual(
              input, &parser));
          DO_(::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
            parser.key().data(), parser.key().length(),
            ::google::protobuf::internal::WireFormatLite::PARSE,
            "protobuf_unittest.MessageContainingEnumCalledType.TypeEntry.key"));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(10)) goto parse_loop_type;
        input->UnsafeDecrementRecursionDepth();
        if (input->ExpectAtEnd()) goto success;
        break;
      }

      default: {
      handle_unusual:
        if (tag == 0 ||
            ::google::protobuf::internal::WireFormatLite::GetTagWireType(tag) ==
            ::google::protobuf::internal::WireFormatLite::WIRETYPE_END_GROUP) {
          goto success;
        }
        DO_(::google::protobuf::internal::WireFormatLite::SkipField(input, tag));
        break;
      }
    }
  }
success:
  // @@protoc_insertion_point(parse_success:protobuf_unittest.MessageContainingEnumCalledType)
  return true;
failure:
  // @@protoc_insertion_point(parse_failure:protobuf_unittest.MessageContainingEnumCalledType)
  return false;
#undef DO_
}

void MessageContainingEnumCalledType::SerializeWithCachedSizes(
    ::google::protobuf::io::CodedOutputStream* output) const {
  // @@protoc_insertion_point(serialize_start:protobuf_unittest.MessageContainingEnumCalledType)
  // map<string, .protobuf_unittest.MessageContainingEnumCalledType> type = 1;
  if (!this->type().empty()) {
    typedef ::google::protobuf::Map< ::std::string, ::protobuf_unittest::MessageContainingEnumCalledType >::const_pointer
        ConstPtr;
    typedef ConstPtr SortItem;
    typedef ::google::protobuf::internal::CompareByDerefFirst<SortItem> Less;
    struct Utf8Check {
      static void Check(ConstPtr p) {
        ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
          p->first.data(), p->first.length(),
          ::google::protobuf::internal::WireFormatLite::SERIALIZE,
          "protobuf_unittest.MessageContainingEnumCalledType.TypeEntry.key");
      }
    };

    if (output->IsSerializationDeterminstic() &&
        this->type().size() > 1) {
      ::google::protobuf::scoped_array<SortItem> items(
          new SortItem[this->type().size()]);
      typedef ::google::protobuf::Map< ::std::string, ::protobuf_unittest::MessageContainingEnumCalledType >::size_type size_type;
      size_type n = 0;
      for (::google::protobuf::Map< ::std::string, ::protobuf_unittest::MessageContainingEnumCalledType >::const_iterator
          it = this->type().begin();
          it != this->type().end(); ++it, ++n) {
        items[n] = SortItem(&*it);
      }
      ::std::sort(&items[0], &items[n], Less());
      ::google::protobuf::scoped_ptr<MessageContainingEnumCalledType_TypeEntry> entry;
      for (size_type i = 0; i < n; i++) {
        entry.reset(type_.NewEntryWrapper(
            items[i]->first, items[i]->second));
        ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
            1, *entry, output);
        if (entry->GetArena() != NULL) {
          entry.release();
        }
        Utf8Check::Check(items[i]);
      }
    } else {
      ::google::protobuf::scoped_ptr<MessageContainingEnumCalledType_TypeEntry> entry;
      for (::google::protobuf::Map< ::std::string, ::protobuf_unittest::MessageContainingEnumCalledType >::const_iterator
          it = this->type().begin();
          it != this->type().end(); ++it) {
        entry.reset(type_.NewEntryWrapper(
            it->first, it->second));
        ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
            1, *entry, output);
        if (entry->GetArena() != NULL) {
          entry.release();
        }
        Utf8Check::Check(&*it);
      }
    }
  }

  // @@protoc_insertion_point(serialize_end:protobuf_unittest.MessageContainingEnumCalledType)
}

::google::protobuf::uint8* MessageContainingEnumCalledType::InternalSerializeWithCachedSizesToArray(
    bool deterministic, ::google::protobuf::uint8* target) const {
  (void)deterministic; // Unused
  // @@protoc_insertion_point(serialize_to_array_start:protobuf_unittest.MessageContainingEnumCalledType)
  // map<string, .protobuf_unittest.MessageContainingEnumCalledType> type = 1;
  if (!this->type().empty()) {
    typedef ::google::protobuf::Map< ::std::string, ::protobuf_unittest::MessageContainingEnumCalledType >::const_pointer
        ConstPtr;
    typedef ConstPtr SortItem;
    typedef ::google::protobuf::internal::CompareByDerefFirst<SortItem> Less;
    struct Utf8Check {
      static void Check(ConstPtr p) {
        ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
          p->first.data(), p->first.length(),
          ::google::protobuf::internal::WireFormatLite::SERIALIZE,
          "protobuf_unittest.MessageContainingEnumCalledType.TypeEntry.key");
      }
    };

    if (deterministic &&
        this->type().size() > 1) {
      ::google::protobuf::scoped_array<SortItem> items(
          new SortItem[this->type().size()]);
      typedef ::google::protobuf::Map< ::std::string, ::protobuf_unittest::MessageContainingEnumCalledType >::size_type size_type;
      size_type n = 0;
      for (::google::protobuf::Map< ::std::string, ::protobuf_unittest::MessageContainingEnumCalledType >::const_iterator
          it = this->type().begin();
          it != this->type().end(); ++it, ++n) {
        items[n] = SortItem(&*it);
      }
      ::std::sort(&items[0], &items[n], Less());
      ::google::protobuf::scoped_ptr<MessageContainingEnumCalledType_TypeEntry> entry;
      for (size_type i = 0; i < n; i++) {
        entry.reset(type_.NewEntryWrapper(
            items[i]->first, items[i]->second));
        target = ::google::protobuf::internal::WireFormatLite::
                   InternalWriteMessageNoVirtualToArray(
                       1, *entry, deterministic, target);
;
        if (entry->GetArena() != NULL) {
          entry.release();
        }
        Utf8Check::Check(items[i]);
      }
    } else {
      ::google::protobuf::scoped_ptr<MessageContainingEnumCalledType_TypeEntry> entry;
      for (::google::protobuf::Map< ::std::string, ::protobuf_unittest::MessageContainingEnumCalledType >::const_iterator
          it = this->type().begin();
          it != this->type().end(); ++it) {
        entry.reset(type_.NewEntryWrapper(
            it->first, it->second));
        target = ::google::protobuf::internal::WireFormatLite::
                   InternalWriteMessageNoVirtualToArray(
                       1, *entry, deterministic, target);
;
        if (entry->GetArena() != NULL) {
          entry.release();
        }
        Utf8Check::Check(&*it);
      }
    }
  }

  // @@protoc_insertion_point(serialize_to_array_end:protobuf_unittest.MessageContainingEnumCalledType)
  return target;
}

size_t MessageContainingEnumCalledType::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:protobuf_unittest.MessageContainingEnumCalledType)
  size_t total_size = 0;

  // map<string, .protobuf_unittest.MessageContainingEnumCalledType> type = 1;
  total_size += 1 *
      ::google::protobuf::internal::FromIntSize(this->type_size());
  {
    ::google::protobuf::scoped_ptr<MessageContainingEnumCalledType_TypeEntry> entry;
    for (::google::protobuf::Map< ::std::string, ::protobuf_unittest::MessageContainingEnumCalledType >::const_iterator
        it = this->type().begin();
        it != this->type().end(); ++it) {
      if (entry.get() != NULL && entry->GetArena() != NULL) {
        entry.release();
      }
      entry.reset(type_.NewEntryWrapper(it->first, it->second));
      total_size += ::google::protobuf::internal::WireFormatLite::
          MessageSizeNoVirtual(*entry);
    }
    if (entry.get() != NULL && entry->GetArena() != NULL) {
      entry.release();
    }
  }

  int cached_size = ::google::protobuf::internal::ToCachedSize(total_size);
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = cached_size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
  return total_size;
}

void MessageContainingEnumCalledType::MergeFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_merge_from_start:protobuf_unittest.MessageContainingEnumCalledType)
  if (GOOGLE_PREDICT_FALSE(&from == this)) MergeFromFail(__LINE__);
  const MessageContainingEnumCalledType* source =
      ::google::protobuf::internal::DynamicCastToGenerated<const MessageContainingEnumCalledType>(
          &from);
  if (source == NULL) {
  // @@protoc_insertion_point(generalized_merge_from_cast_fail:protobuf_unittest.MessageContainingEnumCalledType)
    ::google::protobuf::internal::ReflectionOps::Merge(from, this);
  } else {
  // @@protoc_insertion_point(generalized_merge_from_cast_success:protobuf_unittest.MessageContainingEnumCalledType)
    UnsafeMergeFrom(*source);
  }
}

void MessageContainingEnumCalledType::MergeFrom(const MessageContainingEnumCalledType& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:protobuf_unittest.MessageContainingEnumCalledType)
  if (GOOGLE_PREDICT_TRUE(&from != this)) {
    UnsafeMergeFrom(from);
  } else {
    MergeFromFail(__LINE__);
  }
}

void MessageContainingEnumCalledType::UnsafeMergeFrom(const MessageContainingEnumCalledType& from) {
  GOOGLE_DCHECK(&from != this);
  type_.MergeFrom(from.type_);
}

void MessageContainingEnumCalledType::CopyFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_copy_from_start:protobuf_unittest.MessageContainingEnumCalledType)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

void MessageContainingEnumCalledType::CopyFrom(const MessageContainingEnumCalledType& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:protobuf_unittest.MessageContainingEnumCalledType)
  if (&from == this) return;
  Clear();
  UnsafeMergeFrom(from);
}

bool MessageContainingEnumCalledType::IsInitialized() const {

  return true;
}

void MessageContainingEnumCalledType::Swap(MessageContainingEnumCalledType* other) {
  if (other == this) return;
  if (GetArenaNoVirtual() == other->GetArenaNoVirtual()) {
    InternalSwap(other);
  } else {
    MessageContainingEnumCalledType temp;
    temp.UnsafeMergeFrom(*this);
    CopyFrom(*other);
    other->CopyFrom(temp);
  }
}
void MessageContainingEnumCalledType::UnsafeArenaSwap(MessageContainingEnumCalledType* other) {
  if (other == this) return;
  GOOGLE_DCHECK(GetArenaNoVirtual() == other->GetArenaNoVirtual());
  InternalSwap(other);
}
void MessageContainingEnumCalledType::InternalSwap(MessageContainingEnumCalledType* other) {
  type_.Swap(&other->type_);
  _internal_metadata_.Swap(&other->_internal_metadata_);
  std::swap(_cached_size_, other->_cached_size_);
}

::google::protobuf::Metadata MessageContainingEnumCalledType::GetMetadata() const {
  protobuf_AssignDescriptorsOnce();
  ::google::protobuf::Metadata metadata;
  metadata.descriptor = MessageContainingEnumCalledType_descriptor_;
  metadata.reflection = MessageContainingEnumCalledType_reflection_;
  return metadata;
}

#if PROTOBUF_INLINE_NOT_IN_HEADERS
// MessageContainingEnumCalledType

// map<string, .protobuf_unittest.MessageContainingEnumCalledType> type = 1;
int MessageContainingEnumCalledType::type_size() const {
  return type_.size();
}
void MessageContainingEnumCalledType::clear_type() {
  type_.Clear();
}
 const ::google::protobuf::Map< ::std::string, ::protobuf_unittest::MessageContainingEnumCalledType >&
MessageContainingEnumCalledType::type() const {
  // @@protoc_insertion_point(field_map:protobuf_unittest.MessageContainingEnumCalledType.type)
  return type_.GetMap();
}
 ::google::protobuf::Map< ::std::string, ::protobuf_unittest::MessageContainingEnumCalledType >*
MessageContainingEnumCalledType::mutable_type() {
  // @@protoc_insertion_point(field_mutable_map:protobuf_unittest.MessageContainingEnumCalledType.type)
  return type_.MutableMap();
}

inline const MessageContainingEnumCalledType* MessageContainingEnumCalledType::internal_default_instance() {
  return &MessageContainingEnumCalledType_default_instance_.get();
}
#endif  // PROTOBUF_INLINE_NOT_IN_HEADERS

// ===================================================================

#if !defined(_MSC_VER) || _MSC_VER >= 1900
const int MessageContainingMapCalledEntry::kEntryFieldNumber;
#endif  // !defined(_MSC_VER) || _MSC_VER >= 1900

MessageContainingMapCalledEntry::MessageContainingMapCalledEntry()
  : ::google::protobuf::Message(), _internal_metadata_(NULL) {
  if (this != internal_default_instance()) protobuf_InitDefaults_google_2fprotobuf_2fmap_5funittest_2eproto();
  SharedCtor();
  // @@protoc_insertion_point(constructor:protobuf_unittest.MessageContainingMapCalledEntry)
}
MessageContainingMapCalledEntry::MessageContainingMapCalledEntry(::google::protobuf::Arena* arena)
  : ::google::protobuf::Message(),
  _internal_metadata_(arena),
  entry_(arena) {
#ifdef GOOGLE_PROTOBUF_NO_STATIC_INITIALIZER
  protobuf_InitDefaults_google_2fprotobuf_2fmap_5funittest_2eproto();
#endif  // GOOGLE_PROTOBUF_NO_STATIC_INITIALIZER
  SharedCtor();
  RegisterArenaDtor(arena);
  // @@protoc_insertion_point(arena_constructor:protobuf_unittest.MessageContainingMapCalledEntry)
}

void MessageContainingMapCalledEntry::InitAsDefaultInstance() {
}

MessageContainingMapCalledEntry::MessageContainingMapCalledEntry(const MessageContainingMapCalledEntry& from)
  : ::google::protobuf::Message(),
    _internal_metadata_(NULL) {
  SharedCtor();
  UnsafeMergeFrom(from);
  // @@protoc_insertion_point(copy_constructor:protobuf_unittest.MessageContainingMapCalledEntry)
}

void MessageContainingMapCalledEntry::SharedCtor() {
  entry_.SetAssignDescriptorCallback(
      protobuf_AssignDescriptorsOnce);
  entry_.SetEntryDescriptor(
      &::protobuf_unittest::MessageContainingMapCalledEntry_EntryEntry_descriptor_);
  _cached_size_ = 0;
}

MessageContainingMapCalledEntry::~MessageContainingMapCalledEntry() {
  // @@protoc_insertion_point(destructor:protobuf_unittest.MessageContainingMapCalledEntry)
  SharedDtor();
}

void MessageContainingMapCalledEntry::SharedDtor() {
  ::google::protobuf::Arena* arena = GetArenaNoVirtual();
  if (arena != NULL) {
    return;
  }

}

void MessageContainingMapCalledEntry::ArenaDtor(void* object) {
  MessageContainingMapCalledEntry* _this = reinterpret_cast< MessageContainingMapCalledEntry* >(object);
  (void)_this;
}
void MessageContainingMapCalledEntry::RegisterArenaDtor(::google::protobuf::Arena* arena) {
}
void MessageContainingMapCalledEntry::SetCachedSize(int size) const {
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
}
const ::google::protobuf::Descriptor* MessageContainingMapCalledEntry::descriptor() {
  protobuf_AssignDescriptorsOnce();
  return MessageContainingMapCalledEntry_descriptor_;
}

const MessageContainingMapCalledEntry& MessageContainingMapCalledEntry::default_instance() {
  protobuf_InitDefaults_google_2fprotobuf_2fmap_5funittest_2eproto();
  return *internal_default_instance();
}

::google::protobuf::internal::ExplicitlyConstructed<MessageContainingMapCalledEntry> MessageContainingMapCalledEntry_default_instance_;

MessageContainingMapCalledEntry* MessageContainingMapCalledEntry::New(::google::protobuf::Arena* arena) const {
  return ::google::protobuf::Arena::CreateMessage<MessageContainingMapCalledEntry>(arena);
}

void MessageContainingMapCalledEntry::Clear() {
// @@protoc_insertion_point(message_clear_start:protobuf_unittest.MessageContainingMapCalledEntry)
  entry_.Clear();
}

bool MessageContainingMapCalledEntry::MergePartialFromCodedStream(
    ::google::protobuf::io::CodedInputStream* input) {
#define DO_(EXPRESSION) if (!GOOGLE_PREDICT_TRUE(EXPRESSION)) goto failure
  ::google::protobuf::uint32 tag;
  // @@protoc_insertion_point(parse_start:protobuf_unittest.MessageContainingMapCalledEntry)
  for (;;) {
    ::std::pair< ::google::protobuf::uint32, bool> p = input->ReadTagWithCutoff(127);
    tag = p.first;
    if (!p.second) goto handle_unusual;
    switch (::google::protobuf::internal::WireFormatLite::GetTagFieldNumber(tag)) {
      // map<int32, int32> entry = 1;
      case 1: {
        if (tag == 10) {
          DO_(input->IncrementRecursionDepth());
         parse_loop_entry:
          MessageContainingMapCalledEntry_EntryEntry::Parser< ::google::protobuf::internal::MapField<
              ::google::protobuf::int32, ::google::protobuf::int32,
              ::google::protobuf::internal::WireFormatLite::TYPE_INT32,
              ::google::protobuf::internal::WireFormatLite::TYPE_INT32,
              0 >,
            ::google::protobuf::Map< ::google::protobuf::int32, ::google::protobuf::int32 > > parser(&entry_);
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessageNoVirtual(
              input, &parser));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(10)) goto parse_loop_entry;
        input->UnsafeDecrementRecursionDepth();
        if (input->ExpectAtEnd()) goto success;
        break;
      }

      default: {
      handle_unusual:
        if (tag == 0 ||
            ::google::protobuf::internal::WireFormatLite::GetTagWireType(tag) ==
            ::google::protobuf::internal::WireFormatLite::WIRETYPE_END_GROUP) {
          goto success;
        }
        DO_(::google::protobuf::internal::WireFormatLite::SkipField(input, tag));
        break;
      }
    }
  }
success:
  // @@protoc_insertion_point(parse_success:protobuf_unittest.MessageContainingMapCalledEntry)
  return true;
failure:
  // @@protoc_insertion_point(parse_failure:protobuf_unittest.MessageContainingMapCalledEntry)
  return false;
#undef DO_
}

void MessageContainingMapCalledEntry::SerializeWithCachedSizes(
    ::google::protobuf::io::CodedOutputStream* output) const {
  // @@protoc_insertion_point(serialize_start:protobuf_unittest.MessageContainingMapCalledEntry)
  // map<int32, int32> entry = 1;
  if (!this->entry().empty()) {
    typedef ::google::protobuf::Map< ::google::protobuf::int32, ::google::protobuf::int32 >::const_pointer
        ConstPtr;
    typedef ::google::protobuf::internal::SortItem< ::google::protobuf::int32, ConstPtr > SortItem;
    typedef ::google::protobuf::internal::CompareByFirstField<SortItem> Less;

    if (output->IsSerializationDeterminstic() &&
        this->entry().size() > 1) {
      ::google::protobuf::scoped_array<SortItem> items(
          new SortItem[this->entry().size()]);
      typedef ::google::protobuf::Map< ::google::protobuf::int32, ::google::protobuf::int32 >::size_type size_type;
      size_type n = 0;
      for (::google::protobuf::Map< ::google::protobuf::int32, ::google::protobuf::int32 >::const_iterator
          it = this->entry().begin();
          it != this->entry().end(); ++it, ++n) {
        items[n] = SortItem(&*it);
      }
      ::std::sort(&items[0], &items[n], Less());
      ::google::protobuf::scoped_ptr<MessageContainingMapCalledEntry_EntryEntry> entry;
      for (size_type i = 0; i < n; i++) {
        entry.reset(entry_.NewEntryWrapper(
            items[i].second->first, items[i].second->second));
        ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
            1, *entry, output);
        if (entry->GetArena() != NULL) {
          entry.release();
        }
      }
    } else {
      ::google::protobuf::scoped_ptr<MessageContainingMapCalledEntry_EntryEntry> entry;
      for (::google::protobuf::Map< ::google::protobuf::int32, ::google::protobuf::int32 >::const_iterator
          it = this->entry().begin();
          it != this->entry().end(); ++it) {
        entry.reset(entry_.NewEntryWrapper(
            it->first, it->second));
        ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
            1, *entry, output);
        if (entry->GetArena() != NULL) {
          entry.release();
        }
      }
    }
  }

  // @@protoc_insertion_point(serialize_end:protobuf_unittest.MessageContainingMapCalledEntry)
}

::google::protobuf::uint8* MessageContainingMapCalledEntry::InternalSerializeWithCachedSizesToArray(
    bool deterministic, ::google::protobuf::uint8* target) const {
  (void)deterministic; // Unused
  // @@protoc_insertion_point(serialize_to_array_start:protobuf_unittest.MessageContainingMapCalledEntry)
  // map<int32, int32> entry = 1;
  if (!this->entry().empty()) {
    typedef ::google::protobuf::Map< ::google::protobuf::int32, ::google::protobuf::int32 >::const_pointer
        ConstPtr;
    typedef ::google::protobuf::internal::SortItem< ::google::protobuf::int32, ConstPtr > SortItem;
    typedef ::google::protobuf::internal::CompareByFirstField<SortItem> Less;

    if (deterministic &&
        this->entry().size() > 1) {
      ::google::protobuf::scoped_array<SortItem> items(
          new SortItem[this->entry().size()]);
      typedef ::google::protobuf::Map< ::google::protobuf::int32, ::google::protobuf::int32 >::size_type size_type;
      size_type n = 0;
      for (::google::protobuf::Map< ::google::protobuf::int32, ::google::protobuf::int32 >::const_iterator
          it = this->entry().begin();
          it != this->entry().end(); ++it, ++n) {
        items[n] = SortItem(&*it);
      }
      ::std::sort(&items[0], &items[n], Less());
      ::google::protobuf::scoped_ptr<MessageContainingMapCalledEntry_EntryEntry> entry;
      for (size_type i = 0; i < n; i++) {
        entry.reset(entry_.NewEntryWrapper(
            items[i].second->first, items[i].second->second));
        target = ::google::protobuf::internal::WireFormatLite::
                   InternalWriteMessageNoVirtualToArray(
                       1, *entry, deterministic, target);
;
        if (entry->GetArena() != NULL) {
          entry.release();
        }
      }
    } else {
      ::google::protobuf::scoped_ptr<MessageContainingMapCalledEntry_EntryEntry> entry;
      for (::google::protobuf::Map< ::google::protobuf::int32, ::google::protobuf::int32 >::const_iterator
          it = this->entry().begin();
          it != this->entry().end(); ++it) {
        entry.reset(entry_.NewEntryWrapper(
            it->first, it->second));
        target = ::google::protobuf::internal::WireFormatLite::
                   InternalWriteMessageNoVirtualToArray(
                       1, *entry, deterministic, target);
;
        if (entry->GetArena() != NULL) {
          entry.release();
        }
      }
    }
  }

  // @@protoc_insertion_point(serialize_to_array_end:protobuf_unittest.MessageContainingMapCalledEntry)
  return target;
}

size_t MessageContainingMapCalledEntry::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:protobuf_unittest.MessageContainingMapCalledEntry)
  size_t total_size = 0;

  // map<int32, int32> entry = 1;
  total_size += 1 *
      ::google::protobuf::internal::FromIntSize(this->entry_size());
  {
    ::google::protobuf::scoped_ptr<MessageContainingMapCalledEntry_EntryEntry> entry;
    for (::google::protobuf::Map< ::google::protobuf::int32, ::google::protobuf::int32 >::const_iterator
        it = this->entry().begin();
        it != this->entry().end(); ++it) {
      if (entry.get() != NULL && entry->GetArena() != NULL) {
        entry.release();
      }
      entry.reset(entry_.NewEntryWrapper(it->first, it->second));
      total_size += ::google::protobuf::internal::WireFormatLite::
          MessageSizeNoVirtual(*entry);
    }
    if (entry.get() != NULL && entry->GetArena() != NULL) {
      entry.release();
    }
  }

  int cached_size = ::google::protobuf::internal::ToCachedSize(total_size);
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = cached_size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
  return total_size;
}

void MessageContainingMapCalledEntry::MergeFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_merge_from_start:protobuf_unittest.MessageContainingMapCalledEntry)
  if (GOOGLE_PREDICT_FALSE(&from == this)) MergeFromFail(__LINE__);
  const MessageContainingMapCalledEntry* source =
      ::google::protobuf::internal::DynamicCastToGenerated<const MessageContainingMapCalledEntry>(
          &from);
  if (source == NULL) {
  // @@protoc_insertion_point(generalized_merge_from_cast_fail:protobuf_unittest.MessageContainingMapCalledEntry)
    ::google::protobuf::internal::ReflectionOps::Merge(from, this);
  } else {
  // @@protoc_insertion_point(generalized_merge_from_cast_success:protobuf_unittest.MessageContainingMapCalledEntry)
    UnsafeMergeFrom(*source);
  }
}

void MessageContainingMapCalledEntry::MergeFrom(const MessageContainingMapCalledEntry& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:protobuf_unittest.MessageContainingMapCalledEntry)
  if (GOOGLE_PREDICT_TRUE(&from != this)) {
    UnsafeMergeFrom(from);
  } else {
    MergeFromFail(__LINE__);
  }
}

void MessageContainingMapCalledEntry::UnsafeMergeFrom(const MessageContainingMapCalledEntry& from) {
  GOOGLE_DCHECK(&from != this);
  entry_.MergeFrom(from.entry_);
}

void MessageContainingMapCalledEntry::CopyFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_copy_from_start:protobuf_unittest.MessageContainingMapCalledEntry)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

void MessageContainingMapCalledEntry::CopyFrom(const MessageContainingMapCalledEntry& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:protobuf_unittest.MessageContainingMapCalledEntry)
  if (&from == this) return;
  Clear();
  UnsafeMergeFrom(from);
}

bool MessageContainingMapCalledEntry::IsInitialized() const {

  return true;
}

void MessageContainingMapCalledEntry::Swap(MessageContainingMapCalledEntry* other) {
  if (other == this) return;
  if (GetArenaNoVirtual() == other->GetArenaNoVirtual()) {
    InternalSwap(other);
  } else {
    MessageContainingMapCalledEntry temp;
    temp.UnsafeMergeFrom(*this);
    CopyFrom(*other);
    other->CopyFrom(temp);
  }
}
void MessageContainingMapCalledEntry::UnsafeArenaSwap(MessageContainingMapCalledEntry* other) {
  if (other == this) return;
  GOOGLE_DCHECK(GetArenaNoVirtual() == other->GetArenaNoVirtual());
  InternalSwap(other);
}
void MessageContainingMapCalledEntry::InternalSwap(MessageContainingMapCalledEntry* other) {
  entry_.Swap(&other->entry_);
  _internal_metadata_.Swap(&other->_internal_metadata_);
  std::swap(_cached_size_, other->_cached_size_);
}

::google::protobuf::Metadata MessageContainingMapCalledEntry::GetMetadata() const {
  protobuf_AssignDescriptorsOnce();
  ::google::protobuf::Metadata metadata;
  metadata.descriptor = MessageContainingMapCalledEntry_descriptor_;
  metadata.reflection = MessageContainingMapCalledEntry_reflection_;
  return metadata;
}

#if PROTOBUF_INLINE_NOT_IN_HEADERS
// MessageContainingMapCalledEntry

// map<int32, int32> entry = 1;
int MessageContainingMapCalledEntry::entry_size() const {
  return entry_.size();
}
void MessageContainingMapCalledEntry::clear_entry() {
  entry_.Clear();
}
 const ::google::protobuf::Map< ::google::protobuf::int32, ::google::protobuf::int32 >&
MessageContainingMapCalledEntry::entry() const {
  // @@protoc_insertion_point(field_map:protobuf_unittest.MessageContainingMapCalledEntry.entry)
  return entry_.GetMap();
}
 ::google::protobuf::Map< ::google::protobuf::int32, ::google::protobuf::int32 >*
MessageContainingMapCalledEntry::mutable_entry() {
  // @@protoc_insertion_point(field_mutable_map:protobuf_unittest.MessageContainingMapCalledEntry.entry)
  return entry_.MutableMap();
}

inline const MessageContainingMapCalledEntry* MessageContainingMapCalledEntry::internal_default_instance() {
  return &MessageContainingMapCalledEntry_default_instance_.get();
}
#endif  // PROTOBUF_INLINE_NOT_IN_HEADERS

// ===================================================================

#if !defined(_MSC_VER) || _MSC_VER >= 1900
const int TestRecursiveMapMessage::kAFieldNumber;
#endif  // !defined(_MSC_VER) || _MSC_VER >= 1900

TestRecursiveMapMessage::TestRecursiveMapMessage()
  : ::google::protobuf::Message(), _internal_metadata_(NULL) {
  if (this != internal_default_instance()) protobuf_InitDefaults_google_2fprotobuf_2fmap_5funittest_2eproto();
  SharedCtor();
  // @@protoc_insertion_point(constructor:protobuf_unittest.TestRecursiveMapMessage)
}
TestRecursiveMapMessage::TestRecursiveMapMessage(::google::protobuf::Arena* arena)
  : ::google::protobuf::Message(),
  _internal_metadata_(arena),
  a_(arena) {
#ifdef GOOGLE_PROTOBUF_NO_STATIC_INITIALIZER
  protobuf_InitDefaults_google_2fprotobuf_2fmap_5funittest_2eproto();
#endif  // GOOGLE_PROTOBUF_NO_STATIC_INITIALIZER
  SharedCtor();
  RegisterArenaDtor(arena);
  // @@protoc_insertion_point(arena_constructor:protobuf_unittest.TestRecursiveMapMessage)
}

void TestRecursiveMapMessage::InitAsDefaultInstance() {
}

TestRecursiveMapMessage::TestRecursiveMapMessage(const TestRecursiveMapMessage& from)
  : ::google::protobuf::Message(),
    _internal_metadata_(NULL) {
  SharedCtor();
  UnsafeMergeFrom(from);
  // @@protoc_insertion_point(copy_constructor:protobuf_unittest.TestRecursiveMapMessage)
}

void TestRecursiveMapMessage::SharedCtor() {
  a_.SetAssignDescriptorCallback(
      protobuf_AssignDescriptorsOnce);
  a_.SetEntryDescriptor(
      &::protobuf_unittest::TestRecursiveMapMessage_AEntry_descriptor_);
  _cached_size_ = 0;
}

TestRecursiveMapMessage::~TestRecursiveMapMessage() {
  // @@protoc_insertion_point(destructor:protobuf_unittest.TestRecursiveMapMessage)
  SharedDtor();
}

void TestRecursiveMapMessage::SharedDtor() {
  ::google::protobuf::Arena* arena = GetArenaNoVirtual();
  if (arena != NULL) {
    return;
  }

}

void TestRecursiveMapMessage::ArenaDtor(void* object) {
  TestRecursiveMapMessage* _this = reinterpret_cast< TestRecursiveMapMessage* >(object);
  (void)_this;
}
void TestRecursiveMapMessage::RegisterArenaDtor(::google::protobuf::Arena* arena) {
}
void TestRecursiveMapMessage::SetCachedSize(int size) const {
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
}
const ::google::protobuf::Descriptor* TestRecursiveMapMessage::descriptor() {
  protobuf_AssignDescriptorsOnce();
  return TestRecursiveMapMessage_descriptor_;
}

const TestRecursiveMapMessage& TestRecursiveMapMessage::default_instance() {
  protobuf_InitDefaults_google_2fprotobuf_2fmap_5funittest_2eproto();
  return *internal_default_instance();
}

::google::protobuf::internal::ExplicitlyConstructed<TestRecursiveMapMessage> TestRecursiveMapMessage_default_instance_;

TestRecursiveMapMessage* TestRecursiveMapMessage::New(::google::protobuf::Arena* arena) const {
  return ::google::protobuf::Arena::CreateMessage<TestRecursiveMapMessage>(arena);
}

void TestRecursiveMapMessage::Clear() {
// @@protoc_insertion_point(message_clear_start:protobuf_unittest.TestRecursiveMapMessage)
  a_.Clear();
}

bool TestRecursiveMapMessage::MergePartialFromCodedStream(
    ::google::protobuf::io::CodedInputStream* input) {
#define DO_(EXPRESSION) if (!GOOGLE_PREDICT_TRUE(EXPRESSION)) goto failure
  ::google::protobuf::uint32 tag;
  // @@protoc_insertion_point(parse_start:protobuf_unittest.TestRecursiveMapMessage)
  for (;;) {
    ::std::pair< ::google::protobuf::uint32, bool> p = input->ReadTagWithCutoff(127);
    tag = p.first;
    if (!p.second) goto handle_unusual;
    switch (::google::protobuf::internal::WireFormatLite::GetTagFieldNumber(tag)) {
      // map<string, .protobuf_unittest.TestRecursiveMapMessage> a = 1;
      case 1: {
        if (tag == 10) {
          DO_(input->IncrementRecursionDepth());
         parse_loop_a:
          TestRecursiveMapMessage_AEntry::Parser< ::google::protobuf::internal::MapField<
              ::std::string, ::protobuf_unittest::TestRecursiveMapMessage,
              ::google::protobuf::internal::WireFormatLite::TYPE_STRING,
              ::google::protobuf::internal::WireFormatLite::TYPE_MESSAGE,
              0 >,
            ::google::protobuf::Map< ::std::string, ::protobuf_unittest::TestRecursiveMapMessage > > parser(&a_);
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessageNoVirtual(
              input, &parser));
          DO_(::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
            parser.key().data(), parser.key().length(),
            ::google::protobuf::internal::WireFormatLite::PARSE,
            "protobuf_unittest.TestRecursiveMapMessage.AEntry.key"));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(10)) goto parse_loop_a;
        input->UnsafeDecrementRecursionDepth();
        if (input->ExpectAtEnd()) goto success;
        break;
      }

      default: {
      handle_unusual:
        if (tag == 0 ||
            ::google::protobuf::internal::WireFormatLite::GetTagWireType(tag) ==
            ::google::protobuf::internal::WireFormatLite::WIRETYPE_END_GROUP) {
          goto success;
        }
        DO_(::google::protobuf::internal::WireFormatLite::SkipField(input, tag));
        break;
      }
    }
  }
success:
  // @@protoc_insertion_point(parse_success:protobuf_unittest.TestRecursiveMapMessage)
  return true;
failure:
  // @@protoc_insertion_point(parse_failure:protobuf_unittest.TestRecursiveMapMessage)
  return false;
#undef DO_
}

void TestRecursiveMapMessage::SerializeWithCachedSizes(
    ::google::protobuf::io::CodedOutputStream* output) const {
  // @@protoc_insertion_point(serialize_start:protobuf_unittest.TestRecursiveMapMessage)
  // map<string, .protobuf_unittest.TestRecursiveMapMessage> a = 1;
  if (!this->a().empty()) {
    typedef ::google::protobuf::Map< ::std::string, ::protobuf_unittest::TestRecursiveMapMessage >::const_pointer
        ConstPtr;
    typedef ConstPtr SortItem;
    typedef ::google::protobuf::internal::CompareByDerefFirst<SortItem> Less;
    struct Utf8Check {
      static void Check(ConstPtr p) {
        ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
          p->first.data(), p->first.length(),
          ::google::protobuf::internal::WireFormatLite::SERIALIZE,
          "protobuf_unittest.TestRecursiveMapMessage.AEntry.key");
      }
    };

    if (output->IsSerializationDeterminstic() &&
        this->a().size() > 1) {
      ::google::protobuf::scoped_array<SortItem> items(
          new SortItem[this->a().size()]);
      typedef ::google::protobuf::Map< ::std::string, ::protobuf_unittest::TestRecursiveMapMessage >::size_type size_type;
      size_type n = 0;
      for (::google::protobuf::Map< ::std::string, ::protobuf_unittest::TestRecursiveMapMessage >::const_iterator
          it = this->a().begin();
          it != this->a().end(); ++it, ++n) {
        items[n] = SortItem(&*it);
      }
      ::std::sort(&items[0], &items[n], Less());
      ::google::protobuf::scoped_ptr<TestRecursiveMapMessage_AEntry> entry;
      for (size_type i = 0; i < n; i++) {
        entry.reset(a_.NewEntryWrapper(
            items[i]->first, items[i]->second));
        ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
            1, *entry, output);
        if (entry->GetArena() != NULL) {
          entry.release();
        }
        Utf8Check::Check(items[i]);
      }
    } else {
      ::google::protobuf::scoped_ptr<TestRecursiveMapMessage_AEntry> entry;
      for (::google::protobuf::Map< ::std::string, ::protobuf_unittest::TestRecursiveMapMessage >::const_iterator
          it = this->a().begin();
          it != this->a().end(); ++it) {
        entry.reset(a_.NewEntryWrapper(
            it->first, it->second));
        ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
            1, *entry, output);
        if (entry->GetArena() != NULL) {
          entry.release();
        }
        Utf8Check::Check(&*it);
      }
    }
  }

  // @@protoc_insertion_point(serialize_end:protobuf_unittest.TestRecursiveMapMessage)
}

::google::protobuf::uint8* TestRecursiveMapMessage::InternalSerializeWithCachedSizesToArray(
    bool deterministic, ::google::protobuf::uint8* target) const {
  (void)deterministic; // Unused
  // @@protoc_insertion_point(serialize_to_array_start:protobuf_unittest.TestRecursiveMapMessage)
  // map<string, .protobuf_unittest.TestRecursiveMapMessage> a = 1;
  if (!this->a().empty()) {
    typedef ::google::protobuf::Map< ::std::string, ::protobuf_unittest::TestRecursiveMapMessage >::const_pointer
        ConstPtr;
    typedef ConstPtr SortItem;
    typedef ::google::protobuf::internal::CompareByDerefFirst<SortItem> Less;
    struct Utf8Check {
      static void Check(ConstPtr p) {
        ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
          p->first.data(), p->first.length(),
          ::google::protobuf::internal::WireFormatLite::SERIALIZE,
          "protobuf_unittest.TestRecursiveMapMessage.AEntry.key");
      }
    };

    if (deterministic &&
        this->a().size() > 1) {
      ::google::protobuf::scoped_array<SortItem> items(
          new SortItem[this->a().size()]);
      typedef ::google::protobuf::Map< ::std::string, ::protobuf_unittest::TestRecursiveMapMessage >::size_type size_type;
      size_type n = 0;
      for (::google::protobuf::Map< ::std::string, ::protobuf_unittest::TestRecursiveMapMessage >::const_iterator
          it = this->a().begin();
          it != this->a().end(); ++it, ++n) {
        items[n] = SortItem(&*it);
      }
      ::std::sort(&items[0], &items[n], Less());
      ::google::protobuf::scoped_ptr<TestRecursiveMapMessage_AEntry> entry;
      for (size_type i = 0; i < n; i++) {
        entry.reset(a_.NewEntryWrapper(
            items[i]->first, items[i]->second));
        target = ::google::protobuf::internal::WireFormatLite::
                   InternalWriteMessageNoVirtualToArray(
                       1, *entry, deterministic, target);
;
        if (entry->GetArena() != NULL) {
          entry.release();
        }
        Utf8Check::Check(items[i]);
      }
    } else {
      ::google::protobuf::scoped_ptr<TestRecursiveMapMessage_AEntry> entry;
      for (::google::protobuf::Map< ::std::string, ::protobuf_unittest::TestRecursiveMapMessage >::const_iterator
          it = this->a().begin();
          it != this->a().end(); ++it) {
        entry.reset(a_.NewEntryWrapper(
            it->first, it->second));
        target = ::google::protobuf::internal::WireFormatLite::
                   InternalWriteMessageNoVirtualToArray(
                       1, *entry, deterministic, target);
;
        if (entry->GetArena() != NULL) {
          entry.release();
        }
        Utf8Check::Check(&*it);
      }
    }
  }

  // @@protoc_insertion_point(serialize_to_array_end:protobuf_unittest.TestRecursiveMapMessage)
  return target;
}

size_t TestRecursiveMapMessage::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:protobuf_unittest.TestRecursiveMapMessage)
  size_t total_size = 0;

  // map<string, .protobuf_unittest.TestRecursiveMapMessage> a = 1;
  total_size += 1 *
      ::google::protobuf::internal::FromIntSize(this->a_size());
  {
    ::google::protobuf::scoped_ptr<TestRecursiveMapMessage_AEntry> entry;
    for (::google::protobuf::Map< ::std::string, ::protobuf_unittest::TestRecursiveMapMessage >::const_iterator
        it = this->a().begin();
        it != this->a().end(); ++it) {
      if (entry.get() != NULL && entry->GetArena() != NULL) {
        entry.release();
      }
      entry.reset(a_.NewEntryWrapper(it->first, it->second));
      total_size += ::google::protobuf::internal::WireFormatLite::
          MessageSizeNoVirtual(*entry);
    }
    if (entry.get() != NULL && entry->GetArena() != NULL) {
      entry.release();
    }
  }

  int cached_size = ::google::protobuf::internal::ToCachedSize(total_size);
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = cached_size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
  return total_size;
}

void TestRecursiveMapMessage::MergeFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_merge_from_start:protobuf_unittest.TestRecursiveMapMessage)
  if (GOOGLE_PREDICT_FALSE(&from == this)) MergeFromFail(__LINE__);
  const TestRecursiveMapMessage* source =
      ::google::protobuf::internal::DynamicCastToGenerated<const TestRecursiveMapMessage>(
          &from);
  if (source == NULL) {
  // @@protoc_insertion_point(generalized_merge_from_cast_fail:protobuf_unittest.TestRecursiveMapMessage)
    ::google::protobuf::internal::ReflectionOps::Merge(from, this);
  } else {
  // @@protoc_insertion_point(generalized_merge_from_cast_success:protobuf_unittest.TestRecursiveMapMessage)
    UnsafeMergeFrom(*source);
  }
}

void TestRecursiveMapMessage::MergeFrom(const TestRecursiveMapMessage& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:protobuf_unittest.TestRecursiveMapMessage)
  if (GOOGLE_PREDICT_TRUE(&from != this)) {
    UnsafeMergeFrom(from);
  } else {
    MergeFromFail(__LINE__);
  }
}

void TestRecursiveMapMessage::UnsafeMergeFrom(const TestRecursiveMapMessage& from) {
  GOOGLE_DCHECK(&from != this);
  a_.MergeFrom(from.a_);
}

void TestRecursiveMapMessage::CopyFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_copy_from_start:protobuf_unittest.TestRecursiveMapMessage)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

void TestRecursiveMapMessage::CopyFrom(const TestRecursiveMapMessage& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:protobuf_unittest.TestRecursiveMapMessage)
  if (&from == this) return;
  Clear();
  UnsafeMergeFrom(from);
}

bool TestRecursiveMapMessage::IsInitialized() const {

  return true;
}

void TestRecursiveMapMessage::Swap(TestRecursiveMapMessage* other) {
  if (other == this) return;
  if (GetArenaNoVirtual() == other->GetArenaNoVirtual()) {
    InternalSwap(other);
  } else {
    TestRecursiveMapMessage temp;
    temp.UnsafeMergeFrom(*this);
    CopyFrom(*other);
    other->CopyFrom(temp);
  }
}
void TestRecursiveMapMessage::UnsafeArenaSwap(TestRecursiveMapMessage* other) {
  if (other == this) return;
  GOOGLE_DCHECK(GetArenaNoVirtual() == other->GetArenaNoVirtual());
  InternalSwap(other);
}
void TestRecursiveMapMessage::InternalSwap(TestRecursiveMapMessage* other) {
  a_.Swap(&other->a_);
  _internal_metadata_.Swap(&other->_internal_metadata_);
  std::swap(_cached_size_, other->_cached_size_);
}

::google::protobuf::Metadata TestRecursiveMapMessage::GetMetadata() const {
  protobuf_AssignDescriptorsOnce();
  ::google::protobuf::Metadata metadata;
  metadata.descriptor = TestRecursiveMapMessage_descriptor_;
  metadata.reflection = TestRecursiveMapMessage_reflection_;
  return metadata;
}

#if PROTOBUF_INLINE_NOT_IN_HEADERS
// TestRecursiveMapMessage

// map<string, .protobuf_unittest.TestRecursiveMapMessage> a = 1;
int TestRecursiveMapMessage::a_size() const {
  return a_.size();
}
void TestRecursiveMapMessage::clear_a() {
  a_.Clear();
}
 const ::google::protobuf::Map< ::std::string, ::protobuf_unittest::TestRecursiveMapMessage >&
TestRecursiveMapMessage::a() const {
  // @@protoc_insertion_point(field_map:protobuf_unittest.TestRecursiveMapMessage.a)
  return a_.GetMap();
}
 ::google::protobuf::Map< ::std::string, ::protobuf_unittest::TestRecursiveMapMessage >*
TestRecursiveMapMessage::mutable_a() {
  // @@protoc_insertion_point(field_mutable_map:protobuf_unittest.TestRecursiveMapMessage.a)
  return a_.MutableMap();
}

inline const TestRecursiveMapMessage* TestRecursiveMapMessage::internal_default_instance() {
  return &TestRecursiveMapMessage_default_instance_.get();
}
#endif  // PROTOBUF_INLINE_NOT_IN_HEADERS

// @@protoc_insertion_point(namespace_scope)

}  // namespace protobuf_unittest

// @@protoc_insertion_point(global_scope)

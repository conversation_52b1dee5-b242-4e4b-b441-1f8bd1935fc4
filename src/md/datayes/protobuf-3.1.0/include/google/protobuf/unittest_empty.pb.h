// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: google/protobuf/unittest_empty.proto

#ifndef PROTOBUF_google_2fprotobuf_2funittest_5fempty_2eproto__INCLUDED
#define PROTOBUF_google_2fprotobuf_2funittest_5fempty_2eproto__INCLUDED

#include <string>

#include <google/protobuf/stubs/common.h>

#if GOOGLE_PROTOBUF_VERSION < 3001000
#error This file was generated by a newer version of protoc which is
#error incompatible with your Protocol Buffer headers.  Please update
#error your headers.
#endif
#if 3001000 < GOOGLE_PROTOBUF_MIN_PROTOC_VERSION
#error This file was generated by an older version of protoc which is
#error incompatible with your Protocol Buffer headers.  Please
#error regenerate this file with a newer version of protoc.
#endif

#include <google/protobuf/arena.h>
#include <google/protobuf/arenastring.h>
#include <google/protobuf/generated_message_util.h>
#include <google/protobuf/metadata.h>
#include <google/protobuf/repeated_field.h>
#include <google/protobuf/extension_set.h>
// @@protoc_insertion_point(includes)

// Internal implementation detail -- do not call these.
void protobuf_AddDesc_google_2fprotobuf_2funittest_5fempty_2eproto();
void protobuf_InitDefaults_google_2fprotobuf_2funittest_5fempty_2eproto();
void protobuf_AssignDesc_google_2fprotobuf_2funittest_5fempty_2eproto();
void protobuf_ShutdownFile_google_2fprotobuf_2funittest_5fempty_2eproto();


// ===================================================================


// ===================================================================


// ===================================================================

#if !PROTOBUF_INLINE_NOT_IN_HEADERS
#endif  // !PROTOBUF_INLINE_NOT_IN_HEADERS

// @@protoc_insertion_point(namespace_scope)

// @@protoc_insertion_point(global_scope)

#endif  // PROTOBUF_google_2fprotobuf_2funittest_5fempty_2eproto__INCLUDED

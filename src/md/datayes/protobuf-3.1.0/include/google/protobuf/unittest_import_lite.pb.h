// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: google/protobuf/unittest_import_lite.proto

#ifndef PROTOBUF_google_2fprotobuf_2funittest_5fimport_5flite_2eproto__INCLUDED
#define PROTOBUF_google_2fprotobuf_2funittest_5fimport_5flite_2eproto__INCLUDED

#include <string>

#include <google/protobuf/stubs/common.h>

#if GOOGLE_PROTOBUF_VERSION < 3001000
#error This file was generated by a newer version of protoc which is
#error incompatible with your Protocol Buffer headers.  Please update
#error your headers.
#endif
#if 3001000 < GOOGLE_PROTOBUF_MIN_PROTOC_VERSION
#error This file was generated by an older version of protoc which is
#error incompatible with your Protocol Buffer headers.  Please
#error regenerate this file with a newer version of protoc.
#endif

#include <google/protobuf/arena.h>
#include <google/protobuf/arenastring.h>
#include <google/protobuf/generated_message_util.h>
#include <google/protobuf/message_lite.h>
#include <google/protobuf/repeated_field.h>
#include <google/protobuf/extension_set.h>
#include <google/protobuf/generated_enum_util.h>
#include <google/protobuf/unittest_import_public_lite.pb.h>  // IWYU pragma: export
// @@protoc_insertion_point(includes)

namespace protobuf_unittest_import {

// Internal implementation detail -- do not call these.
void protobuf_AddDesc_google_2fprotobuf_2funittest_5fimport_5flite_2eproto();
void protobuf_InitDefaults_google_2fprotobuf_2funittest_5fimport_5flite_2eproto();
void protobuf_AssignDesc_google_2fprotobuf_2funittest_5fimport_5flite_2eproto();
void protobuf_ShutdownFile_google_2fprotobuf_2funittest_5fimport_5flite_2eproto();

class ImportMessageLite;

enum ImportEnumLite {
  IMPORT_LITE_FOO = 7,
  IMPORT_LITE_BAR = 8,
  IMPORT_LITE_BAZ = 9
};
bool ImportEnumLite_IsValid(int value);
const ImportEnumLite ImportEnumLite_MIN = IMPORT_LITE_FOO;
const ImportEnumLite ImportEnumLite_MAX = IMPORT_LITE_BAZ;
const int ImportEnumLite_ARRAYSIZE = ImportEnumLite_MAX + 1;

// ===================================================================

class ImportMessageLite : public ::google::protobuf::MessageLite /* @@protoc_insertion_point(class_definition:protobuf_unittest_import.ImportMessageLite) */ {
 public:
  ImportMessageLite();
  virtual ~ImportMessageLite();

  ImportMessageLite(const ImportMessageLite& from);

  inline ImportMessageLite& operator=(const ImportMessageLite& from) {
    CopyFrom(from);
    return *this;
  }

  inline const ::std::string& unknown_fields() const {
    return _unknown_fields_.GetNoArena(
        &::google::protobuf::internal::GetEmptyStringAlreadyInited());
  }

  inline ::std::string* mutable_unknown_fields() {
    return _unknown_fields_.MutableNoArena(
        &::google::protobuf::internal::GetEmptyStringAlreadyInited());
  }

  static const ImportMessageLite& default_instance();

  static const ImportMessageLite* internal_default_instance();

  void Swap(ImportMessageLite* other);

  // implements Message ----------------------------------------------

  inline ImportMessageLite* New() const { return New(NULL); }

  ImportMessageLite* New(::google::protobuf::Arena* arena) const;
  void CheckTypeAndMergeFrom(const ::google::protobuf::MessageLite& from);
  void CopyFrom(const ImportMessageLite& from);
  void MergeFrom(const ImportMessageLite& from);
  void Clear();
  bool IsInitialized() const;

  size_t ByteSizeLong() const;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input);
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const;
  void DiscardUnknownFields();
  int GetCachedSize() const { return _cached_size_; }
  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const;
  void InternalSwap(ImportMessageLite* other);
  void UnsafeMergeFrom(const ImportMessageLite& from);
  private:
  inline ::google::protobuf::Arena* GetArenaNoVirtual() const {
    return _arena_ptr_;
  }
  inline ::google::protobuf::Arena* MaybeArenaPtr() const {
    return _arena_ptr_;
  }
  public:

  ::std::string GetTypeName() const;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // optional int32 d = 1;
  bool has_d() const;
  void clear_d();
  static const int kDFieldNumber = 1;
  ::google::protobuf::int32 d() const;
  void set_d(::google::protobuf::int32 value);

  // @@protoc_insertion_point(class_scope:protobuf_unittest_import.ImportMessageLite)
 private:
  inline void set_has_d();
  inline void clear_has_d();

  ::google::protobuf::internal::ArenaStringPtr _unknown_fields_;
  ::google::protobuf::Arena* _arena_ptr_;

  ::google::protobuf::internal::HasBits<1> _has_bits_;
  mutable int _cached_size_;
  ::google::protobuf::int32 d_;
  friend void  protobuf_InitDefaults_google_2fprotobuf_2funittest_5fimport_5flite_2eproto_impl();
  friend void  protobuf_AddDesc_google_2fprotobuf_2funittest_5fimport_5flite_2eproto_impl();
  friend void protobuf_AssignDesc_google_2fprotobuf_2funittest_5fimport_5flite_2eproto();
  friend void protobuf_ShutdownFile_google_2fprotobuf_2funittest_5fimport_5flite_2eproto();

  void InitAsDefaultInstance();
};
extern ::google::protobuf::internal::ExplicitlyConstructed<ImportMessageLite> ImportMessageLite_default_instance_;

// ===================================================================


// ===================================================================

#if !PROTOBUF_INLINE_NOT_IN_HEADERS
// ImportMessageLite

// optional int32 d = 1;
inline bool ImportMessageLite::has_d() const {
  return (_has_bits_[0] & 0x00000001u) != 0;
}
inline void ImportMessageLite::set_has_d() {
  _has_bits_[0] |= 0x00000001u;
}
inline void ImportMessageLite::clear_has_d() {
  _has_bits_[0] &= ~0x00000001u;
}
inline void ImportMessageLite::clear_d() {
  d_ = 0;
  clear_has_d();
}
inline ::google::protobuf::int32 ImportMessageLite::d() const {
  // @@protoc_insertion_point(field_get:protobuf_unittest_import.ImportMessageLite.d)
  return d_;
}
inline void ImportMessageLite::set_d(::google::protobuf::int32 value) {
  set_has_d();
  d_ = value;
  // @@protoc_insertion_point(field_set:protobuf_unittest_import.ImportMessageLite.d)
}

inline const ImportMessageLite* ImportMessageLite::internal_default_instance() {
  return &ImportMessageLite_default_instance_.get();
}
#endif  // !PROTOBUF_INLINE_NOT_IN_HEADERS

// @@protoc_insertion_point(namespace_scope)

}  // namespace protobuf_unittest_import

#ifndef SWIG
namespace google {
namespace protobuf {

template <> struct is_proto_enum< ::protobuf_unittest_import::ImportEnumLite> : ::google::protobuf::internal::true_type {};

}  // namespace protobuf
}  // namespace google
#endif  // SWIG

// @@protoc_insertion_point(global_scope)

#endif  // PROTOBUF_google_2fprotobuf_2funittest_5fimport_5flite_2eproto__INCLUDED

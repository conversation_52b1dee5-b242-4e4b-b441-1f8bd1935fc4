// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: google/protobuf/unittest_lite.proto

#ifndef PROTOBUF_google_2fprotobuf_2funittest_5flite_2eproto__INCLUDED
#define PROTOBUF_google_2fprotobuf_2funittest_5flite_2eproto__INCLUDED

#include <string>

#include <google/protobuf/stubs/common.h>

#if GOOGLE_PROTOBUF_VERSION < 3001000
#error This file was generated by a newer version of protoc which is
#error incompatible with your Protocol Buffer headers.  Please update
#error your headers.
#endif
#if 3001000 < GOOGLE_PROTOBUF_MIN_PROTOC_VERSION
#error This file was generated by an older version of protoc which is
#error incompatible with your Protocol Buffer headers.  Please
#error regenerate this file with a newer version of protoc.
#endif

#include <google/protobuf/arena.h>
#include <google/protobuf/arenastring.h>
#include <google/protobuf/generated_message_util.h>
#include <google/protobuf/message_lite.h>
#include <google/protobuf/repeated_field.h>
#include <google/protobuf/extension_set.h>
#include <google/protobuf/generated_enum_util.h>
#include <google/protobuf/unittest_import_lite.pb.h>
// @@protoc_insertion_point(includes)

namespace protobuf_unittest {

// Internal implementation detail -- do not call these.
void protobuf_AddDesc_google_2fprotobuf_2funittest_5flite_2eproto();
void protobuf_InitDefaults_google_2fprotobuf_2funittest_5flite_2eproto();
void protobuf_AssignDesc_google_2fprotobuf_2funittest_5flite_2eproto();
void protobuf_ShutdownFile_google_2fprotobuf_2funittest_5flite_2eproto();

class ForeignMessageLite;
class OptionalGroup_extension_lite;
class RepeatedGroup_extension_lite;
class TestAllExtensionsLite;
class TestAllTypesLite;
class TestAllTypesLite_NestedMessage;
class TestAllTypesLite_OptionalGroup;
class TestAllTypesLite_RepeatedGroup;
class TestDeprecatedLite;
class TestEmptyMessageLite;
class TestEmptyMessageWithExtensionsLite;
class TestNestedExtensionLite;
class TestPackedExtensionsLite;
class TestPackedTypesLite;
class TestParsingMergeLite;
class TestParsingMergeLite_OptionalGroup;
class TestParsingMergeLite_RepeatedFieldsGenerator;
class TestParsingMergeLite_RepeatedFieldsGenerator_Group1;
class TestParsingMergeLite_RepeatedFieldsGenerator_Group2;
class TestParsingMergeLite_RepeatedGroup;
class V1MessageLite;
class V2MessageLite;

enum TestAllTypesLite_NestedEnum {
  TestAllTypesLite_NestedEnum_FOO = 1,
  TestAllTypesLite_NestedEnum_BAR = 2,
  TestAllTypesLite_NestedEnum_BAZ = 3
};
bool TestAllTypesLite_NestedEnum_IsValid(int value);
const TestAllTypesLite_NestedEnum TestAllTypesLite_NestedEnum_NestedEnum_MIN = TestAllTypesLite_NestedEnum_FOO;
const TestAllTypesLite_NestedEnum TestAllTypesLite_NestedEnum_NestedEnum_MAX = TestAllTypesLite_NestedEnum_BAZ;
const int TestAllTypesLite_NestedEnum_NestedEnum_ARRAYSIZE = TestAllTypesLite_NestedEnum_NestedEnum_MAX + 1;

enum ForeignEnumLite {
  FOREIGN_LITE_FOO = 4,
  FOREIGN_LITE_BAR = 5,
  FOREIGN_LITE_BAZ = 6
};
bool ForeignEnumLite_IsValid(int value);
const ForeignEnumLite ForeignEnumLite_MIN = FOREIGN_LITE_FOO;
const ForeignEnumLite ForeignEnumLite_MAX = FOREIGN_LITE_BAZ;
const int ForeignEnumLite_ARRAYSIZE = ForeignEnumLite_MAX + 1;

enum V1EnumLite {
  V1_FIRST = 1
};
bool V1EnumLite_IsValid(int value);
const V1EnumLite V1EnumLite_MIN = V1_FIRST;
const V1EnumLite V1EnumLite_MAX = V1_FIRST;
const int V1EnumLite_ARRAYSIZE = V1EnumLite_MAX + 1;

enum V2EnumLite {
  V2_FIRST = 1,
  V2_SECOND = 2
};
bool V2EnumLite_IsValid(int value);
const V2EnumLite V2EnumLite_MIN = V2_FIRST;
const V2EnumLite V2EnumLite_MAX = V2_SECOND;
const int V2EnumLite_ARRAYSIZE = V2EnumLite_MAX + 1;

// ===================================================================

class TestAllTypesLite_NestedMessage : public ::google::protobuf::MessageLite /* @@protoc_insertion_point(class_definition:protobuf_unittest.TestAllTypesLite.NestedMessage) */ {
 public:
  TestAllTypesLite_NestedMessage();
  virtual ~TestAllTypesLite_NestedMessage();

  TestAllTypesLite_NestedMessage(const TestAllTypesLite_NestedMessage& from);

  inline TestAllTypesLite_NestedMessage& operator=(const TestAllTypesLite_NestedMessage& from) {
    CopyFrom(from);
    return *this;
  }

  inline const ::std::string& unknown_fields() const {
    return _unknown_fields_.GetNoArena(
        &::google::protobuf::internal::GetEmptyStringAlreadyInited());
  }

  inline ::std::string* mutable_unknown_fields() {
    return _unknown_fields_.MutableNoArena(
        &::google::protobuf::internal::GetEmptyStringAlreadyInited());
  }

  static const TestAllTypesLite_NestedMessage& default_instance();

  static const TestAllTypesLite_NestedMessage* internal_default_instance();

  void Swap(TestAllTypesLite_NestedMessage* other);

  // implements Message ----------------------------------------------

  inline TestAllTypesLite_NestedMessage* New() const { return New(NULL); }

  TestAllTypesLite_NestedMessage* New(::google::protobuf::Arena* arena) const;
  void CheckTypeAndMergeFrom(const ::google::protobuf::MessageLite& from);
  void CopyFrom(const TestAllTypesLite_NestedMessage& from);
  void MergeFrom(const TestAllTypesLite_NestedMessage& from);
  void Clear();
  bool IsInitialized() const;

  size_t ByteSizeLong() const;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input);
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const;
  void DiscardUnknownFields();
  int GetCachedSize() const { return _cached_size_; }
  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const;
  void InternalSwap(TestAllTypesLite_NestedMessage* other);
  void UnsafeMergeFrom(const TestAllTypesLite_NestedMessage& from);
  private:
  inline ::google::protobuf::Arena* GetArenaNoVirtual() const {
    return _arena_ptr_;
  }
  inline ::google::protobuf::Arena* MaybeArenaPtr() const {
    return _arena_ptr_;
  }
  public:

  ::std::string GetTypeName() const;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // optional int32 bb = 1;
  bool has_bb() const;
  void clear_bb();
  static const int kBbFieldNumber = 1;
  ::google::protobuf::int32 bb() const;
  void set_bb(::google::protobuf::int32 value);

  // optional int64 cc = 2;
  bool has_cc() const;
  void clear_cc();
  static const int kCcFieldNumber = 2;
  ::google::protobuf::int64 cc() const;
  void set_cc(::google::protobuf::int64 value);

  // @@protoc_insertion_point(class_scope:protobuf_unittest.TestAllTypesLite.NestedMessage)
 private:
  inline void set_has_bb();
  inline void clear_has_bb();
  inline void set_has_cc();
  inline void clear_has_cc();

  ::google::protobuf::internal::ArenaStringPtr _unknown_fields_;
  ::google::protobuf::Arena* _arena_ptr_;

  ::google::protobuf::internal::HasBits<1> _has_bits_;
  mutable int _cached_size_;
  ::google::protobuf::int64 cc_;
  ::google::protobuf::int32 bb_;
  friend void  protobuf_InitDefaults_google_2fprotobuf_2funittest_5flite_2eproto_impl();
  friend void  protobuf_AddDesc_google_2fprotobuf_2funittest_5flite_2eproto_impl();
  friend void protobuf_AssignDesc_google_2fprotobuf_2funittest_5flite_2eproto();
  friend void protobuf_ShutdownFile_google_2fprotobuf_2funittest_5flite_2eproto();

  void InitAsDefaultInstance();
};
extern ::google::protobuf::internal::ExplicitlyConstructed<TestAllTypesLite_NestedMessage> TestAllTypesLite_NestedMessage_default_instance_;

// -------------------------------------------------------------------

class TestAllTypesLite_OptionalGroup : public ::google::protobuf::MessageLite /* @@protoc_insertion_point(class_definition:protobuf_unittest.TestAllTypesLite.OptionalGroup) */ {
 public:
  TestAllTypesLite_OptionalGroup();
  virtual ~TestAllTypesLite_OptionalGroup();

  TestAllTypesLite_OptionalGroup(const TestAllTypesLite_OptionalGroup& from);

  inline TestAllTypesLite_OptionalGroup& operator=(const TestAllTypesLite_OptionalGroup& from) {
    CopyFrom(from);
    return *this;
  }

  inline const ::std::string& unknown_fields() const {
    return _unknown_fields_.GetNoArena(
        &::google::protobuf::internal::GetEmptyStringAlreadyInited());
  }

  inline ::std::string* mutable_unknown_fields() {
    return _unknown_fields_.MutableNoArena(
        &::google::protobuf::internal::GetEmptyStringAlreadyInited());
  }

  static const TestAllTypesLite_OptionalGroup& default_instance();

  static const TestAllTypesLite_OptionalGroup* internal_default_instance();

  void Swap(TestAllTypesLite_OptionalGroup* other);

  // implements Message ----------------------------------------------

  inline TestAllTypesLite_OptionalGroup* New() const { return New(NULL); }

  TestAllTypesLite_OptionalGroup* New(::google::protobuf::Arena* arena) const;
  void CheckTypeAndMergeFrom(const ::google::protobuf::MessageLite& from);
  void CopyFrom(const TestAllTypesLite_OptionalGroup& from);
  void MergeFrom(const TestAllTypesLite_OptionalGroup& from);
  void Clear();
  bool IsInitialized() const;

  size_t ByteSizeLong() const;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input);
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const;
  void DiscardUnknownFields();
  int GetCachedSize() const { return _cached_size_; }
  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const;
  void InternalSwap(TestAllTypesLite_OptionalGroup* other);
  void UnsafeMergeFrom(const TestAllTypesLite_OptionalGroup& from);
  private:
  inline ::google::protobuf::Arena* GetArenaNoVirtual() const {
    return _arena_ptr_;
  }
  inline ::google::protobuf::Arena* MaybeArenaPtr() const {
    return _arena_ptr_;
  }
  public:

  ::std::string GetTypeName() const;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // optional int32 a = 17;
  bool has_a() const;
  void clear_a();
  static const int kAFieldNumber = 17;
  ::google::protobuf::int32 a() const;
  void set_a(::google::protobuf::int32 value);

  // @@protoc_insertion_point(class_scope:protobuf_unittest.TestAllTypesLite.OptionalGroup)
 private:
  inline void set_has_a();
  inline void clear_has_a();

  ::google::protobuf::internal::ArenaStringPtr _unknown_fields_;
  ::google::protobuf::Arena* _arena_ptr_;

  ::google::protobuf::internal::HasBits<1> _has_bits_;
  mutable int _cached_size_;
  ::google::protobuf::int32 a_;
  friend void  protobuf_InitDefaults_google_2fprotobuf_2funittest_5flite_2eproto_impl();
  friend void  protobuf_AddDesc_google_2fprotobuf_2funittest_5flite_2eproto_impl();
  friend void protobuf_AssignDesc_google_2fprotobuf_2funittest_5flite_2eproto();
  friend void protobuf_ShutdownFile_google_2fprotobuf_2funittest_5flite_2eproto();

  void InitAsDefaultInstance();
};
extern ::google::protobuf::internal::ExplicitlyConstructed<TestAllTypesLite_OptionalGroup> TestAllTypesLite_OptionalGroup_default_instance_;

// -------------------------------------------------------------------

class TestAllTypesLite_RepeatedGroup : public ::google::protobuf::MessageLite /* @@protoc_insertion_point(class_definition:protobuf_unittest.TestAllTypesLite.RepeatedGroup) */ {
 public:
  TestAllTypesLite_RepeatedGroup();
  virtual ~TestAllTypesLite_RepeatedGroup();

  TestAllTypesLite_RepeatedGroup(const TestAllTypesLite_RepeatedGroup& from);

  inline TestAllTypesLite_RepeatedGroup& operator=(const TestAllTypesLite_RepeatedGroup& from) {
    CopyFrom(from);
    return *this;
  }

  inline const ::std::string& unknown_fields() const {
    return _unknown_fields_.GetNoArena(
        &::google::protobuf::internal::GetEmptyStringAlreadyInited());
  }

  inline ::std::string* mutable_unknown_fields() {
    return _unknown_fields_.MutableNoArena(
        &::google::protobuf::internal::GetEmptyStringAlreadyInited());
  }

  static const TestAllTypesLite_RepeatedGroup& default_instance();

  static const TestAllTypesLite_RepeatedGroup* internal_default_instance();

  void Swap(TestAllTypesLite_RepeatedGroup* other);

  // implements Message ----------------------------------------------

  inline TestAllTypesLite_RepeatedGroup* New() const { return New(NULL); }

  TestAllTypesLite_RepeatedGroup* New(::google::protobuf::Arena* arena) const;
  void CheckTypeAndMergeFrom(const ::google::protobuf::MessageLite& from);
  void CopyFrom(const TestAllTypesLite_RepeatedGroup& from);
  void MergeFrom(const TestAllTypesLite_RepeatedGroup& from);
  void Clear();
  bool IsInitialized() const;

  size_t ByteSizeLong() const;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input);
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const;
  void DiscardUnknownFields();
  int GetCachedSize() const { return _cached_size_; }
  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const;
  void InternalSwap(TestAllTypesLite_RepeatedGroup* other);
  void UnsafeMergeFrom(const TestAllTypesLite_RepeatedGroup& from);
  private:
  inline ::google::protobuf::Arena* GetArenaNoVirtual() const {
    return _arena_ptr_;
  }
  inline ::google::protobuf::Arena* MaybeArenaPtr() const {
    return _arena_ptr_;
  }
  public:

  ::std::string GetTypeName() const;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // optional int32 a = 47;
  bool has_a() const;
  void clear_a();
  static const int kAFieldNumber = 47;
  ::google::protobuf::int32 a() const;
  void set_a(::google::protobuf::int32 value);

  // @@protoc_insertion_point(class_scope:protobuf_unittest.TestAllTypesLite.RepeatedGroup)
 private:
  inline void set_has_a();
  inline void clear_has_a();

  ::google::protobuf::internal::ArenaStringPtr _unknown_fields_;
  ::google::protobuf::Arena* _arena_ptr_;

  ::google::protobuf::internal::HasBits<1> _has_bits_;
  mutable int _cached_size_;
  ::google::protobuf::int32 a_;
  friend void  protobuf_InitDefaults_google_2fprotobuf_2funittest_5flite_2eproto_impl();
  friend void  protobuf_AddDesc_google_2fprotobuf_2funittest_5flite_2eproto_impl();
  friend void protobuf_AssignDesc_google_2fprotobuf_2funittest_5flite_2eproto();
  friend void protobuf_ShutdownFile_google_2fprotobuf_2funittest_5flite_2eproto();

  void InitAsDefaultInstance();
};
extern ::google::protobuf::internal::ExplicitlyConstructed<TestAllTypesLite_RepeatedGroup> TestAllTypesLite_RepeatedGroup_default_instance_;

// -------------------------------------------------------------------

class TestAllTypesLite : public ::google::protobuf::MessageLite /* @@protoc_insertion_point(class_definition:protobuf_unittest.TestAllTypesLite) */ {
 public:
  TestAllTypesLite();
  virtual ~TestAllTypesLite();

  TestAllTypesLite(const TestAllTypesLite& from);

  inline TestAllTypesLite& operator=(const TestAllTypesLite& from) {
    CopyFrom(from);
    return *this;
  }

  inline const ::std::string& unknown_fields() const {
    return _unknown_fields_.GetNoArena(
        &::google::protobuf::internal::GetEmptyStringAlreadyInited());
  }

  inline ::std::string* mutable_unknown_fields() {
    return _unknown_fields_.MutableNoArena(
        &::google::protobuf::internal::GetEmptyStringAlreadyInited());
  }

  static const TestAllTypesLite& default_instance();

  enum OneofFieldCase {
    kOneofUint32 = 111,
    kOneofNestedMessage = 112,
    kOneofString = 113,
    kOneofBytes = 114,
    kOneofLazyNestedMessage = 115,
    ONEOF_FIELD_NOT_SET = 0,
  };

  static const TestAllTypesLite* internal_default_instance();

  void Swap(TestAllTypesLite* other);

  // implements Message ----------------------------------------------

  inline TestAllTypesLite* New() const { return New(NULL); }

  TestAllTypesLite* New(::google::protobuf::Arena* arena) const;
  void CheckTypeAndMergeFrom(const ::google::protobuf::MessageLite& from);
  void CopyFrom(const TestAllTypesLite& from);
  void MergeFrom(const TestAllTypesLite& from);
  void Clear();
  bool IsInitialized() const;

  size_t ByteSizeLong() const;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input);
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const;
  void DiscardUnknownFields();
  int GetCachedSize() const { return _cached_size_; }
  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const;
  void InternalSwap(TestAllTypesLite* other);
  void UnsafeMergeFrom(const TestAllTypesLite& from);
  private:
  inline ::google::protobuf::Arena* GetArenaNoVirtual() const {
    return _arena_ptr_;
  }
  inline ::google::protobuf::Arena* MaybeArenaPtr() const {
    return _arena_ptr_;
  }
  public:

  ::std::string GetTypeName() const;

  // nested types ----------------------------------------------------

  typedef TestAllTypesLite_NestedMessage NestedMessage;
  typedef TestAllTypesLite_OptionalGroup OptionalGroup;
  typedef TestAllTypesLite_RepeatedGroup RepeatedGroup;

  typedef TestAllTypesLite_NestedEnum NestedEnum;
  static const NestedEnum FOO =
    TestAllTypesLite_NestedEnum_FOO;
  static const NestedEnum BAR =
    TestAllTypesLite_NestedEnum_BAR;
  static const NestedEnum BAZ =
    TestAllTypesLite_NestedEnum_BAZ;
  static inline bool NestedEnum_IsValid(int value) {
    return TestAllTypesLite_NestedEnum_IsValid(value);
  }
  static const NestedEnum NestedEnum_MIN =
    TestAllTypesLite_NestedEnum_NestedEnum_MIN;
  static const NestedEnum NestedEnum_MAX =
    TestAllTypesLite_NestedEnum_NestedEnum_MAX;
  static const int NestedEnum_ARRAYSIZE =
    TestAllTypesLite_NestedEnum_NestedEnum_ARRAYSIZE;

  // accessors -------------------------------------------------------

  // optional int32 optional_int32 = 1;
  bool has_optional_int32() const;
  void clear_optional_int32();
  static const int kOptionalInt32FieldNumber = 1;
  ::google::protobuf::int32 optional_int32() const;
  void set_optional_int32(::google::protobuf::int32 value);

  // optional int64 optional_int64 = 2;
  bool has_optional_int64() const;
  void clear_optional_int64();
  static const int kOptionalInt64FieldNumber = 2;
  ::google::protobuf::int64 optional_int64() const;
  void set_optional_int64(::google::protobuf::int64 value);

  // optional uint32 optional_uint32 = 3;
  bool has_optional_uint32() const;
  void clear_optional_uint32();
  static const int kOptionalUint32FieldNumber = 3;
  ::google::protobuf::uint32 optional_uint32() const;
  void set_optional_uint32(::google::protobuf::uint32 value);

  // optional uint64 optional_uint64 = 4;
  bool has_optional_uint64() const;
  void clear_optional_uint64();
  static const int kOptionalUint64FieldNumber = 4;
  ::google::protobuf::uint64 optional_uint64() const;
  void set_optional_uint64(::google::protobuf::uint64 value);

  // optional sint32 optional_sint32 = 5;
  bool has_optional_sint32() const;
  void clear_optional_sint32();
  static const int kOptionalSint32FieldNumber = 5;
  ::google::protobuf::int32 optional_sint32() const;
  void set_optional_sint32(::google::protobuf::int32 value);

  // optional sint64 optional_sint64 = 6;
  bool has_optional_sint64() const;
  void clear_optional_sint64();
  static const int kOptionalSint64FieldNumber = 6;
  ::google::protobuf::int64 optional_sint64() const;
  void set_optional_sint64(::google::protobuf::int64 value);

  // optional fixed32 optional_fixed32 = 7;
  bool has_optional_fixed32() const;
  void clear_optional_fixed32();
  static const int kOptionalFixed32FieldNumber = 7;
  ::google::protobuf::uint32 optional_fixed32() const;
  void set_optional_fixed32(::google::protobuf::uint32 value);

  // optional fixed64 optional_fixed64 = 8;
  bool has_optional_fixed64() const;
  void clear_optional_fixed64();
  static const int kOptionalFixed64FieldNumber = 8;
  ::google::protobuf::uint64 optional_fixed64() const;
  void set_optional_fixed64(::google::protobuf::uint64 value);

  // optional sfixed32 optional_sfixed32 = 9;
  bool has_optional_sfixed32() const;
  void clear_optional_sfixed32();
  static const int kOptionalSfixed32FieldNumber = 9;
  ::google::protobuf::int32 optional_sfixed32() const;
  void set_optional_sfixed32(::google::protobuf::int32 value);

  // optional sfixed64 optional_sfixed64 = 10;
  bool has_optional_sfixed64() const;
  void clear_optional_sfixed64();
  static const int kOptionalSfixed64FieldNumber = 10;
  ::google::protobuf::int64 optional_sfixed64() const;
  void set_optional_sfixed64(::google::protobuf::int64 value);

  // optional float optional_float = 11;
  bool has_optional_float() const;
  void clear_optional_float();
  static const int kOptionalFloatFieldNumber = 11;
  float optional_float() const;
  void set_optional_float(float value);

  // optional double optional_double = 12;
  bool has_optional_double() const;
  void clear_optional_double();
  static const int kOptionalDoubleFieldNumber = 12;
  double optional_double() const;
  void set_optional_double(double value);

  // optional bool optional_bool = 13;
  bool has_optional_bool() const;
  void clear_optional_bool();
  static const int kOptionalBoolFieldNumber = 13;
  bool optional_bool() const;
  void set_optional_bool(bool value);

  // optional string optional_string = 14;
  bool has_optional_string() const;
  void clear_optional_string();
  static const int kOptionalStringFieldNumber = 14;
  const ::std::string& optional_string() const;
  void set_optional_string(const ::std::string& value);
  void set_optional_string(const char* value);
  void set_optional_string(const char* value, size_t size);
  ::std::string* mutable_optional_string();
  ::std::string* release_optional_string();
  void set_allocated_optional_string(::std::string* optional_string);

  // optional bytes optional_bytes = 15;
  bool has_optional_bytes() const;
  void clear_optional_bytes();
  static const int kOptionalBytesFieldNumber = 15;
  const ::std::string& optional_bytes() const;
  void set_optional_bytes(const ::std::string& value);
  void set_optional_bytes(const char* value);
  void set_optional_bytes(const void* value, size_t size);
  ::std::string* mutable_optional_bytes();
  ::std::string* release_optional_bytes();
  void set_allocated_optional_bytes(::std::string* optional_bytes);

  // optional group OptionalGroup = 16 { ... };
  bool has_optionalgroup() const;
  void clear_optionalgroup();
  static const int kOptionalgroupFieldNumber = 16;
  const ::protobuf_unittest::TestAllTypesLite_OptionalGroup& optionalgroup() const;
  ::protobuf_unittest::TestAllTypesLite_OptionalGroup* mutable_optionalgroup();
  ::protobuf_unittest::TestAllTypesLite_OptionalGroup* release_optionalgroup();
  void set_allocated_optionalgroup(::protobuf_unittest::TestAllTypesLite_OptionalGroup* optionalgroup);

  // optional .protobuf_unittest.TestAllTypesLite.NestedMessage optional_nested_message = 18;
  bool has_optional_nested_message() const;
  void clear_optional_nested_message();
  static const int kOptionalNestedMessageFieldNumber = 18;
  const ::protobuf_unittest::TestAllTypesLite_NestedMessage& optional_nested_message() const;
  ::protobuf_unittest::TestAllTypesLite_NestedMessage* mutable_optional_nested_message();
  ::protobuf_unittest::TestAllTypesLite_NestedMessage* release_optional_nested_message();
  void set_allocated_optional_nested_message(::protobuf_unittest::TestAllTypesLite_NestedMessage* optional_nested_message);

  // optional .protobuf_unittest.ForeignMessageLite optional_foreign_message = 19;
  bool has_optional_foreign_message() const;
  void clear_optional_foreign_message();
  static const int kOptionalForeignMessageFieldNumber = 19;
  const ::protobuf_unittest::ForeignMessageLite& optional_foreign_message() const;
  ::protobuf_unittest::ForeignMessageLite* mutable_optional_foreign_message();
  ::protobuf_unittest::ForeignMessageLite* release_optional_foreign_message();
  void set_allocated_optional_foreign_message(::protobuf_unittest::ForeignMessageLite* optional_foreign_message);

  // optional .protobuf_unittest_import.ImportMessageLite optional_import_message = 20;
  bool has_optional_import_message() const;
  void clear_optional_import_message();
  static const int kOptionalImportMessageFieldNumber = 20;
  const ::protobuf_unittest_import::ImportMessageLite& optional_import_message() const;
  ::protobuf_unittest_import::ImportMessageLite* mutable_optional_import_message();
  ::protobuf_unittest_import::ImportMessageLite* release_optional_import_message();
  void set_allocated_optional_import_message(::protobuf_unittest_import::ImportMessageLite* optional_import_message);

  // optional .protobuf_unittest.TestAllTypesLite.NestedEnum optional_nested_enum = 21;
  bool has_optional_nested_enum() const;
  void clear_optional_nested_enum();
  static const int kOptionalNestedEnumFieldNumber = 21;
  ::protobuf_unittest::TestAllTypesLite_NestedEnum optional_nested_enum() const;
  void set_optional_nested_enum(::protobuf_unittest::TestAllTypesLite_NestedEnum value);

  // optional .protobuf_unittest.ForeignEnumLite optional_foreign_enum = 22;
  bool has_optional_foreign_enum() const;
  void clear_optional_foreign_enum();
  static const int kOptionalForeignEnumFieldNumber = 22;
  ::protobuf_unittest::ForeignEnumLite optional_foreign_enum() const;
  void set_optional_foreign_enum(::protobuf_unittest::ForeignEnumLite value);

  // optional .protobuf_unittest_import.ImportEnumLite optional_import_enum = 23;
  bool has_optional_import_enum() const;
  void clear_optional_import_enum();
  static const int kOptionalImportEnumFieldNumber = 23;
  ::protobuf_unittest_import::ImportEnumLite optional_import_enum() const;
  void set_optional_import_enum(::protobuf_unittest_import::ImportEnumLite value);

  // optional string optional_string_piece = 24 [ctype = STRING_PIECE];
  bool has_optional_string_piece() const;
  void clear_optional_string_piece();
  static const int kOptionalStringPieceFieldNumber = 24;
 private:
  // Hidden due to unknown ctype option.
  const ::std::string& optional_string_piece() const;
  void set_optional_string_piece(const ::std::string& value);
  void set_optional_string_piece(const char* value);
  void set_optional_string_piece(const char* value, size_t size);
  ::std::string* mutable_optional_string_piece();
  ::std::string* release_optional_string_piece();
  void set_allocated_optional_string_piece(::std::string* optional_string_piece);
 public:

  // optional string optional_cord = 25 [ctype = CORD];
  bool has_optional_cord() const;
  void clear_optional_cord();
  static const int kOptionalCordFieldNumber = 25;
 private:
  // Hidden due to unknown ctype option.
  const ::std::string& optional_cord() const;
  void set_optional_cord(const ::std::string& value);
  void set_optional_cord(const char* value);
  void set_optional_cord(const char* value, size_t size);
  ::std::string* mutable_optional_cord();
  ::std::string* release_optional_cord();
  void set_allocated_optional_cord(::std::string* optional_cord);
 public:

  // optional .protobuf_unittest_import.PublicImportMessageLite optional_public_import_message = 26;
  bool has_optional_public_import_message() const;
  void clear_optional_public_import_message();
  static const int kOptionalPublicImportMessageFieldNumber = 26;
  const ::protobuf_unittest_import::PublicImportMessageLite& optional_public_import_message() const;
  ::protobuf_unittest_import::PublicImportMessageLite* mutable_optional_public_import_message();
  ::protobuf_unittest_import::PublicImportMessageLite* release_optional_public_import_message();
  void set_allocated_optional_public_import_message(::protobuf_unittest_import::PublicImportMessageLite* optional_public_import_message);

  // optional .protobuf_unittest.TestAllTypesLite.NestedMessage optional_lazy_message = 27 [lazy = true];
  bool has_optional_lazy_message() const;
  void clear_optional_lazy_message();
  static const int kOptionalLazyMessageFieldNumber = 27;
  const ::protobuf_unittest::TestAllTypesLite_NestedMessage& optional_lazy_message() const;
  ::protobuf_unittest::TestAllTypesLite_NestedMessage* mutable_optional_lazy_message();
  ::protobuf_unittest::TestAllTypesLite_NestedMessage* release_optional_lazy_message();
  void set_allocated_optional_lazy_message(::protobuf_unittest::TestAllTypesLite_NestedMessage* optional_lazy_message);

  // repeated int32 repeated_int32 = 31;
  int repeated_int32_size() const;
  void clear_repeated_int32();
  static const int kRepeatedInt32FieldNumber = 31;
  ::google::protobuf::int32 repeated_int32(int index) const;
  void set_repeated_int32(int index, ::google::protobuf::int32 value);
  void add_repeated_int32(::google::protobuf::int32 value);
  const ::google::protobuf::RepeatedField< ::google::protobuf::int32 >&
      repeated_int32() const;
  ::google::protobuf::RepeatedField< ::google::protobuf::int32 >*
      mutable_repeated_int32();

  // repeated int64 repeated_int64 = 32;
  int repeated_int64_size() const;
  void clear_repeated_int64();
  static const int kRepeatedInt64FieldNumber = 32;
  ::google::protobuf::int64 repeated_int64(int index) const;
  void set_repeated_int64(int index, ::google::protobuf::int64 value);
  void add_repeated_int64(::google::protobuf::int64 value);
  const ::google::protobuf::RepeatedField< ::google::protobuf::int64 >&
      repeated_int64() const;
  ::google::protobuf::RepeatedField< ::google::protobuf::int64 >*
      mutable_repeated_int64();

  // repeated uint32 repeated_uint32 = 33;
  int repeated_uint32_size() const;
  void clear_repeated_uint32();
  static const int kRepeatedUint32FieldNumber = 33;
  ::google::protobuf::uint32 repeated_uint32(int index) const;
  void set_repeated_uint32(int index, ::google::protobuf::uint32 value);
  void add_repeated_uint32(::google::protobuf::uint32 value);
  const ::google::protobuf::RepeatedField< ::google::protobuf::uint32 >&
      repeated_uint32() const;
  ::google::protobuf::RepeatedField< ::google::protobuf::uint32 >*
      mutable_repeated_uint32();

  // repeated uint64 repeated_uint64 = 34;
  int repeated_uint64_size() const;
  void clear_repeated_uint64();
  static const int kRepeatedUint64FieldNumber = 34;
  ::google::protobuf::uint64 repeated_uint64(int index) const;
  void set_repeated_uint64(int index, ::google::protobuf::uint64 value);
  void add_repeated_uint64(::google::protobuf::uint64 value);
  const ::google::protobuf::RepeatedField< ::google::protobuf::uint64 >&
      repeated_uint64() const;
  ::google::protobuf::RepeatedField< ::google::protobuf::uint64 >*
      mutable_repeated_uint64();

  // repeated sint32 repeated_sint32 = 35;
  int repeated_sint32_size() const;
  void clear_repeated_sint32();
  static const int kRepeatedSint32FieldNumber = 35;
  ::google::protobuf::int32 repeated_sint32(int index) const;
  void set_repeated_sint32(int index, ::google::protobuf::int32 value);
  void add_repeated_sint32(::google::protobuf::int32 value);
  const ::google::protobuf::RepeatedField< ::google::protobuf::int32 >&
      repeated_sint32() const;
  ::google::protobuf::RepeatedField< ::google::protobuf::int32 >*
      mutable_repeated_sint32();

  // repeated sint64 repeated_sint64 = 36;
  int repeated_sint64_size() const;
  void clear_repeated_sint64();
  static const int kRepeatedSint64FieldNumber = 36;
  ::google::protobuf::int64 repeated_sint64(int index) const;
  void set_repeated_sint64(int index, ::google::protobuf::int64 value);
  void add_repeated_sint64(::google::protobuf::int64 value);
  const ::google::protobuf::RepeatedField< ::google::protobuf::int64 >&
      repeated_sint64() const;
  ::google::protobuf::RepeatedField< ::google::protobuf::int64 >*
      mutable_repeated_sint64();

  // repeated fixed32 repeated_fixed32 = 37;
  int repeated_fixed32_size() const;
  void clear_repeated_fixed32();
  static const int kRepeatedFixed32FieldNumber = 37;
  ::google::protobuf::uint32 repeated_fixed32(int index) const;
  void set_repeated_fixed32(int index, ::google::protobuf::uint32 value);
  void add_repeated_fixed32(::google::protobuf::uint32 value);
  const ::google::protobuf::RepeatedField< ::google::protobuf::uint32 >&
      repeated_fixed32() const;
  ::google::protobuf::RepeatedField< ::google::protobuf::uint32 >*
      mutable_repeated_fixed32();

  // repeated fixed64 repeated_fixed64 = 38;
  int repeated_fixed64_size() const;
  void clear_repeated_fixed64();
  static const int kRepeatedFixed64FieldNumber = 38;
  ::google::protobuf::uint64 repeated_fixed64(int index) const;
  void set_repeated_fixed64(int index, ::google::protobuf::uint64 value);
  void add_repeated_fixed64(::google::protobuf::uint64 value);
  const ::google::protobuf::RepeatedField< ::google::protobuf::uint64 >&
      repeated_fixed64() const;
  ::google::protobuf::RepeatedField< ::google::protobuf::uint64 >*
      mutable_repeated_fixed64();

  // repeated sfixed32 repeated_sfixed32 = 39;
  int repeated_sfixed32_size() const;
  void clear_repeated_sfixed32();
  static const int kRepeatedSfixed32FieldNumber = 39;
  ::google::protobuf::int32 repeated_sfixed32(int index) const;
  void set_repeated_sfixed32(int index, ::google::protobuf::int32 value);
  void add_repeated_sfixed32(::google::protobuf::int32 value);
  const ::google::protobuf::RepeatedField< ::google::protobuf::int32 >&
      repeated_sfixed32() const;
  ::google::protobuf::RepeatedField< ::google::protobuf::int32 >*
      mutable_repeated_sfixed32();

  // repeated sfixed64 repeated_sfixed64 = 40;
  int repeated_sfixed64_size() const;
  void clear_repeated_sfixed64();
  static const int kRepeatedSfixed64FieldNumber = 40;
  ::google::protobuf::int64 repeated_sfixed64(int index) const;
  void set_repeated_sfixed64(int index, ::google::protobuf::int64 value);
  void add_repeated_sfixed64(::google::protobuf::int64 value);
  const ::google::protobuf::RepeatedField< ::google::protobuf::int64 >&
      repeated_sfixed64() const;
  ::google::protobuf::RepeatedField< ::google::protobuf::int64 >*
      mutable_repeated_sfixed64();

  // repeated float repeated_float = 41;
  int repeated_float_size() const;
  void clear_repeated_float();
  static const int kRepeatedFloatFieldNumber = 41;
  float repeated_float(int index) const;
  void set_repeated_float(int index, float value);
  void add_repeated_float(float value);
  const ::google::protobuf::RepeatedField< float >&
      repeated_float() const;
  ::google::protobuf::RepeatedField< float >*
      mutable_repeated_float();

  // repeated double repeated_double = 42;
  int repeated_double_size() const;
  void clear_repeated_double();
  static const int kRepeatedDoubleFieldNumber = 42;
  double repeated_double(int index) const;
  void set_repeated_double(int index, double value);
  void add_repeated_double(double value);
  const ::google::protobuf::RepeatedField< double >&
      repeated_double() const;
  ::google::protobuf::RepeatedField< double >*
      mutable_repeated_double();

  // repeated bool repeated_bool = 43;
  int repeated_bool_size() const;
  void clear_repeated_bool();
  static const int kRepeatedBoolFieldNumber = 43;
  bool repeated_bool(int index) const;
  void set_repeated_bool(int index, bool value);
  void add_repeated_bool(bool value);
  const ::google::protobuf::RepeatedField< bool >&
      repeated_bool() const;
  ::google::protobuf::RepeatedField< bool >*
      mutable_repeated_bool();

  // repeated string repeated_string = 44;
  int repeated_string_size() const;
  void clear_repeated_string();
  static const int kRepeatedStringFieldNumber = 44;
  const ::std::string& repeated_string(int index) const;
  ::std::string* mutable_repeated_string(int index);
  void set_repeated_string(int index, const ::std::string& value);
  void set_repeated_string(int index, const char* value);
  void set_repeated_string(int index, const char* value, size_t size);
  ::std::string* add_repeated_string();
  void add_repeated_string(const ::std::string& value);
  void add_repeated_string(const char* value);
  void add_repeated_string(const char* value, size_t size);
  const ::google::protobuf::RepeatedPtrField< ::std::string>& repeated_string() const;
  ::google::protobuf::RepeatedPtrField< ::std::string>* mutable_repeated_string();

  // repeated bytes repeated_bytes = 45;
  int repeated_bytes_size() const;
  void clear_repeated_bytes();
  static const int kRepeatedBytesFieldNumber = 45;
  const ::std::string& repeated_bytes(int index) const;
  ::std::string* mutable_repeated_bytes(int index);
  void set_repeated_bytes(int index, const ::std::string& value);
  void set_repeated_bytes(int index, const char* value);
  void set_repeated_bytes(int index, const void* value, size_t size);
  ::std::string* add_repeated_bytes();
  void add_repeated_bytes(const ::std::string& value);
  void add_repeated_bytes(const char* value);
  void add_repeated_bytes(const void* value, size_t size);
  const ::google::protobuf::RepeatedPtrField< ::std::string>& repeated_bytes() const;
  ::google::protobuf::RepeatedPtrField< ::std::string>* mutable_repeated_bytes();

  // repeated group RepeatedGroup = 46 { ... };
  int repeatedgroup_size() const;
  void clear_repeatedgroup();
  static const int kRepeatedgroupFieldNumber = 46;
  const ::protobuf_unittest::TestAllTypesLite_RepeatedGroup& repeatedgroup(int index) const;
  ::protobuf_unittest::TestAllTypesLite_RepeatedGroup* mutable_repeatedgroup(int index);
  ::protobuf_unittest::TestAllTypesLite_RepeatedGroup* add_repeatedgroup();
  ::google::protobuf::RepeatedPtrField< ::protobuf_unittest::TestAllTypesLite_RepeatedGroup >*
      mutable_repeatedgroup();
  const ::google::protobuf::RepeatedPtrField< ::protobuf_unittest::TestAllTypesLite_RepeatedGroup >&
      repeatedgroup() const;

  // repeated .protobuf_unittest.TestAllTypesLite.NestedMessage repeated_nested_message = 48;
  int repeated_nested_message_size() const;
  void clear_repeated_nested_message();
  static const int kRepeatedNestedMessageFieldNumber = 48;
  const ::protobuf_unittest::TestAllTypesLite_NestedMessage& repeated_nested_message(int index) const;
  ::protobuf_unittest::TestAllTypesLite_NestedMessage* mutable_repeated_nested_message(int index);
  ::protobuf_unittest::TestAllTypesLite_NestedMessage* add_repeated_nested_message();
  ::google::protobuf::RepeatedPtrField< ::protobuf_unittest::TestAllTypesLite_NestedMessage >*
      mutable_repeated_nested_message();
  const ::google::protobuf::RepeatedPtrField< ::protobuf_unittest::TestAllTypesLite_NestedMessage >&
      repeated_nested_message() const;

  // repeated .protobuf_unittest.ForeignMessageLite repeated_foreign_message = 49;
  int repeated_foreign_message_size() const;
  void clear_repeated_foreign_message();
  static const int kRepeatedForeignMessageFieldNumber = 49;
  const ::protobuf_unittest::ForeignMessageLite& repeated_foreign_message(int index) const;
  ::protobuf_unittest::ForeignMessageLite* mutable_repeated_foreign_message(int index);
  ::protobuf_unittest::ForeignMessageLite* add_repeated_foreign_message();
  ::google::protobuf::RepeatedPtrField< ::protobuf_unittest::ForeignMessageLite >*
      mutable_repeated_foreign_message();
  const ::google::protobuf::RepeatedPtrField< ::protobuf_unittest::ForeignMessageLite >&
      repeated_foreign_message() const;

  // repeated .protobuf_unittest_import.ImportMessageLite repeated_import_message = 50;
  int repeated_import_message_size() const;
  void clear_repeated_import_message();
  static const int kRepeatedImportMessageFieldNumber = 50;
  const ::protobuf_unittest_import::ImportMessageLite& repeated_import_message(int index) const;
  ::protobuf_unittest_import::ImportMessageLite* mutable_repeated_import_message(int index);
  ::protobuf_unittest_import::ImportMessageLite* add_repeated_import_message();
  ::google::protobuf::RepeatedPtrField< ::protobuf_unittest_import::ImportMessageLite >*
      mutable_repeated_import_message();
  const ::google::protobuf::RepeatedPtrField< ::protobuf_unittest_import::ImportMessageLite >&
      repeated_import_message() const;

  // repeated .protobuf_unittest.TestAllTypesLite.NestedEnum repeated_nested_enum = 51;
  int repeated_nested_enum_size() const;
  void clear_repeated_nested_enum();
  static const int kRepeatedNestedEnumFieldNumber = 51;
  ::protobuf_unittest::TestAllTypesLite_NestedEnum repeated_nested_enum(int index) const;
  void set_repeated_nested_enum(int index, ::protobuf_unittest::TestAllTypesLite_NestedEnum value);
  void add_repeated_nested_enum(::protobuf_unittest::TestAllTypesLite_NestedEnum value);
  const ::google::protobuf::RepeatedField<int>& repeated_nested_enum() const;
  ::google::protobuf::RepeatedField<int>* mutable_repeated_nested_enum();

  // repeated .protobuf_unittest.ForeignEnumLite repeated_foreign_enum = 52;
  int repeated_foreign_enum_size() const;
  void clear_repeated_foreign_enum();
  static const int kRepeatedForeignEnumFieldNumber = 52;
  ::protobuf_unittest::ForeignEnumLite repeated_foreign_enum(int index) const;
  void set_repeated_foreign_enum(int index, ::protobuf_unittest::ForeignEnumLite value);
  void add_repeated_foreign_enum(::protobuf_unittest::ForeignEnumLite value);
  const ::google::protobuf::RepeatedField<int>& repeated_foreign_enum() const;
  ::google::protobuf::RepeatedField<int>* mutable_repeated_foreign_enum();

  // repeated .protobuf_unittest_import.ImportEnumLite repeated_import_enum = 53;
  int repeated_import_enum_size() const;
  void clear_repeated_import_enum();
  static const int kRepeatedImportEnumFieldNumber = 53;
  ::protobuf_unittest_import::ImportEnumLite repeated_import_enum(int index) const;
  void set_repeated_import_enum(int index, ::protobuf_unittest_import::ImportEnumLite value);
  void add_repeated_import_enum(::protobuf_unittest_import::ImportEnumLite value);
  const ::google::protobuf::RepeatedField<int>& repeated_import_enum() const;
  ::google::protobuf::RepeatedField<int>* mutable_repeated_import_enum();

  // repeated string repeated_string_piece = 54 [ctype = STRING_PIECE];
  int repeated_string_piece_size() const;
  void clear_repeated_string_piece();
  static const int kRepeatedStringPieceFieldNumber = 54;
 private:
  // Hidden due to unknown ctype option.
  const ::std::string& repeated_string_piece(int index) const;
  ::std::string* mutable_repeated_string_piece(int index);
  void set_repeated_string_piece(int index, const ::std::string& value);
  void set_repeated_string_piece(int index, const char* value);
  void set_repeated_string_piece(int index, const char* value, size_t size);
  ::std::string* add_repeated_string_piece();
  void add_repeated_string_piece(const ::std::string& value);
  void add_repeated_string_piece(const char* value);
  void add_repeated_string_piece(const char* value, size_t size);
  const ::google::protobuf::RepeatedPtrField< ::std::string>& repeated_string_piece() const;
  ::google::protobuf::RepeatedPtrField< ::std::string>* mutable_repeated_string_piece();
 public:

  // repeated string repeated_cord = 55 [ctype = CORD];
  int repeated_cord_size() const;
  void clear_repeated_cord();
  static const int kRepeatedCordFieldNumber = 55;
 private:
  // Hidden due to unknown ctype option.
  const ::std::string& repeated_cord(int index) const;
  ::std::string* mutable_repeated_cord(int index);
  void set_repeated_cord(int index, const ::std::string& value);
  void set_repeated_cord(int index, const char* value);
  void set_repeated_cord(int index, const char* value, size_t size);
  ::std::string* add_repeated_cord();
  void add_repeated_cord(const ::std::string& value);
  void add_repeated_cord(const char* value);
  void add_repeated_cord(const char* value, size_t size);
  const ::google::protobuf::RepeatedPtrField< ::std::string>& repeated_cord() const;
  ::google::protobuf::RepeatedPtrField< ::std::string>* mutable_repeated_cord();
 public:

  // repeated .protobuf_unittest.TestAllTypesLite.NestedMessage repeated_lazy_message = 57 [lazy = true];
  int repeated_lazy_message_size() const;
  void clear_repeated_lazy_message();
  static const int kRepeatedLazyMessageFieldNumber = 57;
  const ::protobuf_unittest::TestAllTypesLite_NestedMessage& repeated_lazy_message(int index) const;
  ::protobuf_unittest::TestAllTypesLite_NestedMessage* mutable_repeated_lazy_message(int index);
  ::protobuf_unittest::TestAllTypesLite_NestedMessage* add_repeated_lazy_message();
  ::google::protobuf::RepeatedPtrField< ::protobuf_unittest::TestAllTypesLite_NestedMessage >*
      mutable_repeated_lazy_message();
  const ::google::protobuf::RepeatedPtrField< ::protobuf_unittest::TestAllTypesLite_NestedMessage >&
      repeated_lazy_message() const;

  // optional int32 default_int32 = 61 [default = 41];
  bool has_default_int32() const;
  void clear_default_int32();
  static const int kDefaultInt32FieldNumber = 61;
  ::google::protobuf::int32 default_int32() const;
  void set_default_int32(::google::protobuf::int32 value);

  // optional int64 default_int64 = 62 [default = 42];
  bool has_default_int64() const;
  void clear_default_int64();
  static const int kDefaultInt64FieldNumber = 62;
  ::google::protobuf::int64 default_int64() const;
  void set_default_int64(::google::protobuf::int64 value);

  // optional uint32 default_uint32 = 63 [default = 43];
  bool has_default_uint32() const;
  void clear_default_uint32();
  static const int kDefaultUint32FieldNumber = 63;
  ::google::protobuf::uint32 default_uint32() const;
  void set_default_uint32(::google::protobuf::uint32 value);

  // optional uint64 default_uint64 = 64 [default = 44];
  bool has_default_uint64() const;
  void clear_default_uint64();
  static const int kDefaultUint64FieldNumber = 64;
  ::google::protobuf::uint64 default_uint64() const;
  void set_default_uint64(::google::protobuf::uint64 value);

  // optional sint32 default_sint32 = 65 [default = -45];
  bool has_default_sint32() const;
  void clear_default_sint32();
  static const int kDefaultSint32FieldNumber = 65;
  ::google::protobuf::int32 default_sint32() const;
  void set_default_sint32(::google::protobuf::int32 value);

  // optional sint64 default_sint64 = 66 [default = 46];
  bool has_default_sint64() const;
  void clear_default_sint64();
  static const int kDefaultSint64FieldNumber = 66;
  ::google::protobuf::int64 default_sint64() const;
  void set_default_sint64(::google::protobuf::int64 value);

  // optional fixed32 default_fixed32 = 67 [default = 47];
  bool has_default_fixed32() const;
  void clear_default_fixed32();
  static const int kDefaultFixed32FieldNumber = 67;
  ::google::protobuf::uint32 default_fixed32() const;
  void set_default_fixed32(::google::protobuf::uint32 value);

  // optional fixed64 default_fixed64 = 68 [default = 48];
  bool has_default_fixed64() const;
  void clear_default_fixed64();
  static const int kDefaultFixed64FieldNumber = 68;
  ::google::protobuf::uint64 default_fixed64() const;
  void set_default_fixed64(::google::protobuf::uint64 value);

  // optional sfixed32 default_sfixed32 = 69 [default = 49];
  bool has_default_sfixed32() const;
  void clear_default_sfixed32();
  static const int kDefaultSfixed32FieldNumber = 69;
  ::google::protobuf::int32 default_sfixed32() const;
  void set_default_sfixed32(::google::protobuf::int32 value);

  // optional sfixed64 default_sfixed64 = 70 [default = -50];
  bool has_default_sfixed64() const;
  void clear_default_sfixed64();
  static const int kDefaultSfixed64FieldNumber = 70;
  ::google::protobuf::int64 default_sfixed64() const;
  void set_default_sfixed64(::google::protobuf::int64 value);

  // optional float default_float = 71 [default = 51.5];
  bool has_default_float() const;
  void clear_default_float();
  static const int kDefaultFloatFieldNumber = 71;
  float default_float() const;
  void set_default_float(float value);

  // optional double default_double = 72 [default = 52000];
  bool has_default_double() const;
  void clear_default_double();
  static const int kDefaultDoubleFieldNumber = 72;
  double default_double() const;
  void set_default_double(double value);

  // optional bool default_bool = 73 [default = true];
  bool has_default_bool() const;
  void clear_default_bool();
  static const int kDefaultBoolFieldNumber = 73;
  bool default_bool() const;
  void set_default_bool(bool value);

  // optional string default_string = 74 [default = "hello"];
  bool has_default_string() const;
  void clear_default_string();
  static const int kDefaultStringFieldNumber = 74;
  const ::std::string& default_string() const;
  void set_default_string(const ::std::string& value);
  void set_default_string(const char* value);
  void set_default_string(const char* value, size_t size);
  ::std::string* mutable_default_string();
  ::std::string* release_default_string();
  void set_allocated_default_string(::std::string* default_string);

  // optional bytes default_bytes = 75 [default = "world"];
  bool has_default_bytes() const;
  void clear_default_bytes();
  static const int kDefaultBytesFieldNumber = 75;
  const ::std::string& default_bytes() const;
  void set_default_bytes(const ::std::string& value);
  void set_default_bytes(const char* value);
  void set_default_bytes(const void* value, size_t size);
  ::std::string* mutable_default_bytes();
  ::std::string* release_default_bytes();
  void set_allocated_default_bytes(::std::string* default_bytes);

  // optional .protobuf_unittest.TestAllTypesLite.NestedEnum default_nested_enum = 81 [default = BAR];
  bool has_default_nested_enum() const;
  void clear_default_nested_enum();
  static const int kDefaultNestedEnumFieldNumber = 81;
  ::protobuf_unittest::TestAllTypesLite_NestedEnum default_nested_enum() const;
  void set_default_nested_enum(::protobuf_unittest::TestAllTypesLite_NestedEnum value);

  // optional .protobuf_unittest.ForeignEnumLite default_foreign_enum = 82 [default = FOREIGN_LITE_BAR];
  bool has_default_foreign_enum() const;
  void clear_default_foreign_enum();
  static const int kDefaultForeignEnumFieldNumber = 82;
  ::protobuf_unittest::ForeignEnumLite default_foreign_enum() const;
  void set_default_foreign_enum(::protobuf_unittest::ForeignEnumLite value);

  // optional .protobuf_unittest_import.ImportEnumLite default_import_enum = 83 [default = IMPORT_LITE_BAR];
  bool has_default_import_enum() const;
  void clear_default_import_enum();
  static const int kDefaultImportEnumFieldNumber = 83;
  ::protobuf_unittest_import::ImportEnumLite default_import_enum() const;
  void set_default_import_enum(::protobuf_unittest_import::ImportEnumLite value);

  // optional string default_string_piece = 84 [default = "abc", ctype = STRING_PIECE];
  bool has_default_string_piece() const;
  void clear_default_string_piece();
  static const int kDefaultStringPieceFieldNumber = 84;
 private:
  // Hidden due to unknown ctype option.
  const ::std::string& default_string_piece() const;
  void set_default_string_piece(const ::std::string& value);
  void set_default_string_piece(const char* value);
  void set_default_string_piece(const char* value, size_t size);
  ::std::string* mutable_default_string_piece();
  ::std::string* release_default_string_piece();
  void set_allocated_default_string_piece(::std::string* default_string_piece);
 public:

  // optional string default_cord = 85 [default = "123", ctype = CORD];
  bool has_default_cord() const;
  void clear_default_cord();
  static const int kDefaultCordFieldNumber = 85;
 private:
  // Hidden due to unknown ctype option.
  const ::std::string& default_cord() const;
  void set_default_cord(const ::std::string& value);
  void set_default_cord(const char* value);
  void set_default_cord(const char* value, size_t size);
  ::std::string* mutable_default_cord();
  ::std::string* release_default_cord();
  void set_allocated_default_cord(::std::string* default_cord);
 public:

  // optional uint32 oneof_uint32 = 111;
  bool has_oneof_uint32() const;
  void clear_oneof_uint32();
  static const int kOneofUint32FieldNumber = 111;
  ::google::protobuf::uint32 oneof_uint32() const;
  void set_oneof_uint32(::google::protobuf::uint32 value);

  // optional .protobuf_unittest.TestAllTypesLite.NestedMessage oneof_nested_message = 112;
  bool has_oneof_nested_message() const;
  void clear_oneof_nested_message();
  static const int kOneofNestedMessageFieldNumber = 112;
  const ::protobuf_unittest::TestAllTypesLite_NestedMessage& oneof_nested_message() const;
  ::protobuf_unittest::TestAllTypesLite_NestedMessage* mutable_oneof_nested_message();
  ::protobuf_unittest::TestAllTypesLite_NestedMessage* release_oneof_nested_message();
  void set_allocated_oneof_nested_message(::protobuf_unittest::TestAllTypesLite_NestedMessage* oneof_nested_message);

  // optional string oneof_string = 113;
  bool has_oneof_string() const;
  void clear_oneof_string();
  static const int kOneofStringFieldNumber = 113;
  const ::std::string& oneof_string() const;
  void set_oneof_string(const ::std::string& value);
  void set_oneof_string(const char* value);
  void set_oneof_string(const char* value, size_t size);
  ::std::string* mutable_oneof_string();
  ::std::string* release_oneof_string();
  void set_allocated_oneof_string(::std::string* oneof_string);

  // optional bytes oneof_bytes = 114;
  bool has_oneof_bytes() const;
  void clear_oneof_bytes();
  static const int kOneofBytesFieldNumber = 114;
  const ::std::string& oneof_bytes() const;
  void set_oneof_bytes(const ::std::string& value);
  void set_oneof_bytes(const char* value);
  void set_oneof_bytes(const void* value, size_t size);
  ::std::string* mutable_oneof_bytes();
  ::std::string* release_oneof_bytes();
  void set_allocated_oneof_bytes(::std::string* oneof_bytes);

  // optional .protobuf_unittest.TestAllTypesLite.NestedMessage oneof_lazy_nested_message = 115 [lazy = true];
  bool has_oneof_lazy_nested_message() const;
  void clear_oneof_lazy_nested_message();
  static const int kOneofLazyNestedMessageFieldNumber = 115;
  const ::protobuf_unittest::TestAllTypesLite_NestedMessage& oneof_lazy_nested_message() const;
  ::protobuf_unittest::TestAllTypesLite_NestedMessage* mutable_oneof_lazy_nested_message();
  ::protobuf_unittest::TestAllTypesLite_NestedMessage* release_oneof_lazy_nested_message();
  void set_allocated_oneof_lazy_nested_message(::protobuf_unittest::TestAllTypesLite_NestedMessage* oneof_lazy_nested_message);

  OneofFieldCase oneof_field_case() const;
  // @@protoc_insertion_point(class_scope:protobuf_unittest.TestAllTypesLite)
 private:
  inline void set_has_optional_int32();
  inline void clear_has_optional_int32();
  inline void set_has_optional_int64();
  inline void clear_has_optional_int64();
  inline void set_has_optional_uint32();
  inline void clear_has_optional_uint32();
  inline void set_has_optional_uint64();
  inline void clear_has_optional_uint64();
  inline void set_has_optional_sint32();
  inline void clear_has_optional_sint32();
  inline void set_has_optional_sint64();
  inline void clear_has_optional_sint64();
  inline void set_has_optional_fixed32();
  inline void clear_has_optional_fixed32();
  inline void set_has_optional_fixed64();
  inline void clear_has_optional_fixed64();
  inline void set_has_optional_sfixed32();
  inline void clear_has_optional_sfixed32();
  inline void set_has_optional_sfixed64();
  inline void clear_has_optional_sfixed64();
  inline void set_has_optional_float();
  inline void clear_has_optional_float();
  inline void set_has_optional_double();
  inline void clear_has_optional_double();
  inline void set_has_optional_bool();
  inline void clear_has_optional_bool();
  inline void set_has_optional_string();
  inline void clear_has_optional_string();
  inline void set_has_optional_bytes();
  inline void clear_has_optional_bytes();
  inline void set_has_optionalgroup();
  inline void clear_has_optionalgroup();
  inline void set_has_optional_nested_message();
  inline void clear_has_optional_nested_message();
  inline void set_has_optional_foreign_message();
  inline void clear_has_optional_foreign_message();
  inline void set_has_optional_import_message();
  inline void clear_has_optional_import_message();
  inline void set_has_optional_nested_enum();
  inline void clear_has_optional_nested_enum();
  inline void set_has_optional_foreign_enum();
  inline void clear_has_optional_foreign_enum();
  inline void set_has_optional_import_enum();
  inline void clear_has_optional_import_enum();
  inline void set_has_optional_string_piece();
  inline void clear_has_optional_string_piece();
  inline void set_has_optional_cord();
  inline void clear_has_optional_cord();
  inline void set_has_optional_public_import_message();
  inline void clear_has_optional_public_import_message();
  inline void set_has_optional_lazy_message();
  inline void clear_has_optional_lazy_message();
  inline void set_has_default_int32();
  inline void clear_has_default_int32();
  inline void set_has_default_int64();
  inline void clear_has_default_int64();
  inline void set_has_default_uint32();
  inline void clear_has_default_uint32();
  inline void set_has_default_uint64();
  inline void clear_has_default_uint64();
  inline void set_has_default_sint32();
  inline void clear_has_default_sint32();
  inline void set_has_default_sint64();
  inline void clear_has_default_sint64();
  inline void set_has_default_fixed32();
  inline void clear_has_default_fixed32();
  inline void set_has_default_fixed64();
  inline void clear_has_default_fixed64();
  inline void set_has_default_sfixed32();
  inline void clear_has_default_sfixed32();
  inline void set_has_default_sfixed64();
  inline void clear_has_default_sfixed64();
  inline void set_has_default_float();
  inline void clear_has_default_float();
  inline void set_has_default_double();
  inline void clear_has_default_double();
  inline void set_has_default_bool();
  inline void clear_has_default_bool();
  inline void set_has_default_string();
  inline void clear_has_default_string();
  inline void set_has_default_bytes();
  inline void clear_has_default_bytes();
  inline void set_has_default_nested_enum();
  inline void clear_has_default_nested_enum();
  inline void set_has_default_foreign_enum();
  inline void clear_has_default_foreign_enum();
  inline void set_has_default_import_enum();
  inline void clear_has_default_import_enum();
  inline void set_has_default_string_piece();
  inline void clear_has_default_string_piece();
  inline void set_has_default_cord();
  inline void clear_has_default_cord();
  inline void set_has_oneof_uint32();
  inline void set_has_oneof_nested_message();
  inline void set_has_oneof_string();
  inline void set_has_oneof_bytes();
  inline void set_has_oneof_lazy_nested_message();

  inline bool has_oneof_field() const;
  void clear_oneof_field();
  inline void clear_has_oneof_field();

  ::google::protobuf::internal::ArenaStringPtr _unknown_fields_;
  ::google::protobuf::Arena* _arena_ptr_;

  ::google::protobuf::internal::HasBits<3> _has_bits_;
  mutable int _cached_size_;
  ::google::protobuf::RepeatedField< ::google::protobuf::int32 > repeated_int32_;
  ::google::protobuf::RepeatedField< ::google::protobuf::int64 > repeated_int64_;
  ::google::protobuf::RepeatedField< ::google::protobuf::uint32 > repeated_uint32_;
  ::google::protobuf::RepeatedField< ::google::protobuf::uint64 > repeated_uint64_;
  ::google::protobuf::RepeatedField< ::google::protobuf::int32 > repeated_sint32_;
  ::google::protobuf::RepeatedField< ::google::protobuf::int64 > repeated_sint64_;
  ::google::protobuf::RepeatedField< ::google::protobuf::uint32 > repeated_fixed32_;
  ::google::protobuf::RepeatedField< ::google::protobuf::uint64 > repeated_fixed64_;
  ::google::protobuf::RepeatedField< ::google::protobuf::int32 > repeated_sfixed32_;
  ::google::protobuf::RepeatedField< ::google::protobuf::int64 > repeated_sfixed64_;
  ::google::protobuf::RepeatedField< float > repeated_float_;
  ::google::protobuf::RepeatedField< double > repeated_double_;
  ::google::protobuf::RepeatedField< bool > repeated_bool_;
  ::google::protobuf::RepeatedPtrField< ::std::string> repeated_string_;
  ::google::protobuf::RepeatedPtrField< ::std::string> repeated_bytes_;
  ::google::protobuf::RepeatedPtrField< ::protobuf_unittest::TestAllTypesLite_RepeatedGroup > repeatedgroup_;
  ::google::protobuf::RepeatedPtrField< ::protobuf_unittest::TestAllTypesLite_NestedMessage > repeated_nested_message_;
  ::google::protobuf::RepeatedPtrField< ::protobuf_unittest::ForeignMessageLite > repeated_foreign_message_;
  ::google::protobuf::RepeatedPtrField< ::protobuf_unittest_import::ImportMessageLite > repeated_import_message_;
  ::google::protobuf::RepeatedField<int> repeated_nested_enum_;
  ::google::protobuf::RepeatedField<int> repeated_foreign_enum_;
  ::google::protobuf::RepeatedField<int> repeated_import_enum_;
  ::google::protobuf::RepeatedPtrField< ::std::string> repeated_string_piece_;
  ::google::protobuf::RepeatedPtrField< ::std::string> repeated_cord_;
  ::google::protobuf::RepeatedPtrField< ::protobuf_unittest::TestAllTypesLite_NestedMessage > repeated_lazy_message_;
  ::google::protobuf::internal::ArenaStringPtr optional_string_;
  ::google::protobuf::internal::ArenaStringPtr optional_bytes_;
  ::google::protobuf::internal::ArenaStringPtr optional_string_piece_;
  ::google::protobuf::internal::ArenaStringPtr optional_cord_;
  static ::std::string* _default_default_string_;
  ::google::protobuf::internal::ArenaStringPtr default_string_;
  static ::std::string* _default_default_bytes_;
  ::google::protobuf::internal::ArenaStringPtr default_bytes_;
  static ::std::string* _default_default_string_piece_;
  ::google::protobuf::internal::ArenaStringPtr default_string_piece_;
  static ::std::string* _default_default_cord_;
  ::google::protobuf::internal::ArenaStringPtr default_cord_;
  ::protobuf_unittest::TestAllTypesLite_OptionalGroup* optionalgroup_;
  ::protobuf_unittest::TestAllTypesLite_NestedMessage* optional_nested_message_;
  ::protobuf_unittest::ForeignMessageLite* optional_foreign_message_;
  ::protobuf_unittest_import::ImportMessageLite* optional_import_message_;
  ::protobuf_unittest_import::PublicImportMessageLite* optional_public_import_message_;
  ::protobuf_unittest::TestAllTypesLite_NestedMessage* optional_lazy_message_;
  ::google::protobuf::int64 optional_int64_;
  ::google::protobuf::int32 optional_int32_;
  ::google::protobuf::uint32 optional_uint32_;
  ::google::protobuf::uint64 optional_uint64_;
  ::google::protobuf::int64 optional_sint64_;
  ::google::protobuf::int32 optional_sint32_;
  ::google::protobuf::uint32 optional_fixed32_;
  ::google::protobuf::uint64 optional_fixed64_;
  ::google::protobuf::int64 optional_sfixed64_;
  ::google::protobuf::int32 optional_sfixed32_;
  float optional_float_;
  double optional_double_;
  bool optional_bool_;
  int default_import_enum_;
  int optional_nested_enum_;
  int optional_foreign_enum_;
  int optional_import_enum_;
  ::google::protobuf::int32 default_int32_;
  ::google::protobuf::int64 default_int64_;
  ::google::protobuf::uint64 default_uint64_;
  ::google::protobuf::uint32 default_uint32_;
  ::google::protobuf::int32 default_sint32_;
  ::google::protobuf::int64 default_sint64_;
  ::google::protobuf::uint64 default_fixed64_;
  ::google::protobuf::uint32 default_fixed32_;
  ::google::protobuf::int32 default_sfixed32_;
  ::google::protobuf::int64 default_sfixed64_;
  double default_double_;
  float default_float_;
  bool default_bool_;
  int default_nested_enum_;
  int default_foreign_enum_;
  union OneofFieldUnion {
    OneofFieldUnion() {}
    ::google::protobuf::uint32 oneof_uint32_;
    ::protobuf_unittest::TestAllTypesLite_NestedMessage* oneof_nested_message_;
    ::google::protobuf::internal::ArenaStringPtr oneof_string_;
    ::google::protobuf::internal::ArenaStringPtr oneof_bytes_;
    ::protobuf_unittest::TestAllTypesLite_NestedMessage* oneof_lazy_nested_message_;
  } oneof_field_;
  ::google::protobuf::uint32 _oneof_case_[1];

  friend void  protobuf_InitDefaults_google_2fprotobuf_2funittest_5flite_2eproto_impl();
  friend void  protobuf_AddDesc_google_2fprotobuf_2funittest_5flite_2eproto_impl();
  friend void protobuf_AssignDesc_google_2fprotobuf_2funittest_5flite_2eproto();
  friend void protobuf_ShutdownFile_google_2fprotobuf_2funittest_5flite_2eproto();

  void InitAsDefaultInstance();
};
extern ::google::protobuf::internal::ExplicitlyConstructed<TestAllTypesLite> TestAllTypesLite_default_instance_;

// -------------------------------------------------------------------

class ForeignMessageLite : public ::google::protobuf::MessageLite /* @@protoc_insertion_point(class_definition:protobuf_unittest.ForeignMessageLite) */ {
 public:
  ForeignMessageLite();
  virtual ~ForeignMessageLite();

  ForeignMessageLite(const ForeignMessageLite& from);

  inline ForeignMessageLite& operator=(const ForeignMessageLite& from) {
    CopyFrom(from);
    return *this;
  }

  inline const ::std::string& unknown_fields() const {
    return _unknown_fields_.GetNoArena(
        &::google::protobuf::internal::GetEmptyStringAlreadyInited());
  }

  inline ::std::string* mutable_unknown_fields() {
    return _unknown_fields_.MutableNoArena(
        &::google::protobuf::internal::GetEmptyStringAlreadyInited());
  }

  static const ForeignMessageLite& default_instance();

  static const ForeignMessageLite* internal_default_instance();

  void Swap(ForeignMessageLite* other);

  // implements Message ----------------------------------------------

  inline ForeignMessageLite* New() const { return New(NULL); }

  ForeignMessageLite* New(::google::protobuf::Arena* arena) const;
  void CheckTypeAndMergeFrom(const ::google::protobuf::MessageLite& from);
  void CopyFrom(const ForeignMessageLite& from);
  void MergeFrom(const ForeignMessageLite& from);
  void Clear();
  bool IsInitialized() const;

  size_t ByteSizeLong() const;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input);
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const;
  void DiscardUnknownFields();
  int GetCachedSize() const { return _cached_size_; }
  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const;
  void InternalSwap(ForeignMessageLite* other);
  void UnsafeMergeFrom(const ForeignMessageLite& from);
  private:
  inline ::google::protobuf::Arena* GetArenaNoVirtual() const {
    return _arena_ptr_;
  }
  inline ::google::protobuf::Arena* MaybeArenaPtr() const {
    return _arena_ptr_;
  }
  public:

  ::std::string GetTypeName() const;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // optional int32 c = 1;
  bool has_c() const;
  void clear_c();
  static const int kCFieldNumber = 1;
  ::google::protobuf::int32 c() const;
  void set_c(::google::protobuf::int32 value);

  // @@protoc_insertion_point(class_scope:protobuf_unittest.ForeignMessageLite)
 private:
  inline void set_has_c();
  inline void clear_has_c();

  ::google::protobuf::internal::ArenaStringPtr _unknown_fields_;
  ::google::protobuf::Arena* _arena_ptr_;

  ::google::protobuf::internal::HasBits<1> _has_bits_;
  mutable int _cached_size_;
  ::google::protobuf::int32 c_;
  friend void  protobuf_InitDefaults_google_2fprotobuf_2funittest_5flite_2eproto_impl();
  friend void  protobuf_AddDesc_google_2fprotobuf_2funittest_5flite_2eproto_impl();
  friend void protobuf_AssignDesc_google_2fprotobuf_2funittest_5flite_2eproto();
  friend void protobuf_ShutdownFile_google_2fprotobuf_2funittest_5flite_2eproto();

  void InitAsDefaultInstance();
};
extern ::google::protobuf::internal::ExplicitlyConstructed<ForeignMessageLite> ForeignMessageLite_default_instance_;

// -------------------------------------------------------------------

class TestPackedTypesLite : public ::google::protobuf::MessageLite /* @@protoc_insertion_point(class_definition:protobuf_unittest.TestPackedTypesLite) */ {
 public:
  TestPackedTypesLite();
  virtual ~TestPackedTypesLite();

  TestPackedTypesLite(const TestPackedTypesLite& from);

  inline TestPackedTypesLite& operator=(const TestPackedTypesLite& from) {
    CopyFrom(from);
    return *this;
  }

  inline const ::std::string& unknown_fields() const {
    return _unknown_fields_.GetNoArena(
        &::google::protobuf::internal::GetEmptyStringAlreadyInited());
  }

  inline ::std::string* mutable_unknown_fields() {
    return _unknown_fields_.MutableNoArena(
        &::google::protobuf::internal::GetEmptyStringAlreadyInited());
  }

  static const TestPackedTypesLite& default_instance();

  static const TestPackedTypesLite* internal_default_instance();

  void Swap(TestPackedTypesLite* other);

  // implements Message ----------------------------------------------

  inline TestPackedTypesLite* New() const { return New(NULL); }

  TestPackedTypesLite* New(::google::protobuf::Arena* arena) const;
  void CheckTypeAndMergeFrom(const ::google::protobuf::MessageLite& from);
  void CopyFrom(const TestPackedTypesLite& from);
  void MergeFrom(const TestPackedTypesLite& from);
  void Clear();
  bool IsInitialized() const;

  size_t ByteSizeLong() const;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input);
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const;
  void DiscardUnknownFields();
  int GetCachedSize() const { return _cached_size_; }
  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const;
  void InternalSwap(TestPackedTypesLite* other);
  void UnsafeMergeFrom(const TestPackedTypesLite& from);
  private:
  inline ::google::protobuf::Arena* GetArenaNoVirtual() const {
    return _arena_ptr_;
  }
  inline ::google::protobuf::Arena* MaybeArenaPtr() const {
    return _arena_ptr_;
  }
  public:

  ::std::string GetTypeName() const;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // repeated int32 packed_int32 = 90 [packed = true];
  int packed_int32_size() const;
  void clear_packed_int32();
  static const int kPackedInt32FieldNumber = 90;
  ::google::protobuf::int32 packed_int32(int index) const;
  void set_packed_int32(int index, ::google::protobuf::int32 value);
  void add_packed_int32(::google::protobuf::int32 value);
  const ::google::protobuf::RepeatedField< ::google::protobuf::int32 >&
      packed_int32() const;
  ::google::protobuf::RepeatedField< ::google::protobuf::int32 >*
      mutable_packed_int32();

  // repeated int64 packed_int64 = 91 [packed = true];
  int packed_int64_size() const;
  void clear_packed_int64();
  static const int kPackedInt64FieldNumber = 91;
  ::google::protobuf::int64 packed_int64(int index) const;
  void set_packed_int64(int index, ::google::protobuf::int64 value);
  void add_packed_int64(::google::protobuf::int64 value);
  const ::google::protobuf::RepeatedField< ::google::protobuf::int64 >&
      packed_int64() const;
  ::google::protobuf::RepeatedField< ::google::protobuf::int64 >*
      mutable_packed_int64();

  // repeated uint32 packed_uint32 = 92 [packed = true];
  int packed_uint32_size() const;
  void clear_packed_uint32();
  static const int kPackedUint32FieldNumber = 92;
  ::google::protobuf::uint32 packed_uint32(int index) const;
  void set_packed_uint32(int index, ::google::protobuf::uint32 value);
  void add_packed_uint32(::google::protobuf::uint32 value);
  const ::google::protobuf::RepeatedField< ::google::protobuf::uint32 >&
      packed_uint32() const;
  ::google::protobuf::RepeatedField< ::google::protobuf::uint32 >*
      mutable_packed_uint32();

  // repeated uint64 packed_uint64 = 93 [packed = true];
  int packed_uint64_size() const;
  void clear_packed_uint64();
  static const int kPackedUint64FieldNumber = 93;
  ::google::protobuf::uint64 packed_uint64(int index) const;
  void set_packed_uint64(int index, ::google::protobuf::uint64 value);
  void add_packed_uint64(::google::protobuf::uint64 value);
  const ::google::protobuf::RepeatedField< ::google::protobuf::uint64 >&
      packed_uint64() const;
  ::google::protobuf::RepeatedField< ::google::protobuf::uint64 >*
      mutable_packed_uint64();

  // repeated sint32 packed_sint32 = 94 [packed = true];
  int packed_sint32_size() const;
  void clear_packed_sint32();
  static const int kPackedSint32FieldNumber = 94;
  ::google::protobuf::int32 packed_sint32(int index) const;
  void set_packed_sint32(int index, ::google::protobuf::int32 value);
  void add_packed_sint32(::google::protobuf::int32 value);
  const ::google::protobuf::RepeatedField< ::google::protobuf::int32 >&
      packed_sint32() const;
  ::google::protobuf::RepeatedField< ::google::protobuf::int32 >*
      mutable_packed_sint32();

  // repeated sint64 packed_sint64 = 95 [packed = true];
  int packed_sint64_size() const;
  void clear_packed_sint64();
  static const int kPackedSint64FieldNumber = 95;
  ::google::protobuf::int64 packed_sint64(int index) const;
  void set_packed_sint64(int index, ::google::protobuf::int64 value);
  void add_packed_sint64(::google::protobuf::int64 value);
  const ::google::protobuf::RepeatedField< ::google::protobuf::int64 >&
      packed_sint64() const;
  ::google::protobuf::RepeatedField< ::google::protobuf::int64 >*
      mutable_packed_sint64();

  // repeated fixed32 packed_fixed32 = 96 [packed = true];
  int packed_fixed32_size() const;
  void clear_packed_fixed32();
  static const int kPackedFixed32FieldNumber = 96;
  ::google::protobuf::uint32 packed_fixed32(int index) const;
  void set_packed_fixed32(int index, ::google::protobuf::uint32 value);
  void add_packed_fixed32(::google::protobuf::uint32 value);
  const ::google::protobuf::RepeatedField< ::google::protobuf::uint32 >&
      packed_fixed32() const;
  ::google::protobuf::RepeatedField< ::google::protobuf::uint32 >*
      mutable_packed_fixed32();

  // repeated fixed64 packed_fixed64 = 97 [packed = true];
  int packed_fixed64_size() const;
  void clear_packed_fixed64();
  static const int kPackedFixed64FieldNumber = 97;
  ::google::protobuf::uint64 packed_fixed64(int index) const;
  void set_packed_fixed64(int index, ::google::protobuf::uint64 value);
  void add_packed_fixed64(::google::protobuf::uint64 value);
  const ::google::protobuf::RepeatedField< ::google::protobuf::uint64 >&
      packed_fixed64() const;
  ::google::protobuf::RepeatedField< ::google::protobuf::uint64 >*
      mutable_packed_fixed64();

  // repeated sfixed32 packed_sfixed32 = 98 [packed = true];
  int packed_sfixed32_size() const;
  void clear_packed_sfixed32();
  static const int kPackedSfixed32FieldNumber = 98;
  ::google::protobuf::int32 packed_sfixed32(int index) const;
  void set_packed_sfixed32(int index, ::google::protobuf::int32 value);
  void add_packed_sfixed32(::google::protobuf::int32 value);
  const ::google::protobuf::RepeatedField< ::google::protobuf::int32 >&
      packed_sfixed32() const;
  ::google::protobuf::RepeatedField< ::google::protobuf::int32 >*
      mutable_packed_sfixed32();

  // repeated sfixed64 packed_sfixed64 = 99 [packed = true];
  int packed_sfixed64_size() const;
  void clear_packed_sfixed64();
  static const int kPackedSfixed64FieldNumber = 99;
  ::google::protobuf::int64 packed_sfixed64(int index) const;
  void set_packed_sfixed64(int index, ::google::protobuf::int64 value);
  void add_packed_sfixed64(::google::protobuf::int64 value);
  const ::google::protobuf::RepeatedField< ::google::protobuf::int64 >&
      packed_sfixed64() const;
  ::google::protobuf::RepeatedField< ::google::protobuf::int64 >*
      mutable_packed_sfixed64();

  // repeated float packed_float = 100 [packed = true];
  int packed_float_size() const;
  void clear_packed_float();
  static const int kPackedFloatFieldNumber = 100;
  float packed_float(int index) const;
  void set_packed_float(int index, float value);
  void add_packed_float(float value);
  const ::google::protobuf::RepeatedField< float >&
      packed_float() const;
  ::google::protobuf::RepeatedField< float >*
      mutable_packed_float();

  // repeated double packed_double = 101 [packed = true];
  int packed_double_size() const;
  void clear_packed_double();
  static const int kPackedDoubleFieldNumber = 101;
  double packed_double(int index) const;
  void set_packed_double(int index, double value);
  void add_packed_double(double value);
  const ::google::protobuf::RepeatedField< double >&
      packed_double() const;
  ::google::protobuf::RepeatedField< double >*
      mutable_packed_double();

  // repeated bool packed_bool = 102 [packed = true];
  int packed_bool_size() const;
  void clear_packed_bool();
  static const int kPackedBoolFieldNumber = 102;
  bool packed_bool(int index) const;
  void set_packed_bool(int index, bool value);
  void add_packed_bool(bool value);
  const ::google::protobuf::RepeatedField< bool >&
      packed_bool() const;
  ::google::protobuf::RepeatedField< bool >*
      mutable_packed_bool();

  // repeated .protobuf_unittest.ForeignEnumLite packed_enum = 103 [packed = true];
  int packed_enum_size() const;
  void clear_packed_enum();
  static const int kPackedEnumFieldNumber = 103;
  ::protobuf_unittest::ForeignEnumLite packed_enum(int index) const;
  void set_packed_enum(int index, ::protobuf_unittest::ForeignEnumLite value);
  void add_packed_enum(::protobuf_unittest::ForeignEnumLite value);
  const ::google::protobuf::RepeatedField<int>& packed_enum() const;
  ::google::protobuf::RepeatedField<int>* mutable_packed_enum();

  // @@protoc_insertion_point(class_scope:protobuf_unittest.TestPackedTypesLite)
 private:

  ::google::protobuf::internal::ArenaStringPtr _unknown_fields_;
  ::google::protobuf::Arena* _arena_ptr_;

  ::google::protobuf::internal::HasBits<1> _has_bits_;
  mutable int _cached_size_;
  ::google::protobuf::RepeatedField< ::google::protobuf::int32 > packed_int32_;
  mutable int _packed_int32_cached_byte_size_;
  ::google::protobuf::RepeatedField< ::google::protobuf::int64 > packed_int64_;
  mutable int _packed_int64_cached_byte_size_;
  ::google::protobuf::RepeatedField< ::google::protobuf::uint32 > packed_uint32_;
  mutable int _packed_uint32_cached_byte_size_;
  ::google::protobuf::RepeatedField< ::google::protobuf::uint64 > packed_uint64_;
  mutable int _packed_uint64_cached_byte_size_;
  ::google::protobuf::RepeatedField< ::google::protobuf::int32 > packed_sint32_;
  mutable int _packed_sint32_cached_byte_size_;
  ::google::protobuf::RepeatedField< ::google::protobuf::int64 > packed_sint64_;
  mutable int _packed_sint64_cached_byte_size_;
  ::google::protobuf::RepeatedField< ::google::protobuf::uint32 > packed_fixed32_;
  mutable int _packed_fixed32_cached_byte_size_;
  ::google::protobuf::RepeatedField< ::google::protobuf::uint64 > packed_fixed64_;
  mutable int _packed_fixed64_cached_byte_size_;
  ::google::protobuf::RepeatedField< ::google::protobuf::int32 > packed_sfixed32_;
  mutable int _packed_sfixed32_cached_byte_size_;
  ::google::protobuf::RepeatedField< ::google::protobuf::int64 > packed_sfixed64_;
  mutable int _packed_sfixed64_cached_byte_size_;
  ::google::protobuf::RepeatedField< float > packed_float_;
  mutable int _packed_float_cached_byte_size_;
  ::google::protobuf::RepeatedField< double > packed_double_;
  mutable int _packed_double_cached_byte_size_;
  ::google::protobuf::RepeatedField< bool > packed_bool_;
  mutable int _packed_bool_cached_byte_size_;
  ::google::protobuf::RepeatedField<int> packed_enum_;
  mutable int _packed_enum_cached_byte_size_;
  friend void  protobuf_InitDefaults_google_2fprotobuf_2funittest_5flite_2eproto_impl();
  friend void  protobuf_AddDesc_google_2fprotobuf_2funittest_5flite_2eproto_impl();
  friend void protobuf_AssignDesc_google_2fprotobuf_2funittest_5flite_2eproto();
  friend void protobuf_ShutdownFile_google_2fprotobuf_2funittest_5flite_2eproto();

  void InitAsDefaultInstance();
};
extern ::google::protobuf::internal::ExplicitlyConstructed<TestPackedTypesLite> TestPackedTypesLite_default_instance_;

// -------------------------------------------------------------------

class TestAllExtensionsLite : public ::google::protobuf::MessageLite /* @@protoc_insertion_point(class_definition:protobuf_unittest.TestAllExtensionsLite) */ {
 public:
  TestAllExtensionsLite();
  virtual ~TestAllExtensionsLite();

  TestAllExtensionsLite(const TestAllExtensionsLite& from);

  inline TestAllExtensionsLite& operator=(const TestAllExtensionsLite& from) {
    CopyFrom(from);
    return *this;
  }

  inline const ::std::string& unknown_fields() const {
    return _unknown_fields_.GetNoArena(
        &::google::protobuf::internal::GetEmptyStringAlreadyInited());
  }

  inline ::std::string* mutable_unknown_fields() {
    return _unknown_fields_.MutableNoArena(
        &::google::protobuf::internal::GetEmptyStringAlreadyInited());
  }

  static const TestAllExtensionsLite& default_instance();

  static const TestAllExtensionsLite* internal_default_instance();

  void Swap(TestAllExtensionsLite* other);

  // implements Message ----------------------------------------------

  inline TestAllExtensionsLite* New() const { return New(NULL); }

  TestAllExtensionsLite* New(::google::protobuf::Arena* arena) const;
  void CheckTypeAndMergeFrom(const ::google::protobuf::MessageLite& from);
  void CopyFrom(const TestAllExtensionsLite& from);
  void MergeFrom(const TestAllExtensionsLite& from);
  void Clear();
  bool IsInitialized() const;

  size_t ByteSizeLong() const;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input);
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const;
  void DiscardUnknownFields();
  int GetCachedSize() const { return _cached_size_; }
  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const;
  void InternalSwap(TestAllExtensionsLite* other);
  void UnsafeMergeFrom(const TestAllExtensionsLite& from);
  private:
  inline ::google::protobuf::Arena* GetArenaNoVirtual() const {
    return _arena_ptr_;
  }
  inline ::google::protobuf::Arena* MaybeArenaPtr() const {
    return _arena_ptr_;
  }
  public:

  ::std::string GetTypeName() const;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  GOOGLE_PROTOBUF_EXTENSION_ACCESSORS(TestAllExtensionsLite)
  // @@protoc_insertion_point(class_scope:protobuf_unittest.TestAllExtensionsLite)
 private:

  ::google::protobuf::internal::ExtensionSet _extensions_;

  ::google::protobuf::internal::ArenaStringPtr _unknown_fields_;
  ::google::protobuf::Arena* _arena_ptr_;

  ::google::protobuf::internal::HasBits<1> _has_bits_;
  mutable int _cached_size_;
  friend void  protobuf_InitDefaults_google_2fprotobuf_2funittest_5flite_2eproto_impl();
  friend void  protobuf_AddDesc_google_2fprotobuf_2funittest_5flite_2eproto_impl();
  friend void protobuf_AssignDesc_google_2fprotobuf_2funittest_5flite_2eproto();
  friend void protobuf_ShutdownFile_google_2fprotobuf_2funittest_5flite_2eproto();

  void InitAsDefaultInstance();
};
extern ::google::protobuf::internal::ExplicitlyConstructed<TestAllExtensionsLite> TestAllExtensionsLite_default_instance_;

// -------------------------------------------------------------------

class OptionalGroup_extension_lite : public ::google::protobuf::MessageLite /* @@protoc_insertion_point(class_definition:protobuf_unittest.OptionalGroup_extension_lite) */ {
 public:
  OptionalGroup_extension_lite();
  virtual ~OptionalGroup_extension_lite();

  OptionalGroup_extension_lite(const OptionalGroup_extension_lite& from);

  inline OptionalGroup_extension_lite& operator=(const OptionalGroup_extension_lite& from) {
    CopyFrom(from);
    return *this;
  }

  inline const ::std::string& unknown_fields() const {
    return _unknown_fields_.GetNoArena(
        &::google::protobuf::internal::GetEmptyStringAlreadyInited());
  }

  inline ::std::string* mutable_unknown_fields() {
    return _unknown_fields_.MutableNoArena(
        &::google::protobuf::internal::GetEmptyStringAlreadyInited());
  }

  static const OptionalGroup_extension_lite& default_instance();

  static const OptionalGroup_extension_lite* internal_default_instance();

  void Swap(OptionalGroup_extension_lite* other);

  // implements Message ----------------------------------------------

  inline OptionalGroup_extension_lite* New() const { return New(NULL); }

  OptionalGroup_extension_lite* New(::google::protobuf::Arena* arena) const;
  void CheckTypeAndMergeFrom(const ::google::protobuf::MessageLite& from);
  void CopyFrom(const OptionalGroup_extension_lite& from);
  void MergeFrom(const OptionalGroup_extension_lite& from);
  void Clear();
  bool IsInitialized() const;

  size_t ByteSizeLong() const;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input);
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const;
  void DiscardUnknownFields();
  int GetCachedSize() const { return _cached_size_; }
  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const;
  void InternalSwap(OptionalGroup_extension_lite* other);
  void UnsafeMergeFrom(const OptionalGroup_extension_lite& from);
  private:
  inline ::google::protobuf::Arena* GetArenaNoVirtual() const {
    return _arena_ptr_;
  }
  inline ::google::protobuf::Arena* MaybeArenaPtr() const {
    return _arena_ptr_;
  }
  public:

  ::std::string GetTypeName() const;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // optional int32 a = 17;
  bool has_a() const;
  void clear_a();
  static const int kAFieldNumber = 17;
  ::google::protobuf::int32 a() const;
  void set_a(::google::protobuf::int32 value);

  // @@protoc_insertion_point(class_scope:protobuf_unittest.OptionalGroup_extension_lite)
 private:
  inline void set_has_a();
  inline void clear_has_a();

  ::google::protobuf::internal::ArenaStringPtr _unknown_fields_;
  ::google::protobuf::Arena* _arena_ptr_;

  ::google::protobuf::internal::HasBits<1> _has_bits_;
  mutable int _cached_size_;
  ::google::protobuf::int32 a_;
  friend void  protobuf_InitDefaults_google_2fprotobuf_2funittest_5flite_2eproto_impl();
  friend void  protobuf_AddDesc_google_2fprotobuf_2funittest_5flite_2eproto_impl();
  friend void protobuf_AssignDesc_google_2fprotobuf_2funittest_5flite_2eproto();
  friend void protobuf_ShutdownFile_google_2fprotobuf_2funittest_5flite_2eproto();

  void InitAsDefaultInstance();
};
extern ::google::protobuf::internal::ExplicitlyConstructed<OptionalGroup_extension_lite> OptionalGroup_extension_lite_default_instance_;

// -------------------------------------------------------------------

class RepeatedGroup_extension_lite : public ::google::protobuf::MessageLite /* @@protoc_insertion_point(class_definition:protobuf_unittest.RepeatedGroup_extension_lite) */ {
 public:
  RepeatedGroup_extension_lite();
  virtual ~RepeatedGroup_extension_lite();

  RepeatedGroup_extension_lite(const RepeatedGroup_extension_lite& from);

  inline RepeatedGroup_extension_lite& operator=(const RepeatedGroup_extension_lite& from) {
    CopyFrom(from);
    return *this;
  }

  inline const ::std::string& unknown_fields() const {
    return _unknown_fields_.GetNoArena(
        &::google::protobuf::internal::GetEmptyStringAlreadyInited());
  }

  inline ::std::string* mutable_unknown_fields() {
    return _unknown_fields_.MutableNoArena(
        &::google::protobuf::internal::GetEmptyStringAlreadyInited());
  }

  static const RepeatedGroup_extension_lite& default_instance();

  static const RepeatedGroup_extension_lite* internal_default_instance();

  void Swap(RepeatedGroup_extension_lite* other);

  // implements Message ----------------------------------------------

  inline RepeatedGroup_extension_lite* New() const { return New(NULL); }

  RepeatedGroup_extension_lite* New(::google::protobuf::Arena* arena) const;
  void CheckTypeAndMergeFrom(const ::google::protobuf::MessageLite& from);
  void CopyFrom(const RepeatedGroup_extension_lite& from);
  void MergeFrom(const RepeatedGroup_extension_lite& from);
  void Clear();
  bool IsInitialized() const;

  size_t ByteSizeLong() const;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input);
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const;
  void DiscardUnknownFields();
  int GetCachedSize() const { return _cached_size_; }
  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const;
  void InternalSwap(RepeatedGroup_extension_lite* other);
  void UnsafeMergeFrom(const RepeatedGroup_extension_lite& from);
  private:
  inline ::google::protobuf::Arena* GetArenaNoVirtual() const {
    return _arena_ptr_;
  }
  inline ::google::protobuf::Arena* MaybeArenaPtr() const {
    return _arena_ptr_;
  }
  public:

  ::std::string GetTypeName() const;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // optional int32 a = 47;
  bool has_a() const;
  void clear_a();
  static const int kAFieldNumber = 47;
  ::google::protobuf::int32 a() const;
  void set_a(::google::protobuf::int32 value);

  // @@protoc_insertion_point(class_scope:protobuf_unittest.RepeatedGroup_extension_lite)
 private:
  inline void set_has_a();
  inline void clear_has_a();

  ::google::protobuf::internal::ArenaStringPtr _unknown_fields_;
  ::google::protobuf::Arena* _arena_ptr_;

  ::google::protobuf::internal::HasBits<1> _has_bits_;
  mutable int _cached_size_;
  ::google::protobuf::int32 a_;
  friend void  protobuf_InitDefaults_google_2fprotobuf_2funittest_5flite_2eproto_impl();
  friend void  protobuf_AddDesc_google_2fprotobuf_2funittest_5flite_2eproto_impl();
  friend void protobuf_AssignDesc_google_2fprotobuf_2funittest_5flite_2eproto();
  friend void protobuf_ShutdownFile_google_2fprotobuf_2funittest_5flite_2eproto();

  void InitAsDefaultInstance();
};
extern ::google::protobuf::internal::ExplicitlyConstructed<RepeatedGroup_extension_lite> RepeatedGroup_extension_lite_default_instance_;

// -------------------------------------------------------------------

class TestPackedExtensionsLite : public ::google::protobuf::MessageLite /* @@protoc_insertion_point(class_definition:protobuf_unittest.TestPackedExtensionsLite) */ {
 public:
  TestPackedExtensionsLite();
  virtual ~TestPackedExtensionsLite();

  TestPackedExtensionsLite(const TestPackedExtensionsLite& from);

  inline TestPackedExtensionsLite& operator=(const TestPackedExtensionsLite& from) {
    CopyFrom(from);
    return *this;
  }

  inline const ::std::string& unknown_fields() const {
    return _unknown_fields_.GetNoArena(
        &::google::protobuf::internal::GetEmptyStringAlreadyInited());
  }

  inline ::std::string* mutable_unknown_fields() {
    return _unknown_fields_.MutableNoArena(
        &::google::protobuf::internal::GetEmptyStringAlreadyInited());
  }

  static const TestPackedExtensionsLite& default_instance();

  static const TestPackedExtensionsLite* internal_default_instance();

  void Swap(TestPackedExtensionsLite* other);

  // implements Message ----------------------------------------------

  inline TestPackedExtensionsLite* New() const { return New(NULL); }

  TestPackedExtensionsLite* New(::google::protobuf::Arena* arena) const;
  void CheckTypeAndMergeFrom(const ::google::protobuf::MessageLite& from);
  void CopyFrom(const TestPackedExtensionsLite& from);
  void MergeFrom(const TestPackedExtensionsLite& from);
  void Clear();
  bool IsInitialized() const;

  size_t ByteSizeLong() const;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input);
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const;
  void DiscardUnknownFields();
  int GetCachedSize() const { return _cached_size_; }
  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const;
  void InternalSwap(TestPackedExtensionsLite* other);
  void UnsafeMergeFrom(const TestPackedExtensionsLite& from);
  private:
  inline ::google::protobuf::Arena* GetArenaNoVirtual() const {
    return _arena_ptr_;
  }
  inline ::google::protobuf::Arena* MaybeArenaPtr() const {
    return _arena_ptr_;
  }
  public:

  ::std::string GetTypeName() const;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  GOOGLE_PROTOBUF_EXTENSION_ACCESSORS(TestPackedExtensionsLite)
  // @@protoc_insertion_point(class_scope:protobuf_unittest.TestPackedExtensionsLite)
 private:

  ::google::protobuf::internal::ExtensionSet _extensions_;

  ::google::protobuf::internal::ArenaStringPtr _unknown_fields_;
  ::google::protobuf::Arena* _arena_ptr_;

  ::google::protobuf::internal::HasBits<1> _has_bits_;
  mutable int _cached_size_;
  friend void  protobuf_InitDefaults_google_2fprotobuf_2funittest_5flite_2eproto_impl();
  friend void  protobuf_AddDesc_google_2fprotobuf_2funittest_5flite_2eproto_impl();
  friend void protobuf_AssignDesc_google_2fprotobuf_2funittest_5flite_2eproto();
  friend void protobuf_ShutdownFile_google_2fprotobuf_2funittest_5flite_2eproto();

  void InitAsDefaultInstance();
};
extern ::google::protobuf::internal::ExplicitlyConstructed<TestPackedExtensionsLite> TestPackedExtensionsLite_default_instance_;

// -------------------------------------------------------------------

class TestNestedExtensionLite : public ::google::protobuf::MessageLite /* @@protoc_insertion_point(class_definition:protobuf_unittest.TestNestedExtensionLite) */ {
 public:
  TestNestedExtensionLite();
  virtual ~TestNestedExtensionLite();

  TestNestedExtensionLite(const TestNestedExtensionLite& from);

  inline TestNestedExtensionLite& operator=(const TestNestedExtensionLite& from) {
    CopyFrom(from);
    return *this;
  }

  inline const ::std::string& unknown_fields() const {
    return _unknown_fields_.GetNoArena(
        &::google::protobuf::internal::GetEmptyStringAlreadyInited());
  }

  inline ::std::string* mutable_unknown_fields() {
    return _unknown_fields_.MutableNoArena(
        &::google::protobuf::internal::GetEmptyStringAlreadyInited());
  }

  static const TestNestedExtensionLite& default_instance();

  static const TestNestedExtensionLite* internal_default_instance();

  void Swap(TestNestedExtensionLite* other);

  // implements Message ----------------------------------------------

  inline TestNestedExtensionLite* New() const { return New(NULL); }

  TestNestedExtensionLite* New(::google::protobuf::Arena* arena) const;
  void CheckTypeAndMergeFrom(const ::google::protobuf::MessageLite& from);
  void CopyFrom(const TestNestedExtensionLite& from);
  void MergeFrom(const TestNestedExtensionLite& from);
  void Clear();
  bool IsInitialized() const;

  size_t ByteSizeLong() const;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input);
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const;
  void DiscardUnknownFields();
  int GetCachedSize() const { return _cached_size_; }
  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const;
  void InternalSwap(TestNestedExtensionLite* other);
  void UnsafeMergeFrom(const TestNestedExtensionLite& from);
  private:
  inline ::google::protobuf::Arena* GetArenaNoVirtual() const {
    return _arena_ptr_;
  }
  inline ::google::protobuf::Arena* MaybeArenaPtr() const {
    return _arena_ptr_;
  }
  public:

  ::std::string GetTypeName() const;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  static const int kNestedExtensionFieldNumber = 12345;
  static ::google::protobuf::internal::ExtensionIdentifier< ::protobuf_unittest::TestAllExtensionsLite,
      ::google::protobuf::internal::PrimitiveTypeTraits< ::google::protobuf::int32 >, 5, false >
    nested_extension;
  // @@protoc_insertion_point(class_scope:protobuf_unittest.TestNestedExtensionLite)
 private:

  ::google::protobuf::internal::ArenaStringPtr _unknown_fields_;
  ::google::protobuf::Arena* _arena_ptr_;

  ::google::protobuf::internal::HasBits<1> _has_bits_;
  mutable int _cached_size_;
  friend void  protobuf_InitDefaults_google_2fprotobuf_2funittest_5flite_2eproto_impl();
  friend void  protobuf_AddDesc_google_2fprotobuf_2funittest_5flite_2eproto_impl();
  friend void protobuf_AssignDesc_google_2fprotobuf_2funittest_5flite_2eproto();
  friend void protobuf_ShutdownFile_google_2fprotobuf_2funittest_5flite_2eproto();

  void InitAsDefaultInstance();
};
extern ::google::protobuf::internal::ExplicitlyConstructed<TestNestedExtensionLite> TestNestedExtensionLite_default_instance_;

// -------------------------------------------------------------------

class TestDeprecatedLite : public ::google::protobuf::MessageLite /* @@protoc_insertion_point(class_definition:protobuf_unittest.TestDeprecatedLite) */ {
 public:
  TestDeprecatedLite();
  virtual ~TestDeprecatedLite();

  TestDeprecatedLite(const TestDeprecatedLite& from);

  inline TestDeprecatedLite& operator=(const TestDeprecatedLite& from) {
    CopyFrom(from);
    return *this;
  }

  inline const ::std::string& unknown_fields() const {
    return _unknown_fields_.GetNoArena(
        &::google::protobuf::internal::GetEmptyStringAlreadyInited());
  }

  inline ::std::string* mutable_unknown_fields() {
    return _unknown_fields_.MutableNoArena(
        &::google::protobuf::internal::GetEmptyStringAlreadyInited());
  }

  static const TestDeprecatedLite& default_instance();

  static const TestDeprecatedLite* internal_default_instance();

  void Swap(TestDeprecatedLite* other);

  // implements Message ----------------------------------------------

  inline TestDeprecatedLite* New() const { return New(NULL); }

  TestDeprecatedLite* New(::google::protobuf::Arena* arena) const;
  void CheckTypeAndMergeFrom(const ::google::protobuf::MessageLite& from);
  void CopyFrom(const TestDeprecatedLite& from);
  void MergeFrom(const TestDeprecatedLite& from);
  void Clear();
  bool IsInitialized() const;

  size_t ByteSizeLong() const;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input);
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const;
  void DiscardUnknownFields();
  int GetCachedSize() const { return _cached_size_; }
  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const;
  void InternalSwap(TestDeprecatedLite* other);
  void UnsafeMergeFrom(const TestDeprecatedLite& from);
  private:
  inline ::google::protobuf::Arena* GetArenaNoVirtual() const {
    return _arena_ptr_;
  }
  inline ::google::protobuf::Arena* MaybeArenaPtr() const {
    return _arena_ptr_;
  }
  public:

  ::std::string GetTypeName() const;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // optional int32 deprecated_field = 1 [deprecated = true];
  GOOGLE_PROTOBUF_DEPRECATED_ATTR bool has_deprecated_field() const;
  GOOGLE_PROTOBUF_DEPRECATED_ATTR void clear_deprecated_field();
  GOOGLE_PROTOBUF_DEPRECATED_ATTR static const int kDeprecatedFieldFieldNumber = 1;
  GOOGLE_PROTOBUF_DEPRECATED_ATTR ::google::protobuf::int32 deprecated_field() const;
  GOOGLE_PROTOBUF_DEPRECATED_ATTR void set_deprecated_field(::google::protobuf::int32 value);

  // @@protoc_insertion_point(class_scope:protobuf_unittest.TestDeprecatedLite)
 private:
  inline void set_has_deprecated_field();
  inline void clear_has_deprecated_field();

  ::google::protobuf::internal::ArenaStringPtr _unknown_fields_;
  ::google::protobuf::Arena* _arena_ptr_;

  ::google::protobuf::internal::HasBits<1> _has_bits_;
  mutable int _cached_size_;
  ::google::protobuf::int32 deprecated_field_;
  friend void  protobuf_InitDefaults_google_2fprotobuf_2funittest_5flite_2eproto_impl();
  friend void  protobuf_AddDesc_google_2fprotobuf_2funittest_5flite_2eproto_impl();
  friend void protobuf_AssignDesc_google_2fprotobuf_2funittest_5flite_2eproto();
  friend void protobuf_ShutdownFile_google_2fprotobuf_2funittest_5flite_2eproto();

  void InitAsDefaultInstance();
};
extern ::google::protobuf::internal::ExplicitlyConstructed<TestDeprecatedLite> TestDeprecatedLite_default_instance_;

// -------------------------------------------------------------------

class TestParsingMergeLite_RepeatedFieldsGenerator_Group1 : public ::google::protobuf::MessageLite /* @@protoc_insertion_point(class_definition:protobuf_unittest.TestParsingMergeLite.RepeatedFieldsGenerator.Group1) */ {
 public:
  TestParsingMergeLite_RepeatedFieldsGenerator_Group1();
  virtual ~TestParsingMergeLite_RepeatedFieldsGenerator_Group1();

  TestParsingMergeLite_RepeatedFieldsGenerator_Group1(const TestParsingMergeLite_RepeatedFieldsGenerator_Group1& from);

  inline TestParsingMergeLite_RepeatedFieldsGenerator_Group1& operator=(const TestParsingMergeLite_RepeatedFieldsGenerator_Group1& from) {
    CopyFrom(from);
    return *this;
  }

  inline const ::std::string& unknown_fields() const {
    return _unknown_fields_.GetNoArena(
        &::google::protobuf::internal::GetEmptyStringAlreadyInited());
  }

  inline ::std::string* mutable_unknown_fields() {
    return _unknown_fields_.MutableNoArena(
        &::google::protobuf::internal::GetEmptyStringAlreadyInited());
  }

  static const TestParsingMergeLite_RepeatedFieldsGenerator_Group1& default_instance();

  static const TestParsingMergeLite_RepeatedFieldsGenerator_Group1* internal_default_instance();

  void Swap(TestParsingMergeLite_RepeatedFieldsGenerator_Group1* other);

  // implements Message ----------------------------------------------

  inline TestParsingMergeLite_RepeatedFieldsGenerator_Group1* New() const { return New(NULL); }

  TestParsingMergeLite_RepeatedFieldsGenerator_Group1* New(::google::protobuf::Arena* arena) const;
  void CheckTypeAndMergeFrom(const ::google::protobuf::MessageLite& from);
  void CopyFrom(const TestParsingMergeLite_RepeatedFieldsGenerator_Group1& from);
  void MergeFrom(const TestParsingMergeLite_RepeatedFieldsGenerator_Group1& from);
  void Clear();
  bool IsInitialized() const;

  size_t ByteSizeLong() const;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input);
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const;
  void DiscardUnknownFields();
  int GetCachedSize() const { return _cached_size_; }
  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const;
  void InternalSwap(TestParsingMergeLite_RepeatedFieldsGenerator_Group1* other);
  void UnsafeMergeFrom(const TestParsingMergeLite_RepeatedFieldsGenerator_Group1& from);
  private:
  inline ::google::protobuf::Arena* GetArenaNoVirtual() const {
    return _arena_ptr_;
  }
  inline ::google::protobuf::Arena* MaybeArenaPtr() const {
    return _arena_ptr_;
  }
  public:

  ::std::string GetTypeName() const;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // optional .protobuf_unittest.TestAllTypesLite field1 = 11;
  bool has_field1() const;
  void clear_field1();
  static const int kField1FieldNumber = 11;
  const ::protobuf_unittest::TestAllTypesLite& field1() const;
  ::protobuf_unittest::TestAllTypesLite* mutable_field1();
  ::protobuf_unittest::TestAllTypesLite* release_field1();
  void set_allocated_field1(::protobuf_unittest::TestAllTypesLite* field1);

  // @@protoc_insertion_point(class_scope:protobuf_unittest.TestParsingMergeLite.RepeatedFieldsGenerator.Group1)
 private:
  inline void set_has_field1();
  inline void clear_has_field1();

  ::google::protobuf::internal::ArenaStringPtr _unknown_fields_;
  ::google::protobuf::Arena* _arena_ptr_;

  ::google::protobuf::internal::HasBits<1> _has_bits_;
  mutable int _cached_size_;
  ::protobuf_unittest::TestAllTypesLite* field1_;
  friend void  protobuf_InitDefaults_google_2fprotobuf_2funittest_5flite_2eproto_impl();
  friend void  protobuf_AddDesc_google_2fprotobuf_2funittest_5flite_2eproto_impl();
  friend void protobuf_AssignDesc_google_2fprotobuf_2funittest_5flite_2eproto();
  friend void protobuf_ShutdownFile_google_2fprotobuf_2funittest_5flite_2eproto();

  void InitAsDefaultInstance();
};
extern ::google::protobuf::internal::ExplicitlyConstructed<TestParsingMergeLite_RepeatedFieldsGenerator_Group1> TestParsingMergeLite_RepeatedFieldsGenerator_Group1_default_instance_;

// -------------------------------------------------------------------

class TestParsingMergeLite_RepeatedFieldsGenerator_Group2 : public ::google::protobuf::MessageLite /* @@protoc_insertion_point(class_definition:protobuf_unittest.TestParsingMergeLite.RepeatedFieldsGenerator.Group2) */ {
 public:
  TestParsingMergeLite_RepeatedFieldsGenerator_Group2();
  virtual ~TestParsingMergeLite_RepeatedFieldsGenerator_Group2();

  TestParsingMergeLite_RepeatedFieldsGenerator_Group2(const TestParsingMergeLite_RepeatedFieldsGenerator_Group2& from);

  inline TestParsingMergeLite_RepeatedFieldsGenerator_Group2& operator=(const TestParsingMergeLite_RepeatedFieldsGenerator_Group2& from) {
    CopyFrom(from);
    return *this;
  }

  inline const ::std::string& unknown_fields() const {
    return _unknown_fields_.GetNoArena(
        &::google::protobuf::internal::GetEmptyStringAlreadyInited());
  }

  inline ::std::string* mutable_unknown_fields() {
    return _unknown_fields_.MutableNoArena(
        &::google::protobuf::internal::GetEmptyStringAlreadyInited());
  }

  static const TestParsingMergeLite_RepeatedFieldsGenerator_Group2& default_instance();

  static const TestParsingMergeLite_RepeatedFieldsGenerator_Group2* internal_default_instance();

  void Swap(TestParsingMergeLite_RepeatedFieldsGenerator_Group2* other);

  // implements Message ----------------------------------------------

  inline TestParsingMergeLite_RepeatedFieldsGenerator_Group2* New() const { return New(NULL); }

  TestParsingMergeLite_RepeatedFieldsGenerator_Group2* New(::google::protobuf::Arena* arena) const;
  void CheckTypeAndMergeFrom(const ::google::protobuf::MessageLite& from);
  void CopyFrom(const TestParsingMergeLite_RepeatedFieldsGenerator_Group2& from);
  void MergeFrom(const TestParsingMergeLite_RepeatedFieldsGenerator_Group2& from);
  void Clear();
  bool IsInitialized() const;

  size_t ByteSizeLong() const;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input);
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const;
  void DiscardUnknownFields();
  int GetCachedSize() const { return _cached_size_; }
  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const;
  void InternalSwap(TestParsingMergeLite_RepeatedFieldsGenerator_Group2* other);
  void UnsafeMergeFrom(const TestParsingMergeLite_RepeatedFieldsGenerator_Group2& from);
  private:
  inline ::google::protobuf::Arena* GetArenaNoVirtual() const {
    return _arena_ptr_;
  }
  inline ::google::protobuf::Arena* MaybeArenaPtr() const {
    return _arena_ptr_;
  }
  public:

  ::std::string GetTypeName() const;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // optional .protobuf_unittest.TestAllTypesLite field1 = 21;
  bool has_field1() const;
  void clear_field1();
  static const int kField1FieldNumber = 21;
  const ::protobuf_unittest::TestAllTypesLite& field1() const;
  ::protobuf_unittest::TestAllTypesLite* mutable_field1();
  ::protobuf_unittest::TestAllTypesLite* release_field1();
  void set_allocated_field1(::protobuf_unittest::TestAllTypesLite* field1);

  // @@protoc_insertion_point(class_scope:protobuf_unittest.TestParsingMergeLite.RepeatedFieldsGenerator.Group2)
 private:
  inline void set_has_field1();
  inline void clear_has_field1();

  ::google::protobuf::internal::ArenaStringPtr _unknown_fields_;
  ::google::protobuf::Arena* _arena_ptr_;

  ::google::protobuf::internal::HasBits<1> _has_bits_;
  mutable int _cached_size_;
  ::protobuf_unittest::TestAllTypesLite* field1_;
  friend void  protobuf_InitDefaults_google_2fprotobuf_2funittest_5flite_2eproto_impl();
  friend void  protobuf_AddDesc_google_2fprotobuf_2funittest_5flite_2eproto_impl();
  friend void protobuf_AssignDesc_google_2fprotobuf_2funittest_5flite_2eproto();
  friend void protobuf_ShutdownFile_google_2fprotobuf_2funittest_5flite_2eproto();

  void InitAsDefaultInstance();
};
extern ::google::protobuf::internal::ExplicitlyConstructed<TestParsingMergeLite_RepeatedFieldsGenerator_Group2> TestParsingMergeLite_RepeatedFieldsGenerator_Group2_default_instance_;

// -------------------------------------------------------------------

class TestParsingMergeLite_RepeatedFieldsGenerator : public ::google::protobuf::MessageLite /* @@protoc_insertion_point(class_definition:protobuf_unittest.TestParsingMergeLite.RepeatedFieldsGenerator) */ {
 public:
  TestParsingMergeLite_RepeatedFieldsGenerator();
  virtual ~TestParsingMergeLite_RepeatedFieldsGenerator();

  TestParsingMergeLite_RepeatedFieldsGenerator(const TestParsingMergeLite_RepeatedFieldsGenerator& from);

  inline TestParsingMergeLite_RepeatedFieldsGenerator& operator=(const TestParsingMergeLite_RepeatedFieldsGenerator& from) {
    CopyFrom(from);
    return *this;
  }

  inline const ::std::string& unknown_fields() const {
    return _unknown_fields_.GetNoArena(
        &::google::protobuf::internal::GetEmptyStringAlreadyInited());
  }

  inline ::std::string* mutable_unknown_fields() {
    return _unknown_fields_.MutableNoArena(
        &::google::protobuf::internal::GetEmptyStringAlreadyInited());
  }

  static const TestParsingMergeLite_RepeatedFieldsGenerator& default_instance();

  static const TestParsingMergeLite_RepeatedFieldsGenerator* internal_default_instance();

  void Swap(TestParsingMergeLite_RepeatedFieldsGenerator* other);

  // implements Message ----------------------------------------------

  inline TestParsingMergeLite_RepeatedFieldsGenerator* New() const { return New(NULL); }

  TestParsingMergeLite_RepeatedFieldsGenerator* New(::google::protobuf::Arena* arena) const;
  void CheckTypeAndMergeFrom(const ::google::protobuf::MessageLite& from);
  void CopyFrom(const TestParsingMergeLite_RepeatedFieldsGenerator& from);
  void MergeFrom(const TestParsingMergeLite_RepeatedFieldsGenerator& from);
  void Clear();
  bool IsInitialized() const;

  size_t ByteSizeLong() const;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input);
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const;
  void DiscardUnknownFields();
  int GetCachedSize() const { return _cached_size_; }
  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const;
  void InternalSwap(TestParsingMergeLite_RepeatedFieldsGenerator* other);
  void UnsafeMergeFrom(const TestParsingMergeLite_RepeatedFieldsGenerator& from);
  private:
  inline ::google::protobuf::Arena* GetArenaNoVirtual() const {
    return _arena_ptr_;
  }
  inline ::google::protobuf::Arena* MaybeArenaPtr() const {
    return _arena_ptr_;
  }
  public:

  ::std::string GetTypeName() const;

  // nested types ----------------------------------------------------

  typedef TestParsingMergeLite_RepeatedFieldsGenerator_Group1 Group1;
  typedef TestParsingMergeLite_RepeatedFieldsGenerator_Group2 Group2;

  // accessors -------------------------------------------------------

  // repeated .protobuf_unittest.TestAllTypesLite field1 = 1;
  int field1_size() const;
  void clear_field1();
  static const int kField1FieldNumber = 1;
  const ::protobuf_unittest::TestAllTypesLite& field1(int index) const;
  ::protobuf_unittest::TestAllTypesLite* mutable_field1(int index);
  ::protobuf_unittest::TestAllTypesLite* add_field1();
  ::google::protobuf::RepeatedPtrField< ::protobuf_unittest::TestAllTypesLite >*
      mutable_field1();
  const ::google::protobuf::RepeatedPtrField< ::protobuf_unittest::TestAllTypesLite >&
      field1() const;

  // repeated .protobuf_unittest.TestAllTypesLite field2 = 2;
  int field2_size() const;
  void clear_field2();
  static const int kField2FieldNumber = 2;
  const ::protobuf_unittest::TestAllTypesLite& field2(int index) const;
  ::protobuf_unittest::TestAllTypesLite* mutable_field2(int index);
  ::protobuf_unittest::TestAllTypesLite* add_field2();
  ::google::protobuf::RepeatedPtrField< ::protobuf_unittest::TestAllTypesLite >*
      mutable_field2();
  const ::google::protobuf::RepeatedPtrField< ::protobuf_unittest::TestAllTypesLite >&
      field2() const;

  // repeated .protobuf_unittest.TestAllTypesLite field3 = 3;
  int field3_size() const;
  void clear_field3();
  static const int kField3FieldNumber = 3;
  const ::protobuf_unittest::TestAllTypesLite& field3(int index) const;
  ::protobuf_unittest::TestAllTypesLite* mutable_field3(int index);
  ::protobuf_unittest::TestAllTypesLite* add_field3();
  ::google::protobuf::RepeatedPtrField< ::protobuf_unittest::TestAllTypesLite >*
      mutable_field3();
  const ::google::protobuf::RepeatedPtrField< ::protobuf_unittest::TestAllTypesLite >&
      field3() const;

  // repeated group Group1 = 10 { ... };
  int group1_size() const;
  void clear_group1();
  static const int kGroup1FieldNumber = 10;
  const ::protobuf_unittest::TestParsingMergeLite_RepeatedFieldsGenerator_Group1& group1(int index) const;
  ::protobuf_unittest::TestParsingMergeLite_RepeatedFieldsGenerator_Group1* mutable_group1(int index);
  ::protobuf_unittest::TestParsingMergeLite_RepeatedFieldsGenerator_Group1* add_group1();
  ::google::protobuf::RepeatedPtrField< ::protobuf_unittest::TestParsingMergeLite_RepeatedFieldsGenerator_Group1 >*
      mutable_group1();
  const ::google::protobuf::RepeatedPtrField< ::protobuf_unittest::TestParsingMergeLite_RepeatedFieldsGenerator_Group1 >&
      group1() const;

  // repeated group Group2 = 20 { ... };
  int group2_size() const;
  void clear_group2();
  static const int kGroup2FieldNumber = 20;
  const ::protobuf_unittest::TestParsingMergeLite_RepeatedFieldsGenerator_Group2& group2(int index) const;
  ::protobuf_unittest::TestParsingMergeLite_RepeatedFieldsGenerator_Group2* mutable_group2(int index);
  ::protobuf_unittest::TestParsingMergeLite_RepeatedFieldsGenerator_Group2* add_group2();
  ::google::protobuf::RepeatedPtrField< ::protobuf_unittest::TestParsingMergeLite_RepeatedFieldsGenerator_Group2 >*
      mutable_group2();
  const ::google::protobuf::RepeatedPtrField< ::protobuf_unittest::TestParsingMergeLite_RepeatedFieldsGenerator_Group2 >&
      group2() const;

  // repeated .protobuf_unittest.TestAllTypesLite ext1 = 1000;
  int ext1_size() const;
  void clear_ext1();
  static const int kExt1FieldNumber = 1000;
  const ::protobuf_unittest::TestAllTypesLite& ext1(int index) const;
  ::protobuf_unittest::TestAllTypesLite* mutable_ext1(int index);
  ::protobuf_unittest::TestAllTypesLite* add_ext1();
  ::google::protobuf::RepeatedPtrField< ::protobuf_unittest::TestAllTypesLite >*
      mutable_ext1();
  const ::google::protobuf::RepeatedPtrField< ::protobuf_unittest::TestAllTypesLite >&
      ext1() const;

  // repeated .protobuf_unittest.TestAllTypesLite ext2 = 1001;
  int ext2_size() const;
  void clear_ext2();
  static const int kExt2FieldNumber = 1001;
  const ::protobuf_unittest::TestAllTypesLite& ext2(int index) const;
  ::protobuf_unittest::TestAllTypesLite* mutable_ext2(int index);
  ::protobuf_unittest::TestAllTypesLite* add_ext2();
  ::google::protobuf::RepeatedPtrField< ::protobuf_unittest::TestAllTypesLite >*
      mutable_ext2();
  const ::google::protobuf::RepeatedPtrField< ::protobuf_unittest::TestAllTypesLite >&
      ext2() const;

  // @@protoc_insertion_point(class_scope:protobuf_unittest.TestParsingMergeLite.RepeatedFieldsGenerator)
 private:

  ::google::protobuf::internal::ArenaStringPtr _unknown_fields_;
  ::google::protobuf::Arena* _arena_ptr_;

  ::google::protobuf::internal::HasBits<1> _has_bits_;
  mutable int _cached_size_;
  ::google::protobuf::RepeatedPtrField< ::protobuf_unittest::TestAllTypesLite > field1_;
  ::google::protobuf::RepeatedPtrField< ::protobuf_unittest::TestAllTypesLite > field2_;
  ::google::protobuf::RepeatedPtrField< ::protobuf_unittest::TestAllTypesLite > field3_;
  ::google::protobuf::RepeatedPtrField< ::protobuf_unittest::TestParsingMergeLite_RepeatedFieldsGenerator_Group1 > group1_;
  ::google::protobuf::RepeatedPtrField< ::protobuf_unittest::TestParsingMergeLite_RepeatedFieldsGenerator_Group2 > group2_;
  ::google::protobuf::RepeatedPtrField< ::protobuf_unittest::TestAllTypesLite > ext1_;
  ::google::protobuf::RepeatedPtrField< ::protobuf_unittest::TestAllTypesLite > ext2_;
  friend void  protobuf_InitDefaults_google_2fprotobuf_2funittest_5flite_2eproto_impl();
  friend void  protobuf_AddDesc_google_2fprotobuf_2funittest_5flite_2eproto_impl();
  friend void protobuf_AssignDesc_google_2fprotobuf_2funittest_5flite_2eproto();
  friend void protobuf_ShutdownFile_google_2fprotobuf_2funittest_5flite_2eproto();

  void InitAsDefaultInstance();
};
extern ::google::protobuf::internal::ExplicitlyConstructed<TestParsingMergeLite_RepeatedFieldsGenerator> TestParsingMergeLite_RepeatedFieldsGenerator_default_instance_;

// -------------------------------------------------------------------

class TestParsingMergeLite_OptionalGroup : public ::google::protobuf::MessageLite /* @@protoc_insertion_point(class_definition:protobuf_unittest.TestParsingMergeLite.OptionalGroup) */ {
 public:
  TestParsingMergeLite_OptionalGroup();
  virtual ~TestParsingMergeLite_OptionalGroup();

  TestParsingMergeLite_OptionalGroup(const TestParsingMergeLite_OptionalGroup& from);

  inline TestParsingMergeLite_OptionalGroup& operator=(const TestParsingMergeLite_OptionalGroup& from) {
    CopyFrom(from);
    return *this;
  }

  inline const ::std::string& unknown_fields() const {
    return _unknown_fields_.GetNoArena(
        &::google::protobuf::internal::GetEmptyStringAlreadyInited());
  }

  inline ::std::string* mutable_unknown_fields() {
    return _unknown_fields_.MutableNoArena(
        &::google::protobuf::internal::GetEmptyStringAlreadyInited());
  }

  static const TestParsingMergeLite_OptionalGroup& default_instance();

  static const TestParsingMergeLite_OptionalGroup* internal_default_instance();

  void Swap(TestParsingMergeLite_OptionalGroup* other);

  // implements Message ----------------------------------------------

  inline TestParsingMergeLite_OptionalGroup* New() const { return New(NULL); }

  TestParsingMergeLite_OptionalGroup* New(::google::protobuf::Arena* arena) const;
  void CheckTypeAndMergeFrom(const ::google::protobuf::MessageLite& from);
  void CopyFrom(const TestParsingMergeLite_OptionalGroup& from);
  void MergeFrom(const TestParsingMergeLite_OptionalGroup& from);
  void Clear();
  bool IsInitialized() const;

  size_t ByteSizeLong() const;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input);
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const;
  void DiscardUnknownFields();
  int GetCachedSize() const { return _cached_size_; }
  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const;
  void InternalSwap(TestParsingMergeLite_OptionalGroup* other);
  void UnsafeMergeFrom(const TestParsingMergeLite_OptionalGroup& from);
  private:
  inline ::google::protobuf::Arena* GetArenaNoVirtual() const {
    return _arena_ptr_;
  }
  inline ::google::protobuf::Arena* MaybeArenaPtr() const {
    return _arena_ptr_;
  }
  public:

  ::std::string GetTypeName() const;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // optional .protobuf_unittest.TestAllTypesLite optional_group_all_types = 11;
  bool has_optional_group_all_types() const;
  void clear_optional_group_all_types();
  static const int kOptionalGroupAllTypesFieldNumber = 11;
  const ::protobuf_unittest::TestAllTypesLite& optional_group_all_types() const;
  ::protobuf_unittest::TestAllTypesLite* mutable_optional_group_all_types();
  ::protobuf_unittest::TestAllTypesLite* release_optional_group_all_types();
  void set_allocated_optional_group_all_types(::protobuf_unittest::TestAllTypesLite* optional_group_all_types);

  // @@protoc_insertion_point(class_scope:protobuf_unittest.TestParsingMergeLite.OptionalGroup)
 private:
  inline void set_has_optional_group_all_types();
  inline void clear_has_optional_group_all_types();

  ::google::protobuf::internal::ArenaStringPtr _unknown_fields_;
  ::google::protobuf::Arena* _arena_ptr_;

  ::google::protobuf::internal::HasBits<1> _has_bits_;
  mutable int _cached_size_;
  ::protobuf_unittest::TestAllTypesLite* optional_group_all_types_;
  friend void  protobuf_InitDefaults_google_2fprotobuf_2funittest_5flite_2eproto_impl();
  friend void  protobuf_AddDesc_google_2fprotobuf_2funittest_5flite_2eproto_impl();
  friend void protobuf_AssignDesc_google_2fprotobuf_2funittest_5flite_2eproto();
  friend void protobuf_ShutdownFile_google_2fprotobuf_2funittest_5flite_2eproto();

  void InitAsDefaultInstance();
};
extern ::google::protobuf::internal::ExplicitlyConstructed<TestParsingMergeLite_OptionalGroup> TestParsingMergeLite_OptionalGroup_default_instance_;

// -------------------------------------------------------------------

class TestParsingMergeLite_RepeatedGroup : public ::google::protobuf::MessageLite /* @@protoc_insertion_point(class_definition:protobuf_unittest.TestParsingMergeLite.RepeatedGroup) */ {
 public:
  TestParsingMergeLite_RepeatedGroup();
  virtual ~TestParsingMergeLite_RepeatedGroup();

  TestParsingMergeLite_RepeatedGroup(const TestParsingMergeLite_RepeatedGroup& from);

  inline TestParsingMergeLite_RepeatedGroup& operator=(const TestParsingMergeLite_RepeatedGroup& from) {
    CopyFrom(from);
    return *this;
  }

  inline const ::std::string& unknown_fields() const {
    return _unknown_fields_.GetNoArena(
        &::google::protobuf::internal::GetEmptyStringAlreadyInited());
  }

  inline ::std::string* mutable_unknown_fields() {
    return _unknown_fields_.MutableNoArena(
        &::google::protobuf::internal::GetEmptyStringAlreadyInited());
  }

  static const TestParsingMergeLite_RepeatedGroup& default_instance();

  static const TestParsingMergeLite_RepeatedGroup* internal_default_instance();

  void Swap(TestParsingMergeLite_RepeatedGroup* other);

  // implements Message ----------------------------------------------

  inline TestParsingMergeLite_RepeatedGroup* New() const { return New(NULL); }

  TestParsingMergeLite_RepeatedGroup* New(::google::protobuf::Arena* arena) const;
  void CheckTypeAndMergeFrom(const ::google::protobuf::MessageLite& from);
  void CopyFrom(const TestParsingMergeLite_RepeatedGroup& from);
  void MergeFrom(const TestParsingMergeLite_RepeatedGroup& from);
  void Clear();
  bool IsInitialized() const;

  size_t ByteSizeLong() const;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input);
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const;
  void DiscardUnknownFields();
  int GetCachedSize() const { return _cached_size_; }
  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const;
  void InternalSwap(TestParsingMergeLite_RepeatedGroup* other);
  void UnsafeMergeFrom(const TestParsingMergeLite_RepeatedGroup& from);
  private:
  inline ::google::protobuf::Arena* GetArenaNoVirtual() const {
    return _arena_ptr_;
  }
  inline ::google::protobuf::Arena* MaybeArenaPtr() const {
    return _arena_ptr_;
  }
  public:

  ::std::string GetTypeName() const;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // optional .protobuf_unittest.TestAllTypesLite repeated_group_all_types = 21;
  bool has_repeated_group_all_types() const;
  void clear_repeated_group_all_types();
  static const int kRepeatedGroupAllTypesFieldNumber = 21;
  const ::protobuf_unittest::TestAllTypesLite& repeated_group_all_types() const;
  ::protobuf_unittest::TestAllTypesLite* mutable_repeated_group_all_types();
  ::protobuf_unittest::TestAllTypesLite* release_repeated_group_all_types();
  void set_allocated_repeated_group_all_types(::protobuf_unittest::TestAllTypesLite* repeated_group_all_types);

  // @@protoc_insertion_point(class_scope:protobuf_unittest.TestParsingMergeLite.RepeatedGroup)
 private:
  inline void set_has_repeated_group_all_types();
  inline void clear_has_repeated_group_all_types();

  ::google::protobuf::internal::ArenaStringPtr _unknown_fields_;
  ::google::protobuf::Arena* _arena_ptr_;

  ::google::protobuf::internal::HasBits<1> _has_bits_;
  mutable int _cached_size_;
  ::protobuf_unittest::TestAllTypesLite* repeated_group_all_types_;
  friend void  protobuf_InitDefaults_google_2fprotobuf_2funittest_5flite_2eproto_impl();
  friend void  protobuf_AddDesc_google_2fprotobuf_2funittest_5flite_2eproto_impl();
  friend void protobuf_AssignDesc_google_2fprotobuf_2funittest_5flite_2eproto();
  friend void protobuf_ShutdownFile_google_2fprotobuf_2funittest_5flite_2eproto();

  void InitAsDefaultInstance();
};
extern ::google::protobuf::internal::ExplicitlyConstructed<TestParsingMergeLite_RepeatedGroup> TestParsingMergeLite_RepeatedGroup_default_instance_;

// -------------------------------------------------------------------

class TestParsingMergeLite : public ::google::protobuf::MessageLite /* @@protoc_insertion_point(class_definition:protobuf_unittest.TestParsingMergeLite) */ {
 public:
  TestParsingMergeLite();
  virtual ~TestParsingMergeLite();

  TestParsingMergeLite(const TestParsingMergeLite& from);

  inline TestParsingMergeLite& operator=(const TestParsingMergeLite& from) {
    CopyFrom(from);
    return *this;
  }

  inline const ::std::string& unknown_fields() const {
    return _unknown_fields_.GetNoArena(
        &::google::protobuf::internal::GetEmptyStringAlreadyInited());
  }

  inline ::std::string* mutable_unknown_fields() {
    return _unknown_fields_.MutableNoArena(
        &::google::protobuf::internal::GetEmptyStringAlreadyInited());
  }

  static const TestParsingMergeLite& default_instance();

  static const TestParsingMergeLite* internal_default_instance();

  void Swap(TestParsingMergeLite* other);

  // implements Message ----------------------------------------------

  inline TestParsingMergeLite* New() const { return New(NULL); }

  TestParsingMergeLite* New(::google::protobuf::Arena* arena) const;
  void CheckTypeAndMergeFrom(const ::google::protobuf::MessageLite& from);
  void CopyFrom(const TestParsingMergeLite& from);
  void MergeFrom(const TestParsingMergeLite& from);
  void Clear();
  bool IsInitialized() const;

  size_t ByteSizeLong() const;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input);
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const;
  void DiscardUnknownFields();
  int GetCachedSize() const { return _cached_size_; }
  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const;
  void InternalSwap(TestParsingMergeLite* other);
  void UnsafeMergeFrom(const TestParsingMergeLite& from);
  private:
  inline ::google::protobuf::Arena* GetArenaNoVirtual() const {
    return _arena_ptr_;
  }
  inline ::google::protobuf::Arena* MaybeArenaPtr() const {
    return _arena_ptr_;
  }
  public:

  ::std::string GetTypeName() const;

  // nested types ----------------------------------------------------

  typedef TestParsingMergeLite_RepeatedFieldsGenerator RepeatedFieldsGenerator;
  typedef TestParsingMergeLite_OptionalGroup OptionalGroup;
  typedef TestParsingMergeLite_RepeatedGroup RepeatedGroup;

  // accessors -------------------------------------------------------

  // required .protobuf_unittest.TestAllTypesLite required_all_types = 1;
  bool has_required_all_types() const;
  void clear_required_all_types();
  static const int kRequiredAllTypesFieldNumber = 1;
  const ::protobuf_unittest::TestAllTypesLite& required_all_types() const;
  ::protobuf_unittest::TestAllTypesLite* mutable_required_all_types();
  ::protobuf_unittest::TestAllTypesLite* release_required_all_types();
  void set_allocated_required_all_types(::protobuf_unittest::TestAllTypesLite* required_all_types);

  // optional .protobuf_unittest.TestAllTypesLite optional_all_types = 2;
  bool has_optional_all_types() const;
  void clear_optional_all_types();
  static const int kOptionalAllTypesFieldNumber = 2;
  const ::protobuf_unittest::TestAllTypesLite& optional_all_types() const;
  ::protobuf_unittest::TestAllTypesLite* mutable_optional_all_types();
  ::protobuf_unittest::TestAllTypesLite* release_optional_all_types();
  void set_allocated_optional_all_types(::protobuf_unittest::TestAllTypesLite* optional_all_types);

  // repeated .protobuf_unittest.TestAllTypesLite repeated_all_types = 3;
  int repeated_all_types_size() const;
  void clear_repeated_all_types();
  static const int kRepeatedAllTypesFieldNumber = 3;
  const ::protobuf_unittest::TestAllTypesLite& repeated_all_types(int index) const;
  ::protobuf_unittest::TestAllTypesLite* mutable_repeated_all_types(int index);
  ::protobuf_unittest::TestAllTypesLite* add_repeated_all_types();
  ::google::protobuf::RepeatedPtrField< ::protobuf_unittest::TestAllTypesLite >*
      mutable_repeated_all_types();
  const ::google::protobuf::RepeatedPtrField< ::protobuf_unittest::TestAllTypesLite >&
      repeated_all_types() const;

  // optional group OptionalGroup = 10 { ... };
  bool has_optionalgroup() const;
  void clear_optionalgroup();
  static const int kOptionalgroupFieldNumber = 10;
  const ::protobuf_unittest::TestParsingMergeLite_OptionalGroup& optionalgroup() const;
  ::protobuf_unittest::TestParsingMergeLite_OptionalGroup* mutable_optionalgroup();
  ::protobuf_unittest::TestParsingMergeLite_OptionalGroup* release_optionalgroup();
  void set_allocated_optionalgroup(::protobuf_unittest::TestParsingMergeLite_OptionalGroup* optionalgroup);

  // repeated group RepeatedGroup = 20 { ... };
  int repeatedgroup_size() const;
  void clear_repeatedgroup();
  static const int kRepeatedgroupFieldNumber = 20;
  const ::protobuf_unittest::TestParsingMergeLite_RepeatedGroup& repeatedgroup(int index) const;
  ::protobuf_unittest::TestParsingMergeLite_RepeatedGroup* mutable_repeatedgroup(int index);
  ::protobuf_unittest::TestParsingMergeLite_RepeatedGroup* add_repeatedgroup();
  ::google::protobuf::RepeatedPtrField< ::protobuf_unittest::TestParsingMergeLite_RepeatedGroup >*
      mutable_repeatedgroup();
  const ::google::protobuf::RepeatedPtrField< ::protobuf_unittest::TestParsingMergeLite_RepeatedGroup >&
      repeatedgroup() const;

  GOOGLE_PROTOBUF_EXTENSION_ACCESSORS(TestParsingMergeLite)
  static const int kOptionalExtFieldNumber = 1000;
  static ::google::protobuf::internal::ExtensionIdentifier< ::protobuf_unittest::TestParsingMergeLite,
      ::google::protobuf::internal::MessageTypeTraits< ::protobuf_unittest::TestAllTypesLite >, 11, false >
    optional_ext;
  static const int kRepeatedExtFieldNumber = 1001;
  static ::google::protobuf::internal::ExtensionIdentifier< ::protobuf_unittest::TestParsingMergeLite,
      ::google::protobuf::internal::RepeatedMessageTypeTraits< ::protobuf_unittest::TestAllTypesLite >, 11, false >
    repeated_ext;
  // @@protoc_insertion_point(class_scope:protobuf_unittest.TestParsingMergeLite)
 private:
  inline void set_has_required_all_types();
  inline void clear_has_required_all_types();
  inline void set_has_optional_all_types();
  inline void clear_has_optional_all_types();
  inline void set_has_optionalgroup();
  inline void clear_has_optionalgroup();

  ::google::protobuf::internal::ExtensionSet _extensions_;

  ::google::protobuf::internal::ArenaStringPtr _unknown_fields_;
  ::google::protobuf::Arena* _arena_ptr_;

  ::google::protobuf::internal::HasBits<1> _has_bits_;
  mutable int _cached_size_;
  ::google::protobuf::RepeatedPtrField< ::protobuf_unittest::TestAllTypesLite > repeated_all_types_;
  ::google::protobuf::RepeatedPtrField< ::protobuf_unittest::TestParsingMergeLite_RepeatedGroup > repeatedgroup_;
  ::protobuf_unittest::TestAllTypesLite* required_all_types_;
  ::protobuf_unittest::TestAllTypesLite* optional_all_types_;
  ::protobuf_unittest::TestParsingMergeLite_OptionalGroup* optionalgroup_;
  friend void  protobuf_InitDefaults_google_2fprotobuf_2funittest_5flite_2eproto_impl();
  friend void  protobuf_AddDesc_google_2fprotobuf_2funittest_5flite_2eproto_impl();
  friend void protobuf_AssignDesc_google_2fprotobuf_2funittest_5flite_2eproto();
  friend void protobuf_ShutdownFile_google_2fprotobuf_2funittest_5flite_2eproto();

  void InitAsDefaultInstance();
};
extern ::google::protobuf::internal::ExplicitlyConstructed<TestParsingMergeLite> TestParsingMergeLite_default_instance_;

// -------------------------------------------------------------------

class TestEmptyMessageLite : public ::google::protobuf::MessageLite /* @@protoc_insertion_point(class_definition:protobuf_unittest.TestEmptyMessageLite) */ {
 public:
  TestEmptyMessageLite();
  virtual ~TestEmptyMessageLite();

  TestEmptyMessageLite(const TestEmptyMessageLite& from);

  inline TestEmptyMessageLite& operator=(const TestEmptyMessageLite& from) {
    CopyFrom(from);
    return *this;
  }

  inline const ::std::string& unknown_fields() const {
    return _unknown_fields_.GetNoArena(
        &::google::protobuf::internal::GetEmptyStringAlreadyInited());
  }

  inline ::std::string* mutable_unknown_fields() {
    return _unknown_fields_.MutableNoArena(
        &::google::protobuf::internal::GetEmptyStringAlreadyInited());
  }

  static const TestEmptyMessageLite& default_instance();

  static const TestEmptyMessageLite* internal_default_instance();

  void Swap(TestEmptyMessageLite* other);

  // implements Message ----------------------------------------------

  inline TestEmptyMessageLite* New() const { return New(NULL); }

  TestEmptyMessageLite* New(::google::protobuf::Arena* arena) const;
  void CheckTypeAndMergeFrom(const ::google::protobuf::MessageLite& from);
  void CopyFrom(const TestEmptyMessageLite& from);
  void MergeFrom(const TestEmptyMessageLite& from);
  void Clear();
  bool IsInitialized() const;

  size_t ByteSizeLong() const;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input);
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const;
  void DiscardUnknownFields();
  int GetCachedSize() const { return _cached_size_; }
  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const;
  void InternalSwap(TestEmptyMessageLite* other);
  void UnsafeMergeFrom(const TestEmptyMessageLite& from);
  private:
  inline ::google::protobuf::Arena* GetArenaNoVirtual() const {
    return _arena_ptr_;
  }
  inline ::google::protobuf::Arena* MaybeArenaPtr() const {
    return _arena_ptr_;
  }
  public:

  ::std::string GetTypeName() const;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // @@protoc_insertion_point(class_scope:protobuf_unittest.TestEmptyMessageLite)
 private:

  ::google::protobuf::internal::ArenaStringPtr _unknown_fields_;
  ::google::protobuf::Arena* _arena_ptr_;

  ::google::protobuf::internal::HasBits<1> _has_bits_;
  mutable int _cached_size_;
  friend void  protobuf_InitDefaults_google_2fprotobuf_2funittest_5flite_2eproto_impl();
  friend void  protobuf_AddDesc_google_2fprotobuf_2funittest_5flite_2eproto_impl();
  friend void protobuf_AssignDesc_google_2fprotobuf_2funittest_5flite_2eproto();
  friend void protobuf_ShutdownFile_google_2fprotobuf_2funittest_5flite_2eproto();

  void InitAsDefaultInstance();
};
extern ::google::protobuf::internal::ExplicitlyConstructed<TestEmptyMessageLite> TestEmptyMessageLite_default_instance_;

// -------------------------------------------------------------------

class TestEmptyMessageWithExtensionsLite : public ::google::protobuf::MessageLite /* @@protoc_insertion_point(class_definition:protobuf_unittest.TestEmptyMessageWithExtensionsLite) */ {
 public:
  TestEmptyMessageWithExtensionsLite();
  virtual ~TestEmptyMessageWithExtensionsLite();

  TestEmptyMessageWithExtensionsLite(const TestEmptyMessageWithExtensionsLite& from);

  inline TestEmptyMessageWithExtensionsLite& operator=(const TestEmptyMessageWithExtensionsLite& from) {
    CopyFrom(from);
    return *this;
  }

  inline const ::std::string& unknown_fields() const {
    return _unknown_fields_.GetNoArena(
        &::google::protobuf::internal::GetEmptyStringAlreadyInited());
  }

  inline ::std::string* mutable_unknown_fields() {
    return _unknown_fields_.MutableNoArena(
        &::google::protobuf::internal::GetEmptyStringAlreadyInited());
  }

  static const TestEmptyMessageWithExtensionsLite& default_instance();

  static const TestEmptyMessageWithExtensionsLite* internal_default_instance();

  void Swap(TestEmptyMessageWithExtensionsLite* other);

  // implements Message ----------------------------------------------

  inline TestEmptyMessageWithExtensionsLite* New() const { return New(NULL); }

  TestEmptyMessageWithExtensionsLite* New(::google::protobuf::Arena* arena) const;
  void CheckTypeAndMergeFrom(const ::google::protobuf::MessageLite& from);
  void CopyFrom(const TestEmptyMessageWithExtensionsLite& from);
  void MergeFrom(const TestEmptyMessageWithExtensionsLite& from);
  void Clear();
  bool IsInitialized() const;

  size_t ByteSizeLong() const;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input);
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const;
  void DiscardUnknownFields();
  int GetCachedSize() const { return _cached_size_; }
  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const;
  void InternalSwap(TestEmptyMessageWithExtensionsLite* other);
  void UnsafeMergeFrom(const TestEmptyMessageWithExtensionsLite& from);
  private:
  inline ::google::protobuf::Arena* GetArenaNoVirtual() const {
    return _arena_ptr_;
  }
  inline ::google::protobuf::Arena* MaybeArenaPtr() const {
    return _arena_ptr_;
  }
  public:

  ::std::string GetTypeName() const;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  GOOGLE_PROTOBUF_EXTENSION_ACCESSORS(TestEmptyMessageWithExtensionsLite)
  // @@protoc_insertion_point(class_scope:protobuf_unittest.TestEmptyMessageWithExtensionsLite)
 private:

  ::google::protobuf::internal::ExtensionSet _extensions_;

  ::google::protobuf::internal::ArenaStringPtr _unknown_fields_;
  ::google::protobuf::Arena* _arena_ptr_;

  ::google::protobuf::internal::HasBits<1> _has_bits_;
  mutable int _cached_size_;
  friend void  protobuf_InitDefaults_google_2fprotobuf_2funittest_5flite_2eproto_impl();
  friend void  protobuf_AddDesc_google_2fprotobuf_2funittest_5flite_2eproto_impl();
  friend void protobuf_AssignDesc_google_2fprotobuf_2funittest_5flite_2eproto();
  friend void protobuf_ShutdownFile_google_2fprotobuf_2funittest_5flite_2eproto();

  void InitAsDefaultInstance();
};
extern ::google::protobuf::internal::ExplicitlyConstructed<TestEmptyMessageWithExtensionsLite> TestEmptyMessageWithExtensionsLite_default_instance_;

// -------------------------------------------------------------------

class V1MessageLite : public ::google::protobuf::MessageLite /* @@protoc_insertion_point(class_definition:protobuf_unittest.V1MessageLite) */ {
 public:
  V1MessageLite();
  virtual ~V1MessageLite();

  V1MessageLite(const V1MessageLite& from);

  inline V1MessageLite& operator=(const V1MessageLite& from) {
    CopyFrom(from);
    return *this;
  }

  inline const ::std::string& unknown_fields() const {
    return _unknown_fields_.GetNoArena(
        &::google::protobuf::internal::GetEmptyStringAlreadyInited());
  }

  inline ::std::string* mutable_unknown_fields() {
    return _unknown_fields_.MutableNoArena(
        &::google::protobuf::internal::GetEmptyStringAlreadyInited());
  }

  static const V1MessageLite& default_instance();

  static const V1MessageLite* internal_default_instance();

  void Swap(V1MessageLite* other);

  // implements Message ----------------------------------------------

  inline V1MessageLite* New() const { return New(NULL); }

  V1MessageLite* New(::google::protobuf::Arena* arena) const;
  void CheckTypeAndMergeFrom(const ::google::protobuf::MessageLite& from);
  void CopyFrom(const V1MessageLite& from);
  void MergeFrom(const V1MessageLite& from);
  void Clear();
  bool IsInitialized() const;

  size_t ByteSizeLong() const;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input);
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const;
  void DiscardUnknownFields();
  int GetCachedSize() const { return _cached_size_; }
  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const;
  void InternalSwap(V1MessageLite* other);
  void UnsafeMergeFrom(const V1MessageLite& from);
  private:
  inline ::google::protobuf::Arena* GetArenaNoVirtual() const {
    return _arena_ptr_;
  }
  inline ::google::protobuf::Arena* MaybeArenaPtr() const {
    return _arena_ptr_;
  }
  public:

  ::std::string GetTypeName() const;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // required int32 int_field = 1;
  bool has_int_field() const;
  void clear_int_field();
  static const int kIntFieldFieldNumber = 1;
  ::google::protobuf::int32 int_field() const;
  void set_int_field(::google::protobuf::int32 value);

  // optional .protobuf_unittest.V1EnumLite enum_field = 2 [default = V1_FIRST];
  bool has_enum_field() const;
  void clear_enum_field();
  static const int kEnumFieldFieldNumber = 2;
  ::protobuf_unittest::V1EnumLite enum_field() const;
  void set_enum_field(::protobuf_unittest::V1EnumLite value);

  // @@protoc_insertion_point(class_scope:protobuf_unittest.V1MessageLite)
 private:
  inline void set_has_int_field();
  inline void clear_has_int_field();
  inline void set_has_enum_field();
  inline void clear_has_enum_field();

  ::google::protobuf::internal::ArenaStringPtr _unknown_fields_;
  ::google::protobuf::Arena* _arena_ptr_;

  ::google::protobuf::internal::HasBits<1> _has_bits_;
  mutable int _cached_size_;
  ::google::protobuf::int32 int_field_;
  int enum_field_;
  friend void  protobuf_InitDefaults_google_2fprotobuf_2funittest_5flite_2eproto_impl();
  friend void  protobuf_AddDesc_google_2fprotobuf_2funittest_5flite_2eproto_impl();
  friend void protobuf_AssignDesc_google_2fprotobuf_2funittest_5flite_2eproto();
  friend void protobuf_ShutdownFile_google_2fprotobuf_2funittest_5flite_2eproto();

  void InitAsDefaultInstance();
};
extern ::google::protobuf::internal::ExplicitlyConstructed<V1MessageLite> V1MessageLite_default_instance_;

// -------------------------------------------------------------------

class V2MessageLite : public ::google::protobuf::MessageLite /* @@protoc_insertion_point(class_definition:protobuf_unittest.V2MessageLite) */ {
 public:
  V2MessageLite();
  virtual ~V2MessageLite();

  V2MessageLite(const V2MessageLite& from);

  inline V2MessageLite& operator=(const V2MessageLite& from) {
    CopyFrom(from);
    return *this;
  }

  inline const ::std::string& unknown_fields() const {
    return _unknown_fields_.GetNoArena(
        &::google::protobuf::internal::GetEmptyStringAlreadyInited());
  }

  inline ::std::string* mutable_unknown_fields() {
    return _unknown_fields_.MutableNoArena(
        &::google::protobuf::internal::GetEmptyStringAlreadyInited());
  }

  static const V2MessageLite& default_instance();

  static const V2MessageLite* internal_default_instance();

  void Swap(V2MessageLite* other);

  // implements Message ----------------------------------------------

  inline V2MessageLite* New() const { return New(NULL); }

  V2MessageLite* New(::google::protobuf::Arena* arena) const;
  void CheckTypeAndMergeFrom(const ::google::protobuf::MessageLite& from);
  void CopyFrom(const V2MessageLite& from);
  void MergeFrom(const V2MessageLite& from);
  void Clear();
  bool IsInitialized() const;

  size_t ByteSizeLong() const;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input);
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const;
  void DiscardUnknownFields();
  int GetCachedSize() const { return _cached_size_; }
  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const;
  void InternalSwap(V2MessageLite* other);
  void UnsafeMergeFrom(const V2MessageLite& from);
  private:
  inline ::google::protobuf::Arena* GetArenaNoVirtual() const {
    return _arena_ptr_;
  }
  inline ::google::protobuf::Arena* MaybeArenaPtr() const {
    return _arena_ptr_;
  }
  public:

  ::std::string GetTypeName() const;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // required int32 int_field = 1;
  bool has_int_field() const;
  void clear_int_field();
  static const int kIntFieldFieldNumber = 1;
  ::google::protobuf::int32 int_field() const;
  void set_int_field(::google::protobuf::int32 value);

  // optional .protobuf_unittest.V2EnumLite enum_field = 2 [default = V2_FIRST];
  bool has_enum_field() const;
  void clear_enum_field();
  static const int kEnumFieldFieldNumber = 2;
  ::protobuf_unittest::V2EnumLite enum_field() const;
  void set_enum_field(::protobuf_unittest::V2EnumLite value);

  // @@protoc_insertion_point(class_scope:protobuf_unittest.V2MessageLite)
 private:
  inline void set_has_int_field();
  inline void clear_has_int_field();
  inline void set_has_enum_field();
  inline void clear_has_enum_field();

  ::google::protobuf::internal::ArenaStringPtr _unknown_fields_;
  ::google::protobuf::Arena* _arena_ptr_;

  ::google::protobuf::internal::HasBits<1> _has_bits_;
  mutable int _cached_size_;
  ::google::protobuf::int32 int_field_;
  int enum_field_;
  friend void  protobuf_InitDefaults_google_2fprotobuf_2funittest_5flite_2eproto_impl();
  friend void  protobuf_AddDesc_google_2fprotobuf_2funittest_5flite_2eproto_impl();
  friend void protobuf_AssignDesc_google_2fprotobuf_2funittest_5flite_2eproto();
  friend void protobuf_ShutdownFile_google_2fprotobuf_2funittest_5flite_2eproto();

  void InitAsDefaultInstance();
};
extern ::google::protobuf::internal::ExplicitlyConstructed<V2MessageLite> V2MessageLite_default_instance_;

// ===================================================================

static const int kOptionalInt32ExtensionLiteFieldNumber = 1;
extern ::google::protobuf::internal::ExtensionIdentifier< ::protobuf_unittest::TestAllExtensionsLite,
    ::google::protobuf::internal::PrimitiveTypeTraits< ::google::protobuf::int32 >, 5, false >
  optional_int32_extension_lite;
static const int kOptionalInt64ExtensionLiteFieldNumber = 2;
extern ::google::protobuf::internal::ExtensionIdentifier< ::protobuf_unittest::TestAllExtensionsLite,
    ::google::protobuf::internal::PrimitiveTypeTraits< ::google::protobuf::int64 >, 3, false >
  optional_int64_extension_lite;
static const int kOptionalUint32ExtensionLiteFieldNumber = 3;
extern ::google::protobuf::internal::ExtensionIdentifier< ::protobuf_unittest::TestAllExtensionsLite,
    ::google::protobuf::internal::PrimitiveTypeTraits< ::google::protobuf::uint32 >, 13, false >
  optional_uint32_extension_lite;
static const int kOptionalUint64ExtensionLiteFieldNumber = 4;
extern ::google::protobuf::internal::ExtensionIdentifier< ::protobuf_unittest::TestAllExtensionsLite,
    ::google::protobuf::internal::PrimitiveTypeTraits< ::google::protobuf::uint64 >, 4, false >
  optional_uint64_extension_lite;
static const int kOptionalSint32ExtensionLiteFieldNumber = 5;
extern ::google::protobuf::internal::ExtensionIdentifier< ::protobuf_unittest::TestAllExtensionsLite,
    ::google::protobuf::internal::PrimitiveTypeTraits< ::google::protobuf::int32 >, 17, false >
  optional_sint32_extension_lite;
static const int kOptionalSint64ExtensionLiteFieldNumber = 6;
extern ::google::protobuf::internal::ExtensionIdentifier< ::protobuf_unittest::TestAllExtensionsLite,
    ::google::protobuf::internal::PrimitiveTypeTraits< ::google::protobuf::int64 >, 18, false >
  optional_sint64_extension_lite;
static const int kOptionalFixed32ExtensionLiteFieldNumber = 7;
extern ::google::protobuf::internal::ExtensionIdentifier< ::protobuf_unittest::TestAllExtensionsLite,
    ::google::protobuf::internal::PrimitiveTypeTraits< ::google::protobuf::uint32 >, 7, false >
  optional_fixed32_extension_lite;
static const int kOptionalFixed64ExtensionLiteFieldNumber = 8;
extern ::google::protobuf::internal::ExtensionIdentifier< ::protobuf_unittest::TestAllExtensionsLite,
    ::google::protobuf::internal::PrimitiveTypeTraits< ::google::protobuf::uint64 >, 6, false >
  optional_fixed64_extension_lite;
static const int kOptionalSfixed32ExtensionLiteFieldNumber = 9;
extern ::google::protobuf::internal::ExtensionIdentifier< ::protobuf_unittest::TestAllExtensionsLite,
    ::google::protobuf::internal::PrimitiveTypeTraits< ::google::protobuf::int32 >, 15, false >
  optional_sfixed32_extension_lite;
static const int kOptionalSfixed64ExtensionLiteFieldNumber = 10;
extern ::google::protobuf::internal::ExtensionIdentifier< ::protobuf_unittest::TestAllExtensionsLite,
    ::google::protobuf::internal::PrimitiveTypeTraits< ::google::protobuf::int64 >, 16, false >
  optional_sfixed64_extension_lite;
static const int kOptionalFloatExtensionLiteFieldNumber = 11;
extern ::google::protobuf::internal::ExtensionIdentifier< ::protobuf_unittest::TestAllExtensionsLite,
    ::google::protobuf::internal::PrimitiveTypeTraits< float >, 2, false >
  optional_float_extension_lite;
static const int kOptionalDoubleExtensionLiteFieldNumber = 12;
extern ::google::protobuf::internal::ExtensionIdentifier< ::protobuf_unittest::TestAllExtensionsLite,
    ::google::protobuf::internal::PrimitiveTypeTraits< double >, 1, false >
  optional_double_extension_lite;
static const int kOptionalBoolExtensionLiteFieldNumber = 13;
extern ::google::protobuf::internal::ExtensionIdentifier< ::protobuf_unittest::TestAllExtensionsLite,
    ::google::protobuf::internal::PrimitiveTypeTraits< bool >, 8, false >
  optional_bool_extension_lite;
static const int kOptionalStringExtensionLiteFieldNumber = 14;
extern ::google::protobuf::internal::ExtensionIdentifier< ::protobuf_unittest::TestAllExtensionsLite,
    ::google::protobuf::internal::StringTypeTraits, 9, false >
  optional_string_extension_lite;
static const int kOptionalBytesExtensionLiteFieldNumber = 15;
extern ::google::protobuf::internal::ExtensionIdentifier< ::protobuf_unittest::TestAllExtensionsLite,
    ::google::protobuf::internal::StringTypeTraits, 12, false >
  optional_bytes_extension_lite;
static const int kOptionalgroupExtensionLiteFieldNumber = 16;
extern ::google::protobuf::internal::ExtensionIdentifier< ::protobuf_unittest::TestAllExtensionsLite,
    ::google::protobuf::internal::MessageTypeTraits< ::protobuf_unittest::OptionalGroup_extension_lite >, 10, false >
  optionalgroup_extension_lite;
static const int kOptionalNestedMessageExtensionLiteFieldNumber = 18;
extern ::google::protobuf::internal::ExtensionIdentifier< ::protobuf_unittest::TestAllExtensionsLite,
    ::google::protobuf::internal::MessageTypeTraits< ::protobuf_unittest::TestAllTypesLite_NestedMessage >, 11, false >
  optional_nested_message_extension_lite;
static const int kOptionalForeignMessageExtensionLiteFieldNumber = 19;
extern ::google::protobuf::internal::ExtensionIdentifier< ::protobuf_unittest::TestAllExtensionsLite,
    ::google::protobuf::internal::MessageTypeTraits< ::protobuf_unittest::ForeignMessageLite >, 11, false >
  optional_foreign_message_extension_lite;
static const int kOptionalImportMessageExtensionLiteFieldNumber = 20;
extern ::google::protobuf::internal::ExtensionIdentifier< ::protobuf_unittest::TestAllExtensionsLite,
    ::google::protobuf::internal::MessageTypeTraits< ::protobuf_unittest_import::ImportMessageLite >, 11, false >
  optional_import_message_extension_lite;
static const int kOptionalNestedEnumExtensionLiteFieldNumber = 21;
extern ::google::protobuf::internal::ExtensionIdentifier< ::protobuf_unittest::TestAllExtensionsLite,
    ::google::protobuf::internal::EnumTypeTraits< ::protobuf_unittest::TestAllTypesLite_NestedEnum, ::protobuf_unittest::TestAllTypesLite_NestedEnum_IsValid>, 14, false >
  optional_nested_enum_extension_lite;
static const int kOptionalForeignEnumExtensionLiteFieldNumber = 22;
extern ::google::protobuf::internal::ExtensionIdentifier< ::protobuf_unittest::TestAllExtensionsLite,
    ::google::protobuf::internal::EnumTypeTraits< ::protobuf_unittest::ForeignEnumLite, ::protobuf_unittest::ForeignEnumLite_IsValid>, 14, false >
  optional_foreign_enum_extension_lite;
static const int kOptionalImportEnumExtensionLiteFieldNumber = 23;
extern ::google::protobuf::internal::ExtensionIdentifier< ::protobuf_unittest::TestAllExtensionsLite,
    ::google::protobuf::internal::EnumTypeTraits< ::protobuf_unittest_import::ImportEnumLite, ::protobuf_unittest_import::ImportEnumLite_IsValid>, 14, false >
  optional_import_enum_extension_lite;
static const int kOptionalStringPieceExtensionLiteFieldNumber = 24;
extern ::google::protobuf::internal::ExtensionIdentifier< ::protobuf_unittest::TestAllExtensionsLite,
    ::google::protobuf::internal::StringTypeTraits, 9, false >
  optional_string_piece_extension_lite;
static const int kOptionalCordExtensionLiteFieldNumber = 25;
extern ::google::protobuf::internal::ExtensionIdentifier< ::protobuf_unittest::TestAllExtensionsLite,
    ::google::protobuf::internal::StringTypeTraits, 9, false >
  optional_cord_extension_lite;
static const int kOptionalPublicImportMessageExtensionLiteFieldNumber = 26;
extern ::google::protobuf::internal::ExtensionIdentifier< ::protobuf_unittest::TestAllExtensionsLite,
    ::google::protobuf::internal::MessageTypeTraits< ::protobuf_unittest_import::PublicImportMessageLite >, 11, false >
  optional_public_import_message_extension_lite;
static const int kOptionalLazyMessageExtensionLiteFieldNumber = 27;
extern ::google::protobuf::internal::ExtensionIdentifier< ::protobuf_unittest::TestAllExtensionsLite,
    ::google::protobuf::internal::MessageTypeTraits< ::protobuf_unittest::TestAllTypesLite_NestedMessage >, 11, false >
  optional_lazy_message_extension_lite;
static const int kRepeatedInt32ExtensionLiteFieldNumber = 31;
extern ::google::protobuf::internal::ExtensionIdentifier< ::protobuf_unittest::TestAllExtensionsLite,
    ::google::protobuf::internal::RepeatedPrimitiveTypeTraits< ::google::protobuf::int32 >, 5, false >
  repeated_int32_extension_lite;
static const int kRepeatedInt64ExtensionLiteFieldNumber = 32;
extern ::google::protobuf::internal::ExtensionIdentifier< ::protobuf_unittest::TestAllExtensionsLite,
    ::google::protobuf::internal::RepeatedPrimitiveTypeTraits< ::google::protobuf::int64 >, 3, false >
  repeated_int64_extension_lite;
static const int kRepeatedUint32ExtensionLiteFieldNumber = 33;
extern ::google::protobuf::internal::ExtensionIdentifier< ::protobuf_unittest::TestAllExtensionsLite,
    ::google::protobuf::internal::RepeatedPrimitiveTypeTraits< ::google::protobuf::uint32 >, 13, false >
  repeated_uint32_extension_lite;
static const int kRepeatedUint64ExtensionLiteFieldNumber = 34;
extern ::google::protobuf::internal::ExtensionIdentifier< ::protobuf_unittest::TestAllExtensionsLite,
    ::google::protobuf::internal::RepeatedPrimitiveTypeTraits< ::google::protobuf::uint64 >, 4, false >
  repeated_uint64_extension_lite;
static const int kRepeatedSint32ExtensionLiteFieldNumber = 35;
extern ::google::protobuf::internal::ExtensionIdentifier< ::protobuf_unittest::TestAllExtensionsLite,
    ::google::protobuf::internal::RepeatedPrimitiveTypeTraits< ::google::protobuf::int32 >, 17, false >
  repeated_sint32_extension_lite;
static const int kRepeatedSint64ExtensionLiteFieldNumber = 36;
extern ::google::protobuf::internal::ExtensionIdentifier< ::protobuf_unittest::TestAllExtensionsLite,
    ::google::protobuf::internal::RepeatedPrimitiveTypeTraits< ::google::protobuf::int64 >, 18, false >
  repeated_sint64_extension_lite;
static const int kRepeatedFixed32ExtensionLiteFieldNumber = 37;
extern ::google::protobuf::internal::ExtensionIdentifier< ::protobuf_unittest::TestAllExtensionsLite,
    ::google::protobuf::internal::RepeatedPrimitiveTypeTraits< ::google::protobuf::uint32 >, 7, false >
  repeated_fixed32_extension_lite;
static const int kRepeatedFixed64ExtensionLiteFieldNumber = 38;
extern ::google::protobuf::internal::ExtensionIdentifier< ::protobuf_unittest::TestAllExtensionsLite,
    ::google::protobuf::internal::RepeatedPrimitiveTypeTraits< ::google::protobuf::uint64 >, 6, false >
  repeated_fixed64_extension_lite;
static const int kRepeatedSfixed32ExtensionLiteFieldNumber = 39;
extern ::google::protobuf::internal::ExtensionIdentifier< ::protobuf_unittest::TestAllExtensionsLite,
    ::google::protobuf::internal::RepeatedPrimitiveTypeTraits< ::google::protobuf::int32 >, 15, false >
  repeated_sfixed32_extension_lite;
static const int kRepeatedSfixed64ExtensionLiteFieldNumber = 40;
extern ::google::protobuf::internal::ExtensionIdentifier< ::protobuf_unittest::TestAllExtensionsLite,
    ::google::protobuf::internal::RepeatedPrimitiveTypeTraits< ::google::protobuf::int64 >, 16, false >
  repeated_sfixed64_extension_lite;
static const int kRepeatedFloatExtensionLiteFieldNumber = 41;
extern ::google::protobuf::internal::ExtensionIdentifier< ::protobuf_unittest::TestAllExtensionsLite,
    ::google::protobuf::internal::RepeatedPrimitiveTypeTraits< float >, 2, false >
  repeated_float_extension_lite;
static const int kRepeatedDoubleExtensionLiteFieldNumber = 42;
extern ::google::protobuf::internal::ExtensionIdentifier< ::protobuf_unittest::TestAllExtensionsLite,
    ::google::protobuf::internal::RepeatedPrimitiveTypeTraits< double >, 1, false >
  repeated_double_extension_lite;
static const int kRepeatedBoolExtensionLiteFieldNumber = 43;
extern ::google::protobuf::internal::ExtensionIdentifier< ::protobuf_unittest::TestAllExtensionsLite,
    ::google::protobuf::internal::RepeatedPrimitiveTypeTraits< bool >, 8, false >
  repeated_bool_extension_lite;
static const int kRepeatedStringExtensionLiteFieldNumber = 44;
extern ::google::protobuf::internal::ExtensionIdentifier< ::protobuf_unittest::TestAllExtensionsLite,
    ::google::protobuf::internal::RepeatedStringTypeTraits, 9, false >
  repeated_string_extension_lite;
static const int kRepeatedBytesExtensionLiteFieldNumber = 45;
extern ::google::protobuf::internal::ExtensionIdentifier< ::protobuf_unittest::TestAllExtensionsLite,
    ::google::protobuf::internal::RepeatedStringTypeTraits, 12, false >
  repeated_bytes_extension_lite;
static const int kRepeatedgroupExtensionLiteFieldNumber = 46;
extern ::google::protobuf::internal::ExtensionIdentifier< ::protobuf_unittest::TestAllExtensionsLite,
    ::google::protobuf::internal::RepeatedMessageTypeTraits< ::protobuf_unittest::RepeatedGroup_extension_lite >, 10, false >
  repeatedgroup_extension_lite;
static const int kRepeatedNestedMessageExtensionLiteFieldNumber = 48;
extern ::google::protobuf::internal::ExtensionIdentifier< ::protobuf_unittest::TestAllExtensionsLite,
    ::google::protobuf::internal::RepeatedMessageTypeTraits< ::protobuf_unittest::TestAllTypesLite_NestedMessage >, 11, false >
  repeated_nested_message_extension_lite;
static const int kRepeatedForeignMessageExtensionLiteFieldNumber = 49;
extern ::google::protobuf::internal::ExtensionIdentifier< ::protobuf_unittest::TestAllExtensionsLite,
    ::google::protobuf::internal::RepeatedMessageTypeTraits< ::protobuf_unittest::ForeignMessageLite >, 11, false >
  repeated_foreign_message_extension_lite;
static const int kRepeatedImportMessageExtensionLiteFieldNumber = 50;
extern ::google::protobuf::internal::ExtensionIdentifier< ::protobuf_unittest::TestAllExtensionsLite,
    ::google::protobuf::internal::RepeatedMessageTypeTraits< ::protobuf_unittest_import::ImportMessageLite >, 11, false >
  repeated_import_message_extension_lite;
static const int kRepeatedNestedEnumExtensionLiteFieldNumber = 51;
extern ::google::protobuf::internal::ExtensionIdentifier< ::protobuf_unittest::TestAllExtensionsLite,
    ::google::protobuf::internal::RepeatedEnumTypeTraits< ::protobuf_unittest::TestAllTypesLite_NestedEnum, ::protobuf_unittest::TestAllTypesLite_NestedEnum_IsValid>, 14, false >
  repeated_nested_enum_extension_lite;
static const int kRepeatedForeignEnumExtensionLiteFieldNumber = 52;
extern ::google::protobuf::internal::ExtensionIdentifier< ::protobuf_unittest::TestAllExtensionsLite,
    ::google::protobuf::internal::RepeatedEnumTypeTraits< ::protobuf_unittest::ForeignEnumLite, ::protobuf_unittest::ForeignEnumLite_IsValid>, 14, false >
  repeated_foreign_enum_extension_lite;
static const int kRepeatedImportEnumExtensionLiteFieldNumber = 53;
extern ::google::protobuf::internal::ExtensionIdentifier< ::protobuf_unittest::TestAllExtensionsLite,
    ::google::protobuf::internal::RepeatedEnumTypeTraits< ::protobuf_unittest_import::ImportEnumLite, ::protobuf_unittest_import::ImportEnumLite_IsValid>, 14, false >
  repeated_import_enum_extension_lite;
static const int kRepeatedStringPieceExtensionLiteFieldNumber = 54;
extern ::google::protobuf::internal::ExtensionIdentifier< ::protobuf_unittest::TestAllExtensionsLite,
    ::google::protobuf::internal::RepeatedStringTypeTraits, 9, false >
  repeated_string_piece_extension_lite;
static const int kRepeatedCordExtensionLiteFieldNumber = 55;
extern ::google::protobuf::internal::ExtensionIdentifier< ::protobuf_unittest::TestAllExtensionsLite,
    ::google::protobuf::internal::RepeatedStringTypeTraits, 9, false >
  repeated_cord_extension_lite;
static const int kRepeatedLazyMessageExtensionLiteFieldNumber = 57;
extern ::google::protobuf::internal::ExtensionIdentifier< ::protobuf_unittest::TestAllExtensionsLite,
    ::google::protobuf::internal::RepeatedMessageTypeTraits< ::protobuf_unittest::TestAllTypesLite_NestedMessage >, 11, false >
  repeated_lazy_message_extension_lite;
static const int kDefaultInt32ExtensionLiteFieldNumber = 61;
extern ::google::protobuf::internal::ExtensionIdentifier< ::protobuf_unittest::TestAllExtensionsLite,
    ::google::protobuf::internal::PrimitiveTypeTraits< ::google::protobuf::int32 >, 5, false >
  default_int32_extension_lite;
static const int kDefaultInt64ExtensionLiteFieldNumber = 62;
extern ::google::protobuf::internal::ExtensionIdentifier< ::protobuf_unittest::TestAllExtensionsLite,
    ::google::protobuf::internal::PrimitiveTypeTraits< ::google::protobuf::int64 >, 3, false >
  default_int64_extension_lite;
static const int kDefaultUint32ExtensionLiteFieldNumber = 63;
extern ::google::protobuf::internal::ExtensionIdentifier< ::protobuf_unittest::TestAllExtensionsLite,
    ::google::protobuf::internal::PrimitiveTypeTraits< ::google::protobuf::uint32 >, 13, false >
  default_uint32_extension_lite;
static const int kDefaultUint64ExtensionLiteFieldNumber = 64;
extern ::google::protobuf::internal::ExtensionIdentifier< ::protobuf_unittest::TestAllExtensionsLite,
    ::google::protobuf::internal::PrimitiveTypeTraits< ::google::protobuf::uint64 >, 4, false >
  default_uint64_extension_lite;
static const int kDefaultSint32ExtensionLiteFieldNumber = 65;
extern ::google::protobuf::internal::ExtensionIdentifier< ::protobuf_unittest::TestAllExtensionsLite,
    ::google::protobuf::internal::PrimitiveTypeTraits< ::google::protobuf::int32 >, 17, false >
  default_sint32_extension_lite;
static const int kDefaultSint64ExtensionLiteFieldNumber = 66;
extern ::google::protobuf::internal::ExtensionIdentifier< ::protobuf_unittest::TestAllExtensionsLite,
    ::google::protobuf::internal::PrimitiveTypeTraits< ::google::protobuf::int64 >, 18, false >
  default_sint64_extension_lite;
static const int kDefaultFixed32ExtensionLiteFieldNumber = 67;
extern ::google::protobuf::internal::ExtensionIdentifier< ::protobuf_unittest::TestAllExtensionsLite,
    ::google::protobuf::internal::PrimitiveTypeTraits< ::google::protobuf::uint32 >, 7, false >
  default_fixed32_extension_lite;
static const int kDefaultFixed64ExtensionLiteFieldNumber = 68;
extern ::google::protobuf::internal::ExtensionIdentifier< ::protobuf_unittest::TestAllExtensionsLite,
    ::google::protobuf::internal::PrimitiveTypeTraits< ::google::protobuf::uint64 >, 6, false >
  default_fixed64_extension_lite;
static const int kDefaultSfixed32ExtensionLiteFieldNumber = 69;
extern ::google::protobuf::internal::ExtensionIdentifier< ::protobuf_unittest::TestAllExtensionsLite,
    ::google::protobuf::internal::PrimitiveTypeTraits< ::google::protobuf::int32 >, 15, false >
  default_sfixed32_extension_lite;
static const int kDefaultSfixed64ExtensionLiteFieldNumber = 70;
extern ::google::protobuf::internal::ExtensionIdentifier< ::protobuf_unittest::TestAllExtensionsLite,
    ::google::protobuf::internal::PrimitiveTypeTraits< ::google::protobuf::int64 >, 16, false >
  default_sfixed64_extension_lite;
static const int kDefaultFloatExtensionLiteFieldNumber = 71;
extern ::google::protobuf::internal::ExtensionIdentifier< ::protobuf_unittest::TestAllExtensionsLite,
    ::google::protobuf::internal::PrimitiveTypeTraits< float >, 2, false >
  default_float_extension_lite;
static const int kDefaultDoubleExtensionLiteFieldNumber = 72;
extern ::google::protobuf::internal::ExtensionIdentifier< ::protobuf_unittest::TestAllExtensionsLite,
    ::google::protobuf::internal::PrimitiveTypeTraits< double >, 1, false >
  default_double_extension_lite;
static const int kDefaultBoolExtensionLiteFieldNumber = 73;
extern ::google::protobuf::internal::ExtensionIdentifier< ::protobuf_unittest::TestAllExtensionsLite,
    ::google::protobuf::internal::PrimitiveTypeTraits< bool >, 8, false >
  default_bool_extension_lite;
static const int kDefaultStringExtensionLiteFieldNumber = 74;
extern ::google::protobuf::internal::ExtensionIdentifier< ::protobuf_unittest::TestAllExtensionsLite,
    ::google::protobuf::internal::StringTypeTraits, 9, false >
  default_string_extension_lite;
static const int kDefaultBytesExtensionLiteFieldNumber = 75;
extern ::google::protobuf::internal::ExtensionIdentifier< ::protobuf_unittest::TestAllExtensionsLite,
    ::google::protobuf::internal::StringTypeTraits, 12, false >
  default_bytes_extension_lite;
static const int kDefaultNestedEnumExtensionLiteFieldNumber = 81;
extern ::google::protobuf::internal::ExtensionIdentifier< ::protobuf_unittest::TestAllExtensionsLite,
    ::google::protobuf::internal::EnumTypeTraits< ::protobuf_unittest::TestAllTypesLite_NestedEnum, ::protobuf_unittest::TestAllTypesLite_NestedEnum_IsValid>, 14, false >
  default_nested_enum_extension_lite;
static const int kDefaultForeignEnumExtensionLiteFieldNumber = 82;
extern ::google::protobuf::internal::ExtensionIdentifier< ::protobuf_unittest::TestAllExtensionsLite,
    ::google::protobuf::internal::EnumTypeTraits< ::protobuf_unittest::ForeignEnumLite, ::protobuf_unittest::ForeignEnumLite_IsValid>, 14, false >
  default_foreign_enum_extension_lite;
static const int kDefaultImportEnumExtensionLiteFieldNumber = 83;
extern ::google::protobuf::internal::ExtensionIdentifier< ::protobuf_unittest::TestAllExtensionsLite,
    ::google::protobuf::internal::EnumTypeTraits< ::protobuf_unittest_import::ImportEnumLite, ::protobuf_unittest_import::ImportEnumLite_IsValid>, 14, false >
  default_import_enum_extension_lite;
static const int kDefaultStringPieceExtensionLiteFieldNumber = 84;
extern ::google::protobuf::internal::ExtensionIdentifier< ::protobuf_unittest::TestAllExtensionsLite,
    ::google::protobuf::internal::StringTypeTraits, 9, false >
  default_string_piece_extension_lite;
static const int kDefaultCordExtensionLiteFieldNumber = 85;
extern ::google::protobuf::internal::ExtensionIdentifier< ::protobuf_unittest::TestAllExtensionsLite,
    ::google::protobuf::internal::StringTypeTraits, 9, false >
  default_cord_extension_lite;
static const int kOneofUint32ExtensionLiteFieldNumber = 111;
extern ::google::protobuf::internal::ExtensionIdentifier< ::protobuf_unittest::TestAllExtensionsLite,
    ::google::protobuf::internal::PrimitiveTypeTraits< ::google::protobuf::uint32 >, 13, false >
  oneof_uint32_extension_lite;
static const int kOneofNestedMessageExtensionLiteFieldNumber = 112;
extern ::google::protobuf::internal::ExtensionIdentifier< ::protobuf_unittest::TestAllExtensionsLite,
    ::google::protobuf::internal::MessageTypeTraits< ::protobuf_unittest::TestAllTypesLite_NestedMessage >, 11, false >
  oneof_nested_message_extension_lite;
static const int kOneofStringExtensionLiteFieldNumber = 113;
extern ::google::protobuf::internal::ExtensionIdentifier< ::protobuf_unittest::TestAllExtensionsLite,
    ::google::protobuf::internal::StringTypeTraits, 9, false >
  oneof_string_extension_lite;
static const int kOneofBytesExtensionLiteFieldNumber = 114;
extern ::google::protobuf::internal::ExtensionIdentifier< ::protobuf_unittest::TestAllExtensionsLite,
    ::google::protobuf::internal::StringTypeTraits, 12, false >
  oneof_bytes_extension_lite;
static const int kPackedInt32ExtensionLiteFieldNumber = 90;
extern ::google::protobuf::internal::ExtensionIdentifier< ::protobuf_unittest::TestPackedExtensionsLite,
    ::google::protobuf::internal::RepeatedPrimitiveTypeTraits< ::google::protobuf::int32 >, 5, true >
  packed_int32_extension_lite;
static const int kPackedInt64ExtensionLiteFieldNumber = 91;
extern ::google::protobuf::internal::ExtensionIdentifier< ::protobuf_unittest::TestPackedExtensionsLite,
    ::google::protobuf::internal::RepeatedPrimitiveTypeTraits< ::google::protobuf::int64 >, 3, true >
  packed_int64_extension_lite;
static const int kPackedUint32ExtensionLiteFieldNumber = 92;
extern ::google::protobuf::internal::ExtensionIdentifier< ::protobuf_unittest::TestPackedExtensionsLite,
    ::google::protobuf::internal::RepeatedPrimitiveTypeTraits< ::google::protobuf::uint32 >, 13, true >
  packed_uint32_extension_lite;
static const int kPackedUint64ExtensionLiteFieldNumber = 93;
extern ::google::protobuf::internal::ExtensionIdentifier< ::protobuf_unittest::TestPackedExtensionsLite,
    ::google::protobuf::internal::RepeatedPrimitiveTypeTraits< ::google::protobuf::uint64 >, 4, true >
  packed_uint64_extension_lite;
static const int kPackedSint32ExtensionLiteFieldNumber = 94;
extern ::google::protobuf::internal::ExtensionIdentifier< ::protobuf_unittest::TestPackedExtensionsLite,
    ::google::protobuf::internal::RepeatedPrimitiveTypeTraits< ::google::protobuf::int32 >, 17, true >
  packed_sint32_extension_lite;
static const int kPackedSint64ExtensionLiteFieldNumber = 95;
extern ::google::protobuf::internal::ExtensionIdentifier< ::protobuf_unittest::TestPackedExtensionsLite,
    ::google::protobuf::internal::RepeatedPrimitiveTypeTraits< ::google::protobuf::int64 >, 18, true >
  packed_sint64_extension_lite;
static const int kPackedFixed32ExtensionLiteFieldNumber = 96;
extern ::google::protobuf::internal::ExtensionIdentifier< ::protobuf_unittest::TestPackedExtensionsLite,
    ::google::protobuf::internal::RepeatedPrimitiveTypeTraits< ::google::protobuf::uint32 >, 7, true >
  packed_fixed32_extension_lite;
static const int kPackedFixed64ExtensionLiteFieldNumber = 97;
extern ::google::protobuf::internal::ExtensionIdentifier< ::protobuf_unittest::TestPackedExtensionsLite,
    ::google::protobuf::internal::RepeatedPrimitiveTypeTraits< ::google::protobuf::uint64 >, 6, true >
  packed_fixed64_extension_lite;
static const int kPackedSfixed32ExtensionLiteFieldNumber = 98;
extern ::google::protobuf::internal::ExtensionIdentifier< ::protobuf_unittest::TestPackedExtensionsLite,
    ::google::protobuf::internal::RepeatedPrimitiveTypeTraits< ::google::protobuf::int32 >, 15, true >
  packed_sfixed32_extension_lite;
static const int kPackedSfixed64ExtensionLiteFieldNumber = 99;
extern ::google::protobuf::internal::ExtensionIdentifier< ::protobuf_unittest::TestPackedExtensionsLite,
    ::google::protobuf::internal::RepeatedPrimitiveTypeTraits< ::google::protobuf::int64 >, 16, true >
  packed_sfixed64_extension_lite;
static const int kPackedFloatExtensionLiteFieldNumber = 100;
extern ::google::protobuf::internal::ExtensionIdentifier< ::protobuf_unittest::TestPackedExtensionsLite,
    ::google::protobuf::internal::RepeatedPrimitiveTypeTraits< float >, 2, true >
  packed_float_extension_lite;
static const int kPackedDoubleExtensionLiteFieldNumber = 101;
extern ::google::protobuf::internal::ExtensionIdentifier< ::protobuf_unittest::TestPackedExtensionsLite,
    ::google::protobuf::internal::RepeatedPrimitiveTypeTraits< double >, 1, true >
  packed_double_extension_lite;
static const int kPackedBoolExtensionLiteFieldNumber = 102;
extern ::google::protobuf::internal::ExtensionIdentifier< ::protobuf_unittest::TestPackedExtensionsLite,
    ::google::protobuf::internal::RepeatedPrimitiveTypeTraits< bool >, 8, true >
  packed_bool_extension_lite;
static const int kPackedEnumExtensionLiteFieldNumber = 103;
extern ::google::protobuf::internal::ExtensionIdentifier< ::protobuf_unittest::TestPackedExtensionsLite,
    ::google::protobuf::internal::RepeatedEnumTypeTraits< ::protobuf_unittest::ForeignEnumLite, ::protobuf_unittest::ForeignEnumLite_IsValid>, 14, true >
  packed_enum_extension_lite;

// ===================================================================

#if !PROTOBUF_INLINE_NOT_IN_HEADERS
// TestAllTypesLite_NestedMessage

// optional int32 bb = 1;
inline bool TestAllTypesLite_NestedMessage::has_bb() const {
  return (_has_bits_[0] & 0x00000001u) != 0;
}
inline void TestAllTypesLite_NestedMessage::set_has_bb() {
  _has_bits_[0] |= 0x00000001u;
}
inline void TestAllTypesLite_NestedMessage::clear_has_bb() {
  _has_bits_[0] &= ~0x00000001u;
}
inline void TestAllTypesLite_NestedMessage::clear_bb() {
  bb_ = 0;
  clear_has_bb();
}
inline ::google::protobuf::int32 TestAllTypesLite_NestedMessage::bb() const {
  // @@protoc_insertion_point(field_get:protobuf_unittest.TestAllTypesLite.NestedMessage.bb)
  return bb_;
}
inline void TestAllTypesLite_NestedMessage::set_bb(::google::protobuf::int32 value) {
  set_has_bb();
  bb_ = value;
  // @@protoc_insertion_point(field_set:protobuf_unittest.TestAllTypesLite.NestedMessage.bb)
}

// optional int64 cc = 2;
inline bool TestAllTypesLite_NestedMessage::has_cc() const {
  return (_has_bits_[0] & 0x00000002u) != 0;
}
inline void TestAllTypesLite_NestedMessage::set_has_cc() {
  _has_bits_[0] |= 0x00000002u;
}
inline void TestAllTypesLite_NestedMessage::clear_has_cc() {
  _has_bits_[0] &= ~0x00000002u;
}
inline void TestAllTypesLite_NestedMessage::clear_cc() {
  cc_ = GOOGLE_LONGLONG(0);
  clear_has_cc();
}
inline ::google::protobuf::int64 TestAllTypesLite_NestedMessage::cc() const {
  // @@protoc_insertion_point(field_get:protobuf_unittest.TestAllTypesLite.NestedMessage.cc)
  return cc_;
}
inline void TestAllTypesLite_NestedMessage::set_cc(::google::protobuf::int64 value) {
  set_has_cc();
  cc_ = value;
  // @@protoc_insertion_point(field_set:protobuf_unittest.TestAllTypesLite.NestedMessage.cc)
}

inline const TestAllTypesLite_NestedMessage* TestAllTypesLite_NestedMessage::internal_default_instance() {
  return &TestAllTypesLite_NestedMessage_default_instance_.get();
}
// -------------------------------------------------------------------

// TestAllTypesLite_OptionalGroup

// optional int32 a = 17;
inline bool TestAllTypesLite_OptionalGroup::has_a() const {
  return (_has_bits_[0] & 0x00000001u) != 0;
}
inline void TestAllTypesLite_OptionalGroup::set_has_a() {
  _has_bits_[0] |= 0x00000001u;
}
inline void TestAllTypesLite_OptionalGroup::clear_has_a() {
  _has_bits_[0] &= ~0x00000001u;
}
inline void TestAllTypesLite_OptionalGroup::clear_a() {
  a_ = 0;
  clear_has_a();
}
inline ::google::protobuf::int32 TestAllTypesLite_OptionalGroup::a() const {
  // @@protoc_insertion_point(field_get:protobuf_unittest.TestAllTypesLite.OptionalGroup.a)
  return a_;
}
inline void TestAllTypesLite_OptionalGroup::set_a(::google::protobuf::int32 value) {
  set_has_a();
  a_ = value;
  // @@protoc_insertion_point(field_set:protobuf_unittest.TestAllTypesLite.OptionalGroup.a)
}

inline const TestAllTypesLite_OptionalGroup* TestAllTypesLite_OptionalGroup::internal_default_instance() {
  return &TestAllTypesLite_OptionalGroup_default_instance_.get();
}
// -------------------------------------------------------------------

// TestAllTypesLite_RepeatedGroup

// optional int32 a = 47;
inline bool TestAllTypesLite_RepeatedGroup::has_a() const {
  return (_has_bits_[0] & 0x00000001u) != 0;
}
inline void TestAllTypesLite_RepeatedGroup::set_has_a() {
  _has_bits_[0] |= 0x00000001u;
}
inline void TestAllTypesLite_RepeatedGroup::clear_has_a() {
  _has_bits_[0] &= ~0x00000001u;
}
inline void TestAllTypesLite_RepeatedGroup::clear_a() {
  a_ = 0;
  clear_has_a();
}
inline ::google::protobuf::int32 TestAllTypesLite_RepeatedGroup::a() const {
  // @@protoc_insertion_point(field_get:protobuf_unittest.TestAllTypesLite.RepeatedGroup.a)
  return a_;
}
inline void TestAllTypesLite_RepeatedGroup::set_a(::google::protobuf::int32 value) {
  set_has_a();
  a_ = value;
  // @@protoc_insertion_point(field_set:protobuf_unittest.TestAllTypesLite.RepeatedGroup.a)
}

inline const TestAllTypesLite_RepeatedGroup* TestAllTypesLite_RepeatedGroup::internal_default_instance() {
  return &TestAllTypesLite_RepeatedGroup_default_instance_.get();
}
// -------------------------------------------------------------------

// TestAllTypesLite

// optional int32 optional_int32 = 1;
inline bool TestAllTypesLite::has_optional_int32() const {
  return (_has_bits_[0] & 0x00000001u) != 0;
}
inline void TestAllTypesLite::set_has_optional_int32() {
  _has_bits_[0] |= 0x00000001u;
}
inline void TestAllTypesLite::clear_has_optional_int32() {
  _has_bits_[0] &= ~0x00000001u;
}
inline void TestAllTypesLite::clear_optional_int32() {
  optional_int32_ = 0;
  clear_has_optional_int32();
}
inline ::google::protobuf::int32 TestAllTypesLite::optional_int32() const {
  // @@protoc_insertion_point(field_get:protobuf_unittest.TestAllTypesLite.optional_int32)
  return optional_int32_;
}
inline void TestAllTypesLite::set_optional_int32(::google::protobuf::int32 value) {
  set_has_optional_int32();
  optional_int32_ = value;
  // @@protoc_insertion_point(field_set:protobuf_unittest.TestAllTypesLite.optional_int32)
}

// optional int64 optional_int64 = 2;
inline bool TestAllTypesLite::has_optional_int64() const {
  return (_has_bits_[0] & 0x00000002u) != 0;
}
inline void TestAllTypesLite::set_has_optional_int64() {
  _has_bits_[0] |= 0x00000002u;
}
inline void TestAllTypesLite::clear_has_optional_int64() {
  _has_bits_[0] &= ~0x00000002u;
}
inline void TestAllTypesLite::clear_optional_int64() {
  optional_int64_ = GOOGLE_LONGLONG(0);
  clear_has_optional_int64();
}
inline ::google::protobuf::int64 TestAllTypesLite::optional_int64() const {
  // @@protoc_insertion_point(field_get:protobuf_unittest.TestAllTypesLite.optional_int64)
  return optional_int64_;
}
inline void TestAllTypesLite::set_optional_int64(::google::protobuf::int64 value) {
  set_has_optional_int64();
  optional_int64_ = value;
  // @@protoc_insertion_point(field_set:protobuf_unittest.TestAllTypesLite.optional_int64)
}

// optional uint32 optional_uint32 = 3;
inline bool TestAllTypesLite::has_optional_uint32() const {
  return (_has_bits_[0] & 0x00000004u) != 0;
}
inline void TestAllTypesLite::set_has_optional_uint32() {
  _has_bits_[0] |= 0x00000004u;
}
inline void TestAllTypesLite::clear_has_optional_uint32() {
  _has_bits_[0] &= ~0x00000004u;
}
inline void TestAllTypesLite::clear_optional_uint32() {
  optional_uint32_ = 0u;
  clear_has_optional_uint32();
}
inline ::google::protobuf::uint32 TestAllTypesLite::optional_uint32() const {
  // @@protoc_insertion_point(field_get:protobuf_unittest.TestAllTypesLite.optional_uint32)
  return optional_uint32_;
}
inline void TestAllTypesLite::set_optional_uint32(::google::protobuf::uint32 value) {
  set_has_optional_uint32();
  optional_uint32_ = value;
  // @@protoc_insertion_point(field_set:protobuf_unittest.TestAllTypesLite.optional_uint32)
}

// optional uint64 optional_uint64 = 4;
inline bool TestAllTypesLite::has_optional_uint64() const {
  return (_has_bits_[0] & 0x00000008u) != 0;
}
inline void TestAllTypesLite::set_has_optional_uint64() {
  _has_bits_[0] |= 0x00000008u;
}
inline void TestAllTypesLite::clear_has_optional_uint64() {
  _has_bits_[0] &= ~0x00000008u;
}
inline void TestAllTypesLite::clear_optional_uint64() {
  optional_uint64_ = GOOGLE_ULONGLONG(0);
  clear_has_optional_uint64();
}
inline ::google::protobuf::uint64 TestAllTypesLite::optional_uint64() const {
  // @@protoc_insertion_point(field_get:protobuf_unittest.TestAllTypesLite.optional_uint64)
  return optional_uint64_;
}
inline void TestAllTypesLite::set_optional_uint64(::google::protobuf::uint64 value) {
  set_has_optional_uint64();
  optional_uint64_ = value;
  // @@protoc_insertion_point(field_set:protobuf_unittest.TestAllTypesLite.optional_uint64)
}

// optional sint32 optional_sint32 = 5;
inline bool TestAllTypesLite::has_optional_sint32() const {
  return (_has_bits_[0] & 0x00000010u) != 0;
}
inline void TestAllTypesLite::set_has_optional_sint32() {
  _has_bits_[0] |= 0x00000010u;
}
inline void TestAllTypesLite::clear_has_optional_sint32() {
  _has_bits_[0] &= ~0x00000010u;
}
inline void TestAllTypesLite::clear_optional_sint32() {
  optional_sint32_ = 0;
  clear_has_optional_sint32();
}
inline ::google::protobuf::int32 TestAllTypesLite::optional_sint32() const {
  // @@protoc_insertion_point(field_get:protobuf_unittest.TestAllTypesLite.optional_sint32)
  return optional_sint32_;
}
inline void TestAllTypesLite::set_optional_sint32(::google::protobuf::int32 value) {
  set_has_optional_sint32();
  optional_sint32_ = value;
  // @@protoc_insertion_point(field_set:protobuf_unittest.TestAllTypesLite.optional_sint32)
}

// optional sint64 optional_sint64 = 6;
inline bool TestAllTypesLite::has_optional_sint64() const {
  return (_has_bits_[0] & 0x00000020u) != 0;
}
inline void TestAllTypesLite::set_has_optional_sint64() {
  _has_bits_[0] |= 0x00000020u;
}
inline void TestAllTypesLite::clear_has_optional_sint64() {
  _has_bits_[0] &= ~0x00000020u;
}
inline void TestAllTypesLite::clear_optional_sint64() {
  optional_sint64_ = GOOGLE_LONGLONG(0);
  clear_has_optional_sint64();
}
inline ::google::protobuf::int64 TestAllTypesLite::optional_sint64() const {
  // @@protoc_insertion_point(field_get:protobuf_unittest.TestAllTypesLite.optional_sint64)
  return optional_sint64_;
}
inline void TestAllTypesLite::set_optional_sint64(::google::protobuf::int64 value) {
  set_has_optional_sint64();
  optional_sint64_ = value;
  // @@protoc_insertion_point(field_set:protobuf_unittest.TestAllTypesLite.optional_sint64)
}

// optional fixed32 optional_fixed32 = 7;
inline bool TestAllTypesLite::has_optional_fixed32() const {
  return (_has_bits_[0] & 0x00000040u) != 0;
}
inline void TestAllTypesLite::set_has_optional_fixed32() {
  _has_bits_[0] |= 0x00000040u;
}
inline void TestAllTypesLite::clear_has_optional_fixed32() {
  _has_bits_[0] &= ~0x00000040u;
}
inline void TestAllTypesLite::clear_optional_fixed32() {
  optional_fixed32_ = 0u;
  clear_has_optional_fixed32();
}
inline ::google::protobuf::uint32 TestAllTypesLite::optional_fixed32() const {
  // @@protoc_insertion_point(field_get:protobuf_unittest.TestAllTypesLite.optional_fixed32)
  return optional_fixed32_;
}
inline void TestAllTypesLite::set_optional_fixed32(::google::protobuf::uint32 value) {
  set_has_optional_fixed32();
  optional_fixed32_ = value;
  // @@protoc_insertion_point(field_set:protobuf_unittest.TestAllTypesLite.optional_fixed32)
}

// optional fixed64 optional_fixed64 = 8;
inline bool TestAllTypesLite::has_optional_fixed64() const {
  return (_has_bits_[0] & 0x00000080u) != 0;
}
inline void TestAllTypesLite::set_has_optional_fixed64() {
  _has_bits_[0] |= 0x00000080u;
}
inline void TestAllTypesLite::clear_has_optional_fixed64() {
  _has_bits_[0] &= ~0x00000080u;
}
inline void TestAllTypesLite::clear_optional_fixed64() {
  optional_fixed64_ = GOOGLE_ULONGLONG(0);
  clear_has_optional_fixed64();
}
inline ::google::protobuf::uint64 TestAllTypesLite::optional_fixed64() const {
  // @@protoc_insertion_point(field_get:protobuf_unittest.TestAllTypesLite.optional_fixed64)
  return optional_fixed64_;
}
inline void TestAllTypesLite::set_optional_fixed64(::google::protobuf::uint64 value) {
  set_has_optional_fixed64();
  optional_fixed64_ = value;
  // @@protoc_insertion_point(field_set:protobuf_unittest.TestAllTypesLite.optional_fixed64)
}

// optional sfixed32 optional_sfixed32 = 9;
inline bool TestAllTypesLite::has_optional_sfixed32() const {
  return (_has_bits_[0] & 0x00000100u) != 0;
}
inline void TestAllTypesLite::set_has_optional_sfixed32() {
  _has_bits_[0] |= 0x00000100u;
}
inline void TestAllTypesLite::clear_has_optional_sfixed32() {
  _has_bits_[0] &= ~0x00000100u;
}
inline void TestAllTypesLite::clear_optional_sfixed32() {
  optional_sfixed32_ = 0;
  clear_has_optional_sfixed32();
}
inline ::google::protobuf::int32 TestAllTypesLite::optional_sfixed32() const {
  // @@protoc_insertion_point(field_get:protobuf_unittest.TestAllTypesLite.optional_sfixed32)
  return optional_sfixed32_;
}
inline void TestAllTypesLite::set_optional_sfixed32(::google::protobuf::int32 value) {
  set_has_optional_sfixed32();
  optional_sfixed32_ = value;
  // @@protoc_insertion_point(field_set:protobuf_unittest.TestAllTypesLite.optional_sfixed32)
}

// optional sfixed64 optional_sfixed64 = 10;
inline bool TestAllTypesLite::has_optional_sfixed64() const {
  return (_has_bits_[0] & 0x00000200u) != 0;
}
inline void TestAllTypesLite::set_has_optional_sfixed64() {
  _has_bits_[0] |= 0x00000200u;
}
inline void TestAllTypesLite::clear_has_optional_sfixed64() {
  _has_bits_[0] &= ~0x00000200u;
}
inline void TestAllTypesLite::clear_optional_sfixed64() {
  optional_sfixed64_ = GOOGLE_LONGLONG(0);
  clear_has_optional_sfixed64();
}
inline ::google::protobuf::int64 TestAllTypesLite::optional_sfixed64() const {
  // @@protoc_insertion_point(field_get:protobuf_unittest.TestAllTypesLite.optional_sfixed64)
  return optional_sfixed64_;
}
inline void TestAllTypesLite::set_optional_sfixed64(::google::protobuf::int64 value) {
  set_has_optional_sfixed64();
  optional_sfixed64_ = value;
  // @@protoc_insertion_point(field_set:protobuf_unittest.TestAllTypesLite.optional_sfixed64)
}

// optional float optional_float = 11;
inline bool TestAllTypesLite::has_optional_float() const {
  return (_has_bits_[0] & 0x00000400u) != 0;
}
inline void TestAllTypesLite::set_has_optional_float() {
  _has_bits_[0] |= 0x00000400u;
}
inline void TestAllTypesLite::clear_has_optional_float() {
  _has_bits_[0] &= ~0x00000400u;
}
inline void TestAllTypesLite::clear_optional_float() {
  optional_float_ = 0;
  clear_has_optional_float();
}
inline float TestAllTypesLite::optional_float() const {
  // @@protoc_insertion_point(field_get:protobuf_unittest.TestAllTypesLite.optional_float)
  return optional_float_;
}
inline void TestAllTypesLite::set_optional_float(float value) {
  set_has_optional_float();
  optional_float_ = value;
  // @@protoc_insertion_point(field_set:protobuf_unittest.TestAllTypesLite.optional_float)
}

// optional double optional_double = 12;
inline bool TestAllTypesLite::has_optional_double() const {
  return (_has_bits_[0] & 0x00000800u) != 0;
}
inline void TestAllTypesLite::set_has_optional_double() {
  _has_bits_[0] |= 0x00000800u;
}
inline void TestAllTypesLite::clear_has_optional_double() {
  _has_bits_[0] &= ~0x00000800u;
}
inline void TestAllTypesLite::clear_optional_double() {
  optional_double_ = 0;
  clear_has_optional_double();
}
inline double TestAllTypesLite::optional_double() const {
  // @@protoc_insertion_point(field_get:protobuf_unittest.TestAllTypesLite.optional_double)
  return optional_double_;
}
inline void TestAllTypesLite::set_optional_double(double value) {
  set_has_optional_double();
  optional_double_ = value;
  // @@protoc_insertion_point(field_set:protobuf_unittest.TestAllTypesLite.optional_double)
}

// optional bool optional_bool = 13;
inline bool TestAllTypesLite::has_optional_bool() const {
  return (_has_bits_[0] & 0x00001000u) != 0;
}
inline void TestAllTypesLite::set_has_optional_bool() {
  _has_bits_[0] |= 0x00001000u;
}
inline void TestAllTypesLite::clear_has_optional_bool() {
  _has_bits_[0] &= ~0x00001000u;
}
inline void TestAllTypesLite::clear_optional_bool() {
  optional_bool_ = false;
  clear_has_optional_bool();
}
inline bool TestAllTypesLite::optional_bool() const {
  // @@protoc_insertion_point(field_get:protobuf_unittest.TestAllTypesLite.optional_bool)
  return optional_bool_;
}
inline void TestAllTypesLite::set_optional_bool(bool value) {
  set_has_optional_bool();
  optional_bool_ = value;
  // @@protoc_insertion_point(field_set:protobuf_unittest.TestAllTypesLite.optional_bool)
}

// optional string optional_string = 14;
inline bool TestAllTypesLite::has_optional_string() const {
  return (_has_bits_[0] & 0x00002000u) != 0;
}
inline void TestAllTypesLite::set_has_optional_string() {
  _has_bits_[0] |= 0x00002000u;
}
inline void TestAllTypesLite::clear_has_optional_string() {
  _has_bits_[0] &= ~0x00002000u;
}
inline void TestAllTypesLite::clear_optional_string() {
  optional_string_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  clear_has_optional_string();
}
inline const ::std::string& TestAllTypesLite::optional_string() const {
  // @@protoc_insertion_point(field_get:protobuf_unittest.TestAllTypesLite.optional_string)
  return optional_string_.GetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void TestAllTypesLite::set_optional_string(const ::std::string& value) {
  set_has_optional_string();
  optional_string_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:protobuf_unittest.TestAllTypesLite.optional_string)
}
inline void TestAllTypesLite::set_optional_string(const char* value) {
  set_has_optional_string();
  optional_string_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:protobuf_unittest.TestAllTypesLite.optional_string)
}
inline void TestAllTypesLite::set_optional_string(const char* value, size_t size) {
  set_has_optional_string();
  optional_string_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:protobuf_unittest.TestAllTypesLite.optional_string)
}
inline ::std::string* TestAllTypesLite::mutable_optional_string() {
  set_has_optional_string();
  // @@protoc_insertion_point(field_mutable:protobuf_unittest.TestAllTypesLite.optional_string)
  return optional_string_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline ::std::string* TestAllTypesLite::release_optional_string() {
  // @@protoc_insertion_point(field_release:protobuf_unittest.TestAllTypesLite.optional_string)
  clear_has_optional_string();
  return optional_string_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void TestAllTypesLite::set_allocated_optional_string(::std::string* optional_string) {
  if (optional_string != NULL) {
    set_has_optional_string();
  } else {
    clear_has_optional_string();
  }
  optional_string_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), optional_string);
  // @@protoc_insertion_point(field_set_allocated:protobuf_unittest.TestAllTypesLite.optional_string)
}

// optional bytes optional_bytes = 15;
inline bool TestAllTypesLite::has_optional_bytes() const {
  return (_has_bits_[0] & 0x00004000u) != 0;
}
inline void TestAllTypesLite::set_has_optional_bytes() {
  _has_bits_[0] |= 0x00004000u;
}
inline void TestAllTypesLite::clear_has_optional_bytes() {
  _has_bits_[0] &= ~0x00004000u;
}
inline void TestAllTypesLite::clear_optional_bytes() {
  optional_bytes_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  clear_has_optional_bytes();
}
inline const ::std::string& TestAllTypesLite::optional_bytes() const {
  // @@protoc_insertion_point(field_get:protobuf_unittest.TestAllTypesLite.optional_bytes)
  return optional_bytes_.GetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void TestAllTypesLite::set_optional_bytes(const ::std::string& value) {
  set_has_optional_bytes();
  optional_bytes_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:protobuf_unittest.TestAllTypesLite.optional_bytes)
}
inline void TestAllTypesLite::set_optional_bytes(const char* value) {
  set_has_optional_bytes();
  optional_bytes_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:protobuf_unittest.TestAllTypesLite.optional_bytes)
}
inline void TestAllTypesLite::set_optional_bytes(const void* value, size_t size) {
  set_has_optional_bytes();
  optional_bytes_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:protobuf_unittest.TestAllTypesLite.optional_bytes)
}
inline ::std::string* TestAllTypesLite::mutable_optional_bytes() {
  set_has_optional_bytes();
  // @@protoc_insertion_point(field_mutable:protobuf_unittest.TestAllTypesLite.optional_bytes)
  return optional_bytes_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline ::std::string* TestAllTypesLite::release_optional_bytes() {
  // @@protoc_insertion_point(field_release:protobuf_unittest.TestAllTypesLite.optional_bytes)
  clear_has_optional_bytes();
  return optional_bytes_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void TestAllTypesLite::set_allocated_optional_bytes(::std::string* optional_bytes) {
  if (optional_bytes != NULL) {
    set_has_optional_bytes();
  } else {
    clear_has_optional_bytes();
  }
  optional_bytes_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), optional_bytes);
  // @@protoc_insertion_point(field_set_allocated:protobuf_unittest.TestAllTypesLite.optional_bytes)
}

// optional group OptionalGroup = 16 { ... };
inline bool TestAllTypesLite::has_optionalgroup() const {
  return (_has_bits_[0] & 0x00008000u) != 0;
}
inline void TestAllTypesLite::set_has_optionalgroup() {
  _has_bits_[0] |= 0x00008000u;
}
inline void TestAllTypesLite::clear_has_optionalgroup() {
  _has_bits_[0] &= ~0x00008000u;
}
inline void TestAllTypesLite::clear_optionalgroup() {
  if (optionalgroup_ != NULL) optionalgroup_->::protobuf_unittest::TestAllTypesLite_OptionalGroup::Clear();
  clear_has_optionalgroup();
}
inline const ::protobuf_unittest::TestAllTypesLite_OptionalGroup& TestAllTypesLite::optionalgroup() const {
  // @@protoc_insertion_point(field_get:protobuf_unittest.TestAllTypesLite.optionalgroup)
  return optionalgroup_ != NULL ? *optionalgroup_
                         : *::protobuf_unittest::TestAllTypesLite_OptionalGroup::internal_default_instance();
}
inline ::protobuf_unittest::TestAllTypesLite_OptionalGroup* TestAllTypesLite::mutable_optionalgroup() {
  set_has_optionalgroup();
  if (optionalgroup_ == NULL) {
    optionalgroup_ = new ::protobuf_unittest::TestAllTypesLite_OptionalGroup;
  }
  // @@protoc_insertion_point(field_mutable:protobuf_unittest.TestAllTypesLite.optionalgroup)
  return optionalgroup_;
}
inline ::protobuf_unittest::TestAllTypesLite_OptionalGroup* TestAllTypesLite::release_optionalgroup() {
  // @@protoc_insertion_point(field_release:protobuf_unittest.TestAllTypesLite.optionalgroup)
  clear_has_optionalgroup();
  ::protobuf_unittest::TestAllTypesLite_OptionalGroup* temp = optionalgroup_;
  optionalgroup_ = NULL;
  return temp;
}
inline void TestAllTypesLite::set_allocated_optionalgroup(::protobuf_unittest::TestAllTypesLite_OptionalGroup* optionalgroup) {
  delete optionalgroup_;
  optionalgroup_ = optionalgroup;
  if (optionalgroup) {
    set_has_optionalgroup();
  } else {
    clear_has_optionalgroup();
  }
  // @@protoc_insertion_point(field_set_allocated:protobuf_unittest.TestAllTypesLite.optionalgroup)
}

// optional .protobuf_unittest.TestAllTypesLite.NestedMessage optional_nested_message = 18;
inline bool TestAllTypesLite::has_optional_nested_message() const {
  return (_has_bits_[0] & 0x00010000u) != 0;
}
inline void TestAllTypesLite::set_has_optional_nested_message() {
  _has_bits_[0] |= 0x00010000u;
}
inline void TestAllTypesLite::clear_has_optional_nested_message() {
  _has_bits_[0] &= ~0x00010000u;
}
inline void TestAllTypesLite::clear_optional_nested_message() {
  if (optional_nested_message_ != NULL) optional_nested_message_->::protobuf_unittest::TestAllTypesLite_NestedMessage::Clear();
  clear_has_optional_nested_message();
}
inline const ::protobuf_unittest::TestAllTypesLite_NestedMessage& TestAllTypesLite::optional_nested_message() const {
  // @@protoc_insertion_point(field_get:protobuf_unittest.TestAllTypesLite.optional_nested_message)
  return optional_nested_message_ != NULL ? *optional_nested_message_
                         : *::protobuf_unittest::TestAllTypesLite_NestedMessage::internal_default_instance();
}
inline ::protobuf_unittest::TestAllTypesLite_NestedMessage* TestAllTypesLite::mutable_optional_nested_message() {
  set_has_optional_nested_message();
  if (optional_nested_message_ == NULL) {
    optional_nested_message_ = new ::protobuf_unittest::TestAllTypesLite_NestedMessage;
  }
  // @@protoc_insertion_point(field_mutable:protobuf_unittest.TestAllTypesLite.optional_nested_message)
  return optional_nested_message_;
}
inline ::protobuf_unittest::TestAllTypesLite_NestedMessage* TestAllTypesLite::release_optional_nested_message() {
  // @@protoc_insertion_point(field_release:protobuf_unittest.TestAllTypesLite.optional_nested_message)
  clear_has_optional_nested_message();
  ::protobuf_unittest::TestAllTypesLite_NestedMessage* temp = optional_nested_message_;
  optional_nested_message_ = NULL;
  return temp;
}
inline void TestAllTypesLite::set_allocated_optional_nested_message(::protobuf_unittest::TestAllTypesLite_NestedMessage* optional_nested_message) {
  delete optional_nested_message_;
  optional_nested_message_ = optional_nested_message;
  if (optional_nested_message) {
    set_has_optional_nested_message();
  } else {
    clear_has_optional_nested_message();
  }
  // @@protoc_insertion_point(field_set_allocated:protobuf_unittest.TestAllTypesLite.optional_nested_message)
}

// optional .protobuf_unittest.ForeignMessageLite optional_foreign_message = 19;
inline bool TestAllTypesLite::has_optional_foreign_message() const {
  return (_has_bits_[0] & 0x00020000u) != 0;
}
inline void TestAllTypesLite::set_has_optional_foreign_message() {
  _has_bits_[0] |= 0x00020000u;
}
inline void TestAllTypesLite::clear_has_optional_foreign_message() {
  _has_bits_[0] &= ~0x00020000u;
}
inline void TestAllTypesLite::clear_optional_foreign_message() {
  if (optional_foreign_message_ != NULL) optional_foreign_message_->::protobuf_unittest::ForeignMessageLite::Clear();
  clear_has_optional_foreign_message();
}
inline const ::protobuf_unittest::ForeignMessageLite& TestAllTypesLite::optional_foreign_message() const {
  // @@protoc_insertion_point(field_get:protobuf_unittest.TestAllTypesLite.optional_foreign_message)
  return optional_foreign_message_ != NULL ? *optional_foreign_message_
                         : *::protobuf_unittest::ForeignMessageLite::internal_default_instance();
}
inline ::protobuf_unittest::ForeignMessageLite* TestAllTypesLite::mutable_optional_foreign_message() {
  set_has_optional_foreign_message();
  if (optional_foreign_message_ == NULL) {
    optional_foreign_message_ = new ::protobuf_unittest::ForeignMessageLite;
  }
  // @@protoc_insertion_point(field_mutable:protobuf_unittest.TestAllTypesLite.optional_foreign_message)
  return optional_foreign_message_;
}
inline ::protobuf_unittest::ForeignMessageLite* TestAllTypesLite::release_optional_foreign_message() {
  // @@protoc_insertion_point(field_release:protobuf_unittest.TestAllTypesLite.optional_foreign_message)
  clear_has_optional_foreign_message();
  ::protobuf_unittest::ForeignMessageLite* temp = optional_foreign_message_;
  optional_foreign_message_ = NULL;
  return temp;
}
inline void TestAllTypesLite::set_allocated_optional_foreign_message(::protobuf_unittest::ForeignMessageLite* optional_foreign_message) {
  delete optional_foreign_message_;
  optional_foreign_message_ = optional_foreign_message;
  if (optional_foreign_message) {
    set_has_optional_foreign_message();
  } else {
    clear_has_optional_foreign_message();
  }
  // @@protoc_insertion_point(field_set_allocated:protobuf_unittest.TestAllTypesLite.optional_foreign_message)
}

// optional .protobuf_unittest_import.ImportMessageLite optional_import_message = 20;
inline bool TestAllTypesLite::has_optional_import_message() const {
  return (_has_bits_[0] & 0x00040000u) != 0;
}
inline void TestAllTypesLite::set_has_optional_import_message() {
  _has_bits_[0] |= 0x00040000u;
}
inline void TestAllTypesLite::clear_has_optional_import_message() {
  _has_bits_[0] &= ~0x00040000u;
}
inline void TestAllTypesLite::clear_optional_import_message() {
  if (optional_import_message_ != NULL) optional_import_message_->::protobuf_unittest_import::ImportMessageLite::Clear();
  clear_has_optional_import_message();
}
inline const ::protobuf_unittest_import::ImportMessageLite& TestAllTypesLite::optional_import_message() const {
  // @@protoc_insertion_point(field_get:protobuf_unittest.TestAllTypesLite.optional_import_message)
  return optional_import_message_ != NULL ? *optional_import_message_
                         : *::protobuf_unittest_import::ImportMessageLite::internal_default_instance();
}
inline ::protobuf_unittest_import::ImportMessageLite* TestAllTypesLite::mutable_optional_import_message() {
  set_has_optional_import_message();
  if (optional_import_message_ == NULL) {
    optional_import_message_ = new ::protobuf_unittest_import::ImportMessageLite;
  }
  // @@protoc_insertion_point(field_mutable:protobuf_unittest.TestAllTypesLite.optional_import_message)
  return optional_import_message_;
}
inline ::protobuf_unittest_import::ImportMessageLite* TestAllTypesLite::release_optional_import_message() {
  // @@protoc_insertion_point(field_release:protobuf_unittest.TestAllTypesLite.optional_import_message)
  clear_has_optional_import_message();
  ::protobuf_unittest_import::ImportMessageLite* temp = optional_import_message_;
  optional_import_message_ = NULL;
  return temp;
}
inline void TestAllTypesLite::set_allocated_optional_import_message(::protobuf_unittest_import::ImportMessageLite* optional_import_message) {
  delete optional_import_message_;
  optional_import_message_ = optional_import_message;
  if (optional_import_message) {
    set_has_optional_import_message();
  } else {
    clear_has_optional_import_message();
  }
  // @@protoc_insertion_point(field_set_allocated:protobuf_unittest.TestAllTypesLite.optional_import_message)
}

// optional .protobuf_unittest.TestAllTypesLite.NestedEnum optional_nested_enum = 21;
inline bool TestAllTypesLite::has_optional_nested_enum() const {
  return (_has_bits_[0] & 0x00080000u) != 0;
}
inline void TestAllTypesLite::set_has_optional_nested_enum() {
  _has_bits_[0] |= 0x00080000u;
}
inline void TestAllTypesLite::clear_has_optional_nested_enum() {
  _has_bits_[0] &= ~0x00080000u;
}
inline void TestAllTypesLite::clear_optional_nested_enum() {
  optional_nested_enum_ = 1;
  clear_has_optional_nested_enum();
}
inline ::protobuf_unittest::TestAllTypesLite_NestedEnum TestAllTypesLite::optional_nested_enum() const {
  // @@protoc_insertion_point(field_get:protobuf_unittest.TestAllTypesLite.optional_nested_enum)
  return static_cast< ::protobuf_unittest::TestAllTypesLite_NestedEnum >(optional_nested_enum_);
}
inline void TestAllTypesLite::set_optional_nested_enum(::protobuf_unittest::TestAllTypesLite_NestedEnum value) {
  assert(::protobuf_unittest::TestAllTypesLite_NestedEnum_IsValid(value));
  set_has_optional_nested_enum();
  optional_nested_enum_ = value;
  // @@protoc_insertion_point(field_set:protobuf_unittest.TestAllTypesLite.optional_nested_enum)
}

// optional .protobuf_unittest.ForeignEnumLite optional_foreign_enum = 22;
inline bool TestAllTypesLite::has_optional_foreign_enum() const {
  return (_has_bits_[0] & 0x00100000u) != 0;
}
inline void TestAllTypesLite::set_has_optional_foreign_enum() {
  _has_bits_[0] |= 0x00100000u;
}
inline void TestAllTypesLite::clear_has_optional_foreign_enum() {
  _has_bits_[0] &= ~0x00100000u;
}
inline void TestAllTypesLite::clear_optional_foreign_enum() {
  optional_foreign_enum_ = 4;
  clear_has_optional_foreign_enum();
}
inline ::protobuf_unittest::ForeignEnumLite TestAllTypesLite::optional_foreign_enum() const {
  // @@protoc_insertion_point(field_get:protobuf_unittest.TestAllTypesLite.optional_foreign_enum)
  return static_cast< ::protobuf_unittest::ForeignEnumLite >(optional_foreign_enum_);
}
inline void TestAllTypesLite::set_optional_foreign_enum(::protobuf_unittest::ForeignEnumLite value) {
  assert(::protobuf_unittest::ForeignEnumLite_IsValid(value));
  set_has_optional_foreign_enum();
  optional_foreign_enum_ = value;
  // @@protoc_insertion_point(field_set:protobuf_unittest.TestAllTypesLite.optional_foreign_enum)
}

// optional .protobuf_unittest_import.ImportEnumLite optional_import_enum = 23;
inline bool TestAllTypesLite::has_optional_import_enum() const {
  return (_has_bits_[0] & 0x00200000u) != 0;
}
inline void TestAllTypesLite::set_has_optional_import_enum() {
  _has_bits_[0] |= 0x00200000u;
}
inline void TestAllTypesLite::clear_has_optional_import_enum() {
  _has_bits_[0] &= ~0x00200000u;
}
inline void TestAllTypesLite::clear_optional_import_enum() {
  optional_import_enum_ = 7;
  clear_has_optional_import_enum();
}
inline ::protobuf_unittest_import::ImportEnumLite TestAllTypesLite::optional_import_enum() const {
  // @@protoc_insertion_point(field_get:protobuf_unittest.TestAllTypesLite.optional_import_enum)
  return static_cast< ::protobuf_unittest_import::ImportEnumLite >(optional_import_enum_);
}
inline void TestAllTypesLite::set_optional_import_enum(::protobuf_unittest_import::ImportEnumLite value) {
  assert(::protobuf_unittest_import::ImportEnumLite_IsValid(value));
  set_has_optional_import_enum();
  optional_import_enum_ = value;
  // @@protoc_insertion_point(field_set:protobuf_unittest.TestAllTypesLite.optional_import_enum)
}

// optional string optional_string_piece = 24 [ctype = STRING_PIECE];
inline bool TestAllTypesLite::has_optional_string_piece() const {
  return (_has_bits_[0] & 0x00400000u) != 0;
}
inline void TestAllTypesLite::set_has_optional_string_piece() {
  _has_bits_[0] |= 0x00400000u;
}
inline void TestAllTypesLite::clear_has_optional_string_piece() {
  _has_bits_[0] &= ~0x00400000u;
}
inline void TestAllTypesLite::clear_optional_string_piece() {
  optional_string_piece_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  clear_has_optional_string_piece();
}
inline const ::std::string& TestAllTypesLite::optional_string_piece() const {
  // @@protoc_insertion_point(field_get:protobuf_unittest.TestAllTypesLite.optional_string_piece)
  return optional_string_piece_.GetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void TestAllTypesLite::set_optional_string_piece(const ::std::string& value) {
  set_has_optional_string_piece();
  optional_string_piece_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:protobuf_unittest.TestAllTypesLite.optional_string_piece)
}
inline void TestAllTypesLite::set_optional_string_piece(const char* value) {
  set_has_optional_string_piece();
  optional_string_piece_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:protobuf_unittest.TestAllTypesLite.optional_string_piece)
}
inline void TestAllTypesLite::set_optional_string_piece(const char* value, size_t size) {
  set_has_optional_string_piece();
  optional_string_piece_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:protobuf_unittest.TestAllTypesLite.optional_string_piece)
}
inline ::std::string* TestAllTypesLite::mutable_optional_string_piece() {
  set_has_optional_string_piece();
  // @@protoc_insertion_point(field_mutable:protobuf_unittest.TestAllTypesLite.optional_string_piece)
  return optional_string_piece_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline ::std::string* TestAllTypesLite::release_optional_string_piece() {
  // @@protoc_insertion_point(field_release:protobuf_unittest.TestAllTypesLite.optional_string_piece)
  clear_has_optional_string_piece();
  return optional_string_piece_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void TestAllTypesLite::set_allocated_optional_string_piece(::std::string* optional_string_piece) {
  if (optional_string_piece != NULL) {
    set_has_optional_string_piece();
  } else {
    clear_has_optional_string_piece();
  }
  optional_string_piece_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), optional_string_piece);
  // @@protoc_insertion_point(field_set_allocated:protobuf_unittest.TestAllTypesLite.optional_string_piece)
}

// optional string optional_cord = 25 [ctype = CORD];
inline bool TestAllTypesLite::has_optional_cord() const {
  return (_has_bits_[0] & 0x00800000u) != 0;
}
inline void TestAllTypesLite::set_has_optional_cord() {
  _has_bits_[0] |= 0x00800000u;
}
inline void TestAllTypesLite::clear_has_optional_cord() {
  _has_bits_[0] &= ~0x00800000u;
}
inline void TestAllTypesLite::clear_optional_cord() {
  optional_cord_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  clear_has_optional_cord();
}
inline const ::std::string& TestAllTypesLite::optional_cord() const {
  // @@protoc_insertion_point(field_get:protobuf_unittest.TestAllTypesLite.optional_cord)
  return optional_cord_.GetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void TestAllTypesLite::set_optional_cord(const ::std::string& value) {
  set_has_optional_cord();
  optional_cord_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:protobuf_unittest.TestAllTypesLite.optional_cord)
}
inline void TestAllTypesLite::set_optional_cord(const char* value) {
  set_has_optional_cord();
  optional_cord_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:protobuf_unittest.TestAllTypesLite.optional_cord)
}
inline void TestAllTypesLite::set_optional_cord(const char* value, size_t size) {
  set_has_optional_cord();
  optional_cord_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:protobuf_unittest.TestAllTypesLite.optional_cord)
}
inline ::std::string* TestAllTypesLite::mutable_optional_cord() {
  set_has_optional_cord();
  // @@protoc_insertion_point(field_mutable:protobuf_unittest.TestAllTypesLite.optional_cord)
  return optional_cord_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline ::std::string* TestAllTypesLite::release_optional_cord() {
  // @@protoc_insertion_point(field_release:protobuf_unittest.TestAllTypesLite.optional_cord)
  clear_has_optional_cord();
  return optional_cord_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void TestAllTypesLite::set_allocated_optional_cord(::std::string* optional_cord) {
  if (optional_cord != NULL) {
    set_has_optional_cord();
  } else {
    clear_has_optional_cord();
  }
  optional_cord_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), optional_cord);
  // @@protoc_insertion_point(field_set_allocated:protobuf_unittest.TestAllTypesLite.optional_cord)
}

// optional .protobuf_unittest_import.PublicImportMessageLite optional_public_import_message = 26;
inline bool TestAllTypesLite::has_optional_public_import_message() const {
  return (_has_bits_[0] & 0x01000000u) != 0;
}
inline void TestAllTypesLite::set_has_optional_public_import_message() {
  _has_bits_[0] |= 0x01000000u;
}
inline void TestAllTypesLite::clear_has_optional_public_import_message() {
  _has_bits_[0] &= ~0x01000000u;
}
inline void TestAllTypesLite::clear_optional_public_import_message() {
  if (optional_public_import_message_ != NULL) optional_public_import_message_->::protobuf_unittest_import::PublicImportMessageLite::Clear();
  clear_has_optional_public_import_message();
}
inline const ::protobuf_unittest_import::PublicImportMessageLite& TestAllTypesLite::optional_public_import_message() const {
  // @@protoc_insertion_point(field_get:protobuf_unittest.TestAllTypesLite.optional_public_import_message)
  return optional_public_import_message_ != NULL ? *optional_public_import_message_
                         : *::protobuf_unittest_import::PublicImportMessageLite::internal_default_instance();
}
inline ::protobuf_unittest_import::PublicImportMessageLite* TestAllTypesLite::mutable_optional_public_import_message() {
  set_has_optional_public_import_message();
  if (optional_public_import_message_ == NULL) {
    optional_public_import_message_ = new ::protobuf_unittest_import::PublicImportMessageLite;
  }
  // @@protoc_insertion_point(field_mutable:protobuf_unittest.TestAllTypesLite.optional_public_import_message)
  return optional_public_import_message_;
}
inline ::protobuf_unittest_import::PublicImportMessageLite* TestAllTypesLite::release_optional_public_import_message() {
  // @@protoc_insertion_point(field_release:protobuf_unittest.TestAllTypesLite.optional_public_import_message)
  clear_has_optional_public_import_message();
  ::protobuf_unittest_import::PublicImportMessageLite* temp = optional_public_import_message_;
  optional_public_import_message_ = NULL;
  return temp;
}
inline void TestAllTypesLite::set_allocated_optional_public_import_message(::protobuf_unittest_import::PublicImportMessageLite* optional_public_import_message) {
  delete optional_public_import_message_;
  optional_public_import_message_ = optional_public_import_message;
  if (optional_public_import_message) {
    set_has_optional_public_import_message();
  } else {
    clear_has_optional_public_import_message();
  }
  // @@protoc_insertion_point(field_set_allocated:protobuf_unittest.TestAllTypesLite.optional_public_import_message)
}

// optional .protobuf_unittest.TestAllTypesLite.NestedMessage optional_lazy_message = 27 [lazy = true];
inline bool TestAllTypesLite::has_optional_lazy_message() const {
  return (_has_bits_[0] & 0x02000000u) != 0;
}
inline void TestAllTypesLite::set_has_optional_lazy_message() {
  _has_bits_[0] |= 0x02000000u;
}
inline void TestAllTypesLite::clear_has_optional_lazy_message() {
  _has_bits_[0] &= ~0x02000000u;
}
inline void TestAllTypesLite::clear_optional_lazy_message() {
  if (optional_lazy_message_ != NULL) optional_lazy_message_->::protobuf_unittest::TestAllTypesLite_NestedMessage::Clear();
  clear_has_optional_lazy_message();
}
inline const ::protobuf_unittest::TestAllTypesLite_NestedMessage& TestAllTypesLite::optional_lazy_message() const {
  // @@protoc_insertion_point(field_get:protobuf_unittest.TestAllTypesLite.optional_lazy_message)
  return optional_lazy_message_ != NULL ? *optional_lazy_message_
                         : *::protobuf_unittest::TestAllTypesLite_NestedMessage::internal_default_instance();
}
inline ::protobuf_unittest::TestAllTypesLite_NestedMessage* TestAllTypesLite::mutable_optional_lazy_message() {
  set_has_optional_lazy_message();
  if (optional_lazy_message_ == NULL) {
    optional_lazy_message_ = new ::protobuf_unittest::TestAllTypesLite_NestedMessage;
  }
  // @@protoc_insertion_point(field_mutable:protobuf_unittest.TestAllTypesLite.optional_lazy_message)
  return optional_lazy_message_;
}
inline ::protobuf_unittest::TestAllTypesLite_NestedMessage* TestAllTypesLite::release_optional_lazy_message() {
  // @@protoc_insertion_point(field_release:protobuf_unittest.TestAllTypesLite.optional_lazy_message)
  clear_has_optional_lazy_message();
  ::protobuf_unittest::TestAllTypesLite_NestedMessage* temp = optional_lazy_message_;
  optional_lazy_message_ = NULL;
  return temp;
}
inline void TestAllTypesLite::set_allocated_optional_lazy_message(::protobuf_unittest::TestAllTypesLite_NestedMessage* optional_lazy_message) {
  delete optional_lazy_message_;
  optional_lazy_message_ = optional_lazy_message;
  if (optional_lazy_message) {
    set_has_optional_lazy_message();
  } else {
    clear_has_optional_lazy_message();
  }
  // @@protoc_insertion_point(field_set_allocated:protobuf_unittest.TestAllTypesLite.optional_lazy_message)
}

// repeated int32 repeated_int32 = 31;
inline int TestAllTypesLite::repeated_int32_size() const {
  return repeated_int32_.size();
}
inline void TestAllTypesLite::clear_repeated_int32() {
  repeated_int32_.Clear();
}
inline ::google::protobuf::int32 TestAllTypesLite::repeated_int32(int index) const {
  // @@protoc_insertion_point(field_get:protobuf_unittest.TestAllTypesLite.repeated_int32)
  return repeated_int32_.Get(index);
}
inline void TestAllTypesLite::set_repeated_int32(int index, ::google::protobuf::int32 value) {
  repeated_int32_.Set(index, value);
  // @@protoc_insertion_point(field_set:protobuf_unittest.TestAllTypesLite.repeated_int32)
}
inline void TestAllTypesLite::add_repeated_int32(::google::protobuf::int32 value) {
  repeated_int32_.Add(value);
  // @@protoc_insertion_point(field_add:protobuf_unittest.TestAllTypesLite.repeated_int32)
}
inline const ::google::protobuf::RepeatedField< ::google::protobuf::int32 >&
TestAllTypesLite::repeated_int32() const {
  // @@protoc_insertion_point(field_list:protobuf_unittest.TestAllTypesLite.repeated_int32)
  return repeated_int32_;
}
inline ::google::protobuf::RepeatedField< ::google::protobuf::int32 >*
TestAllTypesLite::mutable_repeated_int32() {
  // @@protoc_insertion_point(field_mutable_list:protobuf_unittest.TestAllTypesLite.repeated_int32)
  return &repeated_int32_;
}

// repeated int64 repeated_int64 = 32;
inline int TestAllTypesLite::repeated_int64_size() const {
  return repeated_int64_.size();
}
inline void TestAllTypesLite::clear_repeated_int64() {
  repeated_int64_.Clear();
}
inline ::google::protobuf::int64 TestAllTypesLite::repeated_int64(int index) const {
  // @@protoc_insertion_point(field_get:protobuf_unittest.TestAllTypesLite.repeated_int64)
  return repeated_int64_.Get(index);
}
inline void TestAllTypesLite::set_repeated_int64(int index, ::google::protobuf::int64 value) {
  repeated_int64_.Set(index, value);
  // @@protoc_insertion_point(field_set:protobuf_unittest.TestAllTypesLite.repeated_int64)
}
inline void TestAllTypesLite::add_repeated_int64(::google::protobuf::int64 value) {
  repeated_int64_.Add(value);
  // @@protoc_insertion_point(field_add:protobuf_unittest.TestAllTypesLite.repeated_int64)
}
inline const ::google::protobuf::RepeatedField< ::google::protobuf::int64 >&
TestAllTypesLite::repeated_int64() const {
  // @@protoc_insertion_point(field_list:protobuf_unittest.TestAllTypesLite.repeated_int64)
  return repeated_int64_;
}
inline ::google::protobuf::RepeatedField< ::google::protobuf::int64 >*
TestAllTypesLite::mutable_repeated_int64() {
  // @@protoc_insertion_point(field_mutable_list:protobuf_unittest.TestAllTypesLite.repeated_int64)
  return &repeated_int64_;
}

// repeated uint32 repeated_uint32 = 33;
inline int TestAllTypesLite::repeated_uint32_size() const {
  return repeated_uint32_.size();
}
inline void TestAllTypesLite::clear_repeated_uint32() {
  repeated_uint32_.Clear();
}
inline ::google::protobuf::uint32 TestAllTypesLite::repeated_uint32(int index) const {
  // @@protoc_insertion_point(field_get:protobuf_unittest.TestAllTypesLite.repeated_uint32)
  return repeated_uint32_.Get(index);
}
inline void TestAllTypesLite::set_repeated_uint32(int index, ::google::protobuf::uint32 value) {
  repeated_uint32_.Set(index, value);
  // @@protoc_insertion_point(field_set:protobuf_unittest.TestAllTypesLite.repeated_uint32)
}
inline void TestAllTypesLite::add_repeated_uint32(::google::protobuf::uint32 value) {
  repeated_uint32_.Add(value);
  // @@protoc_insertion_point(field_add:protobuf_unittest.TestAllTypesLite.repeated_uint32)
}
inline const ::google::protobuf::RepeatedField< ::google::protobuf::uint32 >&
TestAllTypesLite::repeated_uint32() const {
  // @@protoc_insertion_point(field_list:protobuf_unittest.TestAllTypesLite.repeated_uint32)
  return repeated_uint32_;
}
inline ::google::protobuf::RepeatedField< ::google::protobuf::uint32 >*
TestAllTypesLite::mutable_repeated_uint32() {
  // @@protoc_insertion_point(field_mutable_list:protobuf_unittest.TestAllTypesLite.repeated_uint32)
  return &repeated_uint32_;
}

// repeated uint64 repeated_uint64 = 34;
inline int TestAllTypesLite::repeated_uint64_size() const {
  return repeated_uint64_.size();
}
inline void TestAllTypesLite::clear_repeated_uint64() {
  repeated_uint64_.Clear();
}
inline ::google::protobuf::uint64 TestAllTypesLite::repeated_uint64(int index) const {
  // @@protoc_insertion_point(field_get:protobuf_unittest.TestAllTypesLite.repeated_uint64)
  return repeated_uint64_.Get(index);
}
inline void TestAllTypesLite::set_repeated_uint64(int index, ::google::protobuf::uint64 value) {
  repeated_uint64_.Set(index, value);
  // @@protoc_insertion_point(field_set:protobuf_unittest.TestAllTypesLite.repeated_uint64)
}
inline void TestAllTypesLite::add_repeated_uint64(::google::protobuf::uint64 value) {
  repeated_uint64_.Add(value);
  // @@protoc_insertion_point(field_add:protobuf_unittest.TestAllTypesLite.repeated_uint64)
}
inline const ::google::protobuf::RepeatedField< ::google::protobuf::uint64 >&
TestAllTypesLite::repeated_uint64() const {
  // @@protoc_insertion_point(field_list:protobuf_unittest.TestAllTypesLite.repeated_uint64)
  return repeated_uint64_;
}
inline ::google::protobuf::RepeatedField< ::google::protobuf::uint64 >*
TestAllTypesLite::mutable_repeated_uint64() {
  // @@protoc_insertion_point(field_mutable_list:protobuf_unittest.TestAllTypesLite.repeated_uint64)
  return &repeated_uint64_;
}

// repeated sint32 repeated_sint32 = 35;
inline int TestAllTypesLite::repeated_sint32_size() const {
  return repeated_sint32_.size();
}
inline void TestAllTypesLite::clear_repeated_sint32() {
  repeated_sint32_.Clear();
}
inline ::google::protobuf::int32 TestAllTypesLite::repeated_sint32(int index) const {
  // @@protoc_insertion_point(field_get:protobuf_unittest.TestAllTypesLite.repeated_sint32)
  return repeated_sint32_.Get(index);
}
inline void TestAllTypesLite::set_repeated_sint32(int index, ::google::protobuf::int32 value) {
  repeated_sint32_.Set(index, value);
  // @@protoc_insertion_point(field_set:protobuf_unittest.TestAllTypesLite.repeated_sint32)
}
inline void TestAllTypesLite::add_repeated_sint32(::google::protobuf::int32 value) {
  repeated_sint32_.Add(value);
  // @@protoc_insertion_point(field_add:protobuf_unittest.TestAllTypesLite.repeated_sint32)
}
inline const ::google::protobuf::RepeatedField< ::google::protobuf::int32 >&
TestAllTypesLite::repeated_sint32() const {
  // @@protoc_insertion_point(field_list:protobuf_unittest.TestAllTypesLite.repeated_sint32)
  return repeated_sint32_;
}
inline ::google::protobuf::RepeatedField< ::google::protobuf::int32 >*
TestAllTypesLite::mutable_repeated_sint32() {
  // @@protoc_insertion_point(field_mutable_list:protobuf_unittest.TestAllTypesLite.repeated_sint32)
  return &repeated_sint32_;
}

// repeated sint64 repeated_sint64 = 36;
inline int TestAllTypesLite::repeated_sint64_size() const {
  return repeated_sint64_.size();
}
inline void TestAllTypesLite::clear_repeated_sint64() {
  repeated_sint64_.Clear();
}
inline ::google::protobuf::int64 TestAllTypesLite::repeated_sint64(int index) const {
  // @@protoc_insertion_point(field_get:protobuf_unittest.TestAllTypesLite.repeated_sint64)
  return repeated_sint64_.Get(index);
}
inline void TestAllTypesLite::set_repeated_sint64(int index, ::google::protobuf::int64 value) {
  repeated_sint64_.Set(index, value);
  // @@protoc_insertion_point(field_set:protobuf_unittest.TestAllTypesLite.repeated_sint64)
}
inline void TestAllTypesLite::add_repeated_sint64(::google::protobuf::int64 value) {
  repeated_sint64_.Add(value);
  // @@protoc_insertion_point(field_add:protobuf_unittest.TestAllTypesLite.repeated_sint64)
}
inline const ::google::protobuf::RepeatedField< ::google::protobuf::int64 >&
TestAllTypesLite::repeated_sint64() const {
  // @@protoc_insertion_point(field_list:protobuf_unittest.TestAllTypesLite.repeated_sint64)
  return repeated_sint64_;
}
inline ::google::protobuf::RepeatedField< ::google::protobuf::int64 >*
TestAllTypesLite::mutable_repeated_sint64() {
  // @@protoc_insertion_point(field_mutable_list:protobuf_unittest.TestAllTypesLite.repeated_sint64)
  return &repeated_sint64_;
}

// repeated fixed32 repeated_fixed32 = 37;
inline int TestAllTypesLite::repeated_fixed32_size() const {
  return repeated_fixed32_.size();
}
inline void TestAllTypesLite::clear_repeated_fixed32() {
  repeated_fixed32_.Clear();
}
inline ::google::protobuf::uint32 TestAllTypesLite::repeated_fixed32(int index) const {
  // @@protoc_insertion_point(field_get:protobuf_unittest.TestAllTypesLite.repeated_fixed32)
  return repeated_fixed32_.Get(index);
}
inline void TestAllTypesLite::set_repeated_fixed32(int index, ::google::protobuf::uint32 value) {
  repeated_fixed32_.Set(index, value);
  // @@protoc_insertion_point(field_set:protobuf_unittest.TestAllTypesLite.repeated_fixed32)
}
inline void TestAllTypesLite::add_repeated_fixed32(::google::protobuf::uint32 value) {
  repeated_fixed32_.Add(value);
  // @@protoc_insertion_point(field_add:protobuf_unittest.TestAllTypesLite.repeated_fixed32)
}
inline const ::google::protobuf::RepeatedField< ::google::protobuf::uint32 >&
TestAllTypesLite::repeated_fixed32() const {
  // @@protoc_insertion_point(field_list:protobuf_unittest.TestAllTypesLite.repeated_fixed32)
  return repeated_fixed32_;
}
inline ::google::protobuf::RepeatedField< ::google::protobuf::uint32 >*
TestAllTypesLite::mutable_repeated_fixed32() {
  // @@protoc_insertion_point(field_mutable_list:protobuf_unittest.TestAllTypesLite.repeated_fixed32)
  return &repeated_fixed32_;
}

// repeated fixed64 repeated_fixed64 = 38;
inline int TestAllTypesLite::repeated_fixed64_size() const {
  return repeated_fixed64_.size();
}
inline void TestAllTypesLite::clear_repeated_fixed64() {
  repeated_fixed64_.Clear();
}
inline ::google::protobuf::uint64 TestAllTypesLite::repeated_fixed64(int index) const {
  // @@protoc_insertion_point(field_get:protobuf_unittest.TestAllTypesLite.repeated_fixed64)
  return repeated_fixed64_.Get(index);
}
inline void TestAllTypesLite::set_repeated_fixed64(int index, ::google::protobuf::uint64 value) {
  repeated_fixed64_.Set(index, value);
  // @@protoc_insertion_point(field_set:protobuf_unittest.TestAllTypesLite.repeated_fixed64)
}
inline void TestAllTypesLite::add_repeated_fixed64(::google::protobuf::uint64 value) {
  repeated_fixed64_.Add(value);
  // @@protoc_insertion_point(field_add:protobuf_unittest.TestAllTypesLite.repeated_fixed64)
}
inline const ::google::protobuf::RepeatedField< ::google::protobuf::uint64 >&
TestAllTypesLite::repeated_fixed64() const {
  // @@protoc_insertion_point(field_list:protobuf_unittest.TestAllTypesLite.repeated_fixed64)
  return repeated_fixed64_;
}
inline ::google::protobuf::RepeatedField< ::google::protobuf::uint64 >*
TestAllTypesLite::mutable_repeated_fixed64() {
  // @@protoc_insertion_point(field_mutable_list:protobuf_unittest.TestAllTypesLite.repeated_fixed64)
  return &repeated_fixed64_;
}

// repeated sfixed32 repeated_sfixed32 = 39;
inline int TestAllTypesLite::repeated_sfixed32_size() const {
  return repeated_sfixed32_.size();
}
inline void TestAllTypesLite::clear_repeated_sfixed32() {
  repeated_sfixed32_.Clear();
}
inline ::google::protobuf::int32 TestAllTypesLite::repeated_sfixed32(int index) const {
  // @@protoc_insertion_point(field_get:protobuf_unittest.TestAllTypesLite.repeated_sfixed32)
  return repeated_sfixed32_.Get(index);
}
inline void TestAllTypesLite::set_repeated_sfixed32(int index, ::google::protobuf::int32 value) {
  repeated_sfixed32_.Set(index, value);
  // @@protoc_insertion_point(field_set:protobuf_unittest.TestAllTypesLite.repeated_sfixed32)
}
inline void TestAllTypesLite::add_repeated_sfixed32(::google::protobuf::int32 value) {
  repeated_sfixed32_.Add(value);
  // @@protoc_insertion_point(field_add:protobuf_unittest.TestAllTypesLite.repeated_sfixed32)
}
inline const ::google::protobuf::RepeatedField< ::google::protobuf::int32 >&
TestAllTypesLite::repeated_sfixed32() const {
  // @@protoc_insertion_point(field_list:protobuf_unittest.TestAllTypesLite.repeated_sfixed32)
  return repeated_sfixed32_;
}
inline ::google::protobuf::RepeatedField< ::google::protobuf::int32 >*
TestAllTypesLite::mutable_repeated_sfixed32() {
  // @@protoc_insertion_point(field_mutable_list:protobuf_unittest.TestAllTypesLite.repeated_sfixed32)
  return &repeated_sfixed32_;
}

// repeated sfixed64 repeated_sfixed64 = 40;
inline int TestAllTypesLite::repeated_sfixed64_size() const {
  return repeated_sfixed64_.size();
}
inline void TestAllTypesLite::clear_repeated_sfixed64() {
  repeated_sfixed64_.Clear();
}
inline ::google::protobuf::int64 TestAllTypesLite::repeated_sfixed64(int index) const {
  // @@protoc_insertion_point(field_get:protobuf_unittest.TestAllTypesLite.repeated_sfixed64)
  return repeated_sfixed64_.Get(index);
}
inline void TestAllTypesLite::set_repeated_sfixed64(int index, ::google::protobuf::int64 value) {
  repeated_sfixed64_.Set(index, value);
  // @@protoc_insertion_point(field_set:protobuf_unittest.TestAllTypesLite.repeated_sfixed64)
}
inline void TestAllTypesLite::add_repeated_sfixed64(::google::protobuf::int64 value) {
  repeated_sfixed64_.Add(value);
  // @@protoc_insertion_point(field_add:protobuf_unittest.TestAllTypesLite.repeated_sfixed64)
}
inline const ::google::protobuf::RepeatedField< ::google::protobuf::int64 >&
TestAllTypesLite::repeated_sfixed64() const {
  // @@protoc_insertion_point(field_list:protobuf_unittest.TestAllTypesLite.repeated_sfixed64)
  return repeated_sfixed64_;
}
inline ::google::protobuf::RepeatedField< ::google::protobuf::int64 >*
TestAllTypesLite::mutable_repeated_sfixed64() {
  // @@protoc_insertion_point(field_mutable_list:protobuf_unittest.TestAllTypesLite.repeated_sfixed64)
  return &repeated_sfixed64_;
}

// repeated float repeated_float = 41;
inline int TestAllTypesLite::repeated_float_size() const {
  return repeated_float_.size();
}
inline void TestAllTypesLite::clear_repeated_float() {
  repeated_float_.Clear();
}
inline float TestAllTypesLite::repeated_float(int index) const {
  // @@protoc_insertion_point(field_get:protobuf_unittest.TestAllTypesLite.repeated_float)
  return repeated_float_.Get(index);
}
inline void TestAllTypesLite::set_repeated_float(int index, float value) {
  repeated_float_.Set(index, value);
  // @@protoc_insertion_point(field_set:protobuf_unittest.TestAllTypesLite.repeated_float)
}
inline void TestAllTypesLite::add_repeated_float(float value) {
  repeated_float_.Add(value);
  // @@protoc_insertion_point(field_add:protobuf_unittest.TestAllTypesLite.repeated_float)
}
inline const ::google::protobuf::RepeatedField< float >&
TestAllTypesLite::repeated_float() const {
  // @@protoc_insertion_point(field_list:protobuf_unittest.TestAllTypesLite.repeated_float)
  return repeated_float_;
}
inline ::google::protobuf::RepeatedField< float >*
TestAllTypesLite::mutable_repeated_float() {
  // @@protoc_insertion_point(field_mutable_list:protobuf_unittest.TestAllTypesLite.repeated_float)
  return &repeated_float_;
}

// repeated double repeated_double = 42;
inline int TestAllTypesLite::repeated_double_size() const {
  return repeated_double_.size();
}
inline void TestAllTypesLite::clear_repeated_double() {
  repeated_double_.Clear();
}
inline double TestAllTypesLite::repeated_double(int index) const {
  // @@protoc_insertion_point(field_get:protobuf_unittest.TestAllTypesLite.repeated_double)
  return repeated_double_.Get(index);
}
inline void TestAllTypesLite::set_repeated_double(int index, double value) {
  repeated_double_.Set(index, value);
  // @@protoc_insertion_point(field_set:protobuf_unittest.TestAllTypesLite.repeated_double)
}
inline void TestAllTypesLite::add_repeated_double(double value) {
  repeated_double_.Add(value);
  // @@protoc_insertion_point(field_add:protobuf_unittest.TestAllTypesLite.repeated_double)
}
inline const ::google::protobuf::RepeatedField< double >&
TestAllTypesLite::repeated_double() const {
  // @@protoc_insertion_point(field_list:protobuf_unittest.TestAllTypesLite.repeated_double)
  return repeated_double_;
}
inline ::google::protobuf::RepeatedField< double >*
TestAllTypesLite::mutable_repeated_double() {
  // @@protoc_insertion_point(field_mutable_list:protobuf_unittest.TestAllTypesLite.repeated_double)
  return &repeated_double_;
}

// repeated bool repeated_bool = 43;
inline int TestAllTypesLite::repeated_bool_size() const {
  return repeated_bool_.size();
}
inline void TestAllTypesLite::clear_repeated_bool() {
  repeated_bool_.Clear();
}
inline bool TestAllTypesLite::repeated_bool(int index) const {
  // @@protoc_insertion_point(field_get:protobuf_unittest.TestAllTypesLite.repeated_bool)
  return repeated_bool_.Get(index);
}
inline void TestAllTypesLite::set_repeated_bool(int index, bool value) {
  repeated_bool_.Set(index, value);
  // @@protoc_insertion_point(field_set:protobuf_unittest.TestAllTypesLite.repeated_bool)
}
inline void TestAllTypesLite::add_repeated_bool(bool value) {
  repeated_bool_.Add(value);
  // @@protoc_insertion_point(field_add:protobuf_unittest.TestAllTypesLite.repeated_bool)
}
inline const ::google::protobuf::RepeatedField< bool >&
TestAllTypesLite::repeated_bool() const {
  // @@protoc_insertion_point(field_list:protobuf_unittest.TestAllTypesLite.repeated_bool)
  return repeated_bool_;
}
inline ::google::protobuf::RepeatedField< bool >*
TestAllTypesLite::mutable_repeated_bool() {
  // @@protoc_insertion_point(field_mutable_list:protobuf_unittest.TestAllTypesLite.repeated_bool)
  return &repeated_bool_;
}

// repeated string repeated_string = 44;
inline int TestAllTypesLite::repeated_string_size() const {
  return repeated_string_.size();
}
inline void TestAllTypesLite::clear_repeated_string() {
  repeated_string_.Clear();
}
inline const ::std::string& TestAllTypesLite::repeated_string(int index) const {
  // @@protoc_insertion_point(field_get:protobuf_unittest.TestAllTypesLite.repeated_string)
  return repeated_string_.Get(index);
}
inline ::std::string* TestAllTypesLite::mutable_repeated_string(int index) {
  // @@protoc_insertion_point(field_mutable:protobuf_unittest.TestAllTypesLite.repeated_string)
  return repeated_string_.Mutable(index);
}
inline void TestAllTypesLite::set_repeated_string(int index, const ::std::string& value) {
  // @@protoc_insertion_point(field_set:protobuf_unittest.TestAllTypesLite.repeated_string)
  repeated_string_.Mutable(index)->assign(value);
}
inline void TestAllTypesLite::set_repeated_string(int index, const char* value) {
  repeated_string_.Mutable(index)->assign(value);
  // @@protoc_insertion_point(field_set_char:protobuf_unittest.TestAllTypesLite.repeated_string)
}
inline void TestAllTypesLite::set_repeated_string(int index, const char* value, size_t size) {
  repeated_string_.Mutable(index)->assign(
    reinterpret_cast<const char*>(value), size);
  // @@protoc_insertion_point(field_set_pointer:protobuf_unittest.TestAllTypesLite.repeated_string)
}
inline ::std::string* TestAllTypesLite::add_repeated_string() {
  // @@protoc_insertion_point(field_add_mutable:protobuf_unittest.TestAllTypesLite.repeated_string)
  return repeated_string_.Add();
}
inline void TestAllTypesLite::add_repeated_string(const ::std::string& value) {
  repeated_string_.Add()->assign(value);
  // @@protoc_insertion_point(field_add:protobuf_unittest.TestAllTypesLite.repeated_string)
}
inline void TestAllTypesLite::add_repeated_string(const char* value) {
  repeated_string_.Add()->assign(value);
  // @@protoc_insertion_point(field_add_char:protobuf_unittest.TestAllTypesLite.repeated_string)
}
inline void TestAllTypesLite::add_repeated_string(const char* value, size_t size) {
  repeated_string_.Add()->assign(reinterpret_cast<const char*>(value), size);
  // @@protoc_insertion_point(field_add_pointer:protobuf_unittest.TestAllTypesLite.repeated_string)
}
inline const ::google::protobuf::RepeatedPtrField< ::std::string>&
TestAllTypesLite::repeated_string() const {
  // @@protoc_insertion_point(field_list:protobuf_unittest.TestAllTypesLite.repeated_string)
  return repeated_string_;
}
inline ::google::protobuf::RepeatedPtrField< ::std::string>*
TestAllTypesLite::mutable_repeated_string() {
  // @@protoc_insertion_point(field_mutable_list:protobuf_unittest.TestAllTypesLite.repeated_string)
  return &repeated_string_;
}

// repeated bytes repeated_bytes = 45;
inline int TestAllTypesLite::repeated_bytes_size() const {
  return repeated_bytes_.size();
}
inline void TestAllTypesLite::clear_repeated_bytes() {
  repeated_bytes_.Clear();
}
inline const ::std::string& TestAllTypesLite::repeated_bytes(int index) const {
  // @@protoc_insertion_point(field_get:protobuf_unittest.TestAllTypesLite.repeated_bytes)
  return repeated_bytes_.Get(index);
}
inline ::std::string* TestAllTypesLite::mutable_repeated_bytes(int index) {
  // @@protoc_insertion_point(field_mutable:protobuf_unittest.TestAllTypesLite.repeated_bytes)
  return repeated_bytes_.Mutable(index);
}
inline void TestAllTypesLite::set_repeated_bytes(int index, const ::std::string& value) {
  // @@protoc_insertion_point(field_set:protobuf_unittest.TestAllTypesLite.repeated_bytes)
  repeated_bytes_.Mutable(index)->assign(value);
}
inline void TestAllTypesLite::set_repeated_bytes(int index, const char* value) {
  repeated_bytes_.Mutable(index)->assign(value);
  // @@protoc_insertion_point(field_set_char:protobuf_unittest.TestAllTypesLite.repeated_bytes)
}
inline void TestAllTypesLite::set_repeated_bytes(int index, const void* value, size_t size) {
  repeated_bytes_.Mutable(index)->assign(
    reinterpret_cast<const char*>(value), size);
  // @@protoc_insertion_point(field_set_pointer:protobuf_unittest.TestAllTypesLite.repeated_bytes)
}
inline ::std::string* TestAllTypesLite::add_repeated_bytes() {
  // @@protoc_insertion_point(field_add_mutable:protobuf_unittest.TestAllTypesLite.repeated_bytes)
  return repeated_bytes_.Add();
}
inline void TestAllTypesLite::add_repeated_bytes(const ::std::string& value) {
  repeated_bytes_.Add()->assign(value);
  // @@protoc_insertion_point(field_add:protobuf_unittest.TestAllTypesLite.repeated_bytes)
}
inline void TestAllTypesLite::add_repeated_bytes(const char* value) {
  repeated_bytes_.Add()->assign(value);
  // @@protoc_insertion_point(field_add_char:protobuf_unittest.TestAllTypesLite.repeated_bytes)
}
inline void TestAllTypesLite::add_repeated_bytes(const void* value, size_t size) {
  repeated_bytes_.Add()->assign(reinterpret_cast<const char*>(value), size);
  // @@protoc_insertion_point(field_add_pointer:protobuf_unittest.TestAllTypesLite.repeated_bytes)
}
inline const ::google::protobuf::RepeatedPtrField< ::std::string>&
TestAllTypesLite::repeated_bytes() const {
  // @@protoc_insertion_point(field_list:protobuf_unittest.TestAllTypesLite.repeated_bytes)
  return repeated_bytes_;
}
inline ::google::protobuf::RepeatedPtrField< ::std::string>*
TestAllTypesLite::mutable_repeated_bytes() {
  // @@protoc_insertion_point(field_mutable_list:protobuf_unittest.TestAllTypesLite.repeated_bytes)
  return &repeated_bytes_;
}

// repeated group RepeatedGroup = 46 { ... };
inline int TestAllTypesLite::repeatedgroup_size() const {
  return repeatedgroup_.size();
}
inline void TestAllTypesLite::clear_repeatedgroup() {
  repeatedgroup_.Clear();
}
inline const ::protobuf_unittest::TestAllTypesLite_RepeatedGroup& TestAllTypesLite::repeatedgroup(int index) const {
  // @@protoc_insertion_point(field_get:protobuf_unittest.TestAllTypesLite.repeatedgroup)
  return repeatedgroup_.Get(index);
}
inline ::protobuf_unittest::TestAllTypesLite_RepeatedGroup* TestAllTypesLite::mutable_repeatedgroup(int index) {
  // @@protoc_insertion_point(field_mutable:protobuf_unittest.TestAllTypesLite.repeatedgroup)
  return repeatedgroup_.Mutable(index);
}
inline ::protobuf_unittest::TestAllTypesLite_RepeatedGroup* TestAllTypesLite::add_repeatedgroup() {
  // @@protoc_insertion_point(field_add:protobuf_unittest.TestAllTypesLite.repeatedgroup)
  return repeatedgroup_.Add();
}
inline ::google::protobuf::RepeatedPtrField< ::protobuf_unittest::TestAllTypesLite_RepeatedGroup >*
TestAllTypesLite::mutable_repeatedgroup() {
  // @@protoc_insertion_point(field_mutable_list:protobuf_unittest.TestAllTypesLite.repeatedgroup)
  return &repeatedgroup_;
}
inline const ::google::protobuf::RepeatedPtrField< ::protobuf_unittest::TestAllTypesLite_RepeatedGroup >&
TestAllTypesLite::repeatedgroup() const {
  // @@protoc_insertion_point(field_list:protobuf_unittest.TestAllTypesLite.repeatedgroup)
  return repeatedgroup_;
}

// repeated .protobuf_unittest.TestAllTypesLite.NestedMessage repeated_nested_message = 48;
inline int TestAllTypesLite::repeated_nested_message_size() const {
  return repeated_nested_message_.size();
}
inline void TestAllTypesLite::clear_repeated_nested_message() {
  repeated_nested_message_.Clear();
}
inline const ::protobuf_unittest::TestAllTypesLite_NestedMessage& TestAllTypesLite::repeated_nested_message(int index) const {
  // @@protoc_insertion_point(field_get:protobuf_unittest.TestAllTypesLite.repeated_nested_message)
  return repeated_nested_message_.Get(index);
}
inline ::protobuf_unittest::TestAllTypesLite_NestedMessage* TestAllTypesLite::mutable_repeated_nested_message(int index) {
  // @@protoc_insertion_point(field_mutable:protobuf_unittest.TestAllTypesLite.repeated_nested_message)
  return repeated_nested_message_.Mutable(index);
}
inline ::protobuf_unittest::TestAllTypesLite_NestedMessage* TestAllTypesLite::add_repeated_nested_message() {
  // @@protoc_insertion_point(field_add:protobuf_unittest.TestAllTypesLite.repeated_nested_message)
  return repeated_nested_message_.Add();
}
inline ::google::protobuf::RepeatedPtrField< ::protobuf_unittest::TestAllTypesLite_NestedMessage >*
TestAllTypesLite::mutable_repeated_nested_message() {
  // @@protoc_insertion_point(field_mutable_list:protobuf_unittest.TestAllTypesLite.repeated_nested_message)
  return &repeated_nested_message_;
}
inline const ::google::protobuf::RepeatedPtrField< ::protobuf_unittest::TestAllTypesLite_NestedMessage >&
TestAllTypesLite::repeated_nested_message() const {
  // @@protoc_insertion_point(field_list:protobuf_unittest.TestAllTypesLite.repeated_nested_message)
  return repeated_nested_message_;
}

// repeated .protobuf_unittest.ForeignMessageLite repeated_foreign_message = 49;
inline int TestAllTypesLite::repeated_foreign_message_size() const {
  return repeated_foreign_message_.size();
}
inline void TestAllTypesLite::clear_repeated_foreign_message() {
  repeated_foreign_message_.Clear();
}
inline const ::protobuf_unittest::ForeignMessageLite& TestAllTypesLite::repeated_foreign_message(int index) const {
  // @@protoc_insertion_point(field_get:protobuf_unittest.TestAllTypesLite.repeated_foreign_message)
  return repeated_foreign_message_.Get(index);
}
inline ::protobuf_unittest::ForeignMessageLite* TestAllTypesLite::mutable_repeated_foreign_message(int index) {
  // @@protoc_insertion_point(field_mutable:protobuf_unittest.TestAllTypesLite.repeated_foreign_message)
  return repeated_foreign_message_.Mutable(index);
}
inline ::protobuf_unittest::ForeignMessageLite* TestAllTypesLite::add_repeated_foreign_message() {
  // @@protoc_insertion_point(field_add:protobuf_unittest.TestAllTypesLite.repeated_foreign_message)
  return repeated_foreign_message_.Add();
}
inline ::google::protobuf::RepeatedPtrField< ::protobuf_unittest::ForeignMessageLite >*
TestAllTypesLite::mutable_repeated_foreign_message() {
  // @@protoc_insertion_point(field_mutable_list:protobuf_unittest.TestAllTypesLite.repeated_foreign_message)
  return &repeated_foreign_message_;
}
inline const ::google::protobuf::RepeatedPtrField< ::protobuf_unittest::ForeignMessageLite >&
TestAllTypesLite::repeated_foreign_message() const {
  // @@protoc_insertion_point(field_list:protobuf_unittest.TestAllTypesLite.repeated_foreign_message)
  return repeated_foreign_message_;
}

// repeated .protobuf_unittest_import.ImportMessageLite repeated_import_message = 50;
inline int TestAllTypesLite::repeated_import_message_size() const {
  return repeated_import_message_.size();
}
inline void TestAllTypesLite::clear_repeated_import_message() {
  repeated_import_message_.Clear();
}
inline const ::protobuf_unittest_import::ImportMessageLite& TestAllTypesLite::repeated_import_message(int index) const {
  // @@protoc_insertion_point(field_get:protobuf_unittest.TestAllTypesLite.repeated_import_message)
  return repeated_import_message_.Get(index);
}
inline ::protobuf_unittest_import::ImportMessageLite* TestAllTypesLite::mutable_repeated_import_message(int index) {
  // @@protoc_insertion_point(field_mutable:protobuf_unittest.TestAllTypesLite.repeated_import_message)
  return repeated_import_message_.Mutable(index);
}
inline ::protobuf_unittest_import::ImportMessageLite* TestAllTypesLite::add_repeated_import_message() {
  // @@protoc_insertion_point(field_add:protobuf_unittest.TestAllTypesLite.repeated_import_message)
  return repeated_import_message_.Add();
}
inline ::google::protobuf::RepeatedPtrField< ::protobuf_unittest_import::ImportMessageLite >*
TestAllTypesLite::mutable_repeated_import_message() {
  // @@protoc_insertion_point(field_mutable_list:protobuf_unittest.TestAllTypesLite.repeated_import_message)
  return &repeated_import_message_;
}
inline const ::google::protobuf::RepeatedPtrField< ::protobuf_unittest_import::ImportMessageLite >&
TestAllTypesLite::repeated_import_message() const {
  // @@protoc_insertion_point(field_list:protobuf_unittest.TestAllTypesLite.repeated_import_message)
  return repeated_import_message_;
}

// repeated .protobuf_unittest.TestAllTypesLite.NestedEnum repeated_nested_enum = 51;
inline int TestAllTypesLite::repeated_nested_enum_size() const {
  return repeated_nested_enum_.size();
}
inline void TestAllTypesLite::clear_repeated_nested_enum() {
  repeated_nested_enum_.Clear();
}
inline ::protobuf_unittest::TestAllTypesLite_NestedEnum TestAllTypesLite::repeated_nested_enum(int index) const {
  // @@protoc_insertion_point(field_get:protobuf_unittest.TestAllTypesLite.repeated_nested_enum)
  return static_cast< ::protobuf_unittest::TestAllTypesLite_NestedEnum >(repeated_nested_enum_.Get(index));
}
inline void TestAllTypesLite::set_repeated_nested_enum(int index, ::protobuf_unittest::TestAllTypesLite_NestedEnum value) {
  assert(::protobuf_unittest::TestAllTypesLite_NestedEnum_IsValid(value));
  repeated_nested_enum_.Set(index, value);
  // @@protoc_insertion_point(field_set:protobuf_unittest.TestAllTypesLite.repeated_nested_enum)
}
inline void TestAllTypesLite::add_repeated_nested_enum(::protobuf_unittest::TestAllTypesLite_NestedEnum value) {
  assert(::protobuf_unittest::TestAllTypesLite_NestedEnum_IsValid(value));
  repeated_nested_enum_.Add(value);
  // @@protoc_insertion_point(field_add:protobuf_unittest.TestAllTypesLite.repeated_nested_enum)
}
inline const ::google::protobuf::RepeatedField<int>&
TestAllTypesLite::repeated_nested_enum() const {
  // @@protoc_insertion_point(field_list:protobuf_unittest.TestAllTypesLite.repeated_nested_enum)
  return repeated_nested_enum_;
}
inline ::google::protobuf::RepeatedField<int>*
TestAllTypesLite::mutable_repeated_nested_enum() {
  // @@protoc_insertion_point(field_mutable_list:protobuf_unittest.TestAllTypesLite.repeated_nested_enum)
  return &repeated_nested_enum_;
}

// repeated .protobuf_unittest.ForeignEnumLite repeated_foreign_enum = 52;
inline int TestAllTypesLite::repeated_foreign_enum_size() const {
  return repeated_foreign_enum_.size();
}
inline void TestAllTypesLite::clear_repeated_foreign_enum() {
  repeated_foreign_enum_.Clear();
}
inline ::protobuf_unittest::ForeignEnumLite TestAllTypesLite::repeated_foreign_enum(int index) const {
  // @@protoc_insertion_point(field_get:protobuf_unittest.TestAllTypesLite.repeated_foreign_enum)
  return static_cast< ::protobuf_unittest::ForeignEnumLite >(repeated_foreign_enum_.Get(index));
}
inline void TestAllTypesLite::set_repeated_foreign_enum(int index, ::protobuf_unittest::ForeignEnumLite value) {
  assert(::protobuf_unittest::ForeignEnumLite_IsValid(value));
  repeated_foreign_enum_.Set(index, value);
  // @@protoc_insertion_point(field_set:protobuf_unittest.TestAllTypesLite.repeated_foreign_enum)
}
inline void TestAllTypesLite::add_repeated_foreign_enum(::protobuf_unittest::ForeignEnumLite value) {
  assert(::protobuf_unittest::ForeignEnumLite_IsValid(value));
  repeated_foreign_enum_.Add(value);
  // @@protoc_insertion_point(field_add:protobuf_unittest.TestAllTypesLite.repeated_foreign_enum)
}
inline const ::google::protobuf::RepeatedField<int>&
TestAllTypesLite::repeated_foreign_enum() const {
  // @@protoc_insertion_point(field_list:protobuf_unittest.TestAllTypesLite.repeated_foreign_enum)
  return repeated_foreign_enum_;
}
inline ::google::protobuf::RepeatedField<int>*
TestAllTypesLite::mutable_repeated_foreign_enum() {
  // @@protoc_insertion_point(field_mutable_list:protobuf_unittest.TestAllTypesLite.repeated_foreign_enum)
  return &repeated_foreign_enum_;
}

// repeated .protobuf_unittest_import.ImportEnumLite repeated_import_enum = 53;
inline int TestAllTypesLite::repeated_import_enum_size() const {
  return repeated_import_enum_.size();
}
inline void TestAllTypesLite::clear_repeated_import_enum() {
  repeated_import_enum_.Clear();
}
inline ::protobuf_unittest_import::ImportEnumLite TestAllTypesLite::repeated_import_enum(int index) const {
  // @@protoc_insertion_point(field_get:protobuf_unittest.TestAllTypesLite.repeated_import_enum)
  return static_cast< ::protobuf_unittest_import::ImportEnumLite >(repeated_import_enum_.Get(index));
}
inline void TestAllTypesLite::set_repeated_import_enum(int index, ::protobuf_unittest_import::ImportEnumLite value) {
  assert(::protobuf_unittest_import::ImportEnumLite_IsValid(value));
  repeated_import_enum_.Set(index, value);
  // @@protoc_insertion_point(field_set:protobuf_unittest.TestAllTypesLite.repeated_import_enum)
}
inline void TestAllTypesLite::add_repeated_import_enum(::protobuf_unittest_import::ImportEnumLite value) {
  assert(::protobuf_unittest_import::ImportEnumLite_IsValid(value));
  repeated_import_enum_.Add(value);
  // @@protoc_insertion_point(field_add:protobuf_unittest.TestAllTypesLite.repeated_import_enum)
}
inline const ::google::protobuf::RepeatedField<int>&
TestAllTypesLite::repeated_import_enum() const {
  // @@protoc_insertion_point(field_list:protobuf_unittest.TestAllTypesLite.repeated_import_enum)
  return repeated_import_enum_;
}
inline ::google::protobuf::RepeatedField<int>*
TestAllTypesLite::mutable_repeated_import_enum() {
  // @@protoc_insertion_point(field_mutable_list:protobuf_unittest.TestAllTypesLite.repeated_import_enum)
  return &repeated_import_enum_;
}

// repeated string repeated_string_piece = 54 [ctype = STRING_PIECE];
inline int TestAllTypesLite::repeated_string_piece_size() const {
  return repeated_string_piece_.size();
}
inline void TestAllTypesLite::clear_repeated_string_piece() {
  repeated_string_piece_.Clear();
}
inline const ::std::string& TestAllTypesLite::repeated_string_piece(int index) const {
  // @@protoc_insertion_point(field_get:protobuf_unittest.TestAllTypesLite.repeated_string_piece)
  return repeated_string_piece_.Get(index);
}
inline ::std::string* TestAllTypesLite::mutable_repeated_string_piece(int index) {
  // @@protoc_insertion_point(field_mutable:protobuf_unittest.TestAllTypesLite.repeated_string_piece)
  return repeated_string_piece_.Mutable(index);
}
inline void TestAllTypesLite::set_repeated_string_piece(int index, const ::std::string& value) {
  // @@protoc_insertion_point(field_set:protobuf_unittest.TestAllTypesLite.repeated_string_piece)
  repeated_string_piece_.Mutable(index)->assign(value);
}
inline void TestAllTypesLite::set_repeated_string_piece(int index, const char* value) {
  repeated_string_piece_.Mutable(index)->assign(value);
  // @@protoc_insertion_point(field_set_char:protobuf_unittest.TestAllTypesLite.repeated_string_piece)
}
inline void TestAllTypesLite::set_repeated_string_piece(int index, const char* value, size_t size) {
  repeated_string_piece_.Mutable(index)->assign(
    reinterpret_cast<const char*>(value), size);
  // @@protoc_insertion_point(field_set_pointer:protobuf_unittest.TestAllTypesLite.repeated_string_piece)
}
inline ::std::string* TestAllTypesLite::add_repeated_string_piece() {
  // @@protoc_insertion_point(field_add_mutable:protobuf_unittest.TestAllTypesLite.repeated_string_piece)
  return repeated_string_piece_.Add();
}
inline void TestAllTypesLite::add_repeated_string_piece(const ::std::string& value) {
  repeated_string_piece_.Add()->assign(value);
  // @@protoc_insertion_point(field_add:protobuf_unittest.TestAllTypesLite.repeated_string_piece)
}
inline void TestAllTypesLite::add_repeated_string_piece(const char* value) {
  repeated_string_piece_.Add()->assign(value);
  // @@protoc_insertion_point(field_add_char:protobuf_unittest.TestAllTypesLite.repeated_string_piece)
}
inline void TestAllTypesLite::add_repeated_string_piece(const char* value, size_t size) {
  repeated_string_piece_.Add()->assign(reinterpret_cast<const char*>(value), size);
  // @@protoc_insertion_point(field_add_pointer:protobuf_unittest.TestAllTypesLite.repeated_string_piece)
}
inline const ::google::protobuf::RepeatedPtrField< ::std::string>&
TestAllTypesLite::repeated_string_piece() const {
  // @@protoc_insertion_point(field_list:protobuf_unittest.TestAllTypesLite.repeated_string_piece)
  return repeated_string_piece_;
}
inline ::google::protobuf::RepeatedPtrField< ::std::string>*
TestAllTypesLite::mutable_repeated_string_piece() {
  // @@protoc_insertion_point(field_mutable_list:protobuf_unittest.TestAllTypesLite.repeated_string_piece)
  return &repeated_string_piece_;
}

// repeated string repeated_cord = 55 [ctype = CORD];
inline int TestAllTypesLite::repeated_cord_size() const {
  return repeated_cord_.size();
}
inline void TestAllTypesLite::clear_repeated_cord() {
  repeated_cord_.Clear();
}
inline const ::std::string& TestAllTypesLite::repeated_cord(int index) const {
  // @@protoc_insertion_point(field_get:protobuf_unittest.TestAllTypesLite.repeated_cord)
  return repeated_cord_.Get(index);
}
inline ::std::string* TestAllTypesLite::mutable_repeated_cord(int index) {
  // @@protoc_insertion_point(field_mutable:protobuf_unittest.TestAllTypesLite.repeated_cord)
  return repeated_cord_.Mutable(index);
}
inline void TestAllTypesLite::set_repeated_cord(int index, const ::std::string& value) {
  // @@protoc_insertion_point(field_set:protobuf_unittest.TestAllTypesLite.repeated_cord)
  repeated_cord_.Mutable(index)->assign(value);
}
inline void TestAllTypesLite::set_repeated_cord(int index, const char* value) {
  repeated_cord_.Mutable(index)->assign(value);
  // @@protoc_insertion_point(field_set_char:protobuf_unittest.TestAllTypesLite.repeated_cord)
}
inline void TestAllTypesLite::set_repeated_cord(int index, const char* value, size_t size) {
  repeated_cord_.Mutable(index)->assign(
    reinterpret_cast<const char*>(value), size);
  // @@protoc_insertion_point(field_set_pointer:protobuf_unittest.TestAllTypesLite.repeated_cord)
}
inline ::std::string* TestAllTypesLite::add_repeated_cord() {
  // @@protoc_insertion_point(field_add_mutable:protobuf_unittest.TestAllTypesLite.repeated_cord)
  return repeated_cord_.Add();
}
inline void TestAllTypesLite::add_repeated_cord(const ::std::string& value) {
  repeated_cord_.Add()->assign(value);
  // @@protoc_insertion_point(field_add:protobuf_unittest.TestAllTypesLite.repeated_cord)
}
inline void TestAllTypesLite::add_repeated_cord(const char* value) {
  repeated_cord_.Add()->assign(value);
  // @@protoc_insertion_point(field_add_char:protobuf_unittest.TestAllTypesLite.repeated_cord)
}
inline void TestAllTypesLite::add_repeated_cord(const char* value, size_t size) {
  repeated_cord_.Add()->assign(reinterpret_cast<const char*>(value), size);
  // @@protoc_insertion_point(field_add_pointer:protobuf_unittest.TestAllTypesLite.repeated_cord)
}
inline const ::google::protobuf::RepeatedPtrField< ::std::string>&
TestAllTypesLite::repeated_cord() const {
  // @@protoc_insertion_point(field_list:protobuf_unittest.TestAllTypesLite.repeated_cord)
  return repeated_cord_;
}
inline ::google::protobuf::RepeatedPtrField< ::std::string>*
TestAllTypesLite::mutable_repeated_cord() {
  // @@protoc_insertion_point(field_mutable_list:protobuf_unittest.TestAllTypesLite.repeated_cord)
  return &repeated_cord_;
}

// repeated .protobuf_unittest.TestAllTypesLite.NestedMessage repeated_lazy_message = 57 [lazy = true];
inline int TestAllTypesLite::repeated_lazy_message_size() const {
  return repeated_lazy_message_.size();
}
inline void TestAllTypesLite::clear_repeated_lazy_message() {
  repeated_lazy_message_.Clear();
}
inline const ::protobuf_unittest::TestAllTypesLite_NestedMessage& TestAllTypesLite::repeated_lazy_message(int index) const {
  // @@protoc_insertion_point(field_get:protobuf_unittest.TestAllTypesLite.repeated_lazy_message)
  return repeated_lazy_message_.Get(index);
}
inline ::protobuf_unittest::TestAllTypesLite_NestedMessage* TestAllTypesLite::mutable_repeated_lazy_message(int index) {
  // @@protoc_insertion_point(field_mutable:protobuf_unittest.TestAllTypesLite.repeated_lazy_message)
  return repeated_lazy_message_.Mutable(index);
}
inline ::protobuf_unittest::TestAllTypesLite_NestedMessage* TestAllTypesLite::add_repeated_lazy_message() {
  // @@protoc_insertion_point(field_add:protobuf_unittest.TestAllTypesLite.repeated_lazy_message)
  return repeated_lazy_message_.Add();
}
inline ::google::protobuf::RepeatedPtrField< ::protobuf_unittest::TestAllTypesLite_NestedMessage >*
TestAllTypesLite::mutable_repeated_lazy_message() {
  // @@protoc_insertion_point(field_mutable_list:protobuf_unittest.TestAllTypesLite.repeated_lazy_message)
  return &repeated_lazy_message_;
}
inline const ::google::protobuf::RepeatedPtrField< ::protobuf_unittest::TestAllTypesLite_NestedMessage >&
TestAllTypesLite::repeated_lazy_message() const {
  // @@protoc_insertion_point(field_list:protobuf_unittest.TestAllTypesLite.repeated_lazy_message)
  return repeated_lazy_message_;
}

// optional int32 default_int32 = 61 [default = 41];
inline bool TestAllTypesLite::has_default_int32() const {
  return (_has_bits_[1] & 0x00080000u) != 0;
}
inline void TestAllTypesLite::set_has_default_int32() {
  _has_bits_[1] |= 0x00080000u;
}
inline void TestAllTypesLite::clear_has_default_int32() {
  _has_bits_[1] &= ~0x00080000u;
}
inline void TestAllTypesLite::clear_default_int32() {
  default_int32_ = 41;
  clear_has_default_int32();
}
inline ::google::protobuf::int32 TestAllTypesLite::default_int32() const {
  // @@protoc_insertion_point(field_get:protobuf_unittest.TestAllTypesLite.default_int32)
  return default_int32_;
}
inline void TestAllTypesLite::set_default_int32(::google::protobuf::int32 value) {
  set_has_default_int32();
  default_int32_ = value;
  // @@protoc_insertion_point(field_set:protobuf_unittest.TestAllTypesLite.default_int32)
}

// optional int64 default_int64 = 62 [default = 42];
inline bool TestAllTypesLite::has_default_int64() const {
  return (_has_bits_[1] & 0x00100000u) != 0;
}
inline void TestAllTypesLite::set_has_default_int64() {
  _has_bits_[1] |= 0x00100000u;
}
inline void TestAllTypesLite::clear_has_default_int64() {
  _has_bits_[1] &= ~0x00100000u;
}
inline void TestAllTypesLite::clear_default_int64() {
  default_int64_ = GOOGLE_LONGLONG(42);
  clear_has_default_int64();
}
inline ::google::protobuf::int64 TestAllTypesLite::default_int64() const {
  // @@protoc_insertion_point(field_get:protobuf_unittest.TestAllTypesLite.default_int64)
  return default_int64_;
}
inline void TestAllTypesLite::set_default_int64(::google::protobuf::int64 value) {
  set_has_default_int64();
  default_int64_ = value;
  // @@protoc_insertion_point(field_set:protobuf_unittest.TestAllTypesLite.default_int64)
}

// optional uint32 default_uint32 = 63 [default = 43];
inline bool TestAllTypesLite::has_default_uint32() const {
  return (_has_bits_[1] & 0x00200000u) != 0;
}
inline void TestAllTypesLite::set_has_default_uint32() {
  _has_bits_[1] |= 0x00200000u;
}
inline void TestAllTypesLite::clear_has_default_uint32() {
  _has_bits_[1] &= ~0x00200000u;
}
inline void TestAllTypesLite::clear_default_uint32() {
  default_uint32_ = 43u;
  clear_has_default_uint32();
}
inline ::google::protobuf::uint32 TestAllTypesLite::default_uint32() const {
  // @@protoc_insertion_point(field_get:protobuf_unittest.TestAllTypesLite.default_uint32)
  return default_uint32_;
}
inline void TestAllTypesLite::set_default_uint32(::google::protobuf::uint32 value) {
  set_has_default_uint32();
  default_uint32_ = value;
  // @@protoc_insertion_point(field_set:protobuf_unittest.TestAllTypesLite.default_uint32)
}

// optional uint64 default_uint64 = 64 [default = 44];
inline bool TestAllTypesLite::has_default_uint64() const {
  return (_has_bits_[1] & 0x00400000u) != 0;
}
inline void TestAllTypesLite::set_has_default_uint64() {
  _has_bits_[1] |= 0x00400000u;
}
inline void TestAllTypesLite::clear_has_default_uint64() {
  _has_bits_[1] &= ~0x00400000u;
}
inline void TestAllTypesLite::clear_default_uint64() {
  default_uint64_ = GOOGLE_ULONGLONG(44);
  clear_has_default_uint64();
}
inline ::google::protobuf::uint64 TestAllTypesLite::default_uint64() const {
  // @@protoc_insertion_point(field_get:protobuf_unittest.TestAllTypesLite.default_uint64)
  return default_uint64_;
}
inline void TestAllTypesLite::set_default_uint64(::google::protobuf::uint64 value) {
  set_has_default_uint64();
  default_uint64_ = value;
  // @@protoc_insertion_point(field_set:protobuf_unittest.TestAllTypesLite.default_uint64)
}

// optional sint32 default_sint32 = 65 [default = -45];
inline bool TestAllTypesLite::has_default_sint32() const {
  return (_has_bits_[1] & 0x00800000u) != 0;
}
inline void TestAllTypesLite::set_has_default_sint32() {
  _has_bits_[1] |= 0x00800000u;
}
inline void TestAllTypesLite::clear_has_default_sint32() {
  _has_bits_[1] &= ~0x00800000u;
}
inline void TestAllTypesLite::clear_default_sint32() {
  default_sint32_ = -45;
  clear_has_default_sint32();
}
inline ::google::protobuf::int32 TestAllTypesLite::default_sint32() const {
  // @@protoc_insertion_point(field_get:protobuf_unittest.TestAllTypesLite.default_sint32)
  return default_sint32_;
}
inline void TestAllTypesLite::set_default_sint32(::google::protobuf::int32 value) {
  set_has_default_sint32();
  default_sint32_ = value;
  // @@protoc_insertion_point(field_set:protobuf_unittest.TestAllTypesLite.default_sint32)
}

// optional sint64 default_sint64 = 66 [default = 46];
inline bool TestAllTypesLite::has_default_sint64() const {
  return (_has_bits_[1] & 0x01000000u) != 0;
}
inline void TestAllTypesLite::set_has_default_sint64() {
  _has_bits_[1] |= 0x01000000u;
}
inline void TestAllTypesLite::clear_has_default_sint64() {
  _has_bits_[1] &= ~0x01000000u;
}
inline void TestAllTypesLite::clear_default_sint64() {
  default_sint64_ = GOOGLE_LONGLONG(46);
  clear_has_default_sint64();
}
inline ::google::protobuf::int64 TestAllTypesLite::default_sint64() const {
  // @@protoc_insertion_point(field_get:protobuf_unittest.TestAllTypesLite.default_sint64)
  return default_sint64_;
}
inline void TestAllTypesLite::set_default_sint64(::google::protobuf::int64 value) {
  set_has_default_sint64();
  default_sint64_ = value;
  // @@protoc_insertion_point(field_set:protobuf_unittest.TestAllTypesLite.default_sint64)
}

// optional fixed32 default_fixed32 = 67 [default = 47];
inline bool TestAllTypesLite::has_default_fixed32() const {
  return (_has_bits_[1] & 0x02000000u) != 0;
}
inline void TestAllTypesLite::set_has_default_fixed32() {
  _has_bits_[1] |= 0x02000000u;
}
inline void TestAllTypesLite::clear_has_default_fixed32() {
  _has_bits_[1] &= ~0x02000000u;
}
inline void TestAllTypesLite::clear_default_fixed32() {
  default_fixed32_ = 47u;
  clear_has_default_fixed32();
}
inline ::google::protobuf::uint32 TestAllTypesLite::default_fixed32() const {
  // @@protoc_insertion_point(field_get:protobuf_unittest.TestAllTypesLite.default_fixed32)
  return default_fixed32_;
}
inline void TestAllTypesLite::set_default_fixed32(::google::protobuf::uint32 value) {
  set_has_default_fixed32();
  default_fixed32_ = value;
  // @@protoc_insertion_point(field_set:protobuf_unittest.TestAllTypesLite.default_fixed32)
}

// optional fixed64 default_fixed64 = 68 [default = 48];
inline bool TestAllTypesLite::has_default_fixed64() const {
  return (_has_bits_[1] & 0x04000000u) != 0;
}
inline void TestAllTypesLite::set_has_default_fixed64() {
  _has_bits_[1] |= 0x04000000u;
}
inline void TestAllTypesLite::clear_has_default_fixed64() {
  _has_bits_[1] &= ~0x04000000u;
}
inline void TestAllTypesLite::clear_default_fixed64() {
  default_fixed64_ = GOOGLE_ULONGLONG(48);
  clear_has_default_fixed64();
}
inline ::google::protobuf::uint64 TestAllTypesLite::default_fixed64() const {
  // @@protoc_insertion_point(field_get:protobuf_unittest.TestAllTypesLite.default_fixed64)
  return default_fixed64_;
}
inline void TestAllTypesLite::set_default_fixed64(::google::protobuf::uint64 value) {
  set_has_default_fixed64();
  default_fixed64_ = value;
  // @@protoc_insertion_point(field_set:protobuf_unittest.TestAllTypesLite.default_fixed64)
}

// optional sfixed32 default_sfixed32 = 69 [default = 49];
inline bool TestAllTypesLite::has_default_sfixed32() const {
  return (_has_bits_[1] & 0x08000000u) != 0;
}
inline void TestAllTypesLite::set_has_default_sfixed32() {
  _has_bits_[1] |= 0x08000000u;
}
inline void TestAllTypesLite::clear_has_default_sfixed32() {
  _has_bits_[1] &= ~0x08000000u;
}
inline void TestAllTypesLite::clear_default_sfixed32() {
  default_sfixed32_ = 49;
  clear_has_default_sfixed32();
}
inline ::google::protobuf::int32 TestAllTypesLite::default_sfixed32() const {
  // @@protoc_insertion_point(field_get:protobuf_unittest.TestAllTypesLite.default_sfixed32)
  return default_sfixed32_;
}
inline void TestAllTypesLite::set_default_sfixed32(::google::protobuf::int32 value) {
  set_has_default_sfixed32();
  default_sfixed32_ = value;
  // @@protoc_insertion_point(field_set:protobuf_unittest.TestAllTypesLite.default_sfixed32)
}

// optional sfixed64 default_sfixed64 = 70 [default = -50];
inline bool TestAllTypesLite::has_default_sfixed64() const {
  return (_has_bits_[1] & 0x10000000u) != 0;
}
inline void TestAllTypesLite::set_has_default_sfixed64() {
  _has_bits_[1] |= 0x10000000u;
}
inline void TestAllTypesLite::clear_has_default_sfixed64() {
  _has_bits_[1] &= ~0x10000000u;
}
inline void TestAllTypesLite::clear_default_sfixed64() {
  default_sfixed64_ = GOOGLE_LONGLONG(-50);
  clear_has_default_sfixed64();
}
inline ::google::protobuf::int64 TestAllTypesLite::default_sfixed64() const {
  // @@protoc_insertion_point(field_get:protobuf_unittest.TestAllTypesLite.default_sfixed64)
  return default_sfixed64_;
}
inline void TestAllTypesLite::set_default_sfixed64(::google::protobuf::int64 value) {
  set_has_default_sfixed64();
  default_sfixed64_ = value;
  // @@protoc_insertion_point(field_set:protobuf_unittest.TestAllTypesLite.default_sfixed64)
}

// optional float default_float = 71 [default = 51.5];
inline bool TestAllTypesLite::has_default_float() const {
  return (_has_bits_[1] & 0x20000000u) != 0;
}
inline void TestAllTypesLite::set_has_default_float() {
  _has_bits_[1] |= 0x20000000u;
}
inline void TestAllTypesLite::clear_has_default_float() {
  _has_bits_[1] &= ~0x20000000u;
}
inline void TestAllTypesLite::clear_default_float() {
  default_float_ = 51.5f;
  clear_has_default_float();
}
inline float TestAllTypesLite::default_float() const {
  // @@protoc_insertion_point(field_get:protobuf_unittest.TestAllTypesLite.default_float)
  return default_float_;
}
inline void TestAllTypesLite::set_default_float(float value) {
  set_has_default_float();
  default_float_ = value;
  // @@protoc_insertion_point(field_set:protobuf_unittest.TestAllTypesLite.default_float)
}

// optional double default_double = 72 [default = 52000];
inline bool TestAllTypesLite::has_default_double() const {
  return (_has_bits_[1] & 0x40000000u) != 0;
}
inline void TestAllTypesLite::set_has_default_double() {
  _has_bits_[1] |= 0x40000000u;
}
inline void TestAllTypesLite::clear_has_default_double() {
  _has_bits_[1] &= ~0x40000000u;
}
inline void TestAllTypesLite::clear_default_double() {
  default_double_ = 52000;
  clear_has_default_double();
}
inline double TestAllTypesLite::default_double() const {
  // @@protoc_insertion_point(field_get:protobuf_unittest.TestAllTypesLite.default_double)
  return default_double_;
}
inline void TestAllTypesLite::set_default_double(double value) {
  set_has_default_double();
  default_double_ = value;
  // @@protoc_insertion_point(field_set:protobuf_unittest.TestAllTypesLite.default_double)
}

// optional bool default_bool = 73 [default = true];
inline bool TestAllTypesLite::has_default_bool() const {
  return (_has_bits_[1] & 0x80000000u) != 0;
}
inline void TestAllTypesLite::set_has_default_bool() {
  _has_bits_[1] |= 0x80000000u;
}
inline void TestAllTypesLite::clear_has_default_bool() {
  _has_bits_[1] &= ~0x80000000u;
}
inline void TestAllTypesLite::clear_default_bool() {
  default_bool_ = true;
  clear_has_default_bool();
}
inline bool TestAllTypesLite::default_bool() const {
  // @@protoc_insertion_point(field_get:protobuf_unittest.TestAllTypesLite.default_bool)
  return default_bool_;
}
inline void TestAllTypesLite::set_default_bool(bool value) {
  set_has_default_bool();
  default_bool_ = value;
  // @@protoc_insertion_point(field_set:protobuf_unittest.TestAllTypesLite.default_bool)
}

// optional string default_string = 74 [default = "hello"];
inline bool TestAllTypesLite::has_default_string() const {
  return (_has_bits_[2] & 0x00000001u) != 0;
}
inline void TestAllTypesLite::set_has_default_string() {
  _has_bits_[2] |= 0x00000001u;
}
inline void TestAllTypesLite::clear_has_default_string() {
  _has_bits_[2] &= ~0x00000001u;
}
inline void TestAllTypesLite::clear_default_string() {
  default_string_.ClearToDefaultNoArena(_default_default_string_);
  clear_has_default_string();
}
inline const ::std::string& TestAllTypesLite::default_string() const {
  // @@protoc_insertion_point(field_get:protobuf_unittest.TestAllTypesLite.default_string)
  return default_string_.GetNoArena(_default_default_string_);
}
inline void TestAllTypesLite::set_default_string(const ::std::string& value) {
  set_has_default_string();
  default_string_.SetNoArena(_default_default_string_, value);
  // @@protoc_insertion_point(field_set:protobuf_unittest.TestAllTypesLite.default_string)
}
inline void TestAllTypesLite::set_default_string(const char* value) {
  set_has_default_string();
  default_string_.SetNoArena(_default_default_string_, ::std::string(value));
  // @@protoc_insertion_point(field_set_char:protobuf_unittest.TestAllTypesLite.default_string)
}
inline void TestAllTypesLite::set_default_string(const char* value, size_t size) {
  set_has_default_string();
  default_string_.SetNoArena(_default_default_string_,
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:protobuf_unittest.TestAllTypesLite.default_string)
}
inline ::std::string* TestAllTypesLite::mutable_default_string() {
  set_has_default_string();
  // @@protoc_insertion_point(field_mutable:protobuf_unittest.TestAllTypesLite.default_string)
  return default_string_.MutableNoArena(_default_default_string_);
}
inline ::std::string* TestAllTypesLite::release_default_string() {
  // @@protoc_insertion_point(field_release:protobuf_unittest.TestAllTypesLite.default_string)
  clear_has_default_string();
  return default_string_.ReleaseNoArena(_default_default_string_);
}
inline void TestAllTypesLite::set_allocated_default_string(::std::string* default_string) {
  if (default_string != NULL) {
    set_has_default_string();
  } else {
    clear_has_default_string();
  }
  default_string_.SetAllocatedNoArena(_default_default_string_, default_string);
  // @@protoc_insertion_point(field_set_allocated:protobuf_unittest.TestAllTypesLite.default_string)
}

// optional bytes default_bytes = 75 [default = "world"];
inline bool TestAllTypesLite::has_default_bytes() const {
  return (_has_bits_[2] & 0x00000002u) != 0;
}
inline void TestAllTypesLite::set_has_default_bytes() {
  _has_bits_[2] |= 0x00000002u;
}
inline void TestAllTypesLite::clear_has_default_bytes() {
  _has_bits_[2] &= ~0x00000002u;
}
inline void TestAllTypesLite::clear_default_bytes() {
  default_bytes_.ClearToDefaultNoArena(_default_default_bytes_);
  clear_has_default_bytes();
}
inline const ::std::string& TestAllTypesLite::default_bytes() const {
  // @@protoc_insertion_point(field_get:protobuf_unittest.TestAllTypesLite.default_bytes)
  return default_bytes_.GetNoArena(_default_default_bytes_);
}
inline void TestAllTypesLite::set_default_bytes(const ::std::string& value) {
  set_has_default_bytes();
  default_bytes_.SetNoArena(_default_default_bytes_, value);
  // @@protoc_insertion_point(field_set:protobuf_unittest.TestAllTypesLite.default_bytes)
}
inline void TestAllTypesLite::set_default_bytes(const char* value) {
  set_has_default_bytes();
  default_bytes_.SetNoArena(_default_default_bytes_, ::std::string(value));
  // @@protoc_insertion_point(field_set_char:protobuf_unittest.TestAllTypesLite.default_bytes)
}
inline void TestAllTypesLite::set_default_bytes(const void* value, size_t size) {
  set_has_default_bytes();
  default_bytes_.SetNoArena(_default_default_bytes_,
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:protobuf_unittest.TestAllTypesLite.default_bytes)
}
inline ::std::string* TestAllTypesLite::mutable_default_bytes() {
  set_has_default_bytes();
  // @@protoc_insertion_point(field_mutable:protobuf_unittest.TestAllTypesLite.default_bytes)
  return default_bytes_.MutableNoArena(_default_default_bytes_);
}
inline ::std::string* TestAllTypesLite::release_default_bytes() {
  // @@protoc_insertion_point(field_release:protobuf_unittest.TestAllTypesLite.default_bytes)
  clear_has_default_bytes();
  return default_bytes_.ReleaseNoArena(_default_default_bytes_);
}
inline void TestAllTypesLite::set_allocated_default_bytes(::std::string* default_bytes) {
  if (default_bytes != NULL) {
    set_has_default_bytes();
  } else {
    clear_has_default_bytes();
  }
  default_bytes_.SetAllocatedNoArena(_default_default_bytes_, default_bytes);
  // @@protoc_insertion_point(field_set_allocated:protobuf_unittest.TestAllTypesLite.default_bytes)
}

// optional .protobuf_unittest.TestAllTypesLite.NestedEnum default_nested_enum = 81 [default = BAR];
inline bool TestAllTypesLite::has_default_nested_enum() const {
  return (_has_bits_[2] & 0x00000004u) != 0;
}
inline void TestAllTypesLite::set_has_default_nested_enum() {
  _has_bits_[2] |= 0x00000004u;
}
inline void TestAllTypesLite::clear_has_default_nested_enum() {
  _has_bits_[2] &= ~0x00000004u;
}
inline void TestAllTypesLite::clear_default_nested_enum() {
  default_nested_enum_ = 2;
  clear_has_default_nested_enum();
}
inline ::protobuf_unittest::TestAllTypesLite_NestedEnum TestAllTypesLite::default_nested_enum() const {
  // @@protoc_insertion_point(field_get:protobuf_unittest.TestAllTypesLite.default_nested_enum)
  return static_cast< ::protobuf_unittest::TestAllTypesLite_NestedEnum >(default_nested_enum_);
}
inline void TestAllTypesLite::set_default_nested_enum(::protobuf_unittest::TestAllTypesLite_NestedEnum value) {
  assert(::protobuf_unittest::TestAllTypesLite_NestedEnum_IsValid(value));
  set_has_default_nested_enum();
  default_nested_enum_ = value;
  // @@protoc_insertion_point(field_set:protobuf_unittest.TestAllTypesLite.default_nested_enum)
}

// optional .protobuf_unittest.ForeignEnumLite default_foreign_enum = 82 [default = FOREIGN_LITE_BAR];
inline bool TestAllTypesLite::has_default_foreign_enum() const {
  return (_has_bits_[2] & 0x00000008u) != 0;
}
inline void TestAllTypesLite::set_has_default_foreign_enum() {
  _has_bits_[2] |= 0x00000008u;
}
inline void TestAllTypesLite::clear_has_default_foreign_enum() {
  _has_bits_[2] &= ~0x00000008u;
}
inline void TestAllTypesLite::clear_default_foreign_enum() {
  default_foreign_enum_ = 5;
  clear_has_default_foreign_enum();
}
inline ::protobuf_unittest::ForeignEnumLite TestAllTypesLite::default_foreign_enum() const {
  // @@protoc_insertion_point(field_get:protobuf_unittest.TestAllTypesLite.default_foreign_enum)
  return static_cast< ::protobuf_unittest::ForeignEnumLite >(default_foreign_enum_);
}
inline void TestAllTypesLite::set_default_foreign_enum(::protobuf_unittest::ForeignEnumLite value) {
  assert(::protobuf_unittest::ForeignEnumLite_IsValid(value));
  set_has_default_foreign_enum();
  default_foreign_enum_ = value;
  // @@protoc_insertion_point(field_set:protobuf_unittest.TestAllTypesLite.default_foreign_enum)
}

// optional .protobuf_unittest_import.ImportEnumLite default_import_enum = 83 [default = IMPORT_LITE_BAR];
inline bool TestAllTypesLite::has_default_import_enum() const {
  return (_has_bits_[2] & 0x00000010u) != 0;
}
inline void TestAllTypesLite::set_has_default_import_enum() {
  _has_bits_[2] |= 0x00000010u;
}
inline void TestAllTypesLite::clear_has_default_import_enum() {
  _has_bits_[2] &= ~0x00000010u;
}
inline void TestAllTypesLite::clear_default_import_enum() {
  default_import_enum_ = 8;
  clear_has_default_import_enum();
}
inline ::protobuf_unittest_import::ImportEnumLite TestAllTypesLite::default_import_enum() const {
  // @@protoc_insertion_point(field_get:protobuf_unittest.TestAllTypesLite.default_import_enum)
  return static_cast< ::protobuf_unittest_import::ImportEnumLite >(default_import_enum_);
}
inline void TestAllTypesLite::set_default_import_enum(::protobuf_unittest_import::ImportEnumLite value) {
  assert(::protobuf_unittest_import::ImportEnumLite_IsValid(value));
  set_has_default_import_enum();
  default_import_enum_ = value;
  // @@protoc_insertion_point(field_set:protobuf_unittest.TestAllTypesLite.default_import_enum)
}

// optional string default_string_piece = 84 [default = "abc", ctype = STRING_PIECE];
inline bool TestAllTypesLite::has_default_string_piece() const {
  return (_has_bits_[2] & 0x00000020u) != 0;
}
inline void TestAllTypesLite::set_has_default_string_piece() {
  _has_bits_[2] |= 0x00000020u;
}
inline void TestAllTypesLite::clear_has_default_string_piece() {
  _has_bits_[2] &= ~0x00000020u;
}
inline void TestAllTypesLite::clear_default_string_piece() {
  default_string_piece_.ClearToDefaultNoArena(_default_default_string_piece_);
  clear_has_default_string_piece();
}
inline const ::std::string& TestAllTypesLite::default_string_piece() const {
  // @@protoc_insertion_point(field_get:protobuf_unittest.TestAllTypesLite.default_string_piece)
  return default_string_piece_.GetNoArena(_default_default_string_piece_);
}
inline void TestAllTypesLite::set_default_string_piece(const ::std::string& value) {
  set_has_default_string_piece();
  default_string_piece_.SetNoArena(_default_default_string_piece_, value);
  // @@protoc_insertion_point(field_set:protobuf_unittest.TestAllTypesLite.default_string_piece)
}
inline void TestAllTypesLite::set_default_string_piece(const char* value) {
  set_has_default_string_piece();
  default_string_piece_.SetNoArena(_default_default_string_piece_, ::std::string(value));
  // @@protoc_insertion_point(field_set_char:protobuf_unittest.TestAllTypesLite.default_string_piece)
}
inline void TestAllTypesLite::set_default_string_piece(const char* value, size_t size) {
  set_has_default_string_piece();
  default_string_piece_.SetNoArena(_default_default_string_piece_,
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:protobuf_unittest.TestAllTypesLite.default_string_piece)
}
inline ::std::string* TestAllTypesLite::mutable_default_string_piece() {
  set_has_default_string_piece();
  // @@protoc_insertion_point(field_mutable:protobuf_unittest.TestAllTypesLite.default_string_piece)
  return default_string_piece_.MutableNoArena(_default_default_string_piece_);
}
inline ::std::string* TestAllTypesLite::release_default_string_piece() {
  // @@protoc_insertion_point(field_release:protobuf_unittest.TestAllTypesLite.default_string_piece)
  clear_has_default_string_piece();
  return default_string_piece_.ReleaseNoArena(_default_default_string_piece_);
}
inline void TestAllTypesLite::set_allocated_default_string_piece(::std::string* default_string_piece) {
  if (default_string_piece != NULL) {
    set_has_default_string_piece();
  } else {
    clear_has_default_string_piece();
  }
  default_string_piece_.SetAllocatedNoArena(_default_default_string_piece_, default_string_piece);
  // @@protoc_insertion_point(field_set_allocated:protobuf_unittest.TestAllTypesLite.default_string_piece)
}

// optional string default_cord = 85 [default = "123", ctype = CORD];
inline bool TestAllTypesLite::has_default_cord() const {
  return (_has_bits_[2] & 0x00000040u) != 0;
}
inline void TestAllTypesLite::set_has_default_cord() {
  _has_bits_[2] |= 0x00000040u;
}
inline void TestAllTypesLite::clear_has_default_cord() {
  _has_bits_[2] &= ~0x00000040u;
}
inline void TestAllTypesLite::clear_default_cord() {
  default_cord_.ClearToDefaultNoArena(_default_default_cord_);
  clear_has_default_cord();
}
inline const ::std::string& TestAllTypesLite::default_cord() const {
  // @@protoc_insertion_point(field_get:protobuf_unittest.TestAllTypesLite.default_cord)
  return default_cord_.GetNoArena(_default_default_cord_);
}
inline void TestAllTypesLite::set_default_cord(const ::std::string& value) {
  set_has_default_cord();
  default_cord_.SetNoArena(_default_default_cord_, value);
  // @@protoc_insertion_point(field_set:protobuf_unittest.TestAllTypesLite.default_cord)
}
inline void TestAllTypesLite::set_default_cord(const char* value) {
  set_has_default_cord();
  default_cord_.SetNoArena(_default_default_cord_, ::std::string(value));
  // @@protoc_insertion_point(field_set_char:protobuf_unittest.TestAllTypesLite.default_cord)
}
inline void TestAllTypesLite::set_default_cord(const char* value, size_t size) {
  set_has_default_cord();
  default_cord_.SetNoArena(_default_default_cord_,
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:protobuf_unittest.TestAllTypesLite.default_cord)
}
inline ::std::string* TestAllTypesLite::mutable_default_cord() {
  set_has_default_cord();
  // @@protoc_insertion_point(field_mutable:protobuf_unittest.TestAllTypesLite.default_cord)
  return default_cord_.MutableNoArena(_default_default_cord_);
}
inline ::std::string* TestAllTypesLite::release_default_cord() {
  // @@protoc_insertion_point(field_release:protobuf_unittest.TestAllTypesLite.default_cord)
  clear_has_default_cord();
  return default_cord_.ReleaseNoArena(_default_default_cord_);
}
inline void TestAllTypesLite::set_allocated_default_cord(::std::string* default_cord) {
  if (default_cord != NULL) {
    set_has_default_cord();
  } else {
    clear_has_default_cord();
  }
  default_cord_.SetAllocatedNoArena(_default_default_cord_, default_cord);
  // @@protoc_insertion_point(field_set_allocated:protobuf_unittest.TestAllTypesLite.default_cord)
}

// optional uint32 oneof_uint32 = 111;
inline bool TestAllTypesLite::has_oneof_uint32() const {
  return oneof_field_case() == kOneofUint32;
}
inline void TestAllTypesLite::set_has_oneof_uint32() {
  _oneof_case_[0] = kOneofUint32;
}
inline void TestAllTypesLite::clear_oneof_uint32() {
  if (has_oneof_uint32()) {
    oneof_field_.oneof_uint32_ = 0u;
    clear_has_oneof_field();
  }
}
inline ::google::protobuf::uint32 TestAllTypesLite::oneof_uint32() const {
  // @@protoc_insertion_point(field_get:protobuf_unittest.TestAllTypesLite.oneof_uint32)
  if (has_oneof_uint32()) {
    return oneof_field_.oneof_uint32_;
  }
  return 0u;
}
inline void TestAllTypesLite::set_oneof_uint32(::google::protobuf::uint32 value) {
  if (!has_oneof_uint32()) {
    clear_oneof_field();
    set_has_oneof_uint32();
  }
  oneof_field_.oneof_uint32_ = value;
  // @@protoc_insertion_point(field_set:protobuf_unittest.TestAllTypesLite.oneof_uint32)
}

// optional .protobuf_unittest.TestAllTypesLite.NestedMessage oneof_nested_message = 112;
inline bool TestAllTypesLite::has_oneof_nested_message() const {
  return oneof_field_case() == kOneofNestedMessage;
}
inline void TestAllTypesLite::set_has_oneof_nested_message() {
  _oneof_case_[0] = kOneofNestedMessage;
}
inline void TestAllTypesLite::clear_oneof_nested_message() {
  if (has_oneof_nested_message()) {
    delete oneof_field_.oneof_nested_message_;
    clear_has_oneof_field();
  }
}
inline  const ::protobuf_unittest::TestAllTypesLite_NestedMessage& TestAllTypesLite::oneof_nested_message() const {
  // @@protoc_insertion_point(field_get:protobuf_unittest.TestAllTypesLite.oneof_nested_message)
  return has_oneof_nested_message()
      ? *oneof_field_.oneof_nested_message_
      : ::protobuf_unittest::TestAllTypesLite_NestedMessage::default_instance();
}
inline ::protobuf_unittest::TestAllTypesLite_NestedMessage* TestAllTypesLite::mutable_oneof_nested_message() {
  if (!has_oneof_nested_message()) {
    clear_oneof_field();
    set_has_oneof_nested_message();
    oneof_field_.oneof_nested_message_ = new ::protobuf_unittest::TestAllTypesLite_NestedMessage;
  }
  // @@protoc_insertion_point(field_mutable:protobuf_unittest.TestAllTypesLite.oneof_nested_message)
  return oneof_field_.oneof_nested_message_;
}
inline ::protobuf_unittest::TestAllTypesLite_NestedMessage* TestAllTypesLite::release_oneof_nested_message() {
  // @@protoc_insertion_point(field_release:protobuf_unittest.TestAllTypesLite.oneof_nested_message)
  if (has_oneof_nested_message()) {
    clear_has_oneof_field();
    ::protobuf_unittest::TestAllTypesLite_NestedMessage* temp = oneof_field_.oneof_nested_message_;
    oneof_field_.oneof_nested_message_ = NULL;
    return temp;
  } else {
    return NULL;
  }
}
inline void TestAllTypesLite::set_allocated_oneof_nested_message(::protobuf_unittest::TestAllTypesLite_NestedMessage* oneof_nested_message) {
  clear_oneof_field();
  if (oneof_nested_message) {
    set_has_oneof_nested_message();
    oneof_field_.oneof_nested_message_ = oneof_nested_message;
  }
  // @@protoc_insertion_point(field_set_allocated:protobuf_unittest.TestAllTypesLite.oneof_nested_message)
}

// optional string oneof_string = 113;
inline bool TestAllTypesLite::has_oneof_string() const {
  return oneof_field_case() == kOneofString;
}
inline void TestAllTypesLite::set_has_oneof_string() {
  _oneof_case_[0] = kOneofString;
}
inline void TestAllTypesLite::clear_oneof_string() {
  if (has_oneof_string()) {
    oneof_field_.oneof_string_.DestroyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
    clear_has_oneof_field();
  }
}
inline const ::std::string& TestAllTypesLite::oneof_string() const {
  // @@protoc_insertion_point(field_get:protobuf_unittest.TestAllTypesLite.oneof_string)
  if (has_oneof_string()) {
    return oneof_field_.oneof_string_.GetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  }
  return *&::google::protobuf::internal::GetEmptyStringAlreadyInited();
}
inline void TestAllTypesLite::set_oneof_string(const ::std::string& value) {
  // @@protoc_insertion_point(field_set:protobuf_unittest.TestAllTypesLite.oneof_string)
  if (!has_oneof_string()) {
    clear_oneof_field();
    set_has_oneof_string();
    oneof_field_.oneof_string_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  }
  oneof_field_.oneof_string_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:protobuf_unittest.TestAllTypesLite.oneof_string)
}
inline void TestAllTypesLite::set_oneof_string(const char* value) {
  if (!has_oneof_string()) {
    clear_oneof_field();
    set_has_oneof_string();
    oneof_field_.oneof_string_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  }
  oneof_field_.oneof_string_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(value));
  // @@protoc_insertion_point(field_set_char:protobuf_unittest.TestAllTypesLite.oneof_string)
}
inline void TestAllTypesLite::set_oneof_string(const char* value, size_t size) {
  if (!has_oneof_string()) {
    clear_oneof_field();
    set_has_oneof_string();
    oneof_field_.oneof_string_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  }
  oneof_field_.oneof_string_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(
      reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:protobuf_unittest.TestAllTypesLite.oneof_string)
}
inline ::std::string* TestAllTypesLite::mutable_oneof_string() {
  if (!has_oneof_string()) {
    clear_oneof_field();
    set_has_oneof_string();
    oneof_field_.oneof_string_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  }
  // @@protoc_insertion_point(field_mutable:protobuf_unittest.TestAllTypesLite.oneof_string)
  return oneof_field_.oneof_string_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline ::std::string* TestAllTypesLite::release_oneof_string() {
  // @@protoc_insertion_point(field_release:protobuf_unittest.TestAllTypesLite.oneof_string)
  if (has_oneof_string()) {
    clear_has_oneof_field();
    return oneof_field_.oneof_string_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  } else {
    return NULL;
  }
}
inline void TestAllTypesLite::set_allocated_oneof_string(::std::string* oneof_string) {
  if (!has_oneof_string()) {
    oneof_field_.oneof_string_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  }
  clear_oneof_field();
  if (oneof_string != NULL) {
    set_has_oneof_string();
    oneof_field_.oneof_string_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
        oneof_string);
  }
  // @@protoc_insertion_point(field_set_allocated:protobuf_unittest.TestAllTypesLite.oneof_string)
}

// optional bytes oneof_bytes = 114;
inline bool TestAllTypesLite::has_oneof_bytes() const {
  return oneof_field_case() == kOneofBytes;
}
inline void TestAllTypesLite::set_has_oneof_bytes() {
  _oneof_case_[0] = kOneofBytes;
}
inline void TestAllTypesLite::clear_oneof_bytes() {
  if (has_oneof_bytes()) {
    oneof_field_.oneof_bytes_.DestroyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
    clear_has_oneof_field();
  }
}
inline const ::std::string& TestAllTypesLite::oneof_bytes() const {
  // @@protoc_insertion_point(field_get:protobuf_unittest.TestAllTypesLite.oneof_bytes)
  if (has_oneof_bytes()) {
    return oneof_field_.oneof_bytes_.GetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  }
  return *&::google::protobuf::internal::GetEmptyStringAlreadyInited();
}
inline void TestAllTypesLite::set_oneof_bytes(const ::std::string& value) {
  // @@protoc_insertion_point(field_set:protobuf_unittest.TestAllTypesLite.oneof_bytes)
  if (!has_oneof_bytes()) {
    clear_oneof_field();
    set_has_oneof_bytes();
    oneof_field_.oneof_bytes_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  }
  oneof_field_.oneof_bytes_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:protobuf_unittest.TestAllTypesLite.oneof_bytes)
}
inline void TestAllTypesLite::set_oneof_bytes(const char* value) {
  if (!has_oneof_bytes()) {
    clear_oneof_field();
    set_has_oneof_bytes();
    oneof_field_.oneof_bytes_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  }
  oneof_field_.oneof_bytes_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(value));
  // @@protoc_insertion_point(field_set_char:protobuf_unittest.TestAllTypesLite.oneof_bytes)
}
inline void TestAllTypesLite::set_oneof_bytes(const void* value, size_t size) {
  if (!has_oneof_bytes()) {
    clear_oneof_field();
    set_has_oneof_bytes();
    oneof_field_.oneof_bytes_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  }
  oneof_field_.oneof_bytes_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(
      reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:protobuf_unittest.TestAllTypesLite.oneof_bytes)
}
inline ::std::string* TestAllTypesLite::mutable_oneof_bytes() {
  if (!has_oneof_bytes()) {
    clear_oneof_field();
    set_has_oneof_bytes();
    oneof_field_.oneof_bytes_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  }
  // @@protoc_insertion_point(field_mutable:protobuf_unittest.TestAllTypesLite.oneof_bytes)
  return oneof_field_.oneof_bytes_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline ::std::string* TestAllTypesLite::release_oneof_bytes() {
  // @@protoc_insertion_point(field_release:protobuf_unittest.TestAllTypesLite.oneof_bytes)
  if (has_oneof_bytes()) {
    clear_has_oneof_field();
    return oneof_field_.oneof_bytes_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  } else {
    return NULL;
  }
}
inline void TestAllTypesLite::set_allocated_oneof_bytes(::std::string* oneof_bytes) {
  if (!has_oneof_bytes()) {
    oneof_field_.oneof_bytes_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  }
  clear_oneof_field();
  if (oneof_bytes != NULL) {
    set_has_oneof_bytes();
    oneof_field_.oneof_bytes_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
        oneof_bytes);
  }
  // @@protoc_insertion_point(field_set_allocated:protobuf_unittest.TestAllTypesLite.oneof_bytes)
}

// optional .protobuf_unittest.TestAllTypesLite.NestedMessage oneof_lazy_nested_message = 115 [lazy = true];
inline bool TestAllTypesLite::has_oneof_lazy_nested_message() const {
  return oneof_field_case() == kOneofLazyNestedMessage;
}
inline void TestAllTypesLite::set_has_oneof_lazy_nested_message() {
  _oneof_case_[0] = kOneofLazyNestedMessage;
}
inline void TestAllTypesLite::clear_oneof_lazy_nested_message() {
  if (has_oneof_lazy_nested_message()) {
    delete oneof_field_.oneof_lazy_nested_message_;
    clear_has_oneof_field();
  }
}
inline  const ::protobuf_unittest::TestAllTypesLite_NestedMessage& TestAllTypesLite::oneof_lazy_nested_message() const {
  // @@protoc_insertion_point(field_get:protobuf_unittest.TestAllTypesLite.oneof_lazy_nested_message)
  return has_oneof_lazy_nested_message()
      ? *oneof_field_.oneof_lazy_nested_message_
      : ::protobuf_unittest::TestAllTypesLite_NestedMessage::default_instance();
}
inline ::protobuf_unittest::TestAllTypesLite_NestedMessage* TestAllTypesLite::mutable_oneof_lazy_nested_message() {
  if (!has_oneof_lazy_nested_message()) {
    clear_oneof_field();
    set_has_oneof_lazy_nested_message();
    oneof_field_.oneof_lazy_nested_message_ = new ::protobuf_unittest::TestAllTypesLite_NestedMessage;
  }
  // @@protoc_insertion_point(field_mutable:protobuf_unittest.TestAllTypesLite.oneof_lazy_nested_message)
  return oneof_field_.oneof_lazy_nested_message_;
}
inline ::protobuf_unittest::TestAllTypesLite_NestedMessage* TestAllTypesLite::release_oneof_lazy_nested_message() {
  // @@protoc_insertion_point(field_release:protobuf_unittest.TestAllTypesLite.oneof_lazy_nested_message)
  if (has_oneof_lazy_nested_message()) {
    clear_has_oneof_field();
    ::protobuf_unittest::TestAllTypesLite_NestedMessage* temp = oneof_field_.oneof_lazy_nested_message_;
    oneof_field_.oneof_lazy_nested_message_ = NULL;
    return temp;
  } else {
    return NULL;
  }
}
inline void TestAllTypesLite::set_allocated_oneof_lazy_nested_message(::protobuf_unittest::TestAllTypesLite_NestedMessage* oneof_lazy_nested_message) {
  clear_oneof_field();
  if (oneof_lazy_nested_message) {
    set_has_oneof_lazy_nested_message();
    oneof_field_.oneof_lazy_nested_message_ = oneof_lazy_nested_message;
  }
  // @@protoc_insertion_point(field_set_allocated:protobuf_unittest.TestAllTypesLite.oneof_lazy_nested_message)
}

inline bool TestAllTypesLite::has_oneof_field() const {
  return oneof_field_case() != ONEOF_FIELD_NOT_SET;
}
inline void TestAllTypesLite::clear_has_oneof_field() {
  _oneof_case_[0] = ONEOF_FIELD_NOT_SET;
}
inline TestAllTypesLite::OneofFieldCase TestAllTypesLite::oneof_field_case() const {
  return TestAllTypesLite::OneofFieldCase(_oneof_case_[0]);
}
inline const TestAllTypesLite* TestAllTypesLite::internal_default_instance() {
  return &TestAllTypesLite_default_instance_.get();
}
// -------------------------------------------------------------------

// ForeignMessageLite

// optional int32 c = 1;
inline bool ForeignMessageLite::has_c() const {
  return (_has_bits_[0] & 0x00000001u) != 0;
}
inline void ForeignMessageLite::set_has_c() {
  _has_bits_[0] |= 0x00000001u;
}
inline void ForeignMessageLite::clear_has_c() {
  _has_bits_[0] &= ~0x00000001u;
}
inline void ForeignMessageLite::clear_c() {
  c_ = 0;
  clear_has_c();
}
inline ::google::protobuf::int32 ForeignMessageLite::c() const {
  // @@protoc_insertion_point(field_get:protobuf_unittest.ForeignMessageLite.c)
  return c_;
}
inline void ForeignMessageLite::set_c(::google::protobuf::int32 value) {
  set_has_c();
  c_ = value;
  // @@protoc_insertion_point(field_set:protobuf_unittest.ForeignMessageLite.c)
}

inline const ForeignMessageLite* ForeignMessageLite::internal_default_instance() {
  return &ForeignMessageLite_default_instance_.get();
}
// -------------------------------------------------------------------

// TestPackedTypesLite

// repeated int32 packed_int32 = 90 [packed = true];
inline int TestPackedTypesLite::packed_int32_size() const {
  return packed_int32_.size();
}
inline void TestPackedTypesLite::clear_packed_int32() {
  packed_int32_.Clear();
}
inline ::google::protobuf::int32 TestPackedTypesLite::packed_int32(int index) const {
  // @@protoc_insertion_point(field_get:protobuf_unittest.TestPackedTypesLite.packed_int32)
  return packed_int32_.Get(index);
}
inline void TestPackedTypesLite::set_packed_int32(int index, ::google::protobuf::int32 value) {
  packed_int32_.Set(index, value);
  // @@protoc_insertion_point(field_set:protobuf_unittest.TestPackedTypesLite.packed_int32)
}
inline void TestPackedTypesLite::add_packed_int32(::google::protobuf::int32 value) {
  packed_int32_.Add(value);
  // @@protoc_insertion_point(field_add:protobuf_unittest.TestPackedTypesLite.packed_int32)
}
inline const ::google::protobuf::RepeatedField< ::google::protobuf::int32 >&
TestPackedTypesLite::packed_int32() const {
  // @@protoc_insertion_point(field_list:protobuf_unittest.TestPackedTypesLite.packed_int32)
  return packed_int32_;
}
inline ::google::protobuf::RepeatedField< ::google::protobuf::int32 >*
TestPackedTypesLite::mutable_packed_int32() {
  // @@protoc_insertion_point(field_mutable_list:protobuf_unittest.TestPackedTypesLite.packed_int32)
  return &packed_int32_;
}

// repeated int64 packed_int64 = 91 [packed = true];
inline int TestPackedTypesLite::packed_int64_size() const {
  return packed_int64_.size();
}
inline void TestPackedTypesLite::clear_packed_int64() {
  packed_int64_.Clear();
}
inline ::google::protobuf::int64 TestPackedTypesLite::packed_int64(int index) const {
  // @@protoc_insertion_point(field_get:protobuf_unittest.TestPackedTypesLite.packed_int64)
  return packed_int64_.Get(index);
}
inline void TestPackedTypesLite::set_packed_int64(int index, ::google::protobuf::int64 value) {
  packed_int64_.Set(index, value);
  // @@protoc_insertion_point(field_set:protobuf_unittest.TestPackedTypesLite.packed_int64)
}
inline void TestPackedTypesLite::add_packed_int64(::google::protobuf::int64 value) {
  packed_int64_.Add(value);
  // @@protoc_insertion_point(field_add:protobuf_unittest.TestPackedTypesLite.packed_int64)
}
inline const ::google::protobuf::RepeatedField< ::google::protobuf::int64 >&
TestPackedTypesLite::packed_int64() const {
  // @@protoc_insertion_point(field_list:protobuf_unittest.TestPackedTypesLite.packed_int64)
  return packed_int64_;
}
inline ::google::protobuf::RepeatedField< ::google::protobuf::int64 >*
TestPackedTypesLite::mutable_packed_int64() {
  // @@protoc_insertion_point(field_mutable_list:protobuf_unittest.TestPackedTypesLite.packed_int64)
  return &packed_int64_;
}

// repeated uint32 packed_uint32 = 92 [packed = true];
inline int TestPackedTypesLite::packed_uint32_size() const {
  return packed_uint32_.size();
}
inline void TestPackedTypesLite::clear_packed_uint32() {
  packed_uint32_.Clear();
}
inline ::google::protobuf::uint32 TestPackedTypesLite::packed_uint32(int index) const {
  // @@protoc_insertion_point(field_get:protobuf_unittest.TestPackedTypesLite.packed_uint32)
  return packed_uint32_.Get(index);
}
inline void TestPackedTypesLite::set_packed_uint32(int index, ::google::protobuf::uint32 value) {
  packed_uint32_.Set(index, value);
  // @@protoc_insertion_point(field_set:protobuf_unittest.TestPackedTypesLite.packed_uint32)
}
inline void TestPackedTypesLite::add_packed_uint32(::google::protobuf::uint32 value) {
  packed_uint32_.Add(value);
  // @@protoc_insertion_point(field_add:protobuf_unittest.TestPackedTypesLite.packed_uint32)
}
inline const ::google::protobuf::RepeatedField< ::google::protobuf::uint32 >&
TestPackedTypesLite::packed_uint32() const {
  // @@protoc_insertion_point(field_list:protobuf_unittest.TestPackedTypesLite.packed_uint32)
  return packed_uint32_;
}
inline ::google::protobuf::RepeatedField< ::google::protobuf::uint32 >*
TestPackedTypesLite::mutable_packed_uint32() {
  // @@protoc_insertion_point(field_mutable_list:protobuf_unittest.TestPackedTypesLite.packed_uint32)
  return &packed_uint32_;
}

// repeated uint64 packed_uint64 = 93 [packed = true];
inline int TestPackedTypesLite::packed_uint64_size() const {
  return packed_uint64_.size();
}
inline void TestPackedTypesLite::clear_packed_uint64() {
  packed_uint64_.Clear();
}
inline ::google::protobuf::uint64 TestPackedTypesLite::packed_uint64(int index) const {
  // @@protoc_insertion_point(field_get:protobuf_unittest.TestPackedTypesLite.packed_uint64)
  return packed_uint64_.Get(index);
}
inline void TestPackedTypesLite::set_packed_uint64(int index, ::google::protobuf::uint64 value) {
  packed_uint64_.Set(index, value);
  // @@protoc_insertion_point(field_set:protobuf_unittest.TestPackedTypesLite.packed_uint64)
}
inline void TestPackedTypesLite::add_packed_uint64(::google::protobuf::uint64 value) {
  packed_uint64_.Add(value);
  // @@protoc_insertion_point(field_add:protobuf_unittest.TestPackedTypesLite.packed_uint64)
}
inline const ::google::protobuf::RepeatedField< ::google::protobuf::uint64 >&
TestPackedTypesLite::packed_uint64() const {
  // @@protoc_insertion_point(field_list:protobuf_unittest.TestPackedTypesLite.packed_uint64)
  return packed_uint64_;
}
inline ::google::protobuf::RepeatedField< ::google::protobuf::uint64 >*
TestPackedTypesLite::mutable_packed_uint64() {
  // @@protoc_insertion_point(field_mutable_list:protobuf_unittest.TestPackedTypesLite.packed_uint64)
  return &packed_uint64_;
}

// repeated sint32 packed_sint32 = 94 [packed = true];
inline int TestPackedTypesLite::packed_sint32_size() const {
  return packed_sint32_.size();
}
inline void TestPackedTypesLite::clear_packed_sint32() {
  packed_sint32_.Clear();
}
inline ::google::protobuf::int32 TestPackedTypesLite::packed_sint32(int index) const {
  // @@protoc_insertion_point(field_get:protobuf_unittest.TestPackedTypesLite.packed_sint32)
  return packed_sint32_.Get(index);
}
inline void TestPackedTypesLite::set_packed_sint32(int index, ::google::protobuf::int32 value) {
  packed_sint32_.Set(index, value);
  // @@protoc_insertion_point(field_set:protobuf_unittest.TestPackedTypesLite.packed_sint32)
}
inline void TestPackedTypesLite::add_packed_sint32(::google::protobuf::int32 value) {
  packed_sint32_.Add(value);
  // @@protoc_insertion_point(field_add:protobuf_unittest.TestPackedTypesLite.packed_sint32)
}
inline const ::google::protobuf::RepeatedField< ::google::protobuf::int32 >&
TestPackedTypesLite::packed_sint32() const {
  // @@protoc_insertion_point(field_list:protobuf_unittest.TestPackedTypesLite.packed_sint32)
  return packed_sint32_;
}
inline ::google::protobuf::RepeatedField< ::google::protobuf::int32 >*
TestPackedTypesLite::mutable_packed_sint32() {
  // @@protoc_insertion_point(field_mutable_list:protobuf_unittest.TestPackedTypesLite.packed_sint32)
  return &packed_sint32_;
}

// repeated sint64 packed_sint64 = 95 [packed = true];
inline int TestPackedTypesLite::packed_sint64_size() const {
  return packed_sint64_.size();
}
inline void TestPackedTypesLite::clear_packed_sint64() {
  packed_sint64_.Clear();
}
inline ::google::protobuf::int64 TestPackedTypesLite::packed_sint64(int index) const {
  // @@protoc_insertion_point(field_get:protobuf_unittest.TestPackedTypesLite.packed_sint64)
  return packed_sint64_.Get(index);
}
inline void TestPackedTypesLite::set_packed_sint64(int index, ::google::protobuf::int64 value) {
  packed_sint64_.Set(index, value);
  // @@protoc_insertion_point(field_set:protobuf_unittest.TestPackedTypesLite.packed_sint64)
}
inline void TestPackedTypesLite::add_packed_sint64(::google::protobuf::int64 value) {
  packed_sint64_.Add(value);
  // @@protoc_insertion_point(field_add:protobuf_unittest.TestPackedTypesLite.packed_sint64)
}
inline const ::google::protobuf::RepeatedField< ::google::protobuf::int64 >&
TestPackedTypesLite::packed_sint64() const {
  // @@protoc_insertion_point(field_list:protobuf_unittest.TestPackedTypesLite.packed_sint64)
  return packed_sint64_;
}
inline ::google::protobuf::RepeatedField< ::google::protobuf::int64 >*
TestPackedTypesLite::mutable_packed_sint64() {
  // @@protoc_insertion_point(field_mutable_list:protobuf_unittest.TestPackedTypesLite.packed_sint64)
  return &packed_sint64_;
}

// repeated fixed32 packed_fixed32 = 96 [packed = true];
inline int TestPackedTypesLite::packed_fixed32_size() const {
  return packed_fixed32_.size();
}
inline void TestPackedTypesLite::clear_packed_fixed32() {
  packed_fixed32_.Clear();
}
inline ::google::protobuf::uint32 TestPackedTypesLite::packed_fixed32(int index) const {
  // @@protoc_insertion_point(field_get:protobuf_unittest.TestPackedTypesLite.packed_fixed32)
  return packed_fixed32_.Get(index);
}
inline void TestPackedTypesLite::set_packed_fixed32(int index, ::google::protobuf::uint32 value) {
  packed_fixed32_.Set(index, value);
  // @@protoc_insertion_point(field_set:protobuf_unittest.TestPackedTypesLite.packed_fixed32)
}
inline void TestPackedTypesLite::add_packed_fixed32(::google::protobuf::uint32 value) {
  packed_fixed32_.Add(value);
  // @@protoc_insertion_point(field_add:protobuf_unittest.TestPackedTypesLite.packed_fixed32)
}
inline const ::google::protobuf::RepeatedField< ::google::protobuf::uint32 >&
TestPackedTypesLite::packed_fixed32() const {
  // @@protoc_insertion_point(field_list:protobuf_unittest.TestPackedTypesLite.packed_fixed32)
  return packed_fixed32_;
}
inline ::google::protobuf::RepeatedField< ::google::protobuf::uint32 >*
TestPackedTypesLite::mutable_packed_fixed32() {
  // @@protoc_insertion_point(field_mutable_list:protobuf_unittest.TestPackedTypesLite.packed_fixed32)
  return &packed_fixed32_;
}

// repeated fixed64 packed_fixed64 = 97 [packed = true];
inline int TestPackedTypesLite::packed_fixed64_size() const {
  return packed_fixed64_.size();
}
inline void TestPackedTypesLite::clear_packed_fixed64() {
  packed_fixed64_.Clear();
}
inline ::google::protobuf::uint64 TestPackedTypesLite::packed_fixed64(int index) const {
  // @@protoc_insertion_point(field_get:protobuf_unittest.TestPackedTypesLite.packed_fixed64)
  return packed_fixed64_.Get(index);
}
inline void TestPackedTypesLite::set_packed_fixed64(int index, ::google::protobuf::uint64 value) {
  packed_fixed64_.Set(index, value);
  // @@protoc_insertion_point(field_set:protobuf_unittest.TestPackedTypesLite.packed_fixed64)
}
inline void TestPackedTypesLite::add_packed_fixed64(::google::protobuf::uint64 value) {
  packed_fixed64_.Add(value);
  // @@protoc_insertion_point(field_add:protobuf_unittest.TestPackedTypesLite.packed_fixed64)
}
inline const ::google::protobuf::RepeatedField< ::google::protobuf::uint64 >&
TestPackedTypesLite::packed_fixed64() const {
  // @@protoc_insertion_point(field_list:protobuf_unittest.TestPackedTypesLite.packed_fixed64)
  return packed_fixed64_;
}
inline ::google::protobuf::RepeatedField< ::google::protobuf::uint64 >*
TestPackedTypesLite::mutable_packed_fixed64() {
  // @@protoc_insertion_point(field_mutable_list:protobuf_unittest.TestPackedTypesLite.packed_fixed64)
  return &packed_fixed64_;
}

// repeated sfixed32 packed_sfixed32 = 98 [packed = true];
inline int TestPackedTypesLite::packed_sfixed32_size() const {
  return packed_sfixed32_.size();
}
inline void TestPackedTypesLite::clear_packed_sfixed32() {
  packed_sfixed32_.Clear();
}
inline ::google::protobuf::int32 TestPackedTypesLite::packed_sfixed32(int index) const {
  // @@protoc_insertion_point(field_get:protobuf_unittest.TestPackedTypesLite.packed_sfixed32)
  return packed_sfixed32_.Get(index);
}
inline void TestPackedTypesLite::set_packed_sfixed32(int index, ::google::protobuf::int32 value) {
  packed_sfixed32_.Set(index, value);
  // @@protoc_insertion_point(field_set:protobuf_unittest.TestPackedTypesLite.packed_sfixed32)
}
inline void TestPackedTypesLite::add_packed_sfixed32(::google::protobuf::int32 value) {
  packed_sfixed32_.Add(value);
  // @@protoc_insertion_point(field_add:protobuf_unittest.TestPackedTypesLite.packed_sfixed32)
}
inline const ::google::protobuf::RepeatedField< ::google::protobuf::int32 >&
TestPackedTypesLite::packed_sfixed32() const {
  // @@protoc_insertion_point(field_list:protobuf_unittest.TestPackedTypesLite.packed_sfixed32)
  return packed_sfixed32_;
}
inline ::google::protobuf::RepeatedField< ::google::protobuf::int32 >*
TestPackedTypesLite::mutable_packed_sfixed32() {
  // @@protoc_insertion_point(field_mutable_list:protobuf_unittest.TestPackedTypesLite.packed_sfixed32)
  return &packed_sfixed32_;
}

// repeated sfixed64 packed_sfixed64 = 99 [packed = true];
inline int TestPackedTypesLite::packed_sfixed64_size() const {
  return packed_sfixed64_.size();
}
inline void TestPackedTypesLite::clear_packed_sfixed64() {
  packed_sfixed64_.Clear();
}
inline ::google::protobuf::int64 TestPackedTypesLite::packed_sfixed64(int index) const {
  // @@protoc_insertion_point(field_get:protobuf_unittest.TestPackedTypesLite.packed_sfixed64)
  return packed_sfixed64_.Get(index);
}
inline void TestPackedTypesLite::set_packed_sfixed64(int index, ::google::protobuf::int64 value) {
  packed_sfixed64_.Set(index, value);
  // @@protoc_insertion_point(field_set:protobuf_unittest.TestPackedTypesLite.packed_sfixed64)
}
inline void TestPackedTypesLite::add_packed_sfixed64(::google::protobuf::int64 value) {
  packed_sfixed64_.Add(value);
  // @@protoc_insertion_point(field_add:protobuf_unittest.TestPackedTypesLite.packed_sfixed64)
}
inline const ::google::protobuf::RepeatedField< ::google::protobuf::int64 >&
TestPackedTypesLite::packed_sfixed64() const {
  // @@protoc_insertion_point(field_list:protobuf_unittest.TestPackedTypesLite.packed_sfixed64)
  return packed_sfixed64_;
}
inline ::google::protobuf::RepeatedField< ::google::protobuf::int64 >*
TestPackedTypesLite::mutable_packed_sfixed64() {
  // @@protoc_insertion_point(field_mutable_list:protobuf_unittest.TestPackedTypesLite.packed_sfixed64)
  return &packed_sfixed64_;
}

// repeated float packed_float = 100 [packed = true];
inline int TestPackedTypesLite::packed_float_size() const {
  return packed_float_.size();
}
inline void TestPackedTypesLite::clear_packed_float() {
  packed_float_.Clear();
}
inline float TestPackedTypesLite::packed_float(int index) const {
  // @@protoc_insertion_point(field_get:protobuf_unittest.TestPackedTypesLite.packed_float)
  return packed_float_.Get(index);
}
inline void TestPackedTypesLite::set_packed_float(int index, float value) {
  packed_float_.Set(index, value);
  // @@protoc_insertion_point(field_set:protobuf_unittest.TestPackedTypesLite.packed_float)
}
inline void TestPackedTypesLite::add_packed_float(float value) {
  packed_float_.Add(value);
  // @@protoc_insertion_point(field_add:protobuf_unittest.TestPackedTypesLite.packed_float)
}
inline const ::google::protobuf::RepeatedField< float >&
TestPackedTypesLite::packed_float() const {
  // @@protoc_insertion_point(field_list:protobuf_unittest.TestPackedTypesLite.packed_float)
  return packed_float_;
}
inline ::google::protobuf::RepeatedField< float >*
TestPackedTypesLite::mutable_packed_float() {
  // @@protoc_insertion_point(field_mutable_list:protobuf_unittest.TestPackedTypesLite.packed_float)
  return &packed_float_;
}

// repeated double packed_double = 101 [packed = true];
inline int TestPackedTypesLite::packed_double_size() const {
  return packed_double_.size();
}
inline void TestPackedTypesLite::clear_packed_double() {
  packed_double_.Clear();
}
inline double TestPackedTypesLite::packed_double(int index) const {
  // @@protoc_insertion_point(field_get:protobuf_unittest.TestPackedTypesLite.packed_double)
  return packed_double_.Get(index);
}
inline void TestPackedTypesLite::set_packed_double(int index, double value) {
  packed_double_.Set(index, value);
  // @@protoc_insertion_point(field_set:protobuf_unittest.TestPackedTypesLite.packed_double)
}
inline void TestPackedTypesLite::add_packed_double(double value) {
  packed_double_.Add(value);
  // @@protoc_insertion_point(field_add:protobuf_unittest.TestPackedTypesLite.packed_double)
}
inline const ::google::protobuf::RepeatedField< double >&
TestPackedTypesLite::packed_double() const {
  // @@protoc_insertion_point(field_list:protobuf_unittest.TestPackedTypesLite.packed_double)
  return packed_double_;
}
inline ::google::protobuf::RepeatedField< double >*
TestPackedTypesLite::mutable_packed_double() {
  // @@protoc_insertion_point(field_mutable_list:protobuf_unittest.TestPackedTypesLite.packed_double)
  return &packed_double_;
}

// repeated bool packed_bool = 102 [packed = true];
inline int TestPackedTypesLite::packed_bool_size() const {
  return packed_bool_.size();
}
inline void TestPackedTypesLite::clear_packed_bool() {
  packed_bool_.Clear();
}
inline bool TestPackedTypesLite::packed_bool(int index) const {
  // @@protoc_insertion_point(field_get:protobuf_unittest.TestPackedTypesLite.packed_bool)
  return packed_bool_.Get(index);
}
inline void TestPackedTypesLite::set_packed_bool(int index, bool value) {
  packed_bool_.Set(index, value);
  // @@protoc_insertion_point(field_set:protobuf_unittest.TestPackedTypesLite.packed_bool)
}
inline void TestPackedTypesLite::add_packed_bool(bool value) {
  packed_bool_.Add(value);
  // @@protoc_insertion_point(field_add:protobuf_unittest.TestPackedTypesLite.packed_bool)
}
inline const ::google::protobuf::RepeatedField< bool >&
TestPackedTypesLite::packed_bool() const {
  // @@protoc_insertion_point(field_list:protobuf_unittest.TestPackedTypesLite.packed_bool)
  return packed_bool_;
}
inline ::google::protobuf::RepeatedField< bool >*
TestPackedTypesLite::mutable_packed_bool() {
  // @@protoc_insertion_point(field_mutable_list:protobuf_unittest.TestPackedTypesLite.packed_bool)
  return &packed_bool_;
}

// repeated .protobuf_unittest.ForeignEnumLite packed_enum = 103 [packed = true];
inline int TestPackedTypesLite::packed_enum_size() const {
  return packed_enum_.size();
}
inline void TestPackedTypesLite::clear_packed_enum() {
  packed_enum_.Clear();
}
inline ::protobuf_unittest::ForeignEnumLite TestPackedTypesLite::packed_enum(int index) const {
  // @@protoc_insertion_point(field_get:protobuf_unittest.TestPackedTypesLite.packed_enum)
  return static_cast< ::protobuf_unittest::ForeignEnumLite >(packed_enum_.Get(index));
}
inline void TestPackedTypesLite::set_packed_enum(int index, ::protobuf_unittest::ForeignEnumLite value) {
  assert(::protobuf_unittest::ForeignEnumLite_IsValid(value));
  packed_enum_.Set(index, value);
  // @@protoc_insertion_point(field_set:protobuf_unittest.TestPackedTypesLite.packed_enum)
}
inline void TestPackedTypesLite::add_packed_enum(::protobuf_unittest::ForeignEnumLite value) {
  assert(::protobuf_unittest::ForeignEnumLite_IsValid(value));
  packed_enum_.Add(value);
  // @@protoc_insertion_point(field_add:protobuf_unittest.TestPackedTypesLite.packed_enum)
}
inline const ::google::protobuf::RepeatedField<int>&
TestPackedTypesLite::packed_enum() const {
  // @@protoc_insertion_point(field_list:protobuf_unittest.TestPackedTypesLite.packed_enum)
  return packed_enum_;
}
inline ::google::protobuf::RepeatedField<int>*
TestPackedTypesLite::mutable_packed_enum() {
  // @@protoc_insertion_point(field_mutable_list:protobuf_unittest.TestPackedTypesLite.packed_enum)
  return &packed_enum_;
}

inline const TestPackedTypesLite* TestPackedTypesLite::internal_default_instance() {
  return &TestPackedTypesLite_default_instance_.get();
}
// -------------------------------------------------------------------

// TestAllExtensionsLite

inline const TestAllExtensionsLite* TestAllExtensionsLite::internal_default_instance() {
  return &TestAllExtensionsLite_default_instance_.get();
}
// -------------------------------------------------------------------

// OptionalGroup_extension_lite

// optional int32 a = 17;
inline bool OptionalGroup_extension_lite::has_a() const {
  return (_has_bits_[0] & 0x00000001u) != 0;
}
inline void OptionalGroup_extension_lite::set_has_a() {
  _has_bits_[0] |= 0x00000001u;
}
inline void OptionalGroup_extension_lite::clear_has_a() {
  _has_bits_[0] &= ~0x00000001u;
}
inline void OptionalGroup_extension_lite::clear_a() {
  a_ = 0;
  clear_has_a();
}
inline ::google::protobuf::int32 OptionalGroup_extension_lite::a() const {
  // @@protoc_insertion_point(field_get:protobuf_unittest.OptionalGroup_extension_lite.a)
  return a_;
}
inline void OptionalGroup_extension_lite::set_a(::google::protobuf::int32 value) {
  set_has_a();
  a_ = value;
  // @@protoc_insertion_point(field_set:protobuf_unittest.OptionalGroup_extension_lite.a)
}

inline const OptionalGroup_extension_lite* OptionalGroup_extension_lite::internal_default_instance() {
  return &OptionalGroup_extension_lite_default_instance_.get();
}
// -------------------------------------------------------------------

// RepeatedGroup_extension_lite

// optional int32 a = 47;
inline bool RepeatedGroup_extension_lite::has_a() const {
  return (_has_bits_[0] & 0x00000001u) != 0;
}
inline void RepeatedGroup_extension_lite::set_has_a() {
  _has_bits_[0] |= 0x00000001u;
}
inline void RepeatedGroup_extension_lite::clear_has_a() {
  _has_bits_[0] &= ~0x00000001u;
}
inline void RepeatedGroup_extension_lite::clear_a() {
  a_ = 0;
  clear_has_a();
}
inline ::google::protobuf::int32 RepeatedGroup_extension_lite::a() const {
  // @@protoc_insertion_point(field_get:protobuf_unittest.RepeatedGroup_extension_lite.a)
  return a_;
}
inline void RepeatedGroup_extension_lite::set_a(::google::protobuf::int32 value) {
  set_has_a();
  a_ = value;
  // @@protoc_insertion_point(field_set:protobuf_unittest.RepeatedGroup_extension_lite.a)
}

inline const RepeatedGroup_extension_lite* RepeatedGroup_extension_lite::internal_default_instance() {
  return &RepeatedGroup_extension_lite_default_instance_.get();
}
// -------------------------------------------------------------------

// TestPackedExtensionsLite

inline const TestPackedExtensionsLite* TestPackedExtensionsLite::internal_default_instance() {
  return &TestPackedExtensionsLite_default_instance_.get();
}
// -------------------------------------------------------------------

// TestNestedExtensionLite

inline const TestNestedExtensionLite* TestNestedExtensionLite::internal_default_instance() {
  return &TestNestedExtensionLite_default_instance_.get();
}
// -------------------------------------------------------------------

// TestDeprecatedLite

// optional int32 deprecated_field = 1 [deprecated = true];
inline bool TestDeprecatedLite::has_deprecated_field() const {
  return (_has_bits_[0] & 0x00000001u) != 0;
}
inline void TestDeprecatedLite::set_has_deprecated_field() {
  _has_bits_[0] |= 0x00000001u;
}
inline void TestDeprecatedLite::clear_has_deprecated_field() {
  _has_bits_[0] &= ~0x00000001u;
}
inline void TestDeprecatedLite::clear_deprecated_field() {
  deprecated_field_ = 0;
  clear_has_deprecated_field();
}
inline ::google::protobuf::int32 TestDeprecatedLite::deprecated_field() const {
  // @@protoc_insertion_point(field_get:protobuf_unittest.TestDeprecatedLite.deprecated_field)
  return deprecated_field_;
}
inline void TestDeprecatedLite::set_deprecated_field(::google::protobuf::int32 value) {
  set_has_deprecated_field();
  deprecated_field_ = value;
  // @@protoc_insertion_point(field_set:protobuf_unittest.TestDeprecatedLite.deprecated_field)
}

inline const TestDeprecatedLite* TestDeprecatedLite::internal_default_instance() {
  return &TestDeprecatedLite_default_instance_.get();
}
// -------------------------------------------------------------------

// TestParsingMergeLite_RepeatedFieldsGenerator_Group1

// optional .protobuf_unittest.TestAllTypesLite field1 = 11;
inline bool TestParsingMergeLite_RepeatedFieldsGenerator_Group1::has_field1() const {
  return (_has_bits_[0] & 0x00000001u) != 0;
}
inline void TestParsingMergeLite_RepeatedFieldsGenerator_Group1::set_has_field1() {
  _has_bits_[0] |= 0x00000001u;
}
inline void TestParsingMergeLite_RepeatedFieldsGenerator_Group1::clear_has_field1() {
  _has_bits_[0] &= ~0x00000001u;
}
inline void TestParsingMergeLite_RepeatedFieldsGenerator_Group1::clear_field1() {
  if (field1_ != NULL) field1_->::protobuf_unittest::TestAllTypesLite::Clear();
  clear_has_field1();
}
inline const ::protobuf_unittest::TestAllTypesLite& TestParsingMergeLite_RepeatedFieldsGenerator_Group1::field1() const {
  // @@protoc_insertion_point(field_get:protobuf_unittest.TestParsingMergeLite.RepeatedFieldsGenerator.Group1.field1)
  return field1_ != NULL ? *field1_
                         : *::protobuf_unittest::TestAllTypesLite::internal_default_instance();
}
inline ::protobuf_unittest::TestAllTypesLite* TestParsingMergeLite_RepeatedFieldsGenerator_Group1::mutable_field1() {
  set_has_field1();
  if (field1_ == NULL) {
    field1_ = new ::protobuf_unittest::TestAllTypesLite;
  }
  // @@protoc_insertion_point(field_mutable:protobuf_unittest.TestParsingMergeLite.RepeatedFieldsGenerator.Group1.field1)
  return field1_;
}
inline ::protobuf_unittest::TestAllTypesLite* TestParsingMergeLite_RepeatedFieldsGenerator_Group1::release_field1() {
  // @@protoc_insertion_point(field_release:protobuf_unittest.TestParsingMergeLite.RepeatedFieldsGenerator.Group1.field1)
  clear_has_field1();
  ::protobuf_unittest::TestAllTypesLite* temp = field1_;
  field1_ = NULL;
  return temp;
}
inline void TestParsingMergeLite_RepeatedFieldsGenerator_Group1::set_allocated_field1(::protobuf_unittest::TestAllTypesLite* field1) {
  delete field1_;
  field1_ = field1;
  if (field1) {
    set_has_field1();
  } else {
    clear_has_field1();
  }
  // @@protoc_insertion_point(field_set_allocated:protobuf_unittest.TestParsingMergeLite.RepeatedFieldsGenerator.Group1.field1)
}

inline const TestParsingMergeLite_RepeatedFieldsGenerator_Group1* TestParsingMergeLite_RepeatedFieldsGenerator_Group1::internal_default_instance() {
  return &TestParsingMergeLite_RepeatedFieldsGenerator_Group1_default_instance_.get();
}
// -------------------------------------------------------------------

// TestParsingMergeLite_RepeatedFieldsGenerator_Group2

// optional .protobuf_unittest.TestAllTypesLite field1 = 21;
inline bool TestParsingMergeLite_RepeatedFieldsGenerator_Group2::has_field1() const {
  return (_has_bits_[0] & 0x00000001u) != 0;
}
inline void TestParsingMergeLite_RepeatedFieldsGenerator_Group2::set_has_field1() {
  _has_bits_[0] |= 0x00000001u;
}
inline void TestParsingMergeLite_RepeatedFieldsGenerator_Group2::clear_has_field1() {
  _has_bits_[0] &= ~0x00000001u;
}
inline void TestParsingMergeLite_RepeatedFieldsGenerator_Group2::clear_field1() {
  if (field1_ != NULL) field1_->::protobuf_unittest::TestAllTypesLite::Clear();
  clear_has_field1();
}
inline const ::protobuf_unittest::TestAllTypesLite& TestParsingMergeLite_RepeatedFieldsGenerator_Group2::field1() const {
  // @@protoc_insertion_point(field_get:protobuf_unittest.TestParsingMergeLite.RepeatedFieldsGenerator.Group2.field1)
  return field1_ != NULL ? *field1_
                         : *::protobuf_unittest::TestAllTypesLite::internal_default_instance();
}
inline ::protobuf_unittest::TestAllTypesLite* TestParsingMergeLite_RepeatedFieldsGenerator_Group2::mutable_field1() {
  set_has_field1();
  if (field1_ == NULL) {
    field1_ = new ::protobuf_unittest::TestAllTypesLite;
  }
  // @@protoc_insertion_point(field_mutable:protobuf_unittest.TestParsingMergeLite.RepeatedFieldsGenerator.Group2.field1)
  return field1_;
}
inline ::protobuf_unittest::TestAllTypesLite* TestParsingMergeLite_RepeatedFieldsGenerator_Group2::release_field1() {
  // @@protoc_insertion_point(field_release:protobuf_unittest.TestParsingMergeLite.RepeatedFieldsGenerator.Group2.field1)
  clear_has_field1();
  ::protobuf_unittest::TestAllTypesLite* temp = field1_;
  field1_ = NULL;
  return temp;
}
inline void TestParsingMergeLite_RepeatedFieldsGenerator_Group2::set_allocated_field1(::protobuf_unittest::TestAllTypesLite* field1) {
  delete field1_;
  field1_ = field1;
  if (field1) {
    set_has_field1();
  } else {
    clear_has_field1();
  }
  // @@protoc_insertion_point(field_set_allocated:protobuf_unittest.TestParsingMergeLite.RepeatedFieldsGenerator.Group2.field1)
}

inline const TestParsingMergeLite_RepeatedFieldsGenerator_Group2* TestParsingMergeLite_RepeatedFieldsGenerator_Group2::internal_default_instance() {
  return &TestParsingMergeLite_RepeatedFieldsGenerator_Group2_default_instance_.get();
}
// -------------------------------------------------------------------

// TestParsingMergeLite_RepeatedFieldsGenerator

// repeated .protobuf_unittest.TestAllTypesLite field1 = 1;
inline int TestParsingMergeLite_RepeatedFieldsGenerator::field1_size() const {
  return field1_.size();
}
inline void TestParsingMergeLite_RepeatedFieldsGenerator::clear_field1() {
  field1_.Clear();
}
inline const ::protobuf_unittest::TestAllTypesLite& TestParsingMergeLite_RepeatedFieldsGenerator::field1(int index) const {
  // @@protoc_insertion_point(field_get:protobuf_unittest.TestParsingMergeLite.RepeatedFieldsGenerator.field1)
  return field1_.Get(index);
}
inline ::protobuf_unittest::TestAllTypesLite* TestParsingMergeLite_RepeatedFieldsGenerator::mutable_field1(int index) {
  // @@protoc_insertion_point(field_mutable:protobuf_unittest.TestParsingMergeLite.RepeatedFieldsGenerator.field1)
  return field1_.Mutable(index);
}
inline ::protobuf_unittest::TestAllTypesLite* TestParsingMergeLite_RepeatedFieldsGenerator::add_field1() {
  // @@protoc_insertion_point(field_add:protobuf_unittest.TestParsingMergeLite.RepeatedFieldsGenerator.field1)
  return field1_.Add();
}
inline ::google::protobuf::RepeatedPtrField< ::protobuf_unittest::TestAllTypesLite >*
TestParsingMergeLite_RepeatedFieldsGenerator::mutable_field1() {
  // @@protoc_insertion_point(field_mutable_list:protobuf_unittest.TestParsingMergeLite.RepeatedFieldsGenerator.field1)
  return &field1_;
}
inline const ::google::protobuf::RepeatedPtrField< ::protobuf_unittest::TestAllTypesLite >&
TestParsingMergeLite_RepeatedFieldsGenerator::field1() const {
  // @@protoc_insertion_point(field_list:protobuf_unittest.TestParsingMergeLite.RepeatedFieldsGenerator.field1)
  return field1_;
}

// repeated .protobuf_unittest.TestAllTypesLite field2 = 2;
inline int TestParsingMergeLite_RepeatedFieldsGenerator::field2_size() const {
  return field2_.size();
}
inline void TestParsingMergeLite_RepeatedFieldsGenerator::clear_field2() {
  field2_.Clear();
}
inline const ::protobuf_unittest::TestAllTypesLite& TestParsingMergeLite_RepeatedFieldsGenerator::field2(int index) const {
  // @@protoc_insertion_point(field_get:protobuf_unittest.TestParsingMergeLite.RepeatedFieldsGenerator.field2)
  return field2_.Get(index);
}
inline ::protobuf_unittest::TestAllTypesLite* TestParsingMergeLite_RepeatedFieldsGenerator::mutable_field2(int index) {
  // @@protoc_insertion_point(field_mutable:protobuf_unittest.TestParsingMergeLite.RepeatedFieldsGenerator.field2)
  return field2_.Mutable(index);
}
inline ::protobuf_unittest::TestAllTypesLite* TestParsingMergeLite_RepeatedFieldsGenerator::add_field2() {
  // @@protoc_insertion_point(field_add:protobuf_unittest.TestParsingMergeLite.RepeatedFieldsGenerator.field2)
  return field2_.Add();
}
inline ::google::protobuf::RepeatedPtrField< ::protobuf_unittest::TestAllTypesLite >*
TestParsingMergeLite_RepeatedFieldsGenerator::mutable_field2() {
  // @@protoc_insertion_point(field_mutable_list:protobuf_unittest.TestParsingMergeLite.RepeatedFieldsGenerator.field2)
  return &field2_;
}
inline const ::google::protobuf::RepeatedPtrField< ::protobuf_unittest::TestAllTypesLite >&
TestParsingMergeLite_RepeatedFieldsGenerator::field2() const {
  // @@protoc_insertion_point(field_list:protobuf_unittest.TestParsingMergeLite.RepeatedFieldsGenerator.field2)
  return field2_;
}

// repeated .protobuf_unittest.TestAllTypesLite field3 = 3;
inline int TestParsingMergeLite_RepeatedFieldsGenerator::field3_size() const {
  return field3_.size();
}
inline void TestParsingMergeLite_RepeatedFieldsGenerator::clear_field3() {
  field3_.Clear();
}
inline const ::protobuf_unittest::TestAllTypesLite& TestParsingMergeLite_RepeatedFieldsGenerator::field3(int index) const {
  // @@protoc_insertion_point(field_get:protobuf_unittest.TestParsingMergeLite.RepeatedFieldsGenerator.field3)
  return field3_.Get(index);
}
inline ::protobuf_unittest::TestAllTypesLite* TestParsingMergeLite_RepeatedFieldsGenerator::mutable_field3(int index) {
  // @@protoc_insertion_point(field_mutable:protobuf_unittest.TestParsingMergeLite.RepeatedFieldsGenerator.field3)
  return field3_.Mutable(index);
}
inline ::protobuf_unittest::TestAllTypesLite* TestParsingMergeLite_RepeatedFieldsGenerator::add_field3() {
  // @@protoc_insertion_point(field_add:protobuf_unittest.TestParsingMergeLite.RepeatedFieldsGenerator.field3)
  return field3_.Add();
}
inline ::google::protobuf::RepeatedPtrField< ::protobuf_unittest::TestAllTypesLite >*
TestParsingMergeLite_RepeatedFieldsGenerator::mutable_field3() {
  // @@protoc_insertion_point(field_mutable_list:protobuf_unittest.TestParsingMergeLite.RepeatedFieldsGenerator.field3)
  return &field3_;
}
inline const ::google::protobuf::RepeatedPtrField< ::protobuf_unittest::TestAllTypesLite >&
TestParsingMergeLite_RepeatedFieldsGenerator::field3() const {
  // @@protoc_insertion_point(field_list:protobuf_unittest.TestParsingMergeLite.RepeatedFieldsGenerator.field3)
  return field3_;
}

// repeated group Group1 = 10 { ... };
inline int TestParsingMergeLite_RepeatedFieldsGenerator::group1_size() const {
  return group1_.size();
}
inline void TestParsingMergeLite_RepeatedFieldsGenerator::clear_group1() {
  group1_.Clear();
}
inline const ::protobuf_unittest::TestParsingMergeLite_RepeatedFieldsGenerator_Group1& TestParsingMergeLite_RepeatedFieldsGenerator::group1(int index) const {
  // @@protoc_insertion_point(field_get:protobuf_unittest.TestParsingMergeLite.RepeatedFieldsGenerator.group1)
  return group1_.Get(index);
}
inline ::protobuf_unittest::TestParsingMergeLite_RepeatedFieldsGenerator_Group1* TestParsingMergeLite_RepeatedFieldsGenerator::mutable_group1(int index) {
  // @@protoc_insertion_point(field_mutable:protobuf_unittest.TestParsingMergeLite.RepeatedFieldsGenerator.group1)
  return group1_.Mutable(index);
}
inline ::protobuf_unittest::TestParsingMergeLite_RepeatedFieldsGenerator_Group1* TestParsingMergeLite_RepeatedFieldsGenerator::add_group1() {
  // @@protoc_insertion_point(field_add:protobuf_unittest.TestParsingMergeLite.RepeatedFieldsGenerator.group1)
  return group1_.Add();
}
inline ::google::protobuf::RepeatedPtrField< ::protobuf_unittest::TestParsingMergeLite_RepeatedFieldsGenerator_Group1 >*
TestParsingMergeLite_RepeatedFieldsGenerator::mutable_group1() {
  // @@protoc_insertion_point(field_mutable_list:protobuf_unittest.TestParsingMergeLite.RepeatedFieldsGenerator.group1)
  return &group1_;
}
inline const ::google::protobuf::RepeatedPtrField< ::protobuf_unittest::TestParsingMergeLite_RepeatedFieldsGenerator_Group1 >&
TestParsingMergeLite_RepeatedFieldsGenerator::group1() const {
  // @@protoc_insertion_point(field_list:protobuf_unittest.TestParsingMergeLite.RepeatedFieldsGenerator.group1)
  return group1_;
}

// repeated group Group2 = 20 { ... };
inline int TestParsingMergeLite_RepeatedFieldsGenerator::group2_size() const {
  return group2_.size();
}
inline void TestParsingMergeLite_RepeatedFieldsGenerator::clear_group2() {
  group2_.Clear();
}
inline const ::protobuf_unittest::TestParsingMergeLite_RepeatedFieldsGenerator_Group2& TestParsingMergeLite_RepeatedFieldsGenerator::group2(int index) const {
  // @@protoc_insertion_point(field_get:protobuf_unittest.TestParsingMergeLite.RepeatedFieldsGenerator.group2)
  return group2_.Get(index);
}
inline ::protobuf_unittest::TestParsingMergeLite_RepeatedFieldsGenerator_Group2* TestParsingMergeLite_RepeatedFieldsGenerator::mutable_group2(int index) {
  // @@protoc_insertion_point(field_mutable:protobuf_unittest.TestParsingMergeLite.RepeatedFieldsGenerator.group2)
  return group2_.Mutable(index);
}
inline ::protobuf_unittest::TestParsingMergeLite_RepeatedFieldsGenerator_Group2* TestParsingMergeLite_RepeatedFieldsGenerator::add_group2() {
  // @@protoc_insertion_point(field_add:protobuf_unittest.TestParsingMergeLite.RepeatedFieldsGenerator.group2)
  return group2_.Add();
}
inline ::google::protobuf::RepeatedPtrField< ::protobuf_unittest::TestParsingMergeLite_RepeatedFieldsGenerator_Group2 >*
TestParsingMergeLite_RepeatedFieldsGenerator::mutable_group2() {
  // @@protoc_insertion_point(field_mutable_list:protobuf_unittest.TestParsingMergeLite.RepeatedFieldsGenerator.group2)
  return &group2_;
}
inline const ::google::protobuf::RepeatedPtrField< ::protobuf_unittest::TestParsingMergeLite_RepeatedFieldsGenerator_Group2 >&
TestParsingMergeLite_RepeatedFieldsGenerator::group2() const {
  // @@protoc_insertion_point(field_list:protobuf_unittest.TestParsingMergeLite.RepeatedFieldsGenerator.group2)
  return group2_;
}

// repeated .protobuf_unittest.TestAllTypesLite ext1 = 1000;
inline int TestParsingMergeLite_RepeatedFieldsGenerator::ext1_size() const {
  return ext1_.size();
}
inline void TestParsingMergeLite_RepeatedFieldsGenerator::clear_ext1() {
  ext1_.Clear();
}
inline const ::protobuf_unittest::TestAllTypesLite& TestParsingMergeLite_RepeatedFieldsGenerator::ext1(int index) const {
  // @@protoc_insertion_point(field_get:protobuf_unittest.TestParsingMergeLite.RepeatedFieldsGenerator.ext1)
  return ext1_.Get(index);
}
inline ::protobuf_unittest::TestAllTypesLite* TestParsingMergeLite_RepeatedFieldsGenerator::mutable_ext1(int index) {
  // @@protoc_insertion_point(field_mutable:protobuf_unittest.TestParsingMergeLite.RepeatedFieldsGenerator.ext1)
  return ext1_.Mutable(index);
}
inline ::protobuf_unittest::TestAllTypesLite* TestParsingMergeLite_RepeatedFieldsGenerator::add_ext1() {
  // @@protoc_insertion_point(field_add:protobuf_unittest.TestParsingMergeLite.RepeatedFieldsGenerator.ext1)
  return ext1_.Add();
}
inline ::google::protobuf::RepeatedPtrField< ::protobuf_unittest::TestAllTypesLite >*
TestParsingMergeLite_RepeatedFieldsGenerator::mutable_ext1() {
  // @@protoc_insertion_point(field_mutable_list:protobuf_unittest.TestParsingMergeLite.RepeatedFieldsGenerator.ext1)
  return &ext1_;
}
inline const ::google::protobuf::RepeatedPtrField< ::protobuf_unittest::TestAllTypesLite >&
TestParsingMergeLite_RepeatedFieldsGenerator::ext1() const {
  // @@protoc_insertion_point(field_list:protobuf_unittest.TestParsingMergeLite.RepeatedFieldsGenerator.ext1)
  return ext1_;
}

// repeated .protobuf_unittest.TestAllTypesLite ext2 = 1001;
inline int TestParsingMergeLite_RepeatedFieldsGenerator::ext2_size() const {
  return ext2_.size();
}
inline void TestParsingMergeLite_RepeatedFieldsGenerator::clear_ext2() {
  ext2_.Clear();
}
inline const ::protobuf_unittest::TestAllTypesLite& TestParsingMergeLite_RepeatedFieldsGenerator::ext2(int index) const {
  // @@protoc_insertion_point(field_get:protobuf_unittest.TestParsingMergeLite.RepeatedFieldsGenerator.ext2)
  return ext2_.Get(index);
}
inline ::protobuf_unittest::TestAllTypesLite* TestParsingMergeLite_RepeatedFieldsGenerator::mutable_ext2(int index) {
  // @@protoc_insertion_point(field_mutable:protobuf_unittest.TestParsingMergeLite.RepeatedFieldsGenerator.ext2)
  return ext2_.Mutable(index);
}
inline ::protobuf_unittest::TestAllTypesLite* TestParsingMergeLite_RepeatedFieldsGenerator::add_ext2() {
  // @@protoc_insertion_point(field_add:protobuf_unittest.TestParsingMergeLite.RepeatedFieldsGenerator.ext2)
  return ext2_.Add();
}
inline ::google::protobuf::RepeatedPtrField< ::protobuf_unittest::TestAllTypesLite >*
TestParsingMergeLite_RepeatedFieldsGenerator::mutable_ext2() {
  // @@protoc_insertion_point(field_mutable_list:protobuf_unittest.TestParsingMergeLite.RepeatedFieldsGenerator.ext2)
  return &ext2_;
}
inline const ::google::protobuf::RepeatedPtrField< ::protobuf_unittest::TestAllTypesLite >&
TestParsingMergeLite_RepeatedFieldsGenerator::ext2() const {
  // @@protoc_insertion_point(field_list:protobuf_unittest.TestParsingMergeLite.RepeatedFieldsGenerator.ext2)
  return ext2_;
}

inline const TestParsingMergeLite_RepeatedFieldsGenerator* TestParsingMergeLite_RepeatedFieldsGenerator::internal_default_instance() {
  return &TestParsingMergeLite_RepeatedFieldsGenerator_default_instance_.get();
}
// -------------------------------------------------------------------

// TestParsingMergeLite_OptionalGroup

// optional .protobuf_unittest.TestAllTypesLite optional_group_all_types = 11;
inline bool TestParsingMergeLite_OptionalGroup::has_optional_group_all_types() const {
  return (_has_bits_[0] & 0x00000001u) != 0;
}
inline void TestParsingMergeLite_OptionalGroup::set_has_optional_group_all_types() {
  _has_bits_[0] |= 0x00000001u;
}
inline void TestParsingMergeLite_OptionalGroup::clear_has_optional_group_all_types() {
  _has_bits_[0] &= ~0x00000001u;
}
inline void TestParsingMergeLite_OptionalGroup::clear_optional_group_all_types() {
  if (optional_group_all_types_ != NULL) optional_group_all_types_->::protobuf_unittest::TestAllTypesLite::Clear();
  clear_has_optional_group_all_types();
}
inline const ::protobuf_unittest::TestAllTypesLite& TestParsingMergeLite_OptionalGroup::optional_group_all_types() const {
  // @@protoc_insertion_point(field_get:protobuf_unittest.TestParsingMergeLite.OptionalGroup.optional_group_all_types)
  return optional_group_all_types_ != NULL ? *optional_group_all_types_
                         : *::protobuf_unittest::TestAllTypesLite::internal_default_instance();
}
inline ::protobuf_unittest::TestAllTypesLite* TestParsingMergeLite_OptionalGroup::mutable_optional_group_all_types() {
  set_has_optional_group_all_types();
  if (optional_group_all_types_ == NULL) {
    optional_group_all_types_ = new ::protobuf_unittest::TestAllTypesLite;
  }
  // @@protoc_insertion_point(field_mutable:protobuf_unittest.TestParsingMergeLite.OptionalGroup.optional_group_all_types)
  return optional_group_all_types_;
}
inline ::protobuf_unittest::TestAllTypesLite* TestParsingMergeLite_OptionalGroup::release_optional_group_all_types() {
  // @@protoc_insertion_point(field_release:protobuf_unittest.TestParsingMergeLite.OptionalGroup.optional_group_all_types)
  clear_has_optional_group_all_types();
  ::protobuf_unittest::TestAllTypesLite* temp = optional_group_all_types_;
  optional_group_all_types_ = NULL;
  return temp;
}
inline void TestParsingMergeLite_OptionalGroup::set_allocated_optional_group_all_types(::protobuf_unittest::TestAllTypesLite* optional_group_all_types) {
  delete optional_group_all_types_;
  optional_group_all_types_ = optional_group_all_types;
  if (optional_group_all_types) {
    set_has_optional_group_all_types();
  } else {
    clear_has_optional_group_all_types();
  }
  // @@protoc_insertion_point(field_set_allocated:protobuf_unittest.TestParsingMergeLite.OptionalGroup.optional_group_all_types)
}

inline const TestParsingMergeLite_OptionalGroup* TestParsingMergeLite_OptionalGroup::internal_default_instance() {
  return &TestParsingMergeLite_OptionalGroup_default_instance_.get();
}
// -------------------------------------------------------------------

// TestParsingMergeLite_RepeatedGroup

// optional .protobuf_unittest.TestAllTypesLite repeated_group_all_types = 21;
inline bool TestParsingMergeLite_RepeatedGroup::has_repeated_group_all_types() const {
  return (_has_bits_[0] & 0x00000001u) != 0;
}
inline void TestParsingMergeLite_RepeatedGroup::set_has_repeated_group_all_types() {
  _has_bits_[0] |= 0x00000001u;
}
inline void TestParsingMergeLite_RepeatedGroup::clear_has_repeated_group_all_types() {
  _has_bits_[0] &= ~0x00000001u;
}
inline void TestParsingMergeLite_RepeatedGroup::clear_repeated_group_all_types() {
  if (repeated_group_all_types_ != NULL) repeated_group_all_types_->::protobuf_unittest::TestAllTypesLite::Clear();
  clear_has_repeated_group_all_types();
}
inline const ::protobuf_unittest::TestAllTypesLite& TestParsingMergeLite_RepeatedGroup::repeated_group_all_types() const {
  // @@protoc_insertion_point(field_get:protobuf_unittest.TestParsingMergeLite.RepeatedGroup.repeated_group_all_types)
  return repeated_group_all_types_ != NULL ? *repeated_group_all_types_
                         : *::protobuf_unittest::TestAllTypesLite::internal_default_instance();
}
inline ::protobuf_unittest::TestAllTypesLite* TestParsingMergeLite_RepeatedGroup::mutable_repeated_group_all_types() {
  set_has_repeated_group_all_types();
  if (repeated_group_all_types_ == NULL) {
    repeated_group_all_types_ = new ::protobuf_unittest::TestAllTypesLite;
  }
  // @@protoc_insertion_point(field_mutable:protobuf_unittest.TestParsingMergeLite.RepeatedGroup.repeated_group_all_types)
  return repeated_group_all_types_;
}
inline ::protobuf_unittest::TestAllTypesLite* TestParsingMergeLite_RepeatedGroup::release_repeated_group_all_types() {
  // @@protoc_insertion_point(field_release:protobuf_unittest.TestParsingMergeLite.RepeatedGroup.repeated_group_all_types)
  clear_has_repeated_group_all_types();
  ::protobuf_unittest::TestAllTypesLite* temp = repeated_group_all_types_;
  repeated_group_all_types_ = NULL;
  return temp;
}
inline void TestParsingMergeLite_RepeatedGroup::set_allocated_repeated_group_all_types(::protobuf_unittest::TestAllTypesLite* repeated_group_all_types) {
  delete repeated_group_all_types_;
  repeated_group_all_types_ = repeated_group_all_types;
  if (repeated_group_all_types) {
    set_has_repeated_group_all_types();
  } else {
    clear_has_repeated_group_all_types();
  }
  // @@protoc_insertion_point(field_set_allocated:protobuf_unittest.TestParsingMergeLite.RepeatedGroup.repeated_group_all_types)
}

inline const TestParsingMergeLite_RepeatedGroup* TestParsingMergeLite_RepeatedGroup::internal_default_instance() {
  return &TestParsingMergeLite_RepeatedGroup_default_instance_.get();
}
// -------------------------------------------------------------------

// TestParsingMergeLite

// required .protobuf_unittest.TestAllTypesLite required_all_types = 1;
inline bool TestParsingMergeLite::has_required_all_types() const {
  return (_has_bits_[0] & 0x00000001u) != 0;
}
inline void TestParsingMergeLite::set_has_required_all_types() {
  _has_bits_[0] |= 0x00000001u;
}
inline void TestParsingMergeLite::clear_has_required_all_types() {
  _has_bits_[0] &= ~0x00000001u;
}
inline void TestParsingMergeLite::clear_required_all_types() {
  if (required_all_types_ != NULL) required_all_types_->::protobuf_unittest::TestAllTypesLite::Clear();
  clear_has_required_all_types();
}
inline const ::protobuf_unittest::TestAllTypesLite& TestParsingMergeLite::required_all_types() const {
  // @@protoc_insertion_point(field_get:protobuf_unittest.TestParsingMergeLite.required_all_types)
  return required_all_types_ != NULL ? *required_all_types_
                         : *::protobuf_unittest::TestAllTypesLite::internal_default_instance();
}
inline ::protobuf_unittest::TestAllTypesLite* TestParsingMergeLite::mutable_required_all_types() {
  set_has_required_all_types();
  if (required_all_types_ == NULL) {
    required_all_types_ = new ::protobuf_unittest::TestAllTypesLite;
  }
  // @@protoc_insertion_point(field_mutable:protobuf_unittest.TestParsingMergeLite.required_all_types)
  return required_all_types_;
}
inline ::protobuf_unittest::TestAllTypesLite* TestParsingMergeLite::release_required_all_types() {
  // @@protoc_insertion_point(field_release:protobuf_unittest.TestParsingMergeLite.required_all_types)
  clear_has_required_all_types();
  ::protobuf_unittest::TestAllTypesLite* temp = required_all_types_;
  required_all_types_ = NULL;
  return temp;
}
inline void TestParsingMergeLite::set_allocated_required_all_types(::protobuf_unittest::TestAllTypesLite* required_all_types) {
  delete required_all_types_;
  required_all_types_ = required_all_types;
  if (required_all_types) {
    set_has_required_all_types();
  } else {
    clear_has_required_all_types();
  }
  // @@protoc_insertion_point(field_set_allocated:protobuf_unittest.TestParsingMergeLite.required_all_types)
}

// optional .protobuf_unittest.TestAllTypesLite optional_all_types = 2;
inline bool TestParsingMergeLite::has_optional_all_types() const {
  return (_has_bits_[0] & 0x00000002u) != 0;
}
inline void TestParsingMergeLite::set_has_optional_all_types() {
  _has_bits_[0] |= 0x00000002u;
}
inline void TestParsingMergeLite::clear_has_optional_all_types() {
  _has_bits_[0] &= ~0x00000002u;
}
inline void TestParsingMergeLite::clear_optional_all_types() {
  if (optional_all_types_ != NULL) optional_all_types_->::protobuf_unittest::TestAllTypesLite::Clear();
  clear_has_optional_all_types();
}
inline const ::protobuf_unittest::TestAllTypesLite& TestParsingMergeLite::optional_all_types() const {
  // @@protoc_insertion_point(field_get:protobuf_unittest.TestParsingMergeLite.optional_all_types)
  return optional_all_types_ != NULL ? *optional_all_types_
                         : *::protobuf_unittest::TestAllTypesLite::internal_default_instance();
}
inline ::protobuf_unittest::TestAllTypesLite* TestParsingMergeLite::mutable_optional_all_types() {
  set_has_optional_all_types();
  if (optional_all_types_ == NULL) {
    optional_all_types_ = new ::protobuf_unittest::TestAllTypesLite;
  }
  // @@protoc_insertion_point(field_mutable:protobuf_unittest.TestParsingMergeLite.optional_all_types)
  return optional_all_types_;
}
inline ::protobuf_unittest::TestAllTypesLite* TestParsingMergeLite::release_optional_all_types() {
  // @@protoc_insertion_point(field_release:protobuf_unittest.TestParsingMergeLite.optional_all_types)
  clear_has_optional_all_types();
  ::protobuf_unittest::TestAllTypesLite* temp = optional_all_types_;
  optional_all_types_ = NULL;
  return temp;
}
inline void TestParsingMergeLite::set_allocated_optional_all_types(::protobuf_unittest::TestAllTypesLite* optional_all_types) {
  delete optional_all_types_;
  optional_all_types_ = optional_all_types;
  if (optional_all_types) {
    set_has_optional_all_types();
  } else {
    clear_has_optional_all_types();
  }
  // @@protoc_insertion_point(field_set_allocated:protobuf_unittest.TestParsingMergeLite.optional_all_types)
}

// repeated .protobuf_unittest.TestAllTypesLite repeated_all_types = 3;
inline int TestParsingMergeLite::repeated_all_types_size() const {
  return repeated_all_types_.size();
}
inline void TestParsingMergeLite::clear_repeated_all_types() {
  repeated_all_types_.Clear();
}
inline const ::protobuf_unittest::TestAllTypesLite& TestParsingMergeLite::repeated_all_types(int index) const {
  // @@protoc_insertion_point(field_get:protobuf_unittest.TestParsingMergeLite.repeated_all_types)
  return repeated_all_types_.Get(index);
}
inline ::protobuf_unittest::TestAllTypesLite* TestParsingMergeLite::mutable_repeated_all_types(int index) {
  // @@protoc_insertion_point(field_mutable:protobuf_unittest.TestParsingMergeLite.repeated_all_types)
  return repeated_all_types_.Mutable(index);
}
inline ::protobuf_unittest::TestAllTypesLite* TestParsingMergeLite::add_repeated_all_types() {
  // @@protoc_insertion_point(field_add:protobuf_unittest.TestParsingMergeLite.repeated_all_types)
  return repeated_all_types_.Add();
}
inline ::google::protobuf::RepeatedPtrField< ::protobuf_unittest::TestAllTypesLite >*
TestParsingMergeLite::mutable_repeated_all_types() {
  // @@protoc_insertion_point(field_mutable_list:protobuf_unittest.TestParsingMergeLite.repeated_all_types)
  return &repeated_all_types_;
}
inline const ::google::protobuf::RepeatedPtrField< ::protobuf_unittest::TestAllTypesLite >&
TestParsingMergeLite::repeated_all_types() const {
  // @@protoc_insertion_point(field_list:protobuf_unittest.TestParsingMergeLite.repeated_all_types)
  return repeated_all_types_;
}

// optional group OptionalGroup = 10 { ... };
inline bool TestParsingMergeLite::has_optionalgroup() const {
  return (_has_bits_[0] & 0x00000008u) != 0;
}
inline void TestParsingMergeLite::set_has_optionalgroup() {
  _has_bits_[0] |= 0x00000008u;
}
inline void TestParsingMergeLite::clear_has_optionalgroup() {
  _has_bits_[0] &= ~0x00000008u;
}
inline void TestParsingMergeLite::clear_optionalgroup() {
  if (optionalgroup_ != NULL) optionalgroup_->::protobuf_unittest::TestParsingMergeLite_OptionalGroup::Clear();
  clear_has_optionalgroup();
}
inline const ::protobuf_unittest::TestParsingMergeLite_OptionalGroup& TestParsingMergeLite::optionalgroup() const {
  // @@protoc_insertion_point(field_get:protobuf_unittest.TestParsingMergeLite.optionalgroup)
  return optionalgroup_ != NULL ? *optionalgroup_
                         : *::protobuf_unittest::TestParsingMergeLite_OptionalGroup::internal_default_instance();
}
inline ::protobuf_unittest::TestParsingMergeLite_OptionalGroup* TestParsingMergeLite::mutable_optionalgroup() {
  set_has_optionalgroup();
  if (optionalgroup_ == NULL) {
    optionalgroup_ = new ::protobuf_unittest::TestParsingMergeLite_OptionalGroup;
  }
  // @@protoc_insertion_point(field_mutable:protobuf_unittest.TestParsingMergeLite.optionalgroup)
  return optionalgroup_;
}
inline ::protobuf_unittest::TestParsingMergeLite_OptionalGroup* TestParsingMergeLite::release_optionalgroup() {
  // @@protoc_insertion_point(field_release:protobuf_unittest.TestParsingMergeLite.optionalgroup)
  clear_has_optionalgroup();
  ::protobuf_unittest::TestParsingMergeLite_OptionalGroup* temp = optionalgroup_;
  optionalgroup_ = NULL;
  return temp;
}
inline void TestParsingMergeLite::set_allocated_optionalgroup(::protobuf_unittest::TestParsingMergeLite_OptionalGroup* optionalgroup) {
  delete optionalgroup_;
  optionalgroup_ = optionalgroup;
  if (optionalgroup) {
    set_has_optionalgroup();
  } else {
    clear_has_optionalgroup();
  }
  // @@protoc_insertion_point(field_set_allocated:protobuf_unittest.TestParsingMergeLite.optionalgroup)
}

// repeated group RepeatedGroup = 20 { ... };
inline int TestParsingMergeLite::repeatedgroup_size() const {
  return repeatedgroup_.size();
}
inline void TestParsingMergeLite::clear_repeatedgroup() {
  repeatedgroup_.Clear();
}
inline const ::protobuf_unittest::TestParsingMergeLite_RepeatedGroup& TestParsingMergeLite::repeatedgroup(int index) const {
  // @@protoc_insertion_point(field_get:protobuf_unittest.TestParsingMergeLite.repeatedgroup)
  return repeatedgroup_.Get(index);
}
inline ::protobuf_unittest::TestParsingMergeLite_RepeatedGroup* TestParsingMergeLite::mutable_repeatedgroup(int index) {
  // @@protoc_insertion_point(field_mutable:protobuf_unittest.TestParsingMergeLite.repeatedgroup)
  return repeatedgroup_.Mutable(index);
}
inline ::protobuf_unittest::TestParsingMergeLite_RepeatedGroup* TestParsingMergeLite::add_repeatedgroup() {
  // @@protoc_insertion_point(field_add:protobuf_unittest.TestParsingMergeLite.repeatedgroup)
  return repeatedgroup_.Add();
}
inline ::google::protobuf::RepeatedPtrField< ::protobuf_unittest::TestParsingMergeLite_RepeatedGroup >*
TestParsingMergeLite::mutable_repeatedgroup() {
  // @@protoc_insertion_point(field_mutable_list:protobuf_unittest.TestParsingMergeLite.repeatedgroup)
  return &repeatedgroup_;
}
inline const ::google::protobuf::RepeatedPtrField< ::protobuf_unittest::TestParsingMergeLite_RepeatedGroup >&
TestParsingMergeLite::repeatedgroup() const {
  // @@protoc_insertion_point(field_list:protobuf_unittest.TestParsingMergeLite.repeatedgroup)
  return repeatedgroup_;
}

inline const TestParsingMergeLite* TestParsingMergeLite::internal_default_instance() {
  return &TestParsingMergeLite_default_instance_.get();
}
// -------------------------------------------------------------------

// TestEmptyMessageLite

inline const TestEmptyMessageLite* TestEmptyMessageLite::internal_default_instance() {
  return &TestEmptyMessageLite_default_instance_.get();
}
// -------------------------------------------------------------------

// TestEmptyMessageWithExtensionsLite

inline const TestEmptyMessageWithExtensionsLite* TestEmptyMessageWithExtensionsLite::internal_default_instance() {
  return &TestEmptyMessageWithExtensionsLite_default_instance_.get();
}
// -------------------------------------------------------------------

// V1MessageLite

// required int32 int_field = 1;
inline bool V1MessageLite::has_int_field() const {
  return (_has_bits_[0] & 0x00000001u) != 0;
}
inline void V1MessageLite::set_has_int_field() {
  _has_bits_[0] |= 0x00000001u;
}
inline void V1MessageLite::clear_has_int_field() {
  _has_bits_[0] &= ~0x00000001u;
}
inline void V1MessageLite::clear_int_field() {
  int_field_ = 0;
  clear_has_int_field();
}
inline ::google::protobuf::int32 V1MessageLite::int_field() const {
  // @@protoc_insertion_point(field_get:protobuf_unittest.V1MessageLite.int_field)
  return int_field_;
}
inline void V1MessageLite::set_int_field(::google::protobuf::int32 value) {
  set_has_int_field();
  int_field_ = value;
  // @@protoc_insertion_point(field_set:protobuf_unittest.V1MessageLite.int_field)
}

// optional .protobuf_unittest.V1EnumLite enum_field = 2 [default = V1_FIRST];
inline bool V1MessageLite::has_enum_field() const {
  return (_has_bits_[0] & 0x00000002u) != 0;
}
inline void V1MessageLite::set_has_enum_field() {
  _has_bits_[0] |= 0x00000002u;
}
inline void V1MessageLite::clear_has_enum_field() {
  _has_bits_[0] &= ~0x00000002u;
}
inline void V1MessageLite::clear_enum_field() {
  enum_field_ = 1;
  clear_has_enum_field();
}
inline ::protobuf_unittest::V1EnumLite V1MessageLite::enum_field() const {
  // @@protoc_insertion_point(field_get:protobuf_unittest.V1MessageLite.enum_field)
  return static_cast< ::protobuf_unittest::V1EnumLite >(enum_field_);
}
inline void V1MessageLite::set_enum_field(::protobuf_unittest::V1EnumLite value) {
  assert(::protobuf_unittest::V1EnumLite_IsValid(value));
  set_has_enum_field();
  enum_field_ = value;
  // @@protoc_insertion_point(field_set:protobuf_unittest.V1MessageLite.enum_field)
}

inline const V1MessageLite* V1MessageLite::internal_default_instance() {
  return &V1MessageLite_default_instance_.get();
}
// -------------------------------------------------------------------

// V2MessageLite

// required int32 int_field = 1;
inline bool V2MessageLite::has_int_field() const {
  return (_has_bits_[0] & 0x00000001u) != 0;
}
inline void V2MessageLite::set_has_int_field() {
  _has_bits_[0] |= 0x00000001u;
}
inline void V2MessageLite::clear_has_int_field() {
  _has_bits_[0] &= ~0x00000001u;
}
inline void V2MessageLite::clear_int_field() {
  int_field_ = 0;
  clear_has_int_field();
}
inline ::google::protobuf::int32 V2MessageLite::int_field() const {
  // @@protoc_insertion_point(field_get:protobuf_unittest.V2MessageLite.int_field)
  return int_field_;
}
inline void V2MessageLite::set_int_field(::google::protobuf::int32 value) {
  set_has_int_field();
  int_field_ = value;
  // @@protoc_insertion_point(field_set:protobuf_unittest.V2MessageLite.int_field)
}

// optional .protobuf_unittest.V2EnumLite enum_field = 2 [default = V2_FIRST];
inline bool V2MessageLite::has_enum_field() const {
  return (_has_bits_[0] & 0x00000002u) != 0;
}
inline void V2MessageLite::set_has_enum_field() {
  _has_bits_[0] |= 0x00000002u;
}
inline void V2MessageLite::clear_has_enum_field() {
  _has_bits_[0] &= ~0x00000002u;
}
inline void V2MessageLite::clear_enum_field() {
  enum_field_ = 1;
  clear_has_enum_field();
}
inline ::protobuf_unittest::V2EnumLite V2MessageLite::enum_field() const {
  // @@protoc_insertion_point(field_get:protobuf_unittest.V2MessageLite.enum_field)
  return static_cast< ::protobuf_unittest::V2EnumLite >(enum_field_);
}
inline void V2MessageLite::set_enum_field(::protobuf_unittest::V2EnumLite value) {
  assert(::protobuf_unittest::V2EnumLite_IsValid(value));
  set_has_enum_field();
  enum_field_ = value;
  // @@protoc_insertion_point(field_set:protobuf_unittest.V2MessageLite.enum_field)
}

inline const V2MessageLite* V2MessageLite::internal_default_instance() {
  return &V2MessageLite_default_instance_.get();
}
#endif  // !PROTOBUF_INLINE_NOT_IN_HEADERS
// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------


// @@protoc_insertion_point(namespace_scope)

}  // namespace protobuf_unittest

#ifndef SWIG
namespace google {
namespace protobuf {

template <> struct is_proto_enum< ::protobuf_unittest::TestAllTypesLite_NestedEnum> : ::google::protobuf::internal::true_type {};
template <> struct is_proto_enum< ::protobuf_unittest::ForeignEnumLite> : ::google::protobuf::internal::true_type {};
template <> struct is_proto_enum< ::protobuf_unittest::V1EnumLite> : ::google::protobuf::internal::true_type {};
template <> struct is_proto_enum< ::protobuf_unittest::V2EnumLite> : ::google::protobuf::internal::true_type {};

}  // namespace protobuf
}  // namespace google
#endif  // SWIG

// @@protoc_insertion_point(global_scope)

#endif  // PROTOBUF_google_2fprotobuf_2funittest_5flite_2eproto__INCLUDED

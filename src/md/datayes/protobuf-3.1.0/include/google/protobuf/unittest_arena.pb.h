// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: google/protobuf/unittest_arena.proto

#ifndef PROTOBUF_google_2fprotobuf_2funittest_5farena_2eproto__INCLUDED
#define PROTOBUF_google_2fprotobuf_2funittest_5farena_2eproto__INCLUDED

#include <string>

#include <google/protobuf/stubs/common.h>

#if GOOGLE_PROTOBUF_VERSION < 3001000
#error This file was generated by a newer version of protoc which is
#error incompatible with your Protocol Buffer headers.  Please update
#error your headers.
#endif
#if 3001000 < GOOGLE_PROTOBUF_MIN_PROTOC_VERSION
#error This file was generated by an older version of protoc which is
#error incompatible with your Protocol Buffer headers.  Please
#error regenerate this file with a newer version of protoc.
#endif

#include <google/protobuf/arena.h>
#include <google/protobuf/arenastring.h>
#include <google/protobuf/generated_message_util.h>
#include <google/protobuf/metadata.h>
#include <google/protobuf/message.h>
#include <google/protobuf/repeated_field.h>
#include <google/protobuf/extension_set.h>
#include <google/protobuf/unknown_field_set.h>
#include <google/protobuf/unittest_no_arena_import.pb.h>
// @@protoc_insertion_point(includes)

namespace proto2_arena_unittest {

// Internal implementation detail -- do not call these.
void protobuf_AddDesc_google_2fprotobuf_2funittest_5farena_2eproto();
void protobuf_InitDefaults_google_2fprotobuf_2funittest_5farena_2eproto();
void protobuf_AssignDesc_google_2fprotobuf_2funittest_5farena_2eproto();
void protobuf_ShutdownFile_google_2fprotobuf_2funittest_5farena_2eproto();

class ArenaMessage;
class NestedMessage;

// ===================================================================

class NestedMessage : public ::google::protobuf::Message /* @@protoc_insertion_point(class_definition:proto2_arena_unittest.NestedMessage) */ {
 public:
  NestedMessage();
  virtual ~NestedMessage();

  NestedMessage(const NestedMessage& from);

  inline NestedMessage& operator=(const NestedMessage& from) {
    CopyFrom(from);
    return *this;
  }

  inline const ::google::protobuf::UnknownFieldSet& unknown_fields() const {
    return _internal_metadata_.unknown_fields();
  }

  inline ::google::protobuf::UnknownFieldSet* mutable_unknown_fields() {
    return _internal_metadata_.mutable_unknown_fields();
  }

  inline ::google::protobuf::Arena* GetArena() const { return GetArenaNoVirtual(); }
  inline void* GetMaybeArenaPointer() const {
    return MaybeArenaPtr();
  }
  static const ::google::protobuf::Descriptor* descriptor();
  static const NestedMessage& default_instance();

  static const NestedMessage* internal_default_instance();

  void UnsafeArenaSwap(NestedMessage* other);
  void Swap(NestedMessage* other);

  // implements Message ----------------------------------------------

  inline NestedMessage* New() const { return New(NULL); }

  NestedMessage* New(::google::protobuf::Arena* arena) const;
  void CopyFrom(const ::google::protobuf::Message& from);
  void MergeFrom(const ::google::protobuf::Message& from);
  void CopyFrom(const NestedMessage& from);
  void MergeFrom(const NestedMessage& from);
  void Clear();
  bool IsInitialized() const;

  size_t ByteSizeLong() const;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input);
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const;
  ::google::protobuf::uint8* InternalSerializeWithCachedSizesToArray(
      bool deterministic, ::google::protobuf::uint8* output) const;
  ::google::protobuf::uint8* SerializeWithCachedSizesToArray(::google::protobuf::uint8* output) const {
    return InternalSerializeWithCachedSizesToArray(false, output);
  }
  int GetCachedSize() const { return _cached_size_; }
  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const;
  void InternalSwap(NestedMessage* other);
  void UnsafeMergeFrom(const NestedMessage& from);
  protected:
  explicit NestedMessage(::google::protobuf::Arena* arena);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::google::protobuf::Arena* arena);
  private:
  inline ::google::protobuf::Arena* GetArenaNoVirtual() const {
    return _internal_metadata_.arena();
  }
  inline void* MaybeArenaPtr() const {
    return _internal_metadata_.raw_arena_ptr();
  }
  public:

  ::google::protobuf::Metadata GetMetadata() const;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // optional int32 d = 1;
  bool has_d() const;
  void clear_d();
  static const int kDFieldNumber = 1;
  ::google::protobuf::int32 d() const;
  void set_d(::google::protobuf::int32 value);

  // @@protoc_insertion_point(class_scope:proto2_arena_unittest.NestedMessage)
 private:
  inline void set_has_d();
  inline void clear_has_d();

  ::google::protobuf::internal::InternalMetadataWithArena _internal_metadata_;
  friend class ::google::protobuf::Arena;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  ::google::protobuf::internal::HasBits<1> _has_bits_;
  mutable int _cached_size_;
  ::google::protobuf::int32 d_;
  friend void  protobuf_InitDefaults_google_2fprotobuf_2funittest_5farena_2eproto_impl();
  friend void  protobuf_AddDesc_google_2fprotobuf_2funittest_5farena_2eproto_impl();
  friend void protobuf_AssignDesc_google_2fprotobuf_2funittest_5farena_2eproto();
  friend void protobuf_ShutdownFile_google_2fprotobuf_2funittest_5farena_2eproto();

  void InitAsDefaultInstance();
};
extern ::google::protobuf::internal::ExplicitlyConstructed<NestedMessage> NestedMessage_default_instance_;

// -------------------------------------------------------------------

class ArenaMessage : public ::google::protobuf::Message /* @@protoc_insertion_point(class_definition:proto2_arena_unittest.ArenaMessage) */ {
 public:
  ArenaMessage();
  virtual ~ArenaMessage();

  ArenaMessage(const ArenaMessage& from);

  inline ArenaMessage& operator=(const ArenaMessage& from) {
    CopyFrom(from);
    return *this;
  }

  inline const ::google::protobuf::UnknownFieldSet& unknown_fields() const {
    return _internal_metadata_.unknown_fields();
  }

  inline ::google::protobuf::UnknownFieldSet* mutable_unknown_fields() {
    return _internal_metadata_.mutable_unknown_fields();
  }

  inline ::google::protobuf::Arena* GetArena() const { return GetArenaNoVirtual(); }
  inline void* GetMaybeArenaPointer() const {
    return MaybeArenaPtr();
  }
  static const ::google::protobuf::Descriptor* descriptor();
  static const ArenaMessage& default_instance();

  static const ArenaMessage* internal_default_instance();

  void UnsafeArenaSwap(ArenaMessage* other);
  void Swap(ArenaMessage* other);

  // implements Message ----------------------------------------------

  inline ArenaMessage* New() const { return New(NULL); }

  ArenaMessage* New(::google::protobuf::Arena* arena) const;
  void CopyFrom(const ::google::protobuf::Message& from);
  void MergeFrom(const ::google::protobuf::Message& from);
  void CopyFrom(const ArenaMessage& from);
  void MergeFrom(const ArenaMessage& from);
  void Clear();
  bool IsInitialized() const;

  size_t ByteSizeLong() const;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input);
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const;
  ::google::protobuf::uint8* InternalSerializeWithCachedSizesToArray(
      bool deterministic, ::google::protobuf::uint8* output) const;
  ::google::protobuf::uint8* SerializeWithCachedSizesToArray(::google::protobuf::uint8* output) const {
    return InternalSerializeWithCachedSizesToArray(false, output);
  }
  int GetCachedSize() const { return _cached_size_; }
  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const;
  void InternalSwap(ArenaMessage* other);
  void UnsafeMergeFrom(const ArenaMessage& from);
  protected:
  explicit ArenaMessage(::google::protobuf::Arena* arena);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::google::protobuf::Arena* arena);
  private:
  inline ::google::protobuf::Arena* GetArenaNoVirtual() const {
    return _internal_metadata_.arena();
  }
  inline void* MaybeArenaPtr() const {
    return _internal_metadata_.raw_arena_ptr();
  }
  public:

  ::google::protobuf::Metadata GetMetadata() const;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // repeated .proto2_arena_unittest.NestedMessage repeated_nested_message = 1;
  int repeated_nested_message_size() const;
  void clear_repeated_nested_message();
  static const int kRepeatedNestedMessageFieldNumber = 1;
  const ::proto2_arena_unittest::NestedMessage& repeated_nested_message(int index) const;
  ::proto2_arena_unittest::NestedMessage* mutable_repeated_nested_message(int index);
  ::proto2_arena_unittest::NestedMessage* add_repeated_nested_message();
  ::google::protobuf::RepeatedPtrField< ::proto2_arena_unittest::NestedMessage >*
      mutable_repeated_nested_message();
  const ::google::protobuf::RepeatedPtrField< ::proto2_arena_unittest::NestedMessage >&
      repeated_nested_message() const;

  // repeated .proto2_arena_unittest.ImportNoArenaNestedMessage repeated_import_no_arena_message = 2;
  int repeated_import_no_arena_message_size() const;
  void clear_repeated_import_no_arena_message();
  static const int kRepeatedImportNoArenaMessageFieldNumber = 2;
  const ::proto2_arena_unittest::ImportNoArenaNestedMessage& repeated_import_no_arena_message(int index) const;
  ::proto2_arena_unittest::ImportNoArenaNestedMessage* mutable_repeated_import_no_arena_message(int index);
  ::proto2_arena_unittest::ImportNoArenaNestedMessage* add_repeated_import_no_arena_message();
  ::google::protobuf::RepeatedPtrField< ::proto2_arena_unittest::ImportNoArenaNestedMessage >*
      mutable_repeated_import_no_arena_message();
  const ::google::protobuf::RepeatedPtrField< ::proto2_arena_unittest::ImportNoArenaNestedMessage >&
      repeated_import_no_arena_message() const;

  // @@protoc_insertion_point(class_scope:proto2_arena_unittest.ArenaMessage)
 private:

  ::google::protobuf::internal::InternalMetadataWithArena _internal_metadata_;
  friend class ::google::protobuf::Arena;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  ::google::protobuf::internal::HasBits<1> _has_bits_;
  mutable int _cached_size_;
  ::google::protobuf::RepeatedPtrField< ::proto2_arena_unittest::NestedMessage > repeated_nested_message_;
  ::google::protobuf::RepeatedPtrField< ::proto2_arena_unittest::ImportNoArenaNestedMessage > repeated_import_no_arena_message_;
  friend void  protobuf_InitDefaults_google_2fprotobuf_2funittest_5farena_2eproto_impl();
  friend void  protobuf_AddDesc_google_2fprotobuf_2funittest_5farena_2eproto_impl();
  friend void protobuf_AssignDesc_google_2fprotobuf_2funittest_5farena_2eproto();
  friend void protobuf_ShutdownFile_google_2fprotobuf_2funittest_5farena_2eproto();

  void InitAsDefaultInstance();
};
extern ::google::protobuf::internal::ExplicitlyConstructed<ArenaMessage> ArenaMessage_default_instance_;

// ===================================================================


// ===================================================================

#if !PROTOBUF_INLINE_NOT_IN_HEADERS
// NestedMessage

// optional int32 d = 1;
inline bool NestedMessage::has_d() const {
  return (_has_bits_[0] & 0x00000001u) != 0;
}
inline void NestedMessage::set_has_d() {
  _has_bits_[0] |= 0x00000001u;
}
inline void NestedMessage::clear_has_d() {
  _has_bits_[0] &= ~0x00000001u;
}
inline void NestedMessage::clear_d() {
  d_ = 0;
  clear_has_d();
}
inline ::google::protobuf::int32 NestedMessage::d() const {
  // @@protoc_insertion_point(field_get:proto2_arena_unittest.NestedMessage.d)
  return d_;
}
inline void NestedMessage::set_d(::google::protobuf::int32 value) {
  set_has_d();
  d_ = value;
  // @@protoc_insertion_point(field_set:proto2_arena_unittest.NestedMessage.d)
}

inline const NestedMessage* NestedMessage::internal_default_instance() {
  return &NestedMessage_default_instance_.get();
}
// -------------------------------------------------------------------

// ArenaMessage

// repeated .proto2_arena_unittest.NestedMessage repeated_nested_message = 1;
inline int ArenaMessage::repeated_nested_message_size() const {
  return repeated_nested_message_.size();
}
inline void ArenaMessage::clear_repeated_nested_message() {
  repeated_nested_message_.Clear();
}
inline const ::proto2_arena_unittest::NestedMessage& ArenaMessage::repeated_nested_message(int index) const {
  // @@protoc_insertion_point(field_get:proto2_arena_unittest.ArenaMessage.repeated_nested_message)
  return repeated_nested_message_.Get(index);
}
inline ::proto2_arena_unittest::NestedMessage* ArenaMessage::mutable_repeated_nested_message(int index) {
  // @@protoc_insertion_point(field_mutable:proto2_arena_unittest.ArenaMessage.repeated_nested_message)
  return repeated_nested_message_.Mutable(index);
}
inline ::proto2_arena_unittest::NestedMessage* ArenaMessage::add_repeated_nested_message() {
  // @@protoc_insertion_point(field_add:proto2_arena_unittest.ArenaMessage.repeated_nested_message)
  return repeated_nested_message_.Add();
}
inline ::google::protobuf::RepeatedPtrField< ::proto2_arena_unittest::NestedMessage >*
ArenaMessage::mutable_repeated_nested_message() {
  // @@protoc_insertion_point(field_mutable_list:proto2_arena_unittest.ArenaMessage.repeated_nested_message)
  return &repeated_nested_message_;
}
inline const ::google::protobuf::RepeatedPtrField< ::proto2_arena_unittest::NestedMessage >&
ArenaMessage::repeated_nested_message() const {
  // @@protoc_insertion_point(field_list:proto2_arena_unittest.ArenaMessage.repeated_nested_message)
  return repeated_nested_message_;
}

// repeated .proto2_arena_unittest.ImportNoArenaNestedMessage repeated_import_no_arena_message = 2;
inline int ArenaMessage::repeated_import_no_arena_message_size() const {
  return repeated_import_no_arena_message_.size();
}
inline void ArenaMessage::clear_repeated_import_no_arena_message() {
  repeated_import_no_arena_message_.Clear();
}
inline const ::proto2_arena_unittest::ImportNoArenaNestedMessage& ArenaMessage::repeated_import_no_arena_message(int index) const {
  // @@protoc_insertion_point(field_get:proto2_arena_unittest.ArenaMessage.repeated_import_no_arena_message)
  return repeated_import_no_arena_message_.Get(index);
}
inline ::proto2_arena_unittest::ImportNoArenaNestedMessage* ArenaMessage::mutable_repeated_import_no_arena_message(int index) {
  // @@protoc_insertion_point(field_mutable:proto2_arena_unittest.ArenaMessage.repeated_import_no_arena_message)
  return repeated_import_no_arena_message_.Mutable(index);
}
inline ::proto2_arena_unittest::ImportNoArenaNestedMessage* ArenaMessage::add_repeated_import_no_arena_message() {
  // @@protoc_insertion_point(field_add:proto2_arena_unittest.ArenaMessage.repeated_import_no_arena_message)
  return repeated_import_no_arena_message_.Add();
}
inline ::google::protobuf::RepeatedPtrField< ::proto2_arena_unittest::ImportNoArenaNestedMessage >*
ArenaMessage::mutable_repeated_import_no_arena_message() {
  // @@protoc_insertion_point(field_mutable_list:proto2_arena_unittest.ArenaMessage.repeated_import_no_arena_message)
  return &repeated_import_no_arena_message_;
}
inline const ::google::protobuf::RepeatedPtrField< ::proto2_arena_unittest::ImportNoArenaNestedMessage >&
ArenaMessage::repeated_import_no_arena_message() const {
  // @@protoc_insertion_point(field_list:proto2_arena_unittest.ArenaMessage.repeated_import_no_arena_message)
  return repeated_import_no_arena_message_;
}

inline const ArenaMessage* ArenaMessage::internal_default_instance() {
  return &ArenaMessage_default_instance_.get();
}
#endif  // !PROTOBUF_INLINE_NOT_IN_HEADERS
// -------------------------------------------------------------------


// @@protoc_insertion_point(namespace_scope)

}  // namespace proto2_arena_unittest

// @@protoc_insertion_point(global_scope)

#endif  // PROTOBUF_google_2fprotobuf_2funittest_5farena_2eproto__INCLUDED

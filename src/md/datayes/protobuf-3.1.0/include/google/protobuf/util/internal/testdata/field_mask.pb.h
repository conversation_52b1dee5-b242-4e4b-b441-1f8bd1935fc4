// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: google/protobuf/util/internal/testdata/field_mask.proto

#ifndef PROTOBUF_google_2fprotobuf_2futil_2finternal_2ftestdata_2ffield_5fmask_2eproto__INCLUDED
#define PROTOBUF_google_2fprotobuf_2futil_2finternal_2ftestdata_2ffield_5fmask_2eproto__INCLUDED

#include <string>

#include <google/protobuf/stubs/common.h>

#if GOOGLE_PROTOBUF_VERSION < 3001000
#error This file was generated by a newer version of protoc which is
#error incompatible with your Protocol Buffer headers.  Please update
#error your headers.
#endif
#if 3001000 < GOOGLE_PROTOBUF_MIN_PROTOC_VERSION
#error This file was generated by an older version of protoc which is
#error incompatible with your Protocol Buffer headers.  Please
#error regenerate this file with a newer version of protoc.
#endif

#include <google/protobuf/arena.h>
#include <google/protobuf/arenastring.h>
#include <google/protobuf/generated_message_util.h>
#include <google/protobuf/metadata.h>
#include <google/protobuf/message.h>
#include <google/protobuf/repeated_field.h>
#include <google/protobuf/extension_set.h>
#include <google/protobuf/unknown_field_set.h>
#include <google/protobuf/field_mask.pb.h>
// @@protoc_insertion_point(includes)

namespace google {
namespace protobuf {
namespace testing {

// Internal implementation detail -- do not call these.
void protobuf_AddDesc_google_2fprotobuf_2futil_2finternal_2ftestdata_2ffield_5fmask_2eproto();
void protobuf_InitDefaults_google_2fprotobuf_2futil_2finternal_2ftestdata_2ffield_5fmask_2eproto();
void protobuf_AssignDesc_google_2fprotobuf_2futil_2finternal_2ftestdata_2ffield_5fmask_2eproto();
void protobuf_ShutdownFile_google_2fprotobuf_2futil_2finternal_2ftestdata_2ffield_5fmask_2eproto();

class FieldMaskTest;
class FieldMaskTestCases;
class FieldMaskWrapper;
class NestedFieldMask;

// ===================================================================

class NestedFieldMask : public ::google::protobuf::Message /* @@protoc_insertion_point(class_definition:google.protobuf.testing.NestedFieldMask) */ {
 public:
  NestedFieldMask();
  virtual ~NestedFieldMask();

  NestedFieldMask(const NestedFieldMask& from);

  inline NestedFieldMask& operator=(const NestedFieldMask& from) {
    CopyFrom(from);
    return *this;
  }

  static const ::google::protobuf::Descriptor* descriptor();
  static const NestedFieldMask& default_instance();

  static const NestedFieldMask* internal_default_instance();

  void Swap(NestedFieldMask* other);

  // implements Message ----------------------------------------------

  inline NestedFieldMask* New() const { return New(NULL); }

  NestedFieldMask* New(::google::protobuf::Arena* arena) const;
  void CopyFrom(const ::google::protobuf::Message& from);
  void MergeFrom(const ::google::protobuf::Message& from);
  void CopyFrom(const NestedFieldMask& from);
  void MergeFrom(const NestedFieldMask& from);
  void Clear();
  bool IsInitialized() const;

  size_t ByteSizeLong() const;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input);
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const;
  ::google::protobuf::uint8* InternalSerializeWithCachedSizesToArray(
      bool deterministic, ::google::protobuf::uint8* output) const;
  ::google::protobuf::uint8* SerializeWithCachedSizesToArray(::google::protobuf::uint8* output) const {
    return InternalSerializeWithCachedSizesToArray(false, output);
  }
  int GetCachedSize() const { return _cached_size_; }
  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const;
  void InternalSwap(NestedFieldMask* other);
  void UnsafeMergeFrom(const NestedFieldMask& from);
  private:
  inline ::google::protobuf::Arena* GetArenaNoVirtual() const {
    return _internal_metadata_.arena();
  }
  inline void* MaybeArenaPtr() const {
    return _internal_metadata_.raw_arena_ptr();
  }
  public:

  ::google::protobuf::Metadata GetMetadata() const;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // optional string data = 1;
  void clear_data();
  static const int kDataFieldNumber = 1;
  const ::std::string& data() const;
  void set_data(const ::std::string& value);
  void set_data(const char* value);
  void set_data(const char* value, size_t size);
  ::std::string* mutable_data();
  ::std::string* release_data();
  void set_allocated_data(::std::string* data);

  // optional .google.protobuf.FieldMask single_mask = 2;
  bool has_single_mask() const;
  void clear_single_mask();
  static const int kSingleMaskFieldNumber = 2;
  const ::google::protobuf::FieldMask& single_mask() const;
  ::google::protobuf::FieldMask* mutable_single_mask();
  ::google::protobuf::FieldMask* release_single_mask();
  void set_allocated_single_mask(::google::protobuf::FieldMask* single_mask);

  // repeated .google.protobuf.FieldMask repeated_mask = 3;
  int repeated_mask_size() const;
  void clear_repeated_mask();
  static const int kRepeatedMaskFieldNumber = 3;
  const ::google::protobuf::FieldMask& repeated_mask(int index) const;
  ::google::protobuf::FieldMask* mutable_repeated_mask(int index);
  ::google::protobuf::FieldMask* add_repeated_mask();
  ::google::protobuf::RepeatedPtrField< ::google::protobuf::FieldMask >*
      mutable_repeated_mask();
  const ::google::protobuf::RepeatedPtrField< ::google::protobuf::FieldMask >&
      repeated_mask() const;

  // @@protoc_insertion_point(class_scope:google.protobuf.testing.NestedFieldMask)
 private:

  ::google::protobuf::internal::InternalMetadataWithArena _internal_metadata_;
  ::google::protobuf::RepeatedPtrField< ::google::protobuf::FieldMask > repeated_mask_;
  ::google::protobuf::internal::ArenaStringPtr data_;
  ::google::protobuf::FieldMask* single_mask_;
  mutable int _cached_size_;
  friend void  protobuf_InitDefaults_google_2fprotobuf_2futil_2finternal_2ftestdata_2ffield_5fmask_2eproto_impl();
  friend void  protobuf_AddDesc_google_2fprotobuf_2futil_2finternal_2ftestdata_2ffield_5fmask_2eproto_impl();
  friend void protobuf_AssignDesc_google_2fprotobuf_2futil_2finternal_2ftestdata_2ffield_5fmask_2eproto();
  friend void protobuf_ShutdownFile_google_2fprotobuf_2futil_2finternal_2ftestdata_2ffield_5fmask_2eproto();

  void InitAsDefaultInstance();
};
extern ::google::protobuf::internal::ExplicitlyConstructed<NestedFieldMask> NestedFieldMask_default_instance_;

// -------------------------------------------------------------------

class FieldMaskTest : public ::google::protobuf::Message /* @@protoc_insertion_point(class_definition:google.protobuf.testing.FieldMaskTest) */ {
 public:
  FieldMaskTest();
  virtual ~FieldMaskTest();

  FieldMaskTest(const FieldMaskTest& from);

  inline FieldMaskTest& operator=(const FieldMaskTest& from) {
    CopyFrom(from);
    return *this;
  }

  static const ::google::protobuf::Descriptor* descriptor();
  static const FieldMaskTest& default_instance();

  static const FieldMaskTest* internal_default_instance();

  void Swap(FieldMaskTest* other);

  // implements Message ----------------------------------------------

  inline FieldMaskTest* New() const { return New(NULL); }

  FieldMaskTest* New(::google::protobuf::Arena* arena) const;
  void CopyFrom(const ::google::protobuf::Message& from);
  void MergeFrom(const ::google::protobuf::Message& from);
  void CopyFrom(const FieldMaskTest& from);
  void MergeFrom(const FieldMaskTest& from);
  void Clear();
  bool IsInitialized() const;

  size_t ByteSizeLong() const;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input);
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const;
  ::google::protobuf::uint8* InternalSerializeWithCachedSizesToArray(
      bool deterministic, ::google::protobuf::uint8* output) const;
  ::google::protobuf::uint8* SerializeWithCachedSizesToArray(::google::protobuf::uint8* output) const {
    return InternalSerializeWithCachedSizesToArray(false, output);
  }
  int GetCachedSize() const { return _cached_size_; }
  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const;
  void InternalSwap(FieldMaskTest* other);
  void UnsafeMergeFrom(const FieldMaskTest& from);
  private:
  inline ::google::protobuf::Arena* GetArenaNoVirtual() const {
    return _internal_metadata_.arena();
  }
  inline void* MaybeArenaPtr() const {
    return _internal_metadata_.raw_arena_ptr();
  }
  public:

  ::google::protobuf::Metadata GetMetadata() const;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // optional string id = 1;
  void clear_id();
  static const int kIdFieldNumber = 1;
  const ::std::string& id() const;
  void set_id(const ::std::string& value);
  void set_id(const char* value);
  void set_id(const char* value, size_t size);
  ::std::string* mutable_id();
  ::std::string* release_id();
  void set_allocated_id(::std::string* id);

  // optional .google.protobuf.FieldMask single_mask = 2;
  bool has_single_mask() const;
  void clear_single_mask();
  static const int kSingleMaskFieldNumber = 2;
  const ::google::protobuf::FieldMask& single_mask() const;
  ::google::protobuf::FieldMask* mutable_single_mask();
  ::google::protobuf::FieldMask* release_single_mask();
  void set_allocated_single_mask(::google::protobuf::FieldMask* single_mask);

  // repeated .google.protobuf.FieldMask repeated_mask = 3;
  int repeated_mask_size() const;
  void clear_repeated_mask();
  static const int kRepeatedMaskFieldNumber = 3;
  const ::google::protobuf::FieldMask& repeated_mask(int index) const;
  ::google::protobuf::FieldMask* mutable_repeated_mask(int index);
  ::google::protobuf::FieldMask* add_repeated_mask();
  ::google::protobuf::RepeatedPtrField< ::google::protobuf::FieldMask >*
      mutable_repeated_mask();
  const ::google::protobuf::RepeatedPtrField< ::google::protobuf::FieldMask >&
      repeated_mask() const;

  // repeated .google.protobuf.testing.NestedFieldMask nested_mask = 4;
  int nested_mask_size() const;
  void clear_nested_mask();
  static const int kNestedMaskFieldNumber = 4;
  const ::google::protobuf::testing::NestedFieldMask& nested_mask(int index) const;
  ::google::protobuf::testing::NestedFieldMask* mutable_nested_mask(int index);
  ::google::protobuf::testing::NestedFieldMask* add_nested_mask();
  ::google::protobuf::RepeatedPtrField< ::google::protobuf::testing::NestedFieldMask >*
      mutable_nested_mask();
  const ::google::protobuf::RepeatedPtrField< ::google::protobuf::testing::NestedFieldMask >&
      nested_mask() const;

  // @@protoc_insertion_point(class_scope:google.protobuf.testing.FieldMaskTest)
 private:

  ::google::protobuf::internal::InternalMetadataWithArena _internal_metadata_;
  ::google::protobuf::RepeatedPtrField< ::google::protobuf::FieldMask > repeated_mask_;
  ::google::protobuf::RepeatedPtrField< ::google::protobuf::testing::NestedFieldMask > nested_mask_;
  ::google::protobuf::internal::ArenaStringPtr id_;
  ::google::protobuf::FieldMask* single_mask_;
  mutable int _cached_size_;
  friend void  protobuf_InitDefaults_google_2fprotobuf_2futil_2finternal_2ftestdata_2ffield_5fmask_2eproto_impl();
  friend void  protobuf_AddDesc_google_2fprotobuf_2futil_2finternal_2ftestdata_2ffield_5fmask_2eproto_impl();
  friend void protobuf_AssignDesc_google_2fprotobuf_2futil_2finternal_2ftestdata_2ffield_5fmask_2eproto();
  friend void protobuf_ShutdownFile_google_2fprotobuf_2futil_2finternal_2ftestdata_2ffield_5fmask_2eproto();

  void InitAsDefaultInstance();
};
extern ::google::protobuf::internal::ExplicitlyConstructed<FieldMaskTest> FieldMaskTest_default_instance_;

// -------------------------------------------------------------------

class FieldMaskTestCases : public ::google::protobuf::Message /* @@protoc_insertion_point(class_definition:google.protobuf.testing.FieldMaskTestCases) */ {
 public:
  FieldMaskTestCases();
  virtual ~FieldMaskTestCases();

  FieldMaskTestCases(const FieldMaskTestCases& from);

  inline FieldMaskTestCases& operator=(const FieldMaskTestCases& from) {
    CopyFrom(from);
    return *this;
  }

  static const ::google::protobuf::Descriptor* descriptor();
  static const FieldMaskTestCases& default_instance();

  static const FieldMaskTestCases* internal_default_instance();

  void Swap(FieldMaskTestCases* other);

  // implements Message ----------------------------------------------

  inline FieldMaskTestCases* New() const { return New(NULL); }

  FieldMaskTestCases* New(::google::protobuf::Arena* arena) const;
  void CopyFrom(const ::google::protobuf::Message& from);
  void MergeFrom(const ::google::protobuf::Message& from);
  void CopyFrom(const FieldMaskTestCases& from);
  void MergeFrom(const FieldMaskTestCases& from);
  void Clear();
  bool IsInitialized() const;

  size_t ByteSizeLong() const;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input);
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const;
  ::google::protobuf::uint8* InternalSerializeWithCachedSizesToArray(
      bool deterministic, ::google::protobuf::uint8* output) const;
  ::google::protobuf::uint8* SerializeWithCachedSizesToArray(::google::protobuf::uint8* output) const {
    return InternalSerializeWithCachedSizesToArray(false, output);
  }
  int GetCachedSize() const { return _cached_size_; }
  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const;
  void InternalSwap(FieldMaskTestCases* other);
  void UnsafeMergeFrom(const FieldMaskTestCases& from);
  private:
  inline ::google::protobuf::Arena* GetArenaNoVirtual() const {
    return _internal_metadata_.arena();
  }
  inline void* MaybeArenaPtr() const {
    return _internal_metadata_.raw_arena_ptr();
  }
  public:

  ::google::protobuf::Metadata GetMetadata() const;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // optional .google.protobuf.testing.FieldMaskWrapper single_mask = 1;
  bool has_single_mask() const;
  void clear_single_mask();
  static const int kSingleMaskFieldNumber = 1;
  const ::google::protobuf::testing::FieldMaskWrapper& single_mask() const;
  ::google::protobuf::testing::FieldMaskWrapper* mutable_single_mask();
  ::google::protobuf::testing::FieldMaskWrapper* release_single_mask();
  void set_allocated_single_mask(::google::protobuf::testing::FieldMaskWrapper* single_mask);

  // optional .google.protobuf.testing.FieldMaskWrapper multiple_mask = 2;
  bool has_multiple_mask() const;
  void clear_multiple_mask();
  static const int kMultipleMaskFieldNumber = 2;
  const ::google::protobuf::testing::FieldMaskWrapper& multiple_mask() const;
  ::google::protobuf::testing::FieldMaskWrapper* mutable_multiple_mask();
  ::google::protobuf::testing::FieldMaskWrapper* release_multiple_mask();
  void set_allocated_multiple_mask(::google::protobuf::testing::FieldMaskWrapper* multiple_mask);

  // optional .google.protobuf.testing.FieldMaskWrapper snake_camel = 3;
  bool has_snake_camel() const;
  void clear_snake_camel();
  static const int kSnakeCamelFieldNumber = 3;
  const ::google::protobuf::testing::FieldMaskWrapper& snake_camel() const;
  ::google::protobuf::testing::FieldMaskWrapper* mutable_snake_camel();
  ::google::protobuf::testing::FieldMaskWrapper* release_snake_camel();
  void set_allocated_snake_camel(::google::protobuf::testing::FieldMaskWrapper* snake_camel);

  // optional .google.protobuf.testing.FieldMaskWrapper empty_field = 4;
  bool has_empty_field() const;
  void clear_empty_field();
  static const int kEmptyFieldFieldNumber = 4;
  const ::google::protobuf::testing::FieldMaskWrapper& empty_field() const;
  ::google::protobuf::testing::FieldMaskWrapper* mutable_empty_field();
  ::google::protobuf::testing::FieldMaskWrapper* release_empty_field();
  void set_allocated_empty_field(::google::protobuf::testing::FieldMaskWrapper* empty_field);

  // optional .google.protobuf.testing.FieldMaskWrapper apiary_format1 = 5;
  bool has_apiary_format1() const;
  void clear_apiary_format1();
  static const int kApiaryFormat1FieldNumber = 5;
  const ::google::protobuf::testing::FieldMaskWrapper& apiary_format1() const;
  ::google::protobuf::testing::FieldMaskWrapper* mutable_apiary_format1();
  ::google::protobuf::testing::FieldMaskWrapper* release_apiary_format1();
  void set_allocated_apiary_format1(::google::protobuf::testing::FieldMaskWrapper* apiary_format1);

  // optional .google.protobuf.testing.FieldMaskWrapper apiary_format2 = 6;
  bool has_apiary_format2() const;
  void clear_apiary_format2();
  static const int kApiaryFormat2FieldNumber = 6;
  const ::google::protobuf::testing::FieldMaskWrapper& apiary_format2() const;
  ::google::protobuf::testing::FieldMaskWrapper* mutable_apiary_format2();
  ::google::protobuf::testing::FieldMaskWrapper* release_apiary_format2();
  void set_allocated_apiary_format2(::google::protobuf::testing::FieldMaskWrapper* apiary_format2);

  // optional .google.protobuf.testing.FieldMaskWrapper apiary_format3 = 7;
  bool has_apiary_format3() const;
  void clear_apiary_format3();
  static const int kApiaryFormat3FieldNumber = 7;
  const ::google::protobuf::testing::FieldMaskWrapper& apiary_format3() const;
  ::google::protobuf::testing::FieldMaskWrapper* mutable_apiary_format3();
  ::google::protobuf::testing::FieldMaskWrapper* release_apiary_format3();
  void set_allocated_apiary_format3(::google::protobuf::testing::FieldMaskWrapper* apiary_format3);

  // optional .google.protobuf.testing.FieldMaskWrapper map_key1 = 8;
  bool has_map_key1() const;
  void clear_map_key1();
  static const int kMapKey1FieldNumber = 8;
  const ::google::protobuf::testing::FieldMaskWrapper& map_key1() const;
  ::google::protobuf::testing::FieldMaskWrapper* mutable_map_key1();
  ::google::protobuf::testing::FieldMaskWrapper* release_map_key1();
  void set_allocated_map_key1(::google::protobuf::testing::FieldMaskWrapper* map_key1);

  // optional .google.protobuf.testing.FieldMaskWrapper map_key2 = 9;
  bool has_map_key2() const;
  void clear_map_key2();
  static const int kMapKey2FieldNumber = 9;
  const ::google::protobuf::testing::FieldMaskWrapper& map_key2() const;
  ::google::protobuf::testing::FieldMaskWrapper* mutable_map_key2();
  ::google::protobuf::testing::FieldMaskWrapper* release_map_key2();
  void set_allocated_map_key2(::google::protobuf::testing::FieldMaskWrapper* map_key2);

  // optional .google.protobuf.testing.FieldMaskWrapper map_key3 = 10;
  bool has_map_key3() const;
  void clear_map_key3();
  static const int kMapKey3FieldNumber = 10;
  const ::google::protobuf::testing::FieldMaskWrapper& map_key3() const;
  ::google::protobuf::testing::FieldMaskWrapper* mutable_map_key3();
  ::google::protobuf::testing::FieldMaskWrapper* release_map_key3();
  void set_allocated_map_key3(::google::protobuf::testing::FieldMaskWrapper* map_key3);

  // optional .google.protobuf.testing.FieldMaskWrapper map_key4 = 11;
  bool has_map_key4() const;
  void clear_map_key4();
  static const int kMapKey4FieldNumber = 11;
  const ::google::protobuf::testing::FieldMaskWrapper& map_key4() const;
  ::google::protobuf::testing::FieldMaskWrapper* mutable_map_key4();
  ::google::protobuf::testing::FieldMaskWrapper* release_map_key4();
  void set_allocated_map_key4(::google::protobuf::testing::FieldMaskWrapper* map_key4);

  // optional .google.protobuf.testing.FieldMaskWrapper map_key5 = 12;
  bool has_map_key5() const;
  void clear_map_key5();
  static const int kMapKey5FieldNumber = 12;
  const ::google::protobuf::testing::FieldMaskWrapper& map_key5() const;
  ::google::protobuf::testing::FieldMaskWrapper* mutable_map_key5();
  ::google::protobuf::testing::FieldMaskWrapper* release_map_key5();
  void set_allocated_map_key5(::google::protobuf::testing::FieldMaskWrapper* map_key5);

  // @@protoc_insertion_point(class_scope:google.protobuf.testing.FieldMaskTestCases)
 private:

  ::google::protobuf::internal::InternalMetadataWithArena _internal_metadata_;
  ::google::protobuf::testing::FieldMaskWrapper* single_mask_;
  ::google::protobuf::testing::FieldMaskWrapper* multiple_mask_;
  ::google::protobuf::testing::FieldMaskWrapper* snake_camel_;
  ::google::protobuf::testing::FieldMaskWrapper* empty_field_;
  ::google::protobuf::testing::FieldMaskWrapper* apiary_format1_;
  ::google::protobuf::testing::FieldMaskWrapper* apiary_format2_;
  ::google::protobuf::testing::FieldMaskWrapper* apiary_format3_;
  ::google::protobuf::testing::FieldMaskWrapper* map_key1_;
  ::google::protobuf::testing::FieldMaskWrapper* map_key2_;
  ::google::protobuf::testing::FieldMaskWrapper* map_key3_;
  ::google::protobuf::testing::FieldMaskWrapper* map_key4_;
  ::google::protobuf::testing::FieldMaskWrapper* map_key5_;
  mutable int _cached_size_;
  friend void  protobuf_InitDefaults_google_2fprotobuf_2futil_2finternal_2ftestdata_2ffield_5fmask_2eproto_impl();
  friend void  protobuf_AddDesc_google_2fprotobuf_2futil_2finternal_2ftestdata_2ffield_5fmask_2eproto_impl();
  friend void protobuf_AssignDesc_google_2fprotobuf_2futil_2finternal_2ftestdata_2ffield_5fmask_2eproto();
  friend void protobuf_ShutdownFile_google_2fprotobuf_2futil_2finternal_2ftestdata_2ffield_5fmask_2eproto();

  void InitAsDefaultInstance();
};
extern ::google::protobuf::internal::ExplicitlyConstructed<FieldMaskTestCases> FieldMaskTestCases_default_instance_;

// -------------------------------------------------------------------

class FieldMaskWrapper : public ::google::protobuf::Message /* @@protoc_insertion_point(class_definition:google.protobuf.testing.FieldMaskWrapper) */ {
 public:
  FieldMaskWrapper();
  virtual ~FieldMaskWrapper();

  FieldMaskWrapper(const FieldMaskWrapper& from);

  inline FieldMaskWrapper& operator=(const FieldMaskWrapper& from) {
    CopyFrom(from);
    return *this;
  }

  static const ::google::protobuf::Descriptor* descriptor();
  static const FieldMaskWrapper& default_instance();

  static const FieldMaskWrapper* internal_default_instance();

  void Swap(FieldMaskWrapper* other);

  // implements Message ----------------------------------------------

  inline FieldMaskWrapper* New() const { return New(NULL); }

  FieldMaskWrapper* New(::google::protobuf::Arena* arena) const;
  void CopyFrom(const ::google::protobuf::Message& from);
  void MergeFrom(const ::google::protobuf::Message& from);
  void CopyFrom(const FieldMaskWrapper& from);
  void MergeFrom(const FieldMaskWrapper& from);
  void Clear();
  bool IsInitialized() const;

  size_t ByteSizeLong() const;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input);
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const;
  ::google::protobuf::uint8* InternalSerializeWithCachedSizesToArray(
      bool deterministic, ::google::protobuf::uint8* output) const;
  ::google::protobuf::uint8* SerializeWithCachedSizesToArray(::google::protobuf::uint8* output) const {
    return InternalSerializeWithCachedSizesToArray(false, output);
  }
  int GetCachedSize() const { return _cached_size_; }
  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const;
  void InternalSwap(FieldMaskWrapper* other);
  void UnsafeMergeFrom(const FieldMaskWrapper& from);
  private:
  inline ::google::protobuf::Arena* GetArenaNoVirtual() const {
    return _internal_metadata_.arena();
  }
  inline void* MaybeArenaPtr() const {
    return _internal_metadata_.raw_arena_ptr();
  }
  public:

  ::google::protobuf::Metadata GetMetadata() const;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // optional .google.protobuf.FieldMask mask = 1;
  bool has_mask() const;
  void clear_mask();
  static const int kMaskFieldNumber = 1;
  const ::google::protobuf::FieldMask& mask() const;
  ::google::protobuf::FieldMask* mutable_mask();
  ::google::protobuf::FieldMask* release_mask();
  void set_allocated_mask(::google::protobuf::FieldMask* mask);

  // @@protoc_insertion_point(class_scope:google.protobuf.testing.FieldMaskWrapper)
 private:

  ::google::protobuf::internal::InternalMetadataWithArena _internal_metadata_;
  ::google::protobuf::FieldMask* mask_;
  mutable int _cached_size_;
  friend void  protobuf_InitDefaults_google_2fprotobuf_2futil_2finternal_2ftestdata_2ffield_5fmask_2eproto_impl();
  friend void  protobuf_AddDesc_google_2fprotobuf_2futil_2finternal_2ftestdata_2ffield_5fmask_2eproto_impl();
  friend void protobuf_AssignDesc_google_2fprotobuf_2futil_2finternal_2ftestdata_2ffield_5fmask_2eproto();
  friend void protobuf_ShutdownFile_google_2fprotobuf_2futil_2finternal_2ftestdata_2ffield_5fmask_2eproto();

  void InitAsDefaultInstance();
};
extern ::google::protobuf::internal::ExplicitlyConstructed<FieldMaskWrapper> FieldMaskWrapper_default_instance_;

// ===================================================================


// ===================================================================

#if !PROTOBUF_INLINE_NOT_IN_HEADERS
// NestedFieldMask

// optional string data = 1;
inline void NestedFieldMask::clear_data() {
  data_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline const ::std::string& NestedFieldMask::data() const {
  // @@protoc_insertion_point(field_get:google.protobuf.testing.NestedFieldMask.data)
  return data_.GetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void NestedFieldMask::set_data(const ::std::string& value) {
  
  data_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:google.protobuf.testing.NestedFieldMask.data)
}
inline void NestedFieldMask::set_data(const char* value) {
  
  data_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:google.protobuf.testing.NestedFieldMask.data)
}
inline void NestedFieldMask::set_data(const char* value, size_t size) {
  
  data_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:google.protobuf.testing.NestedFieldMask.data)
}
inline ::std::string* NestedFieldMask::mutable_data() {
  
  // @@protoc_insertion_point(field_mutable:google.protobuf.testing.NestedFieldMask.data)
  return data_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline ::std::string* NestedFieldMask::release_data() {
  // @@protoc_insertion_point(field_release:google.protobuf.testing.NestedFieldMask.data)
  
  return data_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void NestedFieldMask::set_allocated_data(::std::string* data) {
  if (data != NULL) {
    
  } else {
    
  }
  data_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), data);
  // @@protoc_insertion_point(field_set_allocated:google.protobuf.testing.NestedFieldMask.data)
}

// optional .google.protobuf.FieldMask single_mask = 2;
inline bool NestedFieldMask::has_single_mask() const {
  return this != internal_default_instance() && single_mask_ != NULL;
}
inline void NestedFieldMask::clear_single_mask() {
  if (GetArenaNoVirtual() == NULL && single_mask_ != NULL) delete single_mask_;
  single_mask_ = NULL;
}
inline const ::google::protobuf::FieldMask& NestedFieldMask::single_mask() const {
  // @@protoc_insertion_point(field_get:google.protobuf.testing.NestedFieldMask.single_mask)
  return single_mask_ != NULL ? *single_mask_
                         : *::google::protobuf::FieldMask::internal_default_instance();
}
inline ::google::protobuf::FieldMask* NestedFieldMask::mutable_single_mask() {
  
  if (single_mask_ == NULL) {
    single_mask_ = new ::google::protobuf::FieldMask;
  }
  // @@protoc_insertion_point(field_mutable:google.protobuf.testing.NestedFieldMask.single_mask)
  return single_mask_;
}
inline ::google::protobuf::FieldMask* NestedFieldMask::release_single_mask() {
  // @@protoc_insertion_point(field_release:google.protobuf.testing.NestedFieldMask.single_mask)
  
  ::google::protobuf::FieldMask* temp = single_mask_;
  single_mask_ = NULL;
  return temp;
}
inline void NestedFieldMask::set_allocated_single_mask(::google::protobuf::FieldMask* single_mask) {
  delete single_mask_;
  single_mask_ = single_mask;
  if (single_mask) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_set_allocated:google.protobuf.testing.NestedFieldMask.single_mask)
}

// repeated .google.protobuf.FieldMask repeated_mask = 3;
inline int NestedFieldMask::repeated_mask_size() const {
  return repeated_mask_.size();
}
inline void NestedFieldMask::clear_repeated_mask() {
  repeated_mask_.Clear();
}
inline const ::google::protobuf::FieldMask& NestedFieldMask::repeated_mask(int index) const {
  // @@protoc_insertion_point(field_get:google.protobuf.testing.NestedFieldMask.repeated_mask)
  return repeated_mask_.Get(index);
}
inline ::google::protobuf::FieldMask* NestedFieldMask::mutable_repeated_mask(int index) {
  // @@protoc_insertion_point(field_mutable:google.protobuf.testing.NestedFieldMask.repeated_mask)
  return repeated_mask_.Mutable(index);
}
inline ::google::protobuf::FieldMask* NestedFieldMask::add_repeated_mask() {
  // @@protoc_insertion_point(field_add:google.protobuf.testing.NestedFieldMask.repeated_mask)
  return repeated_mask_.Add();
}
inline ::google::protobuf::RepeatedPtrField< ::google::protobuf::FieldMask >*
NestedFieldMask::mutable_repeated_mask() {
  // @@protoc_insertion_point(field_mutable_list:google.protobuf.testing.NestedFieldMask.repeated_mask)
  return &repeated_mask_;
}
inline const ::google::protobuf::RepeatedPtrField< ::google::protobuf::FieldMask >&
NestedFieldMask::repeated_mask() const {
  // @@protoc_insertion_point(field_list:google.protobuf.testing.NestedFieldMask.repeated_mask)
  return repeated_mask_;
}

inline const NestedFieldMask* NestedFieldMask::internal_default_instance() {
  return &NestedFieldMask_default_instance_.get();
}
// -------------------------------------------------------------------

// FieldMaskTest

// optional string id = 1;
inline void FieldMaskTest::clear_id() {
  id_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline const ::std::string& FieldMaskTest::id() const {
  // @@protoc_insertion_point(field_get:google.protobuf.testing.FieldMaskTest.id)
  return id_.GetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void FieldMaskTest::set_id(const ::std::string& value) {
  
  id_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:google.protobuf.testing.FieldMaskTest.id)
}
inline void FieldMaskTest::set_id(const char* value) {
  
  id_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:google.protobuf.testing.FieldMaskTest.id)
}
inline void FieldMaskTest::set_id(const char* value, size_t size) {
  
  id_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:google.protobuf.testing.FieldMaskTest.id)
}
inline ::std::string* FieldMaskTest::mutable_id() {
  
  // @@protoc_insertion_point(field_mutable:google.protobuf.testing.FieldMaskTest.id)
  return id_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline ::std::string* FieldMaskTest::release_id() {
  // @@protoc_insertion_point(field_release:google.protobuf.testing.FieldMaskTest.id)
  
  return id_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void FieldMaskTest::set_allocated_id(::std::string* id) {
  if (id != NULL) {
    
  } else {
    
  }
  id_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), id);
  // @@protoc_insertion_point(field_set_allocated:google.protobuf.testing.FieldMaskTest.id)
}

// optional .google.protobuf.FieldMask single_mask = 2;
inline bool FieldMaskTest::has_single_mask() const {
  return this != internal_default_instance() && single_mask_ != NULL;
}
inline void FieldMaskTest::clear_single_mask() {
  if (GetArenaNoVirtual() == NULL && single_mask_ != NULL) delete single_mask_;
  single_mask_ = NULL;
}
inline const ::google::protobuf::FieldMask& FieldMaskTest::single_mask() const {
  // @@protoc_insertion_point(field_get:google.protobuf.testing.FieldMaskTest.single_mask)
  return single_mask_ != NULL ? *single_mask_
                         : *::google::protobuf::FieldMask::internal_default_instance();
}
inline ::google::protobuf::FieldMask* FieldMaskTest::mutable_single_mask() {
  
  if (single_mask_ == NULL) {
    single_mask_ = new ::google::protobuf::FieldMask;
  }
  // @@protoc_insertion_point(field_mutable:google.protobuf.testing.FieldMaskTest.single_mask)
  return single_mask_;
}
inline ::google::protobuf::FieldMask* FieldMaskTest::release_single_mask() {
  // @@protoc_insertion_point(field_release:google.protobuf.testing.FieldMaskTest.single_mask)
  
  ::google::protobuf::FieldMask* temp = single_mask_;
  single_mask_ = NULL;
  return temp;
}
inline void FieldMaskTest::set_allocated_single_mask(::google::protobuf::FieldMask* single_mask) {
  delete single_mask_;
  single_mask_ = single_mask;
  if (single_mask) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_set_allocated:google.protobuf.testing.FieldMaskTest.single_mask)
}

// repeated .google.protobuf.FieldMask repeated_mask = 3;
inline int FieldMaskTest::repeated_mask_size() const {
  return repeated_mask_.size();
}
inline void FieldMaskTest::clear_repeated_mask() {
  repeated_mask_.Clear();
}
inline const ::google::protobuf::FieldMask& FieldMaskTest::repeated_mask(int index) const {
  // @@protoc_insertion_point(field_get:google.protobuf.testing.FieldMaskTest.repeated_mask)
  return repeated_mask_.Get(index);
}
inline ::google::protobuf::FieldMask* FieldMaskTest::mutable_repeated_mask(int index) {
  // @@protoc_insertion_point(field_mutable:google.protobuf.testing.FieldMaskTest.repeated_mask)
  return repeated_mask_.Mutable(index);
}
inline ::google::protobuf::FieldMask* FieldMaskTest::add_repeated_mask() {
  // @@protoc_insertion_point(field_add:google.protobuf.testing.FieldMaskTest.repeated_mask)
  return repeated_mask_.Add();
}
inline ::google::protobuf::RepeatedPtrField< ::google::protobuf::FieldMask >*
FieldMaskTest::mutable_repeated_mask() {
  // @@protoc_insertion_point(field_mutable_list:google.protobuf.testing.FieldMaskTest.repeated_mask)
  return &repeated_mask_;
}
inline const ::google::protobuf::RepeatedPtrField< ::google::protobuf::FieldMask >&
FieldMaskTest::repeated_mask() const {
  // @@protoc_insertion_point(field_list:google.protobuf.testing.FieldMaskTest.repeated_mask)
  return repeated_mask_;
}

// repeated .google.protobuf.testing.NestedFieldMask nested_mask = 4;
inline int FieldMaskTest::nested_mask_size() const {
  return nested_mask_.size();
}
inline void FieldMaskTest::clear_nested_mask() {
  nested_mask_.Clear();
}
inline const ::google::protobuf::testing::NestedFieldMask& FieldMaskTest::nested_mask(int index) const {
  // @@protoc_insertion_point(field_get:google.protobuf.testing.FieldMaskTest.nested_mask)
  return nested_mask_.Get(index);
}
inline ::google::protobuf::testing::NestedFieldMask* FieldMaskTest::mutable_nested_mask(int index) {
  // @@protoc_insertion_point(field_mutable:google.protobuf.testing.FieldMaskTest.nested_mask)
  return nested_mask_.Mutable(index);
}
inline ::google::protobuf::testing::NestedFieldMask* FieldMaskTest::add_nested_mask() {
  // @@protoc_insertion_point(field_add:google.protobuf.testing.FieldMaskTest.nested_mask)
  return nested_mask_.Add();
}
inline ::google::protobuf::RepeatedPtrField< ::google::protobuf::testing::NestedFieldMask >*
FieldMaskTest::mutable_nested_mask() {
  // @@protoc_insertion_point(field_mutable_list:google.protobuf.testing.FieldMaskTest.nested_mask)
  return &nested_mask_;
}
inline const ::google::protobuf::RepeatedPtrField< ::google::protobuf::testing::NestedFieldMask >&
FieldMaskTest::nested_mask() const {
  // @@protoc_insertion_point(field_list:google.protobuf.testing.FieldMaskTest.nested_mask)
  return nested_mask_;
}

inline const FieldMaskTest* FieldMaskTest::internal_default_instance() {
  return &FieldMaskTest_default_instance_.get();
}
// -------------------------------------------------------------------

// FieldMaskTestCases

// optional .google.protobuf.testing.FieldMaskWrapper single_mask = 1;
inline bool FieldMaskTestCases::has_single_mask() const {
  return this != internal_default_instance() && single_mask_ != NULL;
}
inline void FieldMaskTestCases::clear_single_mask() {
  if (GetArenaNoVirtual() == NULL && single_mask_ != NULL) delete single_mask_;
  single_mask_ = NULL;
}
inline const ::google::protobuf::testing::FieldMaskWrapper& FieldMaskTestCases::single_mask() const {
  // @@protoc_insertion_point(field_get:google.protobuf.testing.FieldMaskTestCases.single_mask)
  return single_mask_ != NULL ? *single_mask_
                         : *::google::protobuf::testing::FieldMaskWrapper::internal_default_instance();
}
inline ::google::protobuf::testing::FieldMaskWrapper* FieldMaskTestCases::mutable_single_mask() {
  
  if (single_mask_ == NULL) {
    single_mask_ = new ::google::protobuf::testing::FieldMaskWrapper;
  }
  // @@protoc_insertion_point(field_mutable:google.protobuf.testing.FieldMaskTestCases.single_mask)
  return single_mask_;
}
inline ::google::protobuf::testing::FieldMaskWrapper* FieldMaskTestCases::release_single_mask() {
  // @@protoc_insertion_point(field_release:google.protobuf.testing.FieldMaskTestCases.single_mask)
  
  ::google::protobuf::testing::FieldMaskWrapper* temp = single_mask_;
  single_mask_ = NULL;
  return temp;
}
inline void FieldMaskTestCases::set_allocated_single_mask(::google::protobuf::testing::FieldMaskWrapper* single_mask) {
  delete single_mask_;
  single_mask_ = single_mask;
  if (single_mask) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_set_allocated:google.protobuf.testing.FieldMaskTestCases.single_mask)
}

// optional .google.protobuf.testing.FieldMaskWrapper multiple_mask = 2;
inline bool FieldMaskTestCases::has_multiple_mask() const {
  return this != internal_default_instance() && multiple_mask_ != NULL;
}
inline void FieldMaskTestCases::clear_multiple_mask() {
  if (GetArenaNoVirtual() == NULL && multiple_mask_ != NULL) delete multiple_mask_;
  multiple_mask_ = NULL;
}
inline const ::google::protobuf::testing::FieldMaskWrapper& FieldMaskTestCases::multiple_mask() const {
  // @@protoc_insertion_point(field_get:google.protobuf.testing.FieldMaskTestCases.multiple_mask)
  return multiple_mask_ != NULL ? *multiple_mask_
                         : *::google::protobuf::testing::FieldMaskWrapper::internal_default_instance();
}
inline ::google::protobuf::testing::FieldMaskWrapper* FieldMaskTestCases::mutable_multiple_mask() {
  
  if (multiple_mask_ == NULL) {
    multiple_mask_ = new ::google::protobuf::testing::FieldMaskWrapper;
  }
  // @@protoc_insertion_point(field_mutable:google.protobuf.testing.FieldMaskTestCases.multiple_mask)
  return multiple_mask_;
}
inline ::google::protobuf::testing::FieldMaskWrapper* FieldMaskTestCases::release_multiple_mask() {
  // @@protoc_insertion_point(field_release:google.protobuf.testing.FieldMaskTestCases.multiple_mask)
  
  ::google::protobuf::testing::FieldMaskWrapper* temp = multiple_mask_;
  multiple_mask_ = NULL;
  return temp;
}
inline void FieldMaskTestCases::set_allocated_multiple_mask(::google::protobuf::testing::FieldMaskWrapper* multiple_mask) {
  delete multiple_mask_;
  multiple_mask_ = multiple_mask;
  if (multiple_mask) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_set_allocated:google.protobuf.testing.FieldMaskTestCases.multiple_mask)
}

// optional .google.protobuf.testing.FieldMaskWrapper snake_camel = 3;
inline bool FieldMaskTestCases::has_snake_camel() const {
  return this != internal_default_instance() && snake_camel_ != NULL;
}
inline void FieldMaskTestCases::clear_snake_camel() {
  if (GetArenaNoVirtual() == NULL && snake_camel_ != NULL) delete snake_camel_;
  snake_camel_ = NULL;
}
inline const ::google::protobuf::testing::FieldMaskWrapper& FieldMaskTestCases::snake_camel() const {
  // @@protoc_insertion_point(field_get:google.protobuf.testing.FieldMaskTestCases.snake_camel)
  return snake_camel_ != NULL ? *snake_camel_
                         : *::google::protobuf::testing::FieldMaskWrapper::internal_default_instance();
}
inline ::google::protobuf::testing::FieldMaskWrapper* FieldMaskTestCases::mutable_snake_camel() {
  
  if (snake_camel_ == NULL) {
    snake_camel_ = new ::google::protobuf::testing::FieldMaskWrapper;
  }
  // @@protoc_insertion_point(field_mutable:google.protobuf.testing.FieldMaskTestCases.snake_camel)
  return snake_camel_;
}
inline ::google::protobuf::testing::FieldMaskWrapper* FieldMaskTestCases::release_snake_camel() {
  // @@protoc_insertion_point(field_release:google.protobuf.testing.FieldMaskTestCases.snake_camel)
  
  ::google::protobuf::testing::FieldMaskWrapper* temp = snake_camel_;
  snake_camel_ = NULL;
  return temp;
}
inline void FieldMaskTestCases::set_allocated_snake_camel(::google::protobuf::testing::FieldMaskWrapper* snake_camel) {
  delete snake_camel_;
  snake_camel_ = snake_camel;
  if (snake_camel) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_set_allocated:google.protobuf.testing.FieldMaskTestCases.snake_camel)
}

// optional .google.protobuf.testing.FieldMaskWrapper empty_field = 4;
inline bool FieldMaskTestCases::has_empty_field() const {
  return this != internal_default_instance() && empty_field_ != NULL;
}
inline void FieldMaskTestCases::clear_empty_field() {
  if (GetArenaNoVirtual() == NULL && empty_field_ != NULL) delete empty_field_;
  empty_field_ = NULL;
}
inline const ::google::protobuf::testing::FieldMaskWrapper& FieldMaskTestCases::empty_field() const {
  // @@protoc_insertion_point(field_get:google.protobuf.testing.FieldMaskTestCases.empty_field)
  return empty_field_ != NULL ? *empty_field_
                         : *::google::protobuf::testing::FieldMaskWrapper::internal_default_instance();
}
inline ::google::protobuf::testing::FieldMaskWrapper* FieldMaskTestCases::mutable_empty_field() {
  
  if (empty_field_ == NULL) {
    empty_field_ = new ::google::protobuf::testing::FieldMaskWrapper;
  }
  // @@protoc_insertion_point(field_mutable:google.protobuf.testing.FieldMaskTestCases.empty_field)
  return empty_field_;
}
inline ::google::protobuf::testing::FieldMaskWrapper* FieldMaskTestCases::release_empty_field() {
  // @@protoc_insertion_point(field_release:google.protobuf.testing.FieldMaskTestCases.empty_field)
  
  ::google::protobuf::testing::FieldMaskWrapper* temp = empty_field_;
  empty_field_ = NULL;
  return temp;
}
inline void FieldMaskTestCases::set_allocated_empty_field(::google::protobuf::testing::FieldMaskWrapper* empty_field) {
  delete empty_field_;
  empty_field_ = empty_field;
  if (empty_field) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_set_allocated:google.protobuf.testing.FieldMaskTestCases.empty_field)
}

// optional .google.protobuf.testing.FieldMaskWrapper apiary_format1 = 5;
inline bool FieldMaskTestCases::has_apiary_format1() const {
  return this != internal_default_instance() && apiary_format1_ != NULL;
}
inline void FieldMaskTestCases::clear_apiary_format1() {
  if (GetArenaNoVirtual() == NULL && apiary_format1_ != NULL) delete apiary_format1_;
  apiary_format1_ = NULL;
}
inline const ::google::protobuf::testing::FieldMaskWrapper& FieldMaskTestCases::apiary_format1() const {
  // @@protoc_insertion_point(field_get:google.protobuf.testing.FieldMaskTestCases.apiary_format1)
  return apiary_format1_ != NULL ? *apiary_format1_
                         : *::google::protobuf::testing::FieldMaskWrapper::internal_default_instance();
}
inline ::google::protobuf::testing::FieldMaskWrapper* FieldMaskTestCases::mutable_apiary_format1() {
  
  if (apiary_format1_ == NULL) {
    apiary_format1_ = new ::google::protobuf::testing::FieldMaskWrapper;
  }
  // @@protoc_insertion_point(field_mutable:google.protobuf.testing.FieldMaskTestCases.apiary_format1)
  return apiary_format1_;
}
inline ::google::protobuf::testing::FieldMaskWrapper* FieldMaskTestCases::release_apiary_format1() {
  // @@protoc_insertion_point(field_release:google.protobuf.testing.FieldMaskTestCases.apiary_format1)
  
  ::google::protobuf::testing::FieldMaskWrapper* temp = apiary_format1_;
  apiary_format1_ = NULL;
  return temp;
}
inline void FieldMaskTestCases::set_allocated_apiary_format1(::google::protobuf::testing::FieldMaskWrapper* apiary_format1) {
  delete apiary_format1_;
  apiary_format1_ = apiary_format1;
  if (apiary_format1) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_set_allocated:google.protobuf.testing.FieldMaskTestCases.apiary_format1)
}

// optional .google.protobuf.testing.FieldMaskWrapper apiary_format2 = 6;
inline bool FieldMaskTestCases::has_apiary_format2() const {
  return this != internal_default_instance() && apiary_format2_ != NULL;
}
inline void FieldMaskTestCases::clear_apiary_format2() {
  if (GetArenaNoVirtual() == NULL && apiary_format2_ != NULL) delete apiary_format2_;
  apiary_format2_ = NULL;
}
inline const ::google::protobuf::testing::FieldMaskWrapper& FieldMaskTestCases::apiary_format2() const {
  // @@protoc_insertion_point(field_get:google.protobuf.testing.FieldMaskTestCases.apiary_format2)
  return apiary_format2_ != NULL ? *apiary_format2_
                         : *::google::protobuf::testing::FieldMaskWrapper::internal_default_instance();
}
inline ::google::protobuf::testing::FieldMaskWrapper* FieldMaskTestCases::mutable_apiary_format2() {
  
  if (apiary_format2_ == NULL) {
    apiary_format2_ = new ::google::protobuf::testing::FieldMaskWrapper;
  }
  // @@protoc_insertion_point(field_mutable:google.protobuf.testing.FieldMaskTestCases.apiary_format2)
  return apiary_format2_;
}
inline ::google::protobuf::testing::FieldMaskWrapper* FieldMaskTestCases::release_apiary_format2() {
  // @@protoc_insertion_point(field_release:google.protobuf.testing.FieldMaskTestCases.apiary_format2)
  
  ::google::protobuf::testing::FieldMaskWrapper* temp = apiary_format2_;
  apiary_format2_ = NULL;
  return temp;
}
inline void FieldMaskTestCases::set_allocated_apiary_format2(::google::protobuf::testing::FieldMaskWrapper* apiary_format2) {
  delete apiary_format2_;
  apiary_format2_ = apiary_format2;
  if (apiary_format2) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_set_allocated:google.protobuf.testing.FieldMaskTestCases.apiary_format2)
}

// optional .google.protobuf.testing.FieldMaskWrapper apiary_format3 = 7;
inline bool FieldMaskTestCases::has_apiary_format3() const {
  return this != internal_default_instance() && apiary_format3_ != NULL;
}
inline void FieldMaskTestCases::clear_apiary_format3() {
  if (GetArenaNoVirtual() == NULL && apiary_format3_ != NULL) delete apiary_format3_;
  apiary_format3_ = NULL;
}
inline const ::google::protobuf::testing::FieldMaskWrapper& FieldMaskTestCases::apiary_format3() const {
  // @@protoc_insertion_point(field_get:google.protobuf.testing.FieldMaskTestCases.apiary_format3)
  return apiary_format3_ != NULL ? *apiary_format3_
                         : *::google::protobuf::testing::FieldMaskWrapper::internal_default_instance();
}
inline ::google::protobuf::testing::FieldMaskWrapper* FieldMaskTestCases::mutable_apiary_format3() {
  
  if (apiary_format3_ == NULL) {
    apiary_format3_ = new ::google::protobuf::testing::FieldMaskWrapper;
  }
  // @@protoc_insertion_point(field_mutable:google.protobuf.testing.FieldMaskTestCases.apiary_format3)
  return apiary_format3_;
}
inline ::google::protobuf::testing::FieldMaskWrapper* FieldMaskTestCases::release_apiary_format3() {
  // @@protoc_insertion_point(field_release:google.protobuf.testing.FieldMaskTestCases.apiary_format3)
  
  ::google::protobuf::testing::FieldMaskWrapper* temp = apiary_format3_;
  apiary_format3_ = NULL;
  return temp;
}
inline void FieldMaskTestCases::set_allocated_apiary_format3(::google::protobuf::testing::FieldMaskWrapper* apiary_format3) {
  delete apiary_format3_;
  apiary_format3_ = apiary_format3;
  if (apiary_format3) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_set_allocated:google.protobuf.testing.FieldMaskTestCases.apiary_format3)
}

// optional .google.protobuf.testing.FieldMaskWrapper map_key1 = 8;
inline bool FieldMaskTestCases::has_map_key1() const {
  return this != internal_default_instance() && map_key1_ != NULL;
}
inline void FieldMaskTestCases::clear_map_key1() {
  if (GetArenaNoVirtual() == NULL && map_key1_ != NULL) delete map_key1_;
  map_key1_ = NULL;
}
inline const ::google::protobuf::testing::FieldMaskWrapper& FieldMaskTestCases::map_key1() const {
  // @@protoc_insertion_point(field_get:google.protobuf.testing.FieldMaskTestCases.map_key1)
  return map_key1_ != NULL ? *map_key1_
                         : *::google::protobuf::testing::FieldMaskWrapper::internal_default_instance();
}
inline ::google::protobuf::testing::FieldMaskWrapper* FieldMaskTestCases::mutable_map_key1() {
  
  if (map_key1_ == NULL) {
    map_key1_ = new ::google::protobuf::testing::FieldMaskWrapper;
  }
  // @@protoc_insertion_point(field_mutable:google.protobuf.testing.FieldMaskTestCases.map_key1)
  return map_key1_;
}
inline ::google::protobuf::testing::FieldMaskWrapper* FieldMaskTestCases::release_map_key1() {
  // @@protoc_insertion_point(field_release:google.protobuf.testing.FieldMaskTestCases.map_key1)
  
  ::google::protobuf::testing::FieldMaskWrapper* temp = map_key1_;
  map_key1_ = NULL;
  return temp;
}
inline void FieldMaskTestCases::set_allocated_map_key1(::google::protobuf::testing::FieldMaskWrapper* map_key1) {
  delete map_key1_;
  map_key1_ = map_key1;
  if (map_key1) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_set_allocated:google.protobuf.testing.FieldMaskTestCases.map_key1)
}

// optional .google.protobuf.testing.FieldMaskWrapper map_key2 = 9;
inline bool FieldMaskTestCases::has_map_key2() const {
  return this != internal_default_instance() && map_key2_ != NULL;
}
inline void FieldMaskTestCases::clear_map_key2() {
  if (GetArenaNoVirtual() == NULL && map_key2_ != NULL) delete map_key2_;
  map_key2_ = NULL;
}
inline const ::google::protobuf::testing::FieldMaskWrapper& FieldMaskTestCases::map_key2() const {
  // @@protoc_insertion_point(field_get:google.protobuf.testing.FieldMaskTestCases.map_key2)
  return map_key2_ != NULL ? *map_key2_
                         : *::google::protobuf::testing::FieldMaskWrapper::internal_default_instance();
}
inline ::google::protobuf::testing::FieldMaskWrapper* FieldMaskTestCases::mutable_map_key2() {
  
  if (map_key2_ == NULL) {
    map_key2_ = new ::google::protobuf::testing::FieldMaskWrapper;
  }
  // @@protoc_insertion_point(field_mutable:google.protobuf.testing.FieldMaskTestCases.map_key2)
  return map_key2_;
}
inline ::google::protobuf::testing::FieldMaskWrapper* FieldMaskTestCases::release_map_key2() {
  // @@protoc_insertion_point(field_release:google.protobuf.testing.FieldMaskTestCases.map_key2)
  
  ::google::protobuf::testing::FieldMaskWrapper* temp = map_key2_;
  map_key2_ = NULL;
  return temp;
}
inline void FieldMaskTestCases::set_allocated_map_key2(::google::protobuf::testing::FieldMaskWrapper* map_key2) {
  delete map_key2_;
  map_key2_ = map_key2;
  if (map_key2) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_set_allocated:google.protobuf.testing.FieldMaskTestCases.map_key2)
}

// optional .google.protobuf.testing.FieldMaskWrapper map_key3 = 10;
inline bool FieldMaskTestCases::has_map_key3() const {
  return this != internal_default_instance() && map_key3_ != NULL;
}
inline void FieldMaskTestCases::clear_map_key3() {
  if (GetArenaNoVirtual() == NULL && map_key3_ != NULL) delete map_key3_;
  map_key3_ = NULL;
}
inline const ::google::protobuf::testing::FieldMaskWrapper& FieldMaskTestCases::map_key3() const {
  // @@protoc_insertion_point(field_get:google.protobuf.testing.FieldMaskTestCases.map_key3)
  return map_key3_ != NULL ? *map_key3_
                         : *::google::protobuf::testing::FieldMaskWrapper::internal_default_instance();
}
inline ::google::protobuf::testing::FieldMaskWrapper* FieldMaskTestCases::mutable_map_key3() {
  
  if (map_key3_ == NULL) {
    map_key3_ = new ::google::protobuf::testing::FieldMaskWrapper;
  }
  // @@protoc_insertion_point(field_mutable:google.protobuf.testing.FieldMaskTestCases.map_key3)
  return map_key3_;
}
inline ::google::protobuf::testing::FieldMaskWrapper* FieldMaskTestCases::release_map_key3() {
  // @@protoc_insertion_point(field_release:google.protobuf.testing.FieldMaskTestCases.map_key3)
  
  ::google::protobuf::testing::FieldMaskWrapper* temp = map_key3_;
  map_key3_ = NULL;
  return temp;
}
inline void FieldMaskTestCases::set_allocated_map_key3(::google::protobuf::testing::FieldMaskWrapper* map_key3) {
  delete map_key3_;
  map_key3_ = map_key3;
  if (map_key3) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_set_allocated:google.protobuf.testing.FieldMaskTestCases.map_key3)
}

// optional .google.protobuf.testing.FieldMaskWrapper map_key4 = 11;
inline bool FieldMaskTestCases::has_map_key4() const {
  return this != internal_default_instance() && map_key4_ != NULL;
}
inline void FieldMaskTestCases::clear_map_key4() {
  if (GetArenaNoVirtual() == NULL && map_key4_ != NULL) delete map_key4_;
  map_key4_ = NULL;
}
inline const ::google::protobuf::testing::FieldMaskWrapper& FieldMaskTestCases::map_key4() const {
  // @@protoc_insertion_point(field_get:google.protobuf.testing.FieldMaskTestCases.map_key4)
  return map_key4_ != NULL ? *map_key4_
                         : *::google::protobuf::testing::FieldMaskWrapper::internal_default_instance();
}
inline ::google::protobuf::testing::FieldMaskWrapper* FieldMaskTestCases::mutable_map_key4() {
  
  if (map_key4_ == NULL) {
    map_key4_ = new ::google::protobuf::testing::FieldMaskWrapper;
  }
  // @@protoc_insertion_point(field_mutable:google.protobuf.testing.FieldMaskTestCases.map_key4)
  return map_key4_;
}
inline ::google::protobuf::testing::FieldMaskWrapper* FieldMaskTestCases::release_map_key4() {
  // @@protoc_insertion_point(field_release:google.protobuf.testing.FieldMaskTestCases.map_key4)
  
  ::google::protobuf::testing::FieldMaskWrapper* temp = map_key4_;
  map_key4_ = NULL;
  return temp;
}
inline void FieldMaskTestCases::set_allocated_map_key4(::google::protobuf::testing::FieldMaskWrapper* map_key4) {
  delete map_key4_;
  map_key4_ = map_key4;
  if (map_key4) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_set_allocated:google.protobuf.testing.FieldMaskTestCases.map_key4)
}

// optional .google.protobuf.testing.FieldMaskWrapper map_key5 = 12;
inline bool FieldMaskTestCases::has_map_key5() const {
  return this != internal_default_instance() && map_key5_ != NULL;
}
inline void FieldMaskTestCases::clear_map_key5() {
  if (GetArenaNoVirtual() == NULL && map_key5_ != NULL) delete map_key5_;
  map_key5_ = NULL;
}
inline const ::google::protobuf::testing::FieldMaskWrapper& FieldMaskTestCases::map_key5() const {
  // @@protoc_insertion_point(field_get:google.protobuf.testing.FieldMaskTestCases.map_key5)
  return map_key5_ != NULL ? *map_key5_
                         : *::google::protobuf::testing::FieldMaskWrapper::internal_default_instance();
}
inline ::google::protobuf::testing::FieldMaskWrapper* FieldMaskTestCases::mutable_map_key5() {
  
  if (map_key5_ == NULL) {
    map_key5_ = new ::google::protobuf::testing::FieldMaskWrapper;
  }
  // @@protoc_insertion_point(field_mutable:google.protobuf.testing.FieldMaskTestCases.map_key5)
  return map_key5_;
}
inline ::google::protobuf::testing::FieldMaskWrapper* FieldMaskTestCases::release_map_key5() {
  // @@protoc_insertion_point(field_release:google.protobuf.testing.FieldMaskTestCases.map_key5)
  
  ::google::protobuf::testing::FieldMaskWrapper* temp = map_key5_;
  map_key5_ = NULL;
  return temp;
}
inline void FieldMaskTestCases::set_allocated_map_key5(::google::protobuf::testing::FieldMaskWrapper* map_key5) {
  delete map_key5_;
  map_key5_ = map_key5;
  if (map_key5) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_set_allocated:google.protobuf.testing.FieldMaskTestCases.map_key5)
}

inline const FieldMaskTestCases* FieldMaskTestCases::internal_default_instance() {
  return &FieldMaskTestCases_default_instance_.get();
}
// -------------------------------------------------------------------

// FieldMaskWrapper

// optional .google.protobuf.FieldMask mask = 1;
inline bool FieldMaskWrapper::has_mask() const {
  return this != internal_default_instance() && mask_ != NULL;
}
inline void FieldMaskWrapper::clear_mask() {
  if (GetArenaNoVirtual() == NULL && mask_ != NULL) delete mask_;
  mask_ = NULL;
}
inline const ::google::protobuf::FieldMask& FieldMaskWrapper::mask() const {
  // @@protoc_insertion_point(field_get:google.protobuf.testing.FieldMaskWrapper.mask)
  return mask_ != NULL ? *mask_
                         : *::google::protobuf::FieldMask::internal_default_instance();
}
inline ::google::protobuf::FieldMask* FieldMaskWrapper::mutable_mask() {
  
  if (mask_ == NULL) {
    mask_ = new ::google::protobuf::FieldMask;
  }
  // @@protoc_insertion_point(field_mutable:google.protobuf.testing.FieldMaskWrapper.mask)
  return mask_;
}
inline ::google::protobuf::FieldMask* FieldMaskWrapper::release_mask() {
  // @@protoc_insertion_point(field_release:google.protobuf.testing.FieldMaskWrapper.mask)
  
  ::google::protobuf::FieldMask* temp = mask_;
  mask_ = NULL;
  return temp;
}
inline void FieldMaskWrapper::set_allocated_mask(::google::protobuf::FieldMask* mask) {
  delete mask_;
  mask_ = mask;
  if (mask) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_set_allocated:google.protobuf.testing.FieldMaskWrapper.mask)
}

inline const FieldMaskWrapper* FieldMaskWrapper::internal_default_instance() {
  return &FieldMaskWrapper_default_instance_.get();
}
#endif  // !PROTOBUF_INLINE_NOT_IN_HEADERS
// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------


// @@protoc_insertion_point(namespace_scope)

}  // namespace testing
}  // namespace protobuf
}  // namespace google

// @@protoc_insertion_point(global_scope)

#endif  // PROTOBUF_google_2fprotobuf_2futil_2finternal_2ftestdata_2ffield_5fmask_2eproto__INCLUDED

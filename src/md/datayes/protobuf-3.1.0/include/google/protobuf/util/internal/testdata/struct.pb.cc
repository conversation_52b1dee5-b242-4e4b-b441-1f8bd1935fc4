// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: google/protobuf/util/internal/testdata/struct.proto

#define INTERNAL_SUPPRESS_PROTOBUF_FIELD_DEPRECATION
#include <google/protobuf/util/internal/testdata/struct.pb.h>

#include <algorithm>

#include <google/protobuf/stubs/common.h>
#include <google/protobuf/stubs/port.h>
#include <google/protobuf/stubs/once.h>
#include <google/protobuf/io/coded_stream.h>
#include <google/protobuf/wire_format_lite_inl.h>
#include <google/protobuf/descriptor.h>
#include <google/protobuf/generated_message_reflection.h>
#include <google/protobuf/reflection_ops.h>
#include <google/protobuf/wire_format.h>
// @@protoc_insertion_point(includes)

namespace google {
namespace protobuf {
namespace testing {

namespace {

const ::google::protobuf::Descriptor* StructTestCases_descriptor_ = NULL;
const ::google::protobuf::internal::GeneratedMessageReflection*
  StructTestCases_reflection_ = NULL;
const ::google::protobuf::Descriptor* StructWrapper_descriptor_ = NULL;
const ::google::protobuf::internal::GeneratedMessageReflection*
  StructWrapper_reflection_ = NULL;
const ::google::protobuf::Descriptor* ValueWrapper_descriptor_ = NULL;
const ::google::protobuf::internal::GeneratedMessageReflection*
  ValueWrapper_reflection_ = NULL;
const ::google::protobuf::Descriptor* RepeatedValueWrapper_descriptor_ = NULL;
const ::google::protobuf::internal::GeneratedMessageReflection*
  RepeatedValueWrapper_reflection_ = NULL;
const ::google::protobuf::Descriptor* ListValueWrapper_descriptor_ = NULL;
const ::google::protobuf::internal::GeneratedMessageReflection*
  ListValueWrapper_reflection_ = NULL;
const ::google::protobuf::Descriptor* RepeatedListValueWrapper_descriptor_ = NULL;
const ::google::protobuf::internal::GeneratedMessageReflection*
  RepeatedListValueWrapper_reflection_ = NULL;
const ::google::protobuf::Descriptor* MapOfStruct_descriptor_ = NULL;
const ::google::protobuf::internal::GeneratedMessageReflection*
  MapOfStruct_reflection_ = NULL;
const ::google::protobuf::Descriptor* MapOfStruct_StructMapEntry_descriptor_ = NULL;
const ::google::protobuf::Descriptor* MapOfStruct_ValueMapEntry_descriptor_ = NULL;
const ::google::protobuf::Descriptor* MapOfStruct_ListvalueMapEntry_descriptor_ = NULL;
const ::google::protobuf::Descriptor* Dummy_descriptor_ = NULL;
const ::google::protobuf::internal::GeneratedMessageReflection*
  Dummy_reflection_ = NULL;
const ::google::protobuf::Descriptor* StructType_descriptor_ = NULL;
const ::google::protobuf::internal::GeneratedMessageReflection*
  StructType_reflection_ = NULL;

}  // namespace


void protobuf_AssignDesc_google_2fprotobuf_2futil_2finternal_2ftestdata_2fstruct_2eproto() GOOGLE_ATTRIBUTE_COLD;
void protobuf_AssignDesc_google_2fprotobuf_2futil_2finternal_2ftestdata_2fstruct_2eproto() {
  protobuf_AddDesc_google_2fprotobuf_2futil_2finternal_2ftestdata_2fstruct_2eproto();
  const ::google::protobuf::FileDescriptor* file =
    ::google::protobuf::DescriptorPool::generated_pool()->FindFileByName(
      "google/protobuf/util/internal/testdata/struct.proto");
  GOOGLE_CHECK(file != NULL);
  StructTestCases_descriptor_ = file->message_type(0);
  static const int StructTestCases_offsets_[39] = {
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(StructTestCases, empty_value_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(StructTestCases, empty_value2_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(StructTestCases, null_value_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(StructTestCases, simple_struct_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(StructTestCases, longer_struct_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(StructTestCases, struct_with_nested_struct_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(StructTestCases, struct_with_nested_list_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(StructTestCases, struct_with_list_of_nulls_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(StructTestCases, struct_with_list_of_lists_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(StructTestCases, struct_with_list_of_structs_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(StructTestCases, struct_with_empty_list_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(StructTestCases, struct_with_list_with_empty_struct_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(StructTestCases, top_level_struct_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(StructTestCases, top_level_struct_with_empty_list_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(StructTestCases, top_level_struct_with_list_with_empty_struct_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(StructTestCases, value_wrapper_simple_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(StructTestCases, value_wrapper_with_struct_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(StructTestCases, value_wrapper_with_list_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(StructTestCases, value_wrapper_with_empty_list_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(StructTestCases, value_wrapper_with_list_with_empty_struct_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(StructTestCases, list_value_wrapper_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(StructTestCases, list_value_wrapper_with_empty_list_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(StructTestCases, list_value_wrapper_with_list_with_empty_struct_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(StructTestCases, top_level_value_simple_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(StructTestCases, top_level_value_with_struct_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(StructTestCases, top_level_value_with_list_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(StructTestCases, top_level_value_with_empty_list_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(StructTestCases, top_level_value_with_list_with_empty_struct_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(StructTestCases, top_level_listvalue_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(StructTestCases, top_level_empty_listvalue_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(StructTestCases, top_level_listvalue_with_empty_struct_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(StructTestCases, repeated_value_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(StructTestCases, repeated_value_nested_list_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(StructTestCases, repeated_value_nested_list2_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(StructTestCases, repeated_value_nested_list3_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(StructTestCases, repeated_listvalue_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(StructTestCases, map_of_struct_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(StructTestCases, map_of_struct_value_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(StructTestCases, map_of_listvalue_),
  };
  StructTestCases_reflection_ =
    ::google::protobuf::internal::GeneratedMessageReflection::NewGeneratedMessageReflection(
      StructTestCases_descriptor_,
      StructTestCases::internal_default_instance(),
      StructTestCases_offsets_,
      -1,
      -1,
      -1,
      sizeof(StructTestCases),
      GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(StructTestCases, _internal_metadata_));
  StructWrapper_descriptor_ = file->message_type(1);
  static const int StructWrapper_offsets_[1] = {
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(StructWrapper, struct__),
  };
  StructWrapper_reflection_ =
    ::google::protobuf::internal::GeneratedMessageReflection::NewGeneratedMessageReflection(
      StructWrapper_descriptor_,
      StructWrapper::internal_default_instance(),
      StructWrapper_offsets_,
      -1,
      -1,
      -1,
      sizeof(StructWrapper),
      GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(StructWrapper, _internal_metadata_));
  ValueWrapper_descriptor_ = file->message_type(2);
  static const int ValueWrapper_offsets_[1] = {
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(ValueWrapper, value_),
  };
  ValueWrapper_reflection_ =
    ::google::protobuf::internal::GeneratedMessageReflection::NewGeneratedMessageReflection(
      ValueWrapper_descriptor_,
      ValueWrapper::internal_default_instance(),
      ValueWrapper_offsets_,
      -1,
      -1,
      -1,
      sizeof(ValueWrapper),
      GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(ValueWrapper, _internal_metadata_));
  RepeatedValueWrapper_descriptor_ = file->message_type(3);
  static const int RepeatedValueWrapper_offsets_[1] = {
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(RepeatedValueWrapper, values_),
  };
  RepeatedValueWrapper_reflection_ =
    ::google::protobuf::internal::GeneratedMessageReflection::NewGeneratedMessageReflection(
      RepeatedValueWrapper_descriptor_,
      RepeatedValueWrapper::internal_default_instance(),
      RepeatedValueWrapper_offsets_,
      -1,
      -1,
      -1,
      sizeof(RepeatedValueWrapper),
      GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(RepeatedValueWrapper, _internal_metadata_));
  ListValueWrapper_descriptor_ = file->message_type(4);
  static const int ListValueWrapper_offsets_[1] = {
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(ListValueWrapper, shopping_list_),
  };
  ListValueWrapper_reflection_ =
    ::google::protobuf::internal::GeneratedMessageReflection::NewGeneratedMessageReflection(
      ListValueWrapper_descriptor_,
      ListValueWrapper::internal_default_instance(),
      ListValueWrapper_offsets_,
      -1,
      -1,
      -1,
      sizeof(ListValueWrapper),
      GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(ListValueWrapper, _internal_metadata_));
  RepeatedListValueWrapper_descriptor_ = file->message_type(5);
  static const int RepeatedListValueWrapper_offsets_[1] = {
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(RepeatedListValueWrapper, dimensions_),
  };
  RepeatedListValueWrapper_reflection_ =
    ::google::protobuf::internal::GeneratedMessageReflection::NewGeneratedMessageReflection(
      RepeatedListValueWrapper_descriptor_,
      RepeatedListValueWrapper::internal_default_instance(),
      RepeatedListValueWrapper_offsets_,
      -1,
      -1,
      -1,
      sizeof(RepeatedListValueWrapper),
      GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(RepeatedListValueWrapper, _internal_metadata_));
  MapOfStruct_descriptor_ = file->message_type(6);
  static const int MapOfStruct_offsets_[3] = {
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MapOfStruct, struct_map_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MapOfStruct, value_map_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MapOfStruct, listvalue_map_),
  };
  MapOfStruct_reflection_ =
    ::google::protobuf::internal::GeneratedMessageReflection::NewGeneratedMessageReflection(
      MapOfStruct_descriptor_,
      MapOfStruct::internal_default_instance(),
      MapOfStruct_offsets_,
      -1,
      -1,
      -1,
      sizeof(MapOfStruct),
      GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MapOfStruct, _internal_metadata_));
  MapOfStruct_StructMapEntry_descriptor_ = MapOfStruct_descriptor_->nested_type(0);
  MapOfStruct_ValueMapEntry_descriptor_ = MapOfStruct_descriptor_->nested_type(1);
  MapOfStruct_ListvalueMapEntry_descriptor_ = MapOfStruct_descriptor_->nested_type(2);
  Dummy_descriptor_ = file->message_type(7);
  static const int Dummy_offsets_[1] = {
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(Dummy, text_),
  };
  Dummy_reflection_ =
    ::google::protobuf::internal::GeneratedMessageReflection::NewGeneratedMessageReflection(
      Dummy_descriptor_,
      Dummy::internal_default_instance(),
      Dummy_offsets_,
      -1,
      -1,
      -1,
      sizeof(Dummy),
      GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(Dummy, _internal_metadata_));
  StructType_descriptor_ = file->message_type(8);
  static const int StructType_offsets_[1] = {
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(StructType, object_),
  };
  StructType_reflection_ =
    ::google::protobuf::internal::GeneratedMessageReflection::NewGeneratedMessageReflection(
      StructType_descriptor_,
      StructType::internal_default_instance(),
      StructType_offsets_,
      -1,
      -1,
      -1,
      sizeof(StructType),
      GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(StructType, _internal_metadata_));
}

namespace {

GOOGLE_PROTOBUF_DECLARE_ONCE(protobuf_AssignDescriptors_once_);
void protobuf_AssignDescriptorsOnce() {
  ::google::protobuf::GoogleOnceInit(&protobuf_AssignDescriptors_once_,
                 &protobuf_AssignDesc_google_2fprotobuf_2futil_2finternal_2ftestdata_2fstruct_2eproto);
}

void protobuf_RegisterTypes(const ::std::string&) GOOGLE_ATTRIBUTE_COLD;
void protobuf_RegisterTypes(const ::std::string&) {
  protobuf_AssignDescriptorsOnce();
  ::google::protobuf::MessageFactory::InternalRegisterGeneratedMessage(
      StructTestCases_descriptor_, StructTestCases::internal_default_instance());
  ::google::protobuf::MessageFactory::InternalRegisterGeneratedMessage(
      StructWrapper_descriptor_, StructWrapper::internal_default_instance());
  ::google::protobuf::MessageFactory::InternalRegisterGeneratedMessage(
      ValueWrapper_descriptor_, ValueWrapper::internal_default_instance());
  ::google::protobuf::MessageFactory::InternalRegisterGeneratedMessage(
      RepeatedValueWrapper_descriptor_, RepeatedValueWrapper::internal_default_instance());
  ::google::protobuf::MessageFactory::InternalRegisterGeneratedMessage(
      ListValueWrapper_descriptor_, ListValueWrapper::internal_default_instance());
  ::google::protobuf::MessageFactory::InternalRegisterGeneratedMessage(
      RepeatedListValueWrapper_descriptor_, RepeatedListValueWrapper::internal_default_instance());
  ::google::protobuf::MessageFactory::InternalRegisterGeneratedMessage(
      MapOfStruct_descriptor_, MapOfStruct::internal_default_instance());
  ::google::protobuf::MessageFactory::InternalRegisterGeneratedMessage(
        MapOfStruct_StructMapEntry_descriptor_,
        ::google::protobuf::internal::MapEntry<
            ::std::string,
            ::google::protobuf::Struct,
            ::google::protobuf::internal::WireFormatLite::TYPE_STRING,
            ::google::protobuf::internal::WireFormatLite::TYPE_MESSAGE,
            0>::CreateDefaultInstance(
                MapOfStruct_StructMapEntry_descriptor_));
  ::google::protobuf::MessageFactory::InternalRegisterGeneratedMessage(
        MapOfStruct_ValueMapEntry_descriptor_,
        ::google::protobuf::internal::MapEntry<
            ::std::string,
            ::google::protobuf::Value,
            ::google::protobuf::internal::WireFormatLite::TYPE_STRING,
            ::google::protobuf::internal::WireFormatLite::TYPE_MESSAGE,
            0>::CreateDefaultInstance(
                MapOfStruct_ValueMapEntry_descriptor_));
  ::google::protobuf::MessageFactory::InternalRegisterGeneratedMessage(
        MapOfStruct_ListvalueMapEntry_descriptor_,
        ::google::protobuf::internal::MapEntry<
            ::std::string,
            ::google::protobuf::ListValue,
            ::google::protobuf::internal::WireFormatLite::TYPE_STRING,
            ::google::protobuf::internal::WireFormatLite::TYPE_MESSAGE,
            0>::CreateDefaultInstance(
                MapOfStruct_ListvalueMapEntry_descriptor_));
  ::google::protobuf::MessageFactory::InternalRegisterGeneratedMessage(
      Dummy_descriptor_, Dummy::internal_default_instance());
  ::google::protobuf::MessageFactory::InternalRegisterGeneratedMessage(
      StructType_descriptor_, StructType::internal_default_instance());
}

}  // namespace

void protobuf_ShutdownFile_google_2fprotobuf_2futil_2finternal_2ftestdata_2fstruct_2eproto() {
  StructTestCases_default_instance_.Shutdown();
  delete StructTestCases_reflection_;
  StructWrapper_default_instance_.Shutdown();
  delete StructWrapper_reflection_;
  ValueWrapper_default_instance_.Shutdown();
  delete ValueWrapper_reflection_;
  RepeatedValueWrapper_default_instance_.Shutdown();
  delete RepeatedValueWrapper_reflection_;
  ListValueWrapper_default_instance_.Shutdown();
  delete ListValueWrapper_reflection_;
  RepeatedListValueWrapper_default_instance_.Shutdown();
  delete RepeatedListValueWrapper_reflection_;
  MapOfStruct_default_instance_.Shutdown();
  delete MapOfStruct_reflection_;
  Dummy_default_instance_.Shutdown();
  delete Dummy_reflection_;
  StructType_default_instance_.Shutdown();
  delete StructType_reflection_;
}

void protobuf_InitDefaults_google_2fprotobuf_2futil_2finternal_2ftestdata_2fstruct_2eproto_impl() {
  GOOGLE_PROTOBUF_VERIFY_VERSION;

  ::google::protobuf::protobuf_InitDefaults_google_2fprotobuf_2fstruct_2eproto();
  StructTestCases_default_instance_.DefaultConstruct();
  StructWrapper_default_instance_.DefaultConstruct();
  ValueWrapper_default_instance_.DefaultConstruct();
  RepeatedValueWrapper_default_instance_.DefaultConstruct();
  ListValueWrapper_default_instance_.DefaultConstruct();
  RepeatedListValueWrapper_default_instance_.DefaultConstruct();
  ::google::protobuf::internal::GetEmptyString();
  MapOfStruct_default_instance_.DefaultConstruct();
  ::google::protobuf::internal::GetEmptyString();
  Dummy_default_instance_.DefaultConstruct();
  StructType_default_instance_.DefaultConstruct();
  StructTestCases_default_instance_.get_mutable()->InitAsDefaultInstance();
  StructWrapper_default_instance_.get_mutable()->InitAsDefaultInstance();
  ValueWrapper_default_instance_.get_mutable()->InitAsDefaultInstance();
  RepeatedValueWrapper_default_instance_.get_mutable()->InitAsDefaultInstance();
  ListValueWrapper_default_instance_.get_mutable()->InitAsDefaultInstance();
  RepeatedListValueWrapper_default_instance_.get_mutable()->InitAsDefaultInstance();
  MapOfStruct_default_instance_.get_mutable()->InitAsDefaultInstance();
  Dummy_default_instance_.get_mutable()->InitAsDefaultInstance();
  StructType_default_instance_.get_mutable()->InitAsDefaultInstance();
}

GOOGLE_PROTOBUF_DECLARE_ONCE(protobuf_InitDefaults_google_2fprotobuf_2futil_2finternal_2ftestdata_2fstruct_2eproto_once_);
void protobuf_InitDefaults_google_2fprotobuf_2futil_2finternal_2ftestdata_2fstruct_2eproto() {
  ::google::protobuf::GoogleOnceInit(&protobuf_InitDefaults_google_2fprotobuf_2futil_2finternal_2ftestdata_2fstruct_2eproto_once_,
                 &protobuf_InitDefaults_google_2fprotobuf_2futil_2finternal_2ftestdata_2fstruct_2eproto_impl);
}
void protobuf_AddDesc_google_2fprotobuf_2futil_2finternal_2ftestdata_2fstruct_2eproto_impl() {
  GOOGLE_PROTOBUF_VERIFY_VERSION;

  protobuf_InitDefaults_google_2fprotobuf_2futil_2finternal_2ftestdata_2fstruct_2eproto();
  ::google::protobuf::DescriptorPool::InternalAddGeneratedFile(
    "\n3google/protobuf/util/internal/testdata"
    "/struct.proto\022\027google.protobuf.testing\032\034"
    "google/protobuf/struct.proto\"\360\025\n\017StructT"
    "estCases\022;\n\013empty_value\030\001 \001(\0132&.google.p"
    "rotobuf.testing.StructWrapper\022<\n\014empty_v"
    "alue2\030\002 \001(\0132&.google.protobuf.testing.St"
    "ructWrapper\022:\n\nnull_value\030\003 \001(\0132&.google"
    ".protobuf.testing.StructWrapper\022=\n\rsimpl"
    "e_struct\030\004 \001(\0132&.google.protobuf.testing"
    ".StructWrapper\022=\n\rlonger_struct\030\005 \001(\0132&."
    "google.protobuf.testing.StructWrapper\022I\n"
    "\031struct_with_nested_struct\030\006 \001(\0132&.googl"
    "e.protobuf.testing.StructWrapper\022G\n\027stru"
    "ct_with_nested_list\030\007 \001(\0132&.google.proto"
    "buf.testing.StructWrapper\022I\n\031struct_with"
    "_list_of_nulls\030\010 \001(\0132&.google.protobuf.t"
    "esting.StructWrapper\022I\n\031struct_with_list"
    "_of_lists\030\t \001(\0132&.google.protobuf.testin"
    "g.StructWrapper\022K\n\033struct_with_list_of_s"
    "tructs\030\n \001(\0132&.google.protobuf.testing.S"
    "tructWrapper\022F\n\026struct_with_empty_list\030\013"
    " \001(\0132&.google.protobuf.testing.StructWra"
    "pper\022R\n\"struct_with_list_with_empty_stru"
    "ct\030\014 \001(\0132&.google.protobuf.testing.Struc"
    "tWrapper\0221\n\020top_level_struct\030\r \001(\0132\027.goo"
    "gle.protobuf.Struct\022A\n top_level_struct_"
    "with_empty_list\030\016 \001(\0132\027.google.protobuf."
    "Struct\022M\n,top_level_struct_with_list_wit"
    "h_empty_struct\030\017 \001(\0132\027.google.protobuf.S"
    "truct\022C\n\024value_wrapper_simple\030\020 \001(\0132%.go"
    "ogle.protobuf.testing.ValueWrapper\022H\n\031va"
    "lue_wrapper_with_struct\030\021 \001(\0132%.google.p"
    "rotobuf.testing.ValueWrapper\022F\n\027value_wr"
    "apper_with_list\030\022 \001(\0132%.google.protobuf."
    "testing.ValueWrapper\022L\n\035value_wrapper_wi"
    "th_empty_list\030\023 \001(\0132%.google.protobuf.te"
    "sting.ValueWrapper\022X\n)value_wrapper_with"
    "_list_with_empty_struct\030\024 \001(\0132%.google.p"
    "rotobuf.testing.ValueWrapper\022E\n\022list_val"
    "ue_wrapper\030\025 \001(\0132).google.protobuf.testi"
    "ng.ListValueWrapper\022U\n\"list_value_wrappe"
    "r_with_empty_list\030\026 \001(\0132).google.protobu"
    "f.testing.ListValueWrapper\022a\n.list_value"
    "_wrapper_with_list_with_empty_struct\030\027 \001"
    "(\0132).google.protobuf.testing.ListValueWr"
    "apper\0226\n\026top_level_value_simple\030\030 \001(\0132\026."
    "google.protobuf.Value\022;\n\033top_level_value"
    "_with_struct\030\031 \001(\0132\026.google.protobuf.Val"
    "ue\0229\n\031top_level_value_with_list\030\032 \001(\0132\026."
    "google.protobuf.Value\022\?\n\037top_level_value"
    "_with_empty_list\030\033 \001(\0132\026.google.protobuf"
    ".Value\022K\n+top_level_value_with_list_with"
    "_empty_struct\030\034 \001(\0132\026.google.protobuf.Va"
    "lue\0227\n\023top_level_listvalue\030\035 \001(\0132\032.googl"
    "e.protobuf.ListValue\022=\n\031top_level_empty_"
    "listvalue\030\036 \001(\0132\032.google.protobuf.ListVa"
    "lue\022I\n%top_level_listvalue_with_empty_st"
    "ruct\030\037 \001(\0132\032.google.protobuf.ListValue\022E"
    "\n\016repeated_value\030  \001(\0132-.google.protobuf"
    ".testing.RepeatedValueWrapper\022Q\n\032repeate"
    "d_value_nested_list\030! \001(\0132-.google.proto"
    "buf.testing.RepeatedValueWrapper\022R\n\033repe"
    "ated_value_nested_list2\030\" \001(\0132-.google.p"
    "rotobuf.testing.RepeatedValueWrapper\022R\n\033"
    "repeated_value_nested_list3\030# \001(\0132-.goog"
    "le.protobuf.testing.RepeatedValueWrapper"
    "\022M\n\022repeated_listvalue\030$ \001(\01321.google.pr"
    "otobuf.testing.RepeatedListValueWrapper\022"
    ";\n\rmap_of_struct\030% \001(\0132$.google.protobuf"
    ".testing.MapOfStruct\022A\n\023map_of_struct_va"
    "lue\030& \001(\0132$.google.protobuf.testing.MapO"
    "fStruct\022>\n\020map_of_listvalue\030\' \001(\0132$.goog"
    "le.protobuf.testing.MapOfStruct\"8\n\rStruc"
    "tWrapper\022\'\n\006struct\030\001 \001(\0132\027.google.protob"
    "uf.Struct\"5\n\014ValueWrapper\022%\n\005value\030\001 \001(\013"
    "2\026.google.protobuf.Value\">\n\024RepeatedValu"
    "eWrapper\022&\n\006values\030\001 \003(\0132\026.google.protob"
    "uf.Value\"E\n\020ListValueWrapper\0221\n\rshopping"
    "_list\030\001 \001(\0132\032.google.protobuf.ListValue\""
    "J\n\030RepeatedListValueWrapper\022.\n\ndimension"
    "s\030\001 \003(\0132\032.google.protobuf.ListValue\"\321\003\n\013"
    "MapOfStruct\022G\n\nstruct_map\030\001 \003(\01323.google"
    ".protobuf.testing.MapOfStruct.StructMapE"
    "ntry\022E\n\tvalue_map\030\002 \003(\01322.google.protobu"
    "f.testing.MapOfStruct.ValueMapEntry\022M\n\rl"
    "istvalue_map\030\003 \003(\01326.google.protobuf.tes"
    "ting.MapOfStruct.ListvalueMapEntry\032I\n\016St"
    "ructMapEntry\022\013\n\003key\030\001 \001(\t\022&\n\005value\030\002 \001(\013"
    "2\027.google.protobuf.Struct:\0028\001\032G\n\rValueMa"
    "pEntry\022\013\n\003key\030\001 \001(\t\022%\n\005value\030\002 \001(\0132\026.goo"
    "gle.protobuf.Value:\0028\001\032O\n\021ListvalueMapEn"
    "try\022\013\n\003key\030\001 \001(\t\022)\n\005value\030\002 \001(\0132\032.google"
    ".protobuf.ListValue:\0028\001\"\025\n\005Dummy\022\014\n\004text"
    "\030\001 \001(\t\"5\n\nStructType\022\'\n\006object\030\001 \001(\0132\027.g"
    "oogle.protobuf.Struct2e\n\021StructTestServi"
    "ce\022P\n\004Call\022\036.google.protobuf.testing.Dum"
    "my\032(.google.protobuf.testing.StructTestC"
    "asesb\006proto3", 3892);
  ::google::protobuf::MessageFactory::InternalRegisterGeneratedFile(
    "google/protobuf/util/internal/testdata/struct.proto", &protobuf_RegisterTypes);
  ::google::protobuf::protobuf_AddDesc_google_2fprotobuf_2fstruct_2eproto();
  ::google::protobuf::internal::OnShutdown(&protobuf_ShutdownFile_google_2fprotobuf_2futil_2finternal_2ftestdata_2fstruct_2eproto);
}

GOOGLE_PROTOBUF_DECLARE_ONCE(protobuf_AddDesc_google_2fprotobuf_2futil_2finternal_2ftestdata_2fstruct_2eproto_once_);
void protobuf_AddDesc_google_2fprotobuf_2futil_2finternal_2ftestdata_2fstruct_2eproto() {
  ::google::protobuf::GoogleOnceInit(&protobuf_AddDesc_google_2fprotobuf_2futil_2finternal_2ftestdata_2fstruct_2eproto_once_,
                 &protobuf_AddDesc_google_2fprotobuf_2futil_2finternal_2ftestdata_2fstruct_2eproto_impl);
}
// Force AddDescriptors() to be called at static initialization time.
struct StaticDescriptorInitializer_google_2fprotobuf_2futil_2finternal_2ftestdata_2fstruct_2eproto {
  StaticDescriptorInitializer_google_2fprotobuf_2futil_2finternal_2ftestdata_2fstruct_2eproto() {
    protobuf_AddDesc_google_2fprotobuf_2futil_2finternal_2ftestdata_2fstruct_2eproto();
  }
} static_descriptor_initializer_google_2fprotobuf_2futil_2finternal_2ftestdata_2fstruct_2eproto_;

namespace {

static void MergeFromFail(int line) GOOGLE_ATTRIBUTE_COLD GOOGLE_ATTRIBUTE_NORETURN;
static void MergeFromFail(int line) {
  ::google::protobuf::internal::MergeFromFail(__FILE__, line);
}

}  // namespace


// ===================================================================

#if !defined(_MSC_VER) || _MSC_VER >= 1900
const int StructTestCases::kEmptyValueFieldNumber;
const int StructTestCases::kEmptyValue2FieldNumber;
const int StructTestCases::kNullValueFieldNumber;
const int StructTestCases::kSimpleStructFieldNumber;
const int StructTestCases::kLongerStructFieldNumber;
const int StructTestCases::kStructWithNestedStructFieldNumber;
const int StructTestCases::kStructWithNestedListFieldNumber;
const int StructTestCases::kStructWithListOfNullsFieldNumber;
const int StructTestCases::kStructWithListOfListsFieldNumber;
const int StructTestCases::kStructWithListOfStructsFieldNumber;
const int StructTestCases::kStructWithEmptyListFieldNumber;
const int StructTestCases::kStructWithListWithEmptyStructFieldNumber;
const int StructTestCases::kTopLevelStructFieldNumber;
const int StructTestCases::kTopLevelStructWithEmptyListFieldNumber;
const int StructTestCases::kTopLevelStructWithListWithEmptyStructFieldNumber;
const int StructTestCases::kValueWrapperSimpleFieldNumber;
const int StructTestCases::kValueWrapperWithStructFieldNumber;
const int StructTestCases::kValueWrapperWithListFieldNumber;
const int StructTestCases::kValueWrapperWithEmptyListFieldNumber;
const int StructTestCases::kValueWrapperWithListWithEmptyStructFieldNumber;
const int StructTestCases::kListValueWrapperFieldNumber;
const int StructTestCases::kListValueWrapperWithEmptyListFieldNumber;
const int StructTestCases::kListValueWrapperWithListWithEmptyStructFieldNumber;
const int StructTestCases::kTopLevelValueSimpleFieldNumber;
const int StructTestCases::kTopLevelValueWithStructFieldNumber;
const int StructTestCases::kTopLevelValueWithListFieldNumber;
const int StructTestCases::kTopLevelValueWithEmptyListFieldNumber;
const int StructTestCases::kTopLevelValueWithListWithEmptyStructFieldNumber;
const int StructTestCases::kTopLevelListvalueFieldNumber;
const int StructTestCases::kTopLevelEmptyListvalueFieldNumber;
const int StructTestCases::kTopLevelListvalueWithEmptyStructFieldNumber;
const int StructTestCases::kRepeatedValueFieldNumber;
const int StructTestCases::kRepeatedValueNestedListFieldNumber;
const int StructTestCases::kRepeatedValueNestedList2FieldNumber;
const int StructTestCases::kRepeatedValueNestedList3FieldNumber;
const int StructTestCases::kRepeatedListvalueFieldNumber;
const int StructTestCases::kMapOfStructFieldNumber;
const int StructTestCases::kMapOfStructValueFieldNumber;
const int StructTestCases::kMapOfListvalueFieldNumber;
#endif  // !defined(_MSC_VER) || _MSC_VER >= 1900

StructTestCases::StructTestCases()
  : ::google::protobuf::Message(), _internal_metadata_(NULL) {
  if (this != internal_default_instance()) protobuf_InitDefaults_google_2fprotobuf_2futil_2finternal_2ftestdata_2fstruct_2eproto();
  SharedCtor();
  // @@protoc_insertion_point(constructor:google.protobuf.testing.StructTestCases)
}

void StructTestCases::InitAsDefaultInstance() {
  empty_value_ = const_cast< ::google::protobuf::testing::StructWrapper*>(
      ::google::protobuf::testing::StructWrapper::internal_default_instance());
  empty_value2_ = const_cast< ::google::protobuf::testing::StructWrapper*>(
      ::google::protobuf::testing::StructWrapper::internal_default_instance());
  null_value_ = const_cast< ::google::protobuf::testing::StructWrapper*>(
      ::google::protobuf::testing::StructWrapper::internal_default_instance());
  simple_struct_ = const_cast< ::google::protobuf::testing::StructWrapper*>(
      ::google::protobuf::testing::StructWrapper::internal_default_instance());
  longer_struct_ = const_cast< ::google::protobuf::testing::StructWrapper*>(
      ::google::protobuf::testing::StructWrapper::internal_default_instance());
  struct_with_nested_struct_ = const_cast< ::google::protobuf::testing::StructWrapper*>(
      ::google::protobuf::testing::StructWrapper::internal_default_instance());
  struct_with_nested_list_ = const_cast< ::google::protobuf::testing::StructWrapper*>(
      ::google::protobuf::testing::StructWrapper::internal_default_instance());
  struct_with_list_of_nulls_ = const_cast< ::google::protobuf::testing::StructWrapper*>(
      ::google::protobuf::testing::StructWrapper::internal_default_instance());
  struct_with_list_of_lists_ = const_cast< ::google::protobuf::testing::StructWrapper*>(
      ::google::protobuf::testing::StructWrapper::internal_default_instance());
  struct_with_list_of_structs_ = const_cast< ::google::protobuf::testing::StructWrapper*>(
      ::google::protobuf::testing::StructWrapper::internal_default_instance());
  struct_with_empty_list_ = const_cast< ::google::protobuf::testing::StructWrapper*>(
      ::google::protobuf::testing::StructWrapper::internal_default_instance());
  struct_with_list_with_empty_struct_ = const_cast< ::google::protobuf::testing::StructWrapper*>(
      ::google::protobuf::testing::StructWrapper::internal_default_instance());
  top_level_struct_ = const_cast< ::google::protobuf::Struct*>(
      ::google::protobuf::Struct::internal_default_instance());
  top_level_struct_with_empty_list_ = const_cast< ::google::protobuf::Struct*>(
      ::google::protobuf::Struct::internal_default_instance());
  top_level_struct_with_list_with_empty_struct_ = const_cast< ::google::protobuf::Struct*>(
      ::google::protobuf::Struct::internal_default_instance());
  value_wrapper_simple_ = const_cast< ::google::protobuf::testing::ValueWrapper*>(
      ::google::protobuf::testing::ValueWrapper::internal_default_instance());
  value_wrapper_with_struct_ = const_cast< ::google::protobuf::testing::ValueWrapper*>(
      ::google::protobuf::testing::ValueWrapper::internal_default_instance());
  value_wrapper_with_list_ = const_cast< ::google::protobuf::testing::ValueWrapper*>(
      ::google::protobuf::testing::ValueWrapper::internal_default_instance());
  value_wrapper_with_empty_list_ = const_cast< ::google::protobuf::testing::ValueWrapper*>(
      ::google::protobuf::testing::ValueWrapper::internal_default_instance());
  value_wrapper_with_list_with_empty_struct_ = const_cast< ::google::protobuf::testing::ValueWrapper*>(
      ::google::protobuf::testing::ValueWrapper::internal_default_instance());
  list_value_wrapper_ = const_cast< ::google::protobuf::testing::ListValueWrapper*>(
      ::google::protobuf::testing::ListValueWrapper::internal_default_instance());
  list_value_wrapper_with_empty_list_ = const_cast< ::google::protobuf::testing::ListValueWrapper*>(
      ::google::protobuf::testing::ListValueWrapper::internal_default_instance());
  list_value_wrapper_with_list_with_empty_struct_ = const_cast< ::google::protobuf::testing::ListValueWrapper*>(
      ::google::protobuf::testing::ListValueWrapper::internal_default_instance());
  top_level_value_simple_ = const_cast< ::google::protobuf::Value*>(
      ::google::protobuf::Value::internal_default_instance());
  top_level_value_with_struct_ = const_cast< ::google::protobuf::Value*>(
      ::google::protobuf::Value::internal_default_instance());
  top_level_value_with_list_ = const_cast< ::google::protobuf::Value*>(
      ::google::protobuf::Value::internal_default_instance());
  top_level_value_with_empty_list_ = const_cast< ::google::protobuf::Value*>(
      ::google::protobuf::Value::internal_default_instance());
  top_level_value_with_list_with_empty_struct_ = const_cast< ::google::protobuf::Value*>(
      ::google::protobuf::Value::internal_default_instance());
  top_level_listvalue_ = const_cast< ::google::protobuf::ListValue*>(
      ::google::protobuf::ListValue::internal_default_instance());
  top_level_empty_listvalue_ = const_cast< ::google::protobuf::ListValue*>(
      ::google::protobuf::ListValue::internal_default_instance());
  top_level_listvalue_with_empty_struct_ = const_cast< ::google::protobuf::ListValue*>(
      ::google::protobuf::ListValue::internal_default_instance());
  repeated_value_ = const_cast< ::google::protobuf::testing::RepeatedValueWrapper*>(
      ::google::protobuf::testing::RepeatedValueWrapper::internal_default_instance());
  repeated_value_nested_list_ = const_cast< ::google::protobuf::testing::RepeatedValueWrapper*>(
      ::google::protobuf::testing::RepeatedValueWrapper::internal_default_instance());
  repeated_value_nested_list2_ = const_cast< ::google::protobuf::testing::RepeatedValueWrapper*>(
      ::google::protobuf::testing::RepeatedValueWrapper::internal_default_instance());
  repeated_value_nested_list3_ = const_cast< ::google::protobuf::testing::RepeatedValueWrapper*>(
      ::google::protobuf::testing::RepeatedValueWrapper::internal_default_instance());
  repeated_listvalue_ = const_cast< ::google::protobuf::testing::RepeatedListValueWrapper*>(
      ::google::protobuf::testing::RepeatedListValueWrapper::internal_default_instance());
  map_of_struct_ = const_cast< ::google::protobuf::testing::MapOfStruct*>(
      ::google::protobuf::testing::MapOfStruct::internal_default_instance());
  map_of_struct_value_ = const_cast< ::google::protobuf::testing::MapOfStruct*>(
      ::google::protobuf::testing::MapOfStruct::internal_default_instance());
  map_of_listvalue_ = const_cast< ::google::protobuf::testing::MapOfStruct*>(
      ::google::protobuf::testing::MapOfStruct::internal_default_instance());
}

StructTestCases::StructTestCases(const StructTestCases& from)
  : ::google::protobuf::Message(),
    _internal_metadata_(NULL) {
  SharedCtor();
  UnsafeMergeFrom(from);
  // @@protoc_insertion_point(copy_constructor:google.protobuf.testing.StructTestCases)
}

void StructTestCases::SharedCtor() {
  empty_value_ = NULL;
  empty_value2_ = NULL;
  null_value_ = NULL;
  simple_struct_ = NULL;
  longer_struct_ = NULL;
  struct_with_nested_struct_ = NULL;
  struct_with_nested_list_ = NULL;
  struct_with_list_of_nulls_ = NULL;
  struct_with_list_of_lists_ = NULL;
  struct_with_list_of_structs_ = NULL;
  struct_with_empty_list_ = NULL;
  struct_with_list_with_empty_struct_ = NULL;
  top_level_struct_ = NULL;
  top_level_struct_with_empty_list_ = NULL;
  top_level_struct_with_list_with_empty_struct_ = NULL;
  value_wrapper_simple_ = NULL;
  value_wrapper_with_struct_ = NULL;
  value_wrapper_with_list_ = NULL;
  value_wrapper_with_empty_list_ = NULL;
  value_wrapper_with_list_with_empty_struct_ = NULL;
  list_value_wrapper_ = NULL;
  list_value_wrapper_with_empty_list_ = NULL;
  list_value_wrapper_with_list_with_empty_struct_ = NULL;
  top_level_value_simple_ = NULL;
  top_level_value_with_struct_ = NULL;
  top_level_value_with_list_ = NULL;
  top_level_value_with_empty_list_ = NULL;
  top_level_value_with_list_with_empty_struct_ = NULL;
  top_level_listvalue_ = NULL;
  top_level_empty_listvalue_ = NULL;
  top_level_listvalue_with_empty_struct_ = NULL;
  repeated_value_ = NULL;
  repeated_value_nested_list_ = NULL;
  repeated_value_nested_list2_ = NULL;
  repeated_value_nested_list3_ = NULL;
  repeated_listvalue_ = NULL;
  map_of_struct_ = NULL;
  map_of_struct_value_ = NULL;
  map_of_listvalue_ = NULL;
  _cached_size_ = 0;
}

StructTestCases::~StructTestCases() {
  // @@protoc_insertion_point(destructor:google.protobuf.testing.StructTestCases)
  SharedDtor();
}

void StructTestCases::SharedDtor() {
  if (this != &StructTestCases_default_instance_.get()) {
    delete empty_value_;
    delete empty_value2_;
    delete null_value_;
    delete simple_struct_;
    delete longer_struct_;
    delete struct_with_nested_struct_;
    delete struct_with_nested_list_;
    delete struct_with_list_of_nulls_;
    delete struct_with_list_of_lists_;
    delete struct_with_list_of_structs_;
    delete struct_with_empty_list_;
    delete struct_with_list_with_empty_struct_;
    delete top_level_struct_;
    delete top_level_struct_with_empty_list_;
    delete top_level_struct_with_list_with_empty_struct_;
    delete value_wrapper_simple_;
    delete value_wrapper_with_struct_;
    delete value_wrapper_with_list_;
    delete value_wrapper_with_empty_list_;
    delete value_wrapper_with_list_with_empty_struct_;
    delete list_value_wrapper_;
    delete list_value_wrapper_with_empty_list_;
    delete list_value_wrapper_with_list_with_empty_struct_;
    delete top_level_value_simple_;
    delete top_level_value_with_struct_;
    delete top_level_value_with_list_;
    delete top_level_value_with_empty_list_;
    delete top_level_value_with_list_with_empty_struct_;
    delete top_level_listvalue_;
    delete top_level_empty_listvalue_;
    delete top_level_listvalue_with_empty_struct_;
    delete repeated_value_;
    delete repeated_value_nested_list_;
    delete repeated_value_nested_list2_;
    delete repeated_value_nested_list3_;
    delete repeated_listvalue_;
    delete map_of_struct_;
    delete map_of_struct_value_;
    delete map_of_listvalue_;
  }
}

void StructTestCases::SetCachedSize(int size) const {
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
}
const ::google::protobuf::Descriptor* StructTestCases::descriptor() {
  protobuf_AssignDescriptorsOnce();
  return StructTestCases_descriptor_;
}

const StructTestCases& StructTestCases::default_instance() {
  protobuf_InitDefaults_google_2fprotobuf_2futil_2finternal_2ftestdata_2fstruct_2eproto();
  return *internal_default_instance();
}

::google::protobuf::internal::ExplicitlyConstructed<StructTestCases> StructTestCases_default_instance_;

StructTestCases* StructTestCases::New(::google::protobuf::Arena* arena) const {
  StructTestCases* n = new StructTestCases;
  if (arena != NULL) {
    arena->Own(n);
  }
  return n;
}

void StructTestCases::Clear() {
// @@protoc_insertion_point(message_clear_start:google.protobuf.testing.StructTestCases)
  if (GetArenaNoVirtual() == NULL && empty_value_ != NULL) delete empty_value_;
  empty_value_ = NULL;
  if (GetArenaNoVirtual() == NULL && empty_value2_ != NULL) delete empty_value2_;
  empty_value2_ = NULL;
  if (GetArenaNoVirtual() == NULL && null_value_ != NULL) delete null_value_;
  null_value_ = NULL;
  if (GetArenaNoVirtual() == NULL && simple_struct_ != NULL) delete simple_struct_;
  simple_struct_ = NULL;
  if (GetArenaNoVirtual() == NULL && longer_struct_ != NULL) delete longer_struct_;
  longer_struct_ = NULL;
  if (GetArenaNoVirtual() == NULL && struct_with_nested_struct_ != NULL) delete struct_with_nested_struct_;
  struct_with_nested_struct_ = NULL;
  if (GetArenaNoVirtual() == NULL && struct_with_nested_list_ != NULL) delete struct_with_nested_list_;
  struct_with_nested_list_ = NULL;
  if (GetArenaNoVirtual() == NULL && struct_with_list_of_nulls_ != NULL) delete struct_with_list_of_nulls_;
  struct_with_list_of_nulls_ = NULL;
  if (GetArenaNoVirtual() == NULL && struct_with_list_of_lists_ != NULL) delete struct_with_list_of_lists_;
  struct_with_list_of_lists_ = NULL;
  if (GetArenaNoVirtual() == NULL && struct_with_list_of_structs_ != NULL) delete struct_with_list_of_structs_;
  struct_with_list_of_structs_ = NULL;
  if (GetArenaNoVirtual() == NULL && struct_with_empty_list_ != NULL) delete struct_with_empty_list_;
  struct_with_empty_list_ = NULL;
  if (GetArenaNoVirtual() == NULL && struct_with_list_with_empty_struct_ != NULL) delete struct_with_list_with_empty_struct_;
  struct_with_list_with_empty_struct_ = NULL;
  if (GetArenaNoVirtual() == NULL && top_level_struct_ != NULL) delete top_level_struct_;
  top_level_struct_ = NULL;
  if (GetArenaNoVirtual() == NULL && top_level_struct_with_empty_list_ != NULL) delete top_level_struct_with_empty_list_;
  top_level_struct_with_empty_list_ = NULL;
  if (GetArenaNoVirtual() == NULL && top_level_struct_with_list_with_empty_struct_ != NULL) delete top_level_struct_with_list_with_empty_struct_;
  top_level_struct_with_list_with_empty_struct_ = NULL;
  if (GetArenaNoVirtual() == NULL && value_wrapper_simple_ != NULL) delete value_wrapper_simple_;
  value_wrapper_simple_ = NULL;
  if (GetArenaNoVirtual() == NULL && value_wrapper_with_struct_ != NULL) delete value_wrapper_with_struct_;
  value_wrapper_with_struct_ = NULL;
  if (GetArenaNoVirtual() == NULL && value_wrapper_with_list_ != NULL) delete value_wrapper_with_list_;
  value_wrapper_with_list_ = NULL;
  if (GetArenaNoVirtual() == NULL && value_wrapper_with_empty_list_ != NULL) delete value_wrapper_with_empty_list_;
  value_wrapper_with_empty_list_ = NULL;
  if (GetArenaNoVirtual() == NULL && value_wrapper_with_list_with_empty_struct_ != NULL) delete value_wrapper_with_list_with_empty_struct_;
  value_wrapper_with_list_with_empty_struct_ = NULL;
  if (GetArenaNoVirtual() == NULL && list_value_wrapper_ != NULL) delete list_value_wrapper_;
  list_value_wrapper_ = NULL;
  if (GetArenaNoVirtual() == NULL && list_value_wrapper_with_empty_list_ != NULL) delete list_value_wrapper_with_empty_list_;
  list_value_wrapper_with_empty_list_ = NULL;
  if (GetArenaNoVirtual() == NULL && list_value_wrapper_with_list_with_empty_struct_ != NULL) delete list_value_wrapper_with_list_with_empty_struct_;
  list_value_wrapper_with_list_with_empty_struct_ = NULL;
  if (GetArenaNoVirtual() == NULL && top_level_value_simple_ != NULL) delete top_level_value_simple_;
  top_level_value_simple_ = NULL;
  if (GetArenaNoVirtual() == NULL && top_level_value_with_struct_ != NULL) delete top_level_value_with_struct_;
  top_level_value_with_struct_ = NULL;
  if (GetArenaNoVirtual() == NULL && top_level_value_with_list_ != NULL) delete top_level_value_with_list_;
  top_level_value_with_list_ = NULL;
  if (GetArenaNoVirtual() == NULL && top_level_value_with_empty_list_ != NULL) delete top_level_value_with_empty_list_;
  top_level_value_with_empty_list_ = NULL;
  if (GetArenaNoVirtual() == NULL && top_level_value_with_list_with_empty_struct_ != NULL) delete top_level_value_with_list_with_empty_struct_;
  top_level_value_with_list_with_empty_struct_ = NULL;
  if (GetArenaNoVirtual() == NULL && top_level_listvalue_ != NULL) delete top_level_listvalue_;
  top_level_listvalue_ = NULL;
  if (GetArenaNoVirtual() == NULL && top_level_empty_listvalue_ != NULL) delete top_level_empty_listvalue_;
  top_level_empty_listvalue_ = NULL;
  if (GetArenaNoVirtual() == NULL && top_level_listvalue_with_empty_struct_ != NULL) delete top_level_listvalue_with_empty_struct_;
  top_level_listvalue_with_empty_struct_ = NULL;
  if (GetArenaNoVirtual() == NULL && repeated_value_ != NULL) delete repeated_value_;
  repeated_value_ = NULL;
  if (GetArenaNoVirtual() == NULL && repeated_value_nested_list_ != NULL) delete repeated_value_nested_list_;
  repeated_value_nested_list_ = NULL;
  if (GetArenaNoVirtual() == NULL && repeated_value_nested_list2_ != NULL) delete repeated_value_nested_list2_;
  repeated_value_nested_list2_ = NULL;
  if (GetArenaNoVirtual() == NULL && repeated_value_nested_list3_ != NULL) delete repeated_value_nested_list3_;
  repeated_value_nested_list3_ = NULL;
  if (GetArenaNoVirtual() == NULL && repeated_listvalue_ != NULL) delete repeated_listvalue_;
  repeated_listvalue_ = NULL;
  if (GetArenaNoVirtual() == NULL && map_of_struct_ != NULL) delete map_of_struct_;
  map_of_struct_ = NULL;
  if (GetArenaNoVirtual() == NULL && map_of_struct_value_ != NULL) delete map_of_struct_value_;
  map_of_struct_value_ = NULL;
  if (GetArenaNoVirtual() == NULL && map_of_listvalue_ != NULL) delete map_of_listvalue_;
  map_of_listvalue_ = NULL;
}

bool StructTestCases::MergePartialFromCodedStream(
    ::google::protobuf::io::CodedInputStream* input) {
#define DO_(EXPRESSION) if (!GOOGLE_PREDICT_TRUE(EXPRESSION)) goto failure
  ::google::protobuf::uint32 tag;
  // @@protoc_insertion_point(parse_start:google.protobuf.testing.StructTestCases)
  for (;;) {
    ::std::pair< ::google::protobuf::uint32, bool> p = input->ReadTagWithCutoff(16383);
    tag = p.first;
    if (!p.second) goto handle_unusual;
    switch (::google::protobuf::internal::WireFormatLite::GetTagFieldNumber(tag)) {
      // optional .google.protobuf.testing.StructWrapper empty_value = 1;
      case 1: {
        if (tag == 10) {
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessageNoVirtual(
               input, mutable_empty_value()));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(18)) goto parse_empty_value2;
        break;
      }

      // optional .google.protobuf.testing.StructWrapper empty_value2 = 2;
      case 2: {
        if (tag == 18) {
         parse_empty_value2:
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessageNoVirtual(
               input, mutable_empty_value2()));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(26)) goto parse_null_value;
        break;
      }

      // optional .google.protobuf.testing.StructWrapper null_value = 3;
      case 3: {
        if (tag == 26) {
         parse_null_value:
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessageNoVirtual(
               input, mutable_null_value()));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(34)) goto parse_simple_struct;
        break;
      }

      // optional .google.protobuf.testing.StructWrapper simple_struct = 4;
      case 4: {
        if (tag == 34) {
         parse_simple_struct:
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessageNoVirtual(
               input, mutable_simple_struct()));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(42)) goto parse_longer_struct;
        break;
      }

      // optional .google.protobuf.testing.StructWrapper longer_struct = 5;
      case 5: {
        if (tag == 42) {
         parse_longer_struct:
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessageNoVirtual(
               input, mutable_longer_struct()));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(50)) goto parse_struct_with_nested_struct;
        break;
      }

      // optional .google.protobuf.testing.StructWrapper struct_with_nested_struct = 6;
      case 6: {
        if (tag == 50) {
         parse_struct_with_nested_struct:
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessageNoVirtual(
               input, mutable_struct_with_nested_struct()));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(58)) goto parse_struct_with_nested_list;
        break;
      }

      // optional .google.protobuf.testing.StructWrapper struct_with_nested_list = 7;
      case 7: {
        if (tag == 58) {
         parse_struct_with_nested_list:
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessageNoVirtual(
               input, mutable_struct_with_nested_list()));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(66)) goto parse_struct_with_list_of_nulls;
        break;
      }

      // optional .google.protobuf.testing.StructWrapper struct_with_list_of_nulls = 8;
      case 8: {
        if (tag == 66) {
         parse_struct_with_list_of_nulls:
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessageNoVirtual(
               input, mutable_struct_with_list_of_nulls()));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(74)) goto parse_struct_with_list_of_lists;
        break;
      }

      // optional .google.protobuf.testing.StructWrapper struct_with_list_of_lists = 9;
      case 9: {
        if (tag == 74) {
         parse_struct_with_list_of_lists:
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessageNoVirtual(
               input, mutable_struct_with_list_of_lists()));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(82)) goto parse_struct_with_list_of_structs;
        break;
      }

      // optional .google.protobuf.testing.StructWrapper struct_with_list_of_structs = 10;
      case 10: {
        if (tag == 82) {
         parse_struct_with_list_of_structs:
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessageNoVirtual(
               input, mutable_struct_with_list_of_structs()));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(90)) goto parse_struct_with_empty_list;
        break;
      }

      // optional .google.protobuf.testing.StructWrapper struct_with_empty_list = 11;
      case 11: {
        if (tag == 90) {
         parse_struct_with_empty_list:
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessageNoVirtual(
               input, mutable_struct_with_empty_list()));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(98)) goto parse_struct_with_list_with_empty_struct;
        break;
      }

      // optional .google.protobuf.testing.StructWrapper struct_with_list_with_empty_struct = 12;
      case 12: {
        if (tag == 98) {
         parse_struct_with_list_with_empty_struct:
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessageNoVirtual(
               input, mutable_struct_with_list_with_empty_struct()));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(106)) goto parse_top_level_struct;
        break;
      }

      // optional .google.protobuf.Struct top_level_struct = 13;
      case 13: {
        if (tag == 106) {
         parse_top_level_struct:
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessageNoVirtual(
               input, mutable_top_level_struct()));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(114)) goto parse_top_level_struct_with_empty_list;
        break;
      }

      // optional .google.protobuf.Struct top_level_struct_with_empty_list = 14;
      case 14: {
        if (tag == 114) {
         parse_top_level_struct_with_empty_list:
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessageNoVirtual(
               input, mutable_top_level_struct_with_empty_list()));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(122)) goto parse_top_level_struct_with_list_with_empty_struct;
        break;
      }

      // optional .google.protobuf.Struct top_level_struct_with_list_with_empty_struct = 15;
      case 15: {
        if (tag == 122) {
         parse_top_level_struct_with_list_with_empty_struct:
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessageNoVirtual(
               input, mutable_top_level_struct_with_list_with_empty_struct()));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(130)) goto parse_value_wrapper_simple;
        break;
      }

      // optional .google.protobuf.testing.ValueWrapper value_wrapper_simple = 16;
      case 16: {
        if (tag == 130) {
         parse_value_wrapper_simple:
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessageNoVirtual(
               input, mutable_value_wrapper_simple()));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(138)) goto parse_value_wrapper_with_struct;
        break;
      }

      // optional .google.protobuf.testing.ValueWrapper value_wrapper_with_struct = 17;
      case 17: {
        if (tag == 138) {
         parse_value_wrapper_with_struct:
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessageNoVirtual(
               input, mutable_value_wrapper_with_struct()));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(146)) goto parse_value_wrapper_with_list;
        break;
      }

      // optional .google.protobuf.testing.ValueWrapper value_wrapper_with_list = 18;
      case 18: {
        if (tag == 146) {
         parse_value_wrapper_with_list:
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessageNoVirtual(
               input, mutable_value_wrapper_with_list()));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(154)) goto parse_value_wrapper_with_empty_list;
        break;
      }

      // optional .google.protobuf.testing.ValueWrapper value_wrapper_with_empty_list = 19;
      case 19: {
        if (tag == 154) {
         parse_value_wrapper_with_empty_list:
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessageNoVirtual(
               input, mutable_value_wrapper_with_empty_list()));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(162)) goto parse_value_wrapper_with_list_with_empty_struct;
        break;
      }

      // optional .google.protobuf.testing.ValueWrapper value_wrapper_with_list_with_empty_struct = 20;
      case 20: {
        if (tag == 162) {
         parse_value_wrapper_with_list_with_empty_struct:
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessageNoVirtual(
               input, mutable_value_wrapper_with_list_with_empty_struct()));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(170)) goto parse_list_value_wrapper;
        break;
      }

      // optional .google.protobuf.testing.ListValueWrapper list_value_wrapper = 21;
      case 21: {
        if (tag == 170) {
         parse_list_value_wrapper:
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessageNoVirtual(
               input, mutable_list_value_wrapper()));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(178)) goto parse_list_value_wrapper_with_empty_list;
        break;
      }

      // optional .google.protobuf.testing.ListValueWrapper list_value_wrapper_with_empty_list = 22;
      case 22: {
        if (tag == 178) {
         parse_list_value_wrapper_with_empty_list:
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessageNoVirtual(
               input, mutable_list_value_wrapper_with_empty_list()));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(186)) goto parse_list_value_wrapper_with_list_with_empty_struct;
        break;
      }

      // optional .google.protobuf.testing.ListValueWrapper list_value_wrapper_with_list_with_empty_struct = 23;
      case 23: {
        if (tag == 186) {
         parse_list_value_wrapper_with_list_with_empty_struct:
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessageNoVirtual(
               input, mutable_list_value_wrapper_with_list_with_empty_struct()));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(194)) goto parse_top_level_value_simple;
        break;
      }

      // optional .google.protobuf.Value top_level_value_simple = 24;
      case 24: {
        if (tag == 194) {
         parse_top_level_value_simple:
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessageNoVirtual(
               input, mutable_top_level_value_simple()));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(202)) goto parse_top_level_value_with_struct;
        break;
      }

      // optional .google.protobuf.Value top_level_value_with_struct = 25;
      case 25: {
        if (tag == 202) {
         parse_top_level_value_with_struct:
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessageNoVirtual(
               input, mutable_top_level_value_with_struct()));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(210)) goto parse_top_level_value_with_list;
        break;
      }

      // optional .google.protobuf.Value top_level_value_with_list = 26;
      case 26: {
        if (tag == 210) {
         parse_top_level_value_with_list:
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessageNoVirtual(
               input, mutable_top_level_value_with_list()));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(218)) goto parse_top_level_value_with_empty_list;
        break;
      }

      // optional .google.protobuf.Value top_level_value_with_empty_list = 27;
      case 27: {
        if (tag == 218) {
         parse_top_level_value_with_empty_list:
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessageNoVirtual(
               input, mutable_top_level_value_with_empty_list()));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(226)) goto parse_top_level_value_with_list_with_empty_struct;
        break;
      }

      // optional .google.protobuf.Value top_level_value_with_list_with_empty_struct = 28;
      case 28: {
        if (tag == 226) {
         parse_top_level_value_with_list_with_empty_struct:
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessageNoVirtual(
               input, mutable_top_level_value_with_list_with_empty_struct()));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(234)) goto parse_top_level_listvalue;
        break;
      }

      // optional .google.protobuf.ListValue top_level_listvalue = 29;
      case 29: {
        if (tag == 234) {
         parse_top_level_listvalue:
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessageNoVirtual(
               input, mutable_top_level_listvalue()));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(242)) goto parse_top_level_empty_listvalue;
        break;
      }

      // optional .google.protobuf.ListValue top_level_empty_listvalue = 30;
      case 30: {
        if (tag == 242) {
         parse_top_level_empty_listvalue:
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessageNoVirtual(
               input, mutable_top_level_empty_listvalue()));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(250)) goto parse_top_level_listvalue_with_empty_struct;
        break;
      }

      // optional .google.protobuf.ListValue top_level_listvalue_with_empty_struct = 31;
      case 31: {
        if (tag == 250) {
         parse_top_level_listvalue_with_empty_struct:
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessageNoVirtual(
               input, mutable_top_level_listvalue_with_empty_struct()));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(258)) goto parse_repeated_value;
        break;
      }

      // optional .google.protobuf.testing.RepeatedValueWrapper repeated_value = 32;
      case 32: {
        if (tag == 258) {
         parse_repeated_value:
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessageNoVirtual(
               input, mutable_repeated_value()));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(266)) goto parse_repeated_value_nested_list;
        break;
      }

      // optional .google.protobuf.testing.RepeatedValueWrapper repeated_value_nested_list = 33;
      case 33: {
        if (tag == 266) {
         parse_repeated_value_nested_list:
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessageNoVirtual(
               input, mutable_repeated_value_nested_list()));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(274)) goto parse_repeated_value_nested_list2;
        break;
      }

      // optional .google.protobuf.testing.RepeatedValueWrapper repeated_value_nested_list2 = 34;
      case 34: {
        if (tag == 274) {
         parse_repeated_value_nested_list2:
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessageNoVirtual(
               input, mutable_repeated_value_nested_list2()));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(282)) goto parse_repeated_value_nested_list3;
        break;
      }

      // optional .google.protobuf.testing.RepeatedValueWrapper repeated_value_nested_list3 = 35;
      case 35: {
        if (tag == 282) {
         parse_repeated_value_nested_list3:
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessageNoVirtual(
               input, mutable_repeated_value_nested_list3()));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(290)) goto parse_repeated_listvalue;
        break;
      }

      // optional .google.protobuf.testing.RepeatedListValueWrapper repeated_listvalue = 36;
      case 36: {
        if (tag == 290) {
         parse_repeated_listvalue:
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessageNoVirtual(
               input, mutable_repeated_listvalue()));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(298)) goto parse_map_of_struct;
        break;
      }

      // optional .google.protobuf.testing.MapOfStruct map_of_struct = 37;
      case 37: {
        if (tag == 298) {
         parse_map_of_struct:
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessageNoVirtual(
               input, mutable_map_of_struct()));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(306)) goto parse_map_of_struct_value;
        break;
      }

      // optional .google.protobuf.testing.MapOfStruct map_of_struct_value = 38;
      case 38: {
        if (tag == 306) {
         parse_map_of_struct_value:
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessageNoVirtual(
               input, mutable_map_of_struct_value()));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(314)) goto parse_map_of_listvalue;
        break;
      }

      // optional .google.protobuf.testing.MapOfStruct map_of_listvalue = 39;
      case 39: {
        if (tag == 314) {
         parse_map_of_listvalue:
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessageNoVirtual(
               input, mutable_map_of_listvalue()));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectAtEnd()) goto success;
        break;
      }

      default: {
      handle_unusual:
        if (tag == 0 ||
            ::google::protobuf::internal::WireFormatLite::GetTagWireType(tag) ==
            ::google::protobuf::internal::WireFormatLite::WIRETYPE_END_GROUP) {
          goto success;
        }
        DO_(::google::protobuf::internal::WireFormatLite::SkipField(input, tag));
        break;
      }
    }
  }
success:
  // @@protoc_insertion_point(parse_success:google.protobuf.testing.StructTestCases)
  return true;
failure:
  // @@protoc_insertion_point(parse_failure:google.protobuf.testing.StructTestCases)
  return false;
#undef DO_
}

void StructTestCases::SerializeWithCachedSizes(
    ::google::protobuf::io::CodedOutputStream* output) const {
  // @@protoc_insertion_point(serialize_start:google.protobuf.testing.StructTestCases)
  // optional .google.protobuf.testing.StructWrapper empty_value = 1;
  if (this->has_empty_value()) {
    ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
      1, *this->empty_value_, output);
  }

  // optional .google.protobuf.testing.StructWrapper empty_value2 = 2;
  if (this->has_empty_value2()) {
    ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
      2, *this->empty_value2_, output);
  }

  // optional .google.protobuf.testing.StructWrapper null_value = 3;
  if (this->has_null_value()) {
    ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
      3, *this->null_value_, output);
  }

  // optional .google.protobuf.testing.StructWrapper simple_struct = 4;
  if (this->has_simple_struct()) {
    ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
      4, *this->simple_struct_, output);
  }

  // optional .google.protobuf.testing.StructWrapper longer_struct = 5;
  if (this->has_longer_struct()) {
    ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
      5, *this->longer_struct_, output);
  }

  // optional .google.protobuf.testing.StructWrapper struct_with_nested_struct = 6;
  if (this->has_struct_with_nested_struct()) {
    ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
      6, *this->struct_with_nested_struct_, output);
  }

  // optional .google.protobuf.testing.StructWrapper struct_with_nested_list = 7;
  if (this->has_struct_with_nested_list()) {
    ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
      7, *this->struct_with_nested_list_, output);
  }

  // optional .google.protobuf.testing.StructWrapper struct_with_list_of_nulls = 8;
  if (this->has_struct_with_list_of_nulls()) {
    ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
      8, *this->struct_with_list_of_nulls_, output);
  }

  // optional .google.protobuf.testing.StructWrapper struct_with_list_of_lists = 9;
  if (this->has_struct_with_list_of_lists()) {
    ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
      9, *this->struct_with_list_of_lists_, output);
  }

  // optional .google.protobuf.testing.StructWrapper struct_with_list_of_structs = 10;
  if (this->has_struct_with_list_of_structs()) {
    ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
      10, *this->struct_with_list_of_structs_, output);
  }

  // optional .google.protobuf.testing.StructWrapper struct_with_empty_list = 11;
  if (this->has_struct_with_empty_list()) {
    ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
      11, *this->struct_with_empty_list_, output);
  }

  // optional .google.protobuf.testing.StructWrapper struct_with_list_with_empty_struct = 12;
  if (this->has_struct_with_list_with_empty_struct()) {
    ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
      12, *this->struct_with_list_with_empty_struct_, output);
  }

  // optional .google.protobuf.Struct top_level_struct = 13;
  if (this->has_top_level_struct()) {
    ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
      13, *this->top_level_struct_, output);
  }

  // optional .google.protobuf.Struct top_level_struct_with_empty_list = 14;
  if (this->has_top_level_struct_with_empty_list()) {
    ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
      14, *this->top_level_struct_with_empty_list_, output);
  }

  // optional .google.protobuf.Struct top_level_struct_with_list_with_empty_struct = 15;
  if (this->has_top_level_struct_with_list_with_empty_struct()) {
    ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
      15, *this->top_level_struct_with_list_with_empty_struct_, output);
  }

  // optional .google.protobuf.testing.ValueWrapper value_wrapper_simple = 16;
  if (this->has_value_wrapper_simple()) {
    ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
      16, *this->value_wrapper_simple_, output);
  }

  // optional .google.protobuf.testing.ValueWrapper value_wrapper_with_struct = 17;
  if (this->has_value_wrapper_with_struct()) {
    ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
      17, *this->value_wrapper_with_struct_, output);
  }

  // optional .google.protobuf.testing.ValueWrapper value_wrapper_with_list = 18;
  if (this->has_value_wrapper_with_list()) {
    ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
      18, *this->value_wrapper_with_list_, output);
  }

  // optional .google.protobuf.testing.ValueWrapper value_wrapper_with_empty_list = 19;
  if (this->has_value_wrapper_with_empty_list()) {
    ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
      19, *this->value_wrapper_with_empty_list_, output);
  }

  // optional .google.protobuf.testing.ValueWrapper value_wrapper_with_list_with_empty_struct = 20;
  if (this->has_value_wrapper_with_list_with_empty_struct()) {
    ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
      20, *this->value_wrapper_with_list_with_empty_struct_, output);
  }

  // optional .google.protobuf.testing.ListValueWrapper list_value_wrapper = 21;
  if (this->has_list_value_wrapper()) {
    ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
      21, *this->list_value_wrapper_, output);
  }

  // optional .google.protobuf.testing.ListValueWrapper list_value_wrapper_with_empty_list = 22;
  if (this->has_list_value_wrapper_with_empty_list()) {
    ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
      22, *this->list_value_wrapper_with_empty_list_, output);
  }

  // optional .google.protobuf.testing.ListValueWrapper list_value_wrapper_with_list_with_empty_struct = 23;
  if (this->has_list_value_wrapper_with_list_with_empty_struct()) {
    ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
      23, *this->list_value_wrapper_with_list_with_empty_struct_, output);
  }

  // optional .google.protobuf.Value top_level_value_simple = 24;
  if (this->has_top_level_value_simple()) {
    ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
      24, *this->top_level_value_simple_, output);
  }

  // optional .google.protobuf.Value top_level_value_with_struct = 25;
  if (this->has_top_level_value_with_struct()) {
    ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
      25, *this->top_level_value_with_struct_, output);
  }

  // optional .google.protobuf.Value top_level_value_with_list = 26;
  if (this->has_top_level_value_with_list()) {
    ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
      26, *this->top_level_value_with_list_, output);
  }

  // optional .google.protobuf.Value top_level_value_with_empty_list = 27;
  if (this->has_top_level_value_with_empty_list()) {
    ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
      27, *this->top_level_value_with_empty_list_, output);
  }

  // optional .google.protobuf.Value top_level_value_with_list_with_empty_struct = 28;
  if (this->has_top_level_value_with_list_with_empty_struct()) {
    ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
      28, *this->top_level_value_with_list_with_empty_struct_, output);
  }

  // optional .google.protobuf.ListValue top_level_listvalue = 29;
  if (this->has_top_level_listvalue()) {
    ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
      29, *this->top_level_listvalue_, output);
  }

  // optional .google.protobuf.ListValue top_level_empty_listvalue = 30;
  if (this->has_top_level_empty_listvalue()) {
    ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
      30, *this->top_level_empty_listvalue_, output);
  }

  // optional .google.protobuf.ListValue top_level_listvalue_with_empty_struct = 31;
  if (this->has_top_level_listvalue_with_empty_struct()) {
    ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
      31, *this->top_level_listvalue_with_empty_struct_, output);
  }

  // optional .google.protobuf.testing.RepeatedValueWrapper repeated_value = 32;
  if (this->has_repeated_value()) {
    ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
      32, *this->repeated_value_, output);
  }

  // optional .google.protobuf.testing.RepeatedValueWrapper repeated_value_nested_list = 33;
  if (this->has_repeated_value_nested_list()) {
    ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
      33, *this->repeated_value_nested_list_, output);
  }

  // optional .google.protobuf.testing.RepeatedValueWrapper repeated_value_nested_list2 = 34;
  if (this->has_repeated_value_nested_list2()) {
    ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
      34, *this->repeated_value_nested_list2_, output);
  }

  // optional .google.protobuf.testing.RepeatedValueWrapper repeated_value_nested_list3 = 35;
  if (this->has_repeated_value_nested_list3()) {
    ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
      35, *this->repeated_value_nested_list3_, output);
  }

  // optional .google.protobuf.testing.RepeatedListValueWrapper repeated_listvalue = 36;
  if (this->has_repeated_listvalue()) {
    ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
      36, *this->repeated_listvalue_, output);
  }

  // optional .google.protobuf.testing.MapOfStruct map_of_struct = 37;
  if (this->has_map_of_struct()) {
    ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
      37, *this->map_of_struct_, output);
  }

  // optional .google.protobuf.testing.MapOfStruct map_of_struct_value = 38;
  if (this->has_map_of_struct_value()) {
    ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
      38, *this->map_of_struct_value_, output);
  }

  // optional .google.protobuf.testing.MapOfStruct map_of_listvalue = 39;
  if (this->has_map_of_listvalue()) {
    ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
      39, *this->map_of_listvalue_, output);
  }

  // @@protoc_insertion_point(serialize_end:google.protobuf.testing.StructTestCases)
}

::google::protobuf::uint8* StructTestCases::InternalSerializeWithCachedSizesToArray(
    bool deterministic, ::google::protobuf::uint8* target) const {
  (void)deterministic; // Unused
  // @@protoc_insertion_point(serialize_to_array_start:google.protobuf.testing.StructTestCases)
  // optional .google.protobuf.testing.StructWrapper empty_value = 1;
  if (this->has_empty_value()) {
    target = ::google::protobuf::internal::WireFormatLite::
      InternalWriteMessageNoVirtualToArray(
        1, *this->empty_value_, false, target);
  }

  // optional .google.protobuf.testing.StructWrapper empty_value2 = 2;
  if (this->has_empty_value2()) {
    target = ::google::protobuf::internal::WireFormatLite::
      InternalWriteMessageNoVirtualToArray(
        2, *this->empty_value2_, false, target);
  }

  // optional .google.protobuf.testing.StructWrapper null_value = 3;
  if (this->has_null_value()) {
    target = ::google::protobuf::internal::WireFormatLite::
      InternalWriteMessageNoVirtualToArray(
        3, *this->null_value_, false, target);
  }

  // optional .google.protobuf.testing.StructWrapper simple_struct = 4;
  if (this->has_simple_struct()) {
    target = ::google::protobuf::internal::WireFormatLite::
      InternalWriteMessageNoVirtualToArray(
        4, *this->simple_struct_, false, target);
  }

  // optional .google.protobuf.testing.StructWrapper longer_struct = 5;
  if (this->has_longer_struct()) {
    target = ::google::protobuf::internal::WireFormatLite::
      InternalWriteMessageNoVirtualToArray(
        5, *this->longer_struct_, false, target);
  }

  // optional .google.protobuf.testing.StructWrapper struct_with_nested_struct = 6;
  if (this->has_struct_with_nested_struct()) {
    target = ::google::protobuf::internal::WireFormatLite::
      InternalWriteMessageNoVirtualToArray(
        6, *this->struct_with_nested_struct_, false, target);
  }

  // optional .google.protobuf.testing.StructWrapper struct_with_nested_list = 7;
  if (this->has_struct_with_nested_list()) {
    target = ::google::protobuf::internal::WireFormatLite::
      InternalWriteMessageNoVirtualToArray(
        7, *this->struct_with_nested_list_, false, target);
  }

  // optional .google.protobuf.testing.StructWrapper struct_with_list_of_nulls = 8;
  if (this->has_struct_with_list_of_nulls()) {
    target = ::google::protobuf::internal::WireFormatLite::
      InternalWriteMessageNoVirtualToArray(
        8, *this->struct_with_list_of_nulls_, false, target);
  }

  // optional .google.protobuf.testing.StructWrapper struct_with_list_of_lists = 9;
  if (this->has_struct_with_list_of_lists()) {
    target = ::google::protobuf::internal::WireFormatLite::
      InternalWriteMessageNoVirtualToArray(
        9, *this->struct_with_list_of_lists_, false, target);
  }

  // optional .google.protobuf.testing.StructWrapper struct_with_list_of_structs = 10;
  if (this->has_struct_with_list_of_structs()) {
    target = ::google::protobuf::internal::WireFormatLite::
      InternalWriteMessageNoVirtualToArray(
        10, *this->struct_with_list_of_structs_, false, target);
  }

  // optional .google.protobuf.testing.StructWrapper struct_with_empty_list = 11;
  if (this->has_struct_with_empty_list()) {
    target = ::google::protobuf::internal::WireFormatLite::
      InternalWriteMessageNoVirtualToArray(
        11, *this->struct_with_empty_list_, false, target);
  }

  // optional .google.protobuf.testing.StructWrapper struct_with_list_with_empty_struct = 12;
  if (this->has_struct_with_list_with_empty_struct()) {
    target = ::google::protobuf::internal::WireFormatLite::
      InternalWriteMessageNoVirtualToArray(
        12, *this->struct_with_list_with_empty_struct_, false, target);
  }

  // optional .google.protobuf.Struct top_level_struct = 13;
  if (this->has_top_level_struct()) {
    target = ::google::protobuf::internal::WireFormatLite::
      InternalWriteMessageNoVirtualToArray(
        13, *this->top_level_struct_, false, target);
  }

  // optional .google.protobuf.Struct top_level_struct_with_empty_list = 14;
  if (this->has_top_level_struct_with_empty_list()) {
    target = ::google::protobuf::internal::WireFormatLite::
      InternalWriteMessageNoVirtualToArray(
        14, *this->top_level_struct_with_empty_list_, false, target);
  }

  // optional .google.protobuf.Struct top_level_struct_with_list_with_empty_struct = 15;
  if (this->has_top_level_struct_with_list_with_empty_struct()) {
    target = ::google::protobuf::internal::WireFormatLite::
      InternalWriteMessageNoVirtualToArray(
        15, *this->top_level_struct_with_list_with_empty_struct_, false, target);
  }

  // optional .google.protobuf.testing.ValueWrapper value_wrapper_simple = 16;
  if (this->has_value_wrapper_simple()) {
    target = ::google::protobuf::internal::WireFormatLite::
      InternalWriteMessageNoVirtualToArray(
        16, *this->value_wrapper_simple_, false, target);
  }

  // optional .google.protobuf.testing.ValueWrapper value_wrapper_with_struct = 17;
  if (this->has_value_wrapper_with_struct()) {
    target = ::google::protobuf::internal::WireFormatLite::
      InternalWriteMessageNoVirtualToArray(
        17, *this->value_wrapper_with_struct_, false, target);
  }

  // optional .google.protobuf.testing.ValueWrapper value_wrapper_with_list = 18;
  if (this->has_value_wrapper_with_list()) {
    target = ::google::protobuf::internal::WireFormatLite::
      InternalWriteMessageNoVirtualToArray(
        18, *this->value_wrapper_with_list_, false, target);
  }

  // optional .google.protobuf.testing.ValueWrapper value_wrapper_with_empty_list = 19;
  if (this->has_value_wrapper_with_empty_list()) {
    target = ::google::protobuf::internal::WireFormatLite::
      InternalWriteMessageNoVirtualToArray(
        19, *this->value_wrapper_with_empty_list_, false, target);
  }

  // optional .google.protobuf.testing.ValueWrapper value_wrapper_with_list_with_empty_struct = 20;
  if (this->has_value_wrapper_with_list_with_empty_struct()) {
    target = ::google::protobuf::internal::WireFormatLite::
      InternalWriteMessageNoVirtualToArray(
        20, *this->value_wrapper_with_list_with_empty_struct_, false, target);
  }

  // optional .google.protobuf.testing.ListValueWrapper list_value_wrapper = 21;
  if (this->has_list_value_wrapper()) {
    target = ::google::protobuf::internal::WireFormatLite::
      InternalWriteMessageNoVirtualToArray(
        21, *this->list_value_wrapper_, false, target);
  }

  // optional .google.protobuf.testing.ListValueWrapper list_value_wrapper_with_empty_list = 22;
  if (this->has_list_value_wrapper_with_empty_list()) {
    target = ::google::protobuf::internal::WireFormatLite::
      InternalWriteMessageNoVirtualToArray(
        22, *this->list_value_wrapper_with_empty_list_, false, target);
  }

  // optional .google.protobuf.testing.ListValueWrapper list_value_wrapper_with_list_with_empty_struct = 23;
  if (this->has_list_value_wrapper_with_list_with_empty_struct()) {
    target = ::google::protobuf::internal::WireFormatLite::
      InternalWriteMessageNoVirtualToArray(
        23, *this->list_value_wrapper_with_list_with_empty_struct_, false, target);
  }

  // optional .google.protobuf.Value top_level_value_simple = 24;
  if (this->has_top_level_value_simple()) {
    target = ::google::protobuf::internal::WireFormatLite::
      InternalWriteMessageNoVirtualToArray(
        24, *this->top_level_value_simple_, false, target);
  }

  // optional .google.protobuf.Value top_level_value_with_struct = 25;
  if (this->has_top_level_value_with_struct()) {
    target = ::google::protobuf::internal::WireFormatLite::
      InternalWriteMessageNoVirtualToArray(
        25, *this->top_level_value_with_struct_, false, target);
  }

  // optional .google.protobuf.Value top_level_value_with_list = 26;
  if (this->has_top_level_value_with_list()) {
    target = ::google::protobuf::internal::WireFormatLite::
      InternalWriteMessageNoVirtualToArray(
        26, *this->top_level_value_with_list_, false, target);
  }

  // optional .google.protobuf.Value top_level_value_with_empty_list = 27;
  if (this->has_top_level_value_with_empty_list()) {
    target = ::google::protobuf::internal::WireFormatLite::
      InternalWriteMessageNoVirtualToArray(
        27, *this->top_level_value_with_empty_list_, false, target);
  }

  // optional .google.protobuf.Value top_level_value_with_list_with_empty_struct = 28;
  if (this->has_top_level_value_with_list_with_empty_struct()) {
    target = ::google::protobuf::internal::WireFormatLite::
      InternalWriteMessageNoVirtualToArray(
        28, *this->top_level_value_with_list_with_empty_struct_, false, target);
  }

  // optional .google.protobuf.ListValue top_level_listvalue = 29;
  if (this->has_top_level_listvalue()) {
    target = ::google::protobuf::internal::WireFormatLite::
      InternalWriteMessageNoVirtualToArray(
        29, *this->top_level_listvalue_, false, target);
  }

  // optional .google.protobuf.ListValue top_level_empty_listvalue = 30;
  if (this->has_top_level_empty_listvalue()) {
    target = ::google::protobuf::internal::WireFormatLite::
      InternalWriteMessageNoVirtualToArray(
        30, *this->top_level_empty_listvalue_, false, target);
  }

  // optional .google.protobuf.ListValue top_level_listvalue_with_empty_struct = 31;
  if (this->has_top_level_listvalue_with_empty_struct()) {
    target = ::google::protobuf::internal::WireFormatLite::
      InternalWriteMessageNoVirtualToArray(
        31, *this->top_level_listvalue_with_empty_struct_, false, target);
  }

  // optional .google.protobuf.testing.RepeatedValueWrapper repeated_value = 32;
  if (this->has_repeated_value()) {
    target = ::google::protobuf::internal::WireFormatLite::
      InternalWriteMessageNoVirtualToArray(
        32, *this->repeated_value_, false, target);
  }

  // optional .google.protobuf.testing.RepeatedValueWrapper repeated_value_nested_list = 33;
  if (this->has_repeated_value_nested_list()) {
    target = ::google::protobuf::internal::WireFormatLite::
      InternalWriteMessageNoVirtualToArray(
        33, *this->repeated_value_nested_list_, false, target);
  }

  // optional .google.protobuf.testing.RepeatedValueWrapper repeated_value_nested_list2 = 34;
  if (this->has_repeated_value_nested_list2()) {
    target = ::google::protobuf::internal::WireFormatLite::
      InternalWriteMessageNoVirtualToArray(
        34, *this->repeated_value_nested_list2_, false, target);
  }

  // optional .google.protobuf.testing.RepeatedValueWrapper repeated_value_nested_list3 = 35;
  if (this->has_repeated_value_nested_list3()) {
    target = ::google::protobuf::internal::WireFormatLite::
      InternalWriteMessageNoVirtualToArray(
        35, *this->repeated_value_nested_list3_, false, target);
  }

  // optional .google.protobuf.testing.RepeatedListValueWrapper repeated_listvalue = 36;
  if (this->has_repeated_listvalue()) {
    target = ::google::protobuf::internal::WireFormatLite::
      InternalWriteMessageNoVirtualToArray(
        36, *this->repeated_listvalue_, false, target);
  }

  // optional .google.protobuf.testing.MapOfStruct map_of_struct = 37;
  if (this->has_map_of_struct()) {
    target = ::google::protobuf::internal::WireFormatLite::
      InternalWriteMessageNoVirtualToArray(
        37, *this->map_of_struct_, false, target);
  }

  // optional .google.protobuf.testing.MapOfStruct map_of_struct_value = 38;
  if (this->has_map_of_struct_value()) {
    target = ::google::protobuf::internal::WireFormatLite::
      InternalWriteMessageNoVirtualToArray(
        38, *this->map_of_struct_value_, false, target);
  }

  // optional .google.protobuf.testing.MapOfStruct map_of_listvalue = 39;
  if (this->has_map_of_listvalue()) {
    target = ::google::protobuf::internal::WireFormatLite::
      InternalWriteMessageNoVirtualToArray(
        39, *this->map_of_listvalue_, false, target);
  }

  // @@protoc_insertion_point(serialize_to_array_end:google.protobuf.testing.StructTestCases)
  return target;
}

size_t StructTestCases::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:google.protobuf.testing.StructTestCases)
  size_t total_size = 0;

  // optional .google.protobuf.testing.StructWrapper empty_value = 1;
  if (this->has_empty_value()) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::MessageSizeNoVirtual(
        *this->empty_value_);
  }

  // optional .google.protobuf.testing.StructWrapper empty_value2 = 2;
  if (this->has_empty_value2()) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::MessageSizeNoVirtual(
        *this->empty_value2_);
  }

  // optional .google.protobuf.testing.StructWrapper null_value = 3;
  if (this->has_null_value()) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::MessageSizeNoVirtual(
        *this->null_value_);
  }

  // optional .google.protobuf.testing.StructWrapper simple_struct = 4;
  if (this->has_simple_struct()) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::MessageSizeNoVirtual(
        *this->simple_struct_);
  }

  // optional .google.protobuf.testing.StructWrapper longer_struct = 5;
  if (this->has_longer_struct()) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::MessageSizeNoVirtual(
        *this->longer_struct_);
  }

  // optional .google.protobuf.testing.StructWrapper struct_with_nested_struct = 6;
  if (this->has_struct_with_nested_struct()) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::MessageSizeNoVirtual(
        *this->struct_with_nested_struct_);
  }

  // optional .google.protobuf.testing.StructWrapper struct_with_nested_list = 7;
  if (this->has_struct_with_nested_list()) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::MessageSizeNoVirtual(
        *this->struct_with_nested_list_);
  }

  // optional .google.protobuf.testing.StructWrapper struct_with_list_of_nulls = 8;
  if (this->has_struct_with_list_of_nulls()) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::MessageSizeNoVirtual(
        *this->struct_with_list_of_nulls_);
  }

  // optional .google.protobuf.testing.StructWrapper struct_with_list_of_lists = 9;
  if (this->has_struct_with_list_of_lists()) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::MessageSizeNoVirtual(
        *this->struct_with_list_of_lists_);
  }

  // optional .google.protobuf.testing.StructWrapper struct_with_list_of_structs = 10;
  if (this->has_struct_with_list_of_structs()) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::MessageSizeNoVirtual(
        *this->struct_with_list_of_structs_);
  }

  // optional .google.protobuf.testing.StructWrapper struct_with_empty_list = 11;
  if (this->has_struct_with_empty_list()) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::MessageSizeNoVirtual(
        *this->struct_with_empty_list_);
  }

  // optional .google.protobuf.testing.StructWrapper struct_with_list_with_empty_struct = 12;
  if (this->has_struct_with_list_with_empty_struct()) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::MessageSizeNoVirtual(
        *this->struct_with_list_with_empty_struct_);
  }

  // optional .google.protobuf.Struct top_level_struct = 13;
  if (this->has_top_level_struct()) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::MessageSizeNoVirtual(
        *this->top_level_struct_);
  }

  // optional .google.protobuf.Struct top_level_struct_with_empty_list = 14;
  if (this->has_top_level_struct_with_empty_list()) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::MessageSizeNoVirtual(
        *this->top_level_struct_with_empty_list_);
  }

  // optional .google.protobuf.Struct top_level_struct_with_list_with_empty_struct = 15;
  if (this->has_top_level_struct_with_list_with_empty_struct()) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::MessageSizeNoVirtual(
        *this->top_level_struct_with_list_with_empty_struct_);
  }

  // optional .google.protobuf.testing.ValueWrapper value_wrapper_simple = 16;
  if (this->has_value_wrapper_simple()) {
    total_size += 2 +
      ::google::protobuf::internal::WireFormatLite::MessageSizeNoVirtual(
        *this->value_wrapper_simple_);
  }

  // optional .google.protobuf.testing.ValueWrapper value_wrapper_with_struct = 17;
  if (this->has_value_wrapper_with_struct()) {
    total_size += 2 +
      ::google::protobuf::internal::WireFormatLite::MessageSizeNoVirtual(
        *this->value_wrapper_with_struct_);
  }

  // optional .google.protobuf.testing.ValueWrapper value_wrapper_with_list = 18;
  if (this->has_value_wrapper_with_list()) {
    total_size += 2 +
      ::google::protobuf::internal::WireFormatLite::MessageSizeNoVirtual(
        *this->value_wrapper_with_list_);
  }

  // optional .google.protobuf.testing.ValueWrapper value_wrapper_with_empty_list = 19;
  if (this->has_value_wrapper_with_empty_list()) {
    total_size += 2 +
      ::google::protobuf::internal::WireFormatLite::MessageSizeNoVirtual(
        *this->value_wrapper_with_empty_list_);
  }

  // optional .google.protobuf.testing.ValueWrapper value_wrapper_with_list_with_empty_struct = 20;
  if (this->has_value_wrapper_with_list_with_empty_struct()) {
    total_size += 2 +
      ::google::protobuf::internal::WireFormatLite::MessageSizeNoVirtual(
        *this->value_wrapper_with_list_with_empty_struct_);
  }

  // optional .google.protobuf.testing.ListValueWrapper list_value_wrapper = 21;
  if (this->has_list_value_wrapper()) {
    total_size += 2 +
      ::google::protobuf::internal::WireFormatLite::MessageSizeNoVirtual(
        *this->list_value_wrapper_);
  }

  // optional .google.protobuf.testing.ListValueWrapper list_value_wrapper_with_empty_list = 22;
  if (this->has_list_value_wrapper_with_empty_list()) {
    total_size += 2 +
      ::google::protobuf::internal::WireFormatLite::MessageSizeNoVirtual(
        *this->list_value_wrapper_with_empty_list_);
  }

  // optional .google.protobuf.testing.ListValueWrapper list_value_wrapper_with_list_with_empty_struct = 23;
  if (this->has_list_value_wrapper_with_list_with_empty_struct()) {
    total_size += 2 +
      ::google::protobuf::internal::WireFormatLite::MessageSizeNoVirtual(
        *this->list_value_wrapper_with_list_with_empty_struct_);
  }

  // optional .google.protobuf.Value top_level_value_simple = 24;
  if (this->has_top_level_value_simple()) {
    total_size += 2 +
      ::google::protobuf::internal::WireFormatLite::MessageSizeNoVirtual(
        *this->top_level_value_simple_);
  }

  // optional .google.protobuf.Value top_level_value_with_struct = 25;
  if (this->has_top_level_value_with_struct()) {
    total_size += 2 +
      ::google::protobuf::internal::WireFormatLite::MessageSizeNoVirtual(
        *this->top_level_value_with_struct_);
  }

  // optional .google.protobuf.Value top_level_value_with_list = 26;
  if (this->has_top_level_value_with_list()) {
    total_size += 2 +
      ::google::protobuf::internal::WireFormatLite::MessageSizeNoVirtual(
        *this->top_level_value_with_list_);
  }

  // optional .google.protobuf.Value top_level_value_with_empty_list = 27;
  if (this->has_top_level_value_with_empty_list()) {
    total_size += 2 +
      ::google::protobuf::internal::WireFormatLite::MessageSizeNoVirtual(
        *this->top_level_value_with_empty_list_);
  }

  // optional .google.protobuf.Value top_level_value_with_list_with_empty_struct = 28;
  if (this->has_top_level_value_with_list_with_empty_struct()) {
    total_size += 2 +
      ::google::protobuf::internal::WireFormatLite::MessageSizeNoVirtual(
        *this->top_level_value_with_list_with_empty_struct_);
  }

  // optional .google.protobuf.ListValue top_level_listvalue = 29;
  if (this->has_top_level_listvalue()) {
    total_size += 2 +
      ::google::protobuf::internal::WireFormatLite::MessageSizeNoVirtual(
        *this->top_level_listvalue_);
  }

  // optional .google.protobuf.ListValue top_level_empty_listvalue = 30;
  if (this->has_top_level_empty_listvalue()) {
    total_size += 2 +
      ::google::protobuf::internal::WireFormatLite::MessageSizeNoVirtual(
        *this->top_level_empty_listvalue_);
  }

  // optional .google.protobuf.ListValue top_level_listvalue_with_empty_struct = 31;
  if (this->has_top_level_listvalue_with_empty_struct()) {
    total_size += 2 +
      ::google::protobuf::internal::WireFormatLite::MessageSizeNoVirtual(
        *this->top_level_listvalue_with_empty_struct_);
  }

  // optional .google.protobuf.testing.RepeatedValueWrapper repeated_value = 32;
  if (this->has_repeated_value()) {
    total_size += 2 +
      ::google::protobuf::internal::WireFormatLite::MessageSizeNoVirtual(
        *this->repeated_value_);
  }

  // optional .google.protobuf.testing.RepeatedValueWrapper repeated_value_nested_list = 33;
  if (this->has_repeated_value_nested_list()) {
    total_size += 2 +
      ::google::protobuf::internal::WireFormatLite::MessageSizeNoVirtual(
        *this->repeated_value_nested_list_);
  }

  // optional .google.protobuf.testing.RepeatedValueWrapper repeated_value_nested_list2 = 34;
  if (this->has_repeated_value_nested_list2()) {
    total_size += 2 +
      ::google::protobuf::internal::WireFormatLite::MessageSizeNoVirtual(
        *this->repeated_value_nested_list2_);
  }

  // optional .google.protobuf.testing.RepeatedValueWrapper repeated_value_nested_list3 = 35;
  if (this->has_repeated_value_nested_list3()) {
    total_size += 2 +
      ::google::protobuf::internal::WireFormatLite::MessageSizeNoVirtual(
        *this->repeated_value_nested_list3_);
  }

  // optional .google.protobuf.testing.RepeatedListValueWrapper repeated_listvalue = 36;
  if (this->has_repeated_listvalue()) {
    total_size += 2 +
      ::google::protobuf::internal::WireFormatLite::MessageSizeNoVirtual(
        *this->repeated_listvalue_);
  }

  // optional .google.protobuf.testing.MapOfStruct map_of_struct = 37;
  if (this->has_map_of_struct()) {
    total_size += 2 +
      ::google::protobuf::internal::WireFormatLite::MessageSizeNoVirtual(
        *this->map_of_struct_);
  }

  // optional .google.protobuf.testing.MapOfStruct map_of_struct_value = 38;
  if (this->has_map_of_struct_value()) {
    total_size += 2 +
      ::google::protobuf::internal::WireFormatLite::MessageSizeNoVirtual(
        *this->map_of_struct_value_);
  }

  // optional .google.protobuf.testing.MapOfStruct map_of_listvalue = 39;
  if (this->has_map_of_listvalue()) {
    total_size += 2 +
      ::google::protobuf::internal::WireFormatLite::MessageSizeNoVirtual(
        *this->map_of_listvalue_);
  }

  int cached_size = ::google::protobuf::internal::ToCachedSize(total_size);
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = cached_size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
  return total_size;
}

void StructTestCases::MergeFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_merge_from_start:google.protobuf.testing.StructTestCases)
  if (GOOGLE_PREDICT_FALSE(&from == this)) MergeFromFail(__LINE__);
  const StructTestCases* source =
      ::google::protobuf::internal::DynamicCastToGenerated<const StructTestCases>(
          &from);
  if (source == NULL) {
  // @@protoc_insertion_point(generalized_merge_from_cast_fail:google.protobuf.testing.StructTestCases)
    ::google::protobuf::internal::ReflectionOps::Merge(from, this);
  } else {
  // @@protoc_insertion_point(generalized_merge_from_cast_success:google.protobuf.testing.StructTestCases)
    UnsafeMergeFrom(*source);
  }
}

void StructTestCases::MergeFrom(const StructTestCases& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:google.protobuf.testing.StructTestCases)
  if (GOOGLE_PREDICT_TRUE(&from != this)) {
    UnsafeMergeFrom(from);
  } else {
    MergeFromFail(__LINE__);
  }
}

void StructTestCases::UnsafeMergeFrom(const StructTestCases& from) {
  GOOGLE_DCHECK(&from != this);
  if (from.has_empty_value()) {
    mutable_empty_value()->::google::protobuf::testing::StructWrapper::MergeFrom(from.empty_value());
  }
  if (from.has_empty_value2()) {
    mutable_empty_value2()->::google::protobuf::testing::StructWrapper::MergeFrom(from.empty_value2());
  }
  if (from.has_null_value()) {
    mutable_null_value()->::google::protobuf::testing::StructWrapper::MergeFrom(from.null_value());
  }
  if (from.has_simple_struct()) {
    mutable_simple_struct()->::google::protobuf::testing::StructWrapper::MergeFrom(from.simple_struct());
  }
  if (from.has_longer_struct()) {
    mutable_longer_struct()->::google::protobuf::testing::StructWrapper::MergeFrom(from.longer_struct());
  }
  if (from.has_struct_with_nested_struct()) {
    mutable_struct_with_nested_struct()->::google::protobuf::testing::StructWrapper::MergeFrom(from.struct_with_nested_struct());
  }
  if (from.has_struct_with_nested_list()) {
    mutable_struct_with_nested_list()->::google::protobuf::testing::StructWrapper::MergeFrom(from.struct_with_nested_list());
  }
  if (from.has_struct_with_list_of_nulls()) {
    mutable_struct_with_list_of_nulls()->::google::protobuf::testing::StructWrapper::MergeFrom(from.struct_with_list_of_nulls());
  }
  if (from.has_struct_with_list_of_lists()) {
    mutable_struct_with_list_of_lists()->::google::protobuf::testing::StructWrapper::MergeFrom(from.struct_with_list_of_lists());
  }
  if (from.has_struct_with_list_of_structs()) {
    mutable_struct_with_list_of_structs()->::google::protobuf::testing::StructWrapper::MergeFrom(from.struct_with_list_of_structs());
  }
  if (from.has_struct_with_empty_list()) {
    mutable_struct_with_empty_list()->::google::protobuf::testing::StructWrapper::MergeFrom(from.struct_with_empty_list());
  }
  if (from.has_struct_with_list_with_empty_struct()) {
    mutable_struct_with_list_with_empty_struct()->::google::protobuf::testing::StructWrapper::MergeFrom(from.struct_with_list_with_empty_struct());
  }
  if (from.has_top_level_struct()) {
    mutable_top_level_struct()->::google::protobuf::Struct::MergeFrom(from.top_level_struct());
  }
  if (from.has_top_level_struct_with_empty_list()) {
    mutable_top_level_struct_with_empty_list()->::google::protobuf::Struct::MergeFrom(from.top_level_struct_with_empty_list());
  }
  if (from.has_top_level_struct_with_list_with_empty_struct()) {
    mutable_top_level_struct_with_list_with_empty_struct()->::google::protobuf::Struct::MergeFrom(from.top_level_struct_with_list_with_empty_struct());
  }
  if (from.has_value_wrapper_simple()) {
    mutable_value_wrapper_simple()->::google::protobuf::testing::ValueWrapper::MergeFrom(from.value_wrapper_simple());
  }
  if (from.has_value_wrapper_with_struct()) {
    mutable_value_wrapper_with_struct()->::google::protobuf::testing::ValueWrapper::MergeFrom(from.value_wrapper_with_struct());
  }
  if (from.has_value_wrapper_with_list()) {
    mutable_value_wrapper_with_list()->::google::protobuf::testing::ValueWrapper::MergeFrom(from.value_wrapper_with_list());
  }
  if (from.has_value_wrapper_with_empty_list()) {
    mutable_value_wrapper_with_empty_list()->::google::protobuf::testing::ValueWrapper::MergeFrom(from.value_wrapper_with_empty_list());
  }
  if (from.has_value_wrapper_with_list_with_empty_struct()) {
    mutable_value_wrapper_with_list_with_empty_struct()->::google::protobuf::testing::ValueWrapper::MergeFrom(from.value_wrapper_with_list_with_empty_struct());
  }
  if (from.has_list_value_wrapper()) {
    mutable_list_value_wrapper()->::google::protobuf::testing::ListValueWrapper::MergeFrom(from.list_value_wrapper());
  }
  if (from.has_list_value_wrapper_with_empty_list()) {
    mutable_list_value_wrapper_with_empty_list()->::google::protobuf::testing::ListValueWrapper::MergeFrom(from.list_value_wrapper_with_empty_list());
  }
  if (from.has_list_value_wrapper_with_list_with_empty_struct()) {
    mutable_list_value_wrapper_with_list_with_empty_struct()->::google::protobuf::testing::ListValueWrapper::MergeFrom(from.list_value_wrapper_with_list_with_empty_struct());
  }
  if (from.has_top_level_value_simple()) {
    mutable_top_level_value_simple()->::google::protobuf::Value::MergeFrom(from.top_level_value_simple());
  }
  if (from.has_top_level_value_with_struct()) {
    mutable_top_level_value_with_struct()->::google::protobuf::Value::MergeFrom(from.top_level_value_with_struct());
  }
  if (from.has_top_level_value_with_list()) {
    mutable_top_level_value_with_list()->::google::protobuf::Value::MergeFrom(from.top_level_value_with_list());
  }
  if (from.has_top_level_value_with_empty_list()) {
    mutable_top_level_value_with_empty_list()->::google::protobuf::Value::MergeFrom(from.top_level_value_with_empty_list());
  }
  if (from.has_top_level_value_with_list_with_empty_struct()) {
    mutable_top_level_value_with_list_with_empty_struct()->::google::protobuf::Value::MergeFrom(from.top_level_value_with_list_with_empty_struct());
  }
  if (from.has_top_level_listvalue()) {
    mutable_top_level_listvalue()->::google::protobuf::ListValue::MergeFrom(from.top_level_listvalue());
  }
  if (from.has_top_level_empty_listvalue()) {
    mutable_top_level_empty_listvalue()->::google::protobuf::ListValue::MergeFrom(from.top_level_empty_listvalue());
  }
  if (from.has_top_level_listvalue_with_empty_struct()) {
    mutable_top_level_listvalue_with_empty_struct()->::google::protobuf::ListValue::MergeFrom(from.top_level_listvalue_with_empty_struct());
  }
  if (from.has_repeated_value()) {
    mutable_repeated_value()->::google::protobuf::testing::RepeatedValueWrapper::MergeFrom(from.repeated_value());
  }
  if (from.has_repeated_value_nested_list()) {
    mutable_repeated_value_nested_list()->::google::protobuf::testing::RepeatedValueWrapper::MergeFrom(from.repeated_value_nested_list());
  }
  if (from.has_repeated_value_nested_list2()) {
    mutable_repeated_value_nested_list2()->::google::protobuf::testing::RepeatedValueWrapper::MergeFrom(from.repeated_value_nested_list2());
  }
  if (from.has_repeated_value_nested_list3()) {
    mutable_repeated_value_nested_list3()->::google::protobuf::testing::RepeatedValueWrapper::MergeFrom(from.repeated_value_nested_list3());
  }
  if (from.has_repeated_listvalue()) {
    mutable_repeated_listvalue()->::google::protobuf::testing::RepeatedListValueWrapper::MergeFrom(from.repeated_listvalue());
  }
  if (from.has_map_of_struct()) {
    mutable_map_of_struct()->::google::protobuf::testing::MapOfStruct::MergeFrom(from.map_of_struct());
  }
  if (from.has_map_of_struct_value()) {
    mutable_map_of_struct_value()->::google::protobuf::testing::MapOfStruct::MergeFrom(from.map_of_struct_value());
  }
  if (from.has_map_of_listvalue()) {
    mutable_map_of_listvalue()->::google::protobuf::testing::MapOfStruct::MergeFrom(from.map_of_listvalue());
  }
}

void StructTestCases::CopyFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_copy_from_start:google.protobuf.testing.StructTestCases)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

void StructTestCases::CopyFrom(const StructTestCases& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:google.protobuf.testing.StructTestCases)
  if (&from == this) return;
  Clear();
  UnsafeMergeFrom(from);
}

bool StructTestCases::IsInitialized() const {

  return true;
}

void StructTestCases::Swap(StructTestCases* other) {
  if (other == this) return;
  InternalSwap(other);
}
void StructTestCases::InternalSwap(StructTestCases* other) {
  std::swap(empty_value_, other->empty_value_);
  std::swap(empty_value2_, other->empty_value2_);
  std::swap(null_value_, other->null_value_);
  std::swap(simple_struct_, other->simple_struct_);
  std::swap(longer_struct_, other->longer_struct_);
  std::swap(struct_with_nested_struct_, other->struct_with_nested_struct_);
  std::swap(struct_with_nested_list_, other->struct_with_nested_list_);
  std::swap(struct_with_list_of_nulls_, other->struct_with_list_of_nulls_);
  std::swap(struct_with_list_of_lists_, other->struct_with_list_of_lists_);
  std::swap(struct_with_list_of_structs_, other->struct_with_list_of_structs_);
  std::swap(struct_with_empty_list_, other->struct_with_empty_list_);
  std::swap(struct_with_list_with_empty_struct_, other->struct_with_list_with_empty_struct_);
  std::swap(top_level_struct_, other->top_level_struct_);
  std::swap(top_level_struct_with_empty_list_, other->top_level_struct_with_empty_list_);
  std::swap(top_level_struct_with_list_with_empty_struct_, other->top_level_struct_with_list_with_empty_struct_);
  std::swap(value_wrapper_simple_, other->value_wrapper_simple_);
  std::swap(value_wrapper_with_struct_, other->value_wrapper_with_struct_);
  std::swap(value_wrapper_with_list_, other->value_wrapper_with_list_);
  std::swap(value_wrapper_with_empty_list_, other->value_wrapper_with_empty_list_);
  std::swap(value_wrapper_with_list_with_empty_struct_, other->value_wrapper_with_list_with_empty_struct_);
  std::swap(list_value_wrapper_, other->list_value_wrapper_);
  std::swap(list_value_wrapper_with_empty_list_, other->list_value_wrapper_with_empty_list_);
  std::swap(list_value_wrapper_with_list_with_empty_struct_, other->list_value_wrapper_with_list_with_empty_struct_);
  std::swap(top_level_value_simple_, other->top_level_value_simple_);
  std::swap(top_level_value_with_struct_, other->top_level_value_with_struct_);
  std::swap(top_level_value_with_list_, other->top_level_value_with_list_);
  std::swap(top_level_value_with_empty_list_, other->top_level_value_with_empty_list_);
  std::swap(top_level_value_with_list_with_empty_struct_, other->top_level_value_with_list_with_empty_struct_);
  std::swap(top_level_listvalue_, other->top_level_listvalue_);
  std::swap(top_level_empty_listvalue_, other->top_level_empty_listvalue_);
  std::swap(top_level_listvalue_with_empty_struct_, other->top_level_listvalue_with_empty_struct_);
  std::swap(repeated_value_, other->repeated_value_);
  std::swap(repeated_value_nested_list_, other->repeated_value_nested_list_);
  std::swap(repeated_value_nested_list2_, other->repeated_value_nested_list2_);
  std::swap(repeated_value_nested_list3_, other->repeated_value_nested_list3_);
  std::swap(repeated_listvalue_, other->repeated_listvalue_);
  std::swap(map_of_struct_, other->map_of_struct_);
  std::swap(map_of_struct_value_, other->map_of_struct_value_);
  std::swap(map_of_listvalue_, other->map_of_listvalue_);
  _internal_metadata_.Swap(&other->_internal_metadata_);
  std::swap(_cached_size_, other->_cached_size_);
}

::google::protobuf::Metadata StructTestCases::GetMetadata() const {
  protobuf_AssignDescriptorsOnce();
  ::google::protobuf::Metadata metadata;
  metadata.descriptor = StructTestCases_descriptor_;
  metadata.reflection = StructTestCases_reflection_;
  return metadata;
}

#if PROTOBUF_INLINE_NOT_IN_HEADERS
// StructTestCases

// optional .google.protobuf.testing.StructWrapper empty_value = 1;
bool StructTestCases::has_empty_value() const {
  return this != internal_default_instance() && empty_value_ != NULL;
}
void StructTestCases::clear_empty_value() {
  if (GetArenaNoVirtual() == NULL && empty_value_ != NULL) delete empty_value_;
  empty_value_ = NULL;
}
const ::google::protobuf::testing::StructWrapper& StructTestCases::empty_value() const {
  // @@protoc_insertion_point(field_get:google.protobuf.testing.StructTestCases.empty_value)
  return empty_value_ != NULL ? *empty_value_
                         : *::google::protobuf::testing::StructWrapper::internal_default_instance();
}
::google::protobuf::testing::StructWrapper* StructTestCases::mutable_empty_value() {
  
  if (empty_value_ == NULL) {
    empty_value_ = new ::google::protobuf::testing::StructWrapper;
  }
  // @@protoc_insertion_point(field_mutable:google.protobuf.testing.StructTestCases.empty_value)
  return empty_value_;
}
::google::protobuf::testing::StructWrapper* StructTestCases::release_empty_value() {
  // @@protoc_insertion_point(field_release:google.protobuf.testing.StructTestCases.empty_value)
  
  ::google::protobuf::testing::StructWrapper* temp = empty_value_;
  empty_value_ = NULL;
  return temp;
}
void StructTestCases::set_allocated_empty_value(::google::protobuf::testing::StructWrapper* empty_value) {
  delete empty_value_;
  empty_value_ = empty_value;
  if (empty_value) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_set_allocated:google.protobuf.testing.StructTestCases.empty_value)
}

// optional .google.protobuf.testing.StructWrapper empty_value2 = 2;
bool StructTestCases::has_empty_value2() const {
  return this != internal_default_instance() && empty_value2_ != NULL;
}
void StructTestCases::clear_empty_value2() {
  if (GetArenaNoVirtual() == NULL && empty_value2_ != NULL) delete empty_value2_;
  empty_value2_ = NULL;
}
const ::google::protobuf::testing::StructWrapper& StructTestCases::empty_value2() const {
  // @@protoc_insertion_point(field_get:google.protobuf.testing.StructTestCases.empty_value2)
  return empty_value2_ != NULL ? *empty_value2_
                         : *::google::protobuf::testing::StructWrapper::internal_default_instance();
}
::google::protobuf::testing::StructWrapper* StructTestCases::mutable_empty_value2() {
  
  if (empty_value2_ == NULL) {
    empty_value2_ = new ::google::protobuf::testing::StructWrapper;
  }
  // @@protoc_insertion_point(field_mutable:google.protobuf.testing.StructTestCases.empty_value2)
  return empty_value2_;
}
::google::protobuf::testing::StructWrapper* StructTestCases::release_empty_value2() {
  // @@protoc_insertion_point(field_release:google.protobuf.testing.StructTestCases.empty_value2)
  
  ::google::protobuf::testing::StructWrapper* temp = empty_value2_;
  empty_value2_ = NULL;
  return temp;
}
void StructTestCases::set_allocated_empty_value2(::google::protobuf::testing::StructWrapper* empty_value2) {
  delete empty_value2_;
  empty_value2_ = empty_value2;
  if (empty_value2) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_set_allocated:google.protobuf.testing.StructTestCases.empty_value2)
}

// optional .google.protobuf.testing.StructWrapper null_value = 3;
bool StructTestCases::has_null_value() const {
  return this != internal_default_instance() && null_value_ != NULL;
}
void StructTestCases::clear_null_value() {
  if (GetArenaNoVirtual() == NULL && null_value_ != NULL) delete null_value_;
  null_value_ = NULL;
}
const ::google::protobuf::testing::StructWrapper& StructTestCases::null_value() const {
  // @@protoc_insertion_point(field_get:google.protobuf.testing.StructTestCases.null_value)
  return null_value_ != NULL ? *null_value_
                         : *::google::protobuf::testing::StructWrapper::internal_default_instance();
}
::google::protobuf::testing::StructWrapper* StructTestCases::mutable_null_value() {
  
  if (null_value_ == NULL) {
    null_value_ = new ::google::protobuf::testing::StructWrapper;
  }
  // @@protoc_insertion_point(field_mutable:google.protobuf.testing.StructTestCases.null_value)
  return null_value_;
}
::google::protobuf::testing::StructWrapper* StructTestCases::release_null_value() {
  // @@protoc_insertion_point(field_release:google.protobuf.testing.StructTestCases.null_value)
  
  ::google::protobuf::testing::StructWrapper* temp = null_value_;
  null_value_ = NULL;
  return temp;
}
void StructTestCases::set_allocated_null_value(::google::protobuf::testing::StructWrapper* null_value) {
  delete null_value_;
  null_value_ = null_value;
  if (null_value) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_set_allocated:google.protobuf.testing.StructTestCases.null_value)
}

// optional .google.protobuf.testing.StructWrapper simple_struct = 4;
bool StructTestCases::has_simple_struct() const {
  return this != internal_default_instance() && simple_struct_ != NULL;
}
void StructTestCases::clear_simple_struct() {
  if (GetArenaNoVirtual() == NULL && simple_struct_ != NULL) delete simple_struct_;
  simple_struct_ = NULL;
}
const ::google::protobuf::testing::StructWrapper& StructTestCases::simple_struct() const {
  // @@protoc_insertion_point(field_get:google.protobuf.testing.StructTestCases.simple_struct)
  return simple_struct_ != NULL ? *simple_struct_
                         : *::google::protobuf::testing::StructWrapper::internal_default_instance();
}
::google::protobuf::testing::StructWrapper* StructTestCases::mutable_simple_struct() {
  
  if (simple_struct_ == NULL) {
    simple_struct_ = new ::google::protobuf::testing::StructWrapper;
  }
  // @@protoc_insertion_point(field_mutable:google.protobuf.testing.StructTestCases.simple_struct)
  return simple_struct_;
}
::google::protobuf::testing::StructWrapper* StructTestCases::release_simple_struct() {
  // @@protoc_insertion_point(field_release:google.protobuf.testing.StructTestCases.simple_struct)
  
  ::google::protobuf::testing::StructWrapper* temp = simple_struct_;
  simple_struct_ = NULL;
  return temp;
}
void StructTestCases::set_allocated_simple_struct(::google::protobuf::testing::StructWrapper* simple_struct) {
  delete simple_struct_;
  simple_struct_ = simple_struct;
  if (simple_struct) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_set_allocated:google.protobuf.testing.StructTestCases.simple_struct)
}

// optional .google.protobuf.testing.StructWrapper longer_struct = 5;
bool StructTestCases::has_longer_struct() const {
  return this != internal_default_instance() && longer_struct_ != NULL;
}
void StructTestCases::clear_longer_struct() {
  if (GetArenaNoVirtual() == NULL && longer_struct_ != NULL) delete longer_struct_;
  longer_struct_ = NULL;
}
const ::google::protobuf::testing::StructWrapper& StructTestCases::longer_struct() const {
  // @@protoc_insertion_point(field_get:google.protobuf.testing.StructTestCases.longer_struct)
  return longer_struct_ != NULL ? *longer_struct_
                         : *::google::protobuf::testing::StructWrapper::internal_default_instance();
}
::google::protobuf::testing::StructWrapper* StructTestCases::mutable_longer_struct() {
  
  if (longer_struct_ == NULL) {
    longer_struct_ = new ::google::protobuf::testing::StructWrapper;
  }
  // @@protoc_insertion_point(field_mutable:google.protobuf.testing.StructTestCases.longer_struct)
  return longer_struct_;
}
::google::protobuf::testing::StructWrapper* StructTestCases::release_longer_struct() {
  // @@protoc_insertion_point(field_release:google.protobuf.testing.StructTestCases.longer_struct)
  
  ::google::protobuf::testing::StructWrapper* temp = longer_struct_;
  longer_struct_ = NULL;
  return temp;
}
void StructTestCases::set_allocated_longer_struct(::google::protobuf::testing::StructWrapper* longer_struct) {
  delete longer_struct_;
  longer_struct_ = longer_struct;
  if (longer_struct) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_set_allocated:google.protobuf.testing.StructTestCases.longer_struct)
}

// optional .google.protobuf.testing.StructWrapper struct_with_nested_struct = 6;
bool StructTestCases::has_struct_with_nested_struct() const {
  return this != internal_default_instance() && struct_with_nested_struct_ != NULL;
}
void StructTestCases::clear_struct_with_nested_struct() {
  if (GetArenaNoVirtual() == NULL && struct_with_nested_struct_ != NULL) delete struct_with_nested_struct_;
  struct_with_nested_struct_ = NULL;
}
const ::google::protobuf::testing::StructWrapper& StructTestCases::struct_with_nested_struct() const {
  // @@protoc_insertion_point(field_get:google.protobuf.testing.StructTestCases.struct_with_nested_struct)
  return struct_with_nested_struct_ != NULL ? *struct_with_nested_struct_
                         : *::google::protobuf::testing::StructWrapper::internal_default_instance();
}
::google::protobuf::testing::StructWrapper* StructTestCases::mutable_struct_with_nested_struct() {
  
  if (struct_with_nested_struct_ == NULL) {
    struct_with_nested_struct_ = new ::google::protobuf::testing::StructWrapper;
  }
  // @@protoc_insertion_point(field_mutable:google.protobuf.testing.StructTestCases.struct_with_nested_struct)
  return struct_with_nested_struct_;
}
::google::protobuf::testing::StructWrapper* StructTestCases::release_struct_with_nested_struct() {
  // @@protoc_insertion_point(field_release:google.protobuf.testing.StructTestCases.struct_with_nested_struct)
  
  ::google::protobuf::testing::StructWrapper* temp = struct_with_nested_struct_;
  struct_with_nested_struct_ = NULL;
  return temp;
}
void StructTestCases::set_allocated_struct_with_nested_struct(::google::protobuf::testing::StructWrapper* struct_with_nested_struct) {
  delete struct_with_nested_struct_;
  struct_with_nested_struct_ = struct_with_nested_struct;
  if (struct_with_nested_struct) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_set_allocated:google.protobuf.testing.StructTestCases.struct_with_nested_struct)
}

// optional .google.protobuf.testing.StructWrapper struct_with_nested_list = 7;
bool StructTestCases::has_struct_with_nested_list() const {
  return this != internal_default_instance() && struct_with_nested_list_ != NULL;
}
void StructTestCases::clear_struct_with_nested_list() {
  if (GetArenaNoVirtual() == NULL && struct_with_nested_list_ != NULL) delete struct_with_nested_list_;
  struct_with_nested_list_ = NULL;
}
const ::google::protobuf::testing::StructWrapper& StructTestCases::struct_with_nested_list() const {
  // @@protoc_insertion_point(field_get:google.protobuf.testing.StructTestCases.struct_with_nested_list)
  return struct_with_nested_list_ != NULL ? *struct_with_nested_list_
                         : *::google::protobuf::testing::StructWrapper::internal_default_instance();
}
::google::protobuf::testing::StructWrapper* StructTestCases::mutable_struct_with_nested_list() {
  
  if (struct_with_nested_list_ == NULL) {
    struct_with_nested_list_ = new ::google::protobuf::testing::StructWrapper;
  }
  // @@protoc_insertion_point(field_mutable:google.protobuf.testing.StructTestCases.struct_with_nested_list)
  return struct_with_nested_list_;
}
::google::protobuf::testing::StructWrapper* StructTestCases::release_struct_with_nested_list() {
  // @@protoc_insertion_point(field_release:google.protobuf.testing.StructTestCases.struct_with_nested_list)
  
  ::google::protobuf::testing::StructWrapper* temp = struct_with_nested_list_;
  struct_with_nested_list_ = NULL;
  return temp;
}
void StructTestCases::set_allocated_struct_with_nested_list(::google::protobuf::testing::StructWrapper* struct_with_nested_list) {
  delete struct_with_nested_list_;
  struct_with_nested_list_ = struct_with_nested_list;
  if (struct_with_nested_list) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_set_allocated:google.protobuf.testing.StructTestCases.struct_with_nested_list)
}

// optional .google.protobuf.testing.StructWrapper struct_with_list_of_nulls = 8;
bool StructTestCases::has_struct_with_list_of_nulls() const {
  return this != internal_default_instance() && struct_with_list_of_nulls_ != NULL;
}
void StructTestCases::clear_struct_with_list_of_nulls() {
  if (GetArenaNoVirtual() == NULL && struct_with_list_of_nulls_ != NULL) delete struct_with_list_of_nulls_;
  struct_with_list_of_nulls_ = NULL;
}
const ::google::protobuf::testing::StructWrapper& StructTestCases::struct_with_list_of_nulls() const {
  // @@protoc_insertion_point(field_get:google.protobuf.testing.StructTestCases.struct_with_list_of_nulls)
  return struct_with_list_of_nulls_ != NULL ? *struct_with_list_of_nulls_
                         : *::google::protobuf::testing::StructWrapper::internal_default_instance();
}
::google::protobuf::testing::StructWrapper* StructTestCases::mutable_struct_with_list_of_nulls() {
  
  if (struct_with_list_of_nulls_ == NULL) {
    struct_with_list_of_nulls_ = new ::google::protobuf::testing::StructWrapper;
  }
  // @@protoc_insertion_point(field_mutable:google.protobuf.testing.StructTestCases.struct_with_list_of_nulls)
  return struct_with_list_of_nulls_;
}
::google::protobuf::testing::StructWrapper* StructTestCases::release_struct_with_list_of_nulls() {
  // @@protoc_insertion_point(field_release:google.protobuf.testing.StructTestCases.struct_with_list_of_nulls)
  
  ::google::protobuf::testing::StructWrapper* temp = struct_with_list_of_nulls_;
  struct_with_list_of_nulls_ = NULL;
  return temp;
}
void StructTestCases::set_allocated_struct_with_list_of_nulls(::google::protobuf::testing::StructWrapper* struct_with_list_of_nulls) {
  delete struct_with_list_of_nulls_;
  struct_with_list_of_nulls_ = struct_with_list_of_nulls;
  if (struct_with_list_of_nulls) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_set_allocated:google.protobuf.testing.StructTestCases.struct_with_list_of_nulls)
}

// optional .google.protobuf.testing.StructWrapper struct_with_list_of_lists = 9;
bool StructTestCases::has_struct_with_list_of_lists() const {
  return this != internal_default_instance() && struct_with_list_of_lists_ != NULL;
}
void StructTestCases::clear_struct_with_list_of_lists() {
  if (GetArenaNoVirtual() == NULL && struct_with_list_of_lists_ != NULL) delete struct_with_list_of_lists_;
  struct_with_list_of_lists_ = NULL;
}
const ::google::protobuf::testing::StructWrapper& StructTestCases::struct_with_list_of_lists() const {
  // @@protoc_insertion_point(field_get:google.protobuf.testing.StructTestCases.struct_with_list_of_lists)
  return struct_with_list_of_lists_ != NULL ? *struct_with_list_of_lists_
                         : *::google::protobuf::testing::StructWrapper::internal_default_instance();
}
::google::protobuf::testing::StructWrapper* StructTestCases::mutable_struct_with_list_of_lists() {
  
  if (struct_with_list_of_lists_ == NULL) {
    struct_with_list_of_lists_ = new ::google::protobuf::testing::StructWrapper;
  }
  // @@protoc_insertion_point(field_mutable:google.protobuf.testing.StructTestCases.struct_with_list_of_lists)
  return struct_with_list_of_lists_;
}
::google::protobuf::testing::StructWrapper* StructTestCases::release_struct_with_list_of_lists() {
  // @@protoc_insertion_point(field_release:google.protobuf.testing.StructTestCases.struct_with_list_of_lists)
  
  ::google::protobuf::testing::StructWrapper* temp = struct_with_list_of_lists_;
  struct_with_list_of_lists_ = NULL;
  return temp;
}
void StructTestCases::set_allocated_struct_with_list_of_lists(::google::protobuf::testing::StructWrapper* struct_with_list_of_lists) {
  delete struct_with_list_of_lists_;
  struct_with_list_of_lists_ = struct_with_list_of_lists;
  if (struct_with_list_of_lists) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_set_allocated:google.protobuf.testing.StructTestCases.struct_with_list_of_lists)
}

// optional .google.protobuf.testing.StructWrapper struct_with_list_of_structs = 10;
bool StructTestCases::has_struct_with_list_of_structs() const {
  return this != internal_default_instance() && struct_with_list_of_structs_ != NULL;
}
void StructTestCases::clear_struct_with_list_of_structs() {
  if (GetArenaNoVirtual() == NULL && struct_with_list_of_structs_ != NULL) delete struct_with_list_of_structs_;
  struct_with_list_of_structs_ = NULL;
}
const ::google::protobuf::testing::StructWrapper& StructTestCases::struct_with_list_of_structs() const {
  // @@protoc_insertion_point(field_get:google.protobuf.testing.StructTestCases.struct_with_list_of_structs)
  return struct_with_list_of_structs_ != NULL ? *struct_with_list_of_structs_
                         : *::google::protobuf::testing::StructWrapper::internal_default_instance();
}
::google::protobuf::testing::StructWrapper* StructTestCases::mutable_struct_with_list_of_structs() {
  
  if (struct_with_list_of_structs_ == NULL) {
    struct_with_list_of_structs_ = new ::google::protobuf::testing::StructWrapper;
  }
  // @@protoc_insertion_point(field_mutable:google.protobuf.testing.StructTestCases.struct_with_list_of_structs)
  return struct_with_list_of_structs_;
}
::google::protobuf::testing::StructWrapper* StructTestCases::release_struct_with_list_of_structs() {
  // @@protoc_insertion_point(field_release:google.protobuf.testing.StructTestCases.struct_with_list_of_structs)
  
  ::google::protobuf::testing::StructWrapper* temp = struct_with_list_of_structs_;
  struct_with_list_of_structs_ = NULL;
  return temp;
}
void StructTestCases::set_allocated_struct_with_list_of_structs(::google::protobuf::testing::StructWrapper* struct_with_list_of_structs) {
  delete struct_with_list_of_structs_;
  struct_with_list_of_structs_ = struct_with_list_of_structs;
  if (struct_with_list_of_structs) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_set_allocated:google.protobuf.testing.StructTestCases.struct_with_list_of_structs)
}

// optional .google.protobuf.testing.StructWrapper struct_with_empty_list = 11;
bool StructTestCases::has_struct_with_empty_list() const {
  return this != internal_default_instance() && struct_with_empty_list_ != NULL;
}
void StructTestCases::clear_struct_with_empty_list() {
  if (GetArenaNoVirtual() == NULL && struct_with_empty_list_ != NULL) delete struct_with_empty_list_;
  struct_with_empty_list_ = NULL;
}
const ::google::protobuf::testing::StructWrapper& StructTestCases::struct_with_empty_list() const {
  // @@protoc_insertion_point(field_get:google.protobuf.testing.StructTestCases.struct_with_empty_list)
  return struct_with_empty_list_ != NULL ? *struct_with_empty_list_
                         : *::google::protobuf::testing::StructWrapper::internal_default_instance();
}
::google::protobuf::testing::StructWrapper* StructTestCases::mutable_struct_with_empty_list() {
  
  if (struct_with_empty_list_ == NULL) {
    struct_with_empty_list_ = new ::google::protobuf::testing::StructWrapper;
  }
  // @@protoc_insertion_point(field_mutable:google.protobuf.testing.StructTestCases.struct_with_empty_list)
  return struct_with_empty_list_;
}
::google::protobuf::testing::StructWrapper* StructTestCases::release_struct_with_empty_list() {
  // @@protoc_insertion_point(field_release:google.protobuf.testing.StructTestCases.struct_with_empty_list)
  
  ::google::protobuf::testing::StructWrapper* temp = struct_with_empty_list_;
  struct_with_empty_list_ = NULL;
  return temp;
}
void StructTestCases::set_allocated_struct_with_empty_list(::google::protobuf::testing::StructWrapper* struct_with_empty_list) {
  delete struct_with_empty_list_;
  struct_with_empty_list_ = struct_with_empty_list;
  if (struct_with_empty_list) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_set_allocated:google.protobuf.testing.StructTestCases.struct_with_empty_list)
}

// optional .google.protobuf.testing.StructWrapper struct_with_list_with_empty_struct = 12;
bool StructTestCases::has_struct_with_list_with_empty_struct() const {
  return this != internal_default_instance() && struct_with_list_with_empty_struct_ != NULL;
}
void StructTestCases::clear_struct_with_list_with_empty_struct() {
  if (GetArenaNoVirtual() == NULL && struct_with_list_with_empty_struct_ != NULL) delete struct_with_list_with_empty_struct_;
  struct_with_list_with_empty_struct_ = NULL;
}
const ::google::protobuf::testing::StructWrapper& StructTestCases::struct_with_list_with_empty_struct() const {
  // @@protoc_insertion_point(field_get:google.protobuf.testing.StructTestCases.struct_with_list_with_empty_struct)
  return struct_with_list_with_empty_struct_ != NULL ? *struct_with_list_with_empty_struct_
                         : *::google::protobuf::testing::StructWrapper::internal_default_instance();
}
::google::protobuf::testing::StructWrapper* StructTestCases::mutable_struct_with_list_with_empty_struct() {
  
  if (struct_with_list_with_empty_struct_ == NULL) {
    struct_with_list_with_empty_struct_ = new ::google::protobuf::testing::StructWrapper;
  }
  // @@protoc_insertion_point(field_mutable:google.protobuf.testing.StructTestCases.struct_with_list_with_empty_struct)
  return struct_with_list_with_empty_struct_;
}
::google::protobuf::testing::StructWrapper* StructTestCases::release_struct_with_list_with_empty_struct() {
  // @@protoc_insertion_point(field_release:google.protobuf.testing.StructTestCases.struct_with_list_with_empty_struct)
  
  ::google::protobuf::testing::StructWrapper* temp = struct_with_list_with_empty_struct_;
  struct_with_list_with_empty_struct_ = NULL;
  return temp;
}
void StructTestCases::set_allocated_struct_with_list_with_empty_struct(::google::protobuf::testing::StructWrapper* struct_with_list_with_empty_struct) {
  delete struct_with_list_with_empty_struct_;
  struct_with_list_with_empty_struct_ = struct_with_list_with_empty_struct;
  if (struct_with_list_with_empty_struct) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_set_allocated:google.protobuf.testing.StructTestCases.struct_with_list_with_empty_struct)
}

// optional .google.protobuf.Struct top_level_struct = 13;
bool StructTestCases::has_top_level_struct() const {
  return this != internal_default_instance() && top_level_struct_ != NULL;
}
void StructTestCases::clear_top_level_struct() {
  if (GetArenaNoVirtual() == NULL && top_level_struct_ != NULL) delete top_level_struct_;
  top_level_struct_ = NULL;
}
const ::google::protobuf::Struct& StructTestCases::top_level_struct() const {
  // @@protoc_insertion_point(field_get:google.protobuf.testing.StructTestCases.top_level_struct)
  return top_level_struct_ != NULL ? *top_level_struct_
                         : *::google::protobuf::Struct::internal_default_instance();
}
::google::protobuf::Struct* StructTestCases::mutable_top_level_struct() {
  
  if (top_level_struct_ == NULL) {
    top_level_struct_ = new ::google::protobuf::Struct;
  }
  // @@protoc_insertion_point(field_mutable:google.protobuf.testing.StructTestCases.top_level_struct)
  return top_level_struct_;
}
::google::protobuf::Struct* StructTestCases::release_top_level_struct() {
  // @@protoc_insertion_point(field_release:google.protobuf.testing.StructTestCases.top_level_struct)
  
  ::google::protobuf::Struct* temp = top_level_struct_;
  top_level_struct_ = NULL;
  return temp;
}
void StructTestCases::set_allocated_top_level_struct(::google::protobuf::Struct* top_level_struct) {
  delete top_level_struct_;
  if (top_level_struct != NULL && top_level_struct->GetArena() != NULL) {
    ::google::protobuf::Struct* new_top_level_struct = new ::google::protobuf::Struct;
    new_top_level_struct->CopyFrom(*top_level_struct);
    top_level_struct = new_top_level_struct;
  }
  top_level_struct_ = top_level_struct;
  if (top_level_struct) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_set_allocated:google.protobuf.testing.StructTestCases.top_level_struct)
}

// optional .google.protobuf.Struct top_level_struct_with_empty_list = 14;
bool StructTestCases::has_top_level_struct_with_empty_list() const {
  return this != internal_default_instance() && top_level_struct_with_empty_list_ != NULL;
}
void StructTestCases::clear_top_level_struct_with_empty_list() {
  if (GetArenaNoVirtual() == NULL && top_level_struct_with_empty_list_ != NULL) delete top_level_struct_with_empty_list_;
  top_level_struct_with_empty_list_ = NULL;
}
const ::google::protobuf::Struct& StructTestCases::top_level_struct_with_empty_list() const {
  // @@protoc_insertion_point(field_get:google.protobuf.testing.StructTestCases.top_level_struct_with_empty_list)
  return top_level_struct_with_empty_list_ != NULL ? *top_level_struct_with_empty_list_
                         : *::google::protobuf::Struct::internal_default_instance();
}
::google::protobuf::Struct* StructTestCases::mutable_top_level_struct_with_empty_list() {
  
  if (top_level_struct_with_empty_list_ == NULL) {
    top_level_struct_with_empty_list_ = new ::google::protobuf::Struct;
  }
  // @@protoc_insertion_point(field_mutable:google.protobuf.testing.StructTestCases.top_level_struct_with_empty_list)
  return top_level_struct_with_empty_list_;
}
::google::protobuf::Struct* StructTestCases::release_top_level_struct_with_empty_list() {
  // @@protoc_insertion_point(field_release:google.protobuf.testing.StructTestCases.top_level_struct_with_empty_list)
  
  ::google::protobuf::Struct* temp = top_level_struct_with_empty_list_;
  top_level_struct_with_empty_list_ = NULL;
  return temp;
}
void StructTestCases::set_allocated_top_level_struct_with_empty_list(::google::protobuf::Struct* top_level_struct_with_empty_list) {
  delete top_level_struct_with_empty_list_;
  if (top_level_struct_with_empty_list != NULL && top_level_struct_with_empty_list->GetArena() != NULL) {
    ::google::protobuf::Struct* new_top_level_struct_with_empty_list = new ::google::protobuf::Struct;
    new_top_level_struct_with_empty_list->CopyFrom(*top_level_struct_with_empty_list);
    top_level_struct_with_empty_list = new_top_level_struct_with_empty_list;
  }
  top_level_struct_with_empty_list_ = top_level_struct_with_empty_list;
  if (top_level_struct_with_empty_list) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_set_allocated:google.protobuf.testing.StructTestCases.top_level_struct_with_empty_list)
}

// optional .google.protobuf.Struct top_level_struct_with_list_with_empty_struct = 15;
bool StructTestCases::has_top_level_struct_with_list_with_empty_struct() const {
  return this != internal_default_instance() && top_level_struct_with_list_with_empty_struct_ != NULL;
}
void StructTestCases::clear_top_level_struct_with_list_with_empty_struct() {
  if (GetArenaNoVirtual() == NULL && top_level_struct_with_list_with_empty_struct_ != NULL) delete top_level_struct_with_list_with_empty_struct_;
  top_level_struct_with_list_with_empty_struct_ = NULL;
}
const ::google::protobuf::Struct& StructTestCases::top_level_struct_with_list_with_empty_struct() const {
  // @@protoc_insertion_point(field_get:google.protobuf.testing.StructTestCases.top_level_struct_with_list_with_empty_struct)
  return top_level_struct_with_list_with_empty_struct_ != NULL ? *top_level_struct_with_list_with_empty_struct_
                         : *::google::protobuf::Struct::internal_default_instance();
}
::google::protobuf::Struct* StructTestCases::mutable_top_level_struct_with_list_with_empty_struct() {
  
  if (top_level_struct_with_list_with_empty_struct_ == NULL) {
    top_level_struct_with_list_with_empty_struct_ = new ::google::protobuf::Struct;
  }
  // @@protoc_insertion_point(field_mutable:google.protobuf.testing.StructTestCases.top_level_struct_with_list_with_empty_struct)
  return top_level_struct_with_list_with_empty_struct_;
}
::google::protobuf::Struct* StructTestCases::release_top_level_struct_with_list_with_empty_struct() {
  // @@protoc_insertion_point(field_release:google.protobuf.testing.StructTestCases.top_level_struct_with_list_with_empty_struct)
  
  ::google::protobuf::Struct* temp = top_level_struct_with_list_with_empty_struct_;
  top_level_struct_with_list_with_empty_struct_ = NULL;
  return temp;
}
void StructTestCases::set_allocated_top_level_struct_with_list_with_empty_struct(::google::protobuf::Struct* top_level_struct_with_list_with_empty_struct) {
  delete top_level_struct_with_list_with_empty_struct_;
  if (top_level_struct_with_list_with_empty_struct != NULL && top_level_struct_with_list_with_empty_struct->GetArena() != NULL) {
    ::google::protobuf::Struct* new_top_level_struct_with_list_with_empty_struct = new ::google::protobuf::Struct;
    new_top_level_struct_with_list_with_empty_struct->CopyFrom(*top_level_struct_with_list_with_empty_struct);
    top_level_struct_with_list_with_empty_struct = new_top_level_struct_with_list_with_empty_struct;
  }
  top_level_struct_with_list_with_empty_struct_ = top_level_struct_with_list_with_empty_struct;
  if (top_level_struct_with_list_with_empty_struct) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_set_allocated:google.protobuf.testing.StructTestCases.top_level_struct_with_list_with_empty_struct)
}

// optional .google.protobuf.testing.ValueWrapper value_wrapper_simple = 16;
bool StructTestCases::has_value_wrapper_simple() const {
  return this != internal_default_instance() && value_wrapper_simple_ != NULL;
}
void StructTestCases::clear_value_wrapper_simple() {
  if (GetArenaNoVirtual() == NULL && value_wrapper_simple_ != NULL) delete value_wrapper_simple_;
  value_wrapper_simple_ = NULL;
}
const ::google::protobuf::testing::ValueWrapper& StructTestCases::value_wrapper_simple() const {
  // @@protoc_insertion_point(field_get:google.protobuf.testing.StructTestCases.value_wrapper_simple)
  return value_wrapper_simple_ != NULL ? *value_wrapper_simple_
                         : *::google::protobuf::testing::ValueWrapper::internal_default_instance();
}
::google::protobuf::testing::ValueWrapper* StructTestCases::mutable_value_wrapper_simple() {
  
  if (value_wrapper_simple_ == NULL) {
    value_wrapper_simple_ = new ::google::protobuf::testing::ValueWrapper;
  }
  // @@protoc_insertion_point(field_mutable:google.protobuf.testing.StructTestCases.value_wrapper_simple)
  return value_wrapper_simple_;
}
::google::protobuf::testing::ValueWrapper* StructTestCases::release_value_wrapper_simple() {
  // @@protoc_insertion_point(field_release:google.protobuf.testing.StructTestCases.value_wrapper_simple)
  
  ::google::protobuf::testing::ValueWrapper* temp = value_wrapper_simple_;
  value_wrapper_simple_ = NULL;
  return temp;
}
void StructTestCases::set_allocated_value_wrapper_simple(::google::protobuf::testing::ValueWrapper* value_wrapper_simple) {
  delete value_wrapper_simple_;
  value_wrapper_simple_ = value_wrapper_simple;
  if (value_wrapper_simple) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_set_allocated:google.protobuf.testing.StructTestCases.value_wrapper_simple)
}

// optional .google.protobuf.testing.ValueWrapper value_wrapper_with_struct = 17;
bool StructTestCases::has_value_wrapper_with_struct() const {
  return this != internal_default_instance() && value_wrapper_with_struct_ != NULL;
}
void StructTestCases::clear_value_wrapper_with_struct() {
  if (GetArenaNoVirtual() == NULL && value_wrapper_with_struct_ != NULL) delete value_wrapper_with_struct_;
  value_wrapper_with_struct_ = NULL;
}
const ::google::protobuf::testing::ValueWrapper& StructTestCases::value_wrapper_with_struct() const {
  // @@protoc_insertion_point(field_get:google.protobuf.testing.StructTestCases.value_wrapper_with_struct)
  return value_wrapper_with_struct_ != NULL ? *value_wrapper_with_struct_
                         : *::google::protobuf::testing::ValueWrapper::internal_default_instance();
}
::google::protobuf::testing::ValueWrapper* StructTestCases::mutable_value_wrapper_with_struct() {
  
  if (value_wrapper_with_struct_ == NULL) {
    value_wrapper_with_struct_ = new ::google::protobuf::testing::ValueWrapper;
  }
  // @@protoc_insertion_point(field_mutable:google.protobuf.testing.StructTestCases.value_wrapper_with_struct)
  return value_wrapper_with_struct_;
}
::google::protobuf::testing::ValueWrapper* StructTestCases::release_value_wrapper_with_struct() {
  // @@protoc_insertion_point(field_release:google.protobuf.testing.StructTestCases.value_wrapper_with_struct)
  
  ::google::protobuf::testing::ValueWrapper* temp = value_wrapper_with_struct_;
  value_wrapper_with_struct_ = NULL;
  return temp;
}
void StructTestCases::set_allocated_value_wrapper_with_struct(::google::protobuf::testing::ValueWrapper* value_wrapper_with_struct) {
  delete value_wrapper_with_struct_;
  value_wrapper_with_struct_ = value_wrapper_with_struct;
  if (value_wrapper_with_struct) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_set_allocated:google.protobuf.testing.StructTestCases.value_wrapper_with_struct)
}

// optional .google.protobuf.testing.ValueWrapper value_wrapper_with_list = 18;
bool StructTestCases::has_value_wrapper_with_list() const {
  return this != internal_default_instance() && value_wrapper_with_list_ != NULL;
}
void StructTestCases::clear_value_wrapper_with_list() {
  if (GetArenaNoVirtual() == NULL && value_wrapper_with_list_ != NULL) delete value_wrapper_with_list_;
  value_wrapper_with_list_ = NULL;
}
const ::google::protobuf::testing::ValueWrapper& StructTestCases::value_wrapper_with_list() const {
  // @@protoc_insertion_point(field_get:google.protobuf.testing.StructTestCases.value_wrapper_with_list)
  return value_wrapper_with_list_ != NULL ? *value_wrapper_with_list_
                         : *::google::protobuf::testing::ValueWrapper::internal_default_instance();
}
::google::protobuf::testing::ValueWrapper* StructTestCases::mutable_value_wrapper_with_list() {
  
  if (value_wrapper_with_list_ == NULL) {
    value_wrapper_with_list_ = new ::google::protobuf::testing::ValueWrapper;
  }
  // @@protoc_insertion_point(field_mutable:google.protobuf.testing.StructTestCases.value_wrapper_with_list)
  return value_wrapper_with_list_;
}
::google::protobuf::testing::ValueWrapper* StructTestCases::release_value_wrapper_with_list() {
  // @@protoc_insertion_point(field_release:google.protobuf.testing.StructTestCases.value_wrapper_with_list)
  
  ::google::protobuf::testing::ValueWrapper* temp = value_wrapper_with_list_;
  value_wrapper_with_list_ = NULL;
  return temp;
}
void StructTestCases::set_allocated_value_wrapper_with_list(::google::protobuf::testing::ValueWrapper* value_wrapper_with_list) {
  delete value_wrapper_with_list_;
  value_wrapper_with_list_ = value_wrapper_with_list;
  if (value_wrapper_with_list) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_set_allocated:google.protobuf.testing.StructTestCases.value_wrapper_with_list)
}

// optional .google.protobuf.testing.ValueWrapper value_wrapper_with_empty_list = 19;
bool StructTestCases::has_value_wrapper_with_empty_list() const {
  return this != internal_default_instance() && value_wrapper_with_empty_list_ != NULL;
}
void StructTestCases::clear_value_wrapper_with_empty_list() {
  if (GetArenaNoVirtual() == NULL && value_wrapper_with_empty_list_ != NULL) delete value_wrapper_with_empty_list_;
  value_wrapper_with_empty_list_ = NULL;
}
const ::google::protobuf::testing::ValueWrapper& StructTestCases::value_wrapper_with_empty_list() const {
  // @@protoc_insertion_point(field_get:google.protobuf.testing.StructTestCases.value_wrapper_with_empty_list)
  return value_wrapper_with_empty_list_ != NULL ? *value_wrapper_with_empty_list_
                         : *::google::protobuf::testing::ValueWrapper::internal_default_instance();
}
::google::protobuf::testing::ValueWrapper* StructTestCases::mutable_value_wrapper_with_empty_list() {
  
  if (value_wrapper_with_empty_list_ == NULL) {
    value_wrapper_with_empty_list_ = new ::google::protobuf::testing::ValueWrapper;
  }
  // @@protoc_insertion_point(field_mutable:google.protobuf.testing.StructTestCases.value_wrapper_with_empty_list)
  return value_wrapper_with_empty_list_;
}
::google::protobuf::testing::ValueWrapper* StructTestCases::release_value_wrapper_with_empty_list() {
  // @@protoc_insertion_point(field_release:google.protobuf.testing.StructTestCases.value_wrapper_with_empty_list)
  
  ::google::protobuf::testing::ValueWrapper* temp = value_wrapper_with_empty_list_;
  value_wrapper_with_empty_list_ = NULL;
  return temp;
}
void StructTestCases::set_allocated_value_wrapper_with_empty_list(::google::protobuf::testing::ValueWrapper* value_wrapper_with_empty_list) {
  delete value_wrapper_with_empty_list_;
  value_wrapper_with_empty_list_ = value_wrapper_with_empty_list;
  if (value_wrapper_with_empty_list) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_set_allocated:google.protobuf.testing.StructTestCases.value_wrapper_with_empty_list)
}

// optional .google.protobuf.testing.ValueWrapper value_wrapper_with_list_with_empty_struct = 20;
bool StructTestCases::has_value_wrapper_with_list_with_empty_struct() const {
  return this != internal_default_instance() && value_wrapper_with_list_with_empty_struct_ != NULL;
}
void StructTestCases::clear_value_wrapper_with_list_with_empty_struct() {
  if (GetArenaNoVirtual() == NULL && value_wrapper_with_list_with_empty_struct_ != NULL) delete value_wrapper_with_list_with_empty_struct_;
  value_wrapper_with_list_with_empty_struct_ = NULL;
}
const ::google::protobuf::testing::ValueWrapper& StructTestCases::value_wrapper_with_list_with_empty_struct() const {
  // @@protoc_insertion_point(field_get:google.protobuf.testing.StructTestCases.value_wrapper_with_list_with_empty_struct)
  return value_wrapper_with_list_with_empty_struct_ != NULL ? *value_wrapper_with_list_with_empty_struct_
                         : *::google::protobuf::testing::ValueWrapper::internal_default_instance();
}
::google::protobuf::testing::ValueWrapper* StructTestCases::mutable_value_wrapper_with_list_with_empty_struct() {
  
  if (value_wrapper_with_list_with_empty_struct_ == NULL) {
    value_wrapper_with_list_with_empty_struct_ = new ::google::protobuf::testing::ValueWrapper;
  }
  // @@protoc_insertion_point(field_mutable:google.protobuf.testing.StructTestCases.value_wrapper_with_list_with_empty_struct)
  return value_wrapper_with_list_with_empty_struct_;
}
::google::protobuf::testing::ValueWrapper* StructTestCases::release_value_wrapper_with_list_with_empty_struct() {
  // @@protoc_insertion_point(field_release:google.protobuf.testing.StructTestCases.value_wrapper_with_list_with_empty_struct)
  
  ::google::protobuf::testing::ValueWrapper* temp = value_wrapper_with_list_with_empty_struct_;
  value_wrapper_with_list_with_empty_struct_ = NULL;
  return temp;
}
void StructTestCases::set_allocated_value_wrapper_with_list_with_empty_struct(::google::protobuf::testing::ValueWrapper* value_wrapper_with_list_with_empty_struct) {
  delete value_wrapper_with_list_with_empty_struct_;
  value_wrapper_with_list_with_empty_struct_ = value_wrapper_with_list_with_empty_struct;
  if (value_wrapper_with_list_with_empty_struct) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_set_allocated:google.protobuf.testing.StructTestCases.value_wrapper_with_list_with_empty_struct)
}

// optional .google.protobuf.testing.ListValueWrapper list_value_wrapper = 21;
bool StructTestCases::has_list_value_wrapper() const {
  return this != internal_default_instance() && list_value_wrapper_ != NULL;
}
void StructTestCases::clear_list_value_wrapper() {
  if (GetArenaNoVirtual() == NULL && list_value_wrapper_ != NULL) delete list_value_wrapper_;
  list_value_wrapper_ = NULL;
}
const ::google::protobuf::testing::ListValueWrapper& StructTestCases::list_value_wrapper() const {
  // @@protoc_insertion_point(field_get:google.protobuf.testing.StructTestCases.list_value_wrapper)
  return list_value_wrapper_ != NULL ? *list_value_wrapper_
                         : *::google::protobuf::testing::ListValueWrapper::internal_default_instance();
}
::google::protobuf::testing::ListValueWrapper* StructTestCases::mutable_list_value_wrapper() {
  
  if (list_value_wrapper_ == NULL) {
    list_value_wrapper_ = new ::google::protobuf::testing::ListValueWrapper;
  }
  // @@protoc_insertion_point(field_mutable:google.protobuf.testing.StructTestCases.list_value_wrapper)
  return list_value_wrapper_;
}
::google::protobuf::testing::ListValueWrapper* StructTestCases::release_list_value_wrapper() {
  // @@protoc_insertion_point(field_release:google.protobuf.testing.StructTestCases.list_value_wrapper)
  
  ::google::protobuf::testing::ListValueWrapper* temp = list_value_wrapper_;
  list_value_wrapper_ = NULL;
  return temp;
}
void StructTestCases::set_allocated_list_value_wrapper(::google::protobuf::testing::ListValueWrapper* list_value_wrapper) {
  delete list_value_wrapper_;
  list_value_wrapper_ = list_value_wrapper;
  if (list_value_wrapper) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_set_allocated:google.protobuf.testing.StructTestCases.list_value_wrapper)
}

// optional .google.protobuf.testing.ListValueWrapper list_value_wrapper_with_empty_list = 22;
bool StructTestCases::has_list_value_wrapper_with_empty_list() const {
  return this != internal_default_instance() && list_value_wrapper_with_empty_list_ != NULL;
}
void StructTestCases::clear_list_value_wrapper_with_empty_list() {
  if (GetArenaNoVirtual() == NULL && list_value_wrapper_with_empty_list_ != NULL) delete list_value_wrapper_with_empty_list_;
  list_value_wrapper_with_empty_list_ = NULL;
}
const ::google::protobuf::testing::ListValueWrapper& StructTestCases::list_value_wrapper_with_empty_list() const {
  // @@protoc_insertion_point(field_get:google.protobuf.testing.StructTestCases.list_value_wrapper_with_empty_list)
  return list_value_wrapper_with_empty_list_ != NULL ? *list_value_wrapper_with_empty_list_
                         : *::google::protobuf::testing::ListValueWrapper::internal_default_instance();
}
::google::protobuf::testing::ListValueWrapper* StructTestCases::mutable_list_value_wrapper_with_empty_list() {
  
  if (list_value_wrapper_with_empty_list_ == NULL) {
    list_value_wrapper_with_empty_list_ = new ::google::protobuf::testing::ListValueWrapper;
  }
  // @@protoc_insertion_point(field_mutable:google.protobuf.testing.StructTestCases.list_value_wrapper_with_empty_list)
  return list_value_wrapper_with_empty_list_;
}
::google::protobuf::testing::ListValueWrapper* StructTestCases::release_list_value_wrapper_with_empty_list() {
  // @@protoc_insertion_point(field_release:google.protobuf.testing.StructTestCases.list_value_wrapper_with_empty_list)
  
  ::google::protobuf::testing::ListValueWrapper* temp = list_value_wrapper_with_empty_list_;
  list_value_wrapper_with_empty_list_ = NULL;
  return temp;
}
void StructTestCases::set_allocated_list_value_wrapper_with_empty_list(::google::protobuf::testing::ListValueWrapper* list_value_wrapper_with_empty_list) {
  delete list_value_wrapper_with_empty_list_;
  list_value_wrapper_with_empty_list_ = list_value_wrapper_with_empty_list;
  if (list_value_wrapper_with_empty_list) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_set_allocated:google.protobuf.testing.StructTestCases.list_value_wrapper_with_empty_list)
}

// optional .google.protobuf.testing.ListValueWrapper list_value_wrapper_with_list_with_empty_struct = 23;
bool StructTestCases::has_list_value_wrapper_with_list_with_empty_struct() const {
  return this != internal_default_instance() && list_value_wrapper_with_list_with_empty_struct_ != NULL;
}
void StructTestCases::clear_list_value_wrapper_with_list_with_empty_struct() {
  if (GetArenaNoVirtual() == NULL && list_value_wrapper_with_list_with_empty_struct_ != NULL) delete list_value_wrapper_with_list_with_empty_struct_;
  list_value_wrapper_with_list_with_empty_struct_ = NULL;
}
const ::google::protobuf::testing::ListValueWrapper& StructTestCases::list_value_wrapper_with_list_with_empty_struct() const {
  // @@protoc_insertion_point(field_get:google.protobuf.testing.StructTestCases.list_value_wrapper_with_list_with_empty_struct)
  return list_value_wrapper_with_list_with_empty_struct_ != NULL ? *list_value_wrapper_with_list_with_empty_struct_
                         : *::google::protobuf::testing::ListValueWrapper::internal_default_instance();
}
::google::protobuf::testing::ListValueWrapper* StructTestCases::mutable_list_value_wrapper_with_list_with_empty_struct() {
  
  if (list_value_wrapper_with_list_with_empty_struct_ == NULL) {
    list_value_wrapper_with_list_with_empty_struct_ = new ::google::protobuf::testing::ListValueWrapper;
  }
  // @@protoc_insertion_point(field_mutable:google.protobuf.testing.StructTestCases.list_value_wrapper_with_list_with_empty_struct)
  return list_value_wrapper_with_list_with_empty_struct_;
}
::google::protobuf::testing::ListValueWrapper* StructTestCases::release_list_value_wrapper_with_list_with_empty_struct() {
  // @@protoc_insertion_point(field_release:google.protobuf.testing.StructTestCases.list_value_wrapper_with_list_with_empty_struct)
  
  ::google::protobuf::testing::ListValueWrapper* temp = list_value_wrapper_with_list_with_empty_struct_;
  list_value_wrapper_with_list_with_empty_struct_ = NULL;
  return temp;
}
void StructTestCases::set_allocated_list_value_wrapper_with_list_with_empty_struct(::google::protobuf::testing::ListValueWrapper* list_value_wrapper_with_list_with_empty_struct) {
  delete list_value_wrapper_with_list_with_empty_struct_;
  list_value_wrapper_with_list_with_empty_struct_ = list_value_wrapper_with_list_with_empty_struct;
  if (list_value_wrapper_with_list_with_empty_struct) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_set_allocated:google.protobuf.testing.StructTestCases.list_value_wrapper_with_list_with_empty_struct)
}

// optional .google.protobuf.Value top_level_value_simple = 24;
bool StructTestCases::has_top_level_value_simple() const {
  return this != internal_default_instance() && top_level_value_simple_ != NULL;
}
void StructTestCases::clear_top_level_value_simple() {
  if (GetArenaNoVirtual() == NULL && top_level_value_simple_ != NULL) delete top_level_value_simple_;
  top_level_value_simple_ = NULL;
}
const ::google::protobuf::Value& StructTestCases::top_level_value_simple() const {
  // @@protoc_insertion_point(field_get:google.protobuf.testing.StructTestCases.top_level_value_simple)
  return top_level_value_simple_ != NULL ? *top_level_value_simple_
                         : *::google::protobuf::Value::internal_default_instance();
}
::google::protobuf::Value* StructTestCases::mutable_top_level_value_simple() {
  
  if (top_level_value_simple_ == NULL) {
    top_level_value_simple_ = new ::google::protobuf::Value;
  }
  // @@protoc_insertion_point(field_mutable:google.protobuf.testing.StructTestCases.top_level_value_simple)
  return top_level_value_simple_;
}
::google::protobuf::Value* StructTestCases::release_top_level_value_simple() {
  // @@protoc_insertion_point(field_release:google.protobuf.testing.StructTestCases.top_level_value_simple)
  
  ::google::protobuf::Value* temp = top_level_value_simple_;
  top_level_value_simple_ = NULL;
  return temp;
}
void StructTestCases::set_allocated_top_level_value_simple(::google::protobuf::Value* top_level_value_simple) {
  delete top_level_value_simple_;
  if (top_level_value_simple != NULL && top_level_value_simple->GetArena() != NULL) {
    ::google::protobuf::Value* new_top_level_value_simple = new ::google::protobuf::Value;
    new_top_level_value_simple->CopyFrom(*top_level_value_simple);
    top_level_value_simple = new_top_level_value_simple;
  }
  top_level_value_simple_ = top_level_value_simple;
  if (top_level_value_simple) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_set_allocated:google.protobuf.testing.StructTestCases.top_level_value_simple)
}

// optional .google.protobuf.Value top_level_value_with_struct = 25;
bool StructTestCases::has_top_level_value_with_struct() const {
  return this != internal_default_instance() && top_level_value_with_struct_ != NULL;
}
void StructTestCases::clear_top_level_value_with_struct() {
  if (GetArenaNoVirtual() == NULL && top_level_value_with_struct_ != NULL) delete top_level_value_with_struct_;
  top_level_value_with_struct_ = NULL;
}
const ::google::protobuf::Value& StructTestCases::top_level_value_with_struct() const {
  // @@protoc_insertion_point(field_get:google.protobuf.testing.StructTestCases.top_level_value_with_struct)
  return top_level_value_with_struct_ != NULL ? *top_level_value_with_struct_
                         : *::google::protobuf::Value::internal_default_instance();
}
::google::protobuf::Value* StructTestCases::mutable_top_level_value_with_struct() {
  
  if (top_level_value_with_struct_ == NULL) {
    top_level_value_with_struct_ = new ::google::protobuf::Value;
  }
  // @@protoc_insertion_point(field_mutable:google.protobuf.testing.StructTestCases.top_level_value_with_struct)
  return top_level_value_with_struct_;
}
::google::protobuf::Value* StructTestCases::release_top_level_value_with_struct() {
  // @@protoc_insertion_point(field_release:google.protobuf.testing.StructTestCases.top_level_value_with_struct)
  
  ::google::protobuf::Value* temp = top_level_value_with_struct_;
  top_level_value_with_struct_ = NULL;
  return temp;
}
void StructTestCases::set_allocated_top_level_value_with_struct(::google::protobuf::Value* top_level_value_with_struct) {
  delete top_level_value_with_struct_;
  if (top_level_value_with_struct != NULL && top_level_value_with_struct->GetArena() != NULL) {
    ::google::protobuf::Value* new_top_level_value_with_struct = new ::google::protobuf::Value;
    new_top_level_value_with_struct->CopyFrom(*top_level_value_with_struct);
    top_level_value_with_struct = new_top_level_value_with_struct;
  }
  top_level_value_with_struct_ = top_level_value_with_struct;
  if (top_level_value_with_struct) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_set_allocated:google.protobuf.testing.StructTestCases.top_level_value_with_struct)
}

// optional .google.protobuf.Value top_level_value_with_list = 26;
bool StructTestCases::has_top_level_value_with_list() const {
  return this != internal_default_instance() && top_level_value_with_list_ != NULL;
}
void StructTestCases::clear_top_level_value_with_list() {
  if (GetArenaNoVirtual() == NULL && top_level_value_with_list_ != NULL) delete top_level_value_with_list_;
  top_level_value_with_list_ = NULL;
}
const ::google::protobuf::Value& StructTestCases::top_level_value_with_list() const {
  // @@protoc_insertion_point(field_get:google.protobuf.testing.StructTestCases.top_level_value_with_list)
  return top_level_value_with_list_ != NULL ? *top_level_value_with_list_
                         : *::google::protobuf::Value::internal_default_instance();
}
::google::protobuf::Value* StructTestCases::mutable_top_level_value_with_list() {
  
  if (top_level_value_with_list_ == NULL) {
    top_level_value_with_list_ = new ::google::protobuf::Value;
  }
  // @@protoc_insertion_point(field_mutable:google.protobuf.testing.StructTestCases.top_level_value_with_list)
  return top_level_value_with_list_;
}
::google::protobuf::Value* StructTestCases::release_top_level_value_with_list() {
  // @@protoc_insertion_point(field_release:google.protobuf.testing.StructTestCases.top_level_value_with_list)
  
  ::google::protobuf::Value* temp = top_level_value_with_list_;
  top_level_value_with_list_ = NULL;
  return temp;
}
void StructTestCases::set_allocated_top_level_value_with_list(::google::protobuf::Value* top_level_value_with_list) {
  delete top_level_value_with_list_;
  if (top_level_value_with_list != NULL && top_level_value_with_list->GetArena() != NULL) {
    ::google::protobuf::Value* new_top_level_value_with_list = new ::google::protobuf::Value;
    new_top_level_value_with_list->CopyFrom(*top_level_value_with_list);
    top_level_value_with_list = new_top_level_value_with_list;
  }
  top_level_value_with_list_ = top_level_value_with_list;
  if (top_level_value_with_list) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_set_allocated:google.protobuf.testing.StructTestCases.top_level_value_with_list)
}

// optional .google.protobuf.Value top_level_value_with_empty_list = 27;
bool StructTestCases::has_top_level_value_with_empty_list() const {
  return this != internal_default_instance() && top_level_value_with_empty_list_ != NULL;
}
void StructTestCases::clear_top_level_value_with_empty_list() {
  if (GetArenaNoVirtual() == NULL && top_level_value_with_empty_list_ != NULL) delete top_level_value_with_empty_list_;
  top_level_value_with_empty_list_ = NULL;
}
const ::google::protobuf::Value& StructTestCases::top_level_value_with_empty_list() const {
  // @@protoc_insertion_point(field_get:google.protobuf.testing.StructTestCases.top_level_value_with_empty_list)
  return top_level_value_with_empty_list_ != NULL ? *top_level_value_with_empty_list_
                         : *::google::protobuf::Value::internal_default_instance();
}
::google::protobuf::Value* StructTestCases::mutable_top_level_value_with_empty_list() {
  
  if (top_level_value_with_empty_list_ == NULL) {
    top_level_value_with_empty_list_ = new ::google::protobuf::Value;
  }
  // @@protoc_insertion_point(field_mutable:google.protobuf.testing.StructTestCases.top_level_value_with_empty_list)
  return top_level_value_with_empty_list_;
}
::google::protobuf::Value* StructTestCases::release_top_level_value_with_empty_list() {
  // @@protoc_insertion_point(field_release:google.protobuf.testing.StructTestCases.top_level_value_with_empty_list)
  
  ::google::protobuf::Value* temp = top_level_value_with_empty_list_;
  top_level_value_with_empty_list_ = NULL;
  return temp;
}
void StructTestCases::set_allocated_top_level_value_with_empty_list(::google::protobuf::Value* top_level_value_with_empty_list) {
  delete top_level_value_with_empty_list_;
  if (top_level_value_with_empty_list != NULL && top_level_value_with_empty_list->GetArena() != NULL) {
    ::google::protobuf::Value* new_top_level_value_with_empty_list = new ::google::protobuf::Value;
    new_top_level_value_with_empty_list->CopyFrom(*top_level_value_with_empty_list);
    top_level_value_with_empty_list = new_top_level_value_with_empty_list;
  }
  top_level_value_with_empty_list_ = top_level_value_with_empty_list;
  if (top_level_value_with_empty_list) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_set_allocated:google.protobuf.testing.StructTestCases.top_level_value_with_empty_list)
}

// optional .google.protobuf.Value top_level_value_with_list_with_empty_struct = 28;
bool StructTestCases::has_top_level_value_with_list_with_empty_struct() const {
  return this != internal_default_instance() && top_level_value_with_list_with_empty_struct_ != NULL;
}
void StructTestCases::clear_top_level_value_with_list_with_empty_struct() {
  if (GetArenaNoVirtual() == NULL && top_level_value_with_list_with_empty_struct_ != NULL) delete top_level_value_with_list_with_empty_struct_;
  top_level_value_with_list_with_empty_struct_ = NULL;
}
const ::google::protobuf::Value& StructTestCases::top_level_value_with_list_with_empty_struct() const {
  // @@protoc_insertion_point(field_get:google.protobuf.testing.StructTestCases.top_level_value_with_list_with_empty_struct)
  return top_level_value_with_list_with_empty_struct_ != NULL ? *top_level_value_with_list_with_empty_struct_
                         : *::google::protobuf::Value::internal_default_instance();
}
::google::protobuf::Value* StructTestCases::mutable_top_level_value_with_list_with_empty_struct() {
  
  if (top_level_value_with_list_with_empty_struct_ == NULL) {
    top_level_value_with_list_with_empty_struct_ = new ::google::protobuf::Value;
  }
  // @@protoc_insertion_point(field_mutable:google.protobuf.testing.StructTestCases.top_level_value_with_list_with_empty_struct)
  return top_level_value_with_list_with_empty_struct_;
}
::google::protobuf::Value* StructTestCases::release_top_level_value_with_list_with_empty_struct() {
  // @@protoc_insertion_point(field_release:google.protobuf.testing.StructTestCases.top_level_value_with_list_with_empty_struct)
  
  ::google::protobuf::Value* temp = top_level_value_with_list_with_empty_struct_;
  top_level_value_with_list_with_empty_struct_ = NULL;
  return temp;
}
void StructTestCases::set_allocated_top_level_value_with_list_with_empty_struct(::google::protobuf::Value* top_level_value_with_list_with_empty_struct) {
  delete top_level_value_with_list_with_empty_struct_;
  if (top_level_value_with_list_with_empty_struct != NULL && top_level_value_with_list_with_empty_struct->GetArena() != NULL) {
    ::google::protobuf::Value* new_top_level_value_with_list_with_empty_struct = new ::google::protobuf::Value;
    new_top_level_value_with_list_with_empty_struct->CopyFrom(*top_level_value_with_list_with_empty_struct);
    top_level_value_with_list_with_empty_struct = new_top_level_value_with_list_with_empty_struct;
  }
  top_level_value_with_list_with_empty_struct_ = top_level_value_with_list_with_empty_struct;
  if (top_level_value_with_list_with_empty_struct) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_set_allocated:google.protobuf.testing.StructTestCases.top_level_value_with_list_with_empty_struct)
}

// optional .google.protobuf.ListValue top_level_listvalue = 29;
bool StructTestCases::has_top_level_listvalue() const {
  return this != internal_default_instance() && top_level_listvalue_ != NULL;
}
void StructTestCases::clear_top_level_listvalue() {
  if (GetArenaNoVirtual() == NULL && top_level_listvalue_ != NULL) delete top_level_listvalue_;
  top_level_listvalue_ = NULL;
}
const ::google::protobuf::ListValue& StructTestCases::top_level_listvalue() const {
  // @@protoc_insertion_point(field_get:google.protobuf.testing.StructTestCases.top_level_listvalue)
  return top_level_listvalue_ != NULL ? *top_level_listvalue_
                         : *::google::protobuf::ListValue::internal_default_instance();
}
::google::protobuf::ListValue* StructTestCases::mutable_top_level_listvalue() {
  
  if (top_level_listvalue_ == NULL) {
    top_level_listvalue_ = new ::google::protobuf::ListValue;
  }
  // @@protoc_insertion_point(field_mutable:google.protobuf.testing.StructTestCases.top_level_listvalue)
  return top_level_listvalue_;
}
::google::protobuf::ListValue* StructTestCases::release_top_level_listvalue() {
  // @@protoc_insertion_point(field_release:google.protobuf.testing.StructTestCases.top_level_listvalue)
  
  ::google::protobuf::ListValue* temp = top_level_listvalue_;
  top_level_listvalue_ = NULL;
  return temp;
}
void StructTestCases::set_allocated_top_level_listvalue(::google::protobuf::ListValue* top_level_listvalue) {
  delete top_level_listvalue_;
  if (top_level_listvalue != NULL && top_level_listvalue->GetArena() != NULL) {
    ::google::protobuf::ListValue* new_top_level_listvalue = new ::google::protobuf::ListValue;
    new_top_level_listvalue->CopyFrom(*top_level_listvalue);
    top_level_listvalue = new_top_level_listvalue;
  }
  top_level_listvalue_ = top_level_listvalue;
  if (top_level_listvalue) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_set_allocated:google.protobuf.testing.StructTestCases.top_level_listvalue)
}

// optional .google.protobuf.ListValue top_level_empty_listvalue = 30;
bool StructTestCases::has_top_level_empty_listvalue() const {
  return this != internal_default_instance() && top_level_empty_listvalue_ != NULL;
}
void StructTestCases::clear_top_level_empty_listvalue() {
  if (GetArenaNoVirtual() == NULL && top_level_empty_listvalue_ != NULL) delete top_level_empty_listvalue_;
  top_level_empty_listvalue_ = NULL;
}
const ::google::protobuf::ListValue& StructTestCases::top_level_empty_listvalue() const {
  // @@protoc_insertion_point(field_get:google.protobuf.testing.StructTestCases.top_level_empty_listvalue)
  return top_level_empty_listvalue_ != NULL ? *top_level_empty_listvalue_
                         : *::google::protobuf::ListValue::internal_default_instance();
}
::google::protobuf::ListValue* StructTestCases::mutable_top_level_empty_listvalue() {
  
  if (top_level_empty_listvalue_ == NULL) {
    top_level_empty_listvalue_ = new ::google::protobuf::ListValue;
  }
  // @@protoc_insertion_point(field_mutable:google.protobuf.testing.StructTestCases.top_level_empty_listvalue)
  return top_level_empty_listvalue_;
}
::google::protobuf::ListValue* StructTestCases::release_top_level_empty_listvalue() {
  // @@protoc_insertion_point(field_release:google.protobuf.testing.StructTestCases.top_level_empty_listvalue)
  
  ::google::protobuf::ListValue* temp = top_level_empty_listvalue_;
  top_level_empty_listvalue_ = NULL;
  return temp;
}
void StructTestCases::set_allocated_top_level_empty_listvalue(::google::protobuf::ListValue* top_level_empty_listvalue) {
  delete top_level_empty_listvalue_;
  if (top_level_empty_listvalue != NULL && top_level_empty_listvalue->GetArena() != NULL) {
    ::google::protobuf::ListValue* new_top_level_empty_listvalue = new ::google::protobuf::ListValue;
    new_top_level_empty_listvalue->CopyFrom(*top_level_empty_listvalue);
    top_level_empty_listvalue = new_top_level_empty_listvalue;
  }
  top_level_empty_listvalue_ = top_level_empty_listvalue;
  if (top_level_empty_listvalue) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_set_allocated:google.protobuf.testing.StructTestCases.top_level_empty_listvalue)
}

// optional .google.protobuf.ListValue top_level_listvalue_with_empty_struct = 31;
bool StructTestCases::has_top_level_listvalue_with_empty_struct() const {
  return this != internal_default_instance() && top_level_listvalue_with_empty_struct_ != NULL;
}
void StructTestCases::clear_top_level_listvalue_with_empty_struct() {
  if (GetArenaNoVirtual() == NULL && top_level_listvalue_with_empty_struct_ != NULL) delete top_level_listvalue_with_empty_struct_;
  top_level_listvalue_with_empty_struct_ = NULL;
}
const ::google::protobuf::ListValue& StructTestCases::top_level_listvalue_with_empty_struct() const {
  // @@protoc_insertion_point(field_get:google.protobuf.testing.StructTestCases.top_level_listvalue_with_empty_struct)
  return top_level_listvalue_with_empty_struct_ != NULL ? *top_level_listvalue_with_empty_struct_
                         : *::google::protobuf::ListValue::internal_default_instance();
}
::google::protobuf::ListValue* StructTestCases::mutable_top_level_listvalue_with_empty_struct() {
  
  if (top_level_listvalue_with_empty_struct_ == NULL) {
    top_level_listvalue_with_empty_struct_ = new ::google::protobuf::ListValue;
  }
  // @@protoc_insertion_point(field_mutable:google.protobuf.testing.StructTestCases.top_level_listvalue_with_empty_struct)
  return top_level_listvalue_with_empty_struct_;
}
::google::protobuf::ListValue* StructTestCases::release_top_level_listvalue_with_empty_struct() {
  // @@protoc_insertion_point(field_release:google.protobuf.testing.StructTestCases.top_level_listvalue_with_empty_struct)
  
  ::google::protobuf::ListValue* temp = top_level_listvalue_with_empty_struct_;
  top_level_listvalue_with_empty_struct_ = NULL;
  return temp;
}
void StructTestCases::set_allocated_top_level_listvalue_with_empty_struct(::google::protobuf::ListValue* top_level_listvalue_with_empty_struct) {
  delete top_level_listvalue_with_empty_struct_;
  if (top_level_listvalue_with_empty_struct != NULL && top_level_listvalue_with_empty_struct->GetArena() != NULL) {
    ::google::protobuf::ListValue* new_top_level_listvalue_with_empty_struct = new ::google::protobuf::ListValue;
    new_top_level_listvalue_with_empty_struct->CopyFrom(*top_level_listvalue_with_empty_struct);
    top_level_listvalue_with_empty_struct = new_top_level_listvalue_with_empty_struct;
  }
  top_level_listvalue_with_empty_struct_ = top_level_listvalue_with_empty_struct;
  if (top_level_listvalue_with_empty_struct) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_set_allocated:google.protobuf.testing.StructTestCases.top_level_listvalue_with_empty_struct)
}

// optional .google.protobuf.testing.RepeatedValueWrapper repeated_value = 32;
bool StructTestCases::has_repeated_value() const {
  return this != internal_default_instance() && repeated_value_ != NULL;
}
void StructTestCases::clear_repeated_value() {
  if (GetArenaNoVirtual() == NULL && repeated_value_ != NULL) delete repeated_value_;
  repeated_value_ = NULL;
}
const ::google::protobuf::testing::RepeatedValueWrapper& StructTestCases::repeated_value() const {
  // @@protoc_insertion_point(field_get:google.protobuf.testing.StructTestCases.repeated_value)
  return repeated_value_ != NULL ? *repeated_value_
                         : *::google::protobuf::testing::RepeatedValueWrapper::internal_default_instance();
}
::google::protobuf::testing::RepeatedValueWrapper* StructTestCases::mutable_repeated_value() {
  
  if (repeated_value_ == NULL) {
    repeated_value_ = new ::google::protobuf::testing::RepeatedValueWrapper;
  }
  // @@protoc_insertion_point(field_mutable:google.protobuf.testing.StructTestCases.repeated_value)
  return repeated_value_;
}
::google::protobuf::testing::RepeatedValueWrapper* StructTestCases::release_repeated_value() {
  // @@protoc_insertion_point(field_release:google.protobuf.testing.StructTestCases.repeated_value)
  
  ::google::protobuf::testing::RepeatedValueWrapper* temp = repeated_value_;
  repeated_value_ = NULL;
  return temp;
}
void StructTestCases::set_allocated_repeated_value(::google::protobuf::testing::RepeatedValueWrapper* repeated_value) {
  delete repeated_value_;
  repeated_value_ = repeated_value;
  if (repeated_value) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_set_allocated:google.protobuf.testing.StructTestCases.repeated_value)
}

// optional .google.protobuf.testing.RepeatedValueWrapper repeated_value_nested_list = 33;
bool StructTestCases::has_repeated_value_nested_list() const {
  return this != internal_default_instance() && repeated_value_nested_list_ != NULL;
}
void StructTestCases::clear_repeated_value_nested_list() {
  if (GetArenaNoVirtual() == NULL && repeated_value_nested_list_ != NULL) delete repeated_value_nested_list_;
  repeated_value_nested_list_ = NULL;
}
const ::google::protobuf::testing::RepeatedValueWrapper& StructTestCases::repeated_value_nested_list() const {
  // @@protoc_insertion_point(field_get:google.protobuf.testing.StructTestCases.repeated_value_nested_list)
  return repeated_value_nested_list_ != NULL ? *repeated_value_nested_list_
                         : *::google::protobuf::testing::RepeatedValueWrapper::internal_default_instance();
}
::google::protobuf::testing::RepeatedValueWrapper* StructTestCases::mutable_repeated_value_nested_list() {
  
  if (repeated_value_nested_list_ == NULL) {
    repeated_value_nested_list_ = new ::google::protobuf::testing::RepeatedValueWrapper;
  }
  // @@protoc_insertion_point(field_mutable:google.protobuf.testing.StructTestCases.repeated_value_nested_list)
  return repeated_value_nested_list_;
}
::google::protobuf::testing::RepeatedValueWrapper* StructTestCases::release_repeated_value_nested_list() {
  // @@protoc_insertion_point(field_release:google.protobuf.testing.StructTestCases.repeated_value_nested_list)
  
  ::google::protobuf::testing::RepeatedValueWrapper* temp = repeated_value_nested_list_;
  repeated_value_nested_list_ = NULL;
  return temp;
}
void StructTestCases::set_allocated_repeated_value_nested_list(::google::protobuf::testing::RepeatedValueWrapper* repeated_value_nested_list) {
  delete repeated_value_nested_list_;
  repeated_value_nested_list_ = repeated_value_nested_list;
  if (repeated_value_nested_list) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_set_allocated:google.protobuf.testing.StructTestCases.repeated_value_nested_list)
}

// optional .google.protobuf.testing.RepeatedValueWrapper repeated_value_nested_list2 = 34;
bool StructTestCases::has_repeated_value_nested_list2() const {
  return this != internal_default_instance() && repeated_value_nested_list2_ != NULL;
}
void StructTestCases::clear_repeated_value_nested_list2() {
  if (GetArenaNoVirtual() == NULL && repeated_value_nested_list2_ != NULL) delete repeated_value_nested_list2_;
  repeated_value_nested_list2_ = NULL;
}
const ::google::protobuf::testing::RepeatedValueWrapper& StructTestCases::repeated_value_nested_list2() const {
  // @@protoc_insertion_point(field_get:google.protobuf.testing.StructTestCases.repeated_value_nested_list2)
  return repeated_value_nested_list2_ != NULL ? *repeated_value_nested_list2_
                         : *::google::protobuf::testing::RepeatedValueWrapper::internal_default_instance();
}
::google::protobuf::testing::RepeatedValueWrapper* StructTestCases::mutable_repeated_value_nested_list2() {
  
  if (repeated_value_nested_list2_ == NULL) {
    repeated_value_nested_list2_ = new ::google::protobuf::testing::RepeatedValueWrapper;
  }
  // @@protoc_insertion_point(field_mutable:google.protobuf.testing.StructTestCases.repeated_value_nested_list2)
  return repeated_value_nested_list2_;
}
::google::protobuf::testing::RepeatedValueWrapper* StructTestCases::release_repeated_value_nested_list2() {
  // @@protoc_insertion_point(field_release:google.protobuf.testing.StructTestCases.repeated_value_nested_list2)
  
  ::google::protobuf::testing::RepeatedValueWrapper* temp = repeated_value_nested_list2_;
  repeated_value_nested_list2_ = NULL;
  return temp;
}
void StructTestCases::set_allocated_repeated_value_nested_list2(::google::protobuf::testing::RepeatedValueWrapper* repeated_value_nested_list2) {
  delete repeated_value_nested_list2_;
  repeated_value_nested_list2_ = repeated_value_nested_list2;
  if (repeated_value_nested_list2) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_set_allocated:google.protobuf.testing.StructTestCases.repeated_value_nested_list2)
}

// optional .google.protobuf.testing.RepeatedValueWrapper repeated_value_nested_list3 = 35;
bool StructTestCases::has_repeated_value_nested_list3() const {
  return this != internal_default_instance() && repeated_value_nested_list3_ != NULL;
}
void StructTestCases::clear_repeated_value_nested_list3() {
  if (GetArenaNoVirtual() == NULL && repeated_value_nested_list3_ != NULL) delete repeated_value_nested_list3_;
  repeated_value_nested_list3_ = NULL;
}
const ::google::protobuf::testing::RepeatedValueWrapper& StructTestCases::repeated_value_nested_list3() const {
  // @@protoc_insertion_point(field_get:google.protobuf.testing.StructTestCases.repeated_value_nested_list3)
  return repeated_value_nested_list3_ != NULL ? *repeated_value_nested_list3_
                         : *::google::protobuf::testing::RepeatedValueWrapper::internal_default_instance();
}
::google::protobuf::testing::RepeatedValueWrapper* StructTestCases::mutable_repeated_value_nested_list3() {
  
  if (repeated_value_nested_list3_ == NULL) {
    repeated_value_nested_list3_ = new ::google::protobuf::testing::RepeatedValueWrapper;
  }
  // @@protoc_insertion_point(field_mutable:google.protobuf.testing.StructTestCases.repeated_value_nested_list3)
  return repeated_value_nested_list3_;
}
::google::protobuf::testing::RepeatedValueWrapper* StructTestCases::release_repeated_value_nested_list3() {
  // @@protoc_insertion_point(field_release:google.protobuf.testing.StructTestCases.repeated_value_nested_list3)
  
  ::google::protobuf::testing::RepeatedValueWrapper* temp = repeated_value_nested_list3_;
  repeated_value_nested_list3_ = NULL;
  return temp;
}
void StructTestCases::set_allocated_repeated_value_nested_list3(::google::protobuf::testing::RepeatedValueWrapper* repeated_value_nested_list3) {
  delete repeated_value_nested_list3_;
  repeated_value_nested_list3_ = repeated_value_nested_list3;
  if (repeated_value_nested_list3) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_set_allocated:google.protobuf.testing.StructTestCases.repeated_value_nested_list3)
}

// optional .google.protobuf.testing.RepeatedListValueWrapper repeated_listvalue = 36;
bool StructTestCases::has_repeated_listvalue() const {
  return this != internal_default_instance() && repeated_listvalue_ != NULL;
}
void StructTestCases::clear_repeated_listvalue() {
  if (GetArenaNoVirtual() == NULL && repeated_listvalue_ != NULL) delete repeated_listvalue_;
  repeated_listvalue_ = NULL;
}
const ::google::protobuf::testing::RepeatedListValueWrapper& StructTestCases::repeated_listvalue() const {
  // @@protoc_insertion_point(field_get:google.protobuf.testing.StructTestCases.repeated_listvalue)
  return repeated_listvalue_ != NULL ? *repeated_listvalue_
                         : *::google::protobuf::testing::RepeatedListValueWrapper::internal_default_instance();
}
::google::protobuf::testing::RepeatedListValueWrapper* StructTestCases::mutable_repeated_listvalue() {
  
  if (repeated_listvalue_ == NULL) {
    repeated_listvalue_ = new ::google::protobuf::testing::RepeatedListValueWrapper;
  }
  // @@protoc_insertion_point(field_mutable:google.protobuf.testing.StructTestCases.repeated_listvalue)
  return repeated_listvalue_;
}
::google::protobuf::testing::RepeatedListValueWrapper* StructTestCases::release_repeated_listvalue() {
  // @@protoc_insertion_point(field_release:google.protobuf.testing.StructTestCases.repeated_listvalue)
  
  ::google::protobuf::testing::RepeatedListValueWrapper* temp = repeated_listvalue_;
  repeated_listvalue_ = NULL;
  return temp;
}
void StructTestCases::set_allocated_repeated_listvalue(::google::protobuf::testing::RepeatedListValueWrapper* repeated_listvalue) {
  delete repeated_listvalue_;
  repeated_listvalue_ = repeated_listvalue;
  if (repeated_listvalue) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_set_allocated:google.protobuf.testing.StructTestCases.repeated_listvalue)
}

// optional .google.protobuf.testing.MapOfStruct map_of_struct = 37;
bool StructTestCases::has_map_of_struct() const {
  return this != internal_default_instance() && map_of_struct_ != NULL;
}
void StructTestCases::clear_map_of_struct() {
  if (GetArenaNoVirtual() == NULL && map_of_struct_ != NULL) delete map_of_struct_;
  map_of_struct_ = NULL;
}
const ::google::protobuf::testing::MapOfStruct& StructTestCases::map_of_struct() const {
  // @@protoc_insertion_point(field_get:google.protobuf.testing.StructTestCases.map_of_struct)
  return map_of_struct_ != NULL ? *map_of_struct_
                         : *::google::protobuf::testing::MapOfStruct::internal_default_instance();
}
::google::protobuf::testing::MapOfStruct* StructTestCases::mutable_map_of_struct() {
  
  if (map_of_struct_ == NULL) {
    map_of_struct_ = new ::google::protobuf::testing::MapOfStruct;
  }
  // @@protoc_insertion_point(field_mutable:google.protobuf.testing.StructTestCases.map_of_struct)
  return map_of_struct_;
}
::google::protobuf::testing::MapOfStruct* StructTestCases::release_map_of_struct() {
  // @@protoc_insertion_point(field_release:google.protobuf.testing.StructTestCases.map_of_struct)
  
  ::google::protobuf::testing::MapOfStruct* temp = map_of_struct_;
  map_of_struct_ = NULL;
  return temp;
}
void StructTestCases::set_allocated_map_of_struct(::google::protobuf::testing::MapOfStruct* map_of_struct) {
  delete map_of_struct_;
  map_of_struct_ = map_of_struct;
  if (map_of_struct) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_set_allocated:google.protobuf.testing.StructTestCases.map_of_struct)
}

// optional .google.protobuf.testing.MapOfStruct map_of_struct_value = 38;
bool StructTestCases::has_map_of_struct_value() const {
  return this != internal_default_instance() && map_of_struct_value_ != NULL;
}
void StructTestCases::clear_map_of_struct_value() {
  if (GetArenaNoVirtual() == NULL && map_of_struct_value_ != NULL) delete map_of_struct_value_;
  map_of_struct_value_ = NULL;
}
const ::google::protobuf::testing::MapOfStruct& StructTestCases::map_of_struct_value() const {
  // @@protoc_insertion_point(field_get:google.protobuf.testing.StructTestCases.map_of_struct_value)
  return map_of_struct_value_ != NULL ? *map_of_struct_value_
                         : *::google::protobuf::testing::MapOfStruct::internal_default_instance();
}
::google::protobuf::testing::MapOfStruct* StructTestCases::mutable_map_of_struct_value() {
  
  if (map_of_struct_value_ == NULL) {
    map_of_struct_value_ = new ::google::protobuf::testing::MapOfStruct;
  }
  // @@protoc_insertion_point(field_mutable:google.protobuf.testing.StructTestCases.map_of_struct_value)
  return map_of_struct_value_;
}
::google::protobuf::testing::MapOfStruct* StructTestCases::release_map_of_struct_value() {
  // @@protoc_insertion_point(field_release:google.protobuf.testing.StructTestCases.map_of_struct_value)
  
  ::google::protobuf::testing::MapOfStruct* temp = map_of_struct_value_;
  map_of_struct_value_ = NULL;
  return temp;
}
void StructTestCases::set_allocated_map_of_struct_value(::google::protobuf::testing::MapOfStruct* map_of_struct_value) {
  delete map_of_struct_value_;
  map_of_struct_value_ = map_of_struct_value;
  if (map_of_struct_value) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_set_allocated:google.protobuf.testing.StructTestCases.map_of_struct_value)
}

// optional .google.protobuf.testing.MapOfStruct map_of_listvalue = 39;
bool StructTestCases::has_map_of_listvalue() const {
  return this != internal_default_instance() && map_of_listvalue_ != NULL;
}
void StructTestCases::clear_map_of_listvalue() {
  if (GetArenaNoVirtual() == NULL && map_of_listvalue_ != NULL) delete map_of_listvalue_;
  map_of_listvalue_ = NULL;
}
const ::google::protobuf::testing::MapOfStruct& StructTestCases::map_of_listvalue() const {
  // @@protoc_insertion_point(field_get:google.protobuf.testing.StructTestCases.map_of_listvalue)
  return map_of_listvalue_ != NULL ? *map_of_listvalue_
                         : *::google::protobuf::testing::MapOfStruct::internal_default_instance();
}
::google::protobuf::testing::MapOfStruct* StructTestCases::mutable_map_of_listvalue() {
  
  if (map_of_listvalue_ == NULL) {
    map_of_listvalue_ = new ::google::protobuf::testing::MapOfStruct;
  }
  // @@protoc_insertion_point(field_mutable:google.protobuf.testing.StructTestCases.map_of_listvalue)
  return map_of_listvalue_;
}
::google::protobuf::testing::MapOfStruct* StructTestCases::release_map_of_listvalue() {
  // @@protoc_insertion_point(field_release:google.protobuf.testing.StructTestCases.map_of_listvalue)
  
  ::google::protobuf::testing::MapOfStruct* temp = map_of_listvalue_;
  map_of_listvalue_ = NULL;
  return temp;
}
void StructTestCases::set_allocated_map_of_listvalue(::google::protobuf::testing::MapOfStruct* map_of_listvalue) {
  delete map_of_listvalue_;
  map_of_listvalue_ = map_of_listvalue;
  if (map_of_listvalue) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_set_allocated:google.protobuf.testing.StructTestCases.map_of_listvalue)
}

inline const StructTestCases* StructTestCases::internal_default_instance() {
  return &StructTestCases_default_instance_.get();
}
#endif  // PROTOBUF_INLINE_NOT_IN_HEADERS

// ===================================================================

#if !defined(_MSC_VER) || _MSC_VER >= 1900
const int StructWrapper::kStructFieldNumber;
#endif  // !defined(_MSC_VER) || _MSC_VER >= 1900

StructWrapper::StructWrapper()
  : ::google::protobuf::Message(), _internal_metadata_(NULL) {
  if (this != internal_default_instance()) protobuf_InitDefaults_google_2fprotobuf_2futil_2finternal_2ftestdata_2fstruct_2eproto();
  SharedCtor();
  // @@protoc_insertion_point(constructor:google.protobuf.testing.StructWrapper)
}

void StructWrapper::InitAsDefaultInstance() {
  struct__ = const_cast< ::google::protobuf::Struct*>(
      ::google::protobuf::Struct::internal_default_instance());
}

StructWrapper::StructWrapper(const StructWrapper& from)
  : ::google::protobuf::Message(),
    _internal_metadata_(NULL) {
  SharedCtor();
  UnsafeMergeFrom(from);
  // @@protoc_insertion_point(copy_constructor:google.protobuf.testing.StructWrapper)
}

void StructWrapper::SharedCtor() {
  struct__ = NULL;
  _cached_size_ = 0;
}

StructWrapper::~StructWrapper() {
  // @@protoc_insertion_point(destructor:google.protobuf.testing.StructWrapper)
  SharedDtor();
}

void StructWrapper::SharedDtor() {
  if (this != &StructWrapper_default_instance_.get()) {
    delete struct__;
  }
}

void StructWrapper::SetCachedSize(int size) const {
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
}
const ::google::protobuf::Descriptor* StructWrapper::descriptor() {
  protobuf_AssignDescriptorsOnce();
  return StructWrapper_descriptor_;
}

const StructWrapper& StructWrapper::default_instance() {
  protobuf_InitDefaults_google_2fprotobuf_2futil_2finternal_2ftestdata_2fstruct_2eproto();
  return *internal_default_instance();
}

::google::protobuf::internal::ExplicitlyConstructed<StructWrapper> StructWrapper_default_instance_;

StructWrapper* StructWrapper::New(::google::protobuf::Arena* arena) const {
  StructWrapper* n = new StructWrapper;
  if (arena != NULL) {
    arena->Own(n);
  }
  return n;
}

void StructWrapper::Clear() {
// @@protoc_insertion_point(message_clear_start:google.protobuf.testing.StructWrapper)
  if (GetArenaNoVirtual() == NULL && struct__ != NULL) delete struct__;
  struct__ = NULL;
}

bool StructWrapper::MergePartialFromCodedStream(
    ::google::protobuf::io::CodedInputStream* input) {
#define DO_(EXPRESSION) if (!GOOGLE_PREDICT_TRUE(EXPRESSION)) goto failure
  ::google::protobuf::uint32 tag;
  // @@protoc_insertion_point(parse_start:google.protobuf.testing.StructWrapper)
  for (;;) {
    ::std::pair< ::google::protobuf::uint32, bool> p = input->ReadTagWithCutoff(127);
    tag = p.first;
    if (!p.second) goto handle_unusual;
    switch (::google::protobuf::internal::WireFormatLite::GetTagFieldNumber(tag)) {
      // optional .google.protobuf.Struct struct = 1;
      case 1: {
        if (tag == 10) {
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessageNoVirtual(
               input, mutable_struct_()));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectAtEnd()) goto success;
        break;
      }

      default: {
      handle_unusual:
        if (tag == 0 ||
            ::google::protobuf::internal::WireFormatLite::GetTagWireType(tag) ==
            ::google::protobuf::internal::WireFormatLite::WIRETYPE_END_GROUP) {
          goto success;
        }
        DO_(::google::protobuf::internal::WireFormatLite::SkipField(input, tag));
        break;
      }
    }
  }
success:
  // @@protoc_insertion_point(parse_success:google.protobuf.testing.StructWrapper)
  return true;
failure:
  // @@protoc_insertion_point(parse_failure:google.protobuf.testing.StructWrapper)
  return false;
#undef DO_
}

void StructWrapper::SerializeWithCachedSizes(
    ::google::protobuf::io::CodedOutputStream* output) const {
  // @@protoc_insertion_point(serialize_start:google.protobuf.testing.StructWrapper)
  // optional .google.protobuf.Struct struct = 1;
  if (this->has_struct_()) {
    ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
      1, *this->struct__, output);
  }

  // @@protoc_insertion_point(serialize_end:google.protobuf.testing.StructWrapper)
}

::google::protobuf::uint8* StructWrapper::InternalSerializeWithCachedSizesToArray(
    bool deterministic, ::google::protobuf::uint8* target) const {
  (void)deterministic; // Unused
  // @@protoc_insertion_point(serialize_to_array_start:google.protobuf.testing.StructWrapper)
  // optional .google.protobuf.Struct struct = 1;
  if (this->has_struct_()) {
    target = ::google::protobuf::internal::WireFormatLite::
      InternalWriteMessageNoVirtualToArray(
        1, *this->struct__, false, target);
  }

  // @@protoc_insertion_point(serialize_to_array_end:google.protobuf.testing.StructWrapper)
  return target;
}

size_t StructWrapper::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:google.protobuf.testing.StructWrapper)
  size_t total_size = 0;

  // optional .google.protobuf.Struct struct = 1;
  if (this->has_struct_()) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::MessageSizeNoVirtual(
        *this->struct__);
  }

  int cached_size = ::google::protobuf::internal::ToCachedSize(total_size);
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = cached_size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
  return total_size;
}

void StructWrapper::MergeFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_merge_from_start:google.protobuf.testing.StructWrapper)
  if (GOOGLE_PREDICT_FALSE(&from == this)) MergeFromFail(__LINE__);
  const StructWrapper* source =
      ::google::protobuf::internal::DynamicCastToGenerated<const StructWrapper>(
          &from);
  if (source == NULL) {
  // @@protoc_insertion_point(generalized_merge_from_cast_fail:google.protobuf.testing.StructWrapper)
    ::google::protobuf::internal::ReflectionOps::Merge(from, this);
  } else {
  // @@protoc_insertion_point(generalized_merge_from_cast_success:google.protobuf.testing.StructWrapper)
    UnsafeMergeFrom(*source);
  }
}

void StructWrapper::MergeFrom(const StructWrapper& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:google.protobuf.testing.StructWrapper)
  if (GOOGLE_PREDICT_TRUE(&from != this)) {
    UnsafeMergeFrom(from);
  } else {
    MergeFromFail(__LINE__);
  }
}

void StructWrapper::UnsafeMergeFrom(const StructWrapper& from) {
  GOOGLE_DCHECK(&from != this);
  if (from.has_struct_()) {
    mutable_struct_()->::google::protobuf::Struct::MergeFrom(from.struct_());
  }
}

void StructWrapper::CopyFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_copy_from_start:google.protobuf.testing.StructWrapper)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

void StructWrapper::CopyFrom(const StructWrapper& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:google.protobuf.testing.StructWrapper)
  if (&from == this) return;
  Clear();
  UnsafeMergeFrom(from);
}

bool StructWrapper::IsInitialized() const {

  return true;
}

void StructWrapper::Swap(StructWrapper* other) {
  if (other == this) return;
  InternalSwap(other);
}
void StructWrapper::InternalSwap(StructWrapper* other) {
  std::swap(struct__, other->struct__);
  _internal_metadata_.Swap(&other->_internal_metadata_);
  std::swap(_cached_size_, other->_cached_size_);
}

::google::protobuf::Metadata StructWrapper::GetMetadata() const {
  protobuf_AssignDescriptorsOnce();
  ::google::protobuf::Metadata metadata;
  metadata.descriptor = StructWrapper_descriptor_;
  metadata.reflection = StructWrapper_reflection_;
  return metadata;
}

#if PROTOBUF_INLINE_NOT_IN_HEADERS
// StructWrapper

// optional .google.protobuf.Struct struct = 1;
bool StructWrapper::has_struct_() const {
  return this != internal_default_instance() && struct__ != NULL;
}
void StructWrapper::clear_struct_() {
  if (GetArenaNoVirtual() == NULL && struct__ != NULL) delete struct__;
  struct__ = NULL;
}
const ::google::protobuf::Struct& StructWrapper::struct_() const {
  // @@protoc_insertion_point(field_get:google.protobuf.testing.StructWrapper.struct)
  return struct__ != NULL ? *struct__
                         : *::google::protobuf::Struct::internal_default_instance();
}
::google::protobuf::Struct* StructWrapper::mutable_struct_() {
  
  if (struct__ == NULL) {
    struct__ = new ::google::protobuf::Struct;
  }
  // @@protoc_insertion_point(field_mutable:google.protobuf.testing.StructWrapper.struct)
  return struct__;
}
::google::protobuf::Struct* StructWrapper::release_struct_() {
  // @@protoc_insertion_point(field_release:google.protobuf.testing.StructWrapper.struct)
  
  ::google::protobuf::Struct* temp = struct__;
  struct__ = NULL;
  return temp;
}
void StructWrapper::set_allocated_struct_(::google::protobuf::Struct* struct_) {
  delete struct__;
  if (struct_ != NULL && struct_->GetArena() != NULL) {
    ::google::protobuf::Struct* new_struct_ = new ::google::protobuf::Struct;
    new_struct_->CopyFrom(*struct_);
    struct_ = new_struct_;
  }
  struct__ = struct_;
  if (struct_) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_set_allocated:google.protobuf.testing.StructWrapper.struct)
}

inline const StructWrapper* StructWrapper::internal_default_instance() {
  return &StructWrapper_default_instance_.get();
}
#endif  // PROTOBUF_INLINE_NOT_IN_HEADERS

// ===================================================================

#if !defined(_MSC_VER) || _MSC_VER >= 1900
const int ValueWrapper::kValueFieldNumber;
#endif  // !defined(_MSC_VER) || _MSC_VER >= 1900

ValueWrapper::ValueWrapper()
  : ::google::protobuf::Message(), _internal_metadata_(NULL) {
  if (this != internal_default_instance()) protobuf_InitDefaults_google_2fprotobuf_2futil_2finternal_2ftestdata_2fstruct_2eproto();
  SharedCtor();
  // @@protoc_insertion_point(constructor:google.protobuf.testing.ValueWrapper)
}

void ValueWrapper::InitAsDefaultInstance() {
  value_ = const_cast< ::google::protobuf::Value*>(
      ::google::protobuf::Value::internal_default_instance());
}

ValueWrapper::ValueWrapper(const ValueWrapper& from)
  : ::google::protobuf::Message(),
    _internal_metadata_(NULL) {
  SharedCtor();
  UnsafeMergeFrom(from);
  // @@protoc_insertion_point(copy_constructor:google.protobuf.testing.ValueWrapper)
}

void ValueWrapper::SharedCtor() {
  value_ = NULL;
  _cached_size_ = 0;
}

ValueWrapper::~ValueWrapper() {
  // @@protoc_insertion_point(destructor:google.protobuf.testing.ValueWrapper)
  SharedDtor();
}

void ValueWrapper::SharedDtor() {
  if (this != &ValueWrapper_default_instance_.get()) {
    delete value_;
  }
}

void ValueWrapper::SetCachedSize(int size) const {
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
}
const ::google::protobuf::Descriptor* ValueWrapper::descriptor() {
  protobuf_AssignDescriptorsOnce();
  return ValueWrapper_descriptor_;
}

const ValueWrapper& ValueWrapper::default_instance() {
  protobuf_InitDefaults_google_2fprotobuf_2futil_2finternal_2ftestdata_2fstruct_2eproto();
  return *internal_default_instance();
}

::google::protobuf::internal::ExplicitlyConstructed<ValueWrapper> ValueWrapper_default_instance_;

ValueWrapper* ValueWrapper::New(::google::protobuf::Arena* arena) const {
  ValueWrapper* n = new ValueWrapper;
  if (arena != NULL) {
    arena->Own(n);
  }
  return n;
}

void ValueWrapper::Clear() {
// @@protoc_insertion_point(message_clear_start:google.protobuf.testing.ValueWrapper)
  if (GetArenaNoVirtual() == NULL && value_ != NULL) delete value_;
  value_ = NULL;
}

bool ValueWrapper::MergePartialFromCodedStream(
    ::google::protobuf::io::CodedInputStream* input) {
#define DO_(EXPRESSION) if (!GOOGLE_PREDICT_TRUE(EXPRESSION)) goto failure
  ::google::protobuf::uint32 tag;
  // @@protoc_insertion_point(parse_start:google.protobuf.testing.ValueWrapper)
  for (;;) {
    ::std::pair< ::google::protobuf::uint32, bool> p = input->ReadTagWithCutoff(127);
    tag = p.first;
    if (!p.second) goto handle_unusual;
    switch (::google::protobuf::internal::WireFormatLite::GetTagFieldNumber(tag)) {
      // optional .google.protobuf.Value value = 1;
      case 1: {
        if (tag == 10) {
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessageNoVirtual(
               input, mutable_value()));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectAtEnd()) goto success;
        break;
      }

      default: {
      handle_unusual:
        if (tag == 0 ||
            ::google::protobuf::internal::WireFormatLite::GetTagWireType(tag) ==
            ::google::protobuf::internal::WireFormatLite::WIRETYPE_END_GROUP) {
          goto success;
        }
        DO_(::google::protobuf::internal::WireFormatLite::SkipField(input, tag));
        break;
      }
    }
  }
success:
  // @@protoc_insertion_point(parse_success:google.protobuf.testing.ValueWrapper)
  return true;
failure:
  // @@protoc_insertion_point(parse_failure:google.protobuf.testing.ValueWrapper)
  return false;
#undef DO_
}

void ValueWrapper::SerializeWithCachedSizes(
    ::google::protobuf::io::CodedOutputStream* output) const {
  // @@protoc_insertion_point(serialize_start:google.protobuf.testing.ValueWrapper)
  // optional .google.protobuf.Value value = 1;
  if (this->has_value()) {
    ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
      1, *this->value_, output);
  }

  // @@protoc_insertion_point(serialize_end:google.protobuf.testing.ValueWrapper)
}

::google::protobuf::uint8* ValueWrapper::InternalSerializeWithCachedSizesToArray(
    bool deterministic, ::google::protobuf::uint8* target) const {
  (void)deterministic; // Unused
  // @@protoc_insertion_point(serialize_to_array_start:google.protobuf.testing.ValueWrapper)
  // optional .google.protobuf.Value value = 1;
  if (this->has_value()) {
    target = ::google::protobuf::internal::WireFormatLite::
      InternalWriteMessageNoVirtualToArray(
        1, *this->value_, false, target);
  }

  // @@protoc_insertion_point(serialize_to_array_end:google.protobuf.testing.ValueWrapper)
  return target;
}

size_t ValueWrapper::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:google.protobuf.testing.ValueWrapper)
  size_t total_size = 0;

  // optional .google.protobuf.Value value = 1;
  if (this->has_value()) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::MessageSizeNoVirtual(
        *this->value_);
  }

  int cached_size = ::google::protobuf::internal::ToCachedSize(total_size);
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = cached_size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
  return total_size;
}

void ValueWrapper::MergeFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_merge_from_start:google.protobuf.testing.ValueWrapper)
  if (GOOGLE_PREDICT_FALSE(&from == this)) MergeFromFail(__LINE__);
  const ValueWrapper* source =
      ::google::protobuf::internal::DynamicCastToGenerated<const ValueWrapper>(
          &from);
  if (source == NULL) {
  // @@protoc_insertion_point(generalized_merge_from_cast_fail:google.protobuf.testing.ValueWrapper)
    ::google::protobuf::internal::ReflectionOps::Merge(from, this);
  } else {
  // @@protoc_insertion_point(generalized_merge_from_cast_success:google.protobuf.testing.ValueWrapper)
    UnsafeMergeFrom(*source);
  }
}

void ValueWrapper::MergeFrom(const ValueWrapper& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:google.protobuf.testing.ValueWrapper)
  if (GOOGLE_PREDICT_TRUE(&from != this)) {
    UnsafeMergeFrom(from);
  } else {
    MergeFromFail(__LINE__);
  }
}

void ValueWrapper::UnsafeMergeFrom(const ValueWrapper& from) {
  GOOGLE_DCHECK(&from != this);
  if (from.has_value()) {
    mutable_value()->::google::protobuf::Value::MergeFrom(from.value());
  }
}

void ValueWrapper::CopyFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_copy_from_start:google.protobuf.testing.ValueWrapper)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

void ValueWrapper::CopyFrom(const ValueWrapper& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:google.protobuf.testing.ValueWrapper)
  if (&from == this) return;
  Clear();
  UnsafeMergeFrom(from);
}

bool ValueWrapper::IsInitialized() const {

  return true;
}

void ValueWrapper::Swap(ValueWrapper* other) {
  if (other == this) return;
  InternalSwap(other);
}
void ValueWrapper::InternalSwap(ValueWrapper* other) {
  std::swap(value_, other->value_);
  _internal_metadata_.Swap(&other->_internal_metadata_);
  std::swap(_cached_size_, other->_cached_size_);
}

::google::protobuf::Metadata ValueWrapper::GetMetadata() const {
  protobuf_AssignDescriptorsOnce();
  ::google::protobuf::Metadata metadata;
  metadata.descriptor = ValueWrapper_descriptor_;
  metadata.reflection = ValueWrapper_reflection_;
  return metadata;
}

#if PROTOBUF_INLINE_NOT_IN_HEADERS
// ValueWrapper

// optional .google.protobuf.Value value = 1;
bool ValueWrapper::has_value() const {
  return this != internal_default_instance() && value_ != NULL;
}
void ValueWrapper::clear_value() {
  if (GetArenaNoVirtual() == NULL && value_ != NULL) delete value_;
  value_ = NULL;
}
const ::google::protobuf::Value& ValueWrapper::value() const {
  // @@protoc_insertion_point(field_get:google.protobuf.testing.ValueWrapper.value)
  return value_ != NULL ? *value_
                         : *::google::protobuf::Value::internal_default_instance();
}
::google::protobuf::Value* ValueWrapper::mutable_value() {
  
  if (value_ == NULL) {
    value_ = new ::google::protobuf::Value;
  }
  // @@protoc_insertion_point(field_mutable:google.protobuf.testing.ValueWrapper.value)
  return value_;
}
::google::protobuf::Value* ValueWrapper::release_value() {
  // @@protoc_insertion_point(field_release:google.protobuf.testing.ValueWrapper.value)
  
  ::google::protobuf::Value* temp = value_;
  value_ = NULL;
  return temp;
}
void ValueWrapper::set_allocated_value(::google::protobuf::Value* value) {
  delete value_;
  if (value != NULL && value->GetArena() != NULL) {
    ::google::protobuf::Value* new_value = new ::google::protobuf::Value;
    new_value->CopyFrom(*value);
    value = new_value;
  }
  value_ = value;
  if (value) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_set_allocated:google.protobuf.testing.ValueWrapper.value)
}

inline const ValueWrapper* ValueWrapper::internal_default_instance() {
  return &ValueWrapper_default_instance_.get();
}
#endif  // PROTOBUF_INLINE_NOT_IN_HEADERS

// ===================================================================

#if !defined(_MSC_VER) || _MSC_VER >= 1900
const int RepeatedValueWrapper::kValuesFieldNumber;
#endif  // !defined(_MSC_VER) || _MSC_VER >= 1900

RepeatedValueWrapper::RepeatedValueWrapper()
  : ::google::protobuf::Message(), _internal_metadata_(NULL) {
  if (this != internal_default_instance()) protobuf_InitDefaults_google_2fprotobuf_2futil_2finternal_2ftestdata_2fstruct_2eproto();
  SharedCtor();
  // @@protoc_insertion_point(constructor:google.protobuf.testing.RepeatedValueWrapper)
}

void RepeatedValueWrapper::InitAsDefaultInstance() {
}

RepeatedValueWrapper::RepeatedValueWrapper(const RepeatedValueWrapper& from)
  : ::google::protobuf::Message(),
    _internal_metadata_(NULL) {
  SharedCtor();
  UnsafeMergeFrom(from);
  // @@protoc_insertion_point(copy_constructor:google.protobuf.testing.RepeatedValueWrapper)
}

void RepeatedValueWrapper::SharedCtor() {
  _cached_size_ = 0;
}

RepeatedValueWrapper::~RepeatedValueWrapper() {
  // @@protoc_insertion_point(destructor:google.protobuf.testing.RepeatedValueWrapper)
  SharedDtor();
}

void RepeatedValueWrapper::SharedDtor() {
}

void RepeatedValueWrapper::SetCachedSize(int size) const {
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
}
const ::google::protobuf::Descriptor* RepeatedValueWrapper::descriptor() {
  protobuf_AssignDescriptorsOnce();
  return RepeatedValueWrapper_descriptor_;
}

const RepeatedValueWrapper& RepeatedValueWrapper::default_instance() {
  protobuf_InitDefaults_google_2fprotobuf_2futil_2finternal_2ftestdata_2fstruct_2eproto();
  return *internal_default_instance();
}

::google::protobuf::internal::ExplicitlyConstructed<RepeatedValueWrapper> RepeatedValueWrapper_default_instance_;

RepeatedValueWrapper* RepeatedValueWrapper::New(::google::protobuf::Arena* arena) const {
  RepeatedValueWrapper* n = new RepeatedValueWrapper;
  if (arena != NULL) {
    arena->Own(n);
  }
  return n;
}

void RepeatedValueWrapper::Clear() {
// @@protoc_insertion_point(message_clear_start:google.protobuf.testing.RepeatedValueWrapper)
  values_.Clear();
}

bool RepeatedValueWrapper::MergePartialFromCodedStream(
    ::google::protobuf::io::CodedInputStream* input) {
#define DO_(EXPRESSION) if (!GOOGLE_PREDICT_TRUE(EXPRESSION)) goto failure
  ::google::protobuf::uint32 tag;
  // @@protoc_insertion_point(parse_start:google.protobuf.testing.RepeatedValueWrapper)
  for (;;) {
    ::std::pair< ::google::protobuf::uint32, bool> p = input->ReadTagWithCutoff(127);
    tag = p.first;
    if (!p.second) goto handle_unusual;
    switch (::google::protobuf::internal::WireFormatLite::GetTagFieldNumber(tag)) {
      // repeated .google.protobuf.Value values = 1;
      case 1: {
        if (tag == 10) {
          DO_(input->IncrementRecursionDepth());
         parse_loop_values:
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessageNoVirtualNoRecursionDepth(
                input, add_values()));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(10)) goto parse_loop_values;
        input->UnsafeDecrementRecursionDepth();
        if (input->ExpectAtEnd()) goto success;
        break;
      }

      default: {
      handle_unusual:
        if (tag == 0 ||
            ::google::protobuf::internal::WireFormatLite::GetTagWireType(tag) ==
            ::google::protobuf::internal::WireFormatLite::WIRETYPE_END_GROUP) {
          goto success;
        }
        DO_(::google::protobuf::internal::WireFormatLite::SkipField(input, tag));
        break;
      }
    }
  }
success:
  // @@protoc_insertion_point(parse_success:google.protobuf.testing.RepeatedValueWrapper)
  return true;
failure:
  // @@protoc_insertion_point(parse_failure:google.protobuf.testing.RepeatedValueWrapper)
  return false;
#undef DO_
}

void RepeatedValueWrapper::SerializeWithCachedSizes(
    ::google::protobuf::io::CodedOutputStream* output) const {
  // @@protoc_insertion_point(serialize_start:google.protobuf.testing.RepeatedValueWrapper)
  // repeated .google.protobuf.Value values = 1;
  for (unsigned int i = 0, n = this->values_size(); i < n; i++) {
    ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
      1, this->values(i), output);
  }

  // @@protoc_insertion_point(serialize_end:google.protobuf.testing.RepeatedValueWrapper)
}

::google::protobuf::uint8* RepeatedValueWrapper::InternalSerializeWithCachedSizesToArray(
    bool deterministic, ::google::protobuf::uint8* target) const {
  (void)deterministic; // Unused
  // @@protoc_insertion_point(serialize_to_array_start:google.protobuf.testing.RepeatedValueWrapper)
  // repeated .google.protobuf.Value values = 1;
  for (unsigned int i = 0, n = this->values_size(); i < n; i++) {
    target = ::google::protobuf::internal::WireFormatLite::
      InternalWriteMessageNoVirtualToArray(
        1, this->values(i), false, target);
  }

  // @@protoc_insertion_point(serialize_to_array_end:google.protobuf.testing.RepeatedValueWrapper)
  return target;
}

size_t RepeatedValueWrapper::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:google.protobuf.testing.RepeatedValueWrapper)
  size_t total_size = 0;

  // repeated .google.protobuf.Value values = 1;
  {
    unsigned int count = this->values_size();
    total_size += 1UL * count;
    for (unsigned int i = 0; i < count; i++) {
      total_size +=
        ::google::protobuf::internal::WireFormatLite::MessageSizeNoVirtual(
          this->values(i));
    }
  }

  int cached_size = ::google::protobuf::internal::ToCachedSize(total_size);
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = cached_size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
  return total_size;
}

void RepeatedValueWrapper::MergeFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_merge_from_start:google.protobuf.testing.RepeatedValueWrapper)
  if (GOOGLE_PREDICT_FALSE(&from == this)) MergeFromFail(__LINE__);
  const RepeatedValueWrapper* source =
      ::google::protobuf::internal::DynamicCastToGenerated<const RepeatedValueWrapper>(
          &from);
  if (source == NULL) {
  // @@protoc_insertion_point(generalized_merge_from_cast_fail:google.protobuf.testing.RepeatedValueWrapper)
    ::google::protobuf::internal::ReflectionOps::Merge(from, this);
  } else {
  // @@protoc_insertion_point(generalized_merge_from_cast_success:google.protobuf.testing.RepeatedValueWrapper)
    UnsafeMergeFrom(*source);
  }
}

void RepeatedValueWrapper::MergeFrom(const RepeatedValueWrapper& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:google.protobuf.testing.RepeatedValueWrapper)
  if (GOOGLE_PREDICT_TRUE(&from != this)) {
    UnsafeMergeFrom(from);
  } else {
    MergeFromFail(__LINE__);
  }
}

void RepeatedValueWrapper::UnsafeMergeFrom(const RepeatedValueWrapper& from) {
  GOOGLE_DCHECK(&from != this);
  values_.MergeFrom(from.values_);
}

void RepeatedValueWrapper::CopyFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_copy_from_start:google.protobuf.testing.RepeatedValueWrapper)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

void RepeatedValueWrapper::CopyFrom(const RepeatedValueWrapper& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:google.protobuf.testing.RepeatedValueWrapper)
  if (&from == this) return;
  Clear();
  UnsafeMergeFrom(from);
}

bool RepeatedValueWrapper::IsInitialized() const {

  return true;
}

void RepeatedValueWrapper::Swap(RepeatedValueWrapper* other) {
  if (other == this) return;
  InternalSwap(other);
}
void RepeatedValueWrapper::InternalSwap(RepeatedValueWrapper* other) {
  values_.UnsafeArenaSwap(&other->values_);
  _internal_metadata_.Swap(&other->_internal_metadata_);
  std::swap(_cached_size_, other->_cached_size_);
}

::google::protobuf::Metadata RepeatedValueWrapper::GetMetadata() const {
  protobuf_AssignDescriptorsOnce();
  ::google::protobuf::Metadata metadata;
  metadata.descriptor = RepeatedValueWrapper_descriptor_;
  metadata.reflection = RepeatedValueWrapper_reflection_;
  return metadata;
}

#if PROTOBUF_INLINE_NOT_IN_HEADERS
// RepeatedValueWrapper

// repeated .google.protobuf.Value values = 1;
int RepeatedValueWrapper::values_size() const {
  return values_.size();
}
void RepeatedValueWrapper::clear_values() {
  values_.Clear();
}
const ::google::protobuf::Value& RepeatedValueWrapper::values(int index) const {
  // @@protoc_insertion_point(field_get:google.protobuf.testing.RepeatedValueWrapper.values)
  return values_.Get(index);
}
::google::protobuf::Value* RepeatedValueWrapper::mutable_values(int index) {
  // @@protoc_insertion_point(field_mutable:google.protobuf.testing.RepeatedValueWrapper.values)
  return values_.Mutable(index);
}
::google::protobuf::Value* RepeatedValueWrapper::add_values() {
  // @@protoc_insertion_point(field_add:google.protobuf.testing.RepeatedValueWrapper.values)
  return values_.Add();
}
::google::protobuf::RepeatedPtrField< ::google::protobuf::Value >*
RepeatedValueWrapper::mutable_values() {
  // @@protoc_insertion_point(field_mutable_list:google.protobuf.testing.RepeatedValueWrapper.values)
  return &values_;
}
const ::google::protobuf::RepeatedPtrField< ::google::protobuf::Value >&
RepeatedValueWrapper::values() const {
  // @@protoc_insertion_point(field_list:google.protobuf.testing.RepeatedValueWrapper.values)
  return values_;
}

inline const RepeatedValueWrapper* RepeatedValueWrapper::internal_default_instance() {
  return &RepeatedValueWrapper_default_instance_.get();
}
#endif  // PROTOBUF_INLINE_NOT_IN_HEADERS

// ===================================================================

#if !defined(_MSC_VER) || _MSC_VER >= 1900
const int ListValueWrapper::kShoppingListFieldNumber;
#endif  // !defined(_MSC_VER) || _MSC_VER >= 1900

ListValueWrapper::ListValueWrapper()
  : ::google::protobuf::Message(), _internal_metadata_(NULL) {
  if (this != internal_default_instance()) protobuf_InitDefaults_google_2fprotobuf_2futil_2finternal_2ftestdata_2fstruct_2eproto();
  SharedCtor();
  // @@protoc_insertion_point(constructor:google.protobuf.testing.ListValueWrapper)
}

void ListValueWrapper::InitAsDefaultInstance() {
  shopping_list_ = const_cast< ::google::protobuf::ListValue*>(
      ::google::protobuf::ListValue::internal_default_instance());
}

ListValueWrapper::ListValueWrapper(const ListValueWrapper& from)
  : ::google::protobuf::Message(),
    _internal_metadata_(NULL) {
  SharedCtor();
  UnsafeMergeFrom(from);
  // @@protoc_insertion_point(copy_constructor:google.protobuf.testing.ListValueWrapper)
}

void ListValueWrapper::SharedCtor() {
  shopping_list_ = NULL;
  _cached_size_ = 0;
}

ListValueWrapper::~ListValueWrapper() {
  // @@protoc_insertion_point(destructor:google.protobuf.testing.ListValueWrapper)
  SharedDtor();
}

void ListValueWrapper::SharedDtor() {
  if (this != &ListValueWrapper_default_instance_.get()) {
    delete shopping_list_;
  }
}

void ListValueWrapper::SetCachedSize(int size) const {
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
}
const ::google::protobuf::Descriptor* ListValueWrapper::descriptor() {
  protobuf_AssignDescriptorsOnce();
  return ListValueWrapper_descriptor_;
}

const ListValueWrapper& ListValueWrapper::default_instance() {
  protobuf_InitDefaults_google_2fprotobuf_2futil_2finternal_2ftestdata_2fstruct_2eproto();
  return *internal_default_instance();
}

::google::protobuf::internal::ExplicitlyConstructed<ListValueWrapper> ListValueWrapper_default_instance_;

ListValueWrapper* ListValueWrapper::New(::google::protobuf::Arena* arena) const {
  ListValueWrapper* n = new ListValueWrapper;
  if (arena != NULL) {
    arena->Own(n);
  }
  return n;
}

void ListValueWrapper::Clear() {
// @@protoc_insertion_point(message_clear_start:google.protobuf.testing.ListValueWrapper)
  if (GetArenaNoVirtual() == NULL && shopping_list_ != NULL) delete shopping_list_;
  shopping_list_ = NULL;
}

bool ListValueWrapper::MergePartialFromCodedStream(
    ::google::protobuf::io::CodedInputStream* input) {
#define DO_(EXPRESSION) if (!GOOGLE_PREDICT_TRUE(EXPRESSION)) goto failure
  ::google::protobuf::uint32 tag;
  // @@protoc_insertion_point(parse_start:google.protobuf.testing.ListValueWrapper)
  for (;;) {
    ::std::pair< ::google::protobuf::uint32, bool> p = input->ReadTagWithCutoff(127);
    tag = p.first;
    if (!p.second) goto handle_unusual;
    switch (::google::protobuf::internal::WireFormatLite::GetTagFieldNumber(tag)) {
      // optional .google.protobuf.ListValue shopping_list = 1;
      case 1: {
        if (tag == 10) {
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessageNoVirtual(
               input, mutable_shopping_list()));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectAtEnd()) goto success;
        break;
      }

      default: {
      handle_unusual:
        if (tag == 0 ||
            ::google::protobuf::internal::WireFormatLite::GetTagWireType(tag) ==
            ::google::protobuf::internal::WireFormatLite::WIRETYPE_END_GROUP) {
          goto success;
        }
        DO_(::google::protobuf::internal::WireFormatLite::SkipField(input, tag));
        break;
      }
    }
  }
success:
  // @@protoc_insertion_point(parse_success:google.protobuf.testing.ListValueWrapper)
  return true;
failure:
  // @@protoc_insertion_point(parse_failure:google.protobuf.testing.ListValueWrapper)
  return false;
#undef DO_
}

void ListValueWrapper::SerializeWithCachedSizes(
    ::google::protobuf::io::CodedOutputStream* output) const {
  // @@protoc_insertion_point(serialize_start:google.protobuf.testing.ListValueWrapper)
  // optional .google.protobuf.ListValue shopping_list = 1;
  if (this->has_shopping_list()) {
    ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
      1, *this->shopping_list_, output);
  }

  // @@protoc_insertion_point(serialize_end:google.protobuf.testing.ListValueWrapper)
}

::google::protobuf::uint8* ListValueWrapper::InternalSerializeWithCachedSizesToArray(
    bool deterministic, ::google::protobuf::uint8* target) const {
  (void)deterministic; // Unused
  // @@protoc_insertion_point(serialize_to_array_start:google.protobuf.testing.ListValueWrapper)
  // optional .google.protobuf.ListValue shopping_list = 1;
  if (this->has_shopping_list()) {
    target = ::google::protobuf::internal::WireFormatLite::
      InternalWriteMessageNoVirtualToArray(
        1, *this->shopping_list_, false, target);
  }

  // @@protoc_insertion_point(serialize_to_array_end:google.protobuf.testing.ListValueWrapper)
  return target;
}

size_t ListValueWrapper::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:google.protobuf.testing.ListValueWrapper)
  size_t total_size = 0;

  // optional .google.protobuf.ListValue shopping_list = 1;
  if (this->has_shopping_list()) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::MessageSizeNoVirtual(
        *this->shopping_list_);
  }

  int cached_size = ::google::protobuf::internal::ToCachedSize(total_size);
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = cached_size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
  return total_size;
}

void ListValueWrapper::MergeFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_merge_from_start:google.protobuf.testing.ListValueWrapper)
  if (GOOGLE_PREDICT_FALSE(&from == this)) MergeFromFail(__LINE__);
  const ListValueWrapper* source =
      ::google::protobuf::internal::DynamicCastToGenerated<const ListValueWrapper>(
          &from);
  if (source == NULL) {
  // @@protoc_insertion_point(generalized_merge_from_cast_fail:google.protobuf.testing.ListValueWrapper)
    ::google::protobuf::internal::ReflectionOps::Merge(from, this);
  } else {
  // @@protoc_insertion_point(generalized_merge_from_cast_success:google.protobuf.testing.ListValueWrapper)
    UnsafeMergeFrom(*source);
  }
}

void ListValueWrapper::MergeFrom(const ListValueWrapper& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:google.protobuf.testing.ListValueWrapper)
  if (GOOGLE_PREDICT_TRUE(&from != this)) {
    UnsafeMergeFrom(from);
  } else {
    MergeFromFail(__LINE__);
  }
}

void ListValueWrapper::UnsafeMergeFrom(const ListValueWrapper& from) {
  GOOGLE_DCHECK(&from != this);
  if (from.has_shopping_list()) {
    mutable_shopping_list()->::google::protobuf::ListValue::MergeFrom(from.shopping_list());
  }
}

void ListValueWrapper::CopyFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_copy_from_start:google.protobuf.testing.ListValueWrapper)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

void ListValueWrapper::CopyFrom(const ListValueWrapper& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:google.protobuf.testing.ListValueWrapper)
  if (&from == this) return;
  Clear();
  UnsafeMergeFrom(from);
}

bool ListValueWrapper::IsInitialized() const {

  return true;
}

void ListValueWrapper::Swap(ListValueWrapper* other) {
  if (other == this) return;
  InternalSwap(other);
}
void ListValueWrapper::InternalSwap(ListValueWrapper* other) {
  std::swap(shopping_list_, other->shopping_list_);
  _internal_metadata_.Swap(&other->_internal_metadata_);
  std::swap(_cached_size_, other->_cached_size_);
}

::google::protobuf::Metadata ListValueWrapper::GetMetadata() const {
  protobuf_AssignDescriptorsOnce();
  ::google::protobuf::Metadata metadata;
  metadata.descriptor = ListValueWrapper_descriptor_;
  metadata.reflection = ListValueWrapper_reflection_;
  return metadata;
}

#if PROTOBUF_INLINE_NOT_IN_HEADERS
// ListValueWrapper

// optional .google.protobuf.ListValue shopping_list = 1;
bool ListValueWrapper::has_shopping_list() const {
  return this != internal_default_instance() && shopping_list_ != NULL;
}
void ListValueWrapper::clear_shopping_list() {
  if (GetArenaNoVirtual() == NULL && shopping_list_ != NULL) delete shopping_list_;
  shopping_list_ = NULL;
}
const ::google::protobuf::ListValue& ListValueWrapper::shopping_list() const {
  // @@protoc_insertion_point(field_get:google.protobuf.testing.ListValueWrapper.shopping_list)
  return shopping_list_ != NULL ? *shopping_list_
                         : *::google::protobuf::ListValue::internal_default_instance();
}
::google::protobuf::ListValue* ListValueWrapper::mutable_shopping_list() {
  
  if (shopping_list_ == NULL) {
    shopping_list_ = new ::google::protobuf::ListValue;
  }
  // @@protoc_insertion_point(field_mutable:google.protobuf.testing.ListValueWrapper.shopping_list)
  return shopping_list_;
}
::google::protobuf::ListValue* ListValueWrapper::release_shopping_list() {
  // @@protoc_insertion_point(field_release:google.protobuf.testing.ListValueWrapper.shopping_list)
  
  ::google::protobuf::ListValue* temp = shopping_list_;
  shopping_list_ = NULL;
  return temp;
}
void ListValueWrapper::set_allocated_shopping_list(::google::protobuf::ListValue* shopping_list) {
  delete shopping_list_;
  if (shopping_list != NULL && shopping_list->GetArena() != NULL) {
    ::google::protobuf::ListValue* new_shopping_list = new ::google::protobuf::ListValue;
    new_shopping_list->CopyFrom(*shopping_list);
    shopping_list = new_shopping_list;
  }
  shopping_list_ = shopping_list;
  if (shopping_list) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_set_allocated:google.protobuf.testing.ListValueWrapper.shopping_list)
}

inline const ListValueWrapper* ListValueWrapper::internal_default_instance() {
  return &ListValueWrapper_default_instance_.get();
}
#endif  // PROTOBUF_INLINE_NOT_IN_HEADERS

// ===================================================================

#if !defined(_MSC_VER) || _MSC_VER >= 1900
const int RepeatedListValueWrapper::kDimensionsFieldNumber;
#endif  // !defined(_MSC_VER) || _MSC_VER >= 1900

RepeatedListValueWrapper::RepeatedListValueWrapper()
  : ::google::protobuf::Message(), _internal_metadata_(NULL) {
  if (this != internal_default_instance()) protobuf_InitDefaults_google_2fprotobuf_2futil_2finternal_2ftestdata_2fstruct_2eproto();
  SharedCtor();
  // @@protoc_insertion_point(constructor:google.protobuf.testing.RepeatedListValueWrapper)
}

void RepeatedListValueWrapper::InitAsDefaultInstance() {
}

RepeatedListValueWrapper::RepeatedListValueWrapper(const RepeatedListValueWrapper& from)
  : ::google::protobuf::Message(),
    _internal_metadata_(NULL) {
  SharedCtor();
  UnsafeMergeFrom(from);
  // @@protoc_insertion_point(copy_constructor:google.protobuf.testing.RepeatedListValueWrapper)
}

void RepeatedListValueWrapper::SharedCtor() {
  _cached_size_ = 0;
}

RepeatedListValueWrapper::~RepeatedListValueWrapper() {
  // @@protoc_insertion_point(destructor:google.protobuf.testing.RepeatedListValueWrapper)
  SharedDtor();
}

void RepeatedListValueWrapper::SharedDtor() {
}

void RepeatedListValueWrapper::SetCachedSize(int size) const {
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
}
const ::google::protobuf::Descriptor* RepeatedListValueWrapper::descriptor() {
  protobuf_AssignDescriptorsOnce();
  return RepeatedListValueWrapper_descriptor_;
}

const RepeatedListValueWrapper& RepeatedListValueWrapper::default_instance() {
  protobuf_InitDefaults_google_2fprotobuf_2futil_2finternal_2ftestdata_2fstruct_2eproto();
  return *internal_default_instance();
}

::google::protobuf::internal::ExplicitlyConstructed<RepeatedListValueWrapper> RepeatedListValueWrapper_default_instance_;

RepeatedListValueWrapper* RepeatedListValueWrapper::New(::google::protobuf::Arena* arena) const {
  RepeatedListValueWrapper* n = new RepeatedListValueWrapper;
  if (arena != NULL) {
    arena->Own(n);
  }
  return n;
}

void RepeatedListValueWrapper::Clear() {
// @@protoc_insertion_point(message_clear_start:google.protobuf.testing.RepeatedListValueWrapper)
  dimensions_.Clear();
}

bool RepeatedListValueWrapper::MergePartialFromCodedStream(
    ::google::protobuf::io::CodedInputStream* input) {
#define DO_(EXPRESSION) if (!GOOGLE_PREDICT_TRUE(EXPRESSION)) goto failure
  ::google::protobuf::uint32 tag;
  // @@protoc_insertion_point(parse_start:google.protobuf.testing.RepeatedListValueWrapper)
  for (;;) {
    ::std::pair< ::google::protobuf::uint32, bool> p = input->ReadTagWithCutoff(127);
    tag = p.first;
    if (!p.second) goto handle_unusual;
    switch (::google::protobuf::internal::WireFormatLite::GetTagFieldNumber(tag)) {
      // repeated .google.protobuf.ListValue dimensions = 1;
      case 1: {
        if (tag == 10) {
          DO_(input->IncrementRecursionDepth());
         parse_loop_dimensions:
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessageNoVirtualNoRecursionDepth(
                input, add_dimensions()));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(10)) goto parse_loop_dimensions;
        input->UnsafeDecrementRecursionDepth();
        if (input->ExpectAtEnd()) goto success;
        break;
      }

      default: {
      handle_unusual:
        if (tag == 0 ||
            ::google::protobuf::internal::WireFormatLite::GetTagWireType(tag) ==
            ::google::protobuf::internal::WireFormatLite::WIRETYPE_END_GROUP) {
          goto success;
        }
        DO_(::google::protobuf::internal::WireFormatLite::SkipField(input, tag));
        break;
      }
    }
  }
success:
  // @@protoc_insertion_point(parse_success:google.protobuf.testing.RepeatedListValueWrapper)
  return true;
failure:
  // @@protoc_insertion_point(parse_failure:google.protobuf.testing.RepeatedListValueWrapper)
  return false;
#undef DO_
}

void RepeatedListValueWrapper::SerializeWithCachedSizes(
    ::google::protobuf::io::CodedOutputStream* output) const {
  // @@protoc_insertion_point(serialize_start:google.protobuf.testing.RepeatedListValueWrapper)
  // repeated .google.protobuf.ListValue dimensions = 1;
  for (unsigned int i = 0, n = this->dimensions_size(); i < n; i++) {
    ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
      1, this->dimensions(i), output);
  }

  // @@protoc_insertion_point(serialize_end:google.protobuf.testing.RepeatedListValueWrapper)
}

::google::protobuf::uint8* RepeatedListValueWrapper::InternalSerializeWithCachedSizesToArray(
    bool deterministic, ::google::protobuf::uint8* target) const {
  (void)deterministic; // Unused
  // @@protoc_insertion_point(serialize_to_array_start:google.protobuf.testing.RepeatedListValueWrapper)
  // repeated .google.protobuf.ListValue dimensions = 1;
  for (unsigned int i = 0, n = this->dimensions_size(); i < n; i++) {
    target = ::google::protobuf::internal::WireFormatLite::
      InternalWriteMessageNoVirtualToArray(
        1, this->dimensions(i), false, target);
  }

  // @@protoc_insertion_point(serialize_to_array_end:google.protobuf.testing.RepeatedListValueWrapper)
  return target;
}

size_t RepeatedListValueWrapper::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:google.protobuf.testing.RepeatedListValueWrapper)
  size_t total_size = 0;

  // repeated .google.protobuf.ListValue dimensions = 1;
  {
    unsigned int count = this->dimensions_size();
    total_size += 1UL * count;
    for (unsigned int i = 0; i < count; i++) {
      total_size +=
        ::google::protobuf::internal::WireFormatLite::MessageSizeNoVirtual(
          this->dimensions(i));
    }
  }

  int cached_size = ::google::protobuf::internal::ToCachedSize(total_size);
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = cached_size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
  return total_size;
}

void RepeatedListValueWrapper::MergeFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_merge_from_start:google.protobuf.testing.RepeatedListValueWrapper)
  if (GOOGLE_PREDICT_FALSE(&from == this)) MergeFromFail(__LINE__);
  const RepeatedListValueWrapper* source =
      ::google::protobuf::internal::DynamicCastToGenerated<const RepeatedListValueWrapper>(
          &from);
  if (source == NULL) {
  // @@protoc_insertion_point(generalized_merge_from_cast_fail:google.protobuf.testing.RepeatedListValueWrapper)
    ::google::protobuf::internal::ReflectionOps::Merge(from, this);
  } else {
  // @@protoc_insertion_point(generalized_merge_from_cast_success:google.protobuf.testing.RepeatedListValueWrapper)
    UnsafeMergeFrom(*source);
  }
}

void RepeatedListValueWrapper::MergeFrom(const RepeatedListValueWrapper& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:google.protobuf.testing.RepeatedListValueWrapper)
  if (GOOGLE_PREDICT_TRUE(&from != this)) {
    UnsafeMergeFrom(from);
  } else {
    MergeFromFail(__LINE__);
  }
}

void RepeatedListValueWrapper::UnsafeMergeFrom(const RepeatedListValueWrapper& from) {
  GOOGLE_DCHECK(&from != this);
  dimensions_.MergeFrom(from.dimensions_);
}

void RepeatedListValueWrapper::CopyFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_copy_from_start:google.protobuf.testing.RepeatedListValueWrapper)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

void RepeatedListValueWrapper::CopyFrom(const RepeatedListValueWrapper& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:google.protobuf.testing.RepeatedListValueWrapper)
  if (&from == this) return;
  Clear();
  UnsafeMergeFrom(from);
}

bool RepeatedListValueWrapper::IsInitialized() const {

  return true;
}

void RepeatedListValueWrapper::Swap(RepeatedListValueWrapper* other) {
  if (other == this) return;
  InternalSwap(other);
}
void RepeatedListValueWrapper::InternalSwap(RepeatedListValueWrapper* other) {
  dimensions_.UnsafeArenaSwap(&other->dimensions_);
  _internal_metadata_.Swap(&other->_internal_metadata_);
  std::swap(_cached_size_, other->_cached_size_);
}

::google::protobuf::Metadata RepeatedListValueWrapper::GetMetadata() const {
  protobuf_AssignDescriptorsOnce();
  ::google::protobuf::Metadata metadata;
  metadata.descriptor = RepeatedListValueWrapper_descriptor_;
  metadata.reflection = RepeatedListValueWrapper_reflection_;
  return metadata;
}

#if PROTOBUF_INLINE_NOT_IN_HEADERS
// RepeatedListValueWrapper

// repeated .google.protobuf.ListValue dimensions = 1;
int RepeatedListValueWrapper::dimensions_size() const {
  return dimensions_.size();
}
void RepeatedListValueWrapper::clear_dimensions() {
  dimensions_.Clear();
}
const ::google::protobuf::ListValue& RepeatedListValueWrapper::dimensions(int index) const {
  // @@protoc_insertion_point(field_get:google.protobuf.testing.RepeatedListValueWrapper.dimensions)
  return dimensions_.Get(index);
}
::google::protobuf::ListValue* RepeatedListValueWrapper::mutable_dimensions(int index) {
  // @@protoc_insertion_point(field_mutable:google.protobuf.testing.RepeatedListValueWrapper.dimensions)
  return dimensions_.Mutable(index);
}
::google::protobuf::ListValue* RepeatedListValueWrapper::add_dimensions() {
  // @@protoc_insertion_point(field_add:google.protobuf.testing.RepeatedListValueWrapper.dimensions)
  return dimensions_.Add();
}
::google::protobuf::RepeatedPtrField< ::google::protobuf::ListValue >*
RepeatedListValueWrapper::mutable_dimensions() {
  // @@protoc_insertion_point(field_mutable_list:google.protobuf.testing.RepeatedListValueWrapper.dimensions)
  return &dimensions_;
}
const ::google::protobuf::RepeatedPtrField< ::google::protobuf::ListValue >&
RepeatedListValueWrapper::dimensions() const {
  // @@protoc_insertion_point(field_list:google.protobuf.testing.RepeatedListValueWrapper.dimensions)
  return dimensions_;
}

inline const RepeatedListValueWrapper* RepeatedListValueWrapper::internal_default_instance() {
  return &RepeatedListValueWrapper_default_instance_.get();
}
#endif  // PROTOBUF_INLINE_NOT_IN_HEADERS

// ===================================================================

#if !defined(_MSC_VER) || _MSC_VER >= 1900
const int MapOfStruct::kStructMapFieldNumber;
const int MapOfStruct::kValueMapFieldNumber;
const int MapOfStruct::kListvalueMapFieldNumber;
#endif  // !defined(_MSC_VER) || _MSC_VER >= 1900

MapOfStruct::MapOfStruct()
  : ::google::protobuf::Message(), _internal_metadata_(NULL) {
  if (this != internal_default_instance()) protobuf_InitDefaults_google_2fprotobuf_2futil_2finternal_2ftestdata_2fstruct_2eproto();
  SharedCtor();
  // @@protoc_insertion_point(constructor:google.protobuf.testing.MapOfStruct)
}

void MapOfStruct::InitAsDefaultInstance() {
}

MapOfStruct::MapOfStruct(const MapOfStruct& from)
  : ::google::protobuf::Message(),
    _internal_metadata_(NULL) {
  SharedCtor();
  UnsafeMergeFrom(from);
  // @@protoc_insertion_point(copy_constructor:google.protobuf.testing.MapOfStruct)
}

void MapOfStruct::SharedCtor() {
  struct_map_.SetAssignDescriptorCallback(
      protobuf_AssignDescriptorsOnce);
  struct_map_.SetEntryDescriptor(
      &::google::protobuf::testing::MapOfStruct_StructMapEntry_descriptor_);
  value_map_.SetAssignDescriptorCallback(
      protobuf_AssignDescriptorsOnce);
  value_map_.SetEntryDescriptor(
      &::google::protobuf::testing::MapOfStruct_ValueMapEntry_descriptor_);
  listvalue_map_.SetAssignDescriptorCallback(
      protobuf_AssignDescriptorsOnce);
  listvalue_map_.SetEntryDescriptor(
      &::google::protobuf::testing::MapOfStruct_ListvalueMapEntry_descriptor_);
  _cached_size_ = 0;
}

MapOfStruct::~MapOfStruct() {
  // @@protoc_insertion_point(destructor:google.protobuf.testing.MapOfStruct)
  SharedDtor();
}

void MapOfStruct::SharedDtor() {
}

void MapOfStruct::SetCachedSize(int size) const {
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
}
const ::google::protobuf::Descriptor* MapOfStruct::descriptor() {
  protobuf_AssignDescriptorsOnce();
  return MapOfStruct_descriptor_;
}

const MapOfStruct& MapOfStruct::default_instance() {
  protobuf_InitDefaults_google_2fprotobuf_2futil_2finternal_2ftestdata_2fstruct_2eproto();
  return *internal_default_instance();
}

::google::protobuf::internal::ExplicitlyConstructed<MapOfStruct> MapOfStruct_default_instance_;

MapOfStruct* MapOfStruct::New(::google::protobuf::Arena* arena) const {
  MapOfStruct* n = new MapOfStruct;
  if (arena != NULL) {
    arena->Own(n);
  }
  return n;
}

void MapOfStruct::Clear() {
// @@protoc_insertion_point(message_clear_start:google.protobuf.testing.MapOfStruct)
  struct_map_.Clear();
  value_map_.Clear();
  listvalue_map_.Clear();
}

bool MapOfStruct::MergePartialFromCodedStream(
    ::google::protobuf::io::CodedInputStream* input) {
#define DO_(EXPRESSION) if (!GOOGLE_PREDICT_TRUE(EXPRESSION)) goto failure
  ::google::protobuf::uint32 tag;
  // @@protoc_insertion_point(parse_start:google.protobuf.testing.MapOfStruct)
  for (;;) {
    ::std::pair< ::google::protobuf::uint32, bool> p = input->ReadTagWithCutoff(127);
    tag = p.first;
    if (!p.second) goto handle_unusual;
    switch (::google::protobuf::internal::WireFormatLite::GetTagFieldNumber(tag)) {
      // map<string, .google.protobuf.Struct> struct_map = 1;
      case 1: {
        if (tag == 10) {
          DO_(input->IncrementRecursionDepth());
         parse_loop_struct_map:
          MapOfStruct_StructMapEntry::Parser< ::google::protobuf::internal::MapField<
              ::std::string, ::google::protobuf::Struct,
              ::google::protobuf::internal::WireFormatLite::TYPE_STRING,
              ::google::protobuf::internal::WireFormatLite::TYPE_MESSAGE,
              0 >,
            ::google::protobuf::Map< ::std::string, ::google::protobuf::Struct > > parser(&struct_map_);
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessageNoVirtual(
              input, &parser));
          DO_(::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
            parser.key().data(), parser.key().length(),
            ::google::protobuf::internal::WireFormatLite::PARSE,
            "google.protobuf.testing.MapOfStruct.StructMapEntry.key"));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(10)) goto parse_loop_struct_map;
        if (input->ExpectTag(18)) goto parse_loop_value_map;
        input->UnsafeDecrementRecursionDepth();
        break;
      }

      // map<string, .google.protobuf.Value> value_map = 2;
      case 2: {
        if (tag == 18) {
          DO_(input->IncrementRecursionDepth());
         parse_loop_value_map:
          MapOfStruct_ValueMapEntry::Parser< ::google::protobuf::internal::MapField<
              ::std::string, ::google::protobuf::Value,
              ::google::protobuf::internal::WireFormatLite::TYPE_STRING,
              ::google::protobuf::internal::WireFormatLite::TYPE_MESSAGE,
              0 >,
            ::google::protobuf::Map< ::std::string, ::google::protobuf::Value > > parser(&value_map_);
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessageNoVirtual(
              input, &parser));
          DO_(::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
            parser.key().data(), parser.key().length(),
            ::google::protobuf::internal::WireFormatLite::PARSE,
            "google.protobuf.testing.MapOfStruct.ValueMapEntry.key"));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(18)) goto parse_loop_value_map;
        if (input->ExpectTag(26)) goto parse_loop_listvalue_map;
        input->UnsafeDecrementRecursionDepth();
        break;
      }

      // map<string, .google.protobuf.ListValue> listvalue_map = 3;
      case 3: {
        if (tag == 26) {
          DO_(input->IncrementRecursionDepth());
         parse_loop_listvalue_map:
          MapOfStruct_ListvalueMapEntry::Parser< ::google::protobuf::internal::MapField<
              ::std::string, ::google::protobuf::ListValue,
              ::google::protobuf::internal::WireFormatLite::TYPE_STRING,
              ::google::protobuf::internal::WireFormatLite::TYPE_MESSAGE,
              0 >,
            ::google::protobuf::Map< ::std::string, ::google::protobuf::ListValue > > parser(&listvalue_map_);
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessageNoVirtual(
              input, &parser));
          DO_(::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
            parser.key().data(), parser.key().length(),
            ::google::protobuf::internal::WireFormatLite::PARSE,
            "google.protobuf.testing.MapOfStruct.ListvalueMapEntry.key"));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(26)) goto parse_loop_listvalue_map;
        input->UnsafeDecrementRecursionDepth();
        if (input->ExpectAtEnd()) goto success;
        break;
      }

      default: {
      handle_unusual:
        if (tag == 0 ||
            ::google::protobuf::internal::WireFormatLite::GetTagWireType(tag) ==
            ::google::protobuf::internal::WireFormatLite::WIRETYPE_END_GROUP) {
          goto success;
        }
        DO_(::google::protobuf::internal::WireFormatLite::SkipField(input, tag));
        break;
      }
    }
  }
success:
  // @@protoc_insertion_point(parse_success:google.protobuf.testing.MapOfStruct)
  return true;
failure:
  // @@protoc_insertion_point(parse_failure:google.protobuf.testing.MapOfStruct)
  return false;
#undef DO_
}

void MapOfStruct::SerializeWithCachedSizes(
    ::google::protobuf::io::CodedOutputStream* output) const {
  // @@protoc_insertion_point(serialize_start:google.protobuf.testing.MapOfStruct)
  // map<string, .google.protobuf.Struct> struct_map = 1;
  if (!this->struct_map().empty()) {
    typedef ::google::protobuf::Map< ::std::string, ::google::protobuf::Struct >::const_pointer
        ConstPtr;
    typedef ConstPtr SortItem;
    typedef ::google::protobuf::internal::CompareByDerefFirst<SortItem> Less;
    struct Utf8Check {
      static void Check(ConstPtr p) {
        ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
          p->first.data(), p->first.length(),
          ::google::protobuf::internal::WireFormatLite::SERIALIZE,
          "google.protobuf.testing.MapOfStruct.StructMapEntry.key");
      }
    };

    if (output->IsSerializationDeterminstic() &&
        this->struct_map().size() > 1) {
      ::google::protobuf::scoped_array<SortItem> items(
          new SortItem[this->struct_map().size()]);
      typedef ::google::protobuf::Map< ::std::string, ::google::protobuf::Struct >::size_type size_type;
      size_type n = 0;
      for (::google::protobuf::Map< ::std::string, ::google::protobuf::Struct >::const_iterator
          it = this->struct_map().begin();
          it != this->struct_map().end(); ++it, ++n) {
        items[n] = SortItem(&*it);
      }
      ::std::sort(&items[0], &items[n], Less());
      ::google::protobuf::scoped_ptr<MapOfStruct_StructMapEntry> entry;
      for (size_type i = 0; i < n; i++) {
        entry.reset(struct_map_.NewEntryWrapper(
            items[i]->first, items[i]->second));
        ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
            1, *entry, output);
        Utf8Check::Check(items[i]);
      }
    } else {
      ::google::protobuf::scoped_ptr<MapOfStruct_StructMapEntry> entry;
      for (::google::protobuf::Map< ::std::string, ::google::protobuf::Struct >::const_iterator
          it = this->struct_map().begin();
          it != this->struct_map().end(); ++it) {
        entry.reset(struct_map_.NewEntryWrapper(
            it->first, it->second));
        ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
            1, *entry, output);
        Utf8Check::Check(&*it);
      }
    }
  }

  // map<string, .google.protobuf.Value> value_map = 2;
  if (!this->value_map().empty()) {
    typedef ::google::protobuf::Map< ::std::string, ::google::protobuf::Value >::const_pointer
        ConstPtr;
    typedef ConstPtr SortItem;
    typedef ::google::protobuf::internal::CompareByDerefFirst<SortItem> Less;
    struct Utf8Check {
      static void Check(ConstPtr p) {
        ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
          p->first.data(), p->first.length(),
          ::google::protobuf::internal::WireFormatLite::SERIALIZE,
          "google.protobuf.testing.MapOfStruct.ValueMapEntry.key");
      }
    };

    if (output->IsSerializationDeterminstic() &&
        this->value_map().size() > 1) {
      ::google::protobuf::scoped_array<SortItem> items(
          new SortItem[this->value_map().size()]);
      typedef ::google::protobuf::Map< ::std::string, ::google::protobuf::Value >::size_type size_type;
      size_type n = 0;
      for (::google::protobuf::Map< ::std::string, ::google::protobuf::Value >::const_iterator
          it = this->value_map().begin();
          it != this->value_map().end(); ++it, ++n) {
        items[n] = SortItem(&*it);
      }
      ::std::sort(&items[0], &items[n], Less());
      ::google::protobuf::scoped_ptr<MapOfStruct_ValueMapEntry> entry;
      for (size_type i = 0; i < n; i++) {
        entry.reset(value_map_.NewEntryWrapper(
            items[i]->first, items[i]->second));
        ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
            2, *entry, output);
        Utf8Check::Check(items[i]);
      }
    } else {
      ::google::protobuf::scoped_ptr<MapOfStruct_ValueMapEntry> entry;
      for (::google::protobuf::Map< ::std::string, ::google::protobuf::Value >::const_iterator
          it = this->value_map().begin();
          it != this->value_map().end(); ++it) {
        entry.reset(value_map_.NewEntryWrapper(
            it->first, it->second));
        ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
            2, *entry, output);
        Utf8Check::Check(&*it);
      }
    }
  }

  // map<string, .google.protobuf.ListValue> listvalue_map = 3;
  if (!this->listvalue_map().empty()) {
    typedef ::google::protobuf::Map< ::std::string, ::google::protobuf::ListValue >::const_pointer
        ConstPtr;
    typedef ConstPtr SortItem;
    typedef ::google::protobuf::internal::CompareByDerefFirst<SortItem> Less;
    struct Utf8Check {
      static void Check(ConstPtr p) {
        ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
          p->first.data(), p->first.length(),
          ::google::protobuf::internal::WireFormatLite::SERIALIZE,
          "google.protobuf.testing.MapOfStruct.ListvalueMapEntry.key");
      }
    };

    if (output->IsSerializationDeterminstic() &&
        this->listvalue_map().size() > 1) {
      ::google::protobuf::scoped_array<SortItem> items(
          new SortItem[this->listvalue_map().size()]);
      typedef ::google::protobuf::Map< ::std::string, ::google::protobuf::ListValue >::size_type size_type;
      size_type n = 0;
      for (::google::protobuf::Map< ::std::string, ::google::protobuf::ListValue >::const_iterator
          it = this->listvalue_map().begin();
          it != this->listvalue_map().end(); ++it, ++n) {
        items[n] = SortItem(&*it);
      }
      ::std::sort(&items[0], &items[n], Less());
      ::google::protobuf::scoped_ptr<MapOfStruct_ListvalueMapEntry> entry;
      for (size_type i = 0; i < n; i++) {
        entry.reset(listvalue_map_.NewEntryWrapper(
            items[i]->first, items[i]->second));
        ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
            3, *entry, output);
        Utf8Check::Check(items[i]);
      }
    } else {
      ::google::protobuf::scoped_ptr<MapOfStruct_ListvalueMapEntry> entry;
      for (::google::protobuf::Map< ::std::string, ::google::protobuf::ListValue >::const_iterator
          it = this->listvalue_map().begin();
          it != this->listvalue_map().end(); ++it) {
        entry.reset(listvalue_map_.NewEntryWrapper(
            it->first, it->second));
        ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
            3, *entry, output);
        Utf8Check::Check(&*it);
      }
    }
  }

  // @@protoc_insertion_point(serialize_end:google.protobuf.testing.MapOfStruct)
}

::google::protobuf::uint8* MapOfStruct::InternalSerializeWithCachedSizesToArray(
    bool deterministic, ::google::protobuf::uint8* target) const {
  (void)deterministic; // Unused
  // @@protoc_insertion_point(serialize_to_array_start:google.protobuf.testing.MapOfStruct)
  // map<string, .google.protobuf.Struct> struct_map = 1;
  if (!this->struct_map().empty()) {
    typedef ::google::protobuf::Map< ::std::string, ::google::protobuf::Struct >::const_pointer
        ConstPtr;
    typedef ConstPtr SortItem;
    typedef ::google::protobuf::internal::CompareByDerefFirst<SortItem> Less;
    struct Utf8Check {
      static void Check(ConstPtr p) {
        ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
          p->first.data(), p->first.length(),
          ::google::protobuf::internal::WireFormatLite::SERIALIZE,
          "google.protobuf.testing.MapOfStruct.StructMapEntry.key");
      }
    };

    if (deterministic &&
        this->struct_map().size() > 1) {
      ::google::protobuf::scoped_array<SortItem> items(
          new SortItem[this->struct_map().size()]);
      typedef ::google::protobuf::Map< ::std::string, ::google::protobuf::Struct >::size_type size_type;
      size_type n = 0;
      for (::google::protobuf::Map< ::std::string, ::google::protobuf::Struct >::const_iterator
          it = this->struct_map().begin();
          it != this->struct_map().end(); ++it, ++n) {
        items[n] = SortItem(&*it);
      }
      ::std::sort(&items[0], &items[n], Less());
      ::google::protobuf::scoped_ptr<MapOfStruct_StructMapEntry> entry;
      for (size_type i = 0; i < n; i++) {
        entry.reset(struct_map_.NewEntryWrapper(
            items[i]->first, items[i]->second));
        target = ::google::protobuf::internal::WireFormatLite::
                   InternalWriteMessageNoVirtualToArray(
                       1, *entry, deterministic, target);
;
        Utf8Check::Check(items[i]);
      }
    } else {
      ::google::protobuf::scoped_ptr<MapOfStruct_StructMapEntry> entry;
      for (::google::protobuf::Map< ::std::string, ::google::protobuf::Struct >::const_iterator
          it = this->struct_map().begin();
          it != this->struct_map().end(); ++it) {
        entry.reset(struct_map_.NewEntryWrapper(
            it->first, it->second));
        target = ::google::protobuf::internal::WireFormatLite::
                   InternalWriteMessageNoVirtualToArray(
                       1, *entry, deterministic, target);
;
        Utf8Check::Check(&*it);
      }
    }
  }

  // map<string, .google.protobuf.Value> value_map = 2;
  if (!this->value_map().empty()) {
    typedef ::google::protobuf::Map< ::std::string, ::google::protobuf::Value >::const_pointer
        ConstPtr;
    typedef ConstPtr SortItem;
    typedef ::google::protobuf::internal::CompareByDerefFirst<SortItem> Less;
    struct Utf8Check {
      static void Check(ConstPtr p) {
        ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
          p->first.data(), p->first.length(),
          ::google::protobuf::internal::WireFormatLite::SERIALIZE,
          "google.protobuf.testing.MapOfStruct.ValueMapEntry.key");
      }
    };

    if (deterministic &&
        this->value_map().size() > 1) {
      ::google::protobuf::scoped_array<SortItem> items(
          new SortItem[this->value_map().size()]);
      typedef ::google::protobuf::Map< ::std::string, ::google::protobuf::Value >::size_type size_type;
      size_type n = 0;
      for (::google::protobuf::Map< ::std::string, ::google::protobuf::Value >::const_iterator
          it = this->value_map().begin();
          it != this->value_map().end(); ++it, ++n) {
        items[n] = SortItem(&*it);
      }
      ::std::sort(&items[0], &items[n], Less());
      ::google::protobuf::scoped_ptr<MapOfStruct_ValueMapEntry> entry;
      for (size_type i = 0; i < n; i++) {
        entry.reset(value_map_.NewEntryWrapper(
            items[i]->first, items[i]->second));
        target = ::google::protobuf::internal::WireFormatLite::
                   InternalWriteMessageNoVirtualToArray(
                       2, *entry, deterministic, target);
;
        Utf8Check::Check(items[i]);
      }
    } else {
      ::google::protobuf::scoped_ptr<MapOfStruct_ValueMapEntry> entry;
      for (::google::protobuf::Map< ::std::string, ::google::protobuf::Value >::const_iterator
          it = this->value_map().begin();
          it != this->value_map().end(); ++it) {
        entry.reset(value_map_.NewEntryWrapper(
            it->first, it->second));
        target = ::google::protobuf::internal::WireFormatLite::
                   InternalWriteMessageNoVirtualToArray(
                       2, *entry, deterministic, target);
;
        Utf8Check::Check(&*it);
      }
    }
  }

  // map<string, .google.protobuf.ListValue> listvalue_map = 3;
  if (!this->listvalue_map().empty()) {
    typedef ::google::protobuf::Map< ::std::string, ::google::protobuf::ListValue >::const_pointer
        ConstPtr;
    typedef ConstPtr SortItem;
    typedef ::google::protobuf::internal::CompareByDerefFirst<SortItem> Less;
    struct Utf8Check {
      static void Check(ConstPtr p) {
        ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
          p->first.data(), p->first.length(),
          ::google::protobuf::internal::WireFormatLite::SERIALIZE,
          "google.protobuf.testing.MapOfStruct.ListvalueMapEntry.key");
      }
    };

    if (deterministic &&
        this->listvalue_map().size() > 1) {
      ::google::protobuf::scoped_array<SortItem> items(
          new SortItem[this->listvalue_map().size()]);
      typedef ::google::protobuf::Map< ::std::string, ::google::protobuf::ListValue >::size_type size_type;
      size_type n = 0;
      for (::google::protobuf::Map< ::std::string, ::google::protobuf::ListValue >::const_iterator
          it = this->listvalue_map().begin();
          it != this->listvalue_map().end(); ++it, ++n) {
        items[n] = SortItem(&*it);
      }
      ::std::sort(&items[0], &items[n], Less());
      ::google::protobuf::scoped_ptr<MapOfStruct_ListvalueMapEntry> entry;
      for (size_type i = 0; i < n; i++) {
        entry.reset(listvalue_map_.NewEntryWrapper(
            items[i]->first, items[i]->second));
        target = ::google::protobuf::internal::WireFormatLite::
                   InternalWriteMessageNoVirtualToArray(
                       3, *entry, deterministic, target);
;
        Utf8Check::Check(items[i]);
      }
    } else {
      ::google::protobuf::scoped_ptr<MapOfStruct_ListvalueMapEntry> entry;
      for (::google::protobuf::Map< ::std::string, ::google::protobuf::ListValue >::const_iterator
          it = this->listvalue_map().begin();
          it != this->listvalue_map().end(); ++it) {
        entry.reset(listvalue_map_.NewEntryWrapper(
            it->first, it->second));
        target = ::google::protobuf::internal::WireFormatLite::
                   InternalWriteMessageNoVirtualToArray(
                       3, *entry, deterministic, target);
;
        Utf8Check::Check(&*it);
      }
    }
  }

  // @@protoc_insertion_point(serialize_to_array_end:google.protobuf.testing.MapOfStruct)
  return target;
}

size_t MapOfStruct::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:google.protobuf.testing.MapOfStruct)
  size_t total_size = 0;

  // map<string, .google.protobuf.Struct> struct_map = 1;
  total_size += 1 *
      ::google::protobuf::internal::FromIntSize(this->struct_map_size());
  {
    ::google::protobuf::scoped_ptr<MapOfStruct_StructMapEntry> entry;
    for (::google::protobuf::Map< ::std::string, ::google::protobuf::Struct >::const_iterator
        it = this->struct_map().begin();
        it != this->struct_map().end(); ++it) {
      entry.reset(struct_map_.NewEntryWrapper(it->first, it->second));
      total_size += ::google::protobuf::internal::WireFormatLite::
          MessageSizeNoVirtual(*entry);
    }
  }

  // map<string, .google.protobuf.Value> value_map = 2;
  total_size += 1 *
      ::google::protobuf::internal::FromIntSize(this->value_map_size());
  {
    ::google::protobuf::scoped_ptr<MapOfStruct_ValueMapEntry> entry;
    for (::google::protobuf::Map< ::std::string, ::google::protobuf::Value >::const_iterator
        it = this->value_map().begin();
        it != this->value_map().end(); ++it) {
      entry.reset(value_map_.NewEntryWrapper(it->first, it->second));
      total_size += ::google::protobuf::internal::WireFormatLite::
          MessageSizeNoVirtual(*entry);
    }
  }

  // map<string, .google.protobuf.ListValue> listvalue_map = 3;
  total_size += 1 *
      ::google::protobuf::internal::FromIntSize(this->listvalue_map_size());
  {
    ::google::protobuf::scoped_ptr<MapOfStruct_ListvalueMapEntry> entry;
    for (::google::protobuf::Map< ::std::string, ::google::protobuf::ListValue >::const_iterator
        it = this->listvalue_map().begin();
        it != this->listvalue_map().end(); ++it) {
      entry.reset(listvalue_map_.NewEntryWrapper(it->first, it->second));
      total_size += ::google::protobuf::internal::WireFormatLite::
          MessageSizeNoVirtual(*entry);
    }
  }

  int cached_size = ::google::protobuf::internal::ToCachedSize(total_size);
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = cached_size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
  return total_size;
}

void MapOfStruct::MergeFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_merge_from_start:google.protobuf.testing.MapOfStruct)
  if (GOOGLE_PREDICT_FALSE(&from == this)) MergeFromFail(__LINE__);
  const MapOfStruct* source =
      ::google::protobuf::internal::DynamicCastToGenerated<const MapOfStruct>(
          &from);
  if (source == NULL) {
  // @@protoc_insertion_point(generalized_merge_from_cast_fail:google.protobuf.testing.MapOfStruct)
    ::google::protobuf::internal::ReflectionOps::Merge(from, this);
  } else {
  // @@protoc_insertion_point(generalized_merge_from_cast_success:google.protobuf.testing.MapOfStruct)
    UnsafeMergeFrom(*source);
  }
}

void MapOfStruct::MergeFrom(const MapOfStruct& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:google.protobuf.testing.MapOfStruct)
  if (GOOGLE_PREDICT_TRUE(&from != this)) {
    UnsafeMergeFrom(from);
  } else {
    MergeFromFail(__LINE__);
  }
}

void MapOfStruct::UnsafeMergeFrom(const MapOfStruct& from) {
  GOOGLE_DCHECK(&from != this);
  struct_map_.MergeFrom(from.struct_map_);
  value_map_.MergeFrom(from.value_map_);
  listvalue_map_.MergeFrom(from.listvalue_map_);
}

void MapOfStruct::CopyFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_copy_from_start:google.protobuf.testing.MapOfStruct)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

void MapOfStruct::CopyFrom(const MapOfStruct& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:google.protobuf.testing.MapOfStruct)
  if (&from == this) return;
  Clear();
  UnsafeMergeFrom(from);
}

bool MapOfStruct::IsInitialized() const {

  return true;
}

void MapOfStruct::Swap(MapOfStruct* other) {
  if (other == this) return;
  InternalSwap(other);
}
void MapOfStruct::InternalSwap(MapOfStruct* other) {
  struct_map_.Swap(&other->struct_map_);
  value_map_.Swap(&other->value_map_);
  listvalue_map_.Swap(&other->listvalue_map_);
  _internal_metadata_.Swap(&other->_internal_metadata_);
  std::swap(_cached_size_, other->_cached_size_);
}

::google::protobuf::Metadata MapOfStruct::GetMetadata() const {
  protobuf_AssignDescriptorsOnce();
  ::google::protobuf::Metadata metadata;
  metadata.descriptor = MapOfStruct_descriptor_;
  metadata.reflection = MapOfStruct_reflection_;
  return metadata;
}

#if PROTOBUF_INLINE_NOT_IN_HEADERS
// MapOfStruct

// map<string, .google.protobuf.Struct> struct_map = 1;
int MapOfStruct::struct_map_size() const {
  return struct_map_.size();
}
void MapOfStruct::clear_struct_map() {
  struct_map_.Clear();
}
 const ::google::protobuf::Map< ::std::string, ::google::protobuf::Struct >&
MapOfStruct::struct_map() const {
  // @@protoc_insertion_point(field_map:google.protobuf.testing.MapOfStruct.struct_map)
  return struct_map_.GetMap();
}
 ::google::protobuf::Map< ::std::string, ::google::protobuf::Struct >*
MapOfStruct::mutable_struct_map() {
  // @@protoc_insertion_point(field_mutable_map:google.protobuf.testing.MapOfStruct.struct_map)
  return struct_map_.MutableMap();
}

// map<string, .google.protobuf.Value> value_map = 2;
int MapOfStruct::value_map_size() const {
  return value_map_.size();
}
void MapOfStruct::clear_value_map() {
  value_map_.Clear();
}
 const ::google::protobuf::Map< ::std::string, ::google::protobuf::Value >&
MapOfStruct::value_map() const {
  // @@protoc_insertion_point(field_map:google.protobuf.testing.MapOfStruct.value_map)
  return value_map_.GetMap();
}
 ::google::protobuf::Map< ::std::string, ::google::protobuf::Value >*
MapOfStruct::mutable_value_map() {
  // @@protoc_insertion_point(field_mutable_map:google.protobuf.testing.MapOfStruct.value_map)
  return value_map_.MutableMap();
}

// map<string, .google.protobuf.ListValue> listvalue_map = 3;
int MapOfStruct::listvalue_map_size() const {
  return listvalue_map_.size();
}
void MapOfStruct::clear_listvalue_map() {
  listvalue_map_.Clear();
}
 const ::google::protobuf::Map< ::std::string, ::google::protobuf::ListValue >&
MapOfStruct::listvalue_map() const {
  // @@protoc_insertion_point(field_map:google.protobuf.testing.MapOfStruct.listvalue_map)
  return listvalue_map_.GetMap();
}
 ::google::protobuf::Map< ::std::string, ::google::protobuf::ListValue >*
MapOfStruct::mutable_listvalue_map() {
  // @@protoc_insertion_point(field_mutable_map:google.protobuf.testing.MapOfStruct.listvalue_map)
  return listvalue_map_.MutableMap();
}

inline const MapOfStruct* MapOfStruct::internal_default_instance() {
  return &MapOfStruct_default_instance_.get();
}
#endif  // PROTOBUF_INLINE_NOT_IN_HEADERS

// ===================================================================

#if !defined(_MSC_VER) || _MSC_VER >= 1900
const int Dummy::kTextFieldNumber;
#endif  // !defined(_MSC_VER) || _MSC_VER >= 1900

Dummy::Dummy()
  : ::google::protobuf::Message(), _internal_metadata_(NULL) {
  if (this != internal_default_instance()) protobuf_InitDefaults_google_2fprotobuf_2futil_2finternal_2ftestdata_2fstruct_2eproto();
  SharedCtor();
  // @@protoc_insertion_point(constructor:google.protobuf.testing.Dummy)
}

void Dummy::InitAsDefaultInstance() {
}

Dummy::Dummy(const Dummy& from)
  : ::google::protobuf::Message(),
    _internal_metadata_(NULL) {
  SharedCtor();
  UnsafeMergeFrom(from);
  // @@protoc_insertion_point(copy_constructor:google.protobuf.testing.Dummy)
}

void Dummy::SharedCtor() {
  text_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  _cached_size_ = 0;
}

Dummy::~Dummy() {
  // @@protoc_insertion_point(destructor:google.protobuf.testing.Dummy)
  SharedDtor();
}

void Dummy::SharedDtor() {
  text_.DestroyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}

void Dummy::SetCachedSize(int size) const {
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
}
const ::google::protobuf::Descriptor* Dummy::descriptor() {
  protobuf_AssignDescriptorsOnce();
  return Dummy_descriptor_;
}

const Dummy& Dummy::default_instance() {
  protobuf_InitDefaults_google_2fprotobuf_2futil_2finternal_2ftestdata_2fstruct_2eproto();
  return *internal_default_instance();
}

::google::protobuf::internal::ExplicitlyConstructed<Dummy> Dummy_default_instance_;

Dummy* Dummy::New(::google::protobuf::Arena* arena) const {
  Dummy* n = new Dummy;
  if (arena != NULL) {
    arena->Own(n);
  }
  return n;
}

void Dummy::Clear() {
// @@protoc_insertion_point(message_clear_start:google.protobuf.testing.Dummy)
  text_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}

bool Dummy::MergePartialFromCodedStream(
    ::google::protobuf::io::CodedInputStream* input) {
#define DO_(EXPRESSION) if (!GOOGLE_PREDICT_TRUE(EXPRESSION)) goto failure
  ::google::protobuf::uint32 tag;
  // @@protoc_insertion_point(parse_start:google.protobuf.testing.Dummy)
  for (;;) {
    ::std::pair< ::google::protobuf::uint32, bool> p = input->ReadTagWithCutoff(127);
    tag = p.first;
    if (!p.second) goto handle_unusual;
    switch (::google::protobuf::internal::WireFormatLite::GetTagFieldNumber(tag)) {
      // optional string text = 1;
      case 1: {
        if (tag == 10) {
          DO_(::google::protobuf::internal::WireFormatLite::ReadString(
                input, this->mutable_text()));
          DO_(::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
            this->text().data(), this->text().length(),
            ::google::protobuf::internal::WireFormatLite::PARSE,
            "google.protobuf.testing.Dummy.text"));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectAtEnd()) goto success;
        break;
      }

      default: {
      handle_unusual:
        if (tag == 0 ||
            ::google::protobuf::internal::WireFormatLite::GetTagWireType(tag) ==
            ::google::protobuf::internal::WireFormatLite::WIRETYPE_END_GROUP) {
          goto success;
        }
        DO_(::google::protobuf::internal::WireFormatLite::SkipField(input, tag));
        break;
      }
    }
  }
success:
  // @@protoc_insertion_point(parse_success:google.protobuf.testing.Dummy)
  return true;
failure:
  // @@protoc_insertion_point(parse_failure:google.protobuf.testing.Dummy)
  return false;
#undef DO_
}

void Dummy::SerializeWithCachedSizes(
    ::google::protobuf::io::CodedOutputStream* output) const {
  // @@protoc_insertion_point(serialize_start:google.protobuf.testing.Dummy)
  // optional string text = 1;
  if (this->text().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->text().data(), this->text().length(),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "google.protobuf.testing.Dummy.text");
    ::google::protobuf::internal::WireFormatLite::WriteStringMaybeAliased(
      1, this->text(), output);
  }

  // @@protoc_insertion_point(serialize_end:google.protobuf.testing.Dummy)
}

::google::protobuf::uint8* Dummy::InternalSerializeWithCachedSizesToArray(
    bool deterministic, ::google::protobuf::uint8* target) const {
  (void)deterministic; // Unused
  // @@protoc_insertion_point(serialize_to_array_start:google.protobuf.testing.Dummy)
  // optional string text = 1;
  if (this->text().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->text().data(), this->text().length(),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "google.protobuf.testing.Dummy.text");
    target =
      ::google::protobuf::internal::WireFormatLite::WriteStringToArray(
        1, this->text(), target);
  }

  // @@protoc_insertion_point(serialize_to_array_end:google.protobuf.testing.Dummy)
  return target;
}

size_t Dummy::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:google.protobuf.testing.Dummy)
  size_t total_size = 0;

  // optional string text = 1;
  if (this->text().size() > 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::StringSize(
        this->text());
  }

  int cached_size = ::google::protobuf::internal::ToCachedSize(total_size);
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = cached_size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
  return total_size;
}

void Dummy::MergeFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_merge_from_start:google.protobuf.testing.Dummy)
  if (GOOGLE_PREDICT_FALSE(&from == this)) MergeFromFail(__LINE__);
  const Dummy* source =
      ::google::protobuf::internal::DynamicCastToGenerated<const Dummy>(
          &from);
  if (source == NULL) {
  // @@protoc_insertion_point(generalized_merge_from_cast_fail:google.protobuf.testing.Dummy)
    ::google::protobuf::internal::ReflectionOps::Merge(from, this);
  } else {
  // @@protoc_insertion_point(generalized_merge_from_cast_success:google.protobuf.testing.Dummy)
    UnsafeMergeFrom(*source);
  }
}

void Dummy::MergeFrom(const Dummy& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:google.protobuf.testing.Dummy)
  if (GOOGLE_PREDICT_TRUE(&from != this)) {
    UnsafeMergeFrom(from);
  } else {
    MergeFromFail(__LINE__);
  }
}

void Dummy::UnsafeMergeFrom(const Dummy& from) {
  GOOGLE_DCHECK(&from != this);
  if (from.text().size() > 0) {

    text_.AssignWithDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), from.text_);
  }
}

void Dummy::CopyFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_copy_from_start:google.protobuf.testing.Dummy)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

void Dummy::CopyFrom(const Dummy& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:google.protobuf.testing.Dummy)
  if (&from == this) return;
  Clear();
  UnsafeMergeFrom(from);
}

bool Dummy::IsInitialized() const {

  return true;
}

void Dummy::Swap(Dummy* other) {
  if (other == this) return;
  InternalSwap(other);
}
void Dummy::InternalSwap(Dummy* other) {
  text_.Swap(&other->text_);
  _internal_metadata_.Swap(&other->_internal_metadata_);
  std::swap(_cached_size_, other->_cached_size_);
}

::google::protobuf::Metadata Dummy::GetMetadata() const {
  protobuf_AssignDescriptorsOnce();
  ::google::protobuf::Metadata metadata;
  metadata.descriptor = Dummy_descriptor_;
  metadata.reflection = Dummy_reflection_;
  return metadata;
}

#if PROTOBUF_INLINE_NOT_IN_HEADERS
// Dummy

// optional string text = 1;
void Dummy::clear_text() {
  text_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
const ::std::string& Dummy::text() const {
  // @@protoc_insertion_point(field_get:google.protobuf.testing.Dummy.text)
  return text_.GetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
void Dummy::set_text(const ::std::string& value) {
  
  text_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:google.protobuf.testing.Dummy.text)
}
void Dummy::set_text(const char* value) {
  
  text_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:google.protobuf.testing.Dummy.text)
}
void Dummy::set_text(const char* value, size_t size) {
  
  text_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:google.protobuf.testing.Dummy.text)
}
::std::string* Dummy::mutable_text() {
  
  // @@protoc_insertion_point(field_mutable:google.protobuf.testing.Dummy.text)
  return text_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
::std::string* Dummy::release_text() {
  // @@protoc_insertion_point(field_release:google.protobuf.testing.Dummy.text)
  
  return text_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
void Dummy::set_allocated_text(::std::string* text) {
  if (text != NULL) {
    
  } else {
    
  }
  text_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), text);
  // @@protoc_insertion_point(field_set_allocated:google.protobuf.testing.Dummy.text)
}

inline const Dummy* Dummy::internal_default_instance() {
  return &Dummy_default_instance_.get();
}
#endif  // PROTOBUF_INLINE_NOT_IN_HEADERS

// ===================================================================

#if !defined(_MSC_VER) || _MSC_VER >= 1900
const int StructType::kObjectFieldNumber;
#endif  // !defined(_MSC_VER) || _MSC_VER >= 1900

StructType::StructType()
  : ::google::protobuf::Message(), _internal_metadata_(NULL) {
  if (this != internal_default_instance()) protobuf_InitDefaults_google_2fprotobuf_2futil_2finternal_2ftestdata_2fstruct_2eproto();
  SharedCtor();
  // @@protoc_insertion_point(constructor:google.protobuf.testing.StructType)
}

void StructType::InitAsDefaultInstance() {
  object_ = const_cast< ::google::protobuf::Struct*>(
      ::google::protobuf::Struct::internal_default_instance());
}

StructType::StructType(const StructType& from)
  : ::google::protobuf::Message(),
    _internal_metadata_(NULL) {
  SharedCtor();
  UnsafeMergeFrom(from);
  // @@protoc_insertion_point(copy_constructor:google.protobuf.testing.StructType)
}

void StructType::SharedCtor() {
  object_ = NULL;
  _cached_size_ = 0;
}

StructType::~StructType() {
  // @@protoc_insertion_point(destructor:google.protobuf.testing.StructType)
  SharedDtor();
}

void StructType::SharedDtor() {
  if (this != &StructType_default_instance_.get()) {
    delete object_;
  }
}

void StructType::SetCachedSize(int size) const {
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
}
const ::google::protobuf::Descriptor* StructType::descriptor() {
  protobuf_AssignDescriptorsOnce();
  return StructType_descriptor_;
}

const StructType& StructType::default_instance() {
  protobuf_InitDefaults_google_2fprotobuf_2futil_2finternal_2ftestdata_2fstruct_2eproto();
  return *internal_default_instance();
}

::google::protobuf::internal::ExplicitlyConstructed<StructType> StructType_default_instance_;

StructType* StructType::New(::google::protobuf::Arena* arena) const {
  StructType* n = new StructType;
  if (arena != NULL) {
    arena->Own(n);
  }
  return n;
}

void StructType::Clear() {
// @@protoc_insertion_point(message_clear_start:google.protobuf.testing.StructType)
  if (GetArenaNoVirtual() == NULL && object_ != NULL) delete object_;
  object_ = NULL;
}

bool StructType::MergePartialFromCodedStream(
    ::google::protobuf::io::CodedInputStream* input) {
#define DO_(EXPRESSION) if (!GOOGLE_PREDICT_TRUE(EXPRESSION)) goto failure
  ::google::protobuf::uint32 tag;
  // @@protoc_insertion_point(parse_start:google.protobuf.testing.StructType)
  for (;;) {
    ::std::pair< ::google::protobuf::uint32, bool> p = input->ReadTagWithCutoff(127);
    tag = p.first;
    if (!p.second) goto handle_unusual;
    switch (::google::protobuf::internal::WireFormatLite::GetTagFieldNumber(tag)) {
      // optional .google.protobuf.Struct object = 1;
      case 1: {
        if (tag == 10) {
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessageNoVirtual(
               input, mutable_object()));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectAtEnd()) goto success;
        break;
      }

      default: {
      handle_unusual:
        if (tag == 0 ||
            ::google::protobuf::internal::WireFormatLite::GetTagWireType(tag) ==
            ::google::protobuf::internal::WireFormatLite::WIRETYPE_END_GROUP) {
          goto success;
        }
        DO_(::google::protobuf::internal::WireFormatLite::SkipField(input, tag));
        break;
      }
    }
  }
success:
  // @@protoc_insertion_point(parse_success:google.protobuf.testing.StructType)
  return true;
failure:
  // @@protoc_insertion_point(parse_failure:google.protobuf.testing.StructType)
  return false;
#undef DO_
}

void StructType::SerializeWithCachedSizes(
    ::google::protobuf::io::CodedOutputStream* output) const {
  // @@protoc_insertion_point(serialize_start:google.protobuf.testing.StructType)
  // optional .google.protobuf.Struct object = 1;
  if (this->has_object()) {
    ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
      1, *this->object_, output);
  }

  // @@protoc_insertion_point(serialize_end:google.protobuf.testing.StructType)
}

::google::protobuf::uint8* StructType::InternalSerializeWithCachedSizesToArray(
    bool deterministic, ::google::protobuf::uint8* target) const {
  (void)deterministic; // Unused
  // @@protoc_insertion_point(serialize_to_array_start:google.protobuf.testing.StructType)
  // optional .google.protobuf.Struct object = 1;
  if (this->has_object()) {
    target = ::google::protobuf::internal::WireFormatLite::
      InternalWriteMessageNoVirtualToArray(
        1, *this->object_, false, target);
  }

  // @@protoc_insertion_point(serialize_to_array_end:google.protobuf.testing.StructType)
  return target;
}

size_t StructType::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:google.protobuf.testing.StructType)
  size_t total_size = 0;

  // optional .google.protobuf.Struct object = 1;
  if (this->has_object()) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::MessageSizeNoVirtual(
        *this->object_);
  }

  int cached_size = ::google::protobuf::internal::ToCachedSize(total_size);
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = cached_size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
  return total_size;
}

void StructType::MergeFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_merge_from_start:google.protobuf.testing.StructType)
  if (GOOGLE_PREDICT_FALSE(&from == this)) MergeFromFail(__LINE__);
  const StructType* source =
      ::google::protobuf::internal::DynamicCastToGenerated<const StructType>(
          &from);
  if (source == NULL) {
  // @@protoc_insertion_point(generalized_merge_from_cast_fail:google.protobuf.testing.StructType)
    ::google::protobuf::internal::ReflectionOps::Merge(from, this);
  } else {
  // @@protoc_insertion_point(generalized_merge_from_cast_success:google.protobuf.testing.StructType)
    UnsafeMergeFrom(*source);
  }
}

void StructType::MergeFrom(const StructType& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:google.protobuf.testing.StructType)
  if (GOOGLE_PREDICT_TRUE(&from != this)) {
    UnsafeMergeFrom(from);
  } else {
    MergeFromFail(__LINE__);
  }
}

void StructType::UnsafeMergeFrom(const StructType& from) {
  GOOGLE_DCHECK(&from != this);
  if (from.has_object()) {
    mutable_object()->::google::protobuf::Struct::MergeFrom(from.object());
  }
}

void StructType::CopyFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_copy_from_start:google.protobuf.testing.StructType)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

void StructType::CopyFrom(const StructType& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:google.protobuf.testing.StructType)
  if (&from == this) return;
  Clear();
  UnsafeMergeFrom(from);
}

bool StructType::IsInitialized() const {

  return true;
}

void StructType::Swap(StructType* other) {
  if (other == this) return;
  InternalSwap(other);
}
void StructType::InternalSwap(StructType* other) {
  std::swap(object_, other->object_);
  _internal_metadata_.Swap(&other->_internal_metadata_);
  std::swap(_cached_size_, other->_cached_size_);
}

::google::protobuf::Metadata StructType::GetMetadata() const {
  protobuf_AssignDescriptorsOnce();
  ::google::protobuf::Metadata metadata;
  metadata.descriptor = StructType_descriptor_;
  metadata.reflection = StructType_reflection_;
  return metadata;
}

#if PROTOBUF_INLINE_NOT_IN_HEADERS
// StructType

// optional .google.protobuf.Struct object = 1;
bool StructType::has_object() const {
  return this != internal_default_instance() && object_ != NULL;
}
void StructType::clear_object() {
  if (GetArenaNoVirtual() == NULL && object_ != NULL) delete object_;
  object_ = NULL;
}
const ::google::protobuf::Struct& StructType::object() const {
  // @@protoc_insertion_point(field_get:google.protobuf.testing.StructType.object)
  return object_ != NULL ? *object_
                         : *::google::protobuf::Struct::internal_default_instance();
}
::google::protobuf::Struct* StructType::mutable_object() {
  
  if (object_ == NULL) {
    object_ = new ::google::protobuf::Struct;
  }
  // @@protoc_insertion_point(field_mutable:google.protobuf.testing.StructType.object)
  return object_;
}
::google::protobuf::Struct* StructType::release_object() {
  // @@protoc_insertion_point(field_release:google.protobuf.testing.StructType.object)
  
  ::google::protobuf::Struct* temp = object_;
  object_ = NULL;
  return temp;
}
void StructType::set_allocated_object(::google::protobuf::Struct* object) {
  delete object_;
  if (object != NULL && object->GetArena() != NULL) {
    ::google::protobuf::Struct* new_object = new ::google::protobuf::Struct;
    new_object->CopyFrom(*object);
    object = new_object;
  }
  object_ = object;
  if (object) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_set_allocated:google.protobuf.testing.StructType.object)
}

inline const StructType* StructType::internal_default_instance() {
  return &StructType_default_instance_.get();
}
#endif  // PROTOBUF_INLINE_NOT_IN_HEADERS

// @@protoc_insertion_point(namespace_scope)

}  // namespace testing
}  // namespace protobuf
}  // namespace google

// @@protoc_insertion_point(global_scope)

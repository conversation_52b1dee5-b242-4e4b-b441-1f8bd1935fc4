// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: google/protobuf/util/internal/testdata/timestamp_duration.proto

#define INTERNAL_SUPPRESS_PROTOBUF_FIELD_DEPRECATION
#include <google/protobuf/util/internal/testdata/timestamp_duration.pb.h>

#include <algorithm>

#include <google/protobuf/stubs/common.h>
#include <google/protobuf/stubs/port.h>
#include <google/protobuf/stubs/once.h>
#include <google/protobuf/io/coded_stream.h>
#include <google/protobuf/wire_format_lite_inl.h>
#include <google/protobuf/descriptor.h>
#include <google/protobuf/generated_message_reflection.h>
#include <google/protobuf/reflection_ops.h>
#include <google/protobuf/wire_format.h>
// @@protoc_insertion_point(includes)

namespace google {
namespace protobuf {
namespace testing {

namespace {

const ::google::protobuf::Descriptor* TimestampDurationTestCases_descriptor_ = NULL;
const ::google::protobuf::internal::GeneratedMessageReflection*
  TimestampDurationTestCases_reflection_ = NULL;
const ::google::protobuf::Descriptor* TimeStampType_descriptor_ = NULL;
const ::google::protobuf::internal::GeneratedMessageReflection*
  TimeStampType_reflection_ = NULL;
const ::google::protobuf::Descriptor* DurationType_descriptor_ = NULL;
const ::google::protobuf::internal::GeneratedMessageReflection*
  DurationType_reflection_ = NULL;
const ::google::protobuf::Descriptor* TimestampDuration_descriptor_ = NULL;
const ::google::protobuf::internal::GeneratedMessageReflection*
  TimestampDuration_reflection_ = NULL;

}  // namespace


void protobuf_AssignDesc_google_2fprotobuf_2futil_2finternal_2ftestdata_2ftimestamp_5fduration_2eproto() GOOGLE_ATTRIBUTE_COLD;
void protobuf_AssignDesc_google_2fprotobuf_2futil_2finternal_2ftestdata_2ftimestamp_5fduration_2eproto() {
  protobuf_AddDesc_google_2fprotobuf_2futil_2finternal_2ftestdata_2ftimestamp_5fduration_2eproto();
  const ::google::protobuf::FileDescriptor* file =
    ::google::protobuf::DescriptorPool::generated_pool()->FindFileByName(
      "google/protobuf/util/internal/testdata/timestamp_duration.proto");
  GOOGLE_CHECK(file != NULL);
  TimestampDurationTestCases_descriptor_ = file->message_type(0);
  static const int TimestampDurationTestCases_offsets_[20] = {
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(TimestampDurationTestCases, epoch_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(TimestampDurationTestCases, epoch2_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(TimestampDurationTestCases, mintime_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(TimestampDurationTestCases, maxtime_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(TimestampDurationTestCases, timeval1_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(TimestampDurationTestCases, timeval2_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(TimestampDurationTestCases, timeval3_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(TimestampDurationTestCases, timeval4_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(TimestampDurationTestCases, timeval5_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(TimestampDurationTestCases, timeval6_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(TimestampDurationTestCases, timeval7_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(TimestampDurationTestCases, timeval8_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(TimestampDurationTestCases, zero_duration_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(TimestampDurationTestCases, min_duration_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(TimestampDurationTestCases, max_duration_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(TimestampDurationTestCases, duration1_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(TimestampDurationTestCases, duration2_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(TimestampDurationTestCases, duration3_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(TimestampDurationTestCases, duration4_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(TimestampDurationTestCases, duration5_),
  };
  TimestampDurationTestCases_reflection_ =
    ::google::protobuf::internal::GeneratedMessageReflection::NewGeneratedMessageReflection(
      TimestampDurationTestCases_descriptor_,
      TimestampDurationTestCases::internal_default_instance(),
      TimestampDurationTestCases_offsets_,
      -1,
      -1,
      -1,
      sizeof(TimestampDurationTestCases),
      GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(TimestampDurationTestCases, _internal_metadata_));
  TimeStampType_descriptor_ = file->message_type(1);
  static const int TimeStampType_offsets_[1] = {
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(TimeStampType, timestamp_),
  };
  TimeStampType_reflection_ =
    ::google::protobuf::internal::GeneratedMessageReflection::NewGeneratedMessageReflection(
      TimeStampType_descriptor_,
      TimeStampType::internal_default_instance(),
      TimeStampType_offsets_,
      -1,
      -1,
      -1,
      sizeof(TimeStampType),
      GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(TimeStampType, _internal_metadata_));
  DurationType_descriptor_ = file->message_type(2);
  static const int DurationType_offsets_[1] = {
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(DurationType, duration_),
  };
  DurationType_reflection_ =
    ::google::protobuf::internal::GeneratedMessageReflection::NewGeneratedMessageReflection(
      DurationType_descriptor_,
      DurationType::internal_default_instance(),
      DurationType_offsets_,
      -1,
      -1,
      -1,
      sizeof(DurationType),
      GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(DurationType, _internal_metadata_));
  TimestampDuration_descriptor_ = file->message_type(3);
  static const int TimestampDuration_offsets_[3] = {
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(TimestampDuration, ts_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(TimestampDuration, dur_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(TimestampDuration, rep_ts_),
  };
  TimestampDuration_reflection_ =
    ::google::protobuf::internal::GeneratedMessageReflection::NewGeneratedMessageReflection(
      TimestampDuration_descriptor_,
      TimestampDuration::internal_default_instance(),
      TimestampDuration_offsets_,
      -1,
      -1,
      -1,
      sizeof(TimestampDuration),
      GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(TimestampDuration, _internal_metadata_));
}

namespace {

GOOGLE_PROTOBUF_DECLARE_ONCE(protobuf_AssignDescriptors_once_);
void protobuf_AssignDescriptorsOnce() {
  ::google::protobuf::GoogleOnceInit(&protobuf_AssignDescriptors_once_,
                 &protobuf_AssignDesc_google_2fprotobuf_2futil_2finternal_2ftestdata_2ftimestamp_5fduration_2eproto);
}

void protobuf_RegisterTypes(const ::std::string&) GOOGLE_ATTRIBUTE_COLD;
void protobuf_RegisterTypes(const ::std::string&) {
  protobuf_AssignDescriptorsOnce();
  ::google::protobuf::MessageFactory::InternalRegisterGeneratedMessage(
      TimestampDurationTestCases_descriptor_, TimestampDurationTestCases::internal_default_instance());
  ::google::protobuf::MessageFactory::InternalRegisterGeneratedMessage(
      TimeStampType_descriptor_, TimeStampType::internal_default_instance());
  ::google::protobuf::MessageFactory::InternalRegisterGeneratedMessage(
      DurationType_descriptor_, DurationType::internal_default_instance());
  ::google::protobuf::MessageFactory::InternalRegisterGeneratedMessage(
      TimestampDuration_descriptor_, TimestampDuration::internal_default_instance());
}

}  // namespace

void protobuf_ShutdownFile_google_2fprotobuf_2futil_2finternal_2ftestdata_2ftimestamp_5fduration_2eproto() {
  TimestampDurationTestCases_default_instance_.Shutdown();
  delete TimestampDurationTestCases_reflection_;
  TimeStampType_default_instance_.Shutdown();
  delete TimeStampType_reflection_;
  DurationType_default_instance_.Shutdown();
  delete DurationType_reflection_;
  TimestampDuration_default_instance_.Shutdown();
  delete TimestampDuration_reflection_;
}

void protobuf_InitDefaults_google_2fprotobuf_2futil_2finternal_2ftestdata_2ftimestamp_5fduration_2eproto_impl() {
  GOOGLE_PROTOBUF_VERIFY_VERSION;

  ::google::protobuf::protobuf_InitDefaults_google_2fprotobuf_2ftimestamp_2eproto();
  ::google::protobuf::protobuf_InitDefaults_google_2fprotobuf_2fduration_2eproto();
  TimestampDurationTestCases_default_instance_.DefaultConstruct();
  TimeStampType_default_instance_.DefaultConstruct();
  DurationType_default_instance_.DefaultConstruct();
  TimestampDuration_default_instance_.DefaultConstruct();
  TimestampDurationTestCases_default_instance_.get_mutable()->InitAsDefaultInstance();
  TimeStampType_default_instance_.get_mutable()->InitAsDefaultInstance();
  DurationType_default_instance_.get_mutable()->InitAsDefaultInstance();
  TimestampDuration_default_instance_.get_mutable()->InitAsDefaultInstance();
}

GOOGLE_PROTOBUF_DECLARE_ONCE(protobuf_InitDefaults_google_2fprotobuf_2futil_2finternal_2ftestdata_2ftimestamp_5fduration_2eproto_once_);
void protobuf_InitDefaults_google_2fprotobuf_2futil_2finternal_2ftestdata_2ftimestamp_5fduration_2eproto() {
  ::google::protobuf::GoogleOnceInit(&protobuf_InitDefaults_google_2fprotobuf_2futil_2finternal_2ftestdata_2ftimestamp_5fduration_2eproto_once_,
                 &protobuf_InitDefaults_google_2fprotobuf_2futil_2finternal_2ftestdata_2ftimestamp_5fduration_2eproto_impl);
}
void protobuf_AddDesc_google_2fprotobuf_2futil_2finternal_2ftestdata_2ftimestamp_5fduration_2eproto_impl() {
  GOOGLE_PROTOBUF_VERIFY_VERSION;

  protobuf_InitDefaults_google_2fprotobuf_2futil_2finternal_2ftestdata_2ftimestamp_5fduration_2eproto();
  ::google::protobuf::DescriptorPool::InternalAddGeneratedFile(
    "\n\?google/protobuf/util/internal/testdata"
    "/timestamp_duration.proto\022\027google.protob"
    "uf.testing\032\037google/protobuf/timestamp.pr"
    "oto\032\036google/protobuf/duration.proto\"\217\t\n\032"
    "TimestampDurationTestCases\0225\n\005epoch\030\001 \001("
    "\0132&.google.protobuf.testing.TimeStampTyp"
    "e\0226\n\006epoch2\030\002 \001(\0132&.google.protobuf.test"
    "ing.TimeStampType\0227\n\007mintime\030\003 \001(\0132&.goo"
    "gle.protobuf.testing.TimeStampType\0227\n\007ma"
    "xtime\030\004 \001(\0132&.google.protobuf.testing.Ti"
    "meStampType\0228\n\010timeval1\030\005 \001(\0132&.google.p"
    "rotobuf.testing.TimeStampType\0228\n\010timeval"
    "2\030\006 \001(\0132&.google.protobuf.testing.TimeSt"
    "ampType\0228\n\010timeval3\030\007 \001(\0132&.google.proto"
    "buf.testing.TimeStampType\0228\n\010timeval4\030\010 "
    "\001(\0132&.google.protobuf.testing.TimeStampT"
    "ype\0228\n\010timeval5\030\t \001(\0132&.google.protobuf."
    "testing.TimeStampType\0228\n\010timeval6\030\n \001(\0132"
    "&.google.protobuf.testing.TimeStampType\022"
    "8\n\010timeval7\030\013 \001(\0132&.google.protobuf.test"
    "ing.TimeStampType\022,\n\010timeval8\030\014 \001(\0132\032.go"
    "ogle.protobuf.Timestamp\022<\n\rzero_duration"
    "\030e \001(\0132%.google.protobuf.testing.Duratio"
    "nType\022;\n\014min_duration\030f \001(\0132%.google.pro"
    "tobuf.testing.DurationType\022;\n\014max_durati"
    "on\030g \001(\0132%.google.protobuf.testing.Durat"
    "ionType\0228\n\tduration1\030h \001(\0132%.google.prot"
    "obuf.testing.DurationType\0228\n\tduration2\030i"
    " \001(\0132%.google.protobuf.testing.DurationT"
    "ype\0228\n\tduration3\030j \001(\0132%.google.protobuf"
    ".testing.DurationType\0228\n\tduration4\030k \001(\013"
    "2%.google.protobuf.testing.DurationType\022"
    ",\n\tduration5\030l \001(\0132\031.google.protobuf.Dur"
    "ation\">\n\rTimeStampType\022-\n\ttimestamp\030\001 \001("
    "\0132\032.google.protobuf.Timestamp\";\n\014Duratio"
    "nType\022+\n\010duration\030\001 \001(\0132\031.google.protobu"
    "f.Duration\"\217\001\n\021TimestampDuration\022&\n\002ts\030\001"
    " \001(\0132\032.google.protobuf.Timestamp\022&\n\003dur\030"
    "\002 \001(\0132\031.google.protobuf.Duration\022*\n\006rep_"
    "ts\030\003 \003(\0132\032.google.protobuf.Timestamp2\220\001\n"
    "\034TimestampDurationTestService\022p\n\004Call\0223."
    "google.protobuf.testing.TimestampDuratio"
    "nTestCases\0323.google.protobuf.testing.Tim"
    "estampDurationTestCasesb\006proto3", 1751);
  ::google::protobuf::MessageFactory::InternalRegisterGeneratedFile(
    "google/protobuf/util/internal/testdata/timestamp_duration.proto", &protobuf_RegisterTypes);
  ::google::protobuf::protobuf_AddDesc_google_2fprotobuf_2ftimestamp_2eproto();
  ::google::protobuf::protobuf_AddDesc_google_2fprotobuf_2fduration_2eproto();
  ::google::protobuf::internal::OnShutdown(&protobuf_ShutdownFile_google_2fprotobuf_2futil_2finternal_2ftestdata_2ftimestamp_5fduration_2eproto);
}

GOOGLE_PROTOBUF_DECLARE_ONCE(protobuf_AddDesc_google_2fprotobuf_2futil_2finternal_2ftestdata_2ftimestamp_5fduration_2eproto_once_);
void protobuf_AddDesc_google_2fprotobuf_2futil_2finternal_2ftestdata_2ftimestamp_5fduration_2eproto() {
  ::google::protobuf::GoogleOnceInit(&protobuf_AddDesc_google_2fprotobuf_2futil_2finternal_2ftestdata_2ftimestamp_5fduration_2eproto_once_,
                 &protobuf_AddDesc_google_2fprotobuf_2futil_2finternal_2ftestdata_2ftimestamp_5fduration_2eproto_impl);
}
// Force AddDescriptors() to be called at static initialization time.
struct StaticDescriptorInitializer_google_2fprotobuf_2futil_2finternal_2ftestdata_2ftimestamp_5fduration_2eproto {
  StaticDescriptorInitializer_google_2fprotobuf_2futil_2finternal_2ftestdata_2ftimestamp_5fduration_2eproto() {
    protobuf_AddDesc_google_2fprotobuf_2futil_2finternal_2ftestdata_2ftimestamp_5fduration_2eproto();
  }
} static_descriptor_initializer_google_2fprotobuf_2futil_2finternal_2ftestdata_2ftimestamp_5fduration_2eproto_;

namespace {

static void MergeFromFail(int line) GOOGLE_ATTRIBUTE_COLD GOOGLE_ATTRIBUTE_NORETURN;
static void MergeFromFail(int line) {
  ::google::protobuf::internal::MergeFromFail(__FILE__, line);
}

}  // namespace


// ===================================================================

#if !defined(_MSC_VER) || _MSC_VER >= 1900
const int TimestampDurationTestCases::kEpochFieldNumber;
const int TimestampDurationTestCases::kEpoch2FieldNumber;
const int TimestampDurationTestCases::kMintimeFieldNumber;
const int TimestampDurationTestCases::kMaxtimeFieldNumber;
const int TimestampDurationTestCases::kTimeval1FieldNumber;
const int TimestampDurationTestCases::kTimeval2FieldNumber;
const int TimestampDurationTestCases::kTimeval3FieldNumber;
const int TimestampDurationTestCases::kTimeval4FieldNumber;
const int TimestampDurationTestCases::kTimeval5FieldNumber;
const int TimestampDurationTestCases::kTimeval6FieldNumber;
const int TimestampDurationTestCases::kTimeval7FieldNumber;
const int TimestampDurationTestCases::kTimeval8FieldNumber;
const int TimestampDurationTestCases::kZeroDurationFieldNumber;
const int TimestampDurationTestCases::kMinDurationFieldNumber;
const int TimestampDurationTestCases::kMaxDurationFieldNumber;
const int TimestampDurationTestCases::kDuration1FieldNumber;
const int TimestampDurationTestCases::kDuration2FieldNumber;
const int TimestampDurationTestCases::kDuration3FieldNumber;
const int TimestampDurationTestCases::kDuration4FieldNumber;
const int TimestampDurationTestCases::kDuration5FieldNumber;
#endif  // !defined(_MSC_VER) || _MSC_VER >= 1900

TimestampDurationTestCases::TimestampDurationTestCases()
  : ::google::protobuf::Message(), _internal_metadata_(NULL) {
  if (this != internal_default_instance()) protobuf_InitDefaults_google_2fprotobuf_2futil_2finternal_2ftestdata_2ftimestamp_5fduration_2eproto();
  SharedCtor();
  // @@protoc_insertion_point(constructor:google.protobuf.testing.TimestampDurationTestCases)
}

void TimestampDurationTestCases::InitAsDefaultInstance() {
  epoch_ = const_cast< ::google::protobuf::testing::TimeStampType*>(
      ::google::protobuf::testing::TimeStampType::internal_default_instance());
  epoch2_ = const_cast< ::google::protobuf::testing::TimeStampType*>(
      ::google::protobuf::testing::TimeStampType::internal_default_instance());
  mintime_ = const_cast< ::google::protobuf::testing::TimeStampType*>(
      ::google::protobuf::testing::TimeStampType::internal_default_instance());
  maxtime_ = const_cast< ::google::protobuf::testing::TimeStampType*>(
      ::google::protobuf::testing::TimeStampType::internal_default_instance());
  timeval1_ = const_cast< ::google::protobuf::testing::TimeStampType*>(
      ::google::protobuf::testing::TimeStampType::internal_default_instance());
  timeval2_ = const_cast< ::google::protobuf::testing::TimeStampType*>(
      ::google::protobuf::testing::TimeStampType::internal_default_instance());
  timeval3_ = const_cast< ::google::protobuf::testing::TimeStampType*>(
      ::google::protobuf::testing::TimeStampType::internal_default_instance());
  timeval4_ = const_cast< ::google::protobuf::testing::TimeStampType*>(
      ::google::protobuf::testing::TimeStampType::internal_default_instance());
  timeval5_ = const_cast< ::google::protobuf::testing::TimeStampType*>(
      ::google::protobuf::testing::TimeStampType::internal_default_instance());
  timeval6_ = const_cast< ::google::protobuf::testing::TimeStampType*>(
      ::google::protobuf::testing::TimeStampType::internal_default_instance());
  timeval7_ = const_cast< ::google::protobuf::testing::TimeStampType*>(
      ::google::protobuf::testing::TimeStampType::internal_default_instance());
  timeval8_ = const_cast< ::google::protobuf::Timestamp*>(
      ::google::protobuf::Timestamp::internal_default_instance());
  zero_duration_ = const_cast< ::google::protobuf::testing::DurationType*>(
      ::google::protobuf::testing::DurationType::internal_default_instance());
  min_duration_ = const_cast< ::google::protobuf::testing::DurationType*>(
      ::google::protobuf::testing::DurationType::internal_default_instance());
  max_duration_ = const_cast< ::google::protobuf::testing::DurationType*>(
      ::google::protobuf::testing::DurationType::internal_default_instance());
  duration1_ = const_cast< ::google::protobuf::testing::DurationType*>(
      ::google::protobuf::testing::DurationType::internal_default_instance());
  duration2_ = const_cast< ::google::protobuf::testing::DurationType*>(
      ::google::protobuf::testing::DurationType::internal_default_instance());
  duration3_ = const_cast< ::google::protobuf::testing::DurationType*>(
      ::google::protobuf::testing::DurationType::internal_default_instance());
  duration4_ = const_cast< ::google::protobuf::testing::DurationType*>(
      ::google::protobuf::testing::DurationType::internal_default_instance());
  duration5_ = const_cast< ::google::protobuf::Duration*>(
      ::google::protobuf::Duration::internal_default_instance());
}

TimestampDurationTestCases::TimestampDurationTestCases(const TimestampDurationTestCases& from)
  : ::google::protobuf::Message(),
    _internal_metadata_(NULL) {
  SharedCtor();
  UnsafeMergeFrom(from);
  // @@protoc_insertion_point(copy_constructor:google.protobuf.testing.TimestampDurationTestCases)
}

void TimestampDurationTestCases::SharedCtor() {
  epoch_ = NULL;
  epoch2_ = NULL;
  mintime_ = NULL;
  maxtime_ = NULL;
  timeval1_ = NULL;
  timeval2_ = NULL;
  timeval3_ = NULL;
  timeval4_ = NULL;
  timeval5_ = NULL;
  timeval6_ = NULL;
  timeval7_ = NULL;
  timeval8_ = NULL;
  zero_duration_ = NULL;
  min_duration_ = NULL;
  max_duration_ = NULL;
  duration1_ = NULL;
  duration2_ = NULL;
  duration3_ = NULL;
  duration4_ = NULL;
  duration5_ = NULL;
  _cached_size_ = 0;
}

TimestampDurationTestCases::~TimestampDurationTestCases() {
  // @@protoc_insertion_point(destructor:google.protobuf.testing.TimestampDurationTestCases)
  SharedDtor();
}

void TimestampDurationTestCases::SharedDtor() {
  if (this != &TimestampDurationTestCases_default_instance_.get()) {
    delete epoch_;
    delete epoch2_;
    delete mintime_;
    delete maxtime_;
    delete timeval1_;
    delete timeval2_;
    delete timeval3_;
    delete timeval4_;
    delete timeval5_;
    delete timeval6_;
    delete timeval7_;
    delete timeval8_;
    delete zero_duration_;
    delete min_duration_;
    delete max_duration_;
    delete duration1_;
    delete duration2_;
    delete duration3_;
    delete duration4_;
    delete duration5_;
  }
}

void TimestampDurationTestCases::SetCachedSize(int size) const {
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
}
const ::google::protobuf::Descriptor* TimestampDurationTestCases::descriptor() {
  protobuf_AssignDescriptorsOnce();
  return TimestampDurationTestCases_descriptor_;
}

const TimestampDurationTestCases& TimestampDurationTestCases::default_instance() {
  protobuf_InitDefaults_google_2fprotobuf_2futil_2finternal_2ftestdata_2ftimestamp_5fduration_2eproto();
  return *internal_default_instance();
}

::google::protobuf::internal::ExplicitlyConstructed<TimestampDurationTestCases> TimestampDurationTestCases_default_instance_;

TimestampDurationTestCases* TimestampDurationTestCases::New(::google::protobuf::Arena* arena) const {
  TimestampDurationTestCases* n = new TimestampDurationTestCases;
  if (arena != NULL) {
    arena->Own(n);
  }
  return n;
}

void TimestampDurationTestCases::Clear() {
// @@protoc_insertion_point(message_clear_start:google.protobuf.testing.TimestampDurationTestCases)
  if (GetArenaNoVirtual() == NULL && epoch_ != NULL) delete epoch_;
  epoch_ = NULL;
  if (GetArenaNoVirtual() == NULL && epoch2_ != NULL) delete epoch2_;
  epoch2_ = NULL;
  if (GetArenaNoVirtual() == NULL && mintime_ != NULL) delete mintime_;
  mintime_ = NULL;
  if (GetArenaNoVirtual() == NULL && maxtime_ != NULL) delete maxtime_;
  maxtime_ = NULL;
  if (GetArenaNoVirtual() == NULL && timeval1_ != NULL) delete timeval1_;
  timeval1_ = NULL;
  if (GetArenaNoVirtual() == NULL && timeval2_ != NULL) delete timeval2_;
  timeval2_ = NULL;
  if (GetArenaNoVirtual() == NULL && timeval3_ != NULL) delete timeval3_;
  timeval3_ = NULL;
  if (GetArenaNoVirtual() == NULL && timeval4_ != NULL) delete timeval4_;
  timeval4_ = NULL;
  if (GetArenaNoVirtual() == NULL && timeval5_ != NULL) delete timeval5_;
  timeval5_ = NULL;
  if (GetArenaNoVirtual() == NULL && timeval6_ != NULL) delete timeval6_;
  timeval6_ = NULL;
  if (GetArenaNoVirtual() == NULL && timeval7_ != NULL) delete timeval7_;
  timeval7_ = NULL;
  if (GetArenaNoVirtual() == NULL && timeval8_ != NULL) delete timeval8_;
  timeval8_ = NULL;
  if (GetArenaNoVirtual() == NULL && zero_duration_ != NULL) delete zero_duration_;
  zero_duration_ = NULL;
  if (GetArenaNoVirtual() == NULL && min_duration_ != NULL) delete min_duration_;
  min_duration_ = NULL;
  if (GetArenaNoVirtual() == NULL && max_duration_ != NULL) delete max_duration_;
  max_duration_ = NULL;
  if (GetArenaNoVirtual() == NULL && duration1_ != NULL) delete duration1_;
  duration1_ = NULL;
  if (GetArenaNoVirtual() == NULL && duration2_ != NULL) delete duration2_;
  duration2_ = NULL;
  if (GetArenaNoVirtual() == NULL && duration3_ != NULL) delete duration3_;
  duration3_ = NULL;
  if (GetArenaNoVirtual() == NULL && duration4_ != NULL) delete duration4_;
  duration4_ = NULL;
  if (GetArenaNoVirtual() == NULL && duration5_ != NULL) delete duration5_;
  duration5_ = NULL;
}

bool TimestampDurationTestCases::MergePartialFromCodedStream(
    ::google::protobuf::io::CodedInputStream* input) {
#define DO_(EXPRESSION) if (!GOOGLE_PREDICT_TRUE(EXPRESSION)) goto failure
  ::google::protobuf::uint32 tag;
  // @@protoc_insertion_point(parse_start:google.protobuf.testing.TimestampDurationTestCases)
  for (;;) {
    ::std::pair< ::google::protobuf::uint32, bool> p = input->ReadTagWithCutoff(16383);
    tag = p.first;
    if (!p.second) goto handle_unusual;
    switch (::google::protobuf::internal::WireFormatLite::GetTagFieldNumber(tag)) {
      // optional .google.protobuf.testing.TimeStampType epoch = 1;
      case 1: {
        if (tag == 10) {
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessageNoVirtual(
               input, mutable_epoch()));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(18)) goto parse_epoch2;
        break;
      }

      // optional .google.protobuf.testing.TimeStampType epoch2 = 2;
      case 2: {
        if (tag == 18) {
         parse_epoch2:
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessageNoVirtual(
               input, mutable_epoch2()));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(26)) goto parse_mintime;
        break;
      }

      // optional .google.protobuf.testing.TimeStampType mintime = 3;
      case 3: {
        if (tag == 26) {
         parse_mintime:
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessageNoVirtual(
               input, mutable_mintime()));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(34)) goto parse_maxtime;
        break;
      }

      // optional .google.protobuf.testing.TimeStampType maxtime = 4;
      case 4: {
        if (tag == 34) {
         parse_maxtime:
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessageNoVirtual(
               input, mutable_maxtime()));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(42)) goto parse_timeval1;
        break;
      }

      // optional .google.protobuf.testing.TimeStampType timeval1 = 5;
      case 5: {
        if (tag == 42) {
         parse_timeval1:
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessageNoVirtual(
               input, mutable_timeval1()));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(50)) goto parse_timeval2;
        break;
      }

      // optional .google.protobuf.testing.TimeStampType timeval2 = 6;
      case 6: {
        if (tag == 50) {
         parse_timeval2:
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessageNoVirtual(
               input, mutable_timeval2()));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(58)) goto parse_timeval3;
        break;
      }

      // optional .google.protobuf.testing.TimeStampType timeval3 = 7;
      case 7: {
        if (tag == 58) {
         parse_timeval3:
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessageNoVirtual(
               input, mutable_timeval3()));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(66)) goto parse_timeval4;
        break;
      }

      // optional .google.protobuf.testing.TimeStampType timeval4 = 8;
      case 8: {
        if (tag == 66) {
         parse_timeval4:
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessageNoVirtual(
               input, mutable_timeval4()));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(74)) goto parse_timeval5;
        break;
      }

      // optional .google.protobuf.testing.TimeStampType timeval5 = 9;
      case 9: {
        if (tag == 74) {
         parse_timeval5:
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessageNoVirtual(
               input, mutable_timeval5()));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(82)) goto parse_timeval6;
        break;
      }

      // optional .google.protobuf.testing.TimeStampType timeval6 = 10;
      case 10: {
        if (tag == 82) {
         parse_timeval6:
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessageNoVirtual(
               input, mutable_timeval6()));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(90)) goto parse_timeval7;
        break;
      }

      // optional .google.protobuf.testing.TimeStampType timeval7 = 11;
      case 11: {
        if (tag == 90) {
         parse_timeval7:
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessageNoVirtual(
               input, mutable_timeval7()));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(98)) goto parse_timeval8;
        break;
      }

      // optional .google.protobuf.Timestamp timeval8 = 12;
      case 12: {
        if (tag == 98) {
         parse_timeval8:
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessageNoVirtual(
               input, mutable_timeval8()));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(810)) goto parse_zero_duration;
        break;
      }

      // optional .google.protobuf.testing.DurationType zero_duration = 101;
      case 101: {
        if (tag == 810) {
         parse_zero_duration:
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessageNoVirtual(
               input, mutable_zero_duration()));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(818)) goto parse_min_duration;
        break;
      }

      // optional .google.protobuf.testing.DurationType min_duration = 102;
      case 102: {
        if (tag == 818) {
         parse_min_duration:
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessageNoVirtual(
               input, mutable_min_duration()));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(826)) goto parse_max_duration;
        break;
      }

      // optional .google.protobuf.testing.DurationType max_duration = 103;
      case 103: {
        if (tag == 826) {
         parse_max_duration:
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessageNoVirtual(
               input, mutable_max_duration()));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(834)) goto parse_duration1;
        break;
      }

      // optional .google.protobuf.testing.DurationType duration1 = 104;
      case 104: {
        if (tag == 834) {
         parse_duration1:
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessageNoVirtual(
               input, mutable_duration1()));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(842)) goto parse_duration2;
        break;
      }

      // optional .google.protobuf.testing.DurationType duration2 = 105;
      case 105: {
        if (tag == 842) {
         parse_duration2:
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessageNoVirtual(
               input, mutable_duration2()));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(850)) goto parse_duration3;
        break;
      }

      // optional .google.protobuf.testing.DurationType duration3 = 106;
      case 106: {
        if (tag == 850) {
         parse_duration3:
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessageNoVirtual(
               input, mutable_duration3()));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(858)) goto parse_duration4;
        break;
      }

      // optional .google.protobuf.testing.DurationType duration4 = 107;
      case 107: {
        if (tag == 858) {
         parse_duration4:
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessageNoVirtual(
               input, mutable_duration4()));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(866)) goto parse_duration5;
        break;
      }

      // optional .google.protobuf.Duration duration5 = 108;
      case 108: {
        if (tag == 866) {
         parse_duration5:
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessageNoVirtual(
               input, mutable_duration5()));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectAtEnd()) goto success;
        break;
      }

      default: {
      handle_unusual:
        if (tag == 0 ||
            ::google::protobuf::internal::WireFormatLite::GetTagWireType(tag) ==
            ::google::protobuf::internal::WireFormatLite::WIRETYPE_END_GROUP) {
          goto success;
        }
        DO_(::google::protobuf::internal::WireFormatLite::SkipField(input, tag));
        break;
      }
    }
  }
success:
  // @@protoc_insertion_point(parse_success:google.protobuf.testing.TimestampDurationTestCases)
  return true;
failure:
  // @@protoc_insertion_point(parse_failure:google.protobuf.testing.TimestampDurationTestCases)
  return false;
#undef DO_
}

void TimestampDurationTestCases::SerializeWithCachedSizes(
    ::google::protobuf::io::CodedOutputStream* output) const {
  // @@protoc_insertion_point(serialize_start:google.protobuf.testing.TimestampDurationTestCases)
  // optional .google.protobuf.testing.TimeStampType epoch = 1;
  if (this->has_epoch()) {
    ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
      1, *this->epoch_, output);
  }

  // optional .google.protobuf.testing.TimeStampType epoch2 = 2;
  if (this->has_epoch2()) {
    ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
      2, *this->epoch2_, output);
  }

  // optional .google.protobuf.testing.TimeStampType mintime = 3;
  if (this->has_mintime()) {
    ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
      3, *this->mintime_, output);
  }

  // optional .google.protobuf.testing.TimeStampType maxtime = 4;
  if (this->has_maxtime()) {
    ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
      4, *this->maxtime_, output);
  }

  // optional .google.protobuf.testing.TimeStampType timeval1 = 5;
  if (this->has_timeval1()) {
    ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
      5, *this->timeval1_, output);
  }

  // optional .google.protobuf.testing.TimeStampType timeval2 = 6;
  if (this->has_timeval2()) {
    ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
      6, *this->timeval2_, output);
  }

  // optional .google.protobuf.testing.TimeStampType timeval3 = 7;
  if (this->has_timeval3()) {
    ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
      7, *this->timeval3_, output);
  }

  // optional .google.protobuf.testing.TimeStampType timeval4 = 8;
  if (this->has_timeval4()) {
    ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
      8, *this->timeval4_, output);
  }

  // optional .google.protobuf.testing.TimeStampType timeval5 = 9;
  if (this->has_timeval5()) {
    ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
      9, *this->timeval5_, output);
  }

  // optional .google.protobuf.testing.TimeStampType timeval6 = 10;
  if (this->has_timeval6()) {
    ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
      10, *this->timeval6_, output);
  }

  // optional .google.protobuf.testing.TimeStampType timeval7 = 11;
  if (this->has_timeval7()) {
    ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
      11, *this->timeval7_, output);
  }

  // optional .google.protobuf.Timestamp timeval8 = 12;
  if (this->has_timeval8()) {
    ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
      12, *this->timeval8_, output);
  }

  // optional .google.protobuf.testing.DurationType zero_duration = 101;
  if (this->has_zero_duration()) {
    ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
      101, *this->zero_duration_, output);
  }

  // optional .google.protobuf.testing.DurationType min_duration = 102;
  if (this->has_min_duration()) {
    ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
      102, *this->min_duration_, output);
  }

  // optional .google.protobuf.testing.DurationType max_duration = 103;
  if (this->has_max_duration()) {
    ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
      103, *this->max_duration_, output);
  }

  // optional .google.protobuf.testing.DurationType duration1 = 104;
  if (this->has_duration1()) {
    ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
      104, *this->duration1_, output);
  }

  // optional .google.protobuf.testing.DurationType duration2 = 105;
  if (this->has_duration2()) {
    ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
      105, *this->duration2_, output);
  }

  // optional .google.protobuf.testing.DurationType duration3 = 106;
  if (this->has_duration3()) {
    ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
      106, *this->duration3_, output);
  }

  // optional .google.protobuf.testing.DurationType duration4 = 107;
  if (this->has_duration4()) {
    ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
      107, *this->duration4_, output);
  }

  // optional .google.protobuf.Duration duration5 = 108;
  if (this->has_duration5()) {
    ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
      108, *this->duration5_, output);
  }

  // @@protoc_insertion_point(serialize_end:google.protobuf.testing.TimestampDurationTestCases)
}

::google::protobuf::uint8* TimestampDurationTestCases::InternalSerializeWithCachedSizesToArray(
    bool deterministic, ::google::protobuf::uint8* target) const {
  (void)deterministic; // Unused
  // @@protoc_insertion_point(serialize_to_array_start:google.protobuf.testing.TimestampDurationTestCases)
  // optional .google.protobuf.testing.TimeStampType epoch = 1;
  if (this->has_epoch()) {
    target = ::google::protobuf::internal::WireFormatLite::
      InternalWriteMessageNoVirtualToArray(
        1, *this->epoch_, false, target);
  }

  // optional .google.protobuf.testing.TimeStampType epoch2 = 2;
  if (this->has_epoch2()) {
    target = ::google::protobuf::internal::WireFormatLite::
      InternalWriteMessageNoVirtualToArray(
        2, *this->epoch2_, false, target);
  }

  // optional .google.protobuf.testing.TimeStampType mintime = 3;
  if (this->has_mintime()) {
    target = ::google::protobuf::internal::WireFormatLite::
      InternalWriteMessageNoVirtualToArray(
        3, *this->mintime_, false, target);
  }

  // optional .google.protobuf.testing.TimeStampType maxtime = 4;
  if (this->has_maxtime()) {
    target = ::google::protobuf::internal::WireFormatLite::
      InternalWriteMessageNoVirtualToArray(
        4, *this->maxtime_, false, target);
  }

  // optional .google.protobuf.testing.TimeStampType timeval1 = 5;
  if (this->has_timeval1()) {
    target = ::google::protobuf::internal::WireFormatLite::
      InternalWriteMessageNoVirtualToArray(
        5, *this->timeval1_, false, target);
  }

  // optional .google.protobuf.testing.TimeStampType timeval2 = 6;
  if (this->has_timeval2()) {
    target = ::google::protobuf::internal::WireFormatLite::
      InternalWriteMessageNoVirtualToArray(
        6, *this->timeval2_, false, target);
  }

  // optional .google.protobuf.testing.TimeStampType timeval3 = 7;
  if (this->has_timeval3()) {
    target = ::google::protobuf::internal::WireFormatLite::
      InternalWriteMessageNoVirtualToArray(
        7, *this->timeval3_, false, target);
  }

  // optional .google.protobuf.testing.TimeStampType timeval4 = 8;
  if (this->has_timeval4()) {
    target = ::google::protobuf::internal::WireFormatLite::
      InternalWriteMessageNoVirtualToArray(
        8, *this->timeval4_, false, target);
  }

  // optional .google.protobuf.testing.TimeStampType timeval5 = 9;
  if (this->has_timeval5()) {
    target = ::google::protobuf::internal::WireFormatLite::
      InternalWriteMessageNoVirtualToArray(
        9, *this->timeval5_, false, target);
  }

  // optional .google.protobuf.testing.TimeStampType timeval6 = 10;
  if (this->has_timeval6()) {
    target = ::google::protobuf::internal::WireFormatLite::
      InternalWriteMessageNoVirtualToArray(
        10, *this->timeval6_, false, target);
  }

  // optional .google.protobuf.testing.TimeStampType timeval7 = 11;
  if (this->has_timeval7()) {
    target = ::google::protobuf::internal::WireFormatLite::
      InternalWriteMessageNoVirtualToArray(
        11, *this->timeval7_, false, target);
  }

  // optional .google.protobuf.Timestamp timeval8 = 12;
  if (this->has_timeval8()) {
    target = ::google::protobuf::internal::WireFormatLite::
      InternalWriteMessageNoVirtualToArray(
        12, *this->timeval8_, false, target);
  }

  // optional .google.protobuf.testing.DurationType zero_duration = 101;
  if (this->has_zero_duration()) {
    target = ::google::protobuf::internal::WireFormatLite::
      InternalWriteMessageNoVirtualToArray(
        101, *this->zero_duration_, false, target);
  }

  // optional .google.protobuf.testing.DurationType min_duration = 102;
  if (this->has_min_duration()) {
    target = ::google::protobuf::internal::WireFormatLite::
      InternalWriteMessageNoVirtualToArray(
        102, *this->min_duration_, false, target);
  }

  // optional .google.protobuf.testing.DurationType max_duration = 103;
  if (this->has_max_duration()) {
    target = ::google::protobuf::internal::WireFormatLite::
      InternalWriteMessageNoVirtualToArray(
        103, *this->max_duration_, false, target);
  }

  // optional .google.protobuf.testing.DurationType duration1 = 104;
  if (this->has_duration1()) {
    target = ::google::protobuf::internal::WireFormatLite::
      InternalWriteMessageNoVirtualToArray(
        104, *this->duration1_, false, target);
  }

  // optional .google.protobuf.testing.DurationType duration2 = 105;
  if (this->has_duration2()) {
    target = ::google::protobuf::internal::WireFormatLite::
      InternalWriteMessageNoVirtualToArray(
        105, *this->duration2_, false, target);
  }

  // optional .google.protobuf.testing.DurationType duration3 = 106;
  if (this->has_duration3()) {
    target = ::google::protobuf::internal::WireFormatLite::
      InternalWriteMessageNoVirtualToArray(
        106, *this->duration3_, false, target);
  }

  // optional .google.protobuf.testing.DurationType duration4 = 107;
  if (this->has_duration4()) {
    target = ::google::protobuf::internal::WireFormatLite::
      InternalWriteMessageNoVirtualToArray(
        107, *this->duration4_, false, target);
  }

  // optional .google.protobuf.Duration duration5 = 108;
  if (this->has_duration5()) {
    target = ::google::protobuf::internal::WireFormatLite::
      InternalWriteMessageNoVirtualToArray(
        108, *this->duration5_, false, target);
  }

  // @@protoc_insertion_point(serialize_to_array_end:google.protobuf.testing.TimestampDurationTestCases)
  return target;
}

size_t TimestampDurationTestCases::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:google.protobuf.testing.TimestampDurationTestCases)
  size_t total_size = 0;

  // optional .google.protobuf.testing.TimeStampType epoch = 1;
  if (this->has_epoch()) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::MessageSizeNoVirtual(
        *this->epoch_);
  }

  // optional .google.protobuf.testing.TimeStampType epoch2 = 2;
  if (this->has_epoch2()) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::MessageSizeNoVirtual(
        *this->epoch2_);
  }

  // optional .google.protobuf.testing.TimeStampType mintime = 3;
  if (this->has_mintime()) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::MessageSizeNoVirtual(
        *this->mintime_);
  }

  // optional .google.protobuf.testing.TimeStampType maxtime = 4;
  if (this->has_maxtime()) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::MessageSizeNoVirtual(
        *this->maxtime_);
  }

  // optional .google.protobuf.testing.TimeStampType timeval1 = 5;
  if (this->has_timeval1()) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::MessageSizeNoVirtual(
        *this->timeval1_);
  }

  // optional .google.protobuf.testing.TimeStampType timeval2 = 6;
  if (this->has_timeval2()) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::MessageSizeNoVirtual(
        *this->timeval2_);
  }

  // optional .google.protobuf.testing.TimeStampType timeval3 = 7;
  if (this->has_timeval3()) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::MessageSizeNoVirtual(
        *this->timeval3_);
  }

  // optional .google.protobuf.testing.TimeStampType timeval4 = 8;
  if (this->has_timeval4()) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::MessageSizeNoVirtual(
        *this->timeval4_);
  }

  // optional .google.protobuf.testing.TimeStampType timeval5 = 9;
  if (this->has_timeval5()) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::MessageSizeNoVirtual(
        *this->timeval5_);
  }

  // optional .google.protobuf.testing.TimeStampType timeval6 = 10;
  if (this->has_timeval6()) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::MessageSizeNoVirtual(
        *this->timeval6_);
  }

  // optional .google.protobuf.testing.TimeStampType timeval7 = 11;
  if (this->has_timeval7()) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::MessageSizeNoVirtual(
        *this->timeval7_);
  }

  // optional .google.protobuf.Timestamp timeval8 = 12;
  if (this->has_timeval8()) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::MessageSizeNoVirtual(
        *this->timeval8_);
  }

  // optional .google.protobuf.testing.DurationType zero_duration = 101;
  if (this->has_zero_duration()) {
    total_size += 2 +
      ::google::protobuf::internal::WireFormatLite::MessageSizeNoVirtual(
        *this->zero_duration_);
  }

  // optional .google.protobuf.testing.DurationType min_duration = 102;
  if (this->has_min_duration()) {
    total_size += 2 +
      ::google::protobuf::internal::WireFormatLite::MessageSizeNoVirtual(
        *this->min_duration_);
  }

  // optional .google.protobuf.testing.DurationType max_duration = 103;
  if (this->has_max_duration()) {
    total_size += 2 +
      ::google::protobuf::internal::WireFormatLite::MessageSizeNoVirtual(
        *this->max_duration_);
  }

  // optional .google.protobuf.testing.DurationType duration1 = 104;
  if (this->has_duration1()) {
    total_size += 2 +
      ::google::protobuf::internal::WireFormatLite::MessageSizeNoVirtual(
        *this->duration1_);
  }

  // optional .google.protobuf.testing.DurationType duration2 = 105;
  if (this->has_duration2()) {
    total_size += 2 +
      ::google::protobuf::internal::WireFormatLite::MessageSizeNoVirtual(
        *this->duration2_);
  }

  // optional .google.protobuf.testing.DurationType duration3 = 106;
  if (this->has_duration3()) {
    total_size += 2 +
      ::google::protobuf::internal::WireFormatLite::MessageSizeNoVirtual(
        *this->duration3_);
  }

  // optional .google.protobuf.testing.DurationType duration4 = 107;
  if (this->has_duration4()) {
    total_size += 2 +
      ::google::protobuf::internal::WireFormatLite::MessageSizeNoVirtual(
        *this->duration4_);
  }

  // optional .google.protobuf.Duration duration5 = 108;
  if (this->has_duration5()) {
    total_size += 2 +
      ::google::protobuf::internal::WireFormatLite::MessageSizeNoVirtual(
        *this->duration5_);
  }

  int cached_size = ::google::protobuf::internal::ToCachedSize(total_size);
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = cached_size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
  return total_size;
}

void TimestampDurationTestCases::MergeFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_merge_from_start:google.protobuf.testing.TimestampDurationTestCases)
  if (GOOGLE_PREDICT_FALSE(&from == this)) MergeFromFail(__LINE__);
  const TimestampDurationTestCases* source =
      ::google::protobuf::internal::DynamicCastToGenerated<const TimestampDurationTestCases>(
          &from);
  if (source == NULL) {
  // @@protoc_insertion_point(generalized_merge_from_cast_fail:google.protobuf.testing.TimestampDurationTestCases)
    ::google::protobuf::internal::ReflectionOps::Merge(from, this);
  } else {
  // @@protoc_insertion_point(generalized_merge_from_cast_success:google.protobuf.testing.TimestampDurationTestCases)
    UnsafeMergeFrom(*source);
  }
}

void TimestampDurationTestCases::MergeFrom(const TimestampDurationTestCases& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:google.protobuf.testing.TimestampDurationTestCases)
  if (GOOGLE_PREDICT_TRUE(&from != this)) {
    UnsafeMergeFrom(from);
  } else {
    MergeFromFail(__LINE__);
  }
}

void TimestampDurationTestCases::UnsafeMergeFrom(const TimestampDurationTestCases& from) {
  GOOGLE_DCHECK(&from != this);
  if (from.has_epoch()) {
    mutable_epoch()->::google::protobuf::testing::TimeStampType::MergeFrom(from.epoch());
  }
  if (from.has_epoch2()) {
    mutable_epoch2()->::google::protobuf::testing::TimeStampType::MergeFrom(from.epoch2());
  }
  if (from.has_mintime()) {
    mutable_mintime()->::google::protobuf::testing::TimeStampType::MergeFrom(from.mintime());
  }
  if (from.has_maxtime()) {
    mutable_maxtime()->::google::protobuf::testing::TimeStampType::MergeFrom(from.maxtime());
  }
  if (from.has_timeval1()) {
    mutable_timeval1()->::google::protobuf::testing::TimeStampType::MergeFrom(from.timeval1());
  }
  if (from.has_timeval2()) {
    mutable_timeval2()->::google::protobuf::testing::TimeStampType::MergeFrom(from.timeval2());
  }
  if (from.has_timeval3()) {
    mutable_timeval3()->::google::protobuf::testing::TimeStampType::MergeFrom(from.timeval3());
  }
  if (from.has_timeval4()) {
    mutable_timeval4()->::google::protobuf::testing::TimeStampType::MergeFrom(from.timeval4());
  }
  if (from.has_timeval5()) {
    mutable_timeval5()->::google::protobuf::testing::TimeStampType::MergeFrom(from.timeval5());
  }
  if (from.has_timeval6()) {
    mutable_timeval6()->::google::protobuf::testing::TimeStampType::MergeFrom(from.timeval6());
  }
  if (from.has_timeval7()) {
    mutable_timeval7()->::google::protobuf::testing::TimeStampType::MergeFrom(from.timeval7());
  }
  if (from.has_timeval8()) {
    mutable_timeval8()->::google::protobuf::Timestamp::MergeFrom(from.timeval8());
  }
  if (from.has_zero_duration()) {
    mutable_zero_duration()->::google::protobuf::testing::DurationType::MergeFrom(from.zero_duration());
  }
  if (from.has_min_duration()) {
    mutable_min_duration()->::google::protobuf::testing::DurationType::MergeFrom(from.min_duration());
  }
  if (from.has_max_duration()) {
    mutable_max_duration()->::google::protobuf::testing::DurationType::MergeFrom(from.max_duration());
  }
  if (from.has_duration1()) {
    mutable_duration1()->::google::protobuf::testing::DurationType::MergeFrom(from.duration1());
  }
  if (from.has_duration2()) {
    mutable_duration2()->::google::protobuf::testing::DurationType::MergeFrom(from.duration2());
  }
  if (from.has_duration3()) {
    mutable_duration3()->::google::protobuf::testing::DurationType::MergeFrom(from.duration3());
  }
  if (from.has_duration4()) {
    mutable_duration4()->::google::protobuf::testing::DurationType::MergeFrom(from.duration4());
  }
  if (from.has_duration5()) {
    mutable_duration5()->::google::protobuf::Duration::MergeFrom(from.duration5());
  }
}

void TimestampDurationTestCases::CopyFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_copy_from_start:google.protobuf.testing.TimestampDurationTestCases)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

void TimestampDurationTestCases::CopyFrom(const TimestampDurationTestCases& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:google.protobuf.testing.TimestampDurationTestCases)
  if (&from == this) return;
  Clear();
  UnsafeMergeFrom(from);
}

bool TimestampDurationTestCases::IsInitialized() const {

  return true;
}

void TimestampDurationTestCases::Swap(TimestampDurationTestCases* other) {
  if (other == this) return;
  InternalSwap(other);
}
void TimestampDurationTestCases::InternalSwap(TimestampDurationTestCases* other) {
  std::swap(epoch_, other->epoch_);
  std::swap(epoch2_, other->epoch2_);
  std::swap(mintime_, other->mintime_);
  std::swap(maxtime_, other->maxtime_);
  std::swap(timeval1_, other->timeval1_);
  std::swap(timeval2_, other->timeval2_);
  std::swap(timeval3_, other->timeval3_);
  std::swap(timeval4_, other->timeval4_);
  std::swap(timeval5_, other->timeval5_);
  std::swap(timeval6_, other->timeval6_);
  std::swap(timeval7_, other->timeval7_);
  std::swap(timeval8_, other->timeval8_);
  std::swap(zero_duration_, other->zero_duration_);
  std::swap(min_duration_, other->min_duration_);
  std::swap(max_duration_, other->max_duration_);
  std::swap(duration1_, other->duration1_);
  std::swap(duration2_, other->duration2_);
  std::swap(duration3_, other->duration3_);
  std::swap(duration4_, other->duration4_);
  std::swap(duration5_, other->duration5_);
  _internal_metadata_.Swap(&other->_internal_metadata_);
  std::swap(_cached_size_, other->_cached_size_);
}

::google::protobuf::Metadata TimestampDurationTestCases::GetMetadata() const {
  protobuf_AssignDescriptorsOnce();
  ::google::protobuf::Metadata metadata;
  metadata.descriptor = TimestampDurationTestCases_descriptor_;
  metadata.reflection = TimestampDurationTestCases_reflection_;
  return metadata;
}

#if PROTOBUF_INLINE_NOT_IN_HEADERS
// TimestampDurationTestCases

// optional .google.protobuf.testing.TimeStampType epoch = 1;
bool TimestampDurationTestCases::has_epoch() const {
  return this != internal_default_instance() && epoch_ != NULL;
}
void TimestampDurationTestCases::clear_epoch() {
  if (GetArenaNoVirtual() == NULL && epoch_ != NULL) delete epoch_;
  epoch_ = NULL;
}
const ::google::protobuf::testing::TimeStampType& TimestampDurationTestCases::epoch() const {
  // @@protoc_insertion_point(field_get:google.protobuf.testing.TimestampDurationTestCases.epoch)
  return epoch_ != NULL ? *epoch_
                         : *::google::protobuf::testing::TimeStampType::internal_default_instance();
}
::google::protobuf::testing::TimeStampType* TimestampDurationTestCases::mutable_epoch() {
  
  if (epoch_ == NULL) {
    epoch_ = new ::google::protobuf::testing::TimeStampType;
  }
  // @@protoc_insertion_point(field_mutable:google.protobuf.testing.TimestampDurationTestCases.epoch)
  return epoch_;
}
::google::protobuf::testing::TimeStampType* TimestampDurationTestCases::release_epoch() {
  // @@protoc_insertion_point(field_release:google.protobuf.testing.TimestampDurationTestCases.epoch)
  
  ::google::protobuf::testing::TimeStampType* temp = epoch_;
  epoch_ = NULL;
  return temp;
}
void TimestampDurationTestCases::set_allocated_epoch(::google::protobuf::testing::TimeStampType* epoch) {
  delete epoch_;
  epoch_ = epoch;
  if (epoch) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_set_allocated:google.protobuf.testing.TimestampDurationTestCases.epoch)
}

// optional .google.protobuf.testing.TimeStampType epoch2 = 2;
bool TimestampDurationTestCases::has_epoch2() const {
  return this != internal_default_instance() && epoch2_ != NULL;
}
void TimestampDurationTestCases::clear_epoch2() {
  if (GetArenaNoVirtual() == NULL && epoch2_ != NULL) delete epoch2_;
  epoch2_ = NULL;
}
const ::google::protobuf::testing::TimeStampType& TimestampDurationTestCases::epoch2() const {
  // @@protoc_insertion_point(field_get:google.protobuf.testing.TimestampDurationTestCases.epoch2)
  return epoch2_ != NULL ? *epoch2_
                         : *::google::protobuf::testing::TimeStampType::internal_default_instance();
}
::google::protobuf::testing::TimeStampType* TimestampDurationTestCases::mutable_epoch2() {
  
  if (epoch2_ == NULL) {
    epoch2_ = new ::google::protobuf::testing::TimeStampType;
  }
  // @@protoc_insertion_point(field_mutable:google.protobuf.testing.TimestampDurationTestCases.epoch2)
  return epoch2_;
}
::google::protobuf::testing::TimeStampType* TimestampDurationTestCases::release_epoch2() {
  // @@protoc_insertion_point(field_release:google.protobuf.testing.TimestampDurationTestCases.epoch2)
  
  ::google::protobuf::testing::TimeStampType* temp = epoch2_;
  epoch2_ = NULL;
  return temp;
}
void TimestampDurationTestCases::set_allocated_epoch2(::google::protobuf::testing::TimeStampType* epoch2) {
  delete epoch2_;
  epoch2_ = epoch2;
  if (epoch2) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_set_allocated:google.protobuf.testing.TimestampDurationTestCases.epoch2)
}

// optional .google.protobuf.testing.TimeStampType mintime = 3;
bool TimestampDurationTestCases::has_mintime() const {
  return this != internal_default_instance() && mintime_ != NULL;
}
void TimestampDurationTestCases::clear_mintime() {
  if (GetArenaNoVirtual() == NULL && mintime_ != NULL) delete mintime_;
  mintime_ = NULL;
}
const ::google::protobuf::testing::TimeStampType& TimestampDurationTestCases::mintime() const {
  // @@protoc_insertion_point(field_get:google.protobuf.testing.TimestampDurationTestCases.mintime)
  return mintime_ != NULL ? *mintime_
                         : *::google::protobuf::testing::TimeStampType::internal_default_instance();
}
::google::protobuf::testing::TimeStampType* TimestampDurationTestCases::mutable_mintime() {
  
  if (mintime_ == NULL) {
    mintime_ = new ::google::protobuf::testing::TimeStampType;
  }
  // @@protoc_insertion_point(field_mutable:google.protobuf.testing.TimestampDurationTestCases.mintime)
  return mintime_;
}
::google::protobuf::testing::TimeStampType* TimestampDurationTestCases::release_mintime() {
  // @@protoc_insertion_point(field_release:google.protobuf.testing.TimestampDurationTestCases.mintime)
  
  ::google::protobuf::testing::TimeStampType* temp = mintime_;
  mintime_ = NULL;
  return temp;
}
void TimestampDurationTestCases::set_allocated_mintime(::google::protobuf::testing::TimeStampType* mintime) {
  delete mintime_;
  mintime_ = mintime;
  if (mintime) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_set_allocated:google.protobuf.testing.TimestampDurationTestCases.mintime)
}

// optional .google.protobuf.testing.TimeStampType maxtime = 4;
bool TimestampDurationTestCases::has_maxtime() const {
  return this != internal_default_instance() && maxtime_ != NULL;
}
void TimestampDurationTestCases::clear_maxtime() {
  if (GetArenaNoVirtual() == NULL && maxtime_ != NULL) delete maxtime_;
  maxtime_ = NULL;
}
const ::google::protobuf::testing::TimeStampType& TimestampDurationTestCases::maxtime() const {
  // @@protoc_insertion_point(field_get:google.protobuf.testing.TimestampDurationTestCases.maxtime)
  return maxtime_ != NULL ? *maxtime_
                         : *::google::protobuf::testing::TimeStampType::internal_default_instance();
}
::google::protobuf::testing::TimeStampType* TimestampDurationTestCases::mutable_maxtime() {
  
  if (maxtime_ == NULL) {
    maxtime_ = new ::google::protobuf::testing::TimeStampType;
  }
  // @@protoc_insertion_point(field_mutable:google.protobuf.testing.TimestampDurationTestCases.maxtime)
  return maxtime_;
}
::google::protobuf::testing::TimeStampType* TimestampDurationTestCases::release_maxtime() {
  // @@protoc_insertion_point(field_release:google.protobuf.testing.TimestampDurationTestCases.maxtime)
  
  ::google::protobuf::testing::TimeStampType* temp = maxtime_;
  maxtime_ = NULL;
  return temp;
}
void TimestampDurationTestCases::set_allocated_maxtime(::google::protobuf::testing::TimeStampType* maxtime) {
  delete maxtime_;
  maxtime_ = maxtime;
  if (maxtime) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_set_allocated:google.protobuf.testing.TimestampDurationTestCases.maxtime)
}

// optional .google.protobuf.testing.TimeStampType timeval1 = 5;
bool TimestampDurationTestCases::has_timeval1() const {
  return this != internal_default_instance() && timeval1_ != NULL;
}
void TimestampDurationTestCases::clear_timeval1() {
  if (GetArenaNoVirtual() == NULL && timeval1_ != NULL) delete timeval1_;
  timeval1_ = NULL;
}
const ::google::protobuf::testing::TimeStampType& TimestampDurationTestCases::timeval1() const {
  // @@protoc_insertion_point(field_get:google.protobuf.testing.TimestampDurationTestCases.timeval1)
  return timeval1_ != NULL ? *timeval1_
                         : *::google::protobuf::testing::TimeStampType::internal_default_instance();
}
::google::protobuf::testing::TimeStampType* TimestampDurationTestCases::mutable_timeval1() {
  
  if (timeval1_ == NULL) {
    timeval1_ = new ::google::protobuf::testing::TimeStampType;
  }
  // @@protoc_insertion_point(field_mutable:google.protobuf.testing.TimestampDurationTestCases.timeval1)
  return timeval1_;
}
::google::protobuf::testing::TimeStampType* TimestampDurationTestCases::release_timeval1() {
  // @@protoc_insertion_point(field_release:google.protobuf.testing.TimestampDurationTestCases.timeval1)
  
  ::google::protobuf::testing::TimeStampType* temp = timeval1_;
  timeval1_ = NULL;
  return temp;
}
void TimestampDurationTestCases::set_allocated_timeval1(::google::protobuf::testing::TimeStampType* timeval1) {
  delete timeval1_;
  timeval1_ = timeval1;
  if (timeval1) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_set_allocated:google.protobuf.testing.TimestampDurationTestCases.timeval1)
}

// optional .google.protobuf.testing.TimeStampType timeval2 = 6;
bool TimestampDurationTestCases::has_timeval2() const {
  return this != internal_default_instance() && timeval2_ != NULL;
}
void TimestampDurationTestCases::clear_timeval2() {
  if (GetArenaNoVirtual() == NULL && timeval2_ != NULL) delete timeval2_;
  timeval2_ = NULL;
}
const ::google::protobuf::testing::TimeStampType& TimestampDurationTestCases::timeval2() const {
  // @@protoc_insertion_point(field_get:google.protobuf.testing.TimestampDurationTestCases.timeval2)
  return timeval2_ != NULL ? *timeval2_
                         : *::google::protobuf::testing::TimeStampType::internal_default_instance();
}
::google::protobuf::testing::TimeStampType* TimestampDurationTestCases::mutable_timeval2() {
  
  if (timeval2_ == NULL) {
    timeval2_ = new ::google::protobuf::testing::TimeStampType;
  }
  // @@protoc_insertion_point(field_mutable:google.protobuf.testing.TimestampDurationTestCases.timeval2)
  return timeval2_;
}
::google::protobuf::testing::TimeStampType* TimestampDurationTestCases::release_timeval2() {
  // @@protoc_insertion_point(field_release:google.protobuf.testing.TimestampDurationTestCases.timeval2)
  
  ::google::protobuf::testing::TimeStampType* temp = timeval2_;
  timeval2_ = NULL;
  return temp;
}
void TimestampDurationTestCases::set_allocated_timeval2(::google::protobuf::testing::TimeStampType* timeval2) {
  delete timeval2_;
  timeval2_ = timeval2;
  if (timeval2) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_set_allocated:google.protobuf.testing.TimestampDurationTestCases.timeval2)
}

// optional .google.protobuf.testing.TimeStampType timeval3 = 7;
bool TimestampDurationTestCases::has_timeval3() const {
  return this != internal_default_instance() && timeval3_ != NULL;
}
void TimestampDurationTestCases::clear_timeval3() {
  if (GetArenaNoVirtual() == NULL && timeval3_ != NULL) delete timeval3_;
  timeval3_ = NULL;
}
const ::google::protobuf::testing::TimeStampType& TimestampDurationTestCases::timeval3() const {
  // @@protoc_insertion_point(field_get:google.protobuf.testing.TimestampDurationTestCases.timeval3)
  return timeval3_ != NULL ? *timeval3_
                         : *::google::protobuf::testing::TimeStampType::internal_default_instance();
}
::google::protobuf::testing::TimeStampType* TimestampDurationTestCases::mutable_timeval3() {
  
  if (timeval3_ == NULL) {
    timeval3_ = new ::google::protobuf::testing::TimeStampType;
  }
  // @@protoc_insertion_point(field_mutable:google.protobuf.testing.TimestampDurationTestCases.timeval3)
  return timeval3_;
}
::google::protobuf::testing::TimeStampType* TimestampDurationTestCases::release_timeval3() {
  // @@protoc_insertion_point(field_release:google.protobuf.testing.TimestampDurationTestCases.timeval3)
  
  ::google::protobuf::testing::TimeStampType* temp = timeval3_;
  timeval3_ = NULL;
  return temp;
}
void TimestampDurationTestCases::set_allocated_timeval3(::google::protobuf::testing::TimeStampType* timeval3) {
  delete timeval3_;
  timeval3_ = timeval3;
  if (timeval3) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_set_allocated:google.protobuf.testing.TimestampDurationTestCases.timeval3)
}

// optional .google.protobuf.testing.TimeStampType timeval4 = 8;
bool TimestampDurationTestCases::has_timeval4() const {
  return this != internal_default_instance() && timeval4_ != NULL;
}
void TimestampDurationTestCases::clear_timeval4() {
  if (GetArenaNoVirtual() == NULL && timeval4_ != NULL) delete timeval4_;
  timeval4_ = NULL;
}
const ::google::protobuf::testing::TimeStampType& TimestampDurationTestCases::timeval4() const {
  // @@protoc_insertion_point(field_get:google.protobuf.testing.TimestampDurationTestCases.timeval4)
  return timeval4_ != NULL ? *timeval4_
                         : *::google::protobuf::testing::TimeStampType::internal_default_instance();
}
::google::protobuf::testing::TimeStampType* TimestampDurationTestCases::mutable_timeval4() {
  
  if (timeval4_ == NULL) {
    timeval4_ = new ::google::protobuf::testing::TimeStampType;
  }
  // @@protoc_insertion_point(field_mutable:google.protobuf.testing.TimestampDurationTestCases.timeval4)
  return timeval4_;
}
::google::protobuf::testing::TimeStampType* TimestampDurationTestCases::release_timeval4() {
  // @@protoc_insertion_point(field_release:google.protobuf.testing.TimestampDurationTestCases.timeval4)
  
  ::google::protobuf::testing::TimeStampType* temp = timeval4_;
  timeval4_ = NULL;
  return temp;
}
void TimestampDurationTestCases::set_allocated_timeval4(::google::protobuf::testing::TimeStampType* timeval4) {
  delete timeval4_;
  timeval4_ = timeval4;
  if (timeval4) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_set_allocated:google.protobuf.testing.TimestampDurationTestCases.timeval4)
}

// optional .google.protobuf.testing.TimeStampType timeval5 = 9;
bool TimestampDurationTestCases::has_timeval5() const {
  return this != internal_default_instance() && timeval5_ != NULL;
}
void TimestampDurationTestCases::clear_timeval5() {
  if (GetArenaNoVirtual() == NULL && timeval5_ != NULL) delete timeval5_;
  timeval5_ = NULL;
}
const ::google::protobuf::testing::TimeStampType& TimestampDurationTestCases::timeval5() const {
  // @@protoc_insertion_point(field_get:google.protobuf.testing.TimestampDurationTestCases.timeval5)
  return timeval5_ != NULL ? *timeval5_
                         : *::google::protobuf::testing::TimeStampType::internal_default_instance();
}
::google::protobuf::testing::TimeStampType* TimestampDurationTestCases::mutable_timeval5() {
  
  if (timeval5_ == NULL) {
    timeval5_ = new ::google::protobuf::testing::TimeStampType;
  }
  // @@protoc_insertion_point(field_mutable:google.protobuf.testing.TimestampDurationTestCases.timeval5)
  return timeval5_;
}
::google::protobuf::testing::TimeStampType* TimestampDurationTestCases::release_timeval5() {
  // @@protoc_insertion_point(field_release:google.protobuf.testing.TimestampDurationTestCases.timeval5)
  
  ::google::protobuf::testing::TimeStampType* temp = timeval5_;
  timeval5_ = NULL;
  return temp;
}
void TimestampDurationTestCases::set_allocated_timeval5(::google::protobuf::testing::TimeStampType* timeval5) {
  delete timeval5_;
  timeval5_ = timeval5;
  if (timeval5) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_set_allocated:google.protobuf.testing.TimestampDurationTestCases.timeval5)
}

// optional .google.protobuf.testing.TimeStampType timeval6 = 10;
bool TimestampDurationTestCases::has_timeval6() const {
  return this != internal_default_instance() && timeval6_ != NULL;
}
void TimestampDurationTestCases::clear_timeval6() {
  if (GetArenaNoVirtual() == NULL && timeval6_ != NULL) delete timeval6_;
  timeval6_ = NULL;
}
const ::google::protobuf::testing::TimeStampType& TimestampDurationTestCases::timeval6() const {
  // @@protoc_insertion_point(field_get:google.protobuf.testing.TimestampDurationTestCases.timeval6)
  return timeval6_ != NULL ? *timeval6_
                         : *::google::protobuf::testing::TimeStampType::internal_default_instance();
}
::google::protobuf::testing::TimeStampType* TimestampDurationTestCases::mutable_timeval6() {
  
  if (timeval6_ == NULL) {
    timeval6_ = new ::google::protobuf::testing::TimeStampType;
  }
  // @@protoc_insertion_point(field_mutable:google.protobuf.testing.TimestampDurationTestCases.timeval6)
  return timeval6_;
}
::google::protobuf::testing::TimeStampType* TimestampDurationTestCases::release_timeval6() {
  // @@protoc_insertion_point(field_release:google.protobuf.testing.TimestampDurationTestCases.timeval6)
  
  ::google::protobuf::testing::TimeStampType* temp = timeval6_;
  timeval6_ = NULL;
  return temp;
}
void TimestampDurationTestCases::set_allocated_timeval6(::google::protobuf::testing::TimeStampType* timeval6) {
  delete timeval6_;
  timeval6_ = timeval6;
  if (timeval6) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_set_allocated:google.protobuf.testing.TimestampDurationTestCases.timeval6)
}

// optional .google.protobuf.testing.TimeStampType timeval7 = 11;
bool TimestampDurationTestCases::has_timeval7() const {
  return this != internal_default_instance() && timeval7_ != NULL;
}
void TimestampDurationTestCases::clear_timeval7() {
  if (GetArenaNoVirtual() == NULL && timeval7_ != NULL) delete timeval7_;
  timeval7_ = NULL;
}
const ::google::protobuf::testing::TimeStampType& TimestampDurationTestCases::timeval7() const {
  // @@protoc_insertion_point(field_get:google.protobuf.testing.TimestampDurationTestCases.timeval7)
  return timeval7_ != NULL ? *timeval7_
                         : *::google::protobuf::testing::TimeStampType::internal_default_instance();
}
::google::protobuf::testing::TimeStampType* TimestampDurationTestCases::mutable_timeval7() {
  
  if (timeval7_ == NULL) {
    timeval7_ = new ::google::protobuf::testing::TimeStampType;
  }
  // @@protoc_insertion_point(field_mutable:google.protobuf.testing.TimestampDurationTestCases.timeval7)
  return timeval7_;
}
::google::protobuf::testing::TimeStampType* TimestampDurationTestCases::release_timeval7() {
  // @@protoc_insertion_point(field_release:google.protobuf.testing.TimestampDurationTestCases.timeval7)
  
  ::google::protobuf::testing::TimeStampType* temp = timeval7_;
  timeval7_ = NULL;
  return temp;
}
void TimestampDurationTestCases::set_allocated_timeval7(::google::protobuf::testing::TimeStampType* timeval7) {
  delete timeval7_;
  timeval7_ = timeval7;
  if (timeval7) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_set_allocated:google.protobuf.testing.TimestampDurationTestCases.timeval7)
}

// optional .google.protobuf.Timestamp timeval8 = 12;
bool TimestampDurationTestCases::has_timeval8() const {
  return this != internal_default_instance() && timeval8_ != NULL;
}
void TimestampDurationTestCases::clear_timeval8() {
  if (GetArenaNoVirtual() == NULL && timeval8_ != NULL) delete timeval8_;
  timeval8_ = NULL;
}
const ::google::protobuf::Timestamp& TimestampDurationTestCases::timeval8() const {
  // @@protoc_insertion_point(field_get:google.protobuf.testing.TimestampDurationTestCases.timeval8)
  return timeval8_ != NULL ? *timeval8_
                         : *::google::protobuf::Timestamp::internal_default_instance();
}
::google::protobuf::Timestamp* TimestampDurationTestCases::mutable_timeval8() {
  
  if (timeval8_ == NULL) {
    timeval8_ = new ::google::protobuf::Timestamp;
  }
  // @@protoc_insertion_point(field_mutable:google.protobuf.testing.TimestampDurationTestCases.timeval8)
  return timeval8_;
}
::google::protobuf::Timestamp* TimestampDurationTestCases::release_timeval8() {
  // @@protoc_insertion_point(field_release:google.protobuf.testing.TimestampDurationTestCases.timeval8)
  
  ::google::protobuf::Timestamp* temp = timeval8_;
  timeval8_ = NULL;
  return temp;
}
void TimestampDurationTestCases::set_allocated_timeval8(::google::protobuf::Timestamp* timeval8) {
  delete timeval8_;
  if (timeval8 != NULL && timeval8->GetArena() != NULL) {
    ::google::protobuf::Timestamp* new_timeval8 = new ::google::protobuf::Timestamp;
    new_timeval8->CopyFrom(*timeval8);
    timeval8 = new_timeval8;
  }
  timeval8_ = timeval8;
  if (timeval8) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_set_allocated:google.protobuf.testing.TimestampDurationTestCases.timeval8)
}

// optional .google.protobuf.testing.DurationType zero_duration = 101;
bool TimestampDurationTestCases::has_zero_duration() const {
  return this != internal_default_instance() && zero_duration_ != NULL;
}
void TimestampDurationTestCases::clear_zero_duration() {
  if (GetArenaNoVirtual() == NULL && zero_duration_ != NULL) delete zero_duration_;
  zero_duration_ = NULL;
}
const ::google::protobuf::testing::DurationType& TimestampDurationTestCases::zero_duration() const {
  // @@protoc_insertion_point(field_get:google.protobuf.testing.TimestampDurationTestCases.zero_duration)
  return zero_duration_ != NULL ? *zero_duration_
                         : *::google::protobuf::testing::DurationType::internal_default_instance();
}
::google::protobuf::testing::DurationType* TimestampDurationTestCases::mutable_zero_duration() {
  
  if (zero_duration_ == NULL) {
    zero_duration_ = new ::google::protobuf::testing::DurationType;
  }
  // @@protoc_insertion_point(field_mutable:google.protobuf.testing.TimestampDurationTestCases.zero_duration)
  return zero_duration_;
}
::google::protobuf::testing::DurationType* TimestampDurationTestCases::release_zero_duration() {
  // @@protoc_insertion_point(field_release:google.protobuf.testing.TimestampDurationTestCases.zero_duration)
  
  ::google::protobuf::testing::DurationType* temp = zero_duration_;
  zero_duration_ = NULL;
  return temp;
}
void TimestampDurationTestCases::set_allocated_zero_duration(::google::protobuf::testing::DurationType* zero_duration) {
  delete zero_duration_;
  zero_duration_ = zero_duration;
  if (zero_duration) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_set_allocated:google.protobuf.testing.TimestampDurationTestCases.zero_duration)
}

// optional .google.protobuf.testing.DurationType min_duration = 102;
bool TimestampDurationTestCases::has_min_duration() const {
  return this != internal_default_instance() && min_duration_ != NULL;
}
void TimestampDurationTestCases::clear_min_duration() {
  if (GetArenaNoVirtual() == NULL && min_duration_ != NULL) delete min_duration_;
  min_duration_ = NULL;
}
const ::google::protobuf::testing::DurationType& TimestampDurationTestCases::min_duration() const {
  // @@protoc_insertion_point(field_get:google.protobuf.testing.TimestampDurationTestCases.min_duration)
  return min_duration_ != NULL ? *min_duration_
                         : *::google::protobuf::testing::DurationType::internal_default_instance();
}
::google::protobuf::testing::DurationType* TimestampDurationTestCases::mutable_min_duration() {
  
  if (min_duration_ == NULL) {
    min_duration_ = new ::google::protobuf::testing::DurationType;
  }
  // @@protoc_insertion_point(field_mutable:google.protobuf.testing.TimestampDurationTestCases.min_duration)
  return min_duration_;
}
::google::protobuf::testing::DurationType* TimestampDurationTestCases::release_min_duration() {
  // @@protoc_insertion_point(field_release:google.protobuf.testing.TimestampDurationTestCases.min_duration)
  
  ::google::protobuf::testing::DurationType* temp = min_duration_;
  min_duration_ = NULL;
  return temp;
}
void TimestampDurationTestCases::set_allocated_min_duration(::google::protobuf::testing::DurationType* min_duration) {
  delete min_duration_;
  min_duration_ = min_duration;
  if (min_duration) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_set_allocated:google.protobuf.testing.TimestampDurationTestCases.min_duration)
}

// optional .google.protobuf.testing.DurationType max_duration = 103;
bool TimestampDurationTestCases::has_max_duration() const {
  return this != internal_default_instance() && max_duration_ != NULL;
}
void TimestampDurationTestCases::clear_max_duration() {
  if (GetArenaNoVirtual() == NULL && max_duration_ != NULL) delete max_duration_;
  max_duration_ = NULL;
}
const ::google::protobuf::testing::DurationType& TimestampDurationTestCases::max_duration() const {
  // @@protoc_insertion_point(field_get:google.protobuf.testing.TimestampDurationTestCases.max_duration)
  return max_duration_ != NULL ? *max_duration_
                         : *::google::protobuf::testing::DurationType::internal_default_instance();
}
::google::protobuf::testing::DurationType* TimestampDurationTestCases::mutable_max_duration() {
  
  if (max_duration_ == NULL) {
    max_duration_ = new ::google::protobuf::testing::DurationType;
  }
  // @@protoc_insertion_point(field_mutable:google.protobuf.testing.TimestampDurationTestCases.max_duration)
  return max_duration_;
}
::google::protobuf::testing::DurationType* TimestampDurationTestCases::release_max_duration() {
  // @@protoc_insertion_point(field_release:google.protobuf.testing.TimestampDurationTestCases.max_duration)
  
  ::google::protobuf::testing::DurationType* temp = max_duration_;
  max_duration_ = NULL;
  return temp;
}
void TimestampDurationTestCases::set_allocated_max_duration(::google::protobuf::testing::DurationType* max_duration) {
  delete max_duration_;
  max_duration_ = max_duration;
  if (max_duration) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_set_allocated:google.protobuf.testing.TimestampDurationTestCases.max_duration)
}

// optional .google.protobuf.testing.DurationType duration1 = 104;
bool TimestampDurationTestCases::has_duration1() const {
  return this != internal_default_instance() && duration1_ != NULL;
}
void TimestampDurationTestCases::clear_duration1() {
  if (GetArenaNoVirtual() == NULL && duration1_ != NULL) delete duration1_;
  duration1_ = NULL;
}
const ::google::protobuf::testing::DurationType& TimestampDurationTestCases::duration1() const {
  // @@protoc_insertion_point(field_get:google.protobuf.testing.TimestampDurationTestCases.duration1)
  return duration1_ != NULL ? *duration1_
                         : *::google::protobuf::testing::DurationType::internal_default_instance();
}
::google::protobuf::testing::DurationType* TimestampDurationTestCases::mutable_duration1() {
  
  if (duration1_ == NULL) {
    duration1_ = new ::google::protobuf::testing::DurationType;
  }
  // @@protoc_insertion_point(field_mutable:google.protobuf.testing.TimestampDurationTestCases.duration1)
  return duration1_;
}
::google::protobuf::testing::DurationType* TimestampDurationTestCases::release_duration1() {
  // @@protoc_insertion_point(field_release:google.protobuf.testing.TimestampDurationTestCases.duration1)
  
  ::google::protobuf::testing::DurationType* temp = duration1_;
  duration1_ = NULL;
  return temp;
}
void TimestampDurationTestCases::set_allocated_duration1(::google::protobuf::testing::DurationType* duration1) {
  delete duration1_;
  duration1_ = duration1;
  if (duration1) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_set_allocated:google.protobuf.testing.TimestampDurationTestCases.duration1)
}

// optional .google.protobuf.testing.DurationType duration2 = 105;
bool TimestampDurationTestCases::has_duration2() const {
  return this != internal_default_instance() && duration2_ != NULL;
}
void TimestampDurationTestCases::clear_duration2() {
  if (GetArenaNoVirtual() == NULL && duration2_ != NULL) delete duration2_;
  duration2_ = NULL;
}
const ::google::protobuf::testing::DurationType& TimestampDurationTestCases::duration2() const {
  // @@protoc_insertion_point(field_get:google.protobuf.testing.TimestampDurationTestCases.duration2)
  return duration2_ != NULL ? *duration2_
                         : *::google::protobuf::testing::DurationType::internal_default_instance();
}
::google::protobuf::testing::DurationType* TimestampDurationTestCases::mutable_duration2() {
  
  if (duration2_ == NULL) {
    duration2_ = new ::google::protobuf::testing::DurationType;
  }
  // @@protoc_insertion_point(field_mutable:google.protobuf.testing.TimestampDurationTestCases.duration2)
  return duration2_;
}
::google::protobuf::testing::DurationType* TimestampDurationTestCases::release_duration2() {
  // @@protoc_insertion_point(field_release:google.protobuf.testing.TimestampDurationTestCases.duration2)
  
  ::google::protobuf::testing::DurationType* temp = duration2_;
  duration2_ = NULL;
  return temp;
}
void TimestampDurationTestCases::set_allocated_duration2(::google::protobuf::testing::DurationType* duration2) {
  delete duration2_;
  duration2_ = duration2;
  if (duration2) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_set_allocated:google.protobuf.testing.TimestampDurationTestCases.duration2)
}

// optional .google.protobuf.testing.DurationType duration3 = 106;
bool TimestampDurationTestCases::has_duration3() const {
  return this != internal_default_instance() && duration3_ != NULL;
}
void TimestampDurationTestCases::clear_duration3() {
  if (GetArenaNoVirtual() == NULL && duration3_ != NULL) delete duration3_;
  duration3_ = NULL;
}
const ::google::protobuf::testing::DurationType& TimestampDurationTestCases::duration3() const {
  // @@protoc_insertion_point(field_get:google.protobuf.testing.TimestampDurationTestCases.duration3)
  return duration3_ != NULL ? *duration3_
                         : *::google::protobuf::testing::DurationType::internal_default_instance();
}
::google::protobuf::testing::DurationType* TimestampDurationTestCases::mutable_duration3() {
  
  if (duration3_ == NULL) {
    duration3_ = new ::google::protobuf::testing::DurationType;
  }
  // @@protoc_insertion_point(field_mutable:google.protobuf.testing.TimestampDurationTestCases.duration3)
  return duration3_;
}
::google::protobuf::testing::DurationType* TimestampDurationTestCases::release_duration3() {
  // @@protoc_insertion_point(field_release:google.protobuf.testing.TimestampDurationTestCases.duration3)
  
  ::google::protobuf::testing::DurationType* temp = duration3_;
  duration3_ = NULL;
  return temp;
}
void TimestampDurationTestCases::set_allocated_duration3(::google::protobuf::testing::DurationType* duration3) {
  delete duration3_;
  duration3_ = duration3;
  if (duration3) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_set_allocated:google.protobuf.testing.TimestampDurationTestCases.duration3)
}

// optional .google.protobuf.testing.DurationType duration4 = 107;
bool TimestampDurationTestCases::has_duration4() const {
  return this != internal_default_instance() && duration4_ != NULL;
}
void TimestampDurationTestCases::clear_duration4() {
  if (GetArenaNoVirtual() == NULL && duration4_ != NULL) delete duration4_;
  duration4_ = NULL;
}
const ::google::protobuf::testing::DurationType& TimestampDurationTestCases::duration4() const {
  // @@protoc_insertion_point(field_get:google.protobuf.testing.TimestampDurationTestCases.duration4)
  return duration4_ != NULL ? *duration4_
                         : *::google::protobuf::testing::DurationType::internal_default_instance();
}
::google::protobuf::testing::DurationType* TimestampDurationTestCases::mutable_duration4() {
  
  if (duration4_ == NULL) {
    duration4_ = new ::google::protobuf::testing::DurationType;
  }
  // @@protoc_insertion_point(field_mutable:google.protobuf.testing.TimestampDurationTestCases.duration4)
  return duration4_;
}
::google::protobuf::testing::DurationType* TimestampDurationTestCases::release_duration4() {
  // @@protoc_insertion_point(field_release:google.protobuf.testing.TimestampDurationTestCases.duration4)
  
  ::google::protobuf::testing::DurationType* temp = duration4_;
  duration4_ = NULL;
  return temp;
}
void TimestampDurationTestCases::set_allocated_duration4(::google::protobuf::testing::DurationType* duration4) {
  delete duration4_;
  duration4_ = duration4;
  if (duration4) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_set_allocated:google.protobuf.testing.TimestampDurationTestCases.duration4)
}

// optional .google.protobuf.Duration duration5 = 108;
bool TimestampDurationTestCases::has_duration5() const {
  return this != internal_default_instance() && duration5_ != NULL;
}
void TimestampDurationTestCases::clear_duration5() {
  if (GetArenaNoVirtual() == NULL && duration5_ != NULL) delete duration5_;
  duration5_ = NULL;
}
const ::google::protobuf::Duration& TimestampDurationTestCases::duration5() const {
  // @@protoc_insertion_point(field_get:google.protobuf.testing.TimestampDurationTestCases.duration5)
  return duration5_ != NULL ? *duration5_
                         : *::google::protobuf::Duration::internal_default_instance();
}
::google::protobuf::Duration* TimestampDurationTestCases::mutable_duration5() {
  
  if (duration5_ == NULL) {
    duration5_ = new ::google::protobuf::Duration;
  }
  // @@protoc_insertion_point(field_mutable:google.protobuf.testing.TimestampDurationTestCases.duration5)
  return duration5_;
}
::google::protobuf::Duration* TimestampDurationTestCases::release_duration5() {
  // @@protoc_insertion_point(field_release:google.protobuf.testing.TimestampDurationTestCases.duration5)
  
  ::google::protobuf::Duration* temp = duration5_;
  duration5_ = NULL;
  return temp;
}
void TimestampDurationTestCases::set_allocated_duration5(::google::protobuf::Duration* duration5) {
  delete duration5_;
  if (duration5 != NULL && duration5->GetArena() != NULL) {
    ::google::protobuf::Duration* new_duration5 = new ::google::protobuf::Duration;
    new_duration5->CopyFrom(*duration5);
    duration5 = new_duration5;
  }
  duration5_ = duration5;
  if (duration5) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_set_allocated:google.protobuf.testing.TimestampDurationTestCases.duration5)
}

inline const TimestampDurationTestCases* TimestampDurationTestCases::internal_default_instance() {
  return &TimestampDurationTestCases_default_instance_.get();
}
#endif  // PROTOBUF_INLINE_NOT_IN_HEADERS

// ===================================================================

#if !defined(_MSC_VER) || _MSC_VER >= 1900
const int TimeStampType::kTimestampFieldNumber;
#endif  // !defined(_MSC_VER) || _MSC_VER >= 1900

TimeStampType::TimeStampType()
  : ::google::protobuf::Message(), _internal_metadata_(NULL) {
  if (this != internal_default_instance()) protobuf_InitDefaults_google_2fprotobuf_2futil_2finternal_2ftestdata_2ftimestamp_5fduration_2eproto();
  SharedCtor();
  // @@protoc_insertion_point(constructor:google.protobuf.testing.TimeStampType)
}

void TimeStampType::InitAsDefaultInstance() {
  timestamp_ = const_cast< ::google::protobuf::Timestamp*>(
      ::google::protobuf::Timestamp::internal_default_instance());
}

TimeStampType::TimeStampType(const TimeStampType& from)
  : ::google::protobuf::Message(),
    _internal_metadata_(NULL) {
  SharedCtor();
  UnsafeMergeFrom(from);
  // @@protoc_insertion_point(copy_constructor:google.protobuf.testing.TimeStampType)
}

void TimeStampType::SharedCtor() {
  timestamp_ = NULL;
  _cached_size_ = 0;
}

TimeStampType::~TimeStampType() {
  // @@protoc_insertion_point(destructor:google.protobuf.testing.TimeStampType)
  SharedDtor();
}

void TimeStampType::SharedDtor() {
  if (this != &TimeStampType_default_instance_.get()) {
    delete timestamp_;
  }
}

void TimeStampType::SetCachedSize(int size) const {
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
}
const ::google::protobuf::Descriptor* TimeStampType::descriptor() {
  protobuf_AssignDescriptorsOnce();
  return TimeStampType_descriptor_;
}

const TimeStampType& TimeStampType::default_instance() {
  protobuf_InitDefaults_google_2fprotobuf_2futil_2finternal_2ftestdata_2ftimestamp_5fduration_2eproto();
  return *internal_default_instance();
}

::google::protobuf::internal::ExplicitlyConstructed<TimeStampType> TimeStampType_default_instance_;

TimeStampType* TimeStampType::New(::google::protobuf::Arena* arena) const {
  TimeStampType* n = new TimeStampType;
  if (arena != NULL) {
    arena->Own(n);
  }
  return n;
}

void TimeStampType::Clear() {
// @@protoc_insertion_point(message_clear_start:google.protobuf.testing.TimeStampType)
  if (GetArenaNoVirtual() == NULL && timestamp_ != NULL) delete timestamp_;
  timestamp_ = NULL;
}

bool TimeStampType::MergePartialFromCodedStream(
    ::google::protobuf::io::CodedInputStream* input) {
#define DO_(EXPRESSION) if (!GOOGLE_PREDICT_TRUE(EXPRESSION)) goto failure
  ::google::protobuf::uint32 tag;
  // @@protoc_insertion_point(parse_start:google.protobuf.testing.TimeStampType)
  for (;;) {
    ::std::pair< ::google::protobuf::uint32, bool> p = input->ReadTagWithCutoff(127);
    tag = p.first;
    if (!p.second) goto handle_unusual;
    switch (::google::protobuf::internal::WireFormatLite::GetTagFieldNumber(tag)) {
      // optional .google.protobuf.Timestamp timestamp = 1;
      case 1: {
        if (tag == 10) {
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessageNoVirtual(
               input, mutable_timestamp()));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectAtEnd()) goto success;
        break;
      }

      default: {
      handle_unusual:
        if (tag == 0 ||
            ::google::protobuf::internal::WireFormatLite::GetTagWireType(tag) ==
            ::google::protobuf::internal::WireFormatLite::WIRETYPE_END_GROUP) {
          goto success;
        }
        DO_(::google::protobuf::internal::WireFormatLite::SkipField(input, tag));
        break;
      }
    }
  }
success:
  // @@protoc_insertion_point(parse_success:google.protobuf.testing.TimeStampType)
  return true;
failure:
  // @@protoc_insertion_point(parse_failure:google.protobuf.testing.TimeStampType)
  return false;
#undef DO_
}

void TimeStampType::SerializeWithCachedSizes(
    ::google::protobuf::io::CodedOutputStream* output) const {
  // @@protoc_insertion_point(serialize_start:google.protobuf.testing.TimeStampType)
  // optional .google.protobuf.Timestamp timestamp = 1;
  if (this->has_timestamp()) {
    ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
      1, *this->timestamp_, output);
  }

  // @@protoc_insertion_point(serialize_end:google.protobuf.testing.TimeStampType)
}

::google::protobuf::uint8* TimeStampType::InternalSerializeWithCachedSizesToArray(
    bool deterministic, ::google::protobuf::uint8* target) const {
  (void)deterministic; // Unused
  // @@protoc_insertion_point(serialize_to_array_start:google.protobuf.testing.TimeStampType)
  // optional .google.protobuf.Timestamp timestamp = 1;
  if (this->has_timestamp()) {
    target = ::google::protobuf::internal::WireFormatLite::
      InternalWriteMessageNoVirtualToArray(
        1, *this->timestamp_, false, target);
  }

  // @@protoc_insertion_point(serialize_to_array_end:google.protobuf.testing.TimeStampType)
  return target;
}

size_t TimeStampType::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:google.protobuf.testing.TimeStampType)
  size_t total_size = 0;

  // optional .google.protobuf.Timestamp timestamp = 1;
  if (this->has_timestamp()) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::MessageSizeNoVirtual(
        *this->timestamp_);
  }

  int cached_size = ::google::protobuf::internal::ToCachedSize(total_size);
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = cached_size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
  return total_size;
}

void TimeStampType::MergeFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_merge_from_start:google.protobuf.testing.TimeStampType)
  if (GOOGLE_PREDICT_FALSE(&from == this)) MergeFromFail(__LINE__);
  const TimeStampType* source =
      ::google::protobuf::internal::DynamicCastToGenerated<const TimeStampType>(
          &from);
  if (source == NULL) {
  // @@protoc_insertion_point(generalized_merge_from_cast_fail:google.protobuf.testing.TimeStampType)
    ::google::protobuf::internal::ReflectionOps::Merge(from, this);
  } else {
  // @@protoc_insertion_point(generalized_merge_from_cast_success:google.protobuf.testing.TimeStampType)
    UnsafeMergeFrom(*source);
  }
}

void TimeStampType::MergeFrom(const TimeStampType& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:google.protobuf.testing.TimeStampType)
  if (GOOGLE_PREDICT_TRUE(&from != this)) {
    UnsafeMergeFrom(from);
  } else {
    MergeFromFail(__LINE__);
  }
}

void TimeStampType::UnsafeMergeFrom(const TimeStampType& from) {
  GOOGLE_DCHECK(&from != this);
  if (from.has_timestamp()) {
    mutable_timestamp()->::google::protobuf::Timestamp::MergeFrom(from.timestamp());
  }
}

void TimeStampType::CopyFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_copy_from_start:google.protobuf.testing.TimeStampType)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

void TimeStampType::CopyFrom(const TimeStampType& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:google.protobuf.testing.TimeStampType)
  if (&from == this) return;
  Clear();
  UnsafeMergeFrom(from);
}

bool TimeStampType::IsInitialized() const {

  return true;
}

void TimeStampType::Swap(TimeStampType* other) {
  if (other == this) return;
  InternalSwap(other);
}
void TimeStampType::InternalSwap(TimeStampType* other) {
  std::swap(timestamp_, other->timestamp_);
  _internal_metadata_.Swap(&other->_internal_metadata_);
  std::swap(_cached_size_, other->_cached_size_);
}

::google::protobuf::Metadata TimeStampType::GetMetadata() const {
  protobuf_AssignDescriptorsOnce();
  ::google::protobuf::Metadata metadata;
  metadata.descriptor = TimeStampType_descriptor_;
  metadata.reflection = TimeStampType_reflection_;
  return metadata;
}

#if PROTOBUF_INLINE_NOT_IN_HEADERS
// TimeStampType

// optional .google.protobuf.Timestamp timestamp = 1;
bool TimeStampType::has_timestamp() const {
  return this != internal_default_instance() && timestamp_ != NULL;
}
void TimeStampType::clear_timestamp() {
  if (GetArenaNoVirtual() == NULL && timestamp_ != NULL) delete timestamp_;
  timestamp_ = NULL;
}
const ::google::protobuf::Timestamp& TimeStampType::timestamp() const {
  // @@protoc_insertion_point(field_get:google.protobuf.testing.TimeStampType.timestamp)
  return timestamp_ != NULL ? *timestamp_
                         : *::google::protobuf::Timestamp::internal_default_instance();
}
::google::protobuf::Timestamp* TimeStampType::mutable_timestamp() {
  
  if (timestamp_ == NULL) {
    timestamp_ = new ::google::protobuf::Timestamp;
  }
  // @@protoc_insertion_point(field_mutable:google.protobuf.testing.TimeStampType.timestamp)
  return timestamp_;
}
::google::protobuf::Timestamp* TimeStampType::release_timestamp() {
  // @@protoc_insertion_point(field_release:google.protobuf.testing.TimeStampType.timestamp)
  
  ::google::protobuf::Timestamp* temp = timestamp_;
  timestamp_ = NULL;
  return temp;
}
void TimeStampType::set_allocated_timestamp(::google::protobuf::Timestamp* timestamp) {
  delete timestamp_;
  if (timestamp != NULL && timestamp->GetArena() != NULL) {
    ::google::protobuf::Timestamp* new_timestamp = new ::google::protobuf::Timestamp;
    new_timestamp->CopyFrom(*timestamp);
    timestamp = new_timestamp;
  }
  timestamp_ = timestamp;
  if (timestamp) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_set_allocated:google.protobuf.testing.TimeStampType.timestamp)
}

inline const TimeStampType* TimeStampType::internal_default_instance() {
  return &TimeStampType_default_instance_.get();
}
#endif  // PROTOBUF_INLINE_NOT_IN_HEADERS

// ===================================================================

#if !defined(_MSC_VER) || _MSC_VER >= 1900
const int DurationType::kDurationFieldNumber;
#endif  // !defined(_MSC_VER) || _MSC_VER >= 1900

DurationType::DurationType()
  : ::google::protobuf::Message(), _internal_metadata_(NULL) {
  if (this != internal_default_instance()) protobuf_InitDefaults_google_2fprotobuf_2futil_2finternal_2ftestdata_2ftimestamp_5fduration_2eproto();
  SharedCtor();
  // @@protoc_insertion_point(constructor:google.protobuf.testing.DurationType)
}

void DurationType::InitAsDefaultInstance() {
  duration_ = const_cast< ::google::protobuf::Duration*>(
      ::google::protobuf::Duration::internal_default_instance());
}

DurationType::DurationType(const DurationType& from)
  : ::google::protobuf::Message(),
    _internal_metadata_(NULL) {
  SharedCtor();
  UnsafeMergeFrom(from);
  // @@protoc_insertion_point(copy_constructor:google.protobuf.testing.DurationType)
}

void DurationType::SharedCtor() {
  duration_ = NULL;
  _cached_size_ = 0;
}

DurationType::~DurationType() {
  // @@protoc_insertion_point(destructor:google.protobuf.testing.DurationType)
  SharedDtor();
}

void DurationType::SharedDtor() {
  if (this != &DurationType_default_instance_.get()) {
    delete duration_;
  }
}

void DurationType::SetCachedSize(int size) const {
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
}
const ::google::protobuf::Descriptor* DurationType::descriptor() {
  protobuf_AssignDescriptorsOnce();
  return DurationType_descriptor_;
}

const DurationType& DurationType::default_instance() {
  protobuf_InitDefaults_google_2fprotobuf_2futil_2finternal_2ftestdata_2ftimestamp_5fduration_2eproto();
  return *internal_default_instance();
}

::google::protobuf::internal::ExplicitlyConstructed<DurationType> DurationType_default_instance_;

DurationType* DurationType::New(::google::protobuf::Arena* arena) const {
  DurationType* n = new DurationType;
  if (arena != NULL) {
    arena->Own(n);
  }
  return n;
}

void DurationType::Clear() {
// @@protoc_insertion_point(message_clear_start:google.protobuf.testing.DurationType)
  if (GetArenaNoVirtual() == NULL && duration_ != NULL) delete duration_;
  duration_ = NULL;
}

bool DurationType::MergePartialFromCodedStream(
    ::google::protobuf::io::CodedInputStream* input) {
#define DO_(EXPRESSION) if (!GOOGLE_PREDICT_TRUE(EXPRESSION)) goto failure
  ::google::protobuf::uint32 tag;
  // @@protoc_insertion_point(parse_start:google.protobuf.testing.DurationType)
  for (;;) {
    ::std::pair< ::google::protobuf::uint32, bool> p = input->ReadTagWithCutoff(127);
    tag = p.first;
    if (!p.second) goto handle_unusual;
    switch (::google::protobuf::internal::WireFormatLite::GetTagFieldNumber(tag)) {
      // optional .google.protobuf.Duration duration = 1;
      case 1: {
        if (tag == 10) {
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessageNoVirtual(
               input, mutable_duration()));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectAtEnd()) goto success;
        break;
      }

      default: {
      handle_unusual:
        if (tag == 0 ||
            ::google::protobuf::internal::WireFormatLite::GetTagWireType(tag) ==
            ::google::protobuf::internal::WireFormatLite::WIRETYPE_END_GROUP) {
          goto success;
        }
        DO_(::google::protobuf::internal::WireFormatLite::SkipField(input, tag));
        break;
      }
    }
  }
success:
  // @@protoc_insertion_point(parse_success:google.protobuf.testing.DurationType)
  return true;
failure:
  // @@protoc_insertion_point(parse_failure:google.protobuf.testing.DurationType)
  return false;
#undef DO_
}

void DurationType::SerializeWithCachedSizes(
    ::google::protobuf::io::CodedOutputStream* output) const {
  // @@protoc_insertion_point(serialize_start:google.protobuf.testing.DurationType)
  // optional .google.protobuf.Duration duration = 1;
  if (this->has_duration()) {
    ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
      1, *this->duration_, output);
  }

  // @@protoc_insertion_point(serialize_end:google.protobuf.testing.DurationType)
}

::google::protobuf::uint8* DurationType::InternalSerializeWithCachedSizesToArray(
    bool deterministic, ::google::protobuf::uint8* target) const {
  (void)deterministic; // Unused
  // @@protoc_insertion_point(serialize_to_array_start:google.protobuf.testing.DurationType)
  // optional .google.protobuf.Duration duration = 1;
  if (this->has_duration()) {
    target = ::google::protobuf::internal::WireFormatLite::
      InternalWriteMessageNoVirtualToArray(
        1, *this->duration_, false, target);
  }

  // @@protoc_insertion_point(serialize_to_array_end:google.protobuf.testing.DurationType)
  return target;
}

size_t DurationType::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:google.protobuf.testing.DurationType)
  size_t total_size = 0;

  // optional .google.protobuf.Duration duration = 1;
  if (this->has_duration()) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::MessageSizeNoVirtual(
        *this->duration_);
  }

  int cached_size = ::google::protobuf::internal::ToCachedSize(total_size);
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = cached_size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
  return total_size;
}

void DurationType::MergeFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_merge_from_start:google.protobuf.testing.DurationType)
  if (GOOGLE_PREDICT_FALSE(&from == this)) MergeFromFail(__LINE__);
  const DurationType* source =
      ::google::protobuf::internal::DynamicCastToGenerated<const DurationType>(
          &from);
  if (source == NULL) {
  // @@protoc_insertion_point(generalized_merge_from_cast_fail:google.protobuf.testing.DurationType)
    ::google::protobuf::internal::ReflectionOps::Merge(from, this);
  } else {
  // @@protoc_insertion_point(generalized_merge_from_cast_success:google.protobuf.testing.DurationType)
    UnsafeMergeFrom(*source);
  }
}

void DurationType::MergeFrom(const DurationType& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:google.protobuf.testing.DurationType)
  if (GOOGLE_PREDICT_TRUE(&from != this)) {
    UnsafeMergeFrom(from);
  } else {
    MergeFromFail(__LINE__);
  }
}

void DurationType::UnsafeMergeFrom(const DurationType& from) {
  GOOGLE_DCHECK(&from != this);
  if (from.has_duration()) {
    mutable_duration()->::google::protobuf::Duration::MergeFrom(from.duration());
  }
}

void DurationType::CopyFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_copy_from_start:google.protobuf.testing.DurationType)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

void DurationType::CopyFrom(const DurationType& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:google.protobuf.testing.DurationType)
  if (&from == this) return;
  Clear();
  UnsafeMergeFrom(from);
}

bool DurationType::IsInitialized() const {

  return true;
}

void DurationType::Swap(DurationType* other) {
  if (other == this) return;
  InternalSwap(other);
}
void DurationType::InternalSwap(DurationType* other) {
  std::swap(duration_, other->duration_);
  _internal_metadata_.Swap(&other->_internal_metadata_);
  std::swap(_cached_size_, other->_cached_size_);
}

::google::protobuf::Metadata DurationType::GetMetadata() const {
  protobuf_AssignDescriptorsOnce();
  ::google::protobuf::Metadata metadata;
  metadata.descriptor = DurationType_descriptor_;
  metadata.reflection = DurationType_reflection_;
  return metadata;
}

#if PROTOBUF_INLINE_NOT_IN_HEADERS
// DurationType

// optional .google.protobuf.Duration duration = 1;
bool DurationType::has_duration() const {
  return this != internal_default_instance() && duration_ != NULL;
}
void DurationType::clear_duration() {
  if (GetArenaNoVirtual() == NULL && duration_ != NULL) delete duration_;
  duration_ = NULL;
}
const ::google::protobuf::Duration& DurationType::duration() const {
  // @@protoc_insertion_point(field_get:google.protobuf.testing.DurationType.duration)
  return duration_ != NULL ? *duration_
                         : *::google::protobuf::Duration::internal_default_instance();
}
::google::protobuf::Duration* DurationType::mutable_duration() {
  
  if (duration_ == NULL) {
    duration_ = new ::google::protobuf::Duration;
  }
  // @@protoc_insertion_point(field_mutable:google.protobuf.testing.DurationType.duration)
  return duration_;
}
::google::protobuf::Duration* DurationType::release_duration() {
  // @@protoc_insertion_point(field_release:google.protobuf.testing.DurationType.duration)
  
  ::google::protobuf::Duration* temp = duration_;
  duration_ = NULL;
  return temp;
}
void DurationType::set_allocated_duration(::google::protobuf::Duration* duration) {
  delete duration_;
  if (duration != NULL && duration->GetArena() != NULL) {
    ::google::protobuf::Duration* new_duration = new ::google::protobuf::Duration;
    new_duration->CopyFrom(*duration);
    duration = new_duration;
  }
  duration_ = duration;
  if (duration) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_set_allocated:google.protobuf.testing.DurationType.duration)
}

inline const DurationType* DurationType::internal_default_instance() {
  return &DurationType_default_instance_.get();
}
#endif  // PROTOBUF_INLINE_NOT_IN_HEADERS

// ===================================================================

#if !defined(_MSC_VER) || _MSC_VER >= 1900
const int TimestampDuration::kTsFieldNumber;
const int TimestampDuration::kDurFieldNumber;
const int TimestampDuration::kRepTsFieldNumber;
#endif  // !defined(_MSC_VER) || _MSC_VER >= 1900

TimestampDuration::TimestampDuration()
  : ::google::protobuf::Message(), _internal_metadata_(NULL) {
  if (this != internal_default_instance()) protobuf_InitDefaults_google_2fprotobuf_2futil_2finternal_2ftestdata_2ftimestamp_5fduration_2eproto();
  SharedCtor();
  // @@protoc_insertion_point(constructor:google.protobuf.testing.TimestampDuration)
}

void TimestampDuration::InitAsDefaultInstance() {
  ts_ = const_cast< ::google::protobuf::Timestamp*>(
      ::google::protobuf::Timestamp::internal_default_instance());
  dur_ = const_cast< ::google::protobuf::Duration*>(
      ::google::protobuf::Duration::internal_default_instance());
}

TimestampDuration::TimestampDuration(const TimestampDuration& from)
  : ::google::protobuf::Message(),
    _internal_metadata_(NULL) {
  SharedCtor();
  UnsafeMergeFrom(from);
  // @@protoc_insertion_point(copy_constructor:google.protobuf.testing.TimestampDuration)
}

void TimestampDuration::SharedCtor() {
  ts_ = NULL;
  dur_ = NULL;
  _cached_size_ = 0;
}

TimestampDuration::~TimestampDuration() {
  // @@protoc_insertion_point(destructor:google.protobuf.testing.TimestampDuration)
  SharedDtor();
}

void TimestampDuration::SharedDtor() {
  if (this != &TimestampDuration_default_instance_.get()) {
    delete ts_;
    delete dur_;
  }
}

void TimestampDuration::SetCachedSize(int size) const {
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
}
const ::google::protobuf::Descriptor* TimestampDuration::descriptor() {
  protobuf_AssignDescriptorsOnce();
  return TimestampDuration_descriptor_;
}

const TimestampDuration& TimestampDuration::default_instance() {
  protobuf_InitDefaults_google_2fprotobuf_2futil_2finternal_2ftestdata_2ftimestamp_5fduration_2eproto();
  return *internal_default_instance();
}

::google::protobuf::internal::ExplicitlyConstructed<TimestampDuration> TimestampDuration_default_instance_;

TimestampDuration* TimestampDuration::New(::google::protobuf::Arena* arena) const {
  TimestampDuration* n = new TimestampDuration;
  if (arena != NULL) {
    arena->Own(n);
  }
  return n;
}

void TimestampDuration::Clear() {
// @@protoc_insertion_point(message_clear_start:google.protobuf.testing.TimestampDuration)
  if (GetArenaNoVirtual() == NULL && ts_ != NULL) delete ts_;
  ts_ = NULL;
  if (GetArenaNoVirtual() == NULL && dur_ != NULL) delete dur_;
  dur_ = NULL;
  rep_ts_.Clear();
}

bool TimestampDuration::MergePartialFromCodedStream(
    ::google::protobuf::io::CodedInputStream* input) {
#define DO_(EXPRESSION) if (!GOOGLE_PREDICT_TRUE(EXPRESSION)) goto failure
  ::google::protobuf::uint32 tag;
  // @@protoc_insertion_point(parse_start:google.protobuf.testing.TimestampDuration)
  for (;;) {
    ::std::pair< ::google::protobuf::uint32, bool> p = input->ReadTagWithCutoff(127);
    tag = p.first;
    if (!p.second) goto handle_unusual;
    switch (::google::protobuf::internal::WireFormatLite::GetTagFieldNumber(tag)) {
      // optional .google.protobuf.Timestamp ts = 1;
      case 1: {
        if (tag == 10) {
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessageNoVirtual(
               input, mutable_ts()));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(18)) goto parse_dur;
        break;
      }

      // optional .google.protobuf.Duration dur = 2;
      case 2: {
        if (tag == 18) {
         parse_dur:
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessageNoVirtual(
               input, mutable_dur()));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(26)) goto parse_rep_ts;
        break;
      }

      // repeated .google.protobuf.Timestamp rep_ts = 3;
      case 3: {
        if (tag == 26) {
         parse_rep_ts:
          DO_(input->IncrementRecursionDepth());
         parse_loop_rep_ts:
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessageNoVirtualNoRecursionDepth(
                input, add_rep_ts()));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(26)) goto parse_loop_rep_ts;
        input->UnsafeDecrementRecursionDepth();
        if (input->ExpectAtEnd()) goto success;
        break;
      }

      default: {
      handle_unusual:
        if (tag == 0 ||
            ::google::protobuf::internal::WireFormatLite::GetTagWireType(tag) ==
            ::google::protobuf::internal::WireFormatLite::WIRETYPE_END_GROUP) {
          goto success;
        }
        DO_(::google::protobuf::internal::WireFormatLite::SkipField(input, tag));
        break;
      }
    }
  }
success:
  // @@protoc_insertion_point(parse_success:google.protobuf.testing.TimestampDuration)
  return true;
failure:
  // @@protoc_insertion_point(parse_failure:google.protobuf.testing.TimestampDuration)
  return false;
#undef DO_
}

void TimestampDuration::SerializeWithCachedSizes(
    ::google::protobuf::io::CodedOutputStream* output) const {
  // @@protoc_insertion_point(serialize_start:google.protobuf.testing.TimestampDuration)
  // optional .google.protobuf.Timestamp ts = 1;
  if (this->has_ts()) {
    ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
      1, *this->ts_, output);
  }

  // optional .google.protobuf.Duration dur = 2;
  if (this->has_dur()) {
    ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
      2, *this->dur_, output);
  }

  // repeated .google.protobuf.Timestamp rep_ts = 3;
  for (unsigned int i = 0, n = this->rep_ts_size(); i < n; i++) {
    ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
      3, this->rep_ts(i), output);
  }

  // @@protoc_insertion_point(serialize_end:google.protobuf.testing.TimestampDuration)
}

::google::protobuf::uint8* TimestampDuration::InternalSerializeWithCachedSizesToArray(
    bool deterministic, ::google::protobuf::uint8* target) const {
  (void)deterministic; // Unused
  // @@protoc_insertion_point(serialize_to_array_start:google.protobuf.testing.TimestampDuration)
  // optional .google.protobuf.Timestamp ts = 1;
  if (this->has_ts()) {
    target = ::google::protobuf::internal::WireFormatLite::
      InternalWriteMessageNoVirtualToArray(
        1, *this->ts_, false, target);
  }

  // optional .google.protobuf.Duration dur = 2;
  if (this->has_dur()) {
    target = ::google::protobuf::internal::WireFormatLite::
      InternalWriteMessageNoVirtualToArray(
        2, *this->dur_, false, target);
  }

  // repeated .google.protobuf.Timestamp rep_ts = 3;
  for (unsigned int i = 0, n = this->rep_ts_size(); i < n; i++) {
    target = ::google::protobuf::internal::WireFormatLite::
      InternalWriteMessageNoVirtualToArray(
        3, this->rep_ts(i), false, target);
  }

  // @@protoc_insertion_point(serialize_to_array_end:google.protobuf.testing.TimestampDuration)
  return target;
}

size_t TimestampDuration::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:google.protobuf.testing.TimestampDuration)
  size_t total_size = 0;

  // optional .google.protobuf.Timestamp ts = 1;
  if (this->has_ts()) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::MessageSizeNoVirtual(
        *this->ts_);
  }

  // optional .google.protobuf.Duration dur = 2;
  if (this->has_dur()) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::MessageSizeNoVirtual(
        *this->dur_);
  }

  // repeated .google.protobuf.Timestamp rep_ts = 3;
  {
    unsigned int count = this->rep_ts_size();
    total_size += 1UL * count;
    for (unsigned int i = 0; i < count; i++) {
      total_size +=
        ::google::protobuf::internal::WireFormatLite::MessageSizeNoVirtual(
          this->rep_ts(i));
    }
  }

  int cached_size = ::google::protobuf::internal::ToCachedSize(total_size);
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = cached_size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
  return total_size;
}

void TimestampDuration::MergeFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_merge_from_start:google.protobuf.testing.TimestampDuration)
  if (GOOGLE_PREDICT_FALSE(&from == this)) MergeFromFail(__LINE__);
  const TimestampDuration* source =
      ::google::protobuf::internal::DynamicCastToGenerated<const TimestampDuration>(
          &from);
  if (source == NULL) {
  // @@protoc_insertion_point(generalized_merge_from_cast_fail:google.protobuf.testing.TimestampDuration)
    ::google::protobuf::internal::ReflectionOps::Merge(from, this);
  } else {
  // @@protoc_insertion_point(generalized_merge_from_cast_success:google.protobuf.testing.TimestampDuration)
    UnsafeMergeFrom(*source);
  }
}

void TimestampDuration::MergeFrom(const TimestampDuration& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:google.protobuf.testing.TimestampDuration)
  if (GOOGLE_PREDICT_TRUE(&from != this)) {
    UnsafeMergeFrom(from);
  } else {
    MergeFromFail(__LINE__);
  }
}

void TimestampDuration::UnsafeMergeFrom(const TimestampDuration& from) {
  GOOGLE_DCHECK(&from != this);
  rep_ts_.MergeFrom(from.rep_ts_);
  if (from.has_ts()) {
    mutable_ts()->::google::protobuf::Timestamp::MergeFrom(from.ts());
  }
  if (from.has_dur()) {
    mutable_dur()->::google::protobuf::Duration::MergeFrom(from.dur());
  }
}

void TimestampDuration::CopyFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_copy_from_start:google.protobuf.testing.TimestampDuration)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

void TimestampDuration::CopyFrom(const TimestampDuration& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:google.protobuf.testing.TimestampDuration)
  if (&from == this) return;
  Clear();
  UnsafeMergeFrom(from);
}

bool TimestampDuration::IsInitialized() const {

  return true;
}

void TimestampDuration::Swap(TimestampDuration* other) {
  if (other == this) return;
  InternalSwap(other);
}
void TimestampDuration::InternalSwap(TimestampDuration* other) {
  std::swap(ts_, other->ts_);
  std::swap(dur_, other->dur_);
  rep_ts_.UnsafeArenaSwap(&other->rep_ts_);
  _internal_metadata_.Swap(&other->_internal_metadata_);
  std::swap(_cached_size_, other->_cached_size_);
}

::google::protobuf::Metadata TimestampDuration::GetMetadata() const {
  protobuf_AssignDescriptorsOnce();
  ::google::protobuf::Metadata metadata;
  metadata.descriptor = TimestampDuration_descriptor_;
  metadata.reflection = TimestampDuration_reflection_;
  return metadata;
}

#if PROTOBUF_INLINE_NOT_IN_HEADERS
// TimestampDuration

// optional .google.protobuf.Timestamp ts = 1;
bool TimestampDuration::has_ts() const {
  return this != internal_default_instance() && ts_ != NULL;
}
void TimestampDuration::clear_ts() {
  if (GetArenaNoVirtual() == NULL && ts_ != NULL) delete ts_;
  ts_ = NULL;
}
const ::google::protobuf::Timestamp& TimestampDuration::ts() const {
  // @@protoc_insertion_point(field_get:google.protobuf.testing.TimestampDuration.ts)
  return ts_ != NULL ? *ts_
                         : *::google::protobuf::Timestamp::internal_default_instance();
}
::google::protobuf::Timestamp* TimestampDuration::mutable_ts() {
  
  if (ts_ == NULL) {
    ts_ = new ::google::protobuf::Timestamp;
  }
  // @@protoc_insertion_point(field_mutable:google.protobuf.testing.TimestampDuration.ts)
  return ts_;
}
::google::protobuf::Timestamp* TimestampDuration::release_ts() {
  // @@protoc_insertion_point(field_release:google.protobuf.testing.TimestampDuration.ts)
  
  ::google::protobuf::Timestamp* temp = ts_;
  ts_ = NULL;
  return temp;
}
void TimestampDuration::set_allocated_ts(::google::protobuf::Timestamp* ts) {
  delete ts_;
  if (ts != NULL && ts->GetArena() != NULL) {
    ::google::protobuf::Timestamp* new_ts = new ::google::protobuf::Timestamp;
    new_ts->CopyFrom(*ts);
    ts = new_ts;
  }
  ts_ = ts;
  if (ts) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_set_allocated:google.protobuf.testing.TimestampDuration.ts)
}

// optional .google.protobuf.Duration dur = 2;
bool TimestampDuration::has_dur() const {
  return this != internal_default_instance() && dur_ != NULL;
}
void TimestampDuration::clear_dur() {
  if (GetArenaNoVirtual() == NULL && dur_ != NULL) delete dur_;
  dur_ = NULL;
}
const ::google::protobuf::Duration& TimestampDuration::dur() const {
  // @@protoc_insertion_point(field_get:google.protobuf.testing.TimestampDuration.dur)
  return dur_ != NULL ? *dur_
                         : *::google::protobuf::Duration::internal_default_instance();
}
::google::protobuf::Duration* TimestampDuration::mutable_dur() {
  
  if (dur_ == NULL) {
    dur_ = new ::google::protobuf::Duration;
  }
  // @@protoc_insertion_point(field_mutable:google.protobuf.testing.TimestampDuration.dur)
  return dur_;
}
::google::protobuf::Duration* TimestampDuration::release_dur() {
  // @@protoc_insertion_point(field_release:google.protobuf.testing.TimestampDuration.dur)
  
  ::google::protobuf::Duration* temp = dur_;
  dur_ = NULL;
  return temp;
}
void TimestampDuration::set_allocated_dur(::google::protobuf::Duration* dur) {
  delete dur_;
  if (dur != NULL && dur->GetArena() != NULL) {
    ::google::protobuf::Duration* new_dur = new ::google::protobuf::Duration;
    new_dur->CopyFrom(*dur);
    dur = new_dur;
  }
  dur_ = dur;
  if (dur) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_set_allocated:google.protobuf.testing.TimestampDuration.dur)
}

// repeated .google.protobuf.Timestamp rep_ts = 3;
int TimestampDuration::rep_ts_size() const {
  return rep_ts_.size();
}
void TimestampDuration::clear_rep_ts() {
  rep_ts_.Clear();
}
const ::google::protobuf::Timestamp& TimestampDuration::rep_ts(int index) const {
  // @@protoc_insertion_point(field_get:google.protobuf.testing.TimestampDuration.rep_ts)
  return rep_ts_.Get(index);
}
::google::protobuf::Timestamp* TimestampDuration::mutable_rep_ts(int index) {
  // @@protoc_insertion_point(field_mutable:google.protobuf.testing.TimestampDuration.rep_ts)
  return rep_ts_.Mutable(index);
}
::google::protobuf::Timestamp* TimestampDuration::add_rep_ts() {
  // @@protoc_insertion_point(field_add:google.protobuf.testing.TimestampDuration.rep_ts)
  return rep_ts_.Add();
}
::google::protobuf::RepeatedPtrField< ::google::protobuf::Timestamp >*
TimestampDuration::mutable_rep_ts() {
  // @@protoc_insertion_point(field_mutable_list:google.protobuf.testing.TimestampDuration.rep_ts)
  return &rep_ts_;
}
const ::google::protobuf::RepeatedPtrField< ::google::protobuf::Timestamp >&
TimestampDuration::rep_ts() const {
  // @@protoc_insertion_point(field_list:google.protobuf.testing.TimestampDuration.rep_ts)
  return rep_ts_;
}

inline const TimestampDuration* TimestampDuration::internal_default_instance() {
  return &TimestampDuration_default_instance_.get();
}
#endif  // PROTOBUF_INLINE_NOT_IN_HEADERS

// @@protoc_insertion_point(namespace_scope)

}  // namespace testing
}  // namespace protobuf
}  // namespace google

// @@protoc_insertion_point(global_scope)

// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: google/protobuf/util/json_format_proto3.proto

#define INTERNAL_SUPPRESS_PROTOBUF_FIELD_DEPRECATION
#include <google/protobuf/util/json_format_proto3.pb.h>

#include <algorithm>

#include <google/protobuf/stubs/common.h>
#include <google/protobuf/stubs/port.h>
#include <google/protobuf/stubs/once.h>
#include <google/protobuf/io/coded_stream.h>
#include <google/protobuf/wire_format_lite_inl.h>
#include <google/protobuf/descriptor.h>
#include <google/protobuf/generated_message_reflection.h>
#include <google/protobuf/reflection_ops.h>
#include <google/protobuf/wire_format.h>
// @@protoc_insertion_point(includes)

namespace proto3 {

namespace {

const ::google::protobuf::Descriptor* MessageType_descriptor_ = NULL;
const ::google::protobuf::internal::GeneratedMessageReflection*
  MessageType_reflection_ = NULL;
const ::google::protobuf::Descriptor* TestMessage_descriptor_ = NULL;
const ::google::protobuf::internal::GeneratedMessageReflection*
  TestMessage_reflection_ = NULL;
const ::google::protobuf::Descriptor* TestOneof_descriptor_ = NULL;
const ::google::protobuf::internal::GeneratedMessageReflection*
  TestOneof_reflection_ = NULL;
struct TestOneofOneofInstance {
  ::google::protobuf::int32 oneof_int32_value_;
  ::google::protobuf::internal::ArenaStringPtr oneof_string_value_;
  ::google::protobuf::internal::ArenaStringPtr oneof_bytes_value_;
  int oneof_enum_value_;
  const ::proto3::MessageType* oneof_message_value_;
}* TestOneof_default_oneof_instance_ = NULL;
const ::google::protobuf::Descriptor* TestMap_descriptor_ = NULL;
const ::google::protobuf::internal::GeneratedMessageReflection*
  TestMap_reflection_ = NULL;
const ::google::protobuf::Descriptor* TestMap_BoolMapEntry_descriptor_ = NULL;
const ::google::protobuf::Descriptor* TestMap_Int32MapEntry_descriptor_ = NULL;
const ::google::protobuf::Descriptor* TestMap_Int64MapEntry_descriptor_ = NULL;
const ::google::protobuf::Descriptor* TestMap_Uint32MapEntry_descriptor_ = NULL;
const ::google::protobuf::Descriptor* TestMap_Uint64MapEntry_descriptor_ = NULL;
const ::google::protobuf::Descriptor* TestMap_StringMapEntry_descriptor_ = NULL;
const ::google::protobuf::Descriptor* TestNestedMap_descriptor_ = NULL;
const ::google::protobuf::internal::GeneratedMessageReflection*
  TestNestedMap_reflection_ = NULL;
const ::google::protobuf::Descriptor* TestNestedMap_BoolMapEntry_descriptor_ = NULL;
const ::google::protobuf::Descriptor* TestNestedMap_Int32MapEntry_descriptor_ = NULL;
const ::google::protobuf::Descriptor* TestNestedMap_Int64MapEntry_descriptor_ = NULL;
const ::google::protobuf::Descriptor* TestNestedMap_Uint32MapEntry_descriptor_ = NULL;
const ::google::protobuf::Descriptor* TestNestedMap_Uint64MapEntry_descriptor_ = NULL;
const ::google::protobuf::Descriptor* TestNestedMap_StringMapEntry_descriptor_ = NULL;
const ::google::protobuf::Descriptor* TestNestedMap_MapMapEntry_descriptor_ = NULL;
const ::google::protobuf::Descriptor* TestWrapper_descriptor_ = NULL;
const ::google::protobuf::internal::GeneratedMessageReflection*
  TestWrapper_reflection_ = NULL;
const ::google::protobuf::Descriptor* TestTimestamp_descriptor_ = NULL;
const ::google::protobuf::internal::GeneratedMessageReflection*
  TestTimestamp_reflection_ = NULL;
const ::google::protobuf::Descriptor* TestDuration_descriptor_ = NULL;
const ::google::protobuf::internal::GeneratedMessageReflection*
  TestDuration_reflection_ = NULL;
const ::google::protobuf::Descriptor* TestFieldMask_descriptor_ = NULL;
const ::google::protobuf::internal::GeneratedMessageReflection*
  TestFieldMask_reflection_ = NULL;
const ::google::protobuf::Descriptor* TestStruct_descriptor_ = NULL;
const ::google::protobuf::internal::GeneratedMessageReflection*
  TestStruct_reflection_ = NULL;
const ::google::protobuf::Descriptor* TestAny_descriptor_ = NULL;
const ::google::protobuf::internal::GeneratedMessageReflection*
  TestAny_reflection_ = NULL;
const ::google::protobuf::Descriptor* TestValue_descriptor_ = NULL;
const ::google::protobuf::internal::GeneratedMessageReflection*
  TestValue_reflection_ = NULL;
const ::google::protobuf::Descriptor* TestListValue_descriptor_ = NULL;
const ::google::protobuf::internal::GeneratedMessageReflection*
  TestListValue_reflection_ = NULL;
const ::google::protobuf::Descriptor* TestBoolValue_descriptor_ = NULL;
const ::google::protobuf::internal::GeneratedMessageReflection*
  TestBoolValue_reflection_ = NULL;
const ::google::protobuf::Descriptor* TestBoolValue_BoolMapEntry_descriptor_ = NULL;
const ::google::protobuf::Descriptor* TestCustomJsonName_descriptor_ = NULL;
const ::google::protobuf::internal::GeneratedMessageReflection*
  TestCustomJsonName_reflection_ = NULL;
const ::google::protobuf::Descriptor* TestExtensions_descriptor_ = NULL;
const ::google::protobuf::internal::GeneratedMessageReflection*
  TestExtensions_reflection_ = NULL;
const ::google::protobuf::EnumDescriptor* EnumType_descriptor_ = NULL;

}  // namespace


void protobuf_AssignDesc_google_2fprotobuf_2futil_2fjson_5fformat_5fproto3_2eproto() GOOGLE_ATTRIBUTE_COLD;
void protobuf_AssignDesc_google_2fprotobuf_2futil_2fjson_5fformat_5fproto3_2eproto() {
  protobuf_AddDesc_google_2fprotobuf_2futil_2fjson_5fformat_5fproto3_2eproto();
  const ::google::protobuf::FileDescriptor* file =
    ::google::protobuf::DescriptorPool::generated_pool()->FindFileByName(
      "google/protobuf/util/json_format_proto3.proto");
  GOOGLE_CHECK(file != NULL);
  MessageType_descriptor_ = file->message_type(0);
  static const int MessageType_offsets_[1] = {
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MessageType, value_),
  };
  MessageType_reflection_ =
    ::google::protobuf::internal::GeneratedMessageReflection::NewGeneratedMessageReflection(
      MessageType_descriptor_,
      MessageType::internal_default_instance(),
      MessageType_offsets_,
      -1,
      -1,
      -1,
      sizeof(MessageType),
      GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MessageType, _internal_metadata_));
  TestMessage_descriptor_ = file->message_type(1);
  static const int TestMessage_offsets_[22] = {
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(TestMessage, bool_value_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(TestMessage, int32_value_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(TestMessage, int64_value_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(TestMessage, uint32_value_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(TestMessage, uint64_value_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(TestMessage, float_value_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(TestMessage, double_value_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(TestMessage, string_value_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(TestMessage, bytes_value_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(TestMessage, enum_value_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(TestMessage, message_value_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(TestMessage, repeated_bool_value_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(TestMessage, repeated_int32_value_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(TestMessage, repeated_int64_value_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(TestMessage, repeated_uint32_value_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(TestMessage, repeated_uint64_value_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(TestMessage, repeated_float_value_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(TestMessage, repeated_double_value_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(TestMessage, repeated_string_value_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(TestMessage, repeated_bytes_value_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(TestMessage, repeated_enum_value_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(TestMessage, repeated_message_value_),
  };
  TestMessage_reflection_ =
    ::google::protobuf::internal::GeneratedMessageReflection::NewGeneratedMessageReflection(
      TestMessage_descriptor_,
      TestMessage::internal_default_instance(),
      TestMessage_offsets_,
      -1,
      -1,
      -1,
      sizeof(TestMessage),
      GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(TestMessage, _internal_metadata_));
  TestOneof_descriptor_ = file->message_type(2);
  static const int TestOneof_offsets_[6] = {
    PROTO2_GENERATED_DEFAULT_ONEOF_FIELD_OFFSET(TestOneof_default_oneof_instance_, oneof_int32_value_),
    PROTO2_GENERATED_DEFAULT_ONEOF_FIELD_OFFSET(TestOneof_default_oneof_instance_, oneof_string_value_),
    PROTO2_GENERATED_DEFAULT_ONEOF_FIELD_OFFSET(TestOneof_default_oneof_instance_, oneof_bytes_value_),
    PROTO2_GENERATED_DEFAULT_ONEOF_FIELD_OFFSET(TestOneof_default_oneof_instance_, oneof_enum_value_),
    PROTO2_GENERATED_DEFAULT_ONEOF_FIELD_OFFSET(TestOneof_default_oneof_instance_, oneof_message_value_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(TestOneof, oneof_value_),
  };
  TestOneof_reflection_ =
    ::google::protobuf::internal::GeneratedMessageReflection::NewGeneratedMessageReflection(
      TestOneof_descriptor_,
      TestOneof::internal_default_instance(),
      TestOneof_offsets_,
      -1,
      -1,
      -1,
      TestOneof_default_oneof_instance_,
      GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(TestOneof, _oneof_case_[0]),
      sizeof(TestOneof),
      GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(TestOneof, _internal_metadata_));
  TestMap_descriptor_ = file->message_type(3);
  static const int TestMap_offsets_[6] = {
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(TestMap, bool_map_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(TestMap, int32_map_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(TestMap, int64_map_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(TestMap, uint32_map_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(TestMap, uint64_map_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(TestMap, string_map_),
  };
  TestMap_reflection_ =
    ::google::protobuf::internal::GeneratedMessageReflection::NewGeneratedMessageReflection(
      TestMap_descriptor_,
      TestMap::internal_default_instance(),
      TestMap_offsets_,
      -1,
      -1,
      -1,
      sizeof(TestMap),
      GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(TestMap, _internal_metadata_));
  TestMap_BoolMapEntry_descriptor_ = TestMap_descriptor_->nested_type(0);
  TestMap_Int32MapEntry_descriptor_ = TestMap_descriptor_->nested_type(1);
  TestMap_Int64MapEntry_descriptor_ = TestMap_descriptor_->nested_type(2);
  TestMap_Uint32MapEntry_descriptor_ = TestMap_descriptor_->nested_type(3);
  TestMap_Uint64MapEntry_descriptor_ = TestMap_descriptor_->nested_type(4);
  TestMap_StringMapEntry_descriptor_ = TestMap_descriptor_->nested_type(5);
  TestNestedMap_descriptor_ = file->message_type(4);
  static const int TestNestedMap_offsets_[7] = {
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(TestNestedMap, bool_map_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(TestNestedMap, int32_map_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(TestNestedMap, int64_map_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(TestNestedMap, uint32_map_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(TestNestedMap, uint64_map_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(TestNestedMap, string_map_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(TestNestedMap, map_map_),
  };
  TestNestedMap_reflection_ =
    ::google::protobuf::internal::GeneratedMessageReflection::NewGeneratedMessageReflection(
      TestNestedMap_descriptor_,
      TestNestedMap::internal_default_instance(),
      TestNestedMap_offsets_,
      -1,
      -1,
      -1,
      sizeof(TestNestedMap),
      GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(TestNestedMap, _internal_metadata_));
  TestNestedMap_BoolMapEntry_descriptor_ = TestNestedMap_descriptor_->nested_type(0);
  TestNestedMap_Int32MapEntry_descriptor_ = TestNestedMap_descriptor_->nested_type(1);
  TestNestedMap_Int64MapEntry_descriptor_ = TestNestedMap_descriptor_->nested_type(2);
  TestNestedMap_Uint32MapEntry_descriptor_ = TestNestedMap_descriptor_->nested_type(3);
  TestNestedMap_Uint64MapEntry_descriptor_ = TestNestedMap_descriptor_->nested_type(4);
  TestNestedMap_StringMapEntry_descriptor_ = TestNestedMap_descriptor_->nested_type(5);
  TestNestedMap_MapMapEntry_descriptor_ = TestNestedMap_descriptor_->nested_type(6);
  TestWrapper_descriptor_ = file->message_type(5);
  static const int TestWrapper_offsets_[18] = {
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(TestWrapper, bool_value_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(TestWrapper, int32_value_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(TestWrapper, int64_value_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(TestWrapper, uint32_value_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(TestWrapper, uint64_value_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(TestWrapper, float_value_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(TestWrapper, double_value_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(TestWrapper, string_value_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(TestWrapper, bytes_value_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(TestWrapper, repeated_bool_value_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(TestWrapper, repeated_int32_value_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(TestWrapper, repeated_int64_value_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(TestWrapper, repeated_uint32_value_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(TestWrapper, repeated_uint64_value_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(TestWrapper, repeated_float_value_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(TestWrapper, repeated_double_value_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(TestWrapper, repeated_string_value_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(TestWrapper, repeated_bytes_value_),
  };
  TestWrapper_reflection_ =
    ::google::protobuf::internal::GeneratedMessageReflection::NewGeneratedMessageReflection(
      TestWrapper_descriptor_,
      TestWrapper::internal_default_instance(),
      TestWrapper_offsets_,
      -1,
      -1,
      -1,
      sizeof(TestWrapper),
      GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(TestWrapper, _internal_metadata_));
  TestTimestamp_descriptor_ = file->message_type(6);
  static const int TestTimestamp_offsets_[2] = {
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(TestTimestamp, value_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(TestTimestamp, repeated_value_),
  };
  TestTimestamp_reflection_ =
    ::google::protobuf::internal::GeneratedMessageReflection::NewGeneratedMessageReflection(
      TestTimestamp_descriptor_,
      TestTimestamp::internal_default_instance(),
      TestTimestamp_offsets_,
      -1,
      -1,
      -1,
      sizeof(TestTimestamp),
      GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(TestTimestamp, _internal_metadata_));
  TestDuration_descriptor_ = file->message_type(7);
  static const int TestDuration_offsets_[2] = {
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(TestDuration, value_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(TestDuration, repeated_value_),
  };
  TestDuration_reflection_ =
    ::google::protobuf::internal::GeneratedMessageReflection::NewGeneratedMessageReflection(
      TestDuration_descriptor_,
      TestDuration::internal_default_instance(),
      TestDuration_offsets_,
      -1,
      -1,
      -1,
      sizeof(TestDuration),
      GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(TestDuration, _internal_metadata_));
  TestFieldMask_descriptor_ = file->message_type(8);
  static const int TestFieldMask_offsets_[1] = {
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(TestFieldMask, value_),
  };
  TestFieldMask_reflection_ =
    ::google::protobuf::internal::GeneratedMessageReflection::NewGeneratedMessageReflection(
      TestFieldMask_descriptor_,
      TestFieldMask::internal_default_instance(),
      TestFieldMask_offsets_,
      -1,
      -1,
      -1,
      sizeof(TestFieldMask),
      GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(TestFieldMask, _internal_metadata_));
  TestStruct_descriptor_ = file->message_type(9);
  static const int TestStruct_offsets_[2] = {
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(TestStruct, value_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(TestStruct, repeated_value_),
  };
  TestStruct_reflection_ =
    ::google::protobuf::internal::GeneratedMessageReflection::NewGeneratedMessageReflection(
      TestStruct_descriptor_,
      TestStruct::internal_default_instance(),
      TestStruct_offsets_,
      -1,
      -1,
      -1,
      sizeof(TestStruct),
      GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(TestStruct, _internal_metadata_));
  TestAny_descriptor_ = file->message_type(10);
  static const int TestAny_offsets_[2] = {
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(TestAny, value_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(TestAny, repeated_value_),
  };
  TestAny_reflection_ =
    ::google::protobuf::internal::GeneratedMessageReflection::NewGeneratedMessageReflection(
      TestAny_descriptor_,
      TestAny::internal_default_instance(),
      TestAny_offsets_,
      -1,
      -1,
      -1,
      sizeof(TestAny),
      GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(TestAny, _internal_metadata_));
  TestValue_descriptor_ = file->message_type(11);
  static const int TestValue_offsets_[2] = {
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(TestValue, value_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(TestValue, repeated_value_),
  };
  TestValue_reflection_ =
    ::google::protobuf::internal::GeneratedMessageReflection::NewGeneratedMessageReflection(
      TestValue_descriptor_,
      TestValue::internal_default_instance(),
      TestValue_offsets_,
      -1,
      -1,
      -1,
      sizeof(TestValue),
      GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(TestValue, _internal_metadata_));
  TestListValue_descriptor_ = file->message_type(12);
  static const int TestListValue_offsets_[2] = {
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(TestListValue, value_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(TestListValue, repeated_value_),
  };
  TestListValue_reflection_ =
    ::google::protobuf::internal::GeneratedMessageReflection::NewGeneratedMessageReflection(
      TestListValue_descriptor_,
      TestListValue::internal_default_instance(),
      TestListValue_offsets_,
      -1,
      -1,
      -1,
      sizeof(TestListValue),
      GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(TestListValue, _internal_metadata_));
  TestBoolValue_descriptor_ = file->message_type(13);
  static const int TestBoolValue_offsets_[2] = {
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(TestBoolValue, bool_value_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(TestBoolValue, bool_map_),
  };
  TestBoolValue_reflection_ =
    ::google::protobuf::internal::GeneratedMessageReflection::NewGeneratedMessageReflection(
      TestBoolValue_descriptor_,
      TestBoolValue::internal_default_instance(),
      TestBoolValue_offsets_,
      -1,
      -1,
      -1,
      sizeof(TestBoolValue),
      GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(TestBoolValue, _internal_metadata_));
  TestBoolValue_BoolMapEntry_descriptor_ = TestBoolValue_descriptor_->nested_type(0);
  TestCustomJsonName_descriptor_ = file->message_type(14);
  static const int TestCustomJsonName_offsets_[1] = {
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(TestCustomJsonName, value_),
  };
  TestCustomJsonName_reflection_ =
    ::google::protobuf::internal::GeneratedMessageReflection::NewGeneratedMessageReflection(
      TestCustomJsonName_descriptor_,
      TestCustomJsonName::internal_default_instance(),
      TestCustomJsonName_offsets_,
      -1,
      -1,
      -1,
      sizeof(TestCustomJsonName),
      GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(TestCustomJsonName, _internal_metadata_));
  TestExtensions_descriptor_ = file->message_type(15);
  static const int TestExtensions_offsets_[1] = {
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(TestExtensions, extensions_),
  };
  TestExtensions_reflection_ =
    ::google::protobuf::internal::GeneratedMessageReflection::NewGeneratedMessageReflection(
      TestExtensions_descriptor_,
      TestExtensions::internal_default_instance(),
      TestExtensions_offsets_,
      -1,
      -1,
      -1,
      sizeof(TestExtensions),
      GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(TestExtensions, _internal_metadata_));
  EnumType_descriptor_ = file->enum_type(0);
}

namespace {

GOOGLE_PROTOBUF_DECLARE_ONCE(protobuf_AssignDescriptors_once_);
void protobuf_AssignDescriptorsOnce() {
  ::google::protobuf::GoogleOnceInit(&protobuf_AssignDescriptors_once_,
                 &protobuf_AssignDesc_google_2fprotobuf_2futil_2fjson_5fformat_5fproto3_2eproto);
}

void protobuf_RegisterTypes(const ::std::string&) GOOGLE_ATTRIBUTE_COLD;
void protobuf_RegisterTypes(const ::std::string&) {
  protobuf_AssignDescriptorsOnce();
  ::google::protobuf::MessageFactory::InternalRegisterGeneratedMessage(
      MessageType_descriptor_, MessageType::internal_default_instance());
  ::google::protobuf::MessageFactory::InternalRegisterGeneratedMessage(
      TestMessage_descriptor_, TestMessage::internal_default_instance());
  ::google::protobuf::MessageFactory::InternalRegisterGeneratedMessage(
      TestOneof_descriptor_, TestOneof::internal_default_instance());
  ::google::protobuf::MessageFactory::InternalRegisterGeneratedMessage(
      TestMap_descriptor_, TestMap::internal_default_instance());
  ::google::protobuf::MessageFactory::InternalRegisterGeneratedMessage(
        TestMap_BoolMapEntry_descriptor_,
        ::google::protobuf::internal::MapEntry<
            bool,
            ::google::protobuf::int32,
            ::google::protobuf::internal::WireFormatLite::TYPE_BOOL,
            ::google::protobuf::internal::WireFormatLite::TYPE_INT32,
            0>::CreateDefaultInstance(
                TestMap_BoolMapEntry_descriptor_));
  ::google::protobuf::MessageFactory::InternalRegisterGeneratedMessage(
        TestMap_Int32MapEntry_descriptor_,
        ::google::protobuf::internal::MapEntry<
            ::google::protobuf::int32,
            ::google::protobuf::int32,
            ::google::protobuf::internal::WireFormatLite::TYPE_INT32,
            ::google::protobuf::internal::WireFormatLite::TYPE_INT32,
            0>::CreateDefaultInstance(
                TestMap_Int32MapEntry_descriptor_));
  ::google::protobuf::MessageFactory::InternalRegisterGeneratedMessage(
        TestMap_Int64MapEntry_descriptor_,
        ::google::protobuf::internal::MapEntry<
            ::google::protobuf::int64,
            ::google::protobuf::int32,
            ::google::protobuf::internal::WireFormatLite::TYPE_INT64,
            ::google::protobuf::internal::WireFormatLite::TYPE_INT32,
            0>::CreateDefaultInstance(
                TestMap_Int64MapEntry_descriptor_));
  ::google::protobuf::MessageFactory::InternalRegisterGeneratedMessage(
        TestMap_Uint32MapEntry_descriptor_,
        ::google::protobuf::internal::MapEntry<
            ::google::protobuf::uint32,
            ::google::protobuf::int32,
            ::google::protobuf::internal::WireFormatLite::TYPE_UINT32,
            ::google::protobuf::internal::WireFormatLite::TYPE_INT32,
            0>::CreateDefaultInstance(
                TestMap_Uint32MapEntry_descriptor_));
  ::google::protobuf::MessageFactory::InternalRegisterGeneratedMessage(
        TestMap_Uint64MapEntry_descriptor_,
        ::google::protobuf::internal::MapEntry<
            ::google::protobuf::uint64,
            ::google::protobuf::int32,
            ::google::protobuf::internal::WireFormatLite::TYPE_UINT64,
            ::google::protobuf::internal::WireFormatLite::TYPE_INT32,
            0>::CreateDefaultInstance(
                TestMap_Uint64MapEntry_descriptor_));
  ::google::protobuf::MessageFactory::InternalRegisterGeneratedMessage(
        TestMap_StringMapEntry_descriptor_,
        ::google::protobuf::internal::MapEntry<
            ::std::string,
            ::google::protobuf::int32,
            ::google::protobuf::internal::WireFormatLite::TYPE_STRING,
            ::google::protobuf::internal::WireFormatLite::TYPE_INT32,
            0>::CreateDefaultInstance(
                TestMap_StringMapEntry_descriptor_));
  ::google::protobuf::MessageFactory::InternalRegisterGeneratedMessage(
      TestNestedMap_descriptor_, TestNestedMap::internal_default_instance());
  ::google::protobuf::MessageFactory::InternalRegisterGeneratedMessage(
        TestNestedMap_BoolMapEntry_descriptor_,
        ::google::protobuf::internal::MapEntry<
            bool,
            ::google::protobuf::int32,
            ::google::protobuf::internal::WireFormatLite::TYPE_BOOL,
            ::google::protobuf::internal::WireFormatLite::TYPE_INT32,
            0>::CreateDefaultInstance(
                TestNestedMap_BoolMapEntry_descriptor_));
  ::google::protobuf::MessageFactory::InternalRegisterGeneratedMessage(
        TestNestedMap_Int32MapEntry_descriptor_,
        ::google::protobuf::internal::MapEntry<
            ::google::protobuf::int32,
            ::google::protobuf::int32,
            ::google::protobuf::internal::WireFormatLite::TYPE_INT32,
            ::google::protobuf::internal::WireFormatLite::TYPE_INT32,
            0>::CreateDefaultInstance(
                TestNestedMap_Int32MapEntry_descriptor_));
  ::google::protobuf::MessageFactory::InternalRegisterGeneratedMessage(
        TestNestedMap_Int64MapEntry_descriptor_,
        ::google::protobuf::internal::MapEntry<
            ::google::protobuf::int64,
            ::google::protobuf::int32,
            ::google::protobuf::internal::WireFormatLite::TYPE_INT64,
            ::google::protobuf::internal::WireFormatLite::TYPE_INT32,
            0>::CreateDefaultInstance(
                TestNestedMap_Int64MapEntry_descriptor_));
  ::google::protobuf::MessageFactory::InternalRegisterGeneratedMessage(
        TestNestedMap_Uint32MapEntry_descriptor_,
        ::google::protobuf::internal::MapEntry<
            ::google::protobuf::uint32,
            ::google::protobuf::int32,
            ::google::protobuf::internal::WireFormatLite::TYPE_UINT32,
            ::google::protobuf::internal::WireFormatLite::TYPE_INT32,
            0>::CreateDefaultInstance(
                TestNestedMap_Uint32MapEntry_descriptor_));
  ::google::protobuf::MessageFactory::InternalRegisterGeneratedMessage(
        TestNestedMap_Uint64MapEntry_descriptor_,
        ::google::protobuf::internal::MapEntry<
            ::google::protobuf::uint64,
            ::google::protobuf::int32,
            ::google::protobuf::internal::WireFormatLite::TYPE_UINT64,
            ::google::protobuf::internal::WireFormatLite::TYPE_INT32,
            0>::CreateDefaultInstance(
                TestNestedMap_Uint64MapEntry_descriptor_));
  ::google::protobuf::MessageFactory::InternalRegisterGeneratedMessage(
        TestNestedMap_StringMapEntry_descriptor_,
        ::google::protobuf::internal::MapEntry<
            ::std::string,
            ::google::protobuf::int32,
            ::google::protobuf::internal::WireFormatLite::TYPE_STRING,
            ::google::protobuf::internal::WireFormatLite::TYPE_INT32,
            0>::CreateDefaultInstance(
                TestNestedMap_StringMapEntry_descriptor_));
  ::google::protobuf::MessageFactory::InternalRegisterGeneratedMessage(
        TestNestedMap_MapMapEntry_descriptor_,
        ::google::protobuf::internal::MapEntry<
            ::std::string,
            ::proto3::TestNestedMap,
            ::google::protobuf::internal::WireFormatLite::TYPE_STRING,
            ::google::protobuf::internal::WireFormatLite::TYPE_MESSAGE,
            0>::CreateDefaultInstance(
                TestNestedMap_MapMapEntry_descriptor_));
  ::google::protobuf::MessageFactory::InternalRegisterGeneratedMessage(
      TestWrapper_descriptor_, TestWrapper::internal_default_instance());
  ::google::protobuf::MessageFactory::InternalRegisterGeneratedMessage(
      TestTimestamp_descriptor_, TestTimestamp::internal_default_instance());
  ::google::protobuf::MessageFactory::InternalRegisterGeneratedMessage(
      TestDuration_descriptor_, TestDuration::internal_default_instance());
  ::google::protobuf::MessageFactory::InternalRegisterGeneratedMessage(
      TestFieldMask_descriptor_, TestFieldMask::internal_default_instance());
  ::google::protobuf::MessageFactory::InternalRegisterGeneratedMessage(
      TestStruct_descriptor_, TestStruct::internal_default_instance());
  ::google::protobuf::MessageFactory::InternalRegisterGeneratedMessage(
      TestAny_descriptor_, TestAny::internal_default_instance());
  ::google::protobuf::MessageFactory::InternalRegisterGeneratedMessage(
      TestValue_descriptor_, TestValue::internal_default_instance());
  ::google::protobuf::MessageFactory::InternalRegisterGeneratedMessage(
      TestListValue_descriptor_, TestListValue::internal_default_instance());
  ::google::protobuf::MessageFactory::InternalRegisterGeneratedMessage(
      TestBoolValue_descriptor_, TestBoolValue::internal_default_instance());
  ::google::protobuf::MessageFactory::InternalRegisterGeneratedMessage(
        TestBoolValue_BoolMapEntry_descriptor_,
        ::google::protobuf::internal::MapEntry<
            bool,
            ::google::protobuf::int32,
            ::google::protobuf::internal::WireFormatLite::TYPE_BOOL,
            ::google::protobuf::internal::WireFormatLite::TYPE_INT32,
            0>::CreateDefaultInstance(
                TestBoolValue_BoolMapEntry_descriptor_));
  ::google::protobuf::MessageFactory::InternalRegisterGeneratedMessage(
      TestCustomJsonName_descriptor_, TestCustomJsonName::internal_default_instance());
  ::google::protobuf::MessageFactory::InternalRegisterGeneratedMessage(
      TestExtensions_descriptor_, TestExtensions::internal_default_instance());
}

}  // namespace

void protobuf_ShutdownFile_google_2fprotobuf_2futil_2fjson_5fformat_5fproto3_2eproto() {
  MessageType_default_instance_.Shutdown();
  delete MessageType_reflection_;
  TestMessage_default_instance_.Shutdown();
  delete TestMessage_reflection_;
  TestOneof_default_instance_.Shutdown();
  delete TestOneof_default_oneof_instance_;
  delete TestOneof_reflection_;
  TestMap_default_instance_.Shutdown();
  delete TestMap_reflection_;
  TestNestedMap_default_instance_.Shutdown();
  delete TestNestedMap_reflection_;
  TestWrapper_default_instance_.Shutdown();
  delete TestWrapper_reflection_;
  TestTimestamp_default_instance_.Shutdown();
  delete TestTimestamp_reflection_;
  TestDuration_default_instance_.Shutdown();
  delete TestDuration_reflection_;
  TestFieldMask_default_instance_.Shutdown();
  delete TestFieldMask_reflection_;
  TestStruct_default_instance_.Shutdown();
  delete TestStruct_reflection_;
  TestAny_default_instance_.Shutdown();
  delete TestAny_reflection_;
  TestValue_default_instance_.Shutdown();
  delete TestValue_reflection_;
  TestListValue_default_instance_.Shutdown();
  delete TestListValue_reflection_;
  TestBoolValue_default_instance_.Shutdown();
  delete TestBoolValue_reflection_;
  TestCustomJsonName_default_instance_.Shutdown();
  delete TestCustomJsonName_reflection_;
  TestExtensions_default_instance_.Shutdown();
  delete TestExtensions_reflection_;
}

void protobuf_InitDefaults_google_2fprotobuf_2futil_2fjson_5fformat_5fproto3_2eproto_impl() {
  GOOGLE_PROTOBUF_VERIFY_VERSION;

  ::google::protobuf::protobuf_InitDefaults_google_2fprotobuf_2fduration_2eproto();
  ::google::protobuf::protobuf_InitDefaults_google_2fprotobuf_2ftimestamp_2eproto();
  ::google::protobuf::protobuf_InitDefaults_google_2fprotobuf_2fwrappers_2eproto();
  ::google::protobuf::protobuf_InitDefaults_google_2fprotobuf_2fstruct_2eproto();
  ::google::protobuf::protobuf_InitDefaults_google_2fprotobuf_2fany_2eproto();
  ::google::protobuf::protobuf_InitDefaults_google_2fprotobuf_2ffield_5fmask_2eproto();
  ::protobuf_unittest::protobuf_InitDefaults_google_2fprotobuf_2funittest_2eproto();
  MessageType_default_instance_.DefaultConstruct();
  ::google::protobuf::internal::GetEmptyString();
  TestMessage_default_instance_.DefaultConstruct();
  ::google::protobuf::internal::GetEmptyString();
  TestOneof_default_instance_.DefaultConstruct();
  TestOneof_default_oneof_instance_ = new TestOneofOneofInstance();
  ::google::protobuf::internal::GetEmptyString();
  TestMap_default_instance_.DefaultConstruct();
  ::google::protobuf::internal::GetEmptyString();
  ::google::protobuf::internal::GetEmptyString();
  ::google::protobuf::internal::GetEmptyString();
  ::google::protobuf::internal::GetEmptyString();
  ::google::protobuf::internal::GetEmptyString();
  ::google::protobuf::internal::GetEmptyString();
  ::google::protobuf::internal::GetEmptyString();
  TestNestedMap_default_instance_.DefaultConstruct();
  ::google::protobuf::internal::GetEmptyString();
  ::google::protobuf::internal::GetEmptyString();
  ::google::protobuf::internal::GetEmptyString();
  ::google::protobuf::internal::GetEmptyString();
  ::google::protobuf::internal::GetEmptyString();
  ::google::protobuf::internal::GetEmptyString();
  ::google::protobuf::internal::GetEmptyString();
  TestWrapper_default_instance_.DefaultConstruct();
  TestTimestamp_default_instance_.DefaultConstruct();
  TestDuration_default_instance_.DefaultConstruct();
  TestFieldMask_default_instance_.DefaultConstruct();
  TestStruct_default_instance_.DefaultConstruct();
  TestAny_default_instance_.DefaultConstruct();
  TestValue_default_instance_.DefaultConstruct();
  TestListValue_default_instance_.DefaultConstruct();
  TestBoolValue_default_instance_.DefaultConstruct();
  ::google::protobuf::internal::GetEmptyString();
  TestCustomJsonName_default_instance_.DefaultConstruct();
  TestExtensions_default_instance_.DefaultConstruct();
  MessageType_default_instance_.get_mutable()->InitAsDefaultInstance();
  TestMessage_default_instance_.get_mutable()->InitAsDefaultInstance();
  TestOneof_default_instance_.get_mutable()->InitAsDefaultInstance();
  TestMap_default_instance_.get_mutable()->InitAsDefaultInstance();
  TestNestedMap_default_instance_.get_mutable()->InitAsDefaultInstance();
  TestWrapper_default_instance_.get_mutable()->InitAsDefaultInstance();
  TestTimestamp_default_instance_.get_mutable()->InitAsDefaultInstance();
  TestDuration_default_instance_.get_mutable()->InitAsDefaultInstance();
  TestFieldMask_default_instance_.get_mutable()->InitAsDefaultInstance();
  TestStruct_default_instance_.get_mutable()->InitAsDefaultInstance();
  TestAny_default_instance_.get_mutable()->InitAsDefaultInstance();
  TestValue_default_instance_.get_mutable()->InitAsDefaultInstance();
  TestListValue_default_instance_.get_mutable()->InitAsDefaultInstance();
  TestBoolValue_default_instance_.get_mutable()->InitAsDefaultInstance();
  TestCustomJsonName_default_instance_.get_mutable()->InitAsDefaultInstance();
  TestExtensions_default_instance_.get_mutable()->InitAsDefaultInstance();
}

GOOGLE_PROTOBUF_DECLARE_ONCE(protobuf_InitDefaults_google_2fprotobuf_2futil_2fjson_5fformat_5fproto3_2eproto_once_);
void protobuf_InitDefaults_google_2fprotobuf_2futil_2fjson_5fformat_5fproto3_2eproto() {
  ::google::protobuf::GoogleOnceInit(&protobuf_InitDefaults_google_2fprotobuf_2futil_2fjson_5fformat_5fproto3_2eproto_once_,
                 &protobuf_InitDefaults_google_2fprotobuf_2futil_2fjson_5fformat_5fproto3_2eproto_impl);
}
void protobuf_AddDesc_google_2fprotobuf_2futil_2fjson_5fformat_5fproto3_2eproto_impl() {
  GOOGLE_PROTOBUF_VERIFY_VERSION;

  protobuf_InitDefaults_google_2fprotobuf_2futil_2fjson_5fformat_5fproto3_2eproto();
  ::google::protobuf::DescriptorPool::InternalAddGeneratedFile(
    "\n-google/protobuf/util/json_format_proto"
    "3.proto\022\006proto3\032\036google/protobuf/duratio"
    "n.proto\032\037google/protobuf/timestamp.proto"
    "\032\036google/protobuf/wrappers.proto\032\034google"
    "/protobuf/struct.proto\032\031google/protobuf/"
    "any.proto\032 google/protobuf/field_mask.pr"
    "oto\032\036google/protobuf/unittest.proto\"\034\n\013M"
    "essageType\022\r\n\005value\030\001 \001(\005\"\224\005\n\013TestMessag"
    "e\022\022\n\nbool_value\030\001 \001(\010\022\023\n\013int32_value\030\002 \001"
    "(\005\022\023\n\013int64_value\030\003 \001(\003\022\024\n\014uint32_value\030"
    "\004 \001(\r\022\024\n\014uint64_value\030\005 \001(\004\022\023\n\013float_val"
    "ue\030\006 \001(\002\022\024\n\014double_value\030\007 \001(\001\022\024\n\014string"
    "_value\030\010 \001(\t\022\023\n\013bytes_value\030\t \001(\014\022$\n\nenu"
    "m_value\030\n \001(\0162\020.proto3.EnumType\022*\n\rmessa"
    "ge_value\030\013 \001(\0132\023.proto3.MessageType\022\033\n\023r"
    "epeated_bool_value\030\025 \003(\010\022\034\n\024repeated_int"
    "32_value\030\026 \003(\005\022\034\n\024repeated_int64_value\030\027"
    " \003(\003\022\035\n\025repeated_uint32_value\030\030 \003(\r\022\035\n\025r"
    "epeated_uint64_value\030\031 \003(\004\022\034\n\024repeated_f"
    "loat_value\030\032 \003(\002\022\035\n\025repeated_double_valu"
    "e\030\033 \003(\001\022\035\n\025repeated_string_value\030\034 \003(\t\022\034"
    "\n\024repeated_bytes_value\030\035 \003(\014\022-\n\023repeated"
    "_enum_value\030\036 \003(\0162\020.proto3.EnumType\0223\n\026r"
    "epeated_message_value\030\037 \003(\0132\023.proto3.Mes"
    "sageType\"\324\001\n\tTestOneof\022\033\n\021oneof_int32_va"
    "lue\030\001 \001(\005H\000\022\034\n\022oneof_string_value\030\002 \001(\tH"
    "\000\022\033\n\021oneof_bytes_value\030\003 \001(\014H\000\022,\n\020oneof_"
    "enum_value\030\004 \001(\0162\020.proto3.EnumTypeH\000\0222\n\023"
    "oneof_message_value\030\005 \001(\0132\023.proto3.Messa"
    "geTypeH\000B\r\n\013oneof_value\"\341\004\n\007TestMap\022.\n\010b"
    "ool_map\030\001 \003(\0132\034.proto3.TestMap.BoolMapEn"
    "try\0220\n\tint32_map\030\002 \003(\0132\035.proto3.TestMap."
    "Int32MapEntry\0220\n\tint64_map\030\003 \003(\0132\035.proto"
    "3.TestMap.Int64MapEntry\0222\n\nuint32_map\030\004 "
    "\003(\0132\036.proto3.TestMap.Uint32MapEntry\0222\n\nu"
    "int64_map\030\005 \003(\0132\036.proto3.TestMap.Uint64M"
    "apEntry\0222\n\nstring_map\030\006 \003(\0132\036.proto3.Tes"
    "tMap.StringMapEntry\032.\n\014BoolMapEntry\022\013\n\003k"
    "ey\030\001 \001(\010\022\r\n\005value\030\002 \001(\005:\0028\001\032/\n\rInt32MapE"
    "ntry\022\013\n\003key\030\001 \001(\005\022\r\n\005value\030\002 \001(\005:\0028\001\032/\n\r"
    "Int64MapEntry\022\013\n\003key\030\001 \001(\003\022\r\n\005value\030\002 \001("
    "\005:\0028\001\0320\n\016Uint32MapEntry\022\013\n\003key\030\001 \001(\r\022\r\n\005"
    "value\030\002 \001(\005:\0028\001\0320\n\016Uint64MapEntry\022\013\n\003key"
    "\030\001 \001(\004\022\r\n\005value\030\002 \001(\005:\0028\001\0320\n\016StringMapEn"
    "try\022\013\n\003key\030\001 \001(\t\022\r\n\005value\030\002 \001(\005:\0028\001\"\205\006\n\r"
    "TestNestedMap\0224\n\010bool_map\030\001 \003(\0132\".proto3"
    ".TestNestedMap.BoolMapEntry\0226\n\tint32_map"
    "\030\002 \003(\0132#.proto3.TestNestedMap.Int32MapEn"
    "try\0226\n\tint64_map\030\003 \003(\0132#.proto3.TestNest"
    "edMap.Int64MapEntry\0228\n\nuint32_map\030\004 \003(\0132"
    "$.proto3.TestNestedMap.Uint32MapEntry\0228\n"
    "\nuint64_map\030\005 \003(\0132$.proto3.TestNestedMap"
    ".Uint64MapEntry\0228\n\nstring_map\030\006 \003(\0132$.pr"
    "oto3.TestNestedMap.StringMapEntry\0222\n\007map"
    "_map\030\007 \003(\0132!.proto3.TestNestedMap.MapMap"
    "Entry\032.\n\014BoolMapEntry\022\013\n\003key\030\001 \001(\010\022\r\n\005va"
    "lue\030\002 \001(\005:\0028\001\032/\n\rInt32MapEntry\022\013\n\003key\030\001 "
    "\001(\005\022\r\n\005value\030\002 \001(\005:\0028\001\032/\n\rInt64MapEntry\022"
    "\013\n\003key\030\001 \001(\003\022\r\n\005value\030\002 \001(\005:\0028\001\0320\n\016Uint3"
    "2MapEntry\022\013\n\003key\030\001 \001(\r\022\r\n\005value\030\002 \001(\005:\0028"
    "\001\0320\n\016Uint64MapEntry\022\013\n\003key\030\001 \001(\004\022\r\n\005valu"
    "e\030\002 \001(\005:\0028\001\0320\n\016StringMapEntry\022\013\n\003key\030\001 \001"
    "(\t\022\r\n\005value\030\002 \001(\005:\0028\001\032D\n\013MapMapEntry\022\013\n\003"
    "key\030\001 \001(\t\022$\n\005value\030\002 \001(\0132\025.proto3.TestNe"
    "stedMap:\0028\001\"\356\007\n\013TestWrapper\022.\n\nbool_valu"
    "e\030\001 \001(\0132\032.google.protobuf.BoolValue\0220\n\013i"
    "nt32_value\030\002 \001(\0132\033.google.protobuf.Int32"
    "Value\0220\n\013int64_value\030\003 \001(\0132\033.google.prot"
    "obuf.Int64Value\0222\n\014uint32_value\030\004 \001(\0132\034."
    "google.protobuf.UInt32Value\0222\n\014uint64_va"
    "lue\030\005 \001(\0132\034.google.protobuf.UInt64Value\022"
    "0\n\013float_value\030\006 \001(\0132\033.google.protobuf.F"
    "loatValue\0222\n\014double_value\030\007 \001(\0132\034.google"
    ".protobuf.DoubleValue\0222\n\014string_value\030\010 "
    "\001(\0132\034.google.protobuf.StringValue\0220\n\013byt"
    "es_value\030\t \001(\0132\033.google.protobuf.BytesVa"
    "lue\0227\n\023repeated_bool_value\030\013 \003(\0132\032.googl"
    "e.protobuf.BoolValue\0229\n\024repeated_int32_v"
    "alue\030\014 \003(\0132\033.google.protobuf.Int32Value\022"
    "9\n\024repeated_int64_value\030\r \003(\0132\033.google.p"
    "rotobuf.Int64Value\022;\n\025repeated_uint32_va"
    "lue\030\016 \003(\0132\034.google.protobuf.UInt32Value\022"
    ";\n\025repeated_uint64_value\030\017 \003(\0132\034.google."
    "protobuf.UInt64Value\0229\n\024repeated_float_v"
    "alue\030\020 \003(\0132\033.google.protobuf.FloatValue\022"
    ";\n\025repeated_double_value\030\021 \003(\0132\034.google."
    "protobuf.DoubleValue\022;\n\025repeated_string_"
    "value\030\022 \003(\0132\034.google.protobuf.StringValu"
    "e\0229\n\024repeated_bytes_value\030\023 \003(\0132\033.google"
    ".protobuf.BytesValue\"n\n\rTestTimestamp\022)\n"
    "\005value\030\001 \001(\0132\032.google.protobuf.Timestamp"
    "\0222\n\016repeated_value\030\002 \003(\0132\032.google.protob"
    "uf.Timestamp\"k\n\014TestDuration\022(\n\005value\030\001 "
    "\001(\0132\031.google.protobuf.Duration\0221\n\016repeat"
    "ed_value\030\002 \003(\0132\031.google.protobuf.Duratio"
    "n\":\n\rTestFieldMask\022)\n\005value\030\001 \001(\0132\032.goog"
    "le.protobuf.FieldMask\"e\n\nTestStruct\022&\n\005v"
    "alue\030\001 \001(\0132\027.google.protobuf.Struct\022/\n\016r"
    "epeated_value\030\002 \003(\0132\027.google.protobuf.St"
    "ruct\"\\\n\007TestAny\022#\n\005value\030\001 \001(\0132\024.google."
    "protobuf.Any\022,\n\016repeated_value\030\002 \003(\0132\024.g"
    "oogle.protobuf.Any\"b\n\tTestValue\022%\n\005value"
    "\030\001 \001(\0132\026.google.protobuf.Value\022.\n\016repeat"
    "ed_value\030\002 \003(\0132\026.google.protobuf.Value\"n"
    "\n\rTestListValue\022)\n\005value\030\001 \001(\0132\032.google."
    "protobuf.ListValue\0222\n\016repeated_value\030\002 \003"
    "(\0132\032.google.protobuf.ListValue\"\211\001\n\rTestB"
    "oolValue\022\022\n\nbool_value\030\001 \001(\010\0224\n\010bool_map"
    "\030\002 \003(\0132\".proto3.TestBoolValue.BoolMapEnt"
    "ry\032.\n\014BoolMapEntry\022\013\n\003key\030\001 \001(\010\022\r\n\005value"
    "\030\002 \001(\005:\0028\001\"+\n\022TestCustomJsonName\022\025\n\005valu"
    "e\030\001 \001(\005R\006@value\"J\n\016TestExtensions\0228\n\next"
    "ensions\030\001 \001(\0132$.protobuf_unittest.TestAl"
    "lExtensions*\034\n\010EnumType\022\007\n\003FOO\020\000\022\007\n\003BAR\020"
    "\001b\006proto3", 4569);
  ::google::protobuf::MessageFactory::InternalRegisterGeneratedFile(
    "google/protobuf/util/json_format_proto3.proto", &protobuf_RegisterTypes);
  ::google::protobuf::protobuf_AddDesc_google_2fprotobuf_2fduration_2eproto();
  ::google::protobuf::protobuf_AddDesc_google_2fprotobuf_2ftimestamp_2eproto();
  ::google::protobuf::protobuf_AddDesc_google_2fprotobuf_2fwrappers_2eproto();
  ::google::protobuf::protobuf_AddDesc_google_2fprotobuf_2fstruct_2eproto();
  ::google::protobuf::protobuf_AddDesc_google_2fprotobuf_2fany_2eproto();
  ::google::protobuf::protobuf_AddDesc_google_2fprotobuf_2ffield_5fmask_2eproto();
  ::protobuf_unittest::protobuf_AddDesc_google_2fprotobuf_2funittest_2eproto();
  ::google::protobuf::internal::OnShutdown(&protobuf_ShutdownFile_google_2fprotobuf_2futil_2fjson_5fformat_5fproto3_2eproto);
}

GOOGLE_PROTOBUF_DECLARE_ONCE(protobuf_AddDesc_google_2fprotobuf_2futil_2fjson_5fformat_5fproto3_2eproto_once_);
void protobuf_AddDesc_google_2fprotobuf_2futil_2fjson_5fformat_5fproto3_2eproto() {
  ::google::protobuf::GoogleOnceInit(&protobuf_AddDesc_google_2fprotobuf_2futil_2fjson_5fformat_5fproto3_2eproto_once_,
                 &protobuf_AddDesc_google_2fprotobuf_2futil_2fjson_5fformat_5fproto3_2eproto_impl);
}
// Force AddDescriptors() to be called at static initialization time.
struct StaticDescriptorInitializer_google_2fprotobuf_2futil_2fjson_5fformat_5fproto3_2eproto {
  StaticDescriptorInitializer_google_2fprotobuf_2futil_2fjson_5fformat_5fproto3_2eproto() {
    protobuf_AddDesc_google_2fprotobuf_2futil_2fjson_5fformat_5fproto3_2eproto();
  }
} static_descriptor_initializer_google_2fprotobuf_2futil_2fjson_5fformat_5fproto3_2eproto_;
const ::google::protobuf::EnumDescriptor* EnumType_descriptor() {
  protobuf_AssignDescriptorsOnce();
  return EnumType_descriptor_;
}
bool EnumType_IsValid(int value) {
  switch (value) {
    case 0:
    case 1:
      return true;
    default:
      return false;
  }
}


namespace {

static void MergeFromFail(int line) GOOGLE_ATTRIBUTE_COLD GOOGLE_ATTRIBUTE_NORETURN;
static void MergeFromFail(int line) {
  ::google::protobuf::internal::MergeFromFail(__FILE__, line);
}

}  // namespace


// ===================================================================

#if !defined(_MSC_VER) || _MSC_VER >= 1900
const int MessageType::kValueFieldNumber;
#endif  // !defined(_MSC_VER) || _MSC_VER >= 1900

MessageType::MessageType()
  : ::google::protobuf::Message(), _internal_metadata_(NULL) {
  if (this != internal_default_instance()) protobuf_InitDefaults_google_2fprotobuf_2futil_2fjson_5fformat_5fproto3_2eproto();
  SharedCtor();
  // @@protoc_insertion_point(constructor:proto3.MessageType)
}

void MessageType::InitAsDefaultInstance() {
}

MessageType::MessageType(const MessageType& from)
  : ::google::protobuf::Message(),
    _internal_metadata_(NULL) {
  SharedCtor();
  UnsafeMergeFrom(from);
  // @@protoc_insertion_point(copy_constructor:proto3.MessageType)
}

void MessageType::SharedCtor() {
  value_ = 0;
  _cached_size_ = 0;
}

MessageType::~MessageType() {
  // @@protoc_insertion_point(destructor:proto3.MessageType)
  SharedDtor();
}

void MessageType::SharedDtor() {
}

void MessageType::SetCachedSize(int size) const {
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
}
const ::google::protobuf::Descriptor* MessageType::descriptor() {
  protobuf_AssignDescriptorsOnce();
  return MessageType_descriptor_;
}

const MessageType& MessageType::default_instance() {
  protobuf_InitDefaults_google_2fprotobuf_2futil_2fjson_5fformat_5fproto3_2eproto();
  return *internal_default_instance();
}

::google::protobuf::internal::ExplicitlyConstructed<MessageType> MessageType_default_instance_;

MessageType* MessageType::New(::google::protobuf::Arena* arena) const {
  MessageType* n = new MessageType;
  if (arena != NULL) {
    arena->Own(n);
  }
  return n;
}

void MessageType::Clear() {
// @@protoc_insertion_point(message_clear_start:proto3.MessageType)
  value_ = 0;
}

bool MessageType::MergePartialFromCodedStream(
    ::google::protobuf::io::CodedInputStream* input) {
#define DO_(EXPRESSION) if (!GOOGLE_PREDICT_TRUE(EXPRESSION)) goto failure
  ::google::protobuf::uint32 tag;
  // @@protoc_insertion_point(parse_start:proto3.MessageType)
  for (;;) {
    ::std::pair< ::google::protobuf::uint32, bool> p = input->ReadTagWithCutoff(127);
    tag = p.first;
    if (!p.second) goto handle_unusual;
    switch (::google::protobuf::internal::WireFormatLite::GetTagFieldNumber(tag)) {
      // optional int32 value = 1;
      case 1: {
        if (tag == 8) {

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int32, ::google::protobuf::internal::WireFormatLite::TYPE_INT32>(
                 input, &value_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectAtEnd()) goto success;
        break;
      }

      default: {
      handle_unusual:
        if (tag == 0 ||
            ::google::protobuf::internal::WireFormatLite::GetTagWireType(tag) ==
            ::google::protobuf::internal::WireFormatLite::WIRETYPE_END_GROUP) {
          goto success;
        }
        DO_(::google::protobuf::internal::WireFormatLite::SkipField(input, tag));
        break;
      }
    }
  }
success:
  // @@protoc_insertion_point(parse_success:proto3.MessageType)
  return true;
failure:
  // @@protoc_insertion_point(parse_failure:proto3.MessageType)
  return false;
#undef DO_
}

void MessageType::SerializeWithCachedSizes(
    ::google::protobuf::io::CodedOutputStream* output) const {
  // @@protoc_insertion_point(serialize_start:proto3.MessageType)
  // optional int32 value = 1;
  if (this->value() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt32(1, this->value(), output);
  }

  // @@protoc_insertion_point(serialize_end:proto3.MessageType)
}

::google::protobuf::uint8* MessageType::InternalSerializeWithCachedSizesToArray(
    bool deterministic, ::google::protobuf::uint8* target) const {
  (void)deterministic; // Unused
  // @@protoc_insertion_point(serialize_to_array_start:proto3.MessageType)
  // optional int32 value = 1;
  if (this->value() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt32ToArray(1, this->value(), target);
  }

  // @@protoc_insertion_point(serialize_to_array_end:proto3.MessageType)
  return target;
}

size_t MessageType::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:proto3.MessageType)
  size_t total_size = 0;

  // optional int32 value = 1;
  if (this->value() != 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::Int32Size(
        this->value());
  }

  int cached_size = ::google::protobuf::internal::ToCachedSize(total_size);
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = cached_size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
  return total_size;
}

void MessageType::MergeFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_merge_from_start:proto3.MessageType)
  if (GOOGLE_PREDICT_FALSE(&from == this)) MergeFromFail(__LINE__);
  const MessageType* source =
      ::google::protobuf::internal::DynamicCastToGenerated<const MessageType>(
          &from);
  if (source == NULL) {
  // @@protoc_insertion_point(generalized_merge_from_cast_fail:proto3.MessageType)
    ::google::protobuf::internal::ReflectionOps::Merge(from, this);
  } else {
  // @@protoc_insertion_point(generalized_merge_from_cast_success:proto3.MessageType)
    UnsafeMergeFrom(*source);
  }
}

void MessageType::MergeFrom(const MessageType& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:proto3.MessageType)
  if (GOOGLE_PREDICT_TRUE(&from != this)) {
    UnsafeMergeFrom(from);
  } else {
    MergeFromFail(__LINE__);
  }
}

void MessageType::UnsafeMergeFrom(const MessageType& from) {
  GOOGLE_DCHECK(&from != this);
  if (from.value() != 0) {
    set_value(from.value());
  }
}

void MessageType::CopyFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_copy_from_start:proto3.MessageType)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

void MessageType::CopyFrom(const MessageType& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:proto3.MessageType)
  if (&from == this) return;
  Clear();
  UnsafeMergeFrom(from);
}

bool MessageType::IsInitialized() const {

  return true;
}

void MessageType::Swap(MessageType* other) {
  if (other == this) return;
  InternalSwap(other);
}
void MessageType::InternalSwap(MessageType* other) {
  std::swap(value_, other->value_);
  _internal_metadata_.Swap(&other->_internal_metadata_);
  std::swap(_cached_size_, other->_cached_size_);
}

::google::protobuf::Metadata MessageType::GetMetadata() const {
  protobuf_AssignDescriptorsOnce();
  ::google::protobuf::Metadata metadata;
  metadata.descriptor = MessageType_descriptor_;
  metadata.reflection = MessageType_reflection_;
  return metadata;
}

#if PROTOBUF_INLINE_NOT_IN_HEADERS
// MessageType

// optional int32 value = 1;
void MessageType::clear_value() {
  value_ = 0;
}
::google::protobuf::int32 MessageType::value() const {
  // @@protoc_insertion_point(field_get:proto3.MessageType.value)
  return value_;
}
void MessageType::set_value(::google::protobuf::int32 value) {
  
  value_ = value;
  // @@protoc_insertion_point(field_set:proto3.MessageType.value)
}

inline const MessageType* MessageType::internal_default_instance() {
  return &MessageType_default_instance_.get();
}
#endif  // PROTOBUF_INLINE_NOT_IN_HEADERS

// ===================================================================

#if !defined(_MSC_VER) || _MSC_VER >= 1900
const int TestMessage::kBoolValueFieldNumber;
const int TestMessage::kInt32ValueFieldNumber;
const int TestMessage::kInt64ValueFieldNumber;
const int TestMessage::kUint32ValueFieldNumber;
const int TestMessage::kUint64ValueFieldNumber;
const int TestMessage::kFloatValueFieldNumber;
const int TestMessage::kDoubleValueFieldNumber;
const int TestMessage::kStringValueFieldNumber;
const int TestMessage::kBytesValueFieldNumber;
const int TestMessage::kEnumValueFieldNumber;
const int TestMessage::kMessageValueFieldNumber;
const int TestMessage::kRepeatedBoolValueFieldNumber;
const int TestMessage::kRepeatedInt32ValueFieldNumber;
const int TestMessage::kRepeatedInt64ValueFieldNumber;
const int TestMessage::kRepeatedUint32ValueFieldNumber;
const int TestMessage::kRepeatedUint64ValueFieldNumber;
const int TestMessage::kRepeatedFloatValueFieldNumber;
const int TestMessage::kRepeatedDoubleValueFieldNumber;
const int TestMessage::kRepeatedStringValueFieldNumber;
const int TestMessage::kRepeatedBytesValueFieldNumber;
const int TestMessage::kRepeatedEnumValueFieldNumber;
const int TestMessage::kRepeatedMessageValueFieldNumber;
#endif  // !defined(_MSC_VER) || _MSC_VER >= 1900

TestMessage::TestMessage()
  : ::google::protobuf::Message(), _internal_metadata_(NULL) {
  if (this != internal_default_instance()) protobuf_InitDefaults_google_2fprotobuf_2futil_2fjson_5fformat_5fproto3_2eproto();
  SharedCtor();
  // @@protoc_insertion_point(constructor:proto3.TestMessage)
}

void TestMessage::InitAsDefaultInstance() {
  message_value_ = const_cast< ::proto3::MessageType*>(
      ::proto3::MessageType::internal_default_instance());
}

TestMessage::TestMessage(const TestMessage& from)
  : ::google::protobuf::Message(),
    _internal_metadata_(NULL) {
  SharedCtor();
  UnsafeMergeFrom(from);
  // @@protoc_insertion_point(copy_constructor:proto3.TestMessage)
}

void TestMessage::SharedCtor() {
  string_value_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  bytes_value_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  message_value_ = NULL;
  ::memset(&bool_value_, 0, reinterpret_cast<char*>(&enum_value_) -
    reinterpret_cast<char*>(&bool_value_) + sizeof(enum_value_));
  _cached_size_ = 0;
}

TestMessage::~TestMessage() {
  // @@protoc_insertion_point(destructor:proto3.TestMessage)
  SharedDtor();
}

void TestMessage::SharedDtor() {
  string_value_.DestroyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  bytes_value_.DestroyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  if (this != &TestMessage_default_instance_.get()) {
    delete message_value_;
  }
}

void TestMessage::SetCachedSize(int size) const {
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
}
const ::google::protobuf::Descriptor* TestMessage::descriptor() {
  protobuf_AssignDescriptorsOnce();
  return TestMessage_descriptor_;
}

const TestMessage& TestMessage::default_instance() {
  protobuf_InitDefaults_google_2fprotobuf_2futil_2fjson_5fformat_5fproto3_2eproto();
  return *internal_default_instance();
}

::google::protobuf::internal::ExplicitlyConstructed<TestMessage> TestMessage_default_instance_;

TestMessage* TestMessage::New(::google::protobuf::Arena* arena) const {
  TestMessage* n = new TestMessage;
  if (arena != NULL) {
    arena->Own(n);
  }
  return n;
}

void TestMessage::Clear() {
// @@protoc_insertion_point(message_clear_start:proto3.TestMessage)
#if defined(__clang__)
#define ZR_HELPER_(f) \
  _Pragma("clang diagnostic push") \
  _Pragma("clang diagnostic ignored \"-Winvalid-offsetof\"") \
  __builtin_offsetof(TestMessage, f) \
  _Pragma("clang diagnostic pop")
#else
#define ZR_HELPER_(f) reinterpret_cast<char*>(\
  &reinterpret_cast<TestMessage*>(16)->f)
#endif

#define ZR_(first, last) do {\
  ::memset(&(first), 0,\
           ZR_HELPER_(last) - ZR_HELPER_(first) + sizeof(last));\
} while (0)

  ZR_(bool_value_, double_value_);
  string_value_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  bytes_value_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  enum_value_ = 0;
  if (GetArenaNoVirtual() == NULL && message_value_ != NULL) delete message_value_;
  message_value_ = NULL;

#undef ZR_HELPER_
#undef ZR_

  repeated_bool_value_.Clear();
  repeated_int32_value_.Clear();
  repeated_int64_value_.Clear();
  repeated_uint32_value_.Clear();
  repeated_uint64_value_.Clear();
  repeated_float_value_.Clear();
  repeated_double_value_.Clear();
  repeated_string_value_.Clear();
  repeated_bytes_value_.Clear();
  repeated_enum_value_.Clear();
  repeated_message_value_.Clear();
}

bool TestMessage::MergePartialFromCodedStream(
    ::google::protobuf::io::CodedInputStream* input) {
#define DO_(EXPRESSION) if (!GOOGLE_PREDICT_TRUE(EXPRESSION)) goto failure
  ::google::protobuf::uint32 tag;
  // @@protoc_insertion_point(parse_start:proto3.TestMessage)
  for (;;) {
    ::std::pair< ::google::protobuf::uint32, bool> p = input->ReadTagWithCutoff(16383);
    tag = p.first;
    if (!p.second) goto handle_unusual;
    switch (::google::protobuf::internal::WireFormatLite::GetTagFieldNumber(tag)) {
      // optional bool bool_value = 1;
      case 1: {
        if (tag == 8) {

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   bool, ::google::protobuf::internal::WireFormatLite::TYPE_BOOL>(
                 input, &bool_value_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(16)) goto parse_int32_value;
        break;
      }

      // optional int32 int32_value = 2;
      case 2: {
        if (tag == 16) {
         parse_int32_value:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int32, ::google::protobuf::internal::WireFormatLite::TYPE_INT32>(
                 input, &int32_value_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(24)) goto parse_int64_value;
        break;
      }

      // optional int64 int64_value = 3;
      case 3: {
        if (tag == 24) {
         parse_int64_value:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_INT64>(
                 input, &int64_value_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(32)) goto parse_uint32_value;
        break;
      }

      // optional uint32 uint32_value = 4;
      case 4: {
        if (tag == 32) {
         parse_uint32_value:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::uint32, ::google::protobuf::internal::WireFormatLite::TYPE_UINT32>(
                 input, &uint32_value_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(40)) goto parse_uint64_value;
        break;
      }

      // optional uint64 uint64_value = 5;
      case 5: {
        if (tag == 40) {
         parse_uint64_value:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::uint64, ::google::protobuf::internal::WireFormatLite::TYPE_UINT64>(
                 input, &uint64_value_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(53)) goto parse_float_value;
        break;
      }

      // optional float float_value = 6;
      case 6: {
        if (tag == 53) {
         parse_float_value:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   float, ::google::protobuf::internal::WireFormatLite::TYPE_FLOAT>(
                 input, &float_value_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(57)) goto parse_double_value;
        break;
      }

      // optional double double_value = 7;
      case 7: {
        if (tag == 57) {
         parse_double_value:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   double, ::google::protobuf::internal::WireFormatLite::TYPE_DOUBLE>(
                 input, &double_value_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(66)) goto parse_string_value;
        break;
      }

      // optional string string_value = 8;
      case 8: {
        if (tag == 66) {
         parse_string_value:
          DO_(::google::protobuf::internal::WireFormatLite::ReadString(
                input, this->mutable_string_value()));
          DO_(::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
            this->string_value().data(), this->string_value().length(),
            ::google::protobuf::internal::WireFormatLite::PARSE,
            "proto3.TestMessage.string_value"));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(74)) goto parse_bytes_value;
        break;
      }

      // optional bytes bytes_value = 9;
      case 9: {
        if (tag == 74) {
         parse_bytes_value:
          DO_(::google::protobuf::internal::WireFormatLite::ReadBytes(
                input, this->mutable_bytes_value()));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(80)) goto parse_enum_value;
        break;
      }

      // optional .proto3.EnumType enum_value = 10;
      case 10: {
        if (tag == 80) {
         parse_enum_value:
          int value;
          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   int, ::google::protobuf::internal::WireFormatLite::TYPE_ENUM>(
                 input, &value)));
          set_enum_value(static_cast< ::proto3::EnumType >(value));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(90)) goto parse_message_value;
        break;
      }

      // optional .proto3.MessageType message_value = 11;
      case 11: {
        if (tag == 90) {
         parse_message_value:
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessageNoVirtual(
               input, mutable_message_value()));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(170)) goto parse_repeated_bool_value;
        break;
      }

      // repeated bool repeated_bool_value = 21;
      case 21: {
        if (tag == 170) {
         parse_repeated_bool_value:
          DO_((::google::protobuf::internal::WireFormatLite::ReadPackedPrimitive<
                   bool, ::google::protobuf::internal::WireFormatLite::TYPE_BOOL>(
                 input, this->mutable_repeated_bool_value())));
        } else if (tag == 168) {
          DO_((::google::protobuf::internal::WireFormatLite::ReadRepeatedPrimitiveNoInline<
                   bool, ::google::protobuf::internal::WireFormatLite::TYPE_BOOL>(
                 2, 170, input, this->mutable_repeated_bool_value())));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(178)) goto parse_repeated_int32_value;
        break;
      }

      // repeated int32 repeated_int32_value = 22;
      case 22: {
        if (tag == 178) {
         parse_repeated_int32_value:
          DO_((::google::protobuf::internal::WireFormatLite::ReadPackedPrimitive<
                   ::google::protobuf::int32, ::google::protobuf::internal::WireFormatLite::TYPE_INT32>(
                 input, this->mutable_repeated_int32_value())));
        } else if (tag == 176) {
          DO_((::google::protobuf::internal::WireFormatLite::ReadRepeatedPrimitiveNoInline<
                   ::google::protobuf::int32, ::google::protobuf::internal::WireFormatLite::TYPE_INT32>(
                 2, 178, input, this->mutable_repeated_int32_value())));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(186)) goto parse_repeated_int64_value;
        break;
      }

      // repeated int64 repeated_int64_value = 23;
      case 23: {
        if (tag == 186) {
         parse_repeated_int64_value:
          DO_((::google::protobuf::internal::WireFormatLite::ReadPackedPrimitive<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_INT64>(
                 input, this->mutable_repeated_int64_value())));
        } else if (tag == 184) {
          DO_((::google::protobuf::internal::WireFormatLite::ReadRepeatedPrimitiveNoInline<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_INT64>(
                 2, 186, input, this->mutable_repeated_int64_value())));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(194)) goto parse_repeated_uint32_value;
        break;
      }

      // repeated uint32 repeated_uint32_value = 24;
      case 24: {
        if (tag == 194) {
         parse_repeated_uint32_value:
          DO_((::google::protobuf::internal::WireFormatLite::ReadPackedPrimitive<
                   ::google::protobuf::uint32, ::google::protobuf::internal::WireFormatLite::TYPE_UINT32>(
                 input, this->mutable_repeated_uint32_value())));
        } else if (tag == 192) {
          DO_((::google::protobuf::internal::WireFormatLite::ReadRepeatedPrimitiveNoInline<
                   ::google::protobuf::uint32, ::google::protobuf::internal::WireFormatLite::TYPE_UINT32>(
                 2, 194, input, this->mutable_repeated_uint32_value())));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(202)) goto parse_repeated_uint64_value;
        break;
      }

      // repeated uint64 repeated_uint64_value = 25;
      case 25: {
        if (tag == 202) {
         parse_repeated_uint64_value:
          DO_((::google::protobuf::internal::WireFormatLite::ReadPackedPrimitive<
                   ::google::protobuf::uint64, ::google::protobuf::internal::WireFormatLite::TYPE_UINT64>(
                 input, this->mutable_repeated_uint64_value())));
        } else if (tag == 200) {
          DO_((::google::protobuf::internal::WireFormatLite::ReadRepeatedPrimitiveNoInline<
                   ::google::protobuf::uint64, ::google::protobuf::internal::WireFormatLite::TYPE_UINT64>(
                 2, 202, input, this->mutable_repeated_uint64_value())));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(210)) goto parse_repeated_float_value;
        break;
      }

      // repeated float repeated_float_value = 26;
      case 26: {
        if (tag == 210) {
         parse_repeated_float_value:
          DO_((::google::protobuf::internal::WireFormatLite::ReadPackedPrimitive<
                   float, ::google::protobuf::internal::WireFormatLite::TYPE_FLOAT>(
                 input, this->mutable_repeated_float_value())));
        } else if (tag == 213) {
          DO_((::google::protobuf::internal::WireFormatLite::ReadRepeatedPrimitiveNoInline<
                   float, ::google::protobuf::internal::WireFormatLite::TYPE_FLOAT>(
                 2, 210, input, this->mutable_repeated_float_value())));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(218)) goto parse_repeated_double_value;
        break;
      }

      // repeated double repeated_double_value = 27;
      case 27: {
        if (tag == 218) {
         parse_repeated_double_value:
          DO_((::google::protobuf::internal::WireFormatLite::ReadPackedPrimitive<
                   double, ::google::protobuf::internal::WireFormatLite::TYPE_DOUBLE>(
                 input, this->mutable_repeated_double_value())));
        } else if (tag == 217) {
          DO_((::google::protobuf::internal::WireFormatLite::ReadRepeatedPrimitiveNoInline<
                   double, ::google::protobuf::internal::WireFormatLite::TYPE_DOUBLE>(
                 2, 218, input, this->mutable_repeated_double_value())));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(226)) goto parse_repeated_string_value;
        break;
      }

      // repeated string repeated_string_value = 28;
      case 28: {
        if (tag == 226) {
         parse_repeated_string_value:
          DO_(::google::protobuf::internal::WireFormatLite::ReadString(
                input, this->add_repeated_string_value()));
          DO_(::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
            this->repeated_string_value(this->repeated_string_value_size() - 1).data(),
            this->repeated_string_value(this->repeated_string_value_size() - 1).length(),
            ::google::protobuf::internal::WireFormatLite::PARSE,
            "proto3.TestMessage.repeated_string_value"));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(226)) goto parse_repeated_string_value;
        if (input->ExpectTag(234)) goto parse_repeated_bytes_value;
        break;
      }

      // repeated bytes repeated_bytes_value = 29;
      case 29: {
        if (tag == 234) {
         parse_repeated_bytes_value:
          DO_(::google::protobuf::internal::WireFormatLite::ReadBytes(
                input, this->add_repeated_bytes_value()));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(234)) goto parse_repeated_bytes_value;
        if (input->ExpectTag(242)) goto parse_repeated_enum_value;
        break;
      }

      // repeated .proto3.EnumType repeated_enum_value = 30;
      case 30: {
        if (tag == 242) {
         parse_repeated_enum_value:
          ::google::protobuf::uint32 length;
          DO_(input->ReadVarint32(&length));
          ::google::protobuf::io::CodedInputStream::Limit limit = input->PushLimit(length);
          while (input->BytesUntilLimit() > 0) {
            int value;
            DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   int, ::google::protobuf::internal::WireFormatLite::TYPE_ENUM>(
                 input, &value)));
            add_repeated_enum_value(static_cast< ::proto3::EnumType >(value));
          }
          input->PopLimit(limit);
        } else if (tag == 240) {
          int value;
          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   int, ::google::protobuf::internal::WireFormatLite::TYPE_ENUM>(
                 input, &value)));
          add_repeated_enum_value(static_cast< ::proto3::EnumType >(value));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(250)) goto parse_repeated_message_value;
        break;
      }

      // repeated .proto3.MessageType repeated_message_value = 31;
      case 31: {
        if (tag == 250) {
         parse_repeated_message_value:
          DO_(input->IncrementRecursionDepth());
         parse_loop_repeated_message_value:
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessageNoVirtualNoRecursionDepth(
                input, add_repeated_message_value()));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(250)) goto parse_loop_repeated_message_value;
        input->UnsafeDecrementRecursionDepth();
        if (input->ExpectAtEnd()) goto success;
        break;
      }

      default: {
      handle_unusual:
        if (tag == 0 ||
            ::google::protobuf::internal::WireFormatLite::GetTagWireType(tag) ==
            ::google::protobuf::internal::WireFormatLite::WIRETYPE_END_GROUP) {
          goto success;
        }
        DO_(::google::protobuf::internal::WireFormatLite::SkipField(input, tag));
        break;
      }
    }
  }
success:
  // @@protoc_insertion_point(parse_success:proto3.TestMessage)
  return true;
failure:
  // @@protoc_insertion_point(parse_failure:proto3.TestMessage)
  return false;
#undef DO_
}

void TestMessage::SerializeWithCachedSizes(
    ::google::protobuf::io::CodedOutputStream* output) const {
  // @@protoc_insertion_point(serialize_start:proto3.TestMessage)
  // optional bool bool_value = 1;
  if (this->bool_value() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteBool(1, this->bool_value(), output);
  }

  // optional int32 int32_value = 2;
  if (this->int32_value() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt32(2, this->int32_value(), output);
  }

  // optional int64 int64_value = 3;
  if (this->int64_value() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt64(3, this->int64_value(), output);
  }

  // optional uint32 uint32_value = 4;
  if (this->uint32_value() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteUInt32(4, this->uint32_value(), output);
  }

  // optional uint64 uint64_value = 5;
  if (this->uint64_value() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteUInt64(5, this->uint64_value(), output);
  }

  // optional float float_value = 6;
  if (this->float_value() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteFloat(6, this->float_value(), output);
  }

  // optional double double_value = 7;
  if (this->double_value() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteDouble(7, this->double_value(), output);
  }

  // optional string string_value = 8;
  if (this->string_value().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->string_value().data(), this->string_value().length(),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "proto3.TestMessage.string_value");
    ::google::protobuf::internal::WireFormatLite::WriteStringMaybeAliased(
      8, this->string_value(), output);
  }

  // optional bytes bytes_value = 9;
  if (this->bytes_value().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::WriteBytesMaybeAliased(
      9, this->bytes_value(), output);
  }

  // optional .proto3.EnumType enum_value = 10;
  if (this->enum_value() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteEnum(
      10, this->enum_value(), output);
  }

  // optional .proto3.MessageType message_value = 11;
  if (this->has_message_value()) {
    ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
      11, *this->message_value_, output);
  }

  // repeated bool repeated_bool_value = 21;
  if (this->repeated_bool_value_size() > 0) {
    ::google::protobuf::internal::WireFormatLite::WriteTag(21, ::google::protobuf::internal::WireFormatLite::WIRETYPE_LENGTH_DELIMITED, output);
    output->WriteVarint32(_repeated_bool_value_cached_byte_size_);
  }
  for (int i = 0; i < this->repeated_bool_value_size(); i++) {
    ::google::protobuf::internal::WireFormatLite::WriteBoolNoTag(
      this->repeated_bool_value(i), output);
  }

  // repeated int32 repeated_int32_value = 22;
  if (this->repeated_int32_value_size() > 0) {
    ::google::protobuf::internal::WireFormatLite::WriteTag(22, ::google::protobuf::internal::WireFormatLite::WIRETYPE_LENGTH_DELIMITED, output);
    output->WriteVarint32(_repeated_int32_value_cached_byte_size_);
  }
  for (int i = 0; i < this->repeated_int32_value_size(); i++) {
    ::google::protobuf::internal::WireFormatLite::WriteInt32NoTag(
      this->repeated_int32_value(i), output);
  }

  // repeated int64 repeated_int64_value = 23;
  if (this->repeated_int64_value_size() > 0) {
    ::google::protobuf::internal::WireFormatLite::WriteTag(23, ::google::protobuf::internal::WireFormatLite::WIRETYPE_LENGTH_DELIMITED, output);
    output->WriteVarint32(_repeated_int64_value_cached_byte_size_);
  }
  for (int i = 0; i < this->repeated_int64_value_size(); i++) {
    ::google::protobuf::internal::WireFormatLite::WriteInt64NoTag(
      this->repeated_int64_value(i), output);
  }

  // repeated uint32 repeated_uint32_value = 24;
  if (this->repeated_uint32_value_size() > 0) {
    ::google::protobuf::internal::WireFormatLite::WriteTag(24, ::google::protobuf::internal::WireFormatLite::WIRETYPE_LENGTH_DELIMITED, output);
    output->WriteVarint32(_repeated_uint32_value_cached_byte_size_);
  }
  for (int i = 0; i < this->repeated_uint32_value_size(); i++) {
    ::google::protobuf::internal::WireFormatLite::WriteUInt32NoTag(
      this->repeated_uint32_value(i), output);
  }

  // repeated uint64 repeated_uint64_value = 25;
  if (this->repeated_uint64_value_size() > 0) {
    ::google::protobuf::internal::WireFormatLite::WriteTag(25, ::google::protobuf::internal::WireFormatLite::WIRETYPE_LENGTH_DELIMITED, output);
    output->WriteVarint32(_repeated_uint64_value_cached_byte_size_);
  }
  for (int i = 0; i < this->repeated_uint64_value_size(); i++) {
    ::google::protobuf::internal::WireFormatLite::WriteUInt64NoTag(
      this->repeated_uint64_value(i), output);
  }

  // repeated float repeated_float_value = 26;
  if (this->repeated_float_value_size() > 0) {
    ::google::protobuf::internal::WireFormatLite::WriteTag(26, ::google::protobuf::internal::WireFormatLite::WIRETYPE_LENGTH_DELIMITED, output);
    output->WriteVarint32(_repeated_float_value_cached_byte_size_);
  }
  for (int i = 0; i < this->repeated_float_value_size(); i++) {
    ::google::protobuf::internal::WireFormatLite::WriteFloatNoTag(
      this->repeated_float_value(i), output);
  }

  // repeated double repeated_double_value = 27;
  if (this->repeated_double_value_size() > 0) {
    ::google::protobuf::internal::WireFormatLite::WriteTag(27, ::google::protobuf::internal::WireFormatLite::WIRETYPE_LENGTH_DELIMITED, output);
    output->WriteVarint32(_repeated_double_value_cached_byte_size_);
  }
  for (int i = 0; i < this->repeated_double_value_size(); i++) {
    ::google::protobuf::internal::WireFormatLite::WriteDoubleNoTag(
      this->repeated_double_value(i), output);
  }

  // repeated string repeated_string_value = 28;
  for (int i = 0; i < this->repeated_string_value_size(); i++) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->repeated_string_value(i).data(), this->repeated_string_value(i).length(),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "proto3.TestMessage.repeated_string_value");
    ::google::protobuf::internal::WireFormatLite::WriteString(
      28, this->repeated_string_value(i), output);
  }

  // repeated bytes repeated_bytes_value = 29;
  for (int i = 0; i < this->repeated_bytes_value_size(); i++) {
    ::google::protobuf::internal::WireFormatLite::WriteBytes(
      29, this->repeated_bytes_value(i), output);
  }

  // repeated .proto3.EnumType repeated_enum_value = 30;
  if (this->repeated_enum_value_size() > 0) {
    ::google::protobuf::internal::WireFormatLite::WriteTag(
      30,
      ::google::protobuf::internal::WireFormatLite::WIRETYPE_LENGTH_DELIMITED,
      output);
    output->WriteVarint32(_repeated_enum_value_cached_byte_size_);
  }
  for (int i = 0; i < this->repeated_enum_value_size(); i++) {
    ::google::protobuf::internal::WireFormatLite::WriteEnumNoTag(
      this->repeated_enum_value(i), output);
  }

  // repeated .proto3.MessageType repeated_message_value = 31;
  for (unsigned int i = 0, n = this->repeated_message_value_size(); i < n; i++) {
    ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
      31, this->repeated_message_value(i), output);
  }

  // @@protoc_insertion_point(serialize_end:proto3.TestMessage)
}

::google::protobuf::uint8* TestMessage::InternalSerializeWithCachedSizesToArray(
    bool deterministic, ::google::protobuf::uint8* target) const {
  (void)deterministic; // Unused
  // @@protoc_insertion_point(serialize_to_array_start:proto3.TestMessage)
  // optional bool bool_value = 1;
  if (this->bool_value() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteBoolToArray(1, this->bool_value(), target);
  }

  // optional int32 int32_value = 2;
  if (this->int32_value() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt32ToArray(2, this->int32_value(), target);
  }

  // optional int64 int64_value = 3;
  if (this->int64_value() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt64ToArray(3, this->int64_value(), target);
  }

  // optional uint32 uint32_value = 4;
  if (this->uint32_value() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteUInt32ToArray(4, this->uint32_value(), target);
  }

  // optional uint64 uint64_value = 5;
  if (this->uint64_value() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteUInt64ToArray(5, this->uint64_value(), target);
  }

  // optional float float_value = 6;
  if (this->float_value() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteFloatToArray(6, this->float_value(), target);
  }

  // optional double double_value = 7;
  if (this->double_value() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteDoubleToArray(7, this->double_value(), target);
  }

  // optional string string_value = 8;
  if (this->string_value().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->string_value().data(), this->string_value().length(),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "proto3.TestMessage.string_value");
    target =
      ::google::protobuf::internal::WireFormatLite::WriteStringToArray(
        8, this->string_value(), target);
  }

  // optional bytes bytes_value = 9;
  if (this->bytes_value().size() > 0) {
    target =
      ::google::protobuf::internal::WireFormatLite::WriteBytesToArray(
        9, this->bytes_value(), target);
  }

  // optional .proto3.EnumType enum_value = 10;
  if (this->enum_value() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteEnumToArray(
      10, this->enum_value(), target);
  }

  // optional .proto3.MessageType message_value = 11;
  if (this->has_message_value()) {
    target = ::google::protobuf::internal::WireFormatLite::
      InternalWriteMessageNoVirtualToArray(
        11, *this->message_value_, false, target);
  }

  // repeated bool repeated_bool_value = 21;
  if (this->repeated_bool_value_size() > 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteTagToArray(
      21,
      ::google::protobuf::internal::WireFormatLite::WIRETYPE_LENGTH_DELIMITED,
      target);
    target = ::google::protobuf::io::CodedOutputStream::WriteVarint32ToArray(
      _repeated_bool_value_cached_byte_size_, target);
  }
  for (int i = 0; i < this->repeated_bool_value_size(); i++) {
    target = ::google::protobuf::internal::WireFormatLite::
      WriteBoolNoTagToArray(this->repeated_bool_value(i), target);
  }

  // repeated int32 repeated_int32_value = 22;
  if (this->repeated_int32_value_size() > 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteTagToArray(
      22,
      ::google::protobuf::internal::WireFormatLite::WIRETYPE_LENGTH_DELIMITED,
      target);
    target = ::google::protobuf::io::CodedOutputStream::WriteVarint32ToArray(
      _repeated_int32_value_cached_byte_size_, target);
  }
  for (int i = 0; i < this->repeated_int32_value_size(); i++) {
    target = ::google::protobuf::internal::WireFormatLite::
      WriteInt32NoTagToArray(this->repeated_int32_value(i), target);
  }

  // repeated int64 repeated_int64_value = 23;
  if (this->repeated_int64_value_size() > 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteTagToArray(
      23,
      ::google::protobuf::internal::WireFormatLite::WIRETYPE_LENGTH_DELIMITED,
      target);
    target = ::google::protobuf::io::CodedOutputStream::WriteVarint32ToArray(
      _repeated_int64_value_cached_byte_size_, target);
  }
  for (int i = 0; i < this->repeated_int64_value_size(); i++) {
    target = ::google::protobuf::internal::WireFormatLite::
      WriteInt64NoTagToArray(this->repeated_int64_value(i), target);
  }

  // repeated uint32 repeated_uint32_value = 24;
  if (this->repeated_uint32_value_size() > 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteTagToArray(
      24,
      ::google::protobuf::internal::WireFormatLite::WIRETYPE_LENGTH_DELIMITED,
      target);
    target = ::google::protobuf::io::CodedOutputStream::WriteVarint32ToArray(
      _repeated_uint32_value_cached_byte_size_, target);
  }
  for (int i = 0; i < this->repeated_uint32_value_size(); i++) {
    target = ::google::protobuf::internal::WireFormatLite::
      WriteUInt32NoTagToArray(this->repeated_uint32_value(i), target);
  }

  // repeated uint64 repeated_uint64_value = 25;
  if (this->repeated_uint64_value_size() > 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteTagToArray(
      25,
      ::google::protobuf::internal::WireFormatLite::WIRETYPE_LENGTH_DELIMITED,
      target);
    target = ::google::protobuf::io::CodedOutputStream::WriteVarint32ToArray(
      _repeated_uint64_value_cached_byte_size_, target);
  }
  for (int i = 0; i < this->repeated_uint64_value_size(); i++) {
    target = ::google::protobuf::internal::WireFormatLite::
      WriteUInt64NoTagToArray(this->repeated_uint64_value(i), target);
  }

  // repeated float repeated_float_value = 26;
  if (this->repeated_float_value_size() > 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteTagToArray(
      26,
      ::google::protobuf::internal::WireFormatLite::WIRETYPE_LENGTH_DELIMITED,
      target);
    target = ::google::protobuf::io::CodedOutputStream::WriteVarint32ToArray(
      _repeated_float_value_cached_byte_size_, target);
  }
  for (int i = 0; i < this->repeated_float_value_size(); i++) {
    target = ::google::protobuf::internal::WireFormatLite::
      WriteFloatNoTagToArray(this->repeated_float_value(i), target);
  }

  // repeated double repeated_double_value = 27;
  if (this->repeated_double_value_size() > 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteTagToArray(
      27,
      ::google::protobuf::internal::WireFormatLite::WIRETYPE_LENGTH_DELIMITED,
      target);
    target = ::google::protobuf::io::CodedOutputStream::WriteVarint32ToArray(
      _repeated_double_value_cached_byte_size_, target);
  }
  for (int i = 0; i < this->repeated_double_value_size(); i++) {
    target = ::google::protobuf::internal::WireFormatLite::
      WriteDoubleNoTagToArray(this->repeated_double_value(i), target);
  }

  // repeated string repeated_string_value = 28;
  for (int i = 0; i < this->repeated_string_value_size(); i++) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->repeated_string_value(i).data(), this->repeated_string_value(i).length(),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "proto3.TestMessage.repeated_string_value");
    target = ::google::protobuf::internal::WireFormatLite::
      WriteStringToArray(28, this->repeated_string_value(i), target);
  }

  // repeated bytes repeated_bytes_value = 29;
  for (int i = 0; i < this->repeated_bytes_value_size(); i++) {
    target = ::google::protobuf::internal::WireFormatLite::
      WriteBytesToArray(29, this->repeated_bytes_value(i), target);
  }

  // repeated .proto3.EnumType repeated_enum_value = 30;
  if (this->repeated_enum_value_size() > 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteTagToArray(
      30,
      ::google::protobuf::internal::WireFormatLite::WIRETYPE_LENGTH_DELIMITED,
      target);
    target = ::google::protobuf::io::CodedOutputStream::WriteVarint32ToArray(    _repeated_enum_value_cached_byte_size_, target);
  }
  for (int i = 0; i < this->repeated_enum_value_size(); i++) {
    target = ::google::protobuf::internal::WireFormatLite::WriteEnumNoTagToArray(
      this->repeated_enum_value(i), target);
  }

  // repeated .proto3.MessageType repeated_message_value = 31;
  for (unsigned int i = 0, n = this->repeated_message_value_size(); i < n; i++) {
    target = ::google::protobuf::internal::WireFormatLite::
      InternalWriteMessageNoVirtualToArray(
        31, this->repeated_message_value(i), false, target);
  }

  // @@protoc_insertion_point(serialize_to_array_end:proto3.TestMessage)
  return target;
}

size_t TestMessage::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:proto3.TestMessage)
  size_t total_size = 0;

  // optional bool bool_value = 1;
  if (this->bool_value() != 0) {
    total_size += 1 + 1;
  }

  // optional int32 int32_value = 2;
  if (this->int32_value() != 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::Int32Size(
        this->int32_value());
  }

  // optional int64 int64_value = 3;
  if (this->int64_value() != 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::Int64Size(
        this->int64_value());
  }

  // optional uint32 uint32_value = 4;
  if (this->uint32_value() != 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::UInt32Size(
        this->uint32_value());
  }

  // optional uint64 uint64_value = 5;
  if (this->uint64_value() != 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::UInt64Size(
        this->uint64_value());
  }

  // optional float float_value = 6;
  if (this->float_value() != 0) {
    total_size += 1 + 4;
  }

  // optional double double_value = 7;
  if (this->double_value() != 0) {
    total_size += 1 + 8;
  }

  // optional string string_value = 8;
  if (this->string_value().size() > 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::StringSize(
        this->string_value());
  }

  // optional bytes bytes_value = 9;
  if (this->bytes_value().size() > 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::BytesSize(
        this->bytes_value());
  }

  // optional .proto3.EnumType enum_value = 10;
  if (this->enum_value() != 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::EnumSize(this->enum_value());
  }

  // optional .proto3.MessageType message_value = 11;
  if (this->has_message_value()) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::MessageSizeNoVirtual(
        *this->message_value_);
  }

  // repeated bool repeated_bool_value = 21;
  {
    size_t data_size = 0;
    unsigned int count = this->repeated_bool_value_size();
    data_size = 1UL * count;
    if (data_size > 0) {
      total_size += 2 +
        ::google::protobuf::internal::WireFormatLite::Int32Size(data_size);
    }
    int cached_size = ::google::protobuf::internal::ToCachedSize(data_size);
    GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
    _repeated_bool_value_cached_byte_size_ = cached_size;
    GOOGLE_SAFE_CONCURRENT_WRITES_END();
    total_size += data_size;
  }

  // repeated int32 repeated_int32_value = 22;
  {
    size_t data_size = 0;
    unsigned int count = this->repeated_int32_value_size();
    for (unsigned int i = 0; i < count; i++) {
      data_size += ::google::protobuf::internal::WireFormatLite::
        Int32Size(this->repeated_int32_value(i));
    }
    if (data_size > 0) {
      total_size += 2 +
        ::google::protobuf::internal::WireFormatLite::Int32Size(data_size);
    }
    int cached_size = ::google::protobuf::internal::ToCachedSize(data_size);
    GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
    _repeated_int32_value_cached_byte_size_ = cached_size;
    GOOGLE_SAFE_CONCURRENT_WRITES_END();
    total_size += data_size;
  }

  // repeated int64 repeated_int64_value = 23;
  {
    size_t data_size = 0;
    unsigned int count = this->repeated_int64_value_size();
    for (unsigned int i = 0; i < count; i++) {
      data_size += ::google::protobuf::internal::WireFormatLite::
        Int64Size(this->repeated_int64_value(i));
    }
    if (data_size > 0) {
      total_size += 2 +
        ::google::protobuf::internal::WireFormatLite::Int32Size(data_size);
    }
    int cached_size = ::google::protobuf::internal::ToCachedSize(data_size);
    GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
    _repeated_int64_value_cached_byte_size_ = cached_size;
    GOOGLE_SAFE_CONCURRENT_WRITES_END();
    total_size += data_size;
  }

  // repeated uint32 repeated_uint32_value = 24;
  {
    size_t data_size = 0;
    unsigned int count = this->repeated_uint32_value_size();
    for (unsigned int i = 0; i < count; i++) {
      data_size += ::google::protobuf::internal::WireFormatLite::
        UInt32Size(this->repeated_uint32_value(i));
    }
    if (data_size > 0) {
      total_size += 2 +
        ::google::protobuf::internal::WireFormatLite::Int32Size(data_size);
    }
    int cached_size = ::google::protobuf::internal::ToCachedSize(data_size);
    GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
    _repeated_uint32_value_cached_byte_size_ = cached_size;
    GOOGLE_SAFE_CONCURRENT_WRITES_END();
    total_size += data_size;
  }

  // repeated uint64 repeated_uint64_value = 25;
  {
    size_t data_size = 0;
    unsigned int count = this->repeated_uint64_value_size();
    for (unsigned int i = 0; i < count; i++) {
      data_size += ::google::protobuf::internal::WireFormatLite::
        UInt64Size(this->repeated_uint64_value(i));
    }
    if (data_size > 0) {
      total_size += 2 +
        ::google::protobuf::internal::WireFormatLite::Int32Size(data_size);
    }
    int cached_size = ::google::protobuf::internal::ToCachedSize(data_size);
    GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
    _repeated_uint64_value_cached_byte_size_ = cached_size;
    GOOGLE_SAFE_CONCURRENT_WRITES_END();
    total_size += data_size;
  }

  // repeated float repeated_float_value = 26;
  {
    size_t data_size = 0;
    unsigned int count = this->repeated_float_value_size();
    data_size = 4UL * count;
    if (data_size > 0) {
      total_size += 2 +
        ::google::protobuf::internal::WireFormatLite::Int32Size(data_size);
    }
    int cached_size = ::google::protobuf::internal::ToCachedSize(data_size);
    GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
    _repeated_float_value_cached_byte_size_ = cached_size;
    GOOGLE_SAFE_CONCURRENT_WRITES_END();
    total_size += data_size;
  }

  // repeated double repeated_double_value = 27;
  {
    size_t data_size = 0;
    unsigned int count = this->repeated_double_value_size();
    data_size = 8UL * count;
    if (data_size > 0) {
      total_size += 2 +
        ::google::protobuf::internal::WireFormatLite::Int32Size(data_size);
    }
    int cached_size = ::google::protobuf::internal::ToCachedSize(data_size);
    GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
    _repeated_double_value_cached_byte_size_ = cached_size;
    GOOGLE_SAFE_CONCURRENT_WRITES_END();
    total_size += data_size;
  }

  // repeated string repeated_string_value = 28;
  total_size += 2 *
      ::google::protobuf::internal::FromIntSize(this->repeated_string_value_size());
  for (int i = 0; i < this->repeated_string_value_size(); i++) {
    total_size += ::google::protobuf::internal::WireFormatLite::StringSize(
      this->repeated_string_value(i));
  }

  // repeated bytes repeated_bytes_value = 29;
  total_size += 2 *
      ::google::protobuf::internal::FromIntSize(this->repeated_bytes_value_size());
  for (int i = 0; i < this->repeated_bytes_value_size(); i++) {
    total_size += ::google::protobuf::internal::WireFormatLite::BytesSize(
      this->repeated_bytes_value(i));
  }

  // repeated .proto3.EnumType repeated_enum_value = 30;
  {
    size_t data_size = 0;
    unsigned int count = this->repeated_enum_value_size();for (unsigned int i = 0; i < count; i++) {
      data_size += ::google::protobuf::internal::WireFormatLite::EnumSize(
        this->repeated_enum_value(i));
    }
    if (data_size > 0) {
      total_size += 2 +
        ::google::protobuf::internal::WireFormatLite::Int32Size(data_size);
    }
    int cached_size = ::google::protobuf::internal::ToCachedSize(data_size);
    GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
    _repeated_enum_value_cached_byte_size_ = cached_size;
    GOOGLE_SAFE_CONCURRENT_WRITES_END();
    total_size += data_size;
  }

  // repeated .proto3.MessageType repeated_message_value = 31;
  {
    unsigned int count = this->repeated_message_value_size();
    total_size += 2UL * count;
    for (unsigned int i = 0; i < count; i++) {
      total_size +=
        ::google::protobuf::internal::WireFormatLite::MessageSizeNoVirtual(
          this->repeated_message_value(i));
    }
  }

  int cached_size = ::google::protobuf::internal::ToCachedSize(total_size);
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = cached_size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
  return total_size;
}

void TestMessage::MergeFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_merge_from_start:proto3.TestMessage)
  if (GOOGLE_PREDICT_FALSE(&from == this)) MergeFromFail(__LINE__);
  const TestMessage* source =
      ::google::protobuf::internal::DynamicCastToGenerated<const TestMessage>(
          &from);
  if (source == NULL) {
  // @@protoc_insertion_point(generalized_merge_from_cast_fail:proto3.TestMessage)
    ::google::protobuf::internal::ReflectionOps::Merge(from, this);
  } else {
  // @@protoc_insertion_point(generalized_merge_from_cast_success:proto3.TestMessage)
    UnsafeMergeFrom(*source);
  }
}

void TestMessage::MergeFrom(const TestMessage& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:proto3.TestMessage)
  if (GOOGLE_PREDICT_TRUE(&from != this)) {
    UnsafeMergeFrom(from);
  } else {
    MergeFromFail(__LINE__);
  }
}

void TestMessage::UnsafeMergeFrom(const TestMessage& from) {
  GOOGLE_DCHECK(&from != this);
  repeated_bool_value_.UnsafeMergeFrom(from.repeated_bool_value_);
  repeated_int32_value_.UnsafeMergeFrom(from.repeated_int32_value_);
  repeated_int64_value_.UnsafeMergeFrom(from.repeated_int64_value_);
  repeated_uint32_value_.UnsafeMergeFrom(from.repeated_uint32_value_);
  repeated_uint64_value_.UnsafeMergeFrom(from.repeated_uint64_value_);
  repeated_float_value_.UnsafeMergeFrom(from.repeated_float_value_);
  repeated_double_value_.UnsafeMergeFrom(from.repeated_double_value_);
  repeated_string_value_.UnsafeMergeFrom(from.repeated_string_value_);
  repeated_bytes_value_.UnsafeMergeFrom(from.repeated_bytes_value_);
  repeated_enum_value_.UnsafeMergeFrom(from.repeated_enum_value_);
  repeated_message_value_.MergeFrom(from.repeated_message_value_);
  if (from.bool_value() != 0) {
    set_bool_value(from.bool_value());
  }
  if (from.int32_value() != 0) {
    set_int32_value(from.int32_value());
  }
  if (from.int64_value() != 0) {
    set_int64_value(from.int64_value());
  }
  if (from.uint32_value() != 0) {
    set_uint32_value(from.uint32_value());
  }
  if (from.uint64_value() != 0) {
    set_uint64_value(from.uint64_value());
  }
  if (from.float_value() != 0) {
    set_float_value(from.float_value());
  }
  if (from.double_value() != 0) {
    set_double_value(from.double_value());
  }
  if (from.string_value().size() > 0) {

    string_value_.AssignWithDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), from.string_value_);
  }
  if (from.bytes_value().size() > 0) {

    bytes_value_.AssignWithDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), from.bytes_value_);
  }
  if (from.enum_value() != 0) {
    set_enum_value(from.enum_value());
  }
  if (from.has_message_value()) {
    mutable_message_value()->::proto3::MessageType::MergeFrom(from.message_value());
  }
}

void TestMessage::CopyFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_copy_from_start:proto3.TestMessage)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

void TestMessage::CopyFrom(const TestMessage& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:proto3.TestMessage)
  if (&from == this) return;
  Clear();
  UnsafeMergeFrom(from);
}

bool TestMessage::IsInitialized() const {

  return true;
}

void TestMessage::Swap(TestMessage* other) {
  if (other == this) return;
  InternalSwap(other);
}
void TestMessage::InternalSwap(TestMessage* other) {
  std::swap(bool_value_, other->bool_value_);
  std::swap(int32_value_, other->int32_value_);
  std::swap(int64_value_, other->int64_value_);
  std::swap(uint32_value_, other->uint32_value_);
  std::swap(uint64_value_, other->uint64_value_);
  std::swap(float_value_, other->float_value_);
  std::swap(double_value_, other->double_value_);
  string_value_.Swap(&other->string_value_);
  bytes_value_.Swap(&other->bytes_value_);
  std::swap(enum_value_, other->enum_value_);
  std::swap(message_value_, other->message_value_);
  repeated_bool_value_.UnsafeArenaSwap(&other->repeated_bool_value_);
  repeated_int32_value_.UnsafeArenaSwap(&other->repeated_int32_value_);
  repeated_int64_value_.UnsafeArenaSwap(&other->repeated_int64_value_);
  repeated_uint32_value_.UnsafeArenaSwap(&other->repeated_uint32_value_);
  repeated_uint64_value_.UnsafeArenaSwap(&other->repeated_uint64_value_);
  repeated_float_value_.UnsafeArenaSwap(&other->repeated_float_value_);
  repeated_double_value_.UnsafeArenaSwap(&other->repeated_double_value_);
  repeated_string_value_.UnsafeArenaSwap(&other->repeated_string_value_);
  repeated_bytes_value_.UnsafeArenaSwap(&other->repeated_bytes_value_);
  repeated_enum_value_.UnsafeArenaSwap(&other->repeated_enum_value_);
  repeated_message_value_.UnsafeArenaSwap(&other->repeated_message_value_);
  _internal_metadata_.Swap(&other->_internal_metadata_);
  std::swap(_cached_size_, other->_cached_size_);
}

::google::protobuf::Metadata TestMessage::GetMetadata() const {
  protobuf_AssignDescriptorsOnce();
  ::google::protobuf::Metadata metadata;
  metadata.descriptor = TestMessage_descriptor_;
  metadata.reflection = TestMessage_reflection_;
  return metadata;
}

#if PROTOBUF_INLINE_NOT_IN_HEADERS
// TestMessage

// optional bool bool_value = 1;
void TestMessage::clear_bool_value() {
  bool_value_ = false;
}
bool TestMessage::bool_value() const {
  // @@protoc_insertion_point(field_get:proto3.TestMessage.bool_value)
  return bool_value_;
}
void TestMessage::set_bool_value(bool value) {
  
  bool_value_ = value;
  // @@protoc_insertion_point(field_set:proto3.TestMessage.bool_value)
}

// optional int32 int32_value = 2;
void TestMessage::clear_int32_value() {
  int32_value_ = 0;
}
::google::protobuf::int32 TestMessage::int32_value() const {
  // @@protoc_insertion_point(field_get:proto3.TestMessage.int32_value)
  return int32_value_;
}
void TestMessage::set_int32_value(::google::protobuf::int32 value) {
  
  int32_value_ = value;
  // @@protoc_insertion_point(field_set:proto3.TestMessage.int32_value)
}

// optional int64 int64_value = 3;
void TestMessage::clear_int64_value() {
  int64_value_ = GOOGLE_LONGLONG(0);
}
::google::protobuf::int64 TestMessage::int64_value() const {
  // @@protoc_insertion_point(field_get:proto3.TestMessage.int64_value)
  return int64_value_;
}
void TestMessage::set_int64_value(::google::protobuf::int64 value) {
  
  int64_value_ = value;
  // @@protoc_insertion_point(field_set:proto3.TestMessage.int64_value)
}

// optional uint32 uint32_value = 4;
void TestMessage::clear_uint32_value() {
  uint32_value_ = 0u;
}
::google::protobuf::uint32 TestMessage::uint32_value() const {
  // @@protoc_insertion_point(field_get:proto3.TestMessage.uint32_value)
  return uint32_value_;
}
void TestMessage::set_uint32_value(::google::protobuf::uint32 value) {
  
  uint32_value_ = value;
  // @@protoc_insertion_point(field_set:proto3.TestMessage.uint32_value)
}

// optional uint64 uint64_value = 5;
void TestMessage::clear_uint64_value() {
  uint64_value_ = GOOGLE_ULONGLONG(0);
}
::google::protobuf::uint64 TestMessage::uint64_value() const {
  // @@protoc_insertion_point(field_get:proto3.TestMessage.uint64_value)
  return uint64_value_;
}
void TestMessage::set_uint64_value(::google::protobuf::uint64 value) {
  
  uint64_value_ = value;
  // @@protoc_insertion_point(field_set:proto3.TestMessage.uint64_value)
}

// optional float float_value = 6;
void TestMessage::clear_float_value() {
  float_value_ = 0;
}
float TestMessage::float_value() const {
  // @@protoc_insertion_point(field_get:proto3.TestMessage.float_value)
  return float_value_;
}
void TestMessage::set_float_value(float value) {
  
  float_value_ = value;
  // @@protoc_insertion_point(field_set:proto3.TestMessage.float_value)
}

// optional double double_value = 7;
void TestMessage::clear_double_value() {
  double_value_ = 0;
}
double TestMessage::double_value() const {
  // @@protoc_insertion_point(field_get:proto3.TestMessage.double_value)
  return double_value_;
}
void TestMessage::set_double_value(double value) {
  
  double_value_ = value;
  // @@protoc_insertion_point(field_set:proto3.TestMessage.double_value)
}

// optional string string_value = 8;
void TestMessage::clear_string_value() {
  string_value_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
const ::std::string& TestMessage::string_value() const {
  // @@protoc_insertion_point(field_get:proto3.TestMessage.string_value)
  return string_value_.GetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
void TestMessage::set_string_value(const ::std::string& value) {
  
  string_value_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:proto3.TestMessage.string_value)
}
void TestMessage::set_string_value(const char* value) {
  
  string_value_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:proto3.TestMessage.string_value)
}
void TestMessage::set_string_value(const char* value, size_t size) {
  
  string_value_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:proto3.TestMessage.string_value)
}
::std::string* TestMessage::mutable_string_value() {
  
  // @@protoc_insertion_point(field_mutable:proto3.TestMessage.string_value)
  return string_value_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
::std::string* TestMessage::release_string_value() {
  // @@protoc_insertion_point(field_release:proto3.TestMessage.string_value)
  
  return string_value_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
void TestMessage::set_allocated_string_value(::std::string* string_value) {
  if (string_value != NULL) {
    
  } else {
    
  }
  string_value_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), string_value);
  // @@protoc_insertion_point(field_set_allocated:proto3.TestMessage.string_value)
}

// optional bytes bytes_value = 9;
void TestMessage::clear_bytes_value() {
  bytes_value_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
const ::std::string& TestMessage::bytes_value() const {
  // @@protoc_insertion_point(field_get:proto3.TestMessage.bytes_value)
  return bytes_value_.GetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
void TestMessage::set_bytes_value(const ::std::string& value) {
  
  bytes_value_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:proto3.TestMessage.bytes_value)
}
void TestMessage::set_bytes_value(const char* value) {
  
  bytes_value_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:proto3.TestMessage.bytes_value)
}
void TestMessage::set_bytes_value(const void* value, size_t size) {
  
  bytes_value_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:proto3.TestMessage.bytes_value)
}
::std::string* TestMessage::mutable_bytes_value() {
  
  // @@protoc_insertion_point(field_mutable:proto3.TestMessage.bytes_value)
  return bytes_value_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
::std::string* TestMessage::release_bytes_value() {
  // @@protoc_insertion_point(field_release:proto3.TestMessage.bytes_value)
  
  return bytes_value_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
void TestMessage::set_allocated_bytes_value(::std::string* bytes_value) {
  if (bytes_value != NULL) {
    
  } else {
    
  }
  bytes_value_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), bytes_value);
  // @@protoc_insertion_point(field_set_allocated:proto3.TestMessage.bytes_value)
}

// optional .proto3.EnumType enum_value = 10;
void TestMessage::clear_enum_value() {
  enum_value_ = 0;
}
::proto3::EnumType TestMessage::enum_value() const {
  // @@protoc_insertion_point(field_get:proto3.TestMessage.enum_value)
  return static_cast< ::proto3::EnumType >(enum_value_);
}
void TestMessage::set_enum_value(::proto3::EnumType value) {
  
  enum_value_ = value;
  // @@protoc_insertion_point(field_set:proto3.TestMessage.enum_value)
}

// optional .proto3.MessageType message_value = 11;
bool TestMessage::has_message_value() const {
  return this != internal_default_instance() && message_value_ != NULL;
}
void TestMessage::clear_message_value() {
  if (GetArenaNoVirtual() == NULL && message_value_ != NULL) delete message_value_;
  message_value_ = NULL;
}
const ::proto3::MessageType& TestMessage::message_value() const {
  // @@protoc_insertion_point(field_get:proto3.TestMessage.message_value)
  return message_value_ != NULL ? *message_value_
                         : *::proto3::MessageType::internal_default_instance();
}
::proto3::MessageType* TestMessage::mutable_message_value() {
  
  if (message_value_ == NULL) {
    message_value_ = new ::proto3::MessageType;
  }
  // @@protoc_insertion_point(field_mutable:proto3.TestMessage.message_value)
  return message_value_;
}
::proto3::MessageType* TestMessage::release_message_value() {
  // @@protoc_insertion_point(field_release:proto3.TestMessage.message_value)
  
  ::proto3::MessageType* temp = message_value_;
  message_value_ = NULL;
  return temp;
}
void TestMessage::set_allocated_message_value(::proto3::MessageType* message_value) {
  delete message_value_;
  message_value_ = message_value;
  if (message_value) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_set_allocated:proto3.TestMessage.message_value)
}

// repeated bool repeated_bool_value = 21;
int TestMessage::repeated_bool_value_size() const {
  return repeated_bool_value_.size();
}
void TestMessage::clear_repeated_bool_value() {
  repeated_bool_value_.Clear();
}
bool TestMessage::repeated_bool_value(int index) const {
  // @@protoc_insertion_point(field_get:proto3.TestMessage.repeated_bool_value)
  return repeated_bool_value_.Get(index);
}
void TestMessage::set_repeated_bool_value(int index, bool value) {
  repeated_bool_value_.Set(index, value);
  // @@protoc_insertion_point(field_set:proto3.TestMessage.repeated_bool_value)
}
void TestMessage::add_repeated_bool_value(bool value) {
  repeated_bool_value_.Add(value);
  // @@protoc_insertion_point(field_add:proto3.TestMessage.repeated_bool_value)
}
const ::google::protobuf::RepeatedField< bool >&
TestMessage::repeated_bool_value() const {
  // @@protoc_insertion_point(field_list:proto3.TestMessage.repeated_bool_value)
  return repeated_bool_value_;
}
::google::protobuf::RepeatedField< bool >*
TestMessage::mutable_repeated_bool_value() {
  // @@protoc_insertion_point(field_mutable_list:proto3.TestMessage.repeated_bool_value)
  return &repeated_bool_value_;
}

// repeated int32 repeated_int32_value = 22;
int TestMessage::repeated_int32_value_size() const {
  return repeated_int32_value_.size();
}
void TestMessage::clear_repeated_int32_value() {
  repeated_int32_value_.Clear();
}
::google::protobuf::int32 TestMessage::repeated_int32_value(int index) const {
  // @@protoc_insertion_point(field_get:proto3.TestMessage.repeated_int32_value)
  return repeated_int32_value_.Get(index);
}
void TestMessage::set_repeated_int32_value(int index, ::google::protobuf::int32 value) {
  repeated_int32_value_.Set(index, value);
  // @@protoc_insertion_point(field_set:proto3.TestMessage.repeated_int32_value)
}
void TestMessage::add_repeated_int32_value(::google::protobuf::int32 value) {
  repeated_int32_value_.Add(value);
  // @@protoc_insertion_point(field_add:proto3.TestMessage.repeated_int32_value)
}
const ::google::protobuf::RepeatedField< ::google::protobuf::int32 >&
TestMessage::repeated_int32_value() const {
  // @@protoc_insertion_point(field_list:proto3.TestMessage.repeated_int32_value)
  return repeated_int32_value_;
}
::google::protobuf::RepeatedField< ::google::protobuf::int32 >*
TestMessage::mutable_repeated_int32_value() {
  // @@protoc_insertion_point(field_mutable_list:proto3.TestMessage.repeated_int32_value)
  return &repeated_int32_value_;
}

// repeated int64 repeated_int64_value = 23;
int TestMessage::repeated_int64_value_size() const {
  return repeated_int64_value_.size();
}
void TestMessage::clear_repeated_int64_value() {
  repeated_int64_value_.Clear();
}
::google::protobuf::int64 TestMessage::repeated_int64_value(int index) const {
  // @@protoc_insertion_point(field_get:proto3.TestMessage.repeated_int64_value)
  return repeated_int64_value_.Get(index);
}
void TestMessage::set_repeated_int64_value(int index, ::google::protobuf::int64 value) {
  repeated_int64_value_.Set(index, value);
  // @@protoc_insertion_point(field_set:proto3.TestMessage.repeated_int64_value)
}
void TestMessage::add_repeated_int64_value(::google::protobuf::int64 value) {
  repeated_int64_value_.Add(value);
  // @@protoc_insertion_point(field_add:proto3.TestMessage.repeated_int64_value)
}
const ::google::protobuf::RepeatedField< ::google::protobuf::int64 >&
TestMessage::repeated_int64_value() const {
  // @@protoc_insertion_point(field_list:proto3.TestMessage.repeated_int64_value)
  return repeated_int64_value_;
}
::google::protobuf::RepeatedField< ::google::protobuf::int64 >*
TestMessage::mutable_repeated_int64_value() {
  // @@protoc_insertion_point(field_mutable_list:proto3.TestMessage.repeated_int64_value)
  return &repeated_int64_value_;
}

// repeated uint32 repeated_uint32_value = 24;
int TestMessage::repeated_uint32_value_size() const {
  return repeated_uint32_value_.size();
}
void TestMessage::clear_repeated_uint32_value() {
  repeated_uint32_value_.Clear();
}
::google::protobuf::uint32 TestMessage::repeated_uint32_value(int index) const {
  // @@protoc_insertion_point(field_get:proto3.TestMessage.repeated_uint32_value)
  return repeated_uint32_value_.Get(index);
}
void TestMessage::set_repeated_uint32_value(int index, ::google::protobuf::uint32 value) {
  repeated_uint32_value_.Set(index, value);
  // @@protoc_insertion_point(field_set:proto3.TestMessage.repeated_uint32_value)
}
void TestMessage::add_repeated_uint32_value(::google::protobuf::uint32 value) {
  repeated_uint32_value_.Add(value);
  // @@protoc_insertion_point(field_add:proto3.TestMessage.repeated_uint32_value)
}
const ::google::protobuf::RepeatedField< ::google::protobuf::uint32 >&
TestMessage::repeated_uint32_value() const {
  // @@protoc_insertion_point(field_list:proto3.TestMessage.repeated_uint32_value)
  return repeated_uint32_value_;
}
::google::protobuf::RepeatedField< ::google::protobuf::uint32 >*
TestMessage::mutable_repeated_uint32_value() {
  // @@protoc_insertion_point(field_mutable_list:proto3.TestMessage.repeated_uint32_value)
  return &repeated_uint32_value_;
}

// repeated uint64 repeated_uint64_value = 25;
int TestMessage::repeated_uint64_value_size() const {
  return repeated_uint64_value_.size();
}
void TestMessage::clear_repeated_uint64_value() {
  repeated_uint64_value_.Clear();
}
::google::protobuf::uint64 TestMessage::repeated_uint64_value(int index) const {
  // @@protoc_insertion_point(field_get:proto3.TestMessage.repeated_uint64_value)
  return repeated_uint64_value_.Get(index);
}
void TestMessage::set_repeated_uint64_value(int index, ::google::protobuf::uint64 value) {
  repeated_uint64_value_.Set(index, value);
  // @@protoc_insertion_point(field_set:proto3.TestMessage.repeated_uint64_value)
}
void TestMessage::add_repeated_uint64_value(::google::protobuf::uint64 value) {
  repeated_uint64_value_.Add(value);
  // @@protoc_insertion_point(field_add:proto3.TestMessage.repeated_uint64_value)
}
const ::google::protobuf::RepeatedField< ::google::protobuf::uint64 >&
TestMessage::repeated_uint64_value() const {
  // @@protoc_insertion_point(field_list:proto3.TestMessage.repeated_uint64_value)
  return repeated_uint64_value_;
}
::google::protobuf::RepeatedField< ::google::protobuf::uint64 >*
TestMessage::mutable_repeated_uint64_value() {
  // @@protoc_insertion_point(field_mutable_list:proto3.TestMessage.repeated_uint64_value)
  return &repeated_uint64_value_;
}

// repeated float repeated_float_value = 26;
int TestMessage::repeated_float_value_size() const {
  return repeated_float_value_.size();
}
void TestMessage::clear_repeated_float_value() {
  repeated_float_value_.Clear();
}
float TestMessage::repeated_float_value(int index) const {
  // @@protoc_insertion_point(field_get:proto3.TestMessage.repeated_float_value)
  return repeated_float_value_.Get(index);
}
void TestMessage::set_repeated_float_value(int index, float value) {
  repeated_float_value_.Set(index, value);
  // @@protoc_insertion_point(field_set:proto3.TestMessage.repeated_float_value)
}
void TestMessage::add_repeated_float_value(float value) {
  repeated_float_value_.Add(value);
  // @@protoc_insertion_point(field_add:proto3.TestMessage.repeated_float_value)
}
const ::google::protobuf::RepeatedField< float >&
TestMessage::repeated_float_value() const {
  // @@protoc_insertion_point(field_list:proto3.TestMessage.repeated_float_value)
  return repeated_float_value_;
}
::google::protobuf::RepeatedField< float >*
TestMessage::mutable_repeated_float_value() {
  // @@protoc_insertion_point(field_mutable_list:proto3.TestMessage.repeated_float_value)
  return &repeated_float_value_;
}

// repeated double repeated_double_value = 27;
int TestMessage::repeated_double_value_size() const {
  return repeated_double_value_.size();
}
void TestMessage::clear_repeated_double_value() {
  repeated_double_value_.Clear();
}
double TestMessage::repeated_double_value(int index) const {
  // @@protoc_insertion_point(field_get:proto3.TestMessage.repeated_double_value)
  return repeated_double_value_.Get(index);
}
void TestMessage::set_repeated_double_value(int index, double value) {
  repeated_double_value_.Set(index, value);
  // @@protoc_insertion_point(field_set:proto3.TestMessage.repeated_double_value)
}
void TestMessage::add_repeated_double_value(double value) {
  repeated_double_value_.Add(value);
  // @@protoc_insertion_point(field_add:proto3.TestMessage.repeated_double_value)
}
const ::google::protobuf::RepeatedField< double >&
TestMessage::repeated_double_value() const {
  // @@protoc_insertion_point(field_list:proto3.TestMessage.repeated_double_value)
  return repeated_double_value_;
}
::google::protobuf::RepeatedField< double >*
TestMessage::mutable_repeated_double_value() {
  // @@protoc_insertion_point(field_mutable_list:proto3.TestMessage.repeated_double_value)
  return &repeated_double_value_;
}

// repeated string repeated_string_value = 28;
int TestMessage::repeated_string_value_size() const {
  return repeated_string_value_.size();
}
void TestMessage::clear_repeated_string_value() {
  repeated_string_value_.Clear();
}
const ::std::string& TestMessage::repeated_string_value(int index) const {
  // @@protoc_insertion_point(field_get:proto3.TestMessage.repeated_string_value)
  return repeated_string_value_.Get(index);
}
::std::string* TestMessage::mutable_repeated_string_value(int index) {
  // @@protoc_insertion_point(field_mutable:proto3.TestMessage.repeated_string_value)
  return repeated_string_value_.Mutable(index);
}
void TestMessage::set_repeated_string_value(int index, const ::std::string& value) {
  // @@protoc_insertion_point(field_set:proto3.TestMessage.repeated_string_value)
  repeated_string_value_.Mutable(index)->assign(value);
}
void TestMessage::set_repeated_string_value(int index, const char* value) {
  repeated_string_value_.Mutable(index)->assign(value);
  // @@protoc_insertion_point(field_set_char:proto3.TestMessage.repeated_string_value)
}
void TestMessage::set_repeated_string_value(int index, const char* value, size_t size) {
  repeated_string_value_.Mutable(index)->assign(
    reinterpret_cast<const char*>(value), size);
  // @@protoc_insertion_point(field_set_pointer:proto3.TestMessage.repeated_string_value)
}
::std::string* TestMessage::add_repeated_string_value() {
  // @@protoc_insertion_point(field_add_mutable:proto3.TestMessage.repeated_string_value)
  return repeated_string_value_.Add();
}
void TestMessage::add_repeated_string_value(const ::std::string& value) {
  repeated_string_value_.Add()->assign(value);
  // @@protoc_insertion_point(field_add:proto3.TestMessage.repeated_string_value)
}
void TestMessage::add_repeated_string_value(const char* value) {
  repeated_string_value_.Add()->assign(value);
  // @@protoc_insertion_point(field_add_char:proto3.TestMessage.repeated_string_value)
}
void TestMessage::add_repeated_string_value(const char* value, size_t size) {
  repeated_string_value_.Add()->assign(reinterpret_cast<const char*>(value), size);
  // @@protoc_insertion_point(field_add_pointer:proto3.TestMessage.repeated_string_value)
}
const ::google::protobuf::RepeatedPtrField< ::std::string>&
TestMessage::repeated_string_value() const {
  // @@protoc_insertion_point(field_list:proto3.TestMessage.repeated_string_value)
  return repeated_string_value_;
}
::google::protobuf::RepeatedPtrField< ::std::string>*
TestMessage::mutable_repeated_string_value() {
  // @@protoc_insertion_point(field_mutable_list:proto3.TestMessage.repeated_string_value)
  return &repeated_string_value_;
}

// repeated bytes repeated_bytes_value = 29;
int TestMessage::repeated_bytes_value_size() const {
  return repeated_bytes_value_.size();
}
void TestMessage::clear_repeated_bytes_value() {
  repeated_bytes_value_.Clear();
}
const ::std::string& TestMessage::repeated_bytes_value(int index) const {
  // @@protoc_insertion_point(field_get:proto3.TestMessage.repeated_bytes_value)
  return repeated_bytes_value_.Get(index);
}
::std::string* TestMessage::mutable_repeated_bytes_value(int index) {
  // @@protoc_insertion_point(field_mutable:proto3.TestMessage.repeated_bytes_value)
  return repeated_bytes_value_.Mutable(index);
}
void TestMessage::set_repeated_bytes_value(int index, const ::std::string& value) {
  // @@protoc_insertion_point(field_set:proto3.TestMessage.repeated_bytes_value)
  repeated_bytes_value_.Mutable(index)->assign(value);
}
void TestMessage::set_repeated_bytes_value(int index, const char* value) {
  repeated_bytes_value_.Mutable(index)->assign(value);
  // @@protoc_insertion_point(field_set_char:proto3.TestMessage.repeated_bytes_value)
}
void TestMessage::set_repeated_bytes_value(int index, const void* value, size_t size) {
  repeated_bytes_value_.Mutable(index)->assign(
    reinterpret_cast<const char*>(value), size);
  // @@protoc_insertion_point(field_set_pointer:proto3.TestMessage.repeated_bytes_value)
}
::std::string* TestMessage::add_repeated_bytes_value() {
  // @@protoc_insertion_point(field_add_mutable:proto3.TestMessage.repeated_bytes_value)
  return repeated_bytes_value_.Add();
}
void TestMessage::add_repeated_bytes_value(const ::std::string& value) {
  repeated_bytes_value_.Add()->assign(value);
  // @@protoc_insertion_point(field_add:proto3.TestMessage.repeated_bytes_value)
}
void TestMessage::add_repeated_bytes_value(const char* value) {
  repeated_bytes_value_.Add()->assign(value);
  // @@protoc_insertion_point(field_add_char:proto3.TestMessage.repeated_bytes_value)
}
void TestMessage::add_repeated_bytes_value(const void* value, size_t size) {
  repeated_bytes_value_.Add()->assign(reinterpret_cast<const char*>(value), size);
  // @@protoc_insertion_point(field_add_pointer:proto3.TestMessage.repeated_bytes_value)
}
const ::google::protobuf::RepeatedPtrField< ::std::string>&
TestMessage::repeated_bytes_value() const {
  // @@protoc_insertion_point(field_list:proto3.TestMessage.repeated_bytes_value)
  return repeated_bytes_value_;
}
::google::protobuf::RepeatedPtrField< ::std::string>*
TestMessage::mutable_repeated_bytes_value() {
  // @@protoc_insertion_point(field_mutable_list:proto3.TestMessage.repeated_bytes_value)
  return &repeated_bytes_value_;
}

// repeated .proto3.EnumType repeated_enum_value = 30;
int TestMessage::repeated_enum_value_size() const {
  return repeated_enum_value_.size();
}
void TestMessage::clear_repeated_enum_value() {
  repeated_enum_value_.Clear();
}
::proto3::EnumType TestMessage::repeated_enum_value(int index) const {
  // @@protoc_insertion_point(field_get:proto3.TestMessage.repeated_enum_value)
  return static_cast< ::proto3::EnumType >(repeated_enum_value_.Get(index));
}
void TestMessage::set_repeated_enum_value(int index, ::proto3::EnumType value) {
  repeated_enum_value_.Set(index, value);
  // @@protoc_insertion_point(field_set:proto3.TestMessage.repeated_enum_value)
}
void TestMessage::add_repeated_enum_value(::proto3::EnumType value) {
  repeated_enum_value_.Add(value);
  // @@protoc_insertion_point(field_add:proto3.TestMessage.repeated_enum_value)
}
const ::google::protobuf::RepeatedField<int>&
TestMessage::repeated_enum_value() const {
  // @@protoc_insertion_point(field_list:proto3.TestMessage.repeated_enum_value)
  return repeated_enum_value_;
}
::google::protobuf::RepeatedField<int>*
TestMessage::mutable_repeated_enum_value() {
  // @@protoc_insertion_point(field_mutable_list:proto3.TestMessage.repeated_enum_value)
  return &repeated_enum_value_;
}

// repeated .proto3.MessageType repeated_message_value = 31;
int TestMessage::repeated_message_value_size() const {
  return repeated_message_value_.size();
}
void TestMessage::clear_repeated_message_value() {
  repeated_message_value_.Clear();
}
const ::proto3::MessageType& TestMessage::repeated_message_value(int index) const {
  // @@protoc_insertion_point(field_get:proto3.TestMessage.repeated_message_value)
  return repeated_message_value_.Get(index);
}
::proto3::MessageType* TestMessage::mutable_repeated_message_value(int index) {
  // @@protoc_insertion_point(field_mutable:proto3.TestMessage.repeated_message_value)
  return repeated_message_value_.Mutable(index);
}
::proto3::MessageType* TestMessage::add_repeated_message_value() {
  // @@protoc_insertion_point(field_add:proto3.TestMessage.repeated_message_value)
  return repeated_message_value_.Add();
}
::google::protobuf::RepeatedPtrField< ::proto3::MessageType >*
TestMessage::mutable_repeated_message_value() {
  // @@protoc_insertion_point(field_mutable_list:proto3.TestMessage.repeated_message_value)
  return &repeated_message_value_;
}
const ::google::protobuf::RepeatedPtrField< ::proto3::MessageType >&
TestMessage::repeated_message_value() const {
  // @@protoc_insertion_point(field_list:proto3.TestMessage.repeated_message_value)
  return repeated_message_value_;
}

inline const TestMessage* TestMessage::internal_default_instance() {
  return &TestMessage_default_instance_.get();
}
#endif  // PROTOBUF_INLINE_NOT_IN_HEADERS

// ===================================================================

#if !defined(_MSC_VER) || _MSC_VER >= 1900
const int TestOneof::kOneofInt32ValueFieldNumber;
const int TestOneof::kOneofStringValueFieldNumber;
const int TestOneof::kOneofBytesValueFieldNumber;
const int TestOneof::kOneofEnumValueFieldNumber;
const int TestOneof::kOneofMessageValueFieldNumber;
#endif  // !defined(_MSC_VER) || _MSC_VER >= 1900

TestOneof::TestOneof()
  : ::google::protobuf::Message(), _internal_metadata_(NULL) {
  if (this != internal_default_instance()) protobuf_InitDefaults_google_2fprotobuf_2futil_2fjson_5fformat_5fproto3_2eproto();
  SharedCtor();
  // @@protoc_insertion_point(constructor:proto3.TestOneof)
}

void TestOneof::InitAsDefaultInstance() {
  TestOneof_default_oneof_instance_->oneof_int32_value_ = 0;
  TestOneof_default_oneof_instance_->oneof_string_value_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  TestOneof_default_oneof_instance_->oneof_bytes_value_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  TestOneof_default_oneof_instance_->oneof_enum_value_ = 0;
  TestOneof_default_oneof_instance_->oneof_message_value_ = const_cast< ::proto3::MessageType*>(
      ::proto3::MessageType::internal_default_instance());
}

TestOneof::TestOneof(const TestOneof& from)
  : ::google::protobuf::Message(),
    _internal_metadata_(NULL) {
  SharedCtor();
  UnsafeMergeFrom(from);
  // @@protoc_insertion_point(copy_constructor:proto3.TestOneof)
}

void TestOneof::SharedCtor() {
  clear_has_oneof_value();
  _cached_size_ = 0;
}

TestOneof::~TestOneof() {
  // @@protoc_insertion_point(destructor:proto3.TestOneof)
  SharedDtor();
}

void TestOneof::SharedDtor() {
  if (has_oneof_value()) {
    clear_oneof_value();
  }
}

void TestOneof::SetCachedSize(int size) const {
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
}
const ::google::protobuf::Descriptor* TestOneof::descriptor() {
  protobuf_AssignDescriptorsOnce();
  return TestOneof_descriptor_;
}

const TestOneof& TestOneof::default_instance() {
  protobuf_InitDefaults_google_2fprotobuf_2futil_2fjson_5fformat_5fproto3_2eproto();
  return *internal_default_instance();
}

::google::protobuf::internal::ExplicitlyConstructed<TestOneof> TestOneof_default_instance_;

TestOneof* TestOneof::New(::google::protobuf::Arena* arena) const {
  TestOneof* n = new TestOneof;
  if (arena != NULL) {
    arena->Own(n);
  }
  return n;
}

void TestOneof::clear_oneof_value() {
// @@protoc_insertion_point(one_of_clear_start:proto3.TestOneof)
  switch (oneof_value_case()) {
    case kOneofInt32Value: {
      // No need to clear
      break;
    }
    case kOneofStringValue: {
      oneof_value_.oneof_string_value_.DestroyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
      break;
    }
    case kOneofBytesValue: {
      oneof_value_.oneof_bytes_value_.DestroyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
      break;
    }
    case kOneofEnumValue: {
      // No need to clear
      break;
    }
    case kOneofMessageValue: {
      delete oneof_value_.oneof_message_value_;
      break;
    }
    case ONEOF_VALUE_NOT_SET: {
      break;
    }
  }
  _oneof_case_[0] = ONEOF_VALUE_NOT_SET;
}


void TestOneof::Clear() {
// @@protoc_insertion_point(message_clear_start:proto3.TestOneof)
  clear_oneof_value();
}

bool TestOneof::MergePartialFromCodedStream(
    ::google::protobuf::io::CodedInputStream* input) {
#define DO_(EXPRESSION) if (!GOOGLE_PREDICT_TRUE(EXPRESSION)) goto failure
  ::google::protobuf::uint32 tag;
  // @@protoc_insertion_point(parse_start:proto3.TestOneof)
  for (;;) {
    ::std::pair< ::google::protobuf::uint32, bool> p = input->ReadTagWithCutoff(127);
    tag = p.first;
    if (!p.second) goto handle_unusual;
    switch (::google::protobuf::internal::WireFormatLite::GetTagFieldNumber(tag)) {
      // optional int32 oneof_int32_value = 1;
      case 1: {
        if (tag == 8) {
          clear_oneof_value();
          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int32, ::google::protobuf::internal::WireFormatLite::TYPE_INT32>(
                 input, &oneof_value_.oneof_int32_value_)));
          set_has_oneof_int32_value();
        } else {
          goto handle_unusual;
        }
        goto after_oneof_message_value;
        break;
      }

      // optional string oneof_string_value = 2;
      case 2: {
        if (tag == 18) {
          DO_(::google::protobuf::internal::WireFormatLite::ReadString(
                input, this->mutable_oneof_string_value()));
          DO_(::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
            this->oneof_string_value().data(), this->oneof_string_value().length(),
            ::google::protobuf::internal::WireFormatLite::PARSE,
            "proto3.TestOneof.oneof_string_value"));
        } else {
          goto handle_unusual;
        }
        goto after_oneof_message_value;
        break;
      }

      // optional bytes oneof_bytes_value = 3;
      case 3: {
        if (tag == 26) {
          DO_(::google::protobuf::internal::WireFormatLite::ReadBytes(
                input, this->mutable_oneof_bytes_value()));
        } else {
          goto handle_unusual;
        }
        goto after_oneof_message_value;
        break;
      }

      // optional .proto3.EnumType oneof_enum_value = 4;
      case 4: {
        if (tag == 32) {
          int value;
          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   int, ::google::protobuf::internal::WireFormatLite::TYPE_ENUM>(
                 input, &value)));
          set_oneof_enum_value(static_cast< ::proto3::EnumType >(value));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(42)) goto parse_oneof_message_value;
        break;
      }

      // optional .proto3.MessageType oneof_message_value = 5;
      case 5: {
        if (tag == 42) {
         parse_oneof_message_value:
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessageNoVirtual(
               input, mutable_oneof_message_value()));
        } else {
          goto handle_unusual;
        }
       after_oneof_message_value:
        if (input->ExpectAtEnd()) goto success;
        break;
      }

      default: {
      handle_unusual:
        if (tag == 0 ||
            ::google::protobuf::internal::WireFormatLite::GetTagWireType(tag) ==
            ::google::protobuf::internal::WireFormatLite::WIRETYPE_END_GROUP) {
          goto success;
        }
        DO_(::google::protobuf::internal::WireFormatLite::SkipField(input, tag));
        break;
      }
    }
  }
success:
  // @@protoc_insertion_point(parse_success:proto3.TestOneof)
  return true;
failure:
  // @@protoc_insertion_point(parse_failure:proto3.TestOneof)
  return false;
#undef DO_
}

void TestOneof::SerializeWithCachedSizes(
    ::google::protobuf::io::CodedOutputStream* output) const {
  // @@protoc_insertion_point(serialize_start:proto3.TestOneof)
  // optional int32 oneof_int32_value = 1;
  if (has_oneof_int32_value()) {
    ::google::protobuf::internal::WireFormatLite::WriteInt32(1, this->oneof_int32_value(), output);
  }

  // optional string oneof_string_value = 2;
  if (has_oneof_string_value()) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->oneof_string_value().data(), this->oneof_string_value().length(),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "proto3.TestOneof.oneof_string_value");
    ::google::protobuf::internal::WireFormatLite::WriteStringMaybeAliased(
      2, this->oneof_string_value(), output);
  }

  // optional bytes oneof_bytes_value = 3;
  if (has_oneof_bytes_value()) {
    ::google::protobuf::internal::WireFormatLite::WriteBytesMaybeAliased(
      3, this->oneof_bytes_value(), output);
  }

  // optional .proto3.EnumType oneof_enum_value = 4;
  if (has_oneof_enum_value()) {
    ::google::protobuf::internal::WireFormatLite::WriteEnum(
      4, this->oneof_enum_value(), output);
  }

  // optional .proto3.MessageType oneof_message_value = 5;
  if (has_oneof_message_value()) {
    ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
      5, *oneof_value_.oneof_message_value_, output);
  }

  // @@protoc_insertion_point(serialize_end:proto3.TestOneof)
}

::google::protobuf::uint8* TestOneof::InternalSerializeWithCachedSizesToArray(
    bool deterministic, ::google::protobuf::uint8* target) const {
  (void)deterministic; // Unused
  // @@protoc_insertion_point(serialize_to_array_start:proto3.TestOneof)
  // optional int32 oneof_int32_value = 1;
  if (has_oneof_int32_value()) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt32ToArray(1, this->oneof_int32_value(), target);
  }

  // optional string oneof_string_value = 2;
  if (has_oneof_string_value()) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->oneof_string_value().data(), this->oneof_string_value().length(),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "proto3.TestOneof.oneof_string_value");
    target =
      ::google::protobuf::internal::WireFormatLite::WriteStringToArray(
        2, this->oneof_string_value(), target);
  }

  // optional bytes oneof_bytes_value = 3;
  if (has_oneof_bytes_value()) {
    target =
      ::google::protobuf::internal::WireFormatLite::WriteBytesToArray(
        3, this->oneof_bytes_value(), target);
  }

  // optional .proto3.EnumType oneof_enum_value = 4;
  if (has_oneof_enum_value()) {
    target = ::google::protobuf::internal::WireFormatLite::WriteEnumToArray(
      4, this->oneof_enum_value(), target);
  }

  // optional .proto3.MessageType oneof_message_value = 5;
  if (has_oneof_message_value()) {
    target = ::google::protobuf::internal::WireFormatLite::
      InternalWriteMessageNoVirtualToArray(
        5, *oneof_value_.oneof_message_value_, false, target);
  }

  // @@protoc_insertion_point(serialize_to_array_end:proto3.TestOneof)
  return target;
}

size_t TestOneof::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:proto3.TestOneof)
  size_t total_size = 0;

  switch (oneof_value_case()) {
    // optional int32 oneof_int32_value = 1;
    case kOneofInt32Value: {
      total_size += 1 +
        ::google::protobuf::internal::WireFormatLite::Int32Size(
          this->oneof_int32_value());
      break;
    }
    // optional string oneof_string_value = 2;
    case kOneofStringValue: {
      total_size += 1 +
        ::google::protobuf::internal::WireFormatLite::StringSize(
          this->oneof_string_value());
      break;
    }
    // optional bytes oneof_bytes_value = 3;
    case kOneofBytesValue: {
      total_size += 1 +
        ::google::protobuf::internal::WireFormatLite::BytesSize(
          this->oneof_bytes_value());
      break;
    }
    // optional .proto3.EnumType oneof_enum_value = 4;
    case kOneofEnumValue: {
      total_size += 1 +
        ::google::protobuf::internal::WireFormatLite::EnumSize(this->oneof_enum_value());
      break;
    }
    // optional .proto3.MessageType oneof_message_value = 5;
    case kOneofMessageValue: {
      total_size += 1 +
        ::google::protobuf::internal::WireFormatLite::MessageSizeNoVirtual(
          *oneof_value_.oneof_message_value_);
      break;
    }
    case ONEOF_VALUE_NOT_SET: {
      break;
    }
  }
  int cached_size = ::google::protobuf::internal::ToCachedSize(total_size);
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = cached_size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
  return total_size;
}

void TestOneof::MergeFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_merge_from_start:proto3.TestOneof)
  if (GOOGLE_PREDICT_FALSE(&from == this)) MergeFromFail(__LINE__);
  const TestOneof* source =
      ::google::protobuf::internal::DynamicCastToGenerated<const TestOneof>(
          &from);
  if (source == NULL) {
  // @@protoc_insertion_point(generalized_merge_from_cast_fail:proto3.TestOneof)
    ::google::protobuf::internal::ReflectionOps::Merge(from, this);
  } else {
  // @@protoc_insertion_point(generalized_merge_from_cast_success:proto3.TestOneof)
    UnsafeMergeFrom(*source);
  }
}

void TestOneof::MergeFrom(const TestOneof& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:proto3.TestOneof)
  if (GOOGLE_PREDICT_TRUE(&from != this)) {
    UnsafeMergeFrom(from);
  } else {
    MergeFromFail(__LINE__);
  }
}

void TestOneof::UnsafeMergeFrom(const TestOneof& from) {
  GOOGLE_DCHECK(&from != this);
  switch (from.oneof_value_case()) {
    case kOneofInt32Value: {
      set_oneof_int32_value(from.oneof_int32_value());
      break;
    }
    case kOneofStringValue: {
      set_oneof_string_value(from.oneof_string_value());
      break;
    }
    case kOneofBytesValue: {
      set_oneof_bytes_value(from.oneof_bytes_value());
      break;
    }
    case kOneofEnumValue: {
      set_oneof_enum_value(from.oneof_enum_value());
      break;
    }
    case kOneofMessageValue: {
      mutable_oneof_message_value()->::proto3::MessageType::MergeFrom(from.oneof_message_value());
      break;
    }
    case ONEOF_VALUE_NOT_SET: {
      break;
    }
  }
}

void TestOneof::CopyFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_copy_from_start:proto3.TestOneof)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

void TestOneof::CopyFrom(const TestOneof& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:proto3.TestOneof)
  if (&from == this) return;
  Clear();
  UnsafeMergeFrom(from);
}

bool TestOneof::IsInitialized() const {

  return true;
}

void TestOneof::Swap(TestOneof* other) {
  if (other == this) return;
  InternalSwap(other);
}
void TestOneof::InternalSwap(TestOneof* other) {
  std::swap(oneof_value_, other->oneof_value_);
  std::swap(_oneof_case_[0], other->_oneof_case_[0]);
  _internal_metadata_.Swap(&other->_internal_metadata_);
  std::swap(_cached_size_, other->_cached_size_);
}

::google::protobuf::Metadata TestOneof::GetMetadata() const {
  protobuf_AssignDescriptorsOnce();
  ::google::protobuf::Metadata metadata;
  metadata.descriptor = TestOneof_descriptor_;
  metadata.reflection = TestOneof_reflection_;
  return metadata;
}

#if PROTOBUF_INLINE_NOT_IN_HEADERS
// TestOneof

// optional int32 oneof_int32_value = 1;
bool TestOneof::has_oneof_int32_value() const {
  return oneof_value_case() == kOneofInt32Value;
}
void TestOneof::set_has_oneof_int32_value() {
  _oneof_case_[0] = kOneofInt32Value;
}
void TestOneof::clear_oneof_int32_value() {
  if (has_oneof_int32_value()) {
    oneof_value_.oneof_int32_value_ = 0;
    clear_has_oneof_value();
  }
}
::google::protobuf::int32 TestOneof::oneof_int32_value() const {
  // @@protoc_insertion_point(field_get:proto3.TestOneof.oneof_int32_value)
  if (has_oneof_int32_value()) {
    return oneof_value_.oneof_int32_value_;
  }
  return 0;
}
void TestOneof::set_oneof_int32_value(::google::protobuf::int32 value) {
  if (!has_oneof_int32_value()) {
    clear_oneof_value();
    set_has_oneof_int32_value();
  }
  oneof_value_.oneof_int32_value_ = value;
  // @@protoc_insertion_point(field_set:proto3.TestOneof.oneof_int32_value)
}

// optional string oneof_string_value = 2;
bool TestOneof::has_oneof_string_value() const {
  return oneof_value_case() == kOneofStringValue;
}
void TestOneof::set_has_oneof_string_value() {
  _oneof_case_[0] = kOneofStringValue;
}
void TestOneof::clear_oneof_string_value() {
  if (has_oneof_string_value()) {
    oneof_value_.oneof_string_value_.DestroyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
    clear_has_oneof_value();
  }
}
const ::std::string& TestOneof::oneof_string_value() const {
  // @@protoc_insertion_point(field_get:proto3.TestOneof.oneof_string_value)
  if (has_oneof_string_value()) {
    return oneof_value_.oneof_string_value_.GetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  }
  return *&::google::protobuf::internal::GetEmptyStringAlreadyInited();
}
void TestOneof::set_oneof_string_value(const ::std::string& value) {
  // @@protoc_insertion_point(field_set:proto3.TestOneof.oneof_string_value)
  if (!has_oneof_string_value()) {
    clear_oneof_value();
    set_has_oneof_string_value();
    oneof_value_.oneof_string_value_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  }
  oneof_value_.oneof_string_value_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:proto3.TestOneof.oneof_string_value)
}
void TestOneof::set_oneof_string_value(const char* value) {
  if (!has_oneof_string_value()) {
    clear_oneof_value();
    set_has_oneof_string_value();
    oneof_value_.oneof_string_value_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  }
  oneof_value_.oneof_string_value_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(value));
  // @@protoc_insertion_point(field_set_char:proto3.TestOneof.oneof_string_value)
}
void TestOneof::set_oneof_string_value(const char* value, size_t size) {
  if (!has_oneof_string_value()) {
    clear_oneof_value();
    set_has_oneof_string_value();
    oneof_value_.oneof_string_value_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  }
  oneof_value_.oneof_string_value_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(
      reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:proto3.TestOneof.oneof_string_value)
}
::std::string* TestOneof::mutable_oneof_string_value() {
  if (!has_oneof_string_value()) {
    clear_oneof_value();
    set_has_oneof_string_value();
    oneof_value_.oneof_string_value_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  }
  // @@protoc_insertion_point(field_mutable:proto3.TestOneof.oneof_string_value)
  return oneof_value_.oneof_string_value_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
::std::string* TestOneof::release_oneof_string_value() {
  // @@protoc_insertion_point(field_release:proto3.TestOneof.oneof_string_value)
  if (has_oneof_string_value()) {
    clear_has_oneof_value();
    return oneof_value_.oneof_string_value_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  } else {
    return NULL;
  }
}
void TestOneof::set_allocated_oneof_string_value(::std::string* oneof_string_value) {
  if (!has_oneof_string_value()) {
    oneof_value_.oneof_string_value_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  }
  clear_oneof_value();
  if (oneof_string_value != NULL) {
    set_has_oneof_string_value();
    oneof_value_.oneof_string_value_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
        oneof_string_value);
  }
  // @@protoc_insertion_point(field_set_allocated:proto3.TestOneof.oneof_string_value)
}

// optional bytes oneof_bytes_value = 3;
bool TestOneof::has_oneof_bytes_value() const {
  return oneof_value_case() == kOneofBytesValue;
}
void TestOneof::set_has_oneof_bytes_value() {
  _oneof_case_[0] = kOneofBytesValue;
}
void TestOneof::clear_oneof_bytes_value() {
  if (has_oneof_bytes_value()) {
    oneof_value_.oneof_bytes_value_.DestroyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
    clear_has_oneof_value();
  }
}
const ::std::string& TestOneof::oneof_bytes_value() const {
  // @@protoc_insertion_point(field_get:proto3.TestOneof.oneof_bytes_value)
  if (has_oneof_bytes_value()) {
    return oneof_value_.oneof_bytes_value_.GetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  }
  return *&::google::protobuf::internal::GetEmptyStringAlreadyInited();
}
void TestOneof::set_oneof_bytes_value(const ::std::string& value) {
  // @@protoc_insertion_point(field_set:proto3.TestOneof.oneof_bytes_value)
  if (!has_oneof_bytes_value()) {
    clear_oneof_value();
    set_has_oneof_bytes_value();
    oneof_value_.oneof_bytes_value_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  }
  oneof_value_.oneof_bytes_value_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:proto3.TestOneof.oneof_bytes_value)
}
void TestOneof::set_oneof_bytes_value(const char* value) {
  if (!has_oneof_bytes_value()) {
    clear_oneof_value();
    set_has_oneof_bytes_value();
    oneof_value_.oneof_bytes_value_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  }
  oneof_value_.oneof_bytes_value_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(value));
  // @@protoc_insertion_point(field_set_char:proto3.TestOneof.oneof_bytes_value)
}
void TestOneof::set_oneof_bytes_value(const void* value, size_t size) {
  if (!has_oneof_bytes_value()) {
    clear_oneof_value();
    set_has_oneof_bytes_value();
    oneof_value_.oneof_bytes_value_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  }
  oneof_value_.oneof_bytes_value_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(
      reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:proto3.TestOneof.oneof_bytes_value)
}
::std::string* TestOneof::mutable_oneof_bytes_value() {
  if (!has_oneof_bytes_value()) {
    clear_oneof_value();
    set_has_oneof_bytes_value();
    oneof_value_.oneof_bytes_value_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  }
  // @@protoc_insertion_point(field_mutable:proto3.TestOneof.oneof_bytes_value)
  return oneof_value_.oneof_bytes_value_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
::std::string* TestOneof::release_oneof_bytes_value() {
  // @@protoc_insertion_point(field_release:proto3.TestOneof.oneof_bytes_value)
  if (has_oneof_bytes_value()) {
    clear_has_oneof_value();
    return oneof_value_.oneof_bytes_value_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  } else {
    return NULL;
  }
}
void TestOneof::set_allocated_oneof_bytes_value(::std::string* oneof_bytes_value) {
  if (!has_oneof_bytes_value()) {
    oneof_value_.oneof_bytes_value_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  }
  clear_oneof_value();
  if (oneof_bytes_value != NULL) {
    set_has_oneof_bytes_value();
    oneof_value_.oneof_bytes_value_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
        oneof_bytes_value);
  }
  // @@protoc_insertion_point(field_set_allocated:proto3.TestOneof.oneof_bytes_value)
}

// optional .proto3.EnumType oneof_enum_value = 4;
bool TestOneof::has_oneof_enum_value() const {
  return oneof_value_case() == kOneofEnumValue;
}
void TestOneof::set_has_oneof_enum_value() {
  _oneof_case_[0] = kOneofEnumValue;
}
void TestOneof::clear_oneof_enum_value() {
  if (has_oneof_enum_value()) {
    oneof_value_.oneof_enum_value_ = 0;
    clear_has_oneof_value();
  }
}
::proto3::EnumType TestOneof::oneof_enum_value() const {
  // @@protoc_insertion_point(field_get:proto3.TestOneof.oneof_enum_value)
  if (has_oneof_enum_value()) {
    return static_cast< ::proto3::EnumType >(oneof_value_.oneof_enum_value_);
  }
  return static_cast< ::proto3::EnumType >(0);
}
void TestOneof::set_oneof_enum_value(::proto3::EnumType value) {
  if (!has_oneof_enum_value()) {
    clear_oneof_value();
    set_has_oneof_enum_value();
  }
  oneof_value_.oneof_enum_value_ = value;
  // @@protoc_insertion_point(field_set:proto3.TestOneof.oneof_enum_value)
}

// optional .proto3.MessageType oneof_message_value = 5;
bool TestOneof::has_oneof_message_value() const {
  return oneof_value_case() == kOneofMessageValue;
}
void TestOneof::set_has_oneof_message_value() {
  _oneof_case_[0] = kOneofMessageValue;
}
void TestOneof::clear_oneof_message_value() {
  if (has_oneof_message_value()) {
    delete oneof_value_.oneof_message_value_;
    clear_has_oneof_value();
  }
}
 const ::proto3::MessageType& TestOneof::oneof_message_value() const {
  // @@protoc_insertion_point(field_get:proto3.TestOneof.oneof_message_value)
  return has_oneof_message_value()
      ? *oneof_value_.oneof_message_value_
      : ::proto3::MessageType::default_instance();
}
::proto3::MessageType* TestOneof::mutable_oneof_message_value() {
  if (!has_oneof_message_value()) {
    clear_oneof_value();
    set_has_oneof_message_value();
    oneof_value_.oneof_message_value_ = new ::proto3::MessageType;
  }
  // @@protoc_insertion_point(field_mutable:proto3.TestOneof.oneof_message_value)
  return oneof_value_.oneof_message_value_;
}
::proto3::MessageType* TestOneof::release_oneof_message_value() {
  // @@protoc_insertion_point(field_release:proto3.TestOneof.oneof_message_value)
  if (has_oneof_message_value()) {
    clear_has_oneof_value();
    ::proto3::MessageType* temp = oneof_value_.oneof_message_value_;
    oneof_value_.oneof_message_value_ = NULL;
    return temp;
  } else {
    return NULL;
  }
}
void TestOneof::set_allocated_oneof_message_value(::proto3::MessageType* oneof_message_value) {
  clear_oneof_value();
  if (oneof_message_value) {
    set_has_oneof_message_value();
    oneof_value_.oneof_message_value_ = oneof_message_value;
  }
  // @@protoc_insertion_point(field_set_allocated:proto3.TestOneof.oneof_message_value)
}

bool TestOneof::has_oneof_value() const {
  return oneof_value_case() != ONEOF_VALUE_NOT_SET;
}
void TestOneof::clear_has_oneof_value() {
  _oneof_case_[0] = ONEOF_VALUE_NOT_SET;
}
TestOneof::OneofValueCase TestOneof::oneof_value_case() const {
  return TestOneof::OneofValueCase(_oneof_case_[0]);
}
inline const TestOneof* TestOneof::internal_default_instance() {
  return &TestOneof_default_instance_.get();
}
#endif  // PROTOBUF_INLINE_NOT_IN_HEADERS

// ===================================================================

#if !defined(_MSC_VER) || _MSC_VER >= 1900
const int TestMap::kBoolMapFieldNumber;
const int TestMap::kInt32MapFieldNumber;
const int TestMap::kInt64MapFieldNumber;
const int TestMap::kUint32MapFieldNumber;
const int TestMap::kUint64MapFieldNumber;
const int TestMap::kStringMapFieldNumber;
#endif  // !defined(_MSC_VER) || _MSC_VER >= 1900

TestMap::TestMap()
  : ::google::protobuf::Message(), _internal_metadata_(NULL) {
  if (this != internal_default_instance()) protobuf_InitDefaults_google_2fprotobuf_2futil_2fjson_5fformat_5fproto3_2eproto();
  SharedCtor();
  // @@protoc_insertion_point(constructor:proto3.TestMap)
}

void TestMap::InitAsDefaultInstance() {
}

TestMap::TestMap(const TestMap& from)
  : ::google::protobuf::Message(),
    _internal_metadata_(NULL) {
  SharedCtor();
  UnsafeMergeFrom(from);
  // @@protoc_insertion_point(copy_constructor:proto3.TestMap)
}

void TestMap::SharedCtor() {
  bool_map_.SetAssignDescriptorCallback(
      protobuf_AssignDescriptorsOnce);
  bool_map_.SetEntryDescriptor(
      &::proto3::TestMap_BoolMapEntry_descriptor_);
  int32_map_.SetAssignDescriptorCallback(
      protobuf_AssignDescriptorsOnce);
  int32_map_.SetEntryDescriptor(
      &::proto3::TestMap_Int32MapEntry_descriptor_);
  int64_map_.SetAssignDescriptorCallback(
      protobuf_AssignDescriptorsOnce);
  int64_map_.SetEntryDescriptor(
      &::proto3::TestMap_Int64MapEntry_descriptor_);
  uint32_map_.SetAssignDescriptorCallback(
      protobuf_AssignDescriptorsOnce);
  uint32_map_.SetEntryDescriptor(
      &::proto3::TestMap_Uint32MapEntry_descriptor_);
  uint64_map_.SetAssignDescriptorCallback(
      protobuf_AssignDescriptorsOnce);
  uint64_map_.SetEntryDescriptor(
      &::proto3::TestMap_Uint64MapEntry_descriptor_);
  string_map_.SetAssignDescriptorCallback(
      protobuf_AssignDescriptorsOnce);
  string_map_.SetEntryDescriptor(
      &::proto3::TestMap_StringMapEntry_descriptor_);
  _cached_size_ = 0;
}

TestMap::~TestMap() {
  // @@protoc_insertion_point(destructor:proto3.TestMap)
  SharedDtor();
}

void TestMap::SharedDtor() {
}

void TestMap::SetCachedSize(int size) const {
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
}
const ::google::protobuf::Descriptor* TestMap::descriptor() {
  protobuf_AssignDescriptorsOnce();
  return TestMap_descriptor_;
}

const TestMap& TestMap::default_instance() {
  protobuf_InitDefaults_google_2fprotobuf_2futil_2fjson_5fformat_5fproto3_2eproto();
  return *internal_default_instance();
}

::google::protobuf::internal::ExplicitlyConstructed<TestMap> TestMap_default_instance_;

TestMap* TestMap::New(::google::protobuf::Arena* arena) const {
  TestMap* n = new TestMap;
  if (arena != NULL) {
    arena->Own(n);
  }
  return n;
}

void TestMap::Clear() {
// @@protoc_insertion_point(message_clear_start:proto3.TestMap)
  bool_map_.Clear();
  int32_map_.Clear();
  int64_map_.Clear();
  uint32_map_.Clear();
  uint64_map_.Clear();
  string_map_.Clear();
}

bool TestMap::MergePartialFromCodedStream(
    ::google::protobuf::io::CodedInputStream* input) {
#define DO_(EXPRESSION) if (!GOOGLE_PREDICT_TRUE(EXPRESSION)) goto failure
  ::google::protobuf::uint32 tag;
  // @@protoc_insertion_point(parse_start:proto3.TestMap)
  for (;;) {
    ::std::pair< ::google::protobuf::uint32, bool> p = input->ReadTagWithCutoff(127);
    tag = p.first;
    if (!p.second) goto handle_unusual;
    switch (::google::protobuf::internal::WireFormatLite::GetTagFieldNumber(tag)) {
      // map<bool, int32> bool_map = 1;
      case 1: {
        if (tag == 10) {
          DO_(input->IncrementRecursionDepth());
         parse_loop_bool_map:
          TestMap_BoolMapEntry::Parser< ::google::protobuf::internal::MapField<
              bool, ::google::protobuf::int32,
              ::google::protobuf::internal::WireFormatLite::TYPE_BOOL,
              ::google::protobuf::internal::WireFormatLite::TYPE_INT32,
              0 >,
            ::google::protobuf::Map< bool, ::google::protobuf::int32 > > parser(&bool_map_);
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessageNoVirtual(
              input, &parser));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(10)) goto parse_loop_bool_map;
        if (input->ExpectTag(18)) goto parse_loop_int32_map;
        input->UnsafeDecrementRecursionDepth();
        break;
      }

      // map<int32, int32> int32_map = 2;
      case 2: {
        if (tag == 18) {
          DO_(input->IncrementRecursionDepth());
         parse_loop_int32_map:
          TestMap_Int32MapEntry::Parser< ::google::protobuf::internal::MapField<
              ::google::protobuf::int32, ::google::protobuf::int32,
              ::google::protobuf::internal::WireFormatLite::TYPE_INT32,
              ::google::protobuf::internal::WireFormatLite::TYPE_INT32,
              0 >,
            ::google::protobuf::Map< ::google::protobuf::int32, ::google::protobuf::int32 > > parser(&int32_map_);
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessageNoVirtual(
              input, &parser));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(18)) goto parse_loop_int32_map;
        if (input->ExpectTag(26)) goto parse_loop_int64_map;
        input->UnsafeDecrementRecursionDepth();
        break;
      }

      // map<int64, int32> int64_map = 3;
      case 3: {
        if (tag == 26) {
          DO_(input->IncrementRecursionDepth());
         parse_loop_int64_map:
          TestMap_Int64MapEntry::Parser< ::google::protobuf::internal::MapField<
              ::google::protobuf::int64, ::google::protobuf::int32,
              ::google::protobuf::internal::WireFormatLite::TYPE_INT64,
              ::google::protobuf::internal::WireFormatLite::TYPE_INT32,
              0 >,
            ::google::protobuf::Map< ::google::protobuf::int64, ::google::protobuf::int32 > > parser(&int64_map_);
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessageNoVirtual(
              input, &parser));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(26)) goto parse_loop_int64_map;
        if (input->ExpectTag(34)) goto parse_loop_uint32_map;
        input->UnsafeDecrementRecursionDepth();
        break;
      }

      // map<uint32, int32> uint32_map = 4;
      case 4: {
        if (tag == 34) {
          DO_(input->IncrementRecursionDepth());
         parse_loop_uint32_map:
          TestMap_Uint32MapEntry::Parser< ::google::protobuf::internal::MapField<
              ::google::protobuf::uint32, ::google::protobuf::int32,
              ::google::protobuf::internal::WireFormatLite::TYPE_UINT32,
              ::google::protobuf::internal::WireFormatLite::TYPE_INT32,
              0 >,
            ::google::protobuf::Map< ::google::protobuf::uint32, ::google::protobuf::int32 > > parser(&uint32_map_);
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessageNoVirtual(
              input, &parser));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(34)) goto parse_loop_uint32_map;
        if (input->ExpectTag(42)) goto parse_loop_uint64_map;
        input->UnsafeDecrementRecursionDepth();
        break;
      }

      // map<uint64, int32> uint64_map = 5;
      case 5: {
        if (tag == 42) {
          DO_(input->IncrementRecursionDepth());
         parse_loop_uint64_map:
          TestMap_Uint64MapEntry::Parser< ::google::protobuf::internal::MapField<
              ::google::protobuf::uint64, ::google::protobuf::int32,
              ::google::protobuf::internal::WireFormatLite::TYPE_UINT64,
              ::google::protobuf::internal::WireFormatLite::TYPE_INT32,
              0 >,
            ::google::protobuf::Map< ::google::protobuf::uint64, ::google::protobuf::int32 > > parser(&uint64_map_);
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessageNoVirtual(
              input, &parser));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(42)) goto parse_loop_uint64_map;
        if (input->ExpectTag(50)) goto parse_loop_string_map;
        input->UnsafeDecrementRecursionDepth();
        break;
      }

      // map<string, int32> string_map = 6;
      case 6: {
        if (tag == 50) {
          DO_(input->IncrementRecursionDepth());
         parse_loop_string_map:
          TestMap_StringMapEntry::Parser< ::google::protobuf::internal::MapField<
              ::std::string, ::google::protobuf::int32,
              ::google::protobuf::internal::WireFormatLite::TYPE_STRING,
              ::google::protobuf::internal::WireFormatLite::TYPE_INT32,
              0 >,
            ::google::protobuf::Map< ::std::string, ::google::protobuf::int32 > > parser(&string_map_);
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessageNoVirtual(
              input, &parser));
          DO_(::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
            parser.key().data(), parser.key().length(),
            ::google::protobuf::internal::WireFormatLite::PARSE,
            "proto3.TestMap.StringMapEntry.key"));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(50)) goto parse_loop_string_map;
        input->UnsafeDecrementRecursionDepth();
        if (input->ExpectAtEnd()) goto success;
        break;
      }

      default: {
      handle_unusual:
        if (tag == 0 ||
            ::google::protobuf::internal::WireFormatLite::GetTagWireType(tag) ==
            ::google::protobuf::internal::WireFormatLite::WIRETYPE_END_GROUP) {
          goto success;
        }
        DO_(::google::protobuf::internal::WireFormatLite::SkipField(input, tag));
        break;
      }
    }
  }
success:
  // @@protoc_insertion_point(parse_success:proto3.TestMap)
  return true;
failure:
  // @@protoc_insertion_point(parse_failure:proto3.TestMap)
  return false;
#undef DO_
}

void TestMap::SerializeWithCachedSizes(
    ::google::protobuf::io::CodedOutputStream* output) const {
  // @@protoc_insertion_point(serialize_start:proto3.TestMap)
  // map<bool, int32> bool_map = 1;
  if (!this->bool_map().empty()) {
    typedef ::google::protobuf::Map< bool, ::google::protobuf::int32 >::const_pointer
        ConstPtr;
    typedef ::google::protobuf::internal::SortItem< bool, ConstPtr > SortItem;
    typedef ::google::protobuf::internal::CompareByFirstField<SortItem> Less;

    if (output->IsSerializationDeterminstic() &&
        this->bool_map().size() > 1) {
      ::google::protobuf::scoped_array<SortItem> items(
          new SortItem[this->bool_map().size()]);
      typedef ::google::protobuf::Map< bool, ::google::protobuf::int32 >::size_type size_type;
      size_type n = 0;
      for (::google::protobuf::Map< bool, ::google::protobuf::int32 >::const_iterator
          it = this->bool_map().begin();
          it != this->bool_map().end(); ++it, ++n) {
        items[n] = SortItem(&*it);
      }
      ::std::sort(&items[0], &items[n], Less());
      ::google::protobuf::scoped_ptr<TestMap_BoolMapEntry> entry;
      for (size_type i = 0; i < n; i++) {
        entry.reset(bool_map_.NewEntryWrapper(
            items[i].second->first, items[i].second->second));
        ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
            1, *entry, output);
      }
    } else {
      ::google::protobuf::scoped_ptr<TestMap_BoolMapEntry> entry;
      for (::google::protobuf::Map< bool, ::google::protobuf::int32 >::const_iterator
          it = this->bool_map().begin();
          it != this->bool_map().end(); ++it) {
        entry.reset(bool_map_.NewEntryWrapper(
            it->first, it->second));
        ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
            1, *entry, output);
      }
    }
  }

  // map<int32, int32> int32_map = 2;
  if (!this->int32_map().empty()) {
    typedef ::google::protobuf::Map< ::google::protobuf::int32, ::google::protobuf::int32 >::const_pointer
        ConstPtr;
    typedef ::google::protobuf::internal::SortItem< ::google::protobuf::int32, ConstPtr > SortItem;
    typedef ::google::protobuf::internal::CompareByFirstField<SortItem> Less;

    if (output->IsSerializationDeterminstic() &&
        this->int32_map().size() > 1) {
      ::google::protobuf::scoped_array<SortItem> items(
          new SortItem[this->int32_map().size()]);
      typedef ::google::protobuf::Map< ::google::protobuf::int32, ::google::protobuf::int32 >::size_type size_type;
      size_type n = 0;
      for (::google::protobuf::Map< ::google::protobuf::int32, ::google::protobuf::int32 >::const_iterator
          it = this->int32_map().begin();
          it != this->int32_map().end(); ++it, ++n) {
        items[n] = SortItem(&*it);
      }
      ::std::sort(&items[0], &items[n], Less());
      ::google::protobuf::scoped_ptr<TestMap_Int32MapEntry> entry;
      for (size_type i = 0; i < n; i++) {
        entry.reset(int32_map_.NewEntryWrapper(
            items[i].second->first, items[i].second->second));
        ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
            2, *entry, output);
      }
    } else {
      ::google::protobuf::scoped_ptr<TestMap_Int32MapEntry> entry;
      for (::google::protobuf::Map< ::google::protobuf::int32, ::google::protobuf::int32 >::const_iterator
          it = this->int32_map().begin();
          it != this->int32_map().end(); ++it) {
        entry.reset(int32_map_.NewEntryWrapper(
            it->first, it->second));
        ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
            2, *entry, output);
      }
    }
  }

  // map<int64, int32> int64_map = 3;
  if (!this->int64_map().empty()) {
    typedef ::google::protobuf::Map< ::google::protobuf::int64, ::google::protobuf::int32 >::const_pointer
        ConstPtr;
    typedef ::google::protobuf::internal::SortItem< ::google::protobuf::int64, ConstPtr > SortItem;
    typedef ::google::protobuf::internal::CompareByFirstField<SortItem> Less;

    if (output->IsSerializationDeterminstic() &&
        this->int64_map().size() > 1) {
      ::google::protobuf::scoped_array<SortItem> items(
          new SortItem[this->int64_map().size()]);
      typedef ::google::protobuf::Map< ::google::protobuf::int64, ::google::protobuf::int32 >::size_type size_type;
      size_type n = 0;
      for (::google::protobuf::Map< ::google::protobuf::int64, ::google::protobuf::int32 >::const_iterator
          it = this->int64_map().begin();
          it != this->int64_map().end(); ++it, ++n) {
        items[n] = SortItem(&*it);
      }
      ::std::sort(&items[0], &items[n], Less());
      ::google::protobuf::scoped_ptr<TestMap_Int64MapEntry> entry;
      for (size_type i = 0; i < n; i++) {
        entry.reset(int64_map_.NewEntryWrapper(
            items[i].second->first, items[i].second->second));
        ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
            3, *entry, output);
      }
    } else {
      ::google::protobuf::scoped_ptr<TestMap_Int64MapEntry> entry;
      for (::google::protobuf::Map< ::google::protobuf::int64, ::google::protobuf::int32 >::const_iterator
          it = this->int64_map().begin();
          it != this->int64_map().end(); ++it) {
        entry.reset(int64_map_.NewEntryWrapper(
            it->first, it->second));
        ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
            3, *entry, output);
      }
    }
  }

  // map<uint32, int32> uint32_map = 4;
  if (!this->uint32_map().empty()) {
    typedef ::google::protobuf::Map< ::google::protobuf::uint32, ::google::protobuf::int32 >::const_pointer
        ConstPtr;
    typedef ::google::protobuf::internal::SortItem< ::google::protobuf::uint32, ConstPtr > SortItem;
    typedef ::google::protobuf::internal::CompareByFirstField<SortItem> Less;

    if (output->IsSerializationDeterminstic() &&
        this->uint32_map().size() > 1) {
      ::google::protobuf::scoped_array<SortItem> items(
          new SortItem[this->uint32_map().size()]);
      typedef ::google::protobuf::Map< ::google::protobuf::uint32, ::google::protobuf::int32 >::size_type size_type;
      size_type n = 0;
      for (::google::protobuf::Map< ::google::protobuf::uint32, ::google::protobuf::int32 >::const_iterator
          it = this->uint32_map().begin();
          it != this->uint32_map().end(); ++it, ++n) {
        items[n] = SortItem(&*it);
      }
      ::std::sort(&items[0], &items[n], Less());
      ::google::protobuf::scoped_ptr<TestMap_Uint32MapEntry> entry;
      for (size_type i = 0; i < n; i++) {
        entry.reset(uint32_map_.NewEntryWrapper(
            items[i].second->first, items[i].second->second));
        ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
            4, *entry, output);
      }
    } else {
      ::google::protobuf::scoped_ptr<TestMap_Uint32MapEntry> entry;
      for (::google::protobuf::Map< ::google::protobuf::uint32, ::google::protobuf::int32 >::const_iterator
          it = this->uint32_map().begin();
          it != this->uint32_map().end(); ++it) {
        entry.reset(uint32_map_.NewEntryWrapper(
            it->first, it->second));
        ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
            4, *entry, output);
      }
    }
  }

  // map<uint64, int32> uint64_map = 5;
  if (!this->uint64_map().empty()) {
    typedef ::google::protobuf::Map< ::google::protobuf::uint64, ::google::protobuf::int32 >::const_pointer
        ConstPtr;
    typedef ::google::protobuf::internal::SortItem< ::google::protobuf::uint64, ConstPtr > SortItem;
    typedef ::google::protobuf::internal::CompareByFirstField<SortItem> Less;

    if (output->IsSerializationDeterminstic() &&
        this->uint64_map().size() > 1) {
      ::google::protobuf::scoped_array<SortItem> items(
          new SortItem[this->uint64_map().size()]);
      typedef ::google::protobuf::Map< ::google::protobuf::uint64, ::google::protobuf::int32 >::size_type size_type;
      size_type n = 0;
      for (::google::protobuf::Map< ::google::protobuf::uint64, ::google::protobuf::int32 >::const_iterator
          it = this->uint64_map().begin();
          it != this->uint64_map().end(); ++it, ++n) {
        items[n] = SortItem(&*it);
      }
      ::std::sort(&items[0], &items[n], Less());
      ::google::protobuf::scoped_ptr<TestMap_Uint64MapEntry> entry;
      for (size_type i = 0; i < n; i++) {
        entry.reset(uint64_map_.NewEntryWrapper(
            items[i].second->first, items[i].second->second));
        ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
            5, *entry, output);
      }
    } else {
      ::google::protobuf::scoped_ptr<TestMap_Uint64MapEntry> entry;
      for (::google::protobuf::Map< ::google::protobuf::uint64, ::google::protobuf::int32 >::const_iterator
          it = this->uint64_map().begin();
          it != this->uint64_map().end(); ++it) {
        entry.reset(uint64_map_.NewEntryWrapper(
            it->first, it->second));
        ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
            5, *entry, output);
      }
    }
  }

  // map<string, int32> string_map = 6;
  if (!this->string_map().empty()) {
    typedef ::google::protobuf::Map< ::std::string, ::google::protobuf::int32 >::const_pointer
        ConstPtr;
    typedef ConstPtr SortItem;
    typedef ::google::protobuf::internal::CompareByDerefFirst<SortItem> Less;
    struct Utf8Check {
      static void Check(ConstPtr p) {
        ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
          p->first.data(), p->first.length(),
          ::google::protobuf::internal::WireFormatLite::SERIALIZE,
          "proto3.TestMap.StringMapEntry.key");
      }
    };

    if (output->IsSerializationDeterminstic() &&
        this->string_map().size() > 1) {
      ::google::protobuf::scoped_array<SortItem> items(
          new SortItem[this->string_map().size()]);
      typedef ::google::protobuf::Map< ::std::string, ::google::protobuf::int32 >::size_type size_type;
      size_type n = 0;
      for (::google::protobuf::Map< ::std::string, ::google::protobuf::int32 >::const_iterator
          it = this->string_map().begin();
          it != this->string_map().end(); ++it, ++n) {
        items[n] = SortItem(&*it);
      }
      ::std::sort(&items[0], &items[n], Less());
      ::google::protobuf::scoped_ptr<TestMap_StringMapEntry> entry;
      for (size_type i = 0; i < n; i++) {
        entry.reset(string_map_.NewEntryWrapper(
            items[i]->first, items[i]->second));
        ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
            6, *entry, output);
        Utf8Check::Check(items[i]);
      }
    } else {
      ::google::protobuf::scoped_ptr<TestMap_StringMapEntry> entry;
      for (::google::protobuf::Map< ::std::string, ::google::protobuf::int32 >::const_iterator
          it = this->string_map().begin();
          it != this->string_map().end(); ++it) {
        entry.reset(string_map_.NewEntryWrapper(
            it->first, it->second));
        ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
            6, *entry, output);
        Utf8Check::Check(&*it);
      }
    }
  }

  // @@protoc_insertion_point(serialize_end:proto3.TestMap)
}

::google::protobuf::uint8* TestMap::InternalSerializeWithCachedSizesToArray(
    bool deterministic, ::google::protobuf::uint8* target) const {
  (void)deterministic; // Unused
  // @@protoc_insertion_point(serialize_to_array_start:proto3.TestMap)
  // map<bool, int32> bool_map = 1;
  if (!this->bool_map().empty()) {
    typedef ::google::protobuf::Map< bool, ::google::protobuf::int32 >::const_pointer
        ConstPtr;
    typedef ::google::protobuf::internal::SortItem< bool, ConstPtr > SortItem;
    typedef ::google::protobuf::internal::CompareByFirstField<SortItem> Less;

    if (deterministic &&
        this->bool_map().size() > 1) {
      ::google::protobuf::scoped_array<SortItem> items(
          new SortItem[this->bool_map().size()]);
      typedef ::google::protobuf::Map< bool, ::google::protobuf::int32 >::size_type size_type;
      size_type n = 0;
      for (::google::protobuf::Map< bool, ::google::protobuf::int32 >::const_iterator
          it = this->bool_map().begin();
          it != this->bool_map().end(); ++it, ++n) {
        items[n] = SortItem(&*it);
      }
      ::std::sort(&items[0], &items[n], Less());
      ::google::protobuf::scoped_ptr<TestMap_BoolMapEntry> entry;
      for (size_type i = 0; i < n; i++) {
        entry.reset(bool_map_.NewEntryWrapper(
            items[i].second->first, items[i].second->second));
        target = ::google::protobuf::internal::WireFormatLite::
                   InternalWriteMessageNoVirtualToArray(
                       1, *entry, deterministic, target);
;
      }
    } else {
      ::google::protobuf::scoped_ptr<TestMap_BoolMapEntry> entry;
      for (::google::protobuf::Map< bool, ::google::protobuf::int32 >::const_iterator
          it = this->bool_map().begin();
          it != this->bool_map().end(); ++it) {
        entry.reset(bool_map_.NewEntryWrapper(
            it->first, it->second));
        target = ::google::protobuf::internal::WireFormatLite::
                   InternalWriteMessageNoVirtualToArray(
                       1, *entry, deterministic, target);
;
      }
    }
  }

  // map<int32, int32> int32_map = 2;
  if (!this->int32_map().empty()) {
    typedef ::google::protobuf::Map< ::google::protobuf::int32, ::google::protobuf::int32 >::const_pointer
        ConstPtr;
    typedef ::google::protobuf::internal::SortItem< ::google::protobuf::int32, ConstPtr > SortItem;
    typedef ::google::protobuf::internal::CompareByFirstField<SortItem> Less;

    if (deterministic &&
        this->int32_map().size() > 1) {
      ::google::protobuf::scoped_array<SortItem> items(
          new SortItem[this->int32_map().size()]);
      typedef ::google::protobuf::Map< ::google::protobuf::int32, ::google::protobuf::int32 >::size_type size_type;
      size_type n = 0;
      for (::google::protobuf::Map< ::google::protobuf::int32, ::google::protobuf::int32 >::const_iterator
          it = this->int32_map().begin();
          it != this->int32_map().end(); ++it, ++n) {
        items[n] = SortItem(&*it);
      }
      ::std::sort(&items[0], &items[n], Less());
      ::google::protobuf::scoped_ptr<TestMap_Int32MapEntry> entry;
      for (size_type i = 0; i < n; i++) {
        entry.reset(int32_map_.NewEntryWrapper(
            items[i].second->first, items[i].second->second));
        target = ::google::protobuf::internal::WireFormatLite::
                   InternalWriteMessageNoVirtualToArray(
                       2, *entry, deterministic, target);
;
      }
    } else {
      ::google::protobuf::scoped_ptr<TestMap_Int32MapEntry> entry;
      for (::google::protobuf::Map< ::google::protobuf::int32, ::google::protobuf::int32 >::const_iterator
          it = this->int32_map().begin();
          it != this->int32_map().end(); ++it) {
        entry.reset(int32_map_.NewEntryWrapper(
            it->first, it->second));
        target = ::google::protobuf::internal::WireFormatLite::
                   InternalWriteMessageNoVirtualToArray(
                       2, *entry, deterministic, target);
;
      }
    }
  }

  // map<int64, int32> int64_map = 3;
  if (!this->int64_map().empty()) {
    typedef ::google::protobuf::Map< ::google::protobuf::int64, ::google::protobuf::int32 >::const_pointer
        ConstPtr;
    typedef ::google::protobuf::internal::SortItem< ::google::protobuf::int64, ConstPtr > SortItem;
    typedef ::google::protobuf::internal::CompareByFirstField<SortItem> Less;

    if (deterministic &&
        this->int64_map().size() > 1) {
      ::google::protobuf::scoped_array<SortItem> items(
          new SortItem[this->int64_map().size()]);
      typedef ::google::protobuf::Map< ::google::protobuf::int64, ::google::protobuf::int32 >::size_type size_type;
      size_type n = 0;
      for (::google::protobuf::Map< ::google::protobuf::int64, ::google::protobuf::int32 >::const_iterator
          it = this->int64_map().begin();
          it != this->int64_map().end(); ++it, ++n) {
        items[n] = SortItem(&*it);
      }
      ::std::sort(&items[0], &items[n], Less());
      ::google::protobuf::scoped_ptr<TestMap_Int64MapEntry> entry;
      for (size_type i = 0; i < n; i++) {
        entry.reset(int64_map_.NewEntryWrapper(
            items[i].second->first, items[i].second->second));
        target = ::google::protobuf::internal::WireFormatLite::
                   InternalWriteMessageNoVirtualToArray(
                       3, *entry, deterministic, target);
;
      }
    } else {
      ::google::protobuf::scoped_ptr<TestMap_Int64MapEntry> entry;
      for (::google::protobuf::Map< ::google::protobuf::int64, ::google::protobuf::int32 >::const_iterator
          it = this->int64_map().begin();
          it != this->int64_map().end(); ++it) {
        entry.reset(int64_map_.NewEntryWrapper(
            it->first, it->second));
        target = ::google::protobuf::internal::WireFormatLite::
                   InternalWriteMessageNoVirtualToArray(
                       3, *entry, deterministic, target);
;
      }
    }
  }

  // map<uint32, int32> uint32_map = 4;
  if (!this->uint32_map().empty()) {
    typedef ::google::protobuf::Map< ::google::protobuf::uint32, ::google::protobuf::int32 >::const_pointer
        ConstPtr;
    typedef ::google::protobuf::internal::SortItem< ::google::protobuf::uint32, ConstPtr > SortItem;
    typedef ::google::protobuf::internal::CompareByFirstField<SortItem> Less;

    if (deterministic &&
        this->uint32_map().size() > 1) {
      ::google::protobuf::scoped_array<SortItem> items(
          new SortItem[this->uint32_map().size()]);
      typedef ::google::protobuf::Map< ::google::protobuf::uint32, ::google::protobuf::int32 >::size_type size_type;
      size_type n = 0;
      for (::google::protobuf::Map< ::google::protobuf::uint32, ::google::protobuf::int32 >::const_iterator
          it = this->uint32_map().begin();
          it != this->uint32_map().end(); ++it, ++n) {
        items[n] = SortItem(&*it);
      }
      ::std::sort(&items[0], &items[n], Less());
      ::google::protobuf::scoped_ptr<TestMap_Uint32MapEntry> entry;
      for (size_type i = 0; i < n; i++) {
        entry.reset(uint32_map_.NewEntryWrapper(
            items[i].second->first, items[i].second->second));
        target = ::google::protobuf::internal::WireFormatLite::
                   InternalWriteMessageNoVirtualToArray(
                       4, *entry, deterministic, target);
;
      }
    } else {
      ::google::protobuf::scoped_ptr<TestMap_Uint32MapEntry> entry;
      for (::google::protobuf::Map< ::google::protobuf::uint32, ::google::protobuf::int32 >::const_iterator
          it = this->uint32_map().begin();
          it != this->uint32_map().end(); ++it) {
        entry.reset(uint32_map_.NewEntryWrapper(
            it->first, it->second));
        target = ::google::protobuf::internal::WireFormatLite::
                   InternalWriteMessageNoVirtualToArray(
                       4, *entry, deterministic, target);
;
      }
    }
  }

  // map<uint64, int32> uint64_map = 5;
  if (!this->uint64_map().empty()) {
    typedef ::google::protobuf::Map< ::google::protobuf::uint64, ::google::protobuf::int32 >::const_pointer
        ConstPtr;
    typedef ::google::protobuf::internal::SortItem< ::google::protobuf::uint64, ConstPtr > SortItem;
    typedef ::google::protobuf::internal::CompareByFirstField<SortItem> Less;

    if (deterministic &&
        this->uint64_map().size() > 1) {
      ::google::protobuf::scoped_array<SortItem> items(
          new SortItem[this->uint64_map().size()]);
      typedef ::google::protobuf::Map< ::google::protobuf::uint64, ::google::protobuf::int32 >::size_type size_type;
      size_type n = 0;
      for (::google::protobuf::Map< ::google::protobuf::uint64, ::google::protobuf::int32 >::const_iterator
          it = this->uint64_map().begin();
          it != this->uint64_map().end(); ++it, ++n) {
        items[n] = SortItem(&*it);
      }
      ::std::sort(&items[0], &items[n], Less());
      ::google::protobuf::scoped_ptr<TestMap_Uint64MapEntry> entry;
      for (size_type i = 0; i < n; i++) {
        entry.reset(uint64_map_.NewEntryWrapper(
            items[i].second->first, items[i].second->second));
        target = ::google::protobuf::internal::WireFormatLite::
                   InternalWriteMessageNoVirtualToArray(
                       5, *entry, deterministic, target);
;
      }
    } else {
      ::google::protobuf::scoped_ptr<TestMap_Uint64MapEntry> entry;
      for (::google::protobuf::Map< ::google::protobuf::uint64, ::google::protobuf::int32 >::const_iterator
          it = this->uint64_map().begin();
          it != this->uint64_map().end(); ++it) {
        entry.reset(uint64_map_.NewEntryWrapper(
            it->first, it->second));
        target = ::google::protobuf::internal::WireFormatLite::
                   InternalWriteMessageNoVirtualToArray(
                       5, *entry, deterministic, target);
;
      }
    }
  }

  // map<string, int32> string_map = 6;
  if (!this->string_map().empty()) {
    typedef ::google::protobuf::Map< ::std::string, ::google::protobuf::int32 >::const_pointer
        ConstPtr;
    typedef ConstPtr SortItem;
    typedef ::google::protobuf::internal::CompareByDerefFirst<SortItem> Less;
    struct Utf8Check {
      static void Check(ConstPtr p) {
        ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
          p->first.data(), p->first.length(),
          ::google::protobuf::internal::WireFormatLite::SERIALIZE,
          "proto3.TestMap.StringMapEntry.key");
      }
    };

    if (deterministic &&
        this->string_map().size() > 1) {
      ::google::protobuf::scoped_array<SortItem> items(
          new SortItem[this->string_map().size()]);
      typedef ::google::protobuf::Map< ::std::string, ::google::protobuf::int32 >::size_type size_type;
      size_type n = 0;
      for (::google::protobuf::Map< ::std::string, ::google::protobuf::int32 >::const_iterator
          it = this->string_map().begin();
          it != this->string_map().end(); ++it, ++n) {
        items[n] = SortItem(&*it);
      }
      ::std::sort(&items[0], &items[n], Less());
      ::google::protobuf::scoped_ptr<TestMap_StringMapEntry> entry;
      for (size_type i = 0; i < n; i++) {
        entry.reset(string_map_.NewEntryWrapper(
            items[i]->first, items[i]->second));
        target = ::google::protobuf::internal::WireFormatLite::
                   InternalWriteMessageNoVirtualToArray(
                       6, *entry, deterministic, target);
;
        Utf8Check::Check(items[i]);
      }
    } else {
      ::google::protobuf::scoped_ptr<TestMap_StringMapEntry> entry;
      for (::google::protobuf::Map< ::std::string, ::google::protobuf::int32 >::const_iterator
          it = this->string_map().begin();
          it != this->string_map().end(); ++it) {
        entry.reset(string_map_.NewEntryWrapper(
            it->first, it->second));
        target = ::google::protobuf::internal::WireFormatLite::
                   InternalWriteMessageNoVirtualToArray(
                       6, *entry, deterministic, target);
;
        Utf8Check::Check(&*it);
      }
    }
  }

  // @@protoc_insertion_point(serialize_to_array_end:proto3.TestMap)
  return target;
}

size_t TestMap::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:proto3.TestMap)
  size_t total_size = 0;

  // map<bool, int32> bool_map = 1;
  total_size += 1 *
      ::google::protobuf::internal::FromIntSize(this->bool_map_size());
  {
    ::google::protobuf::scoped_ptr<TestMap_BoolMapEntry> entry;
    for (::google::protobuf::Map< bool, ::google::protobuf::int32 >::const_iterator
        it = this->bool_map().begin();
        it != this->bool_map().end(); ++it) {
      entry.reset(bool_map_.NewEntryWrapper(it->first, it->second));
      total_size += ::google::protobuf::internal::WireFormatLite::
          MessageSizeNoVirtual(*entry);
    }
  }

  // map<int32, int32> int32_map = 2;
  total_size += 1 *
      ::google::protobuf::internal::FromIntSize(this->int32_map_size());
  {
    ::google::protobuf::scoped_ptr<TestMap_Int32MapEntry> entry;
    for (::google::protobuf::Map< ::google::protobuf::int32, ::google::protobuf::int32 >::const_iterator
        it = this->int32_map().begin();
        it != this->int32_map().end(); ++it) {
      entry.reset(int32_map_.NewEntryWrapper(it->first, it->second));
      total_size += ::google::protobuf::internal::WireFormatLite::
          MessageSizeNoVirtual(*entry);
    }
  }

  // map<int64, int32> int64_map = 3;
  total_size += 1 *
      ::google::protobuf::internal::FromIntSize(this->int64_map_size());
  {
    ::google::protobuf::scoped_ptr<TestMap_Int64MapEntry> entry;
    for (::google::protobuf::Map< ::google::protobuf::int64, ::google::protobuf::int32 >::const_iterator
        it = this->int64_map().begin();
        it != this->int64_map().end(); ++it) {
      entry.reset(int64_map_.NewEntryWrapper(it->first, it->second));
      total_size += ::google::protobuf::internal::WireFormatLite::
          MessageSizeNoVirtual(*entry);
    }
  }

  // map<uint32, int32> uint32_map = 4;
  total_size += 1 *
      ::google::protobuf::internal::FromIntSize(this->uint32_map_size());
  {
    ::google::protobuf::scoped_ptr<TestMap_Uint32MapEntry> entry;
    for (::google::protobuf::Map< ::google::protobuf::uint32, ::google::protobuf::int32 >::const_iterator
        it = this->uint32_map().begin();
        it != this->uint32_map().end(); ++it) {
      entry.reset(uint32_map_.NewEntryWrapper(it->first, it->second));
      total_size += ::google::protobuf::internal::WireFormatLite::
          MessageSizeNoVirtual(*entry);
    }
  }

  // map<uint64, int32> uint64_map = 5;
  total_size += 1 *
      ::google::protobuf::internal::FromIntSize(this->uint64_map_size());
  {
    ::google::protobuf::scoped_ptr<TestMap_Uint64MapEntry> entry;
    for (::google::protobuf::Map< ::google::protobuf::uint64, ::google::protobuf::int32 >::const_iterator
        it = this->uint64_map().begin();
        it != this->uint64_map().end(); ++it) {
      entry.reset(uint64_map_.NewEntryWrapper(it->first, it->second));
      total_size += ::google::protobuf::internal::WireFormatLite::
          MessageSizeNoVirtual(*entry);
    }
  }

  // map<string, int32> string_map = 6;
  total_size += 1 *
      ::google::protobuf::internal::FromIntSize(this->string_map_size());
  {
    ::google::protobuf::scoped_ptr<TestMap_StringMapEntry> entry;
    for (::google::protobuf::Map< ::std::string, ::google::protobuf::int32 >::const_iterator
        it = this->string_map().begin();
        it != this->string_map().end(); ++it) {
      entry.reset(string_map_.NewEntryWrapper(it->first, it->second));
      total_size += ::google::protobuf::internal::WireFormatLite::
          MessageSizeNoVirtual(*entry);
    }
  }

  int cached_size = ::google::protobuf::internal::ToCachedSize(total_size);
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = cached_size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
  return total_size;
}

void TestMap::MergeFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_merge_from_start:proto3.TestMap)
  if (GOOGLE_PREDICT_FALSE(&from == this)) MergeFromFail(__LINE__);
  const TestMap* source =
      ::google::protobuf::internal::DynamicCastToGenerated<const TestMap>(
          &from);
  if (source == NULL) {
  // @@protoc_insertion_point(generalized_merge_from_cast_fail:proto3.TestMap)
    ::google::protobuf::internal::ReflectionOps::Merge(from, this);
  } else {
  // @@protoc_insertion_point(generalized_merge_from_cast_success:proto3.TestMap)
    UnsafeMergeFrom(*source);
  }
}

void TestMap::MergeFrom(const TestMap& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:proto3.TestMap)
  if (GOOGLE_PREDICT_TRUE(&from != this)) {
    UnsafeMergeFrom(from);
  } else {
    MergeFromFail(__LINE__);
  }
}

void TestMap::UnsafeMergeFrom(const TestMap& from) {
  GOOGLE_DCHECK(&from != this);
  bool_map_.MergeFrom(from.bool_map_);
  int32_map_.MergeFrom(from.int32_map_);
  int64_map_.MergeFrom(from.int64_map_);
  uint32_map_.MergeFrom(from.uint32_map_);
  uint64_map_.MergeFrom(from.uint64_map_);
  string_map_.MergeFrom(from.string_map_);
}

void TestMap::CopyFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_copy_from_start:proto3.TestMap)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

void TestMap::CopyFrom(const TestMap& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:proto3.TestMap)
  if (&from == this) return;
  Clear();
  UnsafeMergeFrom(from);
}

bool TestMap::IsInitialized() const {

  return true;
}

void TestMap::Swap(TestMap* other) {
  if (other == this) return;
  InternalSwap(other);
}
void TestMap::InternalSwap(TestMap* other) {
  bool_map_.Swap(&other->bool_map_);
  int32_map_.Swap(&other->int32_map_);
  int64_map_.Swap(&other->int64_map_);
  uint32_map_.Swap(&other->uint32_map_);
  uint64_map_.Swap(&other->uint64_map_);
  string_map_.Swap(&other->string_map_);
  _internal_metadata_.Swap(&other->_internal_metadata_);
  std::swap(_cached_size_, other->_cached_size_);
}

::google::protobuf::Metadata TestMap::GetMetadata() const {
  protobuf_AssignDescriptorsOnce();
  ::google::protobuf::Metadata metadata;
  metadata.descriptor = TestMap_descriptor_;
  metadata.reflection = TestMap_reflection_;
  return metadata;
}

#if PROTOBUF_INLINE_NOT_IN_HEADERS
// TestMap

// map<bool, int32> bool_map = 1;
int TestMap::bool_map_size() const {
  return bool_map_.size();
}
void TestMap::clear_bool_map() {
  bool_map_.Clear();
}
 const ::google::protobuf::Map< bool, ::google::protobuf::int32 >&
TestMap::bool_map() const {
  // @@protoc_insertion_point(field_map:proto3.TestMap.bool_map)
  return bool_map_.GetMap();
}
 ::google::protobuf::Map< bool, ::google::protobuf::int32 >*
TestMap::mutable_bool_map() {
  // @@protoc_insertion_point(field_mutable_map:proto3.TestMap.bool_map)
  return bool_map_.MutableMap();
}

// map<int32, int32> int32_map = 2;
int TestMap::int32_map_size() const {
  return int32_map_.size();
}
void TestMap::clear_int32_map() {
  int32_map_.Clear();
}
 const ::google::protobuf::Map< ::google::protobuf::int32, ::google::protobuf::int32 >&
TestMap::int32_map() const {
  // @@protoc_insertion_point(field_map:proto3.TestMap.int32_map)
  return int32_map_.GetMap();
}
 ::google::protobuf::Map< ::google::protobuf::int32, ::google::protobuf::int32 >*
TestMap::mutable_int32_map() {
  // @@protoc_insertion_point(field_mutable_map:proto3.TestMap.int32_map)
  return int32_map_.MutableMap();
}

// map<int64, int32> int64_map = 3;
int TestMap::int64_map_size() const {
  return int64_map_.size();
}
void TestMap::clear_int64_map() {
  int64_map_.Clear();
}
 const ::google::protobuf::Map< ::google::protobuf::int64, ::google::protobuf::int32 >&
TestMap::int64_map() const {
  // @@protoc_insertion_point(field_map:proto3.TestMap.int64_map)
  return int64_map_.GetMap();
}
 ::google::protobuf::Map< ::google::protobuf::int64, ::google::protobuf::int32 >*
TestMap::mutable_int64_map() {
  // @@protoc_insertion_point(field_mutable_map:proto3.TestMap.int64_map)
  return int64_map_.MutableMap();
}

// map<uint32, int32> uint32_map = 4;
int TestMap::uint32_map_size() const {
  return uint32_map_.size();
}
void TestMap::clear_uint32_map() {
  uint32_map_.Clear();
}
 const ::google::protobuf::Map< ::google::protobuf::uint32, ::google::protobuf::int32 >&
TestMap::uint32_map() const {
  // @@protoc_insertion_point(field_map:proto3.TestMap.uint32_map)
  return uint32_map_.GetMap();
}
 ::google::protobuf::Map< ::google::protobuf::uint32, ::google::protobuf::int32 >*
TestMap::mutable_uint32_map() {
  // @@protoc_insertion_point(field_mutable_map:proto3.TestMap.uint32_map)
  return uint32_map_.MutableMap();
}

// map<uint64, int32> uint64_map = 5;
int TestMap::uint64_map_size() const {
  return uint64_map_.size();
}
void TestMap::clear_uint64_map() {
  uint64_map_.Clear();
}
 const ::google::protobuf::Map< ::google::protobuf::uint64, ::google::protobuf::int32 >&
TestMap::uint64_map() const {
  // @@protoc_insertion_point(field_map:proto3.TestMap.uint64_map)
  return uint64_map_.GetMap();
}
 ::google::protobuf::Map< ::google::protobuf::uint64, ::google::protobuf::int32 >*
TestMap::mutable_uint64_map() {
  // @@protoc_insertion_point(field_mutable_map:proto3.TestMap.uint64_map)
  return uint64_map_.MutableMap();
}

// map<string, int32> string_map = 6;
int TestMap::string_map_size() const {
  return string_map_.size();
}
void TestMap::clear_string_map() {
  string_map_.Clear();
}
 const ::google::protobuf::Map< ::std::string, ::google::protobuf::int32 >&
TestMap::string_map() const {
  // @@protoc_insertion_point(field_map:proto3.TestMap.string_map)
  return string_map_.GetMap();
}
 ::google::protobuf::Map< ::std::string, ::google::protobuf::int32 >*
TestMap::mutable_string_map() {
  // @@protoc_insertion_point(field_mutable_map:proto3.TestMap.string_map)
  return string_map_.MutableMap();
}

inline const TestMap* TestMap::internal_default_instance() {
  return &TestMap_default_instance_.get();
}
#endif  // PROTOBUF_INLINE_NOT_IN_HEADERS

// ===================================================================

#if !defined(_MSC_VER) || _MSC_VER >= 1900
const int TestNestedMap::kBoolMapFieldNumber;
const int TestNestedMap::kInt32MapFieldNumber;
const int TestNestedMap::kInt64MapFieldNumber;
const int TestNestedMap::kUint32MapFieldNumber;
const int TestNestedMap::kUint64MapFieldNumber;
const int TestNestedMap::kStringMapFieldNumber;
const int TestNestedMap::kMapMapFieldNumber;
#endif  // !defined(_MSC_VER) || _MSC_VER >= 1900

TestNestedMap::TestNestedMap()
  : ::google::protobuf::Message(), _internal_metadata_(NULL) {
  if (this != internal_default_instance()) protobuf_InitDefaults_google_2fprotobuf_2futil_2fjson_5fformat_5fproto3_2eproto();
  SharedCtor();
  // @@protoc_insertion_point(constructor:proto3.TestNestedMap)
}

void TestNestedMap::InitAsDefaultInstance() {
}

TestNestedMap::TestNestedMap(const TestNestedMap& from)
  : ::google::protobuf::Message(),
    _internal_metadata_(NULL) {
  SharedCtor();
  UnsafeMergeFrom(from);
  // @@protoc_insertion_point(copy_constructor:proto3.TestNestedMap)
}

void TestNestedMap::SharedCtor() {
  bool_map_.SetAssignDescriptorCallback(
      protobuf_AssignDescriptorsOnce);
  bool_map_.SetEntryDescriptor(
      &::proto3::TestNestedMap_BoolMapEntry_descriptor_);
  int32_map_.SetAssignDescriptorCallback(
      protobuf_AssignDescriptorsOnce);
  int32_map_.SetEntryDescriptor(
      &::proto3::TestNestedMap_Int32MapEntry_descriptor_);
  int64_map_.SetAssignDescriptorCallback(
      protobuf_AssignDescriptorsOnce);
  int64_map_.SetEntryDescriptor(
      &::proto3::TestNestedMap_Int64MapEntry_descriptor_);
  uint32_map_.SetAssignDescriptorCallback(
      protobuf_AssignDescriptorsOnce);
  uint32_map_.SetEntryDescriptor(
      &::proto3::TestNestedMap_Uint32MapEntry_descriptor_);
  uint64_map_.SetAssignDescriptorCallback(
      protobuf_AssignDescriptorsOnce);
  uint64_map_.SetEntryDescriptor(
      &::proto3::TestNestedMap_Uint64MapEntry_descriptor_);
  string_map_.SetAssignDescriptorCallback(
      protobuf_AssignDescriptorsOnce);
  string_map_.SetEntryDescriptor(
      &::proto3::TestNestedMap_StringMapEntry_descriptor_);
  map_map_.SetAssignDescriptorCallback(
      protobuf_AssignDescriptorsOnce);
  map_map_.SetEntryDescriptor(
      &::proto3::TestNestedMap_MapMapEntry_descriptor_);
  _cached_size_ = 0;
}

TestNestedMap::~TestNestedMap() {
  // @@protoc_insertion_point(destructor:proto3.TestNestedMap)
  SharedDtor();
}

void TestNestedMap::SharedDtor() {
}

void TestNestedMap::SetCachedSize(int size) const {
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
}
const ::google::protobuf::Descriptor* TestNestedMap::descriptor() {
  protobuf_AssignDescriptorsOnce();
  return TestNestedMap_descriptor_;
}

const TestNestedMap& TestNestedMap::default_instance() {
  protobuf_InitDefaults_google_2fprotobuf_2futil_2fjson_5fformat_5fproto3_2eproto();
  return *internal_default_instance();
}

::google::protobuf::internal::ExplicitlyConstructed<TestNestedMap> TestNestedMap_default_instance_;

TestNestedMap* TestNestedMap::New(::google::protobuf::Arena* arena) const {
  TestNestedMap* n = new TestNestedMap;
  if (arena != NULL) {
    arena->Own(n);
  }
  return n;
}

void TestNestedMap::Clear() {
// @@protoc_insertion_point(message_clear_start:proto3.TestNestedMap)
  bool_map_.Clear();
  int32_map_.Clear();
  int64_map_.Clear();
  uint32_map_.Clear();
  uint64_map_.Clear();
  string_map_.Clear();
  map_map_.Clear();
}

bool TestNestedMap::MergePartialFromCodedStream(
    ::google::protobuf::io::CodedInputStream* input) {
#define DO_(EXPRESSION) if (!GOOGLE_PREDICT_TRUE(EXPRESSION)) goto failure
  ::google::protobuf::uint32 tag;
  // @@protoc_insertion_point(parse_start:proto3.TestNestedMap)
  for (;;) {
    ::std::pair< ::google::protobuf::uint32, bool> p = input->ReadTagWithCutoff(127);
    tag = p.first;
    if (!p.second) goto handle_unusual;
    switch (::google::protobuf::internal::WireFormatLite::GetTagFieldNumber(tag)) {
      // map<bool, int32> bool_map = 1;
      case 1: {
        if (tag == 10) {
          DO_(input->IncrementRecursionDepth());
         parse_loop_bool_map:
          TestNestedMap_BoolMapEntry::Parser< ::google::protobuf::internal::MapField<
              bool, ::google::protobuf::int32,
              ::google::protobuf::internal::WireFormatLite::TYPE_BOOL,
              ::google::protobuf::internal::WireFormatLite::TYPE_INT32,
              0 >,
            ::google::protobuf::Map< bool, ::google::protobuf::int32 > > parser(&bool_map_);
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessageNoVirtual(
              input, &parser));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(10)) goto parse_loop_bool_map;
        if (input->ExpectTag(18)) goto parse_loop_int32_map;
        input->UnsafeDecrementRecursionDepth();
        break;
      }

      // map<int32, int32> int32_map = 2;
      case 2: {
        if (tag == 18) {
          DO_(input->IncrementRecursionDepth());
         parse_loop_int32_map:
          TestNestedMap_Int32MapEntry::Parser< ::google::protobuf::internal::MapField<
              ::google::protobuf::int32, ::google::protobuf::int32,
              ::google::protobuf::internal::WireFormatLite::TYPE_INT32,
              ::google::protobuf::internal::WireFormatLite::TYPE_INT32,
              0 >,
            ::google::protobuf::Map< ::google::protobuf::int32, ::google::protobuf::int32 > > parser(&int32_map_);
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessageNoVirtual(
              input, &parser));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(18)) goto parse_loop_int32_map;
        if (input->ExpectTag(26)) goto parse_loop_int64_map;
        input->UnsafeDecrementRecursionDepth();
        break;
      }

      // map<int64, int32> int64_map = 3;
      case 3: {
        if (tag == 26) {
          DO_(input->IncrementRecursionDepth());
         parse_loop_int64_map:
          TestNestedMap_Int64MapEntry::Parser< ::google::protobuf::internal::MapField<
              ::google::protobuf::int64, ::google::protobuf::int32,
              ::google::protobuf::internal::WireFormatLite::TYPE_INT64,
              ::google::protobuf::internal::WireFormatLite::TYPE_INT32,
              0 >,
            ::google::protobuf::Map< ::google::protobuf::int64, ::google::protobuf::int32 > > parser(&int64_map_);
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessageNoVirtual(
              input, &parser));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(26)) goto parse_loop_int64_map;
        if (input->ExpectTag(34)) goto parse_loop_uint32_map;
        input->UnsafeDecrementRecursionDepth();
        break;
      }

      // map<uint32, int32> uint32_map = 4;
      case 4: {
        if (tag == 34) {
          DO_(input->IncrementRecursionDepth());
         parse_loop_uint32_map:
          TestNestedMap_Uint32MapEntry::Parser< ::google::protobuf::internal::MapField<
              ::google::protobuf::uint32, ::google::protobuf::int32,
              ::google::protobuf::internal::WireFormatLite::TYPE_UINT32,
              ::google::protobuf::internal::WireFormatLite::TYPE_INT32,
              0 >,
            ::google::protobuf::Map< ::google::protobuf::uint32, ::google::protobuf::int32 > > parser(&uint32_map_);
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessageNoVirtual(
              input, &parser));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(34)) goto parse_loop_uint32_map;
        if (input->ExpectTag(42)) goto parse_loop_uint64_map;
        input->UnsafeDecrementRecursionDepth();
        break;
      }

      // map<uint64, int32> uint64_map = 5;
      case 5: {
        if (tag == 42) {
          DO_(input->IncrementRecursionDepth());
         parse_loop_uint64_map:
          TestNestedMap_Uint64MapEntry::Parser< ::google::protobuf::internal::MapField<
              ::google::protobuf::uint64, ::google::protobuf::int32,
              ::google::protobuf::internal::WireFormatLite::TYPE_UINT64,
              ::google::protobuf::internal::WireFormatLite::TYPE_INT32,
              0 >,
            ::google::protobuf::Map< ::google::protobuf::uint64, ::google::protobuf::int32 > > parser(&uint64_map_);
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessageNoVirtual(
              input, &parser));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(42)) goto parse_loop_uint64_map;
        if (input->ExpectTag(50)) goto parse_loop_string_map;
        input->UnsafeDecrementRecursionDepth();
        break;
      }

      // map<string, int32> string_map = 6;
      case 6: {
        if (tag == 50) {
          DO_(input->IncrementRecursionDepth());
         parse_loop_string_map:
          TestNestedMap_StringMapEntry::Parser< ::google::protobuf::internal::MapField<
              ::std::string, ::google::protobuf::int32,
              ::google::protobuf::internal::WireFormatLite::TYPE_STRING,
              ::google::protobuf::internal::WireFormatLite::TYPE_INT32,
              0 >,
            ::google::protobuf::Map< ::std::string, ::google::protobuf::int32 > > parser(&string_map_);
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessageNoVirtual(
              input, &parser));
          DO_(::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
            parser.key().data(), parser.key().length(),
            ::google::protobuf::internal::WireFormatLite::PARSE,
            "proto3.TestNestedMap.StringMapEntry.key"));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(50)) goto parse_loop_string_map;
        if (input->ExpectTag(58)) goto parse_loop_map_map;
        input->UnsafeDecrementRecursionDepth();
        break;
      }

      // map<string, .proto3.TestNestedMap> map_map = 7;
      case 7: {
        if (tag == 58) {
          DO_(input->IncrementRecursionDepth());
         parse_loop_map_map:
          TestNestedMap_MapMapEntry::Parser< ::google::protobuf::internal::MapField<
              ::std::string, ::proto3::TestNestedMap,
              ::google::protobuf::internal::WireFormatLite::TYPE_STRING,
              ::google::protobuf::internal::WireFormatLite::TYPE_MESSAGE,
              0 >,
            ::google::protobuf::Map< ::std::string, ::proto3::TestNestedMap > > parser(&map_map_);
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessageNoVirtual(
              input, &parser));
          DO_(::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
            parser.key().data(), parser.key().length(),
            ::google::protobuf::internal::WireFormatLite::PARSE,
            "proto3.TestNestedMap.MapMapEntry.key"));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(58)) goto parse_loop_map_map;
        input->UnsafeDecrementRecursionDepth();
        if (input->ExpectAtEnd()) goto success;
        break;
      }

      default: {
      handle_unusual:
        if (tag == 0 ||
            ::google::protobuf::internal::WireFormatLite::GetTagWireType(tag) ==
            ::google::protobuf::internal::WireFormatLite::WIRETYPE_END_GROUP) {
          goto success;
        }
        DO_(::google::protobuf::internal::WireFormatLite::SkipField(input, tag));
        break;
      }
    }
  }
success:
  // @@protoc_insertion_point(parse_success:proto3.TestNestedMap)
  return true;
failure:
  // @@protoc_insertion_point(parse_failure:proto3.TestNestedMap)
  return false;
#undef DO_
}

void TestNestedMap::SerializeWithCachedSizes(
    ::google::protobuf::io::CodedOutputStream* output) const {
  // @@protoc_insertion_point(serialize_start:proto3.TestNestedMap)
  // map<bool, int32> bool_map = 1;
  if (!this->bool_map().empty()) {
    typedef ::google::protobuf::Map< bool, ::google::protobuf::int32 >::const_pointer
        ConstPtr;
    typedef ::google::protobuf::internal::SortItem< bool, ConstPtr > SortItem;
    typedef ::google::protobuf::internal::CompareByFirstField<SortItem> Less;

    if (output->IsSerializationDeterminstic() &&
        this->bool_map().size() > 1) {
      ::google::protobuf::scoped_array<SortItem> items(
          new SortItem[this->bool_map().size()]);
      typedef ::google::protobuf::Map< bool, ::google::protobuf::int32 >::size_type size_type;
      size_type n = 0;
      for (::google::protobuf::Map< bool, ::google::protobuf::int32 >::const_iterator
          it = this->bool_map().begin();
          it != this->bool_map().end(); ++it, ++n) {
        items[n] = SortItem(&*it);
      }
      ::std::sort(&items[0], &items[n], Less());
      ::google::protobuf::scoped_ptr<TestNestedMap_BoolMapEntry> entry;
      for (size_type i = 0; i < n; i++) {
        entry.reset(bool_map_.NewEntryWrapper(
            items[i].second->first, items[i].second->second));
        ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
            1, *entry, output);
      }
    } else {
      ::google::protobuf::scoped_ptr<TestNestedMap_BoolMapEntry> entry;
      for (::google::protobuf::Map< bool, ::google::protobuf::int32 >::const_iterator
          it = this->bool_map().begin();
          it != this->bool_map().end(); ++it) {
        entry.reset(bool_map_.NewEntryWrapper(
            it->first, it->second));
        ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
            1, *entry, output);
      }
    }
  }

  // map<int32, int32> int32_map = 2;
  if (!this->int32_map().empty()) {
    typedef ::google::protobuf::Map< ::google::protobuf::int32, ::google::protobuf::int32 >::const_pointer
        ConstPtr;
    typedef ::google::protobuf::internal::SortItem< ::google::protobuf::int32, ConstPtr > SortItem;
    typedef ::google::protobuf::internal::CompareByFirstField<SortItem> Less;

    if (output->IsSerializationDeterminstic() &&
        this->int32_map().size() > 1) {
      ::google::protobuf::scoped_array<SortItem> items(
          new SortItem[this->int32_map().size()]);
      typedef ::google::protobuf::Map< ::google::protobuf::int32, ::google::protobuf::int32 >::size_type size_type;
      size_type n = 0;
      for (::google::protobuf::Map< ::google::protobuf::int32, ::google::protobuf::int32 >::const_iterator
          it = this->int32_map().begin();
          it != this->int32_map().end(); ++it, ++n) {
        items[n] = SortItem(&*it);
      }
      ::std::sort(&items[0], &items[n], Less());
      ::google::protobuf::scoped_ptr<TestNestedMap_Int32MapEntry> entry;
      for (size_type i = 0; i < n; i++) {
        entry.reset(int32_map_.NewEntryWrapper(
            items[i].second->first, items[i].second->second));
        ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
            2, *entry, output);
      }
    } else {
      ::google::protobuf::scoped_ptr<TestNestedMap_Int32MapEntry> entry;
      for (::google::protobuf::Map< ::google::protobuf::int32, ::google::protobuf::int32 >::const_iterator
          it = this->int32_map().begin();
          it != this->int32_map().end(); ++it) {
        entry.reset(int32_map_.NewEntryWrapper(
            it->first, it->second));
        ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
            2, *entry, output);
      }
    }
  }

  // map<int64, int32> int64_map = 3;
  if (!this->int64_map().empty()) {
    typedef ::google::protobuf::Map< ::google::protobuf::int64, ::google::protobuf::int32 >::const_pointer
        ConstPtr;
    typedef ::google::protobuf::internal::SortItem< ::google::protobuf::int64, ConstPtr > SortItem;
    typedef ::google::protobuf::internal::CompareByFirstField<SortItem> Less;

    if (output->IsSerializationDeterminstic() &&
        this->int64_map().size() > 1) {
      ::google::protobuf::scoped_array<SortItem> items(
          new SortItem[this->int64_map().size()]);
      typedef ::google::protobuf::Map< ::google::protobuf::int64, ::google::protobuf::int32 >::size_type size_type;
      size_type n = 0;
      for (::google::protobuf::Map< ::google::protobuf::int64, ::google::protobuf::int32 >::const_iterator
          it = this->int64_map().begin();
          it != this->int64_map().end(); ++it, ++n) {
        items[n] = SortItem(&*it);
      }
      ::std::sort(&items[0], &items[n], Less());
      ::google::protobuf::scoped_ptr<TestNestedMap_Int64MapEntry> entry;
      for (size_type i = 0; i < n; i++) {
        entry.reset(int64_map_.NewEntryWrapper(
            items[i].second->first, items[i].second->second));
        ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
            3, *entry, output);
      }
    } else {
      ::google::protobuf::scoped_ptr<TestNestedMap_Int64MapEntry> entry;
      for (::google::protobuf::Map< ::google::protobuf::int64, ::google::protobuf::int32 >::const_iterator
          it = this->int64_map().begin();
          it != this->int64_map().end(); ++it) {
        entry.reset(int64_map_.NewEntryWrapper(
            it->first, it->second));
        ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
            3, *entry, output);
      }
    }
  }

  // map<uint32, int32> uint32_map = 4;
  if (!this->uint32_map().empty()) {
    typedef ::google::protobuf::Map< ::google::protobuf::uint32, ::google::protobuf::int32 >::const_pointer
        ConstPtr;
    typedef ::google::protobuf::internal::SortItem< ::google::protobuf::uint32, ConstPtr > SortItem;
    typedef ::google::protobuf::internal::CompareByFirstField<SortItem> Less;

    if (output->IsSerializationDeterminstic() &&
        this->uint32_map().size() > 1) {
      ::google::protobuf::scoped_array<SortItem> items(
          new SortItem[this->uint32_map().size()]);
      typedef ::google::protobuf::Map< ::google::protobuf::uint32, ::google::protobuf::int32 >::size_type size_type;
      size_type n = 0;
      for (::google::protobuf::Map< ::google::protobuf::uint32, ::google::protobuf::int32 >::const_iterator
          it = this->uint32_map().begin();
          it != this->uint32_map().end(); ++it, ++n) {
        items[n] = SortItem(&*it);
      }
      ::std::sort(&items[0], &items[n], Less());
      ::google::protobuf::scoped_ptr<TestNestedMap_Uint32MapEntry> entry;
      for (size_type i = 0; i < n; i++) {
        entry.reset(uint32_map_.NewEntryWrapper(
            items[i].second->first, items[i].second->second));
        ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
            4, *entry, output);
      }
    } else {
      ::google::protobuf::scoped_ptr<TestNestedMap_Uint32MapEntry> entry;
      for (::google::protobuf::Map< ::google::protobuf::uint32, ::google::protobuf::int32 >::const_iterator
          it = this->uint32_map().begin();
          it != this->uint32_map().end(); ++it) {
        entry.reset(uint32_map_.NewEntryWrapper(
            it->first, it->second));
        ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
            4, *entry, output);
      }
    }
  }

  // map<uint64, int32> uint64_map = 5;
  if (!this->uint64_map().empty()) {
    typedef ::google::protobuf::Map< ::google::protobuf::uint64, ::google::protobuf::int32 >::const_pointer
        ConstPtr;
    typedef ::google::protobuf::internal::SortItem< ::google::protobuf::uint64, ConstPtr > SortItem;
    typedef ::google::protobuf::internal::CompareByFirstField<SortItem> Less;

    if (output->IsSerializationDeterminstic() &&
        this->uint64_map().size() > 1) {
      ::google::protobuf::scoped_array<SortItem> items(
          new SortItem[this->uint64_map().size()]);
      typedef ::google::protobuf::Map< ::google::protobuf::uint64, ::google::protobuf::int32 >::size_type size_type;
      size_type n = 0;
      for (::google::protobuf::Map< ::google::protobuf::uint64, ::google::protobuf::int32 >::const_iterator
          it = this->uint64_map().begin();
          it != this->uint64_map().end(); ++it, ++n) {
        items[n] = SortItem(&*it);
      }
      ::std::sort(&items[0], &items[n], Less());
      ::google::protobuf::scoped_ptr<TestNestedMap_Uint64MapEntry> entry;
      for (size_type i = 0; i < n; i++) {
        entry.reset(uint64_map_.NewEntryWrapper(
            items[i].second->first, items[i].second->second));
        ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
            5, *entry, output);
      }
    } else {
      ::google::protobuf::scoped_ptr<TestNestedMap_Uint64MapEntry> entry;
      for (::google::protobuf::Map< ::google::protobuf::uint64, ::google::protobuf::int32 >::const_iterator
          it = this->uint64_map().begin();
          it != this->uint64_map().end(); ++it) {
        entry.reset(uint64_map_.NewEntryWrapper(
            it->first, it->second));
        ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
            5, *entry, output);
      }
    }
  }

  // map<string, int32> string_map = 6;
  if (!this->string_map().empty()) {
    typedef ::google::protobuf::Map< ::std::string, ::google::protobuf::int32 >::const_pointer
        ConstPtr;
    typedef ConstPtr SortItem;
    typedef ::google::protobuf::internal::CompareByDerefFirst<SortItem> Less;
    struct Utf8Check {
      static void Check(ConstPtr p) {
        ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
          p->first.data(), p->first.length(),
          ::google::protobuf::internal::WireFormatLite::SERIALIZE,
          "proto3.TestNestedMap.StringMapEntry.key");
      }
    };

    if (output->IsSerializationDeterminstic() &&
        this->string_map().size() > 1) {
      ::google::protobuf::scoped_array<SortItem> items(
          new SortItem[this->string_map().size()]);
      typedef ::google::protobuf::Map< ::std::string, ::google::protobuf::int32 >::size_type size_type;
      size_type n = 0;
      for (::google::protobuf::Map< ::std::string, ::google::protobuf::int32 >::const_iterator
          it = this->string_map().begin();
          it != this->string_map().end(); ++it, ++n) {
        items[n] = SortItem(&*it);
      }
      ::std::sort(&items[0], &items[n], Less());
      ::google::protobuf::scoped_ptr<TestNestedMap_StringMapEntry> entry;
      for (size_type i = 0; i < n; i++) {
        entry.reset(string_map_.NewEntryWrapper(
            items[i]->first, items[i]->second));
        ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
            6, *entry, output);
        Utf8Check::Check(items[i]);
      }
    } else {
      ::google::protobuf::scoped_ptr<TestNestedMap_StringMapEntry> entry;
      for (::google::protobuf::Map< ::std::string, ::google::protobuf::int32 >::const_iterator
          it = this->string_map().begin();
          it != this->string_map().end(); ++it) {
        entry.reset(string_map_.NewEntryWrapper(
            it->first, it->second));
        ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
            6, *entry, output);
        Utf8Check::Check(&*it);
      }
    }
  }

  // map<string, .proto3.TestNestedMap> map_map = 7;
  if (!this->map_map().empty()) {
    typedef ::google::protobuf::Map< ::std::string, ::proto3::TestNestedMap >::const_pointer
        ConstPtr;
    typedef ConstPtr SortItem;
    typedef ::google::protobuf::internal::CompareByDerefFirst<SortItem> Less;
    struct Utf8Check {
      static void Check(ConstPtr p) {
        ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
          p->first.data(), p->first.length(),
          ::google::protobuf::internal::WireFormatLite::SERIALIZE,
          "proto3.TestNestedMap.MapMapEntry.key");
      }
    };

    if (output->IsSerializationDeterminstic() &&
        this->map_map().size() > 1) {
      ::google::protobuf::scoped_array<SortItem> items(
          new SortItem[this->map_map().size()]);
      typedef ::google::protobuf::Map< ::std::string, ::proto3::TestNestedMap >::size_type size_type;
      size_type n = 0;
      for (::google::protobuf::Map< ::std::string, ::proto3::TestNestedMap >::const_iterator
          it = this->map_map().begin();
          it != this->map_map().end(); ++it, ++n) {
        items[n] = SortItem(&*it);
      }
      ::std::sort(&items[0], &items[n], Less());
      ::google::protobuf::scoped_ptr<TestNestedMap_MapMapEntry> entry;
      for (size_type i = 0; i < n; i++) {
        entry.reset(map_map_.NewEntryWrapper(
            items[i]->first, items[i]->second));
        ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
            7, *entry, output);
        Utf8Check::Check(items[i]);
      }
    } else {
      ::google::protobuf::scoped_ptr<TestNestedMap_MapMapEntry> entry;
      for (::google::protobuf::Map< ::std::string, ::proto3::TestNestedMap >::const_iterator
          it = this->map_map().begin();
          it != this->map_map().end(); ++it) {
        entry.reset(map_map_.NewEntryWrapper(
            it->first, it->second));
        ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
            7, *entry, output);
        Utf8Check::Check(&*it);
      }
    }
  }

  // @@protoc_insertion_point(serialize_end:proto3.TestNestedMap)
}

::google::protobuf::uint8* TestNestedMap::InternalSerializeWithCachedSizesToArray(
    bool deterministic, ::google::protobuf::uint8* target) const {
  (void)deterministic; // Unused
  // @@protoc_insertion_point(serialize_to_array_start:proto3.TestNestedMap)
  // map<bool, int32> bool_map = 1;
  if (!this->bool_map().empty()) {
    typedef ::google::protobuf::Map< bool, ::google::protobuf::int32 >::const_pointer
        ConstPtr;
    typedef ::google::protobuf::internal::SortItem< bool, ConstPtr > SortItem;
    typedef ::google::protobuf::internal::CompareByFirstField<SortItem> Less;

    if (deterministic &&
        this->bool_map().size() > 1) {
      ::google::protobuf::scoped_array<SortItem> items(
          new SortItem[this->bool_map().size()]);
      typedef ::google::protobuf::Map< bool, ::google::protobuf::int32 >::size_type size_type;
      size_type n = 0;
      for (::google::protobuf::Map< bool, ::google::protobuf::int32 >::const_iterator
          it = this->bool_map().begin();
          it != this->bool_map().end(); ++it, ++n) {
        items[n] = SortItem(&*it);
      }
      ::std::sort(&items[0], &items[n], Less());
      ::google::protobuf::scoped_ptr<TestNestedMap_BoolMapEntry> entry;
      for (size_type i = 0; i < n; i++) {
        entry.reset(bool_map_.NewEntryWrapper(
            items[i].second->first, items[i].second->second));
        target = ::google::protobuf::internal::WireFormatLite::
                   InternalWriteMessageNoVirtualToArray(
                       1, *entry, deterministic, target);
;
      }
    } else {
      ::google::protobuf::scoped_ptr<TestNestedMap_BoolMapEntry> entry;
      for (::google::protobuf::Map< bool, ::google::protobuf::int32 >::const_iterator
          it = this->bool_map().begin();
          it != this->bool_map().end(); ++it) {
        entry.reset(bool_map_.NewEntryWrapper(
            it->first, it->second));
        target = ::google::protobuf::internal::WireFormatLite::
                   InternalWriteMessageNoVirtualToArray(
                       1, *entry, deterministic, target);
;
      }
    }
  }

  // map<int32, int32> int32_map = 2;
  if (!this->int32_map().empty()) {
    typedef ::google::protobuf::Map< ::google::protobuf::int32, ::google::protobuf::int32 >::const_pointer
        ConstPtr;
    typedef ::google::protobuf::internal::SortItem< ::google::protobuf::int32, ConstPtr > SortItem;
    typedef ::google::protobuf::internal::CompareByFirstField<SortItem> Less;

    if (deterministic &&
        this->int32_map().size() > 1) {
      ::google::protobuf::scoped_array<SortItem> items(
          new SortItem[this->int32_map().size()]);
      typedef ::google::protobuf::Map< ::google::protobuf::int32, ::google::protobuf::int32 >::size_type size_type;
      size_type n = 0;
      for (::google::protobuf::Map< ::google::protobuf::int32, ::google::protobuf::int32 >::const_iterator
          it = this->int32_map().begin();
          it != this->int32_map().end(); ++it, ++n) {
        items[n] = SortItem(&*it);
      }
      ::std::sort(&items[0], &items[n], Less());
      ::google::protobuf::scoped_ptr<TestNestedMap_Int32MapEntry> entry;
      for (size_type i = 0; i < n; i++) {
        entry.reset(int32_map_.NewEntryWrapper(
            items[i].second->first, items[i].second->second));
        target = ::google::protobuf::internal::WireFormatLite::
                   InternalWriteMessageNoVirtualToArray(
                       2, *entry, deterministic, target);
;
      }
    } else {
      ::google::protobuf::scoped_ptr<TestNestedMap_Int32MapEntry> entry;
      for (::google::protobuf::Map< ::google::protobuf::int32, ::google::protobuf::int32 >::const_iterator
          it = this->int32_map().begin();
          it != this->int32_map().end(); ++it) {
        entry.reset(int32_map_.NewEntryWrapper(
            it->first, it->second));
        target = ::google::protobuf::internal::WireFormatLite::
                   InternalWriteMessageNoVirtualToArray(
                       2, *entry, deterministic, target);
;
      }
    }
  }

  // map<int64, int32> int64_map = 3;
  if (!this->int64_map().empty()) {
    typedef ::google::protobuf::Map< ::google::protobuf::int64, ::google::protobuf::int32 >::const_pointer
        ConstPtr;
    typedef ::google::protobuf::internal::SortItem< ::google::protobuf::int64, ConstPtr > SortItem;
    typedef ::google::protobuf::internal::CompareByFirstField<SortItem> Less;

    if (deterministic &&
        this->int64_map().size() > 1) {
      ::google::protobuf::scoped_array<SortItem> items(
          new SortItem[this->int64_map().size()]);
      typedef ::google::protobuf::Map< ::google::protobuf::int64, ::google::protobuf::int32 >::size_type size_type;
      size_type n = 0;
      for (::google::protobuf::Map< ::google::protobuf::int64, ::google::protobuf::int32 >::const_iterator
          it = this->int64_map().begin();
          it != this->int64_map().end(); ++it, ++n) {
        items[n] = SortItem(&*it);
      }
      ::std::sort(&items[0], &items[n], Less());
      ::google::protobuf::scoped_ptr<TestNestedMap_Int64MapEntry> entry;
      for (size_type i = 0; i < n; i++) {
        entry.reset(int64_map_.NewEntryWrapper(
            items[i].second->first, items[i].second->second));
        target = ::google::protobuf::internal::WireFormatLite::
                   InternalWriteMessageNoVirtualToArray(
                       3, *entry, deterministic, target);
;
      }
    } else {
      ::google::protobuf::scoped_ptr<TestNestedMap_Int64MapEntry> entry;
      for (::google::protobuf::Map< ::google::protobuf::int64, ::google::protobuf::int32 >::const_iterator
          it = this->int64_map().begin();
          it != this->int64_map().end(); ++it) {
        entry.reset(int64_map_.NewEntryWrapper(
            it->first, it->second));
        target = ::google::protobuf::internal::WireFormatLite::
                   InternalWriteMessageNoVirtualToArray(
                       3, *entry, deterministic, target);
;
      }
    }
  }

  // map<uint32, int32> uint32_map = 4;
  if (!this->uint32_map().empty()) {
    typedef ::google::protobuf::Map< ::google::protobuf::uint32, ::google::protobuf::int32 >::const_pointer
        ConstPtr;
    typedef ::google::protobuf::internal::SortItem< ::google::protobuf::uint32, ConstPtr > SortItem;
    typedef ::google::protobuf::internal::CompareByFirstField<SortItem> Less;

    if (deterministic &&
        this->uint32_map().size() > 1) {
      ::google::protobuf::scoped_array<SortItem> items(
          new SortItem[this->uint32_map().size()]);
      typedef ::google::protobuf::Map< ::google::protobuf::uint32, ::google::protobuf::int32 >::size_type size_type;
      size_type n = 0;
      for (::google::protobuf::Map< ::google::protobuf::uint32, ::google::protobuf::int32 >::const_iterator
          it = this->uint32_map().begin();
          it != this->uint32_map().end(); ++it, ++n) {
        items[n] = SortItem(&*it);
      }
      ::std::sort(&items[0], &items[n], Less());
      ::google::protobuf::scoped_ptr<TestNestedMap_Uint32MapEntry> entry;
      for (size_type i = 0; i < n; i++) {
        entry.reset(uint32_map_.NewEntryWrapper(
            items[i].second->first, items[i].second->second));
        target = ::google::protobuf::internal::WireFormatLite::
                   InternalWriteMessageNoVirtualToArray(
                       4, *entry, deterministic, target);
;
      }
    } else {
      ::google::protobuf::scoped_ptr<TestNestedMap_Uint32MapEntry> entry;
      for (::google::protobuf::Map< ::google::protobuf::uint32, ::google::protobuf::int32 >::const_iterator
          it = this->uint32_map().begin();
          it != this->uint32_map().end(); ++it) {
        entry.reset(uint32_map_.NewEntryWrapper(
            it->first, it->second));
        target = ::google::protobuf::internal::WireFormatLite::
                   InternalWriteMessageNoVirtualToArray(
                       4, *entry, deterministic, target);
;
      }
    }
  }

  // map<uint64, int32> uint64_map = 5;
  if (!this->uint64_map().empty()) {
    typedef ::google::protobuf::Map< ::google::protobuf::uint64, ::google::protobuf::int32 >::const_pointer
        ConstPtr;
    typedef ::google::protobuf::internal::SortItem< ::google::protobuf::uint64, ConstPtr > SortItem;
    typedef ::google::protobuf::internal::CompareByFirstField<SortItem> Less;

    if (deterministic &&
        this->uint64_map().size() > 1) {
      ::google::protobuf::scoped_array<SortItem> items(
          new SortItem[this->uint64_map().size()]);
      typedef ::google::protobuf::Map< ::google::protobuf::uint64, ::google::protobuf::int32 >::size_type size_type;
      size_type n = 0;
      for (::google::protobuf::Map< ::google::protobuf::uint64, ::google::protobuf::int32 >::const_iterator
          it = this->uint64_map().begin();
          it != this->uint64_map().end(); ++it, ++n) {
        items[n] = SortItem(&*it);
      }
      ::std::sort(&items[0], &items[n], Less());
      ::google::protobuf::scoped_ptr<TestNestedMap_Uint64MapEntry> entry;
      for (size_type i = 0; i < n; i++) {
        entry.reset(uint64_map_.NewEntryWrapper(
            items[i].second->first, items[i].second->second));
        target = ::google::protobuf::internal::WireFormatLite::
                   InternalWriteMessageNoVirtualToArray(
                       5, *entry, deterministic, target);
;
      }
    } else {
      ::google::protobuf::scoped_ptr<TestNestedMap_Uint64MapEntry> entry;
      for (::google::protobuf::Map< ::google::protobuf::uint64, ::google::protobuf::int32 >::const_iterator
          it = this->uint64_map().begin();
          it != this->uint64_map().end(); ++it) {
        entry.reset(uint64_map_.NewEntryWrapper(
            it->first, it->second));
        target = ::google::protobuf::internal::WireFormatLite::
                   InternalWriteMessageNoVirtualToArray(
                       5, *entry, deterministic, target);
;
      }
    }
  }

  // map<string, int32> string_map = 6;
  if (!this->string_map().empty()) {
    typedef ::google::protobuf::Map< ::std::string, ::google::protobuf::int32 >::const_pointer
        ConstPtr;
    typedef ConstPtr SortItem;
    typedef ::google::protobuf::internal::CompareByDerefFirst<SortItem> Less;
    struct Utf8Check {
      static void Check(ConstPtr p) {
        ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
          p->first.data(), p->first.length(),
          ::google::protobuf::internal::WireFormatLite::SERIALIZE,
          "proto3.TestNestedMap.StringMapEntry.key");
      }
    };

    if (deterministic &&
        this->string_map().size() > 1) {
      ::google::protobuf::scoped_array<SortItem> items(
          new SortItem[this->string_map().size()]);
      typedef ::google::protobuf::Map< ::std::string, ::google::protobuf::int32 >::size_type size_type;
      size_type n = 0;
      for (::google::protobuf::Map< ::std::string, ::google::protobuf::int32 >::const_iterator
          it = this->string_map().begin();
          it != this->string_map().end(); ++it, ++n) {
        items[n] = SortItem(&*it);
      }
      ::std::sort(&items[0], &items[n], Less());
      ::google::protobuf::scoped_ptr<TestNestedMap_StringMapEntry> entry;
      for (size_type i = 0; i < n; i++) {
        entry.reset(string_map_.NewEntryWrapper(
            items[i]->first, items[i]->second));
        target = ::google::protobuf::internal::WireFormatLite::
                   InternalWriteMessageNoVirtualToArray(
                       6, *entry, deterministic, target);
;
        Utf8Check::Check(items[i]);
      }
    } else {
      ::google::protobuf::scoped_ptr<TestNestedMap_StringMapEntry> entry;
      for (::google::protobuf::Map< ::std::string, ::google::protobuf::int32 >::const_iterator
          it = this->string_map().begin();
          it != this->string_map().end(); ++it) {
        entry.reset(string_map_.NewEntryWrapper(
            it->first, it->second));
        target = ::google::protobuf::internal::WireFormatLite::
                   InternalWriteMessageNoVirtualToArray(
                       6, *entry, deterministic, target);
;
        Utf8Check::Check(&*it);
      }
    }
  }

  // map<string, .proto3.TestNestedMap> map_map = 7;
  if (!this->map_map().empty()) {
    typedef ::google::protobuf::Map< ::std::string, ::proto3::TestNestedMap >::const_pointer
        ConstPtr;
    typedef ConstPtr SortItem;
    typedef ::google::protobuf::internal::CompareByDerefFirst<SortItem> Less;
    struct Utf8Check {
      static void Check(ConstPtr p) {
        ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
          p->first.data(), p->first.length(),
          ::google::protobuf::internal::WireFormatLite::SERIALIZE,
          "proto3.TestNestedMap.MapMapEntry.key");
      }
    };

    if (deterministic &&
        this->map_map().size() > 1) {
      ::google::protobuf::scoped_array<SortItem> items(
          new SortItem[this->map_map().size()]);
      typedef ::google::protobuf::Map< ::std::string, ::proto3::TestNestedMap >::size_type size_type;
      size_type n = 0;
      for (::google::protobuf::Map< ::std::string, ::proto3::TestNestedMap >::const_iterator
          it = this->map_map().begin();
          it != this->map_map().end(); ++it, ++n) {
        items[n] = SortItem(&*it);
      }
      ::std::sort(&items[0], &items[n], Less());
      ::google::protobuf::scoped_ptr<TestNestedMap_MapMapEntry> entry;
      for (size_type i = 0; i < n; i++) {
        entry.reset(map_map_.NewEntryWrapper(
            items[i]->first, items[i]->second));
        target = ::google::protobuf::internal::WireFormatLite::
                   InternalWriteMessageNoVirtualToArray(
                       7, *entry, deterministic, target);
;
        Utf8Check::Check(items[i]);
      }
    } else {
      ::google::protobuf::scoped_ptr<TestNestedMap_MapMapEntry> entry;
      for (::google::protobuf::Map< ::std::string, ::proto3::TestNestedMap >::const_iterator
          it = this->map_map().begin();
          it != this->map_map().end(); ++it) {
        entry.reset(map_map_.NewEntryWrapper(
            it->first, it->second));
        target = ::google::protobuf::internal::WireFormatLite::
                   InternalWriteMessageNoVirtualToArray(
                       7, *entry, deterministic, target);
;
        Utf8Check::Check(&*it);
      }
    }
  }

  // @@protoc_insertion_point(serialize_to_array_end:proto3.TestNestedMap)
  return target;
}

size_t TestNestedMap::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:proto3.TestNestedMap)
  size_t total_size = 0;

  // map<bool, int32> bool_map = 1;
  total_size += 1 *
      ::google::protobuf::internal::FromIntSize(this->bool_map_size());
  {
    ::google::protobuf::scoped_ptr<TestNestedMap_BoolMapEntry> entry;
    for (::google::protobuf::Map< bool, ::google::protobuf::int32 >::const_iterator
        it = this->bool_map().begin();
        it != this->bool_map().end(); ++it) {
      entry.reset(bool_map_.NewEntryWrapper(it->first, it->second));
      total_size += ::google::protobuf::internal::WireFormatLite::
          MessageSizeNoVirtual(*entry);
    }
  }

  // map<int32, int32> int32_map = 2;
  total_size += 1 *
      ::google::protobuf::internal::FromIntSize(this->int32_map_size());
  {
    ::google::protobuf::scoped_ptr<TestNestedMap_Int32MapEntry> entry;
    for (::google::protobuf::Map< ::google::protobuf::int32, ::google::protobuf::int32 >::const_iterator
        it = this->int32_map().begin();
        it != this->int32_map().end(); ++it) {
      entry.reset(int32_map_.NewEntryWrapper(it->first, it->second));
      total_size += ::google::protobuf::internal::WireFormatLite::
          MessageSizeNoVirtual(*entry);
    }
  }

  // map<int64, int32> int64_map = 3;
  total_size += 1 *
      ::google::protobuf::internal::FromIntSize(this->int64_map_size());
  {
    ::google::protobuf::scoped_ptr<TestNestedMap_Int64MapEntry> entry;
    for (::google::protobuf::Map< ::google::protobuf::int64, ::google::protobuf::int32 >::const_iterator
        it = this->int64_map().begin();
        it != this->int64_map().end(); ++it) {
      entry.reset(int64_map_.NewEntryWrapper(it->first, it->second));
      total_size += ::google::protobuf::internal::WireFormatLite::
          MessageSizeNoVirtual(*entry);
    }
  }

  // map<uint32, int32> uint32_map = 4;
  total_size += 1 *
      ::google::protobuf::internal::FromIntSize(this->uint32_map_size());
  {
    ::google::protobuf::scoped_ptr<TestNestedMap_Uint32MapEntry> entry;
    for (::google::protobuf::Map< ::google::protobuf::uint32, ::google::protobuf::int32 >::const_iterator
        it = this->uint32_map().begin();
        it != this->uint32_map().end(); ++it) {
      entry.reset(uint32_map_.NewEntryWrapper(it->first, it->second));
      total_size += ::google::protobuf::internal::WireFormatLite::
          MessageSizeNoVirtual(*entry);
    }
  }

  // map<uint64, int32> uint64_map = 5;
  total_size += 1 *
      ::google::protobuf::internal::FromIntSize(this->uint64_map_size());
  {
    ::google::protobuf::scoped_ptr<TestNestedMap_Uint64MapEntry> entry;
    for (::google::protobuf::Map< ::google::protobuf::uint64, ::google::protobuf::int32 >::const_iterator
        it = this->uint64_map().begin();
        it != this->uint64_map().end(); ++it) {
      entry.reset(uint64_map_.NewEntryWrapper(it->first, it->second));
      total_size += ::google::protobuf::internal::WireFormatLite::
          MessageSizeNoVirtual(*entry);
    }
  }

  // map<string, int32> string_map = 6;
  total_size += 1 *
      ::google::protobuf::internal::FromIntSize(this->string_map_size());
  {
    ::google::protobuf::scoped_ptr<TestNestedMap_StringMapEntry> entry;
    for (::google::protobuf::Map< ::std::string, ::google::protobuf::int32 >::const_iterator
        it = this->string_map().begin();
        it != this->string_map().end(); ++it) {
      entry.reset(string_map_.NewEntryWrapper(it->first, it->second));
      total_size += ::google::protobuf::internal::WireFormatLite::
          MessageSizeNoVirtual(*entry);
    }
  }

  // map<string, .proto3.TestNestedMap> map_map = 7;
  total_size += 1 *
      ::google::protobuf::internal::FromIntSize(this->map_map_size());
  {
    ::google::protobuf::scoped_ptr<TestNestedMap_MapMapEntry> entry;
    for (::google::protobuf::Map< ::std::string, ::proto3::TestNestedMap >::const_iterator
        it = this->map_map().begin();
        it != this->map_map().end(); ++it) {
      entry.reset(map_map_.NewEntryWrapper(it->first, it->second));
      total_size += ::google::protobuf::internal::WireFormatLite::
          MessageSizeNoVirtual(*entry);
    }
  }

  int cached_size = ::google::protobuf::internal::ToCachedSize(total_size);
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = cached_size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
  return total_size;
}

void TestNestedMap::MergeFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_merge_from_start:proto3.TestNestedMap)
  if (GOOGLE_PREDICT_FALSE(&from == this)) MergeFromFail(__LINE__);
  const TestNestedMap* source =
      ::google::protobuf::internal::DynamicCastToGenerated<const TestNestedMap>(
          &from);
  if (source == NULL) {
  // @@protoc_insertion_point(generalized_merge_from_cast_fail:proto3.TestNestedMap)
    ::google::protobuf::internal::ReflectionOps::Merge(from, this);
  } else {
  // @@protoc_insertion_point(generalized_merge_from_cast_success:proto3.TestNestedMap)
    UnsafeMergeFrom(*source);
  }
}

void TestNestedMap::MergeFrom(const TestNestedMap& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:proto3.TestNestedMap)
  if (GOOGLE_PREDICT_TRUE(&from != this)) {
    UnsafeMergeFrom(from);
  } else {
    MergeFromFail(__LINE__);
  }
}

void TestNestedMap::UnsafeMergeFrom(const TestNestedMap& from) {
  GOOGLE_DCHECK(&from != this);
  bool_map_.MergeFrom(from.bool_map_);
  int32_map_.MergeFrom(from.int32_map_);
  int64_map_.MergeFrom(from.int64_map_);
  uint32_map_.MergeFrom(from.uint32_map_);
  uint64_map_.MergeFrom(from.uint64_map_);
  string_map_.MergeFrom(from.string_map_);
  map_map_.MergeFrom(from.map_map_);
}

void TestNestedMap::CopyFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_copy_from_start:proto3.TestNestedMap)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

void TestNestedMap::CopyFrom(const TestNestedMap& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:proto3.TestNestedMap)
  if (&from == this) return;
  Clear();
  UnsafeMergeFrom(from);
}

bool TestNestedMap::IsInitialized() const {

  return true;
}

void TestNestedMap::Swap(TestNestedMap* other) {
  if (other == this) return;
  InternalSwap(other);
}
void TestNestedMap::InternalSwap(TestNestedMap* other) {
  bool_map_.Swap(&other->bool_map_);
  int32_map_.Swap(&other->int32_map_);
  int64_map_.Swap(&other->int64_map_);
  uint32_map_.Swap(&other->uint32_map_);
  uint64_map_.Swap(&other->uint64_map_);
  string_map_.Swap(&other->string_map_);
  map_map_.Swap(&other->map_map_);
  _internal_metadata_.Swap(&other->_internal_metadata_);
  std::swap(_cached_size_, other->_cached_size_);
}

::google::protobuf::Metadata TestNestedMap::GetMetadata() const {
  protobuf_AssignDescriptorsOnce();
  ::google::protobuf::Metadata metadata;
  metadata.descriptor = TestNestedMap_descriptor_;
  metadata.reflection = TestNestedMap_reflection_;
  return metadata;
}

#if PROTOBUF_INLINE_NOT_IN_HEADERS
// TestNestedMap

// map<bool, int32> bool_map = 1;
int TestNestedMap::bool_map_size() const {
  return bool_map_.size();
}
void TestNestedMap::clear_bool_map() {
  bool_map_.Clear();
}
 const ::google::protobuf::Map< bool, ::google::protobuf::int32 >&
TestNestedMap::bool_map() const {
  // @@protoc_insertion_point(field_map:proto3.TestNestedMap.bool_map)
  return bool_map_.GetMap();
}
 ::google::protobuf::Map< bool, ::google::protobuf::int32 >*
TestNestedMap::mutable_bool_map() {
  // @@protoc_insertion_point(field_mutable_map:proto3.TestNestedMap.bool_map)
  return bool_map_.MutableMap();
}

// map<int32, int32> int32_map = 2;
int TestNestedMap::int32_map_size() const {
  return int32_map_.size();
}
void TestNestedMap::clear_int32_map() {
  int32_map_.Clear();
}
 const ::google::protobuf::Map< ::google::protobuf::int32, ::google::protobuf::int32 >&
TestNestedMap::int32_map() const {
  // @@protoc_insertion_point(field_map:proto3.TestNestedMap.int32_map)
  return int32_map_.GetMap();
}
 ::google::protobuf::Map< ::google::protobuf::int32, ::google::protobuf::int32 >*
TestNestedMap::mutable_int32_map() {
  // @@protoc_insertion_point(field_mutable_map:proto3.TestNestedMap.int32_map)
  return int32_map_.MutableMap();
}

// map<int64, int32> int64_map = 3;
int TestNestedMap::int64_map_size() const {
  return int64_map_.size();
}
void TestNestedMap::clear_int64_map() {
  int64_map_.Clear();
}
 const ::google::protobuf::Map< ::google::protobuf::int64, ::google::protobuf::int32 >&
TestNestedMap::int64_map() const {
  // @@protoc_insertion_point(field_map:proto3.TestNestedMap.int64_map)
  return int64_map_.GetMap();
}
 ::google::protobuf::Map< ::google::protobuf::int64, ::google::protobuf::int32 >*
TestNestedMap::mutable_int64_map() {
  // @@protoc_insertion_point(field_mutable_map:proto3.TestNestedMap.int64_map)
  return int64_map_.MutableMap();
}

// map<uint32, int32> uint32_map = 4;
int TestNestedMap::uint32_map_size() const {
  return uint32_map_.size();
}
void TestNestedMap::clear_uint32_map() {
  uint32_map_.Clear();
}
 const ::google::protobuf::Map< ::google::protobuf::uint32, ::google::protobuf::int32 >&
TestNestedMap::uint32_map() const {
  // @@protoc_insertion_point(field_map:proto3.TestNestedMap.uint32_map)
  return uint32_map_.GetMap();
}
 ::google::protobuf::Map< ::google::protobuf::uint32, ::google::protobuf::int32 >*
TestNestedMap::mutable_uint32_map() {
  // @@protoc_insertion_point(field_mutable_map:proto3.TestNestedMap.uint32_map)
  return uint32_map_.MutableMap();
}

// map<uint64, int32> uint64_map = 5;
int TestNestedMap::uint64_map_size() const {
  return uint64_map_.size();
}
void TestNestedMap::clear_uint64_map() {
  uint64_map_.Clear();
}
 const ::google::protobuf::Map< ::google::protobuf::uint64, ::google::protobuf::int32 >&
TestNestedMap::uint64_map() const {
  // @@protoc_insertion_point(field_map:proto3.TestNestedMap.uint64_map)
  return uint64_map_.GetMap();
}
 ::google::protobuf::Map< ::google::protobuf::uint64, ::google::protobuf::int32 >*
TestNestedMap::mutable_uint64_map() {
  // @@protoc_insertion_point(field_mutable_map:proto3.TestNestedMap.uint64_map)
  return uint64_map_.MutableMap();
}

// map<string, int32> string_map = 6;
int TestNestedMap::string_map_size() const {
  return string_map_.size();
}
void TestNestedMap::clear_string_map() {
  string_map_.Clear();
}
 const ::google::protobuf::Map< ::std::string, ::google::protobuf::int32 >&
TestNestedMap::string_map() const {
  // @@protoc_insertion_point(field_map:proto3.TestNestedMap.string_map)
  return string_map_.GetMap();
}
 ::google::protobuf::Map< ::std::string, ::google::protobuf::int32 >*
TestNestedMap::mutable_string_map() {
  // @@protoc_insertion_point(field_mutable_map:proto3.TestNestedMap.string_map)
  return string_map_.MutableMap();
}

// map<string, .proto3.TestNestedMap> map_map = 7;
int TestNestedMap::map_map_size() const {
  return map_map_.size();
}
void TestNestedMap::clear_map_map() {
  map_map_.Clear();
}
 const ::google::protobuf::Map< ::std::string, ::proto3::TestNestedMap >&
TestNestedMap::map_map() const {
  // @@protoc_insertion_point(field_map:proto3.TestNestedMap.map_map)
  return map_map_.GetMap();
}
 ::google::protobuf::Map< ::std::string, ::proto3::TestNestedMap >*
TestNestedMap::mutable_map_map() {
  // @@protoc_insertion_point(field_mutable_map:proto3.TestNestedMap.map_map)
  return map_map_.MutableMap();
}

inline const TestNestedMap* TestNestedMap::internal_default_instance() {
  return &TestNestedMap_default_instance_.get();
}
#endif  // PROTOBUF_INLINE_NOT_IN_HEADERS

// ===================================================================

#if !defined(_MSC_VER) || _MSC_VER >= 1900
const int TestWrapper::kBoolValueFieldNumber;
const int TestWrapper::kInt32ValueFieldNumber;
const int TestWrapper::kInt64ValueFieldNumber;
const int TestWrapper::kUint32ValueFieldNumber;
const int TestWrapper::kUint64ValueFieldNumber;
const int TestWrapper::kFloatValueFieldNumber;
const int TestWrapper::kDoubleValueFieldNumber;
const int TestWrapper::kStringValueFieldNumber;
const int TestWrapper::kBytesValueFieldNumber;
const int TestWrapper::kRepeatedBoolValueFieldNumber;
const int TestWrapper::kRepeatedInt32ValueFieldNumber;
const int TestWrapper::kRepeatedInt64ValueFieldNumber;
const int TestWrapper::kRepeatedUint32ValueFieldNumber;
const int TestWrapper::kRepeatedUint64ValueFieldNumber;
const int TestWrapper::kRepeatedFloatValueFieldNumber;
const int TestWrapper::kRepeatedDoubleValueFieldNumber;
const int TestWrapper::kRepeatedStringValueFieldNumber;
const int TestWrapper::kRepeatedBytesValueFieldNumber;
#endif  // !defined(_MSC_VER) || _MSC_VER >= 1900

TestWrapper::TestWrapper()
  : ::google::protobuf::Message(), _internal_metadata_(NULL) {
  if (this != internal_default_instance()) protobuf_InitDefaults_google_2fprotobuf_2futil_2fjson_5fformat_5fproto3_2eproto();
  SharedCtor();
  // @@protoc_insertion_point(constructor:proto3.TestWrapper)
}

void TestWrapper::InitAsDefaultInstance() {
  bool_value_ = const_cast< ::google::protobuf::BoolValue*>(
      ::google::protobuf::BoolValue::internal_default_instance());
  int32_value_ = const_cast< ::google::protobuf::Int32Value*>(
      ::google::protobuf::Int32Value::internal_default_instance());
  int64_value_ = const_cast< ::google::protobuf::Int64Value*>(
      ::google::protobuf::Int64Value::internal_default_instance());
  uint32_value_ = const_cast< ::google::protobuf::UInt32Value*>(
      ::google::protobuf::UInt32Value::internal_default_instance());
  uint64_value_ = const_cast< ::google::protobuf::UInt64Value*>(
      ::google::protobuf::UInt64Value::internal_default_instance());
  float_value_ = const_cast< ::google::protobuf::FloatValue*>(
      ::google::protobuf::FloatValue::internal_default_instance());
  double_value_ = const_cast< ::google::protobuf::DoubleValue*>(
      ::google::protobuf::DoubleValue::internal_default_instance());
  string_value_ = const_cast< ::google::protobuf::StringValue*>(
      ::google::protobuf::StringValue::internal_default_instance());
  bytes_value_ = const_cast< ::google::protobuf::BytesValue*>(
      ::google::protobuf::BytesValue::internal_default_instance());
}

TestWrapper::TestWrapper(const TestWrapper& from)
  : ::google::protobuf::Message(),
    _internal_metadata_(NULL) {
  SharedCtor();
  UnsafeMergeFrom(from);
  // @@protoc_insertion_point(copy_constructor:proto3.TestWrapper)
}

void TestWrapper::SharedCtor() {
  bool_value_ = NULL;
  int32_value_ = NULL;
  int64_value_ = NULL;
  uint32_value_ = NULL;
  uint64_value_ = NULL;
  float_value_ = NULL;
  double_value_ = NULL;
  string_value_ = NULL;
  bytes_value_ = NULL;
  _cached_size_ = 0;
}

TestWrapper::~TestWrapper() {
  // @@protoc_insertion_point(destructor:proto3.TestWrapper)
  SharedDtor();
}

void TestWrapper::SharedDtor() {
  if (this != &TestWrapper_default_instance_.get()) {
    delete bool_value_;
    delete int32_value_;
    delete int64_value_;
    delete uint32_value_;
    delete uint64_value_;
    delete float_value_;
    delete double_value_;
    delete string_value_;
    delete bytes_value_;
  }
}

void TestWrapper::SetCachedSize(int size) const {
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
}
const ::google::protobuf::Descriptor* TestWrapper::descriptor() {
  protobuf_AssignDescriptorsOnce();
  return TestWrapper_descriptor_;
}

const TestWrapper& TestWrapper::default_instance() {
  protobuf_InitDefaults_google_2fprotobuf_2futil_2fjson_5fformat_5fproto3_2eproto();
  return *internal_default_instance();
}

::google::protobuf::internal::ExplicitlyConstructed<TestWrapper> TestWrapper_default_instance_;

TestWrapper* TestWrapper::New(::google::protobuf::Arena* arena) const {
  TestWrapper* n = new TestWrapper;
  if (arena != NULL) {
    arena->Own(n);
  }
  return n;
}

void TestWrapper::Clear() {
// @@protoc_insertion_point(message_clear_start:proto3.TestWrapper)
  if (GetArenaNoVirtual() == NULL && bool_value_ != NULL) delete bool_value_;
  bool_value_ = NULL;
  if (GetArenaNoVirtual() == NULL && int32_value_ != NULL) delete int32_value_;
  int32_value_ = NULL;
  if (GetArenaNoVirtual() == NULL && int64_value_ != NULL) delete int64_value_;
  int64_value_ = NULL;
  if (GetArenaNoVirtual() == NULL && uint32_value_ != NULL) delete uint32_value_;
  uint32_value_ = NULL;
  if (GetArenaNoVirtual() == NULL && uint64_value_ != NULL) delete uint64_value_;
  uint64_value_ = NULL;
  if (GetArenaNoVirtual() == NULL && float_value_ != NULL) delete float_value_;
  float_value_ = NULL;
  if (GetArenaNoVirtual() == NULL && double_value_ != NULL) delete double_value_;
  double_value_ = NULL;
  if (GetArenaNoVirtual() == NULL && string_value_ != NULL) delete string_value_;
  string_value_ = NULL;
  if (GetArenaNoVirtual() == NULL && bytes_value_ != NULL) delete bytes_value_;
  bytes_value_ = NULL;
  repeated_bool_value_.Clear();
  repeated_int32_value_.Clear();
  repeated_int64_value_.Clear();
  repeated_uint32_value_.Clear();
  repeated_uint64_value_.Clear();
  repeated_float_value_.Clear();
  repeated_double_value_.Clear();
  repeated_string_value_.Clear();
  repeated_bytes_value_.Clear();
}

bool TestWrapper::MergePartialFromCodedStream(
    ::google::protobuf::io::CodedInputStream* input) {
#define DO_(EXPRESSION) if (!GOOGLE_PREDICT_TRUE(EXPRESSION)) goto failure
  ::google::protobuf::uint32 tag;
  // @@protoc_insertion_point(parse_start:proto3.TestWrapper)
  for (;;) {
    ::std::pair< ::google::protobuf::uint32, bool> p = input->ReadTagWithCutoff(16383);
    tag = p.first;
    if (!p.second) goto handle_unusual;
    switch (::google::protobuf::internal::WireFormatLite::GetTagFieldNumber(tag)) {
      // optional .google.protobuf.BoolValue bool_value = 1;
      case 1: {
        if (tag == 10) {
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessageNoVirtual(
               input, mutable_bool_value()));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(18)) goto parse_int32_value;
        break;
      }

      // optional .google.protobuf.Int32Value int32_value = 2;
      case 2: {
        if (tag == 18) {
         parse_int32_value:
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessageNoVirtual(
               input, mutable_int32_value()));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(26)) goto parse_int64_value;
        break;
      }

      // optional .google.protobuf.Int64Value int64_value = 3;
      case 3: {
        if (tag == 26) {
         parse_int64_value:
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessageNoVirtual(
               input, mutable_int64_value()));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(34)) goto parse_uint32_value;
        break;
      }

      // optional .google.protobuf.UInt32Value uint32_value = 4;
      case 4: {
        if (tag == 34) {
         parse_uint32_value:
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessageNoVirtual(
               input, mutable_uint32_value()));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(42)) goto parse_uint64_value;
        break;
      }

      // optional .google.protobuf.UInt64Value uint64_value = 5;
      case 5: {
        if (tag == 42) {
         parse_uint64_value:
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessageNoVirtual(
               input, mutable_uint64_value()));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(50)) goto parse_float_value;
        break;
      }

      // optional .google.protobuf.FloatValue float_value = 6;
      case 6: {
        if (tag == 50) {
         parse_float_value:
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessageNoVirtual(
               input, mutable_float_value()));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(58)) goto parse_double_value;
        break;
      }

      // optional .google.protobuf.DoubleValue double_value = 7;
      case 7: {
        if (tag == 58) {
         parse_double_value:
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessageNoVirtual(
               input, mutable_double_value()));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(66)) goto parse_string_value;
        break;
      }

      // optional .google.protobuf.StringValue string_value = 8;
      case 8: {
        if (tag == 66) {
         parse_string_value:
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessageNoVirtual(
               input, mutable_string_value()));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(74)) goto parse_bytes_value;
        break;
      }

      // optional .google.protobuf.BytesValue bytes_value = 9;
      case 9: {
        if (tag == 74) {
         parse_bytes_value:
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessageNoVirtual(
               input, mutable_bytes_value()));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(90)) goto parse_repeated_bool_value;
        break;
      }

      // repeated .google.protobuf.BoolValue repeated_bool_value = 11;
      case 11: {
        if (tag == 90) {
         parse_repeated_bool_value:
          DO_(input->IncrementRecursionDepth());
         parse_loop_repeated_bool_value:
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessageNoVirtualNoRecursionDepth(
                input, add_repeated_bool_value()));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(90)) goto parse_loop_repeated_bool_value;
        if (input->ExpectTag(98)) goto parse_loop_repeated_int32_value;
        input->UnsafeDecrementRecursionDepth();
        break;
      }

      // repeated .google.protobuf.Int32Value repeated_int32_value = 12;
      case 12: {
        if (tag == 98) {
          DO_(input->IncrementRecursionDepth());
         parse_loop_repeated_int32_value:
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessageNoVirtualNoRecursionDepth(
                input, add_repeated_int32_value()));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(98)) goto parse_loop_repeated_int32_value;
        if (input->ExpectTag(106)) goto parse_loop_repeated_int64_value;
        input->UnsafeDecrementRecursionDepth();
        break;
      }

      // repeated .google.protobuf.Int64Value repeated_int64_value = 13;
      case 13: {
        if (tag == 106) {
          DO_(input->IncrementRecursionDepth());
         parse_loop_repeated_int64_value:
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessageNoVirtualNoRecursionDepth(
                input, add_repeated_int64_value()));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(106)) goto parse_loop_repeated_int64_value;
        if (input->ExpectTag(114)) goto parse_loop_repeated_uint32_value;
        input->UnsafeDecrementRecursionDepth();
        break;
      }

      // repeated .google.protobuf.UInt32Value repeated_uint32_value = 14;
      case 14: {
        if (tag == 114) {
          DO_(input->IncrementRecursionDepth());
         parse_loop_repeated_uint32_value:
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessageNoVirtualNoRecursionDepth(
                input, add_repeated_uint32_value()));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(114)) goto parse_loop_repeated_uint32_value;
        if (input->ExpectTag(122)) goto parse_loop_repeated_uint64_value;
        input->UnsafeDecrementRecursionDepth();
        break;
      }

      // repeated .google.protobuf.UInt64Value repeated_uint64_value = 15;
      case 15: {
        if (tag == 122) {
          DO_(input->IncrementRecursionDepth());
         parse_loop_repeated_uint64_value:
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessageNoVirtualNoRecursionDepth(
                input, add_repeated_uint64_value()));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(122)) goto parse_loop_repeated_uint64_value;
        if (input->ExpectTag(130)) goto parse_loop_repeated_float_value;
        input->UnsafeDecrementRecursionDepth();
        break;
      }

      // repeated .google.protobuf.FloatValue repeated_float_value = 16;
      case 16: {
        if (tag == 130) {
          DO_(input->IncrementRecursionDepth());
         parse_loop_repeated_float_value:
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessageNoVirtualNoRecursionDepth(
                input, add_repeated_float_value()));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(130)) goto parse_loop_repeated_float_value;
        if (input->ExpectTag(138)) goto parse_loop_repeated_double_value;
        input->UnsafeDecrementRecursionDepth();
        break;
      }

      // repeated .google.protobuf.DoubleValue repeated_double_value = 17;
      case 17: {
        if (tag == 138) {
          DO_(input->IncrementRecursionDepth());
         parse_loop_repeated_double_value:
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessageNoVirtualNoRecursionDepth(
                input, add_repeated_double_value()));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(138)) goto parse_loop_repeated_double_value;
        if (input->ExpectTag(146)) goto parse_loop_repeated_string_value;
        input->UnsafeDecrementRecursionDepth();
        break;
      }

      // repeated .google.protobuf.StringValue repeated_string_value = 18;
      case 18: {
        if (tag == 146) {
          DO_(input->IncrementRecursionDepth());
         parse_loop_repeated_string_value:
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessageNoVirtualNoRecursionDepth(
                input, add_repeated_string_value()));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(146)) goto parse_loop_repeated_string_value;
        if (input->ExpectTag(154)) goto parse_loop_repeated_bytes_value;
        input->UnsafeDecrementRecursionDepth();
        break;
      }

      // repeated .google.protobuf.BytesValue repeated_bytes_value = 19;
      case 19: {
        if (tag == 154) {
          DO_(input->IncrementRecursionDepth());
         parse_loop_repeated_bytes_value:
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessageNoVirtualNoRecursionDepth(
                input, add_repeated_bytes_value()));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(154)) goto parse_loop_repeated_bytes_value;
        input->UnsafeDecrementRecursionDepth();
        if (input->ExpectAtEnd()) goto success;
        break;
      }

      default: {
      handle_unusual:
        if (tag == 0 ||
            ::google::protobuf::internal::WireFormatLite::GetTagWireType(tag) ==
            ::google::protobuf::internal::WireFormatLite::WIRETYPE_END_GROUP) {
          goto success;
        }
        DO_(::google::protobuf::internal::WireFormatLite::SkipField(input, tag));
        break;
      }
    }
  }
success:
  // @@protoc_insertion_point(parse_success:proto3.TestWrapper)
  return true;
failure:
  // @@protoc_insertion_point(parse_failure:proto3.TestWrapper)
  return false;
#undef DO_
}

void TestWrapper::SerializeWithCachedSizes(
    ::google::protobuf::io::CodedOutputStream* output) const {
  // @@protoc_insertion_point(serialize_start:proto3.TestWrapper)
  // optional .google.protobuf.BoolValue bool_value = 1;
  if (this->has_bool_value()) {
    ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
      1, *this->bool_value_, output);
  }

  // optional .google.protobuf.Int32Value int32_value = 2;
  if (this->has_int32_value()) {
    ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
      2, *this->int32_value_, output);
  }

  // optional .google.protobuf.Int64Value int64_value = 3;
  if (this->has_int64_value()) {
    ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
      3, *this->int64_value_, output);
  }

  // optional .google.protobuf.UInt32Value uint32_value = 4;
  if (this->has_uint32_value()) {
    ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
      4, *this->uint32_value_, output);
  }

  // optional .google.protobuf.UInt64Value uint64_value = 5;
  if (this->has_uint64_value()) {
    ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
      5, *this->uint64_value_, output);
  }

  // optional .google.protobuf.FloatValue float_value = 6;
  if (this->has_float_value()) {
    ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
      6, *this->float_value_, output);
  }

  // optional .google.protobuf.DoubleValue double_value = 7;
  if (this->has_double_value()) {
    ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
      7, *this->double_value_, output);
  }

  // optional .google.protobuf.StringValue string_value = 8;
  if (this->has_string_value()) {
    ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
      8, *this->string_value_, output);
  }

  // optional .google.protobuf.BytesValue bytes_value = 9;
  if (this->has_bytes_value()) {
    ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
      9, *this->bytes_value_, output);
  }

  // repeated .google.protobuf.BoolValue repeated_bool_value = 11;
  for (unsigned int i = 0, n = this->repeated_bool_value_size(); i < n; i++) {
    ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
      11, this->repeated_bool_value(i), output);
  }

  // repeated .google.protobuf.Int32Value repeated_int32_value = 12;
  for (unsigned int i = 0, n = this->repeated_int32_value_size(); i < n; i++) {
    ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
      12, this->repeated_int32_value(i), output);
  }

  // repeated .google.protobuf.Int64Value repeated_int64_value = 13;
  for (unsigned int i = 0, n = this->repeated_int64_value_size(); i < n; i++) {
    ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
      13, this->repeated_int64_value(i), output);
  }

  // repeated .google.protobuf.UInt32Value repeated_uint32_value = 14;
  for (unsigned int i = 0, n = this->repeated_uint32_value_size(); i < n; i++) {
    ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
      14, this->repeated_uint32_value(i), output);
  }

  // repeated .google.protobuf.UInt64Value repeated_uint64_value = 15;
  for (unsigned int i = 0, n = this->repeated_uint64_value_size(); i < n; i++) {
    ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
      15, this->repeated_uint64_value(i), output);
  }

  // repeated .google.protobuf.FloatValue repeated_float_value = 16;
  for (unsigned int i = 0, n = this->repeated_float_value_size(); i < n; i++) {
    ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
      16, this->repeated_float_value(i), output);
  }

  // repeated .google.protobuf.DoubleValue repeated_double_value = 17;
  for (unsigned int i = 0, n = this->repeated_double_value_size(); i < n; i++) {
    ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
      17, this->repeated_double_value(i), output);
  }

  // repeated .google.protobuf.StringValue repeated_string_value = 18;
  for (unsigned int i = 0, n = this->repeated_string_value_size(); i < n; i++) {
    ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
      18, this->repeated_string_value(i), output);
  }

  // repeated .google.protobuf.BytesValue repeated_bytes_value = 19;
  for (unsigned int i = 0, n = this->repeated_bytes_value_size(); i < n; i++) {
    ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
      19, this->repeated_bytes_value(i), output);
  }

  // @@protoc_insertion_point(serialize_end:proto3.TestWrapper)
}

::google::protobuf::uint8* TestWrapper::InternalSerializeWithCachedSizesToArray(
    bool deterministic, ::google::protobuf::uint8* target) const {
  (void)deterministic; // Unused
  // @@protoc_insertion_point(serialize_to_array_start:proto3.TestWrapper)
  // optional .google.protobuf.BoolValue bool_value = 1;
  if (this->has_bool_value()) {
    target = ::google::protobuf::internal::WireFormatLite::
      InternalWriteMessageNoVirtualToArray(
        1, *this->bool_value_, false, target);
  }

  // optional .google.protobuf.Int32Value int32_value = 2;
  if (this->has_int32_value()) {
    target = ::google::protobuf::internal::WireFormatLite::
      InternalWriteMessageNoVirtualToArray(
        2, *this->int32_value_, false, target);
  }

  // optional .google.protobuf.Int64Value int64_value = 3;
  if (this->has_int64_value()) {
    target = ::google::protobuf::internal::WireFormatLite::
      InternalWriteMessageNoVirtualToArray(
        3, *this->int64_value_, false, target);
  }

  // optional .google.protobuf.UInt32Value uint32_value = 4;
  if (this->has_uint32_value()) {
    target = ::google::protobuf::internal::WireFormatLite::
      InternalWriteMessageNoVirtualToArray(
        4, *this->uint32_value_, false, target);
  }

  // optional .google.protobuf.UInt64Value uint64_value = 5;
  if (this->has_uint64_value()) {
    target = ::google::protobuf::internal::WireFormatLite::
      InternalWriteMessageNoVirtualToArray(
        5, *this->uint64_value_, false, target);
  }

  // optional .google.protobuf.FloatValue float_value = 6;
  if (this->has_float_value()) {
    target = ::google::protobuf::internal::WireFormatLite::
      InternalWriteMessageNoVirtualToArray(
        6, *this->float_value_, false, target);
  }

  // optional .google.protobuf.DoubleValue double_value = 7;
  if (this->has_double_value()) {
    target = ::google::protobuf::internal::WireFormatLite::
      InternalWriteMessageNoVirtualToArray(
        7, *this->double_value_, false, target);
  }

  // optional .google.protobuf.StringValue string_value = 8;
  if (this->has_string_value()) {
    target = ::google::protobuf::internal::WireFormatLite::
      InternalWriteMessageNoVirtualToArray(
        8, *this->string_value_, false, target);
  }

  // optional .google.protobuf.BytesValue bytes_value = 9;
  if (this->has_bytes_value()) {
    target = ::google::protobuf::internal::WireFormatLite::
      InternalWriteMessageNoVirtualToArray(
        9, *this->bytes_value_, false, target);
  }

  // repeated .google.protobuf.BoolValue repeated_bool_value = 11;
  for (unsigned int i = 0, n = this->repeated_bool_value_size(); i < n; i++) {
    target = ::google::protobuf::internal::WireFormatLite::
      InternalWriteMessageNoVirtualToArray(
        11, this->repeated_bool_value(i), false, target);
  }

  // repeated .google.protobuf.Int32Value repeated_int32_value = 12;
  for (unsigned int i = 0, n = this->repeated_int32_value_size(); i < n; i++) {
    target = ::google::protobuf::internal::WireFormatLite::
      InternalWriteMessageNoVirtualToArray(
        12, this->repeated_int32_value(i), false, target);
  }

  // repeated .google.protobuf.Int64Value repeated_int64_value = 13;
  for (unsigned int i = 0, n = this->repeated_int64_value_size(); i < n; i++) {
    target = ::google::protobuf::internal::WireFormatLite::
      InternalWriteMessageNoVirtualToArray(
        13, this->repeated_int64_value(i), false, target);
  }

  // repeated .google.protobuf.UInt32Value repeated_uint32_value = 14;
  for (unsigned int i = 0, n = this->repeated_uint32_value_size(); i < n; i++) {
    target = ::google::protobuf::internal::WireFormatLite::
      InternalWriteMessageNoVirtualToArray(
        14, this->repeated_uint32_value(i), false, target);
  }

  // repeated .google.protobuf.UInt64Value repeated_uint64_value = 15;
  for (unsigned int i = 0, n = this->repeated_uint64_value_size(); i < n; i++) {
    target = ::google::protobuf::internal::WireFormatLite::
      InternalWriteMessageNoVirtualToArray(
        15, this->repeated_uint64_value(i), false, target);
  }

  // repeated .google.protobuf.FloatValue repeated_float_value = 16;
  for (unsigned int i = 0, n = this->repeated_float_value_size(); i < n; i++) {
    target = ::google::protobuf::internal::WireFormatLite::
      InternalWriteMessageNoVirtualToArray(
        16, this->repeated_float_value(i), false, target);
  }

  // repeated .google.protobuf.DoubleValue repeated_double_value = 17;
  for (unsigned int i = 0, n = this->repeated_double_value_size(); i < n; i++) {
    target = ::google::protobuf::internal::WireFormatLite::
      InternalWriteMessageNoVirtualToArray(
        17, this->repeated_double_value(i), false, target);
  }

  // repeated .google.protobuf.StringValue repeated_string_value = 18;
  for (unsigned int i = 0, n = this->repeated_string_value_size(); i < n; i++) {
    target = ::google::protobuf::internal::WireFormatLite::
      InternalWriteMessageNoVirtualToArray(
        18, this->repeated_string_value(i), false, target);
  }

  // repeated .google.protobuf.BytesValue repeated_bytes_value = 19;
  for (unsigned int i = 0, n = this->repeated_bytes_value_size(); i < n; i++) {
    target = ::google::protobuf::internal::WireFormatLite::
      InternalWriteMessageNoVirtualToArray(
        19, this->repeated_bytes_value(i), false, target);
  }

  // @@protoc_insertion_point(serialize_to_array_end:proto3.TestWrapper)
  return target;
}

size_t TestWrapper::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:proto3.TestWrapper)
  size_t total_size = 0;

  // optional .google.protobuf.BoolValue bool_value = 1;
  if (this->has_bool_value()) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::MessageSizeNoVirtual(
        *this->bool_value_);
  }

  // optional .google.protobuf.Int32Value int32_value = 2;
  if (this->has_int32_value()) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::MessageSizeNoVirtual(
        *this->int32_value_);
  }

  // optional .google.protobuf.Int64Value int64_value = 3;
  if (this->has_int64_value()) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::MessageSizeNoVirtual(
        *this->int64_value_);
  }

  // optional .google.protobuf.UInt32Value uint32_value = 4;
  if (this->has_uint32_value()) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::MessageSizeNoVirtual(
        *this->uint32_value_);
  }

  // optional .google.protobuf.UInt64Value uint64_value = 5;
  if (this->has_uint64_value()) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::MessageSizeNoVirtual(
        *this->uint64_value_);
  }

  // optional .google.protobuf.FloatValue float_value = 6;
  if (this->has_float_value()) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::MessageSizeNoVirtual(
        *this->float_value_);
  }

  // optional .google.protobuf.DoubleValue double_value = 7;
  if (this->has_double_value()) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::MessageSizeNoVirtual(
        *this->double_value_);
  }

  // optional .google.protobuf.StringValue string_value = 8;
  if (this->has_string_value()) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::MessageSizeNoVirtual(
        *this->string_value_);
  }

  // optional .google.protobuf.BytesValue bytes_value = 9;
  if (this->has_bytes_value()) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::MessageSizeNoVirtual(
        *this->bytes_value_);
  }

  // repeated .google.protobuf.BoolValue repeated_bool_value = 11;
  {
    unsigned int count = this->repeated_bool_value_size();
    total_size += 1UL * count;
    for (unsigned int i = 0; i < count; i++) {
      total_size +=
        ::google::protobuf::internal::WireFormatLite::MessageSizeNoVirtual(
          this->repeated_bool_value(i));
    }
  }

  // repeated .google.protobuf.Int32Value repeated_int32_value = 12;
  {
    unsigned int count = this->repeated_int32_value_size();
    total_size += 1UL * count;
    for (unsigned int i = 0; i < count; i++) {
      total_size +=
        ::google::protobuf::internal::WireFormatLite::MessageSizeNoVirtual(
          this->repeated_int32_value(i));
    }
  }

  // repeated .google.protobuf.Int64Value repeated_int64_value = 13;
  {
    unsigned int count = this->repeated_int64_value_size();
    total_size += 1UL * count;
    for (unsigned int i = 0; i < count; i++) {
      total_size +=
        ::google::protobuf::internal::WireFormatLite::MessageSizeNoVirtual(
          this->repeated_int64_value(i));
    }
  }

  // repeated .google.protobuf.UInt32Value repeated_uint32_value = 14;
  {
    unsigned int count = this->repeated_uint32_value_size();
    total_size += 1UL * count;
    for (unsigned int i = 0; i < count; i++) {
      total_size +=
        ::google::protobuf::internal::WireFormatLite::MessageSizeNoVirtual(
          this->repeated_uint32_value(i));
    }
  }

  // repeated .google.protobuf.UInt64Value repeated_uint64_value = 15;
  {
    unsigned int count = this->repeated_uint64_value_size();
    total_size += 1UL * count;
    for (unsigned int i = 0; i < count; i++) {
      total_size +=
        ::google::protobuf::internal::WireFormatLite::MessageSizeNoVirtual(
          this->repeated_uint64_value(i));
    }
  }

  // repeated .google.protobuf.FloatValue repeated_float_value = 16;
  {
    unsigned int count = this->repeated_float_value_size();
    total_size += 2UL * count;
    for (unsigned int i = 0; i < count; i++) {
      total_size +=
        ::google::protobuf::internal::WireFormatLite::MessageSizeNoVirtual(
          this->repeated_float_value(i));
    }
  }

  // repeated .google.protobuf.DoubleValue repeated_double_value = 17;
  {
    unsigned int count = this->repeated_double_value_size();
    total_size += 2UL * count;
    for (unsigned int i = 0; i < count; i++) {
      total_size +=
        ::google::protobuf::internal::WireFormatLite::MessageSizeNoVirtual(
          this->repeated_double_value(i));
    }
  }

  // repeated .google.protobuf.StringValue repeated_string_value = 18;
  {
    unsigned int count = this->repeated_string_value_size();
    total_size += 2UL * count;
    for (unsigned int i = 0; i < count; i++) {
      total_size +=
        ::google::protobuf::internal::WireFormatLite::MessageSizeNoVirtual(
          this->repeated_string_value(i));
    }
  }

  // repeated .google.protobuf.BytesValue repeated_bytes_value = 19;
  {
    unsigned int count = this->repeated_bytes_value_size();
    total_size += 2UL * count;
    for (unsigned int i = 0; i < count; i++) {
      total_size +=
        ::google::protobuf::internal::WireFormatLite::MessageSizeNoVirtual(
          this->repeated_bytes_value(i));
    }
  }

  int cached_size = ::google::protobuf::internal::ToCachedSize(total_size);
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = cached_size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
  return total_size;
}

void TestWrapper::MergeFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_merge_from_start:proto3.TestWrapper)
  if (GOOGLE_PREDICT_FALSE(&from == this)) MergeFromFail(__LINE__);
  const TestWrapper* source =
      ::google::protobuf::internal::DynamicCastToGenerated<const TestWrapper>(
          &from);
  if (source == NULL) {
  // @@protoc_insertion_point(generalized_merge_from_cast_fail:proto3.TestWrapper)
    ::google::protobuf::internal::ReflectionOps::Merge(from, this);
  } else {
  // @@protoc_insertion_point(generalized_merge_from_cast_success:proto3.TestWrapper)
    UnsafeMergeFrom(*source);
  }
}

void TestWrapper::MergeFrom(const TestWrapper& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:proto3.TestWrapper)
  if (GOOGLE_PREDICT_TRUE(&from != this)) {
    UnsafeMergeFrom(from);
  } else {
    MergeFromFail(__LINE__);
  }
}

void TestWrapper::UnsafeMergeFrom(const TestWrapper& from) {
  GOOGLE_DCHECK(&from != this);
  repeated_bool_value_.MergeFrom(from.repeated_bool_value_);
  repeated_int32_value_.MergeFrom(from.repeated_int32_value_);
  repeated_int64_value_.MergeFrom(from.repeated_int64_value_);
  repeated_uint32_value_.MergeFrom(from.repeated_uint32_value_);
  repeated_uint64_value_.MergeFrom(from.repeated_uint64_value_);
  repeated_float_value_.MergeFrom(from.repeated_float_value_);
  repeated_double_value_.MergeFrom(from.repeated_double_value_);
  repeated_string_value_.MergeFrom(from.repeated_string_value_);
  repeated_bytes_value_.MergeFrom(from.repeated_bytes_value_);
  if (from.has_bool_value()) {
    mutable_bool_value()->::google::protobuf::BoolValue::MergeFrom(from.bool_value());
  }
  if (from.has_int32_value()) {
    mutable_int32_value()->::google::protobuf::Int32Value::MergeFrom(from.int32_value());
  }
  if (from.has_int64_value()) {
    mutable_int64_value()->::google::protobuf::Int64Value::MergeFrom(from.int64_value());
  }
  if (from.has_uint32_value()) {
    mutable_uint32_value()->::google::protobuf::UInt32Value::MergeFrom(from.uint32_value());
  }
  if (from.has_uint64_value()) {
    mutable_uint64_value()->::google::protobuf::UInt64Value::MergeFrom(from.uint64_value());
  }
  if (from.has_float_value()) {
    mutable_float_value()->::google::protobuf::FloatValue::MergeFrom(from.float_value());
  }
  if (from.has_double_value()) {
    mutable_double_value()->::google::protobuf::DoubleValue::MergeFrom(from.double_value());
  }
  if (from.has_string_value()) {
    mutable_string_value()->::google::protobuf::StringValue::MergeFrom(from.string_value());
  }
  if (from.has_bytes_value()) {
    mutable_bytes_value()->::google::protobuf::BytesValue::MergeFrom(from.bytes_value());
  }
}

void TestWrapper::CopyFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_copy_from_start:proto3.TestWrapper)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

void TestWrapper::CopyFrom(const TestWrapper& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:proto3.TestWrapper)
  if (&from == this) return;
  Clear();
  UnsafeMergeFrom(from);
}

bool TestWrapper::IsInitialized() const {

  return true;
}

void TestWrapper::Swap(TestWrapper* other) {
  if (other == this) return;
  InternalSwap(other);
}
void TestWrapper::InternalSwap(TestWrapper* other) {
  std::swap(bool_value_, other->bool_value_);
  std::swap(int32_value_, other->int32_value_);
  std::swap(int64_value_, other->int64_value_);
  std::swap(uint32_value_, other->uint32_value_);
  std::swap(uint64_value_, other->uint64_value_);
  std::swap(float_value_, other->float_value_);
  std::swap(double_value_, other->double_value_);
  std::swap(string_value_, other->string_value_);
  std::swap(bytes_value_, other->bytes_value_);
  repeated_bool_value_.UnsafeArenaSwap(&other->repeated_bool_value_);
  repeated_int32_value_.UnsafeArenaSwap(&other->repeated_int32_value_);
  repeated_int64_value_.UnsafeArenaSwap(&other->repeated_int64_value_);
  repeated_uint32_value_.UnsafeArenaSwap(&other->repeated_uint32_value_);
  repeated_uint64_value_.UnsafeArenaSwap(&other->repeated_uint64_value_);
  repeated_float_value_.UnsafeArenaSwap(&other->repeated_float_value_);
  repeated_double_value_.UnsafeArenaSwap(&other->repeated_double_value_);
  repeated_string_value_.UnsafeArenaSwap(&other->repeated_string_value_);
  repeated_bytes_value_.UnsafeArenaSwap(&other->repeated_bytes_value_);
  _internal_metadata_.Swap(&other->_internal_metadata_);
  std::swap(_cached_size_, other->_cached_size_);
}

::google::protobuf::Metadata TestWrapper::GetMetadata() const {
  protobuf_AssignDescriptorsOnce();
  ::google::protobuf::Metadata metadata;
  metadata.descriptor = TestWrapper_descriptor_;
  metadata.reflection = TestWrapper_reflection_;
  return metadata;
}

#if PROTOBUF_INLINE_NOT_IN_HEADERS
// TestWrapper

// optional .google.protobuf.BoolValue bool_value = 1;
bool TestWrapper::has_bool_value() const {
  return this != internal_default_instance() && bool_value_ != NULL;
}
void TestWrapper::clear_bool_value() {
  if (GetArenaNoVirtual() == NULL && bool_value_ != NULL) delete bool_value_;
  bool_value_ = NULL;
}
const ::google::protobuf::BoolValue& TestWrapper::bool_value() const {
  // @@protoc_insertion_point(field_get:proto3.TestWrapper.bool_value)
  return bool_value_ != NULL ? *bool_value_
                         : *::google::protobuf::BoolValue::internal_default_instance();
}
::google::protobuf::BoolValue* TestWrapper::mutable_bool_value() {
  
  if (bool_value_ == NULL) {
    bool_value_ = new ::google::protobuf::BoolValue;
  }
  // @@protoc_insertion_point(field_mutable:proto3.TestWrapper.bool_value)
  return bool_value_;
}
::google::protobuf::BoolValue* TestWrapper::release_bool_value() {
  // @@protoc_insertion_point(field_release:proto3.TestWrapper.bool_value)
  
  ::google::protobuf::BoolValue* temp = bool_value_;
  bool_value_ = NULL;
  return temp;
}
void TestWrapper::set_allocated_bool_value(::google::protobuf::BoolValue* bool_value) {
  delete bool_value_;
  if (bool_value != NULL && bool_value->GetArena() != NULL) {
    ::google::protobuf::BoolValue* new_bool_value = new ::google::protobuf::BoolValue;
    new_bool_value->CopyFrom(*bool_value);
    bool_value = new_bool_value;
  }
  bool_value_ = bool_value;
  if (bool_value) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_set_allocated:proto3.TestWrapper.bool_value)
}

// optional .google.protobuf.Int32Value int32_value = 2;
bool TestWrapper::has_int32_value() const {
  return this != internal_default_instance() && int32_value_ != NULL;
}
void TestWrapper::clear_int32_value() {
  if (GetArenaNoVirtual() == NULL && int32_value_ != NULL) delete int32_value_;
  int32_value_ = NULL;
}
const ::google::protobuf::Int32Value& TestWrapper::int32_value() const {
  // @@protoc_insertion_point(field_get:proto3.TestWrapper.int32_value)
  return int32_value_ != NULL ? *int32_value_
                         : *::google::protobuf::Int32Value::internal_default_instance();
}
::google::protobuf::Int32Value* TestWrapper::mutable_int32_value() {
  
  if (int32_value_ == NULL) {
    int32_value_ = new ::google::protobuf::Int32Value;
  }
  // @@protoc_insertion_point(field_mutable:proto3.TestWrapper.int32_value)
  return int32_value_;
}
::google::protobuf::Int32Value* TestWrapper::release_int32_value() {
  // @@protoc_insertion_point(field_release:proto3.TestWrapper.int32_value)
  
  ::google::protobuf::Int32Value* temp = int32_value_;
  int32_value_ = NULL;
  return temp;
}
void TestWrapper::set_allocated_int32_value(::google::protobuf::Int32Value* int32_value) {
  delete int32_value_;
  if (int32_value != NULL && int32_value->GetArena() != NULL) {
    ::google::protobuf::Int32Value* new_int32_value = new ::google::protobuf::Int32Value;
    new_int32_value->CopyFrom(*int32_value);
    int32_value = new_int32_value;
  }
  int32_value_ = int32_value;
  if (int32_value) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_set_allocated:proto3.TestWrapper.int32_value)
}

// optional .google.protobuf.Int64Value int64_value = 3;
bool TestWrapper::has_int64_value() const {
  return this != internal_default_instance() && int64_value_ != NULL;
}
void TestWrapper::clear_int64_value() {
  if (GetArenaNoVirtual() == NULL && int64_value_ != NULL) delete int64_value_;
  int64_value_ = NULL;
}
const ::google::protobuf::Int64Value& TestWrapper::int64_value() const {
  // @@protoc_insertion_point(field_get:proto3.TestWrapper.int64_value)
  return int64_value_ != NULL ? *int64_value_
                         : *::google::protobuf::Int64Value::internal_default_instance();
}
::google::protobuf::Int64Value* TestWrapper::mutable_int64_value() {
  
  if (int64_value_ == NULL) {
    int64_value_ = new ::google::protobuf::Int64Value;
  }
  // @@protoc_insertion_point(field_mutable:proto3.TestWrapper.int64_value)
  return int64_value_;
}
::google::protobuf::Int64Value* TestWrapper::release_int64_value() {
  // @@protoc_insertion_point(field_release:proto3.TestWrapper.int64_value)
  
  ::google::protobuf::Int64Value* temp = int64_value_;
  int64_value_ = NULL;
  return temp;
}
void TestWrapper::set_allocated_int64_value(::google::protobuf::Int64Value* int64_value) {
  delete int64_value_;
  if (int64_value != NULL && int64_value->GetArena() != NULL) {
    ::google::protobuf::Int64Value* new_int64_value = new ::google::protobuf::Int64Value;
    new_int64_value->CopyFrom(*int64_value);
    int64_value = new_int64_value;
  }
  int64_value_ = int64_value;
  if (int64_value) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_set_allocated:proto3.TestWrapper.int64_value)
}

// optional .google.protobuf.UInt32Value uint32_value = 4;
bool TestWrapper::has_uint32_value() const {
  return this != internal_default_instance() && uint32_value_ != NULL;
}
void TestWrapper::clear_uint32_value() {
  if (GetArenaNoVirtual() == NULL && uint32_value_ != NULL) delete uint32_value_;
  uint32_value_ = NULL;
}
const ::google::protobuf::UInt32Value& TestWrapper::uint32_value() const {
  // @@protoc_insertion_point(field_get:proto3.TestWrapper.uint32_value)
  return uint32_value_ != NULL ? *uint32_value_
                         : *::google::protobuf::UInt32Value::internal_default_instance();
}
::google::protobuf::UInt32Value* TestWrapper::mutable_uint32_value() {
  
  if (uint32_value_ == NULL) {
    uint32_value_ = new ::google::protobuf::UInt32Value;
  }
  // @@protoc_insertion_point(field_mutable:proto3.TestWrapper.uint32_value)
  return uint32_value_;
}
::google::protobuf::UInt32Value* TestWrapper::release_uint32_value() {
  // @@protoc_insertion_point(field_release:proto3.TestWrapper.uint32_value)
  
  ::google::protobuf::UInt32Value* temp = uint32_value_;
  uint32_value_ = NULL;
  return temp;
}
void TestWrapper::set_allocated_uint32_value(::google::protobuf::UInt32Value* uint32_value) {
  delete uint32_value_;
  if (uint32_value != NULL && uint32_value->GetArena() != NULL) {
    ::google::protobuf::UInt32Value* new_uint32_value = new ::google::protobuf::UInt32Value;
    new_uint32_value->CopyFrom(*uint32_value);
    uint32_value = new_uint32_value;
  }
  uint32_value_ = uint32_value;
  if (uint32_value) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_set_allocated:proto3.TestWrapper.uint32_value)
}

// optional .google.protobuf.UInt64Value uint64_value = 5;
bool TestWrapper::has_uint64_value() const {
  return this != internal_default_instance() && uint64_value_ != NULL;
}
void TestWrapper::clear_uint64_value() {
  if (GetArenaNoVirtual() == NULL && uint64_value_ != NULL) delete uint64_value_;
  uint64_value_ = NULL;
}
const ::google::protobuf::UInt64Value& TestWrapper::uint64_value() const {
  // @@protoc_insertion_point(field_get:proto3.TestWrapper.uint64_value)
  return uint64_value_ != NULL ? *uint64_value_
                         : *::google::protobuf::UInt64Value::internal_default_instance();
}
::google::protobuf::UInt64Value* TestWrapper::mutable_uint64_value() {
  
  if (uint64_value_ == NULL) {
    uint64_value_ = new ::google::protobuf::UInt64Value;
  }
  // @@protoc_insertion_point(field_mutable:proto3.TestWrapper.uint64_value)
  return uint64_value_;
}
::google::protobuf::UInt64Value* TestWrapper::release_uint64_value() {
  // @@protoc_insertion_point(field_release:proto3.TestWrapper.uint64_value)
  
  ::google::protobuf::UInt64Value* temp = uint64_value_;
  uint64_value_ = NULL;
  return temp;
}
void TestWrapper::set_allocated_uint64_value(::google::protobuf::UInt64Value* uint64_value) {
  delete uint64_value_;
  if (uint64_value != NULL && uint64_value->GetArena() != NULL) {
    ::google::protobuf::UInt64Value* new_uint64_value = new ::google::protobuf::UInt64Value;
    new_uint64_value->CopyFrom(*uint64_value);
    uint64_value = new_uint64_value;
  }
  uint64_value_ = uint64_value;
  if (uint64_value) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_set_allocated:proto3.TestWrapper.uint64_value)
}

// optional .google.protobuf.FloatValue float_value = 6;
bool TestWrapper::has_float_value() const {
  return this != internal_default_instance() && float_value_ != NULL;
}
void TestWrapper::clear_float_value() {
  if (GetArenaNoVirtual() == NULL && float_value_ != NULL) delete float_value_;
  float_value_ = NULL;
}
const ::google::protobuf::FloatValue& TestWrapper::float_value() const {
  // @@protoc_insertion_point(field_get:proto3.TestWrapper.float_value)
  return float_value_ != NULL ? *float_value_
                         : *::google::protobuf::FloatValue::internal_default_instance();
}
::google::protobuf::FloatValue* TestWrapper::mutable_float_value() {
  
  if (float_value_ == NULL) {
    float_value_ = new ::google::protobuf::FloatValue;
  }
  // @@protoc_insertion_point(field_mutable:proto3.TestWrapper.float_value)
  return float_value_;
}
::google::protobuf::FloatValue* TestWrapper::release_float_value() {
  // @@protoc_insertion_point(field_release:proto3.TestWrapper.float_value)
  
  ::google::protobuf::FloatValue* temp = float_value_;
  float_value_ = NULL;
  return temp;
}
void TestWrapper::set_allocated_float_value(::google::protobuf::FloatValue* float_value) {
  delete float_value_;
  if (float_value != NULL && float_value->GetArena() != NULL) {
    ::google::protobuf::FloatValue* new_float_value = new ::google::protobuf::FloatValue;
    new_float_value->CopyFrom(*float_value);
    float_value = new_float_value;
  }
  float_value_ = float_value;
  if (float_value) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_set_allocated:proto3.TestWrapper.float_value)
}

// optional .google.protobuf.DoubleValue double_value = 7;
bool TestWrapper::has_double_value() const {
  return this != internal_default_instance() && double_value_ != NULL;
}
void TestWrapper::clear_double_value() {
  if (GetArenaNoVirtual() == NULL && double_value_ != NULL) delete double_value_;
  double_value_ = NULL;
}
const ::google::protobuf::DoubleValue& TestWrapper::double_value() const {
  // @@protoc_insertion_point(field_get:proto3.TestWrapper.double_value)
  return double_value_ != NULL ? *double_value_
                         : *::google::protobuf::DoubleValue::internal_default_instance();
}
::google::protobuf::DoubleValue* TestWrapper::mutable_double_value() {
  
  if (double_value_ == NULL) {
    double_value_ = new ::google::protobuf::DoubleValue;
  }
  // @@protoc_insertion_point(field_mutable:proto3.TestWrapper.double_value)
  return double_value_;
}
::google::protobuf::DoubleValue* TestWrapper::release_double_value() {
  // @@protoc_insertion_point(field_release:proto3.TestWrapper.double_value)
  
  ::google::protobuf::DoubleValue* temp = double_value_;
  double_value_ = NULL;
  return temp;
}
void TestWrapper::set_allocated_double_value(::google::protobuf::DoubleValue* double_value) {
  delete double_value_;
  if (double_value != NULL && double_value->GetArena() != NULL) {
    ::google::protobuf::DoubleValue* new_double_value = new ::google::protobuf::DoubleValue;
    new_double_value->CopyFrom(*double_value);
    double_value = new_double_value;
  }
  double_value_ = double_value;
  if (double_value) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_set_allocated:proto3.TestWrapper.double_value)
}

// optional .google.protobuf.StringValue string_value = 8;
bool TestWrapper::has_string_value() const {
  return this != internal_default_instance() && string_value_ != NULL;
}
void TestWrapper::clear_string_value() {
  if (GetArenaNoVirtual() == NULL && string_value_ != NULL) delete string_value_;
  string_value_ = NULL;
}
const ::google::protobuf::StringValue& TestWrapper::string_value() const {
  // @@protoc_insertion_point(field_get:proto3.TestWrapper.string_value)
  return string_value_ != NULL ? *string_value_
                         : *::google::protobuf::StringValue::internal_default_instance();
}
::google::protobuf::StringValue* TestWrapper::mutable_string_value() {
  
  if (string_value_ == NULL) {
    string_value_ = new ::google::protobuf::StringValue;
  }
  // @@protoc_insertion_point(field_mutable:proto3.TestWrapper.string_value)
  return string_value_;
}
::google::protobuf::StringValue* TestWrapper::release_string_value() {
  // @@protoc_insertion_point(field_release:proto3.TestWrapper.string_value)
  
  ::google::protobuf::StringValue* temp = string_value_;
  string_value_ = NULL;
  return temp;
}
void TestWrapper::set_allocated_string_value(::google::protobuf::StringValue* string_value) {
  delete string_value_;
  if (string_value != NULL && string_value->GetArena() != NULL) {
    ::google::protobuf::StringValue* new_string_value = new ::google::protobuf::StringValue;
    new_string_value->CopyFrom(*string_value);
    string_value = new_string_value;
  }
  string_value_ = string_value;
  if (string_value) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_set_allocated:proto3.TestWrapper.string_value)
}

// optional .google.protobuf.BytesValue bytes_value = 9;
bool TestWrapper::has_bytes_value() const {
  return this != internal_default_instance() && bytes_value_ != NULL;
}
void TestWrapper::clear_bytes_value() {
  if (GetArenaNoVirtual() == NULL && bytes_value_ != NULL) delete bytes_value_;
  bytes_value_ = NULL;
}
const ::google::protobuf::BytesValue& TestWrapper::bytes_value() const {
  // @@protoc_insertion_point(field_get:proto3.TestWrapper.bytes_value)
  return bytes_value_ != NULL ? *bytes_value_
                         : *::google::protobuf::BytesValue::internal_default_instance();
}
::google::protobuf::BytesValue* TestWrapper::mutable_bytes_value() {
  
  if (bytes_value_ == NULL) {
    bytes_value_ = new ::google::protobuf::BytesValue;
  }
  // @@protoc_insertion_point(field_mutable:proto3.TestWrapper.bytes_value)
  return bytes_value_;
}
::google::protobuf::BytesValue* TestWrapper::release_bytes_value() {
  // @@protoc_insertion_point(field_release:proto3.TestWrapper.bytes_value)
  
  ::google::protobuf::BytesValue* temp = bytes_value_;
  bytes_value_ = NULL;
  return temp;
}
void TestWrapper::set_allocated_bytes_value(::google::protobuf::BytesValue* bytes_value) {
  delete bytes_value_;
  if (bytes_value != NULL && bytes_value->GetArena() != NULL) {
    ::google::protobuf::BytesValue* new_bytes_value = new ::google::protobuf::BytesValue;
    new_bytes_value->CopyFrom(*bytes_value);
    bytes_value = new_bytes_value;
  }
  bytes_value_ = bytes_value;
  if (bytes_value) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_set_allocated:proto3.TestWrapper.bytes_value)
}

// repeated .google.protobuf.BoolValue repeated_bool_value = 11;
int TestWrapper::repeated_bool_value_size() const {
  return repeated_bool_value_.size();
}
void TestWrapper::clear_repeated_bool_value() {
  repeated_bool_value_.Clear();
}
const ::google::protobuf::BoolValue& TestWrapper::repeated_bool_value(int index) const {
  // @@protoc_insertion_point(field_get:proto3.TestWrapper.repeated_bool_value)
  return repeated_bool_value_.Get(index);
}
::google::protobuf::BoolValue* TestWrapper::mutable_repeated_bool_value(int index) {
  // @@protoc_insertion_point(field_mutable:proto3.TestWrapper.repeated_bool_value)
  return repeated_bool_value_.Mutable(index);
}
::google::protobuf::BoolValue* TestWrapper::add_repeated_bool_value() {
  // @@protoc_insertion_point(field_add:proto3.TestWrapper.repeated_bool_value)
  return repeated_bool_value_.Add();
}
::google::protobuf::RepeatedPtrField< ::google::protobuf::BoolValue >*
TestWrapper::mutable_repeated_bool_value() {
  // @@protoc_insertion_point(field_mutable_list:proto3.TestWrapper.repeated_bool_value)
  return &repeated_bool_value_;
}
const ::google::protobuf::RepeatedPtrField< ::google::protobuf::BoolValue >&
TestWrapper::repeated_bool_value() const {
  // @@protoc_insertion_point(field_list:proto3.TestWrapper.repeated_bool_value)
  return repeated_bool_value_;
}

// repeated .google.protobuf.Int32Value repeated_int32_value = 12;
int TestWrapper::repeated_int32_value_size() const {
  return repeated_int32_value_.size();
}
void TestWrapper::clear_repeated_int32_value() {
  repeated_int32_value_.Clear();
}
const ::google::protobuf::Int32Value& TestWrapper::repeated_int32_value(int index) const {
  // @@protoc_insertion_point(field_get:proto3.TestWrapper.repeated_int32_value)
  return repeated_int32_value_.Get(index);
}
::google::protobuf::Int32Value* TestWrapper::mutable_repeated_int32_value(int index) {
  // @@protoc_insertion_point(field_mutable:proto3.TestWrapper.repeated_int32_value)
  return repeated_int32_value_.Mutable(index);
}
::google::protobuf::Int32Value* TestWrapper::add_repeated_int32_value() {
  // @@protoc_insertion_point(field_add:proto3.TestWrapper.repeated_int32_value)
  return repeated_int32_value_.Add();
}
::google::protobuf::RepeatedPtrField< ::google::protobuf::Int32Value >*
TestWrapper::mutable_repeated_int32_value() {
  // @@protoc_insertion_point(field_mutable_list:proto3.TestWrapper.repeated_int32_value)
  return &repeated_int32_value_;
}
const ::google::protobuf::RepeatedPtrField< ::google::protobuf::Int32Value >&
TestWrapper::repeated_int32_value() const {
  // @@protoc_insertion_point(field_list:proto3.TestWrapper.repeated_int32_value)
  return repeated_int32_value_;
}

// repeated .google.protobuf.Int64Value repeated_int64_value = 13;
int TestWrapper::repeated_int64_value_size() const {
  return repeated_int64_value_.size();
}
void TestWrapper::clear_repeated_int64_value() {
  repeated_int64_value_.Clear();
}
const ::google::protobuf::Int64Value& TestWrapper::repeated_int64_value(int index) const {
  // @@protoc_insertion_point(field_get:proto3.TestWrapper.repeated_int64_value)
  return repeated_int64_value_.Get(index);
}
::google::protobuf::Int64Value* TestWrapper::mutable_repeated_int64_value(int index) {
  // @@protoc_insertion_point(field_mutable:proto3.TestWrapper.repeated_int64_value)
  return repeated_int64_value_.Mutable(index);
}
::google::protobuf::Int64Value* TestWrapper::add_repeated_int64_value() {
  // @@protoc_insertion_point(field_add:proto3.TestWrapper.repeated_int64_value)
  return repeated_int64_value_.Add();
}
::google::protobuf::RepeatedPtrField< ::google::protobuf::Int64Value >*
TestWrapper::mutable_repeated_int64_value() {
  // @@protoc_insertion_point(field_mutable_list:proto3.TestWrapper.repeated_int64_value)
  return &repeated_int64_value_;
}
const ::google::protobuf::RepeatedPtrField< ::google::protobuf::Int64Value >&
TestWrapper::repeated_int64_value() const {
  // @@protoc_insertion_point(field_list:proto3.TestWrapper.repeated_int64_value)
  return repeated_int64_value_;
}

// repeated .google.protobuf.UInt32Value repeated_uint32_value = 14;
int TestWrapper::repeated_uint32_value_size() const {
  return repeated_uint32_value_.size();
}
void TestWrapper::clear_repeated_uint32_value() {
  repeated_uint32_value_.Clear();
}
const ::google::protobuf::UInt32Value& TestWrapper::repeated_uint32_value(int index) const {
  // @@protoc_insertion_point(field_get:proto3.TestWrapper.repeated_uint32_value)
  return repeated_uint32_value_.Get(index);
}
::google::protobuf::UInt32Value* TestWrapper::mutable_repeated_uint32_value(int index) {
  // @@protoc_insertion_point(field_mutable:proto3.TestWrapper.repeated_uint32_value)
  return repeated_uint32_value_.Mutable(index);
}
::google::protobuf::UInt32Value* TestWrapper::add_repeated_uint32_value() {
  // @@protoc_insertion_point(field_add:proto3.TestWrapper.repeated_uint32_value)
  return repeated_uint32_value_.Add();
}
::google::protobuf::RepeatedPtrField< ::google::protobuf::UInt32Value >*
TestWrapper::mutable_repeated_uint32_value() {
  // @@protoc_insertion_point(field_mutable_list:proto3.TestWrapper.repeated_uint32_value)
  return &repeated_uint32_value_;
}
const ::google::protobuf::RepeatedPtrField< ::google::protobuf::UInt32Value >&
TestWrapper::repeated_uint32_value() const {
  // @@protoc_insertion_point(field_list:proto3.TestWrapper.repeated_uint32_value)
  return repeated_uint32_value_;
}

// repeated .google.protobuf.UInt64Value repeated_uint64_value = 15;
int TestWrapper::repeated_uint64_value_size() const {
  return repeated_uint64_value_.size();
}
void TestWrapper::clear_repeated_uint64_value() {
  repeated_uint64_value_.Clear();
}
const ::google::protobuf::UInt64Value& TestWrapper::repeated_uint64_value(int index) const {
  // @@protoc_insertion_point(field_get:proto3.TestWrapper.repeated_uint64_value)
  return repeated_uint64_value_.Get(index);
}
::google::protobuf::UInt64Value* TestWrapper::mutable_repeated_uint64_value(int index) {
  // @@protoc_insertion_point(field_mutable:proto3.TestWrapper.repeated_uint64_value)
  return repeated_uint64_value_.Mutable(index);
}
::google::protobuf::UInt64Value* TestWrapper::add_repeated_uint64_value() {
  // @@protoc_insertion_point(field_add:proto3.TestWrapper.repeated_uint64_value)
  return repeated_uint64_value_.Add();
}
::google::protobuf::RepeatedPtrField< ::google::protobuf::UInt64Value >*
TestWrapper::mutable_repeated_uint64_value() {
  // @@protoc_insertion_point(field_mutable_list:proto3.TestWrapper.repeated_uint64_value)
  return &repeated_uint64_value_;
}
const ::google::protobuf::RepeatedPtrField< ::google::protobuf::UInt64Value >&
TestWrapper::repeated_uint64_value() const {
  // @@protoc_insertion_point(field_list:proto3.TestWrapper.repeated_uint64_value)
  return repeated_uint64_value_;
}

// repeated .google.protobuf.FloatValue repeated_float_value = 16;
int TestWrapper::repeated_float_value_size() const {
  return repeated_float_value_.size();
}
void TestWrapper::clear_repeated_float_value() {
  repeated_float_value_.Clear();
}
const ::google::protobuf::FloatValue& TestWrapper::repeated_float_value(int index) const {
  // @@protoc_insertion_point(field_get:proto3.TestWrapper.repeated_float_value)
  return repeated_float_value_.Get(index);
}
::google::protobuf::FloatValue* TestWrapper::mutable_repeated_float_value(int index) {
  // @@protoc_insertion_point(field_mutable:proto3.TestWrapper.repeated_float_value)
  return repeated_float_value_.Mutable(index);
}
::google::protobuf::FloatValue* TestWrapper::add_repeated_float_value() {
  // @@protoc_insertion_point(field_add:proto3.TestWrapper.repeated_float_value)
  return repeated_float_value_.Add();
}
::google::protobuf::RepeatedPtrField< ::google::protobuf::FloatValue >*
TestWrapper::mutable_repeated_float_value() {
  // @@protoc_insertion_point(field_mutable_list:proto3.TestWrapper.repeated_float_value)
  return &repeated_float_value_;
}
const ::google::protobuf::RepeatedPtrField< ::google::protobuf::FloatValue >&
TestWrapper::repeated_float_value() const {
  // @@protoc_insertion_point(field_list:proto3.TestWrapper.repeated_float_value)
  return repeated_float_value_;
}

// repeated .google.protobuf.DoubleValue repeated_double_value = 17;
int TestWrapper::repeated_double_value_size() const {
  return repeated_double_value_.size();
}
void TestWrapper::clear_repeated_double_value() {
  repeated_double_value_.Clear();
}
const ::google::protobuf::DoubleValue& TestWrapper::repeated_double_value(int index) const {
  // @@protoc_insertion_point(field_get:proto3.TestWrapper.repeated_double_value)
  return repeated_double_value_.Get(index);
}
::google::protobuf::DoubleValue* TestWrapper::mutable_repeated_double_value(int index) {
  // @@protoc_insertion_point(field_mutable:proto3.TestWrapper.repeated_double_value)
  return repeated_double_value_.Mutable(index);
}
::google::protobuf::DoubleValue* TestWrapper::add_repeated_double_value() {
  // @@protoc_insertion_point(field_add:proto3.TestWrapper.repeated_double_value)
  return repeated_double_value_.Add();
}
::google::protobuf::RepeatedPtrField< ::google::protobuf::DoubleValue >*
TestWrapper::mutable_repeated_double_value() {
  // @@protoc_insertion_point(field_mutable_list:proto3.TestWrapper.repeated_double_value)
  return &repeated_double_value_;
}
const ::google::protobuf::RepeatedPtrField< ::google::protobuf::DoubleValue >&
TestWrapper::repeated_double_value() const {
  // @@protoc_insertion_point(field_list:proto3.TestWrapper.repeated_double_value)
  return repeated_double_value_;
}

// repeated .google.protobuf.StringValue repeated_string_value = 18;
int TestWrapper::repeated_string_value_size() const {
  return repeated_string_value_.size();
}
void TestWrapper::clear_repeated_string_value() {
  repeated_string_value_.Clear();
}
const ::google::protobuf::StringValue& TestWrapper::repeated_string_value(int index) const {
  // @@protoc_insertion_point(field_get:proto3.TestWrapper.repeated_string_value)
  return repeated_string_value_.Get(index);
}
::google::protobuf::StringValue* TestWrapper::mutable_repeated_string_value(int index) {
  // @@protoc_insertion_point(field_mutable:proto3.TestWrapper.repeated_string_value)
  return repeated_string_value_.Mutable(index);
}
::google::protobuf::StringValue* TestWrapper::add_repeated_string_value() {
  // @@protoc_insertion_point(field_add:proto3.TestWrapper.repeated_string_value)
  return repeated_string_value_.Add();
}
::google::protobuf::RepeatedPtrField< ::google::protobuf::StringValue >*
TestWrapper::mutable_repeated_string_value() {
  // @@protoc_insertion_point(field_mutable_list:proto3.TestWrapper.repeated_string_value)
  return &repeated_string_value_;
}
const ::google::protobuf::RepeatedPtrField< ::google::protobuf::StringValue >&
TestWrapper::repeated_string_value() const {
  // @@protoc_insertion_point(field_list:proto3.TestWrapper.repeated_string_value)
  return repeated_string_value_;
}

// repeated .google.protobuf.BytesValue repeated_bytes_value = 19;
int TestWrapper::repeated_bytes_value_size() const {
  return repeated_bytes_value_.size();
}
void TestWrapper::clear_repeated_bytes_value() {
  repeated_bytes_value_.Clear();
}
const ::google::protobuf::BytesValue& TestWrapper::repeated_bytes_value(int index) const {
  // @@protoc_insertion_point(field_get:proto3.TestWrapper.repeated_bytes_value)
  return repeated_bytes_value_.Get(index);
}
::google::protobuf::BytesValue* TestWrapper::mutable_repeated_bytes_value(int index) {
  // @@protoc_insertion_point(field_mutable:proto3.TestWrapper.repeated_bytes_value)
  return repeated_bytes_value_.Mutable(index);
}
::google::protobuf::BytesValue* TestWrapper::add_repeated_bytes_value() {
  // @@protoc_insertion_point(field_add:proto3.TestWrapper.repeated_bytes_value)
  return repeated_bytes_value_.Add();
}
::google::protobuf::RepeatedPtrField< ::google::protobuf::BytesValue >*
TestWrapper::mutable_repeated_bytes_value() {
  // @@protoc_insertion_point(field_mutable_list:proto3.TestWrapper.repeated_bytes_value)
  return &repeated_bytes_value_;
}
const ::google::protobuf::RepeatedPtrField< ::google::protobuf::BytesValue >&
TestWrapper::repeated_bytes_value() const {
  // @@protoc_insertion_point(field_list:proto3.TestWrapper.repeated_bytes_value)
  return repeated_bytes_value_;
}

inline const TestWrapper* TestWrapper::internal_default_instance() {
  return &TestWrapper_default_instance_.get();
}
#endif  // PROTOBUF_INLINE_NOT_IN_HEADERS

// ===================================================================

#if !defined(_MSC_VER) || _MSC_VER >= 1900
const int TestTimestamp::kValueFieldNumber;
const int TestTimestamp::kRepeatedValueFieldNumber;
#endif  // !defined(_MSC_VER) || _MSC_VER >= 1900

TestTimestamp::TestTimestamp()
  : ::google::protobuf::Message(), _internal_metadata_(NULL) {
  if (this != internal_default_instance()) protobuf_InitDefaults_google_2fprotobuf_2futil_2fjson_5fformat_5fproto3_2eproto();
  SharedCtor();
  // @@protoc_insertion_point(constructor:proto3.TestTimestamp)
}

void TestTimestamp::InitAsDefaultInstance() {
  value_ = const_cast< ::google::protobuf::Timestamp*>(
      ::google::protobuf::Timestamp::internal_default_instance());
}

TestTimestamp::TestTimestamp(const TestTimestamp& from)
  : ::google::protobuf::Message(),
    _internal_metadata_(NULL) {
  SharedCtor();
  UnsafeMergeFrom(from);
  // @@protoc_insertion_point(copy_constructor:proto3.TestTimestamp)
}

void TestTimestamp::SharedCtor() {
  value_ = NULL;
  _cached_size_ = 0;
}

TestTimestamp::~TestTimestamp() {
  // @@protoc_insertion_point(destructor:proto3.TestTimestamp)
  SharedDtor();
}

void TestTimestamp::SharedDtor() {
  if (this != &TestTimestamp_default_instance_.get()) {
    delete value_;
  }
}

void TestTimestamp::SetCachedSize(int size) const {
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
}
const ::google::protobuf::Descriptor* TestTimestamp::descriptor() {
  protobuf_AssignDescriptorsOnce();
  return TestTimestamp_descriptor_;
}

const TestTimestamp& TestTimestamp::default_instance() {
  protobuf_InitDefaults_google_2fprotobuf_2futil_2fjson_5fformat_5fproto3_2eproto();
  return *internal_default_instance();
}

::google::protobuf::internal::ExplicitlyConstructed<TestTimestamp> TestTimestamp_default_instance_;

TestTimestamp* TestTimestamp::New(::google::protobuf::Arena* arena) const {
  TestTimestamp* n = new TestTimestamp;
  if (arena != NULL) {
    arena->Own(n);
  }
  return n;
}

void TestTimestamp::Clear() {
// @@protoc_insertion_point(message_clear_start:proto3.TestTimestamp)
  if (GetArenaNoVirtual() == NULL && value_ != NULL) delete value_;
  value_ = NULL;
  repeated_value_.Clear();
}

bool TestTimestamp::MergePartialFromCodedStream(
    ::google::protobuf::io::CodedInputStream* input) {
#define DO_(EXPRESSION) if (!GOOGLE_PREDICT_TRUE(EXPRESSION)) goto failure
  ::google::protobuf::uint32 tag;
  // @@protoc_insertion_point(parse_start:proto3.TestTimestamp)
  for (;;) {
    ::std::pair< ::google::protobuf::uint32, bool> p = input->ReadTagWithCutoff(127);
    tag = p.first;
    if (!p.second) goto handle_unusual;
    switch (::google::protobuf::internal::WireFormatLite::GetTagFieldNumber(tag)) {
      // optional .google.protobuf.Timestamp value = 1;
      case 1: {
        if (tag == 10) {
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessageNoVirtual(
               input, mutable_value()));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(18)) goto parse_repeated_value;
        break;
      }

      // repeated .google.protobuf.Timestamp repeated_value = 2;
      case 2: {
        if (tag == 18) {
         parse_repeated_value:
          DO_(input->IncrementRecursionDepth());
         parse_loop_repeated_value:
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessageNoVirtualNoRecursionDepth(
                input, add_repeated_value()));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(18)) goto parse_loop_repeated_value;
        input->UnsafeDecrementRecursionDepth();
        if (input->ExpectAtEnd()) goto success;
        break;
      }

      default: {
      handle_unusual:
        if (tag == 0 ||
            ::google::protobuf::internal::WireFormatLite::GetTagWireType(tag) ==
            ::google::protobuf::internal::WireFormatLite::WIRETYPE_END_GROUP) {
          goto success;
        }
        DO_(::google::protobuf::internal::WireFormatLite::SkipField(input, tag));
        break;
      }
    }
  }
success:
  // @@protoc_insertion_point(parse_success:proto3.TestTimestamp)
  return true;
failure:
  // @@protoc_insertion_point(parse_failure:proto3.TestTimestamp)
  return false;
#undef DO_
}

void TestTimestamp::SerializeWithCachedSizes(
    ::google::protobuf::io::CodedOutputStream* output) const {
  // @@protoc_insertion_point(serialize_start:proto3.TestTimestamp)
  // optional .google.protobuf.Timestamp value = 1;
  if (this->has_value()) {
    ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
      1, *this->value_, output);
  }

  // repeated .google.protobuf.Timestamp repeated_value = 2;
  for (unsigned int i = 0, n = this->repeated_value_size(); i < n; i++) {
    ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
      2, this->repeated_value(i), output);
  }

  // @@protoc_insertion_point(serialize_end:proto3.TestTimestamp)
}

::google::protobuf::uint8* TestTimestamp::InternalSerializeWithCachedSizesToArray(
    bool deterministic, ::google::protobuf::uint8* target) const {
  (void)deterministic; // Unused
  // @@protoc_insertion_point(serialize_to_array_start:proto3.TestTimestamp)
  // optional .google.protobuf.Timestamp value = 1;
  if (this->has_value()) {
    target = ::google::protobuf::internal::WireFormatLite::
      InternalWriteMessageNoVirtualToArray(
        1, *this->value_, false, target);
  }

  // repeated .google.protobuf.Timestamp repeated_value = 2;
  for (unsigned int i = 0, n = this->repeated_value_size(); i < n; i++) {
    target = ::google::protobuf::internal::WireFormatLite::
      InternalWriteMessageNoVirtualToArray(
        2, this->repeated_value(i), false, target);
  }

  // @@protoc_insertion_point(serialize_to_array_end:proto3.TestTimestamp)
  return target;
}

size_t TestTimestamp::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:proto3.TestTimestamp)
  size_t total_size = 0;

  // optional .google.protobuf.Timestamp value = 1;
  if (this->has_value()) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::MessageSizeNoVirtual(
        *this->value_);
  }

  // repeated .google.protobuf.Timestamp repeated_value = 2;
  {
    unsigned int count = this->repeated_value_size();
    total_size += 1UL * count;
    for (unsigned int i = 0; i < count; i++) {
      total_size +=
        ::google::protobuf::internal::WireFormatLite::MessageSizeNoVirtual(
          this->repeated_value(i));
    }
  }

  int cached_size = ::google::protobuf::internal::ToCachedSize(total_size);
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = cached_size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
  return total_size;
}

void TestTimestamp::MergeFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_merge_from_start:proto3.TestTimestamp)
  if (GOOGLE_PREDICT_FALSE(&from == this)) MergeFromFail(__LINE__);
  const TestTimestamp* source =
      ::google::protobuf::internal::DynamicCastToGenerated<const TestTimestamp>(
          &from);
  if (source == NULL) {
  // @@protoc_insertion_point(generalized_merge_from_cast_fail:proto3.TestTimestamp)
    ::google::protobuf::internal::ReflectionOps::Merge(from, this);
  } else {
  // @@protoc_insertion_point(generalized_merge_from_cast_success:proto3.TestTimestamp)
    UnsafeMergeFrom(*source);
  }
}

void TestTimestamp::MergeFrom(const TestTimestamp& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:proto3.TestTimestamp)
  if (GOOGLE_PREDICT_TRUE(&from != this)) {
    UnsafeMergeFrom(from);
  } else {
    MergeFromFail(__LINE__);
  }
}

void TestTimestamp::UnsafeMergeFrom(const TestTimestamp& from) {
  GOOGLE_DCHECK(&from != this);
  repeated_value_.MergeFrom(from.repeated_value_);
  if (from.has_value()) {
    mutable_value()->::google::protobuf::Timestamp::MergeFrom(from.value());
  }
}

void TestTimestamp::CopyFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_copy_from_start:proto3.TestTimestamp)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

void TestTimestamp::CopyFrom(const TestTimestamp& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:proto3.TestTimestamp)
  if (&from == this) return;
  Clear();
  UnsafeMergeFrom(from);
}

bool TestTimestamp::IsInitialized() const {

  return true;
}

void TestTimestamp::Swap(TestTimestamp* other) {
  if (other == this) return;
  InternalSwap(other);
}
void TestTimestamp::InternalSwap(TestTimestamp* other) {
  std::swap(value_, other->value_);
  repeated_value_.UnsafeArenaSwap(&other->repeated_value_);
  _internal_metadata_.Swap(&other->_internal_metadata_);
  std::swap(_cached_size_, other->_cached_size_);
}

::google::protobuf::Metadata TestTimestamp::GetMetadata() const {
  protobuf_AssignDescriptorsOnce();
  ::google::protobuf::Metadata metadata;
  metadata.descriptor = TestTimestamp_descriptor_;
  metadata.reflection = TestTimestamp_reflection_;
  return metadata;
}

#if PROTOBUF_INLINE_NOT_IN_HEADERS
// TestTimestamp

// optional .google.protobuf.Timestamp value = 1;
bool TestTimestamp::has_value() const {
  return this != internal_default_instance() && value_ != NULL;
}
void TestTimestamp::clear_value() {
  if (GetArenaNoVirtual() == NULL && value_ != NULL) delete value_;
  value_ = NULL;
}
const ::google::protobuf::Timestamp& TestTimestamp::value() const {
  // @@protoc_insertion_point(field_get:proto3.TestTimestamp.value)
  return value_ != NULL ? *value_
                         : *::google::protobuf::Timestamp::internal_default_instance();
}
::google::protobuf::Timestamp* TestTimestamp::mutable_value() {
  
  if (value_ == NULL) {
    value_ = new ::google::protobuf::Timestamp;
  }
  // @@protoc_insertion_point(field_mutable:proto3.TestTimestamp.value)
  return value_;
}
::google::protobuf::Timestamp* TestTimestamp::release_value() {
  // @@protoc_insertion_point(field_release:proto3.TestTimestamp.value)
  
  ::google::protobuf::Timestamp* temp = value_;
  value_ = NULL;
  return temp;
}
void TestTimestamp::set_allocated_value(::google::protobuf::Timestamp* value) {
  delete value_;
  if (value != NULL && value->GetArena() != NULL) {
    ::google::protobuf::Timestamp* new_value = new ::google::protobuf::Timestamp;
    new_value->CopyFrom(*value);
    value = new_value;
  }
  value_ = value;
  if (value) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_set_allocated:proto3.TestTimestamp.value)
}

// repeated .google.protobuf.Timestamp repeated_value = 2;
int TestTimestamp::repeated_value_size() const {
  return repeated_value_.size();
}
void TestTimestamp::clear_repeated_value() {
  repeated_value_.Clear();
}
const ::google::protobuf::Timestamp& TestTimestamp::repeated_value(int index) const {
  // @@protoc_insertion_point(field_get:proto3.TestTimestamp.repeated_value)
  return repeated_value_.Get(index);
}
::google::protobuf::Timestamp* TestTimestamp::mutable_repeated_value(int index) {
  // @@protoc_insertion_point(field_mutable:proto3.TestTimestamp.repeated_value)
  return repeated_value_.Mutable(index);
}
::google::protobuf::Timestamp* TestTimestamp::add_repeated_value() {
  // @@protoc_insertion_point(field_add:proto3.TestTimestamp.repeated_value)
  return repeated_value_.Add();
}
::google::protobuf::RepeatedPtrField< ::google::protobuf::Timestamp >*
TestTimestamp::mutable_repeated_value() {
  // @@protoc_insertion_point(field_mutable_list:proto3.TestTimestamp.repeated_value)
  return &repeated_value_;
}
const ::google::protobuf::RepeatedPtrField< ::google::protobuf::Timestamp >&
TestTimestamp::repeated_value() const {
  // @@protoc_insertion_point(field_list:proto3.TestTimestamp.repeated_value)
  return repeated_value_;
}

inline const TestTimestamp* TestTimestamp::internal_default_instance() {
  return &TestTimestamp_default_instance_.get();
}
#endif  // PROTOBUF_INLINE_NOT_IN_HEADERS

// ===================================================================

#if !defined(_MSC_VER) || _MSC_VER >= 1900
const int TestDuration::kValueFieldNumber;
const int TestDuration::kRepeatedValueFieldNumber;
#endif  // !defined(_MSC_VER) || _MSC_VER >= 1900

TestDuration::TestDuration()
  : ::google::protobuf::Message(), _internal_metadata_(NULL) {
  if (this != internal_default_instance()) protobuf_InitDefaults_google_2fprotobuf_2futil_2fjson_5fformat_5fproto3_2eproto();
  SharedCtor();
  // @@protoc_insertion_point(constructor:proto3.TestDuration)
}

void TestDuration::InitAsDefaultInstance() {
  value_ = const_cast< ::google::protobuf::Duration*>(
      ::google::protobuf::Duration::internal_default_instance());
}

TestDuration::TestDuration(const TestDuration& from)
  : ::google::protobuf::Message(),
    _internal_metadata_(NULL) {
  SharedCtor();
  UnsafeMergeFrom(from);
  // @@protoc_insertion_point(copy_constructor:proto3.TestDuration)
}

void TestDuration::SharedCtor() {
  value_ = NULL;
  _cached_size_ = 0;
}

TestDuration::~TestDuration() {
  // @@protoc_insertion_point(destructor:proto3.TestDuration)
  SharedDtor();
}

void TestDuration::SharedDtor() {
  if (this != &TestDuration_default_instance_.get()) {
    delete value_;
  }
}

void TestDuration::SetCachedSize(int size) const {
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
}
const ::google::protobuf::Descriptor* TestDuration::descriptor() {
  protobuf_AssignDescriptorsOnce();
  return TestDuration_descriptor_;
}

const TestDuration& TestDuration::default_instance() {
  protobuf_InitDefaults_google_2fprotobuf_2futil_2fjson_5fformat_5fproto3_2eproto();
  return *internal_default_instance();
}

::google::protobuf::internal::ExplicitlyConstructed<TestDuration> TestDuration_default_instance_;

TestDuration* TestDuration::New(::google::protobuf::Arena* arena) const {
  TestDuration* n = new TestDuration;
  if (arena != NULL) {
    arena->Own(n);
  }
  return n;
}

void TestDuration::Clear() {
// @@protoc_insertion_point(message_clear_start:proto3.TestDuration)
  if (GetArenaNoVirtual() == NULL && value_ != NULL) delete value_;
  value_ = NULL;
  repeated_value_.Clear();
}

bool TestDuration::MergePartialFromCodedStream(
    ::google::protobuf::io::CodedInputStream* input) {
#define DO_(EXPRESSION) if (!GOOGLE_PREDICT_TRUE(EXPRESSION)) goto failure
  ::google::protobuf::uint32 tag;
  // @@protoc_insertion_point(parse_start:proto3.TestDuration)
  for (;;) {
    ::std::pair< ::google::protobuf::uint32, bool> p = input->ReadTagWithCutoff(127);
    tag = p.first;
    if (!p.second) goto handle_unusual;
    switch (::google::protobuf::internal::WireFormatLite::GetTagFieldNumber(tag)) {
      // optional .google.protobuf.Duration value = 1;
      case 1: {
        if (tag == 10) {
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessageNoVirtual(
               input, mutable_value()));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(18)) goto parse_repeated_value;
        break;
      }

      // repeated .google.protobuf.Duration repeated_value = 2;
      case 2: {
        if (tag == 18) {
         parse_repeated_value:
          DO_(input->IncrementRecursionDepth());
         parse_loop_repeated_value:
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessageNoVirtualNoRecursionDepth(
                input, add_repeated_value()));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(18)) goto parse_loop_repeated_value;
        input->UnsafeDecrementRecursionDepth();
        if (input->ExpectAtEnd()) goto success;
        break;
      }

      default: {
      handle_unusual:
        if (tag == 0 ||
            ::google::protobuf::internal::WireFormatLite::GetTagWireType(tag) ==
            ::google::protobuf::internal::WireFormatLite::WIRETYPE_END_GROUP) {
          goto success;
        }
        DO_(::google::protobuf::internal::WireFormatLite::SkipField(input, tag));
        break;
      }
    }
  }
success:
  // @@protoc_insertion_point(parse_success:proto3.TestDuration)
  return true;
failure:
  // @@protoc_insertion_point(parse_failure:proto3.TestDuration)
  return false;
#undef DO_
}

void TestDuration::SerializeWithCachedSizes(
    ::google::protobuf::io::CodedOutputStream* output) const {
  // @@protoc_insertion_point(serialize_start:proto3.TestDuration)
  // optional .google.protobuf.Duration value = 1;
  if (this->has_value()) {
    ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
      1, *this->value_, output);
  }

  // repeated .google.protobuf.Duration repeated_value = 2;
  for (unsigned int i = 0, n = this->repeated_value_size(); i < n; i++) {
    ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
      2, this->repeated_value(i), output);
  }

  // @@protoc_insertion_point(serialize_end:proto3.TestDuration)
}

::google::protobuf::uint8* TestDuration::InternalSerializeWithCachedSizesToArray(
    bool deterministic, ::google::protobuf::uint8* target) const {
  (void)deterministic; // Unused
  // @@protoc_insertion_point(serialize_to_array_start:proto3.TestDuration)
  // optional .google.protobuf.Duration value = 1;
  if (this->has_value()) {
    target = ::google::protobuf::internal::WireFormatLite::
      InternalWriteMessageNoVirtualToArray(
        1, *this->value_, false, target);
  }

  // repeated .google.protobuf.Duration repeated_value = 2;
  for (unsigned int i = 0, n = this->repeated_value_size(); i < n; i++) {
    target = ::google::protobuf::internal::WireFormatLite::
      InternalWriteMessageNoVirtualToArray(
        2, this->repeated_value(i), false, target);
  }

  // @@protoc_insertion_point(serialize_to_array_end:proto3.TestDuration)
  return target;
}

size_t TestDuration::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:proto3.TestDuration)
  size_t total_size = 0;

  // optional .google.protobuf.Duration value = 1;
  if (this->has_value()) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::MessageSizeNoVirtual(
        *this->value_);
  }

  // repeated .google.protobuf.Duration repeated_value = 2;
  {
    unsigned int count = this->repeated_value_size();
    total_size += 1UL * count;
    for (unsigned int i = 0; i < count; i++) {
      total_size +=
        ::google::protobuf::internal::WireFormatLite::MessageSizeNoVirtual(
          this->repeated_value(i));
    }
  }

  int cached_size = ::google::protobuf::internal::ToCachedSize(total_size);
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = cached_size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
  return total_size;
}

void TestDuration::MergeFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_merge_from_start:proto3.TestDuration)
  if (GOOGLE_PREDICT_FALSE(&from == this)) MergeFromFail(__LINE__);
  const TestDuration* source =
      ::google::protobuf::internal::DynamicCastToGenerated<const TestDuration>(
          &from);
  if (source == NULL) {
  // @@protoc_insertion_point(generalized_merge_from_cast_fail:proto3.TestDuration)
    ::google::protobuf::internal::ReflectionOps::Merge(from, this);
  } else {
  // @@protoc_insertion_point(generalized_merge_from_cast_success:proto3.TestDuration)
    UnsafeMergeFrom(*source);
  }
}

void TestDuration::MergeFrom(const TestDuration& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:proto3.TestDuration)
  if (GOOGLE_PREDICT_TRUE(&from != this)) {
    UnsafeMergeFrom(from);
  } else {
    MergeFromFail(__LINE__);
  }
}

void TestDuration::UnsafeMergeFrom(const TestDuration& from) {
  GOOGLE_DCHECK(&from != this);
  repeated_value_.MergeFrom(from.repeated_value_);
  if (from.has_value()) {
    mutable_value()->::google::protobuf::Duration::MergeFrom(from.value());
  }
}

void TestDuration::CopyFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_copy_from_start:proto3.TestDuration)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

void TestDuration::CopyFrom(const TestDuration& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:proto3.TestDuration)
  if (&from == this) return;
  Clear();
  UnsafeMergeFrom(from);
}

bool TestDuration::IsInitialized() const {

  return true;
}

void TestDuration::Swap(TestDuration* other) {
  if (other == this) return;
  InternalSwap(other);
}
void TestDuration::InternalSwap(TestDuration* other) {
  std::swap(value_, other->value_);
  repeated_value_.UnsafeArenaSwap(&other->repeated_value_);
  _internal_metadata_.Swap(&other->_internal_metadata_);
  std::swap(_cached_size_, other->_cached_size_);
}

::google::protobuf::Metadata TestDuration::GetMetadata() const {
  protobuf_AssignDescriptorsOnce();
  ::google::protobuf::Metadata metadata;
  metadata.descriptor = TestDuration_descriptor_;
  metadata.reflection = TestDuration_reflection_;
  return metadata;
}

#if PROTOBUF_INLINE_NOT_IN_HEADERS
// TestDuration

// optional .google.protobuf.Duration value = 1;
bool TestDuration::has_value() const {
  return this != internal_default_instance() && value_ != NULL;
}
void TestDuration::clear_value() {
  if (GetArenaNoVirtual() == NULL && value_ != NULL) delete value_;
  value_ = NULL;
}
const ::google::protobuf::Duration& TestDuration::value() const {
  // @@protoc_insertion_point(field_get:proto3.TestDuration.value)
  return value_ != NULL ? *value_
                         : *::google::protobuf::Duration::internal_default_instance();
}
::google::protobuf::Duration* TestDuration::mutable_value() {
  
  if (value_ == NULL) {
    value_ = new ::google::protobuf::Duration;
  }
  // @@protoc_insertion_point(field_mutable:proto3.TestDuration.value)
  return value_;
}
::google::protobuf::Duration* TestDuration::release_value() {
  // @@protoc_insertion_point(field_release:proto3.TestDuration.value)
  
  ::google::protobuf::Duration* temp = value_;
  value_ = NULL;
  return temp;
}
void TestDuration::set_allocated_value(::google::protobuf::Duration* value) {
  delete value_;
  if (value != NULL && value->GetArena() != NULL) {
    ::google::protobuf::Duration* new_value = new ::google::protobuf::Duration;
    new_value->CopyFrom(*value);
    value = new_value;
  }
  value_ = value;
  if (value) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_set_allocated:proto3.TestDuration.value)
}

// repeated .google.protobuf.Duration repeated_value = 2;
int TestDuration::repeated_value_size() const {
  return repeated_value_.size();
}
void TestDuration::clear_repeated_value() {
  repeated_value_.Clear();
}
const ::google::protobuf::Duration& TestDuration::repeated_value(int index) const {
  // @@protoc_insertion_point(field_get:proto3.TestDuration.repeated_value)
  return repeated_value_.Get(index);
}
::google::protobuf::Duration* TestDuration::mutable_repeated_value(int index) {
  // @@protoc_insertion_point(field_mutable:proto3.TestDuration.repeated_value)
  return repeated_value_.Mutable(index);
}
::google::protobuf::Duration* TestDuration::add_repeated_value() {
  // @@protoc_insertion_point(field_add:proto3.TestDuration.repeated_value)
  return repeated_value_.Add();
}
::google::protobuf::RepeatedPtrField< ::google::protobuf::Duration >*
TestDuration::mutable_repeated_value() {
  // @@protoc_insertion_point(field_mutable_list:proto3.TestDuration.repeated_value)
  return &repeated_value_;
}
const ::google::protobuf::RepeatedPtrField< ::google::protobuf::Duration >&
TestDuration::repeated_value() const {
  // @@protoc_insertion_point(field_list:proto3.TestDuration.repeated_value)
  return repeated_value_;
}

inline const TestDuration* TestDuration::internal_default_instance() {
  return &TestDuration_default_instance_.get();
}
#endif  // PROTOBUF_INLINE_NOT_IN_HEADERS

// ===================================================================

#if !defined(_MSC_VER) || _MSC_VER >= 1900
const int TestFieldMask::kValueFieldNumber;
#endif  // !defined(_MSC_VER) || _MSC_VER >= 1900

TestFieldMask::TestFieldMask()
  : ::google::protobuf::Message(), _internal_metadata_(NULL) {
  if (this != internal_default_instance()) protobuf_InitDefaults_google_2fprotobuf_2futil_2fjson_5fformat_5fproto3_2eproto();
  SharedCtor();
  // @@protoc_insertion_point(constructor:proto3.TestFieldMask)
}

void TestFieldMask::InitAsDefaultInstance() {
  value_ = const_cast< ::google::protobuf::FieldMask*>(
      ::google::protobuf::FieldMask::internal_default_instance());
}

TestFieldMask::TestFieldMask(const TestFieldMask& from)
  : ::google::protobuf::Message(),
    _internal_metadata_(NULL) {
  SharedCtor();
  UnsafeMergeFrom(from);
  // @@protoc_insertion_point(copy_constructor:proto3.TestFieldMask)
}

void TestFieldMask::SharedCtor() {
  value_ = NULL;
  _cached_size_ = 0;
}

TestFieldMask::~TestFieldMask() {
  // @@protoc_insertion_point(destructor:proto3.TestFieldMask)
  SharedDtor();
}

void TestFieldMask::SharedDtor() {
  if (this != &TestFieldMask_default_instance_.get()) {
    delete value_;
  }
}

void TestFieldMask::SetCachedSize(int size) const {
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
}
const ::google::protobuf::Descriptor* TestFieldMask::descriptor() {
  protobuf_AssignDescriptorsOnce();
  return TestFieldMask_descriptor_;
}

const TestFieldMask& TestFieldMask::default_instance() {
  protobuf_InitDefaults_google_2fprotobuf_2futil_2fjson_5fformat_5fproto3_2eproto();
  return *internal_default_instance();
}

::google::protobuf::internal::ExplicitlyConstructed<TestFieldMask> TestFieldMask_default_instance_;

TestFieldMask* TestFieldMask::New(::google::protobuf::Arena* arena) const {
  TestFieldMask* n = new TestFieldMask;
  if (arena != NULL) {
    arena->Own(n);
  }
  return n;
}

void TestFieldMask::Clear() {
// @@protoc_insertion_point(message_clear_start:proto3.TestFieldMask)
  if (GetArenaNoVirtual() == NULL && value_ != NULL) delete value_;
  value_ = NULL;
}

bool TestFieldMask::MergePartialFromCodedStream(
    ::google::protobuf::io::CodedInputStream* input) {
#define DO_(EXPRESSION) if (!GOOGLE_PREDICT_TRUE(EXPRESSION)) goto failure
  ::google::protobuf::uint32 tag;
  // @@protoc_insertion_point(parse_start:proto3.TestFieldMask)
  for (;;) {
    ::std::pair< ::google::protobuf::uint32, bool> p = input->ReadTagWithCutoff(127);
    tag = p.first;
    if (!p.second) goto handle_unusual;
    switch (::google::protobuf::internal::WireFormatLite::GetTagFieldNumber(tag)) {
      // optional .google.protobuf.FieldMask value = 1;
      case 1: {
        if (tag == 10) {
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessageNoVirtual(
               input, mutable_value()));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectAtEnd()) goto success;
        break;
      }

      default: {
      handle_unusual:
        if (tag == 0 ||
            ::google::protobuf::internal::WireFormatLite::GetTagWireType(tag) ==
            ::google::protobuf::internal::WireFormatLite::WIRETYPE_END_GROUP) {
          goto success;
        }
        DO_(::google::protobuf::internal::WireFormatLite::SkipField(input, tag));
        break;
      }
    }
  }
success:
  // @@protoc_insertion_point(parse_success:proto3.TestFieldMask)
  return true;
failure:
  // @@protoc_insertion_point(parse_failure:proto3.TestFieldMask)
  return false;
#undef DO_
}

void TestFieldMask::SerializeWithCachedSizes(
    ::google::protobuf::io::CodedOutputStream* output) const {
  // @@protoc_insertion_point(serialize_start:proto3.TestFieldMask)
  // optional .google.protobuf.FieldMask value = 1;
  if (this->has_value()) {
    ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
      1, *this->value_, output);
  }

  // @@protoc_insertion_point(serialize_end:proto3.TestFieldMask)
}

::google::protobuf::uint8* TestFieldMask::InternalSerializeWithCachedSizesToArray(
    bool deterministic, ::google::protobuf::uint8* target) const {
  (void)deterministic; // Unused
  // @@protoc_insertion_point(serialize_to_array_start:proto3.TestFieldMask)
  // optional .google.protobuf.FieldMask value = 1;
  if (this->has_value()) {
    target = ::google::protobuf::internal::WireFormatLite::
      InternalWriteMessageNoVirtualToArray(
        1, *this->value_, false, target);
  }

  // @@protoc_insertion_point(serialize_to_array_end:proto3.TestFieldMask)
  return target;
}

size_t TestFieldMask::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:proto3.TestFieldMask)
  size_t total_size = 0;

  // optional .google.protobuf.FieldMask value = 1;
  if (this->has_value()) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::MessageSizeNoVirtual(
        *this->value_);
  }

  int cached_size = ::google::protobuf::internal::ToCachedSize(total_size);
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = cached_size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
  return total_size;
}

void TestFieldMask::MergeFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_merge_from_start:proto3.TestFieldMask)
  if (GOOGLE_PREDICT_FALSE(&from == this)) MergeFromFail(__LINE__);
  const TestFieldMask* source =
      ::google::protobuf::internal::DynamicCastToGenerated<const TestFieldMask>(
          &from);
  if (source == NULL) {
  // @@protoc_insertion_point(generalized_merge_from_cast_fail:proto3.TestFieldMask)
    ::google::protobuf::internal::ReflectionOps::Merge(from, this);
  } else {
  // @@protoc_insertion_point(generalized_merge_from_cast_success:proto3.TestFieldMask)
    UnsafeMergeFrom(*source);
  }
}

void TestFieldMask::MergeFrom(const TestFieldMask& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:proto3.TestFieldMask)
  if (GOOGLE_PREDICT_TRUE(&from != this)) {
    UnsafeMergeFrom(from);
  } else {
    MergeFromFail(__LINE__);
  }
}

void TestFieldMask::UnsafeMergeFrom(const TestFieldMask& from) {
  GOOGLE_DCHECK(&from != this);
  if (from.has_value()) {
    mutable_value()->::google::protobuf::FieldMask::MergeFrom(from.value());
  }
}

void TestFieldMask::CopyFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_copy_from_start:proto3.TestFieldMask)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

void TestFieldMask::CopyFrom(const TestFieldMask& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:proto3.TestFieldMask)
  if (&from == this) return;
  Clear();
  UnsafeMergeFrom(from);
}

bool TestFieldMask::IsInitialized() const {

  return true;
}

void TestFieldMask::Swap(TestFieldMask* other) {
  if (other == this) return;
  InternalSwap(other);
}
void TestFieldMask::InternalSwap(TestFieldMask* other) {
  std::swap(value_, other->value_);
  _internal_metadata_.Swap(&other->_internal_metadata_);
  std::swap(_cached_size_, other->_cached_size_);
}

::google::protobuf::Metadata TestFieldMask::GetMetadata() const {
  protobuf_AssignDescriptorsOnce();
  ::google::protobuf::Metadata metadata;
  metadata.descriptor = TestFieldMask_descriptor_;
  metadata.reflection = TestFieldMask_reflection_;
  return metadata;
}

#if PROTOBUF_INLINE_NOT_IN_HEADERS
// TestFieldMask

// optional .google.protobuf.FieldMask value = 1;
bool TestFieldMask::has_value() const {
  return this != internal_default_instance() && value_ != NULL;
}
void TestFieldMask::clear_value() {
  if (GetArenaNoVirtual() == NULL && value_ != NULL) delete value_;
  value_ = NULL;
}
const ::google::protobuf::FieldMask& TestFieldMask::value() const {
  // @@protoc_insertion_point(field_get:proto3.TestFieldMask.value)
  return value_ != NULL ? *value_
                         : *::google::protobuf::FieldMask::internal_default_instance();
}
::google::protobuf::FieldMask* TestFieldMask::mutable_value() {
  
  if (value_ == NULL) {
    value_ = new ::google::protobuf::FieldMask;
  }
  // @@protoc_insertion_point(field_mutable:proto3.TestFieldMask.value)
  return value_;
}
::google::protobuf::FieldMask* TestFieldMask::release_value() {
  // @@protoc_insertion_point(field_release:proto3.TestFieldMask.value)
  
  ::google::protobuf::FieldMask* temp = value_;
  value_ = NULL;
  return temp;
}
void TestFieldMask::set_allocated_value(::google::protobuf::FieldMask* value) {
  delete value_;
  value_ = value;
  if (value) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_set_allocated:proto3.TestFieldMask.value)
}

inline const TestFieldMask* TestFieldMask::internal_default_instance() {
  return &TestFieldMask_default_instance_.get();
}
#endif  // PROTOBUF_INLINE_NOT_IN_HEADERS

// ===================================================================

#if !defined(_MSC_VER) || _MSC_VER >= 1900
const int TestStruct::kValueFieldNumber;
const int TestStruct::kRepeatedValueFieldNumber;
#endif  // !defined(_MSC_VER) || _MSC_VER >= 1900

TestStruct::TestStruct()
  : ::google::protobuf::Message(), _internal_metadata_(NULL) {
  if (this != internal_default_instance()) protobuf_InitDefaults_google_2fprotobuf_2futil_2fjson_5fformat_5fproto3_2eproto();
  SharedCtor();
  // @@protoc_insertion_point(constructor:proto3.TestStruct)
}

void TestStruct::InitAsDefaultInstance() {
  value_ = const_cast< ::google::protobuf::Struct*>(
      ::google::protobuf::Struct::internal_default_instance());
}

TestStruct::TestStruct(const TestStruct& from)
  : ::google::protobuf::Message(),
    _internal_metadata_(NULL) {
  SharedCtor();
  UnsafeMergeFrom(from);
  // @@protoc_insertion_point(copy_constructor:proto3.TestStruct)
}

void TestStruct::SharedCtor() {
  value_ = NULL;
  _cached_size_ = 0;
}

TestStruct::~TestStruct() {
  // @@protoc_insertion_point(destructor:proto3.TestStruct)
  SharedDtor();
}

void TestStruct::SharedDtor() {
  if (this != &TestStruct_default_instance_.get()) {
    delete value_;
  }
}

void TestStruct::SetCachedSize(int size) const {
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
}
const ::google::protobuf::Descriptor* TestStruct::descriptor() {
  protobuf_AssignDescriptorsOnce();
  return TestStruct_descriptor_;
}

const TestStruct& TestStruct::default_instance() {
  protobuf_InitDefaults_google_2fprotobuf_2futil_2fjson_5fformat_5fproto3_2eproto();
  return *internal_default_instance();
}

::google::protobuf::internal::ExplicitlyConstructed<TestStruct> TestStruct_default_instance_;

TestStruct* TestStruct::New(::google::protobuf::Arena* arena) const {
  TestStruct* n = new TestStruct;
  if (arena != NULL) {
    arena->Own(n);
  }
  return n;
}

void TestStruct::Clear() {
// @@protoc_insertion_point(message_clear_start:proto3.TestStruct)
  if (GetArenaNoVirtual() == NULL && value_ != NULL) delete value_;
  value_ = NULL;
  repeated_value_.Clear();
}

bool TestStruct::MergePartialFromCodedStream(
    ::google::protobuf::io::CodedInputStream* input) {
#define DO_(EXPRESSION) if (!GOOGLE_PREDICT_TRUE(EXPRESSION)) goto failure
  ::google::protobuf::uint32 tag;
  // @@protoc_insertion_point(parse_start:proto3.TestStruct)
  for (;;) {
    ::std::pair< ::google::protobuf::uint32, bool> p = input->ReadTagWithCutoff(127);
    tag = p.first;
    if (!p.second) goto handle_unusual;
    switch (::google::protobuf::internal::WireFormatLite::GetTagFieldNumber(tag)) {
      // optional .google.protobuf.Struct value = 1;
      case 1: {
        if (tag == 10) {
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessageNoVirtual(
               input, mutable_value()));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(18)) goto parse_repeated_value;
        break;
      }

      // repeated .google.protobuf.Struct repeated_value = 2;
      case 2: {
        if (tag == 18) {
         parse_repeated_value:
          DO_(input->IncrementRecursionDepth());
         parse_loop_repeated_value:
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessageNoVirtualNoRecursionDepth(
                input, add_repeated_value()));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(18)) goto parse_loop_repeated_value;
        input->UnsafeDecrementRecursionDepth();
        if (input->ExpectAtEnd()) goto success;
        break;
      }

      default: {
      handle_unusual:
        if (tag == 0 ||
            ::google::protobuf::internal::WireFormatLite::GetTagWireType(tag) ==
            ::google::protobuf::internal::WireFormatLite::WIRETYPE_END_GROUP) {
          goto success;
        }
        DO_(::google::protobuf::internal::WireFormatLite::SkipField(input, tag));
        break;
      }
    }
  }
success:
  // @@protoc_insertion_point(parse_success:proto3.TestStruct)
  return true;
failure:
  // @@protoc_insertion_point(parse_failure:proto3.TestStruct)
  return false;
#undef DO_
}

void TestStruct::SerializeWithCachedSizes(
    ::google::protobuf::io::CodedOutputStream* output) const {
  // @@protoc_insertion_point(serialize_start:proto3.TestStruct)
  // optional .google.protobuf.Struct value = 1;
  if (this->has_value()) {
    ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
      1, *this->value_, output);
  }

  // repeated .google.protobuf.Struct repeated_value = 2;
  for (unsigned int i = 0, n = this->repeated_value_size(); i < n; i++) {
    ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
      2, this->repeated_value(i), output);
  }

  // @@protoc_insertion_point(serialize_end:proto3.TestStruct)
}

::google::protobuf::uint8* TestStruct::InternalSerializeWithCachedSizesToArray(
    bool deterministic, ::google::protobuf::uint8* target) const {
  (void)deterministic; // Unused
  // @@protoc_insertion_point(serialize_to_array_start:proto3.TestStruct)
  // optional .google.protobuf.Struct value = 1;
  if (this->has_value()) {
    target = ::google::protobuf::internal::WireFormatLite::
      InternalWriteMessageNoVirtualToArray(
        1, *this->value_, false, target);
  }

  // repeated .google.protobuf.Struct repeated_value = 2;
  for (unsigned int i = 0, n = this->repeated_value_size(); i < n; i++) {
    target = ::google::protobuf::internal::WireFormatLite::
      InternalWriteMessageNoVirtualToArray(
        2, this->repeated_value(i), false, target);
  }

  // @@protoc_insertion_point(serialize_to_array_end:proto3.TestStruct)
  return target;
}

size_t TestStruct::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:proto3.TestStruct)
  size_t total_size = 0;

  // optional .google.protobuf.Struct value = 1;
  if (this->has_value()) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::MessageSizeNoVirtual(
        *this->value_);
  }

  // repeated .google.protobuf.Struct repeated_value = 2;
  {
    unsigned int count = this->repeated_value_size();
    total_size += 1UL * count;
    for (unsigned int i = 0; i < count; i++) {
      total_size +=
        ::google::protobuf::internal::WireFormatLite::MessageSizeNoVirtual(
          this->repeated_value(i));
    }
  }

  int cached_size = ::google::protobuf::internal::ToCachedSize(total_size);
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = cached_size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
  return total_size;
}

void TestStruct::MergeFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_merge_from_start:proto3.TestStruct)
  if (GOOGLE_PREDICT_FALSE(&from == this)) MergeFromFail(__LINE__);
  const TestStruct* source =
      ::google::protobuf::internal::DynamicCastToGenerated<const TestStruct>(
          &from);
  if (source == NULL) {
  // @@protoc_insertion_point(generalized_merge_from_cast_fail:proto3.TestStruct)
    ::google::protobuf::internal::ReflectionOps::Merge(from, this);
  } else {
  // @@protoc_insertion_point(generalized_merge_from_cast_success:proto3.TestStruct)
    UnsafeMergeFrom(*source);
  }
}

void TestStruct::MergeFrom(const TestStruct& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:proto3.TestStruct)
  if (GOOGLE_PREDICT_TRUE(&from != this)) {
    UnsafeMergeFrom(from);
  } else {
    MergeFromFail(__LINE__);
  }
}

void TestStruct::UnsafeMergeFrom(const TestStruct& from) {
  GOOGLE_DCHECK(&from != this);
  repeated_value_.MergeFrom(from.repeated_value_);
  if (from.has_value()) {
    mutable_value()->::google::protobuf::Struct::MergeFrom(from.value());
  }
}

void TestStruct::CopyFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_copy_from_start:proto3.TestStruct)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

void TestStruct::CopyFrom(const TestStruct& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:proto3.TestStruct)
  if (&from == this) return;
  Clear();
  UnsafeMergeFrom(from);
}

bool TestStruct::IsInitialized() const {

  return true;
}

void TestStruct::Swap(TestStruct* other) {
  if (other == this) return;
  InternalSwap(other);
}
void TestStruct::InternalSwap(TestStruct* other) {
  std::swap(value_, other->value_);
  repeated_value_.UnsafeArenaSwap(&other->repeated_value_);
  _internal_metadata_.Swap(&other->_internal_metadata_);
  std::swap(_cached_size_, other->_cached_size_);
}

::google::protobuf::Metadata TestStruct::GetMetadata() const {
  protobuf_AssignDescriptorsOnce();
  ::google::protobuf::Metadata metadata;
  metadata.descriptor = TestStruct_descriptor_;
  metadata.reflection = TestStruct_reflection_;
  return metadata;
}

#if PROTOBUF_INLINE_NOT_IN_HEADERS
// TestStruct

// optional .google.protobuf.Struct value = 1;
bool TestStruct::has_value() const {
  return this != internal_default_instance() && value_ != NULL;
}
void TestStruct::clear_value() {
  if (GetArenaNoVirtual() == NULL && value_ != NULL) delete value_;
  value_ = NULL;
}
const ::google::protobuf::Struct& TestStruct::value() const {
  // @@protoc_insertion_point(field_get:proto3.TestStruct.value)
  return value_ != NULL ? *value_
                         : *::google::protobuf::Struct::internal_default_instance();
}
::google::protobuf::Struct* TestStruct::mutable_value() {
  
  if (value_ == NULL) {
    value_ = new ::google::protobuf::Struct;
  }
  // @@protoc_insertion_point(field_mutable:proto3.TestStruct.value)
  return value_;
}
::google::protobuf::Struct* TestStruct::release_value() {
  // @@protoc_insertion_point(field_release:proto3.TestStruct.value)
  
  ::google::protobuf::Struct* temp = value_;
  value_ = NULL;
  return temp;
}
void TestStruct::set_allocated_value(::google::protobuf::Struct* value) {
  delete value_;
  if (value != NULL && value->GetArena() != NULL) {
    ::google::protobuf::Struct* new_value = new ::google::protobuf::Struct;
    new_value->CopyFrom(*value);
    value = new_value;
  }
  value_ = value;
  if (value) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_set_allocated:proto3.TestStruct.value)
}

// repeated .google.protobuf.Struct repeated_value = 2;
int TestStruct::repeated_value_size() const {
  return repeated_value_.size();
}
void TestStruct::clear_repeated_value() {
  repeated_value_.Clear();
}
const ::google::protobuf::Struct& TestStruct::repeated_value(int index) const {
  // @@protoc_insertion_point(field_get:proto3.TestStruct.repeated_value)
  return repeated_value_.Get(index);
}
::google::protobuf::Struct* TestStruct::mutable_repeated_value(int index) {
  // @@protoc_insertion_point(field_mutable:proto3.TestStruct.repeated_value)
  return repeated_value_.Mutable(index);
}
::google::protobuf::Struct* TestStruct::add_repeated_value() {
  // @@protoc_insertion_point(field_add:proto3.TestStruct.repeated_value)
  return repeated_value_.Add();
}
::google::protobuf::RepeatedPtrField< ::google::protobuf::Struct >*
TestStruct::mutable_repeated_value() {
  // @@protoc_insertion_point(field_mutable_list:proto3.TestStruct.repeated_value)
  return &repeated_value_;
}
const ::google::protobuf::RepeatedPtrField< ::google::protobuf::Struct >&
TestStruct::repeated_value() const {
  // @@protoc_insertion_point(field_list:proto3.TestStruct.repeated_value)
  return repeated_value_;
}

inline const TestStruct* TestStruct::internal_default_instance() {
  return &TestStruct_default_instance_.get();
}
#endif  // PROTOBUF_INLINE_NOT_IN_HEADERS

// ===================================================================

#if !defined(_MSC_VER) || _MSC_VER >= 1900
const int TestAny::kValueFieldNumber;
const int TestAny::kRepeatedValueFieldNumber;
#endif  // !defined(_MSC_VER) || _MSC_VER >= 1900

TestAny::TestAny()
  : ::google::protobuf::Message(), _internal_metadata_(NULL) {
  if (this != internal_default_instance()) protobuf_InitDefaults_google_2fprotobuf_2futil_2fjson_5fformat_5fproto3_2eproto();
  SharedCtor();
  // @@protoc_insertion_point(constructor:proto3.TestAny)
}

void TestAny::InitAsDefaultInstance() {
  value_ = const_cast< ::google::protobuf::Any*>(
      ::google::protobuf::Any::internal_default_instance());
}

TestAny::TestAny(const TestAny& from)
  : ::google::protobuf::Message(),
    _internal_metadata_(NULL) {
  SharedCtor();
  UnsafeMergeFrom(from);
  // @@protoc_insertion_point(copy_constructor:proto3.TestAny)
}

void TestAny::SharedCtor() {
  value_ = NULL;
  _cached_size_ = 0;
}

TestAny::~TestAny() {
  // @@protoc_insertion_point(destructor:proto3.TestAny)
  SharedDtor();
}

void TestAny::SharedDtor() {
  if (this != &TestAny_default_instance_.get()) {
    delete value_;
  }
}

void TestAny::SetCachedSize(int size) const {
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
}
const ::google::protobuf::Descriptor* TestAny::descriptor() {
  protobuf_AssignDescriptorsOnce();
  return TestAny_descriptor_;
}

const TestAny& TestAny::default_instance() {
  protobuf_InitDefaults_google_2fprotobuf_2futil_2fjson_5fformat_5fproto3_2eproto();
  return *internal_default_instance();
}

::google::protobuf::internal::ExplicitlyConstructed<TestAny> TestAny_default_instance_;

TestAny* TestAny::New(::google::protobuf::Arena* arena) const {
  TestAny* n = new TestAny;
  if (arena != NULL) {
    arena->Own(n);
  }
  return n;
}

void TestAny::Clear() {
// @@protoc_insertion_point(message_clear_start:proto3.TestAny)
  if (GetArenaNoVirtual() == NULL && value_ != NULL) delete value_;
  value_ = NULL;
  repeated_value_.Clear();
}

bool TestAny::MergePartialFromCodedStream(
    ::google::protobuf::io::CodedInputStream* input) {
#define DO_(EXPRESSION) if (!GOOGLE_PREDICT_TRUE(EXPRESSION)) goto failure
  ::google::protobuf::uint32 tag;
  // @@protoc_insertion_point(parse_start:proto3.TestAny)
  for (;;) {
    ::std::pair< ::google::protobuf::uint32, bool> p = input->ReadTagWithCutoff(127);
    tag = p.first;
    if (!p.second) goto handle_unusual;
    switch (::google::protobuf::internal::WireFormatLite::GetTagFieldNumber(tag)) {
      // optional .google.protobuf.Any value = 1;
      case 1: {
        if (tag == 10) {
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessageNoVirtual(
               input, mutable_value()));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(18)) goto parse_repeated_value;
        break;
      }

      // repeated .google.protobuf.Any repeated_value = 2;
      case 2: {
        if (tag == 18) {
         parse_repeated_value:
          DO_(input->IncrementRecursionDepth());
         parse_loop_repeated_value:
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessageNoVirtualNoRecursionDepth(
                input, add_repeated_value()));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(18)) goto parse_loop_repeated_value;
        input->UnsafeDecrementRecursionDepth();
        if (input->ExpectAtEnd()) goto success;
        break;
      }

      default: {
      handle_unusual:
        if (tag == 0 ||
            ::google::protobuf::internal::WireFormatLite::GetTagWireType(tag) ==
            ::google::protobuf::internal::WireFormatLite::WIRETYPE_END_GROUP) {
          goto success;
        }
        DO_(::google::protobuf::internal::WireFormatLite::SkipField(input, tag));
        break;
      }
    }
  }
success:
  // @@protoc_insertion_point(parse_success:proto3.TestAny)
  return true;
failure:
  // @@protoc_insertion_point(parse_failure:proto3.TestAny)
  return false;
#undef DO_
}

void TestAny::SerializeWithCachedSizes(
    ::google::protobuf::io::CodedOutputStream* output) const {
  // @@protoc_insertion_point(serialize_start:proto3.TestAny)
  // optional .google.protobuf.Any value = 1;
  if (this->has_value()) {
    ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
      1, *this->value_, output);
  }

  // repeated .google.protobuf.Any repeated_value = 2;
  for (unsigned int i = 0, n = this->repeated_value_size(); i < n; i++) {
    ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
      2, this->repeated_value(i), output);
  }

  // @@protoc_insertion_point(serialize_end:proto3.TestAny)
}

::google::protobuf::uint8* TestAny::InternalSerializeWithCachedSizesToArray(
    bool deterministic, ::google::protobuf::uint8* target) const {
  (void)deterministic; // Unused
  // @@protoc_insertion_point(serialize_to_array_start:proto3.TestAny)
  // optional .google.protobuf.Any value = 1;
  if (this->has_value()) {
    target = ::google::protobuf::internal::WireFormatLite::
      InternalWriteMessageNoVirtualToArray(
        1, *this->value_, false, target);
  }

  // repeated .google.protobuf.Any repeated_value = 2;
  for (unsigned int i = 0, n = this->repeated_value_size(); i < n; i++) {
    target = ::google::protobuf::internal::WireFormatLite::
      InternalWriteMessageNoVirtualToArray(
        2, this->repeated_value(i), false, target);
  }

  // @@protoc_insertion_point(serialize_to_array_end:proto3.TestAny)
  return target;
}

size_t TestAny::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:proto3.TestAny)
  size_t total_size = 0;

  // optional .google.protobuf.Any value = 1;
  if (this->has_value()) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::MessageSizeNoVirtual(
        *this->value_);
  }

  // repeated .google.protobuf.Any repeated_value = 2;
  {
    unsigned int count = this->repeated_value_size();
    total_size += 1UL * count;
    for (unsigned int i = 0; i < count; i++) {
      total_size +=
        ::google::protobuf::internal::WireFormatLite::MessageSizeNoVirtual(
          this->repeated_value(i));
    }
  }

  int cached_size = ::google::protobuf::internal::ToCachedSize(total_size);
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = cached_size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
  return total_size;
}

void TestAny::MergeFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_merge_from_start:proto3.TestAny)
  if (GOOGLE_PREDICT_FALSE(&from == this)) MergeFromFail(__LINE__);
  const TestAny* source =
      ::google::protobuf::internal::DynamicCastToGenerated<const TestAny>(
          &from);
  if (source == NULL) {
  // @@protoc_insertion_point(generalized_merge_from_cast_fail:proto3.TestAny)
    ::google::protobuf::internal::ReflectionOps::Merge(from, this);
  } else {
  // @@protoc_insertion_point(generalized_merge_from_cast_success:proto3.TestAny)
    UnsafeMergeFrom(*source);
  }
}

void TestAny::MergeFrom(const TestAny& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:proto3.TestAny)
  if (GOOGLE_PREDICT_TRUE(&from != this)) {
    UnsafeMergeFrom(from);
  } else {
    MergeFromFail(__LINE__);
  }
}

void TestAny::UnsafeMergeFrom(const TestAny& from) {
  GOOGLE_DCHECK(&from != this);
  repeated_value_.MergeFrom(from.repeated_value_);
  if (from.has_value()) {
    mutable_value()->::google::protobuf::Any::MergeFrom(from.value());
  }
}

void TestAny::CopyFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_copy_from_start:proto3.TestAny)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

void TestAny::CopyFrom(const TestAny& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:proto3.TestAny)
  if (&from == this) return;
  Clear();
  UnsafeMergeFrom(from);
}

bool TestAny::IsInitialized() const {

  return true;
}

void TestAny::Swap(TestAny* other) {
  if (other == this) return;
  InternalSwap(other);
}
void TestAny::InternalSwap(TestAny* other) {
  std::swap(value_, other->value_);
  repeated_value_.UnsafeArenaSwap(&other->repeated_value_);
  _internal_metadata_.Swap(&other->_internal_metadata_);
  std::swap(_cached_size_, other->_cached_size_);
}

::google::protobuf::Metadata TestAny::GetMetadata() const {
  protobuf_AssignDescriptorsOnce();
  ::google::protobuf::Metadata metadata;
  metadata.descriptor = TestAny_descriptor_;
  metadata.reflection = TestAny_reflection_;
  return metadata;
}

#if PROTOBUF_INLINE_NOT_IN_HEADERS
// TestAny

// optional .google.protobuf.Any value = 1;
bool TestAny::has_value() const {
  return this != internal_default_instance() && value_ != NULL;
}
void TestAny::clear_value() {
  if (GetArenaNoVirtual() == NULL && value_ != NULL) delete value_;
  value_ = NULL;
}
const ::google::protobuf::Any& TestAny::value() const {
  // @@protoc_insertion_point(field_get:proto3.TestAny.value)
  return value_ != NULL ? *value_
                         : *::google::protobuf::Any::internal_default_instance();
}
::google::protobuf::Any* TestAny::mutable_value() {
  
  if (value_ == NULL) {
    value_ = new ::google::protobuf::Any;
  }
  // @@protoc_insertion_point(field_mutable:proto3.TestAny.value)
  return value_;
}
::google::protobuf::Any* TestAny::release_value() {
  // @@protoc_insertion_point(field_release:proto3.TestAny.value)
  
  ::google::protobuf::Any* temp = value_;
  value_ = NULL;
  return temp;
}
void TestAny::set_allocated_value(::google::protobuf::Any* value) {
  delete value_;
  value_ = value;
  if (value) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_set_allocated:proto3.TestAny.value)
}

// repeated .google.protobuf.Any repeated_value = 2;
int TestAny::repeated_value_size() const {
  return repeated_value_.size();
}
void TestAny::clear_repeated_value() {
  repeated_value_.Clear();
}
const ::google::protobuf::Any& TestAny::repeated_value(int index) const {
  // @@protoc_insertion_point(field_get:proto3.TestAny.repeated_value)
  return repeated_value_.Get(index);
}
::google::protobuf::Any* TestAny::mutable_repeated_value(int index) {
  // @@protoc_insertion_point(field_mutable:proto3.TestAny.repeated_value)
  return repeated_value_.Mutable(index);
}
::google::protobuf::Any* TestAny::add_repeated_value() {
  // @@protoc_insertion_point(field_add:proto3.TestAny.repeated_value)
  return repeated_value_.Add();
}
::google::protobuf::RepeatedPtrField< ::google::protobuf::Any >*
TestAny::mutable_repeated_value() {
  // @@protoc_insertion_point(field_mutable_list:proto3.TestAny.repeated_value)
  return &repeated_value_;
}
const ::google::protobuf::RepeatedPtrField< ::google::protobuf::Any >&
TestAny::repeated_value() const {
  // @@protoc_insertion_point(field_list:proto3.TestAny.repeated_value)
  return repeated_value_;
}

inline const TestAny* TestAny::internal_default_instance() {
  return &TestAny_default_instance_.get();
}
#endif  // PROTOBUF_INLINE_NOT_IN_HEADERS

// ===================================================================

#if !defined(_MSC_VER) || _MSC_VER >= 1900
const int TestValue::kValueFieldNumber;
const int TestValue::kRepeatedValueFieldNumber;
#endif  // !defined(_MSC_VER) || _MSC_VER >= 1900

TestValue::TestValue()
  : ::google::protobuf::Message(), _internal_metadata_(NULL) {
  if (this != internal_default_instance()) protobuf_InitDefaults_google_2fprotobuf_2futil_2fjson_5fformat_5fproto3_2eproto();
  SharedCtor();
  // @@protoc_insertion_point(constructor:proto3.TestValue)
}

void TestValue::InitAsDefaultInstance() {
  value_ = const_cast< ::google::protobuf::Value*>(
      ::google::protobuf::Value::internal_default_instance());
}

TestValue::TestValue(const TestValue& from)
  : ::google::protobuf::Message(),
    _internal_metadata_(NULL) {
  SharedCtor();
  UnsafeMergeFrom(from);
  // @@protoc_insertion_point(copy_constructor:proto3.TestValue)
}

void TestValue::SharedCtor() {
  value_ = NULL;
  _cached_size_ = 0;
}

TestValue::~TestValue() {
  // @@protoc_insertion_point(destructor:proto3.TestValue)
  SharedDtor();
}

void TestValue::SharedDtor() {
  if (this != &TestValue_default_instance_.get()) {
    delete value_;
  }
}

void TestValue::SetCachedSize(int size) const {
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
}
const ::google::protobuf::Descriptor* TestValue::descriptor() {
  protobuf_AssignDescriptorsOnce();
  return TestValue_descriptor_;
}

const TestValue& TestValue::default_instance() {
  protobuf_InitDefaults_google_2fprotobuf_2futil_2fjson_5fformat_5fproto3_2eproto();
  return *internal_default_instance();
}

::google::protobuf::internal::ExplicitlyConstructed<TestValue> TestValue_default_instance_;

TestValue* TestValue::New(::google::protobuf::Arena* arena) const {
  TestValue* n = new TestValue;
  if (arena != NULL) {
    arena->Own(n);
  }
  return n;
}

void TestValue::Clear() {
// @@protoc_insertion_point(message_clear_start:proto3.TestValue)
  if (GetArenaNoVirtual() == NULL && value_ != NULL) delete value_;
  value_ = NULL;
  repeated_value_.Clear();
}

bool TestValue::MergePartialFromCodedStream(
    ::google::protobuf::io::CodedInputStream* input) {
#define DO_(EXPRESSION) if (!GOOGLE_PREDICT_TRUE(EXPRESSION)) goto failure
  ::google::protobuf::uint32 tag;
  // @@protoc_insertion_point(parse_start:proto3.TestValue)
  for (;;) {
    ::std::pair< ::google::protobuf::uint32, bool> p = input->ReadTagWithCutoff(127);
    tag = p.first;
    if (!p.second) goto handle_unusual;
    switch (::google::protobuf::internal::WireFormatLite::GetTagFieldNumber(tag)) {
      // optional .google.protobuf.Value value = 1;
      case 1: {
        if (tag == 10) {
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessageNoVirtual(
               input, mutable_value()));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(18)) goto parse_repeated_value;
        break;
      }

      // repeated .google.protobuf.Value repeated_value = 2;
      case 2: {
        if (tag == 18) {
         parse_repeated_value:
          DO_(input->IncrementRecursionDepth());
         parse_loop_repeated_value:
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessageNoVirtualNoRecursionDepth(
                input, add_repeated_value()));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(18)) goto parse_loop_repeated_value;
        input->UnsafeDecrementRecursionDepth();
        if (input->ExpectAtEnd()) goto success;
        break;
      }

      default: {
      handle_unusual:
        if (tag == 0 ||
            ::google::protobuf::internal::WireFormatLite::GetTagWireType(tag) ==
            ::google::protobuf::internal::WireFormatLite::WIRETYPE_END_GROUP) {
          goto success;
        }
        DO_(::google::protobuf::internal::WireFormatLite::SkipField(input, tag));
        break;
      }
    }
  }
success:
  // @@protoc_insertion_point(parse_success:proto3.TestValue)
  return true;
failure:
  // @@protoc_insertion_point(parse_failure:proto3.TestValue)
  return false;
#undef DO_
}

void TestValue::SerializeWithCachedSizes(
    ::google::protobuf::io::CodedOutputStream* output) const {
  // @@protoc_insertion_point(serialize_start:proto3.TestValue)
  // optional .google.protobuf.Value value = 1;
  if (this->has_value()) {
    ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
      1, *this->value_, output);
  }

  // repeated .google.protobuf.Value repeated_value = 2;
  for (unsigned int i = 0, n = this->repeated_value_size(); i < n; i++) {
    ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
      2, this->repeated_value(i), output);
  }

  // @@protoc_insertion_point(serialize_end:proto3.TestValue)
}

::google::protobuf::uint8* TestValue::InternalSerializeWithCachedSizesToArray(
    bool deterministic, ::google::protobuf::uint8* target) const {
  (void)deterministic; // Unused
  // @@protoc_insertion_point(serialize_to_array_start:proto3.TestValue)
  // optional .google.protobuf.Value value = 1;
  if (this->has_value()) {
    target = ::google::protobuf::internal::WireFormatLite::
      InternalWriteMessageNoVirtualToArray(
        1, *this->value_, false, target);
  }

  // repeated .google.protobuf.Value repeated_value = 2;
  for (unsigned int i = 0, n = this->repeated_value_size(); i < n; i++) {
    target = ::google::protobuf::internal::WireFormatLite::
      InternalWriteMessageNoVirtualToArray(
        2, this->repeated_value(i), false, target);
  }

  // @@protoc_insertion_point(serialize_to_array_end:proto3.TestValue)
  return target;
}

size_t TestValue::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:proto3.TestValue)
  size_t total_size = 0;

  // optional .google.protobuf.Value value = 1;
  if (this->has_value()) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::MessageSizeNoVirtual(
        *this->value_);
  }

  // repeated .google.protobuf.Value repeated_value = 2;
  {
    unsigned int count = this->repeated_value_size();
    total_size += 1UL * count;
    for (unsigned int i = 0; i < count; i++) {
      total_size +=
        ::google::protobuf::internal::WireFormatLite::MessageSizeNoVirtual(
          this->repeated_value(i));
    }
  }

  int cached_size = ::google::protobuf::internal::ToCachedSize(total_size);
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = cached_size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
  return total_size;
}

void TestValue::MergeFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_merge_from_start:proto3.TestValue)
  if (GOOGLE_PREDICT_FALSE(&from == this)) MergeFromFail(__LINE__);
  const TestValue* source =
      ::google::protobuf::internal::DynamicCastToGenerated<const TestValue>(
          &from);
  if (source == NULL) {
  // @@protoc_insertion_point(generalized_merge_from_cast_fail:proto3.TestValue)
    ::google::protobuf::internal::ReflectionOps::Merge(from, this);
  } else {
  // @@protoc_insertion_point(generalized_merge_from_cast_success:proto3.TestValue)
    UnsafeMergeFrom(*source);
  }
}

void TestValue::MergeFrom(const TestValue& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:proto3.TestValue)
  if (GOOGLE_PREDICT_TRUE(&from != this)) {
    UnsafeMergeFrom(from);
  } else {
    MergeFromFail(__LINE__);
  }
}

void TestValue::UnsafeMergeFrom(const TestValue& from) {
  GOOGLE_DCHECK(&from != this);
  repeated_value_.MergeFrom(from.repeated_value_);
  if (from.has_value()) {
    mutable_value()->::google::protobuf::Value::MergeFrom(from.value());
  }
}

void TestValue::CopyFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_copy_from_start:proto3.TestValue)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

void TestValue::CopyFrom(const TestValue& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:proto3.TestValue)
  if (&from == this) return;
  Clear();
  UnsafeMergeFrom(from);
}

bool TestValue::IsInitialized() const {

  return true;
}

void TestValue::Swap(TestValue* other) {
  if (other == this) return;
  InternalSwap(other);
}
void TestValue::InternalSwap(TestValue* other) {
  std::swap(value_, other->value_);
  repeated_value_.UnsafeArenaSwap(&other->repeated_value_);
  _internal_metadata_.Swap(&other->_internal_metadata_);
  std::swap(_cached_size_, other->_cached_size_);
}

::google::protobuf::Metadata TestValue::GetMetadata() const {
  protobuf_AssignDescriptorsOnce();
  ::google::protobuf::Metadata metadata;
  metadata.descriptor = TestValue_descriptor_;
  metadata.reflection = TestValue_reflection_;
  return metadata;
}

#if PROTOBUF_INLINE_NOT_IN_HEADERS
// TestValue

// optional .google.protobuf.Value value = 1;
bool TestValue::has_value() const {
  return this != internal_default_instance() && value_ != NULL;
}
void TestValue::clear_value() {
  if (GetArenaNoVirtual() == NULL && value_ != NULL) delete value_;
  value_ = NULL;
}
const ::google::protobuf::Value& TestValue::value() const {
  // @@protoc_insertion_point(field_get:proto3.TestValue.value)
  return value_ != NULL ? *value_
                         : *::google::protobuf::Value::internal_default_instance();
}
::google::protobuf::Value* TestValue::mutable_value() {
  
  if (value_ == NULL) {
    value_ = new ::google::protobuf::Value;
  }
  // @@protoc_insertion_point(field_mutable:proto3.TestValue.value)
  return value_;
}
::google::protobuf::Value* TestValue::release_value() {
  // @@protoc_insertion_point(field_release:proto3.TestValue.value)
  
  ::google::protobuf::Value* temp = value_;
  value_ = NULL;
  return temp;
}
void TestValue::set_allocated_value(::google::protobuf::Value* value) {
  delete value_;
  if (value != NULL && value->GetArena() != NULL) {
    ::google::protobuf::Value* new_value = new ::google::protobuf::Value;
    new_value->CopyFrom(*value);
    value = new_value;
  }
  value_ = value;
  if (value) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_set_allocated:proto3.TestValue.value)
}

// repeated .google.protobuf.Value repeated_value = 2;
int TestValue::repeated_value_size() const {
  return repeated_value_.size();
}
void TestValue::clear_repeated_value() {
  repeated_value_.Clear();
}
const ::google::protobuf::Value& TestValue::repeated_value(int index) const {
  // @@protoc_insertion_point(field_get:proto3.TestValue.repeated_value)
  return repeated_value_.Get(index);
}
::google::protobuf::Value* TestValue::mutable_repeated_value(int index) {
  // @@protoc_insertion_point(field_mutable:proto3.TestValue.repeated_value)
  return repeated_value_.Mutable(index);
}
::google::protobuf::Value* TestValue::add_repeated_value() {
  // @@protoc_insertion_point(field_add:proto3.TestValue.repeated_value)
  return repeated_value_.Add();
}
::google::protobuf::RepeatedPtrField< ::google::protobuf::Value >*
TestValue::mutable_repeated_value() {
  // @@protoc_insertion_point(field_mutable_list:proto3.TestValue.repeated_value)
  return &repeated_value_;
}
const ::google::protobuf::RepeatedPtrField< ::google::protobuf::Value >&
TestValue::repeated_value() const {
  // @@protoc_insertion_point(field_list:proto3.TestValue.repeated_value)
  return repeated_value_;
}

inline const TestValue* TestValue::internal_default_instance() {
  return &TestValue_default_instance_.get();
}
#endif  // PROTOBUF_INLINE_NOT_IN_HEADERS

// ===================================================================

#if !defined(_MSC_VER) || _MSC_VER >= 1900
const int TestListValue::kValueFieldNumber;
const int TestListValue::kRepeatedValueFieldNumber;
#endif  // !defined(_MSC_VER) || _MSC_VER >= 1900

TestListValue::TestListValue()
  : ::google::protobuf::Message(), _internal_metadata_(NULL) {
  if (this != internal_default_instance()) protobuf_InitDefaults_google_2fprotobuf_2futil_2fjson_5fformat_5fproto3_2eproto();
  SharedCtor();
  // @@protoc_insertion_point(constructor:proto3.TestListValue)
}

void TestListValue::InitAsDefaultInstance() {
  value_ = const_cast< ::google::protobuf::ListValue*>(
      ::google::protobuf::ListValue::internal_default_instance());
}

TestListValue::TestListValue(const TestListValue& from)
  : ::google::protobuf::Message(),
    _internal_metadata_(NULL) {
  SharedCtor();
  UnsafeMergeFrom(from);
  // @@protoc_insertion_point(copy_constructor:proto3.TestListValue)
}

void TestListValue::SharedCtor() {
  value_ = NULL;
  _cached_size_ = 0;
}

TestListValue::~TestListValue() {
  // @@protoc_insertion_point(destructor:proto3.TestListValue)
  SharedDtor();
}

void TestListValue::SharedDtor() {
  if (this != &TestListValue_default_instance_.get()) {
    delete value_;
  }
}

void TestListValue::SetCachedSize(int size) const {
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
}
const ::google::protobuf::Descriptor* TestListValue::descriptor() {
  protobuf_AssignDescriptorsOnce();
  return TestListValue_descriptor_;
}

const TestListValue& TestListValue::default_instance() {
  protobuf_InitDefaults_google_2fprotobuf_2futil_2fjson_5fformat_5fproto3_2eproto();
  return *internal_default_instance();
}

::google::protobuf::internal::ExplicitlyConstructed<TestListValue> TestListValue_default_instance_;

TestListValue* TestListValue::New(::google::protobuf::Arena* arena) const {
  TestListValue* n = new TestListValue;
  if (arena != NULL) {
    arena->Own(n);
  }
  return n;
}

void TestListValue::Clear() {
// @@protoc_insertion_point(message_clear_start:proto3.TestListValue)
  if (GetArenaNoVirtual() == NULL && value_ != NULL) delete value_;
  value_ = NULL;
  repeated_value_.Clear();
}

bool TestListValue::MergePartialFromCodedStream(
    ::google::protobuf::io::CodedInputStream* input) {
#define DO_(EXPRESSION) if (!GOOGLE_PREDICT_TRUE(EXPRESSION)) goto failure
  ::google::protobuf::uint32 tag;
  // @@protoc_insertion_point(parse_start:proto3.TestListValue)
  for (;;) {
    ::std::pair< ::google::protobuf::uint32, bool> p = input->ReadTagWithCutoff(127);
    tag = p.first;
    if (!p.second) goto handle_unusual;
    switch (::google::protobuf::internal::WireFormatLite::GetTagFieldNumber(tag)) {
      // optional .google.protobuf.ListValue value = 1;
      case 1: {
        if (tag == 10) {
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessageNoVirtual(
               input, mutable_value()));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(18)) goto parse_repeated_value;
        break;
      }

      // repeated .google.protobuf.ListValue repeated_value = 2;
      case 2: {
        if (tag == 18) {
         parse_repeated_value:
          DO_(input->IncrementRecursionDepth());
         parse_loop_repeated_value:
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessageNoVirtualNoRecursionDepth(
                input, add_repeated_value()));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(18)) goto parse_loop_repeated_value;
        input->UnsafeDecrementRecursionDepth();
        if (input->ExpectAtEnd()) goto success;
        break;
      }

      default: {
      handle_unusual:
        if (tag == 0 ||
            ::google::protobuf::internal::WireFormatLite::GetTagWireType(tag) ==
            ::google::protobuf::internal::WireFormatLite::WIRETYPE_END_GROUP) {
          goto success;
        }
        DO_(::google::protobuf::internal::WireFormatLite::SkipField(input, tag));
        break;
      }
    }
  }
success:
  // @@protoc_insertion_point(parse_success:proto3.TestListValue)
  return true;
failure:
  // @@protoc_insertion_point(parse_failure:proto3.TestListValue)
  return false;
#undef DO_
}

void TestListValue::SerializeWithCachedSizes(
    ::google::protobuf::io::CodedOutputStream* output) const {
  // @@protoc_insertion_point(serialize_start:proto3.TestListValue)
  // optional .google.protobuf.ListValue value = 1;
  if (this->has_value()) {
    ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
      1, *this->value_, output);
  }

  // repeated .google.protobuf.ListValue repeated_value = 2;
  for (unsigned int i = 0, n = this->repeated_value_size(); i < n; i++) {
    ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
      2, this->repeated_value(i), output);
  }

  // @@protoc_insertion_point(serialize_end:proto3.TestListValue)
}

::google::protobuf::uint8* TestListValue::InternalSerializeWithCachedSizesToArray(
    bool deterministic, ::google::protobuf::uint8* target) const {
  (void)deterministic; // Unused
  // @@protoc_insertion_point(serialize_to_array_start:proto3.TestListValue)
  // optional .google.protobuf.ListValue value = 1;
  if (this->has_value()) {
    target = ::google::protobuf::internal::WireFormatLite::
      InternalWriteMessageNoVirtualToArray(
        1, *this->value_, false, target);
  }

  // repeated .google.protobuf.ListValue repeated_value = 2;
  for (unsigned int i = 0, n = this->repeated_value_size(); i < n; i++) {
    target = ::google::protobuf::internal::WireFormatLite::
      InternalWriteMessageNoVirtualToArray(
        2, this->repeated_value(i), false, target);
  }

  // @@protoc_insertion_point(serialize_to_array_end:proto3.TestListValue)
  return target;
}

size_t TestListValue::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:proto3.TestListValue)
  size_t total_size = 0;

  // optional .google.protobuf.ListValue value = 1;
  if (this->has_value()) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::MessageSizeNoVirtual(
        *this->value_);
  }

  // repeated .google.protobuf.ListValue repeated_value = 2;
  {
    unsigned int count = this->repeated_value_size();
    total_size += 1UL * count;
    for (unsigned int i = 0; i < count; i++) {
      total_size +=
        ::google::protobuf::internal::WireFormatLite::MessageSizeNoVirtual(
          this->repeated_value(i));
    }
  }

  int cached_size = ::google::protobuf::internal::ToCachedSize(total_size);
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = cached_size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
  return total_size;
}

void TestListValue::MergeFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_merge_from_start:proto3.TestListValue)
  if (GOOGLE_PREDICT_FALSE(&from == this)) MergeFromFail(__LINE__);
  const TestListValue* source =
      ::google::protobuf::internal::DynamicCastToGenerated<const TestListValue>(
          &from);
  if (source == NULL) {
  // @@protoc_insertion_point(generalized_merge_from_cast_fail:proto3.TestListValue)
    ::google::protobuf::internal::ReflectionOps::Merge(from, this);
  } else {
  // @@protoc_insertion_point(generalized_merge_from_cast_success:proto3.TestListValue)
    UnsafeMergeFrom(*source);
  }
}

void TestListValue::MergeFrom(const TestListValue& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:proto3.TestListValue)
  if (GOOGLE_PREDICT_TRUE(&from != this)) {
    UnsafeMergeFrom(from);
  } else {
    MergeFromFail(__LINE__);
  }
}

void TestListValue::UnsafeMergeFrom(const TestListValue& from) {
  GOOGLE_DCHECK(&from != this);
  repeated_value_.MergeFrom(from.repeated_value_);
  if (from.has_value()) {
    mutable_value()->::google::protobuf::ListValue::MergeFrom(from.value());
  }
}

void TestListValue::CopyFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_copy_from_start:proto3.TestListValue)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

void TestListValue::CopyFrom(const TestListValue& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:proto3.TestListValue)
  if (&from == this) return;
  Clear();
  UnsafeMergeFrom(from);
}

bool TestListValue::IsInitialized() const {

  return true;
}

void TestListValue::Swap(TestListValue* other) {
  if (other == this) return;
  InternalSwap(other);
}
void TestListValue::InternalSwap(TestListValue* other) {
  std::swap(value_, other->value_);
  repeated_value_.UnsafeArenaSwap(&other->repeated_value_);
  _internal_metadata_.Swap(&other->_internal_metadata_);
  std::swap(_cached_size_, other->_cached_size_);
}

::google::protobuf::Metadata TestListValue::GetMetadata() const {
  protobuf_AssignDescriptorsOnce();
  ::google::protobuf::Metadata metadata;
  metadata.descriptor = TestListValue_descriptor_;
  metadata.reflection = TestListValue_reflection_;
  return metadata;
}

#if PROTOBUF_INLINE_NOT_IN_HEADERS
// TestListValue

// optional .google.protobuf.ListValue value = 1;
bool TestListValue::has_value() const {
  return this != internal_default_instance() && value_ != NULL;
}
void TestListValue::clear_value() {
  if (GetArenaNoVirtual() == NULL && value_ != NULL) delete value_;
  value_ = NULL;
}
const ::google::protobuf::ListValue& TestListValue::value() const {
  // @@protoc_insertion_point(field_get:proto3.TestListValue.value)
  return value_ != NULL ? *value_
                         : *::google::protobuf::ListValue::internal_default_instance();
}
::google::protobuf::ListValue* TestListValue::mutable_value() {
  
  if (value_ == NULL) {
    value_ = new ::google::protobuf::ListValue;
  }
  // @@protoc_insertion_point(field_mutable:proto3.TestListValue.value)
  return value_;
}
::google::protobuf::ListValue* TestListValue::release_value() {
  // @@protoc_insertion_point(field_release:proto3.TestListValue.value)
  
  ::google::protobuf::ListValue* temp = value_;
  value_ = NULL;
  return temp;
}
void TestListValue::set_allocated_value(::google::protobuf::ListValue* value) {
  delete value_;
  if (value != NULL && value->GetArena() != NULL) {
    ::google::protobuf::ListValue* new_value = new ::google::protobuf::ListValue;
    new_value->CopyFrom(*value);
    value = new_value;
  }
  value_ = value;
  if (value) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_set_allocated:proto3.TestListValue.value)
}

// repeated .google.protobuf.ListValue repeated_value = 2;
int TestListValue::repeated_value_size() const {
  return repeated_value_.size();
}
void TestListValue::clear_repeated_value() {
  repeated_value_.Clear();
}
const ::google::protobuf::ListValue& TestListValue::repeated_value(int index) const {
  // @@protoc_insertion_point(field_get:proto3.TestListValue.repeated_value)
  return repeated_value_.Get(index);
}
::google::protobuf::ListValue* TestListValue::mutable_repeated_value(int index) {
  // @@protoc_insertion_point(field_mutable:proto3.TestListValue.repeated_value)
  return repeated_value_.Mutable(index);
}
::google::protobuf::ListValue* TestListValue::add_repeated_value() {
  // @@protoc_insertion_point(field_add:proto3.TestListValue.repeated_value)
  return repeated_value_.Add();
}
::google::protobuf::RepeatedPtrField< ::google::protobuf::ListValue >*
TestListValue::mutable_repeated_value() {
  // @@protoc_insertion_point(field_mutable_list:proto3.TestListValue.repeated_value)
  return &repeated_value_;
}
const ::google::protobuf::RepeatedPtrField< ::google::protobuf::ListValue >&
TestListValue::repeated_value() const {
  // @@protoc_insertion_point(field_list:proto3.TestListValue.repeated_value)
  return repeated_value_;
}

inline const TestListValue* TestListValue::internal_default_instance() {
  return &TestListValue_default_instance_.get();
}
#endif  // PROTOBUF_INLINE_NOT_IN_HEADERS

// ===================================================================

#if !defined(_MSC_VER) || _MSC_VER >= 1900
const int TestBoolValue::kBoolValueFieldNumber;
const int TestBoolValue::kBoolMapFieldNumber;
#endif  // !defined(_MSC_VER) || _MSC_VER >= 1900

TestBoolValue::TestBoolValue()
  : ::google::protobuf::Message(), _internal_metadata_(NULL) {
  if (this != internal_default_instance()) protobuf_InitDefaults_google_2fprotobuf_2futil_2fjson_5fformat_5fproto3_2eproto();
  SharedCtor();
  // @@protoc_insertion_point(constructor:proto3.TestBoolValue)
}

void TestBoolValue::InitAsDefaultInstance() {
}

TestBoolValue::TestBoolValue(const TestBoolValue& from)
  : ::google::protobuf::Message(),
    _internal_metadata_(NULL) {
  SharedCtor();
  UnsafeMergeFrom(from);
  // @@protoc_insertion_point(copy_constructor:proto3.TestBoolValue)
}

void TestBoolValue::SharedCtor() {
  bool_map_.SetAssignDescriptorCallback(
      protobuf_AssignDescriptorsOnce);
  bool_map_.SetEntryDescriptor(
      &::proto3::TestBoolValue_BoolMapEntry_descriptor_);
  bool_value_ = false;
  _cached_size_ = 0;
}

TestBoolValue::~TestBoolValue() {
  // @@protoc_insertion_point(destructor:proto3.TestBoolValue)
  SharedDtor();
}

void TestBoolValue::SharedDtor() {
}

void TestBoolValue::SetCachedSize(int size) const {
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
}
const ::google::protobuf::Descriptor* TestBoolValue::descriptor() {
  protobuf_AssignDescriptorsOnce();
  return TestBoolValue_descriptor_;
}

const TestBoolValue& TestBoolValue::default_instance() {
  protobuf_InitDefaults_google_2fprotobuf_2futil_2fjson_5fformat_5fproto3_2eproto();
  return *internal_default_instance();
}

::google::protobuf::internal::ExplicitlyConstructed<TestBoolValue> TestBoolValue_default_instance_;

TestBoolValue* TestBoolValue::New(::google::protobuf::Arena* arena) const {
  TestBoolValue* n = new TestBoolValue;
  if (arena != NULL) {
    arena->Own(n);
  }
  return n;
}

void TestBoolValue::Clear() {
// @@protoc_insertion_point(message_clear_start:proto3.TestBoolValue)
  bool_value_ = false;
  bool_map_.Clear();
}

bool TestBoolValue::MergePartialFromCodedStream(
    ::google::protobuf::io::CodedInputStream* input) {
#define DO_(EXPRESSION) if (!GOOGLE_PREDICT_TRUE(EXPRESSION)) goto failure
  ::google::protobuf::uint32 tag;
  // @@protoc_insertion_point(parse_start:proto3.TestBoolValue)
  for (;;) {
    ::std::pair< ::google::protobuf::uint32, bool> p = input->ReadTagWithCutoff(127);
    tag = p.first;
    if (!p.second) goto handle_unusual;
    switch (::google::protobuf::internal::WireFormatLite::GetTagFieldNumber(tag)) {
      // optional bool bool_value = 1;
      case 1: {
        if (tag == 8) {

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   bool, ::google::protobuf::internal::WireFormatLite::TYPE_BOOL>(
                 input, &bool_value_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(18)) goto parse_bool_map;
        break;
      }

      // map<bool, int32> bool_map = 2;
      case 2: {
        if (tag == 18) {
         parse_bool_map:
          DO_(input->IncrementRecursionDepth());
         parse_loop_bool_map:
          TestBoolValue_BoolMapEntry::Parser< ::google::protobuf::internal::MapField<
              bool, ::google::protobuf::int32,
              ::google::protobuf::internal::WireFormatLite::TYPE_BOOL,
              ::google::protobuf::internal::WireFormatLite::TYPE_INT32,
              0 >,
            ::google::protobuf::Map< bool, ::google::protobuf::int32 > > parser(&bool_map_);
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessageNoVirtual(
              input, &parser));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(18)) goto parse_loop_bool_map;
        input->UnsafeDecrementRecursionDepth();
        if (input->ExpectAtEnd()) goto success;
        break;
      }

      default: {
      handle_unusual:
        if (tag == 0 ||
            ::google::protobuf::internal::WireFormatLite::GetTagWireType(tag) ==
            ::google::protobuf::internal::WireFormatLite::WIRETYPE_END_GROUP) {
          goto success;
        }
        DO_(::google::protobuf::internal::WireFormatLite::SkipField(input, tag));
        break;
      }
    }
  }
success:
  // @@protoc_insertion_point(parse_success:proto3.TestBoolValue)
  return true;
failure:
  // @@protoc_insertion_point(parse_failure:proto3.TestBoolValue)
  return false;
#undef DO_
}

void TestBoolValue::SerializeWithCachedSizes(
    ::google::protobuf::io::CodedOutputStream* output) const {
  // @@protoc_insertion_point(serialize_start:proto3.TestBoolValue)
  // optional bool bool_value = 1;
  if (this->bool_value() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteBool(1, this->bool_value(), output);
  }

  // map<bool, int32> bool_map = 2;
  if (!this->bool_map().empty()) {
    typedef ::google::protobuf::Map< bool, ::google::protobuf::int32 >::const_pointer
        ConstPtr;
    typedef ::google::protobuf::internal::SortItem< bool, ConstPtr > SortItem;
    typedef ::google::protobuf::internal::CompareByFirstField<SortItem> Less;

    if (output->IsSerializationDeterminstic() &&
        this->bool_map().size() > 1) {
      ::google::protobuf::scoped_array<SortItem> items(
          new SortItem[this->bool_map().size()]);
      typedef ::google::protobuf::Map< bool, ::google::protobuf::int32 >::size_type size_type;
      size_type n = 0;
      for (::google::protobuf::Map< bool, ::google::protobuf::int32 >::const_iterator
          it = this->bool_map().begin();
          it != this->bool_map().end(); ++it, ++n) {
        items[n] = SortItem(&*it);
      }
      ::std::sort(&items[0], &items[n], Less());
      ::google::protobuf::scoped_ptr<TestBoolValue_BoolMapEntry> entry;
      for (size_type i = 0; i < n; i++) {
        entry.reset(bool_map_.NewEntryWrapper(
            items[i].second->first, items[i].second->second));
        ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
            2, *entry, output);
      }
    } else {
      ::google::protobuf::scoped_ptr<TestBoolValue_BoolMapEntry> entry;
      for (::google::protobuf::Map< bool, ::google::protobuf::int32 >::const_iterator
          it = this->bool_map().begin();
          it != this->bool_map().end(); ++it) {
        entry.reset(bool_map_.NewEntryWrapper(
            it->first, it->second));
        ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
            2, *entry, output);
      }
    }
  }

  // @@protoc_insertion_point(serialize_end:proto3.TestBoolValue)
}

::google::protobuf::uint8* TestBoolValue::InternalSerializeWithCachedSizesToArray(
    bool deterministic, ::google::protobuf::uint8* target) const {
  (void)deterministic; // Unused
  // @@protoc_insertion_point(serialize_to_array_start:proto3.TestBoolValue)
  // optional bool bool_value = 1;
  if (this->bool_value() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteBoolToArray(1, this->bool_value(), target);
  }

  // map<bool, int32> bool_map = 2;
  if (!this->bool_map().empty()) {
    typedef ::google::protobuf::Map< bool, ::google::protobuf::int32 >::const_pointer
        ConstPtr;
    typedef ::google::protobuf::internal::SortItem< bool, ConstPtr > SortItem;
    typedef ::google::protobuf::internal::CompareByFirstField<SortItem> Less;

    if (deterministic &&
        this->bool_map().size() > 1) {
      ::google::protobuf::scoped_array<SortItem> items(
          new SortItem[this->bool_map().size()]);
      typedef ::google::protobuf::Map< bool, ::google::protobuf::int32 >::size_type size_type;
      size_type n = 0;
      for (::google::protobuf::Map< bool, ::google::protobuf::int32 >::const_iterator
          it = this->bool_map().begin();
          it != this->bool_map().end(); ++it, ++n) {
        items[n] = SortItem(&*it);
      }
      ::std::sort(&items[0], &items[n], Less());
      ::google::protobuf::scoped_ptr<TestBoolValue_BoolMapEntry> entry;
      for (size_type i = 0; i < n; i++) {
        entry.reset(bool_map_.NewEntryWrapper(
            items[i].second->first, items[i].second->second));
        target = ::google::protobuf::internal::WireFormatLite::
                   InternalWriteMessageNoVirtualToArray(
                       2, *entry, deterministic, target);
;
      }
    } else {
      ::google::protobuf::scoped_ptr<TestBoolValue_BoolMapEntry> entry;
      for (::google::protobuf::Map< bool, ::google::protobuf::int32 >::const_iterator
          it = this->bool_map().begin();
          it != this->bool_map().end(); ++it) {
        entry.reset(bool_map_.NewEntryWrapper(
            it->first, it->second));
        target = ::google::protobuf::internal::WireFormatLite::
                   InternalWriteMessageNoVirtualToArray(
                       2, *entry, deterministic, target);
;
      }
    }
  }

  // @@protoc_insertion_point(serialize_to_array_end:proto3.TestBoolValue)
  return target;
}

size_t TestBoolValue::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:proto3.TestBoolValue)
  size_t total_size = 0;

  // optional bool bool_value = 1;
  if (this->bool_value() != 0) {
    total_size += 1 + 1;
  }

  // map<bool, int32> bool_map = 2;
  total_size += 1 *
      ::google::protobuf::internal::FromIntSize(this->bool_map_size());
  {
    ::google::protobuf::scoped_ptr<TestBoolValue_BoolMapEntry> entry;
    for (::google::protobuf::Map< bool, ::google::protobuf::int32 >::const_iterator
        it = this->bool_map().begin();
        it != this->bool_map().end(); ++it) {
      entry.reset(bool_map_.NewEntryWrapper(it->first, it->second));
      total_size += ::google::protobuf::internal::WireFormatLite::
          MessageSizeNoVirtual(*entry);
    }
  }

  int cached_size = ::google::protobuf::internal::ToCachedSize(total_size);
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = cached_size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
  return total_size;
}

void TestBoolValue::MergeFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_merge_from_start:proto3.TestBoolValue)
  if (GOOGLE_PREDICT_FALSE(&from == this)) MergeFromFail(__LINE__);
  const TestBoolValue* source =
      ::google::protobuf::internal::DynamicCastToGenerated<const TestBoolValue>(
          &from);
  if (source == NULL) {
  // @@protoc_insertion_point(generalized_merge_from_cast_fail:proto3.TestBoolValue)
    ::google::protobuf::internal::ReflectionOps::Merge(from, this);
  } else {
  // @@protoc_insertion_point(generalized_merge_from_cast_success:proto3.TestBoolValue)
    UnsafeMergeFrom(*source);
  }
}

void TestBoolValue::MergeFrom(const TestBoolValue& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:proto3.TestBoolValue)
  if (GOOGLE_PREDICT_TRUE(&from != this)) {
    UnsafeMergeFrom(from);
  } else {
    MergeFromFail(__LINE__);
  }
}

void TestBoolValue::UnsafeMergeFrom(const TestBoolValue& from) {
  GOOGLE_DCHECK(&from != this);
  bool_map_.MergeFrom(from.bool_map_);
  if (from.bool_value() != 0) {
    set_bool_value(from.bool_value());
  }
}

void TestBoolValue::CopyFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_copy_from_start:proto3.TestBoolValue)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

void TestBoolValue::CopyFrom(const TestBoolValue& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:proto3.TestBoolValue)
  if (&from == this) return;
  Clear();
  UnsafeMergeFrom(from);
}

bool TestBoolValue::IsInitialized() const {

  return true;
}

void TestBoolValue::Swap(TestBoolValue* other) {
  if (other == this) return;
  InternalSwap(other);
}
void TestBoolValue::InternalSwap(TestBoolValue* other) {
  std::swap(bool_value_, other->bool_value_);
  bool_map_.Swap(&other->bool_map_);
  _internal_metadata_.Swap(&other->_internal_metadata_);
  std::swap(_cached_size_, other->_cached_size_);
}

::google::protobuf::Metadata TestBoolValue::GetMetadata() const {
  protobuf_AssignDescriptorsOnce();
  ::google::protobuf::Metadata metadata;
  metadata.descriptor = TestBoolValue_descriptor_;
  metadata.reflection = TestBoolValue_reflection_;
  return metadata;
}

#if PROTOBUF_INLINE_NOT_IN_HEADERS
// TestBoolValue

// optional bool bool_value = 1;
void TestBoolValue::clear_bool_value() {
  bool_value_ = false;
}
bool TestBoolValue::bool_value() const {
  // @@protoc_insertion_point(field_get:proto3.TestBoolValue.bool_value)
  return bool_value_;
}
void TestBoolValue::set_bool_value(bool value) {
  
  bool_value_ = value;
  // @@protoc_insertion_point(field_set:proto3.TestBoolValue.bool_value)
}

// map<bool, int32> bool_map = 2;
int TestBoolValue::bool_map_size() const {
  return bool_map_.size();
}
void TestBoolValue::clear_bool_map() {
  bool_map_.Clear();
}
 const ::google::protobuf::Map< bool, ::google::protobuf::int32 >&
TestBoolValue::bool_map() const {
  // @@protoc_insertion_point(field_map:proto3.TestBoolValue.bool_map)
  return bool_map_.GetMap();
}
 ::google::protobuf::Map< bool, ::google::protobuf::int32 >*
TestBoolValue::mutable_bool_map() {
  // @@protoc_insertion_point(field_mutable_map:proto3.TestBoolValue.bool_map)
  return bool_map_.MutableMap();
}

inline const TestBoolValue* TestBoolValue::internal_default_instance() {
  return &TestBoolValue_default_instance_.get();
}
#endif  // PROTOBUF_INLINE_NOT_IN_HEADERS

// ===================================================================

#if !defined(_MSC_VER) || _MSC_VER >= 1900
const int TestCustomJsonName::kValueFieldNumber;
#endif  // !defined(_MSC_VER) || _MSC_VER >= 1900

TestCustomJsonName::TestCustomJsonName()
  : ::google::protobuf::Message(), _internal_metadata_(NULL) {
  if (this != internal_default_instance()) protobuf_InitDefaults_google_2fprotobuf_2futil_2fjson_5fformat_5fproto3_2eproto();
  SharedCtor();
  // @@protoc_insertion_point(constructor:proto3.TestCustomJsonName)
}

void TestCustomJsonName::InitAsDefaultInstance() {
}

TestCustomJsonName::TestCustomJsonName(const TestCustomJsonName& from)
  : ::google::protobuf::Message(),
    _internal_metadata_(NULL) {
  SharedCtor();
  UnsafeMergeFrom(from);
  // @@protoc_insertion_point(copy_constructor:proto3.TestCustomJsonName)
}

void TestCustomJsonName::SharedCtor() {
  value_ = 0;
  _cached_size_ = 0;
}

TestCustomJsonName::~TestCustomJsonName() {
  // @@protoc_insertion_point(destructor:proto3.TestCustomJsonName)
  SharedDtor();
}

void TestCustomJsonName::SharedDtor() {
}

void TestCustomJsonName::SetCachedSize(int size) const {
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
}
const ::google::protobuf::Descriptor* TestCustomJsonName::descriptor() {
  protobuf_AssignDescriptorsOnce();
  return TestCustomJsonName_descriptor_;
}

const TestCustomJsonName& TestCustomJsonName::default_instance() {
  protobuf_InitDefaults_google_2fprotobuf_2futil_2fjson_5fformat_5fproto3_2eproto();
  return *internal_default_instance();
}

::google::protobuf::internal::ExplicitlyConstructed<TestCustomJsonName> TestCustomJsonName_default_instance_;

TestCustomJsonName* TestCustomJsonName::New(::google::protobuf::Arena* arena) const {
  TestCustomJsonName* n = new TestCustomJsonName;
  if (arena != NULL) {
    arena->Own(n);
  }
  return n;
}

void TestCustomJsonName::Clear() {
// @@protoc_insertion_point(message_clear_start:proto3.TestCustomJsonName)
  value_ = 0;
}

bool TestCustomJsonName::MergePartialFromCodedStream(
    ::google::protobuf::io::CodedInputStream* input) {
#define DO_(EXPRESSION) if (!GOOGLE_PREDICT_TRUE(EXPRESSION)) goto failure
  ::google::protobuf::uint32 tag;
  // @@protoc_insertion_point(parse_start:proto3.TestCustomJsonName)
  for (;;) {
    ::std::pair< ::google::protobuf::uint32, bool> p = input->ReadTagWithCutoff(127);
    tag = p.first;
    if (!p.second) goto handle_unusual;
    switch (::google::protobuf::internal::WireFormatLite::GetTagFieldNumber(tag)) {
      // optional int32 value = 1[json_name = "@value"];
      case 1: {
        if (tag == 8) {

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int32, ::google::protobuf::internal::WireFormatLite::TYPE_INT32>(
                 input, &value_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectAtEnd()) goto success;
        break;
      }

      default: {
      handle_unusual:
        if (tag == 0 ||
            ::google::protobuf::internal::WireFormatLite::GetTagWireType(tag) ==
            ::google::protobuf::internal::WireFormatLite::WIRETYPE_END_GROUP) {
          goto success;
        }
        DO_(::google::protobuf::internal::WireFormatLite::SkipField(input, tag));
        break;
      }
    }
  }
success:
  // @@protoc_insertion_point(parse_success:proto3.TestCustomJsonName)
  return true;
failure:
  // @@protoc_insertion_point(parse_failure:proto3.TestCustomJsonName)
  return false;
#undef DO_
}

void TestCustomJsonName::SerializeWithCachedSizes(
    ::google::protobuf::io::CodedOutputStream* output) const {
  // @@protoc_insertion_point(serialize_start:proto3.TestCustomJsonName)
  // optional int32 value = 1[json_name = "@value"];
  if (this->value() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt32(1, this->value(), output);
  }

  // @@protoc_insertion_point(serialize_end:proto3.TestCustomJsonName)
}

::google::protobuf::uint8* TestCustomJsonName::InternalSerializeWithCachedSizesToArray(
    bool deterministic, ::google::protobuf::uint8* target) const {
  (void)deterministic; // Unused
  // @@protoc_insertion_point(serialize_to_array_start:proto3.TestCustomJsonName)
  // optional int32 value = 1[json_name = "@value"];
  if (this->value() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt32ToArray(1, this->value(), target);
  }

  // @@protoc_insertion_point(serialize_to_array_end:proto3.TestCustomJsonName)
  return target;
}

size_t TestCustomJsonName::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:proto3.TestCustomJsonName)
  size_t total_size = 0;

  // optional int32 value = 1[json_name = "@value"];
  if (this->value() != 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::Int32Size(
        this->value());
  }

  int cached_size = ::google::protobuf::internal::ToCachedSize(total_size);
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = cached_size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
  return total_size;
}

void TestCustomJsonName::MergeFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_merge_from_start:proto3.TestCustomJsonName)
  if (GOOGLE_PREDICT_FALSE(&from == this)) MergeFromFail(__LINE__);
  const TestCustomJsonName* source =
      ::google::protobuf::internal::DynamicCastToGenerated<const TestCustomJsonName>(
          &from);
  if (source == NULL) {
  // @@protoc_insertion_point(generalized_merge_from_cast_fail:proto3.TestCustomJsonName)
    ::google::protobuf::internal::ReflectionOps::Merge(from, this);
  } else {
  // @@protoc_insertion_point(generalized_merge_from_cast_success:proto3.TestCustomJsonName)
    UnsafeMergeFrom(*source);
  }
}

void TestCustomJsonName::MergeFrom(const TestCustomJsonName& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:proto3.TestCustomJsonName)
  if (GOOGLE_PREDICT_TRUE(&from != this)) {
    UnsafeMergeFrom(from);
  } else {
    MergeFromFail(__LINE__);
  }
}

void TestCustomJsonName::UnsafeMergeFrom(const TestCustomJsonName& from) {
  GOOGLE_DCHECK(&from != this);
  if (from.value() != 0) {
    set_value(from.value());
  }
}

void TestCustomJsonName::CopyFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_copy_from_start:proto3.TestCustomJsonName)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

void TestCustomJsonName::CopyFrom(const TestCustomJsonName& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:proto3.TestCustomJsonName)
  if (&from == this) return;
  Clear();
  UnsafeMergeFrom(from);
}

bool TestCustomJsonName::IsInitialized() const {

  return true;
}

void TestCustomJsonName::Swap(TestCustomJsonName* other) {
  if (other == this) return;
  InternalSwap(other);
}
void TestCustomJsonName::InternalSwap(TestCustomJsonName* other) {
  std::swap(value_, other->value_);
  _internal_metadata_.Swap(&other->_internal_metadata_);
  std::swap(_cached_size_, other->_cached_size_);
}

::google::protobuf::Metadata TestCustomJsonName::GetMetadata() const {
  protobuf_AssignDescriptorsOnce();
  ::google::protobuf::Metadata metadata;
  metadata.descriptor = TestCustomJsonName_descriptor_;
  metadata.reflection = TestCustomJsonName_reflection_;
  return metadata;
}

#if PROTOBUF_INLINE_NOT_IN_HEADERS
// TestCustomJsonName

// optional int32 value = 1[json_name = "@value"];
void TestCustomJsonName::clear_value() {
  value_ = 0;
}
::google::protobuf::int32 TestCustomJsonName::value() const {
  // @@protoc_insertion_point(field_get:proto3.TestCustomJsonName.value)
  return value_;
}
void TestCustomJsonName::set_value(::google::protobuf::int32 value) {
  
  value_ = value;
  // @@protoc_insertion_point(field_set:proto3.TestCustomJsonName.value)
}

inline const TestCustomJsonName* TestCustomJsonName::internal_default_instance() {
  return &TestCustomJsonName_default_instance_.get();
}
#endif  // PROTOBUF_INLINE_NOT_IN_HEADERS

// ===================================================================

#if !defined(_MSC_VER) || _MSC_VER >= 1900
const int TestExtensions::kExtensionsFieldNumber;
#endif  // !defined(_MSC_VER) || _MSC_VER >= 1900

TestExtensions::TestExtensions()
  : ::google::protobuf::Message(), _internal_metadata_(NULL) {
  if (this != internal_default_instance()) protobuf_InitDefaults_google_2fprotobuf_2futil_2fjson_5fformat_5fproto3_2eproto();
  SharedCtor();
  // @@protoc_insertion_point(constructor:proto3.TestExtensions)
}

void TestExtensions::InitAsDefaultInstance() {
  extensions_ = const_cast< ::protobuf_unittest::TestAllExtensions*>(
      ::protobuf_unittest::TestAllExtensions::internal_default_instance());
}

TestExtensions::TestExtensions(const TestExtensions& from)
  : ::google::protobuf::Message(),
    _internal_metadata_(NULL) {
  SharedCtor();
  UnsafeMergeFrom(from);
  // @@protoc_insertion_point(copy_constructor:proto3.TestExtensions)
}

void TestExtensions::SharedCtor() {
  extensions_ = NULL;
  _cached_size_ = 0;
}

TestExtensions::~TestExtensions() {
  // @@protoc_insertion_point(destructor:proto3.TestExtensions)
  SharedDtor();
}

void TestExtensions::SharedDtor() {
  if (this != &TestExtensions_default_instance_.get()) {
    delete extensions_;
  }
}

void TestExtensions::SetCachedSize(int size) const {
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
}
const ::google::protobuf::Descriptor* TestExtensions::descriptor() {
  protobuf_AssignDescriptorsOnce();
  return TestExtensions_descriptor_;
}

const TestExtensions& TestExtensions::default_instance() {
  protobuf_InitDefaults_google_2fprotobuf_2futil_2fjson_5fformat_5fproto3_2eproto();
  return *internal_default_instance();
}

::google::protobuf::internal::ExplicitlyConstructed<TestExtensions> TestExtensions_default_instance_;

TestExtensions* TestExtensions::New(::google::protobuf::Arena* arena) const {
  TestExtensions* n = new TestExtensions;
  if (arena != NULL) {
    arena->Own(n);
  }
  return n;
}

void TestExtensions::Clear() {
// @@protoc_insertion_point(message_clear_start:proto3.TestExtensions)
  if (GetArenaNoVirtual() == NULL && extensions_ != NULL) delete extensions_;
  extensions_ = NULL;
}

bool TestExtensions::MergePartialFromCodedStream(
    ::google::protobuf::io::CodedInputStream* input) {
#define DO_(EXPRESSION) if (!GOOGLE_PREDICT_TRUE(EXPRESSION)) goto failure
  ::google::protobuf::uint32 tag;
  // @@protoc_insertion_point(parse_start:proto3.TestExtensions)
  for (;;) {
    ::std::pair< ::google::protobuf::uint32, bool> p = input->ReadTagWithCutoff(127);
    tag = p.first;
    if (!p.second) goto handle_unusual;
    switch (::google::protobuf::internal::WireFormatLite::GetTagFieldNumber(tag)) {
      // optional .protobuf_unittest.TestAllExtensions extensions = 1;
      case 1: {
        if (tag == 10) {
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessageNoVirtual(
               input, mutable_extensions()));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectAtEnd()) goto success;
        break;
      }

      default: {
      handle_unusual:
        if (tag == 0 ||
            ::google::protobuf::internal::WireFormatLite::GetTagWireType(tag) ==
            ::google::protobuf::internal::WireFormatLite::WIRETYPE_END_GROUP) {
          goto success;
        }
        DO_(::google::protobuf::internal::WireFormatLite::SkipField(input, tag));
        break;
      }
    }
  }
success:
  // @@protoc_insertion_point(parse_success:proto3.TestExtensions)
  return true;
failure:
  // @@protoc_insertion_point(parse_failure:proto3.TestExtensions)
  return false;
#undef DO_
}

void TestExtensions::SerializeWithCachedSizes(
    ::google::protobuf::io::CodedOutputStream* output) const {
  // @@protoc_insertion_point(serialize_start:proto3.TestExtensions)
  // optional .protobuf_unittest.TestAllExtensions extensions = 1;
  if (this->has_extensions()) {
    ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
      1, *this->extensions_, output);
  }

  // @@protoc_insertion_point(serialize_end:proto3.TestExtensions)
}

::google::protobuf::uint8* TestExtensions::InternalSerializeWithCachedSizesToArray(
    bool deterministic, ::google::protobuf::uint8* target) const {
  (void)deterministic; // Unused
  // @@protoc_insertion_point(serialize_to_array_start:proto3.TestExtensions)
  // optional .protobuf_unittest.TestAllExtensions extensions = 1;
  if (this->has_extensions()) {
    target = ::google::protobuf::internal::WireFormatLite::
      InternalWriteMessageNoVirtualToArray(
        1, *this->extensions_, false, target);
  }

  // @@protoc_insertion_point(serialize_to_array_end:proto3.TestExtensions)
  return target;
}

size_t TestExtensions::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:proto3.TestExtensions)
  size_t total_size = 0;

  // optional .protobuf_unittest.TestAllExtensions extensions = 1;
  if (this->has_extensions()) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::MessageSizeNoVirtual(
        *this->extensions_);
  }

  int cached_size = ::google::protobuf::internal::ToCachedSize(total_size);
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = cached_size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
  return total_size;
}

void TestExtensions::MergeFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_merge_from_start:proto3.TestExtensions)
  if (GOOGLE_PREDICT_FALSE(&from == this)) MergeFromFail(__LINE__);
  const TestExtensions* source =
      ::google::protobuf::internal::DynamicCastToGenerated<const TestExtensions>(
          &from);
  if (source == NULL) {
  // @@protoc_insertion_point(generalized_merge_from_cast_fail:proto3.TestExtensions)
    ::google::protobuf::internal::ReflectionOps::Merge(from, this);
  } else {
  // @@protoc_insertion_point(generalized_merge_from_cast_success:proto3.TestExtensions)
    UnsafeMergeFrom(*source);
  }
}

void TestExtensions::MergeFrom(const TestExtensions& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:proto3.TestExtensions)
  if (GOOGLE_PREDICT_TRUE(&from != this)) {
    UnsafeMergeFrom(from);
  } else {
    MergeFromFail(__LINE__);
  }
}

void TestExtensions::UnsafeMergeFrom(const TestExtensions& from) {
  GOOGLE_DCHECK(&from != this);
  if (from.has_extensions()) {
    mutable_extensions()->::protobuf_unittest::TestAllExtensions::MergeFrom(from.extensions());
  }
}

void TestExtensions::CopyFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_copy_from_start:proto3.TestExtensions)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

void TestExtensions::CopyFrom(const TestExtensions& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:proto3.TestExtensions)
  if (&from == this) return;
  Clear();
  UnsafeMergeFrom(from);
}

bool TestExtensions::IsInitialized() const {

  if (has_extensions()) {
    if (!this->extensions_->IsInitialized()) return false;
  }
  return true;
}

void TestExtensions::Swap(TestExtensions* other) {
  if (other == this) return;
  InternalSwap(other);
}
void TestExtensions::InternalSwap(TestExtensions* other) {
  std::swap(extensions_, other->extensions_);
  _internal_metadata_.Swap(&other->_internal_metadata_);
  std::swap(_cached_size_, other->_cached_size_);
}

::google::protobuf::Metadata TestExtensions::GetMetadata() const {
  protobuf_AssignDescriptorsOnce();
  ::google::protobuf::Metadata metadata;
  metadata.descriptor = TestExtensions_descriptor_;
  metadata.reflection = TestExtensions_reflection_;
  return metadata;
}

#if PROTOBUF_INLINE_NOT_IN_HEADERS
// TestExtensions

// optional .protobuf_unittest.TestAllExtensions extensions = 1;
bool TestExtensions::has_extensions() const {
  return this != internal_default_instance() && extensions_ != NULL;
}
void TestExtensions::clear_extensions() {
  if (GetArenaNoVirtual() == NULL && extensions_ != NULL) delete extensions_;
  extensions_ = NULL;
}
const ::protobuf_unittest::TestAllExtensions& TestExtensions::extensions() const {
  // @@protoc_insertion_point(field_get:proto3.TestExtensions.extensions)
  return extensions_ != NULL ? *extensions_
                         : *::protobuf_unittest::TestAllExtensions::internal_default_instance();
}
::protobuf_unittest::TestAllExtensions* TestExtensions::mutable_extensions() {
  
  if (extensions_ == NULL) {
    extensions_ = new ::protobuf_unittest::TestAllExtensions;
  }
  // @@protoc_insertion_point(field_mutable:proto3.TestExtensions.extensions)
  return extensions_;
}
::protobuf_unittest::TestAllExtensions* TestExtensions::release_extensions() {
  // @@protoc_insertion_point(field_release:proto3.TestExtensions.extensions)
  
  ::protobuf_unittest::TestAllExtensions* temp = extensions_;
  extensions_ = NULL;
  return temp;
}
void TestExtensions::set_allocated_extensions(::protobuf_unittest::TestAllExtensions* extensions) {
  delete extensions_;
  if (extensions != NULL && extensions->GetArena() != NULL) {
    ::protobuf_unittest::TestAllExtensions* new_extensions = new ::protobuf_unittest::TestAllExtensions;
    new_extensions->CopyFrom(*extensions);
    extensions = new_extensions;
  }
  extensions_ = extensions;
  if (extensions) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_set_allocated:proto3.TestExtensions.extensions)
}

inline const TestExtensions* TestExtensions::internal_default_instance() {
  return &TestExtensions_default_instance_.get();
}
#endif  // PROTOBUF_INLINE_NOT_IN_HEADERS

// @@protoc_insertion_point(namespace_scope)

}  // namespace proto3

// @@protoc_insertion_point(global_scope)

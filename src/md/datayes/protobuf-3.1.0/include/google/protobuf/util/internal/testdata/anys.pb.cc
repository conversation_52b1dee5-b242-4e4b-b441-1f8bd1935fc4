// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: google/protobuf/util/internal/testdata/anys.proto

#define INTERNAL_SUPPRESS_PROTOBUF_FIELD_DEPRECATION
#include <google/protobuf/util/internal/testdata/anys.pb.h>

#include <algorithm>

#include <google/protobuf/stubs/common.h>
#include <google/protobuf/stubs/port.h>
#include <google/protobuf/stubs/once.h>
#include <google/protobuf/io/coded_stream.h>
#include <google/protobuf/wire_format_lite_inl.h>
#include <google/protobuf/descriptor.h>
#include <google/protobuf/generated_message_reflection.h>
#include <google/protobuf/reflection_ops.h>
#include <google/protobuf/wire_format.h>
// @@protoc_insertion_point(includes)

namespace google {
namespace protobuf {
namespace testing {

namespace {

const ::google::protobuf::Descriptor* AnyTestCases_descriptor_ = NULL;
const ::google::protobuf::internal::GeneratedMessageReflection*
  AnyTestCases_reflection_ = NULL;
const ::google::protobuf::Descriptor* AnyWrapper_descriptor_ = NULL;
const ::google::protobuf::internal::GeneratedMessageReflection*
  AnyWrapper_reflection_ = NULL;
const ::google::protobuf::Descriptor* Imports_descriptor_ = NULL;
const ::google::protobuf::internal::GeneratedMessageReflection*
  Imports_reflection_ = NULL;
const ::google::protobuf::Descriptor* Data_descriptor_ = NULL;
const ::google::protobuf::internal::GeneratedMessageReflection*
  Data_reflection_ = NULL;
const ::google::protobuf::Descriptor* Data_MapDataEntry_descriptor_ = NULL;
const ::google::protobuf::Descriptor* AnyIn_descriptor_ = NULL;
const ::google::protobuf::internal::GeneratedMessageReflection*
  AnyIn_reflection_ = NULL;
const ::google::protobuf::Descriptor* AnyOut_descriptor_ = NULL;
const ::google::protobuf::internal::GeneratedMessageReflection*
  AnyOut_reflection_ = NULL;
const ::google::protobuf::Descriptor* AnyM_descriptor_ = NULL;
const ::google::protobuf::internal::GeneratedMessageReflection*
  AnyM_reflection_ = NULL;

}  // namespace


void protobuf_AssignDesc_google_2fprotobuf_2futil_2finternal_2ftestdata_2fanys_2eproto() GOOGLE_ATTRIBUTE_COLD;
void protobuf_AssignDesc_google_2fprotobuf_2futil_2finternal_2ftestdata_2fanys_2eproto() {
  protobuf_AddDesc_google_2fprotobuf_2futil_2finternal_2ftestdata_2fanys_2eproto();
  const ::google::protobuf::FileDescriptor* file =
    ::google::protobuf::DescriptorPool::generated_pool()->FindFileByName(
      "google/protobuf/util/internal/testdata/anys.proto");
  GOOGLE_CHECK(file != NULL);
  AnyTestCases_descriptor_ = file->message_type(0);
  static const int AnyTestCases_offsets_[17] = {
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(AnyTestCases, empty_any_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(AnyTestCases, type_only_any_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(AnyTestCases, wrapper_any_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(AnyTestCases, any_with_timestamp_value_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(AnyTestCases, any_with_duration_value_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(AnyTestCases, any_with_struct_value_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(AnyTestCases, recursive_any_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(AnyTestCases, any_with_message_value_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(AnyTestCases, any_with_nested_message_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(AnyTestCases, any_with_message_with_wrapper_type_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(AnyTestCases, any_with_message_with_timestamp_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(AnyTestCases, any_with_message_containing_map_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(AnyTestCases, any_with_message_containing_struct_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(AnyTestCases, any_with_message_containing_repeated_message_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(AnyTestCases, recursive_any_with_type_field_at_end_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(AnyTestCases, top_level_any_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(AnyTestCases, top_level_any_with_type_field_at_end_),
  };
  AnyTestCases_reflection_ =
    ::google::protobuf::internal::GeneratedMessageReflection::NewGeneratedMessageReflection(
      AnyTestCases_descriptor_,
      AnyTestCases::internal_default_instance(),
      AnyTestCases_offsets_,
      -1,
      -1,
      -1,
      sizeof(AnyTestCases),
      GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(AnyTestCases, _internal_metadata_));
  AnyWrapper_descriptor_ = file->message_type(1);
  static const int AnyWrapper_offsets_[1] = {
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(AnyWrapper, any_),
  };
  AnyWrapper_reflection_ =
    ::google::protobuf::internal::GeneratedMessageReflection::NewGeneratedMessageReflection(
      AnyWrapper_descriptor_,
      AnyWrapper::internal_default_instance(),
      AnyWrapper_offsets_,
      -1,
      -1,
      -1,
      sizeof(AnyWrapper),
      GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(AnyWrapper, _internal_metadata_));
  Imports_descriptor_ = file->message_type(2);
  static const int Imports_offsets_[6] = {
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(Imports, dbl_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(Imports, struct__),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(Imports, timestamp_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(Imports, duration_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(Imports, i32_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(Imports, data_),
  };
  Imports_reflection_ =
    ::google::protobuf::internal::GeneratedMessageReflection::NewGeneratedMessageReflection(
      Imports_descriptor_,
      Imports::internal_default_instance(),
      Imports_offsets_,
      -1,
      -1,
      -1,
      sizeof(Imports),
      GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(Imports, _internal_metadata_));
  Data_descriptor_ = file->message_type(3);
  static const int Data_offsets_[9] = {
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(Data, attr_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(Data, str_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(Data, msgs_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(Data, nested_data_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(Data, int_wrapper_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(Data, time_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(Data, map_data_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(Data, struct_data_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(Data, repeated_data_),
  };
  Data_reflection_ =
    ::google::protobuf::internal::GeneratedMessageReflection::NewGeneratedMessageReflection(
      Data_descriptor_,
      Data::internal_default_instance(),
      Data_offsets_,
      -1,
      -1,
      -1,
      sizeof(Data),
      GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(Data, _internal_metadata_));
  Data_MapDataEntry_descriptor_ = Data_descriptor_->nested_type(0);
  AnyIn_descriptor_ = file->message_type(4);
  static const int AnyIn_offsets_[2] = {
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(AnyIn, something_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(AnyIn, any_),
  };
  AnyIn_reflection_ =
    ::google::protobuf::internal::GeneratedMessageReflection::NewGeneratedMessageReflection(
      AnyIn_descriptor_,
      AnyIn::internal_default_instance(),
      AnyIn_offsets_,
      -1,
      -1,
      -1,
      sizeof(AnyIn),
      GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(AnyIn, _internal_metadata_));
  AnyOut_descriptor_ = file->message_type(5);
  static const int AnyOut_offsets_[1] = {
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(AnyOut, any_),
  };
  AnyOut_reflection_ =
    ::google::protobuf::internal::GeneratedMessageReflection::NewGeneratedMessageReflection(
      AnyOut_descriptor_,
      AnyOut::internal_default_instance(),
      AnyOut_offsets_,
      -1,
      -1,
      -1,
      sizeof(AnyOut),
      GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(AnyOut, _internal_metadata_));
  AnyM_descriptor_ = file->message_type(6);
  static const int AnyM_offsets_[1] = {
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(AnyM, foo_),
  };
  AnyM_reflection_ =
    ::google::protobuf::internal::GeneratedMessageReflection::NewGeneratedMessageReflection(
      AnyM_descriptor_,
      AnyM::internal_default_instance(),
      AnyM_offsets_,
      -1,
      -1,
      -1,
      sizeof(AnyM),
      GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(AnyM, _internal_metadata_));
}

namespace {

GOOGLE_PROTOBUF_DECLARE_ONCE(protobuf_AssignDescriptors_once_);
void protobuf_AssignDescriptorsOnce() {
  ::google::protobuf::GoogleOnceInit(&protobuf_AssignDescriptors_once_,
                 &protobuf_AssignDesc_google_2fprotobuf_2futil_2finternal_2ftestdata_2fanys_2eproto);
}

void protobuf_RegisterTypes(const ::std::string&) GOOGLE_ATTRIBUTE_COLD;
void protobuf_RegisterTypes(const ::std::string&) {
  protobuf_AssignDescriptorsOnce();
  ::google::protobuf::MessageFactory::InternalRegisterGeneratedMessage(
      AnyTestCases_descriptor_, AnyTestCases::internal_default_instance());
  ::google::protobuf::MessageFactory::InternalRegisterGeneratedMessage(
      AnyWrapper_descriptor_, AnyWrapper::internal_default_instance());
  ::google::protobuf::MessageFactory::InternalRegisterGeneratedMessage(
      Imports_descriptor_, Imports::internal_default_instance());
  ::google::protobuf::MessageFactory::InternalRegisterGeneratedMessage(
      Data_descriptor_, Data::internal_default_instance());
  ::google::protobuf::MessageFactory::InternalRegisterGeneratedMessage(
        Data_MapDataEntry_descriptor_,
        ::google::protobuf::internal::MapEntry<
            ::std::string,
            ::std::string,
            ::google::protobuf::internal::WireFormatLite::TYPE_STRING,
            ::google::protobuf::internal::WireFormatLite::TYPE_STRING,
            0>::CreateDefaultInstance(
                Data_MapDataEntry_descriptor_));
  ::google::protobuf::MessageFactory::InternalRegisterGeneratedMessage(
      AnyIn_descriptor_, AnyIn::internal_default_instance());
  ::google::protobuf::MessageFactory::InternalRegisterGeneratedMessage(
      AnyOut_descriptor_, AnyOut::internal_default_instance());
  ::google::protobuf::MessageFactory::InternalRegisterGeneratedMessage(
      AnyM_descriptor_, AnyM::internal_default_instance());
}

}  // namespace

void protobuf_ShutdownFile_google_2fprotobuf_2futil_2finternal_2ftestdata_2fanys_2eproto() {
  AnyTestCases_default_instance_.Shutdown();
  delete AnyTestCases_reflection_;
  AnyWrapper_default_instance_.Shutdown();
  delete AnyWrapper_reflection_;
  Imports_default_instance_.Shutdown();
  delete Imports_reflection_;
  Data_default_instance_.Shutdown();
  delete Data_reflection_;
  AnyIn_default_instance_.Shutdown();
  delete AnyIn_reflection_;
  AnyOut_default_instance_.Shutdown();
  delete AnyOut_reflection_;
  AnyM_default_instance_.Shutdown();
  delete AnyM_reflection_;
}

void protobuf_InitDefaults_google_2fprotobuf_2futil_2finternal_2ftestdata_2fanys_2eproto_impl() {
  GOOGLE_PROTOBUF_VERIFY_VERSION;

  ::google::protobuf::protobuf_InitDefaults_google_2fprotobuf_2fany_2eproto();
  ::google::protobuf::protobuf_InitDefaults_google_2fprotobuf_2fstruct_2eproto();
  ::google::protobuf::protobuf_InitDefaults_google_2fprotobuf_2ftimestamp_2eproto();
  ::google::protobuf::protobuf_InitDefaults_google_2fprotobuf_2fduration_2eproto();
  ::google::protobuf::protobuf_InitDefaults_google_2fprotobuf_2fwrappers_2eproto();
  AnyTestCases_default_instance_.DefaultConstruct();
  AnyWrapper_default_instance_.DefaultConstruct();
  Imports_default_instance_.DefaultConstruct();
  ::google::protobuf::internal::GetEmptyString();
  Data_default_instance_.DefaultConstruct();
  ::google::protobuf::internal::GetEmptyString();
  ::google::protobuf::internal::GetEmptyString();
  AnyIn_default_instance_.DefaultConstruct();
  AnyOut_default_instance_.DefaultConstruct();
  ::google::protobuf::internal::GetEmptyString();
  AnyM_default_instance_.DefaultConstruct();
  AnyTestCases_default_instance_.get_mutable()->InitAsDefaultInstance();
  AnyWrapper_default_instance_.get_mutable()->InitAsDefaultInstance();
  Imports_default_instance_.get_mutable()->InitAsDefaultInstance();
  Data_default_instance_.get_mutable()->InitAsDefaultInstance();
  AnyIn_default_instance_.get_mutable()->InitAsDefaultInstance();
  AnyOut_default_instance_.get_mutable()->InitAsDefaultInstance();
  AnyM_default_instance_.get_mutable()->InitAsDefaultInstance();
}

GOOGLE_PROTOBUF_DECLARE_ONCE(protobuf_InitDefaults_google_2fprotobuf_2futil_2finternal_2ftestdata_2fanys_2eproto_once_);
void protobuf_InitDefaults_google_2fprotobuf_2futil_2finternal_2ftestdata_2fanys_2eproto() {
  ::google::protobuf::GoogleOnceInit(&protobuf_InitDefaults_google_2fprotobuf_2futil_2finternal_2ftestdata_2fanys_2eproto_once_,
                 &protobuf_InitDefaults_google_2fprotobuf_2futil_2finternal_2ftestdata_2fanys_2eproto_impl);
}
void protobuf_AddDesc_google_2fprotobuf_2futil_2finternal_2ftestdata_2fanys_2eproto_impl() {
  GOOGLE_PROTOBUF_VERIFY_VERSION;

  protobuf_InitDefaults_google_2fprotobuf_2futil_2finternal_2ftestdata_2fanys_2eproto();
  ::google::protobuf::DescriptorPool::InternalAddGeneratedFile(
    "\n1google/protobuf/util/internal/testdata"
    "/anys.proto\022\027google.protobuf.testing\032\031go"
    "ogle/protobuf/any.proto\032\034google/protobuf"
    "/struct.proto\032\037google/protobuf/timestamp"
    ".proto\032\036google/protobuf/duration.proto\032\036"
    "google/protobuf/wrappers.proto\"\261\t\n\014AnyTe"
    "stCases\0226\n\tempty_any\030\001 \001(\0132#.google.prot"
    "obuf.testing.AnyWrapper\022:\n\rtype_only_any"
    "\030\002 \001(\0132#.google.protobuf.testing.AnyWrap"
    "per\0228\n\013wrapper_any\030\003 \001(\0132#.google.protob"
    "uf.testing.AnyWrapper\022E\n\030any_with_timest"
    "amp_value\030\004 \001(\0132#.google.protobuf.testin"
    "g.AnyWrapper\022D\n\027any_with_duration_value\030"
    "\005 \001(\0132#.google.protobuf.testing.AnyWrapp"
    "er\022B\n\025any_with_struct_value\030\006 \001(\0132#.goog"
    "le.protobuf.testing.AnyWrapper\022:\n\rrecurs"
    "ive_any\030\007 \001(\0132#.google.protobuf.testing."
    "AnyWrapper\022C\n\026any_with_message_value\030\010 \001"
    "(\0132#.google.protobuf.testing.AnyWrapper\022"
    "D\n\027any_with_nested_message\030\t \001(\0132#.googl"
    "e.protobuf.testing.AnyWrapper\022O\n\"any_wit"
    "h_message_with_wrapper_type\030\n \001(\0132#.goog"
    "le.protobuf.testing.AnyWrapper\022L\n\037any_wi"
    "th_message_with_timestamp\030\013 \001(\0132#.google"
    ".protobuf.testing.AnyWrapper\022L\n\037any_with"
    "_message_containing_map\030\014 \001(\0132#.google.p"
    "rotobuf.testing.AnyWrapper\022O\n\"any_with_m"
    "essage_containing_struct\030\r \001(\0132#.google."
    "protobuf.testing.AnyWrapper\022Y\n,any_with_"
    "message_containing_repeated_message\030\016 \001("
    "\0132#.google.protobuf.testing.AnyWrapper\022Q"
    "\n$recursive_any_with_type_field_at_end\030\017"
    " \001(\0132#.google.protobuf.testing.AnyWrappe"
    "r\022+\n\rtop_level_any\0302 \001(\0132\024.google.protob"
    "uf.Any\022B\n$top_level_any_with_type_field_"
    "at_end\0303 \001(\0132\024.google.protobuf.Any\"/\n\nAn"
    "yWrapper\022!\n\003any\030\001 \001(\0132\024.google.protobuf."
    "Any\"\220\002\n\007Imports\022)\n\003dbl\030\001 \001(\0132\034.google.pr"
    "otobuf.DoubleValue\022\'\n\006struct\030\002 \001(\0132\027.goo"
    "gle.protobuf.Struct\022-\n\ttimestamp\030\003 \001(\0132\032"
    ".google.protobuf.Timestamp\022+\n\010duration\030\004"
    " \001(\0132\031.google.protobuf.Duration\022(\n\003i32\030\005"
    " \001(\0132\033.google.protobuf.Int32Value\022+\n\004dat"
    "a\030d \001(\0132\035.google.protobuf.testing.Data\"\221"
    "\003\n\004Data\022\014\n\004attr\030\001 \001(\005\022\013\n\003str\030\002 \001(\t\022\014\n\004ms"
    "gs\030\003 \003(\t\0222\n\013nested_data\030\004 \001(\0132\035.google.p"
    "rotobuf.testing.Data\0220\n\013int_wrapper\030\005 \001("
    "\0132\033.google.protobuf.Int32Value\022(\n\004time\030\006"
    " \001(\0132\032.google.protobuf.Timestamp\022<\n\010map_"
    "data\030\007 \003(\0132*.google.protobuf.testing.Dat"
    "a.MapDataEntry\022,\n\013struct_data\030\010 \001(\0132\027.go"
    "ogle.protobuf.Struct\0224\n\rrepeated_data\030\t "
    "\003(\0132\035.google.protobuf.testing.Data\032.\n\014Ma"
    "pDataEntry\022\013\n\003key\030\001 \001(\t\022\r\n\005value\030\002 \001(\t:\002"
    "8\001\"=\n\005AnyIn\022\021\n\tsomething\030\001 \001(\t\022!\n\003any\030\002 "
    "\001(\0132\024.google.protobuf.Any\"+\n\006AnyOut\022!\n\003a"
    "ny\030\001 \001(\0132\024.google.protobuf.Any\"\023\n\004AnyM\022\013"
    "\n\003foo\030\001 \001(\t2\263\001\n\016AnyTestService\022T\n\004Call\022%"
    ".google.protobuf.testing.AnyTestCases\032%."
    "google.protobuf.testing.AnyTestCases\022K\n\005"
    "Call1\022 .google.protobuf.testing.Imports\032"
    " .google.protobuf.testing.Importsb\006proto"
    "3", 2481);
  ::google::protobuf::MessageFactory::InternalRegisterGeneratedFile(
    "google/protobuf/util/internal/testdata/anys.proto", &protobuf_RegisterTypes);
  ::google::protobuf::protobuf_AddDesc_google_2fprotobuf_2fany_2eproto();
  ::google::protobuf::protobuf_AddDesc_google_2fprotobuf_2fstruct_2eproto();
  ::google::protobuf::protobuf_AddDesc_google_2fprotobuf_2ftimestamp_2eproto();
  ::google::protobuf::protobuf_AddDesc_google_2fprotobuf_2fduration_2eproto();
  ::google::protobuf::protobuf_AddDesc_google_2fprotobuf_2fwrappers_2eproto();
  ::google::protobuf::internal::OnShutdown(&protobuf_ShutdownFile_google_2fprotobuf_2futil_2finternal_2ftestdata_2fanys_2eproto);
}

GOOGLE_PROTOBUF_DECLARE_ONCE(protobuf_AddDesc_google_2fprotobuf_2futil_2finternal_2ftestdata_2fanys_2eproto_once_);
void protobuf_AddDesc_google_2fprotobuf_2futil_2finternal_2ftestdata_2fanys_2eproto() {
  ::google::protobuf::GoogleOnceInit(&protobuf_AddDesc_google_2fprotobuf_2futil_2finternal_2ftestdata_2fanys_2eproto_once_,
                 &protobuf_AddDesc_google_2fprotobuf_2futil_2finternal_2ftestdata_2fanys_2eproto_impl);
}
// Force AddDescriptors() to be called at static initialization time.
struct StaticDescriptorInitializer_google_2fprotobuf_2futil_2finternal_2ftestdata_2fanys_2eproto {
  StaticDescriptorInitializer_google_2fprotobuf_2futil_2finternal_2ftestdata_2fanys_2eproto() {
    protobuf_AddDesc_google_2fprotobuf_2futil_2finternal_2ftestdata_2fanys_2eproto();
  }
} static_descriptor_initializer_google_2fprotobuf_2futil_2finternal_2ftestdata_2fanys_2eproto_;

namespace {

static void MergeFromFail(int line) GOOGLE_ATTRIBUTE_COLD GOOGLE_ATTRIBUTE_NORETURN;
static void MergeFromFail(int line) {
  ::google::protobuf::internal::MergeFromFail(__FILE__, line);
}

}  // namespace


// ===================================================================

#if !defined(_MSC_VER) || _MSC_VER >= 1900
const int AnyTestCases::kEmptyAnyFieldNumber;
const int AnyTestCases::kTypeOnlyAnyFieldNumber;
const int AnyTestCases::kWrapperAnyFieldNumber;
const int AnyTestCases::kAnyWithTimestampValueFieldNumber;
const int AnyTestCases::kAnyWithDurationValueFieldNumber;
const int AnyTestCases::kAnyWithStructValueFieldNumber;
const int AnyTestCases::kRecursiveAnyFieldNumber;
const int AnyTestCases::kAnyWithMessageValueFieldNumber;
const int AnyTestCases::kAnyWithNestedMessageFieldNumber;
const int AnyTestCases::kAnyWithMessageWithWrapperTypeFieldNumber;
const int AnyTestCases::kAnyWithMessageWithTimestampFieldNumber;
const int AnyTestCases::kAnyWithMessageContainingMapFieldNumber;
const int AnyTestCases::kAnyWithMessageContainingStructFieldNumber;
const int AnyTestCases::kAnyWithMessageContainingRepeatedMessageFieldNumber;
const int AnyTestCases::kRecursiveAnyWithTypeFieldAtEndFieldNumber;
const int AnyTestCases::kTopLevelAnyFieldNumber;
const int AnyTestCases::kTopLevelAnyWithTypeFieldAtEndFieldNumber;
#endif  // !defined(_MSC_VER) || _MSC_VER >= 1900

AnyTestCases::AnyTestCases()
  : ::google::protobuf::Message(), _internal_metadata_(NULL) {
  if (this != internal_default_instance()) protobuf_InitDefaults_google_2fprotobuf_2futil_2finternal_2ftestdata_2fanys_2eproto();
  SharedCtor();
  // @@protoc_insertion_point(constructor:google.protobuf.testing.AnyTestCases)
}

void AnyTestCases::InitAsDefaultInstance() {
  empty_any_ = const_cast< ::google::protobuf::testing::AnyWrapper*>(
      ::google::protobuf::testing::AnyWrapper::internal_default_instance());
  type_only_any_ = const_cast< ::google::protobuf::testing::AnyWrapper*>(
      ::google::protobuf::testing::AnyWrapper::internal_default_instance());
  wrapper_any_ = const_cast< ::google::protobuf::testing::AnyWrapper*>(
      ::google::protobuf::testing::AnyWrapper::internal_default_instance());
  any_with_timestamp_value_ = const_cast< ::google::protobuf::testing::AnyWrapper*>(
      ::google::protobuf::testing::AnyWrapper::internal_default_instance());
  any_with_duration_value_ = const_cast< ::google::protobuf::testing::AnyWrapper*>(
      ::google::protobuf::testing::AnyWrapper::internal_default_instance());
  any_with_struct_value_ = const_cast< ::google::protobuf::testing::AnyWrapper*>(
      ::google::protobuf::testing::AnyWrapper::internal_default_instance());
  recursive_any_ = const_cast< ::google::protobuf::testing::AnyWrapper*>(
      ::google::protobuf::testing::AnyWrapper::internal_default_instance());
  any_with_message_value_ = const_cast< ::google::protobuf::testing::AnyWrapper*>(
      ::google::protobuf::testing::AnyWrapper::internal_default_instance());
  any_with_nested_message_ = const_cast< ::google::protobuf::testing::AnyWrapper*>(
      ::google::protobuf::testing::AnyWrapper::internal_default_instance());
  any_with_message_with_wrapper_type_ = const_cast< ::google::protobuf::testing::AnyWrapper*>(
      ::google::protobuf::testing::AnyWrapper::internal_default_instance());
  any_with_message_with_timestamp_ = const_cast< ::google::protobuf::testing::AnyWrapper*>(
      ::google::protobuf::testing::AnyWrapper::internal_default_instance());
  any_with_message_containing_map_ = const_cast< ::google::protobuf::testing::AnyWrapper*>(
      ::google::protobuf::testing::AnyWrapper::internal_default_instance());
  any_with_message_containing_struct_ = const_cast< ::google::protobuf::testing::AnyWrapper*>(
      ::google::protobuf::testing::AnyWrapper::internal_default_instance());
  any_with_message_containing_repeated_message_ = const_cast< ::google::protobuf::testing::AnyWrapper*>(
      ::google::protobuf::testing::AnyWrapper::internal_default_instance());
  recursive_any_with_type_field_at_end_ = const_cast< ::google::protobuf::testing::AnyWrapper*>(
      ::google::protobuf::testing::AnyWrapper::internal_default_instance());
  top_level_any_ = const_cast< ::google::protobuf::Any*>(
      ::google::protobuf::Any::internal_default_instance());
  top_level_any_with_type_field_at_end_ = const_cast< ::google::protobuf::Any*>(
      ::google::protobuf::Any::internal_default_instance());
}

AnyTestCases::AnyTestCases(const AnyTestCases& from)
  : ::google::protobuf::Message(),
    _internal_metadata_(NULL) {
  SharedCtor();
  UnsafeMergeFrom(from);
  // @@protoc_insertion_point(copy_constructor:google.protobuf.testing.AnyTestCases)
}

void AnyTestCases::SharedCtor() {
  empty_any_ = NULL;
  type_only_any_ = NULL;
  wrapper_any_ = NULL;
  any_with_timestamp_value_ = NULL;
  any_with_duration_value_ = NULL;
  any_with_struct_value_ = NULL;
  recursive_any_ = NULL;
  any_with_message_value_ = NULL;
  any_with_nested_message_ = NULL;
  any_with_message_with_wrapper_type_ = NULL;
  any_with_message_with_timestamp_ = NULL;
  any_with_message_containing_map_ = NULL;
  any_with_message_containing_struct_ = NULL;
  any_with_message_containing_repeated_message_ = NULL;
  recursive_any_with_type_field_at_end_ = NULL;
  top_level_any_ = NULL;
  top_level_any_with_type_field_at_end_ = NULL;
  _cached_size_ = 0;
}

AnyTestCases::~AnyTestCases() {
  // @@protoc_insertion_point(destructor:google.protobuf.testing.AnyTestCases)
  SharedDtor();
}

void AnyTestCases::SharedDtor() {
  if (this != &AnyTestCases_default_instance_.get()) {
    delete empty_any_;
    delete type_only_any_;
    delete wrapper_any_;
    delete any_with_timestamp_value_;
    delete any_with_duration_value_;
    delete any_with_struct_value_;
    delete recursive_any_;
    delete any_with_message_value_;
    delete any_with_nested_message_;
    delete any_with_message_with_wrapper_type_;
    delete any_with_message_with_timestamp_;
    delete any_with_message_containing_map_;
    delete any_with_message_containing_struct_;
    delete any_with_message_containing_repeated_message_;
    delete recursive_any_with_type_field_at_end_;
    delete top_level_any_;
    delete top_level_any_with_type_field_at_end_;
  }
}

void AnyTestCases::SetCachedSize(int size) const {
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
}
const ::google::protobuf::Descriptor* AnyTestCases::descriptor() {
  protobuf_AssignDescriptorsOnce();
  return AnyTestCases_descriptor_;
}

const AnyTestCases& AnyTestCases::default_instance() {
  protobuf_InitDefaults_google_2fprotobuf_2futil_2finternal_2ftestdata_2fanys_2eproto();
  return *internal_default_instance();
}

::google::protobuf::internal::ExplicitlyConstructed<AnyTestCases> AnyTestCases_default_instance_;

AnyTestCases* AnyTestCases::New(::google::protobuf::Arena* arena) const {
  AnyTestCases* n = new AnyTestCases;
  if (arena != NULL) {
    arena->Own(n);
  }
  return n;
}

void AnyTestCases::Clear() {
// @@protoc_insertion_point(message_clear_start:google.protobuf.testing.AnyTestCases)
  if (GetArenaNoVirtual() == NULL && empty_any_ != NULL) delete empty_any_;
  empty_any_ = NULL;
  if (GetArenaNoVirtual() == NULL && type_only_any_ != NULL) delete type_only_any_;
  type_only_any_ = NULL;
  if (GetArenaNoVirtual() == NULL && wrapper_any_ != NULL) delete wrapper_any_;
  wrapper_any_ = NULL;
  if (GetArenaNoVirtual() == NULL && any_with_timestamp_value_ != NULL) delete any_with_timestamp_value_;
  any_with_timestamp_value_ = NULL;
  if (GetArenaNoVirtual() == NULL && any_with_duration_value_ != NULL) delete any_with_duration_value_;
  any_with_duration_value_ = NULL;
  if (GetArenaNoVirtual() == NULL && any_with_struct_value_ != NULL) delete any_with_struct_value_;
  any_with_struct_value_ = NULL;
  if (GetArenaNoVirtual() == NULL && recursive_any_ != NULL) delete recursive_any_;
  recursive_any_ = NULL;
  if (GetArenaNoVirtual() == NULL && any_with_message_value_ != NULL) delete any_with_message_value_;
  any_with_message_value_ = NULL;
  if (GetArenaNoVirtual() == NULL && any_with_nested_message_ != NULL) delete any_with_nested_message_;
  any_with_nested_message_ = NULL;
  if (GetArenaNoVirtual() == NULL && any_with_message_with_wrapper_type_ != NULL) delete any_with_message_with_wrapper_type_;
  any_with_message_with_wrapper_type_ = NULL;
  if (GetArenaNoVirtual() == NULL && any_with_message_with_timestamp_ != NULL) delete any_with_message_with_timestamp_;
  any_with_message_with_timestamp_ = NULL;
  if (GetArenaNoVirtual() == NULL && any_with_message_containing_map_ != NULL) delete any_with_message_containing_map_;
  any_with_message_containing_map_ = NULL;
  if (GetArenaNoVirtual() == NULL && any_with_message_containing_struct_ != NULL) delete any_with_message_containing_struct_;
  any_with_message_containing_struct_ = NULL;
  if (GetArenaNoVirtual() == NULL && any_with_message_containing_repeated_message_ != NULL) delete any_with_message_containing_repeated_message_;
  any_with_message_containing_repeated_message_ = NULL;
  if (GetArenaNoVirtual() == NULL && recursive_any_with_type_field_at_end_ != NULL) delete recursive_any_with_type_field_at_end_;
  recursive_any_with_type_field_at_end_ = NULL;
  if (GetArenaNoVirtual() == NULL && top_level_any_ != NULL) delete top_level_any_;
  top_level_any_ = NULL;
  if (GetArenaNoVirtual() == NULL && top_level_any_with_type_field_at_end_ != NULL) delete top_level_any_with_type_field_at_end_;
  top_level_any_with_type_field_at_end_ = NULL;
}

bool AnyTestCases::MergePartialFromCodedStream(
    ::google::protobuf::io::CodedInputStream* input) {
#define DO_(EXPRESSION) if (!GOOGLE_PREDICT_TRUE(EXPRESSION)) goto failure
  ::google::protobuf::uint32 tag;
  // @@protoc_insertion_point(parse_start:google.protobuf.testing.AnyTestCases)
  for (;;) {
    ::std::pair< ::google::protobuf::uint32, bool> p = input->ReadTagWithCutoff(16383);
    tag = p.first;
    if (!p.second) goto handle_unusual;
    switch (::google::protobuf::internal::WireFormatLite::GetTagFieldNumber(tag)) {
      // optional .google.protobuf.testing.AnyWrapper empty_any = 1;
      case 1: {
        if (tag == 10) {
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessageNoVirtual(
               input, mutable_empty_any()));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(18)) goto parse_type_only_any;
        break;
      }

      // optional .google.protobuf.testing.AnyWrapper type_only_any = 2;
      case 2: {
        if (tag == 18) {
         parse_type_only_any:
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessageNoVirtual(
               input, mutable_type_only_any()));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(26)) goto parse_wrapper_any;
        break;
      }

      // optional .google.protobuf.testing.AnyWrapper wrapper_any = 3;
      case 3: {
        if (tag == 26) {
         parse_wrapper_any:
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessageNoVirtual(
               input, mutable_wrapper_any()));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(34)) goto parse_any_with_timestamp_value;
        break;
      }

      // optional .google.protobuf.testing.AnyWrapper any_with_timestamp_value = 4;
      case 4: {
        if (tag == 34) {
         parse_any_with_timestamp_value:
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessageNoVirtual(
               input, mutable_any_with_timestamp_value()));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(42)) goto parse_any_with_duration_value;
        break;
      }

      // optional .google.protobuf.testing.AnyWrapper any_with_duration_value = 5;
      case 5: {
        if (tag == 42) {
         parse_any_with_duration_value:
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessageNoVirtual(
               input, mutable_any_with_duration_value()));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(50)) goto parse_any_with_struct_value;
        break;
      }

      // optional .google.protobuf.testing.AnyWrapper any_with_struct_value = 6;
      case 6: {
        if (tag == 50) {
         parse_any_with_struct_value:
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessageNoVirtual(
               input, mutable_any_with_struct_value()));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(58)) goto parse_recursive_any;
        break;
      }

      // optional .google.protobuf.testing.AnyWrapper recursive_any = 7;
      case 7: {
        if (tag == 58) {
         parse_recursive_any:
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessageNoVirtual(
               input, mutable_recursive_any()));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(66)) goto parse_any_with_message_value;
        break;
      }

      // optional .google.protobuf.testing.AnyWrapper any_with_message_value = 8;
      case 8: {
        if (tag == 66) {
         parse_any_with_message_value:
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessageNoVirtual(
               input, mutable_any_with_message_value()));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(74)) goto parse_any_with_nested_message;
        break;
      }

      // optional .google.protobuf.testing.AnyWrapper any_with_nested_message = 9;
      case 9: {
        if (tag == 74) {
         parse_any_with_nested_message:
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessageNoVirtual(
               input, mutable_any_with_nested_message()));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(82)) goto parse_any_with_message_with_wrapper_type;
        break;
      }

      // optional .google.protobuf.testing.AnyWrapper any_with_message_with_wrapper_type = 10;
      case 10: {
        if (tag == 82) {
         parse_any_with_message_with_wrapper_type:
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessageNoVirtual(
               input, mutable_any_with_message_with_wrapper_type()));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(90)) goto parse_any_with_message_with_timestamp;
        break;
      }

      // optional .google.protobuf.testing.AnyWrapper any_with_message_with_timestamp = 11;
      case 11: {
        if (tag == 90) {
         parse_any_with_message_with_timestamp:
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessageNoVirtual(
               input, mutable_any_with_message_with_timestamp()));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(98)) goto parse_any_with_message_containing_map;
        break;
      }

      // optional .google.protobuf.testing.AnyWrapper any_with_message_containing_map = 12;
      case 12: {
        if (tag == 98) {
         parse_any_with_message_containing_map:
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessageNoVirtual(
               input, mutable_any_with_message_containing_map()));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(106)) goto parse_any_with_message_containing_struct;
        break;
      }

      // optional .google.protobuf.testing.AnyWrapper any_with_message_containing_struct = 13;
      case 13: {
        if (tag == 106) {
         parse_any_with_message_containing_struct:
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessageNoVirtual(
               input, mutable_any_with_message_containing_struct()));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(114)) goto parse_any_with_message_containing_repeated_message;
        break;
      }

      // optional .google.protobuf.testing.AnyWrapper any_with_message_containing_repeated_message = 14;
      case 14: {
        if (tag == 114) {
         parse_any_with_message_containing_repeated_message:
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessageNoVirtual(
               input, mutable_any_with_message_containing_repeated_message()));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(122)) goto parse_recursive_any_with_type_field_at_end;
        break;
      }

      // optional .google.protobuf.testing.AnyWrapper recursive_any_with_type_field_at_end = 15;
      case 15: {
        if (tag == 122) {
         parse_recursive_any_with_type_field_at_end:
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessageNoVirtual(
               input, mutable_recursive_any_with_type_field_at_end()));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(402)) goto parse_top_level_any;
        break;
      }

      // optional .google.protobuf.Any top_level_any = 50;
      case 50: {
        if (tag == 402) {
         parse_top_level_any:
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessageNoVirtual(
               input, mutable_top_level_any()));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(410)) goto parse_top_level_any_with_type_field_at_end;
        break;
      }

      // optional .google.protobuf.Any top_level_any_with_type_field_at_end = 51;
      case 51: {
        if (tag == 410) {
         parse_top_level_any_with_type_field_at_end:
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessageNoVirtual(
               input, mutable_top_level_any_with_type_field_at_end()));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectAtEnd()) goto success;
        break;
      }

      default: {
      handle_unusual:
        if (tag == 0 ||
            ::google::protobuf::internal::WireFormatLite::GetTagWireType(tag) ==
            ::google::protobuf::internal::WireFormatLite::WIRETYPE_END_GROUP) {
          goto success;
        }
        DO_(::google::protobuf::internal::WireFormatLite::SkipField(input, tag));
        break;
      }
    }
  }
success:
  // @@protoc_insertion_point(parse_success:google.protobuf.testing.AnyTestCases)
  return true;
failure:
  // @@protoc_insertion_point(parse_failure:google.protobuf.testing.AnyTestCases)
  return false;
#undef DO_
}

void AnyTestCases::SerializeWithCachedSizes(
    ::google::protobuf::io::CodedOutputStream* output) const {
  // @@protoc_insertion_point(serialize_start:google.protobuf.testing.AnyTestCases)
  // optional .google.protobuf.testing.AnyWrapper empty_any = 1;
  if (this->has_empty_any()) {
    ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
      1, *this->empty_any_, output);
  }

  // optional .google.protobuf.testing.AnyWrapper type_only_any = 2;
  if (this->has_type_only_any()) {
    ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
      2, *this->type_only_any_, output);
  }

  // optional .google.protobuf.testing.AnyWrapper wrapper_any = 3;
  if (this->has_wrapper_any()) {
    ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
      3, *this->wrapper_any_, output);
  }

  // optional .google.protobuf.testing.AnyWrapper any_with_timestamp_value = 4;
  if (this->has_any_with_timestamp_value()) {
    ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
      4, *this->any_with_timestamp_value_, output);
  }

  // optional .google.protobuf.testing.AnyWrapper any_with_duration_value = 5;
  if (this->has_any_with_duration_value()) {
    ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
      5, *this->any_with_duration_value_, output);
  }

  // optional .google.protobuf.testing.AnyWrapper any_with_struct_value = 6;
  if (this->has_any_with_struct_value()) {
    ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
      6, *this->any_with_struct_value_, output);
  }

  // optional .google.protobuf.testing.AnyWrapper recursive_any = 7;
  if (this->has_recursive_any()) {
    ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
      7, *this->recursive_any_, output);
  }

  // optional .google.protobuf.testing.AnyWrapper any_with_message_value = 8;
  if (this->has_any_with_message_value()) {
    ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
      8, *this->any_with_message_value_, output);
  }

  // optional .google.protobuf.testing.AnyWrapper any_with_nested_message = 9;
  if (this->has_any_with_nested_message()) {
    ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
      9, *this->any_with_nested_message_, output);
  }

  // optional .google.protobuf.testing.AnyWrapper any_with_message_with_wrapper_type = 10;
  if (this->has_any_with_message_with_wrapper_type()) {
    ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
      10, *this->any_with_message_with_wrapper_type_, output);
  }

  // optional .google.protobuf.testing.AnyWrapper any_with_message_with_timestamp = 11;
  if (this->has_any_with_message_with_timestamp()) {
    ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
      11, *this->any_with_message_with_timestamp_, output);
  }

  // optional .google.protobuf.testing.AnyWrapper any_with_message_containing_map = 12;
  if (this->has_any_with_message_containing_map()) {
    ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
      12, *this->any_with_message_containing_map_, output);
  }

  // optional .google.protobuf.testing.AnyWrapper any_with_message_containing_struct = 13;
  if (this->has_any_with_message_containing_struct()) {
    ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
      13, *this->any_with_message_containing_struct_, output);
  }

  // optional .google.protobuf.testing.AnyWrapper any_with_message_containing_repeated_message = 14;
  if (this->has_any_with_message_containing_repeated_message()) {
    ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
      14, *this->any_with_message_containing_repeated_message_, output);
  }

  // optional .google.protobuf.testing.AnyWrapper recursive_any_with_type_field_at_end = 15;
  if (this->has_recursive_any_with_type_field_at_end()) {
    ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
      15, *this->recursive_any_with_type_field_at_end_, output);
  }

  // optional .google.protobuf.Any top_level_any = 50;
  if (this->has_top_level_any()) {
    ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
      50, *this->top_level_any_, output);
  }

  // optional .google.protobuf.Any top_level_any_with_type_field_at_end = 51;
  if (this->has_top_level_any_with_type_field_at_end()) {
    ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
      51, *this->top_level_any_with_type_field_at_end_, output);
  }

  // @@protoc_insertion_point(serialize_end:google.protobuf.testing.AnyTestCases)
}

::google::protobuf::uint8* AnyTestCases::InternalSerializeWithCachedSizesToArray(
    bool deterministic, ::google::protobuf::uint8* target) const {
  (void)deterministic; // Unused
  // @@protoc_insertion_point(serialize_to_array_start:google.protobuf.testing.AnyTestCases)
  // optional .google.protobuf.testing.AnyWrapper empty_any = 1;
  if (this->has_empty_any()) {
    target = ::google::protobuf::internal::WireFormatLite::
      InternalWriteMessageNoVirtualToArray(
        1, *this->empty_any_, false, target);
  }

  // optional .google.protobuf.testing.AnyWrapper type_only_any = 2;
  if (this->has_type_only_any()) {
    target = ::google::protobuf::internal::WireFormatLite::
      InternalWriteMessageNoVirtualToArray(
        2, *this->type_only_any_, false, target);
  }

  // optional .google.protobuf.testing.AnyWrapper wrapper_any = 3;
  if (this->has_wrapper_any()) {
    target = ::google::protobuf::internal::WireFormatLite::
      InternalWriteMessageNoVirtualToArray(
        3, *this->wrapper_any_, false, target);
  }

  // optional .google.protobuf.testing.AnyWrapper any_with_timestamp_value = 4;
  if (this->has_any_with_timestamp_value()) {
    target = ::google::protobuf::internal::WireFormatLite::
      InternalWriteMessageNoVirtualToArray(
        4, *this->any_with_timestamp_value_, false, target);
  }

  // optional .google.protobuf.testing.AnyWrapper any_with_duration_value = 5;
  if (this->has_any_with_duration_value()) {
    target = ::google::protobuf::internal::WireFormatLite::
      InternalWriteMessageNoVirtualToArray(
        5, *this->any_with_duration_value_, false, target);
  }

  // optional .google.protobuf.testing.AnyWrapper any_with_struct_value = 6;
  if (this->has_any_with_struct_value()) {
    target = ::google::protobuf::internal::WireFormatLite::
      InternalWriteMessageNoVirtualToArray(
        6, *this->any_with_struct_value_, false, target);
  }

  // optional .google.protobuf.testing.AnyWrapper recursive_any = 7;
  if (this->has_recursive_any()) {
    target = ::google::protobuf::internal::WireFormatLite::
      InternalWriteMessageNoVirtualToArray(
        7, *this->recursive_any_, false, target);
  }

  // optional .google.protobuf.testing.AnyWrapper any_with_message_value = 8;
  if (this->has_any_with_message_value()) {
    target = ::google::protobuf::internal::WireFormatLite::
      InternalWriteMessageNoVirtualToArray(
        8, *this->any_with_message_value_, false, target);
  }

  // optional .google.protobuf.testing.AnyWrapper any_with_nested_message = 9;
  if (this->has_any_with_nested_message()) {
    target = ::google::protobuf::internal::WireFormatLite::
      InternalWriteMessageNoVirtualToArray(
        9, *this->any_with_nested_message_, false, target);
  }

  // optional .google.protobuf.testing.AnyWrapper any_with_message_with_wrapper_type = 10;
  if (this->has_any_with_message_with_wrapper_type()) {
    target = ::google::protobuf::internal::WireFormatLite::
      InternalWriteMessageNoVirtualToArray(
        10, *this->any_with_message_with_wrapper_type_, false, target);
  }

  // optional .google.protobuf.testing.AnyWrapper any_with_message_with_timestamp = 11;
  if (this->has_any_with_message_with_timestamp()) {
    target = ::google::protobuf::internal::WireFormatLite::
      InternalWriteMessageNoVirtualToArray(
        11, *this->any_with_message_with_timestamp_, false, target);
  }

  // optional .google.protobuf.testing.AnyWrapper any_with_message_containing_map = 12;
  if (this->has_any_with_message_containing_map()) {
    target = ::google::protobuf::internal::WireFormatLite::
      InternalWriteMessageNoVirtualToArray(
        12, *this->any_with_message_containing_map_, false, target);
  }

  // optional .google.protobuf.testing.AnyWrapper any_with_message_containing_struct = 13;
  if (this->has_any_with_message_containing_struct()) {
    target = ::google::protobuf::internal::WireFormatLite::
      InternalWriteMessageNoVirtualToArray(
        13, *this->any_with_message_containing_struct_, false, target);
  }

  // optional .google.protobuf.testing.AnyWrapper any_with_message_containing_repeated_message = 14;
  if (this->has_any_with_message_containing_repeated_message()) {
    target = ::google::protobuf::internal::WireFormatLite::
      InternalWriteMessageNoVirtualToArray(
        14, *this->any_with_message_containing_repeated_message_, false, target);
  }

  // optional .google.protobuf.testing.AnyWrapper recursive_any_with_type_field_at_end = 15;
  if (this->has_recursive_any_with_type_field_at_end()) {
    target = ::google::protobuf::internal::WireFormatLite::
      InternalWriteMessageNoVirtualToArray(
        15, *this->recursive_any_with_type_field_at_end_, false, target);
  }

  // optional .google.protobuf.Any top_level_any = 50;
  if (this->has_top_level_any()) {
    target = ::google::protobuf::internal::WireFormatLite::
      InternalWriteMessageNoVirtualToArray(
        50, *this->top_level_any_, false, target);
  }

  // optional .google.protobuf.Any top_level_any_with_type_field_at_end = 51;
  if (this->has_top_level_any_with_type_field_at_end()) {
    target = ::google::protobuf::internal::WireFormatLite::
      InternalWriteMessageNoVirtualToArray(
        51, *this->top_level_any_with_type_field_at_end_, false, target);
  }

  // @@protoc_insertion_point(serialize_to_array_end:google.protobuf.testing.AnyTestCases)
  return target;
}

size_t AnyTestCases::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:google.protobuf.testing.AnyTestCases)
  size_t total_size = 0;

  // optional .google.protobuf.testing.AnyWrapper empty_any = 1;
  if (this->has_empty_any()) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::MessageSizeNoVirtual(
        *this->empty_any_);
  }

  // optional .google.protobuf.testing.AnyWrapper type_only_any = 2;
  if (this->has_type_only_any()) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::MessageSizeNoVirtual(
        *this->type_only_any_);
  }

  // optional .google.protobuf.testing.AnyWrapper wrapper_any = 3;
  if (this->has_wrapper_any()) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::MessageSizeNoVirtual(
        *this->wrapper_any_);
  }

  // optional .google.protobuf.testing.AnyWrapper any_with_timestamp_value = 4;
  if (this->has_any_with_timestamp_value()) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::MessageSizeNoVirtual(
        *this->any_with_timestamp_value_);
  }

  // optional .google.protobuf.testing.AnyWrapper any_with_duration_value = 5;
  if (this->has_any_with_duration_value()) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::MessageSizeNoVirtual(
        *this->any_with_duration_value_);
  }

  // optional .google.protobuf.testing.AnyWrapper any_with_struct_value = 6;
  if (this->has_any_with_struct_value()) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::MessageSizeNoVirtual(
        *this->any_with_struct_value_);
  }

  // optional .google.protobuf.testing.AnyWrapper recursive_any = 7;
  if (this->has_recursive_any()) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::MessageSizeNoVirtual(
        *this->recursive_any_);
  }

  // optional .google.protobuf.testing.AnyWrapper any_with_message_value = 8;
  if (this->has_any_with_message_value()) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::MessageSizeNoVirtual(
        *this->any_with_message_value_);
  }

  // optional .google.protobuf.testing.AnyWrapper any_with_nested_message = 9;
  if (this->has_any_with_nested_message()) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::MessageSizeNoVirtual(
        *this->any_with_nested_message_);
  }

  // optional .google.protobuf.testing.AnyWrapper any_with_message_with_wrapper_type = 10;
  if (this->has_any_with_message_with_wrapper_type()) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::MessageSizeNoVirtual(
        *this->any_with_message_with_wrapper_type_);
  }

  // optional .google.protobuf.testing.AnyWrapper any_with_message_with_timestamp = 11;
  if (this->has_any_with_message_with_timestamp()) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::MessageSizeNoVirtual(
        *this->any_with_message_with_timestamp_);
  }

  // optional .google.protobuf.testing.AnyWrapper any_with_message_containing_map = 12;
  if (this->has_any_with_message_containing_map()) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::MessageSizeNoVirtual(
        *this->any_with_message_containing_map_);
  }

  // optional .google.protobuf.testing.AnyWrapper any_with_message_containing_struct = 13;
  if (this->has_any_with_message_containing_struct()) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::MessageSizeNoVirtual(
        *this->any_with_message_containing_struct_);
  }

  // optional .google.protobuf.testing.AnyWrapper any_with_message_containing_repeated_message = 14;
  if (this->has_any_with_message_containing_repeated_message()) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::MessageSizeNoVirtual(
        *this->any_with_message_containing_repeated_message_);
  }

  // optional .google.protobuf.testing.AnyWrapper recursive_any_with_type_field_at_end = 15;
  if (this->has_recursive_any_with_type_field_at_end()) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::MessageSizeNoVirtual(
        *this->recursive_any_with_type_field_at_end_);
  }

  // optional .google.protobuf.Any top_level_any = 50;
  if (this->has_top_level_any()) {
    total_size += 2 +
      ::google::protobuf::internal::WireFormatLite::MessageSizeNoVirtual(
        *this->top_level_any_);
  }

  // optional .google.protobuf.Any top_level_any_with_type_field_at_end = 51;
  if (this->has_top_level_any_with_type_field_at_end()) {
    total_size += 2 +
      ::google::protobuf::internal::WireFormatLite::MessageSizeNoVirtual(
        *this->top_level_any_with_type_field_at_end_);
  }

  int cached_size = ::google::protobuf::internal::ToCachedSize(total_size);
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = cached_size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
  return total_size;
}

void AnyTestCases::MergeFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_merge_from_start:google.protobuf.testing.AnyTestCases)
  if (GOOGLE_PREDICT_FALSE(&from == this)) MergeFromFail(__LINE__);
  const AnyTestCases* source =
      ::google::protobuf::internal::DynamicCastToGenerated<const AnyTestCases>(
          &from);
  if (source == NULL) {
  // @@protoc_insertion_point(generalized_merge_from_cast_fail:google.protobuf.testing.AnyTestCases)
    ::google::protobuf::internal::ReflectionOps::Merge(from, this);
  } else {
  // @@protoc_insertion_point(generalized_merge_from_cast_success:google.protobuf.testing.AnyTestCases)
    UnsafeMergeFrom(*source);
  }
}

void AnyTestCases::MergeFrom(const AnyTestCases& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:google.protobuf.testing.AnyTestCases)
  if (GOOGLE_PREDICT_TRUE(&from != this)) {
    UnsafeMergeFrom(from);
  } else {
    MergeFromFail(__LINE__);
  }
}

void AnyTestCases::UnsafeMergeFrom(const AnyTestCases& from) {
  GOOGLE_DCHECK(&from != this);
  if (from.has_empty_any()) {
    mutable_empty_any()->::google::protobuf::testing::AnyWrapper::MergeFrom(from.empty_any());
  }
  if (from.has_type_only_any()) {
    mutable_type_only_any()->::google::protobuf::testing::AnyWrapper::MergeFrom(from.type_only_any());
  }
  if (from.has_wrapper_any()) {
    mutable_wrapper_any()->::google::protobuf::testing::AnyWrapper::MergeFrom(from.wrapper_any());
  }
  if (from.has_any_with_timestamp_value()) {
    mutable_any_with_timestamp_value()->::google::protobuf::testing::AnyWrapper::MergeFrom(from.any_with_timestamp_value());
  }
  if (from.has_any_with_duration_value()) {
    mutable_any_with_duration_value()->::google::protobuf::testing::AnyWrapper::MergeFrom(from.any_with_duration_value());
  }
  if (from.has_any_with_struct_value()) {
    mutable_any_with_struct_value()->::google::protobuf::testing::AnyWrapper::MergeFrom(from.any_with_struct_value());
  }
  if (from.has_recursive_any()) {
    mutable_recursive_any()->::google::protobuf::testing::AnyWrapper::MergeFrom(from.recursive_any());
  }
  if (from.has_any_with_message_value()) {
    mutable_any_with_message_value()->::google::protobuf::testing::AnyWrapper::MergeFrom(from.any_with_message_value());
  }
  if (from.has_any_with_nested_message()) {
    mutable_any_with_nested_message()->::google::protobuf::testing::AnyWrapper::MergeFrom(from.any_with_nested_message());
  }
  if (from.has_any_with_message_with_wrapper_type()) {
    mutable_any_with_message_with_wrapper_type()->::google::protobuf::testing::AnyWrapper::MergeFrom(from.any_with_message_with_wrapper_type());
  }
  if (from.has_any_with_message_with_timestamp()) {
    mutable_any_with_message_with_timestamp()->::google::protobuf::testing::AnyWrapper::MergeFrom(from.any_with_message_with_timestamp());
  }
  if (from.has_any_with_message_containing_map()) {
    mutable_any_with_message_containing_map()->::google::protobuf::testing::AnyWrapper::MergeFrom(from.any_with_message_containing_map());
  }
  if (from.has_any_with_message_containing_struct()) {
    mutable_any_with_message_containing_struct()->::google::protobuf::testing::AnyWrapper::MergeFrom(from.any_with_message_containing_struct());
  }
  if (from.has_any_with_message_containing_repeated_message()) {
    mutable_any_with_message_containing_repeated_message()->::google::protobuf::testing::AnyWrapper::MergeFrom(from.any_with_message_containing_repeated_message());
  }
  if (from.has_recursive_any_with_type_field_at_end()) {
    mutable_recursive_any_with_type_field_at_end()->::google::protobuf::testing::AnyWrapper::MergeFrom(from.recursive_any_with_type_field_at_end());
  }
  if (from.has_top_level_any()) {
    mutable_top_level_any()->::google::protobuf::Any::MergeFrom(from.top_level_any());
  }
  if (from.has_top_level_any_with_type_field_at_end()) {
    mutable_top_level_any_with_type_field_at_end()->::google::protobuf::Any::MergeFrom(from.top_level_any_with_type_field_at_end());
  }
}

void AnyTestCases::CopyFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_copy_from_start:google.protobuf.testing.AnyTestCases)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

void AnyTestCases::CopyFrom(const AnyTestCases& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:google.protobuf.testing.AnyTestCases)
  if (&from == this) return;
  Clear();
  UnsafeMergeFrom(from);
}

bool AnyTestCases::IsInitialized() const {

  return true;
}

void AnyTestCases::Swap(AnyTestCases* other) {
  if (other == this) return;
  InternalSwap(other);
}
void AnyTestCases::InternalSwap(AnyTestCases* other) {
  std::swap(empty_any_, other->empty_any_);
  std::swap(type_only_any_, other->type_only_any_);
  std::swap(wrapper_any_, other->wrapper_any_);
  std::swap(any_with_timestamp_value_, other->any_with_timestamp_value_);
  std::swap(any_with_duration_value_, other->any_with_duration_value_);
  std::swap(any_with_struct_value_, other->any_with_struct_value_);
  std::swap(recursive_any_, other->recursive_any_);
  std::swap(any_with_message_value_, other->any_with_message_value_);
  std::swap(any_with_nested_message_, other->any_with_nested_message_);
  std::swap(any_with_message_with_wrapper_type_, other->any_with_message_with_wrapper_type_);
  std::swap(any_with_message_with_timestamp_, other->any_with_message_with_timestamp_);
  std::swap(any_with_message_containing_map_, other->any_with_message_containing_map_);
  std::swap(any_with_message_containing_struct_, other->any_with_message_containing_struct_);
  std::swap(any_with_message_containing_repeated_message_, other->any_with_message_containing_repeated_message_);
  std::swap(recursive_any_with_type_field_at_end_, other->recursive_any_with_type_field_at_end_);
  std::swap(top_level_any_, other->top_level_any_);
  std::swap(top_level_any_with_type_field_at_end_, other->top_level_any_with_type_field_at_end_);
  _internal_metadata_.Swap(&other->_internal_metadata_);
  std::swap(_cached_size_, other->_cached_size_);
}

::google::protobuf::Metadata AnyTestCases::GetMetadata() const {
  protobuf_AssignDescriptorsOnce();
  ::google::protobuf::Metadata metadata;
  metadata.descriptor = AnyTestCases_descriptor_;
  metadata.reflection = AnyTestCases_reflection_;
  return metadata;
}

#if PROTOBUF_INLINE_NOT_IN_HEADERS
// AnyTestCases

// optional .google.protobuf.testing.AnyWrapper empty_any = 1;
bool AnyTestCases::has_empty_any() const {
  return this != internal_default_instance() && empty_any_ != NULL;
}
void AnyTestCases::clear_empty_any() {
  if (GetArenaNoVirtual() == NULL && empty_any_ != NULL) delete empty_any_;
  empty_any_ = NULL;
}
const ::google::protobuf::testing::AnyWrapper& AnyTestCases::empty_any() const {
  // @@protoc_insertion_point(field_get:google.protobuf.testing.AnyTestCases.empty_any)
  return empty_any_ != NULL ? *empty_any_
                         : *::google::protobuf::testing::AnyWrapper::internal_default_instance();
}
::google::protobuf::testing::AnyWrapper* AnyTestCases::mutable_empty_any() {
  
  if (empty_any_ == NULL) {
    empty_any_ = new ::google::protobuf::testing::AnyWrapper;
  }
  // @@protoc_insertion_point(field_mutable:google.protobuf.testing.AnyTestCases.empty_any)
  return empty_any_;
}
::google::protobuf::testing::AnyWrapper* AnyTestCases::release_empty_any() {
  // @@protoc_insertion_point(field_release:google.protobuf.testing.AnyTestCases.empty_any)
  
  ::google::protobuf::testing::AnyWrapper* temp = empty_any_;
  empty_any_ = NULL;
  return temp;
}
void AnyTestCases::set_allocated_empty_any(::google::protobuf::testing::AnyWrapper* empty_any) {
  delete empty_any_;
  empty_any_ = empty_any;
  if (empty_any) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_set_allocated:google.protobuf.testing.AnyTestCases.empty_any)
}

// optional .google.protobuf.testing.AnyWrapper type_only_any = 2;
bool AnyTestCases::has_type_only_any() const {
  return this != internal_default_instance() && type_only_any_ != NULL;
}
void AnyTestCases::clear_type_only_any() {
  if (GetArenaNoVirtual() == NULL && type_only_any_ != NULL) delete type_only_any_;
  type_only_any_ = NULL;
}
const ::google::protobuf::testing::AnyWrapper& AnyTestCases::type_only_any() const {
  // @@protoc_insertion_point(field_get:google.protobuf.testing.AnyTestCases.type_only_any)
  return type_only_any_ != NULL ? *type_only_any_
                         : *::google::protobuf::testing::AnyWrapper::internal_default_instance();
}
::google::protobuf::testing::AnyWrapper* AnyTestCases::mutable_type_only_any() {
  
  if (type_only_any_ == NULL) {
    type_only_any_ = new ::google::protobuf::testing::AnyWrapper;
  }
  // @@protoc_insertion_point(field_mutable:google.protobuf.testing.AnyTestCases.type_only_any)
  return type_only_any_;
}
::google::protobuf::testing::AnyWrapper* AnyTestCases::release_type_only_any() {
  // @@protoc_insertion_point(field_release:google.protobuf.testing.AnyTestCases.type_only_any)
  
  ::google::protobuf::testing::AnyWrapper* temp = type_only_any_;
  type_only_any_ = NULL;
  return temp;
}
void AnyTestCases::set_allocated_type_only_any(::google::protobuf::testing::AnyWrapper* type_only_any) {
  delete type_only_any_;
  type_only_any_ = type_only_any;
  if (type_only_any) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_set_allocated:google.protobuf.testing.AnyTestCases.type_only_any)
}

// optional .google.protobuf.testing.AnyWrapper wrapper_any = 3;
bool AnyTestCases::has_wrapper_any() const {
  return this != internal_default_instance() && wrapper_any_ != NULL;
}
void AnyTestCases::clear_wrapper_any() {
  if (GetArenaNoVirtual() == NULL && wrapper_any_ != NULL) delete wrapper_any_;
  wrapper_any_ = NULL;
}
const ::google::protobuf::testing::AnyWrapper& AnyTestCases::wrapper_any() const {
  // @@protoc_insertion_point(field_get:google.protobuf.testing.AnyTestCases.wrapper_any)
  return wrapper_any_ != NULL ? *wrapper_any_
                         : *::google::protobuf::testing::AnyWrapper::internal_default_instance();
}
::google::protobuf::testing::AnyWrapper* AnyTestCases::mutable_wrapper_any() {
  
  if (wrapper_any_ == NULL) {
    wrapper_any_ = new ::google::protobuf::testing::AnyWrapper;
  }
  // @@protoc_insertion_point(field_mutable:google.protobuf.testing.AnyTestCases.wrapper_any)
  return wrapper_any_;
}
::google::protobuf::testing::AnyWrapper* AnyTestCases::release_wrapper_any() {
  // @@protoc_insertion_point(field_release:google.protobuf.testing.AnyTestCases.wrapper_any)
  
  ::google::protobuf::testing::AnyWrapper* temp = wrapper_any_;
  wrapper_any_ = NULL;
  return temp;
}
void AnyTestCases::set_allocated_wrapper_any(::google::protobuf::testing::AnyWrapper* wrapper_any) {
  delete wrapper_any_;
  wrapper_any_ = wrapper_any;
  if (wrapper_any) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_set_allocated:google.protobuf.testing.AnyTestCases.wrapper_any)
}

// optional .google.protobuf.testing.AnyWrapper any_with_timestamp_value = 4;
bool AnyTestCases::has_any_with_timestamp_value() const {
  return this != internal_default_instance() && any_with_timestamp_value_ != NULL;
}
void AnyTestCases::clear_any_with_timestamp_value() {
  if (GetArenaNoVirtual() == NULL && any_with_timestamp_value_ != NULL) delete any_with_timestamp_value_;
  any_with_timestamp_value_ = NULL;
}
const ::google::protobuf::testing::AnyWrapper& AnyTestCases::any_with_timestamp_value() const {
  // @@protoc_insertion_point(field_get:google.protobuf.testing.AnyTestCases.any_with_timestamp_value)
  return any_with_timestamp_value_ != NULL ? *any_with_timestamp_value_
                         : *::google::protobuf::testing::AnyWrapper::internal_default_instance();
}
::google::protobuf::testing::AnyWrapper* AnyTestCases::mutable_any_with_timestamp_value() {
  
  if (any_with_timestamp_value_ == NULL) {
    any_with_timestamp_value_ = new ::google::protobuf::testing::AnyWrapper;
  }
  // @@protoc_insertion_point(field_mutable:google.protobuf.testing.AnyTestCases.any_with_timestamp_value)
  return any_with_timestamp_value_;
}
::google::protobuf::testing::AnyWrapper* AnyTestCases::release_any_with_timestamp_value() {
  // @@protoc_insertion_point(field_release:google.protobuf.testing.AnyTestCases.any_with_timestamp_value)
  
  ::google::protobuf::testing::AnyWrapper* temp = any_with_timestamp_value_;
  any_with_timestamp_value_ = NULL;
  return temp;
}
void AnyTestCases::set_allocated_any_with_timestamp_value(::google::protobuf::testing::AnyWrapper* any_with_timestamp_value) {
  delete any_with_timestamp_value_;
  any_with_timestamp_value_ = any_with_timestamp_value;
  if (any_with_timestamp_value) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_set_allocated:google.protobuf.testing.AnyTestCases.any_with_timestamp_value)
}

// optional .google.protobuf.testing.AnyWrapper any_with_duration_value = 5;
bool AnyTestCases::has_any_with_duration_value() const {
  return this != internal_default_instance() && any_with_duration_value_ != NULL;
}
void AnyTestCases::clear_any_with_duration_value() {
  if (GetArenaNoVirtual() == NULL && any_with_duration_value_ != NULL) delete any_with_duration_value_;
  any_with_duration_value_ = NULL;
}
const ::google::protobuf::testing::AnyWrapper& AnyTestCases::any_with_duration_value() const {
  // @@protoc_insertion_point(field_get:google.protobuf.testing.AnyTestCases.any_with_duration_value)
  return any_with_duration_value_ != NULL ? *any_with_duration_value_
                         : *::google::protobuf::testing::AnyWrapper::internal_default_instance();
}
::google::protobuf::testing::AnyWrapper* AnyTestCases::mutable_any_with_duration_value() {
  
  if (any_with_duration_value_ == NULL) {
    any_with_duration_value_ = new ::google::protobuf::testing::AnyWrapper;
  }
  // @@protoc_insertion_point(field_mutable:google.protobuf.testing.AnyTestCases.any_with_duration_value)
  return any_with_duration_value_;
}
::google::protobuf::testing::AnyWrapper* AnyTestCases::release_any_with_duration_value() {
  // @@protoc_insertion_point(field_release:google.protobuf.testing.AnyTestCases.any_with_duration_value)
  
  ::google::protobuf::testing::AnyWrapper* temp = any_with_duration_value_;
  any_with_duration_value_ = NULL;
  return temp;
}
void AnyTestCases::set_allocated_any_with_duration_value(::google::protobuf::testing::AnyWrapper* any_with_duration_value) {
  delete any_with_duration_value_;
  any_with_duration_value_ = any_with_duration_value;
  if (any_with_duration_value) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_set_allocated:google.protobuf.testing.AnyTestCases.any_with_duration_value)
}

// optional .google.protobuf.testing.AnyWrapper any_with_struct_value = 6;
bool AnyTestCases::has_any_with_struct_value() const {
  return this != internal_default_instance() && any_with_struct_value_ != NULL;
}
void AnyTestCases::clear_any_with_struct_value() {
  if (GetArenaNoVirtual() == NULL && any_with_struct_value_ != NULL) delete any_with_struct_value_;
  any_with_struct_value_ = NULL;
}
const ::google::protobuf::testing::AnyWrapper& AnyTestCases::any_with_struct_value() const {
  // @@protoc_insertion_point(field_get:google.protobuf.testing.AnyTestCases.any_with_struct_value)
  return any_with_struct_value_ != NULL ? *any_with_struct_value_
                         : *::google::protobuf::testing::AnyWrapper::internal_default_instance();
}
::google::protobuf::testing::AnyWrapper* AnyTestCases::mutable_any_with_struct_value() {
  
  if (any_with_struct_value_ == NULL) {
    any_with_struct_value_ = new ::google::protobuf::testing::AnyWrapper;
  }
  // @@protoc_insertion_point(field_mutable:google.protobuf.testing.AnyTestCases.any_with_struct_value)
  return any_with_struct_value_;
}
::google::protobuf::testing::AnyWrapper* AnyTestCases::release_any_with_struct_value() {
  // @@protoc_insertion_point(field_release:google.protobuf.testing.AnyTestCases.any_with_struct_value)
  
  ::google::protobuf::testing::AnyWrapper* temp = any_with_struct_value_;
  any_with_struct_value_ = NULL;
  return temp;
}
void AnyTestCases::set_allocated_any_with_struct_value(::google::protobuf::testing::AnyWrapper* any_with_struct_value) {
  delete any_with_struct_value_;
  any_with_struct_value_ = any_with_struct_value;
  if (any_with_struct_value) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_set_allocated:google.protobuf.testing.AnyTestCases.any_with_struct_value)
}

// optional .google.protobuf.testing.AnyWrapper recursive_any = 7;
bool AnyTestCases::has_recursive_any() const {
  return this != internal_default_instance() && recursive_any_ != NULL;
}
void AnyTestCases::clear_recursive_any() {
  if (GetArenaNoVirtual() == NULL && recursive_any_ != NULL) delete recursive_any_;
  recursive_any_ = NULL;
}
const ::google::protobuf::testing::AnyWrapper& AnyTestCases::recursive_any() const {
  // @@protoc_insertion_point(field_get:google.protobuf.testing.AnyTestCases.recursive_any)
  return recursive_any_ != NULL ? *recursive_any_
                         : *::google::protobuf::testing::AnyWrapper::internal_default_instance();
}
::google::protobuf::testing::AnyWrapper* AnyTestCases::mutable_recursive_any() {
  
  if (recursive_any_ == NULL) {
    recursive_any_ = new ::google::protobuf::testing::AnyWrapper;
  }
  // @@protoc_insertion_point(field_mutable:google.protobuf.testing.AnyTestCases.recursive_any)
  return recursive_any_;
}
::google::protobuf::testing::AnyWrapper* AnyTestCases::release_recursive_any() {
  // @@protoc_insertion_point(field_release:google.protobuf.testing.AnyTestCases.recursive_any)
  
  ::google::protobuf::testing::AnyWrapper* temp = recursive_any_;
  recursive_any_ = NULL;
  return temp;
}
void AnyTestCases::set_allocated_recursive_any(::google::protobuf::testing::AnyWrapper* recursive_any) {
  delete recursive_any_;
  recursive_any_ = recursive_any;
  if (recursive_any) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_set_allocated:google.protobuf.testing.AnyTestCases.recursive_any)
}

// optional .google.protobuf.testing.AnyWrapper any_with_message_value = 8;
bool AnyTestCases::has_any_with_message_value() const {
  return this != internal_default_instance() && any_with_message_value_ != NULL;
}
void AnyTestCases::clear_any_with_message_value() {
  if (GetArenaNoVirtual() == NULL && any_with_message_value_ != NULL) delete any_with_message_value_;
  any_with_message_value_ = NULL;
}
const ::google::protobuf::testing::AnyWrapper& AnyTestCases::any_with_message_value() const {
  // @@protoc_insertion_point(field_get:google.protobuf.testing.AnyTestCases.any_with_message_value)
  return any_with_message_value_ != NULL ? *any_with_message_value_
                         : *::google::protobuf::testing::AnyWrapper::internal_default_instance();
}
::google::protobuf::testing::AnyWrapper* AnyTestCases::mutable_any_with_message_value() {
  
  if (any_with_message_value_ == NULL) {
    any_with_message_value_ = new ::google::protobuf::testing::AnyWrapper;
  }
  // @@protoc_insertion_point(field_mutable:google.protobuf.testing.AnyTestCases.any_with_message_value)
  return any_with_message_value_;
}
::google::protobuf::testing::AnyWrapper* AnyTestCases::release_any_with_message_value() {
  // @@protoc_insertion_point(field_release:google.protobuf.testing.AnyTestCases.any_with_message_value)
  
  ::google::protobuf::testing::AnyWrapper* temp = any_with_message_value_;
  any_with_message_value_ = NULL;
  return temp;
}
void AnyTestCases::set_allocated_any_with_message_value(::google::protobuf::testing::AnyWrapper* any_with_message_value) {
  delete any_with_message_value_;
  any_with_message_value_ = any_with_message_value;
  if (any_with_message_value) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_set_allocated:google.protobuf.testing.AnyTestCases.any_with_message_value)
}

// optional .google.protobuf.testing.AnyWrapper any_with_nested_message = 9;
bool AnyTestCases::has_any_with_nested_message() const {
  return this != internal_default_instance() && any_with_nested_message_ != NULL;
}
void AnyTestCases::clear_any_with_nested_message() {
  if (GetArenaNoVirtual() == NULL && any_with_nested_message_ != NULL) delete any_with_nested_message_;
  any_with_nested_message_ = NULL;
}
const ::google::protobuf::testing::AnyWrapper& AnyTestCases::any_with_nested_message() const {
  // @@protoc_insertion_point(field_get:google.protobuf.testing.AnyTestCases.any_with_nested_message)
  return any_with_nested_message_ != NULL ? *any_with_nested_message_
                         : *::google::protobuf::testing::AnyWrapper::internal_default_instance();
}
::google::protobuf::testing::AnyWrapper* AnyTestCases::mutable_any_with_nested_message() {
  
  if (any_with_nested_message_ == NULL) {
    any_with_nested_message_ = new ::google::protobuf::testing::AnyWrapper;
  }
  // @@protoc_insertion_point(field_mutable:google.protobuf.testing.AnyTestCases.any_with_nested_message)
  return any_with_nested_message_;
}
::google::protobuf::testing::AnyWrapper* AnyTestCases::release_any_with_nested_message() {
  // @@protoc_insertion_point(field_release:google.protobuf.testing.AnyTestCases.any_with_nested_message)
  
  ::google::protobuf::testing::AnyWrapper* temp = any_with_nested_message_;
  any_with_nested_message_ = NULL;
  return temp;
}
void AnyTestCases::set_allocated_any_with_nested_message(::google::protobuf::testing::AnyWrapper* any_with_nested_message) {
  delete any_with_nested_message_;
  any_with_nested_message_ = any_with_nested_message;
  if (any_with_nested_message) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_set_allocated:google.protobuf.testing.AnyTestCases.any_with_nested_message)
}

// optional .google.protobuf.testing.AnyWrapper any_with_message_with_wrapper_type = 10;
bool AnyTestCases::has_any_with_message_with_wrapper_type() const {
  return this != internal_default_instance() && any_with_message_with_wrapper_type_ != NULL;
}
void AnyTestCases::clear_any_with_message_with_wrapper_type() {
  if (GetArenaNoVirtual() == NULL && any_with_message_with_wrapper_type_ != NULL) delete any_with_message_with_wrapper_type_;
  any_with_message_with_wrapper_type_ = NULL;
}
const ::google::protobuf::testing::AnyWrapper& AnyTestCases::any_with_message_with_wrapper_type() const {
  // @@protoc_insertion_point(field_get:google.protobuf.testing.AnyTestCases.any_with_message_with_wrapper_type)
  return any_with_message_with_wrapper_type_ != NULL ? *any_with_message_with_wrapper_type_
                         : *::google::protobuf::testing::AnyWrapper::internal_default_instance();
}
::google::protobuf::testing::AnyWrapper* AnyTestCases::mutable_any_with_message_with_wrapper_type() {
  
  if (any_with_message_with_wrapper_type_ == NULL) {
    any_with_message_with_wrapper_type_ = new ::google::protobuf::testing::AnyWrapper;
  }
  // @@protoc_insertion_point(field_mutable:google.protobuf.testing.AnyTestCases.any_with_message_with_wrapper_type)
  return any_with_message_with_wrapper_type_;
}
::google::protobuf::testing::AnyWrapper* AnyTestCases::release_any_with_message_with_wrapper_type() {
  // @@protoc_insertion_point(field_release:google.protobuf.testing.AnyTestCases.any_with_message_with_wrapper_type)
  
  ::google::protobuf::testing::AnyWrapper* temp = any_with_message_with_wrapper_type_;
  any_with_message_with_wrapper_type_ = NULL;
  return temp;
}
void AnyTestCases::set_allocated_any_with_message_with_wrapper_type(::google::protobuf::testing::AnyWrapper* any_with_message_with_wrapper_type) {
  delete any_with_message_with_wrapper_type_;
  any_with_message_with_wrapper_type_ = any_with_message_with_wrapper_type;
  if (any_with_message_with_wrapper_type) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_set_allocated:google.protobuf.testing.AnyTestCases.any_with_message_with_wrapper_type)
}

// optional .google.protobuf.testing.AnyWrapper any_with_message_with_timestamp = 11;
bool AnyTestCases::has_any_with_message_with_timestamp() const {
  return this != internal_default_instance() && any_with_message_with_timestamp_ != NULL;
}
void AnyTestCases::clear_any_with_message_with_timestamp() {
  if (GetArenaNoVirtual() == NULL && any_with_message_with_timestamp_ != NULL) delete any_with_message_with_timestamp_;
  any_with_message_with_timestamp_ = NULL;
}
const ::google::protobuf::testing::AnyWrapper& AnyTestCases::any_with_message_with_timestamp() const {
  // @@protoc_insertion_point(field_get:google.protobuf.testing.AnyTestCases.any_with_message_with_timestamp)
  return any_with_message_with_timestamp_ != NULL ? *any_with_message_with_timestamp_
                         : *::google::protobuf::testing::AnyWrapper::internal_default_instance();
}
::google::protobuf::testing::AnyWrapper* AnyTestCases::mutable_any_with_message_with_timestamp() {
  
  if (any_with_message_with_timestamp_ == NULL) {
    any_with_message_with_timestamp_ = new ::google::protobuf::testing::AnyWrapper;
  }
  // @@protoc_insertion_point(field_mutable:google.protobuf.testing.AnyTestCases.any_with_message_with_timestamp)
  return any_with_message_with_timestamp_;
}
::google::protobuf::testing::AnyWrapper* AnyTestCases::release_any_with_message_with_timestamp() {
  // @@protoc_insertion_point(field_release:google.protobuf.testing.AnyTestCases.any_with_message_with_timestamp)
  
  ::google::protobuf::testing::AnyWrapper* temp = any_with_message_with_timestamp_;
  any_with_message_with_timestamp_ = NULL;
  return temp;
}
void AnyTestCases::set_allocated_any_with_message_with_timestamp(::google::protobuf::testing::AnyWrapper* any_with_message_with_timestamp) {
  delete any_with_message_with_timestamp_;
  any_with_message_with_timestamp_ = any_with_message_with_timestamp;
  if (any_with_message_with_timestamp) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_set_allocated:google.protobuf.testing.AnyTestCases.any_with_message_with_timestamp)
}

// optional .google.protobuf.testing.AnyWrapper any_with_message_containing_map = 12;
bool AnyTestCases::has_any_with_message_containing_map() const {
  return this != internal_default_instance() && any_with_message_containing_map_ != NULL;
}
void AnyTestCases::clear_any_with_message_containing_map() {
  if (GetArenaNoVirtual() == NULL && any_with_message_containing_map_ != NULL) delete any_with_message_containing_map_;
  any_with_message_containing_map_ = NULL;
}
const ::google::protobuf::testing::AnyWrapper& AnyTestCases::any_with_message_containing_map() const {
  // @@protoc_insertion_point(field_get:google.protobuf.testing.AnyTestCases.any_with_message_containing_map)
  return any_with_message_containing_map_ != NULL ? *any_with_message_containing_map_
                         : *::google::protobuf::testing::AnyWrapper::internal_default_instance();
}
::google::protobuf::testing::AnyWrapper* AnyTestCases::mutable_any_with_message_containing_map() {
  
  if (any_with_message_containing_map_ == NULL) {
    any_with_message_containing_map_ = new ::google::protobuf::testing::AnyWrapper;
  }
  // @@protoc_insertion_point(field_mutable:google.protobuf.testing.AnyTestCases.any_with_message_containing_map)
  return any_with_message_containing_map_;
}
::google::protobuf::testing::AnyWrapper* AnyTestCases::release_any_with_message_containing_map() {
  // @@protoc_insertion_point(field_release:google.protobuf.testing.AnyTestCases.any_with_message_containing_map)
  
  ::google::protobuf::testing::AnyWrapper* temp = any_with_message_containing_map_;
  any_with_message_containing_map_ = NULL;
  return temp;
}
void AnyTestCases::set_allocated_any_with_message_containing_map(::google::protobuf::testing::AnyWrapper* any_with_message_containing_map) {
  delete any_with_message_containing_map_;
  any_with_message_containing_map_ = any_with_message_containing_map;
  if (any_with_message_containing_map) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_set_allocated:google.protobuf.testing.AnyTestCases.any_with_message_containing_map)
}

// optional .google.protobuf.testing.AnyWrapper any_with_message_containing_struct = 13;
bool AnyTestCases::has_any_with_message_containing_struct() const {
  return this != internal_default_instance() && any_with_message_containing_struct_ != NULL;
}
void AnyTestCases::clear_any_with_message_containing_struct() {
  if (GetArenaNoVirtual() == NULL && any_with_message_containing_struct_ != NULL) delete any_with_message_containing_struct_;
  any_with_message_containing_struct_ = NULL;
}
const ::google::protobuf::testing::AnyWrapper& AnyTestCases::any_with_message_containing_struct() const {
  // @@protoc_insertion_point(field_get:google.protobuf.testing.AnyTestCases.any_with_message_containing_struct)
  return any_with_message_containing_struct_ != NULL ? *any_with_message_containing_struct_
                         : *::google::protobuf::testing::AnyWrapper::internal_default_instance();
}
::google::protobuf::testing::AnyWrapper* AnyTestCases::mutable_any_with_message_containing_struct() {
  
  if (any_with_message_containing_struct_ == NULL) {
    any_with_message_containing_struct_ = new ::google::protobuf::testing::AnyWrapper;
  }
  // @@protoc_insertion_point(field_mutable:google.protobuf.testing.AnyTestCases.any_with_message_containing_struct)
  return any_with_message_containing_struct_;
}
::google::protobuf::testing::AnyWrapper* AnyTestCases::release_any_with_message_containing_struct() {
  // @@protoc_insertion_point(field_release:google.protobuf.testing.AnyTestCases.any_with_message_containing_struct)
  
  ::google::protobuf::testing::AnyWrapper* temp = any_with_message_containing_struct_;
  any_with_message_containing_struct_ = NULL;
  return temp;
}
void AnyTestCases::set_allocated_any_with_message_containing_struct(::google::protobuf::testing::AnyWrapper* any_with_message_containing_struct) {
  delete any_with_message_containing_struct_;
  any_with_message_containing_struct_ = any_with_message_containing_struct;
  if (any_with_message_containing_struct) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_set_allocated:google.protobuf.testing.AnyTestCases.any_with_message_containing_struct)
}

// optional .google.protobuf.testing.AnyWrapper any_with_message_containing_repeated_message = 14;
bool AnyTestCases::has_any_with_message_containing_repeated_message() const {
  return this != internal_default_instance() && any_with_message_containing_repeated_message_ != NULL;
}
void AnyTestCases::clear_any_with_message_containing_repeated_message() {
  if (GetArenaNoVirtual() == NULL && any_with_message_containing_repeated_message_ != NULL) delete any_with_message_containing_repeated_message_;
  any_with_message_containing_repeated_message_ = NULL;
}
const ::google::protobuf::testing::AnyWrapper& AnyTestCases::any_with_message_containing_repeated_message() const {
  // @@protoc_insertion_point(field_get:google.protobuf.testing.AnyTestCases.any_with_message_containing_repeated_message)
  return any_with_message_containing_repeated_message_ != NULL ? *any_with_message_containing_repeated_message_
                         : *::google::protobuf::testing::AnyWrapper::internal_default_instance();
}
::google::protobuf::testing::AnyWrapper* AnyTestCases::mutable_any_with_message_containing_repeated_message() {
  
  if (any_with_message_containing_repeated_message_ == NULL) {
    any_with_message_containing_repeated_message_ = new ::google::protobuf::testing::AnyWrapper;
  }
  // @@protoc_insertion_point(field_mutable:google.protobuf.testing.AnyTestCases.any_with_message_containing_repeated_message)
  return any_with_message_containing_repeated_message_;
}
::google::protobuf::testing::AnyWrapper* AnyTestCases::release_any_with_message_containing_repeated_message() {
  // @@protoc_insertion_point(field_release:google.protobuf.testing.AnyTestCases.any_with_message_containing_repeated_message)
  
  ::google::protobuf::testing::AnyWrapper* temp = any_with_message_containing_repeated_message_;
  any_with_message_containing_repeated_message_ = NULL;
  return temp;
}
void AnyTestCases::set_allocated_any_with_message_containing_repeated_message(::google::protobuf::testing::AnyWrapper* any_with_message_containing_repeated_message) {
  delete any_with_message_containing_repeated_message_;
  any_with_message_containing_repeated_message_ = any_with_message_containing_repeated_message;
  if (any_with_message_containing_repeated_message) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_set_allocated:google.protobuf.testing.AnyTestCases.any_with_message_containing_repeated_message)
}

// optional .google.protobuf.testing.AnyWrapper recursive_any_with_type_field_at_end = 15;
bool AnyTestCases::has_recursive_any_with_type_field_at_end() const {
  return this != internal_default_instance() && recursive_any_with_type_field_at_end_ != NULL;
}
void AnyTestCases::clear_recursive_any_with_type_field_at_end() {
  if (GetArenaNoVirtual() == NULL && recursive_any_with_type_field_at_end_ != NULL) delete recursive_any_with_type_field_at_end_;
  recursive_any_with_type_field_at_end_ = NULL;
}
const ::google::protobuf::testing::AnyWrapper& AnyTestCases::recursive_any_with_type_field_at_end() const {
  // @@protoc_insertion_point(field_get:google.protobuf.testing.AnyTestCases.recursive_any_with_type_field_at_end)
  return recursive_any_with_type_field_at_end_ != NULL ? *recursive_any_with_type_field_at_end_
                         : *::google::protobuf::testing::AnyWrapper::internal_default_instance();
}
::google::protobuf::testing::AnyWrapper* AnyTestCases::mutable_recursive_any_with_type_field_at_end() {
  
  if (recursive_any_with_type_field_at_end_ == NULL) {
    recursive_any_with_type_field_at_end_ = new ::google::protobuf::testing::AnyWrapper;
  }
  // @@protoc_insertion_point(field_mutable:google.protobuf.testing.AnyTestCases.recursive_any_with_type_field_at_end)
  return recursive_any_with_type_field_at_end_;
}
::google::protobuf::testing::AnyWrapper* AnyTestCases::release_recursive_any_with_type_field_at_end() {
  // @@protoc_insertion_point(field_release:google.protobuf.testing.AnyTestCases.recursive_any_with_type_field_at_end)
  
  ::google::protobuf::testing::AnyWrapper* temp = recursive_any_with_type_field_at_end_;
  recursive_any_with_type_field_at_end_ = NULL;
  return temp;
}
void AnyTestCases::set_allocated_recursive_any_with_type_field_at_end(::google::protobuf::testing::AnyWrapper* recursive_any_with_type_field_at_end) {
  delete recursive_any_with_type_field_at_end_;
  recursive_any_with_type_field_at_end_ = recursive_any_with_type_field_at_end;
  if (recursive_any_with_type_field_at_end) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_set_allocated:google.protobuf.testing.AnyTestCases.recursive_any_with_type_field_at_end)
}

// optional .google.protobuf.Any top_level_any = 50;
bool AnyTestCases::has_top_level_any() const {
  return this != internal_default_instance() && top_level_any_ != NULL;
}
void AnyTestCases::clear_top_level_any() {
  if (GetArenaNoVirtual() == NULL && top_level_any_ != NULL) delete top_level_any_;
  top_level_any_ = NULL;
}
const ::google::protobuf::Any& AnyTestCases::top_level_any() const {
  // @@protoc_insertion_point(field_get:google.protobuf.testing.AnyTestCases.top_level_any)
  return top_level_any_ != NULL ? *top_level_any_
                         : *::google::protobuf::Any::internal_default_instance();
}
::google::protobuf::Any* AnyTestCases::mutable_top_level_any() {
  
  if (top_level_any_ == NULL) {
    top_level_any_ = new ::google::protobuf::Any;
  }
  // @@protoc_insertion_point(field_mutable:google.protobuf.testing.AnyTestCases.top_level_any)
  return top_level_any_;
}
::google::protobuf::Any* AnyTestCases::release_top_level_any() {
  // @@protoc_insertion_point(field_release:google.protobuf.testing.AnyTestCases.top_level_any)
  
  ::google::protobuf::Any* temp = top_level_any_;
  top_level_any_ = NULL;
  return temp;
}
void AnyTestCases::set_allocated_top_level_any(::google::protobuf::Any* top_level_any) {
  delete top_level_any_;
  top_level_any_ = top_level_any;
  if (top_level_any) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_set_allocated:google.protobuf.testing.AnyTestCases.top_level_any)
}

// optional .google.protobuf.Any top_level_any_with_type_field_at_end = 51;
bool AnyTestCases::has_top_level_any_with_type_field_at_end() const {
  return this != internal_default_instance() && top_level_any_with_type_field_at_end_ != NULL;
}
void AnyTestCases::clear_top_level_any_with_type_field_at_end() {
  if (GetArenaNoVirtual() == NULL && top_level_any_with_type_field_at_end_ != NULL) delete top_level_any_with_type_field_at_end_;
  top_level_any_with_type_field_at_end_ = NULL;
}
const ::google::protobuf::Any& AnyTestCases::top_level_any_with_type_field_at_end() const {
  // @@protoc_insertion_point(field_get:google.protobuf.testing.AnyTestCases.top_level_any_with_type_field_at_end)
  return top_level_any_with_type_field_at_end_ != NULL ? *top_level_any_with_type_field_at_end_
                         : *::google::protobuf::Any::internal_default_instance();
}
::google::protobuf::Any* AnyTestCases::mutable_top_level_any_with_type_field_at_end() {
  
  if (top_level_any_with_type_field_at_end_ == NULL) {
    top_level_any_with_type_field_at_end_ = new ::google::protobuf::Any;
  }
  // @@protoc_insertion_point(field_mutable:google.protobuf.testing.AnyTestCases.top_level_any_with_type_field_at_end)
  return top_level_any_with_type_field_at_end_;
}
::google::protobuf::Any* AnyTestCases::release_top_level_any_with_type_field_at_end() {
  // @@protoc_insertion_point(field_release:google.protobuf.testing.AnyTestCases.top_level_any_with_type_field_at_end)
  
  ::google::protobuf::Any* temp = top_level_any_with_type_field_at_end_;
  top_level_any_with_type_field_at_end_ = NULL;
  return temp;
}
void AnyTestCases::set_allocated_top_level_any_with_type_field_at_end(::google::protobuf::Any* top_level_any_with_type_field_at_end) {
  delete top_level_any_with_type_field_at_end_;
  top_level_any_with_type_field_at_end_ = top_level_any_with_type_field_at_end;
  if (top_level_any_with_type_field_at_end) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_set_allocated:google.protobuf.testing.AnyTestCases.top_level_any_with_type_field_at_end)
}

inline const AnyTestCases* AnyTestCases::internal_default_instance() {
  return &AnyTestCases_default_instance_.get();
}
#endif  // PROTOBUF_INLINE_NOT_IN_HEADERS

// ===================================================================

#if !defined(_MSC_VER) || _MSC_VER >= 1900
const int AnyWrapper::kAnyFieldNumber;
#endif  // !defined(_MSC_VER) || _MSC_VER >= 1900

AnyWrapper::AnyWrapper()
  : ::google::protobuf::Message(), _internal_metadata_(NULL) {
  if (this != internal_default_instance()) protobuf_InitDefaults_google_2fprotobuf_2futil_2finternal_2ftestdata_2fanys_2eproto();
  SharedCtor();
  // @@protoc_insertion_point(constructor:google.protobuf.testing.AnyWrapper)
}

void AnyWrapper::InitAsDefaultInstance() {
  any_ = const_cast< ::google::protobuf::Any*>(
      ::google::protobuf::Any::internal_default_instance());
}

AnyWrapper::AnyWrapper(const AnyWrapper& from)
  : ::google::protobuf::Message(),
    _internal_metadata_(NULL) {
  SharedCtor();
  UnsafeMergeFrom(from);
  // @@protoc_insertion_point(copy_constructor:google.protobuf.testing.AnyWrapper)
}

void AnyWrapper::SharedCtor() {
  any_ = NULL;
  _cached_size_ = 0;
}

AnyWrapper::~AnyWrapper() {
  // @@protoc_insertion_point(destructor:google.protobuf.testing.AnyWrapper)
  SharedDtor();
}

void AnyWrapper::SharedDtor() {
  if (this != &AnyWrapper_default_instance_.get()) {
    delete any_;
  }
}

void AnyWrapper::SetCachedSize(int size) const {
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
}
const ::google::protobuf::Descriptor* AnyWrapper::descriptor() {
  protobuf_AssignDescriptorsOnce();
  return AnyWrapper_descriptor_;
}

const AnyWrapper& AnyWrapper::default_instance() {
  protobuf_InitDefaults_google_2fprotobuf_2futil_2finternal_2ftestdata_2fanys_2eproto();
  return *internal_default_instance();
}

::google::protobuf::internal::ExplicitlyConstructed<AnyWrapper> AnyWrapper_default_instance_;

AnyWrapper* AnyWrapper::New(::google::protobuf::Arena* arena) const {
  AnyWrapper* n = new AnyWrapper;
  if (arena != NULL) {
    arena->Own(n);
  }
  return n;
}

void AnyWrapper::Clear() {
// @@protoc_insertion_point(message_clear_start:google.protobuf.testing.AnyWrapper)
  if (GetArenaNoVirtual() == NULL && any_ != NULL) delete any_;
  any_ = NULL;
}

bool AnyWrapper::MergePartialFromCodedStream(
    ::google::protobuf::io::CodedInputStream* input) {
#define DO_(EXPRESSION) if (!GOOGLE_PREDICT_TRUE(EXPRESSION)) goto failure
  ::google::protobuf::uint32 tag;
  // @@protoc_insertion_point(parse_start:google.protobuf.testing.AnyWrapper)
  for (;;) {
    ::std::pair< ::google::protobuf::uint32, bool> p = input->ReadTagWithCutoff(127);
    tag = p.first;
    if (!p.second) goto handle_unusual;
    switch (::google::protobuf::internal::WireFormatLite::GetTagFieldNumber(tag)) {
      // optional .google.protobuf.Any any = 1;
      case 1: {
        if (tag == 10) {
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessageNoVirtual(
               input, mutable_any()));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectAtEnd()) goto success;
        break;
      }

      default: {
      handle_unusual:
        if (tag == 0 ||
            ::google::protobuf::internal::WireFormatLite::GetTagWireType(tag) ==
            ::google::protobuf::internal::WireFormatLite::WIRETYPE_END_GROUP) {
          goto success;
        }
        DO_(::google::protobuf::internal::WireFormatLite::SkipField(input, tag));
        break;
      }
    }
  }
success:
  // @@protoc_insertion_point(parse_success:google.protobuf.testing.AnyWrapper)
  return true;
failure:
  // @@protoc_insertion_point(parse_failure:google.protobuf.testing.AnyWrapper)
  return false;
#undef DO_
}

void AnyWrapper::SerializeWithCachedSizes(
    ::google::protobuf::io::CodedOutputStream* output) const {
  // @@protoc_insertion_point(serialize_start:google.protobuf.testing.AnyWrapper)
  // optional .google.protobuf.Any any = 1;
  if (this->has_any()) {
    ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
      1, *this->any_, output);
  }

  // @@protoc_insertion_point(serialize_end:google.protobuf.testing.AnyWrapper)
}

::google::protobuf::uint8* AnyWrapper::InternalSerializeWithCachedSizesToArray(
    bool deterministic, ::google::protobuf::uint8* target) const {
  (void)deterministic; // Unused
  // @@protoc_insertion_point(serialize_to_array_start:google.protobuf.testing.AnyWrapper)
  // optional .google.protobuf.Any any = 1;
  if (this->has_any()) {
    target = ::google::protobuf::internal::WireFormatLite::
      InternalWriteMessageNoVirtualToArray(
        1, *this->any_, false, target);
  }

  // @@protoc_insertion_point(serialize_to_array_end:google.protobuf.testing.AnyWrapper)
  return target;
}

size_t AnyWrapper::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:google.protobuf.testing.AnyWrapper)
  size_t total_size = 0;

  // optional .google.protobuf.Any any = 1;
  if (this->has_any()) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::MessageSizeNoVirtual(
        *this->any_);
  }

  int cached_size = ::google::protobuf::internal::ToCachedSize(total_size);
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = cached_size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
  return total_size;
}

void AnyWrapper::MergeFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_merge_from_start:google.protobuf.testing.AnyWrapper)
  if (GOOGLE_PREDICT_FALSE(&from == this)) MergeFromFail(__LINE__);
  const AnyWrapper* source =
      ::google::protobuf::internal::DynamicCastToGenerated<const AnyWrapper>(
          &from);
  if (source == NULL) {
  // @@protoc_insertion_point(generalized_merge_from_cast_fail:google.protobuf.testing.AnyWrapper)
    ::google::protobuf::internal::ReflectionOps::Merge(from, this);
  } else {
  // @@protoc_insertion_point(generalized_merge_from_cast_success:google.protobuf.testing.AnyWrapper)
    UnsafeMergeFrom(*source);
  }
}

void AnyWrapper::MergeFrom(const AnyWrapper& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:google.protobuf.testing.AnyWrapper)
  if (GOOGLE_PREDICT_TRUE(&from != this)) {
    UnsafeMergeFrom(from);
  } else {
    MergeFromFail(__LINE__);
  }
}

void AnyWrapper::UnsafeMergeFrom(const AnyWrapper& from) {
  GOOGLE_DCHECK(&from != this);
  if (from.has_any()) {
    mutable_any()->::google::protobuf::Any::MergeFrom(from.any());
  }
}

void AnyWrapper::CopyFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_copy_from_start:google.protobuf.testing.AnyWrapper)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

void AnyWrapper::CopyFrom(const AnyWrapper& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:google.protobuf.testing.AnyWrapper)
  if (&from == this) return;
  Clear();
  UnsafeMergeFrom(from);
}

bool AnyWrapper::IsInitialized() const {

  return true;
}

void AnyWrapper::Swap(AnyWrapper* other) {
  if (other == this) return;
  InternalSwap(other);
}
void AnyWrapper::InternalSwap(AnyWrapper* other) {
  std::swap(any_, other->any_);
  _internal_metadata_.Swap(&other->_internal_metadata_);
  std::swap(_cached_size_, other->_cached_size_);
}

::google::protobuf::Metadata AnyWrapper::GetMetadata() const {
  protobuf_AssignDescriptorsOnce();
  ::google::protobuf::Metadata metadata;
  metadata.descriptor = AnyWrapper_descriptor_;
  metadata.reflection = AnyWrapper_reflection_;
  return metadata;
}

#if PROTOBUF_INLINE_NOT_IN_HEADERS
// AnyWrapper

// optional .google.protobuf.Any any = 1;
bool AnyWrapper::has_any() const {
  return this != internal_default_instance() && any_ != NULL;
}
void AnyWrapper::clear_any() {
  if (GetArenaNoVirtual() == NULL && any_ != NULL) delete any_;
  any_ = NULL;
}
const ::google::protobuf::Any& AnyWrapper::any() const {
  // @@protoc_insertion_point(field_get:google.protobuf.testing.AnyWrapper.any)
  return any_ != NULL ? *any_
                         : *::google::protobuf::Any::internal_default_instance();
}
::google::protobuf::Any* AnyWrapper::mutable_any() {
  
  if (any_ == NULL) {
    any_ = new ::google::protobuf::Any;
  }
  // @@protoc_insertion_point(field_mutable:google.protobuf.testing.AnyWrapper.any)
  return any_;
}
::google::protobuf::Any* AnyWrapper::release_any() {
  // @@protoc_insertion_point(field_release:google.protobuf.testing.AnyWrapper.any)
  
  ::google::protobuf::Any* temp = any_;
  any_ = NULL;
  return temp;
}
void AnyWrapper::set_allocated_any(::google::protobuf::Any* any) {
  delete any_;
  any_ = any;
  if (any) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_set_allocated:google.protobuf.testing.AnyWrapper.any)
}

inline const AnyWrapper* AnyWrapper::internal_default_instance() {
  return &AnyWrapper_default_instance_.get();
}
#endif  // PROTOBUF_INLINE_NOT_IN_HEADERS

// ===================================================================

#if !defined(_MSC_VER) || _MSC_VER >= 1900
const int Imports::kDblFieldNumber;
const int Imports::kStructFieldNumber;
const int Imports::kTimestampFieldNumber;
const int Imports::kDurationFieldNumber;
const int Imports::kI32FieldNumber;
const int Imports::kDataFieldNumber;
#endif  // !defined(_MSC_VER) || _MSC_VER >= 1900

Imports::Imports()
  : ::google::protobuf::Message(), _internal_metadata_(NULL) {
  if (this != internal_default_instance()) protobuf_InitDefaults_google_2fprotobuf_2futil_2finternal_2ftestdata_2fanys_2eproto();
  SharedCtor();
  // @@protoc_insertion_point(constructor:google.protobuf.testing.Imports)
}

void Imports::InitAsDefaultInstance() {
  dbl_ = const_cast< ::google::protobuf::DoubleValue*>(
      ::google::protobuf::DoubleValue::internal_default_instance());
  struct__ = const_cast< ::google::protobuf::Struct*>(
      ::google::protobuf::Struct::internal_default_instance());
  timestamp_ = const_cast< ::google::protobuf::Timestamp*>(
      ::google::protobuf::Timestamp::internal_default_instance());
  duration_ = const_cast< ::google::protobuf::Duration*>(
      ::google::protobuf::Duration::internal_default_instance());
  i32_ = const_cast< ::google::protobuf::Int32Value*>(
      ::google::protobuf::Int32Value::internal_default_instance());
  data_ = const_cast< ::google::protobuf::testing::Data*>(
      ::google::protobuf::testing::Data::internal_default_instance());
}

Imports::Imports(const Imports& from)
  : ::google::protobuf::Message(),
    _internal_metadata_(NULL) {
  SharedCtor();
  UnsafeMergeFrom(from);
  // @@protoc_insertion_point(copy_constructor:google.protobuf.testing.Imports)
}

void Imports::SharedCtor() {
  dbl_ = NULL;
  struct__ = NULL;
  timestamp_ = NULL;
  duration_ = NULL;
  i32_ = NULL;
  data_ = NULL;
  _cached_size_ = 0;
}

Imports::~Imports() {
  // @@protoc_insertion_point(destructor:google.protobuf.testing.Imports)
  SharedDtor();
}

void Imports::SharedDtor() {
  if (this != &Imports_default_instance_.get()) {
    delete dbl_;
    delete struct__;
    delete timestamp_;
    delete duration_;
    delete i32_;
    delete data_;
  }
}

void Imports::SetCachedSize(int size) const {
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
}
const ::google::protobuf::Descriptor* Imports::descriptor() {
  protobuf_AssignDescriptorsOnce();
  return Imports_descriptor_;
}

const Imports& Imports::default_instance() {
  protobuf_InitDefaults_google_2fprotobuf_2futil_2finternal_2ftestdata_2fanys_2eproto();
  return *internal_default_instance();
}

::google::protobuf::internal::ExplicitlyConstructed<Imports> Imports_default_instance_;

Imports* Imports::New(::google::protobuf::Arena* arena) const {
  Imports* n = new Imports;
  if (arena != NULL) {
    arena->Own(n);
  }
  return n;
}

void Imports::Clear() {
// @@protoc_insertion_point(message_clear_start:google.protobuf.testing.Imports)
  if (GetArenaNoVirtual() == NULL && dbl_ != NULL) delete dbl_;
  dbl_ = NULL;
  if (GetArenaNoVirtual() == NULL && struct__ != NULL) delete struct__;
  struct__ = NULL;
  if (GetArenaNoVirtual() == NULL && timestamp_ != NULL) delete timestamp_;
  timestamp_ = NULL;
  if (GetArenaNoVirtual() == NULL && duration_ != NULL) delete duration_;
  duration_ = NULL;
  if (GetArenaNoVirtual() == NULL && i32_ != NULL) delete i32_;
  i32_ = NULL;
  if (GetArenaNoVirtual() == NULL && data_ != NULL) delete data_;
  data_ = NULL;
}

bool Imports::MergePartialFromCodedStream(
    ::google::protobuf::io::CodedInputStream* input) {
#define DO_(EXPRESSION) if (!GOOGLE_PREDICT_TRUE(EXPRESSION)) goto failure
  ::google::protobuf::uint32 tag;
  // @@protoc_insertion_point(parse_start:google.protobuf.testing.Imports)
  for (;;) {
    ::std::pair< ::google::protobuf::uint32, bool> p = input->ReadTagWithCutoff(16383);
    tag = p.first;
    if (!p.second) goto handle_unusual;
    switch (::google::protobuf::internal::WireFormatLite::GetTagFieldNumber(tag)) {
      // optional .google.protobuf.DoubleValue dbl = 1;
      case 1: {
        if (tag == 10) {
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessageNoVirtual(
               input, mutable_dbl()));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(18)) goto parse_struct;
        break;
      }

      // optional .google.protobuf.Struct struct = 2;
      case 2: {
        if (tag == 18) {
         parse_struct:
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessageNoVirtual(
               input, mutable_struct_()));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(26)) goto parse_timestamp;
        break;
      }

      // optional .google.protobuf.Timestamp timestamp = 3;
      case 3: {
        if (tag == 26) {
         parse_timestamp:
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessageNoVirtual(
               input, mutable_timestamp()));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(34)) goto parse_duration;
        break;
      }

      // optional .google.protobuf.Duration duration = 4;
      case 4: {
        if (tag == 34) {
         parse_duration:
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessageNoVirtual(
               input, mutable_duration()));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(42)) goto parse_i32;
        break;
      }

      // optional .google.protobuf.Int32Value i32 = 5;
      case 5: {
        if (tag == 42) {
         parse_i32:
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessageNoVirtual(
               input, mutable_i32()));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(802)) goto parse_data;
        break;
      }

      // optional .google.protobuf.testing.Data data = 100;
      case 100: {
        if (tag == 802) {
         parse_data:
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessageNoVirtual(
               input, mutable_data()));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectAtEnd()) goto success;
        break;
      }

      default: {
      handle_unusual:
        if (tag == 0 ||
            ::google::protobuf::internal::WireFormatLite::GetTagWireType(tag) ==
            ::google::protobuf::internal::WireFormatLite::WIRETYPE_END_GROUP) {
          goto success;
        }
        DO_(::google::protobuf::internal::WireFormatLite::SkipField(input, tag));
        break;
      }
    }
  }
success:
  // @@protoc_insertion_point(parse_success:google.protobuf.testing.Imports)
  return true;
failure:
  // @@protoc_insertion_point(parse_failure:google.protobuf.testing.Imports)
  return false;
#undef DO_
}

void Imports::SerializeWithCachedSizes(
    ::google::protobuf::io::CodedOutputStream* output) const {
  // @@protoc_insertion_point(serialize_start:google.protobuf.testing.Imports)
  // optional .google.protobuf.DoubleValue dbl = 1;
  if (this->has_dbl()) {
    ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
      1, *this->dbl_, output);
  }

  // optional .google.protobuf.Struct struct = 2;
  if (this->has_struct_()) {
    ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
      2, *this->struct__, output);
  }

  // optional .google.protobuf.Timestamp timestamp = 3;
  if (this->has_timestamp()) {
    ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
      3, *this->timestamp_, output);
  }

  // optional .google.protobuf.Duration duration = 4;
  if (this->has_duration()) {
    ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
      4, *this->duration_, output);
  }

  // optional .google.protobuf.Int32Value i32 = 5;
  if (this->has_i32()) {
    ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
      5, *this->i32_, output);
  }

  // optional .google.protobuf.testing.Data data = 100;
  if (this->has_data()) {
    ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
      100, *this->data_, output);
  }

  // @@protoc_insertion_point(serialize_end:google.protobuf.testing.Imports)
}

::google::protobuf::uint8* Imports::InternalSerializeWithCachedSizesToArray(
    bool deterministic, ::google::protobuf::uint8* target) const {
  (void)deterministic; // Unused
  // @@protoc_insertion_point(serialize_to_array_start:google.protobuf.testing.Imports)
  // optional .google.protobuf.DoubleValue dbl = 1;
  if (this->has_dbl()) {
    target = ::google::protobuf::internal::WireFormatLite::
      InternalWriteMessageNoVirtualToArray(
        1, *this->dbl_, false, target);
  }

  // optional .google.protobuf.Struct struct = 2;
  if (this->has_struct_()) {
    target = ::google::protobuf::internal::WireFormatLite::
      InternalWriteMessageNoVirtualToArray(
        2, *this->struct__, false, target);
  }

  // optional .google.protobuf.Timestamp timestamp = 3;
  if (this->has_timestamp()) {
    target = ::google::protobuf::internal::WireFormatLite::
      InternalWriteMessageNoVirtualToArray(
        3, *this->timestamp_, false, target);
  }

  // optional .google.protobuf.Duration duration = 4;
  if (this->has_duration()) {
    target = ::google::protobuf::internal::WireFormatLite::
      InternalWriteMessageNoVirtualToArray(
        4, *this->duration_, false, target);
  }

  // optional .google.protobuf.Int32Value i32 = 5;
  if (this->has_i32()) {
    target = ::google::protobuf::internal::WireFormatLite::
      InternalWriteMessageNoVirtualToArray(
        5, *this->i32_, false, target);
  }

  // optional .google.protobuf.testing.Data data = 100;
  if (this->has_data()) {
    target = ::google::protobuf::internal::WireFormatLite::
      InternalWriteMessageNoVirtualToArray(
        100, *this->data_, false, target);
  }

  // @@protoc_insertion_point(serialize_to_array_end:google.protobuf.testing.Imports)
  return target;
}

size_t Imports::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:google.protobuf.testing.Imports)
  size_t total_size = 0;

  // optional .google.protobuf.DoubleValue dbl = 1;
  if (this->has_dbl()) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::MessageSizeNoVirtual(
        *this->dbl_);
  }

  // optional .google.protobuf.Struct struct = 2;
  if (this->has_struct_()) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::MessageSizeNoVirtual(
        *this->struct__);
  }

  // optional .google.protobuf.Timestamp timestamp = 3;
  if (this->has_timestamp()) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::MessageSizeNoVirtual(
        *this->timestamp_);
  }

  // optional .google.protobuf.Duration duration = 4;
  if (this->has_duration()) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::MessageSizeNoVirtual(
        *this->duration_);
  }

  // optional .google.protobuf.Int32Value i32 = 5;
  if (this->has_i32()) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::MessageSizeNoVirtual(
        *this->i32_);
  }

  // optional .google.protobuf.testing.Data data = 100;
  if (this->has_data()) {
    total_size += 2 +
      ::google::protobuf::internal::WireFormatLite::MessageSizeNoVirtual(
        *this->data_);
  }

  int cached_size = ::google::protobuf::internal::ToCachedSize(total_size);
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = cached_size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
  return total_size;
}

void Imports::MergeFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_merge_from_start:google.protobuf.testing.Imports)
  if (GOOGLE_PREDICT_FALSE(&from == this)) MergeFromFail(__LINE__);
  const Imports* source =
      ::google::protobuf::internal::DynamicCastToGenerated<const Imports>(
          &from);
  if (source == NULL) {
  // @@protoc_insertion_point(generalized_merge_from_cast_fail:google.protobuf.testing.Imports)
    ::google::protobuf::internal::ReflectionOps::Merge(from, this);
  } else {
  // @@protoc_insertion_point(generalized_merge_from_cast_success:google.protobuf.testing.Imports)
    UnsafeMergeFrom(*source);
  }
}

void Imports::MergeFrom(const Imports& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:google.protobuf.testing.Imports)
  if (GOOGLE_PREDICT_TRUE(&from != this)) {
    UnsafeMergeFrom(from);
  } else {
    MergeFromFail(__LINE__);
  }
}

void Imports::UnsafeMergeFrom(const Imports& from) {
  GOOGLE_DCHECK(&from != this);
  if (from.has_dbl()) {
    mutable_dbl()->::google::protobuf::DoubleValue::MergeFrom(from.dbl());
  }
  if (from.has_struct_()) {
    mutable_struct_()->::google::protobuf::Struct::MergeFrom(from.struct_());
  }
  if (from.has_timestamp()) {
    mutable_timestamp()->::google::protobuf::Timestamp::MergeFrom(from.timestamp());
  }
  if (from.has_duration()) {
    mutable_duration()->::google::protobuf::Duration::MergeFrom(from.duration());
  }
  if (from.has_i32()) {
    mutable_i32()->::google::protobuf::Int32Value::MergeFrom(from.i32());
  }
  if (from.has_data()) {
    mutable_data()->::google::protobuf::testing::Data::MergeFrom(from.data());
  }
}

void Imports::CopyFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_copy_from_start:google.protobuf.testing.Imports)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

void Imports::CopyFrom(const Imports& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:google.protobuf.testing.Imports)
  if (&from == this) return;
  Clear();
  UnsafeMergeFrom(from);
}

bool Imports::IsInitialized() const {

  return true;
}

void Imports::Swap(Imports* other) {
  if (other == this) return;
  InternalSwap(other);
}
void Imports::InternalSwap(Imports* other) {
  std::swap(dbl_, other->dbl_);
  std::swap(struct__, other->struct__);
  std::swap(timestamp_, other->timestamp_);
  std::swap(duration_, other->duration_);
  std::swap(i32_, other->i32_);
  std::swap(data_, other->data_);
  _internal_metadata_.Swap(&other->_internal_metadata_);
  std::swap(_cached_size_, other->_cached_size_);
}

::google::protobuf::Metadata Imports::GetMetadata() const {
  protobuf_AssignDescriptorsOnce();
  ::google::protobuf::Metadata metadata;
  metadata.descriptor = Imports_descriptor_;
  metadata.reflection = Imports_reflection_;
  return metadata;
}

#if PROTOBUF_INLINE_NOT_IN_HEADERS
// Imports

// optional .google.protobuf.DoubleValue dbl = 1;
bool Imports::has_dbl() const {
  return this != internal_default_instance() && dbl_ != NULL;
}
void Imports::clear_dbl() {
  if (GetArenaNoVirtual() == NULL && dbl_ != NULL) delete dbl_;
  dbl_ = NULL;
}
const ::google::protobuf::DoubleValue& Imports::dbl() const {
  // @@protoc_insertion_point(field_get:google.protobuf.testing.Imports.dbl)
  return dbl_ != NULL ? *dbl_
                         : *::google::protobuf::DoubleValue::internal_default_instance();
}
::google::protobuf::DoubleValue* Imports::mutable_dbl() {
  
  if (dbl_ == NULL) {
    dbl_ = new ::google::protobuf::DoubleValue;
  }
  // @@protoc_insertion_point(field_mutable:google.protobuf.testing.Imports.dbl)
  return dbl_;
}
::google::protobuf::DoubleValue* Imports::release_dbl() {
  // @@protoc_insertion_point(field_release:google.protobuf.testing.Imports.dbl)
  
  ::google::protobuf::DoubleValue* temp = dbl_;
  dbl_ = NULL;
  return temp;
}
void Imports::set_allocated_dbl(::google::protobuf::DoubleValue* dbl) {
  delete dbl_;
  if (dbl != NULL && dbl->GetArena() != NULL) {
    ::google::protobuf::DoubleValue* new_dbl = new ::google::protobuf::DoubleValue;
    new_dbl->CopyFrom(*dbl);
    dbl = new_dbl;
  }
  dbl_ = dbl;
  if (dbl) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_set_allocated:google.protobuf.testing.Imports.dbl)
}

// optional .google.protobuf.Struct struct = 2;
bool Imports::has_struct_() const {
  return this != internal_default_instance() && struct__ != NULL;
}
void Imports::clear_struct_() {
  if (GetArenaNoVirtual() == NULL && struct__ != NULL) delete struct__;
  struct__ = NULL;
}
const ::google::protobuf::Struct& Imports::struct_() const {
  // @@protoc_insertion_point(field_get:google.protobuf.testing.Imports.struct)
  return struct__ != NULL ? *struct__
                         : *::google::protobuf::Struct::internal_default_instance();
}
::google::protobuf::Struct* Imports::mutable_struct_() {
  
  if (struct__ == NULL) {
    struct__ = new ::google::protobuf::Struct;
  }
  // @@protoc_insertion_point(field_mutable:google.protobuf.testing.Imports.struct)
  return struct__;
}
::google::protobuf::Struct* Imports::release_struct_() {
  // @@protoc_insertion_point(field_release:google.protobuf.testing.Imports.struct)
  
  ::google::protobuf::Struct* temp = struct__;
  struct__ = NULL;
  return temp;
}
void Imports::set_allocated_struct_(::google::protobuf::Struct* struct_) {
  delete struct__;
  if (struct_ != NULL && struct_->GetArena() != NULL) {
    ::google::protobuf::Struct* new_struct_ = new ::google::protobuf::Struct;
    new_struct_->CopyFrom(*struct_);
    struct_ = new_struct_;
  }
  struct__ = struct_;
  if (struct_) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_set_allocated:google.protobuf.testing.Imports.struct)
}

// optional .google.protobuf.Timestamp timestamp = 3;
bool Imports::has_timestamp() const {
  return this != internal_default_instance() && timestamp_ != NULL;
}
void Imports::clear_timestamp() {
  if (GetArenaNoVirtual() == NULL && timestamp_ != NULL) delete timestamp_;
  timestamp_ = NULL;
}
const ::google::protobuf::Timestamp& Imports::timestamp() const {
  // @@protoc_insertion_point(field_get:google.protobuf.testing.Imports.timestamp)
  return timestamp_ != NULL ? *timestamp_
                         : *::google::protobuf::Timestamp::internal_default_instance();
}
::google::protobuf::Timestamp* Imports::mutable_timestamp() {
  
  if (timestamp_ == NULL) {
    timestamp_ = new ::google::protobuf::Timestamp;
  }
  // @@protoc_insertion_point(field_mutable:google.protobuf.testing.Imports.timestamp)
  return timestamp_;
}
::google::protobuf::Timestamp* Imports::release_timestamp() {
  // @@protoc_insertion_point(field_release:google.protobuf.testing.Imports.timestamp)
  
  ::google::protobuf::Timestamp* temp = timestamp_;
  timestamp_ = NULL;
  return temp;
}
void Imports::set_allocated_timestamp(::google::protobuf::Timestamp* timestamp) {
  delete timestamp_;
  if (timestamp != NULL && timestamp->GetArena() != NULL) {
    ::google::protobuf::Timestamp* new_timestamp = new ::google::protobuf::Timestamp;
    new_timestamp->CopyFrom(*timestamp);
    timestamp = new_timestamp;
  }
  timestamp_ = timestamp;
  if (timestamp) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_set_allocated:google.protobuf.testing.Imports.timestamp)
}

// optional .google.protobuf.Duration duration = 4;
bool Imports::has_duration() const {
  return this != internal_default_instance() && duration_ != NULL;
}
void Imports::clear_duration() {
  if (GetArenaNoVirtual() == NULL && duration_ != NULL) delete duration_;
  duration_ = NULL;
}
const ::google::protobuf::Duration& Imports::duration() const {
  // @@protoc_insertion_point(field_get:google.protobuf.testing.Imports.duration)
  return duration_ != NULL ? *duration_
                         : *::google::protobuf::Duration::internal_default_instance();
}
::google::protobuf::Duration* Imports::mutable_duration() {
  
  if (duration_ == NULL) {
    duration_ = new ::google::protobuf::Duration;
  }
  // @@protoc_insertion_point(field_mutable:google.protobuf.testing.Imports.duration)
  return duration_;
}
::google::protobuf::Duration* Imports::release_duration() {
  // @@protoc_insertion_point(field_release:google.protobuf.testing.Imports.duration)
  
  ::google::protobuf::Duration* temp = duration_;
  duration_ = NULL;
  return temp;
}
void Imports::set_allocated_duration(::google::protobuf::Duration* duration) {
  delete duration_;
  if (duration != NULL && duration->GetArena() != NULL) {
    ::google::protobuf::Duration* new_duration = new ::google::protobuf::Duration;
    new_duration->CopyFrom(*duration);
    duration = new_duration;
  }
  duration_ = duration;
  if (duration) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_set_allocated:google.protobuf.testing.Imports.duration)
}

// optional .google.protobuf.Int32Value i32 = 5;
bool Imports::has_i32() const {
  return this != internal_default_instance() && i32_ != NULL;
}
void Imports::clear_i32() {
  if (GetArenaNoVirtual() == NULL && i32_ != NULL) delete i32_;
  i32_ = NULL;
}
const ::google::protobuf::Int32Value& Imports::i32() const {
  // @@protoc_insertion_point(field_get:google.protobuf.testing.Imports.i32)
  return i32_ != NULL ? *i32_
                         : *::google::protobuf::Int32Value::internal_default_instance();
}
::google::protobuf::Int32Value* Imports::mutable_i32() {
  
  if (i32_ == NULL) {
    i32_ = new ::google::protobuf::Int32Value;
  }
  // @@protoc_insertion_point(field_mutable:google.protobuf.testing.Imports.i32)
  return i32_;
}
::google::protobuf::Int32Value* Imports::release_i32() {
  // @@protoc_insertion_point(field_release:google.protobuf.testing.Imports.i32)
  
  ::google::protobuf::Int32Value* temp = i32_;
  i32_ = NULL;
  return temp;
}
void Imports::set_allocated_i32(::google::protobuf::Int32Value* i32) {
  delete i32_;
  if (i32 != NULL && i32->GetArena() != NULL) {
    ::google::protobuf::Int32Value* new_i32 = new ::google::protobuf::Int32Value;
    new_i32->CopyFrom(*i32);
    i32 = new_i32;
  }
  i32_ = i32;
  if (i32) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_set_allocated:google.protobuf.testing.Imports.i32)
}

// optional .google.protobuf.testing.Data data = 100;
bool Imports::has_data() const {
  return this != internal_default_instance() && data_ != NULL;
}
void Imports::clear_data() {
  if (GetArenaNoVirtual() == NULL && data_ != NULL) delete data_;
  data_ = NULL;
}
const ::google::protobuf::testing::Data& Imports::data() const {
  // @@protoc_insertion_point(field_get:google.protobuf.testing.Imports.data)
  return data_ != NULL ? *data_
                         : *::google::protobuf::testing::Data::internal_default_instance();
}
::google::protobuf::testing::Data* Imports::mutable_data() {
  
  if (data_ == NULL) {
    data_ = new ::google::protobuf::testing::Data;
  }
  // @@protoc_insertion_point(field_mutable:google.protobuf.testing.Imports.data)
  return data_;
}
::google::protobuf::testing::Data* Imports::release_data() {
  // @@protoc_insertion_point(field_release:google.protobuf.testing.Imports.data)
  
  ::google::protobuf::testing::Data* temp = data_;
  data_ = NULL;
  return temp;
}
void Imports::set_allocated_data(::google::protobuf::testing::Data* data) {
  delete data_;
  data_ = data;
  if (data) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_set_allocated:google.protobuf.testing.Imports.data)
}

inline const Imports* Imports::internal_default_instance() {
  return &Imports_default_instance_.get();
}
#endif  // PROTOBUF_INLINE_NOT_IN_HEADERS

// ===================================================================

#if !defined(_MSC_VER) || _MSC_VER >= 1900
const int Data::kAttrFieldNumber;
const int Data::kStrFieldNumber;
const int Data::kMsgsFieldNumber;
const int Data::kNestedDataFieldNumber;
const int Data::kIntWrapperFieldNumber;
const int Data::kTimeFieldNumber;
const int Data::kMapDataFieldNumber;
const int Data::kStructDataFieldNumber;
const int Data::kRepeatedDataFieldNumber;
#endif  // !defined(_MSC_VER) || _MSC_VER >= 1900

Data::Data()
  : ::google::protobuf::Message(), _internal_metadata_(NULL) {
  if (this != internal_default_instance()) protobuf_InitDefaults_google_2fprotobuf_2futil_2finternal_2ftestdata_2fanys_2eproto();
  SharedCtor();
  // @@protoc_insertion_point(constructor:google.protobuf.testing.Data)
}

void Data::InitAsDefaultInstance() {
  nested_data_ = const_cast< ::google::protobuf::testing::Data*>(
      ::google::protobuf::testing::Data::internal_default_instance());
  int_wrapper_ = const_cast< ::google::protobuf::Int32Value*>(
      ::google::protobuf::Int32Value::internal_default_instance());
  time_ = const_cast< ::google::protobuf::Timestamp*>(
      ::google::protobuf::Timestamp::internal_default_instance());
  struct_data_ = const_cast< ::google::protobuf::Struct*>(
      ::google::protobuf::Struct::internal_default_instance());
}

Data::Data(const Data& from)
  : ::google::protobuf::Message(),
    _internal_metadata_(NULL) {
  SharedCtor();
  UnsafeMergeFrom(from);
  // @@protoc_insertion_point(copy_constructor:google.protobuf.testing.Data)
}

void Data::SharedCtor() {
  map_data_.SetAssignDescriptorCallback(
      protobuf_AssignDescriptorsOnce);
  map_data_.SetEntryDescriptor(
      &::google::protobuf::testing::Data_MapDataEntry_descriptor_);
  str_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  nested_data_ = NULL;
  int_wrapper_ = NULL;
  time_ = NULL;
  struct_data_ = NULL;
  attr_ = 0;
  _cached_size_ = 0;
}

Data::~Data() {
  // @@protoc_insertion_point(destructor:google.protobuf.testing.Data)
  SharedDtor();
}

void Data::SharedDtor() {
  str_.DestroyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  if (this != &Data_default_instance_.get()) {
    delete nested_data_;
    delete int_wrapper_;
    delete time_;
    delete struct_data_;
  }
}

void Data::SetCachedSize(int size) const {
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
}
const ::google::protobuf::Descriptor* Data::descriptor() {
  protobuf_AssignDescriptorsOnce();
  return Data_descriptor_;
}

const Data& Data::default_instance() {
  protobuf_InitDefaults_google_2fprotobuf_2futil_2finternal_2ftestdata_2fanys_2eproto();
  return *internal_default_instance();
}

::google::protobuf::internal::ExplicitlyConstructed<Data> Data_default_instance_;

Data* Data::New(::google::protobuf::Arena* arena) const {
  Data* n = new Data;
  if (arena != NULL) {
    arena->Own(n);
  }
  return n;
}

void Data::Clear() {
// @@protoc_insertion_point(message_clear_start:google.protobuf.testing.Data)
  attr_ = 0;
  str_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  if (GetArenaNoVirtual() == NULL && nested_data_ != NULL) delete nested_data_;
  nested_data_ = NULL;
  if (GetArenaNoVirtual() == NULL && int_wrapper_ != NULL) delete int_wrapper_;
  int_wrapper_ = NULL;
  if (GetArenaNoVirtual() == NULL && time_ != NULL) delete time_;
  time_ = NULL;
  if (GetArenaNoVirtual() == NULL && struct_data_ != NULL) delete struct_data_;
  struct_data_ = NULL;
  msgs_.Clear();
  map_data_.Clear();
  repeated_data_.Clear();
}

bool Data::MergePartialFromCodedStream(
    ::google::protobuf::io::CodedInputStream* input) {
#define DO_(EXPRESSION) if (!GOOGLE_PREDICT_TRUE(EXPRESSION)) goto failure
  ::google::protobuf::uint32 tag;
  // @@protoc_insertion_point(parse_start:google.protobuf.testing.Data)
  for (;;) {
    ::std::pair< ::google::protobuf::uint32, bool> p = input->ReadTagWithCutoff(127);
    tag = p.first;
    if (!p.second) goto handle_unusual;
    switch (::google::protobuf::internal::WireFormatLite::GetTagFieldNumber(tag)) {
      // optional int32 attr = 1;
      case 1: {
        if (tag == 8) {

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int32, ::google::protobuf::internal::WireFormatLite::TYPE_INT32>(
                 input, &attr_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(18)) goto parse_str;
        break;
      }

      // optional string str = 2;
      case 2: {
        if (tag == 18) {
         parse_str:
          DO_(::google::protobuf::internal::WireFormatLite::ReadString(
                input, this->mutable_str()));
          DO_(::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
            this->str().data(), this->str().length(),
            ::google::protobuf::internal::WireFormatLite::PARSE,
            "google.protobuf.testing.Data.str"));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(26)) goto parse_msgs;
        break;
      }

      // repeated string msgs = 3;
      case 3: {
        if (tag == 26) {
         parse_msgs:
          DO_(::google::protobuf::internal::WireFormatLite::ReadString(
                input, this->add_msgs()));
          DO_(::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
            this->msgs(this->msgs_size() - 1).data(),
            this->msgs(this->msgs_size() - 1).length(),
            ::google::protobuf::internal::WireFormatLite::PARSE,
            "google.protobuf.testing.Data.msgs"));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(26)) goto parse_msgs;
        if (input->ExpectTag(34)) goto parse_nested_data;
        break;
      }

      // optional .google.protobuf.testing.Data nested_data = 4;
      case 4: {
        if (tag == 34) {
         parse_nested_data:
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessageNoVirtual(
               input, mutable_nested_data()));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(42)) goto parse_int_wrapper;
        break;
      }

      // optional .google.protobuf.Int32Value int_wrapper = 5;
      case 5: {
        if (tag == 42) {
         parse_int_wrapper:
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessageNoVirtual(
               input, mutable_int_wrapper()));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(50)) goto parse_time;
        break;
      }

      // optional .google.protobuf.Timestamp time = 6;
      case 6: {
        if (tag == 50) {
         parse_time:
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessageNoVirtual(
               input, mutable_time()));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(58)) goto parse_map_data;
        break;
      }

      // map<string, string> map_data = 7;
      case 7: {
        if (tag == 58) {
         parse_map_data:
          DO_(input->IncrementRecursionDepth());
         parse_loop_map_data:
          Data_MapDataEntry::Parser< ::google::protobuf::internal::MapField<
              ::std::string, ::std::string,
              ::google::protobuf::internal::WireFormatLite::TYPE_STRING,
              ::google::protobuf::internal::WireFormatLite::TYPE_STRING,
              0 >,
            ::google::protobuf::Map< ::std::string, ::std::string > > parser(&map_data_);
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessageNoVirtual(
              input, &parser));
          DO_(::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
            parser.key().data(), parser.key().length(),
            ::google::protobuf::internal::WireFormatLite::PARSE,
            "google.protobuf.testing.Data.MapDataEntry.key"));
          DO_(::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
            parser.value().data(), parser.value().length(),
            ::google::protobuf::internal::WireFormatLite::PARSE,
            "google.protobuf.testing.Data.MapDataEntry.value"));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(58)) goto parse_loop_map_data;
        input->UnsafeDecrementRecursionDepth();
        if (input->ExpectTag(66)) goto parse_struct_data;
        break;
      }

      // optional .google.protobuf.Struct struct_data = 8;
      case 8: {
        if (tag == 66) {
         parse_struct_data:
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessageNoVirtual(
               input, mutable_struct_data()));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(74)) goto parse_repeated_data;
        break;
      }

      // repeated .google.protobuf.testing.Data repeated_data = 9;
      case 9: {
        if (tag == 74) {
         parse_repeated_data:
          DO_(input->IncrementRecursionDepth());
         parse_loop_repeated_data:
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessageNoVirtualNoRecursionDepth(
                input, add_repeated_data()));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(74)) goto parse_loop_repeated_data;
        input->UnsafeDecrementRecursionDepth();
        if (input->ExpectAtEnd()) goto success;
        break;
      }

      default: {
      handle_unusual:
        if (tag == 0 ||
            ::google::protobuf::internal::WireFormatLite::GetTagWireType(tag) ==
            ::google::protobuf::internal::WireFormatLite::WIRETYPE_END_GROUP) {
          goto success;
        }
        DO_(::google::protobuf::internal::WireFormatLite::SkipField(input, tag));
        break;
      }
    }
  }
success:
  // @@protoc_insertion_point(parse_success:google.protobuf.testing.Data)
  return true;
failure:
  // @@protoc_insertion_point(parse_failure:google.protobuf.testing.Data)
  return false;
#undef DO_
}

void Data::SerializeWithCachedSizes(
    ::google::protobuf::io::CodedOutputStream* output) const {
  // @@protoc_insertion_point(serialize_start:google.protobuf.testing.Data)
  // optional int32 attr = 1;
  if (this->attr() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt32(1, this->attr(), output);
  }

  // optional string str = 2;
  if (this->str().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->str().data(), this->str().length(),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "google.protobuf.testing.Data.str");
    ::google::protobuf::internal::WireFormatLite::WriteStringMaybeAliased(
      2, this->str(), output);
  }

  // repeated string msgs = 3;
  for (int i = 0; i < this->msgs_size(); i++) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->msgs(i).data(), this->msgs(i).length(),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "google.protobuf.testing.Data.msgs");
    ::google::protobuf::internal::WireFormatLite::WriteString(
      3, this->msgs(i), output);
  }

  // optional .google.protobuf.testing.Data nested_data = 4;
  if (this->has_nested_data()) {
    ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
      4, *this->nested_data_, output);
  }

  // optional .google.protobuf.Int32Value int_wrapper = 5;
  if (this->has_int_wrapper()) {
    ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
      5, *this->int_wrapper_, output);
  }

  // optional .google.protobuf.Timestamp time = 6;
  if (this->has_time()) {
    ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
      6, *this->time_, output);
  }

  // map<string, string> map_data = 7;
  if (!this->map_data().empty()) {
    typedef ::google::protobuf::Map< ::std::string, ::std::string >::const_pointer
        ConstPtr;
    typedef ConstPtr SortItem;
    typedef ::google::protobuf::internal::CompareByDerefFirst<SortItem> Less;
    struct Utf8Check {
      static void Check(ConstPtr p) {
        ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
          p->first.data(), p->first.length(),
          ::google::protobuf::internal::WireFormatLite::SERIALIZE,
          "google.protobuf.testing.Data.MapDataEntry.key");
        ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
          p->second.data(), p->second.length(),
          ::google::protobuf::internal::WireFormatLite::SERIALIZE,
          "google.protobuf.testing.Data.MapDataEntry.value");
      }
    };

    if (output->IsSerializationDeterminstic() &&
        this->map_data().size() > 1) {
      ::google::protobuf::scoped_array<SortItem> items(
          new SortItem[this->map_data().size()]);
      typedef ::google::protobuf::Map< ::std::string, ::std::string >::size_type size_type;
      size_type n = 0;
      for (::google::protobuf::Map< ::std::string, ::std::string >::const_iterator
          it = this->map_data().begin();
          it != this->map_data().end(); ++it, ++n) {
        items[n] = SortItem(&*it);
      }
      ::std::sort(&items[0], &items[n], Less());
      ::google::protobuf::scoped_ptr<Data_MapDataEntry> entry;
      for (size_type i = 0; i < n; i++) {
        entry.reset(map_data_.NewEntryWrapper(
            items[i]->first, items[i]->second));
        ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
            7, *entry, output);
        Utf8Check::Check(items[i]);
      }
    } else {
      ::google::protobuf::scoped_ptr<Data_MapDataEntry> entry;
      for (::google::protobuf::Map< ::std::string, ::std::string >::const_iterator
          it = this->map_data().begin();
          it != this->map_data().end(); ++it) {
        entry.reset(map_data_.NewEntryWrapper(
            it->first, it->second));
        ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
            7, *entry, output);
        Utf8Check::Check(&*it);
      }
    }
  }

  // optional .google.protobuf.Struct struct_data = 8;
  if (this->has_struct_data()) {
    ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
      8, *this->struct_data_, output);
  }

  // repeated .google.protobuf.testing.Data repeated_data = 9;
  for (unsigned int i = 0, n = this->repeated_data_size(); i < n; i++) {
    ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
      9, this->repeated_data(i), output);
  }

  // @@protoc_insertion_point(serialize_end:google.protobuf.testing.Data)
}

::google::protobuf::uint8* Data::InternalSerializeWithCachedSizesToArray(
    bool deterministic, ::google::protobuf::uint8* target) const {
  (void)deterministic; // Unused
  // @@protoc_insertion_point(serialize_to_array_start:google.protobuf.testing.Data)
  // optional int32 attr = 1;
  if (this->attr() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt32ToArray(1, this->attr(), target);
  }

  // optional string str = 2;
  if (this->str().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->str().data(), this->str().length(),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "google.protobuf.testing.Data.str");
    target =
      ::google::protobuf::internal::WireFormatLite::WriteStringToArray(
        2, this->str(), target);
  }

  // repeated string msgs = 3;
  for (int i = 0; i < this->msgs_size(); i++) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->msgs(i).data(), this->msgs(i).length(),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "google.protobuf.testing.Data.msgs");
    target = ::google::protobuf::internal::WireFormatLite::
      WriteStringToArray(3, this->msgs(i), target);
  }

  // optional .google.protobuf.testing.Data nested_data = 4;
  if (this->has_nested_data()) {
    target = ::google::protobuf::internal::WireFormatLite::
      InternalWriteMessageNoVirtualToArray(
        4, *this->nested_data_, false, target);
  }

  // optional .google.protobuf.Int32Value int_wrapper = 5;
  if (this->has_int_wrapper()) {
    target = ::google::protobuf::internal::WireFormatLite::
      InternalWriteMessageNoVirtualToArray(
        5, *this->int_wrapper_, false, target);
  }

  // optional .google.protobuf.Timestamp time = 6;
  if (this->has_time()) {
    target = ::google::protobuf::internal::WireFormatLite::
      InternalWriteMessageNoVirtualToArray(
        6, *this->time_, false, target);
  }

  // map<string, string> map_data = 7;
  if (!this->map_data().empty()) {
    typedef ::google::protobuf::Map< ::std::string, ::std::string >::const_pointer
        ConstPtr;
    typedef ConstPtr SortItem;
    typedef ::google::protobuf::internal::CompareByDerefFirst<SortItem> Less;
    struct Utf8Check {
      static void Check(ConstPtr p) {
        ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
          p->first.data(), p->first.length(),
          ::google::protobuf::internal::WireFormatLite::SERIALIZE,
          "google.protobuf.testing.Data.MapDataEntry.key");
        ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
          p->second.data(), p->second.length(),
          ::google::protobuf::internal::WireFormatLite::SERIALIZE,
          "google.protobuf.testing.Data.MapDataEntry.value");
      }
    };

    if (deterministic &&
        this->map_data().size() > 1) {
      ::google::protobuf::scoped_array<SortItem> items(
          new SortItem[this->map_data().size()]);
      typedef ::google::protobuf::Map< ::std::string, ::std::string >::size_type size_type;
      size_type n = 0;
      for (::google::protobuf::Map< ::std::string, ::std::string >::const_iterator
          it = this->map_data().begin();
          it != this->map_data().end(); ++it, ++n) {
        items[n] = SortItem(&*it);
      }
      ::std::sort(&items[0], &items[n], Less());
      ::google::protobuf::scoped_ptr<Data_MapDataEntry> entry;
      for (size_type i = 0; i < n; i++) {
        entry.reset(map_data_.NewEntryWrapper(
            items[i]->first, items[i]->second));
        target = ::google::protobuf::internal::WireFormatLite::
                   InternalWriteMessageNoVirtualToArray(
                       7, *entry, deterministic, target);
;
        Utf8Check::Check(items[i]);
      }
    } else {
      ::google::protobuf::scoped_ptr<Data_MapDataEntry> entry;
      for (::google::protobuf::Map< ::std::string, ::std::string >::const_iterator
          it = this->map_data().begin();
          it != this->map_data().end(); ++it) {
        entry.reset(map_data_.NewEntryWrapper(
            it->first, it->second));
        target = ::google::protobuf::internal::WireFormatLite::
                   InternalWriteMessageNoVirtualToArray(
                       7, *entry, deterministic, target);
;
        Utf8Check::Check(&*it);
      }
    }
  }

  // optional .google.protobuf.Struct struct_data = 8;
  if (this->has_struct_data()) {
    target = ::google::protobuf::internal::WireFormatLite::
      InternalWriteMessageNoVirtualToArray(
        8, *this->struct_data_, false, target);
  }

  // repeated .google.protobuf.testing.Data repeated_data = 9;
  for (unsigned int i = 0, n = this->repeated_data_size(); i < n; i++) {
    target = ::google::protobuf::internal::WireFormatLite::
      InternalWriteMessageNoVirtualToArray(
        9, this->repeated_data(i), false, target);
  }

  // @@protoc_insertion_point(serialize_to_array_end:google.protobuf.testing.Data)
  return target;
}

size_t Data::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:google.protobuf.testing.Data)
  size_t total_size = 0;

  // optional int32 attr = 1;
  if (this->attr() != 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::Int32Size(
        this->attr());
  }

  // optional string str = 2;
  if (this->str().size() > 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::StringSize(
        this->str());
  }

  // optional .google.protobuf.testing.Data nested_data = 4;
  if (this->has_nested_data()) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::MessageSizeNoVirtual(
        *this->nested_data_);
  }

  // optional .google.protobuf.Int32Value int_wrapper = 5;
  if (this->has_int_wrapper()) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::MessageSizeNoVirtual(
        *this->int_wrapper_);
  }

  // optional .google.protobuf.Timestamp time = 6;
  if (this->has_time()) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::MessageSizeNoVirtual(
        *this->time_);
  }

  // optional .google.protobuf.Struct struct_data = 8;
  if (this->has_struct_data()) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::MessageSizeNoVirtual(
        *this->struct_data_);
  }

  // repeated string msgs = 3;
  total_size += 1 *
      ::google::protobuf::internal::FromIntSize(this->msgs_size());
  for (int i = 0; i < this->msgs_size(); i++) {
    total_size += ::google::protobuf::internal::WireFormatLite::StringSize(
      this->msgs(i));
  }

  // map<string, string> map_data = 7;
  total_size += 1 *
      ::google::protobuf::internal::FromIntSize(this->map_data_size());
  {
    ::google::protobuf::scoped_ptr<Data_MapDataEntry> entry;
    for (::google::protobuf::Map< ::std::string, ::std::string >::const_iterator
        it = this->map_data().begin();
        it != this->map_data().end(); ++it) {
      entry.reset(map_data_.NewEntryWrapper(it->first, it->second));
      total_size += ::google::protobuf::internal::WireFormatLite::
          MessageSizeNoVirtual(*entry);
    }
  }

  // repeated .google.protobuf.testing.Data repeated_data = 9;
  {
    unsigned int count = this->repeated_data_size();
    total_size += 1UL * count;
    for (unsigned int i = 0; i < count; i++) {
      total_size +=
        ::google::protobuf::internal::WireFormatLite::MessageSizeNoVirtual(
          this->repeated_data(i));
    }
  }

  int cached_size = ::google::protobuf::internal::ToCachedSize(total_size);
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = cached_size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
  return total_size;
}

void Data::MergeFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_merge_from_start:google.protobuf.testing.Data)
  if (GOOGLE_PREDICT_FALSE(&from == this)) MergeFromFail(__LINE__);
  const Data* source =
      ::google::protobuf::internal::DynamicCastToGenerated<const Data>(
          &from);
  if (source == NULL) {
  // @@protoc_insertion_point(generalized_merge_from_cast_fail:google.protobuf.testing.Data)
    ::google::protobuf::internal::ReflectionOps::Merge(from, this);
  } else {
  // @@protoc_insertion_point(generalized_merge_from_cast_success:google.protobuf.testing.Data)
    UnsafeMergeFrom(*source);
  }
}

void Data::MergeFrom(const Data& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:google.protobuf.testing.Data)
  if (GOOGLE_PREDICT_TRUE(&from != this)) {
    UnsafeMergeFrom(from);
  } else {
    MergeFromFail(__LINE__);
  }
}

void Data::UnsafeMergeFrom(const Data& from) {
  GOOGLE_DCHECK(&from != this);
  msgs_.UnsafeMergeFrom(from.msgs_);
  map_data_.MergeFrom(from.map_data_);
  repeated_data_.MergeFrom(from.repeated_data_);
  if (from.attr() != 0) {
    set_attr(from.attr());
  }
  if (from.str().size() > 0) {

    str_.AssignWithDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), from.str_);
  }
  if (from.has_nested_data()) {
    mutable_nested_data()->::google::protobuf::testing::Data::MergeFrom(from.nested_data());
  }
  if (from.has_int_wrapper()) {
    mutable_int_wrapper()->::google::protobuf::Int32Value::MergeFrom(from.int_wrapper());
  }
  if (from.has_time()) {
    mutable_time()->::google::protobuf::Timestamp::MergeFrom(from.time());
  }
  if (from.has_struct_data()) {
    mutable_struct_data()->::google::protobuf::Struct::MergeFrom(from.struct_data());
  }
}

void Data::CopyFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_copy_from_start:google.protobuf.testing.Data)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

void Data::CopyFrom(const Data& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:google.protobuf.testing.Data)
  if (&from == this) return;
  Clear();
  UnsafeMergeFrom(from);
}

bool Data::IsInitialized() const {

  return true;
}

void Data::Swap(Data* other) {
  if (other == this) return;
  InternalSwap(other);
}
void Data::InternalSwap(Data* other) {
  std::swap(attr_, other->attr_);
  str_.Swap(&other->str_);
  msgs_.UnsafeArenaSwap(&other->msgs_);
  std::swap(nested_data_, other->nested_data_);
  std::swap(int_wrapper_, other->int_wrapper_);
  std::swap(time_, other->time_);
  map_data_.Swap(&other->map_data_);
  std::swap(struct_data_, other->struct_data_);
  repeated_data_.UnsafeArenaSwap(&other->repeated_data_);
  _internal_metadata_.Swap(&other->_internal_metadata_);
  std::swap(_cached_size_, other->_cached_size_);
}

::google::protobuf::Metadata Data::GetMetadata() const {
  protobuf_AssignDescriptorsOnce();
  ::google::protobuf::Metadata metadata;
  metadata.descriptor = Data_descriptor_;
  metadata.reflection = Data_reflection_;
  return metadata;
}

#if PROTOBUF_INLINE_NOT_IN_HEADERS
// Data

// optional int32 attr = 1;
void Data::clear_attr() {
  attr_ = 0;
}
::google::protobuf::int32 Data::attr() const {
  // @@protoc_insertion_point(field_get:google.protobuf.testing.Data.attr)
  return attr_;
}
void Data::set_attr(::google::protobuf::int32 value) {
  
  attr_ = value;
  // @@protoc_insertion_point(field_set:google.protobuf.testing.Data.attr)
}

// optional string str = 2;
void Data::clear_str() {
  str_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
const ::std::string& Data::str() const {
  // @@protoc_insertion_point(field_get:google.protobuf.testing.Data.str)
  return str_.GetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
void Data::set_str(const ::std::string& value) {
  
  str_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:google.protobuf.testing.Data.str)
}
void Data::set_str(const char* value) {
  
  str_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:google.protobuf.testing.Data.str)
}
void Data::set_str(const char* value, size_t size) {
  
  str_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:google.protobuf.testing.Data.str)
}
::std::string* Data::mutable_str() {
  
  // @@protoc_insertion_point(field_mutable:google.protobuf.testing.Data.str)
  return str_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
::std::string* Data::release_str() {
  // @@protoc_insertion_point(field_release:google.protobuf.testing.Data.str)
  
  return str_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
void Data::set_allocated_str(::std::string* str) {
  if (str != NULL) {
    
  } else {
    
  }
  str_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), str);
  // @@protoc_insertion_point(field_set_allocated:google.protobuf.testing.Data.str)
}

// repeated string msgs = 3;
int Data::msgs_size() const {
  return msgs_.size();
}
void Data::clear_msgs() {
  msgs_.Clear();
}
const ::std::string& Data::msgs(int index) const {
  // @@protoc_insertion_point(field_get:google.protobuf.testing.Data.msgs)
  return msgs_.Get(index);
}
::std::string* Data::mutable_msgs(int index) {
  // @@protoc_insertion_point(field_mutable:google.protobuf.testing.Data.msgs)
  return msgs_.Mutable(index);
}
void Data::set_msgs(int index, const ::std::string& value) {
  // @@protoc_insertion_point(field_set:google.protobuf.testing.Data.msgs)
  msgs_.Mutable(index)->assign(value);
}
void Data::set_msgs(int index, const char* value) {
  msgs_.Mutable(index)->assign(value);
  // @@protoc_insertion_point(field_set_char:google.protobuf.testing.Data.msgs)
}
void Data::set_msgs(int index, const char* value, size_t size) {
  msgs_.Mutable(index)->assign(
    reinterpret_cast<const char*>(value), size);
  // @@protoc_insertion_point(field_set_pointer:google.protobuf.testing.Data.msgs)
}
::std::string* Data::add_msgs() {
  // @@protoc_insertion_point(field_add_mutable:google.protobuf.testing.Data.msgs)
  return msgs_.Add();
}
void Data::add_msgs(const ::std::string& value) {
  msgs_.Add()->assign(value);
  // @@protoc_insertion_point(field_add:google.protobuf.testing.Data.msgs)
}
void Data::add_msgs(const char* value) {
  msgs_.Add()->assign(value);
  // @@protoc_insertion_point(field_add_char:google.protobuf.testing.Data.msgs)
}
void Data::add_msgs(const char* value, size_t size) {
  msgs_.Add()->assign(reinterpret_cast<const char*>(value), size);
  // @@protoc_insertion_point(field_add_pointer:google.protobuf.testing.Data.msgs)
}
const ::google::protobuf::RepeatedPtrField< ::std::string>&
Data::msgs() const {
  // @@protoc_insertion_point(field_list:google.protobuf.testing.Data.msgs)
  return msgs_;
}
::google::protobuf::RepeatedPtrField< ::std::string>*
Data::mutable_msgs() {
  // @@protoc_insertion_point(field_mutable_list:google.protobuf.testing.Data.msgs)
  return &msgs_;
}

// optional .google.protobuf.testing.Data nested_data = 4;
bool Data::has_nested_data() const {
  return this != internal_default_instance() && nested_data_ != NULL;
}
void Data::clear_nested_data() {
  if (GetArenaNoVirtual() == NULL && nested_data_ != NULL) delete nested_data_;
  nested_data_ = NULL;
}
const ::google::protobuf::testing::Data& Data::nested_data() const {
  // @@protoc_insertion_point(field_get:google.protobuf.testing.Data.nested_data)
  return nested_data_ != NULL ? *nested_data_
                         : *::google::protobuf::testing::Data::internal_default_instance();
}
::google::protobuf::testing::Data* Data::mutable_nested_data() {
  
  if (nested_data_ == NULL) {
    nested_data_ = new ::google::protobuf::testing::Data;
  }
  // @@protoc_insertion_point(field_mutable:google.protobuf.testing.Data.nested_data)
  return nested_data_;
}
::google::protobuf::testing::Data* Data::release_nested_data() {
  // @@protoc_insertion_point(field_release:google.protobuf.testing.Data.nested_data)
  
  ::google::protobuf::testing::Data* temp = nested_data_;
  nested_data_ = NULL;
  return temp;
}
void Data::set_allocated_nested_data(::google::protobuf::testing::Data* nested_data) {
  delete nested_data_;
  nested_data_ = nested_data;
  if (nested_data) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_set_allocated:google.protobuf.testing.Data.nested_data)
}

// optional .google.protobuf.Int32Value int_wrapper = 5;
bool Data::has_int_wrapper() const {
  return this != internal_default_instance() && int_wrapper_ != NULL;
}
void Data::clear_int_wrapper() {
  if (GetArenaNoVirtual() == NULL && int_wrapper_ != NULL) delete int_wrapper_;
  int_wrapper_ = NULL;
}
const ::google::protobuf::Int32Value& Data::int_wrapper() const {
  // @@protoc_insertion_point(field_get:google.protobuf.testing.Data.int_wrapper)
  return int_wrapper_ != NULL ? *int_wrapper_
                         : *::google::protobuf::Int32Value::internal_default_instance();
}
::google::protobuf::Int32Value* Data::mutable_int_wrapper() {
  
  if (int_wrapper_ == NULL) {
    int_wrapper_ = new ::google::protobuf::Int32Value;
  }
  // @@protoc_insertion_point(field_mutable:google.protobuf.testing.Data.int_wrapper)
  return int_wrapper_;
}
::google::protobuf::Int32Value* Data::release_int_wrapper() {
  // @@protoc_insertion_point(field_release:google.protobuf.testing.Data.int_wrapper)
  
  ::google::protobuf::Int32Value* temp = int_wrapper_;
  int_wrapper_ = NULL;
  return temp;
}
void Data::set_allocated_int_wrapper(::google::protobuf::Int32Value* int_wrapper) {
  delete int_wrapper_;
  if (int_wrapper != NULL && int_wrapper->GetArena() != NULL) {
    ::google::protobuf::Int32Value* new_int_wrapper = new ::google::protobuf::Int32Value;
    new_int_wrapper->CopyFrom(*int_wrapper);
    int_wrapper = new_int_wrapper;
  }
  int_wrapper_ = int_wrapper;
  if (int_wrapper) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_set_allocated:google.protobuf.testing.Data.int_wrapper)
}

// optional .google.protobuf.Timestamp time = 6;
bool Data::has_time() const {
  return this != internal_default_instance() && time_ != NULL;
}
void Data::clear_time() {
  if (GetArenaNoVirtual() == NULL && time_ != NULL) delete time_;
  time_ = NULL;
}
const ::google::protobuf::Timestamp& Data::time() const {
  // @@protoc_insertion_point(field_get:google.protobuf.testing.Data.time)
  return time_ != NULL ? *time_
                         : *::google::protobuf::Timestamp::internal_default_instance();
}
::google::protobuf::Timestamp* Data::mutable_time() {
  
  if (time_ == NULL) {
    time_ = new ::google::protobuf::Timestamp;
  }
  // @@protoc_insertion_point(field_mutable:google.protobuf.testing.Data.time)
  return time_;
}
::google::protobuf::Timestamp* Data::release_time() {
  // @@protoc_insertion_point(field_release:google.protobuf.testing.Data.time)
  
  ::google::protobuf::Timestamp* temp = time_;
  time_ = NULL;
  return temp;
}
void Data::set_allocated_time(::google::protobuf::Timestamp* time) {
  delete time_;
  if (time != NULL && time->GetArena() != NULL) {
    ::google::protobuf::Timestamp* new_time = new ::google::protobuf::Timestamp;
    new_time->CopyFrom(*time);
    time = new_time;
  }
  time_ = time;
  if (time) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_set_allocated:google.protobuf.testing.Data.time)
}

// map<string, string> map_data = 7;
int Data::map_data_size() const {
  return map_data_.size();
}
void Data::clear_map_data() {
  map_data_.Clear();
}
 const ::google::protobuf::Map< ::std::string, ::std::string >&
Data::map_data() const {
  // @@protoc_insertion_point(field_map:google.protobuf.testing.Data.map_data)
  return map_data_.GetMap();
}
 ::google::protobuf::Map< ::std::string, ::std::string >*
Data::mutable_map_data() {
  // @@protoc_insertion_point(field_mutable_map:google.protobuf.testing.Data.map_data)
  return map_data_.MutableMap();
}

// optional .google.protobuf.Struct struct_data = 8;
bool Data::has_struct_data() const {
  return this != internal_default_instance() && struct_data_ != NULL;
}
void Data::clear_struct_data() {
  if (GetArenaNoVirtual() == NULL && struct_data_ != NULL) delete struct_data_;
  struct_data_ = NULL;
}
const ::google::protobuf::Struct& Data::struct_data() const {
  // @@protoc_insertion_point(field_get:google.protobuf.testing.Data.struct_data)
  return struct_data_ != NULL ? *struct_data_
                         : *::google::protobuf::Struct::internal_default_instance();
}
::google::protobuf::Struct* Data::mutable_struct_data() {
  
  if (struct_data_ == NULL) {
    struct_data_ = new ::google::protobuf::Struct;
  }
  // @@protoc_insertion_point(field_mutable:google.protobuf.testing.Data.struct_data)
  return struct_data_;
}
::google::protobuf::Struct* Data::release_struct_data() {
  // @@protoc_insertion_point(field_release:google.protobuf.testing.Data.struct_data)
  
  ::google::protobuf::Struct* temp = struct_data_;
  struct_data_ = NULL;
  return temp;
}
void Data::set_allocated_struct_data(::google::protobuf::Struct* struct_data) {
  delete struct_data_;
  if (struct_data != NULL && struct_data->GetArena() != NULL) {
    ::google::protobuf::Struct* new_struct_data = new ::google::protobuf::Struct;
    new_struct_data->CopyFrom(*struct_data);
    struct_data = new_struct_data;
  }
  struct_data_ = struct_data;
  if (struct_data) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_set_allocated:google.protobuf.testing.Data.struct_data)
}

// repeated .google.protobuf.testing.Data repeated_data = 9;
int Data::repeated_data_size() const {
  return repeated_data_.size();
}
void Data::clear_repeated_data() {
  repeated_data_.Clear();
}
const ::google::protobuf::testing::Data& Data::repeated_data(int index) const {
  // @@protoc_insertion_point(field_get:google.protobuf.testing.Data.repeated_data)
  return repeated_data_.Get(index);
}
::google::protobuf::testing::Data* Data::mutable_repeated_data(int index) {
  // @@protoc_insertion_point(field_mutable:google.protobuf.testing.Data.repeated_data)
  return repeated_data_.Mutable(index);
}
::google::protobuf::testing::Data* Data::add_repeated_data() {
  // @@protoc_insertion_point(field_add:google.protobuf.testing.Data.repeated_data)
  return repeated_data_.Add();
}
::google::protobuf::RepeatedPtrField< ::google::protobuf::testing::Data >*
Data::mutable_repeated_data() {
  // @@protoc_insertion_point(field_mutable_list:google.protobuf.testing.Data.repeated_data)
  return &repeated_data_;
}
const ::google::protobuf::RepeatedPtrField< ::google::protobuf::testing::Data >&
Data::repeated_data() const {
  // @@protoc_insertion_point(field_list:google.protobuf.testing.Data.repeated_data)
  return repeated_data_;
}

inline const Data* Data::internal_default_instance() {
  return &Data_default_instance_.get();
}
#endif  // PROTOBUF_INLINE_NOT_IN_HEADERS

// ===================================================================

#if !defined(_MSC_VER) || _MSC_VER >= 1900
const int AnyIn::kSomethingFieldNumber;
const int AnyIn::kAnyFieldNumber;
#endif  // !defined(_MSC_VER) || _MSC_VER >= 1900

AnyIn::AnyIn()
  : ::google::protobuf::Message(), _internal_metadata_(NULL) {
  if (this != internal_default_instance()) protobuf_InitDefaults_google_2fprotobuf_2futil_2finternal_2ftestdata_2fanys_2eproto();
  SharedCtor();
  // @@protoc_insertion_point(constructor:google.protobuf.testing.AnyIn)
}

void AnyIn::InitAsDefaultInstance() {
  any_ = const_cast< ::google::protobuf::Any*>(
      ::google::protobuf::Any::internal_default_instance());
}

AnyIn::AnyIn(const AnyIn& from)
  : ::google::protobuf::Message(),
    _internal_metadata_(NULL) {
  SharedCtor();
  UnsafeMergeFrom(from);
  // @@protoc_insertion_point(copy_constructor:google.protobuf.testing.AnyIn)
}

void AnyIn::SharedCtor() {
  something_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  any_ = NULL;
  _cached_size_ = 0;
}

AnyIn::~AnyIn() {
  // @@protoc_insertion_point(destructor:google.protobuf.testing.AnyIn)
  SharedDtor();
}

void AnyIn::SharedDtor() {
  something_.DestroyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  if (this != &AnyIn_default_instance_.get()) {
    delete any_;
  }
}

void AnyIn::SetCachedSize(int size) const {
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
}
const ::google::protobuf::Descriptor* AnyIn::descriptor() {
  protobuf_AssignDescriptorsOnce();
  return AnyIn_descriptor_;
}

const AnyIn& AnyIn::default_instance() {
  protobuf_InitDefaults_google_2fprotobuf_2futil_2finternal_2ftestdata_2fanys_2eproto();
  return *internal_default_instance();
}

::google::protobuf::internal::ExplicitlyConstructed<AnyIn> AnyIn_default_instance_;

AnyIn* AnyIn::New(::google::protobuf::Arena* arena) const {
  AnyIn* n = new AnyIn;
  if (arena != NULL) {
    arena->Own(n);
  }
  return n;
}

void AnyIn::Clear() {
// @@protoc_insertion_point(message_clear_start:google.protobuf.testing.AnyIn)
  something_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  if (GetArenaNoVirtual() == NULL && any_ != NULL) delete any_;
  any_ = NULL;
}

bool AnyIn::MergePartialFromCodedStream(
    ::google::protobuf::io::CodedInputStream* input) {
#define DO_(EXPRESSION) if (!GOOGLE_PREDICT_TRUE(EXPRESSION)) goto failure
  ::google::protobuf::uint32 tag;
  // @@protoc_insertion_point(parse_start:google.protobuf.testing.AnyIn)
  for (;;) {
    ::std::pair< ::google::protobuf::uint32, bool> p = input->ReadTagWithCutoff(127);
    tag = p.first;
    if (!p.second) goto handle_unusual;
    switch (::google::protobuf::internal::WireFormatLite::GetTagFieldNumber(tag)) {
      // optional string something = 1;
      case 1: {
        if (tag == 10) {
          DO_(::google::protobuf::internal::WireFormatLite::ReadString(
                input, this->mutable_something()));
          DO_(::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
            this->something().data(), this->something().length(),
            ::google::protobuf::internal::WireFormatLite::PARSE,
            "google.protobuf.testing.AnyIn.something"));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(18)) goto parse_any;
        break;
      }

      // optional .google.protobuf.Any any = 2;
      case 2: {
        if (tag == 18) {
         parse_any:
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessageNoVirtual(
               input, mutable_any()));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectAtEnd()) goto success;
        break;
      }

      default: {
      handle_unusual:
        if (tag == 0 ||
            ::google::protobuf::internal::WireFormatLite::GetTagWireType(tag) ==
            ::google::protobuf::internal::WireFormatLite::WIRETYPE_END_GROUP) {
          goto success;
        }
        DO_(::google::protobuf::internal::WireFormatLite::SkipField(input, tag));
        break;
      }
    }
  }
success:
  // @@protoc_insertion_point(parse_success:google.protobuf.testing.AnyIn)
  return true;
failure:
  // @@protoc_insertion_point(parse_failure:google.protobuf.testing.AnyIn)
  return false;
#undef DO_
}

void AnyIn::SerializeWithCachedSizes(
    ::google::protobuf::io::CodedOutputStream* output) const {
  // @@protoc_insertion_point(serialize_start:google.protobuf.testing.AnyIn)
  // optional string something = 1;
  if (this->something().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->something().data(), this->something().length(),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "google.protobuf.testing.AnyIn.something");
    ::google::protobuf::internal::WireFormatLite::WriteStringMaybeAliased(
      1, this->something(), output);
  }

  // optional .google.protobuf.Any any = 2;
  if (this->has_any()) {
    ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
      2, *this->any_, output);
  }

  // @@protoc_insertion_point(serialize_end:google.protobuf.testing.AnyIn)
}

::google::protobuf::uint8* AnyIn::InternalSerializeWithCachedSizesToArray(
    bool deterministic, ::google::protobuf::uint8* target) const {
  (void)deterministic; // Unused
  // @@protoc_insertion_point(serialize_to_array_start:google.protobuf.testing.AnyIn)
  // optional string something = 1;
  if (this->something().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->something().data(), this->something().length(),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "google.protobuf.testing.AnyIn.something");
    target =
      ::google::protobuf::internal::WireFormatLite::WriteStringToArray(
        1, this->something(), target);
  }

  // optional .google.protobuf.Any any = 2;
  if (this->has_any()) {
    target = ::google::protobuf::internal::WireFormatLite::
      InternalWriteMessageNoVirtualToArray(
        2, *this->any_, false, target);
  }

  // @@protoc_insertion_point(serialize_to_array_end:google.protobuf.testing.AnyIn)
  return target;
}

size_t AnyIn::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:google.protobuf.testing.AnyIn)
  size_t total_size = 0;

  // optional string something = 1;
  if (this->something().size() > 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::StringSize(
        this->something());
  }

  // optional .google.protobuf.Any any = 2;
  if (this->has_any()) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::MessageSizeNoVirtual(
        *this->any_);
  }

  int cached_size = ::google::protobuf::internal::ToCachedSize(total_size);
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = cached_size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
  return total_size;
}

void AnyIn::MergeFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_merge_from_start:google.protobuf.testing.AnyIn)
  if (GOOGLE_PREDICT_FALSE(&from == this)) MergeFromFail(__LINE__);
  const AnyIn* source =
      ::google::protobuf::internal::DynamicCastToGenerated<const AnyIn>(
          &from);
  if (source == NULL) {
  // @@protoc_insertion_point(generalized_merge_from_cast_fail:google.protobuf.testing.AnyIn)
    ::google::protobuf::internal::ReflectionOps::Merge(from, this);
  } else {
  // @@protoc_insertion_point(generalized_merge_from_cast_success:google.protobuf.testing.AnyIn)
    UnsafeMergeFrom(*source);
  }
}

void AnyIn::MergeFrom(const AnyIn& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:google.protobuf.testing.AnyIn)
  if (GOOGLE_PREDICT_TRUE(&from != this)) {
    UnsafeMergeFrom(from);
  } else {
    MergeFromFail(__LINE__);
  }
}

void AnyIn::UnsafeMergeFrom(const AnyIn& from) {
  GOOGLE_DCHECK(&from != this);
  if (from.something().size() > 0) {

    something_.AssignWithDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), from.something_);
  }
  if (from.has_any()) {
    mutable_any()->::google::protobuf::Any::MergeFrom(from.any());
  }
}

void AnyIn::CopyFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_copy_from_start:google.protobuf.testing.AnyIn)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

void AnyIn::CopyFrom(const AnyIn& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:google.protobuf.testing.AnyIn)
  if (&from == this) return;
  Clear();
  UnsafeMergeFrom(from);
}

bool AnyIn::IsInitialized() const {

  return true;
}

void AnyIn::Swap(AnyIn* other) {
  if (other == this) return;
  InternalSwap(other);
}
void AnyIn::InternalSwap(AnyIn* other) {
  something_.Swap(&other->something_);
  std::swap(any_, other->any_);
  _internal_metadata_.Swap(&other->_internal_metadata_);
  std::swap(_cached_size_, other->_cached_size_);
}

::google::protobuf::Metadata AnyIn::GetMetadata() const {
  protobuf_AssignDescriptorsOnce();
  ::google::protobuf::Metadata metadata;
  metadata.descriptor = AnyIn_descriptor_;
  metadata.reflection = AnyIn_reflection_;
  return metadata;
}

#if PROTOBUF_INLINE_NOT_IN_HEADERS
// AnyIn

// optional string something = 1;
void AnyIn::clear_something() {
  something_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
const ::std::string& AnyIn::something() const {
  // @@protoc_insertion_point(field_get:google.protobuf.testing.AnyIn.something)
  return something_.GetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
void AnyIn::set_something(const ::std::string& value) {
  
  something_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:google.protobuf.testing.AnyIn.something)
}
void AnyIn::set_something(const char* value) {
  
  something_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:google.protobuf.testing.AnyIn.something)
}
void AnyIn::set_something(const char* value, size_t size) {
  
  something_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:google.protobuf.testing.AnyIn.something)
}
::std::string* AnyIn::mutable_something() {
  
  // @@protoc_insertion_point(field_mutable:google.protobuf.testing.AnyIn.something)
  return something_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
::std::string* AnyIn::release_something() {
  // @@protoc_insertion_point(field_release:google.protobuf.testing.AnyIn.something)
  
  return something_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
void AnyIn::set_allocated_something(::std::string* something) {
  if (something != NULL) {
    
  } else {
    
  }
  something_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), something);
  // @@protoc_insertion_point(field_set_allocated:google.protobuf.testing.AnyIn.something)
}

// optional .google.protobuf.Any any = 2;
bool AnyIn::has_any() const {
  return this != internal_default_instance() && any_ != NULL;
}
void AnyIn::clear_any() {
  if (GetArenaNoVirtual() == NULL && any_ != NULL) delete any_;
  any_ = NULL;
}
const ::google::protobuf::Any& AnyIn::any() const {
  // @@protoc_insertion_point(field_get:google.protobuf.testing.AnyIn.any)
  return any_ != NULL ? *any_
                         : *::google::protobuf::Any::internal_default_instance();
}
::google::protobuf::Any* AnyIn::mutable_any() {
  
  if (any_ == NULL) {
    any_ = new ::google::protobuf::Any;
  }
  // @@protoc_insertion_point(field_mutable:google.protobuf.testing.AnyIn.any)
  return any_;
}
::google::protobuf::Any* AnyIn::release_any() {
  // @@protoc_insertion_point(field_release:google.protobuf.testing.AnyIn.any)
  
  ::google::protobuf::Any* temp = any_;
  any_ = NULL;
  return temp;
}
void AnyIn::set_allocated_any(::google::protobuf::Any* any) {
  delete any_;
  any_ = any;
  if (any) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_set_allocated:google.protobuf.testing.AnyIn.any)
}

inline const AnyIn* AnyIn::internal_default_instance() {
  return &AnyIn_default_instance_.get();
}
#endif  // PROTOBUF_INLINE_NOT_IN_HEADERS

// ===================================================================

#if !defined(_MSC_VER) || _MSC_VER >= 1900
const int AnyOut::kAnyFieldNumber;
#endif  // !defined(_MSC_VER) || _MSC_VER >= 1900

AnyOut::AnyOut()
  : ::google::protobuf::Message(), _internal_metadata_(NULL) {
  if (this != internal_default_instance()) protobuf_InitDefaults_google_2fprotobuf_2futil_2finternal_2ftestdata_2fanys_2eproto();
  SharedCtor();
  // @@protoc_insertion_point(constructor:google.protobuf.testing.AnyOut)
}

void AnyOut::InitAsDefaultInstance() {
  any_ = const_cast< ::google::protobuf::Any*>(
      ::google::protobuf::Any::internal_default_instance());
}

AnyOut::AnyOut(const AnyOut& from)
  : ::google::protobuf::Message(),
    _internal_metadata_(NULL) {
  SharedCtor();
  UnsafeMergeFrom(from);
  // @@protoc_insertion_point(copy_constructor:google.protobuf.testing.AnyOut)
}

void AnyOut::SharedCtor() {
  any_ = NULL;
  _cached_size_ = 0;
}

AnyOut::~AnyOut() {
  // @@protoc_insertion_point(destructor:google.protobuf.testing.AnyOut)
  SharedDtor();
}

void AnyOut::SharedDtor() {
  if (this != &AnyOut_default_instance_.get()) {
    delete any_;
  }
}

void AnyOut::SetCachedSize(int size) const {
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
}
const ::google::protobuf::Descriptor* AnyOut::descriptor() {
  protobuf_AssignDescriptorsOnce();
  return AnyOut_descriptor_;
}

const AnyOut& AnyOut::default_instance() {
  protobuf_InitDefaults_google_2fprotobuf_2futil_2finternal_2ftestdata_2fanys_2eproto();
  return *internal_default_instance();
}

::google::protobuf::internal::ExplicitlyConstructed<AnyOut> AnyOut_default_instance_;

AnyOut* AnyOut::New(::google::protobuf::Arena* arena) const {
  AnyOut* n = new AnyOut;
  if (arena != NULL) {
    arena->Own(n);
  }
  return n;
}

void AnyOut::Clear() {
// @@protoc_insertion_point(message_clear_start:google.protobuf.testing.AnyOut)
  if (GetArenaNoVirtual() == NULL && any_ != NULL) delete any_;
  any_ = NULL;
}

bool AnyOut::MergePartialFromCodedStream(
    ::google::protobuf::io::CodedInputStream* input) {
#define DO_(EXPRESSION) if (!GOOGLE_PREDICT_TRUE(EXPRESSION)) goto failure
  ::google::protobuf::uint32 tag;
  // @@protoc_insertion_point(parse_start:google.protobuf.testing.AnyOut)
  for (;;) {
    ::std::pair< ::google::protobuf::uint32, bool> p = input->ReadTagWithCutoff(127);
    tag = p.first;
    if (!p.second) goto handle_unusual;
    switch (::google::protobuf::internal::WireFormatLite::GetTagFieldNumber(tag)) {
      // optional .google.protobuf.Any any = 1;
      case 1: {
        if (tag == 10) {
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessageNoVirtual(
               input, mutable_any()));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectAtEnd()) goto success;
        break;
      }

      default: {
      handle_unusual:
        if (tag == 0 ||
            ::google::protobuf::internal::WireFormatLite::GetTagWireType(tag) ==
            ::google::protobuf::internal::WireFormatLite::WIRETYPE_END_GROUP) {
          goto success;
        }
        DO_(::google::protobuf::internal::WireFormatLite::SkipField(input, tag));
        break;
      }
    }
  }
success:
  // @@protoc_insertion_point(parse_success:google.protobuf.testing.AnyOut)
  return true;
failure:
  // @@protoc_insertion_point(parse_failure:google.protobuf.testing.AnyOut)
  return false;
#undef DO_
}

void AnyOut::SerializeWithCachedSizes(
    ::google::protobuf::io::CodedOutputStream* output) const {
  // @@protoc_insertion_point(serialize_start:google.protobuf.testing.AnyOut)
  // optional .google.protobuf.Any any = 1;
  if (this->has_any()) {
    ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
      1, *this->any_, output);
  }

  // @@protoc_insertion_point(serialize_end:google.protobuf.testing.AnyOut)
}

::google::protobuf::uint8* AnyOut::InternalSerializeWithCachedSizesToArray(
    bool deterministic, ::google::protobuf::uint8* target) const {
  (void)deterministic; // Unused
  // @@protoc_insertion_point(serialize_to_array_start:google.protobuf.testing.AnyOut)
  // optional .google.protobuf.Any any = 1;
  if (this->has_any()) {
    target = ::google::protobuf::internal::WireFormatLite::
      InternalWriteMessageNoVirtualToArray(
        1, *this->any_, false, target);
  }

  // @@protoc_insertion_point(serialize_to_array_end:google.protobuf.testing.AnyOut)
  return target;
}

size_t AnyOut::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:google.protobuf.testing.AnyOut)
  size_t total_size = 0;

  // optional .google.protobuf.Any any = 1;
  if (this->has_any()) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::MessageSizeNoVirtual(
        *this->any_);
  }

  int cached_size = ::google::protobuf::internal::ToCachedSize(total_size);
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = cached_size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
  return total_size;
}

void AnyOut::MergeFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_merge_from_start:google.protobuf.testing.AnyOut)
  if (GOOGLE_PREDICT_FALSE(&from == this)) MergeFromFail(__LINE__);
  const AnyOut* source =
      ::google::protobuf::internal::DynamicCastToGenerated<const AnyOut>(
          &from);
  if (source == NULL) {
  // @@protoc_insertion_point(generalized_merge_from_cast_fail:google.protobuf.testing.AnyOut)
    ::google::protobuf::internal::ReflectionOps::Merge(from, this);
  } else {
  // @@protoc_insertion_point(generalized_merge_from_cast_success:google.protobuf.testing.AnyOut)
    UnsafeMergeFrom(*source);
  }
}

void AnyOut::MergeFrom(const AnyOut& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:google.protobuf.testing.AnyOut)
  if (GOOGLE_PREDICT_TRUE(&from != this)) {
    UnsafeMergeFrom(from);
  } else {
    MergeFromFail(__LINE__);
  }
}

void AnyOut::UnsafeMergeFrom(const AnyOut& from) {
  GOOGLE_DCHECK(&from != this);
  if (from.has_any()) {
    mutable_any()->::google::protobuf::Any::MergeFrom(from.any());
  }
}

void AnyOut::CopyFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_copy_from_start:google.protobuf.testing.AnyOut)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

void AnyOut::CopyFrom(const AnyOut& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:google.protobuf.testing.AnyOut)
  if (&from == this) return;
  Clear();
  UnsafeMergeFrom(from);
}

bool AnyOut::IsInitialized() const {

  return true;
}

void AnyOut::Swap(AnyOut* other) {
  if (other == this) return;
  InternalSwap(other);
}
void AnyOut::InternalSwap(AnyOut* other) {
  std::swap(any_, other->any_);
  _internal_metadata_.Swap(&other->_internal_metadata_);
  std::swap(_cached_size_, other->_cached_size_);
}

::google::protobuf::Metadata AnyOut::GetMetadata() const {
  protobuf_AssignDescriptorsOnce();
  ::google::protobuf::Metadata metadata;
  metadata.descriptor = AnyOut_descriptor_;
  metadata.reflection = AnyOut_reflection_;
  return metadata;
}

#if PROTOBUF_INLINE_NOT_IN_HEADERS
// AnyOut

// optional .google.protobuf.Any any = 1;
bool AnyOut::has_any() const {
  return this != internal_default_instance() && any_ != NULL;
}
void AnyOut::clear_any() {
  if (GetArenaNoVirtual() == NULL && any_ != NULL) delete any_;
  any_ = NULL;
}
const ::google::protobuf::Any& AnyOut::any() const {
  // @@protoc_insertion_point(field_get:google.protobuf.testing.AnyOut.any)
  return any_ != NULL ? *any_
                         : *::google::protobuf::Any::internal_default_instance();
}
::google::protobuf::Any* AnyOut::mutable_any() {
  
  if (any_ == NULL) {
    any_ = new ::google::protobuf::Any;
  }
  // @@protoc_insertion_point(field_mutable:google.protobuf.testing.AnyOut.any)
  return any_;
}
::google::protobuf::Any* AnyOut::release_any() {
  // @@protoc_insertion_point(field_release:google.protobuf.testing.AnyOut.any)
  
  ::google::protobuf::Any* temp = any_;
  any_ = NULL;
  return temp;
}
void AnyOut::set_allocated_any(::google::protobuf::Any* any) {
  delete any_;
  any_ = any;
  if (any) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_set_allocated:google.protobuf.testing.AnyOut.any)
}

inline const AnyOut* AnyOut::internal_default_instance() {
  return &AnyOut_default_instance_.get();
}
#endif  // PROTOBUF_INLINE_NOT_IN_HEADERS

// ===================================================================

#if !defined(_MSC_VER) || _MSC_VER >= 1900
const int AnyM::kFooFieldNumber;
#endif  // !defined(_MSC_VER) || _MSC_VER >= 1900

AnyM::AnyM()
  : ::google::protobuf::Message(), _internal_metadata_(NULL) {
  if (this != internal_default_instance()) protobuf_InitDefaults_google_2fprotobuf_2futil_2finternal_2ftestdata_2fanys_2eproto();
  SharedCtor();
  // @@protoc_insertion_point(constructor:google.protobuf.testing.AnyM)
}

void AnyM::InitAsDefaultInstance() {
}

AnyM::AnyM(const AnyM& from)
  : ::google::protobuf::Message(),
    _internal_metadata_(NULL) {
  SharedCtor();
  UnsafeMergeFrom(from);
  // @@protoc_insertion_point(copy_constructor:google.protobuf.testing.AnyM)
}

void AnyM::SharedCtor() {
  foo_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  _cached_size_ = 0;
}

AnyM::~AnyM() {
  // @@protoc_insertion_point(destructor:google.protobuf.testing.AnyM)
  SharedDtor();
}

void AnyM::SharedDtor() {
  foo_.DestroyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}

void AnyM::SetCachedSize(int size) const {
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
}
const ::google::protobuf::Descriptor* AnyM::descriptor() {
  protobuf_AssignDescriptorsOnce();
  return AnyM_descriptor_;
}

const AnyM& AnyM::default_instance() {
  protobuf_InitDefaults_google_2fprotobuf_2futil_2finternal_2ftestdata_2fanys_2eproto();
  return *internal_default_instance();
}

::google::protobuf::internal::ExplicitlyConstructed<AnyM> AnyM_default_instance_;

AnyM* AnyM::New(::google::protobuf::Arena* arena) const {
  AnyM* n = new AnyM;
  if (arena != NULL) {
    arena->Own(n);
  }
  return n;
}

void AnyM::Clear() {
// @@protoc_insertion_point(message_clear_start:google.protobuf.testing.AnyM)
  foo_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}

bool AnyM::MergePartialFromCodedStream(
    ::google::protobuf::io::CodedInputStream* input) {
#define DO_(EXPRESSION) if (!GOOGLE_PREDICT_TRUE(EXPRESSION)) goto failure
  ::google::protobuf::uint32 tag;
  // @@protoc_insertion_point(parse_start:google.protobuf.testing.AnyM)
  for (;;) {
    ::std::pair< ::google::protobuf::uint32, bool> p = input->ReadTagWithCutoff(127);
    tag = p.first;
    if (!p.second) goto handle_unusual;
    switch (::google::protobuf::internal::WireFormatLite::GetTagFieldNumber(tag)) {
      // optional string foo = 1;
      case 1: {
        if (tag == 10) {
          DO_(::google::protobuf::internal::WireFormatLite::ReadString(
                input, this->mutable_foo()));
          DO_(::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
            this->foo().data(), this->foo().length(),
            ::google::protobuf::internal::WireFormatLite::PARSE,
            "google.protobuf.testing.AnyM.foo"));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectAtEnd()) goto success;
        break;
      }

      default: {
      handle_unusual:
        if (tag == 0 ||
            ::google::protobuf::internal::WireFormatLite::GetTagWireType(tag) ==
            ::google::protobuf::internal::WireFormatLite::WIRETYPE_END_GROUP) {
          goto success;
        }
        DO_(::google::protobuf::internal::WireFormatLite::SkipField(input, tag));
        break;
      }
    }
  }
success:
  // @@protoc_insertion_point(parse_success:google.protobuf.testing.AnyM)
  return true;
failure:
  // @@protoc_insertion_point(parse_failure:google.protobuf.testing.AnyM)
  return false;
#undef DO_
}

void AnyM::SerializeWithCachedSizes(
    ::google::protobuf::io::CodedOutputStream* output) const {
  // @@protoc_insertion_point(serialize_start:google.protobuf.testing.AnyM)
  // optional string foo = 1;
  if (this->foo().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->foo().data(), this->foo().length(),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "google.protobuf.testing.AnyM.foo");
    ::google::protobuf::internal::WireFormatLite::WriteStringMaybeAliased(
      1, this->foo(), output);
  }

  // @@protoc_insertion_point(serialize_end:google.protobuf.testing.AnyM)
}

::google::protobuf::uint8* AnyM::InternalSerializeWithCachedSizesToArray(
    bool deterministic, ::google::protobuf::uint8* target) const {
  (void)deterministic; // Unused
  // @@protoc_insertion_point(serialize_to_array_start:google.protobuf.testing.AnyM)
  // optional string foo = 1;
  if (this->foo().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->foo().data(), this->foo().length(),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "google.protobuf.testing.AnyM.foo");
    target =
      ::google::protobuf::internal::WireFormatLite::WriteStringToArray(
        1, this->foo(), target);
  }

  // @@protoc_insertion_point(serialize_to_array_end:google.protobuf.testing.AnyM)
  return target;
}

size_t AnyM::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:google.protobuf.testing.AnyM)
  size_t total_size = 0;

  // optional string foo = 1;
  if (this->foo().size() > 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::StringSize(
        this->foo());
  }

  int cached_size = ::google::protobuf::internal::ToCachedSize(total_size);
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = cached_size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
  return total_size;
}

void AnyM::MergeFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_merge_from_start:google.protobuf.testing.AnyM)
  if (GOOGLE_PREDICT_FALSE(&from == this)) MergeFromFail(__LINE__);
  const AnyM* source =
      ::google::protobuf::internal::DynamicCastToGenerated<const AnyM>(
          &from);
  if (source == NULL) {
  // @@protoc_insertion_point(generalized_merge_from_cast_fail:google.protobuf.testing.AnyM)
    ::google::protobuf::internal::ReflectionOps::Merge(from, this);
  } else {
  // @@protoc_insertion_point(generalized_merge_from_cast_success:google.protobuf.testing.AnyM)
    UnsafeMergeFrom(*source);
  }
}

void AnyM::MergeFrom(const AnyM& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:google.protobuf.testing.AnyM)
  if (GOOGLE_PREDICT_TRUE(&from != this)) {
    UnsafeMergeFrom(from);
  } else {
    MergeFromFail(__LINE__);
  }
}

void AnyM::UnsafeMergeFrom(const AnyM& from) {
  GOOGLE_DCHECK(&from != this);
  if (from.foo().size() > 0) {

    foo_.AssignWithDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), from.foo_);
  }
}

void AnyM::CopyFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_copy_from_start:google.protobuf.testing.AnyM)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

void AnyM::CopyFrom(const AnyM& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:google.protobuf.testing.AnyM)
  if (&from == this) return;
  Clear();
  UnsafeMergeFrom(from);
}

bool AnyM::IsInitialized() const {

  return true;
}

void AnyM::Swap(AnyM* other) {
  if (other == this) return;
  InternalSwap(other);
}
void AnyM::InternalSwap(AnyM* other) {
  foo_.Swap(&other->foo_);
  _internal_metadata_.Swap(&other->_internal_metadata_);
  std::swap(_cached_size_, other->_cached_size_);
}

::google::protobuf::Metadata AnyM::GetMetadata() const {
  protobuf_AssignDescriptorsOnce();
  ::google::protobuf::Metadata metadata;
  metadata.descriptor = AnyM_descriptor_;
  metadata.reflection = AnyM_reflection_;
  return metadata;
}

#if PROTOBUF_INLINE_NOT_IN_HEADERS
// AnyM

// optional string foo = 1;
void AnyM::clear_foo() {
  foo_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
const ::std::string& AnyM::foo() const {
  // @@protoc_insertion_point(field_get:google.protobuf.testing.AnyM.foo)
  return foo_.GetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
void AnyM::set_foo(const ::std::string& value) {
  
  foo_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:google.protobuf.testing.AnyM.foo)
}
void AnyM::set_foo(const char* value) {
  
  foo_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:google.protobuf.testing.AnyM.foo)
}
void AnyM::set_foo(const char* value, size_t size) {
  
  foo_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:google.protobuf.testing.AnyM.foo)
}
::std::string* AnyM::mutable_foo() {
  
  // @@protoc_insertion_point(field_mutable:google.protobuf.testing.AnyM.foo)
  return foo_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
::std::string* AnyM::release_foo() {
  // @@protoc_insertion_point(field_release:google.protobuf.testing.AnyM.foo)
  
  return foo_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
void AnyM::set_allocated_foo(::std::string* foo) {
  if (foo != NULL) {
    
  } else {
    
  }
  foo_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), foo);
  // @@protoc_insertion_point(field_set_allocated:google.protobuf.testing.AnyM.foo)
}

inline const AnyM* AnyM::internal_default_instance() {
  return &AnyM_default_instance_.get();
}
#endif  // PROTOBUF_INLINE_NOT_IN_HEADERS

// @@protoc_insertion_point(namespace_scope)

}  // namespace testing
}  // namespace protobuf
}  // namespace google

// @@protoc_insertion_point(global_scope)

// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: google/protobuf/util/internal/testdata/wrappers.proto

#define INTERNAL_SUPPRESS_PROTOBUF_FIELD_DEPRECATION
#include <google/protobuf/util/internal/testdata/wrappers.pb.h>

#include <algorithm>

#include <google/protobuf/stubs/common.h>
#include <google/protobuf/stubs/port.h>
#include <google/protobuf/stubs/once.h>
#include <google/protobuf/io/coded_stream.h>
#include <google/protobuf/wire_format_lite_inl.h>
#include <google/protobuf/descriptor.h>
#include <google/protobuf/generated_message_reflection.h>
#include <google/protobuf/reflection_ops.h>
#include <google/protobuf/wire_format.h>
// @@protoc_insertion_point(includes)

namespace google {
namespace protobuf {
namespace testing {

namespace {

const ::google::protobuf::Descriptor* WrappersTestCases_descriptor_ = NULL;
const ::google::protobuf::internal::GeneratedMessageReflection*
  WrappersTestCases_reflection_ = NULL;
const ::google::protobuf::Descriptor* DoubleWrapper_descriptor_ = NULL;
const ::google::protobuf::internal::GeneratedMessageReflection*
  DoubleWrapper_reflection_ = NULL;
const ::google::protobuf::Descriptor* FloatWrapper_descriptor_ = NULL;
const ::google::protobuf::internal::GeneratedMessageReflection*
  FloatWrapper_reflection_ = NULL;
const ::google::protobuf::Descriptor* Int64Wrapper_descriptor_ = NULL;
const ::google::protobuf::internal::GeneratedMessageReflection*
  Int64Wrapper_reflection_ = NULL;
const ::google::protobuf::Descriptor* UInt64Wrapper_descriptor_ = NULL;
const ::google::protobuf::internal::GeneratedMessageReflection*
  UInt64Wrapper_reflection_ = NULL;
const ::google::protobuf::Descriptor* Int32Wrapper_descriptor_ = NULL;
const ::google::protobuf::internal::GeneratedMessageReflection*
  Int32Wrapper_reflection_ = NULL;
const ::google::protobuf::Descriptor* UInt32Wrapper_descriptor_ = NULL;
const ::google::protobuf::internal::GeneratedMessageReflection*
  UInt32Wrapper_reflection_ = NULL;
const ::google::protobuf::Descriptor* BoolWrapper_descriptor_ = NULL;
const ::google::protobuf::internal::GeneratedMessageReflection*
  BoolWrapper_reflection_ = NULL;
const ::google::protobuf::Descriptor* StringWrapper_descriptor_ = NULL;
const ::google::protobuf::internal::GeneratedMessageReflection*
  StringWrapper_reflection_ = NULL;
const ::google::protobuf::Descriptor* BytesWrapper_descriptor_ = NULL;
const ::google::protobuf::internal::GeneratedMessageReflection*
  BytesWrapper_reflection_ = NULL;

}  // namespace


void protobuf_AssignDesc_google_2fprotobuf_2futil_2finternal_2ftestdata_2fwrappers_2eproto() GOOGLE_ATTRIBUTE_COLD;
void protobuf_AssignDesc_google_2fprotobuf_2futil_2finternal_2ftestdata_2fwrappers_2eproto() {
  protobuf_AddDesc_google_2fprotobuf_2futil_2finternal_2ftestdata_2fwrappers_2eproto();
  const ::google::protobuf::FileDescriptor* file =
    ::google::protobuf::DescriptorPool::generated_pool()->FindFileByName(
      "google/protobuf/util/internal/testdata/wrappers.proto");
  GOOGLE_CHECK(file != NULL);
  WrappersTestCases_descriptor_ = file->message_type(0);
  static const int WrappersTestCases_offsets_[18] = {
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(WrappersTestCases, double_wrapper_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(WrappersTestCases, float_wrapper_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(WrappersTestCases, int64_wrapper_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(WrappersTestCases, uint64_wrapper_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(WrappersTestCases, int32_wrapper_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(WrappersTestCases, uint32_wrapper_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(WrappersTestCases, bool_wrapper_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(WrappersTestCases, string_wrapper_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(WrappersTestCases, bytes_wrapper_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(WrappersTestCases, double_wrapper_default_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(WrappersTestCases, float_wrapper_default_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(WrappersTestCases, int64_wrapper_default_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(WrappersTestCases, uint64_wrapper_default_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(WrappersTestCases, int32_wrapper_default_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(WrappersTestCases, uint32_wrapper_default_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(WrappersTestCases, bool_wrapper_default_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(WrappersTestCases, string_wrapper_default_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(WrappersTestCases, bytes_wrapper_default_),
  };
  WrappersTestCases_reflection_ =
    ::google::protobuf::internal::GeneratedMessageReflection::NewGeneratedMessageReflection(
      WrappersTestCases_descriptor_,
      WrappersTestCases::internal_default_instance(),
      WrappersTestCases_offsets_,
      -1,
      -1,
      -1,
      sizeof(WrappersTestCases),
      GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(WrappersTestCases, _internal_metadata_));
  DoubleWrapper_descriptor_ = file->message_type(1);
  static const int DoubleWrapper_offsets_[1] = {
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(DoubleWrapper, double__),
  };
  DoubleWrapper_reflection_ =
    ::google::protobuf::internal::GeneratedMessageReflection::NewGeneratedMessageReflection(
      DoubleWrapper_descriptor_,
      DoubleWrapper::internal_default_instance(),
      DoubleWrapper_offsets_,
      -1,
      -1,
      -1,
      sizeof(DoubleWrapper),
      GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(DoubleWrapper, _internal_metadata_));
  FloatWrapper_descriptor_ = file->message_type(2);
  static const int FloatWrapper_offsets_[1] = {
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(FloatWrapper, float__),
  };
  FloatWrapper_reflection_ =
    ::google::protobuf::internal::GeneratedMessageReflection::NewGeneratedMessageReflection(
      FloatWrapper_descriptor_,
      FloatWrapper::internal_default_instance(),
      FloatWrapper_offsets_,
      -1,
      -1,
      -1,
      sizeof(FloatWrapper),
      GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(FloatWrapper, _internal_metadata_));
  Int64Wrapper_descriptor_ = file->message_type(3);
  static const int Int64Wrapper_offsets_[1] = {
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(Int64Wrapper, int64_),
  };
  Int64Wrapper_reflection_ =
    ::google::protobuf::internal::GeneratedMessageReflection::NewGeneratedMessageReflection(
      Int64Wrapper_descriptor_,
      Int64Wrapper::internal_default_instance(),
      Int64Wrapper_offsets_,
      -1,
      -1,
      -1,
      sizeof(Int64Wrapper),
      GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(Int64Wrapper, _internal_metadata_));
  UInt64Wrapper_descriptor_ = file->message_type(4);
  static const int UInt64Wrapper_offsets_[1] = {
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(UInt64Wrapper, uint64_),
  };
  UInt64Wrapper_reflection_ =
    ::google::protobuf::internal::GeneratedMessageReflection::NewGeneratedMessageReflection(
      UInt64Wrapper_descriptor_,
      UInt64Wrapper::internal_default_instance(),
      UInt64Wrapper_offsets_,
      -1,
      -1,
      -1,
      sizeof(UInt64Wrapper),
      GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(UInt64Wrapper, _internal_metadata_));
  Int32Wrapper_descriptor_ = file->message_type(5);
  static const int Int32Wrapper_offsets_[1] = {
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(Int32Wrapper, int32_),
  };
  Int32Wrapper_reflection_ =
    ::google::protobuf::internal::GeneratedMessageReflection::NewGeneratedMessageReflection(
      Int32Wrapper_descriptor_,
      Int32Wrapper::internal_default_instance(),
      Int32Wrapper_offsets_,
      -1,
      -1,
      -1,
      sizeof(Int32Wrapper),
      GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(Int32Wrapper, _internal_metadata_));
  UInt32Wrapper_descriptor_ = file->message_type(6);
  static const int UInt32Wrapper_offsets_[1] = {
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(UInt32Wrapper, uint32_),
  };
  UInt32Wrapper_reflection_ =
    ::google::protobuf::internal::GeneratedMessageReflection::NewGeneratedMessageReflection(
      UInt32Wrapper_descriptor_,
      UInt32Wrapper::internal_default_instance(),
      UInt32Wrapper_offsets_,
      -1,
      -1,
      -1,
      sizeof(UInt32Wrapper),
      GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(UInt32Wrapper, _internal_metadata_));
  BoolWrapper_descriptor_ = file->message_type(7);
  static const int BoolWrapper_offsets_[1] = {
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(BoolWrapper, bool__),
  };
  BoolWrapper_reflection_ =
    ::google::protobuf::internal::GeneratedMessageReflection::NewGeneratedMessageReflection(
      BoolWrapper_descriptor_,
      BoolWrapper::internal_default_instance(),
      BoolWrapper_offsets_,
      -1,
      -1,
      -1,
      sizeof(BoolWrapper),
      GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(BoolWrapper, _internal_metadata_));
  StringWrapper_descriptor_ = file->message_type(8);
  static const int StringWrapper_offsets_[1] = {
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(StringWrapper, string_),
  };
  StringWrapper_reflection_ =
    ::google::protobuf::internal::GeneratedMessageReflection::NewGeneratedMessageReflection(
      StringWrapper_descriptor_,
      StringWrapper::internal_default_instance(),
      StringWrapper_offsets_,
      -1,
      -1,
      -1,
      sizeof(StringWrapper),
      GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(StringWrapper, _internal_metadata_));
  BytesWrapper_descriptor_ = file->message_type(9);
  static const int BytesWrapper_offsets_[1] = {
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(BytesWrapper, bytes_),
  };
  BytesWrapper_reflection_ =
    ::google::protobuf::internal::GeneratedMessageReflection::NewGeneratedMessageReflection(
      BytesWrapper_descriptor_,
      BytesWrapper::internal_default_instance(),
      BytesWrapper_offsets_,
      -1,
      -1,
      -1,
      sizeof(BytesWrapper),
      GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(BytesWrapper, _internal_metadata_));
}

namespace {

GOOGLE_PROTOBUF_DECLARE_ONCE(protobuf_AssignDescriptors_once_);
void protobuf_AssignDescriptorsOnce() {
  ::google::protobuf::GoogleOnceInit(&protobuf_AssignDescriptors_once_,
                 &protobuf_AssignDesc_google_2fprotobuf_2futil_2finternal_2ftestdata_2fwrappers_2eproto);
}

void protobuf_RegisterTypes(const ::std::string&) GOOGLE_ATTRIBUTE_COLD;
void protobuf_RegisterTypes(const ::std::string&) {
  protobuf_AssignDescriptorsOnce();
  ::google::protobuf::MessageFactory::InternalRegisterGeneratedMessage(
      WrappersTestCases_descriptor_, WrappersTestCases::internal_default_instance());
  ::google::protobuf::MessageFactory::InternalRegisterGeneratedMessage(
      DoubleWrapper_descriptor_, DoubleWrapper::internal_default_instance());
  ::google::protobuf::MessageFactory::InternalRegisterGeneratedMessage(
      FloatWrapper_descriptor_, FloatWrapper::internal_default_instance());
  ::google::protobuf::MessageFactory::InternalRegisterGeneratedMessage(
      Int64Wrapper_descriptor_, Int64Wrapper::internal_default_instance());
  ::google::protobuf::MessageFactory::InternalRegisterGeneratedMessage(
      UInt64Wrapper_descriptor_, UInt64Wrapper::internal_default_instance());
  ::google::protobuf::MessageFactory::InternalRegisterGeneratedMessage(
      Int32Wrapper_descriptor_, Int32Wrapper::internal_default_instance());
  ::google::protobuf::MessageFactory::InternalRegisterGeneratedMessage(
      UInt32Wrapper_descriptor_, UInt32Wrapper::internal_default_instance());
  ::google::protobuf::MessageFactory::InternalRegisterGeneratedMessage(
      BoolWrapper_descriptor_, BoolWrapper::internal_default_instance());
  ::google::protobuf::MessageFactory::InternalRegisterGeneratedMessage(
      StringWrapper_descriptor_, StringWrapper::internal_default_instance());
  ::google::protobuf::MessageFactory::InternalRegisterGeneratedMessage(
      BytesWrapper_descriptor_, BytesWrapper::internal_default_instance());
}

}  // namespace

void protobuf_ShutdownFile_google_2fprotobuf_2futil_2finternal_2ftestdata_2fwrappers_2eproto() {
  WrappersTestCases_default_instance_.Shutdown();
  delete WrappersTestCases_reflection_;
  DoubleWrapper_default_instance_.Shutdown();
  delete DoubleWrapper_reflection_;
  FloatWrapper_default_instance_.Shutdown();
  delete FloatWrapper_reflection_;
  Int64Wrapper_default_instance_.Shutdown();
  delete Int64Wrapper_reflection_;
  UInt64Wrapper_default_instance_.Shutdown();
  delete UInt64Wrapper_reflection_;
  Int32Wrapper_default_instance_.Shutdown();
  delete Int32Wrapper_reflection_;
  UInt32Wrapper_default_instance_.Shutdown();
  delete UInt32Wrapper_reflection_;
  BoolWrapper_default_instance_.Shutdown();
  delete BoolWrapper_reflection_;
  StringWrapper_default_instance_.Shutdown();
  delete StringWrapper_reflection_;
  BytesWrapper_default_instance_.Shutdown();
  delete BytesWrapper_reflection_;
}

void protobuf_InitDefaults_google_2fprotobuf_2futil_2finternal_2ftestdata_2fwrappers_2eproto_impl() {
  GOOGLE_PROTOBUF_VERIFY_VERSION;

  ::google::protobuf::protobuf_InitDefaults_google_2fprotobuf_2fwrappers_2eproto();
  WrappersTestCases_default_instance_.DefaultConstruct();
  DoubleWrapper_default_instance_.DefaultConstruct();
  FloatWrapper_default_instance_.DefaultConstruct();
  Int64Wrapper_default_instance_.DefaultConstruct();
  UInt64Wrapper_default_instance_.DefaultConstruct();
  Int32Wrapper_default_instance_.DefaultConstruct();
  UInt32Wrapper_default_instance_.DefaultConstruct();
  BoolWrapper_default_instance_.DefaultConstruct();
  StringWrapper_default_instance_.DefaultConstruct();
  BytesWrapper_default_instance_.DefaultConstruct();
  WrappersTestCases_default_instance_.get_mutable()->InitAsDefaultInstance();
  DoubleWrapper_default_instance_.get_mutable()->InitAsDefaultInstance();
  FloatWrapper_default_instance_.get_mutable()->InitAsDefaultInstance();
  Int64Wrapper_default_instance_.get_mutable()->InitAsDefaultInstance();
  UInt64Wrapper_default_instance_.get_mutable()->InitAsDefaultInstance();
  Int32Wrapper_default_instance_.get_mutable()->InitAsDefaultInstance();
  UInt32Wrapper_default_instance_.get_mutable()->InitAsDefaultInstance();
  BoolWrapper_default_instance_.get_mutable()->InitAsDefaultInstance();
  StringWrapper_default_instance_.get_mutable()->InitAsDefaultInstance();
  BytesWrapper_default_instance_.get_mutable()->InitAsDefaultInstance();
}

GOOGLE_PROTOBUF_DECLARE_ONCE(protobuf_InitDefaults_google_2fprotobuf_2futil_2finternal_2ftestdata_2fwrappers_2eproto_once_);
void protobuf_InitDefaults_google_2fprotobuf_2futil_2finternal_2ftestdata_2fwrappers_2eproto() {
  ::google::protobuf::GoogleOnceInit(&protobuf_InitDefaults_google_2fprotobuf_2futil_2finternal_2ftestdata_2fwrappers_2eproto_once_,
                 &protobuf_InitDefaults_google_2fprotobuf_2futil_2finternal_2ftestdata_2fwrappers_2eproto_impl);
}
void protobuf_AddDesc_google_2fprotobuf_2futil_2finternal_2ftestdata_2fwrappers_2eproto_impl() {
  GOOGLE_PROTOBUF_VERIFY_VERSION;

  protobuf_InitDefaults_google_2fprotobuf_2futil_2finternal_2ftestdata_2fwrappers_2eproto();
  ::google::protobuf::DescriptorPool::InternalAddGeneratedFile(
    "\n5google/protobuf/util/internal/testdata"
    "/wrappers.proto\022\027google.protobuf.testing"
    "\032\036google/protobuf/wrappers.proto\"\303\t\n\021Wra"
    "ppersTestCases\022>\n\016double_wrapper\030\001 \001(\0132&"
    ".google.protobuf.testing.DoubleWrapper\022<"
    "\n\rfloat_wrapper\030\002 \001(\0132%.google.protobuf."
    "testing.FloatWrapper\022<\n\rint64_wrapper\030\003 "
    "\001(\0132%.google.protobuf.testing.Int64Wrapp"
    "er\022>\n\016uint64_wrapper\030\004 \001(\0132&.google.prot"
    "obuf.testing.UInt64Wrapper\022<\n\rint32_wrap"
    "per\030\005 \001(\0132%.google.protobuf.testing.Int3"
    "2Wrapper\022>\n\016uint32_wrapper\030\006 \001(\0132&.googl"
    "e.protobuf.testing.UInt32Wrapper\022:\n\014bool"
    "_wrapper\030\007 \001(\0132$.google.protobuf.testing"
    ".BoolWrapper\022>\n\016string_wrapper\030\010 \001(\0132&.g"
    "oogle.protobuf.testing.StringWrapper\022<\n\r"
    "bytes_wrapper\030\t \001(\0132%.google.protobuf.te"
    "sting.BytesWrapper\022F\n\026double_wrapper_def"
    "ault\030\n \001(\0132&.google.protobuf.testing.Dou"
    "bleWrapper\022D\n\025float_wrapper_default\030\013 \001("
    "\0132%.google.protobuf.testing.FloatWrapper"
    "\022D\n\025int64_wrapper_default\030\014 \001(\0132%.google"
    ".protobuf.testing.Int64Wrapper\022F\n\026uint64"
    "_wrapper_default\030\r \001(\0132&.google.protobuf"
    ".testing.UInt64Wrapper\022D\n\025int32_wrapper_"
    "default\030\016 \001(\0132%.google.protobuf.testing."
    "Int32Wrapper\022F\n\026uint32_wrapper_default\030\017"
    " \001(\0132&.google.protobuf.testing.UInt32Wra"
    "pper\022B\n\024bool_wrapper_default\030\020 \001(\0132$.goo"
    "gle.protobuf.testing.BoolWrapper\022F\n\026stri"
    "ng_wrapper_default\030\021 \001(\0132&.google.protob"
    "uf.testing.StringWrapper\022D\n\025bytes_wrappe"
    "r_default\030\022 \001(\0132%.google.protobuf.testin"
    "g.BytesWrapper\"=\n\rDoubleWrapper\022,\n\006doubl"
    "e\030\001 \001(\0132\034.google.protobuf.DoubleValue\":\n"
    "\014FloatWrapper\022*\n\005float\030\001 \001(\0132\033.google.pr"
    "otobuf.FloatValue\":\n\014Int64Wrapper\022*\n\005int"
    "64\030\001 \001(\0132\033.google.protobuf.Int64Value\"=\n"
    "\rUInt64Wrapper\022,\n\006uint64\030\001 \001(\0132\034.google."
    "protobuf.UInt64Value\":\n\014Int32Wrapper\022*\n\005"
    "int32\030\001 \001(\0132\033.google.protobuf.Int32Value"
    "\"=\n\rUInt32Wrapper\022,\n\006uint32\030\001 \001(\0132\034.goog"
    "le.protobuf.UInt32Value\"7\n\013BoolWrapper\022("
    "\n\004bool\030\001 \001(\0132\032.google.protobuf.BoolValue"
    "\"=\n\rStringWrapper\022,\n\006string\030\001 \001(\0132\034.goog"
    "le.protobuf.StringValue\":\n\014BytesWrapper\022"
    "*\n\005bytes\030\001 \001(\0132\033.google.protobuf.BytesVa"
    "lue2u\n\023WrappersTestService\022^\n\004Call\022*.goo"
    "gle.protobuf.testing.WrappersTestCases\032*"
    ".google.protobuf.testing.WrappersTestCas"
    "esb\006proto3", 2010);
  ::google::protobuf::MessageFactory::InternalRegisterGeneratedFile(
    "google/protobuf/util/internal/testdata/wrappers.proto", &protobuf_RegisterTypes);
  ::google::protobuf::protobuf_AddDesc_google_2fprotobuf_2fwrappers_2eproto();
  ::google::protobuf::internal::OnShutdown(&protobuf_ShutdownFile_google_2fprotobuf_2futil_2finternal_2ftestdata_2fwrappers_2eproto);
}

GOOGLE_PROTOBUF_DECLARE_ONCE(protobuf_AddDesc_google_2fprotobuf_2futil_2finternal_2ftestdata_2fwrappers_2eproto_once_);
void protobuf_AddDesc_google_2fprotobuf_2futil_2finternal_2ftestdata_2fwrappers_2eproto() {
  ::google::protobuf::GoogleOnceInit(&protobuf_AddDesc_google_2fprotobuf_2futil_2finternal_2ftestdata_2fwrappers_2eproto_once_,
                 &protobuf_AddDesc_google_2fprotobuf_2futil_2finternal_2ftestdata_2fwrappers_2eproto_impl);
}
// Force AddDescriptors() to be called at static initialization time.
struct StaticDescriptorInitializer_google_2fprotobuf_2futil_2finternal_2ftestdata_2fwrappers_2eproto {
  StaticDescriptorInitializer_google_2fprotobuf_2futil_2finternal_2ftestdata_2fwrappers_2eproto() {
    protobuf_AddDesc_google_2fprotobuf_2futil_2finternal_2ftestdata_2fwrappers_2eproto();
  }
} static_descriptor_initializer_google_2fprotobuf_2futil_2finternal_2ftestdata_2fwrappers_2eproto_;

namespace {

static void MergeFromFail(int line) GOOGLE_ATTRIBUTE_COLD GOOGLE_ATTRIBUTE_NORETURN;
static void MergeFromFail(int line) {
  ::google::protobuf::internal::MergeFromFail(__FILE__, line);
}

}  // namespace


// ===================================================================

#if !defined(_MSC_VER) || _MSC_VER >= 1900
const int WrappersTestCases::kDoubleWrapperFieldNumber;
const int WrappersTestCases::kFloatWrapperFieldNumber;
const int WrappersTestCases::kInt64WrapperFieldNumber;
const int WrappersTestCases::kUint64WrapperFieldNumber;
const int WrappersTestCases::kInt32WrapperFieldNumber;
const int WrappersTestCases::kUint32WrapperFieldNumber;
const int WrappersTestCases::kBoolWrapperFieldNumber;
const int WrappersTestCases::kStringWrapperFieldNumber;
const int WrappersTestCases::kBytesWrapperFieldNumber;
const int WrappersTestCases::kDoubleWrapperDefaultFieldNumber;
const int WrappersTestCases::kFloatWrapperDefaultFieldNumber;
const int WrappersTestCases::kInt64WrapperDefaultFieldNumber;
const int WrappersTestCases::kUint64WrapperDefaultFieldNumber;
const int WrappersTestCases::kInt32WrapperDefaultFieldNumber;
const int WrappersTestCases::kUint32WrapperDefaultFieldNumber;
const int WrappersTestCases::kBoolWrapperDefaultFieldNumber;
const int WrappersTestCases::kStringWrapperDefaultFieldNumber;
const int WrappersTestCases::kBytesWrapperDefaultFieldNumber;
#endif  // !defined(_MSC_VER) || _MSC_VER >= 1900

WrappersTestCases::WrappersTestCases()
  : ::google::protobuf::Message(), _internal_metadata_(NULL) {
  if (this != internal_default_instance()) protobuf_InitDefaults_google_2fprotobuf_2futil_2finternal_2ftestdata_2fwrappers_2eproto();
  SharedCtor();
  // @@protoc_insertion_point(constructor:google.protobuf.testing.WrappersTestCases)
}

void WrappersTestCases::InitAsDefaultInstance() {
  double_wrapper_ = const_cast< ::google::protobuf::testing::DoubleWrapper*>(
      ::google::protobuf::testing::DoubleWrapper::internal_default_instance());
  float_wrapper_ = const_cast< ::google::protobuf::testing::FloatWrapper*>(
      ::google::protobuf::testing::FloatWrapper::internal_default_instance());
  int64_wrapper_ = const_cast< ::google::protobuf::testing::Int64Wrapper*>(
      ::google::protobuf::testing::Int64Wrapper::internal_default_instance());
  uint64_wrapper_ = const_cast< ::google::protobuf::testing::UInt64Wrapper*>(
      ::google::protobuf::testing::UInt64Wrapper::internal_default_instance());
  int32_wrapper_ = const_cast< ::google::protobuf::testing::Int32Wrapper*>(
      ::google::protobuf::testing::Int32Wrapper::internal_default_instance());
  uint32_wrapper_ = const_cast< ::google::protobuf::testing::UInt32Wrapper*>(
      ::google::protobuf::testing::UInt32Wrapper::internal_default_instance());
  bool_wrapper_ = const_cast< ::google::protobuf::testing::BoolWrapper*>(
      ::google::protobuf::testing::BoolWrapper::internal_default_instance());
  string_wrapper_ = const_cast< ::google::protobuf::testing::StringWrapper*>(
      ::google::protobuf::testing::StringWrapper::internal_default_instance());
  bytes_wrapper_ = const_cast< ::google::protobuf::testing::BytesWrapper*>(
      ::google::protobuf::testing::BytesWrapper::internal_default_instance());
  double_wrapper_default_ = const_cast< ::google::protobuf::testing::DoubleWrapper*>(
      ::google::protobuf::testing::DoubleWrapper::internal_default_instance());
  float_wrapper_default_ = const_cast< ::google::protobuf::testing::FloatWrapper*>(
      ::google::protobuf::testing::FloatWrapper::internal_default_instance());
  int64_wrapper_default_ = const_cast< ::google::protobuf::testing::Int64Wrapper*>(
      ::google::protobuf::testing::Int64Wrapper::internal_default_instance());
  uint64_wrapper_default_ = const_cast< ::google::protobuf::testing::UInt64Wrapper*>(
      ::google::protobuf::testing::UInt64Wrapper::internal_default_instance());
  int32_wrapper_default_ = const_cast< ::google::protobuf::testing::Int32Wrapper*>(
      ::google::protobuf::testing::Int32Wrapper::internal_default_instance());
  uint32_wrapper_default_ = const_cast< ::google::protobuf::testing::UInt32Wrapper*>(
      ::google::protobuf::testing::UInt32Wrapper::internal_default_instance());
  bool_wrapper_default_ = const_cast< ::google::protobuf::testing::BoolWrapper*>(
      ::google::protobuf::testing::BoolWrapper::internal_default_instance());
  string_wrapper_default_ = const_cast< ::google::protobuf::testing::StringWrapper*>(
      ::google::protobuf::testing::StringWrapper::internal_default_instance());
  bytes_wrapper_default_ = const_cast< ::google::protobuf::testing::BytesWrapper*>(
      ::google::protobuf::testing::BytesWrapper::internal_default_instance());
}

WrappersTestCases::WrappersTestCases(const WrappersTestCases& from)
  : ::google::protobuf::Message(),
    _internal_metadata_(NULL) {
  SharedCtor();
  UnsafeMergeFrom(from);
  // @@protoc_insertion_point(copy_constructor:google.protobuf.testing.WrappersTestCases)
}

void WrappersTestCases::SharedCtor() {
  double_wrapper_ = NULL;
  float_wrapper_ = NULL;
  int64_wrapper_ = NULL;
  uint64_wrapper_ = NULL;
  int32_wrapper_ = NULL;
  uint32_wrapper_ = NULL;
  bool_wrapper_ = NULL;
  string_wrapper_ = NULL;
  bytes_wrapper_ = NULL;
  double_wrapper_default_ = NULL;
  float_wrapper_default_ = NULL;
  int64_wrapper_default_ = NULL;
  uint64_wrapper_default_ = NULL;
  int32_wrapper_default_ = NULL;
  uint32_wrapper_default_ = NULL;
  bool_wrapper_default_ = NULL;
  string_wrapper_default_ = NULL;
  bytes_wrapper_default_ = NULL;
  _cached_size_ = 0;
}

WrappersTestCases::~WrappersTestCases() {
  // @@protoc_insertion_point(destructor:google.protobuf.testing.WrappersTestCases)
  SharedDtor();
}

void WrappersTestCases::SharedDtor() {
  if (this != &WrappersTestCases_default_instance_.get()) {
    delete double_wrapper_;
    delete float_wrapper_;
    delete int64_wrapper_;
    delete uint64_wrapper_;
    delete int32_wrapper_;
    delete uint32_wrapper_;
    delete bool_wrapper_;
    delete string_wrapper_;
    delete bytes_wrapper_;
    delete double_wrapper_default_;
    delete float_wrapper_default_;
    delete int64_wrapper_default_;
    delete uint64_wrapper_default_;
    delete int32_wrapper_default_;
    delete uint32_wrapper_default_;
    delete bool_wrapper_default_;
    delete string_wrapper_default_;
    delete bytes_wrapper_default_;
  }
}

void WrappersTestCases::SetCachedSize(int size) const {
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
}
const ::google::protobuf::Descriptor* WrappersTestCases::descriptor() {
  protobuf_AssignDescriptorsOnce();
  return WrappersTestCases_descriptor_;
}

const WrappersTestCases& WrappersTestCases::default_instance() {
  protobuf_InitDefaults_google_2fprotobuf_2futil_2finternal_2ftestdata_2fwrappers_2eproto();
  return *internal_default_instance();
}

::google::protobuf::internal::ExplicitlyConstructed<WrappersTestCases> WrappersTestCases_default_instance_;

WrappersTestCases* WrappersTestCases::New(::google::protobuf::Arena* arena) const {
  WrappersTestCases* n = new WrappersTestCases;
  if (arena != NULL) {
    arena->Own(n);
  }
  return n;
}

void WrappersTestCases::Clear() {
// @@protoc_insertion_point(message_clear_start:google.protobuf.testing.WrappersTestCases)
  if (GetArenaNoVirtual() == NULL && double_wrapper_ != NULL) delete double_wrapper_;
  double_wrapper_ = NULL;
  if (GetArenaNoVirtual() == NULL && float_wrapper_ != NULL) delete float_wrapper_;
  float_wrapper_ = NULL;
  if (GetArenaNoVirtual() == NULL && int64_wrapper_ != NULL) delete int64_wrapper_;
  int64_wrapper_ = NULL;
  if (GetArenaNoVirtual() == NULL && uint64_wrapper_ != NULL) delete uint64_wrapper_;
  uint64_wrapper_ = NULL;
  if (GetArenaNoVirtual() == NULL && int32_wrapper_ != NULL) delete int32_wrapper_;
  int32_wrapper_ = NULL;
  if (GetArenaNoVirtual() == NULL && uint32_wrapper_ != NULL) delete uint32_wrapper_;
  uint32_wrapper_ = NULL;
  if (GetArenaNoVirtual() == NULL && bool_wrapper_ != NULL) delete bool_wrapper_;
  bool_wrapper_ = NULL;
  if (GetArenaNoVirtual() == NULL && string_wrapper_ != NULL) delete string_wrapper_;
  string_wrapper_ = NULL;
  if (GetArenaNoVirtual() == NULL && bytes_wrapper_ != NULL) delete bytes_wrapper_;
  bytes_wrapper_ = NULL;
  if (GetArenaNoVirtual() == NULL && double_wrapper_default_ != NULL) delete double_wrapper_default_;
  double_wrapper_default_ = NULL;
  if (GetArenaNoVirtual() == NULL && float_wrapper_default_ != NULL) delete float_wrapper_default_;
  float_wrapper_default_ = NULL;
  if (GetArenaNoVirtual() == NULL && int64_wrapper_default_ != NULL) delete int64_wrapper_default_;
  int64_wrapper_default_ = NULL;
  if (GetArenaNoVirtual() == NULL && uint64_wrapper_default_ != NULL) delete uint64_wrapper_default_;
  uint64_wrapper_default_ = NULL;
  if (GetArenaNoVirtual() == NULL && int32_wrapper_default_ != NULL) delete int32_wrapper_default_;
  int32_wrapper_default_ = NULL;
  if (GetArenaNoVirtual() == NULL && uint32_wrapper_default_ != NULL) delete uint32_wrapper_default_;
  uint32_wrapper_default_ = NULL;
  if (GetArenaNoVirtual() == NULL && bool_wrapper_default_ != NULL) delete bool_wrapper_default_;
  bool_wrapper_default_ = NULL;
  if (GetArenaNoVirtual() == NULL && string_wrapper_default_ != NULL) delete string_wrapper_default_;
  string_wrapper_default_ = NULL;
  if (GetArenaNoVirtual() == NULL && bytes_wrapper_default_ != NULL) delete bytes_wrapper_default_;
  bytes_wrapper_default_ = NULL;
}

bool WrappersTestCases::MergePartialFromCodedStream(
    ::google::protobuf::io::CodedInputStream* input) {
#define DO_(EXPRESSION) if (!GOOGLE_PREDICT_TRUE(EXPRESSION)) goto failure
  ::google::protobuf::uint32 tag;
  // @@protoc_insertion_point(parse_start:google.protobuf.testing.WrappersTestCases)
  for (;;) {
    ::std::pair< ::google::protobuf::uint32, bool> p = input->ReadTagWithCutoff(16383);
    tag = p.first;
    if (!p.second) goto handle_unusual;
    switch (::google::protobuf::internal::WireFormatLite::GetTagFieldNumber(tag)) {
      // optional .google.protobuf.testing.DoubleWrapper double_wrapper = 1;
      case 1: {
        if (tag == 10) {
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessageNoVirtual(
               input, mutable_double_wrapper()));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(18)) goto parse_float_wrapper;
        break;
      }

      // optional .google.protobuf.testing.FloatWrapper float_wrapper = 2;
      case 2: {
        if (tag == 18) {
         parse_float_wrapper:
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessageNoVirtual(
               input, mutable_float_wrapper()));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(26)) goto parse_int64_wrapper;
        break;
      }

      // optional .google.protobuf.testing.Int64Wrapper int64_wrapper = 3;
      case 3: {
        if (tag == 26) {
         parse_int64_wrapper:
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessageNoVirtual(
               input, mutable_int64_wrapper()));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(34)) goto parse_uint64_wrapper;
        break;
      }

      // optional .google.protobuf.testing.UInt64Wrapper uint64_wrapper = 4;
      case 4: {
        if (tag == 34) {
         parse_uint64_wrapper:
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessageNoVirtual(
               input, mutable_uint64_wrapper()));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(42)) goto parse_int32_wrapper;
        break;
      }

      // optional .google.protobuf.testing.Int32Wrapper int32_wrapper = 5;
      case 5: {
        if (tag == 42) {
         parse_int32_wrapper:
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessageNoVirtual(
               input, mutable_int32_wrapper()));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(50)) goto parse_uint32_wrapper;
        break;
      }

      // optional .google.protobuf.testing.UInt32Wrapper uint32_wrapper = 6;
      case 6: {
        if (tag == 50) {
         parse_uint32_wrapper:
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessageNoVirtual(
               input, mutable_uint32_wrapper()));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(58)) goto parse_bool_wrapper;
        break;
      }

      // optional .google.protobuf.testing.BoolWrapper bool_wrapper = 7;
      case 7: {
        if (tag == 58) {
         parse_bool_wrapper:
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessageNoVirtual(
               input, mutable_bool_wrapper()));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(66)) goto parse_string_wrapper;
        break;
      }

      // optional .google.protobuf.testing.StringWrapper string_wrapper = 8;
      case 8: {
        if (tag == 66) {
         parse_string_wrapper:
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessageNoVirtual(
               input, mutable_string_wrapper()));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(74)) goto parse_bytes_wrapper;
        break;
      }

      // optional .google.protobuf.testing.BytesWrapper bytes_wrapper = 9;
      case 9: {
        if (tag == 74) {
         parse_bytes_wrapper:
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessageNoVirtual(
               input, mutable_bytes_wrapper()));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(82)) goto parse_double_wrapper_default;
        break;
      }

      // optional .google.protobuf.testing.DoubleWrapper double_wrapper_default = 10;
      case 10: {
        if (tag == 82) {
         parse_double_wrapper_default:
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessageNoVirtual(
               input, mutable_double_wrapper_default()));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(90)) goto parse_float_wrapper_default;
        break;
      }

      // optional .google.protobuf.testing.FloatWrapper float_wrapper_default = 11;
      case 11: {
        if (tag == 90) {
         parse_float_wrapper_default:
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessageNoVirtual(
               input, mutable_float_wrapper_default()));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(98)) goto parse_int64_wrapper_default;
        break;
      }

      // optional .google.protobuf.testing.Int64Wrapper int64_wrapper_default = 12;
      case 12: {
        if (tag == 98) {
         parse_int64_wrapper_default:
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessageNoVirtual(
               input, mutable_int64_wrapper_default()));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(106)) goto parse_uint64_wrapper_default;
        break;
      }

      // optional .google.protobuf.testing.UInt64Wrapper uint64_wrapper_default = 13;
      case 13: {
        if (tag == 106) {
         parse_uint64_wrapper_default:
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessageNoVirtual(
               input, mutable_uint64_wrapper_default()));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(114)) goto parse_int32_wrapper_default;
        break;
      }

      // optional .google.protobuf.testing.Int32Wrapper int32_wrapper_default = 14;
      case 14: {
        if (tag == 114) {
         parse_int32_wrapper_default:
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessageNoVirtual(
               input, mutable_int32_wrapper_default()));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(122)) goto parse_uint32_wrapper_default;
        break;
      }

      // optional .google.protobuf.testing.UInt32Wrapper uint32_wrapper_default = 15;
      case 15: {
        if (tag == 122) {
         parse_uint32_wrapper_default:
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessageNoVirtual(
               input, mutable_uint32_wrapper_default()));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(130)) goto parse_bool_wrapper_default;
        break;
      }

      // optional .google.protobuf.testing.BoolWrapper bool_wrapper_default = 16;
      case 16: {
        if (tag == 130) {
         parse_bool_wrapper_default:
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessageNoVirtual(
               input, mutable_bool_wrapper_default()));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(138)) goto parse_string_wrapper_default;
        break;
      }

      // optional .google.protobuf.testing.StringWrapper string_wrapper_default = 17;
      case 17: {
        if (tag == 138) {
         parse_string_wrapper_default:
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessageNoVirtual(
               input, mutable_string_wrapper_default()));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(146)) goto parse_bytes_wrapper_default;
        break;
      }

      // optional .google.protobuf.testing.BytesWrapper bytes_wrapper_default = 18;
      case 18: {
        if (tag == 146) {
         parse_bytes_wrapper_default:
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessageNoVirtual(
               input, mutable_bytes_wrapper_default()));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectAtEnd()) goto success;
        break;
      }

      default: {
      handle_unusual:
        if (tag == 0 ||
            ::google::protobuf::internal::WireFormatLite::GetTagWireType(tag) ==
            ::google::protobuf::internal::WireFormatLite::WIRETYPE_END_GROUP) {
          goto success;
        }
        DO_(::google::protobuf::internal::WireFormatLite::SkipField(input, tag));
        break;
      }
    }
  }
success:
  // @@protoc_insertion_point(parse_success:google.protobuf.testing.WrappersTestCases)
  return true;
failure:
  // @@protoc_insertion_point(parse_failure:google.protobuf.testing.WrappersTestCases)
  return false;
#undef DO_
}

void WrappersTestCases::SerializeWithCachedSizes(
    ::google::protobuf::io::CodedOutputStream* output) const {
  // @@protoc_insertion_point(serialize_start:google.protobuf.testing.WrappersTestCases)
  // optional .google.protobuf.testing.DoubleWrapper double_wrapper = 1;
  if (this->has_double_wrapper()) {
    ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
      1, *this->double_wrapper_, output);
  }

  // optional .google.protobuf.testing.FloatWrapper float_wrapper = 2;
  if (this->has_float_wrapper()) {
    ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
      2, *this->float_wrapper_, output);
  }

  // optional .google.protobuf.testing.Int64Wrapper int64_wrapper = 3;
  if (this->has_int64_wrapper()) {
    ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
      3, *this->int64_wrapper_, output);
  }

  // optional .google.protobuf.testing.UInt64Wrapper uint64_wrapper = 4;
  if (this->has_uint64_wrapper()) {
    ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
      4, *this->uint64_wrapper_, output);
  }

  // optional .google.protobuf.testing.Int32Wrapper int32_wrapper = 5;
  if (this->has_int32_wrapper()) {
    ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
      5, *this->int32_wrapper_, output);
  }

  // optional .google.protobuf.testing.UInt32Wrapper uint32_wrapper = 6;
  if (this->has_uint32_wrapper()) {
    ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
      6, *this->uint32_wrapper_, output);
  }

  // optional .google.protobuf.testing.BoolWrapper bool_wrapper = 7;
  if (this->has_bool_wrapper()) {
    ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
      7, *this->bool_wrapper_, output);
  }

  // optional .google.protobuf.testing.StringWrapper string_wrapper = 8;
  if (this->has_string_wrapper()) {
    ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
      8, *this->string_wrapper_, output);
  }

  // optional .google.protobuf.testing.BytesWrapper bytes_wrapper = 9;
  if (this->has_bytes_wrapper()) {
    ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
      9, *this->bytes_wrapper_, output);
  }

  // optional .google.protobuf.testing.DoubleWrapper double_wrapper_default = 10;
  if (this->has_double_wrapper_default()) {
    ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
      10, *this->double_wrapper_default_, output);
  }

  // optional .google.protobuf.testing.FloatWrapper float_wrapper_default = 11;
  if (this->has_float_wrapper_default()) {
    ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
      11, *this->float_wrapper_default_, output);
  }

  // optional .google.protobuf.testing.Int64Wrapper int64_wrapper_default = 12;
  if (this->has_int64_wrapper_default()) {
    ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
      12, *this->int64_wrapper_default_, output);
  }

  // optional .google.protobuf.testing.UInt64Wrapper uint64_wrapper_default = 13;
  if (this->has_uint64_wrapper_default()) {
    ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
      13, *this->uint64_wrapper_default_, output);
  }

  // optional .google.protobuf.testing.Int32Wrapper int32_wrapper_default = 14;
  if (this->has_int32_wrapper_default()) {
    ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
      14, *this->int32_wrapper_default_, output);
  }

  // optional .google.protobuf.testing.UInt32Wrapper uint32_wrapper_default = 15;
  if (this->has_uint32_wrapper_default()) {
    ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
      15, *this->uint32_wrapper_default_, output);
  }

  // optional .google.protobuf.testing.BoolWrapper bool_wrapper_default = 16;
  if (this->has_bool_wrapper_default()) {
    ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
      16, *this->bool_wrapper_default_, output);
  }

  // optional .google.protobuf.testing.StringWrapper string_wrapper_default = 17;
  if (this->has_string_wrapper_default()) {
    ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
      17, *this->string_wrapper_default_, output);
  }

  // optional .google.protobuf.testing.BytesWrapper bytes_wrapper_default = 18;
  if (this->has_bytes_wrapper_default()) {
    ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
      18, *this->bytes_wrapper_default_, output);
  }

  // @@protoc_insertion_point(serialize_end:google.protobuf.testing.WrappersTestCases)
}

::google::protobuf::uint8* WrappersTestCases::InternalSerializeWithCachedSizesToArray(
    bool deterministic, ::google::protobuf::uint8* target) const {
  (void)deterministic; // Unused
  // @@protoc_insertion_point(serialize_to_array_start:google.protobuf.testing.WrappersTestCases)
  // optional .google.protobuf.testing.DoubleWrapper double_wrapper = 1;
  if (this->has_double_wrapper()) {
    target = ::google::protobuf::internal::WireFormatLite::
      InternalWriteMessageNoVirtualToArray(
        1, *this->double_wrapper_, false, target);
  }

  // optional .google.protobuf.testing.FloatWrapper float_wrapper = 2;
  if (this->has_float_wrapper()) {
    target = ::google::protobuf::internal::WireFormatLite::
      InternalWriteMessageNoVirtualToArray(
        2, *this->float_wrapper_, false, target);
  }

  // optional .google.protobuf.testing.Int64Wrapper int64_wrapper = 3;
  if (this->has_int64_wrapper()) {
    target = ::google::protobuf::internal::WireFormatLite::
      InternalWriteMessageNoVirtualToArray(
        3, *this->int64_wrapper_, false, target);
  }

  // optional .google.protobuf.testing.UInt64Wrapper uint64_wrapper = 4;
  if (this->has_uint64_wrapper()) {
    target = ::google::protobuf::internal::WireFormatLite::
      InternalWriteMessageNoVirtualToArray(
        4, *this->uint64_wrapper_, false, target);
  }

  // optional .google.protobuf.testing.Int32Wrapper int32_wrapper = 5;
  if (this->has_int32_wrapper()) {
    target = ::google::protobuf::internal::WireFormatLite::
      InternalWriteMessageNoVirtualToArray(
        5, *this->int32_wrapper_, false, target);
  }

  // optional .google.protobuf.testing.UInt32Wrapper uint32_wrapper = 6;
  if (this->has_uint32_wrapper()) {
    target = ::google::protobuf::internal::WireFormatLite::
      InternalWriteMessageNoVirtualToArray(
        6, *this->uint32_wrapper_, false, target);
  }

  // optional .google.protobuf.testing.BoolWrapper bool_wrapper = 7;
  if (this->has_bool_wrapper()) {
    target = ::google::protobuf::internal::WireFormatLite::
      InternalWriteMessageNoVirtualToArray(
        7, *this->bool_wrapper_, false, target);
  }

  // optional .google.protobuf.testing.StringWrapper string_wrapper = 8;
  if (this->has_string_wrapper()) {
    target = ::google::protobuf::internal::WireFormatLite::
      InternalWriteMessageNoVirtualToArray(
        8, *this->string_wrapper_, false, target);
  }

  // optional .google.protobuf.testing.BytesWrapper bytes_wrapper = 9;
  if (this->has_bytes_wrapper()) {
    target = ::google::protobuf::internal::WireFormatLite::
      InternalWriteMessageNoVirtualToArray(
        9, *this->bytes_wrapper_, false, target);
  }

  // optional .google.protobuf.testing.DoubleWrapper double_wrapper_default = 10;
  if (this->has_double_wrapper_default()) {
    target = ::google::protobuf::internal::WireFormatLite::
      InternalWriteMessageNoVirtualToArray(
        10, *this->double_wrapper_default_, false, target);
  }

  // optional .google.protobuf.testing.FloatWrapper float_wrapper_default = 11;
  if (this->has_float_wrapper_default()) {
    target = ::google::protobuf::internal::WireFormatLite::
      InternalWriteMessageNoVirtualToArray(
        11, *this->float_wrapper_default_, false, target);
  }

  // optional .google.protobuf.testing.Int64Wrapper int64_wrapper_default = 12;
  if (this->has_int64_wrapper_default()) {
    target = ::google::protobuf::internal::WireFormatLite::
      InternalWriteMessageNoVirtualToArray(
        12, *this->int64_wrapper_default_, false, target);
  }

  // optional .google.protobuf.testing.UInt64Wrapper uint64_wrapper_default = 13;
  if (this->has_uint64_wrapper_default()) {
    target = ::google::protobuf::internal::WireFormatLite::
      InternalWriteMessageNoVirtualToArray(
        13, *this->uint64_wrapper_default_, false, target);
  }

  // optional .google.protobuf.testing.Int32Wrapper int32_wrapper_default = 14;
  if (this->has_int32_wrapper_default()) {
    target = ::google::protobuf::internal::WireFormatLite::
      InternalWriteMessageNoVirtualToArray(
        14, *this->int32_wrapper_default_, false, target);
  }

  // optional .google.protobuf.testing.UInt32Wrapper uint32_wrapper_default = 15;
  if (this->has_uint32_wrapper_default()) {
    target = ::google::protobuf::internal::WireFormatLite::
      InternalWriteMessageNoVirtualToArray(
        15, *this->uint32_wrapper_default_, false, target);
  }

  // optional .google.protobuf.testing.BoolWrapper bool_wrapper_default = 16;
  if (this->has_bool_wrapper_default()) {
    target = ::google::protobuf::internal::WireFormatLite::
      InternalWriteMessageNoVirtualToArray(
        16, *this->bool_wrapper_default_, false, target);
  }

  // optional .google.protobuf.testing.StringWrapper string_wrapper_default = 17;
  if (this->has_string_wrapper_default()) {
    target = ::google::protobuf::internal::WireFormatLite::
      InternalWriteMessageNoVirtualToArray(
        17, *this->string_wrapper_default_, false, target);
  }

  // optional .google.protobuf.testing.BytesWrapper bytes_wrapper_default = 18;
  if (this->has_bytes_wrapper_default()) {
    target = ::google::protobuf::internal::WireFormatLite::
      InternalWriteMessageNoVirtualToArray(
        18, *this->bytes_wrapper_default_, false, target);
  }

  // @@protoc_insertion_point(serialize_to_array_end:google.protobuf.testing.WrappersTestCases)
  return target;
}

size_t WrappersTestCases::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:google.protobuf.testing.WrappersTestCases)
  size_t total_size = 0;

  // optional .google.protobuf.testing.DoubleWrapper double_wrapper = 1;
  if (this->has_double_wrapper()) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::MessageSizeNoVirtual(
        *this->double_wrapper_);
  }

  // optional .google.protobuf.testing.FloatWrapper float_wrapper = 2;
  if (this->has_float_wrapper()) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::MessageSizeNoVirtual(
        *this->float_wrapper_);
  }

  // optional .google.protobuf.testing.Int64Wrapper int64_wrapper = 3;
  if (this->has_int64_wrapper()) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::MessageSizeNoVirtual(
        *this->int64_wrapper_);
  }

  // optional .google.protobuf.testing.UInt64Wrapper uint64_wrapper = 4;
  if (this->has_uint64_wrapper()) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::MessageSizeNoVirtual(
        *this->uint64_wrapper_);
  }

  // optional .google.protobuf.testing.Int32Wrapper int32_wrapper = 5;
  if (this->has_int32_wrapper()) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::MessageSizeNoVirtual(
        *this->int32_wrapper_);
  }

  // optional .google.protobuf.testing.UInt32Wrapper uint32_wrapper = 6;
  if (this->has_uint32_wrapper()) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::MessageSizeNoVirtual(
        *this->uint32_wrapper_);
  }

  // optional .google.protobuf.testing.BoolWrapper bool_wrapper = 7;
  if (this->has_bool_wrapper()) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::MessageSizeNoVirtual(
        *this->bool_wrapper_);
  }

  // optional .google.protobuf.testing.StringWrapper string_wrapper = 8;
  if (this->has_string_wrapper()) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::MessageSizeNoVirtual(
        *this->string_wrapper_);
  }

  // optional .google.protobuf.testing.BytesWrapper bytes_wrapper = 9;
  if (this->has_bytes_wrapper()) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::MessageSizeNoVirtual(
        *this->bytes_wrapper_);
  }

  // optional .google.protobuf.testing.DoubleWrapper double_wrapper_default = 10;
  if (this->has_double_wrapper_default()) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::MessageSizeNoVirtual(
        *this->double_wrapper_default_);
  }

  // optional .google.protobuf.testing.FloatWrapper float_wrapper_default = 11;
  if (this->has_float_wrapper_default()) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::MessageSizeNoVirtual(
        *this->float_wrapper_default_);
  }

  // optional .google.protobuf.testing.Int64Wrapper int64_wrapper_default = 12;
  if (this->has_int64_wrapper_default()) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::MessageSizeNoVirtual(
        *this->int64_wrapper_default_);
  }

  // optional .google.protobuf.testing.UInt64Wrapper uint64_wrapper_default = 13;
  if (this->has_uint64_wrapper_default()) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::MessageSizeNoVirtual(
        *this->uint64_wrapper_default_);
  }

  // optional .google.protobuf.testing.Int32Wrapper int32_wrapper_default = 14;
  if (this->has_int32_wrapper_default()) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::MessageSizeNoVirtual(
        *this->int32_wrapper_default_);
  }

  // optional .google.protobuf.testing.UInt32Wrapper uint32_wrapper_default = 15;
  if (this->has_uint32_wrapper_default()) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::MessageSizeNoVirtual(
        *this->uint32_wrapper_default_);
  }

  // optional .google.protobuf.testing.BoolWrapper bool_wrapper_default = 16;
  if (this->has_bool_wrapper_default()) {
    total_size += 2 +
      ::google::protobuf::internal::WireFormatLite::MessageSizeNoVirtual(
        *this->bool_wrapper_default_);
  }

  // optional .google.protobuf.testing.StringWrapper string_wrapper_default = 17;
  if (this->has_string_wrapper_default()) {
    total_size += 2 +
      ::google::protobuf::internal::WireFormatLite::MessageSizeNoVirtual(
        *this->string_wrapper_default_);
  }

  // optional .google.protobuf.testing.BytesWrapper bytes_wrapper_default = 18;
  if (this->has_bytes_wrapper_default()) {
    total_size += 2 +
      ::google::protobuf::internal::WireFormatLite::MessageSizeNoVirtual(
        *this->bytes_wrapper_default_);
  }

  int cached_size = ::google::protobuf::internal::ToCachedSize(total_size);
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = cached_size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
  return total_size;
}

void WrappersTestCases::MergeFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_merge_from_start:google.protobuf.testing.WrappersTestCases)
  if (GOOGLE_PREDICT_FALSE(&from == this)) MergeFromFail(__LINE__);
  const WrappersTestCases* source =
      ::google::protobuf::internal::DynamicCastToGenerated<const WrappersTestCases>(
          &from);
  if (source == NULL) {
  // @@protoc_insertion_point(generalized_merge_from_cast_fail:google.protobuf.testing.WrappersTestCases)
    ::google::protobuf::internal::ReflectionOps::Merge(from, this);
  } else {
  // @@protoc_insertion_point(generalized_merge_from_cast_success:google.protobuf.testing.WrappersTestCases)
    UnsafeMergeFrom(*source);
  }
}

void WrappersTestCases::MergeFrom(const WrappersTestCases& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:google.protobuf.testing.WrappersTestCases)
  if (GOOGLE_PREDICT_TRUE(&from != this)) {
    UnsafeMergeFrom(from);
  } else {
    MergeFromFail(__LINE__);
  }
}

void WrappersTestCases::UnsafeMergeFrom(const WrappersTestCases& from) {
  GOOGLE_DCHECK(&from != this);
  if (from.has_double_wrapper()) {
    mutable_double_wrapper()->::google::protobuf::testing::DoubleWrapper::MergeFrom(from.double_wrapper());
  }
  if (from.has_float_wrapper()) {
    mutable_float_wrapper()->::google::protobuf::testing::FloatWrapper::MergeFrom(from.float_wrapper());
  }
  if (from.has_int64_wrapper()) {
    mutable_int64_wrapper()->::google::protobuf::testing::Int64Wrapper::MergeFrom(from.int64_wrapper());
  }
  if (from.has_uint64_wrapper()) {
    mutable_uint64_wrapper()->::google::protobuf::testing::UInt64Wrapper::MergeFrom(from.uint64_wrapper());
  }
  if (from.has_int32_wrapper()) {
    mutable_int32_wrapper()->::google::protobuf::testing::Int32Wrapper::MergeFrom(from.int32_wrapper());
  }
  if (from.has_uint32_wrapper()) {
    mutable_uint32_wrapper()->::google::protobuf::testing::UInt32Wrapper::MergeFrom(from.uint32_wrapper());
  }
  if (from.has_bool_wrapper()) {
    mutable_bool_wrapper()->::google::protobuf::testing::BoolWrapper::MergeFrom(from.bool_wrapper());
  }
  if (from.has_string_wrapper()) {
    mutable_string_wrapper()->::google::protobuf::testing::StringWrapper::MergeFrom(from.string_wrapper());
  }
  if (from.has_bytes_wrapper()) {
    mutable_bytes_wrapper()->::google::protobuf::testing::BytesWrapper::MergeFrom(from.bytes_wrapper());
  }
  if (from.has_double_wrapper_default()) {
    mutable_double_wrapper_default()->::google::protobuf::testing::DoubleWrapper::MergeFrom(from.double_wrapper_default());
  }
  if (from.has_float_wrapper_default()) {
    mutable_float_wrapper_default()->::google::protobuf::testing::FloatWrapper::MergeFrom(from.float_wrapper_default());
  }
  if (from.has_int64_wrapper_default()) {
    mutable_int64_wrapper_default()->::google::protobuf::testing::Int64Wrapper::MergeFrom(from.int64_wrapper_default());
  }
  if (from.has_uint64_wrapper_default()) {
    mutable_uint64_wrapper_default()->::google::protobuf::testing::UInt64Wrapper::MergeFrom(from.uint64_wrapper_default());
  }
  if (from.has_int32_wrapper_default()) {
    mutable_int32_wrapper_default()->::google::protobuf::testing::Int32Wrapper::MergeFrom(from.int32_wrapper_default());
  }
  if (from.has_uint32_wrapper_default()) {
    mutable_uint32_wrapper_default()->::google::protobuf::testing::UInt32Wrapper::MergeFrom(from.uint32_wrapper_default());
  }
  if (from.has_bool_wrapper_default()) {
    mutable_bool_wrapper_default()->::google::protobuf::testing::BoolWrapper::MergeFrom(from.bool_wrapper_default());
  }
  if (from.has_string_wrapper_default()) {
    mutable_string_wrapper_default()->::google::protobuf::testing::StringWrapper::MergeFrom(from.string_wrapper_default());
  }
  if (from.has_bytes_wrapper_default()) {
    mutable_bytes_wrapper_default()->::google::protobuf::testing::BytesWrapper::MergeFrom(from.bytes_wrapper_default());
  }
}

void WrappersTestCases::CopyFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_copy_from_start:google.protobuf.testing.WrappersTestCases)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

void WrappersTestCases::CopyFrom(const WrappersTestCases& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:google.protobuf.testing.WrappersTestCases)
  if (&from == this) return;
  Clear();
  UnsafeMergeFrom(from);
}

bool WrappersTestCases::IsInitialized() const {

  return true;
}

void WrappersTestCases::Swap(WrappersTestCases* other) {
  if (other == this) return;
  InternalSwap(other);
}
void WrappersTestCases::InternalSwap(WrappersTestCases* other) {
  std::swap(double_wrapper_, other->double_wrapper_);
  std::swap(float_wrapper_, other->float_wrapper_);
  std::swap(int64_wrapper_, other->int64_wrapper_);
  std::swap(uint64_wrapper_, other->uint64_wrapper_);
  std::swap(int32_wrapper_, other->int32_wrapper_);
  std::swap(uint32_wrapper_, other->uint32_wrapper_);
  std::swap(bool_wrapper_, other->bool_wrapper_);
  std::swap(string_wrapper_, other->string_wrapper_);
  std::swap(bytes_wrapper_, other->bytes_wrapper_);
  std::swap(double_wrapper_default_, other->double_wrapper_default_);
  std::swap(float_wrapper_default_, other->float_wrapper_default_);
  std::swap(int64_wrapper_default_, other->int64_wrapper_default_);
  std::swap(uint64_wrapper_default_, other->uint64_wrapper_default_);
  std::swap(int32_wrapper_default_, other->int32_wrapper_default_);
  std::swap(uint32_wrapper_default_, other->uint32_wrapper_default_);
  std::swap(bool_wrapper_default_, other->bool_wrapper_default_);
  std::swap(string_wrapper_default_, other->string_wrapper_default_);
  std::swap(bytes_wrapper_default_, other->bytes_wrapper_default_);
  _internal_metadata_.Swap(&other->_internal_metadata_);
  std::swap(_cached_size_, other->_cached_size_);
}

::google::protobuf::Metadata WrappersTestCases::GetMetadata() const {
  protobuf_AssignDescriptorsOnce();
  ::google::protobuf::Metadata metadata;
  metadata.descriptor = WrappersTestCases_descriptor_;
  metadata.reflection = WrappersTestCases_reflection_;
  return metadata;
}

#if PROTOBUF_INLINE_NOT_IN_HEADERS
// WrappersTestCases

// optional .google.protobuf.testing.DoubleWrapper double_wrapper = 1;
bool WrappersTestCases::has_double_wrapper() const {
  return this != internal_default_instance() && double_wrapper_ != NULL;
}
void WrappersTestCases::clear_double_wrapper() {
  if (GetArenaNoVirtual() == NULL && double_wrapper_ != NULL) delete double_wrapper_;
  double_wrapper_ = NULL;
}
const ::google::protobuf::testing::DoubleWrapper& WrappersTestCases::double_wrapper() const {
  // @@protoc_insertion_point(field_get:google.protobuf.testing.WrappersTestCases.double_wrapper)
  return double_wrapper_ != NULL ? *double_wrapper_
                         : *::google::protobuf::testing::DoubleWrapper::internal_default_instance();
}
::google::protobuf::testing::DoubleWrapper* WrappersTestCases::mutable_double_wrapper() {
  
  if (double_wrapper_ == NULL) {
    double_wrapper_ = new ::google::protobuf::testing::DoubleWrapper;
  }
  // @@protoc_insertion_point(field_mutable:google.protobuf.testing.WrappersTestCases.double_wrapper)
  return double_wrapper_;
}
::google::protobuf::testing::DoubleWrapper* WrappersTestCases::release_double_wrapper() {
  // @@protoc_insertion_point(field_release:google.protobuf.testing.WrappersTestCases.double_wrapper)
  
  ::google::protobuf::testing::DoubleWrapper* temp = double_wrapper_;
  double_wrapper_ = NULL;
  return temp;
}
void WrappersTestCases::set_allocated_double_wrapper(::google::protobuf::testing::DoubleWrapper* double_wrapper) {
  delete double_wrapper_;
  double_wrapper_ = double_wrapper;
  if (double_wrapper) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_set_allocated:google.protobuf.testing.WrappersTestCases.double_wrapper)
}

// optional .google.protobuf.testing.FloatWrapper float_wrapper = 2;
bool WrappersTestCases::has_float_wrapper() const {
  return this != internal_default_instance() && float_wrapper_ != NULL;
}
void WrappersTestCases::clear_float_wrapper() {
  if (GetArenaNoVirtual() == NULL && float_wrapper_ != NULL) delete float_wrapper_;
  float_wrapper_ = NULL;
}
const ::google::protobuf::testing::FloatWrapper& WrappersTestCases::float_wrapper() const {
  // @@protoc_insertion_point(field_get:google.protobuf.testing.WrappersTestCases.float_wrapper)
  return float_wrapper_ != NULL ? *float_wrapper_
                         : *::google::protobuf::testing::FloatWrapper::internal_default_instance();
}
::google::protobuf::testing::FloatWrapper* WrappersTestCases::mutable_float_wrapper() {
  
  if (float_wrapper_ == NULL) {
    float_wrapper_ = new ::google::protobuf::testing::FloatWrapper;
  }
  // @@protoc_insertion_point(field_mutable:google.protobuf.testing.WrappersTestCases.float_wrapper)
  return float_wrapper_;
}
::google::protobuf::testing::FloatWrapper* WrappersTestCases::release_float_wrapper() {
  // @@protoc_insertion_point(field_release:google.protobuf.testing.WrappersTestCases.float_wrapper)
  
  ::google::protobuf::testing::FloatWrapper* temp = float_wrapper_;
  float_wrapper_ = NULL;
  return temp;
}
void WrappersTestCases::set_allocated_float_wrapper(::google::protobuf::testing::FloatWrapper* float_wrapper) {
  delete float_wrapper_;
  float_wrapper_ = float_wrapper;
  if (float_wrapper) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_set_allocated:google.protobuf.testing.WrappersTestCases.float_wrapper)
}

// optional .google.protobuf.testing.Int64Wrapper int64_wrapper = 3;
bool WrappersTestCases::has_int64_wrapper() const {
  return this != internal_default_instance() && int64_wrapper_ != NULL;
}
void WrappersTestCases::clear_int64_wrapper() {
  if (GetArenaNoVirtual() == NULL && int64_wrapper_ != NULL) delete int64_wrapper_;
  int64_wrapper_ = NULL;
}
const ::google::protobuf::testing::Int64Wrapper& WrappersTestCases::int64_wrapper() const {
  // @@protoc_insertion_point(field_get:google.protobuf.testing.WrappersTestCases.int64_wrapper)
  return int64_wrapper_ != NULL ? *int64_wrapper_
                         : *::google::protobuf::testing::Int64Wrapper::internal_default_instance();
}
::google::protobuf::testing::Int64Wrapper* WrappersTestCases::mutable_int64_wrapper() {
  
  if (int64_wrapper_ == NULL) {
    int64_wrapper_ = new ::google::protobuf::testing::Int64Wrapper;
  }
  // @@protoc_insertion_point(field_mutable:google.protobuf.testing.WrappersTestCases.int64_wrapper)
  return int64_wrapper_;
}
::google::protobuf::testing::Int64Wrapper* WrappersTestCases::release_int64_wrapper() {
  // @@protoc_insertion_point(field_release:google.protobuf.testing.WrappersTestCases.int64_wrapper)
  
  ::google::protobuf::testing::Int64Wrapper* temp = int64_wrapper_;
  int64_wrapper_ = NULL;
  return temp;
}
void WrappersTestCases::set_allocated_int64_wrapper(::google::protobuf::testing::Int64Wrapper* int64_wrapper) {
  delete int64_wrapper_;
  int64_wrapper_ = int64_wrapper;
  if (int64_wrapper) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_set_allocated:google.protobuf.testing.WrappersTestCases.int64_wrapper)
}

// optional .google.protobuf.testing.UInt64Wrapper uint64_wrapper = 4;
bool WrappersTestCases::has_uint64_wrapper() const {
  return this != internal_default_instance() && uint64_wrapper_ != NULL;
}
void WrappersTestCases::clear_uint64_wrapper() {
  if (GetArenaNoVirtual() == NULL && uint64_wrapper_ != NULL) delete uint64_wrapper_;
  uint64_wrapper_ = NULL;
}
const ::google::protobuf::testing::UInt64Wrapper& WrappersTestCases::uint64_wrapper() const {
  // @@protoc_insertion_point(field_get:google.protobuf.testing.WrappersTestCases.uint64_wrapper)
  return uint64_wrapper_ != NULL ? *uint64_wrapper_
                         : *::google::protobuf::testing::UInt64Wrapper::internal_default_instance();
}
::google::protobuf::testing::UInt64Wrapper* WrappersTestCases::mutable_uint64_wrapper() {
  
  if (uint64_wrapper_ == NULL) {
    uint64_wrapper_ = new ::google::protobuf::testing::UInt64Wrapper;
  }
  // @@protoc_insertion_point(field_mutable:google.protobuf.testing.WrappersTestCases.uint64_wrapper)
  return uint64_wrapper_;
}
::google::protobuf::testing::UInt64Wrapper* WrappersTestCases::release_uint64_wrapper() {
  // @@protoc_insertion_point(field_release:google.protobuf.testing.WrappersTestCases.uint64_wrapper)
  
  ::google::protobuf::testing::UInt64Wrapper* temp = uint64_wrapper_;
  uint64_wrapper_ = NULL;
  return temp;
}
void WrappersTestCases::set_allocated_uint64_wrapper(::google::protobuf::testing::UInt64Wrapper* uint64_wrapper) {
  delete uint64_wrapper_;
  uint64_wrapper_ = uint64_wrapper;
  if (uint64_wrapper) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_set_allocated:google.protobuf.testing.WrappersTestCases.uint64_wrapper)
}

// optional .google.protobuf.testing.Int32Wrapper int32_wrapper = 5;
bool WrappersTestCases::has_int32_wrapper() const {
  return this != internal_default_instance() && int32_wrapper_ != NULL;
}
void WrappersTestCases::clear_int32_wrapper() {
  if (GetArenaNoVirtual() == NULL && int32_wrapper_ != NULL) delete int32_wrapper_;
  int32_wrapper_ = NULL;
}
const ::google::protobuf::testing::Int32Wrapper& WrappersTestCases::int32_wrapper() const {
  // @@protoc_insertion_point(field_get:google.protobuf.testing.WrappersTestCases.int32_wrapper)
  return int32_wrapper_ != NULL ? *int32_wrapper_
                         : *::google::protobuf::testing::Int32Wrapper::internal_default_instance();
}
::google::protobuf::testing::Int32Wrapper* WrappersTestCases::mutable_int32_wrapper() {
  
  if (int32_wrapper_ == NULL) {
    int32_wrapper_ = new ::google::protobuf::testing::Int32Wrapper;
  }
  // @@protoc_insertion_point(field_mutable:google.protobuf.testing.WrappersTestCases.int32_wrapper)
  return int32_wrapper_;
}
::google::protobuf::testing::Int32Wrapper* WrappersTestCases::release_int32_wrapper() {
  // @@protoc_insertion_point(field_release:google.protobuf.testing.WrappersTestCases.int32_wrapper)
  
  ::google::protobuf::testing::Int32Wrapper* temp = int32_wrapper_;
  int32_wrapper_ = NULL;
  return temp;
}
void WrappersTestCases::set_allocated_int32_wrapper(::google::protobuf::testing::Int32Wrapper* int32_wrapper) {
  delete int32_wrapper_;
  int32_wrapper_ = int32_wrapper;
  if (int32_wrapper) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_set_allocated:google.protobuf.testing.WrappersTestCases.int32_wrapper)
}

// optional .google.protobuf.testing.UInt32Wrapper uint32_wrapper = 6;
bool WrappersTestCases::has_uint32_wrapper() const {
  return this != internal_default_instance() && uint32_wrapper_ != NULL;
}
void WrappersTestCases::clear_uint32_wrapper() {
  if (GetArenaNoVirtual() == NULL && uint32_wrapper_ != NULL) delete uint32_wrapper_;
  uint32_wrapper_ = NULL;
}
const ::google::protobuf::testing::UInt32Wrapper& WrappersTestCases::uint32_wrapper() const {
  // @@protoc_insertion_point(field_get:google.protobuf.testing.WrappersTestCases.uint32_wrapper)
  return uint32_wrapper_ != NULL ? *uint32_wrapper_
                         : *::google::protobuf::testing::UInt32Wrapper::internal_default_instance();
}
::google::protobuf::testing::UInt32Wrapper* WrappersTestCases::mutable_uint32_wrapper() {
  
  if (uint32_wrapper_ == NULL) {
    uint32_wrapper_ = new ::google::protobuf::testing::UInt32Wrapper;
  }
  // @@protoc_insertion_point(field_mutable:google.protobuf.testing.WrappersTestCases.uint32_wrapper)
  return uint32_wrapper_;
}
::google::protobuf::testing::UInt32Wrapper* WrappersTestCases::release_uint32_wrapper() {
  // @@protoc_insertion_point(field_release:google.protobuf.testing.WrappersTestCases.uint32_wrapper)
  
  ::google::protobuf::testing::UInt32Wrapper* temp = uint32_wrapper_;
  uint32_wrapper_ = NULL;
  return temp;
}
void WrappersTestCases::set_allocated_uint32_wrapper(::google::protobuf::testing::UInt32Wrapper* uint32_wrapper) {
  delete uint32_wrapper_;
  uint32_wrapper_ = uint32_wrapper;
  if (uint32_wrapper) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_set_allocated:google.protobuf.testing.WrappersTestCases.uint32_wrapper)
}

// optional .google.protobuf.testing.BoolWrapper bool_wrapper = 7;
bool WrappersTestCases::has_bool_wrapper() const {
  return this != internal_default_instance() && bool_wrapper_ != NULL;
}
void WrappersTestCases::clear_bool_wrapper() {
  if (GetArenaNoVirtual() == NULL && bool_wrapper_ != NULL) delete bool_wrapper_;
  bool_wrapper_ = NULL;
}
const ::google::protobuf::testing::BoolWrapper& WrappersTestCases::bool_wrapper() const {
  // @@protoc_insertion_point(field_get:google.protobuf.testing.WrappersTestCases.bool_wrapper)
  return bool_wrapper_ != NULL ? *bool_wrapper_
                         : *::google::protobuf::testing::BoolWrapper::internal_default_instance();
}
::google::protobuf::testing::BoolWrapper* WrappersTestCases::mutable_bool_wrapper() {
  
  if (bool_wrapper_ == NULL) {
    bool_wrapper_ = new ::google::protobuf::testing::BoolWrapper;
  }
  // @@protoc_insertion_point(field_mutable:google.protobuf.testing.WrappersTestCases.bool_wrapper)
  return bool_wrapper_;
}
::google::protobuf::testing::BoolWrapper* WrappersTestCases::release_bool_wrapper() {
  // @@protoc_insertion_point(field_release:google.protobuf.testing.WrappersTestCases.bool_wrapper)
  
  ::google::protobuf::testing::BoolWrapper* temp = bool_wrapper_;
  bool_wrapper_ = NULL;
  return temp;
}
void WrappersTestCases::set_allocated_bool_wrapper(::google::protobuf::testing::BoolWrapper* bool_wrapper) {
  delete bool_wrapper_;
  bool_wrapper_ = bool_wrapper;
  if (bool_wrapper) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_set_allocated:google.protobuf.testing.WrappersTestCases.bool_wrapper)
}

// optional .google.protobuf.testing.StringWrapper string_wrapper = 8;
bool WrappersTestCases::has_string_wrapper() const {
  return this != internal_default_instance() && string_wrapper_ != NULL;
}
void WrappersTestCases::clear_string_wrapper() {
  if (GetArenaNoVirtual() == NULL && string_wrapper_ != NULL) delete string_wrapper_;
  string_wrapper_ = NULL;
}
const ::google::protobuf::testing::StringWrapper& WrappersTestCases::string_wrapper() const {
  // @@protoc_insertion_point(field_get:google.protobuf.testing.WrappersTestCases.string_wrapper)
  return string_wrapper_ != NULL ? *string_wrapper_
                         : *::google::protobuf::testing::StringWrapper::internal_default_instance();
}
::google::protobuf::testing::StringWrapper* WrappersTestCases::mutable_string_wrapper() {
  
  if (string_wrapper_ == NULL) {
    string_wrapper_ = new ::google::protobuf::testing::StringWrapper;
  }
  // @@protoc_insertion_point(field_mutable:google.protobuf.testing.WrappersTestCases.string_wrapper)
  return string_wrapper_;
}
::google::protobuf::testing::StringWrapper* WrappersTestCases::release_string_wrapper() {
  // @@protoc_insertion_point(field_release:google.protobuf.testing.WrappersTestCases.string_wrapper)
  
  ::google::protobuf::testing::StringWrapper* temp = string_wrapper_;
  string_wrapper_ = NULL;
  return temp;
}
void WrappersTestCases::set_allocated_string_wrapper(::google::protobuf::testing::StringWrapper* string_wrapper) {
  delete string_wrapper_;
  string_wrapper_ = string_wrapper;
  if (string_wrapper) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_set_allocated:google.protobuf.testing.WrappersTestCases.string_wrapper)
}

// optional .google.protobuf.testing.BytesWrapper bytes_wrapper = 9;
bool WrappersTestCases::has_bytes_wrapper() const {
  return this != internal_default_instance() && bytes_wrapper_ != NULL;
}
void WrappersTestCases::clear_bytes_wrapper() {
  if (GetArenaNoVirtual() == NULL && bytes_wrapper_ != NULL) delete bytes_wrapper_;
  bytes_wrapper_ = NULL;
}
const ::google::protobuf::testing::BytesWrapper& WrappersTestCases::bytes_wrapper() const {
  // @@protoc_insertion_point(field_get:google.protobuf.testing.WrappersTestCases.bytes_wrapper)
  return bytes_wrapper_ != NULL ? *bytes_wrapper_
                         : *::google::protobuf::testing::BytesWrapper::internal_default_instance();
}
::google::protobuf::testing::BytesWrapper* WrappersTestCases::mutable_bytes_wrapper() {
  
  if (bytes_wrapper_ == NULL) {
    bytes_wrapper_ = new ::google::protobuf::testing::BytesWrapper;
  }
  // @@protoc_insertion_point(field_mutable:google.protobuf.testing.WrappersTestCases.bytes_wrapper)
  return bytes_wrapper_;
}
::google::protobuf::testing::BytesWrapper* WrappersTestCases::release_bytes_wrapper() {
  // @@protoc_insertion_point(field_release:google.protobuf.testing.WrappersTestCases.bytes_wrapper)
  
  ::google::protobuf::testing::BytesWrapper* temp = bytes_wrapper_;
  bytes_wrapper_ = NULL;
  return temp;
}
void WrappersTestCases::set_allocated_bytes_wrapper(::google::protobuf::testing::BytesWrapper* bytes_wrapper) {
  delete bytes_wrapper_;
  bytes_wrapper_ = bytes_wrapper;
  if (bytes_wrapper) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_set_allocated:google.protobuf.testing.WrappersTestCases.bytes_wrapper)
}

// optional .google.protobuf.testing.DoubleWrapper double_wrapper_default = 10;
bool WrappersTestCases::has_double_wrapper_default() const {
  return this != internal_default_instance() && double_wrapper_default_ != NULL;
}
void WrappersTestCases::clear_double_wrapper_default() {
  if (GetArenaNoVirtual() == NULL && double_wrapper_default_ != NULL) delete double_wrapper_default_;
  double_wrapper_default_ = NULL;
}
const ::google::protobuf::testing::DoubleWrapper& WrappersTestCases::double_wrapper_default() const {
  // @@protoc_insertion_point(field_get:google.protobuf.testing.WrappersTestCases.double_wrapper_default)
  return double_wrapper_default_ != NULL ? *double_wrapper_default_
                         : *::google::protobuf::testing::DoubleWrapper::internal_default_instance();
}
::google::protobuf::testing::DoubleWrapper* WrappersTestCases::mutable_double_wrapper_default() {
  
  if (double_wrapper_default_ == NULL) {
    double_wrapper_default_ = new ::google::protobuf::testing::DoubleWrapper;
  }
  // @@protoc_insertion_point(field_mutable:google.protobuf.testing.WrappersTestCases.double_wrapper_default)
  return double_wrapper_default_;
}
::google::protobuf::testing::DoubleWrapper* WrappersTestCases::release_double_wrapper_default() {
  // @@protoc_insertion_point(field_release:google.protobuf.testing.WrappersTestCases.double_wrapper_default)
  
  ::google::protobuf::testing::DoubleWrapper* temp = double_wrapper_default_;
  double_wrapper_default_ = NULL;
  return temp;
}
void WrappersTestCases::set_allocated_double_wrapper_default(::google::protobuf::testing::DoubleWrapper* double_wrapper_default) {
  delete double_wrapper_default_;
  double_wrapper_default_ = double_wrapper_default;
  if (double_wrapper_default) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_set_allocated:google.protobuf.testing.WrappersTestCases.double_wrapper_default)
}

// optional .google.protobuf.testing.FloatWrapper float_wrapper_default = 11;
bool WrappersTestCases::has_float_wrapper_default() const {
  return this != internal_default_instance() && float_wrapper_default_ != NULL;
}
void WrappersTestCases::clear_float_wrapper_default() {
  if (GetArenaNoVirtual() == NULL && float_wrapper_default_ != NULL) delete float_wrapper_default_;
  float_wrapper_default_ = NULL;
}
const ::google::protobuf::testing::FloatWrapper& WrappersTestCases::float_wrapper_default() const {
  // @@protoc_insertion_point(field_get:google.protobuf.testing.WrappersTestCases.float_wrapper_default)
  return float_wrapper_default_ != NULL ? *float_wrapper_default_
                         : *::google::protobuf::testing::FloatWrapper::internal_default_instance();
}
::google::protobuf::testing::FloatWrapper* WrappersTestCases::mutable_float_wrapper_default() {
  
  if (float_wrapper_default_ == NULL) {
    float_wrapper_default_ = new ::google::protobuf::testing::FloatWrapper;
  }
  // @@protoc_insertion_point(field_mutable:google.protobuf.testing.WrappersTestCases.float_wrapper_default)
  return float_wrapper_default_;
}
::google::protobuf::testing::FloatWrapper* WrappersTestCases::release_float_wrapper_default() {
  // @@protoc_insertion_point(field_release:google.protobuf.testing.WrappersTestCases.float_wrapper_default)
  
  ::google::protobuf::testing::FloatWrapper* temp = float_wrapper_default_;
  float_wrapper_default_ = NULL;
  return temp;
}
void WrappersTestCases::set_allocated_float_wrapper_default(::google::protobuf::testing::FloatWrapper* float_wrapper_default) {
  delete float_wrapper_default_;
  float_wrapper_default_ = float_wrapper_default;
  if (float_wrapper_default) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_set_allocated:google.protobuf.testing.WrappersTestCases.float_wrapper_default)
}

// optional .google.protobuf.testing.Int64Wrapper int64_wrapper_default = 12;
bool WrappersTestCases::has_int64_wrapper_default() const {
  return this != internal_default_instance() && int64_wrapper_default_ != NULL;
}
void WrappersTestCases::clear_int64_wrapper_default() {
  if (GetArenaNoVirtual() == NULL && int64_wrapper_default_ != NULL) delete int64_wrapper_default_;
  int64_wrapper_default_ = NULL;
}
const ::google::protobuf::testing::Int64Wrapper& WrappersTestCases::int64_wrapper_default() const {
  // @@protoc_insertion_point(field_get:google.protobuf.testing.WrappersTestCases.int64_wrapper_default)
  return int64_wrapper_default_ != NULL ? *int64_wrapper_default_
                         : *::google::protobuf::testing::Int64Wrapper::internal_default_instance();
}
::google::protobuf::testing::Int64Wrapper* WrappersTestCases::mutable_int64_wrapper_default() {
  
  if (int64_wrapper_default_ == NULL) {
    int64_wrapper_default_ = new ::google::protobuf::testing::Int64Wrapper;
  }
  // @@protoc_insertion_point(field_mutable:google.protobuf.testing.WrappersTestCases.int64_wrapper_default)
  return int64_wrapper_default_;
}
::google::protobuf::testing::Int64Wrapper* WrappersTestCases::release_int64_wrapper_default() {
  // @@protoc_insertion_point(field_release:google.protobuf.testing.WrappersTestCases.int64_wrapper_default)
  
  ::google::protobuf::testing::Int64Wrapper* temp = int64_wrapper_default_;
  int64_wrapper_default_ = NULL;
  return temp;
}
void WrappersTestCases::set_allocated_int64_wrapper_default(::google::protobuf::testing::Int64Wrapper* int64_wrapper_default) {
  delete int64_wrapper_default_;
  int64_wrapper_default_ = int64_wrapper_default;
  if (int64_wrapper_default) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_set_allocated:google.protobuf.testing.WrappersTestCases.int64_wrapper_default)
}

// optional .google.protobuf.testing.UInt64Wrapper uint64_wrapper_default = 13;
bool WrappersTestCases::has_uint64_wrapper_default() const {
  return this != internal_default_instance() && uint64_wrapper_default_ != NULL;
}
void WrappersTestCases::clear_uint64_wrapper_default() {
  if (GetArenaNoVirtual() == NULL && uint64_wrapper_default_ != NULL) delete uint64_wrapper_default_;
  uint64_wrapper_default_ = NULL;
}
const ::google::protobuf::testing::UInt64Wrapper& WrappersTestCases::uint64_wrapper_default() const {
  // @@protoc_insertion_point(field_get:google.protobuf.testing.WrappersTestCases.uint64_wrapper_default)
  return uint64_wrapper_default_ != NULL ? *uint64_wrapper_default_
                         : *::google::protobuf::testing::UInt64Wrapper::internal_default_instance();
}
::google::protobuf::testing::UInt64Wrapper* WrappersTestCases::mutable_uint64_wrapper_default() {
  
  if (uint64_wrapper_default_ == NULL) {
    uint64_wrapper_default_ = new ::google::protobuf::testing::UInt64Wrapper;
  }
  // @@protoc_insertion_point(field_mutable:google.protobuf.testing.WrappersTestCases.uint64_wrapper_default)
  return uint64_wrapper_default_;
}
::google::protobuf::testing::UInt64Wrapper* WrappersTestCases::release_uint64_wrapper_default() {
  // @@protoc_insertion_point(field_release:google.protobuf.testing.WrappersTestCases.uint64_wrapper_default)
  
  ::google::protobuf::testing::UInt64Wrapper* temp = uint64_wrapper_default_;
  uint64_wrapper_default_ = NULL;
  return temp;
}
void WrappersTestCases::set_allocated_uint64_wrapper_default(::google::protobuf::testing::UInt64Wrapper* uint64_wrapper_default) {
  delete uint64_wrapper_default_;
  uint64_wrapper_default_ = uint64_wrapper_default;
  if (uint64_wrapper_default) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_set_allocated:google.protobuf.testing.WrappersTestCases.uint64_wrapper_default)
}

// optional .google.protobuf.testing.Int32Wrapper int32_wrapper_default = 14;
bool WrappersTestCases::has_int32_wrapper_default() const {
  return this != internal_default_instance() && int32_wrapper_default_ != NULL;
}
void WrappersTestCases::clear_int32_wrapper_default() {
  if (GetArenaNoVirtual() == NULL && int32_wrapper_default_ != NULL) delete int32_wrapper_default_;
  int32_wrapper_default_ = NULL;
}
const ::google::protobuf::testing::Int32Wrapper& WrappersTestCases::int32_wrapper_default() const {
  // @@protoc_insertion_point(field_get:google.protobuf.testing.WrappersTestCases.int32_wrapper_default)
  return int32_wrapper_default_ != NULL ? *int32_wrapper_default_
                         : *::google::protobuf::testing::Int32Wrapper::internal_default_instance();
}
::google::protobuf::testing::Int32Wrapper* WrappersTestCases::mutable_int32_wrapper_default() {
  
  if (int32_wrapper_default_ == NULL) {
    int32_wrapper_default_ = new ::google::protobuf::testing::Int32Wrapper;
  }
  // @@protoc_insertion_point(field_mutable:google.protobuf.testing.WrappersTestCases.int32_wrapper_default)
  return int32_wrapper_default_;
}
::google::protobuf::testing::Int32Wrapper* WrappersTestCases::release_int32_wrapper_default() {
  // @@protoc_insertion_point(field_release:google.protobuf.testing.WrappersTestCases.int32_wrapper_default)
  
  ::google::protobuf::testing::Int32Wrapper* temp = int32_wrapper_default_;
  int32_wrapper_default_ = NULL;
  return temp;
}
void WrappersTestCases::set_allocated_int32_wrapper_default(::google::protobuf::testing::Int32Wrapper* int32_wrapper_default) {
  delete int32_wrapper_default_;
  int32_wrapper_default_ = int32_wrapper_default;
  if (int32_wrapper_default) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_set_allocated:google.protobuf.testing.WrappersTestCases.int32_wrapper_default)
}

// optional .google.protobuf.testing.UInt32Wrapper uint32_wrapper_default = 15;
bool WrappersTestCases::has_uint32_wrapper_default() const {
  return this != internal_default_instance() && uint32_wrapper_default_ != NULL;
}
void WrappersTestCases::clear_uint32_wrapper_default() {
  if (GetArenaNoVirtual() == NULL && uint32_wrapper_default_ != NULL) delete uint32_wrapper_default_;
  uint32_wrapper_default_ = NULL;
}
const ::google::protobuf::testing::UInt32Wrapper& WrappersTestCases::uint32_wrapper_default() const {
  // @@protoc_insertion_point(field_get:google.protobuf.testing.WrappersTestCases.uint32_wrapper_default)
  return uint32_wrapper_default_ != NULL ? *uint32_wrapper_default_
                         : *::google::protobuf::testing::UInt32Wrapper::internal_default_instance();
}
::google::protobuf::testing::UInt32Wrapper* WrappersTestCases::mutable_uint32_wrapper_default() {
  
  if (uint32_wrapper_default_ == NULL) {
    uint32_wrapper_default_ = new ::google::protobuf::testing::UInt32Wrapper;
  }
  // @@protoc_insertion_point(field_mutable:google.protobuf.testing.WrappersTestCases.uint32_wrapper_default)
  return uint32_wrapper_default_;
}
::google::protobuf::testing::UInt32Wrapper* WrappersTestCases::release_uint32_wrapper_default() {
  // @@protoc_insertion_point(field_release:google.protobuf.testing.WrappersTestCases.uint32_wrapper_default)
  
  ::google::protobuf::testing::UInt32Wrapper* temp = uint32_wrapper_default_;
  uint32_wrapper_default_ = NULL;
  return temp;
}
void WrappersTestCases::set_allocated_uint32_wrapper_default(::google::protobuf::testing::UInt32Wrapper* uint32_wrapper_default) {
  delete uint32_wrapper_default_;
  uint32_wrapper_default_ = uint32_wrapper_default;
  if (uint32_wrapper_default) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_set_allocated:google.protobuf.testing.WrappersTestCases.uint32_wrapper_default)
}

// optional .google.protobuf.testing.BoolWrapper bool_wrapper_default = 16;
bool WrappersTestCases::has_bool_wrapper_default() const {
  return this != internal_default_instance() && bool_wrapper_default_ != NULL;
}
void WrappersTestCases::clear_bool_wrapper_default() {
  if (GetArenaNoVirtual() == NULL && bool_wrapper_default_ != NULL) delete bool_wrapper_default_;
  bool_wrapper_default_ = NULL;
}
const ::google::protobuf::testing::BoolWrapper& WrappersTestCases::bool_wrapper_default() const {
  // @@protoc_insertion_point(field_get:google.protobuf.testing.WrappersTestCases.bool_wrapper_default)
  return bool_wrapper_default_ != NULL ? *bool_wrapper_default_
                         : *::google::protobuf::testing::BoolWrapper::internal_default_instance();
}
::google::protobuf::testing::BoolWrapper* WrappersTestCases::mutable_bool_wrapper_default() {
  
  if (bool_wrapper_default_ == NULL) {
    bool_wrapper_default_ = new ::google::protobuf::testing::BoolWrapper;
  }
  // @@protoc_insertion_point(field_mutable:google.protobuf.testing.WrappersTestCases.bool_wrapper_default)
  return bool_wrapper_default_;
}
::google::protobuf::testing::BoolWrapper* WrappersTestCases::release_bool_wrapper_default() {
  // @@protoc_insertion_point(field_release:google.protobuf.testing.WrappersTestCases.bool_wrapper_default)
  
  ::google::protobuf::testing::BoolWrapper* temp = bool_wrapper_default_;
  bool_wrapper_default_ = NULL;
  return temp;
}
void WrappersTestCases::set_allocated_bool_wrapper_default(::google::protobuf::testing::BoolWrapper* bool_wrapper_default) {
  delete bool_wrapper_default_;
  bool_wrapper_default_ = bool_wrapper_default;
  if (bool_wrapper_default) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_set_allocated:google.protobuf.testing.WrappersTestCases.bool_wrapper_default)
}

// optional .google.protobuf.testing.StringWrapper string_wrapper_default = 17;
bool WrappersTestCases::has_string_wrapper_default() const {
  return this != internal_default_instance() && string_wrapper_default_ != NULL;
}
void WrappersTestCases::clear_string_wrapper_default() {
  if (GetArenaNoVirtual() == NULL && string_wrapper_default_ != NULL) delete string_wrapper_default_;
  string_wrapper_default_ = NULL;
}
const ::google::protobuf::testing::StringWrapper& WrappersTestCases::string_wrapper_default() const {
  // @@protoc_insertion_point(field_get:google.protobuf.testing.WrappersTestCases.string_wrapper_default)
  return string_wrapper_default_ != NULL ? *string_wrapper_default_
                         : *::google::protobuf::testing::StringWrapper::internal_default_instance();
}
::google::protobuf::testing::StringWrapper* WrappersTestCases::mutable_string_wrapper_default() {
  
  if (string_wrapper_default_ == NULL) {
    string_wrapper_default_ = new ::google::protobuf::testing::StringWrapper;
  }
  // @@protoc_insertion_point(field_mutable:google.protobuf.testing.WrappersTestCases.string_wrapper_default)
  return string_wrapper_default_;
}
::google::protobuf::testing::StringWrapper* WrappersTestCases::release_string_wrapper_default() {
  // @@protoc_insertion_point(field_release:google.protobuf.testing.WrappersTestCases.string_wrapper_default)
  
  ::google::protobuf::testing::StringWrapper* temp = string_wrapper_default_;
  string_wrapper_default_ = NULL;
  return temp;
}
void WrappersTestCases::set_allocated_string_wrapper_default(::google::protobuf::testing::StringWrapper* string_wrapper_default) {
  delete string_wrapper_default_;
  string_wrapper_default_ = string_wrapper_default;
  if (string_wrapper_default) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_set_allocated:google.protobuf.testing.WrappersTestCases.string_wrapper_default)
}

// optional .google.protobuf.testing.BytesWrapper bytes_wrapper_default = 18;
bool WrappersTestCases::has_bytes_wrapper_default() const {
  return this != internal_default_instance() && bytes_wrapper_default_ != NULL;
}
void WrappersTestCases::clear_bytes_wrapper_default() {
  if (GetArenaNoVirtual() == NULL && bytes_wrapper_default_ != NULL) delete bytes_wrapper_default_;
  bytes_wrapper_default_ = NULL;
}
const ::google::protobuf::testing::BytesWrapper& WrappersTestCases::bytes_wrapper_default() const {
  // @@protoc_insertion_point(field_get:google.protobuf.testing.WrappersTestCases.bytes_wrapper_default)
  return bytes_wrapper_default_ != NULL ? *bytes_wrapper_default_
                         : *::google::protobuf::testing::BytesWrapper::internal_default_instance();
}
::google::protobuf::testing::BytesWrapper* WrappersTestCases::mutable_bytes_wrapper_default() {
  
  if (bytes_wrapper_default_ == NULL) {
    bytes_wrapper_default_ = new ::google::protobuf::testing::BytesWrapper;
  }
  // @@protoc_insertion_point(field_mutable:google.protobuf.testing.WrappersTestCases.bytes_wrapper_default)
  return bytes_wrapper_default_;
}
::google::protobuf::testing::BytesWrapper* WrappersTestCases::release_bytes_wrapper_default() {
  // @@protoc_insertion_point(field_release:google.protobuf.testing.WrappersTestCases.bytes_wrapper_default)
  
  ::google::protobuf::testing::BytesWrapper* temp = bytes_wrapper_default_;
  bytes_wrapper_default_ = NULL;
  return temp;
}
void WrappersTestCases::set_allocated_bytes_wrapper_default(::google::protobuf::testing::BytesWrapper* bytes_wrapper_default) {
  delete bytes_wrapper_default_;
  bytes_wrapper_default_ = bytes_wrapper_default;
  if (bytes_wrapper_default) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_set_allocated:google.protobuf.testing.WrappersTestCases.bytes_wrapper_default)
}

inline const WrappersTestCases* WrappersTestCases::internal_default_instance() {
  return &WrappersTestCases_default_instance_.get();
}
#endif  // PROTOBUF_INLINE_NOT_IN_HEADERS

// ===================================================================

#if !defined(_MSC_VER) || _MSC_VER >= 1900
const int DoubleWrapper::kDoubleFieldNumber;
#endif  // !defined(_MSC_VER) || _MSC_VER >= 1900

DoubleWrapper::DoubleWrapper()
  : ::google::protobuf::Message(), _internal_metadata_(NULL) {
  if (this != internal_default_instance()) protobuf_InitDefaults_google_2fprotobuf_2futil_2finternal_2ftestdata_2fwrappers_2eproto();
  SharedCtor();
  // @@protoc_insertion_point(constructor:google.protobuf.testing.DoubleWrapper)
}

void DoubleWrapper::InitAsDefaultInstance() {
  double__ = const_cast< ::google::protobuf::DoubleValue*>(
      ::google::protobuf::DoubleValue::internal_default_instance());
}

DoubleWrapper::DoubleWrapper(const DoubleWrapper& from)
  : ::google::protobuf::Message(),
    _internal_metadata_(NULL) {
  SharedCtor();
  UnsafeMergeFrom(from);
  // @@protoc_insertion_point(copy_constructor:google.protobuf.testing.DoubleWrapper)
}

void DoubleWrapper::SharedCtor() {
  double__ = NULL;
  _cached_size_ = 0;
}

DoubleWrapper::~DoubleWrapper() {
  // @@protoc_insertion_point(destructor:google.protobuf.testing.DoubleWrapper)
  SharedDtor();
}

void DoubleWrapper::SharedDtor() {
  if (this != &DoubleWrapper_default_instance_.get()) {
    delete double__;
  }
}

void DoubleWrapper::SetCachedSize(int size) const {
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
}
const ::google::protobuf::Descriptor* DoubleWrapper::descriptor() {
  protobuf_AssignDescriptorsOnce();
  return DoubleWrapper_descriptor_;
}

const DoubleWrapper& DoubleWrapper::default_instance() {
  protobuf_InitDefaults_google_2fprotobuf_2futil_2finternal_2ftestdata_2fwrappers_2eproto();
  return *internal_default_instance();
}

::google::protobuf::internal::ExplicitlyConstructed<DoubleWrapper> DoubleWrapper_default_instance_;

DoubleWrapper* DoubleWrapper::New(::google::protobuf::Arena* arena) const {
  DoubleWrapper* n = new DoubleWrapper;
  if (arena != NULL) {
    arena->Own(n);
  }
  return n;
}

void DoubleWrapper::Clear() {
// @@protoc_insertion_point(message_clear_start:google.protobuf.testing.DoubleWrapper)
  if (GetArenaNoVirtual() == NULL && double__ != NULL) delete double__;
  double__ = NULL;
}

bool DoubleWrapper::MergePartialFromCodedStream(
    ::google::protobuf::io::CodedInputStream* input) {
#define DO_(EXPRESSION) if (!GOOGLE_PREDICT_TRUE(EXPRESSION)) goto failure
  ::google::protobuf::uint32 tag;
  // @@protoc_insertion_point(parse_start:google.protobuf.testing.DoubleWrapper)
  for (;;) {
    ::std::pair< ::google::protobuf::uint32, bool> p = input->ReadTagWithCutoff(127);
    tag = p.first;
    if (!p.second) goto handle_unusual;
    switch (::google::protobuf::internal::WireFormatLite::GetTagFieldNumber(tag)) {
      // optional .google.protobuf.DoubleValue double = 1;
      case 1: {
        if (tag == 10) {
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessageNoVirtual(
               input, mutable_double_()));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectAtEnd()) goto success;
        break;
      }

      default: {
      handle_unusual:
        if (tag == 0 ||
            ::google::protobuf::internal::WireFormatLite::GetTagWireType(tag) ==
            ::google::protobuf::internal::WireFormatLite::WIRETYPE_END_GROUP) {
          goto success;
        }
        DO_(::google::protobuf::internal::WireFormatLite::SkipField(input, tag));
        break;
      }
    }
  }
success:
  // @@protoc_insertion_point(parse_success:google.protobuf.testing.DoubleWrapper)
  return true;
failure:
  // @@protoc_insertion_point(parse_failure:google.protobuf.testing.DoubleWrapper)
  return false;
#undef DO_
}

void DoubleWrapper::SerializeWithCachedSizes(
    ::google::protobuf::io::CodedOutputStream* output) const {
  // @@protoc_insertion_point(serialize_start:google.protobuf.testing.DoubleWrapper)
  // optional .google.protobuf.DoubleValue double = 1;
  if (this->has_double_()) {
    ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
      1, *this->double__, output);
  }

  // @@protoc_insertion_point(serialize_end:google.protobuf.testing.DoubleWrapper)
}

::google::protobuf::uint8* DoubleWrapper::InternalSerializeWithCachedSizesToArray(
    bool deterministic, ::google::protobuf::uint8* target) const {
  (void)deterministic; // Unused
  // @@protoc_insertion_point(serialize_to_array_start:google.protobuf.testing.DoubleWrapper)
  // optional .google.protobuf.DoubleValue double = 1;
  if (this->has_double_()) {
    target = ::google::protobuf::internal::WireFormatLite::
      InternalWriteMessageNoVirtualToArray(
        1, *this->double__, false, target);
  }

  // @@protoc_insertion_point(serialize_to_array_end:google.protobuf.testing.DoubleWrapper)
  return target;
}

size_t DoubleWrapper::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:google.protobuf.testing.DoubleWrapper)
  size_t total_size = 0;

  // optional .google.protobuf.DoubleValue double = 1;
  if (this->has_double_()) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::MessageSizeNoVirtual(
        *this->double__);
  }

  int cached_size = ::google::protobuf::internal::ToCachedSize(total_size);
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = cached_size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
  return total_size;
}

void DoubleWrapper::MergeFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_merge_from_start:google.protobuf.testing.DoubleWrapper)
  if (GOOGLE_PREDICT_FALSE(&from == this)) MergeFromFail(__LINE__);
  const DoubleWrapper* source =
      ::google::protobuf::internal::DynamicCastToGenerated<const DoubleWrapper>(
          &from);
  if (source == NULL) {
  // @@protoc_insertion_point(generalized_merge_from_cast_fail:google.protobuf.testing.DoubleWrapper)
    ::google::protobuf::internal::ReflectionOps::Merge(from, this);
  } else {
  // @@protoc_insertion_point(generalized_merge_from_cast_success:google.protobuf.testing.DoubleWrapper)
    UnsafeMergeFrom(*source);
  }
}

void DoubleWrapper::MergeFrom(const DoubleWrapper& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:google.protobuf.testing.DoubleWrapper)
  if (GOOGLE_PREDICT_TRUE(&from != this)) {
    UnsafeMergeFrom(from);
  } else {
    MergeFromFail(__LINE__);
  }
}

void DoubleWrapper::UnsafeMergeFrom(const DoubleWrapper& from) {
  GOOGLE_DCHECK(&from != this);
  if (from.has_double_()) {
    mutable_double_()->::google::protobuf::DoubleValue::MergeFrom(from.double_());
  }
}

void DoubleWrapper::CopyFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_copy_from_start:google.protobuf.testing.DoubleWrapper)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

void DoubleWrapper::CopyFrom(const DoubleWrapper& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:google.protobuf.testing.DoubleWrapper)
  if (&from == this) return;
  Clear();
  UnsafeMergeFrom(from);
}

bool DoubleWrapper::IsInitialized() const {

  return true;
}

void DoubleWrapper::Swap(DoubleWrapper* other) {
  if (other == this) return;
  InternalSwap(other);
}
void DoubleWrapper::InternalSwap(DoubleWrapper* other) {
  std::swap(double__, other->double__);
  _internal_metadata_.Swap(&other->_internal_metadata_);
  std::swap(_cached_size_, other->_cached_size_);
}

::google::protobuf::Metadata DoubleWrapper::GetMetadata() const {
  protobuf_AssignDescriptorsOnce();
  ::google::protobuf::Metadata metadata;
  metadata.descriptor = DoubleWrapper_descriptor_;
  metadata.reflection = DoubleWrapper_reflection_;
  return metadata;
}

#if PROTOBUF_INLINE_NOT_IN_HEADERS
// DoubleWrapper

// optional .google.protobuf.DoubleValue double = 1;
bool DoubleWrapper::has_double_() const {
  return this != internal_default_instance() && double__ != NULL;
}
void DoubleWrapper::clear_double_() {
  if (GetArenaNoVirtual() == NULL && double__ != NULL) delete double__;
  double__ = NULL;
}
const ::google::protobuf::DoubleValue& DoubleWrapper::double_() const {
  // @@protoc_insertion_point(field_get:google.protobuf.testing.DoubleWrapper.double)
  return double__ != NULL ? *double__
                         : *::google::protobuf::DoubleValue::internal_default_instance();
}
::google::protobuf::DoubleValue* DoubleWrapper::mutable_double_() {
  
  if (double__ == NULL) {
    double__ = new ::google::protobuf::DoubleValue;
  }
  // @@protoc_insertion_point(field_mutable:google.protobuf.testing.DoubleWrapper.double)
  return double__;
}
::google::protobuf::DoubleValue* DoubleWrapper::release_double_() {
  // @@protoc_insertion_point(field_release:google.protobuf.testing.DoubleWrapper.double)
  
  ::google::protobuf::DoubleValue* temp = double__;
  double__ = NULL;
  return temp;
}
void DoubleWrapper::set_allocated_double_(::google::protobuf::DoubleValue* double_) {
  delete double__;
  if (double_ != NULL && double_->GetArena() != NULL) {
    ::google::protobuf::DoubleValue* new_double_ = new ::google::protobuf::DoubleValue;
    new_double_->CopyFrom(*double_);
    double_ = new_double_;
  }
  double__ = double_;
  if (double_) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_set_allocated:google.protobuf.testing.DoubleWrapper.double)
}

inline const DoubleWrapper* DoubleWrapper::internal_default_instance() {
  return &DoubleWrapper_default_instance_.get();
}
#endif  // PROTOBUF_INLINE_NOT_IN_HEADERS

// ===================================================================

#if !defined(_MSC_VER) || _MSC_VER >= 1900
const int FloatWrapper::kFloatFieldNumber;
#endif  // !defined(_MSC_VER) || _MSC_VER >= 1900

FloatWrapper::FloatWrapper()
  : ::google::protobuf::Message(), _internal_metadata_(NULL) {
  if (this != internal_default_instance()) protobuf_InitDefaults_google_2fprotobuf_2futil_2finternal_2ftestdata_2fwrappers_2eproto();
  SharedCtor();
  // @@protoc_insertion_point(constructor:google.protobuf.testing.FloatWrapper)
}

void FloatWrapper::InitAsDefaultInstance() {
  float__ = const_cast< ::google::protobuf::FloatValue*>(
      ::google::protobuf::FloatValue::internal_default_instance());
}

FloatWrapper::FloatWrapper(const FloatWrapper& from)
  : ::google::protobuf::Message(),
    _internal_metadata_(NULL) {
  SharedCtor();
  UnsafeMergeFrom(from);
  // @@protoc_insertion_point(copy_constructor:google.protobuf.testing.FloatWrapper)
}

void FloatWrapper::SharedCtor() {
  float__ = NULL;
  _cached_size_ = 0;
}

FloatWrapper::~FloatWrapper() {
  // @@protoc_insertion_point(destructor:google.protobuf.testing.FloatWrapper)
  SharedDtor();
}

void FloatWrapper::SharedDtor() {
  if (this != &FloatWrapper_default_instance_.get()) {
    delete float__;
  }
}

void FloatWrapper::SetCachedSize(int size) const {
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
}
const ::google::protobuf::Descriptor* FloatWrapper::descriptor() {
  protobuf_AssignDescriptorsOnce();
  return FloatWrapper_descriptor_;
}

const FloatWrapper& FloatWrapper::default_instance() {
  protobuf_InitDefaults_google_2fprotobuf_2futil_2finternal_2ftestdata_2fwrappers_2eproto();
  return *internal_default_instance();
}

::google::protobuf::internal::ExplicitlyConstructed<FloatWrapper> FloatWrapper_default_instance_;

FloatWrapper* FloatWrapper::New(::google::protobuf::Arena* arena) const {
  FloatWrapper* n = new FloatWrapper;
  if (arena != NULL) {
    arena->Own(n);
  }
  return n;
}

void FloatWrapper::Clear() {
// @@protoc_insertion_point(message_clear_start:google.protobuf.testing.FloatWrapper)
  if (GetArenaNoVirtual() == NULL && float__ != NULL) delete float__;
  float__ = NULL;
}

bool FloatWrapper::MergePartialFromCodedStream(
    ::google::protobuf::io::CodedInputStream* input) {
#define DO_(EXPRESSION) if (!GOOGLE_PREDICT_TRUE(EXPRESSION)) goto failure
  ::google::protobuf::uint32 tag;
  // @@protoc_insertion_point(parse_start:google.protobuf.testing.FloatWrapper)
  for (;;) {
    ::std::pair< ::google::protobuf::uint32, bool> p = input->ReadTagWithCutoff(127);
    tag = p.first;
    if (!p.second) goto handle_unusual;
    switch (::google::protobuf::internal::WireFormatLite::GetTagFieldNumber(tag)) {
      // optional .google.protobuf.FloatValue float = 1;
      case 1: {
        if (tag == 10) {
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessageNoVirtual(
               input, mutable_float_()));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectAtEnd()) goto success;
        break;
      }

      default: {
      handle_unusual:
        if (tag == 0 ||
            ::google::protobuf::internal::WireFormatLite::GetTagWireType(tag) ==
            ::google::protobuf::internal::WireFormatLite::WIRETYPE_END_GROUP) {
          goto success;
        }
        DO_(::google::protobuf::internal::WireFormatLite::SkipField(input, tag));
        break;
      }
    }
  }
success:
  // @@protoc_insertion_point(parse_success:google.protobuf.testing.FloatWrapper)
  return true;
failure:
  // @@protoc_insertion_point(parse_failure:google.protobuf.testing.FloatWrapper)
  return false;
#undef DO_
}

void FloatWrapper::SerializeWithCachedSizes(
    ::google::protobuf::io::CodedOutputStream* output) const {
  // @@protoc_insertion_point(serialize_start:google.protobuf.testing.FloatWrapper)
  // optional .google.protobuf.FloatValue float = 1;
  if (this->has_float_()) {
    ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
      1, *this->float__, output);
  }

  // @@protoc_insertion_point(serialize_end:google.protobuf.testing.FloatWrapper)
}

::google::protobuf::uint8* FloatWrapper::InternalSerializeWithCachedSizesToArray(
    bool deterministic, ::google::protobuf::uint8* target) const {
  (void)deterministic; // Unused
  // @@protoc_insertion_point(serialize_to_array_start:google.protobuf.testing.FloatWrapper)
  // optional .google.protobuf.FloatValue float = 1;
  if (this->has_float_()) {
    target = ::google::protobuf::internal::WireFormatLite::
      InternalWriteMessageNoVirtualToArray(
        1, *this->float__, false, target);
  }

  // @@protoc_insertion_point(serialize_to_array_end:google.protobuf.testing.FloatWrapper)
  return target;
}

size_t FloatWrapper::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:google.protobuf.testing.FloatWrapper)
  size_t total_size = 0;

  // optional .google.protobuf.FloatValue float = 1;
  if (this->has_float_()) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::MessageSizeNoVirtual(
        *this->float__);
  }

  int cached_size = ::google::protobuf::internal::ToCachedSize(total_size);
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = cached_size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
  return total_size;
}

void FloatWrapper::MergeFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_merge_from_start:google.protobuf.testing.FloatWrapper)
  if (GOOGLE_PREDICT_FALSE(&from == this)) MergeFromFail(__LINE__);
  const FloatWrapper* source =
      ::google::protobuf::internal::DynamicCastToGenerated<const FloatWrapper>(
          &from);
  if (source == NULL) {
  // @@protoc_insertion_point(generalized_merge_from_cast_fail:google.protobuf.testing.FloatWrapper)
    ::google::protobuf::internal::ReflectionOps::Merge(from, this);
  } else {
  // @@protoc_insertion_point(generalized_merge_from_cast_success:google.protobuf.testing.FloatWrapper)
    UnsafeMergeFrom(*source);
  }
}

void FloatWrapper::MergeFrom(const FloatWrapper& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:google.protobuf.testing.FloatWrapper)
  if (GOOGLE_PREDICT_TRUE(&from != this)) {
    UnsafeMergeFrom(from);
  } else {
    MergeFromFail(__LINE__);
  }
}

void FloatWrapper::UnsafeMergeFrom(const FloatWrapper& from) {
  GOOGLE_DCHECK(&from != this);
  if (from.has_float_()) {
    mutable_float_()->::google::protobuf::FloatValue::MergeFrom(from.float_());
  }
}

void FloatWrapper::CopyFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_copy_from_start:google.protobuf.testing.FloatWrapper)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

void FloatWrapper::CopyFrom(const FloatWrapper& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:google.protobuf.testing.FloatWrapper)
  if (&from == this) return;
  Clear();
  UnsafeMergeFrom(from);
}

bool FloatWrapper::IsInitialized() const {

  return true;
}

void FloatWrapper::Swap(FloatWrapper* other) {
  if (other == this) return;
  InternalSwap(other);
}
void FloatWrapper::InternalSwap(FloatWrapper* other) {
  std::swap(float__, other->float__);
  _internal_metadata_.Swap(&other->_internal_metadata_);
  std::swap(_cached_size_, other->_cached_size_);
}

::google::protobuf::Metadata FloatWrapper::GetMetadata() const {
  protobuf_AssignDescriptorsOnce();
  ::google::protobuf::Metadata metadata;
  metadata.descriptor = FloatWrapper_descriptor_;
  metadata.reflection = FloatWrapper_reflection_;
  return metadata;
}

#if PROTOBUF_INLINE_NOT_IN_HEADERS
// FloatWrapper

// optional .google.protobuf.FloatValue float = 1;
bool FloatWrapper::has_float_() const {
  return this != internal_default_instance() && float__ != NULL;
}
void FloatWrapper::clear_float_() {
  if (GetArenaNoVirtual() == NULL && float__ != NULL) delete float__;
  float__ = NULL;
}
const ::google::protobuf::FloatValue& FloatWrapper::float_() const {
  // @@protoc_insertion_point(field_get:google.protobuf.testing.FloatWrapper.float)
  return float__ != NULL ? *float__
                         : *::google::protobuf::FloatValue::internal_default_instance();
}
::google::protobuf::FloatValue* FloatWrapper::mutable_float_() {
  
  if (float__ == NULL) {
    float__ = new ::google::protobuf::FloatValue;
  }
  // @@protoc_insertion_point(field_mutable:google.protobuf.testing.FloatWrapper.float)
  return float__;
}
::google::protobuf::FloatValue* FloatWrapper::release_float_() {
  // @@protoc_insertion_point(field_release:google.protobuf.testing.FloatWrapper.float)
  
  ::google::protobuf::FloatValue* temp = float__;
  float__ = NULL;
  return temp;
}
void FloatWrapper::set_allocated_float_(::google::protobuf::FloatValue* float_) {
  delete float__;
  if (float_ != NULL && float_->GetArena() != NULL) {
    ::google::protobuf::FloatValue* new_float_ = new ::google::protobuf::FloatValue;
    new_float_->CopyFrom(*float_);
    float_ = new_float_;
  }
  float__ = float_;
  if (float_) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_set_allocated:google.protobuf.testing.FloatWrapper.float)
}

inline const FloatWrapper* FloatWrapper::internal_default_instance() {
  return &FloatWrapper_default_instance_.get();
}
#endif  // PROTOBUF_INLINE_NOT_IN_HEADERS

// ===================================================================

#if !defined(_MSC_VER) || _MSC_VER >= 1900
const int Int64Wrapper::kInt64FieldNumber;
#endif  // !defined(_MSC_VER) || _MSC_VER >= 1900

Int64Wrapper::Int64Wrapper()
  : ::google::protobuf::Message(), _internal_metadata_(NULL) {
  if (this != internal_default_instance()) protobuf_InitDefaults_google_2fprotobuf_2futil_2finternal_2ftestdata_2fwrappers_2eproto();
  SharedCtor();
  // @@protoc_insertion_point(constructor:google.protobuf.testing.Int64Wrapper)
}

void Int64Wrapper::InitAsDefaultInstance() {
  int64_ = const_cast< ::google::protobuf::Int64Value*>(
      ::google::protobuf::Int64Value::internal_default_instance());
}

Int64Wrapper::Int64Wrapper(const Int64Wrapper& from)
  : ::google::protobuf::Message(),
    _internal_metadata_(NULL) {
  SharedCtor();
  UnsafeMergeFrom(from);
  // @@protoc_insertion_point(copy_constructor:google.protobuf.testing.Int64Wrapper)
}

void Int64Wrapper::SharedCtor() {
  int64_ = NULL;
  _cached_size_ = 0;
}

Int64Wrapper::~Int64Wrapper() {
  // @@protoc_insertion_point(destructor:google.protobuf.testing.Int64Wrapper)
  SharedDtor();
}

void Int64Wrapper::SharedDtor() {
  if (this != &Int64Wrapper_default_instance_.get()) {
    delete int64_;
  }
}

void Int64Wrapper::SetCachedSize(int size) const {
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
}
const ::google::protobuf::Descriptor* Int64Wrapper::descriptor() {
  protobuf_AssignDescriptorsOnce();
  return Int64Wrapper_descriptor_;
}

const Int64Wrapper& Int64Wrapper::default_instance() {
  protobuf_InitDefaults_google_2fprotobuf_2futil_2finternal_2ftestdata_2fwrappers_2eproto();
  return *internal_default_instance();
}

::google::protobuf::internal::ExplicitlyConstructed<Int64Wrapper> Int64Wrapper_default_instance_;

Int64Wrapper* Int64Wrapper::New(::google::protobuf::Arena* arena) const {
  Int64Wrapper* n = new Int64Wrapper;
  if (arena != NULL) {
    arena->Own(n);
  }
  return n;
}

void Int64Wrapper::Clear() {
// @@protoc_insertion_point(message_clear_start:google.protobuf.testing.Int64Wrapper)
  if (GetArenaNoVirtual() == NULL && int64_ != NULL) delete int64_;
  int64_ = NULL;
}

bool Int64Wrapper::MergePartialFromCodedStream(
    ::google::protobuf::io::CodedInputStream* input) {
#define DO_(EXPRESSION) if (!GOOGLE_PREDICT_TRUE(EXPRESSION)) goto failure
  ::google::protobuf::uint32 tag;
  // @@protoc_insertion_point(parse_start:google.protobuf.testing.Int64Wrapper)
  for (;;) {
    ::std::pair< ::google::protobuf::uint32, bool> p = input->ReadTagWithCutoff(127);
    tag = p.first;
    if (!p.second) goto handle_unusual;
    switch (::google::protobuf::internal::WireFormatLite::GetTagFieldNumber(tag)) {
      // optional .google.protobuf.Int64Value int64 = 1;
      case 1: {
        if (tag == 10) {
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessageNoVirtual(
               input, mutable_int64()));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectAtEnd()) goto success;
        break;
      }

      default: {
      handle_unusual:
        if (tag == 0 ||
            ::google::protobuf::internal::WireFormatLite::GetTagWireType(tag) ==
            ::google::protobuf::internal::WireFormatLite::WIRETYPE_END_GROUP) {
          goto success;
        }
        DO_(::google::protobuf::internal::WireFormatLite::SkipField(input, tag));
        break;
      }
    }
  }
success:
  // @@protoc_insertion_point(parse_success:google.protobuf.testing.Int64Wrapper)
  return true;
failure:
  // @@protoc_insertion_point(parse_failure:google.protobuf.testing.Int64Wrapper)
  return false;
#undef DO_
}

void Int64Wrapper::SerializeWithCachedSizes(
    ::google::protobuf::io::CodedOutputStream* output) const {
  // @@protoc_insertion_point(serialize_start:google.protobuf.testing.Int64Wrapper)
  // optional .google.protobuf.Int64Value int64 = 1;
  if (this->has_int64()) {
    ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
      1, *this->int64_, output);
  }

  // @@protoc_insertion_point(serialize_end:google.protobuf.testing.Int64Wrapper)
}

::google::protobuf::uint8* Int64Wrapper::InternalSerializeWithCachedSizesToArray(
    bool deterministic, ::google::protobuf::uint8* target) const {
  (void)deterministic; // Unused
  // @@protoc_insertion_point(serialize_to_array_start:google.protobuf.testing.Int64Wrapper)
  // optional .google.protobuf.Int64Value int64 = 1;
  if (this->has_int64()) {
    target = ::google::protobuf::internal::WireFormatLite::
      InternalWriteMessageNoVirtualToArray(
        1, *this->int64_, false, target);
  }

  // @@protoc_insertion_point(serialize_to_array_end:google.protobuf.testing.Int64Wrapper)
  return target;
}

size_t Int64Wrapper::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:google.protobuf.testing.Int64Wrapper)
  size_t total_size = 0;

  // optional .google.protobuf.Int64Value int64 = 1;
  if (this->has_int64()) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::MessageSizeNoVirtual(
        *this->int64_);
  }

  int cached_size = ::google::protobuf::internal::ToCachedSize(total_size);
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = cached_size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
  return total_size;
}

void Int64Wrapper::MergeFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_merge_from_start:google.protobuf.testing.Int64Wrapper)
  if (GOOGLE_PREDICT_FALSE(&from == this)) MergeFromFail(__LINE__);
  const Int64Wrapper* source =
      ::google::protobuf::internal::DynamicCastToGenerated<const Int64Wrapper>(
          &from);
  if (source == NULL) {
  // @@protoc_insertion_point(generalized_merge_from_cast_fail:google.protobuf.testing.Int64Wrapper)
    ::google::protobuf::internal::ReflectionOps::Merge(from, this);
  } else {
  // @@protoc_insertion_point(generalized_merge_from_cast_success:google.protobuf.testing.Int64Wrapper)
    UnsafeMergeFrom(*source);
  }
}

void Int64Wrapper::MergeFrom(const Int64Wrapper& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:google.protobuf.testing.Int64Wrapper)
  if (GOOGLE_PREDICT_TRUE(&from != this)) {
    UnsafeMergeFrom(from);
  } else {
    MergeFromFail(__LINE__);
  }
}

void Int64Wrapper::UnsafeMergeFrom(const Int64Wrapper& from) {
  GOOGLE_DCHECK(&from != this);
  if (from.has_int64()) {
    mutable_int64()->::google::protobuf::Int64Value::MergeFrom(from.int64());
  }
}

void Int64Wrapper::CopyFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_copy_from_start:google.protobuf.testing.Int64Wrapper)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

void Int64Wrapper::CopyFrom(const Int64Wrapper& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:google.protobuf.testing.Int64Wrapper)
  if (&from == this) return;
  Clear();
  UnsafeMergeFrom(from);
}

bool Int64Wrapper::IsInitialized() const {

  return true;
}

void Int64Wrapper::Swap(Int64Wrapper* other) {
  if (other == this) return;
  InternalSwap(other);
}
void Int64Wrapper::InternalSwap(Int64Wrapper* other) {
  std::swap(int64_, other->int64_);
  _internal_metadata_.Swap(&other->_internal_metadata_);
  std::swap(_cached_size_, other->_cached_size_);
}

::google::protobuf::Metadata Int64Wrapper::GetMetadata() const {
  protobuf_AssignDescriptorsOnce();
  ::google::protobuf::Metadata metadata;
  metadata.descriptor = Int64Wrapper_descriptor_;
  metadata.reflection = Int64Wrapper_reflection_;
  return metadata;
}

#if PROTOBUF_INLINE_NOT_IN_HEADERS
// Int64Wrapper

// optional .google.protobuf.Int64Value int64 = 1;
bool Int64Wrapper::has_int64() const {
  return this != internal_default_instance() && int64_ != NULL;
}
void Int64Wrapper::clear_int64() {
  if (GetArenaNoVirtual() == NULL && int64_ != NULL) delete int64_;
  int64_ = NULL;
}
const ::google::protobuf::Int64Value& Int64Wrapper::int64() const {
  // @@protoc_insertion_point(field_get:google.protobuf.testing.Int64Wrapper.int64)
  return int64_ != NULL ? *int64_
                         : *::google::protobuf::Int64Value::internal_default_instance();
}
::google::protobuf::Int64Value* Int64Wrapper::mutable_int64() {
  
  if (int64_ == NULL) {
    int64_ = new ::google::protobuf::Int64Value;
  }
  // @@protoc_insertion_point(field_mutable:google.protobuf.testing.Int64Wrapper.int64)
  return int64_;
}
::google::protobuf::Int64Value* Int64Wrapper::release_int64() {
  // @@protoc_insertion_point(field_release:google.protobuf.testing.Int64Wrapper.int64)
  
  ::google::protobuf::Int64Value* temp = int64_;
  int64_ = NULL;
  return temp;
}
void Int64Wrapper::set_allocated_int64(::google::protobuf::Int64Value* int64) {
  delete int64_;
  if (int64 != NULL && int64->GetArena() != NULL) {
    ::google::protobuf::Int64Value* new_int64 = new ::google::protobuf::Int64Value;
    new_int64->CopyFrom(*int64);
    int64 = new_int64;
  }
  int64_ = int64;
  if (int64) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_set_allocated:google.protobuf.testing.Int64Wrapper.int64)
}

inline const Int64Wrapper* Int64Wrapper::internal_default_instance() {
  return &Int64Wrapper_default_instance_.get();
}
#endif  // PROTOBUF_INLINE_NOT_IN_HEADERS

// ===================================================================

#if !defined(_MSC_VER) || _MSC_VER >= 1900
const int UInt64Wrapper::kUint64FieldNumber;
#endif  // !defined(_MSC_VER) || _MSC_VER >= 1900

UInt64Wrapper::UInt64Wrapper()
  : ::google::protobuf::Message(), _internal_metadata_(NULL) {
  if (this != internal_default_instance()) protobuf_InitDefaults_google_2fprotobuf_2futil_2finternal_2ftestdata_2fwrappers_2eproto();
  SharedCtor();
  // @@protoc_insertion_point(constructor:google.protobuf.testing.UInt64Wrapper)
}

void UInt64Wrapper::InitAsDefaultInstance() {
  uint64_ = const_cast< ::google::protobuf::UInt64Value*>(
      ::google::protobuf::UInt64Value::internal_default_instance());
}

UInt64Wrapper::UInt64Wrapper(const UInt64Wrapper& from)
  : ::google::protobuf::Message(),
    _internal_metadata_(NULL) {
  SharedCtor();
  UnsafeMergeFrom(from);
  // @@protoc_insertion_point(copy_constructor:google.protobuf.testing.UInt64Wrapper)
}

void UInt64Wrapper::SharedCtor() {
  uint64_ = NULL;
  _cached_size_ = 0;
}

UInt64Wrapper::~UInt64Wrapper() {
  // @@protoc_insertion_point(destructor:google.protobuf.testing.UInt64Wrapper)
  SharedDtor();
}

void UInt64Wrapper::SharedDtor() {
  if (this != &UInt64Wrapper_default_instance_.get()) {
    delete uint64_;
  }
}

void UInt64Wrapper::SetCachedSize(int size) const {
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
}
const ::google::protobuf::Descriptor* UInt64Wrapper::descriptor() {
  protobuf_AssignDescriptorsOnce();
  return UInt64Wrapper_descriptor_;
}

const UInt64Wrapper& UInt64Wrapper::default_instance() {
  protobuf_InitDefaults_google_2fprotobuf_2futil_2finternal_2ftestdata_2fwrappers_2eproto();
  return *internal_default_instance();
}

::google::protobuf::internal::ExplicitlyConstructed<UInt64Wrapper> UInt64Wrapper_default_instance_;

UInt64Wrapper* UInt64Wrapper::New(::google::protobuf::Arena* arena) const {
  UInt64Wrapper* n = new UInt64Wrapper;
  if (arena != NULL) {
    arena->Own(n);
  }
  return n;
}

void UInt64Wrapper::Clear() {
// @@protoc_insertion_point(message_clear_start:google.protobuf.testing.UInt64Wrapper)
  if (GetArenaNoVirtual() == NULL && uint64_ != NULL) delete uint64_;
  uint64_ = NULL;
}

bool UInt64Wrapper::MergePartialFromCodedStream(
    ::google::protobuf::io::CodedInputStream* input) {
#define DO_(EXPRESSION) if (!GOOGLE_PREDICT_TRUE(EXPRESSION)) goto failure
  ::google::protobuf::uint32 tag;
  // @@protoc_insertion_point(parse_start:google.protobuf.testing.UInt64Wrapper)
  for (;;) {
    ::std::pair< ::google::protobuf::uint32, bool> p = input->ReadTagWithCutoff(127);
    tag = p.first;
    if (!p.second) goto handle_unusual;
    switch (::google::protobuf::internal::WireFormatLite::GetTagFieldNumber(tag)) {
      // optional .google.protobuf.UInt64Value uint64 = 1;
      case 1: {
        if (tag == 10) {
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessageNoVirtual(
               input, mutable_uint64()));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectAtEnd()) goto success;
        break;
      }

      default: {
      handle_unusual:
        if (tag == 0 ||
            ::google::protobuf::internal::WireFormatLite::GetTagWireType(tag) ==
            ::google::protobuf::internal::WireFormatLite::WIRETYPE_END_GROUP) {
          goto success;
        }
        DO_(::google::protobuf::internal::WireFormatLite::SkipField(input, tag));
        break;
      }
    }
  }
success:
  // @@protoc_insertion_point(parse_success:google.protobuf.testing.UInt64Wrapper)
  return true;
failure:
  // @@protoc_insertion_point(parse_failure:google.protobuf.testing.UInt64Wrapper)
  return false;
#undef DO_
}

void UInt64Wrapper::SerializeWithCachedSizes(
    ::google::protobuf::io::CodedOutputStream* output) const {
  // @@protoc_insertion_point(serialize_start:google.protobuf.testing.UInt64Wrapper)
  // optional .google.protobuf.UInt64Value uint64 = 1;
  if (this->has_uint64()) {
    ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
      1, *this->uint64_, output);
  }

  // @@protoc_insertion_point(serialize_end:google.protobuf.testing.UInt64Wrapper)
}

::google::protobuf::uint8* UInt64Wrapper::InternalSerializeWithCachedSizesToArray(
    bool deterministic, ::google::protobuf::uint8* target) const {
  (void)deterministic; // Unused
  // @@protoc_insertion_point(serialize_to_array_start:google.protobuf.testing.UInt64Wrapper)
  // optional .google.protobuf.UInt64Value uint64 = 1;
  if (this->has_uint64()) {
    target = ::google::protobuf::internal::WireFormatLite::
      InternalWriteMessageNoVirtualToArray(
        1, *this->uint64_, false, target);
  }

  // @@protoc_insertion_point(serialize_to_array_end:google.protobuf.testing.UInt64Wrapper)
  return target;
}

size_t UInt64Wrapper::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:google.protobuf.testing.UInt64Wrapper)
  size_t total_size = 0;

  // optional .google.protobuf.UInt64Value uint64 = 1;
  if (this->has_uint64()) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::MessageSizeNoVirtual(
        *this->uint64_);
  }

  int cached_size = ::google::protobuf::internal::ToCachedSize(total_size);
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = cached_size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
  return total_size;
}

void UInt64Wrapper::MergeFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_merge_from_start:google.protobuf.testing.UInt64Wrapper)
  if (GOOGLE_PREDICT_FALSE(&from == this)) MergeFromFail(__LINE__);
  const UInt64Wrapper* source =
      ::google::protobuf::internal::DynamicCastToGenerated<const UInt64Wrapper>(
          &from);
  if (source == NULL) {
  // @@protoc_insertion_point(generalized_merge_from_cast_fail:google.protobuf.testing.UInt64Wrapper)
    ::google::protobuf::internal::ReflectionOps::Merge(from, this);
  } else {
  // @@protoc_insertion_point(generalized_merge_from_cast_success:google.protobuf.testing.UInt64Wrapper)
    UnsafeMergeFrom(*source);
  }
}

void UInt64Wrapper::MergeFrom(const UInt64Wrapper& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:google.protobuf.testing.UInt64Wrapper)
  if (GOOGLE_PREDICT_TRUE(&from != this)) {
    UnsafeMergeFrom(from);
  } else {
    MergeFromFail(__LINE__);
  }
}

void UInt64Wrapper::UnsafeMergeFrom(const UInt64Wrapper& from) {
  GOOGLE_DCHECK(&from != this);
  if (from.has_uint64()) {
    mutable_uint64()->::google::protobuf::UInt64Value::MergeFrom(from.uint64());
  }
}

void UInt64Wrapper::CopyFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_copy_from_start:google.protobuf.testing.UInt64Wrapper)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

void UInt64Wrapper::CopyFrom(const UInt64Wrapper& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:google.protobuf.testing.UInt64Wrapper)
  if (&from == this) return;
  Clear();
  UnsafeMergeFrom(from);
}

bool UInt64Wrapper::IsInitialized() const {

  return true;
}

void UInt64Wrapper::Swap(UInt64Wrapper* other) {
  if (other == this) return;
  InternalSwap(other);
}
void UInt64Wrapper::InternalSwap(UInt64Wrapper* other) {
  std::swap(uint64_, other->uint64_);
  _internal_metadata_.Swap(&other->_internal_metadata_);
  std::swap(_cached_size_, other->_cached_size_);
}

::google::protobuf::Metadata UInt64Wrapper::GetMetadata() const {
  protobuf_AssignDescriptorsOnce();
  ::google::protobuf::Metadata metadata;
  metadata.descriptor = UInt64Wrapper_descriptor_;
  metadata.reflection = UInt64Wrapper_reflection_;
  return metadata;
}

#if PROTOBUF_INLINE_NOT_IN_HEADERS
// UInt64Wrapper

// optional .google.protobuf.UInt64Value uint64 = 1;
bool UInt64Wrapper::has_uint64() const {
  return this != internal_default_instance() && uint64_ != NULL;
}
void UInt64Wrapper::clear_uint64() {
  if (GetArenaNoVirtual() == NULL && uint64_ != NULL) delete uint64_;
  uint64_ = NULL;
}
const ::google::protobuf::UInt64Value& UInt64Wrapper::uint64() const {
  // @@protoc_insertion_point(field_get:google.protobuf.testing.UInt64Wrapper.uint64)
  return uint64_ != NULL ? *uint64_
                         : *::google::protobuf::UInt64Value::internal_default_instance();
}
::google::protobuf::UInt64Value* UInt64Wrapper::mutable_uint64() {
  
  if (uint64_ == NULL) {
    uint64_ = new ::google::protobuf::UInt64Value;
  }
  // @@protoc_insertion_point(field_mutable:google.protobuf.testing.UInt64Wrapper.uint64)
  return uint64_;
}
::google::protobuf::UInt64Value* UInt64Wrapper::release_uint64() {
  // @@protoc_insertion_point(field_release:google.protobuf.testing.UInt64Wrapper.uint64)
  
  ::google::protobuf::UInt64Value* temp = uint64_;
  uint64_ = NULL;
  return temp;
}
void UInt64Wrapper::set_allocated_uint64(::google::protobuf::UInt64Value* uint64) {
  delete uint64_;
  if (uint64 != NULL && uint64->GetArena() != NULL) {
    ::google::protobuf::UInt64Value* new_uint64 = new ::google::protobuf::UInt64Value;
    new_uint64->CopyFrom(*uint64);
    uint64 = new_uint64;
  }
  uint64_ = uint64;
  if (uint64) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_set_allocated:google.protobuf.testing.UInt64Wrapper.uint64)
}

inline const UInt64Wrapper* UInt64Wrapper::internal_default_instance() {
  return &UInt64Wrapper_default_instance_.get();
}
#endif  // PROTOBUF_INLINE_NOT_IN_HEADERS

// ===================================================================

#if !defined(_MSC_VER) || _MSC_VER >= 1900
const int Int32Wrapper::kInt32FieldNumber;
#endif  // !defined(_MSC_VER) || _MSC_VER >= 1900

Int32Wrapper::Int32Wrapper()
  : ::google::protobuf::Message(), _internal_metadata_(NULL) {
  if (this != internal_default_instance()) protobuf_InitDefaults_google_2fprotobuf_2futil_2finternal_2ftestdata_2fwrappers_2eproto();
  SharedCtor();
  // @@protoc_insertion_point(constructor:google.protobuf.testing.Int32Wrapper)
}

void Int32Wrapper::InitAsDefaultInstance() {
  int32_ = const_cast< ::google::protobuf::Int32Value*>(
      ::google::protobuf::Int32Value::internal_default_instance());
}

Int32Wrapper::Int32Wrapper(const Int32Wrapper& from)
  : ::google::protobuf::Message(),
    _internal_metadata_(NULL) {
  SharedCtor();
  UnsafeMergeFrom(from);
  // @@protoc_insertion_point(copy_constructor:google.protobuf.testing.Int32Wrapper)
}

void Int32Wrapper::SharedCtor() {
  int32_ = NULL;
  _cached_size_ = 0;
}

Int32Wrapper::~Int32Wrapper() {
  // @@protoc_insertion_point(destructor:google.protobuf.testing.Int32Wrapper)
  SharedDtor();
}

void Int32Wrapper::SharedDtor() {
  if (this != &Int32Wrapper_default_instance_.get()) {
    delete int32_;
  }
}

void Int32Wrapper::SetCachedSize(int size) const {
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
}
const ::google::protobuf::Descriptor* Int32Wrapper::descriptor() {
  protobuf_AssignDescriptorsOnce();
  return Int32Wrapper_descriptor_;
}

const Int32Wrapper& Int32Wrapper::default_instance() {
  protobuf_InitDefaults_google_2fprotobuf_2futil_2finternal_2ftestdata_2fwrappers_2eproto();
  return *internal_default_instance();
}

::google::protobuf::internal::ExplicitlyConstructed<Int32Wrapper> Int32Wrapper_default_instance_;

Int32Wrapper* Int32Wrapper::New(::google::protobuf::Arena* arena) const {
  Int32Wrapper* n = new Int32Wrapper;
  if (arena != NULL) {
    arena->Own(n);
  }
  return n;
}

void Int32Wrapper::Clear() {
// @@protoc_insertion_point(message_clear_start:google.protobuf.testing.Int32Wrapper)
  if (GetArenaNoVirtual() == NULL && int32_ != NULL) delete int32_;
  int32_ = NULL;
}

bool Int32Wrapper::MergePartialFromCodedStream(
    ::google::protobuf::io::CodedInputStream* input) {
#define DO_(EXPRESSION) if (!GOOGLE_PREDICT_TRUE(EXPRESSION)) goto failure
  ::google::protobuf::uint32 tag;
  // @@protoc_insertion_point(parse_start:google.protobuf.testing.Int32Wrapper)
  for (;;) {
    ::std::pair< ::google::protobuf::uint32, bool> p = input->ReadTagWithCutoff(127);
    tag = p.first;
    if (!p.second) goto handle_unusual;
    switch (::google::protobuf::internal::WireFormatLite::GetTagFieldNumber(tag)) {
      // optional .google.protobuf.Int32Value int32 = 1;
      case 1: {
        if (tag == 10) {
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessageNoVirtual(
               input, mutable_int32()));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectAtEnd()) goto success;
        break;
      }

      default: {
      handle_unusual:
        if (tag == 0 ||
            ::google::protobuf::internal::WireFormatLite::GetTagWireType(tag) ==
            ::google::protobuf::internal::WireFormatLite::WIRETYPE_END_GROUP) {
          goto success;
        }
        DO_(::google::protobuf::internal::WireFormatLite::SkipField(input, tag));
        break;
      }
    }
  }
success:
  // @@protoc_insertion_point(parse_success:google.protobuf.testing.Int32Wrapper)
  return true;
failure:
  // @@protoc_insertion_point(parse_failure:google.protobuf.testing.Int32Wrapper)
  return false;
#undef DO_
}

void Int32Wrapper::SerializeWithCachedSizes(
    ::google::protobuf::io::CodedOutputStream* output) const {
  // @@protoc_insertion_point(serialize_start:google.protobuf.testing.Int32Wrapper)
  // optional .google.protobuf.Int32Value int32 = 1;
  if (this->has_int32()) {
    ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
      1, *this->int32_, output);
  }

  // @@protoc_insertion_point(serialize_end:google.protobuf.testing.Int32Wrapper)
}

::google::protobuf::uint8* Int32Wrapper::InternalSerializeWithCachedSizesToArray(
    bool deterministic, ::google::protobuf::uint8* target) const {
  (void)deterministic; // Unused
  // @@protoc_insertion_point(serialize_to_array_start:google.protobuf.testing.Int32Wrapper)
  // optional .google.protobuf.Int32Value int32 = 1;
  if (this->has_int32()) {
    target = ::google::protobuf::internal::WireFormatLite::
      InternalWriteMessageNoVirtualToArray(
        1, *this->int32_, false, target);
  }

  // @@protoc_insertion_point(serialize_to_array_end:google.protobuf.testing.Int32Wrapper)
  return target;
}

size_t Int32Wrapper::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:google.protobuf.testing.Int32Wrapper)
  size_t total_size = 0;

  // optional .google.protobuf.Int32Value int32 = 1;
  if (this->has_int32()) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::MessageSizeNoVirtual(
        *this->int32_);
  }

  int cached_size = ::google::protobuf::internal::ToCachedSize(total_size);
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = cached_size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
  return total_size;
}

void Int32Wrapper::MergeFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_merge_from_start:google.protobuf.testing.Int32Wrapper)
  if (GOOGLE_PREDICT_FALSE(&from == this)) MergeFromFail(__LINE__);
  const Int32Wrapper* source =
      ::google::protobuf::internal::DynamicCastToGenerated<const Int32Wrapper>(
          &from);
  if (source == NULL) {
  // @@protoc_insertion_point(generalized_merge_from_cast_fail:google.protobuf.testing.Int32Wrapper)
    ::google::protobuf::internal::ReflectionOps::Merge(from, this);
  } else {
  // @@protoc_insertion_point(generalized_merge_from_cast_success:google.protobuf.testing.Int32Wrapper)
    UnsafeMergeFrom(*source);
  }
}

void Int32Wrapper::MergeFrom(const Int32Wrapper& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:google.protobuf.testing.Int32Wrapper)
  if (GOOGLE_PREDICT_TRUE(&from != this)) {
    UnsafeMergeFrom(from);
  } else {
    MergeFromFail(__LINE__);
  }
}

void Int32Wrapper::UnsafeMergeFrom(const Int32Wrapper& from) {
  GOOGLE_DCHECK(&from != this);
  if (from.has_int32()) {
    mutable_int32()->::google::protobuf::Int32Value::MergeFrom(from.int32());
  }
}

void Int32Wrapper::CopyFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_copy_from_start:google.protobuf.testing.Int32Wrapper)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

void Int32Wrapper::CopyFrom(const Int32Wrapper& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:google.protobuf.testing.Int32Wrapper)
  if (&from == this) return;
  Clear();
  UnsafeMergeFrom(from);
}

bool Int32Wrapper::IsInitialized() const {

  return true;
}

void Int32Wrapper::Swap(Int32Wrapper* other) {
  if (other == this) return;
  InternalSwap(other);
}
void Int32Wrapper::InternalSwap(Int32Wrapper* other) {
  std::swap(int32_, other->int32_);
  _internal_metadata_.Swap(&other->_internal_metadata_);
  std::swap(_cached_size_, other->_cached_size_);
}

::google::protobuf::Metadata Int32Wrapper::GetMetadata() const {
  protobuf_AssignDescriptorsOnce();
  ::google::protobuf::Metadata metadata;
  metadata.descriptor = Int32Wrapper_descriptor_;
  metadata.reflection = Int32Wrapper_reflection_;
  return metadata;
}

#if PROTOBUF_INLINE_NOT_IN_HEADERS
// Int32Wrapper

// optional .google.protobuf.Int32Value int32 = 1;
bool Int32Wrapper::has_int32() const {
  return this != internal_default_instance() && int32_ != NULL;
}
void Int32Wrapper::clear_int32() {
  if (GetArenaNoVirtual() == NULL && int32_ != NULL) delete int32_;
  int32_ = NULL;
}
const ::google::protobuf::Int32Value& Int32Wrapper::int32() const {
  // @@protoc_insertion_point(field_get:google.protobuf.testing.Int32Wrapper.int32)
  return int32_ != NULL ? *int32_
                         : *::google::protobuf::Int32Value::internal_default_instance();
}
::google::protobuf::Int32Value* Int32Wrapper::mutable_int32() {
  
  if (int32_ == NULL) {
    int32_ = new ::google::protobuf::Int32Value;
  }
  // @@protoc_insertion_point(field_mutable:google.protobuf.testing.Int32Wrapper.int32)
  return int32_;
}
::google::protobuf::Int32Value* Int32Wrapper::release_int32() {
  // @@protoc_insertion_point(field_release:google.protobuf.testing.Int32Wrapper.int32)
  
  ::google::protobuf::Int32Value* temp = int32_;
  int32_ = NULL;
  return temp;
}
void Int32Wrapper::set_allocated_int32(::google::protobuf::Int32Value* int32) {
  delete int32_;
  if (int32 != NULL && int32->GetArena() != NULL) {
    ::google::protobuf::Int32Value* new_int32 = new ::google::protobuf::Int32Value;
    new_int32->CopyFrom(*int32);
    int32 = new_int32;
  }
  int32_ = int32;
  if (int32) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_set_allocated:google.protobuf.testing.Int32Wrapper.int32)
}

inline const Int32Wrapper* Int32Wrapper::internal_default_instance() {
  return &Int32Wrapper_default_instance_.get();
}
#endif  // PROTOBUF_INLINE_NOT_IN_HEADERS

// ===================================================================

#if !defined(_MSC_VER) || _MSC_VER >= 1900
const int UInt32Wrapper::kUint32FieldNumber;
#endif  // !defined(_MSC_VER) || _MSC_VER >= 1900

UInt32Wrapper::UInt32Wrapper()
  : ::google::protobuf::Message(), _internal_metadata_(NULL) {
  if (this != internal_default_instance()) protobuf_InitDefaults_google_2fprotobuf_2futil_2finternal_2ftestdata_2fwrappers_2eproto();
  SharedCtor();
  // @@protoc_insertion_point(constructor:google.protobuf.testing.UInt32Wrapper)
}

void UInt32Wrapper::InitAsDefaultInstance() {
  uint32_ = const_cast< ::google::protobuf::UInt32Value*>(
      ::google::protobuf::UInt32Value::internal_default_instance());
}

UInt32Wrapper::UInt32Wrapper(const UInt32Wrapper& from)
  : ::google::protobuf::Message(),
    _internal_metadata_(NULL) {
  SharedCtor();
  UnsafeMergeFrom(from);
  // @@protoc_insertion_point(copy_constructor:google.protobuf.testing.UInt32Wrapper)
}

void UInt32Wrapper::SharedCtor() {
  uint32_ = NULL;
  _cached_size_ = 0;
}

UInt32Wrapper::~UInt32Wrapper() {
  // @@protoc_insertion_point(destructor:google.protobuf.testing.UInt32Wrapper)
  SharedDtor();
}

void UInt32Wrapper::SharedDtor() {
  if (this != &UInt32Wrapper_default_instance_.get()) {
    delete uint32_;
  }
}

void UInt32Wrapper::SetCachedSize(int size) const {
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
}
const ::google::protobuf::Descriptor* UInt32Wrapper::descriptor() {
  protobuf_AssignDescriptorsOnce();
  return UInt32Wrapper_descriptor_;
}

const UInt32Wrapper& UInt32Wrapper::default_instance() {
  protobuf_InitDefaults_google_2fprotobuf_2futil_2finternal_2ftestdata_2fwrappers_2eproto();
  return *internal_default_instance();
}

::google::protobuf::internal::ExplicitlyConstructed<UInt32Wrapper> UInt32Wrapper_default_instance_;

UInt32Wrapper* UInt32Wrapper::New(::google::protobuf::Arena* arena) const {
  UInt32Wrapper* n = new UInt32Wrapper;
  if (arena != NULL) {
    arena->Own(n);
  }
  return n;
}

void UInt32Wrapper::Clear() {
// @@protoc_insertion_point(message_clear_start:google.protobuf.testing.UInt32Wrapper)
  if (GetArenaNoVirtual() == NULL && uint32_ != NULL) delete uint32_;
  uint32_ = NULL;
}

bool UInt32Wrapper::MergePartialFromCodedStream(
    ::google::protobuf::io::CodedInputStream* input) {
#define DO_(EXPRESSION) if (!GOOGLE_PREDICT_TRUE(EXPRESSION)) goto failure
  ::google::protobuf::uint32 tag;
  // @@protoc_insertion_point(parse_start:google.protobuf.testing.UInt32Wrapper)
  for (;;) {
    ::std::pair< ::google::protobuf::uint32, bool> p = input->ReadTagWithCutoff(127);
    tag = p.first;
    if (!p.second) goto handle_unusual;
    switch (::google::protobuf::internal::WireFormatLite::GetTagFieldNumber(tag)) {
      // optional .google.protobuf.UInt32Value uint32 = 1;
      case 1: {
        if (tag == 10) {
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessageNoVirtual(
               input, mutable_uint32()));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectAtEnd()) goto success;
        break;
      }

      default: {
      handle_unusual:
        if (tag == 0 ||
            ::google::protobuf::internal::WireFormatLite::GetTagWireType(tag) ==
            ::google::protobuf::internal::WireFormatLite::WIRETYPE_END_GROUP) {
          goto success;
        }
        DO_(::google::protobuf::internal::WireFormatLite::SkipField(input, tag));
        break;
      }
    }
  }
success:
  // @@protoc_insertion_point(parse_success:google.protobuf.testing.UInt32Wrapper)
  return true;
failure:
  // @@protoc_insertion_point(parse_failure:google.protobuf.testing.UInt32Wrapper)
  return false;
#undef DO_
}

void UInt32Wrapper::SerializeWithCachedSizes(
    ::google::protobuf::io::CodedOutputStream* output) const {
  // @@protoc_insertion_point(serialize_start:google.protobuf.testing.UInt32Wrapper)
  // optional .google.protobuf.UInt32Value uint32 = 1;
  if (this->has_uint32()) {
    ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
      1, *this->uint32_, output);
  }

  // @@protoc_insertion_point(serialize_end:google.protobuf.testing.UInt32Wrapper)
}

::google::protobuf::uint8* UInt32Wrapper::InternalSerializeWithCachedSizesToArray(
    bool deterministic, ::google::protobuf::uint8* target) const {
  (void)deterministic; // Unused
  // @@protoc_insertion_point(serialize_to_array_start:google.protobuf.testing.UInt32Wrapper)
  // optional .google.protobuf.UInt32Value uint32 = 1;
  if (this->has_uint32()) {
    target = ::google::protobuf::internal::WireFormatLite::
      InternalWriteMessageNoVirtualToArray(
        1, *this->uint32_, false, target);
  }

  // @@protoc_insertion_point(serialize_to_array_end:google.protobuf.testing.UInt32Wrapper)
  return target;
}

size_t UInt32Wrapper::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:google.protobuf.testing.UInt32Wrapper)
  size_t total_size = 0;

  // optional .google.protobuf.UInt32Value uint32 = 1;
  if (this->has_uint32()) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::MessageSizeNoVirtual(
        *this->uint32_);
  }

  int cached_size = ::google::protobuf::internal::ToCachedSize(total_size);
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = cached_size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
  return total_size;
}

void UInt32Wrapper::MergeFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_merge_from_start:google.protobuf.testing.UInt32Wrapper)
  if (GOOGLE_PREDICT_FALSE(&from == this)) MergeFromFail(__LINE__);
  const UInt32Wrapper* source =
      ::google::protobuf::internal::DynamicCastToGenerated<const UInt32Wrapper>(
          &from);
  if (source == NULL) {
  // @@protoc_insertion_point(generalized_merge_from_cast_fail:google.protobuf.testing.UInt32Wrapper)
    ::google::protobuf::internal::ReflectionOps::Merge(from, this);
  } else {
  // @@protoc_insertion_point(generalized_merge_from_cast_success:google.protobuf.testing.UInt32Wrapper)
    UnsafeMergeFrom(*source);
  }
}

void UInt32Wrapper::MergeFrom(const UInt32Wrapper& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:google.protobuf.testing.UInt32Wrapper)
  if (GOOGLE_PREDICT_TRUE(&from != this)) {
    UnsafeMergeFrom(from);
  } else {
    MergeFromFail(__LINE__);
  }
}

void UInt32Wrapper::UnsafeMergeFrom(const UInt32Wrapper& from) {
  GOOGLE_DCHECK(&from != this);
  if (from.has_uint32()) {
    mutable_uint32()->::google::protobuf::UInt32Value::MergeFrom(from.uint32());
  }
}

void UInt32Wrapper::CopyFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_copy_from_start:google.protobuf.testing.UInt32Wrapper)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

void UInt32Wrapper::CopyFrom(const UInt32Wrapper& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:google.protobuf.testing.UInt32Wrapper)
  if (&from == this) return;
  Clear();
  UnsafeMergeFrom(from);
}

bool UInt32Wrapper::IsInitialized() const {

  return true;
}

void UInt32Wrapper::Swap(UInt32Wrapper* other) {
  if (other == this) return;
  InternalSwap(other);
}
void UInt32Wrapper::InternalSwap(UInt32Wrapper* other) {
  std::swap(uint32_, other->uint32_);
  _internal_metadata_.Swap(&other->_internal_metadata_);
  std::swap(_cached_size_, other->_cached_size_);
}

::google::protobuf::Metadata UInt32Wrapper::GetMetadata() const {
  protobuf_AssignDescriptorsOnce();
  ::google::protobuf::Metadata metadata;
  metadata.descriptor = UInt32Wrapper_descriptor_;
  metadata.reflection = UInt32Wrapper_reflection_;
  return metadata;
}

#if PROTOBUF_INLINE_NOT_IN_HEADERS
// UInt32Wrapper

// optional .google.protobuf.UInt32Value uint32 = 1;
bool UInt32Wrapper::has_uint32() const {
  return this != internal_default_instance() && uint32_ != NULL;
}
void UInt32Wrapper::clear_uint32() {
  if (GetArenaNoVirtual() == NULL && uint32_ != NULL) delete uint32_;
  uint32_ = NULL;
}
const ::google::protobuf::UInt32Value& UInt32Wrapper::uint32() const {
  // @@protoc_insertion_point(field_get:google.protobuf.testing.UInt32Wrapper.uint32)
  return uint32_ != NULL ? *uint32_
                         : *::google::protobuf::UInt32Value::internal_default_instance();
}
::google::protobuf::UInt32Value* UInt32Wrapper::mutable_uint32() {
  
  if (uint32_ == NULL) {
    uint32_ = new ::google::protobuf::UInt32Value;
  }
  // @@protoc_insertion_point(field_mutable:google.protobuf.testing.UInt32Wrapper.uint32)
  return uint32_;
}
::google::protobuf::UInt32Value* UInt32Wrapper::release_uint32() {
  // @@protoc_insertion_point(field_release:google.protobuf.testing.UInt32Wrapper.uint32)
  
  ::google::protobuf::UInt32Value* temp = uint32_;
  uint32_ = NULL;
  return temp;
}
void UInt32Wrapper::set_allocated_uint32(::google::protobuf::UInt32Value* uint32) {
  delete uint32_;
  if (uint32 != NULL && uint32->GetArena() != NULL) {
    ::google::protobuf::UInt32Value* new_uint32 = new ::google::protobuf::UInt32Value;
    new_uint32->CopyFrom(*uint32);
    uint32 = new_uint32;
  }
  uint32_ = uint32;
  if (uint32) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_set_allocated:google.protobuf.testing.UInt32Wrapper.uint32)
}

inline const UInt32Wrapper* UInt32Wrapper::internal_default_instance() {
  return &UInt32Wrapper_default_instance_.get();
}
#endif  // PROTOBUF_INLINE_NOT_IN_HEADERS

// ===================================================================

#if !defined(_MSC_VER) || _MSC_VER >= 1900
const int BoolWrapper::kBoolFieldNumber;
#endif  // !defined(_MSC_VER) || _MSC_VER >= 1900

BoolWrapper::BoolWrapper()
  : ::google::protobuf::Message(), _internal_metadata_(NULL) {
  if (this != internal_default_instance()) protobuf_InitDefaults_google_2fprotobuf_2futil_2finternal_2ftestdata_2fwrappers_2eproto();
  SharedCtor();
  // @@protoc_insertion_point(constructor:google.protobuf.testing.BoolWrapper)
}

void BoolWrapper::InitAsDefaultInstance() {
  bool__ = const_cast< ::google::protobuf::BoolValue*>(
      ::google::protobuf::BoolValue::internal_default_instance());
}

BoolWrapper::BoolWrapper(const BoolWrapper& from)
  : ::google::protobuf::Message(),
    _internal_metadata_(NULL) {
  SharedCtor();
  UnsafeMergeFrom(from);
  // @@protoc_insertion_point(copy_constructor:google.protobuf.testing.BoolWrapper)
}

void BoolWrapper::SharedCtor() {
  bool__ = NULL;
  _cached_size_ = 0;
}

BoolWrapper::~BoolWrapper() {
  // @@protoc_insertion_point(destructor:google.protobuf.testing.BoolWrapper)
  SharedDtor();
}

void BoolWrapper::SharedDtor() {
  if (this != &BoolWrapper_default_instance_.get()) {
    delete bool__;
  }
}

void BoolWrapper::SetCachedSize(int size) const {
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
}
const ::google::protobuf::Descriptor* BoolWrapper::descriptor() {
  protobuf_AssignDescriptorsOnce();
  return BoolWrapper_descriptor_;
}

const BoolWrapper& BoolWrapper::default_instance() {
  protobuf_InitDefaults_google_2fprotobuf_2futil_2finternal_2ftestdata_2fwrappers_2eproto();
  return *internal_default_instance();
}

::google::protobuf::internal::ExplicitlyConstructed<BoolWrapper> BoolWrapper_default_instance_;

BoolWrapper* BoolWrapper::New(::google::protobuf::Arena* arena) const {
  BoolWrapper* n = new BoolWrapper;
  if (arena != NULL) {
    arena->Own(n);
  }
  return n;
}

void BoolWrapper::Clear() {
// @@protoc_insertion_point(message_clear_start:google.protobuf.testing.BoolWrapper)
  if (GetArenaNoVirtual() == NULL && bool__ != NULL) delete bool__;
  bool__ = NULL;
}

bool BoolWrapper::MergePartialFromCodedStream(
    ::google::protobuf::io::CodedInputStream* input) {
#define DO_(EXPRESSION) if (!GOOGLE_PREDICT_TRUE(EXPRESSION)) goto failure
  ::google::protobuf::uint32 tag;
  // @@protoc_insertion_point(parse_start:google.protobuf.testing.BoolWrapper)
  for (;;) {
    ::std::pair< ::google::protobuf::uint32, bool> p = input->ReadTagWithCutoff(127);
    tag = p.first;
    if (!p.second) goto handle_unusual;
    switch (::google::protobuf::internal::WireFormatLite::GetTagFieldNumber(tag)) {
      // optional .google.protobuf.BoolValue bool = 1;
      case 1: {
        if (tag == 10) {
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessageNoVirtual(
               input, mutable_bool_()));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectAtEnd()) goto success;
        break;
      }

      default: {
      handle_unusual:
        if (tag == 0 ||
            ::google::protobuf::internal::WireFormatLite::GetTagWireType(tag) ==
            ::google::protobuf::internal::WireFormatLite::WIRETYPE_END_GROUP) {
          goto success;
        }
        DO_(::google::protobuf::internal::WireFormatLite::SkipField(input, tag));
        break;
      }
    }
  }
success:
  // @@protoc_insertion_point(parse_success:google.protobuf.testing.BoolWrapper)
  return true;
failure:
  // @@protoc_insertion_point(parse_failure:google.protobuf.testing.BoolWrapper)
  return false;
#undef DO_
}

void BoolWrapper::SerializeWithCachedSizes(
    ::google::protobuf::io::CodedOutputStream* output) const {
  // @@protoc_insertion_point(serialize_start:google.protobuf.testing.BoolWrapper)
  // optional .google.protobuf.BoolValue bool = 1;
  if (this->has_bool_()) {
    ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
      1, *this->bool__, output);
  }

  // @@protoc_insertion_point(serialize_end:google.protobuf.testing.BoolWrapper)
}

::google::protobuf::uint8* BoolWrapper::InternalSerializeWithCachedSizesToArray(
    bool deterministic, ::google::protobuf::uint8* target) const {
  (void)deterministic; // Unused
  // @@protoc_insertion_point(serialize_to_array_start:google.protobuf.testing.BoolWrapper)
  // optional .google.protobuf.BoolValue bool = 1;
  if (this->has_bool_()) {
    target = ::google::protobuf::internal::WireFormatLite::
      InternalWriteMessageNoVirtualToArray(
        1, *this->bool__, false, target);
  }

  // @@protoc_insertion_point(serialize_to_array_end:google.protobuf.testing.BoolWrapper)
  return target;
}

size_t BoolWrapper::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:google.protobuf.testing.BoolWrapper)
  size_t total_size = 0;

  // optional .google.protobuf.BoolValue bool = 1;
  if (this->has_bool_()) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::MessageSizeNoVirtual(
        *this->bool__);
  }

  int cached_size = ::google::protobuf::internal::ToCachedSize(total_size);
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = cached_size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
  return total_size;
}

void BoolWrapper::MergeFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_merge_from_start:google.protobuf.testing.BoolWrapper)
  if (GOOGLE_PREDICT_FALSE(&from == this)) MergeFromFail(__LINE__);
  const BoolWrapper* source =
      ::google::protobuf::internal::DynamicCastToGenerated<const BoolWrapper>(
          &from);
  if (source == NULL) {
  // @@protoc_insertion_point(generalized_merge_from_cast_fail:google.protobuf.testing.BoolWrapper)
    ::google::protobuf::internal::ReflectionOps::Merge(from, this);
  } else {
  // @@protoc_insertion_point(generalized_merge_from_cast_success:google.protobuf.testing.BoolWrapper)
    UnsafeMergeFrom(*source);
  }
}

void BoolWrapper::MergeFrom(const BoolWrapper& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:google.protobuf.testing.BoolWrapper)
  if (GOOGLE_PREDICT_TRUE(&from != this)) {
    UnsafeMergeFrom(from);
  } else {
    MergeFromFail(__LINE__);
  }
}

void BoolWrapper::UnsafeMergeFrom(const BoolWrapper& from) {
  GOOGLE_DCHECK(&from != this);
  if (from.has_bool_()) {
    mutable_bool_()->::google::protobuf::BoolValue::MergeFrom(from.bool_());
  }
}

void BoolWrapper::CopyFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_copy_from_start:google.protobuf.testing.BoolWrapper)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

void BoolWrapper::CopyFrom(const BoolWrapper& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:google.protobuf.testing.BoolWrapper)
  if (&from == this) return;
  Clear();
  UnsafeMergeFrom(from);
}

bool BoolWrapper::IsInitialized() const {

  return true;
}

void BoolWrapper::Swap(BoolWrapper* other) {
  if (other == this) return;
  InternalSwap(other);
}
void BoolWrapper::InternalSwap(BoolWrapper* other) {
  std::swap(bool__, other->bool__);
  _internal_metadata_.Swap(&other->_internal_metadata_);
  std::swap(_cached_size_, other->_cached_size_);
}

::google::protobuf::Metadata BoolWrapper::GetMetadata() const {
  protobuf_AssignDescriptorsOnce();
  ::google::protobuf::Metadata metadata;
  metadata.descriptor = BoolWrapper_descriptor_;
  metadata.reflection = BoolWrapper_reflection_;
  return metadata;
}

#if PROTOBUF_INLINE_NOT_IN_HEADERS
// BoolWrapper

// optional .google.protobuf.BoolValue bool = 1;
bool BoolWrapper::has_bool_() const {
  return this != internal_default_instance() && bool__ != NULL;
}
void BoolWrapper::clear_bool_() {
  if (GetArenaNoVirtual() == NULL && bool__ != NULL) delete bool__;
  bool__ = NULL;
}
const ::google::protobuf::BoolValue& BoolWrapper::bool_() const {
  // @@protoc_insertion_point(field_get:google.protobuf.testing.BoolWrapper.bool)
  return bool__ != NULL ? *bool__
                         : *::google::protobuf::BoolValue::internal_default_instance();
}
::google::protobuf::BoolValue* BoolWrapper::mutable_bool_() {
  
  if (bool__ == NULL) {
    bool__ = new ::google::protobuf::BoolValue;
  }
  // @@protoc_insertion_point(field_mutable:google.protobuf.testing.BoolWrapper.bool)
  return bool__;
}
::google::protobuf::BoolValue* BoolWrapper::release_bool_() {
  // @@protoc_insertion_point(field_release:google.protobuf.testing.BoolWrapper.bool)
  
  ::google::protobuf::BoolValue* temp = bool__;
  bool__ = NULL;
  return temp;
}
void BoolWrapper::set_allocated_bool_(::google::protobuf::BoolValue* bool_) {
  delete bool__;
  if (bool_ != NULL && bool_->GetArena() != NULL) {
    ::google::protobuf::BoolValue* new_bool_ = new ::google::protobuf::BoolValue;
    new_bool_->CopyFrom(*bool_);
    bool_ = new_bool_;
  }
  bool__ = bool_;
  if (bool_) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_set_allocated:google.protobuf.testing.BoolWrapper.bool)
}

inline const BoolWrapper* BoolWrapper::internal_default_instance() {
  return &BoolWrapper_default_instance_.get();
}
#endif  // PROTOBUF_INLINE_NOT_IN_HEADERS

// ===================================================================

#if !defined(_MSC_VER) || _MSC_VER >= 1900
const int StringWrapper::kStringFieldNumber;
#endif  // !defined(_MSC_VER) || _MSC_VER >= 1900

StringWrapper::StringWrapper()
  : ::google::protobuf::Message(), _internal_metadata_(NULL) {
  if (this != internal_default_instance()) protobuf_InitDefaults_google_2fprotobuf_2futil_2finternal_2ftestdata_2fwrappers_2eproto();
  SharedCtor();
  // @@protoc_insertion_point(constructor:google.protobuf.testing.StringWrapper)
}

void StringWrapper::InitAsDefaultInstance() {
  string_ = const_cast< ::google::protobuf::StringValue*>(
      ::google::protobuf::StringValue::internal_default_instance());
}

StringWrapper::StringWrapper(const StringWrapper& from)
  : ::google::protobuf::Message(),
    _internal_metadata_(NULL) {
  SharedCtor();
  UnsafeMergeFrom(from);
  // @@protoc_insertion_point(copy_constructor:google.protobuf.testing.StringWrapper)
}

void StringWrapper::SharedCtor() {
  string_ = NULL;
  _cached_size_ = 0;
}

StringWrapper::~StringWrapper() {
  // @@protoc_insertion_point(destructor:google.protobuf.testing.StringWrapper)
  SharedDtor();
}

void StringWrapper::SharedDtor() {
  if (this != &StringWrapper_default_instance_.get()) {
    delete string_;
  }
}

void StringWrapper::SetCachedSize(int size) const {
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
}
const ::google::protobuf::Descriptor* StringWrapper::descriptor() {
  protobuf_AssignDescriptorsOnce();
  return StringWrapper_descriptor_;
}

const StringWrapper& StringWrapper::default_instance() {
  protobuf_InitDefaults_google_2fprotobuf_2futil_2finternal_2ftestdata_2fwrappers_2eproto();
  return *internal_default_instance();
}

::google::protobuf::internal::ExplicitlyConstructed<StringWrapper> StringWrapper_default_instance_;

StringWrapper* StringWrapper::New(::google::protobuf::Arena* arena) const {
  StringWrapper* n = new StringWrapper;
  if (arena != NULL) {
    arena->Own(n);
  }
  return n;
}

void StringWrapper::Clear() {
// @@protoc_insertion_point(message_clear_start:google.protobuf.testing.StringWrapper)
  if (GetArenaNoVirtual() == NULL && string_ != NULL) delete string_;
  string_ = NULL;
}

bool StringWrapper::MergePartialFromCodedStream(
    ::google::protobuf::io::CodedInputStream* input) {
#define DO_(EXPRESSION) if (!GOOGLE_PREDICT_TRUE(EXPRESSION)) goto failure
  ::google::protobuf::uint32 tag;
  // @@protoc_insertion_point(parse_start:google.protobuf.testing.StringWrapper)
  for (;;) {
    ::std::pair< ::google::protobuf::uint32, bool> p = input->ReadTagWithCutoff(127);
    tag = p.first;
    if (!p.second) goto handle_unusual;
    switch (::google::protobuf::internal::WireFormatLite::GetTagFieldNumber(tag)) {
      // optional .google.protobuf.StringValue string = 1;
      case 1: {
        if (tag == 10) {
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessageNoVirtual(
               input, mutable_string()));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectAtEnd()) goto success;
        break;
      }

      default: {
      handle_unusual:
        if (tag == 0 ||
            ::google::protobuf::internal::WireFormatLite::GetTagWireType(tag) ==
            ::google::protobuf::internal::WireFormatLite::WIRETYPE_END_GROUP) {
          goto success;
        }
        DO_(::google::protobuf::internal::WireFormatLite::SkipField(input, tag));
        break;
      }
    }
  }
success:
  // @@protoc_insertion_point(parse_success:google.protobuf.testing.StringWrapper)
  return true;
failure:
  // @@protoc_insertion_point(parse_failure:google.protobuf.testing.StringWrapper)
  return false;
#undef DO_
}

void StringWrapper::SerializeWithCachedSizes(
    ::google::protobuf::io::CodedOutputStream* output) const {
  // @@protoc_insertion_point(serialize_start:google.protobuf.testing.StringWrapper)
  // optional .google.protobuf.StringValue string = 1;
  if (this->has_string()) {
    ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
      1, *this->string_, output);
  }

  // @@protoc_insertion_point(serialize_end:google.protobuf.testing.StringWrapper)
}

::google::protobuf::uint8* StringWrapper::InternalSerializeWithCachedSizesToArray(
    bool deterministic, ::google::protobuf::uint8* target) const {
  (void)deterministic; // Unused
  // @@protoc_insertion_point(serialize_to_array_start:google.protobuf.testing.StringWrapper)
  // optional .google.protobuf.StringValue string = 1;
  if (this->has_string()) {
    target = ::google::protobuf::internal::WireFormatLite::
      InternalWriteMessageNoVirtualToArray(
        1, *this->string_, false, target);
  }

  // @@protoc_insertion_point(serialize_to_array_end:google.protobuf.testing.StringWrapper)
  return target;
}

size_t StringWrapper::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:google.protobuf.testing.StringWrapper)
  size_t total_size = 0;

  // optional .google.protobuf.StringValue string = 1;
  if (this->has_string()) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::MessageSizeNoVirtual(
        *this->string_);
  }

  int cached_size = ::google::protobuf::internal::ToCachedSize(total_size);
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = cached_size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
  return total_size;
}

void StringWrapper::MergeFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_merge_from_start:google.protobuf.testing.StringWrapper)
  if (GOOGLE_PREDICT_FALSE(&from == this)) MergeFromFail(__LINE__);
  const StringWrapper* source =
      ::google::protobuf::internal::DynamicCastToGenerated<const StringWrapper>(
          &from);
  if (source == NULL) {
  // @@protoc_insertion_point(generalized_merge_from_cast_fail:google.protobuf.testing.StringWrapper)
    ::google::protobuf::internal::ReflectionOps::Merge(from, this);
  } else {
  // @@protoc_insertion_point(generalized_merge_from_cast_success:google.protobuf.testing.StringWrapper)
    UnsafeMergeFrom(*source);
  }
}

void StringWrapper::MergeFrom(const StringWrapper& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:google.protobuf.testing.StringWrapper)
  if (GOOGLE_PREDICT_TRUE(&from != this)) {
    UnsafeMergeFrom(from);
  } else {
    MergeFromFail(__LINE__);
  }
}

void StringWrapper::UnsafeMergeFrom(const StringWrapper& from) {
  GOOGLE_DCHECK(&from != this);
  if (from.has_string()) {
    mutable_string()->::google::protobuf::StringValue::MergeFrom(from.string());
  }
}

void StringWrapper::CopyFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_copy_from_start:google.protobuf.testing.StringWrapper)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

void StringWrapper::CopyFrom(const StringWrapper& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:google.protobuf.testing.StringWrapper)
  if (&from == this) return;
  Clear();
  UnsafeMergeFrom(from);
}

bool StringWrapper::IsInitialized() const {

  return true;
}

void StringWrapper::Swap(StringWrapper* other) {
  if (other == this) return;
  InternalSwap(other);
}
void StringWrapper::InternalSwap(StringWrapper* other) {
  std::swap(string_, other->string_);
  _internal_metadata_.Swap(&other->_internal_metadata_);
  std::swap(_cached_size_, other->_cached_size_);
}

::google::protobuf::Metadata StringWrapper::GetMetadata() const {
  protobuf_AssignDescriptorsOnce();
  ::google::protobuf::Metadata metadata;
  metadata.descriptor = StringWrapper_descriptor_;
  metadata.reflection = StringWrapper_reflection_;
  return metadata;
}

#if PROTOBUF_INLINE_NOT_IN_HEADERS
// StringWrapper

// optional .google.protobuf.StringValue string = 1;
bool StringWrapper::has_string() const {
  return this != internal_default_instance() && string_ != NULL;
}
void StringWrapper::clear_string() {
  if (GetArenaNoVirtual() == NULL && string_ != NULL) delete string_;
  string_ = NULL;
}
const ::google::protobuf::StringValue& StringWrapper::string() const {
  // @@protoc_insertion_point(field_get:google.protobuf.testing.StringWrapper.string)
  return string_ != NULL ? *string_
                         : *::google::protobuf::StringValue::internal_default_instance();
}
::google::protobuf::StringValue* StringWrapper::mutable_string() {
  
  if (string_ == NULL) {
    string_ = new ::google::protobuf::StringValue;
  }
  // @@protoc_insertion_point(field_mutable:google.protobuf.testing.StringWrapper.string)
  return string_;
}
::google::protobuf::StringValue* StringWrapper::release_string() {
  // @@protoc_insertion_point(field_release:google.protobuf.testing.StringWrapper.string)
  
  ::google::protobuf::StringValue* temp = string_;
  string_ = NULL;
  return temp;
}
void StringWrapper::set_allocated_string(::google::protobuf::StringValue* string) {
  delete string_;
  if (string != NULL && string->GetArena() != NULL) {
    ::google::protobuf::StringValue* new_string = new ::google::protobuf::StringValue;
    new_string->CopyFrom(*string);
    string = new_string;
  }
  string_ = string;
  if (string) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_set_allocated:google.protobuf.testing.StringWrapper.string)
}

inline const StringWrapper* StringWrapper::internal_default_instance() {
  return &StringWrapper_default_instance_.get();
}
#endif  // PROTOBUF_INLINE_NOT_IN_HEADERS

// ===================================================================

#if !defined(_MSC_VER) || _MSC_VER >= 1900
const int BytesWrapper::kBytesFieldNumber;
#endif  // !defined(_MSC_VER) || _MSC_VER >= 1900

BytesWrapper::BytesWrapper()
  : ::google::protobuf::Message(), _internal_metadata_(NULL) {
  if (this != internal_default_instance()) protobuf_InitDefaults_google_2fprotobuf_2futil_2finternal_2ftestdata_2fwrappers_2eproto();
  SharedCtor();
  // @@protoc_insertion_point(constructor:google.protobuf.testing.BytesWrapper)
}

void BytesWrapper::InitAsDefaultInstance() {
  bytes_ = const_cast< ::google::protobuf::BytesValue*>(
      ::google::protobuf::BytesValue::internal_default_instance());
}

BytesWrapper::BytesWrapper(const BytesWrapper& from)
  : ::google::protobuf::Message(),
    _internal_metadata_(NULL) {
  SharedCtor();
  UnsafeMergeFrom(from);
  // @@protoc_insertion_point(copy_constructor:google.protobuf.testing.BytesWrapper)
}

void BytesWrapper::SharedCtor() {
  bytes_ = NULL;
  _cached_size_ = 0;
}

BytesWrapper::~BytesWrapper() {
  // @@protoc_insertion_point(destructor:google.protobuf.testing.BytesWrapper)
  SharedDtor();
}

void BytesWrapper::SharedDtor() {
  if (this != &BytesWrapper_default_instance_.get()) {
    delete bytes_;
  }
}

void BytesWrapper::SetCachedSize(int size) const {
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
}
const ::google::protobuf::Descriptor* BytesWrapper::descriptor() {
  protobuf_AssignDescriptorsOnce();
  return BytesWrapper_descriptor_;
}

const BytesWrapper& BytesWrapper::default_instance() {
  protobuf_InitDefaults_google_2fprotobuf_2futil_2finternal_2ftestdata_2fwrappers_2eproto();
  return *internal_default_instance();
}

::google::protobuf::internal::ExplicitlyConstructed<BytesWrapper> BytesWrapper_default_instance_;

BytesWrapper* BytesWrapper::New(::google::protobuf::Arena* arena) const {
  BytesWrapper* n = new BytesWrapper;
  if (arena != NULL) {
    arena->Own(n);
  }
  return n;
}

void BytesWrapper::Clear() {
// @@protoc_insertion_point(message_clear_start:google.protobuf.testing.BytesWrapper)
  if (GetArenaNoVirtual() == NULL && bytes_ != NULL) delete bytes_;
  bytes_ = NULL;
}

bool BytesWrapper::MergePartialFromCodedStream(
    ::google::protobuf::io::CodedInputStream* input) {
#define DO_(EXPRESSION) if (!GOOGLE_PREDICT_TRUE(EXPRESSION)) goto failure
  ::google::protobuf::uint32 tag;
  // @@protoc_insertion_point(parse_start:google.protobuf.testing.BytesWrapper)
  for (;;) {
    ::std::pair< ::google::protobuf::uint32, bool> p = input->ReadTagWithCutoff(127);
    tag = p.first;
    if (!p.second) goto handle_unusual;
    switch (::google::protobuf::internal::WireFormatLite::GetTagFieldNumber(tag)) {
      // optional .google.protobuf.BytesValue bytes = 1;
      case 1: {
        if (tag == 10) {
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessageNoVirtual(
               input, mutable_bytes()));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectAtEnd()) goto success;
        break;
      }

      default: {
      handle_unusual:
        if (tag == 0 ||
            ::google::protobuf::internal::WireFormatLite::GetTagWireType(tag) ==
            ::google::protobuf::internal::WireFormatLite::WIRETYPE_END_GROUP) {
          goto success;
        }
        DO_(::google::protobuf::internal::WireFormatLite::SkipField(input, tag));
        break;
      }
    }
  }
success:
  // @@protoc_insertion_point(parse_success:google.protobuf.testing.BytesWrapper)
  return true;
failure:
  // @@protoc_insertion_point(parse_failure:google.protobuf.testing.BytesWrapper)
  return false;
#undef DO_
}

void BytesWrapper::SerializeWithCachedSizes(
    ::google::protobuf::io::CodedOutputStream* output) const {
  // @@protoc_insertion_point(serialize_start:google.protobuf.testing.BytesWrapper)
  // optional .google.protobuf.BytesValue bytes = 1;
  if (this->has_bytes()) {
    ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
      1, *this->bytes_, output);
  }

  // @@protoc_insertion_point(serialize_end:google.protobuf.testing.BytesWrapper)
}

::google::protobuf::uint8* BytesWrapper::InternalSerializeWithCachedSizesToArray(
    bool deterministic, ::google::protobuf::uint8* target) const {
  (void)deterministic; // Unused
  // @@protoc_insertion_point(serialize_to_array_start:google.protobuf.testing.BytesWrapper)
  // optional .google.protobuf.BytesValue bytes = 1;
  if (this->has_bytes()) {
    target = ::google::protobuf::internal::WireFormatLite::
      InternalWriteMessageNoVirtualToArray(
        1, *this->bytes_, false, target);
  }

  // @@protoc_insertion_point(serialize_to_array_end:google.protobuf.testing.BytesWrapper)
  return target;
}

size_t BytesWrapper::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:google.protobuf.testing.BytesWrapper)
  size_t total_size = 0;

  // optional .google.protobuf.BytesValue bytes = 1;
  if (this->has_bytes()) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::MessageSizeNoVirtual(
        *this->bytes_);
  }

  int cached_size = ::google::protobuf::internal::ToCachedSize(total_size);
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = cached_size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
  return total_size;
}

void BytesWrapper::MergeFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_merge_from_start:google.protobuf.testing.BytesWrapper)
  if (GOOGLE_PREDICT_FALSE(&from == this)) MergeFromFail(__LINE__);
  const BytesWrapper* source =
      ::google::protobuf::internal::DynamicCastToGenerated<const BytesWrapper>(
          &from);
  if (source == NULL) {
  // @@protoc_insertion_point(generalized_merge_from_cast_fail:google.protobuf.testing.BytesWrapper)
    ::google::protobuf::internal::ReflectionOps::Merge(from, this);
  } else {
  // @@protoc_insertion_point(generalized_merge_from_cast_success:google.protobuf.testing.BytesWrapper)
    UnsafeMergeFrom(*source);
  }
}

void BytesWrapper::MergeFrom(const BytesWrapper& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:google.protobuf.testing.BytesWrapper)
  if (GOOGLE_PREDICT_TRUE(&from != this)) {
    UnsafeMergeFrom(from);
  } else {
    MergeFromFail(__LINE__);
  }
}

void BytesWrapper::UnsafeMergeFrom(const BytesWrapper& from) {
  GOOGLE_DCHECK(&from != this);
  if (from.has_bytes()) {
    mutable_bytes()->::google::protobuf::BytesValue::MergeFrom(from.bytes());
  }
}

void BytesWrapper::CopyFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_copy_from_start:google.protobuf.testing.BytesWrapper)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

void BytesWrapper::CopyFrom(const BytesWrapper& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:google.protobuf.testing.BytesWrapper)
  if (&from == this) return;
  Clear();
  UnsafeMergeFrom(from);
}

bool BytesWrapper::IsInitialized() const {

  return true;
}

void BytesWrapper::Swap(BytesWrapper* other) {
  if (other == this) return;
  InternalSwap(other);
}
void BytesWrapper::InternalSwap(BytesWrapper* other) {
  std::swap(bytes_, other->bytes_);
  _internal_metadata_.Swap(&other->_internal_metadata_);
  std::swap(_cached_size_, other->_cached_size_);
}

::google::protobuf::Metadata BytesWrapper::GetMetadata() const {
  protobuf_AssignDescriptorsOnce();
  ::google::protobuf::Metadata metadata;
  metadata.descriptor = BytesWrapper_descriptor_;
  metadata.reflection = BytesWrapper_reflection_;
  return metadata;
}

#if PROTOBUF_INLINE_NOT_IN_HEADERS
// BytesWrapper

// optional .google.protobuf.BytesValue bytes = 1;
bool BytesWrapper::has_bytes() const {
  return this != internal_default_instance() && bytes_ != NULL;
}
void BytesWrapper::clear_bytes() {
  if (GetArenaNoVirtual() == NULL && bytes_ != NULL) delete bytes_;
  bytes_ = NULL;
}
const ::google::protobuf::BytesValue& BytesWrapper::bytes() const {
  // @@protoc_insertion_point(field_get:google.protobuf.testing.BytesWrapper.bytes)
  return bytes_ != NULL ? *bytes_
                         : *::google::protobuf::BytesValue::internal_default_instance();
}
::google::protobuf::BytesValue* BytesWrapper::mutable_bytes() {
  
  if (bytes_ == NULL) {
    bytes_ = new ::google::protobuf::BytesValue;
  }
  // @@protoc_insertion_point(field_mutable:google.protobuf.testing.BytesWrapper.bytes)
  return bytes_;
}
::google::protobuf::BytesValue* BytesWrapper::release_bytes() {
  // @@protoc_insertion_point(field_release:google.protobuf.testing.BytesWrapper.bytes)
  
  ::google::protobuf::BytesValue* temp = bytes_;
  bytes_ = NULL;
  return temp;
}
void BytesWrapper::set_allocated_bytes(::google::protobuf::BytesValue* bytes) {
  delete bytes_;
  if (bytes != NULL && bytes->GetArena() != NULL) {
    ::google::protobuf::BytesValue* new_bytes = new ::google::protobuf::BytesValue;
    new_bytes->CopyFrom(*bytes);
    bytes = new_bytes;
  }
  bytes_ = bytes;
  if (bytes) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_set_allocated:google.protobuf.testing.BytesWrapper.bytes)
}

inline const BytesWrapper* BytesWrapper::internal_default_instance() {
  return &BytesWrapper_default_instance_.get();
}
#endif  // PROTOBUF_INLINE_NOT_IN_HEADERS

// @@protoc_insertion_point(namespace_scope)

}  // namespace testing
}  // namespace protobuf
}  // namespace google

// @@protoc_insertion_point(global_scope)

// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: google/protobuf/util/internal/testdata/anys.proto

#ifndef PROTOBUF_google_2fprotobuf_2futil_2finternal_2ftestdata_2fanys_2eproto__INCLUDED
#define PROTOBUF_google_2fprotobuf_2futil_2finternal_2ftestdata_2fanys_2eproto__INCLUDED

#include <string>

#include <google/protobuf/stubs/common.h>

#if GOOGLE_PROTOBUF_VERSION < 3001000
#error This file was generated by a newer version of protoc which is
#error incompatible with your Protocol Buffer headers.  Please update
#error your headers.
#endif
#if 3001000 < GOOGLE_PROTOBUF_MIN_PROTOC_VERSION
#error This file was generated by an older version of protoc which is
#error incompatible with your Protocol Buffer headers.  Please
#error regenerate this file with a newer version of protoc.
#endif

#include <google/protobuf/arena.h>
#include <google/protobuf/arenastring.h>
#include <google/protobuf/generated_message_util.h>
#include <google/protobuf/metadata.h>
#include <google/protobuf/message.h>
#include <google/protobuf/repeated_field.h>
#include <google/protobuf/extension_set.h>
#include <google/protobuf/map.h>
#include <google/protobuf/map_field_inl.h>
#include <google/protobuf/unknown_field_set.h>
#include <google/protobuf/any.pb.h>
#include <google/protobuf/struct.pb.h>
#include <google/protobuf/timestamp.pb.h>
#include <google/protobuf/duration.pb.h>
#include <google/protobuf/wrappers.pb.h>
// @@protoc_insertion_point(includes)

namespace google {
namespace protobuf {
namespace testing {

// Internal implementation detail -- do not call these.
void protobuf_AddDesc_google_2fprotobuf_2futil_2finternal_2ftestdata_2fanys_2eproto();
void protobuf_InitDefaults_google_2fprotobuf_2futil_2finternal_2ftestdata_2fanys_2eproto();
void protobuf_AssignDesc_google_2fprotobuf_2futil_2finternal_2ftestdata_2fanys_2eproto();
void protobuf_ShutdownFile_google_2fprotobuf_2futil_2finternal_2ftestdata_2fanys_2eproto();

class AnyIn;
class AnyM;
class AnyOut;
class AnyTestCases;
class AnyWrapper;
class Data;
class Imports;

// ===================================================================

class AnyTestCases : public ::google::protobuf::Message /* @@protoc_insertion_point(class_definition:google.protobuf.testing.AnyTestCases) */ {
 public:
  AnyTestCases();
  virtual ~AnyTestCases();

  AnyTestCases(const AnyTestCases& from);

  inline AnyTestCases& operator=(const AnyTestCases& from) {
    CopyFrom(from);
    return *this;
  }

  static const ::google::protobuf::Descriptor* descriptor();
  static const AnyTestCases& default_instance();

  static const AnyTestCases* internal_default_instance();

  void Swap(AnyTestCases* other);

  // implements Message ----------------------------------------------

  inline AnyTestCases* New() const { return New(NULL); }

  AnyTestCases* New(::google::protobuf::Arena* arena) const;
  void CopyFrom(const ::google::protobuf::Message& from);
  void MergeFrom(const ::google::protobuf::Message& from);
  void CopyFrom(const AnyTestCases& from);
  void MergeFrom(const AnyTestCases& from);
  void Clear();
  bool IsInitialized() const;

  size_t ByteSizeLong() const;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input);
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const;
  ::google::protobuf::uint8* InternalSerializeWithCachedSizesToArray(
      bool deterministic, ::google::protobuf::uint8* output) const;
  ::google::protobuf::uint8* SerializeWithCachedSizesToArray(::google::protobuf::uint8* output) const {
    return InternalSerializeWithCachedSizesToArray(false, output);
  }
  int GetCachedSize() const { return _cached_size_; }
  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const;
  void InternalSwap(AnyTestCases* other);
  void UnsafeMergeFrom(const AnyTestCases& from);
  private:
  inline ::google::protobuf::Arena* GetArenaNoVirtual() const {
    return _internal_metadata_.arena();
  }
  inline void* MaybeArenaPtr() const {
    return _internal_metadata_.raw_arena_ptr();
  }
  public:

  ::google::protobuf::Metadata GetMetadata() const;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // optional .google.protobuf.testing.AnyWrapper empty_any = 1;
  bool has_empty_any() const;
  void clear_empty_any();
  static const int kEmptyAnyFieldNumber = 1;
  const ::google::protobuf::testing::AnyWrapper& empty_any() const;
  ::google::protobuf::testing::AnyWrapper* mutable_empty_any();
  ::google::protobuf::testing::AnyWrapper* release_empty_any();
  void set_allocated_empty_any(::google::protobuf::testing::AnyWrapper* empty_any);

  // optional .google.protobuf.testing.AnyWrapper type_only_any = 2;
  bool has_type_only_any() const;
  void clear_type_only_any();
  static const int kTypeOnlyAnyFieldNumber = 2;
  const ::google::protobuf::testing::AnyWrapper& type_only_any() const;
  ::google::protobuf::testing::AnyWrapper* mutable_type_only_any();
  ::google::protobuf::testing::AnyWrapper* release_type_only_any();
  void set_allocated_type_only_any(::google::protobuf::testing::AnyWrapper* type_only_any);

  // optional .google.protobuf.testing.AnyWrapper wrapper_any = 3;
  bool has_wrapper_any() const;
  void clear_wrapper_any();
  static const int kWrapperAnyFieldNumber = 3;
  const ::google::protobuf::testing::AnyWrapper& wrapper_any() const;
  ::google::protobuf::testing::AnyWrapper* mutable_wrapper_any();
  ::google::protobuf::testing::AnyWrapper* release_wrapper_any();
  void set_allocated_wrapper_any(::google::protobuf::testing::AnyWrapper* wrapper_any);

  // optional .google.protobuf.testing.AnyWrapper any_with_timestamp_value = 4;
  bool has_any_with_timestamp_value() const;
  void clear_any_with_timestamp_value();
  static const int kAnyWithTimestampValueFieldNumber = 4;
  const ::google::protobuf::testing::AnyWrapper& any_with_timestamp_value() const;
  ::google::protobuf::testing::AnyWrapper* mutable_any_with_timestamp_value();
  ::google::protobuf::testing::AnyWrapper* release_any_with_timestamp_value();
  void set_allocated_any_with_timestamp_value(::google::protobuf::testing::AnyWrapper* any_with_timestamp_value);

  // optional .google.protobuf.testing.AnyWrapper any_with_duration_value = 5;
  bool has_any_with_duration_value() const;
  void clear_any_with_duration_value();
  static const int kAnyWithDurationValueFieldNumber = 5;
  const ::google::protobuf::testing::AnyWrapper& any_with_duration_value() const;
  ::google::protobuf::testing::AnyWrapper* mutable_any_with_duration_value();
  ::google::protobuf::testing::AnyWrapper* release_any_with_duration_value();
  void set_allocated_any_with_duration_value(::google::protobuf::testing::AnyWrapper* any_with_duration_value);

  // optional .google.protobuf.testing.AnyWrapper any_with_struct_value = 6;
  bool has_any_with_struct_value() const;
  void clear_any_with_struct_value();
  static const int kAnyWithStructValueFieldNumber = 6;
  const ::google::protobuf::testing::AnyWrapper& any_with_struct_value() const;
  ::google::protobuf::testing::AnyWrapper* mutable_any_with_struct_value();
  ::google::protobuf::testing::AnyWrapper* release_any_with_struct_value();
  void set_allocated_any_with_struct_value(::google::protobuf::testing::AnyWrapper* any_with_struct_value);

  // optional .google.protobuf.testing.AnyWrapper recursive_any = 7;
  bool has_recursive_any() const;
  void clear_recursive_any();
  static const int kRecursiveAnyFieldNumber = 7;
  const ::google::protobuf::testing::AnyWrapper& recursive_any() const;
  ::google::protobuf::testing::AnyWrapper* mutable_recursive_any();
  ::google::protobuf::testing::AnyWrapper* release_recursive_any();
  void set_allocated_recursive_any(::google::protobuf::testing::AnyWrapper* recursive_any);

  // optional .google.protobuf.testing.AnyWrapper any_with_message_value = 8;
  bool has_any_with_message_value() const;
  void clear_any_with_message_value();
  static const int kAnyWithMessageValueFieldNumber = 8;
  const ::google::protobuf::testing::AnyWrapper& any_with_message_value() const;
  ::google::protobuf::testing::AnyWrapper* mutable_any_with_message_value();
  ::google::protobuf::testing::AnyWrapper* release_any_with_message_value();
  void set_allocated_any_with_message_value(::google::protobuf::testing::AnyWrapper* any_with_message_value);

  // optional .google.protobuf.testing.AnyWrapper any_with_nested_message = 9;
  bool has_any_with_nested_message() const;
  void clear_any_with_nested_message();
  static const int kAnyWithNestedMessageFieldNumber = 9;
  const ::google::protobuf::testing::AnyWrapper& any_with_nested_message() const;
  ::google::protobuf::testing::AnyWrapper* mutable_any_with_nested_message();
  ::google::protobuf::testing::AnyWrapper* release_any_with_nested_message();
  void set_allocated_any_with_nested_message(::google::protobuf::testing::AnyWrapper* any_with_nested_message);

  // optional .google.protobuf.testing.AnyWrapper any_with_message_with_wrapper_type = 10;
  bool has_any_with_message_with_wrapper_type() const;
  void clear_any_with_message_with_wrapper_type();
  static const int kAnyWithMessageWithWrapperTypeFieldNumber = 10;
  const ::google::protobuf::testing::AnyWrapper& any_with_message_with_wrapper_type() const;
  ::google::protobuf::testing::AnyWrapper* mutable_any_with_message_with_wrapper_type();
  ::google::protobuf::testing::AnyWrapper* release_any_with_message_with_wrapper_type();
  void set_allocated_any_with_message_with_wrapper_type(::google::protobuf::testing::AnyWrapper* any_with_message_with_wrapper_type);

  // optional .google.protobuf.testing.AnyWrapper any_with_message_with_timestamp = 11;
  bool has_any_with_message_with_timestamp() const;
  void clear_any_with_message_with_timestamp();
  static const int kAnyWithMessageWithTimestampFieldNumber = 11;
  const ::google::protobuf::testing::AnyWrapper& any_with_message_with_timestamp() const;
  ::google::protobuf::testing::AnyWrapper* mutable_any_with_message_with_timestamp();
  ::google::protobuf::testing::AnyWrapper* release_any_with_message_with_timestamp();
  void set_allocated_any_with_message_with_timestamp(::google::protobuf::testing::AnyWrapper* any_with_message_with_timestamp);

  // optional .google.protobuf.testing.AnyWrapper any_with_message_containing_map = 12;
  bool has_any_with_message_containing_map() const;
  void clear_any_with_message_containing_map();
  static const int kAnyWithMessageContainingMapFieldNumber = 12;
  const ::google::protobuf::testing::AnyWrapper& any_with_message_containing_map() const;
  ::google::protobuf::testing::AnyWrapper* mutable_any_with_message_containing_map();
  ::google::protobuf::testing::AnyWrapper* release_any_with_message_containing_map();
  void set_allocated_any_with_message_containing_map(::google::protobuf::testing::AnyWrapper* any_with_message_containing_map);

  // optional .google.protobuf.testing.AnyWrapper any_with_message_containing_struct = 13;
  bool has_any_with_message_containing_struct() const;
  void clear_any_with_message_containing_struct();
  static const int kAnyWithMessageContainingStructFieldNumber = 13;
  const ::google::protobuf::testing::AnyWrapper& any_with_message_containing_struct() const;
  ::google::protobuf::testing::AnyWrapper* mutable_any_with_message_containing_struct();
  ::google::protobuf::testing::AnyWrapper* release_any_with_message_containing_struct();
  void set_allocated_any_with_message_containing_struct(::google::protobuf::testing::AnyWrapper* any_with_message_containing_struct);

  // optional .google.protobuf.testing.AnyWrapper any_with_message_containing_repeated_message = 14;
  bool has_any_with_message_containing_repeated_message() const;
  void clear_any_with_message_containing_repeated_message();
  static const int kAnyWithMessageContainingRepeatedMessageFieldNumber = 14;
  const ::google::protobuf::testing::AnyWrapper& any_with_message_containing_repeated_message() const;
  ::google::protobuf::testing::AnyWrapper* mutable_any_with_message_containing_repeated_message();
  ::google::protobuf::testing::AnyWrapper* release_any_with_message_containing_repeated_message();
  void set_allocated_any_with_message_containing_repeated_message(::google::protobuf::testing::AnyWrapper* any_with_message_containing_repeated_message);

  // optional .google.protobuf.testing.AnyWrapper recursive_any_with_type_field_at_end = 15;
  bool has_recursive_any_with_type_field_at_end() const;
  void clear_recursive_any_with_type_field_at_end();
  static const int kRecursiveAnyWithTypeFieldAtEndFieldNumber = 15;
  const ::google::protobuf::testing::AnyWrapper& recursive_any_with_type_field_at_end() const;
  ::google::protobuf::testing::AnyWrapper* mutable_recursive_any_with_type_field_at_end();
  ::google::protobuf::testing::AnyWrapper* release_recursive_any_with_type_field_at_end();
  void set_allocated_recursive_any_with_type_field_at_end(::google::protobuf::testing::AnyWrapper* recursive_any_with_type_field_at_end);

  // optional .google.protobuf.Any top_level_any = 50;
  bool has_top_level_any() const;
  void clear_top_level_any();
  static const int kTopLevelAnyFieldNumber = 50;
  const ::google::protobuf::Any& top_level_any() const;
  ::google::protobuf::Any* mutable_top_level_any();
  ::google::protobuf::Any* release_top_level_any();
  void set_allocated_top_level_any(::google::protobuf::Any* top_level_any);

  // optional .google.protobuf.Any top_level_any_with_type_field_at_end = 51;
  bool has_top_level_any_with_type_field_at_end() const;
  void clear_top_level_any_with_type_field_at_end();
  static const int kTopLevelAnyWithTypeFieldAtEndFieldNumber = 51;
  const ::google::protobuf::Any& top_level_any_with_type_field_at_end() const;
  ::google::protobuf::Any* mutable_top_level_any_with_type_field_at_end();
  ::google::protobuf::Any* release_top_level_any_with_type_field_at_end();
  void set_allocated_top_level_any_with_type_field_at_end(::google::protobuf::Any* top_level_any_with_type_field_at_end);

  // @@protoc_insertion_point(class_scope:google.protobuf.testing.AnyTestCases)
 private:

  ::google::protobuf::internal::InternalMetadataWithArena _internal_metadata_;
  ::google::protobuf::testing::AnyWrapper* empty_any_;
  ::google::protobuf::testing::AnyWrapper* type_only_any_;
  ::google::protobuf::testing::AnyWrapper* wrapper_any_;
  ::google::protobuf::testing::AnyWrapper* any_with_timestamp_value_;
  ::google::protobuf::testing::AnyWrapper* any_with_duration_value_;
  ::google::protobuf::testing::AnyWrapper* any_with_struct_value_;
  ::google::protobuf::testing::AnyWrapper* recursive_any_;
  ::google::protobuf::testing::AnyWrapper* any_with_message_value_;
  ::google::protobuf::testing::AnyWrapper* any_with_nested_message_;
  ::google::protobuf::testing::AnyWrapper* any_with_message_with_wrapper_type_;
  ::google::protobuf::testing::AnyWrapper* any_with_message_with_timestamp_;
  ::google::protobuf::testing::AnyWrapper* any_with_message_containing_map_;
  ::google::protobuf::testing::AnyWrapper* any_with_message_containing_struct_;
  ::google::protobuf::testing::AnyWrapper* any_with_message_containing_repeated_message_;
  ::google::protobuf::testing::AnyWrapper* recursive_any_with_type_field_at_end_;
  ::google::protobuf::Any* top_level_any_;
  ::google::protobuf::Any* top_level_any_with_type_field_at_end_;
  mutable int _cached_size_;
  friend void  protobuf_InitDefaults_google_2fprotobuf_2futil_2finternal_2ftestdata_2fanys_2eproto_impl();
  friend void  protobuf_AddDesc_google_2fprotobuf_2futil_2finternal_2ftestdata_2fanys_2eproto_impl();
  friend void protobuf_AssignDesc_google_2fprotobuf_2futil_2finternal_2ftestdata_2fanys_2eproto();
  friend void protobuf_ShutdownFile_google_2fprotobuf_2futil_2finternal_2ftestdata_2fanys_2eproto();

  void InitAsDefaultInstance();
};
extern ::google::protobuf::internal::ExplicitlyConstructed<AnyTestCases> AnyTestCases_default_instance_;

// -------------------------------------------------------------------

class AnyWrapper : public ::google::protobuf::Message /* @@protoc_insertion_point(class_definition:google.protobuf.testing.AnyWrapper) */ {
 public:
  AnyWrapper();
  virtual ~AnyWrapper();

  AnyWrapper(const AnyWrapper& from);

  inline AnyWrapper& operator=(const AnyWrapper& from) {
    CopyFrom(from);
    return *this;
  }

  static const ::google::protobuf::Descriptor* descriptor();
  static const AnyWrapper& default_instance();

  static const AnyWrapper* internal_default_instance();

  void Swap(AnyWrapper* other);

  // implements Message ----------------------------------------------

  inline AnyWrapper* New() const { return New(NULL); }

  AnyWrapper* New(::google::protobuf::Arena* arena) const;
  void CopyFrom(const ::google::protobuf::Message& from);
  void MergeFrom(const ::google::protobuf::Message& from);
  void CopyFrom(const AnyWrapper& from);
  void MergeFrom(const AnyWrapper& from);
  void Clear();
  bool IsInitialized() const;

  size_t ByteSizeLong() const;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input);
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const;
  ::google::protobuf::uint8* InternalSerializeWithCachedSizesToArray(
      bool deterministic, ::google::protobuf::uint8* output) const;
  ::google::protobuf::uint8* SerializeWithCachedSizesToArray(::google::protobuf::uint8* output) const {
    return InternalSerializeWithCachedSizesToArray(false, output);
  }
  int GetCachedSize() const { return _cached_size_; }
  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const;
  void InternalSwap(AnyWrapper* other);
  void UnsafeMergeFrom(const AnyWrapper& from);
  private:
  inline ::google::protobuf::Arena* GetArenaNoVirtual() const {
    return _internal_metadata_.arena();
  }
  inline void* MaybeArenaPtr() const {
    return _internal_metadata_.raw_arena_ptr();
  }
  public:

  ::google::protobuf::Metadata GetMetadata() const;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // optional .google.protobuf.Any any = 1;
  bool has_any() const;
  void clear_any();
  static const int kAnyFieldNumber = 1;
  const ::google::protobuf::Any& any() const;
  ::google::protobuf::Any* mutable_any();
  ::google::protobuf::Any* release_any();
  void set_allocated_any(::google::protobuf::Any* any);

  // @@protoc_insertion_point(class_scope:google.protobuf.testing.AnyWrapper)
 private:

  ::google::protobuf::internal::InternalMetadataWithArena _internal_metadata_;
  ::google::protobuf::Any* any_;
  mutable int _cached_size_;
  friend void  protobuf_InitDefaults_google_2fprotobuf_2futil_2finternal_2ftestdata_2fanys_2eproto_impl();
  friend void  protobuf_AddDesc_google_2fprotobuf_2futil_2finternal_2ftestdata_2fanys_2eproto_impl();
  friend void protobuf_AssignDesc_google_2fprotobuf_2futil_2finternal_2ftestdata_2fanys_2eproto();
  friend void protobuf_ShutdownFile_google_2fprotobuf_2futil_2finternal_2ftestdata_2fanys_2eproto();

  void InitAsDefaultInstance();
};
extern ::google::protobuf::internal::ExplicitlyConstructed<AnyWrapper> AnyWrapper_default_instance_;

// -------------------------------------------------------------------

class Imports : public ::google::protobuf::Message /* @@protoc_insertion_point(class_definition:google.protobuf.testing.Imports) */ {
 public:
  Imports();
  virtual ~Imports();

  Imports(const Imports& from);

  inline Imports& operator=(const Imports& from) {
    CopyFrom(from);
    return *this;
  }

  static const ::google::protobuf::Descriptor* descriptor();
  static const Imports& default_instance();

  static const Imports* internal_default_instance();

  void Swap(Imports* other);

  // implements Message ----------------------------------------------

  inline Imports* New() const { return New(NULL); }

  Imports* New(::google::protobuf::Arena* arena) const;
  void CopyFrom(const ::google::protobuf::Message& from);
  void MergeFrom(const ::google::protobuf::Message& from);
  void CopyFrom(const Imports& from);
  void MergeFrom(const Imports& from);
  void Clear();
  bool IsInitialized() const;

  size_t ByteSizeLong() const;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input);
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const;
  ::google::protobuf::uint8* InternalSerializeWithCachedSizesToArray(
      bool deterministic, ::google::protobuf::uint8* output) const;
  ::google::protobuf::uint8* SerializeWithCachedSizesToArray(::google::protobuf::uint8* output) const {
    return InternalSerializeWithCachedSizesToArray(false, output);
  }
  int GetCachedSize() const { return _cached_size_; }
  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const;
  void InternalSwap(Imports* other);
  void UnsafeMergeFrom(const Imports& from);
  private:
  inline ::google::protobuf::Arena* GetArenaNoVirtual() const {
    return _internal_metadata_.arena();
  }
  inline void* MaybeArenaPtr() const {
    return _internal_metadata_.raw_arena_ptr();
  }
  public:

  ::google::protobuf::Metadata GetMetadata() const;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // optional .google.protobuf.DoubleValue dbl = 1;
  bool has_dbl() const;
  void clear_dbl();
  static const int kDblFieldNumber = 1;
  const ::google::protobuf::DoubleValue& dbl() const;
  ::google::protobuf::DoubleValue* mutable_dbl();
  ::google::protobuf::DoubleValue* release_dbl();
  void set_allocated_dbl(::google::protobuf::DoubleValue* dbl);

  // optional .google.protobuf.Struct struct = 2;
  bool has_struct_() const;
  void clear_struct_();
  static const int kStructFieldNumber = 2;
  const ::google::protobuf::Struct& struct_() const;
  ::google::protobuf::Struct* mutable_struct_();
  ::google::protobuf::Struct* release_struct_();
  void set_allocated_struct_(::google::protobuf::Struct* struct_);

  // optional .google.protobuf.Timestamp timestamp = 3;
  bool has_timestamp() const;
  void clear_timestamp();
  static const int kTimestampFieldNumber = 3;
  const ::google::protobuf::Timestamp& timestamp() const;
  ::google::protobuf::Timestamp* mutable_timestamp();
  ::google::protobuf::Timestamp* release_timestamp();
  void set_allocated_timestamp(::google::protobuf::Timestamp* timestamp);

  // optional .google.protobuf.Duration duration = 4;
  bool has_duration() const;
  void clear_duration();
  static const int kDurationFieldNumber = 4;
  const ::google::protobuf::Duration& duration() const;
  ::google::protobuf::Duration* mutable_duration();
  ::google::protobuf::Duration* release_duration();
  void set_allocated_duration(::google::protobuf::Duration* duration);

  // optional .google.protobuf.Int32Value i32 = 5;
  bool has_i32() const;
  void clear_i32();
  static const int kI32FieldNumber = 5;
  const ::google::protobuf::Int32Value& i32() const;
  ::google::protobuf::Int32Value* mutable_i32();
  ::google::protobuf::Int32Value* release_i32();
  void set_allocated_i32(::google::protobuf::Int32Value* i32);

  // optional .google.protobuf.testing.Data data = 100;
  bool has_data() const;
  void clear_data();
  static const int kDataFieldNumber = 100;
  const ::google::protobuf::testing::Data& data() const;
  ::google::protobuf::testing::Data* mutable_data();
  ::google::protobuf::testing::Data* release_data();
  void set_allocated_data(::google::protobuf::testing::Data* data);

  // @@protoc_insertion_point(class_scope:google.protobuf.testing.Imports)
 private:

  ::google::protobuf::internal::InternalMetadataWithArena _internal_metadata_;
  ::google::protobuf::DoubleValue* dbl_;
  ::google::protobuf::Struct* struct__;
  ::google::protobuf::Timestamp* timestamp_;
  ::google::protobuf::Duration* duration_;
  ::google::protobuf::Int32Value* i32_;
  ::google::protobuf::testing::Data* data_;
  mutable int _cached_size_;
  friend void  protobuf_InitDefaults_google_2fprotobuf_2futil_2finternal_2ftestdata_2fanys_2eproto_impl();
  friend void  protobuf_AddDesc_google_2fprotobuf_2futil_2finternal_2ftestdata_2fanys_2eproto_impl();
  friend void protobuf_AssignDesc_google_2fprotobuf_2futil_2finternal_2ftestdata_2fanys_2eproto();
  friend void protobuf_ShutdownFile_google_2fprotobuf_2futil_2finternal_2ftestdata_2fanys_2eproto();

  void InitAsDefaultInstance();
};
extern ::google::protobuf::internal::ExplicitlyConstructed<Imports> Imports_default_instance_;

// -------------------------------------------------------------------

class Data : public ::google::protobuf::Message /* @@protoc_insertion_point(class_definition:google.protobuf.testing.Data) */ {
 public:
  Data();
  virtual ~Data();

  Data(const Data& from);

  inline Data& operator=(const Data& from) {
    CopyFrom(from);
    return *this;
  }

  static const ::google::protobuf::Descriptor* descriptor();
  static const Data& default_instance();

  static const Data* internal_default_instance();

  void Swap(Data* other);

  // implements Message ----------------------------------------------

  inline Data* New() const { return New(NULL); }

  Data* New(::google::protobuf::Arena* arena) const;
  void CopyFrom(const ::google::protobuf::Message& from);
  void MergeFrom(const ::google::protobuf::Message& from);
  void CopyFrom(const Data& from);
  void MergeFrom(const Data& from);
  void Clear();
  bool IsInitialized() const;

  size_t ByteSizeLong() const;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input);
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const;
  ::google::protobuf::uint8* InternalSerializeWithCachedSizesToArray(
      bool deterministic, ::google::protobuf::uint8* output) const;
  ::google::protobuf::uint8* SerializeWithCachedSizesToArray(::google::protobuf::uint8* output) const {
    return InternalSerializeWithCachedSizesToArray(false, output);
  }
  int GetCachedSize() const { return _cached_size_; }
  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const;
  void InternalSwap(Data* other);
  void UnsafeMergeFrom(const Data& from);
  private:
  inline ::google::protobuf::Arena* GetArenaNoVirtual() const {
    return _internal_metadata_.arena();
  }
  inline void* MaybeArenaPtr() const {
    return _internal_metadata_.raw_arena_ptr();
  }
  public:

  ::google::protobuf::Metadata GetMetadata() const;

  // nested types ----------------------------------------------------


  // accessors -------------------------------------------------------

  // optional int32 attr = 1;
  void clear_attr();
  static const int kAttrFieldNumber = 1;
  ::google::protobuf::int32 attr() const;
  void set_attr(::google::protobuf::int32 value);

  // optional string str = 2;
  void clear_str();
  static const int kStrFieldNumber = 2;
  const ::std::string& str() const;
  void set_str(const ::std::string& value);
  void set_str(const char* value);
  void set_str(const char* value, size_t size);
  ::std::string* mutable_str();
  ::std::string* release_str();
  void set_allocated_str(::std::string* str);

  // repeated string msgs = 3;
  int msgs_size() const;
  void clear_msgs();
  static const int kMsgsFieldNumber = 3;
  const ::std::string& msgs(int index) const;
  ::std::string* mutable_msgs(int index);
  void set_msgs(int index, const ::std::string& value);
  void set_msgs(int index, const char* value);
  void set_msgs(int index, const char* value, size_t size);
  ::std::string* add_msgs();
  void add_msgs(const ::std::string& value);
  void add_msgs(const char* value);
  void add_msgs(const char* value, size_t size);
  const ::google::protobuf::RepeatedPtrField< ::std::string>& msgs() const;
  ::google::protobuf::RepeatedPtrField< ::std::string>* mutable_msgs();

  // optional .google.protobuf.testing.Data nested_data = 4;
  bool has_nested_data() const;
  void clear_nested_data();
  static const int kNestedDataFieldNumber = 4;
  const ::google::protobuf::testing::Data& nested_data() const;
  ::google::protobuf::testing::Data* mutable_nested_data();
  ::google::protobuf::testing::Data* release_nested_data();
  void set_allocated_nested_data(::google::protobuf::testing::Data* nested_data);

  // optional .google.protobuf.Int32Value int_wrapper = 5;
  bool has_int_wrapper() const;
  void clear_int_wrapper();
  static const int kIntWrapperFieldNumber = 5;
  const ::google::protobuf::Int32Value& int_wrapper() const;
  ::google::protobuf::Int32Value* mutable_int_wrapper();
  ::google::protobuf::Int32Value* release_int_wrapper();
  void set_allocated_int_wrapper(::google::protobuf::Int32Value* int_wrapper);

  // optional .google.protobuf.Timestamp time = 6;
  bool has_time() const;
  void clear_time();
  static const int kTimeFieldNumber = 6;
  const ::google::protobuf::Timestamp& time() const;
  ::google::protobuf::Timestamp* mutable_time();
  ::google::protobuf::Timestamp* release_time();
  void set_allocated_time(::google::protobuf::Timestamp* time);

  // map<string, string> map_data = 7;
  int map_data_size() const;
  void clear_map_data();
  static const int kMapDataFieldNumber = 7;
  const ::google::protobuf::Map< ::std::string, ::std::string >&
      map_data() const;
  ::google::protobuf::Map< ::std::string, ::std::string >*
      mutable_map_data();

  // optional .google.protobuf.Struct struct_data = 8;
  bool has_struct_data() const;
  void clear_struct_data();
  static const int kStructDataFieldNumber = 8;
  const ::google::protobuf::Struct& struct_data() const;
  ::google::protobuf::Struct* mutable_struct_data();
  ::google::protobuf::Struct* release_struct_data();
  void set_allocated_struct_data(::google::protobuf::Struct* struct_data);

  // repeated .google.protobuf.testing.Data repeated_data = 9;
  int repeated_data_size() const;
  void clear_repeated_data();
  static const int kRepeatedDataFieldNumber = 9;
  const ::google::protobuf::testing::Data& repeated_data(int index) const;
  ::google::protobuf::testing::Data* mutable_repeated_data(int index);
  ::google::protobuf::testing::Data* add_repeated_data();
  ::google::protobuf::RepeatedPtrField< ::google::protobuf::testing::Data >*
      mutable_repeated_data();
  const ::google::protobuf::RepeatedPtrField< ::google::protobuf::testing::Data >&
      repeated_data() const;

  // @@protoc_insertion_point(class_scope:google.protobuf.testing.Data)
 private:

  ::google::protobuf::internal::InternalMetadataWithArena _internal_metadata_;
  ::google::protobuf::RepeatedPtrField< ::std::string> msgs_;
  typedef ::google::protobuf::internal::MapEntryLite<
      ::std::string, ::std::string,
      ::google::protobuf::internal::WireFormatLite::TYPE_STRING,
      ::google::protobuf::internal::WireFormatLite::TYPE_STRING,
      0 >
      Data_MapDataEntry;
  ::google::protobuf::internal::MapField<
      ::std::string, ::std::string,
      ::google::protobuf::internal::WireFormatLite::TYPE_STRING,
      ::google::protobuf::internal::WireFormatLite::TYPE_STRING,
      0 > map_data_;
  ::google::protobuf::RepeatedPtrField< ::google::protobuf::testing::Data > repeated_data_;
  ::google::protobuf::internal::ArenaStringPtr str_;
  ::google::protobuf::testing::Data* nested_data_;
  ::google::protobuf::Int32Value* int_wrapper_;
  ::google::protobuf::Timestamp* time_;
  ::google::protobuf::Struct* struct_data_;
  ::google::protobuf::int32 attr_;
  mutable int _cached_size_;
  friend void  protobuf_InitDefaults_google_2fprotobuf_2futil_2finternal_2ftestdata_2fanys_2eproto_impl();
  friend void  protobuf_AddDesc_google_2fprotobuf_2futil_2finternal_2ftestdata_2fanys_2eproto_impl();
  friend void protobuf_AssignDesc_google_2fprotobuf_2futil_2finternal_2ftestdata_2fanys_2eproto();
  friend void protobuf_ShutdownFile_google_2fprotobuf_2futil_2finternal_2ftestdata_2fanys_2eproto();

  void InitAsDefaultInstance();
};
extern ::google::protobuf::internal::ExplicitlyConstructed<Data> Data_default_instance_;

// -------------------------------------------------------------------

class AnyIn : public ::google::protobuf::Message /* @@protoc_insertion_point(class_definition:google.protobuf.testing.AnyIn) */ {
 public:
  AnyIn();
  virtual ~AnyIn();

  AnyIn(const AnyIn& from);

  inline AnyIn& operator=(const AnyIn& from) {
    CopyFrom(from);
    return *this;
  }

  static const ::google::protobuf::Descriptor* descriptor();
  static const AnyIn& default_instance();

  static const AnyIn* internal_default_instance();

  void Swap(AnyIn* other);

  // implements Message ----------------------------------------------

  inline AnyIn* New() const { return New(NULL); }

  AnyIn* New(::google::protobuf::Arena* arena) const;
  void CopyFrom(const ::google::protobuf::Message& from);
  void MergeFrom(const ::google::protobuf::Message& from);
  void CopyFrom(const AnyIn& from);
  void MergeFrom(const AnyIn& from);
  void Clear();
  bool IsInitialized() const;

  size_t ByteSizeLong() const;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input);
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const;
  ::google::protobuf::uint8* InternalSerializeWithCachedSizesToArray(
      bool deterministic, ::google::protobuf::uint8* output) const;
  ::google::protobuf::uint8* SerializeWithCachedSizesToArray(::google::protobuf::uint8* output) const {
    return InternalSerializeWithCachedSizesToArray(false, output);
  }
  int GetCachedSize() const { return _cached_size_; }
  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const;
  void InternalSwap(AnyIn* other);
  void UnsafeMergeFrom(const AnyIn& from);
  private:
  inline ::google::protobuf::Arena* GetArenaNoVirtual() const {
    return _internal_metadata_.arena();
  }
  inline void* MaybeArenaPtr() const {
    return _internal_metadata_.raw_arena_ptr();
  }
  public:

  ::google::protobuf::Metadata GetMetadata() const;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // optional string something = 1;
  void clear_something();
  static const int kSomethingFieldNumber = 1;
  const ::std::string& something() const;
  void set_something(const ::std::string& value);
  void set_something(const char* value);
  void set_something(const char* value, size_t size);
  ::std::string* mutable_something();
  ::std::string* release_something();
  void set_allocated_something(::std::string* something);

  // optional .google.protobuf.Any any = 2;
  bool has_any() const;
  void clear_any();
  static const int kAnyFieldNumber = 2;
  const ::google::protobuf::Any& any() const;
  ::google::protobuf::Any* mutable_any();
  ::google::protobuf::Any* release_any();
  void set_allocated_any(::google::protobuf::Any* any);

  // @@protoc_insertion_point(class_scope:google.protobuf.testing.AnyIn)
 private:

  ::google::protobuf::internal::InternalMetadataWithArena _internal_metadata_;
  ::google::protobuf::internal::ArenaStringPtr something_;
  ::google::protobuf::Any* any_;
  mutable int _cached_size_;
  friend void  protobuf_InitDefaults_google_2fprotobuf_2futil_2finternal_2ftestdata_2fanys_2eproto_impl();
  friend void  protobuf_AddDesc_google_2fprotobuf_2futil_2finternal_2ftestdata_2fanys_2eproto_impl();
  friend void protobuf_AssignDesc_google_2fprotobuf_2futil_2finternal_2ftestdata_2fanys_2eproto();
  friend void protobuf_ShutdownFile_google_2fprotobuf_2futil_2finternal_2ftestdata_2fanys_2eproto();

  void InitAsDefaultInstance();
};
extern ::google::protobuf::internal::ExplicitlyConstructed<AnyIn> AnyIn_default_instance_;

// -------------------------------------------------------------------

class AnyOut : public ::google::protobuf::Message /* @@protoc_insertion_point(class_definition:google.protobuf.testing.AnyOut) */ {
 public:
  AnyOut();
  virtual ~AnyOut();

  AnyOut(const AnyOut& from);

  inline AnyOut& operator=(const AnyOut& from) {
    CopyFrom(from);
    return *this;
  }

  static const ::google::protobuf::Descriptor* descriptor();
  static const AnyOut& default_instance();

  static const AnyOut* internal_default_instance();

  void Swap(AnyOut* other);

  // implements Message ----------------------------------------------

  inline AnyOut* New() const { return New(NULL); }

  AnyOut* New(::google::protobuf::Arena* arena) const;
  void CopyFrom(const ::google::protobuf::Message& from);
  void MergeFrom(const ::google::protobuf::Message& from);
  void CopyFrom(const AnyOut& from);
  void MergeFrom(const AnyOut& from);
  void Clear();
  bool IsInitialized() const;

  size_t ByteSizeLong() const;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input);
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const;
  ::google::protobuf::uint8* InternalSerializeWithCachedSizesToArray(
      bool deterministic, ::google::protobuf::uint8* output) const;
  ::google::protobuf::uint8* SerializeWithCachedSizesToArray(::google::protobuf::uint8* output) const {
    return InternalSerializeWithCachedSizesToArray(false, output);
  }
  int GetCachedSize() const { return _cached_size_; }
  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const;
  void InternalSwap(AnyOut* other);
  void UnsafeMergeFrom(const AnyOut& from);
  private:
  inline ::google::protobuf::Arena* GetArenaNoVirtual() const {
    return _internal_metadata_.arena();
  }
  inline void* MaybeArenaPtr() const {
    return _internal_metadata_.raw_arena_ptr();
  }
  public:

  ::google::protobuf::Metadata GetMetadata() const;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // optional .google.protobuf.Any any = 1;
  bool has_any() const;
  void clear_any();
  static const int kAnyFieldNumber = 1;
  const ::google::protobuf::Any& any() const;
  ::google::protobuf::Any* mutable_any();
  ::google::protobuf::Any* release_any();
  void set_allocated_any(::google::protobuf::Any* any);

  // @@protoc_insertion_point(class_scope:google.protobuf.testing.AnyOut)
 private:

  ::google::protobuf::internal::InternalMetadataWithArena _internal_metadata_;
  ::google::protobuf::Any* any_;
  mutable int _cached_size_;
  friend void  protobuf_InitDefaults_google_2fprotobuf_2futil_2finternal_2ftestdata_2fanys_2eproto_impl();
  friend void  protobuf_AddDesc_google_2fprotobuf_2futil_2finternal_2ftestdata_2fanys_2eproto_impl();
  friend void protobuf_AssignDesc_google_2fprotobuf_2futil_2finternal_2ftestdata_2fanys_2eproto();
  friend void protobuf_ShutdownFile_google_2fprotobuf_2futil_2finternal_2ftestdata_2fanys_2eproto();

  void InitAsDefaultInstance();
};
extern ::google::protobuf::internal::ExplicitlyConstructed<AnyOut> AnyOut_default_instance_;

// -------------------------------------------------------------------

class AnyM : public ::google::protobuf::Message /* @@protoc_insertion_point(class_definition:google.protobuf.testing.AnyM) */ {
 public:
  AnyM();
  virtual ~AnyM();

  AnyM(const AnyM& from);

  inline AnyM& operator=(const AnyM& from) {
    CopyFrom(from);
    return *this;
  }

  static const ::google::protobuf::Descriptor* descriptor();
  static const AnyM& default_instance();

  static const AnyM* internal_default_instance();

  void Swap(AnyM* other);

  // implements Message ----------------------------------------------

  inline AnyM* New() const { return New(NULL); }

  AnyM* New(::google::protobuf::Arena* arena) const;
  void CopyFrom(const ::google::protobuf::Message& from);
  void MergeFrom(const ::google::protobuf::Message& from);
  void CopyFrom(const AnyM& from);
  void MergeFrom(const AnyM& from);
  void Clear();
  bool IsInitialized() const;

  size_t ByteSizeLong() const;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input);
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const;
  ::google::protobuf::uint8* InternalSerializeWithCachedSizesToArray(
      bool deterministic, ::google::protobuf::uint8* output) const;
  ::google::protobuf::uint8* SerializeWithCachedSizesToArray(::google::protobuf::uint8* output) const {
    return InternalSerializeWithCachedSizesToArray(false, output);
  }
  int GetCachedSize() const { return _cached_size_; }
  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const;
  void InternalSwap(AnyM* other);
  void UnsafeMergeFrom(const AnyM& from);
  private:
  inline ::google::protobuf::Arena* GetArenaNoVirtual() const {
    return _internal_metadata_.arena();
  }
  inline void* MaybeArenaPtr() const {
    return _internal_metadata_.raw_arena_ptr();
  }
  public:

  ::google::protobuf::Metadata GetMetadata() const;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // optional string foo = 1;
  void clear_foo();
  static const int kFooFieldNumber = 1;
  const ::std::string& foo() const;
  void set_foo(const ::std::string& value);
  void set_foo(const char* value);
  void set_foo(const char* value, size_t size);
  ::std::string* mutable_foo();
  ::std::string* release_foo();
  void set_allocated_foo(::std::string* foo);

  // @@protoc_insertion_point(class_scope:google.protobuf.testing.AnyM)
 private:

  ::google::protobuf::internal::InternalMetadataWithArena _internal_metadata_;
  ::google::protobuf::internal::ArenaStringPtr foo_;
  mutable int _cached_size_;
  friend void  protobuf_InitDefaults_google_2fprotobuf_2futil_2finternal_2ftestdata_2fanys_2eproto_impl();
  friend void  protobuf_AddDesc_google_2fprotobuf_2futil_2finternal_2ftestdata_2fanys_2eproto_impl();
  friend void protobuf_AssignDesc_google_2fprotobuf_2futil_2finternal_2ftestdata_2fanys_2eproto();
  friend void protobuf_ShutdownFile_google_2fprotobuf_2futil_2finternal_2ftestdata_2fanys_2eproto();

  void InitAsDefaultInstance();
};
extern ::google::protobuf::internal::ExplicitlyConstructed<AnyM> AnyM_default_instance_;

// ===================================================================


// ===================================================================

#if !PROTOBUF_INLINE_NOT_IN_HEADERS
// AnyTestCases

// optional .google.protobuf.testing.AnyWrapper empty_any = 1;
inline bool AnyTestCases::has_empty_any() const {
  return this != internal_default_instance() && empty_any_ != NULL;
}
inline void AnyTestCases::clear_empty_any() {
  if (GetArenaNoVirtual() == NULL && empty_any_ != NULL) delete empty_any_;
  empty_any_ = NULL;
}
inline const ::google::protobuf::testing::AnyWrapper& AnyTestCases::empty_any() const {
  // @@protoc_insertion_point(field_get:google.protobuf.testing.AnyTestCases.empty_any)
  return empty_any_ != NULL ? *empty_any_
                         : *::google::protobuf::testing::AnyWrapper::internal_default_instance();
}
inline ::google::protobuf::testing::AnyWrapper* AnyTestCases::mutable_empty_any() {
  
  if (empty_any_ == NULL) {
    empty_any_ = new ::google::protobuf::testing::AnyWrapper;
  }
  // @@protoc_insertion_point(field_mutable:google.protobuf.testing.AnyTestCases.empty_any)
  return empty_any_;
}
inline ::google::protobuf::testing::AnyWrapper* AnyTestCases::release_empty_any() {
  // @@protoc_insertion_point(field_release:google.protobuf.testing.AnyTestCases.empty_any)
  
  ::google::protobuf::testing::AnyWrapper* temp = empty_any_;
  empty_any_ = NULL;
  return temp;
}
inline void AnyTestCases::set_allocated_empty_any(::google::protobuf::testing::AnyWrapper* empty_any) {
  delete empty_any_;
  empty_any_ = empty_any;
  if (empty_any) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_set_allocated:google.protobuf.testing.AnyTestCases.empty_any)
}

// optional .google.protobuf.testing.AnyWrapper type_only_any = 2;
inline bool AnyTestCases::has_type_only_any() const {
  return this != internal_default_instance() && type_only_any_ != NULL;
}
inline void AnyTestCases::clear_type_only_any() {
  if (GetArenaNoVirtual() == NULL && type_only_any_ != NULL) delete type_only_any_;
  type_only_any_ = NULL;
}
inline const ::google::protobuf::testing::AnyWrapper& AnyTestCases::type_only_any() const {
  // @@protoc_insertion_point(field_get:google.protobuf.testing.AnyTestCases.type_only_any)
  return type_only_any_ != NULL ? *type_only_any_
                         : *::google::protobuf::testing::AnyWrapper::internal_default_instance();
}
inline ::google::protobuf::testing::AnyWrapper* AnyTestCases::mutable_type_only_any() {
  
  if (type_only_any_ == NULL) {
    type_only_any_ = new ::google::protobuf::testing::AnyWrapper;
  }
  // @@protoc_insertion_point(field_mutable:google.protobuf.testing.AnyTestCases.type_only_any)
  return type_only_any_;
}
inline ::google::protobuf::testing::AnyWrapper* AnyTestCases::release_type_only_any() {
  // @@protoc_insertion_point(field_release:google.protobuf.testing.AnyTestCases.type_only_any)
  
  ::google::protobuf::testing::AnyWrapper* temp = type_only_any_;
  type_only_any_ = NULL;
  return temp;
}
inline void AnyTestCases::set_allocated_type_only_any(::google::protobuf::testing::AnyWrapper* type_only_any) {
  delete type_only_any_;
  type_only_any_ = type_only_any;
  if (type_only_any) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_set_allocated:google.protobuf.testing.AnyTestCases.type_only_any)
}

// optional .google.protobuf.testing.AnyWrapper wrapper_any = 3;
inline bool AnyTestCases::has_wrapper_any() const {
  return this != internal_default_instance() && wrapper_any_ != NULL;
}
inline void AnyTestCases::clear_wrapper_any() {
  if (GetArenaNoVirtual() == NULL && wrapper_any_ != NULL) delete wrapper_any_;
  wrapper_any_ = NULL;
}
inline const ::google::protobuf::testing::AnyWrapper& AnyTestCases::wrapper_any() const {
  // @@protoc_insertion_point(field_get:google.protobuf.testing.AnyTestCases.wrapper_any)
  return wrapper_any_ != NULL ? *wrapper_any_
                         : *::google::protobuf::testing::AnyWrapper::internal_default_instance();
}
inline ::google::protobuf::testing::AnyWrapper* AnyTestCases::mutable_wrapper_any() {
  
  if (wrapper_any_ == NULL) {
    wrapper_any_ = new ::google::protobuf::testing::AnyWrapper;
  }
  // @@protoc_insertion_point(field_mutable:google.protobuf.testing.AnyTestCases.wrapper_any)
  return wrapper_any_;
}
inline ::google::protobuf::testing::AnyWrapper* AnyTestCases::release_wrapper_any() {
  // @@protoc_insertion_point(field_release:google.protobuf.testing.AnyTestCases.wrapper_any)
  
  ::google::protobuf::testing::AnyWrapper* temp = wrapper_any_;
  wrapper_any_ = NULL;
  return temp;
}
inline void AnyTestCases::set_allocated_wrapper_any(::google::protobuf::testing::AnyWrapper* wrapper_any) {
  delete wrapper_any_;
  wrapper_any_ = wrapper_any;
  if (wrapper_any) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_set_allocated:google.protobuf.testing.AnyTestCases.wrapper_any)
}

// optional .google.protobuf.testing.AnyWrapper any_with_timestamp_value = 4;
inline bool AnyTestCases::has_any_with_timestamp_value() const {
  return this != internal_default_instance() && any_with_timestamp_value_ != NULL;
}
inline void AnyTestCases::clear_any_with_timestamp_value() {
  if (GetArenaNoVirtual() == NULL && any_with_timestamp_value_ != NULL) delete any_with_timestamp_value_;
  any_with_timestamp_value_ = NULL;
}
inline const ::google::protobuf::testing::AnyWrapper& AnyTestCases::any_with_timestamp_value() const {
  // @@protoc_insertion_point(field_get:google.protobuf.testing.AnyTestCases.any_with_timestamp_value)
  return any_with_timestamp_value_ != NULL ? *any_with_timestamp_value_
                         : *::google::protobuf::testing::AnyWrapper::internal_default_instance();
}
inline ::google::protobuf::testing::AnyWrapper* AnyTestCases::mutable_any_with_timestamp_value() {
  
  if (any_with_timestamp_value_ == NULL) {
    any_with_timestamp_value_ = new ::google::protobuf::testing::AnyWrapper;
  }
  // @@protoc_insertion_point(field_mutable:google.protobuf.testing.AnyTestCases.any_with_timestamp_value)
  return any_with_timestamp_value_;
}
inline ::google::protobuf::testing::AnyWrapper* AnyTestCases::release_any_with_timestamp_value() {
  // @@protoc_insertion_point(field_release:google.protobuf.testing.AnyTestCases.any_with_timestamp_value)
  
  ::google::protobuf::testing::AnyWrapper* temp = any_with_timestamp_value_;
  any_with_timestamp_value_ = NULL;
  return temp;
}
inline void AnyTestCases::set_allocated_any_with_timestamp_value(::google::protobuf::testing::AnyWrapper* any_with_timestamp_value) {
  delete any_with_timestamp_value_;
  any_with_timestamp_value_ = any_with_timestamp_value;
  if (any_with_timestamp_value) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_set_allocated:google.protobuf.testing.AnyTestCases.any_with_timestamp_value)
}

// optional .google.protobuf.testing.AnyWrapper any_with_duration_value = 5;
inline bool AnyTestCases::has_any_with_duration_value() const {
  return this != internal_default_instance() && any_with_duration_value_ != NULL;
}
inline void AnyTestCases::clear_any_with_duration_value() {
  if (GetArenaNoVirtual() == NULL && any_with_duration_value_ != NULL) delete any_with_duration_value_;
  any_with_duration_value_ = NULL;
}
inline const ::google::protobuf::testing::AnyWrapper& AnyTestCases::any_with_duration_value() const {
  // @@protoc_insertion_point(field_get:google.protobuf.testing.AnyTestCases.any_with_duration_value)
  return any_with_duration_value_ != NULL ? *any_with_duration_value_
                         : *::google::protobuf::testing::AnyWrapper::internal_default_instance();
}
inline ::google::protobuf::testing::AnyWrapper* AnyTestCases::mutable_any_with_duration_value() {
  
  if (any_with_duration_value_ == NULL) {
    any_with_duration_value_ = new ::google::protobuf::testing::AnyWrapper;
  }
  // @@protoc_insertion_point(field_mutable:google.protobuf.testing.AnyTestCases.any_with_duration_value)
  return any_with_duration_value_;
}
inline ::google::protobuf::testing::AnyWrapper* AnyTestCases::release_any_with_duration_value() {
  // @@protoc_insertion_point(field_release:google.protobuf.testing.AnyTestCases.any_with_duration_value)
  
  ::google::protobuf::testing::AnyWrapper* temp = any_with_duration_value_;
  any_with_duration_value_ = NULL;
  return temp;
}
inline void AnyTestCases::set_allocated_any_with_duration_value(::google::protobuf::testing::AnyWrapper* any_with_duration_value) {
  delete any_with_duration_value_;
  any_with_duration_value_ = any_with_duration_value;
  if (any_with_duration_value) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_set_allocated:google.protobuf.testing.AnyTestCases.any_with_duration_value)
}

// optional .google.protobuf.testing.AnyWrapper any_with_struct_value = 6;
inline bool AnyTestCases::has_any_with_struct_value() const {
  return this != internal_default_instance() && any_with_struct_value_ != NULL;
}
inline void AnyTestCases::clear_any_with_struct_value() {
  if (GetArenaNoVirtual() == NULL && any_with_struct_value_ != NULL) delete any_with_struct_value_;
  any_with_struct_value_ = NULL;
}
inline const ::google::protobuf::testing::AnyWrapper& AnyTestCases::any_with_struct_value() const {
  // @@protoc_insertion_point(field_get:google.protobuf.testing.AnyTestCases.any_with_struct_value)
  return any_with_struct_value_ != NULL ? *any_with_struct_value_
                         : *::google::protobuf::testing::AnyWrapper::internal_default_instance();
}
inline ::google::protobuf::testing::AnyWrapper* AnyTestCases::mutable_any_with_struct_value() {
  
  if (any_with_struct_value_ == NULL) {
    any_with_struct_value_ = new ::google::protobuf::testing::AnyWrapper;
  }
  // @@protoc_insertion_point(field_mutable:google.protobuf.testing.AnyTestCases.any_with_struct_value)
  return any_with_struct_value_;
}
inline ::google::protobuf::testing::AnyWrapper* AnyTestCases::release_any_with_struct_value() {
  // @@protoc_insertion_point(field_release:google.protobuf.testing.AnyTestCases.any_with_struct_value)
  
  ::google::protobuf::testing::AnyWrapper* temp = any_with_struct_value_;
  any_with_struct_value_ = NULL;
  return temp;
}
inline void AnyTestCases::set_allocated_any_with_struct_value(::google::protobuf::testing::AnyWrapper* any_with_struct_value) {
  delete any_with_struct_value_;
  any_with_struct_value_ = any_with_struct_value;
  if (any_with_struct_value) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_set_allocated:google.protobuf.testing.AnyTestCases.any_with_struct_value)
}

// optional .google.protobuf.testing.AnyWrapper recursive_any = 7;
inline bool AnyTestCases::has_recursive_any() const {
  return this != internal_default_instance() && recursive_any_ != NULL;
}
inline void AnyTestCases::clear_recursive_any() {
  if (GetArenaNoVirtual() == NULL && recursive_any_ != NULL) delete recursive_any_;
  recursive_any_ = NULL;
}
inline const ::google::protobuf::testing::AnyWrapper& AnyTestCases::recursive_any() const {
  // @@protoc_insertion_point(field_get:google.protobuf.testing.AnyTestCases.recursive_any)
  return recursive_any_ != NULL ? *recursive_any_
                         : *::google::protobuf::testing::AnyWrapper::internal_default_instance();
}
inline ::google::protobuf::testing::AnyWrapper* AnyTestCases::mutable_recursive_any() {
  
  if (recursive_any_ == NULL) {
    recursive_any_ = new ::google::protobuf::testing::AnyWrapper;
  }
  // @@protoc_insertion_point(field_mutable:google.protobuf.testing.AnyTestCases.recursive_any)
  return recursive_any_;
}
inline ::google::protobuf::testing::AnyWrapper* AnyTestCases::release_recursive_any() {
  // @@protoc_insertion_point(field_release:google.protobuf.testing.AnyTestCases.recursive_any)
  
  ::google::protobuf::testing::AnyWrapper* temp = recursive_any_;
  recursive_any_ = NULL;
  return temp;
}
inline void AnyTestCases::set_allocated_recursive_any(::google::protobuf::testing::AnyWrapper* recursive_any) {
  delete recursive_any_;
  recursive_any_ = recursive_any;
  if (recursive_any) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_set_allocated:google.protobuf.testing.AnyTestCases.recursive_any)
}

// optional .google.protobuf.testing.AnyWrapper any_with_message_value = 8;
inline bool AnyTestCases::has_any_with_message_value() const {
  return this != internal_default_instance() && any_with_message_value_ != NULL;
}
inline void AnyTestCases::clear_any_with_message_value() {
  if (GetArenaNoVirtual() == NULL && any_with_message_value_ != NULL) delete any_with_message_value_;
  any_with_message_value_ = NULL;
}
inline const ::google::protobuf::testing::AnyWrapper& AnyTestCases::any_with_message_value() const {
  // @@protoc_insertion_point(field_get:google.protobuf.testing.AnyTestCases.any_with_message_value)
  return any_with_message_value_ != NULL ? *any_with_message_value_
                         : *::google::protobuf::testing::AnyWrapper::internal_default_instance();
}
inline ::google::protobuf::testing::AnyWrapper* AnyTestCases::mutable_any_with_message_value() {
  
  if (any_with_message_value_ == NULL) {
    any_with_message_value_ = new ::google::protobuf::testing::AnyWrapper;
  }
  // @@protoc_insertion_point(field_mutable:google.protobuf.testing.AnyTestCases.any_with_message_value)
  return any_with_message_value_;
}
inline ::google::protobuf::testing::AnyWrapper* AnyTestCases::release_any_with_message_value() {
  // @@protoc_insertion_point(field_release:google.protobuf.testing.AnyTestCases.any_with_message_value)
  
  ::google::protobuf::testing::AnyWrapper* temp = any_with_message_value_;
  any_with_message_value_ = NULL;
  return temp;
}
inline void AnyTestCases::set_allocated_any_with_message_value(::google::protobuf::testing::AnyWrapper* any_with_message_value) {
  delete any_with_message_value_;
  any_with_message_value_ = any_with_message_value;
  if (any_with_message_value) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_set_allocated:google.protobuf.testing.AnyTestCases.any_with_message_value)
}

// optional .google.protobuf.testing.AnyWrapper any_with_nested_message = 9;
inline bool AnyTestCases::has_any_with_nested_message() const {
  return this != internal_default_instance() && any_with_nested_message_ != NULL;
}
inline void AnyTestCases::clear_any_with_nested_message() {
  if (GetArenaNoVirtual() == NULL && any_with_nested_message_ != NULL) delete any_with_nested_message_;
  any_with_nested_message_ = NULL;
}
inline const ::google::protobuf::testing::AnyWrapper& AnyTestCases::any_with_nested_message() const {
  // @@protoc_insertion_point(field_get:google.protobuf.testing.AnyTestCases.any_with_nested_message)
  return any_with_nested_message_ != NULL ? *any_with_nested_message_
                         : *::google::protobuf::testing::AnyWrapper::internal_default_instance();
}
inline ::google::protobuf::testing::AnyWrapper* AnyTestCases::mutable_any_with_nested_message() {
  
  if (any_with_nested_message_ == NULL) {
    any_with_nested_message_ = new ::google::protobuf::testing::AnyWrapper;
  }
  // @@protoc_insertion_point(field_mutable:google.protobuf.testing.AnyTestCases.any_with_nested_message)
  return any_with_nested_message_;
}
inline ::google::protobuf::testing::AnyWrapper* AnyTestCases::release_any_with_nested_message() {
  // @@protoc_insertion_point(field_release:google.protobuf.testing.AnyTestCases.any_with_nested_message)
  
  ::google::protobuf::testing::AnyWrapper* temp = any_with_nested_message_;
  any_with_nested_message_ = NULL;
  return temp;
}
inline void AnyTestCases::set_allocated_any_with_nested_message(::google::protobuf::testing::AnyWrapper* any_with_nested_message) {
  delete any_with_nested_message_;
  any_with_nested_message_ = any_with_nested_message;
  if (any_with_nested_message) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_set_allocated:google.protobuf.testing.AnyTestCases.any_with_nested_message)
}

// optional .google.protobuf.testing.AnyWrapper any_with_message_with_wrapper_type = 10;
inline bool AnyTestCases::has_any_with_message_with_wrapper_type() const {
  return this != internal_default_instance() && any_with_message_with_wrapper_type_ != NULL;
}
inline void AnyTestCases::clear_any_with_message_with_wrapper_type() {
  if (GetArenaNoVirtual() == NULL && any_with_message_with_wrapper_type_ != NULL) delete any_with_message_with_wrapper_type_;
  any_with_message_with_wrapper_type_ = NULL;
}
inline const ::google::protobuf::testing::AnyWrapper& AnyTestCases::any_with_message_with_wrapper_type() const {
  // @@protoc_insertion_point(field_get:google.protobuf.testing.AnyTestCases.any_with_message_with_wrapper_type)
  return any_with_message_with_wrapper_type_ != NULL ? *any_with_message_with_wrapper_type_
                         : *::google::protobuf::testing::AnyWrapper::internal_default_instance();
}
inline ::google::protobuf::testing::AnyWrapper* AnyTestCases::mutable_any_with_message_with_wrapper_type() {
  
  if (any_with_message_with_wrapper_type_ == NULL) {
    any_with_message_with_wrapper_type_ = new ::google::protobuf::testing::AnyWrapper;
  }
  // @@protoc_insertion_point(field_mutable:google.protobuf.testing.AnyTestCases.any_with_message_with_wrapper_type)
  return any_with_message_with_wrapper_type_;
}
inline ::google::protobuf::testing::AnyWrapper* AnyTestCases::release_any_with_message_with_wrapper_type() {
  // @@protoc_insertion_point(field_release:google.protobuf.testing.AnyTestCases.any_with_message_with_wrapper_type)
  
  ::google::protobuf::testing::AnyWrapper* temp = any_with_message_with_wrapper_type_;
  any_with_message_with_wrapper_type_ = NULL;
  return temp;
}
inline void AnyTestCases::set_allocated_any_with_message_with_wrapper_type(::google::protobuf::testing::AnyWrapper* any_with_message_with_wrapper_type) {
  delete any_with_message_with_wrapper_type_;
  any_with_message_with_wrapper_type_ = any_with_message_with_wrapper_type;
  if (any_with_message_with_wrapper_type) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_set_allocated:google.protobuf.testing.AnyTestCases.any_with_message_with_wrapper_type)
}

// optional .google.protobuf.testing.AnyWrapper any_with_message_with_timestamp = 11;
inline bool AnyTestCases::has_any_with_message_with_timestamp() const {
  return this != internal_default_instance() && any_with_message_with_timestamp_ != NULL;
}
inline void AnyTestCases::clear_any_with_message_with_timestamp() {
  if (GetArenaNoVirtual() == NULL && any_with_message_with_timestamp_ != NULL) delete any_with_message_with_timestamp_;
  any_with_message_with_timestamp_ = NULL;
}
inline const ::google::protobuf::testing::AnyWrapper& AnyTestCases::any_with_message_with_timestamp() const {
  // @@protoc_insertion_point(field_get:google.protobuf.testing.AnyTestCases.any_with_message_with_timestamp)
  return any_with_message_with_timestamp_ != NULL ? *any_with_message_with_timestamp_
                         : *::google::protobuf::testing::AnyWrapper::internal_default_instance();
}
inline ::google::protobuf::testing::AnyWrapper* AnyTestCases::mutable_any_with_message_with_timestamp() {
  
  if (any_with_message_with_timestamp_ == NULL) {
    any_with_message_with_timestamp_ = new ::google::protobuf::testing::AnyWrapper;
  }
  // @@protoc_insertion_point(field_mutable:google.protobuf.testing.AnyTestCases.any_with_message_with_timestamp)
  return any_with_message_with_timestamp_;
}
inline ::google::protobuf::testing::AnyWrapper* AnyTestCases::release_any_with_message_with_timestamp() {
  // @@protoc_insertion_point(field_release:google.protobuf.testing.AnyTestCases.any_with_message_with_timestamp)
  
  ::google::protobuf::testing::AnyWrapper* temp = any_with_message_with_timestamp_;
  any_with_message_with_timestamp_ = NULL;
  return temp;
}
inline void AnyTestCases::set_allocated_any_with_message_with_timestamp(::google::protobuf::testing::AnyWrapper* any_with_message_with_timestamp) {
  delete any_with_message_with_timestamp_;
  any_with_message_with_timestamp_ = any_with_message_with_timestamp;
  if (any_with_message_with_timestamp) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_set_allocated:google.protobuf.testing.AnyTestCases.any_with_message_with_timestamp)
}

// optional .google.protobuf.testing.AnyWrapper any_with_message_containing_map = 12;
inline bool AnyTestCases::has_any_with_message_containing_map() const {
  return this != internal_default_instance() && any_with_message_containing_map_ != NULL;
}
inline void AnyTestCases::clear_any_with_message_containing_map() {
  if (GetArenaNoVirtual() == NULL && any_with_message_containing_map_ != NULL) delete any_with_message_containing_map_;
  any_with_message_containing_map_ = NULL;
}
inline const ::google::protobuf::testing::AnyWrapper& AnyTestCases::any_with_message_containing_map() const {
  // @@protoc_insertion_point(field_get:google.protobuf.testing.AnyTestCases.any_with_message_containing_map)
  return any_with_message_containing_map_ != NULL ? *any_with_message_containing_map_
                         : *::google::protobuf::testing::AnyWrapper::internal_default_instance();
}
inline ::google::protobuf::testing::AnyWrapper* AnyTestCases::mutable_any_with_message_containing_map() {
  
  if (any_with_message_containing_map_ == NULL) {
    any_with_message_containing_map_ = new ::google::protobuf::testing::AnyWrapper;
  }
  // @@protoc_insertion_point(field_mutable:google.protobuf.testing.AnyTestCases.any_with_message_containing_map)
  return any_with_message_containing_map_;
}
inline ::google::protobuf::testing::AnyWrapper* AnyTestCases::release_any_with_message_containing_map() {
  // @@protoc_insertion_point(field_release:google.protobuf.testing.AnyTestCases.any_with_message_containing_map)
  
  ::google::protobuf::testing::AnyWrapper* temp = any_with_message_containing_map_;
  any_with_message_containing_map_ = NULL;
  return temp;
}
inline void AnyTestCases::set_allocated_any_with_message_containing_map(::google::protobuf::testing::AnyWrapper* any_with_message_containing_map) {
  delete any_with_message_containing_map_;
  any_with_message_containing_map_ = any_with_message_containing_map;
  if (any_with_message_containing_map) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_set_allocated:google.protobuf.testing.AnyTestCases.any_with_message_containing_map)
}

// optional .google.protobuf.testing.AnyWrapper any_with_message_containing_struct = 13;
inline bool AnyTestCases::has_any_with_message_containing_struct() const {
  return this != internal_default_instance() && any_with_message_containing_struct_ != NULL;
}
inline void AnyTestCases::clear_any_with_message_containing_struct() {
  if (GetArenaNoVirtual() == NULL && any_with_message_containing_struct_ != NULL) delete any_with_message_containing_struct_;
  any_with_message_containing_struct_ = NULL;
}
inline const ::google::protobuf::testing::AnyWrapper& AnyTestCases::any_with_message_containing_struct() const {
  // @@protoc_insertion_point(field_get:google.protobuf.testing.AnyTestCases.any_with_message_containing_struct)
  return any_with_message_containing_struct_ != NULL ? *any_with_message_containing_struct_
                         : *::google::protobuf::testing::AnyWrapper::internal_default_instance();
}
inline ::google::protobuf::testing::AnyWrapper* AnyTestCases::mutable_any_with_message_containing_struct() {
  
  if (any_with_message_containing_struct_ == NULL) {
    any_with_message_containing_struct_ = new ::google::protobuf::testing::AnyWrapper;
  }
  // @@protoc_insertion_point(field_mutable:google.protobuf.testing.AnyTestCases.any_with_message_containing_struct)
  return any_with_message_containing_struct_;
}
inline ::google::protobuf::testing::AnyWrapper* AnyTestCases::release_any_with_message_containing_struct() {
  // @@protoc_insertion_point(field_release:google.protobuf.testing.AnyTestCases.any_with_message_containing_struct)
  
  ::google::protobuf::testing::AnyWrapper* temp = any_with_message_containing_struct_;
  any_with_message_containing_struct_ = NULL;
  return temp;
}
inline void AnyTestCases::set_allocated_any_with_message_containing_struct(::google::protobuf::testing::AnyWrapper* any_with_message_containing_struct) {
  delete any_with_message_containing_struct_;
  any_with_message_containing_struct_ = any_with_message_containing_struct;
  if (any_with_message_containing_struct) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_set_allocated:google.protobuf.testing.AnyTestCases.any_with_message_containing_struct)
}

// optional .google.protobuf.testing.AnyWrapper any_with_message_containing_repeated_message = 14;
inline bool AnyTestCases::has_any_with_message_containing_repeated_message() const {
  return this != internal_default_instance() && any_with_message_containing_repeated_message_ != NULL;
}
inline void AnyTestCases::clear_any_with_message_containing_repeated_message() {
  if (GetArenaNoVirtual() == NULL && any_with_message_containing_repeated_message_ != NULL) delete any_with_message_containing_repeated_message_;
  any_with_message_containing_repeated_message_ = NULL;
}
inline const ::google::protobuf::testing::AnyWrapper& AnyTestCases::any_with_message_containing_repeated_message() const {
  // @@protoc_insertion_point(field_get:google.protobuf.testing.AnyTestCases.any_with_message_containing_repeated_message)
  return any_with_message_containing_repeated_message_ != NULL ? *any_with_message_containing_repeated_message_
                         : *::google::protobuf::testing::AnyWrapper::internal_default_instance();
}
inline ::google::protobuf::testing::AnyWrapper* AnyTestCases::mutable_any_with_message_containing_repeated_message() {
  
  if (any_with_message_containing_repeated_message_ == NULL) {
    any_with_message_containing_repeated_message_ = new ::google::protobuf::testing::AnyWrapper;
  }
  // @@protoc_insertion_point(field_mutable:google.protobuf.testing.AnyTestCases.any_with_message_containing_repeated_message)
  return any_with_message_containing_repeated_message_;
}
inline ::google::protobuf::testing::AnyWrapper* AnyTestCases::release_any_with_message_containing_repeated_message() {
  // @@protoc_insertion_point(field_release:google.protobuf.testing.AnyTestCases.any_with_message_containing_repeated_message)
  
  ::google::protobuf::testing::AnyWrapper* temp = any_with_message_containing_repeated_message_;
  any_with_message_containing_repeated_message_ = NULL;
  return temp;
}
inline void AnyTestCases::set_allocated_any_with_message_containing_repeated_message(::google::protobuf::testing::AnyWrapper* any_with_message_containing_repeated_message) {
  delete any_with_message_containing_repeated_message_;
  any_with_message_containing_repeated_message_ = any_with_message_containing_repeated_message;
  if (any_with_message_containing_repeated_message) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_set_allocated:google.protobuf.testing.AnyTestCases.any_with_message_containing_repeated_message)
}

// optional .google.protobuf.testing.AnyWrapper recursive_any_with_type_field_at_end = 15;
inline bool AnyTestCases::has_recursive_any_with_type_field_at_end() const {
  return this != internal_default_instance() && recursive_any_with_type_field_at_end_ != NULL;
}
inline void AnyTestCases::clear_recursive_any_with_type_field_at_end() {
  if (GetArenaNoVirtual() == NULL && recursive_any_with_type_field_at_end_ != NULL) delete recursive_any_with_type_field_at_end_;
  recursive_any_with_type_field_at_end_ = NULL;
}
inline const ::google::protobuf::testing::AnyWrapper& AnyTestCases::recursive_any_with_type_field_at_end() const {
  // @@protoc_insertion_point(field_get:google.protobuf.testing.AnyTestCases.recursive_any_with_type_field_at_end)
  return recursive_any_with_type_field_at_end_ != NULL ? *recursive_any_with_type_field_at_end_
                         : *::google::protobuf::testing::AnyWrapper::internal_default_instance();
}
inline ::google::protobuf::testing::AnyWrapper* AnyTestCases::mutable_recursive_any_with_type_field_at_end() {
  
  if (recursive_any_with_type_field_at_end_ == NULL) {
    recursive_any_with_type_field_at_end_ = new ::google::protobuf::testing::AnyWrapper;
  }
  // @@protoc_insertion_point(field_mutable:google.protobuf.testing.AnyTestCases.recursive_any_with_type_field_at_end)
  return recursive_any_with_type_field_at_end_;
}
inline ::google::protobuf::testing::AnyWrapper* AnyTestCases::release_recursive_any_with_type_field_at_end() {
  // @@protoc_insertion_point(field_release:google.protobuf.testing.AnyTestCases.recursive_any_with_type_field_at_end)
  
  ::google::protobuf::testing::AnyWrapper* temp = recursive_any_with_type_field_at_end_;
  recursive_any_with_type_field_at_end_ = NULL;
  return temp;
}
inline void AnyTestCases::set_allocated_recursive_any_with_type_field_at_end(::google::protobuf::testing::AnyWrapper* recursive_any_with_type_field_at_end) {
  delete recursive_any_with_type_field_at_end_;
  recursive_any_with_type_field_at_end_ = recursive_any_with_type_field_at_end;
  if (recursive_any_with_type_field_at_end) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_set_allocated:google.protobuf.testing.AnyTestCases.recursive_any_with_type_field_at_end)
}

// optional .google.protobuf.Any top_level_any = 50;
inline bool AnyTestCases::has_top_level_any() const {
  return this != internal_default_instance() && top_level_any_ != NULL;
}
inline void AnyTestCases::clear_top_level_any() {
  if (GetArenaNoVirtual() == NULL && top_level_any_ != NULL) delete top_level_any_;
  top_level_any_ = NULL;
}
inline const ::google::protobuf::Any& AnyTestCases::top_level_any() const {
  // @@protoc_insertion_point(field_get:google.protobuf.testing.AnyTestCases.top_level_any)
  return top_level_any_ != NULL ? *top_level_any_
                         : *::google::protobuf::Any::internal_default_instance();
}
inline ::google::protobuf::Any* AnyTestCases::mutable_top_level_any() {
  
  if (top_level_any_ == NULL) {
    top_level_any_ = new ::google::protobuf::Any;
  }
  // @@protoc_insertion_point(field_mutable:google.protobuf.testing.AnyTestCases.top_level_any)
  return top_level_any_;
}
inline ::google::protobuf::Any* AnyTestCases::release_top_level_any() {
  // @@protoc_insertion_point(field_release:google.protobuf.testing.AnyTestCases.top_level_any)
  
  ::google::protobuf::Any* temp = top_level_any_;
  top_level_any_ = NULL;
  return temp;
}
inline void AnyTestCases::set_allocated_top_level_any(::google::protobuf::Any* top_level_any) {
  delete top_level_any_;
  top_level_any_ = top_level_any;
  if (top_level_any) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_set_allocated:google.protobuf.testing.AnyTestCases.top_level_any)
}

// optional .google.protobuf.Any top_level_any_with_type_field_at_end = 51;
inline bool AnyTestCases::has_top_level_any_with_type_field_at_end() const {
  return this != internal_default_instance() && top_level_any_with_type_field_at_end_ != NULL;
}
inline void AnyTestCases::clear_top_level_any_with_type_field_at_end() {
  if (GetArenaNoVirtual() == NULL && top_level_any_with_type_field_at_end_ != NULL) delete top_level_any_with_type_field_at_end_;
  top_level_any_with_type_field_at_end_ = NULL;
}
inline const ::google::protobuf::Any& AnyTestCases::top_level_any_with_type_field_at_end() const {
  // @@protoc_insertion_point(field_get:google.protobuf.testing.AnyTestCases.top_level_any_with_type_field_at_end)
  return top_level_any_with_type_field_at_end_ != NULL ? *top_level_any_with_type_field_at_end_
                         : *::google::protobuf::Any::internal_default_instance();
}
inline ::google::protobuf::Any* AnyTestCases::mutable_top_level_any_with_type_field_at_end() {
  
  if (top_level_any_with_type_field_at_end_ == NULL) {
    top_level_any_with_type_field_at_end_ = new ::google::protobuf::Any;
  }
  // @@protoc_insertion_point(field_mutable:google.protobuf.testing.AnyTestCases.top_level_any_with_type_field_at_end)
  return top_level_any_with_type_field_at_end_;
}
inline ::google::protobuf::Any* AnyTestCases::release_top_level_any_with_type_field_at_end() {
  // @@protoc_insertion_point(field_release:google.protobuf.testing.AnyTestCases.top_level_any_with_type_field_at_end)
  
  ::google::protobuf::Any* temp = top_level_any_with_type_field_at_end_;
  top_level_any_with_type_field_at_end_ = NULL;
  return temp;
}
inline void AnyTestCases::set_allocated_top_level_any_with_type_field_at_end(::google::protobuf::Any* top_level_any_with_type_field_at_end) {
  delete top_level_any_with_type_field_at_end_;
  top_level_any_with_type_field_at_end_ = top_level_any_with_type_field_at_end;
  if (top_level_any_with_type_field_at_end) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_set_allocated:google.protobuf.testing.AnyTestCases.top_level_any_with_type_field_at_end)
}

inline const AnyTestCases* AnyTestCases::internal_default_instance() {
  return &AnyTestCases_default_instance_.get();
}
// -------------------------------------------------------------------

// AnyWrapper

// optional .google.protobuf.Any any = 1;
inline bool AnyWrapper::has_any() const {
  return this != internal_default_instance() && any_ != NULL;
}
inline void AnyWrapper::clear_any() {
  if (GetArenaNoVirtual() == NULL && any_ != NULL) delete any_;
  any_ = NULL;
}
inline const ::google::protobuf::Any& AnyWrapper::any() const {
  // @@protoc_insertion_point(field_get:google.protobuf.testing.AnyWrapper.any)
  return any_ != NULL ? *any_
                         : *::google::protobuf::Any::internal_default_instance();
}
inline ::google::protobuf::Any* AnyWrapper::mutable_any() {
  
  if (any_ == NULL) {
    any_ = new ::google::protobuf::Any;
  }
  // @@protoc_insertion_point(field_mutable:google.protobuf.testing.AnyWrapper.any)
  return any_;
}
inline ::google::protobuf::Any* AnyWrapper::release_any() {
  // @@protoc_insertion_point(field_release:google.protobuf.testing.AnyWrapper.any)
  
  ::google::protobuf::Any* temp = any_;
  any_ = NULL;
  return temp;
}
inline void AnyWrapper::set_allocated_any(::google::protobuf::Any* any) {
  delete any_;
  any_ = any;
  if (any) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_set_allocated:google.protobuf.testing.AnyWrapper.any)
}

inline const AnyWrapper* AnyWrapper::internal_default_instance() {
  return &AnyWrapper_default_instance_.get();
}
// -------------------------------------------------------------------

// Imports

// optional .google.protobuf.DoubleValue dbl = 1;
inline bool Imports::has_dbl() const {
  return this != internal_default_instance() && dbl_ != NULL;
}
inline void Imports::clear_dbl() {
  if (GetArenaNoVirtual() == NULL && dbl_ != NULL) delete dbl_;
  dbl_ = NULL;
}
inline const ::google::protobuf::DoubleValue& Imports::dbl() const {
  // @@protoc_insertion_point(field_get:google.protobuf.testing.Imports.dbl)
  return dbl_ != NULL ? *dbl_
                         : *::google::protobuf::DoubleValue::internal_default_instance();
}
inline ::google::protobuf::DoubleValue* Imports::mutable_dbl() {
  
  if (dbl_ == NULL) {
    dbl_ = new ::google::protobuf::DoubleValue;
  }
  // @@protoc_insertion_point(field_mutable:google.protobuf.testing.Imports.dbl)
  return dbl_;
}
inline ::google::protobuf::DoubleValue* Imports::release_dbl() {
  // @@protoc_insertion_point(field_release:google.protobuf.testing.Imports.dbl)
  
  ::google::protobuf::DoubleValue* temp = dbl_;
  dbl_ = NULL;
  return temp;
}
inline void Imports::set_allocated_dbl(::google::protobuf::DoubleValue* dbl) {
  delete dbl_;
  if (dbl != NULL && dbl->GetArena() != NULL) {
    ::google::protobuf::DoubleValue* new_dbl = new ::google::protobuf::DoubleValue;
    new_dbl->CopyFrom(*dbl);
    dbl = new_dbl;
  }
  dbl_ = dbl;
  if (dbl) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_set_allocated:google.protobuf.testing.Imports.dbl)
}

// optional .google.protobuf.Struct struct = 2;
inline bool Imports::has_struct_() const {
  return this != internal_default_instance() && struct__ != NULL;
}
inline void Imports::clear_struct_() {
  if (GetArenaNoVirtual() == NULL && struct__ != NULL) delete struct__;
  struct__ = NULL;
}
inline const ::google::protobuf::Struct& Imports::struct_() const {
  // @@protoc_insertion_point(field_get:google.protobuf.testing.Imports.struct)
  return struct__ != NULL ? *struct__
                         : *::google::protobuf::Struct::internal_default_instance();
}
inline ::google::protobuf::Struct* Imports::mutable_struct_() {
  
  if (struct__ == NULL) {
    struct__ = new ::google::protobuf::Struct;
  }
  // @@protoc_insertion_point(field_mutable:google.protobuf.testing.Imports.struct)
  return struct__;
}
inline ::google::protobuf::Struct* Imports::release_struct_() {
  // @@protoc_insertion_point(field_release:google.protobuf.testing.Imports.struct)
  
  ::google::protobuf::Struct* temp = struct__;
  struct__ = NULL;
  return temp;
}
inline void Imports::set_allocated_struct_(::google::protobuf::Struct* struct_) {
  delete struct__;
  if (struct_ != NULL && struct_->GetArena() != NULL) {
    ::google::protobuf::Struct* new_struct_ = new ::google::protobuf::Struct;
    new_struct_->CopyFrom(*struct_);
    struct_ = new_struct_;
  }
  struct__ = struct_;
  if (struct_) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_set_allocated:google.protobuf.testing.Imports.struct)
}

// optional .google.protobuf.Timestamp timestamp = 3;
inline bool Imports::has_timestamp() const {
  return this != internal_default_instance() && timestamp_ != NULL;
}
inline void Imports::clear_timestamp() {
  if (GetArenaNoVirtual() == NULL && timestamp_ != NULL) delete timestamp_;
  timestamp_ = NULL;
}
inline const ::google::protobuf::Timestamp& Imports::timestamp() const {
  // @@protoc_insertion_point(field_get:google.protobuf.testing.Imports.timestamp)
  return timestamp_ != NULL ? *timestamp_
                         : *::google::protobuf::Timestamp::internal_default_instance();
}
inline ::google::protobuf::Timestamp* Imports::mutable_timestamp() {
  
  if (timestamp_ == NULL) {
    timestamp_ = new ::google::protobuf::Timestamp;
  }
  // @@protoc_insertion_point(field_mutable:google.protobuf.testing.Imports.timestamp)
  return timestamp_;
}
inline ::google::protobuf::Timestamp* Imports::release_timestamp() {
  // @@protoc_insertion_point(field_release:google.protobuf.testing.Imports.timestamp)
  
  ::google::protobuf::Timestamp* temp = timestamp_;
  timestamp_ = NULL;
  return temp;
}
inline void Imports::set_allocated_timestamp(::google::protobuf::Timestamp* timestamp) {
  delete timestamp_;
  if (timestamp != NULL && timestamp->GetArena() != NULL) {
    ::google::protobuf::Timestamp* new_timestamp = new ::google::protobuf::Timestamp;
    new_timestamp->CopyFrom(*timestamp);
    timestamp = new_timestamp;
  }
  timestamp_ = timestamp;
  if (timestamp) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_set_allocated:google.protobuf.testing.Imports.timestamp)
}

// optional .google.protobuf.Duration duration = 4;
inline bool Imports::has_duration() const {
  return this != internal_default_instance() && duration_ != NULL;
}
inline void Imports::clear_duration() {
  if (GetArenaNoVirtual() == NULL && duration_ != NULL) delete duration_;
  duration_ = NULL;
}
inline const ::google::protobuf::Duration& Imports::duration() const {
  // @@protoc_insertion_point(field_get:google.protobuf.testing.Imports.duration)
  return duration_ != NULL ? *duration_
                         : *::google::protobuf::Duration::internal_default_instance();
}
inline ::google::protobuf::Duration* Imports::mutable_duration() {
  
  if (duration_ == NULL) {
    duration_ = new ::google::protobuf::Duration;
  }
  // @@protoc_insertion_point(field_mutable:google.protobuf.testing.Imports.duration)
  return duration_;
}
inline ::google::protobuf::Duration* Imports::release_duration() {
  // @@protoc_insertion_point(field_release:google.protobuf.testing.Imports.duration)
  
  ::google::protobuf::Duration* temp = duration_;
  duration_ = NULL;
  return temp;
}
inline void Imports::set_allocated_duration(::google::protobuf::Duration* duration) {
  delete duration_;
  if (duration != NULL && duration->GetArena() != NULL) {
    ::google::protobuf::Duration* new_duration = new ::google::protobuf::Duration;
    new_duration->CopyFrom(*duration);
    duration = new_duration;
  }
  duration_ = duration;
  if (duration) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_set_allocated:google.protobuf.testing.Imports.duration)
}

// optional .google.protobuf.Int32Value i32 = 5;
inline bool Imports::has_i32() const {
  return this != internal_default_instance() && i32_ != NULL;
}
inline void Imports::clear_i32() {
  if (GetArenaNoVirtual() == NULL && i32_ != NULL) delete i32_;
  i32_ = NULL;
}
inline const ::google::protobuf::Int32Value& Imports::i32() const {
  // @@protoc_insertion_point(field_get:google.protobuf.testing.Imports.i32)
  return i32_ != NULL ? *i32_
                         : *::google::protobuf::Int32Value::internal_default_instance();
}
inline ::google::protobuf::Int32Value* Imports::mutable_i32() {
  
  if (i32_ == NULL) {
    i32_ = new ::google::protobuf::Int32Value;
  }
  // @@protoc_insertion_point(field_mutable:google.protobuf.testing.Imports.i32)
  return i32_;
}
inline ::google::protobuf::Int32Value* Imports::release_i32() {
  // @@protoc_insertion_point(field_release:google.protobuf.testing.Imports.i32)
  
  ::google::protobuf::Int32Value* temp = i32_;
  i32_ = NULL;
  return temp;
}
inline void Imports::set_allocated_i32(::google::protobuf::Int32Value* i32) {
  delete i32_;
  if (i32 != NULL && i32->GetArena() != NULL) {
    ::google::protobuf::Int32Value* new_i32 = new ::google::protobuf::Int32Value;
    new_i32->CopyFrom(*i32);
    i32 = new_i32;
  }
  i32_ = i32;
  if (i32) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_set_allocated:google.protobuf.testing.Imports.i32)
}

// optional .google.protobuf.testing.Data data = 100;
inline bool Imports::has_data() const {
  return this != internal_default_instance() && data_ != NULL;
}
inline void Imports::clear_data() {
  if (GetArenaNoVirtual() == NULL && data_ != NULL) delete data_;
  data_ = NULL;
}
inline const ::google::protobuf::testing::Data& Imports::data() const {
  // @@protoc_insertion_point(field_get:google.protobuf.testing.Imports.data)
  return data_ != NULL ? *data_
                         : *::google::protobuf::testing::Data::internal_default_instance();
}
inline ::google::protobuf::testing::Data* Imports::mutable_data() {
  
  if (data_ == NULL) {
    data_ = new ::google::protobuf::testing::Data;
  }
  // @@protoc_insertion_point(field_mutable:google.protobuf.testing.Imports.data)
  return data_;
}
inline ::google::protobuf::testing::Data* Imports::release_data() {
  // @@protoc_insertion_point(field_release:google.protobuf.testing.Imports.data)
  
  ::google::protobuf::testing::Data* temp = data_;
  data_ = NULL;
  return temp;
}
inline void Imports::set_allocated_data(::google::protobuf::testing::Data* data) {
  delete data_;
  data_ = data;
  if (data) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_set_allocated:google.protobuf.testing.Imports.data)
}

inline const Imports* Imports::internal_default_instance() {
  return &Imports_default_instance_.get();
}
// -------------------------------------------------------------------

// Data

// optional int32 attr = 1;
inline void Data::clear_attr() {
  attr_ = 0;
}
inline ::google::protobuf::int32 Data::attr() const {
  // @@protoc_insertion_point(field_get:google.protobuf.testing.Data.attr)
  return attr_;
}
inline void Data::set_attr(::google::protobuf::int32 value) {
  
  attr_ = value;
  // @@protoc_insertion_point(field_set:google.protobuf.testing.Data.attr)
}

// optional string str = 2;
inline void Data::clear_str() {
  str_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline const ::std::string& Data::str() const {
  // @@protoc_insertion_point(field_get:google.protobuf.testing.Data.str)
  return str_.GetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void Data::set_str(const ::std::string& value) {
  
  str_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:google.protobuf.testing.Data.str)
}
inline void Data::set_str(const char* value) {
  
  str_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:google.protobuf.testing.Data.str)
}
inline void Data::set_str(const char* value, size_t size) {
  
  str_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:google.protobuf.testing.Data.str)
}
inline ::std::string* Data::mutable_str() {
  
  // @@protoc_insertion_point(field_mutable:google.protobuf.testing.Data.str)
  return str_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline ::std::string* Data::release_str() {
  // @@protoc_insertion_point(field_release:google.protobuf.testing.Data.str)
  
  return str_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void Data::set_allocated_str(::std::string* str) {
  if (str != NULL) {
    
  } else {
    
  }
  str_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), str);
  // @@protoc_insertion_point(field_set_allocated:google.protobuf.testing.Data.str)
}

// repeated string msgs = 3;
inline int Data::msgs_size() const {
  return msgs_.size();
}
inline void Data::clear_msgs() {
  msgs_.Clear();
}
inline const ::std::string& Data::msgs(int index) const {
  // @@protoc_insertion_point(field_get:google.protobuf.testing.Data.msgs)
  return msgs_.Get(index);
}
inline ::std::string* Data::mutable_msgs(int index) {
  // @@protoc_insertion_point(field_mutable:google.protobuf.testing.Data.msgs)
  return msgs_.Mutable(index);
}
inline void Data::set_msgs(int index, const ::std::string& value) {
  // @@protoc_insertion_point(field_set:google.protobuf.testing.Data.msgs)
  msgs_.Mutable(index)->assign(value);
}
inline void Data::set_msgs(int index, const char* value) {
  msgs_.Mutable(index)->assign(value);
  // @@protoc_insertion_point(field_set_char:google.protobuf.testing.Data.msgs)
}
inline void Data::set_msgs(int index, const char* value, size_t size) {
  msgs_.Mutable(index)->assign(
    reinterpret_cast<const char*>(value), size);
  // @@protoc_insertion_point(field_set_pointer:google.protobuf.testing.Data.msgs)
}
inline ::std::string* Data::add_msgs() {
  // @@protoc_insertion_point(field_add_mutable:google.protobuf.testing.Data.msgs)
  return msgs_.Add();
}
inline void Data::add_msgs(const ::std::string& value) {
  msgs_.Add()->assign(value);
  // @@protoc_insertion_point(field_add:google.protobuf.testing.Data.msgs)
}
inline void Data::add_msgs(const char* value) {
  msgs_.Add()->assign(value);
  // @@protoc_insertion_point(field_add_char:google.protobuf.testing.Data.msgs)
}
inline void Data::add_msgs(const char* value, size_t size) {
  msgs_.Add()->assign(reinterpret_cast<const char*>(value), size);
  // @@protoc_insertion_point(field_add_pointer:google.protobuf.testing.Data.msgs)
}
inline const ::google::protobuf::RepeatedPtrField< ::std::string>&
Data::msgs() const {
  // @@protoc_insertion_point(field_list:google.protobuf.testing.Data.msgs)
  return msgs_;
}
inline ::google::protobuf::RepeatedPtrField< ::std::string>*
Data::mutable_msgs() {
  // @@protoc_insertion_point(field_mutable_list:google.protobuf.testing.Data.msgs)
  return &msgs_;
}

// optional .google.protobuf.testing.Data nested_data = 4;
inline bool Data::has_nested_data() const {
  return this != internal_default_instance() && nested_data_ != NULL;
}
inline void Data::clear_nested_data() {
  if (GetArenaNoVirtual() == NULL && nested_data_ != NULL) delete nested_data_;
  nested_data_ = NULL;
}
inline const ::google::protobuf::testing::Data& Data::nested_data() const {
  // @@protoc_insertion_point(field_get:google.protobuf.testing.Data.nested_data)
  return nested_data_ != NULL ? *nested_data_
                         : *::google::protobuf::testing::Data::internal_default_instance();
}
inline ::google::protobuf::testing::Data* Data::mutable_nested_data() {
  
  if (nested_data_ == NULL) {
    nested_data_ = new ::google::protobuf::testing::Data;
  }
  // @@protoc_insertion_point(field_mutable:google.protobuf.testing.Data.nested_data)
  return nested_data_;
}
inline ::google::protobuf::testing::Data* Data::release_nested_data() {
  // @@protoc_insertion_point(field_release:google.protobuf.testing.Data.nested_data)
  
  ::google::protobuf::testing::Data* temp = nested_data_;
  nested_data_ = NULL;
  return temp;
}
inline void Data::set_allocated_nested_data(::google::protobuf::testing::Data* nested_data) {
  delete nested_data_;
  nested_data_ = nested_data;
  if (nested_data) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_set_allocated:google.protobuf.testing.Data.nested_data)
}

// optional .google.protobuf.Int32Value int_wrapper = 5;
inline bool Data::has_int_wrapper() const {
  return this != internal_default_instance() && int_wrapper_ != NULL;
}
inline void Data::clear_int_wrapper() {
  if (GetArenaNoVirtual() == NULL && int_wrapper_ != NULL) delete int_wrapper_;
  int_wrapper_ = NULL;
}
inline const ::google::protobuf::Int32Value& Data::int_wrapper() const {
  // @@protoc_insertion_point(field_get:google.protobuf.testing.Data.int_wrapper)
  return int_wrapper_ != NULL ? *int_wrapper_
                         : *::google::protobuf::Int32Value::internal_default_instance();
}
inline ::google::protobuf::Int32Value* Data::mutable_int_wrapper() {
  
  if (int_wrapper_ == NULL) {
    int_wrapper_ = new ::google::protobuf::Int32Value;
  }
  // @@protoc_insertion_point(field_mutable:google.protobuf.testing.Data.int_wrapper)
  return int_wrapper_;
}
inline ::google::protobuf::Int32Value* Data::release_int_wrapper() {
  // @@protoc_insertion_point(field_release:google.protobuf.testing.Data.int_wrapper)
  
  ::google::protobuf::Int32Value* temp = int_wrapper_;
  int_wrapper_ = NULL;
  return temp;
}
inline void Data::set_allocated_int_wrapper(::google::protobuf::Int32Value* int_wrapper) {
  delete int_wrapper_;
  if (int_wrapper != NULL && int_wrapper->GetArena() != NULL) {
    ::google::protobuf::Int32Value* new_int_wrapper = new ::google::protobuf::Int32Value;
    new_int_wrapper->CopyFrom(*int_wrapper);
    int_wrapper = new_int_wrapper;
  }
  int_wrapper_ = int_wrapper;
  if (int_wrapper) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_set_allocated:google.protobuf.testing.Data.int_wrapper)
}

// optional .google.protobuf.Timestamp time = 6;
inline bool Data::has_time() const {
  return this != internal_default_instance() && time_ != NULL;
}
inline void Data::clear_time() {
  if (GetArenaNoVirtual() == NULL && time_ != NULL) delete time_;
  time_ = NULL;
}
inline const ::google::protobuf::Timestamp& Data::time() const {
  // @@protoc_insertion_point(field_get:google.protobuf.testing.Data.time)
  return time_ != NULL ? *time_
                         : *::google::protobuf::Timestamp::internal_default_instance();
}
inline ::google::protobuf::Timestamp* Data::mutable_time() {
  
  if (time_ == NULL) {
    time_ = new ::google::protobuf::Timestamp;
  }
  // @@protoc_insertion_point(field_mutable:google.protobuf.testing.Data.time)
  return time_;
}
inline ::google::protobuf::Timestamp* Data::release_time() {
  // @@protoc_insertion_point(field_release:google.protobuf.testing.Data.time)
  
  ::google::protobuf::Timestamp* temp = time_;
  time_ = NULL;
  return temp;
}
inline void Data::set_allocated_time(::google::protobuf::Timestamp* time) {
  delete time_;
  if (time != NULL && time->GetArena() != NULL) {
    ::google::protobuf::Timestamp* new_time = new ::google::protobuf::Timestamp;
    new_time->CopyFrom(*time);
    time = new_time;
  }
  time_ = time;
  if (time) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_set_allocated:google.protobuf.testing.Data.time)
}

// map<string, string> map_data = 7;
inline int Data::map_data_size() const {
  return map_data_.size();
}
inline void Data::clear_map_data() {
  map_data_.Clear();
}
inline const ::google::protobuf::Map< ::std::string, ::std::string >&
Data::map_data() const {
  // @@protoc_insertion_point(field_map:google.protobuf.testing.Data.map_data)
  return map_data_.GetMap();
}
inline ::google::protobuf::Map< ::std::string, ::std::string >*
Data::mutable_map_data() {
  // @@protoc_insertion_point(field_mutable_map:google.protobuf.testing.Data.map_data)
  return map_data_.MutableMap();
}

// optional .google.protobuf.Struct struct_data = 8;
inline bool Data::has_struct_data() const {
  return this != internal_default_instance() && struct_data_ != NULL;
}
inline void Data::clear_struct_data() {
  if (GetArenaNoVirtual() == NULL && struct_data_ != NULL) delete struct_data_;
  struct_data_ = NULL;
}
inline const ::google::protobuf::Struct& Data::struct_data() const {
  // @@protoc_insertion_point(field_get:google.protobuf.testing.Data.struct_data)
  return struct_data_ != NULL ? *struct_data_
                         : *::google::protobuf::Struct::internal_default_instance();
}
inline ::google::protobuf::Struct* Data::mutable_struct_data() {
  
  if (struct_data_ == NULL) {
    struct_data_ = new ::google::protobuf::Struct;
  }
  // @@protoc_insertion_point(field_mutable:google.protobuf.testing.Data.struct_data)
  return struct_data_;
}
inline ::google::protobuf::Struct* Data::release_struct_data() {
  // @@protoc_insertion_point(field_release:google.protobuf.testing.Data.struct_data)
  
  ::google::protobuf::Struct* temp = struct_data_;
  struct_data_ = NULL;
  return temp;
}
inline void Data::set_allocated_struct_data(::google::protobuf::Struct* struct_data) {
  delete struct_data_;
  if (struct_data != NULL && struct_data->GetArena() != NULL) {
    ::google::protobuf::Struct* new_struct_data = new ::google::protobuf::Struct;
    new_struct_data->CopyFrom(*struct_data);
    struct_data = new_struct_data;
  }
  struct_data_ = struct_data;
  if (struct_data) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_set_allocated:google.protobuf.testing.Data.struct_data)
}

// repeated .google.protobuf.testing.Data repeated_data = 9;
inline int Data::repeated_data_size() const {
  return repeated_data_.size();
}
inline void Data::clear_repeated_data() {
  repeated_data_.Clear();
}
inline const ::google::protobuf::testing::Data& Data::repeated_data(int index) const {
  // @@protoc_insertion_point(field_get:google.protobuf.testing.Data.repeated_data)
  return repeated_data_.Get(index);
}
inline ::google::protobuf::testing::Data* Data::mutable_repeated_data(int index) {
  // @@protoc_insertion_point(field_mutable:google.protobuf.testing.Data.repeated_data)
  return repeated_data_.Mutable(index);
}
inline ::google::protobuf::testing::Data* Data::add_repeated_data() {
  // @@protoc_insertion_point(field_add:google.protobuf.testing.Data.repeated_data)
  return repeated_data_.Add();
}
inline ::google::protobuf::RepeatedPtrField< ::google::protobuf::testing::Data >*
Data::mutable_repeated_data() {
  // @@protoc_insertion_point(field_mutable_list:google.protobuf.testing.Data.repeated_data)
  return &repeated_data_;
}
inline const ::google::protobuf::RepeatedPtrField< ::google::protobuf::testing::Data >&
Data::repeated_data() const {
  // @@protoc_insertion_point(field_list:google.protobuf.testing.Data.repeated_data)
  return repeated_data_;
}

inline const Data* Data::internal_default_instance() {
  return &Data_default_instance_.get();
}
// -------------------------------------------------------------------

// AnyIn

// optional string something = 1;
inline void AnyIn::clear_something() {
  something_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline const ::std::string& AnyIn::something() const {
  // @@protoc_insertion_point(field_get:google.protobuf.testing.AnyIn.something)
  return something_.GetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void AnyIn::set_something(const ::std::string& value) {
  
  something_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:google.protobuf.testing.AnyIn.something)
}
inline void AnyIn::set_something(const char* value) {
  
  something_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:google.protobuf.testing.AnyIn.something)
}
inline void AnyIn::set_something(const char* value, size_t size) {
  
  something_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:google.protobuf.testing.AnyIn.something)
}
inline ::std::string* AnyIn::mutable_something() {
  
  // @@protoc_insertion_point(field_mutable:google.protobuf.testing.AnyIn.something)
  return something_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline ::std::string* AnyIn::release_something() {
  // @@protoc_insertion_point(field_release:google.protobuf.testing.AnyIn.something)
  
  return something_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void AnyIn::set_allocated_something(::std::string* something) {
  if (something != NULL) {
    
  } else {
    
  }
  something_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), something);
  // @@protoc_insertion_point(field_set_allocated:google.protobuf.testing.AnyIn.something)
}

// optional .google.protobuf.Any any = 2;
inline bool AnyIn::has_any() const {
  return this != internal_default_instance() && any_ != NULL;
}
inline void AnyIn::clear_any() {
  if (GetArenaNoVirtual() == NULL && any_ != NULL) delete any_;
  any_ = NULL;
}
inline const ::google::protobuf::Any& AnyIn::any() const {
  // @@protoc_insertion_point(field_get:google.protobuf.testing.AnyIn.any)
  return any_ != NULL ? *any_
                         : *::google::protobuf::Any::internal_default_instance();
}
inline ::google::protobuf::Any* AnyIn::mutable_any() {
  
  if (any_ == NULL) {
    any_ = new ::google::protobuf::Any;
  }
  // @@protoc_insertion_point(field_mutable:google.protobuf.testing.AnyIn.any)
  return any_;
}
inline ::google::protobuf::Any* AnyIn::release_any() {
  // @@protoc_insertion_point(field_release:google.protobuf.testing.AnyIn.any)
  
  ::google::protobuf::Any* temp = any_;
  any_ = NULL;
  return temp;
}
inline void AnyIn::set_allocated_any(::google::protobuf::Any* any) {
  delete any_;
  any_ = any;
  if (any) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_set_allocated:google.protobuf.testing.AnyIn.any)
}

inline const AnyIn* AnyIn::internal_default_instance() {
  return &AnyIn_default_instance_.get();
}
// -------------------------------------------------------------------

// AnyOut

// optional .google.protobuf.Any any = 1;
inline bool AnyOut::has_any() const {
  return this != internal_default_instance() && any_ != NULL;
}
inline void AnyOut::clear_any() {
  if (GetArenaNoVirtual() == NULL && any_ != NULL) delete any_;
  any_ = NULL;
}
inline const ::google::protobuf::Any& AnyOut::any() const {
  // @@protoc_insertion_point(field_get:google.protobuf.testing.AnyOut.any)
  return any_ != NULL ? *any_
                         : *::google::protobuf::Any::internal_default_instance();
}
inline ::google::protobuf::Any* AnyOut::mutable_any() {
  
  if (any_ == NULL) {
    any_ = new ::google::protobuf::Any;
  }
  // @@protoc_insertion_point(field_mutable:google.protobuf.testing.AnyOut.any)
  return any_;
}
inline ::google::protobuf::Any* AnyOut::release_any() {
  // @@protoc_insertion_point(field_release:google.protobuf.testing.AnyOut.any)
  
  ::google::protobuf::Any* temp = any_;
  any_ = NULL;
  return temp;
}
inline void AnyOut::set_allocated_any(::google::protobuf::Any* any) {
  delete any_;
  any_ = any;
  if (any) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_set_allocated:google.protobuf.testing.AnyOut.any)
}

inline const AnyOut* AnyOut::internal_default_instance() {
  return &AnyOut_default_instance_.get();
}
// -------------------------------------------------------------------

// AnyM

// optional string foo = 1;
inline void AnyM::clear_foo() {
  foo_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline const ::std::string& AnyM::foo() const {
  // @@protoc_insertion_point(field_get:google.protobuf.testing.AnyM.foo)
  return foo_.GetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void AnyM::set_foo(const ::std::string& value) {
  
  foo_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:google.protobuf.testing.AnyM.foo)
}
inline void AnyM::set_foo(const char* value) {
  
  foo_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:google.protobuf.testing.AnyM.foo)
}
inline void AnyM::set_foo(const char* value, size_t size) {
  
  foo_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:google.protobuf.testing.AnyM.foo)
}
inline ::std::string* AnyM::mutable_foo() {
  
  // @@protoc_insertion_point(field_mutable:google.protobuf.testing.AnyM.foo)
  return foo_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline ::std::string* AnyM::release_foo() {
  // @@protoc_insertion_point(field_release:google.protobuf.testing.AnyM.foo)
  
  return foo_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void AnyM::set_allocated_foo(::std::string* foo) {
  if (foo != NULL) {
    
  } else {
    
  }
  foo_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), foo);
  // @@protoc_insertion_point(field_set_allocated:google.protobuf.testing.AnyM.foo)
}

inline const AnyM* AnyM::internal_default_instance() {
  return &AnyM_default_instance_.get();
}
#endif  // !PROTOBUF_INLINE_NOT_IN_HEADERS
// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------


// @@protoc_insertion_point(namespace_scope)

}  // namespace testing
}  // namespace protobuf
}  // namespace google

// @@protoc_insertion_point(global_scope)

#endif  // PROTOBUF_google_2fprotobuf_2futil_2finternal_2ftestdata_2fanys_2eproto__INCLUDED

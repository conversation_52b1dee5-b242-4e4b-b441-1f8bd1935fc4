// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: google/protobuf/util/internal/testdata/books.proto

#ifndef PROTOBUF_google_2fprotobuf_2futil_2finternal_2ftestdata_2fbooks_2eproto__INCLUDED
#define PROTOBUF_google_2fprotobuf_2futil_2finternal_2ftestdata_2fbooks_2eproto__INCLUDED

#include <string>

#include <google/protobuf/stubs/common.h>

#if GOOGLE_PROTOBUF_VERSION < 3001000
#error This file was generated by a newer version of protoc which is
#error incompatible with your Protocol Buffer headers.  Please update
#error your headers.
#endif
#if 3001000 < GOOGLE_PROTOBUF_MIN_PROTOC_VERSION
#error This file was generated by an older version of protoc which is
#error incompatible with your Protocol Buffer headers.  Please
#error regenerate this file with a newer version of protoc.
#endif

#include <google/protobuf/arena.h>
#include <google/protobuf/arenastring.h>
#include <google/protobuf/generated_message_util.h>
#include <google/protobuf/metadata.h>
#include <google/protobuf/message.h>
#include <google/protobuf/repeated_field.h>
#include <google/protobuf/extension_set.h>
#include <google/protobuf/generated_enum_reflection.h>
#include <google/protobuf/unknown_field_set.h>
// @@protoc_insertion_point(includes)

namespace google {
namespace protobuf {
namespace testing {

// Internal implementation detail -- do not call these.
void protobuf_AddDesc_google_2fprotobuf_2futil_2finternal_2ftestdata_2fbooks_2eproto();
void protobuf_InitDefaults_google_2fprotobuf_2futil_2finternal_2ftestdata_2fbooks_2eproto();
void protobuf_AssignDesc_google_2fprotobuf_2futil_2finternal_2ftestdata_2fbooks_2eproto();
void protobuf_ShutdownFile_google_2fprotobuf_2futil_2finternal_2ftestdata_2fbooks_2eproto();

class Author;
class BadAuthor;
class BadNestedBook;
class Book;
class Book_Data;
class Book_Label;
class Cyclic;
class NestedBook;
class PackedPrimitive;
class Primitive;
class Publisher;

enum Book_Type {
  Book_Type_FICTION = 1,
  Book_Type_KIDS = 2,
  Book_Type_ACTION_AND_ADVENTURE = 3,
  Book_Type_arts_and_photography = 4
};
bool Book_Type_IsValid(int value);
const Book_Type Book_Type_Type_MIN = Book_Type_FICTION;
const Book_Type Book_Type_Type_MAX = Book_Type_arts_and_photography;
const int Book_Type_Type_ARRAYSIZE = Book_Type_Type_MAX + 1;

const ::google::protobuf::EnumDescriptor* Book_Type_descriptor();
inline const ::std::string& Book_Type_Name(Book_Type value) {
  return ::google::protobuf::internal::NameOfEnum(
    Book_Type_descriptor(), value);
}
inline bool Book_Type_Parse(
    const ::std::string& name, Book_Type* value) {
  return ::google::protobuf::internal::ParseNamedEnum<Book_Type>(
    Book_Type_descriptor(), name, value);
}
// ===================================================================

class Book_Data : public ::google::protobuf::Message /* @@protoc_insertion_point(class_definition:google.protobuf.testing.Book.Data) */ {
 public:
  Book_Data();
  virtual ~Book_Data();

  Book_Data(const Book_Data& from);

  inline Book_Data& operator=(const Book_Data& from) {
    CopyFrom(from);
    return *this;
  }

  inline const ::google::protobuf::UnknownFieldSet& unknown_fields() const {
    return _internal_metadata_.unknown_fields();
  }

  inline ::google::protobuf::UnknownFieldSet* mutable_unknown_fields() {
    return _internal_metadata_.mutable_unknown_fields();
  }

  static const ::google::protobuf::Descriptor* descriptor();
  static const Book_Data& default_instance();

  static const Book_Data* internal_default_instance();

  void Swap(Book_Data* other);

  // implements Message ----------------------------------------------

  inline Book_Data* New() const { return New(NULL); }

  Book_Data* New(::google::protobuf::Arena* arena) const;
  void CopyFrom(const ::google::protobuf::Message& from);
  void MergeFrom(const ::google::protobuf::Message& from);
  void CopyFrom(const Book_Data& from);
  void MergeFrom(const Book_Data& from);
  void Clear();
  bool IsInitialized() const;

  size_t ByteSizeLong() const;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input);
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const;
  ::google::protobuf::uint8* InternalSerializeWithCachedSizesToArray(
      bool deterministic, ::google::protobuf::uint8* output) const;
  ::google::protobuf::uint8* SerializeWithCachedSizesToArray(::google::protobuf::uint8* output) const {
    return InternalSerializeWithCachedSizesToArray(false, output);
  }
  int GetCachedSize() const { return _cached_size_; }
  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const;
  void InternalSwap(Book_Data* other);
  void UnsafeMergeFrom(const Book_Data& from);
  private:
  inline ::google::protobuf::Arena* GetArenaNoVirtual() const {
    return _internal_metadata_.arena();
  }
  inline void* MaybeArenaPtr() const {
    return _internal_metadata_.raw_arena_ptr();
  }
  public:

  ::google::protobuf::Metadata GetMetadata() const;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // optional uint32 year = 7;
  bool has_year() const;
  void clear_year();
  static const int kYearFieldNumber = 7;
  ::google::protobuf::uint32 year() const;
  void set_year(::google::protobuf::uint32 value);

  // optional string copyright = 8;
  bool has_copyright() const;
  void clear_copyright();
  static const int kCopyrightFieldNumber = 8;
  const ::std::string& copyright() const;
  void set_copyright(const ::std::string& value);
  void set_copyright(const char* value);
  void set_copyright(const char* value, size_t size);
  ::std::string* mutable_copyright();
  ::std::string* release_copyright();
  void set_allocated_copyright(::std::string* copyright);

  // @@protoc_insertion_point(class_scope:google.protobuf.testing.Book.Data)
 private:
  inline void set_has_year();
  inline void clear_has_year();
  inline void set_has_copyright();
  inline void clear_has_copyright();

  ::google::protobuf::internal::InternalMetadataWithArena _internal_metadata_;
  ::google::protobuf::internal::HasBits<1> _has_bits_;
  mutable int _cached_size_;
  ::google::protobuf::internal::ArenaStringPtr copyright_;
  ::google::protobuf::uint32 year_;
  friend void  protobuf_InitDefaults_google_2fprotobuf_2futil_2finternal_2ftestdata_2fbooks_2eproto_impl();
  friend void  protobuf_AddDesc_google_2fprotobuf_2futil_2finternal_2ftestdata_2fbooks_2eproto_impl();
  friend void protobuf_AssignDesc_google_2fprotobuf_2futil_2finternal_2ftestdata_2fbooks_2eproto();
  friend void protobuf_ShutdownFile_google_2fprotobuf_2futil_2finternal_2ftestdata_2fbooks_2eproto();

  void InitAsDefaultInstance();
};
extern ::google::protobuf::internal::ExplicitlyConstructed<Book_Data> Book_Data_default_instance_;

// -------------------------------------------------------------------

class Book_Label : public ::google::protobuf::Message /* @@protoc_insertion_point(class_definition:google.protobuf.testing.Book.Label) */ {
 public:
  Book_Label();
  virtual ~Book_Label();

  Book_Label(const Book_Label& from);

  inline Book_Label& operator=(const Book_Label& from) {
    CopyFrom(from);
    return *this;
  }

  inline const ::google::protobuf::UnknownFieldSet& unknown_fields() const {
    return _internal_metadata_.unknown_fields();
  }

  inline ::google::protobuf::UnknownFieldSet* mutable_unknown_fields() {
    return _internal_metadata_.mutable_unknown_fields();
  }

  static const ::google::protobuf::Descriptor* descriptor();
  static const Book_Label& default_instance();

  static const Book_Label* internal_default_instance();

  void Swap(Book_Label* other);

  // implements Message ----------------------------------------------

  inline Book_Label* New() const { return New(NULL); }

  Book_Label* New(::google::protobuf::Arena* arena) const;
  void CopyFrom(const ::google::protobuf::Message& from);
  void MergeFrom(const ::google::protobuf::Message& from);
  void CopyFrom(const Book_Label& from);
  void MergeFrom(const Book_Label& from);
  void Clear();
  bool IsInitialized() const;

  size_t ByteSizeLong() const;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input);
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const;
  ::google::protobuf::uint8* InternalSerializeWithCachedSizesToArray(
      bool deterministic, ::google::protobuf::uint8* output) const;
  ::google::protobuf::uint8* SerializeWithCachedSizesToArray(::google::protobuf::uint8* output) const {
    return InternalSerializeWithCachedSizesToArray(false, output);
  }
  int GetCachedSize() const { return _cached_size_; }
  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const;
  void InternalSwap(Book_Label* other);
  void UnsafeMergeFrom(const Book_Label& from);
  private:
  inline ::google::protobuf::Arena* GetArenaNoVirtual() const {
    return _internal_metadata_.arena();
  }
  inline void* MaybeArenaPtr() const {
    return _internal_metadata_.raw_arena_ptr();
  }
  public:

  ::google::protobuf::Metadata GetMetadata() const;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // optional string key = 1;
  bool has_key() const;
  void clear_key();
  static const int kKeyFieldNumber = 1;
  const ::std::string& key() const;
  void set_key(const ::std::string& value);
  void set_key(const char* value);
  void set_key(const char* value, size_t size);
  ::std::string* mutable_key();
  ::std::string* release_key();
  void set_allocated_key(::std::string* key);

  // optional string value = 2;
  bool has_value() const;
  void clear_value();
  static const int kValueFieldNumber = 2;
  const ::std::string& value() const;
  void set_value(const ::std::string& value);
  void set_value(const char* value);
  void set_value(const char* value, size_t size);
  ::std::string* mutable_value();
  ::std::string* release_value();
  void set_allocated_value(::std::string* value);

  // @@protoc_insertion_point(class_scope:google.protobuf.testing.Book.Label)
 private:
  inline void set_has_key();
  inline void clear_has_key();
  inline void set_has_value();
  inline void clear_has_value();

  ::google::protobuf::internal::InternalMetadataWithArena _internal_metadata_;
  ::google::protobuf::internal::HasBits<1> _has_bits_;
  mutable int _cached_size_;
  ::google::protobuf::internal::ArenaStringPtr key_;
  ::google::protobuf::internal::ArenaStringPtr value_;
  friend void  protobuf_InitDefaults_google_2fprotobuf_2futil_2finternal_2ftestdata_2fbooks_2eproto_impl();
  friend void  protobuf_AddDesc_google_2fprotobuf_2futil_2finternal_2ftestdata_2fbooks_2eproto_impl();
  friend void protobuf_AssignDesc_google_2fprotobuf_2futil_2finternal_2ftestdata_2fbooks_2eproto();
  friend void protobuf_ShutdownFile_google_2fprotobuf_2futil_2finternal_2ftestdata_2fbooks_2eproto();

  void InitAsDefaultInstance();
};
extern ::google::protobuf::internal::ExplicitlyConstructed<Book_Label> Book_Label_default_instance_;

// -------------------------------------------------------------------

class Book : public ::google::protobuf::Message /* @@protoc_insertion_point(class_definition:google.protobuf.testing.Book) */ {
 public:
  Book();
  virtual ~Book();

  Book(const Book& from);

  inline Book& operator=(const Book& from) {
    CopyFrom(from);
    return *this;
  }

  inline const ::google::protobuf::UnknownFieldSet& unknown_fields() const {
    return _internal_metadata_.unknown_fields();
  }

  inline ::google::protobuf::UnknownFieldSet* mutable_unknown_fields() {
    return _internal_metadata_.mutable_unknown_fields();
  }

  static const ::google::protobuf::Descriptor* descriptor();
  static const Book& default_instance();

  static const Book* internal_default_instance();

  void Swap(Book* other);

  // implements Message ----------------------------------------------

  inline Book* New() const { return New(NULL); }

  Book* New(::google::protobuf::Arena* arena) const;
  void CopyFrom(const ::google::protobuf::Message& from);
  void MergeFrom(const ::google::protobuf::Message& from);
  void CopyFrom(const Book& from);
  void MergeFrom(const Book& from);
  void Clear();
  bool IsInitialized() const;

  size_t ByteSizeLong() const;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input);
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const;
  ::google::protobuf::uint8* InternalSerializeWithCachedSizesToArray(
      bool deterministic, ::google::protobuf::uint8* output) const;
  ::google::protobuf::uint8* SerializeWithCachedSizesToArray(::google::protobuf::uint8* output) const {
    return InternalSerializeWithCachedSizesToArray(false, output);
  }
  int GetCachedSize() const { return _cached_size_; }
  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const;
  void InternalSwap(Book* other);
  void UnsafeMergeFrom(const Book& from);
  private:
  inline ::google::protobuf::Arena* GetArenaNoVirtual() const {
    return _internal_metadata_.arena();
  }
  inline void* MaybeArenaPtr() const {
    return _internal_metadata_.raw_arena_ptr();
  }
  public:

  ::google::protobuf::Metadata GetMetadata() const;

  // nested types ----------------------------------------------------

  typedef Book_Data Data;
  typedef Book_Label Label;

  typedef Book_Type Type;
  static const Type FICTION =
    Book_Type_FICTION;
  static const Type KIDS =
    Book_Type_KIDS;
  static const Type ACTION_AND_ADVENTURE =
    Book_Type_ACTION_AND_ADVENTURE;
  static const Type arts_and_photography =
    Book_Type_arts_and_photography;
  static inline bool Type_IsValid(int value) {
    return Book_Type_IsValid(value);
  }
  static const Type Type_MIN =
    Book_Type_Type_MIN;
  static const Type Type_MAX =
    Book_Type_Type_MAX;
  static const int Type_ARRAYSIZE =
    Book_Type_Type_ARRAYSIZE;
  static inline const ::google::protobuf::EnumDescriptor*
  Type_descriptor() {
    return Book_Type_descriptor();
  }
  static inline const ::std::string& Type_Name(Type value) {
    return Book_Type_Name(value);
  }
  static inline bool Type_Parse(const ::std::string& name,
      Type* value) {
    return Book_Type_Parse(name, value);
  }

  // accessors -------------------------------------------------------

  // optional string title = 1;
  bool has_title() const;
  void clear_title();
  static const int kTitleFieldNumber = 1;
  const ::std::string& title() const;
  void set_title(const ::std::string& value);
  void set_title(const char* value);
  void set_title(const char* value, size_t size);
  ::std::string* mutable_title();
  ::std::string* release_title();
  void set_allocated_title(::std::string* title);

  // optional .google.protobuf.testing.Author author = 2;
  bool has_author() const;
  void clear_author();
  static const int kAuthorFieldNumber = 2;
  const ::google::protobuf::testing::Author& author() const;
  ::google::protobuf::testing::Author* mutable_author();
  ::google::protobuf::testing::Author* release_author();
  void set_allocated_author(::google::protobuf::testing::Author* author);

  // optional uint32 length = 3;
  bool has_length() const;
  void clear_length();
  static const int kLengthFieldNumber = 3;
  ::google::protobuf::uint32 length() const;
  void set_length(::google::protobuf::uint32 value);

  // optional int64 published = 4;
  bool has_published() const;
  void clear_published();
  static const int kPublishedFieldNumber = 4;
  ::google::protobuf::int64 published() const;
  void set_published(::google::protobuf::int64 value);

  // optional bytes content = 5;
  bool has_content() const;
  void clear_content();
  static const int kContentFieldNumber = 5;
  const ::std::string& content() const;
  void set_content(const ::std::string& value);
  void set_content(const char* value);
  void set_content(const void* value, size_t size);
  ::std::string* mutable_content();
  ::std::string* release_content();
  void set_allocated_content(::std::string* content);

  // optional group Data = 6 { ... };
  bool has_data() const;
  void clear_data();
  static const int kDataFieldNumber = 6;
  const ::google::protobuf::testing::Book_Data& data() const;
  ::google::protobuf::testing::Book_Data* mutable_data();
  ::google::protobuf::testing::Book_Data* release_data();
  void set_allocated_data(::google::protobuf::testing::Book_Data* data);

  // optional .google.protobuf.testing.Publisher publisher = 9;
  bool has_publisher() const;
  void clear_publisher();
  static const int kPublisherFieldNumber = 9;
  const ::google::protobuf::testing::Publisher& publisher() const;
  ::google::protobuf::testing::Publisher* mutable_publisher();
  ::google::protobuf::testing::Publisher* release_publisher();
  void set_allocated_publisher(::google::protobuf::testing::Publisher* publisher);

  // repeated .google.protobuf.testing.Book.Label labels = 10;
  int labels_size() const;
  void clear_labels();
  static const int kLabelsFieldNumber = 10;
  const ::google::protobuf::testing::Book_Label& labels(int index) const;
  ::google::protobuf::testing::Book_Label* mutable_labels(int index);
  ::google::protobuf::testing::Book_Label* add_labels();
  ::google::protobuf::RepeatedPtrField< ::google::protobuf::testing::Book_Label >*
      mutable_labels();
  const ::google::protobuf::RepeatedPtrField< ::google::protobuf::testing::Book_Label >&
      labels() const;

  // optional .google.protobuf.testing.Book.Type type = 11;
  bool has_type() const;
  void clear_type();
  static const int kTypeFieldNumber = 11;
  ::google::protobuf::testing::Book_Type type() const;
  void set_type(::google::protobuf::testing::Book_Type value);

  GOOGLE_PROTOBUF_EXTENSION_ACCESSORS(Book)
  // @@protoc_insertion_point(class_scope:google.protobuf.testing.Book)
 private:
  inline void set_has_title();
  inline void clear_has_title();
  inline void set_has_author();
  inline void clear_has_author();
  inline void set_has_length();
  inline void clear_has_length();
  inline void set_has_published();
  inline void clear_has_published();
  inline void set_has_content();
  inline void clear_has_content();
  inline void set_has_data();
  inline void clear_has_data();
  inline void set_has_publisher();
  inline void clear_has_publisher();
  inline void set_has_type();
  inline void clear_has_type();

  ::google::protobuf::internal::ExtensionSet _extensions_;

  ::google::protobuf::internal::InternalMetadataWithArena _internal_metadata_;
  ::google::protobuf::internal::HasBits<1> _has_bits_;
  mutable int _cached_size_;
  ::google::protobuf::RepeatedPtrField< ::google::protobuf::testing::Book_Label > labels_;
  ::google::protobuf::internal::ArenaStringPtr title_;
  ::google::protobuf::internal::ArenaStringPtr content_;
  ::google::protobuf::testing::Author* author_;
  ::google::protobuf::testing::Book_Data* data_;
  ::google::protobuf::testing::Publisher* publisher_;
  ::google::protobuf::int64 published_;
  ::google::protobuf::uint32 length_;
  int type_;
  friend void  protobuf_InitDefaults_google_2fprotobuf_2futil_2finternal_2ftestdata_2fbooks_2eproto_impl();
  friend void  protobuf_AddDesc_google_2fprotobuf_2futil_2finternal_2ftestdata_2fbooks_2eproto_impl();
  friend void protobuf_AssignDesc_google_2fprotobuf_2futil_2finternal_2ftestdata_2fbooks_2eproto();
  friend void protobuf_ShutdownFile_google_2fprotobuf_2futil_2finternal_2ftestdata_2fbooks_2eproto();

  void InitAsDefaultInstance();
};
extern ::google::protobuf::internal::ExplicitlyConstructed<Book> Book_default_instance_;

// -------------------------------------------------------------------

class Publisher : public ::google::protobuf::Message /* @@protoc_insertion_point(class_definition:google.protobuf.testing.Publisher) */ {
 public:
  Publisher();
  virtual ~Publisher();

  Publisher(const Publisher& from);

  inline Publisher& operator=(const Publisher& from) {
    CopyFrom(from);
    return *this;
  }

  inline const ::google::protobuf::UnknownFieldSet& unknown_fields() const {
    return _internal_metadata_.unknown_fields();
  }

  inline ::google::protobuf::UnknownFieldSet* mutable_unknown_fields() {
    return _internal_metadata_.mutable_unknown_fields();
  }

  static const ::google::protobuf::Descriptor* descriptor();
  static const Publisher& default_instance();

  static const Publisher* internal_default_instance();

  void Swap(Publisher* other);

  // implements Message ----------------------------------------------

  inline Publisher* New() const { return New(NULL); }

  Publisher* New(::google::protobuf::Arena* arena) const;
  void CopyFrom(const ::google::protobuf::Message& from);
  void MergeFrom(const ::google::protobuf::Message& from);
  void CopyFrom(const Publisher& from);
  void MergeFrom(const Publisher& from);
  void Clear();
  bool IsInitialized() const;

  size_t ByteSizeLong() const;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input);
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const;
  ::google::protobuf::uint8* InternalSerializeWithCachedSizesToArray(
      bool deterministic, ::google::protobuf::uint8* output) const;
  ::google::protobuf::uint8* SerializeWithCachedSizesToArray(::google::protobuf::uint8* output) const {
    return InternalSerializeWithCachedSizesToArray(false, output);
  }
  int GetCachedSize() const { return _cached_size_; }
  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const;
  void InternalSwap(Publisher* other);
  void UnsafeMergeFrom(const Publisher& from);
  private:
  inline ::google::protobuf::Arena* GetArenaNoVirtual() const {
    return _internal_metadata_.arena();
  }
  inline void* MaybeArenaPtr() const {
    return _internal_metadata_.raw_arena_ptr();
  }
  public:

  ::google::protobuf::Metadata GetMetadata() const;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // required string name = 1;
  bool has_name() const;
  void clear_name();
  static const int kNameFieldNumber = 1;
  const ::std::string& name() const;
  void set_name(const ::std::string& value);
  void set_name(const char* value);
  void set_name(const char* value, size_t size);
  ::std::string* mutable_name();
  ::std::string* release_name();
  void set_allocated_name(::std::string* name);

  // @@protoc_insertion_point(class_scope:google.protobuf.testing.Publisher)
 private:
  inline void set_has_name();
  inline void clear_has_name();

  ::google::protobuf::internal::InternalMetadataWithArena _internal_metadata_;
  ::google::protobuf::internal::HasBits<1> _has_bits_;
  mutable int _cached_size_;
  ::google::protobuf::internal::ArenaStringPtr name_;
  friend void  protobuf_InitDefaults_google_2fprotobuf_2futil_2finternal_2ftestdata_2fbooks_2eproto_impl();
  friend void  protobuf_AddDesc_google_2fprotobuf_2futil_2finternal_2ftestdata_2fbooks_2eproto_impl();
  friend void protobuf_AssignDesc_google_2fprotobuf_2futil_2finternal_2ftestdata_2fbooks_2eproto();
  friend void protobuf_ShutdownFile_google_2fprotobuf_2futil_2finternal_2ftestdata_2fbooks_2eproto();

  void InitAsDefaultInstance();
};
extern ::google::protobuf::internal::ExplicitlyConstructed<Publisher> Publisher_default_instance_;

// -------------------------------------------------------------------

class Author : public ::google::protobuf::Message /* @@protoc_insertion_point(class_definition:google.protobuf.testing.Author) */ {
 public:
  Author();
  virtual ~Author();

  Author(const Author& from);

  inline Author& operator=(const Author& from) {
    CopyFrom(from);
    return *this;
  }

  inline const ::google::protobuf::UnknownFieldSet& unknown_fields() const {
    return _internal_metadata_.unknown_fields();
  }

  inline ::google::protobuf::UnknownFieldSet* mutable_unknown_fields() {
    return _internal_metadata_.mutable_unknown_fields();
  }

  static const ::google::protobuf::Descriptor* descriptor();
  static const Author& default_instance();

  static const Author* internal_default_instance();

  void Swap(Author* other);

  // implements Message ----------------------------------------------

  inline Author* New() const { return New(NULL); }

  Author* New(::google::protobuf::Arena* arena) const;
  void CopyFrom(const ::google::protobuf::Message& from);
  void MergeFrom(const ::google::protobuf::Message& from);
  void CopyFrom(const Author& from);
  void MergeFrom(const Author& from);
  void Clear();
  bool IsInitialized() const;

  size_t ByteSizeLong() const;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input);
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const;
  ::google::protobuf::uint8* InternalSerializeWithCachedSizesToArray(
      bool deterministic, ::google::protobuf::uint8* output) const;
  ::google::protobuf::uint8* SerializeWithCachedSizesToArray(::google::protobuf::uint8* output) const {
    return InternalSerializeWithCachedSizesToArray(false, output);
  }
  int GetCachedSize() const { return _cached_size_; }
  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const;
  void InternalSwap(Author* other);
  void UnsafeMergeFrom(const Author& from);
  private:
  inline ::google::protobuf::Arena* GetArenaNoVirtual() const {
    return _internal_metadata_.arena();
  }
  inline void* MaybeArenaPtr() const {
    return _internal_metadata_.raw_arena_ptr();
  }
  public:

  ::google::protobuf::Metadata GetMetadata() const;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // optional uint64 id = 1[json_name = "@id"];
  bool has_id() const;
  void clear_id();
  static const int kIdFieldNumber = 1;
  ::google::protobuf::uint64 id() const;
  void set_id(::google::protobuf::uint64 value);

  // optional string name = 2;
  bool has_name() const;
  void clear_name();
  static const int kNameFieldNumber = 2;
  const ::std::string& name() const;
  void set_name(const ::std::string& value);
  void set_name(const char* value);
  void set_name(const char* value, size_t size);
  ::std::string* mutable_name();
  ::std::string* release_name();
  void set_allocated_name(::std::string* name);

  // repeated string pseudonym = 3;
  int pseudonym_size() const;
  void clear_pseudonym();
  static const int kPseudonymFieldNumber = 3;
  const ::std::string& pseudonym(int index) const;
  ::std::string* mutable_pseudonym(int index);
  void set_pseudonym(int index, const ::std::string& value);
  void set_pseudonym(int index, const char* value);
  void set_pseudonym(int index, const char* value, size_t size);
  ::std::string* add_pseudonym();
  void add_pseudonym(const ::std::string& value);
  void add_pseudonym(const char* value);
  void add_pseudonym(const char* value, size_t size);
  const ::google::protobuf::RepeatedPtrField< ::std::string>& pseudonym() const;
  ::google::protobuf::RepeatedPtrField< ::std::string>* mutable_pseudonym();

  // optional bool alive = 4;
  bool has_alive() const;
  void clear_alive();
  static const int kAliveFieldNumber = 4;
  bool alive() const;
  void set_alive(bool value);

  // repeated .google.protobuf.testing.Author friend = 5;
  int friend__size() const;
  void clear_friend_();
  static const int kFriendFieldNumber = 5;
  const ::google::protobuf::testing::Author& friend_(int index) const;
  ::google::protobuf::testing::Author* mutable_friend_(int index);
  ::google::protobuf::testing::Author* add_friend_();
  ::google::protobuf::RepeatedPtrField< ::google::protobuf::testing::Author >*
      mutable_friend_();
  const ::google::protobuf::RepeatedPtrField< ::google::protobuf::testing::Author >&
      friend_() const;

  // @@protoc_insertion_point(class_scope:google.protobuf.testing.Author)
 private:
  inline void set_has_id();
  inline void clear_has_id();
  inline void set_has_name();
  inline void clear_has_name();
  inline void set_has_alive();
  inline void clear_has_alive();

  ::google::protobuf::internal::InternalMetadataWithArena _internal_metadata_;
  ::google::protobuf::internal::HasBits<1> _has_bits_;
  mutable int _cached_size_;
  ::google::protobuf::RepeatedPtrField< ::std::string> pseudonym_;
  ::google::protobuf::RepeatedPtrField< ::google::protobuf::testing::Author > friend__;
  ::google::protobuf::internal::ArenaStringPtr name_;
  ::google::protobuf::uint64 id_;
  bool alive_;
  friend void  protobuf_InitDefaults_google_2fprotobuf_2futil_2finternal_2ftestdata_2fbooks_2eproto_impl();
  friend void  protobuf_AddDesc_google_2fprotobuf_2futil_2finternal_2ftestdata_2fbooks_2eproto_impl();
  friend void protobuf_AssignDesc_google_2fprotobuf_2futil_2finternal_2ftestdata_2fbooks_2eproto();
  friend void protobuf_ShutdownFile_google_2fprotobuf_2futil_2finternal_2ftestdata_2fbooks_2eproto();

  void InitAsDefaultInstance();
};
extern ::google::protobuf::internal::ExplicitlyConstructed<Author> Author_default_instance_;

// -------------------------------------------------------------------

class BadAuthor : public ::google::protobuf::Message /* @@protoc_insertion_point(class_definition:google.protobuf.testing.BadAuthor) */ {
 public:
  BadAuthor();
  virtual ~BadAuthor();

  BadAuthor(const BadAuthor& from);

  inline BadAuthor& operator=(const BadAuthor& from) {
    CopyFrom(from);
    return *this;
  }

  inline const ::google::protobuf::UnknownFieldSet& unknown_fields() const {
    return _internal_metadata_.unknown_fields();
  }

  inline ::google::protobuf::UnknownFieldSet* mutable_unknown_fields() {
    return _internal_metadata_.mutable_unknown_fields();
  }

  static const ::google::protobuf::Descriptor* descriptor();
  static const BadAuthor& default_instance();

  static const BadAuthor* internal_default_instance();

  void Swap(BadAuthor* other);

  // implements Message ----------------------------------------------

  inline BadAuthor* New() const { return New(NULL); }

  BadAuthor* New(::google::protobuf::Arena* arena) const;
  void CopyFrom(const ::google::protobuf::Message& from);
  void MergeFrom(const ::google::protobuf::Message& from);
  void CopyFrom(const BadAuthor& from);
  void MergeFrom(const BadAuthor& from);
  void Clear();
  bool IsInitialized() const;

  size_t ByteSizeLong() const;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input);
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const;
  ::google::protobuf::uint8* InternalSerializeWithCachedSizesToArray(
      bool deterministic, ::google::protobuf::uint8* output) const;
  ::google::protobuf::uint8* SerializeWithCachedSizesToArray(::google::protobuf::uint8* output) const {
    return InternalSerializeWithCachedSizesToArray(false, output);
  }
  int GetCachedSize() const { return _cached_size_; }
  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const;
  void InternalSwap(BadAuthor* other);
  void UnsafeMergeFrom(const BadAuthor& from);
  private:
  inline ::google::protobuf::Arena* GetArenaNoVirtual() const {
    return _internal_metadata_.arena();
  }
  inline void* MaybeArenaPtr() const {
    return _internal_metadata_.raw_arena_ptr();
  }
  public:

  ::google::protobuf::Metadata GetMetadata() const;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // optional string id = 1;
  bool has_id() const;
  void clear_id();
  static const int kIdFieldNumber = 1;
  const ::std::string& id() const;
  void set_id(const ::std::string& value);
  void set_id(const char* value);
  void set_id(const char* value, size_t size);
  ::std::string* mutable_id();
  ::std::string* release_id();
  void set_allocated_id(::std::string* id);

  // repeated uint64 name = 2;
  int name_size() const;
  void clear_name();
  static const int kNameFieldNumber = 2;
  ::google::protobuf::uint64 name(int index) const;
  void set_name(int index, ::google::protobuf::uint64 value);
  void add_name(::google::protobuf::uint64 value);
  const ::google::protobuf::RepeatedField< ::google::protobuf::uint64 >&
      name() const;
  ::google::protobuf::RepeatedField< ::google::protobuf::uint64 >*
      mutable_name();

  // optional string pseudonym = 3;
  bool has_pseudonym() const;
  void clear_pseudonym();
  static const int kPseudonymFieldNumber = 3;
  const ::std::string& pseudonym() const;
  void set_pseudonym(const ::std::string& value);
  void set_pseudonym(const char* value);
  void set_pseudonym(const char* value, size_t size);
  ::std::string* mutable_pseudonym();
  ::std::string* release_pseudonym();
  void set_allocated_pseudonym(::std::string* pseudonym);

  // repeated bool alive = 4 [packed = true];
  int alive_size() const;
  void clear_alive();
  static const int kAliveFieldNumber = 4;
  bool alive(int index) const;
  void set_alive(int index, bool value);
  void add_alive(bool value);
  const ::google::protobuf::RepeatedField< bool >&
      alive() const;
  ::google::protobuf::RepeatedField< bool >*
      mutable_alive();

  // @@protoc_insertion_point(class_scope:google.protobuf.testing.BadAuthor)
 private:
  inline void set_has_id();
  inline void clear_has_id();
  inline void set_has_pseudonym();
  inline void clear_has_pseudonym();

  ::google::protobuf::internal::InternalMetadataWithArena _internal_metadata_;
  ::google::protobuf::internal::HasBits<1> _has_bits_;
  mutable int _cached_size_;
  ::google::protobuf::RepeatedField< ::google::protobuf::uint64 > name_;
  ::google::protobuf::RepeatedField< bool > alive_;
  mutable int _alive_cached_byte_size_;
  ::google::protobuf::internal::ArenaStringPtr id_;
  ::google::protobuf::internal::ArenaStringPtr pseudonym_;
  friend void  protobuf_InitDefaults_google_2fprotobuf_2futil_2finternal_2ftestdata_2fbooks_2eproto_impl();
  friend void  protobuf_AddDesc_google_2fprotobuf_2futil_2finternal_2ftestdata_2fbooks_2eproto_impl();
  friend void protobuf_AssignDesc_google_2fprotobuf_2futil_2finternal_2ftestdata_2fbooks_2eproto();
  friend void protobuf_ShutdownFile_google_2fprotobuf_2futil_2finternal_2ftestdata_2fbooks_2eproto();

  void InitAsDefaultInstance();
};
extern ::google::protobuf::internal::ExplicitlyConstructed<BadAuthor> BadAuthor_default_instance_;

// -------------------------------------------------------------------

class Primitive : public ::google::protobuf::Message /* @@protoc_insertion_point(class_definition:google.protobuf.testing.Primitive) */ {
 public:
  Primitive();
  virtual ~Primitive();

  Primitive(const Primitive& from);

  inline Primitive& operator=(const Primitive& from) {
    CopyFrom(from);
    return *this;
  }

  inline const ::google::protobuf::UnknownFieldSet& unknown_fields() const {
    return _internal_metadata_.unknown_fields();
  }

  inline ::google::protobuf::UnknownFieldSet* mutable_unknown_fields() {
    return _internal_metadata_.mutable_unknown_fields();
  }

  static const ::google::protobuf::Descriptor* descriptor();
  static const Primitive& default_instance();

  static const Primitive* internal_default_instance();

  void Swap(Primitive* other);

  // implements Message ----------------------------------------------

  inline Primitive* New() const { return New(NULL); }

  Primitive* New(::google::protobuf::Arena* arena) const;
  void CopyFrom(const ::google::protobuf::Message& from);
  void MergeFrom(const ::google::protobuf::Message& from);
  void CopyFrom(const Primitive& from);
  void MergeFrom(const Primitive& from);
  void Clear();
  bool IsInitialized() const;

  size_t ByteSizeLong() const;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input);
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const;
  ::google::protobuf::uint8* InternalSerializeWithCachedSizesToArray(
      bool deterministic, ::google::protobuf::uint8* output) const;
  ::google::protobuf::uint8* SerializeWithCachedSizesToArray(::google::protobuf::uint8* output) const {
    return InternalSerializeWithCachedSizesToArray(false, output);
  }
  int GetCachedSize() const { return _cached_size_; }
  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const;
  void InternalSwap(Primitive* other);
  void UnsafeMergeFrom(const Primitive& from);
  private:
  inline ::google::protobuf::Arena* GetArenaNoVirtual() const {
    return _internal_metadata_.arena();
  }
  inline void* MaybeArenaPtr() const {
    return _internal_metadata_.raw_arena_ptr();
  }
  public:

  ::google::protobuf::Metadata GetMetadata() const;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // optional fixed32 fix32 = 1;
  bool has_fix32() const;
  void clear_fix32();
  static const int kFix32FieldNumber = 1;
  ::google::protobuf::uint32 fix32() const;
  void set_fix32(::google::protobuf::uint32 value);

  // optional uint32 u32 = 2;
  bool has_u32() const;
  void clear_u32();
  static const int kU32FieldNumber = 2;
  ::google::protobuf::uint32 u32() const;
  void set_u32(::google::protobuf::uint32 value);

  // optional int32 i32 = 3;
  bool has_i32() const;
  void clear_i32();
  static const int kI32FieldNumber = 3;
  ::google::protobuf::int32 i32() const;
  void set_i32(::google::protobuf::int32 value);

  // optional sfixed32 sf32 = 4;
  bool has_sf32() const;
  void clear_sf32();
  static const int kSf32FieldNumber = 4;
  ::google::protobuf::int32 sf32() const;
  void set_sf32(::google::protobuf::int32 value);

  // optional sint32 s32 = 5;
  bool has_s32() const;
  void clear_s32();
  static const int kS32FieldNumber = 5;
  ::google::protobuf::int32 s32() const;
  void set_s32(::google::protobuf::int32 value);

  // optional fixed64 fix64 = 6;
  bool has_fix64() const;
  void clear_fix64();
  static const int kFix64FieldNumber = 6;
  ::google::protobuf::uint64 fix64() const;
  void set_fix64(::google::protobuf::uint64 value);

  // optional uint64 u64 = 7;
  bool has_u64() const;
  void clear_u64();
  static const int kU64FieldNumber = 7;
  ::google::protobuf::uint64 u64() const;
  void set_u64(::google::protobuf::uint64 value);

  // optional int64 i64 = 8;
  bool has_i64() const;
  void clear_i64();
  static const int kI64FieldNumber = 8;
  ::google::protobuf::int64 i64() const;
  void set_i64(::google::protobuf::int64 value);

  // optional sfixed64 sf64 = 9;
  bool has_sf64() const;
  void clear_sf64();
  static const int kSf64FieldNumber = 9;
  ::google::protobuf::int64 sf64() const;
  void set_sf64(::google::protobuf::int64 value);

  // optional sint64 s64 = 10;
  bool has_s64() const;
  void clear_s64();
  static const int kS64FieldNumber = 10;
  ::google::protobuf::int64 s64() const;
  void set_s64(::google::protobuf::int64 value);

  // optional string str = 11;
  bool has_str() const;
  void clear_str();
  static const int kStrFieldNumber = 11;
  const ::std::string& str() const;
  void set_str(const ::std::string& value);
  void set_str(const char* value);
  void set_str(const char* value, size_t size);
  ::std::string* mutable_str();
  ::std::string* release_str();
  void set_allocated_str(::std::string* str);

  // optional bytes bytes = 12;
  bool has_bytes() const;
  void clear_bytes();
  static const int kBytesFieldNumber = 12;
  const ::std::string& bytes() const;
  void set_bytes(const ::std::string& value);
  void set_bytes(const char* value);
  void set_bytes(const void* value, size_t size);
  ::std::string* mutable_bytes();
  ::std::string* release_bytes();
  void set_allocated_bytes(::std::string* bytes);

  // optional float float = 13;
  bool has_float_() const;
  void clear_float_();
  static const int kFloatFieldNumber = 13;
  float float_() const;
  void set_float_(float value);

  // optional double double = 14;
  bool has_double_() const;
  void clear_double_();
  static const int kDoubleFieldNumber = 14;
  double double_() const;
  void set_double_(double value);

  // optional bool bool = 15;
  bool has_bool_() const;
  void clear_bool_();
  static const int kBoolFieldNumber = 15;
  bool bool_() const;
  void set_bool_(bool value);

  // repeated fixed32 rep_fix32 = 16;
  int rep_fix32_size() const;
  void clear_rep_fix32();
  static const int kRepFix32FieldNumber = 16;
  ::google::protobuf::uint32 rep_fix32(int index) const;
  void set_rep_fix32(int index, ::google::protobuf::uint32 value);
  void add_rep_fix32(::google::protobuf::uint32 value);
  const ::google::protobuf::RepeatedField< ::google::protobuf::uint32 >&
      rep_fix32() const;
  ::google::protobuf::RepeatedField< ::google::protobuf::uint32 >*
      mutable_rep_fix32();

  // repeated uint32 rep_u32 = 17;
  int rep_u32_size() const;
  void clear_rep_u32();
  static const int kRepU32FieldNumber = 17;
  ::google::protobuf::uint32 rep_u32(int index) const;
  void set_rep_u32(int index, ::google::protobuf::uint32 value);
  void add_rep_u32(::google::protobuf::uint32 value);
  const ::google::protobuf::RepeatedField< ::google::protobuf::uint32 >&
      rep_u32() const;
  ::google::protobuf::RepeatedField< ::google::protobuf::uint32 >*
      mutable_rep_u32();

  // repeated int32 rep_i32 = 18;
  int rep_i32_size() const;
  void clear_rep_i32();
  static const int kRepI32FieldNumber = 18;
  ::google::protobuf::int32 rep_i32(int index) const;
  void set_rep_i32(int index, ::google::protobuf::int32 value);
  void add_rep_i32(::google::protobuf::int32 value);
  const ::google::protobuf::RepeatedField< ::google::protobuf::int32 >&
      rep_i32() const;
  ::google::protobuf::RepeatedField< ::google::protobuf::int32 >*
      mutable_rep_i32();

  // repeated sfixed32 rep_sf32 = 19;
  int rep_sf32_size() const;
  void clear_rep_sf32();
  static const int kRepSf32FieldNumber = 19;
  ::google::protobuf::int32 rep_sf32(int index) const;
  void set_rep_sf32(int index, ::google::protobuf::int32 value);
  void add_rep_sf32(::google::protobuf::int32 value);
  const ::google::protobuf::RepeatedField< ::google::protobuf::int32 >&
      rep_sf32() const;
  ::google::protobuf::RepeatedField< ::google::protobuf::int32 >*
      mutable_rep_sf32();

  // repeated sint32 rep_s32 = 20;
  int rep_s32_size() const;
  void clear_rep_s32();
  static const int kRepS32FieldNumber = 20;
  ::google::protobuf::int32 rep_s32(int index) const;
  void set_rep_s32(int index, ::google::protobuf::int32 value);
  void add_rep_s32(::google::protobuf::int32 value);
  const ::google::protobuf::RepeatedField< ::google::protobuf::int32 >&
      rep_s32() const;
  ::google::protobuf::RepeatedField< ::google::protobuf::int32 >*
      mutable_rep_s32();

  // repeated fixed64 rep_fix64 = 21;
  int rep_fix64_size() const;
  void clear_rep_fix64();
  static const int kRepFix64FieldNumber = 21;
  ::google::protobuf::uint64 rep_fix64(int index) const;
  void set_rep_fix64(int index, ::google::protobuf::uint64 value);
  void add_rep_fix64(::google::protobuf::uint64 value);
  const ::google::protobuf::RepeatedField< ::google::protobuf::uint64 >&
      rep_fix64() const;
  ::google::protobuf::RepeatedField< ::google::protobuf::uint64 >*
      mutable_rep_fix64();

  // repeated uint64 rep_u64 = 22;
  int rep_u64_size() const;
  void clear_rep_u64();
  static const int kRepU64FieldNumber = 22;
  ::google::protobuf::uint64 rep_u64(int index) const;
  void set_rep_u64(int index, ::google::protobuf::uint64 value);
  void add_rep_u64(::google::protobuf::uint64 value);
  const ::google::protobuf::RepeatedField< ::google::protobuf::uint64 >&
      rep_u64() const;
  ::google::protobuf::RepeatedField< ::google::protobuf::uint64 >*
      mutable_rep_u64();

  // repeated int64 rep_i64 = 23;
  int rep_i64_size() const;
  void clear_rep_i64();
  static const int kRepI64FieldNumber = 23;
  ::google::protobuf::int64 rep_i64(int index) const;
  void set_rep_i64(int index, ::google::protobuf::int64 value);
  void add_rep_i64(::google::protobuf::int64 value);
  const ::google::protobuf::RepeatedField< ::google::protobuf::int64 >&
      rep_i64() const;
  ::google::protobuf::RepeatedField< ::google::protobuf::int64 >*
      mutable_rep_i64();

  // repeated sfixed64 rep_sf64 = 24;
  int rep_sf64_size() const;
  void clear_rep_sf64();
  static const int kRepSf64FieldNumber = 24;
  ::google::protobuf::int64 rep_sf64(int index) const;
  void set_rep_sf64(int index, ::google::protobuf::int64 value);
  void add_rep_sf64(::google::protobuf::int64 value);
  const ::google::protobuf::RepeatedField< ::google::protobuf::int64 >&
      rep_sf64() const;
  ::google::protobuf::RepeatedField< ::google::protobuf::int64 >*
      mutable_rep_sf64();

  // repeated sint64 rep_s64 = 25;
  int rep_s64_size() const;
  void clear_rep_s64();
  static const int kRepS64FieldNumber = 25;
  ::google::protobuf::int64 rep_s64(int index) const;
  void set_rep_s64(int index, ::google::protobuf::int64 value);
  void add_rep_s64(::google::protobuf::int64 value);
  const ::google::protobuf::RepeatedField< ::google::protobuf::int64 >&
      rep_s64() const;
  ::google::protobuf::RepeatedField< ::google::protobuf::int64 >*
      mutable_rep_s64();

  // repeated string rep_str = 26;
  int rep_str_size() const;
  void clear_rep_str();
  static const int kRepStrFieldNumber = 26;
  const ::std::string& rep_str(int index) const;
  ::std::string* mutable_rep_str(int index);
  void set_rep_str(int index, const ::std::string& value);
  void set_rep_str(int index, const char* value);
  void set_rep_str(int index, const char* value, size_t size);
  ::std::string* add_rep_str();
  void add_rep_str(const ::std::string& value);
  void add_rep_str(const char* value);
  void add_rep_str(const char* value, size_t size);
  const ::google::protobuf::RepeatedPtrField< ::std::string>& rep_str() const;
  ::google::protobuf::RepeatedPtrField< ::std::string>* mutable_rep_str();

  // repeated bytes rep_bytes = 27;
  int rep_bytes_size() const;
  void clear_rep_bytes();
  static const int kRepBytesFieldNumber = 27;
  const ::std::string& rep_bytes(int index) const;
  ::std::string* mutable_rep_bytes(int index);
  void set_rep_bytes(int index, const ::std::string& value);
  void set_rep_bytes(int index, const char* value);
  void set_rep_bytes(int index, const void* value, size_t size);
  ::std::string* add_rep_bytes();
  void add_rep_bytes(const ::std::string& value);
  void add_rep_bytes(const char* value);
  void add_rep_bytes(const void* value, size_t size);
  const ::google::protobuf::RepeatedPtrField< ::std::string>& rep_bytes() const;
  ::google::protobuf::RepeatedPtrField< ::std::string>* mutable_rep_bytes();

  // repeated float rep_float = 28;
  int rep_float_size() const;
  void clear_rep_float();
  static const int kRepFloatFieldNumber = 28;
  float rep_float(int index) const;
  void set_rep_float(int index, float value);
  void add_rep_float(float value);
  const ::google::protobuf::RepeatedField< float >&
      rep_float() const;
  ::google::protobuf::RepeatedField< float >*
      mutable_rep_float();

  // repeated double rep_double = 29;
  int rep_double_size() const;
  void clear_rep_double();
  static const int kRepDoubleFieldNumber = 29;
  double rep_double(int index) const;
  void set_rep_double(int index, double value);
  void add_rep_double(double value);
  const ::google::protobuf::RepeatedField< double >&
      rep_double() const;
  ::google::protobuf::RepeatedField< double >*
      mutable_rep_double();

  // repeated bool rep_bool = 30;
  int rep_bool_size() const;
  void clear_rep_bool();
  static const int kRepBoolFieldNumber = 30;
  bool rep_bool(int index) const;
  void set_rep_bool(int index, bool value);
  void add_rep_bool(bool value);
  const ::google::protobuf::RepeatedField< bool >&
      rep_bool() const;
  ::google::protobuf::RepeatedField< bool >*
      mutable_rep_bool();

  // @@protoc_insertion_point(class_scope:google.protobuf.testing.Primitive)
 private:
  inline void set_has_fix32();
  inline void clear_has_fix32();
  inline void set_has_u32();
  inline void clear_has_u32();
  inline void set_has_i32();
  inline void clear_has_i32();
  inline void set_has_sf32();
  inline void clear_has_sf32();
  inline void set_has_s32();
  inline void clear_has_s32();
  inline void set_has_fix64();
  inline void clear_has_fix64();
  inline void set_has_u64();
  inline void clear_has_u64();
  inline void set_has_i64();
  inline void clear_has_i64();
  inline void set_has_sf64();
  inline void clear_has_sf64();
  inline void set_has_s64();
  inline void clear_has_s64();
  inline void set_has_str();
  inline void clear_has_str();
  inline void set_has_bytes();
  inline void clear_has_bytes();
  inline void set_has_float_();
  inline void clear_has_float_();
  inline void set_has_double_();
  inline void clear_has_double_();
  inline void set_has_bool_();
  inline void clear_has_bool_();

  ::google::protobuf::internal::InternalMetadataWithArena _internal_metadata_;
  ::google::protobuf::internal::HasBits<1> _has_bits_;
  mutable int _cached_size_;
  ::google::protobuf::RepeatedField< ::google::protobuf::uint32 > rep_fix32_;
  ::google::protobuf::RepeatedField< ::google::protobuf::uint32 > rep_u32_;
  ::google::protobuf::RepeatedField< ::google::protobuf::int32 > rep_i32_;
  ::google::protobuf::RepeatedField< ::google::protobuf::int32 > rep_sf32_;
  ::google::protobuf::RepeatedField< ::google::protobuf::int32 > rep_s32_;
  ::google::protobuf::RepeatedField< ::google::protobuf::uint64 > rep_fix64_;
  ::google::protobuf::RepeatedField< ::google::protobuf::uint64 > rep_u64_;
  ::google::protobuf::RepeatedField< ::google::protobuf::int64 > rep_i64_;
  ::google::protobuf::RepeatedField< ::google::protobuf::int64 > rep_sf64_;
  ::google::protobuf::RepeatedField< ::google::protobuf::int64 > rep_s64_;
  ::google::protobuf::RepeatedPtrField< ::std::string> rep_str_;
  ::google::protobuf::RepeatedPtrField< ::std::string> rep_bytes_;
  ::google::protobuf::RepeatedField< float > rep_float_;
  ::google::protobuf::RepeatedField< double > rep_double_;
  ::google::protobuf::RepeatedField< bool > rep_bool_;
  ::google::protobuf::internal::ArenaStringPtr str_;
  ::google::protobuf::internal::ArenaStringPtr bytes_;
  ::google::protobuf::uint32 fix32_;
  ::google::protobuf::uint32 u32_;
  ::google::protobuf::int32 i32_;
  ::google::protobuf::int32 sf32_;
  ::google::protobuf::uint64 fix64_;
  ::google::protobuf::uint64 u64_;
  ::google::protobuf::int64 i64_;
  ::google::protobuf::int64 sf64_;
  ::google::protobuf::int32 s32_;
  float float__;
  ::google::protobuf::int64 s64_;
  double double__;
  bool bool__;
  friend void  protobuf_InitDefaults_google_2fprotobuf_2futil_2finternal_2ftestdata_2fbooks_2eproto_impl();
  friend void  protobuf_AddDesc_google_2fprotobuf_2futil_2finternal_2ftestdata_2fbooks_2eproto_impl();
  friend void protobuf_AssignDesc_google_2fprotobuf_2futil_2finternal_2ftestdata_2fbooks_2eproto();
  friend void protobuf_ShutdownFile_google_2fprotobuf_2futil_2finternal_2ftestdata_2fbooks_2eproto();

  void InitAsDefaultInstance();
};
extern ::google::protobuf::internal::ExplicitlyConstructed<Primitive> Primitive_default_instance_;

// -------------------------------------------------------------------

class PackedPrimitive : public ::google::protobuf::Message /* @@protoc_insertion_point(class_definition:google.protobuf.testing.PackedPrimitive) */ {
 public:
  PackedPrimitive();
  virtual ~PackedPrimitive();

  PackedPrimitive(const PackedPrimitive& from);

  inline PackedPrimitive& operator=(const PackedPrimitive& from) {
    CopyFrom(from);
    return *this;
  }

  inline const ::google::protobuf::UnknownFieldSet& unknown_fields() const {
    return _internal_metadata_.unknown_fields();
  }

  inline ::google::protobuf::UnknownFieldSet* mutable_unknown_fields() {
    return _internal_metadata_.mutable_unknown_fields();
  }

  static const ::google::protobuf::Descriptor* descriptor();
  static const PackedPrimitive& default_instance();

  static const PackedPrimitive* internal_default_instance();

  void Swap(PackedPrimitive* other);

  // implements Message ----------------------------------------------

  inline PackedPrimitive* New() const { return New(NULL); }

  PackedPrimitive* New(::google::protobuf::Arena* arena) const;
  void CopyFrom(const ::google::protobuf::Message& from);
  void MergeFrom(const ::google::protobuf::Message& from);
  void CopyFrom(const PackedPrimitive& from);
  void MergeFrom(const PackedPrimitive& from);
  void Clear();
  bool IsInitialized() const;

  size_t ByteSizeLong() const;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input);
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const;
  ::google::protobuf::uint8* InternalSerializeWithCachedSizesToArray(
      bool deterministic, ::google::protobuf::uint8* output) const;
  ::google::protobuf::uint8* SerializeWithCachedSizesToArray(::google::protobuf::uint8* output) const {
    return InternalSerializeWithCachedSizesToArray(false, output);
  }
  int GetCachedSize() const { return _cached_size_; }
  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const;
  void InternalSwap(PackedPrimitive* other);
  void UnsafeMergeFrom(const PackedPrimitive& from);
  private:
  inline ::google::protobuf::Arena* GetArenaNoVirtual() const {
    return _internal_metadata_.arena();
  }
  inline void* MaybeArenaPtr() const {
    return _internal_metadata_.raw_arena_ptr();
  }
  public:

  ::google::protobuf::Metadata GetMetadata() const;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // repeated fixed32 rep_fix32 = 16 [packed = true];
  int rep_fix32_size() const;
  void clear_rep_fix32();
  static const int kRepFix32FieldNumber = 16;
  ::google::protobuf::uint32 rep_fix32(int index) const;
  void set_rep_fix32(int index, ::google::protobuf::uint32 value);
  void add_rep_fix32(::google::protobuf::uint32 value);
  const ::google::protobuf::RepeatedField< ::google::protobuf::uint32 >&
      rep_fix32() const;
  ::google::protobuf::RepeatedField< ::google::protobuf::uint32 >*
      mutable_rep_fix32();

  // repeated uint32 rep_u32 = 17 [packed = true];
  int rep_u32_size() const;
  void clear_rep_u32();
  static const int kRepU32FieldNumber = 17;
  ::google::protobuf::uint32 rep_u32(int index) const;
  void set_rep_u32(int index, ::google::protobuf::uint32 value);
  void add_rep_u32(::google::protobuf::uint32 value);
  const ::google::protobuf::RepeatedField< ::google::protobuf::uint32 >&
      rep_u32() const;
  ::google::protobuf::RepeatedField< ::google::protobuf::uint32 >*
      mutable_rep_u32();

  // repeated int32 rep_i32 = 18 [packed = true];
  int rep_i32_size() const;
  void clear_rep_i32();
  static const int kRepI32FieldNumber = 18;
  ::google::protobuf::int32 rep_i32(int index) const;
  void set_rep_i32(int index, ::google::protobuf::int32 value);
  void add_rep_i32(::google::protobuf::int32 value);
  const ::google::protobuf::RepeatedField< ::google::protobuf::int32 >&
      rep_i32() const;
  ::google::protobuf::RepeatedField< ::google::protobuf::int32 >*
      mutable_rep_i32();

  // repeated sfixed32 rep_sf32 = 19 [packed = true];
  int rep_sf32_size() const;
  void clear_rep_sf32();
  static const int kRepSf32FieldNumber = 19;
  ::google::protobuf::int32 rep_sf32(int index) const;
  void set_rep_sf32(int index, ::google::protobuf::int32 value);
  void add_rep_sf32(::google::protobuf::int32 value);
  const ::google::protobuf::RepeatedField< ::google::protobuf::int32 >&
      rep_sf32() const;
  ::google::protobuf::RepeatedField< ::google::protobuf::int32 >*
      mutable_rep_sf32();

  // repeated sint32 rep_s32 = 20 [packed = true];
  int rep_s32_size() const;
  void clear_rep_s32();
  static const int kRepS32FieldNumber = 20;
  ::google::protobuf::int32 rep_s32(int index) const;
  void set_rep_s32(int index, ::google::protobuf::int32 value);
  void add_rep_s32(::google::protobuf::int32 value);
  const ::google::protobuf::RepeatedField< ::google::protobuf::int32 >&
      rep_s32() const;
  ::google::protobuf::RepeatedField< ::google::protobuf::int32 >*
      mutable_rep_s32();

  // repeated fixed64 rep_fix64 = 21 [packed = true];
  int rep_fix64_size() const;
  void clear_rep_fix64();
  static const int kRepFix64FieldNumber = 21;
  ::google::protobuf::uint64 rep_fix64(int index) const;
  void set_rep_fix64(int index, ::google::protobuf::uint64 value);
  void add_rep_fix64(::google::protobuf::uint64 value);
  const ::google::protobuf::RepeatedField< ::google::protobuf::uint64 >&
      rep_fix64() const;
  ::google::protobuf::RepeatedField< ::google::protobuf::uint64 >*
      mutable_rep_fix64();

  // repeated uint64 rep_u64 = 22 [packed = true];
  int rep_u64_size() const;
  void clear_rep_u64();
  static const int kRepU64FieldNumber = 22;
  ::google::protobuf::uint64 rep_u64(int index) const;
  void set_rep_u64(int index, ::google::protobuf::uint64 value);
  void add_rep_u64(::google::protobuf::uint64 value);
  const ::google::protobuf::RepeatedField< ::google::protobuf::uint64 >&
      rep_u64() const;
  ::google::protobuf::RepeatedField< ::google::protobuf::uint64 >*
      mutable_rep_u64();

  // repeated int64 rep_i64 = 23 [packed = true];
  int rep_i64_size() const;
  void clear_rep_i64();
  static const int kRepI64FieldNumber = 23;
  ::google::protobuf::int64 rep_i64(int index) const;
  void set_rep_i64(int index, ::google::protobuf::int64 value);
  void add_rep_i64(::google::protobuf::int64 value);
  const ::google::protobuf::RepeatedField< ::google::protobuf::int64 >&
      rep_i64() const;
  ::google::protobuf::RepeatedField< ::google::protobuf::int64 >*
      mutable_rep_i64();

  // repeated sfixed64 rep_sf64 = 24 [packed = true];
  int rep_sf64_size() const;
  void clear_rep_sf64();
  static const int kRepSf64FieldNumber = 24;
  ::google::protobuf::int64 rep_sf64(int index) const;
  void set_rep_sf64(int index, ::google::protobuf::int64 value);
  void add_rep_sf64(::google::protobuf::int64 value);
  const ::google::protobuf::RepeatedField< ::google::protobuf::int64 >&
      rep_sf64() const;
  ::google::protobuf::RepeatedField< ::google::protobuf::int64 >*
      mutable_rep_sf64();

  // repeated sint64 rep_s64 = 25 [packed = true];
  int rep_s64_size() const;
  void clear_rep_s64();
  static const int kRepS64FieldNumber = 25;
  ::google::protobuf::int64 rep_s64(int index) const;
  void set_rep_s64(int index, ::google::protobuf::int64 value);
  void add_rep_s64(::google::protobuf::int64 value);
  const ::google::protobuf::RepeatedField< ::google::protobuf::int64 >&
      rep_s64() const;
  ::google::protobuf::RepeatedField< ::google::protobuf::int64 >*
      mutable_rep_s64();

  // repeated float rep_float = 28 [packed = true];
  int rep_float_size() const;
  void clear_rep_float();
  static const int kRepFloatFieldNumber = 28;
  float rep_float(int index) const;
  void set_rep_float(int index, float value);
  void add_rep_float(float value);
  const ::google::protobuf::RepeatedField< float >&
      rep_float() const;
  ::google::protobuf::RepeatedField< float >*
      mutable_rep_float();

  // repeated double rep_double = 29 [packed = true];
  int rep_double_size() const;
  void clear_rep_double();
  static const int kRepDoubleFieldNumber = 29;
  double rep_double(int index) const;
  void set_rep_double(int index, double value);
  void add_rep_double(double value);
  const ::google::protobuf::RepeatedField< double >&
      rep_double() const;
  ::google::protobuf::RepeatedField< double >*
      mutable_rep_double();

  // repeated bool rep_bool = 30 [packed = true];
  int rep_bool_size() const;
  void clear_rep_bool();
  static const int kRepBoolFieldNumber = 30;
  bool rep_bool(int index) const;
  void set_rep_bool(int index, bool value);
  void add_rep_bool(bool value);
  const ::google::protobuf::RepeatedField< bool >&
      rep_bool() const;
  ::google::protobuf::RepeatedField< bool >*
      mutable_rep_bool();

  // @@protoc_insertion_point(class_scope:google.protobuf.testing.PackedPrimitive)
 private:

  ::google::protobuf::internal::InternalMetadataWithArena _internal_metadata_;
  ::google::protobuf::internal::HasBits<1> _has_bits_;
  mutable int _cached_size_;
  ::google::protobuf::RepeatedField< ::google::protobuf::uint32 > rep_fix32_;
  mutable int _rep_fix32_cached_byte_size_;
  ::google::protobuf::RepeatedField< ::google::protobuf::uint32 > rep_u32_;
  mutable int _rep_u32_cached_byte_size_;
  ::google::protobuf::RepeatedField< ::google::protobuf::int32 > rep_i32_;
  mutable int _rep_i32_cached_byte_size_;
  ::google::protobuf::RepeatedField< ::google::protobuf::int32 > rep_sf32_;
  mutable int _rep_sf32_cached_byte_size_;
  ::google::protobuf::RepeatedField< ::google::protobuf::int32 > rep_s32_;
  mutable int _rep_s32_cached_byte_size_;
  ::google::protobuf::RepeatedField< ::google::protobuf::uint64 > rep_fix64_;
  mutable int _rep_fix64_cached_byte_size_;
  ::google::protobuf::RepeatedField< ::google::protobuf::uint64 > rep_u64_;
  mutable int _rep_u64_cached_byte_size_;
  ::google::protobuf::RepeatedField< ::google::protobuf::int64 > rep_i64_;
  mutable int _rep_i64_cached_byte_size_;
  ::google::protobuf::RepeatedField< ::google::protobuf::int64 > rep_sf64_;
  mutable int _rep_sf64_cached_byte_size_;
  ::google::protobuf::RepeatedField< ::google::protobuf::int64 > rep_s64_;
  mutable int _rep_s64_cached_byte_size_;
  ::google::protobuf::RepeatedField< float > rep_float_;
  mutable int _rep_float_cached_byte_size_;
  ::google::protobuf::RepeatedField< double > rep_double_;
  mutable int _rep_double_cached_byte_size_;
  ::google::protobuf::RepeatedField< bool > rep_bool_;
  mutable int _rep_bool_cached_byte_size_;
  friend void  protobuf_InitDefaults_google_2fprotobuf_2futil_2finternal_2ftestdata_2fbooks_2eproto_impl();
  friend void  protobuf_AddDesc_google_2fprotobuf_2futil_2finternal_2ftestdata_2fbooks_2eproto_impl();
  friend void protobuf_AssignDesc_google_2fprotobuf_2futil_2finternal_2ftestdata_2fbooks_2eproto();
  friend void protobuf_ShutdownFile_google_2fprotobuf_2futil_2finternal_2ftestdata_2fbooks_2eproto();

  void InitAsDefaultInstance();
};
extern ::google::protobuf::internal::ExplicitlyConstructed<PackedPrimitive> PackedPrimitive_default_instance_;

// -------------------------------------------------------------------

class NestedBook : public ::google::protobuf::Message /* @@protoc_insertion_point(class_definition:google.protobuf.testing.NestedBook) */ {
 public:
  NestedBook();
  virtual ~NestedBook();

  NestedBook(const NestedBook& from);

  inline NestedBook& operator=(const NestedBook& from) {
    CopyFrom(from);
    return *this;
  }

  inline const ::google::protobuf::UnknownFieldSet& unknown_fields() const {
    return _internal_metadata_.unknown_fields();
  }

  inline ::google::protobuf::UnknownFieldSet* mutable_unknown_fields() {
    return _internal_metadata_.mutable_unknown_fields();
  }

  static const ::google::protobuf::Descriptor* descriptor();
  static const NestedBook& default_instance();

  static const NestedBook* internal_default_instance();

  void Swap(NestedBook* other);

  // implements Message ----------------------------------------------

  inline NestedBook* New() const { return New(NULL); }

  NestedBook* New(::google::protobuf::Arena* arena) const;
  void CopyFrom(const ::google::protobuf::Message& from);
  void MergeFrom(const ::google::protobuf::Message& from);
  void CopyFrom(const NestedBook& from);
  void MergeFrom(const NestedBook& from);
  void Clear();
  bool IsInitialized() const;

  size_t ByteSizeLong() const;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input);
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const;
  ::google::protobuf::uint8* InternalSerializeWithCachedSizesToArray(
      bool deterministic, ::google::protobuf::uint8* output) const;
  ::google::protobuf::uint8* SerializeWithCachedSizesToArray(::google::protobuf::uint8* output) const {
    return InternalSerializeWithCachedSizesToArray(false, output);
  }
  int GetCachedSize() const { return _cached_size_; }
  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const;
  void InternalSwap(NestedBook* other);
  void UnsafeMergeFrom(const NestedBook& from);
  private:
  inline ::google::protobuf::Arena* GetArenaNoVirtual() const {
    return _internal_metadata_.arena();
  }
  inline void* MaybeArenaPtr() const {
    return _internal_metadata_.raw_arena_ptr();
  }
  public:

  ::google::protobuf::Metadata GetMetadata() const;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // optional .google.protobuf.testing.Book book = 1;
  bool has_book() const;
  void clear_book();
  static const int kBookFieldNumber = 1;
  const ::google::protobuf::testing::Book& book() const;
  ::google::protobuf::testing::Book* mutable_book();
  ::google::protobuf::testing::Book* release_book();
  void set_allocated_book(::google::protobuf::testing::Book* book);

  static const int kAnotherBookFieldNumber = 301;
  static ::google::protobuf::internal::ExtensionIdentifier< ::google::protobuf::testing::Book,
      ::google::protobuf::internal::MessageTypeTraits< ::google::protobuf::testing::NestedBook >, 11, false >
    another_book;
  // @@protoc_insertion_point(class_scope:google.protobuf.testing.NestedBook)
 private:
  inline void set_has_book();
  inline void clear_has_book();

  ::google::protobuf::internal::InternalMetadataWithArena _internal_metadata_;
  ::google::protobuf::internal::HasBits<1> _has_bits_;
  mutable int _cached_size_;
  ::google::protobuf::testing::Book* book_;
  friend void  protobuf_InitDefaults_google_2fprotobuf_2futil_2finternal_2ftestdata_2fbooks_2eproto_impl();
  friend void  protobuf_AddDesc_google_2fprotobuf_2futil_2finternal_2ftestdata_2fbooks_2eproto_impl();
  friend void protobuf_AssignDesc_google_2fprotobuf_2futil_2finternal_2ftestdata_2fbooks_2eproto();
  friend void protobuf_ShutdownFile_google_2fprotobuf_2futil_2finternal_2ftestdata_2fbooks_2eproto();

  void InitAsDefaultInstance();
};
extern ::google::protobuf::internal::ExplicitlyConstructed<NestedBook> NestedBook_default_instance_;

// -------------------------------------------------------------------

class BadNestedBook : public ::google::protobuf::Message /* @@protoc_insertion_point(class_definition:google.protobuf.testing.BadNestedBook) */ {
 public:
  BadNestedBook();
  virtual ~BadNestedBook();

  BadNestedBook(const BadNestedBook& from);

  inline BadNestedBook& operator=(const BadNestedBook& from) {
    CopyFrom(from);
    return *this;
  }

  inline const ::google::protobuf::UnknownFieldSet& unknown_fields() const {
    return _internal_metadata_.unknown_fields();
  }

  inline ::google::protobuf::UnknownFieldSet* mutable_unknown_fields() {
    return _internal_metadata_.mutable_unknown_fields();
  }

  static const ::google::protobuf::Descriptor* descriptor();
  static const BadNestedBook& default_instance();

  static const BadNestedBook* internal_default_instance();

  void Swap(BadNestedBook* other);

  // implements Message ----------------------------------------------

  inline BadNestedBook* New() const { return New(NULL); }

  BadNestedBook* New(::google::protobuf::Arena* arena) const;
  void CopyFrom(const ::google::protobuf::Message& from);
  void MergeFrom(const ::google::protobuf::Message& from);
  void CopyFrom(const BadNestedBook& from);
  void MergeFrom(const BadNestedBook& from);
  void Clear();
  bool IsInitialized() const;

  size_t ByteSizeLong() const;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input);
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const;
  ::google::protobuf::uint8* InternalSerializeWithCachedSizesToArray(
      bool deterministic, ::google::protobuf::uint8* output) const;
  ::google::protobuf::uint8* SerializeWithCachedSizesToArray(::google::protobuf::uint8* output) const {
    return InternalSerializeWithCachedSizesToArray(false, output);
  }
  int GetCachedSize() const { return _cached_size_; }
  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const;
  void InternalSwap(BadNestedBook* other);
  void UnsafeMergeFrom(const BadNestedBook& from);
  private:
  inline ::google::protobuf::Arena* GetArenaNoVirtual() const {
    return _internal_metadata_.arena();
  }
  inline void* MaybeArenaPtr() const {
    return _internal_metadata_.raw_arena_ptr();
  }
  public:

  ::google::protobuf::Metadata GetMetadata() const;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // repeated uint32 book = 1 [packed = true];
  int book_size() const;
  void clear_book();
  static const int kBookFieldNumber = 1;
  ::google::protobuf::uint32 book(int index) const;
  void set_book(int index, ::google::protobuf::uint32 value);
  void add_book(::google::protobuf::uint32 value);
  const ::google::protobuf::RepeatedField< ::google::protobuf::uint32 >&
      book() const;
  ::google::protobuf::RepeatedField< ::google::protobuf::uint32 >*
      mutable_book();

  // @@protoc_insertion_point(class_scope:google.protobuf.testing.BadNestedBook)
 private:

  ::google::protobuf::internal::InternalMetadataWithArena _internal_metadata_;
  ::google::protobuf::internal::HasBits<1> _has_bits_;
  mutable int _cached_size_;
  ::google::protobuf::RepeatedField< ::google::protobuf::uint32 > book_;
  mutable int _book_cached_byte_size_;
  friend void  protobuf_InitDefaults_google_2fprotobuf_2futil_2finternal_2ftestdata_2fbooks_2eproto_impl();
  friend void  protobuf_AddDesc_google_2fprotobuf_2futil_2finternal_2ftestdata_2fbooks_2eproto_impl();
  friend void protobuf_AssignDesc_google_2fprotobuf_2futil_2finternal_2ftestdata_2fbooks_2eproto();
  friend void protobuf_ShutdownFile_google_2fprotobuf_2futil_2finternal_2ftestdata_2fbooks_2eproto();

  void InitAsDefaultInstance();
};
extern ::google::protobuf::internal::ExplicitlyConstructed<BadNestedBook> BadNestedBook_default_instance_;

// -------------------------------------------------------------------

class Cyclic : public ::google::protobuf::Message /* @@protoc_insertion_point(class_definition:google.protobuf.testing.Cyclic) */ {
 public:
  Cyclic();
  virtual ~Cyclic();

  Cyclic(const Cyclic& from);

  inline Cyclic& operator=(const Cyclic& from) {
    CopyFrom(from);
    return *this;
  }

  inline const ::google::protobuf::UnknownFieldSet& unknown_fields() const {
    return _internal_metadata_.unknown_fields();
  }

  inline ::google::protobuf::UnknownFieldSet* mutable_unknown_fields() {
    return _internal_metadata_.mutable_unknown_fields();
  }

  static const ::google::protobuf::Descriptor* descriptor();
  static const Cyclic& default_instance();

  static const Cyclic* internal_default_instance();

  void Swap(Cyclic* other);

  // implements Message ----------------------------------------------

  inline Cyclic* New() const { return New(NULL); }

  Cyclic* New(::google::protobuf::Arena* arena) const;
  void CopyFrom(const ::google::protobuf::Message& from);
  void MergeFrom(const ::google::protobuf::Message& from);
  void CopyFrom(const Cyclic& from);
  void MergeFrom(const Cyclic& from);
  void Clear();
  bool IsInitialized() const;

  size_t ByteSizeLong() const;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input);
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const;
  ::google::protobuf::uint8* InternalSerializeWithCachedSizesToArray(
      bool deterministic, ::google::protobuf::uint8* output) const;
  ::google::protobuf::uint8* SerializeWithCachedSizesToArray(::google::protobuf::uint8* output) const {
    return InternalSerializeWithCachedSizesToArray(false, output);
  }
  int GetCachedSize() const { return _cached_size_; }
  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const;
  void InternalSwap(Cyclic* other);
  void UnsafeMergeFrom(const Cyclic& from);
  private:
  inline ::google::protobuf::Arena* GetArenaNoVirtual() const {
    return _internal_metadata_.arena();
  }
  inline void* MaybeArenaPtr() const {
    return _internal_metadata_.raw_arena_ptr();
  }
  public:

  ::google::protobuf::Metadata GetMetadata() const;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // optional int32 m_int = 1;
  bool has_m_int() const;
  void clear_m_int();
  static const int kMIntFieldNumber = 1;
  ::google::protobuf::int32 m_int() const;
  void set_m_int(::google::protobuf::int32 value);

  // optional string m_str = 2;
  bool has_m_str() const;
  void clear_m_str();
  static const int kMStrFieldNumber = 2;
  const ::std::string& m_str() const;
  void set_m_str(const ::std::string& value);
  void set_m_str(const char* value);
  void set_m_str(const char* value, size_t size);
  ::std::string* mutable_m_str();
  ::std::string* release_m_str();
  void set_allocated_m_str(::std::string* m_str);

  // optional .google.protobuf.testing.Book m_book = 3;
  bool has_m_book() const;
  void clear_m_book();
  static const int kMBookFieldNumber = 3;
  const ::google::protobuf::testing::Book& m_book() const;
  ::google::protobuf::testing::Book* mutable_m_book();
  ::google::protobuf::testing::Book* release_m_book();
  void set_allocated_m_book(::google::protobuf::testing::Book* m_book);

  // repeated .google.protobuf.testing.Author m_author = 5;
  int m_author_size() const;
  void clear_m_author();
  static const int kMAuthorFieldNumber = 5;
  const ::google::protobuf::testing::Author& m_author(int index) const;
  ::google::protobuf::testing::Author* mutable_m_author(int index);
  ::google::protobuf::testing::Author* add_m_author();
  ::google::protobuf::RepeatedPtrField< ::google::protobuf::testing::Author >*
      mutable_m_author();
  const ::google::protobuf::RepeatedPtrField< ::google::protobuf::testing::Author >&
      m_author() const;

  // optional .google.protobuf.testing.Cyclic m_cyclic = 4;
  bool has_m_cyclic() const;
  void clear_m_cyclic();
  static const int kMCyclicFieldNumber = 4;
  const ::google::protobuf::testing::Cyclic& m_cyclic() const;
  ::google::protobuf::testing::Cyclic* mutable_m_cyclic();
  ::google::protobuf::testing::Cyclic* release_m_cyclic();
  void set_allocated_m_cyclic(::google::protobuf::testing::Cyclic* m_cyclic);

  // @@protoc_insertion_point(class_scope:google.protobuf.testing.Cyclic)
 private:
  inline void set_has_m_int();
  inline void clear_has_m_int();
  inline void set_has_m_str();
  inline void clear_has_m_str();
  inline void set_has_m_book();
  inline void clear_has_m_book();
  inline void set_has_m_cyclic();
  inline void clear_has_m_cyclic();

  ::google::protobuf::internal::InternalMetadataWithArena _internal_metadata_;
  ::google::protobuf::internal::HasBits<1> _has_bits_;
  mutable int _cached_size_;
  ::google::protobuf::RepeatedPtrField< ::google::protobuf::testing::Author > m_author_;
  ::google::protobuf::internal::ArenaStringPtr m_str_;
  ::google::protobuf::testing::Book* m_book_;
  ::google::protobuf::testing::Cyclic* m_cyclic_;
  ::google::protobuf::int32 m_int_;
  friend void  protobuf_InitDefaults_google_2fprotobuf_2futil_2finternal_2ftestdata_2fbooks_2eproto_impl();
  friend void  protobuf_AddDesc_google_2fprotobuf_2futil_2finternal_2ftestdata_2fbooks_2eproto_impl();
  friend void protobuf_AssignDesc_google_2fprotobuf_2futil_2finternal_2ftestdata_2fbooks_2eproto();
  friend void protobuf_ShutdownFile_google_2fprotobuf_2futil_2finternal_2ftestdata_2fbooks_2eproto();

  void InitAsDefaultInstance();
};
extern ::google::protobuf::internal::ExplicitlyConstructed<Cyclic> Cyclic_default_instance_;

// ===================================================================

static const int kMoreAuthorFieldNumber = 201;
extern ::google::protobuf::internal::ExtensionIdentifier< ::google::protobuf::testing::Book,
    ::google::protobuf::internal::RepeatedMessageTypeTraits< ::google::protobuf::testing::Author >, 11, false >
  more_author;

// ===================================================================

#if !PROTOBUF_INLINE_NOT_IN_HEADERS
// Book_Data

// optional uint32 year = 7;
inline bool Book_Data::has_year() const {
  return (_has_bits_[0] & 0x00000001u) != 0;
}
inline void Book_Data::set_has_year() {
  _has_bits_[0] |= 0x00000001u;
}
inline void Book_Data::clear_has_year() {
  _has_bits_[0] &= ~0x00000001u;
}
inline void Book_Data::clear_year() {
  year_ = 0u;
  clear_has_year();
}
inline ::google::protobuf::uint32 Book_Data::year() const {
  // @@protoc_insertion_point(field_get:google.protobuf.testing.Book.Data.year)
  return year_;
}
inline void Book_Data::set_year(::google::protobuf::uint32 value) {
  set_has_year();
  year_ = value;
  // @@protoc_insertion_point(field_set:google.protobuf.testing.Book.Data.year)
}

// optional string copyright = 8;
inline bool Book_Data::has_copyright() const {
  return (_has_bits_[0] & 0x00000002u) != 0;
}
inline void Book_Data::set_has_copyright() {
  _has_bits_[0] |= 0x00000002u;
}
inline void Book_Data::clear_has_copyright() {
  _has_bits_[0] &= ~0x00000002u;
}
inline void Book_Data::clear_copyright() {
  copyright_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  clear_has_copyright();
}
inline const ::std::string& Book_Data::copyright() const {
  // @@protoc_insertion_point(field_get:google.protobuf.testing.Book.Data.copyright)
  return copyright_.GetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void Book_Data::set_copyright(const ::std::string& value) {
  set_has_copyright();
  copyright_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:google.protobuf.testing.Book.Data.copyright)
}
inline void Book_Data::set_copyright(const char* value) {
  set_has_copyright();
  copyright_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:google.protobuf.testing.Book.Data.copyright)
}
inline void Book_Data::set_copyright(const char* value, size_t size) {
  set_has_copyright();
  copyright_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:google.protobuf.testing.Book.Data.copyright)
}
inline ::std::string* Book_Data::mutable_copyright() {
  set_has_copyright();
  // @@protoc_insertion_point(field_mutable:google.protobuf.testing.Book.Data.copyright)
  return copyright_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline ::std::string* Book_Data::release_copyright() {
  // @@protoc_insertion_point(field_release:google.protobuf.testing.Book.Data.copyright)
  clear_has_copyright();
  return copyright_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void Book_Data::set_allocated_copyright(::std::string* copyright) {
  if (copyright != NULL) {
    set_has_copyright();
  } else {
    clear_has_copyright();
  }
  copyright_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), copyright);
  // @@protoc_insertion_point(field_set_allocated:google.protobuf.testing.Book.Data.copyright)
}

inline const Book_Data* Book_Data::internal_default_instance() {
  return &Book_Data_default_instance_.get();
}
// -------------------------------------------------------------------

// Book_Label

// optional string key = 1;
inline bool Book_Label::has_key() const {
  return (_has_bits_[0] & 0x00000001u) != 0;
}
inline void Book_Label::set_has_key() {
  _has_bits_[0] |= 0x00000001u;
}
inline void Book_Label::clear_has_key() {
  _has_bits_[0] &= ~0x00000001u;
}
inline void Book_Label::clear_key() {
  key_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  clear_has_key();
}
inline const ::std::string& Book_Label::key() const {
  // @@protoc_insertion_point(field_get:google.protobuf.testing.Book.Label.key)
  return key_.GetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void Book_Label::set_key(const ::std::string& value) {
  set_has_key();
  key_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:google.protobuf.testing.Book.Label.key)
}
inline void Book_Label::set_key(const char* value) {
  set_has_key();
  key_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:google.protobuf.testing.Book.Label.key)
}
inline void Book_Label::set_key(const char* value, size_t size) {
  set_has_key();
  key_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:google.protobuf.testing.Book.Label.key)
}
inline ::std::string* Book_Label::mutable_key() {
  set_has_key();
  // @@protoc_insertion_point(field_mutable:google.protobuf.testing.Book.Label.key)
  return key_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline ::std::string* Book_Label::release_key() {
  // @@protoc_insertion_point(field_release:google.protobuf.testing.Book.Label.key)
  clear_has_key();
  return key_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void Book_Label::set_allocated_key(::std::string* key) {
  if (key != NULL) {
    set_has_key();
  } else {
    clear_has_key();
  }
  key_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), key);
  // @@protoc_insertion_point(field_set_allocated:google.protobuf.testing.Book.Label.key)
}

// optional string value = 2;
inline bool Book_Label::has_value() const {
  return (_has_bits_[0] & 0x00000002u) != 0;
}
inline void Book_Label::set_has_value() {
  _has_bits_[0] |= 0x00000002u;
}
inline void Book_Label::clear_has_value() {
  _has_bits_[0] &= ~0x00000002u;
}
inline void Book_Label::clear_value() {
  value_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  clear_has_value();
}
inline const ::std::string& Book_Label::value() const {
  // @@protoc_insertion_point(field_get:google.protobuf.testing.Book.Label.value)
  return value_.GetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void Book_Label::set_value(const ::std::string& value) {
  set_has_value();
  value_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:google.protobuf.testing.Book.Label.value)
}
inline void Book_Label::set_value(const char* value) {
  set_has_value();
  value_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:google.protobuf.testing.Book.Label.value)
}
inline void Book_Label::set_value(const char* value, size_t size) {
  set_has_value();
  value_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:google.protobuf.testing.Book.Label.value)
}
inline ::std::string* Book_Label::mutable_value() {
  set_has_value();
  // @@protoc_insertion_point(field_mutable:google.protobuf.testing.Book.Label.value)
  return value_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline ::std::string* Book_Label::release_value() {
  // @@protoc_insertion_point(field_release:google.protobuf.testing.Book.Label.value)
  clear_has_value();
  return value_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void Book_Label::set_allocated_value(::std::string* value) {
  if (value != NULL) {
    set_has_value();
  } else {
    clear_has_value();
  }
  value_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set_allocated:google.protobuf.testing.Book.Label.value)
}

inline const Book_Label* Book_Label::internal_default_instance() {
  return &Book_Label_default_instance_.get();
}
// -------------------------------------------------------------------

// Book

// optional string title = 1;
inline bool Book::has_title() const {
  return (_has_bits_[0] & 0x00000001u) != 0;
}
inline void Book::set_has_title() {
  _has_bits_[0] |= 0x00000001u;
}
inline void Book::clear_has_title() {
  _has_bits_[0] &= ~0x00000001u;
}
inline void Book::clear_title() {
  title_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  clear_has_title();
}
inline const ::std::string& Book::title() const {
  // @@protoc_insertion_point(field_get:google.protobuf.testing.Book.title)
  return title_.GetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void Book::set_title(const ::std::string& value) {
  set_has_title();
  title_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:google.protobuf.testing.Book.title)
}
inline void Book::set_title(const char* value) {
  set_has_title();
  title_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:google.protobuf.testing.Book.title)
}
inline void Book::set_title(const char* value, size_t size) {
  set_has_title();
  title_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:google.protobuf.testing.Book.title)
}
inline ::std::string* Book::mutable_title() {
  set_has_title();
  // @@protoc_insertion_point(field_mutable:google.protobuf.testing.Book.title)
  return title_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline ::std::string* Book::release_title() {
  // @@protoc_insertion_point(field_release:google.protobuf.testing.Book.title)
  clear_has_title();
  return title_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void Book::set_allocated_title(::std::string* title) {
  if (title != NULL) {
    set_has_title();
  } else {
    clear_has_title();
  }
  title_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), title);
  // @@protoc_insertion_point(field_set_allocated:google.protobuf.testing.Book.title)
}

// optional .google.protobuf.testing.Author author = 2;
inline bool Book::has_author() const {
  return (_has_bits_[0] & 0x00000002u) != 0;
}
inline void Book::set_has_author() {
  _has_bits_[0] |= 0x00000002u;
}
inline void Book::clear_has_author() {
  _has_bits_[0] &= ~0x00000002u;
}
inline void Book::clear_author() {
  if (author_ != NULL) author_->::google::protobuf::testing::Author::Clear();
  clear_has_author();
}
inline const ::google::protobuf::testing::Author& Book::author() const {
  // @@protoc_insertion_point(field_get:google.protobuf.testing.Book.author)
  return author_ != NULL ? *author_
                         : *::google::protobuf::testing::Author::internal_default_instance();
}
inline ::google::protobuf::testing::Author* Book::mutable_author() {
  set_has_author();
  if (author_ == NULL) {
    author_ = new ::google::protobuf::testing::Author;
  }
  // @@protoc_insertion_point(field_mutable:google.protobuf.testing.Book.author)
  return author_;
}
inline ::google::protobuf::testing::Author* Book::release_author() {
  // @@protoc_insertion_point(field_release:google.protobuf.testing.Book.author)
  clear_has_author();
  ::google::protobuf::testing::Author* temp = author_;
  author_ = NULL;
  return temp;
}
inline void Book::set_allocated_author(::google::protobuf::testing::Author* author) {
  delete author_;
  author_ = author;
  if (author) {
    set_has_author();
  } else {
    clear_has_author();
  }
  // @@protoc_insertion_point(field_set_allocated:google.protobuf.testing.Book.author)
}

// optional uint32 length = 3;
inline bool Book::has_length() const {
  return (_has_bits_[0] & 0x00000004u) != 0;
}
inline void Book::set_has_length() {
  _has_bits_[0] |= 0x00000004u;
}
inline void Book::clear_has_length() {
  _has_bits_[0] &= ~0x00000004u;
}
inline void Book::clear_length() {
  length_ = 0u;
  clear_has_length();
}
inline ::google::protobuf::uint32 Book::length() const {
  // @@protoc_insertion_point(field_get:google.protobuf.testing.Book.length)
  return length_;
}
inline void Book::set_length(::google::protobuf::uint32 value) {
  set_has_length();
  length_ = value;
  // @@protoc_insertion_point(field_set:google.protobuf.testing.Book.length)
}

// optional int64 published = 4;
inline bool Book::has_published() const {
  return (_has_bits_[0] & 0x00000008u) != 0;
}
inline void Book::set_has_published() {
  _has_bits_[0] |= 0x00000008u;
}
inline void Book::clear_has_published() {
  _has_bits_[0] &= ~0x00000008u;
}
inline void Book::clear_published() {
  published_ = GOOGLE_LONGLONG(0);
  clear_has_published();
}
inline ::google::protobuf::int64 Book::published() const {
  // @@protoc_insertion_point(field_get:google.protobuf.testing.Book.published)
  return published_;
}
inline void Book::set_published(::google::protobuf::int64 value) {
  set_has_published();
  published_ = value;
  // @@protoc_insertion_point(field_set:google.protobuf.testing.Book.published)
}

// optional bytes content = 5;
inline bool Book::has_content() const {
  return (_has_bits_[0] & 0x00000010u) != 0;
}
inline void Book::set_has_content() {
  _has_bits_[0] |= 0x00000010u;
}
inline void Book::clear_has_content() {
  _has_bits_[0] &= ~0x00000010u;
}
inline void Book::clear_content() {
  content_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  clear_has_content();
}
inline const ::std::string& Book::content() const {
  // @@protoc_insertion_point(field_get:google.protobuf.testing.Book.content)
  return content_.GetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void Book::set_content(const ::std::string& value) {
  set_has_content();
  content_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:google.protobuf.testing.Book.content)
}
inline void Book::set_content(const char* value) {
  set_has_content();
  content_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:google.protobuf.testing.Book.content)
}
inline void Book::set_content(const void* value, size_t size) {
  set_has_content();
  content_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:google.protobuf.testing.Book.content)
}
inline ::std::string* Book::mutable_content() {
  set_has_content();
  // @@protoc_insertion_point(field_mutable:google.protobuf.testing.Book.content)
  return content_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline ::std::string* Book::release_content() {
  // @@protoc_insertion_point(field_release:google.protobuf.testing.Book.content)
  clear_has_content();
  return content_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void Book::set_allocated_content(::std::string* content) {
  if (content != NULL) {
    set_has_content();
  } else {
    clear_has_content();
  }
  content_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), content);
  // @@protoc_insertion_point(field_set_allocated:google.protobuf.testing.Book.content)
}

// optional group Data = 6 { ... };
inline bool Book::has_data() const {
  return (_has_bits_[0] & 0x00000020u) != 0;
}
inline void Book::set_has_data() {
  _has_bits_[0] |= 0x00000020u;
}
inline void Book::clear_has_data() {
  _has_bits_[0] &= ~0x00000020u;
}
inline void Book::clear_data() {
  if (data_ != NULL) data_->::google::protobuf::testing::Book_Data::Clear();
  clear_has_data();
}
inline const ::google::protobuf::testing::Book_Data& Book::data() const {
  // @@protoc_insertion_point(field_get:google.protobuf.testing.Book.data)
  return data_ != NULL ? *data_
                         : *::google::protobuf::testing::Book_Data::internal_default_instance();
}
inline ::google::protobuf::testing::Book_Data* Book::mutable_data() {
  set_has_data();
  if (data_ == NULL) {
    data_ = new ::google::protobuf::testing::Book_Data;
  }
  // @@protoc_insertion_point(field_mutable:google.protobuf.testing.Book.data)
  return data_;
}
inline ::google::protobuf::testing::Book_Data* Book::release_data() {
  // @@protoc_insertion_point(field_release:google.protobuf.testing.Book.data)
  clear_has_data();
  ::google::protobuf::testing::Book_Data* temp = data_;
  data_ = NULL;
  return temp;
}
inline void Book::set_allocated_data(::google::protobuf::testing::Book_Data* data) {
  delete data_;
  data_ = data;
  if (data) {
    set_has_data();
  } else {
    clear_has_data();
  }
  // @@protoc_insertion_point(field_set_allocated:google.protobuf.testing.Book.data)
}

// optional .google.protobuf.testing.Publisher publisher = 9;
inline bool Book::has_publisher() const {
  return (_has_bits_[0] & 0x00000040u) != 0;
}
inline void Book::set_has_publisher() {
  _has_bits_[0] |= 0x00000040u;
}
inline void Book::clear_has_publisher() {
  _has_bits_[0] &= ~0x00000040u;
}
inline void Book::clear_publisher() {
  if (publisher_ != NULL) publisher_->::google::protobuf::testing::Publisher::Clear();
  clear_has_publisher();
}
inline const ::google::protobuf::testing::Publisher& Book::publisher() const {
  // @@protoc_insertion_point(field_get:google.protobuf.testing.Book.publisher)
  return publisher_ != NULL ? *publisher_
                         : *::google::protobuf::testing::Publisher::internal_default_instance();
}
inline ::google::protobuf::testing::Publisher* Book::mutable_publisher() {
  set_has_publisher();
  if (publisher_ == NULL) {
    publisher_ = new ::google::protobuf::testing::Publisher;
  }
  // @@protoc_insertion_point(field_mutable:google.protobuf.testing.Book.publisher)
  return publisher_;
}
inline ::google::protobuf::testing::Publisher* Book::release_publisher() {
  // @@protoc_insertion_point(field_release:google.protobuf.testing.Book.publisher)
  clear_has_publisher();
  ::google::protobuf::testing::Publisher* temp = publisher_;
  publisher_ = NULL;
  return temp;
}
inline void Book::set_allocated_publisher(::google::protobuf::testing::Publisher* publisher) {
  delete publisher_;
  publisher_ = publisher;
  if (publisher) {
    set_has_publisher();
  } else {
    clear_has_publisher();
  }
  // @@protoc_insertion_point(field_set_allocated:google.protobuf.testing.Book.publisher)
}

// repeated .google.protobuf.testing.Book.Label labels = 10;
inline int Book::labels_size() const {
  return labels_.size();
}
inline void Book::clear_labels() {
  labels_.Clear();
}
inline const ::google::protobuf::testing::Book_Label& Book::labels(int index) const {
  // @@protoc_insertion_point(field_get:google.protobuf.testing.Book.labels)
  return labels_.Get(index);
}
inline ::google::protobuf::testing::Book_Label* Book::mutable_labels(int index) {
  // @@protoc_insertion_point(field_mutable:google.protobuf.testing.Book.labels)
  return labels_.Mutable(index);
}
inline ::google::protobuf::testing::Book_Label* Book::add_labels() {
  // @@protoc_insertion_point(field_add:google.protobuf.testing.Book.labels)
  return labels_.Add();
}
inline ::google::protobuf::RepeatedPtrField< ::google::protobuf::testing::Book_Label >*
Book::mutable_labels() {
  // @@protoc_insertion_point(field_mutable_list:google.protobuf.testing.Book.labels)
  return &labels_;
}
inline const ::google::protobuf::RepeatedPtrField< ::google::protobuf::testing::Book_Label >&
Book::labels() const {
  // @@protoc_insertion_point(field_list:google.protobuf.testing.Book.labels)
  return labels_;
}

// optional .google.protobuf.testing.Book.Type type = 11;
inline bool Book::has_type() const {
  return (_has_bits_[0] & 0x00000100u) != 0;
}
inline void Book::set_has_type() {
  _has_bits_[0] |= 0x00000100u;
}
inline void Book::clear_has_type() {
  _has_bits_[0] &= ~0x00000100u;
}
inline void Book::clear_type() {
  type_ = 1;
  clear_has_type();
}
inline ::google::protobuf::testing::Book_Type Book::type() const {
  // @@protoc_insertion_point(field_get:google.protobuf.testing.Book.type)
  return static_cast< ::google::protobuf::testing::Book_Type >(type_);
}
inline void Book::set_type(::google::protobuf::testing::Book_Type value) {
  assert(::google::protobuf::testing::Book_Type_IsValid(value));
  set_has_type();
  type_ = value;
  // @@protoc_insertion_point(field_set:google.protobuf.testing.Book.type)
}

inline const Book* Book::internal_default_instance() {
  return &Book_default_instance_.get();
}
// -------------------------------------------------------------------

// Publisher

// required string name = 1;
inline bool Publisher::has_name() const {
  return (_has_bits_[0] & 0x00000001u) != 0;
}
inline void Publisher::set_has_name() {
  _has_bits_[0] |= 0x00000001u;
}
inline void Publisher::clear_has_name() {
  _has_bits_[0] &= ~0x00000001u;
}
inline void Publisher::clear_name() {
  name_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  clear_has_name();
}
inline const ::std::string& Publisher::name() const {
  // @@protoc_insertion_point(field_get:google.protobuf.testing.Publisher.name)
  return name_.GetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void Publisher::set_name(const ::std::string& value) {
  set_has_name();
  name_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:google.protobuf.testing.Publisher.name)
}
inline void Publisher::set_name(const char* value) {
  set_has_name();
  name_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:google.protobuf.testing.Publisher.name)
}
inline void Publisher::set_name(const char* value, size_t size) {
  set_has_name();
  name_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:google.protobuf.testing.Publisher.name)
}
inline ::std::string* Publisher::mutable_name() {
  set_has_name();
  // @@protoc_insertion_point(field_mutable:google.protobuf.testing.Publisher.name)
  return name_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline ::std::string* Publisher::release_name() {
  // @@protoc_insertion_point(field_release:google.protobuf.testing.Publisher.name)
  clear_has_name();
  return name_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void Publisher::set_allocated_name(::std::string* name) {
  if (name != NULL) {
    set_has_name();
  } else {
    clear_has_name();
  }
  name_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), name);
  // @@protoc_insertion_point(field_set_allocated:google.protobuf.testing.Publisher.name)
}

inline const Publisher* Publisher::internal_default_instance() {
  return &Publisher_default_instance_.get();
}
// -------------------------------------------------------------------

// Author

// optional uint64 id = 1[json_name = "@id"];
inline bool Author::has_id() const {
  return (_has_bits_[0] & 0x00000001u) != 0;
}
inline void Author::set_has_id() {
  _has_bits_[0] |= 0x00000001u;
}
inline void Author::clear_has_id() {
  _has_bits_[0] &= ~0x00000001u;
}
inline void Author::clear_id() {
  id_ = GOOGLE_ULONGLONG(0);
  clear_has_id();
}
inline ::google::protobuf::uint64 Author::id() const {
  // @@protoc_insertion_point(field_get:google.protobuf.testing.Author.id)
  return id_;
}
inline void Author::set_id(::google::protobuf::uint64 value) {
  set_has_id();
  id_ = value;
  // @@protoc_insertion_point(field_set:google.protobuf.testing.Author.id)
}

// optional string name = 2;
inline bool Author::has_name() const {
  return (_has_bits_[0] & 0x00000002u) != 0;
}
inline void Author::set_has_name() {
  _has_bits_[0] |= 0x00000002u;
}
inline void Author::clear_has_name() {
  _has_bits_[0] &= ~0x00000002u;
}
inline void Author::clear_name() {
  name_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  clear_has_name();
}
inline const ::std::string& Author::name() const {
  // @@protoc_insertion_point(field_get:google.protobuf.testing.Author.name)
  return name_.GetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void Author::set_name(const ::std::string& value) {
  set_has_name();
  name_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:google.protobuf.testing.Author.name)
}
inline void Author::set_name(const char* value) {
  set_has_name();
  name_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:google.protobuf.testing.Author.name)
}
inline void Author::set_name(const char* value, size_t size) {
  set_has_name();
  name_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:google.protobuf.testing.Author.name)
}
inline ::std::string* Author::mutable_name() {
  set_has_name();
  // @@protoc_insertion_point(field_mutable:google.protobuf.testing.Author.name)
  return name_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline ::std::string* Author::release_name() {
  // @@protoc_insertion_point(field_release:google.protobuf.testing.Author.name)
  clear_has_name();
  return name_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void Author::set_allocated_name(::std::string* name) {
  if (name != NULL) {
    set_has_name();
  } else {
    clear_has_name();
  }
  name_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), name);
  // @@protoc_insertion_point(field_set_allocated:google.protobuf.testing.Author.name)
}

// repeated string pseudonym = 3;
inline int Author::pseudonym_size() const {
  return pseudonym_.size();
}
inline void Author::clear_pseudonym() {
  pseudonym_.Clear();
}
inline const ::std::string& Author::pseudonym(int index) const {
  // @@protoc_insertion_point(field_get:google.protobuf.testing.Author.pseudonym)
  return pseudonym_.Get(index);
}
inline ::std::string* Author::mutable_pseudonym(int index) {
  // @@protoc_insertion_point(field_mutable:google.protobuf.testing.Author.pseudonym)
  return pseudonym_.Mutable(index);
}
inline void Author::set_pseudonym(int index, const ::std::string& value) {
  // @@protoc_insertion_point(field_set:google.protobuf.testing.Author.pseudonym)
  pseudonym_.Mutable(index)->assign(value);
}
inline void Author::set_pseudonym(int index, const char* value) {
  pseudonym_.Mutable(index)->assign(value);
  // @@protoc_insertion_point(field_set_char:google.protobuf.testing.Author.pseudonym)
}
inline void Author::set_pseudonym(int index, const char* value, size_t size) {
  pseudonym_.Mutable(index)->assign(
    reinterpret_cast<const char*>(value), size);
  // @@protoc_insertion_point(field_set_pointer:google.protobuf.testing.Author.pseudonym)
}
inline ::std::string* Author::add_pseudonym() {
  // @@protoc_insertion_point(field_add_mutable:google.protobuf.testing.Author.pseudonym)
  return pseudonym_.Add();
}
inline void Author::add_pseudonym(const ::std::string& value) {
  pseudonym_.Add()->assign(value);
  // @@protoc_insertion_point(field_add:google.protobuf.testing.Author.pseudonym)
}
inline void Author::add_pseudonym(const char* value) {
  pseudonym_.Add()->assign(value);
  // @@protoc_insertion_point(field_add_char:google.protobuf.testing.Author.pseudonym)
}
inline void Author::add_pseudonym(const char* value, size_t size) {
  pseudonym_.Add()->assign(reinterpret_cast<const char*>(value), size);
  // @@protoc_insertion_point(field_add_pointer:google.protobuf.testing.Author.pseudonym)
}
inline const ::google::protobuf::RepeatedPtrField< ::std::string>&
Author::pseudonym() const {
  // @@protoc_insertion_point(field_list:google.protobuf.testing.Author.pseudonym)
  return pseudonym_;
}
inline ::google::protobuf::RepeatedPtrField< ::std::string>*
Author::mutable_pseudonym() {
  // @@protoc_insertion_point(field_mutable_list:google.protobuf.testing.Author.pseudonym)
  return &pseudonym_;
}

// optional bool alive = 4;
inline bool Author::has_alive() const {
  return (_has_bits_[0] & 0x00000008u) != 0;
}
inline void Author::set_has_alive() {
  _has_bits_[0] |= 0x00000008u;
}
inline void Author::clear_has_alive() {
  _has_bits_[0] &= ~0x00000008u;
}
inline void Author::clear_alive() {
  alive_ = false;
  clear_has_alive();
}
inline bool Author::alive() const {
  // @@protoc_insertion_point(field_get:google.protobuf.testing.Author.alive)
  return alive_;
}
inline void Author::set_alive(bool value) {
  set_has_alive();
  alive_ = value;
  // @@protoc_insertion_point(field_set:google.protobuf.testing.Author.alive)
}

// repeated .google.protobuf.testing.Author friend = 5;
inline int Author::friend__size() const {
  return friend__.size();
}
inline void Author::clear_friend_() {
  friend__.Clear();
}
inline const ::google::protobuf::testing::Author& Author::friend_(int index) const {
  // @@protoc_insertion_point(field_get:google.protobuf.testing.Author.friend)
  return friend__.Get(index);
}
inline ::google::protobuf::testing::Author* Author::mutable_friend_(int index) {
  // @@protoc_insertion_point(field_mutable:google.protobuf.testing.Author.friend)
  return friend__.Mutable(index);
}
inline ::google::protobuf::testing::Author* Author::add_friend_() {
  // @@protoc_insertion_point(field_add:google.protobuf.testing.Author.friend)
  return friend__.Add();
}
inline ::google::protobuf::RepeatedPtrField< ::google::protobuf::testing::Author >*
Author::mutable_friend_() {
  // @@protoc_insertion_point(field_mutable_list:google.protobuf.testing.Author.friend)
  return &friend__;
}
inline const ::google::protobuf::RepeatedPtrField< ::google::protobuf::testing::Author >&
Author::friend_() const {
  // @@protoc_insertion_point(field_list:google.protobuf.testing.Author.friend)
  return friend__;
}

inline const Author* Author::internal_default_instance() {
  return &Author_default_instance_.get();
}
// -------------------------------------------------------------------

// BadAuthor

// optional string id = 1;
inline bool BadAuthor::has_id() const {
  return (_has_bits_[0] & 0x00000001u) != 0;
}
inline void BadAuthor::set_has_id() {
  _has_bits_[0] |= 0x00000001u;
}
inline void BadAuthor::clear_has_id() {
  _has_bits_[0] &= ~0x00000001u;
}
inline void BadAuthor::clear_id() {
  id_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  clear_has_id();
}
inline const ::std::string& BadAuthor::id() const {
  // @@protoc_insertion_point(field_get:google.protobuf.testing.BadAuthor.id)
  return id_.GetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void BadAuthor::set_id(const ::std::string& value) {
  set_has_id();
  id_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:google.protobuf.testing.BadAuthor.id)
}
inline void BadAuthor::set_id(const char* value) {
  set_has_id();
  id_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:google.protobuf.testing.BadAuthor.id)
}
inline void BadAuthor::set_id(const char* value, size_t size) {
  set_has_id();
  id_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:google.protobuf.testing.BadAuthor.id)
}
inline ::std::string* BadAuthor::mutable_id() {
  set_has_id();
  // @@protoc_insertion_point(field_mutable:google.protobuf.testing.BadAuthor.id)
  return id_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline ::std::string* BadAuthor::release_id() {
  // @@protoc_insertion_point(field_release:google.protobuf.testing.BadAuthor.id)
  clear_has_id();
  return id_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void BadAuthor::set_allocated_id(::std::string* id) {
  if (id != NULL) {
    set_has_id();
  } else {
    clear_has_id();
  }
  id_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), id);
  // @@protoc_insertion_point(field_set_allocated:google.protobuf.testing.BadAuthor.id)
}

// repeated uint64 name = 2;
inline int BadAuthor::name_size() const {
  return name_.size();
}
inline void BadAuthor::clear_name() {
  name_.Clear();
}
inline ::google::protobuf::uint64 BadAuthor::name(int index) const {
  // @@protoc_insertion_point(field_get:google.protobuf.testing.BadAuthor.name)
  return name_.Get(index);
}
inline void BadAuthor::set_name(int index, ::google::protobuf::uint64 value) {
  name_.Set(index, value);
  // @@protoc_insertion_point(field_set:google.protobuf.testing.BadAuthor.name)
}
inline void BadAuthor::add_name(::google::protobuf::uint64 value) {
  name_.Add(value);
  // @@protoc_insertion_point(field_add:google.protobuf.testing.BadAuthor.name)
}
inline const ::google::protobuf::RepeatedField< ::google::protobuf::uint64 >&
BadAuthor::name() const {
  // @@protoc_insertion_point(field_list:google.protobuf.testing.BadAuthor.name)
  return name_;
}
inline ::google::protobuf::RepeatedField< ::google::protobuf::uint64 >*
BadAuthor::mutable_name() {
  // @@protoc_insertion_point(field_mutable_list:google.protobuf.testing.BadAuthor.name)
  return &name_;
}

// optional string pseudonym = 3;
inline bool BadAuthor::has_pseudonym() const {
  return (_has_bits_[0] & 0x00000004u) != 0;
}
inline void BadAuthor::set_has_pseudonym() {
  _has_bits_[0] |= 0x00000004u;
}
inline void BadAuthor::clear_has_pseudonym() {
  _has_bits_[0] &= ~0x00000004u;
}
inline void BadAuthor::clear_pseudonym() {
  pseudonym_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  clear_has_pseudonym();
}
inline const ::std::string& BadAuthor::pseudonym() const {
  // @@protoc_insertion_point(field_get:google.protobuf.testing.BadAuthor.pseudonym)
  return pseudonym_.GetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void BadAuthor::set_pseudonym(const ::std::string& value) {
  set_has_pseudonym();
  pseudonym_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:google.protobuf.testing.BadAuthor.pseudonym)
}
inline void BadAuthor::set_pseudonym(const char* value) {
  set_has_pseudonym();
  pseudonym_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:google.protobuf.testing.BadAuthor.pseudonym)
}
inline void BadAuthor::set_pseudonym(const char* value, size_t size) {
  set_has_pseudonym();
  pseudonym_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:google.protobuf.testing.BadAuthor.pseudonym)
}
inline ::std::string* BadAuthor::mutable_pseudonym() {
  set_has_pseudonym();
  // @@protoc_insertion_point(field_mutable:google.protobuf.testing.BadAuthor.pseudonym)
  return pseudonym_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline ::std::string* BadAuthor::release_pseudonym() {
  // @@protoc_insertion_point(field_release:google.protobuf.testing.BadAuthor.pseudonym)
  clear_has_pseudonym();
  return pseudonym_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void BadAuthor::set_allocated_pseudonym(::std::string* pseudonym) {
  if (pseudonym != NULL) {
    set_has_pseudonym();
  } else {
    clear_has_pseudonym();
  }
  pseudonym_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), pseudonym);
  // @@protoc_insertion_point(field_set_allocated:google.protobuf.testing.BadAuthor.pseudonym)
}

// repeated bool alive = 4 [packed = true];
inline int BadAuthor::alive_size() const {
  return alive_.size();
}
inline void BadAuthor::clear_alive() {
  alive_.Clear();
}
inline bool BadAuthor::alive(int index) const {
  // @@protoc_insertion_point(field_get:google.protobuf.testing.BadAuthor.alive)
  return alive_.Get(index);
}
inline void BadAuthor::set_alive(int index, bool value) {
  alive_.Set(index, value);
  // @@protoc_insertion_point(field_set:google.protobuf.testing.BadAuthor.alive)
}
inline void BadAuthor::add_alive(bool value) {
  alive_.Add(value);
  // @@protoc_insertion_point(field_add:google.protobuf.testing.BadAuthor.alive)
}
inline const ::google::protobuf::RepeatedField< bool >&
BadAuthor::alive() const {
  // @@protoc_insertion_point(field_list:google.protobuf.testing.BadAuthor.alive)
  return alive_;
}
inline ::google::protobuf::RepeatedField< bool >*
BadAuthor::mutable_alive() {
  // @@protoc_insertion_point(field_mutable_list:google.protobuf.testing.BadAuthor.alive)
  return &alive_;
}

inline const BadAuthor* BadAuthor::internal_default_instance() {
  return &BadAuthor_default_instance_.get();
}
// -------------------------------------------------------------------

// Primitive

// optional fixed32 fix32 = 1;
inline bool Primitive::has_fix32() const {
  return (_has_bits_[0] & 0x00000001u) != 0;
}
inline void Primitive::set_has_fix32() {
  _has_bits_[0] |= 0x00000001u;
}
inline void Primitive::clear_has_fix32() {
  _has_bits_[0] &= ~0x00000001u;
}
inline void Primitive::clear_fix32() {
  fix32_ = 0u;
  clear_has_fix32();
}
inline ::google::protobuf::uint32 Primitive::fix32() const {
  // @@protoc_insertion_point(field_get:google.protobuf.testing.Primitive.fix32)
  return fix32_;
}
inline void Primitive::set_fix32(::google::protobuf::uint32 value) {
  set_has_fix32();
  fix32_ = value;
  // @@protoc_insertion_point(field_set:google.protobuf.testing.Primitive.fix32)
}

// optional uint32 u32 = 2;
inline bool Primitive::has_u32() const {
  return (_has_bits_[0] & 0x00000002u) != 0;
}
inline void Primitive::set_has_u32() {
  _has_bits_[0] |= 0x00000002u;
}
inline void Primitive::clear_has_u32() {
  _has_bits_[0] &= ~0x00000002u;
}
inline void Primitive::clear_u32() {
  u32_ = 0u;
  clear_has_u32();
}
inline ::google::protobuf::uint32 Primitive::u32() const {
  // @@protoc_insertion_point(field_get:google.protobuf.testing.Primitive.u32)
  return u32_;
}
inline void Primitive::set_u32(::google::protobuf::uint32 value) {
  set_has_u32();
  u32_ = value;
  // @@protoc_insertion_point(field_set:google.protobuf.testing.Primitive.u32)
}

// optional int32 i32 = 3;
inline bool Primitive::has_i32() const {
  return (_has_bits_[0] & 0x00000004u) != 0;
}
inline void Primitive::set_has_i32() {
  _has_bits_[0] |= 0x00000004u;
}
inline void Primitive::clear_has_i32() {
  _has_bits_[0] &= ~0x00000004u;
}
inline void Primitive::clear_i32() {
  i32_ = 0;
  clear_has_i32();
}
inline ::google::protobuf::int32 Primitive::i32() const {
  // @@protoc_insertion_point(field_get:google.protobuf.testing.Primitive.i32)
  return i32_;
}
inline void Primitive::set_i32(::google::protobuf::int32 value) {
  set_has_i32();
  i32_ = value;
  // @@protoc_insertion_point(field_set:google.protobuf.testing.Primitive.i32)
}

// optional sfixed32 sf32 = 4;
inline bool Primitive::has_sf32() const {
  return (_has_bits_[0] & 0x00000008u) != 0;
}
inline void Primitive::set_has_sf32() {
  _has_bits_[0] |= 0x00000008u;
}
inline void Primitive::clear_has_sf32() {
  _has_bits_[0] &= ~0x00000008u;
}
inline void Primitive::clear_sf32() {
  sf32_ = 0;
  clear_has_sf32();
}
inline ::google::protobuf::int32 Primitive::sf32() const {
  // @@protoc_insertion_point(field_get:google.protobuf.testing.Primitive.sf32)
  return sf32_;
}
inline void Primitive::set_sf32(::google::protobuf::int32 value) {
  set_has_sf32();
  sf32_ = value;
  // @@protoc_insertion_point(field_set:google.protobuf.testing.Primitive.sf32)
}

// optional sint32 s32 = 5;
inline bool Primitive::has_s32() const {
  return (_has_bits_[0] & 0x00000010u) != 0;
}
inline void Primitive::set_has_s32() {
  _has_bits_[0] |= 0x00000010u;
}
inline void Primitive::clear_has_s32() {
  _has_bits_[0] &= ~0x00000010u;
}
inline void Primitive::clear_s32() {
  s32_ = 0;
  clear_has_s32();
}
inline ::google::protobuf::int32 Primitive::s32() const {
  // @@protoc_insertion_point(field_get:google.protobuf.testing.Primitive.s32)
  return s32_;
}
inline void Primitive::set_s32(::google::protobuf::int32 value) {
  set_has_s32();
  s32_ = value;
  // @@protoc_insertion_point(field_set:google.protobuf.testing.Primitive.s32)
}

// optional fixed64 fix64 = 6;
inline bool Primitive::has_fix64() const {
  return (_has_bits_[0] & 0x00000020u) != 0;
}
inline void Primitive::set_has_fix64() {
  _has_bits_[0] |= 0x00000020u;
}
inline void Primitive::clear_has_fix64() {
  _has_bits_[0] &= ~0x00000020u;
}
inline void Primitive::clear_fix64() {
  fix64_ = GOOGLE_ULONGLONG(0);
  clear_has_fix64();
}
inline ::google::protobuf::uint64 Primitive::fix64() const {
  // @@protoc_insertion_point(field_get:google.protobuf.testing.Primitive.fix64)
  return fix64_;
}
inline void Primitive::set_fix64(::google::protobuf::uint64 value) {
  set_has_fix64();
  fix64_ = value;
  // @@protoc_insertion_point(field_set:google.protobuf.testing.Primitive.fix64)
}

// optional uint64 u64 = 7;
inline bool Primitive::has_u64() const {
  return (_has_bits_[0] & 0x00000040u) != 0;
}
inline void Primitive::set_has_u64() {
  _has_bits_[0] |= 0x00000040u;
}
inline void Primitive::clear_has_u64() {
  _has_bits_[0] &= ~0x00000040u;
}
inline void Primitive::clear_u64() {
  u64_ = GOOGLE_ULONGLONG(0);
  clear_has_u64();
}
inline ::google::protobuf::uint64 Primitive::u64() const {
  // @@protoc_insertion_point(field_get:google.protobuf.testing.Primitive.u64)
  return u64_;
}
inline void Primitive::set_u64(::google::protobuf::uint64 value) {
  set_has_u64();
  u64_ = value;
  // @@protoc_insertion_point(field_set:google.protobuf.testing.Primitive.u64)
}

// optional int64 i64 = 8;
inline bool Primitive::has_i64() const {
  return (_has_bits_[0] & 0x00000080u) != 0;
}
inline void Primitive::set_has_i64() {
  _has_bits_[0] |= 0x00000080u;
}
inline void Primitive::clear_has_i64() {
  _has_bits_[0] &= ~0x00000080u;
}
inline void Primitive::clear_i64() {
  i64_ = GOOGLE_LONGLONG(0);
  clear_has_i64();
}
inline ::google::protobuf::int64 Primitive::i64() const {
  // @@protoc_insertion_point(field_get:google.protobuf.testing.Primitive.i64)
  return i64_;
}
inline void Primitive::set_i64(::google::protobuf::int64 value) {
  set_has_i64();
  i64_ = value;
  // @@protoc_insertion_point(field_set:google.protobuf.testing.Primitive.i64)
}

// optional sfixed64 sf64 = 9;
inline bool Primitive::has_sf64() const {
  return (_has_bits_[0] & 0x00000100u) != 0;
}
inline void Primitive::set_has_sf64() {
  _has_bits_[0] |= 0x00000100u;
}
inline void Primitive::clear_has_sf64() {
  _has_bits_[0] &= ~0x00000100u;
}
inline void Primitive::clear_sf64() {
  sf64_ = GOOGLE_LONGLONG(0);
  clear_has_sf64();
}
inline ::google::protobuf::int64 Primitive::sf64() const {
  // @@protoc_insertion_point(field_get:google.protobuf.testing.Primitive.sf64)
  return sf64_;
}
inline void Primitive::set_sf64(::google::protobuf::int64 value) {
  set_has_sf64();
  sf64_ = value;
  // @@protoc_insertion_point(field_set:google.protobuf.testing.Primitive.sf64)
}

// optional sint64 s64 = 10;
inline bool Primitive::has_s64() const {
  return (_has_bits_[0] & 0x00000200u) != 0;
}
inline void Primitive::set_has_s64() {
  _has_bits_[0] |= 0x00000200u;
}
inline void Primitive::clear_has_s64() {
  _has_bits_[0] &= ~0x00000200u;
}
inline void Primitive::clear_s64() {
  s64_ = GOOGLE_LONGLONG(0);
  clear_has_s64();
}
inline ::google::protobuf::int64 Primitive::s64() const {
  // @@protoc_insertion_point(field_get:google.protobuf.testing.Primitive.s64)
  return s64_;
}
inline void Primitive::set_s64(::google::protobuf::int64 value) {
  set_has_s64();
  s64_ = value;
  // @@protoc_insertion_point(field_set:google.protobuf.testing.Primitive.s64)
}

// optional string str = 11;
inline bool Primitive::has_str() const {
  return (_has_bits_[0] & 0x00000400u) != 0;
}
inline void Primitive::set_has_str() {
  _has_bits_[0] |= 0x00000400u;
}
inline void Primitive::clear_has_str() {
  _has_bits_[0] &= ~0x00000400u;
}
inline void Primitive::clear_str() {
  str_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  clear_has_str();
}
inline const ::std::string& Primitive::str() const {
  // @@protoc_insertion_point(field_get:google.protobuf.testing.Primitive.str)
  return str_.GetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void Primitive::set_str(const ::std::string& value) {
  set_has_str();
  str_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:google.protobuf.testing.Primitive.str)
}
inline void Primitive::set_str(const char* value) {
  set_has_str();
  str_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:google.protobuf.testing.Primitive.str)
}
inline void Primitive::set_str(const char* value, size_t size) {
  set_has_str();
  str_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:google.protobuf.testing.Primitive.str)
}
inline ::std::string* Primitive::mutable_str() {
  set_has_str();
  // @@protoc_insertion_point(field_mutable:google.protobuf.testing.Primitive.str)
  return str_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline ::std::string* Primitive::release_str() {
  // @@protoc_insertion_point(field_release:google.protobuf.testing.Primitive.str)
  clear_has_str();
  return str_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void Primitive::set_allocated_str(::std::string* str) {
  if (str != NULL) {
    set_has_str();
  } else {
    clear_has_str();
  }
  str_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), str);
  // @@protoc_insertion_point(field_set_allocated:google.protobuf.testing.Primitive.str)
}

// optional bytes bytes = 12;
inline bool Primitive::has_bytes() const {
  return (_has_bits_[0] & 0x00000800u) != 0;
}
inline void Primitive::set_has_bytes() {
  _has_bits_[0] |= 0x00000800u;
}
inline void Primitive::clear_has_bytes() {
  _has_bits_[0] &= ~0x00000800u;
}
inline void Primitive::clear_bytes() {
  bytes_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  clear_has_bytes();
}
inline const ::std::string& Primitive::bytes() const {
  // @@protoc_insertion_point(field_get:google.protobuf.testing.Primitive.bytes)
  return bytes_.GetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void Primitive::set_bytes(const ::std::string& value) {
  set_has_bytes();
  bytes_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:google.protobuf.testing.Primitive.bytes)
}
inline void Primitive::set_bytes(const char* value) {
  set_has_bytes();
  bytes_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:google.protobuf.testing.Primitive.bytes)
}
inline void Primitive::set_bytes(const void* value, size_t size) {
  set_has_bytes();
  bytes_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:google.protobuf.testing.Primitive.bytes)
}
inline ::std::string* Primitive::mutable_bytes() {
  set_has_bytes();
  // @@protoc_insertion_point(field_mutable:google.protobuf.testing.Primitive.bytes)
  return bytes_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline ::std::string* Primitive::release_bytes() {
  // @@protoc_insertion_point(field_release:google.protobuf.testing.Primitive.bytes)
  clear_has_bytes();
  return bytes_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void Primitive::set_allocated_bytes(::std::string* bytes) {
  if (bytes != NULL) {
    set_has_bytes();
  } else {
    clear_has_bytes();
  }
  bytes_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), bytes);
  // @@protoc_insertion_point(field_set_allocated:google.protobuf.testing.Primitive.bytes)
}

// optional float float = 13;
inline bool Primitive::has_float_() const {
  return (_has_bits_[0] & 0x00001000u) != 0;
}
inline void Primitive::set_has_float_() {
  _has_bits_[0] |= 0x00001000u;
}
inline void Primitive::clear_has_float_() {
  _has_bits_[0] &= ~0x00001000u;
}
inline void Primitive::clear_float_() {
  float__ = 0;
  clear_has_float_();
}
inline float Primitive::float_() const {
  // @@protoc_insertion_point(field_get:google.protobuf.testing.Primitive.float)
  return float__;
}
inline void Primitive::set_float_(float value) {
  set_has_float_();
  float__ = value;
  // @@protoc_insertion_point(field_set:google.protobuf.testing.Primitive.float)
}

// optional double double = 14;
inline bool Primitive::has_double_() const {
  return (_has_bits_[0] & 0x00002000u) != 0;
}
inline void Primitive::set_has_double_() {
  _has_bits_[0] |= 0x00002000u;
}
inline void Primitive::clear_has_double_() {
  _has_bits_[0] &= ~0x00002000u;
}
inline void Primitive::clear_double_() {
  double__ = 0;
  clear_has_double_();
}
inline double Primitive::double_() const {
  // @@protoc_insertion_point(field_get:google.protobuf.testing.Primitive.double)
  return double__;
}
inline void Primitive::set_double_(double value) {
  set_has_double_();
  double__ = value;
  // @@protoc_insertion_point(field_set:google.protobuf.testing.Primitive.double)
}

// optional bool bool = 15;
inline bool Primitive::has_bool_() const {
  return (_has_bits_[0] & 0x00004000u) != 0;
}
inline void Primitive::set_has_bool_() {
  _has_bits_[0] |= 0x00004000u;
}
inline void Primitive::clear_has_bool_() {
  _has_bits_[0] &= ~0x00004000u;
}
inline void Primitive::clear_bool_() {
  bool__ = false;
  clear_has_bool_();
}
inline bool Primitive::bool_() const {
  // @@protoc_insertion_point(field_get:google.protobuf.testing.Primitive.bool)
  return bool__;
}
inline void Primitive::set_bool_(bool value) {
  set_has_bool_();
  bool__ = value;
  // @@protoc_insertion_point(field_set:google.protobuf.testing.Primitive.bool)
}

// repeated fixed32 rep_fix32 = 16;
inline int Primitive::rep_fix32_size() const {
  return rep_fix32_.size();
}
inline void Primitive::clear_rep_fix32() {
  rep_fix32_.Clear();
}
inline ::google::protobuf::uint32 Primitive::rep_fix32(int index) const {
  // @@protoc_insertion_point(field_get:google.protobuf.testing.Primitive.rep_fix32)
  return rep_fix32_.Get(index);
}
inline void Primitive::set_rep_fix32(int index, ::google::protobuf::uint32 value) {
  rep_fix32_.Set(index, value);
  // @@protoc_insertion_point(field_set:google.protobuf.testing.Primitive.rep_fix32)
}
inline void Primitive::add_rep_fix32(::google::protobuf::uint32 value) {
  rep_fix32_.Add(value);
  // @@protoc_insertion_point(field_add:google.protobuf.testing.Primitive.rep_fix32)
}
inline const ::google::protobuf::RepeatedField< ::google::protobuf::uint32 >&
Primitive::rep_fix32() const {
  // @@protoc_insertion_point(field_list:google.protobuf.testing.Primitive.rep_fix32)
  return rep_fix32_;
}
inline ::google::protobuf::RepeatedField< ::google::protobuf::uint32 >*
Primitive::mutable_rep_fix32() {
  // @@protoc_insertion_point(field_mutable_list:google.protobuf.testing.Primitive.rep_fix32)
  return &rep_fix32_;
}

// repeated uint32 rep_u32 = 17;
inline int Primitive::rep_u32_size() const {
  return rep_u32_.size();
}
inline void Primitive::clear_rep_u32() {
  rep_u32_.Clear();
}
inline ::google::protobuf::uint32 Primitive::rep_u32(int index) const {
  // @@protoc_insertion_point(field_get:google.protobuf.testing.Primitive.rep_u32)
  return rep_u32_.Get(index);
}
inline void Primitive::set_rep_u32(int index, ::google::protobuf::uint32 value) {
  rep_u32_.Set(index, value);
  // @@protoc_insertion_point(field_set:google.protobuf.testing.Primitive.rep_u32)
}
inline void Primitive::add_rep_u32(::google::protobuf::uint32 value) {
  rep_u32_.Add(value);
  // @@protoc_insertion_point(field_add:google.protobuf.testing.Primitive.rep_u32)
}
inline const ::google::protobuf::RepeatedField< ::google::protobuf::uint32 >&
Primitive::rep_u32() const {
  // @@protoc_insertion_point(field_list:google.protobuf.testing.Primitive.rep_u32)
  return rep_u32_;
}
inline ::google::protobuf::RepeatedField< ::google::protobuf::uint32 >*
Primitive::mutable_rep_u32() {
  // @@protoc_insertion_point(field_mutable_list:google.protobuf.testing.Primitive.rep_u32)
  return &rep_u32_;
}

// repeated int32 rep_i32 = 18;
inline int Primitive::rep_i32_size() const {
  return rep_i32_.size();
}
inline void Primitive::clear_rep_i32() {
  rep_i32_.Clear();
}
inline ::google::protobuf::int32 Primitive::rep_i32(int index) const {
  // @@protoc_insertion_point(field_get:google.protobuf.testing.Primitive.rep_i32)
  return rep_i32_.Get(index);
}
inline void Primitive::set_rep_i32(int index, ::google::protobuf::int32 value) {
  rep_i32_.Set(index, value);
  // @@protoc_insertion_point(field_set:google.protobuf.testing.Primitive.rep_i32)
}
inline void Primitive::add_rep_i32(::google::protobuf::int32 value) {
  rep_i32_.Add(value);
  // @@protoc_insertion_point(field_add:google.protobuf.testing.Primitive.rep_i32)
}
inline const ::google::protobuf::RepeatedField< ::google::protobuf::int32 >&
Primitive::rep_i32() const {
  // @@protoc_insertion_point(field_list:google.protobuf.testing.Primitive.rep_i32)
  return rep_i32_;
}
inline ::google::protobuf::RepeatedField< ::google::protobuf::int32 >*
Primitive::mutable_rep_i32() {
  // @@protoc_insertion_point(field_mutable_list:google.protobuf.testing.Primitive.rep_i32)
  return &rep_i32_;
}

// repeated sfixed32 rep_sf32 = 19;
inline int Primitive::rep_sf32_size() const {
  return rep_sf32_.size();
}
inline void Primitive::clear_rep_sf32() {
  rep_sf32_.Clear();
}
inline ::google::protobuf::int32 Primitive::rep_sf32(int index) const {
  // @@protoc_insertion_point(field_get:google.protobuf.testing.Primitive.rep_sf32)
  return rep_sf32_.Get(index);
}
inline void Primitive::set_rep_sf32(int index, ::google::protobuf::int32 value) {
  rep_sf32_.Set(index, value);
  // @@protoc_insertion_point(field_set:google.protobuf.testing.Primitive.rep_sf32)
}
inline void Primitive::add_rep_sf32(::google::protobuf::int32 value) {
  rep_sf32_.Add(value);
  // @@protoc_insertion_point(field_add:google.protobuf.testing.Primitive.rep_sf32)
}
inline const ::google::protobuf::RepeatedField< ::google::protobuf::int32 >&
Primitive::rep_sf32() const {
  // @@protoc_insertion_point(field_list:google.protobuf.testing.Primitive.rep_sf32)
  return rep_sf32_;
}
inline ::google::protobuf::RepeatedField< ::google::protobuf::int32 >*
Primitive::mutable_rep_sf32() {
  // @@protoc_insertion_point(field_mutable_list:google.protobuf.testing.Primitive.rep_sf32)
  return &rep_sf32_;
}

// repeated sint32 rep_s32 = 20;
inline int Primitive::rep_s32_size() const {
  return rep_s32_.size();
}
inline void Primitive::clear_rep_s32() {
  rep_s32_.Clear();
}
inline ::google::protobuf::int32 Primitive::rep_s32(int index) const {
  // @@protoc_insertion_point(field_get:google.protobuf.testing.Primitive.rep_s32)
  return rep_s32_.Get(index);
}
inline void Primitive::set_rep_s32(int index, ::google::protobuf::int32 value) {
  rep_s32_.Set(index, value);
  // @@protoc_insertion_point(field_set:google.protobuf.testing.Primitive.rep_s32)
}
inline void Primitive::add_rep_s32(::google::protobuf::int32 value) {
  rep_s32_.Add(value);
  // @@protoc_insertion_point(field_add:google.protobuf.testing.Primitive.rep_s32)
}
inline const ::google::protobuf::RepeatedField< ::google::protobuf::int32 >&
Primitive::rep_s32() const {
  // @@protoc_insertion_point(field_list:google.protobuf.testing.Primitive.rep_s32)
  return rep_s32_;
}
inline ::google::protobuf::RepeatedField< ::google::protobuf::int32 >*
Primitive::mutable_rep_s32() {
  // @@protoc_insertion_point(field_mutable_list:google.protobuf.testing.Primitive.rep_s32)
  return &rep_s32_;
}

// repeated fixed64 rep_fix64 = 21;
inline int Primitive::rep_fix64_size() const {
  return rep_fix64_.size();
}
inline void Primitive::clear_rep_fix64() {
  rep_fix64_.Clear();
}
inline ::google::protobuf::uint64 Primitive::rep_fix64(int index) const {
  // @@protoc_insertion_point(field_get:google.protobuf.testing.Primitive.rep_fix64)
  return rep_fix64_.Get(index);
}
inline void Primitive::set_rep_fix64(int index, ::google::protobuf::uint64 value) {
  rep_fix64_.Set(index, value);
  // @@protoc_insertion_point(field_set:google.protobuf.testing.Primitive.rep_fix64)
}
inline void Primitive::add_rep_fix64(::google::protobuf::uint64 value) {
  rep_fix64_.Add(value);
  // @@protoc_insertion_point(field_add:google.protobuf.testing.Primitive.rep_fix64)
}
inline const ::google::protobuf::RepeatedField< ::google::protobuf::uint64 >&
Primitive::rep_fix64() const {
  // @@protoc_insertion_point(field_list:google.protobuf.testing.Primitive.rep_fix64)
  return rep_fix64_;
}
inline ::google::protobuf::RepeatedField< ::google::protobuf::uint64 >*
Primitive::mutable_rep_fix64() {
  // @@protoc_insertion_point(field_mutable_list:google.protobuf.testing.Primitive.rep_fix64)
  return &rep_fix64_;
}

// repeated uint64 rep_u64 = 22;
inline int Primitive::rep_u64_size() const {
  return rep_u64_.size();
}
inline void Primitive::clear_rep_u64() {
  rep_u64_.Clear();
}
inline ::google::protobuf::uint64 Primitive::rep_u64(int index) const {
  // @@protoc_insertion_point(field_get:google.protobuf.testing.Primitive.rep_u64)
  return rep_u64_.Get(index);
}
inline void Primitive::set_rep_u64(int index, ::google::protobuf::uint64 value) {
  rep_u64_.Set(index, value);
  // @@protoc_insertion_point(field_set:google.protobuf.testing.Primitive.rep_u64)
}
inline void Primitive::add_rep_u64(::google::protobuf::uint64 value) {
  rep_u64_.Add(value);
  // @@protoc_insertion_point(field_add:google.protobuf.testing.Primitive.rep_u64)
}
inline const ::google::protobuf::RepeatedField< ::google::protobuf::uint64 >&
Primitive::rep_u64() const {
  // @@protoc_insertion_point(field_list:google.protobuf.testing.Primitive.rep_u64)
  return rep_u64_;
}
inline ::google::protobuf::RepeatedField< ::google::protobuf::uint64 >*
Primitive::mutable_rep_u64() {
  // @@protoc_insertion_point(field_mutable_list:google.protobuf.testing.Primitive.rep_u64)
  return &rep_u64_;
}

// repeated int64 rep_i64 = 23;
inline int Primitive::rep_i64_size() const {
  return rep_i64_.size();
}
inline void Primitive::clear_rep_i64() {
  rep_i64_.Clear();
}
inline ::google::protobuf::int64 Primitive::rep_i64(int index) const {
  // @@protoc_insertion_point(field_get:google.protobuf.testing.Primitive.rep_i64)
  return rep_i64_.Get(index);
}
inline void Primitive::set_rep_i64(int index, ::google::protobuf::int64 value) {
  rep_i64_.Set(index, value);
  // @@protoc_insertion_point(field_set:google.protobuf.testing.Primitive.rep_i64)
}
inline void Primitive::add_rep_i64(::google::protobuf::int64 value) {
  rep_i64_.Add(value);
  // @@protoc_insertion_point(field_add:google.protobuf.testing.Primitive.rep_i64)
}
inline const ::google::protobuf::RepeatedField< ::google::protobuf::int64 >&
Primitive::rep_i64() const {
  // @@protoc_insertion_point(field_list:google.protobuf.testing.Primitive.rep_i64)
  return rep_i64_;
}
inline ::google::protobuf::RepeatedField< ::google::protobuf::int64 >*
Primitive::mutable_rep_i64() {
  // @@protoc_insertion_point(field_mutable_list:google.protobuf.testing.Primitive.rep_i64)
  return &rep_i64_;
}

// repeated sfixed64 rep_sf64 = 24;
inline int Primitive::rep_sf64_size() const {
  return rep_sf64_.size();
}
inline void Primitive::clear_rep_sf64() {
  rep_sf64_.Clear();
}
inline ::google::protobuf::int64 Primitive::rep_sf64(int index) const {
  // @@protoc_insertion_point(field_get:google.protobuf.testing.Primitive.rep_sf64)
  return rep_sf64_.Get(index);
}
inline void Primitive::set_rep_sf64(int index, ::google::protobuf::int64 value) {
  rep_sf64_.Set(index, value);
  // @@protoc_insertion_point(field_set:google.protobuf.testing.Primitive.rep_sf64)
}
inline void Primitive::add_rep_sf64(::google::protobuf::int64 value) {
  rep_sf64_.Add(value);
  // @@protoc_insertion_point(field_add:google.protobuf.testing.Primitive.rep_sf64)
}
inline const ::google::protobuf::RepeatedField< ::google::protobuf::int64 >&
Primitive::rep_sf64() const {
  // @@protoc_insertion_point(field_list:google.protobuf.testing.Primitive.rep_sf64)
  return rep_sf64_;
}
inline ::google::protobuf::RepeatedField< ::google::protobuf::int64 >*
Primitive::mutable_rep_sf64() {
  // @@protoc_insertion_point(field_mutable_list:google.protobuf.testing.Primitive.rep_sf64)
  return &rep_sf64_;
}

// repeated sint64 rep_s64 = 25;
inline int Primitive::rep_s64_size() const {
  return rep_s64_.size();
}
inline void Primitive::clear_rep_s64() {
  rep_s64_.Clear();
}
inline ::google::protobuf::int64 Primitive::rep_s64(int index) const {
  // @@protoc_insertion_point(field_get:google.protobuf.testing.Primitive.rep_s64)
  return rep_s64_.Get(index);
}
inline void Primitive::set_rep_s64(int index, ::google::protobuf::int64 value) {
  rep_s64_.Set(index, value);
  // @@protoc_insertion_point(field_set:google.protobuf.testing.Primitive.rep_s64)
}
inline void Primitive::add_rep_s64(::google::protobuf::int64 value) {
  rep_s64_.Add(value);
  // @@protoc_insertion_point(field_add:google.protobuf.testing.Primitive.rep_s64)
}
inline const ::google::protobuf::RepeatedField< ::google::protobuf::int64 >&
Primitive::rep_s64() const {
  // @@protoc_insertion_point(field_list:google.protobuf.testing.Primitive.rep_s64)
  return rep_s64_;
}
inline ::google::protobuf::RepeatedField< ::google::protobuf::int64 >*
Primitive::mutable_rep_s64() {
  // @@protoc_insertion_point(field_mutable_list:google.protobuf.testing.Primitive.rep_s64)
  return &rep_s64_;
}

// repeated string rep_str = 26;
inline int Primitive::rep_str_size() const {
  return rep_str_.size();
}
inline void Primitive::clear_rep_str() {
  rep_str_.Clear();
}
inline const ::std::string& Primitive::rep_str(int index) const {
  // @@protoc_insertion_point(field_get:google.protobuf.testing.Primitive.rep_str)
  return rep_str_.Get(index);
}
inline ::std::string* Primitive::mutable_rep_str(int index) {
  // @@protoc_insertion_point(field_mutable:google.protobuf.testing.Primitive.rep_str)
  return rep_str_.Mutable(index);
}
inline void Primitive::set_rep_str(int index, const ::std::string& value) {
  // @@protoc_insertion_point(field_set:google.protobuf.testing.Primitive.rep_str)
  rep_str_.Mutable(index)->assign(value);
}
inline void Primitive::set_rep_str(int index, const char* value) {
  rep_str_.Mutable(index)->assign(value);
  // @@protoc_insertion_point(field_set_char:google.protobuf.testing.Primitive.rep_str)
}
inline void Primitive::set_rep_str(int index, const char* value, size_t size) {
  rep_str_.Mutable(index)->assign(
    reinterpret_cast<const char*>(value), size);
  // @@protoc_insertion_point(field_set_pointer:google.protobuf.testing.Primitive.rep_str)
}
inline ::std::string* Primitive::add_rep_str() {
  // @@protoc_insertion_point(field_add_mutable:google.protobuf.testing.Primitive.rep_str)
  return rep_str_.Add();
}
inline void Primitive::add_rep_str(const ::std::string& value) {
  rep_str_.Add()->assign(value);
  // @@protoc_insertion_point(field_add:google.protobuf.testing.Primitive.rep_str)
}
inline void Primitive::add_rep_str(const char* value) {
  rep_str_.Add()->assign(value);
  // @@protoc_insertion_point(field_add_char:google.protobuf.testing.Primitive.rep_str)
}
inline void Primitive::add_rep_str(const char* value, size_t size) {
  rep_str_.Add()->assign(reinterpret_cast<const char*>(value), size);
  // @@protoc_insertion_point(field_add_pointer:google.protobuf.testing.Primitive.rep_str)
}
inline const ::google::protobuf::RepeatedPtrField< ::std::string>&
Primitive::rep_str() const {
  // @@protoc_insertion_point(field_list:google.protobuf.testing.Primitive.rep_str)
  return rep_str_;
}
inline ::google::protobuf::RepeatedPtrField< ::std::string>*
Primitive::mutable_rep_str() {
  // @@protoc_insertion_point(field_mutable_list:google.protobuf.testing.Primitive.rep_str)
  return &rep_str_;
}

// repeated bytes rep_bytes = 27;
inline int Primitive::rep_bytes_size() const {
  return rep_bytes_.size();
}
inline void Primitive::clear_rep_bytes() {
  rep_bytes_.Clear();
}
inline const ::std::string& Primitive::rep_bytes(int index) const {
  // @@protoc_insertion_point(field_get:google.protobuf.testing.Primitive.rep_bytes)
  return rep_bytes_.Get(index);
}
inline ::std::string* Primitive::mutable_rep_bytes(int index) {
  // @@protoc_insertion_point(field_mutable:google.protobuf.testing.Primitive.rep_bytes)
  return rep_bytes_.Mutable(index);
}
inline void Primitive::set_rep_bytes(int index, const ::std::string& value) {
  // @@protoc_insertion_point(field_set:google.protobuf.testing.Primitive.rep_bytes)
  rep_bytes_.Mutable(index)->assign(value);
}
inline void Primitive::set_rep_bytes(int index, const char* value) {
  rep_bytes_.Mutable(index)->assign(value);
  // @@protoc_insertion_point(field_set_char:google.protobuf.testing.Primitive.rep_bytes)
}
inline void Primitive::set_rep_bytes(int index, const void* value, size_t size) {
  rep_bytes_.Mutable(index)->assign(
    reinterpret_cast<const char*>(value), size);
  // @@protoc_insertion_point(field_set_pointer:google.protobuf.testing.Primitive.rep_bytes)
}
inline ::std::string* Primitive::add_rep_bytes() {
  // @@protoc_insertion_point(field_add_mutable:google.protobuf.testing.Primitive.rep_bytes)
  return rep_bytes_.Add();
}
inline void Primitive::add_rep_bytes(const ::std::string& value) {
  rep_bytes_.Add()->assign(value);
  // @@protoc_insertion_point(field_add:google.protobuf.testing.Primitive.rep_bytes)
}
inline void Primitive::add_rep_bytes(const char* value) {
  rep_bytes_.Add()->assign(value);
  // @@protoc_insertion_point(field_add_char:google.protobuf.testing.Primitive.rep_bytes)
}
inline void Primitive::add_rep_bytes(const void* value, size_t size) {
  rep_bytes_.Add()->assign(reinterpret_cast<const char*>(value), size);
  // @@protoc_insertion_point(field_add_pointer:google.protobuf.testing.Primitive.rep_bytes)
}
inline const ::google::protobuf::RepeatedPtrField< ::std::string>&
Primitive::rep_bytes() const {
  // @@protoc_insertion_point(field_list:google.protobuf.testing.Primitive.rep_bytes)
  return rep_bytes_;
}
inline ::google::protobuf::RepeatedPtrField< ::std::string>*
Primitive::mutable_rep_bytes() {
  // @@protoc_insertion_point(field_mutable_list:google.protobuf.testing.Primitive.rep_bytes)
  return &rep_bytes_;
}

// repeated float rep_float = 28;
inline int Primitive::rep_float_size() const {
  return rep_float_.size();
}
inline void Primitive::clear_rep_float() {
  rep_float_.Clear();
}
inline float Primitive::rep_float(int index) const {
  // @@protoc_insertion_point(field_get:google.protobuf.testing.Primitive.rep_float)
  return rep_float_.Get(index);
}
inline void Primitive::set_rep_float(int index, float value) {
  rep_float_.Set(index, value);
  // @@protoc_insertion_point(field_set:google.protobuf.testing.Primitive.rep_float)
}
inline void Primitive::add_rep_float(float value) {
  rep_float_.Add(value);
  // @@protoc_insertion_point(field_add:google.protobuf.testing.Primitive.rep_float)
}
inline const ::google::protobuf::RepeatedField< float >&
Primitive::rep_float() const {
  // @@protoc_insertion_point(field_list:google.protobuf.testing.Primitive.rep_float)
  return rep_float_;
}
inline ::google::protobuf::RepeatedField< float >*
Primitive::mutable_rep_float() {
  // @@protoc_insertion_point(field_mutable_list:google.protobuf.testing.Primitive.rep_float)
  return &rep_float_;
}

// repeated double rep_double = 29;
inline int Primitive::rep_double_size() const {
  return rep_double_.size();
}
inline void Primitive::clear_rep_double() {
  rep_double_.Clear();
}
inline double Primitive::rep_double(int index) const {
  // @@protoc_insertion_point(field_get:google.protobuf.testing.Primitive.rep_double)
  return rep_double_.Get(index);
}
inline void Primitive::set_rep_double(int index, double value) {
  rep_double_.Set(index, value);
  // @@protoc_insertion_point(field_set:google.protobuf.testing.Primitive.rep_double)
}
inline void Primitive::add_rep_double(double value) {
  rep_double_.Add(value);
  // @@protoc_insertion_point(field_add:google.protobuf.testing.Primitive.rep_double)
}
inline const ::google::protobuf::RepeatedField< double >&
Primitive::rep_double() const {
  // @@protoc_insertion_point(field_list:google.protobuf.testing.Primitive.rep_double)
  return rep_double_;
}
inline ::google::protobuf::RepeatedField< double >*
Primitive::mutable_rep_double() {
  // @@protoc_insertion_point(field_mutable_list:google.protobuf.testing.Primitive.rep_double)
  return &rep_double_;
}

// repeated bool rep_bool = 30;
inline int Primitive::rep_bool_size() const {
  return rep_bool_.size();
}
inline void Primitive::clear_rep_bool() {
  rep_bool_.Clear();
}
inline bool Primitive::rep_bool(int index) const {
  // @@protoc_insertion_point(field_get:google.protobuf.testing.Primitive.rep_bool)
  return rep_bool_.Get(index);
}
inline void Primitive::set_rep_bool(int index, bool value) {
  rep_bool_.Set(index, value);
  // @@protoc_insertion_point(field_set:google.protobuf.testing.Primitive.rep_bool)
}
inline void Primitive::add_rep_bool(bool value) {
  rep_bool_.Add(value);
  // @@protoc_insertion_point(field_add:google.protobuf.testing.Primitive.rep_bool)
}
inline const ::google::protobuf::RepeatedField< bool >&
Primitive::rep_bool() const {
  // @@protoc_insertion_point(field_list:google.protobuf.testing.Primitive.rep_bool)
  return rep_bool_;
}
inline ::google::protobuf::RepeatedField< bool >*
Primitive::mutable_rep_bool() {
  // @@protoc_insertion_point(field_mutable_list:google.protobuf.testing.Primitive.rep_bool)
  return &rep_bool_;
}

inline const Primitive* Primitive::internal_default_instance() {
  return &Primitive_default_instance_.get();
}
// -------------------------------------------------------------------

// PackedPrimitive

// repeated fixed32 rep_fix32 = 16 [packed = true];
inline int PackedPrimitive::rep_fix32_size() const {
  return rep_fix32_.size();
}
inline void PackedPrimitive::clear_rep_fix32() {
  rep_fix32_.Clear();
}
inline ::google::protobuf::uint32 PackedPrimitive::rep_fix32(int index) const {
  // @@protoc_insertion_point(field_get:google.protobuf.testing.PackedPrimitive.rep_fix32)
  return rep_fix32_.Get(index);
}
inline void PackedPrimitive::set_rep_fix32(int index, ::google::protobuf::uint32 value) {
  rep_fix32_.Set(index, value);
  // @@protoc_insertion_point(field_set:google.protobuf.testing.PackedPrimitive.rep_fix32)
}
inline void PackedPrimitive::add_rep_fix32(::google::protobuf::uint32 value) {
  rep_fix32_.Add(value);
  // @@protoc_insertion_point(field_add:google.protobuf.testing.PackedPrimitive.rep_fix32)
}
inline const ::google::protobuf::RepeatedField< ::google::protobuf::uint32 >&
PackedPrimitive::rep_fix32() const {
  // @@protoc_insertion_point(field_list:google.protobuf.testing.PackedPrimitive.rep_fix32)
  return rep_fix32_;
}
inline ::google::protobuf::RepeatedField< ::google::protobuf::uint32 >*
PackedPrimitive::mutable_rep_fix32() {
  // @@protoc_insertion_point(field_mutable_list:google.protobuf.testing.PackedPrimitive.rep_fix32)
  return &rep_fix32_;
}

// repeated uint32 rep_u32 = 17 [packed = true];
inline int PackedPrimitive::rep_u32_size() const {
  return rep_u32_.size();
}
inline void PackedPrimitive::clear_rep_u32() {
  rep_u32_.Clear();
}
inline ::google::protobuf::uint32 PackedPrimitive::rep_u32(int index) const {
  // @@protoc_insertion_point(field_get:google.protobuf.testing.PackedPrimitive.rep_u32)
  return rep_u32_.Get(index);
}
inline void PackedPrimitive::set_rep_u32(int index, ::google::protobuf::uint32 value) {
  rep_u32_.Set(index, value);
  // @@protoc_insertion_point(field_set:google.protobuf.testing.PackedPrimitive.rep_u32)
}
inline void PackedPrimitive::add_rep_u32(::google::protobuf::uint32 value) {
  rep_u32_.Add(value);
  // @@protoc_insertion_point(field_add:google.protobuf.testing.PackedPrimitive.rep_u32)
}
inline const ::google::protobuf::RepeatedField< ::google::protobuf::uint32 >&
PackedPrimitive::rep_u32() const {
  // @@protoc_insertion_point(field_list:google.protobuf.testing.PackedPrimitive.rep_u32)
  return rep_u32_;
}
inline ::google::protobuf::RepeatedField< ::google::protobuf::uint32 >*
PackedPrimitive::mutable_rep_u32() {
  // @@protoc_insertion_point(field_mutable_list:google.protobuf.testing.PackedPrimitive.rep_u32)
  return &rep_u32_;
}

// repeated int32 rep_i32 = 18 [packed = true];
inline int PackedPrimitive::rep_i32_size() const {
  return rep_i32_.size();
}
inline void PackedPrimitive::clear_rep_i32() {
  rep_i32_.Clear();
}
inline ::google::protobuf::int32 PackedPrimitive::rep_i32(int index) const {
  // @@protoc_insertion_point(field_get:google.protobuf.testing.PackedPrimitive.rep_i32)
  return rep_i32_.Get(index);
}
inline void PackedPrimitive::set_rep_i32(int index, ::google::protobuf::int32 value) {
  rep_i32_.Set(index, value);
  // @@protoc_insertion_point(field_set:google.protobuf.testing.PackedPrimitive.rep_i32)
}
inline void PackedPrimitive::add_rep_i32(::google::protobuf::int32 value) {
  rep_i32_.Add(value);
  // @@protoc_insertion_point(field_add:google.protobuf.testing.PackedPrimitive.rep_i32)
}
inline const ::google::protobuf::RepeatedField< ::google::protobuf::int32 >&
PackedPrimitive::rep_i32() const {
  // @@protoc_insertion_point(field_list:google.protobuf.testing.PackedPrimitive.rep_i32)
  return rep_i32_;
}
inline ::google::protobuf::RepeatedField< ::google::protobuf::int32 >*
PackedPrimitive::mutable_rep_i32() {
  // @@protoc_insertion_point(field_mutable_list:google.protobuf.testing.PackedPrimitive.rep_i32)
  return &rep_i32_;
}

// repeated sfixed32 rep_sf32 = 19 [packed = true];
inline int PackedPrimitive::rep_sf32_size() const {
  return rep_sf32_.size();
}
inline void PackedPrimitive::clear_rep_sf32() {
  rep_sf32_.Clear();
}
inline ::google::protobuf::int32 PackedPrimitive::rep_sf32(int index) const {
  // @@protoc_insertion_point(field_get:google.protobuf.testing.PackedPrimitive.rep_sf32)
  return rep_sf32_.Get(index);
}
inline void PackedPrimitive::set_rep_sf32(int index, ::google::protobuf::int32 value) {
  rep_sf32_.Set(index, value);
  // @@protoc_insertion_point(field_set:google.protobuf.testing.PackedPrimitive.rep_sf32)
}
inline void PackedPrimitive::add_rep_sf32(::google::protobuf::int32 value) {
  rep_sf32_.Add(value);
  // @@protoc_insertion_point(field_add:google.protobuf.testing.PackedPrimitive.rep_sf32)
}
inline const ::google::protobuf::RepeatedField< ::google::protobuf::int32 >&
PackedPrimitive::rep_sf32() const {
  // @@protoc_insertion_point(field_list:google.protobuf.testing.PackedPrimitive.rep_sf32)
  return rep_sf32_;
}
inline ::google::protobuf::RepeatedField< ::google::protobuf::int32 >*
PackedPrimitive::mutable_rep_sf32() {
  // @@protoc_insertion_point(field_mutable_list:google.protobuf.testing.PackedPrimitive.rep_sf32)
  return &rep_sf32_;
}

// repeated sint32 rep_s32 = 20 [packed = true];
inline int PackedPrimitive::rep_s32_size() const {
  return rep_s32_.size();
}
inline void PackedPrimitive::clear_rep_s32() {
  rep_s32_.Clear();
}
inline ::google::protobuf::int32 PackedPrimitive::rep_s32(int index) const {
  // @@protoc_insertion_point(field_get:google.protobuf.testing.PackedPrimitive.rep_s32)
  return rep_s32_.Get(index);
}
inline void PackedPrimitive::set_rep_s32(int index, ::google::protobuf::int32 value) {
  rep_s32_.Set(index, value);
  // @@protoc_insertion_point(field_set:google.protobuf.testing.PackedPrimitive.rep_s32)
}
inline void PackedPrimitive::add_rep_s32(::google::protobuf::int32 value) {
  rep_s32_.Add(value);
  // @@protoc_insertion_point(field_add:google.protobuf.testing.PackedPrimitive.rep_s32)
}
inline const ::google::protobuf::RepeatedField< ::google::protobuf::int32 >&
PackedPrimitive::rep_s32() const {
  // @@protoc_insertion_point(field_list:google.protobuf.testing.PackedPrimitive.rep_s32)
  return rep_s32_;
}
inline ::google::protobuf::RepeatedField< ::google::protobuf::int32 >*
PackedPrimitive::mutable_rep_s32() {
  // @@protoc_insertion_point(field_mutable_list:google.protobuf.testing.PackedPrimitive.rep_s32)
  return &rep_s32_;
}

// repeated fixed64 rep_fix64 = 21 [packed = true];
inline int PackedPrimitive::rep_fix64_size() const {
  return rep_fix64_.size();
}
inline void PackedPrimitive::clear_rep_fix64() {
  rep_fix64_.Clear();
}
inline ::google::protobuf::uint64 PackedPrimitive::rep_fix64(int index) const {
  // @@protoc_insertion_point(field_get:google.protobuf.testing.PackedPrimitive.rep_fix64)
  return rep_fix64_.Get(index);
}
inline void PackedPrimitive::set_rep_fix64(int index, ::google::protobuf::uint64 value) {
  rep_fix64_.Set(index, value);
  // @@protoc_insertion_point(field_set:google.protobuf.testing.PackedPrimitive.rep_fix64)
}
inline void PackedPrimitive::add_rep_fix64(::google::protobuf::uint64 value) {
  rep_fix64_.Add(value);
  // @@protoc_insertion_point(field_add:google.protobuf.testing.PackedPrimitive.rep_fix64)
}
inline const ::google::protobuf::RepeatedField< ::google::protobuf::uint64 >&
PackedPrimitive::rep_fix64() const {
  // @@protoc_insertion_point(field_list:google.protobuf.testing.PackedPrimitive.rep_fix64)
  return rep_fix64_;
}
inline ::google::protobuf::RepeatedField< ::google::protobuf::uint64 >*
PackedPrimitive::mutable_rep_fix64() {
  // @@protoc_insertion_point(field_mutable_list:google.protobuf.testing.PackedPrimitive.rep_fix64)
  return &rep_fix64_;
}

// repeated uint64 rep_u64 = 22 [packed = true];
inline int PackedPrimitive::rep_u64_size() const {
  return rep_u64_.size();
}
inline void PackedPrimitive::clear_rep_u64() {
  rep_u64_.Clear();
}
inline ::google::protobuf::uint64 PackedPrimitive::rep_u64(int index) const {
  // @@protoc_insertion_point(field_get:google.protobuf.testing.PackedPrimitive.rep_u64)
  return rep_u64_.Get(index);
}
inline void PackedPrimitive::set_rep_u64(int index, ::google::protobuf::uint64 value) {
  rep_u64_.Set(index, value);
  // @@protoc_insertion_point(field_set:google.protobuf.testing.PackedPrimitive.rep_u64)
}
inline void PackedPrimitive::add_rep_u64(::google::protobuf::uint64 value) {
  rep_u64_.Add(value);
  // @@protoc_insertion_point(field_add:google.protobuf.testing.PackedPrimitive.rep_u64)
}
inline const ::google::protobuf::RepeatedField< ::google::protobuf::uint64 >&
PackedPrimitive::rep_u64() const {
  // @@protoc_insertion_point(field_list:google.protobuf.testing.PackedPrimitive.rep_u64)
  return rep_u64_;
}
inline ::google::protobuf::RepeatedField< ::google::protobuf::uint64 >*
PackedPrimitive::mutable_rep_u64() {
  // @@protoc_insertion_point(field_mutable_list:google.protobuf.testing.PackedPrimitive.rep_u64)
  return &rep_u64_;
}

// repeated int64 rep_i64 = 23 [packed = true];
inline int PackedPrimitive::rep_i64_size() const {
  return rep_i64_.size();
}
inline void PackedPrimitive::clear_rep_i64() {
  rep_i64_.Clear();
}
inline ::google::protobuf::int64 PackedPrimitive::rep_i64(int index) const {
  // @@protoc_insertion_point(field_get:google.protobuf.testing.PackedPrimitive.rep_i64)
  return rep_i64_.Get(index);
}
inline void PackedPrimitive::set_rep_i64(int index, ::google::protobuf::int64 value) {
  rep_i64_.Set(index, value);
  // @@protoc_insertion_point(field_set:google.protobuf.testing.PackedPrimitive.rep_i64)
}
inline void PackedPrimitive::add_rep_i64(::google::protobuf::int64 value) {
  rep_i64_.Add(value);
  // @@protoc_insertion_point(field_add:google.protobuf.testing.PackedPrimitive.rep_i64)
}
inline const ::google::protobuf::RepeatedField< ::google::protobuf::int64 >&
PackedPrimitive::rep_i64() const {
  // @@protoc_insertion_point(field_list:google.protobuf.testing.PackedPrimitive.rep_i64)
  return rep_i64_;
}
inline ::google::protobuf::RepeatedField< ::google::protobuf::int64 >*
PackedPrimitive::mutable_rep_i64() {
  // @@protoc_insertion_point(field_mutable_list:google.protobuf.testing.PackedPrimitive.rep_i64)
  return &rep_i64_;
}

// repeated sfixed64 rep_sf64 = 24 [packed = true];
inline int PackedPrimitive::rep_sf64_size() const {
  return rep_sf64_.size();
}
inline void PackedPrimitive::clear_rep_sf64() {
  rep_sf64_.Clear();
}
inline ::google::protobuf::int64 PackedPrimitive::rep_sf64(int index) const {
  // @@protoc_insertion_point(field_get:google.protobuf.testing.PackedPrimitive.rep_sf64)
  return rep_sf64_.Get(index);
}
inline void PackedPrimitive::set_rep_sf64(int index, ::google::protobuf::int64 value) {
  rep_sf64_.Set(index, value);
  // @@protoc_insertion_point(field_set:google.protobuf.testing.PackedPrimitive.rep_sf64)
}
inline void PackedPrimitive::add_rep_sf64(::google::protobuf::int64 value) {
  rep_sf64_.Add(value);
  // @@protoc_insertion_point(field_add:google.protobuf.testing.PackedPrimitive.rep_sf64)
}
inline const ::google::protobuf::RepeatedField< ::google::protobuf::int64 >&
PackedPrimitive::rep_sf64() const {
  // @@protoc_insertion_point(field_list:google.protobuf.testing.PackedPrimitive.rep_sf64)
  return rep_sf64_;
}
inline ::google::protobuf::RepeatedField< ::google::protobuf::int64 >*
PackedPrimitive::mutable_rep_sf64() {
  // @@protoc_insertion_point(field_mutable_list:google.protobuf.testing.PackedPrimitive.rep_sf64)
  return &rep_sf64_;
}

// repeated sint64 rep_s64 = 25 [packed = true];
inline int PackedPrimitive::rep_s64_size() const {
  return rep_s64_.size();
}
inline void PackedPrimitive::clear_rep_s64() {
  rep_s64_.Clear();
}
inline ::google::protobuf::int64 PackedPrimitive::rep_s64(int index) const {
  // @@protoc_insertion_point(field_get:google.protobuf.testing.PackedPrimitive.rep_s64)
  return rep_s64_.Get(index);
}
inline void PackedPrimitive::set_rep_s64(int index, ::google::protobuf::int64 value) {
  rep_s64_.Set(index, value);
  // @@protoc_insertion_point(field_set:google.protobuf.testing.PackedPrimitive.rep_s64)
}
inline void PackedPrimitive::add_rep_s64(::google::protobuf::int64 value) {
  rep_s64_.Add(value);
  // @@protoc_insertion_point(field_add:google.protobuf.testing.PackedPrimitive.rep_s64)
}
inline const ::google::protobuf::RepeatedField< ::google::protobuf::int64 >&
PackedPrimitive::rep_s64() const {
  // @@protoc_insertion_point(field_list:google.protobuf.testing.PackedPrimitive.rep_s64)
  return rep_s64_;
}
inline ::google::protobuf::RepeatedField< ::google::protobuf::int64 >*
PackedPrimitive::mutable_rep_s64() {
  // @@protoc_insertion_point(field_mutable_list:google.protobuf.testing.PackedPrimitive.rep_s64)
  return &rep_s64_;
}

// repeated float rep_float = 28 [packed = true];
inline int PackedPrimitive::rep_float_size() const {
  return rep_float_.size();
}
inline void PackedPrimitive::clear_rep_float() {
  rep_float_.Clear();
}
inline float PackedPrimitive::rep_float(int index) const {
  // @@protoc_insertion_point(field_get:google.protobuf.testing.PackedPrimitive.rep_float)
  return rep_float_.Get(index);
}
inline void PackedPrimitive::set_rep_float(int index, float value) {
  rep_float_.Set(index, value);
  // @@protoc_insertion_point(field_set:google.protobuf.testing.PackedPrimitive.rep_float)
}
inline void PackedPrimitive::add_rep_float(float value) {
  rep_float_.Add(value);
  // @@protoc_insertion_point(field_add:google.protobuf.testing.PackedPrimitive.rep_float)
}
inline const ::google::protobuf::RepeatedField< float >&
PackedPrimitive::rep_float() const {
  // @@protoc_insertion_point(field_list:google.protobuf.testing.PackedPrimitive.rep_float)
  return rep_float_;
}
inline ::google::protobuf::RepeatedField< float >*
PackedPrimitive::mutable_rep_float() {
  // @@protoc_insertion_point(field_mutable_list:google.protobuf.testing.PackedPrimitive.rep_float)
  return &rep_float_;
}

// repeated double rep_double = 29 [packed = true];
inline int PackedPrimitive::rep_double_size() const {
  return rep_double_.size();
}
inline void PackedPrimitive::clear_rep_double() {
  rep_double_.Clear();
}
inline double PackedPrimitive::rep_double(int index) const {
  // @@protoc_insertion_point(field_get:google.protobuf.testing.PackedPrimitive.rep_double)
  return rep_double_.Get(index);
}
inline void PackedPrimitive::set_rep_double(int index, double value) {
  rep_double_.Set(index, value);
  // @@protoc_insertion_point(field_set:google.protobuf.testing.PackedPrimitive.rep_double)
}
inline void PackedPrimitive::add_rep_double(double value) {
  rep_double_.Add(value);
  // @@protoc_insertion_point(field_add:google.protobuf.testing.PackedPrimitive.rep_double)
}
inline const ::google::protobuf::RepeatedField< double >&
PackedPrimitive::rep_double() const {
  // @@protoc_insertion_point(field_list:google.protobuf.testing.PackedPrimitive.rep_double)
  return rep_double_;
}
inline ::google::protobuf::RepeatedField< double >*
PackedPrimitive::mutable_rep_double() {
  // @@protoc_insertion_point(field_mutable_list:google.protobuf.testing.PackedPrimitive.rep_double)
  return &rep_double_;
}

// repeated bool rep_bool = 30 [packed = true];
inline int PackedPrimitive::rep_bool_size() const {
  return rep_bool_.size();
}
inline void PackedPrimitive::clear_rep_bool() {
  rep_bool_.Clear();
}
inline bool PackedPrimitive::rep_bool(int index) const {
  // @@protoc_insertion_point(field_get:google.protobuf.testing.PackedPrimitive.rep_bool)
  return rep_bool_.Get(index);
}
inline void PackedPrimitive::set_rep_bool(int index, bool value) {
  rep_bool_.Set(index, value);
  // @@protoc_insertion_point(field_set:google.protobuf.testing.PackedPrimitive.rep_bool)
}
inline void PackedPrimitive::add_rep_bool(bool value) {
  rep_bool_.Add(value);
  // @@protoc_insertion_point(field_add:google.protobuf.testing.PackedPrimitive.rep_bool)
}
inline const ::google::protobuf::RepeatedField< bool >&
PackedPrimitive::rep_bool() const {
  // @@protoc_insertion_point(field_list:google.protobuf.testing.PackedPrimitive.rep_bool)
  return rep_bool_;
}
inline ::google::protobuf::RepeatedField< bool >*
PackedPrimitive::mutable_rep_bool() {
  // @@protoc_insertion_point(field_mutable_list:google.protobuf.testing.PackedPrimitive.rep_bool)
  return &rep_bool_;
}

inline const PackedPrimitive* PackedPrimitive::internal_default_instance() {
  return &PackedPrimitive_default_instance_.get();
}
// -------------------------------------------------------------------

// NestedBook

// optional .google.protobuf.testing.Book book = 1;
inline bool NestedBook::has_book() const {
  return (_has_bits_[0] & 0x00000001u) != 0;
}
inline void NestedBook::set_has_book() {
  _has_bits_[0] |= 0x00000001u;
}
inline void NestedBook::clear_has_book() {
  _has_bits_[0] &= ~0x00000001u;
}
inline void NestedBook::clear_book() {
  if (book_ != NULL) book_->::google::protobuf::testing::Book::Clear();
  clear_has_book();
}
inline const ::google::protobuf::testing::Book& NestedBook::book() const {
  // @@protoc_insertion_point(field_get:google.protobuf.testing.NestedBook.book)
  return book_ != NULL ? *book_
                         : *::google::protobuf::testing::Book::internal_default_instance();
}
inline ::google::protobuf::testing::Book* NestedBook::mutable_book() {
  set_has_book();
  if (book_ == NULL) {
    book_ = new ::google::protobuf::testing::Book;
  }
  // @@protoc_insertion_point(field_mutable:google.protobuf.testing.NestedBook.book)
  return book_;
}
inline ::google::protobuf::testing::Book* NestedBook::release_book() {
  // @@protoc_insertion_point(field_release:google.protobuf.testing.NestedBook.book)
  clear_has_book();
  ::google::protobuf::testing::Book* temp = book_;
  book_ = NULL;
  return temp;
}
inline void NestedBook::set_allocated_book(::google::protobuf::testing::Book* book) {
  delete book_;
  book_ = book;
  if (book) {
    set_has_book();
  } else {
    clear_has_book();
  }
  // @@protoc_insertion_point(field_set_allocated:google.protobuf.testing.NestedBook.book)
}

inline const NestedBook* NestedBook::internal_default_instance() {
  return &NestedBook_default_instance_.get();
}
// -------------------------------------------------------------------

// BadNestedBook

// repeated uint32 book = 1 [packed = true];
inline int BadNestedBook::book_size() const {
  return book_.size();
}
inline void BadNestedBook::clear_book() {
  book_.Clear();
}
inline ::google::protobuf::uint32 BadNestedBook::book(int index) const {
  // @@protoc_insertion_point(field_get:google.protobuf.testing.BadNestedBook.book)
  return book_.Get(index);
}
inline void BadNestedBook::set_book(int index, ::google::protobuf::uint32 value) {
  book_.Set(index, value);
  // @@protoc_insertion_point(field_set:google.protobuf.testing.BadNestedBook.book)
}
inline void BadNestedBook::add_book(::google::protobuf::uint32 value) {
  book_.Add(value);
  // @@protoc_insertion_point(field_add:google.protobuf.testing.BadNestedBook.book)
}
inline const ::google::protobuf::RepeatedField< ::google::protobuf::uint32 >&
BadNestedBook::book() const {
  // @@protoc_insertion_point(field_list:google.protobuf.testing.BadNestedBook.book)
  return book_;
}
inline ::google::protobuf::RepeatedField< ::google::protobuf::uint32 >*
BadNestedBook::mutable_book() {
  // @@protoc_insertion_point(field_mutable_list:google.protobuf.testing.BadNestedBook.book)
  return &book_;
}

inline const BadNestedBook* BadNestedBook::internal_default_instance() {
  return &BadNestedBook_default_instance_.get();
}
// -------------------------------------------------------------------

// Cyclic

// optional int32 m_int = 1;
inline bool Cyclic::has_m_int() const {
  return (_has_bits_[0] & 0x00000001u) != 0;
}
inline void Cyclic::set_has_m_int() {
  _has_bits_[0] |= 0x00000001u;
}
inline void Cyclic::clear_has_m_int() {
  _has_bits_[0] &= ~0x00000001u;
}
inline void Cyclic::clear_m_int() {
  m_int_ = 0;
  clear_has_m_int();
}
inline ::google::protobuf::int32 Cyclic::m_int() const {
  // @@protoc_insertion_point(field_get:google.protobuf.testing.Cyclic.m_int)
  return m_int_;
}
inline void Cyclic::set_m_int(::google::protobuf::int32 value) {
  set_has_m_int();
  m_int_ = value;
  // @@protoc_insertion_point(field_set:google.protobuf.testing.Cyclic.m_int)
}

// optional string m_str = 2;
inline bool Cyclic::has_m_str() const {
  return (_has_bits_[0] & 0x00000002u) != 0;
}
inline void Cyclic::set_has_m_str() {
  _has_bits_[0] |= 0x00000002u;
}
inline void Cyclic::clear_has_m_str() {
  _has_bits_[0] &= ~0x00000002u;
}
inline void Cyclic::clear_m_str() {
  m_str_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  clear_has_m_str();
}
inline const ::std::string& Cyclic::m_str() const {
  // @@protoc_insertion_point(field_get:google.protobuf.testing.Cyclic.m_str)
  return m_str_.GetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void Cyclic::set_m_str(const ::std::string& value) {
  set_has_m_str();
  m_str_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:google.protobuf.testing.Cyclic.m_str)
}
inline void Cyclic::set_m_str(const char* value) {
  set_has_m_str();
  m_str_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:google.protobuf.testing.Cyclic.m_str)
}
inline void Cyclic::set_m_str(const char* value, size_t size) {
  set_has_m_str();
  m_str_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:google.protobuf.testing.Cyclic.m_str)
}
inline ::std::string* Cyclic::mutable_m_str() {
  set_has_m_str();
  // @@protoc_insertion_point(field_mutable:google.protobuf.testing.Cyclic.m_str)
  return m_str_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline ::std::string* Cyclic::release_m_str() {
  // @@protoc_insertion_point(field_release:google.protobuf.testing.Cyclic.m_str)
  clear_has_m_str();
  return m_str_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void Cyclic::set_allocated_m_str(::std::string* m_str) {
  if (m_str != NULL) {
    set_has_m_str();
  } else {
    clear_has_m_str();
  }
  m_str_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), m_str);
  // @@protoc_insertion_point(field_set_allocated:google.protobuf.testing.Cyclic.m_str)
}

// optional .google.protobuf.testing.Book m_book = 3;
inline bool Cyclic::has_m_book() const {
  return (_has_bits_[0] & 0x00000004u) != 0;
}
inline void Cyclic::set_has_m_book() {
  _has_bits_[0] |= 0x00000004u;
}
inline void Cyclic::clear_has_m_book() {
  _has_bits_[0] &= ~0x00000004u;
}
inline void Cyclic::clear_m_book() {
  if (m_book_ != NULL) m_book_->::google::protobuf::testing::Book::Clear();
  clear_has_m_book();
}
inline const ::google::protobuf::testing::Book& Cyclic::m_book() const {
  // @@protoc_insertion_point(field_get:google.protobuf.testing.Cyclic.m_book)
  return m_book_ != NULL ? *m_book_
                         : *::google::protobuf::testing::Book::internal_default_instance();
}
inline ::google::protobuf::testing::Book* Cyclic::mutable_m_book() {
  set_has_m_book();
  if (m_book_ == NULL) {
    m_book_ = new ::google::protobuf::testing::Book;
  }
  // @@protoc_insertion_point(field_mutable:google.protobuf.testing.Cyclic.m_book)
  return m_book_;
}
inline ::google::protobuf::testing::Book* Cyclic::release_m_book() {
  // @@protoc_insertion_point(field_release:google.protobuf.testing.Cyclic.m_book)
  clear_has_m_book();
  ::google::protobuf::testing::Book* temp = m_book_;
  m_book_ = NULL;
  return temp;
}
inline void Cyclic::set_allocated_m_book(::google::protobuf::testing::Book* m_book) {
  delete m_book_;
  m_book_ = m_book;
  if (m_book) {
    set_has_m_book();
  } else {
    clear_has_m_book();
  }
  // @@protoc_insertion_point(field_set_allocated:google.protobuf.testing.Cyclic.m_book)
}

// repeated .google.protobuf.testing.Author m_author = 5;
inline int Cyclic::m_author_size() const {
  return m_author_.size();
}
inline void Cyclic::clear_m_author() {
  m_author_.Clear();
}
inline const ::google::protobuf::testing::Author& Cyclic::m_author(int index) const {
  // @@protoc_insertion_point(field_get:google.protobuf.testing.Cyclic.m_author)
  return m_author_.Get(index);
}
inline ::google::protobuf::testing::Author* Cyclic::mutable_m_author(int index) {
  // @@protoc_insertion_point(field_mutable:google.protobuf.testing.Cyclic.m_author)
  return m_author_.Mutable(index);
}
inline ::google::protobuf::testing::Author* Cyclic::add_m_author() {
  // @@protoc_insertion_point(field_add:google.protobuf.testing.Cyclic.m_author)
  return m_author_.Add();
}
inline ::google::protobuf::RepeatedPtrField< ::google::protobuf::testing::Author >*
Cyclic::mutable_m_author() {
  // @@protoc_insertion_point(field_mutable_list:google.protobuf.testing.Cyclic.m_author)
  return &m_author_;
}
inline const ::google::protobuf::RepeatedPtrField< ::google::protobuf::testing::Author >&
Cyclic::m_author() const {
  // @@protoc_insertion_point(field_list:google.protobuf.testing.Cyclic.m_author)
  return m_author_;
}

// optional .google.protobuf.testing.Cyclic m_cyclic = 4;
inline bool Cyclic::has_m_cyclic() const {
  return (_has_bits_[0] & 0x00000010u) != 0;
}
inline void Cyclic::set_has_m_cyclic() {
  _has_bits_[0] |= 0x00000010u;
}
inline void Cyclic::clear_has_m_cyclic() {
  _has_bits_[0] &= ~0x00000010u;
}
inline void Cyclic::clear_m_cyclic() {
  if (m_cyclic_ != NULL) m_cyclic_->::google::protobuf::testing::Cyclic::Clear();
  clear_has_m_cyclic();
}
inline const ::google::protobuf::testing::Cyclic& Cyclic::m_cyclic() const {
  // @@protoc_insertion_point(field_get:google.protobuf.testing.Cyclic.m_cyclic)
  return m_cyclic_ != NULL ? *m_cyclic_
                         : *::google::protobuf::testing::Cyclic::internal_default_instance();
}
inline ::google::protobuf::testing::Cyclic* Cyclic::mutable_m_cyclic() {
  set_has_m_cyclic();
  if (m_cyclic_ == NULL) {
    m_cyclic_ = new ::google::protobuf::testing::Cyclic;
  }
  // @@protoc_insertion_point(field_mutable:google.protobuf.testing.Cyclic.m_cyclic)
  return m_cyclic_;
}
inline ::google::protobuf::testing::Cyclic* Cyclic::release_m_cyclic() {
  // @@protoc_insertion_point(field_release:google.protobuf.testing.Cyclic.m_cyclic)
  clear_has_m_cyclic();
  ::google::protobuf::testing::Cyclic* temp = m_cyclic_;
  m_cyclic_ = NULL;
  return temp;
}
inline void Cyclic::set_allocated_m_cyclic(::google::protobuf::testing::Cyclic* m_cyclic) {
  delete m_cyclic_;
  m_cyclic_ = m_cyclic;
  if (m_cyclic) {
    set_has_m_cyclic();
  } else {
    clear_has_m_cyclic();
  }
  // @@protoc_insertion_point(field_set_allocated:google.protobuf.testing.Cyclic.m_cyclic)
}

inline const Cyclic* Cyclic::internal_default_instance() {
  return &Cyclic_default_instance_.get();
}
#endif  // !PROTOBUF_INLINE_NOT_IN_HEADERS
// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------


// @@protoc_insertion_point(namespace_scope)

}  // namespace testing
}  // namespace protobuf
}  // namespace google

#ifndef SWIG
namespace google {
namespace protobuf {

template <> struct is_proto_enum< ::google::protobuf::testing::Book_Type> : ::google::protobuf::internal::true_type {};
template <>
inline const EnumDescriptor* GetEnumDescriptor< ::google::protobuf::testing::Book_Type>() {
  return ::google::protobuf::testing::Book_Type_descriptor();
}

}  // namespace protobuf
}  // namespace google
#endif  // SWIG

// @@protoc_insertion_point(global_scope)

#endif  // PROTOBUF_google_2fprotobuf_2futil_2finternal_2ftestdata_2fbooks_2eproto__INCLUDED

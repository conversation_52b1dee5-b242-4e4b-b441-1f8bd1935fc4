// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: google/protobuf/util/message_differencer_unittest.proto

#ifndef PROTOBUF_google_2fprotobuf_2futil_2fmessage_5fdifferencer_5funittest_2eproto__INCLUDED
#define PROTOBUF_google_2fprotobuf_2futil_2fmessage_5fdifferencer_5funittest_2eproto__INCLUDED

#include <string>

#include <google/protobuf/stubs/common.h>

#if GOOGLE_PROTOBUF_VERSION < 3001000
#error This file was generated by a newer version of protoc which is
#error incompatible with your Protocol Buffer headers.  Please update
#error your headers.
#endif
#if 3001000 < GOOGLE_PROTOBUF_MIN_PROTOC_VERSION
#error This file was generated by an older version of protoc which is
#error incompatible with your Protocol Buffer headers.  Please
#error regenerate this file with a newer version of protoc.
#endif

#include <google/protobuf/arena.h>
#include <google/protobuf/arenastring.h>
#include <google/protobuf/generated_message_util.h>
#include <google/protobuf/metadata.h>
#include <google/protobuf/message.h>
#include <google/protobuf/repeated_field.h>
#include <google/protobuf/extension_set.h>
#include <google/protobuf/unknown_field_set.h>
// @@protoc_insertion_point(includes)

namespace protobuf_unittest {

// Internal implementation detail -- do not call these.
void protobuf_AddDesc_google_2fprotobuf_2futil_2fmessage_5fdifferencer_5funittest_2eproto();
void protobuf_InitDefaults_google_2fprotobuf_2futil_2fmessage_5fdifferencer_5funittest_2eproto();
void protobuf_AssignDesc_google_2fprotobuf_2futil_2fmessage_5fdifferencer_5funittest_2eproto();
void protobuf_ShutdownFile_google_2fprotobuf_2futil_2fmessage_5fdifferencer_5funittest_2eproto();

class TestDiffMessage;
class TestDiffMessage_Item;
class TestField;

// ===================================================================

class TestField : public ::google::protobuf::Message /* @@protoc_insertion_point(class_definition:protobuf_unittest.TestField) */ {
 public:
  TestField();
  virtual ~TestField();

  TestField(const TestField& from);

  inline TestField& operator=(const TestField& from) {
    CopyFrom(from);
    return *this;
  }

  inline const ::google::protobuf::UnknownFieldSet& unknown_fields() const {
    return _internal_metadata_.unknown_fields();
  }

  inline ::google::protobuf::UnknownFieldSet* mutable_unknown_fields() {
    return _internal_metadata_.mutable_unknown_fields();
  }

  static const ::google::protobuf::Descriptor* descriptor();
  static const TestField& default_instance();

  static const TestField* internal_default_instance();

  void Swap(TestField* other);

  // implements Message ----------------------------------------------

  inline TestField* New() const { return New(NULL); }

  TestField* New(::google::protobuf::Arena* arena) const;
  void CopyFrom(const ::google::protobuf::Message& from);
  void MergeFrom(const ::google::protobuf::Message& from);
  void CopyFrom(const TestField& from);
  void MergeFrom(const TestField& from);
  void Clear();
  bool IsInitialized() const;

  size_t ByteSizeLong() const;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input);
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const;
  ::google::protobuf::uint8* InternalSerializeWithCachedSizesToArray(
      bool deterministic, ::google::protobuf::uint8* output) const;
  ::google::protobuf::uint8* SerializeWithCachedSizesToArray(::google::protobuf::uint8* output) const {
    return InternalSerializeWithCachedSizesToArray(false, output);
  }
  int GetCachedSize() const { return _cached_size_; }
  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const;
  void InternalSwap(TestField* other);
  void UnsafeMergeFrom(const TestField& from);
  private:
  inline ::google::protobuf::Arena* GetArenaNoVirtual() const {
    return _internal_metadata_.arena();
  }
  inline void* MaybeArenaPtr() const {
    return _internal_metadata_.raw_arena_ptr();
  }
  public:

  ::google::protobuf::Metadata GetMetadata() const;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // optional int32 a = 3;
  bool has_a() const;
  void clear_a();
  static const int kAFieldNumber = 3;
  ::google::protobuf::int32 a() const;
  void set_a(::google::protobuf::int32 value);

  // optional int32 b = 4;
  bool has_b() const;
  void clear_b();
  static const int kBFieldNumber = 4;
  ::google::protobuf::int32 b() const;
  void set_b(::google::protobuf::int32 value);

  // optional int32 c = 1;
  bool has_c() const;
  void clear_c();
  static const int kCFieldNumber = 1;
  ::google::protobuf::int32 c() const;
  void set_c(::google::protobuf::int32 value);

  // repeated int32 rc = 2;
  int rc_size() const;
  void clear_rc();
  static const int kRcFieldNumber = 2;
  ::google::protobuf::int32 rc(int index) const;
  void set_rc(int index, ::google::protobuf::int32 value);
  void add_rc(::google::protobuf::int32 value);
  const ::google::protobuf::RepeatedField< ::google::protobuf::int32 >&
      rc() const;
  ::google::protobuf::RepeatedField< ::google::protobuf::int32 >*
      mutable_rc();

  // optional .protobuf_unittest.TestField m = 5;
  bool has_m() const;
  void clear_m();
  static const int kMFieldNumber = 5;
  const ::protobuf_unittest::TestField& m() const;
  ::protobuf_unittest::TestField* mutable_m();
  ::protobuf_unittest::TestField* release_m();
  void set_allocated_m(::protobuf_unittest::TestField* m);

  static const int kTfFieldNumber = 100;
  static ::google::protobuf::internal::ExtensionIdentifier< ::protobuf_unittest::TestDiffMessage,
      ::google::protobuf::internal::MessageTypeTraits< ::protobuf_unittest::TestField >, 11, false >
    tf;
  // @@protoc_insertion_point(class_scope:protobuf_unittest.TestField)
 private:
  inline void set_has_a();
  inline void clear_has_a();
  inline void set_has_b();
  inline void clear_has_b();
  inline void set_has_c();
  inline void clear_has_c();
  inline void set_has_m();
  inline void clear_has_m();

  ::google::protobuf::internal::InternalMetadataWithArena _internal_metadata_;
  ::google::protobuf::internal::HasBits<1> _has_bits_;
  mutable int _cached_size_;
  ::google::protobuf::RepeatedField< ::google::protobuf::int32 > rc_;
  ::protobuf_unittest::TestField* m_;
  ::google::protobuf::int32 a_;
  ::google::protobuf::int32 b_;
  ::google::protobuf::int32 c_;
  friend void  protobuf_InitDefaults_google_2fprotobuf_2futil_2fmessage_5fdifferencer_5funittest_2eproto_impl();
  friend void  protobuf_AddDesc_google_2fprotobuf_2futil_2fmessage_5fdifferencer_5funittest_2eproto_impl();
  friend void protobuf_AssignDesc_google_2fprotobuf_2futil_2fmessage_5fdifferencer_5funittest_2eproto();
  friend void protobuf_ShutdownFile_google_2fprotobuf_2futil_2fmessage_5fdifferencer_5funittest_2eproto();

  void InitAsDefaultInstance();
};
extern ::google::protobuf::internal::ExplicitlyConstructed<TestField> TestField_default_instance_;

// -------------------------------------------------------------------

class TestDiffMessage_Item : public ::google::protobuf::Message /* @@protoc_insertion_point(class_definition:protobuf_unittest.TestDiffMessage.Item) */ {
 public:
  TestDiffMessage_Item();
  virtual ~TestDiffMessage_Item();

  TestDiffMessage_Item(const TestDiffMessage_Item& from);

  inline TestDiffMessage_Item& operator=(const TestDiffMessage_Item& from) {
    CopyFrom(from);
    return *this;
  }

  inline const ::google::protobuf::UnknownFieldSet& unknown_fields() const {
    return _internal_metadata_.unknown_fields();
  }

  inline ::google::protobuf::UnknownFieldSet* mutable_unknown_fields() {
    return _internal_metadata_.mutable_unknown_fields();
  }

  static const ::google::protobuf::Descriptor* descriptor();
  static const TestDiffMessage_Item& default_instance();

  static const TestDiffMessage_Item* internal_default_instance();

  void Swap(TestDiffMessage_Item* other);

  // implements Message ----------------------------------------------

  inline TestDiffMessage_Item* New() const { return New(NULL); }

  TestDiffMessage_Item* New(::google::protobuf::Arena* arena) const;
  void CopyFrom(const ::google::protobuf::Message& from);
  void MergeFrom(const ::google::protobuf::Message& from);
  void CopyFrom(const TestDiffMessage_Item& from);
  void MergeFrom(const TestDiffMessage_Item& from);
  void Clear();
  bool IsInitialized() const;

  size_t ByteSizeLong() const;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input);
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const;
  ::google::protobuf::uint8* InternalSerializeWithCachedSizesToArray(
      bool deterministic, ::google::protobuf::uint8* output) const;
  ::google::protobuf::uint8* SerializeWithCachedSizesToArray(::google::protobuf::uint8* output) const {
    return InternalSerializeWithCachedSizesToArray(false, output);
  }
  int GetCachedSize() const { return _cached_size_; }
  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const;
  void InternalSwap(TestDiffMessage_Item* other);
  void UnsafeMergeFrom(const TestDiffMessage_Item& from);
  private:
  inline ::google::protobuf::Arena* GetArenaNoVirtual() const {
    return _internal_metadata_.arena();
  }
  inline void* MaybeArenaPtr() const {
    return _internal_metadata_.raw_arena_ptr();
  }
  public:

  ::google::protobuf::Metadata GetMetadata() const;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // optional int32 a = 2;
  bool has_a() const;
  void clear_a();
  static const int kAFieldNumber = 2;
  ::google::protobuf::int32 a() const;
  void set_a(::google::protobuf::int32 value);

  // optional string b = 4;
  bool has_b() const;
  void clear_b();
  static const int kBFieldNumber = 4;
  const ::std::string& b() const;
  void set_b(const ::std::string& value);
  void set_b(const char* value);
  void set_b(const char* value, size_t size);
  ::std::string* mutable_b();
  ::std::string* release_b();
  void set_allocated_b(::std::string* b);

  // repeated int32 ra = 3;
  int ra_size() const;
  void clear_ra();
  static const int kRaFieldNumber = 3;
  ::google::protobuf::int32 ra(int index) const;
  void set_ra(int index, ::google::protobuf::int32 value);
  void add_ra(::google::protobuf::int32 value);
  const ::google::protobuf::RepeatedField< ::google::protobuf::int32 >&
      ra() const;
  ::google::protobuf::RepeatedField< ::google::protobuf::int32 >*
      mutable_ra();

  // repeated string rb = 5;
  int rb_size() const;
  void clear_rb();
  static const int kRbFieldNumber = 5;
  const ::std::string& rb(int index) const;
  ::std::string* mutable_rb(int index);
  void set_rb(int index, const ::std::string& value);
  void set_rb(int index, const char* value);
  void set_rb(int index, const char* value, size_t size);
  ::std::string* add_rb();
  void add_rb(const ::std::string& value);
  void add_rb(const char* value);
  void add_rb(const char* value, size_t size);
  const ::google::protobuf::RepeatedPtrField< ::std::string>& rb() const;
  ::google::protobuf::RepeatedPtrField< ::std::string>* mutable_rb();

  // optional .protobuf_unittest.TestField m = 6;
  bool has_m() const;
  void clear_m();
  static const int kMFieldNumber = 6;
  const ::protobuf_unittest::TestField& m() const;
  ::protobuf_unittest::TestField* mutable_m();
  ::protobuf_unittest::TestField* release_m();
  void set_allocated_m(::protobuf_unittest::TestField* m);

  // repeated .protobuf_unittest.TestField rm = 7;
  int rm_size() const;
  void clear_rm();
  static const int kRmFieldNumber = 7;
  const ::protobuf_unittest::TestField& rm(int index) const;
  ::protobuf_unittest::TestField* mutable_rm(int index);
  ::protobuf_unittest::TestField* add_rm();
  ::google::protobuf::RepeatedPtrField< ::protobuf_unittest::TestField >*
      mutable_rm();
  const ::google::protobuf::RepeatedPtrField< ::protobuf_unittest::TestField >&
      rm() const;

  // @@protoc_insertion_point(class_scope:protobuf_unittest.TestDiffMessage.Item)
 private:
  inline void set_has_a();
  inline void clear_has_a();
  inline void set_has_b();
  inline void clear_has_b();
  inline void set_has_m();
  inline void clear_has_m();

  ::google::protobuf::internal::InternalMetadataWithArena _internal_metadata_;
  ::google::protobuf::internal::HasBits<1> _has_bits_;
  mutable int _cached_size_;
  ::google::protobuf::RepeatedField< ::google::protobuf::int32 > ra_;
  ::google::protobuf::RepeatedPtrField< ::std::string> rb_;
  ::google::protobuf::RepeatedPtrField< ::protobuf_unittest::TestField > rm_;
  ::google::protobuf::internal::ArenaStringPtr b_;
  ::protobuf_unittest::TestField* m_;
  ::google::protobuf::int32 a_;
  friend void  protobuf_InitDefaults_google_2fprotobuf_2futil_2fmessage_5fdifferencer_5funittest_2eproto_impl();
  friend void  protobuf_AddDesc_google_2fprotobuf_2futil_2fmessage_5fdifferencer_5funittest_2eproto_impl();
  friend void protobuf_AssignDesc_google_2fprotobuf_2futil_2fmessage_5fdifferencer_5funittest_2eproto();
  friend void protobuf_ShutdownFile_google_2fprotobuf_2futil_2fmessage_5fdifferencer_5funittest_2eproto();

  void InitAsDefaultInstance();
};
extern ::google::protobuf::internal::ExplicitlyConstructed<TestDiffMessage_Item> TestDiffMessage_Item_default_instance_;

// -------------------------------------------------------------------

class TestDiffMessage : public ::google::protobuf::Message /* @@protoc_insertion_point(class_definition:protobuf_unittest.TestDiffMessage) */ {
 public:
  TestDiffMessage();
  virtual ~TestDiffMessage();

  TestDiffMessage(const TestDiffMessage& from);

  inline TestDiffMessage& operator=(const TestDiffMessage& from) {
    CopyFrom(from);
    return *this;
  }

  inline const ::google::protobuf::UnknownFieldSet& unknown_fields() const {
    return _internal_metadata_.unknown_fields();
  }

  inline ::google::protobuf::UnknownFieldSet* mutable_unknown_fields() {
    return _internal_metadata_.mutable_unknown_fields();
  }

  static const ::google::protobuf::Descriptor* descriptor();
  static const TestDiffMessage& default_instance();

  static const TestDiffMessage* internal_default_instance();

  void Swap(TestDiffMessage* other);

  // implements Message ----------------------------------------------

  inline TestDiffMessage* New() const { return New(NULL); }

  TestDiffMessage* New(::google::protobuf::Arena* arena) const;
  void CopyFrom(const ::google::protobuf::Message& from);
  void MergeFrom(const ::google::protobuf::Message& from);
  void CopyFrom(const TestDiffMessage& from);
  void MergeFrom(const TestDiffMessage& from);
  void Clear();
  bool IsInitialized() const;

  size_t ByteSizeLong() const;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input);
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const;
  ::google::protobuf::uint8* InternalSerializeWithCachedSizesToArray(
      bool deterministic, ::google::protobuf::uint8* output) const;
  ::google::protobuf::uint8* SerializeWithCachedSizesToArray(::google::protobuf::uint8* output) const {
    return InternalSerializeWithCachedSizesToArray(false, output);
  }
  int GetCachedSize() const { return _cached_size_; }
  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const;
  void InternalSwap(TestDiffMessage* other);
  void UnsafeMergeFrom(const TestDiffMessage& from);
  private:
  inline ::google::protobuf::Arena* GetArenaNoVirtual() const {
    return _internal_metadata_.arena();
  }
  inline void* MaybeArenaPtr() const {
    return _internal_metadata_.raw_arena_ptr();
  }
  public:

  ::google::protobuf::Metadata GetMetadata() const;

  // nested types ----------------------------------------------------

  typedef TestDiffMessage_Item Item;

  // accessors -------------------------------------------------------

  // repeated group Item = 1 { ... };
  int item_size() const;
  void clear_item();
  static const int kItemFieldNumber = 1;
  const ::protobuf_unittest::TestDiffMessage_Item& item(int index) const;
  ::protobuf_unittest::TestDiffMessage_Item* mutable_item(int index);
  ::protobuf_unittest::TestDiffMessage_Item* add_item();
  ::google::protobuf::RepeatedPtrField< ::protobuf_unittest::TestDiffMessage_Item >*
      mutable_item();
  const ::google::protobuf::RepeatedPtrField< ::protobuf_unittest::TestDiffMessage_Item >&
      item() const;

  // optional int32 v = 13 [deprecated = true];
  GOOGLE_PROTOBUF_DEPRECATED_ATTR bool has_v() const;
  GOOGLE_PROTOBUF_DEPRECATED_ATTR void clear_v();
  GOOGLE_PROTOBUF_DEPRECATED_ATTR static const int kVFieldNumber = 13;
  GOOGLE_PROTOBUF_DEPRECATED_ATTR ::google::protobuf::int32 v() const;
  GOOGLE_PROTOBUF_DEPRECATED_ATTR void set_v(::google::protobuf::int32 value);

  // optional string w = 14;
  bool has_w() const;
  void clear_w();
  static const int kWFieldNumber = 14;
  const ::std::string& w() const;
  void set_w(const ::std::string& value);
  void set_w(const char* value);
  void set_w(const char* value, size_t size);
  ::std::string* mutable_w();
  ::std::string* release_w();
  void set_allocated_w(::std::string* w);

  // optional .protobuf_unittest.TestField m = 15;
  bool has_m() const;
  void clear_m();
  static const int kMFieldNumber = 15;
  const ::protobuf_unittest::TestField& m() const;
  ::protobuf_unittest::TestField* mutable_m();
  ::protobuf_unittest::TestField* release_m();
  void set_allocated_m(::protobuf_unittest::TestField* m);

  // repeated int32 rv = 11;
  int rv_size() const;
  void clear_rv();
  static const int kRvFieldNumber = 11;
  ::google::protobuf::int32 rv(int index) const;
  void set_rv(int index, ::google::protobuf::int32 value);
  void add_rv(::google::protobuf::int32 value);
  const ::google::protobuf::RepeatedField< ::google::protobuf::int32 >&
      rv() const;
  ::google::protobuf::RepeatedField< ::google::protobuf::int32 >*
      mutable_rv();

  // repeated string rw = 10;
  int rw_size() const;
  void clear_rw();
  static const int kRwFieldNumber = 10;
  const ::std::string& rw(int index) const;
  ::std::string* mutable_rw(int index);
  void set_rw(int index, const ::std::string& value);
  void set_rw(int index, const char* value);
  void set_rw(int index, const char* value, size_t size);
  ::std::string* add_rw();
  void add_rw(const ::std::string& value);
  void add_rw(const char* value);
  void add_rw(const char* value, size_t size);
  const ::google::protobuf::RepeatedPtrField< ::std::string>& rw() const;
  ::google::protobuf::RepeatedPtrField< ::std::string>* mutable_rw();

  // repeated .protobuf_unittest.TestField rm = 12 [deprecated = true];
  GOOGLE_PROTOBUF_DEPRECATED_ATTR int rm_size() const;
  GOOGLE_PROTOBUF_DEPRECATED_ATTR void clear_rm();
  GOOGLE_PROTOBUF_DEPRECATED_ATTR static const int kRmFieldNumber = 12;
  GOOGLE_PROTOBUF_DEPRECATED_ATTR const ::protobuf_unittest::TestField& rm(int index) const;
  GOOGLE_PROTOBUF_DEPRECATED_ATTR ::protobuf_unittest::TestField* mutable_rm(int index);
  GOOGLE_PROTOBUF_DEPRECATED_ATTR ::protobuf_unittest::TestField* add_rm();
  GOOGLE_PROTOBUF_DEPRECATED_ATTR ::google::protobuf::RepeatedPtrField< ::protobuf_unittest::TestField >*
      mutable_rm();
  GOOGLE_PROTOBUF_DEPRECATED_ATTR const ::google::protobuf::RepeatedPtrField< ::protobuf_unittest::TestField >&
      rm() const;

  GOOGLE_PROTOBUF_EXTENSION_ACCESSORS(TestDiffMessage)
  // @@protoc_insertion_point(class_scope:protobuf_unittest.TestDiffMessage)
 private:
  inline void set_has_v();
  inline void clear_has_v();
  inline void set_has_w();
  inline void clear_has_w();
  inline void set_has_m();
  inline void clear_has_m();

  ::google::protobuf::internal::ExtensionSet _extensions_;

  ::google::protobuf::internal::InternalMetadataWithArena _internal_metadata_;
  ::google::protobuf::internal::HasBits<1> _has_bits_;
  mutable int _cached_size_;
  ::google::protobuf::RepeatedPtrField< ::protobuf_unittest::TestDiffMessage_Item > item_;
  ::google::protobuf::RepeatedField< ::google::protobuf::int32 > rv_;
  ::google::protobuf::RepeatedPtrField< ::std::string> rw_;
  ::google::protobuf::RepeatedPtrField< ::protobuf_unittest::TestField > rm_;
  ::google::protobuf::internal::ArenaStringPtr w_;
  ::protobuf_unittest::TestField* m_;
  ::google::protobuf::int32 v_;
  friend void  protobuf_InitDefaults_google_2fprotobuf_2futil_2fmessage_5fdifferencer_5funittest_2eproto_impl();
  friend void  protobuf_AddDesc_google_2fprotobuf_2futil_2fmessage_5fdifferencer_5funittest_2eproto_impl();
  friend void protobuf_AssignDesc_google_2fprotobuf_2futil_2fmessage_5fdifferencer_5funittest_2eproto();
  friend void protobuf_ShutdownFile_google_2fprotobuf_2futil_2fmessage_5fdifferencer_5funittest_2eproto();

  void InitAsDefaultInstance();
};
extern ::google::protobuf::internal::ExplicitlyConstructed<TestDiffMessage> TestDiffMessage_default_instance_;

// ===================================================================


// ===================================================================

#if !PROTOBUF_INLINE_NOT_IN_HEADERS
// TestField

// optional int32 a = 3;
inline bool TestField::has_a() const {
  return (_has_bits_[0] & 0x00000001u) != 0;
}
inline void TestField::set_has_a() {
  _has_bits_[0] |= 0x00000001u;
}
inline void TestField::clear_has_a() {
  _has_bits_[0] &= ~0x00000001u;
}
inline void TestField::clear_a() {
  a_ = 0;
  clear_has_a();
}
inline ::google::protobuf::int32 TestField::a() const {
  // @@protoc_insertion_point(field_get:protobuf_unittest.TestField.a)
  return a_;
}
inline void TestField::set_a(::google::protobuf::int32 value) {
  set_has_a();
  a_ = value;
  // @@protoc_insertion_point(field_set:protobuf_unittest.TestField.a)
}

// optional int32 b = 4;
inline bool TestField::has_b() const {
  return (_has_bits_[0] & 0x00000002u) != 0;
}
inline void TestField::set_has_b() {
  _has_bits_[0] |= 0x00000002u;
}
inline void TestField::clear_has_b() {
  _has_bits_[0] &= ~0x00000002u;
}
inline void TestField::clear_b() {
  b_ = 0;
  clear_has_b();
}
inline ::google::protobuf::int32 TestField::b() const {
  // @@protoc_insertion_point(field_get:protobuf_unittest.TestField.b)
  return b_;
}
inline void TestField::set_b(::google::protobuf::int32 value) {
  set_has_b();
  b_ = value;
  // @@protoc_insertion_point(field_set:protobuf_unittest.TestField.b)
}

// optional int32 c = 1;
inline bool TestField::has_c() const {
  return (_has_bits_[0] & 0x00000004u) != 0;
}
inline void TestField::set_has_c() {
  _has_bits_[0] |= 0x00000004u;
}
inline void TestField::clear_has_c() {
  _has_bits_[0] &= ~0x00000004u;
}
inline void TestField::clear_c() {
  c_ = 0;
  clear_has_c();
}
inline ::google::protobuf::int32 TestField::c() const {
  // @@protoc_insertion_point(field_get:protobuf_unittest.TestField.c)
  return c_;
}
inline void TestField::set_c(::google::protobuf::int32 value) {
  set_has_c();
  c_ = value;
  // @@protoc_insertion_point(field_set:protobuf_unittest.TestField.c)
}

// repeated int32 rc = 2;
inline int TestField::rc_size() const {
  return rc_.size();
}
inline void TestField::clear_rc() {
  rc_.Clear();
}
inline ::google::protobuf::int32 TestField::rc(int index) const {
  // @@protoc_insertion_point(field_get:protobuf_unittest.TestField.rc)
  return rc_.Get(index);
}
inline void TestField::set_rc(int index, ::google::protobuf::int32 value) {
  rc_.Set(index, value);
  // @@protoc_insertion_point(field_set:protobuf_unittest.TestField.rc)
}
inline void TestField::add_rc(::google::protobuf::int32 value) {
  rc_.Add(value);
  // @@protoc_insertion_point(field_add:protobuf_unittest.TestField.rc)
}
inline const ::google::protobuf::RepeatedField< ::google::protobuf::int32 >&
TestField::rc() const {
  // @@protoc_insertion_point(field_list:protobuf_unittest.TestField.rc)
  return rc_;
}
inline ::google::protobuf::RepeatedField< ::google::protobuf::int32 >*
TestField::mutable_rc() {
  // @@protoc_insertion_point(field_mutable_list:protobuf_unittest.TestField.rc)
  return &rc_;
}

// optional .protobuf_unittest.TestField m = 5;
inline bool TestField::has_m() const {
  return (_has_bits_[0] & 0x00000010u) != 0;
}
inline void TestField::set_has_m() {
  _has_bits_[0] |= 0x00000010u;
}
inline void TestField::clear_has_m() {
  _has_bits_[0] &= ~0x00000010u;
}
inline void TestField::clear_m() {
  if (m_ != NULL) m_->::protobuf_unittest::TestField::Clear();
  clear_has_m();
}
inline const ::protobuf_unittest::TestField& TestField::m() const {
  // @@protoc_insertion_point(field_get:protobuf_unittest.TestField.m)
  return m_ != NULL ? *m_
                         : *::protobuf_unittest::TestField::internal_default_instance();
}
inline ::protobuf_unittest::TestField* TestField::mutable_m() {
  set_has_m();
  if (m_ == NULL) {
    m_ = new ::protobuf_unittest::TestField;
  }
  // @@protoc_insertion_point(field_mutable:protobuf_unittest.TestField.m)
  return m_;
}
inline ::protobuf_unittest::TestField* TestField::release_m() {
  // @@protoc_insertion_point(field_release:protobuf_unittest.TestField.m)
  clear_has_m();
  ::protobuf_unittest::TestField* temp = m_;
  m_ = NULL;
  return temp;
}
inline void TestField::set_allocated_m(::protobuf_unittest::TestField* m) {
  delete m_;
  m_ = m;
  if (m) {
    set_has_m();
  } else {
    clear_has_m();
  }
  // @@protoc_insertion_point(field_set_allocated:protobuf_unittest.TestField.m)
}

inline const TestField* TestField::internal_default_instance() {
  return &TestField_default_instance_.get();
}
// -------------------------------------------------------------------

// TestDiffMessage_Item

// optional int32 a = 2;
inline bool TestDiffMessage_Item::has_a() const {
  return (_has_bits_[0] & 0x00000001u) != 0;
}
inline void TestDiffMessage_Item::set_has_a() {
  _has_bits_[0] |= 0x00000001u;
}
inline void TestDiffMessage_Item::clear_has_a() {
  _has_bits_[0] &= ~0x00000001u;
}
inline void TestDiffMessage_Item::clear_a() {
  a_ = 0;
  clear_has_a();
}
inline ::google::protobuf::int32 TestDiffMessage_Item::a() const {
  // @@protoc_insertion_point(field_get:protobuf_unittest.TestDiffMessage.Item.a)
  return a_;
}
inline void TestDiffMessage_Item::set_a(::google::protobuf::int32 value) {
  set_has_a();
  a_ = value;
  // @@protoc_insertion_point(field_set:protobuf_unittest.TestDiffMessage.Item.a)
}

// optional string b = 4;
inline bool TestDiffMessage_Item::has_b() const {
  return (_has_bits_[0] & 0x00000002u) != 0;
}
inline void TestDiffMessage_Item::set_has_b() {
  _has_bits_[0] |= 0x00000002u;
}
inline void TestDiffMessage_Item::clear_has_b() {
  _has_bits_[0] &= ~0x00000002u;
}
inline void TestDiffMessage_Item::clear_b() {
  b_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  clear_has_b();
}
inline const ::std::string& TestDiffMessage_Item::b() const {
  // @@protoc_insertion_point(field_get:protobuf_unittest.TestDiffMessage.Item.b)
  return b_.GetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void TestDiffMessage_Item::set_b(const ::std::string& value) {
  set_has_b();
  b_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:protobuf_unittest.TestDiffMessage.Item.b)
}
inline void TestDiffMessage_Item::set_b(const char* value) {
  set_has_b();
  b_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:protobuf_unittest.TestDiffMessage.Item.b)
}
inline void TestDiffMessage_Item::set_b(const char* value, size_t size) {
  set_has_b();
  b_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:protobuf_unittest.TestDiffMessage.Item.b)
}
inline ::std::string* TestDiffMessage_Item::mutable_b() {
  set_has_b();
  // @@protoc_insertion_point(field_mutable:protobuf_unittest.TestDiffMessage.Item.b)
  return b_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline ::std::string* TestDiffMessage_Item::release_b() {
  // @@protoc_insertion_point(field_release:protobuf_unittest.TestDiffMessage.Item.b)
  clear_has_b();
  return b_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void TestDiffMessage_Item::set_allocated_b(::std::string* b) {
  if (b != NULL) {
    set_has_b();
  } else {
    clear_has_b();
  }
  b_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), b);
  // @@protoc_insertion_point(field_set_allocated:protobuf_unittest.TestDiffMessage.Item.b)
}

// repeated int32 ra = 3;
inline int TestDiffMessage_Item::ra_size() const {
  return ra_.size();
}
inline void TestDiffMessage_Item::clear_ra() {
  ra_.Clear();
}
inline ::google::protobuf::int32 TestDiffMessage_Item::ra(int index) const {
  // @@protoc_insertion_point(field_get:protobuf_unittest.TestDiffMessage.Item.ra)
  return ra_.Get(index);
}
inline void TestDiffMessage_Item::set_ra(int index, ::google::protobuf::int32 value) {
  ra_.Set(index, value);
  // @@protoc_insertion_point(field_set:protobuf_unittest.TestDiffMessage.Item.ra)
}
inline void TestDiffMessage_Item::add_ra(::google::protobuf::int32 value) {
  ra_.Add(value);
  // @@protoc_insertion_point(field_add:protobuf_unittest.TestDiffMessage.Item.ra)
}
inline const ::google::protobuf::RepeatedField< ::google::protobuf::int32 >&
TestDiffMessage_Item::ra() const {
  // @@protoc_insertion_point(field_list:protobuf_unittest.TestDiffMessage.Item.ra)
  return ra_;
}
inline ::google::protobuf::RepeatedField< ::google::protobuf::int32 >*
TestDiffMessage_Item::mutable_ra() {
  // @@protoc_insertion_point(field_mutable_list:protobuf_unittest.TestDiffMessage.Item.ra)
  return &ra_;
}

// repeated string rb = 5;
inline int TestDiffMessage_Item::rb_size() const {
  return rb_.size();
}
inline void TestDiffMessage_Item::clear_rb() {
  rb_.Clear();
}
inline const ::std::string& TestDiffMessage_Item::rb(int index) const {
  // @@protoc_insertion_point(field_get:protobuf_unittest.TestDiffMessage.Item.rb)
  return rb_.Get(index);
}
inline ::std::string* TestDiffMessage_Item::mutable_rb(int index) {
  // @@protoc_insertion_point(field_mutable:protobuf_unittest.TestDiffMessage.Item.rb)
  return rb_.Mutable(index);
}
inline void TestDiffMessage_Item::set_rb(int index, const ::std::string& value) {
  // @@protoc_insertion_point(field_set:protobuf_unittest.TestDiffMessage.Item.rb)
  rb_.Mutable(index)->assign(value);
}
inline void TestDiffMessage_Item::set_rb(int index, const char* value) {
  rb_.Mutable(index)->assign(value);
  // @@protoc_insertion_point(field_set_char:protobuf_unittest.TestDiffMessage.Item.rb)
}
inline void TestDiffMessage_Item::set_rb(int index, const char* value, size_t size) {
  rb_.Mutable(index)->assign(
    reinterpret_cast<const char*>(value), size);
  // @@protoc_insertion_point(field_set_pointer:protobuf_unittest.TestDiffMessage.Item.rb)
}
inline ::std::string* TestDiffMessage_Item::add_rb() {
  // @@protoc_insertion_point(field_add_mutable:protobuf_unittest.TestDiffMessage.Item.rb)
  return rb_.Add();
}
inline void TestDiffMessage_Item::add_rb(const ::std::string& value) {
  rb_.Add()->assign(value);
  // @@protoc_insertion_point(field_add:protobuf_unittest.TestDiffMessage.Item.rb)
}
inline void TestDiffMessage_Item::add_rb(const char* value) {
  rb_.Add()->assign(value);
  // @@protoc_insertion_point(field_add_char:protobuf_unittest.TestDiffMessage.Item.rb)
}
inline void TestDiffMessage_Item::add_rb(const char* value, size_t size) {
  rb_.Add()->assign(reinterpret_cast<const char*>(value), size);
  // @@protoc_insertion_point(field_add_pointer:protobuf_unittest.TestDiffMessage.Item.rb)
}
inline const ::google::protobuf::RepeatedPtrField< ::std::string>&
TestDiffMessage_Item::rb() const {
  // @@protoc_insertion_point(field_list:protobuf_unittest.TestDiffMessage.Item.rb)
  return rb_;
}
inline ::google::protobuf::RepeatedPtrField< ::std::string>*
TestDiffMessage_Item::mutable_rb() {
  // @@protoc_insertion_point(field_mutable_list:protobuf_unittest.TestDiffMessage.Item.rb)
  return &rb_;
}

// optional .protobuf_unittest.TestField m = 6;
inline bool TestDiffMessage_Item::has_m() const {
  return (_has_bits_[0] & 0x00000010u) != 0;
}
inline void TestDiffMessage_Item::set_has_m() {
  _has_bits_[0] |= 0x00000010u;
}
inline void TestDiffMessage_Item::clear_has_m() {
  _has_bits_[0] &= ~0x00000010u;
}
inline void TestDiffMessage_Item::clear_m() {
  if (m_ != NULL) m_->::protobuf_unittest::TestField::Clear();
  clear_has_m();
}
inline const ::protobuf_unittest::TestField& TestDiffMessage_Item::m() const {
  // @@protoc_insertion_point(field_get:protobuf_unittest.TestDiffMessage.Item.m)
  return m_ != NULL ? *m_
                         : *::protobuf_unittest::TestField::internal_default_instance();
}
inline ::protobuf_unittest::TestField* TestDiffMessage_Item::mutable_m() {
  set_has_m();
  if (m_ == NULL) {
    m_ = new ::protobuf_unittest::TestField;
  }
  // @@protoc_insertion_point(field_mutable:protobuf_unittest.TestDiffMessage.Item.m)
  return m_;
}
inline ::protobuf_unittest::TestField* TestDiffMessage_Item::release_m() {
  // @@protoc_insertion_point(field_release:protobuf_unittest.TestDiffMessage.Item.m)
  clear_has_m();
  ::protobuf_unittest::TestField* temp = m_;
  m_ = NULL;
  return temp;
}
inline void TestDiffMessage_Item::set_allocated_m(::protobuf_unittest::TestField* m) {
  delete m_;
  m_ = m;
  if (m) {
    set_has_m();
  } else {
    clear_has_m();
  }
  // @@protoc_insertion_point(field_set_allocated:protobuf_unittest.TestDiffMessage.Item.m)
}

// repeated .protobuf_unittest.TestField rm = 7;
inline int TestDiffMessage_Item::rm_size() const {
  return rm_.size();
}
inline void TestDiffMessage_Item::clear_rm() {
  rm_.Clear();
}
inline const ::protobuf_unittest::TestField& TestDiffMessage_Item::rm(int index) const {
  // @@protoc_insertion_point(field_get:protobuf_unittest.TestDiffMessage.Item.rm)
  return rm_.Get(index);
}
inline ::protobuf_unittest::TestField* TestDiffMessage_Item::mutable_rm(int index) {
  // @@protoc_insertion_point(field_mutable:protobuf_unittest.TestDiffMessage.Item.rm)
  return rm_.Mutable(index);
}
inline ::protobuf_unittest::TestField* TestDiffMessage_Item::add_rm() {
  // @@protoc_insertion_point(field_add:protobuf_unittest.TestDiffMessage.Item.rm)
  return rm_.Add();
}
inline ::google::protobuf::RepeatedPtrField< ::protobuf_unittest::TestField >*
TestDiffMessage_Item::mutable_rm() {
  // @@protoc_insertion_point(field_mutable_list:protobuf_unittest.TestDiffMessage.Item.rm)
  return &rm_;
}
inline const ::google::protobuf::RepeatedPtrField< ::protobuf_unittest::TestField >&
TestDiffMessage_Item::rm() const {
  // @@protoc_insertion_point(field_list:protobuf_unittest.TestDiffMessage.Item.rm)
  return rm_;
}

inline const TestDiffMessage_Item* TestDiffMessage_Item::internal_default_instance() {
  return &TestDiffMessage_Item_default_instance_.get();
}
// -------------------------------------------------------------------

// TestDiffMessage

// repeated group Item = 1 { ... };
inline int TestDiffMessage::item_size() const {
  return item_.size();
}
inline void TestDiffMessage::clear_item() {
  item_.Clear();
}
inline const ::protobuf_unittest::TestDiffMessage_Item& TestDiffMessage::item(int index) const {
  // @@protoc_insertion_point(field_get:protobuf_unittest.TestDiffMessage.item)
  return item_.Get(index);
}
inline ::protobuf_unittest::TestDiffMessage_Item* TestDiffMessage::mutable_item(int index) {
  // @@protoc_insertion_point(field_mutable:protobuf_unittest.TestDiffMessage.item)
  return item_.Mutable(index);
}
inline ::protobuf_unittest::TestDiffMessage_Item* TestDiffMessage::add_item() {
  // @@protoc_insertion_point(field_add:protobuf_unittest.TestDiffMessage.item)
  return item_.Add();
}
inline ::google::protobuf::RepeatedPtrField< ::protobuf_unittest::TestDiffMessage_Item >*
TestDiffMessage::mutable_item() {
  // @@protoc_insertion_point(field_mutable_list:protobuf_unittest.TestDiffMessage.item)
  return &item_;
}
inline const ::google::protobuf::RepeatedPtrField< ::protobuf_unittest::TestDiffMessage_Item >&
TestDiffMessage::item() const {
  // @@protoc_insertion_point(field_list:protobuf_unittest.TestDiffMessage.item)
  return item_;
}

// optional int32 v = 13 [deprecated = true];
inline bool TestDiffMessage::has_v() const {
  return (_has_bits_[0] & 0x00000002u) != 0;
}
inline void TestDiffMessage::set_has_v() {
  _has_bits_[0] |= 0x00000002u;
}
inline void TestDiffMessage::clear_has_v() {
  _has_bits_[0] &= ~0x00000002u;
}
inline void TestDiffMessage::clear_v() {
  v_ = 0;
  clear_has_v();
}
inline ::google::protobuf::int32 TestDiffMessage::v() const {
  // @@protoc_insertion_point(field_get:protobuf_unittest.TestDiffMessage.v)
  return v_;
}
inline void TestDiffMessage::set_v(::google::protobuf::int32 value) {
  set_has_v();
  v_ = value;
  // @@protoc_insertion_point(field_set:protobuf_unittest.TestDiffMessage.v)
}

// optional string w = 14;
inline bool TestDiffMessage::has_w() const {
  return (_has_bits_[0] & 0x00000004u) != 0;
}
inline void TestDiffMessage::set_has_w() {
  _has_bits_[0] |= 0x00000004u;
}
inline void TestDiffMessage::clear_has_w() {
  _has_bits_[0] &= ~0x00000004u;
}
inline void TestDiffMessage::clear_w() {
  w_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  clear_has_w();
}
inline const ::std::string& TestDiffMessage::w() const {
  // @@protoc_insertion_point(field_get:protobuf_unittest.TestDiffMessage.w)
  return w_.GetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void TestDiffMessage::set_w(const ::std::string& value) {
  set_has_w();
  w_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:protobuf_unittest.TestDiffMessage.w)
}
inline void TestDiffMessage::set_w(const char* value) {
  set_has_w();
  w_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:protobuf_unittest.TestDiffMessage.w)
}
inline void TestDiffMessage::set_w(const char* value, size_t size) {
  set_has_w();
  w_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:protobuf_unittest.TestDiffMessage.w)
}
inline ::std::string* TestDiffMessage::mutable_w() {
  set_has_w();
  // @@protoc_insertion_point(field_mutable:protobuf_unittest.TestDiffMessage.w)
  return w_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline ::std::string* TestDiffMessage::release_w() {
  // @@protoc_insertion_point(field_release:protobuf_unittest.TestDiffMessage.w)
  clear_has_w();
  return w_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void TestDiffMessage::set_allocated_w(::std::string* w) {
  if (w != NULL) {
    set_has_w();
  } else {
    clear_has_w();
  }
  w_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), w);
  // @@protoc_insertion_point(field_set_allocated:protobuf_unittest.TestDiffMessage.w)
}

// optional .protobuf_unittest.TestField m = 15;
inline bool TestDiffMessage::has_m() const {
  return (_has_bits_[0] & 0x00000008u) != 0;
}
inline void TestDiffMessage::set_has_m() {
  _has_bits_[0] |= 0x00000008u;
}
inline void TestDiffMessage::clear_has_m() {
  _has_bits_[0] &= ~0x00000008u;
}
inline void TestDiffMessage::clear_m() {
  if (m_ != NULL) m_->::protobuf_unittest::TestField::Clear();
  clear_has_m();
}
inline const ::protobuf_unittest::TestField& TestDiffMessage::m() const {
  // @@protoc_insertion_point(field_get:protobuf_unittest.TestDiffMessage.m)
  return m_ != NULL ? *m_
                         : *::protobuf_unittest::TestField::internal_default_instance();
}
inline ::protobuf_unittest::TestField* TestDiffMessage::mutable_m() {
  set_has_m();
  if (m_ == NULL) {
    m_ = new ::protobuf_unittest::TestField;
  }
  // @@protoc_insertion_point(field_mutable:protobuf_unittest.TestDiffMessage.m)
  return m_;
}
inline ::protobuf_unittest::TestField* TestDiffMessage::release_m() {
  // @@protoc_insertion_point(field_release:protobuf_unittest.TestDiffMessage.m)
  clear_has_m();
  ::protobuf_unittest::TestField* temp = m_;
  m_ = NULL;
  return temp;
}
inline void TestDiffMessage::set_allocated_m(::protobuf_unittest::TestField* m) {
  delete m_;
  m_ = m;
  if (m) {
    set_has_m();
  } else {
    clear_has_m();
  }
  // @@protoc_insertion_point(field_set_allocated:protobuf_unittest.TestDiffMessage.m)
}

// repeated int32 rv = 11;
inline int TestDiffMessage::rv_size() const {
  return rv_.size();
}
inline void TestDiffMessage::clear_rv() {
  rv_.Clear();
}
inline ::google::protobuf::int32 TestDiffMessage::rv(int index) const {
  // @@protoc_insertion_point(field_get:protobuf_unittest.TestDiffMessage.rv)
  return rv_.Get(index);
}
inline void TestDiffMessage::set_rv(int index, ::google::protobuf::int32 value) {
  rv_.Set(index, value);
  // @@protoc_insertion_point(field_set:protobuf_unittest.TestDiffMessage.rv)
}
inline void TestDiffMessage::add_rv(::google::protobuf::int32 value) {
  rv_.Add(value);
  // @@protoc_insertion_point(field_add:protobuf_unittest.TestDiffMessage.rv)
}
inline const ::google::protobuf::RepeatedField< ::google::protobuf::int32 >&
TestDiffMessage::rv() const {
  // @@protoc_insertion_point(field_list:protobuf_unittest.TestDiffMessage.rv)
  return rv_;
}
inline ::google::protobuf::RepeatedField< ::google::protobuf::int32 >*
TestDiffMessage::mutable_rv() {
  // @@protoc_insertion_point(field_mutable_list:protobuf_unittest.TestDiffMessage.rv)
  return &rv_;
}

// repeated string rw = 10;
inline int TestDiffMessage::rw_size() const {
  return rw_.size();
}
inline void TestDiffMessage::clear_rw() {
  rw_.Clear();
}
inline const ::std::string& TestDiffMessage::rw(int index) const {
  // @@protoc_insertion_point(field_get:protobuf_unittest.TestDiffMessage.rw)
  return rw_.Get(index);
}
inline ::std::string* TestDiffMessage::mutable_rw(int index) {
  // @@protoc_insertion_point(field_mutable:protobuf_unittest.TestDiffMessage.rw)
  return rw_.Mutable(index);
}
inline void TestDiffMessage::set_rw(int index, const ::std::string& value) {
  // @@protoc_insertion_point(field_set:protobuf_unittest.TestDiffMessage.rw)
  rw_.Mutable(index)->assign(value);
}
inline void TestDiffMessage::set_rw(int index, const char* value) {
  rw_.Mutable(index)->assign(value);
  // @@protoc_insertion_point(field_set_char:protobuf_unittest.TestDiffMessage.rw)
}
inline void TestDiffMessage::set_rw(int index, const char* value, size_t size) {
  rw_.Mutable(index)->assign(
    reinterpret_cast<const char*>(value), size);
  // @@protoc_insertion_point(field_set_pointer:protobuf_unittest.TestDiffMessage.rw)
}
inline ::std::string* TestDiffMessage::add_rw() {
  // @@protoc_insertion_point(field_add_mutable:protobuf_unittest.TestDiffMessage.rw)
  return rw_.Add();
}
inline void TestDiffMessage::add_rw(const ::std::string& value) {
  rw_.Add()->assign(value);
  // @@protoc_insertion_point(field_add:protobuf_unittest.TestDiffMessage.rw)
}
inline void TestDiffMessage::add_rw(const char* value) {
  rw_.Add()->assign(value);
  // @@protoc_insertion_point(field_add_char:protobuf_unittest.TestDiffMessage.rw)
}
inline void TestDiffMessage::add_rw(const char* value, size_t size) {
  rw_.Add()->assign(reinterpret_cast<const char*>(value), size);
  // @@protoc_insertion_point(field_add_pointer:protobuf_unittest.TestDiffMessage.rw)
}
inline const ::google::protobuf::RepeatedPtrField< ::std::string>&
TestDiffMessage::rw() const {
  // @@protoc_insertion_point(field_list:protobuf_unittest.TestDiffMessage.rw)
  return rw_;
}
inline ::google::protobuf::RepeatedPtrField< ::std::string>*
TestDiffMessage::mutable_rw() {
  // @@protoc_insertion_point(field_mutable_list:protobuf_unittest.TestDiffMessage.rw)
  return &rw_;
}

// repeated .protobuf_unittest.TestField rm = 12 [deprecated = true];
inline int TestDiffMessage::rm_size() const {
  return rm_.size();
}
inline void TestDiffMessage::clear_rm() {
  rm_.Clear();
}
inline const ::protobuf_unittest::TestField& TestDiffMessage::rm(int index) const {
  // @@protoc_insertion_point(field_get:protobuf_unittest.TestDiffMessage.rm)
  return rm_.Get(index);
}
inline ::protobuf_unittest::TestField* TestDiffMessage::mutable_rm(int index) {
  // @@protoc_insertion_point(field_mutable:protobuf_unittest.TestDiffMessage.rm)
  return rm_.Mutable(index);
}
inline ::protobuf_unittest::TestField* TestDiffMessage::add_rm() {
  // @@protoc_insertion_point(field_add:protobuf_unittest.TestDiffMessage.rm)
  return rm_.Add();
}
inline ::google::protobuf::RepeatedPtrField< ::protobuf_unittest::TestField >*
TestDiffMessage::mutable_rm() {
  // @@protoc_insertion_point(field_mutable_list:protobuf_unittest.TestDiffMessage.rm)
  return &rm_;
}
inline const ::google::protobuf::RepeatedPtrField< ::protobuf_unittest::TestField >&
TestDiffMessage::rm() const {
  // @@protoc_insertion_point(field_list:protobuf_unittest.TestDiffMessage.rm)
  return rm_;
}

inline const TestDiffMessage* TestDiffMessage::internal_default_instance() {
  return &TestDiffMessage_default_instance_.get();
}
#endif  // !PROTOBUF_INLINE_NOT_IN_HEADERS
// -------------------------------------------------------------------

// -------------------------------------------------------------------


// @@protoc_insertion_point(namespace_scope)

}  // namespace protobuf_unittest

// @@protoc_insertion_point(global_scope)

#endif  // PROTOBUF_google_2fprotobuf_2futil_2fmessage_5fdifferencer_5funittest_2eproto__INCLUDED

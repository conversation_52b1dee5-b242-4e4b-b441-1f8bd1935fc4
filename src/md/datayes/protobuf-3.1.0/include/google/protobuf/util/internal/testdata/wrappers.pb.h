// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: google/protobuf/util/internal/testdata/wrappers.proto

#ifndef PROTOBUF_google_2fprotobuf_2futil_2finternal_2ftestdata_2fwrappers_2eproto__INCLUDED
#define PROTOBUF_google_2fprotobuf_2futil_2finternal_2ftestdata_2fwrappers_2eproto__INCLUDED

#include <string>

#include <google/protobuf/stubs/common.h>

#if GOOGLE_PROTOBUF_VERSION < 3001000
#error This file was generated by a newer version of protoc which is
#error incompatible with your Protocol Buffer headers.  Please update
#error your headers.
#endif
#if 3001000 < GOOGLE_PROTOBUF_MIN_PROTOC_VERSION
#error This file was generated by an older version of protoc which is
#error incompatible with your Protocol Buffer headers.  Please
#error regenerate this file with a newer version of protoc.
#endif

#include <google/protobuf/arena.h>
#include <google/protobuf/arenastring.h>
#include <google/protobuf/generated_message_util.h>
#include <google/protobuf/metadata.h>
#include <google/protobuf/message.h>
#include <google/protobuf/repeated_field.h>
#include <google/protobuf/extension_set.h>
#include <google/protobuf/unknown_field_set.h>
#include <google/protobuf/wrappers.pb.h>
// @@protoc_insertion_point(includes)

namespace google {
namespace protobuf {
namespace testing {

// Internal implementation detail -- do not call these.
void protobuf_AddDesc_google_2fprotobuf_2futil_2finternal_2ftestdata_2fwrappers_2eproto();
void protobuf_InitDefaults_google_2fprotobuf_2futil_2finternal_2ftestdata_2fwrappers_2eproto();
void protobuf_AssignDesc_google_2fprotobuf_2futil_2finternal_2ftestdata_2fwrappers_2eproto();
void protobuf_ShutdownFile_google_2fprotobuf_2futil_2finternal_2ftestdata_2fwrappers_2eproto();

class BoolWrapper;
class BytesWrapper;
class DoubleWrapper;
class FloatWrapper;
class Int32Wrapper;
class Int64Wrapper;
class StringWrapper;
class UInt32Wrapper;
class UInt64Wrapper;
class WrappersTestCases;

// ===================================================================

class WrappersTestCases : public ::google::protobuf::Message /* @@protoc_insertion_point(class_definition:google.protobuf.testing.WrappersTestCases) */ {
 public:
  WrappersTestCases();
  virtual ~WrappersTestCases();

  WrappersTestCases(const WrappersTestCases& from);

  inline WrappersTestCases& operator=(const WrappersTestCases& from) {
    CopyFrom(from);
    return *this;
  }

  static const ::google::protobuf::Descriptor* descriptor();
  static const WrappersTestCases& default_instance();

  static const WrappersTestCases* internal_default_instance();

  void Swap(WrappersTestCases* other);

  // implements Message ----------------------------------------------

  inline WrappersTestCases* New() const { return New(NULL); }

  WrappersTestCases* New(::google::protobuf::Arena* arena) const;
  void CopyFrom(const ::google::protobuf::Message& from);
  void MergeFrom(const ::google::protobuf::Message& from);
  void CopyFrom(const WrappersTestCases& from);
  void MergeFrom(const WrappersTestCases& from);
  void Clear();
  bool IsInitialized() const;

  size_t ByteSizeLong() const;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input);
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const;
  ::google::protobuf::uint8* InternalSerializeWithCachedSizesToArray(
      bool deterministic, ::google::protobuf::uint8* output) const;
  ::google::protobuf::uint8* SerializeWithCachedSizesToArray(::google::protobuf::uint8* output) const {
    return InternalSerializeWithCachedSizesToArray(false, output);
  }
  int GetCachedSize() const { return _cached_size_; }
  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const;
  void InternalSwap(WrappersTestCases* other);
  void UnsafeMergeFrom(const WrappersTestCases& from);
  private:
  inline ::google::protobuf::Arena* GetArenaNoVirtual() const {
    return _internal_metadata_.arena();
  }
  inline void* MaybeArenaPtr() const {
    return _internal_metadata_.raw_arena_ptr();
  }
  public:

  ::google::protobuf::Metadata GetMetadata() const;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // optional .google.protobuf.testing.DoubleWrapper double_wrapper = 1;
  bool has_double_wrapper() const;
  void clear_double_wrapper();
  static const int kDoubleWrapperFieldNumber = 1;
  const ::google::protobuf::testing::DoubleWrapper& double_wrapper() const;
  ::google::protobuf::testing::DoubleWrapper* mutable_double_wrapper();
  ::google::protobuf::testing::DoubleWrapper* release_double_wrapper();
  void set_allocated_double_wrapper(::google::protobuf::testing::DoubleWrapper* double_wrapper);

  // optional .google.protobuf.testing.FloatWrapper float_wrapper = 2;
  bool has_float_wrapper() const;
  void clear_float_wrapper();
  static const int kFloatWrapperFieldNumber = 2;
  const ::google::protobuf::testing::FloatWrapper& float_wrapper() const;
  ::google::protobuf::testing::FloatWrapper* mutable_float_wrapper();
  ::google::protobuf::testing::FloatWrapper* release_float_wrapper();
  void set_allocated_float_wrapper(::google::protobuf::testing::FloatWrapper* float_wrapper);

  // optional .google.protobuf.testing.Int64Wrapper int64_wrapper = 3;
  bool has_int64_wrapper() const;
  void clear_int64_wrapper();
  static const int kInt64WrapperFieldNumber = 3;
  const ::google::protobuf::testing::Int64Wrapper& int64_wrapper() const;
  ::google::protobuf::testing::Int64Wrapper* mutable_int64_wrapper();
  ::google::protobuf::testing::Int64Wrapper* release_int64_wrapper();
  void set_allocated_int64_wrapper(::google::protobuf::testing::Int64Wrapper* int64_wrapper);

  // optional .google.protobuf.testing.UInt64Wrapper uint64_wrapper = 4;
  bool has_uint64_wrapper() const;
  void clear_uint64_wrapper();
  static const int kUint64WrapperFieldNumber = 4;
  const ::google::protobuf::testing::UInt64Wrapper& uint64_wrapper() const;
  ::google::protobuf::testing::UInt64Wrapper* mutable_uint64_wrapper();
  ::google::protobuf::testing::UInt64Wrapper* release_uint64_wrapper();
  void set_allocated_uint64_wrapper(::google::protobuf::testing::UInt64Wrapper* uint64_wrapper);

  // optional .google.protobuf.testing.Int32Wrapper int32_wrapper = 5;
  bool has_int32_wrapper() const;
  void clear_int32_wrapper();
  static const int kInt32WrapperFieldNumber = 5;
  const ::google::protobuf::testing::Int32Wrapper& int32_wrapper() const;
  ::google::protobuf::testing::Int32Wrapper* mutable_int32_wrapper();
  ::google::protobuf::testing::Int32Wrapper* release_int32_wrapper();
  void set_allocated_int32_wrapper(::google::protobuf::testing::Int32Wrapper* int32_wrapper);

  // optional .google.protobuf.testing.UInt32Wrapper uint32_wrapper = 6;
  bool has_uint32_wrapper() const;
  void clear_uint32_wrapper();
  static const int kUint32WrapperFieldNumber = 6;
  const ::google::protobuf::testing::UInt32Wrapper& uint32_wrapper() const;
  ::google::protobuf::testing::UInt32Wrapper* mutable_uint32_wrapper();
  ::google::protobuf::testing::UInt32Wrapper* release_uint32_wrapper();
  void set_allocated_uint32_wrapper(::google::protobuf::testing::UInt32Wrapper* uint32_wrapper);

  // optional .google.protobuf.testing.BoolWrapper bool_wrapper = 7;
  bool has_bool_wrapper() const;
  void clear_bool_wrapper();
  static const int kBoolWrapperFieldNumber = 7;
  const ::google::protobuf::testing::BoolWrapper& bool_wrapper() const;
  ::google::protobuf::testing::BoolWrapper* mutable_bool_wrapper();
  ::google::protobuf::testing::BoolWrapper* release_bool_wrapper();
  void set_allocated_bool_wrapper(::google::protobuf::testing::BoolWrapper* bool_wrapper);

  // optional .google.protobuf.testing.StringWrapper string_wrapper = 8;
  bool has_string_wrapper() const;
  void clear_string_wrapper();
  static const int kStringWrapperFieldNumber = 8;
  const ::google::protobuf::testing::StringWrapper& string_wrapper() const;
  ::google::protobuf::testing::StringWrapper* mutable_string_wrapper();
  ::google::protobuf::testing::StringWrapper* release_string_wrapper();
  void set_allocated_string_wrapper(::google::protobuf::testing::StringWrapper* string_wrapper);

  // optional .google.protobuf.testing.BytesWrapper bytes_wrapper = 9;
  bool has_bytes_wrapper() const;
  void clear_bytes_wrapper();
  static const int kBytesWrapperFieldNumber = 9;
  const ::google::protobuf::testing::BytesWrapper& bytes_wrapper() const;
  ::google::protobuf::testing::BytesWrapper* mutable_bytes_wrapper();
  ::google::protobuf::testing::BytesWrapper* release_bytes_wrapper();
  void set_allocated_bytes_wrapper(::google::protobuf::testing::BytesWrapper* bytes_wrapper);

  // optional .google.protobuf.testing.DoubleWrapper double_wrapper_default = 10;
  bool has_double_wrapper_default() const;
  void clear_double_wrapper_default();
  static const int kDoubleWrapperDefaultFieldNumber = 10;
  const ::google::protobuf::testing::DoubleWrapper& double_wrapper_default() const;
  ::google::protobuf::testing::DoubleWrapper* mutable_double_wrapper_default();
  ::google::protobuf::testing::DoubleWrapper* release_double_wrapper_default();
  void set_allocated_double_wrapper_default(::google::protobuf::testing::DoubleWrapper* double_wrapper_default);

  // optional .google.protobuf.testing.FloatWrapper float_wrapper_default = 11;
  bool has_float_wrapper_default() const;
  void clear_float_wrapper_default();
  static const int kFloatWrapperDefaultFieldNumber = 11;
  const ::google::protobuf::testing::FloatWrapper& float_wrapper_default() const;
  ::google::protobuf::testing::FloatWrapper* mutable_float_wrapper_default();
  ::google::protobuf::testing::FloatWrapper* release_float_wrapper_default();
  void set_allocated_float_wrapper_default(::google::protobuf::testing::FloatWrapper* float_wrapper_default);

  // optional .google.protobuf.testing.Int64Wrapper int64_wrapper_default = 12;
  bool has_int64_wrapper_default() const;
  void clear_int64_wrapper_default();
  static const int kInt64WrapperDefaultFieldNumber = 12;
  const ::google::protobuf::testing::Int64Wrapper& int64_wrapper_default() const;
  ::google::protobuf::testing::Int64Wrapper* mutable_int64_wrapper_default();
  ::google::protobuf::testing::Int64Wrapper* release_int64_wrapper_default();
  void set_allocated_int64_wrapper_default(::google::protobuf::testing::Int64Wrapper* int64_wrapper_default);

  // optional .google.protobuf.testing.UInt64Wrapper uint64_wrapper_default = 13;
  bool has_uint64_wrapper_default() const;
  void clear_uint64_wrapper_default();
  static const int kUint64WrapperDefaultFieldNumber = 13;
  const ::google::protobuf::testing::UInt64Wrapper& uint64_wrapper_default() const;
  ::google::protobuf::testing::UInt64Wrapper* mutable_uint64_wrapper_default();
  ::google::protobuf::testing::UInt64Wrapper* release_uint64_wrapper_default();
  void set_allocated_uint64_wrapper_default(::google::protobuf::testing::UInt64Wrapper* uint64_wrapper_default);

  // optional .google.protobuf.testing.Int32Wrapper int32_wrapper_default = 14;
  bool has_int32_wrapper_default() const;
  void clear_int32_wrapper_default();
  static const int kInt32WrapperDefaultFieldNumber = 14;
  const ::google::protobuf::testing::Int32Wrapper& int32_wrapper_default() const;
  ::google::protobuf::testing::Int32Wrapper* mutable_int32_wrapper_default();
  ::google::protobuf::testing::Int32Wrapper* release_int32_wrapper_default();
  void set_allocated_int32_wrapper_default(::google::protobuf::testing::Int32Wrapper* int32_wrapper_default);

  // optional .google.protobuf.testing.UInt32Wrapper uint32_wrapper_default = 15;
  bool has_uint32_wrapper_default() const;
  void clear_uint32_wrapper_default();
  static const int kUint32WrapperDefaultFieldNumber = 15;
  const ::google::protobuf::testing::UInt32Wrapper& uint32_wrapper_default() const;
  ::google::protobuf::testing::UInt32Wrapper* mutable_uint32_wrapper_default();
  ::google::protobuf::testing::UInt32Wrapper* release_uint32_wrapper_default();
  void set_allocated_uint32_wrapper_default(::google::protobuf::testing::UInt32Wrapper* uint32_wrapper_default);

  // optional .google.protobuf.testing.BoolWrapper bool_wrapper_default = 16;
  bool has_bool_wrapper_default() const;
  void clear_bool_wrapper_default();
  static const int kBoolWrapperDefaultFieldNumber = 16;
  const ::google::protobuf::testing::BoolWrapper& bool_wrapper_default() const;
  ::google::protobuf::testing::BoolWrapper* mutable_bool_wrapper_default();
  ::google::protobuf::testing::BoolWrapper* release_bool_wrapper_default();
  void set_allocated_bool_wrapper_default(::google::protobuf::testing::BoolWrapper* bool_wrapper_default);

  // optional .google.protobuf.testing.StringWrapper string_wrapper_default = 17;
  bool has_string_wrapper_default() const;
  void clear_string_wrapper_default();
  static const int kStringWrapperDefaultFieldNumber = 17;
  const ::google::protobuf::testing::StringWrapper& string_wrapper_default() const;
  ::google::protobuf::testing::StringWrapper* mutable_string_wrapper_default();
  ::google::protobuf::testing::StringWrapper* release_string_wrapper_default();
  void set_allocated_string_wrapper_default(::google::protobuf::testing::StringWrapper* string_wrapper_default);

  // optional .google.protobuf.testing.BytesWrapper bytes_wrapper_default = 18;
  bool has_bytes_wrapper_default() const;
  void clear_bytes_wrapper_default();
  static const int kBytesWrapperDefaultFieldNumber = 18;
  const ::google::protobuf::testing::BytesWrapper& bytes_wrapper_default() const;
  ::google::protobuf::testing::BytesWrapper* mutable_bytes_wrapper_default();
  ::google::protobuf::testing::BytesWrapper* release_bytes_wrapper_default();
  void set_allocated_bytes_wrapper_default(::google::protobuf::testing::BytesWrapper* bytes_wrapper_default);

  // @@protoc_insertion_point(class_scope:google.protobuf.testing.WrappersTestCases)
 private:

  ::google::protobuf::internal::InternalMetadataWithArena _internal_metadata_;
  ::google::protobuf::testing::DoubleWrapper* double_wrapper_;
  ::google::protobuf::testing::FloatWrapper* float_wrapper_;
  ::google::protobuf::testing::Int64Wrapper* int64_wrapper_;
  ::google::protobuf::testing::UInt64Wrapper* uint64_wrapper_;
  ::google::protobuf::testing::Int32Wrapper* int32_wrapper_;
  ::google::protobuf::testing::UInt32Wrapper* uint32_wrapper_;
  ::google::protobuf::testing::BoolWrapper* bool_wrapper_;
  ::google::protobuf::testing::StringWrapper* string_wrapper_;
  ::google::protobuf::testing::BytesWrapper* bytes_wrapper_;
  ::google::protobuf::testing::DoubleWrapper* double_wrapper_default_;
  ::google::protobuf::testing::FloatWrapper* float_wrapper_default_;
  ::google::protobuf::testing::Int64Wrapper* int64_wrapper_default_;
  ::google::protobuf::testing::UInt64Wrapper* uint64_wrapper_default_;
  ::google::protobuf::testing::Int32Wrapper* int32_wrapper_default_;
  ::google::protobuf::testing::UInt32Wrapper* uint32_wrapper_default_;
  ::google::protobuf::testing::BoolWrapper* bool_wrapper_default_;
  ::google::protobuf::testing::StringWrapper* string_wrapper_default_;
  ::google::protobuf::testing::BytesWrapper* bytes_wrapper_default_;
  mutable int _cached_size_;
  friend void  protobuf_InitDefaults_google_2fprotobuf_2futil_2finternal_2ftestdata_2fwrappers_2eproto_impl();
  friend void  protobuf_AddDesc_google_2fprotobuf_2futil_2finternal_2ftestdata_2fwrappers_2eproto_impl();
  friend void protobuf_AssignDesc_google_2fprotobuf_2futil_2finternal_2ftestdata_2fwrappers_2eproto();
  friend void protobuf_ShutdownFile_google_2fprotobuf_2futil_2finternal_2ftestdata_2fwrappers_2eproto();

  void InitAsDefaultInstance();
};
extern ::google::protobuf::internal::ExplicitlyConstructed<WrappersTestCases> WrappersTestCases_default_instance_;

// -------------------------------------------------------------------

class DoubleWrapper : public ::google::protobuf::Message /* @@protoc_insertion_point(class_definition:google.protobuf.testing.DoubleWrapper) */ {
 public:
  DoubleWrapper();
  virtual ~DoubleWrapper();

  DoubleWrapper(const DoubleWrapper& from);

  inline DoubleWrapper& operator=(const DoubleWrapper& from) {
    CopyFrom(from);
    return *this;
  }

  static const ::google::protobuf::Descriptor* descriptor();
  static const DoubleWrapper& default_instance();

  static const DoubleWrapper* internal_default_instance();

  void Swap(DoubleWrapper* other);

  // implements Message ----------------------------------------------

  inline DoubleWrapper* New() const { return New(NULL); }

  DoubleWrapper* New(::google::protobuf::Arena* arena) const;
  void CopyFrom(const ::google::protobuf::Message& from);
  void MergeFrom(const ::google::protobuf::Message& from);
  void CopyFrom(const DoubleWrapper& from);
  void MergeFrom(const DoubleWrapper& from);
  void Clear();
  bool IsInitialized() const;

  size_t ByteSizeLong() const;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input);
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const;
  ::google::protobuf::uint8* InternalSerializeWithCachedSizesToArray(
      bool deterministic, ::google::protobuf::uint8* output) const;
  ::google::protobuf::uint8* SerializeWithCachedSizesToArray(::google::protobuf::uint8* output) const {
    return InternalSerializeWithCachedSizesToArray(false, output);
  }
  int GetCachedSize() const { return _cached_size_; }
  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const;
  void InternalSwap(DoubleWrapper* other);
  void UnsafeMergeFrom(const DoubleWrapper& from);
  private:
  inline ::google::protobuf::Arena* GetArenaNoVirtual() const {
    return _internal_metadata_.arena();
  }
  inline void* MaybeArenaPtr() const {
    return _internal_metadata_.raw_arena_ptr();
  }
  public:

  ::google::protobuf::Metadata GetMetadata() const;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // optional .google.protobuf.DoubleValue double = 1;
  bool has_double_() const;
  void clear_double_();
  static const int kDoubleFieldNumber = 1;
  const ::google::protobuf::DoubleValue& double_() const;
  ::google::protobuf::DoubleValue* mutable_double_();
  ::google::protobuf::DoubleValue* release_double_();
  void set_allocated_double_(::google::protobuf::DoubleValue* double_);

  // @@protoc_insertion_point(class_scope:google.protobuf.testing.DoubleWrapper)
 private:

  ::google::protobuf::internal::InternalMetadataWithArena _internal_metadata_;
  ::google::protobuf::DoubleValue* double__;
  mutable int _cached_size_;
  friend void  protobuf_InitDefaults_google_2fprotobuf_2futil_2finternal_2ftestdata_2fwrappers_2eproto_impl();
  friend void  protobuf_AddDesc_google_2fprotobuf_2futil_2finternal_2ftestdata_2fwrappers_2eproto_impl();
  friend void protobuf_AssignDesc_google_2fprotobuf_2futil_2finternal_2ftestdata_2fwrappers_2eproto();
  friend void protobuf_ShutdownFile_google_2fprotobuf_2futil_2finternal_2ftestdata_2fwrappers_2eproto();

  void InitAsDefaultInstance();
};
extern ::google::protobuf::internal::ExplicitlyConstructed<DoubleWrapper> DoubleWrapper_default_instance_;

// -------------------------------------------------------------------

class FloatWrapper : public ::google::protobuf::Message /* @@protoc_insertion_point(class_definition:google.protobuf.testing.FloatWrapper) */ {
 public:
  FloatWrapper();
  virtual ~FloatWrapper();

  FloatWrapper(const FloatWrapper& from);

  inline FloatWrapper& operator=(const FloatWrapper& from) {
    CopyFrom(from);
    return *this;
  }

  static const ::google::protobuf::Descriptor* descriptor();
  static const FloatWrapper& default_instance();

  static const FloatWrapper* internal_default_instance();

  void Swap(FloatWrapper* other);

  // implements Message ----------------------------------------------

  inline FloatWrapper* New() const { return New(NULL); }

  FloatWrapper* New(::google::protobuf::Arena* arena) const;
  void CopyFrom(const ::google::protobuf::Message& from);
  void MergeFrom(const ::google::protobuf::Message& from);
  void CopyFrom(const FloatWrapper& from);
  void MergeFrom(const FloatWrapper& from);
  void Clear();
  bool IsInitialized() const;

  size_t ByteSizeLong() const;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input);
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const;
  ::google::protobuf::uint8* InternalSerializeWithCachedSizesToArray(
      bool deterministic, ::google::protobuf::uint8* output) const;
  ::google::protobuf::uint8* SerializeWithCachedSizesToArray(::google::protobuf::uint8* output) const {
    return InternalSerializeWithCachedSizesToArray(false, output);
  }
  int GetCachedSize() const { return _cached_size_; }
  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const;
  void InternalSwap(FloatWrapper* other);
  void UnsafeMergeFrom(const FloatWrapper& from);
  private:
  inline ::google::protobuf::Arena* GetArenaNoVirtual() const {
    return _internal_metadata_.arena();
  }
  inline void* MaybeArenaPtr() const {
    return _internal_metadata_.raw_arena_ptr();
  }
  public:

  ::google::protobuf::Metadata GetMetadata() const;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // optional .google.protobuf.FloatValue float = 1;
  bool has_float_() const;
  void clear_float_();
  static const int kFloatFieldNumber = 1;
  const ::google::protobuf::FloatValue& float_() const;
  ::google::protobuf::FloatValue* mutable_float_();
  ::google::protobuf::FloatValue* release_float_();
  void set_allocated_float_(::google::protobuf::FloatValue* float_);

  // @@protoc_insertion_point(class_scope:google.protobuf.testing.FloatWrapper)
 private:

  ::google::protobuf::internal::InternalMetadataWithArena _internal_metadata_;
  ::google::protobuf::FloatValue* float__;
  mutable int _cached_size_;
  friend void  protobuf_InitDefaults_google_2fprotobuf_2futil_2finternal_2ftestdata_2fwrappers_2eproto_impl();
  friend void  protobuf_AddDesc_google_2fprotobuf_2futil_2finternal_2ftestdata_2fwrappers_2eproto_impl();
  friend void protobuf_AssignDesc_google_2fprotobuf_2futil_2finternal_2ftestdata_2fwrappers_2eproto();
  friend void protobuf_ShutdownFile_google_2fprotobuf_2futil_2finternal_2ftestdata_2fwrappers_2eproto();

  void InitAsDefaultInstance();
};
extern ::google::protobuf::internal::ExplicitlyConstructed<FloatWrapper> FloatWrapper_default_instance_;

// -------------------------------------------------------------------

class Int64Wrapper : public ::google::protobuf::Message /* @@protoc_insertion_point(class_definition:google.protobuf.testing.Int64Wrapper) */ {
 public:
  Int64Wrapper();
  virtual ~Int64Wrapper();

  Int64Wrapper(const Int64Wrapper& from);

  inline Int64Wrapper& operator=(const Int64Wrapper& from) {
    CopyFrom(from);
    return *this;
  }

  static const ::google::protobuf::Descriptor* descriptor();
  static const Int64Wrapper& default_instance();

  static const Int64Wrapper* internal_default_instance();

  void Swap(Int64Wrapper* other);

  // implements Message ----------------------------------------------

  inline Int64Wrapper* New() const { return New(NULL); }

  Int64Wrapper* New(::google::protobuf::Arena* arena) const;
  void CopyFrom(const ::google::protobuf::Message& from);
  void MergeFrom(const ::google::protobuf::Message& from);
  void CopyFrom(const Int64Wrapper& from);
  void MergeFrom(const Int64Wrapper& from);
  void Clear();
  bool IsInitialized() const;

  size_t ByteSizeLong() const;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input);
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const;
  ::google::protobuf::uint8* InternalSerializeWithCachedSizesToArray(
      bool deterministic, ::google::protobuf::uint8* output) const;
  ::google::protobuf::uint8* SerializeWithCachedSizesToArray(::google::protobuf::uint8* output) const {
    return InternalSerializeWithCachedSizesToArray(false, output);
  }
  int GetCachedSize() const { return _cached_size_; }
  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const;
  void InternalSwap(Int64Wrapper* other);
  void UnsafeMergeFrom(const Int64Wrapper& from);
  private:
  inline ::google::protobuf::Arena* GetArenaNoVirtual() const {
    return _internal_metadata_.arena();
  }
  inline void* MaybeArenaPtr() const {
    return _internal_metadata_.raw_arena_ptr();
  }
  public:

  ::google::protobuf::Metadata GetMetadata() const;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // optional .google.protobuf.Int64Value int64 = 1;
  bool has_int64() const;
  void clear_int64();
  static const int kInt64FieldNumber = 1;
  const ::google::protobuf::Int64Value& int64() const;
  ::google::protobuf::Int64Value* mutable_int64();
  ::google::protobuf::Int64Value* release_int64();
  void set_allocated_int64(::google::protobuf::Int64Value* int64);

  // @@protoc_insertion_point(class_scope:google.protobuf.testing.Int64Wrapper)
 private:

  ::google::protobuf::internal::InternalMetadataWithArena _internal_metadata_;
  ::google::protobuf::Int64Value* int64_;
  mutable int _cached_size_;
  friend void  protobuf_InitDefaults_google_2fprotobuf_2futil_2finternal_2ftestdata_2fwrappers_2eproto_impl();
  friend void  protobuf_AddDesc_google_2fprotobuf_2futil_2finternal_2ftestdata_2fwrappers_2eproto_impl();
  friend void protobuf_AssignDesc_google_2fprotobuf_2futil_2finternal_2ftestdata_2fwrappers_2eproto();
  friend void protobuf_ShutdownFile_google_2fprotobuf_2futil_2finternal_2ftestdata_2fwrappers_2eproto();

  void InitAsDefaultInstance();
};
extern ::google::protobuf::internal::ExplicitlyConstructed<Int64Wrapper> Int64Wrapper_default_instance_;

// -------------------------------------------------------------------

class UInt64Wrapper : public ::google::protobuf::Message /* @@protoc_insertion_point(class_definition:google.protobuf.testing.UInt64Wrapper) */ {
 public:
  UInt64Wrapper();
  virtual ~UInt64Wrapper();

  UInt64Wrapper(const UInt64Wrapper& from);

  inline UInt64Wrapper& operator=(const UInt64Wrapper& from) {
    CopyFrom(from);
    return *this;
  }

  static const ::google::protobuf::Descriptor* descriptor();
  static const UInt64Wrapper& default_instance();

  static const UInt64Wrapper* internal_default_instance();

  void Swap(UInt64Wrapper* other);

  // implements Message ----------------------------------------------

  inline UInt64Wrapper* New() const { return New(NULL); }

  UInt64Wrapper* New(::google::protobuf::Arena* arena) const;
  void CopyFrom(const ::google::protobuf::Message& from);
  void MergeFrom(const ::google::protobuf::Message& from);
  void CopyFrom(const UInt64Wrapper& from);
  void MergeFrom(const UInt64Wrapper& from);
  void Clear();
  bool IsInitialized() const;

  size_t ByteSizeLong() const;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input);
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const;
  ::google::protobuf::uint8* InternalSerializeWithCachedSizesToArray(
      bool deterministic, ::google::protobuf::uint8* output) const;
  ::google::protobuf::uint8* SerializeWithCachedSizesToArray(::google::protobuf::uint8* output) const {
    return InternalSerializeWithCachedSizesToArray(false, output);
  }
  int GetCachedSize() const { return _cached_size_; }
  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const;
  void InternalSwap(UInt64Wrapper* other);
  void UnsafeMergeFrom(const UInt64Wrapper& from);
  private:
  inline ::google::protobuf::Arena* GetArenaNoVirtual() const {
    return _internal_metadata_.arena();
  }
  inline void* MaybeArenaPtr() const {
    return _internal_metadata_.raw_arena_ptr();
  }
  public:

  ::google::protobuf::Metadata GetMetadata() const;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // optional .google.protobuf.UInt64Value uint64 = 1;
  bool has_uint64() const;
  void clear_uint64();
  static const int kUint64FieldNumber = 1;
  const ::google::protobuf::UInt64Value& uint64() const;
  ::google::protobuf::UInt64Value* mutable_uint64();
  ::google::protobuf::UInt64Value* release_uint64();
  void set_allocated_uint64(::google::protobuf::UInt64Value* uint64);

  // @@protoc_insertion_point(class_scope:google.protobuf.testing.UInt64Wrapper)
 private:

  ::google::protobuf::internal::InternalMetadataWithArena _internal_metadata_;
  ::google::protobuf::UInt64Value* uint64_;
  mutable int _cached_size_;
  friend void  protobuf_InitDefaults_google_2fprotobuf_2futil_2finternal_2ftestdata_2fwrappers_2eproto_impl();
  friend void  protobuf_AddDesc_google_2fprotobuf_2futil_2finternal_2ftestdata_2fwrappers_2eproto_impl();
  friend void protobuf_AssignDesc_google_2fprotobuf_2futil_2finternal_2ftestdata_2fwrappers_2eproto();
  friend void protobuf_ShutdownFile_google_2fprotobuf_2futil_2finternal_2ftestdata_2fwrappers_2eproto();

  void InitAsDefaultInstance();
};
extern ::google::protobuf::internal::ExplicitlyConstructed<UInt64Wrapper> UInt64Wrapper_default_instance_;

// -------------------------------------------------------------------

class Int32Wrapper : public ::google::protobuf::Message /* @@protoc_insertion_point(class_definition:google.protobuf.testing.Int32Wrapper) */ {
 public:
  Int32Wrapper();
  virtual ~Int32Wrapper();

  Int32Wrapper(const Int32Wrapper& from);

  inline Int32Wrapper& operator=(const Int32Wrapper& from) {
    CopyFrom(from);
    return *this;
  }

  static const ::google::protobuf::Descriptor* descriptor();
  static const Int32Wrapper& default_instance();

  static const Int32Wrapper* internal_default_instance();

  void Swap(Int32Wrapper* other);

  // implements Message ----------------------------------------------

  inline Int32Wrapper* New() const { return New(NULL); }

  Int32Wrapper* New(::google::protobuf::Arena* arena) const;
  void CopyFrom(const ::google::protobuf::Message& from);
  void MergeFrom(const ::google::protobuf::Message& from);
  void CopyFrom(const Int32Wrapper& from);
  void MergeFrom(const Int32Wrapper& from);
  void Clear();
  bool IsInitialized() const;

  size_t ByteSizeLong() const;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input);
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const;
  ::google::protobuf::uint8* InternalSerializeWithCachedSizesToArray(
      bool deterministic, ::google::protobuf::uint8* output) const;
  ::google::protobuf::uint8* SerializeWithCachedSizesToArray(::google::protobuf::uint8* output) const {
    return InternalSerializeWithCachedSizesToArray(false, output);
  }
  int GetCachedSize() const { return _cached_size_; }
  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const;
  void InternalSwap(Int32Wrapper* other);
  void UnsafeMergeFrom(const Int32Wrapper& from);
  private:
  inline ::google::protobuf::Arena* GetArenaNoVirtual() const {
    return _internal_metadata_.arena();
  }
  inline void* MaybeArenaPtr() const {
    return _internal_metadata_.raw_arena_ptr();
  }
  public:

  ::google::protobuf::Metadata GetMetadata() const;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // optional .google.protobuf.Int32Value int32 = 1;
  bool has_int32() const;
  void clear_int32();
  static const int kInt32FieldNumber = 1;
  const ::google::protobuf::Int32Value& int32() const;
  ::google::protobuf::Int32Value* mutable_int32();
  ::google::protobuf::Int32Value* release_int32();
  void set_allocated_int32(::google::protobuf::Int32Value* int32);

  // @@protoc_insertion_point(class_scope:google.protobuf.testing.Int32Wrapper)
 private:

  ::google::protobuf::internal::InternalMetadataWithArena _internal_metadata_;
  ::google::protobuf::Int32Value* int32_;
  mutable int _cached_size_;
  friend void  protobuf_InitDefaults_google_2fprotobuf_2futil_2finternal_2ftestdata_2fwrappers_2eproto_impl();
  friend void  protobuf_AddDesc_google_2fprotobuf_2futil_2finternal_2ftestdata_2fwrappers_2eproto_impl();
  friend void protobuf_AssignDesc_google_2fprotobuf_2futil_2finternal_2ftestdata_2fwrappers_2eproto();
  friend void protobuf_ShutdownFile_google_2fprotobuf_2futil_2finternal_2ftestdata_2fwrappers_2eproto();

  void InitAsDefaultInstance();
};
extern ::google::protobuf::internal::ExplicitlyConstructed<Int32Wrapper> Int32Wrapper_default_instance_;

// -------------------------------------------------------------------

class UInt32Wrapper : public ::google::protobuf::Message /* @@protoc_insertion_point(class_definition:google.protobuf.testing.UInt32Wrapper) */ {
 public:
  UInt32Wrapper();
  virtual ~UInt32Wrapper();

  UInt32Wrapper(const UInt32Wrapper& from);

  inline UInt32Wrapper& operator=(const UInt32Wrapper& from) {
    CopyFrom(from);
    return *this;
  }

  static const ::google::protobuf::Descriptor* descriptor();
  static const UInt32Wrapper& default_instance();

  static const UInt32Wrapper* internal_default_instance();

  void Swap(UInt32Wrapper* other);

  // implements Message ----------------------------------------------

  inline UInt32Wrapper* New() const { return New(NULL); }

  UInt32Wrapper* New(::google::protobuf::Arena* arena) const;
  void CopyFrom(const ::google::protobuf::Message& from);
  void MergeFrom(const ::google::protobuf::Message& from);
  void CopyFrom(const UInt32Wrapper& from);
  void MergeFrom(const UInt32Wrapper& from);
  void Clear();
  bool IsInitialized() const;

  size_t ByteSizeLong() const;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input);
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const;
  ::google::protobuf::uint8* InternalSerializeWithCachedSizesToArray(
      bool deterministic, ::google::protobuf::uint8* output) const;
  ::google::protobuf::uint8* SerializeWithCachedSizesToArray(::google::protobuf::uint8* output) const {
    return InternalSerializeWithCachedSizesToArray(false, output);
  }
  int GetCachedSize() const { return _cached_size_; }
  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const;
  void InternalSwap(UInt32Wrapper* other);
  void UnsafeMergeFrom(const UInt32Wrapper& from);
  private:
  inline ::google::protobuf::Arena* GetArenaNoVirtual() const {
    return _internal_metadata_.arena();
  }
  inline void* MaybeArenaPtr() const {
    return _internal_metadata_.raw_arena_ptr();
  }
  public:

  ::google::protobuf::Metadata GetMetadata() const;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // optional .google.protobuf.UInt32Value uint32 = 1;
  bool has_uint32() const;
  void clear_uint32();
  static const int kUint32FieldNumber = 1;
  const ::google::protobuf::UInt32Value& uint32() const;
  ::google::protobuf::UInt32Value* mutable_uint32();
  ::google::protobuf::UInt32Value* release_uint32();
  void set_allocated_uint32(::google::protobuf::UInt32Value* uint32);

  // @@protoc_insertion_point(class_scope:google.protobuf.testing.UInt32Wrapper)
 private:

  ::google::protobuf::internal::InternalMetadataWithArena _internal_metadata_;
  ::google::protobuf::UInt32Value* uint32_;
  mutable int _cached_size_;
  friend void  protobuf_InitDefaults_google_2fprotobuf_2futil_2finternal_2ftestdata_2fwrappers_2eproto_impl();
  friend void  protobuf_AddDesc_google_2fprotobuf_2futil_2finternal_2ftestdata_2fwrappers_2eproto_impl();
  friend void protobuf_AssignDesc_google_2fprotobuf_2futil_2finternal_2ftestdata_2fwrappers_2eproto();
  friend void protobuf_ShutdownFile_google_2fprotobuf_2futil_2finternal_2ftestdata_2fwrappers_2eproto();

  void InitAsDefaultInstance();
};
extern ::google::protobuf::internal::ExplicitlyConstructed<UInt32Wrapper> UInt32Wrapper_default_instance_;

// -------------------------------------------------------------------

class BoolWrapper : public ::google::protobuf::Message /* @@protoc_insertion_point(class_definition:google.protobuf.testing.BoolWrapper) */ {
 public:
  BoolWrapper();
  virtual ~BoolWrapper();

  BoolWrapper(const BoolWrapper& from);

  inline BoolWrapper& operator=(const BoolWrapper& from) {
    CopyFrom(from);
    return *this;
  }

  static const ::google::protobuf::Descriptor* descriptor();
  static const BoolWrapper& default_instance();

  static const BoolWrapper* internal_default_instance();

  void Swap(BoolWrapper* other);

  // implements Message ----------------------------------------------

  inline BoolWrapper* New() const { return New(NULL); }

  BoolWrapper* New(::google::protobuf::Arena* arena) const;
  void CopyFrom(const ::google::protobuf::Message& from);
  void MergeFrom(const ::google::protobuf::Message& from);
  void CopyFrom(const BoolWrapper& from);
  void MergeFrom(const BoolWrapper& from);
  void Clear();
  bool IsInitialized() const;

  size_t ByteSizeLong() const;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input);
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const;
  ::google::protobuf::uint8* InternalSerializeWithCachedSizesToArray(
      bool deterministic, ::google::protobuf::uint8* output) const;
  ::google::protobuf::uint8* SerializeWithCachedSizesToArray(::google::protobuf::uint8* output) const {
    return InternalSerializeWithCachedSizesToArray(false, output);
  }
  int GetCachedSize() const { return _cached_size_; }
  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const;
  void InternalSwap(BoolWrapper* other);
  void UnsafeMergeFrom(const BoolWrapper& from);
  private:
  inline ::google::protobuf::Arena* GetArenaNoVirtual() const {
    return _internal_metadata_.arena();
  }
  inline void* MaybeArenaPtr() const {
    return _internal_metadata_.raw_arena_ptr();
  }
  public:

  ::google::protobuf::Metadata GetMetadata() const;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // optional .google.protobuf.BoolValue bool = 1;
  bool has_bool_() const;
  void clear_bool_();
  static const int kBoolFieldNumber = 1;
  const ::google::protobuf::BoolValue& bool_() const;
  ::google::protobuf::BoolValue* mutable_bool_();
  ::google::protobuf::BoolValue* release_bool_();
  void set_allocated_bool_(::google::protobuf::BoolValue* bool_);

  // @@protoc_insertion_point(class_scope:google.protobuf.testing.BoolWrapper)
 private:

  ::google::protobuf::internal::InternalMetadataWithArena _internal_metadata_;
  ::google::protobuf::BoolValue* bool__;
  mutable int _cached_size_;
  friend void  protobuf_InitDefaults_google_2fprotobuf_2futil_2finternal_2ftestdata_2fwrappers_2eproto_impl();
  friend void  protobuf_AddDesc_google_2fprotobuf_2futil_2finternal_2ftestdata_2fwrappers_2eproto_impl();
  friend void protobuf_AssignDesc_google_2fprotobuf_2futil_2finternal_2ftestdata_2fwrappers_2eproto();
  friend void protobuf_ShutdownFile_google_2fprotobuf_2futil_2finternal_2ftestdata_2fwrappers_2eproto();

  void InitAsDefaultInstance();
};
extern ::google::protobuf::internal::ExplicitlyConstructed<BoolWrapper> BoolWrapper_default_instance_;

// -------------------------------------------------------------------

class StringWrapper : public ::google::protobuf::Message /* @@protoc_insertion_point(class_definition:google.protobuf.testing.StringWrapper) */ {
 public:
  StringWrapper();
  virtual ~StringWrapper();

  StringWrapper(const StringWrapper& from);

  inline StringWrapper& operator=(const StringWrapper& from) {
    CopyFrom(from);
    return *this;
  }

  static const ::google::protobuf::Descriptor* descriptor();
  static const StringWrapper& default_instance();

  static const StringWrapper* internal_default_instance();

  void Swap(StringWrapper* other);

  // implements Message ----------------------------------------------

  inline StringWrapper* New() const { return New(NULL); }

  StringWrapper* New(::google::protobuf::Arena* arena) const;
  void CopyFrom(const ::google::protobuf::Message& from);
  void MergeFrom(const ::google::protobuf::Message& from);
  void CopyFrom(const StringWrapper& from);
  void MergeFrom(const StringWrapper& from);
  void Clear();
  bool IsInitialized() const;

  size_t ByteSizeLong() const;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input);
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const;
  ::google::protobuf::uint8* InternalSerializeWithCachedSizesToArray(
      bool deterministic, ::google::protobuf::uint8* output) const;
  ::google::protobuf::uint8* SerializeWithCachedSizesToArray(::google::protobuf::uint8* output) const {
    return InternalSerializeWithCachedSizesToArray(false, output);
  }
  int GetCachedSize() const { return _cached_size_; }
  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const;
  void InternalSwap(StringWrapper* other);
  void UnsafeMergeFrom(const StringWrapper& from);
  private:
  inline ::google::protobuf::Arena* GetArenaNoVirtual() const {
    return _internal_metadata_.arena();
  }
  inline void* MaybeArenaPtr() const {
    return _internal_metadata_.raw_arena_ptr();
  }
  public:

  ::google::protobuf::Metadata GetMetadata() const;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // optional .google.protobuf.StringValue string = 1;
  bool has_string() const;
  void clear_string();
  static const int kStringFieldNumber = 1;
  const ::google::protobuf::StringValue& string() const;
  ::google::protobuf::StringValue* mutable_string();
  ::google::protobuf::StringValue* release_string();
  void set_allocated_string(::google::protobuf::StringValue* string);

  // @@protoc_insertion_point(class_scope:google.protobuf.testing.StringWrapper)
 private:

  ::google::protobuf::internal::InternalMetadataWithArena _internal_metadata_;
  ::google::protobuf::StringValue* string_;
  mutable int _cached_size_;
  friend void  protobuf_InitDefaults_google_2fprotobuf_2futil_2finternal_2ftestdata_2fwrappers_2eproto_impl();
  friend void  protobuf_AddDesc_google_2fprotobuf_2futil_2finternal_2ftestdata_2fwrappers_2eproto_impl();
  friend void protobuf_AssignDesc_google_2fprotobuf_2futil_2finternal_2ftestdata_2fwrappers_2eproto();
  friend void protobuf_ShutdownFile_google_2fprotobuf_2futil_2finternal_2ftestdata_2fwrappers_2eproto();

  void InitAsDefaultInstance();
};
extern ::google::protobuf::internal::ExplicitlyConstructed<StringWrapper> StringWrapper_default_instance_;

// -------------------------------------------------------------------

class BytesWrapper : public ::google::protobuf::Message /* @@protoc_insertion_point(class_definition:google.protobuf.testing.BytesWrapper) */ {
 public:
  BytesWrapper();
  virtual ~BytesWrapper();

  BytesWrapper(const BytesWrapper& from);

  inline BytesWrapper& operator=(const BytesWrapper& from) {
    CopyFrom(from);
    return *this;
  }

  static const ::google::protobuf::Descriptor* descriptor();
  static const BytesWrapper& default_instance();

  static const BytesWrapper* internal_default_instance();

  void Swap(BytesWrapper* other);

  // implements Message ----------------------------------------------

  inline BytesWrapper* New() const { return New(NULL); }

  BytesWrapper* New(::google::protobuf::Arena* arena) const;
  void CopyFrom(const ::google::protobuf::Message& from);
  void MergeFrom(const ::google::protobuf::Message& from);
  void CopyFrom(const BytesWrapper& from);
  void MergeFrom(const BytesWrapper& from);
  void Clear();
  bool IsInitialized() const;

  size_t ByteSizeLong() const;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input);
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const;
  ::google::protobuf::uint8* InternalSerializeWithCachedSizesToArray(
      bool deterministic, ::google::protobuf::uint8* output) const;
  ::google::protobuf::uint8* SerializeWithCachedSizesToArray(::google::protobuf::uint8* output) const {
    return InternalSerializeWithCachedSizesToArray(false, output);
  }
  int GetCachedSize() const { return _cached_size_; }
  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const;
  void InternalSwap(BytesWrapper* other);
  void UnsafeMergeFrom(const BytesWrapper& from);
  private:
  inline ::google::protobuf::Arena* GetArenaNoVirtual() const {
    return _internal_metadata_.arena();
  }
  inline void* MaybeArenaPtr() const {
    return _internal_metadata_.raw_arena_ptr();
  }
  public:

  ::google::protobuf::Metadata GetMetadata() const;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // optional .google.protobuf.BytesValue bytes = 1;
  bool has_bytes() const;
  void clear_bytes();
  static const int kBytesFieldNumber = 1;
  const ::google::protobuf::BytesValue& bytes() const;
  ::google::protobuf::BytesValue* mutable_bytes();
  ::google::protobuf::BytesValue* release_bytes();
  void set_allocated_bytes(::google::protobuf::BytesValue* bytes);

  // @@protoc_insertion_point(class_scope:google.protobuf.testing.BytesWrapper)
 private:

  ::google::protobuf::internal::InternalMetadataWithArena _internal_metadata_;
  ::google::protobuf::BytesValue* bytes_;
  mutable int _cached_size_;
  friend void  protobuf_InitDefaults_google_2fprotobuf_2futil_2finternal_2ftestdata_2fwrappers_2eproto_impl();
  friend void  protobuf_AddDesc_google_2fprotobuf_2futil_2finternal_2ftestdata_2fwrappers_2eproto_impl();
  friend void protobuf_AssignDesc_google_2fprotobuf_2futil_2finternal_2ftestdata_2fwrappers_2eproto();
  friend void protobuf_ShutdownFile_google_2fprotobuf_2futil_2finternal_2ftestdata_2fwrappers_2eproto();

  void InitAsDefaultInstance();
};
extern ::google::protobuf::internal::ExplicitlyConstructed<BytesWrapper> BytesWrapper_default_instance_;

// ===================================================================


// ===================================================================

#if !PROTOBUF_INLINE_NOT_IN_HEADERS
// WrappersTestCases

// optional .google.protobuf.testing.DoubleWrapper double_wrapper = 1;
inline bool WrappersTestCases::has_double_wrapper() const {
  return this != internal_default_instance() && double_wrapper_ != NULL;
}
inline void WrappersTestCases::clear_double_wrapper() {
  if (GetArenaNoVirtual() == NULL && double_wrapper_ != NULL) delete double_wrapper_;
  double_wrapper_ = NULL;
}
inline const ::google::protobuf::testing::DoubleWrapper& WrappersTestCases::double_wrapper() const {
  // @@protoc_insertion_point(field_get:google.protobuf.testing.WrappersTestCases.double_wrapper)
  return double_wrapper_ != NULL ? *double_wrapper_
                         : *::google::protobuf::testing::DoubleWrapper::internal_default_instance();
}
inline ::google::protobuf::testing::DoubleWrapper* WrappersTestCases::mutable_double_wrapper() {
  
  if (double_wrapper_ == NULL) {
    double_wrapper_ = new ::google::protobuf::testing::DoubleWrapper;
  }
  // @@protoc_insertion_point(field_mutable:google.protobuf.testing.WrappersTestCases.double_wrapper)
  return double_wrapper_;
}
inline ::google::protobuf::testing::DoubleWrapper* WrappersTestCases::release_double_wrapper() {
  // @@protoc_insertion_point(field_release:google.protobuf.testing.WrappersTestCases.double_wrapper)
  
  ::google::protobuf::testing::DoubleWrapper* temp = double_wrapper_;
  double_wrapper_ = NULL;
  return temp;
}
inline void WrappersTestCases::set_allocated_double_wrapper(::google::protobuf::testing::DoubleWrapper* double_wrapper) {
  delete double_wrapper_;
  double_wrapper_ = double_wrapper;
  if (double_wrapper) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_set_allocated:google.protobuf.testing.WrappersTestCases.double_wrapper)
}

// optional .google.protobuf.testing.FloatWrapper float_wrapper = 2;
inline bool WrappersTestCases::has_float_wrapper() const {
  return this != internal_default_instance() && float_wrapper_ != NULL;
}
inline void WrappersTestCases::clear_float_wrapper() {
  if (GetArenaNoVirtual() == NULL && float_wrapper_ != NULL) delete float_wrapper_;
  float_wrapper_ = NULL;
}
inline const ::google::protobuf::testing::FloatWrapper& WrappersTestCases::float_wrapper() const {
  // @@protoc_insertion_point(field_get:google.protobuf.testing.WrappersTestCases.float_wrapper)
  return float_wrapper_ != NULL ? *float_wrapper_
                         : *::google::protobuf::testing::FloatWrapper::internal_default_instance();
}
inline ::google::protobuf::testing::FloatWrapper* WrappersTestCases::mutable_float_wrapper() {
  
  if (float_wrapper_ == NULL) {
    float_wrapper_ = new ::google::protobuf::testing::FloatWrapper;
  }
  // @@protoc_insertion_point(field_mutable:google.protobuf.testing.WrappersTestCases.float_wrapper)
  return float_wrapper_;
}
inline ::google::protobuf::testing::FloatWrapper* WrappersTestCases::release_float_wrapper() {
  // @@protoc_insertion_point(field_release:google.protobuf.testing.WrappersTestCases.float_wrapper)
  
  ::google::protobuf::testing::FloatWrapper* temp = float_wrapper_;
  float_wrapper_ = NULL;
  return temp;
}
inline void WrappersTestCases::set_allocated_float_wrapper(::google::protobuf::testing::FloatWrapper* float_wrapper) {
  delete float_wrapper_;
  float_wrapper_ = float_wrapper;
  if (float_wrapper) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_set_allocated:google.protobuf.testing.WrappersTestCases.float_wrapper)
}

// optional .google.protobuf.testing.Int64Wrapper int64_wrapper = 3;
inline bool WrappersTestCases::has_int64_wrapper() const {
  return this != internal_default_instance() && int64_wrapper_ != NULL;
}
inline void WrappersTestCases::clear_int64_wrapper() {
  if (GetArenaNoVirtual() == NULL && int64_wrapper_ != NULL) delete int64_wrapper_;
  int64_wrapper_ = NULL;
}
inline const ::google::protobuf::testing::Int64Wrapper& WrappersTestCases::int64_wrapper() const {
  // @@protoc_insertion_point(field_get:google.protobuf.testing.WrappersTestCases.int64_wrapper)
  return int64_wrapper_ != NULL ? *int64_wrapper_
                         : *::google::protobuf::testing::Int64Wrapper::internal_default_instance();
}
inline ::google::protobuf::testing::Int64Wrapper* WrappersTestCases::mutable_int64_wrapper() {
  
  if (int64_wrapper_ == NULL) {
    int64_wrapper_ = new ::google::protobuf::testing::Int64Wrapper;
  }
  // @@protoc_insertion_point(field_mutable:google.protobuf.testing.WrappersTestCases.int64_wrapper)
  return int64_wrapper_;
}
inline ::google::protobuf::testing::Int64Wrapper* WrappersTestCases::release_int64_wrapper() {
  // @@protoc_insertion_point(field_release:google.protobuf.testing.WrappersTestCases.int64_wrapper)
  
  ::google::protobuf::testing::Int64Wrapper* temp = int64_wrapper_;
  int64_wrapper_ = NULL;
  return temp;
}
inline void WrappersTestCases::set_allocated_int64_wrapper(::google::protobuf::testing::Int64Wrapper* int64_wrapper) {
  delete int64_wrapper_;
  int64_wrapper_ = int64_wrapper;
  if (int64_wrapper) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_set_allocated:google.protobuf.testing.WrappersTestCases.int64_wrapper)
}

// optional .google.protobuf.testing.UInt64Wrapper uint64_wrapper = 4;
inline bool WrappersTestCases::has_uint64_wrapper() const {
  return this != internal_default_instance() && uint64_wrapper_ != NULL;
}
inline void WrappersTestCases::clear_uint64_wrapper() {
  if (GetArenaNoVirtual() == NULL && uint64_wrapper_ != NULL) delete uint64_wrapper_;
  uint64_wrapper_ = NULL;
}
inline const ::google::protobuf::testing::UInt64Wrapper& WrappersTestCases::uint64_wrapper() const {
  // @@protoc_insertion_point(field_get:google.protobuf.testing.WrappersTestCases.uint64_wrapper)
  return uint64_wrapper_ != NULL ? *uint64_wrapper_
                         : *::google::protobuf::testing::UInt64Wrapper::internal_default_instance();
}
inline ::google::protobuf::testing::UInt64Wrapper* WrappersTestCases::mutable_uint64_wrapper() {
  
  if (uint64_wrapper_ == NULL) {
    uint64_wrapper_ = new ::google::protobuf::testing::UInt64Wrapper;
  }
  // @@protoc_insertion_point(field_mutable:google.protobuf.testing.WrappersTestCases.uint64_wrapper)
  return uint64_wrapper_;
}
inline ::google::protobuf::testing::UInt64Wrapper* WrappersTestCases::release_uint64_wrapper() {
  // @@protoc_insertion_point(field_release:google.protobuf.testing.WrappersTestCases.uint64_wrapper)
  
  ::google::protobuf::testing::UInt64Wrapper* temp = uint64_wrapper_;
  uint64_wrapper_ = NULL;
  return temp;
}
inline void WrappersTestCases::set_allocated_uint64_wrapper(::google::protobuf::testing::UInt64Wrapper* uint64_wrapper) {
  delete uint64_wrapper_;
  uint64_wrapper_ = uint64_wrapper;
  if (uint64_wrapper) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_set_allocated:google.protobuf.testing.WrappersTestCases.uint64_wrapper)
}

// optional .google.protobuf.testing.Int32Wrapper int32_wrapper = 5;
inline bool WrappersTestCases::has_int32_wrapper() const {
  return this != internal_default_instance() && int32_wrapper_ != NULL;
}
inline void WrappersTestCases::clear_int32_wrapper() {
  if (GetArenaNoVirtual() == NULL && int32_wrapper_ != NULL) delete int32_wrapper_;
  int32_wrapper_ = NULL;
}
inline const ::google::protobuf::testing::Int32Wrapper& WrappersTestCases::int32_wrapper() const {
  // @@protoc_insertion_point(field_get:google.protobuf.testing.WrappersTestCases.int32_wrapper)
  return int32_wrapper_ != NULL ? *int32_wrapper_
                         : *::google::protobuf::testing::Int32Wrapper::internal_default_instance();
}
inline ::google::protobuf::testing::Int32Wrapper* WrappersTestCases::mutable_int32_wrapper() {
  
  if (int32_wrapper_ == NULL) {
    int32_wrapper_ = new ::google::protobuf::testing::Int32Wrapper;
  }
  // @@protoc_insertion_point(field_mutable:google.protobuf.testing.WrappersTestCases.int32_wrapper)
  return int32_wrapper_;
}
inline ::google::protobuf::testing::Int32Wrapper* WrappersTestCases::release_int32_wrapper() {
  // @@protoc_insertion_point(field_release:google.protobuf.testing.WrappersTestCases.int32_wrapper)
  
  ::google::protobuf::testing::Int32Wrapper* temp = int32_wrapper_;
  int32_wrapper_ = NULL;
  return temp;
}
inline void WrappersTestCases::set_allocated_int32_wrapper(::google::protobuf::testing::Int32Wrapper* int32_wrapper) {
  delete int32_wrapper_;
  int32_wrapper_ = int32_wrapper;
  if (int32_wrapper) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_set_allocated:google.protobuf.testing.WrappersTestCases.int32_wrapper)
}

// optional .google.protobuf.testing.UInt32Wrapper uint32_wrapper = 6;
inline bool WrappersTestCases::has_uint32_wrapper() const {
  return this != internal_default_instance() && uint32_wrapper_ != NULL;
}
inline void WrappersTestCases::clear_uint32_wrapper() {
  if (GetArenaNoVirtual() == NULL && uint32_wrapper_ != NULL) delete uint32_wrapper_;
  uint32_wrapper_ = NULL;
}
inline const ::google::protobuf::testing::UInt32Wrapper& WrappersTestCases::uint32_wrapper() const {
  // @@protoc_insertion_point(field_get:google.protobuf.testing.WrappersTestCases.uint32_wrapper)
  return uint32_wrapper_ != NULL ? *uint32_wrapper_
                         : *::google::protobuf::testing::UInt32Wrapper::internal_default_instance();
}
inline ::google::protobuf::testing::UInt32Wrapper* WrappersTestCases::mutable_uint32_wrapper() {
  
  if (uint32_wrapper_ == NULL) {
    uint32_wrapper_ = new ::google::protobuf::testing::UInt32Wrapper;
  }
  // @@protoc_insertion_point(field_mutable:google.protobuf.testing.WrappersTestCases.uint32_wrapper)
  return uint32_wrapper_;
}
inline ::google::protobuf::testing::UInt32Wrapper* WrappersTestCases::release_uint32_wrapper() {
  // @@protoc_insertion_point(field_release:google.protobuf.testing.WrappersTestCases.uint32_wrapper)
  
  ::google::protobuf::testing::UInt32Wrapper* temp = uint32_wrapper_;
  uint32_wrapper_ = NULL;
  return temp;
}
inline void WrappersTestCases::set_allocated_uint32_wrapper(::google::protobuf::testing::UInt32Wrapper* uint32_wrapper) {
  delete uint32_wrapper_;
  uint32_wrapper_ = uint32_wrapper;
  if (uint32_wrapper) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_set_allocated:google.protobuf.testing.WrappersTestCases.uint32_wrapper)
}

// optional .google.protobuf.testing.BoolWrapper bool_wrapper = 7;
inline bool WrappersTestCases::has_bool_wrapper() const {
  return this != internal_default_instance() && bool_wrapper_ != NULL;
}
inline void WrappersTestCases::clear_bool_wrapper() {
  if (GetArenaNoVirtual() == NULL && bool_wrapper_ != NULL) delete bool_wrapper_;
  bool_wrapper_ = NULL;
}
inline const ::google::protobuf::testing::BoolWrapper& WrappersTestCases::bool_wrapper() const {
  // @@protoc_insertion_point(field_get:google.protobuf.testing.WrappersTestCases.bool_wrapper)
  return bool_wrapper_ != NULL ? *bool_wrapper_
                         : *::google::protobuf::testing::BoolWrapper::internal_default_instance();
}
inline ::google::protobuf::testing::BoolWrapper* WrappersTestCases::mutable_bool_wrapper() {
  
  if (bool_wrapper_ == NULL) {
    bool_wrapper_ = new ::google::protobuf::testing::BoolWrapper;
  }
  // @@protoc_insertion_point(field_mutable:google.protobuf.testing.WrappersTestCases.bool_wrapper)
  return bool_wrapper_;
}
inline ::google::protobuf::testing::BoolWrapper* WrappersTestCases::release_bool_wrapper() {
  // @@protoc_insertion_point(field_release:google.protobuf.testing.WrappersTestCases.bool_wrapper)
  
  ::google::protobuf::testing::BoolWrapper* temp = bool_wrapper_;
  bool_wrapper_ = NULL;
  return temp;
}
inline void WrappersTestCases::set_allocated_bool_wrapper(::google::protobuf::testing::BoolWrapper* bool_wrapper) {
  delete bool_wrapper_;
  bool_wrapper_ = bool_wrapper;
  if (bool_wrapper) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_set_allocated:google.protobuf.testing.WrappersTestCases.bool_wrapper)
}

// optional .google.protobuf.testing.StringWrapper string_wrapper = 8;
inline bool WrappersTestCases::has_string_wrapper() const {
  return this != internal_default_instance() && string_wrapper_ != NULL;
}
inline void WrappersTestCases::clear_string_wrapper() {
  if (GetArenaNoVirtual() == NULL && string_wrapper_ != NULL) delete string_wrapper_;
  string_wrapper_ = NULL;
}
inline const ::google::protobuf::testing::StringWrapper& WrappersTestCases::string_wrapper() const {
  // @@protoc_insertion_point(field_get:google.protobuf.testing.WrappersTestCases.string_wrapper)
  return string_wrapper_ != NULL ? *string_wrapper_
                         : *::google::protobuf::testing::StringWrapper::internal_default_instance();
}
inline ::google::protobuf::testing::StringWrapper* WrappersTestCases::mutable_string_wrapper() {
  
  if (string_wrapper_ == NULL) {
    string_wrapper_ = new ::google::protobuf::testing::StringWrapper;
  }
  // @@protoc_insertion_point(field_mutable:google.protobuf.testing.WrappersTestCases.string_wrapper)
  return string_wrapper_;
}
inline ::google::protobuf::testing::StringWrapper* WrappersTestCases::release_string_wrapper() {
  // @@protoc_insertion_point(field_release:google.protobuf.testing.WrappersTestCases.string_wrapper)
  
  ::google::protobuf::testing::StringWrapper* temp = string_wrapper_;
  string_wrapper_ = NULL;
  return temp;
}
inline void WrappersTestCases::set_allocated_string_wrapper(::google::protobuf::testing::StringWrapper* string_wrapper) {
  delete string_wrapper_;
  string_wrapper_ = string_wrapper;
  if (string_wrapper) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_set_allocated:google.protobuf.testing.WrappersTestCases.string_wrapper)
}

// optional .google.protobuf.testing.BytesWrapper bytes_wrapper = 9;
inline bool WrappersTestCases::has_bytes_wrapper() const {
  return this != internal_default_instance() && bytes_wrapper_ != NULL;
}
inline void WrappersTestCases::clear_bytes_wrapper() {
  if (GetArenaNoVirtual() == NULL && bytes_wrapper_ != NULL) delete bytes_wrapper_;
  bytes_wrapper_ = NULL;
}
inline const ::google::protobuf::testing::BytesWrapper& WrappersTestCases::bytes_wrapper() const {
  // @@protoc_insertion_point(field_get:google.protobuf.testing.WrappersTestCases.bytes_wrapper)
  return bytes_wrapper_ != NULL ? *bytes_wrapper_
                         : *::google::protobuf::testing::BytesWrapper::internal_default_instance();
}
inline ::google::protobuf::testing::BytesWrapper* WrappersTestCases::mutable_bytes_wrapper() {
  
  if (bytes_wrapper_ == NULL) {
    bytes_wrapper_ = new ::google::protobuf::testing::BytesWrapper;
  }
  // @@protoc_insertion_point(field_mutable:google.protobuf.testing.WrappersTestCases.bytes_wrapper)
  return bytes_wrapper_;
}
inline ::google::protobuf::testing::BytesWrapper* WrappersTestCases::release_bytes_wrapper() {
  // @@protoc_insertion_point(field_release:google.protobuf.testing.WrappersTestCases.bytes_wrapper)
  
  ::google::protobuf::testing::BytesWrapper* temp = bytes_wrapper_;
  bytes_wrapper_ = NULL;
  return temp;
}
inline void WrappersTestCases::set_allocated_bytes_wrapper(::google::protobuf::testing::BytesWrapper* bytes_wrapper) {
  delete bytes_wrapper_;
  bytes_wrapper_ = bytes_wrapper;
  if (bytes_wrapper) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_set_allocated:google.protobuf.testing.WrappersTestCases.bytes_wrapper)
}

// optional .google.protobuf.testing.DoubleWrapper double_wrapper_default = 10;
inline bool WrappersTestCases::has_double_wrapper_default() const {
  return this != internal_default_instance() && double_wrapper_default_ != NULL;
}
inline void WrappersTestCases::clear_double_wrapper_default() {
  if (GetArenaNoVirtual() == NULL && double_wrapper_default_ != NULL) delete double_wrapper_default_;
  double_wrapper_default_ = NULL;
}
inline const ::google::protobuf::testing::DoubleWrapper& WrappersTestCases::double_wrapper_default() const {
  // @@protoc_insertion_point(field_get:google.protobuf.testing.WrappersTestCases.double_wrapper_default)
  return double_wrapper_default_ != NULL ? *double_wrapper_default_
                         : *::google::protobuf::testing::DoubleWrapper::internal_default_instance();
}
inline ::google::protobuf::testing::DoubleWrapper* WrappersTestCases::mutable_double_wrapper_default() {
  
  if (double_wrapper_default_ == NULL) {
    double_wrapper_default_ = new ::google::protobuf::testing::DoubleWrapper;
  }
  // @@protoc_insertion_point(field_mutable:google.protobuf.testing.WrappersTestCases.double_wrapper_default)
  return double_wrapper_default_;
}
inline ::google::protobuf::testing::DoubleWrapper* WrappersTestCases::release_double_wrapper_default() {
  // @@protoc_insertion_point(field_release:google.protobuf.testing.WrappersTestCases.double_wrapper_default)
  
  ::google::protobuf::testing::DoubleWrapper* temp = double_wrapper_default_;
  double_wrapper_default_ = NULL;
  return temp;
}
inline void WrappersTestCases::set_allocated_double_wrapper_default(::google::protobuf::testing::DoubleWrapper* double_wrapper_default) {
  delete double_wrapper_default_;
  double_wrapper_default_ = double_wrapper_default;
  if (double_wrapper_default) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_set_allocated:google.protobuf.testing.WrappersTestCases.double_wrapper_default)
}

// optional .google.protobuf.testing.FloatWrapper float_wrapper_default = 11;
inline bool WrappersTestCases::has_float_wrapper_default() const {
  return this != internal_default_instance() && float_wrapper_default_ != NULL;
}
inline void WrappersTestCases::clear_float_wrapper_default() {
  if (GetArenaNoVirtual() == NULL && float_wrapper_default_ != NULL) delete float_wrapper_default_;
  float_wrapper_default_ = NULL;
}
inline const ::google::protobuf::testing::FloatWrapper& WrappersTestCases::float_wrapper_default() const {
  // @@protoc_insertion_point(field_get:google.protobuf.testing.WrappersTestCases.float_wrapper_default)
  return float_wrapper_default_ != NULL ? *float_wrapper_default_
                         : *::google::protobuf::testing::FloatWrapper::internal_default_instance();
}
inline ::google::protobuf::testing::FloatWrapper* WrappersTestCases::mutable_float_wrapper_default() {
  
  if (float_wrapper_default_ == NULL) {
    float_wrapper_default_ = new ::google::protobuf::testing::FloatWrapper;
  }
  // @@protoc_insertion_point(field_mutable:google.protobuf.testing.WrappersTestCases.float_wrapper_default)
  return float_wrapper_default_;
}
inline ::google::protobuf::testing::FloatWrapper* WrappersTestCases::release_float_wrapper_default() {
  // @@protoc_insertion_point(field_release:google.protobuf.testing.WrappersTestCases.float_wrapper_default)
  
  ::google::protobuf::testing::FloatWrapper* temp = float_wrapper_default_;
  float_wrapper_default_ = NULL;
  return temp;
}
inline void WrappersTestCases::set_allocated_float_wrapper_default(::google::protobuf::testing::FloatWrapper* float_wrapper_default) {
  delete float_wrapper_default_;
  float_wrapper_default_ = float_wrapper_default;
  if (float_wrapper_default) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_set_allocated:google.protobuf.testing.WrappersTestCases.float_wrapper_default)
}

// optional .google.protobuf.testing.Int64Wrapper int64_wrapper_default = 12;
inline bool WrappersTestCases::has_int64_wrapper_default() const {
  return this != internal_default_instance() && int64_wrapper_default_ != NULL;
}
inline void WrappersTestCases::clear_int64_wrapper_default() {
  if (GetArenaNoVirtual() == NULL && int64_wrapper_default_ != NULL) delete int64_wrapper_default_;
  int64_wrapper_default_ = NULL;
}
inline const ::google::protobuf::testing::Int64Wrapper& WrappersTestCases::int64_wrapper_default() const {
  // @@protoc_insertion_point(field_get:google.protobuf.testing.WrappersTestCases.int64_wrapper_default)
  return int64_wrapper_default_ != NULL ? *int64_wrapper_default_
                         : *::google::protobuf::testing::Int64Wrapper::internal_default_instance();
}
inline ::google::protobuf::testing::Int64Wrapper* WrappersTestCases::mutable_int64_wrapper_default() {
  
  if (int64_wrapper_default_ == NULL) {
    int64_wrapper_default_ = new ::google::protobuf::testing::Int64Wrapper;
  }
  // @@protoc_insertion_point(field_mutable:google.protobuf.testing.WrappersTestCases.int64_wrapper_default)
  return int64_wrapper_default_;
}
inline ::google::protobuf::testing::Int64Wrapper* WrappersTestCases::release_int64_wrapper_default() {
  // @@protoc_insertion_point(field_release:google.protobuf.testing.WrappersTestCases.int64_wrapper_default)
  
  ::google::protobuf::testing::Int64Wrapper* temp = int64_wrapper_default_;
  int64_wrapper_default_ = NULL;
  return temp;
}
inline void WrappersTestCases::set_allocated_int64_wrapper_default(::google::protobuf::testing::Int64Wrapper* int64_wrapper_default) {
  delete int64_wrapper_default_;
  int64_wrapper_default_ = int64_wrapper_default;
  if (int64_wrapper_default) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_set_allocated:google.protobuf.testing.WrappersTestCases.int64_wrapper_default)
}

// optional .google.protobuf.testing.UInt64Wrapper uint64_wrapper_default = 13;
inline bool WrappersTestCases::has_uint64_wrapper_default() const {
  return this != internal_default_instance() && uint64_wrapper_default_ != NULL;
}
inline void WrappersTestCases::clear_uint64_wrapper_default() {
  if (GetArenaNoVirtual() == NULL && uint64_wrapper_default_ != NULL) delete uint64_wrapper_default_;
  uint64_wrapper_default_ = NULL;
}
inline const ::google::protobuf::testing::UInt64Wrapper& WrappersTestCases::uint64_wrapper_default() const {
  // @@protoc_insertion_point(field_get:google.protobuf.testing.WrappersTestCases.uint64_wrapper_default)
  return uint64_wrapper_default_ != NULL ? *uint64_wrapper_default_
                         : *::google::protobuf::testing::UInt64Wrapper::internal_default_instance();
}
inline ::google::protobuf::testing::UInt64Wrapper* WrappersTestCases::mutable_uint64_wrapper_default() {
  
  if (uint64_wrapper_default_ == NULL) {
    uint64_wrapper_default_ = new ::google::protobuf::testing::UInt64Wrapper;
  }
  // @@protoc_insertion_point(field_mutable:google.protobuf.testing.WrappersTestCases.uint64_wrapper_default)
  return uint64_wrapper_default_;
}
inline ::google::protobuf::testing::UInt64Wrapper* WrappersTestCases::release_uint64_wrapper_default() {
  // @@protoc_insertion_point(field_release:google.protobuf.testing.WrappersTestCases.uint64_wrapper_default)
  
  ::google::protobuf::testing::UInt64Wrapper* temp = uint64_wrapper_default_;
  uint64_wrapper_default_ = NULL;
  return temp;
}
inline void WrappersTestCases::set_allocated_uint64_wrapper_default(::google::protobuf::testing::UInt64Wrapper* uint64_wrapper_default) {
  delete uint64_wrapper_default_;
  uint64_wrapper_default_ = uint64_wrapper_default;
  if (uint64_wrapper_default) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_set_allocated:google.protobuf.testing.WrappersTestCases.uint64_wrapper_default)
}

// optional .google.protobuf.testing.Int32Wrapper int32_wrapper_default = 14;
inline bool WrappersTestCases::has_int32_wrapper_default() const {
  return this != internal_default_instance() && int32_wrapper_default_ != NULL;
}
inline void WrappersTestCases::clear_int32_wrapper_default() {
  if (GetArenaNoVirtual() == NULL && int32_wrapper_default_ != NULL) delete int32_wrapper_default_;
  int32_wrapper_default_ = NULL;
}
inline const ::google::protobuf::testing::Int32Wrapper& WrappersTestCases::int32_wrapper_default() const {
  // @@protoc_insertion_point(field_get:google.protobuf.testing.WrappersTestCases.int32_wrapper_default)
  return int32_wrapper_default_ != NULL ? *int32_wrapper_default_
                         : *::google::protobuf::testing::Int32Wrapper::internal_default_instance();
}
inline ::google::protobuf::testing::Int32Wrapper* WrappersTestCases::mutable_int32_wrapper_default() {
  
  if (int32_wrapper_default_ == NULL) {
    int32_wrapper_default_ = new ::google::protobuf::testing::Int32Wrapper;
  }
  // @@protoc_insertion_point(field_mutable:google.protobuf.testing.WrappersTestCases.int32_wrapper_default)
  return int32_wrapper_default_;
}
inline ::google::protobuf::testing::Int32Wrapper* WrappersTestCases::release_int32_wrapper_default() {
  // @@protoc_insertion_point(field_release:google.protobuf.testing.WrappersTestCases.int32_wrapper_default)
  
  ::google::protobuf::testing::Int32Wrapper* temp = int32_wrapper_default_;
  int32_wrapper_default_ = NULL;
  return temp;
}
inline void WrappersTestCases::set_allocated_int32_wrapper_default(::google::protobuf::testing::Int32Wrapper* int32_wrapper_default) {
  delete int32_wrapper_default_;
  int32_wrapper_default_ = int32_wrapper_default;
  if (int32_wrapper_default) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_set_allocated:google.protobuf.testing.WrappersTestCases.int32_wrapper_default)
}

// optional .google.protobuf.testing.UInt32Wrapper uint32_wrapper_default = 15;
inline bool WrappersTestCases::has_uint32_wrapper_default() const {
  return this != internal_default_instance() && uint32_wrapper_default_ != NULL;
}
inline void WrappersTestCases::clear_uint32_wrapper_default() {
  if (GetArenaNoVirtual() == NULL && uint32_wrapper_default_ != NULL) delete uint32_wrapper_default_;
  uint32_wrapper_default_ = NULL;
}
inline const ::google::protobuf::testing::UInt32Wrapper& WrappersTestCases::uint32_wrapper_default() const {
  // @@protoc_insertion_point(field_get:google.protobuf.testing.WrappersTestCases.uint32_wrapper_default)
  return uint32_wrapper_default_ != NULL ? *uint32_wrapper_default_
                         : *::google::protobuf::testing::UInt32Wrapper::internal_default_instance();
}
inline ::google::protobuf::testing::UInt32Wrapper* WrappersTestCases::mutable_uint32_wrapper_default() {
  
  if (uint32_wrapper_default_ == NULL) {
    uint32_wrapper_default_ = new ::google::protobuf::testing::UInt32Wrapper;
  }
  // @@protoc_insertion_point(field_mutable:google.protobuf.testing.WrappersTestCases.uint32_wrapper_default)
  return uint32_wrapper_default_;
}
inline ::google::protobuf::testing::UInt32Wrapper* WrappersTestCases::release_uint32_wrapper_default() {
  // @@protoc_insertion_point(field_release:google.protobuf.testing.WrappersTestCases.uint32_wrapper_default)
  
  ::google::protobuf::testing::UInt32Wrapper* temp = uint32_wrapper_default_;
  uint32_wrapper_default_ = NULL;
  return temp;
}
inline void WrappersTestCases::set_allocated_uint32_wrapper_default(::google::protobuf::testing::UInt32Wrapper* uint32_wrapper_default) {
  delete uint32_wrapper_default_;
  uint32_wrapper_default_ = uint32_wrapper_default;
  if (uint32_wrapper_default) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_set_allocated:google.protobuf.testing.WrappersTestCases.uint32_wrapper_default)
}

// optional .google.protobuf.testing.BoolWrapper bool_wrapper_default = 16;
inline bool WrappersTestCases::has_bool_wrapper_default() const {
  return this != internal_default_instance() && bool_wrapper_default_ != NULL;
}
inline void WrappersTestCases::clear_bool_wrapper_default() {
  if (GetArenaNoVirtual() == NULL && bool_wrapper_default_ != NULL) delete bool_wrapper_default_;
  bool_wrapper_default_ = NULL;
}
inline const ::google::protobuf::testing::BoolWrapper& WrappersTestCases::bool_wrapper_default() const {
  // @@protoc_insertion_point(field_get:google.protobuf.testing.WrappersTestCases.bool_wrapper_default)
  return bool_wrapper_default_ != NULL ? *bool_wrapper_default_
                         : *::google::protobuf::testing::BoolWrapper::internal_default_instance();
}
inline ::google::protobuf::testing::BoolWrapper* WrappersTestCases::mutable_bool_wrapper_default() {
  
  if (bool_wrapper_default_ == NULL) {
    bool_wrapper_default_ = new ::google::protobuf::testing::BoolWrapper;
  }
  // @@protoc_insertion_point(field_mutable:google.protobuf.testing.WrappersTestCases.bool_wrapper_default)
  return bool_wrapper_default_;
}
inline ::google::protobuf::testing::BoolWrapper* WrappersTestCases::release_bool_wrapper_default() {
  // @@protoc_insertion_point(field_release:google.protobuf.testing.WrappersTestCases.bool_wrapper_default)
  
  ::google::protobuf::testing::BoolWrapper* temp = bool_wrapper_default_;
  bool_wrapper_default_ = NULL;
  return temp;
}
inline void WrappersTestCases::set_allocated_bool_wrapper_default(::google::protobuf::testing::BoolWrapper* bool_wrapper_default) {
  delete bool_wrapper_default_;
  bool_wrapper_default_ = bool_wrapper_default;
  if (bool_wrapper_default) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_set_allocated:google.protobuf.testing.WrappersTestCases.bool_wrapper_default)
}

// optional .google.protobuf.testing.StringWrapper string_wrapper_default = 17;
inline bool WrappersTestCases::has_string_wrapper_default() const {
  return this != internal_default_instance() && string_wrapper_default_ != NULL;
}
inline void WrappersTestCases::clear_string_wrapper_default() {
  if (GetArenaNoVirtual() == NULL && string_wrapper_default_ != NULL) delete string_wrapper_default_;
  string_wrapper_default_ = NULL;
}
inline const ::google::protobuf::testing::StringWrapper& WrappersTestCases::string_wrapper_default() const {
  // @@protoc_insertion_point(field_get:google.protobuf.testing.WrappersTestCases.string_wrapper_default)
  return string_wrapper_default_ != NULL ? *string_wrapper_default_
                         : *::google::protobuf::testing::StringWrapper::internal_default_instance();
}
inline ::google::protobuf::testing::StringWrapper* WrappersTestCases::mutable_string_wrapper_default() {
  
  if (string_wrapper_default_ == NULL) {
    string_wrapper_default_ = new ::google::protobuf::testing::StringWrapper;
  }
  // @@protoc_insertion_point(field_mutable:google.protobuf.testing.WrappersTestCases.string_wrapper_default)
  return string_wrapper_default_;
}
inline ::google::protobuf::testing::StringWrapper* WrappersTestCases::release_string_wrapper_default() {
  // @@protoc_insertion_point(field_release:google.protobuf.testing.WrappersTestCases.string_wrapper_default)
  
  ::google::protobuf::testing::StringWrapper* temp = string_wrapper_default_;
  string_wrapper_default_ = NULL;
  return temp;
}
inline void WrappersTestCases::set_allocated_string_wrapper_default(::google::protobuf::testing::StringWrapper* string_wrapper_default) {
  delete string_wrapper_default_;
  string_wrapper_default_ = string_wrapper_default;
  if (string_wrapper_default) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_set_allocated:google.protobuf.testing.WrappersTestCases.string_wrapper_default)
}

// optional .google.protobuf.testing.BytesWrapper bytes_wrapper_default = 18;
inline bool WrappersTestCases::has_bytes_wrapper_default() const {
  return this != internal_default_instance() && bytes_wrapper_default_ != NULL;
}
inline void WrappersTestCases::clear_bytes_wrapper_default() {
  if (GetArenaNoVirtual() == NULL && bytes_wrapper_default_ != NULL) delete bytes_wrapper_default_;
  bytes_wrapper_default_ = NULL;
}
inline const ::google::protobuf::testing::BytesWrapper& WrappersTestCases::bytes_wrapper_default() const {
  // @@protoc_insertion_point(field_get:google.protobuf.testing.WrappersTestCases.bytes_wrapper_default)
  return bytes_wrapper_default_ != NULL ? *bytes_wrapper_default_
                         : *::google::protobuf::testing::BytesWrapper::internal_default_instance();
}
inline ::google::protobuf::testing::BytesWrapper* WrappersTestCases::mutable_bytes_wrapper_default() {
  
  if (bytes_wrapper_default_ == NULL) {
    bytes_wrapper_default_ = new ::google::protobuf::testing::BytesWrapper;
  }
  // @@protoc_insertion_point(field_mutable:google.protobuf.testing.WrappersTestCases.bytes_wrapper_default)
  return bytes_wrapper_default_;
}
inline ::google::protobuf::testing::BytesWrapper* WrappersTestCases::release_bytes_wrapper_default() {
  // @@protoc_insertion_point(field_release:google.protobuf.testing.WrappersTestCases.bytes_wrapper_default)
  
  ::google::protobuf::testing::BytesWrapper* temp = bytes_wrapper_default_;
  bytes_wrapper_default_ = NULL;
  return temp;
}
inline void WrappersTestCases::set_allocated_bytes_wrapper_default(::google::protobuf::testing::BytesWrapper* bytes_wrapper_default) {
  delete bytes_wrapper_default_;
  bytes_wrapper_default_ = bytes_wrapper_default;
  if (bytes_wrapper_default) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_set_allocated:google.protobuf.testing.WrappersTestCases.bytes_wrapper_default)
}

inline const WrappersTestCases* WrappersTestCases::internal_default_instance() {
  return &WrappersTestCases_default_instance_.get();
}
// -------------------------------------------------------------------

// DoubleWrapper

// optional .google.protobuf.DoubleValue double = 1;
inline bool DoubleWrapper::has_double_() const {
  return this != internal_default_instance() && double__ != NULL;
}
inline void DoubleWrapper::clear_double_() {
  if (GetArenaNoVirtual() == NULL && double__ != NULL) delete double__;
  double__ = NULL;
}
inline const ::google::protobuf::DoubleValue& DoubleWrapper::double_() const {
  // @@protoc_insertion_point(field_get:google.protobuf.testing.DoubleWrapper.double)
  return double__ != NULL ? *double__
                         : *::google::protobuf::DoubleValue::internal_default_instance();
}
inline ::google::protobuf::DoubleValue* DoubleWrapper::mutable_double_() {
  
  if (double__ == NULL) {
    double__ = new ::google::protobuf::DoubleValue;
  }
  // @@protoc_insertion_point(field_mutable:google.protobuf.testing.DoubleWrapper.double)
  return double__;
}
inline ::google::protobuf::DoubleValue* DoubleWrapper::release_double_() {
  // @@protoc_insertion_point(field_release:google.protobuf.testing.DoubleWrapper.double)
  
  ::google::protobuf::DoubleValue* temp = double__;
  double__ = NULL;
  return temp;
}
inline void DoubleWrapper::set_allocated_double_(::google::protobuf::DoubleValue* double_) {
  delete double__;
  if (double_ != NULL && double_->GetArena() != NULL) {
    ::google::protobuf::DoubleValue* new_double_ = new ::google::protobuf::DoubleValue;
    new_double_->CopyFrom(*double_);
    double_ = new_double_;
  }
  double__ = double_;
  if (double_) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_set_allocated:google.protobuf.testing.DoubleWrapper.double)
}

inline const DoubleWrapper* DoubleWrapper::internal_default_instance() {
  return &DoubleWrapper_default_instance_.get();
}
// -------------------------------------------------------------------

// FloatWrapper

// optional .google.protobuf.FloatValue float = 1;
inline bool FloatWrapper::has_float_() const {
  return this != internal_default_instance() && float__ != NULL;
}
inline void FloatWrapper::clear_float_() {
  if (GetArenaNoVirtual() == NULL && float__ != NULL) delete float__;
  float__ = NULL;
}
inline const ::google::protobuf::FloatValue& FloatWrapper::float_() const {
  // @@protoc_insertion_point(field_get:google.protobuf.testing.FloatWrapper.float)
  return float__ != NULL ? *float__
                         : *::google::protobuf::FloatValue::internal_default_instance();
}
inline ::google::protobuf::FloatValue* FloatWrapper::mutable_float_() {
  
  if (float__ == NULL) {
    float__ = new ::google::protobuf::FloatValue;
  }
  // @@protoc_insertion_point(field_mutable:google.protobuf.testing.FloatWrapper.float)
  return float__;
}
inline ::google::protobuf::FloatValue* FloatWrapper::release_float_() {
  // @@protoc_insertion_point(field_release:google.protobuf.testing.FloatWrapper.float)
  
  ::google::protobuf::FloatValue* temp = float__;
  float__ = NULL;
  return temp;
}
inline void FloatWrapper::set_allocated_float_(::google::protobuf::FloatValue* float_) {
  delete float__;
  if (float_ != NULL && float_->GetArena() != NULL) {
    ::google::protobuf::FloatValue* new_float_ = new ::google::protobuf::FloatValue;
    new_float_->CopyFrom(*float_);
    float_ = new_float_;
  }
  float__ = float_;
  if (float_) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_set_allocated:google.protobuf.testing.FloatWrapper.float)
}

inline const FloatWrapper* FloatWrapper::internal_default_instance() {
  return &FloatWrapper_default_instance_.get();
}
// -------------------------------------------------------------------

// Int64Wrapper

// optional .google.protobuf.Int64Value int64 = 1;
inline bool Int64Wrapper::has_int64() const {
  return this != internal_default_instance() && int64_ != NULL;
}
inline void Int64Wrapper::clear_int64() {
  if (GetArenaNoVirtual() == NULL && int64_ != NULL) delete int64_;
  int64_ = NULL;
}
inline const ::google::protobuf::Int64Value& Int64Wrapper::int64() const {
  // @@protoc_insertion_point(field_get:google.protobuf.testing.Int64Wrapper.int64)
  return int64_ != NULL ? *int64_
                         : *::google::protobuf::Int64Value::internal_default_instance();
}
inline ::google::protobuf::Int64Value* Int64Wrapper::mutable_int64() {
  
  if (int64_ == NULL) {
    int64_ = new ::google::protobuf::Int64Value;
  }
  // @@protoc_insertion_point(field_mutable:google.protobuf.testing.Int64Wrapper.int64)
  return int64_;
}
inline ::google::protobuf::Int64Value* Int64Wrapper::release_int64() {
  // @@protoc_insertion_point(field_release:google.protobuf.testing.Int64Wrapper.int64)
  
  ::google::protobuf::Int64Value* temp = int64_;
  int64_ = NULL;
  return temp;
}
inline void Int64Wrapper::set_allocated_int64(::google::protobuf::Int64Value* int64) {
  delete int64_;
  if (int64 != NULL && int64->GetArena() != NULL) {
    ::google::protobuf::Int64Value* new_int64 = new ::google::protobuf::Int64Value;
    new_int64->CopyFrom(*int64);
    int64 = new_int64;
  }
  int64_ = int64;
  if (int64) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_set_allocated:google.protobuf.testing.Int64Wrapper.int64)
}

inline const Int64Wrapper* Int64Wrapper::internal_default_instance() {
  return &Int64Wrapper_default_instance_.get();
}
// -------------------------------------------------------------------

// UInt64Wrapper

// optional .google.protobuf.UInt64Value uint64 = 1;
inline bool UInt64Wrapper::has_uint64() const {
  return this != internal_default_instance() && uint64_ != NULL;
}
inline void UInt64Wrapper::clear_uint64() {
  if (GetArenaNoVirtual() == NULL && uint64_ != NULL) delete uint64_;
  uint64_ = NULL;
}
inline const ::google::protobuf::UInt64Value& UInt64Wrapper::uint64() const {
  // @@protoc_insertion_point(field_get:google.protobuf.testing.UInt64Wrapper.uint64)
  return uint64_ != NULL ? *uint64_
                         : *::google::protobuf::UInt64Value::internal_default_instance();
}
inline ::google::protobuf::UInt64Value* UInt64Wrapper::mutable_uint64() {
  
  if (uint64_ == NULL) {
    uint64_ = new ::google::protobuf::UInt64Value;
  }
  // @@protoc_insertion_point(field_mutable:google.protobuf.testing.UInt64Wrapper.uint64)
  return uint64_;
}
inline ::google::protobuf::UInt64Value* UInt64Wrapper::release_uint64() {
  // @@protoc_insertion_point(field_release:google.protobuf.testing.UInt64Wrapper.uint64)
  
  ::google::protobuf::UInt64Value* temp = uint64_;
  uint64_ = NULL;
  return temp;
}
inline void UInt64Wrapper::set_allocated_uint64(::google::protobuf::UInt64Value* uint64) {
  delete uint64_;
  if (uint64 != NULL && uint64->GetArena() != NULL) {
    ::google::protobuf::UInt64Value* new_uint64 = new ::google::protobuf::UInt64Value;
    new_uint64->CopyFrom(*uint64);
    uint64 = new_uint64;
  }
  uint64_ = uint64;
  if (uint64) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_set_allocated:google.protobuf.testing.UInt64Wrapper.uint64)
}

inline const UInt64Wrapper* UInt64Wrapper::internal_default_instance() {
  return &UInt64Wrapper_default_instance_.get();
}
// -------------------------------------------------------------------

// Int32Wrapper

// optional .google.protobuf.Int32Value int32 = 1;
inline bool Int32Wrapper::has_int32() const {
  return this != internal_default_instance() && int32_ != NULL;
}
inline void Int32Wrapper::clear_int32() {
  if (GetArenaNoVirtual() == NULL && int32_ != NULL) delete int32_;
  int32_ = NULL;
}
inline const ::google::protobuf::Int32Value& Int32Wrapper::int32() const {
  // @@protoc_insertion_point(field_get:google.protobuf.testing.Int32Wrapper.int32)
  return int32_ != NULL ? *int32_
                         : *::google::protobuf::Int32Value::internal_default_instance();
}
inline ::google::protobuf::Int32Value* Int32Wrapper::mutable_int32() {
  
  if (int32_ == NULL) {
    int32_ = new ::google::protobuf::Int32Value;
  }
  // @@protoc_insertion_point(field_mutable:google.protobuf.testing.Int32Wrapper.int32)
  return int32_;
}
inline ::google::protobuf::Int32Value* Int32Wrapper::release_int32() {
  // @@protoc_insertion_point(field_release:google.protobuf.testing.Int32Wrapper.int32)
  
  ::google::protobuf::Int32Value* temp = int32_;
  int32_ = NULL;
  return temp;
}
inline void Int32Wrapper::set_allocated_int32(::google::protobuf::Int32Value* int32) {
  delete int32_;
  if (int32 != NULL && int32->GetArena() != NULL) {
    ::google::protobuf::Int32Value* new_int32 = new ::google::protobuf::Int32Value;
    new_int32->CopyFrom(*int32);
    int32 = new_int32;
  }
  int32_ = int32;
  if (int32) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_set_allocated:google.protobuf.testing.Int32Wrapper.int32)
}

inline const Int32Wrapper* Int32Wrapper::internal_default_instance() {
  return &Int32Wrapper_default_instance_.get();
}
// -------------------------------------------------------------------

// UInt32Wrapper

// optional .google.protobuf.UInt32Value uint32 = 1;
inline bool UInt32Wrapper::has_uint32() const {
  return this != internal_default_instance() && uint32_ != NULL;
}
inline void UInt32Wrapper::clear_uint32() {
  if (GetArenaNoVirtual() == NULL && uint32_ != NULL) delete uint32_;
  uint32_ = NULL;
}
inline const ::google::protobuf::UInt32Value& UInt32Wrapper::uint32() const {
  // @@protoc_insertion_point(field_get:google.protobuf.testing.UInt32Wrapper.uint32)
  return uint32_ != NULL ? *uint32_
                         : *::google::protobuf::UInt32Value::internal_default_instance();
}
inline ::google::protobuf::UInt32Value* UInt32Wrapper::mutable_uint32() {
  
  if (uint32_ == NULL) {
    uint32_ = new ::google::protobuf::UInt32Value;
  }
  // @@protoc_insertion_point(field_mutable:google.protobuf.testing.UInt32Wrapper.uint32)
  return uint32_;
}
inline ::google::protobuf::UInt32Value* UInt32Wrapper::release_uint32() {
  // @@protoc_insertion_point(field_release:google.protobuf.testing.UInt32Wrapper.uint32)
  
  ::google::protobuf::UInt32Value* temp = uint32_;
  uint32_ = NULL;
  return temp;
}
inline void UInt32Wrapper::set_allocated_uint32(::google::protobuf::UInt32Value* uint32) {
  delete uint32_;
  if (uint32 != NULL && uint32->GetArena() != NULL) {
    ::google::protobuf::UInt32Value* new_uint32 = new ::google::protobuf::UInt32Value;
    new_uint32->CopyFrom(*uint32);
    uint32 = new_uint32;
  }
  uint32_ = uint32;
  if (uint32) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_set_allocated:google.protobuf.testing.UInt32Wrapper.uint32)
}

inline const UInt32Wrapper* UInt32Wrapper::internal_default_instance() {
  return &UInt32Wrapper_default_instance_.get();
}
// -------------------------------------------------------------------

// BoolWrapper

// optional .google.protobuf.BoolValue bool = 1;
inline bool BoolWrapper::has_bool_() const {
  return this != internal_default_instance() && bool__ != NULL;
}
inline void BoolWrapper::clear_bool_() {
  if (GetArenaNoVirtual() == NULL && bool__ != NULL) delete bool__;
  bool__ = NULL;
}
inline const ::google::protobuf::BoolValue& BoolWrapper::bool_() const {
  // @@protoc_insertion_point(field_get:google.protobuf.testing.BoolWrapper.bool)
  return bool__ != NULL ? *bool__
                         : *::google::protobuf::BoolValue::internal_default_instance();
}
inline ::google::protobuf::BoolValue* BoolWrapper::mutable_bool_() {
  
  if (bool__ == NULL) {
    bool__ = new ::google::protobuf::BoolValue;
  }
  // @@protoc_insertion_point(field_mutable:google.protobuf.testing.BoolWrapper.bool)
  return bool__;
}
inline ::google::protobuf::BoolValue* BoolWrapper::release_bool_() {
  // @@protoc_insertion_point(field_release:google.protobuf.testing.BoolWrapper.bool)
  
  ::google::protobuf::BoolValue* temp = bool__;
  bool__ = NULL;
  return temp;
}
inline void BoolWrapper::set_allocated_bool_(::google::protobuf::BoolValue* bool_) {
  delete bool__;
  if (bool_ != NULL && bool_->GetArena() != NULL) {
    ::google::protobuf::BoolValue* new_bool_ = new ::google::protobuf::BoolValue;
    new_bool_->CopyFrom(*bool_);
    bool_ = new_bool_;
  }
  bool__ = bool_;
  if (bool_) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_set_allocated:google.protobuf.testing.BoolWrapper.bool)
}

inline const BoolWrapper* BoolWrapper::internal_default_instance() {
  return &BoolWrapper_default_instance_.get();
}
// -------------------------------------------------------------------

// StringWrapper

// optional .google.protobuf.StringValue string = 1;
inline bool StringWrapper::has_string() const {
  return this != internal_default_instance() && string_ != NULL;
}
inline void StringWrapper::clear_string() {
  if (GetArenaNoVirtual() == NULL && string_ != NULL) delete string_;
  string_ = NULL;
}
inline const ::google::protobuf::StringValue& StringWrapper::string() const {
  // @@protoc_insertion_point(field_get:google.protobuf.testing.StringWrapper.string)
  return string_ != NULL ? *string_
                         : *::google::protobuf::StringValue::internal_default_instance();
}
inline ::google::protobuf::StringValue* StringWrapper::mutable_string() {
  
  if (string_ == NULL) {
    string_ = new ::google::protobuf::StringValue;
  }
  // @@protoc_insertion_point(field_mutable:google.protobuf.testing.StringWrapper.string)
  return string_;
}
inline ::google::protobuf::StringValue* StringWrapper::release_string() {
  // @@protoc_insertion_point(field_release:google.protobuf.testing.StringWrapper.string)
  
  ::google::protobuf::StringValue* temp = string_;
  string_ = NULL;
  return temp;
}
inline void StringWrapper::set_allocated_string(::google::protobuf::StringValue* string) {
  delete string_;
  if (string != NULL && string->GetArena() != NULL) {
    ::google::protobuf::StringValue* new_string = new ::google::protobuf::StringValue;
    new_string->CopyFrom(*string);
    string = new_string;
  }
  string_ = string;
  if (string) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_set_allocated:google.protobuf.testing.StringWrapper.string)
}

inline const StringWrapper* StringWrapper::internal_default_instance() {
  return &StringWrapper_default_instance_.get();
}
// -------------------------------------------------------------------

// BytesWrapper

// optional .google.protobuf.BytesValue bytes = 1;
inline bool BytesWrapper::has_bytes() const {
  return this != internal_default_instance() && bytes_ != NULL;
}
inline void BytesWrapper::clear_bytes() {
  if (GetArenaNoVirtual() == NULL && bytes_ != NULL) delete bytes_;
  bytes_ = NULL;
}
inline const ::google::protobuf::BytesValue& BytesWrapper::bytes() const {
  // @@protoc_insertion_point(field_get:google.protobuf.testing.BytesWrapper.bytes)
  return bytes_ != NULL ? *bytes_
                         : *::google::protobuf::BytesValue::internal_default_instance();
}
inline ::google::protobuf::BytesValue* BytesWrapper::mutable_bytes() {
  
  if (bytes_ == NULL) {
    bytes_ = new ::google::protobuf::BytesValue;
  }
  // @@protoc_insertion_point(field_mutable:google.protobuf.testing.BytesWrapper.bytes)
  return bytes_;
}
inline ::google::protobuf::BytesValue* BytesWrapper::release_bytes() {
  // @@protoc_insertion_point(field_release:google.protobuf.testing.BytesWrapper.bytes)
  
  ::google::protobuf::BytesValue* temp = bytes_;
  bytes_ = NULL;
  return temp;
}
inline void BytesWrapper::set_allocated_bytes(::google::protobuf::BytesValue* bytes) {
  delete bytes_;
  if (bytes != NULL && bytes->GetArena() != NULL) {
    ::google::protobuf::BytesValue* new_bytes = new ::google::protobuf::BytesValue;
    new_bytes->CopyFrom(*bytes);
    bytes = new_bytes;
  }
  bytes_ = bytes;
  if (bytes) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_set_allocated:google.protobuf.testing.BytesWrapper.bytes)
}

inline const BytesWrapper* BytesWrapper::internal_default_instance() {
  return &BytesWrapper_default_instance_.get();
}
#endif  // !PROTOBUF_INLINE_NOT_IN_HEADERS
// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------


// @@protoc_insertion_point(namespace_scope)

}  // namespace testing
}  // namespace protobuf
}  // namespace google

// @@protoc_insertion_point(global_scope)

#endif  // PROTOBUF_google_2fprotobuf_2futil_2finternal_2ftestdata_2fwrappers_2eproto__INCLUDED

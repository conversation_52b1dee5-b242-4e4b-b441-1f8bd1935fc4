// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: google/protobuf/util/internal/testdata/timestamp_duration.proto

#ifndef PROTOBUF_google_2fprotobuf_2futil_2finternal_2ftestdata_2ftimestamp_5fduration_2eproto__INCLUDED
#define PROTOBUF_google_2fprotobuf_2futil_2finternal_2ftestdata_2ftimestamp_5fduration_2eproto__INCLUDED

#include <string>

#include <google/protobuf/stubs/common.h>

#if GOOGLE_PROTOBUF_VERSION < 3001000
#error This file was generated by a newer version of protoc which is
#error incompatible with your Protocol Buffer headers.  Please update
#error your headers.
#endif
#if 3001000 < GOOGLE_PROTOBUF_MIN_PROTOC_VERSION
#error This file was generated by an older version of protoc which is
#error incompatible with your Protocol Buffer headers.  Please
#error regenerate this file with a newer version of protoc.
#endif

#include <google/protobuf/arena.h>
#include <google/protobuf/arenastring.h>
#include <google/protobuf/generated_message_util.h>
#include <google/protobuf/metadata.h>
#include <google/protobuf/message.h>
#include <google/protobuf/repeated_field.h>
#include <google/protobuf/extension_set.h>
#include <google/protobuf/unknown_field_set.h>
#include <google/protobuf/timestamp.pb.h>
#include <google/protobuf/duration.pb.h>
// @@protoc_insertion_point(includes)

namespace google {
namespace protobuf {
namespace testing {

// Internal implementation detail -- do not call these.
void protobuf_AddDesc_google_2fprotobuf_2futil_2finternal_2ftestdata_2ftimestamp_5fduration_2eproto();
void protobuf_InitDefaults_google_2fprotobuf_2futil_2finternal_2ftestdata_2ftimestamp_5fduration_2eproto();
void protobuf_AssignDesc_google_2fprotobuf_2futil_2finternal_2ftestdata_2ftimestamp_5fduration_2eproto();
void protobuf_ShutdownFile_google_2fprotobuf_2futil_2finternal_2ftestdata_2ftimestamp_5fduration_2eproto();

class DurationType;
class TimeStampType;
class TimestampDuration;
class TimestampDurationTestCases;

// ===================================================================

class TimestampDurationTestCases : public ::google::protobuf::Message /* @@protoc_insertion_point(class_definition:google.protobuf.testing.TimestampDurationTestCases) */ {
 public:
  TimestampDurationTestCases();
  virtual ~TimestampDurationTestCases();

  TimestampDurationTestCases(const TimestampDurationTestCases& from);

  inline TimestampDurationTestCases& operator=(const TimestampDurationTestCases& from) {
    CopyFrom(from);
    return *this;
  }

  static const ::google::protobuf::Descriptor* descriptor();
  static const TimestampDurationTestCases& default_instance();

  static const TimestampDurationTestCases* internal_default_instance();

  void Swap(TimestampDurationTestCases* other);

  // implements Message ----------------------------------------------

  inline TimestampDurationTestCases* New() const { return New(NULL); }

  TimestampDurationTestCases* New(::google::protobuf::Arena* arena) const;
  void CopyFrom(const ::google::protobuf::Message& from);
  void MergeFrom(const ::google::protobuf::Message& from);
  void CopyFrom(const TimestampDurationTestCases& from);
  void MergeFrom(const TimestampDurationTestCases& from);
  void Clear();
  bool IsInitialized() const;

  size_t ByteSizeLong() const;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input);
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const;
  ::google::protobuf::uint8* InternalSerializeWithCachedSizesToArray(
      bool deterministic, ::google::protobuf::uint8* output) const;
  ::google::protobuf::uint8* SerializeWithCachedSizesToArray(::google::protobuf::uint8* output) const {
    return InternalSerializeWithCachedSizesToArray(false, output);
  }
  int GetCachedSize() const { return _cached_size_; }
  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const;
  void InternalSwap(TimestampDurationTestCases* other);
  void UnsafeMergeFrom(const TimestampDurationTestCases& from);
  private:
  inline ::google::protobuf::Arena* GetArenaNoVirtual() const {
    return _internal_metadata_.arena();
  }
  inline void* MaybeArenaPtr() const {
    return _internal_metadata_.raw_arena_ptr();
  }
  public:

  ::google::protobuf::Metadata GetMetadata() const;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // optional .google.protobuf.testing.TimeStampType epoch = 1;
  bool has_epoch() const;
  void clear_epoch();
  static const int kEpochFieldNumber = 1;
  const ::google::protobuf::testing::TimeStampType& epoch() const;
  ::google::protobuf::testing::TimeStampType* mutable_epoch();
  ::google::protobuf::testing::TimeStampType* release_epoch();
  void set_allocated_epoch(::google::protobuf::testing::TimeStampType* epoch);

  // optional .google.protobuf.testing.TimeStampType epoch2 = 2;
  bool has_epoch2() const;
  void clear_epoch2();
  static const int kEpoch2FieldNumber = 2;
  const ::google::protobuf::testing::TimeStampType& epoch2() const;
  ::google::protobuf::testing::TimeStampType* mutable_epoch2();
  ::google::protobuf::testing::TimeStampType* release_epoch2();
  void set_allocated_epoch2(::google::protobuf::testing::TimeStampType* epoch2);

  // optional .google.protobuf.testing.TimeStampType mintime = 3;
  bool has_mintime() const;
  void clear_mintime();
  static const int kMintimeFieldNumber = 3;
  const ::google::protobuf::testing::TimeStampType& mintime() const;
  ::google::protobuf::testing::TimeStampType* mutable_mintime();
  ::google::protobuf::testing::TimeStampType* release_mintime();
  void set_allocated_mintime(::google::protobuf::testing::TimeStampType* mintime);

  // optional .google.protobuf.testing.TimeStampType maxtime = 4;
  bool has_maxtime() const;
  void clear_maxtime();
  static const int kMaxtimeFieldNumber = 4;
  const ::google::protobuf::testing::TimeStampType& maxtime() const;
  ::google::protobuf::testing::TimeStampType* mutable_maxtime();
  ::google::protobuf::testing::TimeStampType* release_maxtime();
  void set_allocated_maxtime(::google::protobuf::testing::TimeStampType* maxtime);

  // optional .google.protobuf.testing.TimeStampType timeval1 = 5;
  bool has_timeval1() const;
  void clear_timeval1();
  static const int kTimeval1FieldNumber = 5;
  const ::google::protobuf::testing::TimeStampType& timeval1() const;
  ::google::protobuf::testing::TimeStampType* mutable_timeval1();
  ::google::protobuf::testing::TimeStampType* release_timeval1();
  void set_allocated_timeval1(::google::protobuf::testing::TimeStampType* timeval1);

  // optional .google.protobuf.testing.TimeStampType timeval2 = 6;
  bool has_timeval2() const;
  void clear_timeval2();
  static const int kTimeval2FieldNumber = 6;
  const ::google::protobuf::testing::TimeStampType& timeval2() const;
  ::google::protobuf::testing::TimeStampType* mutable_timeval2();
  ::google::protobuf::testing::TimeStampType* release_timeval2();
  void set_allocated_timeval2(::google::protobuf::testing::TimeStampType* timeval2);

  // optional .google.protobuf.testing.TimeStampType timeval3 = 7;
  bool has_timeval3() const;
  void clear_timeval3();
  static const int kTimeval3FieldNumber = 7;
  const ::google::protobuf::testing::TimeStampType& timeval3() const;
  ::google::protobuf::testing::TimeStampType* mutable_timeval3();
  ::google::protobuf::testing::TimeStampType* release_timeval3();
  void set_allocated_timeval3(::google::protobuf::testing::TimeStampType* timeval3);

  // optional .google.protobuf.testing.TimeStampType timeval4 = 8;
  bool has_timeval4() const;
  void clear_timeval4();
  static const int kTimeval4FieldNumber = 8;
  const ::google::protobuf::testing::TimeStampType& timeval4() const;
  ::google::protobuf::testing::TimeStampType* mutable_timeval4();
  ::google::protobuf::testing::TimeStampType* release_timeval4();
  void set_allocated_timeval4(::google::protobuf::testing::TimeStampType* timeval4);

  // optional .google.protobuf.testing.TimeStampType timeval5 = 9;
  bool has_timeval5() const;
  void clear_timeval5();
  static const int kTimeval5FieldNumber = 9;
  const ::google::protobuf::testing::TimeStampType& timeval5() const;
  ::google::protobuf::testing::TimeStampType* mutable_timeval5();
  ::google::protobuf::testing::TimeStampType* release_timeval5();
  void set_allocated_timeval5(::google::protobuf::testing::TimeStampType* timeval5);

  // optional .google.protobuf.testing.TimeStampType timeval6 = 10;
  bool has_timeval6() const;
  void clear_timeval6();
  static const int kTimeval6FieldNumber = 10;
  const ::google::protobuf::testing::TimeStampType& timeval6() const;
  ::google::protobuf::testing::TimeStampType* mutable_timeval6();
  ::google::protobuf::testing::TimeStampType* release_timeval6();
  void set_allocated_timeval6(::google::protobuf::testing::TimeStampType* timeval6);

  // optional .google.protobuf.testing.TimeStampType timeval7 = 11;
  bool has_timeval7() const;
  void clear_timeval7();
  static const int kTimeval7FieldNumber = 11;
  const ::google::protobuf::testing::TimeStampType& timeval7() const;
  ::google::protobuf::testing::TimeStampType* mutable_timeval7();
  ::google::protobuf::testing::TimeStampType* release_timeval7();
  void set_allocated_timeval7(::google::protobuf::testing::TimeStampType* timeval7);

  // optional .google.protobuf.Timestamp timeval8 = 12;
  bool has_timeval8() const;
  void clear_timeval8();
  static const int kTimeval8FieldNumber = 12;
  const ::google::protobuf::Timestamp& timeval8() const;
  ::google::protobuf::Timestamp* mutable_timeval8();
  ::google::protobuf::Timestamp* release_timeval8();
  void set_allocated_timeval8(::google::protobuf::Timestamp* timeval8);

  // optional .google.protobuf.testing.DurationType zero_duration = 101;
  bool has_zero_duration() const;
  void clear_zero_duration();
  static const int kZeroDurationFieldNumber = 101;
  const ::google::protobuf::testing::DurationType& zero_duration() const;
  ::google::protobuf::testing::DurationType* mutable_zero_duration();
  ::google::protobuf::testing::DurationType* release_zero_duration();
  void set_allocated_zero_duration(::google::protobuf::testing::DurationType* zero_duration);

  // optional .google.protobuf.testing.DurationType min_duration = 102;
  bool has_min_duration() const;
  void clear_min_duration();
  static const int kMinDurationFieldNumber = 102;
  const ::google::protobuf::testing::DurationType& min_duration() const;
  ::google::protobuf::testing::DurationType* mutable_min_duration();
  ::google::protobuf::testing::DurationType* release_min_duration();
  void set_allocated_min_duration(::google::protobuf::testing::DurationType* min_duration);

  // optional .google.protobuf.testing.DurationType max_duration = 103;
  bool has_max_duration() const;
  void clear_max_duration();
  static const int kMaxDurationFieldNumber = 103;
  const ::google::protobuf::testing::DurationType& max_duration() const;
  ::google::protobuf::testing::DurationType* mutable_max_duration();
  ::google::protobuf::testing::DurationType* release_max_duration();
  void set_allocated_max_duration(::google::protobuf::testing::DurationType* max_duration);

  // optional .google.protobuf.testing.DurationType duration1 = 104;
  bool has_duration1() const;
  void clear_duration1();
  static const int kDuration1FieldNumber = 104;
  const ::google::protobuf::testing::DurationType& duration1() const;
  ::google::protobuf::testing::DurationType* mutable_duration1();
  ::google::protobuf::testing::DurationType* release_duration1();
  void set_allocated_duration1(::google::protobuf::testing::DurationType* duration1);

  // optional .google.protobuf.testing.DurationType duration2 = 105;
  bool has_duration2() const;
  void clear_duration2();
  static const int kDuration2FieldNumber = 105;
  const ::google::protobuf::testing::DurationType& duration2() const;
  ::google::protobuf::testing::DurationType* mutable_duration2();
  ::google::protobuf::testing::DurationType* release_duration2();
  void set_allocated_duration2(::google::protobuf::testing::DurationType* duration2);

  // optional .google.protobuf.testing.DurationType duration3 = 106;
  bool has_duration3() const;
  void clear_duration3();
  static const int kDuration3FieldNumber = 106;
  const ::google::protobuf::testing::DurationType& duration3() const;
  ::google::protobuf::testing::DurationType* mutable_duration3();
  ::google::protobuf::testing::DurationType* release_duration3();
  void set_allocated_duration3(::google::protobuf::testing::DurationType* duration3);

  // optional .google.protobuf.testing.DurationType duration4 = 107;
  bool has_duration4() const;
  void clear_duration4();
  static const int kDuration4FieldNumber = 107;
  const ::google::protobuf::testing::DurationType& duration4() const;
  ::google::protobuf::testing::DurationType* mutable_duration4();
  ::google::protobuf::testing::DurationType* release_duration4();
  void set_allocated_duration4(::google::protobuf::testing::DurationType* duration4);

  // optional .google.protobuf.Duration duration5 = 108;
  bool has_duration5() const;
  void clear_duration5();
  static const int kDuration5FieldNumber = 108;
  const ::google::protobuf::Duration& duration5() const;
  ::google::protobuf::Duration* mutable_duration5();
  ::google::protobuf::Duration* release_duration5();
  void set_allocated_duration5(::google::protobuf::Duration* duration5);

  // @@protoc_insertion_point(class_scope:google.protobuf.testing.TimestampDurationTestCases)
 private:

  ::google::protobuf::internal::InternalMetadataWithArena _internal_metadata_;
  ::google::protobuf::testing::TimeStampType* epoch_;
  ::google::protobuf::testing::TimeStampType* epoch2_;
  ::google::protobuf::testing::TimeStampType* mintime_;
  ::google::protobuf::testing::TimeStampType* maxtime_;
  ::google::protobuf::testing::TimeStampType* timeval1_;
  ::google::protobuf::testing::TimeStampType* timeval2_;
  ::google::protobuf::testing::TimeStampType* timeval3_;
  ::google::protobuf::testing::TimeStampType* timeval4_;
  ::google::protobuf::testing::TimeStampType* timeval5_;
  ::google::protobuf::testing::TimeStampType* timeval6_;
  ::google::protobuf::testing::TimeStampType* timeval7_;
  ::google::protobuf::Timestamp* timeval8_;
  ::google::protobuf::testing::DurationType* zero_duration_;
  ::google::protobuf::testing::DurationType* min_duration_;
  ::google::protobuf::testing::DurationType* max_duration_;
  ::google::protobuf::testing::DurationType* duration1_;
  ::google::protobuf::testing::DurationType* duration2_;
  ::google::protobuf::testing::DurationType* duration3_;
  ::google::protobuf::testing::DurationType* duration4_;
  ::google::protobuf::Duration* duration5_;
  mutable int _cached_size_;
  friend void  protobuf_InitDefaults_google_2fprotobuf_2futil_2finternal_2ftestdata_2ftimestamp_5fduration_2eproto_impl();
  friend void  protobuf_AddDesc_google_2fprotobuf_2futil_2finternal_2ftestdata_2ftimestamp_5fduration_2eproto_impl();
  friend void protobuf_AssignDesc_google_2fprotobuf_2futil_2finternal_2ftestdata_2ftimestamp_5fduration_2eproto();
  friend void protobuf_ShutdownFile_google_2fprotobuf_2futil_2finternal_2ftestdata_2ftimestamp_5fduration_2eproto();

  void InitAsDefaultInstance();
};
extern ::google::protobuf::internal::ExplicitlyConstructed<TimestampDurationTestCases> TimestampDurationTestCases_default_instance_;

// -------------------------------------------------------------------

class TimeStampType : public ::google::protobuf::Message /* @@protoc_insertion_point(class_definition:google.protobuf.testing.TimeStampType) */ {
 public:
  TimeStampType();
  virtual ~TimeStampType();

  TimeStampType(const TimeStampType& from);

  inline TimeStampType& operator=(const TimeStampType& from) {
    CopyFrom(from);
    return *this;
  }

  static const ::google::protobuf::Descriptor* descriptor();
  static const TimeStampType& default_instance();

  static const TimeStampType* internal_default_instance();

  void Swap(TimeStampType* other);

  // implements Message ----------------------------------------------

  inline TimeStampType* New() const { return New(NULL); }

  TimeStampType* New(::google::protobuf::Arena* arena) const;
  void CopyFrom(const ::google::protobuf::Message& from);
  void MergeFrom(const ::google::protobuf::Message& from);
  void CopyFrom(const TimeStampType& from);
  void MergeFrom(const TimeStampType& from);
  void Clear();
  bool IsInitialized() const;

  size_t ByteSizeLong() const;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input);
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const;
  ::google::protobuf::uint8* InternalSerializeWithCachedSizesToArray(
      bool deterministic, ::google::protobuf::uint8* output) const;
  ::google::protobuf::uint8* SerializeWithCachedSizesToArray(::google::protobuf::uint8* output) const {
    return InternalSerializeWithCachedSizesToArray(false, output);
  }
  int GetCachedSize() const { return _cached_size_; }
  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const;
  void InternalSwap(TimeStampType* other);
  void UnsafeMergeFrom(const TimeStampType& from);
  private:
  inline ::google::protobuf::Arena* GetArenaNoVirtual() const {
    return _internal_metadata_.arena();
  }
  inline void* MaybeArenaPtr() const {
    return _internal_metadata_.raw_arena_ptr();
  }
  public:

  ::google::protobuf::Metadata GetMetadata() const;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // optional .google.protobuf.Timestamp timestamp = 1;
  bool has_timestamp() const;
  void clear_timestamp();
  static const int kTimestampFieldNumber = 1;
  const ::google::protobuf::Timestamp& timestamp() const;
  ::google::protobuf::Timestamp* mutable_timestamp();
  ::google::protobuf::Timestamp* release_timestamp();
  void set_allocated_timestamp(::google::protobuf::Timestamp* timestamp);

  // @@protoc_insertion_point(class_scope:google.protobuf.testing.TimeStampType)
 private:

  ::google::protobuf::internal::InternalMetadataWithArena _internal_metadata_;
  ::google::protobuf::Timestamp* timestamp_;
  mutable int _cached_size_;
  friend void  protobuf_InitDefaults_google_2fprotobuf_2futil_2finternal_2ftestdata_2ftimestamp_5fduration_2eproto_impl();
  friend void  protobuf_AddDesc_google_2fprotobuf_2futil_2finternal_2ftestdata_2ftimestamp_5fduration_2eproto_impl();
  friend void protobuf_AssignDesc_google_2fprotobuf_2futil_2finternal_2ftestdata_2ftimestamp_5fduration_2eproto();
  friend void protobuf_ShutdownFile_google_2fprotobuf_2futil_2finternal_2ftestdata_2ftimestamp_5fduration_2eproto();

  void InitAsDefaultInstance();
};
extern ::google::protobuf::internal::ExplicitlyConstructed<TimeStampType> TimeStampType_default_instance_;

// -------------------------------------------------------------------

class DurationType : public ::google::protobuf::Message /* @@protoc_insertion_point(class_definition:google.protobuf.testing.DurationType) */ {
 public:
  DurationType();
  virtual ~DurationType();

  DurationType(const DurationType& from);

  inline DurationType& operator=(const DurationType& from) {
    CopyFrom(from);
    return *this;
  }

  static const ::google::protobuf::Descriptor* descriptor();
  static const DurationType& default_instance();

  static const DurationType* internal_default_instance();

  void Swap(DurationType* other);

  // implements Message ----------------------------------------------

  inline DurationType* New() const { return New(NULL); }

  DurationType* New(::google::protobuf::Arena* arena) const;
  void CopyFrom(const ::google::protobuf::Message& from);
  void MergeFrom(const ::google::protobuf::Message& from);
  void CopyFrom(const DurationType& from);
  void MergeFrom(const DurationType& from);
  void Clear();
  bool IsInitialized() const;

  size_t ByteSizeLong() const;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input);
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const;
  ::google::protobuf::uint8* InternalSerializeWithCachedSizesToArray(
      bool deterministic, ::google::protobuf::uint8* output) const;
  ::google::protobuf::uint8* SerializeWithCachedSizesToArray(::google::protobuf::uint8* output) const {
    return InternalSerializeWithCachedSizesToArray(false, output);
  }
  int GetCachedSize() const { return _cached_size_; }
  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const;
  void InternalSwap(DurationType* other);
  void UnsafeMergeFrom(const DurationType& from);
  private:
  inline ::google::protobuf::Arena* GetArenaNoVirtual() const {
    return _internal_metadata_.arena();
  }
  inline void* MaybeArenaPtr() const {
    return _internal_metadata_.raw_arena_ptr();
  }
  public:

  ::google::protobuf::Metadata GetMetadata() const;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // optional .google.protobuf.Duration duration = 1;
  bool has_duration() const;
  void clear_duration();
  static const int kDurationFieldNumber = 1;
  const ::google::protobuf::Duration& duration() const;
  ::google::protobuf::Duration* mutable_duration();
  ::google::protobuf::Duration* release_duration();
  void set_allocated_duration(::google::protobuf::Duration* duration);

  // @@protoc_insertion_point(class_scope:google.protobuf.testing.DurationType)
 private:

  ::google::protobuf::internal::InternalMetadataWithArena _internal_metadata_;
  ::google::protobuf::Duration* duration_;
  mutable int _cached_size_;
  friend void  protobuf_InitDefaults_google_2fprotobuf_2futil_2finternal_2ftestdata_2ftimestamp_5fduration_2eproto_impl();
  friend void  protobuf_AddDesc_google_2fprotobuf_2futil_2finternal_2ftestdata_2ftimestamp_5fduration_2eproto_impl();
  friend void protobuf_AssignDesc_google_2fprotobuf_2futil_2finternal_2ftestdata_2ftimestamp_5fduration_2eproto();
  friend void protobuf_ShutdownFile_google_2fprotobuf_2futil_2finternal_2ftestdata_2ftimestamp_5fduration_2eproto();

  void InitAsDefaultInstance();
};
extern ::google::protobuf::internal::ExplicitlyConstructed<DurationType> DurationType_default_instance_;

// -------------------------------------------------------------------

class TimestampDuration : public ::google::protobuf::Message /* @@protoc_insertion_point(class_definition:google.protobuf.testing.TimestampDuration) */ {
 public:
  TimestampDuration();
  virtual ~TimestampDuration();

  TimestampDuration(const TimestampDuration& from);

  inline TimestampDuration& operator=(const TimestampDuration& from) {
    CopyFrom(from);
    return *this;
  }

  static const ::google::protobuf::Descriptor* descriptor();
  static const TimestampDuration& default_instance();

  static const TimestampDuration* internal_default_instance();

  void Swap(TimestampDuration* other);

  // implements Message ----------------------------------------------

  inline TimestampDuration* New() const { return New(NULL); }

  TimestampDuration* New(::google::protobuf::Arena* arena) const;
  void CopyFrom(const ::google::protobuf::Message& from);
  void MergeFrom(const ::google::protobuf::Message& from);
  void CopyFrom(const TimestampDuration& from);
  void MergeFrom(const TimestampDuration& from);
  void Clear();
  bool IsInitialized() const;

  size_t ByteSizeLong() const;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input);
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const;
  ::google::protobuf::uint8* InternalSerializeWithCachedSizesToArray(
      bool deterministic, ::google::protobuf::uint8* output) const;
  ::google::protobuf::uint8* SerializeWithCachedSizesToArray(::google::protobuf::uint8* output) const {
    return InternalSerializeWithCachedSizesToArray(false, output);
  }
  int GetCachedSize() const { return _cached_size_; }
  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const;
  void InternalSwap(TimestampDuration* other);
  void UnsafeMergeFrom(const TimestampDuration& from);
  private:
  inline ::google::protobuf::Arena* GetArenaNoVirtual() const {
    return _internal_metadata_.arena();
  }
  inline void* MaybeArenaPtr() const {
    return _internal_metadata_.raw_arena_ptr();
  }
  public:

  ::google::protobuf::Metadata GetMetadata() const;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // optional .google.protobuf.Timestamp ts = 1;
  bool has_ts() const;
  void clear_ts();
  static const int kTsFieldNumber = 1;
  const ::google::protobuf::Timestamp& ts() const;
  ::google::protobuf::Timestamp* mutable_ts();
  ::google::protobuf::Timestamp* release_ts();
  void set_allocated_ts(::google::protobuf::Timestamp* ts);

  // optional .google.protobuf.Duration dur = 2;
  bool has_dur() const;
  void clear_dur();
  static const int kDurFieldNumber = 2;
  const ::google::protobuf::Duration& dur() const;
  ::google::protobuf::Duration* mutable_dur();
  ::google::protobuf::Duration* release_dur();
  void set_allocated_dur(::google::protobuf::Duration* dur);

  // repeated .google.protobuf.Timestamp rep_ts = 3;
  int rep_ts_size() const;
  void clear_rep_ts();
  static const int kRepTsFieldNumber = 3;
  const ::google::protobuf::Timestamp& rep_ts(int index) const;
  ::google::protobuf::Timestamp* mutable_rep_ts(int index);
  ::google::protobuf::Timestamp* add_rep_ts();
  ::google::protobuf::RepeatedPtrField< ::google::protobuf::Timestamp >*
      mutable_rep_ts();
  const ::google::protobuf::RepeatedPtrField< ::google::protobuf::Timestamp >&
      rep_ts() const;

  // @@protoc_insertion_point(class_scope:google.protobuf.testing.TimestampDuration)
 private:

  ::google::protobuf::internal::InternalMetadataWithArena _internal_metadata_;
  ::google::protobuf::RepeatedPtrField< ::google::protobuf::Timestamp > rep_ts_;
  ::google::protobuf::Timestamp* ts_;
  ::google::protobuf::Duration* dur_;
  mutable int _cached_size_;
  friend void  protobuf_InitDefaults_google_2fprotobuf_2futil_2finternal_2ftestdata_2ftimestamp_5fduration_2eproto_impl();
  friend void  protobuf_AddDesc_google_2fprotobuf_2futil_2finternal_2ftestdata_2ftimestamp_5fduration_2eproto_impl();
  friend void protobuf_AssignDesc_google_2fprotobuf_2futil_2finternal_2ftestdata_2ftimestamp_5fduration_2eproto();
  friend void protobuf_ShutdownFile_google_2fprotobuf_2futil_2finternal_2ftestdata_2ftimestamp_5fduration_2eproto();

  void InitAsDefaultInstance();
};
extern ::google::protobuf::internal::ExplicitlyConstructed<TimestampDuration> TimestampDuration_default_instance_;

// ===================================================================


// ===================================================================

#if !PROTOBUF_INLINE_NOT_IN_HEADERS
// TimestampDurationTestCases

// optional .google.protobuf.testing.TimeStampType epoch = 1;
inline bool TimestampDurationTestCases::has_epoch() const {
  return this != internal_default_instance() && epoch_ != NULL;
}
inline void TimestampDurationTestCases::clear_epoch() {
  if (GetArenaNoVirtual() == NULL && epoch_ != NULL) delete epoch_;
  epoch_ = NULL;
}
inline const ::google::protobuf::testing::TimeStampType& TimestampDurationTestCases::epoch() const {
  // @@protoc_insertion_point(field_get:google.protobuf.testing.TimestampDurationTestCases.epoch)
  return epoch_ != NULL ? *epoch_
                         : *::google::protobuf::testing::TimeStampType::internal_default_instance();
}
inline ::google::protobuf::testing::TimeStampType* TimestampDurationTestCases::mutable_epoch() {
  
  if (epoch_ == NULL) {
    epoch_ = new ::google::protobuf::testing::TimeStampType;
  }
  // @@protoc_insertion_point(field_mutable:google.protobuf.testing.TimestampDurationTestCases.epoch)
  return epoch_;
}
inline ::google::protobuf::testing::TimeStampType* TimestampDurationTestCases::release_epoch() {
  // @@protoc_insertion_point(field_release:google.protobuf.testing.TimestampDurationTestCases.epoch)
  
  ::google::protobuf::testing::TimeStampType* temp = epoch_;
  epoch_ = NULL;
  return temp;
}
inline void TimestampDurationTestCases::set_allocated_epoch(::google::protobuf::testing::TimeStampType* epoch) {
  delete epoch_;
  epoch_ = epoch;
  if (epoch) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_set_allocated:google.protobuf.testing.TimestampDurationTestCases.epoch)
}

// optional .google.protobuf.testing.TimeStampType epoch2 = 2;
inline bool TimestampDurationTestCases::has_epoch2() const {
  return this != internal_default_instance() && epoch2_ != NULL;
}
inline void TimestampDurationTestCases::clear_epoch2() {
  if (GetArenaNoVirtual() == NULL && epoch2_ != NULL) delete epoch2_;
  epoch2_ = NULL;
}
inline const ::google::protobuf::testing::TimeStampType& TimestampDurationTestCases::epoch2() const {
  // @@protoc_insertion_point(field_get:google.protobuf.testing.TimestampDurationTestCases.epoch2)
  return epoch2_ != NULL ? *epoch2_
                         : *::google::protobuf::testing::TimeStampType::internal_default_instance();
}
inline ::google::protobuf::testing::TimeStampType* TimestampDurationTestCases::mutable_epoch2() {
  
  if (epoch2_ == NULL) {
    epoch2_ = new ::google::protobuf::testing::TimeStampType;
  }
  // @@protoc_insertion_point(field_mutable:google.protobuf.testing.TimestampDurationTestCases.epoch2)
  return epoch2_;
}
inline ::google::protobuf::testing::TimeStampType* TimestampDurationTestCases::release_epoch2() {
  // @@protoc_insertion_point(field_release:google.protobuf.testing.TimestampDurationTestCases.epoch2)
  
  ::google::protobuf::testing::TimeStampType* temp = epoch2_;
  epoch2_ = NULL;
  return temp;
}
inline void TimestampDurationTestCases::set_allocated_epoch2(::google::protobuf::testing::TimeStampType* epoch2) {
  delete epoch2_;
  epoch2_ = epoch2;
  if (epoch2) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_set_allocated:google.protobuf.testing.TimestampDurationTestCases.epoch2)
}

// optional .google.protobuf.testing.TimeStampType mintime = 3;
inline bool TimestampDurationTestCases::has_mintime() const {
  return this != internal_default_instance() && mintime_ != NULL;
}
inline void TimestampDurationTestCases::clear_mintime() {
  if (GetArenaNoVirtual() == NULL && mintime_ != NULL) delete mintime_;
  mintime_ = NULL;
}
inline const ::google::protobuf::testing::TimeStampType& TimestampDurationTestCases::mintime() const {
  // @@protoc_insertion_point(field_get:google.protobuf.testing.TimestampDurationTestCases.mintime)
  return mintime_ != NULL ? *mintime_
                         : *::google::protobuf::testing::TimeStampType::internal_default_instance();
}
inline ::google::protobuf::testing::TimeStampType* TimestampDurationTestCases::mutable_mintime() {
  
  if (mintime_ == NULL) {
    mintime_ = new ::google::protobuf::testing::TimeStampType;
  }
  // @@protoc_insertion_point(field_mutable:google.protobuf.testing.TimestampDurationTestCases.mintime)
  return mintime_;
}
inline ::google::protobuf::testing::TimeStampType* TimestampDurationTestCases::release_mintime() {
  // @@protoc_insertion_point(field_release:google.protobuf.testing.TimestampDurationTestCases.mintime)
  
  ::google::protobuf::testing::TimeStampType* temp = mintime_;
  mintime_ = NULL;
  return temp;
}
inline void TimestampDurationTestCases::set_allocated_mintime(::google::protobuf::testing::TimeStampType* mintime) {
  delete mintime_;
  mintime_ = mintime;
  if (mintime) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_set_allocated:google.protobuf.testing.TimestampDurationTestCases.mintime)
}

// optional .google.protobuf.testing.TimeStampType maxtime = 4;
inline bool TimestampDurationTestCases::has_maxtime() const {
  return this != internal_default_instance() && maxtime_ != NULL;
}
inline void TimestampDurationTestCases::clear_maxtime() {
  if (GetArenaNoVirtual() == NULL && maxtime_ != NULL) delete maxtime_;
  maxtime_ = NULL;
}
inline const ::google::protobuf::testing::TimeStampType& TimestampDurationTestCases::maxtime() const {
  // @@protoc_insertion_point(field_get:google.protobuf.testing.TimestampDurationTestCases.maxtime)
  return maxtime_ != NULL ? *maxtime_
                         : *::google::protobuf::testing::TimeStampType::internal_default_instance();
}
inline ::google::protobuf::testing::TimeStampType* TimestampDurationTestCases::mutable_maxtime() {
  
  if (maxtime_ == NULL) {
    maxtime_ = new ::google::protobuf::testing::TimeStampType;
  }
  // @@protoc_insertion_point(field_mutable:google.protobuf.testing.TimestampDurationTestCases.maxtime)
  return maxtime_;
}
inline ::google::protobuf::testing::TimeStampType* TimestampDurationTestCases::release_maxtime() {
  // @@protoc_insertion_point(field_release:google.protobuf.testing.TimestampDurationTestCases.maxtime)
  
  ::google::protobuf::testing::TimeStampType* temp = maxtime_;
  maxtime_ = NULL;
  return temp;
}
inline void TimestampDurationTestCases::set_allocated_maxtime(::google::protobuf::testing::TimeStampType* maxtime) {
  delete maxtime_;
  maxtime_ = maxtime;
  if (maxtime) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_set_allocated:google.protobuf.testing.TimestampDurationTestCases.maxtime)
}

// optional .google.protobuf.testing.TimeStampType timeval1 = 5;
inline bool TimestampDurationTestCases::has_timeval1() const {
  return this != internal_default_instance() && timeval1_ != NULL;
}
inline void TimestampDurationTestCases::clear_timeval1() {
  if (GetArenaNoVirtual() == NULL && timeval1_ != NULL) delete timeval1_;
  timeval1_ = NULL;
}
inline const ::google::protobuf::testing::TimeStampType& TimestampDurationTestCases::timeval1() const {
  // @@protoc_insertion_point(field_get:google.protobuf.testing.TimestampDurationTestCases.timeval1)
  return timeval1_ != NULL ? *timeval1_
                         : *::google::protobuf::testing::TimeStampType::internal_default_instance();
}
inline ::google::protobuf::testing::TimeStampType* TimestampDurationTestCases::mutable_timeval1() {
  
  if (timeval1_ == NULL) {
    timeval1_ = new ::google::protobuf::testing::TimeStampType;
  }
  // @@protoc_insertion_point(field_mutable:google.protobuf.testing.TimestampDurationTestCases.timeval1)
  return timeval1_;
}
inline ::google::protobuf::testing::TimeStampType* TimestampDurationTestCases::release_timeval1() {
  // @@protoc_insertion_point(field_release:google.protobuf.testing.TimestampDurationTestCases.timeval1)
  
  ::google::protobuf::testing::TimeStampType* temp = timeval1_;
  timeval1_ = NULL;
  return temp;
}
inline void TimestampDurationTestCases::set_allocated_timeval1(::google::protobuf::testing::TimeStampType* timeval1) {
  delete timeval1_;
  timeval1_ = timeval1;
  if (timeval1) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_set_allocated:google.protobuf.testing.TimestampDurationTestCases.timeval1)
}

// optional .google.protobuf.testing.TimeStampType timeval2 = 6;
inline bool TimestampDurationTestCases::has_timeval2() const {
  return this != internal_default_instance() && timeval2_ != NULL;
}
inline void TimestampDurationTestCases::clear_timeval2() {
  if (GetArenaNoVirtual() == NULL && timeval2_ != NULL) delete timeval2_;
  timeval2_ = NULL;
}
inline const ::google::protobuf::testing::TimeStampType& TimestampDurationTestCases::timeval2() const {
  // @@protoc_insertion_point(field_get:google.protobuf.testing.TimestampDurationTestCases.timeval2)
  return timeval2_ != NULL ? *timeval2_
                         : *::google::protobuf::testing::TimeStampType::internal_default_instance();
}
inline ::google::protobuf::testing::TimeStampType* TimestampDurationTestCases::mutable_timeval2() {
  
  if (timeval2_ == NULL) {
    timeval2_ = new ::google::protobuf::testing::TimeStampType;
  }
  // @@protoc_insertion_point(field_mutable:google.protobuf.testing.TimestampDurationTestCases.timeval2)
  return timeval2_;
}
inline ::google::protobuf::testing::TimeStampType* TimestampDurationTestCases::release_timeval2() {
  // @@protoc_insertion_point(field_release:google.protobuf.testing.TimestampDurationTestCases.timeval2)
  
  ::google::protobuf::testing::TimeStampType* temp = timeval2_;
  timeval2_ = NULL;
  return temp;
}
inline void TimestampDurationTestCases::set_allocated_timeval2(::google::protobuf::testing::TimeStampType* timeval2) {
  delete timeval2_;
  timeval2_ = timeval2;
  if (timeval2) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_set_allocated:google.protobuf.testing.TimestampDurationTestCases.timeval2)
}

// optional .google.protobuf.testing.TimeStampType timeval3 = 7;
inline bool TimestampDurationTestCases::has_timeval3() const {
  return this != internal_default_instance() && timeval3_ != NULL;
}
inline void TimestampDurationTestCases::clear_timeval3() {
  if (GetArenaNoVirtual() == NULL && timeval3_ != NULL) delete timeval3_;
  timeval3_ = NULL;
}
inline const ::google::protobuf::testing::TimeStampType& TimestampDurationTestCases::timeval3() const {
  // @@protoc_insertion_point(field_get:google.protobuf.testing.TimestampDurationTestCases.timeval3)
  return timeval3_ != NULL ? *timeval3_
                         : *::google::protobuf::testing::TimeStampType::internal_default_instance();
}
inline ::google::protobuf::testing::TimeStampType* TimestampDurationTestCases::mutable_timeval3() {
  
  if (timeval3_ == NULL) {
    timeval3_ = new ::google::protobuf::testing::TimeStampType;
  }
  // @@protoc_insertion_point(field_mutable:google.protobuf.testing.TimestampDurationTestCases.timeval3)
  return timeval3_;
}
inline ::google::protobuf::testing::TimeStampType* TimestampDurationTestCases::release_timeval3() {
  // @@protoc_insertion_point(field_release:google.protobuf.testing.TimestampDurationTestCases.timeval3)
  
  ::google::protobuf::testing::TimeStampType* temp = timeval3_;
  timeval3_ = NULL;
  return temp;
}
inline void TimestampDurationTestCases::set_allocated_timeval3(::google::protobuf::testing::TimeStampType* timeval3) {
  delete timeval3_;
  timeval3_ = timeval3;
  if (timeval3) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_set_allocated:google.protobuf.testing.TimestampDurationTestCases.timeval3)
}

// optional .google.protobuf.testing.TimeStampType timeval4 = 8;
inline bool TimestampDurationTestCases::has_timeval4() const {
  return this != internal_default_instance() && timeval4_ != NULL;
}
inline void TimestampDurationTestCases::clear_timeval4() {
  if (GetArenaNoVirtual() == NULL && timeval4_ != NULL) delete timeval4_;
  timeval4_ = NULL;
}
inline const ::google::protobuf::testing::TimeStampType& TimestampDurationTestCases::timeval4() const {
  // @@protoc_insertion_point(field_get:google.protobuf.testing.TimestampDurationTestCases.timeval4)
  return timeval4_ != NULL ? *timeval4_
                         : *::google::protobuf::testing::TimeStampType::internal_default_instance();
}
inline ::google::protobuf::testing::TimeStampType* TimestampDurationTestCases::mutable_timeval4() {
  
  if (timeval4_ == NULL) {
    timeval4_ = new ::google::protobuf::testing::TimeStampType;
  }
  // @@protoc_insertion_point(field_mutable:google.protobuf.testing.TimestampDurationTestCases.timeval4)
  return timeval4_;
}
inline ::google::protobuf::testing::TimeStampType* TimestampDurationTestCases::release_timeval4() {
  // @@protoc_insertion_point(field_release:google.protobuf.testing.TimestampDurationTestCases.timeval4)
  
  ::google::protobuf::testing::TimeStampType* temp = timeval4_;
  timeval4_ = NULL;
  return temp;
}
inline void TimestampDurationTestCases::set_allocated_timeval4(::google::protobuf::testing::TimeStampType* timeval4) {
  delete timeval4_;
  timeval4_ = timeval4;
  if (timeval4) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_set_allocated:google.protobuf.testing.TimestampDurationTestCases.timeval4)
}

// optional .google.protobuf.testing.TimeStampType timeval5 = 9;
inline bool TimestampDurationTestCases::has_timeval5() const {
  return this != internal_default_instance() && timeval5_ != NULL;
}
inline void TimestampDurationTestCases::clear_timeval5() {
  if (GetArenaNoVirtual() == NULL && timeval5_ != NULL) delete timeval5_;
  timeval5_ = NULL;
}
inline const ::google::protobuf::testing::TimeStampType& TimestampDurationTestCases::timeval5() const {
  // @@protoc_insertion_point(field_get:google.protobuf.testing.TimestampDurationTestCases.timeval5)
  return timeval5_ != NULL ? *timeval5_
                         : *::google::protobuf::testing::TimeStampType::internal_default_instance();
}
inline ::google::protobuf::testing::TimeStampType* TimestampDurationTestCases::mutable_timeval5() {
  
  if (timeval5_ == NULL) {
    timeval5_ = new ::google::protobuf::testing::TimeStampType;
  }
  // @@protoc_insertion_point(field_mutable:google.protobuf.testing.TimestampDurationTestCases.timeval5)
  return timeval5_;
}
inline ::google::protobuf::testing::TimeStampType* TimestampDurationTestCases::release_timeval5() {
  // @@protoc_insertion_point(field_release:google.protobuf.testing.TimestampDurationTestCases.timeval5)
  
  ::google::protobuf::testing::TimeStampType* temp = timeval5_;
  timeval5_ = NULL;
  return temp;
}
inline void TimestampDurationTestCases::set_allocated_timeval5(::google::protobuf::testing::TimeStampType* timeval5) {
  delete timeval5_;
  timeval5_ = timeval5;
  if (timeval5) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_set_allocated:google.protobuf.testing.TimestampDurationTestCases.timeval5)
}

// optional .google.protobuf.testing.TimeStampType timeval6 = 10;
inline bool TimestampDurationTestCases::has_timeval6() const {
  return this != internal_default_instance() && timeval6_ != NULL;
}
inline void TimestampDurationTestCases::clear_timeval6() {
  if (GetArenaNoVirtual() == NULL && timeval6_ != NULL) delete timeval6_;
  timeval6_ = NULL;
}
inline const ::google::protobuf::testing::TimeStampType& TimestampDurationTestCases::timeval6() const {
  // @@protoc_insertion_point(field_get:google.protobuf.testing.TimestampDurationTestCases.timeval6)
  return timeval6_ != NULL ? *timeval6_
                         : *::google::protobuf::testing::TimeStampType::internal_default_instance();
}
inline ::google::protobuf::testing::TimeStampType* TimestampDurationTestCases::mutable_timeval6() {
  
  if (timeval6_ == NULL) {
    timeval6_ = new ::google::protobuf::testing::TimeStampType;
  }
  // @@protoc_insertion_point(field_mutable:google.protobuf.testing.TimestampDurationTestCases.timeval6)
  return timeval6_;
}
inline ::google::protobuf::testing::TimeStampType* TimestampDurationTestCases::release_timeval6() {
  // @@protoc_insertion_point(field_release:google.protobuf.testing.TimestampDurationTestCases.timeval6)
  
  ::google::protobuf::testing::TimeStampType* temp = timeval6_;
  timeval6_ = NULL;
  return temp;
}
inline void TimestampDurationTestCases::set_allocated_timeval6(::google::protobuf::testing::TimeStampType* timeval6) {
  delete timeval6_;
  timeval6_ = timeval6;
  if (timeval6) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_set_allocated:google.protobuf.testing.TimestampDurationTestCases.timeval6)
}

// optional .google.protobuf.testing.TimeStampType timeval7 = 11;
inline bool TimestampDurationTestCases::has_timeval7() const {
  return this != internal_default_instance() && timeval7_ != NULL;
}
inline void TimestampDurationTestCases::clear_timeval7() {
  if (GetArenaNoVirtual() == NULL && timeval7_ != NULL) delete timeval7_;
  timeval7_ = NULL;
}
inline const ::google::protobuf::testing::TimeStampType& TimestampDurationTestCases::timeval7() const {
  // @@protoc_insertion_point(field_get:google.protobuf.testing.TimestampDurationTestCases.timeval7)
  return timeval7_ != NULL ? *timeval7_
                         : *::google::protobuf::testing::TimeStampType::internal_default_instance();
}
inline ::google::protobuf::testing::TimeStampType* TimestampDurationTestCases::mutable_timeval7() {
  
  if (timeval7_ == NULL) {
    timeval7_ = new ::google::protobuf::testing::TimeStampType;
  }
  // @@protoc_insertion_point(field_mutable:google.protobuf.testing.TimestampDurationTestCases.timeval7)
  return timeval7_;
}
inline ::google::protobuf::testing::TimeStampType* TimestampDurationTestCases::release_timeval7() {
  // @@protoc_insertion_point(field_release:google.protobuf.testing.TimestampDurationTestCases.timeval7)
  
  ::google::protobuf::testing::TimeStampType* temp = timeval7_;
  timeval7_ = NULL;
  return temp;
}
inline void TimestampDurationTestCases::set_allocated_timeval7(::google::protobuf::testing::TimeStampType* timeval7) {
  delete timeval7_;
  timeval7_ = timeval7;
  if (timeval7) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_set_allocated:google.protobuf.testing.TimestampDurationTestCases.timeval7)
}

// optional .google.protobuf.Timestamp timeval8 = 12;
inline bool TimestampDurationTestCases::has_timeval8() const {
  return this != internal_default_instance() && timeval8_ != NULL;
}
inline void TimestampDurationTestCases::clear_timeval8() {
  if (GetArenaNoVirtual() == NULL && timeval8_ != NULL) delete timeval8_;
  timeval8_ = NULL;
}
inline const ::google::protobuf::Timestamp& TimestampDurationTestCases::timeval8() const {
  // @@protoc_insertion_point(field_get:google.protobuf.testing.TimestampDurationTestCases.timeval8)
  return timeval8_ != NULL ? *timeval8_
                         : *::google::protobuf::Timestamp::internal_default_instance();
}
inline ::google::protobuf::Timestamp* TimestampDurationTestCases::mutable_timeval8() {
  
  if (timeval8_ == NULL) {
    timeval8_ = new ::google::protobuf::Timestamp;
  }
  // @@protoc_insertion_point(field_mutable:google.protobuf.testing.TimestampDurationTestCases.timeval8)
  return timeval8_;
}
inline ::google::protobuf::Timestamp* TimestampDurationTestCases::release_timeval8() {
  // @@protoc_insertion_point(field_release:google.protobuf.testing.TimestampDurationTestCases.timeval8)
  
  ::google::protobuf::Timestamp* temp = timeval8_;
  timeval8_ = NULL;
  return temp;
}
inline void TimestampDurationTestCases::set_allocated_timeval8(::google::protobuf::Timestamp* timeval8) {
  delete timeval8_;
  if (timeval8 != NULL && timeval8->GetArena() != NULL) {
    ::google::protobuf::Timestamp* new_timeval8 = new ::google::protobuf::Timestamp;
    new_timeval8->CopyFrom(*timeval8);
    timeval8 = new_timeval8;
  }
  timeval8_ = timeval8;
  if (timeval8) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_set_allocated:google.protobuf.testing.TimestampDurationTestCases.timeval8)
}

// optional .google.protobuf.testing.DurationType zero_duration = 101;
inline bool TimestampDurationTestCases::has_zero_duration() const {
  return this != internal_default_instance() && zero_duration_ != NULL;
}
inline void TimestampDurationTestCases::clear_zero_duration() {
  if (GetArenaNoVirtual() == NULL && zero_duration_ != NULL) delete zero_duration_;
  zero_duration_ = NULL;
}
inline const ::google::protobuf::testing::DurationType& TimestampDurationTestCases::zero_duration() const {
  // @@protoc_insertion_point(field_get:google.protobuf.testing.TimestampDurationTestCases.zero_duration)
  return zero_duration_ != NULL ? *zero_duration_
                         : *::google::protobuf::testing::DurationType::internal_default_instance();
}
inline ::google::protobuf::testing::DurationType* TimestampDurationTestCases::mutable_zero_duration() {
  
  if (zero_duration_ == NULL) {
    zero_duration_ = new ::google::protobuf::testing::DurationType;
  }
  // @@protoc_insertion_point(field_mutable:google.protobuf.testing.TimestampDurationTestCases.zero_duration)
  return zero_duration_;
}
inline ::google::protobuf::testing::DurationType* TimestampDurationTestCases::release_zero_duration() {
  // @@protoc_insertion_point(field_release:google.protobuf.testing.TimestampDurationTestCases.zero_duration)
  
  ::google::protobuf::testing::DurationType* temp = zero_duration_;
  zero_duration_ = NULL;
  return temp;
}
inline void TimestampDurationTestCases::set_allocated_zero_duration(::google::protobuf::testing::DurationType* zero_duration) {
  delete zero_duration_;
  zero_duration_ = zero_duration;
  if (zero_duration) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_set_allocated:google.protobuf.testing.TimestampDurationTestCases.zero_duration)
}

// optional .google.protobuf.testing.DurationType min_duration = 102;
inline bool TimestampDurationTestCases::has_min_duration() const {
  return this != internal_default_instance() && min_duration_ != NULL;
}
inline void TimestampDurationTestCases::clear_min_duration() {
  if (GetArenaNoVirtual() == NULL && min_duration_ != NULL) delete min_duration_;
  min_duration_ = NULL;
}
inline const ::google::protobuf::testing::DurationType& TimestampDurationTestCases::min_duration() const {
  // @@protoc_insertion_point(field_get:google.protobuf.testing.TimestampDurationTestCases.min_duration)
  return min_duration_ != NULL ? *min_duration_
                         : *::google::protobuf::testing::DurationType::internal_default_instance();
}
inline ::google::protobuf::testing::DurationType* TimestampDurationTestCases::mutable_min_duration() {
  
  if (min_duration_ == NULL) {
    min_duration_ = new ::google::protobuf::testing::DurationType;
  }
  // @@protoc_insertion_point(field_mutable:google.protobuf.testing.TimestampDurationTestCases.min_duration)
  return min_duration_;
}
inline ::google::protobuf::testing::DurationType* TimestampDurationTestCases::release_min_duration() {
  // @@protoc_insertion_point(field_release:google.protobuf.testing.TimestampDurationTestCases.min_duration)
  
  ::google::protobuf::testing::DurationType* temp = min_duration_;
  min_duration_ = NULL;
  return temp;
}
inline void TimestampDurationTestCases::set_allocated_min_duration(::google::protobuf::testing::DurationType* min_duration) {
  delete min_duration_;
  min_duration_ = min_duration;
  if (min_duration) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_set_allocated:google.protobuf.testing.TimestampDurationTestCases.min_duration)
}

// optional .google.protobuf.testing.DurationType max_duration = 103;
inline bool TimestampDurationTestCases::has_max_duration() const {
  return this != internal_default_instance() && max_duration_ != NULL;
}
inline void TimestampDurationTestCases::clear_max_duration() {
  if (GetArenaNoVirtual() == NULL && max_duration_ != NULL) delete max_duration_;
  max_duration_ = NULL;
}
inline const ::google::protobuf::testing::DurationType& TimestampDurationTestCases::max_duration() const {
  // @@protoc_insertion_point(field_get:google.protobuf.testing.TimestampDurationTestCases.max_duration)
  return max_duration_ != NULL ? *max_duration_
                         : *::google::protobuf::testing::DurationType::internal_default_instance();
}
inline ::google::protobuf::testing::DurationType* TimestampDurationTestCases::mutable_max_duration() {
  
  if (max_duration_ == NULL) {
    max_duration_ = new ::google::protobuf::testing::DurationType;
  }
  // @@protoc_insertion_point(field_mutable:google.protobuf.testing.TimestampDurationTestCases.max_duration)
  return max_duration_;
}
inline ::google::protobuf::testing::DurationType* TimestampDurationTestCases::release_max_duration() {
  // @@protoc_insertion_point(field_release:google.protobuf.testing.TimestampDurationTestCases.max_duration)
  
  ::google::protobuf::testing::DurationType* temp = max_duration_;
  max_duration_ = NULL;
  return temp;
}
inline void TimestampDurationTestCases::set_allocated_max_duration(::google::protobuf::testing::DurationType* max_duration) {
  delete max_duration_;
  max_duration_ = max_duration;
  if (max_duration) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_set_allocated:google.protobuf.testing.TimestampDurationTestCases.max_duration)
}

// optional .google.protobuf.testing.DurationType duration1 = 104;
inline bool TimestampDurationTestCases::has_duration1() const {
  return this != internal_default_instance() && duration1_ != NULL;
}
inline void TimestampDurationTestCases::clear_duration1() {
  if (GetArenaNoVirtual() == NULL && duration1_ != NULL) delete duration1_;
  duration1_ = NULL;
}
inline const ::google::protobuf::testing::DurationType& TimestampDurationTestCases::duration1() const {
  // @@protoc_insertion_point(field_get:google.protobuf.testing.TimestampDurationTestCases.duration1)
  return duration1_ != NULL ? *duration1_
                         : *::google::protobuf::testing::DurationType::internal_default_instance();
}
inline ::google::protobuf::testing::DurationType* TimestampDurationTestCases::mutable_duration1() {
  
  if (duration1_ == NULL) {
    duration1_ = new ::google::protobuf::testing::DurationType;
  }
  // @@protoc_insertion_point(field_mutable:google.protobuf.testing.TimestampDurationTestCases.duration1)
  return duration1_;
}
inline ::google::protobuf::testing::DurationType* TimestampDurationTestCases::release_duration1() {
  // @@protoc_insertion_point(field_release:google.protobuf.testing.TimestampDurationTestCases.duration1)
  
  ::google::protobuf::testing::DurationType* temp = duration1_;
  duration1_ = NULL;
  return temp;
}
inline void TimestampDurationTestCases::set_allocated_duration1(::google::protobuf::testing::DurationType* duration1) {
  delete duration1_;
  duration1_ = duration1;
  if (duration1) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_set_allocated:google.protobuf.testing.TimestampDurationTestCases.duration1)
}

// optional .google.protobuf.testing.DurationType duration2 = 105;
inline bool TimestampDurationTestCases::has_duration2() const {
  return this != internal_default_instance() && duration2_ != NULL;
}
inline void TimestampDurationTestCases::clear_duration2() {
  if (GetArenaNoVirtual() == NULL && duration2_ != NULL) delete duration2_;
  duration2_ = NULL;
}
inline const ::google::protobuf::testing::DurationType& TimestampDurationTestCases::duration2() const {
  // @@protoc_insertion_point(field_get:google.protobuf.testing.TimestampDurationTestCases.duration2)
  return duration2_ != NULL ? *duration2_
                         : *::google::protobuf::testing::DurationType::internal_default_instance();
}
inline ::google::protobuf::testing::DurationType* TimestampDurationTestCases::mutable_duration2() {
  
  if (duration2_ == NULL) {
    duration2_ = new ::google::protobuf::testing::DurationType;
  }
  // @@protoc_insertion_point(field_mutable:google.protobuf.testing.TimestampDurationTestCases.duration2)
  return duration2_;
}
inline ::google::protobuf::testing::DurationType* TimestampDurationTestCases::release_duration2() {
  // @@protoc_insertion_point(field_release:google.protobuf.testing.TimestampDurationTestCases.duration2)
  
  ::google::protobuf::testing::DurationType* temp = duration2_;
  duration2_ = NULL;
  return temp;
}
inline void TimestampDurationTestCases::set_allocated_duration2(::google::protobuf::testing::DurationType* duration2) {
  delete duration2_;
  duration2_ = duration2;
  if (duration2) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_set_allocated:google.protobuf.testing.TimestampDurationTestCases.duration2)
}

// optional .google.protobuf.testing.DurationType duration3 = 106;
inline bool TimestampDurationTestCases::has_duration3() const {
  return this != internal_default_instance() && duration3_ != NULL;
}
inline void TimestampDurationTestCases::clear_duration3() {
  if (GetArenaNoVirtual() == NULL && duration3_ != NULL) delete duration3_;
  duration3_ = NULL;
}
inline const ::google::protobuf::testing::DurationType& TimestampDurationTestCases::duration3() const {
  // @@protoc_insertion_point(field_get:google.protobuf.testing.TimestampDurationTestCases.duration3)
  return duration3_ != NULL ? *duration3_
                         : *::google::protobuf::testing::DurationType::internal_default_instance();
}
inline ::google::protobuf::testing::DurationType* TimestampDurationTestCases::mutable_duration3() {
  
  if (duration3_ == NULL) {
    duration3_ = new ::google::protobuf::testing::DurationType;
  }
  // @@protoc_insertion_point(field_mutable:google.protobuf.testing.TimestampDurationTestCases.duration3)
  return duration3_;
}
inline ::google::protobuf::testing::DurationType* TimestampDurationTestCases::release_duration3() {
  // @@protoc_insertion_point(field_release:google.protobuf.testing.TimestampDurationTestCases.duration3)
  
  ::google::protobuf::testing::DurationType* temp = duration3_;
  duration3_ = NULL;
  return temp;
}
inline void TimestampDurationTestCases::set_allocated_duration3(::google::protobuf::testing::DurationType* duration3) {
  delete duration3_;
  duration3_ = duration3;
  if (duration3) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_set_allocated:google.protobuf.testing.TimestampDurationTestCases.duration3)
}

// optional .google.protobuf.testing.DurationType duration4 = 107;
inline bool TimestampDurationTestCases::has_duration4() const {
  return this != internal_default_instance() && duration4_ != NULL;
}
inline void TimestampDurationTestCases::clear_duration4() {
  if (GetArenaNoVirtual() == NULL && duration4_ != NULL) delete duration4_;
  duration4_ = NULL;
}
inline const ::google::protobuf::testing::DurationType& TimestampDurationTestCases::duration4() const {
  // @@protoc_insertion_point(field_get:google.protobuf.testing.TimestampDurationTestCases.duration4)
  return duration4_ != NULL ? *duration4_
                         : *::google::protobuf::testing::DurationType::internal_default_instance();
}
inline ::google::protobuf::testing::DurationType* TimestampDurationTestCases::mutable_duration4() {
  
  if (duration4_ == NULL) {
    duration4_ = new ::google::protobuf::testing::DurationType;
  }
  // @@protoc_insertion_point(field_mutable:google.protobuf.testing.TimestampDurationTestCases.duration4)
  return duration4_;
}
inline ::google::protobuf::testing::DurationType* TimestampDurationTestCases::release_duration4() {
  // @@protoc_insertion_point(field_release:google.protobuf.testing.TimestampDurationTestCases.duration4)
  
  ::google::protobuf::testing::DurationType* temp = duration4_;
  duration4_ = NULL;
  return temp;
}
inline void TimestampDurationTestCases::set_allocated_duration4(::google::protobuf::testing::DurationType* duration4) {
  delete duration4_;
  duration4_ = duration4;
  if (duration4) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_set_allocated:google.protobuf.testing.TimestampDurationTestCases.duration4)
}

// optional .google.protobuf.Duration duration5 = 108;
inline bool TimestampDurationTestCases::has_duration5() const {
  return this != internal_default_instance() && duration5_ != NULL;
}
inline void TimestampDurationTestCases::clear_duration5() {
  if (GetArenaNoVirtual() == NULL && duration5_ != NULL) delete duration5_;
  duration5_ = NULL;
}
inline const ::google::protobuf::Duration& TimestampDurationTestCases::duration5() const {
  // @@protoc_insertion_point(field_get:google.protobuf.testing.TimestampDurationTestCases.duration5)
  return duration5_ != NULL ? *duration5_
                         : *::google::protobuf::Duration::internal_default_instance();
}
inline ::google::protobuf::Duration* TimestampDurationTestCases::mutable_duration5() {
  
  if (duration5_ == NULL) {
    duration5_ = new ::google::protobuf::Duration;
  }
  // @@protoc_insertion_point(field_mutable:google.protobuf.testing.TimestampDurationTestCases.duration5)
  return duration5_;
}
inline ::google::protobuf::Duration* TimestampDurationTestCases::release_duration5() {
  // @@protoc_insertion_point(field_release:google.protobuf.testing.TimestampDurationTestCases.duration5)
  
  ::google::protobuf::Duration* temp = duration5_;
  duration5_ = NULL;
  return temp;
}
inline void TimestampDurationTestCases::set_allocated_duration5(::google::protobuf::Duration* duration5) {
  delete duration5_;
  if (duration5 != NULL && duration5->GetArena() != NULL) {
    ::google::protobuf::Duration* new_duration5 = new ::google::protobuf::Duration;
    new_duration5->CopyFrom(*duration5);
    duration5 = new_duration5;
  }
  duration5_ = duration5;
  if (duration5) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_set_allocated:google.protobuf.testing.TimestampDurationTestCases.duration5)
}

inline const TimestampDurationTestCases* TimestampDurationTestCases::internal_default_instance() {
  return &TimestampDurationTestCases_default_instance_.get();
}
// -------------------------------------------------------------------

// TimeStampType

// optional .google.protobuf.Timestamp timestamp = 1;
inline bool TimeStampType::has_timestamp() const {
  return this != internal_default_instance() && timestamp_ != NULL;
}
inline void TimeStampType::clear_timestamp() {
  if (GetArenaNoVirtual() == NULL && timestamp_ != NULL) delete timestamp_;
  timestamp_ = NULL;
}
inline const ::google::protobuf::Timestamp& TimeStampType::timestamp() const {
  // @@protoc_insertion_point(field_get:google.protobuf.testing.TimeStampType.timestamp)
  return timestamp_ != NULL ? *timestamp_
                         : *::google::protobuf::Timestamp::internal_default_instance();
}
inline ::google::protobuf::Timestamp* TimeStampType::mutable_timestamp() {
  
  if (timestamp_ == NULL) {
    timestamp_ = new ::google::protobuf::Timestamp;
  }
  // @@protoc_insertion_point(field_mutable:google.protobuf.testing.TimeStampType.timestamp)
  return timestamp_;
}
inline ::google::protobuf::Timestamp* TimeStampType::release_timestamp() {
  // @@protoc_insertion_point(field_release:google.protobuf.testing.TimeStampType.timestamp)
  
  ::google::protobuf::Timestamp* temp = timestamp_;
  timestamp_ = NULL;
  return temp;
}
inline void TimeStampType::set_allocated_timestamp(::google::protobuf::Timestamp* timestamp) {
  delete timestamp_;
  if (timestamp != NULL && timestamp->GetArena() != NULL) {
    ::google::protobuf::Timestamp* new_timestamp = new ::google::protobuf::Timestamp;
    new_timestamp->CopyFrom(*timestamp);
    timestamp = new_timestamp;
  }
  timestamp_ = timestamp;
  if (timestamp) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_set_allocated:google.protobuf.testing.TimeStampType.timestamp)
}

inline const TimeStampType* TimeStampType::internal_default_instance() {
  return &TimeStampType_default_instance_.get();
}
// -------------------------------------------------------------------

// DurationType

// optional .google.protobuf.Duration duration = 1;
inline bool DurationType::has_duration() const {
  return this != internal_default_instance() && duration_ != NULL;
}
inline void DurationType::clear_duration() {
  if (GetArenaNoVirtual() == NULL && duration_ != NULL) delete duration_;
  duration_ = NULL;
}
inline const ::google::protobuf::Duration& DurationType::duration() const {
  // @@protoc_insertion_point(field_get:google.protobuf.testing.DurationType.duration)
  return duration_ != NULL ? *duration_
                         : *::google::protobuf::Duration::internal_default_instance();
}
inline ::google::protobuf::Duration* DurationType::mutable_duration() {
  
  if (duration_ == NULL) {
    duration_ = new ::google::protobuf::Duration;
  }
  // @@protoc_insertion_point(field_mutable:google.protobuf.testing.DurationType.duration)
  return duration_;
}
inline ::google::protobuf::Duration* DurationType::release_duration() {
  // @@protoc_insertion_point(field_release:google.protobuf.testing.DurationType.duration)
  
  ::google::protobuf::Duration* temp = duration_;
  duration_ = NULL;
  return temp;
}
inline void DurationType::set_allocated_duration(::google::protobuf::Duration* duration) {
  delete duration_;
  if (duration != NULL && duration->GetArena() != NULL) {
    ::google::protobuf::Duration* new_duration = new ::google::protobuf::Duration;
    new_duration->CopyFrom(*duration);
    duration = new_duration;
  }
  duration_ = duration;
  if (duration) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_set_allocated:google.protobuf.testing.DurationType.duration)
}

inline const DurationType* DurationType::internal_default_instance() {
  return &DurationType_default_instance_.get();
}
// -------------------------------------------------------------------

// TimestampDuration

// optional .google.protobuf.Timestamp ts = 1;
inline bool TimestampDuration::has_ts() const {
  return this != internal_default_instance() && ts_ != NULL;
}
inline void TimestampDuration::clear_ts() {
  if (GetArenaNoVirtual() == NULL && ts_ != NULL) delete ts_;
  ts_ = NULL;
}
inline const ::google::protobuf::Timestamp& TimestampDuration::ts() const {
  // @@protoc_insertion_point(field_get:google.protobuf.testing.TimestampDuration.ts)
  return ts_ != NULL ? *ts_
                         : *::google::protobuf::Timestamp::internal_default_instance();
}
inline ::google::protobuf::Timestamp* TimestampDuration::mutable_ts() {
  
  if (ts_ == NULL) {
    ts_ = new ::google::protobuf::Timestamp;
  }
  // @@protoc_insertion_point(field_mutable:google.protobuf.testing.TimestampDuration.ts)
  return ts_;
}
inline ::google::protobuf::Timestamp* TimestampDuration::release_ts() {
  // @@protoc_insertion_point(field_release:google.protobuf.testing.TimestampDuration.ts)
  
  ::google::protobuf::Timestamp* temp = ts_;
  ts_ = NULL;
  return temp;
}
inline void TimestampDuration::set_allocated_ts(::google::protobuf::Timestamp* ts) {
  delete ts_;
  if (ts != NULL && ts->GetArena() != NULL) {
    ::google::protobuf::Timestamp* new_ts = new ::google::protobuf::Timestamp;
    new_ts->CopyFrom(*ts);
    ts = new_ts;
  }
  ts_ = ts;
  if (ts) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_set_allocated:google.protobuf.testing.TimestampDuration.ts)
}

// optional .google.protobuf.Duration dur = 2;
inline bool TimestampDuration::has_dur() const {
  return this != internal_default_instance() && dur_ != NULL;
}
inline void TimestampDuration::clear_dur() {
  if (GetArenaNoVirtual() == NULL && dur_ != NULL) delete dur_;
  dur_ = NULL;
}
inline const ::google::protobuf::Duration& TimestampDuration::dur() const {
  // @@protoc_insertion_point(field_get:google.protobuf.testing.TimestampDuration.dur)
  return dur_ != NULL ? *dur_
                         : *::google::protobuf::Duration::internal_default_instance();
}
inline ::google::protobuf::Duration* TimestampDuration::mutable_dur() {
  
  if (dur_ == NULL) {
    dur_ = new ::google::protobuf::Duration;
  }
  // @@protoc_insertion_point(field_mutable:google.protobuf.testing.TimestampDuration.dur)
  return dur_;
}
inline ::google::protobuf::Duration* TimestampDuration::release_dur() {
  // @@protoc_insertion_point(field_release:google.protobuf.testing.TimestampDuration.dur)
  
  ::google::protobuf::Duration* temp = dur_;
  dur_ = NULL;
  return temp;
}
inline void TimestampDuration::set_allocated_dur(::google::protobuf::Duration* dur) {
  delete dur_;
  if (dur != NULL && dur->GetArena() != NULL) {
    ::google::protobuf::Duration* new_dur = new ::google::protobuf::Duration;
    new_dur->CopyFrom(*dur);
    dur = new_dur;
  }
  dur_ = dur;
  if (dur) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_set_allocated:google.protobuf.testing.TimestampDuration.dur)
}

// repeated .google.protobuf.Timestamp rep_ts = 3;
inline int TimestampDuration::rep_ts_size() const {
  return rep_ts_.size();
}
inline void TimestampDuration::clear_rep_ts() {
  rep_ts_.Clear();
}
inline const ::google::protobuf::Timestamp& TimestampDuration::rep_ts(int index) const {
  // @@protoc_insertion_point(field_get:google.protobuf.testing.TimestampDuration.rep_ts)
  return rep_ts_.Get(index);
}
inline ::google::protobuf::Timestamp* TimestampDuration::mutable_rep_ts(int index) {
  // @@protoc_insertion_point(field_mutable:google.protobuf.testing.TimestampDuration.rep_ts)
  return rep_ts_.Mutable(index);
}
inline ::google::protobuf::Timestamp* TimestampDuration::add_rep_ts() {
  // @@protoc_insertion_point(field_add:google.protobuf.testing.TimestampDuration.rep_ts)
  return rep_ts_.Add();
}
inline ::google::protobuf::RepeatedPtrField< ::google::protobuf::Timestamp >*
TimestampDuration::mutable_rep_ts() {
  // @@protoc_insertion_point(field_mutable_list:google.protobuf.testing.TimestampDuration.rep_ts)
  return &rep_ts_;
}
inline const ::google::protobuf::RepeatedPtrField< ::google::protobuf::Timestamp >&
TimestampDuration::rep_ts() const {
  // @@protoc_insertion_point(field_list:google.protobuf.testing.TimestampDuration.rep_ts)
  return rep_ts_;
}

inline const TimestampDuration* TimestampDuration::internal_default_instance() {
  return &TimestampDuration_default_instance_.get();
}
#endif  // !PROTOBUF_INLINE_NOT_IN_HEADERS
// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------


// @@protoc_insertion_point(namespace_scope)

}  // namespace testing
}  // namespace protobuf
}  // namespace google

// @@protoc_insertion_point(global_scope)

#endif  // PROTOBUF_google_2fprotobuf_2futil_2finternal_2ftestdata_2ftimestamp_5fduration_2eproto__INCLUDED

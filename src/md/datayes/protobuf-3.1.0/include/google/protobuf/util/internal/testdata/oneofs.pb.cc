// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: google/protobuf/util/internal/testdata/oneofs.proto

#define INTERNAL_SUPPRESS_PROTOBUF_FIELD_DEPRECATION
#include <google/protobuf/util/internal/testdata/oneofs.pb.h>

#include <algorithm>

#include <google/protobuf/stubs/common.h>
#include <google/protobuf/stubs/port.h>
#include <google/protobuf/stubs/once.h>
#include <google/protobuf/io/coded_stream.h>
#include <google/protobuf/wire_format_lite_inl.h>
#include <google/protobuf/descriptor.h>
#include <google/protobuf/generated_message_reflection.h>
#include <google/protobuf/reflection_ops.h>
#include <google/protobuf/wire_format.h>
// @@protoc_insertion_point(includes)

namespace google {
namespace protobuf {
namespace testing {
namespace oneofs {

namespace {

const ::google::protobuf::Descriptor* OneOfsRequest_descriptor_ = NULL;
const ::google::protobuf::internal::GeneratedMessageReflection*
  OneOfsRequest_reflection_ = NULL;
struct OneOfsRequestOneofInstance {
  ::google::protobuf::internal::ArenaStringPtr str_data_;
  ::google::protobuf::int32 int_data_;
  const ::google::protobuf::testing::oneofs::Data* message_data_;
  const ::google::protobuf::testing::oneofs::MoreData* more_data_;
  const ::google::protobuf::Struct* struct_data_;
  const ::google::protobuf::Value* value_data_;
  const ::google::protobuf::ListValue* list_value_data_;
  const ::google::protobuf::Timestamp* ts_data_;
}* OneOfsRequest_default_oneof_instance_ = NULL;
const ::google::protobuf::Descriptor* RequestWithSimpleOneof_descriptor_ = NULL;
const ::google::protobuf::internal::GeneratedMessageReflection*
  RequestWithSimpleOneof_reflection_ = NULL;
struct RequestWithSimpleOneofOneofInstance {
  ::google::protobuf::internal::ArenaStringPtr str_data_;
  ::google::protobuf::int32 int_data_;
  const ::google::protobuf::testing::oneofs::Data* message_data_;
  const ::google::protobuf::testing::oneofs::MoreData* more_data_;
}* RequestWithSimpleOneof_default_oneof_instance_ = NULL;
const ::google::protobuf::Descriptor* Data_descriptor_ = NULL;
const ::google::protobuf::internal::GeneratedMessageReflection*
  Data_reflection_ = NULL;
const ::google::protobuf::Descriptor* MoreData_descriptor_ = NULL;
const ::google::protobuf::internal::GeneratedMessageReflection*
  MoreData_reflection_ = NULL;
const ::google::protobuf::Descriptor* Response_descriptor_ = NULL;
const ::google::protobuf::internal::GeneratedMessageReflection*
  Response_reflection_ = NULL;

}  // namespace


void protobuf_AssignDesc_google_2fprotobuf_2futil_2finternal_2ftestdata_2foneofs_2eproto() GOOGLE_ATTRIBUTE_COLD;
void protobuf_AssignDesc_google_2fprotobuf_2futil_2finternal_2ftestdata_2foneofs_2eproto() {
  protobuf_AddDesc_google_2fprotobuf_2futil_2finternal_2ftestdata_2foneofs_2eproto();
  const ::google::protobuf::FileDescriptor* file =
    ::google::protobuf::DescriptorPool::generated_pool()->FindFileByName(
      "google/protobuf/util/internal/testdata/oneofs.proto");
  GOOGLE_CHECK(file != NULL);
  OneOfsRequest_descriptor_ = file->message_type(0);
  static const int OneOfsRequest_offsets_[11] = {
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(OneOfsRequest, value_),
    PROTO2_GENERATED_DEFAULT_ONEOF_FIELD_OFFSET(OneOfsRequest_default_oneof_instance_, str_data_),
    PROTO2_GENERATED_DEFAULT_ONEOF_FIELD_OFFSET(OneOfsRequest_default_oneof_instance_, int_data_),
    PROTO2_GENERATED_DEFAULT_ONEOF_FIELD_OFFSET(OneOfsRequest_default_oneof_instance_, message_data_),
    PROTO2_GENERATED_DEFAULT_ONEOF_FIELD_OFFSET(OneOfsRequest_default_oneof_instance_, more_data_),
    PROTO2_GENERATED_DEFAULT_ONEOF_FIELD_OFFSET(OneOfsRequest_default_oneof_instance_, struct_data_),
    PROTO2_GENERATED_DEFAULT_ONEOF_FIELD_OFFSET(OneOfsRequest_default_oneof_instance_, value_data_),
    PROTO2_GENERATED_DEFAULT_ONEOF_FIELD_OFFSET(OneOfsRequest_default_oneof_instance_, list_value_data_),
    PROTO2_GENERATED_DEFAULT_ONEOF_FIELD_OFFSET(OneOfsRequest_default_oneof_instance_, ts_data_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(OneOfsRequest, any_data_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(OneOfsRequest, data_),
  };
  OneOfsRequest_reflection_ =
    ::google::protobuf::internal::GeneratedMessageReflection::NewGeneratedMessageReflection(
      OneOfsRequest_descriptor_,
      OneOfsRequest::internal_default_instance(),
      OneOfsRequest_offsets_,
      -1,
      -1,
      -1,
      OneOfsRequest_default_oneof_instance_,
      GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(OneOfsRequest, _oneof_case_[0]),
      sizeof(OneOfsRequest),
      GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(OneOfsRequest, _internal_metadata_));
  RequestWithSimpleOneof_descriptor_ = file->message_type(1);
  static const int RequestWithSimpleOneof_offsets_[6] = {
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(RequestWithSimpleOneof, value_),
    PROTO2_GENERATED_DEFAULT_ONEOF_FIELD_OFFSET(RequestWithSimpleOneof_default_oneof_instance_, str_data_),
    PROTO2_GENERATED_DEFAULT_ONEOF_FIELD_OFFSET(RequestWithSimpleOneof_default_oneof_instance_, int_data_),
    PROTO2_GENERATED_DEFAULT_ONEOF_FIELD_OFFSET(RequestWithSimpleOneof_default_oneof_instance_, message_data_),
    PROTO2_GENERATED_DEFAULT_ONEOF_FIELD_OFFSET(RequestWithSimpleOneof_default_oneof_instance_, more_data_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(RequestWithSimpleOneof, data_),
  };
  RequestWithSimpleOneof_reflection_ =
    ::google::protobuf::internal::GeneratedMessageReflection::NewGeneratedMessageReflection(
      RequestWithSimpleOneof_descriptor_,
      RequestWithSimpleOneof::internal_default_instance(),
      RequestWithSimpleOneof_offsets_,
      -1,
      -1,
      -1,
      RequestWithSimpleOneof_default_oneof_instance_,
      GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(RequestWithSimpleOneof, _oneof_case_[0]),
      sizeof(RequestWithSimpleOneof),
      GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(RequestWithSimpleOneof, _internal_metadata_));
  Data_descriptor_ = file->message_type(2);
  static const int Data_offsets_[1] = {
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(Data, data_value_),
  };
  Data_reflection_ =
    ::google::protobuf::internal::GeneratedMessageReflection::NewGeneratedMessageReflection(
      Data_descriptor_,
      Data::internal_default_instance(),
      Data_offsets_,
      -1,
      -1,
      -1,
      sizeof(Data),
      GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(Data, _internal_metadata_));
  MoreData_descriptor_ = file->message_type(3);
  static const int MoreData_offsets_[1] = {
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MoreData, str_value_),
  };
  MoreData_reflection_ =
    ::google::protobuf::internal::GeneratedMessageReflection::NewGeneratedMessageReflection(
      MoreData_descriptor_,
      MoreData::internal_default_instance(),
      MoreData_offsets_,
      -1,
      -1,
      -1,
      sizeof(MoreData),
      GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MoreData, _internal_metadata_));
  Response_descriptor_ = file->message_type(4);
  static const int Response_offsets_[1] = {
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(Response, value_),
  };
  Response_reflection_ =
    ::google::protobuf::internal::GeneratedMessageReflection::NewGeneratedMessageReflection(
      Response_descriptor_,
      Response::internal_default_instance(),
      Response_offsets_,
      -1,
      -1,
      -1,
      sizeof(Response),
      GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(Response, _internal_metadata_));
}

namespace {

GOOGLE_PROTOBUF_DECLARE_ONCE(protobuf_AssignDescriptors_once_);
void protobuf_AssignDescriptorsOnce() {
  ::google::protobuf::GoogleOnceInit(&protobuf_AssignDescriptors_once_,
                 &protobuf_AssignDesc_google_2fprotobuf_2futil_2finternal_2ftestdata_2foneofs_2eproto);
}

void protobuf_RegisterTypes(const ::std::string&) GOOGLE_ATTRIBUTE_COLD;
void protobuf_RegisterTypes(const ::std::string&) {
  protobuf_AssignDescriptorsOnce();
  ::google::protobuf::MessageFactory::InternalRegisterGeneratedMessage(
      OneOfsRequest_descriptor_, OneOfsRequest::internal_default_instance());
  ::google::protobuf::MessageFactory::InternalRegisterGeneratedMessage(
      RequestWithSimpleOneof_descriptor_, RequestWithSimpleOneof::internal_default_instance());
  ::google::protobuf::MessageFactory::InternalRegisterGeneratedMessage(
      Data_descriptor_, Data::internal_default_instance());
  ::google::protobuf::MessageFactory::InternalRegisterGeneratedMessage(
      MoreData_descriptor_, MoreData::internal_default_instance());
  ::google::protobuf::MessageFactory::InternalRegisterGeneratedMessage(
      Response_descriptor_, Response::internal_default_instance());
}

}  // namespace

void protobuf_ShutdownFile_google_2fprotobuf_2futil_2finternal_2ftestdata_2foneofs_2eproto() {
  OneOfsRequest_default_instance_.Shutdown();
  delete OneOfsRequest_default_oneof_instance_;
  delete OneOfsRequest_reflection_;
  RequestWithSimpleOneof_default_instance_.Shutdown();
  delete RequestWithSimpleOneof_default_oneof_instance_;
  delete RequestWithSimpleOneof_reflection_;
  Data_default_instance_.Shutdown();
  delete Data_reflection_;
  MoreData_default_instance_.Shutdown();
  delete MoreData_reflection_;
  Response_default_instance_.Shutdown();
  delete Response_reflection_;
}

void protobuf_InitDefaults_google_2fprotobuf_2futil_2finternal_2ftestdata_2foneofs_2eproto_impl() {
  GOOGLE_PROTOBUF_VERIFY_VERSION;

  ::google::protobuf::protobuf_InitDefaults_google_2fprotobuf_2fany_2eproto();
  ::google::protobuf::protobuf_InitDefaults_google_2fprotobuf_2fstruct_2eproto();
  ::google::protobuf::protobuf_InitDefaults_google_2fprotobuf_2ftimestamp_2eproto();
  ::google::protobuf::internal::GetEmptyString();
  OneOfsRequest_default_instance_.DefaultConstruct();
  OneOfsRequest_default_oneof_instance_ = new OneOfsRequestOneofInstance();
  ::google::protobuf::internal::GetEmptyString();
  RequestWithSimpleOneof_default_instance_.DefaultConstruct();
  RequestWithSimpleOneof_default_oneof_instance_ = new RequestWithSimpleOneofOneofInstance();
  Data_default_instance_.DefaultConstruct();
  ::google::protobuf::internal::GetEmptyString();
  MoreData_default_instance_.DefaultConstruct();
  ::google::protobuf::internal::GetEmptyString();
  Response_default_instance_.DefaultConstruct();
  OneOfsRequest_default_instance_.get_mutable()->InitAsDefaultInstance();
  RequestWithSimpleOneof_default_instance_.get_mutable()->InitAsDefaultInstance();
  Data_default_instance_.get_mutable()->InitAsDefaultInstance();
  MoreData_default_instance_.get_mutable()->InitAsDefaultInstance();
  Response_default_instance_.get_mutable()->InitAsDefaultInstance();
}

GOOGLE_PROTOBUF_DECLARE_ONCE(protobuf_InitDefaults_google_2fprotobuf_2futil_2finternal_2ftestdata_2foneofs_2eproto_once_);
void protobuf_InitDefaults_google_2fprotobuf_2futil_2finternal_2ftestdata_2foneofs_2eproto() {
  ::google::protobuf::GoogleOnceInit(&protobuf_InitDefaults_google_2fprotobuf_2futil_2finternal_2ftestdata_2foneofs_2eproto_once_,
                 &protobuf_InitDefaults_google_2fprotobuf_2futil_2finternal_2ftestdata_2foneofs_2eproto_impl);
}
void protobuf_AddDesc_google_2fprotobuf_2futil_2finternal_2ftestdata_2foneofs_2eproto_impl() {
  GOOGLE_PROTOBUF_VERIFY_VERSION;

  protobuf_InitDefaults_google_2fprotobuf_2futil_2finternal_2ftestdata_2foneofs_2eproto();
  ::google::protobuf::DescriptorPool::InternalAddGeneratedFile(
    "\n3google/protobuf/util/internal/testdata"
    "/oneofs.proto\022\036google.protobuf.testing.o"
    "neofs\032\031google/protobuf/any.proto\032\034google"
    "/protobuf/struct.proto\032\037google/protobuf/"
    "timestamp.proto\"\267\003\n\rOneOfsRequest\022\r\n\005val"
    "ue\030\001 \001(\t\022\022\n\010str_data\030\002 \001(\tH\000\022\022\n\010int_data"
    "\030\003 \001(\005H\000\022<\n\014message_data\030\004 \001(\0132$.google."
    "protobuf.testing.oneofs.DataH\000\022=\n\tmore_d"
    "ata\030\005 \001(\0132(.google.protobuf.testing.oneo"
    "fs.MoreDataH\000\022.\n\013struct_data\030\006 \001(\0132\027.goo"
    "gle.protobuf.StructH\000\022,\n\nvalue_data\030\007 \001("
    "\0132\026.google.protobuf.ValueH\000\0225\n\017list_valu"
    "e_data\030\010 \001(\0132\032.google.protobuf.ListValue"
    "H\000\022-\n\007ts_data\030\t \001(\0132\032.google.protobuf.Ti"
    "mestampH\000\022&\n\010any_data\030\023 \001(\0132\024.google.pro"
    "tobuf.AnyB\006\n\004data\"\324\001\n\026RequestWithSimpleO"
    "neof\022\r\n\005value\030\001 \001(\t\022\022\n\010str_data\030\002 \001(\tH\000\022"
    "\022\n\010int_data\030\003 \001(\005H\000\022<\n\014message_data\030\004 \001("
    "\0132$.google.protobuf.testing.oneofs.DataH"
    "\000\022=\n\tmore_data\030\005 \001(\0132(.google.protobuf.t"
    "esting.oneofs.MoreDataH\000B\006\n\004data\"\032\n\004Data"
    "\022\022\n\ndata_value\030\001 \001(\005\"\035\n\010MoreData\022\021\n\tstr_"
    "value\030\001 \001(\t\"\031\n\010Response\022\r\n\005value\030\001 \001(\tb\006"
    "proto3", 926);
  ::google::protobuf::MessageFactory::InternalRegisterGeneratedFile(
    "google/protobuf/util/internal/testdata/oneofs.proto", &protobuf_RegisterTypes);
  ::google::protobuf::protobuf_AddDesc_google_2fprotobuf_2fany_2eproto();
  ::google::protobuf::protobuf_AddDesc_google_2fprotobuf_2fstruct_2eproto();
  ::google::protobuf::protobuf_AddDesc_google_2fprotobuf_2ftimestamp_2eproto();
  ::google::protobuf::internal::OnShutdown(&protobuf_ShutdownFile_google_2fprotobuf_2futil_2finternal_2ftestdata_2foneofs_2eproto);
}

GOOGLE_PROTOBUF_DECLARE_ONCE(protobuf_AddDesc_google_2fprotobuf_2futil_2finternal_2ftestdata_2foneofs_2eproto_once_);
void protobuf_AddDesc_google_2fprotobuf_2futil_2finternal_2ftestdata_2foneofs_2eproto() {
  ::google::protobuf::GoogleOnceInit(&protobuf_AddDesc_google_2fprotobuf_2futil_2finternal_2ftestdata_2foneofs_2eproto_once_,
                 &protobuf_AddDesc_google_2fprotobuf_2futil_2finternal_2ftestdata_2foneofs_2eproto_impl);
}
// Force AddDescriptors() to be called at static initialization time.
struct StaticDescriptorInitializer_google_2fprotobuf_2futil_2finternal_2ftestdata_2foneofs_2eproto {
  StaticDescriptorInitializer_google_2fprotobuf_2futil_2finternal_2ftestdata_2foneofs_2eproto() {
    protobuf_AddDesc_google_2fprotobuf_2futil_2finternal_2ftestdata_2foneofs_2eproto();
  }
} static_descriptor_initializer_google_2fprotobuf_2futil_2finternal_2ftestdata_2foneofs_2eproto_;

namespace {

static void MergeFromFail(int line) GOOGLE_ATTRIBUTE_COLD GOOGLE_ATTRIBUTE_NORETURN;
static void MergeFromFail(int line) {
  ::google::protobuf::internal::MergeFromFail(__FILE__, line);
}

}  // namespace


// ===================================================================

#if !defined(_MSC_VER) || _MSC_VER >= 1900
const int OneOfsRequest::kValueFieldNumber;
const int OneOfsRequest::kStrDataFieldNumber;
const int OneOfsRequest::kIntDataFieldNumber;
const int OneOfsRequest::kMessageDataFieldNumber;
const int OneOfsRequest::kMoreDataFieldNumber;
const int OneOfsRequest::kStructDataFieldNumber;
const int OneOfsRequest::kValueDataFieldNumber;
const int OneOfsRequest::kListValueDataFieldNumber;
const int OneOfsRequest::kTsDataFieldNumber;
const int OneOfsRequest::kAnyDataFieldNumber;
#endif  // !defined(_MSC_VER) || _MSC_VER >= 1900

OneOfsRequest::OneOfsRequest()
  : ::google::protobuf::Message(), _internal_metadata_(NULL) {
  if (this != internal_default_instance()) protobuf_InitDefaults_google_2fprotobuf_2futil_2finternal_2ftestdata_2foneofs_2eproto();
  SharedCtor();
  // @@protoc_insertion_point(constructor:google.protobuf.testing.oneofs.OneOfsRequest)
}

void OneOfsRequest::InitAsDefaultInstance() {
  OneOfsRequest_default_oneof_instance_->str_data_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  OneOfsRequest_default_oneof_instance_->int_data_ = 0;
  OneOfsRequest_default_oneof_instance_->message_data_ = const_cast< ::google::protobuf::testing::oneofs::Data*>(
      ::google::protobuf::testing::oneofs::Data::internal_default_instance());
  OneOfsRequest_default_oneof_instance_->more_data_ = const_cast< ::google::protobuf::testing::oneofs::MoreData*>(
      ::google::protobuf::testing::oneofs::MoreData::internal_default_instance());
  OneOfsRequest_default_oneof_instance_->struct_data_ = const_cast< ::google::protobuf::Struct*>(
      ::google::protobuf::Struct::internal_default_instance());
  OneOfsRequest_default_oneof_instance_->value_data_ = const_cast< ::google::protobuf::Value*>(
      ::google::protobuf::Value::internal_default_instance());
  OneOfsRequest_default_oneof_instance_->list_value_data_ = const_cast< ::google::protobuf::ListValue*>(
      ::google::protobuf::ListValue::internal_default_instance());
  OneOfsRequest_default_oneof_instance_->ts_data_ = const_cast< ::google::protobuf::Timestamp*>(
      ::google::protobuf::Timestamp::internal_default_instance());
  any_data_ = const_cast< ::google::protobuf::Any*>(
      ::google::protobuf::Any::internal_default_instance());
}

OneOfsRequest::OneOfsRequest(const OneOfsRequest& from)
  : ::google::protobuf::Message(),
    _internal_metadata_(NULL) {
  SharedCtor();
  UnsafeMergeFrom(from);
  // @@protoc_insertion_point(copy_constructor:google.protobuf.testing.oneofs.OneOfsRequest)
}

void OneOfsRequest::SharedCtor() {
  value_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  any_data_ = NULL;
  clear_has_data();
  _cached_size_ = 0;
}

OneOfsRequest::~OneOfsRequest() {
  // @@protoc_insertion_point(destructor:google.protobuf.testing.oneofs.OneOfsRequest)
  SharedDtor();
}

void OneOfsRequest::SharedDtor() {
  value_.DestroyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  if (has_data()) {
    clear_data();
  }
  if (this != &OneOfsRequest_default_instance_.get()) {
    delete any_data_;
  }
}

void OneOfsRequest::SetCachedSize(int size) const {
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
}
const ::google::protobuf::Descriptor* OneOfsRequest::descriptor() {
  protobuf_AssignDescriptorsOnce();
  return OneOfsRequest_descriptor_;
}

const OneOfsRequest& OneOfsRequest::default_instance() {
  protobuf_InitDefaults_google_2fprotobuf_2futil_2finternal_2ftestdata_2foneofs_2eproto();
  return *internal_default_instance();
}

::google::protobuf::internal::ExplicitlyConstructed<OneOfsRequest> OneOfsRequest_default_instance_;

OneOfsRequest* OneOfsRequest::New(::google::protobuf::Arena* arena) const {
  OneOfsRequest* n = new OneOfsRequest;
  if (arena != NULL) {
    arena->Own(n);
  }
  return n;
}

void OneOfsRequest::clear_data() {
// @@protoc_insertion_point(one_of_clear_start:google.protobuf.testing.oneofs.OneOfsRequest)
  switch (data_case()) {
    case kStrData: {
      data_.str_data_.DestroyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
      break;
    }
    case kIntData: {
      // No need to clear
      break;
    }
    case kMessageData: {
      delete data_.message_data_;
      break;
    }
    case kMoreData: {
      delete data_.more_data_;
      break;
    }
    case kStructData: {
      delete data_.struct_data_;
      break;
    }
    case kValueData: {
      delete data_.value_data_;
      break;
    }
    case kListValueData: {
      delete data_.list_value_data_;
      break;
    }
    case kTsData: {
      delete data_.ts_data_;
      break;
    }
    case DATA_NOT_SET: {
      break;
    }
  }
  _oneof_case_[0] = DATA_NOT_SET;
}


void OneOfsRequest::Clear() {
// @@protoc_insertion_point(message_clear_start:google.protobuf.testing.oneofs.OneOfsRequest)
  value_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  if (GetArenaNoVirtual() == NULL && any_data_ != NULL) delete any_data_;
  any_data_ = NULL;
  clear_data();
}

bool OneOfsRequest::MergePartialFromCodedStream(
    ::google::protobuf::io::CodedInputStream* input) {
#define DO_(EXPRESSION) if (!GOOGLE_PREDICT_TRUE(EXPRESSION)) goto failure
  ::google::protobuf::uint32 tag;
  // @@protoc_insertion_point(parse_start:google.protobuf.testing.oneofs.OneOfsRequest)
  for (;;) {
    ::std::pair< ::google::protobuf::uint32, bool> p = input->ReadTagWithCutoff(16383);
    tag = p.first;
    if (!p.second) goto handle_unusual;
    switch (::google::protobuf::internal::WireFormatLite::GetTagFieldNumber(tag)) {
      // optional string value = 1;
      case 1: {
        if (tag == 10) {
          DO_(::google::protobuf::internal::WireFormatLite::ReadString(
                input, this->mutable_value()));
          DO_(::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
            this->value().data(), this->value().length(),
            ::google::protobuf::internal::WireFormatLite::PARSE,
            "google.protobuf.testing.oneofs.OneOfsRequest.value"));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(18)) goto parse_str_data;
        break;
      }

      // optional string str_data = 2;
      case 2: {
        if (tag == 18) {
         parse_str_data:
          DO_(::google::protobuf::internal::WireFormatLite::ReadString(
                input, this->mutable_str_data()));
          DO_(::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
            this->str_data().data(), this->str_data().length(),
            ::google::protobuf::internal::WireFormatLite::PARSE,
            "google.protobuf.testing.oneofs.OneOfsRequest.str_data"));
        } else {
          goto handle_unusual;
        }
        goto after_ts_data;
        break;
      }

      // optional int32 int_data = 3;
      case 3: {
        if (tag == 24) {
          clear_data();
          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int32, ::google::protobuf::internal::WireFormatLite::TYPE_INT32>(
                 input, &data_.int_data_)));
          set_has_int_data();
        } else {
          goto handle_unusual;
        }
        goto after_ts_data;
        break;
      }

      // optional .google.protobuf.testing.oneofs.Data message_data = 4;
      case 4: {
        if (tag == 34) {
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessageNoVirtual(
               input, mutable_message_data()));
        } else {
          goto handle_unusual;
        }
        goto after_ts_data;
        break;
      }

      // optional .google.protobuf.testing.oneofs.MoreData more_data = 5;
      case 5: {
        if (tag == 42) {
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessageNoVirtual(
               input, mutable_more_data()));
        } else {
          goto handle_unusual;
        }
        goto after_ts_data;
        break;
      }

      // optional .google.protobuf.Struct struct_data = 6;
      case 6: {
        if (tag == 50) {
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessageNoVirtual(
               input, mutable_struct_data()));
        } else {
          goto handle_unusual;
        }
        goto after_ts_data;
        break;
      }

      // optional .google.protobuf.Value value_data = 7;
      case 7: {
        if (tag == 58) {
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessageNoVirtual(
               input, mutable_value_data()));
        } else {
          goto handle_unusual;
        }
        goto after_ts_data;
        break;
      }

      // optional .google.protobuf.ListValue list_value_data = 8;
      case 8: {
        if (tag == 66) {
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessageNoVirtual(
               input, mutable_list_value_data()));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(74)) goto parse_ts_data;
        break;
      }

      // optional .google.protobuf.Timestamp ts_data = 9;
      case 9: {
        if (tag == 74) {
         parse_ts_data:
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessageNoVirtual(
               input, mutable_ts_data()));
        } else {
          goto handle_unusual;
        }
       after_ts_data:
        if (input->ExpectTag(154)) goto parse_any_data;
        break;
      }

      // optional .google.protobuf.Any any_data = 19;
      case 19: {
        if (tag == 154) {
         parse_any_data:
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessageNoVirtual(
               input, mutable_any_data()));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectAtEnd()) goto success;
        break;
      }

      default: {
      handle_unusual:
        if (tag == 0 ||
            ::google::protobuf::internal::WireFormatLite::GetTagWireType(tag) ==
            ::google::protobuf::internal::WireFormatLite::WIRETYPE_END_GROUP) {
          goto success;
        }
        DO_(::google::protobuf::internal::WireFormatLite::SkipField(input, tag));
        break;
      }
    }
  }
success:
  // @@protoc_insertion_point(parse_success:google.protobuf.testing.oneofs.OneOfsRequest)
  return true;
failure:
  // @@protoc_insertion_point(parse_failure:google.protobuf.testing.oneofs.OneOfsRequest)
  return false;
#undef DO_
}

void OneOfsRequest::SerializeWithCachedSizes(
    ::google::protobuf::io::CodedOutputStream* output) const {
  // @@protoc_insertion_point(serialize_start:google.protobuf.testing.oneofs.OneOfsRequest)
  // optional string value = 1;
  if (this->value().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->value().data(), this->value().length(),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "google.protobuf.testing.oneofs.OneOfsRequest.value");
    ::google::protobuf::internal::WireFormatLite::WriteStringMaybeAliased(
      1, this->value(), output);
  }

  // optional string str_data = 2;
  if (has_str_data()) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->str_data().data(), this->str_data().length(),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "google.protobuf.testing.oneofs.OneOfsRequest.str_data");
    ::google::protobuf::internal::WireFormatLite::WriteStringMaybeAliased(
      2, this->str_data(), output);
  }

  // optional int32 int_data = 3;
  if (has_int_data()) {
    ::google::protobuf::internal::WireFormatLite::WriteInt32(3, this->int_data(), output);
  }

  // optional .google.protobuf.testing.oneofs.Data message_data = 4;
  if (has_message_data()) {
    ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
      4, *data_.message_data_, output);
  }

  // optional .google.protobuf.testing.oneofs.MoreData more_data = 5;
  if (has_more_data()) {
    ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
      5, *data_.more_data_, output);
  }

  // optional .google.protobuf.Struct struct_data = 6;
  if (has_struct_data()) {
    ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
      6, *data_.struct_data_, output);
  }

  // optional .google.protobuf.Value value_data = 7;
  if (has_value_data()) {
    ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
      7, *data_.value_data_, output);
  }

  // optional .google.protobuf.ListValue list_value_data = 8;
  if (has_list_value_data()) {
    ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
      8, *data_.list_value_data_, output);
  }

  // optional .google.protobuf.Timestamp ts_data = 9;
  if (has_ts_data()) {
    ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
      9, *data_.ts_data_, output);
  }

  // optional .google.protobuf.Any any_data = 19;
  if (this->has_any_data()) {
    ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
      19, *this->any_data_, output);
  }

  // @@protoc_insertion_point(serialize_end:google.protobuf.testing.oneofs.OneOfsRequest)
}

::google::protobuf::uint8* OneOfsRequest::InternalSerializeWithCachedSizesToArray(
    bool deterministic, ::google::protobuf::uint8* target) const {
  (void)deterministic; // Unused
  // @@protoc_insertion_point(serialize_to_array_start:google.protobuf.testing.oneofs.OneOfsRequest)
  // optional string value = 1;
  if (this->value().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->value().data(), this->value().length(),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "google.protobuf.testing.oneofs.OneOfsRequest.value");
    target =
      ::google::protobuf::internal::WireFormatLite::WriteStringToArray(
        1, this->value(), target);
  }

  // optional string str_data = 2;
  if (has_str_data()) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->str_data().data(), this->str_data().length(),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "google.protobuf.testing.oneofs.OneOfsRequest.str_data");
    target =
      ::google::protobuf::internal::WireFormatLite::WriteStringToArray(
        2, this->str_data(), target);
  }

  // optional int32 int_data = 3;
  if (has_int_data()) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt32ToArray(3, this->int_data(), target);
  }

  // optional .google.protobuf.testing.oneofs.Data message_data = 4;
  if (has_message_data()) {
    target = ::google::protobuf::internal::WireFormatLite::
      InternalWriteMessageNoVirtualToArray(
        4, *data_.message_data_, false, target);
  }

  // optional .google.protobuf.testing.oneofs.MoreData more_data = 5;
  if (has_more_data()) {
    target = ::google::protobuf::internal::WireFormatLite::
      InternalWriteMessageNoVirtualToArray(
        5, *data_.more_data_, false, target);
  }

  // optional .google.protobuf.Struct struct_data = 6;
  if (has_struct_data()) {
    target = ::google::protobuf::internal::WireFormatLite::
      InternalWriteMessageNoVirtualToArray(
        6, *data_.struct_data_, false, target);
  }

  // optional .google.protobuf.Value value_data = 7;
  if (has_value_data()) {
    target = ::google::protobuf::internal::WireFormatLite::
      InternalWriteMessageNoVirtualToArray(
        7, *data_.value_data_, false, target);
  }

  // optional .google.protobuf.ListValue list_value_data = 8;
  if (has_list_value_data()) {
    target = ::google::protobuf::internal::WireFormatLite::
      InternalWriteMessageNoVirtualToArray(
        8, *data_.list_value_data_, false, target);
  }

  // optional .google.protobuf.Timestamp ts_data = 9;
  if (has_ts_data()) {
    target = ::google::protobuf::internal::WireFormatLite::
      InternalWriteMessageNoVirtualToArray(
        9, *data_.ts_data_, false, target);
  }

  // optional .google.protobuf.Any any_data = 19;
  if (this->has_any_data()) {
    target = ::google::protobuf::internal::WireFormatLite::
      InternalWriteMessageNoVirtualToArray(
        19, *this->any_data_, false, target);
  }

  // @@protoc_insertion_point(serialize_to_array_end:google.protobuf.testing.oneofs.OneOfsRequest)
  return target;
}

size_t OneOfsRequest::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:google.protobuf.testing.oneofs.OneOfsRequest)
  size_t total_size = 0;

  // optional string value = 1;
  if (this->value().size() > 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::StringSize(
        this->value());
  }

  // optional .google.protobuf.Any any_data = 19;
  if (this->has_any_data()) {
    total_size += 2 +
      ::google::protobuf::internal::WireFormatLite::MessageSizeNoVirtual(
        *this->any_data_);
  }

  switch (data_case()) {
    // optional string str_data = 2;
    case kStrData: {
      total_size += 1 +
        ::google::protobuf::internal::WireFormatLite::StringSize(
          this->str_data());
      break;
    }
    // optional int32 int_data = 3;
    case kIntData: {
      total_size += 1 +
        ::google::protobuf::internal::WireFormatLite::Int32Size(
          this->int_data());
      break;
    }
    // optional .google.protobuf.testing.oneofs.Data message_data = 4;
    case kMessageData: {
      total_size += 1 +
        ::google::protobuf::internal::WireFormatLite::MessageSizeNoVirtual(
          *data_.message_data_);
      break;
    }
    // optional .google.protobuf.testing.oneofs.MoreData more_data = 5;
    case kMoreData: {
      total_size += 1 +
        ::google::protobuf::internal::WireFormatLite::MessageSizeNoVirtual(
          *data_.more_data_);
      break;
    }
    // optional .google.protobuf.Struct struct_data = 6;
    case kStructData: {
      total_size += 1 +
        ::google::protobuf::internal::WireFormatLite::MessageSizeNoVirtual(
          *data_.struct_data_);
      break;
    }
    // optional .google.protobuf.Value value_data = 7;
    case kValueData: {
      total_size += 1 +
        ::google::protobuf::internal::WireFormatLite::MessageSizeNoVirtual(
          *data_.value_data_);
      break;
    }
    // optional .google.protobuf.ListValue list_value_data = 8;
    case kListValueData: {
      total_size += 1 +
        ::google::protobuf::internal::WireFormatLite::MessageSizeNoVirtual(
          *data_.list_value_data_);
      break;
    }
    // optional .google.protobuf.Timestamp ts_data = 9;
    case kTsData: {
      total_size += 1 +
        ::google::protobuf::internal::WireFormatLite::MessageSizeNoVirtual(
          *data_.ts_data_);
      break;
    }
    case DATA_NOT_SET: {
      break;
    }
  }
  int cached_size = ::google::protobuf::internal::ToCachedSize(total_size);
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = cached_size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
  return total_size;
}

void OneOfsRequest::MergeFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_merge_from_start:google.protobuf.testing.oneofs.OneOfsRequest)
  if (GOOGLE_PREDICT_FALSE(&from == this)) MergeFromFail(__LINE__);
  const OneOfsRequest* source =
      ::google::protobuf::internal::DynamicCastToGenerated<const OneOfsRequest>(
          &from);
  if (source == NULL) {
  // @@protoc_insertion_point(generalized_merge_from_cast_fail:google.protobuf.testing.oneofs.OneOfsRequest)
    ::google::protobuf::internal::ReflectionOps::Merge(from, this);
  } else {
  // @@protoc_insertion_point(generalized_merge_from_cast_success:google.protobuf.testing.oneofs.OneOfsRequest)
    UnsafeMergeFrom(*source);
  }
}

void OneOfsRequest::MergeFrom(const OneOfsRequest& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:google.protobuf.testing.oneofs.OneOfsRequest)
  if (GOOGLE_PREDICT_TRUE(&from != this)) {
    UnsafeMergeFrom(from);
  } else {
    MergeFromFail(__LINE__);
  }
}

void OneOfsRequest::UnsafeMergeFrom(const OneOfsRequest& from) {
  GOOGLE_DCHECK(&from != this);
  switch (from.data_case()) {
    case kStrData: {
      set_str_data(from.str_data());
      break;
    }
    case kIntData: {
      set_int_data(from.int_data());
      break;
    }
    case kMessageData: {
      mutable_message_data()->::google::protobuf::testing::oneofs::Data::MergeFrom(from.message_data());
      break;
    }
    case kMoreData: {
      mutable_more_data()->::google::protobuf::testing::oneofs::MoreData::MergeFrom(from.more_data());
      break;
    }
    case kStructData: {
      mutable_struct_data()->::google::protobuf::Struct::MergeFrom(from.struct_data());
      break;
    }
    case kValueData: {
      mutable_value_data()->::google::protobuf::Value::MergeFrom(from.value_data());
      break;
    }
    case kListValueData: {
      mutable_list_value_data()->::google::protobuf::ListValue::MergeFrom(from.list_value_data());
      break;
    }
    case kTsData: {
      mutable_ts_data()->::google::protobuf::Timestamp::MergeFrom(from.ts_data());
      break;
    }
    case DATA_NOT_SET: {
      break;
    }
  }
  if (from.value().size() > 0) {

    value_.AssignWithDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), from.value_);
  }
  if (from.has_any_data()) {
    mutable_any_data()->::google::protobuf::Any::MergeFrom(from.any_data());
  }
}

void OneOfsRequest::CopyFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_copy_from_start:google.protobuf.testing.oneofs.OneOfsRequest)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

void OneOfsRequest::CopyFrom(const OneOfsRequest& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:google.protobuf.testing.oneofs.OneOfsRequest)
  if (&from == this) return;
  Clear();
  UnsafeMergeFrom(from);
}

bool OneOfsRequest::IsInitialized() const {

  return true;
}

void OneOfsRequest::Swap(OneOfsRequest* other) {
  if (other == this) return;
  InternalSwap(other);
}
void OneOfsRequest::InternalSwap(OneOfsRequest* other) {
  value_.Swap(&other->value_);
  std::swap(any_data_, other->any_data_);
  std::swap(data_, other->data_);
  std::swap(_oneof_case_[0], other->_oneof_case_[0]);
  _internal_metadata_.Swap(&other->_internal_metadata_);
  std::swap(_cached_size_, other->_cached_size_);
}

::google::protobuf::Metadata OneOfsRequest::GetMetadata() const {
  protobuf_AssignDescriptorsOnce();
  ::google::protobuf::Metadata metadata;
  metadata.descriptor = OneOfsRequest_descriptor_;
  metadata.reflection = OneOfsRequest_reflection_;
  return metadata;
}

#if PROTOBUF_INLINE_NOT_IN_HEADERS
// OneOfsRequest

// optional string value = 1;
void OneOfsRequest::clear_value() {
  value_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
const ::std::string& OneOfsRequest::value() const {
  // @@protoc_insertion_point(field_get:google.protobuf.testing.oneofs.OneOfsRequest.value)
  return value_.GetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
void OneOfsRequest::set_value(const ::std::string& value) {
  
  value_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:google.protobuf.testing.oneofs.OneOfsRequest.value)
}
void OneOfsRequest::set_value(const char* value) {
  
  value_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:google.protobuf.testing.oneofs.OneOfsRequest.value)
}
void OneOfsRequest::set_value(const char* value, size_t size) {
  
  value_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:google.protobuf.testing.oneofs.OneOfsRequest.value)
}
::std::string* OneOfsRequest::mutable_value() {
  
  // @@protoc_insertion_point(field_mutable:google.protobuf.testing.oneofs.OneOfsRequest.value)
  return value_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
::std::string* OneOfsRequest::release_value() {
  // @@protoc_insertion_point(field_release:google.protobuf.testing.oneofs.OneOfsRequest.value)
  
  return value_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
void OneOfsRequest::set_allocated_value(::std::string* value) {
  if (value != NULL) {
    
  } else {
    
  }
  value_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set_allocated:google.protobuf.testing.oneofs.OneOfsRequest.value)
}

// optional string str_data = 2;
bool OneOfsRequest::has_str_data() const {
  return data_case() == kStrData;
}
void OneOfsRequest::set_has_str_data() {
  _oneof_case_[0] = kStrData;
}
void OneOfsRequest::clear_str_data() {
  if (has_str_data()) {
    data_.str_data_.DestroyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
    clear_has_data();
  }
}
const ::std::string& OneOfsRequest::str_data() const {
  // @@protoc_insertion_point(field_get:google.protobuf.testing.oneofs.OneOfsRequest.str_data)
  if (has_str_data()) {
    return data_.str_data_.GetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  }
  return *&::google::protobuf::internal::GetEmptyStringAlreadyInited();
}
void OneOfsRequest::set_str_data(const ::std::string& value) {
  // @@protoc_insertion_point(field_set:google.protobuf.testing.oneofs.OneOfsRequest.str_data)
  if (!has_str_data()) {
    clear_data();
    set_has_str_data();
    data_.str_data_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  }
  data_.str_data_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:google.protobuf.testing.oneofs.OneOfsRequest.str_data)
}
void OneOfsRequest::set_str_data(const char* value) {
  if (!has_str_data()) {
    clear_data();
    set_has_str_data();
    data_.str_data_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  }
  data_.str_data_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(value));
  // @@protoc_insertion_point(field_set_char:google.protobuf.testing.oneofs.OneOfsRequest.str_data)
}
void OneOfsRequest::set_str_data(const char* value, size_t size) {
  if (!has_str_data()) {
    clear_data();
    set_has_str_data();
    data_.str_data_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  }
  data_.str_data_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(
      reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:google.protobuf.testing.oneofs.OneOfsRequest.str_data)
}
::std::string* OneOfsRequest::mutable_str_data() {
  if (!has_str_data()) {
    clear_data();
    set_has_str_data();
    data_.str_data_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  }
  // @@protoc_insertion_point(field_mutable:google.protobuf.testing.oneofs.OneOfsRequest.str_data)
  return data_.str_data_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
::std::string* OneOfsRequest::release_str_data() {
  // @@protoc_insertion_point(field_release:google.protobuf.testing.oneofs.OneOfsRequest.str_data)
  if (has_str_data()) {
    clear_has_data();
    return data_.str_data_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  } else {
    return NULL;
  }
}
void OneOfsRequest::set_allocated_str_data(::std::string* str_data) {
  if (!has_str_data()) {
    data_.str_data_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  }
  clear_data();
  if (str_data != NULL) {
    set_has_str_data();
    data_.str_data_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
        str_data);
  }
  // @@protoc_insertion_point(field_set_allocated:google.protobuf.testing.oneofs.OneOfsRequest.str_data)
}

// optional int32 int_data = 3;
bool OneOfsRequest::has_int_data() const {
  return data_case() == kIntData;
}
void OneOfsRequest::set_has_int_data() {
  _oneof_case_[0] = kIntData;
}
void OneOfsRequest::clear_int_data() {
  if (has_int_data()) {
    data_.int_data_ = 0;
    clear_has_data();
  }
}
::google::protobuf::int32 OneOfsRequest::int_data() const {
  // @@protoc_insertion_point(field_get:google.protobuf.testing.oneofs.OneOfsRequest.int_data)
  if (has_int_data()) {
    return data_.int_data_;
  }
  return 0;
}
void OneOfsRequest::set_int_data(::google::protobuf::int32 value) {
  if (!has_int_data()) {
    clear_data();
    set_has_int_data();
  }
  data_.int_data_ = value;
  // @@protoc_insertion_point(field_set:google.protobuf.testing.oneofs.OneOfsRequest.int_data)
}

// optional .google.protobuf.testing.oneofs.Data message_data = 4;
bool OneOfsRequest::has_message_data() const {
  return data_case() == kMessageData;
}
void OneOfsRequest::set_has_message_data() {
  _oneof_case_[0] = kMessageData;
}
void OneOfsRequest::clear_message_data() {
  if (has_message_data()) {
    delete data_.message_data_;
    clear_has_data();
  }
}
 const ::google::protobuf::testing::oneofs::Data& OneOfsRequest::message_data() const {
  // @@protoc_insertion_point(field_get:google.protobuf.testing.oneofs.OneOfsRequest.message_data)
  return has_message_data()
      ? *data_.message_data_
      : ::google::protobuf::testing::oneofs::Data::default_instance();
}
::google::protobuf::testing::oneofs::Data* OneOfsRequest::mutable_message_data() {
  if (!has_message_data()) {
    clear_data();
    set_has_message_data();
    data_.message_data_ = new ::google::protobuf::testing::oneofs::Data;
  }
  // @@protoc_insertion_point(field_mutable:google.protobuf.testing.oneofs.OneOfsRequest.message_data)
  return data_.message_data_;
}
::google::protobuf::testing::oneofs::Data* OneOfsRequest::release_message_data() {
  // @@protoc_insertion_point(field_release:google.protobuf.testing.oneofs.OneOfsRequest.message_data)
  if (has_message_data()) {
    clear_has_data();
    ::google::protobuf::testing::oneofs::Data* temp = data_.message_data_;
    data_.message_data_ = NULL;
    return temp;
  } else {
    return NULL;
  }
}
void OneOfsRequest::set_allocated_message_data(::google::protobuf::testing::oneofs::Data* message_data) {
  clear_data();
  if (message_data) {
    set_has_message_data();
    data_.message_data_ = message_data;
  }
  // @@protoc_insertion_point(field_set_allocated:google.protobuf.testing.oneofs.OneOfsRequest.message_data)
}

// optional .google.protobuf.testing.oneofs.MoreData more_data = 5;
bool OneOfsRequest::has_more_data() const {
  return data_case() == kMoreData;
}
void OneOfsRequest::set_has_more_data() {
  _oneof_case_[0] = kMoreData;
}
void OneOfsRequest::clear_more_data() {
  if (has_more_data()) {
    delete data_.more_data_;
    clear_has_data();
  }
}
 const ::google::protobuf::testing::oneofs::MoreData& OneOfsRequest::more_data() const {
  // @@protoc_insertion_point(field_get:google.protobuf.testing.oneofs.OneOfsRequest.more_data)
  return has_more_data()
      ? *data_.more_data_
      : ::google::protobuf::testing::oneofs::MoreData::default_instance();
}
::google::protobuf::testing::oneofs::MoreData* OneOfsRequest::mutable_more_data() {
  if (!has_more_data()) {
    clear_data();
    set_has_more_data();
    data_.more_data_ = new ::google::protobuf::testing::oneofs::MoreData;
  }
  // @@protoc_insertion_point(field_mutable:google.protobuf.testing.oneofs.OneOfsRequest.more_data)
  return data_.more_data_;
}
::google::protobuf::testing::oneofs::MoreData* OneOfsRequest::release_more_data() {
  // @@protoc_insertion_point(field_release:google.protobuf.testing.oneofs.OneOfsRequest.more_data)
  if (has_more_data()) {
    clear_has_data();
    ::google::protobuf::testing::oneofs::MoreData* temp = data_.more_data_;
    data_.more_data_ = NULL;
    return temp;
  } else {
    return NULL;
  }
}
void OneOfsRequest::set_allocated_more_data(::google::protobuf::testing::oneofs::MoreData* more_data) {
  clear_data();
  if (more_data) {
    set_has_more_data();
    data_.more_data_ = more_data;
  }
  // @@protoc_insertion_point(field_set_allocated:google.protobuf.testing.oneofs.OneOfsRequest.more_data)
}

// optional .google.protobuf.Struct struct_data = 6;
bool OneOfsRequest::has_struct_data() const {
  return data_case() == kStructData;
}
void OneOfsRequest::set_has_struct_data() {
  _oneof_case_[0] = kStructData;
}
void OneOfsRequest::clear_struct_data() {
  if (has_struct_data()) {
    delete data_.struct_data_;
    clear_has_data();
  }
}
 const ::google::protobuf::Struct& OneOfsRequest::struct_data() const {
  // @@protoc_insertion_point(field_get:google.protobuf.testing.oneofs.OneOfsRequest.struct_data)
  return has_struct_data()
      ? *data_.struct_data_
      : ::google::protobuf::Struct::default_instance();
}
::google::protobuf::Struct* OneOfsRequest::mutable_struct_data() {
  if (!has_struct_data()) {
    clear_data();
    set_has_struct_data();
    data_.struct_data_ = new ::google::protobuf::Struct;
  }
  // @@protoc_insertion_point(field_mutable:google.protobuf.testing.oneofs.OneOfsRequest.struct_data)
  return data_.struct_data_;
}
::google::protobuf::Struct* OneOfsRequest::release_struct_data() {
  // @@protoc_insertion_point(field_release:google.protobuf.testing.oneofs.OneOfsRequest.struct_data)
  if (has_struct_data()) {
    clear_has_data();
    ::google::protobuf::Struct* temp = data_.struct_data_;
    data_.struct_data_ = NULL;
    return temp;
  } else {
    return NULL;
  }
}
void OneOfsRequest::set_allocated_struct_data(::google::protobuf::Struct* struct_data) {
  clear_data();
  if (struct_data) {
    if (static_cast< ::google::protobuf::Struct*>(struct_data)->GetArena() != NULL) {
      ::google::protobuf::Struct* new_struct_data = new ::google::protobuf::Struct;
      new_struct_data->CopyFrom(*struct_data);
      struct_data = new_struct_data;
    }
    set_has_struct_data();
    data_.struct_data_ = struct_data;
  }
  // @@protoc_insertion_point(field_set_allocated:google.protobuf.testing.oneofs.OneOfsRequest.struct_data)
}

// optional .google.protobuf.Value value_data = 7;
bool OneOfsRequest::has_value_data() const {
  return data_case() == kValueData;
}
void OneOfsRequest::set_has_value_data() {
  _oneof_case_[0] = kValueData;
}
void OneOfsRequest::clear_value_data() {
  if (has_value_data()) {
    delete data_.value_data_;
    clear_has_data();
  }
}
 const ::google::protobuf::Value& OneOfsRequest::value_data() const {
  // @@protoc_insertion_point(field_get:google.protobuf.testing.oneofs.OneOfsRequest.value_data)
  return has_value_data()
      ? *data_.value_data_
      : ::google::protobuf::Value::default_instance();
}
::google::protobuf::Value* OneOfsRequest::mutable_value_data() {
  if (!has_value_data()) {
    clear_data();
    set_has_value_data();
    data_.value_data_ = new ::google::protobuf::Value;
  }
  // @@protoc_insertion_point(field_mutable:google.protobuf.testing.oneofs.OneOfsRequest.value_data)
  return data_.value_data_;
}
::google::protobuf::Value* OneOfsRequest::release_value_data() {
  // @@protoc_insertion_point(field_release:google.protobuf.testing.oneofs.OneOfsRequest.value_data)
  if (has_value_data()) {
    clear_has_data();
    ::google::protobuf::Value* temp = data_.value_data_;
    data_.value_data_ = NULL;
    return temp;
  } else {
    return NULL;
  }
}
void OneOfsRequest::set_allocated_value_data(::google::protobuf::Value* value_data) {
  clear_data();
  if (value_data) {
    if (static_cast< ::google::protobuf::Value*>(value_data)->GetArena() != NULL) {
      ::google::protobuf::Value* new_value_data = new ::google::protobuf::Value;
      new_value_data->CopyFrom(*value_data);
      value_data = new_value_data;
    }
    set_has_value_data();
    data_.value_data_ = value_data;
  }
  // @@protoc_insertion_point(field_set_allocated:google.protobuf.testing.oneofs.OneOfsRequest.value_data)
}

// optional .google.protobuf.ListValue list_value_data = 8;
bool OneOfsRequest::has_list_value_data() const {
  return data_case() == kListValueData;
}
void OneOfsRequest::set_has_list_value_data() {
  _oneof_case_[0] = kListValueData;
}
void OneOfsRequest::clear_list_value_data() {
  if (has_list_value_data()) {
    delete data_.list_value_data_;
    clear_has_data();
  }
}
 const ::google::protobuf::ListValue& OneOfsRequest::list_value_data() const {
  // @@protoc_insertion_point(field_get:google.protobuf.testing.oneofs.OneOfsRequest.list_value_data)
  return has_list_value_data()
      ? *data_.list_value_data_
      : ::google::protobuf::ListValue::default_instance();
}
::google::protobuf::ListValue* OneOfsRequest::mutable_list_value_data() {
  if (!has_list_value_data()) {
    clear_data();
    set_has_list_value_data();
    data_.list_value_data_ = new ::google::protobuf::ListValue;
  }
  // @@protoc_insertion_point(field_mutable:google.protobuf.testing.oneofs.OneOfsRequest.list_value_data)
  return data_.list_value_data_;
}
::google::protobuf::ListValue* OneOfsRequest::release_list_value_data() {
  // @@protoc_insertion_point(field_release:google.protobuf.testing.oneofs.OneOfsRequest.list_value_data)
  if (has_list_value_data()) {
    clear_has_data();
    ::google::protobuf::ListValue* temp = data_.list_value_data_;
    data_.list_value_data_ = NULL;
    return temp;
  } else {
    return NULL;
  }
}
void OneOfsRequest::set_allocated_list_value_data(::google::protobuf::ListValue* list_value_data) {
  clear_data();
  if (list_value_data) {
    if (static_cast< ::google::protobuf::ListValue*>(list_value_data)->GetArena() != NULL) {
      ::google::protobuf::ListValue* new_list_value_data = new ::google::protobuf::ListValue;
      new_list_value_data->CopyFrom(*list_value_data);
      list_value_data = new_list_value_data;
    }
    set_has_list_value_data();
    data_.list_value_data_ = list_value_data;
  }
  // @@protoc_insertion_point(field_set_allocated:google.protobuf.testing.oneofs.OneOfsRequest.list_value_data)
}

// optional .google.protobuf.Timestamp ts_data = 9;
bool OneOfsRequest::has_ts_data() const {
  return data_case() == kTsData;
}
void OneOfsRequest::set_has_ts_data() {
  _oneof_case_[0] = kTsData;
}
void OneOfsRequest::clear_ts_data() {
  if (has_ts_data()) {
    delete data_.ts_data_;
    clear_has_data();
  }
}
 const ::google::protobuf::Timestamp& OneOfsRequest::ts_data() const {
  // @@protoc_insertion_point(field_get:google.protobuf.testing.oneofs.OneOfsRequest.ts_data)
  return has_ts_data()
      ? *data_.ts_data_
      : ::google::protobuf::Timestamp::default_instance();
}
::google::protobuf::Timestamp* OneOfsRequest::mutable_ts_data() {
  if (!has_ts_data()) {
    clear_data();
    set_has_ts_data();
    data_.ts_data_ = new ::google::protobuf::Timestamp;
  }
  // @@protoc_insertion_point(field_mutable:google.protobuf.testing.oneofs.OneOfsRequest.ts_data)
  return data_.ts_data_;
}
::google::protobuf::Timestamp* OneOfsRequest::release_ts_data() {
  // @@protoc_insertion_point(field_release:google.protobuf.testing.oneofs.OneOfsRequest.ts_data)
  if (has_ts_data()) {
    clear_has_data();
    ::google::protobuf::Timestamp* temp = data_.ts_data_;
    data_.ts_data_ = NULL;
    return temp;
  } else {
    return NULL;
  }
}
void OneOfsRequest::set_allocated_ts_data(::google::protobuf::Timestamp* ts_data) {
  clear_data();
  if (ts_data) {
    if (static_cast< ::google::protobuf::Timestamp*>(ts_data)->GetArena() != NULL) {
      ::google::protobuf::Timestamp* new_ts_data = new ::google::protobuf::Timestamp;
      new_ts_data->CopyFrom(*ts_data);
      ts_data = new_ts_data;
    }
    set_has_ts_data();
    data_.ts_data_ = ts_data;
  }
  // @@protoc_insertion_point(field_set_allocated:google.protobuf.testing.oneofs.OneOfsRequest.ts_data)
}

// optional .google.protobuf.Any any_data = 19;
bool OneOfsRequest::has_any_data() const {
  return this != internal_default_instance() && any_data_ != NULL;
}
void OneOfsRequest::clear_any_data() {
  if (GetArenaNoVirtual() == NULL && any_data_ != NULL) delete any_data_;
  any_data_ = NULL;
}
const ::google::protobuf::Any& OneOfsRequest::any_data() const {
  // @@protoc_insertion_point(field_get:google.protobuf.testing.oneofs.OneOfsRequest.any_data)
  return any_data_ != NULL ? *any_data_
                         : *::google::protobuf::Any::internal_default_instance();
}
::google::protobuf::Any* OneOfsRequest::mutable_any_data() {
  
  if (any_data_ == NULL) {
    any_data_ = new ::google::protobuf::Any;
  }
  // @@protoc_insertion_point(field_mutable:google.protobuf.testing.oneofs.OneOfsRequest.any_data)
  return any_data_;
}
::google::protobuf::Any* OneOfsRequest::release_any_data() {
  // @@protoc_insertion_point(field_release:google.protobuf.testing.oneofs.OneOfsRequest.any_data)
  
  ::google::protobuf::Any* temp = any_data_;
  any_data_ = NULL;
  return temp;
}
void OneOfsRequest::set_allocated_any_data(::google::protobuf::Any* any_data) {
  delete any_data_;
  any_data_ = any_data;
  if (any_data) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_set_allocated:google.protobuf.testing.oneofs.OneOfsRequest.any_data)
}

bool OneOfsRequest::has_data() const {
  return data_case() != DATA_NOT_SET;
}
void OneOfsRequest::clear_has_data() {
  _oneof_case_[0] = DATA_NOT_SET;
}
OneOfsRequest::DataCase OneOfsRequest::data_case() const {
  return OneOfsRequest::DataCase(_oneof_case_[0]);
}
inline const OneOfsRequest* OneOfsRequest::internal_default_instance() {
  return &OneOfsRequest_default_instance_.get();
}
#endif  // PROTOBUF_INLINE_NOT_IN_HEADERS

// ===================================================================

#if !defined(_MSC_VER) || _MSC_VER >= 1900
const int RequestWithSimpleOneof::kValueFieldNumber;
const int RequestWithSimpleOneof::kStrDataFieldNumber;
const int RequestWithSimpleOneof::kIntDataFieldNumber;
const int RequestWithSimpleOneof::kMessageDataFieldNumber;
const int RequestWithSimpleOneof::kMoreDataFieldNumber;
#endif  // !defined(_MSC_VER) || _MSC_VER >= 1900

RequestWithSimpleOneof::RequestWithSimpleOneof()
  : ::google::protobuf::Message(), _internal_metadata_(NULL) {
  if (this != internal_default_instance()) protobuf_InitDefaults_google_2fprotobuf_2futil_2finternal_2ftestdata_2foneofs_2eproto();
  SharedCtor();
  // @@protoc_insertion_point(constructor:google.protobuf.testing.oneofs.RequestWithSimpleOneof)
}

void RequestWithSimpleOneof::InitAsDefaultInstance() {
  RequestWithSimpleOneof_default_oneof_instance_->str_data_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  RequestWithSimpleOneof_default_oneof_instance_->int_data_ = 0;
  RequestWithSimpleOneof_default_oneof_instance_->message_data_ = const_cast< ::google::protobuf::testing::oneofs::Data*>(
      ::google::protobuf::testing::oneofs::Data::internal_default_instance());
  RequestWithSimpleOneof_default_oneof_instance_->more_data_ = const_cast< ::google::protobuf::testing::oneofs::MoreData*>(
      ::google::protobuf::testing::oneofs::MoreData::internal_default_instance());
}

RequestWithSimpleOneof::RequestWithSimpleOneof(const RequestWithSimpleOneof& from)
  : ::google::protobuf::Message(),
    _internal_metadata_(NULL) {
  SharedCtor();
  UnsafeMergeFrom(from);
  // @@protoc_insertion_point(copy_constructor:google.protobuf.testing.oneofs.RequestWithSimpleOneof)
}

void RequestWithSimpleOneof::SharedCtor() {
  value_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  clear_has_data();
  _cached_size_ = 0;
}

RequestWithSimpleOneof::~RequestWithSimpleOneof() {
  // @@protoc_insertion_point(destructor:google.protobuf.testing.oneofs.RequestWithSimpleOneof)
  SharedDtor();
}

void RequestWithSimpleOneof::SharedDtor() {
  value_.DestroyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  if (has_data()) {
    clear_data();
  }
}

void RequestWithSimpleOneof::SetCachedSize(int size) const {
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
}
const ::google::protobuf::Descriptor* RequestWithSimpleOneof::descriptor() {
  protobuf_AssignDescriptorsOnce();
  return RequestWithSimpleOneof_descriptor_;
}

const RequestWithSimpleOneof& RequestWithSimpleOneof::default_instance() {
  protobuf_InitDefaults_google_2fprotobuf_2futil_2finternal_2ftestdata_2foneofs_2eproto();
  return *internal_default_instance();
}

::google::protobuf::internal::ExplicitlyConstructed<RequestWithSimpleOneof> RequestWithSimpleOneof_default_instance_;

RequestWithSimpleOneof* RequestWithSimpleOneof::New(::google::protobuf::Arena* arena) const {
  RequestWithSimpleOneof* n = new RequestWithSimpleOneof;
  if (arena != NULL) {
    arena->Own(n);
  }
  return n;
}

void RequestWithSimpleOneof::clear_data() {
// @@protoc_insertion_point(one_of_clear_start:google.protobuf.testing.oneofs.RequestWithSimpleOneof)
  switch (data_case()) {
    case kStrData: {
      data_.str_data_.DestroyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
      break;
    }
    case kIntData: {
      // No need to clear
      break;
    }
    case kMessageData: {
      delete data_.message_data_;
      break;
    }
    case kMoreData: {
      delete data_.more_data_;
      break;
    }
    case DATA_NOT_SET: {
      break;
    }
  }
  _oneof_case_[0] = DATA_NOT_SET;
}


void RequestWithSimpleOneof::Clear() {
// @@protoc_insertion_point(message_clear_start:google.protobuf.testing.oneofs.RequestWithSimpleOneof)
  value_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  clear_data();
}

bool RequestWithSimpleOneof::MergePartialFromCodedStream(
    ::google::protobuf::io::CodedInputStream* input) {
#define DO_(EXPRESSION) if (!GOOGLE_PREDICT_TRUE(EXPRESSION)) goto failure
  ::google::protobuf::uint32 tag;
  // @@protoc_insertion_point(parse_start:google.protobuf.testing.oneofs.RequestWithSimpleOneof)
  for (;;) {
    ::std::pair< ::google::protobuf::uint32, bool> p = input->ReadTagWithCutoff(127);
    tag = p.first;
    if (!p.second) goto handle_unusual;
    switch (::google::protobuf::internal::WireFormatLite::GetTagFieldNumber(tag)) {
      // optional string value = 1;
      case 1: {
        if (tag == 10) {
          DO_(::google::protobuf::internal::WireFormatLite::ReadString(
                input, this->mutable_value()));
          DO_(::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
            this->value().data(), this->value().length(),
            ::google::protobuf::internal::WireFormatLite::PARSE,
            "google.protobuf.testing.oneofs.RequestWithSimpleOneof.value"));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(18)) goto parse_str_data;
        break;
      }

      // optional string str_data = 2;
      case 2: {
        if (tag == 18) {
         parse_str_data:
          DO_(::google::protobuf::internal::WireFormatLite::ReadString(
                input, this->mutable_str_data()));
          DO_(::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
            this->str_data().data(), this->str_data().length(),
            ::google::protobuf::internal::WireFormatLite::PARSE,
            "google.protobuf.testing.oneofs.RequestWithSimpleOneof.str_data"));
        } else {
          goto handle_unusual;
        }
        goto after_more_data;
        break;
      }

      // optional int32 int_data = 3;
      case 3: {
        if (tag == 24) {
          clear_data();
          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int32, ::google::protobuf::internal::WireFormatLite::TYPE_INT32>(
                 input, &data_.int_data_)));
          set_has_int_data();
        } else {
          goto handle_unusual;
        }
        goto after_more_data;
        break;
      }

      // optional .google.protobuf.testing.oneofs.Data message_data = 4;
      case 4: {
        if (tag == 34) {
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessageNoVirtual(
               input, mutable_message_data()));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(42)) goto parse_more_data;
        break;
      }

      // optional .google.protobuf.testing.oneofs.MoreData more_data = 5;
      case 5: {
        if (tag == 42) {
         parse_more_data:
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessageNoVirtual(
               input, mutable_more_data()));
        } else {
          goto handle_unusual;
        }
       after_more_data:
        if (input->ExpectAtEnd()) goto success;
        break;
      }

      default: {
      handle_unusual:
        if (tag == 0 ||
            ::google::protobuf::internal::WireFormatLite::GetTagWireType(tag) ==
            ::google::protobuf::internal::WireFormatLite::WIRETYPE_END_GROUP) {
          goto success;
        }
        DO_(::google::protobuf::internal::WireFormatLite::SkipField(input, tag));
        break;
      }
    }
  }
success:
  // @@protoc_insertion_point(parse_success:google.protobuf.testing.oneofs.RequestWithSimpleOneof)
  return true;
failure:
  // @@protoc_insertion_point(parse_failure:google.protobuf.testing.oneofs.RequestWithSimpleOneof)
  return false;
#undef DO_
}

void RequestWithSimpleOneof::SerializeWithCachedSizes(
    ::google::protobuf::io::CodedOutputStream* output) const {
  // @@protoc_insertion_point(serialize_start:google.protobuf.testing.oneofs.RequestWithSimpleOneof)
  // optional string value = 1;
  if (this->value().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->value().data(), this->value().length(),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "google.protobuf.testing.oneofs.RequestWithSimpleOneof.value");
    ::google::protobuf::internal::WireFormatLite::WriteStringMaybeAliased(
      1, this->value(), output);
  }

  // optional string str_data = 2;
  if (has_str_data()) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->str_data().data(), this->str_data().length(),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "google.protobuf.testing.oneofs.RequestWithSimpleOneof.str_data");
    ::google::protobuf::internal::WireFormatLite::WriteStringMaybeAliased(
      2, this->str_data(), output);
  }

  // optional int32 int_data = 3;
  if (has_int_data()) {
    ::google::protobuf::internal::WireFormatLite::WriteInt32(3, this->int_data(), output);
  }

  // optional .google.protobuf.testing.oneofs.Data message_data = 4;
  if (has_message_data()) {
    ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
      4, *data_.message_data_, output);
  }

  // optional .google.protobuf.testing.oneofs.MoreData more_data = 5;
  if (has_more_data()) {
    ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
      5, *data_.more_data_, output);
  }

  // @@protoc_insertion_point(serialize_end:google.protobuf.testing.oneofs.RequestWithSimpleOneof)
}

::google::protobuf::uint8* RequestWithSimpleOneof::InternalSerializeWithCachedSizesToArray(
    bool deterministic, ::google::protobuf::uint8* target) const {
  (void)deterministic; // Unused
  // @@protoc_insertion_point(serialize_to_array_start:google.protobuf.testing.oneofs.RequestWithSimpleOneof)
  // optional string value = 1;
  if (this->value().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->value().data(), this->value().length(),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "google.protobuf.testing.oneofs.RequestWithSimpleOneof.value");
    target =
      ::google::protobuf::internal::WireFormatLite::WriteStringToArray(
        1, this->value(), target);
  }

  // optional string str_data = 2;
  if (has_str_data()) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->str_data().data(), this->str_data().length(),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "google.protobuf.testing.oneofs.RequestWithSimpleOneof.str_data");
    target =
      ::google::protobuf::internal::WireFormatLite::WriteStringToArray(
        2, this->str_data(), target);
  }

  // optional int32 int_data = 3;
  if (has_int_data()) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt32ToArray(3, this->int_data(), target);
  }

  // optional .google.protobuf.testing.oneofs.Data message_data = 4;
  if (has_message_data()) {
    target = ::google::protobuf::internal::WireFormatLite::
      InternalWriteMessageNoVirtualToArray(
        4, *data_.message_data_, false, target);
  }

  // optional .google.protobuf.testing.oneofs.MoreData more_data = 5;
  if (has_more_data()) {
    target = ::google::protobuf::internal::WireFormatLite::
      InternalWriteMessageNoVirtualToArray(
        5, *data_.more_data_, false, target);
  }

  // @@protoc_insertion_point(serialize_to_array_end:google.protobuf.testing.oneofs.RequestWithSimpleOneof)
  return target;
}

size_t RequestWithSimpleOneof::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:google.protobuf.testing.oneofs.RequestWithSimpleOneof)
  size_t total_size = 0;

  // optional string value = 1;
  if (this->value().size() > 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::StringSize(
        this->value());
  }

  switch (data_case()) {
    // optional string str_data = 2;
    case kStrData: {
      total_size += 1 +
        ::google::protobuf::internal::WireFormatLite::StringSize(
          this->str_data());
      break;
    }
    // optional int32 int_data = 3;
    case kIntData: {
      total_size += 1 +
        ::google::protobuf::internal::WireFormatLite::Int32Size(
          this->int_data());
      break;
    }
    // optional .google.protobuf.testing.oneofs.Data message_data = 4;
    case kMessageData: {
      total_size += 1 +
        ::google::protobuf::internal::WireFormatLite::MessageSizeNoVirtual(
          *data_.message_data_);
      break;
    }
    // optional .google.protobuf.testing.oneofs.MoreData more_data = 5;
    case kMoreData: {
      total_size += 1 +
        ::google::protobuf::internal::WireFormatLite::MessageSizeNoVirtual(
          *data_.more_data_);
      break;
    }
    case DATA_NOT_SET: {
      break;
    }
  }
  int cached_size = ::google::protobuf::internal::ToCachedSize(total_size);
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = cached_size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
  return total_size;
}

void RequestWithSimpleOneof::MergeFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_merge_from_start:google.protobuf.testing.oneofs.RequestWithSimpleOneof)
  if (GOOGLE_PREDICT_FALSE(&from == this)) MergeFromFail(__LINE__);
  const RequestWithSimpleOneof* source =
      ::google::protobuf::internal::DynamicCastToGenerated<const RequestWithSimpleOneof>(
          &from);
  if (source == NULL) {
  // @@protoc_insertion_point(generalized_merge_from_cast_fail:google.protobuf.testing.oneofs.RequestWithSimpleOneof)
    ::google::protobuf::internal::ReflectionOps::Merge(from, this);
  } else {
  // @@protoc_insertion_point(generalized_merge_from_cast_success:google.protobuf.testing.oneofs.RequestWithSimpleOneof)
    UnsafeMergeFrom(*source);
  }
}

void RequestWithSimpleOneof::MergeFrom(const RequestWithSimpleOneof& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:google.protobuf.testing.oneofs.RequestWithSimpleOneof)
  if (GOOGLE_PREDICT_TRUE(&from != this)) {
    UnsafeMergeFrom(from);
  } else {
    MergeFromFail(__LINE__);
  }
}

void RequestWithSimpleOneof::UnsafeMergeFrom(const RequestWithSimpleOneof& from) {
  GOOGLE_DCHECK(&from != this);
  switch (from.data_case()) {
    case kStrData: {
      set_str_data(from.str_data());
      break;
    }
    case kIntData: {
      set_int_data(from.int_data());
      break;
    }
    case kMessageData: {
      mutable_message_data()->::google::protobuf::testing::oneofs::Data::MergeFrom(from.message_data());
      break;
    }
    case kMoreData: {
      mutable_more_data()->::google::protobuf::testing::oneofs::MoreData::MergeFrom(from.more_data());
      break;
    }
    case DATA_NOT_SET: {
      break;
    }
  }
  if (from.value().size() > 0) {

    value_.AssignWithDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), from.value_);
  }
}

void RequestWithSimpleOneof::CopyFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_copy_from_start:google.protobuf.testing.oneofs.RequestWithSimpleOneof)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

void RequestWithSimpleOneof::CopyFrom(const RequestWithSimpleOneof& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:google.protobuf.testing.oneofs.RequestWithSimpleOneof)
  if (&from == this) return;
  Clear();
  UnsafeMergeFrom(from);
}

bool RequestWithSimpleOneof::IsInitialized() const {

  return true;
}

void RequestWithSimpleOneof::Swap(RequestWithSimpleOneof* other) {
  if (other == this) return;
  InternalSwap(other);
}
void RequestWithSimpleOneof::InternalSwap(RequestWithSimpleOneof* other) {
  value_.Swap(&other->value_);
  std::swap(data_, other->data_);
  std::swap(_oneof_case_[0], other->_oneof_case_[0]);
  _internal_metadata_.Swap(&other->_internal_metadata_);
  std::swap(_cached_size_, other->_cached_size_);
}

::google::protobuf::Metadata RequestWithSimpleOneof::GetMetadata() const {
  protobuf_AssignDescriptorsOnce();
  ::google::protobuf::Metadata metadata;
  metadata.descriptor = RequestWithSimpleOneof_descriptor_;
  metadata.reflection = RequestWithSimpleOneof_reflection_;
  return metadata;
}

#if PROTOBUF_INLINE_NOT_IN_HEADERS
// RequestWithSimpleOneof

// optional string value = 1;
void RequestWithSimpleOneof::clear_value() {
  value_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
const ::std::string& RequestWithSimpleOneof::value() const {
  // @@protoc_insertion_point(field_get:google.protobuf.testing.oneofs.RequestWithSimpleOneof.value)
  return value_.GetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
void RequestWithSimpleOneof::set_value(const ::std::string& value) {
  
  value_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:google.protobuf.testing.oneofs.RequestWithSimpleOneof.value)
}
void RequestWithSimpleOneof::set_value(const char* value) {
  
  value_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:google.protobuf.testing.oneofs.RequestWithSimpleOneof.value)
}
void RequestWithSimpleOneof::set_value(const char* value, size_t size) {
  
  value_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:google.protobuf.testing.oneofs.RequestWithSimpleOneof.value)
}
::std::string* RequestWithSimpleOneof::mutable_value() {
  
  // @@protoc_insertion_point(field_mutable:google.protobuf.testing.oneofs.RequestWithSimpleOneof.value)
  return value_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
::std::string* RequestWithSimpleOneof::release_value() {
  // @@protoc_insertion_point(field_release:google.protobuf.testing.oneofs.RequestWithSimpleOneof.value)
  
  return value_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
void RequestWithSimpleOneof::set_allocated_value(::std::string* value) {
  if (value != NULL) {
    
  } else {
    
  }
  value_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set_allocated:google.protobuf.testing.oneofs.RequestWithSimpleOneof.value)
}

// optional string str_data = 2;
bool RequestWithSimpleOneof::has_str_data() const {
  return data_case() == kStrData;
}
void RequestWithSimpleOneof::set_has_str_data() {
  _oneof_case_[0] = kStrData;
}
void RequestWithSimpleOneof::clear_str_data() {
  if (has_str_data()) {
    data_.str_data_.DestroyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
    clear_has_data();
  }
}
const ::std::string& RequestWithSimpleOneof::str_data() const {
  // @@protoc_insertion_point(field_get:google.protobuf.testing.oneofs.RequestWithSimpleOneof.str_data)
  if (has_str_data()) {
    return data_.str_data_.GetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  }
  return *&::google::protobuf::internal::GetEmptyStringAlreadyInited();
}
void RequestWithSimpleOneof::set_str_data(const ::std::string& value) {
  // @@protoc_insertion_point(field_set:google.protobuf.testing.oneofs.RequestWithSimpleOneof.str_data)
  if (!has_str_data()) {
    clear_data();
    set_has_str_data();
    data_.str_data_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  }
  data_.str_data_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:google.protobuf.testing.oneofs.RequestWithSimpleOneof.str_data)
}
void RequestWithSimpleOneof::set_str_data(const char* value) {
  if (!has_str_data()) {
    clear_data();
    set_has_str_data();
    data_.str_data_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  }
  data_.str_data_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(value));
  // @@protoc_insertion_point(field_set_char:google.protobuf.testing.oneofs.RequestWithSimpleOneof.str_data)
}
void RequestWithSimpleOneof::set_str_data(const char* value, size_t size) {
  if (!has_str_data()) {
    clear_data();
    set_has_str_data();
    data_.str_data_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  }
  data_.str_data_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(
      reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:google.protobuf.testing.oneofs.RequestWithSimpleOneof.str_data)
}
::std::string* RequestWithSimpleOneof::mutable_str_data() {
  if (!has_str_data()) {
    clear_data();
    set_has_str_data();
    data_.str_data_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  }
  // @@protoc_insertion_point(field_mutable:google.protobuf.testing.oneofs.RequestWithSimpleOneof.str_data)
  return data_.str_data_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
::std::string* RequestWithSimpleOneof::release_str_data() {
  // @@protoc_insertion_point(field_release:google.protobuf.testing.oneofs.RequestWithSimpleOneof.str_data)
  if (has_str_data()) {
    clear_has_data();
    return data_.str_data_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  } else {
    return NULL;
  }
}
void RequestWithSimpleOneof::set_allocated_str_data(::std::string* str_data) {
  if (!has_str_data()) {
    data_.str_data_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  }
  clear_data();
  if (str_data != NULL) {
    set_has_str_data();
    data_.str_data_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
        str_data);
  }
  // @@protoc_insertion_point(field_set_allocated:google.protobuf.testing.oneofs.RequestWithSimpleOneof.str_data)
}

// optional int32 int_data = 3;
bool RequestWithSimpleOneof::has_int_data() const {
  return data_case() == kIntData;
}
void RequestWithSimpleOneof::set_has_int_data() {
  _oneof_case_[0] = kIntData;
}
void RequestWithSimpleOneof::clear_int_data() {
  if (has_int_data()) {
    data_.int_data_ = 0;
    clear_has_data();
  }
}
::google::protobuf::int32 RequestWithSimpleOneof::int_data() const {
  // @@protoc_insertion_point(field_get:google.protobuf.testing.oneofs.RequestWithSimpleOneof.int_data)
  if (has_int_data()) {
    return data_.int_data_;
  }
  return 0;
}
void RequestWithSimpleOneof::set_int_data(::google::protobuf::int32 value) {
  if (!has_int_data()) {
    clear_data();
    set_has_int_data();
  }
  data_.int_data_ = value;
  // @@protoc_insertion_point(field_set:google.protobuf.testing.oneofs.RequestWithSimpleOneof.int_data)
}

// optional .google.protobuf.testing.oneofs.Data message_data = 4;
bool RequestWithSimpleOneof::has_message_data() const {
  return data_case() == kMessageData;
}
void RequestWithSimpleOneof::set_has_message_data() {
  _oneof_case_[0] = kMessageData;
}
void RequestWithSimpleOneof::clear_message_data() {
  if (has_message_data()) {
    delete data_.message_data_;
    clear_has_data();
  }
}
 const ::google::protobuf::testing::oneofs::Data& RequestWithSimpleOneof::message_data() const {
  // @@protoc_insertion_point(field_get:google.protobuf.testing.oneofs.RequestWithSimpleOneof.message_data)
  return has_message_data()
      ? *data_.message_data_
      : ::google::protobuf::testing::oneofs::Data::default_instance();
}
::google::protobuf::testing::oneofs::Data* RequestWithSimpleOneof::mutable_message_data() {
  if (!has_message_data()) {
    clear_data();
    set_has_message_data();
    data_.message_data_ = new ::google::protobuf::testing::oneofs::Data;
  }
  // @@protoc_insertion_point(field_mutable:google.protobuf.testing.oneofs.RequestWithSimpleOneof.message_data)
  return data_.message_data_;
}
::google::protobuf::testing::oneofs::Data* RequestWithSimpleOneof::release_message_data() {
  // @@protoc_insertion_point(field_release:google.protobuf.testing.oneofs.RequestWithSimpleOneof.message_data)
  if (has_message_data()) {
    clear_has_data();
    ::google::protobuf::testing::oneofs::Data* temp = data_.message_data_;
    data_.message_data_ = NULL;
    return temp;
  } else {
    return NULL;
  }
}
void RequestWithSimpleOneof::set_allocated_message_data(::google::protobuf::testing::oneofs::Data* message_data) {
  clear_data();
  if (message_data) {
    set_has_message_data();
    data_.message_data_ = message_data;
  }
  // @@protoc_insertion_point(field_set_allocated:google.protobuf.testing.oneofs.RequestWithSimpleOneof.message_data)
}

// optional .google.protobuf.testing.oneofs.MoreData more_data = 5;
bool RequestWithSimpleOneof::has_more_data() const {
  return data_case() == kMoreData;
}
void RequestWithSimpleOneof::set_has_more_data() {
  _oneof_case_[0] = kMoreData;
}
void RequestWithSimpleOneof::clear_more_data() {
  if (has_more_data()) {
    delete data_.more_data_;
    clear_has_data();
  }
}
 const ::google::protobuf::testing::oneofs::MoreData& RequestWithSimpleOneof::more_data() const {
  // @@protoc_insertion_point(field_get:google.protobuf.testing.oneofs.RequestWithSimpleOneof.more_data)
  return has_more_data()
      ? *data_.more_data_
      : ::google::protobuf::testing::oneofs::MoreData::default_instance();
}
::google::protobuf::testing::oneofs::MoreData* RequestWithSimpleOneof::mutable_more_data() {
  if (!has_more_data()) {
    clear_data();
    set_has_more_data();
    data_.more_data_ = new ::google::protobuf::testing::oneofs::MoreData;
  }
  // @@protoc_insertion_point(field_mutable:google.protobuf.testing.oneofs.RequestWithSimpleOneof.more_data)
  return data_.more_data_;
}
::google::protobuf::testing::oneofs::MoreData* RequestWithSimpleOneof::release_more_data() {
  // @@protoc_insertion_point(field_release:google.protobuf.testing.oneofs.RequestWithSimpleOneof.more_data)
  if (has_more_data()) {
    clear_has_data();
    ::google::protobuf::testing::oneofs::MoreData* temp = data_.more_data_;
    data_.more_data_ = NULL;
    return temp;
  } else {
    return NULL;
  }
}
void RequestWithSimpleOneof::set_allocated_more_data(::google::protobuf::testing::oneofs::MoreData* more_data) {
  clear_data();
  if (more_data) {
    set_has_more_data();
    data_.more_data_ = more_data;
  }
  // @@protoc_insertion_point(field_set_allocated:google.protobuf.testing.oneofs.RequestWithSimpleOneof.more_data)
}

bool RequestWithSimpleOneof::has_data() const {
  return data_case() != DATA_NOT_SET;
}
void RequestWithSimpleOneof::clear_has_data() {
  _oneof_case_[0] = DATA_NOT_SET;
}
RequestWithSimpleOneof::DataCase RequestWithSimpleOneof::data_case() const {
  return RequestWithSimpleOneof::DataCase(_oneof_case_[0]);
}
inline const RequestWithSimpleOneof* RequestWithSimpleOneof::internal_default_instance() {
  return &RequestWithSimpleOneof_default_instance_.get();
}
#endif  // PROTOBUF_INLINE_NOT_IN_HEADERS

// ===================================================================

#if !defined(_MSC_VER) || _MSC_VER >= 1900
const int Data::kDataValueFieldNumber;
#endif  // !defined(_MSC_VER) || _MSC_VER >= 1900

Data::Data()
  : ::google::protobuf::Message(), _internal_metadata_(NULL) {
  if (this != internal_default_instance()) protobuf_InitDefaults_google_2fprotobuf_2futil_2finternal_2ftestdata_2foneofs_2eproto();
  SharedCtor();
  // @@protoc_insertion_point(constructor:google.protobuf.testing.oneofs.Data)
}

void Data::InitAsDefaultInstance() {
}

Data::Data(const Data& from)
  : ::google::protobuf::Message(),
    _internal_metadata_(NULL) {
  SharedCtor();
  UnsafeMergeFrom(from);
  // @@protoc_insertion_point(copy_constructor:google.protobuf.testing.oneofs.Data)
}

void Data::SharedCtor() {
  data_value_ = 0;
  _cached_size_ = 0;
}

Data::~Data() {
  // @@protoc_insertion_point(destructor:google.protobuf.testing.oneofs.Data)
  SharedDtor();
}

void Data::SharedDtor() {
}

void Data::SetCachedSize(int size) const {
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
}
const ::google::protobuf::Descriptor* Data::descriptor() {
  protobuf_AssignDescriptorsOnce();
  return Data_descriptor_;
}

const Data& Data::default_instance() {
  protobuf_InitDefaults_google_2fprotobuf_2futil_2finternal_2ftestdata_2foneofs_2eproto();
  return *internal_default_instance();
}

::google::protobuf::internal::ExplicitlyConstructed<Data> Data_default_instance_;

Data* Data::New(::google::protobuf::Arena* arena) const {
  Data* n = new Data;
  if (arena != NULL) {
    arena->Own(n);
  }
  return n;
}

void Data::Clear() {
// @@protoc_insertion_point(message_clear_start:google.protobuf.testing.oneofs.Data)
  data_value_ = 0;
}

bool Data::MergePartialFromCodedStream(
    ::google::protobuf::io::CodedInputStream* input) {
#define DO_(EXPRESSION) if (!GOOGLE_PREDICT_TRUE(EXPRESSION)) goto failure
  ::google::protobuf::uint32 tag;
  // @@protoc_insertion_point(parse_start:google.protobuf.testing.oneofs.Data)
  for (;;) {
    ::std::pair< ::google::protobuf::uint32, bool> p = input->ReadTagWithCutoff(127);
    tag = p.first;
    if (!p.second) goto handle_unusual;
    switch (::google::protobuf::internal::WireFormatLite::GetTagFieldNumber(tag)) {
      // optional int32 data_value = 1;
      case 1: {
        if (tag == 8) {

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int32, ::google::protobuf::internal::WireFormatLite::TYPE_INT32>(
                 input, &data_value_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectAtEnd()) goto success;
        break;
      }

      default: {
      handle_unusual:
        if (tag == 0 ||
            ::google::protobuf::internal::WireFormatLite::GetTagWireType(tag) ==
            ::google::protobuf::internal::WireFormatLite::WIRETYPE_END_GROUP) {
          goto success;
        }
        DO_(::google::protobuf::internal::WireFormatLite::SkipField(input, tag));
        break;
      }
    }
  }
success:
  // @@protoc_insertion_point(parse_success:google.protobuf.testing.oneofs.Data)
  return true;
failure:
  // @@protoc_insertion_point(parse_failure:google.protobuf.testing.oneofs.Data)
  return false;
#undef DO_
}

void Data::SerializeWithCachedSizes(
    ::google::protobuf::io::CodedOutputStream* output) const {
  // @@protoc_insertion_point(serialize_start:google.protobuf.testing.oneofs.Data)
  // optional int32 data_value = 1;
  if (this->data_value() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt32(1, this->data_value(), output);
  }

  // @@protoc_insertion_point(serialize_end:google.protobuf.testing.oneofs.Data)
}

::google::protobuf::uint8* Data::InternalSerializeWithCachedSizesToArray(
    bool deterministic, ::google::protobuf::uint8* target) const {
  (void)deterministic; // Unused
  // @@protoc_insertion_point(serialize_to_array_start:google.protobuf.testing.oneofs.Data)
  // optional int32 data_value = 1;
  if (this->data_value() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt32ToArray(1, this->data_value(), target);
  }

  // @@protoc_insertion_point(serialize_to_array_end:google.protobuf.testing.oneofs.Data)
  return target;
}

size_t Data::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:google.protobuf.testing.oneofs.Data)
  size_t total_size = 0;

  // optional int32 data_value = 1;
  if (this->data_value() != 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::Int32Size(
        this->data_value());
  }

  int cached_size = ::google::protobuf::internal::ToCachedSize(total_size);
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = cached_size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
  return total_size;
}

void Data::MergeFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_merge_from_start:google.protobuf.testing.oneofs.Data)
  if (GOOGLE_PREDICT_FALSE(&from == this)) MergeFromFail(__LINE__);
  const Data* source =
      ::google::protobuf::internal::DynamicCastToGenerated<const Data>(
          &from);
  if (source == NULL) {
  // @@protoc_insertion_point(generalized_merge_from_cast_fail:google.protobuf.testing.oneofs.Data)
    ::google::protobuf::internal::ReflectionOps::Merge(from, this);
  } else {
  // @@protoc_insertion_point(generalized_merge_from_cast_success:google.protobuf.testing.oneofs.Data)
    UnsafeMergeFrom(*source);
  }
}

void Data::MergeFrom(const Data& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:google.protobuf.testing.oneofs.Data)
  if (GOOGLE_PREDICT_TRUE(&from != this)) {
    UnsafeMergeFrom(from);
  } else {
    MergeFromFail(__LINE__);
  }
}

void Data::UnsafeMergeFrom(const Data& from) {
  GOOGLE_DCHECK(&from != this);
  if (from.data_value() != 0) {
    set_data_value(from.data_value());
  }
}

void Data::CopyFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_copy_from_start:google.protobuf.testing.oneofs.Data)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

void Data::CopyFrom(const Data& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:google.protobuf.testing.oneofs.Data)
  if (&from == this) return;
  Clear();
  UnsafeMergeFrom(from);
}

bool Data::IsInitialized() const {

  return true;
}

void Data::Swap(Data* other) {
  if (other == this) return;
  InternalSwap(other);
}
void Data::InternalSwap(Data* other) {
  std::swap(data_value_, other->data_value_);
  _internal_metadata_.Swap(&other->_internal_metadata_);
  std::swap(_cached_size_, other->_cached_size_);
}

::google::protobuf::Metadata Data::GetMetadata() const {
  protobuf_AssignDescriptorsOnce();
  ::google::protobuf::Metadata metadata;
  metadata.descriptor = Data_descriptor_;
  metadata.reflection = Data_reflection_;
  return metadata;
}

#if PROTOBUF_INLINE_NOT_IN_HEADERS
// Data

// optional int32 data_value = 1;
void Data::clear_data_value() {
  data_value_ = 0;
}
::google::protobuf::int32 Data::data_value() const {
  // @@protoc_insertion_point(field_get:google.protobuf.testing.oneofs.Data.data_value)
  return data_value_;
}
void Data::set_data_value(::google::protobuf::int32 value) {
  
  data_value_ = value;
  // @@protoc_insertion_point(field_set:google.protobuf.testing.oneofs.Data.data_value)
}

inline const Data* Data::internal_default_instance() {
  return &Data_default_instance_.get();
}
#endif  // PROTOBUF_INLINE_NOT_IN_HEADERS

// ===================================================================

#if !defined(_MSC_VER) || _MSC_VER >= 1900
const int MoreData::kStrValueFieldNumber;
#endif  // !defined(_MSC_VER) || _MSC_VER >= 1900

MoreData::MoreData()
  : ::google::protobuf::Message(), _internal_metadata_(NULL) {
  if (this != internal_default_instance()) protobuf_InitDefaults_google_2fprotobuf_2futil_2finternal_2ftestdata_2foneofs_2eproto();
  SharedCtor();
  // @@protoc_insertion_point(constructor:google.protobuf.testing.oneofs.MoreData)
}

void MoreData::InitAsDefaultInstance() {
}

MoreData::MoreData(const MoreData& from)
  : ::google::protobuf::Message(),
    _internal_metadata_(NULL) {
  SharedCtor();
  UnsafeMergeFrom(from);
  // @@protoc_insertion_point(copy_constructor:google.protobuf.testing.oneofs.MoreData)
}

void MoreData::SharedCtor() {
  str_value_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  _cached_size_ = 0;
}

MoreData::~MoreData() {
  // @@protoc_insertion_point(destructor:google.protobuf.testing.oneofs.MoreData)
  SharedDtor();
}

void MoreData::SharedDtor() {
  str_value_.DestroyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}

void MoreData::SetCachedSize(int size) const {
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
}
const ::google::protobuf::Descriptor* MoreData::descriptor() {
  protobuf_AssignDescriptorsOnce();
  return MoreData_descriptor_;
}

const MoreData& MoreData::default_instance() {
  protobuf_InitDefaults_google_2fprotobuf_2futil_2finternal_2ftestdata_2foneofs_2eproto();
  return *internal_default_instance();
}

::google::protobuf::internal::ExplicitlyConstructed<MoreData> MoreData_default_instance_;

MoreData* MoreData::New(::google::protobuf::Arena* arena) const {
  MoreData* n = new MoreData;
  if (arena != NULL) {
    arena->Own(n);
  }
  return n;
}

void MoreData::Clear() {
// @@protoc_insertion_point(message_clear_start:google.protobuf.testing.oneofs.MoreData)
  str_value_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}

bool MoreData::MergePartialFromCodedStream(
    ::google::protobuf::io::CodedInputStream* input) {
#define DO_(EXPRESSION) if (!GOOGLE_PREDICT_TRUE(EXPRESSION)) goto failure
  ::google::protobuf::uint32 tag;
  // @@protoc_insertion_point(parse_start:google.protobuf.testing.oneofs.MoreData)
  for (;;) {
    ::std::pair< ::google::protobuf::uint32, bool> p = input->ReadTagWithCutoff(127);
    tag = p.first;
    if (!p.second) goto handle_unusual;
    switch (::google::protobuf::internal::WireFormatLite::GetTagFieldNumber(tag)) {
      // optional string str_value = 1;
      case 1: {
        if (tag == 10) {
          DO_(::google::protobuf::internal::WireFormatLite::ReadString(
                input, this->mutable_str_value()));
          DO_(::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
            this->str_value().data(), this->str_value().length(),
            ::google::protobuf::internal::WireFormatLite::PARSE,
            "google.protobuf.testing.oneofs.MoreData.str_value"));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectAtEnd()) goto success;
        break;
      }

      default: {
      handle_unusual:
        if (tag == 0 ||
            ::google::protobuf::internal::WireFormatLite::GetTagWireType(tag) ==
            ::google::protobuf::internal::WireFormatLite::WIRETYPE_END_GROUP) {
          goto success;
        }
        DO_(::google::protobuf::internal::WireFormatLite::SkipField(input, tag));
        break;
      }
    }
  }
success:
  // @@protoc_insertion_point(parse_success:google.protobuf.testing.oneofs.MoreData)
  return true;
failure:
  // @@protoc_insertion_point(parse_failure:google.protobuf.testing.oneofs.MoreData)
  return false;
#undef DO_
}

void MoreData::SerializeWithCachedSizes(
    ::google::protobuf::io::CodedOutputStream* output) const {
  // @@protoc_insertion_point(serialize_start:google.protobuf.testing.oneofs.MoreData)
  // optional string str_value = 1;
  if (this->str_value().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->str_value().data(), this->str_value().length(),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "google.protobuf.testing.oneofs.MoreData.str_value");
    ::google::protobuf::internal::WireFormatLite::WriteStringMaybeAliased(
      1, this->str_value(), output);
  }

  // @@protoc_insertion_point(serialize_end:google.protobuf.testing.oneofs.MoreData)
}

::google::protobuf::uint8* MoreData::InternalSerializeWithCachedSizesToArray(
    bool deterministic, ::google::protobuf::uint8* target) const {
  (void)deterministic; // Unused
  // @@protoc_insertion_point(serialize_to_array_start:google.protobuf.testing.oneofs.MoreData)
  // optional string str_value = 1;
  if (this->str_value().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->str_value().data(), this->str_value().length(),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "google.protobuf.testing.oneofs.MoreData.str_value");
    target =
      ::google::protobuf::internal::WireFormatLite::WriteStringToArray(
        1, this->str_value(), target);
  }

  // @@protoc_insertion_point(serialize_to_array_end:google.protobuf.testing.oneofs.MoreData)
  return target;
}

size_t MoreData::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:google.protobuf.testing.oneofs.MoreData)
  size_t total_size = 0;

  // optional string str_value = 1;
  if (this->str_value().size() > 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::StringSize(
        this->str_value());
  }

  int cached_size = ::google::protobuf::internal::ToCachedSize(total_size);
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = cached_size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
  return total_size;
}

void MoreData::MergeFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_merge_from_start:google.protobuf.testing.oneofs.MoreData)
  if (GOOGLE_PREDICT_FALSE(&from == this)) MergeFromFail(__LINE__);
  const MoreData* source =
      ::google::protobuf::internal::DynamicCastToGenerated<const MoreData>(
          &from);
  if (source == NULL) {
  // @@protoc_insertion_point(generalized_merge_from_cast_fail:google.protobuf.testing.oneofs.MoreData)
    ::google::protobuf::internal::ReflectionOps::Merge(from, this);
  } else {
  // @@protoc_insertion_point(generalized_merge_from_cast_success:google.protobuf.testing.oneofs.MoreData)
    UnsafeMergeFrom(*source);
  }
}

void MoreData::MergeFrom(const MoreData& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:google.protobuf.testing.oneofs.MoreData)
  if (GOOGLE_PREDICT_TRUE(&from != this)) {
    UnsafeMergeFrom(from);
  } else {
    MergeFromFail(__LINE__);
  }
}

void MoreData::UnsafeMergeFrom(const MoreData& from) {
  GOOGLE_DCHECK(&from != this);
  if (from.str_value().size() > 0) {

    str_value_.AssignWithDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), from.str_value_);
  }
}

void MoreData::CopyFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_copy_from_start:google.protobuf.testing.oneofs.MoreData)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

void MoreData::CopyFrom(const MoreData& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:google.protobuf.testing.oneofs.MoreData)
  if (&from == this) return;
  Clear();
  UnsafeMergeFrom(from);
}

bool MoreData::IsInitialized() const {

  return true;
}

void MoreData::Swap(MoreData* other) {
  if (other == this) return;
  InternalSwap(other);
}
void MoreData::InternalSwap(MoreData* other) {
  str_value_.Swap(&other->str_value_);
  _internal_metadata_.Swap(&other->_internal_metadata_);
  std::swap(_cached_size_, other->_cached_size_);
}

::google::protobuf::Metadata MoreData::GetMetadata() const {
  protobuf_AssignDescriptorsOnce();
  ::google::protobuf::Metadata metadata;
  metadata.descriptor = MoreData_descriptor_;
  metadata.reflection = MoreData_reflection_;
  return metadata;
}

#if PROTOBUF_INLINE_NOT_IN_HEADERS
// MoreData

// optional string str_value = 1;
void MoreData::clear_str_value() {
  str_value_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
const ::std::string& MoreData::str_value() const {
  // @@protoc_insertion_point(field_get:google.protobuf.testing.oneofs.MoreData.str_value)
  return str_value_.GetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
void MoreData::set_str_value(const ::std::string& value) {
  
  str_value_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:google.protobuf.testing.oneofs.MoreData.str_value)
}
void MoreData::set_str_value(const char* value) {
  
  str_value_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:google.protobuf.testing.oneofs.MoreData.str_value)
}
void MoreData::set_str_value(const char* value, size_t size) {
  
  str_value_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:google.protobuf.testing.oneofs.MoreData.str_value)
}
::std::string* MoreData::mutable_str_value() {
  
  // @@protoc_insertion_point(field_mutable:google.protobuf.testing.oneofs.MoreData.str_value)
  return str_value_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
::std::string* MoreData::release_str_value() {
  // @@protoc_insertion_point(field_release:google.protobuf.testing.oneofs.MoreData.str_value)
  
  return str_value_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
void MoreData::set_allocated_str_value(::std::string* str_value) {
  if (str_value != NULL) {
    
  } else {
    
  }
  str_value_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), str_value);
  // @@protoc_insertion_point(field_set_allocated:google.protobuf.testing.oneofs.MoreData.str_value)
}

inline const MoreData* MoreData::internal_default_instance() {
  return &MoreData_default_instance_.get();
}
#endif  // PROTOBUF_INLINE_NOT_IN_HEADERS

// ===================================================================

#if !defined(_MSC_VER) || _MSC_VER >= 1900
const int Response::kValueFieldNumber;
#endif  // !defined(_MSC_VER) || _MSC_VER >= 1900

Response::Response()
  : ::google::protobuf::Message(), _internal_metadata_(NULL) {
  if (this != internal_default_instance()) protobuf_InitDefaults_google_2fprotobuf_2futil_2finternal_2ftestdata_2foneofs_2eproto();
  SharedCtor();
  // @@protoc_insertion_point(constructor:google.protobuf.testing.oneofs.Response)
}

void Response::InitAsDefaultInstance() {
}

Response::Response(const Response& from)
  : ::google::protobuf::Message(),
    _internal_metadata_(NULL) {
  SharedCtor();
  UnsafeMergeFrom(from);
  // @@protoc_insertion_point(copy_constructor:google.protobuf.testing.oneofs.Response)
}

void Response::SharedCtor() {
  value_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  _cached_size_ = 0;
}

Response::~Response() {
  // @@protoc_insertion_point(destructor:google.protobuf.testing.oneofs.Response)
  SharedDtor();
}

void Response::SharedDtor() {
  value_.DestroyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}

void Response::SetCachedSize(int size) const {
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
}
const ::google::protobuf::Descriptor* Response::descriptor() {
  protobuf_AssignDescriptorsOnce();
  return Response_descriptor_;
}

const Response& Response::default_instance() {
  protobuf_InitDefaults_google_2fprotobuf_2futil_2finternal_2ftestdata_2foneofs_2eproto();
  return *internal_default_instance();
}

::google::protobuf::internal::ExplicitlyConstructed<Response> Response_default_instance_;

Response* Response::New(::google::protobuf::Arena* arena) const {
  Response* n = new Response;
  if (arena != NULL) {
    arena->Own(n);
  }
  return n;
}

void Response::Clear() {
// @@protoc_insertion_point(message_clear_start:google.protobuf.testing.oneofs.Response)
  value_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}

bool Response::MergePartialFromCodedStream(
    ::google::protobuf::io::CodedInputStream* input) {
#define DO_(EXPRESSION) if (!GOOGLE_PREDICT_TRUE(EXPRESSION)) goto failure
  ::google::protobuf::uint32 tag;
  // @@protoc_insertion_point(parse_start:google.protobuf.testing.oneofs.Response)
  for (;;) {
    ::std::pair< ::google::protobuf::uint32, bool> p = input->ReadTagWithCutoff(127);
    tag = p.first;
    if (!p.second) goto handle_unusual;
    switch (::google::protobuf::internal::WireFormatLite::GetTagFieldNumber(tag)) {
      // optional string value = 1;
      case 1: {
        if (tag == 10) {
          DO_(::google::protobuf::internal::WireFormatLite::ReadString(
                input, this->mutable_value()));
          DO_(::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
            this->value().data(), this->value().length(),
            ::google::protobuf::internal::WireFormatLite::PARSE,
            "google.protobuf.testing.oneofs.Response.value"));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectAtEnd()) goto success;
        break;
      }

      default: {
      handle_unusual:
        if (tag == 0 ||
            ::google::protobuf::internal::WireFormatLite::GetTagWireType(tag) ==
            ::google::protobuf::internal::WireFormatLite::WIRETYPE_END_GROUP) {
          goto success;
        }
        DO_(::google::protobuf::internal::WireFormatLite::SkipField(input, tag));
        break;
      }
    }
  }
success:
  // @@protoc_insertion_point(parse_success:google.protobuf.testing.oneofs.Response)
  return true;
failure:
  // @@protoc_insertion_point(parse_failure:google.protobuf.testing.oneofs.Response)
  return false;
#undef DO_
}

void Response::SerializeWithCachedSizes(
    ::google::protobuf::io::CodedOutputStream* output) const {
  // @@protoc_insertion_point(serialize_start:google.protobuf.testing.oneofs.Response)
  // optional string value = 1;
  if (this->value().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->value().data(), this->value().length(),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "google.protobuf.testing.oneofs.Response.value");
    ::google::protobuf::internal::WireFormatLite::WriteStringMaybeAliased(
      1, this->value(), output);
  }

  // @@protoc_insertion_point(serialize_end:google.protobuf.testing.oneofs.Response)
}

::google::protobuf::uint8* Response::InternalSerializeWithCachedSizesToArray(
    bool deterministic, ::google::protobuf::uint8* target) const {
  (void)deterministic; // Unused
  // @@protoc_insertion_point(serialize_to_array_start:google.protobuf.testing.oneofs.Response)
  // optional string value = 1;
  if (this->value().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->value().data(), this->value().length(),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "google.protobuf.testing.oneofs.Response.value");
    target =
      ::google::protobuf::internal::WireFormatLite::WriteStringToArray(
        1, this->value(), target);
  }

  // @@protoc_insertion_point(serialize_to_array_end:google.protobuf.testing.oneofs.Response)
  return target;
}

size_t Response::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:google.protobuf.testing.oneofs.Response)
  size_t total_size = 0;

  // optional string value = 1;
  if (this->value().size() > 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::StringSize(
        this->value());
  }

  int cached_size = ::google::protobuf::internal::ToCachedSize(total_size);
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = cached_size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
  return total_size;
}

void Response::MergeFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_merge_from_start:google.protobuf.testing.oneofs.Response)
  if (GOOGLE_PREDICT_FALSE(&from == this)) MergeFromFail(__LINE__);
  const Response* source =
      ::google::protobuf::internal::DynamicCastToGenerated<const Response>(
          &from);
  if (source == NULL) {
  // @@protoc_insertion_point(generalized_merge_from_cast_fail:google.protobuf.testing.oneofs.Response)
    ::google::protobuf::internal::ReflectionOps::Merge(from, this);
  } else {
  // @@protoc_insertion_point(generalized_merge_from_cast_success:google.protobuf.testing.oneofs.Response)
    UnsafeMergeFrom(*source);
  }
}

void Response::MergeFrom(const Response& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:google.protobuf.testing.oneofs.Response)
  if (GOOGLE_PREDICT_TRUE(&from != this)) {
    UnsafeMergeFrom(from);
  } else {
    MergeFromFail(__LINE__);
  }
}

void Response::UnsafeMergeFrom(const Response& from) {
  GOOGLE_DCHECK(&from != this);
  if (from.value().size() > 0) {

    value_.AssignWithDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), from.value_);
  }
}

void Response::CopyFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_copy_from_start:google.protobuf.testing.oneofs.Response)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

void Response::CopyFrom(const Response& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:google.protobuf.testing.oneofs.Response)
  if (&from == this) return;
  Clear();
  UnsafeMergeFrom(from);
}

bool Response::IsInitialized() const {

  return true;
}

void Response::Swap(Response* other) {
  if (other == this) return;
  InternalSwap(other);
}
void Response::InternalSwap(Response* other) {
  value_.Swap(&other->value_);
  _internal_metadata_.Swap(&other->_internal_metadata_);
  std::swap(_cached_size_, other->_cached_size_);
}

::google::protobuf::Metadata Response::GetMetadata() const {
  protobuf_AssignDescriptorsOnce();
  ::google::protobuf::Metadata metadata;
  metadata.descriptor = Response_descriptor_;
  metadata.reflection = Response_reflection_;
  return metadata;
}

#if PROTOBUF_INLINE_NOT_IN_HEADERS
// Response

// optional string value = 1;
void Response::clear_value() {
  value_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
const ::std::string& Response::value() const {
  // @@protoc_insertion_point(field_get:google.protobuf.testing.oneofs.Response.value)
  return value_.GetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
void Response::set_value(const ::std::string& value) {
  
  value_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:google.protobuf.testing.oneofs.Response.value)
}
void Response::set_value(const char* value) {
  
  value_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:google.protobuf.testing.oneofs.Response.value)
}
void Response::set_value(const char* value, size_t size) {
  
  value_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:google.protobuf.testing.oneofs.Response.value)
}
::std::string* Response::mutable_value() {
  
  // @@protoc_insertion_point(field_mutable:google.protobuf.testing.oneofs.Response.value)
  return value_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
::std::string* Response::release_value() {
  // @@protoc_insertion_point(field_release:google.protobuf.testing.oneofs.Response.value)
  
  return value_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
void Response::set_allocated_value(::std::string* value) {
  if (value != NULL) {
    
  } else {
    
  }
  value_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set_allocated:google.protobuf.testing.oneofs.Response.value)
}

inline const Response* Response::internal_default_instance() {
  return &Response_default_instance_.get();
}
#endif  // PROTOBUF_INLINE_NOT_IN_HEADERS

// @@protoc_insertion_point(namespace_scope)

}  // namespace oneofs
}  // namespace testing
}  // namespace protobuf
}  // namespace google

// @@protoc_insertion_point(global_scope)

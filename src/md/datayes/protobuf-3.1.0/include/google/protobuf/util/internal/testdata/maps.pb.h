// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: google/protobuf/util/internal/testdata/maps.proto

#ifndef PROTOBUF_google_2fprotobuf_2futil_2finternal_2ftestdata_2fmaps_2eproto__INCLUDED
#define PROTOBUF_google_2fprotobuf_2futil_2finternal_2ftestdata_2fmaps_2eproto__INCLUDED

#include <string>

#include <google/protobuf/stubs/common.h>

#if GOOGLE_PROTOBUF_VERSION < 3001000
#error This file was generated by a newer version of protoc which is
#error incompatible with your Protocol Buffer headers.  Please update
#error your headers.
#endif
#if 3001000 < GOOGLE_PROTOBUF_MIN_PROTOC_VERSION
#error This file was generated by an older version of protoc which is
#error incompatible with your Protocol Buffer headers.  Please
#error regenerate this file with a newer version of protoc.
#endif

#include <google/protobuf/arena.h>
#include <google/protobuf/arenastring.h>
#include <google/protobuf/generated_message_util.h>
#include <google/protobuf/metadata.h>
#include <google/protobuf/message.h>
#include <google/protobuf/repeated_field.h>
#include <google/protobuf/extension_set.h>
#include <google/protobuf/map.h>
#include <google/protobuf/map_field_inl.h>
#include <google/protobuf/generated_enum_reflection.h>
#include <google/protobuf/unknown_field_set.h>
// @@protoc_insertion_point(includes)

namespace google {
namespace protobuf {
namespace testing {

// Internal implementation detail -- do not call these.
void protobuf_AddDesc_google_2fprotobuf_2futil_2finternal_2ftestdata_2fmaps_2eproto();
void protobuf_InitDefaults_google_2fprotobuf_2futil_2finternal_2ftestdata_2fmaps_2eproto();
void protobuf_AssignDesc_google_2fprotobuf_2futil_2finternal_2ftestdata_2fmaps_2eproto();
void protobuf_ShutdownFile_google_2fprotobuf_2futil_2finternal_2ftestdata_2fmaps_2eproto();

class BoolToString;
class DummyRequest;
class EmptyMap;
class IntToString;
class MapIn;
class MapM;
class MapOfObjects;
class MapOfObjects_M;
class MapOut;
class MapOutWireFormat;
class MapOutWireFormat_Map1Entry;
class MapOutWireFormat_Map2Entry;
class MapOutWireFormat_Map3Entry;
class MapOutWireFormat_Map4Entry;
class MapsTestCases;
class Mixed1;
class Mixed2;
class StringtoInt;

enum Mixed2_E {
  Mixed2_E_E0 = 0,
  Mixed2_E_E1 = 1,
  Mixed2_E_E2 = 2,
  Mixed2_E_E3 = 3,
  Mixed2_E_Mixed2_E_INT_MIN_SENTINEL_DO_NOT_USE_ = ::google::protobuf::kint32min,
  Mixed2_E_Mixed2_E_INT_MAX_SENTINEL_DO_NOT_USE_ = ::google::protobuf::kint32max
};
bool Mixed2_E_IsValid(int value);
const Mixed2_E Mixed2_E_E_MIN = Mixed2_E_E0;
const Mixed2_E Mixed2_E_E_MAX = Mixed2_E_E3;
const int Mixed2_E_E_ARRAYSIZE = Mixed2_E_E_MAX + 1;

const ::google::protobuf::EnumDescriptor* Mixed2_E_descriptor();
inline const ::std::string& Mixed2_E_Name(Mixed2_E value) {
  return ::google::protobuf::internal::NameOfEnum(
    Mixed2_E_descriptor(), value);
}
inline bool Mixed2_E_Parse(
    const ::std::string& name, Mixed2_E* value) {
  return ::google::protobuf::internal::ParseNamedEnum<Mixed2_E>(
    Mixed2_E_descriptor(), name, value);
}
// ===================================================================

class MapsTestCases : public ::google::protobuf::Message /* @@protoc_insertion_point(class_definition:google.protobuf.testing.MapsTestCases) */ {
 public:
  MapsTestCases();
  virtual ~MapsTestCases();

  MapsTestCases(const MapsTestCases& from);

  inline MapsTestCases& operator=(const MapsTestCases& from) {
    CopyFrom(from);
    return *this;
  }

  static const ::google::protobuf::Descriptor* descriptor();
  static const MapsTestCases& default_instance();

  static const MapsTestCases* internal_default_instance();

  void Swap(MapsTestCases* other);

  // implements Message ----------------------------------------------

  inline MapsTestCases* New() const { return New(NULL); }

  MapsTestCases* New(::google::protobuf::Arena* arena) const;
  void CopyFrom(const ::google::protobuf::Message& from);
  void MergeFrom(const ::google::protobuf::Message& from);
  void CopyFrom(const MapsTestCases& from);
  void MergeFrom(const MapsTestCases& from);
  void Clear();
  bool IsInitialized() const;

  size_t ByteSizeLong() const;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input);
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const;
  ::google::protobuf::uint8* InternalSerializeWithCachedSizesToArray(
      bool deterministic, ::google::protobuf::uint8* output) const;
  ::google::protobuf::uint8* SerializeWithCachedSizesToArray(::google::protobuf::uint8* output) const {
    return InternalSerializeWithCachedSizesToArray(false, output);
  }
  int GetCachedSize() const { return _cached_size_; }
  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const;
  void InternalSwap(MapsTestCases* other);
  void UnsafeMergeFrom(const MapsTestCases& from);
  private:
  inline ::google::protobuf::Arena* GetArenaNoVirtual() const {
    return _internal_metadata_.arena();
  }
  inline void* MaybeArenaPtr() const {
    return _internal_metadata_.raw_arena_ptr();
  }
  public:

  ::google::protobuf::Metadata GetMetadata() const;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // optional .google.protobuf.testing.EmptyMap empty_map = 1;
  bool has_empty_map() const;
  void clear_empty_map();
  static const int kEmptyMapFieldNumber = 1;
  const ::google::protobuf::testing::EmptyMap& empty_map() const;
  ::google::protobuf::testing::EmptyMap* mutable_empty_map();
  ::google::protobuf::testing::EmptyMap* release_empty_map();
  void set_allocated_empty_map(::google::protobuf::testing::EmptyMap* empty_map);

  // optional .google.protobuf.testing.StringtoInt string_to_int = 2;
  bool has_string_to_int() const;
  void clear_string_to_int();
  static const int kStringToIntFieldNumber = 2;
  const ::google::protobuf::testing::StringtoInt& string_to_int() const;
  ::google::protobuf::testing::StringtoInt* mutable_string_to_int();
  ::google::protobuf::testing::StringtoInt* release_string_to_int();
  void set_allocated_string_to_int(::google::protobuf::testing::StringtoInt* string_to_int);

  // optional .google.protobuf.testing.IntToString int_to_string = 3;
  bool has_int_to_string() const;
  void clear_int_to_string();
  static const int kIntToStringFieldNumber = 3;
  const ::google::protobuf::testing::IntToString& int_to_string() const;
  ::google::protobuf::testing::IntToString* mutable_int_to_string();
  ::google::protobuf::testing::IntToString* release_int_to_string();
  void set_allocated_int_to_string(::google::protobuf::testing::IntToString* int_to_string);

  // optional .google.protobuf.testing.Mixed1 mixed1 = 4;
  bool has_mixed1() const;
  void clear_mixed1();
  static const int kMixed1FieldNumber = 4;
  const ::google::protobuf::testing::Mixed1& mixed1() const;
  ::google::protobuf::testing::Mixed1* mutable_mixed1();
  ::google::protobuf::testing::Mixed1* release_mixed1();
  void set_allocated_mixed1(::google::protobuf::testing::Mixed1* mixed1);

  // optional .google.protobuf.testing.Mixed2 mixed2 = 5;
  bool has_mixed2() const;
  void clear_mixed2();
  static const int kMixed2FieldNumber = 5;
  const ::google::protobuf::testing::Mixed2& mixed2() const;
  ::google::protobuf::testing::Mixed2* mutable_mixed2();
  ::google::protobuf::testing::Mixed2* release_mixed2();
  void set_allocated_mixed2(::google::protobuf::testing::Mixed2* mixed2);

  // optional .google.protobuf.testing.MapOfObjects map_of_objects = 6;
  bool has_map_of_objects() const;
  void clear_map_of_objects();
  static const int kMapOfObjectsFieldNumber = 6;
  const ::google::protobuf::testing::MapOfObjects& map_of_objects() const;
  ::google::protobuf::testing::MapOfObjects* mutable_map_of_objects();
  ::google::protobuf::testing::MapOfObjects* release_map_of_objects();
  void set_allocated_map_of_objects(::google::protobuf::testing::MapOfObjects* map_of_objects);

  // optional .google.protobuf.testing.StringtoInt empty_key_string_to_int1 = 7;
  bool has_empty_key_string_to_int1() const;
  void clear_empty_key_string_to_int1();
  static const int kEmptyKeyStringToInt1FieldNumber = 7;
  const ::google::protobuf::testing::StringtoInt& empty_key_string_to_int1() const;
  ::google::protobuf::testing::StringtoInt* mutable_empty_key_string_to_int1();
  ::google::protobuf::testing::StringtoInt* release_empty_key_string_to_int1();
  void set_allocated_empty_key_string_to_int1(::google::protobuf::testing::StringtoInt* empty_key_string_to_int1);

  // optional .google.protobuf.testing.StringtoInt empty_key_string_to_int2 = 8;
  bool has_empty_key_string_to_int2() const;
  void clear_empty_key_string_to_int2();
  static const int kEmptyKeyStringToInt2FieldNumber = 8;
  const ::google::protobuf::testing::StringtoInt& empty_key_string_to_int2() const;
  ::google::protobuf::testing::StringtoInt* mutable_empty_key_string_to_int2();
  ::google::protobuf::testing::StringtoInt* release_empty_key_string_to_int2();
  void set_allocated_empty_key_string_to_int2(::google::protobuf::testing::StringtoInt* empty_key_string_to_int2);

  // optional .google.protobuf.testing.StringtoInt empty_key_string_to_int3 = 9;
  bool has_empty_key_string_to_int3() const;
  void clear_empty_key_string_to_int3();
  static const int kEmptyKeyStringToInt3FieldNumber = 9;
  const ::google::protobuf::testing::StringtoInt& empty_key_string_to_int3() const;
  ::google::protobuf::testing::StringtoInt* mutable_empty_key_string_to_int3();
  ::google::protobuf::testing::StringtoInt* release_empty_key_string_to_int3();
  void set_allocated_empty_key_string_to_int3(::google::protobuf::testing::StringtoInt* empty_key_string_to_int3);

  // optional .google.protobuf.testing.BoolToString empty_key_bool_to_string = 10;
  bool has_empty_key_bool_to_string() const;
  void clear_empty_key_bool_to_string();
  static const int kEmptyKeyBoolToStringFieldNumber = 10;
  const ::google::protobuf::testing::BoolToString& empty_key_bool_to_string() const;
  ::google::protobuf::testing::BoolToString* mutable_empty_key_bool_to_string();
  ::google::protobuf::testing::BoolToString* release_empty_key_bool_to_string();
  void set_allocated_empty_key_bool_to_string(::google::protobuf::testing::BoolToString* empty_key_bool_to_string);

  // optional .google.protobuf.testing.IntToString empty_key_int_to_string = 11;
  bool has_empty_key_int_to_string() const;
  void clear_empty_key_int_to_string();
  static const int kEmptyKeyIntToStringFieldNumber = 11;
  const ::google::protobuf::testing::IntToString& empty_key_int_to_string() const;
  ::google::protobuf::testing::IntToString* mutable_empty_key_int_to_string();
  ::google::protobuf::testing::IntToString* release_empty_key_int_to_string();
  void set_allocated_empty_key_int_to_string(::google::protobuf::testing::IntToString* empty_key_int_to_string);

  // optional .google.protobuf.testing.Mixed1 empty_key_mixed = 12;
  bool has_empty_key_mixed() const;
  void clear_empty_key_mixed();
  static const int kEmptyKeyMixedFieldNumber = 12;
  const ::google::protobuf::testing::Mixed1& empty_key_mixed() const;
  ::google::protobuf::testing::Mixed1* mutable_empty_key_mixed();
  ::google::protobuf::testing::Mixed1* release_empty_key_mixed();
  void set_allocated_empty_key_mixed(::google::protobuf::testing::Mixed1* empty_key_mixed);

  // optional .google.protobuf.testing.MapOfObjects empty_key_map_objects = 13;
  bool has_empty_key_map_objects() const;
  void clear_empty_key_map_objects();
  static const int kEmptyKeyMapObjectsFieldNumber = 13;
  const ::google::protobuf::testing::MapOfObjects& empty_key_map_objects() const;
  ::google::protobuf::testing::MapOfObjects* mutable_empty_key_map_objects();
  ::google::protobuf::testing::MapOfObjects* release_empty_key_map_objects();
  void set_allocated_empty_key_map_objects(::google::protobuf::testing::MapOfObjects* empty_key_map_objects);

  // @@protoc_insertion_point(class_scope:google.protobuf.testing.MapsTestCases)
 private:

  ::google::protobuf::internal::InternalMetadataWithArena _internal_metadata_;
  ::google::protobuf::testing::EmptyMap* empty_map_;
  ::google::protobuf::testing::StringtoInt* string_to_int_;
  ::google::protobuf::testing::IntToString* int_to_string_;
  ::google::protobuf::testing::Mixed1* mixed1_;
  ::google::protobuf::testing::Mixed2* mixed2_;
  ::google::protobuf::testing::MapOfObjects* map_of_objects_;
  ::google::protobuf::testing::StringtoInt* empty_key_string_to_int1_;
  ::google::protobuf::testing::StringtoInt* empty_key_string_to_int2_;
  ::google::protobuf::testing::StringtoInt* empty_key_string_to_int3_;
  ::google::protobuf::testing::BoolToString* empty_key_bool_to_string_;
  ::google::protobuf::testing::IntToString* empty_key_int_to_string_;
  ::google::protobuf::testing::Mixed1* empty_key_mixed_;
  ::google::protobuf::testing::MapOfObjects* empty_key_map_objects_;
  mutable int _cached_size_;
  friend void  protobuf_InitDefaults_google_2fprotobuf_2futil_2finternal_2ftestdata_2fmaps_2eproto_impl();
  friend void  protobuf_AddDesc_google_2fprotobuf_2futil_2finternal_2ftestdata_2fmaps_2eproto_impl();
  friend void protobuf_AssignDesc_google_2fprotobuf_2futil_2finternal_2ftestdata_2fmaps_2eproto();
  friend void protobuf_ShutdownFile_google_2fprotobuf_2futil_2finternal_2ftestdata_2fmaps_2eproto();

  void InitAsDefaultInstance();
};
extern ::google::protobuf::internal::ExplicitlyConstructed<MapsTestCases> MapsTestCases_default_instance_;

// -------------------------------------------------------------------

class EmptyMap : public ::google::protobuf::Message /* @@protoc_insertion_point(class_definition:google.protobuf.testing.EmptyMap) */ {
 public:
  EmptyMap();
  virtual ~EmptyMap();

  EmptyMap(const EmptyMap& from);

  inline EmptyMap& operator=(const EmptyMap& from) {
    CopyFrom(from);
    return *this;
  }

  static const ::google::protobuf::Descriptor* descriptor();
  static const EmptyMap& default_instance();

  static const EmptyMap* internal_default_instance();

  void Swap(EmptyMap* other);

  // implements Message ----------------------------------------------

  inline EmptyMap* New() const { return New(NULL); }

  EmptyMap* New(::google::protobuf::Arena* arena) const;
  void CopyFrom(const ::google::protobuf::Message& from);
  void MergeFrom(const ::google::protobuf::Message& from);
  void CopyFrom(const EmptyMap& from);
  void MergeFrom(const EmptyMap& from);
  void Clear();
  bool IsInitialized() const;

  size_t ByteSizeLong() const;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input);
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const;
  ::google::protobuf::uint8* InternalSerializeWithCachedSizesToArray(
      bool deterministic, ::google::protobuf::uint8* output) const;
  ::google::protobuf::uint8* SerializeWithCachedSizesToArray(::google::protobuf::uint8* output) const {
    return InternalSerializeWithCachedSizesToArray(false, output);
  }
  int GetCachedSize() const { return _cached_size_; }
  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const;
  void InternalSwap(EmptyMap* other);
  void UnsafeMergeFrom(const EmptyMap& from);
  private:
  inline ::google::protobuf::Arena* GetArenaNoVirtual() const {
    return _internal_metadata_.arena();
  }
  inline void* MaybeArenaPtr() const {
    return _internal_metadata_.raw_arena_ptr();
  }
  public:

  ::google::protobuf::Metadata GetMetadata() const;

  // nested types ----------------------------------------------------


  // accessors -------------------------------------------------------

  // map<int32, int32> map = 1;
  int map_size() const;
  void clear_map();
  static const int kMapFieldNumber = 1;
  const ::google::protobuf::Map< ::google::protobuf::int32, ::google::protobuf::int32 >&
      map() const;
  ::google::protobuf::Map< ::google::protobuf::int32, ::google::protobuf::int32 >*
      mutable_map();

  // @@protoc_insertion_point(class_scope:google.protobuf.testing.EmptyMap)
 private:

  ::google::protobuf::internal::InternalMetadataWithArena _internal_metadata_;
  typedef ::google::protobuf::internal::MapEntryLite<
      ::google::protobuf::int32, ::google::protobuf::int32,
      ::google::protobuf::internal::WireFormatLite::TYPE_INT32,
      ::google::protobuf::internal::WireFormatLite::TYPE_INT32,
      0 >
      EmptyMap_MapEntry;
  ::google::protobuf::internal::MapField<
      ::google::protobuf::int32, ::google::protobuf::int32,
      ::google::protobuf::internal::WireFormatLite::TYPE_INT32,
      ::google::protobuf::internal::WireFormatLite::TYPE_INT32,
      0 > map_;
  mutable int _cached_size_;
  friend void  protobuf_InitDefaults_google_2fprotobuf_2futil_2finternal_2ftestdata_2fmaps_2eproto_impl();
  friend void  protobuf_AddDesc_google_2fprotobuf_2futil_2finternal_2ftestdata_2fmaps_2eproto_impl();
  friend void protobuf_AssignDesc_google_2fprotobuf_2futil_2finternal_2ftestdata_2fmaps_2eproto();
  friend void protobuf_ShutdownFile_google_2fprotobuf_2futil_2finternal_2ftestdata_2fmaps_2eproto();

  void InitAsDefaultInstance();
};
extern ::google::protobuf::internal::ExplicitlyConstructed<EmptyMap> EmptyMap_default_instance_;

// -------------------------------------------------------------------

class StringtoInt : public ::google::protobuf::Message /* @@protoc_insertion_point(class_definition:google.protobuf.testing.StringtoInt) */ {
 public:
  StringtoInt();
  virtual ~StringtoInt();

  StringtoInt(const StringtoInt& from);

  inline StringtoInt& operator=(const StringtoInt& from) {
    CopyFrom(from);
    return *this;
  }

  static const ::google::protobuf::Descriptor* descriptor();
  static const StringtoInt& default_instance();

  static const StringtoInt* internal_default_instance();

  void Swap(StringtoInt* other);

  // implements Message ----------------------------------------------

  inline StringtoInt* New() const { return New(NULL); }

  StringtoInt* New(::google::protobuf::Arena* arena) const;
  void CopyFrom(const ::google::protobuf::Message& from);
  void MergeFrom(const ::google::protobuf::Message& from);
  void CopyFrom(const StringtoInt& from);
  void MergeFrom(const StringtoInt& from);
  void Clear();
  bool IsInitialized() const;

  size_t ByteSizeLong() const;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input);
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const;
  ::google::protobuf::uint8* InternalSerializeWithCachedSizesToArray(
      bool deterministic, ::google::protobuf::uint8* output) const;
  ::google::protobuf::uint8* SerializeWithCachedSizesToArray(::google::protobuf::uint8* output) const {
    return InternalSerializeWithCachedSizesToArray(false, output);
  }
  int GetCachedSize() const { return _cached_size_; }
  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const;
  void InternalSwap(StringtoInt* other);
  void UnsafeMergeFrom(const StringtoInt& from);
  private:
  inline ::google::protobuf::Arena* GetArenaNoVirtual() const {
    return _internal_metadata_.arena();
  }
  inline void* MaybeArenaPtr() const {
    return _internal_metadata_.raw_arena_ptr();
  }
  public:

  ::google::protobuf::Metadata GetMetadata() const;

  // nested types ----------------------------------------------------


  // accessors -------------------------------------------------------

  // map<string, int32> map = 1;
  int map_size() const;
  void clear_map();
  static const int kMapFieldNumber = 1;
  const ::google::protobuf::Map< ::std::string, ::google::protobuf::int32 >&
      map() const;
  ::google::protobuf::Map< ::std::string, ::google::protobuf::int32 >*
      mutable_map();

  // @@protoc_insertion_point(class_scope:google.protobuf.testing.StringtoInt)
 private:

  ::google::protobuf::internal::InternalMetadataWithArena _internal_metadata_;
  typedef ::google::protobuf::internal::MapEntryLite<
      ::std::string, ::google::protobuf::int32,
      ::google::protobuf::internal::WireFormatLite::TYPE_STRING,
      ::google::protobuf::internal::WireFormatLite::TYPE_INT32,
      0 >
      StringtoInt_MapEntry;
  ::google::protobuf::internal::MapField<
      ::std::string, ::google::protobuf::int32,
      ::google::protobuf::internal::WireFormatLite::TYPE_STRING,
      ::google::protobuf::internal::WireFormatLite::TYPE_INT32,
      0 > map_;
  mutable int _cached_size_;
  friend void  protobuf_InitDefaults_google_2fprotobuf_2futil_2finternal_2ftestdata_2fmaps_2eproto_impl();
  friend void  protobuf_AddDesc_google_2fprotobuf_2futil_2finternal_2ftestdata_2fmaps_2eproto_impl();
  friend void protobuf_AssignDesc_google_2fprotobuf_2futil_2finternal_2ftestdata_2fmaps_2eproto();
  friend void protobuf_ShutdownFile_google_2fprotobuf_2futil_2finternal_2ftestdata_2fmaps_2eproto();

  void InitAsDefaultInstance();
};
extern ::google::protobuf::internal::ExplicitlyConstructed<StringtoInt> StringtoInt_default_instance_;

// -------------------------------------------------------------------

class IntToString : public ::google::protobuf::Message /* @@protoc_insertion_point(class_definition:google.protobuf.testing.IntToString) */ {
 public:
  IntToString();
  virtual ~IntToString();

  IntToString(const IntToString& from);

  inline IntToString& operator=(const IntToString& from) {
    CopyFrom(from);
    return *this;
  }

  static const ::google::protobuf::Descriptor* descriptor();
  static const IntToString& default_instance();

  static const IntToString* internal_default_instance();

  void Swap(IntToString* other);

  // implements Message ----------------------------------------------

  inline IntToString* New() const { return New(NULL); }

  IntToString* New(::google::protobuf::Arena* arena) const;
  void CopyFrom(const ::google::protobuf::Message& from);
  void MergeFrom(const ::google::protobuf::Message& from);
  void CopyFrom(const IntToString& from);
  void MergeFrom(const IntToString& from);
  void Clear();
  bool IsInitialized() const;

  size_t ByteSizeLong() const;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input);
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const;
  ::google::protobuf::uint8* InternalSerializeWithCachedSizesToArray(
      bool deterministic, ::google::protobuf::uint8* output) const;
  ::google::protobuf::uint8* SerializeWithCachedSizesToArray(::google::protobuf::uint8* output) const {
    return InternalSerializeWithCachedSizesToArray(false, output);
  }
  int GetCachedSize() const { return _cached_size_; }
  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const;
  void InternalSwap(IntToString* other);
  void UnsafeMergeFrom(const IntToString& from);
  private:
  inline ::google::protobuf::Arena* GetArenaNoVirtual() const {
    return _internal_metadata_.arena();
  }
  inline void* MaybeArenaPtr() const {
    return _internal_metadata_.raw_arena_ptr();
  }
  public:

  ::google::protobuf::Metadata GetMetadata() const;

  // nested types ----------------------------------------------------


  // accessors -------------------------------------------------------

  // map<int32, string> map = 1;
  int map_size() const;
  void clear_map();
  static const int kMapFieldNumber = 1;
  const ::google::protobuf::Map< ::google::protobuf::int32, ::std::string >&
      map() const;
  ::google::protobuf::Map< ::google::protobuf::int32, ::std::string >*
      mutable_map();

  // @@protoc_insertion_point(class_scope:google.protobuf.testing.IntToString)
 private:

  ::google::protobuf::internal::InternalMetadataWithArena _internal_metadata_;
  typedef ::google::protobuf::internal::MapEntryLite<
      ::google::protobuf::int32, ::std::string,
      ::google::protobuf::internal::WireFormatLite::TYPE_INT32,
      ::google::protobuf::internal::WireFormatLite::TYPE_STRING,
      0 >
      IntToString_MapEntry;
  ::google::protobuf::internal::MapField<
      ::google::protobuf::int32, ::std::string,
      ::google::protobuf::internal::WireFormatLite::TYPE_INT32,
      ::google::protobuf::internal::WireFormatLite::TYPE_STRING,
      0 > map_;
  mutable int _cached_size_;
  friend void  protobuf_InitDefaults_google_2fprotobuf_2futil_2finternal_2ftestdata_2fmaps_2eproto_impl();
  friend void  protobuf_AddDesc_google_2fprotobuf_2futil_2finternal_2ftestdata_2fmaps_2eproto_impl();
  friend void protobuf_AssignDesc_google_2fprotobuf_2futil_2finternal_2ftestdata_2fmaps_2eproto();
  friend void protobuf_ShutdownFile_google_2fprotobuf_2futil_2finternal_2ftestdata_2fmaps_2eproto();

  void InitAsDefaultInstance();
};
extern ::google::protobuf::internal::ExplicitlyConstructed<IntToString> IntToString_default_instance_;

// -------------------------------------------------------------------

class BoolToString : public ::google::protobuf::Message /* @@protoc_insertion_point(class_definition:google.protobuf.testing.BoolToString) */ {
 public:
  BoolToString();
  virtual ~BoolToString();

  BoolToString(const BoolToString& from);

  inline BoolToString& operator=(const BoolToString& from) {
    CopyFrom(from);
    return *this;
  }

  static const ::google::protobuf::Descriptor* descriptor();
  static const BoolToString& default_instance();

  static const BoolToString* internal_default_instance();

  void Swap(BoolToString* other);

  // implements Message ----------------------------------------------

  inline BoolToString* New() const { return New(NULL); }

  BoolToString* New(::google::protobuf::Arena* arena) const;
  void CopyFrom(const ::google::protobuf::Message& from);
  void MergeFrom(const ::google::protobuf::Message& from);
  void CopyFrom(const BoolToString& from);
  void MergeFrom(const BoolToString& from);
  void Clear();
  bool IsInitialized() const;

  size_t ByteSizeLong() const;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input);
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const;
  ::google::protobuf::uint8* InternalSerializeWithCachedSizesToArray(
      bool deterministic, ::google::protobuf::uint8* output) const;
  ::google::protobuf::uint8* SerializeWithCachedSizesToArray(::google::protobuf::uint8* output) const {
    return InternalSerializeWithCachedSizesToArray(false, output);
  }
  int GetCachedSize() const { return _cached_size_; }
  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const;
  void InternalSwap(BoolToString* other);
  void UnsafeMergeFrom(const BoolToString& from);
  private:
  inline ::google::protobuf::Arena* GetArenaNoVirtual() const {
    return _internal_metadata_.arena();
  }
  inline void* MaybeArenaPtr() const {
    return _internal_metadata_.raw_arena_ptr();
  }
  public:

  ::google::protobuf::Metadata GetMetadata() const;

  // nested types ----------------------------------------------------


  // accessors -------------------------------------------------------

  // map<bool, string> map = 1;
  int map_size() const;
  void clear_map();
  static const int kMapFieldNumber = 1;
  const ::google::protobuf::Map< bool, ::std::string >&
      map() const;
  ::google::protobuf::Map< bool, ::std::string >*
      mutable_map();

  // @@protoc_insertion_point(class_scope:google.protobuf.testing.BoolToString)
 private:

  ::google::protobuf::internal::InternalMetadataWithArena _internal_metadata_;
  typedef ::google::protobuf::internal::MapEntryLite<
      bool, ::std::string,
      ::google::protobuf::internal::WireFormatLite::TYPE_BOOL,
      ::google::protobuf::internal::WireFormatLite::TYPE_STRING,
      0 >
      BoolToString_MapEntry;
  ::google::protobuf::internal::MapField<
      bool, ::std::string,
      ::google::protobuf::internal::WireFormatLite::TYPE_BOOL,
      ::google::protobuf::internal::WireFormatLite::TYPE_STRING,
      0 > map_;
  mutable int _cached_size_;
  friend void  protobuf_InitDefaults_google_2fprotobuf_2futil_2finternal_2ftestdata_2fmaps_2eproto_impl();
  friend void  protobuf_AddDesc_google_2fprotobuf_2futil_2finternal_2ftestdata_2fmaps_2eproto_impl();
  friend void protobuf_AssignDesc_google_2fprotobuf_2futil_2finternal_2ftestdata_2fmaps_2eproto();
  friend void protobuf_ShutdownFile_google_2fprotobuf_2futil_2finternal_2ftestdata_2fmaps_2eproto();

  void InitAsDefaultInstance();
};
extern ::google::protobuf::internal::ExplicitlyConstructed<BoolToString> BoolToString_default_instance_;

// -------------------------------------------------------------------

class Mixed1 : public ::google::protobuf::Message /* @@protoc_insertion_point(class_definition:google.protobuf.testing.Mixed1) */ {
 public:
  Mixed1();
  virtual ~Mixed1();

  Mixed1(const Mixed1& from);

  inline Mixed1& operator=(const Mixed1& from) {
    CopyFrom(from);
    return *this;
  }

  static const ::google::protobuf::Descriptor* descriptor();
  static const Mixed1& default_instance();

  static const Mixed1* internal_default_instance();

  void Swap(Mixed1* other);

  // implements Message ----------------------------------------------

  inline Mixed1* New() const { return New(NULL); }

  Mixed1* New(::google::protobuf::Arena* arena) const;
  void CopyFrom(const ::google::protobuf::Message& from);
  void MergeFrom(const ::google::protobuf::Message& from);
  void CopyFrom(const Mixed1& from);
  void MergeFrom(const Mixed1& from);
  void Clear();
  bool IsInitialized() const;

  size_t ByteSizeLong() const;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input);
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const;
  ::google::protobuf::uint8* InternalSerializeWithCachedSizesToArray(
      bool deterministic, ::google::protobuf::uint8* output) const;
  ::google::protobuf::uint8* SerializeWithCachedSizesToArray(::google::protobuf::uint8* output) const {
    return InternalSerializeWithCachedSizesToArray(false, output);
  }
  int GetCachedSize() const { return _cached_size_; }
  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const;
  void InternalSwap(Mixed1* other);
  void UnsafeMergeFrom(const Mixed1& from);
  private:
  inline ::google::protobuf::Arena* GetArenaNoVirtual() const {
    return _internal_metadata_.arena();
  }
  inline void* MaybeArenaPtr() const {
    return _internal_metadata_.raw_arena_ptr();
  }
  public:

  ::google::protobuf::Metadata GetMetadata() const;

  // nested types ----------------------------------------------------


  // accessors -------------------------------------------------------

  // optional string msg = 1;
  void clear_msg();
  static const int kMsgFieldNumber = 1;
  const ::std::string& msg() const;
  void set_msg(const ::std::string& value);
  void set_msg(const char* value);
  void set_msg(const char* value, size_t size);
  ::std::string* mutable_msg();
  ::std::string* release_msg();
  void set_allocated_msg(::std::string* msg);

  // map<string, float> map = 2;
  int map_size() const;
  void clear_map();
  static const int kMapFieldNumber = 2;
  const ::google::protobuf::Map< ::std::string, float >&
      map() const;
  ::google::protobuf::Map< ::std::string, float >*
      mutable_map();

  // @@protoc_insertion_point(class_scope:google.protobuf.testing.Mixed1)
 private:

  ::google::protobuf::internal::InternalMetadataWithArena _internal_metadata_;
  typedef ::google::protobuf::internal::MapEntryLite<
      ::std::string, float,
      ::google::protobuf::internal::WireFormatLite::TYPE_STRING,
      ::google::protobuf::internal::WireFormatLite::TYPE_FLOAT,
      0 >
      Mixed1_MapEntry;
  ::google::protobuf::internal::MapField<
      ::std::string, float,
      ::google::protobuf::internal::WireFormatLite::TYPE_STRING,
      ::google::protobuf::internal::WireFormatLite::TYPE_FLOAT,
      0 > map_;
  ::google::protobuf::internal::ArenaStringPtr msg_;
  mutable int _cached_size_;
  friend void  protobuf_InitDefaults_google_2fprotobuf_2futil_2finternal_2ftestdata_2fmaps_2eproto_impl();
  friend void  protobuf_AddDesc_google_2fprotobuf_2futil_2finternal_2ftestdata_2fmaps_2eproto_impl();
  friend void protobuf_AssignDesc_google_2fprotobuf_2futil_2finternal_2ftestdata_2fmaps_2eproto();
  friend void protobuf_ShutdownFile_google_2fprotobuf_2futil_2finternal_2ftestdata_2fmaps_2eproto();

  void InitAsDefaultInstance();
};
extern ::google::protobuf::internal::ExplicitlyConstructed<Mixed1> Mixed1_default_instance_;

// -------------------------------------------------------------------

class Mixed2 : public ::google::protobuf::Message /* @@protoc_insertion_point(class_definition:google.protobuf.testing.Mixed2) */ {
 public:
  Mixed2();
  virtual ~Mixed2();

  Mixed2(const Mixed2& from);

  inline Mixed2& operator=(const Mixed2& from) {
    CopyFrom(from);
    return *this;
  }

  static const ::google::protobuf::Descriptor* descriptor();
  static const Mixed2& default_instance();

  static const Mixed2* internal_default_instance();

  void Swap(Mixed2* other);

  // implements Message ----------------------------------------------

  inline Mixed2* New() const { return New(NULL); }

  Mixed2* New(::google::protobuf::Arena* arena) const;
  void CopyFrom(const ::google::protobuf::Message& from);
  void MergeFrom(const ::google::protobuf::Message& from);
  void CopyFrom(const Mixed2& from);
  void MergeFrom(const Mixed2& from);
  void Clear();
  bool IsInitialized() const;

  size_t ByteSizeLong() const;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input);
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const;
  ::google::protobuf::uint8* InternalSerializeWithCachedSizesToArray(
      bool deterministic, ::google::protobuf::uint8* output) const;
  ::google::protobuf::uint8* SerializeWithCachedSizesToArray(::google::protobuf::uint8* output) const {
    return InternalSerializeWithCachedSizesToArray(false, output);
  }
  int GetCachedSize() const { return _cached_size_; }
  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const;
  void InternalSwap(Mixed2* other);
  void UnsafeMergeFrom(const Mixed2& from);
  private:
  inline ::google::protobuf::Arena* GetArenaNoVirtual() const {
    return _internal_metadata_.arena();
  }
  inline void* MaybeArenaPtr() const {
    return _internal_metadata_.raw_arena_ptr();
  }
  public:

  ::google::protobuf::Metadata GetMetadata() const;

  // nested types ----------------------------------------------------


  typedef Mixed2_E E;
  static const E E0 =
    Mixed2_E_E0;
  static const E E1 =
    Mixed2_E_E1;
  static const E E2 =
    Mixed2_E_E2;
  static const E E3 =
    Mixed2_E_E3;
  static inline bool E_IsValid(int value) {
    return Mixed2_E_IsValid(value);
  }
  static const E E_MIN =
    Mixed2_E_E_MIN;
  static const E E_MAX =
    Mixed2_E_E_MAX;
  static const int E_ARRAYSIZE =
    Mixed2_E_E_ARRAYSIZE;
  static inline const ::google::protobuf::EnumDescriptor*
  E_descriptor() {
    return Mixed2_E_descriptor();
  }
  static inline const ::std::string& E_Name(E value) {
    return Mixed2_E_Name(value);
  }
  static inline bool E_Parse(const ::std::string& name,
      E* value) {
    return Mixed2_E_Parse(name, value);
  }

  // accessors -------------------------------------------------------

  // map<int32, bool> map = 1;
  int map_size() const;
  void clear_map();
  static const int kMapFieldNumber = 1;
  const ::google::protobuf::Map< ::google::protobuf::int32, bool >&
      map() const;
  ::google::protobuf::Map< ::google::protobuf::int32, bool >*
      mutable_map();

  // optional .google.protobuf.testing.Mixed2.E ee = 2;
  void clear_ee();
  static const int kEeFieldNumber = 2;
  ::google::protobuf::testing::Mixed2_E ee() const;
  void set_ee(::google::protobuf::testing::Mixed2_E value);

  // @@protoc_insertion_point(class_scope:google.protobuf.testing.Mixed2)
 private:

  ::google::protobuf::internal::InternalMetadataWithArena _internal_metadata_;
  typedef ::google::protobuf::internal::MapEntryLite<
      ::google::protobuf::int32, bool,
      ::google::protobuf::internal::WireFormatLite::TYPE_INT32,
      ::google::protobuf::internal::WireFormatLite::TYPE_BOOL,
      0 >
      Mixed2_MapEntry;
  ::google::protobuf::internal::MapField<
      ::google::protobuf::int32, bool,
      ::google::protobuf::internal::WireFormatLite::TYPE_INT32,
      ::google::protobuf::internal::WireFormatLite::TYPE_BOOL,
      0 > map_;
  int ee_;
  mutable int _cached_size_;
  friend void  protobuf_InitDefaults_google_2fprotobuf_2futil_2finternal_2ftestdata_2fmaps_2eproto_impl();
  friend void  protobuf_AddDesc_google_2fprotobuf_2futil_2finternal_2ftestdata_2fmaps_2eproto_impl();
  friend void protobuf_AssignDesc_google_2fprotobuf_2futil_2finternal_2ftestdata_2fmaps_2eproto();
  friend void protobuf_ShutdownFile_google_2fprotobuf_2futil_2finternal_2ftestdata_2fmaps_2eproto();

  void InitAsDefaultInstance();
};
extern ::google::protobuf::internal::ExplicitlyConstructed<Mixed2> Mixed2_default_instance_;

// -------------------------------------------------------------------

class MapOfObjects_M : public ::google::protobuf::Message /* @@protoc_insertion_point(class_definition:google.protobuf.testing.MapOfObjects.M) */ {
 public:
  MapOfObjects_M();
  virtual ~MapOfObjects_M();

  MapOfObjects_M(const MapOfObjects_M& from);

  inline MapOfObjects_M& operator=(const MapOfObjects_M& from) {
    CopyFrom(from);
    return *this;
  }

  static const ::google::protobuf::Descriptor* descriptor();
  static const MapOfObjects_M& default_instance();

  static const MapOfObjects_M* internal_default_instance();

  void Swap(MapOfObjects_M* other);

  // implements Message ----------------------------------------------

  inline MapOfObjects_M* New() const { return New(NULL); }

  MapOfObjects_M* New(::google::protobuf::Arena* arena) const;
  void CopyFrom(const ::google::protobuf::Message& from);
  void MergeFrom(const ::google::protobuf::Message& from);
  void CopyFrom(const MapOfObjects_M& from);
  void MergeFrom(const MapOfObjects_M& from);
  void Clear();
  bool IsInitialized() const;

  size_t ByteSizeLong() const;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input);
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const;
  ::google::protobuf::uint8* InternalSerializeWithCachedSizesToArray(
      bool deterministic, ::google::protobuf::uint8* output) const;
  ::google::protobuf::uint8* SerializeWithCachedSizesToArray(::google::protobuf::uint8* output) const {
    return InternalSerializeWithCachedSizesToArray(false, output);
  }
  int GetCachedSize() const { return _cached_size_; }
  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const;
  void InternalSwap(MapOfObjects_M* other);
  void UnsafeMergeFrom(const MapOfObjects_M& from);
  private:
  inline ::google::protobuf::Arena* GetArenaNoVirtual() const {
    return _internal_metadata_.arena();
  }
  inline void* MaybeArenaPtr() const {
    return _internal_metadata_.raw_arena_ptr();
  }
  public:

  ::google::protobuf::Metadata GetMetadata() const;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // optional string inner_text = 1;
  void clear_inner_text();
  static const int kInnerTextFieldNumber = 1;
  const ::std::string& inner_text() const;
  void set_inner_text(const ::std::string& value);
  void set_inner_text(const char* value);
  void set_inner_text(const char* value, size_t size);
  ::std::string* mutable_inner_text();
  ::std::string* release_inner_text();
  void set_allocated_inner_text(::std::string* inner_text);

  // @@protoc_insertion_point(class_scope:google.protobuf.testing.MapOfObjects.M)
 private:

  ::google::protobuf::internal::InternalMetadataWithArena _internal_metadata_;
  ::google::protobuf::internal::ArenaStringPtr inner_text_;
  mutable int _cached_size_;
  friend void  protobuf_InitDefaults_google_2fprotobuf_2futil_2finternal_2ftestdata_2fmaps_2eproto_impl();
  friend void  protobuf_AddDesc_google_2fprotobuf_2futil_2finternal_2ftestdata_2fmaps_2eproto_impl();
  friend void protobuf_AssignDesc_google_2fprotobuf_2futil_2finternal_2ftestdata_2fmaps_2eproto();
  friend void protobuf_ShutdownFile_google_2fprotobuf_2futil_2finternal_2ftestdata_2fmaps_2eproto();

  void InitAsDefaultInstance();
};
extern ::google::protobuf::internal::ExplicitlyConstructed<MapOfObjects_M> MapOfObjects_M_default_instance_;

// -------------------------------------------------------------------

class MapOfObjects : public ::google::protobuf::Message /* @@protoc_insertion_point(class_definition:google.protobuf.testing.MapOfObjects) */ {
 public:
  MapOfObjects();
  virtual ~MapOfObjects();

  MapOfObjects(const MapOfObjects& from);

  inline MapOfObjects& operator=(const MapOfObjects& from) {
    CopyFrom(from);
    return *this;
  }

  static const ::google::protobuf::Descriptor* descriptor();
  static const MapOfObjects& default_instance();

  static const MapOfObjects* internal_default_instance();

  void Swap(MapOfObjects* other);

  // implements Message ----------------------------------------------

  inline MapOfObjects* New() const { return New(NULL); }

  MapOfObjects* New(::google::protobuf::Arena* arena) const;
  void CopyFrom(const ::google::protobuf::Message& from);
  void MergeFrom(const ::google::protobuf::Message& from);
  void CopyFrom(const MapOfObjects& from);
  void MergeFrom(const MapOfObjects& from);
  void Clear();
  bool IsInitialized() const;

  size_t ByteSizeLong() const;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input);
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const;
  ::google::protobuf::uint8* InternalSerializeWithCachedSizesToArray(
      bool deterministic, ::google::protobuf::uint8* output) const;
  ::google::protobuf::uint8* SerializeWithCachedSizesToArray(::google::protobuf::uint8* output) const {
    return InternalSerializeWithCachedSizesToArray(false, output);
  }
  int GetCachedSize() const { return _cached_size_; }
  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const;
  void InternalSwap(MapOfObjects* other);
  void UnsafeMergeFrom(const MapOfObjects& from);
  private:
  inline ::google::protobuf::Arena* GetArenaNoVirtual() const {
    return _internal_metadata_.arena();
  }
  inline void* MaybeArenaPtr() const {
    return _internal_metadata_.raw_arena_ptr();
  }
  public:

  ::google::protobuf::Metadata GetMetadata() const;

  // nested types ----------------------------------------------------

  typedef MapOfObjects_M M;

  // accessors -------------------------------------------------------

  // map<string, .google.protobuf.testing.MapOfObjects.M> map = 1;
  int map_size() const;
  void clear_map();
  static const int kMapFieldNumber = 1;
  const ::google::protobuf::Map< ::std::string, ::google::protobuf::testing::MapOfObjects_M >&
      map() const;
  ::google::protobuf::Map< ::std::string, ::google::protobuf::testing::MapOfObjects_M >*
      mutable_map();

  // @@protoc_insertion_point(class_scope:google.protobuf.testing.MapOfObjects)
 private:

  ::google::protobuf::internal::InternalMetadataWithArena _internal_metadata_;
  typedef ::google::protobuf::internal::MapEntryLite<
      ::std::string, ::google::protobuf::testing::MapOfObjects_M,
      ::google::protobuf::internal::WireFormatLite::TYPE_STRING,
      ::google::protobuf::internal::WireFormatLite::TYPE_MESSAGE,
      0 >
      MapOfObjects_MapEntry;
  ::google::protobuf::internal::MapField<
      ::std::string, ::google::protobuf::testing::MapOfObjects_M,
      ::google::protobuf::internal::WireFormatLite::TYPE_STRING,
      ::google::protobuf::internal::WireFormatLite::TYPE_MESSAGE,
      0 > map_;
  mutable int _cached_size_;
  friend void  protobuf_InitDefaults_google_2fprotobuf_2futil_2finternal_2ftestdata_2fmaps_2eproto_impl();
  friend void  protobuf_AddDesc_google_2fprotobuf_2futil_2finternal_2ftestdata_2fmaps_2eproto_impl();
  friend void protobuf_AssignDesc_google_2fprotobuf_2futil_2finternal_2ftestdata_2fmaps_2eproto();
  friend void protobuf_ShutdownFile_google_2fprotobuf_2futil_2finternal_2ftestdata_2fmaps_2eproto();

  void InitAsDefaultInstance();
};
extern ::google::protobuf::internal::ExplicitlyConstructed<MapOfObjects> MapOfObjects_default_instance_;

// -------------------------------------------------------------------

class DummyRequest : public ::google::protobuf::Message /* @@protoc_insertion_point(class_definition:google.protobuf.testing.DummyRequest) */ {
 public:
  DummyRequest();
  virtual ~DummyRequest();

  DummyRequest(const DummyRequest& from);

  inline DummyRequest& operator=(const DummyRequest& from) {
    CopyFrom(from);
    return *this;
  }

  static const ::google::protobuf::Descriptor* descriptor();
  static const DummyRequest& default_instance();

  static const DummyRequest* internal_default_instance();

  void Swap(DummyRequest* other);

  // implements Message ----------------------------------------------

  inline DummyRequest* New() const { return New(NULL); }

  DummyRequest* New(::google::protobuf::Arena* arena) const;
  void CopyFrom(const ::google::protobuf::Message& from);
  void MergeFrom(const ::google::protobuf::Message& from);
  void CopyFrom(const DummyRequest& from);
  void MergeFrom(const DummyRequest& from);
  void Clear();
  bool IsInitialized() const;

  size_t ByteSizeLong() const;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input);
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const;
  ::google::protobuf::uint8* InternalSerializeWithCachedSizesToArray(
      bool deterministic, ::google::protobuf::uint8* output) const;
  ::google::protobuf::uint8* SerializeWithCachedSizesToArray(::google::protobuf::uint8* output) const {
    return InternalSerializeWithCachedSizesToArray(false, output);
  }
  int GetCachedSize() const { return _cached_size_; }
  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const;
  void InternalSwap(DummyRequest* other);
  void UnsafeMergeFrom(const DummyRequest& from);
  private:
  inline ::google::protobuf::Arena* GetArenaNoVirtual() const {
    return _internal_metadata_.arena();
  }
  inline void* MaybeArenaPtr() const {
    return _internal_metadata_.raw_arena_ptr();
  }
  public:

  ::google::protobuf::Metadata GetMetadata() const;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // @@protoc_insertion_point(class_scope:google.protobuf.testing.DummyRequest)
 private:

  ::google::protobuf::internal::InternalMetadataWithArena _internal_metadata_;
  mutable int _cached_size_;
  friend void  protobuf_InitDefaults_google_2fprotobuf_2futil_2finternal_2ftestdata_2fmaps_2eproto_impl();
  friend void  protobuf_AddDesc_google_2fprotobuf_2futil_2finternal_2ftestdata_2fmaps_2eproto_impl();
  friend void protobuf_AssignDesc_google_2fprotobuf_2futil_2finternal_2ftestdata_2fmaps_2eproto();
  friend void protobuf_ShutdownFile_google_2fprotobuf_2futil_2finternal_2ftestdata_2fmaps_2eproto();

  void InitAsDefaultInstance();
};
extern ::google::protobuf::internal::ExplicitlyConstructed<DummyRequest> DummyRequest_default_instance_;

// -------------------------------------------------------------------

class MapIn : public ::google::protobuf::Message /* @@protoc_insertion_point(class_definition:google.protobuf.testing.MapIn) */ {
 public:
  MapIn();
  virtual ~MapIn();

  MapIn(const MapIn& from);

  inline MapIn& operator=(const MapIn& from) {
    CopyFrom(from);
    return *this;
  }

  static const ::google::protobuf::Descriptor* descriptor();
  static const MapIn& default_instance();

  static const MapIn* internal_default_instance();

  void Swap(MapIn* other);

  // implements Message ----------------------------------------------

  inline MapIn* New() const { return New(NULL); }

  MapIn* New(::google::protobuf::Arena* arena) const;
  void CopyFrom(const ::google::protobuf::Message& from);
  void MergeFrom(const ::google::protobuf::Message& from);
  void CopyFrom(const MapIn& from);
  void MergeFrom(const MapIn& from);
  void Clear();
  bool IsInitialized() const;

  size_t ByteSizeLong() const;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input);
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const;
  ::google::protobuf::uint8* InternalSerializeWithCachedSizesToArray(
      bool deterministic, ::google::protobuf::uint8* output) const;
  ::google::protobuf::uint8* SerializeWithCachedSizesToArray(::google::protobuf::uint8* output) const {
    return InternalSerializeWithCachedSizesToArray(false, output);
  }
  int GetCachedSize() const { return _cached_size_; }
  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const;
  void InternalSwap(MapIn* other);
  void UnsafeMergeFrom(const MapIn& from);
  private:
  inline ::google::protobuf::Arena* GetArenaNoVirtual() const {
    return _internal_metadata_.arena();
  }
  inline void* MaybeArenaPtr() const {
    return _internal_metadata_.raw_arena_ptr();
  }
  public:

  ::google::protobuf::Metadata GetMetadata() const;

  // nested types ----------------------------------------------------


  // accessors -------------------------------------------------------

  // optional string other = 1;
  void clear_other();
  static const int kOtherFieldNumber = 1;
  const ::std::string& other() const;
  void set_other(const ::std::string& value);
  void set_other(const char* value);
  void set_other(const char* value, size_t size);
  ::std::string* mutable_other();
  ::std::string* release_other();
  void set_allocated_other(::std::string* other);

  // repeated string things = 2;
  int things_size() const;
  void clear_things();
  static const int kThingsFieldNumber = 2;
  const ::std::string& things(int index) const;
  ::std::string* mutable_things(int index);
  void set_things(int index, const ::std::string& value);
  void set_things(int index, const char* value);
  void set_things(int index, const char* value, size_t size);
  ::std::string* add_things();
  void add_things(const ::std::string& value);
  void add_things(const char* value);
  void add_things(const char* value, size_t size);
  const ::google::protobuf::RepeatedPtrField< ::std::string>& things() const;
  ::google::protobuf::RepeatedPtrField< ::std::string>* mutable_things();

  // map<string, string> map_input = 3;
  int map_input_size() const;
  void clear_map_input();
  static const int kMapInputFieldNumber = 3;
  const ::google::protobuf::Map< ::std::string, ::std::string >&
      map_input() const;
  ::google::protobuf::Map< ::std::string, ::std::string >*
      mutable_map_input();

  // @@protoc_insertion_point(class_scope:google.protobuf.testing.MapIn)
 private:

  ::google::protobuf::internal::InternalMetadataWithArena _internal_metadata_;
  ::google::protobuf::RepeatedPtrField< ::std::string> things_;
  typedef ::google::protobuf::internal::MapEntryLite<
      ::std::string, ::std::string,
      ::google::protobuf::internal::WireFormatLite::TYPE_STRING,
      ::google::protobuf::internal::WireFormatLite::TYPE_STRING,
      0 >
      MapIn_MapInputEntry;
  ::google::protobuf::internal::MapField<
      ::std::string, ::std::string,
      ::google::protobuf::internal::WireFormatLite::TYPE_STRING,
      ::google::protobuf::internal::WireFormatLite::TYPE_STRING,
      0 > map_input_;
  ::google::protobuf::internal::ArenaStringPtr other_;
  mutable int _cached_size_;
  friend void  protobuf_InitDefaults_google_2fprotobuf_2futil_2finternal_2ftestdata_2fmaps_2eproto_impl();
  friend void  protobuf_AddDesc_google_2fprotobuf_2futil_2finternal_2ftestdata_2fmaps_2eproto_impl();
  friend void protobuf_AssignDesc_google_2fprotobuf_2futil_2finternal_2ftestdata_2fmaps_2eproto();
  friend void protobuf_ShutdownFile_google_2fprotobuf_2futil_2finternal_2ftestdata_2fmaps_2eproto();

  void InitAsDefaultInstance();
};
extern ::google::protobuf::internal::ExplicitlyConstructed<MapIn> MapIn_default_instance_;

// -------------------------------------------------------------------

class MapOut : public ::google::protobuf::Message /* @@protoc_insertion_point(class_definition:google.protobuf.testing.MapOut) */ {
 public:
  MapOut();
  virtual ~MapOut();

  MapOut(const MapOut& from);

  inline MapOut& operator=(const MapOut& from) {
    CopyFrom(from);
    return *this;
  }

  static const ::google::protobuf::Descriptor* descriptor();
  static const MapOut& default_instance();

  static const MapOut* internal_default_instance();

  void Swap(MapOut* other);

  // implements Message ----------------------------------------------

  inline MapOut* New() const { return New(NULL); }

  MapOut* New(::google::protobuf::Arena* arena) const;
  void CopyFrom(const ::google::protobuf::Message& from);
  void MergeFrom(const ::google::protobuf::Message& from);
  void CopyFrom(const MapOut& from);
  void MergeFrom(const MapOut& from);
  void Clear();
  bool IsInitialized() const;

  size_t ByteSizeLong() const;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input);
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const;
  ::google::protobuf::uint8* InternalSerializeWithCachedSizesToArray(
      bool deterministic, ::google::protobuf::uint8* output) const;
  ::google::protobuf::uint8* SerializeWithCachedSizesToArray(::google::protobuf::uint8* output) const {
    return InternalSerializeWithCachedSizesToArray(false, output);
  }
  int GetCachedSize() const { return _cached_size_; }
  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const;
  void InternalSwap(MapOut* other);
  void UnsafeMergeFrom(const MapOut& from);
  private:
  inline ::google::protobuf::Arena* GetArenaNoVirtual() const {
    return _internal_metadata_.arena();
  }
  inline void* MaybeArenaPtr() const {
    return _internal_metadata_.raw_arena_ptr();
  }
  public:

  ::google::protobuf::Metadata GetMetadata() const;

  // nested types ----------------------------------------------------


  // accessors -------------------------------------------------------

  // map<string, .google.protobuf.testing.MapM> map1 = 1;
  int map1_size() const;
  void clear_map1();
  static const int kMap1FieldNumber = 1;
  const ::google::protobuf::Map< ::std::string, ::google::protobuf::testing::MapM >&
      map1() const;
  ::google::protobuf::Map< ::std::string, ::google::protobuf::testing::MapM >*
      mutable_map1();

  // map<string, .google.protobuf.testing.MapOut> map2 = 2;
  int map2_size() const;
  void clear_map2();
  static const int kMap2FieldNumber = 2;
  const ::google::protobuf::Map< ::std::string, ::google::protobuf::testing::MapOut >&
      map2() const;
  ::google::protobuf::Map< ::std::string, ::google::protobuf::testing::MapOut >*
      mutable_map2();

  // map<int32, string> map3 = 3;
  int map3_size() const;
  void clear_map3();
  static const int kMap3FieldNumber = 3;
  const ::google::protobuf::Map< ::google::protobuf::int32, ::std::string >&
      map3() const;
  ::google::protobuf::Map< ::google::protobuf::int32, ::std::string >*
      mutable_map3();

  // map<bool, string> map4 = 5;
  int map4_size() const;
  void clear_map4();
  static const int kMap4FieldNumber = 5;
  const ::google::protobuf::Map< bool, ::std::string >&
      map4() const;
  ::google::protobuf::Map< bool, ::std::string >*
      mutable_map4();

  // optional string bar = 4;
  void clear_bar();
  static const int kBarFieldNumber = 4;
  const ::std::string& bar() const;
  void set_bar(const ::std::string& value);
  void set_bar(const char* value);
  void set_bar(const char* value, size_t size);
  ::std::string* mutable_bar();
  ::std::string* release_bar();
  void set_allocated_bar(::std::string* bar);

  // @@protoc_insertion_point(class_scope:google.protobuf.testing.MapOut)
 private:

  ::google::protobuf::internal::InternalMetadataWithArena _internal_metadata_;
  typedef ::google::protobuf::internal::MapEntryLite<
      ::std::string, ::google::protobuf::testing::MapM,
      ::google::protobuf::internal::WireFormatLite::TYPE_STRING,
      ::google::protobuf::internal::WireFormatLite::TYPE_MESSAGE,
      0 >
      MapOut_Map1Entry;
  ::google::protobuf::internal::MapField<
      ::std::string, ::google::protobuf::testing::MapM,
      ::google::protobuf::internal::WireFormatLite::TYPE_STRING,
      ::google::protobuf::internal::WireFormatLite::TYPE_MESSAGE,
      0 > map1_;
  typedef ::google::protobuf::internal::MapEntryLite<
      ::std::string, ::google::protobuf::testing::MapOut,
      ::google::protobuf::internal::WireFormatLite::TYPE_STRING,
      ::google::protobuf::internal::WireFormatLite::TYPE_MESSAGE,
      0 >
      MapOut_Map2Entry;
  ::google::protobuf::internal::MapField<
      ::std::string, ::google::protobuf::testing::MapOut,
      ::google::protobuf::internal::WireFormatLite::TYPE_STRING,
      ::google::protobuf::internal::WireFormatLite::TYPE_MESSAGE,
      0 > map2_;
  typedef ::google::protobuf::internal::MapEntryLite<
      ::google::protobuf::int32, ::std::string,
      ::google::protobuf::internal::WireFormatLite::TYPE_INT32,
      ::google::protobuf::internal::WireFormatLite::TYPE_STRING,
      0 >
      MapOut_Map3Entry;
  ::google::protobuf::internal::MapField<
      ::google::protobuf::int32, ::std::string,
      ::google::protobuf::internal::WireFormatLite::TYPE_INT32,
      ::google::protobuf::internal::WireFormatLite::TYPE_STRING,
      0 > map3_;
  typedef ::google::protobuf::internal::MapEntryLite<
      bool, ::std::string,
      ::google::protobuf::internal::WireFormatLite::TYPE_BOOL,
      ::google::protobuf::internal::WireFormatLite::TYPE_STRING,
      0 >
      MapOut_Map4Entry;
  ::google::protobuf::internal::MapField<
      bool, ::std::string,
      ::google::protobuf::internal::WireFormatLite::TYPE_BOOL,
      ::google::protobuf::internal::WireFormatLite::TYPE_STRING,
      0 > map4_;
  ::google::protobuf::internal::ArenaStringPtr bar_;
  mutable int _cached_size_;
  friend void  protobuf_InitDefaults_google_2fprotobuf_2futil_2finternal_2ftestdata_2fmaps_2eproto_impl();
  friend void  protobuf_AddDesc_google_2fprotobuf_2futil_2finternal_2ftestdata_2fmaps_2eproto_impl();
  friend void protobuf_AssignDesc_google_2fprotobuf_2futil_2finternal_2ftestdata_2fmaps_2eproto();
  friend void protobuf_ShutdownFile_google_2fprotobuf_2futil_2finternal_2ftestdata_2fmaps_2eproto();

  void InitAsDefaultInstance();
};
extern ::google::protobuf::internal::ExplicitlyConstructed<MapOut> MapOut_default_instance_;

// -------------------------------------------------------------------

class MapOutWireFormat_Map1Entry : public ::google::protobuf::Message /* @@protoc_insertion_point(class_definition:google.protobuf.testing.MapOutWireFormat.Map1Entry) */ {
 public:
  MapOutWireFormat_Map1Entry();
  virtual ~MapOutWireFormat_Map1Entry();

  MapOutWireFormat_Map1Entry(const MapOutWireFormat_Map1Entry& from);

  inline MapOutWireFormat_Map1Entry& operator=(const MapOutWireFormat_Map1Entry& from) {
    CopyFrom(from);
    return *this;
  }

  static const ::google::protobuf::Descriptor* descriptor();
  static const MapOutWireFormat_Map1Entry& default_instance();

  static const MapOutWireFormat_Map1Entry* internal_default_instance();

  void Swap(MapOutWireFormat_Map1Entry* other);

  // implements Message ----------------------------------------------

  inline MapOutWireFormat_Map1Entry* New() const { return New(NULL); }

  MapOutWireFormat_Map1Entry* New(::google::protobuf::Arena* arena) const;
  void CopyFrom(const ::google::protobuf::Message& from);
  void MergeFrom(const ::google::protobuf::Message& from);
  void CopyFrom(const MapOutWireFormat_Map1Entry& from);
  void MergeFrom(const MapOutWireFormat_Map1Entry& from);
  void Clear();
  bool IsInitialized() const;

  size_t ByteSizeLong() const;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input);
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const;
  ::google::protobuf::uint8* InternalSerializeWithCachedSizesToArray(
      bool deterministic, ::google::protobuf::uint8* output) const;
  ::google::protobuf::uint8* SerializeWithCachedSizesToArray(::google::protobuf::uint8* output) const {
    return InternalSerializeWithCachedSizesToArray(false, output);
  }
  int GetCachedSize() const { return _cached_size_; }
  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const;
  void InternalSwap(MapOutWireFormat_Map1Entry* other);
  void UnsafeMergeFrom(const MapOutWireFormat_Map1Entry& from);
  private:
  inline ::google::protobuf::Arena* GetArenaNoVirtual() const {
    return _internal_metadata_.arena();
  }
  inline void* MaybeArenaPtr() const {
    return _internal_metadata_.raw_arena_ptr();
  }
  public:

  ::google::protobuf::Metadata GetMetadata() const;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // optional string key = 1;
  void clear_key();
  static const int kKeyFieldNumber = 1;
  const ::std::string& key() const;
  void set_key(const ::std::string& value);
  void set_key(const char* value);
  void set_key(const char* value, size_t size);
  ::std::string* mutable_key();
  ::std::string* release_key();
  void set_allocated_key(::std::string* key);

  // optional .google.protobuf.testing.MapM value = 2;
  bool has_value() const;
  void clear_value();
  static const int kValueFieldNumber = 2;
  const ::google::protobuf::testing::MapM& value() const;
  ::google::protobuf::testing::MapM* mutable_value();
  ::google::protobuf::testing::MapM* release_value();
  void set_allocated_value(::google::protobuf::testing::MapM* value);

  // @@protoc_insertion_point(class_scope:google.protobuf.testing.MapOutWireFormat.Map1Entry)
 private:

  ::google::protobuf::internal::InternalMetadataWithArena _internal_metadata_;
  ::google::protobuf::internal::ArenaStringPtr key_;
  ::google::protobuf::testing::MapM* value_;
  mutable int _cached_size_;
  friend void  protobuf_InitDefaults_google_2fprotobuf_2futil_2finternal_2ftestdata_2fmaps_2eproto_impl();
  friend void  protobuf_AddDesc_google_2fprotobuf_2futil_2finternal_2ftestdata_2fmaps_2eproto_impl();
  friend void protobuf_AssignDesc_google_2fprotobuf_2futil_2finternal_2ftestdata_2fmaps_2eproto();
  friend void protobuf_ShutdownFile_google_2fprotobuf_2futil_2finternal_2ftestdata_2fmaps_2eproto();

  void InitAsDefaultInstance();
};
extern ::google::protobuf::internal::ExplicitlyConstructed<MapOutWireFormat_Map1Entry> MapOutWireFormat_Map1Entry_default_instance_;

// -------------------------------------------------------------------

class MapOutWireFormat_Map2Entry : public ::google::protobuf::Message /* @@protoc_insertion_point(class_definition:google.protobuf.testing.MapOutWireFormat.Map2Entry) */ {
 public:
  MapOutWireFormat_Map2Entry();
  virtual ~MapOutWireFormat_Map2Entry();

  MapOutWireFormat_Map2Entry(const MapOutWireFormat_Map2Entry& from);

  inline MapOutWireFormat_Map2Entry& operator=(const MapOutWireFormat_Map2Entry& from) {
    CopyFrom(from);
    return *this;
  }

  static const ::google::protobuf::Descriptor* descriptor();
  static const MapOutWireFormat_Map2Entry& default_instance();

  static const MapOutWireFormat_Map2Entry* internal_default_instance();

  void Swap(MapOutWireFormat_Map2Entry* other);

  // implements Message ----------------------------------------------

  inline MapOutWireFormat_Map2Entry* New() const { return New(NULL); }

  MapOutWireFormat_Map2Entry* New(::google::protobuf::Arena* arena) const;
  void CopyFrom(const ::google::protobuf::Message& from);
  void MergeFrom(const ::google::protobuf::Message& from);
  void CopyFrom(const MapOutWireFormat_Map2Entry& from);
  void MergeFrom(const MapOutWireFormat_Map2Entry& from);
  void Clear();
  bool IsInitialized() const;

  size_t ByteSizeLong() const;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input);
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const;
  ::google::protobuf::uint8* InternalSerializeWithCachedSizesToArray(
      bool deterministic, ::google::protobuf::uint8* output) const;
  ::google::protobuf::uint8* SerializeWithCachedSizesToArray(::google::protobuf::uint8* output) const {
    return InternalSerializeWithCachedSizesToArray(false, output);
  }
  int GetCachedSize() const { return _cached_size_; }
  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const;
  void InternalSwap(MapOutWireFormat_Map2Entry* other);
  void UnsafeMergeFrom(const MapOutWireFormat_Map2Entry& from);
  private:
  inline ::google::protobuf::Arena* GetArenaNoVirtual() const {
    return _internal_metadata_.arena();
  }
  inline void* MaybeArenaPtr() const {
    return _internal_metadata_.raw_arena_ptr();
  }
  public:

  ::google::protobuf::Metadata GetMetadata() const;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // optional string key = 1;
  void clear_key();
  static const int kKeyFieldNumber = 1;
  const ::std::string& key() const;
  void set_key(const ::std::string& value);
  void set_key(const char* value);
  void set_key(const char* value, size_t size);
  ::std::string* mutable_key();
  ::std::string* release_key();
  void set_allocated_key(::std::string* key);

  // optional .google.protobuf.testing.MapOut value = 2;
  bool has_value() const;
  void clear_value();
  static const int kValueFieldNumber = 2;
  const ::google::protobuf::testing::MapOut& value() const;
  ::google::protobuf::testing::MapOut* mutable_value();
  ::google::protobuf::testing::MapOut* release_value();
  void set_allocated_value(::google::protobuf::testing::MapOut* value);

  // @@protoc_insertion_point(class_scope:google.protobuf.testing.MapOutWireFormat.Map2Entry)
 private:

  ::google::protobuf::internal::InternalMetadataWithArena _internal_metadata_;
  ::google::protobuf::internal::ArenaStringPtr key_;
  ::google::protobuf::testing::MapOut* value_;
  mutable int _cached_size_;
  friend void  protobuf_InitDefaults_google_2fprotobuf_2futil_2finternal_2ftestdata_2fmaps_2eproto_impl();
  friend void  protobuf_AddDesc_google_2fprotobuf_2futil_2finternal_2ftestdata_2fmaps_2eproto_impl();
  friend void protobuf_AssignDesc_google_2fprotobuf_2futil_2finternal_2ftestdata_2fmaps_2eproto();
  friend void protobuf_ShutdownFile_google_2fprotobuf_2futil_2finternal_2ftestdata_2fmaps_2eproto();

  void InitAsDefaultInstance();
};
extern ::google::protobuf::internal::ExplicitlyConstructed<MapOutWireFormat_Map2Entry> MapOutWireFormat_Map2Entry_default_instance_;

// -------------------------------------------------------------------

class MapOutWireFormat_Map3Entry : public ::google::protobuf::Message /* @@protoc_insertion_point(class_definition:google.protobuf.testing.MapOutWireFormat.Map3Entry) */ {
 public:
  MapOutWireFormat_Map3Entry();
  virtual ~MapOutWireFormat_Map3Entry();

  MapOutWireFormat_Map3Entry(const MapOutWireFormat_Map3Entry& from);

  inline MapOutWireFormat_Map3Entry& operator=(const MapOutWireFormat_Map3Entry& from) {
    CopyFrom(from);
    return *this;
  }

  static const ::google::protobuf::Descriptor* descriptor();
  static const MapOutWireFormat_Map3Entry& default_instance();

  static const MapOutWireFormat_Map3Entry* internal_default_instance();

  void Swap(MapOutWireFormat_Map3Entry* other);

  // implements Message ----------------------------------------------

  inline MapOutWireFormat_Map3Entry* New() const { return New(NULL); }

  MapOutWireFormat_Map3Entry* New(::google::protobuf::Arena* arena) const;
  void CopyFrom(const ::google::protobuf::Message& from);
  void MergeFrom(const ::google::protobuf::Message& from);
  void CopyFrom(const MapOutWireFormat_Map3Entry& from);
  void MergeFrom(const MapOutWireFormat_Map3Entry& from);
  void Clear();
  bool IsInitialized() const;

  size_t ByteSizeLong() const;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input);
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const;
  ::google::protobuf::uint8* InternalSerializeWithCachedSizesToArray(
      bool deterministic, ::google::protobuf::uint8* output) const;
  ::google::protobuf::uint8* SerializeWithCachedSizesToArray(::google::protobuf::uint8* output) const {
    return InternalSerializeWithCachedSizesToArray(false, output);
  }
  int GetCachedSize() const { return _cached_size_; }
  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const;
  void InternalSwap(MapOutWireFormat_Map3Entry* other);
  void UnsafeMergeFrom(const MapOutWireFormat_Map3Entry& from);
  private:
  inline ::google::protobuf::Arena* GetArenaNoVirtual() const {
    return _internal_metadata_.arena();
  }
  inline void* MaybeArenaPtr() const {
    return _internal_metadata_.raw_arena_ptr();
  }
  public:

  ::google::protobuf::Metadata GetMetadata() const;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // optional int32 key = 1;
  void clear_key();
  static const int kKeyFieldNumber = 1;
  ::google::protobuf::int32 key() const;
  void set_key(::google::protobuf::int32 value);

  // optional string value = 2;
  void clear_value();
  static const int kValueFieldNumber = 2;
  const ::std::string& value() const;
  void set_value(const ::std::string& value);
  void set_value(const char* value);
  void set_value(const char* value, size_t size);
  ::std::string* mutable_value();
  ::std::string* release_value();
  void set_allocated_value(::std::string* value);

  // @@protoc_insertion_point(class_scope:google.protobuf.testing.MapOutWireFormat.Map3Entry)
 private:

  ::google::protobuf::internal::InternalMetadataWithArena _internal_metadata_;
  ::google::protobuf::internal::ArenaStringPtr value_;
  ::google::protobuf::int32 key_;
  mutable int _cached_size_;
  friend void  protobuf_InitDefaults_google_2fprotobuf_2futil_2finternal_2ftestdata_2fmaps_2eproto_impl();
  friend void  protobuf_AddDesc_google_2fprotobuf_2futil_2finternal_2ftestdata_2fmaps_2eproto_impl();
  friend void protobuf_AssignDesc_google_2fprotobuf_2futil_2finternal_2ftestdata_2fmaps_2eproto();
  friend void protobuf_ShutdownFile_google_2fprotobuf_2futil_2finternal_2ftestdata_2fmaps_2eproto();

  void InitAsDefaultInstance();
};
extern ::google::protobuf::internal::ExplicitlyConstructed<MapOutWireFormat_Map3Entry> MapOutWireFormat_Map3Entry_default_instance_;

// -------------------------------------------------------------------

class MapOutWireFormat_Map4Entry : public ::google::protobuf::Message /* @@protoc_insertion_point(class_definition:google.protobuf.testing.MapOutWireFormat.Map4Entry) */ {
 public:
  MapOutWireFormat_Map4Entry();
  virtual ~MapOutWireFormat_Map4Entry();

  MapOutWireFormat_Map4Entry(const MapOutWireFormat_Map4Entry& from);

  inline MapOutWireFormat_Map4Entry& operator=(const MapOutWireFormat_Map4Entry& from) {
    CopyFrom(from);
    return *this;
  }

  static const ::google::protobuf::Descriptor* descriptor();
  static const MapOutWireFormat_Map4Entry& default_instance();

  static const MapOutWireFormat_Map4Entry* internal_default_instance();

  void Swap(MapOutWireFormat_Map4Entry* other);

  // implements Message ----------------------------------------------

  inline MapOutWireFormat_Map4Entry* New() const { return New(NULL); }

  MapOutWireFormat_Map4Entry* New(::google::protobuf::Arena* arena) const;
  void CopyFrom(const ::google::protobuf::Message& from);
  void MergeFrom(const ::google::protobuf::Message& from);
  void CopyFrom(const MapOutWireFormat_Map4Entry& from);
  void MergeFrom(const MapOutWireFormat_Map4Entry& from);
  void Clear();
  bool IsInitialized() const;

  size_t ByteSizeLong() const;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input);
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const;
  ::google::protobuf::uint8* InternalSerializeWithCachedSizesToArray(
      bool deterministic, ::google::protobuf::uint8* output) const;
  ::google::protobuf::uint8* SerializeWithCachedSizesToArray(::google::protobuf::uint8* output) const {
    return InternalSerializeWithCachedSizesToArray(false, output);
  }
  int GetCachedSize() const { return _cached_size_; }
  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const;
  void InternalSwap(MapOutWireFormat_Map4Entry* other);
  void UnsafeMergeFrom(const MapOutWireFormat_Map4Entry& from);
  private:
  inline ::google::protobuf::Arena* GetArenaNoVirtual() const {
    return _internal_metadata_.arena();
  }
  inline void* MaybeArenaPtr() const {
    return _internal_metadata_.raw_arena_ptr();
  }
  public:

  ::google::protobuf::Metadata GetMetadata() const;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // optional bool key = 1;
  void clear_key();
  static const int kKeyFieldNumber = 1;
  bool key() const;
  void set_key(bool value);

  // optional string value = 2;
  void clear_value();
  static const int kValueFieldNumber = 2;
  const ::std::string& value() const;
  void set_value(const ::std::string& value);
  void set_value(const char* value);
  void set_value(const char* value, size_t size);
  ::std::string* mutable_value();
  ::std::string* release_value();
  void set_allocated_value(::std::string* value);

  // @@protoc_insertion_point(class_scope:google.protobuf.testing.MapOutWireFormat.Map4Entry)
 private:

  ::google::protobuf::internal::InternalMetadataWithArena _internal_metadata_;
  ::google::protobuf::internal::ArenaStringPtr value_;
  bool key_;
  mutable int _cached_size_;
  friend void  protobuf_InitDefaults_google_2fprotobuf_2futil_2finternal_2ftestdata_2fmaps_2eproto_impl();
  friend void  protobuf_AddDesc_google_2fprotobuf_2futil_2finternal_2ftestdata_2fmaps_2eproto_impl();
  friend void protobuf_AssignDesc_google_2fprotobuf_2futil_2finternal_2ftestdata_2fmaps_2eproto();
  friend void protobuf_ShutdownFile_google_2fprotobuf_2futil_2finternal_2ftestdata_2fmaps_2eproto();

  void InitAsDefaultInstance();
};
extern ::google::protobuf::internal::ExplicitlyConstructed<MapOutWireFormat_Map4Entry> MapOutWireFormat_Map4Entry_default_instance_;

// -------------------------------------------------------------------

class MapOutWireFormat : public ::google::protobuf::Message /* @@protoc_insertion_point(class_definition:google.protobuf.testing.MapOutWireFormat) */ {
 public:
  MapOutWireFormat();
  virtual ~MapOutWireFormat();

  MapOutWireFormat(const MapOutWireFormat& from);

  inline MapOutWireFormat& operator=(const MapOutWireFormat& from) {
    CopyFrom(from);
    return *this;
  }

  static const ::google::protobuf::Descriptor* descriptor();
  static const MapOutWireFormat& default_instance();

  static const MapOutWireFormat* internal_default_instance();

  void Swap(MapOutWireFormat* other);

  // implements Message ----------------------------------------------

  inline MapOutWireFormat* New() const { return New(NULL); }

  MapOutWireFormat* New(::google::protobuf::Arena* arena) const;
  void CopyFrom(const ::google::protobuf::Message& from);
  void MergeFrom(const ::google::protobuf::Message& from);
  void CopyFrom(const MapOutWireFormat& from);
  void MergeFrom(const MapOutWireFormat& from);
  void Clear();
  bool IsInitialized() const;

  size_t ByteSizeLong() const;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input);
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const;
  ::google::protobuf::uint8* InternalSerializeWithCachedSizesToArray(
      bool deterministic, ::google::protobuf::uint8* output) const;
  ::google::protobuf::uint8* SerializeWithCachedSizesToArray(::google::protobuf::uint8* output) const {
    return InternalSerializeWithCachedSizesToArray(false, output);
  }
  int GetCachedSize() const { return _cached_size_; }
  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const;
  void InternalSwap(MapOutWireFormat* other);
  void UnsafeMergeFrom(const MapOutWireFormat& from);
  private:
  inline ::google::protobuf::Arena* GetArenaNoVirtual() const {
    return _internal_metadata_.arena();
  }
  inline void* MaybeArenaPtr() const {
    return _internal_metadata_.raw_arena_ptr();
  }
  public:

  ::google::protobuf::Metadata GetMetadata() const;

  // nested types ----------------------------------------------------

  typedef MapOutWireFormat_Map1Entry Map1Entry;
  typedef MapOutWireFormat_Map2Entry Map2Entry;
  typedef MapOutWireFormat_Map3Entry Map3Entry;
  typedef MapOutWireFormat_Map4Entry Map4Entry;

  // accessors -------------------------------------------------------

  // repeated .google.protobuf.testing.MapOutWireFormat.Map1Entry map1 = 1;
  int map1_size() const;
  void clear_map1();
  static const int kMap1FieldNumber = 1;
  const ::google::protobuf::testing::MapOutWireFormat_Map1Entry& map1(int index) const;
  ::google::protobuf::testing::MapOutWireFormat_Map1Entry* mutable_map1(int index);
  ::google::protobuf::testing::MapOutWireFormat_Map1Entry* add_map1();
  ::google::protobuf::RepeatedPtrField< ::google::protobuf::testing::MapOutWireFormat_Map1Entry >*
      mutable_map1();
  const ::google::protobuf::RepeatedPtrField< ::google::protobuf::testing::MapOutWireFormat_Map1Entry >&
      map1() const;

  // repeated .google.protobuf.testing.MapOutWireFormat.Map2Entry map2 = 2;
  int map2_size() const;
  void clear_map2();
  static const int kMap2FieldNumber = 2;
  const ::google::protobuf::testing::MapOutWireFormat_Map2Entry& map2(int index) const;
  ::google::protobuf::testing::MapOutWireFormat_Map2Entry* mutable_map2(int index);
  ::google::protobuf::testing::MapOutWireFormat_Map2Entry* add_map2();
  ::google::protobuf::RepeatedPtrField< ::google::protobuf::testing::MapOutWireFormat_Map2Entry >*
      mutable_map2();
  const ::google::protobuf::RepeatedPtrField< ::google::protobuf::testing::MapOutWireFormat_Map2Entry >&
      map2() const;

  // repeated .google.protobuf.testing.MapOutWireFormat.Map3Entry map3 = 3;
  int map3_size() const;
  void clear_map3();
  static const int kMap3FieldNumber = 3;
  const ::google::protobuf::testing::MapOutWireFormat_Map3Entry& map3(int index) const;
  ::google::protobuf::testing::MapOutWireFormat_Map3Entry* mutable_map3(int index);
  ::google::protobuf::testing::MapOutWireFormat_Map3Entry* add_map3();
  ::google::protobuf::RepeatedPtrField< ::google::protobuf::testing::MapOutWireFormat_Map3Entry >*
      mutable_map3();
  const ::google::protobuf::RepeatedPtrField< ::google::protobuf::testing::MapOutWireFormat_Map3Entry >&
      map3() const;

  // repeated .google.protobuf.testing.MapOutWireFormat.Map4Entry map4 = 5;
  int map4_size() const;
  void clear_map4();
  static const int kMap4FieldNumber = 5;
  const ::google::protobuf::testing::MapOutWireFormat_Map4Entry& map4(int index) const;
  ::google::protobuf::testing::MapOutWireFormat_Map4Entry* mutable_map4(int index);
  ::google::protobuf::testing::MapOutWireFormat_Map4Entry* add_map4();
  ::google::protobuf::RepeatedPtrField< ::google::protobuf::testing::MapOutWireFormat_Map4Entry >*
      mutable_map4();
  const ::google::protobuf::RepeatedPtrField< ::google::protobuf::testing::MapOutWireFormat_Map4Entry >&
      map4() const;

  // optional string bar = 4;
  void clear_bar();
  static const int kBarFieldNumber = 4;
  const ::std::string& bar() const;
  void set_bar(const ::std::string& value);
  void set_bar(const char* value);
  void set_bar(const char* value, size_t size);
  ::std::string* mutable_bar();
  ::std::string* release_bar();
  void set_allocated_bar(::std::string* bar);

  // @@protoc_insertion_point(class_scope:google.protobuf.testing.MapOutWireFormat)
 private:

  ::google::protobuf::internal::InternalMetadataWithArena _internal_metadata_;
  ::google::protobuf::RepeatedPtrField< ::google::protobuf::testing::MapOutWireFormat_Map1Entry > map1_;
  ::google::protobuf::RepeatedPtrField< ::google::protobuf::testing::MapOutWireFormat_Map2Entry > map2_;
  ::google::protobuf::RepeatedPtrField< ::google::protobuf::testing::MapOutWireFormat_Map3Entry > map3_;
  ::google::protobuf::RepeatedPtrField< ::google::protobuf::testing::MapOutWireFormat_Map4Entry > map4_;
  ::google::protobuf::internal::ArenaStringPtr bar_;
  mutable int _cached_size_;
  friend void  protobuf_InitDefaults_google_2fprotobuf_2futil_2finternal_2ftestdata_2fmaps_2eproto_impl();
  friend void  protobuf_AddDesc_google_2fprotobuf_2futil_2finternal_2ftestdata_2fmaps_2eproto_impl();
  friend void protobuf_AssignDesc_google_2fprotobuf_2futil_2finternal_2ftestdata_2fmaps_2eproto();
  friend void protobuf_ShutdownFile_google_2fprotobuf_2futil_2finternal_2ftestdata_2fmaps_2eproto();

  void InitAsDefaultInstance();
};
extern ::google::protobuf::internal::ExplicitlyConstructed<MapOutWireFormat> MapOutWireFormat_default_instance_;

// -------------------------------------------------------------------

class MapM : public ::google::protobuf::Message /* @@protoc_insertion_point(class_definition:google.protobuf.testing.MapM) */ {
 public:
  MapM();
  virtual ~MapM();

  MapM(const MapM& from);

  inline MapM& operator=(const MapM& from) {
    CopyFrom(from);
    return *this;
  }

  static const ::google::protobuf::Descriptor* descriptor();
  static const MapM& default_instance();

  static const MapM* internal_default_instance();

  void Swap(MapM* other);

  // implements Message ----------------------------------------------

  inline MapM* New() const { return New(NULL); }

  MapM* New(::google::protobuf::Arena* arena) const;
  void CopyFrom(const ::google::protobuf::Message& from);
  void MergeFrom(const ::google::protobuf::Message& from);
  void CopyFrom(const MapM& from);
  void MergeFrom(const MapM& from);
  void Clear();
  bool IsInitialized() const;

  size_t ByteSizeLong() const;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input);
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const;
  ::google::protobuf::uint8* InternalSerializeWithCachedSizesToArray(
      bool deterministic, ::google::protobuf::uint8* output) const;
  ::google::protobuf::uint8* SerializeWithCachedSizesToArray(::google::protobuf::uint8* output) const {
    return InternalSerializeWithCachedSizesToArray(false, output);
  }
  int GetCachedSize() const { return _cached_size_; }
  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const;
  void InternalSwap(MapM* other);
  void UnsafeMergeFrom(const MapM& from);
  private:
  inline ::google::protobuf::Arena* GetArenaNoVirtual() const {
    return _internal_metadata_.arena();
  }
  inline void* MaybeArenaPtr() const {
    return _internal_metadata_.raw_arena_ptr();
  }
  public:

  ::google::protobuf::Metadata GetMetadata() const;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // optional string foo = 1;
  void clear_foo();
  static const int kFooFieldNumber = 1;
  const ::std::string& foo() const;
  void set_foo(const ::std::string& value);
  void set_foo(const char* value);
  void set_foo(const char* value, size_t size);
  ::std::string* mutable_foo();
  ::std::string* release_foo();
  void set_allocated_foo(::std::string* foo);

  // @@protoc_insertion_point(class_scope:google.protobuf.testing.MapM)
 private:

  ::google::protobuf::internal::InternalMetadataWithArena _internal_metadata_;
  ::google::protobuf::internal::ArenaStringPtr foo_;
  mutable int _cached_size_;
  friend void  protobuf_InitDefaults_google_2fprotobuf_2futil_2finternal_2ftestdata_2fmaps_2eproto_impl();
  friend void  protobuf_AddDesc_google_2fprotobuf_2futil_2finternal_2ftestdata_2fmaps_2eproto_impl();
  friend void protobuf_AssignDesc_google_2fprotobuf_2futil_2finternal_2ftestdata_2fmaps_2eproto();
  friend void protobuf_ShutdownFile_google_2fprotobuf_2futil_2finternal_2ftestdata_2fmaps_2eproto();

  void InitAsDefaultInstance();
};
extern ::google::protobuf::internal::ExplicitlyConstructed<MapM> MapM_default_instance_;

// ===================================================================


// ===================================================================

#if !PROTOBUF_INLINE_NOT_IN_HEADERS
// MapsTestCases

// optional .google.protobuf.testing.EmptyMap empty_map = 1;
inline bool MapsTestCases::has_empty_map() const {
  return this != internal_default_instance() && empty_map_ != NULL;
}
inline void MapsTestCases::clear_empty_map() {
  if (GetArenaNoVirtual() == NULL && empty_map_ != NULL) delete empty_map_;
  empty_map_ = NULL;
}
inline const ::google::protobuf::testing::EmptyMap& MapsTestCases::empty_map() const {
  // @@protoc_insertion_point(field_get:google.protobuf.testing.MapsTestCases.empty_map)
  return empty_map_ != NULL ? *empty_map_
                         : *::google::protobuf::testing::EmptyMap::internal_default_instance();
}
inline ::google::protobuf::testing::EmptyMap* MapsTestCases::mutable_empty_map() {
  
  if (empty_map_ == NULL) {
    empty_map_ = new ::google::protobuf::testing::EmptyMap;
  }
  // @@protoc_insertion_point(field_mutable:google.protobuf.testing.MapsTestCases.empty_map)
  return empty_map_;
}
inline ::google::protobuf::testing::EmptyMap* MapsTestCases::release_empty_map() {
  // @@protoc_insertion_point(field_release:google.protobuf.testing.MapsTestCases.empty_map)
  
  ::google::protobuf::testing::EmptyMap* temp = empty_map_;
  empty_map_ = NULL;
  return temp;
}
inline void MapsTestCases::set_allocated_empty_map(::google::protobuf::testing::EmptyMap* empty_map) {
  delete empty_map_;
  empty_map_ = empty_map;
  if (empty_map) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_set_allocated:google.protobuf.testing.MapsTestCases.empty_map)
}

// optional .google.protobuf.testing.StringtoInt string_to_int = 2;
inline bool MapsTestCases::has_string_to_int() const {
  return this != internal_default_instance() && string_to_int_ != NULL;
}
inline void MapsTestCases::clear_string_to_int() {
  if (GetArenaNoVirtual() == NULL && string_to_int_ != NULL) delete string_to_int_;
  string_to_int_ = NULL;
}
inline const ::google::protobuf::testing::StringtoInt& MapsTestCases::string_to_int() const {
  // @@protoc_insertion_point(field_get:google.protobuf.testing.MapsTestCases.string_to_int)
  return string_to_int_ != NULL ? *string_to_int_
                         : *::google::protobuf::testing::StringtoInt::internal_default_instance();
}
inline ::google::protobuf::testing::StringtoInt* MapsTestCases::mutable_string_to_int() {
  
  if (string_to_int_ == NULL) {
    string_to_int_ = new ::google::protobuf::testing::StringtoInt;
  }
  // @@protoc_insertion_point(field_mutable:google.protobuf.testing.MapsTestCases.string_to_int)
  return string_to_int_;
}
inline ::google::protobuf::testing::StringtoInt* MapsTestCases::release_string_to_int() {
  // @@protoc_insertion_point(field_release:google.protobuf.testing.MapsTestCases.string_to_int)
  
  ::google::protobuf::testing::StringtoInt* temp = string_to_int_;
  string_to_int_ = NULL;
  return temp;
}
inline void MapsTestCases::set_allocated_string_to_int(::google::protobuf::testing::StringtoInt* string_to_int) {
  delete string_to_int_;
  string_to_int_ = string_to_int;
  if (string_to_int) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_set_allocated:google.protobuf.testing.MapsTestCases.string_to_int)
}

// optional .google.protobuf.testing.IntToString int_to_string = 3;
inline bool MapsTestCases::has_int_to_string() const {
  return this != internal_default_instance() && int_to_string_ != NULL;
}
inline void MapsTestCases::clear_int_to_string() {
  if (GetArenaNoVirtual() == NULL && int_to_string_ != NULL) delete int_to_string_;
  int_to_string_ = NULL;
}
inline const ::google::protobuf::testing::IntToString& MapsTestCases::int_to_string() const {
  // @@protoc_insertion_point(field_get:google.protobuf.testing.MapsTestCases.int_to_string)
  return int_to_string_ != NULL ? *int_to_string_
                         : *::google::protobuf::testing::IntToString::internal_default_instance();
}
inline ::google::protobuf::testing::IntToString* MapsTestCases::mutable_int_to_string() {
  
  if (int_to_string_ == NULL) {
    int_to_string_ = new ::google::protobuf::testing::IntToString;
  }
  // @@protoc_insertion_point(field_mutable:google.protobuf.testing.MapsTestCases.int_to_string)
  return int_to_string_;
}
inline ::google::protobuf::testing::IntToString* MapsTestCases::release_int_to_string() {
  // @@protoc_insertion_point(field_release:google.protobuf.testing.MapsTestCases.int_to_string)
  
  ::google::protobuf::testing::IntToString* temp = int_to_string_;
  int_to_string_ = NULL;
  return temp;
}
inline void MapsTestCases::set_allocated_int_to_string(::google::protobuf::testing::IntToString* int_to_string) {
  delete int_to_string_;
  int_to_string_ = int_to_string;
  if (int_to_string) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_set_allocated:google.protobuf.testing.MapsTestCases.int_to_string)
}

// optional .google.protobuf.testing.Mixed1 mixed1 = 4;
inline bool MapsTestCases::has_mixed1() const {
  return this != internal_default_instance() && mixed1_ != NULL;
}
inline void MapsTestCases::clear_mixed1() {
  if (GetArenaNoVirtual() == NULL && mixed1_ != NULL) delete mixed1_;
  mixed1_ = NULL;
}
inline const ::google::protobuf::testing::Mixed1& MapsTestCases::mixed1() const {
  // @@protoc_insertion_point(field_get:google.protobuf.testing.MapsTestCases.mixed1)
  return mixed1_ != NULL ? *mixed1_
                         : *::google::protobuf::testing::Mixed1::internal_default_instance();
}
inline ::google::protobuf::testing::Mixed1* MapsTestCases::mutable_mixed1() {
  
  if (mixed1_ == NULL) {
    mixed1_ = new ::google::protobuf::testing::Mixed1;
  }
  // @@protoc_insertion_point(field_mutable:google.protobuf.testing.MapsTestCases.mixed1)
  return mixed1_;
}
inline ::google::protobuf::testing::Mixed1* MapsTestCases::release_mixed1() {
  // @@protoc_insertion_point(field_release:google.protobuf.testing.MapsTestCases.mixed1)
  
  ::google::protobuf::testing::Mixed1* temp = mixed1_;
  mixed1_ = NULL;
  return temp;
}
inline void MapsTestCases::set_allocated_mixed1(::google::protobuf::testing::Mixed1* mixed1) {
  delete mixed1_;
  mixed1_ = mixed1;
  if (mixed1) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_set_allocated:google.protobuf.testing.MapsTestCases.mixed1)
}

// optional .google.protobuf.testing.Mixed2 mixed2 = 5;
inline bool MapsTestCases::has_mixed2() const {
  return this != internal_default_instance() && mixed2_ != NULL;
}
inline void MapsTestCases::clear_mixed2() {
  if (GetArenaNoVirtual() == NULL && mixed2_ != NULL) delete mixed2_;
  mixed2_ = NULL;
}
inline const ::google::protobuf::testing::Mixed2& MapsTestCases::mixed2() const {
  // @@protoc_insertion_point(field_get:google.protobuf.testing.MapsTestCases.mixed2)
  return mixed2_ != NULL ? *mixed2_
                         : *::google::protobuf::testing::Mixed2::internal_default_instance();
}
inline ::google::protobuf::testing::Mixed2* MapsTestCases::mutable_mixed2() {
  
  if (mixed2_ == NULL) {
    mixed2_ = new ::google::protobuf::testing::Mixed2;
  }
  // @@protoc_insertion_point(field_mutable:google.protobuf.testing.MapsTestCases.mixed2)
  return mixed2_;
}
inline ::google::protobuf::testing::Mixed2* MapsTestCases::release_mixed2() {
  // @@protoc_insertion_point(field_release:google.protobuf.testing.MapsTestCases.mixed2)
  
  ::google::protobuf::testing::Mixed2* temp = mixed2_;
  mixed2_ = NULL;
  return temp;
}
inline void MapsTestCases::set_allocated_mixed2(::google::protobuf::testing::Mixed2* mixed2) {
  delete mixed2_;
  mixed2_ = mixed2;
  if (mixed2) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_set_allocated:google.protobuf.testing.MapsTestCases.mixed2)
}

// optional .google.protobuf.testing.MapOfObjects map_of_objects = 6;
inline bool MapsTestCases::has_map_of_objects() const {
  return this != internal_default_instance() && map_of_objects_ != NULL;
}
inline void MapsTestCases::clear_map_of_objects() {
  if (GetArenaNoVirtual() == NULL && map_of_objects_ != NULL) delete map_of_objects_;
  map_of_objects_ = NULL;
}
inline const ::google::protobuf::testing::MapOfObjects& MapsTestCases::map_of_objects() const {
  // @@protoc_insertion_point(field_get:google.protobuf.testing.MapsTestCases.map_of_objects)
  return map_of_objects_ != NULL ? *map_of_objects_
                         : *::google::protobuf::testing::MapOfObjects::internal_default_instance();
}
inline ::google::protobuf::testing::MapOfObjects* MapsTestCases::mutable_map_of_objects() {
  
  if (map_of_objects_ == NULL) {
    map_of_objects_ = new ::google::protobuf::testing::MapOfObjects;
  }
  // @@protoc_insertion_point(field_mutable:google.protobuf.testing.MapsTestCases.map_of_objects)
  return map_of_objects_;
}
inline ::google::protobuf::testing::MapOfObjects* MapsTestCases::release_map_of_objects() {
  // @@protoc_insertion_point(field_release:google.protobuf.testing.MapsTestCases.map_of_objects)
  
  ::google::protobuf::testing::MapOfObjects* temp = map_of_objects_;
  map_of_objects_ = NULL;
  return temp;
}
inline void MapsTestCases::set_allocated_map_of_objects(::google::protobuf::testing::MapOfObjects* map_of_objects) {
  delete map_of_objects_;
  map_of_objects_ = map_of_objects;
  if (map_of_objects) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_set_allocated:google.protobuf.testing.MapsTestCases.map_of_objects)
}

// optional .google.protobuf.testing.StringtoInt empty_key_string_to_int1 = 7;
inline bool MapsTestCases::has_empty_key_string_to_int1() const {
  return this != internal_default_instance() && empty_key_string_to_int1_ != NULL;
}
inline void MapsTestCases::clear_empty_key_string_to_int1() {
  if (GetArenaNoVirtual() == NULL && empty_key_string_to_int1_ != NULL) delete empty_key_string_to_int1_;
  empty_key_string_to_int1_ = NULL;
}
inline const ::google::protobuf::testing::StringtoInt& MapsTestCases::empty_key_string_to_int1() const {
  // @@protoc_insertion_point(field_get:google.protobuf.testing.MapsTestCases.empty_key_string_to_int1)
  return empty_key_string_to_int1_ != NULL ? *empty_key_string_to_int1_
                         : *::google::protobuf::testing::StringtoInt::internal_default_instance();
}
inline ::google::protobuf::testing::StringtoInt* MapsTestCases::mutable_empty_key_string_to_int1() {
  
  if (empty_key_string_to_int1_ == NULL) {
    empty_key_string_to_int1_ = new ::google::protobuf::testing::StringtoInt;
  }
  // @@protoc_insertion_point(field_mutable:google.protobuf.testing.MapsTestCases.empty_key_string_to_int1)
  return empty_key_string_to_int1_;
}
inline ::google::protobuf::testing::StringtoInt* MapsTestCases::release_empty_key_string_to_int1() {
  // @@protoc_insertion_point(field_release:google.protobuf.testing.MapsTestCases.empty_key_string_to_int1)
  
  ::google::protobuf::testing::StringtoInt* temp = empty_key_string_to_int1_;
  empty_key_string_to_int1_ = NULL;
  return temp;
}
inline void MapsTestCases::set_allocated_empty_key_string_to_int1(::google::protobuf::testing::StringtoInt* empty_key_string_to_int1) {
  delete empty_key_string_to_int1_;
  empty_key_string_to_int1_ = empty_key_string_to_int1;
  if (empty_key_string_to_int1) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_set_allocated:google.protobuf.testing.MapsTestCases.empty_key_string_to_int1)
}

// optional .google.protobuf.testing.StringtoInt empty_key_string_to_int2 = 8;
inline bool MapsTestCases::has_empty_key_string_to_int2() const {
  return this != internal_default_instance() && empty_key_string_to_int2_ != NULL;
}
inline void MapsTestCases::clear_empty_key_string_to_int2() {
  if (GetArenaNoVirtual() == NULL && empty_key_string_to_int2_ != NULL) delete empty_key_string_to_int2_;
  empty_key_string_to_int2_ = NULL;
}
inline const ::google::protobuf::testing::StringtoInt& MapsTestCases::empty_key_string_to_int2() const {
  // @@protoc_insertion_point(field_get:google.protobuf.testing.MapsTestCases.empty_key_string_to_int2)
  return empty_key_string_to_int2_ != NULL ? *empty_key_string_to_int2_
                         : *::google::protobuf::testing::StringtoInt::internal_default_instance();
}
inline ::google::protobuf::testing::StringtoInt* MapsTestCases::mutable_empty_key_string_to_int2() {
  
  if (empty_key_string_to_int2_ == NULL) {
    empty_key_string_to_int2_ = new ::google::protobuf::testing::StringtoInt;
  }
  // @@protoc_insertion_point(field_mutable:google.protobuf.testing.MapsTestCases.empty_key_string_to_int2)
  return empty_key_string_to_int2_;
}
inline ::google::protobuf::testing::StringtoInt* MapsTestCases::release_empty_key_string_to_int2() {
  // @@protoc_insertion_point(field_release:google.protobuf.testing.MapsTestCases.empty_key_string_to_int2)
  
  ::google::protobuf::testing::StringtoInt* temp = empty_key_string_to_int2_;
  empty_key_string_to_int2_ = NULL;
  return temp;
}
inline void MapsTestCases::set_allocated_empty_key_string_to_int2(::google::protobuf::testing::StringtoInt* empty_key_string_to_int2) {
  delete empty_key_string_to_int2_;
  empty_key_string_to_int2_ = empty_key_string_to_int2;
  if (empty_key_string_to_int2) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_set_allocated:google.protobuf.testing.MapsTestCases.empty_key_string_to_int2)
}

// optional .google.protobuf.testing.StringtoInt empty_key_string_to_int3 = 9;
inline bool MapsTestCases::has_empty_key_string_to_int3() const {
  return this != internal_default_instance() && empty_key_string_to_int3_ != NULL;
}
inline void MapsTestCases::clear_empty_key_string_to_int3() {
  if (GetArenaNoVirtual() == NULL && empty_key_string_to_int3_ != NULL) delete empty_key_string_to_int3_;
  empty_key_string_to_int3_ = NULL;
}
inline const ::google::protobuf::testing::StringtoInt& MapsTestCases::empty_key_string_to_int3() const {
  // @@protoc_insertion_point(field_get:google.protobuf.testing.MapsTestCases.empty_key_string_to_int3)
  return empty_key_string_to_int3_ != NULL ? *empty_key_string_to_int3_
                         : *::google::protobuf::testing::StringtoInt::internal_default_instance();
}
inline ::google::protobuf::testing::StringtoInt* MapsTestCases::mutable_empty_key_string_to_int3() {
  
  if (empty_key_string_to_int3_ == NULL) {
    empty_key_string_to_int3_ = new ::google::protobuf::testing::StringtoInt;
  }
  // @@protoc_insertion_point(field_mutable:google.protobuf.testing.MapsTestCases.empty_key_string_to_int3)
  return empty_key_string_to_int3_;
}
inline ::google::protobuf::testing::StringtoInt* MapsTestCases::release_empty_key_string_to_int3() {
  // @@protoc_insertion_point(field_release:google.protobuf.testing.MapsTestCases.empty_key_string_to_int3)
  
  ::google::protobuf::testing::StringtoInt* temp = empty_key_string_to_int3_;
  empty_key_string_to_int3_ = NULL;
  return temp;
}
inline void MapsTestCases::set_allocated_empty_key_string_to_int3(::google::protobuf::testing::StringtoInt* empty_key_string_to_int3) {
  delete empty_key_string_to_int3_;
  empty_key_string_to_int3_ = empty_key_string_to_int3;
  if (empty_key_string_to_int3) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_set_allocated:google.protobuf.testing.MapsTestCases.empty_key_string_to_int3)
}

// optional .google.protobuf.testing.BoolToString empty_key_bool_to_string = 10;
inline bool MapsTestCases::has_empty_key_bool_to_string() const {
  return this != internal_default_instance() && empty_key_bool_to_string_ != NULL;
}
inline void MapsTestCases::clear_empty_key_bool_to_string() {
  if (GetArenaNoVirtual() == NULL && empty_key_bool_to_string_ != NULL) delete empty_key_bool_to_string_;
  empty_key_bool_to_string_ = NULL;
}
inline const ::google::protobuf::testing::BoolToString& MapsTestCases::empty_key_bool_to_string() const {
  // @@protoc_insertion_point(field_get:google.protobuf.testing.MapsTestCases.empty_key_bool_to_string)
  return empty_key_bool_to_string_ != NULL ? *empty_key_bool_to_string_
                         : *::google::protobuf::testing::BoolToString::internal_default_instance();
}
inline ::google::protobuf::testing::BoolToString* MapsTestCases::mutable_empty_key_bool_to_string() {
  
  if (empty_key_bool_to_string_ == NULL) {
    empty_key_bool_to_string_ = new ::google::protobuf::testing::BoolToString;
  }
  // @@protoc_insertion_point(field_mutable:google.protobuf.testing.MapsTestCases.empty_key_bool_to_string)
  return empty_key_bool_to_string_;
}
inline ::google::protobuf::testing::BoolToString* MapsTestCases::release_empty_key_bool_to_string() {
  // @@protoc_insertion_point(field_release:google.protobuf.testing.MapsTestCases.empty_key_bool_to_string)
  
  ::google::protobuf::testing::BoolToString* temp = empty_key_bool_to_string_;
  empty_key_bool_to_string_ = NULL;
  return temp;
}
inline void MapsTestCases::set_allocated_empty_key_bool_to_string(::google::protobuf::testing::BoolToString* empty_key_bool_to_string) {
  delete empty_key_bool_to_string_;
  empty_key_bool_to_string_ = empty_key_bool_to_string;
  if (empty_key_bool_to_string) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_set_allocated:google.protobuf.testing.MapsTestCases.empty_key_bool_to_string)
}

// optional .google.protobuf.testing.IntToString empty_key_int_to_string = 11;
inline bool MapsTestCases::has_empty_key_int_to_string() const {
  return this != internal_default_instance() && empty_key_int_to_string_ != NULL;
}
inline void MapsTestCases::clear_empty_key_int_to_string() {
  if (GetArenaNoVirtual() == NULL && empty_key_int_to_string_ != NULL) delete empty_key_int_to_string_;
  empty_key_int_to_string_ = NULL;
}
inline const ::google::protobuf::testing::IntToString& MapsTestCases::empty_key_int_to_string() const {
  // @@protoc_insertion_point(field_get:google.protobuf.testing.MapsTestCases.empty_key_int_to_string)
  return empty_key_int_to_string_ != NULL ? *empty_key_int_to_string_
                         : *::google::protobuf::testing::IntToString::internal_default_instance();
}
inline ::google::protobuf::testing::IntToString* MapsTestCases::mutable_empty_key_int_to_string() {
  
  if (empty_key_int_to_string_ == NULL) {
    empty_key_int_to_string_ = new ::google::protobuf::testing::IntToString;
  }
  // @@protoc_insertion_point(field_mutable:google.protobuf.testing.MapsTestCases.empty_key_int_to_string)
  return empty_key_int_to_string_;
}
inline ::google::protobuf::testing::IntToString* MapsTestCases::release_empty_key_int_to_string() {
  // @@protoc_insertion_point(field_release:google.protobuf.testing.MapsTestCases.empty_key_int_to_string)
  
  ::google::protobuf::testing::IntToString* temp = empty_key_int_to_string_;
  empty_key_int_to_string_ = NULL;
  return temp;
}
inline void MapsTestCases::set_allocated_empty_key_int_to_string(::google::protobuf::testing::IntToString* empty_key_int_to_string) {
  delete empty_key_int_to_string_;
  empty_key_int_to_string_ = empty_key_int_to_string;
  if (empty_key_int_to_string) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_set_allocated:google.protobuf.testing.MapsTestCases.empty_key_int_to_string)
}

// optional .google.protobuf.testing.Mixed1 empty_key_mixed = 12;
inline bool MapsTestCases::has_empty_key_mixed() const {
  return this != internal_default_instance() && empty_key_mixed_ != NULL;
}
inline void MapsTestCases::clear_empty_key_mixed() {
  if (GetArenaNoVirtual() == NULL && empty_key_mixed_ != NULL) delete empty_key_mixed_;
  empty_key_mixed_ = NULL;
}
inline const ::google::protobuf::testing::Mixed1& MapsTestCases::empty_key_mixed() const {
  // @@protoc_insertion_point(field_get:google.protobuf.testing.MapsTestCases.empty_key_mixed)
  return empty_key_mixed_ != NULL ? *empty_key_mixed_
                         : *::google::protobuf::testing::Mixed1::internal_default_instance();
}
inline ::google::protobuf::testing::Mixed1* MapsTestCases::mutable_empty_key_mixed() {
  
  if (empty_key_mixed_ == NULL) {
    empty_key_mixed_ = new ::google::protobuf::testing::Mixed1;
  }
  // @@protoc_insertion_point(field_mutable:google.protobuf.testing.MapsTestCases.empty_key_mixed)
  return empty_key_mixed_;
}
inline ::google::protobuf::testing::Mixed1* MapsTestCases::release_empty_key_mixed() {
  // @@protoc_insertion_point(field_release:google.protobuf.testing.MapsTestCases.empty_key_mixed)
  
  ::google::protobuf::testing::Mixed1* temp = empty_key_mixed_;
  empty_key_mixed_ = NULL;
  return temp;
}
inline void MapsTestCases::set_allocated_empty_key_mixed(::google::protobuf::testing::Mixed1* empty_key_mixed) {
  delete empty_key_mixed_;
  empty_key_mixed_ = empty_key_mixed;
  if (empty_key_mixed) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_set_allocated:google.protobuf.testing.MapsTestCases.empty_key_mixed)
}

// optional .google.protobuf.testing.MapOfObjects empty_key_map_objects = 13;
inline bool MapsTestCases::has_empty_key_map_objects() const {
  return this != internal_default_instance() && empty_key_map_objects_ != NULL;
}
inline void MapsTestCases::clear_empty_key_map_objects() {
  if (GetArenaNoVirtual() == NULL && empty_key_map_objects_ != NULL) delete empty_key_map_objects_;
  empty_key_map_objects_ = NULL;
}
inline const ::google::protobuf::testing::MapOfObjects& MapsTestCases::empty_key_map_objects() const {
  // @@protoc_insertion_point(field_get:google.protobuf.testing.MapsTestCases.empty_key_map_objects)
  return empty_key_map_objects_ != NULL ? *empty_key_map_objects_
                         : *::google::protobuf::testing::MapOfObjects::internal_default_instance();
}
inline ::google::protobuf::testing::MapOfObjects* MapsTestCases::mutable_empty_key_map_objects() {
  
  if (empty_key_map_objects_ == NULL) {
    empty_key_map_objects_ = new ::google::protobuf::testing::MapOfObjects;
  }
  // @@protoc_insertion_point(field_mutable:google.protobuf.testing.MapsTestCases.empty_key_map_objects)
  return empty_key_map_objects_;
}
inline ::google::protobuf::testing::MapOfObjects* MapsTestCases::release_empty_key_map_objects() {
  // @@protoc_insertion_point(field_release:google.protobuf.testing.MapsTestCases.empty_key_map_objects)
  
  ::google::protobuf::testing::MapOfObjects* temp = empty_key_map_objects_;
  empty_key_map_objects_ = NULL;
  return temp;
}
inline void MapsTestCases::set_allocated_empty_key_map_objects(::google::protobuf::testing::MapOfObjects* empty_key_map_objects) {
  delete empty_key_map_objects_;
  empty_key_map_objects_ = empty_key_map_objects;
  if (empty_key_map_objects) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_set_allocated:google.protobuf.testing.MapsTestCases.empty_key_map_objects)
}

inline const MapsTestCases* MapsTestCases::internal_default_instance() {
  return &MapsTestCases_default_instance_.get();
}
// -------------------------------------------------------------------

// EmptyMap

// map<int32, int32> map = 1;
inline int EmptyMap::map_size() const {
  return map_.size();
}
inline void EmptyMap::clear_map() {
  map_.Clear();
}
inline const ::google::protobuf::Map< ::google::protobuf::int32, ::google::protobuf::int32 >&
EmptyMap::map() const {
  // @@protoc_insertion_point(field_map:google.protobuf.testing.EmptyMap.map)
  return map_.GetMap();
}
inline ::google::protobuf::Map< ::google::protobuf::int32, ::google::protobuf::int32 >*
EmptyMap::mutable_map() {
  // @@protoc_insertion_point(field_mutable_map:google.protobuf.testing.EmptyMap.map)
  return map_.MutableMap();
}

inline const EmptyMap* EmptyMap::internal_default_instance() {
  return &EmptyMap_default_instance_.get();
}
// -------------------------------------------------------------------

// StringtoInt

// map<string, int32> map = 1;
inline int StringtoInt::map_size() const {
  return map_.size();
}
inline void StringtoInt::clear_map() {
  map_.Clear();
}
inline const ::google::protobuf::Map< ::std::string, ::google::protobuf::int32 >&
StringtoInt::map() const {
  // @@protoc_insertion_point(field_map:google.protobuf.testing.StringtoInt.map)
  return map_.GetMap();
}
inline ::google::protobuf::Map< ::std::string, ::google::protobuf::int32 >*
StringtoInt::mutable_map() {
  // @@protoc_insertion_point(field_mutable_map:google.protobuf.testing.StringtoInt.map)
  return map_.MutableMap();
}

inline const StringtoInt* StringtoInt::internal_default_instance() {
  return &StringtoInt_default_instance_.get();
}
// -------------------------------------------------------------------

// IntToString

// map<int32, string> map = 1;
inline int IntToString::map_size() const {
  return map_.size();
}
inline void IntToString::clear_map() {
  map_.Clear();
}
inline const ::google::protobuf::Map< ::google::protobuf::int32, ::std::string >&
IntToString::map() const {
  // @@protoc_insertion_point(field_map:google.protobuf.testing.IntToString.map)
  return map_.GetMap();
}
inline ::google::protobuf::Map< ::google::protobuf::int32, ::std::string >*
IntToString::mutable_map() {
  // @@protoc_insertion_point(field_mutable_map:google.protobuf.testing.IntToString.map)
  return map_.MutableMap();
}

inline const IntToString* IntToString::internal_default_instance() {
  return &IntToString_default_instance_.get();
}
// -------------------------------------------------------------------

// BoolToString

// map<bool, string> map = 1;
inline int BoolToString::map_size() const {
  return map_.size();
}
inline void BoolToString::clear_map() {
  map_.Clear();
}
inline const ::google::protobuf::Map< bool, ::std::string >&
BoolToString::map() const {
  // @@protoc_insertion_point(field_map:google.protobuf.testing.BoolToString.map)
  return map_.GetMap();
}
inline ::google::protobuf::Map< bool, ::std::string >*
BoolToString::mutable_map() {
  // @@protoc_insertion_point(field_mutable_map:google.protobuf.testing.BoolToString.map)
  return map_.MutableMap();
}

inline const BoolToString* BoolToString::internal_default_instance() {
  return &BoolToString_default_instance_.get();
}
// -------------------------------------------------------------------

// Mixed1

// optional string msg = 1;
inline void Mixed1::clear_msg() {
  msg_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline const ::std::string& Mixed1::msg() const {
  // @@protoc_insertion_point(field_get:google.protobuf.testing.Mixed1.msg)
  return msg_.GetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void Mixed1::set_msg(const ::std::string& value) {
  
  msg_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:google.protobuf.testing.Mixed1.msg)
}
inline void Mixed1::set_msg(const char* value) {
  
  msg_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:google.protobuf.testing.Mixed1.msg)
}
inline void Mixed1::set_msg(const char* value, size_t size) {
  
  msg_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:google.protobuf.testing.Mixed1.msg)
}
inline ::std::string* Mixed1::mutable_msg() {
  
  // @@protoc_insertion_point(field_mutable:google.protobuf.testing.Mixed1.msg)
  return msg_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline ::std::string* Mixed1::release_msg() {
  // @@protoc_insertion_point(field_release:google.protobuf.testing.Mixed1.msg)
  
  return msg_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void Mixed1::set_allocated_msg(::std::string* msg) {
  if (msg != NULL) {
    
  } else {
    
  }
  msg_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), msg);
  // @@protoc_insertion_point(field_set_allocated:google.protobuf.testing.Mixed1.msg)
}

// map<string, float> map = 2;
inline int Mixed1::map_size() const {
  return map_.size();
}
inline void Mixed1::clear_map() {
  map_.Clear();
}
inline const ::google::protobuf::Map< ::std::string, float >&
Mixed1::map() const {
  // @@protoc_insertion_point(field_map:google.protobuf.testing.Mixed1.map)
  return map_.GetMap();
}
inline ::google::protobuf::Map< ::std::string, float >*
Mixed1::mutable_map() {
  // @@protoc_insertion_point(field_mutable_map:google.protobuf.testing.Mixed1.map)
  return map_.MutableMap();
}

inline const Mixed1* Mixed1::internal_default_instance() {
  return &Mixed1_default_instance_.get();
}
// -------------------------------------------------------------------

// Mixed2

// map<int32, bool> map = 1;
inline int Mixed2::map_size() const {
  return map_.size();
}
inline void Mixed2::clear_map() {
  map_.Clear();
}
inline const ::google::protobuf::Map< ::google::protobuf::int32, bool >&
Mixed2::map() const {
  // @@protoc_insertion_point(field_map:google.protobuf.testing.Mixed2.map)
  return map_.GetMap();
}
inline ::google::protobuf::Map< ::google::protobuf::int32, bool >*
Mixed2::mutable_map() {
  // @@protoc_insertion_point(field_mutable_map:google.protobuf.testing.Mixed2.map)
  return map_.MutableMap();
}

// optional .google.protobuf.testing.Mixed2.E ee = 2;
inline void Mixed2::clear_ee() {
  ee_ = 0;
}
inline ::google::protobuf::testing::Mixed2_E Mixed2::ee() const {
  // @@protoc_insertion_point(field_get:google.protobuf.testing.Mixed2.ee)
  return static_cast< ::google::protobuf::testing::Mixed2_E >(ee_);
}
inline void Mixed2::set_ee(::google::protobuf::testing::Mixed2_E value) {
  
  ee_ = value;
  // @@protoc_insertion_point(field_set:google.protobuf.testing.Mixed2.ee)
}

inline const Mixed2* Mixed2::internal_default_instance() {
  return &Mixed2_default_instance_.get();
}
// -------------------------------------------------------------------

// MapOfObjects_M

// optional string inner_text = 1;
inline void MapOfObjects_M::clear_inner_text() {
  inner_text_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline const ::std::string& MapOfObjects_M::inner_text() const {
  // @@protoc_insertion_point(field_get:google.protobuf.testing.MapOfObjects.M.inner_text)
  return inner_text_.GetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void MapOfObjects_M::set_inner_text(const ::std::string& value) {
  
  inner_text_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:google.protobuf.testing.MapOfObjects.M.inner_text)
}
inline void MapOfObjects_M::set_inner_text(const char* value) {
  
  inner_text_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:google.protobuf.testing.MapOfObjects.M.inner_text)
}
inline void MapOfObjects_M::set_inner_text(const char* value, size_t size) {
  
  inner_text_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:google.protobuf.testing.MapOfObjects.M.inner_text)
}
inline ::std::string* MapOfObjects_M::mutable_inner_text() {
  
  // @@protoc_insertion_point(field_mutable:google.protobuf.testing.MapOfObjects.M.inner_text)
  return inner_text_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline ::std::string* MapOfObjects_M::release_inner_text() {
  // @@protoc_insertion_point(field_release:google.protobuf.testing.MapOfObjects.M.inner_text)
  
  return inner_text_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void MapOfObjects_M::set_allocated_inner_text(::std::string* inner_text) {
  if (inner_text != NULL) {
    
  } else {
    
  }
  inner_text_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), inner_text);
  // @@protoc_insertion_point(field_set_allocated:google.protobuf.testing.MapOfObjects.M.inner_text)
}

inline const MapOfObjects_M* MapOfObjects_M::internal_default_instance() {
  return &MapOfObjects_M_default_instance_.get();
}
// -------------------------------------------------------------------

// MapOfObjects

// map<string, .google.protobuf.testing.MapOfObjects.M> map = 1;
inline int MapOfObjects::map_size() const {
  return map_.size();
}
inline void MapOfObjects::clear_map() {
  map_.Clear();
}
inline const ::google::protobuf::Map< ::std::string, ::google::protobuf::testing::MapOfObjects_M >&
MapOfObjects::map() const {
  // @@protoc_insertion_point(field_map:google.protobuf.testing.MapOfObjects.map)
  return map_.GetMap();
}
inline ::google::protobuf::Map< ::std::string, ::google::protobuf::testing::MapOfObjects_M >*
MapOfObjects::mutable_map() {
  // @@protoc_insertion_point(field_mutable_map:google.protobuf.testing.MapOfObjects.map)
  return map_.MutableMap();
}

inline const MapOfObjects* MapOfObjects::internal_default_instance() {
  return &MapOfObjects_default_instance_.get();
}
// -------------------------------------------------------------------

// DummyRequest

inline const DummyRequest* DummyRequest::internal_default_instance() {
  return &DummyRequest_default_instance_.get();
}
// -------------------------------------------------------------------

// MapIn

// optional string other = 1;
inline void MapIn::clear_other() {
  other_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline const ::std::string& MapIn::other() const {
  // @@protoc_insertion_point(field_get:google.protobuf.testing.MapIn.other)
  return other_.GetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void MapIn::set_other(const ::std::string& value) {
  
  other_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:google.protobuf.testing.MapIn.other)
}
inline void MapIn::set_other(const char* value) {
  
  other_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:google.protobuf.testing.MapIn.other)
}
inline void MapIn::set_other(const char* value, size_t size) {
  
  other_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:google.protobuf.testing.MapIn.other)
}
inline ::std::string* MapIn::mutable_other() {
  
  // @@protoc_insertion_point(field_mutable:google.protobuf.testing.MapIn.other)
  return other_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline ::std::string* MapIn::release_other() {
  // @@protoc_insertion_point(field_release:google.protobuf.testing.MapIn.other)
  
  return other_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void MapIn::set_allocated_other(::std::string* other) {
  if (other != NULL) {
    
  } else {
    
  }
  other_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), other);
  // @@protoc_insertion_point(field_set_allocated:google.protobuf.testing.MapIn.other)
}

// repeated string things = 2;
inline int MapIn::things_size() const {
  return things_.size();
}
inline void MapIn::clear_things() {
  things_.Clear();
}
inline const ::std::string& MapIn::things(int index) const {
  // @@protoc_insertion_point(field_get:google.protobuf.testing.MapIn.things)
  return things_.Get(index);
}
inline ::std::string* MapIn::mutable_things(int index) {
  // @@protoc_insertion_point(field_mutable:google.protobuf.testing.MapIn.things)
  return things_.Mutable(index);
}
inline void MapIn::set_things(int index, const ::std::string& value) {
  // @@protoc_insertion_point(field_set:google.protobuf.testing.MapIn.things)
  things_.Mutable(index)->assign(value);
}
inline void MapIn::set_things(int index, const char* value) {
  things_.Mutable(index)->assign(value);
  // @@protoc_insertion_point(field_set_char:google.protobuf.testing.MapIn.things)
}
inline void MapIn::set_things(int index, const char* value, size_t size) {
  things_.Mutable(index)->assign(
    reinterpret_cast<const char*>(value), size);
  // @@protoc_insertion_point(field_set_pointer:google.protobuf.testing.MapIn.things)
}
inline ::std::string* MapIn::add_things() {
  // @@protoc_insertion_point(field_add_mutable:google.protobuf.testing.MapIn.things)
  return things_.Add();
}
inline void MapIn::add_things(const ::std::string& value) {
  things_.Add()->assign(value);
  // @@protoc_insertion_point(field_add:google.protobuf.testing.MapIn.things)
}
inline void MapIn::add_things(const char* value) {
  things_.Add()->assign(value);
  // @@protoc_insertion_point(field_add_char:google.protobuf.testing.MapIn.things)
}
inline void MapIn::add_things(const char* value, size_t size) {
  things_.Add()->assign(reinterpret_cast<const char*>(value), size);
  // @@protoc_insertion_point(field_add_pointer:google.protobuf.testing.MapIn.things)
}
inline const ::google::protobuf::RepeatedPtrField< ::std::string>&
MapIn::things() const {
  // @@protoc_insertion_point(field_list:google.protobuf.testing.MapIn.things)
  return things_;
}
inline ::google::protobuf::RepeatedPtrField< ::std::string>*
MapIn::mutable_things() {
  // @@protoc_insertion_point(field_mutable_list:google.protobuf.testing.MapIn.things)
  return &things_;
}

// map<string, string> map_input = 3;
inline int MapIn::map_input_size() const {
  return map_input_.size();
}
inline void MapIn::clear_map_input() {
  map_input_.Clear();
}
inline const ::google::protobuf::Map< ::std::string, ::std::string >&
MapIn::map_input() const {
  // @@protoc_insertion_point(field_map:google.protobuf.testing.MapIn.map_input)
  return map_input_.GetMap();
}
inline ::google::protobuf::Map< ::std::string, ::std::string >*
MapIn::mutable_map_input() {
  // @@protoc_insertion_point(field_mutable_map:google.protobuf.testing.MapIn.map_input)
  return map_input_.MutableMap();
}

inline const MapIn* MapIn::internal_default_instance() {
  return &MapIn_default_instance_.get();
}
// -------------------------------------------------------------------

// MapOut

// map<string, .google.protobuf.testing.MapM> map1 = 1;
inline int MapOut::map1_size() const {
  return map1_.size();
}
inline void MapOut::clear_map1() {
  map1_.Clear();
}
inline const ::google::protobuf::Map< ::std::string, ::google::protobuf::testing::MapM >&
MapOut::map1() const {
  // @@protoc_insertion_point(field_map:google.protobuf.testing.MapOut.map1)
  return map1_.GetMap();
}
inline ::google::protobuf::Map< ::std::string, ::google::protobuf::testing::MapM >*
MapOut::mutable_map1() {
  // @@protoc_insertion_point(field_mutable_map:google.protobuf.testing.MapOut.map1)
  return map1_.MutableMap();
}

// map<string, .google.protobuf.testing.MapOut> map2 = 2;
inline int MapOut::map2_size() const {
  return map2_.size();
}
inline void MapOut::clear_map2() {
  map2_.Clear();
}
inline const ::google::protobuf::Map< ::std::string, ::google::protobuf::testing::MapOut >&
MapOut::map2() const {
  // @@protoc_insertion_point(field_map:google.protobuf.testing.MapOut.map2)
  return map2_.GetMap();
}
inline ::google::protobuf::Map< ::std::string, ::google::protobuf::testing::MapOut >*
MapOut::mutable_map2() {
  // @@protoc_insertion_point(field_mutable_map:google.protobuf.testing.MapOut.map2)
  return map2_.MutableMap();
}

// map<int32, string> map3 = 3;
inline int MapOut::map3_size() const {
  return map3_.size();
}
inline void MapOut::clear_map3() {
  map3_.Clear();
}
inline const ::google::protobuf::Map< ::google::protobuf::int32, ::std::string >&
MapOut::map3() const {
  // @@protoc_insertion_point(field_map:google.protobuf.testing.MapOut.map3)
  return map3_.GetMap();
}
inline ::google::protobuf::Map< ::google::protobuf::int32, ::std::string >*
MapOut::mutable_map3() {
  // @@protoc_insertion_point(field_mutable_map:google.protobuf.testing.MapOut.map3)
  return map3_.MutableMap();
}

// map<bool, string> map4 = 5;
inline int MapOut::map4_size() const {
  return map4_.size();
}
inline void MapOut::clear_map4() {
  map4_.Clear();
}
inline const ::google::protobuf::Map< bool, ::std::string >&
MapOut::map4() const {
  // @@protoc_insertion_point(field_map:google.protobuf.testing.MapOut.map4)
  return map4_.GetMap();
}
inline ::google::protobuf::Map< bool, ::std::string >*
MapOut::mutable_map4() {
  // @@protoc_insertion_point(field_mutable_map:google.protobuf.testing.MapOut.map4)
  return map4_.MutableMap();
}

// optional string bar = 4;
inline void MapOut::clear_bar() {
  bar_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline const ::std::string& MapOut::bar() const {
  // @@protoc_insertion_point(field_get:google.protobuf.testing.MapOut.bar)
  return bar_.GetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void MapOut::set_bar(const ::std::string& value) {
  
  bar_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:google.protobuf.testing.MapOut.bar)
}
inline void MapOut::set_bar(const char* value) {
  
  bar_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:google.protobuf.testing.MapOut.bar)
}
inline void MapOut::set_bar(const char* value, size_t size) {
  
  bar_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:google.protobuf.testing.MapOut.bar)
}
inline ::std::string* MapOut::mutable_bar() {
  
  // @@protoc_insertion_point(field_mutable:google.protobuf.testing.MapOut.bar)
  return bar_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline ::std::string* MapOut::release_bar() {
  // @@protoc_insertion_point(field_release:google.protobuf.testing.MapOut.bar)
  
  return bar_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void MapOut::set_allocated_bar(::std::string* bar) {
  if (bar != NULL) {
    
  } else {
    
  }
  bar_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), bar);
  // @@protoc_insertion_point(field_set_allocated:google.protobuf.testing.MapOut.bar)
}

inline const MapOut* MapOut::internal_default_instance() {
  return &MapOut_default_instance_.get();
}
// -------------------------------------------------------------------

// MapOutWireFormat_Map1Entry

// optional string key = 1;
inline void MapOutWireFormat_Map1Entry::clear_key() {
  key_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline const ::std::string& MapOutWireFormat_Map1Entry::key() const {
  // @@protoc_insertion_point(field_get:google.protobuf.testing.MapOutWireFormat.Map1Entry.key)
  return key_.GetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void MapOutWireFormat_Map1Entry::set_key(const ::std::string& value) {
  
  key_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:google.protobuf.testing.MapOutWireFormat.Map1Entry.key)
}
inline void MapOutWireFormat_Map1Entry::set_key(const char* value) {
  
  key_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:google.protobuf.testing.MapOutWireFormat.Map1Entry.key)
}
inline void MapOutWireFormat_Map1Entry::set_key(const char* value, size_t size) {
  
  key_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:google.protobuf.testing.MapOutWireFormat.Map1Entry.key)
}
inline ::std::string* MapOutWireFormat_Map1Entry::mutable_key() {
  
  // @@protoc_insertion_point(field_mutable:google.protobuf.testing.MapOutWireFormat.Map1Entry.key)
  return key_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline ::std::string* MapOutWireFormat_Map1Entry::release_key() {
  // @@protoc_insertion_point(field_release:google.protobuf.testing.MapOutWireFormat.Map1Entry.key)
  
  return key_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void MapOutWireFormat_Map1Entry::set_allocated_key(::std::string* key) {
  if (key != NULL) {
    
  } else {
    
  }
  key_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), key);
  // @@protoc_insertion_point(field_set_allocated:google.protobuf.testing.MapOutWireFormat.Map1Entry.key)
}

// optional .google.protobuf.testing.MapM value = 2;
inline bool MapOutWireFormat_Map1Entry::has_value() const {
  return this != internal_default_instance() && value_ != NULL;
}
inline void MapOutWireFormat_Map1Entry::clear_value() {
  if (GetArenaNoVirtual() == NULL && value_ != NULL) delete value_;
  value_ = NULL;
}
inline const ::google::protobuf::testing::MapM& MapOutWireFormat_Map1Entry::value() const {
  // @@protoc_insertion_point(field_get:google.protobuf.testing.MapOutWireFormat.Map1Entry.value)
  return value_ != NULL ? *value_
                         : *::google::protobuf::testing::MapM::internal_default_instance();
}
inline ::google::protobuf::testing::MapM* MapOutWireFormat_Map1Entry::mutable_value() {
  
  if (value_ == NULL) {
    value_ = new ::google::protobuf::testing::MapM;
  }
  // @@protoc_insertion_point(field_mutable:google.protobuf.testing.MapOutWireFormat.Map1Entry.value)
  return value_;
}
inline ::google::protobuf::testing::MapM* MapOutWireFormat_Map1Entry::release_value() {
  // @@protoc_insertion_point(field_release:google.protobuf.testing.MapOutWireFormat.Map1Entry.value)
  
  ::google::protobuf::testing::MapM* temp = value_;
  value_ = NULL;
  return temp;
}
inline void MapOutWireFormat_Map1Entry::set_allocated_value(::google::protobuf::testing::MapM* value) {
  delete value_;
  value_ = value;
  if (value) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_set_allocated:google.protobuf.testing.MapOutWireFormat.Map1Entry.value)
}

inline const MapOutWireFormat_Map1Entry* MapOutWireFormat_Map1Entry::internal_default_instance() {
  return &MapOutWireFormat_Map1Entry_default_instance_.get();
}
// -------------------------------------------------------------------

// MapOutWireFormat_Map2Entry

// optional string key = 1;
inline void MapOutWireFormat_Map2Entry::clear_key() {
  key_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline const ::std::string& MapOutWireFormat_Map2Entry::key() const {
  // @@protoc_insertion_point(field_get:google.protobuf.testing.MapOutWireFormat.Map2Entry.key)
  return key_.GetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void MapOutWireFormat_Map2Entry::set_key(const ::std::string& value) {
  
  key_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:google.protobuf.testing.MapOutWireFormat.Map2Entry.key)
}
inline void MapOutWireFormat_Map2Entry::set_key(const char* value) {
  
  key_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:google.protobuf.testing.MapOutWireFormat.Map2Entry.key)
}
inline void MapOutWireFormat_Map2Entry::set_key(const char* value, size_t size) {
  
  key_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:google.protobuf.testing.MapOutWireFormat.Map2Entry.key)
}
inline ::std::string* MapOutWireFormat_Map2Entry::mutable_key() {
  
  // @@protoc_insertion_point(field_mutable:google.protobuf.testing.MapOutWireFormat.Map2Entry.key)
  return key_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline ::std::string* MapOutWireFormat_Map2Entry::release_key() {
  // @@protoc_insertion_point(field_release:google.protobuf.testing.MapOutWireFormat.Map2Entry.key)
  
  return key_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void MapOutWireFormat_Map2Entry::set_allocated_key(::std::string* key) {
  if (key != NULL) {
    
  } else {
    
  }
  key_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), key);
  // @@protoc_insertion_point(field_set_allocated:google.protobuf.testing.MapOutWireFormat.Map2Entry.key)
}

// optional .google.protobuf.testing.MapOut value = 2;
inline bool MapOutWireFormat_Map2Entry::has_value() const {
  return this != internal_default_instance() && value_ != NULL;
}
inline void MapOutWireFormat_Map2Entry::clear_value() {
  if (GetArenaNoVirtual() == NULL && value_ != NULL) delete value_;
  value_ = NULL;
}
inline const ::google::protobuf::testing::MapOut& MapOutWireFormat_Map2Entry::value() const {
  // @@protoc_insertion_point(field_get:google.protobuf.testing.MapOutWireFormat.Map2Entry.value)
  return value_ != NULL ? *value_
                         : *::google::protobuf::testing::MapOut::internal_default_instance();
}
inline ::google::protobuf::testing::MapOut* MapOutWireFormat_Map2Entry::mutable_value() {
  
  if (value_ == NULL) {
    value_ = new ::google::protobuf::testing::MapOut;
  }
  // @@protoc_insertion_point(field_mutable:google.protobuf.testing.MapOutWireFormat.Map2Entry.value)
  return value_;
}
inline ::google::protobuf::testing::MapOut* MapOutWireFormat_Map2Entry::release_value() {
  // @@protoc_insertion_point(field_release:google.protobuf.testing.MapOutWireFormat.Map2Entry.value)
  
  ::google::protobuf::testing::MapOut* temp = value_;
  value_ = NULL;
  return temp;
}
inline void MapOutWireFormat_Map2Entry::set_allocated_value(::google::protobuf::testing::MapOut* value) {
  delete value_;
  value_ = value;
  if (value) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_set_allocated:google.protobuf.testing.MapOutWireFormat.Map2Entry.value)
}

inline const MapOutWireFormat_Map2Entry* MapOutWireFormat_Map2Entry::internal_default_instance() {
  return &MapOutWireFormat_Map2Entry_default_instance_.get();
}
// -------------------------------------------------------------------

// MapOutWireFormat_Map3Entry

// optional int32 key = 1;
inline void MapOutWireFormat_Map3Entry::clear_key() {
  key_ = 0;
}
inline ::google::protobuf::int32 MapOutWireFormat_Map3Entry::key() const {
  // @@protoc_insertion_point(field_get:google.protobuf.testing.MapOutWireFormat.Map3Entry.key)
  return key_;
}
inline void MapOutWireFormat_Map3Entry::set_key(::google::protobuf::int32 value) {
  
  key_ = value;
  // @@protoc_insertion_point(field_set:google.protobuf.testing.MapOutWireFormat.Map3Entry.key)
}

// optional string value = 2;
inline void MapOutWireFormat_Map3Entry::clear_value() {
  value_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline const ::std::string& MapOutWireFormat_Map3Entry::value() const {
  // @@protoc_insertion_point(field_get:google.protobuf.testing.MapOutWireFormat.Map3Entry.value)
  return value_.GetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void MapOutWireFormat_Map3Entry::set_value(const ::std::string& value) {
  
  value_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:google.protobuf.testing.MapOutWireFormat.Map3Entry.value)
}
inline void MapOutWireFormat_Map3Entry::set_value(const char* value) {
  
  value_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:google.protobuf.testing.MapOutWireFormat.Map3Entry.value)
}
inline void MapOutWireFormat_Map3Entry::set_value(const char* value, size_t size) {
  
  value_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:google.protobuf.testing.MapOutWireFormat.Map3Entry.value)
}
inline ::std::string* MapOutWireFormat_Map3Entry::mutable_value() {
  
  // @@protoc_insertion_point(field_mutable:google.protobuf.testing.MapOutWireFormat.Map3Entry.value)
  return value_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline ::std::string* MapOutWireFormat_Map3Entry::release_value() {
  // @@protoc_insertion_point(field_release:google.protobuf.testing.MapOutWireFormat.Map3Entry.value)
  
  return value_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void MapOutWireFormat_Map3Entry::set_allocated_value(::std::string* value) {
  if (value != NULL) {
    
  } else {
    
  }
  value_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set_allocated:google.protobuf.testing.MapOutWireFormat.Map3Entry.value)
}

inline const MapOutWireFormat_Map3Entry* MapOutWireFormat_Map3Entry::internal_default_instance() {
  return &MapOutWireFormat_Map3Entry_default_instance_.get();
}
// -------------------------------------------------------------------

// MapOutWireFormat_Map4Entry

// optional bool key = 1;
inline void MapOutWireFormat_Map4Entry::clear_key() {
  key_ = false;
}
inline bool MapOutWireFormat_Map4Entry::key() const {
  // @@protoc_insertion_point(field_get:google.protobuf.testing.MapOutWireFormat.Map4Entry.key)
  return key_;
}
inline void MapOutWireFormat_Map4Entry::set_key(bool value) {
  
  key_ = value;
  // @@protoc_insertion_point(field_set:google.protobuf.testing.MapOutWireFormat.Map4Entry.key)
}

// optional string value = 2;
inline void MapOutWireFormat_Map4Entry::clear_value() {
  value_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline const ::std::string& MapOutWireFormat_Map4Entry::value() const {
  // @@protoc_insertion_point(field_get:google.protobuf.testing.MapOutWireFormat.Map4Entry.value)
  return value_.GetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void MapOutWireFormat_Map4Entry::set_value(const ::std::string& value) {
  
  value_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:google.protobuf.testing.MapOutWireFormat.Map4Entry.value)
}
inline void MapOutWireFormat_Map4Entry::set_value(const char* value) {
  
  value_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:google.protobuf.testing.MapOutWireFormat.Map4Entry.value)
}
inline void MapOutWireFormat_Map4Entry::set_value(const char* value, size_t size) {
  
  value_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:google.protobuf.testing.MapOutWireFormat.Map4Entry.value)
}
inline ::std::string* MapOutWireFormat_Map4Entry::mutable_value() {
  
  // @@protoc_insertion_point(field_mutable:google.protobuf.testing.MapOutWireFormat.Map4Entry.value)
  return value_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline ::std::string* MapOutWireFormat_Map4Entry::release_value() {
  // @@protoc_insertion_point(field_release:google.protobuf.testing.MapOutWireFormat.Map4Entry.value)
  
  return value_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void MapOutWireFormat_Map4Entry::set_allocated_value(::std::string* value) {
  if (value != NULL) {
    
  } else {
    
  }
  value_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set_allocated:google.protobuf.testing.MapOutWireFormat.Map4Entry.value)
}

inline const MapOutWireFormat_Map4Entry* MapOutWireFormat_Map4Entry::internal_default_instance() {
  return &MapOutWireFormat_Map4Entry_default_instance_.get();
}
// -------------------------------------------------------------------

// MapOutWireFormat

// repeated .google.protobuf.testing.MapOutWireFormat.Map1Entry map1 = 1;
inline int MapOutWireFormat::map1_size() const {
  return map1_.size();
}
inline void MapOutWireFormat::clear_map1() {
  map1_.Clear();
}
inline const ::google::protobuf::testing::MapOutWireFormat_Map1Entry& MapOutWireFormat::map1(int index) const {
  // @@protoc_insertion_point(field_get:google.protobuf.testing.MapOutWireFormat.map1)
  return map1_.Get(index);
}
inline ::google::protobuf::testing::MapOutWireFormat_Map1Entry* MapOutWireFormat::mutable_map1(int index) {
  // @@protoc_insertion_point(field_mutable:google.protobuf.testing.MapOutWireFormat.map1)
  return map1_.Mutable(index);
}
inline ::google::protobuf::testing::MapOutWireFormat_Map1Entry* MapOutWireFormat::add_map1() {
  // @@protoc_insertion_point(field_add:google.protobuf.testing.MapOutWireFormat.map1)
  return map1_.Add();
}
inline ::google::protobuf::RepeatedPtrField< ::google::protobuf::testing::MapOutWireFormat_Map1Entry >*
MapOutWireFormat::mutable_map1() {
  // @@protoc_insertion_point(field_mutable_list:google.protobuf.testing.MapOutWireFormat.map1)
  return &map1_;
}
inline const ::google::protobuf::RepeatedPtrField< ::google::protobuf::testing::MapOutWireFormat_Map1Entry >&
MapOutWireFormat::map1() const {
  // @@protoc_insertion_point(field_list:google.protobuf.testing.MapOutWireFormat.map1)
  return map1_;
}

// repeated .google.protobuf.testing.MapOutWireFormat.Map2Entry map2 = 2;
inline int MapOutWireFormat::map2_size() const {
  return map2_.size();
}
inline void MapOutWireFormat::clear_map2() {
  map2_.Clear();
}
inline const ::google::protobuf::testing::MapOutWireFormat_Map2Entry& MapOutWireFormat::map2(int index) const {
  // @@protoc_insertion_point(field_get:google.protobuf.testing.MapOutWireFormat.map2)
  return map2_.Get(index);
}
inline ::google::protobuf::testing::MapOutWireFormat_Map2Entry* MapOutWireFormat::mutable_map2(int index) {
  // @@protoc_insertion_point(field_mutable:google.protobuf.testing.MapOutWireFormat.map2)
  return map2_.Mutable(index);
}
inline ::google::protobuf::testing::MapOutWireFormat_Map2Entry* MapOutWireFormat::add_map2() {
  // @@protoc_insertion_point(field_add:google.protobuf.testing.MapOutWireFormat.map2)
  return map2_.Add();
}
inline ::google::protobuf::RepeatedPtrField< ::google::protobuf::testing::MapOutWireFormat_Map2Entry >*
MapOutWireFormat::mutable_map2() {
  // @@protoc_insertion_point(field_mutable_list:google.protobuf.testing.MapOutWireFormat.map2)
  return &map2_;
}
inline const ::google::protobuf::RepeatedPtrField< ::google::protobuf::testing::MapOutWireFormat_Map2Entry >&
MapOutWireFormat::map2() const {
  // @@protoc_insertion_point(field_list:google.protobuf.testing.MapOutWireFormat.map2)
  return map2_;
}

// repeated .google.protobuf.testing.MapOutWireFormat.Map3Entry map3 = 3;
inline int MapOutWireFormat::map3_size() const {
  return map3_.size();
}
inline void MapOutWireFormat::clear_map3() {
  map3_.Clear();
}
inline const ::google::protobuf::testing::MapOutWireFormat_Map3Entry& MapOutWireFormat::map3(int index) const {
  // @@protoc_insertion_point(field_get:google.protobuf.testing.MapOutWireFormat.map3)
  return map3_.Get(index);
}
inline ::google::protobuf::testing::MapOutWireFormat_Map3Entry* MapOutWireFormat::mutable_map3(int index) {
  // @@protoc_insertion_point(field_mutable:google.protobuf.testing.MapOutWireFormat.map3)
  return map3_.Mutable(index);
}
inline ::google::protobuf::testing::MapOutWireFormat_Map3Entry* MapOutWireFormat::add_map3() {
  // @@protoc_insertion_point(field_add:google.protobuf.testing.MapOutWireFormat.map3)
  return map3_.Add();
}
inline ::google::protobuf::RepeatedPtrField< ::google::protobuf::testing::MapOutWireFormat_Map3Entry >*
MapOutWireFormat::mutable_map3() {
  // @@protoc_insertion_point(field_mutable_list:google.protobuf.testing.MapOutWireFormat.map3)
  return &map3_;
}
inline const ::google::protobuf::RepeatedPtrField< ::google::protobuf::testing::MapOutWireFormat_Map3Entry >&
MapOutWireFormat::map3() const {
  // @@protoc_insertion_point(field_list:google.protobuf.testing.MapOutWireFormat.map3)
  return map3_;
}

// repeated .google.protobuf.testing.MapOutWireFormat.Map4Entry map4 = 5;
inline int MapOutWireFormat::map4_size() const {
  return map4_.size();
}
inline void MapOutWireFormat::clear_map4() {
  map4_.Clear();
}
inline const ::google::protobuf::testing::MapOutWireFormat_Map4Entry& MapOutWireFormat::map4(int index) const {
  // @@protoc_insertion_point(field_get:google.protobuf.testing.MapOutWireFormat.map4)
  return map4_.Get(index);
}
inline ::google::protobuf::testing::MapOutWireFormat_Map4Entry* MapOutWireFormat::mutable_map4(int index) {
  // @@protoc_insertion_point(field_mutable:google.protobuf.testing.MapOutWireFormat.map4)
  return map4_.Mutable(index);
}
inline ::google::protobuf::testing::MapOutWireFormat_Map4Entry* MapOutWireFormat::add_map4() {
  // @@protoc_insertion_point(field_add:google.protobuf.testing.MapOutWireFormat.map4)
  return map4_.Add();
}
inline ::google::protobuf::RepeatedPtrField< ::google::protobuf::testing::MapOutWireFormat_Map4Entry >*
MapOutWireFormat::mutable_map4() {
  // @@protoc_insertion_point(field_mutable_list:google.protobuf.testing.MapOutWireFormat.map4)
  return &map4_;
}
inline const ::google::protobuf::RepeatedPtrField< ::google::protobuf::testing::MapOutWireFormat_Map4Entry >&
MapOutWireFormat::map4() const {
  // @@protoc_insertion_point(field_list:google.protobuf.testing.MapOutWireFormat.map4)
  return map4_;
}

// optional string bar = 4;
inline void MapOutWireFormat::clear_bar() {
  bar_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline const ::std::string& MapOutWireFormat::bar() const {
  // @@protoc_insertion_point(field_get:google.protobuf.testing.MapOutWireFormat.bar)
  return bar_.GetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void MapOutWireFormat::set_bar(const ::std::string& value) {
  
  bar_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:google.protobuf.testing.MapOutWireFormat.bar)
}
inline void MapOutWireFormat::set_bar(const char* value) {
  
  bar_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:google.protobuf.testing.MapOutWireFormat.bar)
}
inline void MapOutWireFormat::set_bar(const char* value, size_t size) {
  
  bar_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:google.protobuf.testing.MapOutWireFormat.bar)
}
inline ::std::string* MapOutWireFormat::mutable_bar() {
  
  // @@protoc_insertion_point(field_mutable:google.protobuf.testing.MapOutWireFormat.bar)
  return bar_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline ::std::string* MapOutWireFormat::release_bar() {
  // @@protoc_insertion_point(field_release:google.protobuf.testing.MapOutWireFormat.bar)
  
  return bar_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void MapOutWireFormat::set_allocated_bar(::std::string* bar) {
  if (bar != NULL) {
    
  } else {
    
  }
  bar_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), bar);
  // @@protoc_insertion_point(field_set_allocated:google.protobuf.testing.MapOutWireFormat.bar)
}

inline const MapOutWireFormat* MapOutWireFormat::internal_default_instance() {
  return &MapOutWireFormat_default_instance_.get();
}
// -------------------------------------------------------------------

// MapM

// optional string foo = 1;
inline void MapM::clear_foo() {
  foo_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline const ::std::string& MapM::foo() const {
  // @@protoc_insertion_point(field_get:google.protobuf.testing.MapM.foo)
  return foo_.GetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void MapM::set_foo(const ::std::string& value) {
  
  foo_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:google.protobuf.testing.MapM.foo)
}
inline void MapM::set_foo(const char* value) {
  
  foo_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:google.protobuf.testing.MapM.foo)
}
inline void MapM::set_foo(const char* value, size_t size) {
  
  foo_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:google.protobuf.testing.MapM.foo)
}
inline ::std::string* MapM::mutable_foo() {
  
  // @@protoc_insertion_point(field_mutable:google.protobuf.testing.MapM.foo)
  return foo_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline ::std::string* MapM::release_foo() {
  // @@protoc_insertion_point(field_release:google.protobuf.testing.MapM.foo)
  
  return foo_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void MapM::set_allocated_foo(::std::string* foo) {
  if (foo != NULL) {
    
  } else {
    
  }
  foo_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), foo);
  // @@protoc_insertion_point(field_set_allocated:google.protobuf.testing.MapM.foo)
}

inline const MapM* MapM::internal_default_instance() {
  return &MapM_default_instance_.get();
}
#endif  // !PROTOBUF_INLINE_NOT_IN_HEADERS
// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------


// @@protoc_insertion_point(namespace_scope)

}  // namespace testing
}  // namespace protobuf
}  // namespace google

#ifndef SWIG
namespace google {
namespace protobuf {

template <> struct is_proto_enum< ::google::protobuf::testing::Mixed2_E> : ::google::protobuf::internal::true_type {};
template <>
inline const EnumDescriptor* GetEnumDescriptor< ::google::protobuf::testing::Mixed2_E>() {
  return ::google::protobuf::testing::Mixed2_E_descriptor();
}

}  // namespace protobuf
}  // namespace google
#endif  // SWIG

// @@protoc_insertion_point(global_scope)

#endif  // PROTOBUF_google_2fprotobuf_2futil_2finternal_2ftestdata_2fmaps_2eproto__INCLUDED

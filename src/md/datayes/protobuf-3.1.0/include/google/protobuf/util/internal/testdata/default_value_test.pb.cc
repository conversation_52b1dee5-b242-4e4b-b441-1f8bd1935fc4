// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: google/protobuf/util/internal/testdata/default_value_test.proto

#define INTERNAL_SUPPRESS_PROTOBUF_FIELD_DEPRECATION
#include <google/protobuf/util/internal/testdata/default_value_test.pb.h>

#include <algorithm>

#include <google/protobuf/stubs/common.h>
#include <google/protobuf/stubs/port.h>
#include <google/protobuf/stubs/once.h>
#include <google/protobuf/io/coded_stream.h>
#include <google/protobuf/wire_format_lite_inl.h>
#include <google/protobuf/descriptor.h>
#include <google/protobuf/generated_message_reflection.h>
#include <google/protobuf/reflection_ops.h>
#include <google/protobuf/wire_format.h>
// @@protoc_insertion_point(includes)

namespace google {
namespace protobuf {
namespace testing {

namespace {

const ::google::protobuf::Descriptor* DefaultValueTest_descriptor_ = NULL;
const ::google::protobuf::internal::GeneratedMessageReflection*
  DefaultValueTest_reflection_ = NULL;
const ::google::protobuf::EnumDescriptor* DefaultValueTest_EnumDefault_descriptor_ = NULL;

}  // namespace


void protobuf_AssignDesc_google_2fprotobuf_2futil_2finternal_2ftestdata_2fdefault_5fvalue_5ftest_2eproto() GOOGLE_ATTRIBUTE_COLD;
void protobuf_AssignDesc_google_2fprotobuf_2futil_2finternal_2ftestdata_2fdefault_5fvalue_5ftest_2eproto() {
  protobuf_AddDesc_google_2fprotobuf_2futil_2finternal_2ftestdata_2fdefault_5fvalue_5ftest_2eproto();
  const ::google::protobuf::FileDescriptor* file =
    ::google::protobuf::DescriptorPool::generated_pool()->FindFileByName(
      "google/protobuf/util/internal/testdata/default_value_test.proto");
  GOOGLE_CHECK(file != NULL);
  DefaultValueTest_descriptor_ = file->message_type(0);
  static const int DefaultValueTest_offsets_[11] = {
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(DefaultValueTest, double_value_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(DefaultValueTest, repeated_double_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(DefaultValueTest, float_value_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(DefaultValueTest, int64_value_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(DefaultValueTest, uint64_value_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(DefaultValueTest, int32_value_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(DefaultValueTest, uint32_value_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(DefaultValueTest, bool_value_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(DefaultValueTest, string_value_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(DefaultValueTest, bytes_value_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(DefaultValueTest, enum_value_),
  };
  DefaultValueTest_reflection_ =
    ::google::protobuf::internal::GeneratedMessageReflection::NewGeneratedMessageReflection(
      DefaultValueTest_descriptor_,
      DefaultValueTest::internal_default_instance(),
      DefaultValueTest_offsets_,
      -1,
      -1,
      -1,
      sizeof(DefaultValueTest),
      GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(DefaultValueTest, _internal_metadata_));
  DefaultValueTest_EnumDefault_descriptor_ = DefaultValueTest_descriptor_->enum_type(0);
}

namespace {

GOOGLE_PROTOBUF_DECLARE_ONCE(protobuf_AssignDescriptors_once_);
void protobuf_AssignDescriptorsOnce() {
  ::google::protobuf::GoogleOnceInit(&protobuf_AssignDescriptors_once_,
                 &protobuf_AssignDesc_google_2fprotobuf_2futil_2finternal_2ftestdata_2fdefault_5fvalue_5ftest_2eproto);
}

void protobuf_RegisterTypes(const ::std::string&) GOOGLE_ATTRIBUTE_COLD;
void protobuf_RegisterTypes(const ::std::string&) {
  protobuf_AssignDescriptorsOnce();
  ::google::protobuf::MessageFactory::InternalRegisterGeneratedMessage(
      DefaultValueTest_descriptor_, DefaultValueTest::internal_default_instance());
}

}  // namespace

void protobuf_ShutdownFile_google_2fprotobuf_2futil_2finternal_2ftestdata_2fdefault_5fvalue_5ftest_2eproto() {
  DefaultValueTest_default_instance_.Shutdown();
  delete DefaultValueTest_reflection_;
}

void protobuf_InitDefaults_google_2fprotobuf_2futil_2finternal_2ftestdata_2fdefault_5fvalue_5ftest_2eproto_impl() {
  GOOGLE_PROTOBUF_VERIFY_VERSION;

  ::google::protobuf::internal::GetEmptyString();
  DefaultValueTest_default_instance_.DefaultConstruct();
  DefaultValueTest_default_instance_.get_mutable()->InitAsDefaultInstance();
}

GOOGLE_PROTOBUF_DECLARE_ONCE(protobuf_InitDefaults_google_2fprotobuf_2futil_2finternal_2ftestdata_2fdefault_5fvalue_5ftest_2eproto_once_);
void protobuf_InitDefaults_google_2fprotobuf_2futil_2finternal_2ftestdata_2fdefault_5fvalue_5ftest_2eproto() {
  ::google::protobuf::GoogleOnceInit(&protobuf_InitDefaults_google_2fprotobuf_2futil_2finternal_2ftestdata_2fdefault_5fvalue_5ftest_2eproto_once_,
                 &protobuf_InitDefaults_google_2fprotobuf_2futil_2finternal_2ftestdata_2fdefault_5fvalue_5ftest_2eproto_impl);
}
void protobuf_AddDesc_google_2fprotobuf_2futil_2finternal_2ftestdata_2fdefault_5fvalue_5ftest_2eproto_impl() {
  GOOGLE_PROTOBUF_VERIFY_VERSION;

  protobuf_InitDefaults_google_2fprotobuf_2futil_2finternal_2ftestdata_2fdefault_5fvalue_5ftest_2eproto();
  ::google::protobuf::DescriptorPool::InternalAddGeneratedFile(
    "\n\?google/protobuf/util/internal/testdata"
    "/default_value_test.proto\022\027google.protob"
    "uf.testing\"\372\002\n\020DefaultValueTest\022\024\n\014doubl"
    "e_value\030\001 \001(\001\022\027\n\017repeated_double\030\002 \003(\001\022\023"
    "\n\013float_value\030\003 \001(\002\022\023\n\013int64_value\030\005 \001(\003"
    "\022\024\n\014uint64_value\030\007 \001(\004\022\023\n\013int32_value\030\t "
    "\001(\005\022\024\n\014uint32_value\030\013 \001(\r\022\022\n\nbool_value\030"
    "\r \001(\010\022\024\n\014string_value\030\017 \001(\t\022\027\n\013bytes_val"
    "ue\030\021 \001(\014B\002\010\001\022I\n\nenum_value\030\022 \001(\01625.googl"
    "e.protobuf.testing.DefaultValueTest.Enum"
    "Default\">\n\013EnumDefault\022\016\n\nENUM_FIRST\020\000\022\017"
    "\n\013ENUM_SECOND\020\001\022\016\n\nENUM_THIRD\020\002b\006proto3", 479);
  ::google::protobuf::MessageFactory::InternalRegisterGeneratedFile(
    "google/protobuf/util/internal/testdata/default_value_test.proto", &protobuf_RegisterTypes);
  ::google::protobuf::internal::OnShutdown(&protobuf_ShutdownFile_google_2fprotobuf_2futil_2finternal_2ftestdata_2fdefault_5fvalue_5ftest_2eproto);
}

GOOGLE_PROTOBUF_DECLARE_ONCE(protobuf_AddDesc_google_2fprotobuf_2futil_2finternal_2ftestdata_2fdefault_5fvalue_5ftest_2eproto_once_);
void protobuf_AddDesc_google_2fprotobuf_2futil_2finternal_2ftestdata_2fdefault_5fvalue_5ftest_2eproto() {
  ::google::protobuf::GoogleOnceInit(&protobuf_AddDesc_google_2fprotobuf_2futil_2finternal_2ftestdata_2fdefault_5fvalue_5ftest_2eproto_once_,
                 &protobuf_AddDesc_google_2fprotobuf_2futil_2finternal_2ftestdata_2fdefault_5fvalue_5ftest_2eproto_impl);
}
// Force AddDescriptors() to be called at static initialization time.
struct StaticDescriptorInitializer_google_2fprotobuf_2futil_2finternal_2ftestdata_2fdefault_5fvalue_5ftest_2eproto {
  StaticDescriptorInitializer_google_2fprotobuf_2futil_2finternal_2ftestdata_2fdefault_5fvalue_5ftest_2eproto() {
    protobuf_AddDesc_google_2fprotobuf_2futil_2finternal_2ftestdata_2fdefault_5fvalue_5ftest_2eproto();
  }
} static_descriptor_initializer_google_2fprotobuf_2futil_2finternal_2ftestdata_2fdefault_5fvalue_5ftest_2eproto_;

namespace {

static void MergeFromFail(int line) GOOGLE_ATTRIBUTE_COLD GOOGLE_ATTRIBUTE_NORETURN;
static void MergeFromFail(int line) {
  ::google::protobuf::internal::MergeFromFail(__FILE__, line);
}

}  // namespace


// ===================================================================

const ::google::protobuf::EnumDescriptor* DefaultValueTest_EnumDefault_descriptor() {
  protobuf_AssignDescriptorsOnce();
  return DefaultValueTest_EnumDefault_descriptor_;
}
bool DefaultValueTest_EnumDefault_IsValid(int value) {
  switch (value) {
    case 0:
    case 1:
    case 2:
      return true;
    default:
      return false;
  }
}

#if !defined(_MSC_VER) || _MSC_VER >= 1900
const DefaultValueTest_EnumDefault DefaultValueTest::ENUM_FIRST;
const DefaultValueTest_EnumDefault DefaultValueTest::ENUM_SECOND;
const DefaultValueTest_EnumDefault DefaultValueTest::ENUM_THIRD;
const DefaultValueTest_EnumDefault DefaultValueTest::EnumDefault_MIN;
const DefaultValueTest_EnumDefault DefaultValueTest::EnumDefault_MAX;
const int DefaultValueTest::EnumDefault_ARRAYSIZE;
#endif  // !defined(_MSC_VER) || _MSC_VER >= 1900
#if !defined(_MSC_VER) || _MSC_VER >= 1900
const int DefaultValueTest::kDoubleValueFieldNumber;
const int DefaultValueTest::kRepeatedDoubleFieldNumber;
const int DefaultValueTest::kFloatValueFieldNumber;
const int DefaultValueTest::kInt64ValueFieldNumber;
const int DefaultValueTest::kUint64ValueFieldNumber;
const int DefaultValueTest::kInt32ValueFieldNumber;
const int DefaultValueTest::kUint32ValueFieldNumber;
const int DefaultValueTest::kBoolValueFieldNumber;
const int DefaultValueTest::kStringValueFieldNumber;
const int DefaultValueTest::kBytesValueFieldNumber;
const int DefaultValueTest::kEnumValueFieldNumber;
#endif  // !defined(_MSC_VER) || _MSC_VER >= 1900

DefaultValueTest::DefaultValueTest()
  : ::google::protobuf::Message(), _internal_metadata_(NULL) {
  if (this != internal_default_instance()) protobuf_InitDefaults_google_2fprotobuf_2futil_2finternal_2ftestdata_2fdefault_5fvalue_5ftest_2eproto();
  SharedCtor();
  // @@protoc_insertion_point(constructor:google.protobuf.testing.DefaultValueTest)
}

void DefaultValueTest::InitAsDefaultInstance() {
}

DefaultValueTest::DefaultValueTest(const DefaultValueTest& from)
  : ::google::protobuf::Message(),
    _internal_metadata_(NULL) {
  SharedCtor();
  UnsafeMergeFrom(from);
  // @@protoc_insertion_point(copy_constructor:google.protobuf.testing.DefaultValueTest)
}

void DefaultValueTest::SharedCtor() {
  string_value_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  bytes_value_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  ::memset(&double_value_, 0, reinterpret_cast<char*>(&enum_value_) -
    reinterpret_cast<char*>(&double_value_) + sizeof(enum_value_));
  _cached_size_ = 0;
}

DefaultValueTest::~DefaultValueTest() {
  // @@protoc_insertion_point(destructor:google.protobuf.testing.DefaultValueTest)
  SharedDtor();
}

void DefaultValueTest::SharedDtor() {
  string_value_.DestroyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  bytes_value_.DestroyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}

void DefaultValueTest::SetCachedSize(int size) const {
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
}
const ::google::protobuf::Descriptor* DefaultValueTest::descriptor() {
  protobuf_AssignDescriptorsOnce();
  return DefaultValueTest_descriptor_;
}

const DefaultValueTest& DefaultValueTest::default_instance() {
  protobuf_InitDefaults_google_2fprotobuf_2futil_2finternal_2ftestdata_2fdefault_5fvalue_5ftest_2eproto();
  return *internal_default_instance();
}

::google::protobuf::internal::ExplicitlyConstructed<DefaultValueTest> DefaultValueTest_default_instance_;

DefaultValueTest* DefaultValueTest::New(::google::protobuf::Arena* arena) const {
  DefaultValueTest* n = new DefaultValueTest;
  if (arena != NULL) {
    arena->Own(n);
  }
  return n;
}

void DefaultValueTest::Clear() {
// @@protoc_insertion_point(message_clear_start:google.protobuf.testing.DefaultValueTest)
#if defined(__clang__)
#define ZR_HELPER_(f) \
  _Pragma("clang diagnostic push") \
  _Pragma("clang diagnostic ignored \"-Winvalid-offsetof\"") \
  __builtin_offsetof(DefaultValueTest, f) \
  _Pragma("clang diagnostic pop")
#else
#define ZR_HELPER_(f) reinterpret_cast<char*>(\
  &reinterpret_cast<DefaultValueTest*>(16)->f)
#endif

#define ZR_(first, last) do {\
  ::memset(&(first), 0,\
           ZR_HELPER_(last) - ZR_HELPER_(first) + sizeof(last));\
} while (0)

  ZR_(double_value_, bool_value_);
  string_value_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  bytes_value_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  enum_value_ = 0;

#undef ZR_HELPER_
#undef ZR_

  repeated_double_.Clear();
}

bool DefaultValueTest::MergePartialFromCodedStream(
    ::google::protobuf::io::CodedInputStream* input) {
#define DO_(EXPRESSION) if (!GOOGLE_PREDICT_TRUE(EXPRESSION)) goto failure
  ::google::protobuf::uint32 tag;
  // @@protoc_insertion_point(parse_start:google.protobuf.testing.DefaultValueTest)
  for (;;) {
    ::std::pair< ::google::protobuf::uint32, bool> p = input->ReadTagWithCutoff(16383);
    tag = p.first;
    if (!p.second) goto handle_unusual;
    switch (::google::protobuf::internal::WireFormatLite::GetTagFieldNumber(tag)) {
      // optional double double_value = 1;
      case 1: {
        if (tag == 9) {

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   double, ::google::protobuf::internal::WireFormatLite::TYPE_DOUBLE>(
                 input, &double_value_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(18)) goto parse_repeated_double;
        break;
      }

      // repeated double repeated_double = 2;
      case 2: {
        if (tag == 18) {
         parse_repeated_double:
          DO_((::google::protobuf::internal::WireFormatLite::ReadPackedPrimitive<
                   double, ::google::protobuf::internal::WireFormatLite::TYPE_DOUBLE>(
                 input, this->mutable_repeated_double())));
        } else if (tag == 17) {
          DO_((::google::protobuf::internal::WireFormatLite::ReadRepeatedPrimitiveNoInline<
                   double, ::google::protobuf::internal::WireFormatLite::TYPE_DOUBLE>(
                 1, 18, input, this->mutable_repeated_double())));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(29)) goto parse_float_value;
        break;
      }

      // optional float float_value = 3;
      case 3: {
        if (tag == 29) {
         parse_float_value:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   float, ::google::protobuf::internal::WireFormatLite::TYPE_FLOAT>(
                 input, &float_value_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(40)) goto parse_int64_value;
        break;
      }

      // optional int64 int64_value = 5;
      case 5: {
        if (tag == 40) {
         parse_int64_value:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_INT64>(
                 input, &int64_value_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(56)) goto parse_uint64_value;
        break;
      }

      // optional uint64 uint64_value = 7;
      case 7: {
        if (tag == 56) {
         parse_uint64_value:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::uint64, ::google::protobuf::internal::WireFormatLite::TYPE_UINT64>(
                 input, &uint64_value_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(72)) goto parse_int32_value;
        break;
      }

      // optional int32 int32_value = 9;
      case 9: {
        if (tag == 72) {
         parse_int32_value:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int32, ::google::protobuf::internal::WireFormatLite::TYPE_INT32>(
                 input, &int32_value_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(88)) goto parse_uint32_value;
        break;
      }

      // optional uint32 uint32_value = 11;
      case 11: {
        if (tag == 88) {
         parse_uint32_value:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::uint32, ::google::protobuf::internal::WireFormatLite::TYPE_UINT32>(
                 input, &uint32_value_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(104)) goto parse_bool_value;
        break;
      }

      // optional bool bool_value = 13;
      case 13: {
        if (tag == 104) {
         parse_bool_value:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   bool, ::google::protobuf::internal::WireFormatLite::TYPE_BOOL>(
                 input, &bool_value_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(122)) goto parse_string_value;
        break;
      }

      // optional string string_value = 15;
      case 15: {
        if (tag == 122) {
         parse_string_value:
          DO_(::google::protobuf::internal::WireFormatLite::ReadString(
                input, this->mutable_string_value()));
          DO_(::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
            this->string_value().data(), this->string_value().length(),
            ::google::protobuf::internal::WireFormatLite::PARSE,
            "google.protobuf.testing.DefaultValueTest.string_value"));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(138)) goto parse_bytes_value;
        break;
      }

      // optional bytes bytes_value = 17 [ctype = CORD];
      case 17: {
        if (tag == 138) {
         parse_bytes_value:
          DO_(::google::protobuf::internal::WireFormatLite::ReadBytes(
                input, this->mutable_bytes_value()));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(144)) goto parse_enum_value;
        break;
      }

      // optional .google.protobuf.testing.DefaultValueTest.EnumDefault enum_value = 18;
      case 18: {
        if (tag == 144) {
         parse_enum_value:
          int value;
          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   int, ::google::protobuf::internal::WireFormatLite::TYPE_ENUM>(
                 input, &value)));
          set_enum_value(static_cast< ::google::protobuf::testing::DefaultValueTest_EnumDefault >(value));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectAtEnd()) goto success;
        break;
      }

      default: {
      handle_unusual:
        if (tag == 0 ||
            ::google::protobuf::internal::WireFormatLite::GetTagWireType(tag) ==
            ::google::protobuf::internal::WireFormatLite::WIRETYPE_END_GROUP) {
          goto success;
        }
        DO_(::google::protobuf::internal::WireFormatLite::SkipField(input, tag));
        break;
      }
    }
  }
success:
  // @@protoc_insertion_point(parse_success:google.protobuf.testing.DefaultValueTest)
  return true;
failure:
  // @@protoc_insertion_point(parse_failure:google.protobuf.testing.DefaultValueTest)
  return false;
#undef DO_
}

void DefaultValueTest::SerializeWithCachedSizes(
    ::google::protobuf::io::CodedOutputStream* output) const {
  // @@protoc_insertion_point(serialize_start:google.protobuf.testing.DefaultValueTest)
  // optional double double_value = 1;
  if (this->double_value() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteDouble(1, this->double_value(), output);
  }

  // repeated double repeated_double = 2;
  if (this->repeated_double_size() > 0) {
    ::google::protobuf::internal::WireFormatLite::WriteTag(2, ::google::protobuf::internal::WireFormatLite::WIRETYPE_LENGTH_DELIMITED, output);
    output->WriteVarint32(_repeated_double_cached_byte_size_);
  }
  for (int i = 0; i < this->repeated_double_size(); i++) {
    ::google::protobuf::internal::WireFormatLite::WriteDoubleNoTag(
      this->repeated_double(i), output);
  }

  // optional float float_value = 3;
  if (this->float_value() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteFloat(3, this->float_value(), output);
  }

  // optional int64 int64_value = 5;
  if (this->int64_value() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt64(5, this->int64_value(), output);
  }

  // optional uint64 uint64_value = 7;
  if (this->uint64_value() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteUInt64(7, this->uint64_value(), output);
  }

  // optional int32 int32_value = 9;
  if (this->int32_value() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt32(9, this->int32_value(), output);
  }

  // optional uint32 uint32_value = 11;
  if (this->uint32_value() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteUInt32(11, this->uint32_value(), output);
  }

  // optional bool bool_value = 13;
  if (this->bool_value() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteBool(13, this->bool_value(), output);
  }

  // optional string string_value = 15;
  if (this->string_value().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->string_value().data(), this->string_value().length(),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "google.protobuf.testing.DefaultValueTest.string_value");
    ::google::protobuf::internal::WireFormatLite::WriteStringMaybeAliased(
      15, this->string_value(), output);
  }

  // optional bytes bytes_value = 17 [ctype = CORD];
  if (this->bytes_value().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::WriteBytesMaybeAliased(
      17, this->bytes_value(), output);
  }

  // optional .google.protobuf.testing.DefaultValueTest.EnumDefault enum_value = 18;
  if (this->enum_value() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteEnum(
      18, this->enum_value(), output);
  }

  // @@protoc_insertion_point(serialize_end:google.protobuf.testing.DefaultValueTest)
}

::google::protobuf::uint8* DefaultValueTest::InternalSerializeWithCachedSizesToArray(
    bool deterministic, ::google::protobuf::uint8* target) const {
  (void)deterministic; // Unused
  // @@protoc_insertion_point(serialize_to_array_start:google.protobuf.testing.DefaultValueTest)
  // optional double double_value = 1;
  if (this->double_value() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteDoubleToArray(1, this->double_value(), target);
  }

  // repeated double repeated_double = 2;
  if (this->repeated_double_size() > 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteTagToArray(
      2,
      ::google::protobuf::internal::WireFormatLite::WIRETYPE_LENGTH_DELIMITED,
      target);
    target = ::google::protobuf::io::CodedOutputStream::WriteVarint32ToArray(
      _repeated_double_cached_byte_size_, target);
  }
  for (int i = 0; i < this->repeated_double_size(); i++) {
    target = ::google::protobuf::internal::WireFormatLite::
      WriteDoubleNoTagToArray(this->repeated_double(i), target);
  }

  // optional float float_value = 3;
  if (this->float_value() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteFloatToArray(3, this->float_value(), target);
  }

  // optional int64 int64_value = 5;
  if (this->int64_value() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt64ToArray(5, this->int64_value(), target);
  }

  // optional uint64 uint64_value = 7;
  if (this->uint64_value() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteUInt64ToArray(7, this->uint64_value(), target);
  }

  // optional int32 int32_value = 9;
  if (this->int32_value() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt32ToArray(9, this->int32_value(), target);
  }

  // optional uint32 uint32_value = 11;
  if (this->uint32_value() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteUInt32ToArray(11, this->uint32_value(), target);
  }

  // optional bool bool_value = 13;
  if (this->bool_value() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteBoolToArray(13, this->bool_value(), target);
  }

  // optional string string_value = 15;
  if (this->string_value().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->string_value().data(), this->string_value().length(),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "google.protobuf.testing.DefaultValueTest.string_value");
    target =
      ::google::protobuf::internal::WireFormatLite::WriteStringToArray(
        15, this->string_value(), target);
  }

  // optional bytes bytes_value = 17 [ctype = CORD];
  if (this->bytes_value().size() > 0) {
    target =
      ::google::protobuf::internal::WireFormatLite::WriteBytesToArray(
        17, this->bytes_value(), target);
  }

  // optional .google.protobuf.testing.DefaultValueTest.EnumDefault enum_value = 18;
  if (this->enum_value() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteEnumToArray(
      18, this->enum_value(), target);
  }

  // @@protoc_insertion_point(serialize_to_array_end:google.protobuf.testing.DefaultValueTest)
  return target;
}

size_t DefaultValueTest::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:google.protobuf.testing.DefaultValueTest)
  size_t total_size = 0;

  // optional double double_value = 1;
  if (this->double_value() != 0) {
    total_size += 1 + 8;
  }

  // optional float float_value = 3;
  if (this->float_value() != 0) {
    total_size += 1 + 4;
  }

  // optional int64 int64_value = 5;
  if (this->int64_value() != 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::Int64Size(
        this->int64_value());
  }

  // optional uint64 uint64_value = 7;
  if (this->uint64_value() != 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::UInt64Size(
        this->uint64_value());
  }

  // optional int32 int32_value = 9;
  if (this->int32_value() != 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::Int32Size(
        this->int32_value());
  }

  // optional uint32 uint32_value = 11;
  if (this->uint32_value() != 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::UInt32Size(
        this->uint32_value());
  }

  // optional bool bool_value = 13;
  if (this->bool_value() != 0) {
    total_size += 1 + 1;
  }

  // optional string string_value = 15;
  if (this->string_value().size() > 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::StringSize(
        this->string_value());
  }

  // optional bytes bytes_value = 17 [ctype = CORD];
  if (this->bytes_value().size() > 0) {
    total_size += 2 +
      ::google::protobuf::internal::WireFormatLite::BytesSize(
        this->bytes_value());
  }

  // optional .google.protobuf.testing.DefaultValueTest.EnumDefault enum_value = 18;
  if (this->enum_value() != 0) {
    total_size += 2 +
      ::google::protobuf::internal::WireFormatLite::EnumSize(this->enum_value());
  }

  // repeated double repeated_double = 2;
  {
    size_t data_size = 0;
    unsigned int count = this->repeated_double_size();
    data_size = 8UL * count;
    if (data_size > 0) {
      total_size += 1 +
        ::google::protobuf::internal::WireFormatLite::Int32Size(data_size);
    }
    int cached_size = ::google::protobuf::internal::ToCachedSize(data_size);
    GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
    _repeated_double_cached_byte_size_ = cached_size;
    GOOGLE_SAFE_CONCURRENT_WRITES_END();
    total_size += data_size;
  }

  int cached_size = ::google::protobuf::internal::ToCachedSize(total_size);
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = cached_size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
  return total_size;
}

void DefaultValueTest::MergeFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_merge_from_start:google.protobuf.testing.DefaultValueTest)
  if (GOOGLE_PREDICT_FALSE(&from == this)) MergeFromFail(__LINE__);
  const DefaultValueTest* source =
      ::google::protobuf::internal::DynamicCastToGenerated<const DefaultValueTest>(
          &from);
  if (source == NULL) {
  // @@protoc_insertion_point(generalized_merge_from_cast_fail:google.protobuf.testing.DefaultValueTest)
    ::google::protobuf::internal::ReflectionOps::Merge(from, this);
  } else {
  // @@protoc_insertion_point(generalized_merge_from_cast_success:google.protobuf.testing.DefaultValueTest)
    UnsafeMergeFrom(*source);
  }
}

void DefaultValueTest::MergeFrom(const DefaultValueTest& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:google.protobuf.testing.DefaultValueTest)
  if (GOOGLE_PREDICT_TRUE(&from != this)) {
    UnsafeMergeFrom(from);
  } else {
    MergeFromFail(__LINE__);
  }
}

void DefaultValueTest::UnsafeMergeFrom(const DefaultValueTest& from) {
  GOOGLE_DCHECK(&from != this);
  repeated_double_.UnsafeMergeFrom(from.repeated_double_);
  if (from.double_value() != 0) {
    set_double_value(from.double_value());
  }
  if (from.float_value() != 0) {
    set_float_value(from.float_value());
  }
  if (from.int64_value() != 0) {
    set_int64_value(from.int64_value());
  }
  if (from.uint64_value() != 0) {
    set_uint64_value(from.uint64_value());
  }
  if (from.int32_value() != 0) {
    set_int32_value(from.int32_value());
  }
  if (from.uint32_value() != 0) {
    set_uint32_value(from.uint32_value());
  }
  if (from.bool_value() != 0) {
    set_bool_value(from.bool_value());
  }
  if (from.string_value().size() > 0) {

    string_value_.AssignWithDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), from.string_value_);
  }
  if (from.bytes_value().size() > 0) {

    bytes_value_.AssignWithDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), from.bytes_value_);
  }
  if (from.enum_value() != 0) {
    set_enum_value(from.enum_value());
  }
}

void DefaultValueTest::CopyFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_copy_from_start:google.protobuf.testing.DefaultValueTest)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

void DefaultValueTest::CopyFrom(const DefaultValueTest& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:google.protobuf.testing.DefaultValueTest)
  if (&from == this) return;
  Clear();
  UnsafeMergeFrom(from);
}

bool DefaultValueTest::IsInitialized() const {

  return true;
}

void DefaultValueTest::Swap(DefaultValueTest* other) {
  if (other == this) return;
  InternalSwap(other);
}
void DefaultValueTest::InternalSwap(DefaultValueTest* other) {
  std::swap(double_value_, other->double_value_);
  repeated_double_.UnsafeArenaSwap(&other->repeated_double_);
  std::swap(float_value_, other->float_value_);
  std::swap(int64_value_, other->int64_value_);
  std::swap(uint64_value_, other->uint64_value_);
  std::swap(int32_value_, other->int32_value_);
  std::swap(uint32_value_, other->uint32_value_);
  std::swap(bool_value_, other->bool_value_);
  string_value_.Swap(&other->string_value_);
  bytes_value_.Swap(&other->bytes_value_);
  std::swap(enum_value_, other->enum_value_);
  _internal_metadata_.Swap(&other->_internal_metadata_);
  std::swap(_cached_size_, other->_cached_size_);
}

::google::protobuf::Metadata DefaultValueTest::GetMetadata() const {
  protobuf_AssignDescriptorsOnce();
  ::google::protobuf::Metadata metadata;
  metadata.descriptor = DefaultValueTest_descriptor_;
  metadata.reflection = DefaultValueTest_reflection_;
  return metadata;
}

#if PROTOBUF_INLINE_NOT_IN_HEADERS
// DefaultValueTest

// optional double double_value = 1;
void DefaultValueTest::clear_double_value() {
  double_value_ = 0;
}
double DefaultValueTest::double_value() const {
  // @@protoc_insertion_point(field_get:google.protobuf.testing.DefaultValueTest.double_value)
  return double_value_;
}
void DefaultValueTest::set_double_value(double value) {
  
  double_value_ = value;
  // @@protoc_insertion_point(field_set:google.protobuf.testing.DefaultValueTest.double_value)
}

// repeated double repeated_double = 2;
int DefaultValueTest::repeated_double_size() const {
  return repeated_double_.size();
}
void DefaultValueTest::clear_repeated_double() {
  repeated_double_.Clear();
}
double DefaultValueTest::repeated_double(int index) const {
  // @@protoc_insertion_point(field_get:google.protobuf.testing.DefaultValueTest.repeated_double)
  return repeated_double_.Get(index);
}
void DefaultValueTest::set_repeated_double(int index, double value) {
  repeated_double_.Set(index, value);
  // @@protoc_insertion_point(field_set:google.protobuf.testing.DefaultValueTest.repeated_double)
}
void DefaultValueTest::add_repeated_double(double value) {
  repeated_double_.Add(value);
  // @@protoc_insertion_point(field_add:google.protobuf.testing.DefaultValueTest.repeated_double)
}
const ::google::protobuf::RepeatedField< double >&
DefaultValueTest::repeated_double() const {
  // @@protoc_insertion_point(field_list:google.protobuf.testing.DefaultValueTest.repeated_double)
  return repeated_double_;
}
::google::protobuf::RepeatedField< double >*
DefaultValueTest::mutable_repeated_double() {
  // @@protoc_insertion_point(field_mutable_list:google.protobuf.testing.DefaultValueTest.repeated_double)
  return &repeated_double_;
}

// optional float float_value = 3;
void DefaultValueTest::clear_float_value() {
  float_value_ = 0;
}
float DefaultValueTest::float_value() const {
  // @@protoc_insertion_point(field_get:google.protobuf.testing.DefaultValueTest.float_value)
  return float_value_;
}
void DefaultValueTest::set_float_value(float value) {
  
  float_value_ = value;
  // @@protoc_insertion_point(field_set:google.protobuf.testing.DefaultValueTest.float_value)
}

// optional int64 int64_value = 5;
void DefaultValueTest::clear_int64_value() {
  int64_value_ = GOOGLE_LONGLONG(0);
}
::google::protobuf::int64 DefaultValueTest::int64_value() const {
  // @@protoc_insertion_point(field_get:google.protobuf.testing.DefaultValueTest.int64_value)
  return int64_value_;
}
void DefaultValueTest::set_int64_value(::google::protobuf::int64 value) {
  
  int64_value_ = value;
  // @@protoc_insertion_point(field_set:google.protobuf.testing.DefaultValueTest.int64_value)
}

// optional uint64 uint64_value = 7;
void DefaultValueTest::clear_uint64_value() {
  uint64_value_ = GOOGLE_ULONGLONG(0);
}
::google::protobuf::uint64 DefaultValueTest::uint64_value() const {
  // @@protoc_insertion_point(field_get:google.protobuf.testing.DefaultValueTest.uint64_value)
  return uint64_value_;
}
void DefaultValueTest::set_uint64_value(::google::protobuf::uint64 value) {
  
  uint64_value_ = value;
  // @@protoc_insertion_point(field_set:google.protobuf.testing.DefaultValueTest.uint64_value)
}

// optional int32 int32_value = 9;
void DefaultValueTest::clear_int32_value() {
  int32_value_ = 0;
}
::google::protobuf::int32 DefaultValueTest::int32_value() const {
  // @@protoc_insertion_point(field_get:google.protobuf.testing.DefaultValueTest.int32_value)
  return int32_value_;
}
void DefaultValueTest::set_int32_value(::google::protobuf::int32 value) {
  
  int32_value_ = value;
  // @@protoc_insertion_point(field_set:google.protobuf.testing.DefaultValueTest.int32_value)
}

// optional uint32 uint32_value = 11;
void DefaultValueTest::clear_uint32_value() {
  uint32_value_ = 0u;
}
::google::protobuf::uint32 DefaultValueTest::uint32_value() const {
  // @@protoc_insertion_point(field_get:google.protobuf.testing.DefaultValueTest.uint32_value)
  return uint32_value_;
}
void DefaultValueTest::set_uint32_value(::google::protobuf::uint32 value) {
  
  uint32_value_ = value;
  // @@protoc_insertion_point(field_set:google.protobuf.testing.DefaultValueTest.uint32_value)
}

// optional bool bool_value = 13;
void DefaultValueTest::clear_bool_value() {
  bool_value_ = false;
}
bool DefaultValueTest::bool_value() const {
  // @@protoc_insertion_point(field_get:google.protobuf.testing.DefaultValueTest.bool_value)
  return bool_value_;
}
void DefaultValueTest::set_bool_value(bool value) {
  
  bool_value_ = value;
  // @@protoc_insertion_point(field_set:google.protobuf.testing.DefaultValueTest.bool_value)
}

// optional string string_value = 15;
void DefaultValueTest::clear_string_value() {
  string_value_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
const ::std::string& DefaultValueTest::string_value() const {
  // @@protoc_insertion_point(field_get:google.protobuf.testing.DefaultValueTest.string_value)
  return string_value_.GetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
void DefaultValueTest::set_string_value(const ::std::string& value) {
  
  string_value_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:google.protobuf.testing.DefaultValueTest.string_value)
}
void DefaultValueTest::set_string_value(const char* value) {
  
  string_value_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:google.protobuf.testing.DefaultValueTest.string_value)
}
void DefaultValueTest::set_string_value(const char* value, size_t size) {
  
  string_value_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:google.protobuf.testing.DefaultValueTest.string_value)
}
::std::string* DefaultValueTest::mutable_string_value() {
  
  // @@protoc_insertion_point(field_mutable:google.protobuf.testing.DefaultValueTest.string_value)
  return string_value_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
::std::string* DefaultValueTest::release_string_value() {
  // @@protoc_insertion_point(field_release:google.protobuf.testing.DefaultValueTest.string_value)
  
  return string_value_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
void DefaultValueTest::set_allocated_string_value(::std::string* string_value) {
  if (string_value != NULL) {
    
  } else {
    
  }
  string_value_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), string_value);
  // @@protoc_insertion_point(field_set_allocated:google.protobuf.testing.DefaultValueTest.string_value)
}

// optional bytes bytes_value = 17 [ctype = CORD];
void DefaultValueTest::clear_bytes_value() {
  bytes_value_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
const ::std::string& DefaultValueTest::bytes_value() const {
  // @@protoc_insertion_point(field_get:google.protobuf.testing.DefaultValueTest.bytes_value)
  return bytes_value_.GetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
void DefaultValueTest::set_bytes_value(const ::std::string& value) {
  
  bytes_value_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:google.protobuf.testing.DefaultValueTest.bytes_value)
}
void DefaultValueTest::set_bytes_value(const char* value) {
  
  bytes_value_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:google.protobuf.testing.DefaultValueTest.bytes_value)
}
void DefaultValueTest::set_bytes_value(const void* value, size_t size) {
  
  bytes_value_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:google.protobuf.testing.DefaultValueTest.bytes_value)
}
::std::string* DefaultValueTest::mutable_bytes_value() {
  
  // @@protoc_insertion_point(field_mutable:google.protobuf.testing.DefaultValueTest.bytes_value)
  return bytes_value_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
::std::string* DefaultValueTest::release_bytes_value() {
  // @@protoc_insertion_point(field_release:google.protobuf.testing.DefaultValueTest.bytes_value)
  
  return bytes_value_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
void DefaultValueTest::set_allocated_bytes_value(::std::string* bytes_value) {
  if (bytes_value != NULL) {
    
  } else {
    
  }
  bytes_value_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), bytes_value);
  // @@protoc_insertion_point(field_set_allocated:google.protobuf.testing.DefaultValueTest.bytes_value)
}

// optional .google.protobuf.testing.DefaultValueTest.EnumDefault enum_value = 18;
void DefaultValueTest::clear_enum_value() {
  enum_value_ = 0;
}
::google::protobuf::testing::DefaultValueTest_EnumDefault DefaultValueTest::enum_value() const {
  // @@protoc_insertion_point(field_get:google.protobuf.testing.DefaultValueTest.enum_value)
  return static_cast< ::google::protobuf::testing::DefaultValueTest_EnumDefault >(enum_value_);
}
void DefaultValueTest::set_enum_value(::google::protobuf::testing::DefaultValueTest_EnumDefault value) {
  
  enum_value_ = value;
  // @@protoc_insertion_point(field_set:google.protobuf.testing.DefaultValueTest.enum_value)
}

inline const DefaultValueTest* DefaultValueTest::internal_default_instance() {
  return &DefaultValueTest_default_instance_.get();
}
#endif  // PROTOBUF_INLINE_NOT_IN_HEADERS

// @@protoc_insertion_point(namespace_scope)

}  // namespace testing
}  // namespace protobuf
}  // namespace google

// @@protoc_insertion_point(global_scope)

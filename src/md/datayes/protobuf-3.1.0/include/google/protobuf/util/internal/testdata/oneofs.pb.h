// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: google/protobuf/util/internal/testdata/oneofs.proto

#ifndef PROTOBUF_google_2fprotobuf_2futil_2finternal_2ftestdata_2foneofs_2eproto__INCLUDED
#define PROTOBUF_google_2fprotobuf_2futil_2finternal_2ftestdata_2foneofs_2eproto__INCLUDED

#include <string>

#include <google/protobuf/stubs/common.h>

#if GOOGLE_PROTOBUF_VERSION < 3001000
#error This file was generated by a newer version of protoc which is
#error incompatible with your Protocol Buffer headers.  Please update
#error your headers.
#endif
#if 3001000 < GOOGLE_PROTOBUF_MIN_PROTOC_VERSION
#error This file was generated by an older version of protoc which is
#error incompatible with your Protocol Buffer headers.  Please
#error regenerate this file with a newer version of protoc.
#endif

#include <google/protobuf/arena.h>
#include <google/protobuf/arenastring.h>
#include <google/protobuf/generated_message_util.h>
#include <google/protobuf/metadata.h>
#include <google/protobuf/message.h>
#include <google/protobuf/repeated_field.h>
#include <google/protobuf/extension_set.h>
#include <google/protobuf/unknown_field_set.h>
#include <google/protobuf/any.pb.h>
#include <google/protobuf/struct.pb.h>
#include <google/protobuf/timestamp.pb.h>
// @@protoc_insertion_point(includes)

namespace google {
namespace protobuf {
namespace testing {
namespace oneofs {

// Internal implementation detail -- do not call these.
void protobuf_AddDesc_google_2fprotobuf_2futil_2finternal_2ftestdata_2foneofs_2eproto();
void protobuf_InitDefaults_google_2fprotobuf_2futil_2finternal_2ftestdata_2foneofs_2eproto();
void protobuf_AssignDesc_google_2fprotobuf_2futil_2finternal_2ftestdata_2foneofs_2eproto();
void protobuf_ShutdownFile_google_2fprotobuf_2futil_2finternal_2ftestdata_2foneofs_2eproto();

class Data;
class MoreData;
class OneOfsRequest;
class RequestWithSimpleOneof;
class Response;

// ===================================================================

class OneOfsRequest : public ::google::protobuf::Message /* @@protoc_insertion_point(class_definition:google.protobuf.testing.oneofs.OneOfsRequest) */ {
 public:
  OneOfsRequest();
  virtual ~OneOfsRequest();

  OneOfsRequest(const OneOfsRequest& from);

  inline OneOfsRequest& operator=(const OneOfsRequest& from) {
    CopyFrom(from);
    return *this;
  }

  static const ::google::protobuf::Descriptor* descriptor();
  static const OneOfsRequest& default_instance();

  enum DataCase {
    kStrData = 2,
    kIntData = 3,
    kMessageData = 4,
    kMoreData = 5,
    kStructData = 6,
    kValueData = 7,
    kListValueData = 8,
    kTsData = 9,
    DATA_NOT_SET = 0,
  };

  static const OneOfsRequest* internal_default_instance();

  void Swap(OneOfsRequest* other);

  // implements Message ----------------------------------------------

  inline OneOfsRequest* New() const { return New(NULL); }

  OneOfsRequest* New(::google::protobuf::Arena* arena) const;
  void CopyFrom(const ::google::protobuf::Message& from);
  void MergeFrom(const ::google::protobuf::Message& from);
  void CopyFrom(const OneOfsRequest& from);
  void MergeFrom(const OneOfsRequest& from);
  void Clear();
  bool IsInitialized() const;

  size_t ByteSizeLong() const;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input);
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const;
  ::google::protobuf::uint8* InternalSerializeWithCachedSizesToArray(
      bool deterministic, ::google::protobuf::uint8* output) const;
  ::google::protobuf::uint8* SerializeWithCachedSizesToArray(::google::protobuf::uint8* output) const {
    return InternalSerializeWithCachedSizesToArray(false, output);
  }
  int GetCachedSize() const { return _cached_size_; }
  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const;
  void InternalSwap(OneOfsRequest* other);
  void UnsafeMergeFrom(const OneOfsRequest& from);
  private:
  inline ::google::protobuf::Arena* GetArenaNoVirtual() const {
    return _internal_metadata_.arena();
  }
  inline void* MaybeArenaPtr() const {
    return _internal_metadata_.raw_arena_ptr();
  }
  public:

  ::google::protobuf::Metadata GetMetadata() const;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // optional string value = 1;
  void clear_value();
  static const int kValueFieldNumber = 1;
  const ::std::string& value() const;
  void set_value(const ::std::string& value);
  void set_value(const char* value);
  void set_value(const char* value, size_t size);
  ::std::string* mutable_value();
  ::std::string* release_value();
  void set_allocated_value(::std::string* value);

  // optional string str_data = 2;
  private:
  bool has_str_data() const;
  public:
  void clear_str_data();
  static const int kStrDataFieldNumber = 2;
  const ::std::string& str_data() const;
  void set_str_data(const ::std::string& value);
  void set_str_data(const char* value);
  void set_str_data(const char* value, size_t size);
  ::std::string* mutable_str_data();
  ::std::string* release_str_data();
  void set_allocated_str_data(::std::string* str_data);

  // optional int32 int_data = 3;
  private:
  bool has_int_data() const;
  public:
  void clear_int_data();
  static const int kIntDataFieldNumber = 3;
  ::google::protobuf::int32 int_data() const;
  void set_int_data(::google::protobuf::int32 value);

  // optional .google.protobuf.testing.oneofs.Data message_data = 4;
  bool has_message_data() const;
  void clear_message_data();
  static const int kMessageDataFieldNumber = 4;
  const ::google::protobuf::testing::oneofs::Data& message_data() const;
  ::google::protobuf::testing::oneofs::Data* mutable_message_data();
  ::google::protobuf::testing::oneofs::Data* release_message_data();
  void set_allocated_message_data(::google::protobuf::testing::oneofs::Data* message_data);

  // optional .google.protobuf.testing.oneofs.MoreData more_data = 5;
  bool has_more_data() const;
  void clear_more_data();
  static const int kMoreDataFieldNumber = 5;
  const ::google::protobuf::testing::oneofs::MoreData& more_data() const;
  ::google::protobuf::testing::oneofs::MoreData* mutable_more_data();
  ::google::protobuf::testing::oneofs::MoreData* release_more_data();
  void set_allocated_more_data(::google::protobuf::testing::oneofs::MoreData* more_data);

  // optional .google.protobuf.Struct struct_data = 6;
  bool has_struct_data() const;
  void clear_struct_data();
  static const int kStructDataFieldNumber = 6;
  const ::google::protobuf::Struct& struct_data() const;
  ::google::protobuf::Struct* mutable_struct_data();
  ::google::protobuf::Struct* release_struct_data();
  void set_allocated_struct_data(::google::protobuf::Struct* struct_data);

  // optional .google.protobuf.Value value_data = 7;
  bool has_value_data() const;
  void clear_value_data();
  static const int kValueDataFieldNumber = 7;
  const ::google::protobuf::Value& value_data() const;
  ::google::protobuf::Value* mutable_value_data();
  ::google::protobuf::Value* release_value_data();
  void set_allocated_value_data(::google::protobuf::Value* value_data);

  // optional .google.protobuf.ListValue list_value_data = 8;
  bool has_list_value_data() const;
  void clear_list_value_data();
  static const int kListValueDataFieldNumber = 8;
  const ::google::protobuf::ListValue& list_value_data() const;
  ::google::protobuf::ListValue* mutable_list_value_data();
  ::google::protobuf::ListValue* release_list_value_data();
  void set_allocated_list_value_data(::google::protobuf::ListValue* list_value_data);

  // optional .google.protobuf.Timestamp ts_data = 9;
  bool has_ts_data() const;
  void clear_ts_data();
  static const int kTsDataFieldNumber = 9;
  const ::google::protobuf::Timestamp& ts_data() const;
  ::google::protobuf::Timestamp* mutable_ts_data();
  ::google::protobuf::Timestamp* release_ts_data();
  void set_allocated_ts_data(::google::protobuf::Timestamp* ts_data);

  // optional .google.protobuf.Any any_data = 19;
  bool has_any_data() const;
  void clear_any_data();
  static const int kAnyDataFieldNumber = 19;
  const ::google::protobuf::Any& any_data() const;
  ::google::protobuf::Any* mutable_any_data();
  ::google::protobuf::Any* release_any_data();
  void set_allocated_any_data(::google::protobuf::Any* any_data);

  DataCase data_case() const;
  // @@protoc_insertion_point(class_scope:google.protobuf.testing.oneofs.OneOfsRequest)
 private:
  inline void set_has_str_data();
  inline void set_has_int_data();
  inline void set_has_message_data();
  inline void set_has_more_data();
  inline void set_has_struct_data();
  inline void set_has_value_data();
  inline void set_has_list_value_data();
  inline void set_has_ts_data();

  inline bool has_data() const;
  void clear_data();
  inline void clear_has_data();

  ::google::protobuf::internal::InternalMetadataWithArena _internal_metadata_;
  ::google::protobuf::internal::ArenaStringPtr value_;
  ::google::protobuf::Any* any_data_;
  union DataUnion {
    DataUnion() {}
    ::google::protobuf::internal::ArenaStringPtr str_data_;
    ::google::protobuf::int32 int_data_;
    ::google::protobuf::testing::oneofs::Data* message_data_;
    ::google::protobuf::testing::oneofs::MoreData* more_data_;
    ::google::protobuf::Struct* struct_data_;
    ::google::protobuf::Value* value_data_;
    ::google::protobuf::ListValue* list_value_data_;
    ::google::protobuf::Timestamp* ts_data_;
  } data_;
  mutable int _cached_size_;
  ::google::protobuf::uint32 _oneof_case_[1];

  friend void  protobuf_InitDefaults_google_2fprotobuf_2futil_2finternal_2ftestdata_2foneofs_2eproto_impl();
  friend void  protobuf_AddDesc_google_2fprotobuf_2futil_2finternal_2ftestdata_2foneofs_2eproto_impl();
  friend void protobuf_AssignDesc_google_2fprotobuf_2futil_2finternal_2ftestdata_2foneofs_2eproto();
  friend void protobuf_ShutdownFile_google_2fprotobuf_2futil_2finternal_2ftestdata_2foneofs_2eproto();

  void InitAsDefaultInstance();
};
extern ::google::protobuf::internal::ExplicitlyConstructed<OneOfsRequest> OneOfsRequest_default_instance_;

// -------------------------------------------------------------------

class RequestWithSimpleOneof : public ::google::protobuf::Message /* @@protoc_insertion_point(class_definition:google.protobuf.testing.oneofs.RequestWithSimpleOneof) */ {
 public:
  RequestWithSimpleOneof();
  virtual ~RequestWithSimpleOneof();

  RequestWithSimpleOneof(const RequestWithSimpleOneof& from);

  inline RequestWithSimpleOneof& operator=(const RequestWithSimpleOneof& from) {
    CopyFrom(from);
    return *this;
  }

  static const ::google::protobuf::Descriptor* descriptor();
  static const RequestWithSimpleOneof& default_instance();

  enum DataCase {
    kStrData = 2,
    kIntData = 3,
    kMessageData = 4,
    kMoreData = 5,
    DATA_NOT_SET = 0,
  };

  static const RequestWithSimpleOneof* internal_default_instance();

  void Swap(RequestWithSimpleOneof* other);

  // implements Message ----------------------------------------------

  inline RequestWithSimpleOneof* New() const { return New(NULL); }

  RequestWithSimpleOneof* New(::google::protobuf::Arena* arena) const;
  void CopyFrom(const ::google::protobuf::Message& from);
  void MergeFrom(const ::google::protobuf::Message& from);
  void CopyFrom(const RequestWithSimpleOneof& from);
  void MergeFrom(const RequestWithSimpleOneof& from);
  void Clear();
  bool IsInitialized() const;

  size_t ByteSizeLong() const;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input);
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const;
  ::google::protobuf::uint8* InternalSerializeWithCachedSizesToArray(
      bool deterministic, ::google::protobuf::uint8* output) const;
  ::google::protobuf::uint8* SerializeWithCachedSizesToArray(::google::protobuf::uint8* output) const {
    return InternalSerializeWithCachedSizesToArray(false, output);
  }
  int GetCachedSize() const { return _cached_size_; }
  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const;
  void InternalSwap(RequestWithSimpleOneof* other);
  void UnsafeMergeFrom(const RequestWithSimpleOneof& from);
  private:
  inline ::google::protobuf::Arena* GetArenaNoVirtual() const {
    return _internal_metadata_.arena();
  }
  inline void* MaybeArenaPtr() const {
    return _internal_metadata_.raw_arena_ptr();
  }
  public:

  ::google::protobuf::Metadata GetMetadata() const;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // optional string value = 1;
  void clear_value();
  static const int kValueFieldNumber = 1;
  const ::std::string& value() const;
  void set_value(const ::std::string& value);
  void set_value(const char* value);
  void set_value(const char* value, size_t size);
  ::std::string* mutable_value();
  ::std::string* release_value();
  void set_allocated_value(::std::string* value);

  // optional string str_data = 2;
  private:
  bool has_str_data() const;
  public:
  void clear_str_data();
  static const int kStrDataFieldNumber = 2;
  const ::std::string& str_data() const;
  void set_str_data(const ::std::string& value);
  void set_str_data(const char* value);
  void set_str_data(const char* value, size_t size);
  ::std::string* mutable_str_data();
  ::std::string* release_str_data();
  void set_allocated_str_data(::std::string* str_data);

  // optional int32 int_data = 3;
  private:
  bool has_int_data() const;
  public:
  void clear_int_data();
  static const int kIntDataFieldNumber = 3;
  ::google::protobuf::int32 int_data() const;
  void set_int_data(::google::protobuf::int32 value);

  // optional .google.protobuf.testing.oneofs.Data message_data = 4;
  bool has_message_data() const;
  void clear_message_data();
  static const int kMessageDataFieldNumber = 4;
  const ::google::protobuf::testing::oneofs::Data& message_data() const;
  ::google::protobuf::testing::oneofs::Data* mutable_message_data();
  ::google::protobuf::testing::oneofs::Data* release_message_data();
  void set_allocated_message_data(::google::protobuf::testing::oneofs::Data* message_data);

  // optional .google.protobuf.testing.oneofs.MoreData more_data = 5;
  bool has_more_data() const;
  void clear_more_data();
  static const int kMoreDataFieldNumber = 5;
  const ::google::protobuf::testing::oneofs::MoreData& more_data() const;
  ::google::protobuf::testing::oneofs::MoreData* mutable_more_data();
  ::google::protobuf::testing::oneofs::MoreData* release_more_data();
  void set_allocated_more_data(::google::protobuf::testing::oneofs::MoreData* more_data);

  DataCase data_case() const;
  // @@protoc_insertion_point(class_scope:google.protobuf.testing.oneofs.RequestWithSimpleOneof)
 private:
  inline void set_has_str_data();
  inline void set_has_int_data();
  inline void set_has_message_data();
  inline void set_has_more_data();

  inline bool has_data() const;
  void clear_data();
  inline void clear_has_data();

  ::google::protobuf::internal::InternalMetadataWithArena _internal_metadata_;
  ::google::protobuf::internal::ArenaStringPtr value_;
  union DataUnion {
    DataUnion() {}
    ::google::protobuf::internal::ArenaStringPtr str_data_;
    ::google::protobuf::int32 int_data_;
    ::google::protobuf::testing::oneofs::Data* message_data_;
    ::google::protobuf::testing::oneofs::MoreData* more_data_;
  } data_;
  mutable int _cached_size_;
  ::google::protobuf::uint32 _oneof_case_[1];

  friend void  protobuf_InitDefaults_google_2fprotobuf_2futil_2finternal_2ftestdata_2foneofs_2eproto_impl();
  friend void  protobuf_AddDesc_google_2fprotobuf_2futil_2finternal_2ftestdata_2foneofs_2eproto_impl();
  friend void protobuf_AssignDesc_google_2fprotobuf_2futil_2finternal_2ftestdata_2foneofs_2eproto();
  friend void protobuf_ShutdownFile_google_2fprotobuf_2futil_2finternal_2ftestdata_2foneofs_2eproto();

  void InitAsDefaultInstance();
};
extern ::google::protobuf::internal::ExplicitlyConstructed<RequestWithSimpleOneof> RequestWithSimpleOneof_default_instance_;

// -------------------------------------------------------------------

class Data : public ::google::protobuf::Message /* @@protoc_insertion_point(class_definition:google.protobuf.testing.oneofs.Data) */ {
 public:
  Data();
  virtual ~Data();

  Data(const Data& from);

  inline Data& operator=(const Data& from) {
    CopyFrom(from);
    return *this;
  }

  static const ::google::protobuf::Descriptor* descriptor();
  static const Data& default_instance();

  static const Data* internal_default_instance();

  void Swap(Data* other);

  // implements Message ----------------------------------------------

  inline Data* New() const { return New(NULL); }

  Data* New(::google::protobuf::Arena* arena) const;
  void CopyFrom(const ::google::protobuf::Message& from);
  void MergeFrom(const ::google::protobuf::Message& from);
  void CopyFrom(const Data& from);
  void MergeFrom(const Data& from);
  void Clear();
  bool IsInitialized() const;

  size_t ByteSizeLong() const;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input);
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const;
  ::google::protobuf::uint8* InternalSerializeWithCachedSizesToArray(
      bool deterministic, ::google::protobuf::uint8* output) const;
  ::google::protobuf::uint8* SerializeWithCachedSizesToArray(::google::protobuf::uint8* output) const {
    return InternalSerializeWithCachedSizesToArray(false, output);
  }
  int GetCachedSize() const { return _cached_size_; }
  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const;
  void InternalSwap(Data* other);
  void UnsafeMergeFrom(const Data& from);
  private:
  inline ::google::protobuf::Arena* GetArenaNoVirtual() const {
    return _internal_metadata_.arena();
  }
  inline void* MaybeArenaPtr() const {
    return _internal_metadata_.raw_arena_ptr();
  }
  public:

  ::google::protobuf::Metadata GetMetadata() const;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // optional int32 data_value = 1;
  void clear_data_value();
  static const int kDataValueFieldNumber = 1;
  ::google::protobuf::int32 data_value() const;
  void set_data_value(::google::protobuf::int32 value);

  // @@protoc_insertion_point(class_scope:google.protobuf.testing.oneofs.Data)
 private:

  ::google::protobuf::internal::InternalMetadataWithArena _internal_metadata_;
  ::google::protobuf::int32 data_value_;
  mutable int _cached_size_;
  friend void  protobuf_InitDefaults_google_2fprotobuf_2futil_2finternal_2ftestdata_2foneofs_2eproto_impl();
  friend void  protobuf_AddDesc_google_2fprotobuf_2futil_2finternal_2ftestdata_2foneofs_2eproto_impl();
  friend void protobuf_AssignDesc_google_2fprotobuf_2futil_2finternal_2ftestdata_2foneofs_2eproto();
  friend void protobuf_ShutdownFile_google_2fprotobuf_2futil_2finternal_2ftestdata_2foneofs_2eproto();

  void InitAsDefaultInstance();
};
extern ::google::protobuf::internal::ExplicitlyConstructed<Data> Data_default_instance_;

// -------------------------------------------------------------------

class MoreData : public ::google::protobuf::Message /* @@protoc_insertion_point(class_definition:google.protobuf.testing.oneofs.MoreData) */ {
 public:
  MoreData();
  virtual ~MoreData();

  MoreData(const MoreData& from);

  inline MoreData& operator=(const MoreData& from) {
    CopyFrom(from);
    return *this;
  }

  static const ::google::protobuf::Descriptor* descriptor();
  static const MoreData& default_instance();

  static const MoreData* internal_default_instance();

  void Swap(MoreData* other);

  // implements Message ----------------------------------------------

  inline MoreData* New() const { return New(NULL); }

  MoreData* New(::google::protobuf::Arena* arena) const;
  void CopyFrom(const ::google::protobuf::Message& from);
  void MergeFrom(const ::google::protobuf::Message& from);
  void CopyFrom(const MoreData& from);
  void MergeFrom(const MoreData& from);
  void Clear();
  bool IsInitialized() const;

  size_t ByteSizeLong() const;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input);
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const;
  ::google::protobuf::uint8* InternalSerializeWithCachedSizesToArray(
      bool deterministic, ::google::protobuf::uint8* output) const;
  ::google::protobuf::uint8* SerializeWithCachedSizesToArray(::google::protobuf::uint8* output) const {
    return InternalSerializeWithCachedSizesToArray(false, output);
  }
  int GetCachedSize() const { return _cached_size_; }
  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const;
  void InternalSwap(MoreData* other);
  void UnsafeMergeFrom(const MoreData& from);
  private:
  inline ::google::protobuf::Arena* GetArenaNoVirtual() const {
    return _internal_metadata_.arena();
  }
  inline void* MaybeArenaPtr() const {
    return _internal_metadata_.raw_arena_ptr();
  }
  public:

  ::google::protobuf::Metadata GetMetadata() const;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // optional string str_value = 1;
  void clear_str_value();
  static const int kStrValueFieldNumber = 1;
  const ::std::string& str_value() const;
  void set_str_value(const ::std::string& value);
  void set_str_value(const char* value);
  void set_str_value(const char* value, size_t size);
  ::std::string* mutable_str_value();
  ::std::string* release_str_value();
  void set_allocated_str_value(::std::string* str_value);

  // @@protoc_insertion_point(class_scope:google.protobuf.testing.oneofs.MoreData)
 private:

  ::google::protobuf::internal::InternalMetadataWithArena _internal_metadata_;
  ::google::protobuf::internal::ArenaStringPtr str_value_;
  mutable int _cached_size_;
  friend void  protobuf_InitDefaults_google_2fprotobuf_2futil_2finternal_2ftestdata_2foneofs_2eproto_impl();
  friend void  protobuf_AddDesc_google_2fprotobuf_2futil_2finternal_2ftestdata_2foneofs_2eproto_impl();
  friend void protobuf_AssignDesc_google_2fprotobuf_2futil_2finternal_2ftestdata_2foneofs_2eproto();
  friend void protobuf_ShutdownFile_google_2fprotobuf_2futil_2finternal_2ftestdata_2foneofs_2eproto();

  void InitAsDefaultInstance();
};
extern ::google::protobuf::internal::ExplicitlyConstructed<MoreData> MoreData_default_instance_;

// -------------------------------------------------------------------

class Response : public ::google::protobuf::Message /* @@protoc_insertion_point(class_definition:google.protobuf.testing.oneofs.Response) */ {
 public:
  Response();
  virtual ~Response();

  Response(const Response& from);

  inline Response& operator=(const Response& from) {
    CopyFrom(from);
    return *this;
  }

  static const ::google::protobuf::Descriptor* descriptor();
  static const Response& default_instance();

  static const Response* internal_default_instance();

  void Swap(Response* other);

  // implements Message ----------------------------------------------

  inline Response* New() const { return New(NULL); }

  Response* New(::google::protobuf::Arena* arena) const;
  void CopyFrom(const ::google::protobuf::Message& from);
  void MergeFrom(const ::google::protobuf::Message& from);
  void CopyFrom(const Response& from);
  void MergeFrom(const Response& from);
  void Clear();
  bool IsInitialized() const;

  size_t ByteSizeLong() const;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input);
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const;
  ::google::protobuf::uint8* InternalSerializeWithCachedSizesToArray(
      bool deterministic, ::google::protobuf::uint8* output) const;
  ::google::protobuf::uint8* SerializeWithCachedSizesToArray(::google::protobuf::uint8* output) const {
    return InternalSerializeWithCachedSizesToArray(false, output);
  }
  int GetCachedSize() const { return _cached_size_; }
  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const;
  void InternalSwap(Response* other);
  void UnsafeMergeFrom(const Response& from);
  private:
  inline ::google::protobuf::Arena* GetArenaNoVirtual() const {
    return _internal_metadata_.arena();
  }
  inline void* MaybeArenaPtr() const {
    return _internal_metadata_.raw_arena_ptr();
  }
  public:

  ::google::protobuf::Metadata GetMetadata() const;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // optional string value = 1;
  void clear_value();
  static const int kValueFieldNumber = 1;
  const ::std::string& value() const;
  void set_value(const ::std::string& value);
  void set_value(const char* value);
  void set_value(const char* value, size_t size);
  ::std::string* mutable_value();
  ::std::string* release_value();
  void set_allocated_value(::std::string* value);

  // @@protoc_insertion_point(class_scope:google.protobuf.testing.oneofs.Response)
 private:

  ::google::protobuf::internal::InternalMetadataWithArena _internal_metadata_;
  ::google::protobuf::internal::ArenaStringPtr value_;
  mutable int _cached_size_;
  friend void  protobuf_InitDefaults_google_2fprotobuf_2futil_2finternal_2ftestdata_2foneofs_2eproto_impl();
  friend void  protobuf_AddDesc_google_2fprotobuf_2futil_2finternal_2ftestdata_2foneofs_2eproto_impl();
  friend void protobuf_AssignDesc_google_2fprotobuf_2futil_2finternal_2ftestdata_2foneofs_2eproto();
  friend void protobuf_ShutdownFile_google_2fprotobuf_2futil_2finternal_2ftestdata_2foneofs_2eproto();

  void InitAsDefaultInstance();
};
extern ::google::protobuf::internal::ExplicitlyConstructed<Response> Response_default_instance_;

// ===================================================================


// ===================================================================

#if !PROTOBUF_INLINE_NOT_IN_HEADERS
// OneOfsRequest

// optional string value = 1;
inline void OneOfsRequest::clear_value() {
  value_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline const ::std::string& OneOfsRequest::value() const {
  // @@protoc_insertion_point(field_get:google.protobuf.testing.oneofs.OneOfsRequest.value)
  return value_.GetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void OneOfsRequest::set_value(const ::std::string& value) {
  
  value_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:google.protobuf.testing.oneofs.OneOfsRequest.value)
}
inline void OneOfsRequest::set_value(const char* value) {
  
  value_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:google.protobuf.testing.oneofs.OneOfsRequest.value)
}
inline void OneOfsRequest::set_value(const char* value, size_t size) {
  
  value_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:google.protobuf.testing.oneofs.OneOfsRequest.value)
}
inline ::std::string* OneOfsRequest::mutable_value() {
  
  // @@protoc_insertion_point(field_mutable:google.protobuf.testing.oneofs.OneOfsRequest.value)
  return value_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline ::std::string* OneOfsRequest::release_value() {
  // @@protoc_insertion_point(field_release:google.protobuf.testing.oneofs.OneOfsRequest.value)
  
  return value_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void OneOfsRequest::set_allocated_value(::std::string* value) {
  if (value != NULL) {
    
  } else {
    
  }
  value_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set_allocated:google.protobuf.testing.oneofs.OneOfsRequest.value)
}

// optional string str_data = 2;
inline bool OneOfsRequest::has_str_data() const {
  return data_case() == kStrData;
}
inline void OneOfsRequest::set_has_str_data() {
  _oneof_case_[0] = kStrData;
}
inline void OneOfsRequest::clear_str_data() {
  if (has_str_data()) {
    data_.str_data_.DestroyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
    clear_has_data();
  }
}
inline const ::std::string& OneOfsRequest::str_data() const {
  // @@protoc_insertion_point(field_get:google.protobuf.testing.oneofs.OneOfsRequest.str_data)
  if (has_str_data()) {
    return data_.str_data_.GetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  }
  return *&::google::protobuf::internal::GetEmptyStringAlreadyInited();
}
inline void OneOfsRequest::set_str_data(const ::std::string& value) {
  // @@protoc_insertion_point(field_set:google.protobuf.testing.oneofs.OneOfsRequest.str_data)
  if (!has_str_data()) {
    clear_data();
    set_has_str_data();
    data_.str_data_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  }
  data_.str_data_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:google.protobuf.testing.oneofs.OneOfsRequest.str_data)
}
inline void OneOfsRequest::set_str_data(const char* value) {
  if (!has_str_data()) {
    clear_data();
    set_has_str_data();
    data_.str_data_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  }
  data_.str_data_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(value));
  // @@protoc_insertion_point(field_set_char:google.protobuf.testing.oneofs.OneOfsRequest.str_data)
}
inline void OneOfsRequest::set_str_data(const char* value, size_t size) {
  if (!has_str_data()) {
    clear_data();
    set_has_str_data();
    data_.str_data_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  }
  data_.str_data_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(
      reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:google.protobuf.testing.oneofs.OneOfsRequest.str_data)
}
inline ::std::string* OneOfsRequest::mutable_str_data() {
  if (!has_str_data()) {
    clear_data();
    set_has_str_data();
    data_.str_data_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  }
  // @@protoc_insertion_point(field_mutable:google.protobuf.testing.oneofs.OneOfsRequest.str_data)
  return data_.str_data_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline ::std::string* OneOfsRequest::release_str_data() {
  // @@protoc_insertion_point(field_release:google.protobuf.testing.oneofs.OneOfsRequest.str_data)
  if (has_str_data()) {
    clear_has_data();
    return data_.str_data_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  } else {
    return NULL;
  }
}
inline void OneOfsRequest::set_allocated_str_data(::std::string* str_data) {
  if (!has_str_data()) {
    data_.str_data_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  }
  clear_data();
  if (str_data != NULL) {
    set_has_str_data();
    data_.str_data_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
        str_data);
  }
  // @@protoc_insertion_point(field_set_allocated:google.protobuf.testing.oneofs.OneOfsRequest.str_data)
}

// optional int32 int_data = 3;
inline bool OneOfsRequest::has_int_data() const {
  return data_case() == kIntData;
}
inline void OneOfsRequest::set_has_int_data() {
  _oneof_case_[0] = kIntData;
}
inline void OneOfsRequest::clear_int_data() {
  if (has_int_data()) {
    data_.int_data_ = 0;
    clear_has_data();
  }
}
inline ::google::protobuf::int32 OneOfsRequest::int_data() const {
  // @@protoc_insertion_point(field_get:google.protobuf.testing.oneofs.OneOfsRequest.int_data)
  if (has_int_data()) {
    return data_.int_data_;
  }
  return 0;
}
inline void OneOfsRequest::set_int_data(::google::protobuf::int32 value) {
  if (!has_int_data()) {
    clear_data();
    set_has_int_data();
  }
  data_.int_data_ = value;
  // @@protoc_insertion_point(field_set:google.protobuf.testing.oneofs.OneOfsRequest.int_data)
}

// optional .google.protobuf.testing.oneofs.Data message_data = 4;
inline bool OneOfsRequest::has_message_data() const {
  return data_case() == kMessageData;
}
inline void OneOfsRequest::set_has_message_data() {
  _oneof_case_[0] = kMessageData;
}
inline void OneOfsRequest::clear_message_data() {
  if (has_message_data()) {
    delete data_.message_data_;
    clear_has_data();
  }
}
inline  const ::google::protobuf::testing::oneofs::Data& OneOfsRequest::message_data() const {
  // @@protoc_insertion_point(field_get:google.protobuf.testing.oneofs.OneOfsRequest.message_data)
  return has_message_data()
      ? *data_.message_data_
      : ::google::protobuf::testing::oneofs::Data::default_instance();
}
inline ::google::protobuf::testing::oneofs::Data* OneOfsRequest::mutable_message_data() {
  if (!has_message_data()) {
    clear_data();
    set_has_message_data();
    data_.message_data_ = new ::google::protobuf::testing::oneofs::Data;
  }
  // @@protoc_insertion_point(field_mutable:google.protobuf.testing.oneofs.OneOfsRequest.message_data)
  return data_.message_data_;
}
inline ::google::protobuf::testing::oneofs::Data* OneOfsRequest::release_message_data() {
  // @@protoc_insertion_point(field_release:google.protobuf.testing.oneofs.OneOfsRequest.message_data)
  if (has_message_data()) {
    clear_has_data();
    ::google::protobuf::testing::oneofs::Data* temp = data_.message_data_;
    data_.message_data_ = NULL;
    return temp;
  } else {
    return NULL;
  }
}
inline void OneOfsRequest::set_allocated_message_data(::google::protobuf::testing::oneofs::Data* message_data) {
  clear_data();
  if (message_data) {
    set_has_message_data();
    data_.message_data_ = message_data;
  }
  // @@protoc_insertion_point(field_set_allocated:google.protobuf.testing.oneofs.OneOfsRequest.message_data)
}

// optional .google.protobuf.testing.oneofs.MoreData more_data = 5;
inline bool OneOfsRequest::has_more_data() const {
  return data_case() == kMoreData;
}
inline void OneOfsRequest::set_has_more_data() {
  _oneof_case_[0] = kMoreData;
}
inline void OneOfsRequest::clear_more_data() {
  if (has_more_data()) {
    delete data_.more_data_;
    clear_has_data();
  }
}
inline  const ::google::protobuf::testing::oneofs::MoreData& OneOfsRequest::more_data() const {
  // @@protoc_insertion_point(field_get:google.protobuf.testing.oneofs.OneOfsRequest.more_data)
  return has_more_data()
      ? *data_.more_data_
      : ::google::protobuf::testing::oneofs::MoreData::default_instance();
}
inline ::google::protobuf::testing::oneofs::MoreData* OneOfsRequest::mutable_more_data() {
  if (!has_more_data()) {
    clear_data();
    set_has_more_data();
    data_.more_data_ = new ::google::protobuf::testing::oneofs::MoreData;
  }
  // @@protoc_insertion_point(field_mutable:google.protobuf.testing.oneofs.OneOfsRequest.more_data)
  return data_.more_data_;
}
inline ::google::protobuf::testing::oneofs::MoreData* OneOfsRequest::release_more_data() {
  // @@protoc_insertion_point(field_release:google.protobuf.testing.oneofs.OneOfsRequest.more_data)
  if (has_more_data()) {
    clear_has_data();
    ::google::protobuf::testing::oneofs::MoreData* temp = data_.more_data_;
    data_.more_data_ = NULL;
    return temp;
  } else {
    return NULL;
  }
}
inline void OneOfsRequest::set_allocated_more_data(::google::protobuf::testing::oneofs::MoreData* more_data) {
  clear_data();
  if (more_data) {
    set_has_more_data();
    data_.more_data_ = more_data;
  }
  // @@protoc_insertion_point(field_set_allocated:google.protobuf.testing.oneofs.OneOfsRequest.more_data)
}

// optional .google.protobuf.Struct struct_data = 6;
inline bool OneOfsRequest::has_struct_data() const {
  return data_case() == kStructData;
}
inline void OneOfsRequest::set_has_struct_data() {
  _oneof_case_[0] = kStructData;
}
inline void OneOfsRequest::clear_struct_data() {
  if (has_struct_data()) {
    delete data_.struct_data_;
    clear_has_data();
  }
}
inline  const ::google::protobuf::Struct& OneOfsRequest::struct_data() const {
  // @@protoc_insertion_point(field_get:google.protobuf.testing.oneofs.OneOfsRequest.struct_data)
  return has_struct_data()
      ? *data_.struct_data_
      : ::google::protobuf::Struct::default_instance();
}
inline ::google::protobuf::Struct* OneOfsRequest::mutable_struct_data() {
  if (!has_struct_data()) {
    clear_data();
    set_has_struct_data();
    data_.struct_data_ = new ::google::protobuf::Struct;
  }
  // @@protoc_insertion_point(field_mutable:google.protobuf.testing.oneofs.OneOfsRequest.struct_data)
  return data_.struct_data_;
}
inline ::google::protobuf::Struct* OneOfsRequest::release_struct_data() {
  // @@protoc_insertion_point(field_release:google.protobuf.testing.oneofs.OneOfsRequest.struct_data)
  if (has_struct_data()) {
    clear_has_data();
    ::google::protobuf::Struct* temp = data_.struct_data_;
    data_.struct_data_ = NULL;
    return temp;
  } else {
    return NULL;
  }
}
inline void OneOfsRequest::set_allocated_struct_data(::google::protobuf::Struct* struct_data) {
  clear_data();
  if (struct_data) {
    if (static_cast< ::google::protobuf::Struct*>(struct_data)->GetArena() != NULL) {
      ::google::protobuf::Struct* new_struct_data = new ::google::protobuf::Struct;
      new_struct_data->CopyFrom(*struct_data);
      struct_data = new_struct_data;
    }
    set_has_struct_data();
    data_.struct_data_ = struct_data;
  }
  // @@protoc_insertion_point(field_set_allocated:google.protobuf.testing.oneofs.OneOfsRequest.struct_data)
}

// optional .google.protobuf.Value value_data = 7;
inline bool OneOfsRequest::has_value_data() const {
  return data_case() == kValueData;
}
inline void OneOfsRequest::set_has_value_data() {
  _oneof_case_[0] = kValueData;
}
inline void OneOfsRequest::clear_value_data() {
  if (has_value_data()) {
    delete data_.value_data_;
    clear_has_data();
  }
}
inline  const ::google::protobuf::Value& OneOfsRequest::value_data() const {
  // @@protoc_insertion_point(field_get:google.protobuf.testing.oneofs.OneOfsRequest.value_data)
  return has_value_data()
      ? *data_.value_data_
      : ::google::protobuf::Value::default_instance();
}
inline ::google::protobuf::Value* OneOfsRequest::mutable_value_data() {
  if (!has_value_data()) {
    clear_data();
    set_has_value_data();
    data_.value_data_ = new ::google::protobuf::Value;
  }
  // @@protoc_insertion_point(field_mutable:google.protobuf.testing.oneofs.OneOfsRequest.value_data)
  return data_.value_data_;
}
inline ::google::protobuf::Value* OneOfsRequest::release_value_data() {
  // @@protoc_insertion_point(field_release:google.protobuf.testing.oneofs.OneOfsRequest.value_data)
  if (has_value_data()) {
    clear_has_data();
    ::google::protobuf::Value* temp = data_.value_data_;
    data_.value_data_ = NULL;
    return temp;
  } else {
    return NULL;
  }
}
inline void OneOfsRequest::set_allocated_value_data(::google::protobuf::Value* value_data) {
  clear_data();
  if (value_data) {
    if (static_cast< ::google::protobuf::Value*>(value_data)->GetArena() != NULL) {
      ::google::protobuf::Value* new_value_data = new ::google::protobuf::Value;
      new_value_data->CopyFrom(*value_data);
      value_data = new_value_data;
    }
    set_has_value_data();
    data_.value_data_ = value_data;
  }
  // @@protoc_insertion_point(field_set_allocated:google.protobuf.testing.oneofs.OneOfsRequest.value_data)
}

// optional .google.protobuf.ListValue list_value_data = 8;
inline bool OneOfsRequest::has_list_value_data() const {
  return data_case() == kListValueData;
}
inline void OneOfsRequest::set_has_list_value_data() {
  _oneof_case_[0] = kListValueData;
}
inline void OneOfsRequest::clear_list_value_data() {
  if (has_list_value_data()) {
    delete data_.list_value_data_;
    clear_has_data();
  }
}
inline  const ::google::protobuf::ListValue& OneOfsRequest::list_value_data() const {
  // @@protoc_insertion_point(field_get:google.protobuf.testing.oneofs.OneOfsRequest.list_value_data)
  return has_list_value_data()
      ? *data_.list_value_data_
      : ::google::protobuf::ListValue::default_instance();
}
inline ::google::protobuf::ListValue* OneOfsRequest::mutable_list_value_data() {
  if (!has_list_value_data()) {
    clear_data();
    set_has_list_value_data();
    data_.list_value_data_ = new ::google::protobuf::ListValue;
  }
  // @@protoc_insertion_point(field_mutable:google.protobuf.testing.oneofs.OneOfsRequest.list_value_data)
  return data_.list_value_data_;
}
inline ::google::protobuf::ListValue* OneOfsRequest::release_list_value_data() {
  // @@protoc_insertion_point(field_release:google.protobuf.testing.oneofs.OneOfsRequest.list_value_data)
  if (has_list_value_data()) {
    clear_has_data();
    ::google::protobuf::ListValue* temp = data_.list_value_data_;
    data_.list_value_data_ = NULL;
    return temp;
  } else {
    return NULL;
  }
}
inline void OneOfsRequest::set_allocated_list_value_data(::google::protobuf::ListValue* list_value_data) {
  clear_data();
  if (list_value_data) {
    if (static_cast< ::google::protobuf::ListValue*>(list_value_data)->GetArena() != NULL) {
      ::google::protobuf::ListValue* new_list_value_data = new ::google::protobuf::ListValue;
      new_list_value_data->CopyFrom(*list_value_data);
      list_value_data = new_list_value_data;
    }
    set_has_list_value_data();
    data_.list_value_data_ = list_value_data;
  }
  // @@protoc_insertion_point(field_set_allocated:google.protobuf.testing.oneofs.OneOfsRequest.list_value_data)
}

// optional .google.protobuf.Timestamp ts_data = 9;
inline bool OneOfsRequest::has_ts_data() const {
  return data_case() == kTsData;
}
inline void OneOfsRequest::set_has_ts_data() {
  _oneof_case_[0] = kTsData;
}
inline void OneOfsRequest::clear_ts_data() {
  if (has_ts_data()) {
    delete data_.ts_data_;
    clear_has_data();
  }
}
inline  const ::google::protobuf::Timestamp& OneOfsRequest::ts_data() const {
  // @@protoc_insertion_point(field_get:google.protobuf.testing.oneofs.OneOfsRequest.ts_data)
  return has_ts_data()
      ? *data_.ts_data_
      : ::google::protobuf::Timestamp::default_instance();
}
inline ::google::protobuf::Timestamp* OneOfsRequest::mutable_ts_data() {
  if (!has_ts_data()) {
    clear_data();
    set_has_ts_data();
    data_.ts_data_ = new ::google::protobuf::Timestamp;
  }
  // @@protoc_insertion_point(field_mutable:google.protobuf.testing.oneofs.OneOfsRequest.ts_data)
  return data_.ts_data_;
}
inline ::google::protobuf::Timestamp* OneOfsRequest::release_ts_data() {
  // @@protoc_insertion_point(field_release:google.protobuf.testing.oneofs.OneOfsRequest.ts_data)
  if (has_ts_data()) {
    clear_has_data();
    ::google::protobuf::Timestamp* temp = data_.ts_data_;
    data_.ts_data_ = NULL;
    return temp;
  } else {
    return NULL;
  }
}
inline void OneOfsRequest::set_allocated_ts_data(::google::protobuf::Timestamp* ts_data) {
  clear_data();
  if (ts_data) {
    if (static_cast< ::google::protobuf::Timestamp*>(ts_data)->GetArena() != NULL) {
      ::google::protobuf::Timestamp* new_ts_data = new ::google::protobuf::Timestamp;
      new_ts_data->CopyFrom(*ts_data);
      ts_data = new_ts_data;
    }
    set_has_ts_data();
    data_.ts_data_ = ts_data;
  }
  // @@protoc_insertion_point(field_set_allocated:google.protobuf.testing.oneofs.OneOfsRequest.ts_data)
}

// optional .google.protobuf.Any any_data = 19;
inline bool OneOfsRequest::has_any_data() const {
  return this != internal_default_instance() && any_data_ != NULL;
}
inline void OneOfsRequest::clear_any_data() {
  if (GetArenaNoVirtual() == NULL && any_data_ != NULL) delete any_data_;
  any_data_ = NULL;
}
inline const ::google::protobuf::Any& OneOfsRequest::any_data() const {
  // @@protoc_insertion_point(field_get:google.protobuf.testing.oneofs.OneOfsRequest.any_data)
  return any_data_ != NULL ? *any_data_
                         : *::google::protobuf::Any::internal_default_instance();
}
inline ::google::protobuf::Any* OneOfsRequest::mutable_any_data() {
  
  if (any_data_ == NULL) {
    any_data_ = new ::google::protobuf::Any;
  }
  // @@protoc_insertion_point(field_mutable:google.protobuf.testing.oneofs.OneOfsRequest.any_data)
  return any_data_;
}
inline ::google::protobuf::Any* OneOfsRequest::release_any_data() {
  // @@protoc_insertion_point(field_release:google.protobuf.testing.oneofs.OneOfsRequest.any_data)
  
  ::google::protobuf::Any* temp = any_data_;
  any_data_ = NULL;
  return temp;
}
inline void OneOfsRequest::set_allocated_any_data(::google::protobuf::Any* any_data) {
  delete any_data_;
  any_data_ = any_data;
  if (any_data) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_set_allocated:google.protobuf.testing.oneofs.OneOfsRequest.any_data)
}

inline bool OneOfsRequest::has_data() const {
  return data_case() != DATA_NOT_SET;
}
inline void OneOfsRequest::clear_has_data() {
  _oneof_case_[0] = DATA_NOT_SET;
}
inline OneOfsRequest::DataCase OneOfsRequest::data_case() const {
  return OneOfsRequest::DataCase(_oneof_case_[0]);
}
inline const OneOfsRequest* OneOfsRequest::internal_default_instance() {
  return &OneOfsRequest_default_instance_.get();
}
// -------------------------------------------------------------------

// RequestWithSimpleOneof

// optional string value = 1;
inline void RequestWithSimpleOneof::clear_value() {
  value_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline const ::std::string& RequestWithSimpleOneof::value() const {
  // @@protoc_insertion_point(field_get:google.protobuf.testing.oneofs.RequestWithSimpleOneof.value)
  return value_.GetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void RequestWithSimpleOneof::set_value(const ::std::string& value) {
  
  value_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:google.protobuf.testing.oneofs.RequestWithSimpleOneof.value)
}
inline void RequestWithSimpleOneof::set_value(const char* value) {
  
  value_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:google.protobuf.testing.oneofs.RequestWithSimpleOneof.value)
}
inline void RequestWithSimpleOneof::set_value(const char* value, size_t size) {
  
  value_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:google.protobuf.testing.oneofs.RequestWithSimpleOneof.value)
}
inline ::std::string* RequestWithSimpleOneof::mutable_value() {
  
  // @@protoc_insertion_point(field_mutable:google.protobuf.testing.oneofs.RequestWithSimpleOneof.value)
  return value_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline ::std::string* RequestWithSimpleOneof::release_value() {
  // @@protoc_insertion_point(field_release:google.protobuf.testing.oneofs.RequestWithSimpleOneof.value)
  
  return value_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void RequestWithSimpleOneof::set_allocated_value(::std::string* value) {
  if (value != NULL) {
    
  } else {
    
  }
  value_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set_allocated:google.protobuf.testing.oneofs.RequestWithSimpleOneof.value)
}

// optional string str_data = 2;
inline bool RequestWithSimpleOneof::has_str_data() const {
  return data_case() == kStrData;
}
inline void RequestWithSimpleOneof::set_has_str_data() {
  _oneof_case_[0] = kStrData;
}
inline void RequestWithSimpleOneof::clear_str_data() {
  if (has_str_data()) {
    data_.str_data_.DestroyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
    clear_has_data();
  }
}
inline const ::std::string& RequestWithSimpleOneof::str_data() const {
  // @@protoc_insertion_point(field_get:google.protobuf.testing.oneofs.RequestWithSimpleOneof.str_data)
  if (has_str_data()) {
    return data_.str_data_.GetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  }
  return *&::google::protobuf::internal::GetEmptyStringAlreadyInited();
}
inline void RequestWithSimpleOneof::set_str_data(const ::std::string& value) {
  // @@protoc_insertion_point(field_set:google.protobuf.testing.oneofs.RequestWithSimpleOneof.str_data)
  if (!has_str_data()) {
    clear_data();
    set_has_str_data();
    data_.str_data_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  }
  data_.str_data_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:google.protobuf.testing.oneofs.RequestWithSimpleOneof.str_data)
}
inline void RequestWithSimpleOneof::set_str_data(const char* value) {
  if (!has_str_data()) {
    clear_data();
    set_has_str_data();
    data_.str_data_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  }
  data_.str_data_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(value));
  // @@protoc_insertion_point(field_set_char:google.protobuf.testing.oneofs.RequestWithSimpleOneof.str_data)
}
inline void RequestWithSimpleOneof::set_str_data(const char* value, size_t size) {
  if (!has_str_data()) {
    clear_data();
    set_has_str_data();
    data_.str_data_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  }
  data_.str_data_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(
      reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:google.protobuf.testing.oneofs.RequestWithSimpleOneof.str_data)
}
inline ::std::string* RequestWithSimpleOneof::mutable_str_data() {
  if (!has_str_data()) {
    clear_data();
    set_has_str_data();
    data_.str_data_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  }
  // @@protoc_insertion_point(field_mutable:google.protobuf.testing.oneofs.RequestWithSimpleOneof.str_data)
  return data_.str_data_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline ::std::string* RequestWithSimpleOneof::release_str_data() {
  // @@protoc_insertion_point(field_release:google.protobuf.testing.oneofs.RequestWithSimpleOneof.str_data)
  if (has_str_data()) {
    clear_has_data();
    return data_.str_data_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  } else {
    return NULL;
  }
}
inline void RequestWithSimpleOneof::set_allocated_str_data(::std::string* str_data) {
  if (!has_str_data()) {
    data_.str_data_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  }
  clear_data();
  if (str_data != NULL) {
    set_has_str_data();
    data_.str_data_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
        str_data);
  }
  // @@protoc_insertion_point(field_set_allocated:google.protobuf.testing.oneofs.RequestWithSimpleOneof.str_data)
}

// optional int32 int_data = 3;
inline bool RequestWithSimpleOneof::has_int_data() const {
  return data_case() == kIntData;
}
inline void RequestWithSimpleOneof::set_has_int_data() {
  _oneof_case_[0] = kIntData;
}
inline void RequestWithSimpleOneof::clear_int_data() {
  if (has_int_data()) {
    data_.int_data_ = 0;
    clear_has_data();
  }
}
inline ::google::protobuf::int32 RequestWithSimpleOneof::int_data() const {
  // @@protoc_insertion_point(field_get:google.protobuf.testing.oneofs.RequestWithSimpleOneof.int_data)
  if (has_int_data()) {
    return data_.int_data_;
  }
  return 0;
}
inline void RequestWithSimpleOneof::set_int_data(::google::protobuf::int32 value) {
  if (!has_int_data()) {
    clear_data();
    set_has_int_data();
  }
  data_.int_data_ = value;
  // @@protoc_insertion_point(field_set:google.protobuf.testing.oneofs.RequestWithSimpleOneof.int_data)
}

// optional .google.protobuf.testing.oneofs.Data message_data = 4;
inline bool RequestWithSimpleOneof::has_message_data() const {
  return data_case() == kMessageData;
}
inline void RequestWithSimpleOneof::set_has_message_data() {
  _oneof_case_[0] = kMessageData;
}
inline void RequestWithSimpleOneof::clear_message_data() {
  if (has_message_data()) {
    delete data_.message_data_;
    clear_has_data();
  }
}
inline  const ::google::protobuf::testing::oneofs::Data& RequestWithSimpleOneof::message_data() const {
  // @@protoc_insertion_point(field_get:google.protobuf.testing.oneofs.RequestWithSimpleOneof.message_data)
  return has_message_data()
      ? *data_.message_data_
      : ::google::protobuf::testing::oneofs::Data::default_instance();
}
inline ::google::protobuf::testing::oneofs::Data* RequestWithSimpleOneof::mutable_message_data() {
  if (!has_message_data()) {
    clear_data();
    set_has_message_data();
    data_.message_data_ = new ::google::protobuf::testing::oneofs::Data;
  }
  // @@protoc_insertion_point(field_mutable:google.protobuf.testing.oneofs.RequestWithSimpleOneof.message_data)
  return data_.message_data_;
}
inline ::google::protobuf::testing::oneofs::Data* RequestWithSimpleOneof::release_message_data() {
  // @@protoc_insertion_point(field_release:google.protobuf.testing.oneofs.RequestWithSimpleOneof.message_data)
  if (has_message_data()) {
    clear_has_data();
    ::google::protobuf::testing::oneofs::Data* temp = data_.message_data_;
    data_.message_data_ = NULL;
    return temp;
  } else {
    return NULL;
  }
}
inline void RequestWithSimpleOneof::set_allocated_message_data(::google::protobuf::testing::oneofs::Data* message_data) {
  clear_data();
  if (message_data) {
    set_has_message_data();
    data_.message_data_ = message_data;
  }
  // @@protoc_insertion_point(field_set_allocated:google.protobuf.testing.oneofs.RequestWithSimpleOneof.message_data)
}

// optional .google.protobuf.testing.oneofs.MoreData more_data = 5;
inline bool RequestWithSimpleOneof::has_more_data() const {
  return data_case() == kMoreData;
}
inline void RequestWithSimpleOneof::set_has_more_data() {
  _oneof_case_[0] = kMoreData;
}
inline void RequestWithSimpleOneof::clear_more_data() {
  if (has_more_data()) {
    delete data_.more_data_;
    clear_has_data();
  }
}
inline  const ::google::protobuf::testing::oneofs::MoreData& RequestWithSimpleOneof::more_data() const {
  // @@protoc_insertion_point(field_get:google.protobuf.testing.oneofs.RequestWithSimpleOneof.more_data)
  return has_more_data()
      ? *data_.more_data_
      : ::google::protobuf::testing::oneofs::MoreData::default_instance();
}
inline ::google::protobuf::testing::oneofs::MoreData* RequestWithSimpleOneof::mutable_more_data() {
  if (!has_more_data()) {
    clear_data();
    set_has_more_data();
    data_.more_data_ = new ::google::protobuf::testing::oneofs::MoreData;
  }
  // @@protoc_insertion_point(field_mutable:google.protobuf.testing.oneofs.RequestWithSimpleOneof.more_data)
  return data_.more_data_;
}
inline ::google::protobuf::testing::oneofs::MoreData* RequestWithSimpleOneof::release_more_data() {
  // @@protoc_insertion_point(field_release:google.protobuf.testing.oneofs.RequestWithSimpleOneof.more_data)
  if (has_more_data()) {
    clear_has_data();
    ::google::protobuf::testing::oneofs::MoreData* temp = data_.more_data_;
    data_.more_data_ = NULL;
    return temp;
  } else {
    return NULL;
  }
}
inline void RequestWithSimpleOneof::set_allocated_more_data(::google::protobuf::testing::oneofs::MoreData* more_data) {
  clear_data();
  if (more_data) {
    set_has_more_data();
    data_.more_data_ = more_data;
  }
  // @@protoc_insertion_point(field_set_allocated:google.protobuf.testing.oneofs.RequestWithSimpleOneof.more_data)
}

inline bool RequestWithSimpleOneof::has_data() const {
  return data_case() != DATA_NOT_SET;
}
inline void RequestWithSimpleOneof::clear_has_data() {
  _oneof_case_[0] = DATA_NOT_SET;
}
inline RequestWithSimpleOneof::DataCase RequestWithSimpleOneof::data_case() const {
  return RequestWithSimpleOneof::DataCase(_oneof_case_[0]);
}
inline const RequestWithSimpleOneof* RequestWithSimpleOneof::internal_default_instance() {
  return &RequestWithSimpleOneof_default_instance_.get();
}
// -------------------------------------------------------------------

// Data

// optional int32 data_value = 1;
inline void Data::clear_data_value() {
  data_value_ = 0;
}
inline ::google::protobuf::int32 Data::data_value() const {
  // @@protoc_insertion_point(field_get:google.protobuf.testing.oneofs.Data.data_value)
  return data_value_;
}
inline void Data::set_data_value(::google::protobuf::int32 value) {
  
  data_value_ = value;
  // @@protoc_insertion_point(field_set:google.protobuf.testing.oneofs.Data.data_value)
}

inline const Data* Data::internal_default_instance() {
  return &Data_default_instance_.get();
}
// -------------------------------------------------------------------

// MoreData

// optional string str_value = 1;
inline void MoreData::clear_str_value() {
  str_value_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline const ::std::string& MoreData::str_value() const {
  // @@protoc_insertion_point(field_get:google.protobuf.testing.oneofs.MoreData.str_value)
  return str_value_.GetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void MoreData::set_str_value(const ::std::string& value) {
  
  str_value_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:google.protobuf.testing.oneofs.MoreData.str_value)
}
inline void MoreData::set_str_value(const char* value) {
  
  str_value_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:google.protobuf.testing.oneofs.MoreData.str_value)
}
inline void MoreData::set_str_value(const char* value, size_t size) {
  
  str_value_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:google.protobuf.testing.oneofs.MoreData.str_value)
}
inline ::std::string* MoreData::mutable_str_value() {
  
  // @@protoc_insertion_point(field_mutable:google.protobuf.testing.oneofs.MoreData.str_value)
  return str_value_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline ::std::string* MoreData::release_str_value() {
  // @@protoc_insertion_point(field_release:google.protobuf.testing.oneofs.MoreData.str_value)
  
  return str_value_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void MoreData::set_allocated_str_value(::std::string* str_value) {
  if (str_value != NULL) {
    
  } else {
    
  }
  str_value_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), str_value);
  // @@protoc_insertion_point(field_set_allocated:google.protobuf.testing.oneofs.MoreData.str_value)
}

inline const MoreData* MoreData::internal_default_instance() {
  return &MoreData_default_instance_.get();
}
// -------------------------------------------------------------------

// Response

// optional string value = 1;
inline void Response::clear_value() {
  value_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline const ::std::string& Response::value() const {
  // @@protoc_insertion_point(field_get:google.protobuf.testing.oneofs.Response.value)
  return value_.GetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void Response::set_value(const ::std::string& value) {
  
  value_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:google.protobuf.testing.oneofs.Response.value)
}
inline void Response::set_value(const char* value) {
  
  value_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:google.protobuf.testing.oneofs.Response.value)
}
inline void Response::set_value(const char* value, size_t size) {
  
  value_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:google.protobuf.testing.oneofs.Response.value)
}
inline ::std::string* Response::mutable_value() {
  
  // @@protoc_insertion_point(field_mutable:google.protobuf.testing.oneofs.Response.value)
  return value_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline ::std::string* Response::release_value() {
  // @@protoc_insertion_point(field_release:google.protobuf.testing.oneofs.Response.value)
  
  return value_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void Response::set_allocated_value(::std::string* value) {
  if (value != NULL) {
    
  } else {
    
  }
  value_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set_allocated:google.protobuf.testing.oneofs.Response.value)
}

inline const Response* Response::internal_default_instance() {
  return &Response_default_instance_.get();
}
#endif  // !PROTOBUF_INLINE_NOT_IN_HEADERS
// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------


// @@protoc_insertion_point(namespace_scope)

}  // namespace oneofs
}  // namespace testing
}  // namespace protobuf
}  // namespace google

// @@protoc_insertion_point(global_scope)

#endif  // PROTOBUF_google_2fprotobuf_2futil_2finternal_2ftestdata_2foneofs_2eproto__INCLUDED

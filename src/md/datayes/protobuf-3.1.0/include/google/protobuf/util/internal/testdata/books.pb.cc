// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: google/protobuf/util/internal/testdata/books.proto

#define INTERNAL_SUPPRESS_PROTOBUF_FIELD_DEPRECATION
#include <google/protobuf/util/internal/testdata/books.pb.h>

#include <algorithm>

#include <google/protobuf/stubs/common.h>
#include <google/protobuf/stubs/port.h>
#include <google/protobuf/stubs/once.h>
#include <google/protobuf/io/coded_stream.h>
#include <google/protobuf/wire_format_lite_inl.h>
#include <google/protobuf/descriptor.h>
#include <google/protobuf/generated_message_reflection.h>
#include <google/protobuf/reflection_ops.h>
#include <google/protobuf/wire_format.h>
// @@protoc_insertion_point(includes)

namespace google {
namespace protobuf {
namespace testing {

namespace {

const ::google::protobuf::Descriptor* Book_descriptor_ = NULL;
const ::google::protobuf::internal::GeneratedMessageReflection*
  Book_reflection_ = NULL;
const ::google::protobuf::Descriptor* Book_Data_descriptor_ = NULL;
const ::google::protobuf::internal::GeneratedMessageReflection*
  Book_Data_reflection_ = NULL;
const ::google::protobuf::Descriptor* Book_Label_descriptor_ = NULL;
const ::google::protobuf::internal::GeneratedMessageReflection*
  Book_Label_reflection_ = NULL;
const ::google::protobuf::EnumDescriptor* Book_Type_descriptor_ = NULL;
const ::google::protobuf::Descriptor* Publisher_descriptor_ = NULL;
const ::google::protobuf::internal::GeneratedMessageReflection*
  Publisher_reflection_ = NULL;
const ::google::protobuf::Descriptor* Author_descriptor_ = NULL;
const ::google::protobuf::internal::GeneratedMessageReflection*
  Author_reflection_ = NULL;
const ::google::protobuf::Descriptor* BadAuthor_descriptor_ = NULL;
const ::google::protobuf::internal::GeneratedMessageReflection*
  BadAuthor_reflection_ = NULL;
const ::google::protobuf::Descriptor* Primitive_descriptor_ = NULL;
const ::google::protobuf::internal::GeneratedMessageReflection*
  Primitive_reflection_ = NULL;
const ::google::protobuf::Descriptor* PackedPrimitive_descriptor_ = NULL;
const ::google::protobuf::internal::GeneratedMessageReflection*
  PackedPrimitive_reflection_ = NULL;
const ::google::protobuf::Descriptor* NestedBook_descriptor_ = NULL;
const ::google::protobuf::internal::GeneratedMessageReflection*
  NestedBook_reflection_ = NULL;
const ::google::protobuf::Descriptor* BadNestedBook_descriptor_ = NULL;
const ::google::protobuf::internal::GeneratedMessageReflection*
  BadNestedBook_reflection_ = NULL;
const ::google::protobuf::Descriptor* Cyclic_descriptor_ = NULL;
const ::google::protobuf::internal::GeneratedMessageReflection*
  Cyclic_reflection_ = NULL;

}  // namespace


void protobuf_AssignDesc_google_2fprotobuf_2futil_2finternal_2ftestdata_2fbooks_2eproto() GOOGLE_ATTRIBUTE_COLD;
void protobuf_AssignDesc_google_2fprotobuf_2futil_2finternal_2ftestdata_2fbooks_2eproto() {
  protobuf_AddDesc_google_2fprotobuf_2futil_2finternal_2ftestdata_2fbooks_2eproto();
  const ::google::protobuf::FileDescriptor* file =
    ::google::protobuf::DescriptorPool::generated_pool()->FindFileByName(
      "google/protobuf/util/internal/testdata/books.proto");
  GOOGLE_CHECK(file != NULL);
  Book_descriptor_ = file->message_type(0);
  static const int Book_offsets_[9] = {
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(Book, title_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(Book, author_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(Book, length_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(Book, published_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(Book, content_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(Book, data_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(Book, publisher_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(Book, labels_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(Book, type_),
  };
  Book_reflection_ =
    ::google::protobuf::internal::GeneratedMessageReflection::NewGeneratedMessageReflection(
      Book_descriptor_,
      Book::internal_default_instance(),
      Book_offsets_,
      GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(Book, _has_bits_),
      -1,
      GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(Book, _extensions_),
      sizeof(Book),
      GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(Book, _internal_metadata_));
  Book_Data_descriptor_ = Book_descriptor_->nested_type(0);
  static const int Book_Data_offsets_[2] = {
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(Book_Data, year_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(Book_Data, copyright_),
  };
  Book_Data_reflection_ =
    ::google::protobuf::internal::GeneratedMessageReflection::NewGeneratedMessageReflection(
      Book_Data_descriptor_,
      Book_Data::internal_default_instance(),
      Book_Data_offsets_,
      GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(Book_Data, _has_bits_),
      -1,
      -1,
      sizeof(Book_Data),
      GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(Book_Data, _internal_metadata_));
  Book_Label_descriptor_ = Book_descriptor_->nested_type(1);
  static const int Book_Label_offsets_[2] = {
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(Book_Label, key_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(Book_Label, value_),
  };
  Book_Label_reflection_ =
    ::google::protobuf::internal::GeneratedMessageReflection::NewGeneratedMessageReflection(
      Book_Label_descriptor_,
      Book_Label::internal_default_instance(),
      Book_Label_offsets_,
      GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(Book_Label, _has_bits_),
      -1,
      -1,
      sizeof(Book_Label),
      GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(Book_Label, _internal_metadata_));
  Book_Type_descriptor_ = Book_descriptor_->enum_type(0);
  Publisher_descriptor_ = file->message_type(1);
  static const int Publisher_offsets_[1] = {
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(Publisher, name_),
  };
  Publisher_reflection_ =
    ::google::protobuf::internal::GeneratedMessageReflection::NewGeneratedMessageReflection(
      Publisher_descriptor_,
      Publisher::internal_default_instance(),
      Publisher_offsets_,
      GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(Publisher, _has_bits_),
      -1,
      -1,
      sizeof(Publisher),
      GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(Publisher, _internal_metadata_));
  Author_descriptor_ = file->message_type(2);
  static const int Author_offsets_[5] = {
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(Author, id_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(Author, name_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(Author, pseudonym_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(Author, alive_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(Author, friend__),
  };
  Author_reflection_ =
    ::google::protobuf::internal::GeneratedMessageReflection::NewGeneratedMessageReflection(
      Author_descriptor_,
      Author::internal_default_instance(),
      Author_offsets_,
      GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(Author, _has_bits_),
      -1,
      -1,
      sizeof(Author),
      GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(Author, _internal_metadata_));
  BadAuthor_descriptor_ = file->message_type(3);
  static const int BadAuthor_offsets_[4] = {
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(BadAuthor, id_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(BadAuthor, name_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(BadAuthor, pseudonym_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(BadAuthor, alive_),
  };
  BadAuthor_reflection_ =
    ::google::protobuf::internal::GeneratedMessageReflection::NewGeneratedMessageReflection(
      BadAuthor_descriptor_,
      BadAuthor::internal_default_instance(),
      BadAuthor_offsets_,
      GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(BadAuthor, _has_bits_),
      -1,
      -1,
      sizeof(BadAuthor),
      GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(BadAuthor, _internal_metadata_));
  Primitive_descriptor_ = file->message_type(4);
  static const int Primitive_offsets_[30] = {
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(Primitive, fix32_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(Primitive, u32_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(Primitive, i32_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(Primitive, sf32_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(Primitive, s32_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(Primitive, fix64_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(Primitive, u64_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(Primitive, i64_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(Primitive, sf64_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(Primitive, s64_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(Primitive, str_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(Primitive, bytes_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(Primitive, float__),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(Primitive, double__),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(Primitive, bool__),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(Primitive, rep_fix32_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(Primitive, rep_u32_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(Primitive, rep_i32_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(Primitive, rep_sf32_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(Primitive, rep_s32_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(Primitive, rep_fix64_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(Primitive, rep_u64_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(Primitive, rep_i64_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(Primitive, rep_sf64_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(Primitive, rep_s64_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(Primitive, rep_str_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(Primitive, rep_bytes_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(Primitive, rep_float_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(Primitive, rep_double_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(Primitive, rep_bool_),
  };
  Primitive_reflection_ =
    ::google::protobuf::internal::GeneratedMessageReflection::NewGeneratedMessageReflection(
      Primitive_descriptor_,
      Primitive::internal_default_instance(),
      Primitive_offsets_,
      GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(Primitive, _has_bits_),
      -1,
      -1,
      sizeof(Primitive),
      GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(Primitive, _internal_metadata_));
  PackedPrimitive_descriptor_ = file->message_type(5);
  static const int PackedPrimitive_offsets_[13] = {
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(PackedPrimitive, rep_fix32_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(PackedPrimitive, rep_u32_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(PackedPrimitive, rep_i32_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(PackedPrimitive, rep_sf32_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(PackedPrimitive, rep_s32_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(PackedPrimitive, rep_fix64_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(PackedPrimitive, rep_u64_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(PackedPrimitive, rep_i64_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(PackedPrimitive, rep_sf64_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(PackedPrimitive, rep_s64_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(PackedPrimitive, rep_float_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(PackedPrimitive, rep_double_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(PackedPrimitive, rep_bool_),
  };
  PackedPrimitive_reflection_ =
    ::google::protobuf::internal::GeneratedMessageReflection::NewGeneratedMessageReflection(
      PackedPrimitive_descriptor_,
      PackedPrimitive::internal_default_instance(),
      PackedPrimitive_offsets_,
      GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(PackedPrimitive, _has_bits_),
      -1,
      -1,
      sizeof(PackedPrimitive),
      GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(PackedPrimitive, _internal_metadata_));
  NestedBook_descriptor_ = file->message_type(6);
  static const int NestedBook_offsets_[1] = {
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(NestedBook, book_),
  };
  NestedBook_reflection_ =
    ::google::protobuf::internal::GeneratedMessageReflection::NewGeneratedMessageReflection(
      NestedBook_descriptor_,
      NestedBook::internal_default_instance(),
      NestedBook_offsets_,
      GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(NestedBook, _has_bits_),
      -1,
      -1,
      sizeof(NestedBook),
      GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(NestedBook, _internal_metadata_));
  BadNestedBook_descriptor_ = file->message_type(7);
  static const int BadNestedBook_offsets_[1] = {
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(BadNestedBook, book_),
  };
  BadNestedBook_reflection_ =
    ::google::protobuf::internal::GeneratedMessageReflection::NewGeneratedMessageReflection(
      BadNestedBook_descriptor_,
      BadNestedBook::internal_default_instance(),
      BadNestedBook_offsets_,
      GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(BadNestedBook, _has_bits_),
      -1,
      -1,
      sizeof(BadNestedBook),
      GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(BadNestedBook, _internal_metadata_));
  Cyclic_descriptor_ = file->message_type(8);
  static const int Cyclic_offsets_[5] = {
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(Cyclic, m_int_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(Cyclic, m_str_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(Cyclic, m_book_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(Cyclic, m_author_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(Cyclic, m_cyclic_),
  };
  Cyclic_reflection_ =
    ::google::protobuf::internal::GeneratedMessageReflection::NewGeneratedMessageReflection(
      Cyclic_descriptor_,
      Cyclic::internal_default_instance(),
      Cyclic_offsets_,
      GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(Cyclic, _has_bits_),
      -1,
      -1,
      sizeof(Cyclic),
      GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(Cyclic, _internal_metadata_));
}

namespace {

GOOGLE_PROTOBUF_DECLARE_ONCE(protobuf_AssignDescriptors_once_);
void protobuf_AssignDescriptorsOnce() {
  ::google::protobuf::GoogleOnceInit(&protobuf_AssignDescriptors_once_,
                 &protobuf_AssignDesc_google_2fprotobuf_2futil_2finternal_2ftestdata_2fbooks_2eproto);
}

void protobuf_RegisterTypes(const ::std::string&) GOOGLE_ATTRIBUTE_COLD;
void protobuf_RegisterTypes(const ::std::string&) {
  protobuf_AssignDescriptorsOnce();
  ::google::protobuf::MessageFactory::InternalRegisterGeneratedMessage(
      Book_descriptor_, Book::internal_default_instance());
  ::google::protobuf::MessageFactory::InternalRegisterGeneratedMessage(
      Book_Data_descriptor_, Book_Data::internal_default_instance());
  ::google::protobuf::MessageFactory::InternalRegisterGeneratedMessage(
      Book_Label_descriptor_, Book_Label::internal_default_instance());
  ::google::protobuf::MessageFactory::InternalRegisterGeneratedMessage(
      Publisher_descriptor_, Publisher::internal_default_instance());
  ::google::protobuf::MessageFactory::InternalRegisterGeneratedMessage(
      Author_descriptor_, Author::internal_default_instance());
  ::google::protobuf::MessageFactory::InternalRegisterGeneratedMessage(
      BadAuthor_descriptor_, BadAuthor::internal_default_instance());
  ::google::protobuf::MessageFactory::InternalRegisterGeneratedMessage(
      Primitive_descriptor_, Primitive::internal_default_instance());
  ::google::protobuf::MessageFactory::InternalRegisterGeneratedMessage(
      PackedPrimitive_descriptor_, PackedPrimitive::internal_default_instance());
  ::google::protobuf::MessageFactory::InternalRegisterGeneratedMessage(
      NestedBook_descriptor_, NestedBook::internal_default_instance());
  ::google::protobuf::MessageFactory::InternalRegisterGeneratedMessage(
      BadNestedBook_descriptor_, BadNestedBook::internal_default_instance());
  ::google::protobuf::MessageFactory::InternalRegisterGeneratedMessage(
      Cyclic_descriptor_, Cyclic::internal_default_instance());
}

}  // namespace

void protobuf_ShutdownFile_google_2fprotobuf_2futil_2finternal_2ftestdata_2fbooks_2eproto() {
  Book_default_instance_.Shutdown();
  delete Book_reflection_;
  Book_Data_default_instance_.Shutdown();
  delete Book_Data_reflection_;
  Book_Label_default_instance_.Shutdown();
  delete Book_Label_reflection_;
  Publisher_default_instance_.Shutdown();
  delete Publisher_reflection_;
  Author_default_instance_.Shutdown();
  delete Author_reflection_;
  BadAuthor_default_instance_.Shutdown();
  delete BadAuthor_reflection_;
  Primitive_default_instance_.Shutdown();
  delete Primitive_reflection_;
  PackedPrimitive_default_instance_.Shutdown();
  delete PackedPrimitive_reflection_;
  NestedBook_default_instance_.Shutdown();
  delete NestedBook_reflection_;
  BadNestedBook_default_instance_.Shutdown();
  delete BadNestedBook_reflection_;
  Cyclic_default_instance_.Shutdown();
  delete Cyclic_reflection_;
}

void protobuf_InitDefaults_google_2fprotobuf_2futil_2finternal_2ftestdata_2fbooks_2eproto_impl() {
  GOOGLE_PROTOBUF_VERIFY_VERSION;

  ::google::protobuf::internal::GetEmptyString();
  Book_default_instance_.DefaultConstruct();
  ::google::protobuf::internal::GetEmptyString();
  Book_Data_default_instance_.DefaultConstruct();
  ::google::protobuf::internal::GetEmptyString();
  Book_Label_default_instance_.DefaultConstruct();
  ::google::protobuf::internal::GetEmptyString();
  Publisher_default_instance_.DefaultConstruct();
  ::google::protobuf::internal::GetEmptyString();
  Author_default_instance_.DefaultConstruct();
  ::google::protobuf::internal::GetEmptyString();
  BadAuthor_default_instance_.DefaultConstruct();
  ::google::protobuf::internal::GetEmptyString();
  Primitive_default_instance_.DefaultConstruct();
  PackedPrimitive_default_instance_.DefaultConstruct();
  NestedBook_default_instance_.DefaultConstruct();
  BadNestedBook_default_instance_.DefaultConstruct();
  ::google::protobuf::internal::GetEmptyString();
  Cyclic_default_instance_.DefaultConstruct();
  ::google::protobuf::internal::ExtensionSet::RegisterMessageExtension(
    ::google::protobuf::testing::Book::internal_default_instance(),
    201, 11, true, false,
    ::google::protobuf::testing::Author::internal_default_instance());
  Book_default_instance_.get_mutable()->InitAsDefaultInstance();
  Book_Data_default_instance_.get_mutable()->InitAsDefaultInstance();
  Book_Label_default_instance_.get_mutable()->InitAsDefaultInstance();
  Publisher_default_instance_.get_mutable()->InitAsDefaultInstance();
  Author_default_instance_.get_mutable()->InitAsDefaultInstance();
  BadAuthor_default_instance_.get_mutable()->InitAsDefaultInstance();
  Primitive_default_instance_.get_mutable()->InitAsDefaultInstance();
  PackedPrimitive_default_instance_.get_mutable()->InitAsDefaultInstance();
  NestedBook_default_instance_.get_mutable()->InitAsDefaultInstance();
  ::google::protobuf::internal::ExtensionSet::RegisterMessageExtension(
    ::google::protobuf::testing::Book::internal_default_instance(),
    301, 11, false, false,
    ::google::protobuf::testing::NestedBook::internal_default_instance());
  BadNestedBook_default_instance_.get_mutable()->InitAsDefaultInstance();
  Cyclic_default_instance_.get_mutable()->InitAsDefaultInstance();
}

GOOGLE_PROTOBUF_DECLARE_ONCE(protobuf_InitDefaults_google_2fprotobuf_2futil_2finternal_2ftestdata_2fbooks_2eproto_once_);
void protobuf_InitDefaults_google_2fprotobuf_2futil_2finternal_2ftestdata_2fbooks_2eproto() {
  ::google::protobuf::GoogleOnceInit(&protobuf_InitDefaults_google_2fprotobuf_2futil_2finternal_2ftestdata_2fbooks_2eproto_once_,
                 &protobuf_InitDefaults_google_2fprotobuf_2futil_2finternal_2ftestdata_2fbooks_2eproto_impl);
}
void protobuf_AddDesc_google_2fprotobuf_2futil_2finternal_2ftestdata_2fbooks_2eproto_impl() {
  GOOGLE_PROTOBUF_VERIFY_VERSION;

  protobuf_InitDefaults_google_2fprotobuf_2futil_2finternal_2ftestdata_2fbooks_2eproto();
  ::google::protobuf::DescriptorPool::InternalAddGeneratedFile(
    "\n2google/protobuf/util/internal/testdata"
    "/books.proto\022\027google.protobuf.testing\"\363\003"
    "\n\004Book\022\r\n\005title\030\001 \001(\t\022/\n\006author\030\002 \001(\0132\037."
    "google.protobuf.testing.Author\022\016\n\006length"
    "\030\003 \001(\r\022\021\n\tpublished\030\004 \001(\003\022\017\n\007content\030\005 \001"
    "(\014\0220\n\004data\030\006 \001(\n2\".google.protobuf.testi"
    "ng.Book.Data\0225\n\tpublisher\030\t \001(\0132\".google"
    ".protobuf.testing.Publisher\0223\n\006labels\030\n "
    "\003(\0132#.google.protobuf.testing.Book.Label"
    "\0220\n\004type\030\013 \001(\0162\".google.protobuf.testing"
    ".Book.Type\032\'\n\004Data\022\014\n\004year\030\007 \001(\r\022\021\n\tcopy"
    "right\030\010 \001(\t\032#\n\005Label\022\013\n\003key\030\001 \001(\t\022\r\n\005val"
    "ue\030\002 \001(\t\"Q\n\004Type\022\013\n\007FICTION\020\001\022\010\n\004KIDS\020\002\022"
    "\030\n\024ACTION_AND_ADVENTURE\020\003\022\030\n\024arts_and_ph"
    "otography\020\004*\006\010\310\001\020\364\003\"\031\n\tPublisher\022\014\n\004name"
    "\030\001 \002(\t\"z\n\006Author\022\017\n\002id\030\001 \001(\004R\003@id\022\014\n\004nam"
    "e\030\002 \001(\t\022\021\n\tpseudonym\030\003 \003(\t\022\r\n\005alive\030\004 \001("
    "\010\022/\n\006friend\030\005 \003(\0132\037.google.protobuf.test"
    "ing.Author\"K\n\tBadAuthor\022\n\n\002id\030\001 \001(\t\022\014\n\004n"
    "ame\030\002 \003(\004\022\021\n\tpseudonym\030\003 \001(\t\022\021\n\005alive\030\004 "
    "\003(\010B\002\020\001\"\351\003\n\tPrimitive\022\r\n\005fix32\030\001 \001(\007\022\013\n\003"
    "u32\030\002 \001(\r\022\013\n\003i32\030\003 \001(\005\022\014\n\004sf32\030\004 \001(\017\022\013\n\003"
    "s32\030\005 \001(\021\022\r\n\005fix64\030\006 \001(\006\022\013\n\003u64\030\007 \001(\004\022\013\n"
    "\003i64\030\010 \001(\003\022\014\n\004sf64\030\t \001(\020\022\013\n\003s64\030\n \001(\022\022\013\n"
    "\003str\030\013 \001(\t\022\r\n\005bytes\030\014 \001(\014\022\r\n\005float\030\r \001(\002"
    "\022\016\n\006double\030\016 \001(\001\022\014\n\004bool\030\017 \001(\010\022\021\n\trep_fi"
    "x32\030\020 \003(\007\022\017\n\007rep_u32\030\021 \003(\r\022\017\n\007rep_i32\030\022 "
    "\003(\005\022\020\n\010rep_sf32\030\023 \003(\017\022\017\n\007rep_s32\030\024 \003(\021\022\021"
    "\n\trep_fix64\030\025 \003(\006\022\017\n\007rep_u64\030\026 \003(\004\022\017\n\007re"
    "p_i64\030\027 \003(\003\022\020\n\010rep_sf64\030\030 \003(\020\022\017\n\007rep_s64"
    "\030\031 \003(\022\022\017\n\007rep_str\030\032 \003(\t\022\021\n\trep_bytes\030\033 \003"
    "(\014\022\021\n\trep_float\030\034 \003(\002\022\022\n\nrep_double\030\035 \003("
    "\001\022\020\n\010rep_bool\030\036 \003(\010\"\256\002\n\017PackedPrimitive\022"
    "\025\n\trep_fix32\030\020 \003(\007B\002\020\001\022\023\n\007rep_u32\030\021 \003(\rB"
    "\002\020\001\022\023\n\007rep_i32\030\022 \003(\005B\002\020\001\022\024\n\010rep_sf32\030\023 \003"
    "(\017B\002\020\001\022\023\n\007rep_s32\030\024 \003(\021B\002\020\001\022\025\n\trep_fix64"
    "\030\025 \003(\006B\002\020\001\022\023\n\007rep_u64\030\026 \003(\004B\002\020\001\022\023\n\007rep_i"
    "64\030\027 \003(\003B\002\020\001\022\024\n\010rep_sf64\030\030 \003(\020B\002\020\001\022\023\n\007re"
    "p_s64\030\031 \003(\022B\002\020\001\022\025\n\trep_float\030\034 \003(\002B\002\020\001\022\026"
    "\n\nrep_double\030\035 \003(\001B\002\020\001\022\024\n\010rep_bool\030\036 \003(\010"
    "B\002\020\001\"\224\001\n\nNestedBook\022+\n\004book\030\001 \001(\0132\035.goog"
    "le.protobuf.testing.Book2Y\n\014another_book"
    "\022\035.google.protobuf.testing.Book\030\255\002 \001(\0132#"
    ".google.protobuf.testing.NestedBook\"!\n\rB"
    "adNestedBook\022\020\n\004book\030\001 \003(\rB\002\020\001\"\273\001\n\006Cycli"
    "c\022\r\n\005m_int\030\001 \001(\005\022\r\n\005m_str\030\002 \001(\t\022-\n\006m_boo"
    "k\030\003 \001(\0132\035.google.protobuf.testing.Book\0221"
    "\n\010m_author\030\005 \003(\0132\037.google.protobuf.testi"
    "ng.Author\0221\n\010m_cyclic\030\004 \001(\0132\037.google.pro"
    "tobuf.testing.Cyclic:T\n\013more_author\022\035.go"
    "ogle.protobuf.testing.Book\030\311\001 \003(\0132\037.goog"
    "le.protobuf.testing.Author", 2066);
  ::google::protobuf::MessageFactory::InternalRegisterGeneratedFile(
    "google/protobuf/util/internal/testdata/books.proto", &protobuf_RegisterTypes);
  ::google::protobuf::internal::OnShutdown(&protobuf_ShutdownFile_google_2fprotobuf_2futil_2finternal_2ftestdata_2fbooks_2eproto);
}

GOOGLE_PROTOBUF_DECLARE_ONCE(protobuf_AddDesc_google_2fprotobuf_2futil_2finternal_2ftestdata_2fbooks_2eproto_once_);
void protobuf_AddDesc_google_2fprotobuf_2futil_2finternal_2ftestdata_2fbooks_2eproto() {
  ::google::protobuf::GoogleOnceInit(&protobuf_AddDesc_google_2fprotobuf_2futil_2finternal_2ftestdata_2fbooks_2eproto_once_,
                 &protobuf_AddDesc_google_2fprotobuf_2futil_2finternal_2ftestdata_2fbooks_2eproto_impl);
}
// Force AddDescriptors() to be called at static initialization time.
struct StaticDescriptorInitializer_google_2fprotobuf_2futil_2finternal_2ftestdata_2fbooks_2eproto {
  StaticDescriptorInitializer_google_2fprotobuf_2futil_2finternal_2ftestdata_2fbooks_2eproto() {
    protobuf_AddDesc_google_2fprotobuf_2futil_2finternal_2ftestdata_2fbooks_2eproto();
  }
} static_descriptor_initializer_google_2fprotobuf_2futil_2finternal_2ftestdata_2fbooks_2eproto_;

namespace {

static void MergeFromFail(int line) GOOGLE_ATTRIBUTE_COLD GOOGLE_ATTRIBUTE_NORETURN;
static void MergeFromFail(int line) {
  ::google::protobuf::internal::MergeFromFail(__FILE__, line);
}

}  // namespace


// ===================================================================

const ::google::protobuf::EnumDescriptor* Book_Type_descriptor() {
  protobuf_AssignDescriptorsOnce();
  return Book_Type_descriptor_;
}
bool Book_Type_IsValid(int value) {
  switch (value) {
    case 1:
    case 2:
    case 3:
    case 4:
      return true;
    default:
      return false;
  }
}

#if !defined(_MSC_VER) || _MSC_VER >= 1900
const Book_Type Book::FICTION;
const Book_Type Book::KIDS;
const Book_Type Book::ACTION_AND_ADVENTURE;
const Book_Type Book::arts_and_photography;
const Book_Type Book::Type_MIN;
const Book_Type Book::Type_MAX;
const int Book::Type_ARRAYSIZE;
#endif  // !defined(_MSC_VER) || _MSC_VER >= 1900
#if !defined(_MSC_VER) || _MSC_VER >= 1900
const int Book_Data::kYearFieldNumber;
const int Book_Data::kCopyrightFieldNumber;
#endif  // !defined(_MSC_VER) || _MSC_VER >= 1900

Book_Data::Book_Data()
  : ::google::protobuf::Message(), _internal_metadata_(NULL) {
  if (this != internal_default_instance()) protobuf_InitDefaults_google_2fprotobuf_2futil_2finternal_2ftestdata_2fbooks_2eproto();
  SharedCtor();
  // @@protoc_insertion_point(constructor:google.protobuf.testing.Book.Data)
}

void Book_Data::InitAsDefaultInstance() {
}

Book_Data::Book_Data(const Book_Data& from)
  : ::google::protobuf::Message(),
    _internal_metadata_(NULL) {
  SharedCtor();
  UnsafeMergeFrom(from);
  // @@protoc_insertion_point(copy_constructor:google.protobuf.testing.Book.Data)
}

void Book_Data::SharedCtor() {
  _cached_size_ = 0;
  copyright_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  year_ = 0u;
}

Book_Data::~Book_Data() {
  // @@protoc_insertion_point(destructor:google.protobuf.testing.Book.Data)
  SharedDtor();
}

void Book_Data::SharedDtor() {
  copyright_.DestroyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}

void Book_Data::SetCachedSize(int size) const {
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
}
const ::google::protobuf::Descriptor* Book_Data::descriptor() {
  protobuf_AssignDescriptorsOnce();
  return Book_Data_descriptor_;
}

const Book_Data& Book_Data::default_instance() {
  protobuf_InitDefaults_google_2fprotobuf_2futil_2finternal_2ftestdata_2fbooks_2eproto();
  return *internal_default_instance();
}

::google::protobuf::internal::ExplicitlyConstructed<Book_Data> Book_Data_default_instance_;

Book_Data* Book_Data::New(::google::protobuf::Arena* arena) const {
  Book_Data* n = new Book_Data;
  if (arena != NULL) {
    arena->Own(n);
  }
  return n;
}

void Book_Data::Clear() {
// @@protoc_insertion_point(message_clear_start:google.protobuf.testing.Book.Data)
  if (_has_bits_[0 / 32] & 3u) {
    year_ = 0u;
    if (has_copyright()) {
      copyright_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
    }
  }
  _has_bits_.Clear();
  if (_internal_metadata_.have_unknown_fields()) {
    mutable_unknown_fields()->Clear();
  }
}

bool Book_Data::MergePartialFromCodedStream(
    ::google::protobuf::io::CodedInputStream* input) {
#define DO_(EXPRESSION) if (!GOOGLE_PREDICT_TRUE(EXPRESSION)) goto failure
  ::google::protobuf::uint32 tag;
  // @@protoc_insertion_point(parse_start:google.protobuf.testing.Book.Data)
  for (;;) {
    ::std::pair< ::google::protobuf::uint32, bool> p = input->ReadTagWithCutoff(127);
    tag = p.first;
    if (!p.second) goto handle_unusual;
    switch (::google::protobuf::internal::WireFormatLite::GetTagFieldNumber(tag)) {
      // optional uint32 year = 7;
      case 7: {
        if (tag == 56) {
          set_has_year();
          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::uint32, ::google::protobuf::internal::WireFormatLite::TYPE_UINT32>(
                 input, &year_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(66)) goto parse_copyright;
        break;
      }

      // optional string copyright = 8;
      case 8: {
        if (tag == 66) {
         parse_copyright:
          DO_(::google::protobuf::internal::WireFormatLite::ReadString(
                input, this->mutable_copyright()));
          ::google::protobuf::internal::WireFormat::VerifyUTF8StringNamedField(
            this->copyright().data(), this->copyright().length(),
            ::google::protobuf::internal::WireFormat::PARSE,
            "google.protobuf.testing.Book.Data.copyright");
        } else {
          goto handle_unusual;
        }
        if (input->ExpectAtEnd()) goto success;
        break;
      }

      default: {
      handle_unusual:
        if (tag == 0 ||
            ::google::protobuf::internal::WireFormatLite::GetTagWireType(tag) ==
            ::google::protobuf::internal::WireFormatLite::WIRETYPE_END_GROUP) {
          goto success;
        }
        DO_(::google::protobuf::internal::WireFormat::SkipField(
              input, tag, mutable_unknown_fields()));
        break;
      }
    }
  }
success:
  // @@protoc_insertion_point(parse_success:google.protobuf.testing.Book.Data)
  return true;
failure:
  // @@protoc_insertion_point(parse_failure:google.protobuf.testing.Book.Data)
  return false;
#undef DO_
}

void Book_Data::SerializeWithCachedSizes(
    ::google::protobuf::io::CodedOutputStream* output) const {
  // @@protoc_insertion_point(serialize_start:google.protobuf.testing.Book.Data)
  // optional uint32 year = 7;
  if (has_year()) {
    ::google::protobuf::internal::WireFormatLite::WriteUInt32(7, this->year(), output);
  }

  // optional string copyright = 8;
  if (has_copyright()) {
    ::google::protobuf::internal::WireFormat::VerifyUTF8StringNamedField(
      this->copyright().data(), this->copyright().length(),
      ::google::protobuf::internal::WireFormat::SERIALIZE,
      "google.protobuf.testing.Book.Data.copyright");
    ::google::protobuf::internal::WireFormatLite::WriteStringMaybeAliased(
      8, this->copyright(), output);
  }

  if (_internal_metadata_.have_unknown_fields()) {
    ::google::protobuf::internal::WireFormat::SerializeUnknownFields(
        unknown_fields(), output);
  }
  // @@protoc_insertion_point(serialize_end:google.protobuf.testing.Book.Data)
}

::google::protobuf::uint8* Book_Data::InternalSerializeWithCachedSizesToArray(
    bool deterministic, ::google::protobuf::uint8* target) const {
  (void)deterministic; // Unused
  // @@protoc_insertion_point(serialize_to_array_start:google.protobuf.testing.Book.Data)
  // optional uint32 year = 7;
  if (has_year()) {
    target = ::google::protobuf::internal::WireFormatLite::WriteUInt32ToArray(7, this->year(), target);
  }

  // optional string copyright = 8;
  if (has_copyright()) {
    ::google::protobuf::internal::WireFormat::VerifyUTF8StringNamedField(
      this->copyright().data(), this->copyright().length(),
      ::google::protobuf::internal::WireFormat::SERIALIZE,
      "google.protobuf.testing.Book.Data.copyright");
    target =
      ::google::protobuf::internal::WireFormatLite::WriteStringToArray(
        8, this->copyright(), target);
  }

  if (_internal_metadata_.have_unknown_fields()) {
    target = ::google::protobuf::internal::WireFormat::SerializeUnknownFieldsToArray(
        unknown_fields(), target);
  }
  // @@protoc_insertion_point(serialize_to_array_end:google.protobuf.testing.Book.Data)
  return target;
}

size_t Book_Data::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:google.protobuf.testing.Book.Data)
  size_t total_size = 0;

  if (_has_bits_[0 / 32] & 3u) {
    // optional uint32 year = 7;
    if (has_year()) {
      total_size += 1 +
        ::google::protobuf::internal::WireFormatLite::UInt32Size(
          this->year());
    }

    // optional string copyright = 8;
    if (has_copyright()) {
      total_size += 1 +
        ::google::protobuf::internal::WireFormatLite::StringSize(
          this->copyright());
    }

  }
  if (_internal_metadata_.have_unknown_fields()) {
    total_size +=
      ::google::protobuf::internal::WireFormat::ComputeUnknownFieldsSize(
        unknown_fields());
  }
  int cached_size = ::google::protobuf::internal::ToCachedSize(total_size);
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = cached_size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
  return total_size;
}

void Book_Data::MergeFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_merge_from_start:google.protobuf.testing.Book.Data)
  if (GOOGLE_PREDICT_FALSE(&from == this)) MergeFromFail(__LINE__);
  const Book_Data* source =
      ::google::protobuf::internal::DynamicCastToGenerated<const Book_Data>(
          &from);
  if (source == NULL) {
  // @@protoc_insertion_point(generalized_merge_from_cast_fail:google.protobuf.testing.Book.Data)
    ::google::protobuf::internal::ReflectionOps::Merge(from, this);
  } else {
  // @@protoc_insertion_point(generalized_merge_from_cast_success:google.protobuf.testing.Book.Data)
    UnsafeMergeFrom(*source);
  }
}

void Book_Data::MergeFrom(const Book_Data& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:google.protobuf.testing.Book.Data)
  if (GOOGLE_PREDICT_TRUE(&from != this)) {
    UnsafeMergeFrom(from);
  } else {
    MergeFromFail(__LINE__);
  }
}

void Book_Data::UnsafeMergeFrom(const Book_Data& from) {
  GOOGLE_DCHECK(&from != this);
  if (from._has_bits_[0 / 32] & (0xffu << (0 % 32))) {
    if (from.has_year()) {
      set_year(from.year());
    }
    if (from.has_copyright()) {
      set_has_copyright();
      copyright_.AssignWithDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), from.copyright_);
    }
  }
  if (from._internal_metadata_.have_unknown_fields()) {
    ::google::protobuf::UnknownFieldSet::MergeToInternalMetdata(
      from.unknown_fields(), &_internal_metadata_);
  }
}

void Book_Data::CopyFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_copy_from_start:google.protobuf.testing.Book.Data)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

void Book_Data::CopyFrom(const Book_Data& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:google.protobuf.testing.Book.Data)
  if (&from == this) return;
  Clear();
  UnsafeMergeFrom(from);
}

bool Book_Data::IsInitialized() const {

  return true;
}

void Book_Data::Swap(Book_Data* other) {
  if (other == this) return;
  InternalSwap(other);
}
void Book_Data::InternalSwap(Book_Data* other) {
  std::swap(year_, other->year_);
  copyright_.Swap(&other->copyright_);
  std::swap(_has_bits_[0], other->_has_bits_[0]);
  _internal_metadata_.Swap(&other->_internal_metadata_);
  std::swap(_cached_size_, other->_cached_size_);
}

::google::protobuf::Metadata Book_Data::GetMetadata() const {
  protobuf_AssignDescriptorsOnce();
  ::google::protobuf::Metadata metadata;
  metadata.descriptor = Book_Data_descriptor_;
  metadata.reflection = Book_Data_reflection_;
  return metadata;
}


// -------------------------------------------------------------------

#if !defined(_MSC_VER) || _MSC_VER >= 1900
const int Book_Label::kKeyFieldNumber;
const int Book_Label::kValueFieldNumber;
#endif  // !defined(_MSC_VER) || _MSC_VER >= 1900

Book_Label::Book_Label()
  : ::google::protobuf::Message(), _internal_metadata_(NULL) {
  if (this != internal_default_instance()) protobuf_InitDefaults_google_2fprotobuf_2futil_2finternal_2ftestdata_2fbooks_2eproto();
  SharedCtor();
  // @@protoc_insertion_point(constructor:google.protobuf.testing.Book.Label)
}

void Book_Label::InitAsDefaultInstance() {
}

Book_Label::Book_Label(const Book_Label& from)
  : ::google::protobuf::Message(),
    _internal_metadata_(NULL) {
  SharedCtor();
  UnsafeMergeFrom(from);
  // @@protoc_insertion_point(copy_constructor:google.protobuf.testing.Book.Label)
}

void Book_Label::SharedCtor() {
  _cached_size_ = 0;
  key_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  value_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}

Book_Label::~Book_Label() {
  // @@protoc_insertion_point(destructor:google.protobuf.testing.Book.Label)
  SharedDtor();
}

void Book_Label::SharedDtor() {
  key_.DestroyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  value_.DestroyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}

void Book_Label::SetCachedSize(int size) const {
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
}
const ::google::protobuf::Descriptor* Book_Label::descriptor() {
  protobuf_AssignDescriptorsOnce();
  return Book_Label_descriptor_;
}

const Book_Label& Book_Label::default_instance() {
  protobuf_InitDefaults_google_2fprotobuf_2futil_2finternal_2ftestdata_2fbooks_2eproto();
  return *internal_default_instance();
}

::google::protobuf::internal::ExplicitlyConstructed<Book_Label> Book_Label_default_instance_;

Book_Label* Book_Label::New(::google::protobuf::Arena* arena) const {
  Book_Label* n = new Book_Label;
  if (arena != NULL) {
    arena->Own(n);
  }
  return n;
}

void Book_Label::Clear() {
// @@protoc_insertion_point(message_clear_start:google.protobuf.testing.Book.Label)
  if (_has_bits_[0 / 32] & 3u) {
    if (has_key()) {
      key_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
    }
    if (has_value()) {
      value_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
    }
  }
  _has_bits_.Clear();
  if (_internal_metadata_.have_unknown_fields()) {
    mutable_unknown_fields()->Clear();
  }
}

bool Book_Label::MergePartialFromCodedStream(
    ::google::protobuf::io::CodedInputStream* input) {
#define DO_(EXPRESSION) if (!GOOGLE_PREDICT_TRUE(EXPRESSION)) goto failure
  ::google::protobuf::uint32 tag;
  // @@protoc_insertion_point(parse_start:google.protobuf.testing.Book.Label)
  for (;;) {
    ::std::pair< ::google::protobuf::uint32, bool> p = input->ReadTagWithCutoff(127);
    tag = p.first;
    if (!p.second) goto handle_unusual;
    switch (::google::protobuf::internal::WireFormatLite::GetTagFieldNumber(tag)) {
      // optional string key = 1;
      case 1: {
        if (tag == 10) {
          DO_(::google::protobuf::internal::WireFormatLite::ReadString(
                input, this->mutable_key()));
          ::google::protobuf::internal::WireFormat::VerifyUTF8StringNamedField(
            this->key().data(), this->key().length(),
            ::google::protobuf::internal::WireFormat::PARSE,
            "google.protobuf.testing.Book.Label.key");
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(18)) goto parse_value;
        break;
      }

      // optional string value = 2;
      case 2: {
        if (tag == 18) {
         parse_value:
          DO_(::google::protobuf::internal::WireFormatLite::ReadString(
                input, this->mutable_value()));
          ::google::protobuf::internal::WireFormat::VerifyUTF8StringNamedField(
            this->value().data(), this->value().length(),
            ::google::protobuf::internal::WireFormat::PARSE,
            "google.protobuf.testing.Book.Label.value");
        } else {
          goto handle_unusual;
        }
        if (input->ExpectAtEnd()) goto success;
        break;
      }

      default: {
      handle_unusual:
        if (tag == 0 ||
            ::google::protobuf::internal::WireFormatLite::GetTagWireType(tag) ==
            ::google::protobuf::internal::WireFormatLite::WIRETYPE_END_GROUP) {
          goto success;
        }
        DO_(::google::protobuf::internal::WireFormat::SkipField(
              input, tag, mutable_unknown_fields()));
        break;
      }
    }
  }
success:
  // @@protoc_insertion_point(parse_success:google.protobuf.testing.Book.Label)
  return true;
failure:
  // @@protoc_insertion_point(parse_failure:google.protobuf.testing.Book.Label)
  return false;
#undef DO_
}

void Book_Label::SerializeWithCachedSizes(
    ::google::protobuf::io::CodedOutputStream* output) const {
  // @@protoc_insertion_point(serialize_start:google.protobuf.testing.Book.Label)
  // optional string key = 1;
  if (has_key()) {
    ::google::protobuf::internal::WireFormat::VerifyUTF8StringNamedField(
      this->key().data(), this->key().length(),
      ::google::protobuf::internal::WireFormat::SERIALIZE,
      "google.protobuf.testing.Book.Label.key");
    ::google::protobuf::internal::WireFormatLite::WriteStringMaybeAliased(
      1, this->key(), output);
  }

  // optional string value = 2;
  if (has_value()) {
    ::google::protobuf::internal::WireFormat::VerifyUTF8StringNamedField(
      this->value().data(), this->value().length(),
      ::google::protobuf::internal::WireFormat::SERIALIZE,
      "google.protobuf.testing.Book.Label.value");
    ::google::protobuf::internal::WireFormatLite::WriteStringMaybeAliased(
      2, this->value(), output);
  }

  if (_internal_metadata_.have_unknown_fields()) {
    ::google::protobuf::internal::WireFormat::SerializeUnknownFields(
        unknown_fields(), output);
  }
  // @@protoc_insertion_point(serialize_end:google.protobuf.testing.Book.Label)
}

::google::protobuf::uint8* Book_Label::InternalSerializeWithCachedSizesToArray(
    bool deterministic, ::google::protobuf::uint8* target) const {
  (void)deterministic; // Unused
  // @@protoc_insertion_point(serialize_to_array_start:google.protobuf.testing.Book.Label)
  // optional string key = 1;
  if (has_key()) {
    ::google::protobuf::internal::WireFormat::VerifyUTF8StringNamedField(
      this->key().data(), this->key().length(),
      ::google::protobuf::internal::WireFormat::SERIALIZE,
      "google.protobuf.testing.Book.Label.key");
    target =
      ::google::protobuf::internal::WireFormatLite::WriteStringToArray(
        1, this->key(), target);
  }

  // optional string value = 2;
  if (has_value()) {
    ::google::protobuf::internal::WireFormat::VerifyUTF8StringNamedField(
      this->value().data(), this->value().length(),
      ::google::protobuf::internal::WireFormat::SERIALIZE,
      "google.protobuf.testing.Book.Label.value");
    target =
      ::google::protobuf::internal::WireFormatLite::WriteStringToArray(
        2, this->value(), target);
  }

  if (_internal_metadata_.have_unknown_fields()) {
    target = ::google::protobuf::internal::WireFormat::SerializeUnknownFieldsToArray(
        unknown_fields(), target);
  }
  // @@protoc_insertion_point(serialize_to_array_end:google.protobuf.testing.Book.Label)
  return target;
}

size_t Book_Label::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:google.protobuf.testing.Book.Label)
  size_t total_size = 0;

  if (_has_bits_[0 / 32] & 3u) {
    // optional string key = 1;
    if (has_key()) {
      total_size += 1 +
        ::google::protobuf::internal::WireFormatLite::StringSize(
          this->key());
    }

    // optional string value = 2;
    if (has_value()) {
      total_size += 1 +
        ::google::protobuf::internal::WireFormatLite::StringSize(
          this->value());
    }

  }
  if (_internal_metadata_.have_unknown_fields()) {
    total_size +=
      ::google::protobuf::internal::WireFormat::ComputeUnknownFieldsSize(
        unknown_fields());
  }
  int cached_size = ::google::protobuf::internal::ToCachedSize(total_size);
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = cached_size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
  return total_size;
}

void Book_Label::MergeFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_merge_from_start:google.protobuf.testing.Book.Label)
  if (GOOGLE_PREDICT_FALSE(&from == this)) MergeFromFail(__LINE__);
  const Book_Label* source =
      ::google::protobuf::internal::DynamicCastToGenerated<const Book_Label>(
          &from);
  if (source == NULL) {
  // @@protoc_insertion_point(generalized_merge_from_cast_fail:google.protobuf.testing.Book.Label)
    ::google::protobuf::internal::ReflectionOps::Merge(from, this);
  } else {
  // @@protoc_insertion_point(generalized_merge_from_cast_success:google.protobuf.testing.Book.Label)
    UnsafeMergeFrom(*source);
  }
}

void Book_Label::MergeFrom(const Book_Label& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:google.protobuf.testing.Book.Label)
  if (GOOGLE_PREDICT_TRUE(&from != this)) {
    UnsafeMergeFrom(from);
  } else {
    MergeFromFail(__LINE__);
  }
}

void Book_Label::UnsafeMergeFrom(const Book_Label& from) {
  GOOGLE_DCHECK(&from != this);
  if (from._has_bits_[0 / 32] & (0xffu << (0 % 32))) {
    if (from.has_key()) {
      set_has_key();
      key_.AssignWithDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), from.key_);
    }
    if (from.has_value()) {
      set_has_value();
      value_.AssignWithDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), from.value_);
    }
  }
  if (from._internal_metadata_.have_unknown_fields()) {
    ::google::protobuf::UnknownFieldSet::MergeToInternalMetdata(
      from.unknown_fields(), &_internal_metadata_);
  }
}

void Book_Label::CopyFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_copy_from_start:google.protobuf.testing.Book.Label)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

void Book_Label::CopyFrom(const Book_Label& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:google.protobuf.testing.Book.Label)
  if (&from == this) return;
  Clear();
  UnsafeMergeFrom(from);
}

bool Book_Label::IsInitialized() const {

  return true;
}

void Book_Label::Swap(Book_Label* other) {
  if (other == this) return;
  InternalSwap(other);
}
void Book_Label::InternalSwap(Book_Label* other) {
  key_.Swap(&other->key_);
  value_.Swap(&other->value_);
  std::swap(_has_bits_[0], other->_has_bits_[0]);
  _internal_metadata_.Swap(&other->_internal_metadata_);
  std::swap(_cached_size_, other->_cached_size_);
}

::google::protobuf::Metadata Book_Label::GetMetadata() const {
  protobuf_AssignDescriptorsOnce();
  ::google::protobuf::Metadata metadata;
  metadata.descriptor = Book_Label_descriptor_;
  metadata.reflection = Book_Label_reflection_;
  return metadata;
}


// -------------------------------------------------------------------

#if !defined(_MSC_VER) || _MSC_VER >= 1900
const int Book::kTitleFieldNumber;
const int Book::kAuthorFieldNumber;
const int Book::kLengthFieldNumber;
const int Book::kPublishedFieldNumber;
const int Book::kContentFieldNumber;
const int Book::kDataFieldNumber;
const int Book::kPublisherFieldNumber;
const int Book::kLabelsFieldNumber;
const int Book::kTypeFieldNumber;
#endif  // !defined(_MSC_VER) || _MSC_VER >= 1900

Book::Book()
  : ::google::protobuf::Message(), _internal_metadata_(NULL) {
  if (this != internal_default_instance()) protobuf_InitDefaults_google_2fprotobuf_2futil_2finternal_2ftestdata_2fbooks_2eproto();
  SharedCtor();
  // @@protoc_insertion_point(constructor:google.protobuf.testing.Book)
}

void Book::InitAsDefaultInstance() {
  author_ = const_cast< ::google::protobuf::testing::Author*>(
      ::google::protobuf::testing::Author::internal_default_instance());
  data_ = const_cast< ::google::protobuf::testing::Book_Data*>(
      ::google::protobuf::testing::Book_Data::internal_default_instance());
  publisher_ = const_cast< ::google::protobuf::testing::Publisher*>(
      ::google::protobuf::testing::Publisher::internal_default_instance());
}

Book::Book(const Book& from)
  : ::google::protobuf::Message(),
    _internal_metadata_(NULL) {
  SharedCtor();
  UnsafeMergeFrom(from);
  // @@protoc_insertion_point(copy_constructor:google.protobuf.testing.Book)
}

void Book::SharedCtor() {
  _cached_size_ = 0;
  title_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  content_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  author_ = NULL;
  data_ = NULL;
  publisher_ = NULL;
  ::memset(&published_, 0, reinterpret_cast<char*>(&length_) -
    reinterpret_cast<char*>(&published_) + sizeof(length_));
  type_ = 1;
}

Book::~Book() {
  // @@protoc_insertion_point(destructor:google.protobuf.testing.Book)
  SharedDtor();
}

void Book::SharedDtor() {
  title_.DestroyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  content_.DestroyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  if (this != &Book_default_instance_.get()) {
    delete author_;
    delete data_;
    delete publisher_;
  }
}

void Book::SetCachedSize(int size) const {
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
}
const ::google::protobuf::Descriptor* Book::descriptor() {
  protobuf_AssignDescriptorsOnce();
  return Book_descriptor_;
}

const Book& Book::default_instance() {
  protobuf_InitDefaults_google_2fprotobuf_2futil_2finternal_2ftestdata_2fbooks_2eproto();
  return *internal_default_instance();
}

::google::protobuf::internal::ExplicitlyConstructed<Book> Book_default_instance_;

Book* Book::New(::google::protobuf::Arena* arena) const {
  Book* n = new Book;
  if (arena != NULL) {
    arena->Own(n);
  }
  return n;
}

void Book::Clear() {
// @@protoc_insertion_point(message_clear_start:google.protobuf.testing.Book)
  _extensions_.Clear();
#if defined(__clang__)
#define ZR_HELPER_(f) \
  _Pragma("clang diagnostic push") \
  _Pragma("clang diagnostic ignored \"-Winvalid-offsetof\"") \
  __builtin_offsetof(Book, f) \
  _Pragma("clang diagnostic pop")
#else
#define ZR_HELPER_(f) reinterpret_cast<char*>(\
  &reinterpret_cast<Book*>(16)->f)
#endif

#define ZR_(first, last) do {\
  ::memset(&(first), 0,\
           ZR_HELPER_(last) - ZR_HELPER_(first) + sizeof(last));\
} while (0)

  if (_has_bits_[0 / 32] & 127u) {
    ZR_(published_, length_);
    if (has_title()) {
      title_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
    }
    if (has_author()) {
      if (author_ != NULL) author_->::google::protobuf::testing::Author::Clear();
    }
    if (has_content()) {
      content_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
    }
    if (has_data()) {
      if (data_ != NULL) data_->::google::protobuf::testing::Book_Data::Clear();
    }
    if (has_publisher()) {
      if (publisher_ != NULL) publisher_->::google::protobuf::testing::Publisher::Clear();
    }
  }
  type_ = 1;

#undef ZR_HELPER_
#undef ZR_

  labels_.Clear();
  _has_bits_.Clear();
  if (_internal_metadata_.have_unknown_fields()) {
    mutable_unknown_fields()->Clear();
  }
}

bool Book::MergePartialFromCodedStream(
    ::google::protobuf::io::CodedInputStream* input) {
#define DO_(EXPRESSION) if (!GOOGLE_PREDICT_TRUE(EXPRESSION)) goto failure
  ::google::protobuf::uint32 tag;
  // @@protoc_insertion_point(parse_start:google.protobuf.testing.Book)
  for (;;) {
    ::std::pair< ::google::protobuf::uint32, bool> p = input->ReadTagWithCutoff(127);
    tag = p.first;
    if (!p.second) goto handle_unusual;
    switch (::google::protobuf::internal::WireFormatLite::GetTagFieldNumber(tag)) {
      // optional string title = 1;
      case 1: {
        if (tag == 10) {
          DO_(::google::protobuf::internal::WireFormatLite::ReadString(
                input, this->mutable_title()));
          ::google::protobuf::internal::WireFormat::VerifyUTF8StringNamedField(
            this->title().data(), this->title().length(),
            ::google::protobuf::internal::WireFormat::PARSE,
            "google.protobuf.testing.Book.title");
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(18)) goto parse_author;
        break;
      }

      // optional .google.protobuf.testing.Author author = 2;
      case 2: {
        if (tag == 18) {
         parse_author:
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessageNoVirtual(
               input, mutable_author()));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(24)) goto parse_length;
        break;
      }

      // optional uint32 length = 3;
      case 3: {
        if (tag == 24) {
         parse_length:
          set_has_length();
          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::uint32, ::google::protobuf::internal::WireFormatLite::TYPE_UINT32>(
                 input, &length_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(32)) goto parse_published;
        break;
      }

      // optional int64 published = 4;
      case 4: {
        if (tag == 32) {
         parse_published:
          set_has_published();
          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_INT64>(
                 input, &published_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(42)) goto parse_content;
        break;
      }

      // optional bytes content = 5;
      case 5: {
        if (tag == 42) {
         parse_content:
          DO_(::google::protobuf::internal::WireFormatLite::ReadBytes(
                input, this->mutable_content()));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(51)) goto parse_data;
        break;
      }

      // optional group Data = 6 { ... };
      case 6: {
        if (tag == 51) {
         parse_data:
          DO_(::google::protobuf::internal::WireFormatLite::ReadGroupNoVirtual(
                6, input, mutable_data()));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(74)) goto parse_publisher;
        break;
      }

      // optional .google.protobuf.testing.Publisher publisher = 9;
      case 9: {
        if (tag == 74) {
         parse_publisher:
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessageNoVirtual(
               input, mutable_publisher()));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(82)) goto parse_labels;
        break;
      }

      // repeated .google.protobuf.testing.Book.Label labels = 10;
      case 10: {
        if (tag == 82) {
         parse_labels:
          DO_(input->IncrementRecursionDepth());
         parse_loop_labels:
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessageNoVirtualNoRecursionDepth(
                input, add_labels()));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(82)) goto parse_loop_labels;
        input->UnsafeDecrementRecursionDepth();
        if (input->ExpectTag(88)) goto parse_type;
        break;
      }

      // optional .google.protobuf.testing.Book.Type type = 11;
      case 11: {
        if (tag == 88) {
         parse_type:
          int value;
          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   int, ::google::protobuf::internal::WireFormatLite::TYPE_ENUM>(
                 input, &value)));
          if (::google::protobuf::testing::Book_Type_IsValid(value)) {
            set_type(static_cast< ::google::protobuf::testing::Book_Type >(value));
          } else {
            mutable_unknown_fields()->AddVarint(11, value);
          }
        } else {
          goto handle_unusual;
        }
        if (input->ExpectAtEnd()) goto success;
        break;
      }

      default: {
      handle_unusual:
        if (tag == 0 ||
            ::google::protobuf::internal::WireFormatLite::GetTagWireType(tag) ==
            ::google::protobuf::internal::WireFormatLite::WIRETYPE_END_GROUP) {
          goto success;
        }
        if ((1600u <= tag && tag < 4000u)) {
          DO_(_extensions_.ParseField(tag, input, internal_default_instance(),
                                      mutable_unknown_fields()));
          continue;
        }
        DO_(::google::protobuf::internal::WireFormat::SkipField(
              input, tag, mutable_unknown_fields()));
        break;
      }
    }
  }
success:
  // @@protoc_insertion_point(parse_success:google.protobuf.testing.Book)
  return true;
failure:
  // @@protoc_insertion_point(parse_failure:google.protobuf.testing.Book)
  return false;
#undef DO_
}

void Book::SerializeWithCachedSizes(
    ::google::protobuf::io::CodedOutputStream* output) const {
  // @@protoc_insertion_point(serialize_start:google.protobuf.testing.Book)
  // optional string title = 1;
  if (has_title()) {
    ::google::protobuf::internal::WireFormat::VerifyUTF8StringNamedField(
      this->title().data(), this->title().length(),
      ::google::protobuf::internal::WireFormat::SERIALIZE,
      "google.protobuf.testing.Book.title");
    ::google::protobuf::internal::WireFormatLite::WriteStringMaybeAliased(
      1, this->title(), output);
  }

  // optional .google.protobuf.testing.Author author = 2;
  if (has_author()) {
    ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
      2, *this->author_, output);
  }

  // optional uint32 length = 3;
  if (has_length()) {
    ::google::protobuf::internal::WireFormatLite::WriteUInt32(3, this->length(), output);
  }

  // optional int64 published = 4;
  if (has_published()) {
    ::google::protobuf::internal::WireFormatLite::WriteInt64(4, this->published(), output);
  }

  // optional bytes content = 5;
  if (has_content()) {
    ::google::protobuf::internal::WireFormatLite::WriteBytesMaybeAliased(
      5, this->content(), output);
  }

  // optional group Data = 6 { ... };
  if (has_data()) {
    ::google::protobuf::internal::WireFormatLite::WriteGroupMaybeToArray(
      6, *this->data_, output);
  }

  // optional .google.protobuf.testing.Publisher publisher = 9;
  if (has_publisher()) {
    ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
      9, *this->publisher_, output);
  }

  // repeated .google.protobuf.testing.Book.Label labels = 10;
  for (unsigned int i = 0, n = this->labels_size(); i < n; i++) {
    ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
      10, this->labels(i), output);
  }

  // optional .google.protobuf.testing.Book.Type type = 11;
  if (has_type()) {
    ::google::protobuf::internal::WireFormatLite::WriteEnum(
      11, this->type(), output);
  }

  // Extension range [200, 500)
  _extensions_.SerializeWithCachedSizes(
      200, 500, output);

  if (_internal_metadata_.have_unknown_fields()) {
    ::google::protobuf::internal::WireFormat::SerializeUnknownFields(
        unknown_fields(), output);
  }
  // @@protoc_insertion_point(serialize_end:google.protobuf.testing.Book)
}

::google::protobuf::uint8* Book::InternalSerializeWithCachedSizesToArray(
    bool deterministic, ::google::protobuf::uint8* target) const {
  (void)deterministic; // Unused
  // @@protoc_insertion_point(serialize_to_array_start:google.protobuf.testing.Book)
  // optional string title = 1;
  if (has_title()) {
    ::google::protobuf::internal::WireFormat::VerifyUTF8StringNamedField(
      this->title().data(), this->title().length(),
      ::google::protobuf::internal::WireFormat::SERIALIZE,
      "google.protobuf.testing.Book.title");
    target =
      ::google::protobuf::internal::WireFormatLite::WriteStringToArray(
        1, this->title(), target);
  }

  // optional .google.protobuf.testing.Author author = 2;
  if (has_author()) {
    target = ::google::protobuf::internal::WireFormatLite::
      InternalWriteMessageNoVirtualToArray(
        2, *this->author_, false, target);
  }

  // optional uint32 length = 3;
  if (has_length()) {
    target = ::google::protobuf::internal::WireFormatLite::WriteUInt32ToArray(3, this->length(), target);
  }

  // optional int64 published = 4;
  if (has_published()) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt64ToArray(4, this->published(), target);
  }

  // optional bytes content = 5;
  if (has_content()) {
    target =
      ::google::protobuf::internal::WireFormatLite::WriteBytesToArray(
        5, this->content(), target);
  }

  // optional group Data = 6 { ... };
  if (has_data()) {
    target = ::google::protobuf::internal::WireFormatLite::
      InternalWriteGroupNoVirtualToArray(
        6, *this->data_, false, target);
  }

  // optional .google.protobuf.testing.Publisher publisher = 9;
  if (has_publisher()) {
    target = ::google::protobuf::internal::WireFormatLite::
      InternalWriteMessageNoVirtualToArray(
        9, *this->publisher_, false, target);
  }

  // repeated .google.protobuf.testing.Book.Label labels = 10;
  for (unsigned int i = 0, n = this->labels_size(); i < n; i++) {
    target = ::google::protobuf::internal::WireFormatLite::
      InternalWriteMessageNoVirtualToArray(
        10, this->labels(i), false, target);
  }

  // optional .google.protobuf.testing.Book.Type type = 11;
  if (has_type()) {
    target = ::google::protobuf::internal::WireFormatLite::WriteEnumToArray(
      11, this->type(), target);
  }

  // Extension range [200, 500)
  target = _extensions_.InternalSerializeWithCachedSizesToArray(
      200, 500, false, target);

  if (_internal_metadata_.have_unknown_fields()) {
    target = ::google::protobuf::internal::WireFormat::SerializeUnknownFieldsToArray(
        unknown_fields(), target);
  }
  // @@protoc_insertion_point(serialize_to_array_end:google.protobuf.testing.Book)
  return target;
}

size_t Book::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:google.protobuf.testing.Book)
  size_t total_size = 0;

  if (_has_bits_[0 / 32] & 127u) {
    // optional string title = 1;
    if (has_title()) {
      total_size += 1 +
        ::google::protobuf::internal::WireFormatLite::StringSize(
          this->title());
    }

    // optional .google.protobuf.testing.Author author = 2;
    if (has_author()) {
      total_size += 1 +
        ::google::protobuf::internal::WireFormatLite::MessageSizeNoVirtual(
          *this->author_);
    }

    // optional uint32 length = 3;
    if (has_length()) {
      total_size += 1 +
        ::google::protobuf::internal::WireFormatLite::UInt32Size(
          this->length());
    }

    // optional int64 published = 4;
    if (has_published()) {
      total_size += 1 +
        ::google::protobuf::internal::WireFormatLite::Int64Size(
          this->published());
    }

    // optional bytes content = 5;
    if (has_content()) {
      total_size += 1 +
        ::google::protobuf::internal::WireFormatLite::BytesSize(
          this->content());
    }

    // optional group Data = 6 { ... };
    if (has_data()) {
      total_size += 2 +
        ::google::protobuf::internal::WireFormatLite::GroupSizeNoVirtual(
          *this->data_);
    }

    // optional .google.protobuf.testing.Publisher publisher = 9;
    if (has_publisher()) {
      total_size += 1 +
        ::google::protobuf::internal::WireFormatLite::MessageSizeNoVirtual(
          *this->publisher_);
    }

  }
  // optional .google.protobuf.testing.Book.Type type = 11;
  if (has_type()) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::EnumSize(this->type());
  }

  // repeated .google.protobuf.testing.Book.Label labels = 10;
  {
    unsigned int count = this->labels_size();
    total_size += 1UL * count;
    for (unsigned int i = 0; i < count; i++) {
      total_size +=
        ::google::protobuf::internal::WireFormatLite::MessageSizeNoVirtual(
          this->labels(i));
    }
  }

  total_size += _extensions_.ByteSize();

  if (_internal_metadata_.have_unknown_fields()) {
    total_size +=
      ::google::protobuf::internal::WireFormat::ComputeUnknownFieldsSize(
        unknown_fields());
  }
  int cached_size = ::google::protobuf::internal::ToCachedSize(total_size);
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = cached_size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
  return total_size;
}

void Book::MergeFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_merge_from_start:google.protobuf.testing.Book)
  if (GOOGLE_PREDICT_FALSE(&from == this)) MergeFromFail(__LINE__);
  const Book* source =
      ::google::protobuf::internal::DynamicCastToGenerated<const Book>(
          &from);
  if (source == NULL) {
  // @@protoc_insertion_point(generalized_merge_from_cast_fail:google.protobuf.testing.Book)
    ::google::protobuf::internal::ReflectionOps::Merge(from, this);
  } else {
  // @@protoc_insertion_point(generalized_merge_from_cast_success:google.protobuf.testing.Book)
    UnsafeMergeFrom(*source);
  }
}

void Book::MergeFrom(const Book& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:google.protobuf.testing.Book)
  if (GOOGLE_PREDICT_TRUE(&from != this)) {
    UnsafeMergeFrom(from);
  } else {
    MergeFromFail(__LINE__);
  }
}

void Book::UnsafeMergeFrom(const Book& from) {
  GOOGLE_DCHECK(&from != this);
  labels_.MergeFrom(from.labels_);
  if (from._has_bits_[0 / 32] & (0xffu << (0 % 32))) {
    if (from.has_title()) {
      set_has_title();
      title_.AssignWithDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), from.title_);
    }
    if (from.has_author()) {
      mutable_author()->::google::protobuf::testing::Author::MergeFrom(from.author());
    }
    if (from.has_length()) {
      set_length(from.length());
    }
    if (from.has_published()) {
      set_published(from.published());
    }
    if (from.has_content()) {
      set_has_content();
      content_.AssignWithDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), from.content_);
    }
    if (from.has_data()) {
      mutable_data()->::google::protobuf::testing::Book_Data::MergeFrom(from.data());
    }
    if (from.has_publisher()) {
      mutable_publisher()->::google::protobuf::testing::Publisher::MergeFrom(from.publisher());
    }
  }
  if (from._has_bits_[8 / 32] & (0xffu << (8 % 32))) {
    if (from.has_type()) {
      set_type(from.type());
    }
  }
  _extensions_.MergeFrom(from._extensions_);
  if (from._internal_metadata_.have_unknown_fields()) {
    ::google::protobuf::UnknownFieldSet::MergeToInternalMetdata(
      from.unknown_fields(), &_internal_metadata_);
  }
}

void Book::CopyFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_copy_from_start:google.protobuf.testing.Book)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

void Book::CopyFrom(const Book& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:google.protobuf.testing.Book)
  if (&from == this) return;
  Clear();
  UnsafeMergeFrom(from);
}

bool Book::IsInitialized() const {

  if (has_publisher()) {
    if (!this->publisher_->IsInitialized()) return false;
  }

  if (!_extensions_.IsInitialized()) {
    return false;
  }
  return true;
}

void Book::Swap(Book* other) {
  if (other == this) return;
  InternalSwap(other);
}
void Book::InternalSwap(Book* other) {
  title_.Swap(&other->title_);
  std::swap(author_, other->author_);
  std::swap(length_, other->length_);
  std::swap(published_, other->published_);
  content_.Swap(&other->content_);
  std::swap(data_, other->data_);
  std::swap(publisher_, other->publisher_);
  labels_.UnsafeArenaSwap(&other->labels_);
  std::swap(type_, other->type_);
  std::swap(_has_bits_[0], other->_has_bits_[0]);
  _internal_metadata_.Swap(&other->_internal_metadata_);
  std::swap(_cached_size_, other->_cached_size_);
  _extensions_.Swap(&other->_extensions_);
}

::google::protobuf::Metadata Book::GetMetadata() const {
  protobuf_AssignDescriptorsOnce();
  ::google::protobuf::Metadata metadata;
  metadata.descriptor = Book_descriptor_;
  metadata.reflection = Book_reflection_;
  return metadata;
}

#if PROTOBUF_INLINE_NOT_IN_HEADERS
// Book_Data

// optional uint32 year = 7;
bool Book_Data::has_year() const {
  return (_has_bits_[0] & 0x00000001u) != 0;
}
void Book_Data::set_has_year() {
  _has_bits_[0] |= 0x00000001u;
}
void Book_Data::clear_has_year() {
  _has_bits_[0] &= ~0x00000001u;
}
void Book_Data::clear_year() {
  year_ = 0u;
  clear_has_year();
}
::google::protobuf::uint32 Book_Data::year() const {
  // @@protoc_insertion_point(field_get:google.protobuf.testing.Book.Data.year)
  return year_;
}
void Book_Data::set_year(::google::protobuf::uint32 value) {
  set_has_year();
  year_ = value;
  // @@protoc_insertion_point(field_set:google.protobuf.testing.Book.Data.year)
}

// optional string copyright = 8;
bool Book_Data::has_copyright() const {
  return (_has_bits_[0] & 0x00000002u) != 0;
}
void Book_Data::set_has_copyright() {
  _has_bits_[0] |= 0x00000002u;
}
void Book_Data::clear_has_copyright() {
  _has_bits_[0] &= ~0x00000002u;
}
void Book_Data::clear_copyright() {
  copyright_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  clear_has_copyright();
}
const ::std::string& Book_Data::copyright() const {
  // @@protoc_insertion_point(field_get:google.protobuf.testing.Book.Data.copyright)
  return copyright_.GetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
void Book_Data::set_copyright(const ::std::string& value) {
  set_has_copyright();
  copyright_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:google.protobuf.testing.Book.Data.copyright)
}
void Book_Data::set_copyright(const char* value) {
  set_has_copyright();
  copyright_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:google.protobuf.testing.Book.Data.copyright)
}
void Book_Data::set_copyright(const char* value, size_t size) {
  set_has_copyright();
  copyright_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:google.protobuf.testing.Book.Data.copyright)
}
::std::string* Book_Data::mutable_copyright() {
  set_has_copyright();
  // @@protoc_insertion_point(field_mutable:google.protobuf.testing.Book.Data.copyright)
  return copyright_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
::std::string* Book_Data::release_copyright() {
  // @@protoc_insertion_point(field_release:google.protobuf.testing.Book.Data.copyright)
  clear_has_copyright();
  return copyright_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
void Book_Data::set_allocated_copyright(::std::string* copyright) {
  if (copyright != NULL) {
    set_has_copyright();
  } else {
    clear_has_copyright();
  }
  copyright_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), copyright);
  // @@protoc_insertion_point(field_set_allocated:google.protobuf.testing.Book.Data.copyright)
}

inline const Book_Data* Book_Data::internal_default_instance() {
  return &Book_Data_default_instance_.get();
}
// -------------------------------------------------------------------

// Book_Label

// optional string key = 1;
bool Book_Label::has_key() const {
  return (_has_bits_[0] & 0x00000001u) != 0;
}
void Book_Label::set_has_key() {
  _has_bits_[0] |= 0x00000001u;
}
void Book_Label::clear_has_key() {
  _has_bits_[0] &= ~0x00000001u;
}
void Book_Label::clear_key() {
  key_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  clear_has_key();
}
const ::std::string& Book_Label::key() const {
  // @@protoc_insertion_point(field_get:google.protobuf.testing.Book.Label.key)
  return key_.GetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
void Book_Label::set_key(const ::std::string& value) {
  set_has_key();
  key_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:google.protobuf.testing.Book.Label.key)
}
void Book_Label::set_key(const char* value) {
  set_has_key();
  key_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:google.protobuf.testing.Book.Label.key)
}
void Book_Label::set_key(const char* value, size_t size) {
  set_has_key();
  key_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:google.protobuf.testing.Book.Label.key)
}
::std::string* Book_Label::mutable_key() {
  set_has_key();
  // @@protoc_insertion_point(field_mutable:google.protobuf.testing.Book.Label.key)
  return key_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
::std::string* Book_Label::release_key() {
  // @@protoc_insertion_point(field_release:google.protobuf.testing.Book.Label.key)
  clear_has_key();
  return key_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
void Book_Label::set_allocated_key(::std::string* key) {
  if (key != NULL) {
    set_has_key();
  } else {
    clear_has_key();
  }
  key_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), key);
  // @@protoc_insertion_point(field_set_allocated:google.protobuf.testing.Book.Label.key)
}

// optional string value = 2;
bool Book_Label::has_value() const {
  return (_has_bits_[0] & 0x00000002u) != 0;
}
void Book_Label::set_has_value() {
  _has_bits_[0] |= 0x00000002u;
}
void Book_Label::clear_has_value() {
  _has_bits_[0] &= ~0x00000002u;
}
void Book_Label::clear_value() {
  value_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  clear_has_value();
}
const ::std::string& Book_Label::value() const {
  // @@protoc_insertion_point(field_get:google.protobuf.testing.Book.Label.value)
  return value_.GetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
void Book_Label::set_value(const ::std::string& value) {
  set_has_value();
  value_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:google.protobuf.testing.Book.Label.value)
}
void Book_Label::set_value(const char* value) {
  set_has_value();
  value_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:google.protobuf.testing.Book.Label.value)
}
void Book_Label::set_value(const char* value, size_t size) {
  set_has_value();
  value_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:google.protobuf.testing.Book.Label.value)
}
::std::string* Book_Label::mutable_value() {
  set_has_value();
  // @@protoc_insertion_point(field_mutable:google.protobuf.testing.Book.Label.value)
  return value_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
::std::string* Book_Label::release_value() {
  // @@protoc_insertion_point(field_release:google.protobuf.testing.Book.Label.value)
  clear_has_value();
  return value_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
void Book_Label::set_allocated_value(::std::string* value) {
  if (value != NULL) {
    set_has_value();
  } else {
    clear_has_value();
  }
  value_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set_allocated:google.protobuf.testing.Book.Label.value)
}

inline const Book_Label* Book_Label::internal_default_instance() {
  return &Book_Label_default_instance_.get();
}
// -------------------------------------------------------------------

// Book

// optional string title = 1;
bool Book::has_title() const {
  return (_has_bits_[0] & 0x00000001u) != 0;
}
void Book::set_has_title() {
  _has_bits_[0] |= 0x00000001u;
}
void Book::clear_has_title() {
  _has_bits_[0] &= ~0x00000001u;
}
void Book::clear_title() {
  title_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  clear_has_title();
}
const ::std::string& Book::title() const {
  // @@protoc_insertion_point(field_get:google.protobuf.testing.Book.title)
  return title_.GetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
void Book::set_title(const ::std::string& value) {
  set_has_title();
  title_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:google.protobuf.testing.Book.title)
}
void Book::set_title(const char* value) {
  set_has_title();
  title_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:google.protobuf.testing.Book.title)
}
void Book::set_title(const char* value, size_t size) {
  set_has_title();
  title_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:google.protobuf.testing.Book.title)
}
::std::string* Book::mutable_title() {
  set_has_title();
  // @@protoc_insertion_point(field_mutable:google.protobuf.testing.Book.title)
  return title_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
::std::string* Book::release_title() {
  // @@protoc_insertion_point(field_release:google.protobuf.testing.Book.title)
  clear_has_title();
  return title_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
void Book::set_allocated_title(::std::string* title) {
  if (title != NULL) {
    set_has_title();
  } else {
    clear_has_title();
  }
  title_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), title);
  // @@protoc_insertion_point(field_set_allocated:google.protobuf.testing.Book.title)
}

// optional .google.protobuf.testing.Author author = 2;
bool Book::has_author() const {
  return (_has_bits_[0] & 0x00000002u) != 0;
}
void Book::set_has_author() {
  _has_bits_[0] |= 0x00000002u;
}
void Book::clear_has_author() {
  _has_bits_[0] &= ~0x00000002u;
}
void Book::clear_author() {
  if (author_ != NULL) author_->::google::protobuf::testing::Author::Clear();
  clear_has_author();
}
const ::google::protobuf::testing::Author& Book::author() const {
  // @@protoc_insertion_point(field_get:google.protobuf.testing.Book.author)
  return author_ != NULL ? *author_
                         : *::google::protobuf::testing::Author::internal_default_instance();
}
::google::protobuf::testing::Author* Book::mutable_author() {
  set_has_author();
  if (author_ == NULL) {
    author_ = new ::google::protobuf::testing::Author;
  }
  // @@protoc_insertion_point(field_mutable:google.protobuf.testing.Book.author)
  return author_;
}
::google::protobuf::testing::Author* Book::release_author() {
  // @@protoc_insertion_point(field_release:google.protobuf.testing.Book.author)
  clear_has_author();
  ::google::protobuf::testing::Author* temp = author_;
  author_ = NULL;
  return temp;
}
void Book::set_allocated_author(::google::protobuf::testing::Author* author) {
  delete author_;
  author_ = author;
  if (author) {
    set_has_author();
  } else {
    clear_has_author();
  }
  // @@protoc_insertion_point(field_set_allocated:google.protobuf.testing.Book.author)
}

// optional uint32 length = 3;
bool Book::has_length() const {
  return (_has_bits_[0] & 0x00000004u) != 0;
}
void Book::set_has_length() {
  _has_bits_[0] |= 0x00000004u;
}
void Book::clear_has_length() {
  _has_bits_[0] &= ~0x00000004u;
}
void Book::clear_length() {
  length_ = 0u;
  clear_has_length();
}
::google::protobuf::uint32 Book::length() const {
  // @@protoc_insertion_point(field_get:google.protobuf.testing.Book.length)
  return length_;
}
void Book::set_length(::google::protobuf::uint32 value) {
  set_has_length();
  length_ = value;
  // @@protoc_insertion_point(field_set:google.protobuf.testing.Book.length)
}

// optional int64 published = 4;
bool Book::has_published() const {
  return (_has_bits_[0] & 0x00000008u) != 0;
}
void Book::set_has_published() {
  _has_bits_[0] |= 0x00000008u;
}
void Book::clear_has_published() {
  _has_bits_[0] &= ~0x00000008u;
}
void Book::clear_published() {
  published_ = GOOGLE_LONGLONG(0);
  clear_has_published();
}
::google::protobuf::int64 Book::published() const {
  // @@protoc_insertion_point(field_get:google.protobuf.testing.Book.published)
  return published_;
}
void Book::set_published(::google::protobuf::int64 value) {
  set_has_published();
  published_ = value;
  // @@protoc_insertion_point(field_set:google.protobuf.testing.Book.published)
}

// optional bytes content = 5;
bool Book::has_content() const {
  return (_has_bits_[0] & 0x00000010u) != 0;
}
void Book::set_has_content() {
  _has_bits_[0] |= 0x00000010u;
}
void Book::clear_has_content() {
  _has_bits_[0] &= ~0x00000010u;
}
void Book::clear_content() {
  content_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  clear_has_content();
}
const ::std::string& Book::content() const {
  // @@protoc_insertion_point(field_get:google.protobuf.testing.Book.content)
  return content_.GetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
void Book::set_content(const ::std::string& value) {
  set_has_content();
  content_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:google.protobuf.testing.Book.content)
}
void Book::set_content(const char* value) {
  set_has_content();
  content_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:google.protobuf.testing.Book.content)
}
void Book::set_content(const void* value, size_t size) {
  set_has_content();
  content_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:google.protobuf.testing.Book.content)
}
::std::string* Book::mutable_content() {
  set_has_content();
  // @@protoc_insertion_point(field_mutable:google.protobuf.testing.Book.content)
  return content_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
::std::string* Book::release_content() {
  // @@protoc_insertion_point(field_release:google.protobuf.testing.Book.content)
  clear_has_content();
  return content_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
void Book::set_allocated_content(::std::string* content) {
  if (content != NULL) {
    set_has_content();
  } else {
    clear_has_content();
  }
  content_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), content);
  // @@protoc_insertion_point(field_set_allocated:google.protobuf.testing.Book.content)
}

// optional group Data = 6 { ... };
bool Book::has_data() const {
  return (_has_bits_[0] & 0x00000020u) != 0;
}
void Book::set_has_data() {
  _has_bits_[0] |= 0x00000020u;
}
void Book::clear_has_data() {
  _has_bits_[0] &= ~0x00000020u;
}
void Book::clear_data() {
  if (data_ != NULL) data_->::google::protobuf::testing::Book_Data::Clear();
  clear_has_data();
}
const ::google::protobuf::testing::Book_Data& Book::data() const {
  // @@protoc_insertion_point(field_get:google.protobuf.testing.Book.data)
  return data_ != NULL ? *data_
                         : *::google::protobuf::testing::Book_Data::internal_default_instance();
}
::google::protobuf::testing::Book_Data* Book::mutable_data() {
  set_has_data();
  if (data_ == NULL) {
    data_ = new ::google::protobuf::testing::Book_Data;
  }
  // @@protoc_insertion_point(field_mutable:google.protobuf.testing.Book.data)
  return data_;
}
::google::protobuf::testing::Book_Data* Book::release_data() {
  // @@protoc_insertion_point(field_release:google.protobuf.testing.Book.data)
  clear_has_data();
  ::google::protobuf::testing::Book_Data* temp = data_;
  data_ = NULL;
  return temp;
}
void Book::set_allocated_data(::google::protobuf::testing::Book_Data* data) {
  delete data_;
  data_ = data;
  if (data) {
    set_has_data();
  } else {
    clear_has_data();
  }
  // @@protoc_insertion_point(field_set_allocated:google.protobuf.testing.Book.data)
}

// optional .google.protobuf.testing.Publisher publisher = 9;
bool Book::has_publisher() const {
  return (_has_bits_[0] & 0x00000040u) != 0;
}
void Book::set_has_publisher() {
  _has_bits_[0] |= 0x00000040u;
}
void Book::clear_has_publisher() {
  _has_bits_[0] &= ~0x00000040u;
}
void Book::clear_publisher() {
  if (publisher_ != NULL) publisher_->::google::protobuf::testing::Publisher::Clear();
  clear_has_publisher();
}
const ::google::protobuf::testing::Publisher& Book::publisher() const {
  // @@protoc_insertion_point(field_get:google.protobuf.testing.Book.publisher)
  return publisher_ != NULL ? *publisher_
                         : *::google::protobuf::testing::Publisher::internal_default_instance();
}
::google::protobuf::testing::Publisher* Book::mutable_publisher() {
  set_has_publisher();
  if (publisher_ == NULL) {
    publisher_ = new ::google::protobuf::testing::Publisher;
  }
  // @@protoc_insertion_point(field_mutable:google.protobuf.testing.Book.publisher)
  return publisher_;
}
::google::protobuf::testing::Publisher* Book::release_publisher() {
  // @@protoc_insertion_point(field_release:google.protobuf.testing.Book.publisher)
  clear_has_publisher();
  ::google::protobuf::testing::Publisher* temp = publisher_;
  publisher_ = NULL;
  return temp;
}
void Book::set_allocated_publisher(::google::protobuf::testing::Publisher* publisher) {
  delete publisher_;
  publisher_ = publisher;
  if (publisher) {
    set_has_publisher();
  } else {
    clear_has_publisher();
  }
  // @@protoc_insertion_point(field_set_allocated:google.protobuf.testing.Book.publisher)
}

// repeated .google.protobuf.testing.Book.Label labels = 10;
int Book::labels_size() const {
  return labels_.size();
}
void Book::clear_labels() {
  labels_.Clear();
}
const ::google::protobuf::testing::Book_Label& Book::labels(int index) const {
  // @@protoc_insertion_point(field_get:google.protobuf.testing.Book.labels)
  return labels_.Get(index);
}
::google::protobuf::testing::Book_Label* Book::mutable_labels(int index) {
  // @@protoc_insertion_point(field_mutable:google.protobuf.testing.Book.labels)
  return labels_.Mutable(index);
}
::google::protobuf::testing::Book_Label* Book::add_labels() {
  // @@protoc_insertion_point(field_add:google.protobuf.testing.Book.labels)
  return labels_.Add();
}
::google::protobuf::RepeatedPtrField< ::google::protobuf::testing::Book_Label >*
Book::mutable_labels() {
  // @@protoc_insertion_point(field_mutable_list:google.protobuf.testing.Book.labels)
  return &labels_;
}
const ::google::protobuf::RepeatedPtrField< ::google::protobuf::testing::Book_Label >&
Book::labels() const {
  // @@protoc_insertion_point(field_list:google.protobuf.testing.Book.labels)
  return labels_;
}

// optional .google.protobuf.testing.Book.Type type = 11;
bool Book::has_type() const {
  return (_has_bits_[0] & 0x00000100u) != 0;
}
void Book::set_has_type() {
  _has_bits_[0] |= 0x00000100u;
}
void Book::clear_has_type() {
  _has_bits_[0] &= ~0x00000100u;
}
void Book::clear_type() {
  type_ = 1;
  clear_has_type();
}
::google::protobuf::testing::Book_Type Book::type() const {
  // @@protoc_insertion_point(field_get:google.protobuf.testing.Book.type)
  return static_cast< ::google::protobuf::testing::Book_Type >(type_);
}
void Book::set_type(::google::protobuf::testing::Book_Type value) {
  assert(::google::protobuf::testing::Book_Type_IsValid(value));
  set_has_type();
  type_ = value;
  // @@protoc_insertion_point(field_set:google.protobuf.testing.Book.type)
}

inline const Book* Book::internal_default_instance() {
  return &Book_default_instance_.get();
}
#endif  // PROTOBUF_INLINE_NOT_IN_HEADERS

// ===================================================================

#if !defined(_MSC_VER) || _MSC_VER >= 1900
const int Publisher::kNameFieldNumber;
#endif  // !defined(_MSC_VER) || _MSC_VER >= 1900

Publisher::Publisher()
  : ::google::protobuf::Message(), _internal_metadata_(NULL) {
  if (this != internal_default_instance()) protobuf_InitDefaults_google_2fprotobuf_2futil_2finternal_2ftestdata_2fbooks_2eproto();
  SharedCtor();
  // @@protoc_insertion_point(constructor:google.protobuf.testing.Publisher)
}

void Publisher::InitAsDefaultInstance() {
}

Publisher::Publisher(const Publisher& from)
  : ::google::protobuf::Message(),
    _internal_metadata_(NULL) {
  SharedCtor();
  UnsafeMergeFrom(from);
  // @@protoc_insertion_point(copy_constructor:google.protobuf.testing.Publisher)
}

void Publisher::SharedCtor() {
  _cached_size_ = 0;
  name_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}

Publisher::~Publisher() {
  // @@protoc_insertion_point(destructor:google.protobuf.testing.Publisher)
  SharedDtor();
}

void Publisher::SharedDtor() {
  name_.DestroyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}

void Publisher::SetCachedSize(int size) const {
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
}
const ::google::protobuf::Descriptor* Publisher::descriptor() {
  protobuf_AssignDescriptorsOnce();
  return Publisher_descriptor_;
}

const Publisher& Publisher::default_instance() {
  protobuf_InitDefaults_google_2fprotobuf_2futil_2finternal_2ftestdata_2fbooks_2eproto();
  return *internal_default_instance();
}

::google::protobuf::internal::ExplicitlyConstructed<Publisher> Publisher_default_instance_;

Publisher* Publisher::New(::google::protobuf::Arena* arena) const {
  Publisher* n = new Publisher;
  if (arena != NULL) {
    arena->Own(n);
  }
  return n;
}

void Publisher::Clear() {
// @@protoc_insertion_point(message_clear_start:google.protobuf.testing.Publisher)
  if (has_name()) {
    name_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  }
  _has_bits_.Clear();
  if (_internal_metadata_.have_unknown_fields()) {
    mutable_unknown_fields()->Clear();
  }
}

bool Publisher::MergePartialFromCodedStream(
    ::google::protobuf::io::CodedInputStream* input) {
#define DO_(EXPRESSION) if (!GOOGLE_PREDICT_TRUE(EXPRESSION)) goto failure
  ::google::protobuf::uint32 tag;
  // @@protoc_insertion_point(parse_start:google.protobuf.testing.Publisher)
  for (;;) {
    ::std::pair< ::google::protobuf::uint32, bool> p = input->ReadTagWithCutoff(127);
    tag = p.first;
    if (!p.second) goto handle_unusual;
    switch (::google::protobuf::internal::WireFormatLite::GetTagFieldNumber(tag)) {
      // required string name = 1;
      case 1: {
        if (tag == 10) {
          DO_(::google::protobuf::internal::WireFormatLite::ReadString(
                input, this->mutable_name()));
          ::google::protobuf::internal::WireFormat::VerifyUTF8StringNamedField(
            this->name().data(), this->name().length(),
            ::google::protobuf::internal::WireFormat::PARSE,
            "google.protobuf.testing.Publisher.name");
        } else {
          goto handle_unusual;
        }
        if (input->ExpectAtEnd()) goto success;
        break;
      }

      default: {
      handle_unusual:
        if (tag == 0 ||
            ::google::protobuf::internal::WireFormatLite::GetTagWireType(tag) ==
            ::google::protobuf::internal::WireFormatLite::WIRETYPE_END_GROUP) {
          goto success;
        }
        DO_(::google::protobuf::internal::WireFormat::SkipField(
              input, tag, mutable_unknown_fields()));
        break;
      }
    }
  }
success:
  // @@protoc_insertion_point(parse_success:google.protobuf.testing.Publisher)
  return true;
failure:
  // @@protoc_insertion_point(parse_failure:google.protobuf.testing.Publisher)
  return false;
#undef DO_
}

void Publisher::SerializeWithCachedSizes(
    ::google::protobuf::io::CodedOutputStream* output) const {
  // @@protoc_insertion_point(serialize_start:google.protobuf.testing.Publisher)
  // required string name = 1;
  if (has_name()) {
    ::google::protobuf::internal::WireFormat::VerifyUTF8StringNamedField(
      this->name().data(), this->name().length(),
      ::google::protobuf::internal::WireFormat::SERIALIZE,
      "google.protobuf.testing.Publisher.name");
    ::google::protobuf::internal::WireFormatLite::WriteStringMaybeAliased(
      1, this->name(), output);
  }

  if (_internal_metadata_.have_unknown_fields()) {
    ::google::protobuf::internal::WireFormat::SerializeUnknownFields(
        unknown_fields(), output);
  }
  // @@protoc_insertion_point(serialize_end:google.protobuf.testing.Publisher)
}

::google::protobuf::uint8* Publisher::InternalSerializeWithCachedSizesToArray(
    bool deterministic, ::google::protobuf::uint8* target) const {
  (void)deterministic; // Unused
  // @@protoc_insertion_point(serialize_to_array_start:google.protobuf.testing.Publisher)
  // required string name = 1;
  if (has_name()) {
    ::google::protobuf::internal::WireFormat::VerifyUTF8StringNamedField(
      this->name().data(), this->name().length(),
      ::google::protobuf::internal::WireFormat::SERIALIZE,
      "google.protobuf.testing.Publisher.name");
    target =
      ::google::protobuf::internal::WireFormatLite::WriteStringToArray(
        1, this->name(), target);
  }

  if (_internal_metadata_.have_unknown_fields()) {
    target = ::google::protobuf::internal::WireFormat::SerializeUnknownFieldsToArray(
        unknown_fields(), target);
  }
  // @@protoc_insertion_point(serialize_to_array_end:google.protobuf.testing.Publisher)
  return target;
}

size_t Publisher::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:google.protobuf.testing.Publisher)
  size_t total_size = 0;

  // required string name = 1;
  if (has_name()) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::StringSize(
        this->name());
  }
  if (_internal_metadata_.have_unknown_fields()) {
    total_size +=
      ::google::protobuf::internal::WireFormat::ComputeUnknownFieldsSize(
        unknown_fields());
  }
  int cached_size = ::google::protobuf::internal::ToCachedSize(total_size);
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = cached_size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
  return total_size;
}

void Publisher::MergeFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_merge_from_start:google.protobuf.testing.Publisher)
  if (GOOGLE_PREDICT_FALSE(&from == this)) MergeFromFail(__LINE__);
  const Publisher* source =
      ::google::protobuf::internal::DynamicCastToGenerated<const Publisher>(
          &from);
  if (source == NULL) {
  // @@protoc_insertion_point(generalized_merge_from_cast_fail:google.protobuf.testing.Publisher)
    ::google::protobuf::internal::ReflectionOps::Merge(from, this);
  } else {
  // @@protoc_insertion_point(generalized_merge_from_cast_success:google.protobuf.testing.Publisher)
    UnsafeMergeFrom(*source);
  }
}

void Publisher::MergeFrom(const Publisher& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:google.protobuf.testing.Publisher)
  if (GOOGLE_PREDICT_TRUE(&from != this)) {
    UnsafeMergeFrom(from);
  } else {
    MergeFromFail(__LINE__);
  }
}

void Publisher::UnsafeMergeFrom(const Publisher& from) {
  GOOGLE_DCHECK(&from != this);
  if (from._has_bits_[0 / 32] & (0xffu << (0 % 32))) {
    if (from.has_name()) {
      set_has_name();
      name_.AssignWithDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), from.name_);
    }
  }
  if (from._internal_metadata_.have_unknown_fields()) {
    ::google::protobuf::UnknownFieldSet::MergeToInternalMetdata(
      from.unknown_fields(), &_internal_metadata_);
  }
}

void Publisher::CopyFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_copy_from_start:google.protobuf.testing.Publisher)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

void Publisher::CopyFrom(const Publisher& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:google.protobuf.testing.Publisher)
  if (&from == this) return;
  Clear();
  UnsafeMergeFrom(from);
}

bool Publisher::IsInitialized() const {
  if ((_has_bits_[0] & 0x00000001) != 0x00000001) return false;

  return true;
}

void Publisher::Swap(Publisher* other) {
  if (other == this) return;
  InternalSwap(other);
}
void Publisher::InternalSwap(Publisher* other) {
  name_.Swap(&other->name_);
  std::swap(_has_bits_[0], other->_has_bits_[0]);
  _internal_metadata_.Swap(&other->_internal_metadata_);
  std::swap(_cached_size_, other->_cached_size_);
}

::google::protobuf::Metadata Publisher::GetMetadata() const {
  protobuf_AssignDescriptorsOnce();
  ::google::protobuf::Metadata metadata;
  metadata.descriptor = Publisher_descriptor_;
  metadata.reflection = Publisher_reflection_;
  return metadata;
}

#if PROTOBUF_INLINE_NOT_IN_HEADERS
// Publisher

// required string name = 1;
bool Publisher::has_name() const {
  return (_has_bits_[0] & 0x00000001u) != 0;
}
void Publisher::set_has_name() {
  _has_bits_[0] |= 0x00000001u;
}
void Publisher::clear_has_name() {
  _has_bits_[0] &= ~0x00000001u;
}
void Publisher::clear_name() {
  name_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  clear_has_name();
}
const ::std::string& Publisher::name() const {
  // @@protoc_insertion_point(field_get:google.protobuf.testing.Publisher.name)
  return name_.GetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
void Publisher::set_name(const ::std::string& value) {
  set_has_name();
  name_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:google.protobuf.testing.Publisher.name)
}
void Publisher::set_name(const char* value) {
  set_has_name();
  name_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:google.protobuf.testing.Publisher.name)
}
void Publisher::set_name(const char* value, size_t size) {
  set_has_name();
  name_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:google.protobuf.testing.Publisher.name)
}
::std::string* Publisher::mutable_name() {
  set_has_name();
  // @@protoc_insertion_point(field_mutable:google.protobuf.testing.Publisher.name)
  return name_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
::std::string* Publisher::release_name() {
  // @@protoc_insertion_point(field_release:google.protobuf.testing.Publisher.name)
  clear_has_name();
  return name_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
void Publisher::set_allocated_name(::std::string* name) {
  if (name != NULL) {
    set_has_name();
  } else {
    clear_has_name();
  }
  name_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), name);
  // @@protoc_insertion_point(field_set_allocated:google.protobuf.testing.Publisher.name)
}

inline const Publisher* Publisher::internal_default_instance() {
  return &Publisher_default_instance_.get();
}
#endif  // PROTOBUF_INLINE_NOT_IN_HEADERS

// ===================================================================

#if !defined(_MSC_VER) || _MSC_VER >= 1900
const int Author::kIdFieldNumber;
const int Author::kNameFieldNumber;
const int Author::kPseudonymFieldNumber;
const int Author::kAliveFieldNumber;
const int Author::kFriendFieldNumber;
#endif  // !defined(_MSC_VER) || _MSC_VER >= 1900

Author::Author()
  : ::google::protobuf::Message(), _internal_metadata_(NULL) {
  if (this != internal_default_instance()) protobuf_InitDefaults_google_2fprotobuf_2futil_2finternal_2ftestdata_2fbooks_2eproto();
  SharedCtor();
  // @@protoc_insertion_point(constructor:google.protobuf.testing.Author)
}

void Author::InitAsDefaultInstance() {
}

Author::Author(const Author& from)
  : ::google::protobuf::Message(),
    _internal_metadata_(NULL) {
  SharedCtor();
  UnsafeMergeFrom(from);
  // @@protoc_insertion_point(copy_constructor:google.protobuf.testing.Author)
}

void Author::SharedCtor() {
  _cached_size_ = 0;
  name_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  ::memset(&id_, 0, reinterpret_cast<char*>(&alive_) -
    reinterpret_cast<char*>(&id_) + sizeof(alive_));
}

Author::~Author() {
  // @@protoc_insertion_point(destructor:google.protobuf.testing.Author)
  SharedDtor();
}

void Author::SharedDtor() {
  name_.DestroyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}

void Author::SetCachedSize(int size) const {
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
}
const ::google::protobuf::Descriptor* Author::descriptor() {
  protobuf_AssignDescriptorsOnce();
  return Author_descriptor_;
}

const Author& Author::default_instance() {
  protobuf_InitDefaults_google_2fprotobuf_2futil_2finternal_2ftestdata_2fbooks_2eproto();
  return *internal_default_instance();
}

::google::protobuf::internal::ExplicitlyConstructed<Author> Author_default_instance_;

Author* Author::New(::google::protobuf::Arena* arena) const {
  Author* n = new Author;
  if (arena != NULL) {
    arena->Own(n);
  }
  return n;
}

void Author::Clear() {
// @@protoc_insertion_point(message_clear_start:google.protobuf.testing.Author)
#if defined(__clang__)
#define ZR_HELPER_(f) \
  _Pragma("clang diagnostic push") \
  _Pragma("clang diagnostic ignored \"-Winvalid-offsetof\"") \
  __builtin_offsetof(Author, f) \
  _Pragma("clang diagnostic pop")
#else
#define ZR_HELPER_(f) reinterpret_cast<char*>(\
  &reinterpret_cast<Author*>(16)->f)
#endif

#define ZR_(first, last) do {\
  ::memset(&(first), 0,\
           ZR_HELPER_(last) - ZR_HELPER_(first) + sizeof(last));\
} while (0)

  if (_has_bits_[0 / 32] & 11u) {
    ZR_(id_, alive_);
    if (has_name()) {
      name_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
    }
  }

#undef ZR_HELPER_
#undef ZR_

  pseudonym_.Clear();
  friend__.Clear();
  _has_bits_.Clear();
  if (_internal_metadata_.have_unknown_fields()) {
    mutable_unknown_fields()->Clear();
  }
}

bool Author::MergePartialFromCodedStream(
    ::google::protobuf::io::CodedInputStream* input) {
#define DO_(EXPRESSION) if (!GOOGLE_PREDICT_TRUE(EXPRESSION)) goto failure
  ::google::protobuf::uint32 tag;
  // @@protoc_insertion_point(parse_start:google.protobuf.testing.Author)
  for (;;) {
    ::std::pair< ::google::protobuf::uint32, bool> p = input->ReadTagWithCutoff(127);
    tag = p.first;
    if (!p.second) goto handle_unusual;
    switch (::google::protobuf::internal::WireFormatLite::GetTagFieldNumber(tag)) {
      // optional uint64 id = 1[json_name = "@id"];
      case 1: {
        if (tag == 8) {
          set_has_id();
          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::uint64, ::google::protobuf::internal::WireFormatLite::TYPE_UINT64>(
                 input, &id_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(18)) goto parse_name;
        break;
      }

      // optional string name = 2;
      case 2: {
        if (tag == 18) {
         parse_name:
          DO_(::google::protobuf::internal::WireFormatLite::ReadString(
                input, this->mutable_name()));
          ::google::protobuf::internal::WireFormat::VerifyUTF8StringNamedField(
            this->name().data(), this->name().length(),
            ::google::protobuf::internal::WireFormat::PARSE,
            "google.protobuf.testing.Author.name");
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(26)) goto parse_pseudonym;
        break;
      }

      // repeated string pseudonym = 3;
      case 3: {
        if (tag == 26) {
         parse_pseudonym:
          DO_(::google::protobuf::internal::WireFormatLite::ReadString(
                input, this->add_pseudonym()));
          ::google::protobuf::internal::WireFormat::VerifyUTF8StringNamedField(
            this->pseudonym(this->pseudonym_size() - 1).data(),
            this->pseudonym(this->pseudonym_size() - 1).length(),
            ::google::protobuf::internal::WireFormat::PARSE,
            "google.protobuf.testing.Author.pseudonym");
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(26)) goto parse_pseudonym;
        if (input->ExpectTag(32)) goto parse_alive;
        break;
      }

      // optional bool alive = 4;
      case 4: {
        if (tag == 32) {
         parse_alive:
          set_has_alive();
          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   bool, ::google::protobuf::internal::WireFormatLite::TYPE_BOOL>(
                 input, &alive_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(42)) goto parse_friend;
        break;
      }

      // repeated .google.protobuf.testing.Author friend = 5;
      case 5: {
        if (tag == 42) {
         parse_friend:
          DO_(input->IncrementRecursionDepth());
         parse_loop_friend:
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessageNoVirtualNoRecursionDepth(
                input, add_friend_()));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(42)) goto parse_loop_friend;
        input->UnsafeDecrementRecursionDepth();
        if (input->ExpectAtEnd()) goto success;
        break;
      }

      default: {
      handle_unusual:
        if (tag == 0 ||
            ::google::protobuf::internal::WireFormatLite::GetTagWireType(tag) ==
            ::google::protobuf::internal::WireFormatLite::WIRETYPE_END_GROUP) {
          goto success;
        }
        DO_(::google::protobuf::internal::WireFormat::SkipField(
              input, tag, mutable_unknown_fields()));
        break;
      }
    }
  }
success:
  // @@protoc_insertion_point(parse_success:google.protobuf.testing.Author)
  return true;
failure:
  // @@protoc_insertion_point(parse_failure:google.protobuf.testing.Author)
  return false;
#undef DO_
}

void Author::SerializeWithCachedSizes(
    ::google::protobuf::io::CodedOutputStream* output) const {
  // @@protoc_insertion_point(serialize_start:google.protobuf.testing.Author)
  // optional uint64 id = 1[json_name = "@id"];
  if (has_id()) {
    ::google::protobuf::internal::WireFormatLite::WriteUInt64(1, this->id(), output);
  }

  // optional string name = 2;
  if (has_name()) {
    ::google::protobuf::internal::WireFormat::VerifyUTF8StringNamedField(
      this->name().data(), this->name().length(),
      ::google::protobuf::internal::WireFormat::SERIALIZE,
      "google.protobuf.testing.Author.name");
    ::google::protobuf::internal::WireFormatLite::WriteStringMaybeAliased(
      2, this->name(), output);
  }

  // repeated string pseudonym = 3;
  for (int i = 0; i < this->pseudonym_size(); i++) {
    ::google::protobuf::internal::WireFormat::VerifyUTF8StringNamedField(
      this->pseudonym(i).data(), this->pseudonym(i).length(),
      ::google::protobuf::internal::WireFormat::SERIALIZE,
      "google.protobuf.testing.Author.pseudonym");
    ::google::protobuf::internal::WireFormatLite::WriteString(
      3, this->pseudonym(i), output);
  }

  // optional bool alive = 4;
  if (has_alive()) {
    ::google::protobuf::internal::WireFormatLite::WriteBool(4, this->alive(), output);
  }

  // repeated .google.protobuf.testing.Author friend = 5;
  for (unsigned int i = 0, n = this->friend__size(); i < n; i++) {
    ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
      5, this->friend_(i), output);
  }

  if (_internal_metadata_.have_unknown_fields()) {
    ::google::protobuf::internal::WireFormat::SerializeUnknownFields(
        unknown_fields(), output);
  }
  // @@protoc_insertion_point(serialize_end:google.protobuf.testing.Author)
}

::google::protobuf::uint8* Author::InternalSerializeWithCachedSizesToArray(
    bool deterministic, ::google::protobuf::uint8* target) const {
  (void)deterministic; // Unused
  // @@protoc_insertion_point(serialize_to_array_start:google.protobuf.testing.Author)
  // optional uint64 id = 1[json_name = "@id"];
  if (has_id()) {
    target = ::google::protobuf::internal::WireFormatLite::WriteUInt64ToArray(1, this->id(), target);
  }

  // optional string name = 2;
  if (has_name()) {
    ::google::protobuf::internal::WireFormat::VerifyUTF8StringNamedField(
      this->name().data(), this->name().length(),
      ::google::protobuf::internal::WireFormat::SERIALIZE,
      "google.protobuf.testing.Author.name");
    target =
      ::google::protobuf::internal::WireFormatLite::WriteStringToArray(
        2, this->name(), target);
  }

  // repeated string pseudonym = 3;
  for (int i = 0; i < this->pseudonym_size(); i++) {
    ::google::protobuf::internal::WireFormat::VerifyUTF8StringNamedField(
      this->pseudonym(i).data(), this->pseudonym(i).length(),
      ::google::protobuf::internal::WireFormat::SERIALIZE,
      "google.protobuf.testing.Author.pseudonym");
    target = ::google::protobuf::internal::WireFormatLite::
      WriteStringToArray(3, this->pseudonym(i), target);
  }

  // optional bool alive = 4;
  if (has_alive()) {
    target = ::google::protobuf::internal::WireFormatLite::WriteBoolToArray(4, this->alive(), target);
  }

  // repeated .google.protobuf.testing.Author friend = 5;
  for (unsigned int i = 0, n = this->friend__size(); i < n; i++) {
    target = ::google::protobuf::internal::WireFormatLite::
      InternalWriteMessageNoVirtualToArray(
        5, this->friend_(i), false, target);
  }

  if (_internal_metadata_.have_unknown_fields()) {
    target = ::google::protobuf::internal::WireFormat::SerializeUnknownFieldsToArray(
        unknown_fields(), target);
  }
  // @@protoc_insertion_point(serialize_to_array_end:google.protobuf.testing.Author)
  return target;
}

size_t Author::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:google.protobuf.testing.Author)
  size_t total_size = 0;

  if (_has_bits_[0 / 32] & 11u) {
    // optional uint64 id = 1[json_name = "@id"];
    if (has_id()) {
      total_size += 1 +
        ::google::protobuf::internal::WireFormatLite::UInt64Size(
          this->id());
    }

    // optional string name = 2;
    if (has_name()) {
      total_size += 1 +
        ::google::protobuf::internal::WireFormatLite::StringSize(
          this->name());
    }

    // optional bool alive = 4;
    if (has_alive()) {
      total_size += 1 + 1;
    }

  }
  // repeated string pseudonym = 3;
  total_size += 1 *
      ::google::protobuf::internal::FromIntSize(this->pseudonym_size());
  for (int i = 0; i < this->pseudonym_size(); i++) {
    total_size += ::google::protobuf::internal::WireFormatLite::StringSize(
      this->pseudonym(i));
  }

  // repeated .google.protobuf.testing.Author friend = 5;
  {
    unsigned int count = this->friend__size();
    total_size += 1UL * count;
    for (unsigned int i = 0; i < count; i++) {
      total_size +=
        ::google::protobuf::internal::WireFormatLite::MessageSizeNoVirtual(
          this->friend_(i));
    }
  }

  if (_internal_metadata_.have_unknown_fields()) {
    total_size +=
      ::google::protobuf::internal::WireFormat::ComputeUnknownFieldsSize(
        unknown_fields());
  }
  int cached_size = ::google::protobuf::internal::ToCachedSize(total_size);
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = cached_size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
  return total_size;
}

void Author::MergeFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_merge_from_start:google.protobuf.testing.Author)
  if (GOOGLE_PREDICT_FALSE(&from == this)) MergeFromFail(__LINE__);
  const Author* source =
      ::google::protobuf::internal::DynamicCastToGenerated<const Author>(
          &from);
  if (source == NULL) {
  // @@protoc_insertion_point(generalized_merge_from_cast_fail:google.protobuf.testing.Author)
    ::google::protobuf::internal::ReflectionOps::Merge(from, this);
  } else {
  // @@protoc_insertion_point(generalized_merge_from_cast_success:google.protobuf.testing.Author)
    UnsafeMergeFrom(*source);
  }
}

void Author::MergeFrom(const Author& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:google.protobuf.testing.Author)
  if (GOOGLE_PREDICT_TRUE(&from != this)) {
    UnsafeMergeFrom(from);
  } else {
    MergeFromFail(__LINE__);
  }
}

void Author::UnsafeMergeFrom(const Author& from) {
  GOOGLE_DCHECK(&from != this);
  pseudonym_.UnsafeMergeFrom(from.pseudonym_);
  friend__.MergeFrom(from.friend__);
  if (from._has_bits_[0 / 32] & (0xffu << (0 % 32))) {
    if (from.has_id()) {
      set_id(from.id());
    }
    if (from.has_name()) {
      set_has_name();
      name_.AssignWithDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), from.name_);
    }
    if (from.has_alive()) {
      set_alive(from.alive());
    }
  }
  if (from._internal_metadata_.have_unknown_fields()) {
    ::google::protobuf::UnknownFieldSet::MergeToInternalMetdata(
      from.unknown_fields(), &_internal_metadata_);
  }
}

void Author::CopyFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_copy_from_start:google.protobuf.testing.Author)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

void Author::CopyFrom(const Author& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:google.protobuf.testing.Author)
  if (&from == this) return;
  Clear();
  UnsafeMergeFrom(from);
}

bool Author::IsInitialized() const {

  return true;
}

void Author::Swap(Author* other) {
  if (other == this) return;
  InternalSwap(other);
}
void Author::InternalSwap(Author* other) {
  std::swap(id_, other->id_);
  name_.Swap(&other->name_);
  pseudonym_.UnsafeArenaSwap(&other->pseudonym_);
  std::swap(alive_, other->alive_);
  friend__.UnsafeArenaSwap(&other->friend__);
  std::swap(_has_bits_[0], other->_has_bits_[0]);
  _internal_metadata_.Swap(&other->_internal_metadata_);
  std::swap(_cached_size_, other->_cached_size_);
}

::google::protobuf::Metadata Author::GetMetadata() const {
  protobuf_AssignDescriptorsOnce();
  ::google::protobuf::Metadata metadata;
  metadata.descriptor = Author_descriptor_;
  metadata.reflection = Author_reflection_;
  return metadata;
}

#if PROTOBUF_INLINE_NOT_IN_HEADERS
// Author

// optional uint64 id = 1[json_name = "@id"];
bool Author::has_id() const {
  return (_has_bits_[0] & 0x00000001u) != 0;
}
void Author::set_has_id() {
  _has_bits_[0] |= 0x00000001u;
}
void Author::clear_has_id() {
  _has_bits_[0] &= ~0x00000001u;
}
void Author::clear_id() {
  id_ = GOOGLE_ULONGLONG(0);
  clear_has_id();
}
::google::protobuf::uint64 Author::id() const {
  // @@protoc_insertion_point(field_get:google.protobuf.testing.Author.id)
  return id_;
}
void Author::set_id(::google::protobuf::uint64 value) {
  set_has_id();
  id_ = value;
  // @@protoc_insertion_point(field_set:google.protobuf.testing.Author.id)
}

// optional string name = 2;
bool Author::has_name() const {
  return (_has_bits_[0] & 0x00000002u) != 0;
}
void Author::set_has_name() {
  _has_bits_[0] |= 0x00000002u;
}
void Author::clear_has_name() {
  _has_bits_[0] &= ~0x00000002u;
}
void Author::clear_name() {
  name_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  clear_has_name();
}
const ::std::string& Author::name() const {
  // @@protoc_insertion_point(field_get:google.protobuf.testing.Author.name)
  return name_.GetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
void Author::set_name(const ::std::string& value) {
  set_has_name();
  name_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:google.protobuf.testing.Author.name)
}
void Author::set_name(const char* value) {
  set_has_name();
  name_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:google.protobuf.testing.Author.name)
}
void Author::set_name(const char* value, size_t size) {
  set_has_name();
  name_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:google.protobuf.testing.Author.name)
}
::std::string* Author::mutable_name() {
  set_has_name();
  // @@protoc_insertion_point(field_mutable:google.protobuf.testing.Author.name)
  return name_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
::std::string* Author::release_name() {
  // @@protoc_insertion_point(field_release:google.protobuf.testing.Author.name)
  clear_has_name();
  return name_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
void Author::set_allocated_name(::std::string* name) {
  if (name != NULL) {
    set_has_name();
  } else {
    clear_has_name();
  }
  name_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), name);
  // @@protoc_insertion_point(field_set_allocated:google.protobuf.testing.Author.name)
}

// repeated string pseudonym = 3;
int Author::pseudonym_size() const {
  return pseudonym_.size();
}
void Author::clear_pseudonym() {
  pseudonym_.Clear();
}
const ::std::string& Author::pseudonym(int index) const {
  // @@protoc_insertion_point(field_get:google.protobuf.testing.Author.pseudonym)
  return pseudonym_.Get(index);
}
::std::string* Author::mutable_pseudonym(int index) {
  // @@protoc_insertion_point(field_mutable:google.protobuf.testing.Author.pseudonym)
  return pseudonym_.Mutable(index);
}
void Author::set_pseudonym(int index, const ::std::string& value) {
  // @@protoc_insertion_point(field_set:google.protobuf.testing.Author.pseudonym)
  pseudonym_.Mutable(index)->assign(value);
}
void Author::set_pseudonym(int index, const char* value) {
  pseudonym_.Mutable(index)->assign(value);
  // @@protoc_insertion_point(field_set_char:google.protobuf.testing.Author.pseudonym)
}
void Author::set_pseudonym(int index, const char* value, size_t size) {
  pseudonym_.Mutable(index)->assign(
    reinterpret_cast<const char*>(value), size);
  // @@protoc_insertion_point(field_set_pointer:google.protobuf.testing.Author.pseudonym)
}
::std::string* Author::add_pseudonym() {
  // @@protoc_insertion_point(field_add_mutable:google.protobuf.testing.Author.pseudonym)
  return pseudonym_.Add();
}
void Author::add_pseudonym(const ::std::string& value) {
  pseudonym_.Add()->assign(value);
  // @@protoc_insertion_point(field_add:google.protobuf.testing.Author.pseudonym)
}
void Author::add_pseudonym(const char* value) {
  pseudonym_.Add()->assign(value);
  // @@protoc_insertion_point(field_add_char:google.protobuf.testing.Author.pseudonym)
}
void Author::add_pseudonym(const char* value, size_t size) {
  pseudonym_.Add()->assign(reinterpret_cast<const char*>(value), size);
  // @@protoc_insertion_point(field_add_pointer:google.protobuf.testing.Author.pseudonym)
}
const ::google::protobuf::RepeatedPtrField< ::std::string>&
Author::pseudonym() const {
  // @@protoc_insertion_point(field_list:google.protobuf.testing.Author.pseudonym)
  return pseudonym_;
}
::google::protobuf::RepeatedPtrField< ::std::string>*
Author::mutable_pseudonym() {
  // @@protoc_insertion_point(field_mutable_list:google.protobuf.testing.Author.pseudonym)
  return &pseudonym_;
}

// optional bool alive = 4;
bool Author::has_alive() const {
  return (_has_bits_[0] & 0x00000008u) != 0;
}
void Author::set_has_alive() {
  _has_bits_[0] |= 0x00000008u;
}
void Author::clear_has_alive() {
  _has_bits_[0] &= ~0x00000008u;
}
void Author::clear_alive() {
  alive_ = false;
  clear_has_alive();
}
bool Author::alive() const {
  // @@protoc_insertion_point(field_get:google.protobuf.testing.Author.alive)
  return alive_;
}
void Author::set_alive(bool value) {
  set_has_alive();
  alive_ = value;
  // @@protoc_insertion_point(field_set:google.protobuf.testing.Author.alive)
}

// repeated .google.protobuf.testing.Author friend = 5;
int Author::friend__size() const {
  return friend__.size();
}
void Author::clear_friend_() {
  friend__.Clear();
}
const ::google::protobuf::testing::Author& Author::friend_(int index) const {
  // @@protoc_insertion_point(field_get:google.protobuf.testing.Author.friend)
  return friend__.Get(index);
}
::google::protobuf::testing::Author* Author::mutable_friend_(int index) {
  // @@protoc_insertion_point(field_mutable:google.protobuf.testing.Author.friend)
  return friend__.Mutable(index);
}
::google::protobuf::testing::Author* Author::add_friend_() {
  // @@protoc_insertion_point(field_add:google.protobuf.testing.Author.friend)
  return friend__.Add();
}
::google::protobuf::RepeatedPtrField< ::google::protobuf::testing::Author >*
Author::mutable_friend_() {
  // @@protoc_insertion_point(field_mutable_list:google.protobuf.testing.Author.friend)
  return &friend__;
}
const ::google::protobuf::RepeatedPtrField< ::google::protobuf::testing::Author >&
Author::friend_() const {
  // @@protoc_insertion_point(field_list:google.protobuf.testing.Author.friend)
  return friend__;
}

inline const Author* Author::internal_default_instance() {
  return &Author_default_instance_.get();
}
#endif  // PROTOBUF_INLINE_NOT_IN_HEADERS

// ===================================================================

#if !defined(_MSC_VER) || _MSC_VER >= 1900
const int BadAuthor::kIdFieldNumber;
const int BadAuthor::kNameFieldNumber;
const int BadAuthor::kPseudonymFieldNumber;
const int BadAuthor::kAliveFieldNumber;
#endif  // !defined(_MSC_VER) || _MSC_VER >= 1900

BadAuthor::BadAuthor()
  : ::google::protobuf::Message(), _internal_metadata_(NULL) {
  if (this != internal_default_instance()) protobuf_InitDefaults_google_2fprotobuf_2futil_2finternal_2ftestdata_2fbooks_2eproto();
  SharedCtor();
  // @@protoc_insertion_point(constructor:google.protobuf.testing.BadAuthor)
}

void BadAuthor::InitAsDefaultInstance() {
}

BadAuthor::BadAuthor(const BadAuthor& from)
  : ::google::protobuf::Message(),
    _internal_metadata_(NULL) {
  SharedCtor();
  UnsafeMergeFrom(from);
  // @@protoc_insertion_point(copy_constructor:google.protobuf.testing.BadAuthor)
}

void BadAuthor::SharedCtor() {
  _cached_size_ = 0;
  id_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  pseudonym_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}

BadAuthor::~BadAuthor() {
  // @@protoc_insertion_point(destructor:google.protobuf.testing.BadAuthor)
  SharedDtor();
}

void BadAuthor::SharedDtor() {
  id_.DestroyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  pseudonym_.DestroyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}

void BadAuthor::SetCachedSize(int size) const {
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
}
const ::google::protobuf::Descriptor* BadAuthor::descriptor() {
  protobuf_AssignDescriptorsOnce();
  return BadAuthor_descriptor_;
}

const BadAuthor& BadAuthor::default_instance() {
  protobuf_InitDefaults_google_2fprotobuf_2futil_2finternal_2ftestdata_2fbooks_2eproto();
  return *internal_default_instance();
}

::google::protobuf::internal::ExplicitlyConstructed<BadAuthor> BadAuthor_default_instance_;

BadAuthor* BadAuthor::New(::google::protobuf::Arena* arena) const {
  BadAuthor* n = new BadAuthor;
  if (arena != NULL) {
    arena->Own(n);
  }
  return n;
}

void BadAuthor::Clear() {
// @@protoc_insertion_point(message_clear_start:google.protobuf.testing.BadAuthor)
  if (_has_bits_[0 / 32] & 5u) {
    if (has_id()) {
      id_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
    }
    if (has_pseudonym()) {
      pseudonym_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
    }
  }
  name_.Clear();
  alive_.Clear();
  _has_bits_.Clear();
  if (_internal_metadata_.have_unknown_fields()) {
    mutable_unknown_fields()->Clear();
  }
}

bool BadAuthor::MergePartialFromCodedStream(
    ::google::protobuf::io::CodedInputStream* input) {
#define DO_(EXPRESSION) if (!GOOGLE_PREDICT_TRUE(EXPRESSION)) goto failure
  ::google::protobuf::uint32 tag;
  // @@protoc_insertion_point(parse_start:google.protobuf.testing.BadAuthor)
  for (;;) {
    ::std::pair< ::google::protobuf::uint32, bool> p = input->ReadTagWithCutoff(127);
    tag = p.first;
    if (!p.second) goto handle_unusual;
    switch (::google::protobuf::internal::WireFormatLite::GetTagFieldNumber(tag)) {
      // optional string id = 1;
      case 1: {
        if (tag == 10) {
          DO_(::google::protobuf::internal::WireFormatLite::ReadString(
                input, this->mutable_id()));
          ::google::protobuf::internal::WireFormat::VerifyUTF8StringNamedField(
            this->id().data(), this->id().length(),
            ::google::protobuf::internal::WireFormat::PARSE,
            "google.protobuf.testing.BadAuthor.id");
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(16)) goto parse_name;
        break;
      }

      // repeated uint64 name = 2;
      case 2: {
        if (tag == 16) {
         parse_name:
          DO_((::google::protobuf::internal::WireFormatLite::ReadRepeatedPrimitive<
                   ::google::protobuf::uint64, ::google::protobuf::internal::WireFormatLite::TYPE_UINT64>(
                 1, 16, input, this->mutable_name())));
        } else if (tag == 18) {
          DO_((::google::protobuf::internal::WireFormatLite::ReadPackedPrimitiveNoInline<
                   ::google::protobuf::uint64, ::google::protobuf::internal::WireFormatLite::TYPE_UINT64>(
                 input, this->mutable_name())));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(16)) goto parse_name;
        if (input->ExpectTag(26)) goto parse_pseudonym;
        break;
      }

      // optional string pseudonym = 3;
      case 3: {
        if (tag == 26) {
         parse_pseudonym:
          DO_(::google::protobuf::internal::WireFormatLite::ReadString(
                input, this->mutable_pseudonym()));
          ::google::protobuf::internal::WireFormat::VerifyUTF8StringNamedField(
            this->pseudonym().data(), this->pseudonym().length(),
            ::google::protobuf::internal::WireFormat::PARSE,
            "google.protobuf.testing.BadAuthor.pseudonym");
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(34)) goto parse_alive;
        break;
      }

      // repeated bool alive = 4 [packed = true];
      case 4: {
        if (tag == 34) {
         parse_alive:
          DO_((::google::protobuf::internal::WireFormatLite::ReadPackedPrimitive<
                   bool, ::google::protobuf::internal::WireFormatLite::TYPE_BOOL>(
                 input, this->mutable_alive())));
        } else if (tag == 32) {
          DO_((::google::protobuf::internal::WireFormatLite::ReadRepeatedPrimitiveNoInline<
                   bool, ::google::protobuf::internal::WireFormatLite::TYPE_BOOL>(
                 1, 34, input, this->mutable_alive())));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectAtEnd()) goto success;
        break;
      }

      default: {
      handle_unusual:
        if (tag == 0 ||
            ::google::protobuf::internal::WireFormatLite::GetTagWireType(tag) ==
            ::google::protobuf::internal::WireFormatLite::WIRETYPE_END_GROUP) {
          goto success;
        }
        DO_(::google::protobuf::internal::WireFormat::SkipField(
              input, tag, mutable_unknown_fields()));
        break;
      }
    }
  }
success:
  // @@protoc_insertion_point(parse_success:google.protobuf.testing.BadAuthor)
  return true;
failure:
  // @@protoc_insertion_point(parse_failure:google.protobuf.testing.BadAuthor)
  return false;
#undef DO_
}

void BadAuthor::SerializeWithCachedSizes(
    ::google::protobuf::io::CodedOutputStream* output) const {
  // @@protoc_insertion_point(serialize_start:google.protobuf.testing.BadAuthor)
  // optional string id = 1;
  if (has_id()) {
    ::google::protobuf::internal::WireFormat::VerifyUTF8StringNamedField(
      this->id().data(), this->id().length(),
      ::google::protobuf::internal::WireFormat::SERIALIZE,
      "google.protobuf.testing.BadAuthor.id");
    ::google::protobuf::internal::WireFormatLite::WriteStringMaybeAliased(
      1, this->id(), output);
  }

  // repeated uint64 name = 2;
  for (int i = 0; i < this->name_size(); i++) {
    ::google::protobuf::internal::WireFormatLite::WriteUInt64(
      2, this->name(i), output);
  }

  // optional string pseudonym = 3;
  if (has_pseudonym()) {
    ::google::protobuf::internal::WireFormat::VerifyUTF8StringNamedField(
      this->pseudonym().data(), this->pseudonym().length(),
      ::google::protobuf::internal::WireFormat::SERIALIZE,
      "google.protobuf.testing.BadAuthor.pseudonym");
    ::google::protobuf::internal::WireFormatLite::WriteStringMaybeAliased(
      3, this->pseudonym(), output);
  }

  // repeated bool alive = 4 [packed = true];
  if (this->alive_size() > 0) {
    ::google::protobuf::internal::WireFormatLite::WriteTag(4, ::google::protobuf::internal::WireFormatLite::WIRETYPE_LENGTH_DELIMITED, output);
    output->WriteVarint32(_alive_cached_byte_size_);
  }
  for (int i = 0; i < this->alive_size(); i++) {
    ::google::protobuf::internal::WireFormatLite::WriteBoolNoTag(
      this->alive(i), output);
  }

  if (_internal_metadata_.have_unknown_fields()) {
    ::google::protobuf::internal::WireFormat::SerializeUnknownFields(
        unknown_fields(), output);
  }
  // @@protoc_insertion_point(serialize_end:google.protobuf.testing.BadAuthor)
}

::google::protobuf::uint8* BadAuthor::InternalSerializeWithCachedSizesToArray(
    bool deterministic, ::google::protobuf::uint8* target) const {
  (void)deterministic; // Unused
  // @@protoc_insertion_point(serialize_to_array_start:google.protobuf.testing.BadAuthor)
  // optional string id = 1;
  if (has_id()) {
    ::google::protobuf::internal::WireFormat::VerifyUTF8StringNamedField(
      this->id().data(), this->id().length(),
      ::google::protobuf::internal::WireFormat::SERIALIZE,
      "google.protobuf.testing.BadAuthor.id");
    target =
      ::google::protobuf::internal::WireFormatLite::WriteStringToArray(
        1, this->id(), target);
  }

  // repeated uint64 name = 2;
  for (int i = 0; i < this->name_size(); i++) {
    target = ::google::protobuf::internal::WireFormatLite::
      WriteUInt64ToArray(2, this->name(i), target);
  }

  // optional string pseudonym = 3;
  if (has_pseudonym()) {
    ::google::protobuf::internal::WireFormat::VerifyUTF8StringNamedField(
      this->pseudonym().data(), this->pseudonym().length(),
      ::google::protobuf::internal::WireFormat::SERIALIZE,
      "google.protobuf.testing.BadAuthor.pseudonym");
    target =
      ::google::protobuf::internal::WireFormatLite::WriteStringToArray(
        3, this->pseudonym(), target);
  }

  // repeated bool alive = 4 [packed = true];
  if (this->alive_size() > 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteTagToArray(
      4,
      ::google::protobuf::internal::WireFormatLite::WIRETYPE_LENGTH_DELIMITED,
      target);
    target = ::google::protobuf::io::CodedOutputStream::WriteVarint32ToArray(
      _alive_cached_byte_size_, target);
  }
  for (int i = 0; i < this->alive_size(); i++) {
    target = ::google::protobuf::internal::WireFormatLite::
      WriteBoolNoTagToArray(this->alive(i), target);
  }

  if (_internal_metadata_.have_unknown_fields()) {
    target = ::google::protobuf::internal::WireFormat::SerializeUnknownFieldsToArray(
        unknown_fields(), target);
  }
  // @@protoc_insertion_point(serialize_to_array_end:google.protobuf.testing.BadAuthor)
  return target;
}

size_t BadAuthor::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:google.protobuf.testing.BadAuthor)
  size_t total_size = 0;

  if (_has_bits_[0 / 32] & 5u) {
    // optional string id = 1;
    if (has_id()) {
      total_size += 1 +
        ::google::protobuf::internal::WireFormatLite::StringSize(
          this->id());
    }

    // optional string pseudonym = 3;
    if (has_pseudonym()) {
      total_size += 1 +
        ::google::protobuf::internal::WireFormatLite::StringSize(
          this->pseudonym());
    }

  }
  // repeated uint64 name = 2;
  {
    size_t data_size = 0;
    unsigned int count = this->name_size();
    for (unsigned int i = 0; i < count; i++) {
      data_size += ::google::protobuf::internal::WireFormatLite::
        UInt64Size(this->name(i));
    }
    total_size += 1 *
                  ::google::protobuf::internal::FromIntSize(this->name_size());
    total_size += data_size;
  }

  // repeated bool alive = 4 [packed = true];
  {
    size_t data_size = 0;
    unsigned int count = this->alive_size();
    data_size = 1UL * count;
    if (data_size > 0) {
      total_size += 1 +
        ::google::protobuf::internal::WireFormatLite::Int32Size(data_size);
    }
    int cached_size = ::google::protobuf::internal::ToCachedSize(data_size);
    GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
    _alive_cached_byte_size_ = cached_size;
    GOOGLE_SAFE_CONCURRENT_WRITES_END();
    total_size += data_size;
  }

  if (_internal_metadata_.have_unknown_fields()) {
    total_size +=
      ::google::protobuf::internal::WireFormat::ComputeUnknownFieldsSize(
        unknown_fields());
  }
  int cached_size = ::google::protobuf::internal::ToCachedSize(total_size);
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = cached_size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
  return total_size;
}

void BadAuthor::MergeFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_merge_from_start:google.protobuf.testing.BadAuthor)
  if (GOOGLE_PREDICT_FALSE(&from == this)) MergeFromFail(__LINE__);
  const BadAuthor* source =
      ::google::protobuf::internal::DynamicCastToGenerated<const BadAuthor>(
          &from);
  if (source == NULL) {
  // @@protoc_insertion_point(generalized_merge_from_cast_fail:google.protobuf.testing.BadAuthor)
    ::google::protobuf::internal::ReflectionOps::Merge(from, this);
  } else {
  // @@protoc_insertion_point(generalized_merge_from_cast_success:google.protobuf.testing.BadAuthor)
    UnsafeMergeFrom(*source);
  }
}

void BadAuthor::MergeFrom(const BadAuthor& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:google.protobuf.testing.BadAuthor)
  if (GOOGLE_PREDICT_TRUE(&from != this)) {
    UnsafeMergeFrom(from);
  } else {
    MergeFromFail(__LINE__);
  }
}

void BadAuthor::UnsafeMergeFrom(const BadAuthor& from) {
  GOOGLE_DCHECK(&from != this);
  name_.UnsafeMergeFrom(from.name_);
  alive_.UnsafeMergeFrom(from.alive_);
  if (from._has_bits_[0 / 32] & (0xffu << (0 % 32))) {
    if (from.has_id()) {
      set_has_id();
      id_.AssignWithDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), from.id_);
    }
    if (from.has_pseudonym()) {
      set_has_pseudonym();
      pseudonym_.AssignWithDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), from.pseudonym_);
    }
  }
  if (from._internal_metadata_.have_unknown_fields()) {
    ::google::protobuf::UnknownFieldSet::MergeToInternalMetdata(
      from.unknown_fields(), &_internal_metadata_);
  }
}

void BadAuthor::CopyFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_copy_from_start:google.protobuf.testing.BadAuthor)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

void BadAuthor::CopyFrom(const BadAuthor& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:google.protobuf.testing.BadAuthor)
  if (&from == this) return;
  Clear();
  UnsafeMergeFrom(from);
}

bool BadAuthor::IsInitialized() const {

  return true;
}

void BadAuthor::Swap(BadAuthor* other) {
  if (other == this) return;
  InternalSwap(other);
}
void BadAuthor::InternalSwap(BadAuthor* other) {
  id_.Swap(&other->id_);
  name_.UnsafeArenaSwap(&other->name_);
  pseudonym_.Swap(&other->pseudonym_);
  alive_.UnsafeArenaSwap(&other->alive_);
  std::swap(_has_bits_[0], other->_has_bits_[0]);
  _internal_metadata_.Swap(&other->_internal_metadata_);
  std::swap(_cached_size_, other->_cached_size_);
}

::google::protobuf::Metadata BadAuthor::GetMetadata() const {
  protobuf_AssignDescriptorsOnce();
  ::google::protobuf::Metadata metadata;
  metadata.descriptor = BadAuthor_descriptor_;
  metadata.reflection = BadAuthor_reflection_;
  return metadata;
}

#if PROTOBUF_INLINE_NOT_IN_HEADERS
// BadAuthor

// optional string id = 1;
bool BadAuthor::has_id() const {
  return (_has_bits_[0] & 0x00000001u) != 0;
}
void BadAuthor::set_has_id() {
  _has_bits_[0] |= 0x00000001u;
}
void BadAuthor::clear_has_id() {
  _has_bits_[0] &= ~0x00000001u;
}
void BadAuthor::clear_id() {
  id_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  clear_has_id();
}
const ::std::string& BadAuthor::id() const {
  // @@protoc_insertion_point(field_get:google.protobuf.testing.BadAuthor.id)
  return id_.GetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
void BadAuthor::set_id(const ::std::string& value) {
  set_has_id();
  id_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:google.protobuf.testing.BadAuthor.id)
}
void BadAuthor::set_id(const char* value) {
  set_has_id();
  id_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:google.protobuf.testing.BadAuthor.id)
}
void BadAuthor::set_id(const char* value, size_t size) {
  set_has_id();
  id_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:google.protobuf.testing.BadAuthor.id)
}
::std::string* BadAuthor::mutable_id() {
  set_has_id();
  // @@protoc_insertion_point(field_mutable:google.protobuf.testing.BadAuthor.id)
  return id_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
::std::string* BadAuthor::release_id() {
  // @@protoc_insertion_point(field_release:google.protobuf.testing.BadAuthor.id)
  clear_has_id();
  return id_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
void BadAuthor::set_allocated_id(::std::string* id) {
  if (id != NULL) {
    set_has_id();
  } else {
    clear_has_id();
  }
  id_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), id);
  // @@protoc_insertion_point(field_set_allocated:google.protobuf.testing.BadAuthor.id)
}

// repeated uint64 name = 2;
int BadAuthor::name_size() const {
  return name_.size();
}
void BadAuthor::clear_name() {
  name_.Clear();
}
::google::protobuf::uint64 BadAuthor::name(int index) const {
  // @@protoc_insertion_point(field_get:google.protobuf.testing.BadAuthor.name)
  return name_.Get(index);
}
void BadAuthor::set_name(int index, ::google::protobuf::uint64 value) {
  name_.Set(index, value);
  // @@protoc_insertion_point(field_set:google.protobuf.testing.BadAuthor.name)
}
void BadAuthor::add_name(::google::protobuf::uint64 value) {
  name_.Add(value);
  // @@protoc_insertion_point(field_add:google.protobuf.testing.BadAuthor.name)
}
const ::google::protobuf::RepeatedField< ::google::protobuf::uint64 >&
BadAuthor::name() const {
  // @@protoc_insertion_point(field_list:google.protobuf.testing.BadAuthor.name)
  return name_;
}
::google::protobuf::RepeatedField< ::google::protobuf::uint64 >*
BadAuthor::mutable_name() {
  // @@protoc_insertion_point(field_mutable_list:google.protobuf.testing.BadAuthor.name)
  return &name_;
}

// optional string pseudonym = 3;
bool BadAuthor::has_pseudonym() const {
  return (_has_bits_[0] & 0x00000004u) != 0;
}
void BadAuthor::set_has_pseudonym() {
  _has_bits_[0] |= 0x00000004u;
}
void BadAuthor::clear_has_pseudonym() {
  _has_bits_[0] &= ~0x00000004u;
}
void BadAuthor::clear_pseudonym() {
  pseudonym_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  clear_has_pseudonym();
}
const ::std::string& BadAuthor::pseudonym() const {
  // @@protoc_insertion_point(field_get:google.protobuf.testing.BadAuthor.pseudonym)
  return pseudonym_.GetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
void BadAuthor::set_pseudonym(const ::std::string& value) {
  set_has_pseudonym();
  pseudonym_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:google.protobuf.testing.BadAuthor.pseudonym)
}
void BadAuthor::set_pseudonym(const char* value) {
  set_has_pseudonym();
  pseudonym_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:google.protobuf.testing.BadAuthor.pseudonym)
}
void BadAuthor::set_pseudonym(const char* value, size_t size) {
  set_has_pseudonym();
  pseudonym_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:google.protobuf.testing.BadAuthor.pseudonym)
}
::std::string* BadAuthor::mutable_pseudonym() {
  set_has_pseudonym();
  // @@protoc_insertion_point(field_mutable:google.protobuf.testing.BadAuthor.pseudonym)
  return pseudonym_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
::std::string* BadAuthor::release_pseudonym() {
  // @@protoc_insertion_point(field_release:google.protobuf.testing.BadAuthor.pseudonym)
  clear_has_pseudonym();
  return pseudonym_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
void BadAuthor::set_allocated_pseudonym(::std::string* pseudonym) {
  if (pseudonym != NULL) {
    set_has_pseudonym();
  } else {
    clear_has_pseudonym();
  }
  pseudonym_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), pseudonym);
  // @@protoc_insertion_point(field_set_allocated:google.protobuf.testing.BadAuthor.pseudonym)
}

// repeated bool alive = 4 [packed = true];
int BadAuthor::alive_size() const {
  return alive_.size();
}
void BadAuthor::clear_alive() {
  alive_.Clear();
}
bool BadAuthor::alive(int index) const {
  // @@protoc_insertion_point(field_get:google.protobuf.testing.BadAuthor.alive)
  return alive_.Get(index);
}
void BadAuthor::set_alive(int index, bool value) {
  alive_.Set(index, value);
  // @@protoc_insertion_point(field_set:google.protobuf.testing.BadAuthor.alive)
}
void BadAuthor::add_alive(bool value) {
  alive_.Add(value);
  // @@protoc_insertion_point(field_add:google.protobuf.testing.BadAuthor.alive)
}
const ::google::protobuf::RepeatedField< bool >&
BadAuthor::alive() const {
  // @@protoc_insertion_point(field_list:google.protobuf.testing.BadAuthor.alive)
  return alive_;
}
::google::protobuf::RepeatedField< bool >*
BadAuthor::mutable_alive() {
  // @@protoc_insertion_point(field_mutable_list:google.protobuf.testing.BadAuthor.alive)
  return &alive_;
}

inline const BadAuthor* BadAuthor::internal_default_instance() {
  return &BadAuthor_default_instance_.get();
}
#endif  // PROTOBUF_INLINE_NOT_IN_HEADERS

// ===================================================================

#if !defined(_MSC_VER) || _MSC_VER >= 1900
const int Primitive::kFix32FieldNumber;
const int Primitive::kU32FieldNumber;
const int Primitive::kI32FieldNumber;
const int Primitive::kSf32FieldNumber;
const int Primitive::kS32FieldNumber;
const int Primitive::kFix64FieldNumber;
const int Primitive::kU64FieldNumber;
const int Primitive::kI64FieldNumber;
const int Primitive::kSf64FieldNumber;
const int Primitive::kS64FieldNumber;
const int Primitive::kStrFieldNumber;
const int Primitive::kBytesFieldNumber;
const int Primitive::kFloatFieldNumber;
const int Primitive::kDoubleFieldNumber;
const int Primitive::kBoolFieldNumber;
const int Primitive::kRepFix32FieldNumber;
const int Primitive::kRepU32FieldNumber;
const int Primitive::kRepI32FieldNumber;
const int Primitive::kRepSf32FieldNumber;
const int Primitive::kRepS32FieldNumber;
const int Primitive::kRepFix64FieldNumber;
const int Primitive::kRepU64FieldNumber;
const int Primitive::kRepI64FieldNumber;
const int Primitive::kRepSf64FieldNumber;
const int Primitive::kRepS64FieldNumber;
const int Primitive::kRepStrFieldNumber;
const int Primitive::kRepBytesFieldNumber;
const int Primitive::kRepFloatFieldNumber;
const int Primitive::kRepDoubleFieldNumber;
const int Primitive::kRepBoolFieldNumber;
#endif  // !defined(_MSC_VER) || _MSC_VER >= 1900

Primitive::Primitive()
  : ::google::protobuf::Message(), _internal_metadata_(NULL) {
  if (this != internal_default_instance()) protobuf_InitDefaults_google_2fprotobuf_2futil_2finternal_2ftestdata_2fbooks_2eproto();
  SharedCtor();
  // @@protoc_insertion_point(constructor:google.protobuf.testing.Primitive)
}

void Primitive::InitAsDefaultInstance() {
}

Primitive::Primitive(const Primitive& from)
  : ::google::protobuf::Message(),
    _internal_metadata_(NULL) {
  SharedCtor();
  UnsafeMergeFrom(from);
  // @@protoc_insertion_point(copy_constructor:google.protobuf.testing.Primitive)
}

void Primitive::SharedCtor() {
  _cached_size_ = 0;
  str_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  bytes_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  ::memset(&fix32_, 0, reinterpret_cast<char*>(&bool__) -
    reinterpret_cast<char*>(&fix32_) + sizeof(bool__));
}

Primitive::~Primitive() {
  // @@protoc_insertion_point(destructor:google.protobuf.testing.Primitive)
  SharedDtor();
}

void Primitive::SharedDtor() {
  str_.DestroyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  bytes_.DestroyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}

void Primitive::SetCachedSize(int size) const {
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
}
const ::google::protobuf::Descriptor* Primitive::descriptor() {
  protobuf_AssignDescriptorsOnce();
  return Primitive_descriptor_;
}

const Primitive& Primitive::default_instance() {
  protobuf_InitDefaults_google_2fprotobuf_2futil_2finternal_2ftestdata_2fbooks_2eproto();
  return *internal_default_instance();
}

::google::protobuf::internal::ExplicitlyConstructed<Primitive> Primitive_default_instance_;

Primitive* Primitive::New(::google::protobuf::Arena* arena) const {
  Primitive* n = new Primitive;
  if (arena != NULL) {
    arena->Own(n);
  }
  return n;
}

void Primitive::Clear() {
// @@protoc_insertion_point(message_clear_start:google.protobuf.testing.Primitive)
#if defined(__clang__)
#define ZR_HELPER_(f) \
  _Pragma("clang diagnostic push") \
  _Pragma("clang diagnostic ignored \"-Winvalid-offsetof\"") \
  __builtin_offsetof(Primitive, f) \
  _Pragma("clang diagnostic pop")
#else
#define ZR_HELPER_(f) reinterpret_cast<char*>(\
  &reinterpret_cast<Primitive*>(16)->f)
#endif

#define ZR_(first, last) do {\
  ::memset(&(first), 0,\
           ZR_HELPER_(last) - ZR_HELPER_(first) + sizeof(last));\
} while (0)

  if (_has_bits_[0 / 32] & 255u) {
    ZR_(fix32_, i64_);
    s32_ = 0;
  }
  if (_has_bits_[8 / 32] & 32512u) {
    ZR_(float__, bool__);
    sf64_ = GOOGLE_LONGLONG(0);
    if (has_str()) {
      str_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
    }
    if (has_bytes()) {
      bytes_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
    }
  }

#undef ZR_HELPER_
#undef ZR_

  rep_fix32_.Clear();
  rep_u32_.Clear();
  rep_i32_.Clear();
  rep_sf32_.Clear();
  rep_s32_.Clear();
  rep_fix64_.Clear();
  rep_u64_.Clear();
  rep_i64_.Clear();
  rep_sf64_.Clear();
  rep_s64_.Clear();
  rep_str_.Clear();
  rep_bytes_.Clear();
  rep_float_.Clear();
  rep_double_.Clear();
  rep_bool_.Clear();
  _has_bits_.Clear();
  if (_internal_metadata_.have_unknown_fields()) {
    mutable_unknown_fields()->Clear();
  }
}

bool Primitive::MergePartialFromCodedStream(
    ::google::protobuf::io::CodedInputStream* input) {
#define DO_(EXPRESSION) if (!GOOGLE_PREDICT_TRUE(EXPRESSION)) goto failure
  ::google::protobuf::uint32 tag;
  // @@protoc_insertion_point(parse_start:google.protobuf.testing.Primitive)
  for (;;) {
    ::std::pair< ::google::protobuf::uint32, bool> p = input->ReadTagWithCutoff(16383);
    tag = p.first;
    if (!p.second) goto handle_unusual;
    switch (::google::protobuf::internal::WireFormatLite::GetTagFieldNumber(tag)) {
      // optional fixed32 fix32 = 1;
      case 1: {
        if (tag == 13) {
          set_has_fix32();
          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::uint32, ::google::protobuf::internal::WireFormatLite::TYPE_FIXED32>(
                 input, &fix32_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(16)) goto parse_u32;
        break;
      }

      // optional uint32 u32 = 2;
      case 2: {
        if (tag == 16) {
         parse_u32:
          set_has_u32();
          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::uint32, ::google::protobuf::internal::WireFormatLite::TYPE_UINT32>(
                 input, &u32_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(24)) goto parse_i32;
        break;
      }

      // optional int32 i32 = 3;
      case 3: {
        if (tag == 24) {
         parse_i32:
          set_has_i32();
          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int32, ::google::protobuf::internal::WireFormatLite::TYPE_INT32>(
                 input, &i32_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(37)) goto parse_sf32;
        break;
      }

      // optional sfixed32 sf32 = 4;
      case 4: {
        if (tag == 37) {
         parse_sf32:
          set_has_sf32();
          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int32, ::google::protobuf::internal::WireFormatLite::TYPE_SFIXED32>(
                 input, &sf32_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(40)) goto parse_s32;
        break;
      }

      // optional sint32 s32 = 5;
      case 5: {
        if (tag == 40) {
         parse_s32:
          set_has_s32();
          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int32, ::google::protobuf::internal::WireFormatLite::TYPE_SINT32>(
                 input, &s32_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(49)) goto parse_fix64;
        break;
      }

      // optional fixed64 fix64 = 6;
      case 6: {
        if (tag == 49) {
         parse_fix64:
          set_has_fix64();
          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::uint64, ::google::protobuf::internal::WireFormatLite::TYPE_FIXED64>(
                 input, &fix64_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(56)) goto parse_u64;
        break;
      }

      // optional uint64 u64 = 7;
      case 7: {
        if (tag == 56) {
         parse_u64:
          set_has_u64();
          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::uint64, ::google::protobuf::internal::WireFormatLite::TYPE_UINT64>(
                 input, &u64_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(64)) goto parse_i64;
        break;
      }

      // optional int64 i64 = 8;
      case 8: {
        if (tag == 64) {
         parse_i64:
          set_has_i64();
          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_INT64>(
                 input, &i64_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(73)) goto parse_sf64;
        break;
      }

      // optional sfixed64 sf64 = 9;
      case 9: {
        if (tag == 73) {
         parse_sf64:
          set_has_sf64();
          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_SFIXED64>(
                 input, &sf64_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(80)) goto parse_s64;
        break;
      }

      // optional sint64 s64 = 10;
      case 10: {
        if (tag == 80) {
         parse_s64:
          set_has_s64();
          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_SINT64>(
                 input, &s64_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(90)) goto parse_str;
        break;
      }

      // optional string str = 11;
      case 11: {
        if (tag == 90) {
         parse_str:
          DO_(::google::protobuf::internal::WireFormatLite::ReadString(
                input, this->mutable_str()));
          ::google::protobuf::internal::WireFormat::VerifyUTF8StringNamedField(
            this->str().data(), this->str().length(),
            ::google::protobuf::internal::WireFormat::PARSE,
            "google.protobuf.testing.Primitive.str");
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(98)) goto parse_bytes;
        break;
      }

      // optional bytes bytes = 12;
      case 12: {
        if (tag == 98) {
         parse_bytes:
          DO_(::google::protobuf::internal::WireFormatLite::ReadBytes(
                input, this->mutable_bytes()));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(109)) goto parse_float;
        break;
      }

      // optional float float = 13;
      case 13: {
        if (tag == 109) {
         parse_float:
          set_has_float_();
          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   float, ::google::protobuf::internal::WireFormatLite::TYPE_FLOAT>(
                 input, &float__)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(113)) goto parse_double;
        break;
      }

      // optional double double = 14;
      case 14: {
        if (tag == 113) {
         parse_double:
          set_has_double_();
          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   double, ::google::protobuf::internal::WireFormatLite::TYPE_DOUBLE>(
                 input, &double__)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(120)) goto parse_bool;
        break;
      }

      // optional bool bool = 15;
      case 15: {
        if (tag == 120) {
         parse_bool:
          set_has_bool_();
          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   bool, ::google::protobuf::internal::WireFormatLite::TYPE_BOOL>(
                 input, &bool__)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(133)) goto parse_rep_fix32;
        break;
      }

      // repeated fixed32 rep_fix32 = 16;
      case 16: {
        if (tag == 133) {
         parse_rep_fix32:
          DO_((::google::protobuf::internal::WireFormatLite::ReadRepeatedPrimitive<
                   ::google::protobuf::uint32, ::google::protobuf::internal::WireFormatLite::TYPE_FIXED32>(
                 2, 133, input, this->mutable_rep_fix32())));
        } else if (tag == 130) {
          DO_((::google::protobuf::internal::WireFormatLite::ReadPackedPrimitiveNoInline<
                   ::google::protobuf::uint32, ::google::protobuf::internal::WireFormatLite::TYPE_FIXED32>(
                 input, this->mutable_rep_fix32())));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(133)) goto parse_rep_fix32;
        if (input->ExpectTag(136)) goto parse_rep_u32;
        break;
      }

      // repeated uint32 rep_u32 = 17;
      case 17: {
        if (tag == 136) {
         parse_rep_u32:
          DO_((::google::protobuf::internal::WireFormatLite::ReadRepeatedPrimitive<
                   ::google::protobuf::uint32, ::google::protobuf::internal::WireFormatLite::TYPE_UINT32>(
                 2, 136, input, this->mutable_rep_u32())));
        } else if (tag == 138) {
          DO_((::google::protobuf::internal::WireFormatLite::ReadPackedPrimitiveNoInline<
                   ::google::protobuf::uint32, ::google::protobuf::internal::WireFormatLite::TYPE_UINT32>(
                 input, this->mutable_rep_u32())));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(136)) goto parse_rep_u32;
        if (input->ExpectTag(144)) goto parse_rep_i32;
        break;
      }

      // repeated int32 rep_i32 = 18;
      case 18: {
        if (tag == 144) {
         parse_rep_i32:
          DO_((::google::protobuf::internal::WireFormatLite::ReadRepeatedPrimitive<
                   ::google::protobuf::int32, ::google::protobuf::internal::WireFormatLite::TYPE_INT32>(
                 2, 144, input, this->mutable_rep_i32())));
        } else if (tag == 146) {
          DO_((::google::protobuf::internal::WireFormatLite::ReadPackedPrimitiveNoInline<
                   ::google::protobuf::int32, ::google::protobuf::internal::WireFormatLite::TYPE_INT32>(
                 input, this->mutable_rep_i32())));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(144)) goto parse_rep_i32;
        if (input->ExpectTag(157)) goto parse_rep_sf32;
        break;
      }

      // repeated sfixed32 rep_sf32 = 19;
      case 19: {
        if (tag == 157) {
         parse_rep_sf32:
          DO_((::google::protobuf::internal::WireFormatLite::ReadRepeatedPrimitive<
                   ::google::protobuf::int32, ::google::protobuf::internal::WireFormatLite::TYPE_SFIXED32>(
                 2, 157, input, this->mutable_rep_sf32())));
        } else if (tag == 154) {
          DO_((::google::protobuf::internal::WireFormatLite::ReadPackedPrimitiveNoInline<
                   ::google::protobuf::int32, ::google::protobuf::internal::WireFormatLite::TYPE_SFIXED32>(
                 input, this->mutable_rep_sf32())));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(157)) goto parse_rep_sf32;
        if (input->ExpectTag(160)) goto parse_rep_s32;
        break;
      }

      // repeated sint32 rep_s32 = 20;
      case 20: {
        if (tag == 160) {
         parse_rep_s32:
          DO_((::google::protobuf::internal::WireFormatLite::ReadRepeatedPrimitive<
                   ::google::protobuf::int32, ::google::protobuf::internal::WireFormatLite::TYPE_SINT32>(
                 2, 160, input, this->mutable_rep_s32())));
        } else if (tag == 162) {
          DO_((::google::protobuf::internal::WireFormatLite::ReadPackedPrimitiveNoInline<
                   ::google::protobuf::int32, ::google::protobuf::internal::WireFormatLite::TYPE_SINT32>(
                 input, this->mutable_rep_s32())));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(160)) goto parse_rep_s32;
        if (input->ExpectTag(169)) goto parse_rep_fix64;
        break;
      }

      // repeated fixed64 rep_fix64 = 21;
      case 21: {
        if (tag == 169) {
         parse_rep_fix64:
          DO_((::google::protobuf::internal::WireFormatLite::ReadRepeatedPrimitive<
                   ::google::protobuf::uint64, ::google::protobuf::internal::WireFormatLite::TYPE_FIXED64>(
                 2, 169, input, this->mutable_rep_fix64())));
        } else if (tag == 170) {
          DO_((::google::protobuf::internal::WireFormatLite::ReadPackedPrimitiveNoInline<
                   ::google::protobuf::uint64, ::google::protobuf::internal::WireFormatLite::TYPE_FIXED64>(
                 input, this->mutable_rep_fix64())));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(169)) goto parse_rep_fix64;
        if (input->ExpectTag(176)) goto parse_rep_u64;
        break;
      }

      // repeated uint64 rep_u64 = 22;
      case 22: {
        if (tag == 176) {
         parse_rep_u64:
          DO_((::google::protobuf::internal::WireFormatLite::ReadRepeatedPrimitive<
                   ::google::protobuf::uint64, ::google::protobuf::internal::WireFormatLite::TYPE_UINT64>(
                 2, 176, input, this->mutable_rep_u64())));
        } else if (tag == 178) {
          DO_((::google::protobuf::internal::WireFormatLite::ReadPackedPrimitiveNoInline<
                   ::google::protobuf::uint64, ::google::protobuf::internal::WireFormatLite::TYPE_UINT64>(
                 input, this->mutable_rep_u64())));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(176)) goto parse_rep_u64;
        if (input->ExpectTag(184)) goto parse_rep_i64;
        break;
      }

      // repeated int64 rep_i64 = 23;
      case 23: {
        if (tag == 184) {
         parse_rep_i64:
          DO_((::google::protobuf::internal::WireFormatLite::ReadRepeatedPrimitive<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_INT64>(
                 2, 184, input, this->mutable_rep_i64())));
        } else if (tag == 186) {
          DO_((::google::protobuf::internal::WireFormatLite::ReadPackedPrimitiveNoInline<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_INT64>(
                 input, this->mutable_rep_i64())));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(184)) goto parse_rep_i64;
        if (input->ExpectTag(193)) goto parse_rep_sf64;
        break;
      }

      // repeated sfixed64 rep_sf64 = 24;
      case 24: {
        if (tag == 193) {
         parse_rep_sf64:
          DO_((::google::protobuf::internal::WireFormatLite::ReadRepeatedPrimitive<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_SFIXED64>(
                 2, 193, input, this->mutable_rep_sf64())));
        } else if (tag == 194) {
          DO_((::google::protobuf::internal::WireFormatLite::ReadPackedPrimitiveNoInline<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_SFIXED64>(
                 input, this->mutable_rep_sf64())));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(193)) goto parse_rep_sf64;
        if (input->ExpectTag(200)) goto parse_rep_s64;
        break;
      }

      // repeated sint64 rep_s64 = 25;
      case 25: {
        if (tag == 200) {
         parse_rep_s64:
          DO_((::google::protobuf::internal::WireFormatLite::ReadRepeatedPrimitive<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_SINT64>(
                 2, 200, input, this->mutable_rep_s64())));
        } else if (tag == 202) {
          DO_((::google::protobuf::internal::WireFormatLite::ReadPackedPrimitiveNoInline<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_SINT64>(
                 input, this->mutable_rep_s64())));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(200)) goto parse_rep_s64;
        if (input->ExpectTag(210)) goto parse_rep_str;
        break;
      }

      // repeated string rep_str = 26;
      case 26: {
        if (tag == 210) {
         parse_rep_str:
          DO_(::google::protobuf::internal::WireFormatLite::ReadString(
                input, this->add_rep_str()));
          ::google::protobuf::internal::WireFormat::VerifyUTF8StringNamedField(
            this->rep_str(this->rep_str_size() - 1).data(),
            this->rep_str(this->rep_str_size() - 1).length(),
            ::google::protobuf::internal::WireFormat::PARSE,
            "google.protobuf.testing.Primitive.rep_str");
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(210)) goto parse_rep_str;
        if (input->ExpectTag(218)) goto parse_rep_bytes;
        break;
      }

      // repeated bytes rep_bytes = 27;
      case 27: {
        if (tag == 218) {
         parse_rep_bytes:
          DO_(::google::protobuf::internal::WireFormatLite::ReadBytes(
                input, this->add_rep_bytes()));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(218)) goto parse_rep_bytes;
        if (input->ExpectTag(229)) goto parse_rep_float;
        break;
      }

      // repeated float rep_float = 28;
      case 28: {
        if (tag == 229) {
         parse_rep_float:
          DO_((::google::protobuf::internal::WireFormatLite::ReadRepeatedPrimitive<
                   float, ::google::protobuf::internal::WireFormatLite::TYPE_FLOAT>(
                 2, 229, input, this->mutable_rep_float())));
        } else if (tag == 226) {
          DO_((::google::protobuf::internal::WireFormatLite::ReadPackedPrimitiveNoInline<
                   float, ::google::protobuf::internal::WireFormatLite::TYPE_FLOAT>(
                 input, this->mutable_rep_float())));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(229)) goto parse_rep_float;
        if (input->ExpectTag(233)) goto parse_rep_double;
        break;
      }

      // repeated double rep_double = 29;
      case 29: {
        if (tag == 233) {
         parse_rep_double:
          DO_((::google::protobuf::internal::WireFormatLite::ReadRepeatedPrimitive<
                   double, ::google::protobuf::internal::WireFormatLite::TYPE_DOUBLE>(
                 2, 233, input, this->mutable_rep_double())));
        } else if (tag == 234) {
          DO_((::google::protobuf::internal::WireFormatLite::ReadPackedPrimitiveNoInline<
                   double, ::google::protobuf::internal::WireFormatLite::TYPE_DOUBLE>(
                 input, this->mutable_rep_double())));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(233)) goto parse_rep_double;
        if (input->ExpectTag(240)) goto parse_rep_bool;
        break;
      }

      // repeated bool rep_bool = 30;
      case 30: {
        if (tag == 240) {
         parse_rep_bool:
          DO_((::google::protobuf::internal::WireFormatLite::ReadRepeatedPrimitive<
                   bool, ::google::protobuf::internal::WireFormatLite::TYPE_BOOL>(
                 2, 240, input, this->mutable_rep_bool())));
        } else if (tag == 242) {
          DO_((::google::protobuf::internal::WireFormatLite::ReadPackedPrimitiveNoInline<
                   bool, ::google::protobuf::internal::WireFormatLite::TYPE_BOOL>(
                 input, this->mutable_rep_bool())));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(240)) goto parse_rep_bool;
        if (input->ExpectAtEnd()) goto success;
        break;
      }

      default: {
      handle_unusual:
        if (tag == 0 ||
            ::google::protobuf::internal::WireFormatLite::GetTagWireType(tag) ==
            ::google::protobuf::internal::WireFormatLite::WIRETYPE_END_GROUP) {
          goto success;
        }
        DO_(::google::protobuf::internal::WireFormat::SkipField(
              input, tag, mutable_unknown_fields()));
        break;
      }
    }
  }
success:
  // @@protoc_insertion_point(parse_success:google.protobuf.testing.Primitive)
  return true;
failure:
  // @@protoc_insertion_point(parse_failure:google.protobuf.testing.Primitive)
  return false;
#undef DO_
}

void Primitive::SerializeWithCachedSizes(
    ::google::protobuf::io::CodedOutputStream* output) const {
  // @@protoc_insertion_point(serialize_start:google.protobuf.testing.Primitive)
  // optional fixed32 fix32 = 1;
  if (has_fix32()) {
    ::google::protobuf::internal::WireFormatLite::WriteFixed32(1, this->fix32(), output);
  }

  // optional uint32 u32 = 2;
  if (has_u32()) {
    ::google::protobuf::internal::WireFormatLite::WriteUInt32(2, this->u32(), output);
  }

  // optional int32 i32 = 3;
  if (has_i32()) {
    ::google::protobuf::internal::WireFormatLite::WriteInt32(3, this->i32(), output);
  }

  // optional sfixed32 sf32 = 4;
  if (has_sf32()) {
    ::google::protobuf::internal::WireFormatLite::WriteSFixed32(4, this->sf32(), output);
  }

  // optional sint32 s32 = 5;
  if (has_s32()) {
    ::google::protobuf::internal::WireFormatLite::WriteSInt32(5, this->s32(), output);
  }

  // optional fixed64 fix64 = 6;
  if (has_fix64()) {
    ::google::protobuf::internal::WireFormatLite::WriteFixed64(6, this->fix64(), output);
  }

  // optional uint64 u64 = 7;
  if (has_u64()) {
    ::google::protobuf::internal::WireFormatLite::WriteUInt64(7, this->u64(), output);
  }

  // optional int64 i64 = 8;
  if (has_i64()) {
    ::google::protobuf::internal::WireFormatLite::WriteInt64(8, this->i64(), output);
  }

  // optional sfixed64 sf64 = 9;
  if (has_sf64()) {
    ::google::protobuf::internal::WireFormatLite::WriteSFixed64(9, this->sf64(), output);
  }

  // optional sint64 s64 = 10;
  if (has_s64()) {
    ::google::protobuf::internal::WireFormatLite::WriteSInt64(10, this->s64(), output);
  }

  // optional string str = 11;
  if (has_str()) {
    ::google::protobuf::internal::WireFormat::VerifyUTF8StringNamedField(
      this->str().data(), this->str().length(),
      ::google::protobuf::internal::WireFormat::SERIALIZE,
      "google.protobuf.testing.Primitive.str");
    ::google::protobuf::internal::WireFormatLite::WriteStringMaybeAliased(
      11, this->str(), output);
  }

  // optional bytes bytes = 12;
  if (has_bytes()) {
    ::google::protobuf::internal::WireFormatLite::WriteBytesMaybeAliased(
      12, this->bytes(), output);
  }

  // optional float float = 13;
  if (has_float_()) {
    ::google::protobuf::internal::WireFormatLite::WriteFloat(13, this->float_(), output);
  }

  // optional double double = 14;
  if (has_double_()) {
    ::google::protobuf::internal::WireFormatLite::WriteDouble(14, this->double_(), output);
  }

  // optional bool bool = 15;
  if (has_bool_()) {
    ::google::protobuf::internal::WireFormatLite::WriteBool(15, this->bool_(), output);
  }

  // repeated fixed32 rep_fix32 = 16;
  for (int i = 0; i < this->rep_fix32_size(); i++) {
    ::google::protobuf::internal::WireFormatLite::WriteFixed32(
      16, this->rep_fix32(i), output);
  }

  // repeated uint32 rep_u32 = 17;
  for (int i = 0; i < this->rep_u32_size(); i++) {
    ::google::protobuf::internal::WireFormatLite::WriteUInt32(
      17, this->rep_u32(i), output);
  }

  // repeated int32 rep_i32 = 18;
  for (int i = 0; i < this->rep_i32_size(); i++) {
    ::google::protobuf::internal::WireFormatLite::WriteInt32(
      18, this->rep_i32(i), output);
  }

  // repeated sfixed32 rep_sf32 = 19;
  for (int i = 0; i < this->rep_sf32_size(); i++) {
    ::google::protobuf::internal::WireFormatLite::WriteSFixed32(
      19, this->rep_sf32(i), output);
  }

  // repeated sint32 rep_s32 = 20;
  for (int i = 0; i < this->rep_s32_size(); i++) {
    ::google::protobuf::internal::WireFormatLite::WriteSInt32(
      20, this->rep_s32(i), output);
  }

  // repeated fixed64 rep_fix64 = 21;
  for (int i = 0; i < this->rep_fix64_size(); i++) {
    ::google::protobuf::internal::WireFormatLite::WriteFixed64(
      21, this->rep_fix64(i), output);
  }

  // repeated uint64 rep_u64 = 22;
  for (int i = 0; i < this->rep_u64_size(); i++) {
    ::google::protobuf::internal::WireFormatLite::WriteUInt64(
      22, this->rep_u64(i), output);
  }

  // repeated int64 rep_i64 = 23;
  for (int i = 0; i < this->rep_i64_size(); i++) {
    ::google::protobuf::internal::WireFormatLite::WriteInt64(
      23, this->rep_i64(i), output);
  }

  // repeated sfixed64 rep_sf64 = 24;
  for (int i = 0; i < this->rep_sf64_size(); i++) {
    ::google::protobuf::internal::WireFormatLite::WriteSFixed64(
      24, this->rep_sf64(i), output);
  }

  // repeated sint64 rep_s64 = 25;
  for (int i = 0; i < this->rep_s64_size(); i++) {
    ::google::protobuf::internal::WireFormatLite::WriteSInt64(
      25, this->rep_s64(i), output);
  }

  // repeated string rep_str = 26;
  for (int i = 0; i < this->rep_str_size(); i++) {
    ::google::protobuf::internal::WireFormat::VerifyUTF8StringNamedField(
      this->rep_str(i).data(), this->rep_str(i).length(),
      ::google::protobuf::internal::WireFormat::SERIALIZE,
      "google.protobuf.testing.Primitive.rep_str");
    ::google::protobuf::internal::WireFormatLite::WriteString(
      26, this->rep_str(i), output);
  }

  // repeated bytes rep_bytes = 27;
  for (int i = 0; i < this->rep_bytes_size(); i++) {
    ::google::protobuf::internal::WireFormatLite::WriteBytes(
      27, this->rep_bytes(i), output);
  }

  // repeated float rep_float = 28;
  for (int i = 0; i < this->rep_float_size(); i++) {
    ::google::protobuf::internal::WireFormatLite::WriteFloat(
      28, this->rep_float(i), output);
  }

  // repeated double rep_double = 29;
  for (int i = 0; i < this->rep_double_size(); i++) {
    ::google::protobuf::internal::WireFormatLite::WriteDouble(
      29, this->rep_double(i), output);
  }

  // repeated bool rep_bool = 30;
  for (int i = 0; i < this->rep_bool_size(); i++) {
    ::google::protobuf::internal::WireFormatLite::WriteBool(
      30, this->rep_bool(i), output);
  }

  if (_internal_metadata_.have_unknown_fields()) {
    ::google::protobuf::internal::WireFormat::SerializeUnknownFields(
        unknown_fields(), output);
  }
  // @@protoc_insertion_point(serialize_end:google.protobuf.testing.Primitive)
}

::google::protobuf::uint8* Primitive::InternalSerializeWithCachedSizesToArray(
    bool deterministic, ::google::protobuf::uint8* target) const {
  (void)deterministic; // Unused
  // @@protoc_insertion_point(serialize_to_array_start:google.protobuf.testing.Primitive)
  // optional fixed32 fix32 = 1;
  if (has_fix32()) {
    target = ::google::protobuf::internal::WireFormatLite::WriteFixed32ToArray(1, this->fix32(), target);
  }

  // optional uint32 u32 = 2;
  if (has_u32()) {
    target = ::google::protobuf::internal::WireFormatLite::WriteUInt32ToArray(2, this->u32(), target);
  }

  // optional int32 i32 = 3;
  if (has_i32()) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt32ToArray(3, this->i32(), target);
  }

  // optional sfixed32 sf32 = 4;
  if (has_sf32()) {
    target = ::google::protobuf::internal::WireFormatLite::WriteSFixed32ToArray(4, this->sf32(), target);
  }

  // optional sint32 s32 = 5;
  if (has_s32()) {
    target = ::google::protobuf::internal::WireFormatLite::WriteSInt32ToArray(5, this->s32(), target);
  }

  // optional fixed64 fix64 = 6;
  if (has_fix64()) {
    target = ::google::protobuf::internal::WireFormatLite::WriteFixed64ToArray(6, this->fix64(), target);
  }

  // optional uint64 u64 = 7;
  if (has_u64()) {
    target = ::google::protobuf::internal::WireFormatLite::WriteUInt64ToArray(7, this->u64(), target);
  }

  // optional int64 i64 = 8;
  if (has_i64()) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt64ToArray(8, this->i64(), target);
  }

  // optional sfixed64 sf64 = 9;
  if (has_sf64()) {
    target = ::google::protobuf::internal::WireFormatLite::WriteSFixed64ToArray(9, this->sf64(), target);
  }

  // optional sint64 s64 = 10;
  if (has_s64()) {
    target = ::google::protobuf::internal::WireFormatLite::WriteSInt64ToArray(10, this->s64(), target);
  }

  // optional string str = 11;
  if (has_str()) {
    ::google::protobuf::internal::WireFormat::VerifyUTF8StringNamedField(
      this->str().data(), this->str().length(),
      ::google::protobuf::internal::WireFormat::SERIALIZE,
      "google.protobuf.testing.Primitive.str");
    target =
      ::google::protobuf::internal::WireFormatLite::WriteStringToArray(
        11, this->str(), target);
  }

  // optional bytes bytes = 12;
  if (has_bytes()) {
    target =
      ::google::protobuf::internal::WireFormatLite::WriteBytesToArray(
        12, this->bytes(), target);
  }

  // optional float float = 13;
  if (has_float_()) {
    target = ::google::protobuf::internal::WireFormatLite::WriteFloatToArray(13, this->float_(), target);
  }

  // optional double double = 14;
  if (has_double_()) {
    target = ::google::protobuf::internal::WireFormatLite::WriteDoubleToArray(14, this->double_(), target);
  }

  // optional bool bool = 15;
  if (has_bool_()) {
    target = ::google::protobuf::internal::WireFormatLite::WriteBoolToArray(15, this->bool_(), target);
  }

  // repeated fixed32 rep_fix32 = 16;
  for (int i = 0; i < this->rep_fix32_size(); i++) {
    target = ::google::protobuf::internal::WireFormatLite::
      WriteFixed32ToArray(16, this->rep_fix32(i), target);
  }

  // repeated uint32 rep_u32 = 17;
  for (int i = 0; i < this->rep_u32_size(); i++) {
    target = ::google::protobuf::internal::WireFormatLite::
      WriteUInt32ToArray(17, this->rep_u32(i), target);
  }

  // repeated int32 rep_i32 = 18;
  for (int i = 0; i < this->rep_i32_size(); i++) {
    target = ::google::protobuf::internal::WireFormatLite::
      WriteInt32ToArray(18, this->rep_i32(i), target);
  }

  // repeated sfixed32 rep_sf32 = 19;
  for (int i = 0; i < this->rep_sf32_size(); i++) {
    target = ::google::protobuf::internal::WireFormatLite::
      WriteSFixed32ToArray(19, this->rep_sf32(i), target);
  }

  // repeated sint32 rep_s32 = 20;
  for (int i = 0; i < this->rep_s32_size(); i++) {
    target = ::google::protobuf::internal::WireFormatLite::
      WriteSInt32ToArray(20, this->rep_s32(i), target);
  }

  // repeated fixed64 rep_fix64 = 21;
  for (int i = 0; i < this->rep_fix64_size(); i++) {
    target = ::google::protobuf::internal::WireFormatLite::
      WriteFixed64ToArray(21, this->rep_fix64(i), target);
  }

  // repeated uint64 rep_u64 = 22;
  for (int i = 0; i < this->rep_u64_size(); i++) {
    target = ::google::protobuf::internal::WireFormatLite::
      WriteUInt64ToArray(22, this->rep_u64(i), target);
  }

  // repeated int64 rep_i64 = 23;
  for (int i = 0; i < this->rep_i64_size(); i++) {
    target = ::google::protobuf::internal::WireFormatLite::
      WriteInt64ToArray(23, this->rep_i64(i), target);
  }

  // repeated sfixed64 rep_sf64 = 24;
  for (int i = 0; i < this->rep_sf64_size(); i++) {
    target = ::google::protobuf::internal::WireFormatLite::
      WriteSFixed64ToArray(24, this->rep_sf64(i), target);
  }

  // repeated sint64 rep_s64 = 25;
  for (int i = 0; i < this->rep_s64_size(); i++) {
    target = ::google::protobuf::internal::WireFormatLite::
      WriteSInt64ToArray(25, this->rep_s64(i), target);
  }

  // repeated string rep_str = 26;
  for (int i = 0; i < this->rep_str_size(); i++) {
    ::google::protobuf::internal::WireFormat::VerifyUTF8StringNamedField(
      this->rep_str(i).data(), this->rep_str(i).length(),
      ::google::protobuf::internal::WireFormat::SERIALIZE,
      "google.protobuf.testing.Primitive.rep_str");
    target = ::google::protobuf::internal::WireFormatLite::
      WriteStringToArray(26, this->rep_str(i), target);
  }

  // repeated bytes rep_bytes = 27;
  for (int i = 0; i < this->rep_bytes_size(); i++) {
    target = ::google::protobuf::internal::WireFormatLite::
      WriteBytesToArray(27, this->rep_bytes(i), target);
  }

  // repeated float rep_float = 28;
  for (int i = 0; i < this->rep_float_size(); i++) {
    target = ::google::protobuf::internal::WireFormatLite::
      WriteFloatToArray(28, this->rep_float(i), target);
  }

  // repeated double rep_double = 29;
  for (int i = 0; i < this->rep_double_size(); i++) {
    target = ::google::protobuf::internal::WireFormatLite::
      WriteDoubleToArray(29, this->rep_double(i), target);
  }

  // repeated bool rep_bool = 30;
  for (int i = 0; i < this->rep_bool_size(); i++) {
    target = ::google::protobuf::internal::WireFormatLite::
      WriteBoolToArray(30, this->rep_bool(i), target);
  }

  if (_internal_metadata_.have_unknown_fields()) {
    target = ::google::protobuf::internal::WireFormat::SerializeUnknownFieldsToArray(
        unknown_fields(), target);
  }
  // @@protoc_insertion_point(serialize_to_array_end:google.protobuf.testing.Primitive)
  return target;
}

size_t Primitive::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:google.protobuf.testing.Primitive)
  size_t total_size = 0;

  if (_has_bits_[0 / 32] & 255u) {
    // optional fixed32 fix32 = 1;
    if (has_fix32()) {
      total_size += 1 + 4;
    }

    // optional uint32 u32 = 2;
    if (has_u32()) {
      total_size += 1 +
        ::google::protobuf::internal::WireFormatLite::UInt32Size(
          this->u32());
    }

    // optional int32 i32 = 3;
    if (has_i32()) {
      total_size += 1 +
        ::google::protobuf::internal::WireFormatLite::Int32Size(
          this->i32());
    }

    // optional sfixed32 sf32 = 4;
    if (has_sf32()) {
      total_size += 1 + 4;
    }

    // optional sint32 s32 = 5;
    if (has_s32()) {
      total_size += 1 +
        ::google::protobuf::internal::WireFormatLite::SInt32Size(
          this->s32());
    }

    // optional fixed64 fix64 = 6;
    if (has_fix64()) {
      total_size += 1 + 8;
    }

    // optional uint64 u64 = 7;
    if (has_u64()) {
      total_size += 1 +
        ::google::protobuf::internal::WireFormatLite::UInt64Size(
          this->u64());
    }

    // optional int64 i64 = 8;
    if (has_i64()) {
      total_size += 1 +
        ::google::protobuf::internal::WireFormatLite::Int64Size(
          this->i64());
    }

  }
  if (_has_bits_[8 / 32] & 32512u) {
    // optional sfixed64 sf64 = 9;
    if (has_sf64()) {
      total_size += 1 + 8;
    }

    // optional sint64 s64 = 10;
    if (has_s64()) {
      total_size += 1 +
        ::google::protobuf::internal::WireFormatLite::SInt64Size(
          this->s64());
    }

    // optional string str = 11;
    if (has_str()) {
      total_size += 1 +
        ::google::protobuf::internal::WireFormatLite::StringSize(
          this->str());
    }

    // optional bytes bytes = 12;
    if (has_bytes()) {
      total_size += 1 +
        ::google::protobuf::internal::WireFormatLite::BytesSize(
          this->bytes());
    }

    // optional float float = 13;
    if (has_float_()) {
      total_size += 1 + 4;
    }

    // optional double double = 14;
    if (has_double_()) {
      total_size += 1 + 8;
    }

    // optional bool bool = 15;
    if (has_bool_()) {
      total_size += 1 + 1;
    }

  }
  // repeated fixed32 rep_fix32 = 16;
  {
    size_t data_size = 0;
    unsigned int count = this->rep_fix32_size();
    data_size = 4UL * count;
    total_size += 2 *
                  ::google::protobuf::internal::FromIntSize(this->rep_fix32_size());
    total_size += data_size;
  }

  // repeated uint32 rep_u32 = 17;
  {
    size_t data_size = 0;
    unsigned int count = this->rep_u32_size();
    for (unsigned int i = 0; i < count; i++) {
      data_size += ::google::protobuf::internal::WireFormatLite::
        UInt32Size(this->rep_u32(i));
    }
    total_size += 2 *
                  ::google::protobuf::internal::FromIntSize(this->rep_u32_size());
    total_size += data_size;
  }

  // repeated int32 rep_i32 = 18;
  {
    size_t data_size = 0;
    unsigned int count = this->rep_i32_size();
    for (unsigned int i = 0; i < count; i++) {
      data_size += ::google::protobuf::internal::WireFormatLite::
        Int32Size(this->rep_i32(i));
    }
    total_size += 2 *
                  ::google::protobuf::internal::FromIntSize(this->rep_i32_size());
    total_size += data_size;
  }

  // repeated sfixed32 rep_sf32 = 19;
  {
    size_t data_size = 0;
    unsigned int count = this->rep_sf32_size();
    data_size = 4UL * count;
    total_size += 2 *
                  ::google::protobuf::internal::FromIntSize(this->rep_sf32_size());
    total_size += data_size;
  }

  // repeated sint32 rep_s32 = 20;
  {
    size_t data_size = 0;
    unsigned int count = this->rep_s32_size();
    for (unsigned int i = 0; i < count; i++) {
      data_size += ::google::protobuf::internal::WireFormatLite::
        SInt32Size(this->rep_s32(i));
    }
    total_size += 2 *
                  ::google::protobuf::internal::FromIntSize(this->rep_s32_size());
    total_size += data_size;
  }

  // repeated fixed64 rep_fix64 = 21;
  {
    size_t data_size = 0;
    unsigned int count = this->rep_fix64_size();
    data_size = 8UL * count;
    total_size += 2 *
                  ::google::protobuf::internal::FromIntSize(this->rep_fix64_size());
    total_size += data_size;
  }

  // repeated uint64 rep_u64 = 22;
  {
    size_t data_size = 0;
    unsigned int count = this->rep_u64_size();
    for (unsigned int i = 0; i < count; i++) {
      data_size += ::google::protobuf::internal::WireFormatLite::
        UInt64Size(this->rep_u64(i));
    }
    total_size += 2 *
                  ::google::protobuf::internal::FromIntSize(this->rep_u64_size());
    total_size += data_size;
  }

  // repeated int64 rep_i64 = 23;
  {
    size_t data_size = 0;
    unsigned int count = this->rep_i64_size();
    for (unsigned int i = 0; i < count; i++) {
      data_size += ::google::protobuf::internal::WireFormatLite::
        Int64Size(this->rep_i64(i));
    }
    total_size += 2 *
                  ::google::protobuf::internal::FromIntSize(this->rep_i64_size());
    total_size += data_size;
  }

  // repeated sfixed64 rep_sf64 = 24;
  {
    size_t data_size = 0;
    unsigned int count = this->rep_sf64_size();
    data_size = 8UL * count;
    total_size += 2 *
                  ::google::protobuf::internal::FromIntSize(this->rep_sf64_size());
    total_size += data_size;
  }

  // repeated sint64 rep_s64 = 25;
  {
    size_t data_size = 0;
    unsigned int count = this->rep_s64_size();
    for (unsigned int i = 0; i < count; i++) {
      data_size += ::google::protobuf::internal::WireFormatLite::
        SInt64Size(this->rep_s64(i));
    }
    total_size += 2 *
                  ::google::protobuf::internal::FromIntSize(this->rep_s64_size());
    total_size += data_size;
  }

  // repeated string rep_str = 26;
  total_size += 2 *
      ::google::protobuf::internal::FromIntSize(this->rep_str_size());
  for (int i = 0; i < this->rep_str_size(); i++) {
    total_size += ::google::protobuf::internal::WireFormatLite::StringSize(
      this->rep_str(i));
  }

  // repeated bytes rep_bytes = 27;
  total_size += 2 *
      ::google::protobuf::internal::FromIntSize(this->rep_bytes_size());
  for (int i = 0; i < this->rep_bytes_size(); i++) {
    total_size += ::google::protobuf::internal::WireFormatLite::BytesSize(
      this->rep_bytes(i));
  }

  // repeated float rep_float = 28;
  {
    size_t data_size = 0;
    unsigned int count = this->rep_float_size();
    data_size = 4UL * count;
    total_size += 2 *
                  ::google::protobuf::internal::FromIntSize(this->rep_float_size());
    total_size += data_size;
  }

  // repeated double rep_double = 29;
  {
    size_t data_size = 0;
    unsigned int count = this->rep_double_size();
    data_size = 8UL * count;
    total_size += 2 *
                  ::google::protobuf::internal::FromIntSize(this->rep_double_size());
    total_size += data_size;
  }

  // repeated bool rep_bool = 30;
  {
    size_t data_size = 0;
    unsigned int count = this->rep_bool_size();
    data_size = 1UL * count;
    total_size += 2 *
                  ::google::protobuf::internal::FromIntSize(this->rep_bool_size());
    total_size += data_size;
  }

  if (_internal_metadata_.have_unknown_fields()) {
    total_size +=
      ::google::protobuf::internal::WireFormat::ComputeUnknownFieldsSize(
        unknown_fields());
  }
  int cached_size = ::google::protobuf::internal::ToCachedSize(total_size);
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = cached_size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
  return total_size;
}

void Primitive::MergeFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_merge_from_start:google.protobuf.testing.Primitive)
  if (GOOGLE_PREDICT_FALSE(&from == this)) MergeFromFail(__LINE__);
  const Primitive* source =
      ::google::protobuf::internal::DynamicCastToGenerated<const Primitive>(
          &from);
  if (source == NULL) {
  // @@protoc_insertion_point(generalized_merge_from_cast_fail:google.protobuf.testing.Primitive)
    ::google::protobuf::internal::ReflectionOps::Merge(from, this);
  } else {
  // @@protoc_insertion_point(generalized_merge_from_cast_success:google.protobuf.testing.Primitive)
    UnsafeMergeFrom(*source);
  }
}

void Primitive::MergeFrom(const Primitive& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:google.protobuf.testing.Primitive)
  if (GOOGLE_PREDICT_TRUE(&from != this)) {
    UnsafeMergeFrom(from);
  } else {
    MergeFromFail(__LINE__);
  }
}

void Primitive::UnsafeMergeFrom(const Primitive& from) {
  GOOGLE_DCHECK(&from != this);
  rep_fix32_.UnsafeMergeFrom(from.rep_fix32_);
  rep_u32_.UnsafeMergeFrom(from.rep_u32_);
  rep_i32_.UnsafeMergeFrom(from.rep_i32_);
  rep_sf32_.UnsafeMergeFrom(from.rep_sf32_);
  rep_s32_.UnsafeMergeFrom(from.rep_s32_);
  rep_fix64_.UnsafeMergeFrom(from.rep_fix64_);
  rep_u64_.UnsafeMergeFrom(from.rep_u64_);
  rep_i64_.UnsafeMergeFrom(from.rep_i64_);
  rep_sf64_.UnsafeMergeFrom(from.rep_sf64_);
  rep_s64_.UnsafeMergeFrom(from.rep_s64_);
  rep_str_.UnsafeMergeFrom(from.rep_str_);
  rep_bytes_.UnsafeMergeFrom(from.rep_bytes_);
  rep_float_.UnsafeMergeFrom(from.rep_float_);
  rep_double_.UnsafeMergeFrom(from.rep_double_);
  rep_bool_.UnsafeMergeFrom(from.rep_bool_);
  if (from._has_bits_[0 / 32] & (0xffu << (0 % 32))) {
    if (from.has_fix32()) {
      set_fix32(from.fix32());
    }
    if (from.has_u32()) {
      set_u32(from.u32());
    }
    if (from.has_i32()) {
      set_i32(from.i32());
    }
    if (from.has_sf32()) {
      set_sf32(from.sf32());
    }
    if (from.has_s32()) {
      set_s32(from.s32());
    }
    if (from.has_fix64()) {
      set_fix64(from.fix64());
    }
    if (from.has_u64()) {
      set_u64(from.u64());
    }
    if (from.has_i64()) {
      set_i64(from.i64());
    }
  }
  if (from._has_bits_[8 / 32] & (0xffu << (8 % 32))) {
    if (from.has_sf64()) {
      set_sf64(from.sf64());
    }
    if (from.has_s64()) {
      set_s64(from.s64());
    }
    if (from.has_str()) {
      set_has_str();
      str_.AssignWithDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), from.str_);
    }
    if (from.has_bytes()) {
      set_has_bytes();
      bytes_.AssignWithDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), from.bytes_);
    }
    if (from.has_float_()) {
      set_float_(from.float_());
    }
    if (from.has_double_()) {
      set_double_(from.double_());
    }
    if (from.has_bool_()) {
      set_bool_(from.bool_());
    }
  }
  if (from._internal_metadata_.have_unknown_fields()) {
    ::google::protobuf::UnknownFieldSet::MergeToInternalMetdata(
      from.unknown_fields(), &_internal_metadata_);
  }
}

void Primitive::CopyFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_copy_from_start:google.protobuf.testing.Primitive)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

void Primitive::CopyFrom(const Primitive& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:google.protobuf.testing.Primitive)
  if (&from == this) return;
  Clear();
  UnsafeMergeFrom(from);
}

bool Primitive::IsInitialized() const {

  return true;
}

void Primitive::Swap(Primitive* other) {
  if (other == this) return;
  InternalSwap(other);
}
void Primitive::InternalSwap(Primitive* other) {
  std::swap(fix32_, other->fix32_);
  std::swap(u32_, other->u32_);
  std::swap(i32_, other->i32_);
  std::swap(sf32_, other->sf32_);
  std::swap(s32_, other->s32_);
  std::swap(fix64_, other->fix64_);
  std::swap(u64_, other->u64_);
  std::swap(i64_, other->i64_);
  std::swap(sf64_, other->sf64_);
  std::swap(s64_, other->s64_);
  str_.Swap(&other->str_);
  bytes_.Swap(&other->bytes_);
  std::swap(float__, other->float__);
  std::swap(double__, other->double__);
  std::swap(bool__, other->bool__);
  rep_fix32_.UnsafeArenaSwap(&other->rep_fix32_);
  rep_u32_.UnsafeArenaSwap(&other->rep_u32_);
  rep_i32_.UnsafeArenaSwap(&other->rep_i32_);
  rep_sf32_.UnsafeArenaSwap(&other->rep_sf32_);
  rep_s32_.UnsafeArenaSwap(&other->rep_s32_);
  rep_fix64_.UnsafeArenaSwap(&other->rep_fix64_);
  rep_u64_.UnsafeArenaSwap(&other->rep_u64_);
  rep_i64_.UnsafeArenaSwap(&other->rep_i64_);
  rep_sf64_.UnsafeArenaSwap(&other->rep_sf64_);
  rep_s64_.UnsafeArenaSwap(&other->rep_s64_);
  rep_str_.UnsafeArenaSwap(&other->rep_str_);
  rep_bytes_.UnsafeArenaSwap(&other->rep_bytes_);
  rep_float_.UnsafeArenaSwap(&other->rep_float_);
  rep_double_.UnsafeArenaSwap(&other->rep_double_);
  rep_bool_.UnsafeArenaSwap(&other->rep_bool_);
  std::swap(_has_bits_[0], other->_has_bits_[0]);
  _internal_metadata_.Swap(&other->_internal_metadata_);
  std::swap(_cached_size_, other->_cached_size_);
}

::google::protobuf::Metadata Primitive::GetMetadata() const {
  protobuf_AssignDescriptorsOnce();
  ::google::protobuf::Metadata metadata;
  metadata.descriptor = Primitive_descriptor_;
  metadata.reflection = Primitive_reflection_;
  return metadata;
}

#if PROTOBUF_INLINE_NOT_IN_HEADERS
// Primitive

// optional fixed32 fix32 = 1;
bool Primitive::has_fix32() const {
  return (_has_bits_[0] & 0x00000001u) != 0;
}
void Primitive::set_has_fix32() {
  _has_bits_[0] |= 0x00000001u;
}
void Primitive::clear_has_fix32() {
  _has_bits_[0] &= ~0x00000001u;
}
void Primitive::clear_fix32() {
  fix32_ = 0u;
  clear_has_fix32();
}
::google::protobuf::uint32 Primitive::fix32() const {
  // @@protoc_insertion_point(field_get:google.protobuf.testing.Primitive.fix32)
  return fix32_;
}
void Primitive::set_fix32(::google::protobuf::uint32 value) {
  set_has_fix32();
  fix32_ = value;
  // @@protoc_insertion_point(field_set:google.protobuf.testing.Primitive.fix32)
}

// optional uint32 u32 = 2;
bool Primitive::has_u32() const {
  return (_has_bits_[0] & 0x00000002u) != 0;
}
void Primitive::set_has_u32() {
  _has_bits_[0] |= 0x00000002u;
}
void Primitive::clear_has_u32() {
  _has_bits_[0] &= ~0x00000002u;
}
void Primitive::clear_u32() {
  u32_ = 0u;
  clear_has_u32();
}
::google::protobuf::uint32 Primitive::u32() const {
  // @@protoc_insertion_point(field_get:google.protobuf.testing.Primitive.u32)
  return u32_;
}
void Primitive::set_u32(::google::protobuf::uint32 value) {
  set_has_u32();
  u32_ = value;
  // @@protoc_insertion_point(field_set:google.protobuf.testing.Primitive.u32)
}

// optional int32 i32 = 3;
bool Primitive::has_i32() const {
  return (_has_bits_[0] & 0x00000004u) != 0;
}
void Primitive::set_has_i32() {
  _has_bits_[0] |= 0x00000004u;
}
void Primitive::clear_has_i32() {
  _has_bits_[0] &= ~0x00000004u;
}
void Primitive::clear_i32() {
  i32_ = 0;
  clear_has_i32();
}
::google::protobuf::int32 Primitive::i32() const {
  // @@protoc_insertion_point(field_get:google.protobuf.testing.Primitive.i32)
  return i32_;
}
void Primitive::set_i32(::google::protobuf::int32 value) {
  set_has_i32();
  i32_ = value;
  // @@protoc_insertion_point(field_set:google.protobuf.testing.Primitive.i32)
}

// optional sfixed32 sf32 = 4;
bool Primitive::has_sf32() const {
  return (_has_bits_[0] & 0x00000008u) != 0;
}
void Primitive::set_has_sf32() {
  _has_bits_[0] |= 0x00000008u;
}
void Primitive::clear_has_sf32() {
  _has_bits_[0] &= ~0x00000008u;
}
void Primitive::clear_sf32() {
  sf32_ = 0;
  clear_has_sf32();
}
::google::protobuf::int32 Primitive::sf32() const {
  // @@protoc_insertion_point(field_get:google.protobuf.testing.Primitive.sf32)
  return sf32_;
}
void Primitive::set_sf32(::google::protobuf::int32 value) {
  set_has_sf32();
  sf32_ = value;
  // @@protoc_insertion_point(field_set:google.protobuf.testing.Primitive.sf32)
}

// optional sint32 s32 = 5;
bool Primitive::has_s32() const {
  return (_has_bits_[0] & 0x00000010u) != 0;
}
void Primitive::set_has_s32() {
  _has_bits_[0] |= 0x00000010u;
}
void Primitive::clear_has_s32() {
  _has_bits_[0] &= ~0x00000010u;
}
void Primitive::clear_s32() {
  s32_ = 0;
  clear_has_s32();
}
::google::protobuf::int32 Primitive::s32() const {
  // @@protoc_insertion_point(field_get:google.protobuf.testing.Primitive.s32)
  return s32_;
}
void Primitive::set_s32(::google::protobuf::int32 value) {
  set_has_s32();
  s32_ = value;
  // @@protoc_insertion_point(field_set:google.protobuf.testing.Primitive.s32)
}

// optional fixed64 fix64 = 6;
bool Primitive::has_fix64() const {
  return (_has_bits_[0] & 0x00000020u) != 0;
}
void Primitive::set_has_fix64() {
  _has_bits_[0] |= 0x00000020u;
}
void Primitive::clear_has_fix64() {
  _has_bits_[0] &= ~0x00000020u;
}
void Primitive::clear_fix64() {
  fix64_ = GOOGLE_ULONGLONG(0);
  clear_has_fix64();
}
::google::protobuf::uint64 Primitive::fix64() const {
  // @@protoc_insertion_point(field_get:google.protobuf.testing.Primitive.fix64)
  return fix64_;
}
void Primitive::set_fix64(::google::protobuf::uint64 value) {
  set_has_fix64();
  fix64_ = value;
  // @@protoc_insertion_point(field_set:google.protobuf.testing.Primitive.fix64)
}

// optional uint64 u64 = 7;
bool Primitive::has_u64() const {
  return (_has_bits_[0] & 0x00000040u) != 0;
}
void Primitive::set_has_u64() {
  _has_bits_[0] |= 0x00000040u;
}
void Primitive::clear_has_u64() {
  _has_bits_[0] &= ~0x00000040u;
}
void Primitive::clear_u64() {
  u64_ = GOOGLE_ULONGLONG(0);
  clear_has_u64();
}
::google::protobuf::uint64 Primitive::u64() const {
  // @@protoc_insertion_point(field_get:google.protobuf.testing.Primitive.u64)
  return u64_;
}
void Primitive::set_u64(::google::protobuf::uint64 value) {
  set_has_u64();
  u64_ = value;
  // @@protoc_insertion_point(field_set:google.protobuf.testing.Primitive.u64)
}

// optional int64 i64 = 8;
bool Primitive::has_i64() const {
  return (_has_bits_[0] & 0x00000080u) != 0;
}
void Primitive::set_has_i64() {
  _has_bits_[0] |= 0x00000080u;
}
void Primitive::clear_has_i64() {
  _has_bits_[0] &= ~0x00000080u;
}
void Primitive::clear_i64() {
  i64_ = GOOGLE_LONGLONG(0);
  clear_has_i64();
}
::google::protobuf::int64 Primitive::i64() const {
  // @@protoc_insertion_point(field_get:google.protobuf.testing.Primitive.i64)
  return i64_;
}
void Primitive::set_i64(::google::protobuf::int64 value) {
  set_has_i64();
  i64_ = value;
  // @@protoc_insertion_point(field_set:google.protobuf.testing.Primitive.i64)
}

// optional sfixed64 sf64 = 9;
bool Primitive::has_sf64() const {
  return (_has_bits_[0] & 0x00000100u) != 0;
}
void Primitive::set_has_sf64() {
  _has_bits_[0] |= 0x00000100u;
}
void Primitive::clear_has_sf64() {
  _has_bits_[0] &= ~0x00000100u;
}
void Primitive::clear_sf64() {
  sf64_ = GOOGLE_LONGLONG(0);
  clear_has_sf64();
}
::google::protobuf::int64 Primitive::sf64() const {
  // @@protoc_insertion_point(field_get:google.protobuf.testing.Primitive.sf64)
  return sf64_;
}
void Primitive::set_sf64(::google::protobuf::int64 value) {
  set_has_sf64();
  sf64_ = value;
  // @@protoc_insertion_point(field_set:google.protobuf.testing.Primitive.sf64)
}

// optional sint64 s64 = 10;
bool Primitive::has_s64() const {
  return (_has_bits_[0] & 0x00000200u) != 0;
}
void Primitive::set_has_s64() {
  _has_bits_[0] |= 0x00000200u;
}
void Primitive::clear_has_s64() {
  _has_bits_[0] &= ~0x00000200u;
}
void Primitive::clear_s64() {
  s64_ = GOOGLE_LONGLONG(0);
  clear_has_s64();
}
::google::protobuf::int64 Primitive::s64() const {
  // @@protoc_insertion_point(field_get:google.protobuf.testing.Primitive.s64)
  return s64_;
}
void Primitive::set_s64(::google::protobuf::int64 value) {
  set_has_s64();
  s64_ = value;
  // @@protoc_insertion_point(field_set:google.protobuf.testing.Primitive.s64)
}

// optional string str = 11;
bool Primitive::has_str() const {
  return (_has_bits_[0] & 0x00000400u) != 0;
}
void Primitive::set_has_str() {
  _has_bits_[0] |= 0x00000400u;
}
void Primitive::clear_has_str() {
  _has_bits_[0] &= ~0x00000400u;
}
void Primitive::clear_str() {
  str_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  clear_has_str();
}
const ::std::string& Primitive::str() const {
  // @@protoc_insertion_point(field_get:google.protobuf.testing.Primitive.str)
  return str_.GetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
void Primitive::set_str(const ::std::string& value) {
  set_has_str();
  str_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:google.protobuf.testing.Primitive.str)
}
void Primitive::set_str(const char* value) {
  set_has_str();
  str_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:google.protobuf.testing.Primitive.str)
}
void Primitive::set_str(const char* value, size_t size) {
  set_has_str();
  str_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:google.protobuf.testing.Primitive.str)
}
::std::string* Primitive::mutable_str() {
  set_has_str();
  // @@protoc_insertion_point(field_mutable:google.protobuf.testing.Primitive.str)
  return str_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
::std::string* Primitive::release_str() {
  // @@protoc_insertion_point(field_release:google.protobuf.testing.Primitive.str)
  clear_has_str();
  return str_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
void Primitive::set_allocated_str(::std::string* str) {
  if (str != NULL) {
    set_has_str();
  } else {
    clear_has_str();
  }
  str_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), str);
  // @@protoc_insertion_point(field_set_allocated:google.protobuf.testing.Primitive.str)
}

// optional bytes bytes = 12;
bool Primitive::has_bytes() const {
  return (_has_bits_[0] & 0x00000800u) != 0;
}
void Primitive::set_has_bytes() {
  _has_bits_[0] |= 0x00000800u;
}
void Primitive::clear_has_bytes() {
  _has_bits_[0] &= ~0x00000800u;
}
void Primitive::clear_bytes() {
  bytes_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  clear_has_bytes();
}
const ::std::string& Primitive::bytes() const {
  // @@protoc_insertion_point(field_get:google.protobuf.testing.Primitive.bytes)
  return bytes_.GetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
void Primitive::set_bytes(const ::std::string& value) {
  set_has_bytes();
  bytes_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:google.protobuf.testing.Primitive.bytes)
}
void Primitive::set_bytes(const char* value) {
  set_has_bytes();
  bytes_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:google.protobuf.testing.Primitive.bytes)
}
void Primitive::set_bytes(const void* value, size_t size) {
  set_has_bytes();
  bytes_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:google.protobuf.testing.Primitive.bytes)
}
::std::string* Primitive::mutable_bytes() {
  set_has_bytes();
  // @@protoc_insertion_point(field_mutable:google.protobuf.testing.Primitive.bytes)
  return bytes_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
::std::string* Primitive::release_bytes() {
  // @@protoc_insertion_point(field_release:google.protobuf.testing.Primitive.bytes)
  clear_has_bytes();
  return bytes_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
void Primitive::set_allocated_bytes(::std::string* bytes) {
  if (bytes != NULL) {
    set_has_bytes();
  } else {
    clear_has_bytes();
  }
  bytes_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), bytes);
  // @@protoc_insertion_point(field_set_allocated:google.protobuf.testing.Primitive.bytes)
}

// optional float float = 13;
bool Primitive::has_float_() const {
  return (_has_bits_[0] & 0x00001000u) != 0;
}
void Primitive::set_has_float_() {
  _has_bits_[0] |= 0x00001000u;
}
void Primitive::clear_has_float_() {
  _has_bits_[0] &= ~0x00001000u;
}
void Primitive::clear_float_() {
  float__ = 0;
  clear_has_float_();
}
float Primitive::float_() const {
  // @@protoc_insertion_point(field_get:google.protobuf.testing.Primitive.float)
  return float__;
}
void Primitive::set_float_(float value) {
  set_has_float_();
  float__ = value;
  // @@protoc_insertion_point(field_set:google.protobuf.testing.Primitive.float)
}

// optional double double = 14;
bool Primitive::has_double_() const {
  return (_has_bits_[0] & 0x00002000u) != 0;
}
void Primitive::set_has_double_() {
  _has_bits_[0] |= 0x00002000u;
}
void Primitive::clear_has_double_() {
  _has_bits_[0] &= ~0x00002000u;
}
void Primitive::clear_double_() {
  double__ = 0;
  clear_has_double_();
}
double Primitive::double_() const {
  // @@protoc_insertion_point(field_get:google.protobuf.testing.Primitive.double)
  return double__;
}
void Primitive::set_double_(double value) {
  set_has_double_();
  double__ = value;
  // @@protoc_insertion_point(field_set:google.protobuf.testing.Primitive.double)
}

// optional bool bool = 15;
bool Primitive::has_bool_() const {
  return (_has_bits_[0] & 0x00004000u) != 0;
}
void Primitive::set_has_bool_() {
  _has_bits_[0] |= 0x00004000u;
}
void Primitive::clear_has_bool_() {
  _has_bits_[0] &= ~0x00004000u;
}
void Primitive::clear_bool_() {
  bool__ = false;
  clear_has_bool_();
}
bool Primitive::bool_() const {
  // @@protoc_insertion_point(field_get:google.protobuf.testing.Primitive.bool)
  return bool__;
}
void Primitive::set_bool_(bool value) {
  set_has_bool_();
  bool__ = value;
  // @@protoc_insertion_point(field_set:google.protobuf.testing.Primitive.bool)
}

// repeated fixed32 rep_fix32 = 16;
int Primitive::rep_fix32_size() const {
  return rep_fix32_.size();
}
void Primitive::clear_rep_fix32() {
  rep_fix32_.Clear();
}
::google::protobuf::uint32 Primitive::rep_fix32(int index) const {
  // @@protoc_insertion_point(field_get:google.protobuf.testing.Primitive.rep_fix32)
  return rep_fix32_.Get(index);
}
void Primitive::set_rep_fix32(int index, ::google::protobuf::uint32 value) {
  rep_fix32_.Set(index, value);
  // @@protoc_insertion_point(field_set:google.protobuf.testing.Primitive.rep_fix32)
}
void Primitive::add_rep_fix32(::google::protobuf::uint32 value) {
  rep_fix32_.Add(value);
  // @@protoc_insertion_point(field_add:google.protobuf.testing.Primitive.rep_fix32)
}
const ::google::protobuf::RepeatedField< ::google::protobuf::uint32 >&
Primitive::rep_fix32() const {
  // @@protoc_insertion_point(field_list:google.protobuf.testing.Primitive.rep_fix32)
  return rep_fix32_;
}
::google::protobuf::RepeatedField< ::google::protobuf::uint32 >*
Primitive::mutable_rep_fix32() {
  // @@protoc_insertion_point(field_mutable_list:google.protobuf.testing.Primitive.rep_fix32)
  return &rep_fix32_;
}

// repeated uint32 rep_u32 = 17;
int Primitive::rep_u32_size() const {
  return rep_u32_.size();
}
void Primitive::clear_rep_u32() {
  rep_u32_.Clear();
}
::google::protobuf::uint32 Primitive::rep_u32(int index) const {
  // @@protoc_insertion_point(field_get:google.protobuf.testing.Primitive.rep_u32)
  return rep_u32_.Get(index);
}
void Primitive::set_rep_u32(int index, ::google::protobuf::uint32 value) {
  rep_u32_.Set(index, value);
  // @@protoc_insertion_point(field_set:google.protobuf.testing.Primitive.rep_u32)
}
void Primitive::add_rep_u32(::google::protobuf::uint32 value) {
  rep_u32_.Add(value);
  // @@protoc_insertion_point(field_add:google.protobuf.testing.Primitive.rep_u32)
}
const ::google::protobuf::RepeatedField< ::google::protobuf::uint32 >&
Primitive::rep_u32() const {
  // @@protoc_insertion_point(field_list:google.protobuf.testing.Primitive.rep_u32)
  return rep_u32_;
}
::google::protobuf::RepeatedField< ::google::protobuf::uint32 >*
Primitive::mutable_rep_u32() {
  // @@protoc_insertion_point(field_mutable_list:google.protobuf.testing.Primitive.rep_u32)
  return &rep_u32_;
}

// repeated int32 rep_i32 = 18;
int Primitive::rep_i32_size() const {
  return rep_i32_.size();
}
void Primitive::clear_rep_i32() {
  rep_i32_.Clear();
}
::google::protobuf::int32 Primitive::rep_i32(int index) const {
  // @@protoc_insertion_point(field_get:google.protobuf.testing.Primitive.rep_i32)
  return rep_i32_.Get(index);
}
void Primitive::set_rep_i32(int index, ::google::protobuf::int32 value) {
  rep_i32_.Set(index, value);
  // @@protoc_insertion_point(field_set:google.protobuf.testing.Primitive.rep_i32)
}
void Primitive::add_rep_i32(::google::protobuf::int32 value) {
  rep_i32_.Add(value);
  // @@protoc_insertion_point(field_add:google.protobuf.testing.Primitive.rep_i32)
}
const ::google::protobuf::RepeatedField< ::google::protobuf::int32 >&
Primitive::rep_i32() const {
  // @@protoc_insertion_point(field_list:google.protobuf.testing.Primitive.rep_i32)
  return rep_i32_;
}
::google::protobuf::RepeatedField< ::google::protobuf::int32 >*
Primitive::mutable_rep_i32() {
  // @@protoc_insertion_point(field_mutable_list:google.protobuf.testing.Primitive.rep_i32)
  return &rep_i32_;
}

// repeated sfixed32 rep_sf32 = 19;
int Primitive::rep_sf32_size() const {
  return rep_sf32_.size();
}
void Primitive::clear_rep_sf32() {
  rep_sf32_.Clear();
}
::google::protobuf::int32 Primitive::rep_sf32(int index) const {
  // @@protoc_insertion_point(field_get:google.protobuf.testing.Primitive.rep_sf32)
  return rep_sf32_.Get(index);
}
void Primitive::set_rep_sf32(int index, ::google::protobuf::int32 value) {
  rep_sf32_.Set(index, value);
  // @@protoc_insertion_point(field_set:google.protobuf.testing.Primitive.rep_sf32)
}
void Primitive::add_rep_sf32(::google::protobuf::int32 value) {
  rep_sf32_.Add(value);
  // @@protoc_insertion_point(field_add:google.protobuf.testing.Primitive.rep_sf32)
}
const ::google::protobuf::RepeatedField< ::google::protobuf::int32 >&
Primitive::rep_sf32() const {
  // @@protoc_insertion_point(field_list:google.protobuf.testing.Primitive.rep_sf32)
  return rep_sf32_;
}
::google::protobuf::RepeatedField< ::google::protobuf::int32 >*
Primitive::mutable_rep_sf32() {
  // @@protoc_insertion_point(field_mutable_list:google.protobuf.testing.Primitive.rep_sf32)
  return &rep_sf32_;
}

// repeated sint32 rep_s32 = 20;
int Primitive::rep_s32_size() const {
  return rep_s32_.size();
}
void Primitive::clear_rep_s32() {
  rep_s32_.Clear();
}
::google::protobuf::int32 Primitive::rep_s32(int index) const {
  // @@protoc_insertion_point(field_get:google.protobuf.testing.Primitive.rep_s32)
  return rep_s32_.Get(index);
}
void Primitive::set_rep_s32(int index, ::google::protobuf::int32 value) {
  rep_s32_.Set(index, value);
  // @@protoc_insertion_point(field_set:google.protobuf.testing.Primitive.rep_s32)
}
void Primitive::add_rep_s32(::google::protobuf::int32 value) {
  rep_s32_.Add(value);
  // @@protoc_insertion_point(field_add:google.protobuf.testing.Primitive.rep_s32)
}
const ::google::protobuf::RepeatedField< ::google::protobuf::int32 >&
Primitive::rep_s32() const {
  // @@protoc_insertion_point(field_list:google.protobuf.testing.Primitive.rep_s32)
  return rep_s32_;
}
::google::protobuf::RepeatedField< ::google::protobuf::int32 >*
Primitive::mutable_rep_s32() {
  // @@protoc_insertion_point(field_mutable_list:google.protobuf.testing.Primitive.rep_s32)
  return &rep_s32_;
}

// repeated fixed64 rep_fix64 = 21;
int Primitive::rep_fix64_size() const {
  return rep_fix64_.size();
}
void Primitive::clear_rep_fix64() {
  rep_fix64_.Clear();
}
::google::protobuf::uint64 Primitive::rep_fix64(int index) const {
  // @@protoc_insertion_point(field_get:google.protobuf.testing.Primitive.rep_fix64)
  return rep_fix64_.Get(index);
}
void Primitive::set_rep_fix64(int index, ::google::protobuf::uint64 value) {
  rep_fix64_.Set(index, value);
  // @@protoc_insertion_point(field_set:google.protobuf.testing.Primitive.rep_fix64)
}
void Primitive::add_rep_fix64(::google::protobuf::uint64 value) {
  rep_fix64_.Add(value);
  // @@protoc_insertion_point(field_add:google.protobuf.testing.Primitive.rep_fix64)
}
const ::google::protobuf::RepeatedField< ::google::protobuf::uint64 >&
Primitive::rep_fix64() const {
  // @@protoc_insertion_point(field_list:google.protobuf.testing.Primitive.rep_fix64)
  return rep_fix64_;
}
::google::protobuf::RepeatedField< ::google::protobuf::uint64 >*
Primitive::mutable_rep_fix64() {
  // @@protoc_insertion_point(field_mutable_list:google.protobuf.testing.Primitive.rep_fix64)
  return &rep_fix64_;
}

// repeated uint64 rep_u64 = 22;
int Primitive::rep_u64_size() const {
  return rep_u64_.size();
}
void Primitive::clear_rep_u64() {
  rep_u64_.Clear();
}
::google::protobuf::uint64 Primitive::rep_u64(int index) const {
  // @@protoc_insertion_point(field_get:google.protobuf.testing.Primitive.rep_u64)
  return rep_u64_.Get(index);
}
void Primitive::set_rep_u64(int index, ::google::protobuf::uint64 value) {
  rep_u64_.Set(index, value);
  // @@protoc_insertion_point(field_set:google.protobuf.testing.Primitive.rep_u64)
}
void Primitive::add_rep_u64(::google::protobuf::uint64 value) {
  rep_u64_.Add(value);
  // @@protoc_insertion_point(field_add:google.protobuf.testing.Primitive.rep_u64)
}
const ::google::protobuf::RepeatedField< ::google::protobuf::uint64 >&
Primitive::rep_u64() const {
  // @@protoc_insertion_point(field_list:google.protobuf.testing.Primitive.rep_u64)
  return rep_u64_;
}
::google::protobuf::RepeatedField< ::google::protobuf::uint64 >*
Primitive::mutable_rep_u64() {
  // @@protoc_insertion_point(field_mutable_list:google.protobuf.testing.Primitive.rep_u64)
  return &rep_u64_;
}

// repeated int64 rep_i64 = 23;
int Primitive::rep_i64_size() const {
  return rep_i64_.size();
}
void Primitive::clear_rep_i64() {
  rep_i64_.Clear();
}
::google::protobuf::int64 Primitive::rep_i64(int index) const {
  // @@protoc_insertion_point(field_get:google.protobuf.testing.Primitive.rep_i64)
  return rep_i64_.Get(index);
}
void Primitive::set_rep_i64(int index, ::google::protobuf::int64 value) {
  rep_i64_.Set(index, value);
  // @@protoc_insertion_point(field_set:google.protobuf.testing.Primitive.rep_i64)
}
void Primitive::add_rep_i64(::google::protobuf::int64 value) {
  rep_i64_.Add(value);
  // @@protoc_insertion_point(field_add:google.protobuf.testing.Primitive.rep_i64)
}
const ::google::protobuf::RepeatedField< ::google::protobuf::int64 >&
Primitive::rep_i64() const {
  // @@protoc_insertion_point(field_list:google.protobuf.testing.Primitive.rep_i64)
  return rep_i64_;
}
::google::protobuf::RepeatedField< ::google::protobuf::int64 >*
Primitive::mutable_rep_i64() {
  // @@protoc_insertion_point(field_mutable_list:google.protobuf.testing.Primitive.rep_i64)
  return &rep_i64_;
}

// repeated sfixed64 rep_sf64 = 24;
int Primitive::rep_sf64_size() const {
  return rep_sf64_.size();
}
void Primitive::clear_rep_sf64() {
  rep_sf64_.Clear();
}
::google::protobuf::int64 Primitive::rep_sf64(int index) const {
  // @@protoc_insertion_point(field_get:google.protobuf.testing.Primitive.rep_sf64)
  return rep_sf64_.Get(index);
}
void Primitive::set_rep_sf64(int index, ::google::protobuf::int64 value) {
  rep_sf64_.Set(index, value);
  // @@protoc_insertion_point(field_set:google.protobuf.testing.Primitive.rep_sf64)
}
void Primitive::add_rep_sf64(::google::protobuf::int64 value) {
  rep_sf64_.Add(value);
  // @@protoc_insertion_point(field_add:google.protobuf.testing.Primitive.rep_sf64)
}
const ::google::protobuf::RepeatedField< ::google::protobuf::int64 >&
Primitive::rep_sf64() const {
  // @@protoc_insertion_point(field_list:google.protobuf.testing.Primitive.rep_sf64)
  return rep_sf64_;
}
::google::protobuf::RepeatedField< ::google::protobuf::int64 >*
Primitive::mutable_rep_sf64() {
  // @@protoc_insertion_point(field_mutable_list:google.protobuf.testing.Primitive.rep_sf64)
  return &rep_sf64_;
}

// repeated sint64 rep_s64 = 25;
int Primitive::rep_s64_size() const {
  return rep_s64_.size();
}
void Primitive::clear_rep_s64() {
  rep_s64_.Clear();
}
::google::protobuf::int64 Primitive::rep_s64(int index) const {
  // @@protoc_insertion_point(field_get:google.protobuf.testing.Primitive.rep_s64)
  return rep_s64_.Get(index);
}
void Primitive::set_rep_s64(int index, ::google::protobuf::int64 value) {
  rep_s64_.Set(index, value);
  // @@protoc_insertion_point(field_set:google.protobuf.testing.Primitive.rep_s64)
}
void Primitive::add_rep_s64(::google::protobuf::int64 value) {
  rep_s64_.Add(value);
  // @@protoc_insertion_point(field_add:google.protobuf.testing.Primitive.rep_s64)
}
const ::google::protobuf::RepeatedField< ::google::protobuf::int64 >&
Primitive::rep_s64() const {
  // @@protoc_insertion_point(field_list:google.protobuf.testing.Primitive.rep_s64)
  return rep_s64_;
}
::google::protobuf::RepeatedField< ::google::protobuf::int64 >*
Primitive::mutable_rep_s64() {
  // @@protoc_insertion_point(field_mutable_list:google.protobuf.testing.Primitive.rep_s64)
  return &rep_s64_;
}

// repeated string rep_str = 26;
int Primitive::rep_str_size() const {
  return rep_str_.size();
}
void Primitive::clear_rep_str() {
  rep_str_.Clear();
}
const ::std::string& Primitive::rep_str(int index) const {
  // @@protoc_insertion_point(field_get:google.protobuf.testing.Primitive.rep_str)
  return rep_str_.Get(index);
}
::std::string* Primitive::mutable_rep_str(int index) {
  // @@protoc_insertion_point(field_mutable:google.protobuf.testing.Primitive.rep_str)
  return rep_str_.Mutable(index);
}
void Primitive::set_rep_str(int index, const ::std::string& value) {
  // @@protoc_insertion_point(field_set:google.protobuf.testing.Primitive.rep_str)
  rep_str_.Mutable(index)->assign(value);
}
void Primitive::set_rep_str(int index, const char* value) {
  rep_str_.Mutable(index)->assign(value);
  // @@protoc_insertion_point(field_set_char:google.protobuf.testing.Primitive.rep_str)
}
void Primitive::set_rep_str(int index, const char* value, size_t size) {
  rep_str_.Mutable(index)->assign(
    reinterpret_cast<const char*>(value), size);
  // @@protoc_insertion_point(field_set_pointer:google.protobuf.testing.Primitive.rep_str)
}
::std::string* Primitive::add_rep_str() {
  // @@protoc_insertion_point(field_add_mutable:google.protobuf.testing.Primitive.rep_str)
  return rep_str_.Add();
}
void Primitive::add_rep_str(const ::std::string& value) {
  rep_str_.Add()->assign(value);
  // @@protoc_insertion_point(field_add:google.protobuf.testing.Primitive.rep_str)
}
void Primitive::add_rep_str(const char* value) {
  rep_str_.Add()->assign(value);
  // @@protoc_insertion_point(field_add_char:google.protobuf.testing.Primitive.rep_str)
}
void Primitive::add_rep_str(const char* value, size_t size) {
  rep_str_.Add()->assign(reinterpret_cast<const char*>(value), size);
  // @@protoc_insertion_point(field_add_pointer:google.protobuf.testing.Primitive.rep_str)
}
const ::google::protobuf::RepeatedPtrField< ::std::string>&
Primitive::rep_str() const {
  // @@protoc_insertion_point(field_list:google.protobuf.testing.Primitive.rep_str)
  return rep_str_;
}
::google::protobuf::RepeatedPtrField< ::std::string>*
Primitive::mutable_rep_str() {
  // @@protoc_insertion_point(field_mutable_list:google.protobuf.testing.Primitive.rep_str)
  return &rep_str_;
}

// repeated bytes rep_bytes = 27;
int Primitive::rep_bytes_size() const {
  return rep_bytes_.size();
}
void Primitive::clear_rep_bytes() {
  rep_bytes_.Clear();
}
const ::std::string& Primitive::rep_bytes(int index) const {
  // @@protoc_insertion_point(field_get:google.protobuf.testing.Primitive.rep_bytes)
  return rep_bytes_.Get(index);
}
::std::string* Primitive::mutable_rep_bytes(int index) {
  // @@protoc_insertion_point(field_mutable:google.protobuf.testing.Primitive.rep_bytes)
  return rep_bytes_.Mutable(index);
}
void Primitive::set_rep_bytes(int index, const ::std::string& value) {
  // @@protoc_insertion_point(field_set:google.protobuf.testing.Primitive.rep_bytes)
  rep_bytes_.Mutable(index)->assign(value);
}
void Primitive::set_rep_bytes(int index, const char* value) {
  rep_bytes_.Mutable(index)->assign(value);
  // @@protoc_insertion_point(field_set_char:google.protobuf.testing.Primitive.rep_bytes)
}
void Primitive::set_rep_bytes(int index, const void* value, size_t size) {
  rep_bytes_.Mutable(index)->assign(
    reinterpret_cast<const char*>(value), size);
  // @@protoc_insertion_point(field_set_pointer:google.protobuf.testing.Primitive.rep_bytes)
}
::std::string* Primitive::add_rep_bytes() {
  // @@protoc_insertion_point(field_add_mutable:google.protobuf.testing.Primitive.rep_bytes)
  return rep_bytes_.Add();
}
void Primitive::add_rep_bytes(const ::std::string& value) {
  rep_bytes_.Add()->assign(value);
  // @@protoc_insertion_point(field_add:google.protobuf.testing.Primitive.rep_bytes)
}
void Primitive::add_rep_bytes(const char* value) {
  rep_bytes_.Add()->assign(value);
  // @@protoc_insertion_point(field_add_char:google.protobuf.testing.Primitive.rep_bytes)
}
void Primitive::add_rep_bytes(const void* value, size_t size) {
  rep_bytes_.Add()->assign(reinterpret_cast<const char*>(value), size);
  // @@protoc_insertion_point(field_add_pointer:google.protobuf.testing.Primitive.rep_bytes)
}
const ::google::protobuf::RepeatedPtrField< ::std::string>&
Primitive::rep_bytes() const {
  // @@protoc_insertion_point(field_list:google.protobuf.testing.Primitive.rep_bytes)
  return rep_bytes_;
}
::google::protobuf::RepeatedPtrField< ::std::string>*
Primitive::mutable_rep_bytes() {
  // @@protoc_insertion_point(field_mutable_list:google.protobuf.testing.Primitive.rep_bytes)
  return &rep_bytes_;
}

// repeated float rep_float = 28;
int Primitive::rep_float_size() const {
  return rep_float_.size();
}
void Primitive::clear_rep_float() {
  rep_float_.Clear();
}
float Primitive::rep_float(int index) const {
  // @@protoc_insertion_point(field_get:google.protobuf.testing.Primitive.rep_float)
  return rep_float_.Get(index);
}
void Primitive::set_rep_float(int index, float value) {
  rep_float_.Set(index, value);
  // @@protoc_insertion_point(field_set:google.protobuf.testing.Primitive.rep_float)
}
void Primitive::add_rep_float(float value) {
  rep_float_.Add(value);
  // @@protoc_insertion_point(field_add:google.protobuf.testing.Primitive.rep_float)
}
const ::google::protobuf::RepeatedField< float >&
Primitive::rep_float() const {
  // @@protoc_insertion_point(field_list:google.protobuf.testing.Primitive.rep_float)
  return rep_float_;
}
::google::protobuf::RepeatedField< float >*
Primitive::mutable_rep_float() {
  // @@protoc_insertion_point(field_mutable_list:google.protobuf.testing.Primitive.rep_float)
  return &rep_float_;
}

// repeated double rep_double = 29;
int Primitive::rep_double_size() const {
  return rep_double_.size();
}
void Primitive::clear_rep_double() {
  rep_double_.Clear();
}
double Primitive::rep_double(int index) const {
  // @@protoc_insertion_point(field_get:google.protobuf.testing.Primitive.rep_double)
  return rep_double_.Get(index);
}
void Primitive::set_rep_double(int index, double value) {
  rep_double_.Set(index, value);
  // @@protoc_insertion_point(field_set:google.protobuf.testing.Primitive.rep_double)
}
void Primitive::add_rep_double(double value) {
  rep_double_.Add(value);
  // @@protoc_insertion_point(field_add:google.protobuf.testing.Primitive.rep_double)
}
const ::google::protobuf::RepeatedField< double >&
Primitive::rep_double() const {
  // @@protoc_insertion_point(field_list:google.protobuf.testing.Primitive.rep_double)
  return rep_double_;
}
::google::protobuf::RepeatedField< double >*
Primitive::mutable_rep_double() {
  // @@protoc_insertion_point(field_mutable_list:google.protobuf.testing.Primitive.rep_double)
  return &rep_double_;
}

// repeated bool rep_bool = 30;
int Primitive::rep_bool_size() const {
  return rep_bool_.size();
}
void Primitive::clear_rep_bool() {
  rep_bool_.Clear();
}
bool Primitive::rep_bool(int index) const {
  // @@protoc_insertion_point(field_get:google.protobuf.testing.Primitive.rep_bool)
  return rep_bool_.Get(index);
}
void Primitive::set_rep_bool(int index, bool value) {
  rep_bool_.Set(index, value);
  // @@protoc_insertion_point(field_set:google.protobuf.testing.Primitive.rep_bool)
}
void Primitive::add_rep_bool(bool value) {
  rep_bool_.Add(value);
  // @@protoc_insertion_point(field_add:google.protobuf.testing.Primitive.rep_bool)
}
const ::google::protobuf::RepeatedField< bool >&
Primitive::rep_bool() const {
  // @@protoc_insertion_point(field_list:google.protobuf.testing.Primitive.rep_bool)
  return rep_bool_;
}
::google::protobuf::RepeatedField< bool >*
Primitive::mutable_rep_bool() {
  // @@protoc_insertion_point(field_mutable_list:google.protobuf.testing.Primitive.rep_bool)
  return &rep_bool_;
}

inline const Primitive* Primitive::internal_default_instance() {
  return &Primitive_default_instance_.get();
}
#endif  // PROTOBUF_INLINE_NOT_IN_HEADERS

// ===================================================================

#if !defined(_MSC_VER) || _MSC_VER >= 1900
const int PackedPrimitive::kRepFix32FieldNumber;
const int PackedPrimitive::kRepU32FieldNumber;
const int PackedPrimitive::kRepI32FieldNumber;
const int PackedPrimitive::kRepSf32FieldNumber;
const int PackedPrimitive::kRepS32FieldNumber;
const int PackedPrimitive::kRepFix64FieldNumber;
const int PackedPrimitive::kRepU64FieldNumber;
const int PackedPrimitive::kRepI64FieldNumber;
const int PackedPrimitive::kRepSf64FieldNumber;
const int PackedPrimitive::kRepS64FieldNumber;
const int PackedPrimitive::kRepFloatFieldNumber;
const int PackedPrimitive::kRepDoubleFieldNumber;
const int PackedPrimitive::kRepBoolFieldNumber;
#endif  // !defined(_MSC_VER) || _MSC_VER >= 1900

PackedPrimitive::PackedPrimitive()
  : ::google::protobuf::Message(), _internal_metadata_(NULL) {
  if (this != internal_default_instance()) protobuf_InitDefaults_google_2fprotobuf_2futil_2finternal_2ftestdata_2fbooks_2eproto();
  SharedCtor();
  // @@protoc_insertion_point(constructor:google.protobuf.testing.PackedPrimitive)
}

void PackedPrimitive::InitAsDefaultInstance() {
}

PackedPrimitive::PackedPrimitive(const PackedPrimitive& from)
  : ::google::protobuf::Message(),
    _internal_metadata_(NULL) {
  SharedCtor();
  UnsafeMergeFrom(from);
  // @@protoc_insertion_point(copy_constructor:google.protobuf.testing.PackedPrimitive)
}

void PackedPrimitive::SharedCtor() {
  _cached_size_ = 0;
}

PackedPrimitive::~PackedPrimitive() {
  // @@protoc_insertion_point(destructor:google.protobuf.testing.PackedPrimitive)
  SharedDtor();
}

void PackedPrimitive::SharedDtor() {
}

void PackedPrimitive::SetCachedSize(int size) const {
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
}
const ::google::protobuf::Descriptor* PackedPrimitive::descriptor() {
  protobuf_AssignDescriptorsOnce();
  return PackedPrimitive_descriptor_;
}

const PackedPrimitive& PackedPrimitive::default_instance() {
  protobuf_InitDefaults_google_2fprotobuf_2futil_2finternal_2ftestdata_2fbooks_2eproto();
  return *internal_default_instance();
}

::google::protobuf::internal::ExplicitlyConstructed<PackedPrimitive> PackedPrimitive_default_instance_;

PackedPrimitive* PackedPrimitive::New(::google::protobuf::Arena* arena) const {
  PackedPrimitive* n = new PackedPrimitive;
  if (arena != NULL) {
    arena->Own(n);
  }
  return n;
}

void PackedPrimitive::Clear() {
// @@protoc_insertion_point(message_clear_start:google.protobuf.testing.PackedPrimitive)
  rep_fix32_.Clear();
  rep_u32_.Clear();
  rep_i32_.Clear();
  rep_sf32_.Clear();
  rep_s32_.Clear();
  rep_fix64_.Clear();
  rep_u64_.Clear();
  rep_i64_.Clear();
  rep_sf64_.Clear();
  rep_s64_.Clear();
  rep_float_.Clear();
  rep_double_.Clear();
  rep_bool_.Clear();
  _has_bits_.Clear();
  if (_internal_metadata_.have_unknown_fields()) {
    mutable_unknown_fields()->Clear();
  }
}

bool PackedPrimitive::MergePartialFromCodedStream(
    ::google::protobuf::io::CodedInputStream* input) {
#define DO_(EXPRESSION) if (!GOOGLE_PREDICT_TRUE(EXPRESSION)) goto failure
  ::google::protobuf::uint32 tag;
  // @@protoc_insertion_point(parse_start:google.protobuf.testing.PackedPrimitive)
  for (;;) {
    ::std::pair< ::google::protobuf::uint32, bool> p = input->ReadTagWithCutoff(16383);
    tag = p.first;
    if (!p.second) goto handle_unusual;
    switch (::google::protobuf::internal::WireFormatLite::GetTagFieldNumber(tag)) {
      // repeated fixed32 rep_fix32 = 16 [packed = true];
      case 16: {
        if (tag == 130) {
          DO_((::google::protobuf::internal::WireFormatLite::ReadPackedPrimitive<
                   ::google::protobuf::uint32, ::google::protobuf::internal::WireFormatLite::TYPE_FIXED32>(
                 input, this->mutable_rep_fix32())));
        } else if (tag == 133) {
          DO_((::google::protobuf::internal::WireFormatLite::ReadRepeatedPrimitiveNoInline<
                   ::google::protobuf::uint32, ::google::protobuf::internal::WireFormatLite::TYPE_FIXED32>(
                 2, 130, input, this->mutable_rep_fix32())));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(138)) goto parse_rep_u32;
        break;
      }

      // repeated uint32 rep_u32 = 17 [packed = true];
      case 17: {
        if (tag == 138) {
         parse_rep_u32:
          DO_((::google::protobuf::internal::WireFormatLite::ReadPackedPrimitive<
                   ::google::protobuf::uint32, ::google::protobuf::internal::WireFormatLite::TYPE_UINT32>(
                 input, this->mutable_rep_u32())));
        } else if (tag == 136) {
          DO_((::google::protobuf::internal::WireFormatLite::ReadRepeatedPrimitiveNoInline<
                   ::google::protobuf::uint32, ::google::protobuf::internal::WireFormatLite::TYPE_UINT32>(
                 2, 138, input, this->mutable_rep_u32())));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(146)) goto parse_rep_i32;
        break;
      }

      // repeated int32 rep_i32 = 18 [packed = true];
      case 18: {
        if (tag == 146) {
         parse_rep_i32:
          DO_((::google::protobuf::internal::WireFormatLite::ReadPackedPrimitive<
                   ::google::protobuf::int32, ::google::protobuf::internal::WireFormatLite::TYPE_INT32>(
                 input, this->mutable_rep_i32())));
        } else if (tag == 144) {
          DO_((::google::protobuf::internal::WireFormatLite::ReadRepeatedPrimitiveNoInline<
                   ::google::protobuf::int32, ::google::protobuf::internal::WireFormatLite::TYPE_INT32>(
                 2, 146, input, this->mutable_rep_i32())));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(154)) goto parse_rep_sf32;
        break;
      }

      // repeated sfixed32 rep_sf32 = 19 [packed = true];
      case 19: {
        if (tag == 154) {
         parse_rep_sf32:
          DO_((::google::protobuf::internal::WireFormatLite::ReadPackedPrimitive<
                   ::google::protobuf::int32, ::google::protobuf::internal::WireFormatLite::TYPE_SFIXED32>(
                 input, this->mutable_rep_sf32())));
        } else if (tag == 157) {
          DO_((::google::protobuf::internal::WireFormatLite::ReadRepeatedPrimitiveNoInline<
                   ::google::protobuf::int32, ::google::protobuf::internal::WireFormatLite::TYPE_SFIXED32>(
                 2, 154, input, this->mutable_rep_sf32())));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(162)) goto parse_rep_s32;
        break;
      }

      // repeated sint32 rep_s32 = 20 [packed = true];
      case 20: {
        if (tag == 162) {
         parse_rep_s32:
          DO_((::google::protobuf::internal::WireFormatLite::ReadPackedPrimitive<
                   ::google::protobuf::int32, ::google::protobuf::internal::WireFormatLite::TYPE_SINT32>(
                 input, this->mutable_rep_s32())));
        } else if (tag == 160) {
          DO_((::google::protobuf::internal::WireFormatLite::ReadRepeatedPrimitiveNoInline<
                   ::google::protobuf::int32, ::google::protobuf::internal::WireFormatLite::TYPE_SINT32>(
                 2, 162, input, this->mutable_rep_s32())));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(170)) goto parse_rep_fix64;
        break;
      }

      // repeated fixed64 rep_fix64 = 21 [packed = true];
      case 21: {
        if (tag == 170) {
         parse_rep_fix64:
          DO_((::google::protobuf::internal::WireFormatLite::ReadPackedPrimitive<
                   ::google::protobuf::uint64, ::google::protobuf::internal::WireFormatLite::TYPE_FIXED64>(
                 input, this->mutable_rep_fix64())));
        } else if (tag == 169) {
          DO_((::google::protobuf::internal::WireFormatLite::ReadRepeatedPrimitiveNoInline<
                   ::google::protobuf::uint64, ::google::protobuf::internal::WireFormatLite::TYPE_FIXED64>(
                 2, 170, input, this->mutable_rep_fix64())));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(178)) goto parse_rep_u64;
        break;
      }

      // repeated uint64 rep_u64 = 22 [packed = true];
      case 22: {
        if (tag == 178) {
         parse_rep_u64:
          DO_((::google::protobuf::internal::WireFormatLite::ReadPackedPrimitive<
                   ::google::protobuf::uint64, ::google::protobuf::internal::WireFormatLite::TYPE_UINT64>(
                 input, this->mutable_rep_u64())));
        } else if (tag == 176) {
          DO_((::google::protobuf::internal::WireFormatLite::ReadRepeatedPrimitiveNoInline<
                   ::google::protobuf::uint64, ::google::protobuf::internal::WireFormatLite::TYPE_UINT64>(
                 2, 178, input, this->mutable_rep_u64())));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(186)) goto parse_rep_i64;
        break;
      }

      // repeated int64 rep_i64 = 23 [packed = true];
      case 23: {
        if (tag == 186) {
         parse_rep_i64:
          DO_((::google::protobuf::internal::WireFormatLite::ReadPackedPrimitive<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_INT64>(
                 input, this->mutable_rep_i64())));
        } else if (tag == 184) {
          DO_((::google::protobuf::internal::WireFormatLite::ReadRepeatedPrimitiveNoInline<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_INT64>(
                 2, 186, input, this->mutable_rep_i64())));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(194)) goto parse_rep_sf64;
        break;
      }

      // repeated sfixed64 rep_sf64 = 24 [packed = true];
      case 24: {
        if (tag == 194) {
         parse_rep_sf64:
          DO_((::google::protobuf::internal::WireFormatLite::ReadPackedPrimitive<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_SFIXED64>(
                 input, this->mutable_rep_sf64())));
        } else if (tag == 193) {
          DO_((::google::protobuf::internal::WireFormatLite::ReadRepeatedPrimitiveNoInline<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_SFIXED64>(
                 2, 194, input, this->mutable_rep_sf64())));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(202)) goto parse_rep_s64;
        break;
      }

      // repeated sint64 rep_s64 = 25 [packed = true];
      case 25: {
        if (tag == 202) {
         parse_rep_s64:
          DO_((::google::protobuf::internal::WireFormatLite::ReadPackedPrimitive<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_SINT64>(
                 input, this->mutable_rep_s64())));
        } else if (tag == 200) {
          DO_((::google::protobuf::internal::WireFormatLite::ReadRepeatedPrimitiveNoInline<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_SINT64>(
                 2, 202, input, this->mutable_rep_s64())));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(226)) goto parse_rep_float;
        break;
      }

      // repeated float rep_float = 28 [packed = true];
      case 28: {
        if (tag == 226) {
         parse_rep_float:
          DO_((::google::protobuf::internal::WireFormatLite::ReadPackedPrimitive<
                   float, ::google::protobuf::internal::WireFormatLite::TYPE_FLOAT>(
                 input, this->mutable_rep_float())));
        } else if (tag == 229) {
          DO_((::google::protobuf::internal::WireFormatLite::ReadRepeatedPrimitiveNoInline<
                   float, ::google::protobuf::internal::WireFormatLite::TYPE_FLOAT>(
                 2, 226, input, this->mutable_rep_float())));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(234)) goto parse_rep_double;
        break;
      }

      // repeated double rep_double = 29 [packed = true];
      case 29: {
        if (tag == 234) {
         parse_rep_double:
          DO_((::google::protobuf::internal::WireFormatLite::ReadPackedPrimitive<
                   double, ::google::protobuf::internal::WireFormatLite::TYPE_DOUBLE>(
                 input, this->mutable_rep_double())));
        } else if (tag == 233) {
          DO_((::google::protobuf::internal::WireFormatLite::ReadRepeatedPrimitiveNoInline<
                   double, ::google::protobuf::internal::WireFormatLite::TYPE_DOUBLE>(
                 2, 234, input, this->mutable_rep_double())));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(242)) goto parse_rep_bool;
        break;
      }

      // repeated bool rep_bool = 30 [packed = true];
      case 30: {
        if (tag == 242) {
         parse_rep_bool:
          DO_((::google::protobuf::internal::WireFormatLite::ReadPackedPrimitive<
                   bool, ::google::protobuf::internal::WireFormatLite::TYPE_BOOL>(
                 input, this->mutable_rep_bool())));
        } else if (tag == 240) {
          DO_((::google::protobuf::internal::WireFormatLite::ReadRepeatedPrimitiveNoInline<
                   bool, ::google::protobuf::internal::WireFormatLite::TYPE_BOOL>(
                 2, 242, input, this->mutable_rep_bool())));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectAtEnd()) goto success;
        break;
      }

      default: {
      handle_unusual:
        if (tag == 0 ||
            ::google::protobuf::internal::WireFormatLite::GetTagWireType(tag) ==
            ::google::protobuf::internal::WireFormatLite::WIRETYPE_END_GROUP) {
          goto success;
        }
        DO_(::google::protobuf::internal::WireFormat::SkipField(
              input, tag, mutable_unknown_fields()));
        break;
      }
    }
  }
success:
  // @@protoc_insertion_point(parse_success:google.protobuf.testing.PackedPrimitive)
  return true;
failure:
  // @@protoc_insertion_point(parse_failure:google.protobuf.testing.PackedPrimitive)
  return false;
#undef DO_
}

void PackedPrimitive::SerializeWithCachedSizes(
    ::google::protobuf::io::CodedOutputStream* output) const {
  // @@protoc_insertion_point(serialize_start:google.protobuf.testing.PackedPrimitive)
  // repeated fixed32 rep_fix32 = 16 [packed = true];
  if (this->rep_fix32_size() > 0) {
    ::google::protobuf::internal::WireFormatLite::WriteTag(16, ::google::protobuf::internal::WireFormatLite::WIRETYPE_LENGTH_DELIMITED, output);
    output->WriteVarint32(_rep_fix32_cached_byte_size_);
  }
  for (int i = 0; i < this->rep_fix32_size(); i++) {
    ::google::protobuf::internal::WireFormatLite::WriteFixed32NoTag(
      this->rep_fix32(i), output);
  }

  // repeated uint32 rep_u32 = 17 [packed = true];
  if (this->rep_u32_size() > 0) {
    ::google::protobuf::internal::WireFormatLite::WriteTag(17, ::google::protobuf::internal::WireFormatLite::WIRETYPE_LENGTH_DELIMITED, output);
    output->WriteVarint32(_rep_u32_cached_byte_size_);
  }
  for (int i = 0; i < this->rep_u32_size(); i++) {
    ::google::protobuf::internal::WireFormatLite::WriteUInt32NoTag(
      this->rep_u32(i), output);
  }

  // repeated int32 rep_i32 = 18 [packed = true];
  if (this->rep_i32_size() > 0) {
    ::google::protobuf::internal::WireFormatLite::WriteTag(18, ::google::protobuf::internal::WireFormatLite::WIRETYPE_LENGTH_DELIMITED, output);
    output->WriteVarint32(_rep_i32_cached_byte_size_);
  }
  for (int i = 0; i < this->rep_i32_size(); i++) {
    ::google::protobuf::internal::WireFormatLite::WriteInt32NoTag(
      this->rep_i32(i), output);
  }

  // repeated sfixed32 rep_sf32 = 19 [packed = true];
  if (this->rep_sf32_size() > 0) {
    ::google::protobuf::internal::WireFormatLite::WriteTag(19, ::google::protobuf::internal::WireFormatLite::WIRETYPE_LENGTH_DELIMITED, output);
    output->WriteVarint32(_rep_sf32_cached_byte_size_);
  }
  for (int i = 0; i < this->rep_sf32_size(); i++) {
    ::google::protobuf::internal::WireFormatLite::WriteSFixed32NoTag(
      this->rep_sf32(i), output);
  }

  // repeated sint32 rep_s32 = 20 [packed = true];
  if (this->rep_s32_size() > 0) {
    ::google::protobuf::internal::WireFormatLite::WriteTag(20, ::google::protobuf::internal::WireFormatLite::WIRETYPE_LENGTH_DELIMITED, output);
    output->WriteVarint32(_rep_s32_cached_byte_size_);
  }
  for (int i = 0; i < this->rep_s32_size(); i++) {
    ::google::protobuf::internal::WireFormatLite::WriteSInt32NoTag(
      this->rep_s32(i), output);
  }

  // repeated fixed64 rep_fix64 = 21 [packed = true];
  if (this->rep_fix64_size() > 0) {
    ::google::protobuf::internal::WireFormatLite::WriteTag(21, ::google::protobuf::internal::WireFormatLite::WIRETYPE_LENGTH_DELIMITED, output);
    output->WriteVarint32(_rep_fix64_cached_byte_size_);
  }
  for (int i = 0; i < this->rep_fix64_size(); i++) {
    ::google::protobuf::internal::WireFormatLite::WriteFixed64NoTag(
      this->rep_fix64(i), output);
  }

  // repeated uint64 rep_u64 = 22 [packed = true];
  if (this->rep_u64_size() > 0) {
    ::google::protobuf::internal::WireFormatLite::WriteTag(22, ::google::protobuf::internal::WireFormatLite::WIRETYPE_LENGTH_DELIMITED, output);
    output->WriteVarint32(_rep_u64_cached_byte_size_);
  }
  for (int i = 0; i < this->rep_u64_size(); i++) {
    ::google::protobuf::internal::WireFormatLite::WriteUInt64NoTag(
      this->rep_u64(i), output);
  }

  // repeated int64 rep_i64 = 23 [packed = true];
  if (this->rep_i64_size() > 0) {
    ::google::protobuf::internal::WireFormatLite::WriteTag(23, ::google::protobuf::internal::WireFormatLite::WIRETYPE_LENGTH_DELIMITED, output);
    output->WriteVarint32(_rep_i64_cached_byte_size_);
  }
  for (int i = 0; i < this->rep_i64_size(); i++) {
    ::google::protobuf::internal::WireFormatLite::WriteInt64NoTag(
      this->rep_i64(i), output);
  }

  // repeated sfixed64 rep_sf64 = 24 [packed = true];
  if (this->rep_sf64_size() > 0) {
    ::google::protobuf::internal::WireFormatLite::WriteTag(24, ::google::protobuf::internal::WireFormatLite::WIRETYPE_LENGTH_DELIMITED, output);
    output->WriteVarint32(_rep_sf64_cached_byte_size_);
  }
  for (int i = 0; i < this->rep_sf64_size(); i++) {
    ::google::protobuf::internal::WireFormatLite::WriteSFixed64NoTag(
      this->rep_sf64(i), output);
  }

  // repeated sint64 rep_s64 = 25 [packed = true];
  if (this->rep_s64_size() > 0) {
    ::google::protobuf::internal::WireFormatLite::WriteTag(25, ::google::protobuf::internal::WireFormatLite::WIRETYPE_LENGTH_DELIMITED, output);
    output->WriteVarint32(_rep_s64_cached_byte_size_);
  }
  for (int i = 0; i < this->rep_s64_size(); i++) {
    ::google::protobuf::internal::WireFormatLite::WriteSInt64NoTag(
      this->rep_s64(i), output);
  }

  // repeated float rep_float = 28 [packed = true];
  if (this->rep_float_size() > 0) {
    ::google::protobuf::internal::WireFormatLite::WriteTag(28, ::google::protobuf::internal::WireFormatLite::WIRETYPE_LENGTH_DELIMITED, output);
    output->WriteVarint32(_rep_float_cached_byte_size_);
  }
  for (int i = 0; i < this->rep_float_size(); i++) {
    ::google::protobuf::internal::WireFormatLite::WriteFloatNoTag(
      this->rep_float(i), output);
  }

  // repeated double rep_double = 29 [packed = true];
  if (this->rep_double_size() > 0) {
    ::google::protobuf::internal::WireFormatLite::WriteTag(29, ::google::protobuf::internal::WireFormatLite::WIRETYPE_LENGTH_DELIMITED, output);
    output->WriteVarint32(_rep_double_cached_byte_size_);
  }
  for (int i = 0; i < this->rep_double_size(); i++) {
    ::google::protobuf::internal::WireFormatLite::WriteDoubleNoTag(
      this->rep_double(i), output);
  }

  // repeated bool rep_bool = 30 [packed = true];
  if (this->rep_bool_size() > 0) {
    ::google::protobuf::internal::WireFormatLite::WriteTag(30, ::google::protobuf::internal::WireFormatLite::WIRETYPE_LENGTH_DELIMITED, output);
    output->WriteVarint32(_rep_bool_cached_byte_size_);
  }
  for (int i = 0; i < this->rep_bool_size(); i++) {
    ::google::protobuf::internal::WireFormatLite::WriteBoolNoTag(
      this->rep_bool(i), output);
  }

  if (_internal_metadata_.have_unknown_fields()) {
    ::google::protobuf::internal::WireFormat::SerializeUnknownFields(
        unknown_fields(), output);
  }
  // @@protoc_insertion_point(serialize_end:google.protobuf.testing.PackedPrimitive)
}

::google::protobuf::uint8* PackedPrimitive::InternalSerializeWithCachedSizesToArray(
    bool deterministic, ::google::protobuf::uint8* target) const {
  (void)deterministic; // Unused
  // @@protoc_insertion_point(serialize_to_array_start:google.protobuf.testing.PackedPrimitive)
  // repeated fixed32 rep_fix32 = 16 [packed = true];
  if (this->rep_fix32_size() > 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteTagToArray(
      16,
      ::google::protobuf::internal::WireFormatLite::WIRETYPE_LENGTH_DELIMITED,
      target);
    target = ::google::protobuf::io::CodedOutputStream::WriteVarint32ToArray(
      _rep_fix32_cached_byte_size_, target);
  }
  for (int i = 0; i < this->rep_fix32_size(); i++) {
    target = ::google::protobuf::internal::WireFormatLite::
      WriteFixed32NoTagToArray(this->rep_fix32(i), target);
  }

  // repeated uint32 rep_u32 = 17 [packed = true];
  if (this->rep_u32_size() > 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteTagToArray(
      17,
      ::google::protobuf::internal::WireFormatLite::WIRETYPE_LENGTH_DELIMITED,
      target);
    target = ::google::protobuf::io::CodedOutputStream::WriteVarint32ToArray(
      _rep_u32_cached_byte_size_, target);
  }
  for (int i = 0; i < this->rep_u32_size(); i++) {
    target = ::google::protobuf::internal::WireFormatLite::
      WriteUInt32NoTagToArray(this->rep_u32(i), target);
  }

  // repeated int32 rep_i32 = 18 [packed = true];
  if (this->rep_i32_size() > 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteTagToArray(
      18,
      ::google::protobuf::internal::WireFormatLite::WIRETYPE_LENGTH_DELIMITED,
      target);
    target = ::google::protobuf::io::CodedOutputStream::WriteVarint32ToArray(
      _rep_i32_cached_byte_size_, target);
  }
  for (int i = 0; i < this->rep_i32_size(); i++) {
    target = ::google::protobuf::internal::WireFormatLite::
      WriteInt32NoTagToArray(this->rep_i32(i), target);
  }

  // repeated sfixed32 rep_sf32 = 19 [packed = true];
  if (this->rep_sf32_size() > 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteTagToArray(
      19,
      ::google::protobuf::internal::WireFormatLite::WIRETYPE_LENGTH_DELIMITED,
      target);
    target = ::google::protobuf::io::CodedOutputStream::WriteVarint32ToArray(
      _rep_sf32_cached_byte_size_, target);
  }
  for (int i = 0; i < this->rep_sf32_size(); i++) {
    target = ::google::protobuf::internal::WireFormatLite::
      WriteSFixed32NoTagToArray(this->rep_sf32(i), target);
  }

  // repeated sint32 rep_s32 = 20 [packed = true];
  if (this->rep_s32_size() > 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteTagToArray(
      20,
      ::google::protobuf::internal::WireFormatLite::WIRETYPE_LENGTH_DELIMITED,
      target);
    target = ::google::protobuf::io::CodedOutputStream::WriteVarint32ToArray(
      _rep_s32_cached_byte_size_, target);
  }
  for (int i = 0; i < this->rep_s32_size(); i++) {
    target = ::google::protobuf::internal::WireFormatLite::
      WriteSInt32NoTagToArray(this->rep_s32(i), target);
  }

  // repeated fixed64 rep_fix64 = 21 [packed = true];
  if (this->rep_fix64_size() > 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteTagToArray(
      21,
      ::google::protobuf::internal::WireFormatLite::WIRETYPE_LENGTH_DELIMITED,
      target);
    target = ::google::protobuf::io::CodedOutputStream::WriteVarint32ToArray(
      _rep_fix64_cached_byte_size_, target);
  }
  for (int i = 0; i < this->rep_fix64_size(); i++) {
    target = ::google::protobuf::internal::WireFormatLite::
      WriteFixed64NoTagToArray(this->rep_fix64(i), target);
  }

  // repeated uint64 rep_u64 = 22 [packed = true];
  if (this->rep_u64_size() > 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteTagToArray(
      22,
      ::google::protobuf::internal::WireFormatLite::WIRETYPE_LENGTH_DELIMITED,
      target);
    target = ::google::protobuf::io::CodedOutputStream::WriteVarint32ToArray(
      _rep_u64_cached_byte_size_, target);
  }
  for (int i = 0; i < this->rep_u64_size(); i++) {
    target = ::google::protobuf::internal::WireFormatLite::
      WriteUInt64NoTagToArray(this->rep_u64(i), target);
  }

  // repeated int64 rep_i64 = 23 [packed = true];
  if (this->rep_i64_size() > 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteTagToArray(
      23,
      ::google::protobuf::internal::WireFormatLite::WIRETYPE_LENGTH_DELIMITED,
      target);
    target = ::google::protobuf::io::CodedOutputStream::WriteVarint32ToArray(
      _rep_i64_cached_byte_size_, target);
  }
  for (int i = 0; i < this->rep_i64_size(); i++) {
    target = ::google::protobuf::internal::WireFormatLite::
      WriteInt64NoTagToArray(this->rep_i64(i), target);
  }

  // repeated sfixed64 rep_sf64 = 24 [packed = true];
  if (this->rep_sf64_size() > 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteTagToArray(
      24,
      ::google::protobuf::internal::WireFormatLite::WIRETYPE_LENGTH_DELIMITED,
      target);
    target = ::google::protobuf::io::CodedOutputStream::WriteVarint32ToArray(
      _rep_sf64_cached_byte_size_, target);
  }
  for (int i = 0; i < this->rep_sf64_size(); i++) {
    target = ::google::protobuf::internal::WireFormatLite::
      WriteSFixed64NoTagToArray(this->rep_sf64(i), target);
  }

  // repeated sint64 rep_s64 = 25 [packed = true];
  if (this->rep_s64_size() > 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteTagToArray(
      25,
      ::google::protobuf::internal::WireFormatLite::WIRETYPE_LENGTH_DELIMITED,
      target);
    target = ::google::protobuf::io::CodedOutputStream::WriteVarint32ToArray(
      _rep_s64_cached_byte_size_, target);
  }
  for (int i = 0; i < this->rep_s64_size(); i++) {
    target = ::google::protobuf::internal::WireFormatLite::
      WriteSInt64NoTagToArray(this->rep_s64(i), target);
  }

  // repeated float rep_float = 28 [packed = true];
  if (this->rep_float_size() > 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteTagToArray(
      28,
      ::google::protobuf::internal::WireFormatLite::WIRETYPE_LENGTH_DELIMITED,
      target);
    target = ::google::protobuf::io::CodedOutputStream::WriteVarint32ToArray(
      _rep_float_cached_byte_size_, target);
  }
  for (int i = 0; i < this->rep_float_size(); i++) {
    target = ::google::protobuf::internal::WireFormatLite::
      WriteFloatNoTagToArray(this->rep_float(i), target);
  }

  // repeated double rep_double = 29 [packed = true];
  if (this->rep_double_size() > 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteTagToArray(
      29,
      ::google::protobuf::internal::WireFormatLite::WIRETYPE_LENGTH_DELIMITED,
      target);
    target = ::google::protobuf::io::CodedOutputStream::WriteVarint32ToArray(
      _rep_double_cached_byte_size_, target);
  }
  for (int i = 0; i < this->rep_double_size(); i++) {
    target = ::google::protobuf::internal::WireFormatLite::
      WriteDoubleNoTagToArray(this->rep_double(i), target);
  }

  // repeated bool rep_bool = 30 [packed = true];
  if (this->rep_bool_size() > 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteTagToArray(
      30,
      ::google::protobuf::internal::WireFormatLite::WIRETYPE_LENGTH_DELIMITED,
      target);
    target = ::google::protobuf::io::CodedOutputStream::WriteVarint32ToArray(
      _rep_bool_cached_byte_size_, target);
  }
  for (int i = 0; i < this->rep_bool_size(); i++) {
    target = ::google::protobuf::internal::WireFormatLite::
      WriteBoolNoTagToArray(this->rep_bool(i), target);
  }

  if (_internal_metadata_.have_unknown_fields()) {
    target = ::google::protobuf::internal::WireFormat::SerializeUnknownFieldsToArray(
        unknown_fields(), target);
  }
  // @@protoc_insertion_point(serialize_to_array_end:google.protobuf.testing.PackedPrimitive)
  return target;
}

size_t PackedPrimitive::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:google.protobuf.testing.PackedPrimitive)
  size_t total_size = 0;

  // repeated fixed32 rep_fix32 = 16 [packed = true];
  {
    size_t data_size = 0;
    unsigned int count = this->rep_fix32_size();
    data_size = 4UL * count;
    if (data_size > 0) {
      total_size += 2 +
        ::google::protobuf::internal::WireFormatLite::Int32Size(data_size);
    }
    int cached_size = ::google::protobuf::internal::ToCachedSize(data_size);
    GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
    _rep_fix32_cached_byte_size_ = cached_size;
    GOOGLE_SAFE_CONCURRENT_WRITES_END();
    total_size += data_size;
  }

  // repeated uint32 rep_u32 = 17 [packed = true];
  {
    size_t data_size = 0;
    unsigned int count = this->rep_u32_size();
    for (unsigned int i = 0; i < count; i++) {
      data_size += ::google::protobuf::internal::WireFormatLite::
        UInt32Size(this->rep_u32(i));
    }
    if (data_size > 0) {
      total_size += 2 +
        ::google::protobuf::internal::WireFormatLite::Int32Size(data_size);
    }
    int cached_size = ::google::protobuf::internal::ToCachedSize(data_size);
    GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
    _rep_u32_cached_byte_size_ = cached_size;
    GOOGLE_SAFE_CONCURRENT_WRITES_END();
    total_size += data_size;
  }

  // repeated int32 rep_i32 = 18 [packed = true];
  {
    size_t data_size = 0;
    unsigned int count = this->rep_i32_size();
    for (unsigned int i = 0; i < count; i++) {
      data_size += ::google::protobuf::internal::WireFormatLite::
        Int32Size(this->rep_i32(i));
    }
    if (data_size > 0) {
      total_size += 2 +
        ::google::protobuf::internal::WireFormatLite::Int32Size(data_size);
    }
    int cached_size = ::google::protobuf::internal::ToCachedSize(data_size);
    GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
    _rep_i32_cached_byte_size_ = cached_size;
    GOOGLE_SAFE_CONCURRENT_WRITES_END();
    total_size += data_size;
  }

  // repeated sfixed32 rep_sf32 = 19 [packed = true];
  {
    size_t data_size = 0;
    unsigned int count = this->rep_sf32_size();
    data_size = 4UL * count;
    if (data_size > 0) {
      total_size += 2 +
        ::google::protobuf::internal::WireFormatLite::Int32Size(data_size);
    }
    int cached_size = ::google::protobuf::internal::ToCachedSize(data_size);
    GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
    _rep_sf32_cached_byte_size_ = cached_size;
    GOOGLE_SAFE_CONCURRENT_WRITES_END();
    total_size += data_size;
  }

  // repeated sint32 rep_s32 = 20 [packed = true];
  {
    size_t data_size = 0;
    unsigned int count = this->rep_s32_size();
    for (unsigned int i = 0; i < count; i++) {
      data_size += ::google::protobuf::internal::WireFormatLite::
        SInt32Size(this->rep_s32(i));
    }
    if (data_size > 0) {
      total_size += 2 +
        ::google::protobuf::internal::WireFormatLite::Int32Size(data_size);
    }
    int cached_size = ::google::protobuf::internal::ToCachedSize(data_size);
    GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
    _rep_s32_cached_byte_size_ = cached_size;
    GOOGLE_SAFE_CONCURRENT_WRITES_END();
    total_size += data_size;
  }

  // repeated fixed64 rep_fix64 = 21 [packed = true];
  {
    size_t data_size = 0;
    unsigned int count = this->rep_fix64_size();
    data_size = 8UL * count;
    if (data_size > 0) {
      total_size += 2 +
        ::google::protobuf::internal::WireFormatLite::Int32Size(data_size);
    }
    int cached_size = ::google::protobuf::internal::ToCachedSize(data_size);
    GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
    _rep_fix64_cached_byte_size_ = cached_size;
    GOOGLE_SAFE_CONCURRENT_WRITES_END();
    total_size += data_size;
  }

  // repeated uint64 rep_u64 = 22 [packed = true];
  {
    size_t data_size = 0;
    unsigned int count = this->rep_u64_size();
    for (unsigned int i = 0; i < count; i++) {
      data_size += ::google::protobuf::internal::WireFormatLite::
        UInt64Size(this->rep_u64(i));
    }
    if (data_size > 0) {
      total_size += 2 +
        ::google::protobuf::internal::WireFormatLite::Int32Size(data_size);
    }
    int cached_size = ::google::protobuf::internal::ToCachedSize(data_size);
    GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
    _rep_u64_cached_byte_size_ = cached_size;
    GOOGLE_SAFE_CONCURRENT_WRITES_END();
    total_size += data_size;
  }

  // repeated int64 rep_i64 = 23 [packed = true];
  {
    size_t data_size = 0;
    unsigned int count = this->rep_i64_size();
    for (unsigned int i = 0; i < count; i++) {
      data_size += ::google::protobuf::internal::WireFormatLite::
        Int64Size(this->rep_i64(i));
    }
    if (data_size > 0) {
      total_size += 2 +
        ::google::protobuf::internal::WireFormatLite::Int32Size(data_size);
    }
    int cached_size = ::google::protobuf::internal::ToCachedSize(data_size);
    GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
    _rep_i64_cached_byte_size_ = cached_size;
    GOOGLE_SAFE_CONCURRENT_WRITES_END();
    total_size += data_size;
  }

  // repeated sfixed64 rep_sf64 = 24 [packed = true];
  {
    size_t data_size = 0;
    unsigned int count = this->rep_sf64_size();
    data_size = 8UL * count;
    if (data_size > 0) {
      total_size += 2 +
        ::google::protobuf::internal::WireFormatLite::Int32Size(data_size);
    }
    int cached_size = ::google::protobuf::internal::ToCachedSize(data_size);
    GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
    _rep_sf64_cached_byte_size_ = cached_size;
    GOOGLE_SAFE_CONCURRENT_WRITES_END();
    total_size += data_size;
  }

  // repeated sint64 rep_s64 = 25 [packed = true];
  {
    size_t data_size = 0;
    unsigned int count = this->rep_s64_size();
    for (unsigned int i = 0; i < count; i++) {
      data_size += ::google::protobuf::internal::WireFormatLite::
        SInt64Size(this->rep_s64(i));
    }
    if (data_size > 0) {
      total_size += 2 +
        ::google::protobuf::internal::WireFormatLite::Int32Size(data_size);
    }
    int cached_size = ::google::protobuf::internal::ToCachedSize(data_size);
    GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
    _rep_s64_cached_byte_size_ = cached_size;
    GOOGLE_SAFE_CONCURRENT_WRITES_END();
    total_size += data_size;
  }

  // repeated float rep_float = 28 [packed = true];
  {
    size_t data_size = 0;
    unsigned int count = this->rep_float_size();
    data_size = 4UL * count;
    if (data_size > 0) {
      total_size += 2 +
        ::google::protobuf::internal::WireFormatLite::Int32Size(data_size);
    }
    int cached_size = ::google::protobuf::internal::ToCachedSize(data_size);
    GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
    _rep_float_cached_byte_size_ = cached_size;
    GOOGLE_SAFE_CONCURRENT_WRITES_END();
    total_size += data_size;
  }

  // repeated double rep_double = 29 [packed = true];
  {
    size_t data_size = 0;
    unsigned int count = this->rep_double_size();
    data_size = 8UL * count;
    if (data_size > 0) {
      total_size += 2 +
        ::google::protobuf::internal::WireFormatLite::Int32Size(data_size);
    }
    int cached_size = ::google::protobuf::internal::ToCachedSize(data_size);
    GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
    _rep_double_cached_byte_size_ = cached_size;
    GOOGLE_SAFE_CONCURRENT_WRITES_END();
    total_size += data_size;
  }

  // repeated bool rep_bool = 30 [packed = true];
  {
    size_t data_size = 0;
    unsigned int count = this->rep_bool_size();
    data_size = 1UL * count;
    if (data_size > 0) {
      total_size += 2 +
        ::google::protobuf::internal::WireFormatLite::Int32Size(data_size);
    }
    int cached_size = ::google::protobuf::internal::ToCachedSize(data_size);
    GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
    _rep_bool_cached_byte_size_ = cached_size;
    GOOGLE_SAFE_CONCURRENT_WRITES_END();
    total_size += data_size;
  }

  if (_internal_metadata_.have_unknown_fields()) {
    total_size +=
      ::google::protobuf::internal::WireFormat::ComputeUnknownFieldsSize(
        unknown_fields());
  }
  int cached_size = ::google::protobuf::internal::ToCachedSize(total_size);
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = cached_size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
  return total_size;
}

void PackedPrimitive::MergeFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_merge_from_start:google.protobuf.testing.PackedPrimitive)
  if (GOOGLE_PREDICT_FALSE(&from == this)) MergeFromFail(__LINE__);
  const PackedPrimitive* source =
      ::google::protobuf::internal::DynamicCastToGenerated<const PackedPrimitive>(
          &from);
  if (source == NULL) {
  // @@protoc_insertion_point(generalized_merge_from_cast_fail:google.protobuf.testing.PackedPrimitive)
    ::google::protobuf::internal::ReflectionOps::Merge(from, this);
  } else {
  // @@protoc_insertion_point(generalized_merge_from_cast_success:google.protobuf.testing.PackedPrimitive)
    UnsafeMergeFrom(*source);
  }
}

void PackedPrimitive::MergeFrom(const PackedPrimitive& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:google.protobuf.testing.PackedPrimitive)
  if (GOOGLE_PREDICT_TRUE(&from != this)) {
    UnsafeMergeFrom(from);
  } else {
    MergeFromFail(__LINE__);
  }
}

void PackedPrimitive::UnsafeMergeFrom(const PackedPrimitive& from) {
  GOOGLE_DCHECK(&from != this);
  rep_fix32_.UnsafeMergeFrom(from.rep_fix32_);
  rep_u32_.UnsafeMergeFrom(from.rep_u32_);
  rep_i32_.UnsafeMergeFrom(from.rep_i32_);
  rep_sf32_.UnsafeMergeFrom(from.rep_sf32_);
  rep_s32_.UnsafeMergeFrom(from.rep_s32_);
  rep_fix64_.UnsafeMergeFrom(from.rep_fix64_);
  rep_u64_.UnsafeMergeFrom(from.rep_u64_);
  rep_i64_.UnsafeMergeFrom(from.rep_i64_);
  rep_sf64_.UnsafeMergeFrom(from.rep_sf64_);
  rep_s64_.UnsafeMergeFrom(from.rep_s64_);
  rep_float_.UnsafeMergeFrom(from.rep_float_);
  rep_double_.UnsafeMergeFrom(from.rep_double_);
  rep_bool_.UnsafeMergeFrom(from.rep_bool_);
  if (from._internal_metadata_.have_unknown_fields()) {
    ::google::protobuf::UnknownFieldSet::MergeToInternalMetdata(
      from.unknown_fields(), &_internal_metadata_);
  }
}

void PackedPrimitive::CopyFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_copy_from_start:google.protobuf.testing.PackedPrimitive)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

void PackedPrimitive::CopyFrom(const PackedPrimitive& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:google.protobuf.testing.PackedPrimitive)
  if (&from == this) return;
  Clear();
  UnsafeMergeFrom(from);
}

bool PackedPrimitive::IsInitialized() const {

  return true;
}

void PackedPrimitive::Swap(PackedPrimitive* other) {
  if (other == this) return;
  InternalSwap(other);
}
void PackedPrimitive::InternalSwap(PackedPrimitive* other) {
  rep_fix32_.UnsafeArenaSwap(&other->rep_fix32_);
  rep_u32_.UnsafeArenaSwap(&other->rep_u32_);
  rep_i32_.UnsafeArenaSwap(&other->rep_i32_);
  rep_sf32_.UnsafeArenaSwap(&other->rep_sf32_);
  rep_s32_.UnsafeArenaSwap(&other->rep_s32_);
  rep_fix64_.UnsafeArenaSwap(&other->rep_fix64_);
  rep_u64_.UnsafeArenaSwap(&other->rep_u64_);
  rep_i64_.UnsafeArenaSwap(&other->rep_i64_);
  rep_sf64_.UnsafeArenaSwap(&other->rep_sf64_);
  rep_s64_.UnsafeArenaSwap(&other->rep_s64_);
  rep_float_.UnsafeArenaSwap(&other->rep_float_);
  rep_double_.UnsafeArenaSwap(&other->rep_double_);
  rep_bool_.UnsafeArenaSwap(&other->rep_bool_);
  std::swap(_has_bits_[0], other->_has_bits_[0]);
  _internal_metadata_.Swap(&other->_internal_metadata_);
  std::swap(_cached_size_, other->_cached_size_);
}

::google::protobuf::Metadata PackedPrimitive::GetMetadata() const {
  protobuf_AssignDescriptorsOnce();
  ::google::protobuf::Metadata metadata;
  metadata.descriptor = PackedPrimitive_descriptor_;
  metadata.reflection = PackedPrimitive_reflection_;
  return metadata;
}

#if PROTOBUF_INLINE_NOT_IN_HEADERS
// PackedPrimitive

// repeated fixed32 rep_fix32 = 16 [packed = true];
int PackedPrimitive::rep_fix32_size() const {
  return rep_fix32_.size();
}
void PackedPrimitive::clear_rep_fix32() {
  rep_fix32_.Clear();
}
::google::protobuf::uint32 PackedPrimitive::rep_fix32(int index) const {
  // @@protoc_insertion_point(field_get:google.protobuf.testing.PackedPrimitive.rep_fix32)
  return rep_fix32_.Get(index);
}
void PackedPrimitive::set_rep_fix32(int index, ::google::protobuf::uint32 value) {
  rep_fix32_.Set(index, value);
  // @@protoc_insertion_point(field_set:google.protobuf.testing.PackedPrimitive.rep_fix32)
}
void PackedPrimitive::add_rep_fix32(::google::protobuf::uint32 value) {
  rep_fix32_.Add(value);
  // @@protoc_insertion_point(field_add:google.protobuf.testing.PackedPrimitive.rep_fix32)
}
const ::google::protobuf::RepeatedField< ::google::protobuf::uint32 >&
PackedPrimitive::rep_fix32() const {
  // @@protoc_insertion_point(field_list:google.protobuf.testing.PackedPrimitive.rep_fix32)
  return rep_fix32_;
}
::google::protobuf::RepeatedField< ::google::protobuf::uint32 >*
PackedPrimitive::mutable_rep_fix32() {
  // @@protoc_insertion_point(field_mutable_list:google.protobuf.testing.PackedPrimitive.rep_fix32)
  return &rep_fix32_;
}

// repeated uint32 rep_u32 = 17 [packed = true];
int PackedPrimitive::rep_u32_size() const {
  return rep_u32_.size();
}
void PackedPrimitive::clear_rep_u32() {
  rep_u32_.Clear();
}
::google::protobuf::uint32 PackedPrimitive::rep_u32(int index) const {
  // @@protoc_insertion_point(field_get:google.protobuf.testing.PackedPrimitive.rep_u32)
  return rep_u32_.Get(index);
}
void PackedPrimitive::set_rep_u32(int index, ::google::protobuf::uint32 value) {
  rep_u32_.Set(index, value);
  // @@protoc_insertion_point(field_set:google.protobuf.testing.PackedPrimitive.rep_u32)
}
void PackedPrimitive::add_rep_u32(::google::protobuf::uint32 value) {
  rep_u32_.Add(value);
  // @@protoc_insertion_point(field_add:google.protobuf.testing.PackedPrimitive.rep_u32)
}
const ::google::protobuf::RepeatedField< ::google::protobuf::uint32 >&
PackedPrimitive::rep_u32() const {
  // @@protoc_insertion_point(field_list:google.protobuf.testing.PackedPrimitive.rep_u32)
  return rep_u32_;
}
::google::protobuf::RepeatedField< ::google::protobuf::uint32 >*
PackedPrimitive::mutable_rep_u32() {
  // @@protoc_insertion_point(field_mutable_list:google.protobuf.testing.PackedPrimitive.rep_u32)
  return &rep_u32_;
}

// repeated int32 rep_i32 = 18 [packed = true];
int PackedPrimitive::rep_i32_size() const {
  return rep_i32_.size();
}
void PackedPrimitive::clear_rep_i32() {
  rep_i32_.Clear();
}
::google::protobuf::int32 PackedPrimitive::rep_i32(int index) const {
  // @@protoc_insertion_point(field_get:google.protobuf.testing.PackedPrimitive.rep_i32)
  return rep_i32_.Get(index);
}
void PackedPrimitive::set_rep_i32(int index, ::google::protobuf::int32 value) {
  rep_i32_.Set(index, value);
  // @@protoc_insertion_point(field_set:google.protobuf.testing.PackedPrimitive.rep_i32)
}
void PackedPrimitive::add_rep_i32(::google::protobuf::int32 value) {
  rep_i32_.Add(value);
  // @@protoc_insertion_point(field_add:google.protobuf.testing.PackedPrimitive.rep_i32)
}
const ::google::protobuf::RepeatedField< ::google::protobuf::int32 >&
PackedPrimitive::rep_i32() const {
  // @@protoc_insertion_point(field_list:google.protobuf.testing.PackedPrimitive.rep_i32)
  return rep_i32_;
}
::google::protobuf::RepeatedField< ::google::protobuf::int32 >*
PackedPrimitive::mutable_rep_i32() {
  // @@protoc_insertion_point(field_mutable_list:google.protobuf.testing.PackedPrimitive.rep_i32)
  return &rep_i32_;
}

// repeated sfixed32 rep_sf32 = 19 [packed = true];
int PackedPrimitive::rep_sf32_size() const {
  return rep_sf32_.size();
}
void PackedPrimitive::clear_rep_sf32() {
  rep_sf32_.Clear();
}
::google::protobuf::int32 PackedPrimitive::rep_sf32(int index) const {
  // @@protoc_insertion_point(field_get:google.protobuf.testing.PackedPrimitive.rep_sf32)
  return rep_sf32_.Get(index);
}
void PackedPrimitive::set_rep_sf32(int index, ::google::protobuf::int32 value) {
  rep_sf32_.Set(index, value);
  // @@protoc_insertion_point(field_set:google.protobuf.testing.PackedPrimitive.rep_sf32)
}
void PackedPrimitive::add_rep_sf32(::google::protobuf::int32 value) {
  rep_sf32_.Add(value);
  // @@protoc_insertion_point(field_add:google.protobuf.testing.PackedPrimitive.rep_sf32)
}
const ::google::protobuf::RepeatedField< ::google::protobuf::int32 >&
PackedPrimitive::rep_sf32() const {
  // @@protoc_insertion_point(field_list:google.protobuf.testing.PackedPrimitive.rep_sf32)
  return rep_sf32_;
}
::google::protobuf::RepeatedField< ::google::protobuf::int32 >*
PackedPrimitive::mutable_rep_sf32() {
  // @@protoc_insertion_point(field_mutable_list:google.protobuf.testing.PackedPrimitive.rep_sf32)
  return &rep_sf32_;
}

// repeated sint32 rep_s32 = 20 [packed = true];
int PackedPrimitive::rep_s32_size() const {
  return rep_s32_.size();
}
void PackedPrimitive::clear_rep_s32() {
  rep_s32_.Clear();
}
::google::protobuf::int32 PackedPrimitive::rep_s32(int index) const {
  // @@protoc_insertion_point(field_get:google.protobuf.testing.PackedPrimitive.rep_s32)
  return rep_s32_.Get(index);
}
void PackedPrimitive::set_rep_s32(int index, ::google::protobuf::int32 value) {
  rep_s32_.Set(index, value);
  // @@protoc_insertion_point(field_set:google.protobuf.testing.PackedPrimitive.rep_s32)
}
void PackedPrimitive::add_rep_s32(::google::protobuf::int32 value) {
  rep_s32_.Add(value);
  // @@protoc_insertion_point(field_add:google.protobuf.testing.PackedPrimitive.rep_s32)
}
const ::google::protobuf::RepeatedField< ::google::protobuf::int32 >&
PackedPrimitive::rep_s32() const {
  // @@protoc_insertion_point(field_list:google.protobuf.testing.PackedPrimitive.rep_s32)
  return rep_s32_;
}
::google::protobuf::RepeatedField< ::google::protobuf::int32 >*
PackedPrimitive::mutable_rep_s32() {
  // @@protoc_insertion_point(field_mutable_list:google.protobuf.testing.PackedPrimitive.rep_s32)
  return &rep_s32_;
}

// repeated fixed64 rep_fix64 = 21 [packed = true];
int PackedPrimitive::rep_fix64_size() const {
  return rep_fix64_.size();
}
void PackedPrimitive::clear_rep_fix64() {
  rep_fix64_.Clear();
}
::google::protobuf::uint64 PackedPrimitive::rep_fix64(int index) const {
  // @@protoc_insertion_point(field_get:google.protobuf.testing.PackedPrimitive.rep_fix64)
  return rep_fix64_.Get(index);
}
void PackedPrimitive::set_rep_fix64(int index, ::google::protobuf::uint64 value) {
  rep_fix64_.Set(index, value);
  // @@protoc_insertion_point(field_set:google.protobuf.testing.PackedPrimitive.rep_fix64)
}
void PackedPrimitive::add_rep_fix64(::google::protobuf::uint64 value) {
  rep_fix64_.Add(value);
  // @@protoc_insertion_point(field_add:google.protobuf.testing.PackedPrimitive.rep_fix64)
}
const ::google::protobuf::RepeatedField< ::google::protobuf::uint64 >&
PackedPrimitive::rep_fix64() const {
  // @@protoc_insertion_point(field_list:google.protobuf.testing.PackedPrimitive.rep_fix64)
  return rep_fix64_;
}
::google::protobuf::RepeatedField< ::google::protobuf::uint64 >*
PackedPrimitive::mutable_rep_fix64() {
  // @@protoc_insertion_point(field_mutable_list:google.protobuf.testing.PackedPrimitive.rep_fix64)
  return &rep_fix64_;
}

// repeated uint64 rep_u64 = 22 [packed = true];
int PackedPrimitive::rep_u64_size() const {
  return rep_u64_.size();
}
void PackedPrimitive::clear_rep_u64() {
  rep_u64_.Clear();
}
::google::protobuf::uint64 PackedPrimitive::rep_u64(int index) const {
  // @@protoc_insertion_point(field_get:google.protobuf.testing.PackedPrimitive.rep_u64)
  return rep_u64_.Get(index);
}
void PackedPrimitive::set_rep_u64(int index, ::google::protobuf::uint64 value) {
  rep_u64_.Set(index, value);
  // @@protoc_insertion_point(field_set:google.protobuf.testing.PackedPrimitive.rep_u64)
}
void PackedPrimitive::add_rep_u64(::google::protobuf::uint64 value) {
  rep_u64_.Add(value);
  // @@protoc_insertion_point(field_add:google.protobuf.testing.PackedPrimitive.rep_u64)
}
const ::google::protobuf::RepeatedField< ::google::protobuf::uint64 >&
PackedPrimitive::rep_u64() const {
  // @@protoc_insertion_point(field_list:google.protobuf.testing.PackedPrimitive.rep_u64)
  return rep_u64_;
}
::google::protobuf::RepeatedField< ::google::protobuf::uint64 >*
PackedPrimitive::mutable_rep_u64() {
  // @@protoc_insertion_point(field_mutable_list:google.protobuf.testing.PackedPrimitive.rep_u64)
  return &rep_u64_;
}

// repeated int64 rep_i64 = 23 [packed = true];
int PackedPrimitive::rep_i64_size() const {
  return rep_i64_.size();
}
void PackedPrimitive::clear_rep_i64() {
  rep_i64_.Clear();
}
::google::protobuf::int64 PackedPrimitive::rep_i64(int index) const {
  // @@protoc_insertion_point(field_get:google.protobuf.testing.PackedPrimitive.rep_i64)
  return rep_i64_.Get(index);
}
void PackedPrimitive::set_rep_i64(int index, ::google::protobuf::int64 value) {
  rep_i64_.Set(index, value);
  // @@protoc_insertion_point(field_set:google.protobuf.testing.PackedPrimitive.rep_i64)
}
void PackedPrimitive::add_rep_i64(::google::protobuf::int64 value) {
  rep_i64_.Add(value);
  // @@protoc_insertion_point(field_add:google.protobuf.testing.PackedPrimitive.rep_i64)
}
const ::google::protobuf::RepeatedField< ::google::protobuf::int64 >&
PackedPrimitive::rep_i64() const {
  // @@protoc_insertion_point(field_list:google.protobuf.testing.PackedPrimitive.rep_i64)
  return rep_i64_;
}
::google::protobuf::RepeatedField< ::google::protobuf::int64 >*
PackedPrimitive::mutable_rep_i64() {
  // @@protoc_insertion_point(field_mutable_list:google.protobuf.testing.PackedPrimitive.rep_i64)
  return &rep_i64_;
}

// repeated sfixed64 rep_sf64 = 24 [packed = true];
int PackedPrimitive::rep_sf64_size() const {
  return rep_sf64_.size();
}
void PackedPrimitive::clear_rep_sf64() {
  rep_sf64_.Clear();
}
::google::protobuf::int64 PackedPrimitive::rep_sf64(int index) const {
  // @@protoc_insertion_point(field_get:google.protobuf.testing.PackedPrimitive.rep_sf64)
  return rep_sf64_.Get(index);
}
void PackedPrimitive::set_rep_sf64(int index, ::google::protobuf::int64 value) {
  rep_sf64_.Set(index, value);
  // @@protoc_insertion_point(field_set:google.protobuf.testing.PackedPrimitive.rep_sf64)
}
void PackedPrimitive::add_rep_sf64(::google::protobuf::int64 value) {
  rep_sf64_.Add(value);
  // @@protoc_insertion_point(field_add:google.protobuf.testing.PackedPrimitive.rep_sf64)
}
const ::google::protobuf::RepeatedField< ::google::protobuf::int64 >&
PackedPrimitive::rep_sf64() const {
  // @@protoc_insertion_point(field_list:google.protobuf.testing.PackedPrimitive.rep_sf64)
  return rep_sf64_;
}
::google::protobuf::RepeatedField< ::google::protobuf::int64 >*
PackedPrimitive::mutable_rep_sf64() {
  // @@protoc_insertion_point(field_mutable_list:google.protobuf.testing.PackedPrimitive.rep_sf64)
  return &rep_sf64_;
}

// repeated sint64 rep_s64 = 25 [packed = true];
int PackedPrimitive::rep_s64_size() const {
  return rep_s64_.size();
}
void PackedPrimitive::clear_rep_s64() {
  rep_s64_.Clear();
}
::google::protobuf::int64 PackedPrimitive::rep_s64(int index) const {
  // @@protoc_insertion_point(field_get:google.protobuf.testing.PackedPrimitive.rep_s64)
  return rep_s64_.Get(index);
}
void PackedPrimitive::set_rep_s64(int index, ::google::protobuf::int64 value) {
  rep_s64_.Set(index, value);
  // @@protoc_insertion_point(field_set:google.protobuf.testing.PackedPrimitive.rep_s64)
}
void PackedPrimitive::add_rep_s64(::google::protobuf::int64 value) {
  rep_s64_.Add(value);
  // @@protoc_insertion_point(field_add:google.protobuf.testing.PackedPrimitive.rep_s64)
}
const ::google::protobuf::RepeatedField< ::google::protobuf::int64 >&
PackedPrimitive::rep_s64() const {
  // @@protoc_insertion_point(field_list:google.protobuf.testing.PackedPrimitive.rep_s64)
  return rep_s64_;
}
::google::protobuf::RepeatedField< ::google::protobuf::int64 >*
PackedPrimitive::mutable_rep_s64() {
  // @@protoc_insertion_point(field_mutable_list:google.protobuf.testing.PackedPrimitive.rep_s64)
  return &rep_s64_;
}

// repeated float rep_float = 28 [packed = true];
int PackedPrimitive::rep_float_size() const {
  return rep_float_.size();
}
void PackedPrimitive::clear_rep_float() {
  rep_float_.Clear();
}
float PackedPrimitive::rep_float(int index) const {
  // @@protoc_insertion_point(field_get:google.protobuf.testing.PackedPrimitive.rep_float)
  return rep_float_.Get(index);
}
void PackedPrimitive::set_rep_float(int index, float value) {
  rep_float_.Set(index, value);
  // @@protoc_insertion_point(field_set:google.protobuf.testing.PackedPrimitive.rep_float)
}
void PackedPrimitive::add_rep_float(float value) {
  rep_float_.Add(value);
  // @@protoc_insertion_point(field_add:google.protobuf.testing.PackedPrimitive.rep_float)
}
const ::google::protobuf::RepeatedField< float >&
PackedPrimitive::rep_float() const {
  // @@protoc_insertion_point(field_list:google.protobuf.testing.PackedPrimitive.rep_float)
  return rep_float_;
}
::google::protobuf::RepeatedField< float >*
PackedPrimitive::mutable_rep_float() {
  // @@protoc_insertion_point(field_mutable_list:google.protobuf.testing.PackedPrimitive.rep_float)
  return &rep_float_;
}

// repeated double rep_double = 29 [packed = true];
int PackedPrimitive::rep_double_size() const {
  return rep_double_.size();
}
void PackedPrimitive::clear_rep_double() {
  rep_double_.Clear();
}
double PackedPrimitive::rep_double(int index) const {
  // @@protoc_insertion_point(field_get:google.protobuf.testing.PackedPrimitive.rep_double)
  return rep_double_.Get(index);
}
void PackedPrimitive::set_rep_double(int index, double value) {
  rep_double_.Set(index, value);
  // @@protoc_insertion_point(field_set:google.protobuf.testing.PackedPrimitive.rep_double)
}
void PackedPrimitive::add_rep_double(double value) {
  rep_double_.Add(value);
  // @@protoc_insertion_point(field_add:google.protobuf.testing.PackedPrimitive.rep_double)
}
const ::google::protobuf::RepeatedField< double >&
PackedPrimitive::rep_double() const {
  // @@protoc_insertion_point(field_list:google.protobuf.testing.PackedPrimitive.rep_double)
  return rep_double_;
}
::google::protobuf::RepeatedField< double >*
PackedPrimitive::mutable_rep_double() {
  // @@protoc_insertion_point(field_mutable_list:google.protobuf.testing.PackedPrimitive.rep_double)
  return &rep_double_;
}

// repeated bool rep_bool = 30 [packed = true];
int PackedPrimitive::rep_bool_size() const {
  return rep_bool_.size();
}
void PackedPrimitive::clear_rep_bool() {
  rep_bool_.Clear();
}
bool PackedPrimitive::rep_bool(int index) const {
  // @@protoc_insertion_point(field_get:google.protobuf.testing.PackedPrimitive.rep_bool)
  return rep_bool_.Get(index);
}
void PackedPrimitive::set_rep_bool(int index, bool value) {
  rep_bool_.Set(index, value);
  // @@protoc_insertion_point(field_set:google.protobuf.testing.PackedPrimitive.rep_bool)
}
void PackedPrimitive::add_rep_bool(bool value) {
  rep_bool_.Add(value);
  // @@protoc_insertion_point(field_add:google.protobuf.testing.PackedPrimitive.rep_bool)
}
const ::google::protobuf::RepeatedField< bool >&
PackedPrimitive::rep_bool() const {
  // @@protoc_insertion_point(field_list:google.protobuf.testing.PackedPrimitive.rep_bool)
  return rep_bool_;
}
::google::protobuf::RepeatedField< bool >*
PackedPrimitive::mutable_rep_bool() {
  // @@protoc_insertion_point(field_mutable_list:google.protobuf.testing.PackedPrimitive.rep_bool)
  return &rep_bool_;
}

inline const PackedPrimitive* PackedPrimitive::internal_default_instance() {
  return &PackedPrimitive_default_instance_.get();
}
#endif  // PROTOBUF_INLINE_NOT_IN_HEADERS

// ===================================================================

#if !defined(_MSC_VER) || _MSC_VER >= 1900
const int NestedBook::kBookFieldNumber;
#endif  // !defined(_MSC_VER) || _MSC_VER >= 1900

#if !defined(_MSC_VER) || _MSC_VER >= 1900
const int NestedBook::kAnotherBookFieldNumber;
#endif
::google::protobuf::internal::ExtensionIdentifier< ::google::protobuf::testing::Book,
    ::google::protobuf::internal::MessageTypeTraits< ::google::protobuf::testing::NestedBook >, 11, false >
  NestedBook::another_book(kAnotherBookFieldNumber, *::google::protobuf::testing::NestedBook::internal_default_instance());
NestedBook::NestedBook()
  : ::google::protobuf::Message(), _internal_metadata_(NULL) {
  if (this != internal_default_instance()) protobuf_InitDefaults_google_2fprotobuf_2futil_2finternal_2ftestdata_2fbooks_2eproto();
  SharedCtor();
  // @@protoc_insertion_point(constructor:google.protobuf.testing.NestedBook)
}

void NestedBook::InitAsDefaultInstance() {
  book_ = const_cast< ::google::protobuf::testing::Book*>(
      ::google::protobuf::testing::Book::internal_default_instance());
}

NestedBook::NestedBook(const NestedBook& from)
  : ::google::protobuf::Message(),
    _internal_metadata_(NULL) {
  SharedCtor();
  UnsafeMergeFrom(from);
  // @@protoc_insertion_point(copy_constructor:google.protobuf.testing.NestedBook)
}

void NestedBook::SharedCtor() {
  _cached_size_ = 0;
  book_ = NULL;
}

NestedBook::~NestedBook() {
  // @@protoc_insertion_point(destructor:google.protobuf.testing.NestedBook)
  SharedDtor();
}

void NestedBook::SharedDtor() {
  if (this != &NestedBook_default_instance_.get()) {
    delete book_;
  }
}

void NestedBook::SetCachedSize(int size) const {
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
}
const ::google::protobuf::Descriptor* NestedBook::descriptor() {
  protobuf_AssignDescriptorsOnce();
  return NestedBook_descriptor_;
}

const NestedBook& NestedBook::default_instance() {
  protobuf_InitDefaults_google_2fprotobuf_2futil_2finternal_2ftestdata_2fbooks_2eproto();
  return *internal_default_instance();
}

::google::protobuf::internal::ExplicitlyConstructed<NestedBook> NestedBook_default_instance_;

NestedBook* NestedBook::New(::google::protobuf::Arena* arena) const {
  NestedBook* n = new NestedBook;
  if (arena != NULL) {
    arena->Own(n);
  }
  return n;
}

void NestedBook::Clear() {
// @@protoc_insertion_point(message_clear_start:google.protobuf.testing.NestedBook)
  if (has_book()) {
    if (book_ != NULL) book_->::google::protobuf::testing::Book::Clear();
  }
  _has_bits_.Clear();
  if (_internal_metadata_.have_unknown_fields()) {
    mutable_unknown_fields()->Clear();
  }
}

bool NestedBook::MergePartialFromCodedStream(
    ::google::protobuf::io::CodedInputStream* input) {
#define DO_(EXPRESSION) if (!GOOGLE_PREDICT_TRUE(EXPRESSION)) goto failure
  ::google::protobuf::uint32 tag;
  // @@protoc_insertion_point(parse_start:google.protobuf.testing.NestedBook)
  for (;;) {
    ::std::pair< ::google::protobuf::uint32, bool> p = input->ReadTagWithCutoff(127);
    tag = p.first;
    if (!p.second) goto handle_unusual;
    switch (::google::protobuf::internal::WireFormatLite::GetTagFieldNumber(tag)) {
      // optional .google.protobuf.testing.Book book = 1;
      case 1: {
        if (tag == 10) {
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessageNoVirtual(
               input, mutable_book()));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectAtEnd()) goto success;
        break;
      }

      default: {
      handle_unusual:
        if (tag == 0 ||
            ::google::protobuf::internal::WireFormatLite::GetTagWireType(tag) ==
            ::google::protobuf::internal::WireFormatLite::WIRETYPE_END_GROUP) {
          goto success;
        }
        DO_(::google::protobuf::internal::WireFormat::SkipField(
              input, tag, mutable_unknown_fields()));
        break;
      }
    }
  }
success:
  // @@protoc_insertion_point(parse_success:google.protobuf.testing.NestedBook)
  return true;
failure:
  // @@protoc_insertion_point(parse_failure:google.protobuf.testing.NestedBook)
  return false;
#undef DO_
}

void NestedBook::SerializeWithCachedSizes(
    ::google::protobuf::io::CodedOutputStream* output) const {
  // @@protoc_insertion_point(serialize_start:google.protobuf.testing.NestedBook)
  // optional .google.protobuf.testing.Book book = 1;
  if (has_book()) {
    ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
      1, *this->book_, output);
  }

  if (_internal_metadata_.have_unknown_fields()) {
    ::google::protobuf::internal::WireFormat::SerializeUnknownFields(
        unknown_fields(), output);
  }
  // @@protoc_insertion_point(serialize_end:google.protobuf.testing.NestedBook)
}

::google::protobuf::uint8* NestedBook::InternalSerializeWithCachedSizesToArray(
    bool deterministic, ::google::protobuf::uint8* target) const {
  (void)deterministic; // Unused
  // @@protoc_insertion_point(serialize_to_array_start:google.protobuf.testing.NestedBook)
  // optional .google.protobuf.testing.Book book = 1;
  if (has_book()) {
    target = ::google::protobuf::internal::WireFormatLite::
      InternalWriteMessageNoVirtualToArray(
        1, *this->book_, false, target);
  }

  if (_internal_metadata_.have_unknown_fields()) {
    target = ::google::protobuf::internal::WireFormat::SerializeUnknownFieldsToArray(
        unknown_fields(), target);
  }
  // @@protoc_insertion_point(serialize_to_array_end:google.protobuf.testing.NestedBook)
  return target;
}

size_t NestedBook::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:google.protobuf.testing.NestedBook)
  size_t total_size = 0;

  // optional .google.protobuf.testing.Book book = 1;
  if (has_book()) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::MessageSizeNoVirtual(
        *this->book_);
  }

  if (_internal_metadata_.have_unknown_fields()) {
    total_size +=
      ::google::protobuf::internal::WireFormat::ComputeUnknownFieldsSize(
        unknown_fields());
  }
  int cached_size = ::google::protobuf::internal::ToCachedSize(total_size);
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = cached_size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
  return total_size;
}

void NestedBook::MergeFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_merge_from_start:google.protobuf.testing.NestedBook)
  if (GOOGLE_PREDICT_FALSE(&from == this)) MergeFromFail(__LINE__);
  const NestedBook* source =
      ::google::protobuf::internal::DynamicCastToGenerated<const NestedBook>(
          &from);
  if (source == NULL) {
  // @@protoc_insertion_point(generalized_merge_from_cast_fail:google.protobuf.testing.NestedBook)
    ::google::protobuf::internal::ReflectionOps::Merge(from, this);
  } else {
  // @@protoc_insertion_point(generalized_merge_from_cast_success:google.protobuf.testing.NestedBook)
    UnsafeMergeFrom(*source);
  }
}

void NestedBook::MergeFrom(const NestedBook& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:google.protobuf.testing.NestedBook)
  if (GOOGLE_PREDICT_TRUE(&from != this)) {
    UnsafeMergeFrom(from);
  } else {
    MergeFromFail(__LINE__);
  }
}

void NestedBook::UnsafeMergeFrom(const NestedBook& from) {
  GOOGLE_DCHECK(&from != this);
  if (from._has_bits_[0 / 32] & (0xffu << (0 % 32))) {
    if (from.has_book()) {
      mutable_book()->::google::protobuf::testing::Book::MergeFrom(from.book());
    }
  }
  if (from._internal_metadata_.have_unknown_fields()) {
    ::google::protobuf::UnknownFieldSet::MergeToInternalMetdata(
      from.unknown_fields(), &_internal_metadata_);
  }
}

void NestedBook::CopyFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_copy_from_start:google.protobuf.testing.NestedBook)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

void NestedBook::CopyFrom(const NestedBook& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:google.protobuf.testing.NestedBook)
  if (&from == this) return;
  Clear();
  UnsafeMergeFrom(from);
}

bool NestedBook::IsInitialized() const {

  if (has_book()) {
    if (!this->book_->IsInitialized()) return false;
  }
  return true;
}

void NestedBook::Swap(NestedBook* other) {
  if (other == this) return;
  InternalSwap(other);
}
void NestedBook::InternalSwap(NestedBook* other) {
  std::swap(book_, other->book_);
  std::swap(_has_bits_[0], other->_has_bits_[0]);
  _internal_metadata_.Swap(&other->_internal_metadata_);
  std::swap(_cached_size_, other->_cached_size_);
}

::google::protobuf::Metadata NestedBook::GetMetadata() const {
  protobuf_AssignDescriptorsOnce();
  ::google::protobuf::Metadata metadata;
  metadata.descriptor = NestedBook_descriptor_;
  metadata.reflection = NestedBook_reflection_;
  return metadata;
}

#if PROTOBUF_INLINE_NOT_IN_HEADERS
// NestedBook

// optional .google.protobuf.testing.Book book = 1;
bool NestedBook::has_book() const {
  return (_has_bits_[0] & 0x00000001u) != 0;
}
void NestedBook::set_has_book() {
  _has_bits_[0] |= 0x00000001u;
}
void NestedBook::clear_has_book() {
  _has_bits_[0] &= ~0x00000001u;
}
void NestedBook::clear_book() {
  if (book_ != NULL) book_->::google::protobuf::testing::Book::Clear();
  clear_has_book();
}
const ::google::protobuf::testing::Book& NestedBook::book() const {
  // @@protoc_insertion_point(field_get:google.protobuf.testing.NestedBook.book)
  return book_ != NULL ? *book_
                         : *::google::protobuf::testing::Book::internal_default_instance();
}
::google::protobuf::testing::Book* NestedBook::mutable_book() {
  set_has_book();
  if (book_ == NULL) {
    book_ = new ::google::protobuf::testing::Book;
  }
  // @@protoc_insertion_point(field_mutable:google.protobuf.testing.NestedBook.book)
  return book_;
}
::google::protobuf::testing::Book* NestedBook::release_book() {
  // @@protoc_insertion_point(field_release:google.protobuf.testing.NestedBook.book)
  clear_has_book();
  ::google::protobuf::testing::Book* temp = book_;
  book_ = NULL;
  return temp;
}
void NestedBook::set_allocated_book(::google::protobuf::testing::Book* book) {
  delete book_;
  book_ = book;
  if (book) {
    set_has_book();
  } else {
    clear_has_book();
  }
  // @@protoc_insertion_point(field_set_allocated:google.protobuf.testing.NestedBook.book)
}

inline const NestedBook* NestedBook::internal_default_instance() {
  return &NestedBook_default_instance_.get();
}
#endif  // PROTOBUF_INLINE_NOT_IN_HEADERS

// ===================================================================

#if !defined(_MSC_VER) || _MSC_VER >= 1900
const int BadNestedBook::kBookFieldNumber;
#endif  // !defined(_MSC_VER) || _MSC_VER >= 1900

BadNestedBook::BadNestedBook()
  : ::google::protobuf::Message(), _internal_metadata_(NULL) {
  if (this != internal_default_instance()) protobuf_InitDefaults_google_2fprotobuf_2futil_2finternal_2ftestdata_2fbooks_2eproto();
  SharedCtor();
  // @@protoc_insertion_point(constructor:google.protobuf.testing.BadNestedBook)
}

void BadNestedBook::InitAsDefaultInstance() {
}

BadNestedBook::BadNestedBook(const BadNestedBook& from)
  : ::google::protobuf::Message(),
    _internal_metadata_(NULL) {
  SharedCtor();
  UnsafeMergeFrom(from);
  // @@protoc_insertion_point(copy_constructor:google.protobuf.testing.BadNestedBook)
}

void BadNestedBook::SharedCtor() {
  _cached_size_ = 0;
}

BadNestedBook::~BadNestedBook() {
  // @@protoc_insertion_point(destructor:google.protobuf.testing.BadNestedBook)
  SharedDtor();
}

void BadNestedBook::SharedDtor() {
}

void BadNestedBook::SetCachedSize(int size) const {
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
}
const ::google::protobuf::Descriptor* BadNestedBook::descriptor() {
  protobuf_AssignDescriptorsOnce();
  return BadNestedBook_descriptor_;
}

const BadNestedBook& BadNestedBook::default_instance() {
  protobuf_InitDefaults_google_2fprotobuf_2futil_2finternal_2ftestdata_2fbooks_2eproto();
  return *internal_default_instance();
}

::google::protobuf::internal::ExplicitlyConstructed<BadNestedBook> BadNestedBook_default_instance_;

BadNestedBook* BadNestedBook::New(::google::protobuf::Arena* arena) const {
  BadNestedBook* n = new BadNestedBook;
  if (arena != NULL) {
    arena->Own(n);
  }
  return n;
}

void BadNestedBook::Clear() {
// @@protoc_insertion_point(message_clear_start:google.protobuf.testing.BadNestedBook)
  book_.Clear();
  _has_bits_.Clear();
  if (_internal_metadata_.have_unknown_fields()) {
    mutable_unknown_fields()->Clear();
  }
}

bool BadNestedBook::MergePartialFromCodedStream(
    ::google::protobuf::io::CodedInputStream* input) {
#define DO_(EXPRESSION) if (!GOOGLE_PREDICT_TRUE(EXPRESSION)) goto failure
  ::google::protobuf::uint32 tag;
  // @@protoc_insertion_point(parse_start:google.protobuf.testing.BadNestedBook)
  for (;;) {
    ::std::pair< ::google::protobuf::uint32, bool> p = input->ReadTagWithCutoff(127);
    tag = p.first;
    if (!p.second) goto handle_unusual;
    switch (::google::protobuf::internal::WireFormatLite::GetTagFieldNumber(tag)) {
      // repeated uint32 book = 1 [packed = true];
      case 1: {
        if (tag == 10) {
          DO_((::google::protobuf::internal::WireFormatLite::ReadPackedPrimitive<
                   ::google::protobuf::uint32, ::google::protobuf::internal::WireFormatLite::TYPE_UINT32>(
                 input, this->mutable_book())));
        } else if (tag == 8) {
          DO_((::google::protobuf::internal::WireFormatLite::ReadRepeatedPrimitiveNoInline<
                   ::google::protobuf::uint32, ::google::protobuf::internal::WireFormatLite::TYPE_UINT32>(
                 1, 10, input, this->mutable_book())));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectAtEnd()) goto success;
        break;
      }

      default: {
      handle_unusual:
        if (tag == 0 ||
            ::google::protobuf::internal::WireFormatLite::GetTagWireType(tag) ==
            ::google::protobuf::internal::WireFormatLite::WIRETYPE_END_GROUP) {
          goto success;
        }
        DO_(::google::protobuf::internal::WireFormat::SkipField(
              input, tag, mutable_unknown_fields()));
        break;
      }
    }
  }
success:
  // @@protoc_insertion_point(parse_success:google.protobuf.testing.BadNestedBook)
  return true;
failure:
  // @@protoc_insertion_point(parse_failure:google.protobuf.testing.BadNestedBook)
  return false;
#undef DO_
}

void BadNestedBook::SerializeWithCachedSizes(
    ::google::protobuf::io::CodedOutputStream* output) const {
  // @@protoc_insertion_point(serialize_start:google.protobuf.testing.BadNestedBook)
  // repeated uint32 book = 1 [packed = true];
  if (this->book_size() > 0) {
    ::google::protobuf::internal::WireFormatLite::WriteTag(1, ::google::protobuf::internal::WireFormatLite::WIRETYPE_LENGTH_DELIMITED, output);
    output->WriteVarint32(_book_cached_byte_size_);
  }
  for (int i = 0; i < this->book_size(); i++) {
    ::google::protobuf::internal::WireFormatLite::WriteUInt32NoTag(
      this->book(i), output);
  }

  if (_internal_metadata_.have_unknown_fields()) {
    ::google::protobuf::internal::WireFormat::SerializeUnknownFields(
        unknown_fields(), output);
  }
  // @@protoc_insertion_point(serialize_end:google.protobuf.testing.BadNestedBook)
}

::google::protobuf::uint8* BadNestedBook::InternalSerializeWithCachedSizesToArray(
    bool deterministic, ::google::protobuf::uint8* target) const {
  (void)deterministic; // Unused
  // @@protoc_insertion_point(serialize_to_array_start:google.protobuf.testing.BadNestedBook)
  // repeated uint32 book = 1 [packed = true];
  if (this->book_size() > 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteTagToArray(
      1,
      ::google::protobuf::internal::WireFormatLite::WIRETYPE_LENGTH_DELIMITED,
      target);
    target = ::google::protobuf::io::CodedOutputStream::WriteVarint32ToArray(
      _book_cached_byte_size_, target);
  }
  for (int i = 0; i < this->book_size(); i++) {
    target = ::google::protobuf::internal::WireFormatLite::
      WriteUInt32NoTagToArray(this->book(i), target);
  }

  if (_internal_metadata_.have_unknown_fields()) {
    target = ::google::protobuf::internal::WireFormat::SerializeUnknownFieldsToArray(
        unknown_fields(), target);
  }
  // @@protoc_insertion_point(serialize_to_array_end:google.protobuf.testing.BadNestedBook)
  return target;
}

size_t BadNestedBook::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:google.protobuf.testing.BadNestedBook)
  size_t total_size = 0;

  // repeated uint32 book = 1 [packed = true];
  {
    size_t data_size = 0;
    unsigned int count = this->book_size();
    for (unsigned int i = 0; i < count; i++) {
      data_size += ::google::protobuf::internal::WireFormatLite::
        UInt32Size(this->book(i));
    }
    if (data_size > 0) {
      total_size += 1 +
        ::google::protobuf::internal::WireFormatLite::Int32Size(data_size);
    }
    int cached_size = ::google::protobuf::internal::ToCachedSize(data_size);
    GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
    _book_cached_byte_size_ = cached_size;
    GOOGLE_SAFE_CONCURRENT_WRITES_END();
    total_size += data_size;
  }

  if (_internal_metadata_.have_unknown_fields()) {
    total_size +=
      ::google::protobuf::internal::WireFormat::ComputeUnknownFieldsSize(
        unknown_fields());
  }
  int cached_size = ::google::protobuf::internal::ToCachedSize(total_size);
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = cached_size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
  return total_size;
}

void BadNestedBook::MergeFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_merge_from_start:google.protobuf.testing.BadNestedBook)
  if (GOOGLE_PREDICT_FALSE(&from == this)) MergeFromFail(__LINE__);
  const BadNestedBook* source =
      ::google::protobuf::internal::DynamicCastToGenerated<const BadNestedBook>(
          &from);
  if (source == NULL) {
  // @@protoc_insertion_point(generalized_merge_from_cast_fail:google.protobuf.testing.BadNestedBook)
    ::google::protobuf::internal::ReflectionOps::Merge(from, this);
  } else {
  // @@protoc_insertion_point(generalized_merge_from_cast_success:google.protobuf.testing.BadNestedBook)
    UnsafeMergeFrom(*source);
  }
}

void BadNestedBook::MergeFrom(const BadNestedBook& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:google.protobuf.testing.BadNestedBook)
  if (GOOGLE_PREDICT_TRUE(&from != this)) {
    UnsafeMergeFrom(from);
  } else {
    MergeFromFail(__LINE__);
  }
}

void BadNestedBook::UnsafeMergeFrom(const BadNestedBook& from) {
  GOOGLE_DCHECK(&from != this);
  book_.UnsafeMergeFrom(from.book_);
  if (from._internal_metadata_.have_unknown_fields()) {
    ::google::protobuf::UnknownFieldSet::MergeToInternalMetdata(
      from.unknown_fields(), &_internal_metadata_);
  }
}

void BadNestedBook::CopyFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_copy_from_start:google.protobuf.testing.BadNestedBook)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

void BadNestedBook::CopyFrom(const BadNestedBook& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:google.protobuf.testing.BadNestedBook)
  if (&from == this) return;
  Clear();
  UnsafeMergeFrom(from);
}

bool BadNestedBook::IsInitialized() const {

  return true;
}

void BadNestedBook::Swap(BadNestedBook* other) {
  if (other == this) return;
  InternalSwap(other);
}
void BadNestedBook::InternalSwap(BadNestedBook* other) {
  book_.UnsafeArenaSwap(&other->book_);
  std::swap(_has_bits_[0], other->_has_bits_[0]);
  _internal_metadata_.Swap(&other->_internal_metadata_);
  std::swap(_cached_size_, other->_cached_size_);
}

::google::protobuf::Metadata BadNestedBook::GetMetadata() const {
  protobuf_AssignDescriptorsOnce();
  ::google::protobuf::Metadata metadata;
  metadata.descriptor = BadNestedBook_descriptor_;
  metadata.reflection = BadNestedBook_reflection_;
  return metadata;
}

#if PROTOBUF_INLINE_NOT_IN_HEADERS
// BadNestedBook

// repeated uint32 book = 1 [packed = true];
int BadNestedBook::book_size() const {
  return book_.size();
}
void BadNestedBook::clear_book() {
  book_.Clear();
}
::google::protobuf::uint32 BadNestedBook::book(int index) const {
  // @@protoc_insertion_point(field_get:google.protobuf.testing.BadNestedBook.book)
  return book_.Get(index);
}
void BadNestedBook::set_book(int index, ::google::protobuf::uint32 value) {
  book_.Set(index, value);
  // @@protoc_insertion_point(field_set:google.protobuf.testing.BadNestedBook.book)
}
void BadNestedBook::add_book(::google::protobuf::uint32 value) {
  book_.Add(value);
  // @@protoc_insertion_point(field_add:google.protobuf.testing.BadNestedBook.book)
}
const ::google::protobuf::RepeatedField< ::google::protobuf::uint32 >&
BadNestedBook::book() const {
  // @@protoc_insertion_point(field_list:google.protobuf.testing.BadNestedBook.book)
  return book_;
}
::google::protobuf::RepeatedField< ::google::protobuf::uint32 >*
BadNestedBook::mutable_book() {
  // @@protoc_insertion_point(field_mutable_list:google.protobuf.testing.BadNestedBook.book)
  return &book_;
}

inline const BadNestedBook* BadNestedBook::internal_default_instance() {
  return &BadNestedBook_default_instance_.get();
}
#endif  // PROTOBUF_INLINE_NOT_IN_HEADERS

// ===================================================================

#if !defined(_MSC_VER) || _MSC_VER >= 1900
const int Cyclic::kMIntFieldNumber;
const int Cyclic::kMStrFieldNumber;
const int Cyclic::kMBookFieldNumber;
const int Cyclic::kMAuthorFieldNumber;
const int Cyclic::kMCyclicFieldNumber;
#endif  // !defined(_MSC_VER) || _MSC_VER >= 1900

Cyclic::Cyclic()
  : ::google::protobuf::Message(), _internal_metadata_(NULL) {
  if (this != internal_default_instance()) protobuf_InitDefaults_google_2fprotobuf_2futil_2finternal_2ftestdata_2fbooks_2eproto();
  SharedCtor();
  // @@protoc_insertion_point(constructor:google.protobuf.testing.Cyclic)
}

void Cyclic::InitAsDefaultInstance() {
  m_book_ = const_cast< ::google::protobuf::testing::Book*>(
      ::google::protobuf::testing::Book::internal_default_instance());
  m_cyclic_ = const_cast< ::google::protobuf::testing::Cyclic*>(
      ::google::protobuf::testing::Cyclic::internal_default_instance());
}

Cyclic::Cyclic(const Cyclic& from)
  : ::google::protobuf::Message(),
    _internal_metadata_(NULL) {
  SharedCtor();
  UnsafeMergeFrom(from);
  // @@protoc_insertion_point(copy_constructor:google.protobuf.testing.Cyclic)
}

void Cyclic::SharedCtor() {
  _cached_size_ = 0;
  m_str_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  m_book_ = NULL;
  m_cyclic_ = NULL;
  m_int_ = 0;
}

Cyclic::~Cyclic() {
  // @@protoc_insertion_point(destructor:google.protobuf.testing.Cyclic)
  SharedDtor();
}

void Cyclic::SharedDtor() {
  m_str_.DestroyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  if (this != &Cyclic_default_instance_.get()) {
    delete m_book_;
    delete m_cyclic_;
  }
}

void Cyclic::SetCachedSize(int size) const {
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
}
const ::google::protobuf::Descriptor* Cyclic::descriptor() {
  protobuf_AssignDescriptorsOnce();
  return Cyclic_descriptor_;
}

const Cyclic& Cyclic::default_instance() {
  protobuf_InitDefaults_google_2fprotobuf_2futil_2finternal_2ftestdata_2fbooks_2eproto();
  return *internal_default_instance();
}

::google::protobuf::internal::ExplicitlyConstructed<Cyclic> Cyclic_default_instance_;

Cyclic* Cyclic::New(::google::protobuf::Arena* arena) const {
  Cyclic* n = new Cyclic;
  if (arena != NULL) {
    arena->Own(n);
  }
  return n;
}

void Cyclic::Clear() {
// @@protoc_insertion_point(message_clear_start:google.protobuf.testing.Cyclic)
  if (_has_bits_[0 / 32] & 23u) {
    m_int_ = 0;
    if (has_m_str()) {
      m_str_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
    }
    if (has_m_book()) {
      if (m_book_ != NULL) m_book_->::google::protobuf::testing::Book::Clear();
    }
    if (has_m_cyclic()) {
      if (m_cyclic_ != NULL) m_cyclic_->::google::protobuf::testing::Cyclic::Clear();
    }
  }
  m_author_.Clear();
  _has_bits_.Clear();
  if (_internal_metadata_.have_unknown_fields()) {
    mutable_unknown_fields()->Clear();
  }
}

bool Cyclic::MergePartialFromCodedStream(
    ::google::protobuf::io::CodedInputStream* input) {
#define DO_(EXPRESSION) if (!GOOGLE_PREDICT_TRUE(EXPRESSION)) goto failure
  ::google::protobuf::uint32 tag;
  // @@protoc_insertion_point(parse_start:google.protobuf.testing.Cyclic)
  for (;;) {
    ::std::pair< ::google::protobuf::uint32, bool> p = input->ReadTagWithCutoff(127);
    tag = p.first;
    if (!p.second) goto handle_unusual;
    switch (::google::protobuf::internal::WireFormatLite::GetTagFieldNumber(tag)) {
      // optional int32 m_int = 1;
      case 1: {
        if (tag == 8) {
          set_has_m_int();
          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int32, ::google::protobuf::internal::WireFormatLite::TYPE_INT32>(
                 input, &m_int_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(18)) goto parse_m_str;
        break;
      }

      // optional string m_str = 2;
      case 2: {
        if (tag == 18) {
         parse_m_str:
          DO_(::google::protobuf::internal::WireFormatLite::ReadString(
                input, this->mutable_m_str()));
          ::google::protobuf::internal::WireFormat::VerifyUTF8StringNamedField(
            this->m_str().data(), this->m_str().length(),
            ::google::protobuf::internal::WireFormat::PARSE,
            "google.protobuf.testing.Cyclic.m_str");
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(26)) goto parse_m_book;
        break;
      }

      // optional .google.protobuf.testing.Book m_book = 3;
      case 3: {
        if (tag == 26) {
         parse_m_book:
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessageNoVirtual(
               input, mutable_m_book()));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(34)) goto parse_m_cyclic;
        break;
      }

      // optional .google.protobuf.testing.Cyclic m_cyclic = 4;
      case 4: {
        if (tag == 34) {
         parse_m_cyclic:
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessageNoVirtual(
               input, mutable_m_cyclic()));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(42)) goto parse_m_author;
        break;
      }

      // repeated .google.protobuf.testing.Author m_author = 5;
      case 5: {
        if (tag == 42) {
         parse_m_author:
          DO_(input->IncrementRecursionDepth());
         parse_loop_m_author:
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessageNoVirtualNoRecursionDepth(
                input, add_m_author()));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(42)) goto parse_loop_m_author;
        input->UnsafeDecrementRecursionDepth();
        if (input->ExpectAtEnd()) goto success;
        break;
      }

      default: {
      handle_unusual:
        if (tag == 0 ||
            ::google::protobuf::internal::WireFormatLite::GetTagWireType(tag) ==
            ::google::protobuf::internal::WireFormatLite::WIRETYPE_END_GROUP) {
          goto success;
        }
        DO_(::google::protobuf::internal::WireFormat::SkipField(
              input, tag, mutable_unknown_fields()));
        break;
      }
    }
  }
success:
  // @@protoc_insertion_point(parse_success:google.protobuf.testing.Cyclic)
  return true;
failure:
  // @@protoc_insertion_point(parse_failure:google.protobuf.testing.Cyclic)
  return false;
#undef DO_
}

void Cyclic::SerializeWithCachedSizes(
    ::google::protobuf::io::CodedOutputStream* output) const {
  // @@protoc_insertion_point(serialize_start:google.protobuf.testing.Cyclic)
  // optional int32 m_int = 1;
  if (has_m_int()) {
    ::google::protobuf::internal::WireFormatLite::WriteInt32(1, this->m_int(), output);
  }

  // optional string m_str = 2;
  if (has_m_str()) {
    ::google::protobuf::internal::WireFormat::VerifyUTF8StringNamedField(
      this->m_str().data(), this->m_str().length(),
      ::google::protobuf::internal::WireFormat::SERIALIZE,
      "google.protobuf.testing.Cyclic.m_str");
    ::google::protobuf::internal::WireFormatLite::WriteStringMaybeAliased(
      2, this->m_str(), output);
  }

  // optional .google.protobuf.testing.Book m_book = 3;
  if (has_m_book()) {
    ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
      3, *this->m_book_, output);
  }

  // optional .google.protobuf.testing.Cyclic m_cyclic = 4;
  if (has_m_cyclic()) {
    ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
      4, *this->m_cyclic_, output);
  }

  // repeated .google.protobuf.testing.Author m_author = 5;
  for (unsigned int i = 0, n = this->m_author_size(); i < n; i++) {
    ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
      5, this->m_author(i), output);
  }

  if (_internal_metadata_.have_unknown_fields()) {
    ::google::protobuf::internal::WireFormat::SerializeUnknownFields(
        unknown_fields(), output);
  }
  // @@protoc_insertion_point(serialize_end:google.protobuf.testing.Cyclic)
}

::google::protobuf::uint8* Cyclic::InternalSerializeWithCachedSizesToArray(
    bool deterministic, ::google::protobuf::uint8* target) const {
  (void)deterministic; // Unused
  // @@protoc_insertion_point(serialize_to_array_start:google.protobuf.testing.Cyclic)
  // optional int32 m_int = 1;
  if (has_m_int()) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt32ToArray(1, this->m_int(), target);
  }

  // optional string m_str = 2;
  if (has_m_str()) {
    ::google::protobuf::internal::WireFormat::VerifyUTF8StringNamedField(
      this->m_str().data(), this->m_str().length(),
      ::google::protobuf::internal::WireFormat::SERIALIZE,
      "google.protobuf.testing.Cyclic.m_str");
    target =
      ::google::protobuf::internal::WireFormatLite::WriteStringToArray(
        2, this->m_str(), target);
  }

  // optional .google.protobuf.testing.Book m_book = 3;
  if (has_m_book()) {
    target = ::google::protobuf::internal::WireFormatLite::
      InternalWriteMessageNoVirtualToArray(
        3, *this->m_book_, false, target);
  }

  // optional .google.protobuf.testing.Cyclic m_cyclic = 4;
  if (has_m_cyclic()) {
    target = ::google::protobuf::internal::WireFormatLite::
      InternalWriteMessageNoVirtualToArray(
        4, *this->m_cyclic_, false, target);
  }

  // repeated .google.protobuf.testing.Author m_author = 5;
  for (unsigned int i = 0, n = this->m_author_size(); i < n; i++) {
    target = ::google::protobuf::internal::WireFormatLite::
      InternalWriteMessageNoVirtualToArray(
        5, this->m_author(i), false, target);
  }

  if (_internal_metadata_.have_unknown_fields()) {
    target = ::google::protobuf::internal::WireFormat::SerializeUnknownFieldsToArray(
        unknown_fields(), target);
  }
  // @@protoc_insertion_point(serialize_to_array_end:google.protobuf.testing.Cyclic)
  return target;
}

size_t Cyclic::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:google.protobuf.testing.Cyclic)
  size_t total_size = 0;

  if (_has_bits_[0 / 32] & 23u) {
    // optional int32 m_int = 1;
    if (has_m_int()) {
      total_size += 1 +
        ::google::protobuf::internal::WireFormatLite::Int32Size(
          this->m_int());
    }

    // optional string m_str = 2;
    if (has_m_str()) {
      total_size += 1 +
        ::google::protobuf::internal::WireFormatLite::StringSize(
          this->m_str());
    }

    // optional .google.protobuf.testing.Book m_book = 3;
    if (has_m_book()) {
      total_size += 1 +
        ::google::protobuf::internal::WireFormatLite::MessageSizeNoVirtual(
          *this->m_book_);
    }

    // optional .google.protobuf.testing.Cyclic m_cyclic = 4;
    if (has_m_cyclic()) {
      total_size += 1 +
        ::google::protobuf::internal::WireFormatLite::MessageSizeNoVirtual(
          *this->m_cyclic_);
    }

  }
  // repeated .google.protobuf.testing.Author m_author = 5;
  {
    unsigned int count = this->m_author_size();
    total_size += 1UL * count;
    for (unsigned int i = 0; i < count; i++) {
      total_size +=
        ::google::protobuf::internal::WireFormatLite::MessageSizeNoVirtual(
          this->m_author(i));
    }
  }

  if (_internal_metadata_.have_unknown_fields()) {
    total_size +=
      ::google::protobuf::internal::WireFormat::ComputeUnknownFieldsSize(
        unknown_fields());
  }
  int cached_size = ::google::protobuf::internal::ToCachedSize(total_size);
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = cached_size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
  return total_size;
}

void Cyclic::MergeFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_merge_from_start:google.protobuf.testing.Cyclic)
  if (GOOGLE_PREDICT_FALSE(&from == this)) MergeFromFail(__LINE__);
  const Cyclic* source =
      ::google::protobuf::internal::DynamicCastToGenerated<const Cyclic>(
          &from);
  if (source == NULL) {
  // @@protoc_insertion_point(generalized_merge_from_cast_fail:google.protobuf.testing.Cyclic)
    ::google::protobuf::internal::ReflectionOps::Merge(from, this);
  } else {
  // @@protoc_insertion_point(generalized_merge_from_cast_success:google.protobuf.testing.Cyclic)
    UnsafeMergeFrom(*source);
  }
}

void Cyclic::MergeFrom(const Cyclic& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:google.protobuf.testing.Cyclic)
  if (GOOGLE_PREDICT_TRUE(&from != this)) {
    UnsafeMergeFrom(from);
  } else {
    MergeFromFail(__LINE__);
  }
}

void Cyclic::UnsafeMergeFrom(const Cyclic& from) {
  GOOGLE_DCHECK(&from != this);
  m_author_.MergeFrom(from.m_author_);
  if (from._has_bits_[0 / 32] & (0xffu << (0 % 32))) {
    if (from.has_m_int()) {
      set_m_int(from.m_int());
    }
    if (from.has_m_str()) {
      set_has_m_str();
      m_str_.AssignWithDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), from.m_str_);
    }
    if (from.has_m_book()) {
      mutable_m_book()->::google::protobuf::testing::Book::MergeFrom(from.m_book());
    }
    if (from.has_m_cyclic()) {
      mutable_m_cyclic()->::google::protobuf::testing::Cyclic::MergeFrom(from.m_cyclic());
    }
  }
  if (from._internal_metadata_.have_unknown_fields()) {
    ::google::protobuf::UnknownFieldSet::MergeToInternalMetdata(
      from.unknown_fields(), &_internal_metadata_);
  }
}

void Cyclic::CopyFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_copy_from_start:google.protobuf.testing.Cyclic)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

void Cyclic::CopyFrom(const Cyclic& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:google.protobuf.testing.Cyclic)
  if (&from == this) return;
  Clear();
  UnsafeMergeFrom(from);
}

bool Cyclic::IsInitialized() const {

  if (has_m_book()) {
    if (!this->m_book_->IsInitialized()) return false;
  }
  if (has_m_cyclic()) {
    if (!this->m_cyclic_->IsInitialized()) return false;
  }
  return true;
}

void Cyclic::Swap(Cyclic* other) {
  if (other == this) return;
  InternalSwap(other);
}
void Cyclic::InternalSwap(Cyclic* other) {
  std::swap(m_int_, other->m_int_);
  m_str_.Swap(&other->m_str_);
  std::swap(m_book_, other->m_book_);
  m_author_.UnsafeArenaSwap(&other->m_author_);
  std::swap(m_cyclic_, other->m_cyclic_);
  std::swap(_has_bits_[0], other->_has_bits_[0]);
  _internal_metadata_.Swap(&other->_internal_metadata_);
  std::swap(_cached_size_, other->_cached_size_);
}

::google::protobuf::Metadata Cyclic::GetMetadata() const {
  protobuf_AssignDescriptorsOnce();
  ::google::protobuf::Metadata metadata;
  metadata.descriptor = Cyclic_descriptor_;
  metadata.reflection = Cyclic_reflection_;
  return metadata;
}

#if PROTOBUF_INLINE_NOT_IN_HEADERS
// Cyclic

// optional int32 m_int = 1;
bool Cyclic::has_m_int() const {
  return (_has_bits_[0] & 0x00000001u) != 0;
}
void Cyclic::set_has_m_int() {
  _has_bits_[0] |= 0x00000001u;
}
void Cyclic::clear_has_m_int() {
  _has_bits_[0] &= ~0x00000001u;
}
void Cyclic::clear_m_int() {
  m_int_ = 0;
  clear_has_m_int();
}
::google::protobuf::int32 Cyclic::m_int() const {
  // @@protoc_insertion_point(field_get:google.protobuf.testing.Cyclic.m_int)
  return m_int_;
}
void Cyclic::set_m_int(::google::protobuf::int32 value) {
  set_has_m_int();
  m_int_ = value;
  // @@protoc_insertion_point(field_set:google.protobuf.testing.Cyclic.m_int)
}

// optional string m_str = 2;
bool Cyclic::has_m_str() const {
  return (_has_bits_[0] & 0x00000002u) != 0;
}
void Cyclic::set_has_m_str() {
  _has_bits_[0] |= 0x00000002u;
}
void Cyclic::clear_has_m_str() {
  _has_bits_[0] &= ~0x00000002u;
}
void Cyclic::clear_m_str() {
  m_str_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  clear_has_m_str();
}
const ::std::string& Cyclic::m_str() const {
  // @@protoc_insertion_point(field_get:google.protobuf.testing.Cyclic.m_str)
  return m_str_.GetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
void Cyclic::set_m_str(const ::std::string& value) {
  set_has_m_str();
  m_str_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:google.protobuf.testing.Cyclic.m_str)
}
void Cyclic::set_m_str(const char* value) {
  set_has_m_str();
  m_str_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:google.protobuf.testing.Cyclic.m_str)
}
void Cyclic::set_m_str(const char* value, size_t size) {
  set_has_m_str();
  m_str_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:google.protobuf.testing.Cyclic.m_str)
}
::std::string* Cyclic::mutable_m_str() {
  set_has_m_str();
  // @@protoc_insertion_point(field_mutable:google.protobuf.testing.Cyclic.m_str)
  return m_str_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
::std::string* Cyclic::release_m_str() {
  // @@protoc_insertion_point(field_release:google.protobuf.testing.Cyclic.m_str)
  clear_has_m_str();
  return m_str_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
void Cyclic::set_allocated_m_str(::std::string* m_str) {
  if (m_str != NULL) {
    set_has_m_str();
  } else {
    clear_has_m_str();
  }
  m_str_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), m_str);
  // @@protoc_insertion_point(field_set_allocated:google.protobuf.testing.Cyclic.m_str)
}

// optional .google.protobuf.testing.Book m_book = 3;
bool Cyclic::has_m_book() const {
  return (_has_bits_[0] & 0x00000004u) != 0;
}
void Cyclic::set_has_m_book() {
  _has_bits_[0] |= 0x00000004u;
}
void Cyclic::clear_has_m_book() {
  _has_bits_[0] &= ~0x00000004u;
}
void Cyclic::clear_m_book() {
  if (m_book_ != NULL) m_book_->::google::protobuf::testing::Book::Clear();
  clear_has_m_book();
}
const ::google::protobuf::testing::Book& Cyclic::m_book() const {
  // @@protoc_insertion_point(field_get:google.protobuf.testing.Cyclic.m_book)
  return m_book_ != NULL ? *m_book_
                         : *::google::protobuf::testing::Book::internal_default_instance();
}
::google::protobuf::testing::Book* Cyclic::mutable_m_book() {
  set_has_m_book();
  if (m_book_ == NULL) {
    m_book_ = new ::google::protobuf::testing::Book;
  }
  // @@protoc_insertion_point(field_mutable:google.protobuf.testing.Cyclic.m_book)
  return m_book_;
}
::google::protobuf::testing::Book* Cyclic::release_m_book() {
  // @@protoc_insertion_point(field_release:google.protobuf.testing.Cyclic.m_book)
  clear_has_m_book();
  ::google::protobuf::testing::Book* temp = m_book_;
  m_book_ = NULL;
  return temp;
}
void Cyclic::set_allocated_m_book(::google::protobuf::testing::Book* m_book) {
  delete m_book_;
  m_book_ = m_book;
  if (m_book) {
    set_has_m_book();
  } else {
    clear_has_m_book();
  }
  // @@protoc_insertion_point(field_set_allocated:google.protobuf.testing.Cyclic.m_book)
}

// repeated .google.protobuf.testing.Author m_author = 5;
int Cyclic::m_author_size() const {
  return m_author_.size();
}
void Cyclic::clear_m_author() {
  m_author_.Clear();
}
const ::google::protobuf::testing::Author& Cyclic::m_author(int index) const {
  // @@protoc_insertion_point(field_get:google.protobuf.testing.Cyclic.m_author)
  return m_author_.Get(index);
}
::google::protobuf::testing::Author* Cyclic::mutable_m_author(int index) {
  // @@protoc_insertion_point(field_mutable:google.protobuf.testing.Cyclic.m_author)
  return m_author_.Mutable(index);
}
::google::protobuf::testing::Author* Cyclic::add_m_author() {
  // @@protoc_insertion_point(field_add:google.protobuf.testing.Cyclic.m_author)
  return m_author_.Add();
}
::google::protobuf::RepeatedPtrField< ::google::protobuf::testing::Author >*
Cyclic::mutable_m_author() {
  // @@protoc_insertion_point(field_mutable_list:google.protobuf.testing.Cyclic.m_author)
  return &m_author_;
}
const ::google::protobuf::RepeatedPtrField< ::google::protobuf::testing::Author >&
Cyclic::m_author() const {
  // @@protoc_insertion_point(field_list:google.protobuf.testing.Cyclic.m_author)
  return m_author_;
}

// optional .google.protobuf.testing.Cyclic m_cyclic = 4;
bool Cyclic::has_m_cyclic() const {
  return (_has_bits_[0] & 0x00000010u) != 0;
}
void Cyclic::set_has_m_cyclic() {
  _has_bits_[0] |= 0x00000010u;
}
void Cyclic::clear_has_m_cyclic() {
  _has_bits_[0] &= ~0x00000010u;
}
void Cyclic::clear_m_cyclic() {
  if (m_cyclic_ != NULL) m_cyclic_->::google::protobuf::testing::Cyclic::Clear();
  clear_has_m_cyclic();
}
const ::google::protobuf::testing::Cyclic& Cyclic::m_cyclic() const {
  // @@protoc_insertion_point(field_get:google.protobuf.testing.Cyclic.m_cyclic)
  return m_cyclic_ != NULL ? *m_cyclic_
                         : *::google::protobuf::testing::Cyclic::internal_default_instance();
}
::google::protobuf::testing::Cyclic* Cyclic::mutable_m_cyclic() {
  set_has_m_cyclic();
  if (m_cyclic_ == NULL) {
    m_cyclic_ = new ::google::protobuf::testing::Cyclic;
  }
  // @@protoc_insertion_point(field_mutable:google.protobuf.testing.Cyclic.m_cyclic)
  return m_cyclic_;
}
::google::protobuf::testing::Cyclic* Cyclic::release_m_cyclic() {
  // @@protoc_insertion_point(field_release:google.protobuf.testing.Cyclic.m_cyclic)
  clear_has_m_cyclic();
  ::google::protobuf::testing::Cyclic* temp = m_cyclic_;
  m_cyclic_ = NULL;
  return temp;
}
void Cyclic::set_allocated_m_cyclic(::google::protobuf::testing::Cyclic* m_cyclic) {
  delete m_cyclic_;
  m_cyclic_ = m_cyclic;
  if (m_cyclic) {
    set_has_m_cyclic();
  } else {
    clear_has_m_cyclic();
  }
  // @@protoc_insertion_point(field_set_allocated:google.protobuf.testing.Cyclic.m_cyclic)
}

inline const Cyclic* Cyclic::internal_default_instance() {
  return &Cyclic_default_instance_.get();
}
#endif  // PROTOBUF_INLINE_NOT_IN_HEADERS
::google::protobuf::internal::ExtensionIdentifier< ::google::protobuf::testing::Book,
    ::google::protobuf::internal::RepeatedMessageTypeTraits< ::google::protobuf::testing::Author >, 11, false >
  more_author(kMoreAuthorFieldNumber, *::google::protobuf::testing::Author::internal_default_instance());

// @@protoc_insertion_point(namespace_scope)

}  // namespace testing
}  // namespace protobuf
}  // namespace google

// @@protoc_insertion_point(global_scope)

// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: google/protobuf/util/internal/testdata/field_mask.proto

#define INTERNAL_SUPPRESS_PROTOBUF_FIELD_DEPRECATION
#include <google/protobuf/util/internal/testdata/field_mask.pb.h>

#include <algorithm>

#include <google/protobuf/stubs/common.h>
#include <google/protobuf/stubs/port.h>
#include <google/protobuf/stubs/once.h>
#include <google/protobuf/io/coded_stream.h>
#include <google/protobuf/wire_format_lite_inl.h>
#include <google/protobuf/descriptor.h>
#include <google/protobuf/generated_message_reflection.h>
#include <google/protobuf/reflection_ops.h>
#include <google/protobuf/wire_format.h>
// @@protoc_insertion_point(includes)

namespace google {
namespace protobuf {
namespace testing {

namespace {

const ::google::protobuf::Descriptor* NestedFieldMask_descriptor_ = NULL;
const ::google::protobuf::internal::GeneratedMessageReflection*
  NestedFieldMask_reflection_ = NULL;
const ::google::protobuf::Descriptor* FieldMaskTest_descriptor_ = NULL;
const ::google::protobuf::internal::GeneratedMessageReflection*
  FieldMaskTest_reflection_ = NULL;
const ::google::protobuf::Descriptor* FieldMaskTestCases_descriptor_ = NULL;
const ::google::protobuf::internal::GeneratedMessageReflection*
  FieldMaskTestCases_reflection_ = NULL;
const ::google::protobuf::Descriptor* FieldMaskWrapper_descriptor_ = NULL;
const ::google::protobuf::internal::GeneratedMessageReflection*
  FieldMaskWrapper_reflection_ = NULL;

}  // namespace


void protobuf_AssignDesc_google_2fprotobuf_2futil_2finternal_2ftestdata_2ffield_5fmask_2eproto() GOOGLE_ATTRIBUTE_COLD;
void protobuf_AssignDesc_google_2fprotobuf_2futil_2finternal_2ftestdata_2ffield_5fmask_2eproto() {
  protobuf_AddDesc_google_2fprotobuf_2futil_2finternal_2ftestdata_2ffield_5fmask_2eproto();
  const ::google::protobuf::FileDescriptor* file =
    ::google::protobuf::DescriptorPool::generated_pool()->FindFileByName(
      "google/protobuf/util/internal/testdata/field_mask.proto");
  GOOGLE_CHECK(file != NULL);
  NestedFieldMask_descriptor_ = file->message_type(0);
  static const int NestedFieldMask_offsets_[3] = {
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(NestedFieldMask, data_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(NestedFieldMask, single_mask_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(NestedFieldMask, repeated_mask_),
  };
  NestedFieldMask_reflection_ =
    ::google::protobuf::internal::GeneratedMessageReflection::NewGeneratedMessageReflection(
      NestedFieldMask_descriptor_,
      NestedFieldMask::internal_default_instance(),
      NestedFieldMask_offsets_,
      -1,
      -1,
      -1,
      sizeof(NestedFieldMask),
      GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(NestedFieldMask, _internal_metadata_));
  FieldMaskTest_descriptor_ = file->message_type(1);
  static const int FieldMaskTest_offsets_[4] = {
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(FieldMaskTest, id_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(FieldMaskTest, single_mask_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(FieldMaskTest, repeated_mask_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(FieldMaskTest, nested_mask_),
  };
  FieldMaskTest_reflection_ =
    ::google::protobuf::internal::GeneratedMessageReflection::NewGeneratedMessageReflection(
      FieldMaskTest_descriptor_,
      FieldMaskTest::internal_default_instance(),
      FieldMaskTest_offsets_,
      -1,
      -1,
      -1,
      sizeof(FieldMaskTest),
      GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(FieldMaskTest, _internal_metadata_));
  FieldMaskTestCases_descriptor_ = file->message_type(2);
  static const int FieldMaskTestCases_offsets_[12] = {
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(FieldMaskTestCases, single_mask_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(FieldMaskTestCases, multiple_mask_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(FieldMaskTestCases, snake_camel_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(FieldMaskTestCases, empty_field_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(FieldMaskTestCases, apiary_format1_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(FieldMaskTestCases, apiary_format2_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(FieldMaskTestCases, apiary_format3_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(FieldMaskTestCases, map_key1_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(FieldMaskTestCases, map_key2_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(FieldMaskTestCases, map_key3_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(FieldMaskTestCases, map_key4_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(FieldMaskTestCases, map_key5_),
  };
  FieldMaskTestCases_reflection_ =
    ::google::protobuf::internal::GeneratedMessageReflection::NewGeneratedMessageReflection(
      FieldMaskTestCases_descriptor_,
      FieldMaskTestCases::internal_default_instance(),
      FieldMaskTestCases_offsets_,
      -1,
      -1,
      -1,
      sizeof(FieldMaskTestCases),
      GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(FieldMaskTestCases, _internal_metadata_));
  FieldMaskWrapper_descriptor_ = file->message_type(3);
  static const int FieldMaskWrapper_offsets_[1] = {
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(FieldMaskWrapper, mask_),
  };
  FieldMaskWrapper_reflection_ =
    ::google::protobuf::internal::GeneratedMessageReflection::NewGeneratedMessageReflection(
      FieldMaskWrapper_descriptor_,
      FieldMaskWrapper::internal_default_instance(),
      FieldMaskWrapper_offsets_,
      -1,
      -1,
      -1,
      sizeof(FieldMaskWrapper),
      GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(FieldMaskWrapper, _internal_metadata_));
}

namespace {

GOOGLE_PROTOBUF_DECLARE_ONCE(protobuf_AssignDescriptors_once_);
void protobuf_AssignDescriptorsOnce() {
  ::google::protobuf::GoogleOnceInit(&protobuf_AssignDescriptors_once_,
                 &protobuf_AssignDesc_google_2fprotobuf_2futil_2finternal_2ftestdata_2ffield_5fmask_2eproto);
}

void protobuf_RegisterTypes(const ::std::string&) GOOGLE_ATTRIBUTE_COLD;
void protobuf_RegisterTypes(const ::std::string&) {
  protobuf_AssignDescriptorsOnce();
  ::google::protobuf::MessageFactory::InternalRegisterGeneratedMessage(
      NestedFieldMask_descriptor_, NestedFieldMask::internal_default_instance());
  ::google::protobuf::MessageFactory::InternalRegisterGeneratedMessage(
      FieldMaskTest_descriptor_, FieldMaskTest::internal_default_instance());
  ::google::protobuf::MessageFactory::InternalRegisterGeneratedMessage(
      FieldMaskTestCases_descriptor_, FieldMaskTestCases::internal_default_instance());
  ::google::protobuf::MessageFactory::InternalRegisterGeneratedMessage(
      FieldMaskWrapper_descriptor_, FieldMaskWrapper::internal_default_instance());
}

}  // namespace

void protobuf_ShutdownFile_google_2fprotobuf_2futil_2finternal_2ftestdata_2ffield_5fmask_2eproto() {
  NestedFieldMask_default_instance_.Shutdown();
  delete NestedFieldMask_reflection_;
  FieldMaskTest_default_instance_.Shutdown();
  delete FieldMaskTest_reflection_;
  FieldMaskTestCases_default_instance_.Shutdown();
  delete FieldMaskTestCases_reflection_;
  FieldMaskWrapper_default_instance_.Shutdown();
  delete FieldMaskWrapper_reflection_;
}

void protobuf_InitDefaults_google_2fprotobuf_2futil_2finternal_2ftestdata_2ffield_5fmask_2eproto_impl() {
  GOOGLE_PROTOBUF_VERIFY_VERSION;

  ::google::protobuf::protobuf_InitDefaults_google_2fprotobuf_2ffield_5fmask_2eproto();
  ::google::protobuf::internal::GetEmptyString();
  NestedFieldMask_default_instance_.DefaultConstruct();
  ::google::protobuf::internal::GetEmptyString();
  FieldMaskTest_default_instance_.DefaultConstruct();
  FieldMaskTestCases_default_instance_.DefaultConstruct();
  FieldMaskWrapper_default_instance_.DefaultConstruct();
  NestedFieldMask_default_instance_.get_mutable()->InitAsDefaultInstance();
  FieldMaskTest_default_instance_.get_mutable()->InitAsDefaultInstance();
  FieldMaskTestCases_default_instance_.get_mutable()->InitAsDefaultInstance();
  FieldMaskWrapper_default_instance_.get_mutable()->InitAsDefaultInstance();
}

GOOGLE_PROTOBUF_DECLARE_ONCE(protobuf_InitDefaults_google_2fprotobuf_2futil_2finternal_2ftestdata_2ffield_5fmask_2eproto_once_);
void protobuf_InitDefaults_google_2fprotobuf_2futil_2finternal_2ftestdata_2ffield_5fmask_2eproto() {
  ::google::protobuf::GoogleOnceInit(&protobuf_InitDefaults_google_2fprotobuf_2futil_2finternal_2ftestdata_2ffield_5fmask_2eproto_once_,
                 &protobuf_InitDefaults_google_2fprotobuf_2futil_2finternal_2ftestdata_2ffield_5fmask_2eproto_impl);
}
void protobuf_AddDesc_google_2fprotobuf_2futil_2finternal_2ftestdata_2ffield_5fmask_2eproto_impl() {
  GOOGLE_PROTOBUF_VERIFY_VERSION;

  protobuf_InitDefaults_google_2fprotobuf_2futil_2finternal_2ftestdata_2ffield_5fmask_2eproto();
  ::google::protobuf::DescriptorPool::InternalAddGeneratedFile(
    "\n7google/protobuf/util/internal/testdata"
    "/field_mask.proto\022\027google.protobuf.testi"
    "ng\032 google/protobuf/field_mask.proto\"\203\001\n"
    "\017NestedFieldMask\022\014\n\004data\030\001 \001(\t\022/\n\013single"
    "_mask\030\002 \001(\0132\032.google.protobuf.FieldMask\022"
    "1\n\rrepeated_mask\030\003 \003(\0132\032.google.protobuf"
    ".FieldMask\"\276\001\n\rFieldMaskTest\022\n\n\002id\030\001 \001(\t"
    "\022/\n\013single_mask\030\002 \001(\0132\032.google.protobuf."
    "FieldMask\0221\n\rrepeated_mask\030\003 \003(\0132\032.googl"
    "e.protobuf.FieldMask\022=\n\013nested_mask\030\004 \003("
    "\0132(.google.protobuf.testing.NestedFieldM"
    "ask\"\220\006\n\022FieldMaskTestCases\022>\n\013single_mas"
    "k\030\001 \001(\0132).google.protobuf.testing.FieldM"
    "askWrapper\022@\n\rmultiple_mask\030\002 \001(\0132).goog"
    "le.protobuf.testing.FieldMaskWrapper\022>\n\013"
    "snake_camel\030\003 \001(\0132).google.protobuf.test"
    "ing.FieldMaskWrapper\022>\n\013empty_field\030\004 \001("
    "\0132).google.protobuf.testing.FieldMaskWra"
    "pper\022A\n\016apiary_format1\030\005 \001(\0132).google.pr"
    "otobuf.testing.FieldMaskWrapper\022A\n\016apiar"
    "y_format2\030\006 \001(\0132).google.protobuf.testin"
    "g.FieldMaskWrapper\022A\n\016apiary_format3\030\007 \001"
    "(\0132).google.protobuf.testing.FieldMaskWr"
    "apper\022;\n\010map_key1\030\010 \001(\0132).google.protobu"
    "f.testing.FieldMaskWrapper\022;\n\010map_key2\030\t"
    " \001(\0132).google.protobuf.testing.FieldMask"
    "Wrapper\022;\n\010map_key3\030\n \001(\0132).google.proto"
    "buf.testing.FieldMaskWrapper\022;\n\010map_key4"
    "\030\013 \001(\0132).google.protobuf.testing.FieldMa"
    "skWrapper\022;\n\010map_key5\030\014 \001(\0132).google.pro"
    "tobuf.testing.FieldMaskWrapper\"<\n\020FieldM"
    "askWrapper\022(\n\004mask\030\001 \001(\0132\032.google.protob"
    "uf.FieldMask2x\n\024FieldMaskTestService\022`\n\004"
    "Call\022+.google.protobuf.testing.FieldMask"
    "TestCases\032+.google.protobuf.testing.Fiel"
    "dMaskTestCasesb\006proto3", 1422);
  ::google::protobuf::MessageFactory::InternalRegisterGeneratedFile(
    "google/protobuf/util/internal/testdata/field_mask.proto", &protobuf_RegisterTypes);
  ::google::protobuf::protobuf_AddDesc_google_2fprotobuf_2ffield_5fmask_2eproto();
  ::google::protobuf::internal::OnShutdown(&protobuf_ShutdownFile_google_2fprotobuf_2futil_2finternal_2ftestdata_2ffield_5fmask_2eproto);
}

GOOGLE_PROTOBUF_DECLARE_ONCE(protobuf_AddDesc_google_2fprotobuf_2futil_2finternal_2ftestdata_2ffield_5fmask_2eproto_once_);
void protobuf_AddDesc_google_2fprotobuf_2futil_2finternal_2ftestdata_2ffield_5fmask_2eproto() {
  ::google::protobuf::GoogleOnceInit(&protobuf_AddDesc_google_2fprotobuf_2futil_2finternal_2ftestdata_2ffield_5fmask_2eproto_once_,
                 &protobuf_AddDesc_google_2fprotobuf_2futil_2finternal_2ftestdata_2ffield_5fmask_2eproto_impl);
}
// Force AddDescriptors() to be called at static initialization time.
struct StaticDescriptorInitializer_google_2fprotobuf_2futil_2finternal_2ftestdata_2ffield_5fmask_2eproto {
  StaticDescriptorInitializer_google_2fprotobuf_2futil_2finternal_2ftestdata_2ffield_5fmask_2eproto() {
    protobuf_AddDesc_google_2fprotobuf_2futil_2finternal_2ftestdata_2ffield_5fmask_2eproto();
  }
} static_descriptor_initializer_google_2fprotobuf_2futil_2finternal_2ftestdata_2ffield_5fmask_2eproto_;

namespace {

static void MergeFromFail(int line) GOOGLE_ATTRIBUTE_COLD GOOGLE_ATTRIBUTE_NORETURN;
static void MergeFromFail(int line) {
  ::google::protobuf::internal::MergeFromFail(__FILE__, line);
}

}  // namespace


// ===================================================================

#if !defined(_MSC_VER) || _MSC_VER >= 1900
const int NestedFieldMask::kDataFieldNumber;
const int NestedFieldMask::kSingleMaskFieldNumber;
const int NestedFieldMask::kRepeatedMaskFieldNumber;
#endif  // !defined(_MSC_VER) || _MSC_VER >= 1900

NestedFieldMask::NestedFieldMask()
  : ::google::protobuf::Message(), _internal_metadata_(NULL) {
  if (this != internal_default_instance()) protobuf_InitDefaults_google_2fprotobuf_2futil_2finternal_2ftestdata_2ffield_5fmask_2eproto();
  SharedCtor();
  // @@protoc_insertion_point(constructor:google.protobuf.testing.NestedFieldMask)
}

void NestedFieldMask::InitAsDefaultInstance() {
  single_mask_ = const_cast< ::google::protobuf::FieldMask*>(
      ::google::protobuf::FieldMask::internal_default_instance());
}

NestedFieldMask::NestedFieldMask(const NestedFieldMask& from)
  : ::google::protobuf::Message(),
    _internal_metadata_(NULL) {
  SharedCtor();
  UnsafeMergeFrom(from);
  // @@protoc_insertion_point(copy_constructor:google.protobuf.testing.NestedFieldMask)
}

void NestedFieldMask::SharedCtor() {
  data_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  single_mask_ = NULL;
  _cached_size_ = 0;
}

NestedFieldMask::~NestedFieldMask() {
  // @@protoc_insertion_point(destructor:google.protobuf.testing.NestedFieldMask)
  SharedDtor();
}

void NestedFieldMask::SharedDtor() {
  data_.DestroyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  if (this != &NestedFieldMask_default_instance_.get()) {
    delete single_mask_;
  }
}

void NestedFieldMask::SetCachedSize(int size) const {
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
}
const ::google::protobuf::Descriptor* NestedFieldMask::descriptor() {
  protobuf_AssignDescriptorsOnce();
  return NestedFieldMask_descriptor_;
}

const NestedFieldMask& NestedFieldMask::default_instance() {
  protobuf_InitDefaults_google_2fprotobuf_2futil_2finternal_2ftestdata_2ffield_5fmask_2eproto();
  return *internal_default_instance();
}

::google::protobuf::internal::ExplicitlyConstructed<NestedFieldMask> NestedFieldMask_default_instance_;

NestedFieldMask* NestedFieldMask::New(::google::protobuf::Arena* arena) const {
  NestedFieldMask* n = new NestedFieldMask;
  if (arena != NULL) {
    arena->Own(n);
  }
  return n;
}

void NestedFieldMask::Clear() {
// @@protoc_insertion_point(message_clear_start:google.protobuf.testing.NestedFieldMask)
  data_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  if (GetArenaNoVirtual() == NULL && single_mask_ != NULL) delete single_mask_;
  single_mask_ = NULL;
  repeated_mask_.Clear();
}

bool NestedFieldMask::MergePartialFromCodedStream(
    ::google::protobuf::io::CodedInputStream* input) {
#define DO_(EXPRESSION) if (!GOOGLE_PREDICT_TRUE(EXPRESSION)) goto failure
  ::google::protobuf::uint32 tag;
  // @@protoc_insertion_point(parse_start:google.protobuf.testing.NestedFieldMask)
  for (;;) {
    ::std::pair< ::google::protobuf::uint32, bool> p = input->ReadTagWithCutoff(127);
    tag = p.first;
    if (!p.second) goto handle_unusual;
    switch (::google::protobuf::internal::WireFormatLite::GetTagFieldNumber(tag)) {
      // optional string data = 1;
      case 1: {
        if (tag == 10) {
          DO_(::google::protobuf::internal::WireFormatLite::ReadString(
                input, this->mutable_data()));
          DO_(::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
            this->data().data(), this->data().length(),
            ::google::protobuf::internal::WireFormatLite::PARSE,
            "google.protobuf.testing.NestedFieldMask.data"));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(18)) goto parse_single_mask;
        break;
      }

      // optional .google.protobuf.FieldMask single_mask = 2;
      case 2: {
        if (tag == 18) {
         parse_single_mask:
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessageNoVirtual(
               input, mutable_single_mask()));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(26)) goto parse_repeated_mask;
        break;
      }

      // repeated .google.protobuf.FieldMask repeated_mask = 3;
      case 3: {
        if (tag == 26) {
         parse_repeated_mask:
          DO_(input->IncrementRecursionDepth());
         parse_loop_repeated_mask:
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessageNoVirtualNoRecursionDepth(
                input, add_repeated_mask()));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(26)) goto parse_loop_repeated_mask;
        input->UnsafeDecrementRecursionDepth();
        if (input->ExpectAtEnd()) goto success;
        break;
      }

      default: {
      handle_unusual:
        if (tag == 0 ||
            ::google::protobuf::internal::WireFormatLite::GetTagWireType(tag) ==
            ::google::protobuf::internal::WireFormatLite::WIRETYPE_END_GROUP) {
          goto success;
        }
        DO_(::google::protobuf::internal::WireFormatLite::SkipField(input, tag));
        break;
      }
    }
  }
success:
  // @@protoc_insertion_point(parse_success:google.protobuf.testing.NestedFieldMask)
  return true;
failure:
  // @@protoc_insertion_point(parse_failure:google.protobuf.testing.NestedFieldMask)
  return false;
#undef DO_
}

void NestedFieldMask::SerializeWithCachedSizes(
    ::google::protobuf::io::CodedOutputStream* output) const {
  // @@protoc_insertion_point(serialize_start:google.protobuf.testing.NestedFieldMask)
  // optional string data = 1;
  if (this->data().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->data().data(), this->data().length(),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "google.protobuf.testing.NestedFieldMask.data");
    ::google::protobuf::internal::WireFormatLite::WriteStringMaybeAliased(
      1, this->data(), output);
  }

  // optional .google.protobuf.FieldMask single_mask = 2;
  if (this->has_single_mask()) {
    ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
      2, *this->single_mask_, output);
  }

  // repeated .google.protobuf.FieldMask repeated_mask = 3;
  for (unsigned int i = 0, n = this->repeated_mask_size(); i < n; i++) {
    ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
      3, this->repeated_mask(i), output);
  }

  // @@protoc_insertion_point(serialize_end:google.protobuf.testing.NestedFieldMask)
}

::google::protobuf::uint8* NestedFieldMask::InternalSerializeWithCachedSizesToArray(
    bool deterministic, ::google::protobuf::uint8* target) const {
  (void)deterministic; // Unused
  // @@protoc_insertion_point(serialize_to_array_start:google.protobuf.testing.NestedFieldMask)
  // optional string data = 1;
  if (this->data().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->data().data(), this->data().length(),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "google.protobuf.testing.NestedFieldMask.data");
    target =
      ::google::protobuf::internal::WireFormatLite::WriteStringToArray(
        1, this->data(), target);
  }

  // optional .google.protobuf.FieldMask single_mask = 2;
  if (this->has_single_mask()) {
    target = ::google::protobuf::internal::WireFormatLite::
      InternalWriteMessageNoVirtualToArray(
        2, *this->single_mask_, false, target);
  }

  // repeated .google.protobuf.FieldMask repeated_mask = 3;
  for (unsigned int i = 0, n = this->repeated_mask_size(); i < n; i++) {
    target = ::google::protobuf::internal::WireFormatLite::
      InternalWriteMessageNoVirtualToArray(
        3, this->repeated_mask(i), false, target);
  }

  // @@protoc_insertion_point(serialize_to_array_end:google.protobuf.testing.NestedFieldMask)
  return target;
}

size_t NestedFieldMask::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:google.protobuf.testing.NestedFieldMask)
  size_t total_size = 0;

  // optional string data = 1;
  if (this->data().size() > 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::StringSize(
        this->data());
  }

  // optional .google.protobuf.FieldMask single_mask = 2;
  if (this->has_single_mask()) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::MessageSizeNoVirtual(
        *this->single_mask_);
  }

  // repeated .google.protobuf.FieldMask repeated_mask = 3;
  {
    unsigned int count = this->repeated_mask_size();
    total_size += 1UL * count;
    for (unsigned int i = 0; i < count; i++) {
      total_size +=
        ::google::protobuf::internal::WireFormatLite::MessageSizeNoVirtual(
          this->repeated_mask(i));
    }
  }

  int cached_size = ::google::protobuf::internal::ToCachedSize(total_size);
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = cached_size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
  return total_size;
}

void NestedFieldMask::MergeFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_merge_from_start:google.protobuf.testing.NestedFieldMask)
  if (GOOGLE_PREDICT_FALSE(&from == this)) MergeFromFail(__LINE__);
  const NestedFieldMask* source =
      ::google::protobuf::internal::DynamicCastToGenerated<const NestedFieldMask>(
          &from);
  if (source == NULL) {
  // @@protoc_insertion_point(generalized_merge_from_cast_fail:google.protobuf.testing.NestedFieldMask)
    ::google::protobuf::internal::ReflectionOps::Merge(from, this);
  } else {
  // @@protoc_insertion_point(generalized_merge_from_cast_success:google.protobuf.testing.NestedFieldMask)
    UnsafeMergeFrom(*source);
  }
}

void NestedFieldMask::MergeFrom(const NestedFieldMask& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:google.protobuf.testing.NestedFieldMask)
  if (GOOGLE_PREDICT_TRUE(&from != this)) {
    UnsafeMergeFrom(from);
  } else {
    MergeFromFail(__LINE__);
  }
}

void NestedFieldMask::UnsafeMergeFrom(const NestedFieldMask& from) {
  GOOGLE_DCHECK(&from != this);
  repeated_mask_.MergeFrom(from.repeated_mask_);
  if (from.data().size() > 0) {

    data_.AssignWithDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), from.data_);
  }
  if (from.has_single_mask()) {
    mutable_single_mask()->::google::protobuf::FieldMask::MergeFrom(from.single_mask());
  }
}

void NestedFieldMask::CopyFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_copy_from_start:google.protobuf.testing.NestedFieldMask)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

void NestedFieldMask::CopyFrom(const NestedFieldMask& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:google.protobuf.testing.NestedFieldMask)
  if (&from == this) return;
  Clear();
  UnsafeMergeFrom(from);
}

bool NestedFieldMask::IsInitialized() const {

  return true;
}

void NestedFieldMask::Swap(NestedFieldMask* other) {
  if (other == this) return;
  InternalSwap(other);
}
void NestedFieldMask::InternalSwap(NestedFieldMask* other) {
  data_.Swap(&other->data_);
  std::swap(single_mask_, other->single_mask_);
  repeated_mask_.UnsafeArenaSwap(&other->repeated_mask_);
  _internal_metadata_.Swap(&other->_internal_metadata_);
  std::swap(_cached_size_, other->_cached_size_);
}

::google::protobuf::Metadata NestedFieldMask::GetMetadata() const {
  protobuf_AssignDescriptorsOnce();
  ::google::protobuf::Metadata metadata;
  metadata.descriptor = NestedFieldMask_descriptor_;
  metadata.reflection = NestedFieldMask_reflection_;
  return metadata;
}

#if PROTOBUF_INLINE_NOT_IN_HEADERS
// NestedFieldMask

// optional string data = 1;
void NestedFieldMask::clear_data() {
  data_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
const ::std::string& NestedFieldMask::data() const {
  // @@protoc_insertion_point(field_get:google.protobuf.testing.NestedFieldMask.data)
  return data_.GetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
void NestedFieldMask::set_data(const ::std::string& value) {
  
  data_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:google.protobuf.testing.NestedFieldMask.data)
}
void NestedFieldMask::set_data(const char* value) {
  
  data_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:google.protobuf.testing.NestedFieldMask.data)
}
void NestedFieldMask::set_data(const char* value, size_t size) {
  
  data_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:google.protobuf.testing.NestedFieldMask.data)
}
::std::string* NestedFieldMask::mutable_data() {
  
  // @@protoc_insertion_point(field_mutable:google.protobuf.testing.NestedFieldMask.data)
  return data_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
::std::string* NestedFieldMask::release_data() {
  // @@protoc_insertion_point(field_release:google.protobuf.testing.NestedFieldMask.data)
  
  return data_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
void NestedFieldMask::set_allocated_data(::std::string* data) {
  if (data != NULL) {
    
  } else {
    
  }
  data_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), data);
  // @@protoc_insertion_point(field_set_allocated:google.protobuf.testing.NestedFieldMask.data)
}

// optional .google.protobuf.FieldMask single_mask = 2;
bool NestedFieldMask::has_single_mask() const {
  return this != internal_default_instance() && single_mask_ != NULL;
}
void NestedFieldMask::clear_single_mask() {
  if (GetArenaNoVirtual() == NULL && single_mask_ != NULL) delete single_mask_;
  single_mask_ = NULL;
}
const ::google::protobuf::FieldMask& NestedFieldMask::single_mask() const {
  // @@protoc_insertion_point(field_get:google.protobuf.testing.NestedFieldMask.single_mask)
  return single_mask_ != NULL ? *single_mask_
                         : *::google::protobuf::FieldMask::internal_default_instance();
}
::google::protobuf::FieldMask* NestedFieldMask::mutable_single_mask() {
  
  if (single_mask_ == NULL) {
    single_mask_ = new ::google::protobuf::FieldMask;
  }
  // @@protoc_insertion_point(field_mutable:google.protobuf.testing.NestedFieldMask.single_mask)
  return single_mask_;
}
::google::protobuf::FieldMask* NestedFieldMask::release_single_mask() {
  // @@protoc_insertion_point(field_release:google.protobuf.testing.NestedFieldMask.single_mask)
  
  ::google::protobuf::FieldMask* temp = single_mask_;
  single_mask_ = NULL;
  return temp;
}
void NestedFieldMask::set_allocated_single_mask(::google::protobuf::FieldMask* single_mask) {
  delete single_mask_;
  single_mask_ = single_mask;
  if (single_mask) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_set_allocated:google.protobuf.testing.NestedFieldMask.single_mask)
}

// repeated .google.protobuf.FieldMask repeated_mask = 3;
int NestedFieldMask::repeated_mask_size() const {
  return repeated_mask_.size();
}
void NestedFieldMask::clear_repeated_mask() {
  repeated_mask_.Clear();
}
const ::google::protobuf::FieldMask& NestedFieldMask::repeated_mask(int index) const {
  // @@protoc_insertion_point(field_get:google.protobuf.testing.NestedFieldMask.repeated_mask)
  return repeated_mask_.Get(index);
}
::google::protobuf::FieldMask* NestedFieldMask::mutable_repeated_mask(int index) {
  // @@protoc_insertion_point(field_mutable:google.protobuf.testing.NestedFieldMask.repeated_mask)
  return repeated_mask_.Mutable(index);
}
::google::protobuf::FieldMask* NestedFieldMask::add_repeated_mask() {
  // @@protoc_insertion_point(field_add:google.protobuf.testing.NestedFieldMask.repeated_mask)
  return repeated_mask_.Add();
}
::google::protobuf::RepeatedPtrField< ::google::protobuf::FieldMask >*
NestedFieldMask::mutable_repeated_mask() {
  // @@protoc_insertion_point(field_mutable_list:google.protobuf.testing.NestedFieldMask.repeated_mask)
  return &repeated_mask_;
}
const ::google::protobuf::RepeatedPtrField< ::google::protobuf::FieldMask >&
NestedFieldMask::repeated_mask() const {
  // @@protoc_insertion_point(field_list:google.protobuf.testing.NestedFieldMask.repeated_mask)
  return repeated_mask_;
}

inline const NestedFieldMask* NestedFieldMask::internal_default_instance() {
  return &NestedFieldMask_default_instance_.get();
}
#endif  // PROTOBUF_INLINE_NOT_IN_HEADERS

// ===================================================================

#if !defined(_MSC_VER) || _MSC_VER >= 1900
const int FieldMaskTest::kIdFieldNumber;
const int FieldMaskTest::kSingleMaskFieldNumber;
const int FieldMaskTest::kRepeatedMaskFieldNumber;
const int FieldMaskTest::kNestedMaskFieldNumber;
#endif  // !defined(_MSC_VER) || _MSC_VER >= 1900

FieldMaskTest::FieldMaskTest()
  : ::google::protobuf::Message(), _internal_metadata_(NULL) {
  if (this != internal_default_instance()) protobuf_InitDefaults_google_2fprotobuf_2futil_2finternal_2ftestdata_2ffield_5fmask_2eproto();
  SharedCtor();
  // @@protoc_insertion_point(constructor:google.protobuf.testing.FieldMaskTest)
}

void FieldMaskTest::InitAsDefaultInstance() {
  single_mask_ = const_cast< ::google::protobuf::FieldMask*>(
      ::google::protobuf::FieldMask::internal_default_instance());
}

FieldMaskTest::FieldMaskTest(const FieldMaskTest& from)
  : ::google::protobuf::Message(),
    _internal_metadata_(NULL) {
  SharedCtor();
  UnsafeMergeFrom(from);
  // @@protoc_insertion_point(copy_constructor:google.protobuf.testing.FieldMaskTest)
}

void FieldMaskTest::SharedCtor() {
  id_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  single_mask_ = NULL;
  _cached_size_ = 0;
}

FieldMaskTest::~FieldMaskTest() {
  // @@protoc_insertion_point(destructor:google.protobuf.testing.FieldMaskTest)
  SharedDtor();
}

void FieldMaskTest::SharedDtor() {
  id_.DestroyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  if (this != &FieldMaskTest_default_instance_.get()) {
    delete single_mask_;
  }
}

void FieldMaskTest::SetCachedSize(int size) const {
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
}
const ::google::protobuf::Descriptor* FieldMaskTest::descriptor() {
  protobuf_AssignDescriptorsOnce();
  return FieldMaskTest_descriptor_;
}

const FieldMaskTest& FieldMaskTest::default_instance() {
  protobuf_InitDefaults_google_2fprotobuf_2futil_2finternal_2ftestdata_2ffield_5fmask_2eproto();
  return *internal_default_instance();
}

::google::protobuf::internal::ExplicitlyConstructed<FieldMaskTest> FieldMaskTest_default_instance_;

FieldMaskTest* FieldMaskTest::New(::google::protobuf::Arena* arena) const {
  FieldMaskTest* n = new FieldMaskTest;
  if (arena != NULL) {
    arena->Own(n);
  }
  return n;
}

void FieldMaskTest::Clear() {
// @@protoc_insertion_point(message_clear_start:google.protobuf.testing.FieldMaskTest)
  id_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  if (GetArenaNoVirtual() == NULL && single_mask_ != NULL) delete single_mask_;
  single_mask_ = NULL;
  repeated_mask_.Clear();
  nested_mask_.Clear();
}

bool FieldMaskTest::MergePartialFromCodedStream(
    ::google::protobuf::io::CodedInputStream* input) {
#define DO_(EXPRESSION) if (!GOOGLE_PREDICT_TRUE(EXPRESSION)) goto failure
  ::google::protobuf::uint32 tag;
  // @@protoc_insertion_point(parse_start:google.protobuf.testing.FieldMaskTest)
  for (;;) {
    ::std::pair< ::google::protobuf::uint32, bool> p = input->ReadTagWithCutoff(127);
    tag = p.first;
    if (!p.second) goto handle_unusual;
    switch (::google::protobuf::internal::WireFormatLite::GetTagFieldNumber(tag)) {
      // optional string id = 1;
      case 1: {
        if (tag == 10) {
          DO_(::google::protobuf::internal::WireFormatLite::ReadString(
                input, this->mutable_id()));
          DO_(::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
            this->id().data(), this->id().length(),
            ::google::protobuf::internal::WireFormatLite::PARSE,
            "google.protobuf.testing.FieldMaskTest.id"));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(18)) goto parse_single_mask;
        break;
      }

      // optional .google.protobuf.FieldMask single_mask = 2;
      case 2: {
        if (tag == 18) {
         parse_single_mask:
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessageNoVirtual(
               input, mutable_single_mask()));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(26)) goto parse_repeated_mask;
        break;
      }

      // repeated .google.protobuf.FieldMask repeated_mask = 3;
      case 3: {
        if (tag == 26) {
         parse_repeated_mask:
          DO_(input->IncrementRecursionDepth());
         parse_loop_repeated_mask:
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessageNoVirtualNoRecursionDepth(
                input, add_repeated_mask()));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(26)) goto parse_loop_repeated_mask;
        if (input->ExpectTag(34)) goto parse_loop_nested_mask;
        input->UnsafeDecrementRecursionDepth();
        break;
      }

      // repeated .google.protobuf.testing.NestedFieldMask nested_mask = 4;
      case 4: {
        if (tag == 34) {
          DO_(input->IncrementRecursionDepth());
         parse_loop_nested_mask:
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessageNoVirtualNoRecursionDepth(
                input, add_nested_mask()));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(34)) goto parse_loop_nested_mask;
        input->UnsafeDecrementRecursionDepth();
        if (input->ExpectAtEnd()) goto success;
        break;
      }

      default: {
      handle_unusual:
        if (tag == 0 ||
            ::google::protobuf::internal::WireFormatLite::GetTagWireType(tag) ==
            ::google::protobuf::internal::WireFormatLite::WIRETYPE_END_GROUP) {
          goto success;
        }
        DO_(::google::protobuf::internal::WireFormatLite::SkipField(input, tag));
        break;
      }
    }
  }
success:
  // @@protoc_insertion_point(parse_success:google.protobuf.testing.FieldMaskTest)
  return true;
failure:
  // @@protoc_insertion_point(parse_failure:google.protobuf.testing.FieldMaskTest)
  return false;
#undef DO_
}

void FieldMaskTest::SerializeWithCachedSizes(
    ::google::protobuf::io::CodedOutputStream* output) const {
  // @@protoc_insertion_point(serialize_start:google.protobuf.testing.FieldMaskTest)
  // optional string id = 1;
  if (this->id().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->id().data(), this->id().length(),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "google.protobuf.testing.FieldMaskTest.id");
    ::google::protobuf::internal::WireFormatLite::WriteStringMaybeAliased(
      1, this->id(), output);
  }

  // optional .google.protobuf.FieldMask single_mask = 2;
  if (this->has_single_mask()) {
    ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
      2, *this->single_mask_, output);
  }

  // repeated .google.protobuf.FieldMask repeated_mask = 3;
  for (unsigned int i = 0, n = this->repeated_mask_size(); i < n; i++) {
    ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
      3, this->repeated_mask(i), output);
  }

  // repeated .google.protobuf.testing.NestedFieldMask nested_mask = 4;
  for (unsigned int i = 0, n = this->nested_mask_size(); i < n; i++) {
    ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
      4, this->nested_mask(i), output);
  }

  // @@protoc_insertion_point(serialize_end:google.protobuf.testing.FieldMaskTest)
}

::google::protobuf::uint8* FieldMaskTest::InternalSerializeWithCachedSizesToArray(
    bool deterministic, ::google::protobuf::uint8* target) const {
  (void)deterministic; // Unused
  // @@protoc_insertion_point(serialize_to_array_start:google.protobuf.testing.FieldMaskTest)
  // optional string id = 1;
  if (this->id().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->id().data(), this->id().length(),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "google.protobuf.testing.FieldMaskTest.id");
    target =
      ::google::protobuf::internal::WireFormatLite::WriteStringToArray(
        1, this->id(), target);
  }

  // optional .google.protobuf.FieldMask single_mask = 2;
  if (this->has_single_mask()) {
    target = ::google::protobuf::internal::WireFormatLite::
      InternalWriteMessageNoVirtualToArray(
        2, *this->single_mask_, false, target);
  }

  // repeated .google.protobuf.FieldMask repeated_mask = 3;
  for (unsigned int i = 0, n = this->repeated_mask_size(); i < n; i++) {
    target = ::google::protobuf::internal::WireFormatLite::
      InternalWriteMessageNoVirtualToArray(
        3, this->repeated_mask(i), false, target);
  }

  // repeated .google.protobuf.testing.NestedFieldMask nested_mask = 4;
  for (unsigned int i = 0, n = this->nested_mask_size(); i < n; i++) {
    target = ::google::protobuf::internal::WireFormatLite::
      InternalWriteMessageNoVirtualToArray(
        4, this->nested_mask(i), false, target);
  }

  // @@protoc_insertion_point(serialize_to_array_end:google.protobuf.testing.FieldMaskTest)
  return target;
}

size_t FieldMaskTest::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:google.protobuf.testing.FieldMaskTest)
  size_t total_size = 0;

  // optional string id = 1;
  if (this->id().size() > 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::StringSize(
        this->id());
  }

  // optional .google.protobuf.FieldMask single_mask = 2;
  if (this->has_single_mask()) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::MessageSizeNoVirtual(
        *this->single_mask_);
  }

  // repeated .google.protobuf.FieldMask repeated_mask = 3;
  {
    unsigned int count = this->repeated_mask_size();
    total_size += 1UL * count;
    for (unsigned int i = 0; i < count; i++) {
      total_size +=
        ::google::protobuf::internal::WireFormatLite::MessageSizeNoVirtual(
          this->repeated_mask(i));
    }
  }

  // repeated .google.protobuf.testing.NestedFieldMask nested_mask = 4;
  {
    unsigned int count = this->nested_mask_size();
    total_size += 1UL * count;
    for (unsigned int i = 0; i < count; i++) {
      total_size +=
        ::google::protobuf::internal::WireFormatLite::MessageSizeNoVirtual(
          this->nested_mask(i));
    }
  }

  int cached_size = ::google::protobuf::internal::ToCachedSize(total_size);
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = cached_size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
  return total_size;
}

void FieldMaskTest::MergeFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_merge_from_start:google.protobuf.testing.FieldMaskTest)
  if (GOOGLE_PREDICT_FALSE(&from == this)) MergeFromFail(__LINE__);
  const FieldMaskTest* source =
      ::google::protobuf::internal::DynamicCastToGenerated<const FieldMaskTest>(
          &from);
  if (source == NULL) {
  // @@protoc_insertion_point(generalized_merge_from_cast_fail:google.protobuf.testing.FieldMaskTest)
    ::google::protobuf::internal::ReflectionOps::Merge(from, this);
  } else {
  // @@protoc_insertion_point(generalized_merge_from_cast_success:google.protobuf.testing.FieldMaskTest)
    UnsafeMergeFrom(*source);
  }
}

void FieldMaskTest::MergeFrom(const FieldMaskTest& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:google.protobuf.testing.FieldMaskTest)
  if (GOOGLE_PREDICT_TRUE(&from != this)) {
    UnsafeMergeFrom(from);
  } else {
    MergeFromFail(__LINE__);
  }
}

void FieldMaskTest::UnsafeMergeFrom(const FieldMaskTest& from) {
  GOOGLE_DCHECK(&from != this);
  repeated_mask_.MergeFrom(from.repeated_mask_);
  nested_mask_.MergeFrom(from.nested_mask_);
  if (from.id().size() > 0) {

    id_.AssignWithDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), from.id_);
  }
  if (from.has_single_mask()) {
    mutable_single_mask()->::google::protobuf::FieldMask::MergeFrom(from.single_mask());
  }
}

void FieldMaskTest::CopyFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_copy_from_start:google.protobuf.testing.FieldMaskTest)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

void FieldMaskTest::CopyFrom(const FieldMaskTest& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:google.protobuf.testing.FieldMaskTest)
  if (&from == this) return;
  Clear();
  UnsafeMergeFrom(from);
}

bool FieldMaskTest::IsInitialized() const {

  return true;
}

void FieldMaskTest::Swap(FieldMaskTest* other) {
  if (other == this) return;
  InternalSwap(other);
}
void FieldMaskTest::InternalSwap(FieldMaskTest* other) {
  id_.Swap(&other->id_);
  std::swap(single_mask_, other->single_mask_);
  repeated_mask_.UnsafeArenaSwap(&other->repeated_mask_);
  nested_mask_.UnsafeArenaSwap(&other->nested_mask_);
  _internal_metadata_.Swap(&other->_internal_metadata_);
  std::swap(_cached_size_, other->_cached_size_);
}

::google::protobuf::Metadata FieldMaskTest::GetMetadata() const {
  protobuf_AssignDescriptorsOnce();
  ::google::protobuf::Metadata metadata;
  metadata.descriptor = FieldMaskTest_descriptor_;
  metadata.reflection = FieldMaskTest_reflection_;
  return metadata;
}

#if PROTOBUF_INLINE_NOT_IN_HEADERS
// FieldMaskTest

// optional string id = 1;
void FieldMaskTest::clear_id() {
  id_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
const ::std::string& FieldMaskTest::id() const {
  // @@protoc_insertion_point(field_get:google.protobuf.testing.FieldMaskTest.id)
  return id_.GetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
void FieldMaskTest::set_id(const ::std::string& value) {
  
  id_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:google.protobuf.testing.FieldMaskTest.id)
}
void FieldMaskTest::set_id(const char* value) {
  
  id_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:google.protobuf.testing.FieldMaskTest.id)
}
void FieldMaskTest::set_id(const char* value, size_t size) {
  
  id_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:google.protobuf.testing.FieldMaskTest.id)
}
::std::string* FieldMaskTest::mutable_id() {
  
  // @@protoc_insertion_point(field_mutable:google.protobuf.testing.FieldMaskTest.id)
  return id_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
::std::string* FieldMaskTest::release_id() {
  // @@protoc_insertion_point(field_release:google.protobuf.testing.FieldMaskTest.id)
  
  return id_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
void FieldMaskTest::set_allocated_id(::std::string* id) {
  if (id != NULL) {
    
  } else {
    
  }
  id_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), id);
  // @@protoc_insertion_point(field_set_allocated:google.protobuf.testing.FieldMaskTest.id)
}

// optional .google.protobuf.FieldMask single_mask = 2;
bool FieldMaskTest::has_single_mask() const {
  return this != internal_default_instance() && single_mask_ != NULL;
}
void FieldMaskTest::clear_single_mask() {
  if (GetArenaNoVirtual() == NULL && single_mask_ != NULL) delete single_mask_;
  single_mask_ = NULL;
}
const ::google::protobuf::FieldMask& FieldMaskTest::single_mask() const {
  // @@protoc_insertion_point(field_get:google.protobuf.testing.FieldMaskTest.single_mask)
  return single_mask_ != NULL ? *single_mask_
                         : *::google::protobuf::FieldMask::internal_default_instance();
}
::google::protobuf::FieldMask* FieldMaskTest::mutable_single_mask() {
  
  if (single_mask_ == NULL) {
    single_mask_ = new ::google::protobuf::FieldMask;
  }
  // @@protoc_insertion_point(field_mutable:google.protobuf.testing.FieldMaskTest.single_mask)
  return single_mask_;
}
::google::protobuf::FieldMask* FieldMaskTest::release_single_mask() {
  // @@protoc_insertion_point(field_release:google.protobuf.testing.FieldMaskTest.single_mask)
  
  ::google::protobuf::FieldMask* temp = single_mask_;
  single_mask_ = NULL;
  return temp;
}
void FieldMaskTest::set_allocated_single_mask(::google::protobuf::FieldMask* single_mask) {
  delete single_mask_;
  single_mask_ = single_mask;
  if (single_mask) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_set_allocated:google.protobuf.testing.FieldMaskTest.single_mask)
}

// repeated .google.protobuf.FieldMask repeated_mask = 3;
int FieldMaskTest::repeated_mask_size() const {
  return repeated_mask_.size();
}
void FieldMaskTest::clear_repeated_mask() {
  repeated_mask_.Clear();
}
const ::google::protobuf::FieldMask& FieldMaskTest::repeated_mask(int index) const {
  // @@protoc_insertion_point(field_get:google.protobuf.testing.FieldMaskTest.repeated_mask)
  return repeated_mask_.Get(index);
}
::google::protobuf::FieldMask* FieldMaskTest::mutable_repeated_mask(int index) {
  // @@protoc_insertion_point(field_mutable:google.protobuf.testing.FieldMaskTest.repeated_mask)
  return repeated_mask_.Mutable(index);
}
::google::protobuf::FieldMask* FieldMaskTest::add_repeated_mask() {
  // @@protoc_insertion_point(field_add:google.protobuf.testing.FieldMaskTest.repeated_mask)
  return repeated_mask_.Add();
}
::google::protobuf::RepeatedPtrField< ::google::protobuf::FieldMask >*
FieldMaskTest::mutable_repeated_mask() {
  // @@protoc_insertion_point(field_mutable_list:google.protobuf.testing.FieldMaskTest.repeated_mask)
  return &repeated_mask_;
}
const ::google::protobuf::RepeatedPtrField< ::google::protobuf::FieldMask >&
FieldMaskTest::repeated_mask() const {
  // @@protoc_insertion_point(field_list:google.protobuf.testing.FieldMaskTest.repeated_mask)
  return repeated_mask_;
}

// repeated .google.protobuf.testing.NestedFieldMask nested_mask = 4;
int FieldMaskTest::nested_mask_size() const {
  return nested_mask_.size();
}
void FieldMaskTest::clear_nested_mask() {
  nested_mask_.Clear();
}
const ::google::protobuf::testing::NestedFieldMask& FieldMaskTest::nested_mask(int index) const {
  // @@protoc_insertion_point(field_get:google.protobuf.testing.FieldMaskTest.nested_mask)
  return nested_mask_.Get(index);
}
::google::protobuf::testing::NestedFieldMask* FieldMaskTest::mutable_nested_mask(int index) {
  // @@protoc_insertion_point(field_mutable:google.protobuf.testing.FieldMaskTest.nested_mask)
  return nested_mask_.Mutable(index);
}
::google::protobuf::testing::NestedFieldMask* FieldMaskTest::add_nested_mask() {
  // @@protoc_insertion_point(field_add:google.protobuf.testing.FieldMaskTest.nested_mask)
  return nested_mask_.Add();
}
::google::protobuf::RepeatedPtrField< ::google::protobuf::testing::NestedFieldMask >*
FieldMaskTest::mutable_nested_mask() {
  // @@protoc_insertion_point(field_mutable_list:google.protobuf.testing.FieldMaskTest.nested_mask)
  return &nested_mask_;
}
const ::google::protobuf::RepeatedPtrField< ::google::protobuf::testing::NestedFieldMask >&
FieldMaskTest::nested_mask() const {
  // @@protoc_insertion_point(field_list:google.protobuf.testing.FieldMaskTest.nested_mask)
  return nested_mask_;
}

inline const FieldMaskTest* FieldMaskTest::internal_default_instance() {
  return &FieldMaskTest_default_instance_.get();
}
#endif  // PROTOBUF_INLINE_NOT_IN_HEADERS

// ===================================================================

#if !defined(_MSC_VER) || _MSC_VER >= 1900
const int FieldMaskTestCases::kSingleMaskFieldNumber;
const int FieldMaskTestCases::kMultipleMaskFieldNumber;
const int FieldMaskTestCases::kSnakeCamelFieldNumber;
const int FieldMaskTestCases::kEmptyFieldFieldNumber;
const int FieldMaskTestCases::kApiaryFormat1FieldNumber;
const int FieldMaskTestCases::kApiaryFormat2FieldNumber;
const int FieldMaskTestCases::kApiaryFormat3FieldNumber;
const int FieldMaskTestCases::kMapKey1FieldNumber;
const int FieldMaskTestCases::kMapKey2FieldNumber;
const int FieldMaskTestCases::kMapKey3FieldNumber;
const int FieldMaskTestCases::kMapKey4FieldNumber;
const int FieldMaskTestCases::kMapKey5FieldNumber;
#endif  // !defined(_MSC_VER) || _MSC_VER >= 1900

FieldMaskTestCases::FieldMaskTestCases()
  : ::google::protobuf::Message(), _internal_metadata_(NULL) {
  if (this != internal_default_instance()) protobuf_InitDefaults_google_2fprotobuf_2futil_2finternal_2ftestdata_2ffield_5fmask_2eproto();
  SharedCtor();
  // @@protoc_insertion_point(constructor:google.protobuf.testing.FieldMaskTestCases)
}

void FieldMaskTestCases::InitAsDefaultInstance() {
  single_mask_ = const_cast< ::google::protobuf::testing::FieldMaskWrapper*>(
      ::google::protobuf::testing::FieldMaskWrapper::internal_default_instance());
  multiple_mask_ = const_cast< ::google::protobuf::testing::FieldMaskWrapper*>(
      ::google::protobuf::testing::FieldMaskWrapper::internal_default_instance());
  snake_camel_ = const_cast< ::google::protobuf::testing::FieldMaskWrapper*>(
      ::google::protobuf::testing::FieldMaskWrapper::internal_default_instance());
  empty_field_ = const_cast< ::google::protobuf::testing::FieldMaskWrapper*>(
      ::google::protobuf::testing::FieldMaskWrapper::internal_default_instance());
  apiary_format1_ = const_cast< ::google::protobuf::testing::FieldMaskWrapper*>(
      ::google::protobuf::testing::FieldMaskWrapper::internal_default_instance());
  apiary_format2_ = const_cast< ::google::protobuf::testing::FieldMaskWrapper*>(
      ::google::protobuf::testing::FieldMaskWrapper::internal_default_instance());
  apiary_format3_ = const_cast< ::google::protobuf::testing::FieldMaskWrapper*>(
      ::google::protobuf::testing::FieldMaskWrapper::internal_default_instance());
  map_key1_ = const_cast< ::google::protobuf::testing::FieldMaskWrapper*>(
      ::google::protobuf::testing::FieldMaskWrapper::internal_default_instance());
  map_key2_ = const_cast< ::google::protobuf::testing::FieldMaskWrapper*>(
      ::google::protobuf::testing::FieldMaskWrapper::internal_default_instance());
  map_key3_ = const_cast< ::google::protobuf::testing::FieldMaskWrapper*>(
      ::google::protobuf::testing::FieldMaskWrapper::internal_default_instance());
  map_key4_ = const_cast< ::google::protobuf::testing::FieldMaskWrapper*>(
      ::google::protobuf::testing::FieldMaskWrapper::internal_default_instance());
  map_key5_ = const_cast< ::google::protobuf::testing::FieldMaskWrapper*>(
      ::google::protobuf::testing::FieldMaskWrapper::internal_default_instance());
}

FieldMaskTestCases::FieldMaskTestCases(const FieldMaskTestCases& from)
  : ::google::protobuf::Message(),
    _internal_metadata_(NULL) {
  SharedCtor();
  UnsafeMergeFrom(from);
  // @@protoc_insertion_point(copy_constructor:google.protobuf.testing.FieldMaskTestCases)
}

void FieldMaskTestCases::SharedCtor() {
  single_mask_ = NULL;
  multiple_mask_ = NULL;
  snake_camel_ = NULL;
  empty_field_ = NULL;
  apiary_format1_ = NULL;
  apiary_format2_ = NULL;
  apiary_format3_ = NULL;
  map_key1_ = NULL;
  map_key2_ = NULL;
  map_key3_ = NULL;
  map_key4_ = NULL;
  map_key5_ = NULL;
  _cached_size_ = 0;
}

FieldMaskTestCases::~FieldMaskTestCases() {
  // @@protoc_insertion_point(destructor:google.protobuf.testing.FieldMaskTestCases)
  SharedDtor();
}

void FieldMaskTestCases::SharedDtor() {
  if (this != &FieldMaskTestCases_default_instance_.get()) {
    delete single_mask_;
    delete multiple_mask_;
    delete snake_camel_;
    delete empty_field_;
    delete apiary_format1_;
    delete apiary_format2_;
    delete apiary_format3_;
    delete map_key1_;
    delete map_key2_;
    delete map_key3_;
    delete map_key4_;
    delete map_key5_;
  }
}

void FieldMaskTestCases::SetCachedSize(int size) const {
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
}
const ::google::protobuf::Descriptor* FieldMaskTestCases::descriptor() {
  protobuf_AssignDescriptorsOnce();
  return FieldMaskTestCases_descriptor_;
}

const FieldMaskTestCases& FieldMaskTestCases::default_instance() {
  protobuf_InitDefaults_google_2fprotobuf_2futil_2finternal_2ftestdata_2ffield_5fmask_2eproto();
  return *internal_default_instance();
}

::google::protobuf::internal::ExplicitlyConstructed<FieldMaskTestCases> FieldMaskTestCases_default_instance_;

FieldMaskTestCases* FieldMaskTestCases::New(::google::protobuf::Arena* arena) const {
  FieldMaskTestCases* n = new FieldMaskTestCases;
  if (arena != NULL) {
    arena->Own(n);
  }
  return n;
}

void FieldMaskTestCases::Clear() {
// @@protoc_insertion_point(message_clear_start:google.protobuf.testing.FieldMaskTestCases)
  if (GetArenaNoVirtual() == NULL && single_mask_ != NULL) delete single_mask_;
  single_mask_ = NULL;
  if (GetArenaNoVirtual() == NULL && multiple_mask_ != NULL) delete multiple_mask_;
  multiple_mask_ = NULL;
  if (GetArenaNoVirtual() == NULL && snake_camel_ != NULL) delete snake_camel_;
  snake_camel_ = NULL;
  if (GetArenaNoVirtual() == NULL && empty_field_ != NULL) delete empty_field_;
  empty_field_ = NULL;
  if (GetArenaNoVirtual() == NULL && apiary_format1_ != NULL) delete apiary_format1_;
  apiary_format1_ = NULL;
  if (GetArenaNoVirtual() == NULL && apiary_format2_ != NULL) delete apiary_format2_;
  apiary_format2_ = NULL;
  if (GetArenaNoVirtual() == NULL && apiary_format3_ != NULL) delete apiary_format3_;
  apiary_format3_ = NULL;
  if (GetArenaNoVirtual() == NULL && map_key1_ != NULL) delete map_key1_;
  map_key1_ = NULL;
  if (GetArenaNoVirtual() == NULL && map_key2_ != NULL) delete map_key2_;
  map_key2_ = NULL;
  if (GetArenaNoVirtual() == NULL && map_key3_ != NULL) delete map_key3_;
  map_key3_ = NULL;
  if (GetArenaNoVirtual() == NULL && map_key4_ != NULL) delete map_key4_;
  map_key4_ = NULL;
  if (GetArenaNoVirtual() == NULL && map_key5_ != NULL) delete map_key5_;
  map_key5_ = NULL;
}

bool FieldMaskTestCases::MergePartialFromCodedStream(
    ::google::protobuf::io::CodedInputStream* input) {
#define DO_(EXPRESSION) if (!GOOGLE_PREDICT_TRUE(EXPRESSION)) goto failure
  ::google::protobuf::uint32 tag;
  // @@protoc_insertion_point(parse_start:google.protobuf.testing.FieldMaskTestCases)
  for (;;) {
    ::std::pair< ::google::protobuf::uint32, bool> p = input->ReadTagWithCutoff(127);
    tag = p.first;
    if (!p.second) goto handle_unusual;
    switch (::google::protobuf::internal::WireFormatLite::GetTagFieldNumber(tag)) {
      // optional .google.protobuf.testing.FieldMaskWrapper single_mask = 1;
      case 1: {
        if (tag == 10) {
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessageNoVirtual(
               input, mutable_single_mask()));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(18)) goto parse_multiple_mask;
        break;
      }

      // optional .google.protobuf.testing.FieldMaskWrapper multiple_mask = 2;
      case 2: {
        if (tag == 18) {
         parse_multiple_mask:
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessageNoVirtual(
               input, mutable_multiple_mask()));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(26)) goto parse_snake_camel;
        break;
      }

      // optional .google.protobuf.testing.FieldMaskWrapper snake_camel = 3;
      case 3: {
        if (tag == 26) {
         parse_snake_camel:
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessageNoVirtual(
               input, mutable_snake_camel()));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(34)) goto parse_empty_field;
        break;
      }

      // optional .google.protobuf.testing.FieldMaskWrapper empty_field = 4;
      case 4: {
        if (tag == 34) {
         parse_empty_field:
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessageNoVirtual(
               input, mutable_empty_field()));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(42)) goto parse_apiary_format1;
        break;
      }

      // optional .google.protobuf.testing.FieldMaskWrapper apiary_format1 = 5;
      case 5: {
        if (tag == 42) {
         parse_apiary_format1:
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessageNoVirtual(
               input, mutable_apiary_format1()));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(50)) goto parse_apiary_format2;
        break;
      }

      // optional .google.protobuf.testing.FieldMaskWrapper apiary_format2 = 6;
      case 6: {
        if (tag == 50) {
         parse_apiary_format2:
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessageNoVirtual(
               input, mutable_apiary_format2()));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(58)) goto parse_apiary_format3;
        break;
      }

      // optional .google.protobuf.testing.FieldMaskWrapper apiary_format3 = 7;
      case 7: {
        if (tag == 58) {
         parse_apiary_format3:
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessageNoVirtual(
               input, mutable_apiary_format3()));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(66)) goto parse_map_key1;
        break;
      }

      // optional .google.protobuf.testing.FieldMaskWrapper map_key1 = 8;
      case 8: {
        if (tag == 66) {
         parse_map_key1:
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessageNoVirtual(
               input, mutable_map_key1()));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(74)) goto parse_map_key2;
        break;
      }

      // optional .google.protobuf.testing.FieldMaskWrapper map_key2 = 9;
      case 9: {
        if (tag == 74) {
         parse_map_key2:
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessageNoVirtual(
               input, mutable_map_key2()));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(82)) goto parse_map_key3;
        break;
      }

      // optional .google.protobuf.testing.FieldMaskWrapper map_key3 = 10;
      case 10: {
        if (tag == 82) {
         parse_map_key3:
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessageNoVirtual(
               input, mutable_map_key3()));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(90)) goto parse_map_key4;
        break;
      }

      // optional .google.protobuf.testing.FieldMaskWrapper map_key4 = 11;
      case 11: {
        if (tag == 90) {
         parse_map_key4:
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessageNoVirtual(
               input, mutable_map_key4()));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(98)) goto parse_map_key5;
        break;
      }

      // optional .google.protobuf.testing.FieldMaskWrapper map_key5 = 12;
      case 12: {
        if (tag == 98) {
         parse_map_key5:
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessageNoVirtual(
               input, mutable_map_key5()));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectAtEnd()) goto success;
        break;
      }

      default: {
      handle_unusual:
        if (tag == 0 ||
            ::google::protobuf::internal::WireFormatLite::GetTagWireType(tag) ==
            ::google::protobuf::internal::WireFormatLite::WIRETYPE_END_GROUP) {
          goto success;
        }
        DO_(::google::protobuf::internal::WireFormatLite::SkipField(input, tag));
        break;
      }
    }
  }
success:
  // @@protoc_insertion_point(parse_success:google.protobuf.testing.FieldMaskTestCases)
  return true;
failure:
  // @@protoc_insertion_point(parse_failure:google.protobuf.testing.FieldMaskTestCases)
  return false;
#undef DO_
}

void FieldMaskTestCases::SerializeWithCachedSizes(
    ::google::protobuf::io::CodedOutputStream* output) const {
  // @@protoc_insertion_point(serialize_start:google.protobuf.testing.FieldMaskTestCases)
  // optional .google.protobuf.testing.FieldMaskWrapper single_mask = 1;
  if (this->has_single_mask()) {
    ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
      1, *this->single_mask_, output);
  }

  // optional .google.protobuf.testing.FieldMaskWrapper multiple_mask = 2;
  if (this->has_multiple_mask()) {
    ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
      2, *this->multiple_mask_, output);
  }

  // optional .google.protobuf.testing.FieldMaskWrapper snake_camel = 3;
  if (this->has_snake_camel()) {
    ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
      3, *this->snake_camel_, output);
  }

  // optional .google.protobuf.testing.FieldMaskWrapper empty_field = 4;
  if (this->has_empty_field()) {
    ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
      4, *this->empty_field_, output);
  }

  // optional .google.protobuf.testing.FieldMaskWrapper apiary_format1 = 5;
  if (this->has_apiary_format1()) {
    ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
      5, *this->apiary_format1_, output);
  }

  // optional .google.protobuf.testing.FieldMaskWrapper apiary_format2 = 6;
  if (this->has_apiary_format2()) {
    ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
      6, *this->apiary_format2_, output);
  }

  // optional .google.protobuf.testing.FieldMaskWrapper apiary_format3 = 7;
  if (this->has_apiary_format3()) {
    ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
      7, *this->apiary_format3_, output);
  }

  // optional .google.protobuf.testing.FieldMaskWrapper map_key1 = 8;
  if (this->has_map_key1()) {
    ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
      8, *this->map_key1_, output);
  }

  // optional .google.protobuf.testing.FieldMaskWrapper map_key2 = 9;
  if (this->has_map_key2()) {
    ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
      9, *this->map_key2_, output);
  }

  // optional .google.protobuf.testing.FieldMaskWrapper map_key3 = 10;
  if (this->has_map_key3()) {
    ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
      10, *this->map_key3_, output);
  }

  // optional .google.protobuf.testing.FieldMaskWrapper map_key4 = 11;
  if (this->has_map_key4()) {
    ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
      11, *this->map_key4_, output);
  }

  // optional .google.protobuf.testing.FieldMaskWrapper map_key5 = 12;
  if (this->has_map_key5()) {
    ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
      12, *this->map_key5_, output);
  }

  // @@protoc_insertion_point(serialize_end:google.protobuf.testing.FieldMaskTestCases)
}

::google::protobuf::uint8* FieldMaskTestCases::InternalSerializeWithCachedSizesToArray(
    bool deterministic, ::google::protobuf::uint8* target) const {
  (void)deterministic; // Unused
  // @@protoc_insertion_point(serialize_to_array_start:google.protobuf.testing.FieldMaskTestCases)
  // optional .google.protobuf.testing.FieldMaskWrapper single_mask = 1;
  if (this->has_single_mask()) {
    target = ::google::protobuf::internal::WireFormatLite::
      InternalWriteMessageNoVirtualToArray(
        1, *this->single_mask_, false, target);
  }

  // optional .google.protobuf.testing.FieldMaskWrapper multiple_mask = 2;
  if (this->has_multiple_mask()) {
    target = ::google::protobuf::internal::WireFormatLite::
      InternalWriteMessageNoVirtualToArray(
        2, *this->multiple_mask_, false, target);
  }

  // optional .google.protobuf.testing.FieldMaskWrapper snake_camel = 3;
  if (this->has_snake_camel()) {
    target = ::google::protobuf::internal::WireFormatLite::
      InternalWriteMessageNoVirtualToArray(
        3, *this->snake_camel_, false, target);
  }

  // optional .google.protobuf.testing.FieldMaskWrapper empty_field = 4;
  if (this->has_empty_field()) {
    target = ::google::protobuf::internal::WireFormatLite::
      InternalWriteMessageNoVirtualToArray(
        4, *this->empty_field_, false, target);
  }

  // optional .google.protobuf.testing.FieldMaskWrapper apiary_format1 = 5;
  if (this->has_apiary_format1()) {
    target = ::google::protobuf::internal::WireFormatLite::
      InternalWriteMessageNoVirtualToArray(
        5, *this->apiary_format1_, false, target);
  }

  // optional .google.protobuf.testing.FieldMaskWrapper apiary_format2 = 6;
  if (this->has_apiary_format2()) {
    target = ::google::protobuf::internal::WireFormatLite::
      InternalWriteMessageNoVirtualToArray(
        6, *this->apiary_format2_, false, target);
  }

  // optional .google.protobuf.testing.FieldMaskWrapper apiary_format3 = 7;
  if (this->has_apiary_format3()) {
    target = ::google::protobuf::internal::WireFormatLite::
      InternalWriteMessageNoVirtualToArray(
        7, *this->apiary_format3_, false, target);
  }

  // optional .google.protobuf.testing.FieldMaskWrapper map_key1 = 8;
  if (this->has_map_key1()) {
    target = ::google::protobuf::internal::WireFormatLite::
      InternalWriteMessageNoVirtualToArray(
        8, *this->map_key1_, false, target);
  }

  // optional .google.protobuf.testing.FieldMaskWrapper map_key2 = 9;
  if (this->has_map_key2()) {
    target = ::google::protobuf::internal::WireFormatLite::
      InternalWriteMessageNoVirtualToArray(
        9, *this->map_key2_, false, target);
  }

  // optional .google.protobuf.testing.FieldMaskWrapper map_key3 = 10;
  if (this->has_map_key3()) {
    target = ::google::protobuf::internal::WireFormatLite::
      InternalWriteMessageNoVirtualToArray(
        10, *this->map_key3_, false, target);
  }

  // optional .google.protobuf.testing.FieldMaskWrapper map_key4 = 11;
  if (this->has_map_key4()) {
    target = ::google::protobuf::internal::WireFormatLite::
      InternalWriteMessageNoVirtualToArray(
        11, *this->map_key4_, false, target);
  }

  // optional .google.protobuf.testing.FieldMaskWrapper map_key5 = 12;
  if (this->has_map_key5()) {
    target = ::google::protobuf::internal::WireFormatLite::
      InternalWriteMessageNoVirtualToArray(
        12, *this->map_key5_, false, target);
  }

  // @@protoc_insertion_point(serialize_to_array_end:google.protobuf.testing.FieldMaskTestCases)
  return target;
}

size_t FieldMaskTestCases::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:google.protobuf.testing.FieldMaskTestCases)
  size_t total_size = 0;

  // optional .google.protobuf.testing.FieldMaskWrapper single_mask = 1;
  if (this->has_single_mask()) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::MessageSizeNoVirtual(
        *this->single_mask_);
  }

  // optional .google.protobuf.testing.FieldMaskWrapper multiple_mask = 2;
  if (this->has_multiple_mask()) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::MessageSizeNoVirtual(
        *this->multiple_mask_);
  }

  // optional .google.protobuf.testing.FieldMaskWrapper snake_camel = 3;
  if (this->has_snake_camel()) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::MessageSizeNoVirtual(
        *this->snake_camel_);
  }

  // optional .google.protobuf.testing.FieldMaskWrapper empty_field = 4;
  if (this->has_empty_field()) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::MessageSizeNoVirtual(
        *this->empty_field_);
  }

  // optional .google.protobuf.testing.FieldMaskWrapper apiary_format1 = 5;
  if (this->has_apiary_format1()) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::MessageSizeNoVirtual(
        *this->apiary_format1_);
  }

  // optional .google.protobuf.testing.FieldMaskWrapper apiary_format2 = 6;
  if (this->has_apiary_format2()) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::MessageSizeNoVirtual(
        *this->apiary_format2_);
  }

  // optional .google.protobuf.testing.FieldMaskWrapper apiary_format3 = 7;
  if (this->has_apiary_format3()) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::MessageSizeNoVirtual(
        *this->apiary_format3_);
  }

  // optional .google.protobuf.testing.FieldMaskWrapper map_key1 = 8;
  if (this->has_map_key1()) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::MessageSizeNoVirtual(
        *this->map_key1_);
  }

  // optional .google.protobuf.testing.FieldMaskWrapper map_key2 = 9;
  if (this->has_map_key2()) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::MessageSizeNoVirtual(
        *this->map_key2_);
  }

  // optional .google.protobuf.testing.FieldMaskWrapper map_key3 = 10;
  if (this->has_map_key3()) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::MessageSizeNoVirtual(
        *this->map_key3_);
  }

  // optional .google.protobuf.testing.FieldMaskWrapper map_key4 = 11;
  if (this->has_map_key4()) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::MessageSizeNoVirtual(
        *this->map_key4_);
  }

  // optional .google.protobuf.testing.FieldMaskWrapper map_key5 = 12;
  if (this->has_map_key5()) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::MessageSizeNoVirtual(
        *this->map_key5_);
  }

  int cached_size = ::google::protobuf::internal::ToCachedSize(total_size);
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = cached_size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
  return total_size;
}

void FieldMaskTestCases::MergeFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_merge_from_start:google.protobuf.testing.FieldMaskTestCases)
  if (GOOGLE_PREDICT_FALSE(&from == this)) MergeFromFail(__LINE__);
  const FieldMaskTestCases* source =
      ::google::protobuf::internal::DynamicCastToGenerated<const FieldMaskTestCases>(
          &from);
  if (source == NULL) {
  // @@protoc_insertion_point(generalized_merge_from_cast_fail:google.protobuf.testing.FieldMaskTestCases)
    ::google::protobuf::internal::ReflectionOps::Merge(from, this);
  } else {
  // @@protoc_insertion_point(generalized_merge_from_cast_success:google.protobuf.testing.FieldMaskTestCases)
    UnsafeMergeFrom(*source);
  }
}

void FieldMaskTestCases::MergeFrom(const FieldMaskTestCases& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:google.protobuf.testing.FieldMaskTestCases)
  if (GOOGLE_PREDICT_TRUE(&from != this)) {
    UnsafeMergeFrom(from);
  } else {
    MergeFromFail(__LINE__);
  }
}

void FieldMaskTestCases::UnsafeMergeFrom(const FieldMaskTestCases& from) {
  GOOGLE_DCHECK(&from != this);
  if (from.has_single_mask()) {
    mutable_single_mask()->::google::protobuf::testing::FieldMaskWrapper::MergeFrom(from.single_mask());
  }
  if (from.has_multiple_mask()) {
    mutable_multiple_mask()->::google::protobuf::testing::FieldMaskWrapper::MergeFrom(from.multiple_mask());
  }
  if (from.has_snake_camel()) {
    mutable_snake_camel()->::google::protobuf::testing::FieldMaskWrapper::MergeFrom(from.snake_camel());
  }
  if (from.has_empty_field()) {
    mutable_empty_field()->::google::protobuf::testing::FieldMaskWrapper::MergeFrom(from.empty_field());
  }
  if (from.has_apiary_format1()) {
    mutable_apiary_format1()->::google::protobuf::testing::FieldMaskWrapper::MergeFrom(from.apiary_format1());
  }
  if (from.has_apiary_format2()) {
    mutable_apiary_format2()->::google::protobuf::testing::FieldMaskWrapper::MergeFrom(from.apiary_format2());
  }
  if (from.has_apiary_format3()) {
    mutable_apiary_format3()->::google::protobuf::testing::FieldMaskWrapper::MergeFrom(from.apiary_format3());
  }
  if (from.has_map_key1()) {
    mutable_map_key1()->::google::protobuf::testing::FieldMaskWrapper::MergeFrom(from.map_key1());
  }
  if (from.has_map_key2()) {
    mutable_map_key2()->::google::protobuf::testing::FieldMaskWrapper::MergeFrom(from.map_key2());
  }
  if (from.has_map_key3()) {
    mutable_map_key3()->::google::protobuf::testing::FieldMaskWrapper::MergeFrom(from.map_key3());
  }
  if (from.has_map_key4()) {
    mutable_map_key4()->::google::protobuf::testing::FieldMaskWrapper::MergeFrom(from.map_key4());
  }
  if (from.has_map_key5()) {
    mutable_map_key5()->::google::protobuf::testing::FieldMaskWrapper::MergeFrom(from.map_key5());
  }
}

void FieldMaskTestCases::CopyFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_copy_from_start:google.protobuf.testing.FieldMaskTestCases)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

void FieldMaskTestCases::CopyFrom(const FieldMaskTestCases& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:google.protobuf.testing.FieldMaskTestCases)
  if (&from == this) return;
  Clear();
  UnsafeMergeFrom(from);
}

bool FieldMaskTestCases::IsInitialized() const {

  return true;
}

void FieldMaskTestCases::Swap(FieldMaskTestCases* other) {
  if (other == this) return;
  InternalSwap(other);
}
void FieldMaskTestCases::InternalSwap(FieldMaskTestCases* other) {
  std::swap(single_mask_, other->single_mask_);
  std::swap(multiple_mask_, other->multiple_mask_);
  std::swap(snake_camel_, other->snake_camel_);
  std::swap(empty_field_, other->empty_field_);
  std::swap(apiary_format1_, other->apiary_format1_);
  std::swap(apiary_format2_, other->apiary_format2_);
  std::swap(apiary_format3_, other->apiary_format3_);
  std::swap(map_key1_, other->map_key1_);
  std::swap(map_key2_, other->map_key2_);
  std::swap(map_key3_, other->map_key3_);
  std::swap(map_key4_, other->map_key4_);
  std::swap(map_key5_, other->map_key5_);
  _internal_metadata_.Swap(&other->_internal_metadata_);
  std::swap(_cached_size_, other->_cached_size_);
}

::google::protobuf::Metadata FieldMaskTestCases::GetMetadata() const {
  protobuf_AssignDescriptorsOnce();
  ::google::protobuf::Metadata metadata;
  metadata.descriptor = FieldMaskTestCases_descriptor_;
  metadata.reflection = FieldMaskTestCases_reflection_;
  return metadata;
}

#if PROTOBUF_INLINE_NOT_IN_HEADERS
// FieldMaskTestCases

// optional .google.protobuf.testing.FieldMaskWrapper single_mask = 1;
bool FieldMaskTestCases::has_single_mask() const {
  return this != internal_default_instance() && single_mask_ != NULL;
}
void FieldMaskTestCases::clear_single_mask() {
  if (GetArenaNoVirtual() == NULL && single_mask_ != NULL) delete single_mask_;
  single_mask_ = NULL;
}
const ::google::protobuf::testing::FieldMaskWrapper& FieldMaskTestCases::single_mask() const {
  // @@protoc_insertion_point(field_get:google.protobuf.testing.FieldMaskTestCases.single_mask)
  return single_mask_ != NULL ? *single_mask_
                         : *::google::protobuf::testing::FieldMaskWrapper::internal_default_instance();
}
::google::protobuf::testing::FieldMaskWrapper* FieldMaskTestCases::mutable_single_mask() {
  
  if (single_mask_ == NULL) {
    single_mask_ = new ::google::protobuf::testing::FieldMaskWrapper;
  }
  // @@protoc_insertion_point(field_mutable:google.protobuf.testing.FieldMaskTestCases.single_mask)
  return single_mask_;
}
::google::protobuf::testing::FieldMaskWrapper* FieldMaskTestCases::release_single_mask() {
  // @@protoc_insertion_point(field_release:google.protobuf.testing.FieldMaskTestCases.single_mask)
  
  ::google::protobuf::testing::FieldMaskWrapper* temp = single_mask_;
  single_mask_ = NULL;
  return temp;
}
void FieldMaskTestCases::set_allocated_single_mask(::google::protobuf::testing::FieldMaskWrapper* single_mask) {
  delete single_mask_;
  single_mask_ = single_mask;
  if (single_mask) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_set_allocated:google.protobuf.testing.FieldMaskTestCases.single_mask)
}

// optional .google.protobuf.testing.FieldMaskWrapper multiple_mask = 2;
bool FieldMaskTestCases::has_multiple_mask() const {
  return this != internal_default_instance() && multiple_mask_ != NULL;
}
void FieldMaskTestCases::clear_multiple_mask() {
  if (GetArenaNoVirtual() == NULL && multiple_mask_ != NULL) delete multiple_mask_;
  multiple_mask_ = NULL;
}
const ::google::protobuf::testing::FieldMaskWrapper& FieldMaskTestCases::multiple_mask() const {
  // @@protoc_insertion_point(field_get:google.protobuf.testing.FieldMaskTestCases.multiple_mask)
  return multiple_mask_ != NULL ? *multiple_mask_
                         : *::google::protobuf::testing::FieldMaskWrapper::internal_default_instance();
}
::google::protobuf::testing::FieldMaskWrapper* FieldMaskTestCases::mutable_multiple_mask() {
  
  if (multiple_mask_ == NULL) {
    multiple_mask_ = new ::google::protobuf::testing::FieldMaskWrapper;
  }
  // @@protoc_insertion_point(field_mutable:google.protobuf.testing.FieldMaskTestCases.multiple_mask)
  return multiple_mask_;
}
::google::protobuf::testing::FieldMaskWrapper* FieldMaskTestCases::release_multiple_mask() {
  // @@protoc_insertion_point(field_release:google.protobuf.testing.FieldMaskTestCases.multiple_mask)
  
  ::google::protobuf::testing::FieldMaskWrapper* temp = multiple_mask_;
  multiple_mask_ = NULL;
  return temp;
}
void FieldMaskTestCases::set_allocated_multiple_mask(::google::protobuf::testing::FieldMaskWrapper* multiple_mask) {
  delete multiple_mask_;
  multiple_mask_ = multiple_mask;
  if (multiple_mask) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_set_allocated:google.protobuf.testing.FieldMaskTestCases.multiple_mask)
}

// optional .google.protobuf.testing.FieldMaskWrapper snake_camel = 3;
bool FieldMaskTestCases::has_snake_camel() const {
  return this != internal_default_instance() && snake_camel_ != NULL;
}
void FieldMaskTestCases::clear_snake_camel() {
  if (GetArenaNoVirtual() == NULL && snake_camel_ != NULL) delete snake_camel_;
  snake_camel_ = NULL;
}
const ::google::protobuf::testing::FieldMaskWrapper& FieldMaskTestCases::snake_camel() const {
  // @@protoc_insertion_point(field_get:google.protobuf.testing.FieldMaskTestCases.snake_camel)
  return snake_camel_ != NULL ? *snake_camel_
                         : *::google::protobuf::testing::FieldMaskWrapper::internal_default_instance();
}
::google::protobuf::testing::FieldMaskWrapper* FieldMaskTestCases::mutable_snake_camel() {
  
  if (snake_camel_ == NULL) {
    snake_camel_ = new ::google::protobuf::testing::FieldMaskWrapper;
  }
  // @@protoc_insertion_point(field_mutable:google.protobuf.testing.FieldMaskTestCases.snake_camel)
  return snake_camel_;
}
::google::protobuf::testing::FieldMaskWrapper* FieldMaskTestCases::release_snake_camel() {
  // @@protoc_insertion_point(field_release:google.protobuf.testing.FieldMaskTestCases.snake_camel)
  
  ::google::protobuf::testing::FieldMaskWrapper* temp = snake_camel_;
  snake_camel_ = NULL;
  return temp;
}
void FieldMaskTestCases::set_allocated_snake_camel(::google::protobuf::testing::FieldMaskWrapper* snake_camel) {
  delete snake_camel_;
  snake_camel_ = snake_camel;
  if (snake_camel) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_set_allocated:google.protobuf.testing.FieldMaskTestCases.snake_camel)
}

// optional .google.protobuf.testing.FieldMaskWrapper empty_field = 4;
bool FieldMaskTestCases::has_empty_field() const {
  return this != internal_default_instance() && empty_field_ != NULL;
}
void FieldMaskTestCases::clear_empty_field() {
  if (GetArenaNoVirtual() == NULL && empty_field_ != NULL) delete empty_field_;
  empty_field_ = NULL;
}
const ::google::protobuf::testing::FieldMaskWrapper& FieldMaskTestCases::empty_field() const {
  // @@protoc_insertion_point(field_get:google.protobuf.testing.FieldMaskTestCases.empty_field)
  return empty_field_ != NULL ? *empty_field_
                         : *::google::protobuf::testing::FieldMaskWrapper::internal_default_instance();
}
::google::protobuf::testing::FieldMaskWrapper* FieldMaskTestCases::mutable_empty_field() {
  
  if (empty_field_ == NULL) {
    empty_field_ = new ::google::protobuf::testing::FieldMaskWrapper;
  }
  // @@protoc_insertion_point(field_mutable:google.protobuf.testing.FieldMaskTestCases.empty_field)
  return empty_field_;
}
::google::protobuf::testing::FieldMaskWrapper* FieldMaskTestCases::release_empty_field() {
  // @@protoc_insertion_point(field_release:google.protobuf.testing.FieldMaskTestCases.empty_field)
  
  ::google::protobuf::testing::FieldMaskWrapper* temp = empty_field_;
  empty_field_ = NULL;
  return temp;
}
void FieldMaskTestCases::set_allocated_empty_field(::google::protobuf::testing::FieldMaskWrapper* empty_field) {
  delete empty_field_;
  empty_field_ = empty_field;
  if (empty_field) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_set_allocated:google.protobuf.testing.FieldMaskTestCases.empty_field)
}

// optional .google.protobuf.testing.FieldMaskWrapper apiary_format1 = 5;
bool FieldMaskTestCases::has_apiary_format1() const {
  return this != internal_default_instance() && apiary_format1_ != NULL;
}
void FieldMaskTestCases::clear_apiary_format1() {
  if (GetArenaNoVirtual() == NULL && apiary_format1_ != NULL) delete apiary_format1_;
  apiary_format1_ = NULL;
}
const ::google::protobuf::testing::FieldMaskWrapper& FieldMaskTestCases::apiary_format1() const {
  // @@protoc_insertion_point(field_get:google.protobuf.testing.FieldMaskTestCases.apiary_format1)
  return apiary_format1_ != NULL ? *apiary_format1_
                         : *::google::protobuf::testing::FieldMaskWrapper::internal_default_instance();
}
::google::protobuf::testing::FieldMaskWrapper* FieldMaskTestCases::mutable_apiary_format1() {
  
  if (apiary_format1_ == NULL) {
    apiary_format1_ = new ::google::protobuf::testing::FieldMaskWrapper;
  }
  // @@protoc_insertion_point(field_mutable:google.protobuf.testing.FieldMaskTestCases.apiary_format1)
  return apiary_format1_;
}
::google::protobuf::testing::FieldMaskWrapper* FieldMaskTestCases::release_apiary_format1() {
  // @@protoc_insertion_point(field_release:google.protobuf.testing.FieldMaskTestCases.apiary_format1)
  
  ::google::protobuf::testing::FieldMaskWrapper* temp = apiary_format1_;
  apiary_format1_ = NULL;
  return temp;
}
void FieldMaskTestCases::set_allocated_apiary_format1(::google::protobuf::testing::FieldMaskWrapper* apiary_format1) {
  delete apiary_format1_;
  apiary_format1_ = apiary_format1;
  if (apiary_format1) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_set_allocated:google.protobuf.testing.FieldMaskTestCases.apiary_format1)
}

// optional .google.protobuf.testing.FieldMaskWrapper apiary_format2 = 6;
bool FieldMaskTestCases::has_apiary_format2() const {
  return this != internal_default_instance() && apiary_format2_ != NULL;
}
void FieldMaskTestCases::clear_apiary_format2() {
  if (GetArenaNoVirtual() == NULL && apiary_format2_ != NULL) delete apiary_format2_;
  apiary_format2_ = NULL;
}
const ::google::protobuf::testing::FieldMaskWrapper& FieldMaskTestCases::apiary_format2() const {
  // @@protoc_insertion_point(field_get:google.protobuf.testing.FieldMaskTestCases.apiary_format2)
  return apiary_format2_ != NULL ? *apiary_format2_
                         : *::google::protobuf::testing::FieldMaskWrapper::internal_default_instance();
}
::google::protobuf::testing::FieldMaskWrapper* FieldMaskTestCases::mutable_apiary_format2() {
  
  if (apiary_format2_ == NULL) {
    apiary_format2_ = new ::google::protobuf::testing::FieldMaskWrapper;
  }
  // @@protoc_insertion_point(field_mutable:google.protobuf.testing.FieldMaskTestCases.apiary_format2)
  return apiary_format2_;
}
::google::protobuf::testing::FieldMaskWrapper* FieldMaskTestCases::release_apiary_format2() {
  // @@protoc_insertion_point(field_release:google.protobuf.testing.FieldMaskTestCases.apiary_format2)
  
  ::google::protobuf::testing::FieldMaskWrapper* temp = apiary_format2_;
  apiary_format2_ = NULL;
  return temp;
}
void FieldMaskTestCases::set_allocated_apiary_format2(::google::protobuf::testing::FieldMaskWrapper* apiary_format2) {
  delete apiary_format2_;
  apiary_format2_ = apiary_format2;
  if (apiary_format2) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_set_allocated:google.protobuf.testing.FieldMaskTestCases.apiary_format2)
}

// optional .google.protobuf.testing.FieldMaskWrapper apiary_format3 = 7;
bool FieldMaskTestCases::has_apiary_format3() const {
  return this != internal_default_instance() && apiary_format3_ != NULL;
}
void FieldMaskTestCases::clear_apiary_format3() {
  if (GetArenaNoVirtual() == NULL && apiary_format3_ != NULL) delete apiary_format3_;
  apiary_format3_ = NULL;
}
const ::google::protobuf::testing::FieldMaskWrapper& FieldMaskTestCases::apiary_format3() const {
  // @@protoc_insertion_point(field_get:google.protobuf.testing.FieldMaskTestCases.apiary_format3)
  return apiary_format3_ != NULL ? *apiary_format3_
                         : *::google::protobuf::testing::FieldMaskWrapper::internal_default_instance();
}
::google::protobuf::testing::FieldMaskWrapper* FieldMaskTestCases::mutable_apiary_format3() {
  
  if (apiary_format3_ == NULL) {
    apiary_format3_ = new ::google::protobuf::testing::FieldMaskWrapper;
  }
  // @@protoc_insertion_point(field_mutable:google.protobuf.testing.FieldMaskTestCases.apiary_format3)
  return apiary_format3_;
}
::google::protobuf::testing::FieldMaskWrapper* FieldMaskTestCases::release_apiary_format3() {
  // @@protoc_insertion_point(field_release:google.protobuf.testing.FieldMaskTestCases.apiary_format3)
  
  ::google::protobuf::testing::FieldMaskWrapper* temp = apiary_format3_;
  apiary_format3_ = NULL;
  return temp;
}
void FieldMaskTestCases::set_allocated_apiary_format3(::google::protobuf::testing::FieldMaskWrapper* apiary_format3) {
  delete apiary_format3_;
  apiary_format3_ = apiary_format3;
  if (apiary_format3) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_set_allocated:google.protobuf.testing.FieldMaskTestCases.apiary_format3)
}

// optional .google.protobuf.testing.FieldMaskWrapper map_key1 = 8;
bool FieldMaskTestCases::has_map_key1() const {
  return this != internal_default_instance() && map_key1_ != NULL;
}
void FieldMaskTestCases::clear_map_key1() {
  if (GetArenaNoVirtual() == NULL && map_key1_ != NULL) delete map_key1_;
  map_key1_ = NULL;
}
const ::google::protobuf::testing::FieldMaskWrapper& FieldMaskTestCases::map_key1() const {
  // @@protoc_insertion_point(field_get:google.protobuf.testing.FieldMaskTestCases.map_key1)
  return map_key1_ != NULL ? *map_key1_
                         : *::google::protobuf::testing::FieldMaskWrapper::internal_default_instance();
}
::google::protobuf::testing::FieldMaskWrapper* FieldMaskTestCases::mutable_map_key1() {
  
  if (map_key1_ == NULL) {
    map_key1_ = new ::google::protobuf::testing::FieldMaskWrapper;
  }
  // @@protoc_insertion_point(field_mutable:google.protobuf.testing.FieldMaskTestCases.map_key1)
  return map_key1_;
}
::google::protobuf::testing::FieldMaskWrapper* FieldMaskTestCases::release_map_key1() {
  // @@protoc_insertion_point(field_release:google.protobuf.testing.FieldMaskTestCases.map_key1)
  
  ::google::protobuf::testing::FieldMaskWrapper* temp = map_key1_;
  map_key1_ = NULL;
  return temp;
}
void FieldMaskTestCases::set_allocated_map_key1(::google::protobuf::testing::FieldMaskWrapper* map_key1) {
  delete map_key1_;
  map_key1_ = map_key1;
  if (map_key1) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_set_allocated:google.protobuf.testing.FieldMaskTestCases.map_key1)
}

// optional .google.protobuf.testing.FieldMaskWrapper map_key2 = 9;
bool FieldMaskTestCases::has_map_key2() const {
  return this != internal_default_instance() && map_key2_ != NULL;
}
void FieldMaskTestCases::clear_map_key2() {
  if (GetArenaNoVirtual() == NULL && map_key2_ != NULL) delete map_key2_;
  map_key2_ = NULL;
}
const ::google::protobuf::testing::FieldMaskWrapper& FieldMaskTestCases::map_key2() const {
  // @@protoc_insertion_point(field_get:google.protobuf.testing.FieldMaskTestCases.map_key2)
  return map_key2_ != NULL ? *map_key2_
                         : *::google::protobuf::testing::FieldMaskWrapper::internal_default_instance();
}
::google::protobuf::testing::FieldMaskWrapper* FieldMaskTestCases::mutable_map_key2() {
  
  if (map_key2_ == NULL) {
    map_key2_ = new ::google::protobuf::testing::FieldMaskWrapper;
  }
  // @@protoc_insertion_point(field_mutable:google.protobuf.testing.FieldMaskTestCases.map_key2)
  return map_key2_;
}
::google::protobuf::testing::FieldMaskWrapper* FieldMaskTestCases::release_map_key2() {
  // @@protoc_insertion_point(field_release:google.protobuf.testing.FieldMaskTestCases.map_key2)
  
  ::google::protobuf::testing::FieldMaskWrapper* temp = map_key2_;
  map_key2_ = NULL;
  return temp;
}
void FieldMaskTestCases::set_allocated_map_key2(::google::protobuf::testing::FieldMaskWrapper* map_key2) {
  delete map_key2_;
  map_key2_ = map_key2;
  if (map_key2) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_set_allocated:google.protobuf.testing.FieldMaskTestCases.map_key2)
}

// optional .google.protobuf.testing.FieldMaskWrapper map_key3 = 10;
bool FieldMaskTestCases::has_map_key3() const {
  return this != internal_default_instance() && map_key3_ != NULL;
}
void FieldMaskTestCases::clear_map_key3() {
  if (GetArenaNoVirtual() == NULL && map_key3_ != NULL) delete map_key3_;
  map_key3_ = NULL;
}
const ::google::protobuf::testing::FieldMaskWrapper& FieldMaskTestCases::map_key3() const {
  // @@protoc_insertion_point(field_get:google.protobuf.testing.FieldMaskTestCases.map_key3)
  return map_key3_ != NULL ? *map_key3_
                         : *::google::protobuf::testing::FieldMaskWrapper::internal_default_instance();
}
::google::protobuf::testing::FieldMaskWrapper* FieldMaskTestCases::mutable_map_key3() {
  
  if (map_key3_ == NULL) {
    map_key3_ = new ::google::protobuf::testing::FieldMaskWrapper;
  }
  // @@protoc_insertion_point(field_mutable:google.protobuf.testing.FieldMaskTestCases.map_key3)
  return map_key3_;
}
::google::protobuf::testing::FieldMaskWrapper* FieldMaskTestCases::release_map_key3() {
  // @@protoc_insertion_point(field_release:google.protobuf.testing.FieldMaskTestCases.map_key3)
  
  ::google::protobuf::testing::FieldMaskWrapper* temp = map_key3_;
  map_key3_ = NULL;
  return temp;
}
void FieldMaskTestCases::set_allocated_map_key3(::google::protobuf::testing::FieldMaskWrapper* map_key3) {
  delete map_key3_;
  map_key3_ = map_key3;
  if (map_key3) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_set_allocated:google.protobuf.testing.FieldMaskTestCases.map_key3)
}

// optional .google.protobuf.testing.FieldMaskWrapper map_key4 = 11;
bool FieldMaskTestCases::has_map_key4() const {
  return this != internal_default_instance() && map_key4_ != NULL;
}
void FieldMaskTestCases::clear_map_key4() {
  if (GetArenaNoVirtual() == NULL && map_key4_ != NULL) delete map_key4_;
  map_key4_ = NULL;
}
const ::google::protobuf::testing::FieldMaskWrapper& FieldMaskTestCases::map_key4() const {
  // @@protoc_insertion_point(field_get:google.protobuf.testing.FieldMaskTestCases.map_key4)
  return map_key4_ != NULL ? *map_key4_
                         : *::google::protobuf::testing::FieldMaskWrapper::internal_default_instance();
}
::google::protobuf::testing::FieldMaskWrapper* FieldMaskTestCases::mutable_map_key4() {
  
  if (map_key4_ == NULL) {
    map_key4_ = new ::google::protobuf::testing::FieldMaskWrapper;
  }
  // @@protoc_insertion_point(field_mutable:google.protobuf.testing.FieldMaskTestCases.map_key4)
  return map_key4_;
}
::google::protobuf::testing::FieldMaskWrapper* FieldMaskTestCases::release_map_key4() {
  // @@protoc_insertion_point(field_release:google.protobuf.testing.FieldMaskTestCases.map_key4)
  
  ::google::protobuf::testing::FieldMaskWrapper* temp = map_key4_;
  map_key4_ = NULL;
  return temp;
}
void FieldMaskTestCases::set_allocated_map_key4(::google::protobuf::testing::FieldMaskWrapper* map_key4) {
  delete map_key4_;
  map_key4_ = map_key4;
  if (map_key4) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_set_allocated:google.protobuf.testing.FieldMaskTestCases.map_key4)
}

// optional .google.protobuf.testing.FieldMaskWrapper map_key5 = 12;
bool FieldMaskTestCases::has_map_key5() const {
  return this != internal_default_instance() && map_key5_ != NULL;
}
void FieldMaskTestCases::clear_map_key5() {
  if (GetArenaNoVirtual() == NULL && map_key5_ != NULL) delete map_key5_;
  map_key5_ = NULL;
}
const ::google::protobuf::testing::FieldMaskWrapper& FieldMaskTestCases::map_key5() const {
  // @@protoc_insertion_point(field_get:google.protobuf.testing.FieldMaskTestCases.map_key5)
  return map_key5_ != NULL ? *map_key5_
                         : *::google::protobuf::testing::FieldMaskWrapper::internal_default_instance();
}
::google::protobuf::testing::FieldMaskWrapper* FieldMaskTestCases::mutable_map_key5() {
  
  if (map_key5_ == NULL) {
    map_key5_ = new ::google::protobuf::testing::FieldMaskWrapper;
  }
  // @@protoc_insertion_point(field_mutable:google.protobuf.testing.FieldMaskTestCases.map_key5)
  return map_key5_;
}
::google::protobuf::testing::FieldMaskWrapper* FieldMaskTestCases::release_map_key5() {
  // @@protoc_insertion_point(field_release:google.protobuf.testing.FieldMaskTestCases.map_key5)
  
  ::google::protobuf::testing::FieldMaskWrapper* temp = map_key5_;
  map_key5_ = NULL;
  return temp;
}
void FieldMaskTestCases::set_allocated_map_key5(::google::protobuf::testing::FieldMaskWrapper* map_key5) {
  delete map_key5_;
  map_key5_ = map_key5;
  if (map_key5) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_set_allocated:google.protobuf.testing.FieldMaskTestCases.map_key5)
}

inline const FieldMaskTestCases* FieldMaskTestCases::internal_default_instance() {
  return &FieldMaskTestCases_default_instance_.get();
}
#endif  // PROTOBUF_INLINE_NOT_IN_HEADERS

// ===================================================================

#if !defined(_MSC_VER) || _MSC_VER >= 1900
const int FieldMaskWrapper::kMaskFieldNumber;
#endif  // !defined(_MSC_VER) || _MSC_VER >= 1900

FieldMaskWrapper::FieldMaskWrapper()
  : ::google::protobuf::Message(), _internal_metadata_(NULL) {
  if (this != internal_default_instance()) protobuf_InitDefaults_google_2fprotobuf_2futil_2finternal_2ftestdata_2ffield_5fmask_2eproto();
  SharedCtor();
  // @@protoc_insertion_point(constructor:google.protobuf.testing.FieldMaskWrapper)
}

void FieldMaskWrapper::InitAsDefaultInstance() {
  mask_ = const_cast< ::google::protobuf::FieldMask*>(
      ::google::protobuf::FieldMask::internal_default_instance());
}

FieldMaskWrapper::FieldMaskWrapper(const FieldMaskWrapper& from)
  : ::google::protobuf::Message(),
    _internal_metadata_(NULL) {
  SharedCtor();
  UnsafeMergeFrom(from);
  // @@protoc_insertion_point(copy_constructor:google.protobuf.testing.FieldMaskWrapper)
}

void FieldMaskWrapper::SharedCtor() {
  mask_ = NULL;
  _cached_size_ = 0;
}

FieldMaskWrapper::~FieldMaskWrapper() {
  // @@protoc_insertion_point(destructor:google.protobuf.testing.FieldMaskWrapper)
  SharedDtor();
}

void FieldMaskWrapper::SharedDtor() {
  if (this != &FieldMaskWrapper_default_instance_.get()) {
    delete mask_;
  }
}

void FieldMaskWrapper::SetCachedSize(int size) const {
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
}
const ::google::protobuf::Descriptor* FieldMaskWrapper::descriptor() {
  protobuf_AssignDescriptorsOnce();
  return FieldMaskWrapper_descriptor_;
}

const FieldMaskWrapper& FieldMaskWrapper::default_instance() {
  protobuf_InitDefaults_google_2fprotobuf_2futil_2finternal_2ftestdata_2ffield_5fmask_2eproto();
  return *internal_default_instance();
}

::google::protobuf::internal::ExplicitlyConstructed<FieldMaskWrapper> FieldMaskWrapper_default_instance_;

FieldMaskWrapper* FieldMaskWrapper::New(::google::protobuf::Arena* arena) const {
  FieldMaskWrapper* n = new FieldMaskWrapper;
  if (arena != NULL) {
    arena->Own(n);
  }
  return n;
}

void FieldMaskWrapper::Clear() {
// @@protoc_insertion_point(message_clear_start:google.protobuf.testing.FieldMaskWrapper)
  if (GetArenaNoVirtual() == NULL && mask_ != NULL) delete mask_;
  mask_ = NULL;
}

bool FieldMaskWrapper::MergePartialFromCodedStream(
    ::google::protobuf::io::CodedInputStream* input) {
#define DO_(EXPRESSION) if (!GOOGLE_PREDICT_TRUE(EXPRESSION)) goto failure
  ::google::protobuf::uint32 tag;
  // @@protoc_insertion_point(parse_start:google.protobuf.testing.FieldMaskWrapper)
  for (;;) {
    ::std::pair< ::google::protobuf::uint32, bool> p = input->ReadTagWithCutoff(127);
    tag = p.first;
    if (!p.second) goto handle_unusual;
    switch (::google::protobuf::internal::WireFormatLite::GetTagFieldNumber(tag)) {
      // optional .google.protobuf.FieldMask mask = 1;
      case 1: {
        if (tag == 10) {
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessageNoVirtual(
               input, mutable_mask()));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectAtEnd()) goto success;
        break;
      }

      default: {
      handle_unusual:
        if (tag == 0 ||
            ::google::protobuf::internal::WireFormatLite::GetTagWireType(tag) ==
            ::google::protobuf::internal::WireFormatLite::WIRETYPE_END_GROUP) {
          goto success;
        }
        DO_(::google::protobuf::internal::WireFormatLite::SkipField(input, tag));
        break;
      }
    }
  }
success:
  // @@protoc_insertion_point(parse_success:google.protobuf.testing.FieldMaskWrapper)
  return true;
failure:
  // @@protoc_insertion_point(parse_failure:google.protobuf.testing.FieldMaskWrapper)
  return false;
#undef DO_
}

void FieldMaskWrapper::SerializeWithCachedSizes(
    ::google::protobuf::io::CodedOutputStream* output) const {
  // @@protoc_insertion_point(serialize_start:google.protobuf.testing.FieldMaskWrapper)
  // optional .google.protobuf.FieldMask mask = 1;
  if (this->has_mask()) {
    ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
      1, *this->mask_, output);
  }

  // @@protoc_insertion_point(serialize_end:google.protobuf.testing.FieldMaskWrapper)
}

::google::protobuf::uint8* FieldMaskWrapper::InternalSerializeWithCachedSizesToArray(
    bool deterministic, ::google::protobuf::uint8* target) const {
  (void)deterministic; // Unused
  // @@protoc_insertion_point(serialize_to_array_start:google.protobuf.testing.FieldMaskWrapper)
  // optional .google.protobuf.FieldMask mask = 1;
  if (this->has_mask()) {
    target = ::google::protobuf::internal::WireFormatLite::
      InternalWriteMessageNoVirtualToArray(
        1, *this->mask_, false, target);
  }

  // @@protoc_insertion_point(serialize_to_array_end:google.protobuf.testing.FieldMaskWrapper)
  return target;
}

size_t FieldMaskWrapper::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:google.protobuf.testing.FieldMaskWrapper)
  size_t total_size = 0;

  // optional .google.protobuf.FieldMask mask = 1;
  if (this->has_mask()) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::MessageSizeNoVirtual(
        *this->mask_);
  }

  int cached_size = ::google::protobuf::internal::ToCachedSize(total_size);
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = cached_size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
  return total_size;
}

void FieldMaskWrapper::MergeFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_merge_from_start:google.protobuf.testing.FieldMaskWrapper)
  if (GOOGLE_PREDICT_FALSE(&from == this)) MergeFromFail(__LINE__);
  const FieldMaskWrapper* source =
      ::google::protobuf::internal::DynamicCastToGenerated<const FieldMaskWrapper>(
          &from);
  if (source == NULL) {
  // @@protoc_insertion_point(generalized_merge_from_cast_fail:google.protobuf.testing.FieldMaskWrapper)
    ::google::protobuf::internal::ReflectionOps::Merge(from, this);
  } else {
  // @@protoc_insertion_point(generalized_merge_from_cast_success:google.protobuf.testing.FieldMaskWrapper)
    UnsafeMergeFrom(*source);
  }
}

void FieldMaskWrapper::MergeFrom(const FieldMaskWrapper& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:google.protobuf.testing.FieldMaskWrapper)
  if (GOOGLE_PREDICT_TRUE(&from != this)) {
    UnsafeMergeFrom(from);
  } else {
    MergeFromFail(__LINE__);
  }
}

void FieldMaskWrapper::UnsafeMergeFrom(const FieldMaskWrapper& from) {
  GOOGLE_DCHECK(&from != this);
  if (from.has_mask()) {
    mutable_mask()->::google::protobuf::FieldMask::MergeFrom(from.mask());
  }
}

void FieldMaskWrapper::CopyFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_copy_from_start:google.protobuf.testing.FieldMaskWrapper)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

void FieldMaskWrapper::CopyFrom(const FieldMaskWrapper& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:google.protobuf.testing.FieldMaskWrapper)
  if (&from == this) return;
  Clear();
  UnsafeMergeFrom(from);
}

bool FieldMaskWrapper::IsInitialized() const {

  return true;
}

void FieldMaskWrapper::Swap(FieldMaskWrapper* other) {
  if (other == this) return;
  InternalSwap(other);
}
void FieldMaskWrapper::InternalSwap(FieldMaskWrapper* other) {
  std::swap(mask_, other->mask_);
  _internal_metadata_.Swap(&other->_internal_metadata_);
  std::swap(_cached_size_, other->_cached_size_);
}

::google::protobuf::Metadata FieldMaskWrapper::GetMetadata() const {
  protobuf_AssignDescriptorsOnce();
  ::google::protobuf::Metadata metadata;
  metadata.descriptor = FieldMaskWrapper_descriptor_;
  metadata.reflection = FieldMaskWrapper_reflection_;
  return metadata;
}

#if PROTOBUF_INLINE_NOT_IN_HEADERS
// FieldMaskWrapper

// optional .google.protobuf.FieldMask mask = 1;
bool FieldMaskWrapper::has_mask() const {
  return this != internal_default_instance() && mask_ != NULL;
}
void FieldMaskWrapper::clear_mask() {
  if (GetArenaNoVirtual() == NULL && mask_ != NULL) delete mask_;
  mask_ = NULL;
}
const ::google::protobuf::FieldMask& FieldMaskWrapper::mask() const {
  // @@protoc_insertion_point(field_get:google.protobuf.testing.FieldMaskWrapper.mask)
  return mask_ != NULL ? *mask_
                         : *::google::protobuf::FieldMask::internal_default_instance();
}
::google::protobuf::FieldMask* FieldMaskWrapper::mutable_mask() {
  
  if (mask_ == NULL) {
    mask_ = new ::google::protobuf::FieldMask;
  }
  // @@protoc_insertion_point(field_mutable:google.protobuf.testing.FieldMaskWrapper.mask)
  return mask_;
}
::google::protobuf::FieldMask* FieldMaskWrapper::release_mask() {
  // @@protoc_insertion_point(field_release:google.protobuf.testing.FieldMaskWrapper.mask)
  
  ::google::protobuf::FieldMask* temp = mask_;
  mask_ = NULL;
  return temp;
}
void FieldMaskWrapper::set_allocated_mask(::google::protobuf::FieldMask* mask) {
  delete mask_;
  mask_ = mask;
  if (mask) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_set_allocated:google.protobuf.testing.FieldMaskWrapper.mask)
}

inline const FieldMaskWrapper* FieldMaskWrapper::internal_default_instance() {
  return &FieldMaskWrapper_default_instance_.get();
}
#endif  // PROTOBUF_INLINE_NOT_IN_HEADERS

// @@protoc_insertion_point(namespace_scope)

}  // namespace testing
}  // namespace protobuf
}  // namespace google

// @@protoc_insertion_point(global_scope)

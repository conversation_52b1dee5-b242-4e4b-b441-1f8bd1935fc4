// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: google/protobuf/util/message_differencer_unittest.proto

#define INTERNAL_SUPPRESS_PROTOBUF_FIELD_DEPRECATION
#include <google/protobuf/util/message_differencer_unittest.pb.h>

#include <algorithm>

#include <google/protobuf/stubs/common.h>
#include <google/protobuf/stubs/port.h>
#include <google/protobuf/stubs/once.h>
#include <google/protobuf/io/coded_stream.h>
#include <google/protobuf/wire_format_lite_inl.h>
#include <google/protobuf/descriptor.h>
#include <google/protobuf/generated_message_reflection.h>
#include <google/protobuf/reflection_ops.h>
#include <google/protobuf/wire_format.h>
// @@protoc_insertion_point(includes)

namespace protobuf_unittest {

namespace {

const ::google::protobuf::Descriptor* TestField_descriptor_ = NULL;
const ::google::protobuf::internal::GeneratedMessageReflection*
  TestField_reflection_ = NULL;
const ::google::protobuf::Descriptor* TestDiffMessage_descriptor_ = NULL;
const ::google::protobuf::internal::GeneratedMessageReflection*
  TestDiffMessage_reflection_ = NULL;
const ::google::protobuf::Descriptor* TestDiffMessage_Item_descriptor_ = NULL;
const ::google::protobuf::internal::GeneratedMessageReflection*
  TestDiffMessage_Item_reflection_ = NULL;

}  // namespace


void protobuf_AssignDesc_google_2fprotobuf_2futil_2fmessage_5fdifferencer_5funittest_2eproto() GOOGLE_ATTRIBUTE_COLD;
void protobuf_AssignDesc_google_2fprotobuf_2futil_2fmessage_5fdifferencer_5funittest_2eproto() {
  protobuf_AddDesc_google_2fprotobuf_2futil_2fmessage_5fdifferencer_5funittest_2eproto();
  const ::google::protobuf::FileDescriptor* file =
    ::google::protobuf::DescriptorPool::generated_pool()->FindFileByName(
      "google/protobuf/util/message_differencer_unittest.proto");
  GOOGLE_CHECK(file != NULL);
  TestField_descriptor_ = file->message_type(0);
  static const int TestField_offsets_[5] = {
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(TestField, a_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(TestField, b_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(TestField, c_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(TestField, rc_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(TestField, m_),
  };
  TestField_reflection_ =
    ::google::protobuf::internal::GeneratedMessageReflection::NewGeneratedMessageReflection(
      TestField_descriptor_,
      TestField::internal_default_instance(),
      TestField_offsets_,
      GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(TestField, _has_bits_),
      -1,
      -1,
      sizeof(TestField),
      GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(TestField, _internal_metadata_));
  TestDiffMessage_descriptor_ = file->message_type(1);
  static const int TestDiffMessage_offsets_[7] = {
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(TestDiffMessage, item_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(TestDiffMessage, v_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(TestDiffMessage, w_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(TestDiffMessage, m_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(TestDiffMessage, rv_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(TestDiffMessage, rw_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(TestDiffMessage, rm_),
  };
  TestDiffMessage_reflection_ =
    ::google::protobuf::internal::GeneratedMessageReflection::NewGeneratedMessageReflection(
      TestDiffMessage_descriptor_,
      TestDiffMessage::internal_default_instance(),
      TestDiffMessage_offsets_,
      GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(TestDiffMessage, _has_bits_),
      -1,
      GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(TestDiffMessage, _extensions_),
      sizeof(TestDiffMessage),
      GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(TestDiffMessage, _internal_metadata_));
  TestDiffMessage_Item_descriptor_ = TestDiffMessage_descriptor_->nested_type(0);
  static const int TestDiffMessage_Item_offsets_[6] = {
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(TestDiffMessage_Item, a_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(TestDiffMessage_Item, b_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(TestDiffMessage_Item, ra_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(TestDiffMessage_Item, rb_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(TestDiffMessage_Item, m_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(TestDiffMessage_Item, rm_),
  };
  TestDiffMessage_Item_reflection_ =
    ::google::protobuf::internal::GeneratedMessageReflection::NewGeneratedMessageReflection(
      TestDiffMessage_Item_descriptor_,
      TestDiffMessage_Item::internal_default_instance(),
      TestDiffMessage_Item_offsets_,
      GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(TestDiffMessage_Item, _has_bits_),
      -1,
      -1,
      sizeof(TestDiffMessage_Item),
      GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(TestDiffMessage_Item, _internal_metadata_));
}

namespace {

GOOGLE_PROTOBUF_DECLARE_ONCE(protobuf_AssignDescriptors_once_);
void protobuf_AssignDescriptorsOnce() {
  ::google::protobuf::GoogleOnceInit(&protobuf_AssignDescriptors_once_,
                 &protobuf_AssignDesc_google_2fprotobuf_2futil_2fmessage_5fdifferencer_5funittest_2eproto);
}

void protobuf_RegisterTypes(const ::std::string&) GOOGLE_ATTRIBUTE_COLD;
void protobuf_RegisterTypes(const ::std::string&) {
  protobuf_AssignDescriptorsOnce();
  ::google::protobuf::MessageFactory::InternalRegisterGeneratedMessage(
      TestField_descriptor_, TestField::internal_default_instance());
  ::google::protobuf::MessageFactory::InternalRegisterGeneratedMessage(
      TestDiffMessage_descriptor_, TestDiffMessage::internal_default_instance());
  ::google::protobuf::MessageFactory::InternalRegisterGeneratedMessage(
      TestDiffMessage_Item_descriptor_, TestDiffMessage_Item::internal_default_instance());
}

}  // namespace

void protobuf_ShutdownFile_google_2fprotobuf_2futil_2fmessage_5fdifferencer_5funittest_2eproto() {
  TestField_default_instance_.Shutdown();
  delete TestField_reflection_;
  TestDiffMessage_default_instance_.Shutdown();
  delete TestDiffMessage_reflection_;
  TestDiffMessage_Item_default_instance_.Shutdown();
  delete TestDiffMessage_Item_reflection_;
}

void protobuf_InitDefaults_google_2fprotobuf_2futil_2fmessage_5fdifferencer_5funittest_2eproto_impl() {
  GOOGLE_PROTOBUF_VERIFY_VERSION;

  TestField_default_instance_.DefaultConstruct();
  ::google::protobuf::internal::GetEmptyString();
  TestDiffMessage_default_instance_.DefaultConstruct();
  ::google::protobuf::internal::GetEmptyString();
  TestDiffMessage_Item_default_instance_.DefaultConstruct();
  TestField_default_instance_.get_mutable()->InitAsDefaultInstance();
  ::google::protobuf::internal::ExtensionSet::RegisterMessageExtension(
    ::protobuf_unittest::TestDiffMessage::internal_default_instance(),
    100, 11, false, false,
    ::protobuf_unittest::TestField::internal_default_instance());
  TestDiffMessage_default_instance_.get_mutable()->InitAsDefaultInstance();
  TestDiffMessage_Item_default_instance_.get_mutable()->InitAsDefaultInstance();
}

GOOGLE_PROTOBUF_DECLARE_ONCE(protobuf_InitDefaults_google_2fprotobuf_2futil_2fmessage_5fdifferencer_5funittest_2eproto_once_);
void protobuf_InitDefaults_google_2fprotobuf_2futil_2fmessage_5fdifferencer_5funittest_2eproto() {
  ::google::protobuf::GoogleOnceInit(&protobuf_InitDefaults_google_2fprotobuf_2futil_2fmessage_5fdifferencer_5funittest_2eproto_once_,
                 &protobuf_InitDefaults_google_2fprotobuf_2futil_2fmessage_5fdifferencer_5funittest_2eproto_impl);
}
void protobuf_AddDesc_google_2fprotobuf_2futil_2fmessage_5fdifferencer_5funittest_2eproto_impl() {
  GOOGLE_PROTOBUF_VERIFY_VERSION;

  protobuf_InitDefaults_google_2fprotobuf_2futil_2fmessage_5fdifferencer_5funittest_2eproto();
  ::google::protobuf::DescriptorPool::InternalAddGeneratedFile(
    "\n7google/protobuf/util/message_differenc"
    "er_unittest.proto\022\021protobuf_unittest\"\257\001\n"
    "\tTestField\022\t\n\001a\030\003 \001(\005\022\t\n\001b\030\004 \001(\005\022\t\n\001c\030\001 "
    "\001(\005\022\n\n\002rc\030\002 \003(\005\022\'\n\001m\030\005 \001(\0132\034.protobuf_un"
    "ittest.TestField2L\n\002tf\022\".protobuf_unitte"
    "st.TestDiffMessage\030d \001(\0132\034.protobuf_unit"
    "test.TestField\"\342\002\n\017TestDiffMessage\0225\n\004it"
    "em\030\001 \003(\n2\'.protobuf_unittest.TestDiffMes"
    "sage.Item\022\r\n\001v\030\r \001(\005B\002\030\001\022\t\n\001w\030\016 \001(\t\022\'\n\001m"
    "\030\017 \001(\0132\034.protobuf_unittest.TestField\022\n\n\002"
    "rv\030\013 \003(\005\022\n\n\002rw\030\n \003(\t\022,\n\002rm\030\014 \003(\0132\034.proto"
    "buf_unittest.TestFieldB\002\030\001\032\207\001\n\004Item\022\t\n\001a"
    "\030\002 \001(\005\022\t\n\001b\030\004 \001(\t\022\n\n\002ra\030\003 \003(\005\022\n\n\002rb\030\005 \003("
    "\t\022\'\n\001m\030\006 \001(\0132\034.protobuf_unittest.TestFie"
    "ld\022(\n\002rm\030\007 \003(\0132\034.protobuf_unittest.TestF"
    "ield*\005\010d\020\310\001B\002H\001", 615);
  ::google::protobuf::MessageFactory::InternalRegisterGeneratedFile(
    "google/protobuf/util/message_differencer_unittest.proto", &protobuf_RegisterTypes);
  ::google::protobuf::internal::OnShutdown(&protobuf_ShutdownFile_google_2fprotobuf_2futil_2fmessage_5fdifferencer_5funittest_2eproto);
}

GOOGLE_PROTOBUF_DECLARE_ONCE(protobuf_AddDesc_google_2fprotobuf_2futil_2fmessage_5fdifferencer_5funittest_2eproto_once_);
void protobuf_AddDesc_google_2fprotobuf_2futil_2fmessage_5fdifferencer_5funittest_2eproto() {
  ::google::protobuf::GoogleOnceInit(&protobuf_AddDesc_google_2fprotobuf_2futil_2fmessage_5fdifferencer_5funittest_2eproto_once_,
                 &protobuf_AddDesc_google_2fprotobuf_2futil_2fmessage_5fdifferencer_5funittest_2eproto_impl);
}
// Force AddDescriptors() to be called at static initialization time.
struct StaticDescriptorInitializer_google_2fprotobuf_2futil_2fmessage_5fdifferencer_5funittest_2eproto {
  StaticDescriptorInitializer_google_2fprotobuf_2futil_2fmessage_5fdifferencer_5funittest_2eproto() {
    protobuf_AddDesc_google_2fprotobuf_2futil_2fmessage_5fdifferencer_5funittest_2eproto();
  }
} static_descriptor_initializer_google_2fprotobuf_2futil_2fmessage_5fdifferencer_5funittest_2eproto_;

namespace {

static void MergeFromFail(int line) GOOGLE_ATTRIBUTE_COLD GOOGLE_ATTRIBUTE_NORETURN;
static void MergeFromFail(int line) {
  ::google::protobuf::internal::MergeFromFail(__FILE__, line);
}

}  // namespace


// ===================================================================

#if !defined(_MSC_VER) || _MSC_VER >= 1900
const int TestField::kAFieldNumber;
const int TestField::kBFieldNumber;
const int TestField::kCFieldNumber;
const int TestField::kRcFieldNumber;
const int TestField::kMFieldNumber;
#endif  // !defined(_MSC_VER) || _MSC_VER >= 1900

#if !defined(_MSC_VER) || _MSC_VER >= 1900
const int TestField::kTfFieldNumber;
#endif
::google::protobuf::internal::ExtensionIdentifier< ::protobuf_unittest::TestDiffMessage,
    ::google::protobuf::internal::MessageTypeTraits< ::protobuf_unittest::TestField >, 11, false >
  TestField::tf(kTfFieldNumber, *::protobuf_unittest::TestField::internal_default_instance());
TestField::TestField()
  : ::google::protobuf::Message(), _internal_metadata_(NULL) {
  if (this != internal_default_instance()) protobuf_InitDefaults_google_2fprotobuf_2futil_2fmessage_5fdifferencer_5funittest_2eproto();
  SharedCtor();
  // @@protoc_insertion_point(constructor:protobuf_unittest.TestField)
}

void TestField::InitAsDefaultInstance() {
  m_ = const_cast< ::protobuf_unittest::TestField*>(
      ::protobuf_unittest::TestField::internal_default_instance());
}

TestField::TestField(const TestField& from)
  : ::google::protobuf::Message(),
    _internal_metadata_(NULL) {
  SharedCtor();
  UnsafeMergeFrom(from);
  // @@protoc_insertion_point(copy_constructor:protobuf_unittest.TestField)
}

void TestField::SharedCtor() {
  _cached_size_ = 0;
  m_ = NULL;
  ::memset(&a_, 0, reinterpret_cast<char*>(&c_) -
    reinterpret_cast<char*>(&a_) + sizeof(c_));
}

TestField::~TestField() {
  // @@protoc_insertion_point(destructor:protobuf_unittest.TestField)
  SharedDtor();
}

void TestField::SharedDtor() {
  if (this != &TestField_default_instance_.get()) {
    delete m_;
  }
}

void TestField::SetCachedSize(int size) const {
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
}
const ::google::protobuf::Descriptor* TestField::descriptor() {
  protobuf_AssignDescriptorsOnce();
  return TestField_descriptor_;
}

const TestField& TestField::default_instance() {
  protobuf_InitDefaults_google_2fprotobuf_2futil_2fmessage_5fdifferencer_5funittest_2eproto();
  return *internal_default_instance();
}

::google::protobuf::internal::ExplicitlyConstructed<TestField> TestField_default_instance_;

TestField* TestField::New(::google::protobuf::Arena* arena) const {
  TestField* n = new TestField;
  if (arena != NULL) {
    arena->Own(n);
  }
  return n;
}

void TestField::Clear() {
// @@protoc_insertion_point(message_clear_start:protobuf_unittest.TestField)
#if defined(__clang__)
#define ZR_HELPER_(f) \
  _Pragma("clang diagnostic push") \
  _Pragma("clang diagnostic ignored \"-Winvalid-offsetof\"") \
  __builtin_offsetof(TestField, f) \
  _Pragma("clang diagnostic pop")
#else
#define ZR_HELPER_(f) reinterpret_cast<char*>(\
  &reinterpret_cast<TestField*>(16)->f)
#endif

#define ZR_(first, last) do {\
  ::memset(&(first), 0,\
           ZR_HELPER_(last) - ZR_HELPER_(first) + sizeof(last));\
} while (0)

  if (_has_bits_[0 / 32] & 23u) {
    ZR_(a_, c_);
    if (has_m()) {
      if (m_ != NULL) m_->::protobuf_unittest::TestField::Clear();
    }
  }

#undef ZR_HELPER_
#undef ZR_

  rc_.Clear();
  _has_bits_.Clear();
  if (_internal_metadata_.have_unknown_fields()) {
    mutable_unknown_fields()->Clear();
  }
}

bool TestField::MergePartialFromCodedStream(
    ::google::protobuf::io::CodedInputStream* input) {
#define DO_(EXPRESSION) if (!GOOGLE_PREDICT_TRUE(EXPRESSION)) goto failure
  ::google::protobuf::uint32 tag;
  // @@protoc_insertion_point(parse_start:protobuf_unittest.TestField)
  for (;;) {
    ::std::pair< ::google::protobuf::uint32, bool> p = input->ReadTagWithCutoff(127);
    tag = p.first;
    if (!p.second) goto handle_unusual;
    switch (::google::protobuf::internal::WireFormatLite::GetTagFieldNumber(tag)) {
      // optional int32 c = 1;
      case 1: {
        if (tag == 8) {
          set_has_c();
          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int32, ::google::protobuf::internal::WireFormatLite::TYPE_INT32>(
                 input, &c_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(16)) goto parse_rc;
        break;
      }

      // repeated int32 rc = 2;
      case 2: {
        if (tag == 16) {
         parse_rc:
          DO_((::google::protobuf::internal::WireFormatLite::ReadRepeatedPrimitive<
                   ::google::protobuf::int32, ::google::protobuf::internal::WireFormatLite::TYPE_INT32>(
                 1, 16, input, this->mutable_rc())));
        } else if (tag == 18) {
          DO_((::google::protobuf::internal::WireFormatLite::ReadPackedPrimitiveNoInline<
                   ::google::protobuf::int32, ::google::protobuf::internal::WireFormatLite::TYPE_INT32>(
                 input, this->mutable_rc())));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(16)) goto parse_rc;
        if (input->ExpectTag(24)) goto parse_a;
        break;
      }

      // optional int32 a = 3;
      case 3: {
        if (tag == 24) {
         parse_a:
          set_has_a();
          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int32, ::google::protobuf::internal::WireFormatLite::TYPE_INT32>(
                 input, &a_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(32)) goto parse_b;
        break;
      }

      // optional int32 b = 4;
      case 4: {
        if (tag == 32) {
         parse_b:
          set_has_b();
          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int32, ::google::protobuf::internal::WireFormatLite::TYPE_INT32>(
                 input, &b_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(42)) goto parse_m;
        break;
      }

      // optional .protobuf_unittest.TestField m = 5;
      case 5: {
        if (tag == 42) {
         parse_m:
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessageNoVirtual(
               input, mutable_m()));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectAtEnd()) goto success;
        break;
      }

      default: {
      handle_unusual:
        if (tag == 0 ||
            ::google::protobuf::internal::WireFormatLite::GetTagWireType(tag) ==
            ::google::protobuf::internal::WireFormatLite::WIRETYPE_END_GROUP) {
          goto success;
        }
        DO_(::google::protobuf::internal::WireFormat::SkipField(
              input, tag, mutable_unknown_fields()));
        break;
      }
    }
  }
success:
  // @@protoc_insertion_point(parse_success:protobuf_unittest.TestField)
  return true;
failure:
  // @@protoc_insertion_point(parse_failure:protobuf_unittest.TestField)
  return false;
#undef DO_
}

void TestField::SerializeWithCachedSizes(
    ::google::protobuf::io::CodedOutputStream* output) const {
  // @@protoc_insertion_point(serialize_start:protobuf_unittest.TestField)
  // optional int32 c = 1;
  if (has_c()) {
    ::google::protobuf::internal::WireFormatLite::WriteInt32(1, this->c(), output);
  }

  // repeated int32 rc = 2;
  for (int i = 0; i < this->rc_size(); i++) {
    ::google::protobuf::internal::WireFormatLite::WriteInt32(
      2, this->rc(i), output);
  }

  // optional int32 a = 3;
  if (has_a()) {
    ::google::protobuf::internal::WireFormatLite::WriteInt32(3, this->a(), output);
  }

  // optional int32 b = 4;
  if (has_b()) {
    ::google::protobuf::internal::WireFormatLite::WriteInt32(4, this->b(), output);
  }

  // optional .protobuf_unittest.TestField m = 5;
  if (has_m()) {
    ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
      5, *this->m_, output);
  }

  if (_internal_metadata_.have_unknown_fields()) {
    ::google::protobuf::internal::WireFormat::SerializeUnknownFields(
        unknown_fields(), output);
  }
  // @@protoc_insertion_point(serialize_end:protobuf_unittest.TestField)
}

::google::protobuf::uint8* TestField::InternalSerializeWithCachedSizesToArray(
    bool deterministic, ::google::protobuf::uint8* target) const {
  (void)deterministic; // Unused
  // @@protoc_insertion_point(serialize_to_array_start:protobuf_unittest.TestField)
  // optional int32 c = 1;
  if (has_c()) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt32ToArray(1, this->c(), target);
  }

  // repeated int32 rc = 2;
  for (int i = 0; i < this->rc_size(); i++) {
    target = ::google::protobuf::internal::WireFormatLite::
      WriteInt32ToArray(2, this->rc(i), target);
  }

  // optional int32 a = 3;
  if (has_a()) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt32ToArray(3, this->a(), target);
  }

  // optional int32 b = 4;
  if (has_b()) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt32ToArray(4, this->b(), target);
  }

  // optional .protobuf_unittest.TestField m = 5;
  if (has_m()) {
    target = ::google::protobuf::internal::WireFormatLite::
      InternalWriteMessageNoVirtualToArray(
        5, *this->m_, false, target);
  }

  if (_internal_metadata_.have_unknown_fields()) {
    target = ::google::protobuf::internal::WireFormat::SerializeUnknownFieldsToArray(
        unknown_fields(), target);
  }
  // @@protoc_insertion_point(serialize_to_array_end:protobuf_unittest.TestField)
  return target;
}

size_t TestField::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:protobuf_unittest.TestField)
  size_t total_size = 0;

  if (_has_bits_[0 / 32] & 23u) {
    // optional int32 a = 3;
    if (has_a()) {
      total_size += 1 +
        ::google::protobuf::internal::WireFormatLite::Int32Size(
          this->a());
    }

    // optional int32 b = 4;
    if (has_b()) {
      total_size += 1 +
        ::google::protobuf::internal::WireFormatLite::Int32Size(
          this->b());
    }

    // optional int32 c = 1;
    if (has_c()) {
      total_size += 1 +
        ::google::protobuf::internal::WireFormatLite::Int32Size(
          this->c());
    }

    // optional .protobuf_unittest.TestField m = 5;
    if (has_m()) {
      total_size += 1 +
        ::google::protobuf::internal::WireFormatLite::MessageSizeNoVirtual(
          *this->m_);
    }

  }
  // repeated int32 rc = 2;
  {
    size_t data_size = 0;
    unsigned int count = this->rc_size();
    for (unsigned int i = 0; i < count; i++) {
      data_size += ::google::protobuf::internal::WireFormatLite::
        Int32Size(this->rc(i));
    }
    total_size += 1 *
                  ::google::protobuf::internal::FromIntSize(this->rc_size());
    total_size += data_size;
  }

  if (_internal_metadata_.have_unknown_fields()) {
    total_size +=
      ::google::protobuf::internal::WireFormat::ComputeUnknownFieldsSize(
        unknown_fields());
  }
  int cached_size = ::google::protobuf::internal::ToCachedSize(total_size);
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = cached_size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
  return total_size;
}

void TestField::MergeFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_merge_from_start:protobuf_unittest.TestField)
  if (GOOGLE_PREDICT_FALSE(&from == this)) MergeFromFail(__LINE__);
  const TestField* source =
      ::google::protobuf::internal::DynamicCastToGenerated<const TestField>(
          &from);
  if (source == NULL) {
  // @@protoc_insertion_point(generalized_merge_from_cast_fail:protobuf_unittest.TestField)
    ::google::protobuf::internal::ReflectionOps::Merge(from, this);
  } else {
  // @@protoc_insertion_point(generalized_merge_from_cast_success:protobuf_unittest.TestField)
    UnsafeMergeFrom(*source);
  }
}

void TestField::MergeFrom(const TestField& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:protobuf_unittest.TestField)
  if (GOOGLE_PREDICT_TRUE(&from != this)) {
    UnsafeMergeFrom(from);
  } else {
    MergeFromFail(__LINE__);
  }
}

void TestField::UnsafeMergeFrom(const TestField& from) {
  GOOGLE_DCHECK(&from != this);
  rc_.UnsafeMergeFrom(from.rc_);
  if (from._has_bits_[0 / 32] & (0xffu << (0 % 32))) {
    if (from.has_a()) {
      set_a(from.a());
    }
    if (from.has_b()) {
      set_b(from.b());
    }
    if (from.has_c()) {
      set_c(from.c());
    }
    if (from.has_m()) {
      mutable_m()->::protobuf_unittest::TestField::MergeFrom(from.m());
    }
  }
  if (from._internal_metadata_.have_unknown_fields()) {
    ::google::protobuf::UnknownFieldSet::MergeToInternalMetdata(
      from.unknown_fields(), &_internal_metadata_);
  }
}

void TestField::CopyFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_copy_from_start:protobuf_unittest.TestField)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

void TestField::CopyFrom(const TestField& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:protobuf_unittest.TestField)
  if (&from == this) return;
  Clear();
  UnsafeMergeFrom(from);
}

bool TestField::IsInitialized() const {

  return true;
}

void TestField::Swap(TestField* other) {
  if (other == this) return;
  InternalSwap(other);
}
void TestField::InternalSwap(TestField* other) {
  std::swap(a_, other->a_);
  std::swap(b_, other->b_);
  std::swap(c_, other->c_);
  rc_.UnsafeArenaSwap(&other->rc_);
  std::swap(m_, other->m_);
  std::swap(_has_bits_[0], other->_has_bits_[0]);
  _internal_metadata_.Swap(&other->_internal_metadata_);
  std::swap(_cached_size_, other->_cached_size_);
}

::google::protobuf::Metadata TestField::GetMetadata() const {
  protobuf_AssignDescriptorsOnce();
  ::google::protobuf::Metadata metadata;
  metadata.descriptor = TestField_descriptor_;
  metadata.reflection = TestField_reflection_;
  return metadata;
}

#if PROTOBUF_INLINE_NOT_IN_HEADERS
// TestField

// optional int32 a = 3;
bool TestField::has_a() const {
  return (_has_bits_[0] & 0x00000001u) != 0;
}
void TestField::set_has_a() {
  _has_bits_[0] |= 0x00000001u;
}
void TestField::clear_has_a() {
  _has_bits_[0] &= ~0x00000001u;
}
void TestField::clear_a() {
  a_ = 0;
  clear_has_a();
}
::google::protobuf::int32 TestField::a() const {
  // @@protoc_insertion_point(field_get:protobuf_unittest.TestField.a)
  return a_;
}
void TestField::set_a(::google::protobuf::int32 value) {
  set_has_a();
  a_ = value;
  // @@protoc_insertion_point(field_set:protobuf_unittest.TestField.a)
}

// optional int32 b = 4;
bool TestField::has_b() const {
  return (_has_bits_[0] & 0x00000002u) != 0;
}
void TestField::set_has_b() {
  _has_bits_[0] |= 0x00000002u;
}
void TestField::clear_has_b() {
  _has_bits_[0] &= ~0x00000002u;
}
void TestField::clear_b() {
  b_ = 0;
  clear_has_b();
}
::google::protobuf::int32 TestField::b() const {
  // @@protoc_insertion_point(field_get:protobuf_unittest.TestField.b)
  return b_;
}
void TestField::set_b(::google::protobuf::int32 value) {
  set_has_b();
  b_ = value;
  // @@protoc_insertion_point(field_set:protobuf_unittest.TestField.b)
}

// optional int32 c = 1;
bool TestField::has_c() const {
  return (_has_bits_[0] & 0x00000004u) != 0;
}
void TestField::set_has_c() {
  _has_bits_[0] |= 0x00000004u;
}
void TestField::clear_has_c() {
  _has_bits_[0] &= ~0x00000004u;
}
void TestField::clear_c() {
  c_ = 0;
  clear_has_c();
}
::google::protobuf::int32 TestField::c() const {
  // @@protoc_insertion_point(field_get:protobuf_unittest.TestField.c)
  return c_;
}
void TestField::set_c(::google::protobuf::int32 value) {
  set_has_c();
  c_ = value;
  // @@protoc_insertion_point(field_set:protobuf_unittest.TestField.c)
}

// repeated int32 rc = 2;
int TestField::rc_size() const {
  return rc_.size();
}
void TestField::clear_rc() {
  rc_.Clear();
}
::google::protobuf::int32 TestField::rc(int index) const {
  // @@protoc_insertion_point(field_get:protobuf_unittest.TestField.rc)
  return rc_.Get(index);
}
void TestField::set_rc(int index, ::google::protobuf::int32 value) {
  rc_.Set(index, value);
  // @@protoc_insertion_point(field_set:protobuf_unittest.TestField.rc)
}
void TestField::add_rc(::google::protobuf::int32 value) {
  rc_.Add(value);
  // @@protoc_insertion_point(field_add:protobuf_unittest.TestField.rc)
}
const ::google::protobuf::RepeatedField< ::google::protobuf::int32 >&
TestField::rc() const {
  // @@protoc_insertion_point(field_list:protobuf_unittest.TestField.rc)
  return rc_;
}
::google::protobuf::RepeatedField< ::google::protobuf::int32 >*
TestField::mutable_rc() {
  // @@protoc_insertion_point(field_mutable_list:protobuf_unittest.TestField.rc)
  return &rc_;
}

// optional .protobuf_unittest.TestField m = 5;
bool TestField::has_m() const {
  return (_has_bits_[0] & 0x00000010u) != 0;
}
void TestField::set_has_m() {
  _has_bits_[0] |= 0x00000010u;
}
void TestField::clear_has_m() {
  _has_bits_[0] &= ~0x00000010u;
}
void TestField::clear_m() {
  if (m_ != NULL) m_->::protobuf_unittest::TestField::Clear();
  clear_has_m();
}
const ::protobuf_unittest::TestField& TestField::m() const {
  // @@protoc_insertion_point(field_get:protobuf_unittest.TestField.m)
  return m_ != NULL ? *m_
                         : *::protobuf_unittest::TestField::internal_default_instance();
}
::protobuf_unittest::TestField* TestField::mutable_m() {
  set_has_m();
  if (m_ == NULL) {
    m_ = new ::protobuf_unittest::TestField;
  }
  // @@protoc_insertion_point(field_mutable:protobuf_unittest.TestField.m)
  return m_;
}
::protobuf_unittest::TestField* TestField::release_m() {
  // @@protoc_insertion_point(field_release:protobuf_unittest.TestField.m)
  clear_has_m();
  ::protobuf_unittest::TestField* temp = m_;
  m_ = NULL;
  return temp;
}
void TestField::set_allocated_m(::protobuf_unittest::TestField* m) {
  delete m_;
  m_ = m;
  if (m) {
    set_has_m();
  } else {
    clear_has_m();
  }
  // @@protoc_insertion_point(field_set_allocated:protobuf_unittest.TestField.m)
}

inline const TestField* TestField::internal_default_instance() {
  return &TestField_default_instance_.get();
}
#endif  // PROTOBUF_INLINE_NOT_IN_HEADERS

// ===================================================================

#if !defined(_MSC_VER) || _MSC_VER >= 1900
const int TestDiffMessage_Item::kAFieldNumber;
const int TestDiffMessage_Item::kBFieldNumber;
const int TestDiffMessage_Item::kRaFieldNumber;
const int TestDiffMessage_Item::kRbFieldNumber;
const int TestDiffMessage_Item::kMFieldNumber;
const int TestDiffMessage_Item::kRmFieldNumber;
#endif  // !defined(_MSC_VER) || _MSC_VER >= 1900

TestDiffMessage_Item::TestDiffMessage_Item()
  : ::google::protobuf::Message(), _internal_metadata_(NULL) {
  if (this != internal_default_instance()) protobuf_InitDefaults_google_2fprotobuf_2futil_2fmessage_5fdifferencer_5funittest_2eproto();
  SharedCtor();
  // @@protoc_insertion_point(constructor:protobuf_unittest.TestDiffMessage.Item)
}

void TestDiffMessage_Item::InitAsDefaultInstance() {
  m_ = const_cast< ::protobuf_unittest::TestField*>(
      ::protobuf_unittest::TestField::internal_default_instance());
}

TestDiffMessage_Item::TestDiffMessage_Item(const TestDiffMessage_Item& from)
  : ::google::protobuf::Message(),
    _internal_metadata_(NULL) {
  SharedCtor();
  UnsafeMergeFrom(from);
  // @@protoc_insertion_point(copy_constructor:protobuf_unittest.TestDiffMessage.Item)
}

void TestDiffMessage_Item::SharedCtor() {
  _cached_size_ = 0;
  b_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  m_ = NULL;
  a_ = 0;
}

TestDiffMessage_Item::~TestDiffMessage_Item() {
  // @@protoc_insertion_point(destructor:protobuf_unittest.TestDiffMessage.Item)
  SharedDtor();
}

void TestDiffMessage_Item::SharedDtor() {
  b_.DestroyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  if (this != &TestDiffMessage_Item_default_instance_.get()) {
    delete m_;
  }
}

void TestDiffMessage_Item::SetCachedSize(int size) const {
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
}
const ::google::protobuf::Descriptor* TestDiffMessage_Item::descriptor() {
  protobuf_AssignDescriptorsOnce();
  return TestDiffMessage_Item_descriptor_;
}

const TestDiffMessage_Item& TestDiffMessage_Item::default_instance() {
  protobuf_InitDefaults_google_2fprotobuf_2futil_2fmessage_5fdifferencer_5funittest_2eproto();
  return *internal_default_instance();
}

::google::protobuf::internal::ExplicitlyConstructed<TestDiffMessage_Item> TestDiffMessage_Item_default_instance_;

TestDiffMessage_Item* TestDiffMessage_Item::New(::google::protobuf::Arena* arena) const {
  TestDiffMessage_Item* n = new TestDiffMessage_Item;
  if (arena != NULL) {
    arena->Own(n);
  }
  return n;
}

void TestDiffMessage_Item::Clear() {
// @@protoc_insertion_point(message_clear_start:protobuf_unittest.TestDiffMessage.Item)
  if (_has_bits_[0 / 32] & 19u) {
    a_ = 0;
    if (has_b()) {
      b_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
    }
    if (has_m()) {
      if (m_ != NULL) m_->::protobuf_unittest::TestField::Clear();
    }
  }
  ra_.Clear();
  rb_.Clear();
  rm_.Clear();
  _has_bits_.Clear();
  if (_internal_metadata_.have_unknown_fields()) {
    mutable_unknown_fields()->Clear();
  }
}

bool TestDiffMessage_Item::MergePartialFromCodedStream(
    ::google::protobuf::io::CodedInputStream* input) {
#define DO_(EXPRESSION) if (!GOOGLE_PREDICT_TRUE(EXPRESSION)) goto failure
  ::google::protobuf::uint32 tag;
  // @@protoc_insertion_point(parse_start:protobuf_unittest.TestDiffMessage.Item)
  for (;;) {
    ::std::pair< ::google::protobuf::uint32, bool> p = input->ReadTagWithCutoff(127);
    tag = p.first;
    if (!p.second) goto handle_unusual;
    switch (::google::protobuf::internal::WireFormatLite::GetTagFieldNumber(tag)) {
      // optional int32 a = 2;
      case 2: {
        if (tag == 16) {
          set_has_a();
          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int32, ::google::protobuf::internal::WireFormatLite::TYPE_INT32>(
                 input, &a_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(24)) goto parse_ra;
        break;
      }

      // repeated int32 ra = 3;
      case 3: {
        if (tag == 24) {
         parse_ra:
          DO_((::google::protobuf::internal::WireFormatLite::ReadRepeatedPrimitive<
                   ::google::protobuf::int32, ::google::protobuf::internal::WireFormatLite::TYPE_INT32>(
                 1, 24, input, this->mutable_ra())));
        } else if (tag == 26) {
          DO_((::google::protobuf::internal::WireFormatLite::ReadPackedPrimitiveNoInline<
                   ::google::protobuf::int32, ::google::protobuf::internal::WireFormatLite::TYPE_INT32>(
                 input, this->mutable_ra())));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(24)) goto parse_ra;
        if (input->ExpectTag(34)) goto parse_b;
        break;
      }

      // optional string b = 4;
      case 4: {
        if (tag == 34) {
         parse_b:
          DO_(::google::protobuf::internal::WireFormatLite::ReadString(
                input, this->mutable_b()));
          ::google::protobuf::internal::WireFormat::VerifyUTF8StringNamedField(
            this->b().data(), this->b().length(),
            ::google::protobuf::internal::WireFormat::PARSE,
            "protobuf_unittest.TestDiffMessage.Item.b");
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(42)) goto parse_rb;
        break;
      }

      // repeated string rb = 5;
      case 5: {
        if (tag == 42) {
         parse_rb:
          DO_(::google::protobuf::internal::WireFormatLite::ReadString(
                input, this->add_rb()));
          ::google::protobuf::internal::WireFormat::VerifyUTF8StringNamedField(
            this->rb(this->rb_size() - 1).data(),
            this->rb(this->rb_size() - 1).length(),
            ::google::protobuf::internal::WireFormat::PARSE,
            "protobuf_unittest.TestDiffMessage.Item.rb");
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(42)) goto parse_rb;
        if (input->ExpectTag(50)) goto parse_m;
        break;
      }

      // optional .protobuf_unittest.TestField m = 6;
      case 6: {
        if (tag == 50) {
         parse_m:
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessageNoVirtual(
               input, mutable_m()));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(58)) goto parse_rm;
        break;
      }

      // repeated .protobuf_unittest.TestField rm = 7;
      case 7: {
        if (tag == 58) {
         parse_rm:
          DO_(input->IncrementRecursionDepth());
         parse_loop_rm:
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessageNoVirtualNoRecursionDepth(
                input, add_rm()));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(58)) goto parse_loop_rm;
        input->UnsafeDecrementRecursionDepth();
        if (input->ExpectAtEnd()) goto success;
        break;
      }

      default: {
      handle_unusual:
        if (tag == 0 ||
            ::google::protobuf::internal::WireFormatLite::GetTagWireType(tag) ==
            ::google::protobuf::internal::WireFormatLite::WIRETYPE_END_GROUP) {
          goto success;
        }
        DO_(::google::protobuf::internal::WireFormat::SkipField(
              input, tag, mutable_unknown_fields()));
        break;
      }
    }
  }
success:
  // @@protoc_insertion_point(parse_success:protobuf_unittest.TestDiffMessage.Item)
  return true;
failure:
  // @@protoc_insertion_point(parse_failure:protobuf_unittest.TestDiffMessage.Item)
  return false;
#undef DO_
}

void TestDiffMessage_Item::SerializeWithCachedSizes(
    ::google::protobuf::io::CodedOutputStream* output) const {
  // @@protoc_insertion_point(serialize_start:protobuf_unittest.TestDiffMessage.Item)
  // optional int32 a = 2;
  if (has_a()) {
    ::google::protobuf::internal::WireFormatLite::WriteInt32(2, this->a(), output);
  }

  // repeated int32 ra = 3;
  for (int i = 0; i < this->ra_size(); i++) {
    ::google::protobuf::internal::WireFormatLite::WriteInt32(
      3, this->ra(i), output);
  }

  // optional string b = 4;
  if (has_b()) {
    ::google::protobuf::internal::WireFormat::VerifyUTF8StringNamedField(
      this->b().data(), this->b().length(),
      ::google::protobuf::internal::WireFormat::SERIALIZE,
      "protobuf_unittest.TestDiffMessage.Item.b");
    ::google::protobuf::internal::WireFormatLite::WriteStringMaybeAliased(
      4, this->b(), output);
  }

  // repeated string rb = 5;
  for (int i = 0; i < this->rb_size(); i++) {
    ::google::protobuf::internal::WireFormat::VerifyUTF8StringNamedField(
      this->rb(i).data(), this->rb(i).length(),
      ::google::protobuf::internal::WireFormat::SERIALIZE,
      "protobuf_unittest.TestDiffMessage.Item.rb");
    ::google::protobuf::internal::WireFormatLite::WriteString(
      5, this->rb(i), output);
  }

  // optional .protobuf_unittest.TestField m = 6;
  if (has_m()) {
    ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
      6, *this->m_, output);
  }

  // repeated .protobuf_unittest.TestField rm = 7;
  for (unsigned int i = 0, n = this->rm_size(); i < n; i++) {
    ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
      7, this->rm(i), output);
  }

  if (_internal_metadata_.have_unknown_fields()) {
    ::google::protobuf::internal::WireFormat::SerializeUnknownFields(
        unknown_fields(), output);
  }
  // @@protoc_insertion_point(serialize_end:protobuf_unittest.TestDiffMessage.Item)
}

::google::protobuf::uint8* TestDiffMessage_Item::InternalSerializeWithCachedSizesToArray(
    bool deterministic, ::google::protobuf::uint8* target) const {
  (void)deterministic; // Unused
  // @@protoc_insertion_point(serialize_to_array_start:protobuf_unittest.TestDiffMessage.Item)
  // optional int32 a = 2;
  if (has_a()) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt32ToArray(2, this->a(), target);
  }

  // repeated int32 ra = 3;
  for (int i = 0; i < this->ra_size(); i++) {
    target = ::google::protobuf::internal::WireFormatLite::
      WriteInt32ToArray(3, this->ra(i), target);
  }

  // optional string b = 4;
  if (has_b()) {
    ::google::protobuf::internal::WireFormat::VerifyUTF8StringNamedField(
      this->b().data(), this->b().length(),
      ::google::protobuf::internal::WireFormat::SERIALIZE,
      "protobuf_unittest.TestDiffMessage.Item.b");
    target =
      ::google::protobuf::internal::WireFormatLite::WriteStringToArray(
        4, this->b(), target);
  }

  // repeated string rb = 5;
  for (int i = 0; i < this->rb_size(); i++) {
    ::google::protobuf::internal::WireFormat::VerifyUTF8StringNamedField(
      this->rb(i).data(), this->rb(i).length(),
      ::google::protobuf::internal::WireFormat::SERIALIZE,
      "protobuf_unittest.TestDiffMessage.Item.rb");
    target = ::google::protobuf::internal::WireFormatLite::
      WriteStringToArray(5, this->rb(i), target);
  }

  // optional .protobuf_unittest.TestField m = 6;
  if (has_m()) {
    target = ::google::protobuf::internal::WireFormatLite::
      InternalWriteMessageNoVirtualToArray(
        6, *this->m_, false, target);
  }

  // repeated .protobuf_unittest.TestField rm = 7;
  for (unsigned int i = 0, n = this->rm_size(); i < n; i++) {
    target = ::google::protobuf::internal::WireFormatLite::
      InternalWriteMessageNoVirtualToArray(
        7, this->rm(i), false, target);
  }

  if (_internal_metadata_.have_unknown_fields()) {
    target = ::google::protobuf::internal::WireFormat::SerializeUnknownFieldsToArray(
        unknown_fields(), target);
  }
  // @@protoc_insertion_point(serialize_to_array_end:protobuf_unittest.TestDiffMessage.Item)
  return target;
}

size_t TestDiffMessage_Item::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:protobuf_unittest.TestDiffMessage.Item)
  size_t total_size = 0;

  if (_has_bits_[0 / 32] & 19u) {
    // optional int32 a = 2;
    if (has_a()) {
      total_size += 1 +
        ::google::protobuf::internal::WireFormatLite::Int32Size(
          this->a());
    }

    // optional string b = 4;
    if (has_b()) {
      total_size += 1 +
        ::google::protobuf::internal::WireFormatLite::StringSize(
          this->b());
    }

    // optional .protobuf_unittest.TestField m = 6;
    if (has_m()) {
      total_size += 1 +
        ::google::protobuf::internal::WireFormatLite::MessageSizeNoVirtual(
          *this->m_);
    }

  }
  // repeated int32 ra = 3;
  {
    size_t data_size = 0;
    unsigned int count = this->ra_size();
    for (unsigned int i = 0; i < count; i++) {
      data_size += ::google::protobuf::internal::WireFormatLite::
        Int32Size(this->ra(i));
    }
    total_size += 1 *
                  ::google::protobuf::internal::FromIntSize(this->ra_size());
    total_size += data_size;
  }

  // repeated string rb = 5;
  total_size += 1 *
      ::google::protobuf::internal::FromIntSize(this->rb_size());
  for (int i = 0; i < this->rb_size(); i++) {
    total_size += ::google::protobuf::internal::WireFormatLite::StringSize(
      this->rb(i));
  }

  // repeated .protobuf_unittest.TestField rm = 7;
  {
    unsigned int count = this->rm_size();
    total_size += 1UL * count;
    for (unsigned int i = 0; i < count; i++) {
      total_size +=
        ::google::protobuf::internal::WireFormatLite::MessageSizeNoVirtual(
          this->rm(i));
    }
  }

  if (_internal_metadata_.have_unknown_fields()) {
    total_size +=
      ::google::protobuf::internal::WireFormat::ComputeUnknownFieldsSize(
        unknown_fields());
  }
  int cached_size = ::google::protobuf::internal::ToCachedSize(total_size);
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = cached_size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
  return total_size;
}

void TestDiffMessage_Item::MergeFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_merge_from_start:protobuf_unittest.TestDiffMessage.Item)
  if (GOOGLE_PREDICT_FALSE(&from == this)) MergeFromFail(__LINE__);
  const TestDiffMessage_Item* source =
      ::google::protobuf::internal::DynamicCastToGenerated<const TestDiffMessage_Item>(
          &from);
  if (source == NULL) {
  // @@protoc_insertion_point(generalized_merge_from_cast_fail:protobuf_unittest.TestDiffMessage.Item)
    ::google::protobuf::internal::ReflectionOps::Merge(from, this);
  } else {
  // @@protoc_insertion_point(generalized_merge_from_cast_success:protobuf_unittest.TestDiffMessage.Item)
    UnsafeMergeFrom(*source);
  }
}

void TestDiffMessage_Item::MergeFrom(const TestDiffMessage_Item& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:protobuf_unittest.TestDiffMessage.Item)
  if (GOOGLE_PREDICT_TRUE(&from != this)) {
    UnsafeMergeFrom(from);
  } else {
    MergeFromFail(__LINE__);
  }
}

void TestDiffMessage_Item::UnsafeMergeFrom(const TestDiffMessage_Item& from) {
  GOOGLE_DCHECK(&from != this);
  ra_.UnsafeMergeFrom(from.ra_);
  rb_.UnsafeMergeFrom(from.rb_);
  rm_.MergeFrom(from.rm_);
  if (from._has_bits_[0 / 32] & (0xffu << (0 % 32))) {
    if (from.has_a()) {
      set_a(from.a());
    }
    if (from.has_b()) {
      set_has_b();
      b_.AssignWithDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), from.b_);
    }
    if (from.has_m()) {
      mutable_m()->::protobuf_unittest::TestField::MergeFrom(from.m());
    }
  }
  if (from._internal_metadata_.have_unknown_fields()) {
    ::google::protobuf::UnknownFieldSet::MergeToInternalMetdata(
      from.unknown_fields(), &_internal_metadata_);
  }
}

void TestDiffMessage_Item::CopyFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_copy_from_start:protobuf_unittest.TestDiffMessage.Item)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

void TestDiffMessage_Item::CopyFrom(const TestDiffMessage_Item& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:protobuf_unittest.TestDiffMessage.Item)
  if (&from == this) return;
  Clear();
  UnsafeMergeFrom(from);
}

bool TestDiffMessage_Item::IsInitialized() const {

  return true;
}

void TestDiffMessage_Item::Swap(TestDiffMessage_Item* other) {
  if (other == this) return;
  InternalSwap(other);
}
void TestDiffMessage_Item::InternalSwap(TestDiffMessage_Item* other) {
  std::swap(a_, other->a_);
  b_.Swap(&other->b_);
  ra_.UnsafeArenaSwap(&other->ra_);
  rb_.UnsafeArenaSwap(&other->rb_);
  std::swap(m_, other->m_);
  rm_.UnsafeArenaSwap(&other->rm_);
  std::swap(_has_bits_[0], other->_has_bits_[0]);
  _internal_metadata_.Swap(&other->_internal_metadata_);
  std::swap(_cached_size_, other->_cached_size_);
}

::google::protobuf::Metadata TestDiffMessage_Item::GetMetadata() const {
  protobuf_AssignDescriptorsOnce();
  ::google::protobuf::Metadata metadata;
  metadata.descriptor = TestDiffMessage_Item_descriptor_;
  metadata.reflection = TestDiffMessage_Item_reflection_;
  return metadata;
}


// -------------------------------------------------------------------

#if !defined(_MSC_VER) || _MSC_VER >= 1900
const int TestDiffMessage::kItemFieldNumber;
const int TestDiffMessage::kVFieldNumber;
const int TestDiffMessage::kWFieldNumber;
const int TestDiffMessage::kMFieldNumber;
const int TestDiffMessage::kRvFieldNumber;
const int TestDiffMessage::kRwFieldNumber;
const int TestDiffMessage::kRmFieldNumber;
#endif  // !defined(_MSC_VER) || _MSC_VER >= 1900

TestDiffMessage::TestDiffMessage()
  : ::google::protobuf::Message(), _internal_metadata_(NULL) {
  if (this != internal_default_instance()) protobuf_InitDefaults_google_2fprotobuf_2futil_2fmessage_5fdifferencer_5funittest_2eproto();
  SharedCtor();
  // @@protoc_insertion_point(constructor:protobuf_unittest.TestDiffMessage)
}

void TestDiffMessage::InitAsDefaultInstance() {
  m_ = const_cast< ::protobuf_unittest::TestField*>(
      ::protobuf_unittest::TestField::internal_default_instance());
}

TestDiffMessage::TestDiffMessage(const TestDiffMessage& from)
  : ::google::protobuf::Message(),
    _internal_metadata_(NULL) {
  SharedCtor();
  UnsafeMergeFrom(from);
  // @@protoc_insertion_point(copy_constructor:protobuf_unittest.TestDiffMessage)
}

void TestDiffMessage::SharedCtor() {
  _cached_size_ = 0;
  w_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  m_ = NULL;
  v_ = 0;
}

TestDiffMessage::~TestDiffMessage() {
  // @@protoc_insertion_point(destructor:protobuf_unittest.TestDiffMessage)
  SharedDtor();
}

void TestDiffMessage::SharedDtor() {
  w_.DestroyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  if (this != &TestDiffMessage_default_instance_.get()) {
    delete m_;
  }
}

void TestDiffMessage::SetCachedSize(int size) const {
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
}
const ::google::protobuf::Descriptor* TestDiffMessage::descriptor() {
  protobuf_AssignDescriptorsOnce();
  return TestDiffMessage_descriptor_;
}

const TestDiffMessage& TestDiffMessage::default_instance() {
  protobuf_InitDefaults_google_2fprotobuf_2futil_2fmessage_5fdifferencer_5funittest_2eproto();
  return *internal_default_instance();
}

::google::protobuf::internal::ExplicitlyConstructed<TestDiffMessage> TestDiffMessage_default_instance_;

TestDiffMessage* TestDiffMessage::New(::google::protobuf::Arena* arena) const {
  TestDiffMessage* n = new TestDiffMessage;
  if (arena != NULL) {
    arena->Own(n);
  }
  return n;
}

void TestDiffMessage::Clear() {
// @@protoc_insertion_point(message_clear_start:protobuf_unittest.TestDiffMessage)
  _extensions_.Clear();
  if (_has_bits_[0 / 32] & 14u) {
    v_ = 0;
    if (has_w()) {
      w_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
    }
    if (has_m()) {
      if (m_ != NULL) m_->::protobuf_unittest::TestField::Clear();
    }
  }
  item_.Clear();
  rv_.Clear();
  rw_.Clear();
  rm_.Clear();
  _has_bits_.Clear();
  if (_internal_metadata_.have_unknown_fields()) {
    mutable_unknown_fields()->Clear();
  }
}

bool TestDiffMessage::MergePartialFromCodedStream(
    ::google::protobuf::io::CodedInputStream* input) {
#define DO_(EXPRESSION) if (!GOOGLE_PREDICT_TRUE(EXPRESSION)) goto failure
  ::google::protobuf::uint32 tag;
  // @@protoc_insertion_point(parse_start:protobuf_unittest.TestDiffMessage)
  for (;;) {
    ::std::pair< ::google::protobuf::uint32, bool> p = input->ReadTagWithCutoff(127);
    tag = p.first;
    if (!p.second) goto handle_unusual;
    switch (::google::protobuf::internal::WireFormatLite::GetTagFieldNumber(tag)) {
      // repeated group Item = 1 { ... };
      case 1: {
        if (tag == 11) {
          DO_(input->IncrementRecursionDepth());
         parse_loop_item:
          DO_(::google::protobuf::internal::WireFormatLite::ReadGroupNoVirtualNoRecursionDepth(
                1, input, add_item()));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(11)) goto parse_loop_item;
        input->UnsafeDecrementRecursionDepth();
        if (input->ExpectTag(82)) goto parse_rw;
        break;
      }

      // repeated string rw = 10;
      case 10: {
        if (tag == 82) {
         parse_rw:
          DO_(::google::protobuf::internal::WireFormatLite::ReadString(
                input, this->add_rw()));
          ::google::protobuf::internal::WireFormat::VerifyUTF8StringNamedField(
            this->rw(this->rw_size() - 1).data(),
            this->rw(this->rw_size() - 1).length(),
            ::google::protobuf::internal::WireFormat::PARSE,
            "protobuf_unittest.TestDiffMessage.rw");
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(82)) goto parse_rw;
        if (input->ExpectTag(88)) goto parse_rv;
        break;
      }

      // repeated int32 rv = 11;
      case 11: {
        if (tag == 88) {
         parse_rv:
          DO_((::google::protobuf::internal::WireFormatLite::ReadRepeatedPrimitive<
                   ::google::protobuf::int32, ::google::protobuf::internal::WireFormatLite::TYPE_INT32>(
                 1, 88, input, this->mutable_rv())));
        } else if (tag == 90) {
          DO_((::google::protobuf::internal::WireFormatLite::ReadPackedPrimitiveNoInline<
                   ::google::protobuf::int32, ::google::protobuf::internal::WireFormatLite::TYPE_INT32>(
                 input, this->mutable_rv())));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(88)) goto parse_rv;
        if (input->ExpectTag(98)) goto parse_rm;
        break;
      }

      // repeated .protobuf_unittest.TestField rm = 12 [deprecated = true];
      case 12: {
        if (tag == 98) {
         parse_rm:
          DO_(input->IncrementRecursionDepth());
         parse_loop_rm:
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessageNoVirtualNoRecursionDepth(
                input, add_rm()));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(98)) goto parse_loop_rm;
        input->UnsafeDecrementRecursionDepth();
        if (input->ExpectTag(104)) goto parse_v;
        break;
      }

      // optional int32 v = 13 [deprecated = true];
      case 13: {
        if (tag == 104) {
         parse_v:
          set_has_v();
          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int32, ::google::protobuf::internal::WireFormatLite::TYPE_INT32>(
                 input, &v_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(114)) goto parse_w;
        break;
      }

      // optional string w = 14;
      case 14: {
        if (tag == 114) {
         parse_w:
          DO_(::google::protobuf::internal::WireFormatLite::ReadString(
                input, this->mutable_w()));
          ::google::protobuf::internal::WireFormat::VerifyUTF8StringNamedField(
            this->w().data(), this->w().length(),
            ::google::protobuf::internal::WireFormat::PARSE,
            "protobuf_unittest.TestDiffMessage.w");
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(122)) goto parse_m;
        break;
      }

      // optional .protobuf_unittest.TestField m = 15;
      case 15: {
        if (tag == 122) {
         parse_m:
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessageNoVirtual(
               input, mutable_m()));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectAtEnd()) goto success;
        break;
      }

      default: {
      handle_unusual:
        if (tag == 0 ||
            ::google::protobuf::internal::WireFormatLite::GetTagWireType(tag) ==
            ::google::protobuf::internal::WireFormatLite::WIRETYPE_END_GROUP) {
          goto success;
        }
        if ((800u <= tag && tag < 1600u)) {
          DO_(_extensions_.ParseField(tag, input, internal_default_instance(),
                                      mutable_unknown_fields()));
          continue;
        }
        DO_(::google::protobuf::internal::WireFormat::SkipField(
              input, tag, mutable_unknown_fields()));
        break;
      }
    }
  }
success:
  // @@protoc_insertion_point(parse_success:protobuf_unittest.TestDiffMessage)
  return true;
failure:
  // @@protoc_insertion_point(parse_failure:protobuf_unittest.TestDiffMessage)
  return false;
#undef DO_
}

void TestDiffMessage::SerializeWithCachedSizes(
    ::google::protobuf::io::CodedOutputStream* output) const {
  // @@protoc_insertion_point(serialize_start:protobuf_unittest.TestDiffMessage)
  // repeated group Item = 1 { ... };
  for (unsigned int i = 0, n = this->item_size(); i < n; i++) {
    ::google::protobuf::internal::WireFormatLite::WriteGroupMaybeToArray(
      1, this->item(i), output);
  }

  // repeated string rw = 10;
  for (int i = 0; i < this->rw_size(); i++) {
    ::google::protobuf::internal::WireFormat::VerifyUTF8StringNamedField(
      this->rw(i).data(), this->rw(i).length(),
      ::google::protobuf::internal::WireFormat::SERIALIZE,
      "protobuf_unittest.TestDiffMessage.rw");
    ::google::protobuf::internal::WireFormatLite::WriteString(
      10, this->rw(i), output);
  }

  // repeated int32 rv = 11;
  for (int i = 0; i < this->rv_size(); i++) {
    ::google::protobuf::internal::WireFormatLite::WriteInt32(
      11, this->rv(i), output);
  }

  // repeated .protobuf_unittest.TestField rm = 12 [deprecated = true];
  for (unsigned int i = 0, n = this->rm_size(); i < n; i++) {
    ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
      12, this->rm(i), output);
  }

  // optional int32 v = 13 [deprecated = true];
  if (has_v()) {
    ::google::protobuf::internal::WireFormatLite::WriteInt32(13, this->v(), output);
  }

  // optional string w = 14;
  if (has_w()) {
    ::google::protobuf::internal::WireFormat::VerifyUTF8StringNamedField(
      this->w().data(), this->w().length(),
      ::google::protobuf::internal::WireFormat::SERIALIZE,
      "protobuf_unittest.TestDiffMessage.w");
    ::google::protobuf::internal::WireFormatLite::WriteStringMaybeAliased(
      14, this->w(), output);
  }

  // optional .protobuf_unittest.TestField m = 15;
  if (has_m()) {
    ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
      15, *this->m_, output);
  }

  // Extension range [100, 200)
  _extensions_.SerializeWithCachedSizes(
      100, 200, output);

  if (_internal_metadata_.have_unknown_fields()) {
    ::google::protobuf::internal::WireFormat::SerializeUnknownFields(
        unknown_fields(), output);
  }
  // @@protoc_insertion_point(serialize_end:protobuf_unittest.TestDiffMessage)
}

::google::protobuf::uint8* TestDiffMessage::InternalSerializeWithCachedSizesToArray(
    bool deterministic, ::google::protobuf::uint8* target) const {
  (void)deterministic; // Unused
  // @@protoc_insertion_point(serialize_to_array_start:protobuf_unittest.TestDiffMessage)
  // repeated group Item = 1 { ... };
  for (unsigned int i = 0, n = this->item_size(); i < n; i++) {
    target = ::google::protobuf::internal::WireFormatLite::
      InternalWriteGroupNoVirtualToArray(
        1, this->item(i), false, target);
  }

  // repeated string rw = 10;
  for (int i = 0; i < this->rw_size(); i++) {
    ::google::protobuf::internal::WireFormat::VerifyUTF8StringNamedField(
      this->rw(i).data(), this->rw(i).length(),
      ::google::protobuf::internal::WireFormat::SERIALIZE,
      "protobuf_unittest.TestDiffMessage.rw");
    target = ::google::protobuf::internal::WireFormatLite::
      WriteStringToArray(10, this->rw(i), target);
  }

  // repeated int32 rv = 11;
  for (int i = 0; i < this->rv_size(); i++) {
    target = ::google::protobuf::internal::WireFormatLite::
      WriteInt32ToArray(11, this->rv(i), target);
  }

  // repeated .protobuf_unittest.TestField rm = 12 [deprecated = true];
  for (unsigned int i = 0, n = this->rm_size(); i < n; i++) {
    target = ::google::protobuf::internal::WireFormatLite::
      InternalWriteMessageNoVirtualToArray(
        12, this->rm(i), false, target);
  }

  // optional int32 v = 13 [deprecated = true];
  if (has_v()) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt32ToArray(13, this->v(), target);
  }

  // optional string w = 14;
  if (has_w()) {
    ::google::protobuf::internal::WireFormat::VerifyUTF8StringNamedField(
      this->w().data(), this->w().length(),
      ::google::protobuf::internal::WireFormat::SERIALIZE,
      "protobuf_unittest.TestDiffMessage.w");
    target =
      ::google::protobuf::internal::WireFormatLite::WriteStringToArray(
        14, this->w(), target);
  }

  // optional .protobuf_unittest.TestField m = 15;
  if (has_m()) {
    target = ::google::protobuf::internal::WireFormatLite::
      InternalWriteMessageNoVirtualToArray(
        15, *this->m_, false, target);
  }

  // Extension range [100, 200)
  target = _extensions_.InternalSerializeWithCachedSizesToArray(
      100, 200, false, target);

  if (_internal_metadata_.have_unknown_fields()) {
    target = ::google::protobuf::internal::WireFormat::SerializeUnknownFieldsToArray(
        unknown_fields(), target);
  }
  // @@protoc_insertion_point(serialize_to_array_end:protobuf_unittest.TestDiffMessage)
  return target;
}

size_t TestDiffMessage::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:protobuf_unittest.TestDiffMessage)
  size_t total_size = 0;

  if (_has_bits_[1 / 32] & 14u) {
    // optional int32 v = 13 [deprecated = true];
    if (has_v()) {
      total_size += 1 +
        ::google::protobuf::internal::WireFormatLite::Int32Size(
          this->v());
    }

    // optional string w = 14;
    if (has_w()) {
      total_size += 1 +
        ::google::protobuf::internal::WireFormatLite::StringSize(
          this->w());
    }

    // optional .protobuf_unittest.TestField m = 15;
    if (has_m()) {
      total_size += 1 +
        ::google::protobuf::internal::WireFormatLite::MessageSizeNoVirtual(
          *this->m_);
    }

  }
  // repeated group Item = 1 { ... };
  {
    unsigned int count = this->item_size();
    total_size += 2UL * count;
    for (unsigned int i = 0; i < count; i++) {
      total_size +=
        ::google::protobuf::internal::WireFormatLite::GroupSizeNoVirtual(
          this->item(i));
    }
  }

  // repeated int32 rv = 11;
  {
    size_t data_size = 0;
    unsigned int count = this->rv_size();
    for (unsigned int i = 0; i < count; i++) {
      data_size += ::google::protobuf::internal::WireFormatLite::
        Int32Size(this->rv(i));
    }
    total_size += 1 *
                  ::google::protobuf::internal::FromIntSize(this->rv_size());
    total_size += data_size;
  }

  // repeated string rw = 10;
  total_size += 1 *
      ::google::protobuf::internal::FromIntSize(this->rw_size());
  for (int i = 0; i < this->rw_size(); i++) {
    total_size += ::google::protobuf::internal::WireFormatLite::StringSize(
      this->rw(i));
  }

  // repeated .protobuf_unittest.TestField rm = 12 [deprecated = true];
  {
    unsigned int count = this->rm_size();
    total_size += 1UL * count;
    for (unsigned int i = 0; i < count; i++) {
      total_size +=
        ::google::protobuf::internal::WireFormatLite::MessageSizeNoVirtual(
          this->rm(i));
    }
  }

  total_size += _extensions_.ByteSize();

  if (_internal_metadata_.have_unknown_fields()) {
    total_size +=
      ::google::protobuf::internal::WireFormat::ComputeUnknownFieldsSize(
        unknown_fields());
  }
  int cached_size = ::google::protobuf::internal::ToCachedSize(total_size);
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = cached_size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
  return total_size;
}

void TestDiffMessage::MergeFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_merge_from_start:protobuf_unittest.TestDiffMessage)
  if (GOOGLE_PREDICT_FALSE(&from == this)) MergeFromFail(__LINE__);
  const TestDiffMessage* source =
      ::google::protobuf::internal::DynamicCastToGenerated<const TestDiffMessage>(
          &from);
  if (source == NULL) {
  // @@protoc_insertion_point(generalized_merge_from_cast_fail:protobuf_unittest.TestDiffMessage)
    ::google::protobuf::internal::ReflectionOps::Merge(from, this);
  } else {
  // @@protoc_insertion_point(generalized_merge_from_cast_success:protobuf_unittest.TestDiffMessage)
    UnsafeMergeFrom(*source);
  }
}

void TestDiffMessage::MergeFrom(const TestDiffMessage& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:protobuf_unittest.TestDiffMessage)
  if (GOOGLE_PREDICT_TRUE(&from != this)) {
    UnsafeMergeFrom(from);
  } else {
    MergeFromFail(__LINE__);
  }
}

void TestDiffMessage::UnsafeMergeFrom(const TestDiffMessage& from) {
  GOOGLE_DCHECK(&from != this);
  item_.MergeFrom(from.item_);
  rv_.UnsafeMergeFrom(from.rv_);
  rw_.UnsafeMergeFrom(from.rw_);
  rm_.MergeFrom(from.rm_);
  if (from._has_bits_[1 / 32] & (0xffu << (1 % 32))) {
    if (from.has_v()) {
      set_v(from.v());
    }
    if (from.has_w()) {
      set_has_w();
      w_.AssignWithDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), from.w_);
    }
    if (from.has_m()) {
      mutable_m()->::protobuf_unittest::TestField::MergeFrom(from.m());
    }
  }
  _extensions_.MergeFrom(from._extensions_);
  if (from._internal_metadata_.have_unknown_fields()) {
    ::google::protobuf::UnknownFieldSet::MergeToInternalMetdata(
      from.unknown_fields(), &_internal_metadata_);
  }
}

void TestDiffMessage::CopyFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_copy_from_start:protobuf_unittest.TestDiffMessage)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

void TestDiffMessage::CopyFrom(const TestDiffMessage& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:protobuf_unittest.TestDiffMessage)
  if (&from == this) return;
  Clear();
  UnsafeMergeFrom(from);
}

bool TestDiffMessage::IsInitialized() const {


  if (!_extensions_.IsInitialized()) {
    return false;
  }
  return true;
}

void TestDiffMessage::Swap(TestDiffMessage* other) {
  if (other == this) return;
  InternalSwap(other);
}
void TestDiffMessage::InternalSwap(TestDiffMessage* other) {
  item_.UnsafeArenaSwap(&other->item_);
  std::swap(v_, other->v_);
  w_.Swap(&other->w_);
  std::swap(m_, other->m_);
  rv_.UnsafeArenaSwap(&other->rv_);
  rw_.UnsafeArenaSwap(&other->rw_);
  rm_.UnsafeArenaSwap(&other->rm_);
  std::swap(_has_bits_[0], other->_has_bits_[0]);
  _internal_metadata_.Swap(&other->_internal_metadata_);
  std::swap(_cached_size_, other->_cached_size_);
  _extensions_.Swap(&other->_extensions_);
}

::google::protobuf::Metadata TestDiffMessage::GetMetadata() const {
  protobuf_AssignDescriptorsOnce();
  ::google::protobuf::Metadata metadata;
  metadata.descriptor = TestDiffMessage_descriptor_;
  metadata.reflection = TestDiffMessage_reflection_;
  return metadata;
}

#if PROTOBUF_INLINE_NOT_IN_HEADERS
// TestDiffMessage_Item

// optional int32 a = 2;
bool TestDiffMessage_Item::has_a() const {
  return (_has_bits_[0] & 0x00000001u) != 0;
}
void TestDiffMessage_Item::set_has_a() {
  _has_bits_[0] |= 0x00000001u;
}
void TestDiffMessage_Item::clear_has_a() {
  _has_bits_[0] &= ~0x00000001u;
}
void TestDiffMessage_Item::clear_a() {
  a_ = 0;
  clear_has_a();
}
::google::protobuf::int32 TestDiffMessage_Item::a() const {
  // @@protoc_insertion_point(field_get:protobuf_unittest.TestDiffMessage.Item.a)
  return a_;
}
void TestDiffMessage_Item::set_a(::google::protobuf::int32 value) {
  set_has_a();
  a_ = value;
  // @@protoc_insertion_point(field_set:protobuf_unittest.TestDiffMessage.Item.a)
}

// optional string b = 4;
bool TestDiffMessage_Item::has_b() const {
  return (_has_bits_[0] & 0x00000002u) != 0;
}
void TestDiffMessage_Item::set_has_b() {
  _has_bits_[0] |= 0x00000002u;
}
void TestDiffMessage_Item::clear_has_b() {
  _has_bits_[0] &= ~0x00000002u;
}
void TestDiffMessage_Item::clear_b() {
  b_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  clear_has_b();
}
const ::std::string& TestDiffMessage_Item::b() const {
  // @@protoc_insertion_point(field_get:protobuf_unittest.TestDiffMessage.Item.b)
  return b_.GetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
void TestDiffMessage_Item::set_b(const ::std::string& value) {
  set_has_b();
  b_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:protobuf_unittest.TestDiffMessage.Item.b)
}
void TestDiffMessage_Item::set_b(const char* value) {
  set_has_b();
  b_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:protobuf_unittest.TestDiffMessage.Item.b)
}
void TestDiffMessage_Item::set_b(const char* value, size_t size) {
  set_has_b();
  b_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:protobuf_unittest.TestDiffMessage.Item.b)
}
::std::string* TestDiffMessage_Item::mutable_b() {
  set_has_b();
  // @@protoc_insertion_point(field_mutable:protobuf_unittest.TestDiffMessage.Item.b)
  return b_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
::std::string* TestDiffMessage_Item::release_b() {
  // @@protoc_insertion_point(field_release:protobuf_unittest.TestDiffMessage.Item.b)
  clear_has_b();
  return b_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
void TestDiffMessage_Item::set_allocated_b(::std::string* b) {
  if (b != NULL) {
    set_has_b();
  } else {
    clear_has_b();
  }
  b_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), b);
  // @@protoc_insertion_point(field_set_allocated:protobuf_unittest.TestDiffMessage.Item.b)
}

// repeated int32 ra = 3;
int TestDiffMessage_Item::ra_size() const {
  return ra_.size();
}
void TestDiffMessage_Item::clear_ra() {
  ra_.Clear();
}
::google::protobuf::int32 TestDiffMessage_Item::ra(int index) const {
  // @@protoc_insertion_point(field_get:protobuf_unittest.TestDiffMessage.Item.ra)
  return ra_.Get(index);
}
void TestDiffMessage_Item::set_ra(int index, ::google::protobuf::int32 value) {
  ra_.Set(index, value);
  // @@protoc_insertion_point(field_set:protobuf_unittest.TestDiffMessage.Item.ra)
}
void TestDiffMessage_Item::add_ra(::google::protobuf::int32 value) {
  ra_.Add(value);
  // @@protoc_insertion_point(field_add:protobuf_unittest.TestDiffMessage.Item.ra)
}
const ::google::protobuf::RepeatedField< ::google::protobuf::int32 >&
TestDiffMessage_Item::ra() const {
  // @@protoc_insertion_point(field_list:protobuf_unittest.TestDiffMessage.Item.ra)
  return ra_;
}
::google::protobuf::RepeatedField< ::google::protobuf::int32 >*
TestDiffMessage_Item::mutable_ra() {
  // @@protoc_insertion_point(field_mutable_list:protobuf_unittest.TestDiffMessage.Item.ra)
  return &ra_;
}

// repeated string rb = 5;
int TestDiffMessage_Item::rb_size() const {
  return rb_.size();
}
void TestDiffMessage_Item::clear_rb() {
  rb_.Clear();
}
const ::std::string& TestDiffMessage_Item::rb(int index) const {
  // @@protoc_insertion_point(field_get:protobuf_unittest.TestDiffMessage.Item.rb)
  return rb_.Get(index);
}
::std::string* TestDiffMessage_Item::mutable_rb(int index) {
  // @@protoc_insertion_point(field_mutable:protobuf_unittest.TestDiffMessage.Item.rb)
  return rb_.Mutable(index);
}
void TestDiffMessage_Item::set_rb(int index, const ::std::string& value) {
  // @@protoc_insertion_point(field_set:protobuf_unittest.TestDiffMessage.Item.rb)
  rb_.Mutable(index)->assign(value);
}
void TestDiffMessage_Item::set_rb(int index, const char* value) {
  rb_.Mutable(index)->assign(value);
  // @@protoc_insertion_point(field_set_char:protobuf_unittest.TestDiffMessage.Item.rb)
}
void TestDiffMessage_Item::set_rb(int index, const char* value, size_t size) {
  rb_.Mutable(index)->assign(
    reinterpret_cast<const char*>(value), size);
  // @@protoc_insertion_point(field_set_pointer:protobuf_unittest.TestDiffMessage.Item.rb)
}
::std::string* TestDiffMessage_Item::add_rb() {
  // @@protoc_insertion_point(field_add_mutable:protobuf_unittest.TestDiffMessage.Item.rb)
  return rb_.Add();
}
void TestDiffMessage_Item::add_rb(const ::std::string& value) {
  rb_.Add()->assign(value);
  // @@protoc_insertion_point(field_add:protobuf_unittest.TestDiffMessage.Item.rb)
}
void TestDiffMessage_Item::add_rb(const char* value) {
  rb_.Add()->assign(value);
  // @@protoc_insertion_point(field_add_char:protobuf_unittest.TestDiffMessage.Item.rb)
}
void TestDiffMessage_Item::add_rb(const char* value, size_t size) {
  rb_.Add()->assign(reinterpret_cast<const char*>(value), size);
  // @@protoc_insertion_point(field_add_pointer:protobuf_unittest.TestDiffMessage.Item.rb)
}
const ::google::protobuf::RepeatedPtrField< ::std::string>&
TestDiffMessage_Item::rb() const {
  // @@protoc_insertion_point(field_list:protobuf_unittest.TestDiffMessage.Item.rb)
  return rb_;
}
::google::protobuf::RepeatedPtrField< ::std::string>*
TestDiffMessage_Item::mutable_rb() {
  // @@protoc_insertion_point(field_mutable_list:protobuf_unittest.TestDiffMessage.Item.rb)
  return &rb_;
}

// optional .protobuf_unittest.TestField m = 6;
bool TestDiffMessage_Item::has_m() const {
  return (_has_bits_[0] & 0x00000010u) != 0;
}
void TestDiffMessage_Item::set_has_m() {
  _has_bits_[0] |= 0x00000010u;
}
void TestDiffMessage_Item::clear_has_m() {
  _has_bits_[0] &= ~0x00000010u;
}
void TestDiffMessage_Item::clear_m() {
  if (m_ != NULL) m_->::protobuf_unittest::TestField::Clear();
  clear_has_m();
}
const ::protobuf_unittest::TestField& TestDiffMessage_Item::m() const {
  // @@protoc_insertion_point(field_get:protobuf_unittest.TestDiffMessage.Item.m)
  return m_ != NULL ? *m_
                         : *::protobuf_unittest::TestField::internal_default_instance();
}
::protobuf_unittest::TestField* TestDiffMessage_Item::mutable_m() {
  set_has_m();
  if (m_ == NULL) {
    m_ = new ::protobuf_unittest::TestField;
  }
  // @@protoc_insertion_point(field_mutable:protobuf_unittest.TestDiffMessage.Item.m)
  return m_;
}
::protobuf_unittest::TestField* TestDiffMessage_Item::release_m() {
  // @@protoc_insertion_point(field_release:protobuf_unittest.TestDiffMessage.Item.m)
  clear_has_m();
  ::protobuf_unittest::TestField* temp = m_;
  m_ = NULL;
  return temp;
}
void TestDiffMessage_Item::set_allocated_m(::protobuf_unittest::TestField* m) {
  delete m_;
  m_ = m;
  if (m) {
    set_has_m();
  } else {
    clear_has_m();
  }
  // @@protoc_insertion_point(field_set_allocated:protobuf_unittest.TestDiffMessage.Item.m)
}

// repeated .protobuf_unittest.TestField rm = 7;
int TestDiffMessage_Item::rm_size() const {
  return rm_.size();
}
void TestDiffMessage_Item::clear_rm() {
  rm_.Clear();
}
const ::protobuf_unittest::TestField& TestDiffMessage_Item::rm(int index) const {
  // @@protoc_insertion_point(field_get:protobuf_unittest.TestDiffMessage.Item.rm)
  return rm_.Get(index);
}
::protobuf_unittest::TestField* TestDiffMessage_Item::mutable_rm(int index) {
  // @@protoc_insertion_point(field_mutable:protobuf_unittest.TestDiffMessage.Item.rm)
  return rm_.Mutable(index);
}
::protobuf_unittest::TestField* TestDiffMessage_Item::add_rm() {
  // @@protoc_insertion_point(field_add:protobuf_unittest.TestDiffMessage.Item.rm)
  return rm_.Add();
}
::google::protobuf::RepeatedPtrField< ::protobuf_unittest::TestField >*
TestDiffMessage_Item::mutable_rm() {
  // @@protoc_insertion_point(field_mutable_list:protobuf_unittest.TestDiffMessage.Item.rm)
  return &rm_;
}
const ::google::protobuf::RepeatedPtrField< ::protobuf_unittest::TestField >&
TestDiffMessage_Item::rm() const {
  // @@protoc_insertion_point(field_list:protobuf_unittest.TestDiffMessage.Item.rm)
  return rm_;
}

inline const TestDiffMessage_Item* TestDiffMessage_Item::internal_default_instance() {
  return &TestDiffMessage_Item_default_instance_.get();
}
// -------------------------------------------------------------------

// TestDiffMessage

// repeated group Item = 1 { ... };
int TestDiffMessage::item_size() const {
  return item_.size();
}
void TestDiffMessage::clear_item() {
  item_.Clear();
}
const ::protobuf_unittest::TestDiffMessage_Item& TestDiffMessage::item(int index) const {
  // @@protoc_insertion_point(field_get:protobuf_unittest.TestDiffMessage.item)
  return item_.Get(index);
}
::protobuf_unittest::TestDiffMessage_Item* TestDiffMessage::mutable_item(int index) {
  // @@protoc_insertion_point(field_mutable:protobuf_unittest.TestDiffMessage.item)
  return item_.Mutable(index);
}
::protobuf_unittest::TestDiffMessage_Item* TestDiffMessage::add_item() {
  // @@protoc_insertion_point(field_add:protobuf_unittest.TestDiffMessage.item)
  return item_.Add();
}
::google::protobuf::RepeatedPtrField< ::protobuf_unittest::TestDiffMessage_Item >*
TestDiffMessage::mutable_item() {
  // @@protoc_insertion_point(field_mutable_list:protobuf_unittest.TestDiffMessage.item)
  return &item_;
}
const ::google::protobuf::RepeatedPtrField< ::protobuf_unittest::TestDiffMessage_Item >&
TestDiffMessage::item() const {
  // @@protoc_insertion_point(field_list:protobuf_unittest.TestDiffMessage.item)
  return item_;
}

// optional int32 v = 13 [deprecated = true];
bool TestDiffMessage::has_v() const {
  return (_has_bits_[0] & 0x00000002u) != 0;
}
void TestDiffMessage::set_has_v() {
  _has_bits_[0] |= 0x00000002u;
}
void TestDiffMessage::clear_has_v() {
  _has_bits_[0] &= ~0x00000002u;
}
void TestDiffMessage::clear_v() {
  v_ = 0;
  clear_has_v();
}
::google::protobuf::int32 TestDiffMessage::v() const {
  // @@protoc_insertion_point(field_get:protobuf_unittest.TestDiffMessage.v)
  return v_;
}
void TestDiffMessage::set_v(::google::protobuf::int32 value) {
  set_has_v();
  v_ = value;
  // @@protoc_insertion_point(field_set:protobuf_unittest.TestDiffMessage.v)
}

// optional string w = 14;
bool TestDiffMessage::has_w() const {
  return (_has_bits_[0] & 0x00000004u) != 0;
}
void TestDiffMessage::set_has_w() {
  _has_bits_[0] |= 0x00000004u;
}
void TestDiffMessage::clear_has_w() {
  _has_bits_[0] &= ~0x00000004u;
}
void TestDiffMessage::clear_w() {
  w_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  clear_has_w();
}
const ::std::string& TestDiffMessage::w() const {
  // @@protoc_insertion_point(field_get:protobuf_unittest.TestDiffMessage.w)
  return w_.GetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
void TestDiffMessage::set_w(const ::std::string& value) {
  set_has_w();
  w_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:protobuf_unittest.TestDiffMessage.w)
}
void TestDiffMessage::set_w(const char* value) {
  set_has_w();
  w_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:protobuf_unittest.TestDiffMessage.w)
}
void TestDiffMessage::set_w(const char* value, size_t size) {
  set_has_w();
  w_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:protobuf_unittest.TestDiffMessage.w)
}
::std::string* TestDiffMessage::mutable_w() {
  set_has_w();
  // @@protoc_insertion_point(field_mutable:protobuf_unittest.TestDiffMessage.w)
  return w_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
::std::string* TestDiffMessage::release_w() {
  // @@protoc_insertion_point(field_release:protobuf_unittest.TestDiffMessage.w)
  clear_has_w();
  return w_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
void TestDiffMessage::set_allocated_w(::std::string* w) {
  if (w != NULL) {
    set_has_w();
  } else {
    clear_has_w();
  }
  w_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), w);
  // @@protoc_insertion_point(field_set_allocated:protobuf_unittest.TestDiffMessage.w)
}

// optional .protobuf_unittest.TestField m = 15;
bool TestDiffMessage::has_m() const {
  return (_has_bits_[0] & 0x00000008u) != 0;
}
void TestDiffMessage::set_has_m() {
  _has_bits_[0] |= 0x00000008u;
}
void TestDiffMessage::clear_has_m() {
  _has_bits_[0] &= ~0x00000008u;
}
void TestDiffMessage::clear_m() {
  if (m_ != NULL) m_->::protobuf_unittest::TestField::Clear();
  clear_has_m();
}
const ::protobuf_unittest::TestField& TestDiffMessage::m() const {
  // @@protoc_insertion_point(field_get:protobuf_unittest.TestDiffMessage.m)
  return m_ != NULL ? *m_
                         : *::protobuf_unittest::TestField::internal_default_instance();
}
::protobuf_unittest::TestField* TestDiffMessage::mutable_m() {
  set_has_m();
  if (m_ == NULL) {
    m_ = new ::protobuf_unittest::TestField;
  }
  // @@protoc_insertion_point(field_mutable:protobuf_unittest.TestDiffMessage.m)
  return m_;
}
::protobuf_unittest::TestField* TestDiffMessage::release_m() {
  // @@protoc_insertion_point(field_release:protobuf_unittest.TestDiffMessage.m)
  clear_has_m();
  ::protobuf_unittest::TestField* temp = m_;
  m_ = NULL;
  return temp;
}
void TestDiffMessage::set_allocated_m(::protobuf_unittest::TestField* m) {
  delete m_;
  m_ = m;
  if (m) {
    set_has_m();
  } else {
    clear_has_m();
  }
  // @@protoc_insertion_point(field_set_allocated:protobuf_unittest.TestDiffMessage.m)
}

// repeated int32 rv = 11;
int TestDiffMessage::rv_size() const {
  return rv_.size();
}
void TestDiffMessage::clear_rv() {
  rv_.Clear();
}
::google::protobuf::int32 TestDiffMessage::rv(int index) const {
  // @@protoc_insertion_point(field_get:protobuf_unittest.TestDiffMessage.rv)
  return rv_.Get(index);
}
void TestDiffMessage::set_rv(int index, ::google::protobuf::int32 value) {
  rv_.Set(index, value);
  // @@protoc_insertion_point(field_set:protobuf_unittest.TestDiffMessage.rv)
}
void TestDiffMessage::add_rv(::google::protobuf::int32 value) {
  rv_.Add(value);
  // @@protoc_insertion_point(field_add:protobuf_unittest.TestDiffMessage.rv)
}
const ::google::protobuf::RepeatedField< ::google::protobuf::int32 >&
TestDiffMessage::rv() const {
  // @@protoc_insertion_point(field_list:protobuf_unittest.TestDiffMessage.rv)
  return rv_;
}
::google::protobuf::RepeatedField< ::google::protobuf::int32 >*
TestDiffMessage::mutable_rv() {
  // @@protoc_insertion_point(field_mutable_list:protobuf_unittest.TestDiffMessage.rv)
  return &rv_;
}

// repeated string rw = 10;
int TestDiffMessage::rw_size() const {
  return rw_.size();
}
void TestDiffMessage::clear_rw() {
  rw_.Clear();
}
const ::std::string& TestDiffMessage::rw(int index) const {
  // @@protoc_insertion_point(field_get:protobuf_unittest.TestDiffMessage.rw)
  return rw_.Get(index);
}
::std::string* TestDiffMessage::mutable_rw(int index) {
  // @@protoc_insertion_point(field_mutable:protobuf_unittest.TestDiffMessage.rw)
  return rw_.Mutable(index);
}
void TestDiffMessage::set_rw(int index, const ::std::string& value) {
  // @@protoc_insertion_point(field_set:protobuf_unittest.TestDiffMessage.rw)
  rw_.Mutable(index)->assign(value);
}
void TestDiffMessage::set_rw(int index, const char* value) {
  rw_.Mutable(index)->assign(value);
  // @@protoc_insertion_point(field_set_char:protobuf_unittest.TestDiffMessage.rw)
}
void TestDiffMessage::set_rw(int index, const char* value, size_t size) {
  rw_.Mutable(index)->assign(
    reinterpret_cast<const char*>(value), size);
  // @@protoc_insertion_point(field_set_pointer:protobuf_unittest.TestDiffMessage.rw)
}
::std::string* TestDiffMessage::add_rw() {
  // @@protoc_insertion_point(field_add_mutable:protobuf_unittest.TestDiffMessage.rw)
  return rw_.Add();
}
void TestDiffMessage::add_rw(const ::std::string& value) {
  rw_.Add()->assign(value);
  // @@protoc_insertion_point(field_add:protobuf_unittest.TestDiffMessage.rw)
}
void TestDiffMessage::add_rw(const char* value) {
  rw_.Add()->assign(value);
  // @@protoc_insertion_point(field_add_char:protobuf_unittest.TestDiffMessage.rw)
}
void TestDiffMessage::add_rw(const char* value, size_t size) {
  rw_.Add()->assign(reinterpret_cast<const char*>(value), size);
  // @@protoc_insertion_point(field_add_pointer:protobuf_unittest.TestDiffMessage.rw)
}
const ::google::protobuf::RepeatedPtrField< ::std::string>&
TestDiffMessage::rw() const {
  // @@protoc_insertion_point(field_list:protobuf_unittest.TestDiffMessage.rw)
  return rw_;
}
::google::protobuf::RepeatedPtrField< ::std::string>*
TestDiffMessage::mutable_rw() {
  // @@protoc_insertion_point(field_mutable_list:protobuf_unittest.TestDiffMessage.rw)
  return &rw_;
}

// repeated .protobuf_unittest.TestField rm = 12 [deprecated = true];
int TestDiffMessage::rm_size() const {
  return rm_.size();
}
void TestDiffMessage::clear_rm() {
  rm_.Clear();
}
const ::protobuf_unittest::TestField& TestDiffMessage::rm(int index) const {
  // @@protoc_insertion_point(field_get:protobuf_unittest.TestDiffMessage.rm)
  return rm_.Get(index);
}
::protobuf_unittest::TestField* TestDiffMessage::mutable_rm(int index) {
  // @@protoc_insertion_point(field_mutable:protobuf_unittest.TestDiffMessage.rm)
  return rm_.Mutable(index);
}
::protobuf_unittest::TestField* TestDiffMessage::add_rm() {
  // @@protoc_insertion_point(field_add:protobuf_unittest.TestDiffMessage.rm)
  return rm_.Add();
}
::google::protobuf::RepeatedPtrField< ::protobuf_unittest::TestField >*
TestDiffMessage::mutable_rm() {
  // @@protoc_insertion_point(field_mutable_list:protobuf_unittest.TestDiffMessage.rm)
  return &rm_;
}
const ::google::protobuf::RepeatedPtrField< ::protobuf_unittest::TestField >&
TestDiffMessage::rm() const {
  // @@protoc_insertion_point(field_list:protobuf_unittest.TestDiffMessage.rm)
  return rm_;
}

inline const TestDiffMessage* TestDiffMessage::internal_default_instance() {
  return &TestDiffMessage_default_instance_.get();
}
#endif  // PROTOBUF_INLINE_NOT_IN_HEADERS

// @@protoc_insertion_point(namespace_scope)

}  // namespace protobuf_unittest

// @@protoc_insertion_point(global_scope)

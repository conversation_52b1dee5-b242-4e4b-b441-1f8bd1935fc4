// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: google/protobuf/util/json_format_proto3.proto

#ifndef PROTOBUF_google_2fprotobuf_2futil_2fjson_5fformat_5fproto3_2eproto__INCLUDED
#define PROTOBUF_google_2fprotobuf_2futil_2fjson_5fformat_5fproto3_2eproto__INCLUDED

#include <string>

#include <google/protobuf/stubs/common.h>

#if GOOGLE_PROTOBUF_VERSION < 3001000
#error This file was generated by a newer version of protoc which is
#error incompatible with your Protocol Buffer headers.  Please update
#error your headers.
#endif
#if 3001000 < GOOGLE_PROTOBUF_MIN_PROTOC_VERSION
#error This file was generated by an older version of protoc which is
#error incompatible with your Protocol Buffer headers.  Please
#error regenerate this file with a newer version of protoc.
#endif

#include <google/protobuf/arena.h>
#include <google/protobuf/arenastring.h>
#include <google/protobuf/generated_message_util.h>
#include <google/protobuf/metadata.h>
#include <google/protobuf/message.h>
#include <google/protobuf/repeated_field.h>
#include <google/protobuf/extension_set.h>
#include <google/protobuf/map.h>
#include <google/protobuf/map_field_inl.h>
#include <google/protobuf/generated_enum_reflection.h>
#include <google/protobuf/unknown_field_set.h>
#include <google/protobuf/duration.pb.h>
#include <google/protobuf/timestamp.pb.h>
#include <google/protobuf/wrappers.pb.h>
#include <google/protobuf/struct.pb.h>
#include <google/protobuf/any.pb.h>
#include <google/protobuf/field_mask.pb.h>
#include <google/protobuf/unittest.pb.h>
// @@protoc_insertion_point(includes)

namespace proto3 {

// Internal implementation detail -- do not call these.
void protobuf_AddDesc_google_2fprotobuf_2futil_2fjson_5fformat_5fproto3_2eproto();
void protobuf_InitDefaults_google_2fprotobuf_2futil_2fjson_5fformat_5fproto3_2eproto();
void protobuf_AssignDesc_google_2fprotobuf_2futil_2fjson_5fformat_5fproto3_2eproto();
void protobuf_ShutdownFile_google_2fprotobuf_2futil_2fjson_5fformat_5fproto3_2eproto();

class MessageType;
class TestAny;
class TestBoolValue;
class TestCustomJsonName;
class TestDuration;
class TestExtensions;
class TestFieldMask;
class TestListValue;
class TestMap;
class TestMessage;
class TestNestedMap;
class TestOneof;
class TestStruct;
class TestTimestamp;
class TestValue;
class TestWrapper;

enum EnumType {
  FOO = 0,
  BAR = 1,
  EnumType_INT_MIN_SENTINEL_DO_NOT_USE_ = ::google::protobuf::kint32min,
  EnumType_INT_MAX_SENTINEL_DO_NOT_USE_ = ::google::protobuf::kint32max
};
bool EnumType_IsValid(int value);
const EnumType EnumType_MIN = FOO;
const EnumType EnumType_MAX = BAR;
const int EnumType_ARRAYSIZE = EnumType_MAX + 1;

const ::google::protobuf::EnumDescriptor* EnumType_descriptor();
inline const ::std::string& EnumType_Name(EnumType value) {
  return ::google::protobuf::internal::NameOfEnum(
    EnumType_descriptor(), value);
}
inline bool EnumType_Parse(
    const ::std::string& name, EnumType* value) {
  return ::google::protobuf::internal::ParseNamedEnum<EnumType>(
    EnumType_descriptor(), name, value);
}
// ===================================================================

class MessageType : public ::google::protobuf::Message /* @@protoc_insertion_point(class_definition:proto3.MessageType) */ {
 public:
  MessageType();
  virtual ~MessageType();

  MessageType(const MessageType& from);

  inline MessageType& operator=(const MessageType& from) {
    CopyFrom(from);
    return *this;
  }

  static const ::google::protobuf::Descriptor* descriptor();
  static const MessageType& default_instance();

  static const MessageType* internal_default_instance();

  void Swap(MessageType* other);

  // implements Message ----------------------------------------------

  inline MessageType* New() const { return New(NULL); }

  MessageType* New(::google::protobuf::Arena* arena) const;
  void CopyFrom(const ::google::protobuf::Message& from);
  void MergeFrom(const ::google::protobuf::Message& from);
  void CopyFrom(const MessageType& from);
  void MergeFrom(const MessageType& from);
  void Clear();
  bool IsInitialized() const;

  size_t ByteSizeLong() const;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input);
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const;
  ::google::protobuf::uint8* InternalSerializeWithCachedSizesToArray(
      bool deterministic, ::google::protobuf::uint8* output) const;
  ::google::protobuf::uint8* SerializeWithCachedSizesToArray(::google::protobuf::uint8* output) const {
    return InternalSerializeWithCachedSizesToArray(false, output);
  }
  int GetCachedSize() const { return _cached_size_; }
  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const;
  void InternalSwap(MessageType* other);
  void UnsafeMergeFrom(const MessageType& from);
  private:
  inline ::google::protobuf::Arena* GetArenaNoVirtual() const {
    return _internal_metadata_.arena();
  }
  inline void* MaybeArenaPtr() const {
    return _internal_metadata_.raw_arena_ptr();
  }
  public:

  ::google::protobuf::Metadata GetMetadata() const;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // optional int32 value = 1;
  void clear_value();
  static const int kValueFieldNumber = 1;
  ::google::protobuf::int32 value() const;
  void set_value(::google::protobuf::int32 value);

  // @@protoc_insertion_point(class_scope:proto3.MessageType)
 private:

  ::google::protobuf::internal::InternalMetadataWithArena _internal_metadata_;
  ::google::protobuf::int32 value_;
  mutable int _cached_size_;
  friend void  protobuf_InitDefaults_google_2fprotobuf_2futil_2fjson_5fformat_5fproto3_2eproto_impl();
  friend void  protobuf_AddDesc_google_2fprotobuf_2futil_2fjson_5fformat_5fproto3_2eproto_impl();
  friend void protobuf_AssignDesc_google_2fprotobuf_2futil_2fjson_5fformat_5fproto3_2eproto();
  friend void protobuf_ShutdownFile_google_2fprotobuf_2futil_2fjson_5fformat_5fproto3_2eproto();

  void InitAsDefaultInstance();
};
extern ::google::protobuf::internal::ExplicitlyConstructed<MessageType> MessageType_default_instance_;

// -------------------------------------------------------------------

class TestMessage : public ::google::protobuf::Message /* @@protoc_insertion_point(class_definition:proto3.TestMessage) */ {
 public:
  TestMessage();
  virtual ~TestMessage();

  TestMessage(const TestMessage& from);

  inline TestMessage& operator=(const TestMessage& from) {
    CopyFrom(from);
    return *this;
  }

  static const ::google::protobuf::Descriptor* descriptor();
  static const TestMessage& default_instance();

  static const TestMessage* internal_default_instance();

  void Swap(TestMessage* other);

  // implements Message ----------------------------------------------

  inline TestMessage* New() const { return New(NULL); }

  TestMessage* New(::google::protobuf::Arena* arena) const;
  void CopyFrom(const ::google::protobuf::Message& from);
  void MergeFrom(const ::google::protobuf::Message& from);
  void CopyFrom(const TestMessage& from);
  void MergeFrom(const TestMessage& from);
  void Clear();
  bool IsInitialized() const;

  size_t ByteSizeLong() const;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input);
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const;
  ::google::protobuf::uint8* InternalSerializeWithCachedSizesToArray(
      bool deterministic, ::google::protobuf::uint8* output) const;
  ::google::protobuf::uint8* SerializeWithCachedSizesToArray(::google::protobuf::uint8* output) const {
    return InternalSerializeWithCachedSizesToArray(false, output);
  }
  int GetCachedSize() const { return _cached_size_; }
  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const;
  void InternalSwap(TestMessage* other);
  void UnsafeMergeFrom(const TestMessage& from);
  private:
  inline ::google::protobuf::Arena* GetArenaNoVirtual() const {
    return _internal_metadata_.arena();
  }
  inline void* MaybeArenaPtr() const {
    return _internal_metadata_.raw_arena_ptr();
  }
  public:

  ::google::protobuf::Metadata GetMetadata() const;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // optional bool bool_value = 1;
  void clear_bool_value();
  static const int kBoolValueFieldNumber = 1;
  bool bool_value() const;
  void set_bool_value(bool value);

  // optional int32 int32_value = 2;
  void clear_int32_value();
  static const int kInt32ValueFieldNumber = 2;
  ::google::protobuf::int32 int32_value() const;
  void set_int32_value(::google::protobuf::int32 value);

  // optional int64 int64_value = 3;
  void clear_int64_value();
  static const int kInt64ValueFieldNumber = 3;
  ::google::protobuf::int64 int64_value() const;
  void set_int64_value(::google::protobuf::int64 value);

  // optional uint32 uint32_value = 4;
  void clear_uint32_value();
  static const int kUint32ValueFieldNumber = 4;
  ::google::protobuf::uint32 uint32_value() const;
  void set_uint32_value(::google::protobuf::uint32 value);

  // optional uint64 uint64_value = 5;
  void clear_uint64_value();
  static const int kUint64ValueFieldNumber = 5;
  ::google::protobuf::uint64 uint64_value() const;
  void set_uint64_value(::google::protobuf::uint64 value);

  // optional float float_value = 6;
  void clear_float_value();
  static const int kFloatValueFieldNumber = 6;
  float float_value() const;
  void set_float_value(float value);

  // optional double double_value = 7;
  void clear_double_value();
  static const int kDoubleValueFieldNumber = 7;
  double double_value() const;
  void set_double_value(double value);

  // optional string string_value = 8;
  void clear_string_value();
  static const int kStringValueFieldNumber = 8;
  const ::std::string& string_value() const;
  void set_string_value(const ::std::string& value);
  void set_string_value(const char* value);
  void set_string_value(const char* value, size_t size);
  ::std::string* mutable_string_value();
  ::std::string* release_string_value();
  void set_allocated_string_value(::std::string* string_value);

  // optional bytes bytes_value = 9;
  void clear_bytes_value();
  static const int kBytesValueFieldNumber = 9;
  const ::std::string& bytes_value() const;
  void set_bytes_value(const ::std::string& value);
  void set_bytes_value(const char* value);
  void set_bytes_value(const void* value, size_t size);
  ::std::string* mutable_bytes_value();
  ::std::string* release_bytes_value();
  void set_allocated_bytes_value(::std::string* bytes_value);

  // optional .proto3.EnumType enum_value = 10;
  void clear_enum_value();
  static const int kEnumValueFieldNumber = 10;
  ::proto3::EnumType enum_value() const;
  void set_enum_value(::proto3::EnumType value);

  // optional .proto3.MessageType message_value = 11;
  bool has_message_value() const;
  void clear_message_value();
  static const int kMessageValueFieldNumber = 11;
  const ::proto3::MessageType& message_value() const;
  ::proto3::MessageType* mutable_message_value();
  ::proto3::MessageType* release_message_value();
  void set_allocated_message_value(::proto3::MessageType* message_value);

  // repeated bool repeated_bool_value = 21;
  int repeated_bool_value_size() const;
  void clear_repeated_bool_value();
  static const int kRepeatedBoolValueFieldNumber = 21;
  bool repeated_bool_value(int index) const;
  void set_repeated_bool_value(int index, bool value);
  void add_repeated_bool_value(bool value);
  const ::google::protobuf::RepeatedField< bool >&
      repeated_bool_value() const;
  ::google::protobuf::RepeatedField< bool >*
      mutable_repeated_bool_value();

  // repeated int32 repeated_int32_value = 22;
  int repeated_int32_value_size() const;
  void clear_repeated_int32_value();
  static const int kRepeatedInt32ValueFieldNumber = 22;
  ::google::protobuf::int32 repeated_int32_value(int index) const;
  void set_repeated_int32_value(int index, ::google::protobuf::int32 value);
  void add_repeated_int32_value(::google::protobuf::int32 value);
  const ::google::protobuf::RepeatedField< ::google::protobuf::int32 >&
      repeated_int32_value() const;
  ::google::protobuf::RepeatedField< ::google::protobuf::int32 >*
      mutable_repeated_int32_value();

  // repeated int64 repeated_int64_value = 23;
  int repeated_int64_value_size() const;
  void clear_repeated_int64_value();
  static const int kRepeatedInt64ValueFieldNumber = 23;
  ::google::protobuf::int64 repeated_int64_value(int index) const;
  void set_repeated_int64_value(int index, ::google::protobuf::int64 value);
  void add_repeated_int64_value(::google::protobuf::int64 value);
  const ::google::protobuf::RepeatedField< ::google::protobuf::int64 >&
      repeated_int64_value() const;
  ::google::protobuf::RepeatedField< ::google::protobuf::int64 >*
      mutable_repeated_int64_value();

  // repeated uint32 repeated_uint32_value = 24;
  int repeated_uint32_value_size() const;
  void clear_repeated_uint32_value();
  static const int kRepeatedUint32ValueFieldNumber = 24;
  ::google::protobuf::uint32 repeated_uint32_value(int index) const;
  void set_repeated_uint32_value(int index, ::google::protobuf::uint32 value);
  void add_repeated_uint32_value(::google::protobuf::uint32 value);
  const ::google::protobuf::RepeatedField< ::google::protobuf::uint32 >&
      repeated_uint32_value() const;
  ::google::protobuf::RepeatedField< ::google::protobuf::uint32 >*
      mutable_repeated_uint32_value();

  // repeated uint64 repeated_uint64_value = 25;
  int repeated_uint64_value_size() const;
  void clear_repeated_uint64_value();
  static const int kRepeatedUint64ValueFieldNumber = 25;
  ::google::protobuf::uint64 repeated_uint64_value(int index) const;
  void set_repeated_uint64_value(int index, ::google::protobuf::uint64 value);
  void add_repeated_uint64_value(::google::protobuf::uint64 value);
  const ::google::protobuf::RepeatedField< ::google::protobuf::uint64 >&
      repeated_uint64_value() const;
  ::google::protobuf::RepeatedField< ::google::protobuf::uint64 >*
      mutable_repeated_uint64_value();

  // repeated float repeated_float_value = 26;
  int repeated_float_value_size() const;
  void clear_repeated_float_value();
  static const int kRepeatedFloatValueFieldNumber = 26;
  float repeated_float_value(int index) const;
  void set_repeated_float_value(int index, float value);
  void add_repeated_float_value(float value);
  const ::google::protobuf::RepeatedField< float >&
      repeated_float_value() const;
  ::google::protobuf::RepeatedField< float >*
      mutable_repeated_float_value();

  // repeated double repeated_double_value = 27;
  int repeated_double_value_size() const;
  void clear_repeated_double_value();
  static const int kRepeatedDoubleValueFieldNumber = 27;
  double repeated_double_value(int index) const;
  void set_repeated_double_value(int index, double value);
  void add_repeated_double_value(double value);
  const ::google::protobuf::RepeatedField< double >&
      repeated_double_value() const;
  ::google::protobuf::RepeatedField< double >*
      mutable_repeated_double_value();

  // repeated string repeated_string_value = 28;
  int repeated_string_value_size() const;
  void clear_repeated_string_value();
  static const int kRepeatedStringValueFieldNumber = 28;
  const ::std::string& repeated_string_value(int index) const;
  ::std::string* mutable_repeated_string_value(int index);
  void set_repeated_string_value(int index, const ::std::string& value);
  void set_repeated_string_value(int index, const char* value);
  void set_repeated_string_value(int index, const char* value, size_t size);
  ::std::string* add_repeated_string_value();
  void add_repeated_string_value(const ::std::string& value);
  void add_repeated_string_value(const char* value);
  void add_repeated_string_value(const char* value, size_t size);
  const ::google::protobuf::RepeatedPtrField< ::std::string>& repeated_string_value() const;
  ::google::protobuf::RepeatedPtrField< ::std::string>* mutable_repeated_string_value();

  // repeated bytes repeated_bytes_value = 29;
  int repeated_bytes_value_size() const;
  void clear_repeated_bytes_value();
  static const int kRepeatedBytesValueFieldNumber = 29;
  const ::std::string& repeated_bytes_value(int index) const;
  ::std::string* mutable_repeated_bytes_value(int index);
  void set_repeated_bytes_value(int index, const ::std::string& value);
  void set_repeated_bytes_value(int index, const char* value);
  void set_repeated_bytes_value(int index, const void* value, size_t size);
  ::std::string* add_repeated_bytes_value();
  void add_repeated_bytes_value(const ::std::string& value);
  void add_repeated_bytes_value(const char* value);
  void add_repeated_bytes_value(const void* value, size_t size);
  const ::google::protobuf::RepeatedPtrField< ::std::string>& repeated_bytes_value() const;
  ::google::protobuf::RepeatedPtrField< ::std::string>* mutable_repeated_bytes_value();

  // repeated .proto3.EnumType repeated_enum_value = 30;
  int repeated_enum_value_size() const;
  void clear_repeated_enum_value();
  static const int kRepeatedEnumValueFieldNumber = 30;
  ::proto3::EnumType repeated_enum_value(int index) const;
  void set_repeated_enum_value(int index, ::proto3::EnumType value);
  void add_repeated_enum_value(::proto3::EnumType value);
  const ::google::protobuf::RepeatedField<int>& repeated_enum_value() const;
  ::google::protobuf::RepeatedField<int>* mutable_repeated_enum_value();

  // repeated .proto3.MessageType repeated_message_value = 31;
  int repeated_message_value_size() const;
  void clear_repeated_message_value();
  static const int kRepeatedMessageValueFieldNumber = 31;
  const ::proto3::MessageType& repeated_message_value(int index) const;
  ::proto3::MessageType* mutable_repeated_message_value(int index);
  ::proto3::MessageType* add_repeated_message_value();
  ::google::protobuf::RepeatedPtrField< ::proto3::MessageType >*
      mutable_repeated_message_value();
  const ::google::protobuf::RepeatedPtrField< ::proto3::MessageType >&
      repeated_message_value() const;

  // @@protoc_insertion_point(class_scope:proto3.TestMessage)
 private:

  ::google::protobuf::internal::InternalMetadataWithArena _internal_metadata_;
  ::google::protobuf::RepeatedField< bool > repeated_bool_value_;
  mutable int _repeated_bool_value_cached_byte_size_;
  ::google::protobuf::RepeatedField< ::google::protobuf::int32 > repeated_int32_value_;
  mutable int _repeated_int32_value_cached_byte_size_;
  ::google::protobuf::RepeatedField< ::google::protobuf::int64 > repeated_int64_value_;
  mutable int _repeated_int64_value_cached_byte_size_;
  ::google::protobuf::RepeatedField< ::google::protobuf::uint32 > repeated_uint32_value_;
  mutable int _repeated_uint32_value_cached_byte_size_;
  ::google::protobuf::RepeatedField< ::google::protobuf::uint64 > repeated_uint64_value_;
  mutable int _repeated_uint64_value_cached_byte_size_;
  ::google::protobuf::RepeatedField< float > repeated_float_value_;
  mutable int _repeated_float_value_cached_byte_size_;
  ::google::protobuf::RepeatedField< double > repeated_double_value_;
  mutable int _repeated_double_value_cached_byte_size_;
  ::google::protobuf::RepeatedPtrField< ::std::string> repeated_string_value_;
  ::google::protobuf::RepeatedPtrField< ::std::string> repeated_bytes_value_;
  ::google::protobuf::RepeatedField<int> repeated_enum_value_;
  mutable int _repeated_enum_value_cached_byte_size_;
  ::google::protobuf::RepeatedPtrField< ::proto3::MessageType > repeated_message_value_;
  ::google::protobuf::internal::ArenaStringPtr string_value_;
  ::google::protobuf::internal::ArenaStringPtr bytes_value_;
  ::proto3::MessageType* message_value_;
  bool bool_value_;
  ::google::protobuf::int32 int32_value_;
  ::google::protobuf::int64 int64_value_;
  ::google::protobuf::uint64 uint64_value_;
  ::google::protobuf::uint32 uint32_value_;
  float float_value_;
  double double_value_;
  int enum_value_;
  mutable int _cached_size_;
  friend void  protobuf_InitDefaults_google_2fprotobuf_2futil_2fjson_5fformat_5fproto3_2eproto_impl();
  friend void  protobuf_AddDesc_google_2fprotobuf_2futil_2fjson_5fformat_5fproto3_2eproto_impl();
  friend void protobuf_AssignDesc_google_2fprotobuf_2futil_2fjson_5fformat_5fproto3_2eproto();
  friend void protobuf_ShutdownFile_google_2fprotobuf_2futil_2fjson_5fformat_5fproto3_2eproto();

  void InitAsDefaultInstance();
};
extern ::google::protobuf::internal::ExplicitlyConstructed<TestMessage> TestMessage_default_instance_;

// -------------------------------------------------------------------

class TestOneof : public ::google::protobuf::Message /* @@protoc_insertion_point(class_definition:proto3.TestOneof) */ {
 public:
  TestOneof();
  virtual ~TestOneof();

  TestOneof(const TestOneof& from);

  inline TestOneof& operator=(const TestOneof& from) {
    CopyFrom(from);
    return *this;
  }

  static const ::google::protobuf::Descriptor* descriptor();
  static const TestOneof& default_instance();

  enum OneofValueCase {
    kOneofInt32Value = 1,
    kOneofStringValue = 2,
    kOneofBytesValue = 3,
    kOneofEnumValue = 4,
    kOneofMessageValue = 5,
    ONEOF_VALUE_NOT_SET = 0,
  };

  static const TestOneof* internal_default_instance();

  void Swap(TestOneof* other);

  // implements Message ----------------------------------------------

  inline TestOneof* New() const { return New(NULL); }

  TestOneof* New(::google::protobuf::Arena* arena) const;
  void CopyFrom(const ::google::protobuf::Message& from);
  void MergeFrom(const ::google::protobuf::Message& from);
  void CopyFrom(const TestOneof& from);
  void MergeFrom(const TestOneof& from);
  void Clear();
  bool IsInitialized() const;

  size_t ByteSizeLong() const;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input);
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const;
  ::google::protobuf::uint8* InternalSerializeWithCachedSizesToArray(
      bool deterministic, ::google::protobuf::uint8* output) const;
  ::google::protobuf::uint8* SerializeWithCachedSizesToArray(::google::protobuf::uint8* output) const {
    return InternalSerializeWithCachedSizesToArray(false, output);
  }
  int GetCachedSize() const { return _cached_size_; }
  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const;
  void InternalSwap(TestOneof* other);
  void UnsafeMergeFrom(const TestOneof& from);
  private:
  inline ::google::protobuf::Arena* GetArenaNoVirtual() const {
    return _internal_metadata_.arena();
  }
  inline void* MaybeArenaPtr() const {
    return _internal_metadata_.raw_arena_ptr();
  }
  public:

  ::google::protobuf::Metadata GetMetadata() const;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // optional int32 oneof_int32_value = 1;
  private:
  bool has_oneof_int32_value() const;
  public:
  void clear_oneof_int32_value();
  static const int kOneofInt32ValueFieldNumber = 1;
  ::google::protobuf::int32 oneof_int32_value() const;
  void set_oneof_int32_value(::google::protobuf::int32 value);

  // optional string oneof_string_value = 2;
  private:
  bool has_oneof_string_value() const;
  public:
  void clear_oneof_string_value();
  static const int kOneofStringValueFieldNumber = 2;
  const ::std::string& oneof_string_value() const;
  void set_oneof_string_value(const ::std::string& value);
  void set_oneof_string_value(const char* value);
  void set_oneof_string_value(const char* value, size_t size);
  ::std::string* mutable_oneof_string_value();
  ::std::string* release_oneof_string_value();
  void set_allocated_oneof_string_value(::std::string* oneof_string_value);

  // optional bytes oneof_bytes_value = 3;
  private:
  bool has_oneof_bytes_value() const;
  public:
  void clear_oneof_bytes_value();
  static const int kOneofBytesValueFieldNumber = 3;
  const ::std::string& oneof_bytes_value() const;
  void set_oneof_bytes_value(const ::std::string& value);
  void set_oneof_bytes_value(const char* value);
  void set_oneof_bytes_value(const void* value, size_t size);
  ::std::string* mutable_oneof_bytes_value();
  ::std::string* release_oneof_bytes_value();
  void set_allocated_oneof_bytes_value(::std::string* oneof_bytes_value);

  // optional .proto3.EnumType oneof_enum_value = 4;
  private:
  bool has_oneof_enum_value() const;
  public:
  void clear_oneof_enum_value();
  static const int kOneofEnumValueFieldNumber = 4;
  ::proto3::EnumType oneof_enum_value() const;
  void set_oneof_enum_value(::proto3::EnumType value);

  // optional .proto3.MessageType oneof_message_value = 5;
  bool has_oneof_message_value() const;
  void clear_oneof_message_value();
  static const int kOneofMessageValueFieldNumber = 5;
  const ::proto3::MessageType& oneof_message_value() const;
  ::proto3::MessageType* mutable_oneof_message_value();
  ::proto3::MessageType* release_oneof_message_value();
  void set_allocated_oneof_message_value(::proto3::MessageType* oneof_message_value);

  OneofValueCase oneof_value_case() const;
  // @@protoc_insertion_point(class_scope:proto3.TestOneof)
 private:
  inline void set_has_oneof_int32_value();
  inline void set_has_oneof_string_value();
  inline void set_has_oneof_bytes_value();
  inline void set_has_oneof_enum_value();
  inline void set_has_oneof_message_value();

  inline bool has_oneof_value() const;
  void clear_oneof_value();
  inline void clear_has_oneof_value();

  ::google::protobuf::internal::InternalMetadataWithArena _internal_metadata_;
  union OneofValueUnion {
    OneofValueUnion() {}
    ::google::protobuf::int32 oneof_int32_value_;
    ::google::protobuf::internal::ArenaStringPtr oneof_string_value_;
    ::google::protobuf::internal::ArenaStringPtr oneof_bytes_value_;
    int oneof_enum_value_;
    ::proto3::MessageType* oneof_message_value_;
  } oneof_value_;
  mutable int _cached_size_;
  ::google::protobuf::uint32 _oneof_case_[1];

  friend void  protobuf_InitDefaults_google_2fprotobuf_2futil_2fjson_5fformat_5fproto3_2eproto_impl();
  friend void  protobuf_AddDesc_google_2fprotobuf_2futil_2fjson_5fformat_5fproto3_2eproto_impl();
  friend void protobuf_AssignDesc_google_2fprotobuf_2futil_2fjson_5fformat_5fproto3_2eproto();
  friend void protobuf_ShutdownFile_google_2fprotobuf_2futil_2fjson_5fformat_5fproto3_2eproto();

  void InitAsDefaultInstance();
};
extern ::google::protobuf::internal::ExplicitlyConstructed<TestOneof> TestOneof_default_instance_;

// -------------------------------------------------------------------

class TestMap : public ::google::protobuf::Message /* @@protoc_insertion_point(class_definition:proto3.TestMap) */ {
 public:
  TestMap();
  virtual ~TestMap();

  TestMap(const TestMap& from);

  inline TestMap& operator=(const TestMap& from) {
    CopyFrom(from);
    return *this;
  }

  static const ::google::protobuf::Descriptor* descriptor();
  static const TestMap& default_instance();

  static const TestMap* internal_default_instance();

  void Swap(TestMap* other);

  // implements Message ----------------------------------------------

  inline TestMap* New() const { return New(NULL); }

  TestMap* New(::google::protobuf::Arena* arena) const;
  void CopyFrom(const ::google::protobuf::Message& from);
  void MergeFrom(const ::google::protobuf::Message& from);
  void CopyFrom(const TestMap& from);
  void MergeFrom(const TestMap& from);
  void Clear();
  bool IsInitialized() const;

  size_t ByteSizeLong() const;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input);
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const;
  ::google::protobuf::uint8* InternalSerializeWithCachedSizesToArray(
      bool deterministic, ::google::protobuf::uint8* output) const;
  ::google::protobuf::uint8* SerializeWithCachedSizesToArray(::google::protobuf::uint8* output) const {
    return InternalSerializeWithCachedSizesToArray(false, output);
  }
  int GetCachedSize() const { return _cached_size_; }
  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const;
  void InternalSwap(TestMap* other);
  void UnsafeMergeFrom(const TestMap& from);
  private:
  inline ::google::protobuf::Arena* GetArenaNoVirtual() const {
    return _internal_metadata_.arena();
  }
  inline void* MaybeArenaPtr() const {
    return _internal_metadata_.raw_arena_ptr();
  }
  public:

  ::google::protobuf::Metadata GetMetadata() const;

  // nested types ----------------------------------------------------


  // accessors -------------------------------------------------------

  // map<bool, int32> bool_map = 1;
  int bool_map_size() const;
  void clear_bool_map();
  static const int kBoolMapFieldNumber = 1;
  const ::google::protobuf::Map< bool, ::google::protobuf::int32 >&
      bool_map() const;
  ::google::protobuf::Map< bool, ::google::protobuf::int32 >*
      mutable_bool_map();

  // map<int32, int32> int32_map = 2;
  int int32_map_size() const;
  void clear_int32_map();
  static const int kInt32MapFieldNumber = 2;
  const ::google::protobuf::Map< ::google::protobuf::int32, ::google::protobuf::int32 >&
      int32_map() const;
  ::google::protobuf::Map< ::google::protobuf::int32, ::google::protobuf::int32 >*
      mutable_int32_map();

  // map<int64, int32> int64_map = 3;
  int int64_map_size() const;
  void clear_int64_map();
  static const int kInt64MapFieldNumber = 3;
  const ::google::protobuf::Map< ::google::protobuf::int64, ::google::protobuf::int32 >&
      int64_map() const;
  ::google::protobuf::Map< ::google::protobuf::int64, ::google::protobuf::int32 >*
      mutable_int64_map();

  // map<uint32, int32> uint32_map = 4;
  int uint32_map_size() const;
  void clear_uint32_map();
  static const int kUint32MapFieldNumber = 4;
  const ::google::protobuf::Map< ::google::protobuf::uint32, ::google::protobuf::int32 >&
      uint32_map() const;
  ::google::protobuf::Map< ::google::protobuf::uint32, ::google::protobuf::int32 >*
      mutable_uint32_map();

  // map<uint64, int32> uint64_map = 5;
  int uint64_map_size() const;
  void clear_uint64_map();
  static const int kUint64MapFieldNumber = 5;
  const ::google::protobuf::Map< ::google::protobuf::uint64, ::google::protobuf::int32 >&
      uint64_map() const;
  ::google::protobuf::Map< ::google::protobuf::uint64, ::google::protobuf::int32 >*
      mutable_uint64_map();

  // map<string, int32> string_map = 6;
  int string_map_size() const;
  void clear_string_map();
  static const int kStringMapFieldNumber = 6;
  const ::google::protobuf::Map< ::std::string, ::google::protobuf::int32 >&
      string_map() const;
  ::google::protobuf::Map< ::std::string, ::google::protobuf::int32 >*
      mutable_string_map();

  // @@protoc_insertion_point(class_scope:proto3.TestMap)
 private:

  ::google::protobuf::internal::InternalMetadataWithArena _internal_metadata_;
  typedef ::google::protobuf::internal::MapEntryLite<
      bool, ::google::protobuf::int32,
      ::google::protobuf::internal::WireFormatLite::TYPE_BOOL,
      ::google::protobuf::internal::WireFormatLite::TYPE_INT32,
      0 >
      TestMap_BoolMapEntry;
  ::google::protobuf::internal::MapField<
      bool, ::google::protobuf::int32,
      ::google::protobuf::internal::WireFormatLite::TYPE_BOOL,
      ::google::protobuf::internal::WireFormatLite::TYPE_INT32,
      0 > bool_map_;
  typedef ::google::protobuf::internal::MapEntryLite<
      ::google::protobuf::int32, ::google::protobuf::int32,
      ::google::protobuf::internal::WireFormatLite::TYPE_INT32,
      ::google::protobuf::internal::WireFormatLite::TYPE_INT32,
      0 >
      TestMap_Int32MapEntry;
  ::google::protobuf::internal::MapField<
      ::google::protobuf::int32, ::google::protobuf::int32,
      ::google::protobuf::internal::WireFormatLite::TYPE_INT32,
      ::google::protobuf::internal::WireFormatLite::TYPE_INT32,
      0 > int32_map_;
  typedef ::google::protobuf::internal::MapEntryLite<
      ::google::protobuf::int64, ::google::protobuf::int32,
      ::google::protobuf::internal::WireFormatLite::TYPE_INT64,
      ::google::protobuf::internal::WireFormatLite::TYPE_INT32,
      0 >
      TestMap_Int64MapEntry;
  ::google::protobuf::internal::MapField<
      ::google::protobuf::int64, ::google::protobuf::int32,
      ::google::protobuf::internal::WireFormatLite::TYPE_INT64,
      ::google::protobuf::internal::WireFormatLite::TYPE_INT32,
      0 > int64_map_;
  typedef ::google::protobuf::internal::MapEntryLite<
      ::google::protobuf::uint32, ::google::protobuf::int32,
      ::google::protobuf::internal::WireFormatLite::TYPE_UINT32,
      ::google::protobuf::internal::WireFormatLite::TYPE_INT32,
      0 >
      TestMap_Uint32MapEntry;
  ::google::protobuf::internal::MapField<
      ::google::protobuf::uint32, ::google::protobuf::int32,
      ::google::protobuf::internal::WireFormatLite::TYPE_UINT32,
      ::google::protobuf::internal::WireFormatLite::TYPE_INT32,
      0 > uint32_map_;
  typedef ::google::protobuf::internal::MapEntryLite<
      ::google::protobuf::uint64, ::google::protobuf::int32,
      ::google::protobuf::internal::WireFormatLite::TYPE_UINT64,
      ::google::protobuf::internal::WireFormatLite::TYPE_INT32,
      0 >
      TestMap_Uint64MapEntry;
  ::google::protobuf::internal::MapField<
      ::google::protobuf::uint64, ::google::protobuf::int32,
      ::google::protobuf::internal::WireFormatLite::TYPE_UINT64,
      ::google::protobuf::internal::WireFormatLite::TYPE_INT32,
      0 > uint64_map_;
  typedef ::google::protobuf::internal::MapEntryLite<
      ::std::string, ::google::protobuf::int32,
      ::google::protobuf::internal::WireFormatLite::TYPE_STRING,
      ::google::protobuf::internal::WireFormatLite::TYPE_INT32,
      0 >
      TestMap_StringMapEntry;
  ::google::protobuf::internal::MapField<
      ::std::string, ::google::protobuf::int32,
      ::google::protobuf::internal::WireFormatLite::TYPE_STRING,
      ::google::protobuf::internal::WireFormatLite::TYPE_INT32,
      0 > string_map_;
  mutable int _cached_size_;
  friend void  protobuf_InitDefaults_google_2fprotobuf_2futil_2fjson_5fformat_5fproto3_2eproto_impl();
  friend void  protobuf_AddDesc_google_2fprotobuf_2futil_2fjson_5fformat_5fproto3_2eproto_impl();
  friend void protobuf_AssignDesc_google_2fprotobuf_2futil_2fjson_5fformat_5fproto3_2eproto();
  friend void protobuf_ShutdownFile_google_2fprotobuf_2futil_2fjson_5fformat_5fproto3_2eproto();

  void InitAsDefaultInstance();
};
extern ::google::protobuf::internal::ExplicitlyConstructed<TestMap> TestMap_default_instance_;

// -------------------------------------------------------------------

class TestNestedMap : public ::google::protobuf::Message /* @@protoc_insertion_point(class_definition:proto3.TestNestedMap) */ {
 public:
  TestNestedMap();
  virtual ~TestNestedMap();

  TestNestedMap(const TestNestedMap& from);

  inline TestNestedMap& operator=(const TestNestedMap& from) {
    CopyFrom(from);
    return *this;
  }

  static const ::google::protobuf::Descriptor* descriptor();
  static const TestNestedMap& default_instance();

  static const TestNestedMap* internal_default_instance();

  void Swap(TestNestedMap* other);

  // implements Message ----------------------------------------------

  inline TestNestedMap* New() const { return New(NULL); }

  TestNestedMap* New(::google::protobuf::Arena* arena) const;
  void CopyFrom(const ::google::protobuf::Message& from);
  void MergeFrom(const ::google::protobuf::Message& from);
  void CopyFrom(const TestNestedMap& from);
  void MergeFrom(const TestNestedMap& from);
  void Clear();
  bool IsInitialized() const;

  size_t ByteSizeLong() const;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input);
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const;
  ::google::protobuf::uint8* InternalSerializeWithCachedSizesToArray(
      bool deterministic, ::google::protobuf::uint8* output) const;
  ::google::protobuf::uint8* SerializeWithCachedSizesToArray(::google::protobuf::uint8* output) const {
    return InternalSerializeWithCachedSizesToArray(false, output);
  }
  int GetCachedSize() const { return _cached_size_; }
  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const;
  void InternalSwap(TestNestedMap* other);
  void UnsafeMergeFrom(const TestNestedMap& from);
  private:
  inline ::google::protobuf::Arena* GetArenaNoVirtual() const {
    return _internal_metadata_.arena();
  }
  inline void* MaybeArenaPtr() const {
    return _internal_metadata_.raw_arena_ptr();
  }
  public:

  ::google::protobuf::Metadata GetMetadata() const;

  // nested types ----------------------------------------------------


  // accessors -------------------------------------------------------

  // map<bool, int32> bool_map = 1;
  int bool_map_size() const;
  void clear_bool_map();
  static const int kBoolMapFieldNumber = 1;
  const ::google::protobuf::Map< bool, ::google::protobuf::int32 >&
      bool_map() const;
  ::google::protobuf::Map< bool, ::google::protobuf::int32 >*
      mutable_bool_map();

  // map<int32, int32> int32_map = 2;
  int int32_map_size() const;
  void clear_int32_map();
  static const int kInt32MapFieldNumber = 2;
  const ::google::protobuf::Map< ::google::protobuf::int32, ::google::protobuf::int32 >&
      int32_map() const;
  ::google::protobuf::Map< ::google::protobuf::int32, ::google::protobuf::int32 >*
      mutable_int32_map();

  // map<int64, int32> int64_map = 3;
  int int64_map_size() const;
  void clear_int64_map();
  static const int kInt64MapFieldNumber = 3;
  const ::google::protobuf::Map< ::google::protobuf::int64, ::google::protobuf::int32 >&
      int64_map() const;
  ::google::protobuf::Map< ::google::protobuf::int64, ::google::protobuf::int32 >*
      mutable_int64_map();

  // map<uint32, int32> uint32_map = 4;
  int uint32_map_size() const;
  void clear_uint32_map();
  static const int kUint32MapFieldNumber = 4;
  const ::google::protobuf::Map< ::google::protobuf::uint32, ::google::protobuf::int32 >&
      uint32_map() const;
  ::google::protobuf::Map< ::google::protobuf::uint32, ::google::protobuf::int32 >*
      mutable_uint32_map();

  // map<uint64, int32> uint64_map = 5;
  int uint64_map_size() const;
  void clear_uint64_map();
  static const int kUint64MapFieldNumber = 5;
  const ::google::protobuf::Map< ::google::protobuf::uint64, ::google::protobuf::int32 >&
      uint64_map() const;
  ::google::protobuf::Map< ::google::protobuf::uint64, ::google::protobuf::int32 >*
      mutable_uint64_map();

  // map<string, int32> string_map = 6;
  int string_map_size() const;
  void clear_string_map();
  static const int kStringMapFieldNumber = 6;
  const ::google::protobuf::Map< ::std::string, ::google::protobuf::int32 >&
      string_map() const;
  ::google::protobuf::Map< ::std::string, ::google::protobuf::int32 >*
      mutable_string_map();

  // map<string, .proto3.TestNestedMap> map_map = 7;
  int map_map_size() const;
  void clear_map_map();
  static const int kMapMapFieldNumber = 7;
  const ::google::protobuf::Map< ::std::string, ::proto3::TestNestedMap >&
      map_map() const;
  ::google::protobuf::Map< ::std::string, ::proto3::TestNestedMap >*
      mutable_map_map();

  // @@protoc_insertion_point(class_scope:proto3.TestNestedMap)
 private:

  ::google::protobuf::internal::InternalMetadataWithArena _internal_metadata_;
  typedef ::google::protobuf::internal::MapEntryLite<
      bool, ::google::protobuf::int32,
      ::google::protobuf::internal::WireFormatLite::TYPE_BOOL,
      ::google::protobuf::internal::WireFormatLite::TYPE_INT32,
      0 >
      TestNestedMap_BoolMapEntry;
  ::google::protobuf::internal::MapField<
      bool, ::google::protobuf::int32,
      ::google::protobuf::internal::WireFormatLite::TYPE_BOOL,
      ::google::protobuf::internal::WireFormatLite::TYPE_INT32,
      0 > bool_map_;
  typedef ::google::protobuf::internal::MapEntryLite<
      ::google::protobuf::int32, ::google::protobuf::int32,
      ::google::protobuf::internal::WireFormatLite::TYPE_INT32,
      ::google::protobuf::internal::WireFormatLite::TYPE_INT32,
      0 >
      TestNestedMap_Int32MapEntry;
  ::google::protobuf::internal::MapField<
      ::google::protobuf::int32, ::google::protobuf::int32,
      ::google::protobuf::internal::WireFormatLite::TYPE_INT32,
      ::google::protobuf::internal::WireFormatLite::TYPE_INT32,
      0 > int32_map_;
  typedef ::google::protobuf::internal::MapEntryLite<
      ::google::protobuf::int64, ::google::protobuf::int32,
      ::google::protobuf::internal::WireFormatLite::TYPE_INT64,
      ::google::protobuf::internal::WireFormatLite::TYPE_INT32,
      0 >
      TestNestedMap_Int64MapEntry;
  ::google::protobuf::internal::MapField<
      ::google::protobuf::int64, ::google::protobuf::int32,
      ::google::protobuf::internal::WireFormatLite::TYPE_INT64,
      ::google::protobuf::internal::WireFormatLite::TYPE_INT32,
      0 > int64_map_;
  typedef ::google::protobuf::internal::MapEntryLite<
      ::google::protobuf::uint32, ::google::protobuf::int32,
      ::google::protobuf::internal::WireFormatLite::TYPE_UINT32,
      ::google::protobuf::internal::WireFormatLite::TYPE_INT32,
      0 >
      TestNestedMap_Uint32MapEntry;
  ::google::protobuf::internal::MapField<
      ::google::protobuf::uint32, ::google::protobuf::int32,
      ::google::protobuf::internal::WireFormatLite::TYPE_UINT32,
      ::google::protobuf::internal::WireFormatLite::TYPE_INT32,
      0 > uint32_map_;
  typedef ::google::protobuf::internal::MapEntryLite<
      ::google::protobuf::uint64, ::google::protobuf::int32,
      ::google::protobuf::internal::WireFormatLite::TYPE_UINT64,
      ::google::protobuf::internal::WireFormatLite::TYPE_INT32,
      0 >
      TestNestedMap_Uint64MapEntry;
  ::google::protobuf::internal::MapField<
      ::google::protobuf::uint64, ::google::protobuf::int32,
      ::google::protobuf::internal::WireFormatLite::TYPE_UINT64,
      ::google::protobuf::internal::WireFormatLite::TYPE_INT32,
      0 > uint64_map_;
  typedef ::google::protobuf::internal::MapEntryLite<
      ::std::string, ::google::protobuf::int32,
      ::google::protobuf::internal::WireFormatLite::TYPE_STRING,
      ::google::protobuf::internal::WireFormatLite::TYPE_INT32,
      0 >
      TestNestedMap_StringMapEntry;
  ::google::protobuf::internal::MapField<
      ::std::string, ::google::protobuf::int32,
      ::google::protobuf::internal::WireFormatLite::TYPE_STRING,
      ::google::protobuf::internal::WireFormatLite::TYPE_INT32,
      0 > string_map_;
  typedef ::google::protobuf::internal::MapEntryLite<
      ::std::string, ::proto3::TestNestedMap,
      ::google::protobuf::internal::WireFormatLite::TYPE_STRING,
      ::google::protobuf::internal::WireFormatLite::TYPE_MESSAGE,
      0 >
      TestNestedMap_MapMapEntry;
  ::google::protobuf::internal::MapField<
      ::std::string, ::proto3::TestNestedMap,
      ::google::protobuf::internal::WireFormatLite::TYPE_STRING,
      ::google::protobuf::internal::WireFormatLite::TYPE_MESSAGE,
      0 > map_map_;
  mutable int _cached_size_;
  friend void  protobuf_InitDefaults_google_2fprotobuf_2futil_2fjson_5fformat_5fproto3_2eproto_impl();
  friend void  protobuf_AddDesc_google_2fprotobuf_2futil_2fjson_5fformat_5fproto3_2eproto_impl();
  friend void protobuf_AssignDesc_google_2fprotobuf_2futil_2fjson_5fformat_5fproto3_2eproto();
  friend void protobuf_ShutdownFile_google_2fprotobuf_2futil_2fjson_5fformat_5fproto3_2eproto();

  void InitAsDefaultInstance();
};
extern ::google::protobuf::internal::ExplicitlyConstructed<TestNestedMap> TestNestedMap_default_instance_;

// -------------------------------------------------------------------

class TestWrapper : public ::google::protobuf::Message /* @@protoc_insertion_point(class_definition:proto3.TestWrapper) */ {
 public:
  TestWrapper();
  virtual ~TestWrapper();

  TestWrapper(const TestWrapper& from);

  inline TestWrapper& operator=(const TestWrapper& from) {
    CopyFrom(from);
    return *this;
  }

  static const ::google::protobuf::Descriptor* descriptor();
  static const TestWrapper& default_instance();

  static const TestWrapper* internal_default_instance();

  void Swap(TestWrapper* other);

  // implements Message ----------------------------------------------

  inline TestWrapper* New() const { return New(NULL); }

  TestWrapper* New(::google::protobuf::Arena* arena) const;
  void CopyFrom(const ::google::protobuf::Message& from);
  void MergeFrom(const ::google::protobuf::Message& from);
  void CopyFrom(const TestWrapper& from);
  void MergeFrom(const TestWrapper& from);
  void Clear();
  bool IsInitialized() const;

  size_t ByteSizeLong() const;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input);
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const;
  ::google::protobuf::uint8* InternalSerializeWithCachedSizesToArray(
      bool deterministic, ::google::protobuf::uint8* output) const;
  ::google::protobuf::uint8* SerializeWithCachedSizesToArray(::google::protobuf::uint8* output) const {
    return InternalSerializeWithCachedSizesToArray(false, output);
  }
  int GetCachedSize() const { return _cached_size_; }
  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const;
  void InternalSwap(TestWrapper* other);
  void UnsafeMergeFrom(const TestWrapper& from);
  private:
  inline ::google::protobuf::Arena* GetArenaNoVirtual() const {
    return _internal_metadata_.arena();
  }
  inline void* MaybeArenaPtr() const {
    return _internal_metadata_.raw_arena_ptr();
  }
  public:

  ::google::protobuf::Metadata GetMetadata() const;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // optional .google.protobuf.BoolValue bool_value = 1;
  bool has_bool_value() const;
  void clear_bool_value();
  static const int kBoolValueFieldNumber = 1;
  const ::google::protobuf::BoolValue& bool_value() const;
  ::google::protobuf::BoolValue* mutable_bool_value();
  ::google::protobuf::BoolValue* release_bool_value();
  void set_allocated_bool_value(::google::protobuf::BoolValue* bool_value);

  // optional .google.protobuf.Int32Value int32_value = 2;
  bool has_int32_value() const;
  void clear_int32_value();
  static const int kInt32ValueFieldNumber = 2;
  const ::google::protobuf::Int32Value& int32_value() const;
  ::google::protobuf::Int32Value* mutable_int32_value();
  ::google::protobuf::Int32Value* release_int32_value();
  void set_allocated_int32_value(::google::protobuf::Int32Value* int32_value);

  // optional .google.protobuf.Int64Value int64_value = 3;
  bool has_int64_value() const;
  void clear_int64_value();
  static const int kInt64ValueFieldNumber = 3;
  const ::google::protobuf::Int64Value& int64_value() const;
  ::google::protobuf::Int64Value* mutable_int64_value();
  ::google::protobuf::Int64Value* release_int64_value();
  void set_allocated_int64_value(::google::protobuf::Int64Value* int64_value);

  // optional .google.protobuf.UInt32Value uint32_value = 4;
  bool has_uint32_value() const;
  void clear_uint32_value();
  static const int kUint32ValueFieldNumber = 4;
  const ::google::protobuf::UInt32Value& uint32_value() const;
  ::google::protobuf::UInt32Value* mutable_uint32_value();
  ::google::protobuf::UInt32Value* release_uint32_value();
  void set_allocated_uint32_value(::google::protobuf::UInt32Value* uint32_value);

  // optional .google.protobuf.UInt64Value uint64_value = 5;
  bool has_uint64_value() const;
  void clear_uint64_value();
  static const int kUint64ValueFieldNumber = 5;
  const ::google::protobuf::UInt64Value& uint64_value() const;
  ::google::protobuf::UInt64Value* mutable_uint64_value();
  ::google::protobuf::UInt64Value* release_uint64_value();
  void set_allocated_uint64_value(::google::protobuf::UInt64Value* uint64_value);

  // optional .google.protobuf.FloatValue float_value = 6;
  bool has_float_value() const;
  void clear_float_value();
  static const int kFloatValueFieldNumber = 6;
  const ::google::protobuf::FloatValue& float_value() const;
  ::google::protobuf::FloatValue* mutable_float_value();
  ::google::protobuf::FloatValue* release_float_value();
  void set_allocated_float_value(::google::protobuf::FloatValue* float_value);

  // optional .google.protobuf.DoubleValue double_value = 7;
  bool has_double_value() const;
  void clear_double_value();
  static const int kDoubleValueFieldNumber = 7;
  const ::google::protobuf::DoubleValue& double_value() const;
  ::google::protobuf::DoubleValue* mutable_double_value();
  ::google::protobuf::DoubleValue* release_double_value();
  void set_allocated_double_value(::google::protobuf::DoubleValue* double_value);

  // optional .google.protobuf.StringValue string_value = 8;
  bool has_string_value() const;
  void clear_string_value();
  static const int kStringValueFieldNumber = 8;
  const ::google::protobuf::StringValue& string_value() const;
  ::google::protobuf::StringValue* mutable_string_value();
  ::google::protobuf::StringValue* release_string_value();
  void set_allocated_string_value(::google::protobuf::StringValue* string_value);

  // optional .google.protobuf.BytesValue bytes_value = 9;
  bool has_bytes_value() const;
  void clear_bytes_value();
  static const int kBytesValueFieldNumber = 9;
  const ::google::protobuf::BytesValue& bytes_value() const;
  ::google::protobuf::BytesValue* mutable_bytes_value();
  ::google::protobuf::BytesValue* release_bytes_value();
  void set_allocated_bytes_value(::google::protobuf::BytesValue* bytes_value);

  // repeated .google.protobuf.BoolValue repeated_bool_value = 11;
  int repeated_bool_value_size() const;
  void clear_repeated_bool_value();
  static const int kRepeatedBoolValueFieldNumber = 11;
  const ::google::protobuf::BoolValue& repeated_bool_value(int index) const;
  ::google::protobuf::BoolValue* mutable_repeated_bool_value(int index);
  ::google::protobuf::BoolValue* add_repeated_bool_value();
  ::google::protobuf::RepeatedPtrField< ::google::protobuf::BoolValue >*
      mutable_repeated_bool_value();
  const ::google::protobuf::RepeatedPtrField< ::google::protobuf::BoolValue >&
      repeated_bool_value() const;

  // repeated .google.protobuf.Int32Value repeated_int32_value = 12;
  int repeated_int32_value_size() const;
  void clear_repeated_int32_value();
  static const int kRepeatedInt32ValueFieldNumber = 12;
  const ::google::protobuf::Int32Value& repeated_int32_value(int index) const;
  ::google::protobuf::Int32Value* mutable_repeated_int32_value(int index);
  ::google::protobuf::Int32Value* add_repeated_int32_value();
  ::google::protobuf::RepeatedPtrField< ::google::protobuf::Int32Value >*
      mutable_repeated_int32_value();
  const ::google::protobuf::RepeatedPtrField< ::google::protobuf::Int32Value >&
      repeated_int32_value() const;

  // repeated .google.protobuf.Int64Value repeated_int64_value = 13;
  int repeated_int64_value_size() const;
  void clear_repeated_int64_value();
  static const int kRepeatedInt64ValueFieldNumber = 13;
  const ::google::protobuf::Int64Value& repeated_int64_value(int index) const;
  ::google::protobuf::Int64Value* mutable_repeated_int64_value(int index);
  ::google::protobuf::Int64Value* add_repeated_int64_value();
  ::google::protobuf::RepeatedPtrField< ::google::protobuf::Int64Value >*
      mutable_repeated_int64_value();
  const ::google::protobuf::RepeatedPtrField< ::google::protobuf::Int64Value >&
      repeated_int64_value() const;

  // repeated .google.protobuf.UInt32Value repeated_uint32_value = 14;
  int repeated_uint32_value_size() const;
  void clear_repeated_uint32_value();
  static const int kRepeatedUint32ValueFieldNumber = 14;
  const ::google::protobuf::UInt32Value& repeated_uint32_value(int index) const;
  ::google::protobuf::UInt32Value* mutable_repeated_uint32_value(int index);
  ::google::protobuf::UInt32Value* add_repeated_uint32_value();
  ::google::protobuf::RepeatedPtrField< ::google::protobuf::UInt32Value >*
      mutable_repeated_uint32_value();
  const ::google::protobuf::RepeatedPtrField< ::google::protobuf::UInt32Value >&
      repeated_uint32_value() const;

  // repeated .google.protobuf.UInt64Value repeated_uint64_value = 15;
  int repeated_uint64_value_size() const;
  void clear_repeated_uint64_value();
  static const int kRepeatedUint64ValueFieldNumber = 15;
  const ::google::protobuf::UInt64Value& repeated_uint64_value(int index) const;
  ::google::protobuf::UInt64Value* mutable_repeated_uint64_value(int index);
  ::google::protobuf::UInt64Value* add_repeated_uint64_value();
  ::google::protobuf::RepeatedPtrField< ::google::protobuf::UInt64Value >*
      mutable_repeated_uint64_value();
  const ::google::protobuf::RepeatedPtrField< ::google::protobuf::UInt64Value >&
      repeated_uint64_value() const;

  // repeated .google.protobuf.FloatValue repeated_float_value = 16;
  int repeated_float_value_size() const;
  void clear_repeated_float_value();
  static const int kRepeatedFloatValueFieldNumber = 16;
  const ::google::protobuf::FloatValue& repeated_float_value(int index) const;
  ::google::protobuf::FloatValue* mutable_repeated_float_value(int index);
  ::google::protobuf::FloatValue* add_repeated_float_value();
  ::google::protobuf::RepeatedPtrField< ::google::protobuf::FloatValue >*
      mutable_repeated_float_value();
  const ::google::protobuf::RepeatedPtrField< ::google::protobuf::FloatValue >&
      repeated_float_value() const;

  // repeated .google.protobuf.DoubleValue repeated_double_value = 17;
  int repeated_double_value_size() const;
  void clear_repeated_double_value();
  static const int kRepeatedDoubleValueFieldNumber = 17;
  const ::google::protobuf::DoubleValue& repeated_double_value(int index) const;
  ::google::protobuf::DoubleValue* mutable_repeated_double_value(int index);
  ::google::protobuf::DoubleValue* add_repeated_double_value();
  ::google::protobuf::RepeatedPtrField< ::google::protobuf::DoubleValue >*
      mutable_repeated_double_value();
  const ::google::protobuf::RepeatedPtrField< ::google::protobuf::DoubleValue >&
      repeated_double_value() const;

  // repeated .google.protobuf.StringValue repeated_string_value = 18;
  int repeated_string_value_size() const;
  void clear_repeated_string_value();
  static const int kRepeatedStringValueFieldNumber = 18;
  const ::google::protobuf::StringValue& repeated_string_value(int index) const;
  ::google::protobuf::StringValue* mutable_repeated_string_value(int index);
  ::google::protobuf::StringValue* add_repeated_string_value();
  ::google::protobuf::RepeatedPtrField< ::google::protobuf::StringValue >*
      mutable_repeated_string_value();
  const ::google::protobuf::RepeatedPtrField< ::google::protobuf::StringValue >&
      repeated_string_value() const;

  // repeated .google.protobuf.BytesValue repeated_bytes_value = 19;
  int repeated_bytes_value_size() const;
  void clear_repeated_bytes_value();
  static const int kRepeatedBytesValueFieldNumber = 19;
  const ::google::protobuf::BytesValue& repeated_bytes_value(int index) const;
  ::google::protobuf::BytesValue* mutable_repeated_bytes_value(int index);
  ::google::protobuf::BytesValue* add_repeated_bytes_value();
  ::google::protobuf::RepeatedPtrField< ::google::protobuf::BytesValue >*
      mutable_repeated_bytes_value();
  const ::google::protobuf::RepeatedPtrField< ::google::protobuf::BytesValue >&
      repeated_bytes_value() const;

  // @@protoc_insertion_point(class_scope:proto3.TestWrapper)
 private:

  ::google::protobuf::internal::InternalMetadataWithArena _internal_metadata_;
  ::google::protobuf::RepeatedPtrField< ::google::protobuf::BoolValue > repeated_bool_value_;
  ::google::protobuf::RepeatedPtrField< ::google::protobuf::Int32Value > repeated_int32_value_;
  ::google::protobuf::RepeatedPtrField< ::google::protobuf::Int64Value > repeated_int64_value_;
  ::google::protobuf::RepeatedPtrField< ::google::protobuf::UInt32Value > repeated_uint32_value_;
  ::google::protobuf::RepeatedPtrField< ::google::protobuf::UInt64Value > repeated_uint64_value_;
  ::google::protobuf::RepeatedPtrField< ::google::protobuf::FloatValue > repeated_float_value_;
  ::google::protobuf::RepeatedPtrField< ::google::protobuf::DoubleValue > repeated_double_value_;
  ::google::protobuf::RepeatedPtrField< ::google::protobuf::StringValue > repeated_string_value_;
  ::google::protobuf::RepeatedPtrField< ::google::protobuf::BytesValue > repeated_bytes_value_;
  ::google::protobuf::BoolValue* bool_value_;
  ::google::protobuf::Int32Value* int32_value_;
  ::google::protobuf::Int64Value* int64_value_;
  ::google::protobuf::UInt32Value* uint32_value_;
  ::google::protobuf::UInt64Value* uint64_value_;
  ::google::protobuf::FloatValue* float_value_;
  ::google::protobuf::DoubleValue* double_value_;
  ::google::protobuf::StringValue* string_value_;
  ::google::protobuf::BytesValue* bytes_value_;
  mutable int _cached_size_;
  friend void  protobuf_InitDefaults_google_2fprotobuf_2futil_2fjson_5fformat_5fproto3_2eproto_impl();
  friend void  protobuf_AddDesc_google_2fprotobuf_2futil_2fjson_5fformat_5fproto3_2eproto_impl();
  friend void protobuf_AssignDesc_google_2fprotobuf_2futil_2fjson_5fformat_5fproto3_2eproto();
  friend void protobuf_ShutdownFile_google_2fprotobuf_2futil_2fjson_5fformat_5fproto3_2eproto();

  void InitAsDefaultInstance();
};
extern ::google::protobuf::internal::ExplicitlyConstructed<TestWrapper> TestWrapper_default_instance_;

// -------------------------------------------------------------------

class TestTimestamp : public ::google::protobuf::Message /* @@protoc_insertion_point(class_definition:proto3.TestTimestamp) */ {
 public:
  TestTimestamp();
  virtual ~TestTimestamp();

  TestTimestamp(const TestTimestamp& from);

  inline TestTimestamp& operator=(const TestTimestamp& from) {
    CopyFrom(from);
    return *this;
  }

  static const ::google::protobuf::Descriptor* descriptor();
  static const TestTimestamp& default_instance();

  static const TestTimestamp* internal_default_instance();

  void Swap(TestTimestamp* other);

  // implements Message ----------------------------------------------

  inline TestTimestamp* New() const { return New(NULL); }

  TestTimestamp* New(::google::protobuf::Arena* arena) const;
  void CopyFrom(const ::google::protobuf::Message& from);
  void MergeFrom(const ::google::protobuf::Message& from);
  void CopyFrom(const TestTimestamp& from);
  void MergeFrom(const TestTimestamp& from);
  void Clear();
  bool IsInitialized() const;

  size_t ByteSizeLong() const;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input);
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const;
  ::google::protobuf::uint8* InternalSerializeWithCachedSizesToArray(
      bool deterministic, ::google::protobuf::uint8* output) const;
  ::google::protobuf::uint8* SerializeWithCachedSizesToArray(::google::protobuf::uint8* output) const {
    return InternalSerializeWithCachedSizesToArray(false, output);
  }
  int GetCachedSize() const { return _cached_size_; }
  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const;
  void InternalSwap(TestTimestamp* other);
  void UnsafeMergeFrom(const TestTimestamp& from);
  private:
  inline ::google::protobuf::Arena* GetArenaNoVirtual() const {
    return _internal_metadata_.arena();
  }
  inline void* MaybeArenaPtr() const {
    return _internal_metadata_.raw_arena_ptr();
  }
  public:

  ::google::protobuf::Metadata GetMetadata() const;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // optional .google.protobuf.Timestamp value = 1;
  bool has_value() const;
  void clear_value();
  static const int kValueFieldNumber = 1;
  const ::google::protobuf::Timestamp& value() const;
  ::google::protobuf::Timestamp* mutable_value();
  ::google::protobuf::Timestamp* release_value();
  void set_allocated_value(::google::protobuf::Timestamp* value);

  // repeated .google.protobuf.Timestamp repeated_value = 2;
  int repeated_value_size() const;
  void clear_repeated_value();
  static const int kRepeatedValueFieldNumber = 2;
  const ::google::protobuf::Timestamp& repeated_value(int index) const;
  ::google::protobuf::Timestamp* mutable_repeated_value(int index);
  ::google::protobuf::Timestamp* add_repeated_value();
  ::google::protobuf::RepeatedPtrField< ::google::protobuf::Timestamp >*
      mutable_repeated_value();
  const ::google::protobuf::RepeatedPtrField< ::google::protobuf::Timestamp >&
      repeated_value() const;

  // @@protoc_insertion_point(class_scope:proto3.TestTimestamp)
 private:

  ::google::protobuf::internal::InternalMetadataWithArena _internal_metadata_;
  ::google::protobuf::RepeatedPtrField< ::google::protobuf::Timestamp > repeated_value_;
  ::google::protobuf::Timestamp* value_;
  mutable int _cached_size_;
  friend void  protobuf_InitDefaults_google_2fprotobuf_2futil_2fjson_5fformat_5fproto3_2eproto_impl();
  friend void  protobuf_AddDesc_google_2fprotobuf_2futil_2fjson_5fformat_5fproto3_2eproto_impl();
  friend void protobuf_AssignDesc_google_2fprotobuf_2futil_2fjson_5fformat_5fproto3_2eproto();
  friend void protobuf_ShutdownFile_google_2fprotobuf_2futil_2fjson_5fformat_5fproto3_2eproto();

  void InitAsDefaultInstance();
};
extern ::google::protobuf::internal::ExplicitlyConstructed<TestTimestamp> TestTimestamp_default_instance_;

// -------------------------------------------------------------------

class TestDuration : public ::google::protobuf::Message /* @@protoc_insertion_point(class_definition:proto3.TestDuration) */ {
 public:
  TestDuration();
  virtual ~TestDuration();

  TestDuration(const TestDuration& from);

  inline TestDuration& operator=(const TestDuration& from) {
    CopyFrom(from);
    return *this;
  }

  static const ::google::protobuf::Descriptor* descriptor();
  static const TestDuration& default_instance();

  static const TestDuration* internal_default_instance();

  void Swap(TestDuration* other);

  // implements Message ----------------------------------------------

  inline TestDuration* New() const { return New(NULL); }

  TestDuration* New(::google::protobuf::Arena* arena) const;
  void CopyFrom(const ::google::protobuf::Message& from);
  void MergeFrom(const ::google::protobuf::Message& from);
  void CopyFrom(const TestDuration& from);
  void MergeFrom(const TestDuration& from);
  void Clear();
  bool IsInitialized() const;

  size_t ByteSizeLong() const;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input);
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const;
  ::google::protobuf::uint8* InternalSerializeWithCachedSizesToArray(
      bool deterministic, ::google::protobuf::uint8* output) const;
  ::google::protobuf::uint8* SerializeWithCachedSizesToArray(::google::protobuf::uint8* output) const {
    return InternalSerializeWithCachedSizesToArray(false, output);
  }
  int GetCachedSize() const { return _cached_size_; }
  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const;
  void InternalSwap(TestDuration* other);
  void UnsafeMergeFrom(const TestDuration& from);
  private:
  inline ::google::protobuf::Arena* GetArenaNoVirtual() const {
    return _internal_metadata_.arena();
  }
  inline void* MaybeArenaPtr() const {
    return _internal_metadata_.raw_arena_ptr();
  }
  public:

  ::google::protobuf::Metadata GetMetadata() const;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // optional .google.protobuf.Duration value = 1;
  bool has_value() const;
  void clear_value();
  static const int kValueFieldNumber = 1;
  const ::google::protobuf::Duration& value() const;
  ::google::protobuf::Duration* mutable_value();
  ::google::protobuf::Duration* release_value();
  void set_allocated_value(::google::protobuf::Duration* value);

  // repeated .google.protobuf.Duration repeated_value = 2;
  int repeated_value_size() const;
  void clear_repeated_value();
  static const int kRepeatedValueFieldNumber = 2;
  const ::google::protobuf::Duration& repeated_value(int index) const;
  ::google::protobuf::Duration* mutable_repeated_value(int index);
  ::google::protobuf::Duration* add_repeated_value();
  ::google::protobuf::RepeatedPtrField< ::google::protobuf::Duration >*
      mutable_repeated_value();
  const ::google::protobuf::RepeatedPtrField< ::google::protobuf::Duration >&
      repeated_value() const;

  // @@protoc_insertion_point(class_scope:proto3.TestDuration)
 private:

  ::google::protobuf::internal::InternalMetadataWithArena _internal_metadata_;
  ::google::protobuf::RepeatedPtrField< ::google::protobuf::Duration > repeated_value_;
  ::google::protobuf::Duration* value_;
  mutable int _cached_size_;
  friend void  protobuf_InitDefaults_google_2fprotobuf_2futil_2fjson_5fformat_5fproto3_2eproto_impl();
  friend void  protobuf_AddDesc_google_2fprotobuf_2futil_2fjson_5fformat_5fproto3_2eproto_impl();
  friend void protobuf_AssignDesc_google_2fprotobuf_2futil_2fjson_5fformat_5fproto3_2eproto();
  friend void protobuf_ShutdownFile_google_2fprotobuf_2futil_2fjson_5fformat_5fproto3_2eproto();

  void InitAsDefaultInstance();
};
extern ::google::protobuf::internal::ExplicitlyConstructed<TestDuration> TestDuration_default_instance_;

// -------------------------------------------------------------------

class TestFieldMask : public ::google::protobuf::Message /* @@protoc_insertion_point(class_definition:proto3.TestFieldMask) */ {
 public:
  TestFieldMask();
  virtual ~TestFieldMask();

  TestFieldMask(const TestFieldMask& from);

  inline TestFieldMask& operator=(const TestFieldMask& from) {
    CopyFrom(from);
    return *this;
  }

  static const ::google::protobuf::Descriptor* descriptor();
  static const TestFieldMask& default_instance();

  static const TestFieldMask* internal_default_instance();

  void Swap(TestFieldMask* other);

  // implements Message ----------------------------------------------

  inline TestFieldMask* New() const { return New(NULL); }

  TestFieldMask* New(::google::protobuf::Arena* arena) const;
  void CopyFrom(const ::google::protobuf::Message& from);
  void MergeFrom(const ::google::protobuf::Message& from);
  void CopyFrom(const TestFieldMask& from);
  void MergeFrom(const TestFieldMask& from);
  void Clear();
  bool IsInitialized() const;

  size_t ByteSizeLong() const;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input);
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const;
  ::google::protobuf::uint8* InternalSerializeWithCachedSizesToArray(
      bool deterministic, ::google::protobuf::uint8* output) const;
  ::google::protobuf::uint8* SerializeWithCachedSizesToArray(::google::protobuf::uint8* output) const {
    return InternalSerializeWithCachedSizesToArray(false, output);
  }
  int GetCachedSize() const { return _cached_size_; }
  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const;
  void InternalSwap(TestFieldMask* other);
  void UnsafeMergeFrom(const TestFieldMask& from);
  private:
  inline ::google::protobuf::Arena* GetArenaNoVirtual() const {
    return _internal_metadata_.arena();
  }
  inline void* MaybeArenaPtr() const {
    return _internal_metadata_.raw_arena_ptr();
  }
  public:

  ::google::protobuf::Metadata GetMetadata() const;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // optional .google.protobuf.FieldMask value = 1;
  bool has_value() const;
  void clear_value();
  static const int kValueFieldNumber = 1;
  const ::google::protobuf::FieldMask& value() const;
  ::google::protobuf::FieldMask* mutable_value();
  ::google::protobuf::FieldMask* release_value();
  void set_allocated_value(::google::protobuf::FieldMask* value);

  // @@protoc_insertion_point(class_scope:proto3.TestFieldMask)
 private:

  ::google::protobuf::internal::InternalMetadataWithArena _internal_metadata_;
  ::google::protobuf::FieldMask* value_;
  mutable int _cached_size_;
  friend void  protobuf_InitDefaults_google_2fprotobuf_2futil_2fjson_5fformat_5fproto3_2eproto_impl();
  friend void  protobuf_AddDesc_google_2fprotobuf_2futil_2fjson_5fformat_5fproto3_2eproto_impl();
  friend void protobuf_AssignDesc_google_2fprotobuf_2futil_2fjson_5fformat_5fproto3_2eproto();
  friend void protobuf_ShutdownFile_google_2fprotobuf_2futil_2fjson_5fformat_5fproto3_2eproto();

  void InitAsDefaultInstance();
};
extern ::google::protobuf::internal::ExplicitlyConstructed<TestFieldMask> TestFieldMask_default_instance_;

// -------------------------------------------------------------------

class TestStruct : public ::google::protobuf::Message /* @@protoc_insertion_point(class_definition:proto3.TestStruct) */ {
 public:
  TestStruct();
  virtual ~TestStruct();

  TestStruct(const TestStruct& from);

  inline TestStruct& operator=(const TestStruct& from) {
    CopyFrom(from);
    return *this;
  }

  static const ::google::protobuf::Descriptor* descriptor();
  static const TestStruct& default_instance();

  static const TestStruct* internal_default_instance();

  void Swap(TestStruct* other);

  // implements Message ----------------------------------------------

  inline TestStruct* New() const { return New(NULL); }

  TestStruct* New(::google::protobuf::Arena* arena) const;
  void CopyFrom(const ::google::protobuf::Message& from);
  void MergeFrom(const ::google::protobuf::Message& from);
  void CopyFrom(const TestStruct& from);
  void MergeFrom(const TestStruct& from);
  void Clear();
  bool IsInitialized() const;

  size_t ByteSizeLong() const;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input);
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const;
  ::google::protobuf::uint8* InternalSerializeWithCachedSizesToArray(
      bool deterministic, ::google::protobuf::uint8* output) const;
  ::google::protobuf::uint8* SerializeWithCachedSizesToArray(::google::protobuf::uint8* output) const {
    return InternalSerializeWithCachedSizesToArray(false, output);
  }
  int GetCachedSize() const { return _cached_size_; }
  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const;
  void InternalSwap(TestStruct* other);
  void UnsafeMergeFrom(const TestStruct& from);
  private:
  inline ::google::protobuf::Arena* GetArenaNoVirtual() const {
    return _internal_metadata_.arena();
  }
  inline void* MaybeArenaPtr() const {
    return _internal_metadata_.raw_arena_ptr();
  }
  public:

  ::google::protobuf::Metadata GetMetadata() const;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // optional .google.protobuf.Struct value = 1;
  bool has_value() const;
  void clear_value();
  static const int kValueFieldNumber = 1;
  const ::google::protobuf::Struct& value() const;
  ::google::protobuf::Struct* mutable_value();
  ::google::protobuf::Struct* release_value();
  void set_allocated_value(::google::protobuf::Struct* value);

  // repeated .google.protobuf.Struct repeated_value = 2;
  int repeated_value_size() const;
  void clear_repeated_value();
  static const int kRepeatedValueFieldNumber = 2;
  const ::google::protobuf::Struct& repeated_value(int index) const;
  ::google::protobuf::Struct* mutable_repeated_value(int index);
  ::google::protobuf::Struct* add_repeated_value();
  ::google::protobuf::RepeatedPtrField< ::google::protobuf::Struct >*
      mutable_repeated_value();
  const ::google::protobuf::RepeatedPtrField< ::google::protobuf::Struct >&
      repeated_value() const;

  // @@protoc_insertion_point(class_scope:proto3.TestStruct)
 private:

  ::google::protobuf::internal::InternalMetadataWithArena _internal_metadata_;
  ::google::protobuf::RepeatedPtrField< ::google::protobuf::Struct > repeated_value_;
  ::google::protobuf::Struct* value_;
  mutable int _cached_size_;
  friend void  protobuf_InitDefaults_google_2fprotobuf_2futil_2fjson_5fformat_5fproto3_2eproto_impl();
  friend void  protobuf_AddDesc_google_2fprotobuf_2futil_2fjson_5fformat_5fproto3_2eproto_impl();
  friend void protobuf_AssignDesc_google_2fprotobuf_2futil_2fjson_5fformat_5fproto3_2eproto();
  friend void protobuf_ShutdownFile_google_2fprotobuf_2futil_2fjson_5fformat_5fproto3_2eproto();

  void InitAsDefaultInstance();
};
extern ::google::protobuf::internal::ExplicitlyConstructed<TestStruct> TestStruct_default_instance_;

// -------------------------------------------------------------------

class TestAny : public ::google::protobuf::Message /* @@protoc_insertion_point(class_definition:proto3.TestAny) */ {
 public:
  TestAny();
  virtual ~TestAny();

  TestAny(const TestAny& from);

  inline TestAny& operator=(const TestAny& from) {
    CopyFrom(from);
    return *this;
  }

  static const ::google::protobuf::Descriptor* descriptor();
  static const TestAny& default_instance();

  static const TestAny* internal_default_instance();

  void Swap(TestAny* other);

  // implements Message ----------------------------------------------

  inline TestAny* New() const { return New(NULL); }

  TestAny* New(::google::protobuf::Arena* arena) const;
  void CopyFrom(const ::google::protobuf::Message& from);
  void MergeFrom(const ::google::protobuf::Message& from);
  void CopyFrom(const TestAny& from);
  void MergeFrom(const TestAny& from);
  void Clear();
  bool IsInitialized() const;

  size_t ByteSizeLong() const;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input);
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const;
  ::google::protobuf::uint8* InternalSerializeWithCachedSizesToArray(
      bool deterministic, ::google::protobuf::uint8* output) const;
  ::google::protobuf::uint8* SerializeWithCachedSizesToArray(::google::protobuf::uint8* output) const {
    return InternalSerializeWithCachedSizesToArray(false, output);
  }
  int GetCachedSize() const { return _cached_size_; }
  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const;
  void InternalSwap(TestAny* other);
  void UnsafeMergeFrom(const TestAny& from);
  private:
  inline ::google::protobuf::Arena* GetArenaNoVirtual() const {
    return _internal_metadata_.arena();
  }
  inline void* MaybeArenaPtr() const {
    return _internal_metadata_.raw_arena_ptr();
  }
  public:

  ::google::protobuf::Metadata GetMetadata() const;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // optional .google.protobuf.Any value = 1;
  bool has_value() const;
  void clear_value();
  static const int kValueFieldNumber = 1;
  const ::google::protobuf::Any& value() const;
  ::google::protobuf::Any* mutable_value();
  ::google::protobuf::Any* release_value();
  void set_allocated_value(::google::protobuf::Any* value);

  // repeated .google.protobuf.Any repeated_value = 2;
  int repeated_value_size() const;
  void clear_repeated_value();
  static const int kRepeatedValueFieldNumber = 2;
  const ::google::protobuf::Any& repeated_value(int index) const;
  ::google::protobuf::Any* mutable_repeated_value(int index);
  ::google::protobuf::Any* add_repeated_value();
  ::google::protobuf::RepeatedPtrField< ::google::protobuf::Any >*
      mutable_repeated_value();
  const ::google::protobuf::RepeatedPtrField< ::google::protobuf::Any >&
      repeated_value() const;

  // @@protoc_insertion_point(class_scope:proto3.TestAny)
 private:

  ::google::protobuf::internal::InternalMetadataWithArena _internal_metadata_;
  ::google::protobuf::RepeatedPtrField< ::google::protobuf::Any > repeated_value_;
  ::google::protobuf::Any* value_;
  mutable int _cached_size_;
  friend void  protobuf_InitDefaults_google_2fprotobuf_2futil_2fjson_5fformat_5fproto3_2eproto_impl();
  friend void  protobuf_AddDesc_google_2fprotobuf_2futil_2fjson_5fformat_5fproto3_2eproto_impl();
  friend void protobuf_AssignDesc_google_2fprotobuf_2futil_2fjson_5fformat_5fproto3_2eproto();
  friend void protobuf_ShutdownFile_google_2fprotobuf_2futil_2fjson_5fformat_5fproto3_2eproto();

  void InitAsDefaultInstance();
};
extern ::google::protobuf::internal::ExplicitlyConstructed<TestAny> TestAny_default_instance_;

// -------------------------------------------------------------------

class TestValue : public ::google::protobuf::Message /* @@protoc_insertion_point(class_definition:proto3.TestValue) */ {
 public:
  TestValue();
  virtual ~TestValue();

  TestValue(const TestValue& from);

  inline TestValue& operator=(const TestValue& from) {
    CopyFrom(from);
    return *this;
  }

  static const ::google::protobuf::Descriptor* descriptor();
  static const TestValue& default_instance();

  static const TestValue* internal_default_instance();

  void Swap(TestValue* other);

  // implements Message ----------------------------------------------

  inline TestValue* New() const { return New(NULL); }

  TestValue* New(::google::protobuf::Arena* arena) const;
  void CopyFrom(const ::google::protobuf::Message& from);
  void MergeFrom(const ::google::protobuf::Message& from);
  void CopyFrom(const TestValue& from);
  void MergeFrom(const TestValue& from);
  void Clear();
  bool IsInitialized() const;

  size_t ByteSizeLong() const;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input);
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const;
  ::google::protobuf::uint8* InternalSerializeWithCachedSizesToArray(
      bool deterministic, ::google::protobuf::uint8* output) const;
  ::google::protobuf::uint8* SerializeWithCachedSizesToArray(::google::protobuf::uint8* output) const {
    return InternalSerializeWithCachedSizesToArray(false, output);
  }
  int GetCachedSize() const { return _cached_size_; }
  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const;
  void InternalSwap(TestValue* other);
  void UnsafeMergeFrom(const TestValue& from);
  private:
  inline ::google::protobuf::Arena* GetArenaNoVirtual() const {
    return _internal_metadata_.arena();
  }
  inline void* MaybeArenaPtr() const {
    return _internal_metadata_.raw_arena_ptr();
  }
  public:

  ::google::protobuf::Metadata GetMetadata() const;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // optional .google.protobuf.Value value = 1;
  bool has_value() const;
  void clear_value();
  static const int kValueFieldNumber = 1;
  const ::google::protobuf::Value& value() const;
  ::google::protobuf::Value* mutable_value();
  ::google::protobuf::Value* release_value();
  void set_allocated_value(::google::protobuf::Value* value);

  // repeated .google.protobuf.Value repeated_value = 2;
  int repeated_value_size() const;
  void clear_repeated_value();
  static const int kRepeatedValueFieldNumber = 2;
  const ::google::protobuf::Value& repeated_value(int index) const;
  ::google::protobuf::Value* mutable_repeated_value(int index);
  ::google::protobuf::Value* add_repeated_value();
  ::google::protobuf::RepeatedPtrField< ::google::protobuf::Value >*
      mutable_repeated_value();
  const ::google::protobuf::RepeatedPtrField< ::google::protobuf::Value >&
      repeated_value() const;

  // @@protoc_insertion_point(class_scope:proto3.TestValue)
 private:

  ::google::protobuf::internal::InternalMetadataWithArena _internal_metadata_;
  ::google::protobuf::RepeatedPtrField< ::google::protobuf::Value > repeated_value_;
  ::google::protobuf::Value* value_;
  mutable int _cached_size_;
  friend void  protobuf_InitDefaults_google_2fprotobuf_2futil_2fjson_5fformat_5fproto3_2eproto_impl();
  friend void  protobuf_AddDesc_google_2fprotobuf_2futil_2fjson_5fformat_5fproto3_2eproto_impl();
  friend void protobuf_AssignDesc_google_2fprotobuf_2futil_2fjson_5fformat_5fproto3_2eproto();
  friend void protobuf_ShutdownFile_google_2fprotobuf_2futil_2fjson_5fformat_5fproto3_2eproto();

  void InitAsDefaultInstance();
};
extern ::google::protobuf::internal::ExplicitlyConstructed<TestValue> TestValue_default_instance_;

// -------------------------------------------------------------------

class TestListValue : public ::google::protobuf::Message /* @@protoc_insertion_point(class_definition:proto3.TestListValue) */ {
 public:
  TestListValue();
  virtual ~TestListValue();

  TestListValue(const TestListValue& from);

  inline TestListValue& operator=(const TestListValue& from) {
    CopyFrom(from);
    return *this;
  }

  static const ::google::protobuf::Descriptor* descriptor();
  static const TestListValue& default_instance();

  static const TestListValue* internal_default_instance();

  void Swap(TestListValue* other);

  // implements Message ----------------------------------------------

  inline TestListValue* New() const { return New(NULL); }

  TestListValue* New(::google::protobuf::Arena* arena) const;
  void CopyFrom(const ::google::protobuf::Message& from);
  void MergeFrom(const ::google::protobuf::Message& from);
  void CopyFrom(const TestListValue& from);
  void MergeFrom(const TestListValue& from);
  void Clear();
  bool IsInitialized() const;

  size_t ByteSizeLong() const;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input);
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const;
  ::google::protobuf::uint8* InternalSerializeWithCachedSizesToArray(
      bool deterministic, ::google::protobuf::uint8* output) const;
  ::google::protobuf::uint8* SerializeWithCachedSizesToArray(::google::protobuf::uint8* output) const {
    return InternalSerializeWithCachedSizesToArray(false, output);
  }
  int GetCachedSize() const { return _cached_size_; }
  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const;
  void InternalSwap(TestListValue* other);
  void UnsafeMergeFrom(const TestListValue& from);
  private:
  inline ::google::protobuf::Arena* GetArenaNoVirtual() const {
    return _internal_metadata_.arena();
  }
  inline void* MaybeArenaPtr() const {
    return _internal_metadata_.raw_arena_ptr();
  }
  public:

  ::google::protobuf::Metadata GetMetadata() const;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // optional .google.protobuf.ListValue value = 1;
  bool has_value() const;
  void clear_value();
  static const int kValueFieldNumber = 1;
  const ::google::protobuf::ListValue& value() const;
  ::google::protobuf::ListValue* mutable_value();
  ::google::protobuf::ListValue* release_value();
  void set_allocated_value(::google::protobuf::ListValue* value);

  // repeated .google.protobuf.ListValue repeated_value = 2;
  int repeated_value_size() const;
  void clear_repeated_value();
  static const int kRepeatedValueFieldNumber = 2;
  const ::google::protobuf::ListValue& repeated_value(int index) const;
  ::google::protobuf::ListValue* mutable_repeated_value(int index);
  ::google::protobuf::ListValue* add_repeated_value();
  ::google::protobuf::RepeatedPtrField< ::google::protobuf::ListValue >*
      mutable_repeated_value();
  const ::google::protobuf::RepeatedPtrField< ::google::protobuf::ListValue >&
      repeated_value() const;

  // @@protoc_insertion_point(class_scope:proto3.TestListValue)
 private:

  ::google::protobuf::internal::InternalMetadataWithArena _internal_metadata_;
  ::google::protobuf::RepeatedPtrField< ::google::protobuf::ListValue > repeated_value_;
  ::google::protobuf::ListValue* value_;
  mutable int _cached_size_;
  friend void  protobuf_InitDefaults_google_2fprotobuf_2futil_2fjson_5fformat_5fproto3_2eproto_impl();
  friend void  protobuf_AddDesc_google_2fprotobuf_2futil_2fjson_5fformat_5fproto3_2eproto_impl();
  friend void protobuf_AssignDesc_google_2fprotobuf_2futil_2fjson_5fformat_5fproto3_2eproto();
  friend void protobuf_ShutdownFile_google_2fprotobuf_2futil_2fjson_5fformat_5fproto3_2eproto();

  void InitAsDefaultInstance();
};
extern ::google::protobuf::internal::ExplicitlyConstructed<TestListValue> TestListValue_default_instance_;

// -------------------------------------------------------------------

class TestBoolValue : public ::google::protobuf::Message /* @@protoc_insertion_point(class_definition:proto3.TestBoolValue) */ {
 public:
  TestBoolValue();
  virtual ~TestBoolValue();

  TestBoolValue(const TestBoolValue& from);

  inline TestBoolValue& operator=(const TestBoolValue& from) {
    CopyFrom(from);
    return *this;
  }

  static const ::google::protobuf::Descriptor* descriptor();
  static const TestBoolValue& default_instance();

  static const TestBoolValue* internal_default_instance();

  void Swap(TestBoolValue* other);

  // implements Message ----------------------------------------------

  inline TestBoolValue* New() const { return New(NULL); }

  TestBoolValue* New(::google::protobuf::Arena* arena) const;
  void CopyFrom(const ::google::protobuf::Message& from);
  void MergeFrom(const ::google::protobuf::Message& from);
  void CopyFrom(const TestBoolValue& from);
  void MergeFrom(const TestBoolValue& from);
  void Clear();
  bool IsInitialized() const;

  size_t ByteSizeLong() const;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input);
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const;
  ::google::protobuf::uint8* InternalSerializeWithCachedSizesToArray(
      bool deterministic, ::google::protobuf::uint8* output) const;
  ::google::protobuf::uint8* SerializeWithCachedSizesToArray(::google::protobuf::uint8* output) const {
    return InternalSerializeWithCachedSizesToArray(false, output);
  }
  int GetCachedSize() const { return _cached_size_; }
  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const;
  void InternalSwap(TestBoolValue* other);
  void UnsafeMergeFrom(const TestBoolValue& from);
  private:
  inline ::google::protobuf::Arena* GetArenaNoVirtual() const {
    return _internal_metadata_.arena();
  }
  inline void* MaybeArenaPtr() const {
    return _internal_metadata_.raw_arena_ptr();
  }
  public:

  ::google::protobuf::Metadata GetMetadata() const;

  // nested types ----------------------------------------------------


  // accessors -------------------------------------------------------

  // optional bool bool_value = 1;
  void clear_bool_value();
  static const int kBoolValueFieldNumber = 1;
  bool bool_value() const;
  void set_bool_value(bool value);

  // map<bool, int32> bool_map = 2;
  int bool_map_size() const;
  void clear_bool_map();
  static const int kBoolMapFieldNumber = 2;
  const ::google::protobuf::Map< bool, ::google::protobuf::int32 >&
      bool_map() const;
  ::google::protobuf::Map< bool, ::google::protobuf::int32 >*
      mutable_bool_map();

  // @@protoc_insertion_point(class_scope:proto3.TestBoolValue)
 private:

  ::google::protobuf::internal::InternalMetadataWithArena _internal_metadata_;
  typedef ::google::protobuf::internal::MapEntryLite<
      bool, ::google::protobuf::int32,
      ::google::protobuf::internal::WireFormatLite::TYPE_BOOL,
      ::google::protobuf::internal::WireFormatLite::TYPE_INT32,
      0 >
      TestBoolValue_BoolMapEntry;
  ::google::protobuf::internal::MapField<
      bool, ::google::protobuf::int32,
      ::google::protobuf::internal::WireFormatLite::TYPE_BOOL,
      ::google::protobuf::internal::WireFormatLite::TYPE_INT32,
      0 > bool_map_;
  bool bool_value_;
  mutable int _cached_size_;
  friend void  protobuf_InitDefaults_google_2fprotobuf_2futil_2fjson_5fformat_5fproto3_2eproto_impl();
  friend void  protobuf_AddDesc_google_2fprotobuf_2futil_2fjson_5fformat_5fproto3_2eproto_impl();
  friend void protobuf_AssignDesc_google_2fprotobuf_2futil_2fjson_5fformat_5fproto3_2eproto();
  friend void protobuf_ShutdownFile_google_2fprotobuf_2futil_2fjson_5fformat_5fproto3_2eproto();

  void InitAsDefaultInstance();
};
extern ::google::protobuf::internal::ExplicitlyConstructed<TestBoolValue> TestBoolValue_default_instance_;

// -------------------------------------------------------------------

class TestCustomJsonName : public ::google::protobuf::Message /* @@protoc_insertion_point(class_definition:proto3.TestCustomJsonName) */ {
 public:
  TestCustomJsonName();
  virtual ~TestCustomJsonName();

  TestCustomJsonName(const TestCustomJsonName& from);

  inline TestCustomJsonName& operator=(const TestCustomJsonName& from) {
    CopyFrom(from);
    return *this;
  }

  static const ::google::protobuf::Descriptor* descriptor();
  static const TestCustomJsonName& default_instance();

  static const TestCustomJsonName* internal_default_instance();

  void Swap(TestCustomJsonName* other);

  // implements Message ----------------------------------------------

  inline TestCustomJsonName* New() const { return New(NULL); }

  TestCustomJsonName* New(::google::protobuf::Arena* arena) const;
  void CopyFrom(const ::google::protobuf::Message& from);
  void MergeFrom(const ::google::protobuf::Message& from);
  void CopyFrom(const TestCustomJsonName& from);
  void MergeFrom(const TestCustomJsonName& from);
  void Clear();
  bool IsInitialized() const;

  size_t ByteSizeLong() const;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input);
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const;
  ::google::protobuf::uint8* InternalSerializeWithCachedSizesToArray(
      bool deterministic, ::google::protobuf::uint8* output) const;
  ::google::protobuf::uint8* SerializeWithCachedSizesToArray(::google::protobuf::uint8* output) const {
    return InternalSerializeWithCachedSizesToArray(false, output);
  }
  int GetCachedSize() const { return _cached_size_; }
  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const;
  void InternalSwap(TestCustomJsonName* other);
  void UnsafeMergeFrom(const TestCustomJsonName& from);
  private:
  inline ::google::protobuf::Arena* GetArenaNoVirtual() const {
    return _internal_metadata_.arena();
  }
  inline void* MaybeArenaPtr() const {
    return _internal_metadata_.raw_arena_ptr();
  }
  public:

  ::google::protobuf::Metadata GetMetadata() const;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // optional int32 value = 1[json_name = "@value"];
  void clear_value();
  static const int kValueFieldNumber = 1;
  ::google::protobuf::int32 value() const;
  void set_value(::google::protobuf::int32 value);

  // @@protoc_insertion_point(class_scope:proto3.TestCustomJsonName)
 private:

  ::google::protobuf::internal::InternalMetadataWithArena _internal_metadata_;
  ::google::protobuf::int32 value_;
  mutable int _cached_size_;
  friend void  protobuf_InitDefaults_google_2fprotobuf_2futil_2fjson_5fformat_5fproto3_2eproto_impl();
  friend void  protobuf_AddDesc_google_2fprotobuf_2futil_2fjson_5fformat_5fproto3_2eproto_impl();
  friend void protobuf_AssignDesc_google_2fprotobuf_2futil_2fjson_5fformat_5fproto3_2eproto();
  friend void protobuf_ShutdownFile_google_2fprotobuf_2futil_2fjson_5fformat_5fproto3_2eproto();

  void InitAsDefaultInstance();
};
extern ::google::protobuf::internal::ExplicitlyConstructed<TestCustomJsonName> TestCustomJsonName_default_instance_;

// -------------------------------------------------------------------

class TestExtensions : public ::google::protobuf::Message /* @@protoc_insertion_point(class_definition:proto3.TestExtensions) */ {
 public:
  TestExtensions();
  virtual ~TestExtensions();

  TestExtensions(const TestExtensions& from);

  inline TestExtensions& operator=(const TestExtensions& from) {
    CopyFrom(from);
    return *this;
  }

  static const ::google::protobuf::Descriptor* descriptor();
  static const TestExtensions& default_instance();

  static const TestExtensions* internal_default_instance();

  void Swap(TestExtensions* other);

  // implements Message ----------------------------------------------

  inline TestExtensions* New() const { return New(NULL); }

  TestExtensions* New(::google::protobuf::Arena* arena) const;
  void CopyFrom(const ::google::protobuf::Message& from);
  void MergeFrom(const ::google::protobuf::Message& from);
  void CopyFrom(const TestExtensions& from);
  void MergeFrom(const TestExtensions& from);
  void Clear();
  bool IsInitialized() const;

  size_t ByteSizeLong() const;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input);
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const;
  ::google::protobuf::uint8* InternalSerializeWithCachedSizesToArray(
      bool deterministic, ::google::protobuf::uint8* output) const;
  ::google::protobuf::uint8* SerializeWithCachedSizesToArray(::google::protobuf::uint8* output) const {
    return InternalSerializeWithCachedSizesToArray(false, output);
  }
  int GetCachedSize() const { return _cached_size_; }
  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const;
  void InternalSwap(TestExtensions* other);
  void UnsafeMergeFrom(const TestExtensions& from);
  private:
  inline ::google::protobuf::Arena* GetArenaNoVirtual() const {
    return _internal_metadata_.arena();
  }
  inline void* MaybeArenaPtr() const {
    return _internal_metadata_.raw_arena_ptr();
  }
  public:

  ::google::protobuf::Metadata GetMetadata() const;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // optional .protobuf_unittest.TestAllExtensions extensions = 1;
  bool has_extensions() const;
  void clear_extensions();
  static const int kExtensionsFieldNumber = 1;
  const ::protobuf_unittest::TestAllExtensions& extensions() const;
  ::protobuf_unittest::TestAllExtensions* mutable_extensions();
  ::protobuf_unittest::TestAllExtensions* release_extensions();
  void set_allocated_extensions(::protobuf_unittest::TestAllExtensions* extensions);

  // @@protoc_insertion_point(class_scope:proto3.TestExtensions)
 private:

  ::google::protobuf::internal::InternalMetadataWithArena _internal_metadata_;
  ::protobuf_unittest::TestAllExtensions* extensions_;
  mutable int _cached_size_;
  friend void  protobuf_InitDefaults_google_2fprotobuf_2futil_2fjson_5fformat_5fproto3_2eproto_impl();
  friend void  protobuf_AddDesc_google_2fprotobuf_2futil_2fjson_5fformat_5fproto3_2eproto_impl();
  friend void protobuf_AssignDesc_google_2fprotobuf_2futil_2fjson_5fformat_5fproto3_2eproto();
  friend void protobuf_ShutdownFile_google_2fprotobuf_2futil_2fjson_5fformat_5fproto3_2eproto();

  void InitAsDefaultInstance();
};
extern ::google::protobuf::internal::ExplicitlyConstructed<TestExtensions> TestExtensions_default_instance_;

// ===================================================================


// ===================================================================

#if !PROTOBUF_INLINE_NOT_IN_HEADERS
// MessageType

// optional int32 value = 1;
inline void MessageType::clear_value() {
  value_ = 0;
}
inline ::google::protobuf::int32 MessageType::value() const {
  // @@protoc_insertion_point(field_get:proto3.MessageType.value)
  return value_;
}
inline void MessageType::set_value(::google::protobuf::int32 value) {
  
  value_ = value;
  // @@protoc_insertion_point(field_set:proto3.MessageType.value)
}

inline const MessageType* MessageType::internal_default_instance() {
  return &MessageType_default_instance_.get();
}
// -------------------------------------------------------------------

// TestMessage

// optional bool bool_value = 1;
inline void TestMessage::clear_bool_value() {
  bool_value_ = false;
}
inline bool TestMessage::bool_value() const {
  // @@protoc_insertion_point(field_get:proto3.TestMessage.bool_value)
  return bool_value_;
}
inline void TestMessage::set_bool_value(bool value) {
  
  bool_value_ = value;
  // @@protoc_insertion_point(field_set:proto3.TestMessage.bool_value)
}

// optional int32 int32_value = 2;
inline void TestMessage::clear_int32_value() {
  int32_value_ = 0;
}
inline ::google::protobuf::int32 TestMessage::int32_value() const {
  // @@protoc_insertion_point(field_get:proto3.TestMessage.int32_value)
  return int32_value_;
}
inline void TestMessage::set_int32_value(::google::protobuf::int32 value) {
  
  int32_value_ = value;
  // @@protoc_insertion_point(field_set:proto3.TestMessage.int32_value)
}

// optional int64 int64_value = 3;
inline void TestMessage::clear_int64_value() {
  int64_value_ = GOOGLE_LONGLONG(0);
}
inline ::google::protobuf::int64 TestMessage::int64_value() const {
  // @@protoc_insertion_point(field_get:proto3.TestMessage.int64_value)
  return int64_value_;
}
inline void TestMessage::set_int64_value(::google::protobuf::int64 value) {
  
  int64_value_ = value;
  // @@protoc_insertion_point(field_set:proto3.TestMessage.int64_value)
}

// optional uint32 uint32_value = 4;
inline void TestMessage::clear_uint32_value() {
  uint32_value_ = 0u;
}
inline ::google::protobuf::uint32 TestMessage::uint32_value() const {
  // @@protoc_insertion_point(field_get:proto3.TestMessage.uint32_value)
  return uint32_value_;
}
inline void TestMessage::set_uint32_value(::google::protobuf::uint32 value) {
  
  uint32_value_ = value;
  // @@protoc_insertion_point(field_set:proto3.TestMessage.uint32_value)
}

// optional uint64 uint64_value = 5;
inline void TestMessage::clear_uint64_value() {
  uint64_value_ = GOOGLE_ULONGLONG(0);
}
inline ::google::protobuf::uint64 TestMessage::uint64_value() const {
  // @@protoc_insertion_point(field_get:proto3.TestMessage.uint64_value)
  return uint64_value_;
}
inline void TestMessage::set_uint64_value(::google::protobuf::uint64 value) {
  
  uint64_value_ = value;
  // @@protoc_insertion_point(field_set:proto3.TestMessage.uint64_value)
}

// optional float float_value = 6;
inline void TestMessage::clear_float_value() {
  float_value_ = 0;
}
inline float TestMessage::float_value() const {
  // @@protoc_insertion_point(field_get:proto3.TestMessage.float_value)
  return float_value_;
}
inline void TestMessage::set_float_value(float value) {
  
  float_value_ = value;
  // @@protoc_insertion_point(field_set:proto3.TestMessage.float_value)
}

// optional double double_value = 7;
inline void TestMessage::clear_double_value() {
  double_value_ = 0;
}
inline double TestMessage::double_value() const {
  // @@protoc_insertion_point(field_get:proto3.TestMessage.double_value)
  return double_value_;
}
inline void TestMessage::set_double_value(double value) {
  
  double_value_ = value;
  // @@protoc_insertion_point(field_set:proto3.TestMessage.double_value)
}

// optional string string_value = 8;
inline void TestMessage::clear_string_value() {
  string_value_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline const ::std::string& TestMessage::string_value() const {
  // @@protoc_insertion_point(field_get:proto3.TestMessage.string_value)
  return string_value_.GetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void TestMessage::set_string_value(const ::std::string& value) {
  
  string_value_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:proto3.TestMessage.string_value)
}
inline void TestMessage::set_string_value(const char* value) {
  
  string_value_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:proto3.TestMessage.string_value)
}
inline void TestMessage::set_string_value(const char* value, size_t size) {
  
  string_value_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:proto3.TestMessage.string_value)
}
inline ::std::string* TestMessage::mutable_string_value() {
  
  // @@protoc_insertion_point(field_mutable:proto3.TestMessage.string_value)
  return string_value_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline ::std::string* TestMessage::release_string_value() {
  // @@protoc_insertion_point(field_release:proto3.TestMessage.string_value)
  
  return string_value_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void TestMessage::set_allocated_string_value(::std::string* string_value) {
  if (string_value != NULL) {
    
  } else {
    
  }
  string_value_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), string_value);
  // @@protoc_insertion_point(field_set_allocated:proto3.TestMessage.string_value)
}

// optional bytes bytes_value = 9;
inline void TestMessage::clear_bytes_value() {
  bytes_value_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline const ::std::string& TestMessage::bytes_value() const {
  // @@protoc_insertion_point(field_get:proto3.TestMessage.bytes_value)
  return bytes_value_.GetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void TestMessage::set_bytes_value(const ::std::string& value) {
  
  bytes_value_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:proto3.TestMessage.bytes_value)
}
inline void TestMessage::set_bytes_value(const char* value) {
  
  bytes_value_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:proto3.TestMessage.bytes_value)
}
inline void TestMessage::set_bytes_value(const void* value, size_t size) {
  
  bytes_value_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:proto3.TestMessage.bytes_value)
}
inline ::std::string* TestMessage::mutable_bytes_value() {
  
  // @@protoc_insertion_point(field_mutable:proto3.TestMessage.bytes_value)
  return bytes_value_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline ::std::string* TestMessage::release_bytes_value() {
  // @@protoc_insertion_point(field_release:proto3.TestMessage.bytes_value)
  
  return bytes_value_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void TestMessage::set_allocated_bytes_value(::std::string* bytes_value) {
  if (bytes_value != NULL) {
    
  } else {
    
  }
  bytes_value_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), bytes_value);
  // @@protoc_insertion_point(field_set_allocated:proto3.TestMessage.bytes_value)
}

// optional .proto3.EnumType enum_value = 10;
inline void TestMessage::clear_enum_value() {
  enum_value_ = 0;
}
inline ::proto3::EnumType TestMessage::enum_value() const {
  // @@protoc_insertion_point(field_get:proto3.TestMessage.enum_value)
  return static_cast< ::proto3::EnumType >(enum_value_);
}
inline void TestMessage::set_enum_value(::proto3::EnumType value) {
  
  enum_value_ = value;
  // @@protoc_insertion_point(field_set:proto3.TestMessage.enum_value)
}

// optional .proto3.MessageType message_value = 11;
inline bool TestMessage::has_message_value() const {
  return this != internal_default_instance() && message_value_ != NULL;
}
inline void TestMessage::clear_message_value() {
  if (GetArenaNoVirtual() == NULL && message_value_ != NULL) delete message_value_;
  message_value_ = NULL;
}
inline const ::proto3::MessageType& TestMessage::message_value() const {
  // @@protoc_insertion_point(field_get:proto3.TestMessage.message_value)
  return message_value_ != NULL ? *message_value_
                         : *::proto3::MessageType::internal_default_instance();
}
inline ::proto3::MessageType* TestMessage::mutable_message_value() {
  
  if (message_value_ == NULL) {
    message_value_ = new ::proto3::MessageType;
  }
  // @@protoc_insertion_point(field_mutable:proto3.TestMessage.message_value)
  return message_value_;
}
inline ::proto3::MessageType* TestMessage::release_message_value() {
  // @@protoc_insertion_point(field_release:proto3.TestMessage.message_value)
  
  ::proto3::MessageType* temp = message_value_;
  message_value_ = NULL;
  return temp;
}
inline void TestMessage::set_allocated_message_value(::proto3::MessageType* message_value) {
  delete message_value_;
  message_value_ = message_value;
  if (message_value) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_set_allocated:proto3.TestMessage.message_value)
}

// repeated bool repeated_bool_value = 21;
inline int TestMessage::repeated_bool_value_size() const {
  return repeated_bool_value_.size();
}
inline void TestMessage::clear_repeated_bool_value() {
  repeated_bool_value_.Clear();
}
inline bool TestMessage::repeated_bool_value(int index) const {
  // @@protoc_insertion_point(field_get:proto3.TestMessage.repeated_bool_value)
  return repeated_bool_value_.Get(index);
}
inline void TestMessage::set_repeated_bool_value(int index, bool value) {
  repeated_bool_value_.Set(index, value);
  // @@protoc_insertion_point(field_set:proto3.TestMessage.repeated_bool_value)
}
inline void TestMessage::add_repeated_bool_value(bool value) {
  repeated_bool_value_.Add(value);
  // @@protoc_insertion_point(field_add:proto3.TestMessage.repeated_bool_value)
}
inline const ::google::protobuf::RepeatedField< bool >&
TestMessage::repeated_bool_value() const {
  // @@protoc_insertion_point(field_list:proto3.TestMessage.repeated_bool_value)
  return repeated_bool_value_;
}
inline ::google::protobuf::RepeatedField< bool >*
TestMessage::mutable_repeated_bool_value() {
  // @@protoc_insertion_point(field_mutable_list:proto3.TestMessage.repeated_bool_value)
  return &repeated_bool_value_;
}

// repeated int32 repeated_int32_value = 22;
inline int TestMessage::repeated_int32_value_size() const {
  return repeated_int32_value_.size();
}
inline void TestMessage::clear_repeated_int32_value() {
  repeated_int32_value_.Clear();
}
inline ::google::protobuf::int32 TestMessage::repeated_int32_value(int index) const {
  // @@protoc_insertion_point(field_get:proto3.TestMessage.repeated_int32_value)
  return repeated_int32_value_.Get(index);
}
inline void TestMessage::set_repeated_int32_value(int index, ::google::protobuf::int32 value) {
  repeated_int32_value_.Set(index, value);
  // @@protoc_insertion_point(field_set:proto3.TestMessage.repeated_int32_value)
}
inline void TestMessage::add_repeated_int32_value(::google::protobuf::int32 value) {
  repeated_int32_value_.Add(value);
  // @@protoc_insertion_point(field_add:proto3.TestMessage.repeated_int32_value)
}
inline const ::google::protobuf::RepeatedField< ::google::protobuf::int32 >&
TestMessage::repeated_int32_value() const {
  // @@protoc_insertion_point(field_list:proto3.TestMessage.repeated_int32_value)
  return repeated_int32_value_;
}
inline ::google::protobuf::RepeatedField< ::google::protobuf::int32 >*
TestMessage::mutable_repeated_int32_value() {
  // @@protoc_insertion_point(field_mutable_list:proto3.TestMessage.repeated_int32_value)
  return &repeated_int32_value_;
}

// repeated int64 repeated_int64_value = 23;
inline int TestMessage::repeated_int64_value_size() const {
  return repeated_int64_value_.size();
}
inline void TestMessage::clear_repeated_int64_value() {
  repeated_int64_value_.Clear();
}
inline ::google::protobuf::int64 TestMessage::repeated_int64_value(int index) const {
  // @@protoc_insertion_point(field_get:proto3.TestMessage.repeated_int64_value)
  return repeated_int64_value_.Get(index);
}
inline void TestMessage::set_repeated_int64_value(int index, ::google::protobuf::int64 value) {
  repeated_int64_value_.Set(index, value);
  // @@protoc_insertion_point(field_set:proto3.TestMessage.repeated_int64_value)
}
inline void TestMessage::add_repeated_int64_value(::google::protobuf::int64 value) {
  repeated_int64_value_.Add(value);
  // @@protoc_insertion_point(field_add:proto3.TestMessage.repeated_int64_value)
}
inline const ::google::protobuf::RepeatedField< ::google::protobuf::int64 >&
TestMessage::repeated_int64_value() const {
  // @@protoc_insertion_point(field_list:proto3.TestMessage.repeated_int64_value)
  return repeated_int64_value_;
}
inline ::google::protobuf::RepeatedField< ::google::protobuf::int64 >*
TestMessage::mutable_repeated_int64_value() {
  // @@protoc_insertion_point(field_mutable_list:proto3.TestMessage.repeated_int64_value)
  return &repeated_int64_value_;
}

// repeated uint32 repeated_uint32_value = 24;
inline int TestMessage::repeated_uint32_value_size() const {
  return repeated_uint32_value_.size();
}
inline void TestMessage::clear_repeated_uint32_value() {
  repeated_uint32_value_.Clear();
}
inline ::google::protobuf::uint32 TestMessage::repeated_uint32_value(int index) const {
  // @@protoc_insertion_point(field_get:proto3.TestMessage.repeated_uint32_value)
  return repeated_uint32_value_.Get(index);
}
inline void TestMessage::set_repeated_uint32_value(int index, ::google::protobuf::uint32 value) {
  repeated_uint32_value_.Set(index, value);
  // @@protoc_insertion_point(field_set:proto3.TestMessage.repeated_uint32_value)
}
inline void TestMessage::add_repeated_uint32_value(::google::protobuf::uint32 value) {
  repeated_uint32_value_.Add(value);
  // @@protoc_insertion_point(field_add:proto3.TestMessage.repeated_uint32_value)
}
inline const ::google::protobuf::RepeatedField< ::google::protobuf::uint32 >&
TestMessage::repeated_uint32_value() const {
  // @@protoc_insertion_point(field_list:proto3.TestMessage.repeated_uint32_value)
  return repeated_uint32_value_;
}
inline ::google::protobuf::RepeatedField< ::google::protobuf::uint32 >*
TestMessage::mutable_repeated_uint32_value() {
  // @@protoc_insertion_point(field_mutable_list:proto3.TestMessage.repeated_uint32_value)
  return &repeated_uint32_value_;
}

// repeated uint64 repeated_uint64_value = 25;
inline int TestMessage::repeated_uint64_value_size() const {
  return repeated_uint64_value_.size();
}
inline void TestMessage::clear_repeated_uint64_value() {
  repeated_uint64_value_.Clear();
}
inline ::google::protobuf::uint64 TestMessage::repeated_uint64_value(int index) const {
  // @@protoc_insertion_point(field_get:proto3.TestMessage.repeated_uint64_value)
  return repeated_uint64_value_.Get(index);
}
inline void TestMessage::set_repeated_uint64_value(int index, ::google::protobuf::uint64 value) {
  repeated_uint64_value_.Set(index, value);
  // @@protoc_insertion_point(field_set:proto3.TestMessage.repeated_uint64_value)
}
inline void TestMessage::add_repeated_uint64_value(::google::protobuf::uint64 value) {
  repeated_uint64_value_.Add(value);
  // @@protoc_insertion_point(field_add:proto3.TestMessage.repeated_uint64_value)
}
inline const ::google::protobuf::RepeatedField< ::google::protobuf::uint64 >&
TestMessage::repeated_uint64_value() const {
  // @@protoc_insertion_point(field_list:proto3.TestMessage.repeated_uint64_value)
  return repeated_uint64_value_;
}
inline ::google::protobuf::RepeatedField< ::google::protobuf::uint64 >*
TestMessage::mutable_repeated_uint64_value() {
  // @@protoc_insertion_point(field_mutable_list:proto3.TestMessage.repeated_uint64_value)
  return &repeated_uint64_value_;
}

// repeated float repeated_float_value = 26;
inline int TestMessage::repeated_float_value_size() const {
  return repeated_float_value_.size();
}
inline void TestMessage::clear_repeated_float_value() {
  repeated_float_value_.Clear();
}
inline float TestMessage::repeated_float_value(int index) const {
  // @@protoc_insertion_point(field_get:proto3.TestMessage.repeated_float_value)
  return repeated_float_value_.Get(index);
}
inline void TestMessage::set_repeated_float_value(int index, float value) {
  repeated_float_value_.Set(index, value);
  // @@protoc_insertion_point(field_set:proto3.TestMessage.repeated_float_value)
}
inline void TestMessage::add_repeated_float_value(float value) {
  repeated_float_value_.Add(value);
  // @@protoc_insertion_point(field_add:proto3.TestMessage.repeated_float_value)
}
inline const ::google::protobuf::RepeatedField< float >&
TestMessage::repeated_float_value() const {
  // @@protoc_insertion_point(field_list:proto3.TestMessage.repeated_float_value)
  return repeated_float_value_;
}
inline ::google::protobuf::RepeatedField< float >*
TestMessage::mutable_repeated_float_value() {
  // @@protoc_insertion_point(field_mutable_list:proto3.TestMessage.repeated_float_value)
  return &repeated_float_value_;
}

// repeated double repeated_double_value = 27;
inline int TestMessage::repeated_double_value_size() const {
  return repeated_double_value_.size();
}
inline void TestMessage::clear_repeated_double_value() {
  repeated_double_value_.Clear();
}
inline double TestMessage::repeated_double_value(int index) const {
  // @@protoc_insertion_point(field_get:proto3.TestMessage.repeated_double_value)
  return repeated_double_value_.Get(index);
}
inline void TestMessage::set_repeated_double_value(int index, double value) {
  repeated_double_value_.Set(index, value);
  // @@protoc_insertion_point(field_set:proto3.TestMessage.repeated_double_value)
}
inline void TestMessage::add_repeated_double_value(double value) {
  repeated_double_value_.Add(value);
  // @@protoc_insertion_point(field_add:proto3.TestMessage.repeated_double_value)
}
inline const ::google::protobuf::RepeatedField< double >&
TestMessage::repeated_double_value() const {
  // @@protoc_insertion_point(field_list:proto3.TestMessage.repeated_double_value)
  return repeated_double_value_;
}
inline ::google::protobuf::RepeatedField< double >*
TestMessage::mutable_repeated_double_value() {
  // @@protoc_insertion_point(field_mutable_list:proto3.TestMessage.repeated_double_value)
  return &repeated_double_value_;
}

// repeated string repeated_string_value = 28;
inline int TestMessage::repeated_string_value_size() const {
  return repeated_string_value_.size();
}
inline void TestMessage::clear_repeated_string_value() {
  repeated_string_value_.Clear();
}
inline const ::std::string& TestMessage::repeated_string_value(int index) const {
  // @@protoc_insertion_point(field_get:proto3.TestMessage.repeated_string_value)
  return repeated_string_value_.Get(index);
}
inline ::std::string* TestMessage::mutable_repeated_string_value(int index) {
  // @@protoc_insertion_point(field_mutable:proto3.TestMessage.repeated_string_value)
  return repeated_string_value_.Mutable(index);
}
inline void TestMessage::set_repeated_string_value(int index, const ::std::string& value) {
  // @@protoc_insertion_point(field_set:proto3.TestMessage.repeated_string_value)
  repeated_string_value_.Mutable(index)->assign(value);
}
inline void TestMessage::set_repeated_string_value(int index, const char* value) {
  repeated_string_value_.Mutable(index)->assign(value);
  // @@protoc_insertion_point(field_set_char:proto3.TestMessage.repeated_string_value)
}
inline void TestMessage::set_repeated_string_value(int index, const char* value, size_t size) {
  repeated_string_value_.Mutable(index)->assign(
    reinterpret_cast<const char*>(value), size);
  // @@protoc_insertion_point(field_set_pointer:proto3.TestMessage.repeated_string_value)
}
inline ::std::string* TestMessage::add_repeated_string_value() {
  // @@protoc_insertion_point(field_add_mutable:proto3.TestMessage.repeated_string_value)
  return repeated_string_value_.Add();
}
inline void TestMessage::add_repeated_string_value(const ::std::string& value) {
  repeated_string_value_.Add()->assign(value);
  // @@protoc_insertion_point(field_add:proto3.TestMessage.repeated_string_value)
}
inline void TestMessage::add_repeated_string_value(const char* value) {
  repeated_string_value_.Add()->assign(value);
  // @@protoc_insertion_point(field_add_char:proto3.TestMessage.repeated_string_value)
}
inline void TestMessage::add_repeated_string_value(const char* value, size_t size) {
  repeated_string_value_.Add()->assign(reinterpret_cast<const char*>(value), size);
  // @@protoc_insertion_point(field_add_pointer:proto3.TestMessage.repeated_string_value)
}
inline const ::google::protobuf::RepeatedPtrField< ::std::string>&
TestMessage::repeated_string_value() const {
  // @@protoc_insertion_point(field_list:proto3.TestMessage.repeated_string_value)
  return repeated_string_value_;
}
inline ::google::protobuf::RepeatedPtrField< ::std::string>*
TestMessage::mutable_repeated_string_value() {
  // @@protoc_insertion_point(field_mutable_list:proto3.TestMessage.repeated_string_value)
  return &repeated_string_value_;
}

// repeated bytes repeated_bytes_value = 29;
inline int TestMessage::repeated_bytes_value_size() const {
  return repeated_bytes_value_.size();
}
inline void TestMessage::clear_repeated_bytes_value() {
  repeated_bytes_value_.Clear();
}
inline const ::std::string& TestMessage::repeated_bytes_value(int index) const {
  // @@protoc_insertion_point(field_get:proto3.TestMessage.repeated_bytes_value)
  return repeated_bytes_value_.Get(index);
}
inline ::std::string* TestMessage::mutable_repeated_bytes_value(int index) {
  // @@protoc_insertion_point(field_mutable:proto3.TestMessage.repeated_bytes_value)
  return repeated_bytes_value_.Mutable(index);
}
inline void TestMessage::set_repeated_bytes_value(int index, const ::std::string& value) {
  // @@protoc_insertion_point(field_set:proto3.TestMessage.repeated_bytes_value)
  repeated_bytes_value_.Mutable(index)->assign(value);
}
inline void TestMessage::set_repeated_bytes_value(int index, const char* value) {
  repeated_bytes_value_.Mutable(index)->assign(value);
  // @@protoc_insertion_point(field_set_char:proto3.TestMessage.repeated_bytes_value)
}
inline void TestMessage::set_repeated_bytes_value(int index, const void* value, size_t size) {
  repeated_bytes_value_.Mutable(index)->assign(
    reinterpret_cast<const char*>(value), size);
  // @@protoc_insertion_point(field_set_pointer:proto3.TestMessage.repeated_bytes_value)
}
inline ::std::string* TestMessage::add_repeated_bytes_value() {
  // @@protoc_insertion_point(field_add_mutable:proto3.TestMessage.repeated_bytes_value)
  return repeated_bytes_value_.Add();
}
inline void TestMessage::add_repeated_bytes_value(const ::std::string& value) {
  repeated_bytes_value_.Add()->assign(value);
  // @@protoc_insertion_point(field_add:proto3.TestMessage.repeated_bytes_value)
}
inline void TestMessage::add_repeated_bytes_value(const char* value) {
  repeated_bytes_value_.Add()->assign(value);
  // @@protoc_insertion_point(field_add_char:proto3.TestMessage.repeated_bytes_value)
}
inline void TestMessage::add_repeated_bytes_value(const void* value, size_t size) {
  repeated_bytes_value_.Add()->assign(reinterpret_cast<const char*>(value), size);
  // @@protoc_insertion_point(field_add_pointer:proto3.TestMessage.repeated_bytes_value)
}
inline const ::google::protobuf::RepeatedPtrField< ::std::string>&
TestMessage::repeated_bytes_value() const {
  // @@protoc_insertion_point(field_list:proto3.TestMessage.repeated_bytes_value)
  return repeated_bytes_value_;
}
inline ::google::protobuf::RepeatedPtrField< ::std::string>*
TestMessage::mutable_repeated_bytes_value() {
  // @@protoc_insertion_point(field_mutable_list:proto3.TestMessage.repeated_bytes_value)
  return &repeated_bytes_value_;
}

// repeated .proto3.EnumType repeated_enum_value = 30;
inline int TestMessage::repeated_enum_value_size() const {
  return repeated_enum_value_.size();
}
inline void TestMessage::clear_repeated_enum_value() {
  repeated_enum_value_.Clear();
}
inline ::proto3::EnumType TestMessage::repeated_enum_value(int index) const {
  // @@protoc_insertion_point(field_get:proto3.TestMessage.repeated_enum_value)
  return static_cast< ::proto3::EnumType >(repeated_enum_value_.Get(index));
}
inline void TestMessage::set_repeated_enum_value(int index, ::proto3::EnumType value) {
  repeated_enum_value_.Set(index, value);
  // @@protoc_insertion_point(field_set:proto3.TestMessage.repeated_enum_value)
}
inline void TestMessage::add_repeated_enum_value(::proto3::EnumType value) {
  repeated_enum_value_.Add(value);
  // @@protoc_insertion_point(field_add:proto3.TestMessage.repeated_enum_value)
}
inline const ::google::protobuf::RepeatedField<int>&
TestMessage::repeated_enum_value() const {
  // @@protoc_insertion_point(field_list:proto3.TestMessage.repeated_enum_value)
  return repeated_enum_value_;
}
inline ::google::protobuf::RepeatedField<int>*
TestMessage::mutable_repeated_enum_value() {
  // @@protoc_insertion_point(field_mutable_list:proto3.TestMessage.repeated_enum_value)
  return &repeated_enum_value_;
}

// repeated .proto3.MessageType repeated_message_value = 31;
inline int TestMessage::repeated_message_value_size() const {
  return repeated_message_value_.size();
}
inline void TestMessage::clear_repeated_message_value() {
  repeated_message_value_.Clear();
}
inline const ::proto3::MessageType& TestMessage::repeated_message_value(int index) const {
  // @@protoc_insertion_point(field_get:proto3.TestMessage.repeated_message_value)
  return repeated_message_value_.Get(index);
}
inline ::proto3::MessageType* TestMessage::mutable_repeated_message_value(int index) {
  // @@protoc_insertion_point(field_mutable:proto3.TestMessage.repeated_message_value)
  return repeated_message_value_.Mutable(index);
}
inline ::proto3::MessageType* TestMessage::add_repeated_message_value() {
  // @@protoc_insertion_point(field_add:proto3.TestMessage.repeated_message_value)
  return repeated_message_value_.Add();
}
inline ::google::protobuf::RepeatedPtrField< ::proto3::MessageType >*
TestMessage::mutable_repeated_message_value() {
  // @@protoc_insertion_point(field_mutable_list:proto3.TestMessage.repeated_message_value)
  return &repeated_message_value_;
}
inline const ::google::protobuf::RepeatedPtrField< ::proto3::MessageType >&
TestMessage::repeated_message_value() const {
  // @@protoc_insertion_point(field_list:proto3.TestMessage.repeated_message_value)
  return repeated_message_value_;
}

inline const TestMessage* TestMessage::internal_default_instance() {
  return &TestMessage_default_instance_.get();
}
// -------------------------------------------------------------------

// TestOneof

// optional int32 oneof_int32_value = 1;
inline bool TestOneof::has_oneof_int32_value() const {
  return oneof_value_case() == kOneofInt32Value;
}
inline void TestOneof::set_has_oneof_int32_value() {
  _oneof_case_[0] = kOneofInt32Value;
}
inline void TestOneof::clear_oneof_int32_value() {
  if (has_oneof_int32_value()) {
    oneof_value_.oneof_int32_value_ = 0;
    clear_has_oneof_value();
  }
}
inline ::google::protobuf::int32 TestOneof::oneof_int32_value() const {
  // @@protoc_insertion_point(field_get:proto3.TestOneof.oneof_int32_value)
  if (has_oneof_int32_value()) {
    return oneof_value_.oneof_int32_value_;
  }
  return 0;
}
inline void TestOneof::set_oneof_int32_value(::google::protobuf::int32 value) {
  if (!has_oneof_int32_value()) {
    clear_oneof_value();
    set_has_oneof_int32_value();
  }
  oneof_value_.oneof_int32_value_ = value;
  // @@protoc_insertion_point(field_set:proto3.TestOneof.oneof_int32_value)
}

// optional string oneof_string_value = 2;
inline bool TestOneof::has_oneof_string_value() const {
  return oneof_value_case() == kOneofStringValue;
}
inline void TestOneof::set_has_oneof_string_value() {
  _oneof_case_[0] = kOneofStringValue;
}
inline void TestOneof::clear_oneof_string_value() {
  if (has_oneof_string_value()) {
    oneof_value_.oneof_string_value_.DestroyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
    clear_has_oneof_value();
  }
}
inline const ::std::string& TestOneof::oneof_string_value() const {
  // @@protoc_insertion_point(field_get:proto3.TestOneof.oneof_string_value)
  if (has_oneof_string_value()) {
    return oneof_value_.oneof_string_value_.GetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  }
  return *&::google::protobuf::internal::GetEmptyStringAlreadyInited();
}
inline void TestOneof::set_oneof_string_value(const ::std::string& value) {
  // @@protoc_insertion_point(field_set:proto3.TestOneof.oneof_string_value)
  if (!has_oneof_string_value()) {
    clear_oneof_value();
    set_has_oneof_string_value();
    oneof_value_.oneof_string_value_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  }
  oneof_value_.oneof_string_value_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:proto3.TestOneof.oneof_string_value)
}
inline void TestOneof::set_oneof_string_value(const char* value) {
  if (!has_oneof_string_value()) {
    clear_oneof_value();
    set_has_oneof_string_value();
    oneof_value_.oneof_string_value_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  }
  oneof_value_.oneof_string_value_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(value));
  // @@protoc_insertion_point(field_set_char:proto3.TestOneof.oneof_string_value)
}
inline void TestOneof::set_oneof_string_value(const char* value, size_t size) {
  if (!has_oneof_string_value()) {
    clear_oneof_value();
    set_has_oneof_string_value();
    oneof_value_.oneof_string_value_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  }
  oneof_value_.oneof_string_value_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(
      reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:proto3.TestOneof.oneof_string_value)
}
inline ::std::string* TestOneof::mutable_oneof_string_value() {
  if (!has_oneof_string_value()) {
    clear_oneof_value();
    set_has_oneof_string_value();
    oneof_value_.oneof_string_value_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  }
  // @@protoc_insertion_point(field_mutable:proto3.TestOneof.oneof_string_value)
  return oneof_value_.oneof_string_value_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline ::std::string* TestOneof::release_oneof_string_value() {
  // @@protoc_insertion_point(field_release:proto3.TestOneof.oneof_string_value)
  if (has_oneof_string_value()) {
    clear_has_oneof_value();
    return oneof_value_.oneof_string_value_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  } else {
    return NULL;
  }
}
inline void TestOneof::set_allocated_oneof_string_value(::std::string* oneof_string_value) {
  if (!has_oneof_string_value()) {
    oneof_value_.oneof_string_value_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  }
  clear_oneof_value();
  if (oneof_string_value != NULL) {
    set_has_oneof_string_value();
    oneof_value_.oneof_string_value_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
        oneof_string_value);
  }
  // @@protoc_insertion_point(field_set_allocated:proto3.TestOneof.oneof_string_value)
}

// optional bytes oneof_bytes_value = 3;
inline bool TestOneof::has_oneof_bytes_value() const {
  return oneof_value_case() == kOneofBytesValue;
}
inline void TestOneof::set_has_oneof_bytes_value() {
  _oneof_case_[0] = kOneofBytesValue;
}
inline void TestOneof::clear_oneof_bytes_value() {
  if (has_oneof_bytes_value()) {
    oneof_value_.oneof_bytes_value_.DestroyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
    clear_has_oneof_value();
  }
}
inline const ::std::string& TestOneof::oneof_bytes_value() const {
  // @@protoc_insertion_point(field_get:proto3.TestOneof.oneof_bytes_value)
  if (has_oneof_bytes_value()) {
    return oneof_value_.oneof_bytes_value_.GetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  }
  return *&::google::protobuf::internal::GetEmptyStringAlreadyInited();
}
inline void TestOneof::set_oneof_bytes_value(const ::std::string& value) {
  // @@protoc_insertion_point(field_set:proto3.TestOneof.oneof_bytes_value)
  if (!has_oneof_bytes_value()) {
    clear_oneof_value();
    set_has_oneof_bytes_value();
    oneof_value_.oneof_bytes_value_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  }
  oneof_value_.oneof_bytes_value_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:proto3.TestOneof.oneof_bytes_value)
}
inline void TestOneof::set_oneof_bytes_value(const char* value) {
  if (!has_oneof_bytes_value()) {
    clear_oneof_value();
    set_has_oneof_bytes_value();
    oneof_value_.oneof_bytes_value_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  }
  oneof_value_.oneof_bytes_value_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(value));
  // @@protoc_insertion_point(field_set_char:proto3.TestOneof.oneof_bytes_value)
}
inline void TestOneof::set_oneof_bytes_value(const void* value, size_t size) {
  if (!has_oneof_bytes_value()) {
    clear_oneof_value();
    set_has_oneof_bytes_value();
    oneof_value_.oneof_bytes_value_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  }
  oneof_value_.oneof_bytes_value_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(
      reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:proto3.TestOneof.oneof_bytes_value)
}
inline ::std::string* TestOneof::mutable_oneof_bytes_value() {
  if (!has_oneof_bytes_value()) {
    clear_oneof_value();
    set_has_oneof_bytes_value();
    oneof_value_.oneof_bytes_value_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  }
  // @@protoc_insertion_point(field_mutable:proto3.TestOneof.oneof_bytes_value)
  return oneof_value_.oneof_bytes_value_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline ::std::string* TestOneof::release_oneof_bytes_value() {
  // @@protoc_insertion_point(field_release:proto3.TestOneof.oneof_bytes_value)
  if (has_oneof_bytes_value()) {
    clear_has_oneof_value();
    return oneof_value_.oneof_bytes_value_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  } else {
    return NULL;
  }
}
inline void TestOneof::set_allocated_oneof_bytes_value(::std::string* oneof_bytes_value) {
  if (!has_oneof_bytes_value()) {
    oneof_value_.oneof_bytes_value_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  }
  clear_oneof_value();
  if (oneof_bytes_value != NULL) {
    set_has_oneof_bytes_value();
    oneof_value_.oneof_bytes_value_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
        oneof_bytes_value);
  }
  // @@protoc_insertion_point(field_set_allocated:proto3.TestOneof.oneof_bytes_value)
}

// optional .proto3.EnumType oneof_enum_value = 4;
inline bool TestOneof::has_oneof_enum_value() const {
  return oneof_value_case() == kOneofEnumValue;
}
inline void TestOneof::set_has_oneof_enum_value() {
  _oneof_case_[0] = kOneofEnumValue;
}
inline void TestOneof::clear_oneof_enum_value() {
  if (has_oneof_enum_value()) {
    oneof_value_.oneof_enum_value_ = 0;
    clear_has_oneof_value();
  }
}
inline ::proto3::EnumType TestOneof::oneof_enum_value() const {
  // @@protoc_insertion_point(field_get:proto3.TestOneof.oneof_enum_value)
  if (has_oneof_enum_value()) {
    return static_cast< ::proto3::EnumType >(oneof_value_.oneof_enum_value_);
  }
  return static_cast< ::proto3::EnumType >(0);
}
inline void TestOneof::set_oneof_enum_value(::proto3::EnumType value) {
  if (!has_oneof_enum_value()) {
    clear_oneof_value();
    set_has_oneof_enum_value();
  }
  oneof_value_.oneof_enum_value_ = value;
  // @@protoc_insertion_point(field_set:proto3.TestOneof.oneof_enum_value)
}

// optional .proto3.MessageType oneof_message_value = 5;
inline bool TestOneof::has_oneof_message_value() const {
  return oneof_value_case() == kOneofMessageValue;
}
inline void TestOneof::set_has_oneof_message_value() {
  _oneof_case_[0] = kOneofMessageValue;
}
inline void TestOneof::clear_oneof_message_value() {
  if (has_oneof_message_value()) {
    delete oneof_value_.oneof_message_value_;
    clear_has_oneof_value();
  }
}
inline  const ::proto3::MessageType& TestOneof::oneof_message_value() const {
  // @@protoc_insertion_point(field_get:proto3.TestOneof.oneof_message_value)
  return has_oneof_message_value()
      ? *oneof_value_.oneof_message_value_
      : ::proto3::MessageType::default_instance();
}
inline ::proto3::MessageType* TestOneof::mutable_oneof_message_value() {
  if (!has_oneof_message_value()) {
    clear_oneof_value();
    set_has_oneof_message_value();
    oneof_value_.oneof_message_value_ = new ::proto3::MessageType;
  }
  // @@protoc_insertion_point(field_mutable:proto3.TestOneof.oneof_message_value)
  return oneof_value_.oneof_message_value_;
}
inline ::proto3::MessageType* TestOneof::release_oneof_message_value() {
  // @@protoc_insertion_point(field_release:proto3.TestOneof.oneof_message_value)
  if (has_oneof_message_value()) {
    clear_has_oneof_value();
    ::proto3::MessageType* temp = oneof_value_.oneof_message_value_;
    oneof_value_.oneof_message_value_ = NULL;
    return temp;
  } else {
    return NULL;
  }
}
inline void TestOneof::set_allocated_oneof_message_value(::proto3::MessageType* oneof_message_value) {
  clear_oneof_value();
  if (oneof_message_value) {
    set_has_oneof_message_value();
    oneof_value_.oneof_message_value_ = oneof_message_value;
  }
  // @@protoc_insertion_point(field_set_allocated:proto3.TestOneof.oneof_message_value)
}

inline bool TestOneof::has_oneof_value() const {
  return oneof_value_case() != ONEOF_VALUE_NOT_SET;
}
inline void TestOneof::clear_has_oneof_value() {
  _oneof_case_[0] = ONEOF_VALUE_NOT_SET;
}
inline TestOneof::OneofValueCase TestOneof::oneof_value_case() const {
  return TestOneof::OneofValueCase(_oneof_case_[0]);
}
inline const TestOneof* TestOneof::internal_default_instance() {
  return &TestOneof_default_instance_.get();
}
// -------------------------------------------------------------------

// TestMap

// map<bool, int32> bool_map = 1;
inline int TestMap::bool_map_size() const {
  return bool_map_.size();
}
inline void TestMap::clear_bool_map() {
  bool_map_.Clear();
}
inline const ::google::protobuf::Map< bool, ::google::protobuf::int32 >&
TestMap::bool_map() const {
  // @@protoc_insertion_point(field_map:proto3.TestMap.bool_map)
  return bool_map_.GetMap();
}
inline ::google::protobuf::Map< bool, ::google::protobuf::int32 >*
TestMap::mutable_bool_map() {
  // @@protoc_insertion_point(field_mutable_map:proto3.TestMap.bool_map)
  return bool_map_.MutableMap();
}

// map<int32, int32> int32_map = 2;
inline int TestMap::int32_map_size() const {
  return int32_map_.size();
}
inline void TestMap::clear_int32_map() {
  int32_map_.Clear();
}
inline const ::google::protobuf::Map< ::google::protobuf::int32, ::google::protobuf::int32 >&
TestMap::int32_map() const {
  // @@protoc_insertion_point(field_map:proto3.TestMap.int32_map)
  return int32_map_.GetMap();
}
inline ::google::protobuf::Map< ::google::protobuf::int32, ::google::protobuf::int32 >*
TestMap::mutable_int32_map() {
  // @@protoc_insertion_point(field_mutable_map:proto3.TestMap.int32_map)
  return int32_map_.MutableMap();
}

// map<int64, int32> int64_map = 3;
inline int TestMap::int64_map_size() const {
  return int64_map_.size();
}
inline void TestMap::clear_int64_map() {
  int64_map_.Clear();
}
inline const ::google::protobuf::Map< ::google::protobuf::int64, ::google::protobuf::int32 >&
TestMap::int64_map() const {
  // @@protoc_insertion_point(field_map:proto3.TestMap.int64_map)
  return int64_map_.GetMap();
}
inline ::google::protobuf::Map< ::google::protobuf::int64, ::google::protobuf::int32 >*
TestMap::mutable_int64_map() {
  // @@protoc_insertion_point(field_mutable_map:proto3.TestMap.int64_map)
  return int64_map_.MutableMap();
}

// map<uint32, int32> uint32_map = 4;
inline int TestMap::uint32_map_size() const {
  return uint32_map_.size();
}
inline void TestMap::clear_uint32_map() {
  uint32_map_.Clear();
}
inline const ::google::protobuf::Map< ::google::protobuf::uint32, ::google::protobuf::int32 >&
TestMap::uint32_map() const {
  // @@protoc_insertion_point(field_map:proto3.TestMap.uint32_map)
  return uint32_map_.GetMap();
}
inline ::google::protobuf::Map< ::google::protobuf::uint32, ::google::protobuf::int32 >*
TestMap::mutable_uint32_map() {
  // @@protoc_insertion_point(field_mutable_map:proto3.TestMap.uint32_map)
  return uint32_map_.MutableMap();
}

// map<uint64, int32> uint64_map = 5;
inline int TestMap::uint64_map_size() const {
  return uint64_map_.size();
}
inline void TestMap::clear_uint64_map() {
  uint64_map_.Clear();
}
inline const ::google::protobuf::Map< ::google::protobuf::uint64, ::google::protobuf::int32 >&
TestMap::uint64_map() const {
  // @@protoc_insertion_point(field_map:proto3.TestMap.uint64_map)
  return uint64_map_.GetMap();
}
inline ::google::protobuf::Map< ::google::protobuf::uint64, ::google::protobuf::int32 >*
TestMap::mutable_uint64_map() {
  // @@protoc_insertion_point(field_mutable_map:proto3.TestMap.uint64_map)
  return uint64_map_.MutableMap();
}

// map<string, int32> string_map = 6;
inline int TestMap::string_map_size() const {
  return string_map_.size();
}
inline void TestMap::clear_string_map() {
  string_map_.Clear();
}
inline const ::google::protobuf::Map< ::std::string, ::google::protobuf::int32 >&
TestMap::string_map() const {
  // @@protoc_insertion_point(field_map:proto3.TestMap.string_map)
  return string_map_.GetMap();
}
inline ::google::protobuf::Map< ::std::string, ::google::protobuf::int32 >*
TestMap::mutable_string_map() {
  // @@protoc_insertion_point(field_mutable_map:proto3.TestMap.string_map)
  return string_map_.MutableMap();
}

inline const TestMap* TestMap::internal_default_instance() {
  return &TestMap_default_instance_.get();
}
// -------------------------------------------------------------------

// TestNestedMap

// map<bool, int32> bool_map = 1;
inline int TestNestedMap::bool_map_size() const {
  return bool_map_.size();
}
inline void TestNestedMap::clear_bool_map() {
  bool_map_.Clear();
}
inline const ::google::protobuf::Map< bool, ::google::protobuf::int32 >&
TestNestedMap::bool_map() const {
  // @@protoc_insertion_point(field_map:proto3.TestNestedMap.bool_map)
  return bool_map_.GetMap();
}
inline ::google::protobuf::Map< bool, ::google::protobuf::int32 >*
TestNestedMap::mutable_bool_map() {
  // @@protoc_insertion_point(field_mutable_map:proto3.TestNestedMap.bool_map)
  return bool_map_.MutableMap();
}

// map<int32, int32> int32_map = 2;
inline int TestNestedMap::int32_map_size() const {
  return int32_map_.size();
}
inline void TestNestedMap::clear_int32_map() {
  int32_map_.Clear();
}
inline const ::google::protobuf::Map< ::google::protobuf::int32, ::google::protobuf::int32 >&
TestNestedMap::int32_map() const {
  // @@protoc_insertion_point(field_map:proto3.TestNestedMap.int32_map)
  return int32_map_.GetMap();
}
inline ::google::protobuf::Map< ::google::protobuf::int32, ::google::protobuf::int32 >*
TestNestedMap::mutable_int32_map() {
  // @@protoc_insertion_point(field_mutable_map:proto3.TestNestedMap.int32_map)
  return int32_map_.MutableMap();
}

// map<int64, int32> int64_map = 3;
inline int TestNestedMap::int64_map_size() const {
  return int64_map_.size();
}
inline void TestNestedMap::clear_int64_map() {
  int64_map_.Clear();
}
inline const ::google::protobuf::Map< ::google::protobuf::int64, ::google::protobuf::int32 >&
TestNestedMap::int64_map() const {
  // @@protoc_insertion_point(field_map:proto3.TestNestedMap.int64_map)
  return int64_map_.GetMap();
}
inline ::google::protobuf::Map< ::google::protobuf::int64, ::google::protobuf::int32 >*
TestNestedMap::mutable_int64_map() {
  // @@protoc_insertion_point(field_mutable_map:proto3.TestNestedMap.int64_map)
  return int64_map_.MutableMap();
}

// map<uint32, int32> uint32_map = 4;
inline int TestNestedMap::uint32_map_size() const {
  return uint32_map_.size();
}
inline void TestNestedMap::clear_uint32_map() {
  uint32_map_.Clear();
}
inline const ::google::protobuf::Map< ::google::protobuf::uint32, ::google::protobuf::int32 >&
TestNestedMap::uint32_map() const {
  // @@protoc_insertion_point(field_map:proto3.TestNestedMap.uint32_map)
  return uint32_map_.GetMap();
}
inline ::google::protobuf::Map< ::google::protobuf::uint32, ::google::protobuf::int32 >*
TestNestedMap::mutable_uint32_map() {
  // @@protoc_insertion_point(field_mutable_map:proto3.TestNestedMap.uint32_map)
  return uint32_map_.MutableMap();
}

// map<uint64, int32> uint64_map = 5;
inline int TestNestedMap::uint64_map_size() const {
  return uint64_map_.size();
}
inline void TestNestedMap::clear_uint64_map() {
  uint64_map_.Clear();
}
inline const ::google::protobuf::Map< ::google::protobuf::uint64, ::google::protobuf::int32 >&
TestNestedMap::uint64_map() const {
  // @@protoc_insertion_point(field_map:proto3.TestNestedMap.uint64_map)
  return uint64_map_.GetMap();
}
inline ::google::protobuf::Map< ::google::protobuf::uint64, ::google::protobuf::int32 >*
TestNestedMap::mutable_uint64_map() {
  // @@protoc_insertion_point(field_mutable_map:proto3.TestNestedMap.uint64_map)
  return uint64_map_.MutableMap();
}

// map<string, int32> string_map = 6;
inline int TestNestedMap::string_map_size() const {
  return string_map_.size();
}
inline void TestNestedMap::clear_string_map() {
  string_map_.Clear();
}
inline const ::google::protobuf::Map< ::std::string, ::google::protobuf::int32 >&
TestNestedMap::string_map() const {
  // @@protoc_insertion_point(field_map:proto3.TestNestedMap.string_map)
  return string_map_.GetMap();
}
inline ::google::protobuf::Map< ::std::string, ::google::protobuf::int32 >*
TestNestedMap::mutable_string_map() {
  // @@protoc_insertion_point(field_mutable_map:proto3.TestNestedMap.string_map)
  return string_map_.MutableMap();
}

// map<string, .proto3.TestNestedMap> map_map = 7;
inline int TestNestedMap::map_map_size() const {
  return map_map_.size();
}
inline void TestNestedMap::clear_map_map() {
  map_map_.Clear();
}
inline const ::google::protobuf::Map< ::std::string, ::proto3::TestNestedMap >&
TestNestedMap::map_map() const {
  // @@protoc_insertion_point(field_map:proto3.TestNestedMap.map_map)
  return map_map_.GetMap();
}
inline ::google::protobuf::Map< ::std::string, ::proto3::TestNestedMap >*
TestNestedMap::mutable_map_map() {
  // @@protoc_insertion_point(field_mutable_map:proto3.TestNestedMap.map_map)
  return map_map_.MutableMap();
}

inline const TestNestedMap* TestNestedMap::internal_default_instance() {
  return &TestNestedMap_default_instance_.get();
}
// -------------------------------------------------------------------

// TestWrapper

// optional .google.protobuf.BoolValue bool_value = 1;
inline bool TestWrapper::has_bool_value() const {
  return this != internal_default_instance() && bool_value_ != NULL;
}
inline void TestWrapper::clear_bool_value() {
  if (GetArenaNoVirtual() == NULL && bool_value_ != NULL) delete bool_value_;
  bool_value_ = NULL;
}
inline const ::google::protobuf::BoolValue& TestWrapper::bool_value() const {
  // @@protoc_insertion_point(field_get:proto3.TestWrapper.bool_value)
  return bool_value_ != NULL ? *bool_value_
                         : *::google::protobuf::BoolValue::internal_default_instance();
}
inline ::google::protobuf::BoolValue* TestWrapper::mutable_bool_value() {
  
  if (bool_value_ == NULL) {
    bool_value_ = new ::google::protobuf::BoolValue;
  }
  // @@protoc_insertion_point(field_mutable:proto3.TestWrapper.bool_value)
  return bool_value_;
}
inline ::google::protobuf::BoolValue* TestWrapper::release_bool_value() {
  // @@protoc_insertion_point(field_release:proto3.TestWrapper.bool_value)
  
  ::google::protobuf::BoolValue* temp = bool_value_;
  bool_value_ = NULL;
  return temp;
}
inline void TestWrapper::set_allocated_bool_value(::google::protobuf::BoolValue* bool_value) {
  delete bool_value_;
  if (bool_value != NULL && bool_value->GetArena() != NULL) {
    ::google::protobuf::BoolValue* new_bool_value = new ::google::protobuf::BoolValue;
    new_bool_value->CopyFrom(*bool_value);
    bool_value = new_bool_value;
  }
  bool_value_ = bool_value;
  if (bool_value) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_set_allocated:proto3.TestWrapper.bool_value)
}

// optional .google.protobuf.Int32Value int32_value = 2;
inline bool TestWrapper::has_int32_value() const {
  return this != internal_default_instance() && int32_value_ != NULL;
}
inline void TestWrapper::clear_int32_value() {
  if (GetArenaNoVirtual() == NULL && int32_value_ != NULL) delete int32_value_;
  int32_value_ = NULL;
}
inline const ::google::protobuf::Int32Value& TestWrapper::int32_value() const {
  // @@protoc_insertion_point(field_get:proto3.TestWrapper.int32_value)
  return int32_value_ != NULL ? *int32_value_
                         : *::google::protobuf::Int32Value::internal_default_instance();
}
inline ::google::protobuf::Int32Value* TestWrapper::mutable_int32_value() {
  
  if (int32_value_ == NULL) {
    int32_value_ = new ::google::protobuf::Int32Value;
  }
  // @@protoc_insertion_point(field_mutable:proto3.TestWrapper.int32_value)
  return int32_value_;
}
inline ::google::protobuf::Int32Value* TestWrapper::release_int32_value() {
  // @@protoc_insertion_point(field_release:proto3.TestWrapper.int32_value)
  
  ::google::protobuf::Int32Value* temp = int32_value_;
  int32_value_ = NULL;
  return temp;
}
inline void TestWrapper::set_allocated_int32_value(::google::protobuf::Int32Value* int32_value) {
  delete int32_value_;
  if (int32_value != NULL && int32_value->GetArena() != NULL) {
    ::google::protobuf::Int32Value* new_int32_value = new ::google::protobuf::Int32Value;
    new_int32_value->CopyFrom(*int32_value);
    int32_value = new_int32_value;
  }
  int32_value_ = int32_value;
  if (int32_value) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_set_allocated:proto3.TestWrapper.int32_value)
}

// optional .google.protobuf.Int64Value int64_value = 3;
inline bool TestWrapper::has_int64_value() const {
  return this != internal_default_instance() && int64_value_ != NULL;
}
inline void TestWrapper::clear_int64_value() {
  if (GetArenaNoVirtual() == NULL && int64_value_ != NULL) delete int64_value_;
  int64_value_ = NULL;
}
inline const ::google::protobuf::Int64Value& TestWrapper::int64_value() const {
  // @@protoc_insertion_point(field_get:proto3.TestWrapper.int64_value)
  return int64_value_ != NULL ? *int64_value_
                         : *::google::protobuf::Int64Value::internal_default_instance();
}
inline ::google::protobuf::Int64Value* TestWrapper::mutable_int64_value() {
  
  if (int64_value_ == NULL) {
    int64_value_ = new ::google::protobuf::Int64Value;
  }
  // @@protoc_insertion_point(field_mutable:proto3.TestWrapper.int64_value)
  return int64_value_;
}
inline ::google::protobuf::Int64Value* TestWrapper::release_int64_value() {
  // @@protoc_insertion_point(field_release:proto3.TestWrapper.int64_value)
  
  ::google::protobuf::Int64Value* temp = int64_value_;
  int64_value_ = NULL;
  return temp;
}
inline void TestWrapper::set_allocated_int64_value(::google::protobuf::Int64Value* int64_value) {
  delete int64_value_;
  if (int64_value != NULL && int64_value->GetArena() != NULL) {
    ::google::protobuf::Int64Value* new_int64_value = new ::google::protobuf::Int64Value;
    new_int64_value->CopyFrom(*int64_value);
    int64_value = new_int64_value;
  }
  int64_value_ = int64_value;
  if (int64_value) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_set_allocated:proto3.TestWrapper.int64_value)
}

// optional .google.protobuf.UInt32Value uint32_value = 4;
inline bool TestWrapper::has_uint32_value() const {
  return this != internal_default_instance() && uint32_value_ != NULL;
}
inline void TestWrapper::clear_uint32_value() {
  if (GetArenaNoVirtual() == NULL && uint32_value_ != NULL) delete uint32_value_;
  uint32_value_ = NULL;
}
inline const ::google::protobuf::UInt32Value& TestWrapper::uint32_value() const {
  // @@protoc_insertion_point(field_get:proto3.TestWrapper.uint32_value)
  return uint32_value_ != NULL ? *uint32_value_
                         : *::google::protobuf::UInt32Value::internal_default_instance();
}
inline ::google::protobuf::UInt32Value* TestWrapper::mutable_uint32_value() {
  
  if (uint32_value_ == NULL) {
    uint32_value_ = new ::google::protobuf::UInt32Value;
  }
  // @@protoc_insertion_point(field_mutable:proto3.TestWrapper.uint32_value)
  return uint32_value_;
}
inline ::google::protobuf::UInt32Value* TestWrapper::release_uint32_value() {
  // @@protoc_insertion_point(field_release:proto3.TestWrapper.uint32_value)
  
  ::google::protobuf::UInt32Value* temp = uint32_value_;
  uint32_value_ = NULL;
  return temp;
}
inline void TestWrapper::set_allocated_uint32_value(::google::protobuf::UInt32Value* uint32_value) {
  delete uint32_value_;
  if (uint32_value != NULL && uint32_value->GetArena() != NULL) {
    ::google::protobuf::UInt32Value* new_uint32_value = new ::google::protobuf::UInt32Value;
    new_uint32_value->CopyFrom(*uint32_value);
    uint32_value = new_uint32_value;
  }
  uint32_value_ = uint32_value;
  if (uint32_value) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_set_allocated:proto3.TestWrapper.uint32_value)
}

// optional .google.protobuf.UInt64Value uint64_value = 5;
inline bool TestWrapper::has_uint64_value() const {
  return this != internal_default_instance() && uint64_value_ != NULL;
}
inline void TestWrapper::clear_uint64_value() {
  if (GetArenaNoVirtual() == NULL && uint64_value_ != NULL) delete uint64_value_;
  uint64_value_ = NULL;
}
inline const ::google::protobuf::UInt64Value& TestWrapper::uint64_value() const {
  // @@protoc_insertion_point(field_get:proto3.TestWrapper.uint64_value)
  return uint64_value_ != NULL ? *uint64_value_
                         : *::google::protobuf::UInt64Value::internal_default_instance();
}
inline ::google::protobuf::UInt64Value* TestWrapper::mutable_uint64_value() {
  
  if (uint64_value_ == NULL) {
    uint64_value_ = new ::google::protobuf::UInt64Value;
  }
  // @@protoc_insertion_point(field_mutable:proto3.TestWrapper.uint64_value)
  return uint64_value_;
}
inline ::google::protobuf::UInt64Value* TestWrapper::release_uint64_value() {
  // @@protoc_insertion_point(field_release:proto3.TestWrapper.uint64_value)
  
  ::google::protobuf::UInt64Value* temp = uint64_value_;
  uint64_value_ = NULL;
  return temp;
}
inline void TestWrapper::set_allocated_uint64_value(::google::protobuf::UInt64Value* uint64_value) {
  delete uint64_value_;
  if (uint64_value != NULL && uint64_value->GetArena() != NULL) {
    ::google::protobuf::UInt64Value* new_uint64_value = new ::google::protobuf::UInt64Value;
    new_uint64_value->CopyFrom(*uint64_value);
    uint64_value = new_uint64_value;
  }
  uint64_value_ = uint64_value;
  if (uint64_value) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_set_allocated:proto3.TestWrapper.uint64_value)
}

// optional .google.protobuf.FloatValue float_value = 6;
inline bool TestWrapper::has_float_value() const {
  return this != internal_default_instance() && float_value_ != NULL;
}
inline void TestWrapper::clear_float_value() {
  if (GetArenaNoVirtual() == NULL && float_value_ != NULL) delete float_value_;
  float_value_ = NULL;
}
inline const ::google::protobuf::FloatValue& TestWrapper::float_value() const {
  // @@protoc_insertion_point(field_get:proto3.TestWrapper.float_value)
  return float_value_ != NULL ? *float_value_
                         : *::google::protobuf::FloatValue::internal_default_instance();
}
inline ::google::protobuf::FloatValue* TestWrapper::mutable_float_value() {
  
  if (float_value_ == NULL) {
    float_value_ = new ::google::protobuf::FloatValue;
  }
  // @@protoc_insertion_point(field_mutable:proto3.TestWrapper.float_value)
  return float_value_;
}
inline ::google::protobuf::FloatValue* TestWrapper::release_float_value() {
  // @@protoc_insertion_point(field_release:proto3.TestWrapper.float_value)
  
  ::google::protobuf::FloatValue* temp = float_value_;
  float_value_ = NULL;
  return temp;
}
inline void TestWrapper::set_allocated_float_value(::google::protobuf::FloatValue* float_value) {
  delete float_value_;
  if (float_value != NULL && float_value->GetArena() != NULL) {
    ::google::protobuf::FloatValue* new_float_value = new ::google::protobuf::FloatValue;
    new_float_value->CopyFrom(*float_value);
    float_value = new_float_value;
  }
  float_value_ = float_value;
  if (float_value) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_set_allocated:proto3.TestWrapper.float_value)
}

// optional .google.protobuf.DoubleValue double_value = 7;
inline bool TestWrapper::has_double_value() const {
  return this != internal_default_instance() && double_value_ != NULL;
}
inline void TestWrapper::clear_double_value() {
  if (GetArenaNoVirtual() == NULL && double_value_ != NULL) delete double_value_;
  double_value_ = NULL;
}
inline const ::google::protobuf::DoubleValue& TestWrapper::double_value() const {
  // @@protoc_insertion_point(field_get:proto3.TestWrapper.double_value)
  return double_value_ != NULL ? *double_value_
                         : *::google::protobuf::DoubleValue::internal_default_instance();
}
inline ::google::protobuf::DoubleValue* TestWrapper::mutable_double_value() {
  
  if (double_value_ == NULL) {
    double_value_ = new ::google::protobuf::DoubleValue;
  }
  // @@protoc_insertion_point(field_mutable:proto3.TestWrapper.double_value)
  return double_value_;
}
inline ::google::protobuf::DoubleValue* TestWrapper::release_double_value() {
  // @@protoc_insertion_point(field_release:proto3.TestWrapper.double_value)
  
  ::google::protobuf::DoubleValue* temp = double_value_;
  double_value_ = NULL;
  return temp;
}
inline void TestWrapper::set_allocated_double_value(::google::protobuf::DoubleValue* double_value) {
  delete double_value_;
  if (double_value != NULL && double_value->GetArena() != NULL) {
    ::google::protobuf::DoubleValue* new_double_value = new ::google::protobuf::DoubleValue;
    new_double_value->CopyFrom(*double_value);
    double_value = new_double_value;
  }
  double_value_ = double_value;
  if (double_value) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_set_allocated:proto3.TestWrapper.double_value)
}

// optional .google.protobuf.StringValue string_value = 8;
inline bool TestWrapper::has_string_value() const {
  return this != internal_default_instance() && string_value_ != NULL;
}
inline void TestWrapper::clear_string_value() {
  if (GetArenaNoVirtual() == NULL && string_value_ != NULL) delete string_value_;
  string_value_ = NULL;
}
inline const ::google::protobuf::StringValue& TestWrapper::string_value() const {
  // @@protoc_insertion_point(field_get:proto3.TestWrapper.string_value)
  return string_value_ != NULL ? *string_value_
                         : *::google::protobuf::StringValue::internal_default_instance();
}
inline ::google::protobuf::StringValue* TestWrapper::mutable_string_value() {
  
  if (string_value_ == NULL) {
    string_value_ = new ::google::protobuf::StringValue;
  }
  // @@protoc_insertion_point(field_mutable:proto3.TestWrapper.string_value)
  return string_value_;
}
inline ::google::protobuf::StringValue* TestWrapper::release_string_value() {
  // @@protoc_insertion_point(field_release:proto3.TestWrapper.string_value)
  
  ::google::protobuf::StringValue* temp = string_value_;
  string_value_ = NULL;
  return temp;
}
inline void TestWrapper::set_allocated_string_value(::google::protobuf::StringValue* string_value) {
  delete string_value_;
  if (string_value != NULL && string_value->GetArena() != NULL) {
    ::google::protobuf::StringValue* new_string_value = new ::google::protobuf::StringValue;
    new_string_value->CopyFrom(*string_value);
    string_value = new_string_value;
  }
  string_value_ = string_value;
  if (string_value) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_set_allocated:proto3.TestWrapper.string_value)
}

// optional .google.protobuf.BytesValue bytes_value = 9;
inline bool TestWrapper::has_bytes_value() const {
  return this != internal_default_instance() && bytes_value_ != NULL;
}
inline void TestWrapper::clear_bytes_value() {
  if (GetArenaNoVirtual() == NULL && bytes_value_ != NULL) delete bytes_value_;
  bytes_value_ = NULL;
}
inline const ::google::protobuf::BytesValue& TestWrapper::bytes_value() const {
  // @@protoc_insertion_point(field_get:proto3.TestWrapper.bytes_value)
  return bytes_value_ != NULL ? *bytes_value_
                         : *::google::protobuf::BytesValue::internal_default_instance();
}
inline ::google::protobuf::BytesValue* TestWrapper::mutable_bytes_value() {
  
  if (bytes_value_ == NULL) {
    bytes_value_ = new ::google::protobuf::BytesValue;
  }
  // @@protoc_insertion_point(field_mutable:proto3.TestWrapper.bytes_value)
  return bytes_value_;
}
inline ::google::protobuf::BytesValue* TestWrapper::release_bytes_value() {
  // @@protoc_insertion_point(field_release:proto3.TestWrapper.bytes_value)
  
  ::google::protobuf::BytesValue* temp = bytes_value_;
  bytes_value_ = NULL;
  return temp;
}
inline void TestWrapper::set_allocated_bytes_value(::google::protobuf::BytesValue* bytes_value) {
  delete bytes_value_;
  if (bytes_value != NULL && bytes_value->GetArena() != NULL) {
    ::google::protobuf::BytesValue* new_bytes_value = new ::google::protobuf::BytesValue;
    new_bytes_value->CopyFrom(*bytes_value);
    bytes_value = new_bytes_value;
  }
  bytes_value_ = bytes_value;
  if (bytes_value) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_set_allocated:proto3.TestWrapper.bytes_value)
}

// repeated .google.protobuf.BoolValue repeated_bool_value = 11;
inline int TestWrapper::repeated_bool_value_size() const {
  return repeated_bool_value_.size();
}
inline void TestWrapper::clear_repeated_bool_value() {
  repeated_bool_value_.Clear();
}
inline const ::google::protobuf::BoolValue& TestWrapper::repeated_bool_value(int index) const {
  // @@protoc_insertion_point(field_get:proto3.TestWrapper.repeated_bool_value)
  return repeated_bool_value_.Get(index);
}
inline ::google::protobuf::BoolValue* TestWrapper::mutable_repeated_bool_value(int index) {
  // @@protoc_insertion_point(field_mutable:proto3.TestWrapper.repeated_bool_value)
  return repeated_bool_value_.Mutable(index);
}
inline ::google::protobuf::BoolValue* TestWrapper::add_repeated_bool_value() {
  // @@protoc_insertion_point(field_add:proto3.TestWrapper.repeated_bool_value)
  return repeated_bool_value_.Add();
}
inline ::google::protobuf::RepeatedPtrField< ::google::protobuf::BoolValue >*
TestWrapper::mutable_repeated_bool_value() {
  // @@protoc_insertion_point(field_mutable_list:proto3.TestWrapper.repeated_bool_value)
  return &repeated_bool_value_;
}
inline const ::google::protobuf::RepeatedPtrField< ::google::protobuf::BoolValue >&
TestWrapper::repeated_bool_value() const {
  // @@protoc_insertion_point(field_list:proto3.TestWrapper.repeated_bool_value)
  return repeated_bool_value_;
}

// repeated .google.protobuf.Int32Value repeated_int32_value = 12;
inline int TestWrapper::repeated_int32_value_size() const {
  return repeated_int32_value_.size();
}
inline void TestWrapper::clear_repeated_int32_value() {
  repeated_int32_value_.Clear();
}
inline const ::google::protobuf::Int32Value& TestWrapper::repeated_int32_value(int index) const {
  // @@protoc_insertion_point(field_get:proto3.TestWrapper.repeated_int32_value)
  return repeated_int32_value_.Get(index);
}
inline ::google::protobuf::Int32Value* TestWrapper::mutable_repeated_int32_value(int index) {
  // @@protoc_insertion_point(field_mutable:proto3.TestWrapper.repeated_int32_value)
  return repeated_int32_value_.Mutable(index);
}
inline ::google::protobuf::Int32Value* TestWrapper::add_repeated_int32_value() {
  // @@protoc_insertion_point(field_add:proto3.TestWrapper.repeated_int32_value)
  return repeated_int32_value_.Add();
}
inline ::google::protobuf::RepeatedPtrField< ::google::protobuf::Int32Value >*
TestWrapper::mutable_repeated_int32_value() {
  // @@protoc_insertion_point(field_mutable_list:proto3.TestWrapper.repeated_int32_value)
  return &repeated_int32_value_;
}
inline const ::google::protobuf::RepeatedPtrField< ::google::protobuf::Int32Value >&
TestWrapper::repeated_int32_value() const {
  // @@protoc_insertion_point(field_list:proto3.TestWrapper.repeated_int32_value)
  return repeated_int32_value_;
}

// repeated .google.protobuf.Int64Value repeated_int64_value = 13;
inline int TestWrapper::repeated_int64_value_size() const {
  return repeated_int64_value_.size();
}
inline void TestWrapper::clear_repeated_int64_value() {
  repeated_int64_value_.Clear();
}
inline const ::google::protobuf::Int64Value& TestWrapper::repeated_int64_value(int index) const {
  // @@protoc_insertion_point(field_get:proto3.TestWrapper.repeated_int64_value)
  return repeated_int64_value_.Get(index);
}
inline ::google::protobuf::Int64Value* TestWrapper::mutable_repeated_int64_value(int index) {
  // @@protoc_insertion_point(field_mutable:proto3.TestWrapper.repeated_int64_value)
  return repeated_int64_value_.Mutable(index);
}
inline ::google::protobuf::Int64Value* TestWrapper::add_repeated_int64_value() {
  // @@protoc_insertion_point(field_add:proto3.TestWrapper.repeated_int64_value)
  return repeated_int64_value_.Add();
}
inline ::google::protobuf::RepeatedPtrField< ::google::protobuf::Int64Value >*
TestWrapper::mutable_repeated_int64_value() {
  // @@protoc_insertion_point(field_mutable_list:proto3.TestWrapper.repeated_int64_value)
  return &repeated_int64_value_;
}
inline const ::google::protobuf::RepeatedPtrField< ::google::protobuf::Int64Value >&
TestWrapper::repeated_int64_value() const {
  // @@protoc_insertion_point(field_list:proto3.TestWrapper.repeated_int64_value)
  return repeated_int64_value_;
}

// repeated .google.protobuf.UInt32Value repeated_uint32_value = 14;
inline int TestWrapper::repeated_uint32_value_size() const {
  return repeated_uint32_value_.size();
}
inline void TestWrapper::clear_repeated_uint32_value() {
  repeated_uint32_value_.Clear();
}
inline const ::google::protobuf::UInt32Value& TestWrapper::repeated_uint32_value(int index) const {
  // @@protoc_insertion_point(field_get:proto3.TestWrapper.repeated_uint32_value)
  return repeated_uint32_value_.Get(index);
}
inline ::google::protobuf::UInt32Value* TestWrapper::mutable_repeated_uint32_value(int index) {
  // @@protoc_insertion_point(field_mutable:proto3.TestWrapper.repeated_uint32_value)
  return repeated_uint32_value_.Mutable(index);
}
inline ::google::protobuf::UInt32Value* TestWrapper::add_repeated_uint32_value() {
  // @@protoc_insertion_point(field_add:proto3.TestWrapper.repeated_uint32_value)
  return repeated_uint32_value_.Add();
}
inline ::google::protobuf::RepeatedPtrField< ::google::protobuf::UInt32Value >*
TestWrapper::mutable_repeated_uint32_value() {
  // @@protoc_insertion_point(field_mutable_list:proto3.TestWrapper.repeated_uint32_value)
  return &repeated_uint32_value_;
}
inline const ::google::protobuf::RepeatedPtrField< ::google::protobuf::UInt32Value >&
TestWrapper::repeated_uint32_value() const {
  // @@protoc_insertion_point(field_list:proto3.TestWrapper.repeated_uint32_value)
  return repeated_uint32_value_;
}

// repeated .google.protobuf.UInt64Value repeated_uint64_value = 15;
inline int TestWrapper::repeated_uint64_value_size() const {
  return repeated_uint64_value_.size();
}
inline void TestWrapper::clear_repeated_uint64_value() {
  repeated_uint64_value_.Clear();
}
inline const ::google::protobuf::UInt64Value& TestWrapper::repeated_uint64_value(int index) const {
  // @@protoc_insertion_point(field_get:proto3.TestWrapper.repeated_uint64_value)
  return repeated_uint64_value_.Get(index);
}
inline ::google::protobuf::UInt64Value* TestWrapper::mutable_repeated_uint64_value(int index) {
  // @@protoc_insertion_point(field_mutable:proto3.TestWrapper.repeated_uint64_value)
  return repeated_uint64_value_.Mutable(index);
}
inline ::google::protobuf::UInt64Value* TestWrapper::add_repeated_uint64_value() {
  // @@protoc_insertion_point(field_add:proto3.TestWrapper.repeated_uint64_value)
  return repeated_uint64_value_.Add();
}
inline ::google::protobuf::RepeatedPtrField< ::google::protobuf::UInt64Value >*
TestWrapper::mutable_repeated_uint64_value() {
  // @@protoc_insertion_point(field_mutable_list:proto3.TestWrapper.repeated_uint64_value)
  return &repeated_uint64_value_;
}
inline const ::google::protobuf::RepeatedPtrField< ::google::protobuf::UInt64Value >&
TestWrapper::repeated_uint64_value() const {
  // @@protoc_insertion_point(field_list:proto3.TestWrapper.repeated_uint64_value)
  return repeated_uint64_value_;
}

// repeated .google.protobuf.FloatValue repeated_float_value = 16;
inline int TestWrapper::repeated_float_value_size() const {
  return repeated_float_value_.size();
}
inline void TestWrapper::clear_repeated_float_value() {
  repeated_float_value_.Clear();
}
inline const ::google::protobuf::FloatValue& TestWrapper::repeated_float_value(int index) const {
  // @@protoc_insertion_point(field_get:proto3.TestWrapper.repeated_float_value)
  return repeated_float_value_.Get(index);
}
inline ::google::protobuf::FloatValue* TestWrapper::mutable_repeated_float_value(int index) {
  // @@protoc_insertion_point(field_mutable:proto3.TestWrapper.repeated_float_value)
  return repeated_float_value_.Mutable(index);
}
inline ::google::protobuf::FloatValue* TestWrapper::add_repeated_float_value() {
  // @@protoc_insertion_point(field_add:proto3.TestWrapper.repeated_float_value)
  return repeated_float_value_.Add();
}
inline ::google::protobuf::RepeatedPtrField< ::google::protobuf::FloatValue >*
TestWrapper::mutable_repeated_float_value() {
  // @@protoc_insertion_point(field_mutable_list:proto3.TestWrapper.repeated_float_value)
  return &repeated_float_value_;
}
inline const ::google::protobuf::RepeatedPtrField< ::google::protobuf::FloatValue >&
TestWrapper::repeated_float_value() const {
  // @@protoc_insertion_point(field_list:proto3.TestWrapper.repeated_float_value)
  return repeated_float_value_;
}

// repeated .google.protobuf.DoubleValue repeated_double_value = 17;
inline int TestWrapper::repeated_double_value_size() const {
  return repeated_double_value_.size();
}
inline void TestWrapper::clear_repeated_double_value() {
  repeated_double_value_.Clear();
}
inline const ::google::protobuf::DoubleValue& TestWrapper::repeated_double_value(int index) const {
  // @@protoc_insertion_point(field_get:proto3.TestWrapper.repeated_double_value)
  return repeated_double_value_.Get(index);
}
inline ::google::protobuf::DoubleValue* TestWrapper::mutable_repeated_double_value(int index) {
  // @@protoc_insertion_point(field_mutable:proto3.TestWrapper.repeated_double_value)
  return repeated_double_value_.Mutable(index);
}
inline ::google::protobuf::DoubleValue* TestWrapper::add_repeated_double_value() {
  // @@protoc_insertion_point(field_add:proto3.TestWrapper.repeated_double_value)
  return repeated_double_value_.Add();
}
inline ::google::protobuf::RepeatedPtrField< ::google::protobuf::DoubleValue >*
TestWrapper::mutable_repeated_double_value() {
  // @@protoc_insertion_point(field_mutable_list:proto3.TestWrapper.repeated_double_value)
  return &repeated_double_value_;
}
inline const ::google::protobuf::RepeatedPtrField< ::google::protobuf::DoubleValue >&
TestWrapper::repeated_double_value() const {
  // @@protoc_insertion_point(field_list:proto3.TestWrapper.repeated_double_value)
  return repeated_double_value_;
}

// repeated .google.protobuf.StringValue repeated_string_value = 18;
inline int TestWrapper::repeated_string_value_size() const {
  return repeated_string_value_.size();
}
inline void TestWrapper::clear_repeated_string_value() {
  repeated_string_value_.Clear();
}
inline const ::google::protobuf::StringValue& TestWrapper::repeated_string_value(int index) const {
  // @@protoc_insertion_point(field_get:proto3.TestWrapper.repeated_string_value)
  return repeated_string_value_.Get(index);
}
inline ::google::protobuf::StringValue* TestWrapper::mutable_repeated_string_value(int index) {
  // @@protoc_insertion_point(field_mutable:proto3.TestWrapper.repeated_string_value)
  return repeated_string_value_.Mutable(index);
}
inline ::google::protobuf::StringValue* TestWrapper::add_repeated_string_value() {
  // @@protoc_insertion_point(field_add:proto3.TestWrapper.repeated_string_value)
  return repeated_string_value_.Add();
}
inline ::google::protobuf::RepeatedPtrField< ::google::protobuf::StringValue >*
TestWrapper::mutable_repeated_string_value() {
  // @@protoc_insertion_point(field_mutable_list:proto3.TestWrapper.repeated_string_value)
  return &repeated_string_value_;
}
inline const ::google::protobuf::RepeatedPtrField< ::google::protobuf::StringValue >&
TestWrapper::repeated_string_value() const {
  // @@protoc_insertion_point(field_list:proto3.TestWrapper.repeated_string_value)
  return repeated_string_value_;
}

// repeated .google.protobuf.BytesValue repeated_bytes_value = 19;
inline int TestWrapper::repeated_bytes_value_size() const {
  return repeated_bytes_value_.size();
}
inline void TestWrapper::clear_repeated_bytes_value() {
  repeated_bytes_value_.Clear();
}
inline const ::google::protobuf::BytesValue& TestWrapper::repeated_bytes_value(int index) const {
  // @@protoc_insertion_point(field_get:proto3.TestWrapper.repeated_bytes_value)
  return repeated_bytes_value_.Get(index);
}
inline ::google::protobuf::BytesValue* TestWrapper::mutable_repeated_bytes_value(int index) {
  // @@protoc_insertion_point(field_mutable:proto3.TestWrapper.repeated_bytes_value)
  return repeated_bytes_value_.Mutable(index);
}
inline ::google::protobuf::BytesValue* TestWrapper::add_repeated_bytes_value() {
  // @@protoc_insertion_point(field_add:proto3.TestWrapper.repeated_bytes_value)
  return repeated_bytes_value_.Add();
}
inline ::google::protobuf::RepeatedPtrField< ::google::protobuf::BytesValue >*
TestWrapper::mutable_repeated_bytes_value() {
  // @@protoc_insertion_point(field_mutable_list:proto3.TestWrapper.repeated_bytes_value)
  return &repeated_bytes_value_;
}
inline const ::google::protobuf::RepeatedPtrField< ::google::protobuf::BytesValue >&
TestWrapper::repeated_bytes_value() const {
  // @@protoc_insertion_point(field_list:proto3.TestWrapper.repeated_bytes_value)
  return repeated_bytes_value_;
}

inline const TestWrapper* TestWrapper::internal_default_instance() {
  return &TestWrapper_default_instance_.get();
}
// -------------------------------------------------------------------

// TestTimestamp

// optional .google.protobuf.Timestamp value = 1;
inline bool TestTimestamp::has_value() const {
  return this != internal_default_instance() && value_ != NULL;
}
inline void TestTimestamp::clear_value() {
  if (GetArenaNoVirtual() == NULL && value_ != NULL) delete value_;
  value_ = NULL;
}
inline const ::google::protobuf::Timestamp& TestTimestamp::value() const {
  // @@protoc_insertion_point(field_get:proto3.TestTimestamp.value)
  return value_ != NULL ? *value_
                         : *::google::protobuf::Timestamp::internal_default_instance();
}
inline ::google::protobuf::Timestamp* TestTimestamp::mutable_value() {
  
  if (value_ == NULL) {
    value_ = new ::google::protobuf::Timestamp;
  }
  // @@protoc_insertion_point(field_mutable:proto3.TestTimestamp.value)
  return value_;
}
inline ::google::protobuf::Timestamp* TestTimestamp::release_value() {
  // @@protoc_insertion_point(field_release:proto3.TestTimestamp.value)
  
  ::google::protobuf::Timestamp* temp = value_;
  value_ = NULL;
  return temp;
}
inline void TestTimestamp::set_allocated_value(::google::protobuf::Timestamp* value) {
  delete value_;
  if (value != NULL && value->GetArena() != NULL) {
    ::google::protobuf::Timestamp* new_value = new ::google::protobuf::Timestamp;
    new_value->CopyFrom(*value);
    value = new_value;
  }
  value_ = value;
  if (value) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_set_allocated:proto3.TestTimestamp.value)
}

// repeated .google.protobuf.Timestamp repeated_value = 2;
inline int TestTimestamp::repeated_value_size() const {
  return repeated_value_.size();
}
inline void TestTimestamp::clear_repeated_value() {
  repeated_value_.Clear();
}
inline const ::google::protobuf::Timestamp& TestTimestamp::repeated_value(int index) const {
  // @@protoc_insertion_point(field_get:proto3.TestTimestamp.repeated_value)
  return repeated_value_.Get(index);
}
inline ::google::protobuf::Timestamp* TestTimestamp::mutable_repeated_value(int index) {
  // @@protoc_insertion_point(field_mutable:proto3.TestTimestamp.repeated_value)
  return repeated_value_.Mutable(index);
}
inline ::google::protobuf::Timestamp* TestTimestamp::add_repeated_value() {
  // @@protoc_insertion_point(field_add:proto3.TestTimestamp.repeated_value)
  return repeated_value_.Add();
}
inline ::google::protobuf::RepeatedPtrField< ::google::protobuf::Timestamp >*
TestTimestamp::mutable_repeated_value() {
  // @@protoc_insertion_point(field_mutable_list:proto3.TestTimestamp.repeated_value)
  return &repeated_value_;
}
inline const ::google::protobuf::RepeatedPtrField< ::google::protobuf::Timestamp >&
TestTimestamp::repeated_value() const {
  // @@protoc_insertion_point(field_list:proto3.TestTimestamp.repeated_value)
  return repeated_value_;
}

inline const TestTimestamp* TestTimestamp::internal_default_instance() {
  return &TestTimestamp_default_instance_.get();
}
// -------------------------------------------------------------------

// TestDuration

// optional .google.protobuf.Duration value = 1;
inline bool TestDuration::has_value() const {
  return this != internal_default_instance() && value_ != NULL;
}
inline void TestDuration::clear_value() {
  if (GetArenaNoVirtual() == NULL && value_ != NULL) delete value_;
  value_ = NULL;
}
inline const ::google::protobuf::Duration& TestDuration::value() const {
  // @@protoc_insertion_point(field_get:proto3.TestDuration.value)
  return value_ != NULL ? *value_
                         : *::google::protobuf::Duration::internal_default_instance();
}
inline ::google::protobuf::Duration* TestDuration::mutable_value() {
  
  if (value_ == NULL) {
    value_ = new ::google::protobuf::Duration;
  }
  // @@protoc_insertion_point(field_mutable:proto3.TestDuration.value)
  return value_;
}
inline ::google::protobuf::Duration* TestDuration::release_value() {
  // @@protoc_insertion_point(field_release:proto3.TestDuration.value)
  
  ::google::protobuf::Duration* temp = value_;
  value_ = NULL;
  return temp;
}
inline void TestDuration::set_allocated_value(::google::protobuf::Duration* value) {
  delete value_;
  if (value != NULL && value->GetArena() != NULL) {
    ::google::protobuf::Duration* new_value = new ::google::protobuf::Duration;
    new_value->CopyFrom(*value);
    value = new_value;
  }
  value_ = value;
  if (value) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_set_allocated:proto3.TestDuration.value)
}

// repeated .google.protobuf.Duration repeated_value = 2;
inline int TestDuration::repeated_value_size() const {
  return repeated_value_.size();
}
inline void TestDuration::clear_repeated_value() {
  repeated_value_.Clear();
}
inline const ::google::protobuf::Duration& TestDuration::repeated_value(int index) const {
  // @@protoc_insertion_point(field_get:proto3.TestDuration.repeated_value)
  return repeated_value_.Get(index);
}
inline ::google::protobuf::Duration* TestDuration::mutable_repeated_value(int index) {
  // @@protoc_insertion_point(field_mutable:proto3.TestDuration.repeated_value)
  return repeated_value_.Mutable(index);
}
inline ::google::protobuf::Duration* TestDuration::add_repeated_value() {
  // @@protoc_insertion_point(field_add:proto3.TestDuration.repeated_value)
  return repeated_value_.Add();
}
inline ::google::protobuf::RepeatedPtrField< ::google::protobuf::Duration >*
TestDuration::mutable_repeated_value() {
  // @@protoc_insertion_point(field_mutable_list:proto3.TestDuration.repeated_value)
  return &repeated_value_;
}
inline const ::google::protobuf::RepeatedPtrField< ::google::protobuf::Duration >&
TestDuration::repeated_value() const {
  // @@protoc_insertion_point(field_list:proto3.TestDuration.repeated_value)
  return repeated_value_;
}

inline const TestDuration* TestDuration::internal_default_instance() {
  return &TestDuration_default_instance_.get();
}
// -------------------------------------------------------------------

// TestFieldMask

// optional .google.protobuf.FieldMask value = 1;
inline bool TestFieldMask::has_value() const {
  return this != internal_default_instance() && value_ != NULL;
}
inline void TestFieldMask::clear_value() {
  if (GetArenaNoVirtual() == NULL && value_ != NULL) delete value_;
  value_ = NULL;
}
inline const ::google::protobuf::FieldMask& TestFieldMask::value() const {
  // @@protoc_insertion_point(field_get:proto3.TestFieldMask.value)
  return value_ != NULL ? *value_
                         : *::google::protobuf::FieldMask::internal_default_instance();
}
inline ::google::protobuf::FieldMask* TestFieldMask::mutable_value() {
  
  if (value_ == NULL) {
    value_ = new ::google::protobuf::FieldMask;
  }
  // @@protoc_insertion_point(field_mutable:proto3.TestFieldMask.value)
  return value_;
}
inline ::google::protobuf::FieldMask* TestFieldMask::release_value() {
  // @@protoc_insertion_point(field_release:proto3.TestFieldMask.value)
  
  ::google::protobuf::FieldMask* temp = value_;
  value_ = NULL;
  return temp;
}
inline void TestFieldMask::set_allocated_value(::google::protobuf::FieldMask* value) {
  delete value_;
  value_ = value;
  if (value) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_set_allocated:proto3.TestFieldMask.value)
}

inline const TestFieldMask* TestFieldMask::internal_default_instance() {
  return &TestFieldMask_default_instance_.get();
}
// -------------------------------------------------------------------

// TestStruct

// optional .google.protobuf.Struct value = 1;
inline bool TestStruct::has_value() const {
  return this != internal_default_instance() && value_ != NULL;
}
inline void TestStruct::clear_value() {
  if (GetArenaNoVirtual() == NULL && value_ != NULL) delete value_;
  value_ = NULL;
}
inline const ::google::protobuf::Struct& TestStruct::value() const {
  // @@protoc_insertion_point(field_get:proto3.TestStruct.value)
  return value_ != NULL ? *value_
                         : *::google::protobuf::Struct::internal_default_instance();
}
inline ::google::protobuf::Struct* TestStruct::mutable_value() {
  
  if (value_ == NULL) {
    value_ = new ::google::protobuf::Struct;
  }
  // @@protoc_insertion_point(field_mutable:proto3.TestStruct.value)
  return value_;
}
inline ::google::protobuf::Struct* TestStruct::release_value() {
  // @@protoc_insertion_point(field_release:proto3.TestStruct.value)
  
  ::google::protobuf::Struct* temp = value_;
  value_ = NULL;
  return temp;
}
inline void TestStruct::set_allocated_value(::google::protobuf::Struct* value) {
  delete value_;
  if (value != NULL && value->GetArena() != NULL) {
    ::google::protobuf::Struct* new_value = new ::google::protobuf::Struct;
    new_value->CopyFrom(*value);
    value = new_value;
  }
  value_ = value;
  if (value) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_set_allocated:proto3.TestStruct.value)
}

// repeated .google.protobuf.Struct repeated_value = 2;
inline int TestStruct::repeated_value_size() const {
  return repeated_value_.size();
}
inline void TestStruct::clear_repeated_value() {
  repeated_value_.Clear();
}
inline const ::google::protobuf::Struct& TestStruct::repeated_value(int index) const {
  // @@protoc_insertion_point(field_get:proto3.TestStruct.repeated_value)
  return repeated_value_.Get(index);
}
inline ::google::protobuf::Struct* TestStruct::mutable_repeated_value(int index) {
  // @@protoc_insertion_point(field_mutable:proto3.TestStruct.repeated_value)
  return repeated_value_.Mutable(index);
}
inline ::google::protobuf::Struct* TestStruct::add_repeated_value() {
  // @@protoc_insertion_point(field_add:proto3.TestStruct.repeated_value)
  return repeated_value_.Add();
}
inline ::google::protobuf::RepeatedPtrField< ::google::protobuf::Struct >*
TestStruct::mutable_repeated_value() {
  // @@protoc_insertion_point(field_mutable_list:proto3.TestStruct.repeated_value)
  return &repeated_value_;
}
inline const ::google::protobuf::RepeatedPtrField< ::google::protobuf::Struct >&
TestStruct::repeated_value() const {
  // @@protoc_insertion_point(field_list:proto3.TestStruct.repeated_value)
  return repeated_value_;
}

inline const TestStruct* TestStruct::internal_default_instance() {
  return &TestStruct_default_instance_.get();
}
// -------------------------------------------------------------------

// TestAny

// optional .google.protobuf.Any value = 1;
inline bool TestAny::has_value() const {
  return this != internal_default_instance() && value_ != NULL;
}
inline void TestAny::clear_value() {
  if (GetArenaNoVirtual() == NULL && value_ != NULL) delete value_;
  value_ = NULL;
}
inline const ::google::protobuf::Any& TestAny::value() const {
  // @@protoc_insertion_point(field_get:proto3.TestAny.value)
  return value_ != NULL ? *value_
                         : *::google::protobuf::Any::internal_default_instance();
}
inline ::google::protobuf::Any* TestAny::mutable_value() {
  
  if (value_ == NULL) {
    value_ = new ::google::protobuf::Any;
  }
  // @@protoc_insertion_point(field_mutable:proto3.TestAny.value)
  return value_;
}
inline ::google::protobuf::Any* TestAny::release_value() {
  // @@protoc_insertion_point(field_release:proto3.TestAny.value)
  
  ::google::protobuf::Any* temp = value_;
  value_ = NULL;
  return temp;
}
inline void TestAny::set_allocated_value(::google::protobuf::Any* value) {
  delete value_;
  value_ = value;
  if (value) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_set_allocated:proto3.TestAny.value)
}

// repeated .google.protobuf.Any repeated_value = 2;
inline int TestAny::repeated_value_size() const {
  return repeated_value_.size();
}
inline void TestAny::clear_repeated_value() {
  repeated_value_.Clear();
}
inline const ::google::protobuf::Any& TestAny::repeated_value(int index) const {
  // @@protoc_insertion_point(field_get:proto3.TestAny.repeated_value)
  return repeated_value_.Get(index);
}
inline ::google::protobuf::Any* TestAny::mutable_repeated_value(int index) {
  // @@protoc_insertion_point(field_mutable:proto3.TestAny.repeated_value)
  return repeated_value_.Mutable(index);
}
inline ::google::protobuf::Any* TestAny::add_repeated_value() {
  // @@protoc_insertion_point(field_add:proto3.TestAny.repeated_value)
  return repeated_value_.Add();
}
inline ::google::protobuf::RepeatedPtrField< ::google::protobuf::Any >*
TestAny::mutable_repeated_value() {
  // @@protoc_insertion_point(field_mutable_list:proto3.TestAny.repeated_value)
  return &repeated_value_;
}
inline const ::google::protobuf::RepeatedPtrField< ::google::protobuf::Any >&
TestAny::repeated_value() const {
  // @@protoc_insertion_point(field_list:proto3.TestAny.repeated_value)
  return repeated_value_;
}

inline const TestAny* TestAny::internal_default_instance() {
  return &TestAny_default_instance_.get();
}
// -------------------------------------------------------------------

// TestValue

// optional .google.protobuf.Value value = 1;
inline bool TestValue::has_value() const {
  return this != internal_default_instance() && value_ != NULL;
}
inline void TestValue::clear_value() {
  if (GetArenaNoVirtual() == NULL && value_ != NULL) delete value_;
  value_ = NULL;
}
inline const ::google::protobuf::Value& TestValue::value() const {
  // @@protoc_insertion_point(field_get:proto3.TestValue.value)
  return value_ != NULL ? *value_
                         : *::google::protobuf::Value::internal_default_instance();
}
inline ::google::protobuf::Value* TestValue::mutable_value() {
  
  if (value_ == NULL) {
    value_ = new ::google::protobuf::Value;
  }
  // @@protoc_insertion_point(field_mutable:proto3.TestValue.value)
  return value_;
}
inline ::google::protobuf::Value* TestValue::release_value() {
  // @@protoc_insertion_point(field_release:proto3.TestValue.value)
  
  ::google::protobuf::Value* temp = value_;
  value_ = NULL;
  return temp;
}
inline void TestValue::set_allocated_value(::google::protobuf::Value* value) {
  delete value_;
  if (value != NULL && value->GetArena() != NULL) {
    ::google::protobuf::Value* new_value = new ::google::protobuf::Value;
    new_value->CopyFrom(*value);
    value = new_value;
  }
  value_ = value;
  if (value) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_set_allocated:proto3.TestValue.value)
}

// repeated .google.protobuf.Value repeated_value = 2;
inline int TestValue::repeated_value_size() const {
  return repeated_value_.size();
}
inline void TestValue::clear_repeated_value() {
  repeated_value_.Clear();
}
inline const ::google::protobuf::Value& TestValue::repeated_value(int index) const {
  // @@protoc_insertion_point(field_get:proto3.TestValue.repeated_value)
  return repeated_value_.Get(index);
}
inline ::google::protobuf::Value* TestValue::mutable_repeated_value(int index) {
  // @@protoc_insertion_point(field_mutable:proto3.TestValue.repeated_value)
  return repeated_value_.Mutable(index);
}
inline ::google::protobuf::Value* TestValue::add_repeated_value() {
  // @@protoc_insertion_point(field_add:proto3.TestValue.repeated_value)
  return repeated_value_.Add();
}
inline ::google::protobuf::RepeatedPtrField< ::google::protobuf::Value >*
TestValue::mutable_repeated_value() {
  // @@protoc_insertion_point(field_mutable_list:proto3.TestValue.repeated_value)
  return &repeated_value_;
}
inline const ::google::protobuf::RepeatedPtrField< ::google::protobuf::Value >&
TestValue::repeated_value() const {
  // @@protoc_insertion_point(field_list:proto3.TestValue.repeated_value)
  return repeated_value_;
}

inline const TestValue* TestValue::internal_default_instance() {
  return &TestValue_default_instance_.get();
}
// -------------------------------------------------------------------

// TestListValue

// optional .google.protobuf.ListValue value = 1;
inline bool TestListValue::has_value() const {
  return this != internal_default_instance() && value_ != NULL;
}
inline void TestListValue::clear_value() {
  if (GetArenaNoVirtual() == NULL && value_ != NULL) delete value_;
  value_ = NULL;
}
inline const ::google::protobuf::ListValue& TestListValue::value() const {
  // @@protoc_insertion_point(field_get:proto3.TestListValue.value)
  return value_ != NULL ? *value_
                         : *::google::protobuf::ListValue::internal_default_instance();
}
inline ::google::protobuf::ListValue* TestListValue::mutable_value() {
  
  if (value_ == NULL) {
    value_ = new ::google::protobuf::ListValue;
  }
  // @@protoc_insertion_point(field_mutable:proto3.TestListValue.value)
  return value_;
}
inline ::google::protobuf::ListValue* TestListValue::release_value() {
  // @@protoc_insertion_point(field_release:proto3.TestListValue.value)
  
  ::google::protobuf::ListValue* temp = value_;
  value_ = NULL;
  return temp;
}
inline void TestListValue::set_allocated_value(::google::protobuf::ListValue* value) {
  delete value_;
  if (value != NULL && value->GetArena() != NULL) {
    ::google::protobuf::ListValue* new_value = new ::google::protobuf::ListValue;
    new_value->CopyFrom(*value);
    value = new_value;
  }
  value_ = value;
  if (value) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_set_allocated:proto3.TestListValue.value)
}

// repeated .google.protobuf.ListValue repeated_value = 2;
inline int TestListValue::repeated_value_size() const {
  return repeated_value_.size();
}
inline void TestListValue::clear_repeated_value() {
  repeated_value_.Clear();
}
inline const ::google::protobuf::ListValue& TestListValue::repeated_value(int index) const {
  // @@protoc_insertion_point(field_get:proto3.TestListValue.repeated_value)
  return repeated_value_.Get(index);
}
inline ::google::protobuf::ListValue* TestListValue::mutable_repeated_value(int index) {
  // @@protoc_insertion_point(field_mutable:proto3.TestListValue.repeated_value)
  return repeated_value_.Mutable(index);
}
inline ::google::protobuf::ListValue* TestListValue::add_repeated_value() {
  // @@protoc_insertion_point(field_add:proto3.TestListValue.repeated_value)
  return repeated_value_.Add();
}
inline ::google::protobuf::RepeatedPtrField< ::google::protobuf::ListValue >*
TestListValue::mutable_repeated_value() {
  // @@protoc_insertion_point(field_mutable_list:proto3.TestListValue.repeated_value)
  return &repeated_value_;
}
inline const ::google::protobuf::RepeatedPtrField< ::google::protobuf::ListValue >&
TestListValue::repeated_value() const {
  // @@protoc_insertion_point(field_list:proto3.TestListValue.repeated_value)
  return repeated_value_;
}

inline const TestListValue* TestListValue::internal_default_instance() {
  return &TestListValue_default_instance_.get();
}
// -------------------------------------------------------------------

// TestBoolValue

// optional bool bool_value = 1;
inline void TestBoolValue::clear_bool_value() {
  bool_value_ = false;
}
inline bool TestBoolValue::bool_value() const {
  // @@protoc_insertion_point(field_get:proto3.TestBoolValue.bool_value)
  return bool_value_;
}
inline void TestBoolValue::set_bool_value(bool value) {
  
  bool_value_ = value;
  // @@protoc_insertion_point(field_set:proto3.TestBoolValue.bool_value)
}

// map<bool, int32> bool_map = 2;
inline int TestBoolValue::bool_map_size() const {
  return bool_map_.size();
}
inline void TestBoolValue::clear_bool_map() {
  bool_map_.Clear();
}
inline const ::google::protobuf::Map< bool, ::google::protobuf::int32 >&
TestBoolValue::bool_map() const {
  // @@protoc_insertion_point(field_map:proto3.TestBoolValue.bool_map)
  return bool_map_.GetMap();
}
inline ::google::protobuf::Map< bool, ::google::protobuf::int32 >*
TestBoolValue::mutable_bool_map() {
  // @@protoc_insertion_point(field_mutable_map:proto3.TestBoolValue.bool_map)
  return bool_map_.MutableMap();
}

inline const TestBoolValue* TestBoolValue::internal_default_instance() {
  return &TestBoolValue_default_instance_.get();
}
// -------------------------------------------------------------------

// TestCustomJsonName

// optional int32 value = 1[json_name = "@value"];
inline void TestCustomJsonName::clear_value() {
  value_ = 0;
}
inline ::google::protobuf::int32 TestCustomJsonName::value() const {
  // @@protoc_insertion_point(field_get:proto3.TestCustomJsonName.value)
  return value_;
}
inline void TestCustomJsonName::set_value(::google::protobuf::int32 value) {
  
  value_ = value;
  // @@protoc_insertion_point(field_set:proto3.TestCustomJsonName.value)
}

inline const TestCustomJsonName* TestCustomJsonName::internal_default_instance() {
  return &TestCustomJsonName_default_instance_.get();
}
// -------------------------------------------------------------------

// TestExtensions

// optional .protobuf_unittest.TestAllExtensions extensions = 1;
inline bool TestExtensions::has_extensions() const {
  return this != internal_default_instance() && extensions_ != NULL;
}
inline void TestExtensions::clear_extensions() {
  if (GetArenaNoVirtual() == NULL && extensions_ != NULL) delete extensions_;
  extensions_ = NULL;
}
inline const ::protobuf_unittest::TestAllExtensions& TestExtensions::extensions() const {
  // @@protoc_insertion_point(field_get:proto3.TestExtensions.extensions)
  return extensions_ != NULL ? *extensions_
                         : *::protobuf_unittest::TestAllExtensions::internal_default_instance();
}
inline ::protobuf_unittest::TestAllExtensions* TestExtensions::mutable_extensions() {
  
  if (extensions_ == NULL) {
    extensions_ = new ::protobuf_unittest::TestAllExtensions;
  }
  // @@protoc_insertion_point(field_mutable:proto3.TestExtensions.extensions)
  return extensions_;
}
inline ::protobuf_unittest::TestAllExtensions* TestExtensions::release_extensions() {
  // @@protoc_insertion_point(field_release:proto3.TestExtensions.extensions)
  
  ::protobuf_unittest::TestAllExtensions* temp = extensions_;
  extensions_ = NULL;
  return temp;
}
inline void TestExtensions::set_allocated_extensions(::protobuf_unittest::TestAllExtensions* extensions) {
  delete extensions_;
  if (extensions != NULL && extensions->GetArena() != NULL) {
    ::protobuf_unittest::TestAllExtensions* new_extensions = new ::protobuf_unittest::TestAllExtensions;
    new_extensions->CopyFrom(*extensions);
    extensions = new_extensions;
  }
  extensions_ = extensions;
  if (extensions) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_set_allocated:proto3.TestExtensions.extensions)
}

inline const TestExtensions* TestExtensions::internal_default_instance() {
  return &TestExtensions_default_instance_.get();
}
#endif  // !PROTOBUF_INLINE_NOT_IN_HEADERS
// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------


// @@protoc_insertion_point(namespace_scope)

}  // namespace proto3

#ifndef SWIG
namespace google {
namespace protobuf {

template <> struct is_proto_enum< ::proto3::EnumType> : ::google::protobuf::internal::true_type {};
template <>
inline const EnumDescriptor* GetEnumDescriptor< ::proto3::EnumType>() {
  return ::proto3::EnumType_descriptor();
}

}  // namespace protobuf
}  // namespace google
#endif  // SWIG

// @@protoc_insertion_point(global_scope)

#endif  // PROTOBUF_google_2fprotobuf_2futil_2fjson_5fformat_5fproto3_2eproto__INCLUDED

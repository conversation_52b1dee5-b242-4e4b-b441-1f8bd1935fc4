// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: google/protobuf/util/internal/testdata/default_value.proto

#define INTERNAL_SUPPRESS_PROTOBUF_FIELD_DEPRECATION
#include <google/protobuf/util/internal/testdata/default_value.pb.h>

#include <algorithm>

#include <google/protobuf/stubs/common.h>
#include <google/protobuf/stubs/port.h>
#include <google/protobuf/stubs/once.h>
#include <google/protobuf/io/coded_stream.h>
#include <google/protobuf/wire_format_lite_inl.h>
#include <google/protobuf/descriptor.h>
#include <google/protobuf/generated_message_reflection.h>
#include <google/protobuf/reflection_ops.h>
#include <google/protobuf/wire_format.h>
// @@protoc_insertion_point(includes)

namespace google {
namespace protobuf {
namespace testing {

namespace {

const ::google::protobuf::Descriptor* DefaultValueTestCases_descriptor_ = NULL;
const ::google::protobuf::internal::GeneratedMessageReflection*
  DefaultValueTestCases_reflection_ = NULL;
const ::google::protobuf::Descriptor* DoubleMessage_descriptor_ = NULL;
const ::google::protobuf::internal::GeneratedMessageReflection*
  DoubleMessage_reflection_ = NULL;
struct DoubleMessageOneofInstance {
  ::google::protobuf::internal::ArenaStringPtr str_value_;
  ::google::protobuf::int64 num_value_;
}* DoubleMessage_default_oneof_instance_ = NULL;
const ::google::protobuf::Descriptor* StructMessage_descriptor_ = NULL;
const ::google::protobuf::internal::GeneratedMessageReflection*
  StructMessage_reflection_ = NULL;
const ::google::protobuf::Descriptor* ValueMessage_descriptor_ = NULL;
const ::google::protobuf::internal::GeneratedMessageReflection*
  ValueMessage_reflection_ = NULL;
const ::google::protobuf::Descriptor* ListValueMessage_descriptor_ = NULL;
const ::google::protobuf::internal::GeneratedMessageReflection*
  ListValueMessage_reflection_ = NULL;
const ::google::protobuf::Descriptor* RequestMessage_descriptor_ = NULL;
const ::google::protobuf::internal::GeneratedMessageReflection*
  RequestMessage_reflection_ = NULL;
const ::google::protobuf::Descriptor* AnyMessage_descriptor_ = NULL;
const ::google::protobuf::internal::GeneratedMessageReflection*
  AnyMessage_reflection_ = NULL;
const ::google::protobuf::Descriptor* AnyData_descriptor_ = NULL;
const ::google::protobuf::internal::GeneratedMessageReflection*
  AnyData_reflection_ = NULL;
const ::google::protobuf::Descriptor* AnyData_MapDataEntry_descriptor_ = NULL;
const ::google::protobuf::Descriptor* StringtoIntMap_descriptor_ = NULL;
const ::google::protobuf::internal::GeneratedMessageReflection*
  StringtoIntMap_reflection_ = NULL;
const ::google::protobuf::Descriptor* StringtoIntMap_MapEntry_descriptor_ = NULL;
const ::google::protobuf::Descriptor* IntToStringMap_descriptor_ = NULL;
const ::google::protobuf::internal::GeneratedMessageReflection*
  IntToStringMap_reflection_ = NULL;
const ::google::protobuf::Descriptor* IntToStringMap_MapEntry_descriptor_ = NULL;
const ::google::protobuf::Descriptor* MixedMap_descriptor_ = NULL;
const ::google::protobuf::internal::GeneratedMessageReflection*
  MixedMap_reflection_ = NULL;
const ::google::protobuf::Descriptor* MixedMap_MapEntry_descriptor_ = NULL;
const ::google::protobuf::Descriptor* MixedMap2_descriptor_ = NULL;
const ::google::protobuf::internal::GeneratedMessageReflection*
  MixedMap2_reflection_ = NULL;
const ::google::protobuf::Descriptor* MixedMap2_MapEntry_descriptor_ = NULL;
const ::google::protobuf::EnumDescriptor* MixedMap2_E_descriptor_ = NULL;
const ::google::protobuf::Descriptor* MessageMap_descriptor_ = NULL;
const ::google::protobuf::internal::GeneratedMessageReflection*
  MessageMap_reflection_ = NULL;
const ::google::protobuf::Descriptor* MessageMap_M_descriptor_ = NULL;
const ::google::protobuf::internal::GeneratedMessageReflection*
  MessageMap_M_reflection_ = NULL;
const ::google::protobuf::Descriptor* MessageMap_MapEntry_descriptor_ = NULL;
const ::google::protobuf::Descriptor* DoubleValueMessage_descriptor_ = NULL;
const ::google::protobuf::internal::GeneratedMessageReflection*
  DoubleValueMessage_reflection_ = NULL;

}  // namespace


void protobuf_AssignDesc_google_2fprotobuf_2futil_2finternal_2ftestdata_2fdefault_5fvalue_2eproto() GOOGLE_ATTRIBUTE_COLD;
void protobuf_AssignDesc_google_2fprotobuf_2futil_2finternal_2ftestdata_2fdefault_5fvalue_2eproto() {
  protobuf_AddDesc_google_2fprotobuf_2futil_2finternal_2ftestdata_2fdefault_5fvalue_2eproto();
  const ::google::protobuf::FileDescriptor* file =
    ::google::protobuf::DescriptorPool::generated_pool()->FindFileByName(
      "google/protobuf/util/internal/testdata/default_value.proto");
  GOOGLE_CHECK(file != NULL);
  DefaultValueTestCases_descriptor_ = file->message_type(0);
  static const int DefaultValueTestCases_offsets_[44] = {
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(DefaultValueTestCases, empty_double_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(DefaultValueTestCases, double_with_default_value_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(DefaultValueTestCases, double_with_nondefault_value_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(DefaultValueTestCases, repeated_double_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(DefaultValueTestCases, nested_message_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(DefaultValueTestCases, repeated_nested_message_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(DefaultValueTestCases, double_message_with_oneof_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(DefaultValueTestCases, empty_struct_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(DefaultValueTestCases, empty_struct2_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(DefaultValueTestCases, struct_with_null_value_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(DefaultValueTestCases, struct_with_values_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(DefaultValueTestCases, struct_with_nested_struct_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(DefaultValueTestCases, struct_with_nested_list_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(DefaultValueTestCases, struct_with_list_of_nulls_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(DefaultValueTestCases, struct_with_list_of_lists_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(DefaultValueTestCases, struct_with_list_of_structs_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(DefaultValueTestCases, top_level_struct_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(DefaultValueTestCases, value_wrapper_simple_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(DefaultValueTestCases, value_wrapper_with_struct_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(DefaultValueTestCases, value_wrapper_with_list_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(DefaultValueTestCases, list_value_wrapper_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(DefaultValueTestCases, top_level_value_simple_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(DefaultValueTestCases, top_level_value_with_struct_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(DefaultValueTestCases, top_level_value_with_list_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(DefaultValueTestCases, top_level_listvalue_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(DefaultValueTestCases, empty_any_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(DefaultValueTestCases, type_only_any_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(DefaultValueTestCases, recursive_any_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(DefaultValueTestCases, any_with_message_value_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(DefaultValueTestCases, any_with_nested_message_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(DefaultValueTestCases, any_with_message_containing_map_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(DefaultValueTestCases, any_with_message_containing_struct_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(DefaultValueTestCases, top_level_any_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(DefaultValueTestCases, empty_map_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(DefaultValueTestCases, string_to_int_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(DefaultValueTestCases, int_to_string_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(DefaultValueTestCases, mixed1_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(DefaultValueTestCases, mixed2_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(DefaultValueTestCases, empty_mixed2_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(DefaultValueTestCases, map_of_objects_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(DefaultValueTestCases, mixed_empty_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(DefaultValueTestCases, message_map_empty_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(DefaultValueTestCases, double_value_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(DefaultValueTestCases, double_value_default_),
  };
  DefaultValueTestCases_reflection_ =
    ::google::protobuf::internal::GeneratedMessageReflection::NewGeneratedMessageReflection(
      DefaultValueTestCases_descriptor_,
      DefaultValueTestCases::internal_default_instance(),
      DefaultValueTestCases_offsets_,
      -1,
      -1,
      -1,
      sizeof(DefaultValueTestCases),
      GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(DefaultValueTestCases, _internal_metadata_));
  DoubleMessage_descriptor_ = file->message_type(1);
  static const int DoubleMessage_offsets_[8] = {
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(DoubleMessage, double_value_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(DoubleMessage, repeated_double_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(DoubleMessage, nested_message_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(DoubleMessage, repeated_nested_message_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(DoubleMessage, double_wrapper_),
    PROTO2_GENERATED_DEFAULT_ONEOF_FIELD_OFFSET(DoubleMessage_default_oneof_instance_, str_value_),
    PROTO2_GENERATED_DEFAULT_ONEOF_FIELD_OFFSET(DoubleMessage_default_oneof_instance_, num_value_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(DoubleMessage, value_),
  };
  DoubleMessage_reflection_ =
    ::google::protobuf::internal::GeneratedMessageReflection::NewGeneratedMessageReflection(
      DoubleMessage_descriptor_,
      DoubleMessage::internal_default_instance(),
      DoubleMessage_offsets_,
      -1,
      -1,
      -1,
      DoubleMessage_default_oneof_instance_,
      GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(DoubleMessage, _oneof_case_[0]),
      sizeof(DoubleMessage),
      GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(DoubleMessage, _internal_metadata_));
  StructMessage_descriptor_ = file->message_type(2);
  static const int StructMessage_offsets_[1] = {
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(StructMessage, struct__),
  };
  StructMessage_reflection_ =
    ::google::protobuf::internal::GeneratedMessageReflection::NewGeneratedMessageReflection(
      StructMessage_descriptor_,
      StructMessage::internal_default_instance(),
      StructMessage_offsets_,
      -1,
      -1,
      -1,
      sizeof(StructMessage),
      GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(StructMessage, _internal_metadata_));
  ValueMessage_descriptor_ = file->message_type(3);
  static const int ValueMessage_offsets_[1] = {
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(ValueMessage, value_),
  };
  ValueMessage_reflection_ =
    ::google::protobuf::internal::GeneratedMessageReflection::NewGeneratedMessageReflection(
      ValueMessage_descriptor_,
      ValueMessage::internal_default_instance(),
      ValueMessage_offsets_,
      -1,
      -1,
      -1,
      sizeof(ValueMessage),
      GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(ValueMessage, _internal_metadata_));
  ListValueMessage_descriptor_ = file->message_type(4);
  static const int ListValueMessage_offsets_[1] = {
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(ListValueMessage, shopping_list_),
  };
  ListValueMessage_reflection_ =
    ::google::protobuf::internal::GeneratedMessageReflection::NewGeneratedMessageReflection(
      ListValueMessage_descriptor_,
      ListValueMessage::internal_default_instance(),
      ListValueMessage_offsets_,
      -1,
      -1,
      -1,
      sizeof(ListValueMessage),
      GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(ListValueMessage, _internal_metadata_));
  RequestMessage_descriptor_ = file->message_type(5);
  static const int RequestMessage_offsets_[1] = {
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(RequestMessage, content_),
  };
  RequestMessage_reflection_ =
    ::google::protobuf::internal::GeneratedMessageReflection::NewGeneratedMessageReflection(
      RequestMessage_descriptor_,
      RequestMessage::internal_default_instance(),
      RequestMessage_offsets_,
      -1,
      -1,
      -1,
      sizeof(RequestMessage),
      GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(RequestMessage, _internal_metadata_));
  AnyMessage_descriptor_ = file->message_type(6);
  static const int AnyMessage_offsets_[2] = {
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(AnyMessage, any_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(AnyMessage, data_),
  };
  AnyMessage_reflection_ =
    ::google::protobuf::internal::GeneratedMessageReflection::NewGeneratedMessageReflection(
      AnyMessage_descriptor_,
      AnyMessage::internal_default_instance(),
      AnyMessage_offsets_,
      -1,
      -1,
      -1,
      sizeof(AnyMessage),
      GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(AnyMessage, _internal_metadata_));
  AnyData_descriptor_ = file->message_type(7);
  static const int AnyData_offsets_[7] = {
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(AnyData, attr_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(AnyData, str_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(AnyData, msgs_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(AnyData, nested_data_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(AnyData, map_data_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(AnyData, struct_data_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(AnyData, repeated_data_),
  };
  AnyData_reflection_ =
    ::google::protobuf::internal::GeneratedMessageReflection::NewGeneratedMessageReflection(
      AnyData_descriptor_,
      AnyData::internal_default_instance(),
      AnyData_offsets_,
      -1,
      -1,
      -1,
      sizeof(AnyData),
      GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(AnyData, _internal_metadata_));
  AnyData_MapDataEntry_descriptor_ = AnyData_descriptor_->nested_type(0);
  StringtoIntMap_descriptor_ = file->message_type(8);
  static const int StringtoIntMap_offsets_[1] = {
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(StringtoIntMap, map_),
  };
  StringtoIntMap_reflection_ =
    ::google::protobuf::internal::GeneratedMessageReflection::NewGeneratedMessageReflection(
      StringtoIntMap_descriptor_,
      StringtoIntMap::internal_default_instance(),
      StringtoIntMap_offsets_,
      -1,
      -1,
      -1,
      sizeof(StringtoIntMap),
      GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(StringtoIntMap, _internal_metadata_));
  StringtoIntMap_MapEntry_descriptor_ = StringtoIntMap_descriptor_->nested_type(0);
  IntToStringMap_descriptor_ = file->message_type(9);
  static const int IntToStringMap_offsets_[1] = {
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(IntToStringMap, map_),
  };
  IntToStringMap_reflection_ =
    ::google::protobuf::internal::GeneratedMessageReflection::NewGeneratedMessageReflection(
      IntToStringMap_descriptor_,
      IntToStringMap::internal_default_instance(),
      IntToStringMap_offsets_,
      -1,
      -1,
      -1,
      sizeof(IntToStringMap),
      GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(IntToStringMap, _internal_metadata_));
  IntToStringMap_MapEntry_descriptor_ = IntToStringMap_descriptor_->nested_type(0);
  MixedMap_descriptor_ = file->message_type(10);
  static const int MixedMap_offsets_[3] = {
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MixedMap, msg_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MixedMap, map_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MixedMap, int_value_),
  };
  MixedMap_reflection_ =
    ::google::protobuf::internal::GeneratedMessageReflection::NewGeneratedMessageReflection(
      MixedMap_descriptor_,
      MixedMap::internal_default_instance(),
      MixedMap_offsets_,
      -1,
      -1,
      -1,
      sizeof(MixedMap),
      GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MixedMap, _internal_metadata_));
  MixedMap_MapEntry_descriptor_ = MixedMap_descriptor_->nested_type(0);
  MixedMap2_descriptor_ = file->message_type(11);
  static const int MixedMap2_offsets_[3] = {
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MixedMap2, map_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MixedMap2, ee_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MixedMap2, msg_),
  };
  MixedMap2_reflection_ =
    ::google::protobuf::internal::GeneratedMessageReflection::NewGeneratedMessageReflection(
      MixedMap2_descriptor_,
      MixedMap2::internal_default_instance(),
      MixedMap2_offsets_,
      -1,
      -1,
      -1,
      sizeof(MixedMap2),
      GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MixedMap2, _internal_metadata_));
  MixedMap2_MapEntry_descriptor_ = MixedMap2_descriptor_->nested_type(0);
  MixedMap2_E_descriptor_ = MixedMap2_descriptor_->enum_type(0);
  MessageMap_descriptor_ = file->message_type(12);
  static const int MessageMap_offsets_[1] = {
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MessageMap, map_),
  };
  MessageMap_reflection_ =
    ::google::protobuf::internal::GeneratedMessageReflection::NewGeneratedMessageReflection(
      MessageMap_descriptor_,
      MessageMap::internal_default_instance(),
      MessageMap_offsets_,
      -1,
      -1,
      -1,
      sizeof(MessageMap),
      GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MessageMap, _internal_metadata_));
  MessageMap_M_descriptor_ = MessageMap_descriptor_->nested_type(0);
  static const int MessageMap_M_offsets_[2] = {
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MessageMap_M, inner_int_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MessageMap_M, inner_text_),
  };
  MessageMap_M_reflection_ =
    ::google::protobuf::internal::GeneratedMessageReflection::NewGeneratedMessageReflection(
      MessageMap_M_descriptor_,
      MessageMap_M::internal_default_instance(),
      MessageMap_M_offsets_,
      -1,
      -1,
      -1,
      sizeof(MessageMap_M),
      GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MessageMap_M, _internal_metadata_));
  MessageMap_MapEntry_descriptor_ = MessageMap_descriptor_->nested_type(1);
  DoubleValueMessage_descriptor_ = file->message_type(13);
  static const int DoubleValueMessage_offsets_[1] = {
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(DoubleValueMessage, double__),
  };
  DoubleValueMessage_reflection_ =
    ::google::protobuf::internal::GeneratedMessageReflection::NewGeneratedMessageReflection(
      DoubleValueMessage_descriptor_,
      DoubleValueMessage::internal_default_instance(),
      DoubleValueMessage_offsets_,
      -1,
      -1,
      -1,
      sizeof(DoubleValueMessage),
      GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(DoubleValueMessage, _internal_metadata_));
}

namespace {

GOOGLE_PROTOBUF_DECLARE_ONCE(protobuf_AssignDescriptors_once_);
void protobuf_AssignDescriptorsOnce() {
  ::google::protobuf::GoogleOnceInit(&protobuf_AssignDescriptors_once_,
                 &protobuf_AssignDesc_google_2fprotobuf_2futil_2finternal_2ftestdata_2fdefault_5fvalue_2eproto);
}

void protobuf_RegisterTypes(const ::std::string&) GOOGLE_ATTRIBUTE_COLD;
void protobuf_RegisterTypes(const ::std::string&) {
  protobuf_AssignDescriptorsOnce();
  ::google::protobuf::MessageFactory::InternalRegisterGeneratedMessage(
      DefaultValueTestCases_descriptor_, DefaultValueTestCases::internal_default_instance());
  ::google::protobuf::MessageFactory::InternalRegisterGeneratedMessage(
      DoubleMessage_descriptor_, DoubleMessage::internal_default_instance());
  ::google::protobuf::MessageFactory::InternalRegisterGeneratedMessage(
      StructMessage_descriptor_, StructMessage::internal_default_instance());
  ::google::protobuf::MessageFactory::InternalRegisterGeneratedMessage(
      ValueMessage_descriptor_, ValueMessage::internal_default_instance());
  ::google::protobuf::MessageFactory::InternalRegisterGeneratedMessage(
      ListValueMessage_descriptor_, ListValueMessage::internal_default_instance());
  ::google::protobuf::MessageFactory::InternalRegisterGeneratedMessage(
      RequestMessage_descriptor_, RequestMessage::internal_default_instance());
  ::google::protobuf::MessageFactory::InternalRegisterGeneratedMessage(
      AnyMessage_descriptor_, AnyMessage::internal_default_instance());
  ::google::protobuf::MessageFactory::InternalRegisterGeneratedMessage(
      AnyData_descriptor_, AnyData::internal_default_instance());
  ::google::protobuf::MessageFactory::InternalRegisterGeneratedMessage(
        AnyData_MapDataEntry_descriptor_,
        ::google::protobuf::internal::MapEntry<
            ::std::string,
            ::std::string,
            ::google::protobuf::internal::WireFormatLite::TYPE_STRING,
            ::google::protobuf::internal::WireFormatLite::TYPE_STRING,
            0>::CreateDefaultInstance(
                AnyData_MapDataEntry_descriptor_));
  ::google::protobuf::MessageFactory::InternalRegisterGeneratedMessage(
      StringtoIntMap_descriptor_, StringtoIntMap::internal_default_instance());
  ::google::protobuf::MessageFactory::InternalRegisterGeneratedMessage(
        StringtoIntMap_MapEntry_descriptor_,
        ::google::protobuf::internal::MapEntry<
            ::std::string,
            ::google::protobuf::int32,
            ::google::protobuf::internal::WireFormatLite::TYPE_STRING,
            ::google::protobuf::internal::WireFormatLite::TYPE_INT32,
            0>::CreateDefaultInstance(
                StringtoIntMap_MapEntry_descriptor_));
  ::google::protobuf::MessageFactory::InternalRegisterGeneratedMessage(
      IntToStringMap_descriptor_, IntToStringMap::internal_default_instance());
  ::google::protobuf::MessageFactory::InternalRegisterGeneratedMessage(
        IntToStringMap_MapEntry_descriptor_,
        ::google::protobuf::internal::MapEntry<
            ::google::protobuf::int32,
            ::std::string,
            ::google::protobuf::internal::WireFormatLite::TYPE_INT32,
            ::google::protobuf::internal::WireFormatLite::TYPE_STRING,
            0>::CreateDefaultInstance(
                IntToStringMap_MapEntry_descriptor_));
  ::google::protobuf::MessageFactory::InternalRegisterGeneratedMessage(
      MixedMap_descriptor_, MixedMap::internal_default_instance());
  ::google::protobuf::MessageFactory::InternalRegisterGeneratedMessage(
        MixedMap_MapEntry_descriptor_,
        ::google::protobuf::internal::MapEntry<
            ::std::string,
            float,
            ::google::protobuf::internal::WireFormatLite::TYPE_STRING,
            ::google::protobuf::internal::WireFormatLite::TYPE_FLOAT,
            0>::CreateDefaultInstance(
                MixedMap_MapEntry_descriptor_));
  ::google::protobuf::MessageFactory::InternalRegisterGeneratedMessage(
      MixedMap2_descriptor_, MixedMap2::internal_default_instance());
  ::google::protobuf::MessageFactory::InternalRegisterGeneratedMessage(
        MixedMap2_MapEntry_descriptor_,
        ::google::protobuf::internal::MapEntry<
            ::google::protobuf::int32,
            bool,
            ::google::protobuf::internal::WireFormatLite::TYPE_INT32,
            ::google::protobuf::internal::WireFormatLite::TYPE_BOOL,
            0>::CreateDefaultInstance(
                MixedMap2_MapEntry_descriptor_));
  ::google::protobuf::MessageFactory::InternalRegisterGeneratedMessage(
      MessageMap_descriptor_, MessageMap::internal_default_instance());
  ::google::protobuf::MessageFactory::InternalRegisterGeneratedMessage(
      MessageMap_M_descriptor_, MessageMap_M::internal_default_instance());
  ::google::protobuf::MessageFactory::InternalRegisterGeneratedMessage(
        MessageMap_MapEntry_descriptor_,
        ::google::protobuf::internal::MapEntry<
            ::std::string,
            ::google::protobuf::testing::MessageMap_M,
            ::google::protobuf::internal::WireFormatLite::TYPE_STRING,
            ::google::protobuf::internal::WireFormatLite::TYPE_MESSAGE,
            0>::CreateDefaultInstance(
                MessageMap_MapEntry_descriptor_));
  ::google::protobuf::MessageFactory::InternalRegisterGeneratedMessage(
      DoubleValueMessage_descriptor_, DoubleValueMessage::internal_default_instance());
}

}  // namespace

void protobuf_ShutdownFile_google_2fprotobuf_2futil_2finternal_2ftestdata_2fdefault_5fvalue_2eproto() {
  DefaultValueTestCases_default_instance_.Shutdown();
  delete DefaultValueTestCases_reflection_;
  DoubleMessage_default_instance_.Shutdown();
  delete DoubleMessage_default_oneof_instance_;
  delete DoubleMessage_reflection_;
  StructMessage_default_instance_.Shutdown();
  delete StructMessage_reflection_;
  ValueMessage_default_instance_.Shutdown();
  delete ValueMessage_reflection_;
  ListValueMessage_default_instance_.Shutdown();
  delete ListValueMessage_reflection_;
  RequestMessage_default_instance_.Shutdown();
  delete RequestMessage_reflection_;
  AnyMessage_default_instance_.Shutdown();
  delete AnyMessage_reflection_;
  AnyData_default_instance_.Shutdown();
  delete AnyData_reflection_;
  StringtoIntMap_default_instance_.Shutdown();
  delete StringtoIntMap_reflection_;
  IntToStringMap_default_instance_.Shutdown();
  delete IntToStringMap_reflection_;
  MixedMap_default_instance_.Shutdown();
  delete MixedMap_reflection_;
  MixedMap2_default_instance_.Shutdown();
  delete MixedMap2_reflection_;
  MessageMap_default_instance_.Shutdown();
  delete MessageMap_reflection_;
  MessageMap_M_default_instance_.Shutdown();
  delete MessageMap_M_reflection_;
  DoubleValueMessage_default_instance_.Shutdown();
  delete DoubleValueMessage_reflection_;
}

void protobuf_InitDefaults_google_2fprotobuf_2futil_2finternal_2ftestdata_2fdefault_5fvalue_2eproto_impl() {
  GOOGLE_PROTOBUF_VERIFY_VERSION;

  ::google::protobuf::protobuf_InitDefaults_google_2fprotobuf_2fany_2eproto();
  ::google::protobuf::protobuf_InitDefaults_google_2fprotobuf_2fstruct_2eproto();
  ::google::protobuf::protobuf_InitDefaults_google_2fprotobuf_2fwrappers_2eproto();
  DefaultValueTestCases_default_instance_.DefaultConstruct();
  ::google::protobuf::internal::GetEmptyString();
  DoubleMessage_default_instance_.DefaultConstruct();
  DoubleMessage_default_oneof_instance_ = new DoubleMessageOneofInstance();
  StructMessage_default_instance_.DefaultConstruct();
  ValueMessage_default_instance_.DefaultConstruct();
  ListValueMessage_default_instance_.DefaultConstruct();
  ::google::protobuf::internal::GetEmptyString();
  RequestMessage_default_instance_.DefaultConstruct();
  AnyMessage_default_instance_.DefaultConstruct();
  ::google::protobuf::internal::GetEmptyString();
  AnyData_default_instance_.DefaultConstruct();
  ::google::protobuf::internal::GetEmptyString();
  ::google::protobuf::internal::GetEmptyString();
  StringtoIntMap_default_instance_.DefaultConstruct();
  ::google::protobuf::internal::GetEmptyString();
  IntToStringMap_default_instance_.DefaultConstruct();
  ::google::protobuf::internal::GetEmptyString();
  ::google::protobuf::internal::GetEmptyString();
  MixedMap_default_instance_.DefaultConstruct();
  ::google::protobuf::internal::GetEmptyString();
  ::google::protobuf::internal::GetEmptyString();
  MixedMap2_default_instance_.DefaultConstruct();
  ::google::protobuf::internal::GetEmptyString();
  MessageMap_default_instance_.DefaultConstruct();
  ::google::protobuf::internal::GetEmptyString();
  MessageMap_M_default_instance_.DefaultConstruct();
  DoubleValueMessage_default_instance_.DefaultConstruct();
  DefaultValueTestCases_default_instance_.get_mutable()->InitAsDefaultInstance();
  DoubleMessage_default_instance_.get_mutable()->InitAsDefaultInstance();
  StructMessage_default_instance_.get_mutable()->InitAsDefaultInstance();
  ValueMessage_default_instance_.get_mutable()->InitAsDefaultInstance();
  ListValueMessage_default_instance_.get_mutable()->InitAsDefaultInstance();
  RequestMessage_default_instance_.get_mutable()->InitAsDefaultInstance();
  AnyMessage_default_instance_.get_mutable()->InitAsDefaultInstance();
  AnyData_default_instance_.get_mutable()->InitAsDefaultInstance();
  StringtoIntMap_default_instance_.get_mutable()->InitAsDefaultInstance();
  IntToStringMap_default_instance_.get_mutable()->InitAsDefaultInstance();
  MixedMap_default_instance_.get_mutable()->InitAsDefaultInstance();
  MixedMap2_default_instance_.get_mutable()->InitAsDefaultInstance();
  MessageMap_default_instance_.get_mutable()->InitAsDefaultInstance();
  MessageMap_M_default_instance_.get_mutable()->InitAsDefaultInstance();
  DoubleValueMessage_default_instance_.get_mutable()->InitAsDefaultInstance();
}

GOOGLE_PROTOBUF_DECLARE_ONCE(protobuf_InitDefaults_google_2fprotobuf_2futil_2finternal_2ftestdata_2fdefault_5fvalue_2eproto_once_);
void protobuf_InitDefaults_google_2fprotobuf_2futil_2finternal_2ftestdata_2fdefault_5fvalue_2eproto() {
  ::google::protobuf::GoogleOnceInit(&protobuf_InitDefaults_google_2fprotobuf_2futil_2finternal_2ftestdata_2fdefault_5fvalue_2eproto_once_,
                 &protobuf_InitDefaults_google_2fprotobuf_2futil_2finternal_2ftestdata_2fdefault_5fvalue_2eproto_impl);
}
void protobuf_AddDesc_google_2fprotobuf_2futil_2finternal_2ftestdata_2fdefault_5fvalue_2eproto_impl() {
  GOOGLE_PROTOBUF_VERIFY_VERSION;

  protobuf_InitDefaults_google_2fprotobuf_2futil_2finternal_2ftestdata_2fdefault_5fvalue_2eproto();
  ::google::protobuf::DescriptorPool::InternalAddGeneratedFile(
    "\n:google/protobuf/util/internal/testdata"
    "/default_value.proto\022\027google.protobuf.te"
    "sting\032\031google/protobuf/any.proto\032\034google"
    "/protobuf/struct.proto\032\036google/protobuf/"
    "wrappers.proto\"\211\027\n\025DefaultValueTestCases"
    "\022<\n\014empty_double\030\001 \001(\0132&.google.protobuf"
    ".testing.DoubleMessage\022I\n\031double_with_de"
    "fault_value\030\002 \001(\0132&.google.protobuf.test"
    "ing.DoubleMessage\022L\n\034double_with_nondefa"
    "ult_value\030\003 \001(\0132&.google.protobuf.testin"
    "g.DoubleMessage\022\?\n\017repeated_double\030\004 \001(\013"
    "2&.google.protobuf.testing.DoubleMessage"
    "\022>\n\016nested_message\030\005 \001(\0132&.google.protob"
    "uf.testing.DoubleMessage\022G\n\027repeated_nes"
    "ted_message\030\006 \001(\0132&.google.protobuf.test"
    "ing.DoubleMessage\022I\n\031double_message_with"
    "_oneof\030\007 \001(\0132&.google.protobuf.testing.D"
    "oubleMessage\022=\n\014empty_struct\030\311\001 \001(\0132&.go"
    "ogle.protobuf.testing.StructMessage\022>\n\re"
    "mpty_struct2\030\312\001 \001(\0132&.google.protobuf.te"
    "sting.StructMessage\022G\n\026struct_with_null_"
    "value\030\313\001 \001(\0132&.google.protobuf.testing.S"
    "tructMessage\022C\n\022struct_with_values\030\314\001 \001("
    "\0132&.google.protobuf.testing.StructMessag"
    "e\022J\n\031struct_with_nested_struct\030\315\001 \001(\0132&."
    "google.protobuf.testing.StructMessage\022H\n"
    "\027struct_with_nested_list\030\316\001 \001(\0132&.google"
    ".protobuf.testing.StructMessage\022J\n\031struc"
    "t_with_list_of_nulls\030\317\001 \001(\0132&.google.pro"
    "tobuf.testing.StructMessage\022J\n\031struct_wi"
    "th_list_of_lists\030\320\001 \001(\0132&.google.protobu"
    "f.testing.StructMessage\022L\n\033struct_with_l"
    "ist_of_structs\030\321\001 \001(\0132&.google.protobuf."
    "testing.StructMessage\0222\n\020top_level_struc"
    "t\030\322\001 \001(\0132\027.google.protobuf.Struct\022D\n\024val"
    "ue_wrapper_simple\030\324\001 \001(\0132%.google.protob"
    "uf.testing.ValueMessage\022I\n\031value_wrapper"
    "_with_struct\030\325\001 \001(\0132%.google.protobuf.te"
    "sting.ValueMessage\022G\n\027value_wrapper_with"
    "_list\030\326\001 \001(\0132%.google.protobuf.testing.V"
    "alueMessage\022F\n\022list_value_wrapper\030\327\001 \001(\013"
    "2).google.protobuf.testing.ListValueMess"
    "age\0227\n\026top_level_value_simple\030\330\001 \001(\0132\026.g"
    "oogle.protobuf.Value\022<\n\033top_level_value_"
    "with_struct\030\331\001 \001(\0132\026.google.protobuf.Val"
    "ue\022:\n\031top_level_value_with_list\030\332\001 \001(\0132\026"
    ".google.protobuf.Value\0228\n\023top_level_list"
    "value\030\333\001 \001(\0132\032.google.protobuf.ListValue"
    "\0227\n\tempty_any\030\255\002 \001(\0132#.google.protobuf.t"
    "esting.AnyMessage\022;\n\rtype_only_any\030\256\002 \001("
    "\0132#.google.protobuf.testing.AnyMessage\022;"
    "\n\rrecursive_any\030\257\002 \001(\0132#.google.protobuf"
    ".testing.AnyMessage\022D\n\026any_with_message_"
    "value\030\260\002 \001(\0132#.google.protobuf.testing.A"
    "nyMessage\022E\n\027any_with_nested_message\030\261\002 "
    "\001(\0132#.google.protobuf.testing.AnyMessage"
    "\022M\n\037any_with_message_containing_map\030\262\002 \001"
    "(\0132#.google.protobuf.testing.AnyMessage\022"
    "P\n\"any_with_message_containing_struct\030\263\002"
    " \001(\0132#.google.protobuf.testing.AnyMessag"
    "e\022,\n\rtop_level_any\030\264\002 \001(\0132\024.google.proto"
    "buf.Any\022;\n\tempty_map\030\221\003 \001(\0132\'.google.pro"
    "tobuf.testing.StringtoIntMap\022\?\n\rstring_t"
    "o_int\030\222\003 \001(\0132\'.google.protobuf.testing.S"
    "tringtoIntMap\022\?\n\rint_to_string\030\223\003 \001(\0132\'."
    "google.protobuf.testing.IntToStringMap\0222"
    "\n\006mixed1\030\224\003 \001(\0132!.google.protobuf.testin"
    "g.MixedMap\0223\n\006mixed2\030\225\003 \001(\0132\".google.pro"
    "tobuf.testing.MixedMap2\0229\n\014empty_mixed2\030"
    "\226\003 \001(\0132\".google.protobuf.testing.MixedMa"
    "p2\022<\n\016map_of_objects\030\227\003 \001(\0132#.google.pro"
    "tobuf.testing.MessageMap\0227\n\013mixed_empty\030"
    "\230\003 \001(\0132!.google.protobuf.testing.MixedMa"
    "p\022\?\n\021message_map_empty\030\231\003 \001(\0132#.google.p"
    "rotobuf.testing.MessageMap\022B\n\014double_val"
    "ue\030\365\003 \001(\0132+.google.protobuf.testing.Doub"
    "leValueMessage\022J\n\024double_value_default\030\366"
    "\003 \001(\0132+.google.protobuf.testing.DoubleVa"
    "lueMessage\"\260\002\n\rDoubleMessage\022\024\n\014double_v"
    "alue\030\001 \001(\001\022\027\n\017repeated_double\030\002 \003(\001\022>\n\016n"
    "ested_message\030\003 \001(\0132&.google.protobuf.te"
    "sting.DoubleMessage\022G\n\027repeated_nested_m"
    "essage\030\004 \003(\0132&.google.protobuf.testing.D"
    "oubleMessage\0224\n\016double_wrapper\030d \001(\0132\034.g"
    "oogle.protobuf.DoubleValue\022\023\n\tstr_value\030"
    "p \001(\tH\000\022\023\n\tnum_value\030q \001(\003H\000B\007\n\005value\"8\n"
    "\rStructMessage\022\'\n\006struct\030\001 \001(\0132\027.google."
    "protobuf.Struct\"5\n\014ValueMessage\022%\n\005value"
    "\030\001 \001(\0132\026.google.protobuf.Value\"E\n\020ListVa"
    "lueMessage\0221\n\rshopping_list\030\001 \001(\0132\032.goog"
    "le.protobuf.ListValue\"!\n\016RequestMessage\022"
    "\017\n\007content\030\001 \001(\t\"_\n\nAnyMessage\022!\n\003any\030\001 "
    "\001(\0132\024.google.protobuf.Any\022.\n\004data\030\002 \001(\0132"
    " .google.protobuf.testing.AnyData\"\301\002\n\007An"
    "yData\022\014\n\004attr\030\001 \001(\005\022\013\n\003str\030\002 \001(\t\022\014\n\004msgs"
    "\030\003 \003(\t\0225\n\013nested_data\030\004 \001(\0132 .google.pro"
    "tobuf.testing.AnyData\022\?\n\010map_data\030\007 \003(\0132"
    "-.google.protobuf.testing.AnyData.MapDat"
    "aEntry\022,\n\013struct_data\030\010 \001(\0132\027.google.pro"
    "tobuf.Struct\0227\n\rrepeated_data\030\t \003(\0132 .go"
    "ogle.protobuf.testing.AnyData\032.\n\014MapData"
    "Entry\022\013\n\003key\030\001 \001(\t\022\r\n\005value\030\002 \001(\t:\0028\001\"{\n"
    "\016StringtoIntMap\022=\n\003map\030\001 \003(\01320.google.pr"
    "otobuf.testing.StringtoIntMap.MapEntry\032*"
    "\n\010MapEntry\022\013\n\003key\030\001 \001(\t\022\r\n\005value\030\002 \001(\005:\002"
    "8\001\"{\n\016IntToStringMap\022=\n\003map\030\001 \003(\01320.goog"
    "le.protobuf.testing.IntToStringMap.MapEn"
    "try\032*\n\010MapEntry\022\013\n\003key\030\001 \001(\005\022\r\n\005value\030\002 "
    "\001(\t:\0028\001\"\217\001\n\010MixedMap\022\013\n\003msg\030\001 \001(\t\0227\n\003map"
    "\030\002 \003(\0132*.google.protobuf.testing.MixedMa"
    "p.MapEntry\022\021\n\tint_value\030\003 \001(\005\032*\n\010MapEntr"
    "y\022\013\n\003key\030\001 \001(\t\022\r\n\005value\030\002 \001(\002:\0028\001\"\325\001\n\tMi"
    "xedMap2\0228\n\003map\030\001 \003(\0132+.google.protobuf.t"
    "esting.MixedMap2.MapEntry\0220\n\002ee\030\002 \001(\0162$."
    "google.protobuf.testing.MixedMap2.E\022\013\n\003m"
    "sg\030\004 \001(\t\032*\n\010MapEntry\022\013\n\003key\030\001 \001(\005\022\r\n\005val"
    "ue\030\002 \001(\010:\0028\001\"#\n\001E\022\006\n\002E0\020\000\022\006\n\002E1\020\001\022\006\n\002E2\020"
    "\002\022\006\n\002E3\020\003\"\306\001\n\nMessageMap\0229\n\003map\030\001 \003(\0132,."
    "google.protobuf.testing.MessageMap.MapEn"
    "try\032*\n\001M\022\021\n\tinner_int\030\001 \001(\005\022\022\n\ninner_tex"
    "t\030\002 \001(\t\032Q\n\010MapEntry\022\013\n\003key\030\001 \001(\t\0224\n\005valu"
    "e\030\002 \001(\0132%.google.protobuf.testing.Messag"
    "eMap.M:\0028\001\"B\n\022DoubleValueMessage\022,\n\006doub"
    "le\030\001 \001(\0132\034.google.protobuf.DoubleValue2z"
    "\n\027DefaultValueTestService\022_\n\004Call\022\'.goog"
    "le.protobuf.testing.RequestMessage\032..goo"
    "gle.protobuf.testing.DefaultValueTestCas"
    "esb\006proto3", 5090);
  ::google::protobuf::MessageFactory::InternalRegisterGeneratedFile(
    "google/protobuf/util/internal/testdata/default_value.proto", &protobuf_RegisterTypes);
  ::google::protobuf::protobuf_AddDesc_google_2fprotobuf_2fany_2eproto();
  ::google::protobuf::protobuf_AddDesc_google_2fprotobuf_2fstruct_2eproto();
  ::google::protobuf::protobuf_AddDesc_google_2fprotobuf_2fwrappers_2eproto();
  ::google::protobuf::internal::OnShutdown(&protobuf_ShutdownFile_google_2fprotobuf_2futil_2finternal_2ftestdata_2fdefault_5fvalue_2eproto);
}

GOOGLE_PROTOBUF_DECLARE_ONCE(protobuf_AddDesc_google_2fprotobuf_2futil_2finternal_2ftestdata_2fdefault_5fvalue_2eproto_once_);
void protobuf_AddDesc_google_2fprotobuf_2futil_2finternal_2ftestdata_2fdefault_5fvalue_2eproto() {
  ::google::protobuf::GoogleOnceInit(&protobuf_AddDesc_google_2fprotobuf_2futil_2finternal_2ftestdata_2fdefault_5fvalue_2eproto_once_,
                 &protobuf_AddDesc_google_2fprotobuf_2futil_2finternal_2ftestdata_2fdefault_5fvalue_2eproto_impl);
}
// Force AddDescriptors() to be called at static initialization time.
struct StaticDescriptorInitializer_google_2fprotobuf_2futil_2finternal_2ftestdata_2fdefault_5fvalue_2eproto {
  StaticDescriptorInitializer_google_2fprotobuf_2futil_2finternal_2ftestdata_2fdefault_5fvalue_2eproto() {
    protobuf_AddDesc_google_2fprotobuf_2futil_2finternal_2ftestdata_2fdefault_5fvalue_2eproto();
  }
} static_descriptor_initializer_google_2fprotobuf_2futil_2finternal_2ftestdata_2fdefault_5fvalue_2eproto_;

namespace {

static void MergeFromFail(int line) GOOGLE_ATTRIBUTE_COLD GOOGLE_ATTRIBUTE_NORETURN;
static void MergeFromFail(int line) {
  ::google::protobuf::internal::MergeFromFail(__FILE__, line);
}

}  // namespace


// ===================================================================

#if !defined(_MSC_VER) || _MSC_VER >= 1900
const int DefaultValueTestCases::kEmptyDoubleFieldNumber;
const int DefaultValueTestCases::kDoubleWithDefaultValueFieldNumber;
const int DefaultValueTestCases::kDoubleWithNondefaultValueFieldNumber;
const int DefaultValueTestCases::kRepeatedDoubleFieldNumber;
const int DefaultValueTestCases::kNestedMessageFieldNumber;
const int DefaultValueTestCases::kRepeatedNestedMessageFieldNumber;
const int DefaultValueTestCases::kDoubleMessageWithOneofFieldNumber;
const int DefaultValueTestCases::kEmptyStructFieldNumber;
const int DefaultValueTestCases::kEmptyStruct2FieldNumber;
const int DefaultValueTestCases::kStructWithNullValueFieldNumber;
const int DefaultValueTestCases::kStructWithValuesFieldNumber;
const int DefaultValueTestCases::kStructWithNestedStructFieldNumber;
const int DefaultValueTestCases::kStructWithNestedListFieldNumber;
const int DefaultValueTestCases::kStructWithListOfNullsFieldNumber;
const int DefaultValueTestCases::kStructWithListOfListsFieldNumber;
const int DefaultValueTestCases::kStructWithListOfStructsFieldNumber;
const int DefaultValueTestCases::kTopLevelStructFieldNumber;
const int DefaultValueTestCases::kValueWrapperSimpleFieldNumber;
const int DefaultValueTestCases::kValueWrapperWithStructFieldNumber;
const int DefaultValueTestCases::kValueWrapperWithListFieldNumber;
const int DefaultValueTestCases::kListValueWrapperFieldNumber;
const int DefaultValueTestCases::kTopLevelValueSimpleFieldNumber;
const int DefaultValueTestCases::kTopLevelValueWithStructFieldNumber;
const int DefaultValueTestCases::kTopLevelValueWithListFieldNumber;
const int DefaultValueTestCases::kTopLevelListvalueFieldNumber;
const int DefaultValueTestCases::kEmptyAnyFieldNumber;
const int DefaultValueTestCases::kTypeOnlyAnyFieldNumber;
const int DefaultValueTestCases::kRecursiveAnyFieldNumber;
const int DefaultValueTestCases::kAnyWithMessageValueFieldNumber;
const int DefaultValueTestCases::kAnyWithNestedMessageFieldNumber;
const int DefaultValueTestCases::kAnyWithMessageContainingMapFieldNumber;
const int DefaultValueTestCases::kAnyWithMessageContainingStructFieldNumber;
const int DefaultValueTestCases::kTopLevelAnyFieldNumber;
const int DefaultValueTestCases::kEmptyMapFieldNumber;
const int DefaultValueTestCases::kStringToIntFieldNumber;
const int DefaultValueTestCases::kIntToStringFieldNumber;
const int DefaultValueTestCases::kMixed1FieldNumber;
const int DefaultValueTestCases::kMixed2FieldNumber;
const int DefaultValueTestCases::kEmptyMixed2FieldNumber;
const int DefaultValueTestCases::kMapOfObjectsFieldNumber;
const int DefaultValueTestCases::kMixedEmptyFieldNumber;
const int DefaultValueTestCases::kMessageMapEmptyFieldNumber;
const int DefaultValueTestCases::kDoubleValueFieldNumber;
const int DefaultValueTestCases::kDoubleValueDefaultFieldNumber;
#endif  // !defined(_MSC_VER) || _MSC_VER >= 1900

DefaultValueTestCases::DefaultValueTestCases()
  : ::google::protobuf::Message(), _internal_metadata_(NULL) {
  if (this != internal_default_instance()) protobuf_InitDefaults_google_2fprotobuf_2futil_2finternal_2ftestdata_2fdefault_5fvalue_2eproto();
  SharedCtor();
  // @@protoc_insertion_point(constructor:google.protobuf.testing.DefaultValueTestCases)
}

void DefaultValueTestCases::InitAsDefaultInstance() {
  empty_double_ = const_cast< ::google::protobuf::testing::DoubleMessage*>(
      ::google::protobuf::testing::DoubleMessage::internal_default_instance());
  double_with_default_value_ = const_cast< ::google::protobuf::testing::DoubleMessage*>(
      ::google::protobuf::testing::DoubleMessage::internal_default_instance());
  double_with_nondefault_value_ = const_cast< ::google::protobuf::testing::DoubleMessage*>(
      ::google::protobuf::testing::DoubleMessage::internal_default_instance());
  repeated_double_ = const_cast< ::google::protobuf::testing::DoubleMessage*>(
      ::google::protobuf::testing::DoubleMessage::internal_default_instance());
  nested_message_ = const_cast< ::google::protobuf::testing::DoubleMessage*>(
      ::google::protobuf::testing::DoubleMessage::internal_default_instance());
  repeated_nested_message_ = const_cast< ::google::protobuf::testing::DoubleMessage*>(
      ::google::protobuf::testing::DoubleMessage::internal_default_instance());
  double_message_with_oneof_ = const_cast< ::google::protobuf::testing::DoubleMessage*>(
      ::google::protobuf::testing::DoubleMessage::internal_default_instance());
  empty_struct_ = const_cast< ::google::protobuf::testing::StructMessage*>(
      ::google::protobuf::testing::StructMessage::internal_default_instance());
  empty_struct2_ = const_cast< ::google::protobuf::testing::StructMessage*>(
      ::google::protobuf::testing::StructMessage::internal_default_instance());
  struct_with_null_value_ = const_cast< ::google::protobuf::testing::StructMessage*>(
      ::google::protobuf::testing::StructMessage::internal_default_instance());
  struct_with_values_ = const_cast< ::google::protobuf::testing::StructMessage*>(
      ::google::protobuf::testing::StructMessage::internal_default_instance());
  struct_with_nested_struct_ = const_cast< ::google::protobuf::testing::StructMessage*>(
      ::google::protobuf::testing::StructMessage::internal_default_instance());
  struct_with_nested_list_ = const_cast< ::google::protobuf::testing::StructMessage*>(
      ::google::protobuf::testing::StructMessage::internal_default_instance());
  struct_with_list_of_nulls_ = const_cast< ::google::protobuf::testing::StructMessage*>(
      ::google::protobuf::testing::StructMessage::internal_default_instance());
  struct_with_list_of_lists_ = const_cast< ::google::protobuf::testing::StructMessage*>(
      ::google::protobuf::testing::StructMessage::internal_default_instance());
  struct_with_list_of_structs_ = const_cast< ::google::protobuf::testing::StructMessage*>(
      ::google::protobuf::testing::StructMessage::internal_default_instance());
  top_level_struct_ = const_cast< ::google::protobuf::Struct*>(
      ::google::protobuf::Struct::internal_default_instance());
  value_wrapper_simple_ = const_cast< ::google::protobuf::testing::ValueMessage*>(
      ::google::protobuf::testing::ValueMessage::internal_default_instance());
  value_wrapper_with_struct_ = const_cast< ::google::protobuf::testing::ValueMessage*>(
      ::google::protobuf::testing::ValueMessage::internal_default_instance());
  value_wrapper_with_list_ = const_cast< ::google::protobuf::testing::ValueMessage*>(
      ::google::protobuf::testing::ValueMessage::internal_default_instance());
  list_value_wrapper_ = const_cast< ::google::protobuf::testing::ListValueMessage*>(
      ::google::protobuf::testing::ListValueMessage::internal_default_instance());
  top_level_value_simple_ = const_cast< ::google::protobuf::Value*>(
      ::google::protobuf::Value::internal_default_instance());
  top_level_value_with_struct_ = const_cast< ::google::protobuf::Value*>(
      ::google::protobuf::Value::internal_default_instance());
  top_level_value_with_list_ = const_cast< ::google::protobuf::Value*>(
      ::google::protobuf::Value::internal_default_instance());
  top_level_listvalue_ = const_cast< ::google::protobuf::ListValue*>(
      ::google::protobuf::ListValue::internal_default_instance());
  empty_any_ = const_cast< ::google::protobuf::testing::AnyMessage*>(
      ::google::protobuf::testing::AnyMessage::internal_default_instance());
  type_only_any_ = const_cast< ::google::protobuf::testing::AnyMessage*>(
      ::google::protobuf::testing::AnyMessage::internal_default_instance());
  recursive_any_ = const_cast< ::google::protobuf::testing::AnyMessage*>(
      ::google::protobuf::testing::AnyMessage::internal_default_instance());
  any_with_message_value_ = const_cast< ::google::protobuf::testing::AnyMessage*>(
      ::google::protobuf::testing::AnyMessage::internal_default_instance());
  any_with_nested_message_ = const_cast< ::google::protobuf::testing::AnyMessage*>(
      ::google::protobuf::testing::AnyMessage::internal_default_instance());
  any_with_message_containing_map_ = const_cast< ::google::protobuf::testing::AnyMessage*>(
      ::google::protobuf::testing::AnyMessage::internal_default_instance());
  any_with_message_containing_struct_ = const_cast< ::google::protobuf::testing::AnyMessage*>(
      ::google::protobuf::testing::AnyMessage::internal_default_instance());
  top_level_any_ = const_cast< ::google::protobuf::Any*>(
      ::google::protobuf::Any::internal_default_instance());
  empty_map_ = const_cast< ::google::protobuf::testing::StringtoIntMap*>(
      ::google::protobuf::testing::StringtoIntMap::internal_default_instance());
  string_to_int_ = const_cast< ::google::protobuf::testing::StringtoIntMap*>(
      ::google::protobuf::testing::StringtoIntMap::internal_default_instance());
  int_to_string_ = const_cast< ::google::protobuf::testing::IntToStringMap*>(
      ::google::protobuf::testing::IntToStringMap::internal_default_instance());
  mixed1_ = const_cast< ::google::protobuf::testing::MixedMap*>(
      ::google::protobuf::testing::MixedMap::internal_default_instance());
  mixed2_ = const_cast< ::google::protobuf::testing::MixedMap2*>(
      ::google::protobuf::testing::MixedMap2::internal_default_instance());
  empty_mixed2_ = const_cast< ::google::protobuf::testing::MixedMap2*>(
      ::google::protobuf::testing::MixedMap2::internal_default_instance());
  map_of_objects_ = const_cast< ::google::protobuf::testing::MessageMap*>(
      ::google::protobuf::testing::MessageMap::internal_default_instance());
  mixed_empty_ = const_cast< ::google::protobuf::testing::MixedMap*>(
      ::google::protobuf::testing::MixedMap::internal_default_instance());
  message_map_empty_ = const_cast< ::google::protobuf::testing::MessageMap*>(
      ::google::protobuf::testing::MessageMap::internal_default_instance());
  double_value_ = const_cast< ::google::protobuf::testing::DoubleValueMessage*>(
      ::google::protobuf::testing::DoubleValueMessage::internal_default_instance());
  double_value_default_ = const_cast< ::google::protobuf::testing::DoubleValueMessage*>(
      ::google::protobuf::testing::DoubleValueMessage::internal_default_instance());
}

DefaultValueTestCases::DefaultValueTestCases(const DefaultValueTestCases& from)
  : ::google::protobuf::Message(),
    _internal_metadata_(NULL) {
  SharedCtor();
  UnsafeMergeFrom(from);
  // @@protoc_insertion_point(copy_constructor:google.protobuf.testing.DefaultValueTestCases)
}

void DefaultValueTestCases::SharedCtor() {
  empty_double_ = NULL;
  double_with_default_value_ = NULL;
  double_with_nondefault_value_ = NULL;
  repeated_double_ = NULL;
  nested_message_ = NULL;
  repeated_nested_message_ = NULL;
  double_message_with_oneof_ = NULL;
  empty_struct_ = NULL;
  empty_struct2_ = NULL;
  struct_with_null_value_ = NULL;
  struct_with_values_ = NULL;
  struct_with_nested_struct_ = NULL;
  struct_with_nested_list_ = NULL;
  struct_with_list_of_nulls_ = NULL;
  struct_with_list_of_lists_ = NULL;
  struct_with_list_of_structs_ = NULL;
  top_level_struct_ = NULL;
  value_wrapper_simple_ = NULL;
  value_wrapper_with_struct_ = NULL;
  value_wrapper_with_list_ = NULL;
  list_value_wrapper_ = NULL;
  top_level_value_simple_ = NULL;
  top_level_value_with_struct_ = NULL;
  top_level_value_with_list_ = NULL;
  top_level_listvalue_ = NULL;
  empty_any_ = NULL;
  type_only_any_ = NULL;
  recursive_any_ = NULL;
  any_with_message_value_ = NULL;
  any_with_nested_message_ = NULL;
  any_with_message_containing_map_ = NULL;
  any_with_message_containing_struct_ = NULL;
  top_level_any_ = NULL;
  empty_map_ = NULL;
  string_to_int_ = NULL;
  int_to_string_ = NULL;
  mixed1_ = NULL;
  mixed2_ = NULL;
  empty_mixed2_ = NULL;
  map_of_objects_ = NULL;
  mixed_empty_ = NULL;
  message_map_empty_ = NULL;
  double_value_ = NULL;
  double_value_default_ = NULL;
  _cached_size_ = 0;
}

DefaultValueTestCases::~DefaultValueTestCases() {
  // @@protoc_insertion_point(destructor:google.protobuf.testing.DefaultValueTestCases)
  SharedDtor();
}

void DefaultValueTestCases::SharedDtor() {
  if (this != &DefaultValueTestCases_default_instance_.get()) {
    delete empty_double_;
    delete double_with_default_value_;
    delete double_with_nondefault_value_;
    delete repeated_double_;
    delete nested_message_;
    delete repeated_nested_message_;
    delete double_message_with_oneof_;
    delete empty_struct_;
    delete empty_struct2_;
    delete struct_with_null_value_;
    delete struct_with_values_;
    delete struct_with_nested_struct_;
    delete struct_with_nested_list_;
    delete struct_with_list_of_nulls_;
    delete struct_with_list_of_lists_;
    delete struct_with_list_of_structs_;
    delete top_level_struct_;
    delete value_wrapper_simple_;
    delete value_wrapper_with_struct_;
    delete value_wrapper_with_list_;
    delete list_value_wrapper_;
    delete top_level_value_simple_;
    delete top_level_value_with_struct_;
    delete top_level_value_with_list_;
    delete top_level_listvalue_;
    delete empty_any_;
    delete type_only_any_;
    delete recursive_any_;
    delete any_with_message_value_;
    delete any_with_nested_message_;
    delete any_with_message_containing_map_;
    delete any_with_message_containing_struct_;
    delete top_level_any_;
    delete empty_map_;
    delete string_to_int_;
    delete int_to_string_;
    delete mixed1_;
    delete mixed2_;
    delete empty_mixed2_;
    delete map_of_objects_;
    delete mixed_empty_;
    delete message_map_empty_;
    delete double_value_;
    delete double_value_default_;
  }
}

void DefaultValueTestCases::SetCachedSize(int size) const {
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
}
const ::google::protobuf::Descriptor* DefaultValueTestCases::descriptor() {
  protobuf_AssignDescriptorsOnce();
  return DefaultValueTestCases_descriptor_;
}

const DefaultValueTestCases& DefaultValueTestCases::default_instance() {
  protobuf_InitDefaults_google_2fprotobuf_2futil_2finternal_2ftestdata_2fdefault_5fvalue_2eproto();
  return *internal_default_instance();
}

::google::protobuf::internal::ExplicitlyConstructed<DefaultValueTestCases> DefaultValueTestCases_default_instance_;

DefaultValueTestCases* DefaultValueTestCases::New(::google::protobuf::Arena* arena) const {
  DefaultValueTestCases* n = new DefaultValueTestCases;
  if (arena != NULL) {
    arena->Own(n);
  }
  return n;
}

void DefaultValueTestCases::Clear() {
// @@protoc_insertion_point(message_clear_start:google.protobuf.testing.DefaultValueTestCases)
  if (GetArenaNoVirtual() == NULL && empty_double_ != NULL) delete empty_double_;
  empty_double_ = NULL;
  if (GetArenaNoVirtual() == NULL && double_with_default_value_ != NULL) delete double_with_default_value_;
  double_with_default_value_ = NULL;
  if (GetArenaNoVirtual() == NULL && double_with_nondefault_value_ != NULL) delete double_with_nondefault_value_;
  double_with_nondefault_value_ = NULL;
  if (GetArenaNoVirtual() == NULL && repeated_double_ != NULL) delete repeated_double_;
  repeated_double_ = NULL;
  if (GetArenaNoVirtual() == NULL && nested_message_ != NULL) delete nested_message_;
  nested_message_ = NULL;
  if (GetArenaNoVirtual() == NULL && repeated_nested_message_ != NULL) delete repeated_nested_message_;
  repeated_nested_message_ = NULL;
  if (GetArenaNoVirtual() == NULL && double_message_with_oneof_ != NULL) delete double_message_with_oneof_;
  double_message_with_oneof_ = NULL;
  if (GetArenaNoVirtual() == NULL && empty_struct_ != NULL) delete empty_struct_;
  empty_struct_ = NULL;
  if (GetArenaNoVirtual() == NULL && empty_struct2_ != NULL) delete empty_struct2_;
  empty_struct2_ = NULL;
  if (GetArenaNoVirtual() == NULL && struct_with_null_value_ != NULL) delete struct_with_null_value_;
  struct_with_null_value_ = NULL;
  if (GetArenaNoVirtual() == NULL && struct_with_values_ != NULL) delete struct_with_values_;
  struct_with_values_ = NULL;
  if (GetArenaNoVirtual() == NULL && struct_with_nested_struct_ != NULL) delete struct_with_nested_struct_;
  struct_with_nested_struct_ = NULL;
  if (GetArenaNoVirtual() == NULL && struct_with_nested_list_ != NULL) delete struct_with_nested_list_;
  struct_with_nested_list_ = NULL;
  if (GetArenaNoVirtual() == NULL && struct_with_list_of_nulls_ != NULL) delete struct_with_list_of_nulls_;
  struct_with_list_of_nulls_ = NULL;
  if (GetArenaNoVirtual() == NULL && struct_with_list_of_lists_ != NULL) delete struct_with_list_of_lists_;
  struct_with_list_of_lists_ = NULL;
  if (GetArenaNoVirtual() == NULL && struct_with_list_of_structs_ != NULL) delete struct_with_list_of_structs_;
  struct_with_list_of_structs_ = NULL;
  if (GetArenaNoVirtual() == NULL && top_level_struct_ != NULL) delete top_level_struct_;
  top_level_struct_ = NULL;
  if (GetArenaNoVirtual() == NULL && value_wrapper_simple_ != NULL) delete value_wrapper_simple_;
  value_wrapper_simple_ = NULL;
  if (GetArenaNoVirtual() == NULL && value_wrapper_with_struct_ != NULL) delete value_wrapper_with_struct_;
  value_wrapper_with_struct_ = NULL;
  if (GetArenaNoVirtual() == NULL && value_wrapper_with_list_ != NULL) delete value_wrapper_with_list_;
  value_wrapper_with_list_ = NULL;
  if (GetArenaNoVirtual() == NULL && list_value_wrapper_ != NULL) delete list_value_wrapper_;
  list_value_wrapper_ = NULL;
  if (GetArenaNoVirtual() == NULL && top_level_value_simple_ != NULL) delete top_level_value_simple_;
  top_level_value_simple_ = NULL;
  if (GetArenaNoVirtual() == NULL && top_level_value_with_struct_ != NULL) delete top_level_value_with_struct_;
  top_level_value_with_struct_ = NULL;
  if (GetArenaNoVirtual() == NULL && top_level_value_with_list_ != NULL) delete top_level_value_with_list_;
  top_level_value_with_list_ = NULL;
  if (GetArenaNoVirtual() == NULL && top_level_listvalue_ != NULL) delete top_level_listvalue_;
  top_level_listvalue_ = NULL;
  if (GetArenaNoVirtual() == NULL && empty_any_ != NULL) delete empty_any_;
  empty_any_ = NULL;
  if (GetArenaNoVirtual() == NULL && type_only_any_ != NULL) delete type_only_any_;
  type_only_any_ = NULL;
  if (GetArenaNoVirtual() == NULL && recursive_any_ != NULL) delete recursive_any_;
  recursive_any_ = NULL;
  if (GetArenaNoVirtual() == NULL && any_with_message_value_ != NULL) delete any_with_message_value_;
  any_with_message_value_ = NULL;
  if (GetArenaNoVirtual() == NULL && any_with_nested_message_ != NULL) delete any_with_nested_message_;
  any_with_nested_message_ = NULL;
  if (GetArenaNoVirtual() == NULL && any_with_message_containing_map_ != NULL) delete any_with_message_containing_map_;
  any_with_message_containing_map_ = NULL;
  if (GetArenaNoVirtual() == NULL && any_with_message_containing_struct_ != NULL) delete any_with_message_containing_struct_;
  any_with_message_containing_struct_ = NULL;
  if (GetArenaNoVirtual() == NULL && top_level_any_ != NULL) delete top_level_any_;
  top_level_any_ = NULL;
  if (GetArenaNoVirtual() == NULL && empty_map_ != NULL) delete empty_map_;
  empty_map_ = NULL;
  if (GetArenaNoVirtual() == NULL && string_to_int_ != NULL) delete string_to_int_;
  string_to_int_ = NULL;
  if (GetArenaNoVirtual() == NULL && int_to_string_ != NULL) delete int_to_string_;
  int_to_string_ = NULL;
  if (GetArenaNoVirtual() == NULL && mixed1_ != NULL) delete mixed1_;
  mixed1_ = NULL;
  if (GetArenaNoVirtual() == NULL && mixed2_ != NULL) delete mixed2_;
  mixed2_ = NULL;
  if (GetArenaNoVirtual() == NULL && empty_mixed2_ != NULL) delete empty_mixed2_;
  empty_mixed2_ = NULL;
  if (GetArenaNoVirtual() == NULL && map_of_objects_ != NULL) delete map_of_objects_;
  map_of_objects_ = NULL;
  if (GetArenaNoVirtual() == NULL && mixed_empty_ != NULL) delete mixed_empty_;
  mixed_empty_ = NULL;
  if (GetArenaNoVirtual() == NULL && message_map_empty_ != NULL) delete message_map_empty_;
  message_map_empty_ = NULL;
  if (GetArenaNoVirtual() == NULL && double_value_ != NULL) delete double_value_;
  double_value_ = NULL;
  if (GetArenaNoVirtual() == NULL && double_value_default_ != NULL) delete double_value_default_;
  double_value_default_ = NULL;
}

bool DefaultValueTestCases::MergePartialFromCodedStream(
    ::google::protobuf::io::CodedInputStream* input) {
#define DO_(EXPRESSION) if (!GOOGLE_PREDICT_TRUE(EXPRESSION)) goto failure
  ::google::protobuf::uint32 tag;
  // @@protoc_insertion_point(parse_start:google.protobuf.testing.DefaultValueTestCases)
  for (;;) {
    ::std::pair< ::google::protobuf::uint32, bool> p = input->ReadTagWithCutoff(16383);
    tag = p.first;
    if (!p.second) goto handle_unusual;
    switch (::google::protobuf::internal::WireFormatLite::GetTagFieldNumber(tag)) {
      // optional .google.protobuf.testing.DoubleMessage empty_double = 1;
      case 1: {
        if (tag == 10) {
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessageNoVirtual(
               input, mutable_empty_double()));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(18)) goto parse_double_with_default_value;
        break;
      }

      // optional .google.protobuf.testing.DoubleMessage double_with_default_value = 2;
      case 2: {
        if (tag == 18) {
         parse_double_with_default_value:
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessageNoVirtual(
               input, mutable_double_with_default_value()));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(26)) goto parse_double_with_nondefault_value;
        break;
      }

      // optional .google.protobuf.testing.DoubleMessage double_with_nondefault_value = 3;
      case 3: {
        if (tag == 26) {
         parse_double_with_nondefault_value:
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessageNoVirtual(
               input, mutable_double_with_nondefault_value()));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(34)) goto parse_repeated_double;
        break;
      }

      // optional .google.protobuf.testing.DoubleMessage repeated_double = 4;
      case 4: {
        if (tag == 34) {
         parse_repeated_double:
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessageNoVirtual(
               input, mutable_repeated_double()));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(42)) goto parse_nested_message;
        break;
      }

      // optional .google.protobuf.testing.DoubleMessage nested_message = 5;
      case 5: {
        if (tag == 42) {
         parse_nested_message:
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessageNoVirtual(
               input, mutable_nested_message()));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(50)) goto parse_repeated_nested_message;
        break;
      }

      // optional .google.protobuf.testing.DoubleMessage repeated_nested_message = 6;
      case 6: {
        if (tag == 50) {
         parse_repeated_nested_message:
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessageNoVirtual(
               input, mutable_repeated_nested_message()));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(58)) goto parse_double_message_with_oneof;
        break;
      }

      // optional .google.protobuf.testing.DoubleMessage double_message_with_oneof = 7;
      case 7: {
        if (tag == 58) {
         parse_double_message_with_oneof:
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessageNoVirtual(
               input, mutable_double_message_with_oneof()));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(1610)) goto parse_empty_struct;
        break;
      }

      // optional .google.protobuf.testing.StructMessage empty_struct = 201;
      case 201: {
        if (tag == 1610) {
         parse_empty_struct:
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessageNoVirtual(
               input, mutable_empty_struct()));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(1618)) goto parse_empty_struct2;
        break;
      }

      // optional .google.protobuf.testing.StructMessage empty_struct2 = 202;
      case 202: {
        if (tag == 1618) {
         parse_empty_struct2:
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessageNoVirtual(
               input, mutable_empty_struct2()));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(1626)) goto parse_struct_with_null_value;
        break;
      }

      // optional .google.protobuf.testing.StructMessage struct_with_null_value = 203;
      case 203: {
        if (tag == 1626) {
         parse_struct_with_null_value:
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessageNoVirtual(
               input, mutable_struct_with_null_value()));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(1634)) goto parse_struct_with_values;
        break;
      }

      // optional .google.protobuf.testing.StructMessage struct_with_values = 204;
      case 204: {
        if (tag == 1634) {
         parse_struct_with_values:
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessageNoVirtual(
               input, mutable_struct_with_values()));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(1642)) goto parse_struct_with_nested_struct;
        break;
      }

      // optional .google.protobuf.testing.StructMessage struct_with_nested_struct = 205;
      case 205: {
        if (tag == 1642) {
         parse_struct_with_nested_struct:
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessageNoVirtual(
               input, mutable_struct_with_nested_struct()));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(1650)) goto parse_struct_with_nested_list;
        break;
      }

      // optional .google.protobuf.testing.StructMessage struct_with_nested_list = 206;
      case 206: {
        if (tag == 1650) {
         parse_struct_with_nested_list:
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessageNoVirtual(
               input, mutable_struct_with_nested_list()));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(1658)) goto parse_struct_with_list_of_nulls;
        break;
      }

      // optional .google.protobuf.testing.StructMessage struct_with_list_of_nulls = 207;
      case 207: {
        if (tag == 1658) {
         parse_struct_with_list_of_nulls:
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessageNoVirtual(
               input, mutable_struct_with_list_of_nulls()));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(1666)) goto parse_struct_with_list_of_lists;
        break;
      }

      // optional .google.protobuf.testing.StructMessage struct_with_list_of_lists = 208;
      case 208: {
        if (tag == 1666) {
         parse_struct_with_list_of_lists:
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessageNoVirtual(
               input, mutable_struct_with_list_of_lists()));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(1674)) goto parse_struct_with_list_of_structs;
        break;
      }

      // optional .google.protobuf.testing.StructMessage struct_with_list_of_structs = 209;
      case 209: {
        if (tag == 1674) {
         parse_struct_with_list_of_structs:
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessageNoVirtual(
               input, mutable_struct_with_list_of_structs()));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(1682)) goto parse_top_level_struct;
        break;
      }

      // optional .google.protobuf.Struct top_level_struct = 210;
      case 210: {
        if (tag == 1682) {
         parse_top_level_struct:
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessageNoVirtual(
               input, mutable_top_level_struct()));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(1698)) goto parse_value_wrapper_simple;
        break;
      }

      // optional .google.protobuf.testing.ValueMessage value_wrapper_simple = 212;
      case 212: {
        if (tag == 1698) {
         parse_value_wrapper_simple:
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessageNoVirtual(
               input, mutable_value_wrapper_simple()));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(1706)) goto parse_value_wrapper_with_struct;
        break;
      }

      // optional .google.protobuf.testing.ValueMessage value_wrapper_with_struct = 213;
      case 213: {
        if (tag == 1706) {
         parse_value_wrapper_with_struct:
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessageNoVirtual(
               input, mutable_value_wrapper_with_struct()));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(1714)) goto parse_value_wrapper_with_list;
        break;
      }

      // optional .google.protobuf.testing.ValueMessage value_wrapper_with_list = 214;
      case 214: {
        if (tag == 1714) {
         parse_value_wrapper_with_list:
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessageNoVirtual(
               input, mutable_value_wrapper_with_list()));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(1722)) goto parse_list_value_wrapper;
        break;
      }

      // optional .google.protobuf.testing.ListValueMessage list_value_wrapper = 215;
      case 215: {
        if (tag == 1722) {
         parse_list_value_wrapper:
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessageNoVirtual(
               input, mutable_list_value_wrapper()));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(1730)) goto parse_top_level_value_simple;
        break;
      }

      // optional .google.protobuf.Value top_level_value_simple = 216;
      case 216: {
        if (tag == 1730) {
         parse_top_level_value_simple:
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessageNoVirtual(
               input, mutable_top_level_value_simple()));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(1738)) goto parse_top_level_value_with_struct;
        break;
      }

      // optional .google.protobuf.Value top_level_value_with_struct = 217;
      case 217: {
        if (tag == 1738) {
         parse_top_level_value_with_struct:
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessageNoVirtual(
               input, mutable_top_level_value_with_struct()));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(1746)) goto parse_top_level_value_with_list;
        break;
      }

      // optional .google.protobuf.Value top_level_value_with_list = 218;
      case 218: {
        if (tag == 1746) {
         parse_top_level_value_with_list:
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessageNoVirtual(
               input, mutable_top_level_value_with_list()));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(1754)) goto parse_top_level_listvalue;
        break;
      }

      // optional .google.protobuf.ListValue top_level_listvalue = 219;
      case 219: {
        if (tag == 1754) {
         parse_top_level_listvalue:
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessageNoVirtual(
               input, mutable_top_level_listvalue()));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(2410)) goto parse_empty_any;
        break;
      }

      // optional .google.protobuf.testing.AnyMessage empty_any = 301;
      case 301: {
        if (tag == 2410) {
         parse_empty_any:
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessageNoVirtual(
               input, mutable_empty_any()));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(2418)) goto parse_type_only_any;
        break;
      }

      // optional .google.protobuf.testing.AnyMessage type_only_any = 302;
      case 302: {
        if (tag == 2418) {
         parse_type_only_any:
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessageNoVirtual(
               input, mutable_type_only_any()));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(2426)) goto parse_recursive_any;
        break;
      }

      // optional .google.protobuf.testing.AnyMessage recursive_any = 303;
      case 303: {
        if (tag == 2426) {
         parse_recursive_any:
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessageNoVirtual(
               input, mutable_recursive_any()));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(2434)) goto parse_any_with_message_value;
        break;
      }

      // optional .google.protobuf.testing.AnyMessage any_with_message_value = 304;
      case 304: {
        if (tag == 2434) {
         parse_any_with_message_value:
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessageNoVirtual(
               input, mutable_any_with_message_value()));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(2442)) goto parse_any_with_nested_message;
        break;
      }

      // optional .google.protobuf.testing.AnyMessage any_with_nested_message = 305;
      case 305: {
        if (tag == 2442) {
         parse_any_with_nested_message:
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessageNoVirtual(
               input, mutable_any_with_nested_message()));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(2450)) goto parse_any_with_message_containing_map;
        break;
      }

      // optional .google.protobuf.testing.AnyMessage any_with_message_containing_map = 306;
      case 306: {
        if (tag == 2450) {
         parse_any_with_message_containing_map:
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessageNoVirtual(
               input, mutable_any_with_message_containing_map()));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(2458)) goto parse_any_with_message_containing_struct;
        break;
      }

      // optional .google.protobuf.testing.AnyMessage any_with_message_containing_struct = 307;
      case 307: {
        if (tag == 2458) {
         parse_any_with_message_containing_struct:
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessageNoVirtual(
               input, mutable_any_with_message_containing_struct()));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(2466)) goto parse_top_level_any;
        break;
      }

      // optional .google.protobuf.Any top_level_any = 308;
      case 308: {
        if (tag == 2466) {
         parse_top_level_any:
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessageNoVirtual(
               input, mutable_top_level_any()));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(3210)) goto parse_empty_map;
        break;
      }

      // optional .google.protobuf.testing.StringtoIntMap empty_map = 401;
      case 401: {
        if (tag == 3210) {
         parse_empty_map:
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessageNoVirtual(
               input, mutable_empty_map()));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(3218)) goto parse_string_to_int;
        break;
      }

      // optional .google.protobuf.testing.StringtoIntMap string_to_int = 402;
      case 402: {
        if (tag == 3218) {
         parse_string_to_int:
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessageNoVirtual(
               input, mutable_string_to_int()));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(3226)) goto parse_int_to_string;
        break;
      }

      // optional .google.protobuf.testing.IntToStringMap int_to_string = 403;
      case 403: {
        if (tag == 3226) {
         parse_int_to_string:
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessageNoVirtual(
               input, mutable_int_to_string()));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(3234)) goto parse_mixed1;
        break;
      }

      // optional .google.protobuf.testing.MixedMap mixed1 = 404;
      case 404: {
        if (tag == 3234) {
         parse_mixed1:
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessageNoVirtual(
               input, mutable_mixed1()));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(3242)) goto parse_mixed2;
        break;
      }

      // optional .google.protobuf.testing.MixedMap2 mixed2 = 405;
      case 405: {
        if (tag == 3242) {
         parse_mixed2:
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessageNoVirtual(
               input, mutable_mixed2()));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(3250)) goto parse_empty_mixed2;
        break;
      }

      // optional .google.protobuf.testing.MixedMap2 empty_mixed2 = 406;
      case 406: {
        if (tag == 3250) {
         parse_empty_mixed2:
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessageNoVirtual(
               input, mutable_empty_mixed2()));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(3258)) goto parse_map_of_objects;
        break;
      }

      // optional .google.protobuf.testing.MessageMap map_of_objects = 407;
      case 407: {
        if (tag == 3258) {
         parse_map_of_objects:
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessageNoVirtual(
               input, mutable_map_of_objects()));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(3266)) goto parse_mixed_empty;
        break;
      }

      // optional .google.protobuf.testing.MixedMap mixed_empty = 408;
      case 408: {
        if (tag == 3266) {
         parse_mixed_empty:
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessageNoVirtual(
               input, mutable_mixed_empty()));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(3274)) goto parse_message_map_empty;
        break;
      }

      // optional .google.protobuf.testing.MessageMap message_map_empty = 409;
      case 409: {
        if (tag == 3274) {
         parse_message_map_empty:
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessageNoVirtual(
               input, mutable_message_map_empty()));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(4010)) goto parse_double_value;
        break;
      }

      // optional .google.protobuf.testing.DoubleValueMessage double_value = 501;
      case 501: {
        if (tag == 4010) {
         parse_double_value:
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessageNoVirtual(
               input, mutable_double_value()));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(4018)) goto parse_double_value_default;
        break;
      }

      // optional .google.protobuf.testing.DoubleValueMessage double_value_default = 502;
      case 502: {
        if (tag == 4018) {
         parse_double_value_default:
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessageNoVirtual(
               input, mutable_double_value_default()));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectAtEnd()) goto success;
        break;
      }

      default: {
      handle_unusual:
        if (tag == 0 ||
            ::google::protobuf::internal::WireFormatLite::GetTagWireType(tag) ==
            ::google::protobuf::internal::WireFormatLite::WIRETYPE_END_GROUP) {
          goto success;
        }
        DO_(::google::protobuf::internal::WireFormatLite::SkipField(input, tag));
        break;
      }
    }
  }
success:
  // @@protoc_insertion_point(parse_success:google.protobuf.testing.DefaultValueTestCases)
  return true;
failure:
  // @@protoc_insertion_point(parse_failure:google.protobuf.testing.DefaultValueTestCases)
  return false;
#undef DO_
}

void DefaultValueTestCases::SerializeWithCachedSizes(
    ::google::protobuf::io::CodedOutputStream* output) const {
  // @@protoc_insertion_point(serialize_start:google.protobuf.testing.DefaultValueTestCases)
  // optional .google.protobuf.testing.DoubleMessage empty_double = 1;
  if (this->has_empty_double()) {
    ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
      1, *this->empty_double_, output);
  }

  // optional .google.protobuf.testing.DoubleMessage double_with_default_value = 2;
  if (this->has_double_with_default_value()) {
    ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
      2, *this->double_with_default_value_, output);
  }

  // optional .google.protobuf.testing.DoubleMessage double_with_nondefault_value = 3;
  if (this->has_double_with_nondefault_value()) {
    ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
      3, *this->double_with_nondefault_value_, output);
  }

  // optional .google.protobuf.testing.DoubleMessage repeated_double = 4;
  if (this->has_repeated_double()) {
    ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
      4, *this->repeated_double_, output);
  }

  // optional .google.protobuf.testing.DoubleMessage nested_message = 5;
  if (this->has_nested_message()) {
    ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
      5, *this->nested_message_, output);
  }

  // optional .google.protobuf.testing.DoubleMessage repeated_nested_message = 6;
  if (this->has_repeated_nested_message()) {
    ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
      6, *this->repeated_nested_message_, output);
  }

  // optional .google.protobuf.testing.DoubleMessage double_message_with_oneof = 7;
  if (this->has_double_message_with_oneof()) {
    ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
      7, *this->double_message_with_oneof_, output);
  }

  // optional .google.protobuf.testing.StructMessage empty_struct = 201;
  if (this->has_empty_struct()) {
    ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
      201, *this->empty_struct_, output);
  }

  // optional .google.protobuf.testing.StructMessage empty_struct2 = 202;
  if (this->has_empty_struct2()) {
    ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
      202, *this->empty_struct2_, output);
  }

  // optional .google.protobuf.testing.StructMessage struct_with_null_value = 203;
  if (this->has_struct_with_null_value()) {
    ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
      203, *this->struct_with_null_value_, output);
  }

  // optional .google.protobuf.testing.StructMessage struct_with_values = 204;
  if (this->has_struct_with_values()) {
    ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
      204, *this->struct_with_values_, output);
  }

  // optional .google.protobuf.testing.StructMessage struct_with_nested_struct = 205;
  if (this->has_struct_with_nested_struct()) {
    ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
      205, *this->struct_with_nested_struct_, output);
  }

  // optional .google.protobuf.testing.StructMessage struct_with_nested_list = 206;
  if (this->has_struct_with_nested_list()) {
    ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
      206, *this->struct_with_nested_list_, output);
  }

  // optional .google.protobuf.testing.StructMessage struct_with_list_of_nulls = 207;
  if (this->has_struct_with_list_of_nulls()) {
    ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
      207, *this->struct_with_list_of_nulls_, output);
  }

  // optional .google.protobuf.testing.StructMessage struct_with_list_of_lists = 208;
  if (this->has_struct_with_list_of_lists()) {
    ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
      208, *this->struct_with_list_of_lists_, output);
  }

  // optional .google.protobuf.testing.StructMessage struct_with_list_of_structs = 209;
  if (this->has_struct_with_list_of_structs()) {
    ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
      209, *this->struct_with_list_of_structs_, output);
  }

  // optional .google.protobuf.Struct top_level_struct = 210;
  if (this->has_top_level_struct()) {
    ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
      210, *this->top_level_struct_, output);
  }

  // optional .google.protobuf.testing.ValueMessage value_wrapper_simple = 212;
  if (this->has_value_wrapper_simple()) {
    ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
      212, *this->value_wrapper_simple_, output);
  }

  // optional .google.protobuf.testing.ValueMessage value_wrapper_with_struct = 213;
  if (this->has_value_wrapper_with_struct()) {
    ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
      213, *this->value_wrapper_with_struct_, output);
  }

  // optional .google.protobuf.testing.ValueMessage value_wrapper_with_list = 214;
  if (this->has_value_wrapper_with_list()) {
    ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
      214, *this->value_wrapper_with_list_, output);
  }

  // optional .google.protobuf.testing.ListValueMessage list_value_wrapper = 215;
  if (this->has_list_value_wrapper()) {
    ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
      215, *this->list_value_wrapper_, output);
  }

  // optional .google.protobuf.Value top_level_value_simple = 216;
  if (this->has_top_level_value_simple()) {
    ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
      216, *this->top_level_value_simple_, output);
  }

  // optional .google.protobuf.Value top_level_value_with_struct = 217;
  if (this->has_top_level_value_with_struct()) {
    ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
      217, *this->top_level_value_with_struct_, output);
  }

  // optional .google.protobuf.Value top_level_value_with_list = 218;
  if (this->has_top_level_value_with_list()) {
    ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
      218, *this->top_level_value_with_list_, output);
  }

  // optional .google.protobuf.ListValue top_level_listvalue = 219;
  if (this->has_top_level_listvalue()) {
    ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
      219, *this->top_level_listvalue_, output);
  }

  // optional .google.protobuf.testing.AnyMessage empty_any = 301;
  if (this->has_empty_any()) {
    ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
      301, *this->empty_any_, output);
  }

  // optional .google.protobuf.testing.AnyMessage type_only_any = 302;
  if (this->has_type_only_any()) {
    ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
      302, *this->type_only_any_, output);
  }

  // optional .google.protobuf.testing.AnyMessage recursive_any = 303;
  if (this->has_recursive_any()) {
    ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
      303, *this->recursive_any_, output);
  }

  // optional .google.protobuf.testing.AnyMessage any_with_message_value = 304;
  if (this->has_any_with_message_value()) {
    ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
      304, *this->any_with_message_value_, output);
  }

  // optional .google.protobuf.testing.AnyMessage any_with_nested_message = 305;
  if (this->has_any_with_nested_message()) {
    ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
      305, *this->any_with_nested_message_, output);
  }

  // optional .google.protobuf.testing.AnyMessage any_with_message_containing_map = 306;
  if (this->has_any_with_message_containing_map()) {
    ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
      306, *this->any_with_message_containing_map_, output);
  }

  // optional .google.protobuf.testing.AnyMessage any_with_message_containing_struct = 307;
  if (this->has_any_with_message_containing_struct()) {
    ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
      307, *this->any_with_message_containing_struct_, output);
  }

  // optional .google.protobuf.Any top_level_any = 308;
  if (this->has_top_level_any()) {
    ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
      308, *this->top_level_any_, output);
  }

  // optional .google.protobuf.testing.StringtoIntMap empty_map = 401;
  if (this->has_empty_map()) {
    ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
      401, *this->empty_map_, output);
  }

  // optional .google.protobuf.testing.StringtoIntMap string_to_int = 402;
  if (this->has_string_to_int()) {
    ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
      402, *this->string_to_int_, output);
  }

  // optional .google.protobuf.testing.IntToStringMap int_to_string = 403;
  if (this->has_int_to_string()) {
    ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
      403, *this->int_to_string_, output);
  }

  // optional .google.protobuf.testing.MixedMap mixed1 = 404;
  if (this->has_mixed1()) {
    ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
      404, *this->mixed1_, output);
  }

  // optional .google.protobuf.testing.MixedMap2 mixed2 = 405;
  if (this->has_mixed2()) {
    ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
      405, *this->mixed2_, output);
  }

  // optional .google.protobuf.testing.MixedMap2 empty_mixed2 = 406;
  if (this->has_empty_mixed2()) {
    ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
      406, *this->empty_mixed2_, output);
  }

  // optional .google.protobuf.testing.MessageMap map_of_objects = 407;
  if (this->has_map_of_objects()) {
    ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
      407, *this->map_of_objects_, output);
  }

  // optional .google.protobuf.testing.MixedMap mixed_empty = 408;
  if (this->has_mixed_empty()) {
    ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
      408, *this->mixed_empty_, output);
  }

  // optional .google.protobuf.testing.MessageMap message_map_empty = 409;
  if (this->has_message_map_empty()) {
    ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
      409, *this->message_map_empty_, output);
  }

  // optional .google.protobuf.testing.DoubleValueMessage double_value = 501;
  if (this->has_double_value()) {
    ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
      501, *this->double_value_, output);
  }

  // optional .google.protobuf.testing.DoubleValueMessage double_value_default = 502;
  if (this->has_double_value_default()) {
    ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
      502, *this->double_value_default_, output);
  }

  // @@protoc_insertion_point(serialize_end:google.protobuf.testing.DefaultValueTestCases)
}

::google::protobuf::uint8* DefaultValueTestCases::InternalSerializeWithCachedSizesToArray(
    bool deterministic, ::google::protobuf::uint8* target) const {
  (void)deterministic; // Unused
  // @@protoc_insertion_point(serialize_to_array_start:google.protobuf.testing.DefaultValueTestCases)
  // optional .google.protobuf.testing.DoubleMessage empty_double = 1;
  if (this->has_empty_double()) {
    target = ::google::protobuf::internal::WireFormatLite::
      InternalWriteMessageNoVirtualToArray(
        1, *this->empty_double_, false, target);
  }

  // optional .google.protobuf.testing.DoubleMessage double_with_default_value = 2;
  if (this->has_double_with_default_value()) {
    target = ::google::protobuf::internal::WireFormatLite::
      InternalWriteMessageNoVirtualToArray(
        2, *this->double_with_default_value_, false, target);
  }

  // optional .google.protobuf.testing.DoubleMessage double_with_nondefault_value = 3;
  if (this->has_double_with_nondefault_value()) {
    target = ::google::protobuf::internal::WireFormatLite::
      InternalWriteMessageNoVirtualToArray(
        3, *this->double_with_nondefault_value_, false, target);
  }

  // optional .google.protobuf.testing.DoubleMessage repeated_double = 4;
  if (this->has_repeated_double()) {
    target = ::google::protobuf::internal::WireFormatLite::
      InternalWriteMessageNoVirtualToArray(
        4, *this->repeated_double_, false, target);
  }

  // optional .google.protobuf.testing.DoubleMessage nested_message = 5;
  if (this->has_nested_message()) {
    target = ::google::protobuf::internal::WireFormatLite::
      InternalWriteMessageNoVirtualToArray(
        5, *this->nested_message_, false, target);
  }

  // optional .google.protobuf.testing.DoubleMessage repeated_nested_message = 6;
  if (this->has_repeated_nested_message()) {
    target = ::google::protobuf::internal::WireFormatLite::
      InternalWriteMessageNoVirtualToArray(
        6, *this->repeated_nested_message_, false, target);
  }

  // optional .google.protobuf.testing.DoubleMessage double_message_with_oneof = 7;
  if (this->has_double_message_with_oneof()) {
    target = ::google::protobuf::internal::WireFormatLite::
      InternalWriteMessageNoVirtualToArray(
        7, *this->double_message_with_oneof_, false, target);
  }

  // optional .google.protobuf.testing.StructMessage empty_struct = 201;
  if (this->has_empty_struct()) {
    target = ::google::protobuf::internal::WireFormatLite::
      InternalWriteMessageNoVirtualToArray(
        201, *this->empty_struct_, false, target);
  }

  // optional .google.protobuf.testing.StructMessage empty_struct2 = 202;
  if (this->has_empty_struct2()) {
    target = ::google::protobuf::internal::WireFormatLite::
      InternalWriteMessageNoVirtualToArray(
        202, *this->empty_struct2_, false, target);
  }

  // optional .google.protobuf.testing.StructMessage struct_with_null_value = 203;
  if (this->has_struct_with_null_value()) {
    target = ::google::protobuf::internal::WireFormatLite::
      InternalWriteMessageNoVirtualToArray(
        203, *this->struct_with_null_value_, false, target);
  }

  // optional .google.protobuf.testing.StructMessage struct_with_values = 204;
  if (this->has_struct_with_values()) {
    target = ::google::protobuf::internal::WireFormatLite::
      InternalWriteMessageNoVirtualToArray(
        204, *this->struct_with_values_, false, target);
  }

  // optional .google.protobuf.testing.StructMessage struct_with_nested_struct = 205;
  if (this->has_struct_with_nested_struct()) {
    target = ::google::protobuf::internal::WireFormatLite::
      InternalWriteMessageNoVirtualToArray(
        205, *this->struct_with_nested_struct_, false, target);
  }

  // optional .google.protobuf.testing.StructMessage struct_with_nested_list = 206;
  if (this->has_struct_with_nested_list()) {
    target = ::google::protobuf::internal::WireFormatLite::
      InternalWriteMessageNoVirtualToArray(
        206, *this->struct_with_nested_list_, false, target);
  }

  // optional .google.protobuf.testing.StructMessage struct_with_list_of_nulls = 207;
  if (this->has_struct_with_list_of_nulls()) {
    target = ::google::protobuf::internal::WireFormatLite::
      InternalWriteMessageNoVirtualToArray(
        207, *this->struct_with_list_of_nulls_, false, target);
  }

  // optional .google.protobuf.testing.StructMessage struct_with_list_of_lists = 208;
  if (this->has_struct_with_list_of_lists()) {
    target = ::google::protobuf::internal::WireFormatLite::
      InternalWriteMessageNoVirtualToArray(
        208, *this->struct_with_list_of_lists_, false, target);
  }

  // optional .google.protobuf.testing.StructMessage struct_with_list_of_structs = 209;
  if (this->has_struct_with_list_of_structs()) {
    target = ::google::protobuf::internal::WireFormatLite::
      InternalWriteMessageNoVirtualToArray(
        209, *this->struct_with_list_of_structs_, false, target);
  }

  // optional .google.protobuf.Struct top_level_struct = 210;
  if (this->has_top_level_struct()) {
    target = ::google::protobuf::internal::WireFormatLite::
      InternalWriteMessageNoVirtualToArray(
        210, *this->top_level_struct_, false, target);
  }

  // optional .google.protobuf.testing.ValueMessage value_wrapper_simple = 212;
  if (this->has_value_wrapper_simple()) {
    target = ::google::protobuf::internal::WireFormatLite::
      InternalWriteMessageNoVirtualToArray(
        212, *this->value_wrapper_simple_, false, target);
  }

  // optional .google.protobuf.testing.ValueMessage value_wrapper_with_struct = 213;
  if (this->has_value_wrapper_with_struct()) {
    target = ::google::protobuf::internal::WireFormatLite::
      InternalWriteMessageNoVirtualToArray(
        213, *this->value_wrapper_with_struct_, false, target);
  }

  // optional .google.protobuf.testing.ValueMessage value_wrapper_with_list = 214;
  if (this->has_value_wrapper_with_list()) {
    target = ::google::protobuf::internal::WireFormatLite::
      InternalWriteMessageNoVirtualToArray(
        214, *this->value_wrapper_with_list_, false, target);
  }

  // optional .google.protobuf.testing.ListValueMessage list_value_wrapper = 215;
  if (this->has_list_value_wrapper()) {
    target = ::google::protobuf::internal::WireFormatLite::
      InternalWriteMessageNoVirtualToArray(
        215, *this->list_value_wrapper_, false, target);
  }

  // optional .google.protobuf.Value top_level_value_simple = 216;
  if (this->has_top_level_value_simple()) {
    target = ::google::protobuf::internal::WireFormatLite::
      InternalWriteMessageNoVirtualToArray(
        216, *this->top_level_value_simple_, false, target);
  }

  // optional .google.protobuf.Value top_level_value_with_struct = 217;
  if (this->has_top_level_value_with_struct()) {
    target = ::google::protobuf::internal::WireFormatLite::
      InternalWriteMessageNoVirtualToArray(
        217, *this->top_level_value_with_struct_, false, target);
  }

  // optional .google.protobuf.Value top_level_value_with_list = 218;
  if (this->has_top_level_value_with_list()) {
    target = ::google::protobuf::internal::WireFormatLite::
      InternalWriteMessageNoVirtualToArray(
        218, *this->top_level_value_with_list_, false, target);
  }

  // optional .google.protobuf.ListValue top_level_listvalue = 219;
  if (this->has_top_level_listvalue()) {
    target = ::google::protobuf::internal::WireFormatLite::
      InternalWriteMessageNoVirtualToArray(
        219, *this->top_level_listvalue_, false, target);
  }

  // optional .google.protobuf.testing.AnyMessage empty_any = 301;
  if (this->has_empty_any()) {
    target = ::google::protobuf::internal::WireFormatLite::
      InternalWriteMessageNoVirtualToArray(
        301, *this->empty_any_, false, target);
  }

  // optional .google.protobuf.testing.AnyMessage type_only_any = 302;
  if (this->has_type_only_any()) {
    target = ::google::protobuf::internal::WireFormatLite::
      InternalWriteMessageNoVirtualToArray(
        302, *this->type_only_any_, false, target);
  }

  // optional .google.protobuf.testing.AnyMessage recursive_any = 303;
  if (this->has_recursive_any()) {
    target = ::google::protobuf::internal::WireFormatLite::
      InternalWriteMessageNoVirtualToArray(
        303, *this->recursive_any_, false, target);
  }

  // optional .google.protobuf.testing.AnyMessage any_with_message_value = 304;
  if (this->has_any_with_message_value()) {
    target = ::google::protobuf::internal::WireFormatLite::
      InternalWriteMessageNoVirtualToArray(
        304, *this->any_with_message_value_, false, target);
  }

  // optional .google.protobuf.testing.AnyMessage any_with_nested_message = 305;
  if (this->has_any_with_nested_message()) {
    target = ::google::protobuf::internal::WireFormatLite::
      InternalWriteMessageNoVirtualToArray(
        305, *this->any_with_nested_message_, false, target);
  }

  // optional .google.protobuf.testing.AnyMessage any_with_message_containing_map = 306;
  if (this->has_any_with_message_containing_map()) {
    target = ::google::protobuf::internal::WireFormatLite::
      InternalWriteMessageNoVirtualToArray(
        306, *this->any_with_message_containing_map_, false, target);
  }

  // optional .google.protobuf.testing.AnyMessage any_with_message_containing_struct = 307;
  if (this->has_any_with_message_containing_struct()) {
    target = ::google::protobuf::internal::WireFormatLite::
      InternalWriteMessageNoVirtualToArray(
        307, *this->any_with_message_containing_struct_, false, target);
  }

  // optional .google.protobuf.Any top_level_any = 308;
  if (this->has_top_level_any()) {
    target = ::google::protobuf::internal::WireFormatLite::
      InternalWriteMessageNoVirtualToArray(
        308, *this->top_level_any_, false, target);
  }

  // optional .google.protobuf.testing.StringtoIntMap empty_map = 401;
  if (this->has_empty_map()) {
    target = ::google::protobuf::internal::WireFormatLite::
      InternalWriteMessageNoVirtualToArray(
        401, *this->empty_map_, false, target);
  }

  // optional .google.protobuf.testing.StringtoIntMap string_to_int = 402;
  if (this->has_string_to_int()) {
    target = ::google::protobuf::internal::WireFormatLite::
      InternalWriteMessageNoVirtualToArray(
        402, *this->string_to_int_, false, target);
  }

  // optional .google.protobuf.testing.IntToStringMap int_to_string = 403;
  if (this->has_int_to_string()) {
    target = ::google::protobuf::internal::WireFormatLite::
      InternalWriteMessageNoVirtualToArray(
        403, *this->int_to_string_, false, target);
  }

  // optional .google.protobuf.testing.MixedMap mixed1 = 404;
  if (this->has_mixed1()) {
    target = ::google::protobuf::internal::WireFormatLite::
      InternalWriteMessageNoVirtualToArray(
        404, *this->mixed1_, false, target);
  }

  // optional .google.protobuf.testing.MixedMap2 mixed2 = 405;
  if (this->has_mixed2()) {
    target = ::google::protobuf::internal::WireFormatLite::
      InternalWriteMessageNoVirtualToArray(
        405, *this->mixed2_, false, target);
  }

  // optional .google.protobuf.testing.MixedMap2 empty_mixed2 = 406;
  if (this->has_empty_mixed2()) {
    target = ::google::protobuf::internal::WireFormatLite::
      InternalWriteMessageNoVirtualToArray(
        406, *this->empty_mixed2_, false, target);
  }

  // optional .google.protobuf.testing.MessageMap map_of_objects = 407;
  if (this->has_map_of_objects()) {
    target = ::google::protobuf::internal::WireFormatLite::
      InternalWriteMessageNoVirtualToArray(
        407, *this->map_of_objects_, false, target);
  }

  // optional .google.protobuf.testing.MixedMap mixed_empty = 408;
  if (this->has_mixed_empty()) {
    target = ::google::protobuf::internal::WireFormatLite::
      InternalWriteMessageNoVirtualToArray(
        408, *this->mixed_empty_, false, target);
  }

  // optional .google.protobuf.testing.MessageMap message_map_empty = 409;
  if (this->has_message_map_empty()) {
    target = ::google::protobuf::internal::WireFormatLite::
      InternalWriteMessageNoVirtualToArray(
        409, *this->message_map_empty_, false, target);
  }

  // optional .google.protobuf.testing.DoubleValueMessage double_value = 501;
  if (this->has_double_value()) {
    target = ::google::protobuf::internal::WireFormatLite::
      InternalWriteMessageNoVirtualToArray(
        501, *this->double_value_, false, target);
  }

  // optional .google.protobuf.testing.DoubleValueMessage double_value_default = 502;
  if (this->has_double_value_default()) {
    target = ::google::protobuf::internal::WireFormatLite::
      InternalWriteMessageNoVirtualToArray(
        502, *this->double_value_default_, false, target);
  }

  // @@protoc_insertion_point(serialize_to_array_end:google.protobuf.testing.DefaultValueTestCases)
  return target;
}

size_t DefaultValueTestCases::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:google.protobuf.testing.DefaultValueTestCases)
  size_t total_size = 0;

  // optional .google.protobuf.testing.DoubleMessage empty_double = 1;
  if (this->has_empty_double()) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::MessageSizeNoVirtual(
        *this->empty_double_);
  }

  // optional .google.protobuf.testing.DoubleMessage double_with_default_value = 2;
  if (this->has_double_with_default_value()) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::MessageSizeNoVirtual(
        *this->double_with_default_value_);
  }

  // optional .google.protobuf.testing.DoubleMessage double_with_nondefault_value = 3;
  if (this->has_double_with_nondefault_value()) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::MessageSizeNoVirtual(
        *this->double_with_nondefault_value_);
  }

  // optional .google.protobuf.testing.DoubleMessage repeated_double = 4;
  if (this->has_repeated_double()) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::MessageSizeNoVirtual(
        *this->repeated_double_);
  }

  // optional .google.protobuf.testing.DoubleMessage nested_message = 5;
  if (this->has_nested_message()) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::MessageSizeNoVirtual(
        *this->nested_message_);
  }

  // optional .google.protobuf.testing.DoubleMessage repeated_nested_message = 6;
  if (this->has_repeated_nested_message()) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::MessageSizeNoVirtual(
        *this->repeated_nested_message_);
  }

  // optional .google.protobuf.testing.DoubleMessage double_message_with_oneof = 7;
  if (this->has_double_message_with_oneof()) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::MessageSizeNoVirtual(
        *this->double_message_with_oneof_);
  }

  // optional .google.protobuf.testing.StructMessage empty_struct = 201;
  if (this->has_empty_struct()) {
    total_size += 2 +
      ::google::protobuf::internal::WireFormatLite::MessageSizeNoVirtual(
        *this->empty_struct_);
  }

  // optional .google.protobuf.testing.StructMessage empty_struct2 = 202;
  if (this->has_empty_struct2()) {
    total_size += 2 +
      ::google::protobuf::internal::WireFormatLite::MessageSizeNoVirtual(
        *this->empty_struct2_);
  }

  // optional .google.protobuf.testing.StructMessage struct_with_null_value = 203;
  if (this->has_struct_with_null_value()) {
    total_size += 2 +
      ::google::protobuf::internal::WireFormatLite::MessageSizeNoVirtual(
        *this->struct_with_null_value_);
  }

  // optional .google.protobuf.testing.StructMessage struct_with_values = 204;
  if (this->has_struct_with_values()) {
    total_size += 2 +
      ::google::protobuf::internal::WireFormatLite::MessageSizeNoVirtual(
        *this->struct_with_values_);
  }

  // optional .google.protobuf.testing.StructMessage struct_with_nested_struct = 205;
  if (this->has_struct_with_nested_struct()) {
    total_size += 2 +
      ::google::protobuf::internal::WireFormatLite::MessageSizeNoVirtual(
        *this->struct_with_nested_struct_);
  }

  // optional .google.protobuf.testing.StructMessage struct_with_nested_list = 206;
  if (this->has_struct_with_nested_list()) {
    total_size += 2 +
      ::google::protobuf::internal::WireFormatLite::MessageSizeNoVirtual(
        *this->struct_with_nested_list_);
  }

  // optional .google.protobuf.testing.StructMessage struct_with_list_of_nulls = 207;
  if (this->has_struct_with_list_of_nulls()) {
    total_size += 2 +
      ::google::protobuf::internal::WireFormatLite::MessageSizeNoVirtual(
        *this->struct_with_list_of_nulls_);
  }

  // optional .google.protobuf.testing.StructMessage struct_with_list_of_lists = 208;
  if (this->has_struct_with_list_of_lists()) {
    total_size += 2 +
      ::google::protobuf::internal::WireFormatLite::MessageSizeNoVirtual(
        *this->struct_with_list_of_lists_);
  }

  // optional .google.protobuf.testing.StructMessage struct_with_list_of_structs = 209;
  if (this->has_struct_with_list_of_structs()) {
    total_size += 2 +
      ::google::protobuf::internal::WireFormatLite::MessageSizeNoVirtual(
        *this->struct_with_list_of_structs_);
  }

  // optional .google.protobuf.Struct top_level_struct = 210;
  if (this->has_top_level_struct()) {
    total_size += 2 +
      ::google::protobuf::internal::WireFormatLite::MessageSizeNoVirtual(
        *this->top_level_struct_);
  }

  // optional .google.protobuf.testing.ValueMessage value_wrapper_simple = 212;
  if (this->has_value_wrapper_simple()) {
    total_size += 2 +
      ::google::protobuf::internal::WireFormatLite::MessageSizeNoVirtual(
        *this->value_wrapper_simple_);
  }

  // optional .google.protobuf.testing.ValueMessage value_wrapper_with_struct = 213;
  if (this->has_value_wrapper_with_struct()) {
    total_size += 2 +
      ::google::protobuf::internal::WireFormatLite::MessageSizeNoVirtual(
        *this->value_wrapper_with_struct_);
  }

  // optional .google.protobuf.testing.ValueMessage value_wrapper_with_list = 214;
  if (this->has_value_wrapper_with_list()) {
    total_size += 2 +
      ::google::protobuf::internal::WireFormatLite::MessageSizeNoVirtual(
        *this->value_wrapper_with_list_);
  }

  // optional .google.protobuf.testing.ListValueMessage list_value_wrapper = 215;
  if (this->has_list_value_wrapper()) {
    total_size += 2 +
      ::google::protobuf::internal::WireFormatLite::MessageSizeNoVirtual(
        *this->list_value_wrapper_);
  }

  // optional .google.protobuf.Value top_level_value_simple = 216;
  if (this->has_top_level_value_simple()) {
    total_size += 2 +
      ::google::protobuf::internal::WireFormatLite::MessageSizeNoVirtual(
        *this->top_level_value_simple_);
  }

  // optional .google.protobuf.Value top_level_value_with_struct = 217;
  if (this->has_top_level_value_with_struct()) {
    total_size += 2 +
      ::google::protobuf::internal::WireFormatLite::MessageSizeNoVirtual(
        *this->top_level_value_with_struct_);
  }

  // optional .google.protobuf.Value top_level_value_with_list = 218;
  if (this->has_top_level_value_with_list()) {
    total_size += 2 +
      ::google::protobuf::internal::WireFormatLite::MessageSizeNoVirtual(
        *this->top_level_value_with_list_);
  }

  // optional .google.protobuf.ListValue top_level_listvalue = 219;
  if (this->has_top_level_listvalue()) {
    total_size += 2 +
      ::google::protobuf::internal::WireFormatLite::MessageSizeNoVirtual(
        *this->top_level_listvalue_);
  }

  // optional .google.protobuf.testing.AnyMessage empty_any = 301;
  if (this->has_empty_any()) {
    total_size += 2 +
      ::google::protobuf::internal::WireFormatLite::MessageSizeNoVirtual(
        *this->empty_any_);
  }

  // optional .google.protobuf.testing.AnyMessage type_only_any = 302;
  if (this->has_type_only_any()) {
    total_size += 2 +
      ::google::protobuf::internal::WireFormatLite::MessageSizeNoVirtual(
        *this->type_only_any_);
  }

  // optional .google.protobuf.testing.AnyMessage recursive_any = 303;
  if (this->has_recursive_any()) {
    total_size += 2 +
      ::google::protobuf::internal::WireFormatLite::MessageSizeNoVirtual(
        *this->recursive_any_);
  }

  // optional .google.protobuf.testing.AnyMessage any_with_message_value = 304;
  if (this->has_any_with_message_value()) {
    total_size += 2 +
      ::google::protobuf::internal::WireFormatLite::MessageSizeNoVirtual(
        *this->any_with_message_value_);
  }

  // optional .google.protobuf.testing.AnyMessage any_with_nested_message = 305;
  if (this->has_any_with_nested_message()) {
    total_size += 2 +
      ::google::protobuf::internal::WireFormatLite::MessageSizeNoVirtual(
        *this->any_with_nested_message_);
  }

  // optional .google.protobuf.testing.AnyMessage any_with_message_containing_map = 306;
  if (this->has_any_with_message_containing_map()) {
    total_size += 2 +
      ::google::protobuf::internal::WireFormatLite::MessageSizeNoVirtual(
        *this->any_with_message_containing_map_);
  }

  // optional .google.protobuf.testing.AnyMessage any_with_message_containing_struct = 307;
  if (this->has_any_with_message_containing_struct()) {
    total_size += 2 +
      ::google::protobuf::internal::WireFormatLite::MessageSizeNoVirtual(
        *this->any_with_message_containing_struct_);
  }

  // optional .google.protobuf.Any top_level_any = 308;
  if (this->has_top_level_any()) {
    total_size += 2 +
      ::google::protobuf::internal::WireFormatLite::MessageSizeNoVirtual(
        *this->top_level_any_);
  }

  // optional .google.protobuf.testing.StringtoIntMap empty_map = 401;
  if (this->has_empty_map()) {
    total_size += 2 +
      ::google::protobuf::internal::WireFormatLite::MessageSizeNoVirtual(
        *this->empty_map_);
  }

  // optional .google.protobuf.testing.StringtoIntMap string_to_int = 402;
  if (this->has_string_to_int()) {
    total_size += 2 +
      ::google::protobuf::internal::WireFormatLite::MessageSizeNoVirtual(
        *this->string_to_int_);
  }

  // optional .google.protobuf.testing.IntToStringMap int_to_string = 403;
  if (this->has_int_to_string()) {
    total_size += 2 +
      ::google::protobuf::internal::WireFormatLite::MessageSizeNoVirtual(
        *this->int_to_string_);
  }

  // optional .google.protobuf.testing.MixedMap mixed1 = 404;
  if (this->has_mixed1()) {
    total_size += 2 +
      ::google::protobuf::internal::WireFormatLite::MessageSizeNoVirtual(
        *this->mixed1_);
  }

  // optional .google.protobuf.testing.MixedMap2 mixed2 = 405;
  if (this->has_mixed2()) {
    total_size += 2 +
      ::google::protobuf::internal::WireFormatLite::MessageSizeNoVirtual(
        *this->mixed2_);
  }

  // optional .google.protobuf.testing.MixedMap2 empty_mixed2 = 406;
  if (this->has_empty_mixed2()) {
    total_size += 2 +
      ::google::protobuf::internal::WireFormatLite::MessageSizeNoVirtual(
        *this->empty_mixed2_);
  }

  // optional .google.protobuf.testing.MessageMap map_of_objects = 407;
  if (this->has_map_of_objects()) {
    total_size += 2 +
      ::google::protobuf::internal::WireFormatLite::MessageSizeNoVirtual(
        *this->map_of_objects_);
  }

  // optional .google.protobuf.testing.MixedMap mixed_empty = 408;
  if (this->has_mixed_empty()) {
    total_size += 2 +
      ::google::protobuf::internal::WireFormatLite::MessageSizeNoVirtual(
        *this->mixed_empty_);
  }

  // optional .google.protobuf.testing.MessageMap message_map_empty = 409;
  if (this->has_message_map_empty()) {
    total_size += 2 +
      ::google::protobuf::internal::WireFormatLite::MessageSizeNoVirtual(
        *this->message_map_empty_);
  }

  // optional .google.protobuf.testing.DoubleValueMessage double_value = 501;
  if (this->has_double_value()) {
    total_size += 2 +
      ::google::protobuf::internal::WireFormatLite::MessageSizeNoVirtual(
        *this->double_value_);
  }

  // optional .google.protobuf.testing.DoubleValueMessage double_value_default = 502;
  if (this->has_double_value_default()) {
    total_size += 2 +
      ::google::protobuf::internal::WireFormatLite::MessageSizeNoVirtual(
        *this->double_value_default_);
  }

  int cached_size = ::google::protobuf::internal::ToCachedSize(total_size);
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = cached_size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
  return total_size;
}

void DefaultValueTestCases::MergeFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_merge_from_start:google.protobuf.testing.DefaultValueTestCases)
  if (GOOGLE_PREDICT_FALSE(&from == this)) MergeFromFail(__LINE__);
  const DefaultValueTestCases* source =
      ::google::protobuf::internal::DynamicCastToGenerated<const DefaultValueTestCases>(
          &from);
  if (source == NULL) {
  // @@protoc_insertion_point(generalized_merge_from_cast_fail:google.protobuf.testing.DefaultValueTestCases)
    ::google::protobuf::internal::ReflectionOps::Merge(from, this);
  } else {
  // @@protoc_insertion_point(generalized_merge_from_cast_success:google.protobuf.testing.DefaultValueTestCases)
    UnsafeMergeFrom(*source);
  }
}

void DefaultValueTestCases::MergeFrom(const DefaultValueTestCases& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:google.protobuf.testing.DefaultValueTestCases)
  if (GOOGLE_PREDICT_TRUE(&from != this)) {
    UnsafeMergeFrom(from);
  } else {
    MergeFromFail(__LINE__);
  }
}

void DefaultValueTestCases::UnsafeMergeFrom(const DefaultValueTestCases& from) {
  GOOGLE_DCHECK(&from != this);
  if (from.has_empty_double()) {
    mutable_empty_double()->::google::protobuf::testing::DoubleMessage::MergeFrom(from.empty_double());
  }
  if (from.has_double_with_default_value()) {
    mutable_double_with_default_value()->::google::protobuf::testing::DoubleMessage::MergeFrom(from.double_with_default_value());
  }
  if (from.has_double_with_nondefault_value()) {
    mutable_double_with_nondefault_value()->::google::protobuf::testing::DoubleMessage::MergeFrom(from.double_with_nondefault_value());
  }
  if (from.has_repeated_double()) {
    mutable_repeated_double()->::google::protobuf::testing::DoubleMessage::MergeFrom(from.repeated_double());
  }
  if (from.has_nested_message()) {
    mutable_nested_message()->::google::protobuf::testing::DoubleMessage::MergeFrom(from.nested_message());
  }
  if (from.has_repeated_nested_message()) {
    mutable_repeated_nested_message()->::google::protobuf::testing::DoubleMessage::MergeFrom(from.repeated_nested_message());
  }
  if (from.has_double_message_with_oneof()) {
    mutable_double_message_with_oneof()->::google::protobuf::testing::DoubleMessage::MergeFrom(from.double_message_with_oneof());
  }
  if (from.has_empty_struct()) {
    mutable_empty_struct()->::google::protobuf::testing::StructMessage::MergeFrom(from.empty_struct());
  }
  if (from.has_empty_struct2()) {
    mutable_empty_struct2()->::google::protobuf::testing::StructMessage::MergeFrom(from.empty_struct2());
  }
  if (from.has_struct_with_null_value()) {
    mutable_struct_with_null_value()->::google::protobuf::testing::StructMessage::MergeFrom(from.struct_with_null_value());
  }
  if (from.has_struct_with_values()) {
    mutable_struct_with_values()->::google::protobuf::testing::StructMessage::MergeFrom(from.struct_with_values());
  }
  if (from.has_struct_with_nested_struct()) {
    mutable_struct_with_nested_struct()->::google::protobuf::testing::StructMessage::MergeFrom(from.struct_with_nested_struct());
  }
  if (from.has_struct_with_nested_list()) {
    mutable_struct_with_nested_list()->::google::protobuf::testing::StructMessage::MergeFrom(from.struct_with_nested_list());
  }
  if (from.has_struct_with_list_of_nulls()) {
    mutable_struct_with_list_of_nulls()->::google::protobuf::testing::StructMessage::MergeFrom(from.struct_with_list_of_nulls());
  }
  if (from.has_struct_with_list_of_lists()) {
    mutable_struct_with_list_of_lists()->::google::protobuf::testing::StructMessage::MergeFrom(from.struct_with_list_of_lists());
  }
  if (from.has_struct_with_list_of_structs()) {
    mutable_struct_with_list_of_structs()->::google::protobuf::testing::StructMessage::MergeFrom(from.struct_with_list_of_structs());
  }
  if (from.has_top_level_struct()) {
    mutable_top_level_struct()->::google::protobuf::Struct::MergeFrom(from.top_level_struct());
  }
  if (from.has_value_wrapper_simple()) {
    mutable_value_wrapper_simple()->::google::protobuf::testing::ValueMessage::MergeFrom(from.value_wrapper_simple());
  }
  if (from.has_value_wrapper_with_struct()) {
    mutable_value_wrapper_with_struct()->::google::protobuf::testing::ValueMessage::MergeFrom(from.value_wrapper_with_struct());
  }
  if (from.has_value_wrapper_with_list()) {
    mutable_value_wrapper_with_list()->::google::protobuf::testing::ValueMessage::MergeFrom(from.value_wrapper_with_list());
  }
  if (from.has_list_value_wrapper()) {
    mutable_list_value_wrapper()->::google::protobuf::testing::ListValueMessage::MergeFrom(from.list_value_wrapper());
  }
  if (from.has_top_level_value_simple()) {
    mutable_top_level_value_simple()->::google::protobuf::Value::MergeFrom(from.top_level_value_simple());
  }
  if (from.has_top_level_value_with_struct()) {
    mutable_top_level_value_with_struct()->::google::protobuf::Value::MergeFrom(from.top_level_value_with_struct());
  }
  if (from.has_top_level_value_with_list()) {
    mutable_top_level_value_with_list()->::google::protobuf::Value::MergeFrom(from.top_level_value_with_list());
  }
  if (from.has_top_level_listvalue()) {
    mutable_top_level_listvalue()->::google::protobuf::ListValue::MergeFrom(from.top_level_listvalue());
  }
  if (from.has_empty_any()) {
    mutable_empty_any()->::google::protobuf::testing::AnyMessage::MergeFrom(from.empty_any());
  }
  if (from.has_type_only_any()) {
    mutable_type_only_any()->::google::protobuf::testing::AnyMessage::MergeFrom(from.type_only_any());
  }
  if (from.has_recursive_any()) {
    mutable_recursive_any()->::google::protobuf::testing::AnyMessage::MergeFrom(from.recursive_any());
  }
  if (from.has_any_with_message_value()) {
    mutable_any_with_message_value()->::google::protobuf::testing::AnyMessage::MergeFrom(from.any_with_message_value());
  }
  if (from.has_any_with_nested_message()) {
    mutable_any_with_nested_message()->::google::protobuf::testing::AnyMessage::MergeFrom(from.any_with_nested_message());
  }
  if (from.has_any_with_message_containing_map()) {
    mutable_any_with_message_containing_map()->::google::protobuf::testing::AnyMessage::MergeFrom(from.any_with_message_containing_map());
  }
  if (from.has_any_with_message_containing_struct()) {
    mutable_any_with_message_containing_struct()->::google::protobuf::testing::AnyMessage::MergeFrom(from.any_with_message_containing_struct());
  }
  if (from.has_top_level_any()) {
    mutable_top_level_any()->::google::protobuf::Any::MergeFrom(from.top_level_any());
  }
  if (from.has_empty_map()) {
    mutable_empty_map()->::google::protobuf::testing::StringtoIntMap::MergeFrom(from.empty_map());
  }
  if (from.has_string_to_int()) {
    mutable_string_to_int()->::google::protobuf::testing::StringtoIntMap::MergeFrom(from.string_to_int());
  }
  if (from.has_int_to_string()) {
    mutable_int_to_string()->::google::protobuf::testing::IntToStringMap::MergeFrom(from.int_to_string());
  }
  if (from.has_mixed1()) {
    mutable_mixed1()->::google::protobuf::testing::MixedMap::MergeFrom(from.mixed1());
  }
  if (from.has_mixed2()) {
    mutable_mixed2()->::google::protobuf::testing::MixedMap2::MergeFrom(from.mixed2());
  }
  if (from.has_empty_mixed2()) {
    mutable_empty_mixed2()->::google::protobuf::testing::MixedMap2::MergeFrom(from.empty_mixed2());
  }
  if (from.has_map_of_objects()) {
    mutable_map_of_objects()->::google::protobuf::testing::MessageMap::MergeFrom(from.map_of_objects());
  }
  if (from.has_mixed_empty()) {
    mutable_mixed_empty()->::google::protobuf::testing::MixedMap::MergeFrom(from.mixed_empty());
  }
  if (from.has_message_map_empty()) {
    mutable_message_map_empty()->::google::protobuf::testing::MessageMap::MergeFrom(from.message_map_empty());
  }
  if (from.has_double_value()) {
    mutable_double_value()->::google::protobuf::testing::DoubleValueMessage::MergeFrom(from.double_value());
  }
  if (from.has_double_value_default()) {
    mutable_double_value_default()->::google::protobuf::testing::DoubleValueMessage::MergeFrom(from.double_value_default());
  }
}

void DefaultValueTestCases::CopyFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_copy_from_start:google.protobuf.testing.DefaultValueTestCases)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

void DefaultValueTestCases::CopyFrom(const DefaultValueTestCases& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:google.protobuf.testing.DefaultValueTestCases)
  if (&from == this) return;
  Clear();
  UnsafeMergeFrom(from);
}

bool DefaultValueTestCases::IsInitialized() const {

  return true;
}

void DefaultValueTestCases::Swap(DefaultValueTestCases* other) {
  if (other == this) return;
  InternalSwap(other);
}
void DefaultValueTestCases::InternalSwap(DefaultValueTestCases* other) {
  std::swap(empty_double_, other->empty_double_);
  std::swap(double_with_default_value_, other->double_with_default_value_);
  std::swap(double_with_nondefault_value_, other->double_with_nondefault_value_);
  std::swap(repeated_double_, other->repeated_double_);
  std::swap(nested_message_, other->nested_message_);
  std::swap(repeated_nested_message_, other->repeated_nested_message_);
  std::swap(double_message_with_oneof_, other->double_message_with_oneof_);
  std::swap(empty_struct_, other->empty_struct_);
  std::swap(empty_struct2_, other->empty_struct2_);
  std::swap(struct_with_null_value_, other->struct_with_null_value_);
  std::swap(struct_with_values_, other->struct_with_values_);
  std::swap(struct_with_nested_struct_, other->struct_with_nested_struct_);
  std::swap(struct_with_nested_list_, other->struct_with_nested_list_);
  std::swap(struct_with_list_of_nulls_, other->struct_with_list_of_nulls_);
  std::swap(struct_with_list_of_lists_, other->struct_with_list_of_lists_);
  std::swap(struct_with_list_of_structs_, other->struct_with_list_of_structs_);
  std::swap(top_level_struct_, other->top_level_struct_);
  std::swap(value_wrapper_simple_, other->value_wrapper_simple_);
  std::swap(value_wrapper_with_struct_, other->value_wrapper_with_struct_);
  std::swap(value_wrapper_with_list_, other->value_wrapper_with_list_);
  std::swap(list_value_wrapper_, other->list_value_wrapper_);
  std::swap(top_level_value_simple_, other->top_level_value_simple_);
  std::swap(top_level_value_with_struct_, other->top_level_value_with_struct_);
  std::swap(top_level_value_with_list_, other->top_level_value_with_list_);
  std::swap(top_level_listvalue_, other->top_level_listvalue_);
  std::swap(empty_any_, other->empty_any_);
  std::swap(type_only_any_, other->type_only_any_);
  std::swap(recursive_any_, other->recursive_any_);
  std::swap(any_with_message_value_, other->any_with_message_value_);
  std::swap(any_with_nested_message_, other->any_with_nested_message_);
  std::swap(any_with_message_containing_map_, other->any_with_message_containing_map_);
  std::swap(any_with_message_containing_struct_, other->any_with_message_containing_struct_);
  std::swap(top_level_any_, other->top_level_any_);
  std::swap(empty_map_, other->empty_map_);
  std::swap(string_to_int_, other->string_to_int_);
  std::swap(int_to_string_, other->int_to_string_);
  std::swap(mixed1_, other->mixed1_);
  std::swap(mixed2_, other->mixed2_);
  std::swap(empty_mixed2_, other->empty_mixed2_);
  std::swap(map_of_objects_, other->map_of_objects_);
  std::swap(mixed_empty_, other->mixed_empty_);
  std::swap(message_map_empty_, other->message_map_empty_);
  std::swap(double_value_, other->double_value_);
  std::swap(double_value_default_, other->double_value_default_);
  _internal_metadata_.Swap(&other->_internal_metadata_);
  std::swap(_cached_size_, other->_cached_size_);
}

::google::protobuf::Metadata DefaultValueTestCases::GetMetadata() const {
  protobuf_AssignDescriptorsOnce();
  ::google::protobuf::Metadata metadata;
  metadata.descriptor = DefaultValueTestCases_descriptor_;
  metadata.reflection = DefaultValueTestCases_reflection_;
  return metadata;
}

#if PROTOBUF_INLINE_NOT_IN_HEADERS
// DefaultValueTestCases

// optional .google.protobuf.testing.DoubleMessage empty_double = 1;
bool DefaultValueTestCases::has_empty_double() const {
  return this != internal_default_instance() && empty_double_ != NULL;
}
void DefaultValueTestCases::clear_empty_double() {
  if (GetArenaNoVirtual() == NULL && empty_double_ != NULL) delete empty_double_;
  empty_double_ = NULL;
}
const ::google::protobuf::testing::DoubleMessage& DefaultValueTestCases::empty_double() const {
  // @@protoc_insertion_point(field_get:google.protobuf.testing.DefaultValueTestCases.empty_double)
  return empty_double_ != NULL ? *empty_double_
                         : *::google::protobuf::testing::DoubleMessage::internal_default_instance();
}
::google::protobuf::testing::DoubleMessage* DefaultValueTestCases::mutable_empty_double() {
  
  if (empty_double_ == NULL) {
    empty_double_ = new ::google::protobuf::testing::DoubleMessage;
  }
  // @@protoc_insertion_point(field_mutable:google.protobuf.testing.DefaultValueTestCases.empty_double)
  return empty_double_;
}
::google::protobuf::testing::DoubleMessage* DefaultValueTestCases::release_empty_double() {
  // @@protoc_insertion_point(field_release:google.protobuf.testing.DefaultValueTestCases.empty_double)
  
  ::google::protobuf::testing::DoubleMessage* temp = empty_double_;
  empty_double_ = NULL;
  return temp;
}
void DefaultValueTestCases::set_allocated_empty_double(::google::protobuf::testing::DoubleMessage* empty_double) {
  delete empty_double_;
  empty_double_ = empty_double;
  if (empty_double) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_set_allocated:google.protobuf.testing.DefaultValueTestCases.empty_double)
}

// optional .google.protobuf.testing.DoubleMessage double_with_default_value = 2;
bool DefaultValueTestCases::has_double_with_default_value() const {
  return this != internal_default_instance() && double_with_default_value_ != NULL;
}
void DefaultValueTestCases::clear_double_with_default_value() {
  if (GetArenaNoVirtual() == NULL && double_with_default_value_ != NULL) delete double_with_default_value_;
  double_with_default_value_ = NULL;
}
const ::google::protobuf::testing::DoubleMessage& DefaultValueTestCases::double_with_default_value() const {
  // @@protoc_insertion_point(field_get:google.protobuf.testing.DefaultValueTestCases.double_with_default_value)
  return double_with_default_value_ != NULL ? *double_with_default_value_
                         : *::google::protobuf::testing::DoubleMessage::internal_default_instance();
}
::google::protobuf::testing::DoubleMessage* DefaultValueTestCases::mutable_double_with_default_value() {
  
  if (double_with_default_value_ == NULL) {
    double_with_default_value_ = new ::google::protobuf::testing::DoubleMessage;
  }
  // @@protoc_insertion_point(field_mutable:google.protobuf.testing.DefaultValueTestCases.double_with_default_value)
  return double_with_default_value_;
}
::google::protobuf::testing::DoubleMessage* DefaultValueTestCases::release_double_with_default_value() {
  // @@protoc_insertion_point(field_release:google.protobuf.testing.DefaultValueTestCases.double_with_default_value)
  
  ::google::protobuf::testing::DoubleMessage* temp = double_with_default_value_;
  double_with_default_value_ = NULL;
  return temp;
}
void DefaultValueTestCases::set_allocated_double_with_default_value(::google::protobuf::testing::DoubleMessage* double_with_default_value) {
  delete double_with_default_value_;
  double_with_default_value_ = double_with_default_value;
  if (double_with_default_value) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_set_allocated:google.protobuf.testing.DefaultValueTestCases.double_with_default_value)
}

// optional .google.protobuf.testing.DoubleMessage double_with_nondefault_value = 3;
bool DefaultValueTestCases::has_double_with_nondefault_value() const {
  return this != internal_default_instance() && double_with_nondefault_value_ != NULL;
}
void DefaultValueTestCases::clear_double_with_nondefault_value() {
  if (GetArenaNoVirtual() == NULL && double_with_nondefault_value_ != NULL) delete double_with_nondefault_value_;
  double_with_nondefault_value_ = NULL;
}
const ::google::protobuf::testing::DoubleMessage& DefaultValueTestCases::double_with_nondefault_value() const {
  // @@protoc_insertion_point(field_get:google.protobuf.testing.DefaultValueTestCases.double_with_nondefault_value)
  return double_with_nondefault_value_ != NULL ? *double_with_nondefault_value_
                         : *::google::protobuf::testing::DoubleMessage::internal_default_instance();
}
::google::protobuf::testing::DoubleMessage* DefaultValueTestCases::mutable_double_with_nondefault_value() {
  
  if (double_with_nondefault_value_ == NULL) {
    double_with_nondefault_value_ = new ::google::protobuf::testing::DoubleMessage;
  }
  // @@protoc_insertion_point(field_mutable:google.protobuf.testing.DefaultValueTestCases.double_with_nondefault_value)
  return double_with_nondefault_value_;
}
::google::protobuf::testing::DoubleMessage* DefaultValueTestCases::release_double_with_nondefault_value() {
  // @@protoc_insertion_point(field_release:google.protobuf.testing.DefaultValueTestCases.double_with_nondefault_value)
  
  ::google::protobuf::testing::DoubleMessage* temp = double_with_nondefault_value_;
  double_with_nondefault_value_ = NULL;
  return temp;
}
void DefaultValueTestCases::set_allocated_double_with_nondefault_value(::google::protobuf::testing::DoubleMessage* double_with_nondefault_value) {
  delete double_with_nondefault_value_;
  double_with_nondefault_value_ = double_with_nondefault_value;
  if (double_with_nondefault_value) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_set_allocated:google.protobuf.testing.DefaultValueTestCases.double_with_nondefault_value)
}

// optional .google.protobuf.testing.DoubleMessage repeated_double = 4;
bool DefaultValueTestCases::has_repeated_double() const {
  return this != internal_default_instance() && repeated_double_ != NULL;
}
void DefaultValueTestCases::clear_repeated_double() {
  if (GetArenaNoVirtual() == NULL && repeated_double_ != NULL) delete repeated_double_;
  repeated_double_ = NULL;
}
const ::google::protobuf::testing::DoubleMessage& DefaultValueTestCases::repeated_double() const {
  // @@protoc_insertion_point(field_get:google.protobuf.testing.DefaultValueTestCases.repeated_double)
  return repeated_double_ != NULL ? *repeated_double_
                         : *::google::protobuf::testing::DoubleMessage::internal_default_instance();
}
::google::protobuf::testing::DoubleMessage* DefaultValueTestCases::mutable_repeated_double() {
  
  if (repeated_double_ == NULL) {
    repeated_double_ = new ::google::protobuf::testing::DoubleMessage;
  }
  // @@protoc_insertion_point(field_mutable:google.protobuf.testing.DefaultValueTestCases.repeated_double)
  return repeated_double_;
}
::google::protobuf::testing::DoubleMessage* DefaultValueTestCases::release_repeated_double() {
  // @@protoc_insertion_point(field_release:google.protobuf.testing.DefaultValueTestCases.repeated_double)
  
  ::google::protobuf::testing::DoubleMessage* temp = repeated_double_;
  repeated_double_ = NULL;
  return temp;
}
void DefaultValueTestCases::set_allocated_repeated_double(::google::protobuf::testing::DoubleMessage* repeated_double) {
  delete repeated_double_;
  repeated_double_ = repeated_double;
  if (repeated_double) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_set_allocated:google.protobuf.testing.DefaultValueTestCases.repeated_double)
}

// optional .google.protobuf.testing.DoubleMessage nested_message = 5;
bool DefaultValueTestCases::has_nested_message() const {
  return this != internal_default_instance() && nested_message_ != NULL;
}
void DefaultValueTestCases::clear_nested_message() {
  if (GetArenaNoVirtual() == NULL && nested_message_ != NULL) delete nested_message_;
  nested_message_ = NULL;
}
const ::google::protobuf::testing::DoubleMessage& DefaultValueTestCases::nested_message() const {
  // @@protoc_insertion_point(field_get:google.protobuf.testing.DefaultValueTestCases.nested_message)
  return nested_message_ != NULL ? *nested_message_
                         : *::google::protobuf::testing::DoubleMessage::internal_default_instance();
}
::google::protobuf::testing::DoubleMessage* DefaultValueTestCases::mutable_nested_message() {
  
  if (nested_message_ == NULL) {
    nested_message_ = new ::google::protobuf::testing::DoubleMessage;
  }
  // @@protoc_insertion_point(field_mutable:google.protobuf.testing.DefaultValueTestCases.nested_message)
  return nested_message_;
}
::google::protobuf::testing::DoubleMessage* DefaultValueTestCases::release_nested_message() {
  // @@protoc_insertion_point(field_release:google.protobuf.testing.DefaultValueTestCases.nested_message)
  
  ::google::protobuf::testing::DoubleMessage* temp = nested_message_;
  nested_message_ = NULL;
  return temp;
}
void DefaultValueTestCases::set_allocated_nested_message(::google::protobuf::testing::DoubleMessage* nested_message) {
  delete nested_message_;
  nested_message_ = nested_message;
  if (nested_message) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_set_allocated:google.protobuf.testing.DefaultValueTestCases.nested_message)
}

// optional .google.protobuf.testing.DoubleMessage repeated_nested_message = 6;
bool DefaultValueTestCases::has_repeated_nested_message() const {
  return this != internal_default_instance() && repeated_nested_message_ != NULL;
}
void DefaultValueTestCases::clear_repeated_nested_message() {
  if (GetArenaNoVirtual() == NULL && repeated_nested_message_ != NULL) delete repeated_nested_message_;
  repeated_nested_message_ = NULL;
}
const ::google::protobuf::testing::DoubleMessage& DefaultValueTestCases::repeated_nested_message() const {
  // @@protoc_insertion_point(field_get:google.protobuf.testing.DefaultValueTestCases.repeated_nested_message)
  return repeated_nested_message_ != NULL ? *repeated_nested_message_
                         : *::google::protobuf::testing::DoubleMessage::internal_default_instance();
}
::google::protobuf::testing::DoubleMessage* DefaultValueTestCases::mutable_repeated_nested_message() {
  
  if (repeated_nested_message_ == NULL) {
    repeated_nested_message_ = new ::google::protobuf::testing::DoubleMessage;
  }
  // @@protoc_insertion_point(field_mutable:google.protobuf.testing.DefaultValueTestCases.repeated_nested_message)
  return repeated_nested_message_;
}
::google::protobuf::testing::DoubleMessage* DefaultValueTestCases::release_repeated_nested_message() {
  // @@protoc_insertion_point(field_release:google.protobuf.testing.DefaultValueTestCases.repeated_nested_message)
  
  ::google::protobuf::testing::DoubleMessage* temp = repeated_nested_message_;
  repeated_nested_message_ = NULL;
  return temp;
}
void DefaultValueTestCases::set_allocated_repeated_nested_message(::google::protobuf::testing::DoubleMessage* repeated_nested_message) {
  delete repeated_nested_message_;
  repeated_nested_message_ = repeated_nested_message;
  if (repeated_nested_message) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_set_allocated:google.protobuf.testing.DefaultValueTestCases.repeated_nested_message)
}

// optional .google.protobuf.testing.DoubleMessage double_message_with_oneof = 7;
bool DefaultValueTestCases::has_double_message_with_oneof() const {
  return this != internal_default_instance() && double_message_with_oneof_ != NULL;
}
void DefaultValueTestCases::clear_double_message_with_oneof() {
  if (GetArenaNoVirtual() == NULL && double_message_with_oneof_ != NULL) delete double_message_with_oneof_;
  double_message_with_oneof_ = NULL;
}
const ::google::protobuf::testing::DoubleMessage& DefaultValueTestCases::double_message_with_oneof() const {
  // @@protoc_insertion_point(field_get:google.protobuf.testing.DefaultValueTestCases.double_message_with_oneof)
  return double_message_with_oneof_ != NULL ? *double_message_with_oneof_
                         : *::google::protobuf::testing::DoubleMessage::internal_default_instance();
}
::google::protobuf::testing::DoubleMessage* DefaultValueTestCases::mutable_double_message_with_oneof() {
  
  if (double_message_with_oneof_ == NULL) {
    double_message_with_oneof_ = new ::google::protobuf::testing::DoubleMessage;
  }
  // @@protoc_insertion_point(field_mutable:google.protobuf.testing.DefaultValueTestCases.double_message_with_oneof)
  return double_message_with_oneof_;
}
::google::protobuf::testing::DoubleMessage* DefaultValueTestCases::release_double_message_with_oneof() {
  // @@protoc_insertion_point(field_release:google.protobuf.testing.DefaultValueTestCases.double_message_with_oneof)
  
  ::google::protobuf::testing::DoubleMessage* temp = double_message_with_oneof_;
  double_message_with_oneof_ = NULL;
  return temp;
}
void DefaultValueTestCases::set_allocated_double_message_with_oneof(::google::protobuf::testing::DoubleMessage* double_message_with_oneof) {
  delete double_message_with_oneof_;
  double_message_with_oneof_ = double_message_with_oneof;
  if (double_message_with_oneof) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_set_allocated:google.protobuf.testing.DefaultValueTestCases.double_message_with_oneof)
}

// optional .google.protobuf.testing.StructMessage empty_struct = 201;
bool DefaultValueTestCases::has_empty_struct() const {
  return this != internal_default_instance() && empty_struct_ != NULL;
}
void DefaultValueTestCases::clear_empty_struct() {
  if (GetArenaNoVirtual() == NULL && empty_struct_ != NULL) delete empty_struct_;
  empty_struct_ = NULL;
}
const ::google::protobuf::testing::StructMessage& DefaultValueTestCases::empty_struct() const {
  // @@protoc_insertion_point(field_get:google.protobuf.testing.DefaultValueTestCases.empty_struct)
  return empty_struct_ != NULL ? *empty_struct_
                         : *::google::protobuf::testing::StructMessage::internal_default_instance();
}
::google::protobuf::testing::StructMessage* DefaultValueTestCases::mutable_empty_struct() {
  
  if (empty_struct_ == NULL) {
    empty_struct_ = new ::google::protobuf::testing::StructMessage;
  }
  // @@protoc_insertion_point(field_mutable:google.protobuf.testing.DefaultValueTestCases.empty_struct)
  return empty_struct_;
}
::google::protobuf::testing::StructMessage* DefaultValueTestCases::release_empty_struct() {
  // @@protoc_insertion_point(field_release:google.protobuf.testing.DefaultValueTestCases.empty_struct)
  
  ::google::protobuf::testing::StructMessage* temp = empty_struct_;
  empty_struct_ = NULL;
  return temp;
}
void DefaultValueTestCases::set_allocated_empty_struct(::google::protobuf::testing::StructMessage* empty_struct) {
  delete empty_struct_;
  empty_struct_ = empty_struct;
  if (empty_struct) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_set_allocated:google.protobuf.testing.DefaultValueTestCases.empty_struct)
}

// optional .google.protobuf.testing.StructMessage empty_struct2 = 202;
bool DefaultValueTestCases::has_empty_struct2() const {
  return this != internal_default_instance() && empty_struct2_ != NULL;
}
void DefaultValueTestCases::clear_empty_struct2() {
  if (GetArenaNoVirtual() == NULL && empty_struct2_ != NULL) delete empty_struct2_;
  empty_struct2_ = NULL;
}
const ::google::protobuf::testing::StructMessage& DefaultValueTestCases::empty_struct2() const {
  // @@protoc_insertion_point(field_get:google.protobuf.testing.DefaultValueTestCases.empty_struct2)
  return empty_struct2_ != NULL ? *empty_struct2_
                         : *::google::protobuf::testing::StructMessage::internal_default_instance();
}
::google::protobuf::testing::StructMessage* DefaultValueTestCases::mutable_empty_struct2() {
  
  if (empty_struct2_ == NULL) {
    empty_struct2_ = new ::google::protobuf::testing::StructMessage;
  }
  // @@protoc_insertion_point(field_mutable:google.protobuf.testing.DefaultValueTestCases.empty_struct2)
  return empty_struct2_;
}
::google::protobuf::testing::StructMessage* DefaultValueTestCases::release_empty_struct2() {
  // @@protoc_insertion_point(field_release:google.protobuf.testing.DefaultValueTestCases.empty_struct2)
  
  ::google::protobuf::testing::StructMessage* temp = empty_struct2_;
  empty_struct2_ = NULL;
  return temp;
}
void DefaultValueTestCases::set_allocated_empty_struct2(::google::protobuf::testing::StructMessage* empty_struct2) {
  delete empty_struct2_;
  empty_struct2_ = empty_struct2;
  if (empty_struct2) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_set_allocated:google.protobuf.testing.DefaultValueTestCases.empty_struct2)
}

// optional .google.protobuf.testing.StructMessage struct_with_null_value = 203;
bool DefaultValueTestCases::has_struct_with_null_value() const {
  return this != internal_default_instance() && struct_with_null_value_ != NULL;
}
void DefaultValueTestCases::clear_struct_with_null_value() {
  if (GetArenaNoVirtual() == NULL && struct_with_null_value_ != NULL) delete struct_with_null_value_;
  struct_with_null_value_ = NULL;
}
const ::google::protobuf::testing::StructMessage& DefaultValueTestCases::struct_with_null_value() const {
  // @@protoc_insertion_point(field_get:google.protobuf.testing.DefaultValueTestCases.struct_with_null_value)
  return struct_with_null_value_ != NULL ? *struct_with_null_value_
                         : *::google::protobuf::testing::StructMessage::internal_default_instance();
}
::google::protobuf::testing::StructMessage* DefaultValueTestCases::mutable_struct_with_null_value() {
  
  if (struct_with_null_value_ == NULL) {
    struct_with_null_value_ = new ::google::protobuf::testing::StructMessage;
  }
  // @@protoc_insertion_point(field_mutable:google.protobuf.testing.DefaultValueTestCases.struct_with_null_value)
  return struct_with_null_value_;
}
::google::protobuf::testing::StructMessage* DefaultValueTestCases::release_struct_with_null_value() {
  // @@protoc_insertion_point(field_release:google.protobuf.testing.DefaultValueTestCases.struct_with_null_value)
  
  ::google::protobuf::testing::StructMessage* temp = struct_with_null_value_;
  struct_with_null_value_ = NULL;
  return temp;
}
void DefaultValueTestCases::set_allocated_struct_with_null_value(::google::protobuf::testing::StructMessage* struct_with_null_value) {
  delete struct_with_null_value_;
  struct_with_null_value_ = struct_with_null_value;
  if (struct_with_null_value) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_set_allocated:google.protobuf.testing.DefaultValueTestCases.struct_with_null_value)
}

// optional .google.protobuf.testing.StructMessage struct_with_values = 204;
bool DefaultValueTestCases::has_struct_with_values() const {
  return this != internal_default_instance() && struct_with_values_ != NULL;
}
void DefaultValueTestCases::clear_struct_with_values() {
  if (GetArenaNoVirtual() == NULL && struct_with_values_ != NULL) delete struct_with_values_;
  struct_with_values_ = NULL;
}
const ::google::protobuf::testing::StructMessage& DefaultValueTestCases::struct_with_values() const {
  // @@protoc_insertion_point(field_get:google.protobuf.testing.DefaultValueTestCases.struct_with_values)
  return struct_with_values_ != NULL ? *struct_with_values_
                         : *::google::protobuf::testing::StructMessage::internal_default_instance();
}
::google::protobuf::testing::StructMessage* DefaultValueTestCases::mutable_struct_with_values() {
  
  if (struct_with_values_ == NULL) {
    struct_with_values_ = new ::google::protobuf::testing::StructMessage;
  }
  // @@protoc_insertion_point(field_mutable:google.protobuf.testing.DefaultValueTestCases.struct_with_values)
  return struct_with_values_;
}
::google::protobuf::testing::StructMessage* DefaultValueTestCases::release_struct_with_values() {
  // @@protoc_insertion_point(field_release:google.protobuf.testing.DefaultValueTestCases.struct_with_values)
  
  ::google::protobuf::testing::StructMessage* temp = struct_with_values_;
  struct_with_values_ = NULL;
  return temp;
}
void DefaultValueTestCases::set_allocated_struct_with_values(::google::protobuf::testing::StructMessage* struct_with_values) {
  delete struct_with_values_;
  struct_with_values_ = struct_with_values;
  if (struct_with_values) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_set_allocated:google.protobuf.testing.DefaultValueTestCases.struct_with_values)
}

// optional .google.protobuf.testing.StructMessage struct_with_nested_struct = 205;
bool DefaultValueTestCases::has_struct_with_nested_struct() const {
  return this != internal_default_instance() && struct_with_nested_struct_ != NULL;
}
void DefaultValueTestCases::clear_struct_with_nested_struct() {
  if (GetArenaNoVirtual() == NULL && struct_with_nested_struct_ != NULL) delete struct_with_nested_struct_;
  struct_with_nested_struct_ = NULL;
}
const ::google::protobuf::testing::StructMessage& DefaultValueTestCases::struct_with_nested_struct() const {
  // @@protoc_insertion_point(field_get:google.protobuf.testing.DefaultValueTestCases.struct_with_nested_struct)
  return struct_with_nested_struct_ != NULL ? *struct_with_nested_struct_
                         : *::google::protobuf::testing::StructMessage::internal_default_instance();
}
::google::protobuf::testing::StructMessage* DefaultValueTestCases::mutable_struct_with_nested_struct() {
  
  if (struct_with_nested_struct_ == NULL) {
    struct_with_nested_struct_ = new ::google::protobuf::testing::StructMessage;
  }
  // @@protoc_insertion_point(field_mutable:google.protobuf.testing.DefaultValueTestCases.struct_with_nested_struct)
  return struct_with_nested_struct_;
}
::google::protobuf::testing::StructMessage* DefaultValueTestCases::release_struct_with_nested_struct() {
  // @@protoc_insertion_point(field_release:google.protobuf.testing.DefaultValueTestCases.struct_with_nested_struct)
  
  ::google::protobuf::testing::StructMessage* temp = struct_with_nested_struct_;
  struct_with_nested_struct_ = NULL;
  return temp;
}
void DefaultValueTestCases::set_allocated_struct_with_nested_struct(::google::protobuf::testing::StructMessage* struct_with_nested_struct) {
  delete struct_with_nested_struct_;
  struct_with_nested_struct_ = struct_with_nested_struct;
  if (struct_with_nested_struct) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_set_allocated:google.protobuf.testing.DefaultValueTestCases.struct_with_nested_struct)
}

// optional .google.protobuf.testing.StructMessage struct_with_nested_list = 206;
bool DefaultValueTestCases::has_struct_with_nested_list() const {
  return this != internal_default_instance() && struct_with_nested_list_ != NULL;
}
void DefaultValueTestCases::clear_struct_with_nested_list() {
  if (GetArenaNoVirtual() == NULL && struct_with_nested_list_ != NULL) delete struct_with_nested_list_;
  struct_with_nested_list_ = NULL;
}
const ::google::protobuf::testing::StructMessage& DefaultValueTestCases::struct_with_nested_list() const {
  // @@protoc_insertion_point(field_get:google.protobuf.testing.DefaultValueTestCases.struct_with_nested_list)
  return struct_with_nested_list_ != NULL ? *struct_with_nested_list_
                         : *::google::protobuf::testing::StructMessage::internal_default_instance();
}
::google::protobuf::testing::StructMessage* DefaultValueTestCases::mutable_struct_with_nested_list() {
  
  if (struct_with_nested_list_ == NULL) {
    struct_with_nested_list_ = new ::google::protobuf::testing::StructMessage;
  }
  // @@protoc_insertion_point(field_mutable:google.protobuf.testing.DefaultValueTestCases.struct_with_nested_list)
  return struct_with_nested_list_;
}
::google::protobuf::testing::StructMessage* DefaultValueTestCases::release_struct_with_nested_list() {
  // @@protoc_insertion_point(field_release:google.protobuf.testing.DefaultValueTestCases.struct_with_nested_list)
  
  ::google::protobuf::testing::StructMessage* temp = struct_with_nested_list_;
  struct_with_nested_list_ = NULL;
  return temp;
}
void DefaultValueTestCases::set_allocated_struct_with_nested_list(::google::protobuf::testing::StructMessage* struct_with_nested_list) {
  delete struct_with_nested_list_;
  struct_with_nested_list_ = struct_with_nested_list;
  if (struct_with_nested_list) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_set_allocated:google.protobuf.testing.DefaultValueTestCases.struct_with_nested_list)
}

// optional .google.protobuf.testing.StructMessage struct_with_list_of_nulls = 207;
bool DefaultValueTestCases::has_struct_with_list_of_nulls() const {
  return this != internal_default_instance() && struct_with_list_of_nulls_ != NULL;
}
void DefaultValueTestCases::clear_struct_with_list_of_nulls() {
  if (GetArenaNoVirtual() == NULL && struct_with_list_of_nulls_ != NULL) delete struct_with_list_of_nulls_;
  struct_with_list_of_nulls_ = NULL;
}
const ::google::protobuf::testing::StructMessage& DefaultValueTestCases::struct_with_list_of_nulls() const {
  // @@protoc_insertion_point(field_get:google.protobuf.testing.DefaultValueTestCases.struct_with_list_of_nulls)
  return struct_with_list_of_nulls_ != NULL ? *struct_with_list_of_nulls_
                         : *::google::protobuf::testing::StructMessage::internal_default_instance();
}
::google::protobuf::testing::StructMessage* DefaultValueTestCases::mutable_struct_with_list_of_nulls() {
  
  if (struct_with_list_of_nulls_ == NULL) {
    struct_with_list_of_nulls_ = new ::google::protobuf::testing::StructMessage;
  }
  // @@protoc_insertion_point(field_mutable:google.protobuf.testing.DefaultValueTestCases.struct_with_list_of_nulls)
  return struct_with_list_of_nulls_;
}
::google::protobuf::testing::StructMessage* DefaultValueTestCases::release_struct_with_list_of_nulls() {
  // @@protoc_insertion_point(field_release:google.protobuf.testing.DefaultValueTestCases.struct_with_list_of_nulls)
  
  ::google::protobuf::testing::StructMessage* temp = struct_with_list_of_nulls_;
  struct_with_list_of_nulls_ = NULL;
  return temp;
}
void DefaultValueTestCases::set_allocated_struct_with_list_of_nulls(::google::protobuf::testing::StructMessage* struct_with_list_of_nulls) {
  delete struct_with_list_of_nulls_;
  struct_with_list_of_nulls_ = struct_with_list_of_nulls;
  if (struct_with_list_of_nulls) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_set_allocated:google.protobuf.testing.DefaultValueTestCases.struct_with_list_of_nulls)
}

// optional .google.protobuf.testing.StructMessage struct_with_list_of_lists = 208;
bool DefaultValueTestCases::has_struct_with_list_of_lists() const {
  return this != internal_default_instance() && struct_with_list_of_lists_ != NULL;
}
void DefaultValueTestCases::clear_struct_with_list_of_lists() {
  if (GetArenaNoVirtual() == NULL && struct_with_list_of_lists_ != NULL) delete struct_with_list_of_lists_;
  struct_with_list_of_lists_ = NULL;
}
const ::google::protobuf::testing::StructMessage& DefaultValueTestCases::struct_with_list_of_lists() const {
  // @@protoc_insertion_point(field_get:google.protobuf.testing.DefaultValueTestCases.struct_with_list_of_lists)
  return struct_with_list_of_lists_ != NULL ? *struct_with_list_of_lists_
                         : *::google::protobuf::testing::StructMessage::internal_default_instance();
}
::google::protobuf::testing::StructMessage* DefaultValueTestCases::mutable_struct_with_list_of_lists() {
  
  if (struct_with_list_of_lists_ == NULL) {
    struct_with_list_of_lists_ = new ::google::protobuf::testing::StructMessage;
  }
  // @@protoc_insertion_point(field_mutable:google.protobuf.testing.DefaultValueTestCases.struct_with_list_of_lists)
  return struct_with_list_of_lists_;
}
::google::protobuf::testing::StructMessage* DefaultValueTestCases::release_struct_with_list_of_lists() {
  // @@protoc_insertion_point(field_release:google.protobuf.testing.DefaultValueTestCases.struct_with_list_of_lists)
  
  ::google::protobuf::testing::StructMessage* temp = struct_with_list_of_lists_;
  struct_with_list_of_lists_ = NULL;
  return temp;
}
void DefaultValueTestCases::set_allocated_struct_with_list_of_lists(::google::protobuf::testing::StructMessage* struct_with_list_of_lists) {
  delete struct_with_list_of_lists_;
  struct_with_list_of_lists_ = struct_with_list_of_lists;
  if (struct_with_list_of_lists) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_set_allocated:google.protobuf.testing.DefaultValueTestCases.struct_with_list_of_lists)
}

// optional .google.protobuf.testing.StructMessage struct_with_list_of_structs = 209;
bool DefaultValueTestCases::has_struct_with_list_of_structs() const {
  return this != internal_default_instance() && struct_with_list_of_structs_ != NULL;
}
void DefaultValueTestCases::clear_struct_with_list_of_structs() {
  if (GetArenaNoVirtual() == NULL && struct_with_list_of_structs_ != NULL) delete struct_with_list_of_structs_;
  struct_with_list_of_structs_ = NULL;
}
const ::google::protobuf::testing::StructMessage& DefaultValueTestCases::struct_with_list_of_structs() const {
  // @@protoc_insertion_point(field_get:google.protobuf.testing.DefaultValueTestCases.struct_with_list_of_structs)
  return struct_with_list_of_structs_ != NULL ? *struct_with_list_of_structs_
                         : *::google::protobuf::testing::StructMessage::internal_default_instance();
}
::google::protobuf::testing::StructMessage* DefaultValueTestCases::mutable_struct_with_list_of_structs() {
  
  if (struct_with_list_of_structs_ == NULL) {
    struct_with_list_of_structs_ = new ::google::protobuf::testing::StructMessage;
  }
  // @@protoc_insertion_point(field_mutable:google.protobuf.testing.DefaultValueTestCases.struct_with_list_of_structs)
  return struct_with_list_of_structs_;
}
::google::protobuf::testing::StructMessage* DefaultValueTestCases::release_struct_with_list_of_structs() {
  // @@protoc_insertion_point(field_release:google.protobuf.testing.DefaultValueTestCases.struct_with_list_of_structs)
  
  ::google::protobuf::testing::StructMessage* temp = struct_with_list_of_structs_;
  struct_with_list_of_structs_ = NULL;
  return temp;
}
void DefaultValueTestCases::set_allocated_struct_with_list_of_structs(::google::protobuf::testing::StructMessage* struct_with_list_of_structs) {
  delete struct_with_list_of_structs_;
  struct_with_list_of_structs_ = struct_with_list_of_structs;
  if (struct_with_list_of_structs) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_set_allocated:google.protobuf.testing.DefaultValueTestCases.struct_with_list_of_structs)
}

// optional .google.protobuf.Struct top_level_struct = 210;
bool DefaultValueTestCases::has_top_level_struct() const {
  return this != internal_default_instance() && top_level_struct_ != NULL;
}
void DefaultValueTestCases::clear_top_level_struct() {
  if (GetArenaNoVirtual() == NULL && top_level_struct_ != NULL) delete top_level_struct_;
  top_level_struct_ = NULL;
}
const ::google::protobuf::Struct& DefaultValueTestCases::top_level_struct() const {
  // @@protoc_insertion_point(field_get:google.protobuf.testing.DefaultValueTestCases.top_level_struct)
  return top_level_struct_ != NULL ? *top_level_struct_
                         : *::google::protobuf::Struct::internal_default_instance();
}
::google::protobuf::Struct* DefaultValueTestCases::mutable_top_level_struct() {
  
  if (top_level_struct_ == NULL) {
    top_level_struct_ = new ::google::protobuf::Struct;
  }
  // @@protoc_insertion_point(field_mutable:google.protobuf.testing.DefaultValueTestCases.top_level_struct)
  return top_level_struct_;
}
::google::protobuf::Struct* DefaultValueTestCases::release_top_level_struct() {
  // @@protoc_insertion_point(field_release:google.protobuf.testing.DefaultValueTestCases.top_level_struct)
  
  ::google::protobuf::Struct* temp = top_level_struct_;
  top_level_struct_ = NULL;
  return temp;
}
void DefaultValueTestCases::set_allocated_top_level_struct(::google::protobuf::Struct* top_level_struct) {
  delete top_level_struct_;
  if (top_level_struct != NULL && top_level_struct->GetArena() != NULL) {
    ::google::protobuf::Struct* new_top_level_struct = new ::google::protobuf::Struct;
    new_top_level_struct->CopyFrom(*top_level_struct);
    top_level_struct = new_top_level_struct;
  }
  top_level_struct_ = top_level_struct;
  if (top_level_struct) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_set_allocated:google.protobuf.testing.DefaultValueTestCases.top_level_struct)
}

// optional .google.protobuf.testing.ValueMessage value_wrapper_simple = 212;
bool DefaultValueTestCases::has_value_wrapper_simple() const {
  return this != internal_default_instance() && value_wrapper_simple_ != NULL;
}
void DefaultValueTestCases::clear_value_wrapper_simple() {
  if (GetArenaNoVirtual() == NULL && value_wrapper_simple_ != NULL) delete value_wrapper_simple_;
  value_wrapper_simple_ = NULL;
}
const ::google::protobuf::testing::ValueMessage& DefaultValueTestCases::value_wrapper_simple() const {
  // @@protoc_insertion_point(field_get:google.protobuf.testing.DefaultValueTestCases.value_wrapper_simple)
  return value_wrapper_simple_ != NULL ? *value_wrapper_simple_
                         : *::google::protobuf::testing::ValueMessage::internal_default_instance();
}
::google::protobuf::testing::ValueMessage* DefaultValueTestCases::mutable_value_wrapper_simple() {
  
  if (value_wrapper_simple_ == NULL) {
    value_wrapper_simple_ = new ::google::protobuf::testing::ValueMessage;
  }
  // @@protoc_insertion_point(field_mutable:google.protobuf.testing.DefaultValueTestCases.value_wrapper_simple)
  return value_wrapper_simple_;
}
::google::protobuf::testing::ValueMessage* DefaultValueTestCases::release_value_wrapper_simple() {
  // @@protoc_insertion_point(field_release:google.protobuf.testing.DefaultValueTestCases.value_wrapper_simple)
  
  ::google::protobuf::testing::ValueMessage* temp = value_wrapper_simple_;
  value_wrapper_simple_ = NULL;
  return temp;
}
void DefaultValueTestCases::set_allocated_value_wrapper_simple(::google::protobuf::testing::ValueMessage* value_wrapper_simple) {
  delete value_wrapper_simple_;
  value_wrapper_simple_ = value_wrapper_simple;
  if (value_wrapper_simple) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_set_allocated:google.protobuf.testing.DefaultValueTestCases.value_wrapper_simple)
}

// optional .google.protobuf.testing.ValueMessage value_wrapper_with_struct = 213;
bool DefaultValueTestCases::has_value_wrapper_with_struct() const {
  return this != internal_default_instance() && value_wrapper_with_struct_ != NULL;
}
void DefaultValueTestCases::clear_value_wrapper_with_struct() {
  if (GetArenaNoVirtual() == NULL && value_wrapper_with_struct_ != NULL) delete value_wrapper_with_struct_;
  value_wrapper_with_struct_ = NULL;
}
const ::google::protobuf::testing::ValueMessage& DefaultValueTestCases::value_wrapper_with_struct() const {
  // @@protoc_insertion_point(field_get:google.protobuf.testing.DefaultValueTestCases.value_wrapper_with_struct)
  return value_wrapper_with_struct_ != NULL ? *value_wrapper_with_struct_
                         : *::google::protobuf::testing::ValueMessage::internal_default_instance();
}
::google::protobuf::testing::ValueMessage* DefaultValueTestCases::mutable_value_wrapper_with_struct() {
  
  if (value_wrapper_with_struct_ == NULL) {
    value_wrapper_with_struct_ = new ::google::protobuf::testing::ValueMessage;
  }
  // @@protoc_insertion_point(field_mutable:google.protobuf.testing.DefaultValueTestCases.value_wrapper_with_struct)
  return value_wrapper_with_struct_;
}
::google::protobuf::testing::ValueMessage* DefaultValueTestCases::release_value_wrapper_with_struct() {
  // @@protoc_insertion_point(field_release:google.protobuf.testing.DefaultValueTestCases.value_wrapper_with_struct)
  
  ::google::protobuf::testing::ValueMessage* temp = value_wrapper_with_struct_;
  value_wrapper_with_struct_ = NULL;
  return temp;
}
void DefaultValueTestCases::set_allocated_value_wrapper_with_struct(::google::protobuf::testing::ValueMessage* value_wrapper_with_struct) {
  delete value_wrapper_with_struct_;
  value_wrapper_with_struct_ = value_wrapper_with_struct;
  if (value_wrapper_with_struct) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_set_allocated:google.protobuf.testing.DefaultValueTestCases.value_wrapper_with_struct)
}

// optional .google.protobuf.testing.ValueMessage value_wrapper_with_list = 214;
bool DefaultValueTestCases::has_value_wrapper_with_list() const {
  return this != internal_default_instance() && value_wrapper_with_list_ != NULL;
}
void DefaultValueTestCases::clear_value_wrapper_with_list() {
  if (GetArenaNoVirtual() == NULL && value_wrapper_with_list_ != NULL) delete value_wrapper_with_list_;
  value_wrapper_with_list_ = NULL;
}
const ::google::protobuf::testing::ValueMessage& DefaultValueTestCases::value_wrapper_with_list() const {
  // @@protoc_insertion_point(field_get:google.protobuf.testing.DefaultValueTestCases.value_wrapper_with_list)
  return value_wrapper_with_list_ != NULL ? *value_wrapper_with_list_
                         : *::google::protobuf::testing::ValueMessage::internal_default_instance();
}
::google::protobuf::testing::ValueMessage* DefaultValueTestCases::mutable_value_wrapper_with_list() {
  
  if (value_wrapper_with_list_ == NULL) {
    value_wrapper_with_list_ = new ::google::protobuf::testing::ValueMessage;
  }
  // @@protoc_insertion_point(field_mutable:google.protobuf.testing.DefaultValueTestCases.value_wrapper_with_list)
  return value_wrapper_with_list_;
}
::google::protobuf::testing::ValueMessage* DefaultValueTestCases::release_value_wrapper_with_list() {
  // @@protoc_insertion_point(field_release:google.protobuf.testing.DefaultValueTestCases.value_wrapper_with_list)
  
  ::google::protobuf::testing::ValueMessage* temp = value_wrapper_with_list_;
  value_wrapper_with_list_ = NULL;
  return temp;
}
void DefaultValueTestCases::set_allocated_value_wrapper_with_list(::google::protobuf::testing::ValueMessage* value_wrapper_with_list) {
  delete value_wrapper_with_list_;
  value_wrapper_with_list_ = value_wrapper_with_list;
  if (value_wrapper_with_list) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_set_allocated:google.protobuf.testing.DefaultValueTestCases.value_wrapper_with_list)
}

// optional .google.protobuf.testing.ListValueMessage list_value_wrapper = 215;
bool DefaultValueTestCases::has_list_value_wrapper() const {
  return this != internal_default_instance() && list_value_wrapper_ != NULL;
}
void DefaultValueTestCases::clear_list_value_wrapper() {
  if (GetArenaNoVirtual() == NULL && list_value_wrapper_ != NULL) delete list_value_wrapper_;
  list_value_wrapper_ = NULL;
}
const ::google::protobuf::testing::ListValueMessage& DefaultValueTestCases::list_value_wrapper() const {
  // @@protoc_insertion_point(field_get:google.protobuf.testing.DefaultValueTestCases.list_value_wrapper)
  return list_value_wrapper_ != NULL ? *list_value_wrapper_
                         : *::google::protobuf::testing::ListValueMessage::internal_default_instance();
}
::google::protobuf::testing::ListValueMessage* DefaultValueTestCases::mutable_list_value_wrapper() {
  
  if (list_value_wrapper_ == NULL) {
    list_value_wrapper_ = new ::google::protobuf::testing::ListValueMessage;
  }
  // @@protoc_insertion_point(field_mutable:google.protobuf.testing.DefaultValueTestCases.list_value_wrapper)
  return list_value_wrapper_;
}
::google::protobuf::testing::ListValueMessage* DefaultValueTestCases::release_list_value_wrapper() {
  // @@protoc_insertion_point(field_release:google.protobuf.testing.DefaultValueTestCases.list_value_wrapper)
  
  ::google::protobuf::testing::ListValueMessage* temp = list_value_wrapper_;
  list_value_wrapper_ = NULL;
  return temp;
}
void DefaultValueTestCases::set_allocated_list_value_wrapper(::google::protobuf::testing::ListValueMessage* list_value_wrapper) {
  delete list_value_wrapper_;
  list_value_wrapper_ = list_value_wrapper;
  if (list_value_wrapper) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_set_allocated:google.protobuf.testing.DefaultValueTestCases.list_value_wrapper)
}

// optional .google.protobuf.Value top_level_value_simple = 216;
bool DefaultValueTestCases::has_top_level_value_simple() const {
  return this != internal_default_instance() && top_level_value_simple_ != NULL;
}
void DefaultValueTestCases::clear_top_level_value_simple() {
  if (GetArenaNoVirtual() == NULL && top_level_value_simple_ != NULL) delete top_level_value_simple_;
  top_level_value_simple_ = NULL;
}
const ::google::protobuf::Value& DefaultValueTestCases::top_level_value_simple() const {
  // @@protoc_insertion_point(field_get:google.protobuf.testing.DefaultValueTestCases.top_level_value_simple)
  return top_level_value_simple_ != NULL ? *top_level_value_simple_
                         : *::google::protobuf::Value::internal_default_instance();
}
::google::protobuf::Value* DefaultValueTestCases::mutable_top_level_value_simple() {
  
  if (top_level_value_simple_ == NULL) {
    top_level_value_simple_ = new ::google::protobuf::Value;
  }
  // @@protoc_insertion_point(field_mutable:google.protobuf.testing.DefaultValueTestCases.top_level_value_simple)
  return top_level_value_simple_;
}
::google::protobuf::Value* DefaultValueTestCases::release_top_level_value_simple() {
  // @@protoc_insertion_point(field_release:google.protobuf.testing.DefaultValueTestCases.top_level_value_simple)
  
  ::google::protobuf::Value* temp = top_level_value_simple_;
  top_level_value_simple_ = NULL;
  return temp;
}
void DefaultValueTestCases::set_allocated_top_level_value_simple(::google::protobuf::Value* top_level_value_simple) {
  delete top_level_value_simple_;
  if (top_level_value_simple != NULL && top_level_value_simple->GetArena() != NULL) {
    ::google::protobuf::Value* new_top_level_value_simple = new ::google::protobuf::Value;
    new_top_level_value_simple->CopyFrom(*top_level_value_simple);
    top_level_value_simple = new_top_level_value_simple;
  }
  top_level_value_simple_ = top_level_value_simple;
  if (top_level_value_simple) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_set_allocated:google.protobuf.testing.DefaultValueTestCases.top_level_value_simple)
}

// optional .google.protobuf.Value top_level_value_with_struct = 217;
bool DefaultValueTestCases::has_top_level_value_with_struct() const {
  return this != internal_default_instance() && top_level_value_with_struct_ != NULL;
}
void DefaultValueTestCases::clear_top_level_value_with_struct() {
  if (GetArenaNoVirtual() == NULL && top_level_value_with_struct_ != NULL) delete top_level_value_with_struct_;
  top_level_value_with_struct_ = NULL;
}
const ::google::protobuf::Value& DefaultValueTestCases::top_level_value_with_struct() const {
  // @@protoc_insertion_point(field_get:google.protobuf.testing.DefaultValueTestCases.top_level_value_with_struct)
  return top_level_value_with_struct_ != NULL ? *top_level_value_with_struct_
                         : *::google::protobuf::Value::internal_default_instance();
}
::google::protobuf::Value* DefaultValueTestCases::mutable_top_level_value_with_struct() {
  
  if (top_level_value_with_struct_ == NULL) {
    top_level_value_with_struct_ = new ::google::protobuf::Value;
  }
  // @@protoc_insertion_point(field_mutable:google.protobuf.testing.DefaultValueTestCases.top_level_value_with_struct)
  return top_level_value_with_struct_;
}
::google::protobuf::Value* DefaultValueTestCases::release_top_level_value_with_struct() {
  // @@protoc_insertion_point(field_release:google.protobuf.testing.DefaultValueTestCases.top_level_value_with_struct)
  
  ::google::protobuf::Value* temp = top_level_value_with_struct_;
  top_level_value_with_struct_ = NULL;
  return temp;
}
void DefaultValueTestCases::set_allocated_top_level_value_with_struct(::google::protobuf::Value* top_level_value_with_struct) {
  delete top_level_value_with_struct_;
  if (top_level_value_with_struct != NULL && top_level_value_with_struct->GetArena() != NULL) {
    ::google::protobuf::Value* new_top_level_value_with_struct = new ::google::protobuf::Value;
    new_top_level_value_with_struct->CopyFrom(*top_level_value_with_struct);
    top_level_value_with_struct = new_top_level_value_with_struct;
  }
  top_level_value_with_struct_ = top_level_value_with_struct;
  if (top_level_value_with_struct) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_set_allocated:google.protobuf.testing.DefaultValueTestCases.top_level_value_with_struct)
}

// optional .google.protobuf.Value top_level_value_with_list = 218;
bool DefaultValueTestCases::has_top_level_value_with_list() const {
  return this != internal_default_instance() && top_level_value_with_list_ != NULL;
}
void DefaultValueTestCases::clear_top_level_value_with_list() {
  if (GetArenaNoVirtual() == NULL && top_level_value_with_list_ != NULL) delete top_level_value_with_list_;
  top_level_value_with_list_ = NULL;
}
const ::google::protobuf::Value& DefaultValueTestCases::top_level_value_with_list() const {
  // @@protoc_insertion_point(field_get:google.protobuf.testing.DefaultValueTestCases.top_level_value_with_list)
  return top_level_value_with_list_ != NULL ? *top_level_value_with_list_
                         : *::google::protobuf::Value::internal_default_instance();
}
::google::protobuf::Value* DefaultValueTestCases::mutable_top_level_value_with_list() {
  
  if (top_level_value_with_list_ == NULL) {
    top_level_value_with_list_ = new ::google::protobuf::Value;
  }
  // @@protoc_insertion_point(field_mutable:google.protobuf.testing.DefaultValueTestCases.top_level_value_with_list)
  return top_level_value_with_list_;
}
::google::protobuf::Value* DefaultValueTestCases::release_top_level_value_with_list() {
  // @@protoc_insertion_point(field_release:google.protobuf.testing.DefaultValueTestCases.top_level_value_with_list)
  
  ::google::protobuf::Value* temp = top_level_value_with_list_;
  top_level_value_with_list_ = NULL;
  return temp;
}
void DefaultValueTestCases::set_allocated_top_level_value_with_list(::google::protobuf::Value* top_level_value_with_list) {
  delete top_level_value_with_list_;
  if (top_level_value_with_list != NULL && top_level_value_with_list->GetArena() != NULL) {
    ::google::protobuf::Value* new_top_level_value_with_list = new ::google::protobuf::Value;
    new_top_level_value_with_list->CopyFrom(*top_level_value_with_list);
    top_level_value_with_list = new_top_level_value_with_list;
  }
  top_level_value_with_list_ = top_level_value_with_list;
  if (top_level_value_with_list) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_set_allocated:google.protobuf.testing.DefaultValueTestCases.top_level_value_with_list)
}

// optional .google.protobuf.ListValue top_level_listvalue = 219;
bool DefaultValueTestCases::has_top_level_listvalue() const {
  return this != internal_default_instance() && top_level_listvalue_ != NULL;
}
void DefaultValueTestCases::clear_top_level_listvalue() {
  if (GetArenaNoVirtual() == NULL && top_level_listvalue_ != NULL) delete top_level_listvalue_;
  top_level_listvalue_ = NULL;
}
const ::google::protobuf::ListValue& DefaultValueTestCases::top_level_listvalue() const {
  // @@protoc_insertion_point(field_get:google.protobuf.testing.DefaultValueTestCases.top_level_listvalue)
  return top_level_listvalue_ != NULL ? *top_level_listvalue_
                         : *::google::protobuf::ListValue::internal_default_instance();
}
::google::protobuf::ListValue* DefaultValueTestCases::mutable_top_level_listvalue() {
  
  if (top_level_listvalue_ == NULL) {
    top_level_listvalue_ = new ::google::protobuf::ListValue;
  }
  // @@protoc_insertion_point(field_mutable:google.protobuf.testing.DefaultValueTestCases.top_level_listvalue)
  return top_level_listvalue_;
}
::google::protobuf::ListValue* DefaultValueTestCases::release_top_level_listvalue() {
  // @@protoc_insertion_point(field_release:google.protobuf.testing.DefaultValueTestCases.top_level_listvalue)
  
  ::google::protobuf::ListValue* temp = top_level_listvalue_;
  top_level_listvalue_ = NULL;
  return temp;
}
void DefaultValueTestCases::set_allocated_top_level_listvalue(::google::protobuf::ListValue* top_level_listvalue) {
  delete top_level_listvalue_;
  if (top_level_listvalue != NULL && top_level_listvalue->GetArena() != NULL) {
    ::google::protobuf::ListValue* new_top_level_listvalue = new ::google::protobuf::ListValue;
    new_top_level_listvalue->CopyFrom(*top_level_listvalue);
    top_level_listvalue = new_top_level_listvalue;
  }
  top_level_listvalue_ = top_level_listvalue;
  if (top_level_listvalue) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_set_allocated:google.protobuf.testing.DefaultValueTestCases.top_level_listvalue)
}

// optional .google.protobuf.testing.AnyMessage empty_any = 301;
bool DefaultValueTestCases::has_empty_any() const {
  return this != internal_default_instance() && empty_any_ != NULL;
}
void DefaultValueTestCases::clear_empty_any() {
  if (GetArenaNoVirtual() == NULL && empty_any_ != NULL) delete empty_any_;
  empty_any_ = NULL;
}
const ::google::protobuf::testing::AnyMessage& DefaultValueTestCases::empty_any() const {
  // @@protoc_insertion_point(field_get:google.protobuf.testing.DefaultValueTestCases.empty_any)
  return empty_any_ != NULL ? *empty_any_
                         : *::google::protobuf::testing::AnyMessage::internal_default_instance();
}
::google::protobuf::testing::AnyMessage* DefaultValueTestCases::mutable_empty_any() {
  
  if (empty_any_ == NULL) {
    empty_any_ = new ::google::protobuf::testing::AnyMessage;
  }
  // @@protoc_insertion_point(field_mutable:google.protobuf.testing.DefaultValueTestCases.empty_any)
  return empty_any_;
}
::google::protobuf::testing::AnyMessage* DefaultValueTestCases::release_empty_any() {
  // @@protoc_insertion_point(field_release:google.protobuf.testing.DefaultValueTestCases.empty_any)
  
  ::google::protobuf::testing::AnyMessage* temp = empty_any_;
  empty_any_ = NULL;
  return temp;
}
void DefaultValueTestCases::set_allocated_empty_any(::google::protobuf::testing::AnyMessage* empty_any) {
  delete empty_any_;
  empty_any_ = empty_any;
  if (empty_any) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_set_allocated:google.protobuf.testing.DefaultValueTestCases.empty_any)
}

// optional .google.protobuf.testing.AnyMessage type_only_any = 302;
bool DefaultValueTestCases::has_type_only_any() const {
  return this != internal_default_instance() && type_only_any_ != NULL;
}
void DefaultValueTestCases::clear_type_only_any() {
  if (GetArenaNoVirtual() == NULL && type_only_any_ != NULL) delete type_only_any_;
  type_only_any_ = NULL;
}
const ::google::protobuf::testing::AnyMessage& DefaultValueTestCases::type_only_any() const {
  // @@protoc_insertion_point(field_get:google.protobuf.testing.DefaultValueTestCases.type_only_any)
  return type_only_any_ != NULL ? *type_only_any_
                         : *::google::protobuf::testing::AnyMessage::internal_default_instance();
}
::google::protobuf::testing::AnyMessage* DefaultValueTestCases::mutable_type_only_any() {
  
  if (type_only_any_ == NULL) {
    type_only_any_ = new ::google::protobuf::testing::AnyMessage;
  }
  // @@protoc_insertion_point(field_mutable:google.protobuf.testing.DefaultValueTestCases.type_only_any)
  return type_only_any_;
}
::google::protobuf::testing::AnyMessage* DefaultValueTestCases::release_type_only_any() {
  // @@protoc_insertion_point(field_release:google.protobuf.testing.DefaultValueTestCases.type_only_any)
  
  ::google::protobuf::testing::AnyMessage* temp = type_only_any_;
  type_only_any_ = NULL;
  return temp;
}
void DefaultValueTestCases::set_allocated_type_only_any(::google::protobuf::testing::AnyMessage* type_only_any) {
  delete type_only_any_;
  type_only_any_ = type_only_any;
  if (type_only_any) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_set_allocated:google.protobuf.testing.DefaultValueTestCases.type_only_any)
}

// optional .google.protobuf.testing.AnyMessage recursive_any = 303;
bool DefaultValueTestCases::has_recursive_any() const {
  return this != internal_default_instance() && recursive_any_ != NULL;
}
void DefaultValueTestCases::clear_recursive_any() {
  if (GetArenaNoVirtual() == NULL && recursive_any_ != NULL) delete recursive_any_;
  recursive_any_ = NULL;
}
const ::google::protobuf::testing::AnyMessage& DefaultValueTestCases::recursive_any() const {
  // @@protoc_insertion_point(field_get:google.protobuf.testing.DefaultValueTestCases.recursive_any)
  return recursive_any_ != NULL ? *recursive_any_
                         : *::google::protobuf::testing::AnyMessage::internal_default_instance();
}
::google::protobuf::testing::AnyMessage* DefaultValueTestCases::mutable_recursive_any() {
  
  if (recursive_any_ == NULL) {
    recursive_any_ = new ::google::protobuf::testing::AnyMessage;
  }
  // @@protoc_insertion_point(field_mutable:google.protobuf.testing.DefaultValueTestCases.recursive_any)
  return recursive_any_;
}
::google::protobuf::testing::AnyMessage* DefaultValueTestCases::release_recursive_any() {
  // @@protoc_insertion_point(field_release:google.protobuf.testing.DefaultValueTestCases.recursive_any)
  
  ::google::protobuf::testing::AnyMessage* temp = recursive_any_;
  recursive_any_ = NULL;
  return temp;
}
void DefaultValueTestCases::set_allocated_recursive_any(::google::protobuf::testing::AnyMessage* recursive_any) {
  delete recursive_any_;
  recursive_any_ = recursive_any;
  if (recursive_any) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_set_allocated:google.protobuf.testing.DefaultValueTestCases.recursive_any)
}

// optional .google.protobuf.testing.AnyMessage any_with_message_value = 304;
bool DefaultValueTestCases::has_any_with_message_value() const {
  return this != internal_default_instance() && any_with_message_value_ != NULL;
}
void DefaultValueTestCases::clear_any_with_message_value() {
  if (GetArenaNoVirtual() == NULL && any_with_message_value_ != NULL) delete any_with_message_value_;
  any_with_message_value_ = NULL;
}
const ::google::protobuf::testing::AnyMessage& DefaultValueTestCases::any_with_message_value() const {
  // @@protoc_insertion_point(field_get:google.protobuf.testing.DefaultValueTestCases.any_with_message_value)
  return any_with_message_value_ != NULL ? *any_with_message_value_
                         : *::google::protobuf::testing::AnyMessage::internal_default_instance();
}
::google::protobuf::testing::AnyMessage* DefaultValueTestCases::mutable_any_with_message_value() {
  
  if (any_with_message_value_ == NULL) {
    any_with_message_value_ = new ::google::protobuf::testing::AnyMessage;
  }
  // @@protoc_insertion_point(field_mutable:google.protobuf.testing.DefaultValueTestCases.any_with_message_value)
  return any_with_message_value_;
}
::google::protobuf::testing::AnyMessage* DefaultValueTestCases::release_any_with_message_value() {
  // @@protoc_insertion_point(field_release:google.protobuf.testing.DefaultValueTestCases.any_with_message_value)
  
  ::google::protobuf::testing::AnyMessage* temp = any_with_message_value_;
  any_with_message_value_ = NULL;
  return temp;
}
void DefaultValueTestCases::set_allocated_any_with_message_value(::google::protobuf::testing::AnyMessage* any_with_message_value) {
  delete any_with_message_value_;
  any_with_message_value_ = any_with_message_value;
  if (any_with_message_value) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_set_allocated:google.protobuf.testing.DefaultValueTestCases.any_with_message_value)
}

// optional .google.protobuf.testing.AnyMessage any_with_nested_message = 305;
bool DefaultValueTestCases::has_any_with_nested_message() const {
  return this != internal_default_instance() && any_with_nested_message_ != NULL;
}
void DefaultValueTestCases::clear_any_with_nested_message() {
  if (GetArenaNoVirtual() == NULL && any_with_nested_message_ != NULL) delete any_with_nested_message_;
  any_with_nested_message_ = NULL;
}
const ::google::protobuf::testing::AnyMessage& DefaultValueTestCases::any_with_nested_message() const {
  // @@protoc_insertion_point(field_get:google.protobuf.testing.DefaultValueTestCases.any_with_nested_message)
  return any_with_nested_message_ != NULL ? *any_with_nested_message_
                         : *::google::protobuf::testing::AnyMessage::internal_default_instance();
}
::google::protobuf::testing::AnyMessage* DefaultValueTestCases::mutable_any_with_nested_message() {
  
  if (any_with_nested_message_ == NULL) {
    any_with_nested_message_ = new ::google::protobuf::testing::AnyMessage;
  }
  // @@protoc_insertion_point(field_mutable:google.protobuf.testing.DefaultValueTestCases.any_with_nested_message)
  return any_with_nested_message_;
}
::google::protobuf::testing::AnyMessage* DefaultValueTestCases::release_any_with_nested_message() {
  // @@protoc_insertion_point(field_release:google.protobuf.testing.DefaultValueTestCases.any_with_nested_message)
  
  ::google::protobuf::testing::AnyMessage* temp = any_with_nested_message_;
  any_with_nested_message_ = NULL;
  return temp;
}
void DefaultValueTestCases::set_allocated_any_with_nested_message(::google::protobuf::testing::AnyMessage* any_with_nested_message) {
  delete any_with_nested_message_;
  any_with_nested_message_ = any_with_nested_message;
  if (any_with_nested_message) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_set_allocated:google.protobuf.testing.DefaultValueTestCases.any_with_nested_message)
}

// optional .google.protobuf.testing.AnyMessage any_with_message_containing_map = 306;
bool DefaultValueTestCases::has_any_with_message_containing_map() const {
  return this != internal_default_instance() && any_with_message_containing_map_ != NULL;
}
void DefaultValueTestCases::clear_any_with_message_containing_map() {
  if (GetArenaNoVirtual() == NULL && any_with_message_containing_map_ != NULL) delete any_with_message_containing_map_;
  any_with_message_containing_map_ = NULL;
}
const ::google::protobuf::testing::AnyMessage& DefaultValueTestCases::any_with_message_containing_map() const {
  // @@protoc_insertion_point(field_get:google.protobuf.testing.DefaultValueTestCases.any_with_message_containing_map)
  return any_with_message_containing_map_ != NULL ? *any_with_message_containing_map_
                         : *::google::protobuf::testing::AnyMessage::internal_default_instance();
}
::google::protobuf::testing::AnyMessage* DefaultValueTestCases::mutable_any_with_message_containing_map() {
  
  if (any_with_message_containing_map_ == NULL) {
    any_with_message_containing_map_ = new ::google::protobuf::testing::AnyMessage;
  }
  // @@protoc_insertion_point(field_mutable:google.protobuf.testing.DefaultValueTestCases.any_with_message_containing_map)
  return any_with_message_containing_map_;
}
::google::protobuf::testing::AnyMessage* DefaultValueTestCases::release_any_with_message_containing_map() {
  // @@protoc_insertion_point(field_release:google.protobuf.testing.DefaultValueTestCases.any_with_message_containing_map)
  
  ::google::protobuf::testing::AnyMessage* temp = any_with_message_containing_map_;
  any_with_message_containing_map_ = NULL;
  return temp;
}
void DefaultValueTestCases::set_allocated_any_with_message_containing_map(::google::protobuf::testing::AnyMessage* any_with_message_containing_map) {
  delete any_with_message_containing_map_;
  any_with_message_containing_map_ = any_with_message_containing_map;
  if (any_with_message_containing_map) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_set_allocated:google.protobuf.testing.DefaultValueTestCases.any_with_message_containing_map)
}

// optional .google.protobuf.testing.AnyMessage any_with_message_containing_struct = 307;
bool DefaultValueTestCases::has_any_with_message_containing_struct() const {
  return this != internal_default_instance() && any_with_message_containing_struct_ != NULL;
}
void DefaultValueTestCases::clear_any_with_message_containing_struct() {
  if (GetArenaNoVirtual() == NULL && any_with_message_containing_struct_ != NULL) delete any_with_message_containing_struct_;
  any_with_message_containing_struct_ = NULL;
}
const ::google::protobuf::testing::AnyMessage& DefaultValueTestCases::any_with_message_containing_struct() const {
  // @@protoc_insertion_point(field_get:google.protobuf.testing.DefaultValueTestCases.any_with_message_containing_struct)
  return any_with_message_containing_struct_ != NULL ? *any_with_message_containing_struct_
                         : *::google::protobuf::testing::AnyMessage::internal_default_instance();
}
::google::protobuf::testing::AnyMessage* DefaultValueTestCases::mutable_any_with_message_containing_struct() {
  
  if (any_with_message_containing_struct_ == NULL) {
    any_with_message_containing_struct_ = new ::google::protobuf::testing::AnyMessage;
  }
  // @@protoc_insertion_point(field_mutable:google.protobuf.testing.DefaultValueTestCases.any_with_message_containing_struct)
  return any_with_message_containing_struct_;
}
::google::protobuf::testing::AnyMessage* DefaultValueTestCases::release_any_with_message_containing_struct() {
  // @@protoc_insertion_point(field_release:google.protobuf.testing.DefaultValueTestCases.any_with_message_containing_struct)
  
  ::google::protobuf::testing::AnyMessage* temp = any_with_message_containing_struct_;
  any_with_message_containing_struct_ = NULL;
  return temp;
}
void DefaultValueTestCases::set_allocated_any_with_message_containing_struct(::google::protobuf::testing::AnyMessage* any_with_message_containing_struct) {
  delete any_with_message_containing_struct_;
  any_with_message_containing_struct_ = any_with_message_containing_struct;
  if (any_with_message_containing_struct) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_set_allocated:google.protobuf.testing.DefaultValueTestCases.any_with_message_containing_struct)
}

// optional .google.protobuf.Any top_level_any = 308;
bool DefaultValueTestCases::has_top_level_any() const {
  return this != internal_default_instance() && top_level_any_ != NULL;
}
void DefaultValueTestCases::clear_top_level_any() {
  if (GetArenaNoVirtual() == NULL && top_level_any_ != NULL) delete top_level_any_;
  top_level_any_ = NULL;
}
const ::google::protobuf::Any& DefaultValueTestCases::top_level_any() const {
  // @@protoc_insertion_point(field_get:google.protobuf.testing.DefaultValueTestCases.top_level_any)
  return top_level_any_ != NULL ? *top_level_any_
                         : *::google::protobuf::Any::internal_default_instance();
}
::google::protobuf::Any* DefaultValueTestCases::mutable_top_level_any() {
  
  if (top_level_any_ == NULL) {
    top_level_any_ = new ::google::protobuf::Any;
  }
  // @@protoc_insertion_point(field_mutable:google.protobuf.testing.DefaultValueTestCases.top_level_any)
  return top_level_any_;
}
::google::protobuf::Any* DefaultValueTestCases::release_top_level_any() {
  // @@protoc_insertion_point(field_release:google.protobuf.testing.DefaultValueTestCases.top_level_any)
  
  ::google::protobuf::Any* temp = top_level_any_;
  top_level_any_ = NULL;
  return temp;
}
void DefaultValueTestCases::set_allocated_top_level_any(::google::protobuf::Any* top_level_any) {
  delete top_level_any_;
  top_level_any_ = top_level_any;
  if (top_level_any) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_set_allocated:google.protobuf.testing.DefaultValueTestCases.top_level_any)
}

// optional .google.protobuf.testing.StringtoIntMap empty_map = 401;
bool DefaultValueTestCases::has_empty_map() const {
  return this != internal_default_instance() && empty_map_ != NULL;
}
void DefaultValueTestCases::clear_empty_map() {
  if (GetArenaNoVirtual() == NULL && empty_map_ != NULL) delete empty_map_;
  empty_map_ = NULL;
}
const ::google::protobuf::testing::StringtoIntMap& DefaultValueTestCases::empty_map() const {
  // @@protoc_insertion_point(field_get:google.protobuf.testing.DefaultValueTestCases.empty_map)
  return empty_map_ != NULL ? *empty_map_
                         : *::google::protobuf::testing::StringtoIntMap::internal_default_instance();
}
::google::protobuf::testing::StringtoIntMap* DefaultValueTestCases::mutable_empty_map() {
  
  if (empty_map_ == NULL) {
    empty_map_ = new ::google::protobuf::testing::StringtoIntMap;
  }
  // @@protoc_insertion_point(field_mutable:google.protobuf.testing.DefaultValueTestCases.empty_map)
  return empty_map_;
}
::google::protobuf::testing::StringtoIntMap* DefaultValueTestCases::release_empty_map() {
  // @@protoc_insertion_point(field_release:google.protobuf.testing.DefaultValueTestCases.empty_map)
  
  ::google::protobuf::testing::StringtoIntMap* temp = empty_map_;
  empty_map_ = NULL;
  return temp;
}
void DefaultValueTestCases::set_allocated_empty_map(::google::protobuf::testing::StringtoIntMap* empty_map) {
  delete empty_map_;
  empty_map_ = empty_map;
  if (empty_map) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_set_allocated:google.protobuf.testing.DefaultValueTestCases.empty_map)
}

// optional .google.protobuf.testing.StringtoIntMap string_to_int = 402;
bool DefaultValueTestCases::has_string_to_int() const {
  return this != internal_default_instance() && string_to_int_ != NULL;
}
void DefaultValueTestCases::clear_string_to_int() {
  if (GetArenaNoVirtual() == NULL && string_to_int_ != NULL) delete string_to_int_;
  string_to_int_ = NULL;
}
const ::google::protobuf::testing::StringtoIntMap& DefaultValueTestCases::string_to_int() const {
  // @@protoc_insertion_point(field_get:google.protobuf.testing.DefaultValueTestCases.string_to_int)
  return string_to_int_ != NULL ? *string_to_int_
                         : *::google::protobuf::testing::StringtoIntMap::internal_default_instance();
}
::google::protobuf::testing::StringtoIntMap* DefaultValueTestCases::mutable_string_to_int() {
  
  if (string_to_int_ == NULL) {
    string_to_int_ = new ::google::protobuf::testing::StringtoIntMap;
  }
  // @@protoc_insertion_point(field_mutable:google.protobuf.testing.DefaultValueTestCases.string_to_int)
  return string_to_int_;
}
::google::protobuf::testing::StringtoIntMap* DefaultValueTestCases::release_string_to_int() {
  // @@protoc_insertion_point(field_release:google.protobuf.testing.DefaultValueTestCases.string_to_int)
  
  ::google::protobuf::testing::StringtoIntMap* temp = string_to_int_;
  string_to_int_ = NULL;
  return temp;
}
void DefaultValueTestCases::set_allocated_string_to_int(::google::protobuf::testing::StringtoIntMap* string_to_int) {
  delete string_to_int_;
  string_to_int_ = string_to_int;
  if (string_to_int) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_set_allocated:google.protobuf.testing.DefaultValueTestCases.string_to_int)
}

// optional .google.protobuf.testing.IntToStringMap int_to_string = 403;
bool DefaultValueTestCases::has_int_to_string() const {
  return this != internal_default_instance() && int_to_string_ != NULL;
}
void DefaultValueTestCases::clear_int_to_string() {
  if (GetArenaNoVirtual() == NULL && int_to_string_ != NULL) delete int_to_string_;
  int_to_string_ = NULL;
}
const ::google::protobuf::testing::IntToStringMap& DefaultValueTestCases::int_to_string() const {
  // @@protoc_insertion_point(field_get:google.protobuf.testing.DefaultValueTestCases.int_to_string)
  return int_to_string_ != NULL ? *int_to_string_
                         : *::google::protobuf::testing::IntToStringMap::internal_default_instance();
}
::google::protobuf::testing::IntToStringMap* DefaultValueTestCases::mutable_int_to_string() {
  
  if (int_to_string_ == NULL) {
    int_to_string_ = new ::google::protobuf::testing::IntToStringMap;
  }
  // @@protoc_insertion_point(field_mutable:google.protobuf.testing.DefaultValueTestCases.int_to_string)
  return int_to_string_;
}
::google::protobuf::testing::IntToStringMap* DefaultValueTestCases::release_int_to_string() {
  // @@protoc_insertion_point(field_release:google.protobuf.testing.DefaultValueTestCases.int_to_string)
  
  ::google::protobuf::testing::IntToStringMap* temp = int_to_string_;
  int_to_string_ = NULL;
  return temp;
}
void DefaultValueTestCases::set_allocated_int_to_string(::google::protobuf::testing::IntToStringMap* int_to_string) {
  delete int_to_string_;
  int_to_string_ = int_to_string;
  if (int_to_string) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_set_allocated:google.protobuf.testing.DefaultValueTestCases.int_to_string)
}

// optional .google.protobuf.testing.MixedMap mixed1 = 404;
bool DefaultValueTestCases::has_mixed1() const {
  return this != internal_default_instance() && mixed1_ != NULL;
}
void DefaultValueTestCases::clear_mixed1() {
  if (GetArenaNoVirtual() == NULL && mixed1_ != NULL) delete mixed1_;
  mixed1_ = NULL;
}
const ::google::protobuf::testing::MixedMap& DefaultValueTestCases::mixed1() const {
  // @@protoc_insertion_point(field_get:google.protobuf.testing.DefaultValueTestCases.mixed1)
  return mixed1_ != NULL ? *mixed1_
                         : *::google::protobuf::testing::MixedMap::internal_default_instance();
}
::google::protobuf::testing::MixedMap* DefaultValueTestCases::mutable_mixed1() {
  
  if (mixed1_ == NULL) {
    mixed1_ = new ::google::protobuf::testing::MixedMap;
  }
  // @@protoc_insertion_point(field_mutable:google.protobuf.testing.DefaultValueTestCases.mixed1)
  return mixed1_;
}
::google::protobuf::testing::MixedMap* DefaultValueTestCases::release_mixed1() {
  // @@protoc_insertion_point(field_release:google.protobuf.testing.DefaultValueTestCases.mixed1)
  
  ::google::protobuf::testing::MixedMap* temp = mixed1_;
  mixed1_ = NULL;
  return temp;
}
void DefaultValueTestCases::set_allocated_mixed1(::google::protobuf::testing::MixedMap* mixed1) {
  delete mixed1_;
  mixed1_ = mixed1;
  if (mixed1) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_set_allocated:google.protobuf.testing.DefaultValueTestCases.mixed1)
}

// optional .google.protobuf.testing.MixedMap2 mixed2 = 405;
bool DefaultValueTestCases::has_mixed2() const {
  return this != internal_default_instance() && mixed2_ != NULL;
}
void DefaultValueTestCases::clear_mixed2() {
  if (GetArenaNoVirtual() == NULL && mixed2_ != NULL) delete mixed2_;
  mixed2_ = NULL;
}
const ::google::protobuf::testing::MixedMap2& DefaultValueTestCases::mixed2() const {
  // @@protoc_insertion_point(field_get:google.protobuf.testing.DefaultValueTestCases.mixed2)
  return mixed2_ != NULL ? *mixed2_
                         : *::google::protobuf::testing::MixedMap2::internal_default_instance();
}
::google::protobuf::testing::MixedMap2* DefaultValueTestCases::mutable_mixed2() {
  
  if (mixed2_ == NULL) {
    mixed2_ = new ::google::protobuf::testing::MixedMap2;
  }
  // @@protoc_insertion_point(field_mutable:google.protobuf.testing.DefaultValueTestCases.mixed2)
  return mixed2_;
}
::google::protobuf::testing::MixedMap2* DefaultValueTestCases::release_mixed2() {
  // @@protoc_insertion_point(field_release:google.protobuf.testing.DefaultValueTestCases.mixed2)
  
  ::google::protobuf::testing::MixedMap2* temp = mixed2_;
  mixed2_ = NULL;
  return temp;
}
void DefaultValueTestCases::set_allocated_mixed2(::google::protobuf::testing::MixedMap2* mixed2) {
  delete mixed2_;
  mixed2_ = mixed2;
  if (mixed2) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_set_allocated:google.protobuf.testing.DefaultValueTestCases.mixed2)
}

// optional .google.protobuf.testing.MixedMap2 empty_mixed2 = 406;
bool DefaultValueTestCases::has_empty_mixed2() const {
  return this != internal_default_instance() && empty_mixed2_ != NULL;
}
void DefaultValueTestCases::clear_empty_mixed2() {
  if (GetArenaNoVirtual() == NULL && empty_mixed2_ != NULL) delete empty_mixed2_;
  empty_mixed2_ = NULL;
}
const ::google::protobuf::testing::MixedMap2& DefaultValueTestCases::empty_mixed2() const {
  // @@protoc_insertion_point(field_get:google.protobuf.testing.DefaultValueTestCases.empty_mixed2)
  return empty_mixed2_ != NULL ? *empty_mixed2_
                         : *::google::protobuf::testing::MixedMap2::internal_default_instance();
}
::google::protobuf::testing::MixedMap2* DefaultValueTestCases::mutable_empty_mixed2() {
  
  if (empty_mixed2_ == NULL) {
    empty_mixed2_ = new ::google::protobuf::testing::MixedMap2;
  }
  // @@protoc_insertion_point(field_mutable:google.protobuf.testing.DefaultValueTestCases.empty_mixed2)
  return empty_mixed2_;
}
::google::protobuf::testing::MixedMap2* DefaultValueTestCases::release_empty_mixed2() {
  // @@protoc_insertion_point(field_release:google.protobuf.testing.DefaultValueTestCases.empty_mixed2)
  
  ::google::protobuf::testing::MixedMap2* temp = empty_mixed2_;
  empty_mixed2_ = NULL;
  return temp;
}
void DefaultValueTestCases::set_allocated_empty_mixed2(::google::protobuf::testing::MixedMap2* empty_mixed2) {
  delete empty_mixed2_;
  empty_mixed2_ = empty_mixed2;
  if (empty_mixed2) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_set_allocated:google.protobuf.testing.DefaultValueTestCases.empty_mixed2)
}

// optional .google.protobuf.testing.MessageMap map_of_objects = 407;
bool DefaultValueTestCases::has_map_of_objects() const {
  return this != internal_default_instance() && map_of_objects_ != NULL;
}
void DefaultValueTestCases::clear_map_of_objects() {
  if (GetArenaNoVirtual() == NULL && map_of_objects_ != NULL) delete map_of_objects_;
  map_of_objects_ = NULL;
}
const ::google::protobuf::testing::MessageMap& DefaultValueTestCases::map_of_objects() const {
  // @@protoc_insertion_point(field_get:google.protobuf.testing.DefaultValueTestCases.map_of_objects)
  return map_of_objects_ != NULL ? *map_of_objects_
                         : *::google::protobuf::testing::MessageMap::internal_default_instance();
}
::google::protobuf::testing::MessageMap* DefaultValueTestCases::mutable_map_of_objects() {
  
  if (map_of_objects_ == NULL) {
    map_of_objects_ = new ::google::protobuf::testing::MessageMap;
  }
  // @@protoc_insertion_point(field_mutable:google.protobuf.testing.DefaultValueTestCases.map_of_objects)
  return map_of_objects_;
}
::google::protobuf::testing::MessageMap* DefaultValueTestCases::release_map_of_objects() {
  // @@protoc_insertion_point(field_release:google.protobuf.testing.DefaultValueTestCases.map_of_objects)
  
  ::google::protobuf::testing::MessageMap* temp = map_of_objects_;
  map_of_objects_ = NULL;
  return temp;
}
void DefaultValueTestCases::set_allocated_map_of_objects(::google::protobuf::testing::MessageMap* map_of_objects) {
  delete map_of_objects_;
  map_of_objects_ = map_of_objects;
  if (map_of_objects) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_set_allocated:google.protobuf.testing.DefaultValueTestCases.map_of_objects)
}

// optional .google.protobuf.testing.MixedMap mixed_empty = 408;
bool DefaultValueTestCases::has_mixed_empty() const {
  return this != internal_default_instance() && mixed_empty_ != NULL;
}
void DefaultValueTestCases::clear_mixed_empty() {
  if (GetArenaNoVirtual() == NULL && mixed_empty_ != NULL) delete mixed_empty_;
  mixed_empty_ = NULL;
}
const ::google::protobuf::testing::MixedMap& DefaultValueTestCases::mixed_empty() const {
  // @@protoc_insertion_point(field_get:google.protobuf.testing.DefaultValueTestCases.mixed_empty)
  return mixed_empty_ != NULL ? *mixed_empty_
                         : *::google::protobuf::testing::MixedMap::internal_default_instance();
}
::google::protobuf::testing::MixedMap* DefaultValueTestCases::mutable_mixed_empty() {
  
  if (mixed_empty_ == NULL) {
    mixed_empty_ = new ::google::protobuf::testing::MixedMap;
  }
  // @@protoc_insertion_point(field_mutable:google.protobuf.testing.DefaultValueTestCases.mixed_empty)
  return mixed_empty_;
}
::google::protobuf::testing::MixedMap* DefaultValueTestCases::release_mixed_empty() {
  // @@protoc_insertion_point(field_release:google.protobuf.testing.DefaultValueTestCases.mixed_empty)
  
  ::google::protobuf::testing::MixedMap* temp = mixed_empty_;
  mixed_empty_ = NULL;
  return temp;
}
void DefaultValueTestCases::set_allocated_mixed_empty(::google::protobuf::testing::MixedMap* mixed_empty) {
  delete mixed_empty_;
  mixed_empty_ = mixed_empty;
  if (mixed_empty) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_set_allocated:google.protobuf.testing.DefaultValueTestCases.mixed_empty)
}

// optional .google.protobuf.testing.MessageMap message_map_empty = 409;
bool DefaultValueTestCases::has_message_map_empty() const {
  return this != internal_default_instance() && message_map_empty_ != NULL;
}
void DefaultValueTestCases::clear_message_map_empty() {
  if (GetArenaNoVirtual() == NULL && message_map_empty_ != NULL) delete message_map_empty_;
  message_map_empty_ = NULL;
}
const ::google::protobuf::testing::MessageMap& DefaultValueTestCases::message_map_empty() const {
  // @@protoc_insertion_point(field_get:google.protobuf.testing.DefaultValueTestCases.message_map_empty)
  return message_map_empty_ != NULL ? *message_map_empty_
                         : *::google::protobuf::testing::MessageMap::internal_default_instance();
}
::google::protobuf::testing::MessageMap* DefaultValueTestCases::mutable_message_map_empty() {
  
  if (message_map_empty_ == NULL) {
    message_map_empty_ = new ::google::protobuf::testing::MessageMap;
  }
  // @@protoc_insertion_point(field_mutable:google.protobuf.testing.DefaultValueTestCases.message_map_empty)
  return message_map_empty_;
}
::google::protobuf::testing::MessageMap* DefaultValueTestCases::release_message_map_empty() {
  // @@protoc_insertion_point(field_release:google.protobuf.testing.DefaultValueTestCases.message_map_empty)
  
  ::google::protobuf::testing::MessageMap* temp = message_map_empty_;
  message_map_empty_ = NULL;
  return temp;
}
void DefaultValueTestCases::set_allocated_message_map_empty(::google::protobuf::testing::MessageMap* message_map_empty) {
  delete message_map_empty_;
  message_map_empty_ = message_map_empty;
  if (message_map_empty) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_set_allocated:google.protobuf.testing.DefaultValueTestCases.message_map_empty)
}

// optional .google.protobuf.testing.DoubleValueMessage double_value = 501;
bool DefaultValueTestCases::has_double_value() const {
  return this != internal_default_instance() && double_value_ != NULL;
}
void DefaultValueTestCases::clear_double_value() {
  if (GetArenaNoVirtual() == NULL && double_value_ != NULL) delete double_value_;
  double_value_ = NULL;
}
const ::google::protobuf::testing::DoubleValueMessage& DefaultValueTestCases::double_value() const {
  // @@protoc_insertion_point(field_get:google.protobuf.testing.DefaultValueTestCases.double_value)
  return double_value_ != NULL ? *double_value_
                         : *::google::protobuf::testing::DoubleValueMessage::internal_default_instance();
}
::google::protobuf::testing::DoubleValueMessage* DefaultValueTestCases::mutable_double_value() {
  
  if (double_value_ == NULL) {
    double_value_ = new ::google::protobuf::testing::DoubleValueMessage;
  }
  // @@protoc_insertion_point(field_mutable:google.protobuf.testing.DefaultValueTestCases.double_value)
  return double_value_;
}
::google::protobuf::testing::DoubleValueMessage* DefaultValueTestCases::release_double_value() {
  // @@protoc_insertion_point(field_release:google.protobuf.testing.DefaultValueTestCases.double_value)
  
  ::google::protobuf::testing::DoubleValueMessage* temp = double_value_;
  double_value_ = NULL;
  return temp;
}
void DefaultValueTestCases::set_allocated_double_value(::google::protobuf::testing::DoubleValueMessage* double_value) {
  delete double_value_;
  double_value_ = double_value;
  if (double_value) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_set_allocated:google.protobuf.testing.DefaultValueTestCases.double_value)
}

// optional .google.protobuf.testing.DoubleValueMessage double_value_default = 502;
bool DefaultValueTestCases::has_double_value_default() const {
  return this != internal_default_instance() && double_value_default_ != NULL;
}
void DefaultValueTestCases::clear_double_value_default() {
  if (GetArenaNoVirtual() == NULL && double_value_default_ != NULL) delete double_value_default_;
  double_value_default_ = NULL;
}
const ::google::protobuf::testing::DoubleValueMessage& DefaultValueTestCases::double_value_default() const {
  // @@protoc_insertion_point(field_get:google.protobuf.testing.DefaultValueTestCases.double_value_default)
  return double_value_default_ != NULL ? *double_value_default_
                         : *::google::protobuf::testing::DoubleValueMessage::internal_default_instance();
}
::google::protobuf::testing::DoubleValueMessage* DefaultValueTestCases::mutable_double_value_default() {
  
  if (double_value_default_ == NULL) {
    double_value_default_ = new ::google::protobuf::testing::DoubleValueMessage;
  }
  // @@protoc_insertion_point(field_mutable:google.protobuf.testing.DefaultValueTestCases.double_value_default)
  return double_value_default_;
}
::google::protobuf::testing::DoubleValueMessage* DefaultValueTestCases::release_double_value_default() {
  // @@protoc_insertion_point(field_release:google.protobuf.testing.DefaultValueTestCases.double_value_default)
  
  ::google::protobuf::testing::DoubleValueMessage* temp = double_value_default_;
  double_value_default_ = NULL;
  return temp;
}
void DefaultValueTestCases::set_allocated_double_value_default(::google::protobuf::testing::DoubleValueMessage* double_value_default) {
  delete double_value_default_;
  double_value_default_ = double_value_default;
  if (double_value_default) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_set_allocated:google.protobuf.testing.DefaultValueTestCases.double_value_default)
}

inline const DefaultValueTestCases* DefaultValueTestCases::internal_default_instance() {
  return &DefaultValueTestCases_default_instance_.get();
}
#endif  // PROTOBUF_INLINE_NOT_IN_HEADERS

// ===================================================================

#if !defined(_MSC_VER) || _MSC_VER >= 1900
const int DoubleMessage::kDoubleValueFieldNumber;
const int DoubleMessage::kRepeatedDoubleFieldNumber;
const int DoubleMessage::kNestedMessageFieldNumber;
const int DoubleMessage::kRepeatedNestedMessageFieldNumber;
const int DoubleMessage::kDoubleWrapperFieldNumber;
const int DoubleMessage::kStrValueFieldNumber;
const int DoubleMessage::kNumValueFieldNumber;
#endif  // !defined(_MSC_VER) || _MSC_VER >= 1900

DoubleMessage::DoubleMessage()
  : ::google::protobuf::Message(), _internal_metadata_(NULL) {
  if (this != internal_default_instance()) protobuf_InitDefaults_google_2fprotobuf_2futil_2finternal_2ftestdata_2fdefault_5fvalue_2eproto();
  SharedCtor();
  // @@protoc_insertion_point(constructor:google.protobuf.testing.DoubleMessage)
}

void DoubleMessage::InitAsDefaultInstance() {
  nested_message_ = const_cast< ::google::protobuf::testing::DoubleMessage*>(
      ::google::protobuf::testing::DoubleMessage::internal_default_instance());
  double_wrapper_ = const_cast< ::google::protobuf::DoubleValue*>(
      ::google::protobuf::DoubleValue::internal_default_instance());
  DoubleMessage_default_oneof_instance_->str_value_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  DoubleMessage_default_oneof_instance_->num_value_ = GOOGLE_LONGLONG(0);
}

DoubleMessage::DoubleMessage(const DoubleMessage& from)
  : ::google::protobuf::Message(),
    _internal_metadata_(NULL) {
  SharedCtor();
  UnsafeMergeFrom(from);
  // @@protoc_insertion_point(copy_constructor:google.protobuf.testing.DoubleMessage)
}

void DoubleMessage::SharedCtor() {
  nested_message_ = NULL;
  double_wrapper_ = NULL;
  double_value_ = 0;
  clear_has_value();
  _cached_size_ = 0;
}

DoubleMessage::~DoubleMessage() {
  // @@protoc_insertion_point(destructor:google.protobuf.testing.DoubleMessage)
  SharedDtor();
}

void DoubleMessage::SharedDtor() {
  if (has_value()) {
    clear_value();
  }
  if (this != &DoubleMessage_default_instance_.get()) {
    delete nested_message_;
    delete double_wrapper_;
  }
}

void DoubleMessage::SetCachedSize(int size) const {
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
}
const ::google::protobuf::Descriptor* DoubleMessage::descriptor() {
  protobuf_AssignDescriptorsOnce();
  return DoubleMessage_descriptor_;
}

const DoubleMessage& DoubleMessage::default_instance() {
  protobuf_InitDefaults_google_2fprotobuf_2futil_2finternal_2ftestdata_2fdefault_5fvalue_2eproto();
  return *internal_default_instance();
}

::google::protobuf::internal::ExplicitlyConstructed<DoubleMessage> DoubleMessage_default_instance_;

DoubleMessage* DoubleMessage::New(::google::protobuf::Arena* arena) const {
  DoubleMessage* n = new DoubleMessage;
  if (arena != NULL) {
    arena->Own(n);
  }
  return n;
}

void DoubleMessage::clear_value() {
// @@protoc_insertion_point(one_of_clear_start:google.protobuf.testing.DoubleMessage)
  switch (value_case()) {
    case kStrValue: {
      value_.str_value_.DestroyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
      break;
    }
    case kNumValue: {
      // No need to clear
      break;
    }
    case VALUE_NOT_SET: {
      break;
    }
  }
  _oneof_case_[0] = VALUE_NOT_SET;
}


void DoubleMessage::Clear() {
// @@protoc_insertion_point(message_clear_start:google.protobuf.testing.DoubleMessage)
  double_value_ = 0;
  if (GetArenaNoVirtual() == NULL && nested_message_ != NULL) delete nested_message_;
  nested_message_ = NULL;
  if (GetArenaNoVirtual() == NULL && double_wrapper_ != NULL) delete double_wrapper_;
  double_wrapper_ = NULL;
  repeated_double_.Clear();
  repeated_nested_message_.Clear();
  clear_value();
}

bool DoubleMessage::MergePartialFromCodedStream(
    ::google::protobuf::io::CodedInputStream* input) {
#define DO_(EXPRESSION) if (!GOOGLE_PREDICT_TRUE(EXPRESSION)) goto failure
  ::google::protobuf::uint32 tag;
  // @@protoc_insertion_point(parse_start:google.protobuf.testing.DoubleMessage)
  for (;;) {
    ::std::pair< ::google::protobuf::uint32, bool> p = input->ReadTagWithCutoff(16383);
    tag = p.first;
    if (!p.second) goto handle_unusual;
    switch (::google::protobuf::internal::WireFormatLite::GetTagFieldNumber(tag)) {
      // optional double double_value = 1;
      case 1: {
        if (tag == 9) {

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   double, ::google::protobuf::internal::WireFormatLite::TYPE_DOUBLE>(
                 input, &double_value_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(18)) goto parse_repeated_double;
        break;
      }

      // repeated double repeated_double = 2;
      case 2: {
        if (tag == 18) {
         parse_repeated_double:
          DO_((::google::protobuf::internal::WireFormatLite::ReadPackedPrimitive<
                   double, ::google::protobuf::internal::WireFormatLite::TYPE_DOUBLE>(
                 input, this->mutable_repeated_double())));
        } else if (tag == 17) {
          DO_((::google::protobuf::internal::WireFormatLite::ReadRepeatedPrimitiveNoInline<
                   double, ::google::protobuf::internal::WireFormatLite::TYPE_DOUBLE>(
                 1, 18, input, this->mutable_repeated_double())));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(26)) goto parse_nested_message;
        break;
      }

      // optional .google.protobuf.testing.DoubleMessage nested_message = 3;
      case 3: {
        if (tag == 26) {
         parse_nested_message:
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessageNoVirtual(
               input, mutable_nested_message()));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(34)) goto parse_repeated_nested_message;
        break;
      }

      // repeated .google.protobuf.testing.DoubleMessage repeated_nested_message = 4;
      case 4: {
        if (tag == 34) {
         parse_repeated_nested_message:
          DO_(input->IncrementRecursionDepth());
         parse_loop_repeated_nested_message:
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessageNoVirtualNoRecursionDepth(
                input, add_repeated_nested_message()));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(34)) goto parse_loop_repeated_nested_message;
        input->UnsafeDecrementRecursionDepth();
        if (input->ExpectTag(802)) goto parse_double_wrapper;
        break;
      }

      // optional .google.protobuf.DoubleValue double_wrapper = 100;
      case 100: {
        if (tag == 802) {
         parse_double_wrapper:
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessageNoVirtual(
               input, mutable_double_wrapper()));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(898)) goto parse_str_value;
        break;
      }

      // optional string str_value = 112;
      case 112: {
        if (tag == 898) {
         parse_str_value:
          DO_(::google::protobuf::internal::WireFormatLite::ReadString(
                input, this->mutable_str_value()));
          DO_(::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
            this->str_value().data(), this->str_value().length(),
            ::google::protobuf::internal::WireFormatLite::PARSE,
            "google.protobuf.testing.DoubleMessage.str_value"));
        } else {
          goto handle_unusual;
        }
        goto after_num_value;
        break;
      }

      // optional int64 num_value = 113;
      case 113: {
        if (tag == 904) {
          clear_value();
          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_INT64>(
                 input, &value_.num_value_)));
          set_has_num_value();
        } else {
          goto handle_unusual;
        }
       after_num_value:
        if (input->ExpectAtEnd()) goto success;
        break;
      }

      default: {
      handle_unusual:
        if (tag == 0 ||
            ::google::protobuf::internal::WireFormatLite::GetTagWireType(tag) ==
            ::google::protobuf::internal::WireFormatLite::WIRETYPE_END_GROUP) {
          goto success;
        }
        DO_(::google::protobuf::internal::WireFormatLite::SkipField(input, tag));
        break;
      }
    }
  }
success:
  // @@protoc_insertion_point(parse_success:google.protobuf.testing.DoubleMessage)
  return true;
failure:
  // @@protoc_insertion_point(parse_failure:google.protobuf.testing.DoubleMessage)
  return false;
#undef DO_
}

void DoubleMessage::SerializeWithCachedSizes(
    ::google::protobuf::io::CodedOutputStream* output) const {
  // @@protoc_insertion_point(serialize_start:google.protobuf.testing.DoubleMessage)
  // optional double double_value = 1;
  if (this->double_value() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteDouble(1, this->double_value(), output);
  }

  // repeated double repeated_double = 2;
  if (this->repeated_double_size() > 0) {
    ::google::protobuf::internal::WireFormatLite::WriteTag(2, ::google::protobuf::internal::WireFormatLite::WIRETYPE_LENGTH_DELIMITED, output);
    output->WriteVarint32(_repeated_double_cached_byte_size_);
  }
  for (int i = 0; i < this->repeated_double_size(); i++) {
    ::google::protobuf::internal::WireFormatLite::WriteDoubleNoTag(
      this->repeated_double(i), output);
  }

  // optional .google.protobuf.testing.DoubleMessage nested_message = 3;
  if (this->has_nested_message()) {
    ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
      3, *this->nested_message_, output);
  }

  // repeated .google.protobuf.testing.DoubleMessage repeated_nested_message = 4;
  for (unsigned int i = 0, n = this->repeated_nested_message_size(); i < n; i++) {
    ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
      4, this->repeated_nested_message(i), output);
  }

  // optional .google.protobuf.DoubleValue double_wrapper = 100;
  if (this->has_double_wrapper()) {
    ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
      100, *this->double_wrapper_, output);
  }

  // optional string str_value = 112;
  if (has_str_value()) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->str_value().data(), this->str_value().length(),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "google.protobuf.testing.DoubleMessage.str_value");
    ::google::protobuf::internal::WireFormatLite::WriteStringMaybeAliased(
      112, this->str_value(), output);
  }

  // optional int64 num_value = 113;
  if (has_num_value()) {
    ::google::protobuf::internal::WireFormatLite::WriteInt64(113, this->num_value(), output);
  }

  // @@protoc_insertion_point(serialize_end:google.protobuf.testing.DoubleMessage)
}

::google::protobuf::uint8* DoubleMessage::InternalSerializeWithCachedSizesToArray(
    bool deterministic, ::google::protobuf::uint8* target) const {
  (void)deterministic; // Unused
  // @@protoc_insertion_point(serialize_to_array_start:google.protobuf.testing.DoubleMessage)
  // optional double double_value = 1;
  if (this->double_value() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteDoubleToArray(1, this->double_value(), target);
  }

  // repeated double repeated_double = 2;
  if (this->repeated_double_size() > 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteTagToArray(
      2,
      ::google::protobuf::internal::WireFormatLite::WIRETYPE_LENGTH_DELIMITED,
      target);
    target = ::google::protobuf::io::CodedOutputStream::WriteVarint32ToArray(
      _repeated_double_cached_byte_size_, target);
  }
  for (int i = 0; i < this->repeated_double_size(); i++) {
    target = ::google::protobuf::internal::WireFormatLite::
      WriteDoubleNoTagToArray(this->repeated_double(i), target);
  }

  // optional .google.protobuf.testing.DoubleMessage nested_message = 3;
  if (this->has_nested_message()) {
    target = ::google::protobuf::internal::WireFormatLite::
      InternalWriteMessageNoVirtualToArray(
        3, *this->nested_message_, false, target);
  }

  // repeated .google.protobuf.testing.DoubleMessage repeated_nested_message = 4;
  for (unsigned int i = 0, n = this->repeated_nested_message_size(); i < n; i++) {
    target = ::google::protobuf::internal::WireFormatLite::
      InternalWriteMessageNoVirtualToArray(
        4, this->repeated_nested_message(i), false, target);
  }

  // optional .google.protobuf.DoubleValue double_wrapper = 100;
  if (this->has_double_wrapper()) {
    target = ::google::protobuf::internal::WireFormatLite::
      InternalWriteMessageNoVirtualToArray(
        100, *this->double_wrapper_, false, target);
  }

  // optional string str_value = 112;
  if (has_str_value()) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->str_value().data(), this->str_value().length(),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "google.protobuf.testing.DoubleMessage.str_value");
    target =
      ::google::protobuf::internal::WireFormatLite::WriteStringToArray(
        112, this->str_value(), target);
  }

  // optional int64 num_value = 113;
  if (has_num_value()) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt64ToArray(113, this->num_value(), target);
  }

  // @@protoc_insertion_point(serialize_to_array_end:google.protobuf.testing.DoubleMessage)
  return target;
}

size_t DoubleMessage::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:google.protobuf.testing.DoubleMessage)
  size_t total_size = 0;

  // optional double double_value = 1;
  if (this->double_value() != 0) {
    total_size += 1 + 8;
  }

  // optional .google.protobuf.testing.DoubleMessage nested_message = 3;
  if (this->has_nested_message()) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::MessageSizeNoVirtual(
        *this->nested_message_);
  }

  // optional .google.protobuf.DoubleValue double_wrapper = 100;
  if (this->has_double_wrapper()) {
    total_size += 2 +
      ::google::protobuf::internal::WireFormatLite::MessageSizeNoVirtual(
        *this->double_wrapper_);
  }

  // repeated double repeated_double = 2;
  {
    size_t data_size = 0;
    unsigned int count = this->repeated_double_size();
    data_size = 8UL * count;
    if (data_size > 0) {
      total_size += 1 +
        ::google::protobuf::internal::WireFormatLite::Int32Size(data_size);
    }
    int cached_size = ::google::protobuf::internal::ToCachedSize(data_size);
    GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
    _repeated_double_cached_byte_size_ = cached_size;
    GOOGLE_SAFE_CONCURRENT_WRITES_END();
    total_size += data_size;
  }

  // repeated .google.protobuf.testing.DoubleMessage repeated_nested_message = 4;
  {
    unsigned int count = this->repeated_nested_message_size();
    total_size += 1UL * count;
    for (unsigned int i = 0; i < count; i++) {
      total_size +=
        ::google::protobuf::internal::WireFormatLite::MessageSizeNoVirtual(
          this->repeated_nested_message(i));
    }
  }

  switch (value_case()) {
    // optional string str_value = 112;
    case kStrValue: {
      total_size += 2 +
        ::google::protobuf::internal::WireFormatLite::StringSize(
          this->str_value());
      break;
    }
    // optional int64 num_value = 113;
    case kNumValue: {
      total_size += 2 +
        ::google::protobuf::internal::WireFormatLite::Int64Size(
          this->num_value());
      break;
    }
    case VALUE_NOT_SET: {
      break;
    }
  }
  int cached_size = ::google::protobuf::internal::ToCachedSize(total_size);
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = cached_size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
  return total_size;
}

void DoubleMessage::MergeFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_merge_from_start:google.protobuf.testing.DoubleMessage)
  if (GOOGLE_PREDICT_FALSE(&from == this)) MergeFromFail(__LINE__);
  const DoubleMessage* source =
      ::google::protobuf::internal::DynamicCastToGenerated<const DoubleMessage>(
          &from);
  if (source == NULL) {
  // @@protoc_insertion_point(generalized_merge_from_cast_fail:google.protobuf.testing.DoubleMessage)
    ::google::protobuf::internal::ReflectionOps::Merge(from, this);
  } else {
  // @@protoc_insertion_point(generalized_merge_from_cast_success:google.protobuf.testing.DoubleMessage)
    UnsafeMergeFrom(*source);
  }
}

void DoubleMessage::MergeFrom(const DoubleMessage& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:google.protobuf.testing.DoubleMessage)
  if (GOOGLE_PREDICT_TRUE(&from != this)) {
    UnsafeMergeFrom(from);
  } else {
    MergeFromFail(__LINE__);
  }
}

void DoubleMessage::UnsafeMergeFrom(const DoubleMessage& from) {
  GOOGLE_DCHECK(&from != this);
  repeated_double_.UnsafeMergeFrom(from.repeated_double_);
  repeated_nested_message_.MergeFrom(from.repeated_nested_message_);
  switch (from.value_case()) {
    case kStrValue: {
      set_str_value(from.str_value());
      break;
    }
    case kNumValue: {
      set_num_value(from.num_value());
      break;
    }
    case VALUE_NOT_SET: {
      break;
    }
  }
  if (from.double_value() != 0) {
    set_double_value(from.double_value());
  }
  if (from.has_nested_message()) {
    mutable_nested_message()->::google::protobuf::testing::DoubleMessage::MergeFrom(from.nested_message());
  }
  if (from.has_double_wrapper()) {
    mutable_double_wrapper()->::google::protobuf::DoubleValue::MergeFrom(from.double_wrapper());
  }
}

void DoubleMessage::CopyFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_copy_from_start:google.protobuf.testing.DoubleMessage)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

void DoubleMessage::CopyFrom(const DoubleMessage& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:google.protobuf.testing.DoubleMessage)
  if (&from == this) return;
  Clear();
  UnsafeMergeFrom(from);
}

bool DoubleMessage::IsInitialized() const {

  return true;
}

void DoubleMessage::Swap(DoubleMessage* other) {
  if (other == this) return;
  InternalSwap(other);
}
void DoubleMessage::InternalSwap(DoubleMessage* other) {
  std::swap(double_value_, other->double_value_);
  repeated_double_.UnsafeArenaSwap(&other->repeated_double_);
  std::swap(nested_message_, other->nested_message_);
  repeated_nested_message_.UnsafeArenaSwap(&other->repeated_nested_message_);
  std::swap(double_wrapper_, other->double_wrapper_);
  std::swap(value_, other->value_);
  std::swap(_oneof_case_[0], other->_oneof_case_[0]);
  _internal_metadata_.Swap(&other->_internal_metadata_);
  std::swap(_cached_size_, other->_cached_size_);
}

::google::protobuf::Metadata DoubleMessage::GetMetadata() const {
  protobuf_AssignDescriptorsOnce();
  ::google::protobuf::Metadata metadata;
  metadata.descriptor = DoubleMessage_descriptor_;
  metadata.reflection = DoubleMessage_reflection_;
  return metadata;
}

#if PROTOBUF_INLINE_NOT_IN_HEADERS
// DoubleMessage

// optional double double_value = 1;
void DoubleMessage::clear_double_value() {
  double_value_ = 0;
}
double DoubleMessage::double_value() const {
  // @@protoc_insertion_point(field_get:google.protobuf.testing.DoubleMessage.double_value)
  return double_value_;
}
void DoubleMessage::set_double_value(double value) {
  
  double_value_ = value;
  // @@protoc_insertion_point(field_set:google.protobuf.testing.DoubleMessage.double_value)
}

// repeated double repeated_double = 2;
int DoubleMessage::repeated_double_size() const {
  return repeated_double_.size();
}
void DoubleMessage::clear_repeated_double() {
  repeated_double_.Clear();
}
double DoubleMessage::repeated_double(int index) const {
  // @@protoc_insertion_point(field_get:google.protobuf.testing.DoubleMessage.repeated_double)
  return repeated_double_.Get(index);
}
void DoubleMessage::set_repeated_double(int index, double value) {
  repeated_double_.Set(index, value);
  // @@protoc_insertion_point(field_set:google.protobuf.testing.DoubleMessage.repeated_double)
}
void DoubleMessage::add_repeated_double(double value) {
  repeated_double_.Add(value);
  // @@protoc_insertion_point(field_add:google.protobuf.testing.DoubleMessage.repeated_double)
}
const ::google::protobuf::RepeatedField< double >&
DoubleMessage::repeated_double() const {
  // @@protoc_insertion_point(field_list:google.protobuf.testing.DoubleMessage.repeated_double)
  return repeated_double_;
}
::google::protobuf::RepeatedField< double >*
DoubleMessage::mutable_repeated_double() {
  // @@protoc_insertion_point(field_mutable_list:google.protobuf.testing.DoubleMessage.repeated_double)
  return &repeated_double_;
}

// optional .google.protobuf.testing.DoubleMessage nested_message = 3;
bool DoubleMessage::has_nested_message() const {
  return this != internal_default_instance() && nested_message_ != NULL;
}
void DoubleMessage::clear_nested_message() {
  if (GetArenaNoVirtual() == NULL && nested_message_ != NULL) delete nested_message_;
  nested_message_ = NULL;
}
const ::google::protobuf::testing::DoubleMessage& DoubleMessage::nested_message() const {
  // @@protoc_insertion_point(field_get:google.protobuf.testing.DoubleMessage.nested_message)
  return nested_message_ != NULL ? *nested_message_
                         : *::google::protobuf::testing::DoubleMessage::internal_default_instance();
}
::google::protobuf::testing::DoubleMessage* DoubleMessage::mutable_nested_message() {
  
  if (nested_message_ == NULL) {
    nested_message_ = new ::google::protobuf::testing::DoubleMessage;
  }
  // @@protoc_insertion_point(field_mutable:google.protobuf.testing.DoubleMessage.nested_message)
  return nested_message_;
}
::google::protobuf::testing::DoubleMessage* DoubleMessage::release_nested_message() {
  // @@protoc_insertion_point(field_release:google.protobuf.testing.DoubleMessage.nested_message)
  
  ::google::protobuf::testing::DoubleMessage* temp = nested_message_;
  nested_message_ = NULL;
  return temp;
}
void DoubleMessage::set_allocated_nested_message(::google::protobuf::testing::DoubleMessage* nested_message) {
  delete nested_message_;
  nested_message_ = nested_message;
  if (nested_message) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_set_allocated:google.protobuf.testing.DoubleMessage.nested_message)
}

// repeated .google.protobuf.testing.DoubleMessage repeated_nested_message = 4;
int DoubleMessage::repeated_nested_message_size() const {
  return repeated_nested_message_.size();
}
void DoubleMessage::clear_repeated_nested_message() {
  repeated_nested_message_.Clear();
}
const ::google::protobuf::testing::DoubleMessage& DoubleMessage::repeated_nested_message(int index) const {
  // @@protoc_insertion_point(field_get:google.protobuf.testing.DoubleMessage.repeated_nested_message)
  return repeated_nested_message_.Get(index);
}
::google::protobuf::testing::DoubleMessage* DoubleMessage::mutable_repeated_nested_message(int index) {
  // @@protoc_insertion_point(field_mutable:google.protobuf.testing.DoubleMessage.repeated_nested_message)
  return repeated_nested_message_.Mutable(index);
}
::google::protobuf::testing::DoubleMessage* DoubleMessage::add_repeated_nested_message() {
  // @@protoc_insertion_point(field_add:google.protobuf.testing.DoubleMessage.repeated_nested_message)
  return repeated_nested_message_.Add();
}
::google::protobuf::RepeatedPtrField< ::google::protobuf::testing::DoubleMessage >*
DoubleMessage::mutable_repeated_nested_message() {
  // @@protoc_insertion_point(field_mutable_list:google.protobuf.testing.DoubleMessage.repeated_nested_message)
  return &repeated_nested_message_;
}
const ::google::protobuf::RepeatedPtrField< ::google::protobuf::testing::DoubleMessage >&
DoubleMessage::repeated_nested_message() const {
  // @@protoc_insertion_point(field_list:google.protobuf.testing.DoubleMessage.repeated_nested_message)
  return repeated_nested_message_;
}

// optional .google.protobuf.DoubleValue double_wrapper = 100;
bool DoubleMessage::has_double_wrapper() const {
  return this != internal_default_instance() && double_wrapper_ != NULL;
}
void DoubleMessage::clear_double_wrapper() {
  if (GetArenaNoVirtual() == NULL && double_wrapper_ != NULL) delete double_wrapper_;
  double_wrapper_ = NULL;
}
const ::google::protobuf::DoubleValue& DoubleMessage::double_wrapper() const {
  // @@protoc_insertion_point(field_get:google.protobuf.testing.DoubleMessage.double_wrapper)
  return double_wrapper_ != NULL ? *double_wrapper_
                         : *::google::protobuf::DoubleValue::internal_default_instance();
}
::google::protobuf::DoubleValue* DoubleMessage::mutable_double_wrapper() {
  
  if (double_wrapper_ == NULL) {
    double_wrapper_ = new ::google::protobuf::DoubleValue;
  }
  // @@protoc_insertion_point(field_mutable:google.protobuf.testing.DoubleMessage.double_wrapper)
  return double_wrapper_;
}
::google::protobuf::DoubleValue* DoubleMessage::release_double_wrapper() {
  // @@protoc_insertion_point(field_release:google.protobuf.testing.DoubleMessage.double_wrapper)
  
  ::google::protobuf::DoubleValue* temp = double_wrapper_;
  double_wrapper_ = NULL;
  return temp;
}
void DoubleMessage::set_allocated_double_wrapper(::google::protobuf::DoubleValue* double_wrapper) {
  delete double_wrapper_;
  if (double_wrapper != NULL && double_wrapper->GetArena() != NULL) {
    ::google::protobuf::DoubleValue* new_double_wrapper = new ::google::protobuf::DoubleValue;
    new_double_wrapper->CopyFrom(*double_wrapper);
    double_wrapper = new_double_wrapper;
  }
  double_wrapper_ = double_wrapper;
  if (double_wrapper) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_set_allocated:google.protobuf.testing.DoubleMessage.double_wrapper)
}

// optional string str_value = 112;
bool DoubleMessage::has_str_value() const {
  return value_case() == kStrValue;
}
void DoubleMessage::set_has_str_value() {
  _oneof_case_[0] = kStrValue;
}
void DoubleMessage::clear_str_value() {
  if (has_str_value()) {
    value_.str_value_.DestroyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
    clear_has_value();
  }
}
const ::std::string& DoubleMessage::str_value() const {
  // @@protoc_insertion_point(field_get:google.protobuf.testing.DoubleMessage.str_value)
  if (has_str_value()) {
    return value_.str_value_.GetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  }
  return *&::google::protobuf::internal::GetEmptyStringAlreadyInited();
}
void DoubleMessage::set_str_value(const ::std::string& value) {
  // @@protoc_insertion_point(field_set:google.protobuf.testing.DoubleMessage.str_value)
  if (!has_str_value()) {
    clear_value();
    set_has_str_value();
    value_.str_value_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  }
  value_.str_value_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:google.protobuf.testing.DoubleMessage.str_value)
}
void DoubleMessage::set_str_value(const char* value) {
  if (!has_str_value()) {
    clear_value();
    set_has_str_value();
    value_.str_value_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  }
  value_.str_value_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(value));
  // @@protoc_insertion_point(field_set_char:google.protobuf.testing.DoubleMessage.str_value)
}
void DoubleMessage::set_str_value(const char* value, size_t size) {
  if (!has_str_value()) {
    clear_value();
    set_has_str_value();
    value_.str_value_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  }
  value_.str_value_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(
      reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:google.protobuf.testing.DoubleMessage.str_value)
}
::std::string* DoubleMessage::mutable_str_value() {
  if (!has_str_value()) {
    clear_value();
    set_has_str_value();
    value_.str_value_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  }
  // @@protoc_insertion_point(field_mutable:google.protobuf.testing.DoubleMessage.str_value)
  return value_.str_value_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
::std::string* DoubleMessage::release_str_value() {
  // @@protoc_insertion_point(field_release:google.protobuf.testing.DoubleMessage.str_value)
  if (has_str_value()) {
    clear_has_value();
    return value_.str_value_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  } else {
    return NULL;
  }
}
void DoubleMessage::set_allocated_str_value(::std::string* str_value) {
  if (!has_str_value()) {
    value_.str_value_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  }
  clear_value();
  if (str_value != NULL) {
    set_has_str_value();
    value_.str_value_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
        str_value);
  }
  // @@protoc_insertion_point(field_set_allocated:google.protobuf.testing.DoubleMessage.str_value)
}

// optional int64 num_value = 113;
bool DoubleMessage::has_num_value() const {
  return value_case() == kNumValue;
}
void DoubleMessage::set_has_num_value() {
  _oneof_case_[0] = kNumValue;
}
void DoubleMessage::clear_num_value() {
  if (has_num_value()) {
    value_.num_value_ = GOOGLE_LONGLONG(0);
    clear_has_value();
  }
}
::google::protobuf::int64 DoubleMessage::num_value() const {
  // @@protoc_insertion_point(field_get:google.protobuf.testing.DoubleMessage.num_value)
  if (has_num_value()) {
    return value_.num_value_;
  }
  return GOOGLE_LONGLONG(0);
}
void DoubleMessage::set_num_value(::google::protobuf::int64 value) {
  if (!has_num_value()) {
    clear_value();
    set_has_num_value();
  }
  value_.num_value_ = value;
  // @@protoc_insertion_point(field_set:google.protobuf.testing.DoubleMessage.num_value)
}

bool DoubleMessage::has_value() const {
  return value_case() != VALUE_NOT_SET;
}
void DoubleMessage::clear_has_value() {
  _oneof_case_[0] = VALUE_NOT_SET;
}
DoubleMessage::ValueCase DoubleMessage::value_case() const {
  return DoubleMessage::ValueCase(_oneof_case_[0]);
}
inline const DoubleMessage* DoubleMessage::internal_default_instance() {
  return &DoubleMessage_default_instance_.get();
}
#endif  // PROTOBUF_INLINE_NOT_IN_HEADERS

// ===================================================================

#if !defined(_MSC_VER) || _MSC_VER >= 1900
const int StructMessage::kStructFieldNumber;
#endif  // !defined(_MSC_VER) || _MSC_VER >= 1900

StructMessage::StructMessage()
  : ::google::protobuf::Message(), _internal_metadata_(NULL) {
  if (this != internal_default_instance()) protobuf_InitDefaults_google_2fprotobuf_2futil_2finternal_2ftestdata_2fdefault_5fvalue_2eproto();
  SharedCtor();
  // @@protoc_insertion_point(constructor:google.protobuf.testing.StructMessage)
}

void StructMessage::InitAsDefaultInstance() {
  struct__ = const_cast< ::google::protobuf::Struct*>(
      ::google::protobuf::Struct::internal_default_instance());
}

StructMessage::StructMessage(const StructMessage& from)
  : ::google::protobuf::Message(),
    _internal_metadata_(NULL) {
  SharedCtor();
  UnsafeMergeFrom(from);
  // @@protoc_insertion_point(copy_constructor:google.protobuf.testing.StructMessage)
}

void StructMessage::SharedCtor() {
  struct__ = NULL;
  _cached_size_ = 0;
}

StructMessage::~StructMessage() {
  // @@protoc_insertion_point(destructor:google.protobuf.testing.StructMessage)
  SharedDtor();
}

void StructMessage::SharedDtor() {
  if (this != &StructMessage_default_instance_.get()) {
    delete struct__;
  }
}

void StructMessage::SetCachedSize(int size) const {
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
}
const ::google::protobuf::Descriptor* StructMessage::descriptor() {
  protobuf_AssignDescriptorsOnce();
  return StructMessage_descriptor_;
}

const StructMessage& StructMessage::default_instance() {
  protobuf_InitDefaults_google_2fprotobuf_2futil_2finternal_2ftestdata_2fdefault_5fvalue_2eproto();
  return *internal_default_instance();
}

::google::protobuf::internal::ExplicitlyConstructed<StructMessage> StructMessage_default_instance_;

StructMessage* StructMessage::New(::google::protobuf::Arena* arena) const {
  StructMessage* n = new StructMessage;
  if (arena != NULL) {
    arena->Own(n);
  }
  return n;
}

void StructMessage::Clear() {
// @@protoc_insertion_point(message_clear_start:google.protobuf.testing.StructMessage)
  if (GetArenaNoVirtual() == NULL && struct__ != NULL) delete struct__;
  struct__ = NULL;
}

bool StructMessage::MergePartialFromCodedStream(
    ::google::protobuf::io::CodedInputStream* input) {
#define DO_(EXPRESSION) if (!GOOGLE_PREDICT_TRUE(EXPRESSION)) goto failure
  ::google::protobuf::uint32 tag;
  // @@protoc_insertion_point(parse_start:google.protobuf.testing.StructMessage)
  for (;;) {
    ::std::pair< ::google::protobuf::uint32, bool> p = input->ReadTagWithCutoff(127);
    tag = p.first;
    if (!p.second) goto handle_unusual;
    switch (::google::protobuf::internal::WireFormatLite::GetTagFieldNumber(tag)) {
      // optional .google.protobuf.Struct struct = 1;
      case 1: {
        if (tag == 10) {
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessageNoVirtual(
               input, mutable_struct_()));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectAtEnd()) goto success;
        break;
      }

      default: {
      handle_unusual:
        if (tag == 0 ||
            ::google::protobuf::internal::WireFormatLite::GetTagWireType(tag) ==
            ::google::protobuf::internal::WireFormatLite::WIRETYPE_END_GROUP) {
          goto success;
        }
        DO_(::google::protobuf::internal::WireFormatLite::SkipField(input, tag));
        break;
      }
    }
  }
success:
  // @@protoc_insertion_point(parse_success:google.protobuf.testing.StructMessage)
  return true;
failure:
  // @@protoc_insertion_point(parse_failure:google.protobuf.testing.StructMessage)
  return false;
#undef DO_
}

void StructMessage::SerializeWithCachedSizes(
    ::google::protobuf::io::CodedOutputStream* output) const {
  // @@protoc_insertion_point(serialize_start:google.protobuf.testing.StructMessage)
  // optional .google.protobuf.Struct struct = 1;
  if (this->has_struct_()) {
    ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
      1, *this->struct__, output);
  }

  // @@protoc_insertion_point(serialize_end:google.protobuf.testing.StructMessage)
}

::google::protobuf::uint8* StructMessage::InternalSerializeWithCachedSizesToArray(
    bool deterministic, ::google::protobuf::uint8* target) const {
  (void)deterministic; // Unused
  // @@protoc_insertion_point(serialize_to_array_start:google.protobuf.testing.StructMessage)
  // optional .google.protobuf.Struct struct = 1;
  if (this->has_struct_()) {
    target = ::google::protobuf::internal::WireFormatLite::
      InternalWriteMessageNoVirtualToArray(
        1, *this->struct__, false, target);
  }

  // @@protoc_insertion_point(serialize_to_array_end:google.protobuf.testing.StructMessage)
  return target;
}

size_t StructMessage::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:google.protobuf.testing.StructMessage)
  size_t total_size = 0;

  // optional .google.protobuf.Struct struct = 1;
  if (this->has_struct_()) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::MessageSizeNoVirtual(
        *this->struct__);
  }

  int cached_size = ::google::protobuf::internal::ToCachedSize(total_size);
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = cached_size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
  return total_size;
}

void StructMessage::MergeFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_merge_from_start:google.protobuf.testing.StructMessage)
  if (GOOGLE_PREDICT_FALSE(&from == this)) MergeFromFail(__LINE__);
  const StructMessage* source =
      ::google::protobuf::internal::DynamicCastToGenerated<const StructMessage>(
          &from);
  if (source == NULL) {
  // @@protoc_insertion_point(generalized_merge_from_cast_fail:google.protobuf.testing.StructMessage)
    ::google::protobuf::internal::ReflectionOps::Merge(from, this);
  } else {
  // @@protoc_insertion_point(generalized_merge_from_cast_success:google.protobuf.testing.StructMessage)
    UnsafeMergeFrom(*source);
  }
}

void StructMessage::MergeFrom(const StructMessage& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:google.protobuf.testing.StructMessage)
  if (GOOGLE_PREDICT_TRUE(&from != this)) {
    UnsafeMergeFrom(from);
  } else {
    MergeFromFail(__LINE__);
  }
}

void StructMessage::UnsafeMergeFrom(const StructMessage& from) {
  GOOGLE_DCHECK(&from != this);
  if (from.has_struct_()) {
    mutable_struct_()->::google::protobuf::Struct::MergeFrom(from.struct_());
  }
}

void StructMessage::CopyFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_copy_from_start:google.protobuf.testing.StructMessage)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

void StructMessage::CopyFrom(const StructMessage& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:google.protobuf.testing.StructMessage)
  if (&from == this) return;
  Clear();
  UnsafeMergeFrom(from);
}

bool StructMessage::IsInitialized() const {

  return true;
}

void StructMessage::Swap(StructMessage* other) {
  if (other == this) return;
  InternalSwap(other);
}
void StructMessage::InternalSwap(StructMessage* other) {
  std::swap(struct__, other->struct__);
  _internal_metadata_.Swap(&other->_internal_metadata_);
  std::swap(_cached_size_, other->_cached_size_);
}

::google::protobuf::Metadata StructMessage::GetMetadata() const {
  protobuf_AssignDescriptorsOnce();
  ::google::protobuf::Metadata metadata;
  metadata.descriptor = StructMessage_descriptor_;
  metadata.reflection = StructMessage_reflection_;
  return metadata;
}

#if PROTOBUF_INLINE_NOT_IN_HEADERS
// StructMessage

// optional .google.protobuf.Struct struct = 1;
bool StructMessage::has_struct_() const {
  return this != internal_default_instance() && struct__ != NULL;
}
void StructMessage::clear_struct_() {
  if (GetArenaNoVirtual() == NULL && struct__ != NULL) delete struct__;
  struct__ = NULL;
}
const ::google::protobuf::Struct& StructMessage::struct_() const {
  // @@protoc_insertion_point(field_get:google.protobuf.testing.StructMessage.struct)
  return struct__ != NULL ? *struct__
                         : *::google::protobuf::Struct::internal_default_instance();
}
::google::protobuf::Struct* StructMessage::mutable_struct_() {
  
  if (struct__ == NULL) {
    struct__ = new ::google::protobuf::Struct;
  }
  // @@protoc_insertion_point(field_mutable:google.protobuf.testing.StructMessage.struct)
  return struct__;
}
::google::protobuf::Struct* StructMessage::release_struct_() {
  // @@protoc_insertion_point(field_release:google.protobuf.testing.StructMessage.struct)
  
  ::google::protobuf::Struct* temp = struct__;
  struct__ = NULL;
  return temp;
}
void StructMessage::set_allocated_struct_(::google::protobuf::Struct* struct_) {
  delete struct__;
  if (struct_ != NULL && struct_->GetArena() != NULL) {
    ::google::protobuf::Struct* new_struct_ = new ::google::protobuf::Struct;
    new_struct_->CopyFrom(*struct_);
    struct_ = new_struct_;
  }
  struct__ = struct_;
  if (struct_) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_set_allocated:google.protobuf.testing.StructMessage.struct)
}

inline const StructMessage* StructMessage::internal_default_instance() {
  return &StructMessage_default_instance_.get();
}
#endif  // PROTOBUF_INLINE_NOT_IN_HEADERS

// ===================================================================

#if !defined(_MSC_VER) || _MSC_VER >= 1900
const int ValueMessage::kValueFieldNumber;
#endif  // !defined(_MSC_VER) || _MSC_VER >= 1900

ValueMessage::ValueMessage()
  : ::google::protobuf::Message(), _internal_metadata_(NULL) {
  if (this != internal_default_instance()) protobuf_InitDefaults_google_2fprotobuf_2futil_2finternal_2ftestdata_2fdefault_5fvalue_2eproto();
  SharedCtor();
  // @@protoc_insertion_point(constructor:google.protobuf.testing.ValueMessage)
}

void ValueMessage::InitAsDefaultInstance() {
  value_ = const_cast< ::google::protobuf::Value*>(
      ::google::protobuf::Value::internal_default_instance());
}

ValueMessage::ValueMessage(const ValueMessage& from)
  : ::google::protobuf::Message(),
    _internal_metadata_(NULL) {
  SharedCtor();
  UnsafeMergeFrom(from);
  // @@protoc_insertion_point(copy_constructor:google.protobuf.testing.ValueMessage)
}

void ValueMessage::SharedCtor() {
  value_ = NULL;
  _cached_size_ = 0;
}

ValueMessage::~ValueMessage() {
  // @@protoc_insertion_point(destructor:google.protobuf.testing.ValueMessage)
  SharedDtor();
}

void ValueMessage::SharedDtor() {
  if (this != &ValueMessage_default_instance_.get()) {
    delete value_;
  }
}

void ValueMessage::SetCachedSize(int size) const {
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
}
const ::google::protobuf::Descriptor* ValueMessage::descriptor() {
  protobuf_AssignDescriptorsOnce();
  return ValueMessage_descriptor_;
}

const ValueMessage& ValueMessage::default_instance() {
  protobuf_InitDefaults_google_2fprotobuf_2futil_2finternal_2ftestdata_2fdefault_5fvalue_2eproto();
  return *internal_default_instance();
}

::google::protobuf::internal::ExplicitlyConstructed<ValueMessage> ValueMessage_default_instance_;

ValueMessage* ValueMessage::New(::google::protobuf::Arena* arena) const {
  ValueMessage* n = new ValueMessage;
  if (arena != NULL) {
    arena->Own(n);
  }
  return n;
}

void ValueMessage::Clear() {
// @@protoc_insertion_point(message_clear_start:google.protobuf.testing.ValueMessage)
  if (GetArenaNoVirtual() == NULL && value_ != NULL) delete value_;
  value_ = NULL;
}

bool ValueMessage::MergePartialFromCodedStream(
    ::google::protobuf::io::CodedInputStream* input) {
#define DO_(EXPRESSION) if (!GOOGLE_PREDICT_TRUE(EXPRESSION)) goto failure
  ::google::protobuf::uint32 tag;
  // @@protoc_insertion_point(parse_start:google.protobuf.testing.ValueMessage)
  for (;;) {
    ::std::pair< ::google::protobuf::uint32, bool> p = input->ReadTagWithCutoff(127);
    tag = p.first;
    if (!p.second) goto handle_unusual;
    switch (::google::protobuf::internal::WireFormatLite::GetTagFieldNumber(tag)) {
      // optional .google.protobuf.Value value = 1;
      case 1: {
        if (tag == 10) {
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessageNoVirtual(
               input, mutable_value()));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectAtEnd()) goto success;
        break;
      }

      default: {
      handle_unusual:
        if (tag == 0 ||
            ::google::protobuf::internal::WireFormatLite::GetTagWireType(tag) ==
            ::google::protobuf::internal::WireFormatLite::WIRETYPE_END_GROUP) {
          goto success;
        }
        DO_(::google::protobuf::internal::WireFormatLite::SkipField(input, tag));
        break;
      }
    }
  }
success:
  // @@protoc_insertion_point(parse_success:google.protobuf.testing.ValueMessage)
  return true;
failure:
  // @@protoc_insertion_point(parse_failure:google.protobuf.testing.ValueMessage)
  return false;
#undef DO_
}

void ValueMessage::SerializeWithCachedSizes(
    ::google::protobuf::io::CodedOutputStream* output) const {
  // @@protoc_insertion_point(serialize_start:google.protobuf.testing.ValueMessage)
  // optional .google.protobuf.Value value = 1;
  if (this->has_value()) {
    ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
      1, *this->value_, output);
  }

  // @@protoc_insertion_point(serialize_end:google.protobuf.testing.ValueMessage)
}

::google::protobuf::uint8* ValueMessage::InternalSerializeWithCachedSizesToArray(
    bool deterministic, ::google::protobuf::uint8* target) const {
  (void)deterministic; // Unused
  // @@protoc_insertion_point(serialize_to_array_start:google.protobuf.testing.ValueMessage)
  // optional .google.protobuf.Value value = 1;
  if (this->has_value()) {
    target = ::google::protobuf::internal::WireFormatLite::
      InternalWriteMessageNoVirtualToArray(
        1, *this->value_, false, target);
  }

  // @@protoc_insertion_point(serialize_to_array_end:google.protobuf.testing.ValueMessage)
  return target;
}

size_t ValueMessage::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:google.protobuf.testing.ValueMessage)
  size_t total_size = 0;

  // optional .google.protobuf.Value value = 1;
  if (this->has_value()) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::MessageSizeNoVirtual(
        *this->value_);
  }

  int cached_size = ::google::protobuf::internal::ToCachedSize(total_size);
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = cached_size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
  return total_size;
}

void ValueMessage::MergeFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_merge_from_start:google.protobuf.testing.ValueMessage)
  if (GOOGLE_PREDICT_FALSE(&from == this)) MergeFromFail(__LINE__);
  const ValueMessage* source =
      ::google::protobuf::internal::DynamicCastToGenerated<const ValueMessage>(
          &from);
  if (source == NULL) {
  // @@protoc_insertion_point(generalized_merge_from_cast_fail:google.protobuf.testing.ValueMessage)
    ::google::protobuf::internal::ReflectionOps::Merge(from, this);
  } else {
  // @@protoc_insertion_point(generalized_merge_from_cast_success:google.protobuf.testing.ValueMessage)
    UnsafeMergeFrom(*source);
  }
}

void ValueMessage::MergeFrom(const ValueMessage& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:google.protobuf.testing.ValueMessage)
  if (GOOGLE_PREDICT_TRUE(&from != this)) {
    UnsafeMergeFrom(from);
  } else {
    MergeFromFail(__LINE__);
  }
}

void ValueMessage::UnsafeMergeFrom(const ValueMessage& from) {
  GOOGLE_DCHECK(&from != this);
  if (from.has_value()) {
    mutable_value()->::google::protobuf::Value::MergeFrom(from.value());
  }
}

void ValueMessage::CopyFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_copy_from_start:google.protobuf.testing.ValueMessage)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

void ValueMessage::CopyFrom(const ValueMessage& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:google.protobuf.testing.ValueMessage)
  if (&from == this) return;
  Clear();
  UnsafeMergeFrom(from);
}

bool ValueMessage::IsInitialized() const {

  return true;
}

void ValueMessage::Swap(ValueMessage* other) {
  if (other == this) return;
  InternalSwap(other);
}
void ValueMessage::InternalSwap(ValueMessage* other) {
  std::swap(value_, other->value_);
  _internal_metadata_.Swap(&other->_internal_metadata_);
  std::swap(_cached_size_, other->_cached_size_);
}

::google::protobuf::Metadata ValueMessage::GetMetadata() const {
  protobuf_AssignDescriptorsOnce();
  ::google::protobuf::Metadata metadata;
  metadata.descriptor = ValueMessage_descriptor_;
  metadata.reflection = ValueMessage_reflection_;
  return metadata;
}

#if PROTOBUF_INLINE_NOT_IN_HEADERS
// ValueMessage

// optional .google.protobuf.Value value = 1;
bool ValueMessage::has_value() const {
  return this != internal_default_instance() && value_ != NULL;
}
void ValueMessage::clear_value() {
  if (GetArenaNoVirtual() == NULL && value_ != NULL) delete value_;
  value_ = NULL;
}
const ::google::protobuf::Value& ValueMessage::value() const {
  // @@protoc_insertion_point(field_get:google.protobuf.testing.ValueMessage.value)
  return value_ != NULL ? *value_
                         : *::google::protobuf::Value::internal_default_instance();
}
::google::protobuf::Value* ValueMessage::mutable_value() {
  
  if (value_ == NULL) {
    value_ = new ::google::protobuf::Value;
  }
  // @@protoc_insertion_point(field_mutable:google.protobuf.testing.ValueMessage.value)
  return value_;
}
::google::protobuf::Value* ValueMessage::release_value() {
  // @@protoc_insertion_point(field_release:google.protobuf.testing.ValueMessage.value)
  
  ::google::protobuf::Value* temp = value_;
  value_ = NULL;
  return temp;
}
void ValueMessage::set_allocated_value(::google::protobuf::Value* value) {
  delete value_;
  if (value != NULL && value->GetArena() != NULL) {
    ::google::protobuf::Value* new_value = new ::google::protobuf::Value;
    new_value->CopyFrom(*value);
    value = new_value;
  }
  value_ = value;
  if (value) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_set_allocated:google.protobuf.testing.ValueMessage.value)
}

inline const ValueMessage* ValueMessage::internal_default_instance() {
  return &ValueMessage_default_instance_.get();
}
#endif  // PROTOBUF_INLINE_NOT_IN_HEADERS

// ===================================================================

#if !defined(_MSC_VER) || _MSC_VER >= 1900
const int ListValueMessage::kShoppingListFieldNumber;
#endif  // !defined(_MSC_VER) || _MSC_VER >= 1900

ListValueMessage::ListValueMessage()
  : ::google::protobuf::Message(), _internal_metadata_(NULL) {
  if (this != internal_default_instance()) protobuf_InitDefaults_google_2fprotobuf_2futil_2finternal_2ftestdata_2fdefault_5fvalue_2eproto();
  SharedCtor();
  // @@protoc_insertion_point(constructor:google.protobuf.testing.ListValueMessage)
}

void ListValueMessage::InitAsDefaultInstance() {
  shopping_list_ = const_cast< ::google::protobuf::ListValue*>(
      ::google::protobuf::ListValue::internal_default_instance());
}

ListValueMessage::ListValueMessage(const ListValueMessage& from)
  : ::google::protobuf::Message(),
    _internal_metadata_(NULL) {
  SharedCtor();
  UnsafeMergeFrom(from);
  // @@protoc_insertion_point(copy_constructor:google.protobuf.testing.ListValueMessage)
}

void ListValueMessage::SharedCtor() {
  shopping_list_ = NULL;
  _cached_size_ = 0;
}

ListValueMessage::~ListValueMessage() {
  // @@protoc_insertion_point(destructor:google.protobuf.testing.ListValueMessage)
  SharedDtor();
}

void ListValueMessage::SharedDtor() {
  if (this != &ListValueMessage_default_instance_.get()) {
    delete shopping_list_;
  }
}

void ListValueMessage::SetCachedSize(int size) const {
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
}
const ::google::protobuf::Descriptor* ListValueMessage::descriptor() {
  protobuf_AssignDescriptorsOnce();
  return ListValueMessage_descriptor_;
}

const ListValueMessage& ListValueMessage::default_instance() {
  protobuf_InitDefaults_google_2fprotobuf_2futil_2finternal_2ftestdata_2fdefault_5fvalue_2eproto();
  return *internal_default_instance();
}

::google::protobuf::internal::ExplicitlyConstructed<ListValueMessage> ListValueMessage_default_instance_;

ListValueMessage* ListValueMessage::New(::google::protobuf::Arena* arena) const {
  ListValueMessage* n = new ListValueMessage;
  if (arena != NULL) {
    arena->Own(n);
  }
  return n;
}

void ListValueMessage::Clear() {
// @@protoc_insertion_point(message_clear_start:google.protobuf.testing.ListValueMessage)
  if (GetArenaNoVirtual() == NULL && shopping_list_ != NULL) delete shopping_list_;
  shopping_list_ = NULL;
}

bool ListValueMessage::MergePartialFromCodedStream(
    ::google::protobuf::io::CodedInputStream* input) {
#define DO_(EXPRESSION) if (!GOOGLE_PREDICT_TRUE(EXPRESSION)) goto failure
  ::google::protobuf::uint32 tag;
  // @@protoc_insertion_point(parse_start:google.protobuf.testing.ListValueMessage)
  for (;;) {
    ::std::pair< ::google::protobuf::uint32, bool> p = input->ReadTagWithCutoff(127);
    tag = p.first;
    if (!p.second) goto handle_unusual;
    switch (::google::protobuf::internal::WireFormatLite::GetTagFieldNumber(tag)) {
      // optional .google.protobuf.ListValue shopping_list = 1;
      case 1: {
        if (tag == 10) {
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessageNoVirtual(
               input, mutable_shopping_list()));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectAtEnd()) goto success;
        break;
      }

      default: {
      handle_unusual:
        if (tag == 0 ||
            ::google::protobuf::internal::WireFormatLite::GetTagWireType(tag) ==
            ::google::protobuf::internal::WireFormatLite::WIRETYPE_END_GROUP) {
          goto success;
        }
        DO_(::google::protobuf::internal::WireFormatLite::SkipField(input, tag));
        break;
      }
    }
  }
success:
  // @@protoc_insertion_point(parse_success:google.protobuf.testing.ListValueMessage)
  return true;
failure:
  // @@protoc_insertion_point(parse_failure:google.protobuf.testing.ListValueMessage)
  return false;
#undef DO_
}

void ListValueMessage::SerializeWithCachedSizes(
    ::google::protobuf::io::CodedOutputStream* output) const {
  // @@protoc_insertion_point(serialize_start:google.protobuf.testing.ListValueMessage)
  // optional .google.protobuf.ListValue shopping_list = 1;
  if (this->has_shopping_list()) {
    ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
      1, *this->shopping_list_, output);
  }

  // @@protoc_insertion_point(serialize_end:google.protobuf.testing.ListValueMessage)
}

::google::protobuf::uint8* ListValueMessage::InternalSerializeWithCachedSizesToArray(
    bool deterministic, ::google::protobuf::uint8* target) const {
  (void)deterministic; // Unused
  // @@protoc_insertion_point(serialize_to_array_start:google.protobuf.testing.ListValueMessage)
  // optional .google.protobuf.ListValue shopping_list = 1;
  if (this->has_shopping_list()) {
    target = ::google::protobuf::internal::WireFormatLite::
      InternalWriteMessageNoVirtualToArray(
        1, *this->shopping_list_, false, target);
  }

  // @@protoc_insertion_point(serialize_to_array_end:google.protobuf.testing.ListValueMessage)
  return target;
}

size_t ListValueMessage::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:google.protobuf.testing.ListValueMessage)
  size_t total_size = 0;

  // optional .google.protobuf.ListValue shopping_list = 1;
  if (this->has_shopping_list()) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::MessageSizeNoVirtual(
        *this->shopping_list_);
  }

  int cached_size = ::google::protobuf::internal::ToCachedSize(total_size);
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = cached_size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
  return total_size;
}

void ListValueMessage::MergeFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_merge_from_start:google.protobuf.testing.ListValueMessage)
  if (GOOGLE_PREDICT_FALSE(&from == this)) MergeFromFail(__LINE__);
  const ListValueMessage* source =
      ::google::protobuf::internal::DynamicCastToGenerated<const ListValueMessage>(
          &from);
  if (source == NULL) {
  // @@protoc_insertion_point(generalized_merge_from_cast_fail:google.protobuf.testing.ListValueMessage)
    ::google::protobuf::internal::ReflectionOps::Merge(from, this);
  } else {
  // @@protoc_insertion_point(generalized_merge_from_cast_success:google.protobuf.testing.ListValueMessage)
    UnsafeMergeFrom(*source);
  }
}

void ListValueMessage::MergeFrom(const ListValueMessage& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:google.protobuf.testing.ListValueMessage)
  if (GOOGLE_PREDICT_TRUE(&from != this)) {
    UnsafeMergeFrom(from);
  } else {
    MergeFromFail(__LINE__);
  }
}

void ListValueMessage::UnsafeMergeFrom(const ListValueMessage& from) {
  GOOGLE_DCHECK(&from != this);
  if (from.has_shopping_list()) {
    mutable_shopping_list()->::google::protobuf::ListValue::MergeFrom(from.shopping_list());
  }
}

void ListValueMessage::CopyFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_copy_from_start:google.protobuf.testing.ListValueMessage)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

void ListValueMessage::CopyFrom(const ListValueMessage& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:google.protobuf.testing.ListValueMessage)
  if (&from == this) return;
  Clear();
  UnsafeMergeFrom(from);
}

bool ListValueMessage::IsInitialized() const {

  return true;
}

void ListValueMessage::Swap(ListValueMessage* other) {
  if (other == this) return;
  InternalSwap(other);
}
void ListValueMessage::InternalSwap(ListValueMessage* other) {
  std::swap(shopping_list_, other->shopping_list_);
  _internal_metadata_.Swap(&other->_internal_metadata_);
  std::swap(_cached_size_, other->_cached_size_);
}

::google::protobuf::Metadata ListValueMessage::GetMetadata() const {
  protobuf_AssignDescriptorsOnce();
  ::google::protobuf::Metadata metadata;
  metadata.descriptor = ListValueMessage_descriptor_;
  metadata.reflection = ListValueMessage_reflection_;
  return metadata;
}

#if PROTOBUF_INLINE_NOT_IN_HEADERS
// ListValueMessage

// optional .google.protobuf.ListValue shopping_list = 1;
bool ListValueMessage::has_shopping_list() const {
  return this != internal_default_instance() && shopping_list_ != NULL;
}
void ListValueMessage::clear_shopping_list() {
  if (GetArenaNoVirtual() == NULL && shopping_list_ != NULL) delete shopping_list_;
  shopping_list_ = NULL;
}
const ::google::protobuf::ListValue& ListValueMessage::shopping_list() const {
  // @@protoc_insertion_point(field_get:google.protobuf.testing.ListValueMessage.shopping_list)
  return shopping_list_ != NULL ? *shopping_list_
                         : *::google::protobuf::ListValue::internal_default_instance();
}
::google::protobuf::ListValue* ListValueMessage::mutable_shopping_list() {
  
  if (shopping_list_ == NULL) {
    shopping_list_ = new ::google::protobuf::ListValue;
  }
  // @@protoc_insertion_point(field_mutable:google.protobuf.testing.ListValueMessage.shopping_list)
  return shopping_list_;
}
::google::protobuf::ListValue* ListValueMessage::release_shopping_list() {
  // @@protoc_insertion_point(field_release:google.protobuf.testing.ListValueMessage.shopping_list)
  
  ::google::protobuf::ListValue* temp = shopping_list_;
  shopping_list_ = NULL;
  return temp;
}
void ListValueMessage::set_allocated_shopping_list(::google::protobuf::ListValue* shopping_list) {
  delete shopping_list_;
  if (shopping_list != NULL && shopping_list->GetArena() != NULL) {
    ::google::protobuf::ListValue* new_shopping_list = new ::google::protobuf::ListValue;
    new_shopping_list->CopyFrom(*shopping_list);
    shopping_list = new_shopping_list;
  }
  shopping_list_ = shopping_list;
  if (shopping_list) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_set_allocated:google.protobuf.testing.ListValueMessage.shopping_list)
}

inline const ListValueMessage* ListValueMessage::internal_default_instance() {
  return &ListValueMessage_default_instance_.get();
}
#endif  // PROTOBUF_INLINE_NOT_IN_HEADERS

// ===================================================================

#if !defined(_MSC_VER) || _MSC_VER >= 1900
const int RequestMessage::kContentFieldNumber;
#endif  // !defined(_MSC_VER) || _MSC_VER >= 1900

RequestMessage::RequestMessage()
  : ::google::protobuf::Message(), _internal_metadata_(NULL) {
  if (this != internal_default_instance()) protobuf_InitDefaults_google_2fprotobuf_2futil_2finternal_2ftestdata_2fdefault_5fvalue_2eproto();
  SharedCtor();
  // @@protoc_insertion_point(constructor:google.protobuf.testing.RequestMessage)
}

void RequestMessage::InitAsDefaultInstance() {
}

RequestMessage::RequestMessage(const RequestMessage& from)
  : ::google::protobuf::Message(),
    _internal_metadata_(NULL) {
  SharedCtor();
  UnsafeMergeFrom(from);
  // @@protoc_insertion_point(copy_constructor:google.protobuf.testing.RequestMessage)
}

void RequestMessage::SharedCtor() {
  content_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  _cached_size_ = 0;
}

RequestMessage::~RequestMessage() {
  // @@protoc_insertion_point(destructor:google.protobuf.testing.RequestMessage)
  SharedDtor();
}

void RequestMessage::SharedDtor() {
  content_.DestroyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}

void RequestMessage::SetCachedSize(int size) const {
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
}
const ::google::protobuf::Descriptor* RequestMessage::descriptor() {
  protobuf_AssignDescriptorsOnce();
  return RequestMessage_descriptor_;
}

const RequestMessage& RequestMessage::default_instance() {
  protobuf_InitDefaults_google_2fprotobuf_2futil_2finternal_2ftestdata_2fdefault_5fvalue_2eproto();
  return *internal_default_instance();
}

::google::protobuf::internal::ExplicitlyConstructed<RequestMessage> RequestMessage_default_instance_;

RequestMessage* RequestMessage::New(::google::protobuf::Arena* arena) const {
  RequestMessage* n = new RequestMessage;
  if (arena != NULL) {
    arena->Own(n);
  }
  return n;
}

void RequestMessage::Clear() {
// @@protoc_insertion_point(message_clear_start:google.protobuf.testing.RequestMessage)
  content_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}

bool RequestMessage::MergePartialFromCodedStream(
    ::google::protobuf::io::CodedInputStream* input) {
#define DO_(EXPRESSION) if (!GOOGLE_PREDICT_TRUE(EXPRESSION)) goto failure
  ::google::protobuf::uint32 tag;
  // @@protoc_insertion_point(parse_start:google.protobuf.testing.RequestMessage)
  for (;;) {
    ::std::pair< ::google::protobuf::uint32, bool> p = input->ReadTagWithCutoff(127);
    tag = p.first;
    if (!p.second) goto handle_unusual;
    switch (::google::protobuf::internal::WireFormatLite::GetTagFieldNumber(tag)) {
      // optional string content = 1;
      case 1: {
        if (tag == 10) {
          DO_(::google::protobuf::internal::WireFormatLite::ReadString(
                input, this->mutable_content()));
          DO_(::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
            this->content().data(), this->content().length(),
            ::google::protobuf::internal::WireFormatLite::PARSE,
            "google.protobuf.testing.RequestMessage.content"));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectAtEnd()) goto success;
        break;
      }

      default: {
      handle_unusual:
        if (tag == 0 ||
            ::google::protobuf::internal::WireFormatLite::GetTagWireType(tag) ==
            ::google::protobuf::internal::WireFormatLite::WIRETYPE_END_GROUP) {
          goto success;
        }
        DO_(::google::protobuf::internal::WireFormatLite::SkipField(input, tag));
        break;
      }
    }
  }
success:
  // @@protoc_insertion_point(parse_success:google.protobuf.testing.RequestMessage)
  return true;
failure:
  // @@protoc_insertion_point(parse_failure:google.protobuf.testing.RequestMessage)
  return false;
#undef DO_
}

void RequestMessage::SerializeWithCachedSizes(
    ::google::protobuf::io::CodedOutputStream* output) const {
  // @@protoc_insertion_point(serialize_start:google.protobuf.testing.RequestMessage)
  // optional string content = 1;
  if (this->content().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->content().data(), this->content().length(),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "google.protobuf.testing.RequestMessage.content");
    ::google::protobuf::internal::WireFormatLite::WriteStringMaybeAliased(
      1, this->content(), output);
  }

  // @@protoc_insertion_point(serialize_end:google.protobuf.testing.RequestMessage)
}

::google::protobuf::uint8* RequestMessage::InternalSerializeWithCachedSizesToArray(
    bool deterministic, ::google::protobuf::uint8* target) const {
  (void)deterministic; // Unused
  // @@protoc_insertion_point(serialize_to_array_start:google.protobuf.testing.RequestMessage)
  // optional string content = 1;
  if (this->content().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->content().data(), this->content().length(),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "google.protobuf.testing.RequestMessage.content");
    target =
      ::google::protobuf::internal::WireFormatLite::WriteStringToArray(
        1, this->content(), target);
  }

  // @@protoc_insertion_point(serialize_to_array_end:google.protobuf.testing.RequestMessage)
  return target;
}

size_t RequestMessage::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:google.protobuf.testing.RequestMessage)
  size_t total_size = 0;

  // optional string content = 1;
  if (this->content().size() > 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::StringSize(
        this->content());
  }

  int cached_size = ::google::protobuf::internal::ToCachedSize(total_size);
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = cached_size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
  return total_size;
}

void RequestMessage::MergeFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_merge_from_start:google.protobuf.testing.RequestMessage)
  if (GOOGLE_PREDICT_FALSE(&from == this)) MergeFromFail(__LINE__);
  const RequestMessage* source =
      ::google::protobuf::internal::DynamicCastToGenerated<const RequestMessage>(
          &from);
  if (source == NULL) {
  // @@protoc_insertion_point(generalized_merge_from_cast_fail:google.protobuf.testing.RequestMessage)
    ::google::protobuf::internal::ReflectionOps::Merge(from, this);
  } else {
  // @@protoc_insertion_point(generalized_merge_from_cast_success:google.protobuf.testing.RequestMessage)
    UnsafeMergeFrom(*source);
  }
}

void RequestMessage::MergeFrom(const RequestMessage& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:google.protobuf.testing.RequestMessage)
  if (GOOGLE_PREDICT_TRUE(&from != this)) {
    UnsafeMergeFrom(from);
  } else {
    MergeFromFail(__LINE__);
  }
}

void RequestMessage::UnsafeMergeFrom(const RequestMessage& from) {
  GOOGLE_DCHECK(&from != this);
  if (from.content().size() > 0) {

    content_.AssignWithDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), from.content_);
  }
}

void RequestMessage::CopyFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_copy_from_start:google.protobuf.testing.RequestMessage)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

void RequestMessage::CopyFrom(const RequestMessage& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:google.protobuf.testing.RequestMessage)
  if (&from == this) return;
  Clear();
  UnsafeMergeFrom(from);
}

bool RequestMessage::IsInitialized() const {

  return true;
}

void RequestMessage::Swap(RequestMessage* other) {
  if (other == this) return;
  InternalSwap(other);
}
void RequestMessage::InternalSwap(RequestMessage* other) {
  content_.Swap(&other->content_);
  _internal_metadata_.Swap(&other->_internal_metadata_);
  std::swap(_cached_size_, other->_cached_size_);
}

::google::protobuf::Metadata RequestMessage::GetMetadata() const {
  protobuf_AssignDescriptorsOnce();
  ::google::protobuf::Metadata metadata;
  metadata.descriptor = RequestMessage_descriptor_;
  metadata.reflection = RequestMessage_reflection_;
  return metadata;
}

#if PROTOBUF_INLINE_NOT_IN_HEADERS
// RequestMessage

// optional string content = 1;
void RequestMessage::clear_content() {
  content_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
const ::std::string& RequestMessage::content() const {
  // @@protoc_insertion_point(field_get:google.protobuf.testing.RequestMessage.content)
  return content_.GetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
void RequestMessage::set_content(const ::std::string& value) {
  
  content_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:google.protobuf.testing.RequestMessage.content)
}
void RequestMessage::set_content(const char* value) {
  
  content_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:google.protobuf.testing.RequestMessage.content)
}
void RequestMessage::set_content(const char* value, size_t size) {
  
  content_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:google.protobuf.testing.RequestMessage.content)
}
::std::string* RequestMessage::mutable_content() {
  
  // @@protoc_insertion_point(field_mutable:google.protobuf.testing.RequestMessage.content)
  return content_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
::std::string* RequestMessage::release_content() {
  // @@protoc_insertion_point(field_release:google.protobuf.testing.RequestMessage.content)
  
  return content_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
void RequestMessage::set_allocated_content(::std::string* content) {
  if (content != NULL) {
    
  } else {
    
  }
  content_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), content);
  // @@protoc_insertion_point(field_set_allocated:google.protobuf.testing.RequestMessage.content)
}

inline const RequestMessage* RequestMessage::internal_default_instance() {
  return &RequestMessage_default_instance_.get();
}
#endif  // PROTOBUF_INLINE_NOT_IN_HEADERS

// ===================================================================

#if !defined(_MSC_VER) || _MSC_VER >= 1900
const int AnyMessage::kAnyFieldNumber;
const int AnyMessage::kDataFieldNumber;
#endif  // !defined(_MSC_VER) || _MSC_VER >= 1900

AnyMessage::AnyMessage()
  : ::google::protobuf::Message(), _internal_metadata_(NULL) {
  if (this != internal_default_instance()) protobuf_InitDefaults_google_2fprotobuf_2futil_2finternal_2ftestdata_2fdefault_5fvalue_2eproto();
  SharedCtor();
  // @@protoc_insertion_point(constructor:google.protobuf.testing.AnyMessage)
}

void AnyMessage::InitAsDefaultInstance() {
  any_ = const_cast< ::google::protobuf::Any*>(
      ::google::protobuf::Any::internal_default_instance());
  data_ = const_cast< ::google::protobuf::testing::AnyData*>(
      ::google::protobuf::testing::AnyData::internal_default_instance());
}

AnyMessage::AnyMessage(const AnyMessage& from)
  : ::google::protobuf::Message(),
    _internal_metadata_(NULL) {
  SharedCtor();
  UnsafeMergeFrom(from);
  // @@protoc_insertion_point(copy_constructor:google.protobuf.testing.AnyMessage)
}

void AnyMessage::SharedCtor() {
  any_ = NULL;
  data_ = NULL;
  _cached_size_ = 0;
}

AnyMessage::~AnyMessage() {
  // @@protoc_insertion_point(destructor:google.protobuf.testing.AnyMessage)
  SharedDtor();
}

void AnyMessage::SharedDtor() {
  if (this != &AnyMessage_default_instance_.get()) {
    delete any_;
    delete data_;
  }
}

void AnyMessage::SetCachedSize(int size) const {
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
}
const ::google::protobuf::Descriptor* AnyMessage::descriptor() {
  protobuf_AssignDescriptorsOnce();
  return AnyMessage_descriptor_;
}

const AnyMessage& AnyMessage::default_instance() {
  protobuf_InitDefaults_google_2fprotobuf_2futil_2finternal_2ftestdata_2fdefault_5fvalue_2eproto();
  return *internal_default_instance();
}

::google::protobuf::internal::ExplicitlyConstructed<AnyMessage> AnyMessage_default_instance_;

AnyMessage* AnyMessage::New(::google::protobuf::Arena* arena) const {
  AnyMessage* n = new AnyMessage;
  if (arena != NULL) {
    arena->Own(n);
  }
  return n;
}

void AnyMessage::Clear() {
// @@protoc_insertion_point(message_clear_start:google.protobuf.testing.AnyMessage)
  if (GetArenaNoVirtual() == NULL && any_ != NULL) delete any_;
  any_ = NULL;
  if (GetArenaNoVirtual() == NULL && data_ != NULL) delete data_;
  data_ = NULL;
}

bool AnyMessage::MergePartialFromCodedStream(
    ::google::protobuf::io::CodedInputStream* input) {
#define DO_(EXPRESSION) if (!GOOGLE_PREDICT_TRUE(EXPRESSION)) goto failure
  ::google::protobuf::uint32 tag;
  // @@protoc_insertion_point(parse_start:google.protobuf.testing.AnyMessage)
  for (;;) {
    ::std::pair< ::google::protobuf::uint32, bool> p = input->ReadTagWithCutoff(127);
    tag = p.first;
    if (!p.second) goto handle_unusual;
    switch (::google::protobuf::internal::WireFormatLite::GetTagFieldNumber(tag)) {
      // optional .google.protobuf.Any any = 1;
      case 1: {
        if (tag == 10) {
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessageNoVirtual(
               input, mutable_any()));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(18)) goto parse_data;
        break;
      }

      // optional .google.protobuf.testing.AnyData data = 2;
      case 2: {
        if (tag == 18) {
         parse_data:
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessageNoVirtual(
               input, mutable_data()));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectAtEnd()) goto success;
        break;
      }

      default: {
      handle_unusual:
        if (tag == 0 ||
            ::google::protobuf::internal::WireFormatLite::GetTagWireType(tag) ==
            ::google::protobuf::internal::WireFormatLite::WIRETYPE_END_GROUP) {
          goto success;
        }
        DO_(::google::protobuf::internal::WireFormatLite::SkipField(input, tag));
        break;
      }
    }
  }
success:
  // @@protoc_insertion_point(parse_success:google.protobuf.testing.AnyMessage)
  return true;
failure:
  // @@protoc_insertion_point(parse_failure:google.protobuf.testing.AnyMessage)
  return false;
#undef DO_
}

void AnyMessage::SerializeWithCachedSizes(
    ::google::protobuf::io::CodedOutputStream* output) const {
  // @@protoc_insertion_point(serialize_start:google.protobuf.testing.AnyMessage)
  // optional .google.protobuf.Any any = 1;
  if (this->has_any()) {
    ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
      1, *this->any_, output);
  }

  // optional .google.protobuf.testing.AnyData data = 2;
  if (this->has_data()) {
    ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
      2, *this->data_, output);
  }

  // @@protoc_insertion_point(serialize_end:google.protobuf.testing.AnyMessage)
}

::google::protobuf::uint8* AnyMessage::InternalSerializeWithCachedSizesToArray(
    bool deterministic, ::google::protobuf::uint8* target) const {
  (void)deterministic; // Unused
  // @@protoc_insertion_point(serialize_to_array_start:google.protobuf.testing.AnyMessage)
  // optional .google.protobuf.Any any = 1;
  if (this->has_any()) {
    target = ::google::protobuf::internal::WireFormatLite::
      InternalWriteMessageNoVirtualToArray(
        1, *this->any_, false, target);
  }

  // optional .google.protobuf.testing.AnyData data = 2;
  if (this->has_data()) {
    target = ::google::protobuf::internal::WireFormatLite::
      InternalWriteMessageNoVirtualToArray(
        2, *this->data_, false, target);
  }

  // @@protoc_insertion_point(serialize_to_array_end:google.protobuf.testing.AnyMessage)
  return target;
}

size_t AnyMessage::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:google.protobuf.testing.AnyMessage)
  size_t total_size = 0;

  // optional .google.protobuf.Any any = 1;
  if (this->has_any()) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::MessageSizeNoVirtual(
        *this->any_);
  }

  // optional .google.protobuf.testing.AnyData data = 2;
  if (this->has_data()) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::MessageSizeNoVirtual(
        *this->data_);
  }

  int cached_size = ::google::protobuf::internal::ToCachedSize(total_size);
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = cached_size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
  return total_size;
}

void AnyMessage::MergeFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_merge_from_start:google.protobuf.testing.AnyMessage)
  if (GOOGLE_PREDICT_FALSE(&from == this)) MergeFromFail(__LINE__);
  const AnyMessage* source =
      ::google::protobuf::internal::DynamicCastToGenerated<const AnyMessage>(
          &from);
  if (source == NULL) {
  // @@protoc_insertion_point(generalized_merge_from_cast_fail:google.protobuf.testing.AnyMessage)
    ::google::protobuf::internal::ReflectionOps::Merge(from, this);
  } else {
  // @@protoc_insertion_point(generalized_merge_from_cast_success:google.protobuf.testing.AnyMessage)
    UnsafeMergeFrom(*source);
  }
}

void AnyMessage::MergeFrom(const AnyMessage& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:google.protobuf.testing.AnyMessage)
  if (GOOGLE_PREDICT_TRUE(&from != this)) {
    UnsafeMergeFrom(from);
  } else {
    MergeFromFail(__LINE__);
  }
}

void AnyMessage::UnsafeMergeFrom(const AnyMessage& from) {
  GOOGLE_DCHECK(&from != this);
  if (from.has_any()) {
    mutable_any()->::google::protobuf::Any::MergeFrom(from.any());
  }
  if (from.has_data()) {
    mutable_data()->::google::protobuf::testing::AnyData::MergeFrom(from.data());
  }
}

void AnyMessage::CopyFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_copy_from_start:google.protobuf.testing.AnyMessage)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

void AnyMessage::CopyFrom(const AnyMessage& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:google.protobuf.testing.AnyMessage)
  if (&from == this) return;
  Clear();
  UnsafeMergeFrom(from);
}

bool AnyMessage::IsInitialized() const {

  return true;
}

void AnyMessage::Swap(AnyMessage* other) {
  if (other == this) return;
  InternalSwap(other);
}
void AnyMessage::InternalSwap(AnyMessage* other) {
  std::swap(any_, other->any_);
  std::swap(data_, other->data_);
  _internal_metadata_.Swap(&other->_internal_metadata_);
  std::swap(_cached_size_, other->_cached_size_);
}

::google::protobuf::Metadata AnyMessage::GetMetadata() const {
  protobuf_AssignDescriptorsOnce();
  ::google::protobuf::Metadata metadata;
  metadata.descriptor = AnyMessage_descriptor_;
  metadata.reflection = AnyMessage_reflection_;
  return metadata;
}

#if PROTOBUF_INLINE_NOT_IN_HEADERS
// AnyMessage

// optional .google.protobuf.Any any = 1;
bool AnyMessage::has_any() const {
  return this != internal_default_instance() && any_ != NULL;
}
void AnyMessage::clear_any() {
  if (GetArenaNoVirtual() == NULL && any_ != NULL) delete any_;
  any_ = NULL;
}
const ::google::protobuf::Any& AnyMessage::any() const {
  // @@protoc_insertion_point(field_get:google.protobuf.testing.AnyMessage.any)
  return any_ != NULL ? *any_
                         : *::google::protobuf::Any::internal_default_instance();
}
::google::protobuf::Any* AnyMessage::mutable_any() {
  
  if (any_ == NULL) {
    any_ = new ::google::protobuf::Any;
  }
  // @@protoc_insertion_point(field_mutable:google.protobuf.testing.AnyMessage.any)
  return any_;
}
::google::protobuf::Any* AnyMessage::release_any() {
  // @@protoc_insertion_point(field_release:google.protobuf.testing.AnyMessage.any)
  
  ::google::protobuf::Any* temp = any_;
  any_ = NULL;
  return temp;
}
void AnyMessage::set_allocated_any(::google::protobuf::Any* any) {
  delete any_;
  any_ = any;
  if (any) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_set_allocated:google.protobuf.testing.AnyMessage.any)
}

// optional .google.protobuf.testing.AnyData data = 2;
bool AnyMessage::has_data() const {
  return this != internal_default_instance() && data_ != NULL;
}
void AnyMessage::clear_data() {
  if (GetArenaNoVirtual() == NULL && data_ != NULL) delete data_;
  data_ = NULL;
}
const ::google::protobuf::testing::AnyData& AnyMessage::data() const {
  // @@protoc_insertion_point(field_get:google.protobuf.testing.AnyMessage.data)
  return data_ != NULL ? *data_
                         : *::google::protobuf::testing::AnyData::internal_default_instance();
}
::google::protobuf::testing::AnyData* AnyMessage::mutable_data() {
  
  if (data_ == NULL) {
    data_ = new ::google::protobuf::testing::AnyData;
  }
  // @@protoc_insertion_point(field_mutable:google.protobuf.testing.AnyMessage.data)
  return data_;
}
::google::protobuf::testing::AnyData* AnyMessage::release_data() {
  // @@protoc_insertion_point(field_release:google.protobuf.testing.AnyMessage.data)
  
  ::google::protobuf::testing::AnyData* temp = data_;
  data_ = NULL;
  return temp;
}
void AnyMessage::set_allocated_data(::google::protobuf::testing::AnyData* data) {
  delete data_;
  data_ = data;
  if (data) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_set_allocated:google.protobuf.testing.AnyMessage.data)
}

inline const AnyMessage* AnyMessage::internal_default_instance() {
  return &AnyMessage_default_instance_.get();
}
#endif  // PROTOBUF_INLINE_NOT_IN_HEADERS

// ===================================================================

#if !defined(_MSC_VER) || _MSC_VER >= 1900
const int AnyData::kAttrFieldNumber;
const int AnyData::kStrFieldNumber;
const int AnyData::kMsgsFieldNumber;
const int AnyData::kNestedDataFieldNumber;
const int AnyData::kMapDataFieldNumber;
const int AnyData::kStructDataFieldNumber;
const int AnyData::kRepeatedDataFieldNumber;
#endif  // !defined(_MSC_VER) || _MSC_VER >= 1900

AnyData::AnyData()
  : ::google::protobuf::Message(), _internal_metadata_(NULL) {
  if (this != internal_default_instance()) protobuf_InitDefaults_google_2fprotobuf_2futil_2finternal_2ftestdata_2fdefault_5fvalue_2eproto();
  SharedCtor();
  // @@protoc_insertion_point(constructor:google.protobuf.testing.AnyData)
}

void AnyData::InitAsDefaultInstance() {
  nested_data_ = const_cast< ::google::protobuf::testing::AnyData*>(
      ::google::protobuf::testing::AnyData::internal_default_instance());
  struct_data_ = const_cast< ::google::protobuf::Struct*>(
      ::google::protobuf::Struct::internal_default_instance());
}

AnyData::AnyData(const AnyData& from)
  : ::google::protobuf::Message(),
    _internal_metadata_(NULL) {
  SharedCtor();
  UnsafeMergeFrom(from);
  // @@protoc_insertion_point(copy_constructor:google.protobuf.testing.AnyData)
}

void AnyData::SharedCtor() {
  map_data_.SetAssignDescriptorCallback(
      protobuf_AssignDescriptorsOnce);
  map_data_.SetEntryDescriptor(
      &::google::protobuf::testing::AnyData_MapDataEntry_descriptor_);
  str_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  nested_data_ = NULL;
  struct_data_ = NULL;
  attr_ = 0;
  _cached_size_ = 0;
}

AnyData::~AnyData() {
  // @@protoc_insertion_point(destructor:google.protobuf.testing.AnyData)
  SharedDtor();
}

void AnyData::SharedDtor() {
  str_.DestroyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  if (this != &AnyData_default_instance_.get()) {
    delete nested_data_;
    delete struct_data_;
  }
}

void AnyData::SetCachedSize(int size) const {
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
}
const ::google::protobuf::Descriptor* AnyData::descriptor() {
  protobuf_AssignDescriptorsOnce();
  return AnyData_descriptor_;
}

const AnyData& AnyData::default_instance() {
  protobuf_InitDefaults_google_2fprotobuf_2futil_2finternal_2ftestdata_2fdefault_5fvalue_2eproto();
  return *internal_default_instance();
}

::google::protobuf::internal::ExplicitlyConstructed<AnyData> AnyData_default_instance_;

AnyData* AnyData::New(::google::protobuf::Arena* arena) const {
  AnyData* n = new AnyData;
  if (arena != NULL) {
    arena->Own(n);
  }
  return n;
}

void AnyData::Clear() {
// @@protoc_insertion_point(message_clear_start:google.protobuf.testing.AnyData)
  attr_ = 0;
  str_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  if (GetArenaNoVirtual() == NULL && nested_data_ != NULL) delete nested_data_;
  nested_data_ = NULL;
  if (GetArenaNoVirtual() == NULL && struct_data_ != NULL) delete struct_data_;
  struct_data_ = NULL;
  msgs_.Clear();
  map_data_.Clear();
  repeated_data_.Clear();
}

bool AnyData::MergePartialFromCodedStream(
    ::google::protobuf::io::CodedInputStream* input) {
#define DO_(EXPRESSION) if (!GOOGLE_PREDICT_TRUE(EXPRESSION)) goto failure
  ::google::protobuf::uint32 tag;
  // @@protoc_insertion_point(parse_start:google.protobuf.testing.AnyData)
  for (;;) {
    ::std::pair< ::google::protobuf::uint32, bool> p = input->ReadTagWithCutoff(127);
    tag = p.first;
    if (!p.second) goto handle_unusual;
    switch (::google::protobuf::internal::WireFormatLite::GetTagFieldNumber(tag)) {
      // optional int32 attr = 1;
      case 1: {
        if (tag == 8) {

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int32, ::google::protobuf::internal::WireFormatLite::TYPE_INT32>(
                 input, &attr_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(18)) goto parse_str;
        break;
      }

      // optional string str = 2;
      case 2: {
        if (tag == 18) {
         parse_str:
          DO_(::google::protobuf::internal::WireFormatLite::ReadString(
                input, this->mutable_str()));
          DO_(::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
            this->str().data(), this->str().length(),
            ::google::protobuf::internal::WireFormatLite::PARSE,
            "google.protobuf.testing.AnyData.str"));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(26)) goto parse_msgs;
        break;
      }

      // repeated string msgs = 3;
      case 3: {
        if (tag == 26) {
         parse_msgs:
          DO_(::google::protobuf::internal::WireFormatLite::ReadString(
                input, this->add_msgs()));
          DO_(::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
            this->msgs(this->msgs_size() - 1).data(),
            this->msgs(this->msgs_size() - 1).length(),
            ::google::protobuf::internal::WireFormatLite::PARSE,
            "google.protobuf.testing.AnyData.msgs"));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(26)) goto parse_msgs;
        if (input->ExpectTag(34)) goto parse_nested_data;
        break;
      }

      // optional .google.protobuf.testing.AnyData nested_data = 4;
      case 4: {
        if (tag == 34) {
         parse_nested_data:
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessageNoVirtual(
               input, mutable_nested_data()));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(58)) goto parse_map_data;
        break;
      }

      // map<string, string> map_data = 7;
      case 7: {
        if (tag == 58) {
         parse_map_data:
          DO_(input->IncrementRecursionDepth());
         parse_loop_map_data:
          AnyData_MapDataEntry::Parser< ::google::protobuf::internal::MapField<
              ::std::string, ::std::string,
              ::google::protobuf::internal::WireFormatLite::TYPE_STRING,
              ::google::protobuf::internal::WireFormatLite::TYPE_STRING,
              0 >,
            ::google::protobuf::Map< ::std::string, ::std::string > > parser(&map_data_);
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessageNoVirtual(
              input, &parser));
          DO_(::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
            parser.key().data(), parser.key().length(),
            ::google::protobuf::internal::WireFormatLite::PARSE,
            "google.protobuf.testing.AnyData.MapDataEntry.key"));
          DO_(::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
            parser.value().data(), parser.value().length(),
            ::google::protobuf::internal::WireFormatLite::PARSE,
            "google.protobuf.testing.AnyData.MapDataEntry.value"));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(58)) goto parse_loop_map_data;
        input->UnsafeDecrementRecursionDepth();
        if (input->ExpectTag(66)) goto parse_struct_data;
        break;
      }

      // optional .google.protobuf.Struct struct_data = 8;
      case 8: {
        if (tag == 66) {
         parse_struct_data:
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessageNoVirtual(
               input, mutable_struct_data()));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(74)) goto parse_repeated_data;
        break;
      }

      // repeated .google.protobuf.testing.AnyData repeated_data = 9;
      case 9: {
        if (tag == 74) {
         parse_repeated_data:
          DO_(input->IncrementRecursionDepth());
         parse_loop_repeated_data:
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessageNoVirtualNoRecursionDepth(
                input, add_repeated_data()));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(74)) goto parse_loop_repeated_data;
        input->UnsafeDecrementRecursionDepth();
        if (input->ExpectAtEnd()) goto success;
        break;
      }

      default: {
      handle_unusual:
        if (tag == 0 ||
            ::google::protobuf::internal::WireFormatLite::GetTagWireType(tag) ==
            ::google::protobuf::internal::WireFormatLite::WIRETYPE_END_GROUP) {
          goto success;
        }
        DO_(::google::protobuf::internal::WireFormatLite::SkipField(input, tag));
        break;
      }
    }
  }
success:
  // @@protoc_insertion_point(parse_success:google.protobuf.testing.AnyData)
  return true;
failure:
  // @@protoc_insertion_point(parse_failure:google.protobuf.testing.AnyData)
  return false;
#undef DO_
}

void AnyData::SerializeWithCachedSizes(
    ::google::protobuf::io::CodedOutputStream* output) const {
  // @@protoc_insertion_point(serialize_start:google.protobuf.testing.AnyData)
  // optional int32 attr = 1;
  if (this->attr() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt32(1, this->attr(), output);
  }

  // optional string str = 2;
  if (this->str().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->str().data(), this->str().length(),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "google.protobuf.testing.AnyData.str");
    ::google::protobuf::internal::WireFormatLite::WriteStringMaybeAliased(
      2, this->str(), output);
  }

  // repeated string msgs = 3;
  for (int i = 0; i < this->msgs_size(); i++) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->msgs(i).data(), this->msgs(i).length(),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "google.protobuf.testing.AnyData.msgs");
    ::google::protobuf::internal::WireFormatLite::WriteString(
      3, this->msgs(i), output);
  }

  // optional .google.protobuf.testing.AnyData nested_data = 4;
  if (this->has_nested_data()) {
    ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
      4, *this->nested_data_, output);
  }

  // map<string, string> map_data = 7;
  if (!this->map_data().empty()) {
    typedef ::google::protobuf::Map< ::std::string, ::std::string >::const_pointer
        ConstPtr;
    typedef ConstPtr SortItem;
    typedef ::google::protobuf::internal::CompareByDerefFirst<SortItem> Less;
    struct Utf8Check {
      static void Check(ConstPtr p) {
        ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
          p->first.data(), p->first.length(),
          ::google::protobuf::internal::WireFormatLite::SERIALIZE,
          "google.protobuf.testing.AnyData.MapDataEntry.key");
        ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
          p->second.data(), p->second.length(),
          ::google::protobuf::internal::WireFormatLite::SERIALIZE,
          "google.protobuf.testing.AnyData.MapDataEntry.value");
      }
    };

    if (output->IsSerializationDeterminstic() &&
        this->map_data().size() > 1) {
      ::google::protobuf::scoped_array<SortItem> items(
          new SortItem[this->map_data().size()]);
      typedef ::google::protobuf::Map< ::std::string, ::std::string >::size_type size_type;
      size_type n = 0;
      for (::google::protobuf::Map< ::std::string, ::std::string >::const_iterator
          it = this->map_data().begin();
          it != this->map_data().end(); ++it, ++n) {
        items[n] = SortItem(&*it);
      }
      ::std::sort(&items[0], &items[n], Less());
      ::google::protobuf::scoped_ptr<AnyData_MapDataEntry> entry;
      for (size_type i = 0; i < n; i++) {
        entry.reset(map_data_.NewEntryWrapper(
            items[i]->first, items[i]->second));
        ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
            7, *entry, output);
        Utf8Check::Check(items[i]);
      }
    } else {
      ::google::protobuf::scoped_ptr<AnyData_MapDataEntry> entry;
      for (::google::protobuf::Map< ::std::string, ::std::string >::const_iterator
          it = this->map_data().begin();
          it != this->map_data().end(); ++it) {
        entry.reset(map_data_.NewEntryWrapper(
            it->first, it->second));
        ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
            7, *entry, output);
        Utf8Check::Check(&*it);
      }
    }
  }

  // optional .google.protobuf.Struct struct_data = 8;
  if (this->has_struct_data()) {
    ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
      8, *this->struct_data_, output);
  }

  // repeated .google.protobuf.testing.AnyData repeated_data = 9;
  for (unsigned int i = 0, n = this->repeated_data_size(); i < n; i++) {
    ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
      9, this->repeated_data(i), output);
  }

  // @@protoc_insertion_point(serialize_end:google.protobuf.testing.AnyData)
}

::google::protobuf::uint8* AnyData::InternalSerializeWithCachedSizesToArray(
    bool deterministic, ::google::protobuf::uint8* target) const {
  (void)deterministic; // Unused
  // @@protoc_insertion_point(serialize_to_array_start:google.protobuf.testing.AnyData)
  // optional int32 attr = 1;
  if (this->attr() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt32ToArray(1, this->attr(), target);
  }

  // optional string str = 2;
  if (this->str().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->str().data(), this->str().length(),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "google.protobuf.testing.AnyData.str");
    target =
      ::google::protobuf::internal::WireFormatLite::WriteStringToArray(
        2, this->str(), target);
  }

  // repeated string msgs = 3;
  for (int i = 0; i < this->msgs_size(); i++) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->msgs(i).data(), this->msgs(i).length(),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "google.protobuf.testing.AnyData.msgs");
    target = ::google::protobuf::internal::WireFormatLite::
      WriteStringToArray(3, this->msgs(i), target);
  }

  // optional .google.protobuf.testing.AnyData nested_data = 4;
  if (this->has_nested_data()) {
    target = ::google::protobuf::internal::WireFormatLite::
      InternalWriteMessageNoVirtualToArray(
        4, *this->nested_data_, false, target);
  }

  // map<string, string> map_data = 7;
  if (!this->map_data().empty()) {
    typedef ::google::protobuf::Map< ::std::string, ::std::string >::const_pointer
        ConstPtr;
    typedef ConstPtr SortItem;
    typedef ::google::protobuf::internal::CompareByDerefFirst<SortItem> Less;
    struct Utf8Check {
      static void Check(ConstPtr p) {
        ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
          p->first.data(), p->first.length(),
          ::google::protobuf::internal::WireFormatLite::SERIALIZE,
          "google.protobuf.testing.AnyData.MapDataEntry.key");
        ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
          p->second.data(), p->second.length(),
          ::google::protobuf::internal::WireFormatLite::SERIALIZE,
          "google.protobuf.testing.AnyData.MapDataEntry.value");
      }
    };

    if (deterministic &&
        this->map_data().size() > 1) {
      ::google::protobuf::scoped_array<SortItem> items(
          new SortItem[this->map_data().size()]);
      typedef ::google::protobuf::Map< ::std::string, ::std::string >::size_type size_type;
      size_type n = 0;
      for (::google::protobuf::Map< ::std::string, ::std::string >::const_iterator
          it = this->map_data().begin();
          it != this->map_data().end(); ++it, ++n) {
        items[n] = SortItem(&*it);
      }
      ::std::sort(&items[0], &items[n], Less());
      ::google::protobuf::scoped_ptr<AnyData_MapDataEntry> entry;
      for (size_type i = 0; i < n; i++) {
        entry.reset(map_data_.NewEntryWrapper(
            items[i]->first, items[i]->second));
        target = ::google::protobuf::internal::WireFormatLite::
                   InternalWriteMessageNoVirtualToArray(
                       7, *entry, deterministic, target);
;
        Utf8Check::Check(items[i]);
      }
    } else {
      ::google::protobuf::scoped_ptr<AnyData_MapDataEntry> entry;
      for (::google::protobuf::Map< ::std::string, ::std::string >::const_iterator
          it = this->map_data().begin();
          it != this->map_data().end(); ++it) {
        entry.reset(map_data_.NewEntryWrapper(
            it->first, it->second));
        target = ::google::protobuf::internal::WireFormatLite::
                   InternalWriteMessageNoVirtualToArray(
                       7, *entry, deterministic, target);
;
        Utf8Check::Check(&*it);
      }
    }
  }

  // optional .google.protobuf.Struct struct_data = 8;
  if (this->has_struct_data()) {
    target = ::google::protobuf::internal::WireFormatLite::
      InternalWriteMessageNoVirtualToArray(
        8, *this->struct_data_, false, target);
  }

  // repeated .google.protobuf.testing.AnyData repeated_data = 9;
  for (unsigned int i = 0, n = this->repeated_data_size(); i < n; i++) {
    target = ::google::protobuf::internal::WireFormatLite::
      InternalWriteMessageNoVirtualToArray(
        9, this->repeated_data(i), false, target);
  }

  // @@protoc_insertion_point(serialize_to_array_end:google.protobuf.testing.AnyData)
  return target;
}

size_t AnyData::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:google.protobuf.testing.AnyData)
  size_t total_size = 0;

  // optional int32 attr = 1;
  if (this->attr() != 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::Int32Size(
        this->attr());
  }

  // optional string str = 2;
  if (this->str().size() > 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::StringSize(
        this->str());
  }

  // optional .google.protobuf.testing.AnyData nested_data = 4;
  if (this->has_nested_data()) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::MessageSizeNoVirtual(
        *this->nested_data_);
  }

  // optional .google.protobuf.Struct struct_data = 8;
  if (this->has_struct_data()) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::MessageSizeNoVirtual(
        *this->struct_data_);
  }

  // repeated string msgs = 3;
  total_size += 1 *
      ::google::protobuf::internal::FromIntSize(this->msgs_size());
  for (int i = 0; i < this->msgs_size(); i++) {
    total_size += ::google::protobuf::internal::WireFormatLite::StringSize(
      this->msgs(i));
  }

  // map<string, string> map_data = 7;
  total_size += 1 *
      ::google::protobuf::internal::FromIntSize(this->map_data_size());
  {
    ::google::protobuf::scoped_ptr<AnyData_MapDataEntry> entry;
    for (::google::protobuf::Map< ::std::string, ::std::string >::const_iterator
        it = this->map_data().begin();
        it != this->map_data().end(); ++it) {
      entry.reset(map_data_.NewEntryWrapper(it->first, it->second));
      total_size += ::google::protobuf::internal::WireFormatLite::
          MessageSizeNoVirtual(*entry);
    }
  }

  // repeated .google.protobuf.testing.AnyData repeated_data = 9;
  {
    unsigned int count = this->repeated_data_size();
    total_size += 1UL * count;
    for (unsigned int i = 0; i < count; i++) {
      total_size +=
        ::google::protobuf::internal::WireFormatLite::MessageSizeNoVirtual(
          this->repeated_data(i));
    }
  }

  int cached_size = ::google::protobuf::internal::ToCachedSize(total_size);
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = cached_size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
  return total_size;
}

void AnyData::MergeFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_merge_from_start:google.protobuf.testing.AnyData)
  if (GOOGLE_PREDICT_FALSE(&from == this)) MergeFromFail(__LINE__);
  const AnyData* source =
      ::google::protobuf::internal::DynamicCastToGenerated<const AnyData>(
          &from);
  if (source == NULL) {
  // @@protoc_insertion_point(generalized_merge_from_cast_fail:google.protobuf.testing.AnyData)
    ::google::protobuf::internal::ReflectionOps::Merge(from, this);
  } else {
  // @@protoc_insertion_point(generalized_merge_from_cast_success:google.protobuf.testing.AnyData)
    UnsafeMergeFrom(*source);
  }
}

void AnyData::MergeFrom(const AnyData& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:google.protobuf.testing.AnyData)
  if (GOOGLE_PREDICT_TRUE(&from != this)) {
    UnsafeMergeFrom(from);
  } else {
    MergeFromFail(__LINE__);
  }
}

void AnyData::UnsafeMergeFrom(const AnyData& from) {
  GOOGLE_DCHECK(&from != this);
  msgs_.UnsafeMergeFrom(from.msgs_);
  map_data_.MergeFrom(from.map_data_);
  repeated_data_.MergeFrom(from.repeated_data_);
  if (from.attr() != 0) {
    set_attr(from.attr());
  }
  if (from.str().size() > 0) {

    str_.AssignWithDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), from.str_);
  }
  if (from.has_nested_data()) {
    mutable_nested_data()->::google::protobuf::testing::AnyData::MergeFrom(from.nested_data());
  }
  if (from.has_struct_data()) {
    mutable_struct_data()->::google::protobuf::Struct::MergeFrom(from.struct_data());
  }
}

void AnyData::CopyFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_copy_from_start:google.protobuf.testing.AnyData)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

void AnyData::CopyFrom(const AnyData& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:google.protobuf.testing.AnyData)
  if (&from == this) return;
  Clear();
  UnsafeMergeFrom(from);
}

bool AnyData::IsInitialized() const {

  return true;
}

void AnyData::Swap(AnyData* other) {
  if (other == this) return;
  InternalSwap(other);
}
void AnyData::InternalSwap(AnyData* other) {
  std::swap(attr_, other->attr_);
  str_.Swap(&other->str_);
  msgs_.UnsafeArenaSwap(&other->msgs_);
  std::swap(nested_data_, other->nested_data_);
  map_data_.Swap(&other->map_data_);
  std::swap(struct_data_, other->struct_data_);
  repeated_data_.UnsafeArenaSwap(&other->repeated_data_);
  _internal_metadata_.Swap(&other->_internal_metadata_);
  std::swap(_cached_size_, other->_cached_size_);
}

::google::protobuf::Metadata AnyData::GetMetadata() const {
  protobuf_AssignDescriptorsOnce();
  ::google::protobuf::Metadata metadata;
  metadata.descriptor = AnyData_descriptor_;
  metadata.reflection = AnyData_reflection_;
  return metadata;
}

#if PROTOBUF_INLINE_NOT_IN_HEADERS
// AnyData

// optional int32 attr = 1;
void AnyData::clear_attr() {
  attr_ = 0;
}
::google::protobuf::int32 AnyData::attr() const {
  // @@protoc_insertion_point(field_get:google.protobuf.testing.AnyData.attr)
  return attr_;
}
void AnyData::set_attr(::google::protobuf::int32 value) {
  
  attr_ = value;
  // @@protoc_insertion_point(field_set:google.protobuf.testing.AnyData.attr)
}

// optional string str = 2;
void AnyData::clear_str() {
  str_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
const ::std::string& AnyData::str() const {
  // @@protoc_insertion_point(field_get:google.protobuf.testing.AnyData.str)
  return str_.GetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
void AnyData::set_str(const ::std::string& value) {
  
  str_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:google.protobuf.testing.AnyData.str)
}
void AnyData::set_str(const char* value) {
  
  str_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:google.protobuf.testing.AnyData.str)
}
void AnyData::set_str(const char* value, size_t size) {
  
  str_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:google.protobuf.testing.AnyData.str)
}
::std::string* AnyData::mutable_str() {
  
  // @@protoc_insertion_point(field_mutable:google.protobuf.testing.AnyData.str)
  return str_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
::std::string* AnyData::release_str() {
  // @@protoc_insertion_point(field_release:google.protobuf.testing.AnyData.str)
  
  return str_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
void AnyData::set_allocated_str(::std::string* str) {
  if (str != NULL) {
    
  } else {
    
  }
  str_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), str);
  // @@protoc_insertion_point(field_set_allocated:google.protobuf.testing.AnyData.str)
}

// repeated string msgs = 3;
int AnyData::msgs_size() const {
  return msgs_.size();
}
void AnyData::clear_msgs() {
  msgs_.Clear();
}
const ::std::string& AnyData::msgs(int index) const {
  // @@protoc_insertion_point(field_get:google.protobuf.testing.AnyData.msgs)
  return msgs_.Get(index);
}
::std::string* AnyData::mutable_msgs(int index) {
  // @@protoc_insertion_point(field_mutable:google.protobuf.testing.AnyData.msgs)
  return msgs_.Mutable(index);
}
void AnyData::set_msgs(int index, const ::std::string& value) {
  // @@protoc_insertion_point(field_set:google.protobuf.testing.AnyData.msgs)
  msgs_.Mutable(index)->assign(value);
}
void AnyData::set_msgs(int index, const char* value) {
  msgs_.Mutable(index)->assign(value);
  // @@protoc_insertion_point(field_set_char:google.protobuf.testing.AnyData.msgs)
}
void AnyData::set_msgs(int index, const char* value, size_t size) {
  msgs_.Mutable(index)->assign(
    reinterpret_cast<const char*>(value), size);
  // @@protoc_insertion_point(field_set_pointer:google.protobuf.testing.AnyData.msgs)
}
::std::string* AnyData::add_msgs() {
  // @@protoc_insertion_point(field_add_mutable:google.protobuf.testing.AnyData.msgs)
  return msgs_.Add();
}
void AnyData::add_msgs(const ::std::string& value) {
  msgs_.Add()->assign(value);
  // @@protoc_insertion_point(field_add:google.protobuf.testing.AnyData.msgs)
}
void AnyData::add_msgs(const char* value) {
  msgs_.Add()->assign(value);
  // @@protoc_insertion_point(field_add_char:google.protobuf.testing.AnyData.msgs)
}
void AnyData::add_msgs(const char* value, size_t size) {
  msgs_.Add()->assign(reinterpret_cast<const char*>(value), size);
  // @@protoc_insertion_point(field_add_pointer:google.protobuf.testing.AnyData.msgs)
}
const ::google::protobuf::RepeatedPtrField< ::std::string>&
AnyData::msgs() const {
  // @@protoc_insertion_point(field_list:google.protobuf.testing.AnyData.msgs)
  return msgs_;
}
::google::protobuf::RepeatedPtrField< ::std::string>*
AnyData::mutable_msgs() {
  // @@protoc_insertion_point(field_mutable_list:google.protobuf.testing.AnyData.msgs)
  return &msgs_;
}

// optional .google.protobuf.testing.AnyData nested_data = 4;
bool AnyData::has_nested_data() const {
  return this != internal_default_instance() && nested_data_ != NULL;
}
void AnyData::clear_nested_data() {
  if (GetArenaNoVirtual() == NULL && nested_data_ != NULL) delete nested_data_;
  nested_data_ = NULL;
}
const ::google::protobuf::testing::AnyData& AnyData::nested_data() const {
  // @@protoc_insertion_point(field_get:google.protobuf.testing.AnyData.nested_data)
  return nested_data_ != NULL ? *nested_data_
                         : *::google::protobuf::testing::AnyData::internal_default_instance();
}
::google::protobuf::testing::AnyData* AnyData::mutable_nested_data() {
  
  if (nested_data_ == NULL) {
    nested_data_ = new ::google::protobuf::testing::AnyData;
  }
  // @@protoc_insertion_point(field_mutable:google.protobuf.testing.AnyData.nested_data)
  return nested_data_;
}
::google::protobuf::testing::AnyData* AnyData::release_nested_data() {
  // @@protoc_insertion_point(field_release:google.protobuf.testing.AnyData.nested_data)
  
  ::google::protobuf::testing::AnyData* temp = nested_data_;
  nested_data_ = NULL;
  return temp;
}
void AnyData::set_allocated_nested_data(::google::protobuf::testing::AnyData* nested_data) {
  delete nested_data_;
  nested_data_ = nested_data;
  if (nested_data) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_set_allocated:google.protobuf.testing.AnyData.nested_data)
}

// map<string, string> map_data = 7;
int AnyData::map_data_size() const {
  return map_data_.size();
}
void AnyData::clear_map_data() {
  map_data_.Clear();
}
 const ::google::protobuf::Map< ::std::string, ::std::string >&
AnyData::map_data() const {
  // @@protoc_insertion_point(field_map:google.protobuf.testing.AnyData.map_data)
  return map_data_.GetMap();
}
 ::google::protobuf::Map< ::std::string, ::std::string >*
AnyData::mutable_map_data() {
  // @@protoc_insertion_point(field_mutable_map:google.protobuf.testing.AnyData.map_data)
  return map_data_.MutableMap();
}

// optional .google.protobuf.Struct struct_data = 8;
bool AnyData::has_struct_data() const {
  return this != internal_default_instance() && struct_data_ != NULL;
}
void AnyData::clear_struct_data() {
  if (GetArenaNoVirtual() == NULL && struct_data_ != NULL) delete struct_data_;
  struct_data_ = NULL;
}
const ::google::protobuf::Struct& AnyData::struct_data() const {
  // @@protoc_insertion_point(field_get:google.protobuf.testing.AnyData.struct_data)
  return struct_data_ != NULL ? *struct_data_
                         : *::google::protobuf::Struct::internal_default_instance();
}
::google::protobuf::Struct* AnyData::mutable_struct_data() {
  
  if (struct_data_ == NULL) {
    struct_data_ = new ::google::protobuf::Struct;
  }
  // @@protoc_insertion_point(field_mutable:google.protobuf.testing.AnyData.struct_data)
  return struct_data_;
}
::google::protobuf::Struct* AnyData::release_struct_data() {
  // @@protoc_insertion_point(field_release:google.protobuf.testing.AnyData.struct_data)
  
  ::google::protobuf::Struct* temp = struct_data_;
  struct_data_ = NULL;
  return temp;
}
void AnyData::set_allocated_struct_data(::google::protobuf::Struct* struct_data) {
  delete struct_data_;
  if (struct_data != NULL && struct_data->GetArena() != NULL) {
    ::google::protobuf::Struct* new_struct_data = new ::google::protobuf::Struct;
    new_struct_data->CopyFrom(*struct_data);
    struct_data = new_struct_data;
  }
  struct_data_ = struct_data;
  if (struct_data) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_set_allocated:google.protobuf.testing.AnyData.struct_data)
}

// repeated .google.protobuf.testing.AnyData repeated_data = 9;
int AnyData::repeated_data_size() const {
  return repeated_data_.size();
}
void AnyData::clear_repeated_data() {
  repeated_data_.Clear();
}
const ::google::protobuf::testing::AnyData& AnyData::repeated_data(int index) const {
  // @@protoc_insertion_point(field_get:google.protobuf.testing.AnyData.repeated_data)
  return repeated_data_.Get(index);
}
::google::protobuf::testing::AnyData* AnyData::mutable_repeated_data(int index) {
  // @@protoc_insertion_point(field_mutable:google.protobuf.testing.AnyData.repeated_data)
  return repeated_data_.Mutable(index);
}
::google::protobuf::testing::AnyData* AnyData::add_repeated_data() {
  // @@protoc_insertion_point(field_add:google.protobuf.testing.AnyData.repeated_data)
  return repeated_data_.Add();
}
::google::protobuf::RepeatedPtrField< ::google::protobuf::testing::AnyData >*
AnyData::mutable_repeated_data() {
  // @@protoc_insertion_point(field_mutable_list:google.protobuf.testing.AnyData.repeated_data)
  return &repeated_data_;
}
const ::google::protobuf::RepeatedPtrField< ::google::protobuf::testing::AnyData >&
AnyData::repeated_data() const {
  // @@protoc_insertion_point(field_list:google.protobuf.testing.AnyData.repeated_data)
  return repeated_data_;
}

inline const AnyData* AnyData::internal_default_instance() {
  return &AnyData_default_instance_.get();
}
#endif  // PROTOBUF_INLINE_NOT_IN_HEADERS

// ===================================================================

#if !defined(_MSC_VER) || _MSC_VER >= 1900
const int StringtoIntMap::kMapFieldNumber;
#endif  // !defined(_MSC_VER) || _MSC_VER >= 1900

StringtoIntMap::StringtoIntMap()
  : ::google::protobuf::Message(), _internal_metadata_(NULL) {
  if (this != internal_default_instance()) protobuf_InitDefaults_google_2fprotobuf_2futil_2finternal_2ftestdata_2fdefault_5fvalue_2eproto();
  SharedCtor();
  // @@protoc_insertion_point(constructor:google.protobuf.testing.StringtoIntMap)
}

void StringtoIntMap::InitAsDefaultInstance() {
}

StringtoIntMap::StringtoIntMap(const StringtoIntMap& from)
  : ::google::protobuf::Message(),
    _internal_metadata_(NULL) {
  SharedCtor();
  UnsafeMergeFrom(from);
  // @@protoc_insertion_point(copy_constructor:google.protobuf.testing.StringtoIntMap)
}

void StringtoIntMap::SharedCtor() {
  map_.SetAssignDescriptorCallback(
      protobuf_AssignDescriptorsOnce);
  map_.SetEntryDescriptor(
      &::google::protobuf::testing::StringtoIntMap_MapEntry_descriptor_);
  _cached_size_ = 0;
}

StringtoIntMap::~StringtoIntMap() {
  // @@protoc_insertion_point(destructor:google.protobuf.testing.StringtoIntMap)
  SharedDtor();
}

void StringtoIntMap::SharedDtor() {
}

void StringtoIntMap::SetCachedSize(int size) const {
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
}
const ::google::protobuf::Descriptor* StringtoIntMap::descriptor() {
  protobuf_AssignDescriptorsOnce();
  return StringtoIntMap_descriptor_;
}

const StringtoIntMap& StringtoIntMap::default_instance() {
  protobuf_InitDefaults_google_2fprotobuf_2futil_2finternal_2ftestdata_2fdefault_5fvalue_2eproto();
  return *internal_default_instance();
}

::google::protobuf::internal::ExplicitlyConstructed<StringtoIntMap> StringtoIntMap_default_instance_;

StringtoIntMap* StringtoIntMap::New(::google::protobuf::Arena* arena) const {
  StringtoIntMap* n = new StringtoIntMap;
  if (arena != NULL) {
    arena->Own(n);
  }
  return n;
}

void StringtoIntMap::Clear() {
// @@protoc_insertion_point(message_clear_start:google.protobuf.testing.StringtoIntMap)
  map_.Clear();
}

bool StringtoIntMap::MergePartialFromCodedStream(
    ::google::protobuf::io::CodedInputStream* input) {
#define DO_(EXPRESSION) if (!GOOGLE_PREDICT_TRUE(EXPRESSION)) goto failure
  ::google::protobuf::uint32 tag;
  // @@protoc_insertion_point(parse_start:google.protobuf.testing.StringtoIntMap)
  for (;;) {
    ::std::pair< ::google::protobuf::uint32, bool> p = input->ReadTagWithCutoff(127);
    tag = p.first;
    if (!p.second) goto handle_unusual;
    switch (::google::protobuf::internal::WireFormatLite::GetTagFieldNumber(tag)) {
      // map<string, int32> map = 1;
      case 1: {
        if (tag == 10) {
          DO_(input->IncrementRecursionDepth());
         parse_loop_map:
          StringtoIntMap_MapEntry::Parser< ::google::protobuf::internal::MapField<
              ::std::string, ::google::protobuf::int32,
              ::google::protobuf::internal::WireFormatLite::TYPE_STRING,
              ::google::protobuf::internal::WireFormatLite::TYPE_INT32,
              0 >,
            ::google::protobuf::Map< ::std::string, ::google::protobuf::int32 > > parser(&map_);
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessageNoVirtual(
              input, &parser));
          DO_(::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
            parser.key().data(), parser.key().length(),
            ::google::protobuf::internal::WireFormatLite::PARSE,
            "google.protobuf.testing.StringtoIntMap.MapEntry.key"));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(10)) goto parse_loop_map;
        input->UnsafeDecrementRecursionDepth();
        if (input->ExpectAtEnd()) goto success;
        break;
      }

      default: {
      handle_unusual:
        if (tag == 0 ||
            ::google::protobuf::internal::WireFormatLite::GetTagWireType(tag) ==
            ::google::protobuf::internal::WireFormatLite::WIRETYPE_END_GROUP) {
          goto success;
        }
        DO_(::google::protobuf::internal::WireFormatLite::SkipField(input, tag));
        break;
      }
    }
  }
success:
  // @@protoc_insertion_point(parse_success:google.protobuf.testing.StringtoIntMap)
  return true;
failure:
  // @@protoc_insertion_point(parse_failure:google.protobuf.testing.StringtoIntMap)
  return false;
#undef DO_
}

void StringtoIntMap::SerializeWithCachedSizes(
    ::google::protobuf::io::CodedOutputStream* output) const {
  // @@protoc_insertion_point(serialize_start:google.protobuf.testing.StringtoIntMap)
  // map<string, int32> map = 1;
  if (!this->map().empty()) {
    typedef ::google::protobuf::Map< ::std::string, ::google::protobuf::int32 >::const_pointer
        ConstPtr;
    typedef ConstPtr SortItem;
    typedef ::google::protobuf::internal::CompareByDerefFirst<SortItem> Less;
    struct Utf8Check {
      static void Check(ConstPtr p) {
        ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
          p->first.data(), p->first.length(),
          ::google::protobuf::internal::WireFormatLite::SERIALIZE,
          "google.protobuf.testing.StringtoIntMap.MapEntry.key");
      }
    };

    if (output->IsSerializationDeterminstic() &&
        this->map().size() > 1) {
      ::google::protobuf::scoped_array<SortItem> items(
          new SortItem[this->map().size()]);
      typedef ::google::protobuf::Map< ::std::string, ::google::protobuf::int32 >::size_type size_type;
      size_type n = 0;
      for (::google::protobuf::Map< ::std::string, ::google::protobuf::int32 >::const_iterator
          it = this->map().begin();
          it != this->map().end(); ++it, ++n) {
        items[n] = SortItem(&*it);
      }
      ::std::sort(&items[0], &items[n], Less());
      ::google::protobuf::scoped_ptr<StringtoIntMap_MapEntry> entry;
      for (size_type i = 0; i < n; i++) {
        entry.reset(map_.NewEntryWrapper(
            items[i]->first, items[i]->second));
        ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
            1, *entry, output);
        Utf8Check::Check(items[i]);
      }
    } else {
      ::google::protobuf::scoped_ptr<StringtoIntMap_MapEntry> entry;
      for (::google::protobuf::Map< ::std::string, ::google::protobuf::int32 >::const_iterator
          it = this->map().begin();
          it != this->map().end(); ++it) {
        entry.reset(map_.NewEntryWrapper(
            it->first, it->second));
        ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
            1, *entry, output);
        Utf8Check::Check(&*it);
      }
    }
  }

  // @@protoc_insertion_point(serialize_end:google.protobuf.testing.StringtoIntMap)
}

::google::protobuf::uint8* StringtoIntMap::InternalSerializeWithCachedSizesToArray(
    bool deterministic, ::google::protobuf::uint8* target) const {
  (void)deterministic; // Unused
  // @@protoc_insertion_point(serialize_to_array_start:google.protobuf.testing.StringtoIntMap)
  // map<string, int32> map = 1;
  if (!this->map().empty()) {
    typedef ::google::protobuf::Map< ::std::string, ::google::protobuf::int32 >::const_pointer
        ConstPtr;
    typedef ConstPtr SortItem;
    typedef ::google::protobuf::internal::CompareByDerefFirst<SortItem> Less;
    struct Utf8Check {
      static void Check(ConstPtr p) {
        ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
          p->first.data(), p->first.length(),
          ::google::protobuf::internal::WireFormatLite::SERIALIZE,
          "google.protobuf.testing.StringtoIntMap.MapEntry.key");
      }
    };

    if (deterministic &&
        this->map().size() > 1) {
      ::google::protobuf::scoped_array<SortItem> items(
          new SortItem[this->map().size()]);
      typedef ::google::protobuf::Map< ::std::string, ::google::protobuf::int32 >::size_type size_type;
      size_type n = 0;
      for (::google::protobuf::Map< ::std::string, ::google::protobuf::int32 >::const_iterator
          it = this->map().begin();
          it != this->map().end(); ++it, ++n) {
        items[n] = SortItem(&*it);
      }
      ::std::sort(&items[0], &items[n], Less());
      ::google::protobuf::scoped_ptr<StringtoIntMap_MapEntry> entry;
      for (size_type i = 0; i < n; i++) {
        entry.reset(map_.NewEntryWrapper(
            items[i]->first, items[i]->second));
        target = ::google::protobuf::internal::WireFormatLite::
                   InternalWriteMessageNoVirtualToArray(
                       1, *entry, deterministic, target);
;
        Utf8Check::Check(items[i]);
      }
    } else {
      ::google::protobuf::scoped_ptr<StringtoIntMap_MapEntry> entry;
      for (::google::protobuf::Map< ::std::string, ::google::protobuf::int32 >::const_iterator
          it = this->map().begin();
          it != this->map().end(); ++it) {
        entry.reset(map_.NewEntryWrapper(
            it->first, it->second));
        target = ::google::protobuf::internal::WireFormatLite::
                   InternalWriteMessageNoVirtualToArray(
                       1, *entry, deterministic, target);
;
        Utf8Check::Check(&*it);
      }
    }
  }

  // @@protoc_insertion_point(serialize_to_array_end:google.protobuf.testing.StringtoIntMap)
  return target;
}

size_t StringtoIntMap::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:google.protobuf.testing.StringtoIntMap)
  size_t total_size = 0;

  // map<string, int32> map = 1;
  total_size += 1 *
      ::google::protobuf::internal::FromIntSize(this->map_size());
  {
    ::google::protobuf::scoped_ptr<StringtoIntMap_MapEntry> entry;
    for (::google::protobuf::Map< ::std::string, ::google::protobuf::int32 >::const_iterator
        it = this->map().begin();
        it != this->map().end(); ++it) {
      entry.reset(map_.NewEntryWrapper(it->first, it->second));
      total_size += ::google::protobuf::internal::WireFormatLite::
          MessageSizeNoVirtual(*entry);
    }
  }

  int cached_size = ::google::protobuf::internal::ToCachedSize(total_size);
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = cached_size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
  return total_size;
}

void StringtoIntMap::MergeFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_merge_from_start:google.protobuf.testing.StringtoIntMap)
  if (GOOGLE_PREDICT_FALSE(&from == this)) MergeFromFail(__LINE__);
  const StringtoIntMap* source =
      ::google::protobuf::internal::DynamicCastToGenerated<const StringtoIntMap>(
          &from);
  if (source == NULL) {
  // @@protoc_insertion_point(generalized_merge_from_cast_fail:google.protobuf.testing.StringtoIntMap)
    ::google::protobuf::internal::ReflectionOps::Merge(from, this);
  } else {
  // @@protoc_insertion_point(generalized_merge_from_cast_success:google.protobuf.testing.StringtoIntMap)
    UnsafeMergeFrom(*source);
  }
}

void StringtoIntMap::MergeFrom(const StringtoIntMap& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:google.protobuf.testing.StringtoIntMap)
  if (GOOGLE_PREDICT_TRUE(&from != this)) {
    UnsafeMergeFrom(from);
  } else {
    MergeFromFail(__LINE__);
  }
}

void StringtoIntMap::UnsafeMergeFrom(const StringtoIntMap& from) {
  GOOGLE_DCHECK(&from != this);
  map_.MergeFrom(from.map_);
}

void StringtoIntMap::CopyFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_copy_from_start:google.protobuf.testing.StringtoIntMap)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

void StringtoIntMap::CopyFrom(const StringtoIntMap& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:google.protobuf.testing.StringtoIntMap)
  if (&from == this) return;
  Clear();
  UnsafeMergeFrom(from);
}

bool StringtoIntMap::IsInitialized() const {

  return true;
}

void StringtoIntMap::Swap(StringtoIntMap* other) {
  if (other == this) return;
  InternalSwap(other);
}
void StringtoIntMap::InternalSwap(StringtoIntMap* other) {
  map_.Swap(&other->map_);
  _internal_metadata_.Swap(&other->_internal_metadata_);
  std::swap(_cached_size_, other->_cached_size_);
}

::google::protobuf::Metadata StringtoIntMap::GetMetadata() const {
  protobuf_AssignDescriptorsOnce();
  ::google::protobuf::Metadata metadata;
  metadata.descriptor = StringtoIntMap_descriptor_;
  metadata.reflection = StringtoIntMap_reflection_;
  return metadata;
}

#if PROTOBUF_INLINE_NOT_IN_HEADERS
// StringtoIntMap

// map<string, int32> map = 1;
int StringtoIntMap::map_size() const {
  return map_.size();
}
void StringtoIntMap::clear_map() {
  map_.Clear();
}
 const ::google::protobuf::Map< ::std::string, ::google::protobuf::int32 >&
StringtoIntMap::map() const {
  // @@protoc_insertion_point(field_map:google.protobuf.testing.StringtoIntMap.map)
  return map_.GetMap();
}
 ::google::protobuf::Map< ::std::string, ::google::protobuf::int32 >*
StringtoIntMap::mutable_map() {
  // @@protoc_insertion_point(field_mutable_map:google.protobuf.testing.StringtoIntMap.map)
  return map_.MutableMap();
}

inline const StringtoIntMap* StringtoIntMap::internal_default_instance() {
  return &StringtoIntMap_default_instance_.get();
}
#endif  // PROTOBUF_INLINE_NOT_IN_HEADERS

// ===================================================================

#if !defined(_MSC_VER) || _MSC_VER >= 1900
const int IntToStringMap::kMapFieldNumber;
#endif  // !defined(_MSC_VER) || _MSC_VER >= 1900

IntToStringMap::IntToStringMap()
  : ::google::protobuf::Message(), _internal_metadata_(NULL) {
  if (this != internal_default_instance()) protobuf_InitDefaults_google_2fprotobuf_2futil_2finternal_2ftestdata_2fdefault_5fvalue_2eproto();
  SharedCtor();
  // @@protoc_insertion_point(constructor:google.protobuf.testing.IntToStringMap)
}

void IntToStringMap::InitAsDefaultInstance() {
}

IntToStringMap::IntToStringMap(const IntToStringMap& from)
  : ::google::protobuf::Message(),
    _internal_metadata_(NULL) {
  SharedCtor();
  UnsafeMergeFrom(from);
  // @@protoc_insertion_point(copy_constructor:google.protobuf.testing.IntToStringMap)
}

void IntToStringMap::SharedCtor() {
  map_.SetAssignDescriptorCallback(
      protobuf_AssignDescriptorsOnce);
  map_.SetEntryDescriptor(
      &::google::protobuf::testing::IntToStringMap_MapEntry_descriptor_);
  _cached_size_ = 0;
}

IntToStringMap::~IntToStringMap() {
  // @@protoc_insertion_point(destructor:google.protobuf.testing.IntToStringMap)
  SharedDtor();
}

void IntToStringMap::SharedDtor() {
}

void IntToStringMap::SetCachedSize(int size) const {
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
}
const ::google::protobuf::Descriptor* IntToStringMap::descriptor() {
  protobuf_AssignDescriptorsOnce();
  return IntToStringMap_descriptor_;
}

const IntToStringMap& IntToStringMap::default_instance() {
  protobuf_InitDefaults_google_2fprotobuf_2futil_2finternal_2ftestdata_2fdefault_5fvalue_2eproto();
  return *internal_default_instance();
}

::google::protobuf::internal::ExplicitlyConstructed<IntToStringMap> IntToStringMap_default_instance_;

IntToStringMap* IntToStringMap::New(::google::protobuf::Arena* arena) const {
  IntToStringMap* n = new IntToStringMap;
  if (arena != NULL) {
    arena->Own(n);
  }
  return n;
}

void IntToStringMap::Clear() {
// @@protoc_insertion_point(message_clear_start:google.protobuf.testing.IntToStringMap)
  map_.Clear();
}

bool IntToStringMap::MergePartialFromCodedStream(
    ::google::protobuf::io::CodedInputStream* input) {
#define DO_(EXPRESSION) if (!GOOGLE_PREDICT_TRUE(EXPRESSION)) goto failure
  ::google::protobuf::uint32 tag;
  // @@protoc_insertion_point(parse_start:google.protobuf.testing.IntToStringMap)
  for (;;) {
    ::std::pair< ::google::protobuf::uint32, bool> p = input->ReadTagWithCutoff(127);
    tag = p.first;
    if (!p.second) goto handle_unusual;
    switch (::google::protobuf::internal::WireFormatLite::GetTagFieldNumber(tag)) {
      // map<int32, string> map = 1;
      case 1: {
        if (tag == 10) {
          DO_(input->IncrementRecursionDepth());
         parse_loop_map:
          IntToStringMap_MapEntry::Parser< ::google::protobuf::internal::MapField<
              ::google::protobuf::int32, ::std::string,
              ::google::protobuf::internal::WireFormatLite::TYPE_INT32,
              ::google::protobuf::internal::WireFormatLite::TYPE_STRING,
              0 >,
            ::google::protobuf::Map< ::google::protobuf::int32, ::std::string > > parser(&map_);
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessageNoVirtual(
              input, &parser));
          DO_(::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
            parser.value().data(), parser.value().length(),
            ::google::protobuf::internal::WireFormatLite::PARSE,
            "google.protobuf.testing.IntToStringMap.MapEntry.value"));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(10)) goto parse_loop_map;
        input->UnsafeDecrementRecursionDepth();
        if (input->ExpectAtEnd()) goto success;
        break;
      }

      default: {
      handle_unusual:
        if (tag == 0 ||
            ::google::protobuf::internal::WireFormatLite::GetTagWireType(tag) ==
            ::google::protobuf::internal::WireFormatLite::WIRETYPE_END_GROUP) {
          goto success;
        }
        DO_(::google::protobuf::internal::WireFormatLite::SkipField(input, tag));
        break;
      }
    }
  }
success:
  // @@protoc_insertion_point(parse_success:google.protobuf.testing.IntToStringMap)
  return true;
failure:
  // @@protoc_insertion_point(parse_failure:google.protobuf.testing.IntToStringMap)
  return false;
#undef DO_
}

void IntToStringMap::SerializeWithCachedSizes(
    ::google::protobuf::io::CodedOutputStream* output) const {
  // @@protoc_insertion_point(serialize_start:google.protobuf.testing.IntToStringMap)
  // map<int32, string> map = 1;
  if (!this->map().empty()) {
    typedef ::google::protobuf::Map< ::google::protobuf::int32, ::std::string >::const_pointer
        ConstPtr;
    typedef ::google::protobuf::internal::SortItem< ::google::protobuf::int32, ConstPtr > SortItem;
    typedef ::google::protobuf::internal::CompareByFirstField<SortItem> Less;
    struct Utf8Check {
      static void Check(ConstPtr p) {
        ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
          p->second.data(), p->second.length(),
          ::google::protobuf::internal::WireFormatLite::SERIALIZE,
          "google.protobuf.testing.IntToStringMap.MapEntry.value");
      }
    };

    if (output->IsSerializationDeterminstic() &&
        this->map().size() > 1) {
      ::google::protobuf::scoped_array<SortItem> items(
          new SortItem[this->map().size()]);
      typedef ::google::protobuf::Map< ::google::protobuf::int32, ::std::string >::size_type size_type;
      size_type n = 0;
      for (::google::protobuf::Map< ::google::protobuf::int32, ::std::string >::const_iterator
          it = this->map().begin();
          it != this->map().end(); ++it, ++n) {
        items[n] = SortItem(&*it);
      }
      ::std::sort(&items[0], &items[n], Less());
      ::google::protobuf::scoped_ptr<IntToStringMap_MapEntry> entry;
      for (size_type i = 0; i < n; i++) {
        entry.reset(map_.NewEntryWrapper(
            items[i].second->first, items[i].second->second));
        ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
            1, *entry, output);
        Utf8Check::Check(items[i].second);
      }
    } else {
      ::google::protobuf::scoped_ptr<IntToStringMap_MapEntry> entry;
      for (::google::protobuf::Map< ::google::protobuf::int32, ::std::string >::const_iterator
          it = this->map().begin();
          it != this->map().end(); ++it) {
        entry.reset(map_.NewEntryWrapper(
            it->first, it->second));
        ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
            1, *entry, output);
        Utf8Check::Check(&*it);
      }
    }
  }

  // @@protoc_insertion_point(serialize_end:google.protobuf.testing.IntToStringMap)
}

::google::protobuf::uint8* IntToStringMap::InternalSerializeWithCachedSizesToArray(
    bool deterministic, ::google::protobuf::uint8* target) const {
  (void)deterministic; // Unused
  // @@protoc_insertion_point(serialize_to_array_start:google.protobuf.testing.IntToStringMap)
  // map<int32, string> map = 1;
  if (!this->map().empty()) {
    typedef ::google::protobuf::Map< ::google::protobuf::int32, ::std::string >::const_pointer
        ConstPtr;
    typedef ::google::protobuf::internal::SortItem< ::google::protobuf::int32, ConstPtr > SortItem;
    typedef ::google::protobuf::internal::CompareByFirstField<SortItem> Less;
    struct Utf8Check {
      static void Check(ConstPtr p) {
        ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
          p->second.data(), p->second.length(),
          ::google::protobuf::internal::WireFormatLite::SERIALIZE,
          "google.protobuf.testing.IntToStringMap.MapEntry.value");
      }
    };

    if (deterministic &&
        this->map().size() > 1) {
      ::google::protobuf::scoped_array<SortItem> items(
          new SortItem[this->map().size()]);
      typedef ::google::protobuf::Map< ::google::protobuf::int32, ::std::string >::size_type size_type;
      size_type n = 0;
      for (::google::protobuf::Map< ::google::protobuf::int32, ::std::string >::const_iterator
          it = this->map().begin();
          it != this->map().end(); ++it, ++n) {
        items[n] = SortItem(&*it);
      }
      ::std::sort(&items[0], &items[n], Less());
      ::google::protobuf::scoped_ptr<IntToStringMap_MapEntry> entry;
      for (size_type i = 0; i < n; i++) {
        entry.reset(map_.NewEntryWrapper(
            items[i].second->first, items[i].second->second));
        target = ::google::protobuf::internal::WireFormatLite::
                   InternalWriteMessageNoVirtualToArray(
                       1, *entry, deterministic, target);
;
        Utf8Check::Check(items[i].second);
      }
    } else {
      ::google::protobuf::scoped_ptr<IntToStringMap_MapEntry> entry;
      for (::google::protobuf::Map< ::google::protobuf::int32, ::std::string >::const_iterator
          it = this->map().begin();
          it != this->map().end(); ++it) {
        entry.reset(map_.NewEntryWrapper(
            it->first, it->second));
        target = ::google::protobuf::internal::WireFormatLite::
                   InternalWriteMessageNoVirtualToArray(
                       1, *entry, deterministic, target);
;
        Utf8Check::Check(&*it);
      }
    }
  }

  // @@protoc_insertion_point(serialize_to_array_end:google.protobuf.testing.IntToStringMap)
  return target;
}

size_t IntToStringMap::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:google.protobuf.testing.IntToStringMap)
  size_t total_size = 0;

  // map<int32, string> map = 1;
  total_size += 1 *
      ::google::protobuf::internal::FromIntSize(this->map_size());
  {
    ::google::protobuf::scoped_ptr<IntToStringMap_MapEntry> entry;
    for (::google::protobuf::Map< ::google::protobuf::int32, ::std::string >::const_iterator
        it = this->map().begin();
        it != this->map().end(); ++it) {
      entry.reset(map_.NewEntryWrapper(it->first, it->second));
      total_size += ::google::protobuf::internal::WireFormatLite::
          MessageSizeNoVirtual(*entry);
    }
  }

  int cached_size = ::google::protobuf::internal::ToCachedSize(total_size);
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = cached_size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
  return total_size;
}

void IntToStringMap::MergeFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_merge_from_start:google.protobuf.testing.IntToStringMap)
  if (GOOGLE_PREDICT_FALSE(&from == this)) MergeFromFail(__LINE__);
  const IntToStringMap* source =
      ::google::protobuf::internal::DynamicCastToGenerated<const IntToStringMap>(
          &from);
  if (source == NULL) {
  // @@protoc_insertion_point(generalized_merge_from_cast_fail:google.protobuf.testing.IntToStringMap)
    ::google::protobuf::internal::ReflectionOps::Merge(from, this);
  } else {
  // @@protoc_insertion_point(generalized_merge_from_cast_success:google.protobuf.testing.IntToStringMap)
    UnsafeMergeFrom(*source);
  }
}

void IntToStringMap::MergeFrom(const IntToStringMap& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:google.protobuf.testing.IntToStringMap)
  if (GOOGLE_PREDICT_TRUE(&from != this)) {
    UnsafeMergeFrom(from);
  } else {
    MergeFromFail(__LINE__);
  }
}

void IntToStringMap::UnsafeMergeFrom(const IntToStringMap& from) {
  GOOGLE_DCHECK(&from != this);
  map_.MergeFrom(from.map_);
}

void IntToStringMap::CopyFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_copy_from_start:google.protobuf.testing.IntToStringMap)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

void IntToStringMap::CopyFrom(const IntToStringMap& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:google.protobuf.testing.IntToStringMap)
  if (&from == this) return;
  Clear();
  UnsafeMergeFrom(from);
}

bool IntToStringMap::IsInitialized() const {

  return true;
}

void IntToStringMap::Swap(IntToStringMap* other) {
  if (other == this) return;
  InternalSwap(other);
}
void IntToStringMap::InternalSwap(IntToStringMap* other) {
  map_.Swap(&other->map_);
  _internal_metadata_.Swap(&other->_internal_metadata_);
  std::swap(_cached_size_, other->_cached_size_);
}

::google::protobuf::Metadata IntToStringMap::GetMetadata() const {
  protobuf_AssignDescriptorsOnce();
  ::google::protobuf::Metadata metadata;
  metadata.descriptor = IntToStringMap_descriptor_;
  metadata.reflection = IntToStringMap_reflection_;
  return metadata;
}

#if PROTOBUF_INLINE_NOT_IN_HEADERS
// IntToStringMap

// map<int32, string> map = 1;
int IntToStringMap::map_size() const {
  return map_.size();
}
void IntToStringMap::clear_map() {
  map_.Clear();
}
 const ::google::protobuf::Map< ::google::protobuf::int32, ::std::string >&
IntToStringMap::map() const {
  // @@protoc_insertion_point(field_map:google.protobuf.testing.IntToStringMap.map)
  return map_.GetMap();
}
 ::google::protobuf::Map< ::google::protobuf::int32, ::std::string >*
IntToStringMap::mutable_map() {
  // @@protoc_insertion_point(field_mutable_map:google.protobuf.testing.IntToStringMap.map)
  return map_.MutableMap();
}

inline const IntToStringMap* IntToStringMap::internal_default_instance() {
  return &IntToStringMap_default_instance_.get();
}
#endif  // PROTOBUF_INLINE_NOT_IN_HEADERS

// ===================================================================

#if !defined(_MSC_VER) || _MSC_VER >= 1900
const int MixedMap::kMsgFieldNumber;
const int MixedMap::kMapFieldNumber;
const int MixedMap::kIntValueFieldNumber;
#endif  // !defined(_MSC_VER) || _MSC_VER >= 1900

MixedMap::MixedMap()
  : ::google::protobuf::Message(), _internal_metadata_(NULL) {
  if (this != internal_default_instance()) protobuf_InitDefaults_google_2fprotobuf_2futil_2finternal_2ftestdata_2fdefault_5fvalue_2eproto();
  SharedCtor();
  // @@protoc_insertion_point(constructor:google.protobuf.testing.MixedMap)
}

void MixedMap::InitAsDefaultInstance() {
}

MixedMap::MixedMap(const MixedMap& from)
  : ::google::protobuf::Message(),
    _internal_metadata_(NULL) {
  SharedCtor();
  UnsafeMergeFrom(from);
  // @@protoc_insertion_point(copy_constructor:google.protobuf.testing.MixedMap)
}

void MixedMap::SharedCtor() {
  map_.SetAssignDescriptorCallback(
      protobuf_AssignDescriptorsOnce);
  map_.SetEntryDescriptor(
      &::google::protobuf::testing::MixedMap_MapEntry_descriptor_);
  msg_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  int_value_ = 0;
  _cached_size_ = 0;
}

MixedMap::~MixedMap() {
  // @@protoc_insertion_point(destructor:google.protobuf.testing.MixedMap)
  SharedDtor();
}

void MixedMap::SharedDtor() {
  msg_.DestroyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}

void MixedMap::SetCachedSize(int size) const {
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
}
const ::google::protobuf::Descriptor* MixedMap::descriptor() {
  protobuf_AssignDescriptorsOnce();
  return MixedMap_descriptor_;
}

const MixedMap& MixedMap::default_instance() {
  protobuf_InitDefaults_google_2fprotobuf_2futil_2finternal_2ftestdata_2fdefault_5fvalue_2eproto();
  return *internal_default_instance();
}

::google::protobuf::internal::ExplicitlyConstructed<MixedMap> MixedMap_default_instance_;

MixedMap* MixedMap::New(::google::protobuf::Arena* arena) const {
  MixedMap* n = new MixedMap;
  if (arena != NULL) {
    arena->Own(n);
  }
  return n;
}

void MixedMap::Clear() {
// @@protoc_insertion_point(message_clear_start:google.protobuf.testing.MixedMap)
  msg_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  int_value_ = 0;
  map_.Clear();
}

bool MixedMap::MergePartialFromCodedStream(
    ::google::protobuf::io::CodedInputStream* input) {
#define DO_(EXPRESSION) if (!GOOGLE_PREDICT_TRUE(EXPRESSION)) goto failure
  ::google::protobuf::uint32 tag;
  // @@protoc_insertion_point(parse_start:google.protobuf.testing.MixedMap)
  for (;;) {
    ::std::pair< ::google::protobuf::uint32, bool> p = input->ReadTagWithCutoff(127);
    tag = p.first;
    if (!p.second) goto handle_unusual;
    switch (::google::protobuf::internal::WireFormatLite::GetTagFieldNumber(tag)) {
      // optional string msg = 1;
      case 1: {
        if (tag == 10) {
          DO_(::google::protobuf::internal::WireFormatLite::ReadString(
                input, this->mutable_msg()));
          DO_(::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
            this->msg().data(), this->msg().length(),
            ::google::protobuf::internal::WireFormatLite::PARSE,
            "google.protobuf.testing.MixedMap.msg"));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(18)) goto parse_map;
        break;
      }

      // map<string, float> map = 2;
      case 2: {
        if (tag == 18) {
         parse_map:
          DO_(input->IncrementRecursionDepth());
         parse_loop_map:
          MixedMap_MapEntry::Parser< ::google::protobuf::internal::MapField<
              ::std::string, float,
              ::google::protobuf::internal::WireFormatLite::TYPE_STRING,
              ::google::protobuf::internal::WireFormatLite::TYPE_FLOAT,
              0 >,
            ::google::protobuf::Map< ::std::string, float > > parser(&map_);
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessageNoVirtual(
              input, &parser));
          DO_(::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
            parser.key().data(), parser.key().length(),
            ::google::protobuf::internal::WireFormatLite::PARSE,
            "google.protobuf.testing.MixedMap.MapEntry.key"));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(18)) goto parse_loop_map;
        input->UnsafeDecrementRecursionDepth();
        if (input->ExpectTag(24)) goto parse_int_value;
        break;
      }

      // optional int32 int_value = 3;
      case 3: {
        if (tag == 24) {
         parse_int_value:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int32, ::google::protobuf::internal::WireFormatLite::TYPE_INT32>(
                 input, &int_value_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectAtEnd()) goto success;
        break;
      }

      default: {
      handle_unusual:
        if (tag == 0 ||
            ::google::protobuf::internal::WireFormatLite::GetTagWireType(tag) ==
            ::google::protobuf::internal::WireFormatLite::WIRETYPE_END_GROUP) {
          goto success;
        }
        DO_(::google::protobuf::internal::WireFormatLite::SkipField(input, tag));
        break;
      }
    }
  }
success:
  // @@protoc_insertion_point(parse_success:google.protobuf.testing.MixedMap)
  return true;
failure:
  // @@protoc_insertion_point(parse_failure:google.protobuf.testing.MixedMap)
  return false;
#undef DO_
}

void MixedMap::SerializeWithCachedSizes(
    ::google::protobuf::io::CodedOutputStream* output) const {
  // @@protoc_insertion_point(serialize_start:google.protobuf.testing.MixedMap)
  // optional string msg = 1;
  if (this->msg().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->msg().data(), this->msg().length(),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "google.protobuf.testing.MixedMap.msg");
    ::google::protobuf::internal::WireFormatLite::WriteStringMaybeAliased(
      1, this->msg(), output);
  }

  // map<string, float> map = 2;
  if (!this->map().empty()) {
    typedef ::google::protobuf::Map< ::std::string, float >::const_pointer
        ConstPtr;
    typedef ConstPtr SortItem;
    typedef ::google::protobuf::internal::CompareByDerefFirst<SortItem> Less;
    struct Utf8Check {
      static void Check(ConstPtr p) {
        ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
          p->first.data(), p->first.length(),
          ::google::protobuf::internal::WireFormatLite::SERIALIZE,
          "google.protobuf.testing.MixedMap.MapEntry.key");
      }
    };

    if (output->IsSerializationDeterminstic() &&
        this->map().size() > 1) {
      ::google::protobuf::scoped_array<SortItem> items(
          new SortItem[this->map().size()]);
      typedef ::google::protobuf::Map< ::std::string, float >::size_type size_type;
      size_type n = 0;
      for (::google::protobuf::Map< ::std::string, float >::const_iterator
          it = this->map().begin();
          it != this->map().end(); ++it, ++n) {
        items[n] = SortItem(&*it);
      }
      ::std::sort(&items[0], &items[n], Less());
      ::google::protobuf::scoped_ptr<MixedMap_MapEntry> entry;
      for (size_type i = 0; i < n; i++) {
        entry.reset(map_.NewEntryWrapper(
            items[i]->first, items[i]->second));
        ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
            2, *entry, output);
        Utf8Check::Check(items[i]);
      }
    } else {
      ::google::protobuf::scoped_ptr<MixedMap_MapEntry> entry;
      for (::google::protobuf::Map< ::std::string, float >::const_iterator
          it = this->map().begin();
          it != this->map().end(); ++it) {
        entry.reset(map_.NewEntryWrapper(
            it->first, it->second));
        ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
            2, *entry, output);
        Utf8Check::Check(&*it);
      }
    }
  }

  // optional int32 int_value = 3;
  if (this->int_value() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt32(3, this->int_value(), output);
  }

  // @@protoc_insertion_point(serialize_end:google.protobuf.testing.MixedMap)
}

::google::protobuf::uint8* MixedMap::InternalSerializeWithCachedSizesToArray(
    bool deterministic, ::google::protobuf::uint8* target) const {
  (void)deterministic; // Unused
  // @@protoc_insertion_point(serialize_to_array_start:google.protobuf.testing.MixedMap)
  // optional string msg = 1;
  if (this->msg().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->msg().data(), this->msg().length(),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "google.protobuf.testing.MixedMap.msg");
    target =
      ::google::protobuf::internal::WireFormatLite::WriteStringToArray(
        1, this->msg(), target);
  }

  // map<string, float> map = 2;
  if (!this->map().empty()) {
    typedef ::google::protobuf::Map< ::std::string, float >::const_pointer
        ConstPtr;
    typedef ConstPtr SortItem;
    typedef ::google::protobuf::internal::CompareByDerefFirst<SortItem> Less;
    struct Utf8Check {
      static void Check(ConstPtr p) {
        ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
          p->first.data(), p->first.length(),
          ::google::protobuf::internal::WireFormatLite::SERIALIZE,
          "google.protobuf.testing.MixedMap.MapEntry.key");
      }
    };

    if (deterministic &&
        this->map().size() > 1) {
      ::google::protobuf::scoped_array<SortItem> items(
          new SortItem[this->map().size()]);
      typedef ::google::protobuf::Map< ::std::string, float >::size_type size_type;
      size_type n = 0;
      for (::google::protobuf::Map< ::std::string, float >::const_iterator
          it = this->map().begin();
          it != this->map().end(); ++it, ++n) {
        items[n] = SortItem(&*it);
      }
      ::std::sort(&items[0], &items[n], Less());
      ::google::protobuf::scoped_ptr<MixedMap_MapEntry> entry;
      for (size_type i = 0; i < n; i++) {
        entry.reset(map_.NewEntryWrapper(
            items[i]->first, items[i]->second));
        target = ::google::protobuf::internal::WireFormatLite::
                   InternalWriteMessageNoVirtualToArray(
                       2, *entry, deterministic, target);
;
        Utf8Check::Check(items[i]);
      }
    } else {
      ::google::protobuf::scoped_ptr<MixedMap_MapEntry> entry;
      for (::google::protobuf::Map< ::std::string, float >::const_iterator
          it = this->map().begin();
          it != this->map().end(); ++it) {
        entry.reset(map_.NewEntryWrapper(
            it->first, it->second));
        target = ::google::protobuf::internal::WireFormatLite::
                   InternalWriteMessageNoVirtualToArray(
                       2, *entry, deterministic, target);
;
        Utf8Check::Check(&*it);
      }
    }
  }

  // optional int32 int_value = 3;
  if (this->int_value() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt32ToArray(3, this->int_value(), target);
  }

  // @@protoc_insertion_point(serialize_to_array_end:google.protobuf.testing.MixedMap)
  return target;
}

size_t MixedMap::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:google.protobuf.testing.MixedMap)
  size_t total_size = 0;

  // optional string msg = 1;
  if (this->msg().size() > 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::StringSize(
        this->msg());
  }

  // optional int32 int_value = 3;
  if (this->int_value() != 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::Int32Size(
        this->int_value());
  }

  // map<string, float> map = 2;
  total_size += 1 *
      ::google::protobuf::internal::FromIntSize(this->map_size());
  {
    ::google::protobuf::scoped_ptr<MixedMap_MapEntry> entry;
    for (::google::protobuf::Map< ::std::string, float >::const_iterator
        it = this->map().begin();
        it != this->map().end(); ++it) {
      entry.reset(map_.NewEntryWrapper(it->first, it->second));
      total_size += ::google::protobuf::internal::WireFormatLite::
          MessageSizeNoVirtual(*entry);
    }
  }

  int cached_size = ::google::protobuf::internal::ToCachedSize(total_size);
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = cached_size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
  return total_size;
}

void MixedMap::MergeFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_merge_from_start:google.protobuf.testing.MixedMap)
  if (GOOGLE_PREDICT_FALSE(&from == this)) MergeFromFail(__LINE__);
  const MixedMap* source =
      ::google::protobuf::internal::DynamicCastToGenerated<const MixedMap>(
          &from);
  if (source == NULL) {
  // @@protoc_insertion_point(generalized_merge_from_cast_fail:google.protobuf.testing.MixedMap)
    ::google::protobuf::internal::ReflectionOps::Merge(from, this);
  } else {
  // @@protoc_insertion_point(generalized_merge_from_cast_success:google.protobuf.testing.MixedMap)
    UnsafeMergeFrom(*source);
  }
}

void MixedMap::MergeFrom(const MixedMap& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:google.protobuf.testing.MixedMap)
  if (GOOGLE_PREDICT_TRUE(&from != this)) {
    UnsafeMergeFrom(from);
  } else {
    MergeFromFail(__LINE__);
  }
}

void MixedMap::UnsafeMergeFrom(const MixedMap& from) {
  GOOGLE_DCHECK(&from != this);
  map_.MergeFrom(from.map_);
  if (from.msg().size() > 0) {

    msg_.AssignWithDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), from.msg_);
  }
  if (from.int_value() != 0) {
    set_int_value(from.int_value());
  }
}

void MixedMap::CopyFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_copy_from_start:google.protobuf.testing.MixedMap)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

void MixedMap::CopyFrom(const MixedMap& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:google.protobuf.testing.MixedMap)
  if (&from == this) return;
  Clear();
  UnsafeMergeFrom(from);
}

bool MixedMap::IsInitialized() const {

  return true;
}

void MixedMap::Swap(MixedMap* other) {
  if (other == this) return;
  InternalSwap(other);
}
void MixedMap::InternalSwap(MixedMap* other) {
  msg_.Swap(&other->msg_);
  map_.Swap(&other->map_);
  std::swap(int_value_, other->int_value_);
  _internal_metadata_.Swap(&other->_internal_metadata_);
  std::swap(_cached_size_, other->_cached_size_);
}

::google::protobuf::Metadata MixedMap::GetMetadata() const {
  protobuf_AssignDescriptorsOnce();
  ::google::protobuf::Metadata metadata;
  metadata.descriptor = MixedMap_descriptor_;
  metadata.reflection = MixedMap_reflection_;
  return metadata;
}

#if PROTOBUF_INLINE_NOT_IN_HEADERS
// MixedMap

// optional string msg = 1;
void MixedMap::clear_msg() {
  msg_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
const ::std::string& MixedMap::msg() const {
  // @@protoc_insertion_point(field_get:google.protobuf.testing.MixedMap.msg)
  return msg_.GetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
void MixedMap::set_msg(const ::std::string& value) {
  
  msg_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:google.protobuf.testing.MixedMap.msg)
}
void MixedMap::set_msg(const char* value) {
  
  msg_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:google.protobuf.testing.MixedMap.msg)
}
void MixedMap::set_msg(const char* value, size_t size) {
  
  msg_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:google.protobuf.testing.MixedMap.msg)
}
::std::string* MixedMap::mutable_msg() {
  
  // @@protoc_insertion_point(field_mutable:google.protobuf.testing.MixedMap.msg)
  return msg_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
::std::string* MixedMap::release_msg() {
  // @@protoc_insertion_point(field_release:google.protobuf.testing.MixedMap.msg)
  
  return msg_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
void MixedMap::set_allocated_msg(::std::string* msg) {
  if (msg != NULL) {
    
  } else {
    
  }
  msg_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), msg);
  // @@protoc_insertion_point(field_set_allocated:google.protobuf.testing.MixedMap.msg)
}

// map<string, float> map = 2;
int MixedMap::map_size() const {
  return map_.size();
}
void MixedMap::clear_map() {
  map_.Clear();
}
 const ::google::protobuf::Map< ::std::string, float >&
MixedMap::map() const {
  // @@protoc_insertion_point(field_map:google.protobuf.testing.MixedMap.map)
  return map_.GetMap();
}
 ::google::protobuf::Map< ::std::string, float >*
MixedMap::mutable_map() {
  // @@protoc_insertion_point(field_mutable_map:google.protobuf.testing.MixedMap.map)
  return map_.MutableMap();
}

// optional int32 int_value = 3;
void MixedMap::clear_int_value() {
  int_value_ = 0;
}
::google::protobuf::int32 MixedMap::int_value() const {
  // @@protoc_insertion_point(field_get:google.protobuf.testing.MixedMap.int_value)
  return int_value_;
}
void MixedMap::set_int_value(::google::protobuf::int32 value) {
  
  int_value_ = value;
  // @@protoc_insertion_point(field_set:google.protobuf.testing.MixedMap.int_value)
}

inline const MixedMap* MixedMap::internal_default_instance() {
  return &MixedMap_default_instance_.get();
}
#endif  // PROTOBUF_INLINE_NOT_IN_HEADERS

// ===================================================================

const ::google::protobuf::EnumDescriptor* MixedMap2_E_descriptor() {
  protobuf_AssignDescriptorsOnce();
  return MixedMap2_E_descriptor_;
}
bool MixedMap2_E_IsValid(int value) {
  switch (value) {
    case 0:
    case 1:
    case 2:
    case 3:
      return true;
    default:
      return false;
  }
}

#if !defined(_MSC_VER) || _MSC_VER >= 1900
const MixedMap2_E MixedMap2::E0;
const MixedMap2_E MixedMap2::E1;
const MixedMap2_E MixedMap2::E2;
const MixedMap2_E MixedMap2::E3;
const MixedMap2_E MixedMap2::E_MIN;
const MixedMap2_E MixedMap2::E_MAX;
const int MixedMap2::E_ARRAYSIZE;
#endif  // !defined(_MSC_VER) || _MSC_VER >= 1900
#if !defined(_MSC_VER) || _MSC_VER >= 1900
const int MixedMap2::kMapFieldNumber;
const int MixedMap2::kEeFieldNumber;
const int MixedMap2::kMsgFieldNumber;
#endif  // !defined(_MSC_VER) || _MSC_VER >= 1900

MixedMap2::MixedMap2()
  : ::google::protobuf::Message(), _internal_metadata_(NULL) {
  if (this != internal_default_instance()) protobuf_InitDefaults_google_2fprotobuf_2futil_2finternal_2ftestdata_2fdefault_5fvalue_2eproto();
  SharedCtor();
  // @@protoc_insertion_point(constructor:google.protobuf.testing.MixedMap2)
}

void MixedMap2::InitAsDefaultInstance() {
}

MixedMap2::MixedMap2(const MixedMap2& from)
  : ::google::protobuf::Message(),
    _internal_metadata_(NULL) {
  SharedCtor();
  UnsafeMergeFrom(from);
  // @@protoc_insertion_point(copy_constructor:google.protobuf.testing.MixedMap2)
}

void MixedMap2::SharedCtor() {
  map_.SetAssignDescriptorCallback(
      protobuf_AssignDescriptorsOnce);
  map_.SetEntryDescriptor(
      &::google::protobuf::testing::MixedMap2_MapEntry_descriptor_);
  msg_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  ee_ = 0;
  _cached_size_ = 0;
}

MixedMap2::~MixedMap2() {
  // @@protoc_insertion_point(destructor:google.protobuf.testing.MixedMap2)
  SharedDtor();
}

void MixedMap2::SharedDtor() {
  msg_.DestroyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}

void MixedMap2::SetCachedSize(int size) const {
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
}
const ::google::protobuf::Descriptor* MixedMap2::descriptor() {
  protobuf_AssignDescriptorsOnce();
  return MixedMap2_descriptor_;
}

const MixedMap2& MixedMap2::default_instance() {
  protobuf_InitDefaults_google_2fprotobuf_2futil_2finternal_2ftestdata_2fdefault_5fvalue_2eproto();
  return *internal_default_instance();
}

::google::protobuf::internal::ExplicitlyConstructed<MixedMap2> MixedMap2_default_instance_;

MixedMap2* MixedMap2::New(::google::protobuf::Arena* arena) const {
  MixedMap2* n = new MixedMap2;
  if (arena != NULL) {
    arena->Own(n);
  }
  return n;
}

void MixedMap2::Clear() {
// @@protoc_insertion_point(message_clear_start:google.protobuf.testing.MixedMap2)
  ee_ = 0;
  msg_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  map_.Clear();
}

bool MixedMap2::MergePartialFromCodedStream(
    ::google::protobuf::io::CodedInputStream* input) {
#define DO_(EXPRESSION) if (!GOOGLE_PREDICT_TRUE(EXPRESSION)) goto failure
  ::google::protobuf::uint32 tag;
  // @@protoc_insertion_point(parse_start:google.protobuf.testing.MixedMap2)
  for (;;) {
    ::std::pair< ::google::protobuf::uint32, bool> p = input->ReadTagWithCutoff(127);
    tag = p.first;
    if (!p.second) goto handle_unusual;
    switch (::google::protobuf::internal::WireFormatLite::GetTagFieldNumber(tag)) {
      // map<int32, bool> map = 1;
      case 1: {
        if (tag == 10) {
          DO_(input->IncrementRecursionDepth());
         parse_loop_map:
          MixedMap2_MapEntry::Parser< ::google::protobuf::internal::MapField<
              ::google::protobuf::int32, bool,
              ::google::protobuf::internal::WireFormatLite::TYPE_INT32,
              ::google::protobuf::internal::WireFormatLite::TYPE_BOOL,
              0 >,
            ::google::protobuf::Map< ::google::protobuf::int32, bool > > parser(&map_);
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessageNoVirtual(
              input, &parser));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(10)) goto parse_loop_map;
        input->UnsafeDecrementRecursionDepth();
        if (input->ExpectTag(16)) goto parse_ee;
        break;
      }

      // optional .google.protobuf.testing.MixedMap2.E ee = 2;
      case 2: {
        if (tag == 16) {
         parse_ee:
          int value;
          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   int, ::google::protobuf::internal::WireFormatLite::TYPE_ENUM>(
                 input, &value)));
          set_ee(static_cast< ::google::protobuf::testing::MixedMap2_E >(value));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(34)) goto parse_msg;
        break;
      }

      // optional string msg = 4;
      case 4: {
        if (tag == 34) {
         parse_msg:
          DO_(::google::protobuf::internal::WireFormatLite::ReadString(
                input, this->mutable_msg()));
          DO_(::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
            this->msg().data(), this->msg().length(),
            ::google::protobuf::internal::WireFormatLite::PARSE,
            "google.protobuf.testing.MixedMap2.msg"));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectAtEnd()) goto success;
        break;
      }

      default: {
      handle_unusual:
        if (tag == 0 ||
            ::google::protobuf::internal::WireFormatLite::GetTagWireType(tag) ==
            ::google::protobuf::internal::WireFormatLite::WIRETYPE_END_GROUP) {
          goto success;
        }
        DO_(::google::protobuf::internal::WireFormatLite::SkipField(input, tag));
        break;
      }
    }
  }
success:
  // @@protoc_insertion_point(parse_success:google.protobuf.testing.MixedMap2)
  return true;
failure:
  // @@protoc_insertion_point(parse_failure:google.protobuf.testing.MixedMap2)
  return false;
#undef DO_
}

void MixedMap2::SerializeWithCachedSizes(
    ::google::protobuf::io::CodedOutputStream* output) const {
  // @@protoc_insertion_point(serialize_start:google.protobuf.testing.MixedMap2)
  // map<int32, bool> map = 1;
  if (!this->map().empty()) {
    typedef ::google::protobuf::Map< ::google::protobuf::int32, bool >::const_pointer
        ConstPtr;
    typedef ::google::protobuf::internal::SortItem< ::google::protobuf::int32, ConstPtr > SortItem;
    typedef ::google::protobuf::internal::CompareByFirstField<SortItem> Less;

    if (output->IsSerializationDeterminstic() &&
        this->map().size() > 1) {
      ::google::protobuf::scoped_array<SortItem> items(
          new SortItem[this->map().size()]);
      typedef ::google::protobuf::Map< ::google::protobuf::int32, bool >::size_type size_type;
      size_type n = 0;
      for (::google::protobuf::Map< ::google::protobuf::int32, bool >::const_iterator
          it = this->map().begin();
          it != this->map().end(); ++it, ++n) {
        items[n] = SortItem(&*it);
      }
      ::std::sort(&items[0], &items[n], Less());
      ::google::protobuf::scoped_ptr<MixedMap2_MapEntry> entry;
      for (size_type i = 0; i < n; i++) {
        entry.reset(map_.NewEntryWrapper(
            items[i].second->first, items[i].second->second));
        ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
            1, *entry, output);
      }
    } else {
      ::google::protobuf::scoped_ptr<MixedMap2_MapEntry> entry;
      for (::google::protobuf::Map< ::google::protobuf::int32, bool >::const_iterator
          it = this->map().begin();
          it != this->map().end(); ++it) {
        entry.reset(map_.NewEntryWrapper(
            it->first, it->second));
        ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
            1, *entry, output);
      }
    }
  }

  // optional .google.protobuf.testing.MixedMap2.E ee = 2;
  if (this->ee() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteEnum(
      2, this->ee(), output);
  }

  // optional string msg = 4;
  if (this->msg().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->msg().data(), this->msg().length(),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "google.protobuf.testing.MixedMap2.msg");
    ::google::protobuf::internal::WireFormatLite::WriteStringMaybeAliased(
      4, this->msg(), output);
  }

  // @@protoc_insertion_point(serialize_end:google.protobuf.testing.MixedMap2)
}

::google::protobuf::uint8* MixedMap2::InternalSerializeWithCachedSizesToArray(
    bool deterministic, ::google::protobuf::uint8* target) const {
  (void)deterministic; // Unused
  // @@protoc_insertion_point(serialize_to_array_start:google.protobuf.testing.MixedMap2)
  // map<int32, bool> map = 1;
  if (!this->map().empty()) {
    typedef ::google::protobuf::Map< ::google::protobuf::int32, bool >::const_pointer
        ConstPtr;
    typedef ::google::protobuf::internal::SortItem< ::google::protobuf::int32, ConstPtr > SortItem;
    typedef ::google::protobuf::internal::CompareByFirstField<SortItem> Less;

    if (deterministic &&
        this->map().size() > 1) {
      ::google::protobuf::scoped_array<SortItem> items(
          new SortItem[this->map().size()]);
      typedef ::google::protobuf::Map< ::google::protobuf::int32, bool >::size_type size_type;
      size_type n = 0;
      for (::google::protobuf::Map< ::google::protobuf::int32, bool >::const_iterator
          it = this->map().begin();
          it != this->map().end(); ++it, ++n) {
        items[n] = SortItem(&*it);
      }
      ::std::sort(&items[0], &items[n], Less());
      ::google::protobuf::scoped_ptr<MixedMap2_MapEntry> entry;
      for (size_type i = 0; i < n; i++) {
        entry.reset(map_.NewEntryWrapper(
            items[i].second->first, items[i].second->second));
        target = ::google::protobuf::internal::WireFormatLite::
                   InternalWriteMessageNoVirtualToArray(
                       1, *entry, deterministic, target);
;
      }
    } else {
      ::google::protobuf::scoped_ptr<MixedMap2_MapEntry> entry;
      for (::google::protobuf::Map< ::google::protobuf::int32, bool >::const_iterator
          it = this->map().begin();
          it != this->map().end(); ++it) {
        entry.reset(map_.NewEntryWrapper(
            it->first, it->second));
        target = ::google::protobuf::internal::WireFormatLite::
                   InternalWriteMessageNoVirtualToArray(
                       1, *entry, deterministic, target);
;
      }
    }
  }

  // optional .google.protobuf.testing.MixedMap2.E ee = 2;
  if (this->ee() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteEnumToArray(
      2, this->ee(), target);
  }

  // optional string msg = 4;
  if (this->msg().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->msg().data(), this->msg().length(),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "google.protobuf.testing.MixedMap2.msg");
    target =
      ::google::protobuf::internal::WireFormatLite::WriteStringToArray(
        4, this->msg(), target);
  }

  // @@protoc_insertion_point(serialize_to_array_end:google.protobuf.testing.MixedMap2)
  return target;
}

size_t MixedMap2::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:google.protobuf.testing.MixedMap2)
  size_t total_size = 0;

  // optional .google.protobuf.testing.MixedMap2.E ee = 2;
  if (this->ee() != 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::EnumSize(this->ee());
  }

  // optional string msg = 4;
  if (this->msg().size() > 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::StringSize(
        this->msg());
  }

  // map<int32, bool> map = 1;
  total_size += 1 *
      ::google::protobuf::internal::FromIntSize(this->map_size());
  {
    ::google::protobuf::scoped_ptr<MixedMap2_MapEntry> entry;
    for (::google::protobuf::Map< ::google::protobuf::int32, bool >::const_iterator
        it = this->map().begin();
        it != this->map().end(); ++it) {
      entry.reset(map_.NewEntryWrapper(it->first, it->second));
      total_size += ::google::protobuf::internal::WireFormatLite::
          MessageSizeNoVirtual(*entry);
    }
  }

  int cached_size = ::google::protobuf::internal::ToCachedSize(total_size);
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = cached_size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
  return total_size;
}

void MixedMap2::MergeFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_merge_from_start:google.protobuf.testing.MixedMap2)
  if (GOOGLE_PREDICT_FALSE(&from == this)) MergeFromFail(__LINE__);
  const MixedMap2* source =
      ::google::protobuf::internal::DynamicCastToGenerated<const MixedMap2>(
          &from);
  if (source == NULL) {
  // @@protoc_insertion_point(generalized_merge_from_cast_fail:google.protobuf.testing.MixedMap2)
    ::google::protobuf::internal::ReflectionOps::Merge(from, this);
  } else {
  // @@protoc_insertion_point(generalized_merge_from_cast_success:google.protobuf.testing.MixedMap2)
    UnsafeMergeFrom(*source);
  }
}

void MixedMap2::MergeFrom(const MixedMap2& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:google.protobuf.testing.MixedMap2)
  if (GOOGLE_PREDICT_TRUE(&from != this)) {
    UnsafeMergeFrom(from);
  } else {
    MergeFromFail(__LINE__);
  }
}

void MixedMap2::UnsafeMergeFrom(const MixedMap2& from) {
  GOOGLE_DCHECK(&from != this);
  map_.MergeFrom(from.map_);
  if (from.ee() != 0) {
    set_ee(from.ee());
  }
  if (from.msg().size() > 0) {

    msg_.AssignWithDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), from.msg_);
  }
}

void MixedMap2::CopyFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_copy_from_start:google.protobuf.testing.MixedMap2)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

void MixedMap2::CopyFrom(const MixedMap2& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:google.protobuf.testing.MixedMap2)
  if (&from == this) return;
  Clear();
  UnsafeMergeFrom(from);
}

bool MixedMap2::IsInitialized() const {

  return true;
}

void MixedMap2::Swap(MixedMap2* other) {
  if (other == this) return;
  InternalSwap(other);
}
void MixedMap2::InternalSwap(MixedMap2* other) {
  map_.Swap(&other->map_);
  std::swap(ee_, other->ee_);
  msg_.Swap(&other->msg_);
  _internal_metadata_.Swap(&other->_internal_metadata_);
  std::swap(_cached_size_, other->_cached_size_);
}

::google::protobuf::Metadata MixedMap2::GetMetadata() const {
  protobuf_AssignDescriptorsOnce();
  ::google::protobuf::Metadata metadata;
  metadata.descriptor = MixedMap2_descriptor_;
  metadata.reflection = MixedMap2_reflection_;
  return metadata;
}

#if PROTOBUF_INLINE_NOT_IN_HEADERS
// MixedMap2

// map<int32, bool> map = 1;
int MixedMap2::map_size() const {
  return map_.size();
}
void MixedMap2::clear_map() {
  map_.Clear();
}
 const ::google::protobuf::Map< ::google::protobuf::int32, bool >&
MixedMap2::map() const {
  // @@protoc_insertion_point(field_map:google.protobuf.testing.MixedMap2.map)
  return map_.GetMap();
}
 ::google::protobuf::Map< ::google::protobuf::int32, bool >*
MixedMap2::mutable_map() {
  // @@protoc_insertion_point(field_mutable_map:google.protobuf.testing.MixedMap2.map)
  return map_.MutableMap();
}

// optional .google.protobuf.testing.MixedMap2.E ee = 2;
void MixedMap2::clear_ee() {
  ee_ = 0;
}
::google::protobuf::testing::MixedMap2_E MixedMap2::ee() const {
  // @@protoc_insertion_point(field_get:google.protobuf.testing.MixedMap2.ee)
  return static_cast< ::google::protobuf::testing::MixedMap2_E >(ee_);
}
void MixedMap2::set_ee(::google::protobuf::testing::MixedMap2_E value) {
  
  ee_ = value;
  // @@protoc_insertion_point(field_set:google.protobuf.testing.MixedMap2.ee)
}

// optional string msg = 4;
void MixedMap2::clear_msg() {
  msg_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
const ::std::string& MixedMap2::msg() const {
  // @@protoc_insertion_point(field_get:google.protobuf.testing.MixedMap2.msg)
  return msg_.GetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
void MixedMap2::set_msg(const ::std::string& value) {
  
  msg_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:google.protobuf.testing.MixedMap2.msg)
}
void MixedMap2::set_msg(const char* value) {
  
  msg_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:google.protobuf.testing.MixedMap2.msg)
}
void MixedMap2::set_msg(const char* value, size_t size) {
  
  msg_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:google.protobuf.testing.MixedMap2.msg)
}
::std::string* MixedMap2::mutable_msg() {
  
  // @@protoc_insertion_point(field_mutable:google.protobuf.testing.MixedMap2.msg)
  return msg_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
::std::string* MixedMap2::release_msg() {
  // @@protoc_insertion_point(field_release:google.protobuf.testing.MixedMap2.msg)
  
  return msg_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
void MixedMap2::set_allocated_msg(::std::string* msg) {
  if (msg != NULL) {
    
  } else {
    
  }
  msg_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), msg);
  // @@protoc_insertion_point(field_set_allocated:google.protobuf.testing.MixedMap2.msg)
}

inline const MixedMap2* MixedMap2::internal_default_instance() {
  return &MixedMap2_default_instance_.get();
}
#endif  // PROTOBUF_INLINE_NOT_IN_HEADERS

// ===================================================================

#if !defined(_MSC_VER) || _MSC_VER >= 1900
const int MessageMap_M::kInnerIntFieldNumber;
const int MessageMap_M::kInnerTextFieldNumber;
#endif  // !defined(_MSC_VER) || _MSC_VER >= 1900

MessageMap_M::MessageMap_M()
  : ::google::protobuf::Message(), _internal_metadata_(NULL) {
  if (this != internal_default_instance()) protobuf_InitDefaults_google_2fprotobuf_2futil_2finternal_2ftestdata_2fdefault_5fvalue_2eproto();
  SharedCtor();
  // @@protoc_insertion_point(constructor:google.protobuf.testing.MessageMap.M)
}

void MessageMap_M::InitAsDefaultInstance() {
}

MessageMap_M::MessageMap_M(const MessageMap_M& from)
  : ::google::protobuf::Message(),
    _internal_metadata_(NULL) {
  SharedCtor();
  UnsafeMergeFrom(from);
  // @@protoc_insertion_point(copy_constructor:google.protobuf.testing.MessageMap.M)
}

void MessageMap_M::SharedCtor() {
  inner_text_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  inner_int_ = 0;
  _cached_size_ = 0;
}

MessageMap_M::~MessageMap_M() {
  // @@protoc_insertion_point(destructor:google.protobuf.testing.MessageMap.M)
  SharedDtor();
}

void MessageMap_M::SharedDtor() {
  inner_text_.DestroyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}

void MessageMap_M::SetCachedSize(int size) const {
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
}
const ::google::protobuf::Descriptor* MessageMap_M::descriptor() {
  protobuf_AssignDescriptorsOnce();
  return MessageMap_M_descriptor_;
}

const MessageMap_M& MessageMap_M::default_instance() {
  protobuf_InitDefaults_google_2fprotobuf_2futil_2finternal_2ftestdata_2fdefault_5fvalue_2eproto();
  return *internal_default_instance();
}

::google::protobuf::internal::ExplicitlyConstructed<MessageMap_M> MessageMap_M_default_instance_;

MessageMap_M* MessageMap_M::New(::google::protobuf::Arena* arena) const {
  MessageMap_M* n = new MessageMap_M;
  if (arena != NULL) {
    arena->Own(n);
  }
  return n;
}

void MessageMap_M::Clear() {
// @@protoc_insertion_point(message_clear_start:google.protobuf.testing.MessageMap.M)
  inner_int_ = 0;
  inner_text_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}

bool MessageMap_M::MergePartialFromCodedStream(
    ::google::protobuf::io::CodedInputStream* input) {
#define DO_(EXPRESSION) if (!GOOGLE_PREDICT_TRUE(EXPRESSION)) goto failure
  ::google::protobuf::uint32 tag;
  // @@protoc_insertion_point(parse_start:google.protobuf.testing.MessageMap.M)
  for (;;) {
    ::std::pair< ::google::protobuf::uint32, bool> p = input->ReadTagWithCutoff(127);
    tag = p.first;
    if (!p.second) goto handle_unusual;
    switch (::google::protobuf::internal::WireFormatLite::GetTagFieldNumber(tag)) {
      // optional int32 inner_int = 1;
      case 1: {
        if (tag == 8) {

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int32, ::google::protobuf::internal::WireFormatLite::TYPE_INT32>(
                 input, &inner_int_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(18)) goto parse_inner_text;
        break;
      }

      // optional string inner_text = 2;
      case 2: {
        if (tag == 18) {
         parse_inner_text:
          DO_(::google::protobuf::internal::WireFormatLite::ReadString(
                input, this->mutable_inner_text()));
          DO_(::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
            this->inner_text().data(), this->inner_text().length(),
            ::google::protobuf::internal::WireFormatLite::PARSE,
            "google.protobuf.testing.MessageMap.M.inner_text"));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectAtEnd()) goto success;
        break;
      }

      default: {
      handle_unusual:
        if (tag == 0 ||
            ::google::protobuf::internal::WireFormatLite::GetTagWireType(tag) ==
            ::google::protobuf::internal::WireFormatLite::WIRETYPE_END_GROUP) {
          goto success;
        }
        DO_(::google::protobuf::internal::WireFormatLite::SkipField(input, tag));
        break;
      }
    }
  }
success:
  // @@protoc_insertion_point(parse_success:google.protobuf.testing.MessageMap.M)
  return true;
failure:
  // @@protoc_insertion_point(parse_failure:google.protobuf.testing.MessageMap.M)
  return false;
#undef DO_
}

void MessageMap_M::SerializeWithCachedSizes(
    ::google::protobuf::io::CodedOutputStream* output) const {
  // @@protoc_insertion_point(serialize_start:google.protobuf.testing.MessageMap.M)
  // optional int32 inner_int = 1;
  if (this->inner_int() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt32(1, this->inner_int(), output);
  }

  // optional string inner_text = 2;
  if (this->inner_text().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->inner_text().data(), this->inner_text().length(),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "google.protobuf.testing.MessageMap.M.inner_text");
    ::google::protobuf::internal::WireFormatLite::WriteStringMaybeAliased(
      2, this->inner_text(), output);
  }

  // @@protoc_insertion_point(serialize_end:google.protobuf.testing.MessageMap.M)
}

::google::protobuf::uint8* MessageMap_M::InternalSerializeWithCachedSizesToArray(
    bool deterministic, ::google::protobuf::uint8* target) const {
  (void)deterministic; // Unused
  // @@protoc_insertion_point(serialize_to_array_start:google.protobuf.testing.MessageMap.M)
  // optional int32 inner_int = 1;
  if (this->inner_int() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt32ToArray(1, this->inner_int(), target);
  }

  // optional string inner_text = 2;
  if (this->inner_text().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->inner_text().data(), this->inner_text().length(),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "google.protobuf.testing.MessageMap.M.inner_text");
    target =
      ::google::protobuf::internal::WireFormatLite::WriteStringToArray(
        2, this->inner_text(), target);
  }

  // @@protoc_insertion_point(serialize_to_array_end:google.protobuf.testing.MessageMap.M)
  return target;
}

size_t MessageMap_M::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:google.protobuf.testing.MessageMap.M)
  size_t total_size = 0;

  // optional int32 inner_int = 1;
  if (this->inner_int() != 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::Int32Size(
        this->inner_int());
  }

  // optional string inner_text = 2;
  if (this->inner_text().size() > 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::StringSize(
        this->inner_text());
  }

  int cached_size = ::google::protobuf::internal::ToCachedSize(total_size);
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = cached_size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
  return total_size;
}

void MessageMap_M::MergeFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_merge_from_start:google.protobuf.testing.MessageMap.M)
  if (GOOGLE_PREDICT_FALSE(&from == this)) MergeFromFail(__LINE__);
  const MessageMap_M* source =
      ::google::protobuf::internal::DynamicCastToGenerated<const MessageMap_M>(
          &from);
  if (source == NULL) {
  // @@protoc_insertion_point(generalized_merge_from_cast_fail:google.protobuf.testing.MessageMap.M)
    ::google::protobuf::internal::ReflectionOps::Merge(from, this);
  } else {
  // @@protoc_insertion_point(generalized_merge_from_cast_success:google.protobuf.testing.MessageMap.M)
    UnsafeMergeFrom(*source);
  }
}

void MessageMap_M::MergeFrom(const MessageMap_M& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:google.protobuf.testing.MessageMap.M)
  if (GOOGLE_PREDICT_TRUE(&from != this)) {
    UnsafeMergeFrom(from);
  } else {
    MergeFromFail(__LINE__);
  }
}

void MessageMap_M::UnsafeMergeFrom(const MessageMap_M& from) {
  GOOGLE_DCHECK(&from != this);
  if (from.inner_int() != 0) {
    set_inner_int(from.inner_int());
  }
  if (from.inner_text().size() > 0) {

    inner_text_.AssignWithDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), from.inner_text_);
  }
}

void MessageMap_M::CopyFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_copy_from_start:google.protobuf.testing.MessageMap.M)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

void MessageMap_M::CopyFrom(const MessageMap_M& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:google.protobuf.testing.MessageMap.M)
  if (&from == this) return;
  Clear();
  UnsafeMergeFrom(from);
}

bool MessageMap_M::IsInitialized() const {

  return true;
}

void MessageMap_M::Swap(MessageMap_M* other) {
  if (other == this) return;
  InternalSwap(other);
}
void MessageMap_M::InternalSwap(MessageMap_M* other) {
  std::swap(inner_int_, other->inner_int_);
  inner_text_.Swap(&other->inner_text_);
  _internal_metadata_.Swap(&other->_internal_metadata_);
  std::swap(_cached_size_, other->_cached_size_);
}

::google::protobuf::Metadata MessageMap_M::GetMetadata() const {
  protobuf_AssignDescriptorsOnce();
  ::google::protobuf::Metadata metadata;
  metadata.descriptor = MessageMap_M_descriptor_;
  metadata.reflection = MessageMap_M_reflection_;
  return metadata;
}


// -------------------------------------------------------------------

#if !defined(_MSC_VER) || _MSC_VER >= 1900
const int MessageMap::kMapFieldNumber;
#endif  // !defined(_MSC_VER) || _MSC_VER >= 1900

MessageMap::MessageMap()
  : ::google::protobuf::Message(), _internal_metadata_(NULL) {
  if (this != internal_default_instance()) protobuf_InitDefaults_google_2fprotobuf_2futil_2finternal_2ftestdata_2fdefault_5fvalue_2eproto();
  SharedCtor();
  // @@protoc_insertion_point(constructor:google.protobuf.testing.MessageMap)
}

void MessageMap::InitAsDefaultInstance() {
}

MessageMap::MessageMap(const MessageMap& from)
  : ::google::protobuf::Message(),
    _internal_metadata_(NULL) {
  SharedCtor();
  UnsafeMergeFrom(from);
  // @@protoc_insertion_point(copy_constructor:google.protobuf.testing.MessageMap)
}

void MessageMap::SharedCtor() {
  map_.SetAssignDescriptorCallback(
      protobuf_AssignDescriptorsOnce);
  map_.SetEntryDescriptor(
      &::google::protobuf::testing::MessageMap_MapEntry_descriptor_);
  _cached_size_ = 0;
}

MessageMap::~MessageMap() {
  // @@protoc_insertion_point(destructor:google.protobuf.testing.MessageMap)
  SharedDtor();
}

void MessageMap::SharedDtor() {
}

void MessageMap::SetCachedSize(int size) const {
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
}
const ::google::protobuf::Descriptor* MessageMap::descriptor() {
  protobuf_AssignDescriptorsOnce();
  return MessageMap_descriptor_;
}

const MessageMap& MessageMap::default_instance() {
  protobuf_InitDefaults_google_2fprotobuf_2futil_2finternal_2ftestdata_2fdefault_5fvalue_2eproto();
  return *internal_default_instance();
}

::google::protobuf::internal::ExplicitlyConstructed<MessageMap> MessageMap_default_instance_;

MessageMap* MessageMap::New(::google::protobuf::Arena* arena) const {
  MessageMap* n = new MessageMap;
  if (arena != NULL) {
    arena->Own(n);
  }
  return n;
}

void MessageMap::Clear() {
// @@protoc_insertion_point(message_clear_start:google.protobuf.testing.MessageMap)
  map_.Clear();
}

bool MessageMap::MergePartialFromCodedStream(
    ::google::protobuf::io::CodedInputStream* input) {
#define DO_(EXPRESSION) if (!GOOGLE_PREDICT_TRUE(EXPRESSION)) goto failure
  ::google::protobuf::uint32 tag;
  // @@protoc_insertion_point(parse_start:google.protobuf.testing.MessageMap)
  for (;;) {
    ::std::pair< ::google::protobuf::uint32, bool> p = input->ReadTagWithCutoff(127);
    tag = p.first;
    if (!p.second) goto handle_unusual;
    switch (::google::protobuf::internal::WireFormatLite::GetTagFieldNumber(tag)) {
      // map<string, .google.protobuf.testing.MessageMap.M> map = 1;
      case 1: {
        if (tag == 10) {
          DO_(input->IncrementRecursionDepth());
         parse_loop_map:
          MessageMap_MapEntry::Parser< ::google::protobuf::internal::MapField<
              ::std::string, ::google::protobuf::testing::MessageMap_M,
              ::google::protobuf::internal::WireFormatLite::TYPE_STRING,
              ::google::protobuf::internal::WireFormatLite::TYPE_MESSAGE,
              0 >,
            ::google::protobuf::Map< ::std::string, ::google::protobuf::testing::MessageMap_M > > parser(&map_);
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessageNoVirtual(
              input, &parser));
          DO_(::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
            parser.key().data(), parser.key().length(),
            ::google::protobuf::internal::WireFormatLite::PARSE,
            "google.protobuf.testing.MessageMap.MapEntry.key"));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(10)) goto parse_loop_map;
        input->UnsafeDecrementRecursionDepth();
        if (input->ExpectAtEnd()) goto success;
        break;
      }

      default: {
      handle_unusual:
        if (tag == 0 ||
            ::google::protobuf::internal::WireFormatLite::GetTagWireType(tag) ==
            ::google::protobuf::internal::WireFormatLite::WIRETYPE_END_GROUP) {
          goto success;
        }
        DO_(::google::protobuf::internal::WireFormatLite::SkipField(input, tag));
        break;
      }
    }
  }
success:
  // @@protoc_insertion_point(parse_success:google.protobuf.testing.MessageMap)
  return true;
failure:
  // @@protoc_insertion_point(parse_failure:google.protobuf.testing.MessageMap)
  return false;
#undef DO_
}

void MessageMap::SerializeWithCachedSizes(
    ::google::protobuf::io::CodedOutputStream* output) const {
  // @@protoc_insertion_point(serialize_start:google.protobuf.testing.MessageMap)
  // map<string, .google.protobuf.testing.MessageMap.M> map = 1;
  if (!this->map().empty()) {
    typedef ::google::protobuf::Map< ::std::string, ::google::protobuf::testing::MessageMap_M >::const_pointer
        ConstPtr;
    typedef ConstPtr SortItem;
    typedef ::google::protobuf::internal::CompareByDerefFirst<SortItem> Less;
    struct Utf8Check {
      static void Check(ConstPtr p) {
        ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
          p->first.data(), p->first.length(),
          ::google::protobuf::internal::WireFormatLite::SERIALIZE,
          "google.protobuf.testing.MessageMap.MapEntry.key");
      }
    };

    if (output->IsSerializationDeterminstic() &&
        this->map().size() > 1) {
      ::google::protobuf::scoped_array<SortItem> items(
          new SortItem[this->map().size()]);
      typedef ::google::protobuf::Map< ::std::string, ::google::protobuf::testing::MessageMap_M >::size_type size_type;
      size_type n = 0;
      for (::google::protobuf::Map< ::std::string, ::google::protobuf::testing::MessageMap_M >::const_iterator
          it = this->map().begin();
          it != this->map().end(); ++it, ++n) {
        items[n] = SortItem(&*it);
      }
      ::std::sort(&items[0], &items[n], Less());
      ::google::protobuf::scoped_ptr<MessageMap_MapEntry> entry;
      for (size_type i = 0; i < n; i++) {
        entry.reset(map_.NewEntryWrapper(
            items[i]->first, items[i]->second));
        ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
            1, *entry, output);
        Utf8Check::Check(items[i]);
      }
    } else {
      ::google::protobuf::scoped_ptr<MessageMap_MapEntry> entry;
      for (::google::protobuf::Map< ::std::string, ::google::protobuf::testing::MessageMap_M >::const_iterator
          it = this->map().begin();
          it != this->map().end(); ++it) {
        entry.reset(map_.NewEntryWrapper(
            it->first, it->second));
        ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
            1, *entry, output);
        Utf8Check::Check(&*it);
      }
    }
  }

  // @@protoc_insertion_point(serialize_end:google.protobuf.testing.MessageMap)
}

::google::protobuf::uint8* MessageMap::InternalSerializeWithCachedSizesToArray(
    bool deterministic, ::google::protobuf::uint8* target) const {
  (void)deterministic; // Unused
  // @@protoc_insertion_point(serialize_to_array_start:google.protobuf.testing.MessageMap)
  // map<string, .google.protobuf.testing.MessageMap.M> map = 1;
  if (!this->map().empty()) {
    typedef ::google::protobuf::Map< ::std::string, ::google::protobuf::testing::MessageMap_M >::const_pointer
        ConstPtr;
    typedef ConstPtr SortItem;
    typedef ::google::protobuf::internal::CompareByDerefFirst<SortItem> Less;
    struct Utf8Check {
      static void Check(ConstPtr p) {
        ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
          p->first.data(), p->first.length(),
          ::google::protobuf::internal::WireFormatLite::SERIALIZE,
          "google.protobuf.testing.MessageMap.MapEntry.key");
      }
    };

    if (deterministic &&
        this->map().size() > 1) {
      ::google::protobuf::scoped_array<SortItem> items(
          new SortItem[this->map().size()]);
      typedef ::google::protobuf::Map< ::std::string, ::google::protobuf::testing::MessageMap_M >::size_type size_type;
      size_type n = 0;
      for (::google::protobuf::Map< ::std::string, ::google::protobuf::testing::MessageMap_M >::const_iterator
          it = this->map().begin();
          it != this->map().end(); ++it, ++n) {
        items[n] = SortItem(&*it);
      }
      ::std::sort(&items[0], &items[n], Less());
      ::google::protobuf::scoped_ptr<MessageMap_MapEntry> entry;
      for (size_type i = 0; i < n; i++) {
        entry.reset(map_.NewEntryWrapper(
            items[i]->first, items[i]->second));
        target = ::google::protobuf::internal::WireFormatLite::
                   InternalWriteMessageNoVirtualToArray(
                       1, *entry, deterministic, target);
;
        Utf8Check::Check(items[i]);
      }
    } else {
      ::google::protobuf::scoped_ptr<MessageMap_MapEntry> entry;
      for (::google::protobuf::Map< ::std::string, ::google::protobuf::testing::MessageMap_M >::const_iterator
          it = this->map().begin();
          it != this->map().end(); ++it) {
        entry.reset(map_.NewEntryWrapper(
            it->first, it->second));
        target = ::google::protobuf::internal::WireFormatLite::
                   InternalWriteMessageNoVirtualToArray(
                       1, *entry, deterministic, target);
;
        Utf8Check::Check(&*it);
      }
    }
  }

  // @@protoc_insertion_point(serialize_to_array_end:google.protobuf.testing.MessageMap)
  return target;
}

size_t MessageMap::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:google.protobuf.testing.MessageMap)
  size_t total_size = 0;

  // map<string, .google.protobuf.testing.MessageMap.M> map = 1;
  total_size += 1 *
      ::google::protobuf::internal::FromIntSize(this->map_size());
  {
    ::google::protobuf::scoped_ptr<MessageMap_MapEntry> entry;
    for (::google::protobuf::Map< ::std::string, ::google::protobuf::testing::MessageMap_M >::const_iterator
        it = this->map().begin();
        it != this->map().end(); ++it) {
      entry.reset(map_.NewEntryWrapper(it->first, it->second));
      total_size += ::google::protobuf::internal::WireFormatLite::
          MessageSizeNoVirtual(*entry);
    }
  }

  int cached_size = ::google::protobuf::internal::ToCachedSize(total_size);
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = cached_size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
  return total_size;
}

void MessageMap::MergeFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_merge_from_start:google.protobuf.testing.MessageMap)
  if (GOOGLE_PREDICT_FALSE(&from == this)) MergeFromFail(__LINE__);
  const MessageMap* source =
      ::google::protobuf::internal::DynamicCastToGenerated<const MessageMap>(
          &from);
  if (source == NULL) {
  // @@protoc_insertion_point(generalized_merge_from_cast_fail:google.protobuf.testing.MessageMap)
    ::google::protobuf::internal::ReflectionOps::Merge(from, this);
  } else {
  // @@protoc_insertion_point(generalized_merge_from_cast_success:google.protobuf.testing.MessageMap)
    UnsafeMergeFrom(*source);
  }
}

void MessageMap::MergeFrom(const MessageMap& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:google.protobuf.testing.MessageMap)
  if (GOOGLE_PREDICT_TRUE(&from != this)) {
    UnsafeMergeFrom(from);
  } else {
    MergeFromFail(__LINE__);
  }
}

void MessageMap::UnsafeMergeFrom(const MessageMap& from) {
  GOOGLE_DCHECK(&from != this);
  map_.MergeFrom(from.map_);
}

void MessageMap::CopyFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_copy_from_start:google.protobuf.testing.MessageMap)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

void MessageMap::CopyFrom(const MessageMap& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:google.protobuf.testing.MessageMap)
  if (&from == this) return;
  Clear();
  UnsafeMergeFrom(from);
}

bool MessageMap::IsInitialized() const {

  return true;
}

void MessageMap::Swap(MessageMap* other) {
  if (other == this) return;
  InternalSwap(other);
}
void MessageMap::InternalSwap(MessageMap* other) {
  map_.Swap(&other->map_);
  _internal_metadata_.Swap(&other->_internal_metadata_);
  std::swap(_cached_size_, other->_cached_size_);
}

::google::protobuf::Metadata MessageMap::GetMetadata() const {
  protobuf_AssignDescriptorsOnce();
  ::google::protobuf::Metadata metadata;
  metadata.descriptor = MessageMap_descriptor_;
  metadata.reflection = MessageMap_reflection_;
  return metadata;
}

#if PROTOBUF_INLINE_NOT_IN_HEADERS
// MessageMap_M

// optional int32 inner_int = 1;
void MessageMap_M::clear_inner_int() {
  inner_int_ = 0;
}
::google::protobuf::int32 MessageMap_M::inner_int() const {
  // @@protoc_insertion_point(field_get:google.protobuf.testing.MessageMap.M.inner_int)
  return inner_int_;
}
void MessageMap_M::set_inner_int(::google::protobuf::int32 value) {
  
  inner_int_ = value;
  // @@protoc_insertion_point(field_set:google.protobuf.testing.MessageMap.M.inner_int)
}

// optional string inner_text = 2;
void MessageMap_M::clear_inner_text() {
  inner_text_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
const ::std::string& MessageMap_M::inner_text() const {
  // @@protoc_insertion_point(field_get:google.protobuf.testing.MessageMap.M.inner_text)
  return inner_text_.GetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
void MessageMap_M::set_inner_text(const ::std::string& value) {
  
  inner_text_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:google.protobuf.testing.MessageMap.M.inner_text)
}
void MessageMap_M::set_inner_text(const char* value) {
  
  inner_text_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:google.protobuf.testing.MessageMap.M.inner_text)
}
void MessageMap_M::set_inner_text(const char* value, size_t size) {
  
  inner_text_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:google.protobuf.testing.MessageMap.M.inner_text)
}
::std::string* MessageMap_M::mutable_inner_text() {
  
  // @@protoc_insertion_point(field_mutable:google.protobuf.testing.MessageMap.M.inner_text)
  return inner_text_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
::std::string* MessageMap_M::release_inner_text() {
  // @@protoc_insertion_point(field_release:google.protobuf.testing.MessageMap.M.inner_text)
  
  return inner_text_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
void MessageMap_M::set_allocated_inner_text(::std::string* inner_text) {
  if (inner_text != NULL) {
    
  } else {
    
  }
  inner_text_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), inner_text);
  // @@protoc_insertion_point(field_set_allocated:google.protobuf.testing.MessageMap.M.inner_text)
}

inline const MessageMap_M* MessageMap_M::internal_default_instance() {
  return &MessageMap_M_default_instance_.get();
}
// -------------------------------------------------------------------

// MessageMap

// map<string, .google.protobuf.testing.MessageMap.M> map = 1;
int MessageMap::map_size() const {
  return map_.size();
}
void MessageMap::clear_map() {
  map_.Clear();
}
 const ::google::protobuf::Map< ::std::string, ::google::protobuf::testing::MessageMap_M >&
MessageMap::map() const {
  // @@protoc_insertion_point(field_map:google.protobuf.testing.MessageMap.map)
  return map_.GetMap();
}
 ::google::protobuf::Map< ::std::string, ::google::protobuf::testing::MessageMap_M >*
MessageMap::mutable_map() {
  // @@protoc_insertion_point(field_mutable_map:google.protobuf.testing.MessageMap.map)
  return map_.MutableMap();
}

inline const MessageMap* MessageMap::internal_default_instance() {
  return &MessageMap_default_instance_.get();
}
#endif  // PROTOBUF_INLINE_NOT_IN_HEADERS

// ===================================================================

#if !defined(_MSC_VER) || _MSC_VER >= 1900
const int DoubleValueMessage::kDoubleFieldNumber;
#endif  // !defined(_MSC_VER) || _MSC_VER >= 1900

DoubleValueMessage::DoubleValueMessage()
  : ::google::protobuf::Message(), _internal_metadata_(NULL) {
  if (this != internal_default_instance()) protobuf_InitDefaults_google_2fprotobuf_2futil_2finternal_2ftestdata_2fdefault_5fvalue_2eproto();
  SharedCtor();
  // @@protoc_insertion_point(constructor:google.protobuf.testing.DoubleValueMessage)
}

void DoubleValueMessage::InitAsDefaultInstance() {
  double__ = const_cast< ::google::protobuf::DoubleValue*>(
      ::google::protobuf::DoubleValue::internal_default_instance());
}

DoubleValueMessage::DoubleValueMessage(const DoubleValueMessage& from)
  : ::google::protobuf::Message(),
    _internal_metadata_(NULL) {
  SharedCtor();
  UnsafeMergeFrom(from);
  // @@protoc_insertion_point(copy_constructor:google.protobuf.testing.DoubleValueMessage)
}

void DoubleValueMessage::SharedCtor() {
  double__ = NULL;
  _cached_size_ = 0;
}

DoubleValueMessage::~DoubleValueMessage() {
  // @@protoc_insertion_point(destructor:google.protobuf.testing.DoubleValueMessage)
  SharedDtor();
}

void DoubleValueMessage::SharedDtor() {
  if (this != &DoubleValueMessage_default_instance_.get()) {
    delete double__;
  }
}

void DoubleValueMessage::SetCachedSize(int size) const {
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
}
const ::google::protobuf::Descriptor* DoubleValueMessage::descriptor() {
  protobuf_AssignDescriptorsOnce();
  return DoubleValueMessage_descriptor_;
}

const DoubleValueMessage& DoubleValueMessage::default_instance() {
  protobuf_InitDefaults_google_2fprotobuf_2futil_2finternal_2ftestdata_2fdefault_5fvalue_2eproto();
  return *internal_default_instance();
}

::google::protobuf::internal::ExplicitlyConstructed<DoubleValueMessage> DoubleValueMessage_default_instance_;

DoubleValueMessage* DoubleValueMessage::New(::google::protobuf::Arena* arena) const {
  DoubleValueMessage* n = new DoubleValueMessage;
  if (arena != NULL) {
    arena->Own(n);
  }
  return n;
}

void DoubleValueMessage::Clear() {
// @@protoc_insertion_point(message_clear_start:google.protobuf.testing.DoubleValueMessage)
  if (GetArenaNoVirtual() == NULL && double__ != NULL) delete double__;
  double__ = NULL;
}

bool DoubleValueMessage::MergePartialFromCodedStream(
    ::google::protobuf::io::CodedInputStream* input) {
#define DO_(EXPRESSION) if (!GOOGLE_PREDICT_TRUE(EXPRESSION)) goto failure
  ::google::protobuf::uint32 tag;
  // @@protoc_insertion_point(parse_start:google.protobuf.testing.DoubleValueMessage)
  for (;;) {
    ::std::pair< ::google::protobuf::uint32, bool> p = input->ReadTagWithCutoff(127);
    tag = p.first;
    if (!p.second) goto handle_unusual;
    switch (::google::protobuf::internal::WireFormatLite::GetTagFieldNumber(tag)) {
      // optional .google.protobuf.DoubleValue double = 1;
      case 1: {
        if (tag == 10) {
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessageNoVirtual(
               input, mutable_double_()));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectAtEnd()) goto success;
        break;
      }

      default: {
      handle_unusual:
        if (tag == 0 ||
            ::google::protobuf::internal::WireFormatLite::GetTagWireType(tag) ==
            ::google::protobuf::internal::WireFormatLite::WIRETYPE_END_GROUP) {
          goto success;
        }
        DO_(::google::protobuf::internal::WireFormatLite::SkipField(input, tag));
        break;
      }
    }
  }
success:
  // @@protoc_insertion_point(parse_success:google.protobuf.testing.DoubleValueMessage)
  return true;
failure:
  // @@protoc_insertion_point(parse_failure:google.protobuf.testing.DoubleValueMessage)
  return false;
#undef DO_
}

void DoubleValueMessage::SerializeWithCachedSizes(
    ::google::protobuf::io::CodedOutputStream* output) const {
  // @@protoc_insertion_point(serialize_start:google.protobuf.testing.DoubleValueMessage)
  // optional .google.protobuf.DoubleValue double = 1;
  if (this->has_double_()) {
    ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
      1, *this->double__, output);
  }

  // @@protoc_insertion_point(serialize_end:google.protobuf.testing.DoubleValueMessage)
}

::google::protobuf::uint8* DoubleValueMessage::InternalSerializeWithCachedSizesToArray(
    bool deterministic, ::google::protobuf::uint8* target) const {
  (void)deterministic; // Unused
  // @@protoc_insertion_point(serialize_to_array_start:google.protobuf.testing.DoubleValueMessage)
  // optional .google.protobuf.DoubleValue double = 1;
  if (this->has_double_()) {
    target = ::google::protobuf::internal::WireFormatLite::
      InternalWriteMessageNoVirtualToArray(
        1, *this->double__, false, target);
  }

  // @@protoc_insertion_point(serialize_to_array_end:google.protobuf.testing.DoubleValueMessage)
  return target;
}

size_t DoubleValueMessage::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:google.protobuf.testing.DoubleValueMessage)
  size_t total_size = 0;

  // optional .google.protobuf.DoubleValue double = 1;
  if (this->has_double_()) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::MessageSizeNoVirtual(
        *this->double__);
  }

  int cached_size = ::google::protobuf::internal::ToCachedSize(total_size);
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = cached_size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
  return total_size;
}

void DoubleValueMessage::MergeFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_merge_from_start:google.protobuf.testing.DoubleValueMessage)
  if (GOOGLE_PREDICT_FALSE(&from == this)) MergeFromFail(__LINE__);
  const DoubleValueMessage* source =
      ::google::protobuf::internal::DynamicCastToGenerated<const DoubleValueMessage>(
          &from);
  if (source == NULL) {
  // @@protoc_insertion_point(generalized_merge_from_cast_fail:google.protobuf.testing.DoubleValueMessage)
    ::google::protobuf::internal::ReflectionOps::Merge(from, this);
  } else {
  // @@protoc_insertion_point(generalized_merge_from_cast_success:google.protobuf.testing.DoubleValueMessage)
    UnsafeMergeFrom(*source);
  }
}

void DoubleValueMessage::MergeFrom(const DoubleValueMessage& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:google.protobuf.testing.DoubleValueMessage)
  if (GOOGLE_PREDICT_TRUE(&from != this)) {
    UnsafeMergeFrom(from);
  } else {
    MergeFromFail(__LINE__);
  }
}

void DoubleValueMessage::UnsafeMergeFrom(const DoubleValueMessage& from) {
  GOOGLE_DCHECK(&from != this);
  if (from.has_double_()) {
    mutable_double_()->::google::protobuf::DoubleValue::MergeFrom(from.double_());
  }
}

void DoubleValueMessage::CopyFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_copy_from_start:google.protobuf.testing.DoubleValueMessage)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

void DoubleValueMessage::CopyFrom(const DoubleValueMessage& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:google.protobuf.testing.DoubleValueMessage)
  if (&from == this) return;
  Clear();
  UnsafeMergeFrom(from);
}

bool DoubleValueMessage::IsInitialized() const {

  return true;
}

void DoubleValueMessage::Swap(DoubleValueMessage* other) {
  if (other == this) return;
  InternalSwap(other);
}
void DoubleValueMessage::InternalSwap(DoubleValueMessage* other) {
  std::swap(double__, other->double__);
  _internal_metadata_.Swap(&other->_internal_metadata_);
  std::swap(_cached_size_, other->_cached_size_);
}

::google::protobuf::Metadata DoubleValueMessage::GetMetadata() const {
  protobuf_AssignDescriptorsOnce();
  ::google::protobuf::Metadata metadata;
  metadata.descriptor = DoubleValueMessage_descriptor_;
  metadata.reflection = DoubleValueMessage_reflection_;
  return metadata;
}

#if PROTOBUF_INLINE_NOT_IN_HEADERS
// DoubleValueMessage

// optional .google.protobuf.DoubleValue double = 1;
bool DoubleValueMessage::has_double_() const {
  return this != internal_default_instance() && double__ != NULL;
}
void DoubleValueMessage::clear_double_() {
  if (GetArenaNoVirtual() == NULL && double__ != NULL) delete double__;
  double__ = NULL;
}
const ::google::protobuf::DoubleValue& DoubleValueMessage::double_() const {
  // @@protoc_insertion_point(field_get:google.protobuf.testing.DoubleValueMessage.double)
  return double__ != NULL ? *double__
                         : *::google::protobuf::DoubleValue::internal_default_instance();
}
::google::protobuf::DoubleValue* DoubleValueMessage::mutable_double_() {
  
  if (double__ == NULL) {
    double__ = new ::google::protobuf::DoubleValue;
  }
  // @@protoc_insertion_point(field_mutable:google.protobuf.testing.DoubleValueMessage.double)
  return double__;
}
::google::protobuf::DoubleValue* DoubleValueMessage::release_double_() {
  // @@protoc_insertion_point(field_release:google.protobuf.testing.DoubleValueMessage.double)
  
  ::google::protobuf::DoubleValue* temp = double__;
  double__ = NULL;
  return temp;
}
void DoubleValueMessage::set_allocated_double_(::google::protobuf::DoubleValue* double_) {
  delete double__;
  if (double_ != NULL && double_->GetArena() != NULL) {
    ::google::protobuf::DoubleValue* new_double_ = new ::google::protobuf::DoubleValue;
    new_double_->CopyFrom(*double_);
    double_ = new_double_;
  }
  double__ = double_;
  if (double_) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_set_allocated:google.protobuf.testing.DoubleValueMessage.double)
}

inline const DoubleValueMessage* DoubleValueMessage::internal_default_instance() {
  return &DoubleValueMessage_default_instance_.get();
}
#endif  // PROTOBUF_INLINE_NOT_IN_HEADERS

// @@protoc_insertion_point(namespace_scope)

}  // namespace testing
}  // namespace protobuf
}  // namespace google

// @@protoc_insertion_point(global_scope)

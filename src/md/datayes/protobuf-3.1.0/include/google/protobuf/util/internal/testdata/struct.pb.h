// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: google/protobuf/util/internal/testdata/struct.proto

#ifndef PROTOBUF_google_2fprotobuf_2futil_2finternal_2ftestdata_2fstruct_2eproto__INCLUDED
#define PROTOBUF_google_2fprotobuf_2futil_2finternal_2ftestdata_2fstruct_2eproto__INCLUDED

#include <string>

#include <google/protobuf/stubs/common.h>

#if GOOGLE_PROTOBUF_VERSION < 3001000
#error This file was generated by a newer version of protoc which is
#error incompatible with your Protocol Buffer headers.  Please update
#error your headers.
#endif
#if 3001000 < GOOGLE_PROTOBUF_MIN_PROTOC_VERSION
#error This file was generated by an older version of protoc which is
#error incompatible with your Protocol Buffer headers.  Please
#error regenerate this file with a newer version of protoc.
#endif

#include <google/protobuf/arena.h>
#include <google/protobuf/arenastring.h>
#include <google/protobuf/generated_message_util.h>
#include <google/protobuf/metadata.h>
#include <google/protobuf/message.h>
#include <google/protobuf/repeated_field.h>
#include <google/protobuf/extension_set.h>
#include <google/protobuf/map.h>
#include <google/protobuf/map_field_inl.h>
#include <google/protobuf/unknown_field_set.h>
#include <google/protobuf/struct.pb.h>
// @@protoc_insertion_point(includes)

namespace google {
namespace protobuf {
namespace testing {

// Internal implementation detail -- do not call these.
void protobuf_AddDesc_google_2fprotobuf_2futil_2finternal_2ftestdata_2fstruct_2eproto();
void protobuf_InitDefaults_google_2fprotobuf_2futil_2finternal_2ftestdata_2fstruct_2eproto();
void protobuf_AssignDesc_google_2fprotobuf_2futil_2finternal_2ftestdata_2fstruct_2eproto();
void protobuf_ShutdownFile_google_2fprotobuf_2futil_2finternal_2ftestdata_2fstruct_2eproto();

class Dummy;
class ListValueWrapper;
class MapOfStruct;
class RepeatedListValueWrapper;
class RepeatedValueWrapper;
class StructTestCases;
class StructType;
class StructWrapper;
class ValueWrapper;

// ===================================================================

class StructTestCases : public ::google::protobuf::Message /* @@protoc_insertion_point(class_definition:google.protobuf.testing.StructTestCases) */ {
 public:
  StructTestCases();
  virtual ~StructTestCases();

  StructTestCases(const StructTestCases& from);

  inline StructTestCases& operator=(const StructTestCases& from) {
    CopyFrom(from);
    return *this;
  }

  static const ::google::protobuf::Descriptor* descriptor();
  static const StructTestCases& default_instance();

  static const StructTestCases* internal_default_instance();

  void Swap(StructTestCases* other);

  // implements Message ----------------------------------------------

  inline StructTestCases* New() const { return New(NULL); }

  StructTestCases* New(::google::protobuf::Arena* arena) const;
  void CopyFrom(const ::google::protobuf::Message& from);
  void MergeFrom(const ::google::protobuf::Message& from);
  void CopyFrom(const StructTestCases& from);
  void MergeFrom(const StructTestCases& from);
  void Clear();
  bool IsInitialized() const;

  size_t ByteSizeLong() const;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input);
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const;
  ::google::protobuf::uint8* InternalSerializeWithCachedSizesToArray(
      bool deterministic, ::google::protobuf::uint8* output) const;
  ::google::protobuf::uint8* SerializeWithCachedSizesToArray(::google::protobuf::uint8* output) const {
    return InternalSerializeWithCachedSizesToArray(false, output);
  }
  int GetCachedSize() const { return _cached_size_; }
  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const;
  void InternalSwap(StructTestCases* other);
  void UnsafeMergeFrom(const StructTestCases& from);
  private:
  inline ::google::protobuf::Arena* GetArenaNoVirtual() const {
    return _internal_metadata_.arena();
  }
  inline void* MaybeArenaPtr() const {
    return _internal_metadata_.raw_arena_ptr();
  }
  public:

  ::google::protobuf::Metadata GetMetadata() const;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // optional .google.protobuf.testing.StructWrapper empty_value = 1;
  bool has_empty_value() const;
  void clear_empty_value();
  static const int kEmptyValueFieldNumber = 1;
  const ::google::protobuf::testing::StructWrapper& empty_value() const;
  ::google::protobuf::testing::StructWrapper* mutable_empty_value();
  ::google::protobuf::testing::StructWrapper* release_empty_value();
  void set_allocated_empty_value(::google::protobuf::testing::StructWrapper* empty_value);

  // optional .google.protobuf.testing.StructWrapper empty_value2 = 2;
  bool has_empty_value2() const;
  void clear_empty_value2();
  static const int kEmptyValue2FieldNumber = 2;
  const ::google::protobuf::testing::StructWrapper& empty_value2() const;
  ::google::protobuf::testing::StructWrapper* mutable_empty_value2();
  ::google::protobuf::testing::StructWrapper* release_empty_value2();
  void set_allocated_empty_value2(::google::protobuf::testing::StructWrapper* empty_value2);

  // optional .google.protobuf.testing.StructWrapper null_value = 3;
  bool has_null_value() const;
  void clear_null_value();
  static const int kNullValueFieldNumber = 3;
  const ::google::protobuf::testing::StructWrapper& null_value() const;
  ::google::protobuf::testing::StructWrapper* mutable_null_value();
  ::google::protobuf::testing::StructWrapper* release_null_value();
  void set_allocated_null_value(::google::protobuf::testing::StructWrapper* null_value);

  // optional .google.protobuf.testing.StructWrapper simple_struct = 4;
  bool has_simple_struct() const;
  void clear_simple_struct();
  static const int kSimpleStructFieldNumber = 4;
  const ::google::protobuf::testing::StructWrapper& simple_struct() const;
  ::google::protobuf::testing::StructWrapper* mutable_simple_struct();
  ::google::protobuf::testing::StructWrapper* release_simple_struct();
  void set_allocated_simple_struct(::google::protobuf::testing::StructWrapper* simple_struct);

  // optional .google.protobuf.testing.StructWrapper longer_struct = 5;
  bool has_longer_struct() const;
  void clear_longer_struct();
  static const int kLongerStructFieldNumber = 5;
  const ::google::protobuf::testing::StructWrapper& longer_struct() const;
  ::google::protobuf::testing::StructWrapper* mutable_longer_struct();
  ::google::protobuf::testing::StructWrapper* release_longer_struct();
  void set_allocated_longer_struct(::google::protobuf::testing::StructWrapper* longer_struct);

  // optional .google.protobuf.testing.StructWrapper struct_with_nested_struct = 6;
  bool has_struct_with_nested_struct() const;
  void clear_struct_with_nested_struct();
  static const int kStructWithNestedStructFieldNumber = 6;
  const ::google::protobuf::testing::StructWrapper& struct_with_nested_struct() const;
  ::google::protobuf::testing::StructWrapper* mutable_struct_with_nested_struct();
  ::google::protobuf::testing::StructWrapper* release_struct_with_nested_struct();
  void set_allocated_struct_with_nested_struct(::google::protobuf::testing::StructWrapper* struct_with_nested_struct);

  // optional .google.protobuf.testing.StructWrapper struct_with_nested_list = 7;
  bool has_struct_with_nested_list() const;
  void clear_struct_with_nested_list();
  static const int kStructWithNestedListFieldNumber = 7;
  const ::google::protobuf::testing::StructWrapper& struct_with_nested_list() const;
  ::google::protobuf::testing::StructWrapper* mutable_struct_with_nested_list();
  ::google::protobuf::testing::StructWrapper* release_struct_with_nested_list();
  void set_allocated_struct_with_nested_list(::google::protobuf::testing::StructWrapper* struct_with_nested_list);

  // optional .google.protobuf.testing.StructWrapper struct_with_list_of_nulls = 8;
  bool has_struct_with_list_of_nulls() const;
  void clear_struct_with_list_of_nulls();
  static const int kStructWithListOfNullsFieldNumber = 8;
  const ::google::protobuf::testing::StructWrapper& struct_with_list_of_nulls() const;
  ::google::protobuf::testing::StructWrapper* mutable_struct_with_list_of_nulls();
  ::google::protobuf::testing::StructWrapper* release_struct_with_list_of_nulls();
  void set_allocated_struct_with_list_of_nulls(::google::protobuf::testing::StructWrapper* struct_with_list_of_nulls);

  // optional .google.protobuf.testing.StructWrapper struct_with_list_of_lists = 9;
  bool has_struct_with_list_of_lists() const;
  void clear_struct_with_list_of_lists();
  static const int kStructWithListOfListsFieldNumber = 9;
  const ::google::protobuf::testing::StructWrapper& struct_with_list_of_lists() const;
  ::google::protobuf::testing::StructWrapper* mutable_struct_with_list_of_lists();
  ::google::protobuf::testing::StructWrapper* release_struct_with_list_of_lists();
  void set_allocated_struct_with_list_of_lists(::google::protobuf::testing::StructWrapper* struct_with_list_of_lists);

  // optional .google.protobuf.testing.StructWrapper struct_with_list_of_structs = 10;
  bool has_struct_with_list_of_structs() const;
  void clear_struct_with_list_of_structs();
  static const int kStructWithListOfStructsFieldNumber = 10;
  const ::google::protobuf::testing::StructWrapper& struct_with_list_of_structs() const;
  ::google::protobuf::testing::StructWrapper* mutable_struct_with_list_of_structs();
  ::google::protobuf::testing::StructWrapper* release_struct_with_list_of_structs();
  void set_allocated_struct_with_list_of_structs(::google::protobuf::testing::StructWrapper* struct_with_list_of_structs);

  // optional .google.protobuf.testing.StructWrapper struct_with_empty_list = 11;
  bool has_struct_with_empty_list() const;
  void clear_struct_with_empty_list();
  static const int kStructWithEmptyListFieldNumber = 11;
  const ::google::protobuf::testing::StructWrapper& struct_with_empty_list() const;
  ::google::protobuf::testing::StructWrapper* mutable_struct_with_empty_list();
  ::google::protobuf::testing::StructWrapper* release_struct_with_empty_list();
  void set_allocated_struct_with_empty_list(::google::protobuf::testing::StructWrapper* struct_with_empty_list);

  // optional .google.protobuf.testing.StructWrapper struct_with_list_with_empty_struct = 12;
  bool has_struct_with_list_with_empty_struct() const;
  void clear_struct_with_list_with_empty_struct();
  static const int kStructWithListWithEmptyStructFieldNumber = 12;
  const ::google::protobuf::testing::StructWrapper& struct_with_list_with_empty_struct() const;
  ::google::protobuf::testing::StructWrapper* mutable_struct_with_list_with_empty_struct();
  ::google::protobuf::testing::StructWrapper* release_struct_with_list_with_empty_struct();
  void set_allocated_struct_with_list_with_empty_struct(::google::protobuf::testing::StructWrapper* struct_with_list_with_empty_struct);

  // optional .google.protobuf.Struct top_level_struct = 13;
  bool has_top_level_struct() const;
  void clear_top_level_struct();
  static const int kTopLevelStructFieldNumber = 13;
  const ::google::protobuf::Struct& top_level_struct() const;
  ::google::protobuf::Struct* mutable_top_level_struct();
  ::google::protobuf::Struct* release_top_level_struct();
  void set_allocated_top_level_struct(::google::protobuf::Struct* top_level_struct);

  // optional .google.protobuf.Struct top_level_struct_with_empty_list = 14;
  bool has_top_level_struct_with_empty_list() const;
  void clear_top_level_struct_with_empty_list();
  static const int kTopLevelStructWithEmptyListFieldNumber = 14;
  const ::google::protobuf::Struct& top_level_struct_with_empty_list() const;
  ::google::protobuf::Struct* mutable_top_level_struct_with_empty_list();
  ::google::protobuf::Struct* release_top_level_struct_with_empty_list();
  void set_allocated_top_level_struct_with_empty_list(::google::protobuf::Struct* top_level_struct_with_empty_list);

  // optional .google.protobuf.Struct top_level_struct_with_list_with_empty_struct = 15;
  bool has_top_level_struct_with_list_with_empty_struct() const;
  void clear_top_level_struct_with_list_with_empty_struct();
  static const int kTopLevelStructWithListWithEmptyStructFieldNumber = 15;
  const ::google::protobuf::Struct& top_level_struct_with_list_with_empty_struct() const;
  ::google::protobuf::Struct* mutable_top_level_struct_with_list_with_empty_struct();
  ::google::protobuf::Struct* release_top_level_struct_with_list_with_empty_struct();
  void set_allocated_top_level_struct_with_list_with_empty_struct(::google::protobuf::Struct* top_level_struct_with_list_with_empty_struct);

  // optional .google.protobuf.testing.ValueWrapper value_wrapper_simple = 16;
  bool has_value_wrapper_simple() const;
  void clear_value_wrapper_simple();
  static const int kValueWrapperSimpleFieldNumber = 16;
  const ::google::protobuf::testing::ValueWrapper& value_wrapper_simple() const;
  ::google::protobuf::testing::ValueWrapper* mutable_value_wrapper_simple();
  ::google::protobuf::testing::ValueWrapper* release_value_wrapper_simple();
  void set_allocated_value_wrapper_simple(::google::protobuf::testing::ValueWrapper* value_wrapper_simple);

  // optional .google.protobuf.testing.ValueWrapper value_wrapper_with_struct = 17;
  bool has_value_wrapper_with_struct() const;
  void clear_value_wrapper_with_struct();
  static const int kValueWrapperWithStructFieldNumber = 17;
  const ::google::protobuf::testing::ValueWrapper& value_wrapper_with_struct() const;
  ::google::protobuf::testing::ValueWrapper* mutable_value_wrapper_with_struct();
  ::google::protobuf::testing::ValueWrapper* release_value_wrapper_with_struct();
  void set_allocated_value_wrapper_with_struct(::google::protobuf::testing::ValueWrapper* value_wrapper_with_struct);

  // optional .google.protobuf.testing.ValueWrapper value_wrapper_with_list = 18;
  bool has_value_wrapper_with_list() const;
  void clear_value_wrapper_with_list();
  static const int kValueWrapperWithListFieldNumber = 18;
  const ::google::protobuf::testing::ValueWrapper& value_wrapper_with_list() const;
  ::google::protobuf::testing::ValueWrapper* mutable_value_wrapper_with_list();
  ::google::protobuf::testing::ValueWrapper* release_value_wrapper_with_list();
  void set_allocated_value_wrapper_with_list(::google::protobuf::testing::ValueWrapper* value_wrapper_with_list);

  // optional .google.protobuf.testing.ValueWrapper value_wrapper_with_empty_list = 19;
  bool has_value_wrapper_with_empty_list() const;
  void clear_value_wrapper_with_empty_list();
  static const int kValueWrapperWithEmptyListFieldNumber = 19;
  const ::google::protobuf::testing::ValueWrapper& value_wrapper_with_empty_list() const;
  ::google::protobuf::testing::ValueWrapper* mutable_value_wrapper_with_empty_list();
  ::google::protobuf::testing::ValueWrapper* release_value_wrapper_with_empty_list();
  void set_allocated_value_wrapper_with_empty_list(::google::protobuf::testing::ValueWrapper* value_wrapper_with_empty_list);

  // optional .google.protobuf.testing.ValueWrapper value_wrapper_with_list_with_empty_struct = 20;
  bool has_value_wrapper_with_list_with_empty_struct() const;
  void clear_value_wrapper_with_list_with_empty_struct();
  static const int kValueWrapperWithListWithEmptyStructFieldNumber = 20;
  const ::google::protobuf::testing::ValueWrapper& value_wrapper_with_list_with_empty_struct() const;
  ::google::protobuf::testing::ValueWrapper* mutable_value_wrapper_with_list_with_empty_struct();
  ::google::protobuf::testing::ValueWrapper* release_value_wrapper_with_list_with_empty_struct();
  void set_allocated_value_wrapper_with_list_with_empty_struct(::google::protobuf::testing::ValueWrapper* value_wrapper_with_list_with_empty_struct);

  // optional .google.protobuf.testing.ListValueWrapper list_value_wrapper = 21;
  bool has_list_value_wrapper() const;
  void clear_list_value_wrapper();
  static const int kListValueWrapperFieldNumber = 21;
  const ::google::protobuf::testing::ListValueWrapper& list_value_wrapper() const;
  ::google::protobuf::testing::ListValueWrapper* mutable_list_value_wrapper();
  ::google::protobuf::testing::ListValueWrapper* release_list_value_wrapper();
  void set_allocated_list_value_wrapper(::google::protobuf::testing::ListValueWrapper* list_value_wrapper);

  // optional .google.protobuf.testing.ListValueWrapper list_value_wrapper_with_empty_list = 22;
  bool has_list_value_wrapper_with_empty_list() const;
  void clear_list_value_wrapper_with_empty_list();
  static const int kListValueWrapperWithEmptyListFieldNumber = 22;
  const ::google::protobuf::testing::ListValueWrapper& list_value_wrapper_with_empty_list() const;
  ::google::protobuf::testing::ListValueWrapper* mutable_list_value_wrapper_with_empty_list();
  ::google::protobuf::testing::ListValueWrapper* release_list_value_wrapper_with_empty_list();
  void set_allocated_list_value_wrapper_with_empty_list(::google::protobuf::testing::ListValueWrapper* list_value_wrapper_with_empty_list);

  // optional .google.protobuf.testing.ListValueWrapper list_value_wrapper_with_list_with_empty_struct = 23;
  bool has_list_value_wrapper_with_list_with_empty_struct() const;
  void clear_list_value_wrapper_with_list_with_empty_struct();
  static const int kListValueWrapperWithListWithEmptyStructFieldNumber = 23;
  const ::google::protobuf::testing::ListValueWrapper& list_value_wrapper_with_list_with_empty_struct() const;
  ::google::protobuf::testing::ListValueWrapper* mutable_list_value_wrapper_with_list_with_empty_struct();
  ::google::protobuf::testing::ListValueWrapper* release_list_value_wrapper_with_list_with_empty_struct();
  void set_allocated_list_value_wrapper_with_list_with_empty_struct(::google::protobuf::testing::ListValueWrapper* list_value_wrapper_with_list_with_empty_struct);

  // optional .google.protobuf.Value top_level_value_simple = 24;
  bool has_top_level_value_simple() const;
  void clear_top_level_value_simple();
  static const int kTopLevelValueSimpleFieldNumber = 24;
  const ::google::protobuf::Value& top_level_value_simple() const;
  ::google::protobuf::Value* mutable_top_level_value_simple();
  ::google::protobuf::Value* release_top_level_value_simple();
  void set_allocated_top_level_value_simple(::google::protobuf::Value* top_level_value_simple);

  // optional .google.protobuf.Value top_level_value_with_struct = 25;
  bool has_top_level_value_with_struct() const;
  void clear_top_level_value_with_struct();
  static const int kTopLevelValueWithStructFieldNumber = 25;
  const ::google::protobuf::Value& top_level_value_with_struct() const;
  ::google::protobuf::Value* mutable_top_level_value_with_struct();
  ::google::protobuf::Value* release_top_level_value_with_struct();
  void set_allocated_top_level_value_with_struct(::google::protobuf::Value* top_level_value_with_struct);

  // optional .google.protobuf.Value top_level_value_with_list = 26;
  bool has_top_level_value_with_list() const;
  void clear_top_level_value_with_list();
  static const int kTopLevelValueWithListFieldNumber = 26;
  const ::google::protobuf::Value& top_level_value_with_list() const;
  ::google::protobuf::Value* mutable_top_level_value_with_list();
  ::google::protobuf::Value* release_top_level_value_with_list();
  void set_allocated_top_level_value_with_list(::google::protobuf::Value* top_level_value_with_list);

  // optional .google.protobuf.Value top_level_value_with_empty_list = 27;
  bool has_top_level_value_with_empty_list() const;
  void clear_top_level_value_with_empty_list();
  static const int kTopLevelValueWithEmptyListFieldNumber = 27;
  const ::google::protobuf::Value& top_level_value_with_empty_list() const;
  ::google::protobuf::Value* mutable_top_level_value_with_empty_list();
  ::google::protobuf::Value* release_top_level_value_with_empty_list();
  void set_allocated_top_level_value_with_empty_list(::google::protobuf::Value* top_level_value_with_empty_list);

  // optional .google.protobuf.Value top_level_value_with_list_with_empty_struct = 28;
  bool has_top_level_value_with_list_with_empty_struct() const;
  void clear_top_level_value_with_list_with_empty_struct();
  static const int kTopLevelValueWithListWithEmptyStructFieldNumber = 28;
  const ::google::protobuf::Value& top_level_value_with_list_with_empty_struct() const;
  ::google::protobuf::Value* mutable_top_level_value_with_list_with_empty_struct();
  ::google::protobuf::Value* release_top_level_value_with_list_with_empty_struct();
  void set_allocated_top_level_value_with_list_with_empty_struct(::google::protobuf::Value* top_level_value_with_list_with_empty_struct);

  // optional .google.protobuf.ListValue top_level_listvalue = 29;
  bool has_top_level_listvalue() const;
  void clear_top_level_listvalue();
  static const int kTopLevelListvalueFieldNumber = 29;
  const ::google::protobuf::ListValue& top_level_listvalue() const;
  ::google::protobuf::ListValue* mutable_top_level_listvalue();
  ::google::protobuf::ListValue* release_top_level_listvalue();
  void set_allocated_top_level_listvalue(::google::protobuf::ListValue* top_level_listvalue);

  // optional .google.protobuf.ListValue top_level_empty_listvalue = 30;
  bool has_top_level_empty_listvalue() const;
  void clear_top_level_empty_listvalue();
  static const int kTopLevelEmptyListvalueFieldNumber = 30;
  const ::google::protobuf::ListValue& top_level_empty_listvalue() const;
  ::google::protobuf::ListValue* mutable_top_level_empty_listvalue();
  ::google::protobuf::ListValue* release_top_level_empty_listvalue();
  void set_allocated_top_level_empty_listvalue(::google::protobuf::ListValue* top_level_empty_listvalue);

  // optional .google.protobuf.ListValue top_level_listvalue_with_empty_struct = 31;
  bool has_top_level_listvalue_with_empty_struct() const;
  void clear_top_level_listvalue_with_empty_struct();
  static const int kTopLevelListvalueWithEmptyStructFieldNumber = 31;
  const ::google::protobuf::ListValue& top_level_listvalue_with_empty_struct() const;
  ::google::protobuf::ListValue* mutable_top_level_listvalue_with_empty_struct();
  ::google::protobuf::ListValue* release_top_level_listvalue_with_empty_struct();
  void set_allocated_top_level_listvalue_with_empty_struct(::google::protobuf::ListValue* top_level_listvalue_with_empty_struct);

  // optional .google.protobuf.testing.RepeatedValueWrapper repeated_value = 32;
  bool has_repeated_value() const;
  void clear_repeated_value();
  static const int kRepeatedValueFieldNumber = 32;
  const ::google::protobuf::testing::RepeatedValueWrapper& repeated_value() const;
  ::google::protobuf::testing::RepeatedValueWrapper* mutable_repeated_value();
  ::google::protobuf::testing::RepeatedValueWrapper* release_repeated_value();
  void set_allocated_repeated_value(::google::protobuf::testing::RepeatedValueWrapper* repeated_value);

  // optional .google.protobuf.testing.RepeatedValueWrapper repeated_value_nested_list = 33;
  bool has_repeated_value_nested_list() const;
  void clear_repeated_value_nested_list();
  static const int kRepeatedValueNestedListFieldNumber = 33;
  const ::google::protobuf::testing::RepeatedValueWrapper& repeated_value_nested_list() const;
  ::google::protobuf::testing::RepeatedValueWrapper* mutable_repeated_value_nested_list();
  ::google::protobuf::testing::RepeatedValueWrapper* release_repeated_value_nested_list();
  void set_allocated_repeated_value_nested_list(::google::protobuf::testing::RepeatedValueWrapper* repeated_value_nested_list);

  // optional .google.protobuf.testing.RepeatedValueWrapper repeated_value_nested_list2 = 34;
  bool has_repeated_value_nested_list2() const;
  void clear_repeated_value_nested_list2();
  static const int kRepeatedValueNestedList2FieldNumber = 34;
  const ::google::protobuf::testing::RepeatedValueWrapper& repeated_value_nested_list2() const;
  ::google::protobuf::testing::RepeatedValueWrapper* mutable_repeated_value_nested_list2();
  ::google::protobuf::testing::RepeatedValueWrapper* release_repeated_value_nested_list2();
  void set_allocated_repeated_value_nested_list2(::google::protobuf::testing::RepeatedValueWrapper* repeated_value_nested_list2);

  // optional .google.protobuf.testing.RepeatedValueWrapper repeated_value_nested_list3 = 35;
  bool has_repeated_value_nested_list3() const;
  void clear_repeated_value_nested_list3();
  static const int kRepeatedValueNestedList3FieldNumber = 35;
  const ::google::protobuf::testing::RepeatedValueWrapper& repeated_value_nested_list3() const;
  ::google::protobuf::testing::RepeatedValueWrapper* mutable_repeated_value_nested_list3();
  ::google::protobuf::testing::RepeatedValueWrapper* release_repeated_value_nested_list3();
  void set_allocated_repeated_value_nested_list3(::google::protobuf::testing::RepeatedValueWrapper* repeated_value_nested_list3);

  // optional .google.protobuf.testing.RepeatedListValueWrapper repeated_listvalue = 36;
  bool has_repeated_listvalue() const;
  void clear_repeated_listvalue();
  static const int kRepeatedListvalueFieldNumber = 36;
  const ::google::protobuf::testing::RepeatedListValueWrapper& repeated_listvalue() const;
  ::google::protobuf::testing::RepeatedListValueWrapper* mutable_repeated_listvalue();
  ::google::protobuf::testing::RepeatedListValueWrapper* release_repeated_listvalue();
  void set_allocated_repeated_listvalue(::google::protobuf::testing::RepeatedListValueWrapper* repeated_listvalue);

  // optional .google.protobuf.testing.MapOfStruct map_of_struct = 37;
  bool has_map_of_struct() const;
  void clear_map_of_struct();
  static const int kMapOfStructFieldNumber = 37;
  const ::google::protobuf::testing::MapOfStruct& map_of_struct() const;
  ::google::protobuf::testing::MapOfStruct* mutable_map_of_struct();
  ::google::protobuf::testing::MapOfStruct* release_map_of_struct();
  void set_allocated_map_of_struct(::google::protobuf::testing::MapOfStruct* map_of_struct);

  // optional .google.protobuf.testing.MapOfStruct map_of_struct_value = 38;
  bool has_map_of_struct_value() const;
  void clear_map_of_struct_value();
  static const int kMapOfStructValueFieldNumber = 38;
  const ::google::protobuf::testing::MapOfStruct& map_of_struct_value() const;
  ::google::protobuf::testing::MapOfStruct* mutable_map_of_struct_value();
  ::google::protobuf::testing::MapOfStruct* release_map_of_struct_value();
  void set_allocated_map_of_struct_value(::google::protobuf::testing::MapOfStruct* map_of_struct_value);

  // optional .google.protobuf.testing.MapOfStruct map_of_listvalue = 39;
  bool has_map_of_listvalue() const;
  void clear_map_of_listvalue();
  static const int kMapOfListvalueFieldNumber = 39;
  const ::google::protobuf::testing::MapOfStruct& map_of_listvalue() const;
  ::google::protobuf::testing::MapOfStruct* mutable_map_of_listvalue();
  ::google::protobuf::testing::MapOfStruct* release_map_of_listvalue();
  void set_allocated_map_of_listvalue(::google::protobuf::testing::MapOfStruct* map_of_listvalue);

  // @@protoc_insertion_point(class_scope:google.protobuf.testing.StructTestCases)
 private:

  ::google::protobuf::internal::InternalMetadataWithArena _internal_metadata_;
  ::google::protobuf::testing::StructWrapper* empty_value_;
  ::google::protobuf::testing::StructWrapper* empty_value2_;
  ::google::protobuf::testing::StructWrapper* null_value_;
  ::google::protobuf::testing::StructWrapper* simple_struct_;
  ::google::protobuf::testing::StructWrapper* longer_struct_;
  ::google::protobuf::testing::StructWrapper* struct_with_nested_struct_;
  ::google::protobuf::testing::StructWrapper* struct_with_nested_list_;
  ::google::protobuf::testing::StructWrapper* struct_with_list_of_nulls_;
  ::google::protobuf::testing::StructWrapper* struct_with_list_of_lists_;
  ::google::protobuf::testing::StructWrapper* struct_with_list_of_structs_;
  ::google::protobuf::testing::StructWrapper* struct_with_empty_list_;
  ::google::protobuf::testing::StructWrapper* struct_with_list_with_empty_struct_;
  ::google::protobuf::Struct* top_level_struct_;
  ::google::protobuf::Struct* top_level_struct_with_empty_list_;
  ::google::protobuf::Struct* top_level_struct_with_list_with_empty_struct_;
  ::google::protobuf::testing::ValueWrapper* value_wrapper_simple_;
  ::google::protobuf::testing::ValueWrapper* value_wrapper_with_struct_;
  ::google::protobuf::testing::ValueWrapper* value_wrapper_with_list_;
  ::google::protobuf::testing::ValueWrapper* value_wrapper_with_empty_list_;
  ::google::protobuf::testing::ValueWrapper* value_wrapper_with_list_with_empty_struct_;
  ::google::protobuf::testing::ListValueWrapper* list_value_wrapper_;
  ::google::protobuf::testing::ListValueWrapper* list_value_wrapper_with_empty_list_;
  ::google::protobuf::testing::ListValueWrapper* list_value_wrapper_with_list_with_empty_struct_;
  ::google::protobuf::Value* top_level_value_simple_;
  ::google::protobuf::Value* top_level_value_with_struct_;
  ::google::protobuf::Value* top_level_value_with_list_;
  ::google::protobuf::Value* top_level_value_with_empty_list_;
  ::google::protobuf::Value* top_level_value_with_list_with_empty_struct_;
  ::google::protobuf::ListValue* top_level_listvalue_;
  ::google::protobuf::ListValue* top_level_empty_listvalue_;
  ::google::protobuf::ListValue* top_level_listvalue_with_empty_struct_;
  ::google::protobuf::testing::RepeatedValueWrapper* repeated_value_;
  ::google::protobuf::testing::RepeatedValueWrapper* repeated_value_nested_list_;
  ::google::protobuf::testing::RepeatedValueWrapper* repeated_value_nested_list2_;
  ::google::protobuf::testing::RepeatedValueWrapper* repeated_value_nested_list3_;
  ::google::protobuf::testing::RepeatedListValueWrapper* repeated_listvalue_;
  ::google::protobuf::testing::MapOfStruct* map_of_struct_;
  ::google::protobuf::testing::MapOfStruct* map_of_struct_value_;
  ::google::protobuf::testing::MapOfStruct* map_of_listvalue_;
  mutable int _cached_size_;
  friend void  protobuf_InitDefaults_google_2fprotobuf_2futil_2finternal_2ftestdata_2fstruct_2eproto_impl();
  friend void  protobuf_AddDesc_google_2fprotobuf_2futil_2finternal_2ftestdata_2fstruct_2eproto_impl();
  friend void protobuf_AssignDesc_google_2fprotobuf_2futil_2finternal_2ftestdata_2fstruct_2eproto();
  friend void protobuf_ShutdownFile_google_2fprotobuf_2futil_2finternal_2ftestdata_2fstruct_2eproto();

  void InitAsDefaultInstance();
};
extern ::google::protobuf::internal::ExplicitlyConstructed<StructTestCases> StructTestCases_default_instance_;

// -------------------------------------------------------------------

class StructWrapper : public ::google::protobuf::Message /* @@protoc_insertion_point(class_definition:google.protobuf.testing.StructWrapper) */ {
 public:
  StructWrapper();
  virtual ~StructWrapper();

  StructWrapper(const StructWrapper& from);

  inline StructWrapper& operator=(const StructWrapper& from) {
    CopyFrom(from);
    return *this;
  }

  static const ::google::protobuf::Descriptor* descriptor();
  static const StructWrapper& default_instance();

  static const StructWrapper* internal_default_instance();

  void Swap(StructWrapper* other);

  // implements Message ----------------------------------------------

  inline StructWrapper* New() const { return New(NULL); }

  StructWrapper* New(::google::protobuf::Arena* arena) const;
  void CopyFrom(const ::google::protobuf::Message& from);
  void MergeFrom(const ::google::protobuf::Message& from);
  void CopyFrom(const StructWrapper& from);
  void MergeFrom(const StructWrapper& from);
  void Clear();
  bool IsInitialized() const;

  size_t ByteSizeLong() const;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input);
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const;
  ::google::protobuf::uint8* InternalSerializeWithCachedSizesToArray(
      bool deterministic, ::google::protobuf::uint8* output) const;
  ::google::protobuf::uint8* SerializeWithCachedSizesToArray(::google::protobuf::uint8* output) const {
    return InternalSerializeWithCachedSizesToArray(false, output);
  }
  int GetCachedSize() const { return _cached_size_; }
  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const;
  void InternalSwap(StructWrapper* other);
  void UnsafeMergeFrom(const StructWrapper& from);
  private:
  inline ::google::protobuf::Arena* GetArenaNoVirtual() const {
    return _internal_metadata_.arena();
  }
  inline void* MaybeArenaPtr() const {
    return _internal_metadata_.raw_arena_ptr();
  }
  public:

  ::google::protobuf::Metadata GetMetadata() const;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // optional .google.protobuf.Struct struct = 1;
  bool has_struct_() const;
  void clear_struct_();
  static const int kStructFieldNumber = 1;
  const ::google::protobuf::Struct& struct_() const;
  ::google::protobuf::Struct* mutable_struct_();
  ::google::protobuf::Struct* release_struct_();
  void set_allocated_struct_(::google::protobuf::Struct* struct_);

  // @@protoc_insertion_point(class_scope:google.protobuf.testing.StructWrapper)
 private:

  ::google::protobuf::internal::InternalMetadataWithArena _internal_metadata_;
  ::google::protobuf::Struct* struct__;
  mutable int _cached_size_;
  friend void  protobuf_InitDefaults_google_2fprotobuf_2futil_2finternal_2ftestdata_2fstruct_2eproto_impl();
  friend void  protobuf_AddDesc_google_2fprotobuf_2futil_2finternal_2ftestdata_2fstruct_2eproto_impl();
  friend void protobuf_AssignDesc_google_2fprotobuf_2futil_2finternal_2ftestdata_2fstruct_2eproto();
  friend void protobuf_ShutdownFile_google_2fprotobuf_2futil_2finternal_2ftestdata_2fstruct_2eproto();

  void InitAsDefaultInstance();
};
extern ::google::protobuf::internal::ExplicitlyConstructed<StructWrapper> StructWrapper_default_instance_;

// -------------------------------------------------------------------

class ValueWrapper : public ::google::protobuf::Message /* @@protoc_insertion_point(class_definition:google.protobuf.testing.ValueWrapper) */ {
 public:
  ValueWrapper();
  virtual ~ValueWrapper();

  ValueWrapper(const ValueWrapper& from);

  inline ValueWrapper& operator=(const ValueWrapper& from) {
    CopyFrom(from);
    return *this;
  }

  static const ::google::protobuf::Descriptor* descriptor();
  static const ValueWrapper& default_instance();

  static const ValueWrapper* internal_default_instance();

  void Swap(ValueWrapper* other);

  // implements Message ----------------------------------------------

  inline ValueWrapper* New() const { return New(NULL); }

  ValueWrapper* New(::google::protobuf::Arena* arena) const;
  void CopyFrom(const ::google::protobuf::Message& from);
  void MergeFrom(const ::google::protobuf::Message& from);
  void CopyFrom(const ValueWrapper& from);
  void MergeFrom(const ValueWrapper& from);
  void Clear();
  bool IsInitialized() const;

  size_t ByteSizeLong() const;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input);
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const;
  ::google::protobuf::uint8* InternalSerializeWithCachedSizesToArray(
      bool deterministic, ::google::protobuf::uint8* output) const;
  ::google::protobuf::uint8* SerializeWithCachedSizesToArray(::google::protobuf::uint8* output) const {
    return InternalSerializeWithCachedSizesToArray(false, output);
  }
  int GetCachedSize() const { return _cached_size_; }
  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const;
  void InternalSwap(ValueWrapper* other);
  void UnsafeMergeFrom(const ValueWrapper& from);
  private:
  inline ::google::protobuf::Arena* GetArenaNoVirtual() const {
    return _internal_metadata_.arena();
  }
  inline void* MaybeArenaPtr() const {
    return _internal_metadata_.raw_arena_ptr();
  }
  public:

  ::google::protobuf::Metadata GetMetadata() const;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // optional .google.protobuf.Value value = 1;
  bool has_value() const;
  void clear_value();
  static const int kValueFieldNumber = 1;
  const ::google::protobuf::Value& value() const;
  ::google::protobuf::Value* mutable_value();
  ::google::protobuf::Value* release_value();
  void set_allocated_value(::google::protobuf::Value* value);

  // @@protoc_insertion_point(class_scope:google.protobuf.testing.ValueWrapper)
 private:

  ::google::protobuf::internal::InternalMetadataWithArena _internal_metadata_;
  ::google::protobuf::Value* value_;
  mutable int _cached_size_;
  friend void  protobuf_InitDefaults_google_2fprotobuf_2futil_2finternal_2ftestdata_2fstruct_2eproto_impl();
  friend void  protobuf_AddDesc_google_2fprotobuf_2futil_2finternal_2ftestdata_2fstruct_2eproto_impl();
  friend void protobuf_AssignDesc_google_2fprotobuf_2futil_2finternal_2ftestdata_2fstruct_2eproto();
  friend void protobuf_ShutdownFile_google_2fprotobuf_2futil_2finternal_2ftestdata_2fstruct_2eproto();

  void InitAsDefaultInstance();
};
extern ::google::protobuf::internal::ExplicitlyConstructed<ValueWrapper> ValueWrapper_default_instance_;

// -------------------------------------------------------------------

class RepeatedValueWrapper : public ::google::protobuf::Message /* @@protoc_insertion_point(class_definition:google.protobuf.testing.RepeatedValueWrapper) */ {
 public:
  RepeatedValueWrapper();
  virtual ~RepeatedValueWrapper();

  RepeatedValueWrapper(const RepeatedValueWrapper& from);

  inline RepeatedValueWrapper& operator=(const RepeatedValueWrapper& from) {
    CopyFrom(from);
    return *this;
  }

  static const ::google::protobuf::Descriptor* descriptor();
  static const RepeatedValueWrapper& default_instance();

  static const RepeatedValueWrapper* internal_default_instance();

  void Swap(RepeatedValueWrapper* other);

  // implements Message ----------------------------------------------

  inline RepeatedValueWrapper* New() const { return New(NULL); }

  RepeatedValueWrapper* New(::google::protobuf::Arena* arena) const;
  void CopyFrom(const ::google::protobuf::Message& from);
  void MergeFrom(const ::google::protobuf::Message& from);
  void CopyFrom(const RepeatedValueWrapper& from);
  void MergeFrom(const RepeatedValueWrapper& from);
  void Clear();
  bool IsInitialized() const;

  size_t ByteSizeLong() const;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input);
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const;
  ::google::protobuf::uint8* InternalSerializeWithCachedSizesToArray(
      bool deterministic, ::google::protobuf::uint8* output) const;
  ::google::protobuf::uint8* SerializeWithCachedSizesToArray(::google::protobuf::uint8* output) const {
    return InternalSerializeWithCachedSizesToArray(false, output);
  }
  int GetCachedSize() const { return _cached_size_; }
  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const;
  void InternalSwap(RepeatedValueWrapper* other);
  void UnsafeMergeFrom(const RepeatedValueWrapper& from);
  private:
  inline ::google::protobuf::Arena* GetArenaNoVirtual() const {
    return _internal_metadata_.arena();
  }
  inline void* MaybeArenaPtr() const {
    return _internal_metadata_.raw_arena_ptr();
  }
  public:

  ::google::protobuf::Metadata GetMetadata() const;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // repeated .google.protobuf.Value values = 1;
  int values_size() const;
  void clear_values();
  static const int kValuesFieldNumber = 1;
  const ::google::protobuf::Value& values(int index) const;
  ::google::protobuf::Value* mutable_values(int index);
  ::google::protobuf::Value* add_values();
  ::google::protobuf::RepeatedPtrField< ::google::protobuf::Value >*
      mutable_values();
  const ::google::protobuf::RepeatedPtrField< ::google::protobuf::Value >&
      values() const;

  // @@protoc_insertion_point(class_scope:google.protobuf.testing.RepeatedValueWrapper)
 private:

  ::google::protobuf::internal::InternalMetadataWithArena _internal_metadata_;
  ::google::protobuf::RepeatedPtrField< ::google::protobuf::Value > values_;
  mutable int _cached_size_;
  friend void  protobuf_InitDefaults_google_2fprotobuf_2futil_2finternal_2ftestdata_2fstruct_2eproto_impl();
  friend void  protobuf_AddDesc_google_2fprotobuf_2futil_2finternal_2ftestdata_2fstruct_2eproto_impl();
  friend void protobuf_AssignDesc_google_2fprotobuf_2futil_2finternal_2ftestdata_2fstruct_2eproto();
  friend void protobuf_ShutdownFile_google_2fprotobuf_2futil_2finternal_2ftestdata_2fstruct_2eproto();

  void InitAsDefaultInstance();
};
extern ::google::protobuf::internal::ExplicitlyConstructed<RepeatedValueWrapper> RepeatedValueWrapper_default_instance_;

// -------------------------------------------------------------------

class ListValueWrapper : public ::google::protobuf::Message /* @@protoc_insertion_point(class_definition:google.protobuf.testing.ListValueWrapper) */ {
 public:
  ListValueWrapper();
  virtual ~ListValueWrapper();

  ListValueWrapper(const ListValueWrapper& from);

  inline ListValueWrapper& operator=(const ListValueWrapper& from) {
    CopyFrom(from);
    return *this;
  }

  static const ::google::protobuf::Descriptor* descriptor();
  static const ListValueWrapper& default_instance();

  static const ListValueWrapper* internal_default_instance();

  void Swap(ListValueWrapper* other);

  // implements Message ----------------------------------------------

  inline ListValueWrapper* New() const { return New(NULL); }

  ListValueWrapper* New(::google::protobuf::Arena* arena) const;
  void CopyFrom(const ::google::protobuf::Message& from);
  void MergeFrom(const ::google::protobuf::Message& from);
  void CopyFrom(const ListValueWrapper& from);
  void MergeFrom(const ListValueWrapper& from);
  void Clear();
  bool IsInitialized() const;

  size_t ByteSizeLong() const;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input);
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const;
  ::google::protobuf::uint8* InternalSerializeWithCachedSizesToArray(
      bool deterministic, ::google::protobuf::uint8* output) const;
  ::google::protobuf::uint8* SerializeWithCachedSizesToArray(::google::protobuf::uint8* output) const {
    return InternalSerializeWithCachedSizesToArray(false, output);
  }
  int GetCachedSize() const { return _cached_size_; }
  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const;
  void InternalSwap(ListValueWrapper* other);
  void UnsafeMergeFrom(const ListValueWrapper& from);
  private:
  inline ::google::protobuf::Arena* GetArenaNoVirtual() const {
    return _internal_metadata_.arena();
  }
  inline void* MaybeArenaPtr() const {
    return _internal_metadata_.raw_arena_ptr();
  }
  public:

  ::google::protobuf::Metadata GetMetadata() const;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // optional .google.protobuf.ListValue shopping_list = 1;
  bool has_shopping_list() const;
  void clear_shopping_list();
  static const int kShoppingListFieldNumber = 1;
  const ::google::protobuf::ListValue& shopping_list() const;
  ::google::protobuf::ListValue* mutable_shopping_list();
  ::google::protobuf::ListValue* release_shopping_list();
  void set_allocated_shopping_list(::google::protobuf::ListValue* shopping_list);

  // @@protoc_insertion_point(class_scope:google.protobuf.testing.ListValueWrapper)
 private:

  ::google::protobuf::internal::InternalMetadataWithArena _internal_metadata_;
  ::google::protobuf::ListValue* shopping_list_;
  mutable int _cached_size_;
  friend void  protobuf_InitDefaults_google_2fprotobuf_2futil_2finternal_2ftestdata_2fstruct_2eproto_impl();
  friend void  protobuf_AddDesc_google_2fprotobuf_2futil_2finternal_2ftestdata_2fstruct_2eproto_impl();
  friend void protobuf_AssignDesc_google_2fprotobuf_2futil_2finternal_2ftestdata_2fstruct_2eproto();
  friend void protobuf_ShutdownFile_google_2fprotobuf_2futil_2finternal_2ftestdata_2fstruct_2eproto();

  void InitAsDefaultInstance();
};
extern ::google::protobuf::internal::ExplicitlyConstructed<ListValueWrapper> ListValueWrapper_default_instance_;

// -------------------------------------------------------------------

class RepeatedListValueWrapper : public ::google::protobuf::Message /* @@protoc_insertion_point(class_definition:google.protobuf.testing.RepeatedListValueWrapper) */ {
 public:
  RepeatedListValueWrapper();
  virtual ~RepeatedListValueWrapper();

  RepeatedListValueWrapper(const RepeatedListValueWrapper& from);

  inline RepeatedListValueWrapper& operator=(const RepeatedListValueWrapper& from) {
    CopyFrom(from);
    return *this;
  }

  static const ::google::protobuf::Descriptor* descriptor();
  static const RepeatedListValueWrapper& default_instance();

  static const RepeatedListValueWrapper* internal_default_instance();

  void Swap(RepeatedListValueWrapper* other);

  // implements Message ----------------------------------------------

  inline RepeatedListValueWrapper* New() const { return New(NULL); }

  RepeatedListValueWrapper* New(::google::protobuf::Arena* arena) const;
  void CopyFrom(const ::google::protobuf::Message& from);
  void MergeFrom(const ::google::protobuf::Message& from);
  void CopyFrom(const RepeatedListValueWrapper& from);
  void MergeFrom(const RepeatedListValueWrapper& from);
  void Clear();
  bool IsInitialized() const;

  size_t ByteSizeLong() const;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input);
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const;
  ::google::protobuf::uint8* InternalSerializeWithCachedSizesToArray(
      bool deterministic, ::google::protobuf::uint8* output) const;
  ::google::protobuf::uint8* SerializeWithCachedSizesToArray(::google::protobuf::uint8* output) const {
    return InternalSerializeWithCachedSizesToArray(false, output);
  }
  int GetCachedSize() const { return _cached_size_; }
  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const;
  void InternalSwap(RepeatedListValueWrapper* other);
  void UnsafeMergeFrom(const RepeatedListValueWrapper& from);
  private:
  inline ::google::protobuf::Arena* GetArenaNoVirtual() const {
    return _internal_metadata_.arena();
  }
  inline void* MaybeArenaPtr() const {
    return _internal_metadata_.raw_arena_ptr();
  }
  public:

  ::google::protobuf::Metadata GetMetadata() const;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // repeated .google.protobuf.ListValue dimensions = 1;
  int dimensions_size() const;
  void clear_dimensions();
  static const int kDimensionsFieldNumber = 1;
  const ::google::protobuf::ListValue& dimensions(int index) const;
  ::google::protobuf::ListValue* mutable_dimensions(int index);
  ::google::protobuf::ListValue* add_dimensions();
  ::google::protobuf::RepeatedPtrField< ::google::protobuf::ListValue >*
      mutable_dimensions();
  const ::google::protobuf::RepeatedPtrField< ::google::protobuf::ListValue >&
      dimensions() const;

  // @@protoc_insertion_point(class_scope:google.protobuf.testing.RepeatedListValueWrapper)
 private:

  ::google::protobuf::internal::InternalMetadataWithArena _internal_metadata_;
  ::google::protobuf::RepeatedPtrField< ::google::protobuf::ListValue > dimensions_;
  mutable int _cached_size_;
  friend void  protobuf_InitDefaults_google_2fprotobuf_2futil_2finternal_2ftestdata_2fstruct_2eproto_impl();
  friend void  protobuf_AddDesc_google_2fprotobuf_2futil_2finternal_2ftestdata_2fstruct_2eproto_impl();
  friend void protobuf_AssignDesc_google_2fprotobuf_2futil_2finternal_2ftestdata_2fstruct_2eproto();
  friend void protobuf_ShutdownFile_google_2fprotobuf_2futil_2finternal_2ftestdata_2fstruct_2eproto();

  void InitAsDefaultInstance();
};
extern ::google::protobuf::internal::ExplicitlyConstructed<RepeatedListValueWrapper> RepeatedListValueWrapper_default_instance_;

// -------------------------------------------------------------------

class MapOfStruct : public ::google::protobuf::Message /* @@protoc_insertion_point(class_definition:google.protobuf.testing.MapOfStruct) */ {
 public:
  MapOfStruct();
  virtual ~MapOfStruct();

  MapOfStruct(const MapOfStruct& from);

  inline MapOfStruct& operator=(const MapOfStruct& from) {
    CopyFrom(from);
    return *this;
  }

  static const ::google::protobuf::Descriptor* descriptor();
  static const MapOfStruct& default_instance();

  static const MapOfStruct* internal_default_instance();

  void Swap(MapOfStruct* other);

  // implements Message ----------------------------------------------

  inline MapOfStruct* New() const { return New(NULL); }

  MapOfStruct* New(::google::protobuf::Arena* arena) const;
  void CopyFrom(const ::google::protobuf::Message& from);
  void MergeFrom(const ::google::protobuf::Message& from);
  void CopyFrom(const MapOfStruct& from);
  void MergeFrom(const MapOfStruct& from);
  void Clear();
  bool IsInitialized() const;

  size_t ByteSizeLong() const;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input);
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const;
  ::google::protobuf::uint8* InternalSerializeWithCachedSizesToArray(
      bool deterministic, ::google::protobuf::uint8* output) const;
  ::google::protobuf::uint8* SerializeWithCachedSizesToArray(::google::protobuf::uint8* output) const {
    return InternalSerializeWithCachedSizesToArray(false, output);
  }
  int GetCachedSize() const { return _cached_size_; }
  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const;
  void InternalSwap(MapOfStruct* other);
  void UnsafeMergeFrom(const MapOfStruct& from);
  private:
  inline ::google::protobuf::Arena* GetArenaNoVirtual() const {
    return _internal_metadata_.arena();
  }
  inline void* MaybeArenaPtr() const {
    return _internal_metadata_.raw_arena_ptr();
  }
  public:

  ::google::protobuf::Metadata GetMetadata() const;

  // nested types ----------------------------------------------------


  // accessors -------------------------------------------------------

  // map<string, .google.protobuf.Struct> struct_map = 1;
  int struct_map_size() const;
  void clear_struct_map();
  static const int kStructMapFieldNumber = 1;
  const ::google::protobuf::Map< ::std::string, ::google::protobuf::Struct >&
      struct_map() const;
  ::google::protobuf::Map< ::std::string, ::google::protobuf::Struct >*
      mutable_struct_map();

  // map<string, .google.protobuf.Value> value_map = 2;
  int value_map_size() const;
  void clear_value_map();
  static const int kValueMapFieldNumber = 2;
  const ::google::protobuf::Map< ::std::string, ::google::protobuf::Value >&
      value_map() const;
  ::google::protobuf::Map< ::std::string, ::google::protobuf::Value >*
      mutable_value_map();

  // map<string, .google.protobuf.ListValue> listvalue_map = 3;
  int listvalue_map_size() const;
  void clear_listvalue_map();
  static const int kListvalueMapFieldNumber = 3;
  const ::google::protobuf::Map< ::std::string, ::google::protobuf::ListValue >&
      listvalue_map() const;
  ::google::protobuf::Map< ::std::string, ::google::protobuf::ListValue >*
      mutable_listvalue_map();

  // @@protoc_insertion_point(class_scope:google.protobuf.testing.MapOfStruct)
 private:

  ::google::protobuf::internal::InternalMetadataWithArena _internal_metadata_;
  typedef ::google::protobuf::internal::MapEntryLite<
      ::std::string, ::google::protobuf::Struct,
      ::google::protobuf::internal::WireFormatLite::TYPE_STRING,
      ::google::protobuf::internal::WireFormatLite::TYPE_MESSAGE,
      0 >
      MapOfStruct_StructMapEntry;
  ::google::protobuf::internal::MapField<
      ::std::string, ::google::protobuf::Struct,
      ::google::protobuf::internal::WireFormatLite::TYPE_STRING,
      ::google::protobuf::internal::WireFormatLite::TYPE_MESSAGE,
      0 > struct_map_;
  typedef ::google::protobuf::internal::MapEntryLite<
      ::std::string, ::google::protobuf::Value,
      ::google::protobuf::internal::WireFormatLite::TYPE_STRING,
      ::google::protobuf::internal::WireFormatLite::TYPE_MESSAGE,
      0 >
      MapOfStruct_ValueMapEntry;
  ::google::protobuf::internal::MapField<
      ::std::string, ::google::protobuf::Value,
      ::google::protobuf::internal::WireFormatLite::TYPE_STRING,
      ::google::protobuf::internal::WireFormatLite::TYPE_MESSAGE,
      0 > value_map_;
  typedef ::google::protobuf::internal::MapEntryLite<
      ::std::string, ::google::protobuf::ListValue,
      ::google::protobuf::internal::WireFormatLite::TYPE_STRING,
      ::google::protobuf::internal::WireFormatLite::TYPE_MESSAGE,
      0 >
      MapOfStruct_ListvalueMapEntry;
  ::google::protobuf::internal::MapField<
      ::std::string, ::google::protobuf::ListValue,
      ::google::protobuf::internal::WireFormatLite::TYPE_STRING,
      ::google::protobuf::internal::WireFormatLite::TYPE_MESSAGE,
      0 > listvalue_map_;
  mutable int _cached_size_;
  friend void  protobuf_InitDefaults_google_2fprotobuf_2futil_2finternal_2ftestdata_2fstruct_2eproto_impl();
  friend void  protobuf_AddDesc_google_2fprotobuf_2futil_2finternal_2ftestdata_2fstruct_2eproto_impl();
  friend void protobuf_AssignDesc_google_2fprotobuf_2futil_2finternal_2ftestdata_2fstruct_2eproto();
  friend void protobuf_ShutdownFile_google_2fprotobuf_2futil_2finternal_2ftestdata_2fstruct_2eproto();

  void InitAsDefaultInstance();
};
extern ::google::protobuf::internal::ExplicitlyConstructed<MapOfStruct> MapOfStruct_default_instance_;

// -------------------------------------------------------------------

class Dummy : public ::google::protobuf::Message /* @@protoc_insertion_point(class_definition:google.protobuf.testing.Dummy) */ {
 public:
  Dummy();
  virtual ~Dummy();

  Dummy(const Dummy& from);

  inline Dummy& operator=(const Dummy& from) {
    CopyFrom(from);
    return *this;
  }

  static const ::google::protobuf::Descriptor* descriptor();
  static const Dummy& default_instance();

  static const Dummy* internal_default_instance();

  void Swap(Dummy* other);

  // implements Message ----------------------------------------------

  inline Dummy* New() const { return New(NULL); }

  Dummy* New(::google::protobuf::Arena* arena) const;
  void CopyFrom(const ::google::protobuf::Message& from);
  void MergeFrom(const ::google::protobuf::Message& from);
  void CopyFrom(const Dummy& from);
  void MergeFrom(const Dummy& from);
  void Clear();
  bool IsInitialized() const;

  size_t ByteSizeLong() const;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input);
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const;
  ::google::protobuf::uint8* InternalSerializeWithCachedSizesToArray(
      bool deterministic, ::google::protobuf::uint8* output) const;
  ::google::protobuf::uint8* SerializeWithCachedSizesToArray(::google::protobuf::uint8* output) const {
    return InternalSerializeWithCachedSizesToArray(false, output);
  }
  int GetCachedSize() const { return _cached_size_; }
  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const;
  void InternalSwap(Dummy* other);
  void UnsafeMergeFrom(const Dummy& from);
  private:
  inline ::google::protobuf::Arena* GetArenaNoVirtual() const {
    return _internal_metadata_.arena();
  }
  inline void* MaybeArenaPtr() const {
    return _internal_metadata_.raw_arena_ptr();
  }
  public:

  ::google::protobuf::Metadata GetMetadata() const;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // optional string text = 1;
  void clear_text();
  static const int kTextFieldNumber = 1;
  const ::std::string& text() const;
  void set_text(const ::std::string& value);
  void set_text(const char* value);
  void set_text(const char* value, size_t size);
  ::std::string* mutable_text();
  ::std::string* release_text();
  void set_allocated_text(::std::string* text);

  // @@protoc_insertion_point(class_scope:google.protobuf.testing.Dummy)
 private:

  ::google::protobuf::internal::InternalMetadataWithArena _internal_metadata_;
  ::google::protobuf::internal::ArenaStringPtr text_;
  mutable int _cached_size_;
  friend void  protobuf_InitDefaults_google_2fprotobuf_2futil_2finternal_2ftestdata_2fstruct_2eproto_impl();
  friend void  protobuf_AddDesc_google_2fprotobuf_2futil_2finternal_2ftestdata_2fstruct_2eproto_impl();
  friend void protobuf_AssignDesc_google_2fprotobuf_2futil_2finternal_2ftestdata_2fstruct_2eproto();
  friend void protobuf_ShutdownFile_google_2fprotobuf_2futil_2finternal_2ftestdata_2fstruct_2eproto();

  void InitAsDefaultInstance();
};
extern ::google::protobuf::internal::ExplicitlyConstructed<Dummy> Dummy_default_instance_;

// -------------------------------------------------------------------

class StructType : public ::google::protobuf::Message /* @@protoc_insertion_point(class_definition:google.protobuf.testing.StructType) */ {
 public:
  StructType();
  virtual ~StructType();

  StructType(const StructType& from);

  inline StructType& operator=(const StructType& from) {
    CopyFrom(from);
    return *this;
  }

  static const ::google::protobuf::Descriptor* descriptor();
  static const StructType& default_instance();

  static const StructType* internal_default_instance();

  void Swap(StructType* other);

  // implements Message ----------------------------------------------

  inline StructType* New() const { return New(NULL); }

  StructType* New(::google::protobuf::Arena* arena) const;
  void CopyFrom(const ::google::protobuf::Message& from);
  void MergeFrom(const ::google::protobuf::Message& from);
  void CopyFrom(const StructType& from);
  void MergeFrom(const StructType& from);
  void Clear();
  bool IsInitialized() const;

  size_t ByteSizeLong() const;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input);
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const;
  ::google::protobuf::uint8* InternalSerializeWithCachedSizesToArray(
      bool deterministic, ::google::protobuf::uint8* output) const;
  ::google::protobuf::uint8* SerializeWithCachedSizesToArray(::google::protobuf::uint8* output) const {
    return InternalSerializeWithCachedSizesToArray(false, output);
  }
  int GetCachedSize() const { return _cached_size_; }
  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const;
  void InternalSwap(StructType* other);
  void UnsafeMergeFrom(const StructType& from);
  private:
  inline ::google::protobuf::Arena* GetArenaNoVirtual() const {
    return _internal_metadata_.arena();
  }
  inline void* MaybeArenaPtr() const {
    return _internal_metadata_.raw_arena_ptr();
  }
  public:

  ::google::protobuf::Metadata GetMetadata() const;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // optional .google.protobuf.Struct object = 1;
  bool has_object() const;
  void clear_object();
  static const int kObjectFieldNumber = 1;
  const ::google::protobuf::Struct& object() const;
  ::google::protobuf::Struct* mutable_object();
  ::google::protobuf::Struct* release_object();
  void set_allocated_object(::google::protobuf::Struct* object);

  // @@protoc_insertion_point(class_scope:google.protobuf.testing.StructType)
 private:

  ::google::protobuf::internal::InternalMetadataWithArena _internal_metadata_;
  ::google::protobuf::Struct* object_;
  mutable int _cached_size_;
  friend void  protobuf_InitDefaults_google_2fprotobuf_2futil_2finternal_2ftestdata_2fstruct_2eproto_impl();
  friend void  protobuf_AddDesc_google_2fprotobuf_2futil_2finternal_2ftestdata_2fstruct_2eproto_impl();
  friend void protobuf_AssignDesc_google_2fprotobuf_2futil_2finternal_2ftestdata_2fstruct_2eproto();
  friend void protobuf_ShutdownFile_google_2fprotobuf_2futil_2finternal_2ftestdata_2fstruct_2eproto();

  void InitAsDefaultInstance();
};
extern ::google::protobuf::internal::ExplicitlyConstructed<StructType> StructType_default_instance_;

// ===================================================================


// ===================================================================

#if !PROTOBUF_INLINE_NOT_IN_HEADERS
// StructTestCases

// optional .google.protobuf.testing.StructWrapper empty_value = 1;
inline bool StructTestCases::has_empty_value() const {
  return this != internal_default_instance() && empty_value_ != NULL;
}
inline void StructTestCases::clear_empty_value() {
  if (GetArenaNoVirtual() == NULL && empty_value_ != NULL) delete empty_value_;
  empty_value_ = NULL;
}
inline const ::google::protobuf::testing::StructWrapper& StructTestCases::empty_value() const {
  // @@protoc_insertion_point(field_get:google.protobuf.testing.StructTestCases.empty_value)
  return empty_value_ != NULL ? *empty_value_
                         : *::google::protobuf::testing::StructWrapper::internal_default_instance();
}
inline ::google::protobuf::testing::StructWrapper* StructTestCases::mutable_empty_value() {
  
  if (empty_value_ == NULL) {
    empty_value_ = new ::google::protobuf::testing::StructWrapper;
  }
  // @@protoc_insertion_point(field_mutable:google.protobuf.testing.StructTestCases.empty_value)
  return empty_value_;
}
inline ::google::protobuf::testing::StructWrapper* StructTestCases::release_empty_value() {
  // @@protoc_insertion_point(field_release:google.protobuf.testing.StructTestCases.empty_value)
  
  ::google::protobuf::testing::StructWrapper* temp = empty_value_;
  empty_value_ = NULL;
  return temp;
}
inline void StructTestCases::set_allocated_empty_value(::google::protobuf::testing::StructWrapper* empty_value) {
  delete empty_value_;
  empty_value_ = empty_value;
  if (empty_value) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_set_allocated:google.protobuf.testing.StructTestCases.empty_value)
}

// optional .google.protobuf.testing.StructWrapper empty_value2 = 2;
inline bool StructTestCases::has_empty_value2() const {
  return this != internal_default_instance() && empty_value2_ != NULL;
}
inline void StructTestCases::clear_empty_value2() {
  if (GetArenaNoVirtual() == NULL && empty_value2_ != NULL) delete empty_value2_;
  empty_value2_ = NULL;
}
inline const ::google::protobuf::testing::StructWrapper& StructTestCases::empty_value2() const {
  // @@protoc_insertion_point(field_get:google.protobuf.testing.StructTestCases.empty_value2)
  return empty_value2_ != NULL ? *empty_value2_
                         : *::google::protobuf::testing::StructWrapper::internal_default_instance();
}
inline ::google::protobuf::testing::StructWrapper* StructTestCases::mutable_empty_value2() {
  
  if (empty_value2_ == NULL) {
    empty_value2_ = new ::google::protobuf::testing::StructWrapper;
  }
  // @@protoc_insertion_point(field_mutable:google.protobuf.testing.StructTestCases.empty_value2)
  return empty_value2_;
}
inline ::google::protobuf::testing::StructWrapper* StructTestCases::release_empty_value2() {
  // @@protoc_insertion_point(field_release:google.protobuf.testing.StructTestCases.empty_value2)
  
  ::google::protobuf::testing::StructWrapper* temp = empty_value2_;
  empty_value2_ = NULL;
  return temp;
}
inline void StructTestCases::set_allocated_empty_value2(::google::protobuf::testing::StructWrapper* empty_value2) {
  delete empty_value2_;
  empty_value2_ = empty_value2;
  if (empty_value2) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_set_allocated:google.protobuf.testing.StructTestCases.empty_value2)
}

// optional .google.protobuf.testing.StructWrapper null_value = 3;
inline bool StructTestCases::has_null_value() const {
  return this != internal_default_instance() && null_value_ != NULL;
}
inline void StructTestCases::clear_null_value() {
  if (GetArenaNoVirtual() == NULL && null_value_ != NULL) delete null_value_;
  null_value_ = NULL;
}
inline const ::google::protobuf::testing::StructWrapper& StructTestCases::null_value() const {
  // @@protoc_insertion_point(field_get:google.protobuf.testing.StructTestCases.null_value)
  return null_value_ != NULL ? *null_value_
                         : *::google::protobuf::testing::StructWrapper::internal_default_instance();
}
inline ::google::protobuf::testing::StructWrapper* StructTestCases::mutable_null_value() {
  
  if (null_value_ == NULL) {
    null_value_ = new ::google::protobuf::testing::StructWrapper;
  }
  // @@protoc_insertion_point(field_mutable:google.protobuf.testing.StructTestCases.null_value)
  return null_value_;
}
inline ::google::protobuf::testing::StructWrapper* StructTestCases::release_null_value() {
  // @@protoc_insertion_point(field_release:google.protobuf.testing.StructTestCases.null_value)
  
  ::google::protobuf::testing::StructWrapper* temp = null_value_;
  null_value_ = NULL;
  return temp;
}
inline void StructTestCases::set_allocated_null_value(::google::protobuf::testing::StructWrapper* null_value) {
  delete null_value_;
  null_value_ = null_value;
  if (null_value) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_set_allocated:google.protobuf.testing.StructTestCases.null_value)
}

// optional .google.protobuf.testing.StructWrapper simple_struct = 4;
inline bool StructTestCases::has_simple_struct() const {
  return this != internal_default_instance() && simple_struct_ != NULL;
}
inline void StructTestCases::clear_simple_struct() {
  if (GetArenaNoVirtual() == NULL && simple_struct_ != NULL) delete simple_struct_;
  simple_struct_ = NULL;
}
inline const ::google::protobuf::testing::StructWrapper& StructTestCases::simple_struct() const {
  // @@protoc_insertion_point(field_get:google.protobuf.testing.StructTestCases.simple_struct)
  return simple_struct_ != NULL ? *simple_struct_
                         : *::google::protobuf::testing::StructWrapper::internal_default_instance();
}
inline ::google::protobuf::testing::StructWrapper* StructTestCases::mutable_simple_struct() {
  
  if (simple_struct_ == NULL) {
    simple_struct_ = new ::google::protobuf::testing::StructWrapper;
  }
  // @@protoc_insertion_point(field_mutable:google.protobuf.testing.StructTestCases.simple_struct)
  return simple_struct_;
}
inline ::google::protobuf::testing::StructWrapper* StructTestCases::release_simple_struct() {
  // @@protoc_insertion_point(field_release:google.protobuf.testing.StructTestCases.simple_struct)
  
  ::google::protobuf::testing::StructWrapper* temp = simple_struct_;
  simple_struct_ = NULL;
  return temp;
}
inline void StructTestCases::set_allocated_simple_struct(::google::protobuf::testing::StructWrapper* simple_struct) {
  delete simple_struct_;
  simple_struct_ = simple_struct;
  if (simple_struct) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_set_allocated:google.protobuf.testing.StructTestCases.simple_struct)
}

// optional .google.protobuf.testing.StructWrapper longer_struct = 5;
inline bool StructTestCases::has_longer_struct() const {
  return this != internal_default_instance() && longer_struct_ != NULL;
}
inline void StructTestCases::clear_longer_struct() {
  if (GetArenaNoVirtual() == NULL && longer_struct_ != NULL) delete longer_struct_;
  longer_struct_ = NULL;
}
inline const ::google::protobuf::testing::StructWrapper& StructTestCases::longer_struct() const {
  // @@protoc_insertion_point(field_get:google.protobuf.testing.StructTestCases.longer_struct)
  return longer_struct_ != NULL ? *longer_struct_
                         : *::google::protobuf::testing::StructWrapper::internal_default_instance();
}
inline ::google::protobuf::testing::StructWrapper* StructTestCases::mutable_longer_struct() {
  
  if (longer_struct_ == NULL) {
    longer_struct_ = new ::google::protobuf::testing::StructWrapper;
  }
  // @@protoc_insertion_point(field_mutable:google.protobuf.testing.StructTestCases.longer_struct)
  return longer_struct_;
}
inline ::google::protobuf::testing::StructWrapper* StructTestCases::release_longer_struct() {
  // @@protoc_insertion_point(field_release:google.protobuf.testing.StructTestCases.longer_struct)
  
  ::google::protobuf::testing::StructWrapper* temp = longer_struct_;
  longer_struct_ = NULL;
  return temp;
}
inline void StructTestCases::set_allocated_longer_struct(::google::protobuf::testing::StructWrapper* longer_struct) {
  delete longer_struct_;
  longer_struct_ = longer_struct;
  if (longer_struct) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_set_allocated:google.protobuf.testing.StructTestCases.longer_struct)
}

// optional .google.protobuf.testing.StructWrapper struct_with_nested_struct = 6;
inline bool StructTestCases::has_struct_with_nested_struct() const {
  return this != internal_default_instance() && struct_with_nested_struct_ != NULL;
}
inline void StructTestCases::clear_struct_with_nested_struct() {
  if (GetArenaNoVirtual() == NULL && struct_with_nested_struct_ != NULL) delete struct_with_nested_struct_;
  struct_with_nested_struct_ = NULL;
}
inline const ::google::protobuf::testing::StructWrapper& StructTestCases::struct_with_nested_struct() const {
  // @@protoc_insertion_point(field_get:google.protobuf.testing.StructTestCases.struct_with_nested_struct)
  return struct_with_nested_struct_ != NULL ? *struct_with_nested_struct_
                         : *::google::protobuf::testing::StructWrapper::internal_default_instance();
}
inline ::google::protobuf::testing::StructWrapper* StructTestCases::mutable_struct_with_nested_struct() {
  
  if (struct_with_nested_struct_ == NULL) {
    struct_with_nested_struct_ = new ::google::protobuf::testing::StructWrapper;
  }
  // @@protoc_insertion_point(field_mutable:google.protobuf.testing.StructTestCases.struct_with_nested_struct)
  return struct_with_nested_struct_;
}
inline ::google::protobuf::testing::StructWrapper* StructTestCases::release_struct_with_nested_struct() {
  // @@protoc_insertion_point(field_release:google.protobuf.testing.StructTestCases.struct_with_nested_struct)
  
  ::google::protobuf::testing::StructWrapper* temp = struct_with_nested_struct_;
  struct_with_nested_struct_ = NULL;
  return temp;
}
inline void StructTestCases::set_allocated_struct_with_nested_struct(::google::protobuf::testing::StructWrapper* struct_with_nested_struct) {
  delete struct_with_nested_struct_;
  struct_with_nested_struct_ = struct_with_nested_struct;
  if (struct_with_nested_struct) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_set_allocated:google.protobuf.testing.StructTestCases.struct_with_nested_struct)
}

// optional .google.protobuf.testing.StructWrapper struct_with_nested_list = 7;
inline bool StructTestCases::has_struct_with_nested_list() const {
  return this != internal_default_instance() && struct_with_nested_list_ != NULL;
}
inline void StructTestCases::clear_struct_with_nested_list() {
  if (GetArenaNoVirtual() == NULL && struct_with_nested_list_ != NULL) delete struct_with_nested_list_;
  struct_with_nested_list_ = NULL;
}
inline const ::google::protobuf::testing::StructWrapper& StructTestCases::struct_with_nested_list() const {
  // @@protoc_insertion_point(field_get:google.protobuf.testing.StructTestCases.struct_with_nested_list)
  return struct_with_nested_list_ != NULL ? *struct_with_nested_list_
                         : *::google::protobuf::testing::StructWrapper::internal_default_instance();
}
inline ::google::protobuf::testing::StructWrapper* StructTestCases::mutable_struct_with_nested_list() {
  
  if (struct_with_nested_list_ == NULL) {
    struct_with_nested_list_ = new ::google::protobuf::testing::StructWrapper;
  }
  // @@protoc_insertion_point(field_mutable:google.protobuf.testing.StructTestCases.struct_with_nested_list)
  return struct_with_nested_list_;
}
inline ::google::protobuf::testing::StructWrapper* StructTestCases::release_struct_with_nested_list() {
  // @@protoc_insertion_point(field_release:google.protobuf.testing.StructTestCases.struct_with_nested_list)
  
  ::google::protobuf::testing::StructWrapper* temp = struct_with_nested_list_;
  struct_with_nested_list_ = NULL;
  return temp;
}
inline void StructTestCases::set_allocated_struct_with_nested_list(::google::protobuf::testing::StructWrapper* struct_with_nested_list) {
  delete struct_with_nested_list_;
  struct_with_nested_list_ = struct_with_nested_list;
  if (struct_with_nested_list) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_set_allocated:google.protobuf.testing.StructTestCases.struct_with_nested_list)
}

// optional .google.protobuf.testing.StructWrapper struct_with_list_of_nulls = 8;
inline bool StructTestCases::has_struct_with_list_of_nulls() const {
  return this != internal_default_instance() && struct_with_list_of_nulls_ != NULL;
}
inline void StructTestCases::clear_struct_with_list_of_nulls() {
  if (GetArenaNoVirtual() == NULL && struct_with_list_of_nulls_ != NULL) delete struct_with_list_of_nulls_;
  struct_with_list_of_nulls_ = NULL;
}
inline const ::google::protobuf::testing::StructWrapper& StructTestCases::struct_with_list_of_nulls() const {
  // @@protoc_insertion_point(field_get:google.protobuf.testing.StructTestCases.struct_with_list_of_nulls)
  return struct_with_list_of_nulls_ != NULL ? *struct_with_list_of_nulls_
                         : *::google::protobuf::testing::StructWrapper::internal_default_instance();
}
inline ::google::protobuf::testing::StructWrapper* StructTestCases::mutable_struct_with_list_of_nulls() {
  
  if (struct_with_list_of_nulls_ == NULL) {
    struct_with_list_of_nulls_ = new ::google::protobuf::testing::StructWrapper;
  }
  // @@protoc_insertion_point(field_mutable:google.protobuf.testing.StructTestCases.struct_with_list_of_nulls)
  return struct_with_list_of_nulls_;
}
inline ::google::protobuf::testing::StructWrapper* StructTestCases::release_struct_with_list_of_nulls() {
  // @@protoc_insertion_point(field_release:google.protobuf.testing.StructTestCases.struct_with_list_of_nulls)
  
  ::google::protobuf::testing::StructWrapper* temp = struct_with_list_of_nulls_;
  struct_with_list_of_nulls_ = NULL;
  return temp;
}
inline void StructTestCases::set_allocated_struct_with_list_of_nulls(::google::protobuf::testing::StructWrapper* struct_with_list_of_nulls) {
  delete struct_with_list_of_nulls_;
  struct_with_list_of_nulls_ = struct_with_list_of_nulls;
  if (struct_with_list_of_nulls) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_set_allocated:google.protobuf.testing.StructTestCases.struct_with_list_of_nulls)
}

// optional .google.protobuf.testing.StructWrapper struct_with_list_of_lists = 9;
inline bool StructTestCases::has_struct_with_list_of_lists() const {
  return this != internal_default_instance() && struct_with_list_of_lists_ != NULL;
}
inline void StructTestCases::clear_struct_with_list_of_lists() {
  if (GetArenaNoVirtual() == NULL && struct_with_list_of_lists_ != NULL) delete struct_with_list_of_lists_;
  struct_with_list_of_lists_ = NULL;
}
inline const ::google::protobuf::testing::StructWrapper& StructTestCases::struct_with_list_of_lists() const {
  // @@protoc_insertion_point(field_get:google.protobuf.testing.StructTestCases.struct_with_list_of_lists)
  return struct_with_list_of_lists_ != NULL ? *struct_with_list_of_lists_
                         : *::google::protobuf::testing::StructWrapper::internal_default_instance();
}
inline ::google::protobuf::testing::StructWrapper* StructTestCases::mutable_struct_with_list_of_lists() {
  
  if (struct_with_list_of_lists_ == NULL) {
    struct_with_list_of_lists_ = new ::google::protobuf::testing::StructWrapper;
  }
  // @@protoc_insertion_point(field_mutable:google.protobuf.testing.StructTestCases.struct_with_list_of_lists)
  return struct_with_list_of_lists_;
}
inline ::google::protobuf::testing::StructWrapper* StructTestCases::release_struct_with_list_of_lists() {
  // @@protoc_insertion_point(field_release:google.protobuf.testing.StructTestCases.struct_with_list_of_lists)
  
  ::google::protobuf::testing::StructWrapper* temp = struct_with_list_of_lists_;
  struct_with_list_of_lists_ = NULL;
  return temp;
}
inline void StructTestCases::set_allocated_struct_with_list_of_lists(::google::protobuf::testing::StructWrapper* struct_with_list_of_lists) {
  delete struct_with_list_of_lists_;
  struct_with_list_of_lists_ = struct_with_list_of_lists;
  if (struct_with_list_of_lists) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_set_allocated:google.protobuf.testing.StructTestCases.struct_with_list_of_lists)
}

// optional .google.protobuf.testing.StructWrapper struct_with_list_of_structs = 10;
inline bool StructTestCases::has_struct_with_list_of_structs() const {
  return this != internal_default_instance() && struct_with_list_of_structs_ != NULL;
}
inline void StructTestCases::clear_struct_with_list_of_structs() {
  if (GetArenaNoVirtual() == NULL && struct_with_list_of_structs_ != NULL) delete struct_with_list_of_structs_;
  struct_with_list_of_structs_ = NULL;
}
inline const ::google::protobuf::testing::StructWrapper& StructTestCases::struct_with_list_of_structs() const {
  // @@protoc_insertion_point(field_get:google.protobuf.testing.StructTestCases.struct_with_list_of_structs)
  return struct_with_list_of_structs_ != NULL ? *struct_with_list_of_structs_
                         : *::google::protobuf::testing::StructWrapper::internal_default_instance();
}
inline ::google::protobuf::testing::StructWrapper* StructTestCases::mutable_struct_with_list_of_structs() {
  
  if (struct_with_list_of_structs_ == NULL) {
    struct_with_list_of_structs_ = new ::google::protobuf::testing::StructWrapper;
  }
  // @@protoc_insertion_point(field_mutable:google.protobuf.testing.StructTestCases.struct_with_list_of_structs)
  return struct_with_list_of_structs_;
}
inline ::google::protobuf::testing::StructWrapper* StructTestCases::release_struct_with_list_of_structs() {
  // @@protoc_insertion_point(field_release:google.protobuf.testing.StructTestCases.struct_with_list_of_structs)
  
  ::google::protobuf::testing::StructWrapper* temp = struct_with_list_of_structs_;
  struct_with_list_of_structs_ = NULL;
  return temp;
}
inline void StructTestCases::set_allocated_struct_with_list_of_structs(::google::protobuf::testing::StructWrapper* struct_with_list_of_structs) {
  delete struct_with_list_of_structs_;
  struct_with_list_of_structs_ = struct_with_list_of_structs;
  if (struct_with_list_of_structs) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_set_allocated:google.protobuf.testing.StructTestCases.struct_with_list_of_structs)
}

// optional .google.protobuf.testing.StructWrapper struct_with_empty_list = 11;
inline bool StructTestCases::has_struct_with_empty_list() const {
  return this != internal_default_instance() && struct_with_empty_list_ != NULL;
}
inline void StructTestCases::clear_struct_with_empty_list() {
  if (GetArenaNoVirtual() == NULL && struct_with_empty_list_ != NULL) delete struct_with_empty_list_;
  struct_with_empty_list_ = NULL;
}
inline const ::google::protobuf::testing::StructWrapper& StructTestCases::struct_with_empty_list() const {
  // @@protoc_insertion_point(field_get:google.protobuf.testing.StructTestCases.struct_with_empty_list)
  return struct_with_empty_list_ != NULL ? *struct_with_empty_list_
                         : *::google::protobuf::testing::StructWrapper::internal_default_instance();
}
inline ::google::protobuf::testing::StructWrapper* StructTestCases::mutable_struct_with_empty_list() {
  
  if (struct_with_empty_list_ == NULL) {
    struct_with_empty_list_ = new ::google::protobuf::testing::StructWrapper;
  }
  // @@protoc_insertion_point(field_mutable:google.protobuf.testing.StructTestCases.struct_with_empty_list)
  return struct_with_empty_list_;
}
inline ::google::protobuf::testing::StructWrapper* StructTestCases::release_struct_with_empty_list() {
  // @@protoc_insertion_point(field_release:google.protobuf.testing.StructTestCases.struct_with_empty_list)
  
  ::google::protobuf::testing::StructWrapper* temp = struct_with_empty_list_;
  struct_with_empty_list_ = NULL;
  return temp;
}
inline void StructTestCases::set_allocated_struct_with_empty_list(::google::protobuf::testing::StructWrapper* struct_with_empty_list) {
  delete struct_with_empty_list_;
  struct_with_empty_list_ = struct_with_empty_list;
  if (struct_with_empty_list) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_set_allocated:google.protobuf.testing.StructTestCases.struct_with_empty_list)
}

// optional .google.protobuf.testing.StructWrapper struct_with_list_with_empty_struct = 12;
inline bool StructTestCases::has_struct_with_list_with_empty_struct() const {
  return this != internal_default_instance() && struct_with_list_with_empty_struct_ != NULL;
}
inline void StructTestCases::clear_struct_with_list_with_empty_struct() {
  if (GetArenaNoVirtual() == NULL && struct_with_list_with_empty_struct_ != NULL) delete struct_with_list_with_empty_struct_;
  struct_with_list_with_empty_struct_ = NULL;
}
inline const ::google::protobuf::testing::StructWrapper& StructTestCases::struct_with_list_with_empty_struct() const {
  // @@protoc_insertion_point(field_get:google.protobuf.testing.StructTestCases.struct_with_list_with_empty_struct)
  return struct_with_list_with_empty_struct_ != NULL ? *struct_with_list_with_empty_struct_
                         : *::google::protobuf::testing::StructWrapper::internal_default_instance();
}
inline ::google::protobuf::testing::StructWrapper* StructTestCases::mutable_struct_with_list_with_empty_struct() {
  
  if (struct_with_list_with_empty_struct_ == NULL) {
    struct_with_list_with_empty_struct_ = new ::google::protobuf::testing::StructWrapper;
  }
  // @@protoc_insertion_point(field_mutable:google.protobuf.testing.StructTestCases.struct_with_list_with_empty_struct)
  return struct_with_list_with_empty_struct_;
}
inline ::google::protobuf::testing::StructWrapper* StructTestCases::release_struct_with_list_with_empty_struct() {
  // @@protoc_insertion_point(field_release:google.protobuf.testing.StructTestCases.struct_with_list_with_empty_struct)
  
  ::google::protobuf::testing::StructWrapper* temp = struct_with_list_with_empty_struct_;
  struct_with_list_with_empty_struct_ = NULL;
  return temp;
}
inline void StructTestCases::set_allocated_struct_with_list_with_empty_struct(::google::protobuf::testing::StructWrapper* struct_with_list_with_empty_struct) {
  delete struct_with_list_with_empty_struct_;
  struct_with_list_with_empty_struct_ = struct_with_list_with_empty_struct;
  if (struct_with_list_with_empty_struct) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_set_allocated:google.protobuf.testing.StructTestCases.struct_with_list_with_empty_struct)
}

// optional .google.protobuf.Struct top_level_struct = 13;
inline bool StructTestCases::has_top_level_struct() const {
  return this != internal_default_instance() && top_level_struct_ != NULL;
}
inline void StructTestCases::clear_top_level_struct() {
  if (GetArenaNoVirtual() == NULL && top_level_struct_ != NULL) delete top_level_struct_;
  top_level_struct_ = NULL;
}
inline const ::google::protobuf::Struct& StructTestCases::top_level_struct() const {
  // @@protoc_insertion_point(field_get:google.protobuf.testing.StructTestCases.top_level_struct)
  return top_level_struct_ != NULL ? *top_level_struct_
                         : *::google::protobuf::Struct::internal_default_instance();
}
inline ::google::protobuf::Struct* StructTestCases::mutable_top_level_struct() {
  
  if (top_level_struct_ == NULL) {
    top_level_struct_ = new ::google::protobuf::Struct;
  }
  // @@protoc_insertion_point(field_mutable:google.protobuf.testing.StructTestCases.top_level_struct)
  return top_level_struct_;
}
inline ::google::protobuf::Struct* StructTestCases::release_top_level_struct() {
  // @@protoc_insertion_point(field_release:google.protobuf.testing.StructTestCases.top_level_struct)
  
  ::google::protobuf::Struct* temp = top_level_struct_;
  top_level_struct_ = NULL;
  return temp;
}
inline void StructTestCases::set_allocated_top_level_struct(::google::protobuf::Struct* top_level_struct) {
  delete top_level_struct_;
  if (top_level_struct != NULL && top_level_struct->GetArena() != NULL) {
    ::google::protobuf::Struct* new_top_level_struct = new ::google::protobuf::Struct;
    new_top_level_struct->CopyFrom(*top_level_struct);
    top_level_struct = new_top_level_struct;
  }
  top_level_struct_ = top_level_struct;
  if (top_level_struct) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_set_allocated:google.protobuf.testing.StructTestCases.top_level_struct)
}

// optional .google.protobuf.Struct top_level_struct_with_empty_list = 14;
inline bool StructTestCases::has_top_level_struct_with_empty_list() const {
  return this != internal_default_instance() && top_level_struct_with_empty_list_ != NULL;
}
inline void StructTestCases::clear_top_level_struct_with_empty_list() {
  if (GetArenaNoVirtual() == NULL && top_level_struct_with_empty_list_ != NULL) delete top_level_struct_with_empty_list_;
  top_level_struct_with_empty_list_ = NULL;
}
inline const ::google::protobuf::Struct& StructTestCases::top_level_struct_with_empty_list() const {
  // @@protoc_insertion_point(field_get:google.protobuf.testing.StructTestCases.top_level_struct_with_empty_list)
  return top_level_struct_with_empty_list_ != NULL ? *top_level_struct_with_empty_list_
                         : *::google::protobuf::Struct::internal_default_instance();
}
inline ::google::protobuf::Struct* StructTestCases::mutable_top_level_struct_with_empty_list() {
  
  if (top_level_struct_with_empty_list_ == NULL) {
    top_level_struct_with_empty_list_ = new ::google::protobuf::Struct;
  }
  // @@protoc_insertion_point(field_mutable:google.protobuf.testing.StructTestCases.top_level_struct_with_empty_list)
  return top_level_struct_with_empty_list_;
}
inline ::google::protobuf::Struct* StructTestCases::release_top_level_struct_with_empty_list() {
  // @@protoc_insertion_point(field_release:google.protobuf.testing.StructTestCases.top_level_struct_with_empty_list)
  
  ::google::protobuf::Struct* temp = top_level_struct_with_empty_list_;
  top_level_struct_with_empty_list_ = NULL;
  return temp;
}
inline void StructTestCases::set_allocated_top_level_struct_with_empty_list(::google::protobuf::Struct* top_level_struct_with_empty_list) {
  delete top_level_struct_with_empty_list_;
  if (top_level_struct_with_empty_list != NULL && top_level_struct_with_empty_list->GetArena() != NULL) {
    ::google::protobuf::Struct* new_top_level_struct_with_empty_list = new ::google::protobuf::Struct;
    new_top_level_struct_with_empty_list->CopyFrom(*top_level_struct_with_empty_list);
    top_level_struct_with_empty_list = new_top_level_struct_with_empty_list;
  }
  top_level_struct_with_empty_list_ = top_level_struct_with_empty_list;
  if (top_level_struct_with_empty_list) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_set_allocated:google.protobuf.testing.StructTestCases.top_level_struct_with_empty_list)
}

// optional .google.protobuf.Struct top_level_struct_with_list_with_empty_struct = 15;
inline bool StructTestCases::has_top_level_struct_with_list_with_empty_struct() const {
  return this != internal_default_instance() && top_level_struct_with_list_with_empty_struct_ != NULL;
}
inline void StructTestCases::clear_top_level_struct_with_list_with_empty_struct() {
  if (GetArenaNoVirtual() == NULL && top_level_struct_with_list_with_empty_struct_ != NULL) delete top_level_struct_with_list_with_empty_struct_;
  top_level_struct_with_list_with_empty_struct_ = NULL;
}
inline const ::google::protobuf::Struct& StructTestCases::top_level_struct_with_list_with_empty_struct() const {
  // @@protoc_insertion_point(field_get:google.protobuf.testing.StructTestCases.top_level_struct_with_list_with_empty_struct)
  return top_level_struct_with_list_with_empty_struct_ != NULL ? *top_level_struct_with_list_with_empty_struct_
                         : *::google::protobuf::Struct::internal_default_instance();
}
inline ::google::protobuf::Struct* StructTestCases::mutable_top_level_struct_with_list_with_empty_struct() {
  
  if (top_level_struct_with_list_with_empty_struct_ == NULL) {
    top_level_struct_with_list_with_empty_struct_ = new ::google::protobuf::Struct;
  }
  // @@protoc_insertion_point(field_mutable:google.protobuf.testing.StructTestCases.top_level_struct_with_list_with_empty_struct)
  return top_level_struct_with_list_with_empty_struct_;
}
inline ::google::protobuf::Struct* StructTestCases::release_top_level_struct_with_list_with_empty_struct() {
  // @@protoc_insertion_point(field_release:google.protobuf.testing.StructTestCases.top_level_struct_with_list_with_empty_struct)
  
  ::google::protobuf::Struct* temp = top_level_struct_with_list_with_empty_struct_;
  top_level_struct_with_list_with_empty_struct_ = NULL;
  return temp;
}
inline void StructTestCases::set_allocated_top_level_struct_with_list_with_empty_struct(::google::protobuf::Struct* top_level_struct_with_list_with_empty_struct) {
  delete top_level_struct_with_list_with_empty_struct_;
  if (top_level_struct_with_list_with_empty_struct != NULL && top_level_struct_with_list_with_empty_struct->GetArena() != NULL) {
    ::google::protobuf::Struct* new_top_level_struct_with_list_with_empty_struct = new ::google::protobuf::Struct;
    new_top_level_struct_with_list_with_empty_struct->CopyFrom(*top_level_struct_with_list_with_empty_struct);
    top_level_struct_with_list_with_empty_struct = new_top_level_struct_with_list_with_empty_struct;
  }
  top_level_struct_with_list_with_empty_struct_ = top_level_struct_with_list_with_empty_struct;
  if (top_level_struct_with_list_with_empty_struct) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_set_allocated:google.protobuf.testing.StructTestCases.top_level_struct_with_list_with_empty_struct)
}

// optional .google.protobuf.testing.ValueWrapper value_wrapper_simple = 16;
inline bool StructTestCases::has_value_wrapper_simple() const {
  return this != internal_default_instance() && value_wrapper_simple_ != NULL;
}
inline void StructTestCases::clear_value_wrapper_simple() {
  if (GetArenaNoVirtual() == NULL && value_wrapper_simple_ != NULL) delete value_wrapper_simple_;
  value_wrapper_simple_ = NULL;
}
inline const ::google::protobuf::testing::ValueWrapper& StructTestCases::value_wrapper_simple() const {
  // @@protoc_insertion_point(field_get:google.protobuf.testing.StructTestCases.value_wrapper_simple)
  return value_wrapper_simple_ != NULL ? *value_wrapper_simple_
                         : *::google::protobuf::testing::ValueWrapper::internal_default_instance();
}
inline ::google::protobuf::testing::ValueWrapper* StructTestCases::mutable_value_wrapper_simple() {
  
  if (value_wrapper_simple_ == NULL) {
    value_wrapper_simple_ = new ::google::protobuf::testing::ValueWrapper;
  }
  // @@protoc_insertion_point(field_mutable:google.protobuf.testing.StructTestCases.value_wrapper_simple)
  return value_wrapper_simple_;
}
inline ::google::protobuf::testing::ValueWrapper* StructTestCases::release_value_wrapper_simple() {
  // @@protoc_insertion_point(field_release:google.protobuf.testing.StructTestCases.value_wrapper_simple)
  
  ::google::protobuf::testing::ValueWrapper* temp = value_wrapper_simple_;
  value_wrapper_simple_ = NULL;
  return temp;
}
inline void StructTestCases::set_allocated_value_wrapper_simple(::google::protobuf::testing::ValueWrapper* value_wrapper_simple) {
  delete value_wrapper_simple_;
  value_wrapper_simple_ = value_wrapper_simple;
  if (value_wrapper_simple) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_set_allocated:google.protobuf.testing.StructTestCases.value_wrapper_simple)
}

// optional .google.protobuf.testing.ValueWrapper value_wrapper_with_struct = 17;
inline bool StructTestCases::has_value_wrapper_with_struct() const {
  return this != internal_default_instance() && value_wrapper_with_struct_ != NULL;
}
inline void StructTestCases::clear_value_wrapper_with_struct() {
  if (GetArenaNoVirtual() == NULL && value_wrapper_with_struct_ != NULL) delete value_wrapper_with_struct_;
  value_wrapper_with_struct_ = NULL;
}
inline const ::google::protobuf::testing::ValueWrapper& StructTestCases::value_wrapper_with_struct() const {
  // @@protoc_insertion_point(field_get:google.protobuf.testing.StructTestCases.value_wrapper_with_struct)
  return value_wrapper_with_struct_ != NULL ? *value_wrapper_with_struct_
                         : *::google::protobuf::testing::ValueWrapper::internal_default_instance();
}
inline ::google::protobuf::testing::ValueWrapper* StructTestCases::mutable_value_wrapper_with_struct() {
  
  if (value_wrapper_with_struct_ == NULL) {
    value_wrapper_with_struct_ = new ::google::protobuf::testing::ValueWrapper;
  }
  // @@protoc_insertion_point(field_mutable:google.protobuf.testing.StructTestCases.value_wrapper_with_struct)
  return value_wrapper_with_struct_;
}
inline ::google::protobuf::testing::ValueWrapper* StructTestCases::release_value_wrapper_with_struct() {
  // @@protoc_insertion_point(field_release:google.protobuf.testing.StructTestCases.value_wrapper_with_struct)
  
  ::google::protobuf::testing::ValueWrapper* temp = value_wrapper_with_struct_;
  value_wrapper_with_struct_ = NULL;
  return temp;
}
inline void StructTestCases::set_allocated_value_wrapper_with_struct(::google::protobuf::testing::ValueWrapper* value_wrapper_with_struct) {
  delete value_wrapper_with_struct_;
  value_wrapper_with_struct_ = value_wrapper_with_struct;
  if (value_wrapper_with_struct) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_set_allocated:google.protobuf.testing.StructTestCases.value_wrapper_with_struct)
}

// optional .google.protobuf.testing.ValueWrapper value_wrapper_with_list = 18;
inline bool StructTestCases::has_value_wrapper_with_list() const {
  return this != internal_default_instance() && value_wrapper_with_list_ != NULL;
}
inline void StructTestCases::clear_value_wrapper_with_list() {
  if (GetArenaNoVirtual() == NULL && value_wrapper_with_list_ != NULL) delete value_wrapper_with_list_;
  value_wrapper_with_list_ = NULL;
}
inline const ::google::protobuf::testing::ValueWrapper& StructTestCases::value_wrapper_with_list() const {
  // @@protoc_insertion_point(field_get:google.protobuf.testing.StructTestCases.value_wrapper_with_list)
  return value_wrapper_with_list_ != NULL ? *value_wrapper_with_list_
                         : *::google::protobuf::testing::ValueWrapper::internal_default_instance();
}
inline ::google::protobuf::testing::ValueWrapper* StructTestCases::mutable_value_wrapper_with_list() {
  
  if (value_wrapper_with_list_ == NULL) {
    value_wrapper_with_list_ = new ::google::protobuf::testing::ValueWrapper;
  }
  // @@protoc_insertion_point(field_mutable:google.protobuf.testing.StructTestCases.value_wrapper_with_list)
  return value_wrapper_with_list_;
}
inline ::google::protobuf::testing::ValueWrapper* StructTestCases::release_value_wrapper_with_list() {
  // @@protoc_insertion_point(field_release:google.protobuf.testing.StructTestCases.value_wrapper_with_list)
  
  ::google::protobuf::testing::ValueWrapper* temp = value_wrapper_with_list_;
  value_wrapper_with_list_ = NULL;
  return temp;
}
inline void StructTestCases::set_allocated_value_wrapper_with_list(::google::protobuf::testing::ValueWrapper* value_wrapper_with_list) {
  delete value_wrapper_with_list_;
  value_wrapper_with_list_ = value_wrapper_with_list;
  if (value_wrapper_with_list) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_set_allocated:google.protobuf.testing.StructTestCases.value_wrapper_with_list)
}

// optional .google.protobuf.testing.ValueWrapper value_wrapper_with_empty_list = 19;
inline bool StructTestCases::has_value_wrapper_with_empty_list() const {
  return this != internal_default_instance() && value_wrapper_with_empty_list_ != NULL;
}
inline void StructTestCases::clear_value_wrapper_with_empty_list() {
  if (GetArenaNoVirtual() == NULL && value_wrapper_with_empty_list_ != NULL) delete value_wrapper_with_empty_list_;
  value_wrapper_with_empty_list_ = NULL;
}
inline const ::google::protobuf::testing::ValueWrapper& StructTestCases::value_wrapper_with_empty_list() const {
  // @@protoc_insertion_point(field_get:google.protobuf.testing.StructTestCases.value_wrapper_with_empty_list)
  return value_wrapper_with_empty_list_ != NULL ? *value_wrapper_with_empty_list_
                         : *::google::protobuf::testing::ValueWrapper::internal_default_instance();
}
inline ::google::protobuf::testing::ValueWrapper* StructTestCases::mutable_value_wrapper_with_empty_list() {
  
  if (value_wrapper_with_empty_list_ == NULL) {
    value_wrapper_with_empty_list_ = new ::google::protobuf::testing::ValueWrapper;
  }
  // @@protoc_insertion_point(field_mutable:google.protobuf.testing.StructTestCases.value_wrapper_with_empty_list)
  return value_wrapper_with_empty_list_;
}
inline ::google::protobuf::testing::ValueWrapper* StructTestCases::release_value_wrapper_with_empty_list() {
  // @@protoc_insertion_point(field_release:google.protobuf.testing.StructTestCases.value_wrapper_with_empty_list)
  
  ::google::protobuf::testing::ValueWrapper* temp = value_wrapper_with_empty_list_;
  value_wrapper_with_empty_list_ = NULL;
  return temp;
}
inline void StructTestCases::set_allocated_value_wrapper_with_empty_list(::google::protobuf::testing::ValueWrapper* value_wrapper_with_empty_list) {
  delete value_wrapper_with_empty_list_;
  value_wrapper_with_empty_list_ = value_wrapper_with_empty_list;
  if (value_wrapper_with_empty_list) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_set_allocated:google.protobuf.testing.StructTestCases.value_wrapper_with_empty_list)
}

// optional .google.protobuf.testing.ValueWrapper value_wrapper_with_list_with_empty_struct = 20;
inline bool StructTestCases::has_value_wrapper_with_list_with_empty_struct() const {
  return this != internal_default_instance() && value_wrapper_with_list_with_empty_struct_ != NULL;
}
inline void StructTestCases::clear_value_wrapper_with_list_with_empty_struct() {
  if (GetArenaNoVirtual() == NULL && value_wrapper_with_list_with_empty_struct_ != NULL) delete value_wrapper_with_list_with_empty_struct_;
  value_wrapper_with_list_with_empty_struct_ = NULL;
}
inline const ::google::protobuf::testing::ValueWrapper& StructTestCases::value_wrapper_with_list_with_empty_struct() const {
  // @@protoc_insertion_point(field_get:google.protobuf.testing.StructTestCases.value_wrapper_with_list_with_empty_struct)
  return value_wrapper_with_list_with_empty_struct_ != NULL ? *value_wrapper_with_list_with_empty_struct_
                         : *::google::protobuf::testing::ValueWrapper::internal_default_instance();
}
inline ::google::protobuf::testing::ValueWrapper* StructTestCases::mutable_value_wrapper_with_list_with_empty_struct() {
  
  if (value_wrapper_with_list_with_empty_struct_ == NULL) {
    value_wrapper_with_list_with_empty_struct_ = new ::google::protobuf::testing::ValueWrapper;
  }
  // @@protoc_insertion_point(field_mutable:google.protobuf.testing.StructTestCases.value_wrapper_with_list_with_empty_struct)
  return value_wrapper_with_list_with_empty_struct_;
}
inline ::google::protobuf::testing::ValueWrapper* StructTestCases::release_value_wrapper_with_list_with_empty_struct() {
  // @@protoc_insertion_point(field_release:google.protobuf.testing.StructTestCases.value_wrapper_with_list_with_empty_struct)
  
  ::google::protobuf::testing::ValueWrapper* temp = value_wrapper_with_list_with_empty_struct_;
  value_wrapper_with_list_with_empty_struct_ = NULL;
  return temp;
}
inline void StructTestCases::set_allocated_value_wrapper_with_list_with_empty_struct(::google::protobuf::testing::ValueWrapper* value_wrapper_with_list_with_empty_struct) {
  delete value_wrapper_with_list_with_empty_struct_;
  value_wrapper_with_list_with_empty_struct_ = value_wrapper_with_list_with_empty_struct;
  if (value_wrapper_with_list_with_empty_struct) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_set_allocated:google.protobuf.testing.StructTestCases.value_wrapper_with_list_with_empty_struct)
}

// optional .google.protobuf.testing.ListValueWrapper list_value_wrapper = 21;
inline bool StructTestCases::has_list_value_wrapper() const {
  return this != internal_default_instance() && list_value_wrapper_ != NULL;
}
inline void StructTestCases::clear_list_value_wrapper() {
  if (GetArenaNoVirtual() == NULL && list_value_wrapper_ != NULL) delete list_value_wrapper_;
  list_value_wrapper_ = NULL;
}
inline const ::google::protobuf::testing::ListValueWrapper& StructTestCases::list_value_wrapper() const {
  // @@protoc_insertion_point(field_get:google.protobuf.testing.StructTestCases.list_value_wrapper)
  return list_value_wrapper_ != NULL ? *list_value_wrapper_
                         : *::google::protobuf::testing::ListValueWrapper::internal_default_instance();
}
inline ::google::protobuf::testing::ListValueWrapper* StructTestCases::mutable_list_value_wrapper() {
  
  if (list_value_wrapper_ == NULL) {
    list_value_wrapper_ = new ::google::protobuf::testing::ListValueWrapper;
  }
  // @@protoc_insertion_point(field_mutable:google.protobuf.testing.StructTestCases.list_value_wrapper)
  return list_value_wrapper_;
}
inline ::google::protobuf::testing::ListValueWrapper* StructTestCases::release_list_value_wrapper() {
  // @@protoc_insertion_point(field_release:google.protobuf.testing.StructTestCases.list_value_wrapper)
  
  ::google::protobuf::testing::ListValueWrapper* temp = list_value_wrapper_;
  list_value_wrapper_ = NULL;
  return temp;
}
inline void StructTestCases::set_allocated_list_value_wrapper(::google::protobuf::testing::ListValueWrapper* list_value_wrapper) {
  delete list_value_wrapper_;
  list_value_wrapper_ = list_value_wrapper;
  if (list_value_wrapper) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_set_allocated:google.protobuf.testing.StructTestCases.list_value_wrapper)
}

// optional .google.protobuf.testing.ListValueWrapper list_value_wrapper_with_empty_list = 22;
inline bool StructTestCases::has_list_value_wrapper_with_empty_list() const {
  return this != internal_default_instance() && list_value_wrapper_with_empty_list_ != NULL;
}
inline void StructTestCases::clear_list_value_wrapper_with_empty_list() {
  if (GetArenaNoVirtual() == NULL && list_value_wrapper_with_empty_list_ != NULL) delete list_value_wrapper_with_empty_list_;
  list_value_wrapper_with_empty_list_ = NULL;
}
inline const ::google::protobuf::testing::ListValueWrapper& StructTestCases::list_value_wrapper_with_empty_list() const {
  // @@protoc_insertion_point(field_get:google.protobuf.testing.StructTestCases.list_value_wrapper_with_empty_list)
  return list_value_wrapper_with_empty_list_ != NULL ? *list_value_wrapper_with_empty_list_
                         : *::google::protobuf::testing::ListValueWrapper::internal_default_instance();
}
inline ::google::protobuf::testing::ListValueWrapper* StructTestCases::mutable_list_value_wrapper_with_empty_list() {
  
  if (list_value_wrapper_with_empty_list_ == NULL) {
    list_value_wrapper_with_empty_list_ = new ::google::protobuf::testing::ListValueWrapper;
  }
  // @@protoc_insertion_point(field_mutable:google.protobuf.testing.StructTestCases.list_value_wrapper_with_empty_list)
  return list_value_wrapper_with_empty_list_;
}
inline ::google::protobuf::testing::ListValueWrapper* StructTestCases::release_list_value_wrapper_with_empty_list() {
  // @@protoc_insertion_point(field_release:google.protobuf.testing.StructTestCases.list_value_wrapper_with_empty_list)
  
  ::google::protobuf::testing::ListValueWrapper* temp = list_value_wrapper_with_empty_list_;
  list_value_wrapper_with_empty_list_ = NULL;
  return temp;
}
inline void StructTestCases::set_allocated_list_value_wrapper_with_empty_list(::google::protobuf::testing::ListValueWrapper* list_value_wrapper_with_empty_list) {
  delete list_value_wrapper_with_empty_list_;
  list_value_wrapper_with_empty_list_ = list_value_wrapper_with_empty_list;
  if (list_value_wrapper_with_empty_list) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_set_allocated:google.protobuf.testing.StructTestCases.list_value_wrapper_with_empty_list)
}

// optional .google.protobuf.testing.ListValueWrapper list_value_wrapper_with_list_with_empty_struct = 23;
inline bool StructTestCases::has_list_value_wrapper_with_list_with_empty_struct() const {
  return this != internal_default_instance() && list_value_wrapper_with_list_with_empty_struct_ != NULL;
}
inline void StructTestCases::clear_list_value_wrapper_with_list_with_empty_struct() {
  if (GetArenaNoVirtual() == NULL && list_value_wrapper_with_list_with_empty_struct_ != NULL) delete list_value_wrapper_with_list_with_empty_struct_;
  list_value_wrapper_with_list_with_empty_struct_ = NULL;
}
inline const ::google::protobuf::testing::ListValueWrapper& StructTestCases::list_value_wrapper_with_list_with_empty_struct() const {
  // @@protoc_insertion_point(field_get:google.protobuf.testing.StructTestCases.list_value_wrapper_with_list_with_empty_struct)
  return list_value_wrapper_with_list_with_empty_struct_ != NULL ? *list_value_wrapper_with_list_with_empty_struct_
                         : *::google::protobuf::testing::ListValueWrapper::internal_default_instance();
}
inline ::google::protobuf::testing::ListValueWrapper* StructTestCases::mutable_list_value_wrapper_with_list_with_empty_struct() {
  
  if (list_value_wrapper_with_list_with_empty_struct_ == NULL) {
    list_value_wrapper_with_list_with_empty_struct_ = new ::google::protobuf::testing::ListValueWrapper;
  }
  // @@protoc_insertion_point(field_mutable:google.protobuf.testing.StructTestCases.list_value_wrapper_with_list_with_empty_struct)
  return list_value_wrapper_with_list_with_empty_struct_;
}
inline ::google::protobuf::testing::ListValueWrapper* StructTestCases::release_list_value_wrapper_with_list_with_empty_struct() {
  // @@protoc_insertion_point(field_release:google.protobuf.testing.StructTestCases.list_value_wrapper_with_list_with_empty_struct)
  
  ::google::protobuf::testing::ListValueWrapper* temp = list_value_wrapper_with_list_with_empty_struct_;
  list_value_wrapper_with_list_with_empty_struct_ = NULL;
  return temp;
}
inline void StructTestCases::set_allocated_list_value_wrapper_with_list_with_empty_struct(::google::protobuf::testing::ListValueWrapper* list_value_wrapper_with_list_with_empty_struct) {
  delete list_value_wrapper_with_list_with_empty_struct_;
  list_value_wrapper_with_list_with_empty_struct_ = list_value_wrapper_with_list_with_empty_struct;
  if (list_value_wrapper_with_list_with_empty_struct) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_set_allocated:google.protobuf.testing.StructTestCases.list_value_wrapper_with_list_with_empty_struct)
}

// optional .google.protobuf.Value top_level_value_simple = 24;
inline bool StructTestCases::has_top_level_value_simple() const {
  return this != internal_default_instance() && top_level_value_simple_ != NULL;
}
inline void StructTestCases::clear_top_level_value_simple() {
  if (GetArenaNoVirtual() == NULL && top_level_value_simple_ != NULL) delete top_level_value_simple_;
  top_level_value_simple_ = NULL;
}
inline const ::google::protobuf::Value& StructTestCases::top_level_value_simple() const {
  // @@protoc_insertion_point(field_get:google.protobuf.testing.StructTestCases.top_level_value_simple)
  return top_level_value_simple_ != NULL ? *top_level_value_simple_
                         : *::google::protobuf::Value::internal_default_instance();
}
inline ::google::protobuf::Value* StructTestCases::mutable_top_level_value_simple() {
  
  if (top_level_value_simple_ == NULL) {
    top_level_value_simple_ = new ::google::protobuf::Value;
  }
  // @@protoc_insertion_point(field_mutable:google.protobuf.testing.StructTestCases.top_level_value_simple)
  return top_level_value_simple_;
}
inline ::google::protobuf::Value* StructTestCases::release_top_level_value_simple() {
  // @@protoc_insertion_point(field_release:google.protobuf.testing.StructTestCases.top_level_value_simple)
  
  ::google::protobuf::Value* temp = top_level_value_simple_;
  top_level_value_simple_ = NULL;
  return temp;
}
inline void StructTestCases::set_allocated_top_level_value_simple(::google::protobuf::Value* top_level_value_simple) {
  delete top_level_value_simple_;
  if (top_level_value_simple != NULL && top_level_value_simple->GetArena() != NULL) {
    ::google::protobuf::Value* new_top_level_value_simple = new ::google::protobuf::Value;
    new_top_level_value_simple->CopyFrom(*top_level_value_simple);
    top_level_value_simple = new_top_level_value_simple;
  }
  top_level_value_simple_ = top_level_value_simple;
  if (top_level_value_simple) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_set_allocated:google.protobuf.testing.StructTestCases.top_level_value_simple)
}

// optional .google.protobuf.Value top_level_value_with_struct = 25;
inline bool StructTestCases::has_top_level_value_with_struct() const {
  return this != internal_default_instance() && top_level_value_with_struct_ != NULL;
}
inline void StructTestCases::clear_top_level_value_with_struct() {
  if (GetArenaNoVirtual() == NULL && top_level_value_with_struct_ != NULL) delete top_level_value_with_struct_;
  top_level_value_with_struct_ = NULL;
}
inline const ::google::protobuf::Value& StructTestCases::top_level_value_with_struct() const {
  // @@protoc_insertion_point(field_get:google.protobuf.testing.StructTestCases.top_level_value_with_struct)
  return top_level_value_with_struct_ != NULL ? *top_level_value_with_struct_
                         : *::google::protobuf::Value::internal_default_instance();
}
inline ::google::protobuf::Value* StructTestCases::mutable_top_level_value_with_struct() {
  
  if (top_level_value_with_struct_ == NULL) {
    top_level_value_with_struct_ = new ::google::protobuf::Value;
  }
  // @@protoc_insertion_point(field_mutable:google.protobuf.testing.StructTestCases.top_level_value_with_struct)
  return top_level_value_with_struct_;
}
inline ::google::protobuf::Value* StructTestCases::release_top_level_value_with_struct() {
  // @@protoc_insertion_point(field_release:google.protobuf.testing.StructTestCases.top_level_value_with_struct)
  
  ::google::protobuf::Value* temp = top_level_value_with_struct_;
  top_level_value_with_struct_ = NULL;
  return temp;
}
inline void StructTestCases::set_allocated_top_level_value_with_struct(::google::protobuf::Value* top_level_value_with_struct) {
  delete top_level_value_with_struct_;
  if (top_level_value_with_struct != NULL && top_level_value_with_struct->GetArena() != NULL) {
    ::google::protobuf::Value* new_top_level_value_with_struct = new ::google::protobuf::Value;
    new_top_level_value_with_struct->CopyFrom(*top_level_value_with_struct);
    top_level_value_with_struct = new_top_level_value_with_struct;
  }
  top_level_value_with_struct_ = top_level_value_with_struct;
  if (top_level_value_with_struct) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_set_allocated:google.protobuf.testing.StructTestCases.top_level_value_with_struct)
}

// optional .google.protobuf.Value top_level_value_with_list = 26;
inline bool StructTestCases::has_top_level_value_with_list() const {
  return this != internal_default_instance() && top_level_value_with_list_ != NULL;
}
inline void StructTestCases::clear_top_level_value_with_list() {
  if (GetArenaNoVirtual() == NULL && top_level_value_with_list_ != NULL) delete top_level_value_with_list_;
  top_level_value_with_list_ = NULL;
}
inline const ::google::protobuf::Value& StructTestCases::top_level_value_with_list() const {
  // @@protoc_insertion_point(field_get:google.protobuf.testing.StructTestCases.top_level_value_with_list)
  return top_level_value_with_list_ != NULL ? *top_level_value_with_list_
                         : *::google::protobuf::Value::internal_default_instance();
}
inline ::google::protobuf::Value* StructTestCases::mutable_top_level_value_with_list() {
  
  if (top_level_value_with_list_ == NULL) {
    top_level_value_with_list_ = new ::google::protobuf::Value;
  }
  // @@protoc_insertion_point(field_mutable:google.protobuf.testing.StructTestCases.top_level_value_with_list)
  return top_level_value_with_list_;
}
inline ::google::protobuf::Value* StructTestCases::release_top_level_value_with_list() {
  // @@protoc_insertion_point(field_release:google.protobuf.testing.StructTestCases.top_level_value_with_list)
  
  ::google::protobuf::Value* temp = top_level_value_with_list_;
  top_level_value_with_list_ = NULL;
  return temp;
}
inline void StructTestCases::set_allocated_top_level_value_with_list(::google::protobuf::Value* top_level_value_with_list) {
  delete top_level_value_with_list_;
  if (top_level_value_with_list != NULL && top_level_value_with_list->GetArena() != NULL) {
    ::google::protobuf::Value* new_top_level_value_with_list = new ::google::protobuf::Value;
    new_top_level_value_with_list->CopyFrom(*top_level_value_with_list);
    top_level_value_with_list = new_top_level_value_with_list;
  }
  top_level_value_with_list_ = top_level_value_with_list;
  if (top_level_value_with_list) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_set_allocated:google.protobuf.testing.StructTestCases.top_level_value_with_list)
}

// optional .google.protobuf.Value top_level_value_with_empty_list = 27;
inline bool StructTestCases::has_top_level_value_with_empty_list() const {
  return this != internal_default_instance() && top_level_value_with_empty_list_ != NULL;
}
inline void StructTestCases::clear_top_level_value_with_empty_list() {
  if (GetArenaNoVirtual() == NULL && top_level_value_with_empty_list_ != NULL) delete top_level_value_with_empty_list_;
  top_level_value_with_empty_list_ = NULL;
}
inline const ::google::protobuf::Value& StructTestCases::top_level_value_with_empty_list() const {
  // @@protoc_insertion_point(field_get:google.protobuf.testing.StructTestCases.top_level_value_with_empty_list)
  return top_level_value_with_empty_list_ != NULL ? *top_level_value_with_empty_list_
                         : *::google::protobuf::Value::internal_default_instance();
}
inline ::google::protobuf::Value* StructTestCases::mutable_top_level_value_with_empty_list() {
  
  if (top_level_value_with_empty_list_ == NULL) {
    top_level_value_with_empty_list_ = new ::google::protobuf::Value;
  }
  // @@protoc_insertion_point(field_mutable:google.protobuf.testing.StructTestCases.top_level_value_with_empty_list)
  return top_level_value_with_empty_list_;
}
inline ::google::protobuf::Value* StructTestCases::release_top_level_value_with_empty_list() {
  // @@protoc_insertion_point(field_release:google.protobuf.testing.StructTestCases.top_level_value_with_empty_list)
  
  ::google::protobuf::Value* temp = top_level_value_with_empty_list_;
  top_level_value_with_empty_list_ = NULL;
  return temp;
}
inline void StructTestCases::set_allocated_top_level_value_with_empty_list(::google::protobuf::Value* top_level_value_with_empty_list) {
  delete top_level_value_with_empty_list_;
  if (top_level_value_with_empty_list != NULL && top_level_value_with_empty_list->GetArena() != NULL) {
    ::google::protobuf::Value* new_top_level_value_with_empty_list = new ::google::protobuf::Value;
    new_top_level_value_with_empty_list->CopyFrom(*top_level_value_with_empty_list);
    top_level_value_with_empty_list = new_top_level_value_with_empty_list;
  }
  top_level_value_with_empty_list_ = top_level_value_with_empty_list;
  if (top_level_value_with_empty_list) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_set_allocated:google.protobuf.testing.StructTestCases.top_level_value_with_empty_list)
}

// optional .google.protobuf.Value top_level_value_with_list_with_empty_struct = 28;
inline bool StructTestCases::has_top_level_value_with_list_with_empty_struct() const {
  return this != internal_default_instance() && top_level_value_with_list_with_empty_struct_ != NULL;
}
inline void StructTestCases::clear_top_level_value_with_list_with_empty_struct() {
  if (GetArenaNoVirtual() == NULL && top_level_value_with_list_with_empty_struct_ != NULL) delete top_level_value_with_list_with_empty_struct_;
  top_level_value_with_list_with_empty_struct_ = NULL;
}
inline const ::google::protobuf::Value& StructTestCases::top_level_value_with_list_with_empty_struct() const {
  // @@protoc_insertion_point(field_get:google.protobuf.testing.StructTestCases.top_level_value_with_list_with_empty_struct)
  return top_level_value_with_list_with_empty_struct_ != NULL ? *top_level_value_with_list_with_empty_struct_
                         : *::google::protobuf::Value::internal_default_instance();
}
inline ::google::protobuf::Value* StructTestCases::mutable_top_level_value_with_list_with_empty_struct() {
  
  if (top_level_value_with_list_with_empty_struct_ == NULL) {
    top_level_value_with_list_with_empty_struct_ = new ::google::protobuf::Value;
  }
  // @@protoc_insertion_point(field_mutable:google.protobuf.testing.StructTestCases.top_level_value_with_list_with_empty_struct)
  return top_level_value_with_list_with_empty_struct_;
}
inline ::google::protobuf::Value* StructTestCases::release_top_level_value_with_list_with_empty_struct() {
  // @@protoc_insertion_point(field_release:google.protobuf.testing.StructTestCases.top_level_value_with_list_with_empty_struct)
  
  ::google::protobuf::Value* temp = top_level_value_with_list_with_empty_struct_;
  top_level_value_with_list_with_empty_struct_ = NULL;
  return temp;
}
inline void StructTestCases::set_allocated_top_level_value_with_list_with_empty_struct(::google::protobuf::Value* top_level_value_with_list_with_empty_struct) {
  delete top_level_value_with_list_with_empty_struct_;
  if (top_level_value_with_list_with_empty_struct != NULL && top_level_value_with_list_with_empty_struct->GetArena() != NULL) {
    ::google::protobuf::Value* new_top_level_value_with_list_with_empty_struct = new ::google::protobuf::Value;
    new_top_level_value_with_list_with_empty_struct->CopyFrom(*top_level_value_with_list_with_empty_struct);
    top_level_value_with_list_with_empty_struct = new_top_level_value_with_list_with_empty_struct;
  }
  top_level_value_with_list_with_empty_struct_ = top_level_value_with_list_with_empty_struct;
  if (top_level_value_with_list_with_empty_struct) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_set_allocated:google.protobuf.testing.StructTestCases.top_level_value_with_list_with_empty_struct)
}

// optional .google.protobuf.ListValue top_level_listvalue = 29;
inline bool StructTestCases::has_top_level_listvalue() const {
  return this != internal_default_instance() && top_level_listvalue_ != NULL;
}
inline void StructTestCases::clear_top_level_listvalue() {
  if (GetArenaNoVirtual() == NULL && top_level_listvalue_ != NULL) delete top_level_listvalue_;
  top_level_listvalue_ = NULL;
}
inline const ::google::protobuf::ListValue& StructTestCases::top_level_listvalue() const {
  // @@protoc_insertion_point(field_get:google.protobuf.testing.StructTestCases.top_level_listvalue)
  return top_level_listvalue_ != NULL ? *top_level_listvalue_
                         : *::google::protobuf::ListValue::internal_default_instance();
}
inline ::google::protobuf::ListValue* StructTestCases::mutable_top_level_listvalue() {
  
  if (top_level_listvalue_ == NULL) {
    top_level_listvalue_ = new ::google::protobuf::ListValue;
  }
  // @@protoc_insertion_point(field_mutable:google.protobuf.testing.StructTestCases.top_level_listvalue)
  return top_level_listvalue_;
}
inline ::google::protobuf::ListValue* StructTestCases::release_top_level_listvalue() {
  // @@protoc_insertion_point(field_release:google.protobuf.testing.StructTestCases.top_level_listvalue)
  
  ::google::protobuf::ListValue* temp = top_level_listvalue_;
  top_level_listvalue_ = NULL;
  return temp;
}
inline void StructTestCases::set_allocated_top_level_listvalue(::google::protobuf::ListValue* top_level_listvalue) {
  delete top_level_listvalue_;
  if (top_level_listvalue != NULL && top_level_listvalue->GetArena() != NULL) {
    ::google::protobuf::ListValue* new_top_level_listvalue = new ::google::protobuf::ListValue;
    new_top_level_listvalue->CopyFrom(*top_level_listvalue);
    top_level_listvalue = new_top_level_listvalue;
  }
  top_level_listvalue_ = top_level_listvalue;
  if (top_level_listvalue) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_set_allocated:google.protobuf.testing.StructTestCases.top_level_listvalue)
}

// optional .google.protobuf.ListValue top_level_empty_listvalue = 30;
inline bool StructTestCases::has_top_level_empty_listvalue() const {
  return this != internal_default_instance() && top_level_empty_listvalue_ != NULL;
}
inline void StructTestCases::clear_top_level_empty_listvalue() {
  if (GetArenaNoVirtual() == NULL && top_level_empty_listvalue_ != NULL) delete top_level_empty_listvalue_;
  top_level_empty_listvalue_ = NULL;
}
inline const ::google::protobuf::ListValue& StructTestCases::top_level_empty_listvalue() const {
  // @@protoc_insertion_point(field_get:google.protobuf.testing.StructTestCases.top_level_empty_listvalue)
  return top_level_empty_listvalue_ != NULL ? *top_level_empty_listvalue_
                         : *::google::protobuf::ListValue::internal_default_instance();
}
inline ::google::protobuf::ListValue* StructTestCases::mutable_top_level_empty_listvalue() {
  
  if (top_level_empty_listvalue_ == NULL) {
    top_level_empty_listvalue_ = new ::google::protobuf::ListValue;
  }
  // @@protoc_insertion_point(field_mutable:google.protobuf.testing.StructTestCases.top_level_empty_listvalue)
  return top_level_empty_listvalue_;
}
inline ::google::protobuf::ListValue* StructTestCases::release_top_level_empty_listvalue() {
  // @@protoc_insertion_point(field_release:google.protobuf.testing.StructTestCases.top_level_empty_listvalue)
  
  ::google::protobuf::ListValue* temp = top_level_empty_listvalue_;
  top_level_empty_listvalue_ = NULL;
  return temp;
}
inline void StructTestCases::set_allocated_top_level_empty_listvalue(::google::protobuf::ListValue* top_level_empty_listvalue) {
  delete top_level_empty_listvalue_;
  if (top_level_empty_listvalue != NULL && top_level_empty_listvalue->GetArena() != NULL) {
    ::google::protobuf::ListValue* new_top_level_empty_listvalue = new ::google::protobuf::ListValue;
    new_top_level_empty_listvalue->CopyFrom(*top_level_empty_listvalue);
    top_level_empty_listvalue = new_top_level_empty_listvalue;
  }
  top_level_empty_listvalue_ = top_level_empty_listvalue;
  if (top_level_empty_listvalue) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_set_allocated:google.protobuf.testing.StructTestCases.top_level_empty_listvalue)
}

// optional .google.protobuf.ListValue top_level_listvalue_with_empty_struct = 31;
inline bool StructTestCases::has_top_level_listvalue_with_empty_struct() const {
  return this != internal_default_instance() && top_level_listvalue_with_empty_struct_ != NULL;
}
inline void StructTestCases::clear_top_level_listvalue_with_empty_struct() {
  if (GetArenaNoVirtual() == NULL && top_level_listvalue_with_empty_struct_ != NULL) delete top_level_listvalue_with_empty_struct_;
  top_level_listvalue_with_empty_struct_ = NULL;
}
inline const ::google::protobuf::ListValue& StructTestCases::top_level_listvalue_with_empty_struct() const {
  // @@protoc_insertion_point(field_get:google.protobuf.testing.StructTestCases.top_level_listvalue_with_empty_struct)
  return top_level_listvalue_with_empty_struct_ != NULL ? *top_level_listvalue_with_empty_struct_
                         : *::google::protobuf::ListValue::internal_default_instance();
}
inline ::google::protobuf::ListValue* StructTestCases::mutable_top_level_listvalue_with_empty_struct() {
  
  if (top_level_listvalue_with_empty_struct_ == NULL) {
    top_level_listvalue_with_empty_struct_ = new ::google::protobuf::ListValue;
  }
  // @@protoc_insertion_point(field_mutable:google.protobuf.testing.StructTestCases.top_level_listvalue_with_empty_struct)
  return top_level_listvalue_with_empty_struct_;
}
inline ::google::protobuf::ListValue* StructTestCases::release_top_level_listvalue_with_empty_struct() {
  // @@protoc_insertion_point(field_release:google.protobuf.testing.StructTestCases.top_level_listvalue_with_empty_struct)
  
  ::google::protobuf::ListValue* temp = top_level_listvalue_with_empty_struct_;
  top_level_listvalue_with_empty_struct_ = NULL;
  return temp;
}
inline void StructTestCases::set_allocated_top_level_listvalue_with_empty_struct(::google::protobuf::ListValue* top_level_listvalue_with_empty_struct) {
  delete top_level_listvalue_with_empty_struct_;
  if (top_level_listvalue_with_empty_struct != NULL && top_level_listvalue_with_empty_struct->GetArena() != NULL) {
    ::google::protobuf::ListValue* new_top_level_listvalue_with_empty_struct = new ::google::protobuf::ListValue;
    new_top_level_listvalue_with_empty_struct->CopyFrom(*top_level_listvalue_with_empty_struct);
    top_level_listvalue_with_empty_struct = new_top_level_listvalue_with_empty_struct;
  }
  top_level_listvalue_with_empty_struct_ = top_level_listvalue_with_empty_struct;
  if (top_level_listvalue_with_empty_struct) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_set_allocated:google.protobuf.testing.StructTestCases.top_level_listvalue_with_empty_struct)
}

// optional .google.protobuf.testing.RepeatedValueWrapper repeated_value = 32;
inline bool StructTestCases::has_repeated_value() const {
  return this != internal_default_instance() && repeated_value_ != NULL;
}
inline void StructTestCases::clear_repeated_value() {
  if (GetArenaNoVirtual() == NULL && repeated_value_ != NULL) delete repeated_value_;
  repeated_value_ = NULL;
}
inline const ::google::protobuf::testing::RepeatedValueWrapper& StructTestCases::repeated_value() const {
  // @@protoc_insertion_point(field_get:google.protobuf.testing.StructTestCases.repeated_value)
  return repeated_value_ != NULL ? *repeated_value_
                         : *::google::protobuf::testing::RepeatedValueWrapper::internal_default_instance();
}
inline ::google::protobuf::testing::RepeatedValueWrapper* StructTestCases::mutable_repeated_value() {
  
  if (repeated_value_ == NULL) {
    repeated_value_ = new ::google::protobuf::testing::RepeatedValueWrapper;
  }
  // @@protoc_insertion_point(field_mutable:google.protobuf.testing.StructTestCases.repeated_value)
  return repeated_value_;
}
inline ::google::protobuf::testing::RepeatedValueWrapper* StructTestCases::release_repeated_value() {
  // @@protoc_insertion_point(field_release:google.protobuf.testing.StructTestCases.repeated_value)
  
  ::google::protobuf::testing::RepeatedValueWrapper* temp = repeated_value_;
  repeated_value_ = NULL;
  return temp;
}
inline void StructTestCases::set_allocated_repeated_value(::google::protobuf::testing::RepeatedValueWrapper* repeated_value) {
  delete repeated_value_;
  repeated_value_ = repeated_value;
  if (repeated_value) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_set_allocated:google.protobuf.testing.StructTestCases.repeated_value)
}

// optional .google.protobuf.testing.RepeatedValueWrapper repeated_value_nested_list = 33;
inline bool StructTestCases::has_repeated_value_nested_list() const {
  return this != internal_default_instance() && repeated_value_nested_list_ != NULL;
}
inline void StructTestCases::clear_repeated_value_nested_list() {
  if (GetArenaNoVirtual() == NULL && repeated_value_nested_list_ != NULL) delete repeated_value_nested_list_;
  repeated_value_nested_list_ = NULL;
}
inline const ::google::protobuf::testing::RepeatedValueWrapper& StructTestCases::repeated_value_nested_list() const {
  // @@protoc_insertion_point(field_get:google.protobuf.testing.StructTestCases.repeated_value_nested_list)
  return repeated_value_nested_list_ != NULL ? *repeated_value_nested_list_
                         : *::google::protobuf::testing::RepeatedValueWrapper::internal_default_instance();
}
inline ::google::protobuf::testing::RepeatedValueWrapper* StructTestCases::mutable_repeated_value_nested_list() {
  
  if (repeated_value_nested_list_ == NULL) {
    repeated_value_nested_list_ = new ::google::protobuf::testing::RepeatedValueWrapper;
  }
  // @@protoc_insertion_point(field_mutable:google.protobuf.testing.StructTestCases.repeated_value_nested_list)
  return repeated_value_nested_list_;
}
inline ::google::protobuf::testing::RepeatedValueWrapper* StructTestCases::release_repeated_value_nested_list() {
  // @@protoc_insertion_point(field_release:google.protobuf.testing.StructTestCases.repeated_value_nested_list)
  
  ::google::protobuf::testing::RepeatedValueWrapper* temp = repeated_value_nested_list_;
  repeated_value_nested_list_ = NULL;
  return temp;
}
inline void StructTestCases::set_allocated_repeated_value_nested_list(::google::protobuf::testing::RepeatedValueWrapper* repeated_value_nested_list) {
  delete repeated_value_nested_list_;
  repeated_value_nested_list_ = repeated_value_nested_list;
  if (repeated_value_nested_list) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_set_allocated:google.protobuf.testing.StructTestCases.repeated_value_nested_list)
}

// optional .google.protobuf.testing.RepeatedValueWrapper repeated_value_nested_list2 = 34;
inline bool StructTestCases::has_repeated_value_nested_list2() const {
  return this != internal_default_instance() && repeated_value_nested_list2_ != NULL;
}
inline void StructTestCases::clear_repeated_value_nested_list2() {
  if (GetArenaNoVirtual() == NULL && repeated_value_nested_list2_ != NULL) delete repeated_value_nested_list2_;
  repeated_value_nested_list2_ = NULL;
}
inline const ::google::protobuf::testing::RepeatedValueWrapper& StructTestCases::repeated_value_nested_list2() const {
  // @@protoc_insertion_point(field_get:google.protobuf.testing.StructTestCases.repeated_value_nested_list2)
  return repeated_value_nested_list2_ != NULL ? *repeated_value_nested_list2_
                         : *::google::protobuf::testing::RepeatedValueWrapper::internal_default_instance();
}
inline ::google::protobuf::testing::RepeatedValueWrapper* StructTestCases::mutable_repeated_value_nested_list2() {
  
  if (repeated_value_nested_list2_ == NULL) {
    repeated_value_nested_list2_ = new ::google::protobuf::testing::RepeatedValueWrapper;
  }
  // @@protoc_insertion_point(field_mutable:google.protobuf.testing.StructTestCases.repeated_value_nested_list2)
  return repeated_value_nested_list2_;
}
inline ::google::protobuf::testing::RepeatedValueWrapper* StructTestCases::release_repeated_value_nested_list2() {
  // @@protoc_insertion_point(field_release:google.protobuf.testing.StructTestCases.repeated_value_nested_list2)
  
  ::google::protobuf::testing::RepeatedValueWrapper* temp = repeated_value_nested_list2_;
  repeated_value_nested_list2_ = NULL;
  return temp;
}
inline void StructTestCases::set_allocated_repeated_value_nested_list2(::google::protobuf::testing::RepeatedValueWrapper* repeated_value_nested_list2) {
  delete repeated_value_nested_list2_;
  repeated_value_nested_list2_ = repeated_value_nested_list2;
  if (repeated_value_nested_list2) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_set_allocated:google.protobuf.testing.StructTestCases.repeated_value_nested_list2)
}

// optional .google.protobuf.testing.RepeatedValueWrapper repeated_value_nested_list3 = 35;
inline bool StructTestCases::has_repeated_value_nested_list3() const {
  return this != internal_default_instance() && repeated_value_nested_list3_ != NULL;
}
inline void StructTestCases::clear_repeated_value_nested_list3() {
  if (GetArenaNoVirtual() == NULL && repeated_value_nested_list3_ != NULL) delete repeated_value_nested_list3_;
  repeated_value_nested_list3_ = NULL;
}
inline const ::google::protobuf::testing::RepeatedValueWrapper& StructTestCases::repeated_value_nested_list3() const {
  // @@protoc_insertion_point(field_get:google.protobuf.testing.StructTestCases.repeated_value_nested_list3)
  return repeated_value_nested_list3_ != NULL ? *repeated_value_nested_list3_
                         : *::google::protobuf::testing::RepeatedValueWrapper::internal_default_instance();
}
inline ::google::protobuf::testing::RepeatedValueWrapper* StructTestCases::mutable_repeated_value_nested_list3() {
  
  if (repeated_value_nested_list3_ == NULL) {
    repeated_value_nested_list3_ = new ::google::protobuf::testing::RepeatedValueWrapper;
  }
  // @@protoc_insertion_point(field_mutable:google.protobuf.testing.StructTestCases.repeated_value_nested_list3)
  return repeated_value_nested_list3_;
}
inline ::google::protobuf::testing::RepeatedValueWrapper* StructTestCases::release_repeated_value_nested_list3() {
  // @@protoc_insertion_point(field_release:google.protobuf.testing.StructTestCases.repeated_value_nested_list3)
  
  ::google::protobuf::testing::RepeatedValueWrapper* temp = repeated_value_nested_list3_;
  repeated_value_nested_list3_ = NULL;
  return temp;
}
inline void StructTestCases::set_allocated_repeated_value_nested_list3(::google::protobuf::testing::RepeatedValueWrapper* repeated_value_nested_list3) {
  delete repeated_value_nested_list3_;
  repeated_value_nested_list3_ = repeated_value_nested_list3;
  if (repeated_value_nested_list3) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_set_allocated:google.protobuf.testing.StructTestCases.repeated_value_nested_list3)
}

// optional .google.protobuf.testing.RepeatedListValueWrapper repeated_listvalue = 36;
inline bool StructTestCases::has_repeated_listvalue() const {
  return this != internal_default_instance() && repeated_listvalue_ != NULL;
}
inline void StructTestCases::clear_repeated_listvalue() {
  if (GetArenaNoVirtual() == NULL && repeated_listvalue_ != NULL) delete repeated_listvalue_;
  repeated_listvalue_ = NULL;
}
inline const ::google::protobuf::testing::RepeatedListValueWrapper& StructTestCases::repeated_listvalue() const {
  // @@protoc_insertion_point(field_get:google.protobuf.testing.StructTestCases.repeated_listvalue)
  return repeated_listvalue_ != NULL ? *repeated_listvalue_
                         : *::google::protobuf::testing::RepeatedListValueWrapper::internal_default_instance();
}
inline ::google::protobuf::testing::RepeatedListValueWrapper* StructTestCases::mutable_repeated_listvalue() {
  
  if (repeated_listvalue_ == NULL) {
    repeated_listvalue_ = new ::google::protobuf::testing::RepeatedListValueWrapper;
  }
  // @@protoc_insertion_point(field_mutable:google.protobuf.testing.StructTestCases.repeated_listvalue)
  return repeated_listvalue_;
}
inline ::google::protobuf::testing::RepeatedListValueWrapper* StructTestCases::release_repeated_listvalue() {
  // @@protoc_insertion_point(field_release:google.protobuf.testing.StructTestCases.repeated_listvalue)
  
  ::google::protobuf::testing::RepeatedListValueWrapper* temp = repeated_listvalue_;
  repeated_listvalue_ = NULL;
  return temp;
}
inline void StructTestCases::set_allocated_repeated_listvalue(::google::protobuf::testing::RepeatedListValueWrapper* repeated_listvalue) {
  delete repeated_listvalue_;
  repeated_listvalue_ = repeated_listvalue;
  if (repeated_listvalue) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_set_allocated:google.protobuf.testing.StructTestCases.repeated_listvalue)
}

// optional .google.protobuf.testing.MapOfStruct map_of_struct = 37;
inline bool StructTestCases::has_map_of_struct() const {
  return this != internal_default_instance() && map_of_struct_ != NULL;
}
inline void StructTestCases::clear_map_of_struct() {
  if (GetArenaNoVirtual() == NULL && map_of_struct_ != NULL) delete map_of_struct_;
  map_of_struct_ = NULL;
}
inline const ::google::protobuf::testing::MapOfStruct& StructTestCases::map_of_struct() const {
  // @@protoc_insertion_point(field_get:google.protobuf.testing.StructTestCases.map_of_struct)
  return map_of_struct_ != NULL ? *map_of_struct_
                         : *::google::protobuf::testing::MapOfStruct::internal_default_instance();
}
inline ::google::protobuf::testing::MapOfStruct* StructTestCases::mutable_map_of_struct() {
  
  if (map_of_struct_ == NULL) {
    map_of_struct_ = new ::google::protobuf::testing::MapOfStruct;
  }
  // @@protoc_insertion_point(field_mutable:google.protobuf.testing.StructTestCases.map_of_struct)
  return map_of_struct_;
}
inline ::google::protobuf::testing::MapOfStruct* StructTestCases::release_map_of_struct() {
  // @@protoc_insertion_point(field_release:google.protobuf.testing.StructTestCases.map_of_struct)
  
  ::google::protobuf::testing::MapOfStruct* temp = map_of_struct_;
  map_of_struct_ = NULL;
  return temp;
}
inline void StructTestCases::set_allocated_map_of_struct(::google::protobuf::testing::MapOfStruct* map_of_struct) {
  delete map_of_struct_;
  map_of_struct_ = map_of_struct;
  if (map_of_struct) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_set_allocated:google.protobuf.testing.StructTestCases.map_of_struct)
}

// optional .google.protobuf.testing.MapOfStruct map_of_struct_value = 38;
inline bool StructTestCases::has_map_of_struct_value() const {
  return this != internal_default_instance() && map_of_struct_value_ != NULL;
}
inline void StructTestCases::clear_map_of_struct_value() {
  if (GetArenaNoVirtual() == NULL && map_of_struct_value_ != NULL) delete map_of_struct_value_;
  map_of_struct_value_ = NULL;
}
inline const ::google::protobuf::testing::MapOfStruct& StructTestCases::map_of_struct_value() const {
  // @@protoc_insertion_point(field_get:google.protobuf.testing.StructTestCases.map_of_struct_value)
  return map_of_struct_value_ != NULL ? *map_of_struct_value_
                         : *::google::protobuf::testing::MapOfStruct::internal_default_instance();
}
inline ::google::protobuf::testing::MapOfStruct* StructTestCases::mutable_map_of_struct_value() {
  
  if (map_of_struct_value_ == NULL) {
    map_of_struct_value_ = new ::google::protobuf::testing::MapOfStruct;
  }
  // @@protoc_insertion_point(field_mutable:google.protobuf.testing.StructTestCases.map_of_struct_value)
  return map_of_struct_value_;
}
inline ::google::protobuf::testing::MapOfStruct* StructTestCases::release_map_of_struct_value() {
  // @@protoc_insertion_point(field_release:google.protobuf.testing.StructTestCases.map_of_struct_value)
  
  ::google::protobuf::testing::MapOfStruct* temp = map_of_struct_value_;
  map_of_struct_value_ = NULL;
  return temp;
}
inline void StructTestCases::set_allocated_map_of_struct_value(::google::protobuf::testing::MapOfStruct* map_of_struct_value) {
  delete map_of_struct_value_;
  map_of_struct_value_ = map_of_struct_value;
  if (map_of_struct_value) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_set_allocated:google.protobuf.testing.StructTestCases.map_of_struct_value)
}

// optional .google.protobuf.testing.MapOfStruct map_of_listvalue = 39;
inline bool StructTestCases::has_map_of_listvalue() const {
  return this != internal_default_instance() && map_of_listvalue_ != NULL;
}
inline void StructTestCases::clear_map_of_listvalue() {
  if (GetArenaNoVirtual() == NULL && map_of_listvalue_ != NULL) delete map_of_listvalue_;
  map_of_listvalue_ = NULL;
}
inline const ::google::protobuf::testing::MapOfStruct& StructTestCases::map_of_listvalue() const {
  // @@protoc_insertion_point(field_get:google.protobuf.testing.StructTestCases.map_of_listvalue)
  return map_of_listvalue_ != NULL ? *map_of_listvalue_
                         : *::google::protobuf::testing::MapOfStruct::internal_default_instance();
}
inline ::google::protobuf::testing::MapOfStruct* StructTestCases::mutable_map_of_listvalue() {
  
  if (map_of_listvalue_ == NULL) {
    map_of_listvalue_ = new ::google::protobuf::testing::MapOfStruct;
  }
  // @@protoc_insertion_point(field_mutable:google.protobuf.testing.StructTestCases.map_of_listvalue)
  return map_of_listvalue_;
}
inline ::google::protobuf::testing::MapOfStruct* StructTestCases::release_map_of_listvalue() {
  // @@protoc_insertion_point(field_release:google.protobuf.testing.StructTestCases.map_of_listvalue)
  
  ::google::protobuf::testing::MapOfStruct* temp = map_of_listvalue_;
  map_of_listvalue_ = NULL;
  return temp;
}
inline void StructTestCases::set_allocated_map_of_listvalue(::google::protobuf::testing::MapOfStruct* map_of_listvalue) {
  delete map_of_listvalue_;
  map_of_listvalue_ = map_of_listvalue;
  if (map_of_listvalue) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_set_allocated:google.protobuf.testing.StructTestCases.map_of_listvalue)
}

inline const StructTestCases* StructTestCases::internal_default_instance() {
  return &StructTestCases_default_instance_.get();
}
// -------------------------------------------------------------------

// StructWrapper

// optional .google.protobuf.Struct struct = 1;
inline bool StructWrapper::has_struct_() const {
  return this != internal_default_instance() && struct__ != NULL;
}
inline void StructWrapper::clear_struct_() {
  if (GetArenaNoVirtual() == NULL && struct__ != NULL) delete struct__;
  struct__ = NULL;
}
inline const ::google::protobuf::Struct& StructWrapper::struct_() const {
  // @@protoc_insertion_point(field_get:google.protobuf.testing.StructWrapper.struct)
  return struct__ != NULL ? *struct__
                         : *::google::protobuf::Struct::internal_default_instance();
}
inline ::google::protobuf::Struct* StructWrapper::mutable_struct_() {
  
  if (struct__ == NULL) {
    struct__ = new ::google::protobuf::Struct;
  }
  // @@protoc_insertion_point(field_mutable:google.protobuf.testing.StructWrapper.struct)
  return struct__;
}
inline ::google::protobuf::Struct* StructWrapper::release_struct_() {
  // @@protoc_insertion_point(field_release:google.protobuf.testing.StructWrapper.struct)
  
  ::google::protobuf::Struct* temp = struct__;
  struct__ = NULL;
  return temp;
}
inline void StructWrapper::set_allocated_struct_(::google::protobuf::Struct* struct_) {
  delete struct__;
  if (struct_ != NULL && struct_->GetArena() != NULL) {
    ::google::protobuf::Struct* new_struct_ = new ::google::protobuf::Struct;
    new_struct_->CopyFrom(*struct_);
    struct_ = new_struct_;
  }
  struct__ = struct_;
  if (struct_) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_set_allocated:google.protobuf.testing.StructWrapper.struct)
}

inline const StructWrapper* StructWrapper::internal_default_instance() {
  return &StructWrapper_default_instance_.get();
}
// -------------------------------------------------------------------

// ValueWrapper

// optional .google.protobuf.Value value = 1;
inline bool ValueWrapper::has_value() const {
  return this != internal_default_instance() && value_ != NULL;
}
inline void ValueWrapper::clear_value() {
  if (GetArenaNoVirtual() == NULL && value_ != NULL) delete value_;
  value_ = NULL;
}
inline const ::google::protobuf::Value& ValueWrapper::value() const {
  // @@protoc_insertion_point(field_get:google.protobuf.testing.ValueWrapper.value)
  return value_ != NULL ? *value_
                         : *::google::protobuf::Value::internal_default_instance();
}
inline ::google::protobuf::Value* ValueWrapper::mutable_value() {
  
  if (value_ == NULL) {
    value_ = new ::google::protobuf::Value;
  }
  // @@protoc_insertion_point(field_mutable:google.protobuf.testing.ValueWrapper.value)
  return value_;
}
inline ::google::protobuf::Value* ValueWrapper::release_value() {
  // @@protoc_insertion_point(field_release:google.protobuf.testing.ValueWrapper.value)
  
  ::google::protobuf::Value* temp = value_;
  value_ = NULL;
  return temp;
}
inline void ValueWrapper::set_allocated_value(::google::protobuf::Value* value) {
  delete value_;
  if (value != NULL && value->GetArena() != NULL) {
    ::google::protobuf::Value* new_value = new ::google::protobuf::Value;
    new_value->CopyFrom(*value);
    value = new_value;
  }
  value_ = value;
  if (value) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_set_allocated:google.protobuf.testing.ValueWrapper.value)
}

inline const ValueWrapper* ValueWrapper::internal_default_instance() {
  return &ValueWrapper_default_instance_.get();
}
// -------------------------------------------------------------------

// RepeatedValueWrapper

// repeated .google.protobuf.Value values = 1;
inline int RepeatedValueWrapper::values_size() const {
  return values_.size();
}
inline void RepeatedValueWrapper::clear_values() {
  values_.Clear();
}
inline const ::google::protobuf::Value& RepeatedValueWrapper::values(int index) const {
  // @@protoc_insertion_point(field_get:google.protobuf.testing.RepeatedValueWrapper.values)
  return values_.Get(index);
}
inline ::google::protobuf::Value* RepeatedValueWrapper::mutable_values(int index) {
  // @@protoc_insertion_point(field_mutable:google.protobuf.testing.RepeatedValueWrapper.values)
  return values_.Mutable(index);
}
inline ::google::protobuf::Value* RepeatedValueWrapper::add_values() {
  // @@protoc_insertion_point(field_add:google.protobuf.testing.RepeatedValueWrapper.values)
  return values_.Add();
}
inline ::google::protobuf::RepeatedPtrField< ::google::protobuf::Value >*
RepeatedValueWrapper::mutable_values() {
  // @@protoc_insertion_point(field_mutable_list:google.protobuf.testing.RepeatedValueWrapper.values)
  return &values_;
}
inline const ::google::protobuf::RepeatedPtrField< ::google::protobuf::Value >&
RepeatedValueWrapper::values() const {
  // @@protoc_insertion_point(field_list:google.protobuf.testing.RepeatedValueWrapper.values)
  return values_;
}

inline const RepeatedValueWrapper* RepeatedValueWrapper::internal_default_instance() {
  return &RepeatedValueWrapper_default_instance_.get();
}
// -------------------------------------------------------------------

// ListValueWrapper

// optional .google.protobuf.ListValue shopping_list = 1;
inline bool ListValueWrapper::has_shopping_list() const {
  return this != internal_default_instance() && shopping_list_ != NULL;
}
inline void ListValueWrapper::clear_shopping_list() {
  if (GetArenaNoVirtual() == NULL && shopping_list_ != NULL) delete shopping_list_;
  shopping_list_ = NULL;
}
inline const ::google::protobuf::ListValue& ListValueWrapper::shopping_list() const {
  // @@protoc_insertion_point(field_get:google.protobuf.testing.ListValueWrapper.shopping_list)
  return shopping_list_ != NULL ? *shopping_list_
                         : *::google::protobuf::ListValue::internal_default_instance();
}
inline ::google::protobuf::ListValue* ListValueWrapper::mutable_shopping_list() {
  
  if (shopping_list_ == NULL) {
    shopping_list_ = new ::google::protobuf::ListValue;
  }
  // @@protoc_insertion_point(field_mutable:google.protobuf.testing.ListValueWrapper.shopping_list)
  return shopping_list_;
}
inline ::google::protobuf::ListValue* ListValueWrapper::release_shopping_list() {
  // @@protoc_insertion_point(field_release:google.protobuf.testing.ListValueWrapper.shopping_list)
  
  ::google::protobuf::ListValue* temp = shopping_list_;
  shopping_list_ = NULL;
  return temp;
}
inline void ListValueWrapper::set_allocated_shopping_list(::google::protobuf::ListValue* shopping_list) {
  delete shopping_list_;
  if (shopping_list != NULL && shopping_list->GetArena() != NULL) {
    ::google::protobuf::ListValue* new_shopping_list = new ::google::protobuf::ListValue;
    new_shopping_list->CopyFrom(*shopping_list);
    shopping_list = new_shopping_list;
  }
  shopping_list_ = shopping_list;
  if (shopping_list) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_set_allocated:google.protobuf.testing.ListValueWrapper.shopping_list)
}

inline const ListValueWrapper* ListValueWrapper::internal_default_instance() {
  return &ListValueWrapper_default_instance_.get();
}
// -------------------------------------------------------------------

// RepeatedListValueWrapper

// repeated .google.protobuf.ListValue dimensions = 1;
inline int RepeatedListValueWrapper::dimensions_size() const {
  return dimensions_.size();
}
inline void RepeatedListValueWrapper::clear_dimensions() {
  dimensions_.Clear();
}
inline const ::google::protobuf::ListValue& RepeatedListValueWrapper::dimensions(int index) const {
  // @@protoc_insertion_point(field_get:google.protobuf.testing.RepeatedListValueWrapper.dimensions)
  return dimensions_.Get(index);
}
inline ::google::protobuf::ListValue* RepeatedListValueWrapper::mutable_dimensions(int index) {
  // @@protoc_insertion_point(field_mutable:google.protobuf.testing.RepeatedListValueWrapper.dimensions)
  return dimensions_.Mutable(index);
}
inline ::google::protobuf::ListValue* RepeatedListValueWrapper::add_dimensions() {
  // @@protoc_insertion_point(field_add:google.protobuf.testing.RepeatedListValueWrapper.dimensions)
  return dimensions_.Add();
}
inline ::google::protobuf::RepeatedPtrField< ::google::protobuf::ListValue >*
RepeatedListValueWrapper::mutable_dimensions() {
  // @@protoc_insertion_point(field_mutable_list:google.protobuf.testing.RepeatedListValueWrapper.dimensions)
  return &dimensions_;
}
inline const ::google::protobuf::RepeatedPtrField< ::google::protobuf::ListValue >&
RepeatedListValueWrapper::dimensions() const {
  // @@protoc_insertion_point(field_list:google.protobuf.testing.RepeatedListValueWrapper.dimensions)
  return dimensions_;
}

inline const RepeatedListValueWrapper* RepeatedListValueWrapper::internal_default_instance() {
  return &RepeatedListValueWrapper_default_instance_.get();
}
// -------------------------------------------------------------------

// MapOfStruct

// map<string, .google.protobuf.Struct> struct_map = 1;
inline int MapOfStruct::struct_map_size() const {
  return struct_map_.size();
}
inline void MapOfStruct::clear_struct_map() {
  struct_map_.Clear();
}
inline const ::google::protobuf::Map< ::std::string, ::google::protobuf::Struct >&
MapOfStruct::struct_map() const {
  // @@protoc_insertion_point(field_map:google.protobuf.testing.MapOfStruct.struct_map)
  return struct_map_.GetMap();
}
inline ::google::protobuf::Map< ::std::string, ::google::protobuf::Struct >*
MapOfStruct::mutable_struct_map() {
  // @@protoc_insertion_point(field_mutable_map:google.protobuf.testing.MapOfStruct.struct_map)
  return struct_map_.MutableMap();
}

// map<string, .google.protobuf.Value> value_map = 2;
inline int MapOfStruct::value_map_size() const {
  return value_map_.size();
}
inline void MapOfStruct::clear_value_map() {
  value_map_.Clear();
}
inline const ::google::protobuf::Map< ::std::string, ::google::protobuf::Value >&
MapOfStruct::value_map() const {
  // @@protoc_insertion_point(field_map:google.protobuf.testing.MapOfStruct.value_map)
  return value_map_.GetMap();
}
inline ::google::protobuf::Map< ::std::string, ::google::protobuf::Value >*
MapOfStruct::mutable_value_map() {
  // @@protoc_insertion_point(field_mutable_map:google.protobuf.testing.MapOfStruct.value_map)
  return value_map_.MutableMap();
}

// map<string, .google.protobuf.ListValue> listvalue_map = 3;
inline int MapOfStruct::listvalue_map_size() const {
  return listvalue_map_.size();
}
inline void MapOfStruct::clear_listvalue_map() {
  listvalue_map_.Clear();
}
inline const ::google::protobuf::Map< ::std::string, ::google::protobuf::ListValue >&
MapOfStruct::listvalue_map() const {
  // @@protoc_insertion_point(field_map:google.protobuf.testing.MapOfStruct.listvalue_map)
  return listvalue_map_.GetMap();
}
inline ::google::protobuf::Map< ::std::string, ::google::protobuf::ListValue >*
MapOfStruct::mutable_listvalue_map() {
  // @@protoc_insertion_point(field_mutable_map:google.protobuf.testing.MapOfStruct.listvalue_map)
  return listvalue_map_.MutableMap();
}

inline const MapOfStruct* MapOfStruct::internal_default_instance() {
  return &MapOfStruct_default_instance_.get();
}
// -------------------------------------------------------------------

// Dummy

// optional string text = 1;
inline void Dummy::clear_text() {
  text_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline const ::std::string& Dummy::text() const {
  // @@protoc_insertion_point(field_get:google.protobuf.testing.Dummy.text)
  return text_.GetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void Dummy::set_text(const ::std::string& value) {
  
  text_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:google.protobuf.testing.Dummy.text)
}
inline void Dummy::set_text(const char* value) {
  
  text_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:google.protobuf.testing.Dummy.text)
}
inline void Dummy::set_text(const char* value, size_t size) {
  
  text_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:google.protobuf.testing.Dummy.text)
}
inline ::std::string* Dummy::mutable_text() {
  
  // @@protoc_insertion_point(field_mutable:google.protobuf.testing.Dummy.text)
  return text_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline ::std::string* Dummy::release_text() {
  // @@protoc_insertion_point(field_release:google.protobuf.testing.Dummy.text)
  
  return text_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void Dummy::set_allocated_text(::std::string* text) {
  if (text != NULL) {
    
  } else {
    
  }
  text_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), text);
  // @@protoc_insertion_point(field_set_allocated:google.protobuf.testing.Dummy.text)
}

inline const Dummy* Dummy::internal_default_instance() {
  return &Dummy_default_instance_.get();
}
// -------------------------------------------------------------------

// StructType

// optional .google.protobuf.Struct object = 1;
inline bool StructType::has_object() const {
  return this != internal_default_instance() && object_ != NULL;
}
inline void StructType::clear_object() {
  if (GetArenaNoVirtual() == NULL && object_ != NULL) delete object_;
  object_ = NULL;
}
inline const ::google::protobuf::Struct& StructType::object() const {
  // @@protoc_insertion_point(field_get:google.protobuf.testing.StructType.object)
  return object_ != NULL ? *object_
                         : *::google::protobuf::Struct::internal_default_instance();
}
inline ::google::protobuf::Struct* StructType::mutable_object() {
  
  if (object_ == NULL) {
    object_ = new ::google::protobuf::Struct;
  }
  // @@protoc_insertion_point(field_mutable:google.protobuf.testing.StructType.object)
  return object_;
}
inline ::google::protobuf::Struct* StructType::release_object() {
  // @@protoc_insertion_point(field_release:google.protobuf.testing.StructType.object)
  
  ::google::protobuf::Struct* temp = object_;
  object_ = NULL;
  return temp;
}
inline void StructType::set_allocated_object(::google::protobuf::Struct* object) {
  delete object_;
  if (object != NULL && object->GetArena() != NULL) {
    ::google::protobuf::Struct* new_object = new ::google::protobuf::Struct;
    new_object->CopyFrom(*object);
    object = new_object;
  }
  object_ = object;
  if (object) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_set_allocated:google.protobuf.testing.StructType.object)
}

inline const StructType* StructType::internal_default_instance() {
  return &StructType_default_instance_.get();
}
#endif  // !PROTOBUF_INLINE_NOT_IN_HEADERS
// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------


// @@protoc_insertion_point(namespace_scope)

}  // namespace testing
}  // namespace protobuf
}  // namespace google

// @@protoc_insertion_point(global_scope)

#endif  // PROTOBUF_google_2fprotobuf_2futil_2finternal_2ftestdata_2fstruct_2eproto__INCLUDED

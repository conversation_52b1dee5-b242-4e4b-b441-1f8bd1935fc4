// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: google/protobuf/util/internal/testdata/maps.proto

#define INTERNAL_SUPPRESS_PROTOBUF_FIELD_DEPRECATION
#include <google/protobuf/util/internal/testdata/maps.pb.h>

#include <algorithm>

#include <google/protobuf/stubs/common.h>
#include <google/protobuf/stubs/port.h>
#include <google/protobuf/stubs/once.h>
#include <google/protobuf/io/coded_stream.h>
#include <google/protobuf/wire_format_lite_inl.h>
#include <google/protobuf/descriptor.h>
#include <google/protobuf/generated_message_reflection.h>
#include <google/protobuf/reflection_ops.h>
#include <google/protobuf/wire_format.h>
// @@protoc_insertion_point(includes)

namespace google {
namespace protobuf {
namespace testing {

namespace {

const ::google::protobuf::Descriptor* MapsTestCases_descriptor_ = NULL;
const ::google::protobuf::internal::GeneratedMessageReflection*
  MapsTestCases_reflection_ = NULL;
const ::google::protobuf::Descriptor* EmptyMap_descriptor_ = NULL;
const ::google::protobuf::internal::GeneratedMessageReflection*
  EmptyMap_reflection_ = NULL;
const ::google::protobuf::Descriptor* EmptyMap_MapEntry_descriptor_ = NULL;
const ::google::protobuf::Descriptor* StringtoInt_descriptor_ = NULL;
const ::google::protobuf::internal::GeneratedMessageReflection*
  StringtoInt_reflection_ = NULL;
const ::google::protobuf::Descriptor* StringtoInt_MapEntry_descriptor_ = NULL;
const ::google::protobuf::Descriptor* IntToString_descriptor_ = NULL;
const ::google::protobuf::internal::GeneratedMessageReflection*
  IntToString_reflection_ = NULL;
const ::google::protobuf::Descriptor* IntToString_MapEntry_descriptor_ = NULL;
const ::google::protobuf::Descriptor* BoolToString_descriptor_ = NULL;
const ::google::protobuf::internal::GeneratedMessageReflection*
  BoolToString_reflection_ = NULL;
const ::google::protobuf::Descriptor* BoolToString_MapEntry_descriptor_ = NULL;
const ::google::protobuf::Descriptor* Mixed1_descriptor_ = NULL;
const ::google::protobuf::internal::GeneratedMessageReflection*
  Mixed1_reflection_ = NULL;
const ::google::protobuf::Descriptor* Mixed1_MapEntry_descriptor_ = NULL;
const ::google::protobuf::Descriptor* Mixed2_descriptor_ = NULL;
const ::google::protobuf::internal::GeneratedMessageReflection*
  Mixed2_reflection_ = NULL;
const ::google::protobuf::Descriptor* Mixed2_MapEntry_descriptor_ = NULL;
const ::google::protobuf::EnumDescriptor* Mixed2_E_descriptor_ = NULL;
const ::google::protobuf::Descriptor* MapOfObjects_descriptor_ = NULL;
const ::google::protobuf::internal::GeneratedMessageReflection*
  MapOfObjects_reflection_ = NULL;
const ::google::protobuf::Descriptor* MapOfObjects_M_descriptor_ = NULL;
const ::google::protobuf::internal::GeneratedMessageReflection*
  MapOfObjects_M_reflection_ = NULL;
const ::google::protobuf::Descriptor* MapOfObjects_MapEntry_descriptor_ = NULL;
const ::google::protobuf::Descriptor* DummyRequest_descriptor_ = NULL;
const ::google::protobuf::internal::GeneratedMessageReflection*
  DummyRequest_reflection_ = NULL;
const ::google::protobuf::Descriptor* MapIn_descriptor_ = NULL;
const ::google::protobuf::internal::GeneratedMessageReflection*
  MapIn_reflection_ = NULL;
const ::google::protobuf::Descriptor* MapIn_MapInputEntry_descriptor_ = NULL;
const ::google::protobuf::Descriptor* MapOut_descriptor_ = NULL;
const ::google::protobuf::internal::GeneratedMessageReflection*
  MapOut_reflection_ = NULL;
const ::google::protobuf::Descriptor* MapOut_Map1Entry_descriptor_ = NULL;
const ::google::protobuf::Descriptor* MapOut_Map2Entry_descriptor_ = NULL;
const ::google::protobuf::Descriptor* MapOut_Map3Entry_descriptor_ = NULL;
const ::google::protobuf::Descriptor* MapOut_Map4Entry_descriptor_ = NULL;
const ::google::protobuf::Descriptor* MapOutWireFormat_descriptor_ = NULL;
const ::google::protobuf::internal::GeneratedMessageReflection*
  MapOutWireFormat_reflection_ = NULL;
const ::google::protobuf::Descriptor* MapOutWireFormat_Map1Entry_descriptor_ = NULL;
const ::google::protobuf::internal::GeneratedMessageReflection*
  MapOutWireFormat_Map1Entry_reflection_ = NULL;
const ::google::protobuf::Descriptor* MapOutWireFormat_Map2Entry_descriptor_ = NULL;
const ::google::protobuf::internal::GeneratedMessageReflection*
  MapOutWireFormat_Map2Entry_reflection_ = NULL;
const ::google::protobuf::Descriptor* MapOutWireFormat_Map3Entry_descriptor_ = NULL;
const ::google::protobuf::internal::GeneratedMessageReflection*
  MapOutWireFormat_Map3Entry_reflection_ = NULL;
const ::google::protobuf::Descriptor* MapOutWireFormat_Map4Entry_descriptor_ = NULL;
const ::google::protobuf::internal::GeneratedMessageReflection*
  MapOutWireFormat_Map4Entry_reflection_ = NULL;
const ::google::protobuf::Descriptor* MapM_descriptor_ = NULL;
const ::google::protobuf::internal::GeneratedMessageReflection*
  MapM_reflection_ = NULL;

}  // namespace


void protobuf_AssignDesc_google_2fprotobuf_2futil_2finternal_2ftestdata_2fmaps_2eproto() GOOGLE_ATTRIBUTE_COLD;
void protobuf_AssignDesc_google_2fprotobuf_2futil_2finternal_2ftestdata_2fmaps_2eproto() {
  protobuf_AddDesc_google_2fprotobuf_2futil_2finternal_2ftestdata_2fmaps_2eproto();
  const ::google::protobuf::FileDescriptor* file =
    ::google::protobuf::DescriptorPool::generated_pool()->FindFileByName(
      "google/protobuf/util/internal/testdata/maps.proto");
  GOOGLE_CHECK(file != NULL);
  MapsTestCases_descriptor_ = file->message_type(0);
  static const int MapsTestCases_offsets_[13] = {
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MapsTestCases, empty_map_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MapsTestCases, string_to_int_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MapsTestCases, int_to_string_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MapsTestCases, mixed1_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MapsTestCases, mixed2_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MapsTestCases, map_of_objects_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MapsTestCases, empty_key_string_to_int1_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MapsTestCases, empty_key_string_to_int2_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MapsTestCases, empty_key_string_to_int3_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MapsTestCases, empty_key_bool_to_string_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MapsTestCases, empty_key_int_to_string_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MapsTestCases, empty_key_mixed_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MapsTestCases, empty_key_map_objects_),
  };
  MapsTestCases_reflection_ =
    ::google::protobuf::internal::GeneratedMessageReflection::NewGeneratedMessageReflection(
      MapsTestCases_descriptor_,
      MapsTestCases::internal_default_instance(),
      MapsTestCases_offsets_,
      -1,
      -1,
      -1,
      sizeof(MapsTestCases),
      GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MapsTestCases, _internal_metadata_));
  EmptyMap_descriptor_ = file->message_type(1);
  static const int EmptyMap_offsets_[1] = {
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(EmptyMap, map_),
  };
  EmptyMap_reflection_ =
    ::google::protobuf::internal::GeneratedMessageReflection::NewGeneratedMessageReflection(
      EmptyMap_descriptor_,
      EmptyMap::internal_default_instance(),
      EmptyMap_offsets_,
      -1,
      -1,
      -1,
      sizeof(EmptyMap),
      GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(EmptyMap, _internal_metadata_));
  EmptyMap_MapEntry_descriptor_ = EmptyMap_descriptor_->nested_type(0);
  StringtoInt_descriptor_ = file->message_type(2);
  static const int StringtoInt_offsets_[1] = {
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(StringtoInt, map_),
  };
  StringtoInt_reflection_ =
    ::google::protobuf::internal::GeneratedMessageReflection::NewGeneratedMessageReflection(
      StringtoInt_descriptor_,
      StringtoInt::internal_default_instance(),
      StringtoInt_offsets_,
      -1,
      -1,
      -1,
      sizeof(StringtoInt),
      GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(StringtoInt, _internal_metadata_));
  StringtoInt_MapEntry_descriptor_ = StringtoInt_descriptor_->nested_type(0);
  IntToString_descriptor_ = file->message_type(3);
  static const int IntToString_offsets_[1] = {
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(IntToString, map_),
  };
  IntToString_reflection_ =
    ::google::protobuf::internal::GeneratedMessageReflection::NewGeneratedMessageReflection(
      IntToString_descriptor_,
      IntToString::internal_default_instance(),
      IntToString_offsets_,
      -1,
      -1,
      -1,
      sizeof(IntToString),
      GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(IntToString, _internal_metadata_));
  IntToString_MapEntry_descriptor_ = IntToString_descriptor_->nested_type(0);
  BoolToString_descriptor_ = file->message_type(4);
  static const int BoolToString_offsets_[1] = {
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(BoolToString, map_),
  };
  BoolToString_reflection_ =
    ::google::protobuf::internal::GeneratedMessageReflection::NewGeneratedMessageReflection(
      BoolToString_descriptor_,
      BoolToString::internal_default_instance(),
      BoolToString_offsets_,
      -1,
      -1,
      -1,
      sizeof(BoolToString),
      GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(BoolToString, _internal_metadata_));
  BoolToString_MapEntry_descriptor_ = BoolToString_descriptor_->nested_type(0);
  Mixed1_descriptor_ = file->message_type(5);
  static const int Mixed1_offsets_[2] = {
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(Mixed1, msg_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(Mixed1, map_),
  };
  Mixed1_reflection_ =
    ::google::protobuf::internal::GeneratedMessageReflection::NewGeneratedMessageReflection(
      Mixed1_descriptor_,
      Mixed1::internal_default_instance(),
      Mixed1_offsets_,
      -1,
      -1,
      -1,
      sizeof(Mixed1),
      GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(Mixed1, _internal_metadata_));
  Mixed1_MapEntry_descriptor_ = Mixed1_descriptor_->nested_type(0);
  Mixed2_descriptor_ = file->message_type(6);
  static const int Mixed2_offsets_[2] = {
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(Mixed2, map_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(Mixed2, ee_),
  };
  Mixed2_reflection_ =
    ::google::protobuf::internal::GeneratedMessageReflection::NewGeneratedMessageReflection(
      Mixed2_descriptor_,
      Mixed2::internal_default_instance(),
      Mixed2_offsets_,
      -1,
      -1,
      -1,
      sizeof(Mixed2),
      GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(Mixed2, _internal_metadata_));
  Mixed2_MapEntry_descriptor_ = Mixed2_descriptor_->nested_type(0);
  Mixed2_E_descriptor_ = Mixed2_descriptor_->enum_type(0);
  MapOfObjects_descriptor_ = file->message_type(7);
  static const int MapOfObjects_offsets_[1] = {
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MapOfObjects, map_),
  };
  MapOfObjects_reflection_ =
    ::google::protobuf::internal::GeneratedMessageReflection::NewGeneratedMessageReflection(
      MapOfObjects_descriptor_,
      MapOfObjects::internal_default_instance(),
      MapOfObjects_offsets_,
      -1,
      -1,
      -1,
      sizeof(MapOfObjects),
      GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MapOfObjects, _internal_metadata_));
  MapOfObjects_M_descriptor_ = MapOfObjects_descriptor_->nested_type(0);
  static const int MapOfObjects_M_offsets_[1] = {
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MapOfObjects_M, inner_text_),
  };
  MapOfObjects_M_reflection_ =
    ::google::protobuf::internal::GeneratedMessageReflection::NewGeneratedMessageReflection(
      MapOfObjects_M_descriptor_,
      MapOfObjects_M::internal_default_instance(),
      MapOfObjects_M_offsets_,
      -1,
      -1,
      -1,
      sizeof(MapOfObjects_M),
      GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MapOfObjects_M, _internal_metadata_));
  MapOfObjects_MapEntry_descriptor_ = MapOfObjects_descriptor_->nested_type(1);
  DummyRequest_descriptor_ = file->message_type(8);
  static const int DummyRequest_offsets_[1] = {
  };
  DummyRequest_reflection_ =
    ::google::protobuf::internal::GeneratedMessageReflection::NewGeneratedMessageReflection(
      DummyRequest_descriptor_,
      DummyRequest::internal_default_instance(),
      DummyRequest_offsets_,
      -1,
      -1,
      -1,
      sizeof(DummyRequest),
      GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(DummyRequest, _internal_metadata_));
  MapIn_descriptor_ = file->message_type(9);
  static const int MapIn_offsets_[3] = {
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MapIn, other_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MapIn, things_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MapIn, map_input_),
  };
  MapIn_reflection_ =
    ::google::protobuf::internal::GeneratedMessageReflection::NewGeneratedMessageReflection(
      MapIn_descriptor_,
      MapIn::internal_default_instance(),
      MapIn_offsets_,
      -1,
      -1,
      -1,
      sizeof(MapIn),
      GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MapIn, _internal_metadata_));
  MapIn_MapInputEntry_descriptor_ = MapIn_descriptor_->nested_type(0);
  MapOut_descriptor_ = file->message_type(10);
  static const int MapOut_offsets_[5] = {
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MapOut, map1_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MapOut, map2_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MapOut, map3_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MapOut, map4_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MapOut, bar_),
  };
  MapOut_reflection_ =
    ::google::protobuf::internal::GeneratedMessageReflection::NewGeneratedMessageReflection(
      MapOut_descriptor_,
      MapOut::internal_default_instance(),
      MapOut_offsets_,
      -1,
      -1,
      -1,
      sizeof(MapOut),
      GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MapOut, _internal_metadata_));
  MapOut_Map1Entry_descriptor_ = MapOut_descriptor_->nested_type(0);
  MapOut_Map2Entry_descriptor_ = MapOut_descriptor_->nested_type(1);
  MapOut_Map3Entry_descriptor_ = MapOut_descriptor_->nested_type(2);
  MapOut_Map4Entry_descriptor_ = MapOut_descriptor_->nested_type(3);
  MapOutWireFormat_descriptor_ = file->message_type(11);
  static const int MapOutWireFormat_offsets_[5] = {
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MapOutWireFormat, map1_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MapOutWireFormat, map2_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MapOutWireFormat, map3_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MapOutWireFormat, map4_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MapOutWireFormat, bar_),
  };
  MapOutWireFormat_reflection_ =
    ::google::protobuf::internal::GeneratedMessageReflection::NewGeneratedMessageReflection(
      MapOutWireFormat_descriptor_,
      MapOutWireFormat::internal_default_instance(),
      MapOutWireFormat_offsets_,
      -1,
      -1,
      -1,
      sizeof(MapOutWireFormat),
      GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MapOutWireFormat, _internal_metadata_));
  MapOutWireFormat_Map1Entry_descriptor_ = MapOutWireFormat_descriptor_->nested_type(0);
  static const int MapOutWireFormat_Map1Entry_offsets_[2] = {
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MapOutWireFormat_Map1Entry, key_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MapOutWireFormat_Map1Entry, value_),
  };
  MapOutWireFormat_Map1Entry_reflection_ =
    ::google::protobuf::internal::GeneratedMessageReflection::NewGeneratedMessageReflection(
      MapOutWireFormat_Map1Entry_descriptor_,
      MapOutWireFormat_Map1Entry::internal_default_instance(),
      MapOutWireFormat_Map1Entry_offsets_,
      -1,
      -1,
      -1,
      sizeof(MapOutWireFormat_Map1Entry),
      GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MapOutWireFormat_Map1Entry, _internal_metadata_));
  MapOutWireFormat_Map2Entry_descriptor_ = MapOutWireFormat_descriptor_->nested_type(1);
  static const int MapOutWireFormat_Map2Entry_offsets_[2] = {
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MapOutWireFormat_Map2Entry, key_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MapOutWireFormat_Map2Entry, value_),
  };
  MapOutWireFormat_Map2Entry_reflection_ =
    ::google::protobuf::internal::GeneratedMessageReflection::NewGeneratedMessageReflection(
      MapOutWireFormat_Map2Entry_descriptor_,
      MapOutWireFormat_Map2Entry::internal_default_instance(),
      MapOutWireFormat_Map2Entry_offsets_,
      -1,
      -1,
      -1,
      sizeof(MapOutWireFormat_Map2Entry),
      GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MapOutWireFormat_Map2Entry, _internal_metadata_));
  MapOutWireFormat_Map3Entry_descriptor_ = MapOutWireFormat_descriptor_->nested_type(2);
  static const int MapOutWireFormat_Map3Entry_offsets_[2] = {
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MapOutWireFormat_Map3Entry, key_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MapOutWireFormat_Map3Entry, value_),
  };
  MapOutWireFormat_Map3Entry_reflection_ =
    ::google::protobuf::internal::GeneratedMessageReflection::NewGeneratedMessageReflection(
      MapOutWireFormat_Map3Entry_descriptor_,
      MapOutWireFormat_Map3Entry::internal_default_instance(),
      MapOutWireFormat_Map3Entry_offsets_,
      -1,
      -1,
      -1,
      sizeof(MapOutWireFormat_Map3Entry),
      GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MapOutWireFormat_Map3Entry, _internal_metadata_));
  MapOutWireFormat_Map4Entry_descriptor_ = MapOutWireFormat_descriptor_->nested_type(3);
  static const int MapOutWireFormat_Map4Entry_offsets_[2] = {
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MapOutWireFormat_Map4Entry, key_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MapOutWireFormat_Map4Entry, value_),
  };
  MapOutWireFormat_Map4Entry_reflection_ =
    ::google::protobuf::internal::GeneratedMessageReflection::NewGeneratedMessageReflection(
      MapOutWireFormat_Map4Entry_descriptor_,
      MapOutWireFormat_Map4Entry::internal_default_instance(),
      MapOutWireFormat_Map4Entry_offsets_,
      -1,
      -1,
      -1,
      sizeof(MapOutWireFormat_Map4Entry),
      GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MapOutWireFormat_Map4Entry, _internal_metadata_));
  MapM_descriptor_ = file->message_type(12);
  static const int MapM_offsets_[1] = {
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MapM, foo_),
  };
  MapM_reflection_ =
    ::google::protobuf::internal::GeneratedMessageReflection::NewGeneratedMessageReflection(
      MapM_descriptor_,
      MapM::internal_default_instance(),
      MapM_offsets_,
      -1,
      -1,
      -1,
      sizeof(MapM),
      GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MapM, _internal_metadata_));
}

namespace {

GOOGLE_PROTOBUF_DECLARE_ONCE(protobuf_AssignDescriptors_once_);
void protobuf_AssignDescriptorsOnce() {
  ::google::protobuf::GoogleOnceInit(&protobuf_AssignDescriptors_once_,
                 &protobuf_AssignDesc_google_2fprotobuf_2futil_2finternal_2ftestdata_2fmaps_2eproto);
}

void protobuf_RegisterTypes(const ::std::string&) GOOGLE_ATTRIBUTE_COLD;
void protobuf_RegisterTypes(const ::std::string&) {
  protobuf_AssignDescriptorsOnce();
  ::google::protobuf::MessageFactory::InternalRegisterGeneratedMessage(
      MapsTestCases_descriptor_, MapsTestCases::internal_default_instance());
  ::google::protobuf::MessageFactory::InternalRegisterGeneratedMessage(
      EmptyMap_descriptor_, EmptyMap::internal_default_instance());
  ::google::protobuf::MessageFactory::InternalRegisterGeneratedMessage(
        EmptyMap_MapEntry_descriptor_,
        ::google::protobuf::internal::MapEntry<
            ::google::protobuf::int32,
            ::google::protobuf::int32,
            ::google::protobuf::internal::WireFormatLite::TYPE_INT32,
            ::google::protobuf::internal::WireFormatLite::TYPE_INT32,
            0>::CreateDefaultInstance(
                EmptyMap_MapEntry_descriptor_));
  ::google::protobuf::MessageFactory::InternalRegisterGeneratedMessage(
      StringtoInt_descriptor_, StringtoInt::internal_default_instance());
  ::google::protobuf::MessageFactory::InternalRegisterGeneratedMessage(
        StringtoInt_MapEntry_descriptor_,
        ::google::protobuf::internal::MapEntry<
            ::std::string,
            ::google::protobuf::int32,
            ::google::protobuf::internal::WireFormatLite::TYPE_STRING,
            ::google::protobuf::internal::WireFormatLite::TYPE_INT32,
            0>::CreateDefaultInstance(
                StringtoInt_MapEntry_descriptor_));
  ::google::protobuf::MessageFactory::InternalRegisterGeneratedMessage(
      IntToString_descriptor_, IntToString::internal_default_instance());
  ::google::protobuf::MessageFactory::InternalRegisterGeneratedMessage(
        IntToString_MapEntry_descriptor_,
        ::google::protobuf::internal::MapEntry<
            ::google::protobuf::int32,
            ::std::string,
            ::google::protobuf::internal::WireFormatLite::TYPE_INT32,
            ::google::protobuf::internal::WireFormatLite::TYPE_STRING,
            0>::CreateDefaultInstance(
                IntToString_MapEntry_descriptor_));
  ::google::protobuf::MessageFactory::InternalRegisterGeneratedMessage(
      BoolToString_descriptor_, BoolToString::internal_default_instance());
  ::google::protobuf::MessageFactory::InternalRegisterGeneratedMessage(
        BoolToString_MapEntry_descriptor_,
        ::google::protobuf::internal::MapEntry<
            bool,
            ::std::string,
            ::google::protobuf::internal::WireFormatLite::TYPE_BOOL,
            ::google::protobuf::internal::WireFormatLite::TYPE_STRING,
            0>::CreateDefaultInstance(
                BoolToString_MapEntry_descriptor_));
  ::google::protobuf::MessageFactory::InternalRegisterGeneratedMessage(
      Mixed1_descriptor_, Mixed1::internal_default_instance());
  ::google::protobuf::MessageFactory::InternalRegisterGeneratedMessage(
        Mixed1_MapEntry_descriptor_,
        ::google::protobuf::internal::MapEntry<
            ::std::string,
            float,
            ::google::protobuf::internal::WireFormatLite::TYPE_STRING,
            ::google::protobuf::internal::WireFormatLite::TYPE_FLOAT,
            0>::CreateDefaultInstance(
                Mixed1_MapEntry_descriptor_));
  ::google::protobuf::MessageFactory::InternalRegisterGeneratedMessage(
      Mixed2_descriptor_, Mixed2::internal_default_instance());
  ::google::protobuf::MessageFactory::InternalRegisterGeneratedMessage(
        Mixed2_MapEntry_descriptor_,
        ::google::protobuf::internal::MapEntry<
            ::google::protobuf::int32,
            bool,
            ::google::protobuf::internal::WireFormatLite::TYPE_INT32,
            ::google::protobuf::internal::WireFormatLite::TYPE_BOOL,
            0>::CreateDefaultInstance(
                Mixed2_MapEntry_descriptor_));
  ::google::protobuf::MessageFactory::InternalRegisterGeneratedMessage(
      MapOfObjects_descriptor_, MapOfObjects::internal_default_instance());
  ::google::protobuf::MessageFactory::InternalRegisterGeneratedMessage(
      MapOfObjects_M_descriptor_, MapOfObjects_M::internal_default_instance());
  ::google::protobuf::MessageFactory::InternalRegisterGeneratedMessage(
        MapOfObjects_MapEntry_descriptor_,
        ::google::protobuf::internal::MapEntry<
            ::std::string,
            ::google::protobuf::testing::MapOfObjects_M,
            ::google::protobuf::internal::WireFormatLite::TYPE_STRING,
            ::google::protobuf::internal::WireFormatLite::TYPE_MESSAGE,
            0>::CreateDefaultInstance(
                MapOfObjects_MapEntry_descriptor_));
  ::google::protobuf::MessageFactory::InternalRegisterGeneratedMessage(
      DummyRequest_descriptor_, DummyRequest::internal_default_instance());
  ::google::protobuf::MessageFactory::InternalRegisterGeneratedMessage(
      MapIn_descriptor_, MapIn::internal_default_instance());
  ::google::protobuf::MessageFactory::InternalRegisterGeneratedMessage(
        MapIn_MapInputEntry_descriptor_,
        ::google::protobuf::internal::MapEntry<
            ::std::string,
            ::std::string,
            ::google::protobuf::internal::WireFormatLite::TYPE_STRING,
            ::google::protobuf::internal::WireFormatLite::TYPE_STRING,
            0>::CreateDefaultInstance(
                MapIn_MapInputEntry_descriptor_));
  ::google::protobuf::MessageFactory::InternalRegisterGeneratedMessage(
      MapOut_descriptor_, MapOut::internal_default_instance());
  ::google::protobuf::MessageFactory::InternalRegisterGeneratedMessage(
        MapOut_Map1Entry_descriptor_,
        ::google::protobuf::internal::MapEntry<
            ::std::string,
            ::google::protobuf::testing::MapM,
            ::google::protobuf::internal::WireFormatLite::TYPE_STRING,
            ::google::protobuf::internal::WireFormatLite::TYPE_MESSAGE,
            0>::CreateDefaultInstance(
                MapOut_Map1Entry_descriptor_));
  ::google::protobuf::MessageFactory::InternalRegisterGeneratedMessage(
        MapOut_Map2Entry_descriptor_,
        ::google::protobuf::internal::MapEntry<
            ::std::string,
            ::google::protobuf::testing::MapOut,
            ::google::protobuf::internal::WireFormatLite::TYPE_STRING,
            ::google::protobuf::internal::WireFormatLite::TYPE_MESSAGE,
            0>::CreateDefaultInstance(
                MapOut_Map2Entry_descriptor_));
  ::google::protobuf::MessageFactory::InternalRegisterGeneratedMessage(
        MapOut_Map3Entry_descriptor_,
        ::google::protobuf::internal::MapEntry<
            ::google::protobuf::int32,
            ::std::string,
            ::google::protobuf::internal::WireFormatLite::TYPE_INT32,
            ::google::protobuf::internal::WireFormatLite::TYPE_STRING,
            0>::CreateDefaultInstance(
                MapOut_Map3Entry_descriptor_));
  ::google::protobuf::MessageFactory::InternalRegisterGeneratedMessage(
        MapOut_Map4Entry_descriptor_,
        ::google::protobuf::internal::MapEntry<
            bool,
            ::std::string,
            ::google::protobuf::internal::WireFormatLite::TYPE_BOOL,
            ::google::protobuf::internal::WireFormatLite::TYPE_STRING,
            0>::CreateDefaultInstance(
                MapOut_Map4Entry_descriptor_));
  ::google::protobuf::MessageFactory::InternalRegisterGeneratedMessage(
      MapOutWireFormat_descriptor_, MapOutWireFormat::internal_default_instance());
  ::google::protobuf::MessageFactory::InternalRegisterGeneratedMessage(
      MapOutWireFormat_Map1Entry_descriptor_, MapOutWireFormat_Map1Entry::internal_default_instance());
  ::google::protobuf::MessageFactory::InternalRegisterGeneratedMessage(
      MapOutWireFormat_Map2Entry_descriptor_, MapOutWireFormat_Map2Entry::internal_default_instance());
  ::google::protobuf::MessageFactory::InternalRegisterGeneratedMessage(
      MapOutWireFormat_Map3Entry_descriptor_, MapOutWireFormat_Map3Entry::internal_default_instance());
  ::google::protobuf::MessageFactory::InternalRegisterGeneratedMessage(
      MapOutWireFormat_Map4Entry_descriptor_, MapOutWireFormat_Map4Entry::internal_default_instance());
  ::google::protobuf::MessageFactory::InternalRegisterGeneratedMessage(
      MapM_descriptor_, MapM::internal_default_instance());
}

}  // namespace

void protobuf_ShutdownFile_google_2fprotobuf_2futil_2finternal_2ftestdata_2fmaps_2eproto() {
  MapsTestCases_default_instance_.Shutdown();
  delete MapsTestCases_reflection_;
  EmptyMap_default_instance_.Shutdown();
  delete EmptyMap_reflection_;
  StringtoInt_default_instance_.Shutdown();
  delete StringtoInt_reflection_;
  IntToString_default_instance_.Shutdown();
  delete IntToString_reflection_;
  BoolToString_default_instance_.Shutdown();
  delete BoolToString_reflection_;
  Mixed1_default_instance_.Shutdown();
  delete Mixed1_reflection_;
  Mixed2_default_instance_.Shutdown();
  delete Mixed2_reflection_;
  MapOfObjects_default_instance_.Shutdown();
  delete MapOfObjects_reflection_;
  MapOfObjects_M_default_instance_.Shutdown();
  delete MapOfObjects_M_reflection_;
  DummyRequest_default_instance_.Shutdown();
  delete DummyRequest_reflection_;
  MapIn_default_instance_.Shutdown();
  delete MapIn_reflection_;
  MapOut_default_instance_.Shutdown();
  delete MapOut_reflection_;
  MapOutWireFormat_default_instance_.Shutdown();
  delete MapOutWireFormat_reflection_;
  MapOutWireFormat_Map1Entry_default_instance_.Shutdown();
  delete MapOutWireFormat_Map1Entry_reflection_;
  MapOutWireFormat_Map2Entry_default_instance_.Shutdown();
  delete MapOutWireFormat_Map2Entry_reflection_;
  MapOutWireFormat_Map3Entry_default_instance_.Shutdown();
  delete MapOutWireFormat_Map3Entry_reflection_;
  MapOutWireFormat_Map4Entry_default_instance_.Shutdown();
  delete MapOutWireFormat_Map4Entry_reflection_;
  MapM_default_instance_.Shutdown();
  delete MapM_reflection_;
}

void protobuf_InitDefaults_google_2fprotobuf_2futil_2finternal_2ftestdata_2fmaps_2eproto_impl() {
  GOOGLE_PROTOBUF_VERIFY_VERSION;

  MapsTestCases_default_instance_.DefaultConstruct();
  EmptyMap_default_instance_.DefaultConstruct();
  ::google::protobuf::internal::GetEmptyString();
  ::google::protobuf::internal::GetEmptyString();
  StringtoInt_default_instance_.DefaultConstruct();
  ::google::protobuf::internal::GetEmptyString();
  ::google::protobuf::internal::GetEmptyString();
  IntToString_default_instance_.DefaultConstruct();
  ::google::protobuf::internal::GetEmptyString();
  BoolToString_default_instance_.DefaultConstruct();
  ::google::protobuf::internal::GetEmptyString();
  Mixed1_default_instance_.DefaultConstruct();
  Mixed2_default_instance_.DefaultConstruct();
  ::google::protobuf::internal::GetEmptyString();
  MapOfObjects_default_instance_.DefaultConstruct();
  ::google::protobuf::internal::GetEmptyString();
  MapOfObjects_M_default_instance_.DefaultConstruct();
  DummyRequest_default_instance_.DefaultConstruct();
  ::google::protobuf::internal::GetEmptyString();
  MapIn_default_instance_.DefaultConstruct();
  ::google::protobuf::internal::GetEmptyString();
  MapOut_default_instance_.DefaultConstruct();
  ::google::protobuf::internal::GetEmptyString();
  MapOutWireFormat_default_instance_.DefaultConstruct();
  ::google::protobuf::internal::GetEmptyString();
  MapOutWireFormat_Map1Entry_default_instance_.DefaultConstruct();
  ::google::protobuf::internal::GetEmptyString();
  MapOutWireFormat_Map2Entry_default_instance_.DefaultConstruct();
  ::google::protobuf::internal::GetEmptyString();
  MapOutWireFormat_Map3Entry_default_instance_.DefaultConstruct();
  ::google::protobuf::internal::GetEmptyString();
  MapOutWireFormat_Map4Entry_default_instance_.DefaultConstruct();
  ::google::protobuf::internal::GetEmptyString();
  MapM_default_instance_.DefaultConstruct();
  MapsTestCases_default_instance_.get_mutable()->InitAsDefaultInstance();
  EmptyMap_default_instance_.get_mutable()->InitAsDefaultInstance();
  StringtoInt_default_instance_.get_mutable()->InitAsDefaultInstance();
  IntToString_default_instance_.get_mutable()->InitAsDefaultInstance();
  BoolToString_default_instance_.get_mutable()->InitAsDefaultInstance();
  Mixed1_default_instance_.get_mutable()->InitAsDefaultInstance();
  Mixed2_default_instance_.get_mutable()->InitAsDefaultInstance();
  MapOfObjects_default_instance_.get_mutable()->InitAsDefaultInstance();
  MapOfObjects_M_default_instance_.get_mutable()->InitAsDefaultInstance();
  DummyRequest_default_instance_.get_mutable()->InitAsDefaultInstance();
  MapIn_default_instance_.get_mutable()->InitAsDefaultInstance();
  MapOut_default_instance_.get_mutable()->InitAsDefaultInstance();
  MapOutWireFormat_default_instance_.get_mutable()->InitAsDefaultInstance();
  MapOutWireFormat_Map1Entry_default_instance_.get_mutable()->InitAsDefaultInstance();
  MapOutWireFormat_Map2Entry_default_instance_.get_mutable()->InitAsDefaultInstance();
  MapOutWireFormat_Map3Entry_default_instance_.get_mutable()->InitAsDefaultInstance();
  MapOutWireFormat_Map4Entry_default_instance_.get_mutable()->InitAsDefaultInstance();
  MapM_default_instance_.get_mutable()->InitAsDefaultInstance();
}

GOOGLE_PROTOBUF_DECLARE_ONCE(protobuf_InitDefaults_google_2fprotobuf_2futil_2finternal_2ftestdata_2fmaps_2eproto_once_);
void protobuf_InitDefaults_google_2fprotobuf_2futil_2finternal_2ftestdata_2fmaps_2eproto() {
  ::google::protobuf::GoogleOnceInit(&protobuf_InitDefaults_google_2fprotobuf_2futil_2finternal_2ftestdata_2fmaps_2eproto_once_,
                 &protobuf_InitDefaults_google_2fprotobuf_2futil_2finternal_2ftestdata_2fmaps_2eproto_impl);
}
void protobuf_AddDesc_google_2fprotobuf_2futil_2finternal_2ftestdata_2fmaps_2eproto_impl() {
  GOOGLE_PROTOBUF_VERIFY_VERSION;

  protobuf_InitDefaults_google_2fprotobuf_2futil_2finternal_2ftestdata_2fmaps_2eproto();
  ::google::protobuf::DescriptorPool::InternalAddGeneratedFile(
    "\n1google/protobuf/util/internal/testdata"
    "/maps.proto\022\027google.protobuf.testing\"\310\006\n"
    "\rMapsTestCases\0224\n\tempty_map\030\001 \001(\0132!.goog"
    "le.protobuf.testing.EmptyMap\022;\n\rstring_t"
    "o_int\030\002 \001(\0132$.google.protobuf.testing.St"
    "ringtoInt\022;\n\rint_to_string\030\003 \001(\0132$.googl"
    "e.protobuf.testing.IntToString\022/\n\006mixed1"
    "\030\004 \001(\0132\037.google.protobuf.testing.Mixed1\022"
    "/\n\006mixed2\030\005 \001(\0132\037.google.protobuf.testin"
    "g.Mixed2\022=\n\016map_of_objects\030\006 \001(\0132%.googl"
    "e.protobuf.testing.MapOfObjects\022F\n\030empty"
    "_key_string_to_int1\030\007 \001(\0132$.google.proto"
    "buf.testing.StringtoInt\022F\n\030empty_key_str"
    "ing_to_int2\030\010 \001(\0132$.google.protobuf.test"
    "ing.StringtoInt\022F\n\030empty_key_string_to_i"
    "nt3\030\t \001(\0132$.google.protobuf.testing.Stri"
    "ngtoInt\022G\n\030empty_key_bool_to_string\030\n \001("
    "\0132%.google.protobuf.testing.BoolToString"
    "\022E\n\027empty_key_int_to_string\030\013 \001(\0132$.goog"
    "le.protobuf.testing.IntToString\0228\n\017empty"
    "_key_mixed\030\014 \001(\0132\037.google.protobuf.testi"
    "ng.Mixed1\022D\n\025empty_key_map_objects\030\r \001(\013"
    "2%.google.protobuf.testing.MapOfObjects\""
    "o\n\010EmptyMap\0227\n\003map\030\001 \003(\0132*.google.protob"
    "uf.testing.EmptyMap.MapEntry\032*\n\010MapEntry"
    "\022\013\n\003key\030\001 \001(\005\022\r\n\005value\030\002 \001(\005:\0028\001\"u\n\013Stri"
    "ngtoInt\022:\n\003map\030\001 \003(\0132-.google.protobuf.t"
    "esting.StringtoInt.MapEntry\032*\n\010MapEntry\022"
    "\013\n\003key\030\001 \001(\t\022\r\n\005value\030\002 \001(\005:\0028\001\"u\n\013IntTo"
    "String\022:\n\003map\030\001 \003(\0132-.google.protobuf.te"
    "sting.IntToString.MapEntry\032*\n\010MapEntry\022\013"
    "\n\003key\030\001 \001(\005\022\r\n\005value\030\002 \001(\t:\0028\001\"w\n\014BoolTo"
    "String\022;\n\003map\030\001 \003(\0132..google.protobuf.te"
    "sting.BoolToString.MapEntry\032*\n\010MapEntry\022"
    "\013\n\003key\030\001 \001(\010\022\r\n\005value\030\002 \001(\t:\0028\001\"x\n\006Mixed"
    "1\022\013\n\003msg\030\001 \001(\t\0225\n\003map\030\002 \003(\0132(.google.pro"
    "tobuf.testing.Mixed1.MapEntry\032*\n\010MapEntr"
    "y\022\013\n\003key\030\001 \001(\t\022\r\n\005value\030\002 \001(\002:\0028\001\"\277\001\n\006Mi"
    "xed2\0225\n\003map\030\001 \003(\0132(.google.protobuf.test"
    "ing.Mixed2.MapEntry\022-\n\002ee\030\002 \001(\0162!.google"
    ".protobuf.testing.Mixed2.E\032*\n\010MapEntry\022\013"
    "\n\003key\030\001 \001(\005\022\r\n\005value\030\002 \001(\010:\0028\001\"#\n\001E\022\006\n\002E"
    "0\020\000\022\006\n\002E1\020\001\022\006\n\002E2\020\002\022\006\n\002E3\020\003\"\271\001\n\014MapOfObj"
    "ects\022;\n\003map\030\001 \003(\0132..google.protobuf.test"
    "ing.MapOfObjects.MapEntry\032\027\n\001M\022\022\n\ninner_"
    "text\030\001 \001(\t\032S\n\010MapEntry\022\013\n\003key\030\001 \001(\t\0226\n\005v"
    "alue\030\002 \001(\0132\'.google.protobuf.testing.Map"
    "OfObjects.M:\0028\001\"\016\n\014DummyRequest\"\230\001\n\005MapI"
    "n\022\r\n\005other\030\001 \001(\t\022\016\n\006things\030\002 \003(\t\022\?\n\tmap_"
    "input\030\003 \003(\0132,.google.protobuf.testing.Ma"
    "pIn.MapInputEntry\032/\n\rMapInputEntry\022\013\n\003ke"
    "y\030\001 \001(\t\022\r\n\005value\030\002 \001(\t:\0028\001\"\355\003\n\006MapOut\0227\n"
    "\004map1\030\001 \003(\0132).google.protobuf.testing.Ma"
    "pOut.Map1Entry\0227\n\004map2\030\002 \003(\0132).google.pr"
    "otobuf.testing.MapOut.Map2Entry\0227\n\004map3\030"
    "\003 \003(\0132).google.protobuf.testing.MapOut.M"
    "ap3Entry\0227\n\004map4\030\005 \003(\0132).google.protobuf"
    ".testing.MapOut.Map4Entry\022\013\n\003bar\030\004 \001(\t\032J"
    "\n\tMap1Entry\022\013\n\003key\030\001 \001(\t\022,\n\005value\030\002 \001(\0132"
    "\035.google.protobuf.testing.MapM:\0028\001\032L\n\tMa"
    "p2Entry\022\013\n\003key\030\001 \001(\t\022.\n\005value\030\002 \001(\0132\037.go"
    "ogle.protobuf.testing.MapOut:\0028\001\032+\n\tMap3"
    "Entry\022\013\n\003key\030\001 \001(\005\022\r\n\005value\030\002 \001(\t:\0028\001\032+\n"
    "\tMap4Entry\022\013\n\003key\030\001 \001(\010\022\r\n\005value\030\002 \001(\t:\002"
    "8\001\"\217\004\n\020MapOutWireFormat\022A\n\004map1\030\001 \003(\01323."
    "google.protobuf.testing.MapOutWireFormat"
    ".Map1Entry\022A\n\004map2\030\002 \003(\01323.google.protob"
    "uf.testing.MapOutWireFormat.Map2Entry\022A\n"
    "\004map3\030\003 \003(\01323.google.protobuf.testing.Ma"
    "pOutWireFormat.Map3Entry\022A\n\004map4\030\005 \003(\01323"
    ".google.protobuf.testing.MapOutWireForma"
    "t.Map4Entry\022\013\n\003bar\030\004 \001(\t\032F\n\tMap1Entry\022\013\n"
    "\003key\030\001 \001(\t\022,\n\005value\030\002 \001(\0132\035.google.proto"
    "buf.testing.MapM\032H\n\tMap2Entry\022\013\n\003key\030\001 \001"
    "(\t\022.\n\005value\030\002 \001(\0132\037.google.protobuf.test"
    "ing.MapOut\032\'\n\tMap3Entry\022\013\n\003key\030\001 \001(\005\022\r\n\005"
    "value\030\002 \001(\t\032\'\n\tMap4Entry\022\013\n\003key\030\001 \001(\010\022\r\n"
    "\005value\030\002 \001(\t\"\023\n\004MapM\022\013\n\003foo\030\001 \001(\t2h\n\017Map"
    "sTestService\022U\n\004Call\022%.google.protobuf.t"
    "esting.DummyRequest\032&.google.protobuf.te"
    "sting.MapsTestCasesb\006proto3", 3227);
  ::google::protobuf::MessageFactory::InternalRegisterGeneratedFile(
    "google/protobuf/util/internal/testdata/maps.proto", &protobuf_RegisterTypes);
  ::google::protobuf::internal::OnShutdown(&protobuf_ShutdownFile_google_2fprotobuf_2futil_2finternal_2ftestdata_2fmaps_2eproto);
}

GOOGLE_PROTOBUF_DECLARE_ONCE(protobuf_AddDesc_google_2fprotobuf_2futil_2finternal_2ftestdata_2fmaps_2eproto_once_);
void protobuf_AddDesc_google_2fprotobuf_2futil_2finternal_2ftestdata_2fmaps_2eproto() {
  ::google::protobuf::GoogleOnceInit(&protobuf_AddDesc_google_2fprotobuf_2futil_2finternal_2ftestdata_2fmaps_2eproto_once_,
                 &protobuf_AddDesc_google_2fprotobuf_2futil_2finternal_2ftestdata_2fmaps_2eproto_impl);
}
// Force AddDescriptors() to be called at static initialization time.
struct StaticDescriptorInitializer_google_2fprotobuf_2futil_2finternal_2ftestdata_2fmaps_2eproto {
  StaticDescriptorInitializer_google_2fprotobuf_2futil_2finternal_2ftestdata_2fmaps_2eproto() {
    protobuf_AddDesc_google_2fprotobuf_2futil_2finternal_2ftestdata_2fmaps_2eproto();
  }
} static_descriptor_initializer_google_2fprotobuf_2futil_2finternal_2ftestdata_2fmaps_2eproto_;

namespace {

static void MergeFromFail(int line) GOOGLE_ATTRIBUTE_COLD GOOGLE_ATTRIBUTE_NORETURN;
static void MergeFromFail(int line) {
  ::google::protobuf::internal::MergeFromFail(__FILE__, line);
}

}  // namespace


// ===================================================================

#if !defined(_MSC_VER) || _MSC_VER >= 1900
const int MapsTestCases::kEmptyMapFieldNumber;
const int MapsTestCases::kStringToIntFieldNumber;
const int MapsTestCases::kIntToStringFieldNumber;
const int MapsTestCases::kMixed1FieldNumber;
const int MapsTestCases::kMixed2FieldNumber;
const int MapsTestCases::kMapOfObjectsFieldNumber;
const int MapsTestCases::kEmptyKeyStringToInt1FieldNumber;
const int MapsTestCases::kEmptyKeyStringToInt2FieldNumber;
const int MapsTestCases::kEmptyKeyStringToInt3FieldNumber;
const int MapsTestCases::kEmptyKeyBoolToStringFieldNumber;
const int MapsTestCases::kEmptyKeyIntToStringFieldNumber;
const int MapsTestCases::kEmptyKeyMixedFieldNumber;
const int MapsTestCases::kEmptyKeyMapObjectsFieldNumber;
#endif  // !defined(_MSC_VER) || _MSC_VER >= 1900

MapsTestCases::MapsTestCases()
  : ::google::protobuf::Message(), _internal_metadata_(NULL) {
  if (this != internal_default_instance()) protobuf_InitDefaults_google_2fprotobuf_2futil_2finternal_2ftestdata_2fmaps_2eproto();
  SharedCtor();
  // @@protoc_insertion_point(constructor:google.protobuf.testing.MapsTestCases)
}

void MapsTestCases::InitAsDefaultInstance() {
  empty_map_ = const_cast< ::google::protobuf::testing::EmptyMap*>(
      ::google::protobuf::testing::EmptyMap::internal_default_instance());
  string_to_int_ = const_cast< ::google::protobuf::testing::StringtoInt*>(
      ::google::protobuf::testing::StringtoInt::internal_default_instance());
  int_to_string_ = const_cast< ::google::protobuf::testing::IntToString*>(
      ::google::protobuf::testing::IntToString::internal_default_instance());
  mixed1_ = const_cast< ::google::protobuf::testing::Mixed1*>(
      ::google::protobuf::testing::Mixed1::internal_default_instance());
  mixed2_ = const_cast< ::google::protobuf::testing::Mixed2*>(
      ::google::protobuf::testing::Mixed2::internal_default_instance());
  map_of_objects_ = const_cast< ::google::protobuf::testing::MapOfObjects*>(
      ::google::protobuf::testing::MapOfObjects::internal_default_instance());
  empty_key_string_to_int1_ = const_cast< ::google::protobuf::testing::StringtoInt*>(
      ::google::protobuf::testing::StringtoInt::internal_default_instance());
  empty_key_string_to_int2_ = const_cast< ::google::protobuf::testing::StringtoInt*>(
      ::google::protobuf::testing::StringtoInt::internal_default_instance());
  empty_key_string_to_int3_ = const_cast< ::google::protobuf::testing::StringtoInt*>(
      ::google::protobuf::testing::StringtoInt::internal_default_instance());
  empty_key_bool_to_string_ = const_cast< ::google::protobuf::testing::BoolToString*>(
      ::google::protobuf::testing::BoolToString::internal_default_instance());
  empty_key_int_to_string_ = const_cast< ::google::protobuf::testing::IntToString*>(
      ::google::protobuf::testing::IntToString::internal_default_instance());
  empty_key_mixed_ = const_cast< ::google::protobuf::testing::Mixed1*>(
      ::google::protobuf::testing::Mixed1::internal_default_instance());
  empty_key_map_objects_ = const_cast< ::google::protobuf::testing::MapOfObjects*>(
      ::google::protobuf::testing::MapOfObjects::internal_default_instance());
}

MapsTestCases::MapsTestCases(const MapsTestCases& from)
  : ::google::protobuf::Message(),
    _internal_metadata_(NULL) {
  SharedCtor();
  UnsafeMergeFrom(from);
  // @@protoc_insertion_point(copy_constructor:google.protobuf.testing.MapsTestCases)
}

void MapsTestCases::SharedCtor() {
  empty_map_ = NULL;
  string_to_int_ = NULL;
  int_to_string_ = NULL;
  mixed1_ = NULL;
  mixed2_ = NULL;
  map_of_objects_ = NULL;
  empty_key_string_to_int1_ = NULL;
  empty_key_string_to_int2_ = NULL;
  empty_key_string_to_int3_ = NULL;
  empty_key_bool_to_string_ = NULL;
  empty_key_int_to_string_ = NULL;
  empty_key_mixed_ = NULL;
  empty_key_map_objects_ = NULL;
  _cached_size_ = 0;
}

MapsTestCases::~MapsTestCases() {
  // @@protoc_insertion_point(destructor:google.protobuf.testing.MapsTestCases)
  SharedDtor();
}

void MapsTestCases::SharedDtor() {
  if (this != &MapsTestCases_default_instance_.get()) {
    delete empty_map_;
    delete string_to_int_;
    delete int_to_string_;
    delete mixed1_;
    delete mixed2_;
    delete map_of_objects_;
    delete empty_key_string_to_int1_;
    delete empty_key_string_to_int2_;
    delete empty_key_string_to_int3_;
    delete empty_key_bool_to_string_;
    delete empty_key_int_to_string_;
    delete empty_key_mixed_;
    delete empty_key_map_objects_;
  }
}

void MapsTestCases::SetCachedSize(int size) const {
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
}
const ::google::protobuf::Descriptor* MapsTestCases::descriptor() {
  protobuf_AssignDescriptorsOnce();
  return MapsTestCases_descriptor_;
}

const MapsTestCases& MapsTestCases::default_instance() {
  protobuf_InitDefaults_google_2fprotobuf_2futil_2finternal_2ftestdata_2fmaps_2eproto();
  return *internal_default_instance();
}

::google::protobuf::internal::ExplicitlyConstructed<MapsTestCases> MapsTestCases_default_instance_;

MapsTestCases* MapsTestCases::New(::google::protobuf::Arena* arena) const {
  MapsTestCases* n = new MapsTestCases;
  if (arena != NULL) {
    arena->Own(n);
  }
  return n;
}

void MapsTestCases::Clear() {
// @@protoc_insertion_point(message_clear_start:google.protobuf.testing.MapsTestCases)
  if (GetArenaNoVirtual() == NULL && empty_map_ != NULL) delete empty_map_;
  empty_map_ = NULL;
  if (GetArenaNoVirtual() == NULL && string_to_int_ != NULL) delete string_to_int_;
  string_to_int_ = NULL;
  if (GetArenaNoVirtual() == NULL && int_to_string_ != NULL) delete int_to_string_;
  int_to_string_ = NULL;
  if (GetArenaNoVirtual() == NULL && mixed1_ != NULL) delete mixed1_;
  mixed1_ = NULL;
  if (GetArenaNoVirtual() == NULL && mixed2_ != NULL) delete mixed2_;
  mixed2_ = NULL;
  if (GetArenaNoVirtual() == NULL && map_of_objects_ != NULL) delete map_of_objects_;
  map_of_objects_ = NULL;
  if (GetArenaNoVirtual() == NULL && empty_key_string_to_int1_ != NULL) delete empty_key_string_to_int1_;
  empty_key_string_to_int1_ = NULL;
  if (GetArenaNoVirtual() == NULL && empty_key_string_to_int2_ != NULL) delete empty_key_string_to_int2_;
  empty_key_string_to_int2_ = NULL;
  if (GetArenaNoVirtual() == NULL && empty_key_string_to_int3_ != NULL) delete empty_key_string_to_int3_;
  empty_key_string_to_int3_ = NULL;
  if (GetArenaNoVirtual() == NULL && empty_key_bool_to_string_ != NULL) delete empty_key_bool_to_string_;
  empty_key_bool_to_string_ = NULL;
  if (GetArenaNoVirtual() == NULL && empty_key_int_to_string_ != NULL) delete empty_key_int_to_string_;
  empty_key_int_to_string_ = NULL;
  if (GetArenaNoVirtual() == NULL && empty_key_mixed_ != NULL) delete empty_key_mixed_;
  empty_key_mixed_ = NULL;
  if (GetArenaNoVirtual() == NULL && empty_key_map_objects_ != NULL) delete empty_key_map_objects_;
  empty_key_map_objects_ = NULL;
}

bool MapsTestCases::MergePartialFromCodedStream(
    ::google::protobuf::io::CodedInputStream* input) {
#define DO_(EXPRESSION) if (!GOOGLE_PREDICT_TRUE(EXPRESSION)) goto failure
  ::google::protobuf::uint32 tag;
  // @@protoc_insertion_point(parse_start:google.protobuf.testing.MapsTestCases)
  for (;;) {
    ::std::pair< ::google::protobuf::uint32, bool> p = input->ReadTagWithCutoff(127);
    tag = p.first;
    if (!p.second) goto handle_unusual;
    switch (::google::protobuf::internal::WireFormatLite::GetTagFieldNumber(tag)) {
      // optional .google.protobuf.testing.EmptyMap empty_map = 1;
      case 1: {
        if (tag == 10) {
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessageNoVirtual(
               input, mutable_empty_map()));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(18)) goto parse_string_to_int;
        break;
      }

      // optional .google.protobuf.testing.StringtoInt string_to_int = 2;
      case 2: {
        if (tag == 18) {
         parse_string_to_int:
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessageNoVirtual(
               input, mutable_string_to_int()));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(26)) goto parse_int_to_string;
        break;
      }

      // optional .google.protobuf.testing.IntToString int_to_string = 3;
      case 3: {
        if (tag == 26) {
         parse_int_to_string:
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessageNoVirtual(
               input, mutable_int_to_string()));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(34)) goto parse_mixed1;
        break;
      }

      // optional .google.protobuf.testing.Mixed1 mixed1 = 4;
      case 4: {
        if (tag == 34) {
         parse_mixed1:
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessageNoVirtual(
               input, mutable_mixed1()));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(42)) goto parse_mixed2;
        break;
      }

      // optional .google.protobuf.testing.Mixed2 mixed2 = 5;
      case 5: {
        if (tag == 42) {
         parse_mixed2:
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessageNoVirtual(
               input, mutable_mixed2()));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(50)) goto parse_map_of_objects;
        break;
      }

      // optional .google.protobuf.testing.MapOfObjects map_of_objects = 6;
      case 6: {
        if (tag == 50) {
         parse_map_of_objects:
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessageNoVirtual(
               input, mutable_map_of_objects()));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(58)) goto parse_empty_key_string_to_int1;
        break;
      }

      // optional .google.protobuf.testing.StringtoInt empty_key_string_to_int1 = 7;
      case 7: {
        if (tag == 58) {
         parse_empty_key_string_to_int1:
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessageNoVirtual(
               input, mutable_empty_key_string_to_int1()));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(66)) goto parse_empty_key_string_to_int2;
        break;
      }

      // optional .google.protobuf.testing.StringtoInt empty_key_string_to_int2 = 8;
      case 8: {
        if (tag == 66) {
         parse_empty_key_string_to_int2:
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessageNoVirtual(
               input, mutable_empty_key_string_to_int2()));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(74)) goto parse_empty_key_string_to_int3;
        break;
      }

      // optional .google.protobuf.testing.StringtoInt empty_key_string_to_int3 = 9;
      case 9: {
        if (tag == 74) {
         parse_empty_key_string_to_int3:
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessageNoVirtual(
               input, mutable_empty_key_string_to_int3()));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(82)) goto parse_empty_key_bool_to_string;
        break;
      }

      // optional .google.protobuf.testing.BoolToString empty_key_bool_to_string = 10;
      case 10: {
        if (tag == 82) {
         parse_empty_key_bool_to_string:
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessageNoVirtual(
               input, mutable_empty_key_bool_to_string()));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(90)) goto parse_empty_key_int_to_string;
        break;
      }

      // optional .google.protobuf.testing.IntToString empty_key_int_to_string = 11;
      case 11: {
        if (tag == 90) {
         parse_empty_key_int_to_string:
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessageNoVirtual(
               input, mutable_empty_key_int_to_string()));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(98)) goto parse_empty_key_mixed;
        break;
      }

      // optional .google.protobuf.testing.Mixed1 empty_key_mixed = 12;
      case 12: {
        if (tag == 98) {
         parse_empty_key_mixed:
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessageNoVirtual(
               input, mutable_empty_key_mixed()));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(106)) goto parse_empty_key_map_objects;
        break;
      }

      // optional .google.protobuf.testing.MapOfObjects empty_key_map_objects = 13;
      case 13: {
        if (tag == 106) {
         parse_empty_key_map_objects:
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessageNoVirtual(
               input, mutable_empty_key_map_objects()));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectAtEnd()) goto success;
        break;
      }

      default: {
      handle_unusual:
        if (tag == 0 ||
            ::google::protobuf::internal::WireFormatLite::GetTagWireType(tag) ==
            ::google::protobuf::internal::WireFormatLite::WIRETYPE_END_GROUP) {
          goto success;
        }
        DO_(::google::protobuf::internal::WireFormatLite::SkipField(input, tag));
        break;
      }
    }
  }
success:
  // @@protoc_insertion_point(parse_success:google.protobuf.testing.MapsTestCases)
  return true;
failure:
  // @@protoc_insertion_point(parse_failure:google.protobuf.testing.MapsTestCases)
  return false;
#undef DO_
}

void MapsTestCases::SerializeWithCachedSizes(
    ::google::protobuf::io::CodedOutputStream* output) const {
  // @@protoc_insertion_point(serialize_start:google.protobuf.testing.MapsTestCases)
  // optional .google.protobuf.testing.EmptyMap empty_map = 1;
  if (this->has_empty_map()) {
    ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
      1, *this->empty_map_, output);
  }

  // optional .google.protobuf.testing.StringtoInt string_to_int = 2;
  if (this->has_string_to_int()) {
    ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
      2, *this->string_to_int_, output);
  }

  // optional .google.protobuf.testing.IntToString int_to_string = 3;
  if (this->has_int_to_string()) {
    ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
      3, *this->int_to_string_, output);
  }

  // optional .google.protobuf.testing.Mixed1 mixed1 = 4;
  if (this->has_mixed1()) {
    ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
      4, *this->mixed1_, output);
  }

  // optional .google.protobuf.testing.Mixed2 mixed2 = 5;
  if (this->has_mixed2()) {
    ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
      5, *this->mixed2_, output);
  }

  // optional .google.protobuf.testing.MapOfObjects map_of_objects = 6;
  if (this->has_map_of_objects()) {
    ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
      6, *this->map_of_objects_, output);
  }

  // optional .google.protobuf.testing.StringtoInt empty_key_string_to_int1 = 7;
  if (this->has_empty_key_string_to_int1()) {
    ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
      7, *this->empty_key_string_to_int1_, output);
  }

  // optional .google.protobuf.testing.StringtoInt empty_key_string_to_int2 = 8;
  if (this->has_empty_key_string_to_int2()) {
    ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
      8, *this->empty_key_string_to_int2_, output);
  }

  // optional .google.protobuf.testing.StringtoInt empty_key_string_to_int3 = 9;
  if (this->has_empty_key_string_to_int3()) {
    ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
      9, *this->empty_key_string_to_int3_, output);
  }

  // optional .google.protobuf.testing.BoolToString empty_key_bool_to_string = 10;
  if (this->has_empty_key_bool_to_string()) {
    ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
      10, *this->empty_key_bool_to_string_, output);
  }

  // optional .google.protobuf.testing.IntToString empty_key_int_to_string = 11;
  if (this->has_empty_key_int_to_string()) {
    ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
      11, *this->empty_key_int_to_string_, output);
  }

  // optional .google.protobuf.testing.Mixed1 empty_key_mixed = 12;
  if (this->has_empty_key_mixed()) {
    ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
      12, *this->empty_key_mixed_, output);
  }

  // optional .google.protobuf.testing.MapOfObjects empty_key_map_objects = 13;
  if (this->has_empty_key_map_objects()) {
    ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
      13, *this->empty_key_map_objects_, output);
  }

  // @@protoc_insertion_point(serialize_end:google.protobuf.testing.MapsTestCases)
}

::google::protobuf::uint8* MapsTestCases::InternalSerializeWithCachedSizesToArray(
    bool deterministic, ::google::protobuf::uint8* target) const {
  (void)deterministic; // Unused
  // @@protoc_insertion_point(serialize_to_array_start:google.protobuf.testing.MapsTestCases)
  // optional .google.protobuf.testing.EmptyMap empty_map = 1;
  if (this->has_empty_map()) {
    target = ::google::protobuf::internal::WireFormatLite::
      InternalWriteMessageNoVirtualToArray(
        1, *this->empty_map_, false, target);
  }

  // optional .google.protobuf.testing.StringtoInt string_to_int = 2;
  if (this->has_string_to_int()) {
    target = ::google::protobuf::internal::WireFormatLite::
      InternalWriteMessageNoVirtualToArray(
        2, *this->string_to_int_, false, target);
  }

  // optional .google.protobuf.testing.IntToString int_to_string = 3;
  if (this->has_int_to_string()) {
    target = ::google::protobuf::internal::WireFormatLite::
      InternalWriteMessageNoVirtualToArray(
        3, *this->int_to_string_, false, target);
  }

  // optional .google.protobuf.testing.Mixed1 mixed1 = 4;
  if (this->has_mixed1()) {
    target = ::google::protobuf::internal::WireFormatLite::
      InternalWriteMessageNoVirtualToArray(
        4, *this->mixed1_, false, target);
  }

  // optional .google.protobuf.testing.Mixed2 mixed2 = 5;
  if (this->has_mixed2()) {
    target = ::google::protobuf::internal::WireFormatLite::
      InternalWriteMessageNoVirtualToArray(
        5, *this->mixed2_, false, target);
  }

  // optional .google.protobuf.testing.MapOfObjects map_of_objects = 6;
  if (this->has_map_of_objects()) {
    target = ::google::protobuf::internal::WireFormatLite::
      InternalWriteMessageNoVirtualToArray(
        6, *this->map_of_objects_, false, target);
  }

  // optional .google.protobuf.testing.StringtoInt empty_key_string_to_int1 = 7;
  if (this->has_empty_key_string_to_int1()) {
    target = ::google::protobuf::internal::WireFormatLite::
      InternalWriteMessageNoVirtualToArray(
        7, *this->empty_key_string_to_int1_, false, target);
  }

  // optional .google.protobuf.testing.StringtoInt empty_key_string_to_int2 = 8;
  if (this->has_empty_key_string_to_int2()) {
    target = ::google::protobuf::internal::WireFormatLite::
      InternalWriteMessageNoVirtualToArray(
        8, *this->empty_key_string_to_int2_, false, target);
  }

  // optional .google.protobuf.testing.StringtoInt empty_key_string_to_int3 = 9;
  if (this->has_empty_key_string_to_int3()) {
    target = ::google::protobuf::internal::WireFormatLite::
      InternalWriteMessageNoVirtualToArray(
        9, *this->empty_key_string_to_int3_, false, target);
  }

  // optional .google.protobuf.testing.BoolToString empty_key_bool_to_string = 10;
  if (this->has_empty_key_bool_to_string()) {
    target = ::google::protobuf::internal::WireFormatLite::
      InternalWriteMessageNoVirtualToArray(
        10, *this->empty_key_bool_to_string_, false, target);
  }

  // optional .google.protobuf.testing.IntToString empty_key_int_to_string = 11;
  if (this->has_empty_key_int_to_string()) {
    target = ::google::protobuf::internal::WireFormatLite::
      InternalWriteMessageNoVirtualToArray(
        11, *this->empty_key_int_to_string_, false, target);
  }

  // optional .google.protobuf.testing.Mixed1 empty_key_mixed = 12;
  if (this->has_empty_key_mixed()) {
    target = ::google::protobuf::internal::WireFormatLite::
      InternalWriteMessageNoVirtualToArray(
        12, *this->empty_key_mixed_, false, target);
  }

  // optional .google.protobuf.testing.MapOfObjects empty_key_map_objects = 13;
  if (this->has_empty_key_map_objects()) {
    target = ::google::protobuf::internal::WireFormatLite::
      InternalWriteMessageNoVirtualToArray(
        13, *this->empty_key_map_objects_, false, target);
  }

  // @@protoc_insertion_point(serialize_to_array_end:google.protobuf.testing.MapsTestCases)
  return target;
}

size_t MapsTestCases::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:google.protobuf.testing.MapsTestCases)
  size_t total_size = 0;

  // optional .google.protobuf.testing.EmptyMap empty_map = 1;
  if (this->has_empty_map()) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::MessageSizeNoVirtual(
        *this->empty_map_);
  }

  // optional .google.protobuf.testing.StringtoInt string_to_int = 2;
  if (this->has_string_to_int()) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::MessageSizeNoVirtual(
        *this->string_to_int_);
  }

  // optional .google.protobuf.testing.IntToString int_to_string = 3;
  if (this->has_int_to_string()) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::MessageSizeNoVirtual(
        *this->int_to_string_);
  }

  // optional .google.protobuf.testing.Mixed1 mixed1 = 4;
  if (this->has_mixed1()) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::MessageSizeNoVirtual(
        *this->mixed1_);
  }

  // optional .google.protobuf.testing.Mixed2 mixed2 = 5;
  if (this->has_mixed2()) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::MessageSizeNoVirtual(
        *this->mixed2_);
  }

  // optional .google.protobuf.testing.MapOfObjects map_of_objects = 6;
  if (this->has_map_of_objects()) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::MessageSizeNoVirtual(
        *this->map_of_objects_);
  }

  // optional .google.protobuf.testing.StringtoInt empty_key_string_to_int1 = 7;
  if (this->has_empty_key_string_to_int1()) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::MessageSizeNoVirtual(
        *this->empty_key_string_to_int1_);
  }

  // optional .google.protobuf.testing.StringtoInt empty_key_string_to_int2 = 8;
  if (this->has_empty_key_string_to_int2()) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::MessageSizeNoVirtual(
        *this->empty_key_string_to_int2_);
  }

  // optional .google.protobuf.testing.StringtoInt empty_key_string_to_int3 = 9;
  if (this->has_empty_key_string_to_int3()) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::MessageSizeNoVirtual(
        *this->empty_key_string_to_int3_);
  }

  // optional .google.protobuf.testing.BoolToString empty_key_bool_to_string = 10;
  if (this->has_empty_key_bool_to_string()) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::MessageSizeNoVirtual(
        *this->empty_key_bool_to_string_);
  }

  // optional .google.protobuf.testing.IntToString empty_key_int_to_string = 11;
  if (this->has_empty_key_int_to_string()) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::MessageSizeNoVirtual(
        *this->empty_key_int_to_string_);
  }

  // optional .google.protobuf.testing.Mixed1 empty_key_mixed = 12;
  if (this->has_empty_key_mixed()) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::MessageSizeNoVirtual(
        *this->empty_key_mixed_);
  }

  // optional .google.protobuf.testing.MapOfObjects empty_key_map_objects = 13;
  if (this->has_empty_key_map_objects()) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::MessageSizeNoVirtual(
        *this->empty_key_map_objects_);
  }

  int cached_size = ::google::protobuf::internal::ToCachedSize(total_size);
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = cached_size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
  return total_size;
}

void MapsTestCases::MergeFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_merge_from_start:google.protobuf.testing.MapsTestCases)
  if (GOOGLE_PREDICT_FALSE(&from == this)) MergeFromFail(__LINE__);
  const MapsTestCases* source =
      ::google::protobuf::internal::DynamicCastToGenerated<const MapsTestCases>(
          &from);
  if (source == NULL) {
  // @@protoc_insertion_point(generalized_merge_from_cast_fail:google.protobuf.testing.MapsTestCases)
    ::google::protobuf::internal::ReflectionOps::Merge(from, this);
  } else {
  // @@protoc_insertion_point(generalized_merge_from_cast_success:google.protobuf.testing.MapsTestCases)
    UnsafeMergeFrom(*source);
  }
}

void MapsTestCases::MergeFrom(const MapsTestCases& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:google.protobuf.testing.MapsTestCases)
  if (GOOGLE_PREDICT_TRUE(&from != this)) {
    UnsafeMergeFrom(from);
  } else {
    MergeFromFail(__LINE__);
  }
}

void MapsTestCases::UnsafeMergeFrom(const MapsTestCases& from) {
  GOOGLE_DCHECK(&from != this);
  if (from.has_empty_map()) {
    mutable_empty_map()->::google::protobuf::testing::EmptyMap::MergeFrom(from.empty_map());
  }
  if (from.has_string_to_int()) {
    mutable_string_to_int()->::google::protobuf::testing::StringtoInt::MergeFrom(from.string_to_int());
  }
  if (from.has_int_to_string()) {
    mutable_int_to_string()->::google::protobuf::testing::IntToString::MergeFrom(from.int_to_string());
  }
  if (from.has_mixed1()) {
    mutable_mixed1()->::google::protobuf::testing::Mixed1::MergeFrom(from.mixed1());
  }
  if (from.has_mixed2()) {
    mutable_mixed2()->::google::protobuf::testing::Mixed2::MergeFrom(from.mixed2());
  }
  if (from.has_map_of_objects()) {
    mutable_map_of_objects()->::google::protobuf::testing::MapOfObjects::MergeFrom(from.map_of_objects());
  }
  if (from.has_empty_key_string_to_int1()) {
    mutable_empty_key_string_to_int1()->::google::protobuf::testing::StringtoInt::MergeFrom(from.empty_key_string_to_int1());
  }
  if (from.has_empty_key_string_to_int2()) {
    mutable_empty_key_string_to_int2()->::google::protobuf::testing::StringtoInt::MergeFrom(from.empty_key_string_to_int2());
  }
  if (from.has_empty_key_string_to_int3()) {
    mutable_empty_key_string_to_int3()->::google::protobuf::testing::StringtoInt::MergeFrom(from.empty_key_string_to_int3());
  }
  if (from.has_empty_key_bool_to_string()) {
    mutable_empty_key_bool_to_string()->::google::protobuf::testing::BoolToString::MergeFrom(from.empty_key_bool_to_string());
  }
  if (from.has_empty_key_int_to_string()) {
    mutable_empty_key_int_to_string()->::google::protobuf::testing::IntToString::MergeFrom(from.empty_key_int_to_string());
  }
  if (from.has_empty_key_mixed()) {
    mutable_empty_key_mixed()->::google::protobuf::testing::Mixed1::MergeFrom(from.empty_key_mixed());
  }
  if (from.has_empty_key_map_objects()) {
    mutable_empty_key_map_objects()->::google::protobuf::testing::MapOfObjects::MergeFrom(from.empty_key_map_objects());
  }
}

void MapsTestCases::CopyFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_copy_from_start:google.protobuf.testing.MapsTestCases)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

void MapsTestCases::CopyFrom(const MapsTestCases& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:google.protobuf.testing.MapsTestCases)
  if (&from == this) return;
  Clear();
  UnsafeMergeFrom(from);
}

bool MapsTestCases::IsInitialized() const {

  return true;
}

void MapsTestCases::Swap(MapsTestCases* other) {
  if (other == this) return;
  InternalSwap(other);
}
void MapsTestCases::InternalSwap(MapsTestCases* other) {
  std::swap(empty_map_, other->empty_map_);
  std::swap(string_to_int_, other->string_to_int_);
  std::swap(int_to_string_, other->int_to_string_);
  std::swap(mixed1_, other->mixed1_);
  std::swap(mixed2_, other->mixed2_);
  std::swap(map_of_objects_, other->map_of_objects_);
  std::swap(empty_key_string_to_int1_, other->empty_key_string_to_int1_);
  std::swap(empty_key_string_to_int2_, other->empty_key_string_to_int2_);
  std::swap(empty_key_string_to_int3_, other->empty_key_string_to_int3_);
  std::swap(empty_key_bool_to_string_, other->empty_key_bool_to_string_);
  std::swap(empty_key_int_to_string_, other->empty_key_int_to_string_);
  std::swap(empty_key_mixed_, other->empty_key_mixed_);
  std::swap(empty_key_map_objects_, other->empty_key_map_objects_);
  _internal_metadata_.Swap(&other->_internal_metadata_);
  std::swap(_cached_size_, other->_cached_size_);
}

::google::protobuf::Metadata MapsTestCases::GetMetadata() const {
  protobuf_AssignDescriptorsOnce();
  ::google::protobuf::Metadata metadata;
  metadata.descriptor = MapsTestCases_descriptor_;
  metadata.reflection = MapsTestCases_reflection_;
  return metadata;
}

#if PROTOBUF_INLINE_NOT_IN_HEADERS
// MapsTestCases

// optional .google.protobuf.testing.EmptyMap empty_map = 1;
bool MapsTestCases::has_empty_map() const {
  return this != internal_default_instance() && empty_map_ != NULL;
}
void MapsTestCases::clear_empty_map() {
  if (GetArenaNoVirtual() == NULL && empty_map_ != NULL) delete empty_map_;
  empty_map_ = NULL;
}
const ::google::protobuf::testing::EmptyMap& MapsTestCases::empty_map() const {
  // @@protoc_insertion_point(field_get:google.protobuf.testing.MapsTestCases.empty_map)
  return empty_map_ != NULL ? *empty_map_
                         : *::google::protobuf::testing::EmptyMap::internal_default_instance();
}
::google::protobuf::testing::EmptyMap* MapsTestCases::mutable_empty_map() {
  
  if (empty_map_ == NULL) {
    empty_map_ = new ::google::protobuf::testing::EmptyMap;
  }
  // @@protoc_insertion_point(field_mutable:google.protobuf.testing.MapsTestCases.empty_map)
  return empty_map_;
}
::google::protobuf::testing::EmptyMap* MapsTestCases::release_empty_map() {
  // @@protoc_insertion_point(field_release:google.protobuf.testing.MapsTestCases.empty_map)
  
  ::google::protobuf::testing::EmptyMap* temp = empty_map_;
  empty_map_ = NULL;
  return temp;
}
void MapsTestCases::set_allocated_empty_map(::google::protobuf::testing::EmptyMap* empty_map) {
  delete empty_map_;
  empty_map_ = empty_map;
  if (empty_map) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_set_allocated:google.protobuf.testing.MapsTestCases.empty_map)
}

// optional .google.protobuf.testing.StringtoInt string_to_int = 2;
bool MapsTestCases::has_string_to_int() const {
  return this != internal_default_instance() && string_to_int_ != NULL;
}
void MapsTestCases::clear_string_to_int() {
  if (GetArenaNoVirtual() == NULL && string_to_int_ != NULL) delete string_to_int_;
  string_to_int_ = NULL;
}
const ::google::protobuf::testing::StringtoInt& MapsTestCases::string_to_int() const {
  // @@protoc_insertion_point(field_get:google.protobuf.testing.MapsTestCases.string_to_int)
  return string_to_int_ != NULL ? *string_to_int_
                         : *::google::protobuf::testing::StringtoInt::internal_default_instance();
}
::google::protobuf::testing::StringtoInt* MapsTestCases::mutable_string_to_int() {
  
  if (string_to_int_ == NULL) {
    string_to_int_ = new ::google::protobuf::testing::StringtoInt;
  }
  // @@protoc_insertion_point(field_mutable:google.protobuf.testing.MapsTestCases.string_to_int)
  return string_to_int_;
}
::google::protobuf::testing::StringtoInt* MapsTestCases::release_string_to_int() {
  // @@protoc_insertion_point(field_release:google.protobuf.testing.MapsTestCases.string_to_int)
  
  ::google::protobuf::testing::StringtoInt* temp = string_to_int_;
  string_to_int_ = NULL;
  return temp;
}
void MapsTestCases::set_allocated_string_to_int(::google::protobuf::testing::StringtoInt* string_to_int) {
  delete string_to_int_;
  string_to_int_ = string_to_int;
  if (string_to_int) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_set_allocated:google.protobuf.testing.MapsTestCases.string_to_int)
}

// optional .google.protobuf.testing.IntToString int_to_string = 3;
bool MapsTestCases::has_int_to_string() const {
  return this != internal_default_instance() && int_to_string_ != NULL;
}
void MapsTestCases::clear_int_to_string() {
  if (GetArenaNoVirtual() == NULL && int_to_string_ != NULL) delete int_to_string_;
  int_to_string_ = NULL;
}
const ::google::protobuf::testing::IntToString& MapsTestCases::int_to_string() const {
  // @@protoc_insertion_point(field_get:google.protobuf.testing.MapsTestCases.int_to_string)
  return int_to_string_ != NULL ? *int_to_string_
                         : *::google::protobuf::testing::IntToString::internal_default_instance();
}
::google::protobuf::testing::IntToString* MapsTestCases::mutable_int_to_string() {
  
  if (int_to_string_ == NULL) {
    int_to_string_ = new ::google::protobuf::testing::IntToString;
  }
  // @@protoc_insertion_point(field_mutable:google.protobuf.testing.MapsTestCases.int_to_string)
  return int_to_string_;
}
::google::protobuf::testing::IntToString* MapsTestCases::release_int_to_string() {
  // @@protoc_insertion_point(field_release:google.protobuf.testing.MapsTestCases.int_to_string)
  
  ::google::protobuf::testing::IntToString* temp = int_to_string_;
  int_to_string_ = NULL;
  return temp;
}
void MapsTestCases::set_allocated_int_to_string(::google::protobuf::testing::IntToString* int_to_string) {
  delete int_to_string_;
  int_to_string_ = int_to_string;
  if (int_to_string) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_set_allocated:google.protobuf.testing.MapsTestCases.int_to_string)
}

// optional .google.protobuf.testing.Mixed1 mixed1 = 4;
bool MapsTestCases::has_mixed1() const {
  return this != internal_default_instance() && mixed1_ != NULL;
}
void MapsTestCases::clear_mixed1() {
  if (GetArenaNoVirtual() == NULL && mixed1_ != NULL) delete mixed1_;
  mixed1_ = NULL;
}
const ::google::protobuf::testing::Mixed1& MapsTestCases::mixed1() const {
  // @@protoc_insertion_point(field_get:google.protobuf.testing.MapsTestCases.mixed1)
  return mixed1_ != NULL ? *mixed1_
                         : *::google::protobuf::testing::Mixed1::internal_default_instance();
}
::google::protobuf::testing::Mixed1* MapsTestCases::mutable_mixed1() {
  
  if (mixed1_ == NULL) {
    mixed1_ = new ::google::protobuf::testing::Mixed1;
  }
  // @@protoc_insertion_point(field_mutable:google.protobuf.testing.MapsTestCases.mixed1)
  return mixed1_;
}
::google::protobuf::testing::Mixed1* MapsTestCases::release_mixed1() {
  // @@protoc_insertion_point(field_release:google.protobuf.testing.MapsTestCases.mixed1)
  
  ::google::protobuf::testing::Mixed1* temp = mixed1_;
  mixed1_ = NULL;
  return temp;
}
void MapsTestCases::set_allocated_mixed1(::google::protobuf::testing::Mixed1* mixed1) {
  delete mixed1_;
  mixed1_ = mixed1;
  if (mixed1) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_set_allocated:google.protobuf.testing.MapsTestCases.mixed1)
}

// optional .google.protobuf.testing.Mixed2 mixed2 = 5;
bool MapsTestCases::has_mixed2() const {
  return this != internal_default_instance() && mixed2_ != NULL;
}
void MapsTestCases::clear_mixed2() {
  if (GetArenaNoVirtual() == NULL && mixed2_ != NULL) delete mixed2_;
  mixed2_ = NULL;
}
const ::google::protobuf::testing::Mixed2& MapsTestCases::mixed2() const {
  // @@protoc_insertion_point(field_get:google.protobuf.testing.MapsTestCases.mixed2)
  return mixed2_ != NULL ? *mixed2_
                         : *::google::protobuf::testing::Mixed2::internal_default_instance();
}
::google::protobuf::testing::Mixed2* MapsTestCases::mutable_mixed2() {
  
  if (mixed2_ == NULL) {
    mixed2_ = new ::google::protobuf::testing::Mixed2;
  }
  // @@protoc_insertion_point(field_mutable:google.protobuf.testing.MapsTestCases.mixed2)
  return mixed2_;
}
::google::protobuf::testing::Mixed2* MapsTestCases::release_mixed2() {
  // @@protoc_insertion_point(field_release:google.protobuf.testing.MapsTestCases.mixed2)
  
  ::google::protobuf::testing::Mixed2* temp = mixed2_;
  mixed2_ = NULL;
  return temp;
}
void MapsTestCases::set_allocated_mixed2(::google::protobuf::testing::Mixed2* mixed2) {
  delete mixed2_;
  mixed2_ = mixed2;
  if (mixed2) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_set_allocated:google.protobuf.testing.MapsTestCases.mixed2)
}

// optional .google.protobuf.testing.MapOfObjects map_of_objects = 6;
bool MapsTestCases::has_map_of_objects() const {
  return this != internal_default_instance() && map_of_objects_ != NULL;
}
void MapsTestCases::clear_map_of_objects() {
  if (GetArenaNoVirtual() == NULL && map_of_objects_ != NULL) delete map_of_objects_;
  map_of_objects_ = NULL;
}
const ::google::protobuf::testing::MapOfObjects& MapsTestCases::map_of_objects() const {
  // @@protoc_insertion_point(field_get:google.protobuf.testing.MapsTestCases.map_of_objects)
  return map_of_objects_ != NULL ? *map_of_objects_
                         : *::google::protobuf::testing::MapOfObjects::internal_default_instance();
}
::google::protobuf::testing::MapOfObjects* MapsTestCases::mutable_map_of_objects() {
  
  if (map_of_objects_ == NULL) {
    map_of_objects_ = new ::google::protobuf::testing::MapOfObjects;
  }
  // @@protoc_insertion_point(field_mutable:google.protobuf.testing.MapsTestCases.map_of_objects)
  return map_of_objects_;
}
::google::protobuf::testing::MapOfObjects* MapsTestCases::release_map_of_objects() {
  // @@protoc_insertion_point(field_release:google.protobuf.testing.MapsTestCases.map_of_objects)
  
  ::google::protobuf::testing::MapOfObjects* temp = map_of_objects_;
  map_of_objects_ = NULL;
  return temp;
}
void MapsTestCases::set_allocated_map_of_objects(::google::protobuf::testing::MapOfObjects* map_of_objects) {
  delete map_of_objects_;
  map_of_objects_ = map_of_objects;
  if (map_of_objects) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_set_allocated:google.protobuf.testing.MapsTestCases.map_of_objects)
}

// optional .google.protobuf.testing.StringtoInt empty_key_string_to_int1 = 7;
bool MapsTestCases::has_empty_key_string_to_int1() const {
  return this != internal_default_instance() && empty_key_string_to_int1_ != NULL;
}
void MapsTestCases::clear_empty_key_string_to_int1() {
  if (GetArenaNoVirtual() == NULL && empty_key_string_to_int1_ != NULL) delete empty_key_string_to_int1_;
  empty_key_string_to_int1_ = NULL;
}
const ::google::protobuf::testing::StringtoInt& MapsTestCases::empty_key_string_to_int1() const {
  // @@protoc_insertion_point(field_get:google.protobuf.testing.MapsTestCases.empty_key_string_to_int1)
  return empty_key_string_to_int1_ != NULL ? *empty_key_string_to_int1_
                         : *::google::protobuf::testing::StringtoInt::internal_default_instance();
}
::google::protobuf::testing::StringtoInt* MapsTestCases::mutable_empty_key_string_to_int1() {
  
  if (empty_key_string_to_int1_ == NULL) {
    empty_key_string_to_int1_ = new ::google::protobuf::testing::StringtoInt;
  }
  // @@protoc_insertion_point(field_mutable:google.protobuf.testing.MapsTestCases.empty_key_string_to_int1)
  return empty_key_string_to_int1_;
}
::google::protobuf::testing::StringtoInt* MapsTestCases::release_empty_key_string_to_int1() {
  // @@protoc_insertion_point(field_release:google.protobuf.testing.MapsTestCases.empty_key_string_to_int1)
  
  ::google::protobuf::testing::StringtoInt* temp = empty_key_string_to_int1_;
  empty_key_string_to_int1_ = NULL;
  return temp;
}
void MapsTestCases::set_allocated_empty_key_string_to_int1(::google::protobuf::testing::StringtoInt* empty_key_string_to_int1) {
  delete empty_key_string_to_int1_;
  empty_key_string_to_int1_ = empty_key_string_to_int1;
  if (empty_key_string_to_int1) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_set_allocated:google.protobuf.testing.MapsTestCases.empty_key_string_to_int1)
}

// optional .google.protobuf.testing.StringtoInt empty_key_string_to_int2 = 8;
bool MapsTestCases::has_empty_key_string_to_int2() const {
  return this != internal_default_instance() && empty_key_string_to_int2_ != NULL;
}
void MapsTestCases::clear_empty_key_string_to_int2() {
  if (GetArenaNoVirtual() == NULL && empty_key_string_to_int2_ != NULL) delete empty_key_string_to_int2_;
  empty_key_string_to_int2_ = NULL;
}
const ::google::protobuf::testing::StringtoInt& MapsTestCases::empty_key_string_to_int2() const {
  // @@protoc_insertion_point(field_get:google.protobuf.testing.MapsTestCases.empty_key_string_to_int2)
  return empty_key_string_to_int2_ != NULL ? *empty_key_string_to_int2_
                         : *::google::protobuf::testing::StringtoInt::internal_default_instance();
}
::google::protobuf::testing::StringtoInt* MapsTestCases::mutable_empty_key_string_to_int2() {
  
  if (empty_key_string_to_int2_ == NULL) {
    empty_key_string_to_int2_ = new ::google::protobuf::testing::StringtoInt;
  }
  // @@protoc_insertion_point(field_mutable:google.protobuf.testing.MapsTestCases.empty_key_string_to_int2)
  return empty_key_string_to_int2_;
}
::google::protobuf::testing::StringtoInt* MapsTestCases::release_empty_key_string_to_int2() {
  // @@protoc_insertion_point(field_release:google.protobuf.testing.MapsTestCases.empty_key_string_to_int2)
  
  ::google::protobuf::testing::StringtoInt* temp = empty_key_string_to_int2_;
  empty_key_string_to_int2_ = NULL;
  return temp;
}
void MapsTestCases::set_allocated_empty_key_string_to_int2(::google::protobuf::testing::StringtoInt* empty_key_string_to_int2) {
  delete empty_key_string_to_int2_;
  empty_key_string_to_int2_ = empty_key_string_to_int2;
  if (empty_key_string_to_int2) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_set_allocated:google.protobuf.testing.MapsTestCases.empty_key_string_to_int2)
}

// optional .google.protobuf.testing.StringtoInt empty_key_string_to_int3 = 9;
bool MapsTestCases::has_empty_key_string_to_int3() const {
  return this != internal_default_instance() && empty_key_string_to_int3_ != NULL;
}
void MapsTestCases::clear_empty_key_string_to_int3() {
  if (GetArenaNoVirtual() == NULL && empty_key_string_to_int3_ != NULL) delete empty_key_string_to_int3_;
  empty_key_string_to_int3_ = NULL;
}
const ::google::protobuf::testing::StringtoInt& MapsTestCases::empty_key_string_to_int3() const {
  // @@protoc_insertion_point(field_get:google.protobuf.testing.MapsTestCases.empty_key_string_to_int3)
  return empty_key_string_to_int3_ != NULL ? *empty_key_string_to_int3_
                         : *::google::protobuf::testing::StringtoInt::internal_default_instance();
}
::google::protobuf::testing::StringtoInt* MapsTestCases::mutable_empty_key_string_to_int3() {
  
  if (empty_key_string_to_int3_ == NULL) {
    empty_key_string_to_int3_ = new ::google::protobuf::testing::StringtoInt;
  }
  // @@protoc_insertion_point(field_mutable:google.protobuf.testing.MapsTestCases.empty_key_string_to_int3)
  return empty_key_string_to_int3_;
}
::google::protobuf::testing::StringtoInt* MapsTestCases::release_empty_key_string_to_int3() {
  // @@protoc_insertion_point(field_release:google.protobuf.testing.MapsTestCases.empty_key_string_to_int3)
  
  ::google::protobuf::testing::StringtoInt* temp = empty_key_string_to_int3_;
  empty_key_string_to_int3_ = NULL;
  return temp;
}
void MapsTestCases::set_allocated_empty_key_string_to_int3(::google::protobuf::testing::StringtoInt* empty_key_string_to_int3) {
  delete empty_key_string_to_int3_;
  empty_key_string_to_int3_ = empty_key_string_to_int3;
  if (empty_key_string_to_int3) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_set_allocated:google.protobuf.testing.MapsTestCases.empty_key_string_to_int3)
}

// optional .google.protobuf.testing.BoolToString empty_key_bool_to_string = 10;
bool MapsTestCases::has_empty_key_bool_to_string() const {
  return this != internal_default_instance() && empty_key_bool_to_string_ != NULL;
}
void MapsTestCases::clear_empty_key_bool_to_string() {
  if (GetArenaNoVirtual() == NULL && empty_key_bool_to_string_ != NULL) delete empty_key_bool_to_string_;
  empty_key_bool_to_string_ = NULL;
}
const ::google::protobuf::testing::BoolToString& MapsTestCases::empty_key_bool_to_string() const {
  // @@protoc_insertion_point(field_get:google.protobuf.testing.MapsTestCases.empty_key_bool_to_string)
  return empty_key_bool_to_string_ != NULL ? *empty_key_bool_to_string_
                         : *::google::protobuf::testing::BoolToString::internal_default_instance();
}
::google::protobuf::testing::BoolToString* MapsTestCases::mutable_empty_key_bool_to_string() {
  
  if (empty_key_bool_to_string_ == NULL) {
    empty_key_bool_to_string_ = new ::google::protobuf::testing::BoolToString;
  }
  // @@protoc_insertion_point(field_mutable:google.protobuf.testing.MapsTestCases.empty_key_bool_to_string)
  return empty_key_bool_to_string_;
}
::google::protobuf::testing::BoolToString* MapsTestCases::release_empty_key_bool_to_string() {
  // @@protoc_insertion_point(field_release:google.protobuf.testing.MapsTestCases.empty_key_bool_to_string)
  
  ::google::protobuf::testing::BoolToString* temp = empty_key_bool_to_string_;
  empty_key_bool_to_string_ = NULL;
  return temp;
}
void MapsTestCases::set_allocated_empty_key_bool_to_string(::google::protobuf::testing::BoolToString* empty_key_bool_to_string) {
  delete empty_key_bool_to_string_;
  empty_key_bool_to_string_ = empty_key_bool_to_string;
  if (empty_key_bool_to_string) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_set_allocated:google.protobuf.testing.MapsTestCases.empty_key_bool_to_string)
}

// optional .google.protobuf.testing.IntToString empty_key_int_to_string = 11;
bool MapsTestCases::has_empty_key_int_to_string() const {
  return this != internal_default_instance() && empty_key_int_to_string_ != NULL;
}
void MapsTestCases::clear_empty_key_int_to_string() {
  if (GetArenaNoVirtual() == NULL && empty_key_int_to_string_ != NULL) delete empty_key_int_to_string_;
  empty_key_int_to_string_ = NULL;
}
const ::google::protobuf::testing::IntToString& MapsTestCases::empty_key_int_to_string() const {
  // @@protoc_insertion_point(field_get:google.protobuf.testing.MapsTestCases.empty_key_int_to_string)
  return empty_key_int_to_string_ != NULL ? *empty_key_int_to_string_
                         : *::google::protobuf::testing::IntToString::internal_default_instance();
}
::google::protobuf::testing::IntToString* MapsTestCases::mutable_empty_key_int_to_string() {
  
  if (empty_key_int_to_string_ == NULL) {
    empty_key_int_to_string_ = new ::google::protobuf::testing::IntToString;
  }
  // @@protoc_insertion_point(field_mutable:google.protobuf.testing.MapsTestCases.empty_key_int_to_string)
  return empty_key_int_to_string_;
}
::google::protobuf::testing::IntToString* MapsTestCases::release_empty_key_int_to_string() {
  // @@protoc_insertion_point(field_release:google.protobuf.testing.MapsTestCases.empty_key_int_to_string)
  
  ::google::protobuf::testing::IntToString* temp = empty_key_int_to_string_;
  empty_key_int_to_string_ = NULL;
  return temp;
}
void MapsTestCases::set_allocated_empty_key_int_to_string(::google::protobuf::testing::IntToString* empty_key_int_to_string) {
  delete empty_key_int_to_string_;
  empty_key_int_to_string_ = empty_key_int_to_string;
  if (empty_key_int_to_string) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_set_allocated:google.protobuf.testing.MapsTestCases.empty_key_int_to_string)
}

// optional .google.protobuf.testing.Mixed1 empty_key_mixed = 12;
bool MapsTestCases::has_empty_key_mixed() const {
  return this != internal_default_instance() && empty_key_mixed_ != NULL;
}
void MapsTestCases::clear_empty_key_mixed() {
  if (GetArenaNoVirtual() == NULL && empty_key_mixed_ != NULL) delete empty_key_mixed_;
  empty_key_mixed_ = NULL;
}
const ::google::protobuf::testing::Mixed1& MapsTestCases::empty_key_mixed() const {
  // @@protoc_insertion_point(field_get:google.protobuf.testing.MapsTestCases.empty_key_mixed)
  return empty_key_mixed_ != NULL ? *empty_key_mixed_
                         : *::google::protobuf::testing::Mixed1::internal_default_instance();
}
::google::protobuf::testing::Mixed1* MapsTestCases::mutable_empty_key_mixed() {
  
  if (empty_key_mixed_ == NULL) {
    empty_key_mixed_ = new ::google::protobuf::testing::Mixed1;
  }
  // @@protoc_insertion_point(field_mutable:google.protobuf.testing.MapsTestCases.empty_key_mixed)
  return empty_key_mixed_;
}
::google::protobuf::testing::Mixed1* MapsTestCases::release_empty_key_mixed() {
  // @@protoc_insertion_point(field_release:google.protobuf.testing.MapsTestCases.empty_key_mixed)
  
  ::google::protobuf::testing::Mixed1* temp = empty_key_mixed_;
  empty_key_mixed_ = NULL;
  return temp;
}
void MapsTestCases::set_allocated_empty_key_mixed(::google::protobuf::testing::Mixed1* empty_key_mixed) {
  delete empty_key_mixed_;
  empty_key_mixed_ = empty_key_mixed;
  if (empty_key_mixed) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_set_allocated:google.protobuf.testing.MapsTestCases.empty_key_mixed)
}

// optional .google.protobuf.testing.MapOfObjects empty_key_map_objects = 13;
bool MapsTestCases::has_empty_key_map_objects() const {
  return this != internal_default_instance() && empty_key_map_objects_ != NULL;
}
void MapsTestCases::clear_empty_key_map_objects() {
  if (GetArenaNoVirtual() == NULL && empty_key_map_objects_ != NULL) delete empty_key_map_objects_;
  empty_key_map_objects_ = NULL;
}
const ::google::protobuf::testing::MapOfObjects& MapsTestCases::empty_key_map_objects() const {
  // @@protoc_insertion_point(field_get:google.protobuf.testing.MapsTestCases.empty_key_map_objects)
  return empty_key_map_objects_ != NULL ? *empty_key_map_objects_
                         : *::google::protobuf::testing::MapOfObjects::internal_default_instance();
}
::google::protobuf::testing::MapOfObjects* MapsTestCases::mutable_empty_key_map_objects() {
  
  if (empty_key_map_objects_ == NULL) {
    empty_key_map_objects_ = new ::google::protobuf::testing::MapOfObjects;
  }
  // @@protoc_insertion_point(field_mutable:google.protobuf.testing.MapsTestCases.empty_key_map_objects)
  return empty_key_map_objects_;
}
::google::protobuf::testing::MapOfObjects* MapsTestCases::release_empty_key_map_objects() {
  // @@protoc_insertion_point(field_release:google.protobuf.testing.MapsTestCases.empty_key_map_objects)
  
  ::google::protobuf::testing::MapOfObjects* temp = empty_key_map_objects_;
  empty_key_map_objects_ = NULL;
  return temp;
}
void MapsTestCases::set_allocated_empty_key_map_objects(::google::protobuf::testing::MapOfObjects* empty_key_map_objects) {
  delete empty_key_map_objects_;
  empty_key_map_objects_ = empty_key_map_objects;
  if (empty_key_map_objects) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_set_allocated:google.protobuf.testing.MapsTestCases.empty_key_map_objects)
}

inline const MapsTestCases* MapsTestCases::internal_default_instance() {
  return &MapsTestCases_default_instance_.get();
}
#endif  // PROTOBUF_INLINE_NOT_IN_HEADERS

// ===================================================================

#if !defined(_MSC_VER) || _MSC_VER >= 1900
const int EmptyMap::kMapFieldNumber;
#endif  // !defined(_MSC_VER) || _MSC_VER >= 1900

EmptyMap::EmptyMap()
  : ::google::protobuf::Message(), _internal_metadata_(NULL) {
  if (this != internal_default_instance()) protobuf_InitDefaults_google_2fprotobuf_2futil_2finternal_2ftestdata_2fmaps_2eproto();
  SharedCtor();
  // @@protoc_insertion_point(constructor:google.protobuf.testing.EmptyMap)
}

void EmptyMap::InitAsDefaultInstance() {
}

EmptyMap::EmptyMap(const EmptyMap& from)
  : ::google::protobuf::Message(),
    _internal_metadata_(NULL) {
  SharedCtor();
  UnsafeMergeFrom(from);
  // @@protoc_insertion_point(copy_constructor:google.protobuf.testing.EmptyMap)
}

void EmptyMap::SharedCtor() {
  map_.SetAssignDescriptorCallback(
      protobuf_AssignDescriptorsOnce);
  map_.SetEntryDescriptor(
      &::google::protobuf::testing::EmptyMap_MapEntry_descriptor_);
  _cached_size_ = 0;
}

EmptyMap::~EmptyMap() {
  // @@protoc_insertion_point(destructor:google.protobuf.testing.EmptyMap)
  SharedDtor();
}

void EmptyMap::SharedDtor() {
}

void EmptyMap::SetCachedSize(int size) const {
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
}
const ::google::protobuf::Descriptor* EmptyMap::descriptor() {
  protobuf_AssignDescriptorsOnce();
  return EmptyMap_descriptor_;
}

const EmptyMap& EmptyMap::default_instance() {
  protobuf_InitDefaults_google_2fprotobuf_2futil_2finternal_2ftestdata_2fmaps_2eproto();
  return *internal_default_instance();
}

::google::protobuf::internal::ExplicitlyConstructed<EmptyMap> EmptyMap_default_instance_;

EmptyMap* EmptyMap::New(::google::protobuf::Arena* arena) const {
  EmptyMap* n = new EmptyMap;
  if (arena != NULL) {
    arena->Own(n);
  }
  return n;
}

void EmptyMap::Clear() {
// @@protoc_insertion_point(message_clear_start:google.protobuf.testing.EmptyMap)
  map_.Clear();
}

bool EmptyMap::MergePartialFromCodedStream(
    ::google::protobuf::io::CodedInputStream* input) {
#define DO_(EXPRESSION) if (!GOOGLE_PREDICT_TRUE(EXPRESSION)) goto failure
  ::google::protobuf::uint32 tag;
  // @@protoc_insertion_point(parse_start:google.protobuf.testing.EmptyMap)
  for (;;) {
    ::std::pair< ::google::protobuf::uint32, bool> p = input->ReadTagWithCutoff(127);
    tag = p.first;
    if (!p.second) goto handle_unusual;
    switch (::google::protobuf::internal::WireFormatLite::GetTagFieldNumber(tag)) {
      // map<int32, int32> map = 1;
      case 1: {
        if (tag == 10) {
          DO_(input->IncrementRecursionDepth());
         parse_loop_map:
          EmptyMap_MapEntry::Parser< ::google::protobuf::internal::MapField<
              ::google::protobuf::int32, ::google::protobuf::int32,
              ::google::protobuf::internal::WireFormatLite::TYPE_INT32,
              ::google::protobuf::internal::WireFormatLite::TYPE_INT32,
              0 >,
            ::google::protobuf::Map< ::google::protobuf::int32, ::google::protobuf::int32 > > parser(&map_);
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessageNoVirtual(
              input, &parser));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(10)) goto parse_loop_map;
        input->UnsafeDecrementRecursionDepth();
        if (input->ExpectAtEnd()) goto success;
        break;
      }

      default: {
      handle_unusual:
        if (tag == 0 ||
            ::google::protobuf::internal::WireFormatLite::GetTagWireType(tag) ==
            ::google::protobuf::internal::WireFormatLite::WIRETYPE_END_GROUP) {
          goto success;
        }
        DO_(::google::protobuf::internal::WireFormatLite::SkipField(input, tag));
        break;
      }
    }
  }
success:
  // @@protoc_insertion_point(parse_success:google.protobuf.testing.EmptyMap)
  return true;
failure:
  // @@protoc_insertion_point(parse_failure:google.protobuf.testing.EmptyMap)
  return false;
#undef DO_
}

void EmptyMap::SerializeWithCachedSizes(
    ::google::protobuf::io::CodedOutputStream* output) const {
  // @@protoc_insertion_point(serialize_start:google.protobuf.testing.EmptyMap)
  // map<int32, int32> map = 1;
  if (!this->map().empty()) {
    typedef ::google::protobuf::Map< ::google::protobuf::int32, ::google::protobuf::int32 >::const_pointer
        ConstPtr;
    typedef ::google::protobuf::internal::SortItem< ::google::protobuf::int32, ConstPtr > SortItem;
    typedef ::google::protobuf::internal::CompareByFirstField<SortItem> Less;

    if (output->IsSerializationDeterminstic() &&
        this->map().size() > 1) {
      ::google::protobuf::scoped_array<SortItem> items(
          new SortItem[this->map().size()]);
      typedef ::google::protobuf::Map< ::google::protobuf::int32, ::google::protobuf::int32 >::size_type size_type;
      size_type n = 0;
      for (::google::protobuf::Map< ::google::protobuf::int32, ::google::protobuf::int32 >::const_iterator
          it = this->map().begin();
          it != this->map().end(); ++it, ++n) {
        items[n] = SortItem(&*it);
      }
      ::std::sort(&items[0], &items[n], Less());
      ::google::protobuf::scoped_ptr<EmptyMap_MapEntry> entry;
      for (size_type i = 0; i < n; i++) {
        entry.reset(map_.NewEntryWrapper(
            items[i].second->first, items[i].second->second));
        ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
            1, *entry, output);
      }
    } else {
      ::google::protobuf::scoped_ptr<EmptyMap_MapEntry> entry;
      for (::google::protobuf::Map< ::google::protobuf::int32, ::google::protobuf::int32 >::const_iterator
          it = this->map().begin();
          it != this->map().end(); ++it) {
        entry.reset(map_.NewEntryWrapper(
            it->first, it->second));
        ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
            1, *entry, output);
      }
    }
  }

  // @@protoc_insertion_point(serialize_end:google.protobuf.testing.EmptyMap)
}

::google::protobuf::uint8* EmptyMap::InternalSerializeWithCachedSizesToArray(
    bool deterministic, ::google::protobuf::uint8* target) const {
  (void)deterministic; // Unused
  // @@protoc_insertion_point(serialize_to_array_start:google.protobuf.testing.EmptyMap)
  // map<int32, int32> map = 1;
  if (!this->map().empty()) {
    typedef ::google::protobuf::Map< ::google::protobuf::int32, ::google::protobuf::int32 >::const_pointer
        ConstPtr;
    typedef ::google::protobuf::internal::SortItem< ::google::protobuf::int32, ConstPtr > SortItem;
    typedef ::google::protobuf::internal::CompareByFirstField<SortItem> Less;

    if (deterministic &&
        this->map().size() > 1) {
      ::google::protobuf::scoped_array<SortItem> items(
          new SortItem[this->map().size()]);
      typedef ::google::protobuf::Map< ::google::protobuf::int32, ::google::protobuf::int32 >::size_type size_type;
      size_type n = 0;
      for (::google::protobuf::Map< ::google::protobuf::int32, ::google::protobuf::int32 >::const_iterator
          it = this->map().begin();
          it != this->map().end(); ++it, ++n) {
        items[n] = SortItem(&*it);
      }
      ::std::sort(&items[0], &items[n], Less());
      ::google::protobuf::scoped_ptr<EmptyMap_MapEntry> entry;
      for (size_type i = 0; i < n; i++) {
        entry.reset(map_.NewEntryWrapper(
            items[i].second->first, items[i].second->second));
        target = ::google::protobuf::internal::WireFormatLite::
                   InternalWriteMessageNoVirtualToArray(
                       1, *entry, deterministic, target);
;
      }
    } else {
      ::google::protobuf::scoped_ptr<EmptyMap_MapEntry> entry;
      for (::google::protobuf::Map< ::google::protobuf::int32, ::google::protobuf::int32 >::const_iterator
          it = this->map().begin();
          it != this->map().end(); ++it) {
        entry.reset(map_.NewEntryWrapper(
            it->first, it->second));
        target = ::google::protobuf::internal::WireFormatLite::
                   InternalWriteMessageNoVirtualToArray(
                       1, *entry, deterministic, target);
;
      }
    }
  }

  // @@protoc_insertion_point(serialize_to_array_end:google.protobuf.testing.EmptyMap)
  return target;
}

size_t EmptyMap::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:google.protobuf.testing.EmptyMap)
  size_t total_size = 0;

  // map<int32, int32> map = 1;
  total_size += 1 *
      ::google::protobuf::internal::FromIntSize(this->map_size());
  {
    ::google::protobuf::scoped_ptr<EmptyMap_MapEntry> entry;
    for (::google::protobuf::Map< ::google::protobuf::int32, ::google::protobuf::int32 >::const_iterator
        it = this->map().begin();
        it != this->map().end(); ++it) {
      entry.reset(map_.NewEntryWrapper(it->first, it->second));
      total_size += ::google::protobuf::internal::WireFormatLite::
          MessageSizeNoVirtual(*entry);
    }
  }

  int cached_size = ::google::protobuf::internal::ToCachedSize(total_size);
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = cached_size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
  return total_size;
}

void EmptyMap::MergeFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_merge_from_start:google.protobuf.testing.EmptyMap)
  if (GOOGLE_PREDICT_FALSE(&from == this)) MergeFromFail(__LINE__);
  const EmptyMap* source =
      ::google::protobuf::internal::DynamicCastToGenerated<const EmptyMap>(
          &from);
  if (source == NULL) {
  // @@protoc_insertion_point(generalized_merge_from_cast_fail:google.protobuf.testing.EmptyMap)
    ::google::protobuf::internal::ReflectionOps::Merge(from, this);
  } else {
  // @@protoc_insertion_point(generalized_merge_from_cast_success:google.protobuf.testing.EmptyMap)
    UnsafeMergeFrom(*source);
  }
}

void EmptyMap::MergeFrom(const EmptyMap& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:google.protobuf.testing.EmptyMap)
  if (GOOGLE_PREDICT_TRUE(&from != this)) {
    UnsafeMergeFrom(from);
  } else {
    MergeFromFail(__LINE__);
  }
}

void EmptyMap::UnsafeMergeFrom(const EmptyMap& from) {
  GOOGLE_DCHECK(&from != this);
  map_.MergeFrom(from.map_);
}

void EmptyMap::CopyFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_copy_from_start:google.protobuf.testing.EmptyMap)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

void EmptyMap::CopyFrom(const EmptyMap& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:google.protobuf.testing.EmptyMap)
  if (&from == this) return;
  Clear();
  UnsafeMergeFrom(from);
}

bool EmptyMap::IsInitialized() const {

  return true;
}

void EmptyMap::Swap(EmptyMap* other) {
  if (other == this) return;
  InternalSwap(other);
}
void EmptyMap::InternalSwap(EmptyMap* other) {
  map_.Swap(&other->map_);
  _internal_metadata_.Swap(&other->_internal_metadata_);
  std::swap(_cached_size_, other->_cached_size_);
}

::google::protobuf::Metadata EmptyMap::GetMetadata() const {
  protobuf_AssignDescriptorsOnce();
  ::google::protobuf::Metadata metadata;
  metadata.descriptor = EmptyMap_descriptor_;
  metadata.reflection = EmptyMap_reflection_;
  return metadata;
}

#if PROTOBUF_INLINE_NOT_IN_HEADERS
// EmptyMap

// map<int32, int32> map = 1;
int EmptyMap::map_size() const {
  return map_.size();
}
void EmptyMap::clear_map() {
  map_.Clear();
}
 const ::google::protobuf::Map< ::google::protobuf::int32, ::google::protobuf::int32 >&
EmptyMap::map() const {
  // @@protoc_insertion_point(field_map:google.protobuf.testing.EmptyMap.map)
  return map_.GetMap();
}
 ::google::protobuf::Map< ::google::protobuf::int32, ::google::protobuf::int32 >*
EmptyMap::mutable_map() {
  // @@protoc_insertion_point(field_mutable_map:google.protobuf.testing.EmptyMap.map)
  return map_.MutableMap();
}

inline const EmptyMap* EmptyMap::internal_default_instance() {
  return &EmptyMap_default_instance_.get();
}
#endif  // PROTOBUF_INLINE_NOT_IN_HEADERS

// ===================================================================

#if !defined(_MSC_VER) || _MSC_VER >= 1900
const int StringtoInt::kMapFieldNumber;
#endif  // !defined(_MSC_VER) || _MSC_VER >= 1900

StringtoInt::StringtoInt()
  : ::google::protobuf::Message(), _internal_metadata_(NULL) {
  if (this != internal_default_instance()) protobuf_InitDefaults_google_2fprotobuf_2futil_2finternal_2ftestdata_2fmaps_2eproto();
  SharedCtor();
  // @@protoc_insertion_point(constructor:google.protobuf.testing.StringtoInt)
}

void StringtoInt::InitAsDefaultInstance() {
}

StringtoInt::StringtoInt(const StringtoInt& from)
  : ::google::protobuf::Message(),
    _internal_metadata_(NULL) {
  SharedCtor();
  UnsafeMergeFrom(from);
  // @@protoc_insertion_point(copy_constructor:google.protobuf.testing.StringtoInt)
}

void StringtoInt::SharedCtor() {
  map_.SetAssignDescriptorCallback(
      protobuf_AssignDescriptorsOnce);
  map_.SetEntryDescriptor(
      &::google::protobuf::testing::StringtoInt_MapEntry_descriptor_);
  _cached_size_ = 0;
}

StringtoInt::~StringtoInt() {
  // @@protoc_insertion_point(destructor:google.protobuf.testing.StringtoInt)
  SharedDtor();
}

void StringtoInt::SharedDtor() {
}

void StringtoInt::SetCachedSize(int size) const {
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
}
const ::google::protobuf::Descriptor* StringtoInt::descriptor() {
  protobuf_AssignDescriptorsOnce();
  return StringtoInt_descriptor_;
}

const StringtoInt& StringtoInt::default_instance() {
  protobuf_InitDefaults_google_2fprotobuf_2futil_2finternal_2ftestdata_2fmaps_2eproto();
  return *internal_default_instance();
}

::google::protobuf::internal::ExplicitlyConstructed<StringtoInt> StringtoInt_default_instance_;

StringtoInt* StringtoInt::New(::google::protobuf::Arena* arena) const {
  StringtoInt* n = new StringtoInt;
  if (arena != NULL) {
    arena->Own(n);
  }
  return n;
}

void StringtoInt::Clear() {
// @@protoc_insertion_point(message_clear_start:google.protobuf.testing.StringtoInt)
  map_.Clear();
}

bool StringtoInt::MergePartialFromCodedStream(
    ::google::protobuf::io::CodedInputStream* input) {
#define DO_(EXPRESSION) if (!GOOGLE_PREDICT_TRUE(EXPRESSION)) goto failure
  ::google::protobuf::uint32 tag;
  // @@protoc_insertion_point(parse_start:google.protobuf.testing.StringtoInt)
  for (;;) {
    ::std::pair< ::google::protobuf::uint32, bool> p = input->ReadTagWithCutoff(127);
    tag = p.first;
    if (!p.second) goto handle_unusual;
    switch (::google::protobuf::internal::WireFormatLite::GetTagFieldNumber(tag)) {
      // map<string, int32> map = 1;
      case 1: {
        if (tag == 10) {
          DO_(input->IncrementRecursionDepth());
         parse_loop_map:
          StringtoInt_MapEntry::Parser< ::google::protobuf::internal::MapField<
              ::std::string, ::google::protobuf::int32,
              ::google::protobuf::internal::WireFormatLite::TYPE_STRING,
              ::google::protobuf::internal::WireFormatLite::TYPE_INT32,
              0 >,
            ::google::protobuf::Map< ::std::string, ::google::protobuf::int32 > > parser(&map_);
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessageNoVirtual(
              input, &parser));
          DO_(::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
            parser.key().data(), parser.key().length(),
            ::google::protobuf::internal::WireFormatLite::PARSE,
            "google.protobuf.testing.StringtoInt.MapEntry.key"));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(10)) goto parse_loop_map;
        input->UnsafeDecrementRecursionDepth();
        if (input->ExpectAtEnd()) goto success;
        break;
      }

      default: {
      handle_unusual:
        if (tag == 0 ||
            ::google::protobuf::internal::WireFormatLite::GetTagWireType(tag) ==
            ::google::protobuf::internal::WireFormatLite::WIRETYPE_END_GROUP) {
          goto success;
        }
        DO_(::google::protobuf::internal::WireFormatLite::SkipField(input, tag));
        break;
      }
    }
  }
success:
  // @@protoc_insertion_point(parse_success:google.protobuf.testing.StringtoInt)
  return true;
failure:
  // @@protoc_insertion_point(parse_failure:google.protobuf.testing.StringtoInt)
  return false;
#undef DO_
}

void StringtoInt::SerializeWithCachedSizes(
    ::google::protobuf::io::CodedOutputStream* output) const {
  // @@protoc_insertion_point(serialize_start:google.protobuf.testing.StringtoInt)
  // map<string, int32> map = 1;
  if (!this->map().empty()) {
    typedef ::google::protobuf::Map< ::std::string, ::google::protobuf::int32 >::const_pointer
        ConstPtr;
    typedef ConstPtr SortItem;
    typedef ::google::protobuf::internal::CompareByDerefFirst<SortItem> Less;
    struct Utf8Check {
      static void Check(ConstPtr p) {
        ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
          p->first.data(), p->first.length(),
          ::google::protobuf::internal::WireFormatLite::SERIALIZE,
          "google.protobuf.testing.StringtoInt.MapEntry.key");
      }
    };

    if (output->IsSerializationDeterminstic() &&
        this->map().size() > 1) {
      ::google::protobuf::scoped_array<SortItem> items(
          new SortItem[this->map().size()]);
      typedef ::google::protobuf::Map< ::std::string, ::google::protobuf::int32 >::size_type size_type;
      size_type n = 0;
      for (::google::protobuf::Map< ::std::string, ::google::protobuf::int32 >::const_iterator
          it = this->map().begin();
          it != this->map().end(); ++it, ++n) {
        items[n] = SortItem(&*it);
      }
      ::std::sort(&items[0], &items[n], Less());
      ::google::protobuf::scoped_ptr<StringtoInt_MapEntry> entry;
      for (size_type i = 0; i < n; i++) {
        entry.reset(map_.NewEntryWrapper(
            items[i]->first, items[i]->second));
        ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
            1, *entry, output);
        Utf8Check::Check(items[i]);
      }
    } else {
      ::google::protobuf::scoped_ptr<StringtoInt_MapEntry> entry;
      for (::google::protobuf::Map< ::std::string, ::google::protobuf::int32 >::const_iterator
          it = this->map().begin();
          it != this->map().end(); ++it) {
        entry.reset(map_.NewEntryWrapper(
            it->first, it->second));
        ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
            1, *entry, output);
        Utf8Check::Check(&*it);
      }
    }
  }

  // @@protoc_insertion_point(serialize_end:google.protobuf.testing.StringtoInt)
}

::google::protobuf::uint8* StringtoInt::InternalSerializeWithCachedSizesToArray(
    bool deterministic, ::google::protobuf::uint8* target) const {
  (void)deterministic; // Unused
  // @@protoc_insertion_point(serialize_to_array_start:google.protobuf.testing.StringtoInt)
  // map<string, int32> map = 1;
  if (!this->map().empty()) {
    typedef ::google::protobuf::Map< ::std::string, ::google::protobuf::int32 >::const_pointer
        ConstPtr;
    typedef ConstPtr SortItem;
    typedef ::google::protobuf::internal::CompareByDerefFirst<SortItem> Less;
    struct Utf8Check {
      static void Check(ConstPtr p) {
        ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
          p->first.data(), p->first.length(),
          ::google::protobuf::internal::WireFormatLite::SERIALIZE,
          "google.protobuf.testing.StringtoInt.MapEntry.key");
      }
    };

    if (deterministic &&
        this->map().size() > 1) {
      ::google::protobuf::scoped_array<SortItem> items(
          new SortItem[this->map().size()]);
      typedef ::google::protobuf::Map< ::std::string, ::google::protobuf::int32 >::size_type size_type;
      size_type n = 0;
      for (::google::protobuf::Map< ::std::string, ::google::protobuf::int32 >::const_iterator
          it = this->map().begin();
          it != this->map().end(); ++it, ++n) {
        items[n] = SortItem(&*it);
      }
      ::std::sort(&items[0], &items[n], Less());
      ::google::protobuf::scoped_ptr<StringtoInt_MapEntry> entry;
      for (size_type i = 0; i < n; i++) {
        entry.reset(map_.NewEntryWrapper(
            items[i]->first, items[i]->second));
        target = ::google::protobuf::internal::WireFormatLite::
                   InternalWriteMessageNoVirtualToArray(
                       1, *entry, deterministic, target);
;
        Utf8Check::Check(items[i]);
      }
    } else {
      ::google::protobuf::scoped_ptr<StringtoInt_MapEntry> entry;
      for (::google::protobuf::Map< ::std::string, ::google::protobuf::int32 >::const_iterator
          it = this->map().begin();
          it != this->map().end(); ++it) {
        entry.reset(map_.NewEntryWrapper(
            it->first, it->second));
        target = ::google::protobuf::internal::WireFormatLite::
                   InternalWriteMessageNoVirtualToArray(
                       1, *entry, deterministic, target);
;
        Utf8Check::Check(&*it);
      }
    }
  }

  // @@protoc_insertion_point(serialize_to_array_end:google.protobuf.testing.StringtoInt)
  return target;
}

size_t StringtoInt::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:google.protobuf.testing.StringtoInt)
  size_t total_size = 0;

  // map<string, int32> map = 1;
  total_size += 1 *
      ::google::protobuf::internal::FromIntSize(this->map_size());
  {
    ::google::protobuf::scoped_ptr<StringtoInt_MapEntry> entry;
    for (::google::protobuf::Map< ::std::string, ::google::protobuf::int32 >::const_iterator
        it = this->map().begin();
        it != this->map().end(); ++it) {
      entry.reset(map_.NewEntryWrapper(it->first, it->second));
      total_size += ::google::protobuf::internal::WireFormatLite::
          MessageSizeNoVirtual(*entry);
    }
  }

  int cached_size = ::google::protobuf::internal::ToCachedSize(total_size);
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = cached_size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
  return total_size;
}

void StringtoInt::MergeFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_merge_from_start:google.protobuf.testing.StringtoInt)
  if (GOOGLE_PREDICT_FALSE(&from == this)) MergeFromFail(__LINE__);
  const StringtoInt* source =
      ::google::protobuf::internal::DynamicCastToGenerated<const StringtoInt>(
          &from);
  if (source == NULL) {
  // @@protoc_insertion_point(generalized_merge_from_cast_fail:google.protobuf.testing.StringtoInt)
    ::google::protobuf::internal::ReflectionOps::Merge(from, this);
  } else {
  // @@protoc_insertion_point(generalized_merge_from_cast_success:google.protobuf.testing.StringtoInt)
    UnsafeMergeFrom(*source);
  }
}

void StringtoInt::MergeFrom(const StringtoInt& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:google.protobuf.testing.StringtoInt)
  if (GOOGLE_PREDICT_TRUE(&from != this)) {
    UnsafeMergeFrom(from);
  } else {
    MergeFromFail(__LINE__);
  }
}

void StringtoInt::UnsafeMergeFrom(const StringtoInt& from) {
  GOOGLE_DCHECK(&from != this);
  map_.MergeFrom(from.map_);
}

void StringtoInt::CopyFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_copy_from_start:google.protobuf.testing.StringtoInt)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

void StringtoInt::CopyFrom(const StringtoInt& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:google.protobuf.testing.StringtoInt)
  if (&from == this) return;
  Clear();
  UnsafeMergeFrom(from);
}

bool StringtoInt::IsInitialized() const {

  return true;
}

void StringtoInt::Swap(StringtoInt* other) {
  if (other == this) return;
  InternalSwap(other);
}
void StringtoInt::InternalSwap(StringtoInt* other) {
  map_.Swap(&other->map_);
  _internal_metadata_.Swap(&other->_internal_metadata_);
  std::swap(_cached_size_, other->_cached_size_);
}

::google::protobuf::Metadata StringtoInt::GetMetadata() const {
  protobuf_AssignDescriptorsOnce();
  ::google::protobuf::Metadata metadata;
  metadata.descriptor = StringtoInt_descriptor_;
  metadata.reflection = StringtoInt_reflection_;
  return metadata;
}

#if PROTOBUF_INLINE_NOT_IN_HEADERS
// StringtoInt

// map<string, int32> map = 1;
int StringtoInt::map_size() const {
  return map_.size();
}
void StringtoInt::clear_map() {
  map_.Clear();
}
 const ::google::protobuf::Map< ::std::string, ::google::protobuf::int32 >&
StringtoInt::map() const {
  // @@protoc_insertion_point(field_map:google.protobuf.testing.StringtoInt.map)
  return map_.GetMap();
}
 ::google::protobuf::Map< ::std::string, ::google::protobuf::int32 >*
StringtoInt::mutable_map() {
  // @@protoc_insertion_point(field_mutable_map:google.protobuf.testing.StringtoInt.map)
  return map_.MutableMap();
}

inline const StringtoInt* StringtoInt::internal_default_instance() {
  return &StringtoInt_default_instance_.get();
}
#endif  // PROTOBUF_INLINE_NOT_IN_HEADERS

// ===================================================================

#if !defined(_MSC_VER) || _MSC_VER >= 1900
const int IntToString::kMapFieldNumber;
#endif  // !defined(_MSC_VER) || _MSC_VER >= 1900

IntToString::IntToString()
  : ::google::protobuf::Message(), _internal_metadata_(NULL) {
  if (this != internal_default_instance()) protobuf_InitDefaults_google_2fprotobuf_2futil_2finternal_2ftestdata_2fmaps_2eproto();
  SharedCtor();
  // @@protoc_insertion_point(constructor:google.protobuf.testing.IntToString)
}

void IntToString::InitAsDefaultInstance() {
}

IntToString::IntToString(const IntToString& from)
  : ::google::protobuf::Message(),
    _internal_metadata_(NULL) {
  SharedCtor();
  UnsafeMergeFrom(from);
  // @@protoc_insertion_point(copy_constructor:google.protobuf.testing.IntToString)
}

void IntToString::SharedCtor() {
  map_.SetAssignDescriptorCallback(
      protobuf_AssignDescriptorsOnce);
  map_.SetEntryDescriptor(
      &::google::protobuf::testing::IntToString_MapEntry_descriptor_);
  _cached_size_ = 0;
}

IntToString::~IntToString() {
  // @@protoc_insertion_point(destructor:google.protobuf.testing.IntToString)
  SharedDtor();
}

void IntToString::SharedDtor() {
}

void IntToString::SetCachedSize(int size) const {
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
}
const ::google::protobuf::Descriptor* IntToString::descriptor() {
  protobuf_AssignDescriptorsOnce();
  return IntToString_descriptor_;
}

const IntToString& IntToString::default_instance() {
  protobuf_InitDefaults_google_2fprotobuf_2futil_2finternal_2ftestdata_2fmaps_2eproto();
  return *internal_default_instance();
}

::google::protobuf::internal::ExplicitlyConstructed<IntToString> IntToString_default_instance_;

IntToString* IntToString::New(::google::protobuf::Arena* arena) const {
  IntToString* n = new IntToString;
  if (arena != NULL) {
    arena->Own(n);
  }
  return n;
}

void IntToString::Clear() {
// @@protoc_insertion_point(message_clear_start:google.protobuf.testing.IntToString)
  map_.Clear();
}

bool IntToString::MergePartialFromCodedStream(
    ::google::protobuf::io::CodedInputStream* input) {
#define DO_(EXPRESSION) if (!GOOGLE_PREDICT_TRUE(EXPRESSION)) goto failure
  ::google::protobuf::uint32 tag;
  // @@protoc_insertion_point(parse_start:google.protobuf.testing.IntToString)
  for (;;) {
    ::std::pair< ::google::protobuf::uint32, bool> p = input->ReadTagWithCutoff(127);
    tag = p.first;
    if (!p.second) goto handle_unusual;
    switch (::google::protobuf::internal::WireFormatLite::GetTagFieldNumber(tag)) {
      // map<int32, string> map = 1;
      case 1: {
        if (tag == 10) {
          DO_(input->IncrementRecursionDepth());
         parse_loop_map:
          IntToString_MapEntry::Parser< ::google::protobuf::internal::MapField<
              ::google::protobuf::int32, ::std::string,
              ::google::protobuf::internal::WireFormatLite::TYPE_INT32,
              ::google::protobuf::internal::WireFormatLite::TYPE_STRING,
              0 >,
            ::google::protobuf::Map< ::google::protobuf::int32, ::std::string > > parser(&map_);
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessageNoVirtual(
              input, &parser));
          DO_(::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
            parser.value().data(), parser.value().length(),
            ::google::protobuf::internal::WireFormatLite::PARSE,
            "google.protobuf.testing.IntToString.MapEntry.value"));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(10)) goto parse_loop_map;
        input->UnsafeDecrementRecursionDepth();
        if (input->ExpectAtEnd()) goto success;
        break;
      }

      default: {
      handle_unusual:
        if (tag == 0 ||
            ::google::protobuf::internal::WireFormatLite::GetTagWireType(tag) ==
            ::google::protobuf::internal::WireFormatLite::WIRETYPE_END_GROUP) {
          goto success;
        }
        DO_(::google::protobuf::internal::WireFormatLite::SkipField(input, tag));
        break;
      }
    }
  }
success:
  // @@protoc_insertion_point(parse_success:google.protobuf.testing.IntToString)
  return true;
failure:
  // @@protoc_insertion_point(parse_failure:google.protobuf.testing.IntToString)
  return false;
#undef DO_
}

void IntToString::SerializeWithCachedSizes(
    ::google::protobuf::io::CodedOutputStream* output) const {
  // @@protoc_insertion_point(serialize_start:google.protobuf.testing.IntToString)
  // map<int32, string> map = 1;
  if (!this->map().empty()) {
    typedef ::google::protobuf::Map< ::google::protobuf::int32, ::std::string >::const_pointer
        ConstPtr;
    typedef ::google::protobuf::internal::SortItem< ::google::protobuf::int32, ConstPtr > SortItem;
    typedef ::google::protobuf::internal::CompareByFirstField<SortItem> Less;
    struct Utf8Check {
      static void Check(ConstPtr p) {
        ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
          p->second.data(), p->second.length(),
          ::google::protobuf::internal::WireFormatLite::SERIALIZE,
          "google.protobuf.testing.IntToString.MapEntry.value");
      }
    };

    if (output->IsSerializationDeterminstic() &&
        this->map().size() > 1) {
      ::google::protobuf::scoped_array<SortItem> items(
          new SortItem[this->map().size()]);
      typedef ::google::protobuf::Map< ::google::protobuf::int32, ::std::string >::size_type size_type;
      size_type n = 0;
      for (::google::protobuf::Map< ::google::protobuf::int32, ::std::string >::const_iterator
          it = this->map().begin();
          it != this->map().end(); ++it, ++n) {
        items[n] = SortItem(&*it);
      }
      ::std::sort(&items[0], &items[n], Less());
      ::google::protobuf::scoped_ptr<IntToString_MapEntry> entry;
      for (size_type i = 0; i < n; i++) {
        entry.reset(map_.NewEntryWrapper(
            items[i].second->first, items[i].second->second));
        ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
            1, *entry, output);
        Utf8Check::Check(items[i].second);
      }
    } else {
      ::google::protobuf::scoped_ptr<IntToString_MapEntry> entry;
      for (::google::protobuf::Map< ::google::protobuf::int32, ::std::string >::const_iterator
          it = this->map().begin();
          it != this->map().end(); ++it) {
        entry.reset(map_.NewEntryWrapper(
            it->first, it->second));
        ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
            1, *entry, output);
        Utf8Check::Check(&*it);
      }
    }
  }

  // @@protoc_insertion_point(serialize_end:google.protobuf.testing.IntToString)
}

::google::protobuf::uint8* IntToString::InternalSerializeWithCachedSizesToArray(
    bool deterministic, ::google::protobuf::uint8* target) const {
  (void)deterministic; // Unused
  // @@protoc_insertion_point(serialize_to_array_start:google.protobuf.testing.IntToString)
  // map<int32, string> map = 1;
  if (!this->map().empty()) {
    typedef ::google::protobuf::Map< ::google::protobuf::int32, ::std::string >::const_pointer
        ConstPtr;
    typedef ::google::protobuf::internal::SortItem< ::google::protobuf::int32, ConstPtr > SortItem;
    typedef ::google::protobuf::internal::CompareByFirstField<SortItem> Less;
    struct Utf8Check {
      static void Check(ConstPtr p) {
        ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
          p->second.data(), p->second.length(),
          ::google::protobuf::internal::WireFormatLite::SERIALIZE,
          "google.protobuf.testing.IntToString.MapEntry.value");
      }
    };

    if (deterministic &&
        this->map().size() > 1) {
      ::google::protobuf::scoped_array<SortItem> items(
          new SortItem[this->map().size()]);
      typedef ::google::protobuf::Map< ::google::protobuf::int32, ::std::string >::size_type size_type;
      size_type n = 0;
      for (::google::protobuf::Map< ::google::protobuf::int32, ::std::string >::const_iterator
          it = this->map().begin();
          it != this->map().end(); ++it, ++n) {
        items[n] = SortItem(&*it);
      }
      ::std::sort(&items[0], &items[n], Less());
      ::google::protobuf::scoped_ptr<IntToString_MapEntry> entry;
      for (size_type i = 0; i < n; i++) {
        entry.reset(map_.NewEntryWrapper(
            items[i].second->first, items[i].second->second));
        target = ::google::protobuf::internal::WireFormatLite::
                   InternalWriteMessageNoVirtualToArray(
                       1, *entry, deterministic, target);
;
        Utf8Check::Check(items[i].second);
      }
    } else {
      ::google::protobuf::scoped_ptr<IntToString_MapEntry> entry;
      for (::google::protobuf::Map< ::google::protobuf::int32, ::std::string >::const_iterator
          it = this->map().begin();
          it != this->map().end(); ++it) {
        entry.reset(map_.NewEntryWrapper(
            it->first, it->second));
        target = ::google::protobuf::internal::WireFormatLite::
                   InternalWriteMessageNoVirtualToArray(
                       1, *entry, deterministic, target);
;
        Utf8Check::Check(&*it);
      }
    }
  }

  // @@protoc_insertion_point(serialize_to_array_end:google.protobuf.testing.IntToString)
  return target;
}

size_t IntToString::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:google.protobuf.testing.IntToString)
  size_t total_size = 0;

  // map<int32, string> map = 1;
  total_size += 1 *
      ::google::protobuf::internal::FromIntSize(this->map_size());
  {
    ::google::protobuf::scoped_ptr<IntToString_MapEntry> entry;
    for (::google::protobuf::Map< ::google::protobuf::int32, ::std::string >::const_iterator
        it = this->map().begin();
        it != this->map().end(); ++it) {
      entry.reset(map_.NewEntryWrapper(it->first, it->second));
      total_size += ::google::protobuf::internal::WireFormatLite::
          MessageSizeNoVirtual(*entry);
    }
  }

  int cached_size = ::google::protobuf::internal::ToCachedSize(total_size);
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = cached_size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
  return total_size;
}

void IntToString::MergeFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_merge_from_start:google.protobuf.testing.IntToString)
  if (GOOGLE_PREDICT_FALSE(&from == this)) MergeFromFail(__LINE__);
  const IntToString* source =
      ::google::protobuf::internal::DynamicCastToGenerated<const IntToString>(
          &from);
  if (source == NULL) {
  // @@protoc_insertion_point(generalized_merge_from_cast_fail:google.protobuf.testing.IntToString)
    ::google::protobuf::internal::ReflectionOps::Merge(from, this);
  } else {
  // @@protoc_insertion_point(generalized_merge_from_cast_success:google.protobuf.testing.IntToString)
    UnsafeMergeFrom(*source);
  }
}

void IntToString::MergeFrom(const IntToString& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:google.protobuf.testing.IntToString)
  if (GOOGLE_PREDICT_TRUE(&from != this)) {
    UnsafeMergeFrom(from);
  } else {
    MergeFromFail(__LINE__);
  }
}

void IntToString::UnsafeMergeFrom(const IntToString& from) {
  GOOGLE_DCHECK(&from != this);
  map_.MergeFrom(from.map_);
}

void IntToString::CopyFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_copy_from_start:google.protobuf.testing.IntToString)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

void IntToString::CopyFrom(const IntToString& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:google.protobuf.testing.IntToString)
  if (&from == this) return;
  Clear();
  UnsafeMergeFrom(from);
}

bool IntToString::IsInitialized() const {

  return true;
}

void IntToString::Swap(IntToString* other) {
  if (other == this) return;
  InternalSwap(other);
}
void IntToString::InternalSwap(IntToString* other) {
  map_.Swap(&other->map_);
  _internal_metadata_.Swap(&other->_internal_metadata_);
  std::swap(_cached_size_, other->_cached_size_);
}

::google::protobuf::Metadata IntToString::GetMetadata() const {
  protobuf_AssignDescriptorsOnce();
  ::google::protobuf::Metadata metadata;
  metadata.descriptor = IntToString_descriptor_;
  metadata.reflection = IntToString_reflection_;
  return metadata;
}

#if PROTOBUF_INLINE_NOT_IN_HEADERS
// IntToString

// map<int32, string> map = 1;
int IntToString::map_size() const {
  return map_.size();
}
void IntToString::clear_map() {
  map_.Clear();
}
 const ::google::protobuf::Map< ::google::protobuf::int32, ::std::string >&
IntToString::map() const {
  // @@protoc_insertion_point(field_map:google.protobuf.testing.IntToString.map)
  return map_.GetMap();
}
 ::google::protobuf::Map< ::google::protobuf::int32, ::std::string >*
IntToString::mutable_map() {
  // @@protoc_insertion_point(field_mutable_map:google.protobuf.testing.IntToString.map)
  return map_.MutableMap();
}

inline const IntToString* IntToString::internal_default_instance() {
  return &IntToString_default_instance_.get();
}
#endif  // PROTOBUF_INLINE_NOT_IN_HEADERS

// ===================================================================

#if !defined(_MSC_VER) || _MSC_VER >= 1900
const int BoolToString::kMapFieldNumber;
#endif  // !defined(_MSC_VER) || _MSC_VER >= 1900

BoolToString::BoolToString()
  : ::google::protobuf::Message(), _internal_metadata_(NULL) {
  if (this != internal_default_instance()) protobuf_InitDefaults_google_2fprotobuf_2futil_2finternal_2ftestdata_2fmaps_2eproto();
  SharedCtor();
  // @@protoc_insertion_point(constructor:google.protobuf.testing.BoolToString)
}

void BoolToString::InitAsDefaultInstance() {
}

BoolToString::BoolToString(const BoolToString& from)
  : ::google::protobuf::Message(),
    _internal_metadata_(NULL) {
  SharedCtor();
  UnsafeMergeFrom(from);
  // @@protoc_insertion_point(copy_constructor:google.protobuf.testing.BoolToString)
}

void BoolToString::SharedCtor() {
  map_.SetAssignDescriptorCallback(
      protobuf_AssignDescriptorsOnce);
  map_.SetEntryDescriptor(
      &::google::protobuf::testing::BoolToString_MapEntry_descriptor_);
  _cached_size_ = 0;
}

BoolToString::~BoolToString() {
  // @@protoc_insertion_point(destructor:google.protobuf.testing.BoolToString)
  SharedDtor();
}

void BoolToString::SharedDtor() {
}

void BoolToString::SetCachedSize(int size) const {
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
}
const ::google::protobuf::Descriptor* BoolToString::descriptor() {
  protobuf_AssignDescriptorsOnce();
  return BoolToString_descriptor_;
}

const BoolToString& BoolToString::default_instance() {
  protobuf_InitDefaults_google_2fprotobuf_2futil_2finternal_2ftestdata_2fmaps_2eproto();
  return *internal_default_instance();
}

::google::protobuf::internal::ExplicitlyConstructed<BoolToString> BoolToString_default_instance_;

BoolToString* BoolToString::New(::google::protobuf::Arena* arena) const {
  BoolToString* n = new BoolToString;
  if (arena != NULL) {
    arena->Own(n);
  }
  return n;
}

void BoolToString::Clear() {
// @@protoc_insertion_point(message_clear_start:google.protobuf.testing.BoolToString)
  map_.Clear();
}

bool BoolToString::MergePartialFromCodedStream(
    ::google::protobuf::io::CodedInputStream* input) {
#define DO_(EXPRESSION) if (!GOOGLE_PREDICT_TRUE(EXPRESSION)) goto failure
  ::google::protobuf::uint32 tag;
  // @@protoc_insertion_point(parse_start:google.protobuf.testing.BoolToString)
  for (;;) {
    ::std::pair< ::google::protobuf::uint32, bool> p = input->ReadTagWithCutoff(127);
    tag = p.first;
    if (!p.second) goto handle_unusual;
    switch (::google::protobuf::internal::WireFormatLite::GetTagFieldNumber(tag)) {
      // map<bool, string> map = 1;
      case 1: {
        if (tag == 10) {
          DO_(input->IncrementRecursionDepth());
         parse_loop_map:
          BoolToString_MapEntry::Parser< ::google::protobuf::internal::MapField<
              bool, ::std::string,
              ::google::protobuf::internal::WireFormatLite::TYPE_BOOL,
              ::google::protobuf::internal::WireFormatLite::TYPE_STRING,
              0 >,
            ::google::protobuf::Map< bool, ::std::string > > parser(&map_);
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessageNoVirtual(
              input, &parser));
          DO_(::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
            parser.value().data(), parser.value().length(),
            ::google::protobuf::internal::WireFormatLite::PARSE,
            "google.protobuf.testing.BoolToString.MapEntry.value"));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(10)) goto parse_loop_map;
        input->UnsafeDecrementRecursionDepth();
        if (input->ExpectAtEnd()) goto success;
        break;
      }

      default: {
      handle_unusual:
        if (tag == 0 ||
            ::google::protobuf::internal::WireFormatLite::GetTagWireType(tag) ==
            ::google::protobuf::internal::WireFormatLite::WIRETYPE_END_GROUP) {
          goto success;
        }
        DO_(::google::protobuf::internal::WireFormatLite::SkipField(input, tag));
        break;
      }
    }
  }
success:
  // @@protoc_insertion_point(parse_success:google.protobuf.testing.BoolToString)
  return true;
failure:
  // @@protoc_insertion_point(parse_failure:google.protobuf.testing.BoolToString)
  return false;
#undef DO_
}

void BoolToString::SerializeWithCachedSizes(
    ::google::protobuf::io::CodedOutputStream* output) const {
  // @@protoc_insertion_point(serialize_start:google.protobuf.testing.BoolToString)
  // map<bool, string> map = 1;
  if (!this->map().empty()) {
    typedef ::google::protobuf::Map< bool, ::std::string >::const_pointer
        ConstPtr;
    typedef ::google::protobuf::internal::SortItem< bool, ConstPtr > SortItem;
    typedef ::google::protobuf::internal::CompareByFirstField<SortItem> Less;
    struct Utf8Check {
      static void Check(ConstPtr p) {
        ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
          p->second.data(), p->second.length(),
          ::google::protobuf::internal::WireFormatLite::SERIALIZE,
          "google.protobuf.testing.BoolToString.MapEntry.value");
      }
    };

    if (output->IsSerializationDeterminstic() &&
        this->map().size() > 1) {
      ::google::protobuf::scoped_array<SortItem> items(
          new SortItem[this->map().size()]);
      typedef ::google::protobuf::Map< bool, ::std::string >::size_type size_type;
      size_type n = 0;
      for (::google::protobuf::Map< bool, ::std::string >::const_iterator
          it = this->map().begin();
          it != this->map().end(); ++it, ++n) {
        items[n] = SortItem(&*it);
      }
      ::std::sort(&items[0], &items[n], Less());
      ::google::protobuf::scoped_ptr<BoolToString_MapEntry> entry;
      for (size_type i = 0; i < n; i++) {
        entry.reset(map_.NewEntryWrapper(
            items[i].second->first, items[i].second->second));
        ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
            1, *entry, output);
        Utf8Check::Check(items[i].second);
      }
    } else {
      ::google::protobuf::scoped_ptr<BoolToString_MapEntry> entry;
      for (::google::protobuf::Map< bool, ::std::string >::const_iterator
          it = this->map().begin();
          it != this->map().end(); ++it) {
        entry.reset(map_.NewEntryWrapper(
            it->first, it->second));
        ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
            1, *entry, output);
        Utf8Check::Check(&*it);
      }
    }
  }

  // @@protoc_insertion_point(serialize_end:google.protobuf.testing.BoolToString)
}

::google::protobuf::uint8* BoolToString::InternalSerializeWithCachedSizesToArray(
    bool deterministic, ::google::protobuf::uint8* target) const {
  (void)deterministic; // Unused
  // @@protoc_insertion_point(serialize_to_array_start:google.protobuf.testing.BoolToString)
  // map<bool, string> map = 1;
  if (!this->map().empty()) {
    typedef ::google::protobuf::Map< bool, ::std::string >::const_pointer
        ConstPtr;
    typedef ::google::protobuf::internal::SortItem< bool, ConstPtr > SortItem;
    typedef ::google::protobuf::internal::CompareByFirstField<SortItem> Less;
    struct Utf8Check {
      static void Check(ConstPtr p) {
        ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
          p->second.data(), p->second.length(),
          ::google::protobuf::internal::WireFormatLite::SERIALIZE,
          "google.protobuf.testing.BoolToString.MapEntry.value");
      }
    };

    if (deterministic &&
        this->map().size() > 1) {
      ::google::protobuf::scoped_array<SortItem> items(
          new SortItem[this->map().size()]);
      typedef ::google::protobuf::Map< bool, ::std::string >::size_type size_type;
      size_type n = 0;
      for (::google::protobuf::Map< bool, ::std::string >::const_iterator
          it = this->map().begin();
          it != this->map().end(); ++it, ++n) {
        items[n] = SortItem(&*it);
      }
      ::std::sort(&items[0], &items[n], Less());
      ::google::protobuf::scoped_ptr<BoolToString_MapEntry> entry;
      for (size_type i = 0; i < n; i++) {
        entry.reset(map_.NewEntryWrapper(
            items[i].second->first, items[i].second->second));
        target = ::google::protobuf::internal::WireFormatLite::
                   InternalWriteMessageNoVirtualToArray(
                       1, *entry, deterministic, target);
;
        Utf8Check::Check(items[i].second);
      }
    } else {
      ::google::protobuf::scoped_ptr<BoolToString_MapEntry> entry;
      for (::google::protobuf::Map< bool, ::std::string >::const_iterator
          it = this->map().begin();
          it != this->map().end(); ++it) {
        entry.reset(map_.NewEntryWrapper(
            it->first, it->second));
        target = ::google::protobuf::internal::WireFormatLite::
                   InternalWriteMessageNoVirtualToArray(
                       1, *entry, deterministic, target);
;
        Utf8Check::Check(&*it);
      }
    }
  }

  // @@protoc_insertion_point(serialize_to_array_end:google.protobuf.testing.BoolToString)
  return target;
}

size_t BoolToString::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:google.protobuf.testing.BoolToString)
  size_t total_size = 0;

  // map<bool, string> map = 1;
  total_size += 1 *
      ::google::protobuf::internal::FromIntSize(this->map_size());
  {
    ::google::protobuf::scoped_ptr<BoolToString_MapEntry> entry;
    for (::google::protobuf::Map< bool, ::std::string >::const_iterator
        it = this->map().begin();
        it != this->map().end(); ++it) {
      entry.reset(map_.NewEntryWrapper(it->first, it->second));
      total_size += ::google::protobuf::internal::WireFormatLite::
          MessageSizeNoVirtual(*entry);
    }
  }

  int cached_size = ::google::protobuf::internal::ToCachedSize(total_size);
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = cached_size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
  return total_size;
}

void BoolToString::MergeFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_merge_from_start:google.protobuf.testing.BoolToString)
  if (GOOGLE_PREDICT_FALSE(&from == this)) MergeFromFail(__LINE__);
  const BoolToString* source =
      ::google::protobuf::internal::DynamicCastToGenerated<const BoolToString>(
          &from);
  if (source == NULL) {
  // @@protoc_insertion_point(generalized_merge_from_cast_fail:google.protobuf.testing.BoolToString)
    ::google::protobuf::internal::ReflectionOps::Merge(from, this);
  } else {
  // @@protoc_insertion_point(generalized_merge_from_cast_success:google.protobuf.testing.BoolToString)
    UnsafeMergeFrom(*source);
  }
}

void BoolToString::MergeFrom(const BoolToString& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:google.protobuf.testing.BoolToString)
  if (GOOGLE_PREDICT_TRUE(&from != this)) {
    UnsafeMergeFrom(from);
  } else {
    MergeFromFail(__LINE__);
  }
}

void BoolToString::UnsafeMergeFrom(const BoolToString& from) {
  GOOGLE_DCHECK(&from != this);
  map_.MergeFrom(from.map_);
}

void BoolToString::CopyFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_copy_from_start:google.protobuf.testing.BoolToString)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

void BoolToString::CopyFrom(const BoolToString& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:google.protobuf.testing.BoolToString)
  if (&from == this) return;
  Clear();
  UnsafeMergeFrom(from);
}

bool BoolToString::IsInitialized() const {

  return true;
}

void BoolToString::Swap(BoolToString* other) {
  if (other == this) return;
  InternalSwap(other);
}
void BoolToString::InternalSwap(BoolToString* other) {
  map_.Swap(&other->map_);
  _internal_metadata_.Swap(&other->_internal_metadata_);
  std::swap(_cached_size_, other->_cached_size_);
}

::google::protobuf::Metadata BoolToString::GetMetadata() const {
  protobuf_AssignDescriptorsOnce();
  ::google::protobuf::Metadata metadata;
  metadata.descriptor = BoolToString_descriptor_;
  metadata.reflection = BoolToString_reflection_;
  return metadata;
}

#if PROTOBUF_INLINE_NOT_IN_HEADERS
// BoolToString

// map<bool, string> map = 1;
int BoolToString::map_size() const {
  return map_.size();
}
void BoolToString::clear_map() {
  map_.Clear();
}
 const ::google::protobuf::Map< bool, ::std::string >&
BoolToString::map() const {
  // @@protoc_insertion_point(field_map:google.protobuf.testing.BoolToString.map)
  return map_.GetMap();
}
 ::google::protobuf::Map< bool, ::std::string >*
BoolToString::mutable_map() {
  // @@protoc_insertion_point(field_mutable_map:google.protobuf.testing.BoolToString.map)
  return map_.MutableMap();
}

inline const BoolToString* BoolToString::internal_default_instance() {
  return &BoolToString_default_instance_.get();
}
#endif  // PROTOBUF_INLINE_NOT_IN_HEADERS

// ===================================================================

#if !defined(_MSC_VER) || _MSC_VER >= 1900
const int Mixed1::kMsgFieldNumber;
const int Mixed1::kMapFieldNumber;
#endif  // !defined(_MSC_VER) || _MSC_VER >= 1900

Mixed1::Mixed1()
  : ::google::protobuf::Message(), _internal_metadata_(NULL) {
  if (this != internal_default_instance()) protobuf_InitDefaults_google_2fprotobuf_2futil_2finternal_2ftestdata_2fmaps_2eproto();
  SharedCtor();
  // @@protoc_insertion_point(constructor:google.protobuf.testing.Mixed1)
}

void Mixed1::InitAsDefaultInstance() {
}

Mixed1::Mixed1(const Mixed1& from)
  : ::google::protobuf::Message(),
    _internal_metadata_(NULL) {
  SharedCtor();
  UnsafeMergeFrom(from);
  // @@protoc_insertion_point(copy_constructor:google.protobuf.testing.Mixed1)
}

void Mixed1::SharedCtor() {
  map_.SetAssignDescriptorCallback(
      protobuf_AssignDescriptorsOnce);
  map_.SetEntryDescriptor(
      &::google::protobuf::testing::Mixed1_MapEntry_descriptor_);
  msg_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  _cached_size_ = 0;
}

Mixed1::~Mixed1() {
  // @@protoc_insertion_point(destructor:google.protobuf.testing.Mixed1)
  SharedDtor();
}

void Mixed1::SharedDtor() {
  msg_.DestroyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}

void Mixed1::SetCachedSize(int size) const {
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
}
const ::google::protobuf::Descriptor* Mixed1::descriptor() {
  protobuf_AssignDescriptorsOnce();
  return Mixed1_descriptor_;
}

const Mixed1& Mixed1::default_instance() {
  protobuf_InitDefaults_google_2fprotobuf_2futil_2finternal_2ftestdata_2fmaps_2eproto();
  return *internal_default_instance();
}

::google::protobuf::internal::ExplicitlyConstructed<Mixed1> Mixed1_default_instance_;

Mixed1* Mixed1::New(::google::protobuf::Arena* arena) const {
  Mixed1* n = new Mixed1;
  if (arena != NULL) {
    arena->Own(n);
  }
  return n;
}

void Mixed1::Clear() {
// @@protoc_insertion_point(message_clear_start:google.protobuf.testing.Mixed1)
  msg_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  map_.Clear();
}

bool Mixed1::MergePartialFromCodedStream(
    ::google::protobuf::io::CodedInputStream* input) {
#define DO_(EXPRESSION) if (!GOOGLE_PREDICT_TRUE(EXPRESSION)) goto failure
  ::google::protobuf::uint32 tag;
  // @@protoc_insertion_point(parse_start:google.protobuf.testing.Mixed1)
  for (;;) {
    ::std::pair< ::google::protobuf::uint32, bool> p = input->ReadTagWithCutoff(127);
    tag = p.first;
    if (!p.second) goto handle_unusual;
    switch (::google::protobuf::internal::WireFormatLite::GetTagFieldNumber(tag)) {
      // optional string msg = 1;
      case 1: {
        if (tag == 10) {
          DO_(::google::protobuf::internal::WireFormatLite::ReadString(
                input, this->mutable_msg()));
          DO_(::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
            this->msg().data(), this->msg().length(),
            ::google::protobuf::internal::WireFormatLite::PARSE,
            "google.protobuf.testing.Mixed1.msg"));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(18)) goto parse_map;
        break;
      }

      // map<string, float> map = 2;
      case 2: {
        if (tag == 18) {
         parse_map:
          DO_(input->IncrementRecursionDepth());
         parse_loop_map:
          Mixed1_MapEntry::Parser< ::google::protobuf::internal::MapField<
              ::std::string, float,
              ::google::protobuf::internal::WireFormatLite::TYPE_STRING,
              ::google::protobuf::internal::WireFormatLite::TYPE_FLOAT,
              0 >,
            ::google::protobuf::Map< ::std::string, float > > parser(&map_);
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessageNoVirtual(
              input, &parser));
          DO_(::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
            parser.key().data(), parser.key().length(),
            ::google::protobuf::internal::WireFormatLite::PARSE,
            "google.protobuf.testing.Mixed1.MapEntry.key"));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(18)) goto parse_loop_map;
        input->UnsafeDecrementRecursionDepth();
        if (input->ExpectAtEnd()) goto success;
        break;
      }

      default: {
      handle_unusual:
        if (tag == 0 ||
            ::google::protobuf::internal::WireFormatLite::GetTagWireType(tag) ==
            ::google::protobuf::internal::WireFormatLite::WIRETYPE_END_GROUP) {
          goto success;
        }
        DO_(::google::protobuf::internal::WireFormatLite::SkipField(input, tag));
        break;
      }
    }
  }
success:
  // @@protoc_insertion_point(parse_success:google.protobuf.testing.Mixed1)
  return true;
failure:
  // @@protoc_insertion_point(parse_failure:google.protobuf.testing.Mixed1)
  return false;
#undef DO_
}

void Mixed1::SerializeWithCachedSizes(
    ::google::protobuf::io::CodedOutputStream* output) const {
  // @@protoc_insertion_point(serialize_start:google.protobuf.testing.Mixed1)
  // optional string msg = 1;
  if (this->msg().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->msg().data(), this->msg().length(),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "google.protobuf.testing.Mixed1.msg");
    ::google::protobuf::internal::WireFormatLite::WriteStringMaybeAliased(
      1, this->msg(), output);
  }

  // map<string, float> map = 2;
  if (!this->map().empty()) {
    typedef ::google::protobuf::Map< ::std::string, float >::const_pointer
        ConstPtr;
    typedef ConstPtr SortItem;
    typedef ::google::protobuf::internal::CompareByDerefFirst<SortItem> Less;
    struct Utf8Check {
      static void Check(ConstPtr p) {
        ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
          p->first.data(), p->first.length(),
          ::google::protobuf::internal::WireFormatLite::SERIALIZE,
          "google.protobuf.testing.Mixed1.MapEntry.key");
      }
    };

    if (output->IsSerializationDeterminstic() &&
        this->map().size() > 1) {
      ::google::protobuf::scoped_array<SortItem> items(
          new SortItem[this->map().size()]);
      typedef ::google::protobuf::Map< ::std::string, float >::size_type size_type;
      size_type n = 0;
      for (::google::protobuf::Map< ::std::string, float >::const_iterator
          it = this->map().begin();
          it != this->map().end(); ++it, ++n) {
        items[n] = SortItem(&*it);
      }
      ::std::sort(&items[0], &items[n], Less());
      ::google::protobuf::scoped_ptr<Mixed1_MapEntry> entry;
      for (size_type i = 0; i < n; i++) {
        entry.reset(map_.NewEntryWrapper(
            items[i]->first, items[i]->second));
        ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
            2, *entry, output);
        Utf8Check::Check(items[i]);
      }
    } else {
      ::google::protobuf::scoped_ptr<Mixed1_MapEntry> entry;
      for (::google::protobuf::Map< ::std::string, float >::const_iterator
          it = this->map().begin();
          it != this->map().end(); ++it) {
        entry.reset(map_.NewEntryWrapper(
            it->first, it->second));
        ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
            2, *entry, output);
        Utf8Check::Check(&*it);
      }
    }
  }

  // @@protoc_insertion_point(serialize_end:google.protobuf.testing.Mixed1)
}

::google::protobuf::uint8* Mixed1::InternalSerializeWithCachedSizesToArray(
    bool deterministic, ::google::protobuf::uint8* target) const {
  (void)deterministic; // Unused
  // @@protoc_insertion_point(serialize_to_array_start:google.protobuf.testing.Mixed1)
  // optional string msg = 1;
  if (this->msg().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->msg().data(), this->msg().length(),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "google.protobuf.testing.Mixed1.msg");
    target =
      ::google::protobuf::internal::WireFormatLite::WriteStringToArray(
        1, this->msg(), target);
  }

  // map<string, float> map = 2;
  if (!this->map().empty()) {
    typedef ::google::protobuf::Map< ::std::string, float >::const_pointer
        ConstPtr;
    typedef ConstPtr SortItem;
    typedef ::google::protobuf::internal::CompareByDerefFirst<SortItem> Less;
    struct Utf8Check {
      static void Check(ConstPtr p) {
        ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
          p->first.data(), p->first.length(),
          ::google::protobuf::internal::WireFormatLite::SERIALIZE,
          "google.protobuf.testing.Mixed1.MapEntry.key");
      }
    };

    if (deterministic &&
        this->map().size() > 1) {
      ::google::protobuf::scoped_array<SortItem> items(
          new SortItem[this->map().size()]);
      typedef ::google::protobuf::Map< ::std::string, float >::size_type size_type;
      size_type n = 0;
      for (::google::protobuf::Map< ::std::string, float >::const_iterator
          it = this->map().begin();
          it != this->map().end(); ++it, ++n) {
        items[n] = SortItem(&*it);
      }
      ::std::sort(&items[0], &items[n], Less());
      ::google::protobuf::scoped_ptr<Mixed1_MapEntry> entry;
      for (size_type i = 0; i < n; i++) {
        entry.reset(map_.NewEntryWrapper(
            items[i]->first, items[i]->second));
        target = ::google::protobuf::internal::WireFormatLite::
                   InternalWriteMessageNoVirtualToArray(
                       2, *entry, deterministic, target);
;
        Utf8Check::Check(items[i]);
      }
    } else {
      ::google::protobuf::scoped_ptr<Mixed1_MapEntry> entry;
      for (::google::protobuf::Map< ::std::string, float >::const_iterator
          it = this->map().begin();
          it != this->map().end(); ++it) {
        entry.reset(map_.NewEntryWrapper(
            it->first, it->second));
        target = ::google::protobuf::internal::WireFormatLite::
                   InternalWriteMessageNoVirtualToArray(
                       2, *entry, deterministic, target);
;
        Utf8Check::Check(&*it);
      }
    }
  }

  // @@protoc_insertion_point(serialize_to_array_end:google.protobuf.testing.Mixed1)
  return target;
}

size_t Mixed1::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:google.protobuf.testing.Mixed1)
  size_t total_size = 0;

  // optional string msg = 1;
  if (this->msg().size() > 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::StringSize(
        this->msg());
  }

  // map<string, float> map = 2;
  total_size += 1 *
      ::google::protobuf::internal::FromIntSize(this->map_size());
  {
    ::google::protobuf::scoped_ptr<Mixed1_MapEntry> entry;
    for (::google::protobuf::Map< ::std::string, float >::const_iterator
        it = this->map().begin();
        it != this->map().end(); ++it) {
      entry.reset(map_.NewEntryWrapper(it->first, it->second));
      total_size += ::google::protobuf::internal::WireFormatLite::
          MessageSizeNoVirtual(*entry);
    }
  }

  int cached_size = ::google::protobuf::internal::ToCachedSize(total_size);
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = cached_size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
  return total_size;
}

void Mixed1::MergeFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_merge_from_start:google.protobuf.testing.Mixed1)
  if (GOOGLE_PREDICT_FALSE(&from == this)) MergeFromFail(__LINE__);
  const Mixed1* source =
      ::google::protobuf::internal::DynamicCastToGenerated<const Mixed1>(
          &from);
  if (source == NULL) {
  // @@protoc_insertion_point(generalized_merge_from_cast_fail:google.protobuf.testing.Mixed1)
    ::google::protobuf::internal::ReflectionOps::Merge(from, this);
  } else {
  // @@protoc_insertion_point(generalized_merge_from_cast_success:google.protobuf.testing.Mixed1)
    UnsafeMergeFrom(*source);
  }
}

void Mixed1::MergeFrom(const Mixed1& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:google.protobuf.testing.Mixed1)
  if (GOOGLE_PREDICT_TRUE(&from != this)) {
    UnsafeMergeFrom(from);
  } else {
    MergeFromFail(__LINE__);
  }
}

void Mixed1::UnsafeMergeFrom(const Mixed1& from) {
  GOOGLE_DCHECK(&from != this);
  map_.MergeFrom(from.map_);
  if (from.msg().size() > 0) {

    msg_.AssignWithDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), from.msg_);
  }
}

void Mixed1::CopyFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_copy_from_start:google.protobuf.testing.Mixed1)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

void Mixed1::CopyFrom(const Mixed1& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:google.protobuf.testing.Mixed1)
  if (&from == this) return;
  Clear();
  UnsafeMergeFrom(from);
}

bool Mixed1::IsInitialized() const {

  return true;
}

void Mixed1::Swap(Mixed1* other) {
  if (other == this) return;
  InternalSwap(other);
}
void Mixed1::InternalSwap(Mixed1* other) {
  msg_.Swap(&other->msg_);
  map_.Swap(&other->map_);
  _internal_metadata_.Swap(&other->_internal_metadata_);
  std::swap(_cached_size_, other->_cached_size_);
}

::google::protobuf::Metadata Mixed1::GetMetadata() const {
  protobuf_AssignDescriptorsOnce();
  ::google::protobuf::Metadata metadata;
  metadata.descriptor = Mixed1_descriptor_;
  metadata.reflection = Mixed1_reflection_;
  return metadata;
}

#if PROTOBUF_INLINE_NOT_IN_HEADERS
// Mixed1

// optional string msg = 1;
void Mixed1::clear_msg() {
  msg_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
const ::std::string& Mixed1::msg() const {
  // @@protoc_insertion_point(field_get:google.protobuf.testing.Mixed1.msg)
  return msg_.GetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
void Mixed1::set_msg(const ::std::string& value) {
  
  msg_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:google.protobuf.testing.Mixed1.msg)
}
void Mixed1::set_msg(const char* value) {
  
  msg_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:google.protobuf.testing.Mixed1.msg)
}
void Mixed1::set_msg(const char* value, size_t size) {
  
  msg_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:google.protobuf.testing.Mixed1.msg)
}
::std::string* Mixed1::mutable_msg() {
  
  // @@protoc_insertion_point(field_mutable:google.protobuf.testing.Mixed1.msg)
  return msg_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
::std::string* Mixed1::release_msg() {
  // @@protoc_insertion_point(field_release:google.protobuf.testing.Mixed1.msg)
  
  return msg_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
void Mixed1::set_allocated_msg(::std::string* msg) {
  if (msg != NULL) {
    
  } else {
    
  }
  msg_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), msg);
  // @@protoc_insertion_point(field_set_allocated:google.protobuf.testing.Mixed1.msg)
}

// map<string, float> map = 2;
int Mixed1::map_size() const {
  return map_.size();
}
void Mixed1::clear_map() {
  map_.Clear();
}
 const ::google::protobuf::Map< ::std::string, float >&
Mixed1::map() const {
  // @@protoc_insertion_point(field_map:google.protobuf.testing.Mixed1.map)
  return map_.GetMap();
}
 ::google::protobuf::Map< ::std::string, float >*
Mixed1::mutable_map() {
  // @@protoc_insertion_point(field_mutable_map:google.protobuf.testing.Mixed1.map)
  return map_.MutableMap();
}

inline const Mixed1* Mixed1::internal_default_instance() {
  return &Mixed1_default_instance_.get();
}
#endif  // PROTOBUF_INLINE_NOT_IN_HEADERS

// ===================================================================

const ::google::protobuf::EnumDescriptor* Mixed2_E_descriptor() {
  protobuf_AssignDescriptorsOnce();
  return Mixed2_E_descriptor_;
}
bool Mixed2_E_IsValid(int value) {
  switch (value) {
    case 0:
    case 1:
    case 2:
    case 3:
      return true;
    default:
      return false;
  }
}

#if !defined(_MSC_VER) || _MSC_VER >= 1900
const Mixed2_E Mixed2::E0;
const Mixed2_E Mixed2::E1;
const Mixed2_E Mixed2::E2;
const Mixed2_E Mixed2::E3;
const Mixed2_E Mixed2::E_MIN;
const Mixed2_E Mixed2::E_MAX;
const int Mixed2::E_ARRAYSIZE;
#endif  // !defined(_MSC_VER) || _MSC_VER >= 1900
#if !defined(_MSC_VER) || _MSC_VER >= 1900
const int Mixed2::kMapFieldNumber;
const int Mixed2::kEeFieldNumber;
#endif  // !defined(_MSC_VER) || _MSC_VER >= 1900

Mixed2::Mixed2()
  : ::google::protobuf::Message(), _internal_metadata_(NULL) {
  if (this != internal_default_instance()) protobuf_InitDefaults_google_2fprotobuf_2futil_2finternal_2ftestdata_2fmaps_2eproto();
  SharedCtor();
  // @@protoc_insertion_point(constructor:google.protobuf.testing.Mixed2)
}

void Mixed2::InitAsDefaultInstance() {
}

Mixed2::Mixed2(const Mixed2& from)
  : ::google::protobuf::Message(),
    _internal_metadata_(NULL) {
  SharedCtor();
  UnsafeMergeFrom(from);
  // @@protoc_insertion_point(copy_constructor:google.protobuf.testing.Mixed2)
}

void Mixed2::SharedCtor() {
  map_.SetAssignDescriptorCallback(
      protobuf_AssignDescriptorsOnce);
  map_.SetEntryDescriptor(
      &::google::protobuf::testing::Mixed2_MapEntry_descriptor_);
  ee_ = 0;
  _cached_size_ = 0;
}

Mixed2::~Mixed2() {
  // @@protoc_insertion_point(destructor:google.protobuf.testing.Mixed2)
  SharedDtor();
}

void Mixed2::SharedDtor() {
}

void Mixed2::SetCachedSize(int size) const {
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
}
const ::google::protobuf::Descriptor* Mixed2::descriptor() {
  protobuf_AssignDescriptorsOnce();
  return Mixed2_descriptor_;
}

const Mixed2& Mixed2::default_instance() {
  protobuf_InitDefaults_google_2fprotobuf_2futil_2finternal_2ftestdata_2fmaps_2eproto();
  return *internal_default_instance();
}

::google::protobuf::internal::ExplicitlyConstructed<Mixed2> Mixed2_default_instance_;

Mixed2* Mixed2::New(::google::protobuf::Arena* arena) const {
  Mixed2* n = new Mixed2;
  if (arena != NULL) {
    arena->Own(n);
  }
  return n;
}

void Mixed2::Clear() {
// @@protoc_insertion_point(message_clear_start:google.protobuf.testing.Mixed2)
  ee_ = 0;
  map_.Clear();
}

bool Mixed2::MergePartialFromCodedStream(
    ::google::protobuf::io::CodedInputStream* input) {
#define DO_(EXPRESSION) if (!GOOGLE_PREDICT_TRUE(EXPRESSION)) goto failure
  ::google::protobuf::uint32 tag;
  // @@protoc_insertion_point(parse_start:google.protobuf.testing.Mixed2)
  for (;;) {
    ::std::pair< ::google::protobuf::uint32, bool> p = input->ReadTagWithCutoff(127);
    tag = p.first;
    if (!p.second) goto handle_unusual;
    switch (::google::protobuf::internal::WireFormatLite::GetTagFieldNumber(tag)) {
      // map<int32, bool> map = 1;
      case 1: {
        if (tag == 10) {
          DO_(input->IncrementRecursionDepth());
         parse_loop_map:
          Mixed2_MapEntry::Parser< ::google::protobuf::internal::MapField<
              ::google::protobuf::int32, bool,
              ::google::protobuf::internal::WireFormatLite::TYPE_INT32,
              ::google::protobuf::internal::WireFormatLite::TYPE_BOOL,
              0 >,
            ::google::protobuf::Map< ::google::protobuf::int32, bool > > parser(&map_);
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessageNoVirtual(
              input, &parser));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(10)) goto parse_loop_map;
        input->UnsafeDecrementRecursionDepth();
        if (input->ExpectTag(16)) goto parse_ee;
        break;
      }

      // optional .google.protobuf.testing.Mixed2.E ee = 2;
      case 2: {
        if (tag == 16) {
         parse_ee:
          int value;
          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   int, ::google::protobuf::internal::WireFormatLite::TYPE_ENUM>(
                 input, &value)));
          set_ee(static_cast< ::google::protobuf::testing::Mixed2_E >(value));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectAtEnd()) goto success;
        break;
      }

      default: {
      handle_unusual:
        if (tag == 0 ||
            ::google::protobuf::internal::WireFormatLite::GetTagWireType(tag) ==
            ::google::protobuf::internal::WireFormatLite::WIRETYPE_END_GROUP) {
          goto success;
        }
        DO_(::google::protobuf::internal::WireFormatLite::SkipField(input, tag));
        break;
      }
    }
  }
success:
  // @@protoc_insertion_point(parse_success:google.protobuf.testing.Mixed2)
  return true;
failure:
  // @@protoc_insertion_point(parse_failure:google.protobuf.testing.Mixed2)
  return false;
#undef DO_
}

void Mixed2::SerializeWithCachedSizes(
    ::google::protobuf::io::CodedOutputStream* output) const {
  // @@protoc_insertion_point(serialize_start:google.protobuf.testing.Mixed2)
  // map<int32, bool> map = 1;
  if (!this->map().empty()) {
    typedef ::google::protobuf::Map< ::google::protobuf::int32, bool >::const_pointer
        ConstPtr;
    typedef ::google::protobuf::internal::SortItem< ::google::protobuf::int32, ConstPtr > SortItem;
    typedef ::google::protobuf::internal::CompareByFirstField<SortItem> Less;

    if (output->IsSerializationDeterminstic() &&
        this->map().size() > 1) {
      ::google::protobuf::scoped_array<SortItem> items(
          new SortItem[this->map().size()]);
      typedef ::google::protobuf::Map< ::google::protobuf::int32, bool >::size_type size_type;
      size_type n = 0;
      for (::google::protobuf::Map< ::google::protobuf::int32, bool >::const_iterator
          it = this->map().begin();
          it != this->map().end(); ++it, ++n) {
        items[n] = SortItem(&*it);
      }
      ::std::sort(&items[0], &items[n], Less());
      ::google::protobuf::scoped_ptr<Mixed2_MapEntry> entry;
      for (size_type i = 0; i < n; i++) {
        entry.reset(map_.NewEntryWrapper(
            items[i].second->first, items[i].second->second));
        ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
            1, *entry, output);
      }
    } else {
      ::google::protobuf::scoped_ptr<Mixed2_MapEntry> entry;
      for (::google::protobuf::Map< ::google::protobuf::int32, bool >::const_iterator
          it = this->map().begin();
          it != this->map().end(); ++it) {
        entry.reset(map_.NewEntryWrapper(
            it->first, it->second));
        ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
            1, *entry, output);
      }
    }
  }

  // optional .google.protobuf.testing.Mixed2.E ee = 2;
  if (this->ee() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteEnum(
      2, this->ee(), output);
  }

  // @@protoc_insertion_point(serialize_end:google.protobuf.testing.Mixed2)
}

::google::protobuf::uint8* Mixed2::InternalSerializeWithCachedSizesToArray(
    bool deterministic, ::google::protobuf::uint8* target) const {
  (void)deterministic; // Unused
  // @@protoc_insertion_point(serialize_to_array_start:google.protobuf.testing.Mixed2)
  // map<int32, bool> map = 1;
  if (!this->map().empty()) {
    typedef ::google::protobuf::Map< ::google::protobuf::int32, bool >::const_pointer
        ConstPtr;
    typedef ::google::protobuf::internal::SortItem< ::google::protobuf::int32, ConstPtr > SortItem;
    typedef ::google::protobuf::internal::CompareByFirstField<SortItem> Less;

    if (deterministic &&
        this->map().size() > 1) {
      ::google::protobuf::scoped_array<SortItem> items(
          new SortItem[this->map().size()]);
      typedef ::google::protobuf::Map< ::google::protobuf::int32, bool >::size_type size_type;
      size_type n = 0;
      for (::google::protobuf::Map< ::google::protobuf::int32, bool >::const_iterator
          it = this->map().begin();
          it != this->map().end(); ++it, ++n) {
        items[n] = SortItem(&*it);
      }
      ::std::sort(&items[0], &items[n], Less());
      ::google::protobuf::scoped_ptr<Mixed2_MapEntry> entry;
      for (size_type i = 0; i < n; i++) {
        entry.reset(map_.NewEntryWrapper(
            items[i].second->first, items[i].second->second));
        target = ::google::protobuf::internal::WireFormatLite::
                   InternalWriteMessageNoVirtualToArray(
                       1, *entry, deterministic, target);
;
      }
    } else {
      ::google::protobuf::scoped_ptr<Mixed2_MapEntry> entry;
      for (::google::protobuf::Map< ::google::protobuf::int32, bool >::const_iterator
          it = this->map().begin();
          it != this->map().end(); ++it) {
        entry.reset(map_.NewEntryWrapper(
            it->first, it->second));
        target = ::google::protobuf::internal::WireFormatLite::
                   InternalWriteMessageNoVirtualToArray(
                       1, *entry, deterministic, target);
;
      }
    }
  }

  // optional .google.protobuf.testing.Mixed2.E ee = 2;
  if (this->ee() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteEnumToArray(
      2, this->ee(), target);
  }

  // @@protoc_insertion_point(serialize_to_array_end:google.protobuf.testing.Mixed2)
  return target;
}

size_t Mixed2::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:google.protobuf.testing.Mixed2)
  size_t total_size = 0;

  // optional .google.protobuf.testing.Mixed2.E ee = 2;
  if (this->ee() != 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::EnumSize(this->ee());
  }

  // map<int32, bool> map = 1;
  total_size += 1 *
      ::google::protobuf::internal::FromIntSize(this->map_size());
  {
    ::google::protobuf::scoped_ptr<Mixed2_MapEntry> entry;
    for (::google::protobuf::Map< ::google::protobuf::int32, bool >::const_iterator
        it = this->map().begin();
        it != this->map().end(); ++it) {
      entry.reset(map_.NewEntryWrapper(it->first, it->second));
      total_size += ::google::protobuf::internal::WireFormatLite::
          MessageSizeNoVirtual(*entry);
    }
  }

  int cached_size = ::google::protobuf::internal::ToCachedSize(total_size);
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = cached_size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
  return total_size;
}

void Mixed2::MergeFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_merge_from_start:google.protobuf.testing.Mixed2)
  if (GOOGLE_PREDICT_FALSE(&from == this)) MergeFromFail(__LINE__);
  const Mixed2* source =
      ::google::protobuf::internal::DynamicCastToGenerated<const Mixed2>(
          &from);
  if (source == NULL) {
  // @@protoc_insertion_point(generalized_merge_from_cast_fail:google.protobuf.testing.Mixed2)
    ::google::protobuf::internal::ReflectionOps::Merge(from, this);
  } else {
  // @@protoc_insertion_point(generalized_merge_from_cast_success:google.protobuf.testing.Mixed2)
    UnsafeMergeFrom(*source);
  }
}

void Mixed2::MergeFrom(const Mixed2& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:google.protobuf.testing.Mixed2)
  if (GOOGLE_PREDICT_TRUE(&from != this)) {
    UnsafeMergeFrom(from);
  } else {
    MergeFromFail(__LINE__);
  }
}

void Mixed2::UnsafeMergeFrom(const Mixed2& from) {
  GOOGLE_DCHECK(&from != this);
  map_.MergeFrom(from.map_);
  if (from.ee() != 0) {
    set_ee(from.ee());
  }
}

void Mixed2::CopyFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_copy_from_start:google.protobuf.testing.Mixed2)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

void Mixed2::CopyFrom(const Mixed2& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:google.protobuf.testing.Mixed2)
  if (&from == this) return;
  Clear();
  UnsafeMergeFrom(from);
}

bool Mixed2::IsInitialized() const {

  return true;
}

void Mixed2::Swap(Mixed2* other) {
  if (other == this) return;
  InternalSwap(other);
}
void Mixed2::InternalSwap(Mixed2* other) {
  map_.Swap(&other->map_);
  std::swap(ee_, other->ee_);
  _internal_metadata_.Swap(&other->_internal_metadata_);
  std::swap(_cached_size_, other->_cached_size_);
}

::google::protobuf::Metadata Mixed2::GetMetadata() const {
  protobuf_AssignDescriptorsOnce();
  ::google::protobuf::Metadata metadata;
  metadata.descriptor = Mixed2_descriptor_;
  metadata.reflection = Mixed2_reflection_;
  return metadata;
}

#if PROTOBUF_INLINE_NOT_IN_HEADERS
// Mixed2

// map<int32, bool> map = 1;
int Mixed2::map_size() const {
  return map_.size();
}
void Mixed2::clear_map() {
  map_.Clear();
}
 const ::google::protobuf::Map< ::google::protobuf::int32, bool >&
Mixed2::map() const {
  // @@protoc_insertion_point(field_map:google.protobuf.testing.Mixed2.map)
  return map_.GetMap();
}
 ::google::protobuf::Map< ::google::protobuf::int32, bool >*
Mixed2::mutable_map() {
  // @@protoc_insertion_point(field_mutable_map:google.protobuf.testing.Mixed2.map)
  return map_.MutableMap();
}

// optional .google.protobuf.testing.Mixed2.E ee = 2;
void Mixed2::clear_ee() {
  ee_ = 0;
}
::google::protobuf::testing::Mixed2_E Mixed2::ee() const {
  // @@protoc_insertion_point(field_get:google.protobuf.testing.Mixed2.ee)
  return static_cast< ::google::protobuf::testing::Mixed2_E >(ee_);
}
void Mixed2::set_ee(::google::protobuf::testing::Mixed2_E value) {
  
  ee_ = value;
  // @@protoc_insertion_point(field_set:google.protobuf.testing.Mixed2.ee)
}

inline const Mixed2* Mixed2::internal_default_instance() {
  return &Mixed2_default_instance_.get();
}
#endif  // PROTOBUF_INLINE_NOT_IN_HEADERS

// ===================================================================

#if !defined(_MSC_VER) || _MSC_VER >= 1900
const int MapOfObjects_M::kInnerTextFieldNumber;
#endif  // !defined(_MSC_VER) || _MSC_VER >= 1900

MapOfObjects_M::MapOfObjects_M()
  : ::google::protobuf::Message(), _internal_metadata_(NULL) {
  if (this != internal_default_instance()) protobuf_InitDefaults_google_2fprotobuf_2futil_2finternal_2ftestdata_2fmaps_2eproto();
  SharedCtor();
  // @@protoc_insertion_point(constructor:google.protobuf.testing.MapOfObjects.M)
}

void MapOfObjects_M::InitAsDefaultInstance() {
}

MapOfObjects_M::MapOfObjects_M(const MapOfObjects_M& from)
  : ::google::protobuf::Message(),
    _internal_metadata_(NULL) {
  SharedCtor();
  UnsafeMergeFrom(from);
  // @@protoc_insertion_point(copy_constructor:google.protobuf.testing.MapOfObjects.M)
}

void MapOfObjects_M::SharedCtor() {
  inner_text_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  _cached_size_ = 0;
}

MapOfObjects_M::~MapOfObjects_M() {
  // @@protoc_insertion_point(destructor:google.protobuf.testing.MapOfObjects.M)
  SharedDtor();
}

void MapOfObjects_M::SharedDtor() {
  inner_text_.DestroyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}

void MapOfObjects_M::SetCachedSize(int size) const {
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
}
const ::google::protobuf::Descriptor* MapOfObjects_M::descriptor() {
  protobuf_AssignDescriptorsOnce();
  return MapOfObjects_M_descriptor_;
}

const MapOfObjects_M& MapOfObjects_M::default_instance() {
  protobuf_InitDefaults_google_2fprotobuf_2futil_2finternal_2ftestdata_2fmaps_2eproto();
  return *internal_default_instance();
}

::google::protobuf::internal::ExplicitlyConstructed<MapOfObjects_M> MapOfObjects_M_default_instance_;

MapOfObjects_M* MapOfObjects_M::New(::google::protobuf::Arena* arena) const {
  MapOfObjects_M* n = new MapOfObjects_M;
  if (arena != NULL) {
    arena->Own(n);
  }
  return n;
}

void MapOfObjects_M::Clear() {
// @@protoc_insertion_point(message_clear_start:google.protobuf.testing.MapOfObjects.M)
  inner_text_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}

bool MapOfObjects_M::MergePartialFromCodedStream(
    ::google::protobuf::io::CodedInputStream* input) {
#define DO_(EXPRESSION) if (!GOOGLE_PREDICT_TRUE(EXPRESSION)) goto failure
  ::google::protobuf::uint32 tag;
  // @@protoc_insertion_point(parse_start:google.protobuf.testing.MapOfObjects.M)
  for (;;) {
    ::std::pair< ::google::protobuf::uint32, bool> p = input->ReadTagWithCutoff(127);
    tag = p.first;
    if (!p.second) goto handle_unusual;
    switch (::google::protobuf::internal::WireFormatLite::GetTagFieldNumber(tag)) {
      // optional string inner_text = 1;
      case 1: {
        if (tag == 10) {
          DO_(::google::protobuf::internal::WireFormatLite::ReadString(
                input, this->mutable_inner_text()));
          DO_(::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
            this->inner_text().data(), this->inner_text().length(),
            ::google::protobuf::internal::WireFormatLite::PARSE,
            "google.protobuf.testing.MapOfObjects.M.inner_text"));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectAtEnd()) goto success;
        break;
      }

      default: {
      handle_unusual:
        if (tag == 0 ||
            ::google::protobuf::internal::WireFormatLite::GetTagWireType(tag) ==
            ::google::protobuf::internal::WireFormatLite::WIRETYPE_END_GROUP) {
          goto success;
        }
        DO_(::google::protobuf::internal::WireFormatLite::SkipField(input, tag));
        break;
      }
    }
  }
success:
  // @@protoc_insertion_point(parse_success:google.protobuf.testing.MapOfObjects.M)
  return true;
failure:
  // @@protoc_insertion_point(parse_failure:google.protobuf.testing.MapOfObjects.M)
  return false;
#undef DO_
}

void MapOfObjects_M::SerializeWithCachedSizes(
    ::google::protobuf::io::CodedOutputStream* output) const {
  // @@protoc_insertion_point(serialize_start:google.protobuf.testing.MapOfObjects.M)
  // optional string inner_text = 1;
  if (this->inner_text().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->inner_text().data(), this->inner_text().length(),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "google.protobuf.testing.MapOfObjects.M.inner_text");
    ::google::protobuf::internal::WireFormatLite::WriteStringMaybeAliased(
      1, this->inner_text(), output);
  }

  // @@protoc_insertion_point(serialize_end:google.protobuf.testing.MapOfObjects.M)
}

::google::protobuf::uint8* MapOfObjects_M::InternalSerializeWithCachedSizesToArray(
    bool deterministic, ::google::protobuf::uint8* target) const {
  (void)deterministic; // Unused
  // @@protoc_insertion_point(serialize_to_array_start:google.protobuf.testing.MapOfObjects.M)
  // optional string inner_text = 1;
  if (this->inner_text().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->inner_text().data(), this->inner_text().length(),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "google.protobuf.testing.MapOfObjects.M.inner_text");
    target =
      ::google::protobuf::internal::WireFormatLite::WriteStringToArray(
        1, this->inner_text(), target);
  }

  // @@protoc_insertion_point(serialize_to_array_end:google.protobuf.testing.MapOfObjects.M)
  return target;
}

size_t MapOfObjects_M::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:google.protobuf.testing.MapOfObjects.M)
  size_t total_size = 0;

  // optional string inner_text = 1;
  if (this->inner_text().size() > 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::StringSize(
        this->inner_text());
  }

  int cached_size = ::google::protobuf::internal::ToCachedSize(total_size);
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = cached_size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
  return total_size;
}

void MapOfObjects_M::MergeFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_merge_from_start:google.protobuf.testing.MapOfObjects.M)
  if (GOOGLE_PREDICT_FALSE(&from == this)) MergeFromFail(__LINE__);
  const MapOfObjects_M* source =
      ::google::protobuf::internal::DynamicCastToGenerated<const MapOfObjects_M>(
          &from);
  if (source == NULL) {
  // @@protoc_insertion_point(generalized_merge_from_cast_fail:google.protobuf.testing.MapOfObjects.M)
    ::google::protobuf::internal::ReflectionOps::Merge(from, this);
  } else {
  // @@protoc_insertion_point(generalized_merge_from_cast_success:google.protobuf.testing.MapOfObjects.M)
    UnsafeMergeFrom(*source);
  }
}

void MapOfObjects_M::MergeFrom(const MapOfObjects_M& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:google.protobuf.testing.MapOfObjects.M)
  if (GOOGLE_PREDICT_TRUE(&from != this)) {
    UnsafeMergeFrom(from);
  } else {
    MergeFromFail(__LINE__);
  }
}

void MapOfObjects_M::UnsafeMergeFrom(const MapOfObjects_M& from) {
  GOOGLE_DCHECK(&from != this);
  if (from.inner_text().size() > 0) {

    inner_text_.AssignWithDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), from.inner_text_);
  }
}

void MapOfObjects_M::CopyFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_copy_from_start:google.protobuf.testing.MapOfObjects.M)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

void MapOfObjects_M::CopyFrom(const MapOfObjects_M& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:google.protobuf.testing.MapOfObjects.M)
  if (&from == this) return;
  Clear();
  UnsafeMergeFrom(from);
}

bool MapOfObjects_M::IsInitialized() const {

  return true;
}

void MapOfObjects_M::Swap(MapOfObjects_M* other) {
  if (other == this) return;
  InternalSwap(other);
}
void MapOfObjects_M::InternalSwap(MapOfObjects_M* other) {
  inner_text_.Swap(&other->inner_text_);
  _internal_metadata_.Swap(&other->_internal_metadata_);
  std::swap(_cached_size_, other->_cached_size_);
}

::google::protobuf::Metadata MapOfObjects_M::GetMetadata() const {
  protobuf_AssignDescriptorsOnce();
  ::google::protobuf::Metadata metadata;
  metadata.descriptor = MapOfObjects_M_descriptor_;
  metadata.reflection = MapOfObjects_M_reflection_;
  return metadata;
}


// -------------------------------------------------------------------

#if !defined(_MSC_VER) || _MSC_VER >= 1900
const int MapOfObjects::kMapFieldNumber;
#endif  // !defined(_MSC_VER) || _MSC_VER >= 1900

MapOfObjects::MapOfObjects()
  : ::google::protobuf::Message(), _internal_metadata_(NULL) {
  if (this != internal_default_instance()) protobuf_InitDefaults_google_2fprotobuf_2futil_2finternal_2ftestdata_2fmaps_2eproto();
  SharedCtor();
  // @@protoc_insertion_point(constructor:google.protobuf.testing.MapOfObjects)
}

void MapOfObjects::InitAsDefaultInstance() {
}

MapOfObjects::MapOfObjects(const MapOfObjects& from)
  : ::google::protobuf::Message(),
    _internal_metadata_(NULL) {
  SharedCtor();
  UnsafeMergeFrom(from);
  // @@protoc_insertion_point(copy_constructor:google.protobuf.testing.MapOfObjects)
}

void MapOfObjects::SharedCtor() {
  map_.SetAssignDescriptorCallback(
      protobuf_AssignDescriptorsOnce);
  map_.SetEntryDescriptor(
      &::google::protobuf::testing::MapOfObjects_MapEntry_descriptor_);
  _cached_size_ = 0;
}

MapOfObjects::~MapOfObjects() {
  // @@protoc_insertion_point(destructor:google.protobuf.testing.MapOfObjects)
  SharedDtor();
}

void MapOfObjects::SharedDtor() {
}

void MapOfObjects::SetCachedSize(int size) const {
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
}
const ::google::protobuf::Descriptor* MapOfObjects::descriptor() {
  protobuf_AssignDescriptorsOnce();
  return MapOfObjects_descriptor_;
}

const MapOfObjects& MapOfObjects::default_instance() {
  protobuf_InitDefaults_google_2fprotobuf_2futil_2finternal_2ftestdata_2fmaps_2eproto();
  return *internal_default_instance();
}

::google::protobuf::internal::ExplicitlyConstructed<MapOfObjects> MapOfObjects_default_instance_;

MapOfObjects* MapOfObjects::New(::google::protobuf::Arena* arena) const {
  MapOfObjects* n = new MapOfObjects;
  if (arena != NULL) {
    arena->Own(n);
  }
  return n;
}

void MapOfObjects::Clear() {
// @@protoc_insertion_point(message_clear_start:google.protobuf.testing.MapOfObjects)
  map_.Clear();
}

bool MapOfObjects::MergePartialFromCodedStream(
    ::google::protobuf::io::CodedInputStream* input) {
#define DO_(EXPRESSION) if (!GOOGLE_PREDICT_TRUE(EXPRESSION)) goto failure
  ::google::protobuf::uint32 tag;
  // @@protoc_insertion_point(parse_start:google.protobuf.testing.MapOfObjects)
  for (;;) {
    ::std::pair< ::google::protobuf::uint32, bool> p = input->ReadTagWithCutoff(127);
    tag = p.first;
    if (!p.second) goto handle_unusual;
    switch (::google::protobuf::internal::WireFormatLite::GetTagFieldNumber(tag)) {
      // map<string, .google.protobuf.testing.MapOfObjects.M> map = 1;
      case 1: {
        if (tag == 10) {
          DO_(input->IncrementRecursionDepth());
         parse_loop_map:
          MapOfObjects_MapEntry::Parser< ::google::protobuf::internal::MapField<
              ::std::string, ::google::protobuf::testing::MapOfObjects_M,
              ::google::protobuf::internal::WireFormatLite::TYPE_STRING,
              ::google::protobuf::internal::WireFormatLite::TYPE_MESSAGE,
              0 >,
            ::google::protobuf::Map< ::std::string, ::google::protobuf::testing::MapOfObjects_M > > parser(&map_);
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessageNoVirtual(
              input, &parser));
          DO_(::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
            parser.key().data(), parser.key().length(),
            ::google::protobuf::internal::WireFormatLite::PARSE,
            "google.protobuf.testing.MapOfObjects.MapEntry.key"));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(10)) goto parse_loop_map;
        input->UnsafeDecrementRecursionDepth();
        if (input->ExpectAtEnd()) goto success;
        break;
      }

      default: {
      handle_unusual:
        if (tag == 0 ||
            ::google::protobuf::internal::WireFormatLite::GetTagWireType(tag) ==
            ::google::protobuf::internal::WireFormatLite::WIRETYPE_END_GROUP) {
          goto success;
        }
        DO_(::google::protobuf::internal::WireFormatLite::SkipField(input, tag));
        break;
      }
    }
  }
success:
  // @@protoc_insertion_point(parse_success:google.protobuf.testing.MapOfObjects)
  return true;
failure:
  // @@protoc_insertion_point(parse_failure:google.protobuf.testing.MapOfObjects)
  return false;
#undef DO_
}

void MapOfObjects::SerializeWithCachedSizes(
    ::google::protobuf::io::CodedOutputStream* output) const {
  // @@protoc_insertion_point(serialize_start:google.protobuf.testing.MapOfObjects)
  // map<string, .google.protobuf.testing.MapOfObjects.M> map = 1;
  if (!this->map().empty()) {
    typedef ::google::protobuf::Map< ::std::string, ::google::protobuf::testing::MapOfObjects_M >::const_pointer
        ConstPtr;
    typedef ConstPtr SortItem;
    typedef ::google::protobuf::internal::CompareByDerefFirst<SortItem> Less;
    struct Utf8Check {
      static void Check(ConstPtr p) {
        ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
          p->first.data(), p->first.length(),
          ::google::protobuf::internal::WireFormatLite::SERIALIZE,
          "google.protobuf.testing.MapOfObjects.MapEntry.key");
      }
    };

    if (output->IsSerializationDeterminstic() &&
        this->map().size() > 1) {
      ::google::protobuf::scoped_array<SortItem> items(
          new SortItem[this->map().size()]);
      typedef ::google::protobuf::Map< ::std::string, ::google::protobuf::testing::MapOfObjects_M >::size_type size_type;
      size_type n = 0;
      for (::google::protobuf::Map< ::std::string, ::google::protobuf::testing::MapOfObjects_M >::const_iterator
          it = this->map().begin();
          it != this->map().end(); ++it, ++n) {
        items[n] = SortItem(&*it);
      }
      ::std::sort(&items[0], &items[n], Less());
      ::google::protobuf::scoped_ptr<MapOfObjects_MapEntry> entry;
      for (size_type i = 0; i < n; i++) {
        entry.reset(map_.NewEntryWrapper(
            items[i]->first, items[i]->second));
        ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
            1, *entry, output);
        Utf8Check::Check(items[i]);
      }
    } else {
      ::google::protobuf::scoped_ptr<MapOfObjects_MapEntry> entry;
      for (::google::protobuf::Map< ::std::string, ::google::protobuf::testing::MapOfObjects_M >::const_iterator
          it = this->map().begin();
          it != this->map().end(); ++it) {
        entry.reset(map_.NewEntryWrapper(
            it->first, it->second));
        ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
            1, *entry, output);
        Utf8Check::Check(&*it);
      }
    }
  }

  // @@protoc_insertion_point(serialize_end:google.protobuf.testing.MapOfObjects)
}

::google::protobuf::uint8* MapOfObjects::InternalSerializeWithCachedSizesToArray(
    bool deterministic, ::google::protobuf::uint8* target) const {
  (void)deterministic; // Unused
  // @@protoc_insertion_point(serialize_to_array_start:google.protobuf.testing.MapOfObjects)
  // map<string, .google.protobuf.testing.MapOfObjects.M> map = 1;
  if (!this->map().empty()) {
    typedef ::google::protobuf::Map< ::std::string, ::google::protobuf::testing::MapOfObjects_M >::const_pointer
        ConstPtr;
    typedef ConstPtr SortItem;
    typedef ::google::protobuf::internal::CompareByDerefFirst<SortItem> Less;
    struct Utf8Check {
      static void Check(ConstPtr p) {
        ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
          p->first.data(), p->first.length(),
          ::google::protobuf::internal::WireFormatLite::SERIALIZE,
          "google.protobuf.testing.MapOfObjects.MapEntry.key");
      }
    };

    if (deterministic &&
        this->map().size() > 1) {
      ::google::protobuf::scoped_array<SortItem> items(
          new SortItem[this->map().size()]);
      typedef ::google::protobuf::Map< ::std::string, ::google::protobuf::testing::MapOfObjects_M >::size_type size_type;
      size_type n = 0;
      for (::google::protobuf::Map< ::std::string, ::google::protobuf::testing::MapOfObjects_M >::const_iterator
          it = this->map().begin();
          it != this->map().end(); ++it, ++n) {
        items[n] = SortItem(&*it);
      }
      ::std::sort(&items[0], &items[n], Less());
      ::google::protobuf::scoped_ptr<MapOfObjects_MapEntry> entry;
      for (size_type i = 0; i < n; i++) {
        entry.reset(map_.NewEntryWrapper(
            items[i]->first, items[i]->second));
        target = ::google::protobuf::internal::WireFormatLite::
                   InternalWriteMessageNoVirtualToArray(
                       1, *entry, deterministic, target);
;
        Utf8Check::Check(items[i]);
      }
    } else {
      ::google::protobuf::scoped_ptr<MapOfObjects_MapEntry> entry;
      for (::google::protobuf::Map< ::std::string, ::google::protobuf::testing::MapOfObjects_M >::const_iterator
          it = this->map().begin();
          it != this->map().end(); ++it) {
        entry.reset(map_.NewEntryWrapper(
            it->first, it->second));
        target = ::google::protobuf::internal::WireFormatLite::
                   InternalWriteMessageNoVirtualToArray(
                       1, *entry, deterministic, target);
;
        Utf8Check::Check(&*it);
      }
    }
  }

  // @@protoc_insertion_point(serialize_to_array_end:google.protobuf.testing.MapOfObjects)
  return target;
}

size_t MapOfObjects::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:google.protobuf.testing.MapOfObjects)
  size_t total_size = 0;

  // map<string, .google.protobuf.testing.MapOfObjects.M> map = 1;
  total_size += 1 *
      ::google::protobuf::internal::FromIntSize(this->map_size());
  {
    ::google::protobuf::scoped_ptr<MapOfObjects_MapEntry> entry;
    for (::google::protobuf::Map< ::std::string, ::google::protobuf::testing::MapOfObjects_M >::const_iterator
        it = this->map().begin();
        it != this->map().end(); ++it) {
      entry.reset(map_.NewEntryWrapper(it->first, it->second));
      total_size += ::google::protobuf::internal::WireFormatLite::
          MessageSizeNoVirtual(*entry);
    }
  }

  int cached_size = ::google::protobuf::internal::ToCachedSize(total_size);
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = cached_size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
  return total_size;
}

void MapOfObjects::MergeFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_merge_from_start:google.protobuf.testing.MapOfObjects)
  if (GOOGLE_PREDICT_FALSE(&from == this)) MergeFromFail(__LINE__);
  const MapOfObjects* source =
      ::google::protobuf::internal::DynamicCastToGenerated<const MapOfObjects>(
          &from);
  if (source == NULL) {
  // @@protoc_insertion_point(generalized_merge_from_cast_fail:google.protobuf.testing.MapOfObjects)
    ::google::protobuf::internal::ReflectionOps::Merge(from, this);
  } else {
  // @@protoc_insertion_point(generalized_merge_from_cast_success:google.protobuf.testing.MapOfObjects)
    UnsafeMergeFrom(*source);
  }
}

void MapOfObjects::MergeFrom(const MapOfObjects& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:google.protobuf.testing.MapOfObjects)
  if (GOOGLE_PREDICT_TRUE(&from != this)) {
    UnsafeMergeFrom(from);
  } else {
    MergeFromFail(__LINE__);
  }
}

void MapOfObjects::UnsafeMergeFrom(const MapOfObjects& from) {
  GOOGLE_DCHECK(&from != this);
  map_.MergeFrom(from.map_);
}

void MapOfObjects::CopyFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_copy_from_start:google.protobuf.testing.MapOfObjects)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

void MapOfObjects::CopyFrom(const MapOfObjects& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:google.protobuf.testing.MapOfObjects)
  if (&from == this) return;
  Clear();
  UnsafeMergeFrom(from);
}

bool MapOfObjects::IsInitialized() const {

  return true;
}

void MapOfObjects::Swap(MapOfObjects* other) {
  if (other == this) return;
  InternalSwap(other);
}
void MapOfObjects::InternalSwap(MapOfObjects* other) {
  map_.Swap(&other->map_);
  _internal_metadata_.Swap(&other->_internal_metadata_);
  std::swap(_cached_size_, other->_cached_size_);
}

::google::protobuf::Metadata MapOfObjects::GetMetadata() const {
  protobuf_AssignDescriptorsOnce();
  ::google::protobuf::Metadata metadata;
  metadata.descriptor = MapOfObjects_descriptor_;
  metadata.reflection = MapOfObjects_reflection_;
  return metadata;
}

#if PROTOBUF_INLINE_NOT_IN_HEADERS
// MapOfObjects_M

// optional string inner_text = 1;
void MapOfObjects_M::clear_inner_text() {
  inner_text_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
const ::std::string& MapOfObjects_M::inner_text() const {
  // @@protoc_insertion_point(field_get:google.protobuf.testing.MapOfObjects.M.inner_text)
  return inner_text_.GetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
void MapOfObjects_M::set_inner_text(const ::std::string& value) {
  
  inner_text_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:google.protobuf.testing.MapOfObjects.M.inner_text)
}
void MapOfObjects_M::set_inner_text(const char* value) {
  
  inner_text_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:google.protobuf.testing.MapOfObjects.M.inner_text)
}
void MapOfObjects_M::set_inner_text(const char* value, size_t size) {
  
  inner_text_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:google.protobuf.testing.MapOfObjects.M.inner_text)
}
::std::string* MapOfObjects_M::mutable_inner_text() {
  
  // @@protoc_insertion_point(field_mutable:google.protobuf.testing.MapOfObjects.M.inner_text)
  return inner_text_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
::std::string* MapOfObjects_M::release_inner_text() {
  // @@protoc_insertion_point(field_release:google.protobuf.testing.MapOfObjects.M.inner_text)
  
  return inner_text_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
void MapOfObjects_M::set_allocated_inner_text(::std::string* inner_text) {
  if (inner_text != NULL) {
    
  } else {
    
  }
  inner_text_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), inner_text);
  // @@protoc_insertion_point(field_set_allocated:google.protobuf.testing.MapOfObjects.M.inner_text)
}

inline const MapOfObjects_M* MapOfObjects_M::internal_default_instance() {
  return &MapOfObjects_M_default_instance_.get();
}
// -------------------------------------------------------------------

// MapOfObjects

// map<string, .google.protobuf.testing.MapOfObjects.M> map = 1;
int MapOfObjects::map_size() const {
  return map_.size();
}
void MapOfObjects::clear_map() {
  map_.Clear();
}
 const ::google::protobuf::Map< ::std::string, ::google::protobuf::testing::MapOfObjects_M >&
MapOfObjects::map() const {
  // @@protoc_insertion_point(field_map:google.protobuf.testing.MapOfObjects.map)
  return map_.GetMap();
}
 ::google::protobuf::Map< ::std::string, ::google::protobuf::testing::MapOfObjects_M >*
MapOfObjects::mutable_map() {
  // @@protoc_insertion_point(field_mutable_map:google.protobuf.testing.MapOfObjects.map)
  return map_.MutableMap();
}

inline const MapOfObjects* MapOfObjects::internal_default_instance() {
  return &MapOfObjects_default_instance_.get();
}
#endif  // PROTOBUF_INLINE_NOT_IN_HEADERS

// ===================================================================

#if !defined(_MSC_VER) || _MSC_VER >= 1900
#endif  // !defined(_MSC_VER) || _MSC_VER >= 1900

DummyRequest::DummyRequest()
  : ::google::protobuf::Message(), _internal_metadata_(NULL) {
  if (this != internal_default_instance()) protobuf_InitDefaults_google_2fprotobuf_2futil_2finternal_2ftestdata_2fmaps_2eproto();
  SharedCtor();
  // @@protoc_insertion_point(constructor:google.protobuf.testing.DummyRequest)
}

void DummyRequest::InitAsDefaultInstance() {
}

DummyRequest::DummyRequest(const DummyRequest& from)
  : ::google::protobuf::Message(),
    _internal_metadata_(NULL) {
  SharedCtor();
  UnsafeMergeFrom(from);
  // @@protoc_insertion_point(copy_constructor:google.protobuf.testing.DummyRequest)
}

void DummyRequest::SharedCtor() {
  _cached_size_ = 0;
}

DummyRequest::~DummyRequest() {
  // @@protoc_insertion_point(destructor:google.protobuf.testing.DummyRequest)
  SharedDtor();
}

void DummyRequest::SharedDtor() {
}

void DummyRequest::SetCachedSize(int size) const {
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
}
const ::google::protobuf::Descriptor* DummyRequest::descriptor() {
  protobuf_AssignDescriptorsOnce();
  return DummyRequest_descriptor_;
}

const DummyRequest& DummyRequest::default_instance() {
  protobuf_InitDefaults_google_2fprotobuf_2futil_2finternal_2ftestdata_2fmaps_2eproto();
  return *internal_default_instance();
}

::google::protobuf::internal::ExplicitlyConstructed<DummyRequest> DummyRequest_default_instance_;

DummyRequest* DummyRequest::New(::google::protobuf::Arena* arena) const {
  DummyRequest* n = new DummyRequest;
  if (arena != NULL) {
    arena->Own(n);
  }
  return n;
}

void DummyRequest::Clear() {
// @@protoc_insertion_point(message_clear_start:google.protobuf.testing.DummyRequest)
}

bool DummyRequest::MergePartialFromCodedStream(
    ::google::protobuf::io::CodedInputStream* input) {
#define DO_(EXPRESSION) if (!GOOGLE_PREDICT_TRUE(EXPRESSION)) goto failure
  ::google::protobuf::uint32 tag;
  // @@protoc_insertion_point(parse_start:google.protobuf.testing.DummyRequest)
  for (;;) {
    ::std::pair< ::google::protobuf::uint32, bool> p = input->ReadTagWithCutoff(127);
    tag = p.first;
    if (!p.second) goto handle_unusual;
  handle_unusual:
    if (tag == 0 ||
        ::google::protobuf::internal::WireFormatLite::GetTagWireType(tag) ==
        ::google::protobuf::internal::WireFormatLite::WIRETYPE_END_GROUP) {
      goto success;
    }
    DO_(::google::protobuf::internal::WireFormatLite::SkipField(input, tag));
  }
success:
  // @@protoc_insertion_point(parse_success:google.protobuf.testing.DummyRequest)
  return true;
failure:
  // @@protoc_insertion_point(parse_failure:google.protobuf.testing.DummyRequest)
  return false;
#undef DO_
}

void DummyRequest::SerializeWithCachedSizes(
    ::google::protobuf::io::CodedOutputStream* output) const {
  // @@protoc_insertion_point(serialize_start:google.protobuf.testing.DummyRequest)
  // @@protoc_insertion_point(serialize_end:google.protobuf.testing.DummyRequest)
}

::google::protobuf::uint8* DummyRequest::InternalSerializeWithCachedSizesToArray(
    bool deterministic, ::google::protobuf::uint8* target) const {
  (void)deterministic; // Unused
  // @@protoc_insertion_point(serialize_to_array_start:google.protobuf.testing.DummyRequest)
  // @@protoc_insertion_point(serialize_to_array_end:google.protobuf.testing.DummyRequest)
  return target;
}

size_t DummyRequest::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:google.protobuf.testing.DummyRequest)
  size_t total_size = 0;

  int cached_size = ::google::protobuf::internal::ToCachedSize(total_size);
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = cached_size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
  return total_size;
}

void DummyRequest::MergeFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_merge_from_start:google.protobuf.testing.DummyRequest)
  if (GOOGLE_PREDICT_FALSE(&from == this)) MergeFromFail(__LINE__);
  const DummyRequest* source =
      ::google::protobuf::internal::DynamicCastToGenerated<const DummyRequest>(
          &from);
  if (source == NULL) {
  // @@protoc_insertion_point(generalized_merge_from_cast_fail:google.protobuf.testing.DummyRequest)
    ::google::protobuf::internal::ReflectionOps::Merge(from, this);
  } else {
  // @@protoc_insertion_point(generalized_merge_from_cast_success:google.protobuf.testing.DummyRequest)
    UnsafeMergeFrom(*source);
  }
}

void DummyRequest::MergeFrom(const DummyRequest& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:google.protobuf.testing.DummyRequest)
  if (GOOGLE_PREDICT_TRUE(&from != this)) {
    UnsafeMergeFrom(from);
  } else {
    MergeFromFail(__LINE__);
  }
}

void DummyRequest::UnsafeMergeFrom(const DummyRequest& from) {
  GOOGLE_DCHECK(&from != this);
}

void DummyRequest::CopyFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_copy_from_start:google.protobuf.testing.DummyRequest)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

void DummyRequest::CopyFrom(const DummyRequest& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:google.protobuf.testing.DummyRequest)
  if (&from == this) return;
  Clear();
  UnsafeMergeFrom(from);
}

bool DummyRequest::IsInitialized() const {

  return true;
}

void DummyRequest::Swap(DummyRequest* other) {
  if (other == this) return;
  InternalSwap(other);
}
void DummyRequest::InternalSwap(DummyRequest* other) {
  _internal_metadata_.Swap(&other->_internal_metadata_);
  std::swap(_cached_size_, other->_cached_size_);
}

::google::protobuf::Metadata DummyRequest::GetMetadata() const {
  protobuf_AssignDescriptorsOnce();
  ::google::protobuf::Metadata metadata;
  metadata.descriptor = DummyRequest_descriptor_;
  metadata.reflection = DummyRequest_reflection_;
  return metadata;
}

#if PROTOBUF_INLINE_NOT_IN_HEADERS
// DummyRequest

inline const DummyRequest* DummyRequest::internal_default_instance() {
  return &DummyRequest_default_instance_.get();
}
#endif  // PROTOBUF_INLINE_NOT_IN_HEADERS

// ===================================================================

#if !defined(_MSC_VER) || _MSC_VER >= 1900
const int MapIn::kOtherFieldNumber;
const int MapIn::kThingsFieldNumber;
const int MapIn::kMapInputFieldNumber;
#endif  // !defined(_MSC_VER) || _MSC_VER >= 1900

MapIn::MapIn()
  : ::google::protobuf::Message(), _internal_metadata_(NULL) {
  if (this != internal_default_instance()) protobuf_InitDefaults_google_2fprotobuf_2futil_2finternal_2ftestdata_2fmaps_2eproto();
  SharedCtor();
  // @@protoc_insertion_point(constructor:google.protobuf.testing.MapIn)
}

void MapIn::InitAsDefaultInstance() {
}

MapIn::MapIn(const MapIn& from)
  : ::google::protobuf::Message(),
    _internal_metadata_(NULL) {
  SharedCtor();
  UnsafeMergeFrom(from);
  // @@protoc_insertion_point(copy_constructor:google.protobuf.testing.MapIn)
}

void MapIn::SharedCtor() {
  map_input_.SetAssignDescriptorCallback(
      protobuf_AssignDescriptorsOnce);
  map_input_.SetEntryDescriptor(
      &::google::protobuf::testing::MapIn_MapInputEntry_descriptor_);
  other_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  _cached_size_ = 0;
}

MapIn::~MapIn() {
  // @@protoc_insertion_point(destructor:google.protobuf.testing.MapIn)
  SharedDtor();
}

void MapIn::SharedDtor() {
  other_.DestroyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}

void MapIn::SetCachedSize(int size) const {
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
}
const ::google::protobuf::Descriptor* MapIn::descriptor() {
  protobuf_AssignDescriptorsOnce();
  return MapIn_descriptor_;
}

const MapIn& MapIn::default_instance() {
  protobuf_InitDefaults_google_2fprotobuf_2futil_2finternal_2ftestdata_2fmaps_2eproto();
  return *internal_default_instance();
}

::google::protobuf::internal::ExplicitlyConstructed<MapIn> MapIn_default_instance_;

MapIn* MapIn::New(::google::protobuf::Arena* arena) const {
  MapIn* n = new MapIn;
  if (arena != NULL) {
    arena->Own(n);
  }
  return n;
}

void MapIn::Clear() {
// @@protoc_insertion_point(message_clear_start:google.protobuf.testing.MapIn)
  other_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  things_.Clear();
  map_input_.Clear();
}

bool MapIn::MergePartialFromCodedStream(
    ::google::protobuf::io::CodedInputStream* input) {
#define DO_(EXPRESSION) if (!GOOGLE_PREDICT_TRUE(EXPRESSION)) goto failure
  ::google::protobuf::uint32 tag;
  // @@protoc_insertion_point(parse_start:google.protobuf.testing.MapIn)
  for (;;) {
    ::std::pair< ::google::protobuf::uint32, bool> p = input->ReadTagWithCutoff(127);
    tag = p.first;
    if (!p.second) goto handle_unusual;
    switch (::google::protobuf::internal::WireFormatLite::GetTagFieldNumber(tag)) {
      // optional string other = 1;
      case 1: {
        if (tag == 10) {
          DO_(::google::protobuf::internal::WireFormatLite::ReadString(
                input, this->mutable_other()));
          DO_(::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
            this->other().data(), this->other().length(),
            ::google::protobuf::internal::WireFormatLite::PARSE,
            "google.protobuf.testing.MapIn.other"));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(18)) goto parse_things;
        break;
      }

      // repeated string things = 2;
      case 2: {
        if (tag == 18) {
         parse_things:
          DO_(::google::protobuf::internal::WireFormatLite::ReadString(
                input, this->add_things()));
          DO_(::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
            this->things(this->things_size() - 1).data(),
            this->things(this->things_size() - 1).length(),
            ::google::protobuf::internal::WireFormatLite::PARSE,
            "google.protobuf.testing.MapIn.things"));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(18)) goto parse_things;
        if (input->ExpectTag(26)) goto parse_map_input;
        break;
      }

      // map<string, string> map_input = 3;
      case 3: {
        if (tag == 26) {
         parse_map_input:
          DO_(input->IncrementRecursionDepth());
         parse_loop_map_input:
          MapIn_MapInputEntry::Parser< ::google::protobuf::internal::MapField<
              ::std::string, ::std::string,
              ::google::protobuf::internal::WireFormatLite::TYPE_STRING,
              ::google::protobuf::internal::WireFormatLite::TYPE_STRING,
              0 >,
            ::google::protobuf::Map< ::std::string, ::std::string > > parser(&map_input_);
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessageNoVirtual(
              input, &parser));
          DO_(::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
            parser.key().data(), parser.key().length(),
            ::google::protobuf::internal::WireFormatLite::PARSE,
            "google.protobuf.testing.MapIn.MapInputEntry.key"));
          DO_(::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
            parser.value().data(), parser.value().length(),
            ::google::protobuf::internal::WireFormatLite::PARSE,
            "google.protobuf.testing.MapIn.MapInputEntry.value"));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(26)) goto parse_loop_map_input;
        input->UnsafeDecrementRecursionDepth();
        if (input->ExpectAtEnd()) goto success;
        break;
      }

      default: {
      handle_unusual:
        if (tag == 0 ||
            ::google::protobuf::internal::WireFormatLite::GetTagWireType(tag) ==
            ::google::protobuf::internal::WireFormatLite::WIRETYPE_END_GROUP) {
          goto success;
        }
        DO_(::google::protobuf::internal::WireFormatLite::SkipField(input, tag));
        break;
      }
    }
  }
success:
  // @@protoc_insertion_point(parse_success:google.protobuf.testing.MapIn)
  return true;
failure:
  // @@protoc_insertion_point(parse_failure:google.protobuf.testing.MapIn)
  return false;
#undef DO_
}

void MapIn::SerializeWithCachedSizes(
    ::google::protobuf::io::CodedOutputStream* output) const {
  // @@protoc_insertion_point(serialize_start:google.protobuf.testing.MapIn)
  // optional string other = 1;
  if (this->other().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->other().data(), this->other().length(),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "google.protobuf.testing.MapIn.other");
    ::google::protobuf::internal::WireFormatLite::WriteStringMaybeAliased(
      1, this->other(), output);
  }

  // repeated string things = 2;
  for (int i = 0; i < this->things_size(); i++) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->things(i).data(), this->things(i).length(),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "google.protobuf.testing.MapIn.things");
    ::google::protobuf::internal::WireFormatLite::WriteString(
      2, this->things(i), output);
  }

  // map<string, string> map_input = 3;
  if (!this->map_input().empty()) {
    typedef ::google::protobuf::Map< ::std::string, ::std::string >::const_pointer
        ConstPtr;
    typedef ConstPtr SortItem;
    typedef ::google::protobuf::internal::CompareByDerefFirst<SortItem> Less;
    struct Utf8Check {
      static void Check(ConstPtr p) {
        ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
          p->first.data(), p->first.length(),
          ::google::protobuf::internal::WireFormatLite::SERIALIZE,
          "google.protobuf.testing.MapIn.MapInputEntry.key");
        ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
          p->second.data(), p->second.length(),
          ::google::protobuf::internal::WireFormatLite::SERIALIZE,
          "google.protobuf.testing.MapIn.MapInputEntry.value");
      }
    };

    if (output->IsSerializationDeterminstic() &&
        this->map_input().size() > 1) {
      ::google::protobuf::scoped_array<SortItem> items(
          new SortItem[this->map_input().size()]);
      typedef ::google::protobuf::Map< ::std::string, ::std::string >::size_type size_type;
      size_type n = 0;
      for (::google::protobuf::Map< ::std::string, ::std::string >::const_iterator
          it = this->map_input().begin();
          it != this->map_input().end(); ++it, ++n) {
        items[n] = SortItem(&*it);
      }
      ::std::sort(&items[0], &items[n], Less());
      ::google::protobuf::scoped_ptr<MapIn_MapInputEntry> entry;
      for (size_type i = 0; i < n; i++) {
        entry.reset(map_input_.NewEntryWrapper(
            items[i]->first, items[i]->second));
        ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
            3, *entry, output);
        Utf8Check::Check(items[i]);
      }
    } else {
      ::google::protobuf::scoped_ptr<MapIn_MapInputEntry> entry;
      for (::google::protobuf::Map< ::std::string, ::std::string >::const_iterator
          it = this->map_input().begin();
          it != this->map_input().end(); ++it) {
        entry.reset(map_input_.NewEntryWrapper(
            it->first, it->second));
        ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
            3, *entry, output);
        Utf8Check::Check(&*it);
      }
    }
  }

  // @@protoc_insertion_point(serialize_end:google.protobuf.testing.MapIn)
}

::google::protobuf::uint8* MapIn::InternalSerializeWithCachedSizesToArray(
    bool deterministic, ::google::protobuf::uint8* target) const {
  (void)deterministic; // Unused
  // @@protoc_insertion_point(serialize_to_array_start:google.protobuf.testing.MapIn)
  // optional string other = 1;
  if (this->other().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->other().data(), this->other().length(),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "google.protobuf.testing.MapIn.other");
    target =
      ::google::protobuf::internal::WireFormatLite::WriteStringToArray(
        1, this->other(), target);
  }

  // repeated string things = 2;
  for (int i = 0; i < this->things_size(); i++) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->things(i).data(), this->things(i).length(),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "google.protobuf.testing.MapIn.things");
    target = ::google::protobuf::internal::WireFormatLite::
      WriteStringToArray(2, this->things(i), target);
  }

  // map<string, string> map_input = 3;
  if (!this->map_input().empty()) {
    typedef ::google::protobuf::Map< ::std::string, ::std::string >::const_pointer
        ConstPtr;
    typedef ConstPtr SortItem;
    typedef ::google::protobuf::internal::CompareByDerefFirst<SortItem> Less;
    struct Utf8Check {
      static void Check(ConstPtr p) {
        ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
          p->first.data(), p->first.length(),
          ::google::protobuf::internal::WireFormatLite::SERIALIZE,
          "google.protobuf.testing.MapIn.MapInputEntry.key");
        ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
          p->second.data(), p->second.length(),
          ::google::protobuf::internal::WireFormatLite::SERIALIZE,
          "google.protobuf.testing.MapIn.MapInputEntry.value");
      }
    };

    if (deterministic &&
        this->map_input().size() > 1) {
      ::google::protobuf::scoped_array<SortItem> items(
          new SortItem[this->map_input().size()]);
      typedef ::google::protobuf::Map< ::std::string, ::std::string >::size_type size_type;
      size_type n = 0;
      for (::google::protobuf::Map< ::std::string, ::std::string >::const_iterator
          it = this->map_input().begin();
          it != this->map_input().end(); ++it, ++n) {
        items[n] = SortItem(&*it);
      }
      ::std::sort(&items[0], &items[n], Less());
      ::google::protobuf::scoped_ptr<MapIn_MapInputEntry> entry;
      for (size_type i = 0; i < n; i++) {
        entry.reset(map_input_.NewEntryWrapper(
            items[i]->first, items[i]->second));
        target = ::google::protobuf::internal::WireFormatLite::
                   InternalWriteMessageNoVirtualToArray(
                       3, *entry, deterministic, target);
;
        Utf8Check::Check(items[i]);
      }
    } else {
      ::google::protobuf::scoped_ptr<MapIn_MapInputEntry> entry;
      for (::google::protobuf::Map< ::std::string, ::std::string >::const_iterator
          it = this->map_input().begin();
          it != this->map_input().end(); ++it) {
        entry.reset(map_input_.NewEntryWrapper(
            it->first, it->second));
        target = ::google::protobuf::internal::WireFormatLite::
                   InternalWriteMessageNoVirtualToArray(
                       3, *entry, deterministic, target);
;
        Utf8Check::Check(&*it);
      }
    }
  }

  // @@protoc_insertion_point(serialize_to_array_end:google.protobuf.testing.MapIn)
  return target;
}

size_t MapIn::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:google.protobuf.testing.MapIn)
  size_t total_size = 0;

  // optional string other = 1;
  if (this->other().size() > 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::StringSize(
        this->other());
  }

  // repeated string things = 2;
  total_size += 1 *
      ::google::protobuf::internal::FromIntSize(this->things_size());
  for (int i = 0; i < this->things_size(); i++) {
    total_size += ::google::protobuf::internal::WireFormatLite::StringSize(
      this->things(i));
  }

  // map<string, string> map_input = 3;
  total_size += 1 *
      ::google::protobuf::internal::FromIntSize(this->map_input_size());
  {
    ::google::protobuf::scoped_ptr<MapIn_MapInputEntry> entry;
    for (::google::protobuf::Map< ::std::string, ::std::string >::const_iterator
        it = this->map_input().begin();
        it != this->map_input().end(); ++it) {
      entry.reset(map_input_.NewEntryWrapper(it->first, it->second));
      total_size += ::google::protobuf::internal::WireFormatLite::
          MessageSizeNoVirtual(*entry);
    }
  }

  int cached_size = ::google::protobuf::internal::ToCachedSize(total_size);
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = cached_size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
  return total_size;
}

void MapIn::MergeFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_merge_from_start:google.protobuf.testing.MapIn)
  if (GOOGLE_PREDICT_FALSE(&from == this)) MergeFromFail(__LINE__);
  const MapIn* source =
      ::google::protobuf::internal::DynamicCastToGenerated<const MapIn>(
          &from);
  if (source == NULL) {
  // @@protoc_insertion_point(generalized_merge_from_cast_fail:google.protobuf.testing.MapIn)
    ::google::protobuf::internal::ReflectionOps::Merge(from, this);
  } else {
  // @@protoc_insertion_point(generalized_merge_from_cast_success:google.protobuf.testing.MapIn)
    UnsafeMergeFrom(*source);
  }
}

void MapIn::MergeFrom(const MapIn& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:google.protobuf.testing.MapIn)
  if (GOOGLE_PREDICT_TRUE(&from != this)) {
    UnsafeMergeFrom(from);
  } else {
    MergeFromFail(__LINE__);
  }
}

void MapIn::UnsafeMergeFrom(const MapIn& from) {
  GOOGLE_DCHECK(&from != this);
  things_.UnsafeMergeFrom(from.things_);
  map_input_.MergeFrom(from.map_input_);
  if (from.other().size() > 0) {

    other_.AssignWithDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), from.other_);
  }
}

void MapIn::CopyFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_copy_from_start:google.protobuf.testing.MapIn)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

void MapIn::CopyFrom(const MapIn& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:google.protobuf.testing.MapIn)
  if (&from == this) return;
  Clear();
  UnsafeMergeFrom(from);
}

bool MapIn::IsInitialized() const {

  return true;
}

void MapIn::Swap(MapIn* other) {
  if (other == this) return;
  InternalSwap(other);
}
void MapIn::InternalSwap(MapIn* other) {
  other_.Swap(&other->other_);
  things_.UnsafeArenaSwap(&other->things_);
  map_input_.Swap(&other->map_input_);
  _internal_metadata_.Swap(&other->_internal_metadata_);
  std::swap(_cached_size_, other->_cached_size_);
}

::google::protobuf::Metadata MapIn::GetMetadata() const {
  protobuf_AssignDescriptorsOnce();
  ::google::protobuf::Metadata metadata;
  metadata.descriptor = MapIn_descriptor_;
  metadata.reflection = MapIn_reflection_;
  return metadata;
}

#if PROTOBUF_INLINE_NOT_IN_HEADERS
// MapIn

// optional string other = 1;
void MapIn::clear_other() {
  other_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
const ::std::string& MapIn::other() const {
  // @@protoc_insertion_point(field_get:google.protobuf.testing.MapIn.other)
  return other_.GetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
void MapIn::set_other(const ::std::string& value) {
  
  other_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:google.protobuf.testing.MapIn.other)
}
void MapIn::set_other(const char* value) {
  
  other_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:google.protobuf.testing.MapIn.other)
}
void MapIn::set_other(const char* value, size_t size) {
  
  other_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:google.protobuf.testing.MapIn.other)
}
::std::string* MapIn::mutable_other() {
  
  // @@protoc_insertion_point(field_mutable:google.protobuf.testing.MapIn.other)
  return other_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
::std::string* MapIn::release_other() {
  // @@protoc_insertion_point(field_release:google.protobuf.testing.MapIn.other)
  
  return other_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
void MapIn::set_allocated_other(::std::string* other) {
  if (other != NULL) {
    
  } else {
    
  }
  other_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), other);
  // @@protoc_insertion_point(field_set_allocated:google.protobuf.testing.MapIn.other)
}

// repeated string things = 2;
int MapIn::things_size() const {
  return things_.size();
}
void MapIn::clear_things() {
  things_.Clear();
}
const ::std::string& MapIn::things(int index) const {
  // @@protoc_insertion_point(field_get:google.protobuf.testing.MapIn.things)
  return things_.Get(index);
}
::std::string* MapIn::mutable_things(int index) {
  // @@protoc_insertion_point(field_mutable:google.protobuf.testing.MapIn.things)
  return things_.Mutable(index);
}
void MapIn::set_things(int index, const ::std::string& value) {
  // @@protoc_insertion_point(field_set:google.protobuf.testing.MapIn.things)
  things_.Mutable(index)->assign(value);
}
void MapIn::set_things(int index, const char* value) {
  things_.Mutable(index)->assign(value);
  // @@protoc_insertion_point(field_set_char:google.protobuf.testing.MapIn.things)
}
void MapIn::set_things(int index, const char* value, size_t size) {
  things_.Mutable(index)->assign(
    reinterpret_cast<const char*>(value), size);
  // @@protoc_insertion_point(field_set_pointer:google.protobuf.testing.MapIn.things)
}
::std::string* MapIn::add_things() {
  // @@protoc_insertion_point(field_add_mutable:google.protobuf.testing.MapIn.things)
  return things_.Add();
}
void MapIn::add_things(const ::std::string& value) {
  things_.Add()->assign(value);
  // @@protoc_insertion_point(field_add:google.protobuf.testing.MapIn.things)
}
void MapIn::add_things(const char* value) {
  things_.Add()->assign(value);
  // @@protoc_insertion_point(field_add_char:google.protobuf.testing.MapIn.things)
}
void MapIn::add_things(const char* value, size_t size) {
  things_.Add()->assign(reinterpret_cast<const char*>(value), size);
  // @@protoc_insertion_point(field_add_pointer:google.protobuf.testing.MapIn.things)
}
const ::google::protobuf::RepeatedPtrField< ::std::string>&
MapIn::things() const {
  // @@protoc_insertion_point(field_list:google.protobuf.testing.MapIn.things)
  return things_;
}
::google::protobuf::RepeatedPtrField< ::std::string>*
MapIn::mutable_things() {
  // @@protoc_insertion_point(field_mutable_list:google.protobuf.testing.MapIn.things)
  return &things_;
}

// map<string, string> map_input = 3;
int MapIn::map_input_size() const {
  return map_input_.size();
}
void MapIn::clear_map_input() {
  map_input_.Clear();
}
 const ::google::protobuf::Map< ::std::string, ::std::string >&
MapIn::map_input() const {
  // @@protoc_insertion_point(field_map:google.protobuf.testing.MapIn.map_input)
  return map_input_.GetMap();
}
 ::google::protobuf::Map< ::std::string, ::std::string >*
MapIn::mutable_map_input() {
  // @@protoc_insertion_point(field_mutable_map:google.protobuf.testing.MapIn.map_input)
  return map_input_.MutableMap();
}

inline const MapIn* MapIn::internal_default_instance() {
  return &MapIn_default_instance_.get();
}
#endif  // PROTOBUF_INLINE_NOT_IN_HEADERS

// ===================================================================

#if !defined(_MSC_VER) || _MSC_VER >= 1900
const int MapOut::kMap1FieldNumber;
const int MapOut::kMap2FieldNumber;
const int MapOut::kMap3FieldNumber;
const int MapOut::kMap4FieldNumber;
const int MapOut::kBarFieldNumber;
#endif  // !defined(_MSC_VER) || _MSC_VER >= 1900

MapOut::MapOut()
  : ::google::protobuf::Message(), _internal_metadata_(NULL) {
  if (this != internal_default_instance()) protobuf_InitDefaults_google_2fprotobuf_2futil_2finternal_2ftestdata_2fmaps_2eproto();
  SharedCtor();
  // @@protoc_insertion_point(constructor:google.protobuf.testing.MapOut)
}

void MapOut::InitAsDefaultInstance() {
}

MapOut::MapOut(const MapOut& from)
  : ::google::protobuf::Message(),
    _internal_metadata_(NULL) {
  SharedCtor();
  UnsafeMergeFrom(from);
  // @@protoc_insertion_point(copy_constructor:google.protobuf.testing.MapOut)
}

void MapOut::SharedCtor() {
  map1_.SetAssignDescriptorCallback(
      protobuf_AssignDescriptorsOnce);
  map1_.SetEntryDescriptor(
      &::google::protobuf::testing::MapOut_Map1Entry_descriptor_);
  map2_.SetAssignDescriptorCallback(
      protobuf_AssignDescriptorsOnce);
  map2_.SetEntryDescriptor(
      &::google::protobuf::testing::MapOut_Map2Entry_descriptor_);
  map3_.SetAssignDescriptorCallback(
      protobuf_AssignDescriptorsOnce);
  map3_.SetEntryDescriptor(
      &::google::protobuf::testing::MapOut_Map3Entry_descriptor_);
  map4_.SetAssignDescriptorCallback(
      protobuf_AssignDescriptorsOnce);
  map4_.SetEntryDescriptor(
      &::google::protobuf::testing::MapOut_Map4Entry_descriptor_);
  bar_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  _cached_size_ = 0;
}

MapOut::~MapOut() {
  // @@protoc_insertion_point(destructor:google.protobuf.testing.MapOut)
  SharedDtor();
}

void MapOut::SharedDtor() {
  bar_.DestroyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}

void MapOut::SetCachedSize(int size) const {
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
}
const ::google::protobuf::Descriptor* MapOut::descriptor() {
  protobuf_AssignDescriptorsOnce();
  return MapOut_descriptor_;
}

const MapOut& MapOut::default_instance() {
  protobuf_InitDefaults_google_2fprotobuf_2futil_2finternal_2ftestdata_2fmaps_2eproto();
  return *internal_default_instance();
}

::google::protobuf::internal::ExplicitlyConstructed<MapOut> MapOut_default_instance_;

MapOut* MapOut::New(::google::protobuf::Arena* arena) const {
  MapOut* n = new MapOut;
  if (arena != NULL) {
    arena->Own(n);
  }
  return n;
}

void MapOut::Clear() {
// @@protoc_insertion_point(message_clear_start:google.protobuf.testing.MapOut)
  bar_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  map1_.Clear();
  map2_.Clear();
  map3_.Clear();
  map4_.Clear();
}

bool MapOut::MergePartialFromCodedStream(
    ::google::protobuf::io::CodedInputStream* input) {
#define DO_(EXPRESSION) if (!GOOGLE_PREDICT_TRUE(EXPRESSION)) goto failure
  ::google::protobuf::uint32 tag;
  // @@protoc_insertion_point(parse_start:google.protobuf.testing.MapOut)
  for (;;) {
    ::std::pair< ::google::protobuf::uint32, bool> p = input->ReadTagWithCutoff(127);
    tag = p.first;
    if (!p.second) goto handle_unusual;
    switch (::google::protobuf::internal::WireFormatLite::GetTagFieldNumber(tag)) {
      // map<string, .google.protobuf.testing.MapM> map1 = 1;
      case 1: {
        if (tag == 10) {
          DO_(input->IncrementRecursionDepth());
         parse_loop_map1:
          MapOut_Map1Entry::Parser< ::google::protobuf::internal::MapField<
              ::std::string, ::google::protobuf::testing::MapM,
              ::google::protobuf::internal::WireFormatLite::TYPE_STRING,
              ::google::protobuf::internal::WireFormatLite::TYPE_MESSAGE,
              0 >,
            ::google::protobuf::Map< ::std::string, ::google::protobuf::testing::MapM > > parser(&map1_);
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessageNoVirtual(
              input, &parser));
          DO_(::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
            parser.key().data(), parser.key().length(),
            ::google::protobuf::internal::WireFormatLite::PARSE,
            "google.protobuf.testing.MapOut.Map1Entry.key"));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(10)) goto parse_loop_map1;
        if (input->ExpectTag(18)) goto parse_loop_map2;
        input->UnsafeDecrementRecursionDepth();
        break;
      }

      // map<string, .google.protobuf.testing.MapOut> map2 = 2;
      case 2: {
        if (tag == 18) {
          DO_(input->IncrementRecursionDepth());
         parse_loop_map2:
          MapOut_Map2Entry::Parser< ::google::protobuf::internal::MapField<
              ::std::string, ::google::protobuf::testing::MapOut,
              ::google::protobuf::internal::WireFormatLite::TYPE_STRING,
              ::google::protobuf::internal::WireFormatLite::TYPE_MESSAGE,
              0 >,
            ::google::protobuf::Map< ::std::string, ::google::protobuf::testing::MapOut > > parser(&map2_);
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessageNoVirtual(
              input, &parser));
          DO_(::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
            parser.key().data(), parser.key().length(),
            ::google::protobuf::internal::WireFormatLite::PARSE,
            "google.protobuf.testing.MapOut.Map2Entry.key"));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(18)) goto parse_loop_map2;
        if (input->ExpectTag(26)) goto parse_loop_map3;
        input->UnsafeDecrementRecursionDepth();
        break;
      }

      // map<int32, string> map3 = 3;
      case 3: {
        if (tag == 26) {
          DO_(input->IncrementRecursionDepth());
         parse_loop_map3:
          MapOut_Map3Entry::Parser< ::google::protobuf::internal::MapField<
              ::google::protobuf::int32, ::std::string,
              ::google::protobuf::internal::WireFormatLite::TYPE_INT32,
              ::google::protobuf::internal::WireFormatLite::TYPE_STRING,
              0 >,
            ::google::protobuf::Map< ::google::protobuf::int32, ::std::string > > parser(&map3_);
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessageNoVirtual(
              input, &parser));
          DO_(::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
            parser.value().data(), parser.value().length(),
            ::google::protobuf::internal::WireFormatLite::PARSE,
            "google.protobuf.testing.MapOut.Map3Entry.value"));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(26)) goto parse_loop_map3;
        input->UnsafeDecrementRecursionDepth();
        if (input->ExpectTag(34)) goto parse_bar;
        break;
      }

      // optional string bar = 4;
      case 4: {
        if (tag == 34) {
         parse_bar:
          DO_(::google::protobuf::internal::WireFormatLite::ReadString(
                input, this->mutable_bar()));
          DO_(::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
            this->bar().data(), this->bar().length(),
            ::google::protobuf::internal::WireFormatLite::PARSE,
            "google.protobuf.testing.MapOut.bar"));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(42)) goto parse_map4;
        break;
      }

      // map<bool, string> map4 = 5;
      case 5: {
        if (tag == 42) {
         parse_map4:
          DO_(input->IncrementRecursionDepth());
         parse_loop_map4:
          MapOut_Map4Entry::Parser< ::google::protobuf::internal::MapField<
              bool, ::std::string,
              ::google::protobuf::internal::WireFormatLite::TYPE_BOOL,
              ::google::protobuf::internal::WireFormatLite::TYPE_STRING,
              0 >,
            ::google::protobuf::Map< bool, ::std::string > > parser(&map4_);
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessageNoVirtual(
              input, &parser));
          DO_(::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
            parser.value().data(), parser.value().length(),
            ::google::protobuf::internal::WireFormatLite::PARSE,
            "google.protobuf.testing.MapOut.Map4Entry.value"));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(42)) goto parse_loop_map4;
        input->UnsafeDecrementRecursionDepth();
        if (input->ExpectAtEnd()) goto success;
        break;
      }

      default: {
      handle_unusual:
        if (tag == 0 ||
            ::google::protobuf::internal::WireFormatLite::GetTagWireType(tag) ==
            ::google::protobuf::internal::WireFormatLite::WIRETYPE_END_GROUP) {
          goto success;
        }
        DO_(::google::protobuf::internal::WireFormatLite::SkipField(input, tag));
        break;
      }
    }
  }
success:
  // @@protoc_insertion_point(parse_success:google.protobuf.testing.MapOut)
  return true;
failure:
  // @@protoc_insertion_point(parse_failure:google.protobuf.testing.MapOut)
  return false;
#undef DO_
}

void MapOut::SerializeWithCachedSizes(
    ::google::protobuf::io::CodedOutputStream* output) const {
  // @@protoc_insertion_point(serialize_start:google.protobuf.testing.MapOut)
  // map<string, .google.protobuf.testing.MapM> map1 = 1;
  if (!this->map1().empty()) {
    typedef ::google::protobuf::Map< ::std::string, ::google::protobuf::testing::MapM >::const_pointer
        ConstPtr;
    typedef ConstPtr SortItem;
    typedef ::google::protobuf::internal::CompareByDerefFirst<SortItem> Less;
    struct Utf8Check {
      static void Check(ConstPtr p) {
        ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
          p->first.data(), p->first.length(),
          ::google::protobuf::internal::WireFormatLite::SERIALIZE,
          "google.protobuf.testing.MapOut.Map1Entry.key");
      }
    };

    if (output->IsSerializationDeterminstic() &&
        this->map1().size() > 1) {
      ::google::protobuf::scoped_array<SortItem> items(
          new SortItem[this->map1().size()]);
      typedef ::google::protobuf::Map< ::std::string, ::google::protobuf::testing::MapM >::size_type size_type;
      size_type n = 0;
      for (::google::protobuf::Map< ::std::string, ::google::protobuf::testing::MapM >::const_iterator
          it = this->map1().begin();
          it != this->map1().end(); ++it, ++n) {
        items[n] = SortItem(&*it);
      }
      ::std::sort(&items[0], &items[n], Less());
      ::google::protobuf::scoped_ptr<MapOut_Map1Entry> entry;
      for (size_type i = 0; i < n; i++) {
        entry.reset(map1_.NewEntryWrapper(
            items[i]->first, items[i]->second));
        ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
            1, *entry, output);
        Utf8Check::Check(items[i]);
      }
    } else {
      ::google::protobuf::scoped_ptr<MapOut_Map1Entry> entry;
      for (::google::protobuf::Map< ::std::string, ::google::protobuf::testing::MapM >::const_iterator
          it = this->map1().begin();
          it != this->map1().end(); ++it) {
        entry.reset(map1_.NewEntryWrapper(
            it->first, it->second));
        ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
            1, *entry, output);
        Utf8Check::Check(&*it);
      }
    }
  }

  // map<string, .google.protobuf.testing.MapOut> map2 = 2;
  if (!this->map2().empty()) {
    typedef ::google::protobuf::Map< ::std::string, ::google::protobuf::testing::MapOut >::const_pointer
        ConstPtr;
    typedef ConstPtr SortItem;
    typedef ::google::protobuf::internal::CompareByDerefFirst<SortItem> Less;
    struct Utf8Check {
      static void Check(ConstPtr p) {
        ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
          p->first.data(), p->first.length(),
          ::google::protobuf::internal::WireFormatLite::SERIALIZE,
          "google.protobuf.testing.MapOut.Map2Entry.key");
      }
    };

    if (output->IsSerializationDeterminstic() &&
        this->map2().size() > 1) {
      ::google::protobuf::scoped_array<SortItem> items(
          new SortItem[this->map2().size()]);
      typedef ::google::protobuf::Map< ::std::string, ::google::protobuf::testing::MapOut >::size_type size_type;
      size_type n = 0;
      for (::google::protobuf::Map< ::std::string, ::google::protobuf::testing::MapOut >::const_iterator
          it = this->map2().begin();
          it != this->map2().end(); ++it, ++n) {
        items[n] = SortItem(&*it);
      }
      ::std::sort(&items[0], &items[n], Less());
      ::google::protobuf::scoped_ptr<MapOut_Map2Entry> entry;
      for (size_type i = 0; i < n; i++) {
        entry.reset(map2_.NewEntryWrapper(
            items[i]->first, items[i]->second));
        ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
            2, *entry, output);
        Utf8Check::Check(items[i]);
      }
    } else {
      ::google::protobuf::scoped_ptr<MapOut_Map2Entry> entry;
      for (::google::protobuf::Map< ::std::string, ::google::protobuf::testing::MapOut >::const_iterator
          it = this->map2().begin();
          it != this->map2().end(); ++it) {
        entry.reset(map2_.NewEntryWrapper(
            it->first, it->second));
        ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
            2, *entry, output);
        Utf8Check::Check(&*it);
      }
    }
  }

  // map<int32, string> map3 = 3;
  if (!this->map3().empty()) {
    typedef ::google::protobuf::Map< ::google::protobuf::int32, ::std::string >::const_pointer
        ConstPtr;
    typedef ::google::protobuf::internal::SortItem< ::google::protobuf::int32, ConstPtr > SortItem;
    typedef ::google::protobuf::internal::CompareByFirstField<SortItem> Less;
    struct Utf8Check {
      static void Check(ConstPtr p) {
        ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
          p->second.data(), p->second.length(),
          ::google::protobuf::internal::WireFormatLite::SERIALIZE,
          "google.protobuf.testing.MapOut.Map3Entry.value");
      }
    };

    if (output->IsSerializationDeterminstic() &&
        this->map3().size() > 1) {
      ::google::protobuf::scoped_array<SortItem> items(
          new SortItem[this->map3().size()]);
      typedef ::google::protobuf::Map< ::google::protobuf::int32, ::std::string >::size_type size_type;
      size_type n = 0;
      for (::google::protobuf::Map< ::google::protobuf::int32, ::std::string >::const_iterator
          it = this->map3().begin();
          it != this->map3().end(); ++it, ++n) {
        items[n] = SortItem(&*it);
      }
      ::std::sort(&items[0], &items[n], Less());
      ::google::protobuf::scoped_ptr<MapOut_Map3Entry> entry;
      for (size_type i = 0; i < n; i++) {
        entry.reset(map3_.NewEntryWrapper(
            items[i].second->first, items[i].second->second));
        ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
            3, *entry, output);
        Utf8Check::Check(items[i].second);
      }
    } else {
      ::google::protobuf::scoped_ptr<MapOut_Map3Entry> entry;
      for (::google::protobuf::Map< ::google::protobuf::int32, ::std::string >::const_iterator
          it = this->map3().begin();
          it != this->map3().end(); ++it) {
        entry.reset(map3_.NewEntryWrapper(
            it->first, it->second));
        ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
            3, *entry, output);
        Utf8Check::Check(&*it);
      }
    }
  }

  // optional string bar = 4;
  if (this->bar().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->bar().data(), this->bar().length(),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "google.protobuf.testing.MapOut.bar");
    ::google::protobuf::internal::WireFormatLite::WriteStringMaybeAliased(
      4, this->bar(), output);
  }

  // map<bool, string> map4 = 5;
  if (!this->map4().empty()) {
    typedef ::google::protobuf::Map< bool, ::std::string >::const_pointer
        ConstPtr;
    typedef ::google::protobuf::internal::SortItem< bool, ConstPtr > SortItem;
    typedef ::google::protobuf::internal::CompareByFirstField<SortItem> Less;
    struct Utf8Check {
      static void Check(ConstPtr p) {
        ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
          p->second.data(), p->second.length(),
          ::google::protobuf::internal::WireFormatLite::SERIALIZE,
          "google.protobuf.testing.MapOut.Map4Entry.value");
      }
    };

    if (output->IsSerializationDeterminstic() &&
        this->map4().size() > 1) {
      ::google::protobuf::scoped_array<SortItem> items(
          new SortItem[this->map4().size()]);
      typedef ::google::protobuf::Map< bool, ::std::string >::size_type size_type;
      size_type n = 0;
      for (::google::protobuf::Map< bool, ::std::string >::const_iterator
          it = this->map4().begin();
          it != this->map4().end(); ++it, ++n) {
        items[n] = SortItem(&*it);
      }
      ::std::sort(&items[0], &items[n], Less());
      ::google::protobuf::scoped_ptr<MapOut_Map4Entry> entry;
      for (size_type i = 0; i < n; i++) {
        entry.reset(map4_.NewEntryWrapper(
            items[i].second->first, items[i].second->second));
        ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
            5, *entry, output);
        Utf8Check::Check(items[i].second);
      }
    } else {
      ::google::protobuf::scoped_ptr<MapOut_Map4Entry> entry;
      for (::google::protobuf::Map< bool, ::std::string >::const_iterator
          it = this->map4().begin();
          it != this->map4().end(); ++it) {
        entry.reset(map4_.NewEntryWrapper(
            it->first, it->second));
        ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
            5, *entry, output);
        Utf8Check::Check(&*it);
      }
    }
  }

  // @@protoc_insertion_point(serialize_end:google.protobuf.testing.MapOut)
}

::google::protobuf::uint8* MapOut::InternalSerializeWithCachedSizesToArray(
    bool deterministic, ::google::protobuf::uint8* target) const {
  (void)deterministic; // Unused
  // @@protoc_insertion_point(serialize_to_array_start:google.protobuf.testing.MapOut)
  // map<string, .google.protobuf.testing.MapM> map1 = 1;
  if (!this->map1().empty()) {
    typedef ::google::protobuf::Map< ::std::string, ::google::protobuf::testing::MapM >::const_pointer
        ConstPtr;
    typedef ConstPtr SortItem;
    typedef ::google::protobuf::internal::CompareByDerefFirst<SortItem> Less;
    struct Utf8Check {
      static void Check(ConstPtr p) {
        ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
          p->first.data(), p->first.length(),
          ::google::protobuf::internal::WireFormatLite::SERIALIZE,
          "google.protobuf.testing.MapOut.Map1Entry.key");
      }
    };

    if (deterministic &&
        this->map1().size() > 1) {
      ::google::protobuf::scoped_array<SortItem> items(
          new SortItem[this->map1().size()]);
      typedef ::google::protobuf::Map< ::std::string, ::google::protobuf::testing::MapM >::size_type size_type;
      size_type n = 0;
      for (::google::protobuf::Map< ::std::string, ::google::protobuf::testing::MapM >::const_iterator
          it = this->map1().begin();
          it != this->map1().end(); ++it, ++n) {
        items[n] = SortItem(&*it);
      }
      ::std::sort(&items[0], &items[n], Less());
      ::google::protobuf::scoped_ptr<MapOut_Map1Entry> entry;
      for (size_type i = 0; i < n; i++) {
        entry.reset(map1_.NewEntryWrapper(
            items[i]->first, items[i]->second));
        target = ::google::protobuf::internal::WireFormatLite::
                   InternalWriteMessageNoVirtualToArray(
                       1, *entry, deterministic, target);
;
        Utf8Check::Check(items[i]);
      }
    } else {
      ::google::protobuf::scoped_ptr<MapOut_Map1Entry> entry;
      for (::google::protobuf::Map< ::std::string, ::google::protobuf::testing::MapM >::const_iterator
          it = this->map1().begin();
          it != this->map1().end(); ++it) {
        entry.reset(map1_.NewEntryWrapper(
            it->first, it->second));
        target = ::google::protobuf::internal::WireFormatLite::
                   InternalWriteMessageNoVirtualToArray(
                       1, *entry, deterministic, target);
;
        Utf8Check::Check(&*it);
      }
    }
  }

  // map<string, .google.protobuf.testing.MapOut> map2 = 2;
  if (!this->map2().empty()) {
    typedef ::google::protobuf::Map< ::std::string, ::google::protobuf::testing::MapOut >::const_pointer
        ConstPtr;
    typedef ConstPtr SortItem;
    typedef ::google::protobuf::internal::CompareByDerefFirst<SortItem> Less;
    struct Utf8Check {
      static void Check(ConstPtr p) {
        ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
          p->first.data(), p->first.length(),
          ::google::protobuf::internal::WireFormatLite::SERIALIZE,
          "google.protobuf.testing.MapOut.Map2Entry.key");
      }
    };

    if (deterministic &&
        this->map2().size() > 1) {
      ::google::protobuf::scoped_array<SortItem> items(
          new SortItem[this->map2().size()]);
      typedef ::google::protobuf::Map< ::std::string, ::google::protobuf::testing::MapOut >::size_type size_type;
      size_type n = 0;
      for (::google::protobuf::Map< ::std::string, ::google::protobuf::testing::MapOut >::const_iterator
          it = this->map2().begin();
          it != this->map2().end(); ++it, ++n) {
        items[n] = SortItem(&*it);
      }
      ::std::sort(&items[0], &items[n], Less());
      ::google::protobuf::scoped_ptr<MapOut_Map2Entry> entry;
      for (size_type i = 0; i < n; i++) {
        entry.reset(map2_.NewEntryWrapper(
            items[i]->first, items[i]->second));
        target = ::google::protobuf::internal::WireFormatLite::
                   InternalWriteMessageNoVirtualToArray(
                       2, *entry, deterministic, target);
;
        Utf8Check::Check(items[i]);
      }
    } else {
      ::google::protobuf::scoped_ptr<MapOut_Map2Entry> entry;
      for (::google::protobuf::Map< ::std::string, ::google::protobuf::testing::MapOut >::const_iterator
          it = this->map2().begin();
          it != this->map2().end(); ++it) {
        entry.reset(map2_.NewEntryWrapper(
            it->first, it->second));
        target = ::google::protobuf::internal::WireFormatLite::
                   InternalWriteMessageNoVirtualToArray(
                       2, *entry, deterministic, target);
;
        Utf8Check::Check(&*it);
      }
    }
  }

  // map<int32, string> map3 = 3;
  if (!this->map3().empty()) {
    typedef ::google::protobuf::Map< ::google::protobuf::int32, ::std::string >::const_pointer
        ConstPtr;
    typedef ::google::protobuf::internal::SortItem< ::google::protobuf::int32, ConstPtr > SortItem;
    typedef ::google::protobuf::internal::CompareByFirstField<SortItem> Less;
    struct Utf8Check {
      static void Check(ConstPtr p) {
        ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
          p->second.data(), p->second.length(),
          ::google::protobuf::internal::WireFormatLite::SERIALIZE,
          "google.protobuf.testing.MapOut.Map3Entry.value");
      }
    };

    if (deterministic &&
        this->map3().size() > 1) {
      ::google::protobuf::scoped_array<SortItem> items(
          new SortItem[this->map3().size()]);
      typedef ::google::protobuf::Map< ::google::protobuf::int32, ::std::string >::size_type size_type;
      size_type n = 0;
      for (::google::protobuf::Map< ::google::protobuf::int32, ::std::string >::const_iterator
          it = this->map3().begin();
          it != this->map3().end(); ++it, ++n) {
        items[n] = SortItem(&*it);
      }
      ::std::sort(&items[0], &items[n], Less());
      ::google::protobuf::scoped_ptr<MapOut_Map3Entry> entry;
      for (size_type i = 0; i < n; i++) {
        entry.reset(map3_.NewEntryWrapper(
            items[i].second->first, items[i].second->second));
        target = ::google::protobuf::internal::WireFormatLite::
                   InternalWriteMessageNoVirtualToArray(
                       3, *entry, deterministic, target);
;
        Utf8Check::Check(items[i].second);
      }
    } else {
      ::google::protobuf::scoped_ptr<MapOut_Map3Entry> entry;
      for (::google::protobuf::Map< ::google::protobuf::int32, ::std::string >::const_iterator
          it = this->map3().begin();
          it != this->map3().end(); ++it) {
        entry.reset(map3_.NewEntryWrapper(
            it->first, it->second));
        target = ::google::protobuf::internal::WireFormatLite::
                   InternalWriteMessageNoVirtualToArray(
                       3, *entry, deterministic, target);
;
        Utf8Check::Check(&*it);
      }
    }
  }

  // optional string bar = 4;
  if (this->bar().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->bar().data(), this->bar().length(),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "google.protobuf.testing.MapOut.bar");
    target =
      ::google::protobuf::internal::WireFormatLite::WriteStringToArray(
        4, this->bar(), target);
  }

  // map<bool, string> map4 = 5;
  if (!this->map4().empty()) {
    typedef ::google::protobuf::Map< bool, ::std::string >::const_pointer
        ConstPtr;
    typedef ::google::protobuf::internal::SortItem< bool, ConstPtr > SortItem;
    typedef ::google::protobuf::internal::CompareByFirstField<SortItem> Less;
    struct Utf8Check {
      static void Check(ConstPtr p) {
        ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
          p->second.data(), p->second.length(),
          ::google::protobuf::internal::WireFormatLite::SERIALIZE,
          "google.protobuf.testing.MapOut.Map4Entry.value");
      }
    };

    if (deterministic &&
        this->map4().size() > 1) {
      ::google::protobuf::scoped_array<SortItem> items(
          new SortItem[this->map4().size()]);
      typedef ::google::protobuf::Map< bool, ::std::string >::size_type size_type;
      size_type n = 0;
      for (::google::protobuf::Map< bool, ::std::string >::const_iterator
          it = this->map4().begin();
          it != this->map4().end(); ++it, ++n) {
        items[n] = SortItem(&*it);
      }
      ::std::sort(&items[0], &items[n], Less());
      ::google::protobuf::scoped_ptr<MapOut_Map4Entry> entry;
      for (size_type i = 0; i < n; i++) {
        entry.reset(map4_.NewEntryWrapper(
            items[i].second->first, items[i].second->second));
        target = ::google::protobuf::internal::WireFormatLite::
                   InternalWriteMessageNoVirtualToArray(
                       5, *entry, deterministic, target);
;
        Utf8Check::Check(items[i].second);
      }
    } else {
      ::google::protobuf::scoped_ptr<MapOut_Map4Entry> entry;
      for (::google::protobuf::Map< bool, ::std::string >::const_iterator
          it = this->map4().begin();
          it != this->map4().end(); ++it) {
        entry.reset(map4_.NewEntryWrapper(
            it->first, it->second));
        target = ::google::protobuf::internal::WireFormatLite::
                   InternalWriteMessageNoVirtualToArray(
                       5, *entry, deterministic, target);
;
        Utf8Check::Check(&*it);
      }
    }
  }

  // @@protoc_insertion_point(serialize_to_array_end:google.protobuf.testing.MapOut)
  return target;
}

size_t MapOut::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:google.protobuf.testing.MapOut)
  size_t total_size = 0;

  // optional string bar = 4;
  if (this->bar().size() > 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::StringSize(
        this->bar());
  }

  // map<string, .google.protobuf.testing.MapM> map1 = 1;
  total_size += 1 *
      ::google::protobuf::internal::FromIntSize(this->map1_size());
  {
    ::google::protobuf::scoped_ptr<MapOut_Map1Entry> entry;
    for (::google::protobuf::Map< ::std::string, ::google::protobuf::testing::MapM >::const_iterator
        it = this->map1().begin();
        it != this->map1().end(); ++it) {
      entry.reset(map1_.NewEntryWrapper(it->first, it->second));
      total_size += ::google::protobuf::internal::WireFormatLite::
          MessageSizeNoVirtual(*entry);
    }
  }

  // map<string, .google.protobuf.testing.MapOut> map2 = 2;
  total_size += 1 *
      ::google::protobuf::internal::FromIntSize(this->map2_size());
  {
    ::google::protobuf::scoped_ptr<MapOut_Map2Entry> entry;
    for (::google::protobuf::Map< ::std::string, ::google::protobuf::testing::MapOut >::const_iterator
        it = this->map2().begin();
        it != this->map2().end(); ++it) {
      entry.reset(map2_.NewEntryWrapper(it->first, it->second));
      total_size += ::google::protobuf::internal::WireFormatLite::
          MessageSizeNoVirtual(*entry);
    }
  }

  // map<int32, string> map3 = 3;
  total_size += 1 *
      ::google::protobuf::internal::FromIntSize(this->map3_size());
  {
    ::google::protobuf::scoped_ptr<MapOut_Map3Entry> entry;
    for (::google::protobuf::Map< ::google::protobuf::int32, ::std::string >::const_iterator
        it = this->map3().begin();
        it != this->map3().end(); ++it) {
      entry.reset(map3_.NewEntryWrapper(it->first, it->second));
      total_size += ::google::protobuf::internal::WireFormatLite::
          MessageSizeNoVirtual(*entry);
    }
  }

  // map<bool, string> map4 = 5;
  total_size += 1 *
      ::google::protobuf::internal::FromIntSize(this->map4_size());
  {
    ::google::protobuf::scoped_ptr<MapOut_Map4Entry> entry;
    for (::google::protobuf::Map< bool, ::std::string >::const_iterator
        it = this->map4().begin();
        it != this->map4().end(); ++it) {
      entry.reset(map4_.NewEntryWrapper(it->first, it->second));
      total_size += ::google::protobuf::internal::WireFormatLite::
          MessageSizeNoVirtual(*entry);
    }
  }

  int cached_size = ::google::protobuf::internal::ToCachedSize(total_size);
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = cached_size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
  return total_size;
}

void MapOut::MergeFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_merge_from_start:google.protobuf.testing.MapOut)
  if (GOOGLE_PREDICT_FALSE(&from == this)) MergeFromFail(__LINE__);
  const MapOut* source =
      ::google::protobuf::internal::DynamicCastToGenerated<const MapOut>(
          &from);
  if (source == NULL) {
  // @@protoc_insertion_point(generalized_merge_from_cast_fail:google.protobuf.testing.MapOut)
    ::google::protobuf::internal::ReflectionOps::Merge(from, this);
  } else {
  // @@protoc_insertion_point(generalized_merge_from_cast_success:google.protobuf.testing.MapOut)
    UnsafeMergeFrom(*source);
  }
}

void MapOut::MergeFrom(const MapOut& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:google.protobuf.testing.MapOut)
  if (GOOGLE_PREDICT_TRUE(&from != this)) {
    UnsafeMergeFrom(from);
  } else {
    MergeFromFail(__LINE__);
  }
}

void MapOut::UnsafeMergeFrom(const MapOut& from) {
  GOOGLE_DCHECK(&from != this);
  map1_.MergeFrom(from.map1_);
  map2_.MergeFrom(from.map2_);
  map3_.MergeFrom(from.map3_);
  map4_.MergeFrom(from.map4_);
  if (from.bar().size() > 0) {

    bar_.AssignWithDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), from.bar_);
  }
}

void MapOut::CopyFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_copy_from_start:google.protobuf.testing.MapOut)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

void MapOut::CopyFrom(const MapOut& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:google.protobuf.testing.MapOut)
  if (&from == this) return;
  Clear();
  UnsafeMergeFrom(from);
}

bool MapOut::IsInitialized() const {

  return true;
}

void MapOut::Swap(MapOut* other) {
  if (other == this) return;
  InternalSwap(other);
}
void MapOut::InternalSwap(MapOut* other) {
  map1_.Swap(&other->map1_);
  map2_.Swap(&other->map2_);
  map3_.Swap(&other->map3_);
  map4_.Swap(&other->map4_);
  bar_.Swap(&other->bar_);
  _internal_metadata_.Swap(&other->_internal_metadata_);
  std::swap(_cached_size_, other->_cached_size_);
}

::google::protobuf::Metadata MapOut::GetMetadata() const {
  protobuf_AssignDescriptorsOnce();
  ::google::protobuf::Metadata metadata;
  metadata.descriptor = MapOut_descriptor_;
  metadata.reflection = MapOut_reflection_;
  return metadata;
}

#if PROTOBUF_INLINE_NOT_IN_HEADERS
// MapOut

// map<string, .google.protobuf.testing.MapM> map1 = 1;
int MapOut::map1_size() const {
  return map1_.size();
}
void MapOut::clear_map1() {
  map1_.Clear();
}
 const ::google::protobuf::Map< ::std::string, ::google::protobuf::testing::MapM >&
MapOut::map1() const {
  // @@protoc_insertion_point(field_map:google.protobuf.testing.MapOut.map1)
  return map1_.GetMap();
}
 ::google::protobuf::Map< ::std::string, ::google::protobuf::testing::MapM >*
MapOut::mutable_map1() {
  // @@protoc_insertion_point(field_mutable_map:google.protobuf.testing.MapOut.map1)
  return map1_.MutableMap();
}

// map<string, .google.protobuf.testing.MapOut> map2 = 2;
int MapOut::map2_size() const {
  return map2_.size();
}
void MapOut::clear_map2() {
  map2_.Clear();
}
 const ::google::protobuf::Map< ::std::string, ::google::protobuf::testing::MapOut >&
MapOut::map2() const {
  // @@protoc_insertion_point(field_map:google.protobuf.testing.MapOut.map2)
  return map2_.GetMap();
}
 ::google::protobuf::Map< ::std::string, ::google::protobuf::testing::MapOut >*
MapOut::mutable_map2() {
  // @@protoc_insertion_point(field_mutable_map:google.protobuf.testing.MapOut.map2)
  return map2_.MutableMap();
}

// map<int32, string> map3 = 3;
int MapOut::map3_size() const {
  return map3_.size();
}
void MapOut::clear_map3() {
  map3_.Clear();
}
 const ::google::protobuf::Map< ::google::protobuf::int32, ::std::string >&
MapOut::map3() const {
  // @@protoc_insertion_point(field_map:google.protobuf.testing.MapOut.map3)
  return map3_.GetMap();
}
 ::google::protobuf::Map< ::google::protobuf::int32, ::std::string >*
MapOut::mutable_map3() {
  // @@protoc_insertion_point(field_mutable_map:google.protobuf.testing.MapOut.map3)
  return map3_.MutableMap();
}

// map<bool, string> map4 = 5;
int MapOut::map4_size() const {
  return map4_.size();
}
void MapOut::clear_map4() {
  map4_.Clear();
}
 const ::google::protobuf::Map< bool, ::std::string >&
MapOut::map4() const {
  // @@protoc_insertion_point(field_map:google.protobuf.testing.MapOut.map4)
  return map4_.GetMap();
}
 ::google::protobuf::Map< bool, ::std::string >*
MapOut::mutable_map4() {
  // @@protoc_insertion_point(field_mutable_map:google.protobuf.testing.MapOut.map4)
  return map4_.MutableMap();
}

// optional string bar = 4;
void MapOut::clear_bar() {
  bar_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
const ::std::string& MapOut::bar() const {
  // @@protoc_insertion_point(field_get:google.protobuf.testing.MapOut.bar)
  return bar_.GetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
void MapOut::set_bar(const ::std::string& value) {
  
  bar_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:google.protobuf.testing.MapOut.bar)
}
void MapOut::set_bar(const char* value) {
  
  bar_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:google.protobuf.testing.MapOut.bar)
}
void MapOut::set_bar(const char* value, size_t size) {
  
  bar_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:google.protobuf.testing.MapOut.bar)
}
::std::string* MapOut::mutable_bar() {
  
  // @@protoc_insertion_point(field_mutable:google.protobuf.testing.MapOut.bar)
  return bar_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
::std::string* MapOut::release_bar() {
  // @@protoc_insertion_point(field_release:google.protobuf.testing.MapOut.bar)
  
  return bar_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
void MapOut::set_allocated_bar(::std::string* bar) {
  if (bar != NULL) {
    
  } else {
    
  }
  bar_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), bar);
  // @@protoc_insertion_point(field_set_allocated:google.protobuf.testing.MapOut.bar)
}

inline const MapOut* MapOut::internal_default_instance() {
  return &MapOut_default_instance_.get();
}
#endif  // PROTOBUF_INLINE_NOT_IN_HEADERS

// ===================================================================

#if !defined(_MSC_VER) || _MSC_VER >= 1900
const int MapOutWireFormat_Map1Entry::kKeyFieldNumber;
const int MapOutWireFormat_Map1Entry::kValueFieldNumber;
#endif  // !defined(_MSC_VER) || _MSC_VER >= 1900

MapOutWireFormat_Map1Entry::MapOutWireFormat_Map1Entry()
  : ::google::protobuf::Message(), _internal_metadata_(NULL) {
  if (this != internal_default_instance()) protobuf_InitDefaults_google_2fprotobuf_2futil_2finternal_2ftestdata_2fmaps_2eproto();
  SharedCtor();
  // @@protoc_insertion_point(constructor:google.protobuf.testing.MapOutWireFormat.Map1Entry)
}

void MapOutWireFormat_Map1Entry::InitAsDefaultInstance() {
  value_ = const_cast< ::google::protobuf::testing::MapM*>(
      ::google::protobuf::testing::MapM::internal_default_instance());
}

MapOutWireFormat_Map1Entry::MapOutWireFormat_Map1Entry(const MapOutWireFormat_Map1Entry& from)
  : ::google::protobuf::Message(),
    _internal_metadata_(NULL) {
  SharedCtor();
  UnsafeMergeFrom(from);
  // @@protoc_insertion_point(copy_constructor:google.protobuf.testing.MapOutWireFormat.Map1Entry)
}

void MapOutWireFormat_Map1Entry::SharedCtor() {
  key_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  value_ = NULL;
  _cached_size_ = 0;
}

MapOutWireFormat_Map1Entry::~MapOutWireFormat_Map1Entry() {
  // @@protoc_insertion_point(destructor:google.protobuf.testing.MapOutWireFormat.Map1Entry)
  SharedDtor();
}

void MapOutWireFormat_Map1Entry::SharedDtor() {
  key_.DestroyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  if (this != &MapOutWireFormat_Map1Entry_default_instance_.get()) {
    delete value_;
  }
}

void MapOutWireFormat_Map1Entry::SetCachedSize(int size) const {
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
}
const ::google::protobuf::Descriptor* MapOutWireFormat_Map1Entry::descriptor() {
  protobuf_AssignDescriptorsOnce();
  return MapOutWireFormat_Map1Entry_descriptor_;
}

const MapOutWireFormat_Map1Entry& MapOutWireFormat_Map1Entry::default_instance() {
  protobuf_InitDefaults_google_2fprotobuf_2futil_2finternal_2ftestdata_2fmaps_2eproto();
  return *internal_default_instance();
}

::google::protobuf::internal::ExplicitlyConstructed<MapOutWireFormat_Map1Entry> MapOutWireFormat_Map1Entry_default_instance_;

MapOutWireFormat_Map1Entry* MapOutWireFormat_Map1Entry::New(::google::protobuf::Arena* arena) const {
  MapOutWireFormat_Map1Entry* n = new MapOutWireFormat_Map1Entry;
  if (arena != NULL) {
    arena->Own(n);
  }
  return n;
}

void MapOutWireFormat_Map1Entry::Clear() {
// @@protoc_insertion_point(message_clear_start:google.protobuf.testing.MapOutWireFormat.Map1Entry)
  key_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  if (GetArenaNoVirtual() == NULL && value_ != NULL) delete value_;
  value_ = NULL;
}

bool MapOutWireFormat_Map1Entry::MergePartialFromCodedStream(
    ::google::protobuf::io::CodedInputStream* input) {
#define DO_(EXPRESSION) if (!GOOGLE_PREDICT_TRUE(EXPRESSION)) goto failure
  ::google::protobuf::uint32 tag;
  // @@protoc_insertion_point(parse_start:google.protobuf.testing.MapOutWireFormat.Map1Entry)
  for (;;) {
    ::std::pair< ::google::protobuf::uint32, bool> p = input->ReadTagWithCutoff(127);
    tag = p.first;
    if (!p.second) goto handle_unusual;
    switch (::google::protobuf::internal::WireFormatLite::GetTagFieldNumber(tag)) {
      // optional string key = 1;
      case 1: {
        if (tag == 10) {
          DO_(::google::protobuf::internal::WireFormatLite::ReadString(
                input, this->mutable_key()));
          DO_(::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
            this->key().data(), this->key().length(),
            ::google::protobuf::internal::WireFormatLite::PARSE,
            "google.protobuf.testing.MapOutWireFormat.Map1Entry.key"));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(18)) goto parse_value;
        break;
      }

      // optional .google.protobuf.testing.MapM value = 2;
      case 2: {
        if (tag == 18) {
         parse_value:
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessageNoVirtual(
               input, mutable_value()));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectAtEnd()) goto success;
        break;
      }

      default: {
      handle_unusual:
        if (tag == 0 ||
            ::google::protobuf::internal::WireFormatLite::GetTagWireType(tag) ==
            ::google::protobuf::internal::WireFormatLite::WIRETYPE_END_GROUP) {
          goto success;
        }
        DO_(::google::protobuf::internal::WireFormatLite::SkipField(input, tag));
        break;
      }
    }
  }
success:
  // @@protoc_insertion_point(parse_success:google.protobuf.testing.MapOutWireFormat.Map1Entry)
  return true;
failure:
  // @@protoc_insertion_point(parse_failure:google.protobuf.testing.MapOutWireFormat.Map1Entry)
  return false;
#undef DO_
}

void MapOutWireFormat_Map1Entry::SerializeWithCachedSizes(
    ::google::protobuf::io::CodedOutputStream* output) const {
  // @@protoc_insertion_point(serialize_start:google.protobuf.testing.MapOutWireFormat.Map1Entry)
  // optional string key = 1;
  if (this->key().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->key().data(), this->key().length(),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "google.protobuf.testing.MapOutWireFormat.Map1Entry.key");
    ::google::protobuf::internal::WireFormatLite::WriteStringMaybeAliased(
      1, this->key(), output);
  }

  // optional .google.protobuf.testing.MapM value = 2;
  if (this->has_value()) {
    ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
      2, *this->value_, output);
  }

  // @@protoc_insertion_point(serialize_end:google.protobuf.testing.MapOutWireFormat.Map1Entry)
}

::google::protobuf::uint8* MapOutWireFormat_Map1Entry::InternalSerializeWithCachedSizesToArray(
    bool deterministic, ::google::protobuf::uint8* target) const {
  (void)deterministic; // Unused
  // @@protoc_insertion_point(serialize_to_array_start:google.protobuf.testing.MapOutWireFormat.Map1Entry)
  // optional string key = 1;
  if (this->key().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->key().data(), this->key().length(),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "google.protobuf.testing.MapOutWireFormat.Map1Entry.key");
    target =
      ::google::protobuf::internal::WireFormatLite::WriteStringToArray(
        1, this->key(), target);
  }

  // optional .google.protobuf.testing.MapM value = 2;
  if (this->has_value()) {
    target = ::google::protobuf::internal::WireFormatLite::
      InternalWriteMessageNoVirtualToArray(
        2, *this->value_, false, target);
  }

  // @@protoc_insertion_point(serialize_to_array_end:google.protobuf.testing.MapOutWireFormat.Map1Entry)
  return target;
}

size_t MapOutWireFormat_Map1Entry::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:google.protobuf.testing.MapOutWireFormat.Map1Entry)
  size_t total_size = 0;

  // optional string key = 1;
  if (this->key().size() > 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::StringSize(
        this->key());
  }

  // optional .google.protobuf.testing.MapM value = 2;
  if (this->has_value()) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::MessageSizeNoVirtual(
        *this->value_);
  }

  int cached_size = ::google::protobuf::internal::ToCachedSize(total_size);
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = cached_size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
  return total_size;
}

void MapOutWireFormat_Map1Entry::MergeFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_merge_from_start:google.protobuf.testing.MapOutWireFormat.Map1Entry)
  if (GOOGLE_PREDICT_FALSE(&from == this)) MergeFromFail(__LINE__);
  const MapOutWireFormat_Map1Entry* source =
      ::google::protobuf::internal::DynamicCastToGenerated<const MapOutWireFormat_Map1Entry>(
          &from);
  if (source == NULL) {
  // @@protoc_insertion_point(generalized_merge_from_cast_fail:google.protobuf.testing.MapOutWireFormat.Map1Entry)
    ::google::protobuf::internal::ReflectionOps::Merge(from, this);
  } else {
  // @@protoc_insertion_point(generalized_merge_from_cast_success:google.protobuf.testing.MapOutWireFormat.Map1Entry)
    UnsafeMergeFrom(*source);
  }
}

void MapOutWireFormat_Map1Entry::MergeFrom(const MapOutWireFormat_Map1Entry& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:google.protobuf.testing.MapOutWireFormat.Map1Entry)
  if (GOOGLE_PREDICT_TRUE(&from != this)) {
    UnsafeMergeFrom(from);
  } else {
    MergeFromFail(__LINE__);
  }
}

void MapOutWireFormat_Map1Entry::UnsafeMergeFrom(const MapOutWireFormat_Map1Entry& from) {
  GOOGLE_DCHECK(&from != this);
  if (from.key().size() > 0) {

    key_.AssignWithDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), from.key_);
  }
  if (from.has_value()) {
    mutable_value()->::google::protobuf::testing::MapM::MergeFrom(from.value());
  }
}

void MapOutWireFormat_Map1Entry::CopyFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_copy_from_start:google.protobuf.testing.MapOutWireFormat.Map1Entry)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

void MapOutWireFormat_Map1Entry::CopyFrom(const MapOutWireFormat_Map1Entry& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:google.protobuf.testing.MapOutWireFormat.Map1Entry)
  if (&from == this) return;
  Clear();
  UnsafeMergeFrom(from);
}

bool MapOutWireFormat_Map1Entry::IsInitialized() const {

  return true;
}

void MapOutWireFormat_Map1Entry::Swap(MapOutWireFormat_Map1Entry* other) {
  if (other == this) return;
  InternalSwap(other);
}
void MapOutWireFormat_Map1Entry::InternalSwap(MapOutWireFormat_Map1Entry* other) {
  key_.Swap(&other->key_);
  std::swap(value_, other->value_);
  _internal_metadata_.Swap(&other->_internal_metadata_);
  std::swap(_cached_size_, other->_cached_size_);
}

::google::protobuf::Metadata MapOutWireFormat_Map1Entry::GetMetadata() const {
  protobuf_AssignDescriptorsOnce();
  ::google::protobuf::Metadata metadata;
  metadata.descriptor = MapOutWireFormat_Map1Entry_descriptor_;
  metadata.reflection = MapOutWireFormat_Map1Entry_reflection_;
  return metadata;
}


// -------------------------------------------------------------------

#if !defined(_MSC_VER) || _MSC_VER >= 1900
const int MapOutWireFormat_Map2Entry::kKeyFieldNumber;
const int MapOutWireFormat_Map2Entry::kValueFieldNumber;
#endif  // !defined(_MSC_VER) || _MSC_VER >= 1900

MapOutWireFormat_Map2Entry::MapOutWireFormat_Map2Entry()
  : ::google::protobuf::Message(), _internal_metadata_(NULL) {
  if (this != internal_default_instance()) protobuf_InitDefaults_google_2fprotobuf_2futil_2finternal_2ftestdata_2fmaps_2eproto();
  SharedCtor();
  // @@protoc_insertion_point(constructor:google.protobuf.testing.MapOutWireFormat.Map2Entry)
}

void MapOutWireFormat_Map2Entry::InitAsDefaultInstance() {
  value_ = const_cast< ::google::protobuf::testing::MapOut*>(
      ::google::protobuf::testing::MapOut::internal_default_instance());
}

MapOutWireFormat_Map2Entry::MapOutWireFormat_Map2Entry(const MapOutWireFormat_Map2Entry& from)
  : ::google::protobuf::Message(),
    _internal_metadata_(NULL) {
  SharedCtor();
  UnsafeMergeFrom(from);
  // @@protoc_insertion_point(copy_constructor:google.protobuf.testing.MapOutWireFormat.Map2Entry)
}

void MapOutWireFormat_Map2Entry::SharedCtor() {
  key_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  value_ = NULL;
  _cached_size_ = 0;
}

MapOutWireFormat_Map2Entry::~MapOutWireFormat_Map2Entry() {
  // @@protoc_insertion_point(destructor:google.protobuf.testing.MapOutWireFormat.Map2Entry)
  SharedDtor();
}

void MapOutWireFormat_Map2Entry::SharedDtor() {
  key_.DestroyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  if (this != &MapOutWireFormat_Map2Entry_default_instance_.get()) {
    delete value_;
  }
}

void MapOutWireFormat_Map2Entry::SetCachedSize(int size) const {
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
}
const ::google::protobuf::Descriptor* MapOutWireFormat_Map2Entry::descriptor() {
  protobuf_AssignDescriptorsOnce();
  return MapOutWireFormat_Map2Entry_descriptor_;
}

const MapOutWireFormat_Map2Entry& MapOutWireFormat_Map2Entry::default_instance() {
  protobuf_InitDefaults_google_2fprotobuf_2futil_2finternal_2ftestdata_2fmaps_2eproto();
  return *internal_default_instance();
}

::google::protobuf::internal::ExplicitlyConstructed<MapOutWireFormat_Map2Entry> MapOutWireFormat_Map2Entry_default_instance_;

MapOutWireFormat_Map2Entry* MapOutWireFormat_Map2Entry::New(::google::protobuf::Arena* arena) const {
  MapOutWireFormat_Map2Entry* n = new MapOutWireFormat_Map2Entry;
  if (arena != NULL) {
    arena->Own(n);
  }
  return n;
}

void MapOutWireFormat_Map2Entry::Clear() {
// @@protoc_insertion_point(message_clear_start:google.protobuf.testing.MapOutWireFormat.Map2Entry)
  key_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  if (GetArenaNoVirtual() == NULL && value_ != NULL) delete value_;
  value_ = NULL;
}

bool MapOutWireFormat_Map2Entry::MergePartialFromCodedStream(
    ::google::protobuf::io::CodedInputStream* input) {
#define DO_(EXPRESSION) if (!GOOGLE_PREDICT_TRUE(EXPRESSION)) goto failure
  ::google::protobuf::uint32 tag;
  // @@protoc_insertion_point(parse_start:google.protobuf.testing.MapOutWireFormat.Map2Entry)
  for (;;) {
    ::std::pair< ::google::protobuf::uint32, bool> p = input->ReadTagWithCutoff(127);
    tag = p.first;
    if (!p.second) goto handle_unusual;
    switch (::google::protobuf::internal::WireFormatLite::GetTagFieldNumber(tag)) {
      // optional string key = 1;
      case 1: {
        if (tag == 10) {
          DO_(::google::protobuf::internal::WireFormatLite::ReadString(
                input, this->mutable_key()));
          DO_(::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
            this->key().data(), this->key().length(),
            ::google::protobuf::internal::WireFormatLite::PARSE,
            "google.protobuf.testing.MapOutWireFormat.Map2Entry.key"));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(18)) goto parse_value;
        break;
      }

      // optional .google.protobuf.testing.MapOut value = 2;
      case 2: {
        if (tag == 18) {
         parse_value:
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessageNoVirtual(
               input, mutable_value()));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectAtEnd()) goto success;
        break;
      }

      default: {
      handle_unusual:
        if (tag == 0 ||
            ::google::protobuf::internal::WireFormatLite::GetTagWireType(tag) ==
            ::google::protobuf::internal::WireFormatLite::WIRETYPE_END_GROUP) {
          goto success;
        }
        DO_(::google::protobuf::internal::WireFormatLite::SkipField(input, tag));
        break;
      }
    }
  }
success:
  // @@protoc_insertion_point(parse_success:google.protobuf.testing.MapOutWireFormat.Map2Entry)
  return true;
failure:
  // @@protoc_insertion_point(parse_failure:google.protobuf.testing.MapOutWireFormat.Map2Entry)
  return false;
#undef DO_
}

void MapOutWireFormat_Map2Entry::SerializeWithCachedSizes(
    ::google::protobuf::io::CodedOutputStream* output) const {
  // @@protoc_insertion_point(serialize_start:google.protobuf.testing.MapOutWireFormat.Map2Entry)
  // optional string key = 1;
  if (this->key().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->key().data(), this->key().length(),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "google.protobuf.testing.MapOutWireFormat.Map2Entry.key");
    ::google::protobuf::internal::WireFormatLite::WriteStringMaybeAliased(
      1, this->key(), output);
  }

  // optional .google.protobuf.testing.MapOut value = 2;
  if (this->has_value()) {
    ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
      2, *this->value_, output);
  }

  // @@protoc_insertion_point(serialize_end:google.protobuf.testing.MapOutWireFormat.Map2Entry)
}

::google::protobuf::uint8* MapOutWireFormat_Map2Entry::InternalSerializeWithCachedSizesToArray(
    bool deterministic, ::google::protobuf::uint8* target) const {
  (void)deterministic; // Unused
  // @@protoc_insertion_point(serialize_to_array_start:google.protobuf.testing.MapOutWireFormat.Map2Entry)
  // optional string key = 1;
  if (this->key().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->key().data(), this->key().length(),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "google.protobuf.testing.MapOutWireFormat.Map2Entry.key");
    target =
      ::google::protobuf::internal::WireFormatLite::WriteStringToArray(
        1, this->key(), target);
  }

  // optional .google.protobuf.testing.MapOut value = 2;
  if (this->has_value()) {
    target = ::google::protobuf::internal::WireFormatLite::
      InternalWriteMessageNoVirtualToArray(
        2, *this->value_, false, target);
  }

  // @@protoc_insertion_point(serialize_to_array_end:google.protobuf.testing.MapOutWireFormat.Map2Entry)
  return target;
}

size_t MapOutWireFormat_Map2Entry::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:google.protobuf.testing.MapOutWireFormat.Map2Entry)
  size_t total_size = 0;

  // optional string key = 1;
  if (this->key().size() > 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::StringSize(
        this->key());
  }

  // optional .google.protobuf.testing.MapOut value = 2;
  if (this->has_value()) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::MessageSizeNoVirtual(
        *this->value_);
  }

  int cached_size = ::google::protobuf::internal::ToCachedSize(total_size);
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = cached_size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
  return total_size;
}

void MapOutWireFormat_Map2Entry::MergeFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_merge_from_start:google.protobuf.testing.MapOutWireFormat.Map2Entry)
  if (GOOGLE_PREDICT_FALSE(&from == this)) MergeFromFail(__LINE__);
  const MapOutWireFormat_Map2Entry* source =
      ::google::protobuf::internal::DynamicCastToGenerated<const MapOutWireFormat_Map2Entry>(
          &from);
  if (source == NULL) {
  // @@protoc_insertion_point(generalized_merge_from_cast_fail:google.protobuf.testing.MapOutWireFormat.Map2Entry)
    ::google::protobuf::internal::ReflectionOps::Merge(from, this);
  } else {
  // @@protoc_insertion_point(generalized_merge_from_cast_success:google.protobuf.testing.MapOutWireFormat.Map2Entry)
    UnsafeMergeFrom(*source);
  }
}

void MapOutWireFormat_Map2Entry::MergeFrom(const MapOutWireFormat_Map2Entry& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:google.protobuf.testing.MapOutWireFormat.Map2Entry)
  if (GOOGLE_PREDICT_TRUE(&from != this)) {
    UnsafeMergeFrom(from);
  } else {
    MergeFromFail(__LINE__);
  }
}

void MapOutWireFormat_Map2Entry::UnsafeMergeFrom(const MapOutWireFormat_Map2Entry& from) {
  GOOGLE_DCHECK(&from != this);
  if (from.key().size() > 0) {

    key_.AssignWithDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), from.key_);
  }
  if (from.has_value()) {
    mutable_value()->::google::protobuf::testing::MapOut::MergeFrom(from.value());
  }
}

void MapOutWireFormat_Map2Entry::CopyFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_copy_from_start:google.protobuf.testing.MapOutWireFormat.Map2Entry)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

void MapOutWireFormat_Map2Entry::CopyFrom(const MapOutWireFormat_Map2Entry& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:google.protobuf.testing.MapOutWireFormat.Map2Entry)
  if (&from == this) return;
  Clear();
  UnsafeMergeFrom(from);
}

bool MapOutWireFormat_Map2Entry::IsInitialized() const {

  return true;
}

void MapOutWireFormat_Map2Entry::Swap(MapOutWireFormat_Map2Entry* other) {
  if (other == this) return;
  InternalSwap(other);
}
void MapOutWireFormat_Map2Entry::InternalSwap(MapOutWireFormat_Map2Entry* other) {
  key_.Swap(&other->key_);
  std::swap(value_, other->value_);
  _internal_metadata_.Swap(&other->_internal_metadata_);
  std::swap(_cached_size_, other->_cached_size_);
}

::google::protobuf::Metadata MapOutWireFormat_Map2Entry::GetMetadata() const {
  protobuf_AssignDescriptorsOnce();
  ::google::protobuf::Metadata metadata;
  metadata.descriptor = MapOutWireFormat_Map2Entry_descriptor_;
  metadata.reflection = MapOutWireFormat_Map2Entry_reflection_;
  return metadata;
}


// -------------------------------------------------------------------

#if !defined(_MSC_VER) || _MSC_VER >= 1900
const int MapOutWireFormat_Map3Entry::kKeyFieldNumber;
const int MapOutWireFormat_Map3Entry::kValueFieldNumber;
#endif  // !defined(_MSC_VER) || _MSC_VER >= 1900

MapOutWireFormat_Map3Entry::MapOutWireFormat_Map3Entry()
  : ::google::protobuf::Message(), _internal_metadata_(NULL) {
  if (this != internal_default_instance()) protobuf_InitDefaults_google_2fprotobuf_2futil_2finternal_2ftestdata_2fmaps_2eproto();
  SharedCtor();
  // @@protoc_insertion_point(constructor:google.protobuf.testing.MapOutWireFormat.Map3Entry)
}

void MapOutWireFormat_Map3Entry::InitAsDefaultInstance() {
}

MapOutWireFormat_Map3Entry::MapOutWireFormat_Map3Entry(const MapOutWireFormat_Map3Entry& from)
  : ::google::protobuf::Message(),
    _internal_metadata_(NULL) {
  SharedCtor();
  UnsafeMergeFrom(from);
  // @@protoc_insertion_point(copy_constructor:google.protobuf.testing.MapOutWireFormat.Map3Entry)
}

void MapOutWireFormat_Map3Entry::SharedCtor() {
  value_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  key_ = 0;
  _cached_size_ = 0;
}

MapOutWireFormat_Map3Entry::~MapOutWireFormat_Map3Entry() {
  // @@protoc_insertion_point(destructor:google.protobuf.testing.MapOutWireFormat.Map3Entry)
  SharedDtor();
}

void MapOutWireFormat_Map3Entry::SharedDtor() {
  value_.DestroyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}

void MapOutWireFormat_Map3Entry::SetCachedSize(int size) const {
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
}
const ::google::protobuf::Descriptor* MapOutWireFormat_Map3Entry::descriptor() {
  protobuf_AssignDescriptorsOnce();
  return MapOutWireFormat_Map3Entry_descriptor_;
}

const MapOutWireFormat_Map3Entry& MapOutWireFormat_Map3Entry::default_instance() {
  protobuf_InitDefaults_google_2fprotobuf_2futil_2finternal_2ftestdata_2fmaps_2eproto();
  return *internal_default_instance();
}

::google::protobuf::internal::ExplicitlyConstructed<MapOutWireFormat_Map3Entry> MapOutWireFormat_Map3Entry_default_instance_;

MapOutWireFormat_Map3Entry* MapOutWireFormat_Map3Entry::New(::google::protobuf::Arena* arena) const {
  MapOutWireFormat_Map3Entry* n = new MapOutWireFormat_Map3Entry;
  if (arena != NULL) {
    arena->Own(n);
  }
  return n;
}

void MapOutWireFormat_Map3Entry::Clear() {
// @@protoc_insertion_point(message_clear_start:google.protobuf.testing.MapOutWireFormat.Map3Entry)
  key_ = 0;
  value_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}

bool MapOutWireFormat_Map3Entry::MergePartialFromCodedStream(
    ::google::protobuf::io::CodedInputStream* input) {
#define DO_(EXPRESSION) if (!GOOGLE_PREDICT_TRUE(EXPRESSION)) goto failure
  ::google::protobuf::uint32 tag;
  // @@protoc_insertion_point(parse_start:google.protobuf.testing.MapOutWireFormat.Map3Entry)
  for (;;) {
    ::std::pair< ::google::protobuf::uint32, bool> p = input->ReadTagWithCutoff(127);
    tag = p.first;
    if (!p.second) goto handle_unusual;
    switch (::google::protobuf::internal::WireFormatLite::GetTagFieldNumber(tag)) {
      // optional int32 key = 1;
      case 1: {
        if (tag == 8) {

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int32, ::google::protobuf::internal::WireFormatLite::TYPE_INT32>(
                 input, &key_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(18)) goto parse_value;
        break;
      }

      // optional string value = 2;
      case 2: {
        if (tag == 18) {
         parse_value:
          DO_(::google::protobuf::internal::WireFormatLite::ReadString(
                input, this->mutable_value()));
          DO_(::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
            this->value().data(), this->value().length(),
            ::google::protobuf::internal::WireFormatLite::PARSE,
            "google.protobuf.testing.MapOutWireFormat.Map3Entry.value"));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectAtEnd()) goto success;
        break;
      }

      default: {
      handle_unusual:
        if (tag == 0 ||
            ::google::protobuf::internal::WireFormatLite::GetTagWireType(tag) ==
            ::google::protobuf::internal::WireFormatLite::WIRETYPE_END_GROUP) {
          goto success;
        }
        DO_(::google::protobuf::internal::WireFormatLite::SkipField(input, tag));
        break;
      }
    }
  }
success:
  // @@protoc_insertion_point(parse_success:google.protobuf.testing.MapOutWireFormat.Map3Entry)
  return true;
failure:
  // @@protoc_insertion_point(parse_failure:google.protobuf.testing.MapOutWireFormat.Map3Entry)
  return false;
#undef DO_
}

void MapOutWireFormat_Map3Entry::SerializeWithCachedSizes(
    ::google::protobuf::io::CodedOutputStream* output) const {
  // @@protoc_insertion_point(serialize_start:google.protobuf.testing.MapOutWireFormat.Map3Entry)
  // optional int32 key = 1;
  if (this->key() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt32(1, this->key(), output);
  }

  // optional string value = 2;
  if (this->value().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->value().data(), this->value().length(),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "google.protobuf.testing.MapOutWireFormat.Map3Entry.value");
    ::google::protobuf::internal::WireFormatLite::WriteStringMaybeAliased(
      2, this->value(), output);
  }

  // @@protoc_insertion_point(serialize_end:google.protobuf.testing.MapOutWireFormat.Map3Entry)
}

::google::protobuf::uint8* MapOutWireFormat_Map3Entry::InternalSerializeWithCachedSizesToArray(
    bool deterministic, ::google::protobuf::uint8* target) const {
  (void)deterministic; // Unused
  // @@protoc_insertion_point(serialize_to_array_start:google.protobuf.testing.MapOutWireFormat.Map3Entry)
  // optional int32 key = 1;
  if (this->key() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt32ToArray(1, this->key(), target);
  }

  // optional string value = 2;
  if (this->value().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->value().data(), this->value().length(),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "google.protobuf.testing.MapOutWireFormat.Map3Entry.value");
    target =
      ::google::protobuf::internal::WireFormatLite::WriteStringToArray(
        2, this->value(), target);
  }

  // @@protoc_insertion_point(serialize_to_array_end:google.protobuf.testing.MapOutWireFormat.Map3Entry)
  return target;
}

size_t MapOutWireFormat_Map3Entry::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:google.protobuf.testing.MapOutWireFormat.Map3Entry)
  size_t total_size = 0;

  // optional int32 key = 1;
  if (this->key() != 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::Int32Size(
        this->key());
  }

  // optional string value = 2;
  if (this->value().size() > 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::StringSize(
        this->value());
  }

  int cached_size = ::google::protobuf::internal::ToCachedSize(total_size);
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = cached_size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
  return total_size;
}

void MapOutWireFormat_Map3Entry::MergeFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_merge_from_start:google.protobuf.testing.MapOutWireFormat.Map3Entry)
  if (GOOGLE_PREDICT_FALSE(&from == this)) MergeFromFail(__LINE__);
  const MapOutWireFormat_Map3Entry* source =
      ::google::protobuf::internal::DynamicCastToGenerated<const MapOutWireFormat_Map3Entry>(
          &from);
  if (source == NULL) {
  // @@protoc_insertion_point(generalized_merge_from_cast_fail:google.protobuf.testing.MapOutWireFormat.Map3Entry)
    ::google::protobuf::internal::ReflectionOps::Merge(from, this);
  } else {
  // @@protoc_insertion_point(generalized_merge_from_cast_success:google.protobuf.testing.MapOutWireFormat.Map3Entry)
    UnsafeMergeFrom(*source);
  }
}

void MapOutWireFormat_Map3Entry::MergeFrom(const MapOutWireFormat_Map3Entry& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:google.protobuf.testing.MapOutWireFormat.Map3Entry)
  if (GOOGLE_PREDICT_TRUE(&from != this)) {
    UnsafeMergeFrom(from);
  } else {
    MergeFromFail(__LINE__);
  }
}

void MapOutWireFormat_Map3Entry::UnsafeMergeFrom(const MapOutWireFormat_Map3Entry& from) {
  GOOGLE_DCHECK(&from != this);
  if (from.key() != 0) {
    set_key(from.key());
  }
  if (from.value().size() > 0) {

    value_.AssignWithDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), from.value_);
  }
}

void MapOutWireFormat_Map3Entry::CopyFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_copy_from_start:google.protobuf.testing.MapOutWireFormat.Map3Entry)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

void MapOutWireFormat_Map3Entry::CopyFrom(const MapOutWireFormat_Map3Entry& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:google.protobuf.testing.MapOutWireFormat.Map3Entry)
  if (&from == this) return;
  Clear();
  UnsafeMergeFrom(from);
}

bool MapOutWireFormat_Map3Entry::IsInitialized() const {

  return true;
}

void MapOutWireFormat_Map3Entry::Swap(MapOutWireFormat_Map3Entry* other) {
  if (other == this) return;
  InternalSwap(other);
}
void MapOutWireFormat_Map3Entry::InternalSwap(MapOutWireFormat_Map3Entry* other) {
  std::swap(key_, other->key_);
  value_.Swap(&other->value_);
  _internal_metadata_.Swap(&other->_internal_metadata_);
  std::swap(_cached_size_, other->_cached_size_);
}

::google::protobuf::Metadata MapOutWireFormat_Map3Entry::GetMetadata() const {
  protobuf_AssignDescriptorsOnce();
  ::google::protobuf::Metadata metadata;
  metadata.descriptor = MapOutWireFormat_Map3Entry_descriptor_;
  metadata.reflection = MapOutWireFormat_Map3Entry_reflection_;
  return metadata;
}


// -------------------------------------------------------------------

#if !defined(_MSC_VER) || _MSC_VER >= 1900
const int MapOutWireFormat_Map4Entry::kKeyFieldNumber;
const int MapOutWireFormat_Map4Entry::kValueFieldNumber;
#endif  // !defined(_MSC_VER) || _MSC_VER >= 1900

MapOutWireFormat_Map4Entry::MapOutWireFormat_Map4Entry()
  : ::google::protobuf::Message(), _internal_metadata_(NULL) {
  if (this != internal_default_instance()) protobuf_InitDefaults_google_2fprotobuf_2futil_2finternal_2ftestdata_2fmaps_2eproto();
  SharedCtor();
  // @@protoc_insertion_point(constructor:google.protobuf.testing.MapOutWireFormat.Map4Entry)
}

void MapOutWireFormat_Map4Entry::InitAsDefaultInstance() {
}

MapOutWireFormat_Map4Entry::MapOutWireFormat_Map4Entry(const MapOutWireFormat_Map4Entry& from)
  : ::google::protobuf::Message(),
    _internal_metadata_(NULL) {
  SharedCtor();
  UnsafeMergeFrom(from);
  // @@protoc_insertion_point(copy_constructor:google.protobuf.testing.MapOutWireFormat.Map4Entry)
}

void MapOutWireFormat_Map4Entry::SharedCtor() {
  value_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  key_ = false;
  _cached_size_ = 0;
}

MapOutWireFormat_Map4Entry::~MapOutWireFormat_Map4Entry() {
  // @@protoc_insertion_point(destructor:google.protobuf.testing.MapOutWireFormat.Map4Entry)
  SharedDtor();
}

void MapOutWireFormat_Map4Entry::SharedDtor() {
  value_.DestroyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}

void MapOutWireFormat_Map4Entry::SetCachedSize(int size) const {
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
}
const ::google::protobuf::Descriptor* MapOutWireFormat_Map4Entry::descriptor() {
  protobuf_AssignDescriptorsOnce();
  return MapOutWireFormat_Map4Entry_descriptor_;
}

const MapOutWireFormat_Map4Entry& MapOutWireFormat_Map4Entry::default_instance() {
  protobuf_InitDefaults_google_2fprotobuf_2futil_2finternal_2ftestdata_2fmaps_2eproto();
  return *internal_default_instance();
}

::google::protobuf::internal::ExplicitlyConstructed<MapOutWireFormat_Map4Entry> MapOutWireFormat_Map4Entry_default_instance_;

MapOutWireFormat_Map4Entry* MapOutWireFormat_Map4Entry::New(::google::protobuf::Arena* arena) const {
  MapOutWireFormat_Map4Entry* n = new MapOutWireFormat_Map4Entry;
  if (arena != NULL) {
    arena->Own(n);
  }
  return n;
}

void MapOutWireFormat_Map4Entry::Clear() {
// @@protoc_insertion_point(message_clear_start:google.protobuf.testing.MapOutWireFormat.Map4Entry)
  key_ = false;
  value_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}

bool MapOutWireFormat_Map4Entry::MergePartialFromCodedStream(
    ::google::protobuf::io::CodedInputStream* input) {
#define DO_(EXPRESSION) if (!GOOGLE_PREDICT_TRUE(EXPRESSION)) goto failure
  ::google::protobuf::uint32 tag;
  // @@protoc_insertion_point(parse_start:google.protobuf.testing.MapOutWireFormat.Map4Entry)
  for (;;) {
    ::std::pair< ::google::protobuf::uint32, bool> p = input->ReadTagWithCutoff(127);
    tag = p.first;
    if (!p.second) goto handle_unusual;
    switch (::google::protobuf::internal::WireFormatLite::GetTagFieldNumber(tag)) {
      // optional bool key = 1;
      case 1: {
        if (tag == 8) {

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   bool, ::google::protobuf::internal::WireFormatLite::TYPE_BOOL>(
                 input, &key_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(18)) goto parse_value;
        break;
      }

      // optional string value = 2;
      case 2: {
        if (tag == 18) {
         parse_value:
          DO_(::google::protobuf::internal::WireFormatLite::ReadString(
                input, this->mutable_value()));
          DO_(::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
            this->value().data(), this->value().length(),
            ::google::protobuf::internal::WireFormatLite::PARSE,
            "google.protobuf.testing.MapOutWireFormat.Map4Entry.value"));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectAtEnd()) goto success;
        break;
      }

      default: {
      handle_unusual:
        if (tag == 0 ||
            ::google::protobuf::internal::WireFormatLite::GetTagWireType(tag) ==
            ::google::protobuf::internal::WireFormatLite::WIRETYPE_END_GROUP) {
          goto success;
        }
        DO_(::google::protobuf::internal::WireFormatLite::SkipField(input, tag));
        break;
      }
    }
  }
success:
  // @@protoc_insertion_point(parse_success:google.protobuf.testing.MapOutWireFormat.Map4Entry)
  return true;
failure:
  // @@protoc_insertion_point(parse_failure:google.protobuf.testing.MapOutWireFormat.Map4Entry)
  return false;
#undef DO_
}

void MapOutWireFormat_Map4Entry::SerializeWithCachedSizes(
    ::google::protobuf::io::CodedOutputStream* output) const {
  // @@protoc_insertion_point(serialize_start:google.protobuf.testing.MapOutWireFormat.Map4Entry)
  // optional bool key = 1;
  if (this->key() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteBool(1, this->key(), output);
  }

  // optional string value = 2;
  if (this->value().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->value().data(), this->value().length(),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "google.protobuf.testing.MapOutWireFormat.Map4Entry.value");
    ::google::protobuf::internal::WireFormatLite::WriteStringMaybeAliased(
      2, this->value(), output);
  }

  // @@protoc_insertion_point(serialize_end:google.protobuf.testing.MapOutWireFormat.Map4Entry)
}

::google::protobuf::uint8* MapOutWireFormat_Map4Entry::InternalSerializeWithCachedSizesToArray(
    bool deterministic, ::google::protobuf::uint8* target) const {
  (void)deterministic; // Unused
  // @@protoc_insertion_point(serialize_to_array_start:google.protobuf.testing.MapOutWireFormat.Map4Entry)
  // optional bool key = 1;
  if (this->key() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteBoolToArray(1, this->key(), target);
  }

  // optional string value = 2;
  if (this->value().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->value().data(), this->value().length(),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "google.protobuf.testing.MapOutWireFormat.Map4Entry.value");
    target =
      ::google::protobuf::internal::WireFormatLite::WriteStringToArray(
        2, this->value(), target);
  }

  // @@protoc_insertion_point(serialize_to_array_end:google.protobuf.testing.MapOutWireFormat.Map4Entry)
  return target;
}

size_t MapOutWireFormat_Map4Entry::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:google.protobuf.testing.MapOutWireFormat.Map4Entry)
  size_t total_size = 0;

  // optional bool key = 1;
  if (this->key() != 0) {
    total_size += 1 + 1;
  }

  // optional string value = 2;
  if (this->value().size() > 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::StringSize(
        this->value());
  }

  int cached_size = ::google::protobuf::internal::ToCachedSize(total_size);
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = cached_size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
  return total_size;
}

void MapOutWireFormat_Map4Entry::MergeFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_merge_from_start:google.protobuf.testing.MapOutWireFormat.Map4Entry)
  if (GOOGLE_PREDICT_FALSE(&from == this)) MergeFromFail(__LINE__);
  const MapOutWireFormat_Map4Entry* source =
      ::google::protobuf::internal::DynamicCastToGenerated<const MapOutWireFormat_Map4Entry>(
          &from);
  if (source == NULL) {
  // @@protoc_insertion_point(generalized_merge_from_cast_fail:google.protobuf.testing.MapOutWireFormat.Map4Entry)
    ::google::protobuf::internal::ReflectionOps::Merge(from, this);
  } else {
  // @@protoc_insertion_point(generalized_merge_from_cast_success:google.protobuf.testing.MapOutWireFormat.Map4Entry)
    UnsafeMergeFrom(*source);
  }
}

void MapOutWireFormat_Map4Entry::MergeFrom(const MapOutWireFormat_Map4Entry& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:google.protobuf.testing.MapOutWireFormat.Map4Entry)
  if (GOOGLE_PREDICT_TRUE(&from != this)) {
    UnsafeMergeFrom(from);
  } else {
    MergeFromFail(__LINE__);
  }
}

void MapOutWireFormat_Map4Entry::UnsafeMergeFrom(const MapOutWireFormat_Map4Entry& from) {
  GOOGLE_DCHECK(&from != this);
  if (from.key() != 0) {
    set_key(from.key());
  }
  if (from.value().size() > 0) {

    value_.AssignWithDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), from.value_);
  }
}

void MapOutWireFormat_Map4Entry::CopyFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_copy_from_start:google.protobuf.testing.MapOutWireFormat.Map4Entry)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

void MapOutWireFormat_Map4Entry::CopyFrom(const MapOutWireFormat_Map4Entry& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:google.protobuf.testing.MapOutWireFormat.Map4Entry)
  if (&from == this) return;
  Clear();
  UnsafeMergeFrom(from);
}

bool MapOutWireFormat_Map4Entry::IsInitialized() const {

  return true;
}

void MapOutWireFormat_Map4Entry::Swap(MapOutWireFormat_Map4Entry* other) {
  if (other == this) return;
  InternalSwap(other);
}
void MapOutWireFormat_Map4Entry::InternalSwap(MapOutWireFormat_Map4Entry* other) {
  std::swap(key_, other->key_);
  value_.Swap(&other->value_);
  _internal_metadata_.Swap(&other->_internal_metadata_);
  std::swap(_cached_size_, other->_cached_size_);
}

::google::protobuf::Metadata MapOutWireFormat_Map4Entry::GetMetadata() const {
  protobuf_AssignDescriptorsOnce();
  ::google::protobuf::Metadata metadata;
  metadata.descriptor = MapOutWireFormat_Map4Entry_descriptor_;
  metadata.reflection = MapOutWireFormat_Map4Entry_reflection_;
  return metadata;
}


// -------------------------------------------------------------------

#if !defined(_MSC_VER) || _MSC_VER >= 1900
const int MapOutWireFormat::kMap1FieldNumber;
const int MapOutWireFormat::kMap2FieldNumber;
const int MapOutWireFormat::kMap3FieldNumber;
const int MapOutWireFormat::kMap4FieldNumber;
const int MapOutWireFormat::kBarFieldNumber;
#endif  // !defined(_MSC_VER) || _MSC_VER >= 1900

MapOutWireFormat::MapOutWireFormat()
  : ::google::protobuf::Message(), _internal_metadata_(NULL) {
  if (this != internal_default_instance()) protobuf_InitDefaults_google_2fprotobuf_2futil_2finternal_2ftestdata_2fmaps_2eproto();
  SharedCtor();
  // @@protoc_insertion_point(constructor:google.protobuf.testing.MapOutWireFormat)
}

void MapOutWireFormat::InitAsDefaultInstance() {
}

MapOutWireFormat::MapOutWireFormat(const MapOutWireFormat& from)
  : ::google::protobuf::Message(),
    _internal_metadata_(NULL) {
  SharedCtor();
  UnsafeMergeFrom(from);
  // @@protoc_insertion_point(copy_constructor:google.protobuf.testing.MapOutWireFormat)
}

void MapOutWireFormat::SharedCtor() {
  bar_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  _cached_size_ = 0;
}

MapOutWireFormat::~MapOutWireFormat() {
  // @@protoc_insertion_point(destructor:google.protobuf.testing.MapOutWireFormat)
  SharedDtor();
}

void MapOutWireFormat::SharedDtor() {
  bar_.DestroyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}

void MapOutWireFormat::SetCachedSize(int size) const {
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
}
const ::google::protobuf::Descriptor* MapOutWireFormat::descriptor() {
  protobuf_AssignDescriptorsOnce();
  return MapOutWireFormat_descriptor_;
}

const MapOutWireFormat& MapOutWireFormat::default_instance() {
  protobuf_InitDefaults_google_2fprotobuf_2futil_2finternal_2ftestdata_2fmaps_2eproto();
  return *internal_default_instance();
}

::google::protobuf::internal::ExplicitlyConstructed<MapOutWireFormat> MapOutWireFormat_default_instance_;

MapOutWireFormat* MapOutWireFormat::New(::google::protobuf::Arena* arena) const {
  MapOutWireFormat* n = new MapOutWireFormat;
  if (arena != NULL) {
    arena->Own(n);
  }
  return n;
}

void MapOutWireFormat::Clear() {
// @@protoc_insertion_point(message_clear_start:google.protobuf.testing.MapOutWireFormat)
  bar_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  map1_.Clear();
  map2_.Clear();
  map3_.Clear();
  map4_.Clear();
}

bool MapOutWireFormat::MergePartialFromCodedStream(
    ::google::protobuf::io::CodedInputStream* input) {
#define DO_(EXPRESSION) if (!GOOGLE_PREDICT_TRUE(EXPRESSION)) goto failure
  ::google::protobuf::uint32 tag;
  // @@protoc_insertion_point(parse_start:google.protobuf.testing.MapOutWireFormat)
  for (;;) {
    ::std::pair< ::google::protobuf::uint32, bool> p = input->ReadTagWithCutoff(127);
    tag = p.first;
    if (!p.second) goto handle_unusual;
    switch (::google::protobuf::internal::WireFormatLite::GetTagFieldNumber(tag)) {
      // repeated .google.protobuf.testing.MapOutWireFormat.Map1Entry map1 = 1;
      case 1: {
        if (tag == 10) {
          DO_(input->IncrementRecursionDepth());
         parse_loop_map1:
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessageNoVirtualNoRecursionDepth(
                input, add_map1()));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(10)) goto parse_loop_map1;
        if (input->ExpectTag(18)) goto parse_loop_map2;
        input->UnsafeDecrementRecursionDepth();
        break;
      }

      // repeated .google.protobuf.testing.MapOutWireFormat.Map2Entry map2 = 2;
      case 2: {
        if (tag == 18) {
          DO_(input->IncrementRecursionDepth());
         parse_loop_map2:
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessageNoVirtualNoRecursionDepth(
                input, add_map2()));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(18)) goto parse_loop_map2;
        if (input->ExpectTag(26)) goto parse_loop_map3;
        input->UnsafeDecrementRecursionDepth();
        break;
      }

      // repeated .google.protobuf.testing.MapOutWireFormat.Map3Entry map3 = 3;
      case 3: {
        if (tag == 26) {
          DO_(input->IncrementRecursionDepth());
         parse_loop_map3:
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessageNoVirtualNoRecursionDepth(
                input, add_map3()));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(26)) goto parse_loop_map3;
        input->UnsafeDecrementRecursionDepth();
        if (input->ExpectTag(34)) goto parse_bar;
        break;
      }

      // optional string bar = 4;
      case 4: {
        if (tag == 34) {
         parse_bar:
          DO_(::google::protobuf::internal::WireFormatLite::ReadString(
                input, this->mutable_bar()));
          DO_(::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
            this->bar().data(), this->bar().length(),
            ::google::protobuf::internal::WireFormatLite::PARSE,
            "google.protobuf.testing.MapOutWireFormat.bar"));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(42)) goto parse_map4;
        break;
      }

      // repeated .google.protobuf.testing.MapOutWireFormat.Map4Entry map4 = 5;
      case 5: {
        if (tag == 42) {
         parse_map4:
          DO_(input->IncrementRecursionDepth());
         parse_loop_map4:
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessageNoVirtualNoRecursionDepth(
                input, add_map4()));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(42)) goto parse_loop_map4;
        input->UnsafeDecrementRecursionDepth();
        if (input->ExpectAtEnd()) goto success;
        break;
      }

      default: {
      handle_unusual:
        if (tag == 0 ||
            ::google::protobuf::internal::WireFormatLite::GetTagWireType(tag) ==
            ::google::protobuf::internal::WireFormatLite::WIRETYPE_END_GROUP) {
          goto success;
        }
        DO_(::google::protobuf::internal::WireFormatLite::SkipField(input, tag));
        break;
      }
    }
  }
success:
  // @@protoc_insertion_point(parse_success:google.protobuf.testing.MapOutWireFormat)
  return true;
failure:
  // @@protoc_insertion_point(parse_failure:google.protobuf.testing.MapOutWireFormat)
  return false;
#undef DO_
}

void MapOutWireFormat::SerializeWithCachedSizes(
    ::google::protobuf::io::CodedOutputStream* output) const {
  // @@protoc_insertion_point(serialize_start:google.protobuf.testing.MapOutWireFormat)
  // repeated .google.protobuf.testing.MapOutWireFormat.Map1Entry map1 = 1;
  for (unsigned int i = 0, n = this->map1_size(); i < n; i++) {
    ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
      1, this->map1(i), output);
  }

  // repeated .google.protobuf.testing.MapOutWireFormat.Map2Entry map2 = 2;
  for (unsigned int i = 0, n = this->map2_size(); i < n; i++) {
    ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
      2, this->map2(i), output);
  }

  // repeated .google.protobuf.testing.MapOutWireFormat.Map3Entry map3 = 3;
  for (unsigned int i = 0, n = this->map3_size(); i < n; i++) {
    ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
      3, this->map3(i), output);
  }

  // optional string bar = 4;
  if (this->bar().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->bar().data(), this->bar().length(),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "google.protobuf.testing.MapOutWireFormat.bar");
    ::google::protobuf::internal::WireFormatLite::WriteStringMaybeAliased(
      4, this->bar(), output);
  }

  // repeated .google.protobuf.testing.MapOutWireFormat.Map4Entry map4 = 5;
  for (unsigned int i = 0, n = this->map4_size(); i < n; i++) {
    ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
      5, this->map4(i), output);
  }

  // @@protoc_insertion_point(serialize_end:google.protobuf.testing.MapOutWireFormat)
}

::google::protobuf::uint8* MapOutWireFormat::InternalSerializeWithCachedSizesToArray(
    bool deterministic, ::google::protobuf::uint8* target) const {
  (void)deterministic; // Unused
  // @@protoc_insertion_point(serialize_to_array_start:google.protobuf.testing.MapOutWireFormat)
  // repeated .google.protobuf.testing.MapOutWireFormat.Map1Entry map1 = 1;
  for (unsigned int i = 0, n = this->map1_size(); i < n; i++) {
    target = ::google::protobuf::internal::WireFormatLite::
      InternalWriteMessageNoVirtualToArray(
        1, this->map1(i), false, target);
  }

  // repeated .google.protobuf.testing.MapOutWireFormat.Map2Entry map2 = 2;
  for (unsigned int i = 0, n = this->map2_size(); i < n; i++) {
    target = ::google::protobuf::internal::WireFormatLite::
      InternalWriteMessageNoVirtualToArray(
        2, this->map2(i), false, target);
  }

  // repeated .google.protobuf.testing.MapOutWireFormat.Map3Entry map3 = 3;
  for (unsigned int i = 0, n = this->map3_size(); i < n; i++) {
    target = ::google::protobuf::internal::WireFormatLite::
      InternalWriteMessageNoVirtualToArray(
        3, this->map3(i), false, target);
  }

  // optional string bar = 4;
  if (this->bar().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->bar().data(), this->bar().length(),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "google.protobuf.testing.MapOutWireFormat.bar");
    target =
      ::google::protobuf::internal::WireFormatLite::WriteStringToArray(
        4, this->bar(), target);
  }

  // repeated .google.protobuf.testing.MapOutWireFormat.Map4Entry map4 = 5;
  for (unsigned int i = 0, n = this->map4_size(); i < n; i++) {
    target = ::google::protobuf::internal::WireFormatLite::
      InternalWriteMessageNoVirtualToArray(
        5, this->map4(i), false, target);
  }

  // @@protoc_insertion_point(serialize_to_array_end:google.protobuf.testing.MapOutWireFormat)
  return target;
}

size_t MapOutWireFormat::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:google.protobuf.testing.MapOutWireFormat)
  size_t total_size = 0;

  // optional string bar = 4;
  if (this->bar().size() > 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::StringSize(
        this->bar());
  }

  // repeated .google.protobuf.testing.MapOutWireFormat.Map1Entry map1 = 1;
  {
    unsigned int count = this->map1_size();
    total_size += 1UL * count;
    for (unsigned int i = 0; i < count; i++) {
      total_size +=
        ::google::protobuf::internal::WireFormatLite::MessageSizeNoVirtual(
          this->map1(i));
    }
  }

  // repeated .google.protobuf.testing.MapOutWireFormat.Map2Entry map2 = 2;
  {
    unsigned int count = this->map2_size();
    total_size += 1UL * count;
    for (unsigned int i = 0; i < count; i++) {
      total_size +=
        ::google::protobuf::internal::WireFormatLite::MessageSizeNoVirtual(
          this->map2(i));
    }
  }

  // repeated .google.protobuf.testing.MapOutWireFormat.Map3Entry map3 = 3;
  {
    unsigned int count = this->map3_size();
    total_size += 1UL * count;
    for (unsigned int i = 0; i < count; i++) {
      total_size +=
        ::google::protobuf::internal::WireFormatLite::MessageSizeNoVirtual(
          this->map3(i));
    }
  }

  // repeated .google.protobuf.testing.MapOutWireFormat.Map4Entry map4 = 5;
  {
    unsigned int count = this->map4_size();
    total_size += 1UL * count;
    for (unsigned int i = 0; i < count; i++) {
      total_size +=
        ::google::protobuf::internal::WireFormatLite::MessageSizeNoVirtual(
          this->map4(i));
    }
  }

  int cached_size = ::google::protobuf::internal::ToCachedSize(total_size);
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = cached_size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
  return total_size;
}

void MapOutWireFormat::MergeFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_merge_from_start:google.protobuf.testing.MapOutWireFormat)
  if (GOOGLE_PREDICT_FALSE(&from == this)) MergeFromFail(__LINE__);
  const MapOutWireFormat* source =
      ::google::protobuf::internal::DynamicCastToGenerated<const MapOutWireFormat>(
          &from);
  if (source == NULL) {
  // @@protoc_insertion_point(generalized_merge_from_cast_fail:google.protobuf.testing.MapOutWireFormat)
    ::google::protobuf::internal::ReflectionOps::Merge(from, this);
  } else {
  // @@protoc_insertion_point(generalized_merge_from_cast_success:google.protobuf.testing.MapOutWireFormat)
    UnsafeMergeFrom(*source);
  }
}

void MapOutWireFormat::MergeFrom(const MapOutWireFormat& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:google.protobuf.testing.MapOutWireFormat)
  if (GOOGLE_PREDICT_TRUE(&from != this)) {
    UnsafeMergeFrom(from);
  } else {
    MergeFromFail(__LINE__);
  }
}

void MapOutWireFormat::UnsafeMergeFrom(const MapOutWireFormat& from) {
  GOOGLE_DCHECK(&from != this);
  map1_.MergeFrom(from.map1_);
  map2_.MergeFrom(from.map2_);
  map3_.MergeFrom(from.map3_);
  map4_.MergeFrom(from.map4_);
  if (from.bar().size() > 0) {

    bar_.AssignWithDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), from.bar_);
  }
}

void MapOutWireFormat::CopyFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_copy_from_start:google.protobuf.testing.MapOutWireFormat)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

void MapOutWireFormat::CopyFrom(const MapOutWireFormat& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:google.protobuf.testing.MapOutWireFormat)
  if (&from == this) return;
  Clear();
  UnsafeMergeFrom(from);
}

bool MapOutWireFormat::IsInitialized() const {

  return true;
}

void MapOutWireFormat::Swap(MapOutWireFormat* other) {
  if (other == this) return;
  InternalSwap(other);
}
void MapOutWireFormat::InternalSwap(MapOutWireFormat* other) {
  map1_.UnsafeArenaSwap(&other->map1_);
  map2_.UnsafeArenaSwap(&other->map2_);
  map3_.UnsafeArenaSwap(&other->map3_);
  map4_.UnsafeArenaSwap(&other->map4_);
  bar_.Swap(&other->bar_);
  _internal_metadata_.Swap(&other->_internal_metadata_);
  std::swap(_cached_size_, other->_cached_size_);
}

::google::protobuf::Metadata MapOutWireFormat::GetMetadata() const {
  protobuf_AssignDescriptorsOnce();
  ::google::protobuf::Metadata metadata;
  metadata.descriptor = MapOutWireFormat_descriptor_;
  metadata.reflection = MapOutWireFormat_reflection_;
  return metadata;
}

#if PROTOBUF_INLINE_NOT_IN_HEADERS
// MapOutWireFormat_Map1Entry

// optional string key = 1;
void MapOutWireFormat_Map1Entry::clear_key() {
  key_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
const ::std::string& MapOutWireFormat_Map1Entry::key() const {
  // @@protoc_insertion_point(field_get:google.protobuf.testing.MapOutWireFormat.Map1Entry.key)
  return key_.GetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
void MapOutWireFormat_Map1Entry::set_key(const ::std::string& value) {
  
  key_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:google.protobuf.testing.MapOutWireFormat.Map1Entry.key)
}
void MapOutWireFormat_Map1Entry::set_key(const char* value) {
  
  key_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:google.protobuf.testing.MapOutWireFormat.Map1Entry.key)
}
void MapOutWireFormat_Map1Entry::set_key(const char* value, size_t size) {
  
  key_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:google.protobuf.testing.MapOutWireFormat.Map1Entry.key)
}
::std::string* MapOutWireFormat_Map1Entry::mutable_key() {
  
  // @@protoc_insertion_point(field_mutable:google.protobuf.testing.MapOutWireFormat.Map1Entry.key)
  return key_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
::std::string* MapOutWireFormat_Map1Entry::release_key() {
  // @@protoc_insertion_point(field_release:google.protobuf.testing.MapOutWireFormat.Map1Entry.key)
  
  return key_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
void MapOutWireFormat_Map1Entry::set_allocated_key(::std::string* key) {
  if (key != NULL) {
    
  } else {
    
  }
  key_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), key);
  // @@protoc_insertion_point(field_set_allocated:google.protobuf.testing.MapOutWireFormat.Map1Entry.key)
}

// optional .google.protobuf.testing.MapM value = 2;
bool MapOutWireFormat_Map1Entry::has_value() const {
  return this != internal_default_instance() && value_ != NULL;
}
void MapOutWireFormat_Map1Entry::clear_value() {
  if (GetArenaNoVirtual() == NULL && value_ != NULL) delete value_;
  value_ = NULL;
}
const ::google::protobuf::testing::MapM& MapOutWireFormat_Map1Entry::value() const {
  // @@protoc_insertion_point(field_get:google.protobuf.testing.MapOutWireFormat.Map1Entry.value)
  return value_ != NULL ? *value_
                         : *::google::protobuf::testing::MapM::internal_default_instance();
}
::google::protobuf::testing::MapM* MapOutWireFormat_Map1Entry::mutable_value() {
  
  if (value_ == NULL) {
    value_ = new ::google::protobuf::testing::MapM;
  }
  // @@protoc_insertion_point(field_mutable:google.protobuf.testing.MapOutWireFormat.Map1Entry.value)
  return value_;
}
::google::protobuf::testing::MapM* MapOutWireFormat_Map1Entry::release_value() {
  // @@protoc_insertion_point(field_release:google.protobuf.testing.MapOutWireFormat.Map1Entry.value)
  
  ::google::protobuf::testing::MapM* temp = value_;
  value_ = NULL;
  return temp;
}
void MapOutWireFormat_Map1Entry::set_allocated_value(::google::protobuf::testing::MapM* value) {
  delete value_;
  value_ = value;
  if (value) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_set_allocated:google.protobuf.testing.MapOutWireFormat.Map1Entry.value)
}

inline const MapOutWireFormat_Map1Entry* MapOutWireFormat_Map1Entry::internal_default_instance() {
  return &MapOutWireFormat_Map1Entry_default_instance_.get();
}
// -------------------------------------------------------------------

// MapOutWireFormat_Map2Entry

// optional string key = 1;
void MapOutWireFormat_Map2Entry::clear_key() {
  key_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
const ::std::string& MapOutWireFormat_Map2Entry::key() const {
  // @@protoc_insertion_point(field_get:google.protobuf.testing.MapOutWireFormat.Map2Entry.key)
  return key_.GetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
void MapOutWireFormat_Map2Entry::set_key(const ::std::string& value) {
  
  key_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:google.protobuf.testing.MapOutWireFormat.Map2Entry.key)
}
void MapOutWireFormat_Map2Entry::set_key(const char* value) {
  
  key_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:google.protobuf.testing.MapOutWireFormat.Map2Entry.key)
}
void MapOutWireFormat_Map2Entry::set_key(const char* value, size_t size) {
  
  key_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:google.protobuf.testing.MapOutWireFormat.Map2Entry.key)
}
::std::string* MapOutWireFormat_Map2Entry::mutable_key() {
  
  // @@protoc_insertion_point(field_mutable:google.protobuf.testing.MapOutWireFormat.Map2Entry.key)
  return key_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
::std::string* MapOutWireFormat_Map2Entry::release_key() {
  // @@protoc_insertion_point(field_release:google.protobuf.testing.MapOutWireFormat.Map2Entry.key)
  
  return key_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
void MapOutWireFormat_Map2Entry::set_allocated_key(::std::string* key) {
  if (key != NULL) {
    
  } else {
    
  }
  key_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), key);
  // @@protoc_insertion_point(field_set_allocated:google.protobuf.testing.MapOutWireFormat.Map2Entry.key)
}

// optional .google.protobuf.testing.MapOut value = 2;
bool MapOutWireFormat_Map2Entry::has_value() const {
  return this != internal_default_instance() && value_ != NULL;
}
void MapOutWireFormat_Map2Entry::clear_value() {
  if (GetArenaNoVirtual() == NULL && value_ != NULL) delete value_;
  value_ = NULL;
}
const ::google::protobuf::testing::MapOut& MapOutWireFormat_Map2Entry::value() const {
  // @@protoc_insertion_point(field_get:google.protobuf.testing.MapOutWireFormat.Map2Entry.value)
  return value_ != NULL ? *value_
                         : *::google::protobuf::testing::MapOut::internal_default_instance();
}
::google::protobuf::testing::MapOut* MapOutWireFormat_Map2Entry::mutable_value() {
  
  if (value_ == NULL) {
    value_ = new ::google::protobuf::testing::MapOut;
  }
  // @@protoc_insertion_point(field_mutable:google.protobuf.testing.MapOutWireFormat.Map2Entry.value)
  return value_;
}
::google::protobuf::testing::MapOut* MapOutWireFormat_Map2Entry::release_value() {
  // @@protoc_insertion_point(field_release:google.protobuf.testing.MapOutWireFormat.Map2Entry.value)
  
  ::google::protobuf::testing::MapOut* temp = value_;
  value_ = NULL;
  return temp;
}
void MapOutWireFormat_Map2Entry::set_allocated_value(::google::protobuf::testing::MapOut* value) {
  delete value_;
  value_ = value;
  if (value) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_set_allocated:google.protobuf.testing.MapOutWireFormat.Map2Entry.value)
}

inline const MapOutWireFormat_Map2Entry* MapOutWireFormat_Map2Entry::internal_default_instance() {
  return &MapOutWireFormat_Map2Entry_default_instance_.get();
}
// -------------------------------------------------------------------

// MapOutWireFormat_Map3Entry

// optional int32 key = 1;
void MapOutWireFormat_Map3Entry::clear_key() {
  key_ = 0;
}
::google::protobuf::int32 MapOutWireFormat_Map3Entry::key() const {
  // @@protoc_insertion_point(field_get:google.protobuf.testing.MapOutWireFormat.Map3Entry.key)
  return key_;
}
void MapOutWireFormat_Map3Entry::set_key(::google::protobuf::int32 value) {
  
  key_ = value;
  // @@protoc_insertion_point(field_set:google.protobuf.testing.MapOutWireFormat.Map3Entry.key)
}

// optional string value = 2;
void MapOutWireFormat_Map3Entry::clear_value() {
  value_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
const ::std::string& MapOutWireFormat_Map3Entry::value() const {
  // @@protoc_insertion_point(field_get:google.protobuf.testing.MapOutWireFormat.Map3Entry.value)
  return value_.GetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
void MapOutWireFormat_Map3Entry::set_value(const ::std::string& value) {
  
  value_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:google.protobuf.testing.MapOutWireFormat.Map3Entry.value)
}
void MapOutWireFormat_Map3Entry::set_value(const char* value) {
  
  value_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:google.protobuf.testing.MapOutWireFormat.Map3Entry.value)
}
void MapOutWireFormat_Map3Entry::set_value(const char* value, size_t size) {
  
  value_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:google.protobuf.testing.MapOutWireFormat.Map3Entry.value)
}
::std::string* MapOutWireFormat_Map3Entry::mutable_value() {
  
  // @@protoc_insertion_point(field_mutable:google.protobuf.testing.MapOutWireFormat.Map3Entry.value)
  return value_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
::std::string* MapOutWireFormat_Map3Entry::release_value() {
  // @@protoc_insertion_point(field_release:google.protobuf.testing.MapOutWireFormat.Map3Entry.value)
  
  return value_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
void MapOutWireFormat_Map3Entry::set_allocated_value(::std::string* value) {
  if (value != NULL) {
    
  } else {
    
  }
  value_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set_allocated:google.protobuf.testing.MapOutWireFormat.Map3Entry.value)
}

inline const MapOutWireFormat_Map3Entry* MapOutWireFormat_Map3Entry::internal_default_instance() {
  return &MapOutWireFormat_Map3Entry_default_instance_.get();
}
// -------------------------------------------------------------------

// MapOutWireFormat_Map4Entry

// optional bool key = 1;
void MapOutWireFormat_Map4Entry::clear_key() {
  key_ = false;
}
bool MapOutWireFormat_Map4Entry::key() const {
  // @@protoc_insertion_point(field_get:google.protobuf.testing.MapOutWireFormat.Map4Entry.key)
  return key_;
}
void MapOutWireFormat_Map4Entry::set_key(bool value) {
  
  key_ = value;
  // @@protoc_insertion_point(field_set:google.protobuf.testing.MapOutWireFormat.Map4Entry.key)
}

// optional string value = 2;
void MapOutWireFormat_Map4Entry::clear_value() {
  value_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
const ::std::string& MapOutWireFormat_Map4Entry::value() const {
  // @@protoc_insertion_point(field_get:google.protobuf.testing.MapOutWireFormat.Map4Entry.value)
  return value_.GetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
void MapOutWireFormat_Map4Entry::set_value(const ::std::string& value) {
  
  value_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:google.protobuf.testing.MapOutWireFormat.Map4Entry.value)
}
void MapOutWireFormat_Map4Entry::set_value(const char* value) {
  
  value_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:google.protobuf.testing.MapOutWireFormat.Map4Entry.value)
}
void MapOutWireFormat_Map4Entry::set_value(const char* value, size_t size) {
  
  value_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:google.protobuf.testing.MapOutWireFormat.Map4Entry.value)
}
::std::string* MapOutWireFormat_Map4Entry::mutable_value() {
  
  // @@protoc_insertion_point(field_mutable:google.protobuf.testing.MapOutWireFormat.Map4Entry.value)
  return value_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
::std::string* MapOutWireFormat_Map4Entry::release_value() {
  // @@protoc_insertion_point(field_release:google.protobuf.testing.MapOutWireFormat.Map4Entry.value)
  
  return value_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
void MapOutWireFormat_Map4Entry::set_allocated_value(::std::string* value) {
  if (value != NULL) {
    
  } else {
    
  }
  value_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set_allocated:google.protobuf.testing.MapOutWireFormat.Map4Entry.value)
}

inline const MapOutWireFormat_Map4Entry* MapOutWireFormat_Map4Entry::internal_default_instance() {
  return &MapOutWireFormat_Map4Entry_default_instance_.get();
}
// -------------------------------------------------------------------

// MapOutWireFormat

// repeated .google.protobuf.testing.MapOutWireFormat.Map1Entry map1 = 1;
int MapOutWireFormat::map1_size() const {
  return map1_.size();
}
void MapOutWireFormat::clear_map1() {
  map1_.Clear();
}
const ::google::protobuf::testing::MapOutWireFormat_Map1Entry& MapOutWireFormat::map1(int index) const {
  // @@protoc_insertion_point(field_get:google.protobuf.testing.MapOutWireFormat.map1)
  return map1_.Get(index);
}
::google::protobuf::testing::MapOutWireFormat_Map1Entry* MapOutWireFormat::mutable_map1(int index) {
  // @@protoc_insertion_point(field_mutable:google.protobuf.testing.MapOutWireFormat.map1)
  return map1_.Mutable(index);
}
::google::protobuf::testing::MapOutWireFormat_Map1Entry* MapOutWireFormat::add_map1() {
  // @@protoc_insertion_point(field_add:google.protobuf.testing.MapOutWireFormat.map1)
  return map1_.Add();
}
::google::protobuf::RepeatedPtrField< ::google::protobuf::testing::MapOutWireFormat_Map1Entry >*
MapOutWireFormat::mutable_map1() {
  // @@protoc_insertion_point(field_mutable_list:google.protobuf.testing.MapOutWireFormat.map1)
  return &map1_;
}
const ::google::protobuf::RepeatedPtrField< ::google::protobuf::testing::MapOutWireFormat_Map1Entry >&
MapOutWireFormat::map1() const {
  // @@protoc_insertion_point(field_list:google.protobuf.testing.MapOutWireFormat.map1)
  return map1_;
}

// repeated .google.protobuf.testing.MapOutWireFormat.Map2Entry map2 = 2;
int MapOutWireFormat::map2_size() const {
  return map2_.size();
}
void MapOutWireFormat::clear_map2() {
  map2_.Clear();
}
const ::google::protobuf::testing::MapOutWireFormat_Map2Entry& MapOutWireFormat::map2(int index) const {
  // @@protoc_insertion_point(field_get:google.protobuf.testing.MapOutWireFormat.map2)
  return map2_.Get(index);
}
::google::protobuf::testing::MapOutWireFormat_Map2Entry* MapOutWireFormat::mutable_map2(int index) {
  // @@protoc_insertion_point(field_mutable:google.protobuf.testing.MapOutWireFormat.map2)
  return map2_.Mutable(index);
}
::google::protobuf::testing::MapOutWireFormat_Map2Entry* MapOutWireFormat::add_map2() {
  // @@protoc_insertion_point(field_add:google.protobuf.testing.MapOutWireFormat.map2)
  return map2_.Add();
}
::google::protobuf::RepeatedPtrField< ::google::protobuf::testing::MapOutWireFormat_Map2Entry >*
MapOutWireFormat::mutable_map2() {
  // @@protoc_insertion_point(field_mutable_list:google.protobuf.testing.MapOutWireFormat.map2)
  return &map2_;
}
const ::google::protobuf::RepeatedPtrField< ::google::protobuf::testing::MapOutWireFormat_Map2Entry >&
MapOutWireFormat::map2() const {
  // @@protoc_insertion_point(field_list:google.protobuf.testing.MapOutWireFormat.map2)
  return map2_;
}

// repeated .google.protobuf.testing.MapOutWireFormat.Map3Entry map3 = 3;
int MapOutWireFormat::map3_size() const {
  return map3_.size();
}
void MapOutWireFormat::clear_map3() {
  map3_.Clear();
}
const ::google::protobuf::testing::MapOutWireFormat_Map3Entry& MapOutWireFormat::map3(int index) const {
  // @@protoc_insertion_point(field_get:google.protobuf.testing.MapOutWireFormat.map3)
  return map3_.Get(index);
}
::google::protobuf::testing::MapOutWireFormat_Map3Entry* MapOutWireFormat::mutable_map3(int index) {
  // @@protoc_insertion_point(field_mutable:google.protobuf.testing.MapOutWireFormat.map3)
  return map3_.Mutable(index);
}
::google::protobuf::testing::MapOutWireFormat_Map3Entry* MapOutWireFormat::add_map3() {
  // @@protoc_insertion_point(field_add:google.protobuf.testing.MapOutWireFormat.map3)
  return map3_.Add();
}
::google::protobuf::RepeatedPtrField< ::google::protobuf::testing::MapOutWireFormat_Map3Entry >*
MapOutWireFormat::mutable_map3() {
  // @@protoc_insertion_point(field_mutable_list:google.protobuf.testing.MapOutWireFormat.map3)
  return &map3_;
}
const ::google::protobuf::RepeatedPtrField< ::google::protobuf::testing::MapOutWireFormat_Map3Entry >&
MapOutWireFormat::map3() const {
  // @@protoc_insertion_point(field_list:google.protobuf.testing.MapOutWireFormat.map3)
  return map3_;
}

// repeated .google.protobuf.testing.MapOutWireFormat.Map4Entry map4 = 5;
int MapOutWireFormat::map4_size() const {
  return map4_.size();
}
void MapOutWireFormat::clear_map4() {
  map4_.Clear();
}
const ::google::protobuf::testing::MapOutWireFormat_Map4Entry& MapOutWireFormat::map4(int index) const {
  // @@protoc_insertion_point(field_get:google.protobuf.testing.MapOutWireFormat.map4)
  return map4_.Get(index);
}
::google::protobuf::testing::MapOutWireFormat_Map4Entry* MapOutWireFormat::mutable_map4(int index) {
  // @@protoc_insertion_point(field_mutable:google.protobuf.testing.MapOutWireFormat.map4)
  return map4_.Mutable(index);
}
::google::protobuf::testing::MapOutWireFormat_Map4Entry* MapOutWireFormat::add_map4() {
  // @@protoc_insertion_point(field_add:google.protobuf.testing.MapOutWireFormat.map4)
  return map4_.Add();
}
::google::protobuf::RepeatedPtrField< ::google::protobuf::testing::MapOutWireFormat_Map4Entry >*
MapOutWireFormat::mutable_map4() {
  // @@protoc_insertion_point(field_mutable_list:google.protobuf.testing.MapOutWireFormat.map4)
  return &map4_;
}
const ::google::protobuf::RepeatedPtrField< ::google::protobuf::testing::MapOutWireFormat_Map4Entry >&
MapOutWireFormat::map4() const {
  // @@protoc_insertion_point(field_list:google.protobuf.testing.MapOutWireFormat.map4)
  return map4_;
}

// optional string bar = 4;
void MapOutWireFormat::clear_bar() {
  bar_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
const ::std::string& MapOutWireFormat::bar() const {
  // @@protoc_insertion_point(field_get:google.protobuf.testing.MapOutWireFormat.bar)
  return bar_.GetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
void MapOutWireFormat::set_bar(const ::std::string& value) {
  
  bar_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:google.protobuf.testing.MapOutWireFormat.bar)
}
void MapOutWireFormat::set_bar(const char* value) {
  
  bar_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:google.protobuf.testing.MapOutWireFormat.bar)
}
void MapOutWireFormat::set_bar(const char* value, size_t size) {
  
  bar_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:google.protobuf.testing.MapOutWireFormat.bar)
}
::std::string* MapOutWireFormat::mutable_bar() {
  
  // @@protoc_insertion_point(field_mutable:google.protobuf.testing.MapOutWireFormat.bar)
  return bar_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
::std::string* MapOutWireFormat::release_bar() {
  // @@protoc_insertion_point(field_release:google.protobuf.testing.MapOutWireFormat.bar)
  
  return bar_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
void MapOutWireFormat::set_allocated_bar(::std::string* bar) {
  if (bar != NULL) {
    
  } else {
    
  }
  bar_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), bar);
  // @@protoc_insertion_point(field_set_allocated:google.protobuf.testing.MapOutWireFormat.bar)
}

inline const MapOutWireFormat* MapOutWireFormat::internal_default_instance() {
  return &MapOutWireFormat_default_instance_.get();
}
#endif  // PROTOBUF_INLINE_NOT_IN_HEADERS

// ===================================================================

#if !defined(_MSC_VER) || _MSC_VER >= 1900
const int MapM::kFooFieldNumber;
#endif  // !defined(_MSC_VER) || _MSC_VER >= 1900

MapM::MapM()
  : ::google::protobuf::Message(), _internal_metadata_(NULL) {
  if (this != internal_default_instance()) protobuf_InitDefaults_google_2fprotobuf_2futil_2finternal_2ftestdata_2fmaps_2eproto();
  SharedCtor();
  // @@protoc_insertion_point(constructor:google.protobuf.testing.MapM)
}

void MapM::InitAsDefaultInstance() {
}

MapM::MapM(const MapM& from)
  : ::google::protobuf::Message(),
    _internal_metadata_(NULL) {
  SharedCtor();
  UnsafeMergeFrom(from);
  // @@protoc_insertion_point(copy_constructor:google.protobuf.testing.MapM)
}

void MapM::SharedCtor() {
  foo_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  _cached_size_ = 0;
}

MapM::~MapM() {
  // @@protoc_insertion_point(destructor:google.protobuf.testing.MapM)
  SharedDtor();
}

void MapM::SharedDtor() {
  foo_.DestroyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}

void MapM::SetCachedSize(int size) const {
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
}
const ::google::protobuf::Descriptor* MapM::descriptor() {
  protobuf_AssignDescriptorsOnce();
  return MapM_descriptor_;
}

const MapM& MapM::default_instance() {
  protobuf_InitDefaults_google_2fprotobuf_2futil_2finternal_2ftestdata_2fmaps_2eproto();
  return *internal_default_instance();
}

::google::protobuf::internal::ExplicitlyConstructed<MapM> MapM_default_instance_;

MapM* MapM::New(::google::protobuf::Arena* arena) const {
  MapM* n = new MapM;
  if (arena != NULL) {
    arena->Own(n);
  }
  return n;
}

void MapM::Clear() {
// @@protoc_insertion_point(message_clear_start:google.protobuf.testing.MapM)
  foo_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}

bool MapM::MergePartialFromCodedStream(
    ::google::protobuf::io::CodedInputStream* input) {
#define DO_(EXPRESSION) if (!GOOGLE_PREDICT_TRUE(EXPRESSION)) goto failure
  ::google::protobuf::uint32 tag;
  // @@protoc_insertion_point(parse_start:google.protobuf.testing.MapM)
  for (;;) {
    ::std::pair< ::google::protobuf::uint32, bool> p = input->ReadTagWithCutoff(127);
    tag = p.first;
    if (!p.second) goto handle_unusual;
    switch (::google::protobuf::internal::WireFormatLite::GetTagFieldNumber(tag)) {
      // optional string foo = 1;
      case 1: {
        if (tag == 10) {
          DO_(::google::protobuf::internal::WireFormatLite::ReadString(
                input, this->mutable_foo()));
          DO_(::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
            this->foo().data(), this->foo().length(),
            ::google::protobuf::internal::WireFormatLite::PARSE,
            "google.protobuf.testing.MapM.foo"));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectAtEnd()) goto success;
        break;
      }

      default: {
      handle_unusual:
        if (tag == 0 ||
            ::google::protobuf::internal::WireFormatLite::GetTagWireType(tag) ==
            ::google::protobuf::internal::WireFormatLite::WIRETYPE_END_GROUP) {
          goto success;
        }
        DO_(::google::protobuf::internal::WireFormatLite::SkipField(input, tag));
        break;
      }
    }
  }
success:
  // @@protoc_insertion_point(parse_success:google.protobuf.testing.MapM)
  return true;
failure:
  // @@protoc_insertion_point(parse_failure:google.protobuf.testing.MapM)
  return false;
#undef DO_
}

void MapM::SerializeWithCachedSizes(
    ::google::protobuf::io::CodedOutputStream* output) const {
  // @@protoc_insertion_point(serialize_start:google.protobuf.testing.MapM)
  // optional string foo = 1;
  if (this->foo().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->foo().data(), this->foo().length(),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "google.protobuf.testing.MapM.foo");
    ::google::protobuf::internal::WireFormatLite::WriteStringMaybeAliased(
      1, this->foo(), output);
  }

  // @@protoc_insertion_point(serialize_end:google.protobuf.testing.MapM)
}

::google::protobuf::uint8* MapM::InternalSerializeWithCachedSizesToArray(
    bool deterministic, ::google::protobuf::uint8* target) const {
  (void)deterministic; // Unused
  // @@protoc_insertion_point(serialize_to_array_start:google.protobuf.testing.MapM)
  // optional string foo = 1;
  if (this->foo().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->foo().data(), this->foo().length(),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "google.protobuf.testing.MapM.foo");
    target =
      ::google::protobuf::internal::WireFormatLite::WriteStringToArray(
        1, this->foo(), target);
  }

  // @@protoc_insertion_point(serialize_to_array_end:google.protobuf.testing.MapM)
  return target;
}

size_t MapM::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:google.protobuf.testing.MapM)
  size_t total_size = 0;

  // optional string foo = 1;
  if (this->foo().size() > 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::StringSize(
        this->foo());
  }

  int cached_size = ::google::protobuf::internal::ToCachedSize(total_size);
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = cached_size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
  return total_size;
}

void MapM::MergeFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_merge_from_start:google.protobuf.testing.MapM)
  if (GOOGLE_PREDICT_FALSE(&from == this)) MergeFromFail(__LINE__);
  const MapM* source =
      ::google::protobuf::internal::DynamicCastToGenerated<const MapM>(
          &from);
  if (source == NULL) {
  // @@protoc_insertion_point(generalized_merge_from_cast_fail:google.protobuf.testing.MapM)
    ::google::protobuf::internal::ReflectionOps::Merge(from, this);
  } else {
  // @@protoc_insertion_point(generalized_merge_from_cast_success:google.protobuf.testing.MapM)
    UnsafeMergeFrom(*source);
  }
}

void MapM::MergeFrom(const MapM& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:google.protobuf.testing.MapM)
  if (GOOGLE_PREDICT_TRUE(&from != this)) {
    UnsafeMergeFrom(from);
  } else {
    MergeFromFail(__LINE__);
  }
}

void MapM::UnsafeMergeFrom(const MapM& from) {
  GOOGLE_DCHECK(&from != this);
  if (from.foo().size() > 0) {

    foo_.AssignWithDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), from.foo_);
  }
}

void MapM::CopyFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_copy_from_start:google.protobuf.testing.MapM)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

void MapM::CopyFrom(const MapM& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:google.protobuf.testing.MapM)
  if (&from == this) return;
  Clear();
  UnsafeMergeFrom(from);
}

bool MapM::IsInitialized() const {

  return true;
}

void MapM::Swap(MapM* other) {
  if (other == this) return;
  InternalSwap(other);
}
void MapM::InternalSwap(MapM* other) {
  foo_.Swap(&other->foo_);
  _internal_metadata_.Swap(&other->_internal_metadata_);
  std::swap(_cached_size_, other->_cached_size_);
}

::google::protobuf::Metadata MapM::GetMetadata() const {
  protobuf_AssignDescriptorsOnce();
  ::google::protobuf::Metadata metadata;
  metadata.descriptor = MapM_descriptor_;
  metadata.reflection = MapM_reflection_;
  return metadata;
}

#if PROTOBUF_INLINE_NOT_IN_HEADERS
// MapM

// optional string foo = 1;
void MapM::clear_foo() {
  foo_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
const ::std::string& MapM::foo() const {
  // @@protoc_insertion_point(field_get:google.protobuf.testing.MapM.foo)
  return foo_.GetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
void MapM::set_foo(const ::std::string& value) {
  
  foo_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:google.protobuf.testing.MapM.foo)
}
void MapM::set_foo(const char* value) {
  
  foo_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:google.protobuf.testing.MapM.foo)
}
void MapM::set_foo(const char* value, size_t size) {
  
  foo_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:google.protobuf.testing.MapM.foo)
}
::std::string* MapM::mutable_foo() {
  
  // @@protoc_insertion_point(field_mutable:google.protobuf.testing.MapM.foo)
  return foo_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
::std::string* MapM::release_foo() {
  // @@protoc_insertion_point(field_release:google.protobuf.testing.MapM.foo)
  
  return foo_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
void MapM::set_allocated_foo(::std::string* foo) {
  if (foo != NULL) {
    
  } else {
    
  }
  foo_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), foo);
  // @@protoc_insertion_point(field_set_allocated:google.protobuf.testing.MapM.foo)
}

inline const MapM* MapM::internal_default_instance() {
  return &MapM_default_instance_.get();
}
#endif  // PROTOBUF_INLINE_NOT_IN_HEADERS

// @@protoc_insertion_point(namespace_scope)

}  // namespace testing
}  // namespace protobuf
}  // namespace google

// @@protoc_insertion_point(global_scope)

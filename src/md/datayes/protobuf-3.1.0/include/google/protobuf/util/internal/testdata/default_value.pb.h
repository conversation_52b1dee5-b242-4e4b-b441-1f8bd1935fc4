// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: google/protobuf/util/internal/testdata/default_value.proto

#ifndef PROTOBUF_google_2fprotobuf_2futil_2finternal_2ftestdata_2fdefault_5fvalue_2eproto__INCLUDED
#define PROTOBUF_google_2fprotobuf_2futil_2finternal_2ftestdata_2fdefault_5fvalue_2eproto__INCLUDED

#include <string>

#include <google/protobuf/stubs/common.h>

#if GOOGLE_PROTOBUF_VERSION < 3001000
#error This file was generated by a newer version of protoc which is
#error incompatible with your Protocol Buffer headers.  Please update
#error your headers.
#endif
#if 3001000 < GOOGLE_PROTOBUF_MIN_PROTOC_VERSION
#error This file was generated by an older version of protoc which is
#error incompatible with your Protocol Buffer headers.  Please
#error regenerate this file with a newer version of protoc.
#endif

#include <google/protobuf/arena.h>
#include <google/protobuf/arenastring.h>
#include <google/protobuf/generated_message_util.h>
#include <google/protobuf/metadata.h>
#include <google/protobuf/message.h>
#include <google/protobuf/repeated_field.h>
#include <google/protobuf/extension_set.h>
#include <google/protobuf/map.h>
#include <google/protobuf/map_field_inl.h>
#include <google/protobuf/generated_enum_reflection.h>
#include <google/protobuf/unknown_field_set.h>
#include <google/protobuf/any.pb.h>
#include <google/protobuf/struct.pb.h>
#include <google/protobuf/wrappers.pb.h>
// @@protoc_insertion_point(includes)

namespace google {
namespace protobuf {
namespace testing {

// Internal implementation detail -- do not call these.
void protobuf_AddDesc_google_2fprotobuf_2futil_2finternal_2ftestdata_2fdefault_5fvalue_2eproto();
void protobuf_InitDefaults_google_2fprotobuf_2futil_2finternal_2ftestdata_2fdefault_5fvalue_2eproto();
void protobuf_AssignDesc_google_2fprotobuf_2futil_2finternal_2ftestdata_2fdefault_5fvalue_2eproto();
void protobuf_ShutdownFile_google_2fprotobuf_2futil_2finternal_2ftestdata_2fdefault_5fvalue_2eproto();

class AnyData;
class AnyMessage;
class DefaultValueTestCases;
class DoubleMessage;
class DoubleValueMessage;
class IntToStringMap;
class ListValueMessage;
class MessageMap;
class MessageMap_M;
class MixedMap;
class MixedMap2;
class RequestMessage;
class StringtoIntMap;
class StructMessage;
class ValueMessage;

enum MixedMap2_E {
  MixedMap2_E_E0 = 0,
  MixedMap2_E_E1 = 1,
  MixedMap2_E_E2 = 2,
  MixedMap2_E_E3 = 3,
  MixedMap2_E_MixedMap2_E_INT_MIN_SENTINEL_DO_NOT_USE_ = ::google::protobuf::kint32min,
  MixedMap2_E_MixedMap2_E_INT_MAX_SENTINEL_DO_NOT_USE_ = ::google::protobuf::kint32max
};
bool MixedMap2_E_IsValid(int value);
const MixedMap2_E MixedMap2_E_E_MIN = MixedMap2_E_E0;
const MixedMap2_E MixedMap2_E_E_MAX = MixedMap2_E_E3;
const int MixedMap2_E_E_ARRAYSIZE = MixedMap2_E_E_MAX + 1;

const ::google::protobuf::EnumDescriptor* MixedMap2_E_descriptor();
inline const ::std::string& MixedMap2_E_Name(MixedMap2_E value) {
  return ::google::protobuf::internal::NameOfEnum(
    MixedMap2_E_descriptor(), value);
}
inline bool MixedMap2_E_Parse(
    const ::std::string& name, MixedMap2_E* value) {
  return ::google::protobuf::internal::ParseNamedEnum<MixedMap2_E>(
    MixedMap2_E_descriptor(), name, value);
}
// ===================================================================

class DefaultValueTestCases : public ::google::protobuf::Message /* @@protoc_insertion_point(class_definition:google.protobuf.testing.DefaultValueTestCases) */ {
 public:
  DefaultValueTestCases();
  virtual ~DefaultValueTestCases();

  DefaultValueTestCases(const DefaultValueTestCases& from);

  inline DefaultValueTestCases& operator=(const DefaultValueTestCases& from) {
    CopyFrom(from);
    return *this;
  }

  static const ::google::protobuf::Descriptor* descriptor();
  static const DefaultValueTestCases& default_instance();

  static const DefaultValueTestCases* internal_default_instance();

  void Swap(DefaultValueTestCases* other);

  // implements Message ----------------------------------------------

  inline DefaultValueTestCases* New() const { return New(NULL); }

  DefaultValueTestCases* New(::google::protobuf::Arena* arena) const;
  void CopyFrom(const ::google::protobuf::Message& from);
  void MergeFrom(const ::google::protobuf::Message& from);
  void CopyFrom(const DefaultValueTestCases& from);
  void MergeFrom(const DefaultValueTestCases& from);
  void Clear();
  bool IsInitialized() const;

  size_t ByteSizeLong() const;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input);
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const;
  ::google::protobuf::uint8* InternalSerializeWithCachedSizesToArray(
      bool deterministic, ::google::protobuf::uint8* output) const;
  ::google::protobuf::uint8* SerializeWithCachedSizesToArray(::google::protobuf::uint8* output) const {
    return InternalSerializeWithCachedSizesToArray(false, output);
  }
  int GetCachedSize() const { return _cached_size_; }
  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const;
  void InternalSwap(DefaultValueTestCases* other);
  void UnsafeMergeFrom(const DefaultValueTestCases& from);
  private:
  inline ::google::protobuf::Arena* GetArenaNoVirtual() const {
    return _internal_metadata_.arena();
  }
  inline void* MaybeArenaPtr() const {
    return _internal_metadata_.raw_arena_ptr();
  }
  public:

  ::google::protobuf::Metadata GetMetadata() const;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // optional .google.protobuf.testing.DoubleMessage empty_double = 1;
  bool has_empty_double() const;
  void clear_empty_double();
  static const int kEmptyDoubleFieldNumber = 1;
  const ::google::protobuf::testing::DoubleMessage& empty_double() const;
  ::google::protobuf::testing::DoubleMessage* mutable_empty_double();
  ::google::protobuf::testing::DoubleMessage* release_empty_double();
  void set_allocated_empty_double(::google::protobuf::testing::DoubleMessage* empty_double);

  // optional .google.protobuf.testing.DoubleMessage double_with_default_value = 2;
  bool has_double_with_default_value() const;
  void clear_double_with_default_value();
  static const int kDoubleWithDefaultValueFieldNumber = 2;
  const ::google::protobuf::testing::DoubleMessage& double_with_default_value() const;
  ::google::protobuf::testing::DoubleMessage* mutable_double_with_default_value();
  ::google::protobuf::testing::DoubleMessage* release_double_with_default_value();
  void set_allocated_double_with_default_value(::google::protobuf::testing::DoubleMessage* double_with_default_value);

  // optional .google.protobuf.testing.DoubleMessage double_with_nondefault_value = 3;
  bool has_double_with_nondefault_value() const;
  void clear_double_with_nondefault_value();
  static const int kDoubleWithNondefaultValueFieldNumber = 3;
  const ::google::protobuf::testing::DoubleMessage& double_with_nondefault_value() const;
  ::google::protobuf::testing::DoubleMessage* mutable_double_with_nondefault_value();
  ::google::protobuf::testing::DoubleMessage* release_double_with_nondefault_value();
  void set_allocated_double_with_nondefault_value(::google::protobuf::testing::DoubleMessage* double_with_nondefault_value);

  // optional .google.protobuf.testing.DoubleMessage repeated_double = 4;
  bool has_repeated_double() const;
  void clear_repeated_double();
  static const int kRepeatedDoubleFieldNumber = 4;
  const ::google::protobuf::testing::DoubleMessage& repeated_double() const;
  ::google::protobuf::testing::DoubleMessage* mutable_repeated_double();
  ::google::protobuf::testing::DoubleMessage* release_repeated_double();
  void set_allocated_repeated_double(::google::protobuf::testing::DoubleMessage* repeated_double);

  // optional .google.protobuf.testing.DoubleMessage nested_message = 5;
  bool has_nested_message() const;
  void clear_nested_message();
  static const int kNestedMessageFieldNumber = 5;
  const ::google::protobuf::testing::DoubleMessage& nested_message() const;
  ::google::protobuf::testing::DoubleMessage* mutable_nested_message();
  ::google::protobuf::testing::DoubleMessage* release_nested_message();
  void set_allocated_nested_message(::google::protobuf::testing::DoubleMessage* nested_message);

  // optional .google.protobuf.testing.DoubleMessage repeated_nested_message = 6;
  bool has_repeated_nested_message() const;
  void clear_repeated_nested_message();
  static const int kRepeatedNestedMessageFieldNumber = 6;
  const ::google::protobuf::testing::DoubleMessage& repeated_nested_message() const;
  ::google::protobuf::testing::DoubleMessage* mutable_repeated_nested_message();
  ::google::protobuf::testing::DoubleMessage* release_repeated_nested_message();
  void set_allocated_repeated_nested_message(::google::protobuf::testing::DoubleMessage* repeated_nested_message);

  // optional .google.protobuf.testing.DoubleMessage double_message_with_oneof = 7;
  bool has_double_message_with_oneof() const;
  void clear_double_message_with_oneof();
  static const int kDoubleMessageWithOneofFieldNumber = 7;
  const ::google::protobuf::testing::DoubleMessage& double_message_with_oneof() const;
  ::google::protobuf::testing::DoubleMessage* mutable_double_message_with_oneof();
  ::google::protobuf::testing::DoubleMessage* release_double_message_with_oneof();
  void set_allocated_double_message_with_oneof(::google::protobuf::testing::DoubleMessage* double_message_with_oneof);

  // optional .google.protobuf.testing.StructMessage empty_struct = 201;
  bool has_empty_struct() const;
  void clear_empty_struct();
  static const int kEmptyStructFieldNumber = 201;
  const ::google::protobuf::testing::StructMessage& empty_struct() const;
  ::google::protobuf::testing::StructMessage* mutable_empty_struct();
  ::google::protobuf::testing::StructMessage* release_empty_struct();
  void set_allocated_empty_struct(::google::protobuf::testing::StructMessage* empty_struct);

  // optional .google.protobuf.testing.StructMessage empty_struct2 = 202;
  bool has_empty_struct2() const;
  void clear_empty_struct2();
  static const int kEmptyStruct2FieldNumber = 202;
  const ::google::protobuf::testing::StructMessage& empty_struct2() const;
  ::google::protobuf::testing::StructMessage* mutable_empty_struct2();
  ::google::protobuf::testing::StructMessage* release_empty_struct2();
  void set_allocated_empty_struct2(::google::protobuf::testing::StructMessage* empty_struct2);

  // optional .google.protobuf.testing.StructMessage struct_with_null_value = 203;
  bool has_struct_with_null_value() const;
  void clear_struct_with_null_value();
  static const int kStructWithNullValueFieldNumber = 203;
  const ::google::protobuf::testing::StructMessage& struct_with_null_value() const;
  ::google::protobuf::testing::StructMessage* mutable_struct_with_null_value();
  ::google::protobuf::testing::StructMessage* release_struct_with_null_value();
  void set_allocated_struct_with_null_value(::google::protobuf::testing::StructMessage* struct_with_null_value);

  // optional .google.protobuf.testing.StructMessage struct_with_values = 204;
  bool has_struct_with_values() const;
  void clear_struct_with_values();
  static const int kStructWithValuesFieldNumber = 204;
  const ::google::protobuf::testing::StructMessage& struct_with_values() const;
  ::google::protobuf::testing::StructMessage* mutable_struct_with_values();
  ::google::protobuf::testing::StructMessage* release_struct_with_values();
  void set_allocated_struct_with_values(::google::protobuf::testing::StructMessage* struct_with_values);

  // optional .google.protobuf.testing.StructMessage struct_with_nested_struct = 205;
  bool has_struct_with_nested_struct() const;
  void clear_struct_with_nested_struct();
  static const int kStructWithNestedStructFieldNumber = 205;
  const ::google::protobuf::testing::StructMessage& struct_with_nested_struct() const;
  ::google::protobuf::testing::StructMessage* mutable_struct_with_nested_struct();
  ::google::protobuf::testing::StructMessage* release_struct_with_nested_struct();
  void set_allocated_struct_with_nested_struct(::google::protobuf::testing::StructMessage* struct_with_nested_struct);

  // optional .google.protobuf.testing.StructMessage struct_with_nested_list = 206;
  bool has_struct_with_nested_list() const;
  void clear_struct_with_nested_list();
  static const int kStructWithNestedListFieldNumber = 206;
  const ::google::protobuf::testing::StructMessage& struct_with_nested_list() const;
  ::google::protobuf::testing::StructMessage* mutable_struct_with_nested_list();
  ::google::protobuf::testing::StructMessage* release_struct_with_nested_list();
  void set_allocated_struct_with_nested_list(::google::protobuf::testing::StructMessage* struct_with_nested_list);

  // optional .google.protobuf.testing.StructMessage struct_with_list_of_nulls = 207;
  bool has_struct_with_list_of_nulls() const;
  void clear_struct_with_list_of_nulls();
  static const int kStructWithListOfNullsFieldNumber = 207;
  const ::google::protobuf::testing::StructMessage& struct_with_list_of_nulls() const;
  ::google::protobuf::testing::StructMessage* mutable_struct_with_list_of_nulls();
  ::google::protobuf::testing::StructMessage* release_struct_with_list_of_nulls();
  void set_allocated_struct_with_list_of_nulls(::google::protobuf::testing::StructMessage* struct_with_list_of_nulls);

  // optional .google.protobuf.testing.StructMessage struct_with_list_of_lists = 208;
  bool has_struct_with_list_of_lists() const;
  void clear_struct_with_list_of_lists();
  static const int kStructWithListOfListsFieldNumber = 208;
  const ::google::protobuf::testing::StructMessage& struct_with_list_of_lists() const;
  ::google::protobuf::testing::StructMessage* mutable_struct_with_list_of_lists();
  ::google::protobuf::testing::StructMessage* release_struct_with_list_of_lists();
  void set_allocated_struct_with_list_of_lists(::google::protobuf::testing::StructMessage* struct_with_list_of_lists);

  // optional .google.protobuf.testing.StructMessage struct_with_list_of_structs = 209;
  bool has_struct_with_list_of_structs() const;
  void clear_struct_with_list_of_structs();
  static const int kStructWithListOfStructsFieldNumber = 209;
  const ::google::protobuf::testing::StructMessage& struct_with_list_of_structs() const;
  ::google::protobuf::testing::StructMessage* mutable_struct_with_list_of_structs();
  ::google::protobuf::testing::StructMessage* release_struct_with_list_of_structs();
  void set_allocated_struct_with_list_of_structs(::google::protobuf::testing::StructMessage* struct_with_list_of_structs);

  // optional .google.protobuf.Struct top_level_struct = 210;
  bool has_top_level_struct() const;
  void clear_top_level_struct();
  static const int kTopLevelStructFieldNumber = 210;
  const ::google::protobuf::Struct& top_level_struct() const;
  ::google::protobuf::Struct* mutable_top_level_struct();
  ::google::protobuf::Struct* release_top_level_struct();
  void set_allocated_top_level_struct(::google::protobuf::Struct* top_level_struct);

  // optional .google.protobuf.testing.ValueMessage value_wrapper_simple = 212;
  bool has_value_wrapper_simple() const;
  void clear_value_wrapper_simple();
  static const int kValueWrapperSimpleFieldNumber = 212;
  const ::google::protobuf::testing::ValueMessage& value_wrapper_simple() const;
  ::google::protobuf::testing::ValueMessage* mutable_value_wrapper_simple();
  ::google::protobuf::testing::ValueMessage* release_value_wrapper_simple();
  void set_allocated_value_wrapper_simple(::google::protobuf::testing::ValueMessage* value_wrapper_simple);

  // optional .google.protobuf.testing.ValueMessage value_wrapper_with_struct = 213;
  bool has_value_wrapper_with_struct() const;
  void clear_value_wrapper_with_struct();
  static const int kValueWrapperWithStructFieldNumber = 213;
  const ::google::protobuf::testing::ValueMessage& value_wrapper_with_struct() const;
  ::google::protobuf::testing::ValueMessage* mutable_value_wrapper_with_struct();
  ::google::protobuf::testing::ValueMessage* release_value_wrapper_with_struct();
  void set_allocated_value_wrapper_with_struct(::google::protobuf::testing::ValueMessage* value_wrapper_with_struct);

  // optional .google.protobuf.testing.ValueMessage value_wrapper_with_list = 214;
  bool has_value_wrapper_with_list() const;
  void clear_value_wrapper_with_list();
  static const int kValueWrapperWithListFieldNumber = 214;
  const ::google::protobuf::testing::ValueMessage& value_wrapper_with_list() const;
  ::google::protobuf::testing::ValueMessage* mutable_value_wrapper_with_list();
  ::google::protobuf::testing::ValueMessage* release_value_wrapper_with_list();
  void set_allocated_value_wrapper_with_list(::google::protobuf::testing::ValueMessage* value_wrapper_with_list);

  // optional .google.protobuf.testing.ListValueMessage list_value_wrapper = 215;
  bool has_list_value_wrapper() const;
  void clear_list_value_wrapper();
  static const int kListValueWrapperFieldNumber = 215;
  const ::google::protobuf::testing::ListValueMessage& list_value_wrapper() const;
  ::google::protobuf::testing::ListValueMessage* mutable_list_value_wrapper();
  ::google::protobuf::testing::ListValueMessage* release_list_value_wrapper();
  void set_allocated_list_value_wrapper(::google::protobuf::testing::ListValueMessage* list_value_wrapper);

  // optional .google.protobuf.Value top_level_value_simple = 216;
  bool has_top_level_value_simple() const;
  void clear_top_level_value_simple();
  static const int kTopLevelValueSimpleFieldNumber = 216;
  const ::google::protobuf::Value& top_level_value_simple() const;
  ::google::protobuf::Value* mutable_top_level_value_simple();
  ::google::protobuf::Value* release_top_level_value_simple();
  void set_allocated_top_level_value_simple(::google::protobuf::Value* top_level_value_simple);

  // optional .google.protobuf.Value top_level_value_with_struct = 217;
  bool has_top_level_value_with_struct() const;
  void clear_top_level_value_with_struct();
  static const int kTopLevelValueWithStructFieldNumber = 217;
  const ::google::protobuf::Value& top_level_value_with_struct() const;
  ::google::protobuf::Value* mutable_top_level_value_with_struct();
  ::google::protobuf::Value* release_top_level_value_with_struct();
  void set_allocated_top_level_value_with_struct(::google::protobuf::Value* top_level_value_with_struct);

  // optional .google.protobuf.Value top_level_value_with_list = 218;
  bool has_top_level_value_with_list() const;
  void clear_top_level_value_with_list();
  static const int kTopLevelValueWithListFieldNumber = 218;
  const ::google::protobuf::Value& top_level_value_with_list() const;
  ::google::protobuf::Value* mutable_top_level_value_with_list();
  ::google::protobuf::Value* release_top_level_value_with_list();
  void set_allocated_top_level_value_with_list(::google::protobuf::Value* top_level_value_with_list);

  // optional .google.protobuf.ListValue top_level_listvalue = 219;
  bool has_top_level_listvalue() const;
  void clear_top_level_listvalue();
  static const int kTopLevelListvalueFieldNumber = 219;
  const ::google::protobuf::ListValue& top_level_listvalue() const;
  ::google::protobuf::ListValue* mutable_top_level_listvalue();
  ::google::protobuf::ListValue* release_top_level_listvalue();
  void set_allocated_top_level_listvalue(::google::protobuf::ListValue* top_level_listvalue);

  // optional .google.protobuf.testing.AnyMessage empty_any = 301;
  bool has_empty_any() const;
  void clear_empty_any();
  static const int kEmptyAnyFieldNumber = 301;
  const ::google::protobuf::testing::AnyMessage& empty_any() const;
  ::google::protobuf::testing::AnyMessage* mutable_empty_any();
  ::google::protobuf::testing::AnyMessage* release_empty_any();
  void set_allocated_empty_any(::google::protobuf::testing::AnyMessage* empty_any);

  // optional .google.protobuf.testing.AnyMessage type_only_any = 302;
  bool has_type_only_any() const;
  void clear_type_only_any();
  static const int kTypeOnlyAnyFieldNumber = 302;
  const ::google::protobuf::testing::AnyMessage& type_only_any() const;
  ::google::protobuf::testing::AnyMessage* mutable_type_only_any();
  ::google::protobuf::testing::AnyMessage* release_type_only_any();
  void set_allocated_type_only_any(::google::protobuf::testing::AnyMessage* type_only_any);

  // optional .google.protobuf.testing.AnyMessage recursive_any = 303;
  bool has_recursive_any() const;
  void clear_recursive_any();
  static const int kRecursiveAnyFieldNumber = 303;
  const ::google::protobuf::testing::AnyMessage& recursive_any() const;
  ::google::protobuf::testing::AnyMessage* mutable_recursive_any();
  ::google::protobuf::testing::AnyMessage* release_recursive_any();
  void set_allocated_recursive_any(::google::protobuf::testing::AnyMessage* recursive_any);

  // optional .google.protobuf.testing.AnyMessage any_with_message_value = 304;
  bool has_any_with_message_value() const;
  void clear_any_with_message_value();
  static const int kAnyWithMessageValueFieldNumber = 304;
  const ::google::protobuf::testing::AnyMessage& any_with_message_value() const;
  ::google::protobuf::testing::AnyMessage* mutable_any_with_message_value();
  ::google::protobuf::testing::AnyMessage* release_any_with_message_value();
  void set_allocated_any_with_message_value(::google::protobuf::testing::AnyMessage* any_with_message_value);

  // optional .google.protobuf.testing.AnyMessage any_with_nested_message = 305;
  bool has_any_with_nested_message() const;
  void clear_any_with_nested_message();
  static const int kAnyWithNestedMessageFieldNumber = 305;
  const ::google::protobuf::testing::AnyMessage& any_with_nested_message() const;
  ::google::protobuf::testing::AnyMessage* mutable_any_with_nested_message();
  ::google::protobuf::testing::AnyMessage* release_any_with_nested_message();
  void set_allocated_any_with_nested_message(::google::protobuf::testing::AnyMessage* any_with_nested_message);

  // optional .google.protobuf.testing.AnyMessage any_with_message_containing_map = 306;
  bool has_any_with_message_containing_map() const;
  void clear_any_with_message_containing_map();
  static const int kAnyWithMessageContainingMapFieldNumber = 306;
  const ::google::protobuf::testing::AnyMessage& any_with_message_containing_map() const;
  ::google::protobuf::testing::AnyMessage* mutable_any_with_message_containing_map();
  ::google::protobuf::testing::AnyMessage* release_any_with_message_containing_map();
  void set_allocated_any_with_message_containing_map(::google::protobuf::testing::AnyMessage* any_with_message_containing_map);

  // optional .google.protobuf.testing.AnyMessage any_with_message_containing_struct = 307;
  bool has_any_with_message_containing_struct() const;
  void clear_any_with_message_containing_struct();
  static const int kAnyWithMessageContainingStructFieldNumber = 307;
  const ::google::protobuf::testing::AnyMessage& any_with_message_containing_struct() const;
  ::google::protobuf::testing::AnyMessage* mutable_any_with_message_containing_struct();
  ::google::protobuf::testing::AnyMessage* release_any_with_message_containing_struct();
  void set_allocated_any_with_message_containing_struct(::google::protobuf::testing::AnyMessage* any_with_message_containing_struct);

  // optional .google.protobuf.Any top_level_any = 308;
  bool has_top_level_any() const;
  void clear_top_level_any();
  static const int kTopLevelAnyFieldNumber = 308;
  const ::google::protobuf::Any& top_level_any() const;
  ::google::protobuf::Any* mutable_top_level_any();
  ::google::protobuf::Any* release_top_level_any();
  void set_allocated_top_level_any(::google::protobuf::Any* top_level_any);

  // optional .google.protobuf.testing.StringtoIntMap empty_map = 401;
  bool has_empty_map() const;
  void clear_empty_map();
  static const int kEmptyMapFieldNumber = 401;
  const ::google::protobuf::testing::StringtoIntMap& empty_map() const;
  ::google::protobuf::testing::StringtoIntMap* mutable_empty_map();
  ::google::protobuf::testing::StringtoIntMap* release_empty_map();
  void set_allocated_empty_map(::google::protobuf::testing::StringtoIntMap* empty_map);

  // optional .google.protobuf.testing.StringtoIntMap string_to_int = 402;
  bool has_string_to_int() const;
  void clear_string_to_int();
  static const int kStringToIntFieldNumber = 402;
  const ::google::protobuf::testing::StringtoIntMap& string_to_int() const;
  ::google::protobuf::testing::StringtoIntMap* mutable_string_to_int();
  ::google::protobuf::testing::StringtoIntMap* release_string_to_int();
  void set_allocated_string_to_int(::google::protobuf::testing::StringtoIntMap* string_to_int);

  // optional .google.protobuf.testing.IntToStringMap int_to_string = 403;
  bool has_int_to_string() const;
  void clear_int_to_string();
  static const int kIntToStringFieldNumber = 403;
  const ::google::protobuf::testing::IntToStringMap& int_to_string() const;
  ::google::protobuf::testing::IntToStringMap* mutable_int_to_string();
  ::google::protobuf::testing::IntToStringMap* release_int_to_string();
  void set_allocated_int_to_string(::google::protobuf::testing::IntToStringMap* int_to_string);

  // optional .google.protobuf.testing.MixedMap mixed1 = 404;
  bool has_mixed1() const;
  void clear_mixed1();
  static const int kMixed1FieldNumber = 404;
  const ::google::protobuf::testing::MixedMap& mixed1() const;
  ::google::protobuf::testing::MixedMap* mutable_mixed1();
  ::google::protobuf::testing::MixedMap* release_mixed1();
  void set_allocated_mixed1(::google::protobuf::testing::MixedMap* mixed1);

  // optional .google.protobuf.testing.MixedMap2 mixed2 = 405;
  bool has_mixed2() const;
  void clear_mixed2();
  static const int kMixed2FieldNumber = 405;
  const ::google::protobuf::testing::MixedMap2& mixed2() const;
  ::google::protobuf::testing::MixedMap2* mutable_mixed2();
  ::google::protobuf::testing::MixedMap2* release_mixed2();
  void set_allocated_mixed2(::google::protobuf::testing::MixedMap2* mixed2);

  // optional .google.protobuf.testing.MixedMap2 empty_mixed2 = 406;
  bool has_empty_mixed2() const;
  void clear_empty_mixed2();
  static const int kEmptyMixed2FieldNumber = 406;
  const ::google::protobuf::testing::MixedMap2& empty_mixed2() const;
  ::google::protobuf::testing::MixedMap2* mutable_empty_mixed2();
  ::google::protobuf::testing::MixedMap2* release_empty_mixed2();
  void set_allocated_empty_mixed2(::google::protobuf::testing::MixedMap2* empty_mixed2);

  // optional .google.protobuf.testing.MessageMap map_of_objects = 407;
  bool has_map_of_objects() const;
  void clear_map_of_objects();
  static const int kMapOfObjectsFieldNumber = 407;
  const ::google::protobuf::testing::MessageMap& map_of_objects() const;
  ::google::protobuf::testing::MessageMap* mutable_map_of_objects();
  ::google::protobuf::testing::MessageMap* release_map_of_objects();
  void set_allocated_map_of_objects(::google::protobuf::testing::MessageMap* map_of_objects);

  // optional .google.protobuf.testing.MixedMap mixed_empty = 408;
  bool has_mixed_empty() const;
  void clear_mixed_empty();
  static const int kMixedEmptyFieldNumber = 408;
  const ::google::protobuf::testing::MixedMap& mixed_empty() const;
  ::google::protobuf::testing::MixedMap* mutable_mixed_empty();
  ::google::protobuf::testing::MixedMap* release_mixed_empty();
  void set_allocated_mixed_empty(::google::protobuf::testing::MixedMap* mixed_empty);

  // optional .google.protobuf.testing.MessageMap message_map_empty = 409;
  bool has_message_map_empty() const;
  void clear_message_map_empty();
  static const int kMessageMapEmptyFieldNumber = 409;
  const ::google::protobuf::testing::MessageMap& message_map_empty() const;
  ::google::protobuf::testing::MessageMap* mutable_message_map_empty();
  ::google::protobuf::testing::MessageMap* release_message_map_empty();
  void set_allocated_message_map_empty(::google::protobuf::testing::MessageMap* message_map_empty);

  // optional .google.protobuf.testing.DoubleValueMessage double_value = 501;
  bool has_double_value() const;
  void clear_double_value();
  static const int kDoubleValueFieldNumber = 501;
  const ::google::protobuf::testing::DoubleValueMessage& double_value() const;
  ::google::protobuf::testing::DoubleValueMessage* mutable_double_value();
  ::google::protobuf::testing::DoubleValueMessage* release_double_value();
  void set_allocated_double_value(::google::protobuf::testing::DoubleValueMessage* double_value);

  // optional .google.protobuf.testing.DoubleValueMessage double_value_default = 502;
  bool has_double_value_default() const;
  void clear_double_value_default();
  static const int kDoubleValueDefaultFieldNumber = 502;
  const ::google::protobuf::testing::DoubleValueMessage& double_value_default() const;
  ::google::protobuf::testing::DoubleValueMessage* mutable_double_value_default();
  ::google::protobuf::testing::DoubleValueMessage* release_double_value_default();
  void set_allocated_double_value_default(::google::protobuf::testing::DoubleValueMessage* double_value_default);

  // @@protoc_insertion_point(class_scope:google.protobuf.testing.DefaultValueTestCases)
 private:

  ::google::protobuf::internal::InternalMetadataWithArena _internal_metadata_;
  ::google::protobuf::testing::DoubleMessage* empty_double_;
  ::google::protobuf::testing::DoubleMessage* double_with_default_value_;
  ::google::protobuf::testing::DoubleMessage* double_with_nondefault_value_;
  ::google::protobuf::testing::DoubleMessage* repeated_double_;
  ::google::protobuf::testing::DoubleMessage* nested_message_;
  ::google::protobuf::testing::DoubleMessage* repeated_nested_message_;
  ::google::protobuf::testing::DoubleMessage* double_message_with_oneof_;
  ::google::protobuf::testing::StructMessage* empty_struct_;
  ::google::protobuf::testing::StructMessage* empty_struct2_;
  ::google::protobuf::testing::StructMessage* struct_with_null_value_;
  ::google::protobuf::testing::StructMessage* struct_with_values_;
  ::google::protobuf::testing::StructMessage* struct_with_nested_struct_;
  ::google::protobuf::testing::StructMessage* struct_with_nested_list_;
  ::google::protobuf::testing::StructMessage* struct_with_list_of_nulls_;
  ::google::protobuf::testing::StructMessage* struct_with_list_of_lists_;
  ::google::protobuf::testing::StructMessage* struct_with_list_of_structs_;
  ::google::protobuf::Struct* top_level_struct_;
  ::google::protobuf::testing::ValueMessage* value_wrapper_simple_;
  ::google::protobuf::testing::ValueMessage* value_wrapper_with_struct_;
  ::google::protobuf::testing::ValueMessage* value_wrapper_with_list_;
  ::google::protobuf::testing::ListValueMessage* list_value_wrapper_;
  ::google::protobuf::Value* top_level_value_simple_;
  ::google::protobuf::Value* top_level_value_with_struct_;
  ::google::protobuf::Value* top_level_value_with_list_;
  ::google::protobuf::ListValue* top_level_listvalue_;
  ::google::protobuf::testing::AnyMessage* empty_any_;
  ::google::protobuf::testing::AnyMessage* type_only_any_;
  ::google::protobuf::testing::AnyMessage* recursive_any_;
  ::google::protobuf::testing::AnyMessage* any_with_message_value_;
  ::google::protobuf::testing::AnyMessage* any_with_nested_message_;
  ::google::protobuf::testing::AnyMessage* any_with_message_containing_map_;
  ::google::protobuf::testing::AnyMessage* any_with_message_containing_struct_;
  ::google::protobuf::Any* top_level_any_;
  ::google::protobuf::testing::StringtoIntMap* empty_map_;
  ::google::protobuf::testing::StringtoIntMap* string_to_int_;
  ::google::protobuf::testing::IntToStringMap* int_to_string_;
  ::google::protobuf::testing::MixedMap* mixed1_;
  ::google::protobuf::testing::MixedMap2* mixed2_;
  ::google::protobuf::testing::MixedMap2* empty_mixed2_;
  ::google::protobuf::testing::MessageMap* map_of_objects_;
  ::google::protobuf::testing::MixedMap* mixed_empty_;
  ::google::protobuf::testing::MessageMap* message_map_empty_;
  ::google::protobuf::testing::DoubleValueMessage* double_value_;
  ::google::protobuf::testing::DoubleValueMessage* double_value_default_;
  mutable int _cached_size_;
  friend void  protobuf_InitDefaults_google_2fprotobuf_2futil_2finternal_2ftestdata_2fdefault_5fvalue_2eproto_impl();
  friend void  protobuf_AddDesc_google_2fprotobuf_2futil_2finternal_2ftestdata_2fdefault_5fvalue_2eproto_impl();
  friend void protobuf_AssignDesc_google_2fprotobuf_2futil_2finternal_2ftestdata_2fdefault_5fvalue_2eproto();
  friend void protobuf_ShutdownFile_google_2fprotobuf_2futil_2finternal_2ftestdata_2fdefault_5fvalue_2eproto();

  void InitAsDefaultInstance();
};
extern ::google::protobuf::internal::ExplicitlyConstructed<DefaultValueTestCases> DefaultValueTestCases_default_instance_;

// -------------------------------------------------------------------

class DoubleMessage : public ::google::protobuf::Message /* @@protoc_insertion_point(class_definition:google.protobuf.testing.DoubleMessage) */ {
 public:
  DoubleMessage();
  virtual ~DoubleMessage();

  DoubleMessage(const DoubleMessage& from);

  inline DoubleMessage& operator=(const DoubleMessage& from) {
    CopyFrom(from);
    return *this;
  }

  static const ::google::protobuf::Descriptor* descriptor();
  static const DoubleMessage& default_instance();

  enum ValueCase {
    kStrValue = 112,
    kNumValue = 113,
    VALUE_NOT_SET = 0,
  };

  static const DoubleMessage* internal_default_instance();

  void Swap(DoubleMessage* other);

  // implements Message ----------------------------------------------

  inline DoubleMessage* New() const { return New(NULL); }

  DoubleMessage* New(::google::protobuf::Arena* arena) const;
  void CopyFrom(const ::google::protobuf::Message& from);
  void MergeFrom(const ::google::protobuf::Message& from);
  void CopyFrom(const DoubleMessage& from);
  void MergeFrom(const DoubleMessage& from);
  void Clear();
  bool IsInitialized() const;

  size_t ByteSizeLong() const;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input);
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const;
  ::google::protobuf::uint8* InternalSerializeWithCachedSizesToArray(
      bool deterministic, ::google::protobuf::uint8* output) const;
  ::google::protobuf::uint8* SerializeWithCachedSizesToArray(::google::protobuf::uint8* output) const {
    return InternalSerializeWithCachedSizesToArray(false, output);
  }
  int GetCachedSize() const { return _cached_size_; }
  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const;
  void InternalSwap(DoubleMessage* other);
  void UnsafeMergeFrom(const DoubleMessage& from);
  private:
  inline ::google::protobuf::Arena* GetArenaNoVirtual() const {
    return _internal_metadata_.arena();
  }
  inline void* MaybeArenaPtr() const {
    return _internal_metadata_.raw_arena_ptr();
  }
  public:

  ::google::protobuf::Metadata GetMetadata() const;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // optional double double_value = 1;
  void clear_double_value();
  static const int kDoubleValueFieldNumber = 1;
  double double_value() const;
  void set_double_value(double value);

  // repeated double repeated_double = 2;
  int repeated_double_size() const;
  void clear_repeated_double();
  static const int kRepeatedDoubleFieldNumber = 2;
  double repeated_double(int index) const;
  void set_repeated_double(int index, double value);
  void add_repeated_double(double value);
  const ::google::protobuf::RepeatedField< double >&
      repeated_double() const;
  ::google::protobuf::RepeatedField< double >*
      mutable_repeated_double();

  // optional .google.protobuf.testing.DoubleMessage nested_message = 3;
  bool has_nested_message() const;
  void clear_nested_message();
  static const int kNestedMessageFieldNumber = 3;
  const ::google::protobuf::testing::DoubleMessage& nested_message() const;
  ::google::protobuf::testing::DoubleMessage* mutable_nested_message();
  ::google::protobuf::testing::DoubleMessage* release_nested_message();
  void set_allocated_nested_message(::google::protobuf::testing::DoubleMessage* nested_message);

  // repeated .google.protobuf.testing.DoubleMessage repeated_nested_message = 4;
  int repeated_nested_message_size() const;
  void clear_repeated_nested_message();
  static const int kRepeatedNestedMessageFieldNumber = 4;
  const ::google::protobuf::testing::DoubleMessage& repeated_nested_message(int index) const;
  ::google::protobuf::testing::DoubleMessage* mutable_repeated_nested_message(int index);
  ::google::protobuf::testing::DoubleMessage* add_repeated_nested_message();
  ::google::protobuf::RepeatedPtrField< ::google::protobuf::testing::DoubleMessage >*
      mutable_repeated_nested_message();
  const ::google::protobuf::RepeatedPtrField< ::google::protobuf::testing::DoubleMessage >&
      repeated_nested_message() const;

  // optional .google.protobuf.DoubleValue double_wrapper = 100;
  bool has_double_wrapper() const;
  void clear_double_wrapper();
  static const int kDoubleWrapperFieldNumber = 100;
  const ::google::protobuf::DoubleValue& double_wrapper() const;
  ::google::protobuf::DoubleValue* mutable_double_wrapper();
  ::google::protobuf::DoubleValue* release_double_wrapper();
  void set_allocated_double_wrapper(::google::protobuf::DoubleValue* double_wrapper);

  // optional string str_value = 112;
  private:
  bool has_str_value() const;
  public:
  void clear_str_value();
  static const int kStrValueFieldNumber = 112;
  const ::std::string& str_value() const;
  void set_str_value(const ::std::string& value);
  void set_str_value(const char* value);
  void set_str_value(const char* value, size_t size);
  ::std::string* mutable_str_value();
  ::std::string* release_str_value();
  void set_allocated_str_value(::std::string* str_value);

  // optional int64 num_value = 113;
  private:
  bool has_num_value() const;
  public:
  void clear_num_value();
  static const int kNumValueFieldNumber = 113;
  ::google::protobuf::int64 num_value() const;
  void set_num_value(::google::protobuf::int64 value);

  ValueCase value_case() const;
  // @@protoc_insertion_point(class_scope:google.protobuf.testing.DoubleMessage)
 private:
  inline void set_has_str_value();
  inline void set_has_num_value();

  inline bool has_value() const;
  void clear_value();
  inline void clear_has_value();

  ::google::protobuf::internal::InternalMetadataWithArena _internal_metadata_;
  ::google::protobuf::RepeatedField< double > repeated_double_;
  mutable int _repeated_double_cached_byte_size_;
  ::google::protobuf::RepeatedPtrField< ::google::protobuf::testing::DoubleMessage > repeated_nested_message_;
  ::google::protobuf::testing::DoubleMessage* nested_message_;
  ::google::protobuf::DoubleValue* double_wrapper_;
  double double_value_;
  union ValueUnion {
    ValueUnion() {}
    ::google::protobuf::internal::ArenaStringPtr str_value_;
    ::google::protobuf::int64 num_value_;
  } value_;
  mutable int _cached_size_;
  ::google::protobuf::uint32 _oneof_case_[1];

  friend void  protobuf_InitDefaults_google_2fprotobuf_2futil_2finternal_2ftestdata_2fdefault_5fvalue_2eproto_impl();
  friend void  protobuf_AddDesc_google_2fprotobuf_2futil_2finternal_2ftestdata_2fdefault_5fvalue_2eproto_impl();
  friend void protobuf_AssignDesc_google_2fprotobuf_2futil_2finternal_2ftestdata_2fdefault_5fvalue_2eproto();
  friend void protobuf_ShutdownFile_google_2fprotobuf_2futil_2finternal_2ftestdata_2fdefault_5fvalue_2eproto();

  void InitAsDefaultInstance();
};
extern ::google::protobuf::internal::ExplicitlyConstructed<DoubleMessage> DoubleMessage_default_instance_;

// -------------------------------------------------------------------

class StructMessage : public ::google::protobuf::Message /* @@protoc_insertion_point(class_definition:google.protobuf.testing.StructMessage) */ {
 public:
  StructMessage();
  virtual ~StructMessage();

  StructMessage(const StructMessage& from);

  inline StructMessage& operator=(const StructMessage& from) {
    CopyFrom(from);
    return *this;
  }

  static const ::google::protobuf::Descriptor* descriptor();
  static const StructMessage& default_instance();

  static const StructMessage* internal_default_instance();

  void Swap(StructMessage* other);

  // implements Message ----------------------------------------------

  inline StructMessage* New() const { return New(NULL); }

  StructMessage* New(::google::protobuf::Arena* arena) const;
  void CopyFrom(const ::google::protobuf::Message& from);
  void MergeFrom(const ::google::protobuf::Message& from);
  void CopyFrom(const StructMessage& from);
  void MergeFrom(const StructMessage& from);
  void Clear();
  bool IsInitialized() const;

  size_t ByteSizeLong() const;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input);
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const;
  ::google::protobuf::uint8* InternalSerializeWithCachedSizesToArray(
      bool deterministic, ::google::protobuf::uint8* output) const;
  ::google::protobuf::uint8* SerializeWithCachedSizesToArray(::google::protobuf::uint8* output) const {
    return InternalSerializeWithCachedSizesToArray(false, output);
  }
  int GetCachedSize() const { return _cached_size_; }
  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const;
  void InternalSwap(StructMessage* other);
  void UnsafeMergeFrom(const StructMessage& from);
  private:
  inline ::google::protobuf::Arena* GetArenaNoVirtual() const {
    return _internal_metadata_.arena();
  }
  inline void* MaybeArenaPtr() const {
    return _internal_metadata_.raw_arena_ptr();
  }
  public:

  ::google::protobuf::Metadata GetMetadata() const;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // optional .google.protobuf.Struct struct = 1;
  bool has_struct_() const;
  void clear_struct_();
  static const int kStructFieldNumber = 1;
  const ::google::protobuf::Struct& struct_() const;
  ::google::protobuf::Struct* mutable_struct_();
  ::google::protobuf::Struct* release_struct_();
  void set_allocated_struct_(::google::protobuf::Struct* struct_);

  // @@protoc_insertion_point(class_scope:google.protobuf.testing.StructMessage)
 private:

  ::google::protobuf::internal::InternalMetadataWithArena _internal_metadata_;
  ::google::protobuf::Struct* struct__;
  mutable int _cached_size_;
  friend void  protobuf_InitDefaults_google_2fprotobuf_2futil_2finternal_2ftestdata_2fdefault_5fvalue_2eproto_impl();
  friend void  protobuf_AddDesc_google_2fprotobuf_2futil_2finternal_2ftestdata_2fdefault_5fvalue_2eproto_impl();
  friend void protobuf_AssignDesc_google_2fprotobuf_2futil_2finternal_2ftestdata_2fdefault_5fvalue_2eproto();
  friend void protobuf_ShutdownFile_google_2fprotobuf_2futil_2finternal_2ftestdata_2fdefault_5fvalue_2eproto();

  void InitAsDefaultInstance();
};
extern ::google::protobuf::internal::ExplicitlyConstructed<StructMessage> StructMessage_default_instance_;

// -------------------------------------------------------------------

class ValueMessage : public ::google::protobuf::Message /* @@protoc_insertion_point(class_definition:google.protobuf.testing.ValueMessage) */ {
 public:
  ValueMessage();
  virtual ~ValueMessage();

  ValueMessage(const ValueMessage& from);

  inline ValueMessage& operator=(const ValueMessage& from) {
    CopyFrom(from);
    return *this;
  }

  static const ::google::protobuf::Descriptor* descriptor();
  static const ValueMessage& default_instance();

  static const ValueMessage* internal_default_instance();

  void Swap(ValueMessage* other);

  // implements Message ----------------------------------------------

  inline ValueMessage* New() const { return New(NULL); }

  ValueMessage* New(::google::protobuf::Arena* arena) const;
  void CopyFrom(const ::google::protobuf::Message& from);
  void MergeFrom(const ::google::protobuf::Message& from);
  void CopyFrom(const ValueMessage& from);
  void MergeFrom(const ValueMessage& from);
  void Clear();
  bool IsInitialized() const;

  size_t ByteSizeLong() const;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input);
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const;
  ::google::protobuf::uint8* InternalSerializeWithCachedSizesToArray(
      bool deterministic, ::google::protobuf::uint8* output) const;
  ::google::protobuf::uint8* SerializeWithCachedSizesToArray(::google::protobuf::uint8* output) const {
    return InternalSerializeWithCachedSizesToArray(false, output);
  }
  int GetCachedSize() const { return _cached_size_; }
  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const;
  void InternalSwap(ValueMessage* other);
  void UnsafeMergeFrom(const ValueMessage& from);
  private:
  inline ::google::protobuf::Arena* GetArenaNoVirtual() const {
    return _internal_metadata_.arena();
  }
  inline void* MaybeArenaPtr() const {
    return _internal_metadata_.raw_arena_ptr();
  }
  public:

  ::google::protobuf::Metadata GetMetadata() const;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // optional .google.protobuf.Value value = 1;
  bool has_value() const;
  void clear_value();
  static const int kValueFieldNumber = 1;
  const ::google::protobuf::Value& value() const;
  ::google::protobuf::Value* mutable_value();
  ::google::protobuf::Value* release_value();
  void set_allocated_value(::google::protobuf::Value* value);

  // @@protoc_insertion_point(class_scope:google.protobuf.testing.ValueMessage)
 private:

  ::google::protobuf::internal::InternalMetadataWithArena _internal_metadata_;
  ::google::protobuf::Value* value_;
  mutable int _cached_size_;
  friend void  protobuf_InitDefaults_google_2fprotobuf_2futil_2finternal_2ftestdata_2fdefault_5fvalue_2eproto_impl();
  friend void  protobuf_AddDesc_google_2fprotobuf_2futil_2finternal_2ftestdata_2fdefault_5fvalue_2eproto_impl();
  friend void protobuf_AssignDesc_google_2fprotobuf_2futil_2finternal_2ftestdata_2fdefault_5fvalue_2eproto();
  friend void protobuf_ShutdownFile_google_2fprotobuf_2futil_2finternal_2ftestdata_2fdefault_5fvalue_2eproto();

  void InitAsDefaultInstance();
};
extern ::google::protobuf::internal::ExplicitlyConstructed<ValueMessage> ValueMessage_default_instance_;

// -------------------------------------------------------------------

class ListValueMessage : public ::google::protobuf::Message /* @@protoc_insertion_point(class_definition:google.protobuf.testing.ListValueMessage) */ {
 public:
  ListValueMessage();
  virtual ~ListValueMessage();

  ListValueMessage(const ListValueMessage& from);

  inline ListValueMessage& operator=(const ListValueMessage& from) {
    CopyFrom(from);
    return *this;
  }

  static const ::google::protobuf::Descriptor* descriptor();
  static const ListValueMessage& default_instance();

  static const ListValueMessage* internal_default_instance();

  void Swap(ListValueMessage* other);

  // implements Message ----------------------------------------------

  inline ListValueMessage* New() const { return New(NULL); }

  ListValueMessage* New(::google::protobuf::Arena* arena) const;
  void CopyFrom(const ::google::protobuf::Message& from);
  void MergeFrom(const ::google::protobuf::Message& from);
  void CopyFrom(const ListValueMessage& from);
  void MergeFrom(const ListValueMessage& from);
  void Clear();
  bool IsInitialized() const;

  size_t ByteSizeLong() const;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input);
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const;
  ::google::protobuf::uint8* InternalSerializeWithCachedSizesToArray(
      bool deterministic, ::google::protobuf::uint8* output) const;
  ::google::protobuf::uint8* SerializeWithCachedSizesToArray(::google::protobuf::uint8* output) const {
    return InternalSerializeWithCachedSizesToArray(false, output);
  }
  int GetCachedSize() const { return _cached_size_; }
  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const;
  void InternalSwap(ListValueMessage* other);
  void UnsafeMergeFrom(const ListValueMessage& from);
  private:
  inline ::google::protobuf::Arena* GetArenaNoVirtual() const {
    return _internal_metadata_.arena();
  }
  inline void* MaybeArenaPtr() const {
    return _internal_metadata_.raw_arena_ptr();
  }
  public:

  ::google::protobuf::Metadata GetMetadata() const;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // optional .google.protobuf.ListValue shopping_list = 1;
  bool has_shopping_list() const;
  void clear_shopping_list();
  static const int kShoppingListFieldNumber = 1;
  const ::google::protobuf::ListValue& shopping_list() const;
  ::google::protobuf::ListValue* mutable_shopping_list();
  ::google::protobuf::ListValue* release_shopping_list();
  void set_allocated_shopping_list(::google::protobuf::ListValue* shopping_list);

  // @@protoc_insertion_point(class_scope:google.protobuf.testing.ListValueMessage)
 private:

  ::google::protobuf::internal::InternalMetadataWithArena _internal_metadata_;
  ::google::protobuf::ListValue* shopping_list_;
  mutable int _cached_size_;
  friend void  protobuf_InitDefaults_google_2fprotobuf_2futil_2finternal_2ftestdata_2fdefault_5fvalue_2eproto_impl();
  friend void  protobuf_AddDesc_google_2fprotobuf_2futil_2finternal_2ftestdata_2fdefault_5fvalue_2eproto_impl();
  friend void protobuf_AssignDesc_google_2fprotobuf_2futil_2finternal_2ftestdata_2fdefault_5fvalue_2eproto();
  friend void protobuf_ShutdownFile_google_2fprotobuf_2futil_2finternal_2ftestdata_2fdefault_5fvalue_2eproto();

  void InitAsDefaultInstance();
};
extern ::google::protobuf::internal::ExplicitlyConstructed<ListValueMessage> ListValueMessage_default_instance_;

// -------------------------------------------------------------------

class RequestMessage : public ::google::protobuf::Message /* @@protoc_insertion_point(class_definition:google.protobuf.testing.RequestMessage) */ {
 public:
  RequestMessage();
  virtual ~RequestMessage();

  RequestMessage(const RequestMessage& from);

  inline RequestMessage& operator=(const RequestMessage& from) {
    CopyFrom(from);
    return *this;
  }

  static const ::google::protobuf::Descriptor* descriptor();
  static const RequestMessage& default_instance();

  static const RequestMessage* internal_default_instance();

  void Swap(RequestMessage* other);

  // implements Message ----------------------------------------------

  inline RequestMessage* New() const { return New(NULL); }

  RequestMessage* New(::google::protobuf::Arena* arena) const;
  void CopyFrom(const ::google::protobuf::Message& from);
  void MergeFrom(const ::google::protobuf::Message& from);
  void CopyFrom(const RequestMessage& from);
  void MergeFrom(const RequestMessage& from);
  void Clear();
  bool IsInitialized() const;

  size_t ByteSizeLong() const;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input);
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const;
  ::google::protobuf::uint8* InternalSerializeWithCachedSizesToArray(
      bool deterministic, ::google::protobuf::uint8* output) const;
  ::google::protobuf::uint8* SerializeWithCachedSizesToArray(::google::protobuf::uint8* output) const {
    return InternalSerializeWithCachedSizesToArray(false, output);
  }
  int GetCachedSize() const { return _cached_size_; }
  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const;
  void InternalSwap(RequestMessage* other);
  void UnsafeMergeFrom(const RequestMessage& from);
  private:
  inline ::google::protobuf::Arena* GetArenaNoVirtual() const {
    return _internal_metadata_.arena();
  }
  inline void* MaybeArenaPtr() const {
    return _internal_metadata_.raw_arena_ptr();
  }
  public:

  ::google::protobuf::Metadata GetMetadata() const;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // optional string content = 1;
  void clear_content();
  static const int kContentFieldNumber = 1;
  const ::std::string& content() const;
  void set_content(const ::std::string& value);
  void set_content(const char* value);
  void set_content(const char* value, size_t size);
  ::std::string* mutable_content();
  ::std::string* release_content();
  void set_allocated_content(::std::string* content);

  // @@protoc_insertion_point(class_scope:google.protobuf.testing.RequestMessage)
 private:

  ::google::protobuf::internal::InternalMetadataWithArena _internal_metadata_;
  ::google::protobuf::internal::ArenaStringPtr content_;
  mutable int _cached_size_;
  friend void  protobuf_InitDefaults_google_2fprotobuf_2futil_2finternal_2ftestdata_2fdefault_5fvalue_2eproto_impl();
  friend void  protobuf_AddDesc_google_2fprotobuf_2futil_2finternal_2ftestdata_2fdefault_5fvalue_2eproto_impl();
  friend void protobuf_AssignDesc_google_2fprotobuf_2futil_2finternal_2ftestdata_2fdefault_5fvalue_2eproto();
  friend void protobuf_ShutdownFile_google_2fprotobuf_2futil_2finternal_2ftestdata_2fdefault_5fvalue_2eproto();

  void InitAsDefaultInstance();
};
extern ::google::protobuf::internal::ExplicitlyConstructed<RequestMessage> RequestMessage_default_instance_;

// -------------------------------------------------------------------

class AnyMessage : public ::google::protobuf::Message /* @@protoc_insertion_point(class_definition:google.protobuf.testing.AnyMessage) */ {
 public:
  AnyMessage();
  virtual ~AnyMessage();

  AnyMessage(const AnyMessage& from);

  inline AnyMessage& operator=(const AnyMessage& from) {
    CopyFrom(from);
    return *this;
  }

  static const ::google::protobuf::Descriptor* descriptor();
  static const AnyMessage& default_instance();

  static const AnyMessage* internal_default_instance();

  void Swap(AnyMessage* other);

  // implements Message ----------------------------------------------

  inline AnyMessage* New() const { return New(NULL); }

  AnyMessage* New(::google::protobuf::Arena* arena) const;
  void CopyFrom(const ::google::protobuf::Message& from);
  void MergeFrom(const ::google::protobuf::Message& from);
  void CopyFrom(const AnyMessage& from);
  void MergeFrom(const AnyMessage& from);
  void Clear();
  bool IsInitialized() const;

  size_t ByteSizeLong() const;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input);
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const;
  ::google::protobuf::uint8* InternalSerializeWithCachedSizesToArray(
      bool deterministic, ::google::protobuf::uint8* output) const;
  ::google::protobuf::uint8* SerializeWithCachedSizesToArray(::google::protobuf::uint8* output) const {
    return InternalSerializeWithCachedSizesToArray(false, output);
  }
  int GetCachedSize() const { return _cached_size_; }
  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const;
  void InternalSwap(AnyMessage* other);
  void UnsafeMergeFrom(const AnyMessage& from);
  private:
  inline ::google::protobuf::Arena* GetArenaNoVirtual() const {
    return _internal_metadata_.arena();
  }
  inline void* MaybeArenaPtr() const {
    return _internal_metadata_.raw_arena_ptr();
  }
  public:

  ::google::protobuf::Metadata GetMetadata() const;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // optional .google.protobuf.Any any = 1;
  bool has_any() const;
  void clear_any();
  static const int kAnyFieldNumber = 1;
  const ::google::protobuf::Any& any() const;
  ::google::protobuf::Any* mutable_any();
  ::google::protobuf::Any* release_any();
  void set_allocated_any(::google::protobuf::Any* any);

  // optional .google.protobuf.testing.AnyData data = 2;
  bool has_data() const;
  void clear_data();
  static const int kDataFieldNumber = 2;
  const ::google::protobuf::testing::AnyData& data() const;
  ::google::protobuf::testing::AnyData* mutable_data();
  ::google::protobuf::testing::AnyData* release_data();
  void set_allocated_data(::google::protobuf::testing::AnyData* data);

  // @@protoc_insertion_point(class_scope:google.protobuf.testing.AnyMessage)
 private:

  ::google::protobuf::internal::InternalMetadataWithArena _internal_metadata_;
  ::google::protobuf::Any* any_;
  ::google::protobuf::testing::AnyData* data_;
  mutable int _cached_size_;
  friend void  protobuf_InitDefaults_google_2fprotobuf_2futil_2finternal_2ftestdata_2fdefault_5fvalue_2eproto_impl();
  friend void  protobuf_AddDesc_google_2fprotobuf_2futil_2finternal_2ftestdata_2fdefault_5fvalue_2eproto_impl();
  friend void protobuf_AssignDesc_google_2fprotobuf_2futil_2finternal_2ftestdata_2fdefault_5fvalue_2eproto();
  friend void protobuf_ShutdownFile_google_2fprotobuf_2futil_2finternal_2ftestdata_2fdefault_5fvalue_2eproto();

  void InitAsDefaultInstance();
};
extern ::google::protobuf::internal::ExplicitlyConstructed<AnyMessage> AnyMessage_default_instance_;

// -------------------------------------------------------------------

class AnyData : public ::google::protobuf::Message /* @@protoc_insertion_point(class_definition:google.protobuf.testing.AnyData) */ {
 public:
  AnyData();
  virtual ~AnyData();

  AnyData(const AnyData& from);

  inline AnyData& operator=(const AnyData& from) {
    CopyFrom(from);
    return *this;
  }

  static const ::google::protobuf::Descriptor* descriptor();
  static const AnyData& default_instance();

  static const AnyData* internal_default_instance();

  void Swap(AnyData* other);

  // implements Message ----------------------------------------------

  inline AnyData* New() const { return New(NULL); }

  AnyData* New(::google::protobuf::Arena* arena) const;
  void CopyFrom(const ::google::protobuf::Message& from);
  void MergeFrom(const ::google::protobuf::Message& from);
  void CopyFrom(const AnyData& from);
  void MergeFrom(const AnyData& from);
  void Clear();
  bool IsInitialized() const;

  size_t ByteSizeLong() const;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input);
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const;
  ::google::protobuf::uint8* InternalSerializeWithCachedSizesToArray(
      bool deterministic, ::google::protobuf::uint8* output) const;
  ::google::protobuf::uint8* SerializeWithCachedSizesToArray(::google::protobuf::uint8* output) const {
    return InternalSerializeWithCachedSizesToArray(false, output);
  }
  int GetCachedSize() const { return _cached_size_; }
  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const;
  void InternalSwap(AnyData* other);
  void UnsafeMergeFrom(const AnyData& from);
  private:
  inline ::google::protobuf::Arena* GetArenaNoVirtual() const {
    return _internal_metadata_.arena();
  }
  inline void* MaybeArenaPtr() const {
    return _internal_metadata_.raw_arena_ptr();
  }
  public:

  ::google::protobuf::Metadata GetMetadata() const;

  // nested types ----------------------------------------------------


  // accessors -------------------------------------------------------

  // optional int32 attr = 1;
  void clear_attr();
  static const int kAttrFieldNumber = 1;
  ::google::protobuf::int32 attr() const;
  void set_attr(::google::protobuf::int32 value);

  // optional string str = 2;
  void clear_str();
  static const int kStrFieldNumber = 2;
  const ::std::string& str() const;
  void set_str(const ::std::string& value);
  void set_str(const char* value);
  void set_str(const char* value, size_t size);
  ::std::string* mutable_str();
  ::std::string* release_str();
  void set_allocated_str(::std::string* str);

  // repeated string msgs = 3;
  int msgs_size() const;
  void clear_msgs();
  static const int kMsgsFieldNumber = 3;
  const ::std::string& msgs(int index) const;
  ::std::string* mutable_msgs(int index);
  void set_msgs(int index, const ::std::string& value);
  void set_msgs(int index, const char* value);
  void set_msgs(int index, const char* value, size_t size);
  ::std::string* add_msgs();
  void add_msgs(const ::std::string& value);
  void add_msgs(const char* value);
  void add_msgs(const char* value, size_t size);
  const ::google::protobuf::RepeatedPtrField< ::std::string>& msgs() const;
  ::google::protobuf::RepeatedPtrField< ::std::string>* mutable_msgs();

  // optional .google.protobuf.testing.AnyData nested_data = 4;
  bool has_nested_data() const;
  void clear_nested_data();
  static const int kNestedDataFieldNumber = 4;
  const ::google::protobuf::testing::AnyData& nested_data() const;
  ::google::protobuf::testing::AnyData* mutable_nested_data();
  ::google::protobuf::testing::AnyData* release_nested_data();
  void set_allocated_nested_data(::google::protobuf::testing::AnyData* nested_data);

  // map<string, string> map_data = 7;
  int map_data_size() const;
  void clear_map_data();
  static const int kMapDataFieldNumber = 7;
  const ::google::protobuf::Map< ::std::string, ::std::string >&
      map_data() const;
  ::google::protobuf::Map< ::std::string, ::std::string >*
      mutable_map_data();

  // optional .google.protobuf.Struct struct_data = 8;
  bool has_struct_data() const;
  void clear_struct_data();
  static const int kStructDataFieldNumber = 8;
  const ::google::protobuf::Struct& struct_data() const;
  ::google::protobuf::Struct* mutable_struct_data();
  ::google::protobuf::Struct* release_struct_data();
  void set_allocated_struct_data(::google::protobuf::Struct* struct_data);

  // repeated .google.protobuf.testing.AnyData repeated_data = 9;
  int repeated_data_size() const;
  void clear_repeated_data();
  static const int kRepeatedDataFieldNumber = 9;
  const ::google::protobuf::testing::AnyData& repeated_data(int index) const;
  ::google::protobuf::testing::AnyData* mutable_repeated_data(int index);
  ::google::protobuf::testing::AnyData* add_repeated_data();
  ::google::protobuf::RepeatedPtrField< ::google::protobuf::testing::AnyData >*
      mutable_repeated_data();
  const ::google::protobuf::RepeatedPtrField< ::google::protobuf::testing::AnyData >&
      repeated_data() const;

  // @@protoc_insertion_point(class_scope:google.protobuf.testing.AnyData)
 private:

  ::google::protobuf::internal::InternalMetadataWithArena _internal_metadata_;
  ::google::protobuf::RepeatedPtrField< ::std::string> msgs_;
  typedef ::google::protobuf::internal::MapEntryLite<
      ::std::string, ::std::string,
      ::google::protobuf::internal::WireFormatLite::TYPE_STRING,
      ::google::protobuf::internal::WireFormatLite::TYPE_STRING,
      0 >
      AnyData_MapDataEntry;
  ::google::protobuf::internal::MapField<
      ::std::string, ::std::string,
      ::google::protobuf::internal::WireFormatLite::TYPE_STRING,
      ::google::protobuf::internal::WireFormatLite::TYPE_STRING,
      0 > map_data_;
  ::google::protobuf::RepeatedPtrField< ::google::protobuf::testing::AnyData > repeated_data_;
  ::google::protobuf::internal::ArenaStringPtr str_;
  ::google::protobuf::testing::AnyData* nested_data_;
  ::google::protobuf::Struct* struct_data_;
  ::google::protobuf::int32 attr_;
  mutable int _cached_size_;
  friend void  protobuf_InitDefaults_google_2fprotobuf_2futil_2finternal_2ftestdata_2fdefault_5fvalue_2eproto_impl();
  friend void  protobuf_AddDesc_google_2fprotobuf_2futil_2finternal_2ftestdata_2fdefault_5fvalue_2eproto_impl();
  friend void protobuf_AssignDesc_google_2fprotobuf_2futil_2finternal_2ftestdata_2fdefault_5fvalue_2eproto();
  friend void protobuf_ShutdownFile_google_2fprotobuf_2futil_2finternal_2ftestdata_2fdefault_5fvalue_2eproto();

  void InitAsDefaultInstance();
};
extern ::google::protobuf::internal::ExplicitlyConstructed<AnyData> AnyData_default_instance_;

// -------------------------------------------------------------------

class StringtoIntMap : public ::google::protobuf::Message /* @@protoc_insertion_point(class_definition:google.protobuf.testing.StringtoIntMap) */ {
 public:
  StringtoIntMap();
  virtual ~StringtoIntMap();

  StringtoIntMap(const StringtoIntMap& from);

  inline StringtoIntMap& operator=(const StringtoIntMap& from) {
    CopyFrom(from);
    return *this;
  }

  static const ::google::protobuf::Descriptor* descriptor();
  static const StringtoIntMap& default_instance();

  static const StringtoIntMap* internal_default_instance();

  void Swap(StringtoIntMap* other);

  // implements Message ----------------------------------------------

  inline StringtoIntMap* New() const { return New(NULL); }

  StringtoIntMap* New(::google::protobuf::Arena* arena) const;
  void CopyFrom(const ::google::protobuf::Message& from);
  void MergeFrom(const ::google::protobuf::Message& from);
  void CopyFrom(const StringtoIntMap& from);
  void MergeFrom(const StringtoIntMap& from);
  void Clear();
  bool IsInitialized() const;

  size_t ByteSizeLong() const;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input);
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const;
  ::google::protobuf::uint8* InternalSerializeWithCachedSizesToArray(
      bool deterministic, ::google::protobuf::uint8* output) const;
  ::google::protobuf::uint8* SerializeWithCachedSizesToArray(::google::protobuf::uint8* output) const {
    return InternalSerializeWithCachedSizesToArray(false, output);
  }
  int GetCachedSize() const { return _cached_size_; }
  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const;
  void InternalSwap(StringtoIntMap* other);
  void UnsafeMergeFrom(const StringtoIntMap& from);
  private:
  inline ::google::protobuf::Arena* GetArenaNoVirtual() const {
    return _internal_metadata_.arena();
  }
  inline void* MaybeArenaPtr() const {
    return _internal_metadata_.raw_arena_ptr();
  }
  public:

  ::google::protobuf::Metadata GetMetadata() const;

  // nested types ----------------------------------------------------


  // accessors -------------------------------------------------------

  // map<string, int32> map = 1;
  int map_size() const;
  void clear_map();
  static const int kMapFieldNumber = 1;
  const ::google::protobuf::Map< ::std::string, ::google::protobuf::int32 >&
      map() const;
  ::google::protobuf::Map< ::std::string, ::google::protobuf::int32 >*
      mutable_map();

  // @@protoc_insertion_point(class_scope:google.protobuf.testing.StringtoIntMap)
 private:

  ::google::protobuf::internal::InternalMetadataWithArena _internal_metadata_;
  typedef ::google::protobuf::internal::MapEntryLite<
      ::std::string, ::google::protobuf::int32,
      ::google::protobuf::internal::WireFormatLite::TYPE_STRING,
      ::google::protobuf::internal::WireFormatLite::TYPE_INT32,
      0 >
      StringtoIntMap_MapEntry;
  ::google::protobuf::internal::MapField<
      ::std::string, ::google::protobuf::int32,
      ::google::protobuf::internal::WireFormatLite::TYPE_STRING,
      ::google::protobuf::internal::WireFormatLite::TYPE_INT32,
      0 > map_;
  mutable int _cached_size_;
  friend void  protobuf_InitDefaults_google_2fprotobuf_2futil_2finternal_2ftestdata_2fdefault_5fvalue_2eproto_impl();
  friend void  protobuf_AddDesc_google_2fprotobuf_2futil_2finternal_2ftestdata_2fdefault_5fvalue_2eproto_impl();
  friend void protobuf_AssignDesc_google_2fprotobuf_2futil_2finternal_2ftestdata_2fdefault_5fvalue_2eproto();
  friend void protobuf_ShutdownFile_google_2fprotobuf_2futil_2finternal_2ftestdata_2fdefault_5fvalue_2eproto();

  void InitAsDefaultInstance();
};
extern ::google::protobuf::internal::ExplicitlyConstructed<StringtoIntMap> StringtoIntMap_default_instance_;

// -------------------------------------------------------------------

class IntToStringMap : public ::google::protobuf::Message /* @@protoc_insertion_point(class_definition:google.protobuf.testing.IntToStringMap) */ {
 public:
  IntToStringMap();
  virtual ~IntToStringMap();

  IntToStringMap(const IntToStringMap& from);

  inline IntToStringMap& operator=(const IntToStringMap& from) {
    CopyFrom(from);
    return *this;
  }

  static const ::google::protobuf::Descriptor* descriptor();
  static const IntToStringMap& default_instance();

  static const IntToStringMap* internal_default_instance();

  void Swap(IntToStringMap* other);

  // implements Message ----------------------------------------------

  inline IntToStringMap* New() const { return New(NULL); }

  IntToStringMap* New(::google::protobuf::Arena* arena) const;
  void CopyFrom(const ::google::protobuf::Message& from);
  void MergeFrom(const ::google::protobuf::Message& from);
  void CopyFrom(const IntToStringMap& from);
  void MergeFrom(const IntToStringMap& from);
  void Clear();
  bool IsInitialized() const;

  size_t ByteSizeLong() const;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input);
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const;
  ::google::protobuf::uint8* InternalSerializeWithCachedSizesToArray(
      bool deterministic, ::google::protobuf::uint8* output) const;
  ::google::protobuf::uint8* SerializeWithCachedSizesToArray(::google::protobuf::uint8* output) const {
    return InternalSerializeWithCachedSizesToArray(false, output);
  }
  int GetCachedSize() const { return _cached_size_; }
  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const;
  void InternalSwap(IntToStringMap* other);
  void UnsafeMergeFrom(const IntToStringMap& from);
  private:
  inline ::google::protobuf::Arena* GetArenaNoVirtual() const {
    return _internal_metadata_.arena();
  }
  inline void* MaybeArenaPtr() const {
    return _internal_metadata_.raw_arena_ptr();
  }
  public:

  ::google::protobuf::Metadata GetMetadata() const;

  // nested types ----------------------------------------------------


  // accessors -------------------------------------------------------

  // map<int32, string> map = 1;
  int map_size() const;
  void clear_map();
  static const int kMapFieldNumber = 1;
  const ::google::protobuf::Map< ::google::protobuf::int32, ::std::string >&
      map() const;
  ::google::protobuf::Map< ::google::protobuf::int32, ::std::string >*
      mutable_map();

  // @@protoc_insertion_point(class_scope:google.protobuf.testing.IntToStringMap)
 private:

  ::google::protobuf::internal::InternalMetadataWithArena _internal_metadata_;
  typedef ::google::protobuf::internal::MapEntryLite<
      ::google::protobuf::int32, ::std::string,
      ::google::protobuf::internal::WireFormatLite::TYPE_INT32,
      ::google::protobuf::internal::WireFormatLite::TYPE_STRING,
      0 >
      IntToStringMap_MapEntry;
  ::google::protobuf::internal::MapField<
      ::google::protobuf::int32, ::std::string,
      ::google::protobuf::internal::WireFormatLite::TYPE_INT32,
      ::google::protobuf::internal::WireFormatLite::TYPE_STRING,
      0 > map_;
  mutable int _cached_size_;
  friend void  protobuf_InitDefaults_google_2fprotobuf_2futil_2finternal_2ftestdata_2fdefault_5fvalue_2eproto_impl();
  friend void  protobuf_AddDesc_google_2fprotobuf_2futil_2finternal_2ftestdata_2fdefault_5fvalue_2eproto_impl();
  friend void protobuf_AssignDesc_google_2fprotobuf_2futil_2finternal_2ftestdata_2fdefault_5fvalue_2eproto();
  friend void protobuf_ShutdownFile_google_2fprotobuf_2futil_2finternal_2ftestdata_2fdefault_5fvalue_2eproto();

  void InitAsDefaultInstance();
};
extern ::google::protobuf::internal::ExplicitlyConstructed<IntToStringMap> IntToStringMap_default_instance_;

// -------------------------------------------------------------------

class MixedMap : public ::google::protobuf::Message /* @@protoc_insertion_point(class_definition:google.protobuf.testing.MixedMap) */ {
 public:
  MixedMap();
  virtual ~MixedMap();

  MixedMap(const MixedMap& from);

  inline MixedMap& operator=(const MixedMap& from) {
    CopyFrom(from);
    return *this;
  }

  static const ::google::protobuf::Descriptor* descriptor();
  static const MixedMap& default_instance();

  static const MixedMap* internal_default_instance();

  void Swap(MixedMap* other);

  // implements Message ----------------------------------------------

  inline MixedMap* New() const { return New(NULL); }

  MixedMap* New(::google::protobuf::Arena* arena) const;
  void CopyFrom(const ::google::protobuf::Message& from);
  void MergeFrom(const ::google::protobuf::Message& from);
  void CopyFrom(const MixedMap& from);
  void MergeFrom(const MixedMap& from);
  void Clear();
  bool IsInitialized() const;

  size_t ByteSizeLong() const;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input);
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const;
  ::google::protobuf::uint8* InternalSerializeWithCachedSizesToArray(
      bool deterministic, ::google::protobuf::uint8* output) const;
  ::google::protobuf::uint8* SerializeWithCachedSizesToArray(::google::protobuf::uint8* output) const {
    return InternalSerializeWithCachedSizesToArray(false, output);
  }
  int GetCachedSize() const { return _cached_size_; }
  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const;
  void InternalSwap(MixedMap* other);
  void UnsafeMergeFrom(const MixedMap& from);
  private:
  inline ::google::protobuf::Arena* GetArenaNoVirtual() const {
    return _internal_metadata_.arena();
  }
  inline void* MaybeArenaPtr() const {
    return _internal_metadata_.raw_arena_ptr();
  }
  public:

  ::google::protobuf::Metadata GetMetadata() const;

  // nested types ----------------------------------------------------


  // accessors -------------------------------------------------------

  // optional string msg = 1;
  void clear_msg();
  static const int kMsgFieldNumber = 1;
  const ::std::string& msg() const;
  void set_msg(const ::std::string& value);
  void set_msg(const char* value);
  void set_msg(const char* value, size_t size);
  ::std::string* mutable_msg();
  ::std::string* release_msg();
  void set_allocated_msg(::std::string* msg);

  // map<string, float> map = 2;
  int map_size() const;
  void clear_map();
  static const int kMapFieldNumber = 2;
  const ::google::protobuf::Map< ::std::string, float >&
      map() const;
  ::google::protobuf::Map< ::std::string, float >*
      mutable_map();

  // optional int32 int_value = 3;
  void clear_int_value();
  static const int kIntValueFieldNumber = 3;
  ::google::protobuf::int32 int_value() const;
  void set_int_value(::google::protobuf::int32 value);

  // @@protoc_insertion_point(class_scope:google.protobuf.testing.MixedMap)
 private:

  ::google::protobuf::internal::InternalMetadataWithArena _internal_metadata_;
  typedef ::google::protobuf::internal::MapEntryLite<
      ::std::string, float,
      ::google::protobuf::internal::WireFormatLite::TYPE_STRING,
      ::google::protobuf::internal::WireFormatLite::TYPE_FLOAT,
      0 >
      MixedMap_MapEntry;
  ::google::protobuf::internal::MapField<
      ::std::string, float,
      ::google::protobuf::internal::WireFormatLite::TYPE_STRING,
      ::google::protobuf::internal::WireFormatLite::TYPE_FLOAT,
      0 > map_;
  ::google::protobuf::internal::ArenaStringPtr msg_;
  ::google::protobuf::int32 int_value_;
  mutable int _cached_size_;
  friend void  protobuf_InitDefaults_google_2fprotobuf_2futil_2finternal_2ftestdata_2fdefault_5fvalue_2eproto_impl();
  friend void  protobuf_AddDesc_google_2fprotobuf_2futil_2finternal_2ftestdata_2fdefault_5fvalue_2eproto_impl();
  friend void protobuf_AssignDesc_google_2fprotobuf_2futil_2finternal_2ftestdata_2fdefault_5fvalue_2eproto();
  friend void protobuf_ShutdownFile_google_2fprotobuf_2futil_2finternal_2ftestdata_2fdefault_5fvalue_2eproto();

  void InitAsDefaultInstance();
};
extern ::google::protobuf::internal::ExplicitlyConstructed<MixedMap> MixedMap_default_instance_;

// -------------------------------------------------------------------

class MixedMap2 : public ::google::protobuf::Message /* @@protoc_insertion_point(class_definition:google.protobuf.testing.MixedMap2) */ {
 public:
  MixedMap2();
  virtual ~MixedMap2();

  MixedMap2(const MixedMap2& from);

  inline MixedMap2& operator=(const MixedMap2& from) {
    CopyFrom(from);
    return *this;
  }

  static const ::google::protobuf::Descriptor* descriptor();
  static const MixedMap2& default_instance();

  static const MixedMap2* internal_default_instance();

  void Swap(MixedMap2* other);

  // implements Message ----------------------------------------------

  inline MixedMap2* New() const { return New(NULL); }

  MixedMap2* New(::google::protobuf::Arena* arena) const;
  void CopyFrom(const ::google::protobuf::Message& from);
  void MergeFrom(const ::google::protobuf::Message& from);
  void CopyFrom(const MixedMap2& from);
  void MergeFrom(const MixedMap2& from);
  void Clear();
  bool IsInitialized() const;

  size_t ByteSizeLong() const;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input);
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const;
  ::google::protobuf::uint8* InternalSerializeWithCachedSizesToArray(
      bool deterministic, ::google::protobuf::uint8* output) const;
  ::google::protobuf::uint8* SerializeWithCachedSizesToArray(::google::protobuf::uint8* output) const {
    return InternalSerializeWithCachedSizesToArray(false, output);
  }
  int GetCachedSize() const { return _cached_size_; }
  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const;
  void InternalSwap(MixedMap2* other);
  void UnsafeMergeFrom(const MixedMap2& from);
  private:
  inline ::google::protobuf::Arena* GetArenaNoVirtual() const {
    return _internal_metadata_.arena();
  }
  inline void* MaybeArenaPtr() const {
    return _internal_metadata_.raw_arena_ptr();
  }
  public:

  ::google::protobuf::Metadata GetMetadata() const;

  // nested types ----------------------------------------------------


  typedef MixedMap2_E E;
  static const E E0 =
    MixedMap2_E_E0;
  static const E E1 =
    MixedMap2_E_E1;
  static const E E2 =
    MixedMap2_E_E2;
  static const E E3 =
    MixedMap2_E_E3;
  static inline bool E_IsValid(int value) {
    return MixedMap2_E_IsValid(value);
  }
  static const E E_MIN =
    MixedMap2_E_E_MIN;
  static const E E_MAX =
    MixedMap2_E_E_MAX;
  static const int E_ARRAYSIZE =
    MixedMap2_E_E_ARRAYSIZE;
  static inline const ::google::protobuf::EnumDescriptor*
  E_descriptor() {
    return MixedMap2_E_descriptor();
  }
  static inline const ::std::string& E_Name(E value) {
    return MixedMap2_E_Name(value);
  }
  static inline bool E_Parse(const ::std::string& name,
      E* value) {
    return MixedMap2_E_Parse(name, value);
  }

  // accessors -------------------------------------------------------

  // map<int32, bool> map = 1;
  int map_size() const;
  void clear_map();
  static const int kMapFieldNumber = 1;
  const ::google::protobuf::Map< ::google::protobuf::int32, bool >&
      map() const;
  ::google::protobuf::Map< ::google::protobuf::int32, bool >*
      mutable_map();

  // optional .google.protobuf.testing.MixedMap2.E ee = 2;
  void clear_ee();
  static const int kEeFieldNumber = 2;
  ::google::protobuf::testing::MixedMap2_E ee() const;
  void set_ee(::google::protobuf::testing::MixedMap2_E value);

  // optional string msg = 4;
  void clear_msg();
  static const int kMsgFieldNumber = 4;
  const ::std::string& msg() const;
  void set_msg(const ::std::string& value);
  void set_msg(const char* value);
  void set_msg(const char* value, size_t size);
  ::std::string* mutable_msg();
  ::std::string* release_msg();
  void set_allocated_msg(::std::string* msg);

  // @@protoc_insertion_point(class_scope:google.protobuf.testing.MixedMap2)
 private:

  ::google::protobuf::internal::InternalMetadataWithArena _internal_metadata_;
  typedef ::google::protobuf::internal::MapEntryLite<
      ::google::protobuf::int32, bool,
      ::google::protobuf::internal::WireFormatLite::TYPE_INT32,
      ::google::protobuf::internal::WireFormatLite::TYPE_BOOL,
      0 >
      MixedMap2_MapEntry;
  ::google::protobuf::internal::MapField<
      ::google::protobuf::int32, bool,
      ::google::protobuf::internal::WireFormatLite::TYPE_INT32,
      ::google::protobuf::internal::WireFormatLite::TYPE_BOOL,
      0 > map_;
  ::google::protobuf::internal::ArenaStringPtr msg_;
  int ee_;
  mutable int _cached_size_;
  friend void  protobuf_InitDefaults_google_2fprotobuf_2futil_2finternal_2ftestdata_2fdefault_5fvalue_2eproto_impl();
  friend void  protobuf_AddDesc_google_2fprotobuf_2futil_2finternal_2ftestdata_2fdefault_5fvalue_2eproto_impl();
  friend void protobuf_AssignDesc_google_2fprotobuf_2futil_2finternal_2ftestdata_2fdefault_5fvalue_2eproto();
  friend void protobuf_ShutdownFile_google_2fprotobuf_2futil_2finternal_2ftestdata_2fdefault_5fvalue_2eproto();

  void InitAsDefaultInstance();
};
extern ::google::protobuf::internal::ExplicitlyConstructed<MixedMap2> MixedMap2_default_instance_;

// -------------------------------------------------------------------

class MessageMap_M : public ::google::protobuf::Message /* @@protoc_insertion_point(class_definition:google.protobuf.testing.MessageMap.M) */ {
 public:
  MessageMap_M();
  virtual ~MessageMap_M();

  MessageMap_M(const MessageMap_M& from);

  inline MessageMap_M& operator=(const MessageMap_M& from) {
    CopyFrom(from);
    return *this;
  }

  static const ::google::protobuf::Descriptor* descriptor();
  static const MessageMap_M& default_instance();

  static const MessageMap_M* internal_default_instance();

  void Swap(MessageMap_M* other);

  // implements Message ----------------------------------------------

  inline MessageMap_M* New() const { return New(NULL); }

  MessageMap_M* New(::google::protobuf::Arena* arena) const;
  void CopyFrom(const ::google::protobuf::Message& from);
  void MergeFrom(const ::google::protobuf::Message& from);
  void CopyFrom(const MessageMap_M& from);
  void MergeFrom(const MessageMap_M& from);
  void Clear();
  bool IsInitialized() const;

  size_t ByteSizeLong() const;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input);
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const;
  ::google::protobuf::uint8* InternalSerializeWithCachedSizesToArray(
      bool deterministic, ::google::protobuf::uint8* output) const;
  ::google::protobuf::uint8* SerializeWithCachedSizesToArray(::google::protobuf::uint8* output) const {
    return InternalSerializeWithCachedSizesToArray(false, output);
  }
  int GetCachedSize() const { return _cached_size_; }
  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const;
  void InternalSwap(MessageMap_M* other);
  void UnsafeMergeFrom(const MessageMap_M& from);
  private:
  inline ::google::protobuf::Arena* GetArenaNoVirtual() const {
    return _internal_metadata_.arena();
  }
  inline void* MaybeArenaPtr() const {
    return _internal_metadata_.raw_arena_ptr();
  }
  public:

  ::google::protobuf::Metadata GetMetadata() const;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // optional int32 inner_int = 1;
  void clear_inner_int();
  static const int kInnerIntFieldNumber = 1;
  ::google::protobuf::int32 inner_int() const;
  void set_inner_int(::google::protobuf::int32 value);

  // optional string inner_text = 2;
  void clear_inner_text();
  static const int kInnerTextFieldNumber = 2;
  const ::std::string& inner_text() const;
  void set_inner_text(const ::std::string& value);
  void set_inner_text(const char* value);
  void set_inner_text(const char* value, size_t size);
  ::std::string* mutable_inner_text();
  ::std::string* release_inner_text();
  void set_allocated_inner_text(::std::string* inner_text);

  // @@protoc_insertion_point(class_scope:google.protobuf.testing.MessageMap.M)
 private:

  ::google::protobuf::internal::InternalMetadataWithArena _internal_metadata_;
  ::google::protobuf::internal::ArenaStringPtr inner_text_;
  ::google::protobuf::int32 inner_int_;
  mutable int _cached_size_;
  friend void  protobuf_InitDefaults_google_2fprotobuf_2futil_2finternal_2ftestdata_2fdefault_5fvalue_2eproto_impl();
  friend void  protobuf_AddDesc_google_2fprotobuf_2futil_2finternal_2ftestdata_2fdefault_5fvalue_2eproto_impl();
  friend void protobuf_AssignDesc_google_2fprotobuf_2futil_2finternal_2ftestdata_2fdefault_5fvalue_2eproto();
  friend void protobuf_ShutdownFile_google_2fprotobuf_2futil_2finternal_2ftestdata_2fdefault_5fvalue_2eproto();

  void InitAsDefaultInstance();
};
extern ::google::protobuf::internal::ExplicitlyConstructed<MessageMap_M> MessageMap_M_default_instance_;

// -------------------------------------------------------------------

class MessageMap : public ::google::protobuf::Message /* @@protoc_insertion_point(class_definition:google.protobuf.testing.MessageMap) */ {
 public:
  MessageMap();
  virtual ~MessageMap();

  MessageMap(const MessageMap& from);

  inline MessageMap& operator=(const MessageMap& from) {
    CopyFrom(from);
    return *this;
  }

  static const ::google::protobuf::Descriptor* descriptor();
  static const MessageMap& default_instance();

  static const MessageMap* internal_default_instance();

  void Swap(MessageMap* other);

  // implements Message ----------------------------------------------

  inline MessageMap* New() const { return New(NULL); }

  MessageMap* New(::google::protobuf::Arena* arena) const;
  void CopyFrom(const ::google::protobuf::Message& from);
  void MergeFrom(const ::google::protobuf::Message& from);
  void CopyFrom(const MessageMap& from);
  void MergeFrom(const MessageMap& from);
  void Clear();
  bool IsInitialized() const;

  size_t ByteSizeLong() const;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input);
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const;
  ::google::protobuf::uint8* InternalSerializeWithCachedSizesToArray(
      bool deterministic, ::google::protobuf::uint8* output) const;
  ::google::protobuf::uint8* SerializeWithCachedSizesToArray(::google::protobuf::uint8* output) const {
    return InternalSerializeWithCachedSizesToArray(false, output);
  }
  int GetCachedSize() const { return _cached_size_; }
  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const;
  void InternalSwap(MessageMap* other);
  void UnsafeMergeFrom(const MessageMap& from);
  private:
  inline ::google::protobuf::Arena* GetArenaNoVirtual() const {
    return _internal_metadata_.arena();
  }
  inline void* MaybeArenaPtr() const {
    return _internal_metadata_.raw_arena_ptr();
  }
  public:

  ::google::protobuf::Metadata GetMetadata() const;

  // nested types ----------------------------------------------------

  typedef MessageMap_M M;

  // accessors -------------------------------------------------------

  // map<string, .google.protobuf.testing.MessageMap.M> map = 1;
  int map_size() const;
  void clear_map();
  static const int kMapFieldNumber = 1;
  const ::google::protobuf::Map< ::std::string, ::google::protobuf::testing::MessageMap_M >&
      map() const;
  ::google::protobuf::Map< ::std::string, ::google::protobuf::testing::MessageMap_M >*
      mutable_map();

  // @@protoc_insertion_point(class_scope:google.protobuf.testing.MessageMap)
 private:

  ::google::protobuf::internal::InternalMetadataWithArena _internal_metadata_;
  typedef ::google::protobuf::internal::MapEntryLite<
      ::std::string, ::google::protobuf::testing::MessageMap_M,
      ::google::protobuf::internal::WireFormatLite::TYPE_STRING,
      ::google::protobuf::internal::WireFormatLite::TYPE_MESSAGE,
      0 >
      MessageMap_MapEntry;
  ::google::protobuf::internal::MapField<
      ::std::string, ::google::protobuf::testing::MessageMap_M,
      ::google::protobuf::internal::WireFormatLite::TYPE_STRING,
      ::google::protobuf::internal::WireFormatLite::TYPE_MESSAGE,
      0 > map_;
  mutable int _cached_size_;
  friend void  protobuf_InitDefaults_google_2fprotobuf_2futil_2finternal_2ftestdata_2fdefault_5fvalue_2eproto_impl();
  friend void  protobuf_AddDesc_google_2fprotobuf_2futil_2finternal_2ftestdata_2fdefault_5fvalue_2eproto_impl();
  friend void protobuf_AssignDesc_google_2fprotobuf_2futil_2finternal_2ftestdata_2fdefault_5fvalue_2eproto();
  friend void protobuf_ShutdownFile_google_2fprotobuf_2futil_2finternal_2ftestdata_2fdefault_5fvalue_2eproto();

  void InitAsDefaultInstance();
};
extern ::google::protobuf::internal::ExplicitlyConstructed<MessageMap> MessageMap_default_instance_;

// -------------------------------------------------------------------

class DoubleValueMessage : public ::google::protobuf::Message /* @@protoc_insertion_point(class_definition:google.protobuf.testing.DoubleValueMessage) */ {
 public:
  DoubleValueMessage();
  virtual ~DoubleValueMessage();

  DoubleValueMessage(const DoubleValueMessage& from);

  inline DoubleValueMessage& operator=(const DoubleValueMessage& from) {
    CopyFrom(from);
    return *this;
  }

  static const ::google::protobuf::Descriptor* descriptor();
  static const DoubleValueMessage& default_instance();

  static const DoubleValueMessage* internal_default_instance();

  void Swap(DoubleValueMessage* other);

  // implements Message ----------------------------------------------

  inline DoubleValueMessage* New() const { return New(NULL); }

  DoubleValueMessage* New(::google::protobuf::Arena* arena) const;
  void CopyFrom(const ::google::protobuf::Message& from);
  void MergeFrom(const ::google::protobuf::Message& from);
  void CopyFrom(const DoubleValueMessage& from);
  void MergeFrom(const DoubleValueMessage& from);
  void Clear();
  bool IsInitialized() const;

  size_t ByteSizeLong() const;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input);
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const;
  ::google::protobuf::uint8* InternalSerializeWithCachedSizesToArray(
      bool deterministic, ::google::protobuf::uint8* output) const;
  ::google::protobuf::uint8* SerializeWithCachedSizesToArray(::google::protobuf::uint8* output) const {
    return InternalSerializeWithCachedSizesToArray(false, output);
  }
  int GetCachedSize() const { return _cached_size_; }
  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const;
  void InternalSwap(DoubleValueMessage* other);
  void UnsafeMergeFrom(const DoubleValueMessage& from);
  private:
  inline ::google::protobuf::Arena* GetArenaNoVirtual() const {
    return _internal_metadata_.arena();
  }
  inline void* MaybeArenaPtr() const {
    return _internal_metadata_.raw_arena_ptr();
  }
  public:

  ::google::protobuf::Metadata GetMetadata() const;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // optional .google.protobuf.DoubleValue double = 1;
  bool has_double_() const;
  void clear_double_();
  static const int kDoubleFieldNumber = 1;
  const ::google::protobuf::DoubleValue& double_() const;
  ::google::protobuf::DoubleValue* mutable_double_();
  ::google::protobuf::DoubleValue* release_double_();
  void set_allocated_double_(::google::protobuf::DoubleValue* double_);

  // @@protoc_insertion_point(class_scope:google.protobuf.testing.DoubleValueMessage)
 private:

  ::google::protobuf::internal::InternalMetadataWithArena _internal_metadata_;
  ::google::protobuf::DoubleValue* double__;
  mutable int _cached_size_;
  friend void  protobuf_InitDefaults_google_2fprotobuf_2futil_2finternal_2ftestdata_2fdefault_5fvalue_2eproto_impl();
  friend void  protobuf_AddDesc_google_2fprotobuf_2futil_2finternal_2ftestdata_2fdefault_5fvalue_2eproto_impl();
  friend void protobuf_AssignDesc_google_2fprotobuf_2futil_2finternal_2ftestdata_2fdefault_5fvalue_2eproto();
  friend void protobuf_ShutdownFile_google_2fprotobuf_2futil_2finternal_2ftestdata_2fdefault_5fvalue_2eproto();

  void InitAsDefaultInstance();
};
extern ::google::protobuf::internal::ExplicitlyConstructed<DoubleValueMessage> DoubleValueMessage_default_instance_;

// ===================================================================


// ===================================================================

#if !PROTOBUF_INLINE_NOT_IN_HEADERS
// DefaultValueTestCases

// optional .google.protobuf.testing.DoubleMessage empty_double = 1;
inline bool DefaultValueTestCases::has_empty_double() const {
  return this != internal_default_instance() && empty_double_ != NULL;
}
inline void DefaultValueTestCases::clear_empty_double() {
  if (GetArenaNoVirtual() == NULL && empty_double_ != NULL) delete empty_double_;
  empty_double_ = NULL;
}
inline const ::google::protobuf::testing::DoubleMessage& DefaultValueTestCases::empty_double() const {
  // @@protoc_insertion_point(field_get:google.protobuf.testing.DefaultValueTestCases.empty_double)
  return empty_double_ != NULL ? *empty_double_
                         : *::google::protobuf::testing::DoubleMessage::internal_default_instance();
}
inline ::google::protobuf::testing::DoubleMessage* DefaultValueTestCases::mutable_empty_double() {
  
  if (empty_double_ == NULL) {
    empty_double_ = new ::google::protobuf::testing::DoubleMessage;
  }
  // @@protoc_insertion_point(field_mutable:google.protobuf.testing.DefaultValueTestCases.empty_double)
  return empty_double_;
}
inline ::google::protobuf::testing::DoubleMessage* DefaultValueTestCases::release_empty_double() {
  // @@protoc_insertion_point(field_release:google.protobuf.testing.DefaultValueTestCases.empty_double)
  
  ::google::protobuf::testing::DoubleMessage* temp = empty_double_;
  empty_double_ = NULL;
  return temp;
}
inline void DefaultValueTestCases::set_allocated_empty_double(::google::protobuf::testing::DoubleMessage* empty_double) {
  delete empty_double_;
  empty_double_ = empty_double;
  if (empty_double) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_set_allocated:google.protobuf.testing.DefaultValueTestCases.empty_double)
}

// optional .google.protobuf.testing.DoubleMessage double_with_default_value = 2;
inline bool DefaultValueTestCases::has_double_with_default_value() const {
  return this != internal_default_instance() && double_with_default_value_ != NULL;
}
inline void DefaultValueTestCases::clear_double_with_default_value() {
  if (GetArenaNoVirtual() == NULL && double_with_default_value_ != NULL) delete double_with_default_value_;
  double_with_default_value_ = NULL;
}
inline const ::google::protobuf::testing::DoubleMessage& DefaultValueTestCases::double_with_default_value() const {
  // @@protoc_insertion_point(field_get:google.protobuf.testing.DefaultValueTestCases.double_with_default_value)
  return double_with_default_value_ != NULL ? *double_with_default_value_
                         : *::google::protobuf::testing::DoubleMessage::internal_default_instance();
}
inline ::google::protobuf::testing::DoubleMessage* DefaultValueTestCases::mutable_double_with_default_value() {
  
  if (double_with_default_value_ == NULL) {
    double_with_default_value_ = new ::google::protobuf::testing::DoubleMessage;
  }
  // @@protoc_insertion_point(field_mutable:google.protobuf.testing.DefaultValueTestCases.double_with_default_value)
  return double_with_default_value_;
}
inline ::google::protobuf::testing::DoubleMessage* DefaultValueTestCases::release_double_with_default_value() {
  // @@protoc_insertion_point(field_release:google.protobuf.testing.DefaultValueTestCases.double_with_default_value)
  
  ::google::protobuf::testing::DoubleMessage* temp = double_with_default_value_;
  double_with_default_value_ = NULL;
  return temp;
}
inline void DefaultValueTestCases::set_allocated_double_with_default_value(::google::protobuf::testing::DoubleMessage* double_with_default_value) {
  delete double_with_default_value_;
  double_with_default_value_ = double_with_default_value;
  if (double_with_default_value) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_set_allocated:google.protobuf.testing.DefaultValueTestCases.double_with_default_value)
}

// optional .google.protobuf.testing.DoubleMessage double_with_nondefault_value = 3;
inline bool DefaultValueTestCases::has_double_with_nondefault_value() const {
  return this != internal_default_instance() && double_with_nondefault_value_ != NULL;
}
inline void DefaultValueTestCases::clear_double_with_nondefault_value() {
  if (GetArenaNoVirtual() == NULL && double_with_nondefault_value_ != NULL) delete double_with_nondefault_value_;
  double_with_nondefault_value_ = NULL;
}
inline const ::google::protobuf::testing::DoubleMessage& DefaultValueTestCases::double_with_nondefault_value() const {
  // @@protoc_insertion_point(field_get:google.protobuf.testing.DefaultValueTestCases.double_with_nondefault_value)
  return double_with_nondefault_value_ != NULL ? *double_with_nondefault_value_
                         : *::google::protobuf::testing::DoubleMessage::internal_default_instance();
}
inline ::google::protobuf::testing::DoubleMessage* DefaultValueTestCases::mutable_double_with_nondefault_value() {
  
  if (double_with_nondefault_value_ == NULL) {
    double_with_nondefault_value_ = new ::google::protobuf::testing::DoubleMessage;
  }
  // @@protoc_insertion_point(field_mutable:google.protobuf.testing.DefaultValueTestCases.double_with_nondefault_value)
  return double_with_nondefault_value_;
}
inline ::google::protobuf::testing::DoubleMessage* DefaultValueTestCases::release_double_with_nondefault_value() {
  // @@protoc_insertion_point(field_release:google.protobuf.testing.DefaultValueTestCases.double_with_nondefault_value)
  
  ::google::protobuf::testing::DoubleMessage* temp = double_with_nondefault_value_;
  double_with_nondefault_value_ = NULL;
  return temp;
}
inline void DefaultValueTestCases::set_allocated_double_with_nondefault_value(::google::protobuf::testing::DoubleMessage* double_with_nondefault_value) {
  delete double_with_nondefault_value_;
  double_with_nondefault_value_ = double_with_nondefault_value;
  if (double_with_nondefault_value) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_set_allocated:google.protobuf.testing.DefaultValueTestCases.double_with_nondefault_value)
}

// optional .google.protobuf.testing.DoubleMessage repeated_double = 4;
inline bool DefaultValueTestCases::has_repeated_double() const {
  return this != internal_default_instance() && repeated_double_ != NULL;
}
inline void DefaultValueTestCases::clear_repeated_double() {
  if (GetArenaNoVirtual() == NULL && repeated_double_ != NULL) delete repeated_double_;
  repeated_double_ = NULL;
}
inline const ::google::protobuf::testing::DoubleMessage& DefaultValueTestCases::repeated_double() const {
  // @@protoc_insertion_point(field_get:google.protobuf.testing.DefaultValueTestCases.repeated_double)
  return repeated_double_ != NULL ? *repeated_double_
                         : *::google::protobuf::testing::DoubleMessage::internal_default_instance();
}
inline ::google::protobuf::testing::DoubleMessage* DefaultValueTestCases::mutable_repeated_double() {
  
  if (repeated_double_ == NULL) {
    repeated_double_ = new ::google::protobuf::testing::DoubleMessage;
  }
  // @@protoc_insertion_point(field_mutable:google.protobuf.testing.DefaultValueTestCases.repeated_double)
  return repeated_double_;
}
inline ::google::protobuf::testing::DoubleMessage* DefaultValueTestCases::release_repeated_double() {
  // @@protoc_insertion_point(field_release:google.protobuf.testing.DefaultValueTestCases.repeated_double)
  
  ::google::protobuf::testing::DoubleMessage* temp = repeated_double_;
  repeated_double_ = NULL;
  return temp;
}
inline void DefaultValueTestCases::set_allocated_repeated_double(::google::protobuf::testing::DoubleMessage* repeated_double) {
  delete repeated_double_;
  repeated_double_ = repeated_double;
  if (repeated_double) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_set_allocated:google.protobuf.testing.DefaultValueTestCases.repeated_double)
}

// optional .google.protobuf.testing.DoubleMessage nested_message = 5;
inline bool DefaultValueTestCases::has_nested_message() const {
  return this != internal_default_instance() && nested_message_ != NULL;
}
inline void DefaultValueTestCases::clear_nested_message() {
  if (GetArenaNoVirtual() == NULL && nested_message_ != NULL) delete nested_message_;
  nested_message_ = NULL;
}
inline const ::google::protobuf::testing::DoubleMessage& DefaultValueTestCases::nested_message() const {
  // @@protoc_insertion_point(field_get:google.protobuf.testing.DefaultValueTestCases.nested_message)
  return nested_message_ != NULL ? *nested_message_
                         : *::google::protobuf::testing::DoubleMessage::internal_default_instance();
}
inline ::google::protobuf::testing::DoubleMessage* DefaultValueTestCases::mutable_nested_message() {
  
  if (nested_message_ == NULL) {
    nested_message_ = new ::google::protobuf::testing::DoubleMessage;
  }
  // @@protoc_insertion_point(field_mutable:google.protobuf.testing.DefaultValueTestCases.nested_message)
  return nested_message_;
}
inline ::google::protobuf::testing::DoubleMessage* DefaultValueTestCases::release_nested_message() {
  // @@protoc_insertion_point(field_release:google.protobuf.testing.DefaultValueTestCases.nested_message)
  
  ::google::protobuf::testing::DoubleMessage* temp = nested_message_;
  nested_message_ = NULL;
  return temp;
}
inline void DefaultValueTestCases::set_allocated_nested_message(::google::protobuf::testing::DoubleMessage* nested_message) {
  delete nested_message_;
  nested_message_ = nested_message;
  if (nested_message) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_set_allocated:google.protobuf.testing.DefaultValueTestCases.nested_message)
}

// optional .google.protobuf.testing.DoubleMessage repeated_nested_message = 6;
inline bool DefaultValueTestCases::has_repeated_nested_message() const {
  return this != internal_default_instance() && repeated_nested_message_ != NULL;
}
inline void DefaultValueTestCases::clear_repeated_nested_message() {
  if (GetArenaNoVirtual() == NULL && repeated_nested_message_ != NULL) delete repeated_nested_message_;
  repeated_nested_message_ = NULL;
}
inline const ::google::protobuf::testing::DoubleMessage& DefaultValueTestCases::repeated_nested_message() const {
  // @@protoc_insertion_point(field_get:google.protobuf.testing.DefaultValueTestCases.repeated_nested_message)
  return repeated_nested_message_ != NULL ? *repeated_nested_message_
                         : *::google::protobuf::testing::DoubleMessage::internal_default_instance();
}
inline ::google::protobuf::testing::DoubleMessage* DefaultValueTestCases::mutable_repeated_nested_message() {
  
  if (repeated_nested_message_ == NULL) {
    repeated_nested_message_ = new ::google::protobuf::testing::DoubleMessage;
  }
  // @@protoc_insertion_point(field_mutable:google.protobuf.testing.DefaultValueTestCases.repeated_nested_message)
  return repeated_nested_message_;
}
inline ::google::protobuf::testing::DoubleMessage* DefaultValueTestCases::release_repeated_nested_message() {
  // @@protoc_insertion_point(field_release:google.protobuf.testing.DefaultValueTestCases.repeated_nested_message)
  
  ::google::protobuf::testing::DoubleMessage* temp = repeated_nested_message_;
  repeated_nested_message_ = NULL;
  return temp;
}
inline void DefaultValueTestCases::set_allocated_repeated_nested_message(::google::protobuf::testing::DoubleMessage* repeated_nested_message) {
  delete repeated_nested_message_;
  repeated_nested_message_ = repeated_nested_message;
  if (repeated_nested_message) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_set_allocated:google.protobuf.testing.DefaultValueTestCases.repeated_nested_message)
}

// optional .google.protobuf.testing.DoubleMessage double_message_with_oneof = 7;
inline bool DefaultValueTestCases::has_double_message_with_oneof() const {
  return this != internal_default_instance() && double_message_with_oneof_ != NULL;
}
inline void DefaultValueTestCases::clear_double_message_with_oneof() {
  if (GetArenaNoVirtual() == NULL && double_message_with_oneof_ != NULL) delete double_message_with_oneof_;
  double_message_with_oneof_ = NULL;
}
inline const ::google::protobuf::testing::DoubleMessage& DefaultValueTestCases::double_message_with_oneof() const {
  // @@protoc_insertion_point(field_get:google.protobuf.testing.DefaultValueTestCases.double_message_with_oneof)
  return double_message_with_oneof_ != NULL ? *double_message_with_oneof_
                         : *::google::protobuf::testing::DoubleMessage::internal_default_instance();
}
inline ::google::protobuf::testing::DoubleMessage* DefaultValueTestCases::mutable_double_message_with_oneof() {
  
  if (double_message_with_oneof_ == NULL) {
    double_message_with_oneof_ = new ::google::protobuf::testing::DoubleMessage;
  }
  // @@protoc_insertion_point(field_mutable:google.protobuf.testing.DefaultValueTestCases.double_message_with_oneof)
  return double_message_with_oneof_;
}
inline ::google::protobuf::testing::DoubleMessage* DefaultValueTestCases::release_double_message_with_oneof() {
  // @@protoc_insertion_point(field_release:google.protobuf.testing.DefaultValueTestCases.double_message_with_oneof)
  
  ::google::protobuf::testing::DoubleMessage* temp = double_message_with_oneof_;
  double_message_with_oneof_ = NULL;
  return temp;
}
inline void DefaultValueTestCases::set_allocated_double_message_with_oneof(::google::protobuf::testing::DoubleMessage* double_message_with_oneof) {
  delete double_message_with_oneof_;
  double_message_with_oneof_ = double_message_with_oneof;
  if (double_message_with_oneof) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_set_allocated:google.protobuf.testing.DefaultValueTestCases.double_message_with_oneof)
}

// optional .google.protobuf.testing.StructMessage empty_struct = 201;
inline bool DefaultValueTestCases::has_empty_struct() const {
  return this != internal_default_instance() && empty_struct_ != NULL;
}
inline void DefaultValueTestCases::clear_empty_struct() {
  if (GetArenaNoVirtual() == NULL && empty_struct_ != NULL) delete empty_struct_;
  empty_struct_ = NULL;
}
inline const ::google::protobuf::testing::StructMessage& DefaultValueTestCases::empty_struct() const {
  // @@protoc_insertion_point(field_get:google.protobuf.testing.DefaultValueTestCases.empty_struct)
  return empty_struct_ != NULL ? *empty_struct_
                         : *::google::protobuf::testing::StructMessage::internal_default_instance();
}
inline ::google::protobuf::testing::StructMessage* DefaultValueTestCases::mutable_empty_struct() {
  
  if (empty_struct_ == NULL) {
    empty_struct_ = new ::google::protobuf::testing::StructMessage;
  }
  // @@protoc_insertion_point(field_mutable:google.protobuf.testing.DefaultValueTestCases.empty_struct)
  return empty_struct_;
}
inline ::google::protobuf::testing::StructMessage* DefaultValueTestCases::release_empty_struct() {
  // @@protoc_insertion_point(field_release:google.protobuf.testing.DefaultValueTestCases.empty_struct)
  
  ::google::protobuf::testing::StructMessage* temp = empty_struct_;
  empty_struct_ = NULL;
  return temp;
}
inline void DefaultValueTestCases::set_allocated_empty_struct(::google::protobuf::testing::StructMessage* empty_struct) {
  delete empty_struct_;
  empty_struct_ = empty_struct;
  if (empty_struct) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_set_allocated:google.protobuf.testing.DefaultValueTestCases.empty_struct)
}

// optional .google.protobuf.testing.StructMessage empty_struct2 = 202;
inline bool DefaultValueTestCases::has_empty_struct2() const {
  return this != internal_default_instance() && empty_struct2_ != NULL;
}
inline void DefaultValueTestCases::clear_empty_struct2() {
  if (GetArenaNoVirtual() == NULL && empty_struct2_ != NULL) delete empty_struct2_;
  empty_struct2_ = NULL;
}
inline const ::google::protobuf::testing::StructMessage& DefaultValueTestCases::empty_struct2() const {
  // @@protoc_insertion_point(field_get:google.protobuf.testing.DefaultValueTestCases.empty_struct2)
  return empty_struct2_ != NULL ? *empty_struct2_
                         : *::google::protobuf::testing::StructMessage::internal_default_instance();
}
inline ::google::protobuf::testing::StructMessage* DefaultValueTestCases::mutable_empty_struct2() {
  
  if (empty_struct2_ == NULL) {
    empty_struct2_ = new ::google::protobuf::testing::StructMessage;
  }
  // @@protoc_insertion_point(field_mutable:google.protobuf.testing.DefaultValueTestCases.empty_struct2)
  return empty_struct2_;
}
inline ::google::protobuf::testing::StructMessage* DefaultValueTestCases::release_empty_struct2() {
  // @@protoc_insertion_point(field_release:google.protobuf.testing.DefaultValueTestCases.empty_struct2)
  
  ::google::protobuf::testing::StructMessage* temp = empty_struct2_;
  empty_struct2_ = NULL;
  return temp;
}
inline void DefaultValueTestCases::set_allocated_empty_struct2(::google::protobuf::testing::StructMessage* empty_struct2) {
  delete empty_struct2_;
  empty_struct2_ = empty_struct2;
  if (empty_struct2) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_set_allocated:google.protobuf.testing.DefaultValueTestCases.empty_struct2)
}

// optional .google.protobuf.testing.StructMessage struct_with_null_value = 203;
inline bool DefaultValueTestCases::has_struct_with_null_value() const {
  return this != internal_default_instance() && struct_with_null_value_ != NULL;
}
inline void DefaultValueTestCases::clear_struct_with_null_value() {
  if (GetArenaNoVirtual() == NULL && struct_with_null_value_ != NULL) delete struct_with_null_value_;
  struct_with_null_value_ = NULL;
}
inline const ::google::protobuf::testing::StructMessage& DefaultValueTestCases::struct_with_null_value() const {
  // @@protoc_insertion_point(field_get:google.protobuf.testing.DefaultValueTestCases.struct_with_null_value)
  return struct_with_null_value_ != NULL ? *struct_with_null_value_
                         : *::google::protobuf::testing::StructMessage::internal_default_instance();
}
inline ::google::protobuf::testing::StructMessage* DefaultValueTestCases::mutable_struct_with_null_value() {
  
  if (struct_with_null_value_ == NULL) {
    struct_with_null_value_ = new ::google::protobuf::testing::StructMessage;
  }
  // @@protoc_insertion_point(field_mutable:google.protobuf.testing.DefaultValueTestCases.struct_with_null_value)
  return struct_with_null_value_;
}
inline ::google::protobuf::testing::StructMessage* DefaultValueTestCases::release_struct_with_null_value() {
  // @@protoc_insertion_point(field_release:google.protobuf.testing.DefaultValueTestCases.struct_with_null_value)
  
  ::google::protobuf::testing::StructMessage* temp = struct_with_null_value_;
  struct_with_null_value_ = NULL;
  return temp;
}
inline void DefaultValueTestCases::set_allocated_struct_with_null_value(::google::protobuf::testing::StructMessage* struct_with_null_value) {
  delete struct_with_null_value_;
  struct_with_null_value_ = struct_with_null_value;
  if (struct_with_null_value) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_set_allocated:google.protobuf.testing.DefaultValueTestCases.struct_with_null_value)
}

// optional .google.protobuf.testing.StructMessage struct_with_values = 204;
inline bool DefaultValueTestCases::has_struct_with_values() const {
  return this != internal_default_instance() && struct_with_values_ != NULL;
}
inline void DefaultValueTestCases::clear_struct_with_values() {
  if (GetArenaNoVirtual() == NULL && struct_with_values_ != NULL) delete struct_with_values_;
  struct_with_values_ = NULL;
}
inline const ::google::protobuf::testing::StructMessage& DefaultValueTestCases::struct_with_values() const {
  // @@protoc_insertion_point(field_get:google.protobuf.testing.DefaultValueTestCases.struct_with_values)
  return struct_with_values_ != NULL ? *struct_with_values_
                         : *::google::protobuf::testing::StructMessage::internal_default_instance();
}
inline ::google::protobuf::testing::StructMessage* DefaultValueTestCases::mutable_struct_with_values() {
  
  if (struct_with_values_ == NULL) {
    struct_with_values_ = new ::google::protobuf::testing::StructMessage;
  }
  // @@protoc_insertion_point(field_mutable:google.protobuf.testing.DefaultValueTestCases.struct_with_values)
  return struct_with_values_;
}
inline ::google::protobuf::testing::StructMessage* DefaultValueTestCases::release_struct_with_values() {
  // @@protoc_insertion_point(field_release:google.protobuf.testing.DefaultValueTestCases.struct_with_values)
  
  ::google::protobuf::testing::StructMessage* temp = struct_with_values_;
  struct_with_values_ = NULL;
  return temp;
}
inline void DefaultValueTestCases::set_allocated_struct_with_values(::google::protobuf::testing::StructMessage* struct_with_values) {
  delete struct_with_values_;
  struct_with_values_ = struct_with_values;
  if (struct_with_values) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_set_allocated:google.protobuf.testing.DefaultValueTestCases.struct_with_values)
}

// optional .google.protobuf.testing.StructMessage struct_with_nested_struct = 205;
inline bool DefaultValueTestCases::has_struct_with_nested_struct() const {
  return this != internal_default_instance() && struct_with_nested_struct_ != NULL;
}
inline void DefaultValueTestCases::clear_struct_with_nested_struct() {
  if (GetArenaNoVirtual() == NULL && struct_with_nested_struct_ != NULL) delete struct_with_nested_struct_;
  struct_with_nested_struct_ = NULL;
}
inline const ::google::protobuf::testing::StructMessage& DefaultValueTestCases::struct_with_nested_struct() const {
  // @@protoc_insertion_point(field_get:google.protobuf.testing.DefaultValueTestCases.struct_with_nested_struct)
  return struct_with_nested_struct_ != NULL ? *struct_with_nested_struct_
                         : *::google::protobuf::testing::StructMessage::internal_default_instance();
}
inline ::google::protobuf::testing::StructMessage* DefaultValueTestCases::mutable_struct_with_nested_struct() {
  
  if (struct_with_nested_struct_ == NULL) {
    struct_with_nested_struct_ = new ::google::protobuf::testing::StructMessage;
  }
  // @@protoc_insertion_point(field_mutable:google.protobuf.testing.DefaultValueTestCases.struct_with_nested_struct)
  return struct_with_nested_struct_;
}
inline ::google::protobuf::testing::StructMessage* DefaultValueTestCases::release_struct_with_nested_struct() {
  // @@protoc_insertion_point(field_release:google.protobuf.testing.DefaultValueTestCases.struct_with_nested_struct)
  
  ::google::protobuf::testing::StructMessage* temp = struct_with_nested_struct_;
  struct_with_nested_struct_ = NULL;
  return temp;
}
inline void DefaultValueTestCases::set_allocated_struct_with_nested_struct(::google::protobuf::testing::StructMessage* struct_with_nested_struct) {
  delete struct_with_nested_struct_;
  struct_with_nested_struct_ = struct_with_nested_struct;
  if (struct_with_nested_struct) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_set_allocated:google.protobuf.testing.DefaultValueTestCases.struct_with_nested_struct)
}

// optional .google.protobuf.testing.StructMessage struct_with_nested_list = 206;
inline bool DefaultValueTestCases::has_struct_with_nested_list() const {
  return this != internal_default_instance() && struct_with_nested_list_ != NULL;
}
inline void DefaultValueTestCases::clear_struct_with_nested_list() {
  if (GetArenaNoVirtual() == NULL && struct_with_nested_list_ != NULL) delete struct_with_nested_list_;
  struct_with_nested_list_ = NULL;
}
inline const ::google::protobuf::testing::StructMessage& DefaultValueTestCases::struct_with_nested_list() const {
  // @@protoc_insertion_point(field_get:google.protobuf.testing.DefaultValueTestCases.struct_with_nested_list)
  return struct_with_nested_list_ != NULL ? *struct_with_nested_list_
                         : *::google::protobuf::testing::StructMessage::internal_default_instance();
}
inline ::google::protobuf::testing::StructMessage* DefaultValueTestCases::mutable_struct_with_nested_list() {
  
  if (struct_with_nested_list_ == NULL) {
    struct_with_nested_list_ = new ::google::protobuf::testing::StructMessage;
  }
  // @@protoc_insertion_point(field_mutable:google.protobuf.testing.DefaultValueTestCases.struct_with_nested_list)
  return struct_with_nested_list_;
}
inline ::google::protobuf::testing::StructMessage* DefaultValueTestCases::release_struct_with_nested_list() {
  // @@protoc_insertion_point(field_release:google.protobuf.testing.DefaultValueTestCases.struct_with_nested_list)
  
  ::google::protobuf::testing::StructMessage* temp = struct_with_nested_list_;
  struct_with_nested_list_ = NULL;
  return temp;
}
inline void DefaultValueTestCases::set_allocated_struct_with_nested_list(::google::protobuf::testing::StructMessage* struct_with_nested_list) {
  delete struct_with_nested_list_;
  struct_with_nested_list_ = struct_with_nested_list;
  if (struct_with_nested_list) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_set_allocated:google.protobuf.testing.DefaultValueTestCases.struct_with_nested_list)
}

// optional .google.protobuf.testing.StructMessage struct_with_list_of_nulls = 207;
inline bool DefaultValueTestCases::has_struct_with_list_of_nulls() const {
  return this != internal_default_instance() && struct_with_list_of_nulls_ != NULL;
}
inline void DefaultValueTestCases::clear_struct_with_list_of_nulls() {
  if (GetArenaNoVirtual() == NULL && struct_with_list_of_nulls_ != NULL) delete struct_with_list_of_nulls_;
  struct_with_list_of_nulls_ = NULL;
}
inline const ::google::protobuf::testing::StructMessage& DefaultValueTestCases::struct_with_list_of_nulls() const {
  // @@protoc_insertion_point(field_get:google.protobuf.testing.DefaultValueTestCases.struct_with_list_of_nulls)
  return struct_with_list_of_nulls_ != NULL ? *struct_with_list_of_nulls_
                         : *::google::protobuf::testing::StructMessage::internal_default_instance();
}
inline ::google::protobuf::testing::StructMessage* DefaultValueTestCases::mutable_struct_with_list_of_nulls() {
  
  if (struct_with_list_of_nulls_ == NULL) {
    struct_with_list_of_nulls_ = new ::google::protobuf::testing::StructMessage;
  }
  // @@protoc_insertion_point(field_mutable:google.protobuf.testing.DefaultValueTestCases.struct_with_list_of_nulls)
  return struct_with_list_of_nulls_;
}
inline ::google::protobuf::testing::StructMessage* DefaultValueTestCases::release_struct_with_list_of_nulls() {
  // @@protoc_insertion_point(field_release:google.protobuf.testing.DefaultValueTestCases.struct_with_list_of_nulls)
  
  ::google::protobuf::testing::StructMessage* temp = struct_with_list_of_nulls_;
  struct_with_list_of_nulls_ = NULL;
  return temp;
}
inline void DefaultValueTestCases::set_allocated_struct_with_list_of_nulls(::google::protobuf::testing::StructMessage* struct_with_list_of_nulls) {
  delete struct_with_list_of_nulls_;
  struct_with_list_of_nulls_ = struct_with_list_of_nulls;
  if (struct_with_list_of_nulls) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_set_allocated:google.protobuf.testing.DefaultValueTestCases.struct_with_list_of_nulls)
}

// optional .google.protobuf.testing.StructMessage struct_with_list_of_lists = 208;
inline bool DefaultValueTestCases::has_struct_with_list_of_lists() const {
  return this != internal_default_instance() && struct_with_list_of_lists_ != NULL;
}
inline void DefaultValueTestCases::clear_struct_with_list_of_lists() {
  if (GetArenaNoVirtual() == NULL && struct_with_list_of_lists_ != NULL) delete struct_with_list_of_lists_;
  struct_with_list_of_lists_ = NULL;
}
inline const ::google::protobuf::testing::StructMessage& DefaultValueTestCases::struct_with_list_of_lists() const {
  // @@protoc_insertion_point(field_get:google.protobuf.testing.DefaultValueTestCases.struct_with_list_of_lists)
  return struct_with_list_of_lists_ != NULL ? *struct_with_list_of_lists_
                         : *::google::protobuf::testing::StructMessage::internal_default_instance();
}
inline ::google::protobuf::testing::StructMessage* DefaultValueTestCases::mutable_struct_with_list_of_lists() {
  
  if (struct_with_list_of_lists_ == NULL) {
    struct_with_list_of_lists_ = new ::google::protobuf::testing::StructMessage;
  }
  // @@protoc_insertion_point(field_mutable:google.protobuf.testing.DefaultValueTestCases.struct_with_list_of_lists)
  return struct_with_list_of_lists_;
}
inline ::google::protobuf::testing::StructMessage* DefaultValueTestCases::release_struct_with_list_of_lists() {
  // @@protoc_insertion_point(field_release:google.protobuf.testing.DefaultValueTestCases.struct_with_list_of_lists)
  
  ::google::protobuf::testing::StructMessage* temp = struct_with_list_of_lists_;
  struct_with_list_of_lists_ = NULL;
  return temp;
}
inline void DefaultValueTestCases::set_allocated_struct_with_list_of_lists(::google::protobuf::testing::StructMessage* struct_with_list_of_lists) {
  delete struct_with_list_of_lists_;
  struct_with_list_of_lists_ = struct_with_list_of_lists;
  if (struct_with_list_of_lists) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_set_allocated:google.protobuf.testing.DefaultValueTestCases.struct_with_list_of_lists)
}

// optional .google.protobuf.testing.StructMessage struct_with_list_of_structs = 209;
inline bool DefaultValueTestCases::has_struct_with_list_of_structs() const {
  return this != internal_default_instance() && struct_with_list_of_structs_ != NULL;
}
inline void DefaultValueTestCases::clear_struct_with_list_of_structs() {
  if (GetArenaNoVirtual() == NULL && struct_with_list_of_structs_ != NULL) delete struct_with_list_of_structs_;
  struct_with_list_of_structs_ = NULL;
}
inline const ::google::protobuf::testing::StructMessage& DefaultValueTestCases::struct_with_list_of_structs() const {
  // @@protoc_insertion_point(field_get:google.protobuf.testing.DefaultValueTestCases.struct_with_list_of_structs)
  return struct_with_list_of_structs_ != NULL ? *struct_with_list_of_structs_
                         : *::google::protobuf::testing::StructMessage::internal_default_instance();
}
inline ::google::protobuf::testing::StructMessage* DefaultValueTestCases::mutable_struct_with_list_of_structs() {
  
  if (struct_with_list_of_structs_ == NULL) {
    struct_with_list_of_structs_ = new ::google::protobuf::testing::StructMessage;
  }
  // @@protoc_insertion_point(field_mutable:google.protobuf.testing.DefaultValueTestCases.struct_with_list_of_structs)
  return struct_with_list_of_structs_;
}
inline ::google::protobuf::testing::StructMessage* DefaultValueTestCases::release_struct_with_list_of_structs() {
  // @@protoc_insertion_point(field_release:google.protobuf.testing.DefaultValueTestCases.struct_with_list_of_structs)
  
  ::google::protobuf::testing::StructMessage* temp = struct_with_list_of_structs_;
  struct_with_list_of_structs_ = NULL;
  return temp;
}
inline void DefaultValueTestCases::set_allocated_struct_with_list_of_structs(::google::protobuf::testing::StructMessage* struct_with_list_of_structs) {
  delete struct_with_list_of_structs_;
  struct_with_list_of_structs_ = struct_with_list_of_structs;
  if (struct_with_list_of_structs) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_set_allocated:google.protobuf.testing.DefaultValueTestCases.struct_with_list_of_structs)
}

// optional .google.protobuf.Struct top_level_struct = 210;
inline bool DefaultValueTestCases::has_top_level_struct() const {
  return this != internal_default_instance() && top_level_struct_ != NULL;
}
inline void DefaultValueTestCases::clear_top_level_struct() {
  if (GetArenaNoVirtual() == NULL && top_level_struct_ != NULL) delete top_level_struct_;
  top_level_struct_ = NULL;
}
inline const ::google::protobuf::Struct& DefaultValueTestCases::top_level_struct() const {
  // @@protoc_insertion_point(field_get:google.protobuf.testing.DefaultValueTestCases.top_level_struct)
  return top_level_struct_ != NULL ? *top_level_struct_
                         : *::google::protobuf::Struct::internal_default_instance();
}
inline ::google::protobuf::Struct* DefaultValueTestCases::mutable_top_level_struct() {
  
  if (top_level_struct_ == NULL) {
    top_level_struct_ = new ::google::protobuf::Struct;
  }
  // @@protoc_insertion_point(field_mutable:google.protobuf.testing.DefaultValueTestCases.top_level_struct)
  return top_level_struct_;
}
inline ::google::protobuf::Struct* DefaultValueTestCases::release_top_level_struct() {
  // @@protoc_insertion_point(field_release:google.protobuf.testing.DefaultValueTestCases.top_level_struct)
  
  ::google::protobuf::Struct* temp = top_level_struct_;
  top_level_struct_ = NULL;
  return temp;
}
inline void DefaultValueTestCases::set_allocated_top_level_struct(::google::protobuf::Struct* top_level_struct) {
  delete top_level_struct_;
  if (top_level_struct != NULL && top_level_struct->GetArena() != NULL) {
    ::google::protobuf::Struct* new_top_level_struct = new ::google::protobuf::Struct;
    new_top_level_struct->CopyFrom(*top_level_struct);
    top_level_struct = new_top_level_struct;
  }
  top_level_struct_ = top_level_struct;
  if (top_level_struct) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_set_allocated:google.protobuf.testing.DefaultValueTestCases.top_level_struct)
}

// optional .google.protobuf.testing.ValueMessage value_wrapper_simple = 212;
inline bool DefaultValueTestCases::has_value_wrapper_simple() const {
  return this != internal_default_instance() && value_wrapper_simple_ != NULL;
}
inline void DefaultValueTestCases::clear_value_wrapper_simple() {
  if (GetArenaNoVirtual() == NULL && value_wrapper_simple_ != NULL) delete value_wrapper_simple_;
  value_wrapper_simple_ = NULL;
}
inline const ::google::protobuf::testing::ValueMessage& DefaultValueTestCases::value_wrapper_simple() const {
  // @@protoc_insertion_point(field_get:google.protobuf.testing.DefaultValueTestCases.value_wrapper_simple)
  return value_wrapper_simple_ != NULL ? *value_wrapper_simple_
                         : *::google::protobuf::testing::ValueMessage::internal_default_instance();
}
inline ::google::protobuf::testing::ValueMessage* DefaultValueTestCases::mutable_value_wrapper_simple() {
  
  if (value_wrapper_simple_ == NULL) {
    value_wrapper_simple_ = new ::google::protobuf::testing::ValueMessage;
  }
  // @@protoc_insertion_point(field_mutable:google.protobuf.testing.DefaultValueTestCases.value_wrapper_simple)
  return value_wrapper_simple_;
}
inline ::google::protobuf::testing::ValueMessage* DefaultValueTestCases::release_value_wrapper_simple() {
  // @@protoc_insertion_point(field_release:google.protobuf.testing.DefaultValueTestCases.value_wrapper_simple)
  
  ::google::protobuf::testing::ValueMessage* temp = value_wrapper_simple_;
  value_wrapper_simple_ = NULL;
  return temp;
}
inline void DefaultValueTestCases::set_allocated_value_wrapper_simple(::google::protobuf::testing::ValueMessage* value_wrapper_simple) {
  delete value_wrapper_simple_;
  value_wrapper_simple_ = value_wrapper_simple;
  if (value_wrapper_simple) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_set_allocated:google.protobuf.testing.DefaultValueTestCases.value_wrapper_simple)
}

// optional .google.protobuf.testing.ValueMessage value_wrapper_with_struct = 213;
inline bool DefaultValueTestCases::has_value_wrapper_with_struct() const {
  return this != internal_default_instance() && value_wrapper_with_struct_ != NULL;
}
inline void DefaultValueTestCases::clear_value_wrapper_with_struct() {
  if (GetArenaNoVirtual() == NULL && value_wrapper_with_struct_ != NULL) delete value_wrapper_with_struct_;
  value_wrapper_with_struct_ = NULL;
}
inline const ::google::protobuf::testing::ValueMessage& DefaultValueTestCases::value_wrapper_with_struct() const {
  // @@protoc_insertion_point(field_get:google.protobuf.testing.DefaultValueTestCases.value_wrapper_with_struct)
  return value_wrapper_with_struct_ != NULL ? *value_wrapper_with_struct_
                         : *::google::protobuf::testing::ValueMessage::internal_default_instance();
}
inline ::google::protobuf::testing::ValueMessage* DefaultValueTestCases::mutable_value_wrapper_with_struct() {
  
  if (value_wrapper_with_struct_ == NULL) {
    value_wrapper_with_struct_ = new ::google::protobuf::testing::ValueMessage;
  }
  // @@protoc_insertion_point(field_mutable:google.protobuf.testing.DefaultValueTestCases.value_wrapper_with_struct)
  return value_wrapper_with_struct_;
}
inline ::google::protobuf::testing::ValueMessage* DefaultValueTestCases::release_value_wrapper_with_struct() {
  // @@protoc_insertion_point(field_release:google.protobuf.testing.DefaultValueTestCases.value_wrapper_with_struct)
  
  ::google::protobuf::testing::ValueMessage* temp = value_wrapper_with_struct_;
  value_wrapper_with_struct_ = NULL;
  return temp;
}
inline void DefaultValueTestCases::set_allocated_value_wrapper_with_struct(::google::protobuf::testing::ValueMessage* value_wrapper_with_struct) {
  delete value_wrapper_with_struct_;
  value_wrapper_with_struct_ = value_wrapper_with_struct;
  if (value_wrapper_with_struct) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_set_allocated:google.protobuf.testing.DefaultValueTestCases.value_wrapper_with_struct)
}

// optional .google.protobuf.testing.ValueMessage value_wrapper_with_list = 214;
inline bool DefaultValueTestCases::has_value_wrapper_with_list() const {
  return this != internal_default_instance() && value_wrapper_with_list_ != NULL;
}
inline void DefaultValueTestCases::clear_value_wrapper_with_list() {
  if (GetArenaNoVirtual() == NULL && value_wrapper_with_list_ != NULL) delete value_wrapper_with_list_;
  value_wrapper_with_list_ = NULL;
}
inline const ::google::protobuf::testing::ValueMessage& DefaultValueTestCases::value_wrapper_with_list() const {
  // @@protoc_insertion_point(field_get:google.protobuf.testing.DefaultValueTestCases.value_wrapper_with_list)
  return value_wrapper_with_list_ != NULL ? *value_wrapper_with_list_
                         : *::google::protobuf::testing::ValueMessage::internal_default_instance();
}
inline ::google::protobuf::testing::ValueMessage* DefaultValueTestCases::mutable_value_wrapper_with_list() {
  
  if (value_wrapper_with_list_ == NULL) {
    value_wrapper_with_list_ = new ::google::protobuf::testing::ValueMessage;
  }
  // @@protoc_insertion_point(field_mutable:google.protobuf.testing.DefaultValueTestCases.value_wrapper_with_list)
  return value_wrapper_with_list_;
}
inline ::google::protobuf::testing::ValueMessage* DefaultValueTestCases::release_value_wrapper_with_list() {
  // @@protoc_insertion_point(field_release:google.protobuf.testing.DefaultValueTestCases.value_wrapper_with_list)
  
  ::google::protobuf::testing::ValueMessage* temp = value_wrapper_with_list_;
  value_wrapper_with_list_ = NULL;
  return temp;
}
inline void DefaultValueTestCases::set_allocated_value_wrapper_with_list(::google::protobuf::testing::ValueMessage* value_wrapper_with_list) {
  delete value_wrapper_with_list_;
  value_wrapper_with_list_ = value_wrapper_with_list;
  if (value_wrapper_with_list) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_set_allocated:google.protobuf.testing.DefaultValueTestCases.value_wrapper_with_list)
}

// optional .google.protobuf.testing.ListValueMessage list_value_wrapper = 215;
inline bool DefaultValueTestCases::has_list_value_wrapper() const {
  return this != internal_default_instance() && list_value_wrapper_ != NULL;
}
inline void DefaultValueTestCases::clear_list_value_wrapper() {
  if (GetArenaNoVirtual() == NULL && list_value_wrapper_ != NULL) delete list_value_wrapper_;
  list_value_wrapper_ = NULL;
}
inline const ::google::protobuf::testing::ListValueMessage& DefaultValueTestCases::list_value_wrapper() const {
  // @@protoc_insertion_point(field_get:google.protobuf.testing.DefaultValueTestCases.list_value_wrapper)
  return list_value_wrapper_ != NULL ? *list_value_wrapper_
                         : *::google::protobuf::testing::ListValueMessage::internal_default_instance();
}
inline ::google::protobuf::testing::ListValueMessage* DefaultValueTestCases::mutable_list_value_wrapper() {
  
  if (list_value_wrapper_ == NULL) {
    list_value_wrapper_ = new ::google::protobuf::testing::ListValueMessage;
  }
  // @@protoc_insertion_point(field_mutable:google.protobuf.testing.DefaultValueTestCases.list_value_wrapper)
  return list_value_wrapper_;
}
inline ::google::protobuf::testing::ListValueMessage* DefaultValueTestCases::release_list_value_wrapper() {
  // @@protoc_insertion_point(field_release:google.protobuf.testing.DefaultValueTestCases.list_value_wrapper)
  
  ::google::protobuf::testing::ListValueMessage* temp = list_value_wrapper_;
  list_value_wrapper_ = NULL;
  return temp;
}
inline void DefaultValueTestCases::set_allocated_list_value_wrapper(::google::protobuf::testing::ListValueMessage* list_value_wrapper) {
  delete list_value_wrapper_;
  list_value_wrapper_ = list_value_wrapper;
  if (list_value_wrapper) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_set_allocated:google.protobuf.testing.DefaultValueTestCases.list_value_wrapper)
}

// optional .google.protobuf.Value top_level_value_simple = 216;
inline bool DefaultValueTestCases::has_top_level_value_simple() const {
  return this != internal_default_instance() && top_level_value_simple_ != NULL;
}
inline void DefaultValueTestCases::clear_top_level_value_simple() {
  if (GetArenaNoVirtual() == NULL && top_level_value_simple_ != NULL) delete top_level_value_simple_;
  top_level_value_simple_ = NULL;
}
inline const ::google::protobuf::Value& DefaultValueTestCases::top_level_value_simple() const {
  // @@protoc_insertion_point(field_get:google.protobuf.testing.DefaultValueTestCases.top_level_value_simple)
  return top_level_value_simple_ != NULL ? *top_level_value_simple_
                         : *::google::protobuf::Value::internal_default_instance();
}
inline ::google::protobuf::Value* DefaultValueTestCases::mutable_top_level_value_simple() {
  
  if (top_level_value_simple_ == NULL) {
    top_level_value_simple_ = new ::google::protobuf::Value;
  }
  // @@protoc_insertion_point(field_mutable:google.protobuf.testing.DefaultValueTestCases.top_level_value_simple)
  return top_level_value_simple_;
}
inline ::google::protobuf::Value* DefaultValueTestCases::release_top_level_value_simple() {
  // @@protoc_insertion_point(field_release:google.protobuf.testing.DefaultValueTestCases.top_level_value_simple)
  
  ::google::protobuf::Value* temp = top_level_value_simple_;
  top_level_value_simple_ = NULL;
  return temp;
}
inline void DefaultValueTestCases::set_allocated_top_level_value_simple(::google::protobuf::Value* top_level_value_simple) {
  delete top_level_value_simple_;
  if (top_level_value_simple != NULL && top_level_value_simple->GetArena() != NULL) {
    ::google::protobuf::Value* new_top_level_value_simple = new ::google::protobuf::Value;
    new_top_level_value_simple->CopyFrom(*top_level_value_simple);
    top_level_value_simple = new_top_level_value_simple;
  }
  top_level_value_simple_ = top_level_value_simple;
  if (top_level_value_simple) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_set_allocated:google.protobuf.testing.DefaultValueTestCases.top_level_value_simple)
}

// optional .google.protobuf.Value top_level_value_with_struct = 217;
inline bool DefaultValueTestCases::has_top_level_value_with_struct() const {
  return this != internal_default_instance() && top_level_value_with_struct_ != NULL;
}
inline void DefaultValueTestCases::clear_top_level_value_with_struct() {
  if (GetArenaNoVirtual() == NULL && top_level_value_with_struct_ != NULL) delete top_level_value_with_struct_;
  top_level_value_with_struct_ = NULL;
}
inline const ::google::protobuf::Value& DefaultValueTestCases::top_level_value_with_struct() const {
  // @@protoc_insertion_point(field_get:google.protobuf.testing.DefaultValueTestCases.top_level_value_with_struct)
  return top_level_value_with_struct_ != NULL ? *top_level_value_with_struct_
                         : *::google::protobuf::Value::internal_default_instance();
}
inline ::google::protobuf::Value* DefaultValueTestCases::mutable_top_level_value_with_struct() {
  
  if (top_level_value_with_struct_ == NULL) {
    top_level_value_with_struct_ = new ::google::protobuf::Value;
  }
  // @@protoc_insertion_point(field_mutable:google.protobuf.testing.DefaultValueTestCases.top_level_value_with_struct)
  return top_level_value_with_struct_;
}
inline ::google::protobuf::Value* DefaultValueTestCases::release_top_level_value_with_struct() {
  // @@protoc_insertion_point(field_release:google.protobuf.testing.DefaultValueTestCases.top_level_value_with_struct)
  
  ::google::protobuf::Value* temp = top_level_value_with_struct_;
  top_level_value_with_struct_ = NULL;
  return temp;
}
inline void DefaultValueTestCases::set_allocated_top_level_value_with_struct(::google::protobuf::Value* top_level_value_with_struct) {
  delete top_level_value_with_struct_;
  if (top_level_value_with_struct != NULL && top_level_value_with_struct->GetArena() != NULL) {
    ::google::protobuf::Value* new_top_level_value_with_struct = new ::google::protobuf::Value;
    new_top_level_value_with_struct->CopyFrom(*top_level_value_with_struct);
    top_level_value_with_struct = new_top_level_value_with_struct;
  }
  top_level_value_with_struct_ = top_level_value_with_struct;
  if (top_level_value_with_struct) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_set_allocated:google.protobuf.testing.DefaultValueTestCases.top_level_value_with_struct)
}

// optional .google.protobuf.Value top_level_value_with_list = 218;
inline bool DefaultValueTestCases::has_top_level_value_with_list() const {
  return this != internal_default_instance() && top_level_value_with_list_ != NULL;
}
inline void DefaultValueTestCases::clear_top_level_value_with_list() {
  if (GetArenaNoVirtual() == NULL && top_level_value_with_list_ != NULL) delete top_level_value_with_list_;
  top_level_value_with_list_ = NULL;
}
inline const ::google::protobuf::Value& DefaultValueTestCases::top_level_value_with_list() const {
  // @@protoc_insertion_point(field_get:google.protobuf.testing.DefaultValueTestCases.top_level_value_with_list)
  return top_level_value_with_list_ != NULL ? *top_level_value_with_list_
                         : *::google::protobuf::Value::internal_default_instance();
}
inline ::google::protobuf::Value* DefaultValueTestCases::mutable_top_level_value_with_list() {
  
  if (top_level_value_with_list_ == NULL) {
    top_level_value_with_list_ = new ::google::protobuf::Value;
  }
  // @@protoc_insertion_point(field_mutable:google.protobuf.testing.DefaultValueTestCases.top_level_value_with_list)
  return top_level_value_with_list_;
}
inline ::google::protobuf::Value* DefaultValueTestCases::release_top_level_value_with_list() {
  // @@protoc_insertion_point(field_release:google.protobuf.testing.DefaultValueTestCases.top_level_value_with_list)
  
  ::google::protobuf::Value* temp = top_level_value_with_list_;
  top_level_value_with_list_ = NULL;
  return temp;
}
inline void DefaultValueTestCases::set_allocated_top_level_value_with_list(::google::protobuf::Value* top_level_value_with_list) {
  delete top_level_value_with_list_;
  if (top_level_value_with_list != NULL && top_level_value_with_list->GetArena() != NULL) {
    ::google::protobuf::Value* new_top_level_value_with_list = new ::google::protobuf::Value;
    new_top_level_value_with_list->CopyFrom(*top_level_value_with_list);
    top_level_value_with_list = new_top_level_value_with_list;
  }
  top_level_value_with_list_ = top_level_value_with_list;
  if (top_level_value_with_list) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_set_allocated:google.protobuf.testing.DefaultValueTestCases.top_level_value_with_list)
}

// optional .google.protobuf.ListValue top_level_listvalue = 219;
inline bool DefaultValueTestCases::has_top_level_listvalue() const {
  return this != internal_default_instance() && top_level_listvalue_ != NULL;
}
inline void DefaultValueTestCases::clear_top_level_listvalue() {
  if (GetArenaNoVirtual() == NULL && top_level_listvalue_ != NULL) delete top_level_listvalue_;
  top_level_listvalue_ = NULL;
}
inline const ::google::protobuf::ListValue& DefaultValueTestCases::top_level_listvalue() const {
  // @@protoc_insertion_point(field_get:google.protobuf.testing.DefaultValueTestCases.top_level_listvalue)
  return top_level_listvalue_ != NULL ? *top_level_listvalue_
                         : *::google::protobuf::ListValue::internal_default_instance();
}
inline ::google::protobuf::ListValue* DefaultValueTestCases::mutable_top_level_listvalue() {
  
  if (top_level_listvalue_ == NULL) {
    top_level_listvalue_ = new ::google::protobuf::ListValue;
  }
  // @@protoc_insertion_point(field_mutable:google.protobuf.testing.DefaultValueTestCases.top_level_listvalue)
  return top_level_listvalue_;
}
inline ::google::protobuf::ListValue* DefaultValueTestCases::release_top_level_listvalue() {
  // @@protoc_insertion_point(field_release:google.protobuf.testing.DefaultValueTestCases.top_level_listvalue)
  
  ::google::protobuf::ListValue* temp = top_level_listvalue_;
  top_level_listvalue_ = NULL;
  return temp;
}
inline void DefaultValueTestCases::set_allocated_top_level_listvalue(::google::protobuf::ListValue* top_level_listvalue) {
  delete top_level_listvalue_;
  if (top_level_listvalue != NULL && top_level_listvalue->GetArena() != NULL) {
    ::google::protobuf::ListValue* new_top_level_listvalue = new ::google::protobuf::ListValue;
    new_top_level_listvalue->CopyFrom(*top_level_listvalue);
    top_level_listvalue = new_top_level_listvalue;
  }
  top_level_listvalue_ = top_level_listvalue;
  if (top_level_listvalue) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_set_allocated:google.protobuf.testing.DefaultValueTestCases.top_level_listvalue)
}

// optional .google.protobuf.testing.AnyMessage empty_any = 301;
inline bool DefaultValueTestCases::has_empty_any() const {
  return this != internal_default_instance() && empty_any_ != NULL;
}
inline void DefaultValueTestCases::clear_empty_any() {
  if (GetArenaNoVirtual() == NULL && empty_any_ != NULL) delete empty_any_;
  empty_any_ = NULL;
}
inline const ::google::protobuf::testing::AnyMessage& DefaultValueTestCases::empty_any() const {
  // @@protoc_insertion_point(field_get:google.protobuf.testing.DefaultValueTestCases.empty_any)
  return empty_any_ != NULL ? *empty_any_
                         : *::google::protobuf::testing::AnyMessage::internal_default_instance();
}
inline ::google::protobuf::testing::AnyMessage* DefaultValueTestCases::mutable_empty_any() {
  
  if (empty_any_ == NULL) {
    empty_any_ = new ::google::protobuf::testing::AnyMessage;
  }
  // @@protoc_insertion_point(field_mutable:google.protobuf.testing.DefaultValueTestCases.empty_any)
  return empty_any_;
}
inline ::google::protobuf::testing::AnyMessage* DefaultValueTestCases::release_empty_any() {
  // @@protoc_insertion_point(field_release:google.protobuf.testing.DefaultValueTestCases.empty_any)
  
  ::google::protobuf::testing::AnyMessage* temp = empty_any_;
  empty_any_ = NULL;
  return temp;
}
inline void DefaultValueTestCases::set_allocated_empty_any(::google::protobuf::testing::AnyMessage* empty_any) {
  delete empty_any_;
  empty_any_ = empty_any;
  if (empty_any) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_set_allocated:google.protobuf.testing.DefaultValueTestCases.empty_any)
}

// optional .google.protobuf.testing.AnyMessage type_only_any = 302;
inline bool DefaultValueTestCases::has_type_only_any() const {
  return this != internal_default_instance() && type_only_any_ != NULL;
}
inline void DefaultValueTestCases::clear_type_only_any() {
  if (GetArenaNoVirtual() == NULL && type_only_any_ != NULL) delete type_only_any_;
  type_only_any_ = NULL;
}
inline const ::google::protobuf::testing::AnyMessage& DefaultValueTestCases::type_only_any() const {
  // @@protoc_insertion_point(field_get:google.protobuf.testing.DefaultValueTestCases.type_only_any)
  return type_only_any_ != NULL ? *type_only_any_
                         : *::google::protobuf::testing::AnyMessage::internal_default_instance();
}
inline ::google::protobuf::testing::AnyMessage* DefaultValueTestCases::mutable_type_only_any() {
  
  if (type_only_any_ == NULL) {
    type_only_any_ = new ::google::protobuf::testing::AnyMessage;
  }
  // @@protoc_insertion_point(field_mutable:google.protobuf.testing.DefaultValueTestCases.type_only_any)
  return type_only_any_;
}
inline ::google::protobuf::testing::AnyMessage* DefaultValueTestCases::release_type_only_any() {
  // @@protoc_insertion_point(field_release:google.protobuf.testing.DefaultValueTestCases.type_only_any)
  
  ::google::protobuf::testing::AnyMessage* temp = type_only_any_;
  type_only_any_ = NULL;
  return temp;
}
inline void DefaultValueTestCases::set_allocated_type_only_any(::google::protobuf::testing::AnyMessage* type_only_any) {
  delete type_only_any_;
  type_only_any_ = type_only_any;
  if (type_only_any) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_set_allocated:google.protobuf.testing.DefaultValueTestCases.type_only_any)
}

// optional .google.protobuf.testing.AnyMessage recursive_any = 303;
inline bool DefaultValueTestCases::has_recursive_any() const {
  return this != internal_default_instance() && recursive_any_ != NULL;
}
inline void DefaultValueTestCases::clear_recursive_any() {
  if (GetArenaNoVirtual() == NULL && recursive_any_ != NULL) delete recursive_any_;
  recursive_any_ = NULL;
}
inline const ::google::protobuf::testing::AnyMessage& DefaultValueTestCases::recursive_any() const {
  // @@protoc_insertion_point(field_get:google.protobuf.testing.DefaultValueTestCases.recursive_any)
  return recursive_any_ != NULL ? *recursive_any_
                         : *::google::protobuf::testing::AnyMessage::internal_default_instance();
}
inline ::google::protobuf::testing::AnyMessage* DefaultValueTestCases::mutable_recursive_any() {
  
  if (recursive_any_ == NULL) {
    recursive_any_ = new ::google::protobuf::testing::AnyMessage;
  }
  // @@protoc_insertion_point(field_mutable:google.protobuf.testing.DefaultValueTestCases.recursive_any)
  return recursive_any_;
}
inline ::google::protobuf::testing::AnyMessage* DefaultValueTestCases::release_recursive_any() {
  // @@protoc_insertion_point(field_release:google.protobuf.testing.DefaultValueTestCases.recursive_any)
  
  ::google::protobuf::testing::AnyMessage* temp = recursive_any_;
  recursive_any_ = NULL;
  return temp;
}
inline void DefaultValueTestCases::set_allocated_recursive_any(::google::protobuf::testing::AnyMessage* recursive_any) {
  delete recursive_any_;
  recursive_any_ = recursive_any;
  if (recursive_any) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_set_allocated:google.protobuf.testing.DefaultValueTestCases.recursive_any)
}

// optional .google.protobuf.testing.AnyMessage any_with_message_value = 304;
inline bool DefaultValueTestCases::has_any_with_message_value() const {
  return this != internal_default_instance() && any_with_message_value_ != NULL;
}
inline void DefaultValueTestCases::clear_any_with_message_value() {
  if (GetArenaNoVirtual() == NULL && any_with_message_value_ != NULL) delete any_with_message_value_;
  any_with_message_value_ = NULL;
}
inline const ::google::protobuf::testing::AnyMessage& DefaultValueTestCases::any_with_message_value() const {
  // @@protoc_insertion_point(field_get:google.protobuf.testing.DefaultValueTestCases.any_with_message_value)
  return any_with_message_value_ != NULL ? *any_with_message_value_
                         : *::google::protobuf::testing::AnyMessage::internal_default_instance();
}
inline ::google::protobuf::testing::AnyMessage* DefaultValueTestCases::mutable_any_with_message_value() {
  
  if (any_with_message_value_ == NULL) {
    any_with_message_value_ = new ::google::protobuf::testing::AnyMessage;
  }
  // @@protoc_insertion_point(field_mutable:google.protobuf.testing.DefaultValueTestCases.any_with_message_value)
  return any_with_message_value_;
}
inline ::google::protobuf::testing::AnyMessage* DefaultValueTestCases::release_any_with_message_value() {
  // @@protoc_insertion_point(field_release:google.protobuf.testing.DefaultValueTestCases.any_with_message_value)
  
  ::google::protobuf::testing::AnyMessage* temp = any_with_message_value_;
  any_with_message_value_ = NULL;
  return temp;
}
inline void DefaultValueTestCases::set_allocated_any_with_message_value(::google::protobuf::testing::AnyMessage* any_with_message_value) {
  delete any_with_message_value_;
  any_with_message_value_ = any_with_message_value;
  if (any_with_message_value) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_set_allocated:google.protobuf.testing.DefaultValueTestCases.any_with_message_value)
}

// optional .google.protobuf.testing.AnyMessage any_with_nested_message = 305;
inline bool DefaultValueTestCases::has_any_with_nested_message() const {
  return this != internal_default_instance() && any_with_nested_message_ != NULL;
}
inline void DefaultValueTestCases::clear_any_with_nested_message() {
  if (GetArenaNoVirtual() == NULL && any_with_nested_message_ != NULL) delete any_with_nested_message_;
  any_with_nested_message_ = NULL;
}
inline const ::google::protobuf::testing::AnyMessage& DefaultValueTestCases::any_with_nested_message() const {
  // @@protoc_insertion_point(field_get:google.protobuf.testing.DefaultValueTestCases.any_with_nested_message)
  return any_with_nested_message_ != NULL ? *any_with_nested_message_
                         : *::google::protobuf::testing::AnyMessage::internal_default_instance();
}
inline ::google::protobuf::testing::AnyMessage* DefaultValueTestCases::mutable_any_with_nested_message() {
  
  if (any_with_nested_message_ == NULL) {
    any_with_nested_message_ = new ::google::protobuf::testing::AnyMessage;
  }
  // @@protoc_insertion_point(field_mutable:google.protobuf.testing.DefaultValueTestCases.any_with_nested_message)
  return any_with_nested_message_;
}
inline ::google::protobuf::testing::AnyMessage* DefaultValueTestCases::release_any_with_nested_message() {
  // @@protoc_insertion_point(field_release:google.protobuf.testing.DefaultValueTestCases.any_with_nested_message)
  
  ::google::protobuf::testing::AnyMessage* temp = any_with_nested_message_;
  any_with_nested_message_ = NULL;
  return temp;
}
inline void DefaultValueTestCases::set_allocated_any_with_nested_message(::google::protobuf::testing::AnyMessage* any_with_nested_message) {
  delete any_with_nested_message_;
  any_with_nested_message_ = any_with_nested_message;
  if (any_with_nested_message) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_set_allocated:google.protobuf.testing.DefaultValueTestCases.any_with_nested_message)
}

// optional .google.protobuf.testing.AnyMessage any_with_message_containing_map = 306;
inline bool DefaultValueTestCases::has_any_with_message_containing_map() const {
  return this != internal_default_instance() && any_with_message_containing_map_ != NULL;
}
inline void DefaultValueTestCases::clear_any_with_message_containing_map() {
  if (GetArenaNoVirtual() == NULL && any_with_message_containing_map_ != NULL) delete any_with_message_containing_map_;
  any_with_message_containing_map_ = NULL;
}
inline const ::google::protobuf::testing::AnyMessage& DefaultValueTestCases::any_with_message_containing_map() const {
  // @@protoc_insertion_point(field_get:google.protobuf.testing.DefaultValueTestCases.any_with_message_containing_map)
  return any_with_message_containing_map_ != NULL ? *any_with_message_containing_map_
                         : *::google::protobuf::testing::AnyMessage::internal_default_instance();
}
inline ::google::protobuf::testing::AnyMessage* DefaultValueTestCases::mutable_any_with_message_containing_map() {
  
  if (any_with_message_containing_map_ == NULL) {
    any_with_message_containing_map_ = new ::google::protobuf::testing::AnyMessage;
  }
  // @@protoc_insertion_point(field_mutable:google.protobuf.testing.DefaultValueTestCases.any_with_message_containing_map)
  return any_with_message_containing_map_;
}
inline ::google::protobuf::testing::AnyMessage* DefaultValueTestCases::release_any_with_message_containing_map() {
  // @@protoc_insertion_point(field_release:google.protobuf.testing.DefaultValueTestCases.any_with_message_containing_map)
  
  ::google::protobuf::testing::AnyMessage* temp = any_with_message_containing_map_;
  any_with_message_containing_map_ = NULL;
  return temp;
}
inline void DefaultValueTestCases::set_allocated_any_with_message_containing_map(::google::protobuf::testing::AnyMessage* any_with_message_containing_map) {
  delete any_with_message_containing_map_;
  any_with_message_containing_map_ = any_with_message_containing_map;
  if (any_with_message_containing_map) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_set_allocated:google.protobuf.testing.DefaultValueTestCases.any_with_message_containing_map)
}

// optional .google.protobuf.testing.AnyMessage any_with_message_containing_struct = 307;
inline bool DefaultValueTestCases::has_any_with_message_containing_struct() const {
  return this != internal_default_instance() && any_with_message_containing_struct_ != NULL;
}
inline void DefaultValueTestCases::clear_any_with_message_containing_struct() {
  if (GetArenaNoVirtual() == NULL && any_with_message_containing_struct_ != NULL) delete any_with_message_containing_struct_;
  any_with_message_containing_struct_ = NULL;
}
inline const ::google::protobuf::testing::AnyMessage& DefaultValueTestCases::any_with_message_containing_struct() const {
  // @@protoc_insertion_point(field_get:google.protobuf.testing.DefaultValueTestCases.any_with_message_containing_struct)
  return any_with_message_containing_struct_ != NULL ? *any_with_message_containing_struct_
                         : *::google::protobuf::testing::AnyMessage::internal_default_instance();
}
inline ::google::protobuf::testing::AnyMessage* DefaultValueTestCases::mutable_any_with_message_containing_struct() {
  
  if (any_with_message_containing_struct_ == NULL) {
    any_with_message_containing_struct_ = new ::google::protobuf::testing::AnyMessage;
  }
  // @@protoc_insertion_point(field_mutable:google.protobuf.testing.DefaultValueTestCases.any_with_message_containing_struct)
  return any_with_message_containing_struct_;
}
inline ::google::protobuf::testing::AnyMessage* DefaultValueTestCases::release_any_with_message_containing_struct() {
  // @@protoc_insertion_point(field_release:google.protobuf.testing.DefaultValueTestCases.any_with_message_containing_struct)
  
  ::google::protobuf::testing::AnyMessage* temp = any_with_message_containing_struct_;
  any_with_message_containing_struct_ = NULL;
  return temp;
}
inline void DefaultValueTestCases::set_allocated_any_with_message_containing_struct(::google::protobuf::testing::AnyMessage* any_with_message_containing_struct) {
  delete any_with_message_containing_struct_;
  any_with_message_containing_struct_ = any_with_message_containing_struct;
  if (any_with_message_containing_struct) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_set_allocated:google.protobuf.testing.DefaultValueTestCases.any_with_message_containing_struct)
}

// optional .google.protobuf.Any top_level_any = 308;
inline bool DefaultValueTestCases::has_top_level_any() const {
  return this != internal_default_instance() && top_level_any_ != NULL;
}
inline void DefaultValueTestCases::clear_top_level_any() {
  if (GetArenaNoVirtual() == NULL && top_level_any_ != NULL) delete top_level_any_;
  top_level_any_ = NULL;
}
inline const ::google::protobuf::Any& DefaultValueTestCases::top_level_any() const {
  // @@protoc_insertion_point(field_get:google.protobuf.testing.DefaultValueTestCases.top_level_any)
  return top_level_any_ != NULL ? *top_level_any_
                         : *::google::protobuf::Any::internal_default_instance();
}
inline ::google::protobuf::Any* DefaultValueTestCases::mutable_top_level_any() {
  
  if (top_level_any_ == NULL) {
    top_level_any_ = new ::google::protobuf::Any;
  }
  // @@protoc_insertion_point(field_mutable:google.protobuf.testing.DefaultValueTestCases.top_level_any)
  return top_level_any_;
}
inline ::google::protobuf::Any* DefaultValueTestCases::release_top_level_any() {
  // @@protoc_insertion_point(field_release:google.protobuf.testing.DefaultValueTestCases.top_level_any)
  
  ::google::protobuf::Any* temp = top_level_any_;
  top_level_any_ = NULL;
  return temp;
}
inline void DefaultValueTestCases::set_allocated_top_level_any(::google::protobuf::Any* top_level_any) {
  delete top_level_any_;
  top_level_any_ = top_level_any;
  if (top_level_any) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_set_allocated:google.protobuf.testing.DefaultValueTestCases.top_level_any)
}

// optional .google.protobuf.testing.StringtoIntMap empty_map = 401;
inline bool DefaultValueTestCases::has_empty_map() const {
  return this != internal_default_instance() && empty_map_ != NULL;
}
inline void DefaultValueTestCases::clear_empty_map() {
  if (GetArenaNoVirtual() == NULL && empty_map_ != NULL) delete empty_map_;
  empty_map_ = NULL;
}
inline const ::google::protobuf::testing::StringtoIntMap& DefaultValueTestCases::empty_map() const {
  // @@protoc_insertion_point(field_get:google.protobuf.testing.DefaultValueTestCases.empty_map)
  return empty_map_ != NULL ? *empty_map_
                         : *::google::protobuf::testing::StringtoIntMap::internal_default_instance();
}
inline ::google::protobuf::testing::StringtoIntMap* DefaultValueTestCases::mutable_empty_map() {
  
  if (empty_map_ == NULL) {
    empty_map_ = new ::google::protobuf::testing::StringtoIntMap;
  }
  // @@protoc_insertion_point(field_mutable:google.protobuf.testing.DefaultValueTestCases.empty_map)
  return empty_map_;
}
inline ::google::protobuf::testing::StringtoIntMap* DefaultValueTestCases::release_empty_map() {
  // @@protoc_insertion_point(field_release:google.protobuf.testing.DefaultValueTestCases.empty_map)
  
  ::google::protobuf::testing::StringtoIntMap* temp = empty_map_;
  empty_map_ = NULL;
  return temp;
}
inline void DefaultValueTestCases::set_allocated_empty_map(::google::protobuf::testing::StringtoIntMap* empty_map) {
  delete empty_map_;
  empty_map_ = empty_map;
  if (empty_map) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_set_allocated:google.protobuf.testing.DefaultValueTestCases.empty_map)
}

// optional .google.protobuf.testing.StringtoIntMap string_to_int = 402;
inline bool DefaultValueTestCases::has_string_to_int() const {
  return this != internal_default_instance() && string_to_int_ != NULL;
}
inline void DefaultValueTestCases::clear_string_to_int() {
  if (GetArenaNoVirtual() == NULL && string_to_int_ != NULL) delete string_to_int_;
  string_to_int_ = NULL;
}
inline const ::google::protobuf::testing::StringtoIntMap& DefaultValueTestCases::string_to_int() const {
  // @@protoc_insertion_point(field_get:google.protobuf.testing.DefaultValueTestCases.string_to_int)
  return string_to_int_ != NULL ? *string_to_int_
                         : *::google::protobuf::testing::StringtoIntMap::internal_default_instance();
}
inline ::google::protobuf::testing::StringtoIntMap* DefaultValueTestCases::mutable_string_to_int() {
  
  if (string_to_int_ == NULL) {
    string_to_int_ = new ::google::protobuf::testing::StringtoIntMap;
  }
  // @@protoc_insertion_point(field_mutable:google.protobuf.testing.DefaultValueTestCases.string_to_int)
  return string_to_int_;
}
inline ::google::protobuf::testing::StringtoIntMap* DefaultValueTestCases::release_string_to_int() {
  // @@protoc_insertion_point(field_release:google.protobuf.testing.DefaultValueTestCases.string_to_int)
  
  ::google::protobuf::testing::StringtoIntMap* temp = string_to_int_;
  string_to_int_ = NULL;
  return temp;
}
inline void DefaultValueTestCases::set_allocated_string_to_int(::google::protobuf::testing::StringtoIntMap* string_to_int) {
  delete string_to_int_;
  string_to_int_ = string_to_int;
  if (string_to_int) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_set_allocated:google.protobuf.testing.DefaultValueTestCases.string_to_int)
}

// optional .google.protobuf.testing.IntToStringMap int_to_string = 403;
inline bool DefaultValueTestCases::has_int_to_string() const {
  return this != internal_default_instance() && int_to_string_ != NULL;
}
inline void DefaultValueTestCases::clear_int_to_string() {
  if (GetArenaNoVirtual() == NULL && int_to_string_ != NULL) delete int_to_string_;
  int_to_string_ = NULL;
}
inline const ::google::protobuf::testing::IntToStringMap& DefaultValueTestCases::int_to_string() const {
  // @@protoc_insertion_point(field_get:google.protobuf.testing.DefaultValueTestCases.int_to_string)
  return int_to_string_ != NULL ? *int_to_string_
                         : *::google::protobuf::testing::IntToStringMap::internal_default_instance();
}
inline ::google::protobuf::testing::IntToStringMap* DefaultValueTestCases::mutable_int_to_string() {
  
  if (int_to_string_ == NULL) {
    int_to_string_ = new ::google::protobuf::testing::IntToStringMap;
  }
  // @@protoc_insertion_point(field_mutable:google.protobuf.testing.DefaultValueTestCases.int_to_string)
  return int_to_string_;
}
inline ::google::protobuf::testing::IntToStringMap* DefaultValueTestCases::release_int_to_string() {
  // @@protoc_insertion_point(field_release:google.protobuf.testing.DefaultValueTestCases.int_to_string)
  
  ::google::protobuf::testing::IntToStringMap* temp = int_to_string_;
  int_to_string_ = NULL;
  return temp;
}
inline void DefaultValueTestCases::set_allocated_int_to_string(::google::protobuf::testing::IntToStringMap* int_to_string) {
  delete int_to_string_;
  int_to_string_ = int_to_string;
  if (int_to_string) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_set_allocated:google.protobuf.testing.DefaultValueTestCases.int_to_string)
}

// optional .google.protobuf.testing.MixedMap mixed1 = 404;
inline bool DefaultValueTestCases::has_mixed1() const {
  return this != internal_default_instance() && mixed1_ != NULL;
}
inline void DefaultValueTestCases::clear_mixed1() {
  if (GetArenaNoVirtual() == NULL && mixed1_ != NULL) delete mixed1_;
  mixed1_ = NULL;
}
inline const ::google::protobuf::testing::MixedMap& DefaultValueTestCases::mixed1() const {
  // @@protoc_insertion_point(field_get:google.protobuf.testing.DefaultValueTestCases.mixed1)
  return mixed1_ != NULL ? *mixed1_
                         : *::google::protobuf::testing::MixedMap::internal_default_instance();
}
inline ::google::protobuf::testing::MixedMap* DefaultValueTestCases::mutable_mixed1() {
  
  if (mixed1_ == NULL) {
    mixed1_ = new ::google::protobuf::testing::MixedMap;
  }
  // @@protoc_insertion_point(field_mutable:google.protobuf.testing.DefaultValueTestCases.mixed1)
  return mixed1_;
}
inline ::google::protobuf::testing::MixedMap* DefaultValueTestCases::release_mixed1() {
  // @@protoc_insertion_point(field_release:google.protobuf.testing.DefaultValueTestCases.mixed1)
  
  ::google::protobuf::testing::MixedMap* temp = mixed1_;
  mixed1_ = NULL;
  return temp;
}
inline void DefaultValueTestCases::set_allocated_mixed1(::google::protobuf::testing::MixedMap* mixed1) {
  delete mixed1_;
  mixed1_ = mixed1;
  if (mixed1) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_set_allocated:google.protobuf.testing.DefaultValueTestCases.mixed1)
}

// optional .google.protobuf.testing.MixedMap2 mixed2 = 405;
inline bool DefaultValueTestCases::has_mixed2() const {
  return this != internal_default_instance() && mixed2_ != NULL;
}
inline void DefaultValueTestCases::clear_mixed2() {
  if (GetArenaNoVirtual() == NULL && mixed2_ != NULL) delete mixed2_;
  mixed2_ = NULL;
}
inline const ::google::protobuf::testing::MixedMap2& DefaultValueTestCases::mixed2() const {
  // @@protoc_insertion_point(field_get:google.protobuf.testing.DefaultValueTestCases.mixed2)
  return mixed2_ != NULL ? *mixed2_
                         : *::google::protobuf::testing::MixedMap2::internal_default_instance();
}
inline ::google::protobuf::testing::MixedMap2* DefaultValueTestCases::mutable_mixed2() {
  
  if (mixed2_ == NULL) {
    mixed2_ = new ::google::protobuf::testing::MixedMap2;
  }
  // @@protoc_insertion_point(field_mutable:google.protobuf.testing.DefaultValueTestCases.mixed2)
  return mixed2_;
}
inline ::google::protobuf::testing::MixedMap2* DefaultValueTestCases::release_mixed2() {
  // @@protoc_insertion_point(field_release:google.protobuf.testing.DefaultValueTestCases.mixed2)
  
  ::google::protobuf::testing::MixedMap2* temp = mixed2_;
  mixed2_ = NULL;
  return temp;
}
inline void DefaultValueTestCases::set_allocated_mixed2(::google::protobuf::testing::MixedMap2* mixed2) {
  delete mixed2_;
  mixed2_ = mixed2;
  if (mixed2) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_set_allocated:google.protobuf.testing.DefaultValueTestCases.mixed2)
}

// optional .google.protobuf.testing.MixedMap2 empty_mixed2 = 406;
inline bool DefaultValueTestCases::has_empty_mixed2() const {
  return this != internal_default_instance() && empty_mixed2_ != NULL;
}
inline void DefaultValueTestCases::clear_empty_mixed2() {
  if (GetArenaNoVirtual() == NULL && empty_mixed2_ != NULL) delete empty_mixed2_;
  empty_mixed2_ = NULL;
}
inline const ::google::protobuf::testing::MixedMap2& DefaultValueTestCases::empty_mixed2() const {
  // @@protoc_insertion_point(field_get:google.protobuf.testing.DefaultValueTestCases.empty_mixed2)
  return empty_mixed2_ != NULL ? *empty_mixed2_
                         : *::google::protobuf::testing::MixedMap2::internal_default_instance();
}
inline ::google::protobuf::testing::MixedMap2* DefaultValueTestCases::mutable_empty_mixed2() {
  
  if (empty_mixed2_ == NULL) {
    empty_mixed2_ = new ::google::protobuf::testing::MixedMap2;
  }
  // @@protoc_insertion_point(field_mutable:google.protobuf.testing.DefaultValueTestCases.empty_mixed2)
  return empty_mixed2_;
}
inline ::google::protobuf::testing::MixedMap2* DefaultValueTestCases::release_empty_mixed2() {
  // @@protoc_insertion_point(field_release:google.protobuf.testing.DefaultValueTestCases.empty_mixed2)
  
  ::google::protobuf::testing::MixedMap2* temp = empty_mixed2_;
  empty_mixed2_ = NULL;
  return temp;
}
inline void DefaultValueTestCases::set_allocated_empty_mixed2(::google::protobuf::testing::MixedMap2* empty_mixed2) {
  delete empty_mixed2_;
  empty_mixed2_ = empty_mixed2;
  if (empty_mixed2) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_set_allocated:google.protobuf.testing.DefaultValueTestCases.empty_mixed2)
}

// optional .google.protobuf.testing.MessageMap map_of_objects = 407;
inline bool DefaultValueTestCases::has_map_of_objects() const {
  return this != internal_default_instance() && map_of_objects_ != NULL;
}
inline void DefaultValueTestCases::clear_map_of_objects() {
  if (GetArenaNoVirtual() == NULL && map_of_objects_ != NULL) delete map_of_objects_;
  map_of_objects_ = NULL;
}
inline const ::google::protobuf::testing::MessageMap& DefaultValueTestCases::map_of_objects() const {
  // @@protoc_insertion_point(field_get:google.protobuf.testing.DefaultValueTestCases.map_of_objects)
  return map_of_objects_ != NULL ? *map_of_objects_
                         : *::google::protobuf::testing::MessageMap::internal_default_instance();
}
inline ::google::protobuf::testing::MessageMap* DefaultValueTestCases::mutable_map_of_objects() {
  
  if (map_of_objects_ == NULL) {
    map_of_objects_ = new ::google::protobuf::testing::MessageMap;
  }
  // @@protoc_insertion_point(field_mutable:google.protobuf.testing.DefaultValueTestCases.map_of_objects)
  return map_of_objects_;
}
inline ::google::protobuf::testing::MessageMap* DefaultValueTestCases::release_map_of_objects() {
  // @@protoc_insertion_point(field_release:google.protobuf.testing.DefaultValueTestCases.map_of_objects)
  
  ::google::protobuf::testing::MessageMap* temp = map_of_objects_;
  map_of_objects_ = NULL;
  return temp;
}
inline void DefaultValueTestCases::set_allocated_map_of_objects(::google::protobuf::testing::MessageMap* map_of_objects) {
  delete map_of_objects_;
  map_of_objects_ = map_of_objects;
  if (map_of_objects) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_set_allocated:google.protobuf.testing.DefaultValueTestCases.map_of_objects)
}

// optional .google.protobuf.testing.MixedMap mixed_empty = 408;
inline bool DefaultValueTestCases::has_mixed_empty() const {
  return this != internal_default_instance() && mixed_empty_ != NULL;
}
inline void DefaultValueTestCases::clear_mixed_empty() {
  if (GetArenaNoVirtual() == NULL && mixed_empty_ != NULL) delete mixed_empty_;
  mixed_empty_ = NULL;
}
inline const ::google::protobuf::testing::MixedMap& DefaultValueTestCases::mixed_empty() const {
  // @@protoc_insertion_point(field_get:google.protobuf.testing.DefaultValueTestCases.mixed_empty)
  return mixed_empty_ != NULL ? *mixed_empty_
                         : *::google::protobuf::testing::MixedMap::internal_default_instance();
}
inline ::google::protobuf::testing::MixedMap* DefaultValueTestCases::mutable_mixed_empty() {
  
  if (mixed_empty_ == NULL) {
    mixed_empty_ = new ::google::protobuf::testing::MixedMap;
  }
  // @@protoc_insertion_point(field_mutable:google.protobuf.testing.DefaultValueTestCases.mixed_empty)
  return mixed_empty_;
}
inline ::google::protobuf::testing::MixedMap* DefaultValueTestCases::release_mixed_empty() {
  // @@protoc_insertion_point(field_release:google.protobuf.testing.DefaultValueTestCases.mixed_empty)
  
  ::google::protobuf::testing::MixedMap* temp = mixed_empty_;
  mixed_empty_ = NULL;
  return temp;
}
inline void DefaultValueTestCases::set_allocated_mixed_empty(::google::protobuf::testing::MixedMap* mixed_empty) {
  delete mixed_empty_;
  mixed_empty_ = mixed_empty;
  if (mixed_empty) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_set_allocated:google.protobuf.testing.DefaultValueTestCases.mixed_empty)
}

// optional .google.protobuf.testing.MessageMap message_map_empty = 409;
inline bool DefaultValueTestCases::has_message_map_empty() const {
  return this != internal_default_instance() && message_map_empty_ != NULL;
}
inline void DefaultValueTestCases::clear_message_map_empty() {
  if (GetArenaNoVirtual() == NULL && message_map_empty_ != NULL) delete message_map_empty_;
  message_map_empty_ = NULL;
}
inline const ::google::protobuf::testing::MessageMap& DefaultValueTestCases::message_map_empty() const {
  // @@protoc_insertion_point(field_get:google.protobuf.testing.DefaultValueTestCases.message_map_empty)
  return message_map_empty_ != NULL ? *message_map_empty_
                         : *::google::protobuf::testing::MessageMap::internal_default_instance();
}
inline ::google::protobuf::testing::MessageMap* DefaultValueTestCases::mutable_message_map_empty() {
  
  if (message_map_empty_ == NULL) {
    message_map_empty_ = new ::google::protobuf::testing::MessageMap;
  }
  // @@protoc_insertion_point(field_mutable:google.protobuf.testing.DefaultValueTestCases.message_map_empty)
  return message_map_empty_;
}
inline ::google::protobuf::testing::MessageMap* DefaultValueTestCases::release_message_map_empty() {
  // @@protoc_insertion_point(field_release:google.protobuf.testing.DefaultValueTestCases.message_map_empty)
  
  ::google::protobuf::testing::MessageMap* temp = message_map_empty_;
  message_map_empty_ = NULL;
  return temp;
}
inline void DefaultValueTestCases::set_allocated_message_map_empty(::google::protobuf::testing::MessageMap* message_map_empty) {
  delete message_map_empty_;
  message_map_empty_ = message_map_empty;
  if (message_map_empty) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_set_allocated:google.protobuf.testing.DefaultValueTestCases.message_map_empty)
}

// optional .google.protobuf.testing.DoubleValueMessage double_value = 501;
inline bool DefaultValueTestCases::has_double_value() const {
  return this != internal_default_instance() && double_value_ != NULL;
}
inline void DefaultValueTestCases::clear_double_value() {
  if (GetArenaNoVirtual() == NULL && double_value_ != NULL) delete double_value_;
  double_value_ = NULL;
}
inline const ::google::protobuf::testing::DoubleValueMessage& DefaultValueTestCases::double_value() const {
  // @@protoc_insertion_point(field_get:google.protobuf.testing.DefaultValueTestCases.double_value)
  return double_value_ != NULL ? *double_value_
                         : *::google::protobuf::testing::DoubleValueMessage::internal_default_instance();
}
inline ::google::protobuf::testing::DoubleValueMessage* DefaultValueTestCases::mutable_double_value() {
  
  if (double_value_ == NULL) {
    double_value_ = new ::google::protobuf::testing::DoubleValueMessage;
  }
  // @@protoc_insertion_point(field_mutable:google.protobuf.testing.DefaultValueTestCases.double_value)
  return double_value_;
}
inline ::google::protobuf::testing::DoubleValueMessage* DefaultValueTestCases::release_double_value() {
  // @@protoc_insertion_point(field_release:google.protobuf.testing.DefaultValueTestCases.double_value)
  
  ::google::protobuf::testing::DoubleValueMessage* temp = double_value_;
  double_value_ = NULL;
  return temp;
}
inline void DefaultValueTestCases::set_allocated_double_value(::google::protobuf::testing::DoubleValueMessage* double_value) {
  delete double_value_;
  double_value_ = double_value;
  if (double_value) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_set_allocated:google.protobuf.testing.DefaultValueTestCases.double_value)
}

// optional .google.protobuf.testing.DoubleValueMessage double_value_default = 502;
inline bool DefaultValueTestCases::has_double_value_default() const {
  return this != internal_default_instance() && double_value_default_ != NULL;
}
inline void DefaultValueTestCases::clear_double_value_default() {
  if (GetArenaNoVirtual() == NULL && double_value_default_ != NULL) delete double_value_default_;
  double_value_default_ = NULL;
}
inline const ::google::protobuf::testing::DoubleValueMessage& DefaultValueTestCases::double_value_default() const {
  // @@protoc_insertion_point(field_get:google.protobuf.testing.DefaultValueTestCases.double_value_default)
  return double_value_default_ != NULL ? *double_value_default_
                         : *::google::protobuf::testing::DoubleValueMessage::internal_default_instance();
}
inline ::google::protobuf::testing::DoubleValueMessage* DefaultValueTestCases::mutable_double_value_default() {
  
  if (double_value_default_ == NULL) {
    double_value_default_ = new ::google::protobuf::testing::DoubleValueMessage;
  }
  // @@protoc_insertion_point(field_mutable:google.protobuf.testing.DefaultValueTestCases.double_value_default)
  return double_value_default_;
}
inline ::google::protobuf::testing::DoubleValueMessage* DefaultValueTestCases::release_double_value_default() {
  // @@protoc_insertion_point(field_release:google.protobuf.testing.DefaultValueTestCases.double_value_default)
  
  ::google::protobuf::testing::DoubleValueMessage* temp = double_value_default_;
  double_value_default_ = NULL;
  return temp;
}
inline void DefaultValueTestCases::set_allocated_double_value_default(::google::protobuf::testing::DoubleValueMessage* double_value_default) {
  delete double_value_default_;
  double_value_default_ = double_value_default;
  if (double_value_default) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_set_allocated:google.protobuf.testing.DefaultValueTestCases.double_value_default)
}

inline const DefaultValueTestCases* DefaultValueTestCases::internal_default_instance() {
  return &DefaultValueTestCases_default_instance_.get();
}
// -------------------------------------------------------------------

// DoubleMessage

// optional double double_value = 1;
inline void DoubleMessage::clear_double_value() {
  double_value_ = 0;
}
inline double DoubleMessage::double_value() const {
  // @@protoc_insertion_point(field_get:google.protobuf.testing.DoubleMessage.double_value)
  return double_value_;
}
inline void DoubleMessage::set_double_value(double value) {
  
  double_value_ = value;
  // @@protoc_insertion_point(field_set:google.protobuf.testing.DoubleMessage.double_value)
}

// repeated double repeated_double = 2;
inline int DoubleMessage::repeated_double_size() const {
  return repeated_double_.size();
}
inline void DoubleMessage::clear_repeated_double() {
  repeated_double_.Clear();
}
inline double DoubleMessage::repeated_double(int index) const {
  // @@protoc_insertion_point(field_get:google.protobuf.testing.DoubleMessage.repeated_double)
  return repeated_double_.Get(index);
}
inline void DoubleMessage::set_repeated_double(int index, double value) {
  repeated_double_.Set(index, value);
  // @@protoc_insertion_point(field_set:google.protobuf.testing.DoubleMessage.repeated_double)
}
inline void DoubleMessage::add_repeated_double(double value) {
  repeated_double_.Add(value);
  // @@protoc_insertion_point(field_add:google.protobuf.testing.DoubleMessage.repeated_double)
}
inline const ::google::protobuf::RepeatedField< double >&
DoubleMessage::repeated_double() const {
  // @@protoc_insertion_point(field_list:google.protobuf.testing.DoubleMessage.repeated_double)
  return repeated_double_;
}
inline ::google::protobuf::RepeatedField< double >*
DoubleMessage::mutable_repeated_double() {
  // @@protoc_insertion_point(field_mutable_list:google.protobuf.testing.DoubleMessage.repeated_double)
  return &repeated_double_;
}

// optional .google.protobuf.testing.DoubleMessage nested_message = 3;
inline bool DoubleMessage::has_nested_message() const {
  return this != internal_default_instance() && nested_message_ != NULL;
}
inline void DoubleMessage::clear_nested_message() {
  if (GetArenaNoVirtual() == NULL && nested_message_ != NULL) delete nested_message_;
  nested_message_ = NULL;
}
inline const ::google::protobuf::testing::DoubleMessage& DoubleMessage::nested_message() const {
  // @@protoc_insertion_point(field_get:google.protobuf.testing.DoubleMessage.nested_message)
  return nested_message_ != NULL ? *nested_message_
                         : *::google::protobuf::testing::DoubleMessage::internal_default_instance();
}
inline ::google::protobuf::testing::DoubleMessage* DoubleMessage::mutable_nested_message() {
  
  if (nested_message_ == NULL) {
    nested_message_ = new ::google::protobuf::testing::DoubleMessage;
  }
  // @@protoc_insertion_point(field_mutable:google.protobuf.testing.DoubleMessage.nested_message)
  return nested_message_;
}
inline ::google::protobuf::testing::DoubleMessage* DoubleMessage::release_nested_message() {
  // @@protoc_insertion_point(field_release:google.protobuf.testing.DoubleMessage.nested_message)
  
  ::google::protobuf::testing::DoubleMessage* temp = nested_message_;
  nested_message_ = NULL;
  return temp;
}
inline void DoubleMessage::set_allocated_nested_message(::google::protobuf::testing::DoubleMessage* nested_message) {
  delete nested_message_;
  nested_message_ = nested_message;
  if (nested_message) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_set_allocated:google.protobuf.testing.DoubleMessage.nested_message)
}

// repeated .google.protobuf.testing.DoubleMessage repeated_nested_message = 4;
inline int DoubleMessage::repeated_nested_message_size() const {
  return repeated_nested_message_.size();
}
inline void DoubleMessage::clear_repeated_nested_message() {
  repeated_nested_message_.Clear();
}
inline const ::google::protobuf::testing::DoubleMessage& DoubleMessage::repeated_nested_message(int index) const {
  // @@protoc_insertion_point(field_get:google.protobuf.testing.DoubleMessage.repeated_nested_message)
  return repeated_nested_message_.Get(index);
}
inline ::google::protobuf::testing::DoubleMessage* DoubleMessage::mutable_repeated_nested_message(int index) {
  // @@protoc_insertion_point(field_mutable:google.protobuf.testing.DoubleMessage.repeated_nested_message)
  return repeated_nested_message_.Mutable(index);
}
inline ::google::protobuf::testing::DoubleMessage* DoubleMessage::add_repeated_nested_message() {
  // @@protoc_insertion_point(field_add:google.protobuf.testing.DoubleMessage.repeated_nested_message)
  return repeated_nested_message_.Add();
}
inline ::google::protobuf::RepeatedPtrField< ::google::protobuf::testing::DoubleMessage >*
DoubleMessage::mutable_repeated_nested_message() {
  // @@protoc_insertion_point(field_mutable_list:google.protobuf.testing.DoubleMessage.repeated_nested_message)
  return &repeated_nested_message_;
}
inline const ::google::protobuf::RepeatedPtrField< ::google::protobuf::testing::DoubleMessage >&
DoubleMessage::repeated_nested_message() const {
  // @@protoc_insertion_point(field_list:google.protobuf.testing.DoubleMessage.repeated_nested_message)
  return repeated_nested_message_;
}

// optional .google.protobuf.DoubleValue double_wrapper = 100;
inline bool DoubleMessage::has_double_wrapper() const {
  return this != internal_default_instance() && double_wrapper_ != NULL;
}
inline void DoubleMessage::clear_double_wrapper() {
  if (GetArenaNoVirtual() == NULL && double_wrapper_ != NULL) delete double_wrapper_;
  double_wrapper_ = NULL;
}
inline const ::google::protobuf::DoubleValue& DoubleMessage::double_wrapper() const {
  // @@protoc_insertion_point(field_get:google.protobuf.testing.DoubleMessage.double_wrapper)
  return double_wrapper_ != NULL ? *double_wrapper_
                         : *::google::protobuf::DoubleValue::internal_default_instance();
}
inline ::google::protobuf::DoubleValue* DoubleMessage::mutable_double_wrapper() {
  
  if (double_wrapper_ == NULL) {
    double_wrapper_ = new ::google::protobuf::DoubleValue;
  }
  // @@protoc_insertion_point(field_mutable:google.protobuf.testing.DoubleMessage.double_wrapper)
  return double_wrapper_;
}
inline ::google::protobuf::DoubleValue* DoubleMessage::release_double_wrapper() {
  // @@protoc_insertion_point(field_release:google.protobuf.testing.DoubleMessage.double_wrapper)
  
  ::google::protobuf::DoubleValue* temp = double_wrapper_;
  double_wrapper_ = NULL;
  return temp;
}
inline void DoubleMessage::set_allocated_double_wrapper(::google::protobuf::DoubleValue* double_wrapper) {
  delete double_wrapper_;
  if (double_wrapper != NULL && double_wrapper->GetArena() != NULL) {
    ::google::protobuf::DoubleValue* new_double_wrapper = new ::google::protobuf::DoubleValue;
    new_double_wrapper->CopyFrom(*double_wrapper);
    double_wrapper = new_double_wrapper;
  }
  double_wrapper_ = double_wrapper;
  if (double_wrapper) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_set_allocated:google.protobuf.testing.DoubleMessage.double_wrapper)
}

// optional string str_value = 112;
inline bool DoubleMessage::has_str_value() const {
  return value_case() == kStrValue;
}
inline void DoubleMessage::set_has_str_value() {
  _oneof_case_[0] = kStrValue;
}
inline void DoubleMessage::clear_str_value() {
  if (has_str_value()) {
    value_.str_value_.DestroyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
    clear_has_value();
  }
}
inline const ::std::string& DoubleMessage::str_value() const {
  // @@protoc_insertion_point(field_get:google.protobuf.testing.DoubleMessage.str_value)
  if (has_str_value()) {
    return value_.str_value_.GetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  }
  return *&::google::protobuf::internal::GetEmptyStringAlreadyInited();
}
inline void DoubleMessage::set_str_value(const ::std::string& value) {
  // @@protoc_insertion_point(field_set:google.protobuf.testing.DoubleMessage.str_value)
  if (!has_str_value()) {
    clear_value();
    set_has_str_value();
    value_.str_value_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  }
  value_.str_value_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:google.protobuf.testing.DoubleMessage.str_value)
}
inline void DoubleMessage::set_str_value(const char* value) {
  if (!has_str_value()) {
    clear_value();
    set_has_str_value();
    value_.str_value_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  }
  value_.str_value_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(value));
  // @@protoc_insertion_point(field_set_char:google.protobuf.testing.DoubleMessage.str_value)
}
inline void DoubleMessage::set_str_value(const char* value, size_t size) {
  if (!has_str_value()) {
    clear_value();
    set_has_str_value();
    value_.str_value_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  }
  value_.str_value_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(
      reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:google.protobuf.testing.DoubleMessage.str_value)
}
inline ::std::string* DoubleMessage::mutable_str_value() {
  if (!has_str_value()) {
    clear_value();
    set_has_str_value();
    value_.str_value_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  }
  // @@protoc_insertion_point(field_mutable:google.protobuf.testing.DoubleMessage.str_value)
  return value_.str_value_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline ::std::string* DoubleMessage::release_str_value() {
  // @@protoc_insertion_point(field_release:google.protobuf.testing.DoubleMessage.str_value)
  if (has_str_value()) {
    clear_has_value();
    return value_.str_value_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  } else {
    return NULL;
  }
}
inline void DoubleMessage::set_allocated_str_value(::std::string* str_value) {
  if (!has_str_value()) {
    value_.str_value_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  }
  clear_value();
  if (str_value != NULL) {
    set_has_str_value();
    value_.str_value_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
        str_value);
  }
  // @@protoc_insertion_point(field_set_allocated:google.protobuf.testing.DoubleMessage.str_value)
}

// optional int64 num_value = 113;
inline bool DoubleMessage::has_num_value() const {
  return value_case() == kNumValue;
}
inline void DoubleMessage::set_has_num_value() {
  _oneof_case_[0] = kNumValue;
}
inline void DoubleMessage::clear_num_value() {
  if (has_num_value()) {
    value_.num_value_ = GOOGLE_LONGLONG(0);
    clear_has_value();
  }
}
inline ::google::protobuf::int64 DoubleMessage::num_value() const {
  // @@protoc_insertion_point(field_get:google.protobuf.testing.DoubleMessage.num_value)
  if (has_num_value()) {
    return value_.num_value_;
  }
  return GOOGLE_LONGLONG(0);
}
inline void DoubleMessage::set_num_value(::google::protobuf::int64 value) {
  if (!has_num_value()) {
    clear_value();
    set_has_num_value();
  }
  value_.num_value_ = value;
  // @@protoc_insertion_point(field_set:google.protobuf.testing.DoubleMessage.num_value)
}

inline bool DoubleMessage::has_value() const {
  return value_case() != VALUE_NOT_SET;
}
inline void DoubleMessage::clear_has_value() {
  _oneof_case_[0] = VALUE_NOT_SET;
}
inline DoubleMessage::ValueCase DoubleMessage::value_case() const {
  return DoubleMessage::ValueCase(_oneof_case_[0]);
}
inline const DoubleMessage* DoubleMessage::internal_default_instance() {
  return &DoubleMessage_default_instance_.get();
}
// -------------------------------------------------------------------

// StructMessage

// optional .google.protobuf.Struct struct = 1;
inline bool StructMessage::has_struct_() const {
  return this != internal_default_instance() && struct__ != NULL;
}
inline void StructMessage::clear_struct_() {
  if (GetArenaNoVirtual() == NULL && struct__ != NULL) delete struct__;
  struct__ = NULL;
}
inline const ::google::protobuf::Struct& StructMessage::struct_() const {
  // @@protoc_insertion_point(field_get:google.protobuf.testing.StructMessage.struct)
  return struct__ != NULL ? *struct__
                         : *::google::protobuf::Struct::internal_default_instance();
}
inline ::google::protobuf::Struct* StructMessage::mutable_struct_() {
  
  if (struct__ == NULL) {
    struct__ = new ::google::protobuf::Struct;
  }
  // @@protoc_insertion_point(field_mutable:google.protobuf.testing.StructMessage.struct)
  return struct__;
}
inline ::google::protobuf::Struct* StructMessage::release_struct_() {
  // @@protoc_insertion_point(field_release:google.protobuf.testing.StructMessage.struct)
  
  ::google::protobuf::Struct* temp = struct__;
  struct__ = NULL;
  return temp;
}
inline void StructMessage::set_allocated_struct_(::google::protobuf::Struct* struct_) {
  delete struct__;
  if (struct_ != NULL && struct_->GetArena() != NULL) {
    ::google::protobuf::Struct* new_struct_ = new ::google::protobuf::Struct;
    new_struct_->CopyFrom(*struct_);
    struct_ = new_struct_;
  }
  struct__ = struct_;
  if (struct_) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_set_allocated:google.protobuf.testing.StructMessage.struct)
}

inline const StructMessage* StructMessage::internal_default_instance() {
  return &StructMessage_default_instance_.get();
}
// -------------------------------------------------------------------

// ValueMessage

// optional .google.protobuf.Value value = 1;
inline bool ValueMessage::has_value() const {
  return this != internal_default_instance() && value_ != NULL;
}
inline void ValueMessage::clear_value() {
  if (GetArenaNoVirtual() == NULL && value_ != NULL) delete value_;
  value_ = NULL;
}
inline const ::google::protobuf::Value& ValueMessage::value() const {
  // @@protoc_insertion_point(field_get:google.protobuf.testing.ValueMessage.value)
  return value_ != NULL ? *value_
                         : *::google::protobuf::Value::internal_default_instance();
}
inline ::google::protobuf::Value* ValueMessage::mutable_value() {
  
  if (value_ == NULL) {
    value_ = new ::google::protobuf::Value;
  }
  // @@protoc_insertion_point(field_mutable:google.protobuf.testing.ValueMessage.value)
  return value_;
}
inline ::google::protobuf::Value* ValueMessage::release_value() {
  // @@protoc_insertion_point(field_release:google.protobuf.testing.ValueMessage.value)
  
  ::google::protobuf::Value* temp = value_;
  value_ = NULL;
  return temp;
}
inline void ValueMessage::set_allocated_value(::google::protobuf::Value* value) {
  delete value_;
  if (value != NULL && value->GetArena() != NULL) {
    ::google::protobuf::Value* new_value = new ::google::protobuf::Value;
    new_value->CopyFrom(*value);
    value = new_value;
  }
  value_ = value;
  if (value) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_set_allocated:google.protobuf.testing.ValueMessage.value)
}

inline const ValueMessage* ValueMessage::internal_default_instance() {
  return &ValueMessage_default_instance_.get();
}
// -------------------------------------------------------------------

// ListValueMessage

// optional .google.protobuf.ListValue shopping_list = 1;
inline bool ListValueMessage::has_shopping_list() const {
  return this != internal_default_instance() && shopping_list_ != NULL;
}
inline void ListValueMessage::clear_shopping_list() {
  if (GetArenaNoVirtual() == NULL && shopping_list_ != NULL) delete shopping_list_;
  shopping_list_ = NULL;
}
inline const ::google::protobuf::ListValue& ListValueMessage::shopping_list() const {
  // @@protoc_insertion_point(field_get:google.protobuf.testing.ListValueMessage.shopping_list)
  return shopping_list_ != NULL ? *shopping_list_
                         : *::google::protobuf::ListValue::internal_default_instance();
}
inline ::google::protobuf::ListValue* ListValueMessage::mutable_shopping_list() {
  
  if (shopping_list_ == NULL) {
    shopping_list_ = new ::google::protobuf::ListValue;
  }
  // @@protoc_insertion_point(field_mutable:google.protobuf.testing.ListValueMessage.shopping_list)
  return shopping_list_;
}
inline ::google::protobuf::ListValue* ListValueMessage::release_shopping_list() {
  // @@protoc_insertion_point(field_release:google.protobuf.testing.ListValueMessage.shopping_list)
  
  ::google::protobuf::ListValue* temp = shopping_list_;
  shopping_list_ = NULL;
  return temp;
}
inline void ListValueMessage::set_allocated_shopping_list(::google::protobuf::ListValue* shopping_list) {
  delete shopping_list_;
  if (shopping_list != NULL && shopping_list->GetArena() != NULL) {
    ::google::protobuf::ListValue* new_shopping_list = new ::google::protobuf::ListValue;
    new_shopping_list->CopyFrom(*shopping_list);
    shopping_list = new_shopping_list;
  }
  shopping_list_ = shopping_list;
  if (shopping_list) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_set_allocated:google.protobuf.testing.ListValueMessage.shopping_list)
}

inline const ListValueMessage* ListValueMessage::internal_default_instance() {
  return &ListValueMessage_default_instance_.get();
}
// -------------------------------------------------------------------

// RequestMessage

// optional string content = 1;
inline void RequestMessage::clear_content() {
  content_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline const ::std::string& RequestMessage::content() const {
  // @@protoc_insertion_point(field_get:google.protobuf.testing.RequestMessage.content)
  return content_.GetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void RequestMessage::set_content(const ::std::string& value) {
  
  content_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:google.protobuf.testing.RequestMessage.content)
}
inline void RequestMessage::set_content(const char* value) {
  
  content_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:google.protobuf.testing.RequestMessage.content)
}
inline void RequestMessage::set_content(const char* value, size_t size) {
  
  content_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:google.protobuf.testing.RequestMessage.content)
}
inline ::std::string* RequestMessage::mutable_content() {
  
  // @@protoc_insertion_point(field_mutable:google.protobuf.testing.RequestMessage.content)
  return content_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline ::std::string* RequestMessage::release_content() {
  // @@protoc_insertion_point(field_release:google.protobuf.testing.RequestMessage.content)
  
  return content_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void RequestMessage::set_allocated_content(::std::string* content) {
  if (content != NULL) {
    
  } else {
    
  }
  content_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), content);
  // @@protoc_insertion_point(field_set_allocated:google.protobuf.testing.RequestMessage.content)
}

inline const RequestMessage* RequestMessage::internal_default_instance() {
  return &RequestMessage_default_instance_.get();
}
// -------------------------------------------------------------------

// AnyMessage

// optional .google.protobuf.Any any = 1;
inline bool AnyMessage::has_any() const {
  return this != internal_default_instance() && any_ != NULL;
}
inline void AnyMessage::clear_any() {
  if (GetArenaNoVirtual() == NULL && any_ != NULL) delete any_;
  any_ = NULL;
}
inline const ::google::protobuf::Any& AnyMessage::any() const {
  // @@protoc_insertion_point(field_get:google.protobuf.testing.AnyMessage.any)
  return any_ != NULL ? *any_
                         : *::google::protobuf::Any::internal_default_instance();
}
inline ::google::protobuf::Any* AnyMessage::mutable_any() {
  
  if (any_ == NULL) {
    any_ = new ::google::protobuf::Any;
  }
  // @@protoc_insertion_point(field_mutable:google.protobuf.testing.AnyMessage.any)
  return any_;
}
inline ::google::protobuf::Any* AnyMessage::release_any() {
  // @@protoc_insertion_point(field_release:google.protobuf.testing.AnyMessage.any)
  
  ::google::protobuf::Any* temp = any_;
  any_ = NULL;
  return temp;
}
inline void AnyMessage::set_allocated_any(::google::protobuf::Any* any) {
  delete any_;
  any_ = any;
  if (any) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_set_allocated:google.protobuf.testing.AnyMessage.any)
}

// optional .google.protobuf.testing.AnyData data = 2;
inline bool AnyMessage::has_data() const {
  return this != internal_default_instance() && data_ != NULL;
}
inline void AnyMessage::clear_data() {
  if (GetArenaNoVirtual() == NULL && data_ != NULL) delete data_;
  data_ = NULL;
}
inline const ::google::protobuf::testing::AnyData& AnyMessage::data() const {
  // @@protoc_insertion_point(field_get:google.protobuf.testing.AnyMessage.data)
  return data_ != NULL ? *data_
                         : *::google::protobuf::testing::AnyData::internal_default_instance();
}
inline ::google::protobuf::testing::AnyData* AnyMessage::mutable_data() {
  
  if (data_ == NULL) {
    data_ = new ::google::protobuf::testing::AnyData;
  }
  // @@protoc_insertion_point(field_mutable:google.protobuf.testing.AnyMessage.data)
  return data_;
}
inline ::google::protobuf::testing::AnyData* AnyMessage::release_data() {
  // @@protoc_insertion_point(field_release:google.protobuf.testing.AnyMessage.data)
  
  ::google::protobuf::testing::AnyData* temp = data_;
  data_ = NULL;
  return temp;
}
inline void AnyMessage::set_allocated_data(::google::protobuf::testing::AnyData* data) {
  delete data_;
  data_ = data;
  if (data) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_set_allocated:google.protobuf.testing.AnyMessage.data)
}

inline const AnyMessage* AnyMessage::internal_default_instance() {
  return &AnyMessage_default_instance_.get();
}
// -------------------------------------------------------------------

// AnyData

// optional int32 attr = 1;
inline void AnyData::clear_attr() {
  attr_ = 0;
}
inline ::google::protobuf::int32 AnyData::attr() const {
  // @@protoc_insertion_point(field_get:google.protobuf.testing.AnyData.attr)
  return attr_;
}
inline void AnyData::set_attr(::google::protobuf::int32 value) {
  
  attr_ = value;
  // @@protoc_insertion_point(field_set:google.protobuf.testing.AnyData.attr)
}

// optional string str = 2;
inline void AnyData::clear_str() {
  str_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline const ::std::string& AnyData::str() const {
  // @@protoc_insertion_point(field_get:google.protobuf.testing.AnyData.str)
  return str_.GetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void AnyData::set_str(const ::std::string& value) {
  
  str_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:google.protobuf.testing.AnyData.str)
}
inline void AnyData::set_str(const char* value) {
  
  str_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:google.protobuf.testing.AnyData.str)
}
inline void AnyData::set_str(const char* value, size_t size) {
  
  str_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:google.protobuf.testing.AnyData.str)
}
inline ::std::string* AnyData::mutable_str() {
  
  // @@protoc_insertion_point(field_mutable:google.protobuf.testing.AnyData.str)
  return str_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline ::std::string* AnyData::release_str() {
  // @@protoc_insertion_point(field_release:google.protobuf.testing.AnyData.str)
  
  return str_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void AnyData::set_allocated_str(::std::string* str) {
  if (str != NULL) {
    
  } else {
    
  }
  str_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), str);
  // @@protoc_insertion_point(field_set_allocated:google.protobuf.testing.AnyData.str)
}

// repeated string msgs = 3;
inline int AnyData::msgs_size() const {
  return msgs_.size();
}
inline void AnyData::clear_msgs() {
  msgs_.Clear();
}
inline const ::std::string& AnyData::msgs(int index) const {
  // @@protoc_insertion_point(field_get:google.protobuf.testing.AnyData.msgs)
  return msgs_.Get(index);
}
inline ::std::string* AnyData::mutable_msgs(int index) {
  // @@protoc_insertion_point(field_mutable:google.protobuf.testing.AnyData.msgs)
  return msgs_.Mutable(index);
}
inline void AnyData::set_msgs(int index, const ::std::string& value) {
  // @@protoc_insertion_point(field_set:google.protobuf.testing.AnyData.msgs)
  msgs_.Mutable(index)->assign(value);
}
inline void AnyData::set_msgs(int index, const char* value) {
  msgs_.Mutable(index)->assign(value);
  // @@protoc_insertion_point(field_set_char:google.protobuf.testing.AnyData.msgs)
}
inline void AnyData::set_msgs(int index, const char* value, size_t size) {
  msgs_.Mutable(index)->assign(
    reinterpret_cast<const char*>(value), size);
  // @@protoc_insertion_point(field_set_pointer:google.protobuf.testing.AnyData.msgs)
}
inline ::std::string* AnyData::add_msgs() {
  // @@protoc_insertion_point(field_add_mutable:google.protobuf.testing.AnyData.msgs)
  return msgs_.Add();
}
inline void AnyData::add_msgs(const ::std::string& value) {
  msgs_.Add()->assign(value);
  // @@protoc_insertion_point(field_add:google.protobuf.testing.AnyData.msgs)
}
inline void AnyData::add_msgs(const char* value) {
  msgs_.Add()->assign(value);
  // @@protoc_insertion_point(field_add_char:google.protobuf.testing.AnyData.msgs)
}
inline void AnyData::add_msgs(const char* value, size_t size) {
  msgs_.Add()->assign(reinterpret_cast<const char*>(value), size);
  // @@protoc_insertion_point(field_add_pointer:google.protobuf.testing.AnyData.msgs)
}
inline const ::google::protobuf::RepeatedPtrField< ::std::string>&
AnyData::msgs() const {
  // @@protoc_insertion_point(field_list:google.protobuf.testing.AnyData.msgs)
  return msgs_;
}
inline ::google::protobuf::RepeatedPtrField< ::std::string>*
AnyData::mutable_msgs() {
  // @@protoc_insertion_point(field_mutable_list:google.protobuf.testing.AnyData.msgs)
  return &msgs_;
}

// optional .google.protobuf.testing.AnyData nested_data = 4;
inline bool AnyData::has_nested_data() const {
  return this != internal_default_instance() && nested_data_ != NULL;
}
inline void AnyData::clear_nested_data() {
  if (GetArenaNoVirtual() == NULL && nested_data_ != NULL) delete nested_data_;
  nested_data_ = NULL;
}
inline const ::google::protobuf::testing::AnyData& AnyData::nested_data() const {
  // @@protoc_insertion_point(field_get:google.protobuf.testing.AnyData.nested_data)
  return nested_data_ != NULL ? *nested_data_
                         : *::google::protobuf::testing::AnyData::internal_default_instance();
}
inline ::google::protobuf::testing::AnyData* AnyData::mutable_nested_data() {
  
  if (nested_data_ == NULL) {
    nested_data_ = new ::google::protobuf::testing::AnyData;
  }
  // @@protoc_insertion_point(field_mutable:google.protobuf.testing.AnyData.nested_data)
  return nested_data_;
}
inline ::google::protobuf::testing::AnyData* AnyData::release_nested_data() {
  // @@protoc_insertion_point(field_release:google.protobuf.testing.AnyData.nested_data)
  
  ::google::protobuf::testing::AnyData* temp = nested_data_;
  nested_data_ = NULL;
  return temp;
}
inline void AnyData::set_allocated_nested_data(::google::protobuf::testing::AnyData* nested_data) {
  delete nested_data_;
  nested_data_ = nested_data;
  if (nested_data) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_set_allocated:google.protobuf.testing.AnyData.nested_data)
}

// map<string, string> map_data = 7;
inline int AnyData::map_data_size() const {
  return map_data_.size();
}
inline void AnyData::clear_map_data() {
  map_data_.Clear();
}
inline const ::google::protobuf::Map< ::std::string, ::std::string >&
AnyData::map_data() const {
  // @@protoc_insertion_point(field_map:google.protobuf.testing.AnyData.map_data)
  return map_data_.GetMap();
}
inline ::google::protobuf::Map< ::std::string, ::std::string >*
AnyData::mutable_map_data() {
  // @@protoc_insertion_point(field_mutable_map:google.protobuf.testing.AnyData.map_data)
  return map_data_.MutableMap();
}

// optional .google.protobuf.Struct struct_data = 8;
inline bool AnyData::has_struct_data() const {
  return this != internal_default_instance() && struct_data_ != NULL;
}
inline void AnyData::clear_struct_data() {
  if (GetArenaNoVirtual() == NULL && struct_data_ != NULL) delete struct_data_;
  struct_data_ = NULL;
}
inline const ::google::protobuf::Struct& AnyData::struct_data() const {
  // @@protoc_insertion_point(field_get:google.protobuf.testing.AnyData.struct_data)
  return struct_data_ != NULL ? *struct_data_
                         : *::google::protobuf::Struct::internal_default_instance();
}
inline ::google::protobuf::Struct* AnyData::mutable_struct_data() {
  
  if (struct_data_ == NULL) {
    struct_data_ = new ::google::protobuf::Struct;
  }
  // @@protoc_insertion_point(field_mutable:google.protobuf.testing.AnyData.struct_data)
  return struct_data_;
}
inline ::google::protobuf::Struct* AnyData::release_struct_data() {
  // @@protoc_insertion_point(field_release:google.protobuf.testing.AnyData.struct_data)
  
  ::google::protobuf::Struct* temp = struct_data_;
  struct_data_ = NULL;
  return temp;
}
inline void AnyData::set_allocated_struct_data(::google::protobuf::Struct* struct_data) {
  delete struct_data_;
  if (struct_data != NULL && struct_data->GetArena() != NULL) {
    ::google::protobuf::Struct* new_struct_data = new ::google::protobuf::Struct;
    new_struct_data->CopyFrom(*struct_data);
    struct_data = new_struct_data;
  }
  struct_data_ = struct_data;
  if (struct_data) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_set_allocated:google.protobuf.testing.AnyData.struct_data)
}

// repeated .google.protobuf.testing.AnyData repeated_data = 9;
inline int AnyData::repeated_data_size() const {
  return repeated_data_.size();
}
inline void AnyData::clear_repeated_data() {
  repeated_data_.Clear();
}
inline const ::google::protobuf::testing::AnyData& AnyData::repeated_data(int index) const {
  // @@protoc_insertion_point(field_get:google.protobuf.testing.AnyData.repeated_data)
  return repeated_data_.Get(index);
}
inline ::google::protobuf::testing::AnyData* AnyData::mutable_repeated_data(int index) {
  // @@protoc_insertion_point(field_mutable:google.protobuf.testing.AnyData.repeated_data)
  return repeated_data_.Mutable(index);
}
inline ::google::protobuf::testing::AnyData* AnyData::add_repeated_data() {
  // @@protoc_insertion_point(field_add:google.protobuf.testing.AnyData.repeated_data)
  return repeated_data_.Add();
}
inline ::google::protobuf::RepeatedPtrField< ::google::protobuf::testing::AnyData >*
AnyData::mutable_repeated_data() {
  // @@protoc_insertion_point(field_mutable_list:google.protobuf.testing.AnyData.repeated_data)
  return &repeated_data_;
}
inline const ::google::protobuf::RepeatedPtrField< ::google::protobuf::testing::AnyData >&
AnyData::repeated_data() const {
  // @@protoc_insertion_point(field_list:google.protobuf.testing.AnyData.repeated_data)
  return repeated_data_;
}

inline const AnyData* AnyData::internal_default_instance() {
  return &AnyData_default_instance_.get();
}
// -------------------------------------------------------------------

// StringtoIntMap

// map<string, int32> map = 1;
inline int StringtoIntMap::map_size() const {
  return map_.size();
}
inline void StringtoIntMap::clear_map() {
  map_.Clear();
}
inline const ::google::protobuf::Map< ::std::string, ::google::protobuf::int32 >&
StringtoIntMap::map() const {
  // @@protoc_insertion_point(field_map:google.protobuf.testing.StringtoIntMap.map)
  return map_.GetMap();
}
inline ::google::protobuf::Map< ::std::string, ::google::protobuf::int32 >*
StringtoIntMap::mutable_map() {
  // @@protoc_insertion_point(field_mutable_map:google.protobuf.testing.StringtoIntMap.map)
  return map_.MutableMap();
}

inline const StringtoIntMap* StringtoIntMap::internal_default_instance() {
  return &StringtoIntMap_default_instance_.get();
}
// -------------------------------------------------------------------

// IntToStringMap

// map<int32, string> map = 1;
inline int IntToStringMap::map_size() const {
  return map_.size();
}
inline void IntToStringMap::clear_map() {
  map_.Clear();
}
inline const ::google::protobuf::Map< ::google::protobuf::int32, ::std::string >&
IntToStringMap::map() const {
  // @@protoc_insertion_point(field_map:google.protobuf.testing.IntToStringMap.map)
  return map_.GetMap();
}
inline ::google::protobuf::Map< ::google::protobuf::int32, ::std::string >*
IntToStringMap::mutable_map() {
  // @@protoc_insertion_point(field_mutable_map:google.protobuf.testing.IntToStringMap.map)
  return map_.MutableMap();
}

inline const IntToStringMap* IntToStringMap::internal_default_instance() {
  return &IntToStringMap_default_instance_.get();
}
// -------------------------------------------------------------------

// MixedMap

// optional string msg = 1;
inline void MixedMap::clear_msg() {
  msg_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline const ::std::string& MixedMap::msg() const {
  // @@protoc_insertion_point(field_get:google.protobuf.testing.MixedMap.msg)
  return msg_.GetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void MixedMap::set_msg(const ::std::string& value) {
  
  msg_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:google.protobuf.testing.MixedMap.msg)
}
inline void MixedMap::set_msg(const char* value) {
  
  msg_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:google.protobuf.testing.MixedMap.msg)
}
inline void MixedMap::set_msg(const char* value, size_t size) {
  
  msg_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:google.protobuf.testing.MixedMap.msg)
}
inline ::std::string* MixedMap::mutable_msg() {
  
  // @@protoc_insertion_point(field_mutable:google.protobuf.testing.MixedMap.msg)
  return msg_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline ::std::string* MixedMap::release_msg() {
  // @@protoc_insertion_point(field_release:google.protobuf.testing.MixedMap.msg)
  
  return msg_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void MixedMap::set_allocated_msg(::std::string* msg) {
  if (msg != NULL) {
    
  } else {
    
  }
  msg_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), msg);
  // @@protoc_insertion_point(field_set_allocated:google.protobuf.testing.MixedMap.msg)
}

// map<string, float> map = 2;
inline int MixedMap::map_size() const {
  return map_.size();
}
inline void MixedMap::clear_map() {
  map_.Clear();
}
inline const ::google::protobuf::Map< ::std::string, float >&
MixedMap::map() const {
  // @@protoc_insertion_point(field_map:google.protobuf.testing.MixedMap.map)
  return map_.GetMap();
}
inline ::google::protobuf::Map< ::std::string, float >*
MixedMap::mutable_map() {
  // @@protoc_insertion_point(field_mutable_map:google.protobuf.testing.MixedMap.map)
  return map_.MutableMap();
}

// optional int32 int_value = 3;
inline void MixedMap::clear_int_value() {
  int_value_ = 0;
}
inline ::google::protobuf::int32 MixedMap::int_value() const {
  // @@protoc_insertion_point(field_get:google.protobuf.testing.MixedMap.int_value)
  return int_value_;
}
inline void MixedMap::set_int_value(::google::protobuf::int32 value) {
  
  int_value_ = value;
  // @@protoc_insertion_point(field_set:google.protobuf.testing.MixedMap.int_value)
}

inline const MixedMap* MixedMap::internal_default_instance() {
  return &MixedMap_default_instance_.get();
}
// -------------------------------------------------------------------

// MixedMap2

// map<int32, bool> map = 1;
inline int MixedMap2::map_size() const {
  return map_.size();
}
inline void MixedMap2::clear_map() {
  map_.Clear();
}
inline const ::google::protobuf::Map< ::google::protobuf::int32, bool >&
MixedMap2::map() const {
  // @@protoc_insertion_point(field_map:google.protobuf.testing.MixedMap2.map)
  return map_.GetMap();
}
inline ::google::protobuf::Map< ::google::protobuf::int32, bool >*
MixedMap2::mutable_map() {
  // @@protoc_insertion_point(field_mutable_map:google.protobuf.testing.MixedMap2.map)
  return map_.MutableMap();
}

// optional .google.protobuf.testing.MixedMap2.E ee = 2;
inline void MixedMap2::clear_ee() {
  ee_ = 0;
}
inline ::google::protobuf::testing::MixedMap2_E MixedMap2::ee() const {
  // @@protoc_insertion_point(field_get:google.protobuf.testing.MixedMap2.ee)
  return static_cast< ::google::protobuf::testing::MixedMap2_E >(ee_);
}
inline void MixedMap2::set_ee(::google::protobuf::testing::MixedMap2_E value) {
  
  ee_ = value;
  // @@protoc_insertion_point(field_set:google.protobuf.testing.MixedMap2.ee)
}

// optional string msg = 4;
inline void MixedMap2::clear_msg() {
  msg_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline const ::std::string& MixedMap2::msg() const {
  // @@protoc_insertion_point(field_get:google.protobuf.testing.MixedMap2.msg)
  return msg_.GetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void MixedMap2::set_msg(const ::std::string& value) {
  
  msg_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:google.protobuf.testing.MixedMap2.msg)
}
inline void MixedMap2::set_msg(const char* value) {
  
  msg_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:google.protobuf.testing.MixedMap2.msg)
}
inline void MixedMap2::set_msg(const char* value, size_t size) {
  
  msg_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:google.protobuf.testing.MixedMap2.msg)
}
inline ::std::string* MixedMap2::mutable_msg() {
  
  // @@protoc_insertion_point(field_mutable:google.protobuf.testing.MixedMap2.msg)
  return msg_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline ::std::string* MixedMap2::release_msg() {
  // @@protoc_insertion_point(field_release:google.protobuf.testing.MixedMap2.msg)
  
  return msg_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void MixedMap2::set_allocated_msg(::std::string* msg) {
  if (msg != NULL) {
    
  } else {
    
  }
  msg_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), msg);
  // @@protoc_insertion_point(field_set_allocated:google.protobuf.testing.MixedMap2.msg)
}

inline const MixedMap2* MixedMap2::internal_default_instance() {
  return &MixedMap2_default_instance_.get();
}
// -------------------------------------------------------------------

// MessageMap_M

// optional int32 inner_int = 1;
inline void MessageMap_M::clear_inner_int() {
  inner_int_ = 0;
}
inline ::google::protobuf::int32 MessageMap_M::inner_int() const {
  // @@protoc_insertion_point(field_get:google.protobuf.testing.MessageMap.M.inner_int)
  return inner_int_;
}
inline void MessageMap_M::set_inner_int(::google::protobuf::int32 value) {
  
  inner_int_ = value;
  // @@protoc_insertion_point(field_set:google.protobuf.testing.MessageMap.M.inner_int)
}

// optional string inner_text = 2;
inline void MessageMap_M::clear_inner_text() {
  inner_text_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline const ::std::string& MessageMap_M::inner_text() const {
  // @@protoc_insertion_point(field_get:google.protobuf.testing.MessageMap.M.inner_text)
  return inner_text_.GetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void MessageMap_M::set_inner_text(const ::std::string& value) {
  
  inner_text_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:google.protobuf.testing.MessageMap.M.inner_text)
}
inline void MessageMap_M::set_inner_text(const char* value) {
  
  inner_text_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:google.protobuf.testing.MessageMap.M.inner_text)
}
inline void MessageMap_M::set_inner_text(const char* value, size_t size) {
  
  inner_text_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:google.protobuf.testing.MessageMap.M.inner_text)
}
inline ::std::string* MessageMap_M::mutable_inner_text() {
  
  // @@protoc_insertion_point(field_mutable:google.protobuf.testing.MessageMap.M.inner_text)
  return inner_text_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline ::std::string* MessageMap_M::release_inner_text() {
  // @@protoc_insertion_point(field_release:google.protobuf.testing.MessageMap.M.inner_text)
  
  return inner_text_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void MessageMap_M::set_allocated_inner_text(::std::string* inner_text) {
  if (inner_text != NULL) {
    
  } else {
    
  }
  inner_text_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), inner_text);
  // @@protoc_insertion_point(field_set_allocated:google.protobuf.testing.MessageMap.M.inner_text)
}

inline const MessageMap_M* MessageMap_M::internal_default_instance() {
  return &MessageMap_M_default_instance_.get();
}
// -------------------------------------------------------------------

// MessageMap

// map<string, .google.protobuf.testing.MessageMap.M> map = 1;
inline int MessageMap::map_size() const {
  return map_.size();
}
inline void MessageMap::clear_map() {
  map_.Clear();
}
inline const ::google::protobuf::Map< ::std::string, ::google::protobuf::testing::MessageMap_M >&
MessageMap::map() const {
  // @@protoc_insertion_point(field_map:google.protobuf.testing.MessageMap.map)
  return map_.GetMap();
}
inline ::google::protobuf::Map< ::std::string, ::google::protobuf::testing::MessageMap_M >*
MessageMap::mutable_map() {
  // @@protoc_insertion_point(field_mutable_map:google.protobuf.testing.MessageMap.map)
  return map_.MutableMap();
}

inline const MessageMap* MessageMap::internal_default_instance() {
  return &MessageMap_default_instance_.get();
}
// -------------------------------------------------------------------

// DoubleValueMessage

// optional .google.protobuf.DoubleValue double = 1;
inline bool DoubleValueMessage::has_double_() const {
  return this != internal_default_instance() && double__ != NULL;
}
inline void DoubleValueMessage::clear_double_() {
  if (GetArenaNoVirtual() == NULL && double__ != NULL) delete double__;
  double__ = NULL;
}
inline const ::google::protobuf::DoubleValue& DoubleValueMessage::double_() const {
  // @@protoc_insertion_point(field_get:google.protobuf.testing.DoubleValueMessage.double)
  return double__ != NULL ? *double__
                         : *::google::protobuf::DoubleValue::internal_default_instance();
}
inline ::google::protobuf::DoubleValue* DoubleValueMessage::mutable_double_() {
  
  if (double__ == NULL) {
    double__ = new ::google::protobuf::DoubleValue;
  }
  // @@protoc_insertion_point(field_mutable:google.protobuf.testing.DoubleValueMessage.double)
  return double__;
}
inline ::google::protobuf::DoubleValue* DoubleValueMessage::release_double_() {
  // @@protoc_insertion_point(field_release:google.protobuf.testing.DoubleValueMessage.double)
  
  ::google::protobuf::DoubleValue* temp = double__;
  double__ = NULL;
  return temp;
}
inline void DoubleValueMessage::set_allocated_double_(::google::protobuf::DoubleValue* double_) {
  delete double__;
  if (double_ != NULL && double_->GetArena() != NULL) {
    ::google::protobuf::DoubleValue* new_double_ = new ::google::protobuf::DoubleValue;
    new_double_->CopyFrom(*double_);
    double_ = new_double_;
  }
  double__ = double_;
  if (double_) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_set_allocated:google.protobuf.testing.DoubleValueMessage.double)
}

inline const DoubleValueMessage* DoubleValueMessage::internal_default_instance() {
  return &DoubleValueMessage_default_instance_.get();
}
#endif  // !PROTOBUF_INLINE_NOT_IN_HEADERS
// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------


// @@protoc_insertion_point(namespace_scope)

}  // namespace testing
}  // namespace protobuf
}  // namespace google

#ifndef SWIG
namespace google {
namespace protobuf {

template <> struct is_proto_enum< ::google::protobuf::testing::MixedMap2_E> : ::google::protobuf::internal::true_type {};
template <>
inline const EnumDescriptor* GetEnumDescriptor< ::google::protobuf::testing::MixedMap2_E>() {
  return ::google::protobuf::testing::MixedMap2_E_descriptor();
}

}  // namespace protobuf
}  // namespace google
#endif  // SWIG

// @@protoc_insertion_point(global_scope)

#endif  // PROTOBUF_google_2fprotobuf_2futil_2finternal_2ftestdata_2fdefault_5fvalue_2eproto__INCLUDED

// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: google/protobuf/unittest_no_field_presence.proto

#define INTERNAL_SUPPRESS_PROTOBUF_FIELD_DEPRECATION
#include <google/protobuf/unittest_no_field_presence.pb.h>

#include <algorithm>

#include <google/protobuf/stubs/common.h>
#include <google/protobuf/stubs/port.h>
#include <google/protobuf/stubs/once.h>
#include <google/protobuf/io/coded_stream.h>
#include <google/protobuf/wire_format_lite_inl.h>
#include <google/protobuf/descriptor.h>
#include <google/protobuf/generated_message_reflection.h>
#include <google/protobuf/reflection_ops.h>
#include <google/protobuf/wire_format.h>
// @@protoc_insertion_point(includes)

namespace proto2_nofieldpresence_unittest {

namespace {

const ::google::protobuf::Descriptor* TestAllTypes_descriptor_ = NULL;
const ::google::protobuf::internal::GeneratedMessageReflection*
  TestAllTypes_reflection_ = NULL;
struct TestAllTypesOneofInstance {
  ::google::protobuf::uint32 oneof_uint32_;
  const ::proto2_nofieldpresence_unittest::TestAllTypes_NestedMessage* oneof_nested_message_;
  ::google::protobuf::internal::ArenaStringPtr oneof_string_;
  int oneof_enum_;
}* TestAllTypes_default_oneof_instance_ = NULL;
const ::google::protobuf::Descriptor* TestAllTypes_NestedMessage_descriptor_ = NULL;
const ::google::protobuf::internal::GeneratedMessageReflection*
  TestAllTypes_NestedMessage_reflection_ = NULL;
const ::google::protobuf::EnumDescriptor* TestAllTypes_NestedEnum_descriptor_ = NULL;
const ::google::protobuf::Descriptor* TestProto2Required_descriptor_ = NULL;
const ::google::protobuf::internal::GeneratedMessageReflection*
  TestProto2Required_reflection_ = NULL;
const ::google::protobuf::Descriptor* ForeignMessage_descriptor_ = NULL;
const ::google::protobuf::internal::GeneratedMessageReflection*
  ForeignMessage_reflection_ = NULL;
const ::google::protobuf::EnumDescriptor* ForeignEnum_descriptor_ = NULL;

}  // namespace


void protobuf_AssignDesc_google_2fprotobuf_2funittest_5fno_5ffield_5fpresence_2eproto() GOOGLE_ATTRIBUTE_COLD;
void protobuf_AssignDesc_google_2fprotobuf_2funittest_5fno_5ffield_5fpresence_2eproto() {
  protobuf_AddDesc_google_2fprotobuf_2funittest_5fno_5ffield_5fpresence_2eproto();
  const ::google::protobuf::FileDescriptor* file =
    ::google::protobuf::DescriptorPool::generated_pool()->FindFileByName(
      "google/protobuf/unittest_no_field_presence.proto");
  GOOGLE_CHECK(file != NULL);
  TestAllTypes_descriptor_ = file->message_type(0);
  static const int TestAllTypes_offsets_[51] = {
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(TestAllTypes, optional_int32_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(TestAllTypes, optional_int64_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(TestAllTypes, optional_uint32_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(TestAllTypes, optional_uint64_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(TestAllTypes, optional_sint32_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(TestAllTypes, optional_sint64_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(TestAllTypes, optional_fixed32_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(TestAllTypes, optional_fixed64_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(TestAllTypes, optional_sfixed32_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(TestAllTypes, optional_sfixed64_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(TestAllTypes, optional_float_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(TestAllTypes, optional_double_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(TestAllTypes, optional_bool_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(TestAllTypes, optional_string_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(TestAllTypes, optional_bytes_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(TestAllTypes, optional_nested_message_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(TestAllTypes, optional_foreign_message_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(TestAllTypes, optional_proto2_message_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(TestAllTypes, optional_nested_enum_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(TestAllTypes, optional_foreign_enum_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(TestAllTypes, optional_string_piece_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(TestAllTypes, optional_cord_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(TestAllTypes, optional_lazy_message_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(TestAllTypes, repeated_int32_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(TestAllTypes, repeated_int64_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(TestAllTypes, repeated_uint32_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(TestAllTypes, repeated_uint64_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(TestAllTypes, repeated_sint32_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(TestAllTypes, repeated_sint64_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(TestAllTypes, repeated_fixed32_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(TestAllTypes, repeated_fixed64_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(TestAllTypes, repeated_sfixed32_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(TestAllTypes, repeated_sfixed64_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(TestAllTypes, repeated_float_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(TestAllTypes, repeated_double_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(TestAllTypes, repeated_bool_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(TestAllTypes, repeated_string_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(TestAllTypes, repeated_bytes_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(TestAllTypes, repeated_nested_message_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(TestAllTypes, repeated_foreign_message_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(TestAllTypes, repeated_proto2_message_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(TestAllTypes, repeated_nested_enum_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(TestAllTypes, repeated_foreign_enum_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(TestAllTypes, repeated_string_piece_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(TestAllTypes, repeated_cord_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(TestAllTypes, repeated_lazy_message_),
    PROTO2_GENERATED_DEFAULT_ONEOF_FIELD_OFFSET(TestAllTypes_default_oneof_instance_, oneof_uint32_),
    PROTO2_GENERATED_DEFAULT_ONEOF_FIELD_OFFSET(TestAllTypes_default_oneof_instance_, oneof_nested_message_),
    PROTO2_GENERATED_DEFAULT_ONEOF_FIELD_OFFSET(TestAllTypes_default_oneof_instance_, oneof_string_),
    PROTO2_GENERATED_DEFAULT_ONEOF_FIELD_OFFSET(TestAllTypes_default_oneof_instance_, oneof_enum_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(TestAllTypes, oneof_field_),
  };
  TestAllTypes_reflection_ =
    ::google::protobuf::internal::GeneratedMessageReflection::NewGeneratedMessageReflection(
      TestAllTypes_descriptor_,
      TestAllTypes::internal_default_instance(),
      TestAllTypes_offsets_,
      -1,
      -1,
      -1,
      TestAllTypes_default_oneof_instance_,
      GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(TestAllTypes, _oneof_case_[0]),
      sizeof(TestAllTypes),
      GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(TestAllTypes, _internal_metadata_));
  TestAllTypes_NestedMessage_descriptor_ = TestAllTypes_descriptor_->nested_type(0);
  static const int TestAllTypes_NestedMessage_offsets_[1] = {
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(TestAllTypes_NestedMessage, bb_),
  };
  TestAllTypes_NestedMessage_reflection_ =
    ::google::protobuf::internal::GeneratedMessageReflection::NewGeneratedMessageReflection(
      TestAllTypes_NestedMessage_descriptor_,
      TestAllTypes_NestedMessage::internal_default_instance(),
      TestAllTypes_NestedMessage_offsets_,
      -1,
      -1,
      -1,
      sizeof(TestAllTypes_NestedMessage),
      GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(TestAllTypes_NestedMessage, _internal_metadata_));
  TestAllTypes_NestedEnum_descriptor_ = TestAllTypes_descriptor_->enum_type(0);
  TestProto2Required_descriptor_ = file->message_type(1);
  static const int TestProto2Required_offsets_[1] = {
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(TestProto2Required, proto2_),
  };
  TestProto2Required_reflection_ =
    ::google::protobuf::internal::GeneratedMessageReflection::NewGeneratedMessageReflection(
      TestProto2Required_descriptor_,
      TestProto2Required::internal_default_instance(),
      TestProto2Required_offsets_,
      -1,
      -1,
      -1,
      sizeof(TestProto2Required),
      GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(TestProto2Required, _internal_metadata_));
  ForeignMessage_descriptor_ = file->message_type(2);
  static const int ForeignMessage_offsets_[1] = {
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(ForeignMessage, c_),
  };
  ForeignMessage_reflection_ =
    ::google::protobuf::internal::GeneratedMessageReflection::NewGeneratedMessageReflection(
      ForeignMessage_descriptor_,
      ForeignMessage::internal_default_instance(),
      ForeignMessage_offsets_,
      -1,
      -1,
      -1,
      sizeof(ForeignMessage),
      GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(ForeignMessage, _internal_metadata_));
  ForeignEnum_descriptor_ = file->enum_type(0);
}

namespace {

GOOGLE_PROTOBUF_DECLARE_ONCE(protobuf_AssignDescriptors_once_);
void protobuf_AssignDescriptorsOnce() {
  ::google::protobuf::GoogleOnceInit(&protobuf_AssignDescriptors_once_,
                 &protobuf_AssignDesc_google_2fprotobuf_2funittest_5fno_5ffield_5fpresence_2eproto);
}

void protobuf_RegisterTypes(const ::std::string&) GOOGLE_ATTRIBUTE_COLD;
void protobuf_RegisterTypes(const ::std::string&) {
  protobuf_AssignDescriptorsOnce();
  ::google::protobuf::MessageFactory::InternalRegisterGeneratedMessage(
      TestAllTypes_descriptor_, TestAllTypes::internal_default_instance());
  ::google::protobuf::MessageFactory::InternalRegisterGeneratedMessage(
      TestAllTypes_NestedMessage_descriptor_, TestAllTypes_NestedMessage::internal_default_instance());
  ::google::protobuf::MessageFactory::InternalRegisterGeneratedMessage(
      TestProto2Required_descriptor_, TestProto2Required::internal_default_instance());
  ::google::protobuf::MessageFactory::InternalRegisterGeneratedMessage(
      ForeignMessage_descriptor_, ForeignMessage::internal_default_instance());
}

}  // namespace

void protobuf_ShutdownFile_google_2fprotobuf_2funittest_5fno_5ffield_5fpresence_2eproto() {
  TestAllTypes_default_instance_.Shutdown();
  delete TestAllTypes_default_oneof_instance_;
  delete TestAllTypes_reflection_;
  TestAllTypes_NestedMessage_default_instance_.Shutdown();
  delete TestAllTypes_NestedMessage_reflection_;
  TestProto2Required_default_instance_.Shutdown();
  delete TestProto2Required_reflection_;
  ForeignMessage_default_instance_.Shutdown();
  delete ForeignMessage_reflection_;
}

void protobuf_InitDefaults_google_2fprotobuf_2funittest_5fno_5ffield_5fpresence_2eproto_impl() {
  GOOGLE_PROTOBUF_VERIFY_VERSION;

  ::protobuf_unittest::protobuf_InitDefaults_google_2fprotobuf_2funittest_2eproto();
  ::google::protobuf::internal::GetEmptyString();
  TestAllTypes_default_instance_.DefaultConstruct();
  TestAllTypes_default_oneof_instance_ = new TestAllTypesOneofInstance();
  TestAllTypes_NestedMessage_default_instance_.DefaultConstruct();
  TestProto2Required_default_instance_.DefaultConstruct();
  ForeignMessage_default_instance_.DefaultConstruct();
  TestAllTypes_default_instance_.get_mutable()->InitAsDefaultInstance();
  TestAllTypes_NestedMessage_default_instance_.get_mutable()->InitAsDefaultInstance();
  TestProto2Required_default_instance_.get_mutable()->InitAsDefaultInstance();
  ForeignMessage_default_instance_.get_mutable()->InitAsDefaultInstance();
}

GOOGLE_PROTOBUF_DECLARE_ONCE(protobuf_InitDefaults_google_2fprotobuf_2funittest_5fno_5ffield_5fpresence_2eproto_once_);
void protobuf_InitDefaults_google_2fprotobuf_2funittest_5fno_5ffield_5fpresence_2eproto() {
  ::google::protobuf::GoogleOnceInit(&protobuf_InitDefaults_google_2fprotobuf_2funittest_5fno_5ffield_5fpresence_2eproto_once_,
                 &protobuf_InitDefaults_google_2fprotobuf_2funittest_5fno_5ffield_5fpresence_2eproto_impl);
}
void protobuf_AddDesc_google_2fprotobuf_2funittest_5fno_5ffield_5fpresence_2eproto_impl() {
  GOOGLE_PROTOBUF_VERIFY_VERSION;

  protobuf_InitDefaults_google_2fprotobuf_2funittest_5fno_5ffield_5fpresence_2eproto();
  ::google::protobuf::DescriptorPool::InternalAddGeneratedFile(
    "\n0google/protobuf/unittest_no_field_pres"
    "ence.proto\022\037proto2_nofieldpresence_unitt"
    "est\032\036google/protobuf/unittest.proto\"\232\021\n\014"
    "TestAllTypes\022\026\n\016optional_int32\030\001 \001(\005\022\026\n\016"
    "optional_int64\030\002 \001(\003\022\027\n\017optional_uint32\030"
    "\003 \001(\r\022\027\n\017optional_uint64\030\004 \001(\004\022\027\n\017option"
    "al_sint32\030\005 \001(\021\022\027\n\017optional_sint64\030\006 \001(\022"
    "\022\030\n\020optional_fixed32\030\007 \001(\007\022\030\n\020optional_f"
    "ixed64\030\010 \001(\006\022\031\n\021optional_sfixed32\030\t \001(\017\022"
    "\031\n\021optional_sfixed64\030\n \001(\020\022\026\n\016optional_f"
    "loat\030\013 \001(\002\022\027\n\017optional_double\030\014 \001(\001\022\025\n\ro"
    "ptional_bool\030\r \001(\010\022\027\n\017optional_string\030\016 "
    "\001(\t\022\026\n\016optional_bytes\030\017 \001(\014\022\\\n\027optional_"
    "nested_message\030\022 \001(\0132;.proto2_nofieldpre"
    "sence_unittest.TestAllTypes.NestedMessag"
    "e\022Q\n\030optional_foreign_message\030\023 \001(\0132/.pr"
    "oto2_nofieldpresence_unittest.ForeignMes"
    "sage\022@\n\027optional_proto2_message\030\024 \001(\0132\037."
    "protobuf_unittest.TestAllTypes\022V\n\024option"
    "al_nested_enum\030\025 \001(\01628.proto2_nofieldpre"
    "sence_unittest.TestAllTypes.NestedEnum\022K"
    "\n\025optional_foreign_enum\030\026 \001(\0162,.proto2_n"
    "ofieldpresence_unittest.ForeignEnum\022!\n\025o"
    "ptional_string_piece\030\030 \001(\tB\002\010\002\022\031\n\roption"
    "al_cord\030\031 \001(\tB\002\010\001\022^\n\025optional_lazy_messa"
    "ge\030\036 \001(\0132;.proto2_nofieldpresence_unitte"
    "st.TestAllTypes.NestedMessageB\002(\001\022\026\n\016rep"
    "eated_int32\030\037 \003(\005\022\026\n\016repeated_int64\030  \003("
    "\003\022\027\n\017repeated_uint32\030! \003(\r\022\027\n\017repeated_u"
    "int64\030\" \003(\004\022\027\n\017repeated_sint32\030# \003(\021\022\027\n\017"
    "repeated_sint64\030$ \003(\022\022\030\n\020repeated_fixed3"
    "2\030% \003(\007\022\030\n\020repeated_fixed64\030& \003(\006\022\031\n\021rep"
    "eated_sfixed32\030\' \003(\017\022\031\n\021repeated_sfixed6"
    "4\030( \003(\020\022\026\n\016repeated_float\030) \003(\002\022\027\n\017repea"
    "ted_double\030* \003(\001\022\025\n\rrepeated_bool\030+ \003(\010\022"
    "\027\n\017repeated_string\030, \003(\t\022\026\n\016repeated_byt"
    "es\030- \003(\014\022\\\n\027repeated_nested_message\0300 \003("
    "\0132;.proto2_nofieldpresence_unittest.Test"
    "AllTypes.NestedMessage\022Q\n\030repeated_forei"
    "gn_message\0301 \003(\0132/.proto2_nofieldpresenc"
    "e_unittest.ForeignMessage\022@\n\027repeated_pr"
    "oto2_message\0302 \003(\0132\037.protobuf_unittest.T"
    "estAllTypes\022V\n\024repeated_nested_enum\0303 \003("
    "\01628.proto2_nofieldpresence_unittest.Test"
    "AllTypes.NestedEnum\022K\n\025repeated_foreign_"
    "enum\0304 \003(\0162,.proto2_nofieldpresence_unit"
    "test.ForeignEnum\022!\n\025repeated_string_piec"
    "e\0306 \003(\tB\002\010\002\022\031\n\rrepeated_cord\0307 \003(\tB\002\010\001\022^"
    "\n\025repeated_lazy_message\0309 \003(\0132;.proto2_n"
    "ofieldpresence_unittest.TestAllTypes.Nes"
    "tedMessageB\002(\001\022\026\n\014oneof_uint32\030o \001(\rH\000\022["
    "\n\024oneof_nested_message\030p \001(\0132;.proto2_no"
    "fieldpresence_unittest.TestAllTypes.Nest"
    "edMessageH\000\022\026\n\014oneof_string\030q \001(\tH\000\022N\n\no"
    "neof_enum\030r \001(\01628.proto2_nofieldpresence"
    "_unittest.TestAllTypes.NestedEnumH\000\032\033\n\rN"
    "estedMessage\022\n\n\002bb\030\001 \001(\005\"\'\n\nNestedEnum\022\007"
    "\n\003FOO\020\000\022\007\n\003BAR\020\001\022\007\n\003BAZ\020\002B\r\n\013oneof_field"
    "\"E\n\022TestProto2Required\022/\n\006proto2\030\001 \001(\0132\037"
    ".protobuf_unittest.TestRequired\"\033\n\016Forei"
    "gnMessage\022\t\n\001c\030\001 \001(\005*@\n\013ForeignEnum\022\017\n\013F"
    "OREIGN_FOO\020\000\022\017\n\013FOREIGN_BAR\020\001\022\017\n\013FOREIGN"
    "_BAZ\020\002b\006proto3", 2494);
  ::google::protobuf::MessageFactory::InternalRegisterGeneratedFile(
    "google/protobuf/unittest_no_field_presence.proto", &protobuf_RegisterTypes);
  ::protobuf_unittest::protobuf_AddDesc_google_2fprotobuf_2funittest_2eproto();
  ::google::protobuf::internal::OnShutdown(&protobuf_ShutdownFile_google_2fprotobuf_2funittest_5fno_5ffield_5fpresence_2eproto);
}

GOOGLE_PROTOBUF_DECLARE_ONCE(protobuf_AddDesc_google_2fprotobuf_2funittest_5fno_5ffield_5fpresence_2eproto_once_);
void protobuf_AddDesc_google_2fprotobuf_2funittest_5fno_5ffield_5fpresence_2eproto() {
  ::google::protobuf::GoogleOnceInit(&protobuf_AddDesc_google_2fprotobuf_2funittest_5fno_5ffield_5fpresence_2eproto_once_,
                 &protobuf_AddDesc_google_2fprotobuf_2funittest_5fno_5ffield_5fpresence_2eproto_impl);
}
// Force AddDescriptors() to be called at static initialization time.
struct StaticDescriptorInitializer_google_2fprotobuf_2funittest_5fno_5ffield_5fpresence_2eproto {
  StaticDescriptorInitializer_google_2fprotobuf_2funittest_5fno_5ffield_5fpresence_2eproto() {
    protobuf_AddDesc_google_2fprotobuf_2funittest_5fno_5ffield_5fpresence_2eproto();
  }
} static_descriptor_initializer_google_2fprotobuf_2funittest_5fno_5ffield_5fpresence_2eproto_;
const ::google::protobuf::EnumDescriptor* ForeignEnum_descriptor() {
  protobuf_AssignDescriptorsOnce();
  return ForeignEnum_descriptor_;
}
bool ForeignEnum_IsValid(int value) {
  switch (value) {
    case 0:
    case 1:
    case 2:
      return true;
    default:
      return false;
  }
}


namespace {

static void MergeFromFail(int line) GOOGLE_ATTRIBUTE_COLD GOOGLE_ATTRIBUTE_NORETURN;
static void MergeFromFail(int line) {
  ::google::protobuf::internal::MergeFromFail(__FILE__, line);
}

}  // namespace


// ===================================================================

const ::google::protobuf::EnumDescriptor* TestAllTypes_NestedEnum_descriptor() {
  protobuf_AssignDescriptorsOnce();
  return TestAllTypes_NestedEnum_descriptor_;
}
bool TestAllTypes_NestedEnum_IsValid(int value) {
  switch (value) {
    case 0:
    case 1:
    case 2:
      return true;
    default:
      return false;
  }
}

#if !defined(_MSC_VER) || _MSC_VER >= 1900
const TestAllTypes_NestedEnum TestAllTypes::FOO;
const TestAllTypes_NestedEnum TestAllTypes::BAR;
const TestAllTypes_NestedEnum TestAllTypes::BAZ;
const TestAllTypes_NestedEnum TestAllTypes::NestedEnum_MIN;
const TestAllTypes_NestedEnum TestAllTypes::NestedEnum_MAX;
const int TestAllTypes::NestedEnum_ARRAYSIZE;
#endif  // !defined(_MSC_VER) || _MSC_VER >= 1900
#if !defined(_MSC_VER) || _MSC_VER >= 1900
const int TestAllTypes_NestedMessage::kBbFieldNumber;
#endif  // !defined(_MSC_VER) || _MSC_VER >= 1900

TestAllTypes_NestedMessage::TestAllTypes_NestedMessage()
  : ::google::protobuf::Message(), _internal_metadata_(NULL) {
  if (this != internal_default_instance()) protobuf_InitDefaults_google_2fprotobuf_2funittest_5fno_5ffield_5fpresence_2eproto();
  SharedCtor();
  // @@protoc_insertion_point(constructor:proto2_nofieldpresence_unittest.TestAllTypes.NestedMessage)
}

void TestAllTypes_NestedMessage::InitAsDefaultInstance() {
}

TestAllTypes_NestedMessage::TestAllTypes_NestedMessage(const TestAllTypes_NestedMessage& from)
  : ::google::protobuf::Message(),
    _internal_metadata_(NULL) {
  SharedCtor();
  UnsafeMergeFrom(from);
  // @@protoc_insertion_point(copy_constructor:proto2_nofieldpresence_unittest.TestAllTypes.NestedMessage)
}

void TestAllTypes_NestedMessage::SharedCtor() {
  bb_ = 0;
  _cached_size_ = 0;
}

TestAllTypes_NestedMessage::~TestAllTypes_NestedMessage() {
  // @@protoc_insertion_point(destructor:proto2_nofieldpresence_unittest.TestAllTypes.NestedMessage)
  SharedDtor();
}

void TestAllTypes_NestedMessage::SharedDtor() {
}

void TestAllTypes_NestedMessage::SetCachedSize(int size) const {
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
}
const ::google::protobuf::Descriptor* TestAllTypes_NestedMessage::descriptor() {
  protobuf_AssignDescriptorsOnce();
  return TestAllTypes_NestedMessage_descriptor_;
}

const TestAllTypes_NestedMessage& TestAllTypes_NestedMessage::default_instance() {
  protobuf_InitDefaults_google_2fprotobuf_2funittest_5fno_5ffield_5fpresence_2eproto();
  return *internal_default_instance();
}

::google::protobuf::internal::ExplicitlyConstructed<TestAllTypes_NestedMessage> TestAllTypes_NestedMessage_default_instance_;

TestAllTypes_NestedMessage* TestAllTypes_NestedMessage::New(::google::protobuf::Arena* arena) const {
  TestAllTypes_NestedMessage* n = new TestAllTypes_NestedMessage;
  if (arena != NULL) {
    arena->Own(n);
  }
  return n;
}

void TestAllTypes_NestedMessage::Clear() {
// @@protoc_insertion_point(message_clear_start:proto2_nofieldpresence_unittest.TestAllTypes.NestedMessage)
  bb_ = 0;
}

bool TestAllTypes_NestedMessage::MergePartialFromCodedStream(
    ::google::protobuf::io::CodedInputStream* input) {
#define DO_(EXPRESSION) if (!GOOGLE_PREDICT_TRUE(EXPRESSION)) goto failure
  ::google::protobuf::uint32 tag;
  // @@protoc_insertion_point(parse_start:proto2_nofieldpresence_unittest.TestAllTypes.NestedMessage)
  for (;;) {
    ::std::pair< ::google::protobuf::uint32, bool> p = input->ReadTagWithCutoff(127);
    tag = p.first;
    if (!p.second) goto handle_unusual;
    switch (::google::protobuf::internal::WireFormatLite::GetTagFieldNumber(tag)) {
      // optional int32 bb = 1;
      case 1: {
        if (tag == 8) {

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int32, ::google::protobuf::internal::WireFormatLite::TYPE_INT32>(
                 input, &bb_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectAtEnd()) goto success;
        break;
      }

      default: {
      handle_unusual:
        if (tag == 0 ||
            ::google::protobuf::internal::WireFormatLite::GetTagWireType(tag) ==
            ::google::protobuf::internal::WireFormatLite::WIRETYPE_END_GROUP) {
          goto success;
        }
        DO_(::google::protobuf::internal::WireFormatLite::SkipField(input, tag));
        break;
      }
    }
  }
success:
  // @@protoc_insertion_point(parse_success:proto2_nofieldpresence_unittest.TestAllTypes.NestedMessage)
  return true;
failure:
  // @@protoc_insertion_point(parse_failure:proto2_nofieldpresence_unittest.TestAllTypes.NestedMessage)
  return false;
#undef DO_
}

void TestAllTypes_NestedMessage::SerializeWithCachedSizes(
    ::google::protobuf::io::CodedOutputStream* output) const {
  // @@protoc_insertion_point(serialize_start:proto2_nofieldpresence_unittest.TestAllTypes.NestedMessage)
  // optional int32 bb = 1;
  if (this->bb() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt32(1, this->bb(), output);
  }

  // @@protoc_insertion_point(serialize_end:proto2_nofieldpresence_unittest.TestAllTypes.NestedMessage)
}

::google::protobuf::uint8* TestAllTypes_NestedMessage::InternalSerializeWithCachedSizesToArray(
    bool deterministic, ::google::protobuf::uint8* target) const {
  (void)deterministic; // Unused
  // @@protoc_insertion_point(serialize_to_array_start:proto2_nofieldpresence_unittest.TestAllTypes.NestedMessage)
  // optional int32 bb = 1;
  if (this->bb() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt32ToArray(1, this->bb(), target);
  }

  // @@protoc_insertion_point(serialize_to_array_end:proto2_nofieldpresence_unittest.TestAllTypes.NestedMessage)
  return target;
}

size_t TestAllTypes_NestedMessage::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:proto2_nofieldpresence_unittest.TestAllTypes.NestedMessage)
  size_t total_size = 0;

  // optional int32 bb = 1;
  if (this->bb() != 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::Int32Size(
        this->bb());
  }

  int cached_size = ::google::protobuf::internal::ToCachedSize(total_size);
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = cached_size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
  return total_size;
}

void TestAllTypes_NestedMessage::MergeFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_merge_from_start:proto2_nofieldpresence_unittest.TestAllTypes.NestedMessage)
  if (GOOGLE_PREDICT_FALSE(&from == this)) MergeFromFail(__LINE__);
  const TestAllTypes_NestedMessage* source =
      ::google::protobuf::internal::DynamicCastToGenerated<const TestAllTypes_NestedMessage>(
          &from);
  if (source == NULL) {
  // @@protoc_insertion_point(generalized_merge_from_cast_fail:proto2_nofieldpresence_unittest.TestAllTypes.NestedMessage)
    ::google::protobuf::internal::ReflectionOps::Merge(from, this);
  } else {
  // @@protoc_insertion_point(generalized_merge_from_cast_success:proto2_nofieldpresence_unittest.TestAllTypes.NestedMessage)
    UnsafeMergeFrom(*source);
  }
}

void TestAllTypes_NestedMessage::MergeFrom(const TestAllTypes_NestedMessage& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:proto2_nofieldpresence_unittest.TestAllTypes.NestedMessage)
  if (GOOGLE_PREDICT_TRUE(&from != this)) {
    UnsafeMergeFrom(from);
  } else {
    MergeFromFail(__LINE__);
  }
}

void TestAllTypes_NestedMessage::UnsafeMergeFrom(const TestAllTypes_NestedMessage& from) {
  GOOGLE_DCHECK(&from != this);
  if (from.bb() != 0) {
    set_bb(from.bb());
  }
}

void TestAllTypes_NestedMessage::CopyFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_copy_from_start:proto2_nofieldpresence_unittest.TestAllTypes.NestedMessage)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

void TestAllTypes_NestedMessage::CopyFrom(const TestAllTypes_NestedMessage& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:proto2_nofieldpresence_unittest.TestAllTypes.NestedMessage)
  if (&from == this) return;
  Clear();
  UnsafeMergeFrom(from);
}

bool TestAllTypes_NestedMessage::IsInitialized() const {

  return true;
}

void TestAllTypes_NestedMessage::Swap(TestAllTypes_NestedMessage* other) {
  if (other == this) return;
  InternalSwap(other);
}
void TestAllTypes_NestedMessage::InternalSwap(TestAllTypes_NestedMessage* other) {
  std::swap(bb_, other->bb_);
  _internal_metadata_.Swap(&other->_internal_metadata_);
  std::swap(_cached_size_, other->_cached_size_);
}

::google::protobuf::Metadata TestAllTypes_NestedMessage::GetMetadata() const {
  protobuf_AssignDescriptorsOnce();
  ::google::protobuf::Metadata metadata;
  metadata.descriptor = TestAllTypes_NestedMessage_descriptor_;
  metadata.reflection = TestAllTypes_NestedMessage_reflection_;
  return metadata;
}


// -------------------------------------------------------------------

#if !defined(_MSC_VER) || _MSC_VER >= 1900
const int TestAllTypes::kOptionalInt32FieldNumber;
const int TestAllTypes::kOptionalInt64FieldNumber;
const int TestAllTypes::kOptionalUint32FieldNumber;
const int TestAllTypes::kOptionalUint64FieldNumber;
const int TestAllTypes::kOptionalSint32FieldNumber;
const int TestAllTypes::kOptionalSint64FieldNumber;
const int TestAllTypes::kOptionalFixed32FieldNumber;
const int TestAllTypes::kOptionalFixed64FieldNumber;
const int TestAllTypes::kOptionalSfixed32FieldNumber;
const int TestAllTypes::kOptionalSfixed64FieldNumber;
const int TestAllTypes::kOptionalFloatFieldNumber;
const int TestAllTypes::kOptionalDoubleFieldNumber;
const int TestAllTypes::kOptionalBoolFieldNumber;
const int TestAllTypes::kOptionalStringFieldNumber;
const int TestAllTypes::kOptionalBytesFieldNumber;
const int TestAllTypes::kOptionalNestedMessageFieldNumber;
const int TestAllTypes::kOptionalForeignMessageFieldNumber;
const int TestAllTypes::kOptionalProto2MessageFieldNumber;
const int TestAllTypes::kOptionalNestedEnumFieldNumber;
const int TestAllTypes::kOptionalForeignEnumFieldNumber;
const int TestAllTypes::kOptionalStringPieceFieldNumber;
const int TestAllTypes::kOptionalCordFieldNumber;
const int TestAllTypes::kOptionalLazyMessageFieldNumber;
const int TestAllTypes::kRepeatedInt32FieldNumber;
const int TestAllTypes::kRepeatedInt64FieldNumber;
const int TestAllTypes::kRepeatedUint32FieldNumber;
const int TestAllTypes::kRepeatedUint64FieldNumber;
const int TestAllTypes::kRepeatedSint32FieldNumber;
const int TestAllTypes::kRepeatedSint64FieldNumber;
const int TestAllTypes::kRepeatedFixed32FieldNumber;
const int TestAllTypes::kRepeatedFixed64FieldNumber;
const int TestAllTypes::kRepeatedSfixed32FieldNumber;
const int TestAllTypes::kRepeatedSfixed64FieldNumber;
const int TestAllTypes::kRepeatedFloatFieldNumber;
const int TestAllTypes::kRepeatedDoubleFieldNumber;
const int TestAllTypes::kRepeatedBoolFieldNumber;
const int TestAllTypes::kRepeatedStringFieldNumber;
const int TestAllTypes::kRepeatedBytesFieldNumber;
const int TestAllTypes::kRepeatedNestedMessageFieldNumber;
const int TestAllTypes::kRepeatedForeignMessageFieldNumber;
const int TestAllTypes::kRepeatedProto2MessageFieldNumber;
const int TestAllTypes::kRepeatedNestedEnumFieldNumber;
const int TestAllTypes::kRepeatedForeignEnumFieldNumber;
const int TestAllTypes::kRepeatedStringPieceFieldNumber;
const int TestAllTypes::kRepeatedCordFieldNumber;
const int TestAllTypes::kRepeatedLazyMessageFieldNumber;
const int TestAllTypes::kOneofUint32FieldNumber;
const int TestAllTypes::kOneofNestedMessageFieldNumber;
const int TestAllTypes::kOneofStringFieldNumber;
const int TestAllTypes::kOneofEnumFieldNumber;
#endif  // !defined(_MSC_VER) || _MSC_VER >= 1900

TestAllTypes::TestAllTypes()
  : ::google::protobuf::Message(), _internal_metadata_(NULL) {
  if (this != internal_default_instance()) protobuf_InitDefaults_google_2fprotobuf_2funittest_5fno_5ffield_5fpresence_2eproto();
  SharedCtor();
  // @@protoc_insertion_point(constructor:proto2_nofieldpresence_unittest.TestAllTypes)
}

void TestAllTypes::InitAsDefaultInstance() {
  optional_nested_message_ = const_cast< ::proto2_nofieldpresence_unittest::TestAllTypes_NestedMessage*>(
      ::proto2_nofieldpresence_unittest::TestAllTypes_NestedMessage::internal_default_instance());
  optional_foreign_message_ = const_cast< ::proto2_nofieldpresence_unittest::ForeignMessage*>(
      ::proto2_nofieldpresence_unittest::ForeignMessage::internal_default_instance());
  optional_proto2_message_ = const_cast< ::protobuf_unittest::TestAllTypes*>(
      ::protobuf_unittest::TestAllTypes::internal_default_instance());
  optional_lazy_message_ = const_cast< ::proto2_nofieldpresence_unittest::TestAllTypes_NestedMessage*>(
      ::proto2_nofieldpresence_unittest::TestAllTypes_NestedMessage::internal_default_instance());
  TestAllTypes_default_oneof_instance_->oneof_uint32_ = 0u;
  TestAllTypes_default_oneof_instance_->oneof_nested_message_ = const_cast< ::proto2_nofieldpresence_unittest::TestAllTypes_NestedMessage*>(
      ::proto2_nofieldpresence_unittest::TestAllTypes_NestedMessage::internal_default_instance());
  TestAllTypes_default_oneof_instance_->oneof_string_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  TestAllTypes_default_oneof_instance_->oneof_enum_ = 0;
}

TestAllTypes::TestAllTypes(const TestAllTypes& from)
  : ::google::protobuf::Message(),
    _internal_metadata_(NULL) {
  SharedCtor();
  UnsafeMergeFrom(from);
  // @@protoc_insertion_point(copy_constructor:proto2_nofieldpresence_unittest.TestAllTypes)
}

void TestAllTypes::SharedCtor() {
  optional_string_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  optional_bytes_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  optional_string_piece_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  optional_cord_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  optional_nested_message_ = NULL;
  optional_foreign_message_ = NULL;
  optional_proto2_message_ = NULL;
  optional_lazy_message_ = NULL;
  ::memset(&optional_int64_, 0, reinterpret_cast<char*>(&optional_foreign_enum_) -
    reinterpret_cast<char*>(&optional_int64_) + sizeof(optional_foreign_enum_));
  clear_has_oneof_field();
  _cached_size_ = 0;
}

TestAllTypes::~TestAllTypes() {
  // @@protoc_insertion_point(destructor:proto2_nofieldpresence_unittest.TestAllTypes)
  SharedDtor();
}

void TestAllTypes::SharedDtor() {
  optional_string_.DestroyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  optional_bytes_.DestroyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  optional_string_piece_.DestroyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  optional_cord_.DestroyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  if (has_oneof_field()) {
    clear_oneof_field();
  }
  if (this != &TestAllTypes_default_instance_.get()) {
    delete optional_nested_message_;
    delete optional_foreign_message_;
    delete optional_proto2_message_;
    delete optional_lazy_message_;
  }
}

void TestAllTypes::SetCachedSize(int size) const {
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
}
const ::google::protobuf::Descriptor* TestAllTypes::descriptor() {
  protobuf_AssignDescriptorsOnce();
  return TestAllTypes_descriptor_;
}

const TestAllTypes& TestAllTypes::default_instance() {
  protobuf_InitDefaults_google_2fprotobuf_2funittest_5fno_5ffield_5fpresence_2eproto();
  return *internal_default_instance();
}

::google::protobuf::internal::ExplicitlyConstructed<TestAllTypes> TestAllTypes_default_instance_;

TestAllTypes* TestAllTypes::New(::google::protobuf::Arena* arena) const {
  TestAllTypes* n = new TestAllTypes;
  if (arena != NULL) {
    arena->Own(n);
  }
  return n;
}

void TestAllTypes::clear_oneof_field() {
// @@protoc_insertion_point(one_of_clear_start:proto2_nofieldpresence_unittest.TestAllTypes)
  switch (oneof_field_case()) {
    case kOneofUint32: {
      // No need to clear
      break;
    }
    case kOneofNestedMessage: {
      delete oneof_field_.oneof_nested_message_;
      break;
    }
    case kOneofString: {
      oneof_field_.oneof_string_.DestroyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
      break;
    }
    case kOneofEnum: {
      // No need to clear
      break;
    }
    case ONEOF_FIELD_NOT_SET: {
      break;
    }
  }
  _oneof_case_[0] = ONEOF_FIELD_NOT_SET;
}


void TestAllTypes::Clear() {
// @@protoc_insertion_point(message_clear_start:proto2_nofieldpresence_unittest.TestAllTypes)
#if defined(__clang__)
#define ZR_HELPER_(f) \
  _Pragma("clang diagnostic push") \
  _Pragma("clang diagnostic ignored \"-Winvalid-offsetof\"") \
  __builtin_offsetof(TestAllTypes, f) \
  _Pragma("clang diagnostic pop")
#else
#define ZR_HELPER_(f) reinterpret_cast<char*>(\
  &reinterpret_cast<TestAllTypes*>(16)->f)
#endif

#define ZR_(first, last) do {\
  ::memset(&(first), 0,\
           ZR_HELPER_(last) - ZR_HELPER_(first) + sizeof(last));\
} while (0)

  ZR_(optional_int64_, optional_fixed64_);
  ZR_(optional_sfixed64_, optional_bool_);
  optional_string_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  optional_bytes_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  if (GetArenaNoVirtual() == NULL && optional_nested_message_ != NULL) delete optional_nested_message_;
  optional_nested_message_ = NULL;
  ZR_(optional_nested_enum_, optional_foreign_enum_);
  if (GetArenaNoVirtual() == NULL && optional_foreign_message_ != NULL) delete optional_foreign_message_;
  optional_foreign_message_ = NULL;
  if (GetArenaNoVirtual() == NULL && optional_proto2_message_ != NULL) delete optional_proto2_message_;
  optional_proto2_message_ = NULL;
  optional_string_piece_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  optional_cord_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  if (GetArenaNoVirtual() == NULL && optional_lazy_message_ != NULL) delete optional_lazy_message_;
  optional_lazy_message_ = NULL;

#undef ZR_HELPER_
#undef ZR_

  repeated_int32_.Clear();
  repeated_int64_.Clear();
  repeated_uint32_.Clear();
  repeated_uint64_.Clear();
  repeated_sint32_.Clear();
  repeated_sint64_.Clear();
  repeated_fixed32_.Clear();
  repeated_fixed64_.Clear();
  repeated_sfixed32_.Clear();
  repeated_sfixed64_.Clear();
  repeated_float_.Clear();
  repeated_double_.Clear();
  repeated_bool_.Clear();
  repeated_string_.Clear();
  repeated_bytes_.Clear();
  repeated_nested_message_.Clear();
  repeated_foreign_message_.Clear();
  repeated_proto2_message_.Clear();
  repeated_nested_enum_.Clear();
  repeated_foreign_enum_.Clear();
  repeated_string_piece_.Clear();
  repeated_cord_.Clear();
  repeated_lazy_message_.Clear();
  clear_oneof_field();
}

bool TestAllTypes::MergePartialFromCodedStream(
    ::google::protobuf::io::CodedInputStream* input) {
#define DO_(EXPRESSION) if (!GOOGLE_PREDICT_TRUE(EXPRESSION)) goto failure
  ::google::protobuf::uint32 tag;
  // @@protoc_insertion_point(parse_start:proto2_nofieldpresence_unittest.TestAllTypes)
  for (;;) {
    ::std::pair< ::google::protobuf::uint32, bool> p = input->ReadTagWithCutoff(16383);
    tag = p.first;
    if (!p.second) goto handle_unusual;
    switch (::google::protobuf::internal::WireFormatLite::GetTagFieldNumber(tag)) {
      // optional int32 optional_int32 = 1;
      case 1: {
        if (tag == 8) {

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int32, ::google::protobuf::internal::WireFormatLite::TYPE_INT32>(
                 input, &optional_int32_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(16)) goto parse_optional_int64;
        break;
      }

      // optional int64 optional_int64 = 2;
      case 2: {
        if (tag == 16) {
         parse_optional_int64:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_INT64>(
                 input, &optional_int64_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(24)) goto parse_optional_uint32;
        break;
      }

      // optional uint32 optional_uint32 = 3;
      case 3: {
        if (tag == 24) {
         parse_optional_uint32:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::uint32, ::google::protobuf::internal::WireFormatLite::TYPE_UINT32>(
                 input, &optional_uint32_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(32)) goto parse_optional_uint64;
        break;
      }

      // optional uint64 optional_uint64 = 4;
      case 4: {
        if (tag == 32) {
         parse_optional_uint64:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::uint64, ::google::protobuf::internal::WireFormatLite::TYPE_UINT64>(
                 input, &optional_uint64_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(40)) goto parse_optional_sint32;
        break;
      }

      // optional sint32 optional_sint32 = 5;
      case 5: {
        if (tag == 40) {
         parse_optional_sint32:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int32, ::google::protobuf::internal::WireFormatLite::TYPE_SINT32>(
                 input, &optional_sint32_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(48)) goto parse_optional_sint64;
        break;
      }

      // optional sint64 optional_sint64 = 6;
      case 6: {
        if (tag == 48) {
         parse_optional_sint64:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_SINT64>(
                 input, &optional_sint64_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(61)) goto parse_optional_fixed32;
        break;
      }

      // optional fixed32 optional_fixed32 = 7;
      case 7: {
        if (tag == 61) {
         parse_optional_fixed32:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::uint32, ::google::protobuf::internal::WireFormatLite::TYPE_FIXED32>(
                 input, &optional_fixed32_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(65)) goto parse_optional_fixed64;
        break;
      }

      // optional fixed64 optional_fixed64 = 8;
      case 8: {
        if (tag == 65) {
         parse_optional_fixed64:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::uint64, ::google::protobuf::internal::WireFormatLite::TYPE_FIXED64>(
                 input, &optional_fixed64_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(77)) goto parse_optional_sfixed32;
        break;
      }

      // optional sfixed32 optional_sfixed32 = 9;
      case 9: {
        if (tag == 77) {
         parse_optional_sfixed32:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int32, ::google::protobuf::internal::WireFormatLite::TYPE_SFIXED32>(
                 input, &optional_sfixed32_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(81)) goto parse_optional_sfixed64;
        break;
      }

      // optional sfixed64 optional_sfixed64 = 10;
      case 10: {
        if (tag == 81) {
         parse_optional_sfixed64:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_SFIXED64>(
                 input, &optional_sfixed64_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(93)) goto parse_optional_float;
        break;
      }

      // optional float optional_float = 11;
      case 11: {
        if (tag == 93) {
         parse_optional_float:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   float, ::google::protobuf::internal::WireFormatLite::TYPE_FLOAT>(
                 input, &optional_float_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(97)) goto parse_optional_double;
        break;
      }

      // optional double optional_double = 12;
      case 12: {
        if (tag == 97) {
         parse_optional_double:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   double, ::google::protobuf::internal::WireFormatLite::TYPE_DOUBLE>(
                 input, &optional_double_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(104)) goto parse_optional_bool;
        break;
      }

      // optional bool optional_bool = 13;
      case 13: {
        if (tag == 104) {
         parse_optional_bool:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   bool, ::google::protobuf::internal::WireFormatLite::TYPE_BOOL>(
                 input, &optional_bool_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(114)) goto parse_optional_string;
        break;
      }

      // optional string optional_string = 14;
      case 14: {
        if (tag == 114) {
         parse_optional_string:
          DO_(::google::protobuf::internal::WireFormatLite::ReadString(
                input, this->mutable_optional_string()));
          DO_(::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
            this->optional_string().data(), this->optional_string().length(),
            ::google::protobuf::internal::WireFormatLite::PARSE,
            "proto2_nofieldpresence_unittest.TestAllTypes.optional_string"));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(122)) goto parse_optional_bytes;
        break;
      }

      // optional bytes optional_bytes = 15;
      case 15: {
        if (tag == 122) {
         parse_optional_bytes:
          DO_(::google::protobuf::internal::WireFormatLite::ReadBytes(
                input, this->mutable_optional_bytes()));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(146)) goto parse_optional_nested_message;
        break;
      }

      // optional .proto2_nofieldpresence_unittest.TestAllTypes.NestedMessage optional_nested_message = 18;
      case 18: {
        if (tag == 146) {
         parse_optional_nested_message:
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessageNoVirtual(
               input, mutable_optional_nested_message()));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(154)) goto parse_optional_foreign_message;
        break;
      }

      // optional .proto2_nofieldpresence_unittest.ForeignMessage optional_foreign_message = 19;
      case 19: {
        if (tag == 154) {
         parse_optional_foreign_message:
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessageNoVirtual(
               input, mutable_optional_foreign_message()));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(162)) goto parse_optional_proto2_message;
        break;
      }

      // optional .protobuf_unittest.TestAllTypes optional_proto2_message = 20;
      case 20: {
        if (tag == 162) {
         parse_optional_proto2_message:
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessageNoVirtual(
               input, mutable_optional_proto2_message()));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(168)) goto parse_optional_nested_enum;
        break;
      }

      // optional .proto2_nofieldpresence_unittest.TestAllTypes.NestedEnum optional_nested_enum = 21;
      case 21: {
        if (tag == 168) {
         parse_optional_nested_enum:
          int value;
          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   int, ::google::protobuf::internal::WireFormatLite::TYPE_ENUM>(
                 input, &value)));
          set_optional_nested_enum(static_cast< ::proto2_nofieldpresence_unittest::TestAllTypes_NestedEnum >(value));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(176)) goto parse_optional_foreign_enum;
        break;
      }

      // optional .proto2_nofieldpresence_unittest.ForeignEnum optional_foreign_enum = 22;
      case 22: {
        if (tag == 176) {
         parse_optional_foreign_enum:
          int value;
          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   int, ::google::protobuf::internal::WireFormatLite::TYPE_ENUM>(
                 input, &value)));
          set_optional_foreign_enum(static_cast< ::proto2_nofieldpresence_unittest::ForeignEnum >(value));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(194)) goto parse_optional_string_piece;
        break;
      }

      // optional string optional_string_piece = 24 [ctype = STRING_PIECE];
      case 24: {
        if (tag == 194) {
         parse_optional_string_piece:
          DO_(::google::protobuf::internal::WireFormatLite::ReadString(
                input, this->mutable_optional_string_piece()));
          DO_(::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
            this->optional_string_piece().data(), this->optional_string_piece().length(),
            ::google::protobuf::internal::WireFormatLite::PARSE,
            "proto2_nofieldpresence_unittest.TestAllTypes.optional_string_piece"));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(202)) goto parse_optional_cord;
        break;
      }

      // optional string optional_cord = 25 [ctype = CORD];
      case 25: {
        if (tag == 202) {
         parse_optional_cord:
          DO_(::google::protobuf::internal::WireFormatLite::ReadString(
                input, this->mutable_optional_cord()));
          DO_(::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
            this->optional_cord().data(), this->optional_cord().length(),
            ::google::protobuf::internal::WireFormatLite::PARSE,
            "proto2_nofieldpresence_unittest.TestAllTypes.optional_cord"));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(242)) goto parse_optional_lazy_message;
        break;
      }

      // optional .proto2_nofieldpresence_unittest.TestAllTypes.NestedMessage optional_lazy_message = 30 [lazy = true];
      case 30: {
        if (tag == 242) {
         parse_optional_lazy_message:
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessageNoVirtual(
               input, mutable_optional_lazy_message()));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(250)) goto parse_repeated_int32;
        break;
      }

      // repeated int32 repeated_int32 = 31;
      case 31: {
        if (tag == 250) {
         parse_repeated_int32:
          DO_((::google::protobuf::internal::WireFormatLite::ReadPackedPrimitive<
                   ::google::protobuf::int32, ::google::protobuf::internal::WireFormatLite::TYPE_INT32>(
                 input, this->mutable_repeated_int32())));
        } else if (tag == 248) {
          DO_((::google::protobuf::internal::WireFormatLite::ReadRepeatedPrimitiveNoInline<
                   ::google::protobuf::int32, ::google::protobuf::internal::WireFormatLite::TYPE_INT32>(
                 2, 250, input, this->mutable_repeated_int32())));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(258)) goto parse_repeated_int64;
        break;
      }

      // repeated int64 repeated_int64 = 32;
      case 32: {
        if (tag == 258) {
         parse_repeated_int64:
          DO_((::google::protobuf::internal::WireFormatLite::ReadPackedPrimitive<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_INT64>(
                 input, this->mutable_repeated_int64())));
        } else if (tag == 256) {
          DO_((::google::protobuf::internal::WireFormatLite::ReadRepeatedPrimitiveNoInline<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_INT64>(
                 2, 258, input, this->mutable_repeated_int64())));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(266)) goto parse_repeated_uint32;
        break;
      }

      // repeated uint32 repeated_uint32 = 33;
      case 33: {
        if (tag == 266) {
         parse_repeated_uint32:
          DO_((::google::protobuf::internal::WireFormatLite::ReadPackedPrimitive<
                   ::google::protobuf::uint32, ::google::protobuf::internal::WireFormatLite::TYPE_UINT32>(
                 input, this->mutable_repeated_uint32())));
        } else if (tag == 264) {
          DO_((::google::protobuf::internal::WireFormatLite::ReadRepeatedPrimitiveNoInline<
                   ::google::protobuf::uint32, ::google::protobuf::internal::WireFormatLite::TYPE_UINT32>(
                 2, 266, input, this->mutable_repeated_uint32())));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(274)) goto parse_repeated_uint64;
        break;
      }

      // repeated uint64 repeated_uint64 = 34;
      case 34: {
        if (tag == 274) {
         parse_repeated_uint64:
          DO_((::google::protobuf::internal::WireFormatLite::ReadPackedPrimitive<
                   ::google::protobuf::uint64, ::google::protobuf::internal::WireFormatLite::TYPE_UINT64>(
                 input, this->mutable_repeated_uint64())));
        } else if (tag == 272) {
          DO_((::google::protobuf::internal::WireFormatLite::ReadRepeatedPrimitiveNoInline<
                   ::google::protobuf::uint64, ::google::protobuf::internal::WireFormatLite::TYPE_UINT64>(
                 2, 274, input, this->mutable_repeated_uint64())));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(282)) goto parse_repeated_sint32;
        break;
      }

      // repeated sint32 repeated_sint32 = 35;
      case 35: {
        if (tag == 282) {
         parse_repeated_sint32:
          DO_((::google::protobuf::internal::WireFormatLite::ReadPackedPrimitive<
                   ::google::protobuf::int32, ::google::protobuf::internal::WireFormatLite::TYPE_SINT32>(
                 input, this->mutable_repeated_sint32())));
        } else if (tag == 280) {
          DO_((::google::protobuf::internal::WireFormatLite::ReadRepeatedPrimitiveNoInline<
                   ::google::protobuf::int32, ::google::protobuf::internal::WireFormatLite::TYPE_SINT32>(
                 2, 282, input, this->mutable_repeated_sint32())));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(290)) goto parse_repeated_sint64;
        break;
      }

      // repeated sint64 repeated_sint64 = 36;
      case 36: {
        if (tag == 290) {
         parse_repeated_sint64:
          DO_((::google::protobuf::internal::WireFormatLite::ReadPackedPrimitive<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_SINT64>(
                 input, this->mutable_repeated_sint64())));
        } else if (tag == 288) {
          DO_((::google::protobuf::internal::WireFormatLite::ReadRepeatedPrimitiveNoInline<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_SINT64>(
                 2, 290, input, this->mutable_repeated_sint64())));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(298)) goto parse_repeated_fixed32;
        break;
      }

      // repeated fixed32 repeated_fixed32 = 37;
      case 37: {
        if (tag == 298) {
         parse_repeated_fixed32:
          DO_((::google::protobuf::internal::WireFormatLite::ReadPackedPrimitive<
                   ::google::protobuf::uint32, ::google::protobuf::internal::WireFormatLite::TYPE_FIXED32>(
                 input, this->mutable_repeated_fixed32())));
        } else if (tag == 301) {
          DO_((::google::protobuf::internal::WireFormatLite::ReadRepeatedPrimitiveNoInline<
                   ::google::protobuf::uint32, ::google::protobuf::internal::WireFormatLite::TYPE_FIXED32>(
                 2, 298, input, this->mutable_repeated_fixed32())));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(306)) goto parse_repeated_fixed64;
        break;
      }

      // repeated fixed64 repeated_fixed64 = 38;
      case 38: {
        if (tag == 306) {
         parse_repeated_fixed64:
          DO_((::google::protobuf::internal::WireFormatLite::ReadPackedPrimitive<
                   ::google::protobuf::uint64, ::google::protobuf::internal::WireFormatLite::TYPE_FIXED64>(
                 input, this->mutable_repeated_fixed64())));
        } else if (tag == 305) {
          DO_((::google::protobuf::internal::WireFormatLite::ReadRepeatedPrimitiveNoInline<
                   ::google::protobuf::uint64, ::google::protobuf::internal::WireFormatLite::TYPE_FIXED64>(
                 2, 306, input, this->mutable_repeated_fixed64())));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(314)) goto parse_repeated_sfixed32;
        break;
      }

      // repeated sfixed32 repeated_sfixed32 = 39;
      case 39: {
        if (tag == 314) {
         parse_repeated_sfixed32:
          DO_((::google::protobuf::internal::WireFormatLite::ReadPackedPrimitive<
                   ::google::protobuf::int32, ::google::protobuf::internal::WireFormatLite::TYPE_SFIXED32>(
                 input, this->mutable_repeated_sfixed32())));
        } else if (tag == 317) {
          DO_((::google::protobuf::internal::WireFormatLite::ReadRepeatedPrimitiveNoInline<
                   ::google::protobuf::int32, ::google::protobuf::internal::WireFormatLite::TYPE_SFIXED32>(
                 2, 314, input, this->mutable_repeated_sfixed32())));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(322)) goto parse_repeated_sfixed64;
        break;
      }

      // repeated sfixed64 repeated_sfixed64 = 40;
      case 40: {
        if (tag == 322) {
         parse_repeated_sfixed64:
          DO_((::google::protobuf::internal::WireFormatLite::ReadPackedPrimitive<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_SFIXED64>(
                 input, this->mutable_repeated_sfixed64())));
        } else if (tag == 321) {
          DO_((::google::protobuf::internal::WireFormatLite::ReadRepeatedPrimitiveNoInline<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_SFIXED64>(
                 2, 322, input, this->mutable_repeated_sfixed64())));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(330)) goto parse_repeated_float;
        break;
      }

      // repeated float repeated_float = 41;
      case 41: {
        if (tag == 330) {
         parse_repeated_float:
          DO_((::google::protobuf::internal::WireFormatLite::ReadPackedPrimitive<
                   float, ::google::protobuf::internal::WireFormatLite::TYPE_FLOAT>(
                 input, this->mutable_repeated_float())));
        } else if (tag == 333) {
          DO_((::google::protobuf::internal::WireFormatLite::ReadRepeatedPrimitiveNoInline<
                   float, ::google::protobuf::internal::WireFormatLite::TYPE_FLOAT>(
                 2, 330, input, this->mutable_repeated_float())));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(338)) goto parse_repeated_double;
        break;
      }

      // repeated double repeated_double = 42;
      case 42: {
        if (tag == 338) {
         parse_repeated_double:
          DO_((::google::protobuf::internal::WireFormatLite::ReadPackedPrimitive<
                   double, ::google::protobuf::internal::WireFormatLite::TYPE_DOUBLE>(
                 input, this->mutable_repeated_double())));
        } else if (tag == 337) {
          DO_((::google::protobuf::internal::WireFormatLite::ReadRepeatedPrimitiveNoInline<
                   double, ::google::protobuf::internal::WireFormatLite::TYPE_DOUBLE>(
                 2, 338, input, this->mutable_repeated_double())));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(346)) goto parse_repeated_bool;
        break;
      }

      // repeated bool repeated_bool = 43;
      case 43: {
        if (tag == 346) {
         parse_repeated_bool:
          DO_((::google::protobuf::internal::WireFormatLite::ReadPackedPrimitive<
                   bool, ::google::protobuf::internal::WireFormatLite::TYPE_BOOL>(
                 input, this->mutable_repeated_bool())));
        } else if (tag == 344) {
          DO_((::google::protobuf::internal::WireFormatLite::ReadRepeatedPrimitiveNoInline<
                   bool, ::google::protobuf::internal::WireFormatLite::TYPE_BOOL>(
                 2, 346, input, this->mutable_repeated_bool())));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(354)) goto parse_repeated_string;
        break;
      }

      // repeated string repeated_string = 44;
      case 44: {
        if (tag == 354) {
         parse_repeated_string:
          DO_(::google::protobuf::internal::WireFormatLite::ReadString(
                input, this->add_repeated_string()));
          DO_(::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
            this->repeated_string(this->repeated_string_size() - 1).data(),
            this->repeated_string(this->repeated_string_size() - 1).length(),
            ::google::protobuf::internal::WireFormatLite::PARSE,
            "proto2_nofieldpresence_unittest.TestAllTypes.repeated_string"));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(354)) goto parse_repeated_string;
        if (input->ExpectTag(362)) goto parse_repeated_bytes;
        break;
      }

      // repeated bytes repeated_bytes = 45;
      case 45: {
        if (tag == 362) {
         parse_repeated_bytes:
          DO_(::google::protobuf::internal::WireFormatLite::ReadBytes(
                input, this->add_repeated_bytes()));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(362)) goto parse_repeated_bytes;
        if (input->ExpectTag(386)) goto parse_repeated_nested_message;
        break;
      }

      // repeated .proto2_nofieldpresence_unittest.TestAllTypes.NestedMessage repeated_nested_message = 48;
      case 48: {
        if (tag == 386) {
         parse_repeated_nested_message:
          DO_(input->IncrementRecursionDepth());
         parse_loop_repeated_nested_message:
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessageNoVirtualNoRecursionDepth(
                input, add_repeated_nested_message()));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(386)) goto parse_loop_repeated_nested_message;
        if (input->ExpectTag(394)) goto parse_loop_repeated_foreign_message;
        input->UnsafeDecrementRecursionDepth();
        break;
      }

      // repeated .proto2_nofieldpresence_unittest.ForeignMessage repeated_foreign_message = 49;
      case 49: {
        if (tag == 394) {
          DO_(input->IncrementRecursionDepth());
         parse_loop_repeated_foreign_message:
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessageNoVirtualNoRecursionDepth(
                input, add_repeated_foreign_message()));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(394)) goto parse_loop_repeated_foreign_message;
        if (input->ExpectTag(402)) goto parse_loop_repeated_proto2_message;
        input->UnsafeDecrementRecursionDepth();
        break;
      }

      // repeated .protobuf_unittest.TestAllTypes repeated_proto2_message = 50;
      case 50: {
        if (tag == 402) {
          DO_(input->IncrementRecursionDepth());
         parse_loop_repeated_proto2_message:
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessageNoVirtualNoRecursionDepth(
                input, add_repeated_proto2_message()));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(402)) goto parse_loop_repeated_proto2_message;
        input->UnsafeDecrementRecursionDepth();
        if (input->ExpectTag(410)) goto parse_repeated_nested_enum;
        break;
      }

      // repeated .proto2_nofieldpresence_unittest.TestAllTypes.NestedEnum repeated_nested_enum = 51;
      case 51: {
        if (tag == 410) {
         parse_repeated_nested_enum:
          ::google::protobuf::uint32 length;
          DO_(input->ReadVarint32(&length));
          ::google::protobuf::io::CodedInputStream::Limit limit = input->PushLimit(length);
          while (input->BytesUntilLimit() > 0) {
            int value;
            DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   int, ::google::protobuf::internal::WireFormatLite::TYPE_ENUM>(
                 input, &value)));
            add_repeated_nested_enum(static_cast< ::proto2_nofieldpresence_unittest::TestAllTypes_NestedEnum >(value));
          }
          input->PopLimit(limit);
        } else if (tag == 408) {
          int value;
          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   int, ::google::protobuf::internal::WireFormatLite::TYPE_ENUM>(
                 input, &value)));
          add_repeated_nested_enum(static_cast< ::proto2_nofieldpresence_unittest::TestAllTypes_NestedEnum >(value));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(418)) goto parse_repeated_foreign_enum;
        break;
      }

      // repeated .proto2_nofieldpresence_unittest.ForeignEnum repeated_foreign_enum = 52;
      case 52: {
        if (tag == 418) {
         parse_repeated_foreign_enum:
          ::google::protobuf::uint32 length;
          DO_(input->ReadVarint32(&length));
          ::google::protobuf::io::CodedInputStream::Limit limit = input->PushLimit(length);
          while (input->BytesUntilLimit() > 0) {
            int value;
            DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   int, ::google::protobuf::internal::WireFormatLite::TYPE_ENUM>(
                 input, &value)));
            add_repeated_foreign_enum(static_cast< ::proto2_nofieldpresence_unittest::ForeignEnum >(value));
          }
          input->PopLimit(limit);
        } else if (tag == 416) {
          int value;
          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   int, ::google::protobuf::internal::WireFormatLite::TYPE_ENUM>(
                 input, &value)));
          add_repeated_foreign_enum(static_cast< ::proto2_nofieldpresence_unittest::ForeignEnum >(value));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(434)) goto parse_repeated_string_piece;
        break;
      }

      // repeated string repeated_string_piece = 54 [ctype = STRING_PIECE];
      case 54: {
        if (tag == 434) {
         parse_repeated_string_piece:
          DO_(::google::protobuf::internal::WireFormatLite::ReadString(
                input, this->add_repeated_string_piece()));
          DO_(::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
            this->repeated_string_piece(this->repeated_string_piece_size() - 1).data(),
            this->repeated_string_piece(this->repeated_string_piece_size() - 1).length(),
            ::google::protobuf::internal::WireFormatLite::PARSE,
            "proto2_nofieldpresence_unittest.TestAllTypes.repeated_string_piece"));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(434)) goto parse_repeated_string_piece;
        if (input->ExpectTag(442)) goto parse_repeated_cord;
        break;
      }

      // repeated string repeated_cord = 55 [ctype = CORD];
      case 55: {
        if (tag == 442) {
         parse_repeated_cord:
          DO_(::google::protobuf::internal::WireFormatLite::ReadString(
                input, this->add_repeated_cord()));
          DO_(::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
            this->repeated_cord(this->repeated_cord_size() - 1).data(),
            this->repeated_cord(this->repeated_cord_size() - 1).length(),
            ::google::protobuf::internal::WireFormatLite::PARSE,
            "proto2_nofieldpresence_unittest.TestAllTypes.repeated_cord"));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(442)) goto parse_repeated_cord;
        if (input->ExpectTag(458)) goto parse_repeated_lazy_message;
        break;
      }

      // repeated .proto2_nofieldpresence_unittest.TestAllTypes.NestedMessage repeated_lazy_message = 57 [lazy = true];
      case 57: {
        if (tag == 458) {
         parse_repeated_lazy_message:
          DO_(input->IncrementRecursionDepth());
         parse_loop_repeated_lazy_message:
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessageNoVirtualNoRecursionDepth(
                input, add_repeated_lazy_message()));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(458)) goto parse_loop_repeated_lazy_message;
        input->UnsafeDecrementRecursionDepth();
        if (input->ExpectTag(888)) goto parse_oneof_uint32;
        break;
      }

      // optional uint32 oneof_uint32 = 111;
      case 111: {
        if (tag == 888) {
         parse_oneof_uint32:
          clear_oneof_field();
          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::uint32, ::google::protobuf::internal::WireFormatLite::TYPE_UINT32>(
                 input, &oneof_field_.oneof_uint32_)));
          set_has_oneof_uint32();
        } else {
          goto handle_unusual;
        }
        goto after_oneof_enum;
        break;
      }

      // optional .proto2_nofieldpresence_unittest.TestAllTypes.NestedMessage oneof_nested_message = 112;
      case 112: {
        if (tag == 898) {
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessageNoVirtual(
               input, mutable_oneof_nested_message()));
        } else {
          goto handle_unusual;
        }
        goto after_oneof_enum;
        break;
      }

      // optional string oneof_string = 113;
      case 113: {
        if (tag == 906) {
          DO_(::google::protobuf::internal::WireFormatLite::ReadString(
                input, this->mutable_oneof_string()));
          DO_(::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
            this->oneof_string().data(), this->oneof_string().length(),
            ::google::protobuf::internal::WireFormatLite::PARSE,
            "proto2_nofieldpresence_unittest.TestAllTypes.oneof_string"));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(912)) goto parse_oneof_enum;
        break;
      }

      // optional .proto2_nofieldpresence_unittest.TestAllTypes.NestedEnum oneof_enum = 114;
      case 114: {
        if (tag == 912) {
         parse_oneof_enum:
          int value;
          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   int, ::google::protobuf::internal::WireFormatLite::TYPE_ENUM>(
                 input, &value)));
          set_oneof_enum(static_cast< ::proto2_nofieldpresence_unittest::TestAllTypes_NestedEnum >(value));
        } else {
          goto handle_unusual;
        }
       after_oneof_enum:
        if (input->ExpectAtEnd()) goto success;
        break;
      }

      default: {
      handle_unusual:
        if (tag == 0 ||
            ::google::protobuf::internal::WireFormatLite::GetTagWireType(tag) ==
            ::google::protobuf::internal::WireFormatLite::WIRETYPE_END_GROUP) {
          goto success;
        }
        DO_(::google::protobuf::internal::WireFormatLite::SkipField(input, tag));
        break;
      }
    }
  }
success:
  // @@protoc_insertion_point(parse_success:proto2_nofieldpresence_unittest.TestAllTypes)
  return true;
failure:
  // @@protoc_insertion_point(parse_failure:proto2_nofieldpresence_unittest.TestAllTypes)
  return false;
#undef DO_
}

void TestAllTypes::SerializeWithCachedSizes(
    ::google::protobuf::io::CodedOutputStream* output) const {
  // @@protoc_insertion_point(serialize_start:proto2_nofieldpresence_unittest.TestAllTypes)
  // optional int32 optional_int32 = 1;
  if (this->optional_int32() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt32(1, this->optional_int32(), output);
  }

  // optional int64 optional_int64 = 2;
  if (this->optional_int64() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt64(2, this->optional_int64(), output);
  }

  // optional uint32 optional_uint32 = 3;
  if (this->optional_uint32() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteUInt32(3, this->optional_uint32(), output);
  }

  // optional uint64 optional_uint64 = 4;
  if (this->optional_uint64() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteUInt64(4, this->optional_uint64(), output);
  }

  // optional sint32 optional_sint32 = 5;
  if (this->optional_sint32() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteSInt32(5, this->optional_sint32(), output);
  }

  // optional sint64 optional_sint64 = 6;
  if (this->optional_sint64() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteSInt64(6, this->optional_sint64(), output);
  }

  // optional fixed32 optional_fixed32 = 7;
  if (this->optional_fixed32() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteFixed32(7, this->optional_fixed32(), output);
  }

  // optional fixed64 optional_fixed64 = 8;
  if (this->optional_fixed64() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteFixed64(8, this->optional_fixed64(), output);
  }

  // optional sfixed32 optional_sfixed32 = 9;
  if (this->optional_sfixed32() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteSFixed32(9, this->optional_sfixed32(), output);
  }

  // optional sfixed64 optional_sfixed64 = 10;
  if (this->optional_sfixed64() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteSFixed64(10, this->optional_sfixed64(), output);
  }

  // optional float optional_float = 11;
  if (this->optional_float() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteFloat(11, this->optional_float(), output);
  }

  // optional double optional_double = 12;
  if (this->optional_double() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteDouble(12, this->optional_double(), output);
  }

  // optional bool optional_bool = 13;
  if (this->optional_bool() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteBool(13, this->optional_bool(), output);
  }

  // optional string optional_string = 14;
  if (this->optional_string().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->optional_string().data(), this->optional_string().length(),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "proto2_nofieldpresence_unittest.TestAllTypes.optional_string");
    ::google::protobuf::internal::WireFormatLite::WriteStringMaybeAliased(
      14, this->optional_string(), output);
  }

  // optional bytes optional_bytes = 15;
  if (this->optional_bytes().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::WriteBytesMaybeAliased(
      15, this->optional_bytes(), output);
  }

  // optional .proto2_nofieldpresence_unittest.TestAllTypes.NestedMessage optional_nested_message = 18;
  if (this->has_optional_nested_message()) {
    ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
      18, *this->optional_nested_message_, output);
  }

  // optional .proto2_nofieldpresence_unittest.ForeignMessage optional_foreign_message = 19;
  if (this->has_optional_foreign_message()) {
    ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
      19, *this->optional_foreign_message_, output);
  }

  // optional .protobuf_unittest.TestAllTypes optional_proto2_message = 20;
  if (this->has_optional_proto2_message()) {
    ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
      20, *this->optional_proto2_message_, output);
  }

  // optional .proto2_nofieldpresence_unittest.TestAllTypes.NestedEnum optional_nested_enum = 21;
  if (this->optional_nested_enum() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteEnum(
      21, this->optional_nested_enum(), output);
  }

  // optional .proto2_nofieldpresence_unittest.ForeignEnum optional_foreign_enum = 22;
  if (this->optional_foreign_enum() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteEnum(
      22, this->optional_foreign_enum(), output);
  }

  // optional string optional_string_piece = 24 [ctype = STRING_PIECE];
  if (this->optional_string_piece().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->optional_string_piece().data(), this->optional_string_piece().length(),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "proto2_nofieldpresence_unittest.TestAllTypes.optional_string_piece");
    ::google::protobuf::internal::WireFormatLite::WriteStringMaybeAliased(
      24, this->optional_string_piece(), output);
  }

  // optional string optional_cord = 25 [ctype = CORD];
  if (this->optional_cord().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->optional_cord().data(), this->optional_cord().length(),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "proto2_nofieldpresence_unittest.TestAllTypes.optional_cord");
    ::google::protobuf::internal::WireFormatLite::WriteStringMaybeAliased(
      25, this->optional_cord(), output);
  }

  // optional .proto2_nofieldpresence_unittest.TestAllTypes.NestedMessage optional_lazy_message = 30 [lazy = true];
  if (this->has_optional_lazy_message()) {
    ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
      30, *this->optional_lazy_message_, output);
  }

  // repeated int32 repeated_int32 = 31;
  if (this->repeated_int32_size() > 0) {
    ::google::protobuf::internal::WireFormatLite::WriteTag(31, ::google::protobuf::internal::WireFormatLite::WIRETYPE_LENGTH_DELIMITED, output);
    output->WriteVarint32(_repeated_int32_cached_byte_size_);
  }
  for (int i = 0; i < this->repeated_int32_size(); i++) {
    ::google::protobuf::internal::WireFormatLite::WriteInt32NoTag(
      this->repeated_int32(i), output);
  }

  // repeated int64 repeated_int64 = 32;
  if (this->repeated_int64_size() > 0) {
    ::google::protobuf::internal::WireFormatLite::WriteTag(32, ::google::protobuf::internal::WireFormatLite::WIRETYPE_LENGTH_DELIMITED, output);
    output->WriteVarint32(_repeated_int64_cached_byte_size_);
  }
  for (int i = 0; i < this->repeated_int64_size(); i++) {
    ::google::protobuf::internal::WireFormatLite::WriteInt64NoTag(
      this->repeated_int64(i), output);
  }

  // repeated uint32 repeated_uint32 = 33;
  if (this->repeated_uint32_size() > 0) {
    ::google::protobuf::internal::WireFormatLite::WriteTag(33, ::google::protobuf::internal::WireFormatLite::WIRETYPE_LENGTH_DELIMITED, output);
    output->WriteVarint32(_repeated_uint32_cached_byte_size_);
  }
  for (int i = 0; i < this->repeated_uint32_size(); i++) {
    ::google::protobuf::internal::WireFormatLite::WriteUInt32NoTag(
      this->repeated_uint32(i), output);
  }

  // repeated uint64 repeated_uint64 = 34;
  if (this->repeated_uint64_size() > 0) {
    ::google::protobuf::internal::WireFormatLite::WriteTag(34, ::google::protobuf::internal::WireFormatLite::WIRETYPE_LENGTH_DELIMITED, output);
    output->WriteVarint32(_repeated_uint64_cached_byte_size_);
  }
  for (int i = 0; i < this->repeated_uint64_size(); i++) {
    ::google::protobuf::internal::WireFormatLite::WriteUInt64NoTag(
      this->repeated_uint64(i), output);
  }

  // repeated sint32 repeated_sint32 = 35;
  if (this->repeated_sint32_size() > 0) {
    ::google::protobuf::internal::WireFormatLite::WriteTag(35, ::google::protobuf::internal::WireFormatLite::WIRETYPE_LENGTH_DELIMITED, output);
    output->WriteVarint32(_repeated_sint32_cached_byte_size_);
  }
  for (int i = 0; i < this->repeated_sint32_size(); i++) {
    ::google::protobuf::internal::WireFormatLite::WriteSInt32NoTag(
      this->repeated_sint32(i), output);
  }

  // repeated sint64 repeated_sint64 = 36;
  if (this->repeated_sint64_size() > 0) {
    ::google::protobuf::internal::WireFormatLite::WriteTag(36, ::google::protobuf::internal::WireFormatLite::WIRETYPE_LENGTH_DELIMITED, output);
    output->WriteVarint32(_repeated_sint64_cached_byte_size_);
  }
  for (int i = 0; i < this->repeated_sint64_size(); i++) {
    ::google::protobuf::internal::WireFormatLite::WriteSInt64NoTag(
      this->repeated_sint64(i), output);
  }

  // repeated fixed32 repeated_fixed32 = 37;
  if (this->repeated_fixed32_size() > 0) {
    ::google::protobuf::internal::WireFormatLite::WriteTag(37, ::google::protobuf::internal::WireFormatLite::WIRETYPE_LENGTH_DELIMITED, output);
    output->WriteVarint32(_repeated_fixed32_cached_byte_size_);
  }
  for (int i = 0; i < this->repeated_fixed32_size(); i++) {
    ::google::protobuf::internal::WireFormatLite::WriteFixed32NoTag(
      this->repeated_fixed32(i), output);
  }

  // repeated fixed64 repeated_fixed64 = 38;
  if (this->repeated_fixed64_size() > 0) {
    ::google::protobuf::internal::WireFormatLite::WriteTag(38, ::google::protobuf::internal::WireFormatLite::WIRETYPE_LENGTH_DELIMITED, output);
    output->WriteVarint32(_repeated_fixed64_cached_byte_size_);
  }
  for (int i = 0; i < this->repeated_fixed64_size(); i++) {
    ::google::protobuf::internal::WireFormatLite::WriteFixed64NoTag(
      this->repeated_fixed64(i), output);
  }

  // repeated sfixed32 repeated_sfixed32 = 39;
  if (this->repeated_sfixed32_size() > 0) {
    ::google::protobuf::internal::WireFormatLite::WriteTag(39, ::google::protobuf::internal::WireFormatLite::WIRETYPE_LENGTH_DELIMITED, output);
    output->WriteVarint32(_repeated_sfixed32_cached_byte_size_);
  }
  for (int i = 0; i < this->repeated_sfixed32_size(); i++) {
    ::google::protobuf::internal::WireFormatLite::WriteSFixed32NoTag(
      this->repeated_sfixed32(i), output);
  }

  // repeated sfixed64 repeated_sfixed64 = 40;
  if (this->repeated_sfixed64_size() > 0) {
    ::google::protobuf::internal::WireFormatLite::WriteTag(40, ::google::protobuf::internal::WireFormatLite::WIRETYPE_LENGTH_DELIMITED, output);
    output->WriteVarint32(_repeated_sfixed64_cached_byte_size_);
  }
  for (int i = 0; i < this->repeated_sfixed64_size(); i++) {
    ::google::protobuf::internal::WireFormatLite::WriteSFixed64NoTag(
      this->repeated_sfixed64(i), output);
  }

  // repeated float repeated_float = 41;
  if (this->repeated_float_size() > 0) {
    ::google::protobuf::internal::WireFormatLite::WriteTag(41, ::google::protobuf::internal::WireFormatLite::WIRETYPE_LENGTH_DELIMITED, output);
    output->WriteVarint32(_repeated_float_cached_byte_size_);
  }
  for (int i = 0; i < this->repeated_float_size(); i++) {
    ::google::protobuf::internal::WireFormatLite::WriteFloatNoTag(
      this->repeated_float(i), output);
  }

  // repeated double repeated_double = 42;
  if (this->repeated_double_size() > 0) {
    ::google::protobuf::internal::WireFormatLite::WriteTag(42, ::google::protobuf::internal::WireFormatLite::WIRETYPE_LENGTH_DELIMITED, output);
    output->WriteVarint32(_repeated_double_cached_byte_size_);
  }
  for (int i = 0; i < this->repeated_double_size(); i++) {
    ::google::protobuf::internal::WireFormatLite::WriteDoubleNoTag(
      this->repeated_double(i), output);
  }

  // repeated bool repeated_bool = 43;
  if (this->repeated_bool_size() > 0) {
    ::google::protobuf::internal::WireFormatLite::WriteTag(43, ::google::protobuf::internal::WireFormatLite::WIRETYPE_LENGTH_DELIMITED, output);
    output->WriteVarint32(_repeated_bool_cached_byte_size_);
  }
  for (int i = 0; i < this->repeated_bool_size(); i++) {
    ::google::protobuf::internal::WireFormatLite::WriteBoolNoTag(
      this->repeated_bool(i), output);
  }

  // repeated string repeated_string = 44;
  for (int i = 0; i < this->repeated_string_size(); i++) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->repeated_string(i).data(), this->repeated_string(i).length(),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "proto2_nofieldpresence_unittest.TestAllTypes.repeated_string");
    ::google::protobuf::internal::WireFormatLite::WriteString(
      44, this->repeated_string(i), output);
  }

  // repeated bytes repeated_bytes = 45;
  for (int i = 0; i < this->repeated_bytes_size(); i++) {
    ::google::protobuf::internal::WireFormatLite::WriteBytes(
      45, this->repeated_bytes(i), output);
  }

  // repeated .proto2_nofieldpresence_unittest.TestAllTypes.NestedMessage repeated_nested_message = 48;
  for (unsigned int i = 0, n = this->repeated_nested_message_size(); i < n; i++) {
    ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
      48, this->repeated_nested_message(i), output);
  }

  // repeated .proto2_nofieldpresence_unittest.ForeignMessage repeated_foreign_message = 49;
  for (unsigned int i = 0, n = this->repeated_foreign_message_size(); i < n; i++) {
    ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
      49, this->repeated_foreign_message(i), output);
  }

  // repeated .protobuf_unittest.TestAllTypes repeated_proto2_message = 50;
  for (unsigned int i = 0, n = this->repeated_proto2_message_size(); i < n; i++) {
    ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
      50, this->repeated_proto2_message(i), output);
  }

  // repeated .proto2_nofieldpresence_unittest.TestAllTypes.NestedEnum repeated_nested_enum = 51;
  if (this->repeated_nested_enum_size() > 0) {
    ::google::protobuf::internal::WireFormatLite::WriteTag(
      51,
      ::google::protobuf::internal::WireFormatLite::WIRETYPE_LENGTH_DELIMITED,
      output);
    output->WriteVarint32(_repeated_nested_enum_cached_byte_size_);
  }
  for (int i = 0; i < this->repeated_nested_enum_size(); i++) {
    ::google::protobuf::internal::WireFormatLite::WriteEnumNoTag(
      this->repeated_nested_enum(i), output);
  }

  // repeated .proto2_nofieldpresence_unittest.ForeignEnum repeated_foreign_enum = 52;
  if (this->repeated_foreign_enum_size() > 0) {
    ::google::protobuf::internal::WireFormatLite::WriteTag(
      52,
      ::google::protobuf::internal::WireFormatLite::WIRETYPE_LENGTH_DELIMITED,
      output);
    output->WriteVarint32(_repeated_foreign_enum_cached_byte_size_);
  }
  for (int i = 0; i < this->repeated_foreign_enum_size(); i++) {
    ::google::protobuf::internal::WireFormatLite::WriteEnumNoTag(
      this->repeated_foreign_enum(i), output);
  }

  // repeated string repeated_string_piece = 54 [ctype = STRING_PIECE];
  for (int i = 0; i < this->repeated_string_piece_size(); i++) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->repeated_string_piece(i).data(), this->repeated_string_piece(i).length(),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "proto2_nofieldpresence_unittest.TestAllTypes.repeated_string_piece");
    ::google::protobuf::internal::WireFormatLite::WriteString(
      54, this->repeated_string_piece(i), output);
  }

  // repeated string repeated_cord = 55 [ctype = CORD];
  for (int i = 0; i < this->repeated_cord_size(); i++) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->repeated_cord(i).data(), this->repeated_cord(i).length(),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "proto2_nofieldpresence_unittest.TestAllTypes.repeated_cord");
    ::google::protobuf::internal::WireFormatLite::WriteString(
      55, this->repeated_cord(i), output);
  }

  // repeated .proto2_nofieldpresence_unittest.TestAllTypes.NestedMessage repeated_lazy_message = 57 [lazy = true];
  for (unsigned int i = 0, n = this->repeated_lazy_message_size(); i < n; i++) {
    ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
      57, this->repeated_lazy_message(i), output);
  }

  // optional uint32 oneof_uint32 = 111;
  if (has_oneof_uint32()) {
    ::google::protobuf::internal::WireFormatLite::WriteUInt32(111, this->oneof_uint32(), output);
  }

  // optional .proto2_nofieldpresence_unittest.TestAllTypes.NestedMessage oneof_nested_message = 112;
  if (has_oneof_nested_message()) {
    ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
      112, *oneof_field_.oneof_nested_message_, output);
  }

  // optional string oneof_string = 113;
  if (has_oneof_string()) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->oneof_string().data(), this->oneof_string().length(),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "proto2_nofieldpresence_unittest.TestAllTypes.oneof_string");
    ::google::protobuf::internal::WireFormatLite::WriteStringMaybeAliased(
      113, this->oneof_string(), output);
  }

  // optional .proto2_nofieldpresence_unittest.TestAllTypes.NestedEnum oneof_enum = 114;
  if (has_oneof_enum()) {
    ::google::protobuf::internal::WireFormatLite::WriteEnum(
      114, this->oneof_enum(), output);
  }

  // @@protoc_insertion_point(serialize_end:proto2_nofieldpresence_unittest.TestAllTypes)
}

::google::protobuf::uint8* TestAllTypes::InternalSerializeWithCachedSizesToArray(
    bool deterministic, ::google::protobuf::uint8* target) const {
  (void)deterministic; // Unused
  // @@protoc_insertion_point(serialize_to_array_start:proto2_nofieldpresence_unittest.TestAllTypes)
  // optional int32 optional_int32 = 1;
  if (this->optional_int32() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt32ToArray(1, this->optional_int32(), target);
  }

  // optional int64 optional_int64 = 2;
  if (this->optional_int64() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt64ToArray(2, this->optional_int64(), target);
  }

  // optional uint32 optional_uint32 = 3;
  if (this->optional_uint32() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteUInt32ToArray(3, this->optional_uint32(), target);
  }

  // optional uint64 optional_uint64 = 4;
  if (this->optional_uint64() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteUInt64ToArray(4, this->optional_uint64(), target);
  }

  // optional sint32 optional_sint32 = 5;
  if (this->optional_sint32() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteSInt32ToArray(5, this->optional_sint32(), target);
  }

  // optional sint64 optional_sint64 = 6;
  if (this->optional_sint64() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteSInt64ToArray(6, this->optional_sint64(), target);
  }

  // optional fixed32 optional_fixed32 = 7;
  if (this->optional_fixed32() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteFixed32ToArray(7, this->optional_fixed32(), target);
  }

  // optional fixed64 optional_fixed64 = 8;
  if (this->optional_fixed64() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteFixed64ToArray(8, this->optional_fixed64(), target);
  }

  // optional sfixed32 optional_sfixed32 = 9;
  if (this->optional_sfixed32() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteSFixed32ToArray(9, this->optional_sfixed32(), target);
  }

  // optional sfixed64 optional_sfixed64 = 10;
  if (this->optional_sfixed64() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteSFixed64ToArray(10, this->optional_sfixed64(), target);
  }

  // optional float optional_float = 11;
  if (this->optional_float() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteFloatToArray(11, this->optional_float(), target);
  }

  // optional double optional_double = 12;
  if (this->optional_double() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteDoubleToArray(12, this->optional_double(), target);
  }

  // optional bool optional_bool = 13;
  if (this->optional_bool() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteBoolToArray(13, this->optional_bool(), target);
  }

  // optional string optional_string = 14;
  if (this->optional_string().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->optional_string().data(), this->optional_string().length(),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "proto2_nofieldpresence_unittest.TestAllTypes.optional_string");
    target =
      ::google::protobuf::internal::WireFormatLite::WriteStringToArray(
        14, this->optional_string(), target);
  }

  // optional bytes optional_bytes = 15;
  if (this->optional_bytes().size() > 0) {
    target =
      ::google::protobuf::internal::WireFormatLite::WriteBytesToArray(
        15, this->optional_bytes(), target);
  }

  // optional .proto2_nofieldpresence_unittest.TestAllTypes.NestedMessage optional_nested_message = 18;
  if (this->has_optional_nested_message()) {
    target = ::google::protobuf::internal::WireFormatLite::
      InternalWriteMessageNoVirtualToArray(
        18, *this->optional_nested_message_, false, target);
  }

  // optional .proto2_nofieldpresence_unittest.ForeignMessage optional_foreign_message = 19;
  if (this->has_optional_foreign_message()) {
    target = ::google::protobuf::internal::WireFormatLite::
      InternalWriteMessageNoVirtualToArray(
        19, *this->optional_foreign_message_, false, target);
  }

  // optional .protobuf_unittest.TestAllTypes optional_proto2_message = 20;
  if (this->has_optional_proto2_message()) {
    target = ::google::protobuf::internal::WireFormatLite::
      InternalWriteMessageNoVirtualToArray(
        20, *this->optional_proto2_message_, false, target);
  }

  // optional .proto2_nofieldpresence_unittest.TestAllTypes.NestedEnum optional_nested_enum = 21;
  if (this->optional_nested_enum() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteEnumToArray(
      21, this->optional_nested_enum(), target);
  }

  // optional .proto2_nofieldpresence_unittest.ForeignEnum optional_foreign_enum = 22;
  if (this->optional_foreign_enum() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteEnumToArray(
      22, this->optional_foreign_enum(), target);
  }

  // optional string optional_string_piece = 24 [ctype = STRING_PIECE];
  if (this->optional_string_piece().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->optional_string_piece().data(), this->optional_string_piece().length(),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "proto2_nofieldpresence_unittest.TestAllTypes.optional_string_piece");
    target =
      ::google::protobuf::internal::WireFormatLite::WriteStringToArray(
        24, this->optional_string_piece(), target);
  }

  // optional string optional_cord = 25 [ctype = CORD];
  if (this->optional_cord().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->optional_cord().data(), this->optional_cord().length(),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "proto2_nofieldpresence_unittest.TestAllTypes.optional_cord");
    target =
      ::google::protobuf::internal::WireFormatLite::WriteStringToArray(
        25, this->optional_cord(), target);
  }

  // optional .proto2_nofieldpresence_unittest.TestAllTypes.NestedMessage optional_lazy_message = 30 [lazy = true];
  if (this->has_optional_lazy_message()) {
    target = ::google::protobuf::internal::WireFormatLite::
      InternalWriteMessageNoVirtualToArray(
        30, *this->optional_lazy_message_, false, target);
  }

  // repeated int32 repeated_int32 = 31;
  if (this->repeated_int32_size() > 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteTagToArray(
      31,
      ::google::protobuf::internal::WireFormatLite::WIRETYPE_LENGTH_DELIMITED,
      target);
    target = ::google::protobuf::io::CodedOutputStream::WriteVarint32ToArray(
      _repeated_int32_cached_byte_size_, target);
  }
  for (int i = 0; i < this->repeated_int32_size(); i++) {
    target = ::google::protobuf::internal::WireFormatLite::
      WriteInt32NoTagToArray(this->repeated_int32(i), target);
  }

  // repeated int64 repeated_int64 = 32;
  if (this->repeated_int64_size() > 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteTagToArray(
      32,
      ::google::protobuf::internal::WireFormatLite::WIRETYPE_LENGTH_DELIMITED,
      target);
    target = ::google::protobuf::io::CodedOutputStream::WriteVarint32ToArray(
      _repeated_int64_cached_byte_size_, target);
  }
  for (int i = 0; i < this->repeated_int64_size(); i++) {
    target = ::google::protobuf::internal::WireFormatLite::
      WriteInt64NoTagToArray(this->repeated_int64(i), target);
  }

  // repeated uint32 repeated_uint32 = 33;
  if (this->repeated_uint32_size() > 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteTagToArray(
      33,
      ::google::protobuf::internal::WireFormatLite::WIRETYPE_LENGTH_DELIMITED,
      target);
    target = ::google::protobuf::io::CodedOutputStream::WriteVarint32ToArray(
      _repeated_uint32_cached_byte_size_, target);
  }
  for (int i = 0; i < this->repeated_uint32_size(); i++) {
    target = ::google::protobuf::internal::WireFormatLite::
      WriteUInt32NoTagToArray(this->repeated_uint32(i), target);
  }

  // repeated uint64 repeated_uint64 = 34;
  if (this->repeated_uint64_size() > 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteTagToArray(
      34,
      ::google::protobuf::internal::WireFormatLite::WIRETYPE_LENGTH_DELIMITED,
      target);
    target = ::google::protobuf::io::CodedOutputStream::WriteVarint32ToArray(
      _repeated_uint64_cached_byte_size_, target);
  }
  for (int i = 0; i < this->repeated_uint64_size(); i++) {
    target = ::google::protobuf::internal::WireFormatLite::
      WriteUInt64NoTagToArray(this->repeated_uint64(i), target);
  }

  // repeated sint32 repeated_sint32 = 35;
  if (this->repeated_sint32_size() > 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteTagToArray(
      35,
      ::google::protobuf::internal::WireFormatLite::WIRETYPE_LENGTH_DELIMITED,
      target);
    target = ::google::protobuf::io::CodedOutputStream::WriteVarint32ToArray(
      _repeated_sint32_cached_byte_size_, target);
  }
  for (int i = 0; i < this->repeated_sint32_size(); i++) {
    target = ::google::protobuf::internal::WireFormatLite::
      WriteSInt32NoTagToArray(this->repeated_sint32(i), target);
  }

  // repeated sint64 repeated_sint64 = 36;
  if (this->repeated_sint64_size() > 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteTagToArray(
      36,
      ::google::protobuf::internal::WireFormatLite::WIRETYPE_LENGTH_DELIMITED,
      target);
    target = ::google::protobuf::io::CodedOutputStream::WriteVarint32ToArray(
      _repeated_sint64_cached_byte_size_, target);
  }
  for (int i = 0; i < this->repeated_sint64_size(); i++) {
    target = ::google::protobuf::internal::WireFormatLite::
      WriteSInt64NoTagToArray(this->repeated_sint64(i), target);
  }

  // repeated fixed32 repeated_fixed32 = 37;
  if (this->repeated_fixed32_size() > 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteTagToArray(
      37,
      ::google::protobuf::internal::WireFormatLite::WIRETYPE_LENGTH_DELIMITED,
      target);
    target = ::google::protobuf::io::CodedOutputStream::WriteVarint32ToArray(
      _repeated_fixed32_cached_byte_size_, target);
  }
  for (int i = 0; i < this->repeated_fixed32_size(); i++) {
    target = ::google::protobuf::internal::WireFormatLite::
      WriteFixed32NoTagToArray(this->repeated_fixed32(i), target);
  }

  // repeated fixed64 repeated_fixed64 = 38;
  if (this->repeated_fixed64_size() > 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteTagToArray(
      38,
      ::google::protobuf::internal::WireFormatLite::WIRETYPE_LENGTH_DELIMITED,
      target);
    target = ::google::protobuf::io::CodedOutputStream::WriteVarint32ToArray(
      _repeated_fixed64_cached_byte_size_, target);
  }
  for (int i = 0; i < this->repeated_fixed64_size(); i++) {
    target = ::google::protobuf::internal::WireFormatLite::
      WriteFixed64NoTagToArray(this->repeated_fixed64(i), target);
  }

  // repeated sfixed32 repeated_sfixed32 = 39;
  if (this->repeated_sfixed32_size() > 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteTagToArray(
      39,
      ::google::protobuf::internal::WireFormatLite::WIRETYPE_LENGTH_DELIMITED,
      target);
    target = ::google::protobuf::io::CodedOutputStream::WriteVarint32ToArray(
      _repeated_sfixed32_cached_byte_size_, target);
  }
  for (int i = 0; i < this->repeated_sfixed32_size(); i++) {
    target = ::google::protobuf::internal::WireFormatLite::
      WriteSFixed32NoTagToArray(this->repeated_sfixed32(i), target);
  }

  // repeated sfixed64 repeated_sfixed64 = 40;
  if (this->repeated_sfixed64_size() > 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteTagToArray(
      40,
      ::google::protobuf::internal::WireFormatLite::WIRETYPE_LENGTH_DELIMITED,
      target);
    target = ::google::protobuf::io::CodedOutputStream::WriteVarint32ToArray(
      _repeated_sfixed64_cached_byte_size_, target);
  }
  for (int i = 0; i < this->repeated_sfixed64_size(); i++) {
    target = ::google::protobuf::internal::WireFormatLite::
      WriteSFixed64NoTagToArray(this->repeated_sfixed64(i), target);
  }

  // repeated float repeated_float = 41;
  if (this->repeated_float_size() > 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteTagToArray(
      41,
      ::google::protobuf::internal::WireFormatLite::WIRETYPE_LENGTH_DELIMITED,
      target);
    target = ::google::protobuf::io::CodedOutputStream::WriteVarint32ToArray(
      _repeated_float_cached_byte_size_, target);
  }
  for (int i = 0; i < this->repeated_float_size(); i++) {
    target = ::google::protobuf::internal::WireFormatLite::
      WriteFloatNoTagToArray(this->repeated_float(i), target);
  }

  // repeated double repeated_double = 42;
  if (this->repeated_double_size() > 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteTagToArray(
      42,
      ::google::protobuf::internal::WireFormatLite::WIRETYPE_LENGTH_DELIMITED,
      target);
    target = ::google::protobuf::io::CodedOutputStream::WriteVarint32ToArray(
      _repeated_double_cached_byte_size_, target);
  }
  for (int i = 0; i < this->repeated_double_size(); i++) {
    target = ::google::protobuf::internal::WireFormatLite::
      WriteDoubleNoTagToArray(this->repeated_double(i), target);
  }

  // repeated bool repeated_bool = 43;
  if (this->repeated_bool_size() > 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteTagToArray(
      43,
      ::google::protobuf::internal::WireFormatLite::WIRETYPE_LENGTH_DELIMITED,
      target);
    target = ::google::protobuf::io::CodedOutputStream::WriteVarint32ToArray(
      _repeated_bool_cached_byte_size_, target);
  }
  for (int i = 0; i < this->repeated_bool_size(); i++) {
    target = ::google::protobuf::internal::WireFormatLite::
      WriteBoolNoTagToArray(this->repeated_bool(i), target);
  }

  // repeated string repeated_string = 44;
  for (int i = 0; i < this->repeated_string_size(); i++) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->repeated_string(i).data(), this->repeated_string(i).length(),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "proto2_nofieldpresence_unittest.TestAllTypes.repeated_string");
    target = ::google::protobuf::internal::WireFormatLite::
      WriteStringToArray(44, this->repeated_string(i), target);
  }

  // repeated bytes repeated_bytes = 45;
  for (int i = 0; i < this->repeated_bytes_size(); i++) {
    target = ::google::protobuf::internal::WireFormatLite::
      WriteBytesToArray(45, this->repeated_bytes(i), target);
  }

  // repeated .proto2_nofieldpresence_unittest.TestAllTypes.NestedMessage repeated_nested_message = 48;
  for (unsigned int i = 0, n = this->repeated_nested_message_size(); i < n; i++) {
    target = ::google::protobuf::internal::WireFormatLite::
      InternalWriteMessageNoVirtualToArray(
        48, this->repeated_nested_message(i), false, target);
  }

  // repeated .proto2_nofieldpresence_unittest.ForeignMessage repeated_foreign_message = 49;
  for (unsigned int i = 0, n = this->repeated_foreign_message_size(); i < n; i++) {
    target = ::google::protobuf::internal::WireFormatLite::
      InternalWriteMessageNoVirtualToArray(
        49, this->repeated_foreign_message(i), false, target);
  }

  // repeated .protobuf_unittest.TestAllTypes repeated_proto2_message = 50;
  for (unsigned int i = 0, n = this->repeated_proto2_message_size(); i < n; i++) {
    target = ::google::protobuf::internal::WireFormatLite::
      InternalWriteMessageNoVirtualToArray(
        50, this->repeated_proto2_message(i), false, target);
  }

  // repeated .proto2_nofieldpresence_unittest.TestAllTypes.NestedEnum repeated_nested_enum = 51;
  if (this->repeated_nested_enum_size() > 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteTagToArray(
      51,
      ::google::protobuf::internal::WireFormatLite::WIRETYPE_LENGTH_DELIMITED,
      target);
    target = ::google::protobuf::io::CodedOutputStream::WriteVarint32ToArray(    _repeated_nested_enum_cached_byte_size_, target);
  }
  for (int i = 0; i < this->repeated_nested_enum_size(); i++) {
    target = ::google::protobuf::internal::WireFormatLite::WriteEnumNoTagToArray(
      this->repeated_nested_enum(i), target);
  }

  // repeated .proto2_nofieldpresence_unittest.ForeignEnum repeated_foreign_enum = 52;
  if (this->repeated_foreign_enum_size() > 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteTagToArray(
      52,
      ::google::protobuf::internal::WireFormatLite::WIRETYPE_LENGTH_DELIMITED,
      target);
    target = ::google::protobuf::io::CodedOutputStream::WriteVarint32ToArray(    _repeated_foreign_enum_cached_byte_size_, target);
  }
  for (int i = 0; i < this->repeated_foreign_enum_size(); i++) {
    target = ::google::protobuf::internal::WireFormatLite::WriteEnumNoTagToArray(
      this->repeated_foreign_enum(i), target);
  }

  // repeated string repeated_string_piece = 54 [ctype = STRING_PIECE];
  for (int i = 0; i < this->repeated_string_piece_size(); i++) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->repeated_string_piece(i).data(), this->repeated_string_piece(i).length(),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "proto2_nofieldpresence_unittest.TestAllTypes.repeated_string_piece");
    target = ::google::protobuf::internal::WireFormatLite::
      WriteStringToArray(54, this->repeated_string_piece(i), target);
  }

  // repeated string repeated_cord = 55 [ctype = CORD];
  for (int i = 0; i < this->repeated_cord_size(); i++) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->repeated_cord(i).data(), this->repeated_cord(i).length(),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "proto2_nofieldpresence_unittest.TestAllTypes.repeated_cord");
    target = ::google::protobuf::internal::WireFormatLite::
      WriteStringToArray(55, this->repeated_cord(i), target);
  }

  // repeated .proto2_nofieldpresence_unittest.TestAllTypes.NestedMessage repeated_lazy_message = 57 [lazy = true];
  for (unsigned int i = 0, n = this->repeated_lazy_message_size(); i < n; i++) {
    target = ::google::protobuf::internal::WireFormatLite::
      InternalWriteMessageNoVirtualToArray(
        57, this->repeated_lazy_message(i), false, target);
  }

  // optional uint32 oneof_uint32 = 111;
  if (has_oneof_uint32()) {
    target = ::google::protobuf::internal::WireFormatLite::WriteUInt32ToArray(111, this->oneof_uint32(), target);
  }

  // optional .proto2_nofieldpresence_unittest.TestAllTypes.NestedMessage oneof_nested_message = 112;
  if (has_oneof_nested_message()) {
    target = ::google::protobuf::internal::WireFormatLite::
      InternalWriteMessageNoVirtualToArray(
        112, *oneof_field_.oneof_nested_message_, false, target);
  }

  // optional string oneof_string = 113;
  if (has_oneof_string()) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->oneof_string().data(), this->oneof_string().length(),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "proto2_nofieldpresence_unittest.TestAllTypes.oneof_string");
    target =
      ::google::protobuf::internal::WireFormatLite::WriteStringToArray(
        113, this->oneof_string(), target);
  }

  // optional .proto2_nofieldpresence_unittest.TestAllTypes.NestedEnum oneof_enum = 114;
  if (has_oneof_enum()) {
    target = ::google::protobuf::internal::WireFormatLite::WriteEnumToArray(
      114, this->oneof_enum(), target);
  }

  // @@protoc_insertion_point(serialize_to_array_end:proto2_nofieldpresence_unittest.TestAllTypes)
  return target;
}

size_t TestAllTypes::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:proto2_nofieldpresence_unittest.TestAllTypes)
  size_t total_size = 0;

  // optional int32 optional_int32 = 1;
  if (this->optional_int32() != 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::Int32Size(
        this->optional_int32());
  }

  // optional int64 optional_int64 = 2;
  if (this->optional_int64() != 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::Int64Size(
        this->optional_int64());
  }

  // optional uint32 optional_uint32 = 3;
  if (this->optional_uint32() != 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::UInt32Size(
        this->optional_uint32());
  }

  // optional uint64 optional_uint64 = 4;
  if (this->optional_uint64() != 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::UInt64Size(
        this->optional_uint64());
  }

  // optional sint32 optional_sint32 = 5;
  if (this->optional_sint32() != 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::SInt32Size(
        this->optional_sint32());
  }

  // optional sint64 optional_sint64 = 6;
  if (this->optional_sint64() != 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::SInt64Size(
        this->optional_sint64());
  }

  // optional fixed32 optional_fixed32 = 7;
  if (this->optional_fixed32() != 0) {
    total_size += 1 + 4;
  }

  // optional fixed64 optional_fixed64 = 8;
  if (this->optional_fixed64() != 0) {
    total_size += 1 + 8;
  }

  // optional sfixed32 optional_sfixed32 = 9;
  if (this->optional_sfixed32() != 0) {
    total_size += 1 + 4;
  }

  // optional sfixed64 optional_sfixed64 = 10;
  if (this->optional_sfixed64() != 0) {
    total_size += 1 + 8;
  }

  // optional float optional_float = 11;
  if (this->optional_float() != 0) {
    total_size += 1 + 4;
  }

  // optional double optional_double = 12;
  if (this->optional_double() != 0) {
    total_size += 1 + 8;
  }

  // optional bool optional_bool = 13;
  if (this->optional_bool() != 0) {
    total_size += 1 + 1;
  }

  // optional string optional_string = 14;
  if (this->optional_string().size() > 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::StringSize(
        this->optional_string());
  }

  // optional bytes optional_bytes = 15;
  if (this->optional_bytes().size() > 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::BytesSize(
        this->optional_bytes());
  }

  // optional .proto2_nofieldpresence_unittest.TestAllTypes.NestedMessage optional_nested_message = 18;
  if (this->has_optional_nested_message()) {
    total_size += 2 +
      ::google::protobuf::internal::WireFormatLite::MessageSizeNoVirtual(
        *this->optional_nested_message_);
  }

  // optional .proto2_nofieldpresence_unittest.ForeignMessage optional_foreign_message = 19;
  if (this->has_optional_foreign_message()) {
    total_size += 2 +
      ::google::protobuf::internal::WireFormatLite::MessageSizeNoVirtual(
        *this->optional_foreign_message_);
  }

  // optional .protobuf_unittest.TestAllTypes optional_proto2_message = 20;
  if (this->has_optional_proto2_message()) {
    total_size += 2 +
      ::google::protobuf::internal::WireFormatLite::MessageSizeNoVirtual(
        *this->optional_proto2_message_);
  }

  // optional .proto2_nofieldpresence_unittest.TestAllTypes.NestedEnum optional_nested_enum = 21;
  if (this->optional_nested_enum() != 0) {
    total_size += 2 +
      ::google::protobuf::internal::WireFormatLite::EnumSize(this->optional_nested_enum());
  }

  // optional .proto2_nofieldpresence_unittest.ForeignEnum optional_foreign_enum = 22;
  if (this->optional_foreign_enum() != 0) {
    total_size += 2 +
      ::google::protobuf::internal::WireFormatLite::EnumSize(this->optional_foreign_enum());
  }

  // optional string optional_string_piece = 24 [ctype = STRING_PIECE];
  if (this->optional_string_piece().size() > 0) {
    total_size += 2 +
      ::google::protobuf::internal::WireFormatLite::StringSize(
        this->optional_string_piece());
  }

  // optional string optional_cord = 25 [ctype = CORD];
  if (this->optional_cord().size() > 0) {
    total_size += 2 +
      ::google::protobuf::internal::WireFormatLite::StringSize(
        this->optional_cord());
  }

  // optional .proto2_nofieldpresence_unittest.TestAllTypes.NestedMessage optional_lazy_message = 30 [lazy = true];
  if (this->has_optional_lazy_message()) {
    total_size += 2 +
      ::google::protobuf::internal::WireFormatLite::MessageSizeNoVirtual(
        *this->optional_lazy_message_);
  }

  // repeated int32 repeated_int32 = 31;
  {
    size_t data_size = 0;
    unsigned int count = this->repeated_int32_size();
    for (unsigned int i = 0; i < count; i++) {
      data_size += ::google::protobuf::internal::WireFormatLite::
        Int32Size(this->repeated_int32(i));
    }
    if (data_size > 0) {
      total_size += 2 +
        ::google::protobuf::internal::WireFormatLite::Int32Size(data_size);
    }
    int cached_size = ::google::protobuf::internal::ToCachedSize(data_size);
    GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
    _repeated_int32_cached_byte_size_ = cached_size;
    GOOGLE_SAFE_CONCURRENT_WRITES_END();
    total_size += data_size;
  }

  // repeated int64 repeated_int64 = 32;
  {
    size_t data_size = 0;
    unsigned int count = this->repeated_int64_size();
    for (unsigned int i = 0; i < count; i++) {
      data_size += ::google::protobuf::internal::WireFormatLite::
        Int64Size(this->repeated_int64(i));
    }
    if (data_size > 0) {
      total_size += 2 +
        ::google::protobuf::internal::WireFormatLite::Int32Size(data_size);
    }
    int cached_size = ::google::protobuf::internal::ToCachedSize(data_size);
    GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
    _repeated_int64_cached_byte_size_ = cached_size;
    GOOGLE_SAFE_CONCURRENT_WRITES_END();
    total_size += data_size;
  }

  // repeated uint32 repeated_uint32 = 33;
  {
    size_t data_size = 0;
    unsigned int count = this->repeated_uint32_size();
    for (unsigned int i = 0; i < count; i++) {
      data_size += ::google::protobuf::internal::WireFormatLite::
        UInt32Size(this->repeated_uint32(i));
    }
    if (data_size > 0) {
      total_size += 2 +
        ::google::protobuf::internal::WireFormatLite::Int32Size(data_size);
    }
    int cached_size = ::google::protobuf::internal::ToCachedSize(data_size);
    GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
    _repeated_uint32_cached_byte_size_ = cached_size;
    GOOGLE_SAFE_CONCURRENT_WRITES_END();
    total_size += data_size;
  }

  // repeated uint64 repeated_uint64 = 34;
  {
    size_t data_size = 0;
    unsigned int count = this->repeated_uint64_size();
    for (unsigned int i = 0; i < count; i++) {
      data_size += ::google::protobuf::internal::WireFormatLite::
        UInt64Size(this->repeated_uint64(i));
    }
    if (data_size > 0) {
      total_size += 2 +
        ::google::protobuf::internal::WireFormatLite::Int32Size(data_size);
    }
    int cached_size = ::google::protobuf::internal::ToCachedSize(data_size);
    GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
    _repeated_uint64_cached_byte_size_ = cached_size;
    GOOGLE_SAFE_CONCURRENT_WRITES_END();
    total_size += data_size;
  }

  // repeated sint32 repeated_sint32 = 35;
  {
    size_t data_size = 0;
    unsigned int count = this->repeated_sint32_size();
    for (unsigned int i = 0; i < count; i++) {
      data_size += ::google::protobuf::internal::WireFormatLite::
        SInt32Size(this->repeated_sint32(i));
    }
    if (data_size > 0) {
      total_size += 2 +
        ::google::protobuf::internal::WireFormatLite::Int32Size(data_size);
    }
    int cached_size = ::google::protobuf::internal::ToCachedSize(data_size);
    GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
    _repeated_sint32_cached_byte_size_ = cached_size;
    GOOGLE_SAFE_CONCURRENT_WRITES_END();
    total_size += data_size;
  }

  // repeated sint64 repeated_sint64 = 36;
  {
    size_t data_size = 0;
    unsigned int count = this->repeated_sint64_size();
    for (unsigned int i = 0; i < count; i++) {
      data_size += ::google::protobuf::internal::WireFormatLite::
        SInt64Size(this->repeated_sint64(i));
    }
    if (data_size > 0) {
      total_size += 2 +
        ::google::protobuf::internal::WireFormatLite::Int32Size(data_size);
    }
    int cached_size = ::google::protobuf::internal::ToCachedSize(data_size);
    GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
    _repeated_sint64_cached_byte_size_ = cached_size;
    GOOGLE_SAFE_CONCURRENT_WRITES_END();
    total_size += data_size;
  }

  // repeated fixed32 repeated_fixed32 = 37;
  {
    size_t data_size = 0;
    unsigned int count = this->repeated_fixed32_size();
    data_size = 4UL * count;
    if (data_size > 0) {
      total_size += 2 +
        ::google::protobuf::internal::WireFormatLite::Int32Size(data_size);
    }
    int cached_size = ::google::protobuf::internal::ToCachedSize(data_size);
    GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
    _repeated_fixed32_cached_byte_size_ = cached_size;
    GOOGLE_SAFE_CONCURRENT_WRITES_END();
    total_size += data_size;
  }

  // repeated fixed64 repeated_fixed64 = 38;
  {
    size_t data_size = 0;
    unsigned int count = this->repeated_fixed64_size();
    data_size = 8UL * count;
    if (data_size > 0) {
      total_size += 2 +
        ::google::protobuf::internal::WireFormatLite::Int32Size(data_size);
    }
    int cached_size = ::google::protobuf::internal::ToCachedSize(data_size);
    GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
    _repeated_fixed64_cached_byte_size_ = cached_size;
    GOOGLE_SAFE_CONCURRENT_WRITES_END();
    total_size += data_size;
  }

  // repeated sfixed32 repeated_sfixed32 = 39;
  {
    size_t data_size = 0;
    unsigned int count = this->repeated_sfixed32_size();
    data_size = 4UL * count;
    if (data_size > 0) {
      total_size += 2 +
        ::google::protobuf::internal::WireFormatLite::Int32Size(data_size);
    }
    int cached_size = ::google::protobuf::internal::ToCachedSize(data_size);
    GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
    _repeated_sfixed32_cached_byte_size_ = cached_size;
    GOOGLE_SAFE_CONCURRENT_WRITES_END();
    total_size += data_size;
  }

  // repeated sfixed64 repeated_sfixed64 = 40;
  {
    size_t data_size = 0;
    unsigned int count = this->repeated_sfixed64_size();
    data_size = 8UL * count;
    if (data_size > 0) {
      total_size += 2 +
        ::google::protobuf::internal::WireFormatLite::Int32Size(data_size);
    }
    int cached_size = ::google::protobuf::internal::ToCachedSize(data_size);
    GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
    _repeated_sfixed64_cached_byte_size_ = cached_size;
    GOOGLE_SAFE_CONCURRENT_WRITES_END();
    total_size += data_size;
  }

  // repeated float repeated_float = 41;
  {
    size_t data_size = 0;
    unsigned int count = this->repeated_float_size();
    data_size = 4UL * count;
    if (data_size > 0) {
      total_size += 2 +
        ::google::protobuf::internal::WireFormatLite::Int32Size(data_size);
    }
    int cached_size = ::google::protobuf::internal::ToCachedSize(data_size);
    GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
    _repeated_float_cached_byte_size_ = cached_size;
    GOOGLE_SAFE_CONCURRENT_WRITES_END();
    total_size += data_size;
  }

  // repeated double repeated_double = 42;
  {
    size_t data_size = 0;
    unsigned int count = this->repeated_double_size();
    data_size = 8UL * count;
    if (data_size > 0) {
      total_size += 2 +
        ::google::protobuf::internal::WireFormatLite::Int32Size(data_size);
    }
    int cached_size = ::google::protobuf::internal::ToCachedSize(data_size);
    GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
    _repeated_double_cached_byte_size_ = cached_size;
    GOOGLE_SAFE_CONCURRENT_WRITES_END();
    total_size += data_size;
  }

  // repeated bool repeated_bool = 43;
  {
    size_t data_size = 0;
    unsigned int count = this->repeated_bool_size();
    data_size = 1UL * count;
    if (data_size > 0) {
      total_size += 2 +
        ::google::protobuf::internal::WireFormatLite::Int32Size(data_size);
    }
    int cached_size = ::google::protobuf::internal::ToCachedSize(data_size);
    GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
    _repeated_bool_cached_byte_size_ = cached_size;
    GOOGLE_SAFE_CONCURRENT_WRITES_END();
    total_size += data_size;
  }

  // repeated string repeated_string = 44;
  total_size += 2 *
      ::google::protobuf::internal::FromIntSize(this->repeated_string_size());
  for (int i = 0; i < this->repeated_string_size(); i++) {
    total_size += ::google::protobuf::internal::WireFormatLite::StringSize(
      this->repeated_string(i));
  }

  // repeated bytes repeated_bytes = 45;
  total_size += 2 *
      ::google::protobuf::internal::FromIntSize(this->repeated_bytes_size());
  for (int i = 0; i < this->repeated_bytes_size(); i++) {
    total_size += ::google::protobuf::internal::WireFormatLite::BytesSize(
      this->repeated_bytes(i));
  }

  // repeated .proto2_nofieldpresence_unittest.TestAllTypes.NestedMessage repeated_nested_message = 48;
  {
    unsigned int count = this->repeated_nested_message_size();
    total_size += 2UL * count;
    for (unsigned int i = 0; i < count; i++) {
      total_size +=
        ::google::protobuf::internal::WireFormatLite::MessageSizeNoVirtual(
          this->repeated_nested_message(i));
    }
  }

  // repeated .proto2_nofieldpresence_unittest.ForeignMessage repeated_foreign_message = 49;
  {
    unsigned int count = this->repeated_foreign_message_size();
    total_size += 2UL * count;
    for (unsigned int i = 0; i < count; i++) {
      total_size +=
        ::google::protobuf::internal::WireFormatLite::MessageSizeNoVirtual(
          this->repeated_foreign_message(i));
    }
  }

  // repeated .protobuf_unittest.TestAllTypes repeated_proto2_message = 50;
  {
    unsigned int count = this->repeated_proto2_message_size();
    total_size += 2UL * count;
    for (unsigned int i = 0; i < count; i++) {
      total_size +=
        ::google::protobuf::internal::WireFormatLite::MessageSizeNoVirtual(
          this->repeated_proto2_message(i));
    }
  }

  // repeated .proto2_nofieldpresence_unittest.TestAllTypes.NestedEnum repeated_nested_enum = 51;
  {
    size_t data_size = 0;
    unsigned int count = this->repeated_nested_enum_size();for (unsigned int i = 0; i < count; i++) {
      data_size += ::google::protobuf::internal::WireFormatLite::EnumSize(
        this->repeated_nested_enum(i));
    }
    if (data_size > 0) {
      total_size += 2 +
        ::google::protobuf::internal::WireFormatLite::Int32Size(data_size);
    }
    int cached_size = ::google::protobuf::internal::ToCachedSize(data_size);
    GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
    _repeated_nested_enum_cached_byte_size_ = cached_size;
    GOOGLE_SAFE_CONCURRENT_WRITES_END();
    total_size += data_size;
  }

  // repeated .proto2_nofieldpresence_unittest.ForeignEnum repeated_foreign_enum = 52;
  {
    size_t data_size = 0;
    unsigned int count = this->repeated_foreign_enum_size();for (unsigned int i = 0; i < count; i++) {
      data_size += ::google::protobuf::internal::WireFormatLite::EnumSize(
        this->repeated_foreign_enum(i));
    }
    if (data_size > 0) {
      total_size += 2 +
        ::google::protobuf::internal::WireFormatLite::Int32Size(data_size);
    }
    int cached_size = ::google::protobuf::internal::ToCachedSize(data_size);
    GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
    _repeated_foreign_enum_cached_byte_size_ = cached_size;
    GOOGLE_SAFE_CONCURRENT_WRITES_END();
    total_size += data_size;
  }

  // repeated string repeated_string_piece = 54 [ctype = STRING_PIECE];
  total_size += 2 *
      ::google::protobuf::internal::FromIntSize(this->repeated_string_piece_size());
  for (int i = 0; i < this->repeated_string_piece_size(); i++) {
    total_size += ::google::protobuf::internal::WireFormatLite::StringSize(
      this->repeated_string_piece(i));
  }

  // repeated string repeated_cord = 55 [ctype = CORD];
  total_size += 2 *
      ::google::protobuf::internal::FromIntSize(this->repeated_cord_size());
  for (int i = 0; i < this->repeated_cord_size(); i++) {
    total_size += ::google::protobuf::internal::WireFormatLite::StringSize(
      this->repeated_cord(i));
  }

  // repeated .proto2_nofieldpresence_unittest.TestAllTypes.NestedMessage repeated_lazy_message = 57 [lazy = true];
  {
    unsigned int count = this->repeated_lazy_message_size();
    total_size += 2UL * count;
    for (unsigned int i = 0; i < count; i++) {
      total_size +=
        ::google::protobuf::internal::WireFormatLite::MessageSizeNoVirtual(
          this->repeated_lazy_message(i));
    }
  }

  switch (oneof_field_case()) {
    // optional uint32 oneof_uint32 = 111;
    case kOneofUint32: {
      total_size += 2 +
        ::google::protobuf::internal::WireFormatLite::UInt32Size(
          this->oneof_uint32());
      break;
    }
    // optional .proto2_nofieldpresence_unittest.TestAllTypes.NestedMessage oneof_nested_message = 112;
    case kOneofNestedMessage: {
      total_size += 2 +
        ::google::protobuf::internal::WireFormatLite::MessageSizeNoVirtual(
          *oneof_field_.oneof_nested_message_);
      break;
    }
    // optional string oneof_string = 113;
    case kOneofString: {
      total_size += 2 +
        ::google::protobuf::internal::WireFormatLite::StringSize(
          this->oneof_string());
      break;
    }
    // optional .proto2_nofieldpresence_unittest.TestAllTypes.NestedEnum oneof_enum = 114;
    case kOneofEnum: {
      total_size += 2 +
        ::google::protobuf::internal::WireFormatLite::EnumSize(this->oneof_enum());
      break;
    }
    case ONEOF_FIELD_NOT_SET: {
      break;
    }
  }
  int cached_size = ::google::protobuf::internal::ToCachedSize(total_size);
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = cached_size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
  return total_size;
}

void TestAllTypes::MergeFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_merge_from_start:proto2_nofieldpresence_unittest.TestAllTypes)
  if (GOOGLE_PREDICT_FALSE(&from == this)) MergeFromFail(__LINE__);
  const TestAllTypes* source =
      ::google::protobuf::internal::DynamicCastToGenerated<const TestAllTypes>(
          &from);
  if (source == NULL) {
  // @@protoc_insertion_point(generalized_merge_from_cast_fail:proto2_nofieldpresence_unittest.TestAllTypes)
    ::google::protobuf::internal::ReflectionOps::Merge(from, this);
  } else {
  // @@protoc_insertion_point(generalized_merge_from_cast_success:proto2_nofieldpresence_unittest.TestAllTypes)
    UnsafeMergeFrom(*source);
  }
}

void TestAllTypes::MergeFrom(const TestAllTypes& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:proto2_nofieldpresence_unittest.TestAllTypes)
  if (GOOGLE_PREDICT_TRUE(&from != this)) {
    UnsafeMergeFrom(from);
  } else {
    MergeFromFail(__LINE__);
  }
}

void TestAllTypes::UnsafeMergeFrom(const TestAllTypes& from) {
  GOOGLE_DCHECK(&from != this);
  repeated_int32_.UnsafeMergeFrom(from.repeated_int32_);
  repeated_int64_.UnsafeMergeFrom(from.repeated_int64_);
  repeated_uint32_.UnsafeMergeFrom(from.repeated_uint32_);
  repeated_uint64_.UnsafeMergeFrom(from.repeated_uint64_);
  repeated_sint32_.UnsafeMergeFrom(from.repeated_sint32_);
  repeated_sint64_.UnsafeMergeFrom(from.repeated_sint64_);
  repeated_fixed32_.UnsafeMergeFrom(from.repeated_fixed32_);
  repeated_fixed64_.UnsafeMergeFrom(from.repeated_fixed64_);
  repeated_sfixed32_.UnsafeMergeFrom(from.repeated_sfixed32_);
  repeated_sfixed64_.UnsafeMergeFrom(from.repeated_sfixed64_);
  repeated_float_.UnsafeMergeFrom(from.repeated_float_);
  repeated_double_.UnsafeMergeFrom(from.repeated_double_);
  repeated_bool_.UnsafeMergeFrom(from.repeated_bool_);
  repeated_string_.UnsafeMergeFrom(from.repeated_string_);
  repeated_bytes_.UnsafeMergeFrom(from.repeated_bytes_);
  repeated_nested_message_.MergeFrom(from.repeated_nested_message_);
  repeated_foreign_message_.MergeFrom(from.repeated_foreign_message_);
  repeated_proto2_message_.MergeFrom(from.repeated_proto2_message_);
  repeated_nested_enum_.UnsafeMergeFrom(from.repeated_nested_enum_);
  repeated_foreign_enum_.UnsafeMergeFrom(from.repeated_foreign_enum_);
  repeated_string_piece_.UnsafeMergeFrom(from.repeated_string_piece_);
  repeated_cord_.UnsafeMergeFrom(from.repeated_cord_);
  repeated_lazy_message_.MergeFrom(from.repeated_lazy_message_);
  switch (from.oneof_field_case()) {
    case kOneofUint32: {
      set_oneof_uint32(from.oneof_uint32());
      break;
    }
    case kOneofNestedMessage: {
      mutable_oneof_nested_message()->::proto2_nofieldpresence_unittest::TestAllTypes_NestedMessage::MergeFrom(from.oneof_nested_message());
      break;
    }
    case kOneofString: {
      set_oneof_string(from.oneof_string());
      break;
    }
    case kOneofEnum: {
      set_oneof_enum(from.oneof_enum());
      break;
    }
    case ONEOF_FIELD_NOT_SET: {
      break;
    }
  }
  if (from.optional_int32() != 0) {
    set_optional_int32(from.optional_int32());
  }
  if (from.optional_int64() != 0) {
    set_optional_int64(from.optional_int64());
  }
  if (from.optional_uint32() != 0) {
    set_optional_uint32(from.optional_uint32());
  }
  if (from.optional_uint64() != 0) {
    set_optional_uint64(from.optional_uint64());
  }
  if (from.optional_sint32() != 0) {
    set_optional_sint32(from.optional_sint32());
  }
  if (from.optional_sint64() != 0) {
    set_optional_sint64(from.optional_sint64());
  }
  if (from.optional_fixed32() != 0) {
    set_optional_fixed32(from.optional_fixed32());
  }
  if (from.optional_fixed64() != 0) {
    set_optional_fixed64(from.optional_fixed64());
  }
  if (from.optional_sfixed32() != 0) {
    set_optional_sfixed32(from.optional_sfixed32());
  }
  if (from.optional_sfixed64() != 0) {
    set_optional_sfixed64(from.optional_sfixed64());
  }
  if (from.optional_float() != 0) {
    set_optional_float(from.optional_float());
  }
  if (from.optional_double() != 0) {
    set_optional_double(from.optional_double());
  }
  if (from.optional_bool() != 0) {
    set_optional_bool(from.optional_bool());
  }
  if (from.optional_string().size() > 0) {

    optional_string_.AssignWithDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), from.optional_string_);
  }
  if (from.optional_bytes().size() > 0) {

    optional_bytes_.AssignWithDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), from.optional_bytes_);
  }
  if (from.has_optional_nested_message()) {
    mutable_optional_nested_message()->::proto2_nofieldpresence_unittest::TestAllTypes_NestedMessage::MergeFrom(from.optional_nested_message());
  }
  if (from.has_optional_foreign_message()) {
    mutable_optional_foreign_message()->::proto2_nofieldpresence_unittest::ForeignMessage::MergeFrom(from.optional_foreign_message());
  }
  if (from.has_optional_proto2_message()) {
    mutable_optional_proto2_message()->::protobuf_unittest::TestAllTypes::MergeFrom(from.optional_proto2_message());
  }
  if (from.optional_nested_enum() != 0) {
    set_optional_nested_enum(from.optional_nested_enum());
  }
  if (from.optional_foreign_enum() != 0) {
    set_optional_foreign_enum(from.optional_foreign_enum());
  }
  if (from.optional_string_piece().size() > 0) {

    optional_string_piece_.AssignWithDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), from.optional_string_piece_);
  }
  if (from.optional_cord().size() > 0) {

    optional_cord_.AssignWithDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), from.optional_cord_);
  }
  if (from.has_optional_lazy_message()) {
    mutable_optional_lazy_message()->::proto2_nofieldpresence_unittest::TestAllTypes_NestedMessage::MergeFrom(from.optional_lazy_message());
  }
}

void TestAllTypes::CopyFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_copy_from_start:proto2_nofieldpresence_unittest.TestAllTypes)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

void TestAllTypes::CopyFrom(const TestAllTypes& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:proto2_nofieldpresence_unittest.TestAllTypes)
  if (&from == this) return;
  Clear();
  UnsafeMergeFrom(from);
}

bool TestAllTypes::IsInitialized() const {

  return true;
}

void TestAllTypes::Swap(TestAllTypes* other) {
  if (other == this) return;
  InternalSwap(other);
}
void TestAllTypes::InternalSwap(TestAllTypes* other) {
  std::swap(optional_int32_, other->optional_int32_);
  std::swap(optional_int64_, other->optional_int64_);
  std::swap(optional_uint32_, other->optional_uint32_);
  std::swap(optional_uint64_, other->optional_uint64_);
  std::swap(optional_sint32_, other->optional_sint32_);
  std::swap(optional_sint64_, other->optional_sint64_);
  std::swap(optional_fixed32_, other->optional_fixed32_);
  std::swap(optional_fixed64_, other->optional_fixed64_);
  std::swap(optional_sfixed32_, other->optional_sfixed32_);
  std::swap(optional_sfixed64_, other->optional_sfixed64_);
  std::swap(optional_float_, other->optional_float_);
  std::swap(optional_double_, other->optional_double_);
  std::swap(optional_bool_, other->optional_bool_);
  optional_string_.Swap(&other->optional_string_);
  optional_bytes_.Swap(&other->optional_bytes_);
  std::swap(optional_nested_message_, other->optional_nested_message_);
  std::swap(optional_foreign_message_, other->optional_foreign_message_);
  std::swap(optional_proto2_message_, other->optional_proto2_message_);
  std::swap(optional_nested_enum_, other->optional_nested_enum_);
  std::swap(optional_foreign_enum_, other->optional_foreign_enum_);
  optional_string_piece_.Swap(&other->optional_string_piece_);
  optional_cord_.Swap(&other->optional_cord_);
  std::swap(optional_lazy_message_, other->optional_lazy_message_);
  repeated_int32_.UnsafeArenaSwap(&other->repeated_int32_);
  repeated_int64_.UnsafeArenaSwap(&other->repeated_int64_);
  repeated_uint32_.UnsafeArenaSwap(&other->repeated_uint32_);
  repeated_uint64_.UnsafeArenaSwap(&other->repeated_uint64_);
  repeated_sint32_.UnsafeArenaSwap(&other->repeated_sint32_);
  repeated_sint64_.UnsafeArenaSwap(&other->repeated_sint64_);
  repeated_fixed32_.UnsafeArenaSwap(&other->repeated_fixed32_);
  repeated_fixed64_.UnsafeArenaSwap(&other->repeated_fixed64_);
  repeated_sfixed32_.UnsafeArenaSwap(&other->repeated_sfixed32_);
  repeated_sfixed64_.UnsafeArenaSwap(&other->repeated_sfixed64_);
  repeated_float_.UnsafeArenaSwap(&other->repeated_float_);
  repeated_double_.UnsafeArenaSwap(&other->repeated_double_);
  repeated_bool_.UnsafeArenaSwap(&other->repeated_bool_);
  repeated_string_.UnsafeArenaSwap(&other->repeated_string_);
  repeated_bytes_.UnsafeArenaSwap(&other->repeated_bytes_);
  repeated_nested_message_.UnsafeArenaSwap(&other->repeated_nested_message_);
  repeated_foreign_message_.UnsafeArenaSwap(&other->repeated_foreign_message_);
  repeated_proto2_message_.UnsafeArenaSwap(&other->repeated_proto2_message_);
  repeated_nested_enum_.UnsafeArenaSwap(&other->repeated_nested_enum_);
  repeated_foreign_enum_.UnsafeArenaSwap(&other->repeated_foreign_enum_);
  repeated_string_piece_.UnsafeArenaSwap(&other->repeated_string_piece_);
  repeated_cord_.UnsafeArenaSwap(&other->repeated_cord_);
  repeated_lazy_message_.UnsafeArenaSwap(&other->repeated_lazy_message_);
  std::swap(oneof_field_, other->oneof_field_);
  std::swap(_oneof_case_[0], other->_oneof_case_[0]);
  _internal_metadata_.Swap(&other->_internal_metadata_);
  std::swap(_cached_size_, other->_cached_size_);
}

::google::protobuf::Metadata TestAllTypes::GetMetadata() const {
  protobuf_AssignDescriptorsOnce();
  ::google::protobuf::Metadata metadata;
  metadata.descriptor = TestAllTypes_descriptor_;
  metadata.reflection = TestAllTypes_reflection_;
  return metadata;
}

#if PROTOBUF_INLINE_NOT_IN_HEADERS
// TestAllTypes_NestedMessage

// optional int32 bb = 1;
void TestAllTypes_NestedMessage::clear_bb() {
  bb_ = 0;
}
::google::protobuf::int32 TestAllTypes_NestedMessage::bb() const {
  // @@protoc_insertion_point(field_get:proto2_nofieldpresence_unittest.TestAllTypes.NestedMessage.bb)
  return bb_;
}
void TestAllTypes_NestedMessage::set_bb(::google::protobuf::int32 value) {
  
  bb_ = value;
  // @@protoc_insertion_point(field_set:proto2_nofieldpresence_unittest.TestAllTypes.NestedMessage.bb)
}

inline const TestAllTypes_NestedMessage* TestAllTypes_NestedMessage::internal_default_instance() {
  return &TestAllTypes_NestedMessage_default_instance_.get();
}
// -------------------------------------------------------------------

// TestAllTypes

// optional int32 optional_int32 = 1;
void TestAllTypes::clear_optional_int32() {
  optional_int32_ = 0;
}
::google::protobuf::int32 TestAllTypes::optional_int32() const {
  // @@protoc_insertion_point(field_get:proto2_nofieldpresence_unittest.TestAllTypes.optional_int32)
  return optional_int32_;
}
void TestAllTypes::set_optional_int32(::google::protobuf::int32 value) {
  
  optional_int32_ = value;
  // @@protoc_insertion_point(field_set:proto2_nofieldpresence_unittest.TestAllTypes.optional_int32)
}

// optional int64 optional_int64 = 2;
void TestAllTypes::clear_optional_int64() {
  optional_int64_ = GOOGLE_LONGLONG(0);
}
::google::protobuf::int64 TestAllTypes::optional_int64() const {
  // @@protoc_insertion_point(field_get:proto2_nofieldpresence_unittest.TestAllTypes.optional_int64)
  return optional_int64_;
}
void TestAllTypes::set_optional_int64(::google::protobuf::int64 value) {
  
  optional_int64_ = value;
  // @@protoc_insertion_point(field_set:proto2_nofieldpresence_unittest.TestAllTypes.optional_int64)
}

// optional uint32 optional_uint32 = 3;
void TestAllTypes::clear_optional_uint32() {
  optional_uint32_ = 0u;
}
::google::protobuf::uint32 TestAllTypes::optional_uint32() const {
  // @@protoc_insertion_point(field_get:proto2_nofieldpresence_unittest.TestAllTypes.optional_uint32)
  return optional_uint32_;
}
void TestAllTypes::set_optional_uint32(::google::protobuf::uint32 value) {
  
  optional_uint32_ = value;
  // @@protoc_insertion_point(field_set:proto2_nofieldpresence_unittest.TestAllTypes.optional_uint32)
}

// optional uint64 optional_uint64 = 4;
void TestAllTypes::clear_optional_uint64() {
  optional_uint64_ = GOOGLE_ULONGLONG(0);
}
::google::protobuf::uint64 TestAllTypes::optional_uint64() const {
  // @@protoc_insertion_point(field_get:proto2_nofieldpresence_unittest.TestAllTypes.optional_uint64)
  return optional_uint64_;
}
void TestAllTypes::set_optional_uint64(::google::protobuf::uint64 value) {
  
  optional_uint64_ = value;
  // @@protoc_insertion_point(field_set:proto2_nofieldpresence_unittest.TestAllTypes.optional_uint64)
}

// optional sint32 optional_sint32 = 5;
void TestAllTypes::clear_optional_sint32() {
  optional_sint32_ = 0;
}
::google::protobuf::int32 TestAllTypes::optional_sint32() const {
  // @@protoc_insertion_point(field_get:proto2_nofieldpresence_unittest.TestAllTypes.optional_sint32)
  return optional_sint32_;
}
void TestAllTypes::set_optional_sint32(::google::protobuf::int32 value) {
  
  optional_sint32_ = value;
  // @@protoc_insertion_point(field_set:proto2_nofieldpresence_unittest.TestAllTypes.optional_sint32)
}

// optional sint64 optional_sint64 = 6;
void TestAllTypes::clear_optional_sint64() {
  optional_sint64_ = GOOGLE_LONGLONG(0);
}
::google::protobuf::int64 TestAllTypes::optional_sint64() const {
  // @@protoc_insertion_point(field_get:proto2_nofieldpresence_unittest.TestAllTypes.optional_sint64)
  return optional_sint64_;
}
void TestAllTypes::set_optional_sint64(::google::protobuf::int64 value) {
  
  optional_sint64_ = value;
  // @@protoc_insertion_point(field_set:proto2_nofieldpresence_unittest.TestAllTypes.optional_sint64)
}

// optional fixed32 optional_fixed32 = 7;
void TestAllTypes::clear_optional_fixed32() {
  optional_fixed32_ = 0u;
}
::google::protobuf::uint32 TestAllTypes::optional_fixed32() const {
  // @@protoc_insertion_point(field_get:proto2_nofieldpresence_unittest.TestAllTypes.optional_fixed32)
  return optional_fixed32_;
}
void TestAllTypes::set_optional_fixed32(::google::protobuf::uint32 value) {
  
  optional_fixed32_ = value;
  // @@protoc_insertion_point(field_set:proto2_nofieldpresence_unittest.TestAllTypes.optional_fixed32)
}

// optional fixed64 optional_fixed64 = 8;
void TestAllTypes::clear_optional_fixed64() {
  optional_fixed64_ = GOOGLE_ULONGLONG(0);
}
::google::protobuf::uint64 TestAllTypes::optional_fixed64() const {
  // @@protoc_insertion_point(field_get:proto2_nofieldpresence_unittest.TestAllTypes.optional_fixed64)
  return optional_fixed64_;
}
void TestAllTypes::set_optional_fixed64(::google::protobuf::uint64 value) {
  
  optional_fixed64_ = value;
  // @@protoc_insertion_point(field_set:proto2_nofieldpresence_unittest.TestAllTypes.optional_fixed64)
}

// optional sfixed32 optional_sfixed32 = 9;
void TestAllTypes::clear_optional_sfixed32() {
  optional_sfixed32_ = 0;
}
::google::protobuf::int32 TestAllTypes::optional_sfixed32() const {
  // @@protoc_insertion_point(field_get:proto2_nofieldpresence_unittest.TestAllTypes.optional_sfixed32)
  return optional_sfixed32_;
}
void TestAllTypes::set_optional_sfixed32(::google::protobuf::int32 value) {
  
  optional_sfixed32_ = value;
  // @@protoc_insertion_point(field_set:proto2_nofieldpresence_unittest.TestAllTypes.optional_sfixed32)
}

// optional sfixed64 optional_sfixed64 = 10;
void TestAllTypes::clear_optional_sfixed64() {
  optional_sfixed64_ = GOOGLE_LONGLONG(0);
}
::google::protobuf::int64 TestAllTypes::optional_sfixed64() const {
  // @@protoc_insertion_point(field_get:proto2_nofieldpresence_unittest.TestAllTypes.optional_sfixed64)
  return optional_sfixed64_;
}
void TestAllTypes::set_optional_sfixed64(::google::protobuf::int64 value) {
  
  optional_sfixed64_ = value;
  // @@protoc_insertion_point(field_set:proto2_nofieldpresence_unittest.TestAllTypes.optional_sfixed64)
}

// optional float optional_float = 11;
void TestAllTypes::clear_optional_float() {
  optional_float_ = 0;
}
float TestAllTypes::optional_float() const {
  // @@protoc_insertion_point(field_get:proto2_nofieldpresence_unittest.TestAllTypes.optional_float)
  return optional_float_;
}
void TestAllTypes::set_optional_float(float value) {
  
  optional_float_ = value;
  // @@protoc_insertion_point(field_set:proto2_nofieldpresence_unittest.TestAllTypes.optional_float)
}

// optional double optional_double = 12;
void TestAllTypes::clear_optional_double() {
  optional_double_ = 0;
}
double TestAllTypes::optional_double() const {
  // @@protoc_insertion_point(field_get:proto2_nofieldpresence_unittest.TestAllTypes.optional_double)
  return optional_double_;
}
void TestAllTypes::set_optional_double(double value) {
  
  optional_double_ = value;
  // @@protoc_insertion_point(field_set:proto2_nofieldpresence_unittest.TestAllTypes.optional_double)
}

// optional bool optional_bool = 13;
void TestAllTypes::clear_optional_bool() {
  optional_bool_ = false;
}
bool TestAllTypes::optional_bool() const {
  // @@protoc_insertion_point(field_get:proto2_nofieldpresence_unittest.TestAllTypes.optional_bool)
  return optional_bool_;
}
void TestAllTypes::set_optional_bool(bool value) {
  
  optional_bool_ = value;
  // @@protoc_insertion_point(field_set:proto2_nofieldpresence_unittest.TestAllTypes.optional_bool)
}

// optional string optional_string = 14;
void TestAllTypes::clear_optional_string() {
  optional_string_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
const ::std::string& TestAllTypes::optional_string() const {
  // @@protoc_insertion_point(field_get:proto2_nofieldpresence_unittest.TestAllTypes.optional_string)
  return optional_string_.GetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
void TestAllTypes::set_optional_string(const ::std::string& value) {
  
  optional_string_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:proto2_nofieldpresence_unittest.TestAllTypes.optional_string)
}
void TestAllTypes::set_optional_string(const char* value) {
  
  optional_string_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:proto2_nofieldpresence_unittest.TestAllTypes.optional_string)
}
void TestAllTypes::set_optional_string(const char* value, size_t size) {
  
  optional_string_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:proto2_nofieldpresence_unittest.TestAllTypes.optional_string)
}
::std::string* TestAllTypes::mutable_optional_string() {
  
  // @@protoc_insertion_point(field_mutable:proto2_nofieldpresence_unittest.TestAllTypes.optional_string)
  return optional_string_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
::std::string* TestAllTypes::release_optional_string() {
  // @@protoc_insertion_point(field_release:proto2_nofieldpresence_unittest.TestAllTypes.optional_string)
  
  return optional_string_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
void TestAllTypes::set_allocated_optional_string(::std::string* optional_string) {
  if (optional_string != NULL) {
    
  } else {
    
  }
  optional_string_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), optional_string);
  // @@protoc_insertion_point(field_set_allocated:proto2_nofieldpresence_unittest.TestAllTypes.optional_string)
}

// optional bytes optional_bytes = 15;
void TestAllTypes::clear_optional_bytes() {
  optional_bytes_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
const ::std::string& TestAllTypes::optional_bytes() const {
  // @@protoc_insertion_point(field_get:proto2_nofieldpresence_unittest.TestAllTypes.optional_bytes)
  return optional_bytes_.GetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
void TestAllTypes::set_optional_bytes(const ::std::string& value) {
  
  optional_bytes_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:proto2_nofieldpresence_unittest.TestAllTypes.optional_bytes)
}
void TestAllTypes::set_optional_bytes(const char* value) {
  
  optional_bytes_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:proto2_nofieldpresence_unittest.TestAllTypes.optional_bytes)
}
void TestAllTypes::set_optional_bytes(const void* value, size_t size) {
  
  optional_bytes_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:proto2_nofieldpresence_unittest.TestAllTypes.optional_bytes)
}
::std::string* TestAllTypes::mutable_optional_bytes() {
  
  // @@protoc_insertion_point(field_mutable:proto2_nofieldpresence_unittest.TestAllTypes.optional_bytes)
  return optional_bytes_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
::std::string* TestAllTypes::release_optional_bytes() {
  // @@protoc_insertion_point(field_release:proto2_nofieldpresence_unittest.TestAllTypes.optional_bytes)
  
  return optional_bytes_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
void TestAllTypes::set_allocated_optional_bytes(::std::string* optional_bytes) {
  if (optional_bytes != NULL) {
    
  } else {
    
  }
  optional_bytes_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), optional_bytes);
  // @@protoc_insertion_point(field_set_allocated:proto2_nofieldpresence_unittest.TestAllTypes.optional_bytes)
}

// optional .proto2_nofieldpresence_unittest.TestAllTypes.NestedMessage optional_nested_message = 18;
bool TestAllTypes::has_optional_nested_message() const {
  return this != internal_default_instance() && optional_nested_message_ != NULL;
}
void TestAllTypes::clear_optional_nested_message() {
  if (GetArenaNoVirtual() == NULL && optional_nested_message_ != NULL) delete optional_nested_message_;
  optional_nested_message_ = NULL;
}
const ::proto2_nofieldpresence_unittest::TestAllTypes_NestedMessage& TestAllTypes::optional_nested_message() const {
  // @@protoc_insertion_point(field_get:proto2_nofieldpresence_unittest.TestAllTypes.optional_nested_message)
  return optional_nested_message_ != NULL ? *optional_nested_message_
                         : *::proto2_nofieldpresence_unittest::TestAllTypes_NestedMessage::internal_default_instance();
}
::proto2_nofieldpresence_unittest::TestAllTypes_NestedMessage* TestAllTypes::mutable_optional_nested_message() {
  
  if (optional_nested_message_ == NULL) {
    optional_nested_message_ = new ::proto2_nofieldpresence_unittest::TestAllTypes_NestedMessage;
  }
  // @@protoc_insertion_point(field_mutable:proto2_nofieldpresence_unittest.TestAllTypes.optional_nested_message)
  return optional_nested_message_;
}
::proto2_nofieldpresence_unittest::TestAllTypes_NestedMessage* TestAllTypes::release_optional_nested_message() {
  // @@protoc_insertion_point(field_release:proto2_nofieldpresence_unittest.TestAllTypes.optional_nested_message)
  
  ::proto2_nofieldpresence_unittest::TestAllTypes_NestedMessage* temp = optional_nested_message_;
  optional_nested_message_ = NULL;
  return temp;
}
void TestAllTypes::set_allocated_optional_nested_message(::proto2_nofieldpresence_unittest::TestAllTypes_NestedMessage* optional_nested_message) {
  delete optional_nested_message_;
  optional_nested_message_ = optional_nested_message;
  if (optional_nested_message) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_set_allocated:proto2_nofieldpresence_unittest.TestAllTypes.optional_nested_message)
}

// optional .proto2_nofieldpresence_unittest.ForeignMessage optional_foreign_message = 19;
bool TestAllTypes::has_optional_foreign_message() const {
  return this != internal_default_instance() && optional_foreign_message_ != NULL;
}
void TestAllTypes::clear_optional_foreign_message() {
  if (GetArenaNoVirtual() == NULL && optional_foreign_message_ != NULL) delete optional_foreign_message_;
  optional_foreign_message_ = NULL;
}
const ::proto2_nofieldpresence_unittest::ForeignMessage& TestAllTypes::optional_foreign_message() const {
  // @@protoc_insertion_point(field_get:proto2_nofieldpresence_unittest.TestAllTypes.optional_foreign_message)
  return optional_foreign_message_ != NULL ? *optional_foreign_message_
                         : *::proto2_nofieldpresence_unittest::ForeignMessage::internal_default_instance();
}
::proto2_nofieldpresence_unittest::ForeignMessage* TestAllTypes::mutable_optional_foreign_message() {
  
  if (optional_foreign_message_ == NULL) {
    optional_foreign_message_ = new ::proto2_nofieldpresence_unittest::ForeignMessage;
  }
  // @@protoc_insertion_point(field_mutable:proto2_nofieldpresence_unittest.TestAllTypes.optional_foreign_message)
  return optional_foreign_message_;
}
::proto2_nofieldpresence_unittest::ForeignMessage* TestAllTypes::release_optional_foreign_message() {
  // @@protoc_insertion_point(field_release:proto2_nofieldpresence_unittest.TestAllTypes.optional_foreign_message)
  
  ::proto2_nofieldpresence_unittest::ForeignMessage* temp = optional_foreign_message_;
  optional_foreign_message_ = NULL;
  return temp;
}
void TestAllTypes::set_allocated_optional_foreign_message(::proto2_nofieldpresence_unittest::ForeignMessage* optional_foreign_message) {
  delete optional_foreign_message_;
  optional_foreign_message_ = optional_foreign_message;
  if (optional_foreign_message) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_set_allocated:proto2_nofieldpresence_unittest.TestAllTypes.optional_foreign_message)
}

// optional .protobuf_unittest.TestAllTypes optional_proto2_message = 20;
bool TestAllTypes::has_optional_proto2_message() const {
  return this != internal_default_instance() && optional_proto2_message_ != NULL;
}
void TestAllTypes::clear_optional_proto2_message() {
  if (GetArenaNoVirtual() == NULL && optional_proto2_message_ != NULL) delete optional_proto2_message_;
  optional_proto2_message_ = NULL;
}
const ::protobuf_unittest::TestAllTypes& TestAllTypes::optional_proto2_message() const {
  // @@protoc_insertion_point(field_get:proto2_nofieldpresence_unittest.TestAllTypes.optional_proto2_message)
  return optional_proto2_message_ != NULL ? *optional_proto2_message_
                         : *::protobuf_unittest::TestAllTypes::internal_default_instance();
}
::protobuf_unittest::TestAllTypes* TestAllTypes::mutable_optional_proto2_message() {
  
  if (optional_proto2_message_ == NULL) {
    optional_proto2_message_ = new ::protobuf_unittest::TestAllTypes;
  }
  // @@protoc_insertion_point(field_mutable:proto2_nofieldpresence_unittest.TestAllTypes.optional_proto2_message)
  return optional_proto2_message_;
}
::protobuf_unittest::TestAllTypes* TestAllTypes::release_optional_proto2_message() {
  // @@protoc_insertion_point(field_release:proto2_nofieldpresence_unittest.TestAllTypes.optional_proto2_message)
  
  ::protobuf_unittest::TestAllTypes* temp = optional_proto2_message_;
  optional_proto2_message_ = NULL;
  return temp;
}
void TestAllTypes::set_allocated_optional_proto2_message(::protobuf_unittest::TestAllTypes* optional_proto2_message) {
  delete optional_proto2_message_;
  if (optional_proto2_message != NULL && optional_proto2_message->GetArena() != NULL) {
    ::protobuf_unittest::TestAllTypes* new_optional_proto2_message = new ::protobuf_unittest::TestAllTypes;
    new_optional_proto2_message->CopyFrom(*optional_proto2_message);
    optional_proto2_message = new_optional_proto2_message;
  }
  optional_proto2_message_ = optional_proto2_message;
  if (optional_proto2_message) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_set_allocated:proto2_nofieldpresence_unittest.TestAllTypes.optional_proto2_message)
}

// optional .proto2_nofieldpresence_unittest.TestAllTypes.NestedEnum optional_nested_enum = 21;
void TestAllTypes::clear_optional_nested_enum() {
  optional_nested_enum_ = 0;
}
::proto2_nofieldpresence_unittest::TestAllTypes_NestedEnum TestAllTypes::optional_nested_enum() const {
  // @@protoc_insertion_point(field_get:proto2_nofieldpresence_unittest.TestAllTypes.optional_nested_enum)
  return static_cast< ::proto2_nofieldpresence_unittest::TestAllTypes_NestedEnum >(optional_nested_enum_);
}
void TestAllTypes::set_optional_nested_enum(::proto2_nofieldpresence_unittest::TestAllTypes_NestedEnum value) {
  
  optional_nested_enum_ = value;
  // @@protoc_insertion_point(field_set:proto2_nofieldpresence_unittest.TestAllTypes.optional_nested_enum)
}

// optional .proto2_nofieldpresence_unittest.ForeignEnum optional_foreign_enum = 22;
void TestAllTypes::clear_optional_foreign_enum() {
  optional_foreign_enum_ = 0;
}
::proto2_nofieldpresence_unittest::ForeignEnum TestAllTypes::optional_foreign_enum() const {
  // @@protoc_insertion_point(field_get:proto2_nofieldpresence_unittest.TestAllTypes.optional_foreign_enum)
  return static_cast< ::proto2_nofieldpresence_unittest::ForeignEnum >(optional_foreign_enum_);
}
void TestAllTypes::set_optional_foreign_enum(::proto2_nofieldpresence_unittest::ForeignEnum value) {
  
  optional_foreign_enum_ = value;
  // @@protoc_insertion_point(field_set:proto2_nofieldpresence_unittest.TestAllTypes.optional_foreign_enum)
}

// optional string optional_string_piece = 24 [ctype = STRING_PIECE];
void TestAllTypes::clear_optional_string_piece() {
  optional_string_piece_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
const ::std::string& TestAllTypes::optional_string_piece() const {
  // @@protoc_insertion_point(field_get:proto2_nofieldpresence_unittest.TestAllTypes.optional_string_piece)
  return optional_string_piece_.GetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
void TestAllTypes::set_optional_string_piece(const ::std::string& value) {
  
  optional_string_piece_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:proto2_nofieldpresence_unittest.TestAllTypes.optional_string_piece)
}
void TestAllTypes::set_optional_string_piece(const char* value) {
  
  optional_string_piece_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:proto2_nofieldpresence_unittest.TestAllTypes.optional_string_piece)
}
void TestAllTypes::set_optional_string_piece(const char* value, size_t size) {
  
  optional_string_piece_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:proto2_nofieldpresence_unittest.TestAllTypes.optional_string_piece)
}
::std::string* TestAllTypes::mutable_optional_string_piece() {
  
  // @@protoc_insertion_point(field_mutable:proto2_nofieldpresence_unittest.TestAllTypes.optional_string_piece)
  return optional_string_piece_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
::std::string* TestAllTypes::release_optional_string_piece() {
  // @@protoc_insertion_point(field_release:proto2_nofieldpresence_unittest.TestAllTypes.optional_string_piece)
  
  return optional_string_piece_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
void TestAllTypes::set_allocated_optional_string_piece(::std::string* optional_string_piece) {
  if (optional_string_piece != NULL) {
    
  } else {
    
  }
  optional_string_piece_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), optional_string_piece);
  // @@protoc_insertion_point(field_set_allocated:proto2_nofieldpresence_unittest.TestAllTypes.optional_string_piece)
}

// optional string optional_cord = 25 [ctype = CORD];
void TestAllTypes::clear_optional_cord() {
  optional_cord_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
const ::std::string& TestAllTypes::optional_cord() const {
  // @@protoc_insertion_point(field_get:proto2_nofieldpresence_unittest.TestAllTypes.optional_cord)
  return optional_cord_.GetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
void TestAllTypes::set_optional_cord(const ::std::string& value) {
  
  optional_cord_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:proto2_nofieldpresence_unittest.TestAllTypes.optional_cord)
}
void TestAllTypes::set_optional_cord(const char* value) {
  
  optional_cord_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:proto2_nofieldpresence_unittest.TestAllTypes.optional_cord)
}
void TestAllTypes::set_optional_cord(const char* value, size_t size) {
  
  optional_cord_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:proto2_nofieldpresence_unittest.TestAllTypes.optional_cord)
}
::std::string* TestAllTypes::mutable_optional_cord() {
  
  // @@protoc_insertion_point(field_mutable:proto2_nofieldpresence_unittest.TestAllTypes.optional_cord)
  return optional_cord_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
::std::string* TestAllTypes::release_optional_cord() {
  // @@protoc_insertion_point(field_release:proto2_nofieldpresence_unittest.TestAllTypes.optional_cord)
  
  return optional_cord_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
void TestAllTypes::set_allocated_optional_cord(::std::string* optional_cord) {
  if (optional_cord != NULL) {
    
  } else {
    
  }
  optional_cord_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), optional_cord);
  // @@protoc_insertion_point(field_set_allocated:proto2_nofieldpresence_unittest.TestAllTypes.optional_cord)
}

// optional .proto2_nofieldpresence_unittest.TestAllTypes.NestedMessage optional_lazy_message = 30 [lazy = true];
bool TestAllTypes::has_optional_lazy_message() const {
  return this != internal_default_instance() && optional_lazy_message_ != NULL;
}
void TestAllTypes::clear_optional_lazy_message() {
  if (GetArenaNoVirtual() == NULL && optional_lazy_message_ != NULL) delete optional_lazy_message_;
  optional_lazy_message_ = NULL;
}
const ::proto2_nofieldpresence_unittest::TestAllTypes_NestedMessage& TestAllTypes::optional_lazy_message() const {
  // @@protoc_insertion_point(field_get:proto2_nofieldpresence_unittest.TestAllTypes.optional_lazy_message)
  return optional_lazy_message_ != NULL ? *optional_lazy_message_
                         : *::proto2_nofieldpresence_unittest::TestAllTypes_NestedMessage::internal_default_instance();
}
::proto2_nofieldpresence_unittest::TestAllTypes_NestedMessage* TestAllTypes::mutable_optional_lazy_message() {
  
  if (optional_lazy_message_ == NULL) {
    optional_lazy_message_ = new ::proto2_nofieldpresence_unittest::TestAllTypes_NestedMessage;
  }
  // @@protoc_insertion_point(field_mutable:proto2_nofieldpresence_unittest.TestAllTypes.optional_lazy_message)
  return optional_lazy_message_;
}
::proto2_nofieldpresence_unittest::TestAllTypes_NestedMessage* TestAllTypes::release_optional_lazy_message() {
  // @@protoc_insertion_point(field_release:proto2_nofieldpresence_unittest.TestAllTypes.optional_lazy_message)
  
  ::proto2_nofieldpresence_unittest::TestAllTypes_NestedMessage* temp = optional_lazy_message_;
  optional_lazy_message_ = NULL;
  return temp;
}
void TestAllTypes::set_allocated_optional_lazy_message(::proto2_nofieldpresence_unittest::TestAllTypes_NestedMessage* optional_lazy_message) {
  delete optional_lazy_message_;
  optional_lazy_message_ = optional_lazy_message;
  if (optional_lazy_message) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_set_allocated:proto2_nofieldpresence_unittest.TestAllTypes.optional_lazy_message)
}

// repeated int32 repeated_int32 = 31;
int TestAllTypes::repeated_int32_size() const {
  return repeated_int32_.size();
}
void TestAllTypes::clear_repeated_int32() {
  repeated_int32_.Clear();
}
::google::protobuf::int32 TestAllTypes::repeated_int32(int index) const {
  // @@protoc_insertion_point(field_get:proto2_nofieldpresence_unittest.TestAllTypes.repeated_int32)
  return repeated_int32_.Get(index);
}
void TestAllTypes::set_repeated_int32(int index, ::google::protobuf::int32 value) {
  repeated_int32_.Set(index, value);
  // @@protoc_insertion_point(field_set:proto2_nofieldpresence_unittest.TestAllTypes.repeated_int32)
}
void TestAllTypes::add_repeated_int32(::google::protobuf::int32 value) {
  repeated_int32_.Add(value);
  // @@protoc_insertion_point(field_add:proto2_nofieldpresence_unittest.TestAllTypes.repeated_int32)
}
const ::google::protobuf::RepeatedField< ::google::protobuf::int32 >&
TestAllTypes::repeated_int32() const {
  // @@protoc_insertion_point(field_list:proto2_nofieldpresence_unittest.TestAllTypes.repeated_int32)
  return repeated_int32_;
}
::google::protobuf::RepeatedField< ::google::protobuf::int32 >*
TestAllTypes::mutable_repeated_int32() {
  // @@protoc_insertion_point(field_mutable_list:proto2_nofieldpresence_unittest.TestAllTypes.repeated_int32)
  return &repeated_int32_;
}

// repeated int64 repeated_int64 = 32;
int TestAllTypes::repeated_int64_size() const {
  return repeated_int64_.size();
}
void TestAllTypes::clear_repeated_int64() {
  repeated_int64_.Clear();
}
::google::protobuf::int64 TestAllTypes::repeated_int64(int index) const {
  // @@protoc_insertion_point(field_get:proto2_nofieldpresence_unittest.TestAllTypes.repeated_int64)
  return repeated_int64_.Get(index);
}
void TestAllTypes::set_repeated_int64(int index, ::google::protobuf::int64 value) {
  repeated_int64_.Set(index, value);
  // @@protoc_insertion_point(field_set:proto2_nofieldpresence_unittest.TestAllTypes.repeated_int64)
}
void TestAllTypes::add_repeated_int64(::google::protobuf::int64 value) {
  repeated_int64_.Add(value);
  // @@protoc_insertion_point(field_add:proto2_nofieldpresence_unittest.TestAllTypes.repeated_int64)
}
const ::google::protobuf::RepeatedField< ::google::protobuf::int64 >&
TestAllTypes::repeated_int64() const {
  // @@protoc_insertion_point(field_list:proto2_nofieldpresence_unittest.TestAllTypes.repeated_int64)
  return repeated_int64_;
}
::google::protobuf::RepeatedField< ::google::protobuf::int64 >*
TestAllTypes::mutable_repeated_int64() {
  // @@protoc_insertion_point(field_mutable_list:proto2_nofieldpresence_unittest.TestAllTypes.repeated_int64)
  return &repeated_int64_;
}

// repeated uint32 repeated_uint32 = 33;
int TestAllTypes::repeated_uint32_size() const {
  return repeated_uint32_.size();
}
void TestAllTypes::clear_repeated_uint32() {
  repeated_uint32_.Clear();
}
::google::protobuf::uint32 TestAllTypes::repeated_uint32(int index) const {
  // @@protoc_insertion_point(field_get:proto2_nofieldpresence_unittest.TestAllTypes.repeated_uint32)
  return repeated_uint32_.Get(index);
}
void TestAllTypes::set_repeated_uint32(int index, ::google::protobuf::uint32 value) {
  repeated_uint32_.Set(index, value);
  // @@protoc_insertion_point(field_set:proto2_nofieldpresence_unittest.TestAllTypes.repeated_uint32)
}
void TestAllTypes::add_repeated_uint32(::google::protobuf::uint32 value) {
  repeated_uint32_.Add(value);
  // @@protoc_insertion_point(field_add:proto2_nofieldpresence_unittest.TestAllTypes.repeated_uint32)
}
const ::google::protobuf::RepeatedField< ::google::protobuf::uint32 >&
TestAllTypes::repeated_uint32() const {
  // @@protoc_insertion_point(field_list:proto2_nofieldpresence_unittest.TestAllTypes.repeated_uint32)
  return repeated_uint32_;
}
::google::protobuf::RepeatedField< ::google::protobuf::uint32 >*
TestAllTypes::mutable_repeated_uint32() {
  // @@protoc_insertion_point(field_mutable_list:proto2_nofieldpresence_unittest.TestAllTypes.repeated_uint32)
  return &repeated_uint32_;
}

// repeated uint64 repeated_uint64 = 34;
int TestAllTypes::repeated_uint64_size() const {
  return repeated_uint64_.size();
}
void TestAllTypes::clear_repeated_uint64() {
  repeated_uint64_.Clear();
}
::google::protobuf::uint64 TestAllTypes::repeated_uint64(int index) const {
  // @@protoc_insertion_point(field_get:proto2_nofieldpresence_unittest.TestAllTypes.repeated_uint64)
  return repeated_uint64_.Get(index);
}
void TestAllTypes::set_repeated_uint64(int index, ::google::protobuf::uint64 value) {
  repeated_uint64_.Set(index, value);
  // @@protoc_insertion_point(field_set:proto2_nofieldpresence_unittest.TestAllTypes.repeated_uint64)
}
void TestAllTypes::add_repeated_uint64(::google::protobuf::uint64 value) {
  repeated_uint64_.Add(value);
  // @@protoc_insertion_point(field_add:proto2_nofieldpresence_unittest.TestAllTypes.repeated_uint64)
}
const ::google::protobuf::RepeatedField< ::google::protobuf::uint64 >&
TestAllTypes::repeated_uint64() const {
  // @@protoc_insertion_point(field_list:proto2_nofieldpresence_unittest.TestAllTypes.repeated_uint64)
  return repeated_uint64_;
}
::google::protobuf::RepeatedField< ::google::protobuf::uint64 >*
TestAllTypes::mutable_repeated_uint64() {
  // @@protoc_insertion_point(field_mutable_list:proto2_nofieldpresence_unittest.TestAllTypes.repeated_uint64)
  return &repeated_uint64_;
}

// repeated sint32 repeated_sint32 = 35;
int TestAllTypes::repeated_sint32_size() const {
  return repeated_sint32_.size();
}
void TestAllTypes::clear_repeated_sint32() {
  repeated_sint32_.Clear();
}
::google::protobuf::int32 TestAllTypes::repeated_sint32(int index) const {
  // @@protoc_insertion_point(field_get:proto2_nofieldpresence_unittest.TestAllTypes.repeated_sint32)
  return repeated_sint32_.Get(index);
}
void TestAllTypes::set_repeated_sint32(int index, ::google::protobuf::int32 value) {
  repeated_sint32_.Set(index, value);
  // @@protoc_insertion_point(field_set:proto2_nofieldpresence_unittest.TestAllTypes.repeated_sint32)
}
void TestAllTypes::add_repeated_sint32(::google::protobuf::int32 value) {
  repeated_sint32_.Add(value);
  // @@protoc_insertion_point(field_add:proto2_nofieldpresence_unittest.TestAllTypes.repeated_sint32)
}
const ::google::protobuf::RepeatedField< ::google::protobuf::int32 >&
TestAllTypes::repeated_sint32() const {
  // @@protoc_insertion_point(field_list:proto2_nofieldpresence_unittest.TestAllTypes.repeated_sint32)
  return repeated_sint32_;
}
::google::protobuf::RepeatedField< ::google::protobuf::int32 >*
TestAllTypes::mutable_repeated_sint32() {
  // @@protoc_insertion_point(field_mutable_list:proto2_nofieldpresence_unittest.TestAllTypes.repeated_sint32)
  return &repeated_sint32_;
}

// repeated sint64 repeated_sint64 = 36;
int TestAllTypes::repeated_sint64_size() const {
  return repeated_sint64_.size();
}
void TestAllTypes::clear_repeated_sint64() {
  repeated_sint64_.Clear();
}
::google::protobuf::int64 TestAllTypes::repeated_sint64(int index) const {
  // @@protoc_insertion_point(field_get:proto2_nofieldpresence_unittest.TestAllTypes.repeated_sint64)
  return repeated_sint64_.Get(index);
}
void TestAllTypes::set_repeated_sint64(int index, ::google::protobuf::int64 value) {
  repeated_sint64_.Set(index, value);
  // @@protoc_insertion_point(field_set:proto2_nofieldpresence_unittest.TestAllTypes.repeated_sint64)
}
void TestAllTypes::add_repeated_sint64(::google::protobuf::int64 value) {
  repeated_sint64_.Add(value);
  // @@protoc_insertion_point(field_add:proto2_nofieldpresence_unittest.TestAllTypes.repeated_sint64)
}
const ::google::protobuf::RepeatedField< ::google::protobuf::int64 >&
TestAllTypes::repeated_sint64() const {
  // @@protoc_insertion_point(field_list:proto2_nofieldpresence_unittest.TestAllTypes.repeated_sint64)
  return repeated_sint64_;
}
::google::protobuf::RepeatedField< ::google::protobuf::int64 >*
TestAllTypes::mutable_repeated_sint64() {
  // @@protoc_insertion_point(field_mutable_list:proto2_nofieldpresence_unittest.TestAllTypes.repeated_sint64)
  return &repeated_sint64_;
}

// repeated fixed32 repeated_fixed32 = 37;
int TestAllTypes::repeated_fixed32_size() const {
  return repeated_fixed32_.size();
}
void TestAllTypes::clear_repeated_fixed32() {
  repeated_fixed32_.Clear();
}
::google::protobuf::uint32 TestAllTypes::repeated_fixed32(int index) const {
  // @@protoc_insertion_point(field_get:proto2_nofieldpresence_unittest.TestAllTypes.repeated_fixed32)
  return repeated_fixed32_.Get(index);
}
void TestAllTypes::set_repeated_fixed32(int index, ::google::protobuf::uint32 value) {
  repeated_fixed32_.Set(index, value);
  // @@protoc_insertion_point(field_set:proto2_nofieldpresence_unittest.TestAllTypes.repeated_fixed32)
}
void TestAllTypes::add_repeated_fixed32(::google::protobuf::uint32 value) {
  repeated_fixed32_.Add(value);
  // @@protoc_insertion_point(field_add:proto2_nofieldpresence_unittest.TestAllTypes.repeated_fixed32)
}
const ::google::protobuf::RepeatedField< ::google::protobuf::uint32 >&
TestAllTypes::repeated_fixed32() const {
  // @@protoc_insertion_point(field_list:proto2_nofieldpresence_unittest.TestAllTypes.repeated_fixed32)
  return repeated_fixed32_;
}
::google::protobuf::RepeatedField< ::google::protobuf::uint32 >*
TestAllTypes::mutable_repeated_fixed32() {
  // @@protoc_insertion_point(field_mutable_list:proto2_nofieldpresence_unittest.TestAllTypes.repeated_fixed32)
  return &repeated_fixed32_;
}

// repeated fixed64 repeated_fixed64 = 38;
int TestAllTypes::repeated_fixed64_size() const {
  return repeated_fixed64_.size();
}
void TestAllTypes::clear_repeated_fixed64() {
  repeated_fixed64_.Clear();
}
::google::protobuf::uint64 TestAllTypes::repeated_fixed64(int index) const {
  // @@protoc_insertion_point(field_get:proto2_nofieldpresence_unittest.TestAllTypes.repeated_fixed64)
  return repeated_fixed64_.Get(index);
}
void TestAllTypes::set_repeated_fixed64(int index, ::google::protobuf::uint64 value) {
  repeated_fixed64_.Set(index, value);
  // @@protoc_insertion_point(field_set:proto2_nofieldpresence_unittest.TestAllTypes.repeated_fixed64)
}
void TestAllTypes::add_repeated_fixed64(::google::protobuf::uint64 value) {
  repeated_fixed64_.Add(value);
  // @@protoc_insertion_point(field_add:proto2_nofieldpresence_unittest.TestAllTypes.repeated_fixed64)
}
const ::google::protobuf::RepeatedField< ::google::protobuf::uint64 >&
TestAllTypes::repeated_fixed64() const {
  // @@protoc_insertion_point(field_list:proto2_nofieldpresence_unittest.TestAllTypes.repeated_fixed64)
  return repeated_fixed64_;
}
::google::protobuf::RepeatedField< ::google::protobuf::uint64 >*
TestAllTypes::mutable_repeated_fixed64() {
  // @@protoc_insertion_point(field_mutable_list:proto2_nofieldpresence_unittest.TestAllTypes.repeated_fixed64)
  return &repeated_fixed64_;
}

// repeated sfixed32 repeated_sfixed32 = 39;
int TestAllTypes::repeated_sfixed32_size() const {
  return repeated_sfixed32_.size();
}
void TestAllTypes::clear_repeated_sfixed32() {
  repeated_sfixed32_.Clear();
}
::google::protobuf::int32 TestAllTypes::repeated_sfixed32(int index) const {
  // @@protoc_insertion_point(field_get:proto2_nofieldpresence_unittest.TestAllTypes.repeated_sfixed32)
  return repeated_sfixed32_.Get(index);
}
void TestAllTypes::set_repeated_sfixed32(int index, ::google::protobuf::int32 value) {
  repeated_sfixed32_.Set(index, value);
  // @@protoc_insertion_point(field_set:proto2_nofieldpresence_unittest.TestAllTypes.repeated_sfixed32)
}
void TestAllTypes::add_repeated_sfixed32(::google::protobuf::int32 value) {
  repeated_sfixed32_.Add(value);
  // @@protoc_insertion_point(field_add:proto2_nofieldpresence_unittest.TestAllTypes.repeated_sfixed32)
}
const ::google::protobuf::RepeatedField< ::google::protobuf::int32 >&
TestAllTypes::repeated_sfixed32() const {
  // @@protoc_insertion_point(field_list:proto2_nofieldpresence_unittest.TestAllTypes.repeated_sfixed32)
  return repeated_sfixed32_;
}
::google::protobuf::RepeatedField< ::google::protobuf::int32 >*
TestAllTypes::mutable_repeated_sfixed32() {
  // @@protoc_insertion_point(field_mutable_list:proto2_nofieldpresence_unittest.TestAllTypes.repeated_sfixed32)
  return &repeated_sfixed32_;
}

// repeated sfixed64 repeated_sfixed64 = 40;
int TestAllTypes::repeated_sfixed64_size() const {
  return repeated_sfixed64_.size();
}
void TestAllTypes::clear_repeated_sfixed64() {
  repeated_sfixed64_.Clear();
}
::google::protobuf::int64 TestAllTypes::repeated_sfixed64(int index) const {
  // @@protoc_insertion_point(field_get:proto2_nofieldpresence_unittest.TestAllTypes.repeated_sfixed64)
  return repeated_sfixed64_.Get(index);
}
void TestAllTypes::set_repeated_sfixed64(int index, ::google::protobuf::int64 value) {
  repeated_sfixed64_.Set(index, value);
  // @@protoc_insertion_point(field_set:proto2_nofieldpresence_unittest.TestAllTypes.repeated_sfixed64)
}
void TestAllTypes::add_repeated_sfixed64(::google::protobuf::int64 value) {
  repeated_sfixed64_.Add(value);
  // @@protoc_insertion_point(field_add:proto2_nofieldpresence_unittest.TestAllTypes.repeated_sfixed64)
}
const ::google::protobuf::RepeatedField< ::google::protobuf::int64 >&
TestAllTypes::repeated_sfixed64() const {
  // @@protoc_insertion_point(field_list:proto2_nofieldpresence_unittest.TestAllTypes.repeated_sfixed64)
  return repeated_sfixed64_;
}
::google::protobuf::RepeatedField< ::google::protobuf::int64 >*
TestAllTypes::mutable_repeated_sfixed64() {
  // @@protoc_insertion_point(field_mutable_list:proto2_nofieldpresence_unittest.TestAllTypes.repeated_sfixed64)
  return &repeated_sfixed64_;
}

// repeated float repeated_float = 41;
int TestAllTypes::repeated_float_size() const {
  return repeated_float_.size();
}
void TestAllTypes::clear_repeated_float() {
  repeated_float_.Clear();
}
float TestAllTypes::repeated_float(int index) const {
  // @@protoc_insertion_point(field_get:proto2_nofieldpresence_unittest.TestAllTypes.repeated_float)
  return repeated_float_.Get(index);
}
void TestAllTypes::set_repeated_float(int index, float value) {
  repeated_float_.Set(index, value);
  // @@protoc_insertion_point(field_set:proto2_nofieldpresence_unittest.TestAllTypes.repeated_float)
}
void TestAllTypes::add_repeated_float(float value) {
  repeated_float_.Add(value);
  // @@protoc_insertion_point(field_add:proto2_nofieldpresence_unittest.TestAllTypes.repeated_float)
}
const ::google::protobuf::RepeatedField< float >&
TestAllTypes::repeated_float() const {
  // @@protoc_insertion_point(field_list:proto2_nofieldpresence_unittest.TestAllTypes.repeated_float)
  return repeated_float_;
}
::google::protobuf::RepeatedField< float >*
TestAllTypes::mutable_repeated_float() {
  // @@protoc_insertion_point(field_mutable_list:proto2_nofieldpresence_unittest.TestAllTypes.repeated_float)
  return &repeated_float_;
}

// repeated double repeated_double = 42;
int TestAllTypes::repeated_double_size() const {
  return repeated_double_.size();
}
void TestAllTypes::clear_repeated_double() {
  repeated_double_.Clear();
}
double TestAllTypes::repeated_double(int index) const {
  // @@protoc_insertion_point(field_get:proto2_nofieldpresence_unittest.TestAllTypes.repeated_double)
  return repeated_double_.Get(index);
}
void TestAllTypes::set_repeated_double(int index, double value) {
  repeated_double_.Set(index, value);
  // @@protoc_insertion_point(field_set:proto2_nofieldpresence_unittest.TestAllTypes.repeated_double)
}
void TestAllTypes::add_repeated_double(double value) {
  repeated_double_.Add(value);
  // @@protoc_insertion_point(field_add:proto2_nofieldpresence_unittest.TestAllTypes.repeated_double)
}
const ::google::protobuf::RepeatedField< double >&
TestAllTypes::repeated_double() const {
  // @@protoc_insertion_point(field_list:proto2_nofieldpresence_unittest.TestAllTypes.repeated_double)
  return repeated_double_;
}
::google::protobuf::RepeatedField< double >*
TestAllTypes::mutable_repeated_double() {
  // @@protoc_insertion_point(field_mutable_list:proto2_nofieldpresence_unittest.TestAllTypes.repeated_double)
  return &repeated_double_;
}

// repeated bool repeated_bool = 43;
int TestAllTypes::repeated_bool_size() const {
  return repeated_bool_.size();
}
void TestAllTypes::clear_repeated_bool() {
  repeated_bool_.Clear();
}
bool TestAllTypes::repeated_bool(int index) const {
  // @@protoc_insertion_point(field_get:proto2_nofieldpresence_unittest.TestAllTypes.repeated_bool)
  return repeated_bool_.Get(index);
}
void TestAllTypes::set_repeated_bool(int index, bool value) {
  repeated_bool_.Set(index, value);
  // @@protoc_insertion_point(field_set:proto2_nofieldpresence_unittest.TestAllTypes.repeated_bool)
}
void TestAllTypes::add_repeated_bool(bool value) {
  repeated_bool_.Add(value);
  // @@protoc_insertion_point(field_add:proto2_nofieldpresence_unittest.TestAllTypes.repeated_bool)
}
const ::google::protobuf::RepeatedField< bool >&
TestAllTypes::repeated_bool() const {
  // @@protoc_insertion_point(field_list:proto2_nofieldpresence_unittest.TestAllTypes.repeated_bool)
  return repeated_bool_;
}
::google::protobuf::RepeatedField< bool >*
TestAllTypes::mutable_repeated_bool() {
  // @@protoc_insertion_point(field_mutable_list:proto2_nofieldpresence_unittest.TestAllTypes.repeated_bool)
  return &repeated_bool_;
}

// repeated string repeated_string = 44;
int TestAllTypes::repeated_string_size() const {
  return repeated_string_.size();
}
void TestAllTypes::clear_repeated_string() {
  repeated_string_.Clear();
}
const ::std::string& TestAllTypes::repeated_string(int index) const {
  // @@protoc_insertion_point(field_get:proto2_nofieldpresence_unittest.TestAllTypes.repeated_string)
  return repeated_string_.Get(index);
}
::std::string* TestAllTypes::mutable_repeated_string(int index) {
  // @@protoc_insertion_point(field_mutable:proto2_nofieldpresence_unittest.TestAllTypes.repeated_string)
  return repeated_string_.Mutable(index);
}
void TestAllTypes::set_repeated_string(int index, const ::std::string& value) {
  // @@protoc_insertion_point(field_set:proto2_nofieldpresence_unittest.TestAllTypes.repeated_string)
  repeated_string_.Mutable(index)->assign(value);
}
void TestAllTypes::set_repeated_string(int index, const char* value) {
  repeated_string_.Mutable(index)->assign(value);
  // @@protoc_insertion_point(field_set_char:proto2_nofieldpresence_unittest.TestAllTypes.repeated_string)
}
void TestAllTypes::set_repeated_string(int index, const char* value, size_t size) {
  repeated_string_.Mutable(index)->assign(
    reinterpret_cast<const char*>(value), size);
  // @@protoc_insertion_point(field_set_pointer:proto2_nofieldpresence_unittest.TestAllTypes.repeated_string)
}
::std::string* TestAllTypes::add_repeated_string() {
  // @@protoc_insertion_point(field_add_mutable:proto2_nofieldpresence_unittest.TestAllTypes.repeated_string)
  return repeated_string_.Add();
}
void TestAllTypes::add_repeated_string(const ::std::string& value) {
  repeated_string_.Add()->assign(value);
  // @@protoc_insertion_point(field_add:proto2_nofieldpresence_unittest.TestAllTypes.repeated_string)
}
void TestAllTypes::add_repeated_string(const char* value) {
  repeated_string_.Add()->assign(value);
  // @@protoc_insertion_point(field_add_char:proto2_nofieldpresence_unittest.TestAllTypes.repeated_string)
}
void TestAllTypes::add_repeated_string(const char* value, size_t size) {
  repeated_string_.Add()->assign(reinterpret_cast<const char*>(value), size);
  // @@protoc_insertion_point(field_add_pointer:proto2_nofieldpresence_unittest.TestAllTypes.repeated_string)
}
const ::google::protobuf::RepeatedPtrField< ::std::string>&
TestAllTypes::repeated_string() const {
  // @@protoc_insertion_point(field_list:proto2_nofieldpresence_unittest.TestAllTypes.repeated_string)
  return repeated_string_;
}
::google::protobuf::RepeatedPtrField< ::std::string>*
TestAllTypes::mutable_repeated_string() {
  // @@protoc_insertion_point(field_mutable_list:proto2_nofieldpresence_unittest.TestAllTypes.repeated_string)
  return &repeated_string_;
}

// repeated bytes repeated_bytes = 45;
int TestAllTypes::repeated_bytes_size() const {
  return repeated_bytes_.size();
}
void TestAllTypes::clear_repeated_bytes() {
  repeated_bytes_.Clear();
}
const ::std::string& TestAllTypes::repeated_bytes(int index) const {
  // @@protoc_insertion_point(field_get:proto2_nofieldpresence_unittest.TestAllTypes.repeated_bytes)
  return repeated_bytes_.Get(index);
}
::std::string* TestAllTypes::mutable_repeated_bytes(int index) {
  // @@protoc_insertion_point(field_mutable:proto2_nofieldpresence_unittest.TestAllTypes.repeated_bytes)
  return repeated_bytes_.Mutable(index);
}
void TestAllTypes::set_repeated_bytes(int index, const ::std::string& value) {
  // @@protoc_insertion_point(field_set:proto2_nofieldpresence_unittest.TestAllTypes.repeated_bytes)
  repeated_bytes_.Mutable(index)->assign(value);
}
void TestAllTypes::set_repeated_bytes(int index, const char* value) {
  repeated_bytes_.Mutable(index)->assign(value);
  // @@protoc_insertion_point(field_set_char:proto2_nofieldpresence_unittest.TestAllTypes.repeated_bytes)
}
void TestAllTypes::set_repeated_bytes(int index, const void* value, size_t size) {
  repeated_bytes_.Mutable(index)->assign(
    reinterpret_cast<const char*>(value), size);
  // @@protoc_insertion_point(field_set_pointer:proto2_nofieldpresence_unittest.TestAllTypes.repeated_bytes)
}
::std::string* TestAllTypes::add_repeated_bytes() {
  // @@protoc_insertion_point(field_add_mutable:proto2_nofieldpresence_unittest.TestAllTypes.repeated_bytes)
  return repeated_bytes_.Add();
}
void TestAllTypes::add_repeated_bytes(const ::std::string& value) {
  repeated_bytes_.Add()->assign(value);
  // @@protoc_insertion_point(field_add:proto2_nofieldpresence_unittest.TestAllTypes.repeated_bytes)
}
void TestAllTypes::add_repeated_bytes(const char* value) {
  repeated_bytes_.Add()->assign(value);
  // @@protoc_insertion_point(field_add_char:proto2_nofieldpresence_unittest.TestAllTypes.repeated_bytes)
}
void TestAllTypes::add_repeated_bytes(const void* value, size_t size) {
  repeated_bytes_.Add()->assign(reinterpret_cast<const char*>(value), size);
  // @@protoc_insertion_point(field_add_pointer:proto2_nofieldpresence_unittest.TestAllTypes.repeated_bytes)
}
const ::google::protobuf::RepeatedPtrField< ::std::string>&
TestAllTypes::repeated_bytes() const {
  // @@protoc_insertion_point(field_list:proto2_nofieldpresence_unittest.TestAllTypes.repeated_bytes)
  return repeated_bytes_;
}
::google::protobuf::RepeatedPtrField< ::std::string>*
TestAllTypes::mutable_repeated_bytes() {
  // @@protoc_insertion_point(field_mutable_list:proto2_nofieldpresence_unittest.TestAllTypes.repeated_bytes)
  return &repeated_bytes_;
}

// repeated .proto2_nofieldpresence_unittest.TestAllTypes.NestedMessage repeated_nested_message = 48;
int TestAllTypes::repeated_nested_message_size() const {
  return repeated_nested_message_.size();
}
void TestAllTypes::clear_repeated_nested_message() {
  repeated_nested_message_.Clear();
}
const ::proto2_nofieldpresence_unittest::TestAllTypes_NestedMessage& TestAllTypes::repeated_nested_message(int index) const {
  // @@protoc_insertion_point(field_get:proto2_nofieldpresence_unittest.TestAllTypes.repeated_nested_message)
  return repeated_nested_message_.Get(index);
}
::proto2_nofieldpresence_unittest::TestAllTypes_NestedMessage* TestAllTypes::mutable_repeated_nested_message(int index) {
  // @@protoc_insertion_point(field_mutable:proto2_nofieldpresence_unittest.TestAllTypes.repeated_nested_message)
  return repeated_nested_message_.Mutable(index);
}
::proto2_nofieldpresence_unittest::TestAllTypes_NestedMessage* TestAllTypes::add_repeated_nested_message() {
  // @@protoc_insertion_point(field_add:proto2_nofieldpresence_unittest.TestAllTypes.repeated_nested_message)
  return repeated_nested_message_.Add();
}
::google::protobuf::RepeatedPtrField< ::proto2_nofieldpresence_unittest::TestAllTypes_NestedMessage >*
TestAllTypes::mutable_repeated_nested_message() {
  // @@protoc_insertion_point(field_mutable_list:proto2_nofieldpresence_unittest.TestAllTypes.repeated_nested_message)
  return &repeated_nested_message_;
}
const ::google::protobuf::RepeatedPtrField< ::proto2_nofieldpresence_unittest::TestAllTypes_NestedMessage >&
TestAllTypes::repeated_nested_message() const {
  // @@protoc_insertion_point(field_list:proto2_nofieldpresence_unittest.TestAllTypes.repeated_nested_message)
  return repeated_nested_message_;
}

// repeated .proto2_nofieldpresence_unittest.ForeignMessage repeated_foreign_message = 49;
int TestAllTypes::repeated_foreign_message_size() const {
  return repeated_foreign_message_.size();
}
void TestAllTypes::clear_repeated_foreign_message() {
  repeated_foreign_message_.Clear();
}
const ::proto2_nofieldpresence_unittest::ForeignMessage& TestAllTypes::repeated_foreign_message(int index) const {
  // @@protoc_insertion_point(field_get:proto2_nofieldpresence_unittest.TestAllTypes.repeated_foreign_message)
  return repeated_foreign_message_.Get(index);
}
::proto2_nofieldpresence_unittest::ForeignMessage* TestAllTypes::mutable_repeated_foreign_message(int index) {
  // @@protoc_insertion_point(field_mutable:proto2_nofieldpresence_unittest.TestAllTypes.repeated_foreign_message)
  return repeated_foreign_message_.Mutable(index);
}
::proto2_nofieldpresence_unittest::ForeignMessage* TestAllTypes::add_repeated_foreign_message() {
  // @@protoc_insertion_point(field_add:proto2_nofieldpresence_unittest.TestAllTypes.repeated_foreign_message)
  return repeated_foreign_message_.Add();
}
::google::protobuf::RepeatedPtrField< ::proto2_nofieldpresence_unittest::ForeignMessage >*
TestAllTypes::mutable_repeated_foreign_message() {
  // @@protoc_insertion_point(field_mutable_list:proto2_nofieldpresence_unittest.TestAllTypes.repeated_foreign_message)
  return &repeated_foreign_message_;
}
const ::google::protobuf::RepeatedPtrField< ::proto2_nofieldpresence_unittest::ForeignMessage >&
TestAllTypes::repeated_foreign_message() const {
  // @@protoc_insertion_point(field_list:proto2_nofieldpresence_unittest.TestAllTypes.repeated_foreign_message)
  return repeated_foreign_message_;
}

// repeated .protobuf_unittest.TestAllTypes repeated_proto2_message = 50;
int TestAllTypes::repeated_proto2_message_size() const {
  return repeated_proto2_message_.size();
}
void TestAllTypes::clear_repeated_proto2_message() {
  repeated_proto2_message_.Clear();
}
const ::protobuf_unittest::TestAllTypes& TestAllTypes::repeated_proto2_message(int index) const {
  // @@protoc_insertion_point(field_get:proto2_nofieldpresence_unittest.TestAllTypes.repeated_proto2_message)
  return repeated_proto2_message_.Get(index);
}
::protobuf_unittest::TestAllTypes* TestAllTypes::mutable_repeated_proto2_message(int index) {
  // @@protoc_insertion_point(field_mutable:proto2_nofieldpresence_unittest.TestAllTypes.repeated_proto2_message)
  return repeated_proto2_message_.Mutable(index);
}
::protobuf_unittest::TestAllTypes* TestAllTypes::add_repeated_proto2_message() {
  // @@protoc_insertion_point(field_add:proto2_nofieldpresence_unittest.TestAllTypes.repeated_proto2_message)
  return repeated_proto2_message_.Add();
}
::google::protobuf::RepeatedPtrField< ::protobuf_unittest::TestAllTypes >*
TestAllTypes::mutable_repeated_proto2_message() {
  // @@protoc_insertion_point(field_mutable_list:proto2_nofieldpresence_unittest.TestAllTypes.repeated_proto2_message)
  return &repeated_proto2_message_;
}
const ::google::protobuf::RepeatedPtrField< ::protobuf_unittest::TestAllTypes >&
TestAllTypes::repeated_proto2_message() const {
  // @@protoc_insertion_point(field_list:proto2_nofieldpresence_unittest.TestAllTypes.repeated_proto2_message)
  return repeated_proto2_message_;
}

// repeated .proto2_nofieldpresence_unittest.TestAllTypes.NestedEnum repeated_nested_enum = 51;
int TestAllTypes::repeated_nested_enum_size() const {
  return repeated_nested_enum_.size();
}
void TestAllTypes::clear_repeated_nested_enum() {
  repeated_nested_enum_.Clear();
}
::proto2_nofieldpresence_unittest::TestAllTypes_NestedEnum TestAllTypes::repeated_nested_enum(int index) const {
  // @@protoc_insertion_point(field_get:proto2_nofieldpresence_unittest.TestAllTypes.repeated_nested_enum)
  return static_cast< ::proto2_nofieldpresence_unittest::TestAllTypes_NestedEnum >(repeated_nested_enum_.Get(index));
}
void TestAllTypes::set_repeated_nested_enum(int index, ::proto2_nofieldpresence_unittest::TestAllTypes_NestedEnum value) {
  repeated_nested_enum_.Set(index, value);
  // @@protoc_insertion_point(field_set:proto2_nofieldpresence_unittest.TestAllTypes.repeated_nested_enum)
}
void TestAllTypes::add_repeated_nested_enum(::proto2_nofieldpresence_unittest::TestAllTypes_NestedEnum value) {
  repeated_nested_enum_.Add(value);
  // @@protoc_insertion_point(field_add:proto2_nofieldpresence_unittest.TestAllTypes.repeated_nested_enum)
}
const ::google::protobuf::RepeatedField<int>&
TestAllTypes::repeated_nested_enum() const {
  // @@protoc_insertion_point(field_list:proto2_nofieldpresence_unittest.TestAllTypes.repeated_nested_enum)
  return repeated_nested_enum_;
}
::google::protobuf::RepeatedField<int>*
TestAllTypes::mutable_repeated_nested_enum() {
  // @@protoc_insertion_point(field_mutable_list:proto2_nofieldpresence_unittest.TestAllTypes.repeated_nested_enum)
  return &repeated_nested_enum_;
}

// repeated .proto2_nofieldpresence_unittest.ForeignEnum repeated_foreign_enum = 52;
int TestAllTypes::repeated_foreign_enum_size() const {
  return repeated_foreign_enum_.size();
}
void TestAllTypes::clear_repeated_foreign_enum() {
  repeated_foreign_enum_.Clear();
}
::proto2_nofieldpresence_unittest::ForeignEnum TestAllTypes::repeated_foreign_enum(int index) const {
  // @@protoc_insertion_point(field_get:proto2_nofieldpresence_unittest.TestAllTypes.repeated_foreign_enum)
  return static_cast< ::proto2_nofieldpresence_unittest::ForeignEnum >(repeated_foreign_enum_.Get(index));
}
void TestAllTypes::set_repeated_foreign_enum(int index, ::proto2_nofieldpresence_unittest::ForeignEnum value) {
  repeated_foreign_enum_.Set(index, value);
  // @@protoc_insertion_point(field_set:proto2_nofieldpresence_unittest.TestAllTypes.repeated_foreign_enum)
}
void TestAllTypes::add_repeated_foreign_enum(::proto2_nofieldpresence_unittest::ForeignEnum value) {
  repeated_foreign_enum_.Add(value);
  // @@protoc_insertion_point(field_add:proto2_nofieldpresence_unittest.TestAllTypes.repeated_foreign_enum)
}
const ::google::protobuf::RepeatedField<int>&
TestAllTypes::repeated_foreign_enum() const {
  // @@protoc_insertion_point(field_list:proto2_nofieldpresence_unittest.TestAllTypes.repeated_foreign_enum)
  return repeated_foreign_enum_;
}
::google::protobuf::RepeatedField<int>*
TestAllTypes::mutable_repeated_foreign_enum() {
  // @@protoc_insertion_point(field_mutable_list:proto2_nofieldpresence_unittest.TestAllTypes.repeated_foreign_enum)
  return &repeated_foreign_enum_;
}

// repeated string repeated_string_piece = 54 [ctype = STRING_PIECE];
int TestAllTypes::repeated_string_piece_size() const {
  return repeated_string_piece_.size();
}
void TestAllTypes::clear_repeated_string_piece() {
  repeated_string_piece_.Clear();
}
const ::std::string& TestAllTypes::repeated_string_piece(int index) const {
  // @@protoc_insertion_point(field_get:proto2_nofieldpresence_unittest.TestAllTypes.repeated_string_piece)
  return repeated_string_piece_.Get(index);
}
::std::string* TestAllTypes::mutable_repeated_string_piece(int index) {
  // @@protoc_insertion_point(field_mutable:proto2_nofieldpresence_unittest.TestAllTypes.repeated_string_piece)
  return repeated_string_piece_.Mutable(index);
}
void TestAllTypes::set_repeated_string_piece(int index, const ::std::string& value) {
  // @@protoc_insertion_point(field_set:proto2_nofieldpresence_unittest.TestAllTypes.repeated_string_piece)
  repeated_string_piece_.Mutable(index)->assign(value);
}
void TestAllTypes::set_repeated_string_piece(int index, const char* value) {
  repeated_string_piece_.Mutable(index)->assign(value);
  // @@protoc_insertion_point(field_set_char:proto2_nofieldpresence_unittest.TestAllTypes.repeated_string_piece)
}
void TestAllTypes::set_repeated_string_piece(int index, const char* value, size_t size) {
  repeated_string_piece_.Mutable(index)->assign(
    reinterpret_cast<const char*>(value), size);
  // @@protoc_insertion_point(field_set_pointer:proto2_nofieldpresence_unittest.TestAllTypes.repeated_string_piece)
}
::std::string* TestAllTypes::add_repeated_string_piece() {
  // @@protoc_insertion_point(field_add_mutable:proto2_nofieldpresence_unittest.TestAllTypes.repeated_string_piece)
  return repeated_string_piece_.Add();
}
void TestAllTypes::add_repeated_string_piece(const ::std::string& value) {
  repeated_string_piece_.Add()->assign(value);
  // @@protoc_insertion_point(field_add:proto2_nofieldpresence_unittest.TestAllTypes.repeated_string_piece)
}
void TestAllTypes::add_repeated_string_piece(const char* value) {
  repeated_string_piece_.Add()->assign(value);
  // @@protoc_insertion_point(field_add_char:proto2_nofieldpresence_unittest.TestAllTypes.repeated_string_piece)
}
void TestAllTypes::add_repeated_string_piece(const char* value, size_t size) {
  repeated_string_piece_.Add()->assign(reinterpret_cast<const char*>(value), size);
  // @@protoc_insertion_point(field_add_pointer:proto2_nofieldpresence_unittest.TestAllTypes.repeated_string_piece)
}
const ::google::protobuf::RepeatedPtrField< ::std::string>&
TestAllTypes::repeated_string_piece() const {
  // @@protoc_insertion_point(field_list:proto2_nofieldpresence_unittest.TestAllTypes.repeated_string_piece)
  return repeated_string_piece_;
}
::google::protobuf::RepeatedPtrField< ::std::string>*
TestAllTypes::mutable_repeated_string_piece() {
  // @@protoc_insertion_point(field_mutable_list:proto2_nofieldpresence_unittest.TestAllTypes.repeated_string_piece)
  return &repeated_string_piece_;
}

// repeated string repeated_cord = 55 [ctype = CORD];
int TestAllTypes::repeated_cord_size() const {
  return repeated_cord_.size();
}
void TestAllTypes::clear_repeated_cord() {
  repeated_cord_.Clear();
}
const ::std::string& TestAllTypes::repeated_cord(int index) const {
  // @@protoc_insertion_point(field_get:proto2_nofieldpresence_unittest.TestAllTypes.repeated_cord)
  return repeated_cord_.Get(index);
}
::std::string* TestAllTypes::mutable_repeated_cord(int index) {
  // @@protoc_insertion_point(field_mutable:proto2_nofieldpresence_unittest.TestAllTypes.repeated_cord)
  return repeated_cord_.Mutable(index);
}
void TestAllTypes::set_repeated_cord(int index, const ::std::string& value) {
  // @@protoc_insertion_point(field_set:proto2_nofieldpresence_unittest.TestAllTypes.repeated_cord)
  repeated_cord_.Mutable(index)->assign(value);
}
void TestAllTypes::set_repeated_cord(int index, const char* value) {
  repeated_cord_.Mutable(index)->assign(value);
  // @@protoc_insertion_point(field_set_char:proto2_nofieldpresence_unittest.TestAllTypes.repeated_cord)
}
void TestAllTypes::set_repeated_cord(int index, const char* value, size_t size) {
  repeated_cord_.Mutable(index)->assign(
    reinterpret_cast<const char*>(value), size);
  // @@protoc_insertion_point(field_set_pointer:proto2_nofieldpresence_unittest.TestAllTypes.repeated_cord)
}
::std::string* TestAllTypes::add_repeated_cord() {
  // @@protoc_insertion_point(field_add_mutable:proto2_nofieldpresence_unittest.TestAllTypes.repeated_cord)
  return repeated_cord_.Add();
}
void TestAllTypes::add_repeated_cord(const ::std::string& value) {
  repeated_cord_.Add()->assign(value);
  // @@protoc_insertion_point(field_add:proto2_nofieldpresence_unittest.TestAllTypes.repeated_cord)
}
void TestAllTypes::add_repeated_cord(const char* value) {
  repeated_cord_.Add()->assign(value);
  // @@protoc_insertion_point(field_add_char:proto2_nofieldpresence_unittest.TestAllTypes.repeated_cord)
}
void TestAllTypes::add_repeated_cord(const char* value, size_t size) {
  repeated_cord_.Add()->assign(reinterpret_cast<const char*>(value), size);
  // @@protoc_insertion_point(field_add_pointer:proto2_nofieldpresence_unittest.TestAllTypes.repeated_cord)
}
const ::google::protobuf::RepeatedPtrField< ::std::string>&
TestAllTypes::repeated_cord() const {
  // @@protoc_insertion_point(field_list:proto2_nofieldpresence_unittest.TestAllTypes.repeated_cord)
  return repeated_cord_;
}
::google::protobuf::RepeatedPtrField< ::std::string>*
TestAllTypes::mutable_repeated_cord() {
  // @@protoc_insertion_point(field_mutable_list:proto2_nofieldpresence_unittest.TestAllTypes.repeated_cord)
  return &repeated_cord_;
}

// repeated .proto2_nofieldpresence_unittest.TestAllTypes.NestedMessage repeated_lazy_message = 57 [lazy = true];
int TestAllTypes::repeated_lazy_message_size() const {
  return repeated_lazy_message_.size();
}
void TestAllTypes::clear_repeated_lazy_message() {
  repeated_lazy_message_.Clear();
}
const ::proto2_nofieldpresence_unittest::TestAllTypes_NestedMessage& TestAllTypes::repeated_lazy_message(int index) const {
  // @@protoc_insertion_point(field_get:proto2_nofieldpresence_unittest.TestAllTypes.repeated_lazy_message)
  return repeated_lazy_message_.Get(index);
}
::proto2_nofieldpresence_unittest::TestAllTypes_NestedMessage* TestAllTypes::mutable_repeated_lazy_message(int index) {
  // @@protoc_insertion_point(field_mutable:proto2_nofieldpresence_unittest.TestAllTypes.repeated_lazy_message)
  return repeated_lazy_message_.Mutable(index);
}
::proto2_nofieldpresence_unittest::TestAllTypes_NestedMessage* TestAllTypes::add_repeated_lazy_message() {
  // @@protoc_insertion_point(field_add:proto2_nofieldpresence_unittest.TestAllTypes.repeated_lazy_message)
  return repeated_lazy_message_.Add();
}
::google::protobuf::RepeatedPtrField< ::proto2_nofieldpresence_unittest::TestAllTypes_NestedMessage >*
TestAllTypes::mutable_repeated_lazy_message() {
  // @@protoc_insertion_point(field_mutable_list:proto2_nofieldpresence_unittest.TestAllTypes.repeated_lazy_message)
  return &repeated_lazy_message_;
}
const ::google::protobuf::RepeatedPtrField< ::proto2_nofieldpresence_unittest::TestAllTypes_NestedMessage >&
TestAllTypes::repeated_lazy_message() const {
  // @@protoc_insertion_point(field_list:proto2_nofieldpresence_unittest.TestAllTypes.repeated_lazy_message)
  return repeated_lazy_message_;
}

// optional uint32 oneof_uint32 = 111;
bool TestAllTypes::has_oneof_uint32() const {
  return oneof_field_case() == kOneofUint32;
}
void TestAllTypes::set_has_oneof_uint32() {
  _oneof_case_[0] = kOneofUint32;
}
void TestAllTypes::clear_oneof_uint32() {
  if (has_oneof_uint32()) {
    oneof_field_.oneof_uint32_ = 0u;
    clear_has_oneof_field();
  }
}
::google::protobuf::uint32 TestAllTypes::oneof_uint32() const {
  // @@protoc_insertion_point(field_get:proto2_nofieldpresence_unittest.TestAllTypes.oneof_uint32)
  if (has_oneof_uint32()) {
    return oneof_field_.oneof_uint32_;
  }
  return 0u;
}
void TestAllTypes::set_oneof_uint32(::google::protobuf::uint32 value) {
  if (!has_oneof_uint32()) {
    clear_oneof_field();
    set_has_oneof_uint32();
  }
  oneof_field_.oneof_uint32_ = value;
  // @@protoc_insertion_point(field_set:proto2_nofieldpresence_unittest.TestAllTypes.oneof_uint32)
}

// optional .proto2_nofieldpresence_unittest.TestAllTypes.NestedMessage oneof_nested_message = 112;
bool TestAllTypes::has_oneof_nested_message() const {
  return oneof_field_case() == kOneofNestedMessage;
}
void TestAllTypes::set_has_oneof_nested_message() {
  _oneof_case_[0] = kOneofNestedMessage;
}
void TestAllTypes::clear_oneof_nested_message() {
  if (has_oneof_nested_message()) {
    delete oneof_field_.oneof_nested_message_;
    clear_has_oneof_field();
  }
}
 const ::proto2_nofieldpresence_unittest::TestAllTypes_NestedMessage& TestAllTypes::oneof_nested_message() const {
  // @@protoc_insertion_point(field_get:proto2_nofieldpresence_unittest.TestAllTypes.oneof_nested_message)
  return has_oneof_nested_message()
      ? *oneof_field_.oneof_nested_message_
      : ::proto2_nofieldpresence_unittest::TestAllTypes_NestedMessage::default_instance();
}
::proto2_nofieldpresence_unittest::TestAllTypes_NestedMessage* TestAllTypes::mutable_oneof_nested_message() {
  if (!has_oneof_nested_message()) {
    clear_oneof_field();
    set_has_oneof_nested_message();
    oneof_field_.oneof_nested_message_ = new ::proto2_nofieldpresence_unittest::TestAllTypes_NestedMessage;
  }
  // @@protoc_insertion_point(field_mutable:proto2_nofieldpresence_unittest.TestAllTypes.oneof_nested_message)
  return oneof_field_.oneof_nested_message_;
}
::proto2_nofieldpresence_unittest::TestAllTypes_NestedMessage* TestAllTypes::release_oneof_nested_message() {
  // @@protoc_insertion_point(field_release:proto2_nofieldpresence_unittest.TestAllTypes.oneof_nested_message)
  if (has_oneof_nested_message()) {
    clear_has_oneof_field();
    ::proto2_nofieldpresence_unittest::TestAllTypes_NestedMessage* temp = oneof_field_.oneof_nested_message_;
    oneof_field_.oneof_nested_message_ = NULL;
    return temp;
  } else {
    return NULL;
  }
}
void TestAllTypes::set_allocated_oneof_nested_message(::proto2_nofieldpresence_unittest::TestAllTypes_NestedMessage* oneof_nested_message) {
  clear_oneof_field();
  if (oneof_nested_message) {
    set_has_oneof_nested_message();
    oneof_field_.oneof_nested_message_ = oneof_nested_message;
  }
  // @@protoc_insertion_point(field_set_allocated:proto2_nofieldpresence_unittest.TestAllTypes.oneof_nested_message)
}

// optional string oneof_string = 113;
bool TestAllTypes::has_oneof_string() const {
  return oneof_field_case() == kOneofString;
}
void TestAllTypes::set_has_oneof_string() {
  _oneof_case_[0] = kOneofString;
}
void TestAllTypes::clear_oneof_string() {
  if (has_oneof_string()) {
    oneof_field_.oneof_string_.DestroyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
    clear_has_oneof_field();
  }
}
const ::std::string& TestAllTypes::oneof_string() const {
  // @@protoc_insertion_point(field_get:proto2_nofieldpresence_unittest.TestAllTypes.oneof_string)
  if (has_oneof_string()) {
    return oneof_field_.oneof_string_.GetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  }
  return *&::google::protobuf::internal::GetEmptyStringAlreadyInited();
}
void TestAllTypes::set_oneof_string(const ::std::string& value) {
  // @@protoc_insertion_point(field_set:proto2_nofieldpresence_unittest.TestAllTypes.oneof_string)
  if (!has_oneof_string()) {
    clear_oneof_field();
    set_has_oneof_string();
    oneof_field_.oneof_string_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  }
  oneof_field_.oneof_string_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:proto2_nofieldpresence_unittest.TestAllTypes.oneof_string)
}
void TestAllTypes::set_oneof_string(const char* value) {
  if (!has_oneof_string()) {
    clear_oneof_field();
    set_has_oneof_string();
    oneof_field_.oneof_string_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  }
  oneof_field_.oneof_string_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(value));
  // @@protoc_insertion_point(field_set_char:proto2_nofieldpresence_unittest.TestAllTypes.oneof_string)
}
void TestAllTypes::set_oneof_string(const char* value, size_t size) {
  if (!has_oneof_string()) {
    clear_oneof_field();
    set_has_oneof_string();
    oneof_field_.oneof_string_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  }
  oneof_field_.oneof_string_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(
      reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:proto2_nofieldpresence_unittest.TestAllTypes.oneof_string)
}
::std::string* TestAllTypes::mutable_oneof_string() {
  if (!has_oneof_string()) {
    clear_oneof_field();
    set_has_oneof_string();
    oneof_field_.oneof_string_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  }
  // @@protoc_insertion_point(field_mutable:proto2_nofieldpresence_unittest.TestAllTypes.oneof_string)
  return oneof_field_.oneof_string_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
::std::string* TestAllTypes::release_oneof_string() {
  // @@protoc_insertion_point(field_release:proto2_nofieldpresence_unittest.TestAllTypes.oneof_string)
  if (has_oneof_string()) {
    clear_has_oneof_field();
    return oneof_field_.oneof_string_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  } else {
    return NULL;
  }
}
void TestAllTypes::set_allocated_oneof_string(::std::string* oneof_string) {
  if (!has_oneof_string()) {
    oneof_field_.oneof_string_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  }
  clear_oneof_field();
  if (oneof_string != NULL) {
    set_has_oneof_string();
    oneof_field_.oneof_string_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
        oneof_string);
  }
  // @@protoc_insertion_point(field_set_allocated:proto2_nofieldpresence_unittest.TestAllTypes.oneof_string)
}

// optional .proto2_nofieldpresence_unittest.TestAllTypes.NestedEnum oneof_enum = 114;
bool TestAllTypes::has_oneof_enum() const {
  return oneof_field_case() == kOneofEnum;
}
void TestAllTypes::set_has_oneof_enum() {
  _oneof_case_[0] = kOneofEnum;
}
void TestAllTypes::clear_oneof_enum() {
  if (has_oneof_enum()) {
    oneof_field_.oneof_enum_ = 0;
    clear_has_oneof_field();
  }
}
::proto2_nofieldpresence_unittest::TestAllTypes_NestedEnum TestAllTypes::oneof_enum() const {
  // @@protoc_insertion_point(field_get:proto2_nofieldpresence_unittest.TestAllTypes.oneof_enum)
  if (has_oneof_enum()) {
    return static_cast< ::proto2_nofieldpresence_unittest::TestAllTypes_NestedEnum >(oneof_field_.oneof_enum_);
  }
  return static_cast< ::proto2_nofieldpresence_unittest::TestAllTypes_NestedEnum >(0);
}
void TestAllTypes::set_oneof_enum(::proto2_nofieldpresence_unittest::TestAllTypes_NestedEnum value) {
  if (!has_oneof_enum()) {
    clear_oneof_field();
    set_has_oneof_enum();
  }
  oneof_field_.oneof_enum_ = value;
  // @@protoc_insertion_point(field_set:proto2_nofieldpresence_unittest.TestAllTypes.oneof_enum)
}

bool TestAllTypes::has_oneof_field() const {
  return oneof_field_case() != ONEOF_FIELD_NOT_SET;
}
void TestAllTypes::clear_has_oneof_field() {
  _oneof_case_[0] = ONEOF_FIELD_NOT_SET;
}
TestAllTypes::OneofFieldCase TestAllTypes::oneof_field_case() const {
  return TestAllTypes::OneofFieldCase(_oneof_case_[0]);
}
inline const TestAllTypes* TestAllTypes::internal_default_instance() {
  return &TestAllTypes_default_instance_.get();
}
#endif  // PROTOBUF_INLINE_NOT_IN_HEADERS

// ===================================================================

#if !defined(_MSC_VER) || _MSC_VER >= 1900
const int TestProto2Required::kProto2FieldNumber;
#endif  // !defined(_MSC_VER) || _MSC_VER >= 1900

TestProto2Required::TestProto2Required()
  : ::google::protobuf::Message(), _internal_metadata_(NULL) {
  if (this != internal_default_instance()) protobuf_InitDefaults_google_2fprotobuf_2funittest_5fno_5ffield_5fpresence_2eproto();
  SharedCtor();
  // @@protoc_insertion_point(constructor:proto2_nofieldpresence_unittest.TestProto2Required)
}

void TestProto2Required::InitAsDefaultInstance() {
  proto2_ = const_cast< ::protobuf_unittest::TestRequired*>(
      ::protobuf_unittest::TestRequired::internal_default_instance());
}

TestProto2Required::TestProto2Required(const TestProto2Required& from)
  : ::google::protobuf::Message(),
    _internal_metadata_(NULL) {
  SharedCtor();
  UnsafeMergeFrom(from);
  // @@protoc_insertion_point(copy_constructor:proto2_nofieldpresence_unittest.TestProto2Required)
}

void TestProto2Required::SharedCtor() {
  proto2_ = NULL;
  _cached_size_ = 0;
}

TestProto2Required::~TestProto2Required() {
  // @@protoc_insertion_point(destructor:proto2_nofieldpresence_unittest.TestProto2Required)
  SharedDtor();
}

void TestProto2Required::SharedDtor() {
  if (this != &TestProto2Required_default_instance_.get()) {
    delete proto2_;
  }
}

void TestProto2Required::SetCachedSize(int size) const {
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
}
const ::google::protobuf::Descriptor* TestProto2Required::descriptor() {
  protobuf_AssignDescriptorsOnce();
  return TestProto2Required_descriptor_;
}

const TestProto2Required& TestProto2Required::default_instance() {
  protobuf_InitDefaults_google_2fprotobuf_2funittest_5fno_5ffield_5fpresence_2eproto();
  return *internal_default_instance();
}

::google::protobuf::internal::ExplicitlyConstructed<TestProto2Required> TestProto2Required_default_instance_;

TestProto2Required* TestProto2Required::New(::google::protobuf::Arena* arena) const {
  TestProto2Required* n = new TestProto2Required;
  if (arena != NULL) {
    arena->Own(n);
  }
  return n;
}

void TestProto2Required::Clear() {
// @@protoc_insertion_point(message_clear_start:proto2_nofieldpresence_unittest.TestProto2Required)
  if (GetArenaNoVirtual() == NULL && proto2_ != NULL) delete proto2_;
  proto2_ = NULL;
}

bool TestProto2Required::MergePartialFromCodedStream(
    ::google::protobuf::io::CodedInputStream* input) {
#define DO_(EXPRESSION) if (!GOOGLE_PREDICT_TRUE(EXPRESSION)) goto failure
  ::google::protobuf::uint32 tag;
  // @@protoc_insertion_point(parse_start:proto2_nofieldpresence_unittest.TestProto2Required)
  for (;;) {
    ::std::pair< ::google::protobuf::uint32, bool> p = input->ReadTagWithCutoff(127);
    tag = p.first;
    if (!p.second) goto handle_unusual;
    switch (::google::protobuf::internal::WireFormatLite::GetTagFieldNumber(tag)) {
      // optional .protobuf_unittest.TestRequired proto2 = 1;
      case 1: {
        if (tag == 10) {
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessageNoVirtual(
               input, mutable_proto2()));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectAtEnd()) goto success;
        break;
      }

      default: {
      handle_unusual:
        if (tag == 0 ||
            ::google::protobuf::internal::WireFormatLite::GetTagWireType(tag) ==
            ::google::protobuf::internal::WireFormatLite::WIRETYPE_END_GROUP) {
          goto success;
        }
        DO_(::google::protobuf::internal::WireFormatLite::SkipField(input, tag));
        break;
      }
    }
  }
success:
  // @@protoc_insertion_point(parse_success:proto2_nofieldpresence_unittest.TestProto2Required)
  return true;
failure:
  // @@protoc_insertion_point(parse_failure:proto2_nofieldpresence_unittest.TestProto2Required)
  return false;
#undef DO_
}

void TestProto2Required::SerializeWithCachedSizes(
    ::google::protobuf::io::CodedOutputStream* output) const {
  // @@protoc_insertion_point(serialize_start:proto2_nofieldpresence_unittest.TestProto2Required)
  // optional .protobuf_unittest.TestRequired proto2 = 1;
  if (this->has_proto2()) {
    ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
      1, *this->proto2_, output);
  }

  // @@protoc_insertion_point(serialize_end:proto2_nofieldpresence_unittest.TestProto2Required)
}

::google::protobuf::uint8* TestProto2Required::InternalSerializeWithCachedSizesToArray(
    bool deterministic, ::google::protobuf::uint8* target) const {
  (void)deterministic; // Unused
  // @@protoc_insertion_point(serialize_to_array_start:proto2_nofieldpresence_unittest.TestProto2Required)
  // optional .protobuf_unittest.TestRequired proto2 = 1;
  if (this->has_proto2()) {
    target = ::google::protobuf::internal::WireFormatLite::
      InternalWriteMessageNoVirtualToArray(
        1, *this->proto2_, false, target);
  }

  // @@protoc_insertion_point(serialize_to_array_end:proto2_nofieldpresence_unittest.TestProto2Required)
  return target;
}

size_t TestProto2Required::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:proto2_nofieldpresence_unittest.TestProto2Required)
  size_t total_size = 0;

  // optional .protobuf_unittest.TestRequired proto2 = 1;
  if (this->has_proto2()) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::MessageSizeNoVirtual(
        *this->proto2_);
  }

  int cached_size = ::google::protobuf::internal::ToCachedSize(total_size);
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = cached_size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
  return total_size;
}

void TestProto2Required::MergeFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_merge_from_start:proto2_nofieldpresence_unittest.TestProto2Required)
  if (GOOGLE_PREDICT_FALSE(&from == this)) MergeFromFail(__LINE__);
  const TestProto2Required* source =
      ::google::protobuf::internal::DynamicCastToGenerated<const TestProto2Required>(
          &from);
  if (source == NULL) {
  // @@protoc_insertion_point(generalized_merge_from_cast_fail:proto2_nofieldpresence_unittest.TestProto2Required)
    ::google::protobuf::internal::ReflectionOps::Merge(from, this);
  } else {
  // @@protoc_insertion_point(generalized_merge_from_cast_success:proto2_nofieldpresence_unittest.TestProto2Required)
    UnsafeMergeFrom(*source);
  }
}

void TestProto2Required::MergeFrom(const TestProto2Required& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:proto2_nofieldpresence_unittest.TestProto2Required)
  if (GOOGLE_PREDICT_TRUE(&from != this)) {
    UnsafeMergeFrom(from);
  } else {
    MergeFromFail(__LINE__);
  }
}

void TestProto2Required::UnsafeMergeFrom(const TestProto2Required& from) {
  GOOGLE_DCHECK(&from != this);
  if (from.has_proto2()) {
    mutable_proto2()->::protobuf_unittest::TestRequired::MergeFrom(from.proto2());
  }
}

void TestProto2Required::CopyFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_copy_from_start:proto2_nofieldpresence_unittest.TestProto2Required)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

void TestProto2Required::CopyFrom(const TestProto2Required& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:proto2_nofieldpresence_unittest.TestProto2Required)
  if (&from == this) return;
  Clear();
  UnsafeMergeFrom(from);
}

bool TestProto2Required::IsInitialized() const {

  if (has_proto2()) {
    if (!this->proto2_->IsInitialized()) return false;
  }
  return true;
}

void TestProto2Required::Swap(TestProto2Required* other) {
  if (other == this) return;
  InternalSwap(other);
}
void TestProto2Required::InternalSwap(TestProto2Required* other) {
  std::swap(proto2_, other->proto2_);
  _internal_metadata_.Swap(&other->_internal_metadata_);
  std::swap(_cached_size_, other->_cached_size_);
}

::google::protobuf::Metadata TestProto2Required::GetMetadata() const {
  protobuf_AssignDescriptorsOnce();
  ::google::protobuf::Metadata metadata;
  metadata.descriptor = TestProto2Required_descriptor_;
  metadata.reflection = TestProto2Required_reflection_;
  return metadata;
}

#if PROTOBUF_INLINE_NOT_IN_HEADERS
// TestProto2Required

// optional .protobuf_unittest.TestRequired proto2 = 1;
bool TestProto2Required::has_proto2() const {
  return this != internal_default_instance() && proto2_ != NULL;
}
void TestProto2Required::clear_proto2() {
  if (GetArenaNoVirtual() == NULL && proto2_ != NULL) delete proto2_;
  proto2_ = NULL;
}
const ::protobuf_unittest::TestRequired& TestProto2Required::proto2() const {
  // @@protoc_insertion_point(field_get:proto2_nofieldpresence_unittest.TestProto2Required.proto2)
  return proto2_ != NULL ? *proto2_
                         : *::protobuf_unittest::TestRequired::internal_default_instance();
}
::protobuf_unittest::TestRequired* TestProto2Required::mutable_proto2() {
  
  if (proto2_ == NULL) {
    proto2_ = new ::protobuf_unittest::TestRequired;
  }
  // @@protoc_insertion_point(field_mutable:proto2_nofieldpresence_unittest.TestProto2Required.proto2)
  return proto2_;
}
::protobuf_unittest::TestRequired* TestProto2Required::release_proto2() {
  // @@protoc_insertion_point(field_release:proto2_nofieldpresence_unittest.TestProto2Required.proto2)
  
  ::protobuf_unittest::TestRequired* temp = proto2_;
  proto2_ = NULL;
  return temp;
}
void TestProto2Required::set_allocated_proto2(::protobuf_unittest::TestRequired* proto2) {
  delete proto2_;
  if (proto2 != NULL && proto2->GetArena() != NULL) {
    ::protobuf_unittest::TestRequired* new_proto2 = new ::protobuf_unittest::TestRequired;
    new_proto2->CopyFrom(*proto2);
    proto2 = new_proto2;
  }
  proto2_ = proto2;
  if (proto2) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_set_allocated:proto2_nofieldpresence_unittest.TestProto2Required.proto2)
}

inline const TestProto2Required* TestProto2Required::internal_default_instance() {
  return &TestProto2Required_default_instance_.get();
}
#endif  // PROTOBUF_INLINE_NOT_IN_HEADERS

// ===================================================================

#if !defined(_MSC_VER) || _MSC_VER >= 1900
const int ForeignMessage::kCFieldNumber;
#endif  // !defined(_MSC_VER) || _MSC_VER >= 1900

ForeignMessage::ForeignMessage()
  : ::google::protobuf::Message(), _internal_metadata_(NULL) {
  if (this != internal_default_instance()) protobuf_InitDefaults_google_2fprotobuf_2funittest_5fno_5ffield_5fpresence_2eproto();
  SharedCtor();
  // @@protoc_insertion_point(constructor:proto2_nofieldpresence_unittest.ForeignMessage)
}

void ForeignMessage::InitAsDefaultInstance() {
}

ForeignMessage::ForeignMessage(const ForeignMessage& from)
  : ::google::protobuf::Message(),
    _internal_metadata_(NULL) {
  SharedCtor();
  UnsafeMergeFrom(from);
  // @@protoc_insertion_point(copy_constructor:proto2_nofieldpresence_unittest.ForeignMessage)
}

void ForeignMessage::SharedCtor() {
  c_ = 0;
  _cached_size_ = 0;
}

ForeignMessage::~ForeignMessage() {
  // @@protoc_insertion_point(destructor:proto2_nofieldpresence_unittest.ForeignMessage)
  SharedDtor();
}

void ForeignMessage::SharedDtor() {
}

void ForeignMessage::SetCachedSize(int size) const {
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
}
const ::google::protobuf::Descriptor* ForeignMessage::descriptor() {
  protobuf_AssignDescriptorsOnce();
  return ForeignMessage_descriptor_;
}

const ForeignMessage& ForeignMessage::default_instance() {
  protobuf_InitDefaults_google_2fprotobuf_2funittest_5fno_5ffield_5fpresence_2eproto();
  return *internal_default_instance();
}

::google::protobuf::internal::ExplicitlyConstructed<ForeignMessage> ForeignMessage_default_instance_;

ForeignMessage* ForeignMessage::New(::google::protobuf::Arena* arena) const {
  ForeignMessage* n = new ForeignMessage;
  if (arena != NULL) {
    arena->Own(n);
  }
  return n;
}

void ForeignMessage::Clear() {
// @@protoc_insertion_point(message_clear_start:proto2_nofieldpresence_unittest.ForeignMessage)
  c_ = 0;
}

bool ForeignMessage::MergePartialFromCodedStream(
    ::google::protobuf::io::CodedInputStream* input) {
#define DO_(EXPRESSION) if (!GOOGLE_PREDICT_TRUE(EXPRESSION)) goto failure
  ::google::protobuf::uint32 tag;
  // @@protoc_insertion_point(parse_start:proto2_nofieldpresence_unittest.ForeignMessage)
  for (;;) {
    ::std::pair< ::google::protobuf::uint32, bool> p = input->ReadTagWithCutoff(127);
    tag = p.first;
    if (!p.second) goto handle_unusual;
    switch (::google::protobuf::internal::WireFormatLite::GetTagFieldNumber(tag)) {
      // optional int32 c = 1;
      case 1: {
        if (tag == 8) {

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int32, ::google::protobuf::internal::WireFormatLite::TYPE_INT32>(
                 input, &c_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectAtEnd()) goto success;
        break;
      }

      default: {
      handle_unusual:
        if (tag == 0 ||
            ::google::protobuf::internal::WireFormatLite::GetTagWireType(tag) ==
            ::google::protobuf::internal::WireFormatLite::WIRETYPE_END_GROUP) {
          goto success;
        }
        DO_(::google::protobuf::internal::WireFormatLite::SkipField(input, tag));
        break;
      }
    }
  }
success:
  // @@protoc_insertion_point(parse_success:proto2_nofieldpresence_unittest.ForeignMessage)
  return true;
failure:
  // @@protoc_insertion_point(parse_failure:proto2_nofieldpresence_unittest.ForeignMessage)
  return false;
#undef DO_
}

void ForeignMessage::SerializeWithCachedSizes(
    ::google::protobuf::io::CodedOutputStream* output) const {
  // @@protoc_insertion_point(serialize_start:proto2_nofieldpresence_unittest.ForeignMessage)
  // optional int32 c = 1;
  if (this->c() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt32(1, this->c(), output);
  }

  // @@protoc_insertion_point(serialize_end:proto2_nofieldpresence_unittest.ForeignMessage)
}

::google::protobuf::uint8* ForeignMessage::InternalSerializeWithCachedSizesToArray(
    bool deterministic, ::google::protobuf::uint8* target) const {
  (void)deterministic; // Unused
  // @@protoc_insertion_point(serialize_to_array_start:proto2_nofieldpresence_unittest.ForeignMessage)
  // optional int32 c = 1;
  if (this->c() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt32ToArray(1, this->c(), target);
  }

  // @@protoc_insertion_point(serialize_to_array_end:proto2_nofieldpresence_unittest.ForeignMessage)
  return target;
}

size_t ForeignMessage::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:proto2_nofieldpresence_unittest.ForeignMessage)
  size_t total_size = 0;

  // optional int32 c = 1;
  if (this->c() != 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::Int32Size(
        this->c());
  }

  int cached_size = ::google::protobuf::internal::ToCachedSize(total_size);
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = cached_size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
  return total_size;
}

void ForeignMessage::MergeFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_merge_from_start:proto2_nofieldpresence_unittest.ForeignMessage)
  if (GOOGLE_PREDICT_FALSE(&from == this)) MergeFromFail(__LINE__);
  const ForeignMessage* source =
      ::google::protobuf::internal::DynamicCastToGenerated<const ForeignMessage>(
          &from);
  if (source == NULL) {
  // @@protoc_insertion_point(generalized_merge_from_cast_fail:proto2_nofieldpresence_unittest.ForeignMessage)
    ::google::protobuf::internal::ReflectionOps::Merge(from, this);
  } else {
  // @@protoc_insertion_point(generalized_merge_from_cast_success:proto2_nofieldpresence_unittest.ForeignMessage)
    UnsafeMergeFrom(*source);
  }
}

void ForeignMessage::MergeFrom(const ForeignMessage& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:proto2_nofieldpresence_unittest.ForeignMessage)
  if (GOOGLE_PREDICT_TRUE(&from != this)) {
    UnsafeMergeFrom(from);
  } else {
    MergeFromFail(__LINE__);
  }
}

void ForeignMessage::UnsafeMergeFrom(const ForeignMessage& from) {
  GOOGLE_DCHECK(&from != this);
  if (from.c() != 0) {
    set_c(from.c());
  }
}

void ForeignMessage::CopyFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_copy_from_start:proto2_nofieldpresence_unittest.ForeignMessage)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

void ForeignMessage::CopyFrom(const ForeignMessage& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:proto2_nofieldpresence_unittest.ForeignMessage)
  if (&from == this) return;
  Clear();
  UnsafeMergeFrom(from);
}

bool ForeignMessage::IsInitialized() const {

  return true;
}

void ForeignMessage::Swap(ForeignMessage* other) {
  if (other == this) return;
  InternalSwap(other);
}
void ForeignMessage::InternalSwap(ForeignMessage* other) {
  std::swap(c_, other->c_);
  _internal_metadata_.Swap(&other->_internal_metadata_);
  std::swap(_cached_size_, other->_cached_size_);
}

::google::protobuf::Metadata ForeignMessage::GetMetadata() const {
  protobuf_AssignDescriptorsOnce();
  ::google::protobuf::Metadata metadata;
  metadata.descriptor = ForeignMessage_descriptor_;
  metadata.reflection = ForeignMessage_reflection_;
  return metadata;
}

#if PROTOBUF_INLINE_NOT_IN_HEADERS
// ForeignMessage

// optional int32 c = 1;
void ForeignMessage::clear_c() {
  c_ = 0;
}
::google::protobuf::int32 ForeignMessage::c() const {
  // @@protoc_insertion_point(field_get:proto2_nofieldpresence_unittest.ForeignMessage.c)
  return c_;
}
void ForeignMessage::set_c(::google::protobuf::int32 value) {
  
  c_ = value;
  // @@protoc_insertion_point(field_set:proto2_nofieldpresence_unittest.ForeignMessage.c)
}

inline const ForeignMessage* ForeignMessage::internal_default_instance() {
  return &ForeignMessage_default_instance_.get();
}
#endif  // PROTOBUF_INLINE_NOT_IN_HEADERS

// @@protoc_insertion_point(namespace_scope)

}  // namespace proto2_nofieldpresence_unittest

// @@protoc_insertion_point(global_scope)

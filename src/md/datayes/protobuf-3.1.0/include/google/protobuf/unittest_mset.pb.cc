// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: google/protobuf/unittest_mset.proto

#define INTERNAL_SUPPRESS_PROTOBUF_FIELD_DEPRECATION
#include <google/protobuf/unittest_mset.pb.h>

#include <algorithm>

#include <google/protobuf/stubs/common.h>
#include <google/protobuf/stubs/port.h>
#include <google/protobuf/stubs/once.h>
#include <google/protobuf/io/coded_stream.h>
#include <google/protobuf/wire_format_lite_inl.h>
#include <google/protobuf/descriptor.h>
#include <google/protobuf/generated_message_reflection.h>
#include <google/protobuf/reflection_ops.h>
#include <google/protobuf/wire_format.h>
// @@protoc_insertion_point(includes)

namespace protobuf_unittest {

namespace {

const ::google::protobuf::Descriptor* TestMessageSetContainer_descriptor_ = NULL;
const ::google::protobuf::internal::GeneratedMessageReflection*
  TestMessageSetContainer_reflection_ = NULL;
const ::google::protobuf::Descriptor* TestMessageSetExtension1_descriptor_ = NULL;
const ::google::protobuf::internal::GeneratedMessageReflection*
  TestMessageSetExtension1_reflection_ = NULL;
const ::google::protobuf::Descriptor* TestMessageSetExtension2_descriptor_ = NULL;
const ::google::protobuf::internal::GeneratedMessageReflection*
  TestMessageSetExtension2_reflection_ = NULL;
const ::google::protobuf::Descriptor* RawMessageSet_descriptor_ = NULL;
const ::google::protobuf::internal::GeneratedMessageReflection*
  RawMessageSet_reflection_ = NULL;
const ::google::protobuf::Descriptor* RawMessageSet_Item_descriptor_ = NULL;
const ::google::protobuf::internal::GeneratedMessageReflection*
  RawMessageSet_Item_reflection_ = NULL;

}  // namespace


void protobuf_AssignDesc_google_2fprotobuf_2funittest_5fmset_2eproto() GOOGLE_ATTRIBUTE_COLD;
void protobuf_AssignDesc_google_2fprotobuf_2funittest_5fmset_2eproto() {
  protobuf_AddDesc_google_2fprotobuf_2funittest_5fmset_2eproto();
  const ::google::protobuf::FileDescriptor* file =
    ::google::protobuf::DescriptorPool::generated_pool()->FindFileByName(
      "google/protobuf/unittest_mset.proto");
  GOOGLE_CHECK(file != NULL);
  TestMessageSetContainer_descriptor_ = file->message_type(0);
  static const int TestMessageSetContainer_offsets_[1] = {
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(TestMessageSetContainer, message_set_),
  };
  TestMessageSetContainer_reflection_ =
    ::google::protobuf::internal::GeneratedMessageReflection::NewGeneratedMessageReflection(
      TestMessageSetContainer_descriptor_,
      TestMessageSetContainer::internal_default_instance(),
      TestMessageSetContainer_offsets_,
      GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(TestMessageSetContainer, _has_bits_),
      -1,
      -1,
      sizeof(TestMessageSetContainer),
      GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(TestMessageSetContainer, _internal_metadata_));
  TestMessageSetExtension1_descriptor_ = file->message_type(1);
  static const int TestMessageSetExtension1_offsets_[1] = {
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(TestMessageSetExtension1, i_),
  };
  TestMessageSetExtension1_reflection_ =
    ::google::protobuf::internal::GeneratedMessageReflection::NewGeneratedMessageReflection(
      TestMessageSetExtension1_descriptor_,
      TestMessageSetExtension1::internal_default_instance(),
      TestMessageSetExtension1_offsets_,
      GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(TestMessageSetExtension1, _has_bits_),
      -1,
      -1,
      sizeof(TestMessageSetExtension1),
      GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(TestMessageSetExtension1, _internal_metadata_));
  TestMessageSetExtension2_descriptor_ = file->message_type(2);
  static const int TestMessageSetExtension2_offsets_[1] = {
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(TestMessageSetExtension2, str_),
  };
  TestMessageSetExtension2_reflection_ =
    ::google::protobuf::internal::GeneratedMessageReflection::NewGeneratedMessageReflection(
      TestMessageSetExtension2_descriptor_,
      TestMessageSetExtension2::internal_default_instance(),
      TestMessageSetExtension2_offsets_,
      GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(TestMessageSetExtension2, _has_bits_),
      -1,
      -1,
      sizeof(TestMessageSetExtension2),
      GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(TestMessageSetExtension2, _internal_metadata_));
  RawMessageSet_descriptor_ = file->message_type(3);
  static const int RawMessageSet_offsets_[1] = {
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(RawMessageSet, item_),
  };
  RawMessageSet_reflection_ =
    ::google::protobuf::internal::GeneratedMessageReflection::NewGeneratedMessageReflection(
      RawMessageSet_descriptor_,
      RawMessageSet::internal_default_instance(),
      RawMessageSet_offsets_,
      GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(RawMessageSet, _has_bits_),
      -1,
      -1,
      sizeof(RawMessageSet),
      GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(RawMessageSet, _internal_metadata_));
  RawMessageSet_Item_descriptor_ = RawMessageSet_descriptor_->nested_type(0);
  static const int RawMessageSet_Item_offsets_[2] = {
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(RawMessageSet_Item, type_id_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(RawMessageSet_Item, message_),
  };
  RawMessageSet_Item_reflection_ =
    ::google::protobuf::internal::GeneratedMessageReflection::NewGeneratedMessageReflection(
      RawMessageSet_Item_descriptor_,
      RawMessageSet_Item::internal_default_instance(),
      RawMessageSet_Item_offsets_,
      GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(RawMessageSet_Item, _has_bits_),
      -1,
      -1,
      sizeof(RawMessageSet_Item),
      GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(RawMessageSet_Item, _internal_metadata_));
}

namespace {

GOOGLE_PROTOBUF_DECLARE_ONCE(protobuf_AssignDescriptors_once_);
void protobuf_AssignDescriptorsOnce() {
  ::google::protobuf::GoogleOnceInit(&protobuf_AssignDescriptors_once_,
                 &protobuf_AssignDesc_google_2fprotobuf_2funittest_5fmset_2eproto);
}

void protobuf_RegisterTypes(const ::std::string&) GOOGLE_ATTRIBUTE_COLD;
void protobuf_RegisterTypes(const ::std::string&) {
  protobuf_AssignDescriptorsOnce();
  ::google::protobuf::MessageFactory::InternalRegisterGeneratedMessage(
      TestMessageSetContainer_descriptor_, TestMessageSetContainer::internal_default_instance());
  ::google::protobuf::MessageFactory::InternalRegisterGeneratedMessage(
      TestMessageSetExtension1_descriptor_, TestMessageSetExtension1::internal_default_instance());
  ::google::protobuf::MessageFactory::InternalRegisterGeneratedMessage(
      TestMessageSetExtension2_descriptor_, TestMessageSetExtension2::internal_default_instance());
  ::google::protobuf::MessageFactory::InternalRegisterGeneratedMessage(
      RawMessageSet_descriptor_, RawMessageSet::internal_default_instance());
  ::google::protobuf::MessageFactory::InternalRegisterGeneratedMessage(
      RawMessageSet_Item_descriptor_, RawMessageSet_Item::internal_default_instance());
}

}  // namespace

void protobuf_ShutdownFile_google_2fprotobuf_2funittest_5fmset_2eproto() {
  TestMessageSetContainer_default_instance_.Shutdown();
  delete TestMessageSetContainer_reflection_;
  TestMessageSetExtension1_default_instance_.Shutdown();
  delete TestMessageSetExtension1_reflection_;
  TestMessageSetExtension2_default_instance_.Shutdown();
  delete TestMessageSetExtension2_reflection_;
  RawMessageSet_default_instance_.Shutdown();
  delete RawMessageSet_reflection_;
  RawMessageSet_Item_default_instance_.Shutdown();
  delete RawMessageSet_Item_reflection_;
}

void protobuf_InitDefaults_google_2fprotobuf_2funittest_5fmset_2eproto_impl() {
  GOOGLE_PROTOBUF_VERIFY_VERSION;

  ::proto2_wireformat_unittest::protobuf_InitDefaults_google_2fprotobuf_2funittest_5fmset_5fwire_5fformat_2eproto();
  TestMessageSetContainer_default_instance_.DefaultConstruct();
  TestMessageSetExtension1_default_instance_.DefaultConstruct();
  ::google::protobuf::internal::GetEmptyString();
  TestMessageSetExtension2_default_instance_.DefaultConstruct();
  RawMessageSet_default_instance_.DefaultConstruct();
  ::google::protobuf::internal::GetEmptyString();
  RawMessageSet_Item_default_instance_.DefaultConstruct();
  TestMessageSetContainer_default_instance_.get_mutable()->InitAsDefaultInstance();
  TestMessageSetExtension1_default_instance_.get_mutable()->InitAsDefaultInstance();
  ::google::protobuf::internal::ExtensionSet::RegisterMessageExtension(
    ::proto2_wireformat_unittest::TestMessageSet::internal_default_instance(),
    1545008, 11, false, false,
    ::protobuf_unittest::TestMessageSetExtension1::internal_default_instance());
  TestMessageSetExtension2_default_instance_.get_mutable()->InitAsDefaultInstance();
  ::google::protobuf::internal::ExtensionSet::RegisterMessageExtension(
    ::proto2_wireformat_unittest::TestMessageSet::internal_default_instance(),
    1547769, 11, false, false,
    ::protobuf_unittest::TestMessageSetExtension2::internal_default_instance());
  RawMessageSet_default_instance_.get_mutable()->InitAsDefaultInstance();
  RawMessageSet_Item_default_instance_.get_mutable()->InitAsDefaultInstance();
}

GOOGLE_PROTOBUF_DECLARE_ONCE(protobuf_InitDefaults_google_2fprotobuf_2funittest_5fmset_2eproto_once_);
void protobuf_InitDefaults_google_2fprotobuf_2funittest_5fmset_2eproto() {
  ::google::protobuf::GoogleOnceInit(&protobuf_InitDefaults_google_2fprotobuf_2funittest_5fmset_2eproto_once_,
                 &protobuf_InitDefaults_google_2fprotobuf_2funittest_5fmset_2eproto_impl);
}
void protobuf_AddDesc_google_2fprotobuf_2funittest_5fmset_2eproto_impl() {
  GOOGLE_PROTOBUF_VERIFY_VERSION;

  protobuf_InitDefaults_google_2fprotobuf_2funittest_5fmset_2eproto();
  ::google::protobuf::DescriptorPool::InternalAddGeneratedFile(
    "\n#google/protobuf/unittest_mset.proto\022\021p"
    "rotobuf_unittest\032/google/protobuf/unitte"
    "st_mset_wire_format.proto\"Z\n\027TestMessage"
    "SetContainer\022\?\n\013message_set\030\001 \001(\0132*.prot"
    "o2_wireformat_unittest.TestMessageSet\"\237\001"
    "\n\030TestMessageSetExtension1\022\t\n\001i\030\017 \001(\0052x\n"
    "\025message_set_extension\022*.proto2_wireform"
    "at_unittest.TestMessageSet\030\260\246^ \001(\0132+.pro"
    "tobuf_unittest.TestMessageSetExtension1\""
    "\241\001\n\030TestMessageSetExtension2\022\013\n\003str\030\031 \001("
    "\t2x\n\025message_set_extension\022*.proto2_wire"
    "format_unittest.TestMessageSet\030\371\273^ \001(\0132+"
    ".protobuf_unittest.TestMessageSetExtensi"
    "on2\"n\n\rRawMessageSet\0223\n\004item\030\001 \003(\n2%.pro"
    "tobuf_unittest.RawMessageSet.Item\032(\n\004Ite"
    "m\022\017\n\007type_id\030\002 \002(\005\022\017\n\007message\030\003 \002(\014B\005H\001\370"
    "\001\001", 642);
  ::google::protobuf::MessageFactory::InternalRegisterGeneratedFile(
    "google/protobuf/unittest_mset.proto", &protobuf_RegisterTypes);
  ::proto2_wireformat_unittest::protobuf_AddDesc_google_2fprotobuf_2funittest_5fmset_5fwire_5fformat_2eproto();
  ::google::protobuf::internal::OnShutdown(&protobuf_ShutdownFile_google_2fprotobuf_2funittest_5fmset_2eproto);
}

GOOGLE_PROTOBUF_DECLARE_ONCE(protobuf_AddDesc_google_2fprotobuf_2funittest_5fmset_2eproto_once_);
void protobuf_AddDesc_google_2fprotobuf_2funittest_5fmset_2eproto() {
  ::google::protobuf::GoogleOnceInit(&protobuf_AddDesc_google_2fprotobuf_2funittest_5fmset_2eproto_once_,
                 &protobuf_AddDesc_google_2fprotobuf_2funittest_5fmset_2eproto_impl);
}
// Force AddDescriptors() to be called at static initialization time.
struct StaticDescriptorInitializer_google_2fprotobuf_2funittest_5fmset_2eproto {
  StaticDescriptorInitializer_google_2fprotobuf_2funittest_5fmset_2eproto() {
    protobuf_AddDesc_google_2fprotobuf_2funittest_5fmset_2eproto();
  }
} static_descriptor_initializer_google_2fprotobuf_2funittest_5fmset_2eproto_;

namespace {

static void MergeFromFail(int line) GOOGLE_ATTRIBUTE_COLD GOOGLE_ATTRIBUTE_NORETURN;
static void MergeFromFail(int line) {
  ::google::protobuf::internal::MergeFromFail(__FILE__, line);
}

}  // namespace


// ===================================================================

void TestMessageSetContainer::_slow_mutable_message_set() {
  message_set_ = ::google::protobuf::Arena::CreateMessage< ::proto2_wireformat_unittest::TestMessageSet >(
      GetArenaNoVirtual());
}
::proto2_wireformat_unittest::TestMessageSet* TestMessageSetContainer::_slow_release_message_set() {
  if (message_set_ == NULL) {
    return NULL;
  } else {
    ::proto2_wireformat_unittest::TestMessageSet* temp = new ::proto2_wireformat_unittest::TestMessageSet(*message_set_);
    message_set_ = NULL;
    return temp;
  }
}
::proto2_wireformat_unittest::TestMessageSet* TestMessageSetContainer::unsafe_arena_release_message_set() {
  // @@protoc_insertion_point(field_unsafe_arena_release:protobuf_unittest.TestMessageSetContainer.message_set)
  clear_has_message_set();
  ::proto2_wireformat_unittest::TestMessageSet* temp = message_set_;
  message_set_ = NULL;
  return temp;
}
void TestMessageSetContainer::_slow_set_allocated_message_set(
    ::google::protobuf::Arena* message_arena, ::proto2_wireformat_unittest::TestMessageSet** message_set) {
    if (message_arena != NULL && 
        ::google::protobuf::Arena::GetArena(*message_set) == NULL) {
      message_arena->Own(*message_set);
    } else if (message_arena !=
               ::google::protobuf::Arena::GetArena(*message_set)) {
      ::proto2_wireformat_unittest::TestMessageSet* new_message_set = 
            ::google::protobuf::Arena::CreateMessage< ::proto2_wireformat_unittest::TestMessageSet >(
            message_arena);
      new_message_set->CopyFrom(**message_set);
      *message_set = new_message_set;
    }
}
void TestMessageSetContainer::unsafe_arena_set_allocated_message_set(
    ::proto2_wireformat_unittest::TestMessageSet* message_set) {
  if (GetArenaNoVirtual() == NULL) {
    delete message_set_;
  }
  message_set_ = message_set;
  if (message_set) {
    set_has_message_set();
  } else {
    clear_has_message_set();
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:protobuf_unittest.TestMessageSetContainer.message_set)
}
#if !defined(_MSC_VER) || _MSC_VER >= 1900
const int TestMessageSetContainer::kMessageSetFieldNumber;
#endif  // !defined(_MSC_VER) || _MSC_VER >= 1900

TestMessageSetContainer::TestMessageSetContainer()
  : ::google::protobuf::Message(), _internal_metadata_(NULL) {
  if (this != internal_default_instance()) protobuf_InitDefaults_google_2fprotobuf_2funittest_5fmset_2eproto();
  SharedCtor();
  // @@protoc_insertion_point(constructor:protobuf_unittest.TestMessageSetContainer)
}
TestMessageSetContainer::TestMessageSetContainer(::google::protobuf::Arena* arena)
  : ::google::protobuf::Message(),
  _internal_metadata_(arena) {
#ifdef GOOGLE_PROTOBUF_NO_STATIC_INITIALIZER
  protobuf_InitDefaults_google_2fprotobuf_2funittest_5fmset_2eproto();
#endif  // GOOGLE_PROTOBUF_NO_STATIC_INITIALIZER
  SharedCtor();
  RegisterArenaDtor(arena);
  // @@protoc_insertion_point(arena_constructor:protobuf_unittest.TestMessageSetContainer)
}

void TestMessageSetContainer::InitAsDefaultInstance() {
  message_set_ = const_cast< ::proto2_wireformat_unittest::TestMessageSet*>(
      ::proto2_wireformat_unittest::TestMessageSet::internal_default_instance());
}

TestMessageSetContainer::TestMessageSetContainer(const TestMessageSetContainer& from)
  : ::google::protobuf::Message(),
    _internal_metadata_(NULL) {
  SharedCtor();
  UnsafeMergeFrom(from);
  // @@protoc_insertion_point(copy_constructor:protobuf_unittest.TestMessageSetContainer)
}

void TestMessageSetContainer::SharedCtor() {
  _cached_size_ = 0;
  message_set_ = NULL;
}

TestMessageSetContainer::~TestMessageSetContainer() {
  // @@protoc_insertion_point(destructor:protobuf_unittest.TestMessageSetContainer)
  SharedDtor();
}

void TestMessageSetContainer::SharedDtor() {
  ::google::protobuf::Arena* arena = GetArenaNoVirtual();
  if (arena != NULL) {
    return;
  }

  if (this != &TestMessageSetContainer_default_instance_.get()) {
    delete message_set_;
  }
}

void TestMessageSetContainer::ArenaDtor(void* object) {
  TestMessageSetContainer* _this = reinterpret_cast< TestMessageSetContainer* >(object);
  (void)_this;
}
void TestMessageSetContainer::RegisterArenaDtor(::google::protobuf::Arena* arena) {
}
void TestMessageSetContainer::SetCachedSize(int size) const {
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
}
const ::google::protobuf::Descriptor* TestMessageSetContainer::descriptor() {
  protobuf_AssignDescriptorsOnce();
  return TestMessageSetContainer_descriptor_;
}

const TestMessageSetContainer& TestMessageSetContainer::default_instance() {
  protobuf_InitDefaults_google_2fprotobuf_2funittest_5fmset_2eproto();
  return *internal_default_instance();
}

::google::protobuf::internal::ExplicitlyConstructed<TestMessageSetContainer> TestMessageSetContainer_default_instance_;

TestMessageSetContainer* TestMessageSetContainer::New(::google::protobuf::Arena* arena) const {
  return ::google::protobuf::Arena::CreateMessage<TestMessageSetContainer>(arena);
}

void TestMessageSetContainer::Clear() {
// @@protoc_insertion_point(message_clear_start:protobuf_unittest.TestMessageSetContainer)
  if (has_message_set()) {
    if (message_set_ != NULL) message_set_->::proto2_wireformat_unittest::TestMessageSet::Clear();
  }
  _has_bits_.Clear();
  if (_internal_metadata_.have_unknown_fields()) {
    mutable_unknown_fields()->Clear();
  }
}

bool TestMessageSetContainer::MergePartialFromCodedStream(
    ::google::protobuf::io::CodedInputStream* input) {
#define DO_(EXPRESSION) if (!GOOGLE_PREDICT_TRUE(EXPRESSION)) goto failure
  ::google::protobuf::uint32 tag;
  // @@protoc_insertion_point(parse_start:protobuf_unittest.TestMessageSetContainer)
  for (;;) {
    ::std::pair< ::google::protobuf::uint32, bool> p = input->ReadTagWithCutoff(127);
    tag = p.first;
    if (!p.second) goto handle_unusual;
    switch (::google::protobuf::internal::WireFormatLite::GetTagFieldNumber(tag)) {
      // optional .proto2_wireformat_unittest.TestMessageSet message_set = 1;
      case 1: {
        if (tag == 10) {
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessageNoVirtual(
               input, mutable_message_set()));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectAtEnd()) goto success;
        break;
      }

      default: {
      handle_unusual:
        if (tag == 0 ||
            ::google::protobuf::internal::WireFormatLite::GetTagWireType(tag) ==
            ::google::protobuf::internal::WireFormatLite::WIRETYPE_END_GROUP) {
          goto success;
        }
        DO_(::google::protobuf::internal::WireFormat::SkipField(
              input, tag, mutable_unknown_fields()));
        break;
      }
    }
  }
success:
  // @@protoc_insertion_point(parse_success:protobuf_unittest.TestMessageSetContainer)
  return true;
failure:
  // @@protoc_insertion_point(parse_failure:protobuf_unittest.TestMessageSetContainer)
  return false;
#undef DO_
}

void TestMessageSetContainer::SerializeWithCachedSizes(
    ::google::protobuf::io::CodedOutputStream* output) const {
  // @@protoc_insertion_point(serialize_start:protobuf_unittest.TestMessageSetContainer)
  // optional .proto2_wireformat_unittest.TestMessageSet message_set = 1;
  if (has_message_set()) {
    ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
      1, *this->message_set_, output);
  }

  if (_internal_metadata_.have_unknown_fields()) {
    ::google::protobuf::internal::WireFormat::SerializeUnknownFields(
        unknown_fields(), output);
  }
  // @@protoc_insertion_point(serialize_end:protobuf_unittest.TestMessageSetContainer)
}

::google::protobuf::uint8* TestMessageSetContainer::InternalSerializeWithCachedSizesToArray(
    bool deterministic, ::google::protobuf::uint8* target) const {
  (void)deterministic; // Unused
  // @@protoc_insertion_point(serialize_to_array_start:protobuf_unittest.TestMessageSetContainer)
  // optional .proto2_wireformat_unittest.TestMessageSet message_set = 1;
  if (has_message_set()) {
    target = ::google::protobuf::internal::WireFormatLite::
      InternalWriteMessageNoVirtualToArray(
        1, *this->message_set_, false, target);
  }

  if (_internal_metadata_.have_unknown_fields()) {
    target = ::google::protobuf::internal::WireFormat::SerializeUnknownFieldsToArray(
        unknown_fields(), target);
  }
  // @@protoc_insertion_point(serialize_to_array_end:protobuf_unittest.TestMessageSetContainer)
  return target;
}

size_t TestMessageSetContainer::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:protobuf_unittest.TestMessageSetContainer)
  size_t total_size = 0;

  // optional .proto2_wireformat_unittest.TestMessageSet message_set = 1;
  if (has_message_set()) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::MessageSizeNoVirtual(
        *this->message_set_);
  }

  if (_internal_metadata_.have_unknown_fields()) {
    total_size +=
      ::google::protobuf::internal::WireFormat::ComputeUnknownFieldsSize(
        unknown_fields());
  }
  int cached_size = ::google::protobuf::internal::ToCachedSize(total_size);
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = cached_size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
  return total_size;
}

void TestMessageSetContainer::MergeFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_merge_from_start:protobuf_unittest.TestMessageSetContainer)
  if (GOOGLE_PREDICT_FALSE(&from == this)) MergeFromFail(__LINE__);
  const TestMessageSetContainer* source =
      ::google::protobuf::internal::DynamicCastToGenerated<const TestMessageSetContainer>(
          &from);
  if (source == NULL) {
  // @@protoc_insertion_point(generalized_merge_from_cast_fail:protobuf_unittest.TestMessageSetContainer)
    ::google::protobuf::internal::ReflectionOps::Merge(from, this);
  } else {
  // @@protoc_insertion_point(generalized_merge_from_cast_success:protobuf_unittest.TestMessageSetContainer)
    UnsafeMergeFrom(*source);
  }
}

void TestMessageSetContainer::MergeFrom(const TestMessageSetContainer& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:protobuf_unittest.TestMessageSetContainer)
  if (GOOGLE_PREDICT_TRUE(&from != this)) {
    UnsafeMergeFrom(from);
  } else {
    MergeFromFail(__LINE__);
  }
}

void TestMessageSetContainer::UnsafeMergeFrom(const TestMessageSetContainer& from) {
  GOOGLE_DCHECK(&from != this);
  if (from._has_bits_[0 / 32] & (0xffu << (0 % 32))) {
    if (from.has_message_set()) {
      mutable_message_set()->::proto2_wireformat_unittest::TestMessageSet::MergeFrom(from.message_set());
    }
  }
  if (from._internal_metadata_.have_unknown_fields()) {
    ::google::protobuf::UnknownFieldSet::MergeToInternalMetdata(
      from.unknown_fields(), &_internal_metadata_);
  }
}

void TestMessageSetContainer::CopyFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_copy_from_start:protobuf_unittest.TestMessageSetContainer)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

void TestMessageSetContainer::CopyFrom(const TestMessageSetContainer& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:protobuf_unittest.TestMessageSetContainer)
  if (&from == this) return;
  Clear();
  UnsafeMergeFrom(from);
}

bool TestMessageSetContainer::IsInitialized() const {

  if (has_message_set()) {
    if (!this->message_set_->IsInitialized()) return false;
  }
  return true;
}

void TestMessageSetContainer::Swap(TestMessageSetContainer* other) {
  if (other == this) return;
  if (GetArenaNoVirtual() == other->GetArenaNoVirtual()) {
    InternalSwap(other);
  } else {
    TestMessageSetContainer temp;
    temp.UnsafeMergeFrom(*this);
    CopyFrom(*other);
    other->CopyFrom(temp);
  }
}
void TestMessageSetContainer::UnsafeArenaSwap(TestMessageSetContainer* other) {
  if (other == this) return;
  GOOGLE_DCHECK(GetArenaNoVirtual() == other->GetArenaNoVirtual());
  InternalSwap(other);
}
void TestMessageSetContainer::InternalSwap(TestMessageSetContainer* other) {
  std::swap(message_set_, other->message_set_);
  std::swap(_has_bits_[0], other->_has_bits_[0]);
  _internal_metadata_.Swap(&other->_internal_metadata_);
  std::swap(_cached_size_, other->_cached_size_);
}

::google::protobuf::Metadata TestMessageSetContainer::GetMetadata() const {
  protobuf_AssignDescriptorsOnce();
  ::google::protobuf::Metadata metadata;
  metadata.descriptor = TestMessageSetContainer_descriptor_;
  metadata.reflection = TestMessageSetContainer_reflection_;
  return metadata;
}

#if PROTOBUF_INLINE_NOT_IN_HEADERS
// TestMessageSetContainer

// optional .proto2_wireformat_unittest.TestMessageSet message_set = 1;
bool TestMessageSetContainer::has_message_set() const {
  return (_has_bits_[0] & 0x00000001u) != 0;
}
void TestMessageSetContainer::set_has_message_set() {
  _has_bits_[0] |= 0x00000001u;
}
void TestMessageSetContainer::clear_has_message_set() {
  _has_bits_[0] &= ~0x00000001u;
}
void TestMessageSetContainer::clear_message_set() {
  if (message_set_ != NULL) message_set_->::proto2_wireformat_unittest::TestMessageSet::Clear();
  clear_has_message_set();
}
const ::proto2_wireformat_unittest::TestMessageSet& TestMessageSetContainer::message_set() const {
  // @@protoc_insertion_point(field_get:protobuf_unittest.TestMessageSetContainer.message_set)
  return message_set_ != NULL ? *message_set_
                         : *::proto2_wireformat_unittest::TestMessageSet::internal_default_instance();
}
::proto2_wireformat_unittest::TestMessageSet* TestMessageSetContainer::mutable_message_set() {
  set_has_message_set();
  if (message_set_ == NULL) {
    _slow_mutable_message_set();
  }
  // @@protoc_insertion_point(field_mutable:protobuf_unittest.TestMessageSetContainer.message_set)
  return message_set_;
}
::proto2_wireformat_unittest::TestMessageSet* TestMessageSetContainer::release_message_set() {
  // @@protoc_insertion_point(field_release:protobuf_unittest.TestMessageSetContainer.message_set)
  clear_has_message_set();
  if (GetArenaNoVirtual() != NULL) {
    return _slow_release_message_set();
  } else {
    ::proto2_wireformat_unittest::TestMessageSet* temp = message_set_;
    message_set_ = NULL;
    return temp;
  }
}
 void TestMessageSetContainer::set_allocated_message_set(::proto2_wireformat_unittest::TestMessageSet* message_set) {
  ::google::protobuf::Arena* message_arena = GetArenaNoVirtual();
  if (message_arena == NULL) {
    delete message_set_;
  }
  if (message_set != NULL) {
    _slow_set_allocated_message_set(message_arena, &message_set);
  }
  message_set_ = message_set;
  if (message_set) {
    set_has_message_set();
  } else {
    clear_has_message_set();
  }
  // @@protoc_insertion_point(field_set_allocated:protobuf_unittest.TestMessageSetContainer.message_set)
}

inline const TestMessageSetContainer* TestMessageSetContainer::internal_default_instance() {
  return &TestMessageSetContainer_default_instance_.get();
}
#endif  // PROTOBUF_INLINE_NOT_IN_HEADERS

// ===================================================================

#if !defined(_MSC_VER) || _MSC_VER >= 1900
const int TestMessageSetExtension1::kIFieldNumber;
#endif  // !defined(_MSC_VER) || _MSC_VER >= 1900

#if !defined(_MSC_VER) || _MSC_VER >= 1900
const int TestMessageSetExtension1::kMessageSetExtensionFieldNumber;
#endif
::google::protobuf::internal::ExtensionIdentifier< ::proto2_wireformat_unittest::TestMessageSet,
    ::google::protobuf::internal::MessageTypeTraits< ::protobuf_unittest::TestMessageSetExtension1 >, 11, false >
  TestMessageSetExtension1::message_set_extension(kMessageSetExtensionFieldNumber, *::protobuf_unittest::TestMessageSetExtension1::internal_default_instance());
TestMessageSetExtension1::TestMessageSetExtension1()
  : ::google::protobuf::Message(), _internal_metadata_(NULL) {
  if (this != internal_default_instance()) protobuf_InitDefaults_google_2fprotobuf_2funittest_5fmset_2eproto();
  SharedCtor();
  // @@protoc_insertion_point(constructor:protobuf_unittest.TestMessageSetExtension1)
}
TestMessageSetExtension1::TestMessageSetExtension1(::google::protobuf::Arena* arena)
  : ::google::protobuf::Message(),
  _internal_metadata_(arena) {
#ifdef GOOGLE_PROTOBUF_NO_STATIC_INITIALIZER
  protobuf_InitDefaults_google_2fprotobuf_2funittest_5fmset_2eproto();
#endif  // GOOGLE_PROTOBUF_NO_STATIC_INITIALIZER
  SharedCtor();
  RegisterArenaDtor(arena);
  // @@protoc_insertion_point(arena_constructor:protobuf_unittest.TestMessageSetExtension1)
}

void TestMessageSetExtension1::InitAsDefaultInstance() {
}

TestMessageSetExtension1::TestMessageSetExtension1(const TestMessageSetExtension1& from)
  : ::google::protobuf::Message(),
    _internal_metadata_(NULL) {
  SharedCtor();
  UnsafeMergeFrom(from);
  // @@protoc_insertion_point(copy_constructor:protobuf_unittest.TestMessageSetExtension1)
}

void TestMessageSetExtension1::SharedCtor() {
  _cached_size_ = 0;
  i_ = 0;
}

TestMessageSetExtension1::~TestMessageSetExtension1() {
  // @@protoc_insertion_point(destructor:protobuf_unittest.TestMessageSetExtension1)
  SharedDtor();
}

void TestMessageSetExtension1::SharedDtor() {
  ::google::protobuf::Arena* arena = GetArenaNoVirtual();
  if (arena != NULL) {
    return;
  }

}

void TestMessageSetExtension1::ArenaDtor(void* object) {
  TestMessageSetExtension1* _this = reinterpret_cast< TestMessageSetExtension1* >(object);
  (void)_this;
}
void TestMessageSetExtension1::RegisterArenaDtor(::google::protobuf::Arena* arena) {
}
void TestMessageSetExtension1::SetCachedSize(int size) const {
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
}
const ::google::protobuf::Descriptor* TestMessageSetExtension1::descriptor() {
  protobuf_AssignDescriptorsOnce();
  return TestMessageSetExtension1_descriptor_;
}

const TestMessageSetExtension1& TestMessageSetExtension1::default_instance() {
  protobuf_InitDefaults_google_2fprotobuf_2funittest_5fmset_2eproto();
  return *internal_default_instance();
}

::google::protobuf::internal::ExplicitlyConstructed<TestMessageSetExtension1> TestMessageSetExtension1_default_instance_;

TestMessageSetExtension1* TestMessageSetExtension1::New(::google::protobuf::Arena* arena) const {
  return ::google::protobuf::Arena::CreateMessage<TestMessageSetExtension1>(arena);
}

void TestMessageSetExtension1::Clear() {
// @@protoc_insertion_point(message_clear_start:protobuf_unittest.TestMessageSetExtension1)
  i_ = 0;
  _has_bits_.Clear();
  if (_internal_metadata_.have_unknown_fields()) {
    mutable_unknown_fields()->Clear();
  }
}

bool TestMessageSetExtension1::MergePartialFromCodedStream(
    ::google::protobuf::io::CodedInputStream* input) {
#define DO_(EXPRESSION) if (!GOOGLE_PREDICT_TRUE(EXPRESSION)) goto failure
  ::google::protobuf::uint32 tag;
  // @@protoc_insertion_point(parse_start:protobuf_unittest.TestMessageSetExtension1)
  for (;;) {
    ::std::pair< ::google::protobuf::uint32, bool> p = input->ReadTagWithCutoff(127);
    tag = p.first;
    if (!p.second) goto handle_unusual;
    switch (::google::protobuf::internal::WireFormatLite::GetTagFieldNumber(tag)) {
      // optional int32 i = 15;
      case 15: {
        if (tag == 120) {
          set_has_i();
          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int32, ::google::protobuf::internal::WireFormatLite::TYPE_INT32>(
                 input, &i_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectAtEnd()) goto success;
        break;
      }

      default: {
      handle_unusual:
        if (tag == 0 ||
            ::google::protobuf::internal::WireFormatLite::GetTagWireType(tag) ==
            ::google::protobuf::internal::WireFormatLite::WIRETYPE_END_GROUP) {
          goto success;
        }
        DO_(::google::protobuf::internal::WireFormat::SkipField(
              input, tag, mutable_unknown_fields()));
        break;
      }
    }
  }
success:
  // @@protoc_insertion_point(parse_success:protobuf_unittest.TestMessageSetExtension1)
  return true;
failure:
  // @@protoc_insertion_point(parse_failure:protobuf_unittest.TestMessageSetExtension1)
  return false;
#undef DO_
}

void TestMessageSetExtension1::SerializeWithCachedSizes(
    ::google::protobuf::io::CodedOutputStream* output) const {
  // @@protoc_insertion_point(serialize_start:protobuf_unittest.TestMessageSetExtension1)
  // optional int32 i = 15;
  if (has_i()) {
    ::google::protobuf::internal::WireFormatLite::WriteInt32(15, this->i(), output);
  }

  if (_internal_metadata_.have_unknown_fields()) {
    ::google::protobuf::internal::WireFormat::SerializeUnknownFields(
        unknown_fields(), output);
  }
  // @@protoc_insertion_point(serialize_end:protobuf_unittest.TestMessageSetExtension1)
}

::google::protobuf::uint8* TestMessageSetExtension1::InternalSerializeWithCachedSizesToArray(
    bool deterministic, ::google::protobuf::uint8* target) const {
  (void)deterministic; // Unused
  // @@protoc_insertion_point(serialize_to_array_start:protobuf_unittest.TestMessageSetExtension1)
  // optional int32 i = 15;
  if (has_i()) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt32ToArray(15, this->i(), target);
  }

  if (_internal_metadata_.have_unknown_fields()) {
    target = ::google::protobuf::internal::WireFormat::SerializeUnknownFieldsToArray(
        unknown_fields(), target);
  }
  // @@protoc_insertion_point(serialize_to_array_end:protobuf_unittest.TestMessageSetExtension1)
  return target;
}

size_t TestMessageSetExtension1::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:protobuf_unittest.TestMessageSetExtension1)
  size_t total_size = 0;

  // optional int32 i = 15;
  if (has_i()) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::Int32Size(
        this->i());
  }

  if (_internal_metadata_.have_unknown_fields()) {
    total_size +=
      ::google::protobuf::internal::WireFormat::ComputeUnknownFieldsSize(
        unknown_fields());
  }
  int cached_size = ::google::protobuf::internal::ToCachedSize(total_size);
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = cached_size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
  return total_size;
}

void TestMessageSetExtension1::MergeFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_merge_from_start:protobuf_unittest.TestMessageSetExtension1)
  if (GOOGLE_PREDICT_FALSE(&from == this)) MergeFromFail(__LINE__);
  const TestMessageSetExtension1* source =
      ::google::protobuf::internal::DynamicCastToGenerated<const TestMessageSetExtension1>(
          &from);
  if (source == NULL) {
  // @@protoc_insertion_point(generalized_merge_from_cast_fail:protobuf_unittest.TestMessageSetExtension1)
    ::google::protobuf::internal::ReflectionOps::Merge(from, this);
  } else {
  // @@protoc_insertion_point(generalized_merge_from_cast_success:protobuf_unittest.TestMessageSetExtension1)
    UnsafeMergeFrom(*source);
  }
}

void TestMessageSetExtension1::MergeFrom(const TestMessageSetExtension1& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:protobuf_unittest.TestMessageSetExtension1)
  if (GOOGLE_PREDICT_TRUE(&from != this)) {
    UnsafeMergeFrom(from);
  } else {
    MergeFromFail(__LINE__);
  }
}

void TestMessageSetExtension1::UnsafeMergeFrom(const TestMessageSetExtension1& from) {
  GOOGLE_DCHECK(&from != this);
  if (from._has_bits_[0 / 32] & (0xffu << (0 % 32))) {
    if (from.has_i()) {
      set_i(from.i());
    }
  }
  if (from._internal_metadata_.have_unknown_fields()) {
    ::google::protobuf::UnknownFieldSet::MergeToInternalMetdata(
      from.unknown_fields(), &_internal_metadata_);
  }
}

void TestMessageSetExtension1::CopyFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_copy_from_start:protobuf_unittest.TestMessageSetExtension1)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

void TestMessageSetExtension1::CopyFrom(const TestMessageSetExtension1& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:protobuf_unittest.TestMessageSetExtension1)
  if (&from == this) return;
  Clear();
  UnsafeMergeFrom(from);
}

bool TestMessageSetExtension1::IsInitialized() const {

  return true;
}

void TestMessageSetExtension1::Swap(TestMessageSetExtension1* other) {
  if (other == this) return;
  if (GetArenaNoVirtual() == other->GetArenaNoVirtual()) {
    InternalSwap(other);
  } else {
    TestMessageSetExtension1 temp;
    temp.UnsafeMergeFrom(*this);
    CopyFrom(*other);
    other->CopyFrom(temp);
  }
}
void TestMessageSetExtension1::UnsafeArenaSwap(TestMessageSetExtension1* other) {
  if (other == this) return;
  GOOGLE_DCHECK(GetArenaNoVirtual() == other->GetArenaNoVirtual());
  InternalSwap(other);
}
void TestMessageSetExtension1::InternalSwap(TestMessageSetExtension1* other) {
  std::swap(i_, other->i_);
  std::swap(_has_bits_[0], other->_has_bits_[0]);
  _internal_metadata_.Swap(&other->_internal_metadata_);
  std::swap(_cached_size_, other->_cached_size_);
}

::google::protobuf::Metadata TestMessageSetExtension1::GetMetadata() const {
  protobuf_AssignDescriptorsOnce();
  ::google::protobuf::Metadata metadata;
  metadata.descriptor = TestMessageSetExtension1_descriptor_;
  metadata.reflection = TestMessageSetExtension1_reflection_;
  return metadata;
}

#if PROTOBUF_INLINE_NOT_IN_HEADERS
// TestMessageSetExtension1

// optional int32 i = 15;
bool TestMessageSetExtension1::has_i() const {
  return (_has_bits_[0] & 0x00000001u) != 0;
}
void TestMessageSetExtension1::set_has_i() {
  _has_bits_[0] |= 0x00000001u;
}
void TestMessageSetExtension1::clear_has_i() {
  _has_bits_[0] &= ~0x00000001u;
}
void TestMessageSetExtension1::clear_i() {
  i_ = 0;
  clear_has_i();
}
::google::protobuf::int32 TestMessageSetExtension1::i() const {
  // @@protoc_insertion_point(field_get:protobuf_unittest.TestMessageSetExtension1.i)
  return i_;
}
void TestMessageSetExtension1::set_i(::google::protobuf::int32 value) {
  set_has_i();
  i_ = value;
  // @@protoc_insertion_point(field_set:protobuf_unittest.TestMessageSetExtension1.i)
}

inline const TestMessageSetExtension1* TestMessageSetExtension1::internal_default_instance() {
  return &TestMessageSetExtension1_default_instance_.get();
}
#endif  // PROTOBUF_INLINE_NOT_IN_HEADERS

// ===================================================================

#if !defined(_MSC_VER) || _MSC_VER >= 1900
const int TestMessageSetExtension2::kStrFieldNumber;
#endif  // !defined(_MSC_VER) || _MSC_VER >= 1900

#if !defined(_MSC_VER) || _MSC_VER >= 1900
const int TestMessageSetExtension2::kMessageSetExtensionFieldNumber;
#endif
::google::protobuf::internal::ExtensionIdentifier< ::proto2_wireformat_unittest::TestMessageSet,
    ::google::protobuf::internal::MessageTypeTraits< ::protobuf_unittest::TestMessageSetExtension2 >, 11, false >
  TestMessageSetExtension2::message_set_extension(kMessageSetExtensionFieldNumber, *::protobuf_unittest::TestMessageSetExtension2::internal_default_instance());
TestMessageSetExtension2::TestMessageSetExtension2()
  : ::google::protobuf::Message(), _internal_metadata_(NULL) {
  if (this != internal_default_instance()) protobuf_InitDefaults_google_2fprotobuf_2funittest_5fmset_2eproto();
  SharedCtor();
  // @@protoc_insertion_point(constructor:protobuf_unittest.TestMessageSetExtension2)
}
TestMessageSetExtension2::TestMessageSetExtension2(::google::protobuf::Arena* arena)
  : ::google::protobuf::Message(),
  _internal_metadata_(arena) {
#ifdef GOOGLE_PROTOBUF_NO_STATIC_INITIALIZER
  protobuf_InitDefaults_google_2fprotobuf_2funittest_5fmset_2eproto();
#endif  // GOOGLE_PROTOBUF_NO_STATIC_INITIALIZER
  SharedCtor();
  RegisterArenaDtor(arena);
  // @@protoc_insertion_point(arena_constructor:protobuf_unittest.TestMessageSetExtension2)
}

void TestMessageSetExtension2::InitAsDefaultInstance() {
}

TestMessageSetExtension2::TestMessageSetExtension2(const TestMessageSetExtension2& from)
  : ::google::protobuf::Message(),
    _internal_metadata_(NULL) {
  SharedCtor();
  UnsafeMergeFrom(from);
  // @@protoc_insertion_point(copy_constructor:protobuf_unittest.TestMessageSetExtension2)
}

void TestMessageSetExtension2::SharedCtor() {
  _cached_size_ = 0;
  str_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}

TestMessageSetExtension2::~TestMessageSetExtension2() {
  // @@protoc_insertion_point(destructor:protobuf_unittest.TestMessageSetExtension2)
  SharedDtor();
}

void TestMessageSetExtension2::SharedDtor() {
  ::google::protobuf::Arena* arena = GetArenaNoVirtual();
  if (arena != NULL) {
    return;
  }

  str_.Destroy(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), arena);
}

void TestMessageSetExtension2::ArenaDtor(void* object) {
  TestMessageSetExtension2* _this = reinterpret_cast< TestMessageSetExtension2* >(object);
  (void)_this;
}
void TestMessageSetExtension2::RegisterArenaDtor(::google::protobuf::Arena* arena) {
}
void TestMessageSetExtension2::SetCachedSize(int size) const {
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
}
const ::google::protobuf::Descriptor* TestMessageSetExtension2::descriptor() {
  protobuf_AssignDescriptorsOnce();
  return TestMessageSetExtension2_descriptor_;
}

const TestMessageSetExtension2& TestMessageSetExtension2::default_instance() {
  protobuf_InitDefaults_google_2fprotobuf_2funittest_5fmset_2eproto();
  return *internal_default_instance();
}

::google::protobuf::internal::ExplicitlyConstructed<TestMessageSetExtension2> TestMessageSetExtension2_default_instance_;

TestMessageSetExtension2* TestMessageSetExtension2::New(::google::protobuf::Arena* arena) const {
  return ::google::protobuf::Arena::CreateMessage<TestMessageSetExtension2>(arena);
}

void TestMessageSetExtension2::Clear() {
// @@protoc_insertion_point(message_clear_start:protobuf_unittest.TestMessageSetExtension2)
  if (has_str()) {
    str_.ClearToEmpty(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
  }
  _has_bits_.Clear();
  if (_internal_metadata_.have_unknown_fields()) {
    mutable_unknown_fields()->Clear();
  }
}

bool TestMessageSetExtension2::MergePartialFromCodedStream(
    ::google::protobuf::io::CodedInputStream* input) {
#define DO_(EXPRESSION) if (!GOOGLE_PREDICT_TRUE(EXPRESSION)) goto failure
  ::google::protobuf::uint32 tag;
  // @@protoc_insertion_point(parse_start:protobuf_unittest.TestMessageSetExtension2)
  for (;;) {
    ::std::pair< ::google::protobuf::uint32, bool> p = input->ReadTagWithCutoff(16383);
    tag = p.first;
    if (!p.second) goto handle_unusual;
    switch (::google::protobuf::internal::WireFormatLite::GetTagFieldNumber(tag)) {
      // optional string str = 25;
      case 25: {
        if (tag == 202) {
          DO_(::google::protobuf::internal::WireFormatLite::ReadString(
                input, this->mutable_str()));
          ::google::protobuf::internal::WireFormat::VerifyUTF8StringNamedField(
            this->str().data(), this->str().length(),
            ::google::protobuf::internal::WireFormat::PARSE,
            "protobuf_unittest.TestMessageSetExtension2.str");
        } else {
          goto handle_unusual;
        }
        if (input->ExpectAtEnd()) goto success;
        break;
      }

      default: {
      handle_unusual:
        if (tag == 0 ||
            ::google::protobuf::internal::WireFormatLite::GetTagWireType(tag) ==
            ::google::protobuf::internal::WireFormatLite::WIRETYPE_END_GROUP) {
          goto success;
        }
        DO_(::google::protobuf::internal::WireFormat::SkipField(
              input, tag, mutable_unknown_fields()));
        break;
      }
    }
  }
success:
  // @@protoc_insertion_point(parse_success:protobuf_unittest.TestMessageSetExtension2)
  return true;
failure:
  // @@protoc_insertion_point(parse_failure:protobuf_unittest.TestMessageSetExtension2)
  return false;
#undef DO_
}

void TestMessageSetExtension2::SerializeWithCachedSizes(
    ::google::protobuf::io::CodedOutputStream* output) const {
  // @@protoc_insertion_point(serialize_start:protobuf_unittest.TestMessageSetExtension2)
  // optional string str = 25;
  if (has_str()) {
    ::google::protobuf::internal::WireFormat::VerifyUTF8StringNamedField(
      this->str().data(), this->str().length(),
      ::google::protobuf::internal::WireFormat::SERIALIZE,
      "protobuf_unittest.TestMessageSetExtension2.str");
    ::google::protobuf::internal::WireFormatLite::WriteStringMaybeAliased(
      25, this->str(), output);
  }

  if (_internal_metadata_.have_unknown_fields()) {
    ::google::protobuf::internal::WireFormat::SerializeUnknownFields(
        unknown_fields(), output);
  }
  // @@protoc_insertion_point(serialize_end:protobuf_unittest.TestMessageSetExtension2)
}

::google::protobuf::uint8* TestMessageSetExtension2::InternalSerializeWithCachedSizesToArray(
    bool deterministic, ::google::protobuf::uint8* target) const {
  (void)deterministic; // Unused
  // @@protoc_insertion_point(serialize_to_array_start:protobuf_unittest.TestMessageSetExtension2)
  // optional string str = 25;
  if (has_str()) {
    ::google::protobuf::internal::WireFormat::VerifyUTF8StringNamedField(
      this->str().data(), this->str().length(),
      ::google::protobuf::internal::WireFormat::SERIALIZE,
      "protobuf_unittest.TestMessageSetExtension2.str");
    target =
      ::google::protobuf::internal::WireFormatLite::WriteStringToArray(
        25, this->str(), target);
  }

  if (_internal_metadata_.have_unknown_fields()) {
    target = ::google::protobuf::internal::WireFormat::SerializeUnknownFieldsToArray(
        unknown_fields(), target);
  }
  // @@protoc_insertion_point(serialize_to_array_end:protobuf_unittest.TestMessageSetExtension2)
  return target;
}

size_t TestMessageSetExtension2::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:protobuf_unittest.TestMessageSetExtension2)
  size_t total_size = 0;

  // optional string str = 25;
  if (has_str()) {
    total_size += 2 +
      ::google::protobuf::internal::WireFormatLite::StringSize(
        this->str());
  }

  if (_internal_metadata_.have_unknown_fields()) {
    total_size +=
      ::google::protobuf::internal::WireFormat::ComputeUnknownFieldsSize(
        unknown_fields());
  }
  int cached_size = ::google::protobuf::internal::ToCachedSize(total_size);
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = cached_size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
  return total_size;
}

void TestMessageSetExtension2::MergeFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_merge_from_start:protobuf_unittest.TestMessageSetExtension2)
  if (GOOGLE_PREDICT_FALSE(&from == this)) MergeFromFail(__LINE__);
  const TestMessageSetExtension2* source =
      ::google::protobuf::internal::DynamicCastToGenerated<const TestMessageSetExtension2>(
          &from);
  if (source == NULL) {
  // @@protoc_insertion_point(generalized_merge_from_cast_fail:protobuf_unittest.TestMessageSetExtension2)
    ::google::protobuf::internal::ReflectionOps::Merge(from, this);
  } else {
  // @@protoc_insertion_point(generalized_merge_from_cast_success:protobuf_unittest.TestMessageSetExtension2)
    UnsafeMergeFrom(*source);
  }
}

void TestMessageSetExtension2::MergeFrom(const TestMessageSetExtension2& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:protobuf_unittest.TestMessageSetExtension2)
  if (GOOGLE_PREDICT_TRUE(&from != this)) {
    UnsafeMergeFrom(from);
  } else {
    MergeFromFail(__LINE__);
  }
}

void TestMessageSetExtension2::UnsafeMergeFrom(const TestMessageSetExtension2& from) {
  GOOGLE_DCHECK(&from != this);
  if (from._has_bits_[0 / 32] & (0xffu << (0 % 32))) {
    if (from.has_str()) {
      set_str(from.str());
    }
  }
  if (from._internal_metadata_.have_unknown_fields()) {
    ::google::protobuf::UnknownFieldSet::MergeToInternalMetdata(
      from.unknown_fields(), &_internal_metadata_);
  }
}

void TestMessageSetExtension2::CopyFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_copy_from_start:protobuf_unittest.TestMessageSetExtension2)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

void TestMessageSetExtension2::CopyFrom(const TestMessageSetExtension2& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:protobuf_unittest.TestMessageSetExtension2)
  if (&from == this) return;
  Clear();
  UnsafeMergeFrom(from);
}

bool TestMessageSetExtension2::IsInitialized() const {

  return true;
}

void TestMessageSetExtension2::Swap(TestMessageSetExtension2* other) {
  if (other == this) return;
  if (GetArenaNoVirtual() == other->GetArenaNoVirtual()) {
    InternalSwap(other);
  } else {
    TestMessageSetExtension2 temp;
    temp.UnsafeMergeFrom(*this);
    CopyFrom(*other);
    other->CopyFrom(temp);
  }
}
void TestMessageSetExtension2::UnsafeArenaSwap(TestMessageSetExtension2* other) {
  if (other == this) return;
  GOOGLE_DCHECK(GetArenaNoVirtual() == other->GetArenaNoVirtual());
  InternalSwap(other);
}
void TestMessageSetExtension2::InternalSwap(TestMessageSetExtension2* other) {
  str_.Swap(&other->str_);
  std::swap(_has_bits_[0], other->_has_bits_[0]);
  _internal_metadata_.Swap(&other->_internal_metadata_);
  std::swap(_cached_size_, other->_cached_size_);
}

::google::protobuf::Metadata TestMessageSetExtension2::GetMetadata() const {
  protobuf_AssignDescriptorsOnce();
  ::google::protobuf::Metadata metadata;
  metadata.descriptor = TestMessageSetExtension2_descriptor_;
  metadata.reflection = TestMessageSetExtension2_reflection_;
  return metadata;
}

#if PROTOBUF_INLINE_NOT_IN_HEADERS
// TestMessageSetExtension2

// optional string str = 25;
bool TestMessageSetExtension2::has_str() const {
  return (_has_bits_[0] & 0x00000001u) != 0;
}
void TestMessageSetExtension2::set_has_str() {
  _has_bits_[0] |= 0x00000001u;
}
void TestMessageSetExtension2::clear_has_str() {
  _has_bits_[0] &= ~0x00000001u;
}
void TestMessageSetExtension2::clear_str() {
  str_.ClearToEmpty(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
  clear_has_str();
}
const ::std::string& TestMessageSetExtension2::str() const {
  // @@protoc_insertion_point(field_get:protobuf_unittest.TestMessageSetExtension2.str)
  return str_.Get(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
void TestMessageSetExtension2::set_str(const ::std::string& value) {
  set_has_str();
  str_.Set(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value, GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set:protobuf_unittest.TestMessageSetExtension2.str)
}
void TestMessageSetExtension2::set_str(const char* value) {
  set_has_str();
  str_.Set(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value),
              GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_char:protobuf_unittest.TestMessageSetExtension2.str)
}
void TestMessageSetExtension2::set_str(const char* value,
    size_t size) {
  set_has_str();
  str_.Set(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(
      reinterpret_cast<const char*>(value), size), GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_pointer:protobuf_unittest.TestMessageSetExtension2.str)
}
::std::string* TestMessageSetExtension2::mutable_str() {
  set_has_str();
  // @@protoc_insertion_point(field_mutable:protobuf_unittest.TestMessageSetExtension2.str)
  return str_.Mutable(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
::std::string* TestMessageSetExtension2::release_str() {
  // @@protoc_insertion_point(field_release:protobuf_unittest.TestMessageSetExtension2.str)
  clear_has_str();
  return str_.Release(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
::std::string* TestMessageSetExtension2::unsafe_arena_release_str() {
  // @@protoc_insertion_point(field_unsafe_arena_release:protobuf_unittest.TestMessageSetExtension2.str)
  GOOGLE_DCHECK(GetArenaNoVirtual() != NULL);
  clear_has_str();
  return str_.UnsafeArenaRelease(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      GetArenaNoVirtual());
}
void TestMessageSetExtension2::set_allocated_str(::std::string* str) {
  if (str != NULL) {
    set_has_str();
  } else {
    clear_has_str();
  }
  str_.SetAllocated(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), str,
      GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_allocated:protobuf_unittest.TestMessageSetExtension2.str)
}
void TestMessageSetExtension2::unsafe_arena_set_allocated_str(
    ::std::string* str) {
  GOOGLE_DCHECK(GetArenaNoVirtual() != NULL);
  if (str != NULL) {
    set_has_str();
  } else {
    clear_has_str();
  }
  str_.UnsafeArenaSetAllocated(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      str, GetArenaNoVirtual());
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:protobuf_unittest.TestMessageSetExtension2.str)
}

inline const TestMessageSetExtension2* TestMessageSetExtension2::internal_default_instance() {
  return &TestMessageSetExtension2_default_instance_.get();
}
#endif  // PROTOBUF_INLINE_NOT_IN_HEADERS

// ===================================================================

#if !defined(_MSC_VER) || _MSC_VER >= 1900
const int RawMessageSet_Item::kTypeIdFieldNumber;
const int RawMessageSet_Item::kMessageFieldNumber;
#endif  // !defined(_MSC_VER) || _MSC_VER >= 1900

RawMessageSet_Item::RawMessageSet_Item()
  : ::google::protobuf::Message(), _internal_metadata_(NULL) {
  if (this != internal_default_instance()) protobuf_InitDefaults_google_2fprotobuf_2funittest_5fmset_2eproto();
  SharedCtor();
  // @@protoc_insertion_point(constructor:protobuf_unittest.RawMessageSet.Item)
}
RawMessageSet_Item::RawMessageSet_Item(::google::protobuf::Arena* arena)
  : ::google::protobuf::Message(),
  _internal_metadata_(arena) {
#ifdef GOOGLE_PROTOBUF_NO_STATIC_INITIALIZER
  protobuf_InitDefaults_google_2fprotobuf_2funittest_5fmset_2eproto();
#endif  // GOOGLE_PROTOBUF_NO_STATIC_INITIALIZER
  SharedCtor();
  RegisterArenaDtor(arena);
  // @@protoc_insertion_point(arena_constructor:protobuf_unittest.RawMessageSet.Item)
}

void RawMessageSet_Item::InitAsDefaultInstance() {
}

RawMessageSet_Item::RawMessageSet_Item(const RawMessageSet_Item& from)
  : ::google::protobuf::Message(),
    _internal_metadata_(NULL) {
  SharedCtor();
  UnsafeMergeFrom(from);
  // @@protoc_insertion_point(copy_constructor:protobuf_unittest.RawMessageSet.Item)
}

void RawMessageSet_Item::SharedCtor() {
  _cached_size_ = 0;
  message_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  type_id_ = 0;
}

RawMessageSet_Item::~RawMessageSet_Item() {
  // @@protoc_insertion_point(destructor:protobuf_unittest.RawMessageSet.Item)
  SharedDtor();
}

void RawMessageSet_Item::SharedDtor() {
  ::google::protobuf::Arena* arena = GetArenaNoVirtual();
  if (arena != NULL) {
    return;
  }

  message_.Destroy(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), arena);
}

void RawMessageSet_Item::ArenaDtor(void* object) {
  RawMessageSet_Item* _this = reinterpret_cast< RawMessageSet_Item* >(object);
  (void)_this;
}
void RawMessageSet_Item::RegisterArenaDtor(::google::protobuf::Arena* arena) {
}
void RawMessageSet_Item::SetCachedSize(int size) const {
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
}
const ::google::protobuf::Descriptor* RawMessageSet_Item::descriptor() {
  protobuf_AssignDescriptorsOnce();
  return RawMessageSet_Item_descriptor_;
}

const RawMessageSet_Item& RawMessageSet_Item::default_instance() {
  protobuf_InitDefaults_google_2fprotobuf_2funittest_5fmset_2eproto();
  return *internal_default_instance();
}

::google::protobuf::internal::ExplicitlyConstructed<RawMessageSet_Item> RawMessageSet_Item_default_instance_;

RawMessageSet_Item* RawMessageSet_Item::New(::google::protobuf::Arena* arena) const {
  return ::google::protobuf::Arena::CreateMessage<RawMessageSet_Item>(arena);
}

void RawMessageSet_Item::Clear() {
// @@protoc_insertion_point(message_clear_start:protobuf_unittest.RawMessageSet.Item)
  if (_has_bits_[0 / 32] & 3u) {
    type_id_ = 0;
    if (has_message()) {
      message_.ClearToEmpty(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
    }
  }
  _has_bits_.Clear();
  if (_internal_metadata_.have_unknown_fields()) {
    mutable_unknown_fields()->Clear();
  }
}

bool RawMessageSet_Item::MergePartialFromCodedStream(
    ::google::protobuf::io::CodedInputStream* input) {
#define DO_(EXPRESSION) if (!GOOGLE_PREDICT_TRUE(EXPRESSION)) goto failure
  ::google::protobuf::uint32 tag;
  // @@protoc_insertion_point(parse_start:protobuf_unittest.RawMessageSet.Item)
  for (;;) {
    ::std::pair< ::google::protobuf::uint32, bool> p = input->ReadTagWithCutoff(127);
    tag = p.first;
    if (!p.second) goto handle_unusual;
    switch (::google::protobuf::internal::WireFormatLite::GetTagFieldNumber(tag)) {
      // required int32 type_id = 2;
      case 2: {
        if (tag == 16) {
          set_has_type_id();
          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int32, ::google::protobuf::internal::WireFormatLite::TYPE_INT32>(
                 input, &type_id_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(26)) goto parse_message;
        break;
      }

      // required bytes message = 3;
      case 3: {
        if (tag == 26) {
         parse_message:
          DO_(::google::protobuf::internal::WireFormatLite::ReadBytes(
                input, this->mutable_message()));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectAtEnd()) goto success;
        break;
      }

      default: {
      handle_unusual:
        if (tag == 0 ||
            ::google::protobuf::internal::WireFormatLite::GetTagWireType(tag) ==
            ::google::protobuf::internal::WireFormatLite::WIRETYPE_END_GROUP) {
          goto success;
        }
        DO_(::google::protobuf::internal::WireFormat::SkipField(
              input, tag, mutable_unknown_fields()));
        break;
      }
    }
  }
success:
  // @@protoc_insertion_point(parse_success:protobuf_unittest.RawMessageSet.Item)
  return true;
failure:
  // @@protoc_insertion_point(parse_failure:protobuf_unittest.RawMessageSet.Item)
  return false;
#undef DO_
}

void RawMessageSet_Item::SerializeWithCachedSizes(
    ::google::protobuf::io::CodedOutputStream* output) const {
  // @@protoc_insertion_point(serialize_start:protobuf_unittest.RawMessageSet.Item)
  // required int32 type_id = 2;
  if (has_type_id()) {
    ::google::protobuf::internal::WireFormatLite::WriteInt32(2, this->type_id(), output);
  }

  // required bytes message = 3;
  if (has_message()) {
    ::google::protobuf::internal::WireFormatLite::WriteBytesMaybeAliased(
      3, this->message(), output);
  }

  if (_internal_metadata_.have_unknown_fields()) {
    ::google::protobuf::internal::WireFormat::SerializeUnknownFields(
        unknown_fields(), output);
  }
  // @@protoc_insertion_point(serialize_end:protobuf_unittest.RawMessageSet.Item)
}

::google::protobuf::uint8* RawMessageSet_Item::InternalSerializeWithCachedSizesToArray(
    bool deterministic, ::google::protobuf::uint8* target) const {
  (void)deterministic; // Unused
  // @@protoc_insertion_point(serialize_to_array_start:protobuf_unittest.RawMessageSet.Item)
  // required int32 type_id = 2;
  if (has_type_id()) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt32ToArray(2, this->type_id(), target);
  }

  // required bytes message = 3;
  if (has_message()) {
    target =
      ::google::protobuf::internal::WireFormatLite::WriteBytesToArray(
        3, this->message(), target);
  }

  if (_internal_metadata_.have_unknown_fields()) {
    target = ::google::protobuf::internal::WireFormat::SerializeUnknownFieldsToArray(
        unknown_fields(), target);
  }
  // @@protoc_insertion_point(serialize_to_array_end:protobuf_unittest.RawMessageSet.Item)
  return target;
}

size_t RawMessageSet_Item::RequiredFieldsByteSizeFallback() const {
// @@protoc_insertion_point(required_fields_byte_size_fallback_start:protobuf_unittest.RawMessageSet.Item)
  size_t total_size = 0;

  if (has_type_id()) {
    // required int32 type_id = 2;
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::Int32Size(
        this->type_id());
  }

  if (has_message()) {
    // required bytes message = 3;
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::BytesSize(
        this->message());
  }

  return total_size;
}
size_t RawMessageSet_Item::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:protobuf_unittest.RawMessageSet.Item)
  size_t total_size = 0;

  if (((_has_bits_[0] & 0x00000003) ^ 0x00000003) == 0) {  // All required fields are present.
    // required int32 type_id = 2;
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::Int32Size(
        this->type_id());

    // required bytes message = 3;
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::BytesSize(
        this->message());

  } else {
    total_size += RequiredFieldsByteSizeFallback();
  }
  if (_internal_metadata_.have_unknown_fields()) {
    total_size +=
      ::google::protobuf::internal::WireFormat::ComputeUnknownFieldsSize(
        unknown_fields());
  }
  int cached_size = ::google::protobuf::internal::ToCachedSize(total_size);
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = cached_size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
  return total_size;
}

void RawMessageSet_Item::MergeFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_merge_from_start:protobuf_unittest.RawMessageSet.Item)
  if (GOOGLE_PREDICT_FALSE(&from == this)) MergeFromFail(__LINE__);
  const RawMessageSet_Item* source =
      ::google::protobuf::internal::DynamicCastToGenerated<const RawMessageSet_Item>(
          &from);
  if (source == NULL) {
  // @@protoc_insertion_point(generalized_merge_from_cast_fail:protobuf_unittest.RawMessageSet.Item)
    ::google::protobuf::internal::ReflectionOps::Merge(from, this);
  } else {
  // @@protoc_insertion_point(generalized_merge_from_cast_success:protobuf_unittest.RawMessageSet.Item)
    UnsafeMergeFrom(*source);
  }
}

void RawMessageSet_Item::MergeFrom(const RawMessageSet_Item& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:protobuf_unittest.RawMessageSet.Item)
  if (GOOGLE_PREDICT_TRUE(&from != this)) {
    UnsafeMergeFrom(from);
  } else {
    MergeFromFail(__LINE__);
  }
}

void RawMessageSet_Item::UnsafeMergeFrom(const RawMessageSet_Item& from) {
  GOOGLE_DCHECK(&from != this);
  if (from._has_bits_[0 / 32] & (0xffu << (0 % 32))) {
    if (from.has_type_id()) {
      set_type_id(from.type_id());
    }
    if (from.has_message()) {
      set_message(from.message());
    }
  }
  if (from._internal_metadata_.have_unknown_fields()) {
    ::google::protobuf::UnknownFieldSet::MergeToInternalMetdata(
      from.unknown_fields(), &_internal_metadata_);
  }
}

void RawMessageSet_Item::CopyFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_copy_from_start:protobuf_unittest.RawMessageSet.Item)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

void RawMessageSet_Item::CopyFrom(const RawMessageSet_Item& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:protobuf_unittest.RawMessageSet.Item)
  if (&from == this) return;
  Clear();
  UnsafeMergeFrom(from);
}

bool RawMessageSet_Item::IsInitialized() const {
  if ((_has_bits_[0] & 0x00000003) != 0x00000003) return false;

  return true;
}

void RawMessageSet_Item::Swap(RawMessageSet_Item* other) {
  if (other == this) return;
  if (GetArenaNoVirtual() == other->GetArenaNoVirtual()) {
    InternalSwap(other);
  } else {
    RawMessageSet_Item temp;
    temp.UnsafeMergeFrom(*this);
    CopyFrom(*other);
    other->CopyFrom(temp);
  }
}
void RawMessageSet_Item::UnsafeArenaSwap(RawMessageSet_Item* other) {
  if (other == this) return;
  GOOGLE_DCHECK(GetArenaNoVirtual() == other->GetArenaNoVirtual());
  InternalSwap(other);
}
void RawMessageSet_Item::InternalSwap(RawMessageSet_Item* other) {
  std::swap(type_id_, other->type_id_);
  message_.Swap(&other->message_);
  std::swap(_has_bits_[0], other->_has_bits_[0]);
  _internal_metadata_.Swap(&other->_internal_metadata_);
  std::swap(_cached_size_, other->_cached_size_);
}

::google::protobuf::Metadata RawMessageSet_Item::GetMetadata() const {
  protobuf_AssignDescriptorsOnce();
  ::google::protobuf::Metadata metadata;
  metadata.descriptor = RawMessageSet_Item_descriptor_;
  metadata.reflection = RawMessageSet_Item_reflection_;
  return metadata;
}


// -------------------------------------------------------------------

#if !defined(_MSC_VER) || _MSC_VER >= 1900
const int RawMessageSet::kItemFieldNumber;
#endif  // !defined(_MSC_VER) || _MSC_VER >= 1900

RawMessageSet::RawMessageSet()
  : ::google::protobuf::Message(), _internal_metadata_(NULL) {
  if (this != internal_default_instance()) protobuf_InitDefaults_google_2fprotobuf_2funittest_5fmset_2eproto();
  SharedCtor();
  // @@protoc_insertion_point(constructor:protobuf_unittest.RawMessageSet)
}
RawMessageSet::RawMessageSet(::google::protobuf::Arena* arena)
  : ::google::protobuf::Message(),
  _internal_metadata_(arena),
  item_(arena) {
#ifdef GOOGLE_PROTOBUF_NO_STATIC_INITIALIZER
  protobuf_InitDefaults_google_2fprotobuf_2funittest_5fmset_2eproto();
#endif  // GOOGLE_PROTOBUF_NO_STATIC_INITIALIZER
  SharedCtor();
  RegisterArenaDtor(arena);
  // @@protoc_insertion_point(arena_constructor:protobuf_unittest.RawMessageSet)
}

void RawMessageSet::InitAsDefaultInstance() {
}

RawMessageSet::RawMessageSet(const RawMessageSet& from)
  : ::google::protobuf::Message(),
    _internal_metadata_(NULL) {
  SharedCtor();
  UnsafeMergeFrom(from);
  // @@protoc_insertion_point(copy_constructor:protobuf_unittest.RawMessageSet)
}

void RawMessageSet::SharedCtor() {
  _cached_size_ = 0;
}

RawMessageSet::~RawMessageSet() {
  // @@protoc_insertion_point(destructor:protobuf_unittest.RawMessageSet)
  SharedDtor();
}

void RawMessageSet::SharedDtor() {
  ::google::protobuf::Arena* arena = GetArenaNoVirtual();
  if (arena != NULL) {
    return;
  }

}

void RawMessageSet::ArenaDtor(void* object) {
  RawMessageSet* _this = reinterpret_cast< RawMessageSet* >(object);
  (void)_this;
}
void RawMessageSet::RegisterArenaDtor(::google::protobuf::Arena* arena) {
}
void RawMessageSet::SetCachedSize(int size) const {
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
}
const ::google::protobuf::Descriptor* RawMessageSet::descriptor() {
  protobuf_AssignDescriptorsOnce();
  return RawMessageSet_descriptor_;
}

const RawMessageSet& RawMessageSet::default_instance() {
  protobuf_InitDefaults_google_2fprotobuf_2funittest_5fmset_2eproto();
  return *internal_default_instance();
}

::google::protobuf::internal::ExplicitlyConstructed<RawMessageSet> RawMessageSet_default_instance_;

RawMessageSet* RawMessageSet::New(::google::protobuf::Arena* arena) const {
  return ::google::protobuf::Arena::CreateMessage<RawMessageSet>(arena);
}

void RawMessageSet::Clear() {
// @@protoc_insertion_point(message_clear_start:protobuf_unittest.RawMessageSet)
  item_.Clear();
  _has_bits_.Clear();
  if (_internal_metadata_.have_unknown_fields()) {
    mutable_unknown_fields()->Clear();
  }
}

bool RawMessageSet::MergePartialFromCodedStream(
    ::google::protobuf::io::CodedInputStream* input) {
#define DO_(EXPRESSION) if (!GOOGLE_PREDICT_TRUE(EXPRESSION)) goto failure
  ::google::protobuf::uint32 tag;
  // @@protoc_insertion_point(parse_start:protobuf_unittest.RawMessageSet)
  for (;;) {
    ::std::pair< ::google::protobuf::uint32, bool> p = input->ReadTagWithCutoff(127);
    tag = p.first;
    if (!p.second) goto handle_unusual;
    switch (::google::protobuf::internal::WireFormatLite::GetTagFieldNumber(tag)) {
      // repeated group Item = 1 { ... };
      case 1: {
        if (tag == 11) {
          DO_(input->IncrementRecursionDepth());
         parse_loop_item:
          DO_(::google::protobuf::internal::WireFormatLite::ReadGroupNoVirtualNoRecursionDepth(
                1, input, add_item()));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(11)) goto parse_loop_item;
        input->UnsafeDecrementRecursionDepth();
        if (input->ExpectAtEnd()) goto success;
        break;
      }

      default: {
      handle_unusual:
        if (tag == 0 ||
            ::google::protobuf::internal::WireFormatLite::GetTagWireType(tag) ==
            ::google::protobuf::internal::WireFormatLite::WIRETYPE_END_GROUP) {
          goto success;
        }
        DO_(::google::protobuf::internal::WireFormat::SkipField(
              input, tag, mutable_unknown_fields()));
        break;
      }
    }
  }
success:
  // @@protoc_insertion_point(parse_success:protobuf_unittest.RawMessageSet)
  return true;
failure:
  // @@protoc_insertion_point(parse_failure:protobuf_unittest.RawMessageSet)
  return false;
#undef DO_
}

void RawMessageSet::SerializeWithCachedSizes(
    ::google::protobuf::io::CodedOutputStream* output) const {
  // @@protoc_insertion_point(serialize_start:protobuf_unittest.RawMessageSet)
  // repeated group Item = 1 { ... };
  for (unsigned int i = 0, n = this->item_size(); i < n; i++) {
    ::google::protobuf::internal::WireFormatLite::WriteGroupMaybeToArray(
      1, this->item(i), output);
  }

  if (_internal_metadata_.have_unknown_fields()) {
    ::google::protobuf::internal::WireFormat::SerializeUnknownFields(
        unknown_fields(), output);
  }
  // @@protoc_insertion_point(serialize_end:protobuf_unittest.RawMessageSet)
}

::google::protobuf::uint8* RawMessageSet::InternalSerializeWithCachedSizesToArray(
    bool deterministic, ::google::protobuf::uint8* target) const {
  (void)deterministic; // Unused
  // @@protoc_insertion_point(serialize_to_array_start:protobuf_unittest.RawMessageSet)
  // repeated group Item = 1 { ... };
  for (unsigned int i = 0, n = this->item_size(); i < n; i++) {
    target = ::google::protobuf::internal::WireFormatLite::
      InternalWriteGroupNoVirtualToArray(
        1, this->item(i), false, target);
  }

  if (_internal_metadata_.have_unknown_fields()) {
    target = ::google::protobuf::internal::WireFormat::SerializeUnknownFieldsToArray(
        unknown_fields(), target);
  }
  // @@protoc_insertion_point(serialize_to_array_end:protobuf_unittest.RawMessageSet)
  return target;
}

size_t RawMessageSet::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:protobuf_unittest.RawMessageSet)
  size_t total_size = 0;

  // repeated group Item = 1 { ... };
  {
    unsigned int count = this->item_size();
    total_size += 2UL * count;
    for (unsigned int i = 0; i < count; i++) {
      total_size +=
        ::google::protobuf::internal::WireFormatLite::GroupSizeNoVirtual(
          this->item(i));
    }
  }

  if (_internal_metadata_.have_unknown_fields()) {
    total_size +=
      ::google::protobuf::internal::WireFormat::ComputeUnknownFieldsSize(
        unknown_fields());
  }
  int cached_size = ::google::protobuf::internal::ToCachedSize(total_size);
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = cached_size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
  return total_size;
}

void RawMessageSet::MergeFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_merge_from_start:protobuf_unittest.RawMessageSet)
  if (GOOGLE_PREDICT_FALSE(&from == this)) MergeFromFail(__LINE__);
  const RawMessageSet* source =
      ::google::protobuf::internal::DynamicCastToGenerated<const RawMessageSet>(
          &from);
  if (source == NULL) {
  // @@protoc_insertion_point(generalized_merge_from_cast_fail:protobuf_unittest.RawMessageSet)
    ::google::protobuf::internal::ReflectionOps::Merge(from, this);
  } else {
  // @@protoc_insertion_point(generalized_merge_from_cast_success:protobuf_unittest.RawMessageSet)
    UnsafeMergeFrom(*source);
  }
}

void RawMessageSet::MergeFrom(const RawMessageSet& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:protobuf_unittest.RawMessageSet)
  if (GOOGLE_PREDICT_TRUE(&from != this)) {
    UnsafeMergeFrom(from);
  } else {
    MergeFromFail(__LINE__);
  }
}

void RawMessageSet::UnsafeMergeFrom(const RawMessageSet& from) {
  GOOGLE_DCHECK(&from != this);
  item_.MergeFrom(from.item_);
  if (from._internal_metadata_.have_unknown_fields()) {
    ::google::protobuf::UnknownFieldSet::MergeToInternalMetdata(
      from.unknown_fields(), &_internal_metadata_);
  }
}

void RawMessageSet::CopyFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_copy_from_start:protobuf_unittest.RawMessageSet)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

void RawMessageSet::CopyFrom(const RawMessageSet& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:protobuf_unittest.RawMessageSet)
  if (&from == this) return;
  Clear();
  UnsafeMergeFrom(from);
}

bool RawMessageSet::IsInitialized() const {

  if (!::google::protobuf::internal::AllAreInitialized(this->item())) return false;
  return true;
}

void RawMessageSet::Swap(RawMessageSet* other) {
  if (other == this) return;
  if (GetArenaNoVirtual() == other->GetArenaNoVirtual()) {
    InternalSwap(other);
  } else {
    RawMessageSet temp;
    temp.UnsafeMergeFrom(*this);
    CopyFrom(*other);
    other->CopyFrom(temp);
  }
}
void RawMessageSet::UnsafeArenaSwap(RawMessageSet* other) {
  if (other == this) return;
  GOOGLE_DCHECK(GetArenaNoVirtual() == other->GetArenaNoVirtual());
  InternalSwap(other);
}
void RawMessageSet::InternalSwap(RawMessageSet* other) {
  item_.UnsafeArenaSwap(&other->item_);
  std::swap(_has_bits_[0], other->_has_bits_[0]);
  _internal_metadata_.Swap(&other->_internal_metadata_);
  std::swap(_cached_size_, other->_cached_size_);
}

::google::protobuf::Metadata RawMessageSet::GetMetadata() const {
  protobuf_AssignDescriptorsOnce();
  ::google::protobuf::Metadata metadata;
  metadata.descriptor = RawMessageSet_descriptor_;
  metadata.reflection = RawMessageSet_reflection_;
  return metadata;
}

#if PROTOBUF_INLINE_NOT_IN_HEADERS
// RawMessageSet_Item

// required int32 type_id = 2;
bool RawMessageSet_Item::has_type_id() const {
  return (_has_bits_[0] & 0x00000001u) != 0;
}
void RawMessageSet_Item::set_has_type_id() {
  _has_bits_[0] |= 0x00000001u;
}
void RawMessageSet_Item::clear_has_type_id() {
  _has_bits_[0] &= ~0x00000001u;
}
void RawMessageSet_Item::clear_type_id() {
  type_id_ = 0;
  clear_has_type_id();
}
::google::protobuf::int32 RawMessageSet_Item::type_id() const {
  // @@protoc_insertion_point(field_get:protobuf_unittest.RawMessageSet.Item.type_id)
  return type_id_;
}
void RawMessageSet_Item::set_type_id(::google::protobuf::int32 value) {
  set_has_type_id();
  type_id_ = value;
  // @@protoc_insertion_point(field_set:protobuf_unittest.RawMessageSet.Item.type_id)
}

// required bytes message = 3;
bool RawMessageSet_Item::has_message() const {
  return (_has_bits_[0] & 0x00000002u) != 0;
}
void RawMessageSet_Item::set_has_message() {
  _has_bits_[0] |= 0x00000002u;
}
void RawMessageSet_Item::clear_has_message() {
  _has_bits_[0] &= ~0x00000002u;
}
void RawMessageSet_Item::clear_message() {
  message_.ClearToEmpty(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
  clear_has_message();
}
const ::std::string& RawMessageSet_Item::message() const {
  // @@protoc_insertion_point(field_get:protobuf_unittest.RawMessageSet.Item.message)
  return message_.Get(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
void RawMessageSet_Item::set_message(const ::std::string& value) {
  set_has_message();
  message_.Set(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value, GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set:protobuf_unittest.RawMessageSet.Item.message)
}
void RawMessageSet_Item::set_message(const char* value) {
  set_has_message();
  message_.Set(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value),
              GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_char:protobuf_unittest.RawMessageSet.Item.message)
}
void RawMessageSet_Item::set_message(const void* value,
    size_t size) {
  set_has_message();
  message_.Set(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(
      reinterpret_cast<const char*>(value), size), GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_pointer:protobuf_unittest.RawMessageSet.Item.message)
}
::std::string* RawMessageSet_Item::mutable_message() {
  set_has_message();
  // @@protoc_insertion_point(field_mutable:protobuf_unittest.RawMessageSet.Item.message)
  return message_.Mutable(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
::std::string* RawMessageSet_Item::release_message() {
  // @@protoc_insertion_point(field_release:protobuf_unittest.RawMessageSet.Item.message)
  clear_has_message();
  return message_.Release(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
::std::string* RawMessageSet_Item::unsafe_arena_release_message() {
  // @@protoc_insertion_point(field_unsafe_arena_release:protobuf_unittest.RawMessageSet.Item.message)
  GOOGLE_DCHECK(GetArenaNoVirtual() != NULL);
  clear_has_message();
  return message_.UnsafeArenaRelease(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      GetArenaNoVirtual());
}
void RawMessageSet_Item::set_allocated_message(::std::string* message) {
  if (message != NULL) {
    set_has_message();
  } else {
    clear_has_message();
  }
  message_.SetAllocated(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), message,
      GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_allocated:protobuf_unittest.RawMessageSet.Item.message)
}
void RawMessageSet_Item::unsafe_arena_set_allocated_message(
    ::std::string* message) {
  GOOGLE_DCHECK(GetArenaNoVirtual() != NULL);
  if (message != NULL) {
    set_has_message();
  } else {
    clear_has_message();
  }
  message_.UnsafeArenaSetAllocated(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      message, GetArenaNoVirtual());
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:protobuf_unittest.RawMessageSet.Item.message)
}

inline const RawMessageSet_Item* RawMessageSet_Item::internal_default_instance() {
  return &RawMessageSet_Item_default_instance_.get();
}
// -------------------------------------------------------------------

// RawMessageSet

// repeated group Item = 1 { ... };
int RawMessageSet::item_size() const {
  return item_.size();
}
void RawMessageSet::clear_item() {
  item_.Clear();
}
const ::protobuf_unittest::RawMessageSet_Item& RawMessageSet::item(int index) const {
  // @@protoc_insertion_point(field_get:protobuf_unittest.RawMessageSet.item)
  return item_.Get(index);
}
::protobuf_unittest::RawMessageSet_Item* RawMessageSet::mutable_item(int index) {
  // @@protoc_insertion_point(field_mutable:protobuf_unittest.RawMessageSet.item)
  return item_.Mutable(index);
}
::protobuf_unittest::RawMessageSet_Item* RawMessageSet::add_item() {
  // @@protoc_insertion_point(field_add:protobuf_unittest.RawMessageSet.item)
  return item_.Add();
}
::google::protobuf::RepeatedPtrField< ::protobuf_unittest::RawMessageSet_Item >*
RawMessageSet::mutable_item() {
  // @@protoc_insertion_point(field_mutable_list:protobuf_unittest.RawMessageSet.item)
  return &item_;
}
const ::google::protobuf::RepeatedPtrField< ::protobuf_unittest::RawMessageSet_Item >&
RawMessageSet::item() const {
  // @@protoc_insertion_point(field_list:protobuf_unittest.RawMessageSet.item)
  return item_;
}

inline const RawMessageSet* RawMessageSet::internal_default_instance() {
  return &RawMessageSet_default_instance_.get();
}
#endif  // PROTOBUF_INLINE_NOT_IN_HEADERS

// @@protoc_insertion_point(namespace_scope)

}  // namespace protobuf_unittest

// @@protoc_insertion_point(global_scope)

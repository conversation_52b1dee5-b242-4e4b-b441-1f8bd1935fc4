// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: google/protobuf/unittest_proto3_arena_lite.proto

#define INTERNAL_SUPPRESS_PROTOBUF_FIELD_DEPRECATION
#include <google/protobuf/unittest_proto3_arena_lite.pb.h>

#include <algorithm>

#include <google/protobuf/stubs/common.h>
#include <google/protobuf/stubs/port.h>
#include <google/protobuf/stubs/once.h>
#include <google/protobuf/io/coded_stream.h>
#include <google/protobuf/wire_format_lite_inl.h>
#include <google/protobuf/io/zero_copy_stream_impl_lite.h>
// @@protoc_insertion_point(includes)

namespace proto3_arena_lite_unittest {

void protobuf_ShutdownFile_google_2fprotobuf_2funittest_5fproto3_5farena_5flite_2eproto() {
  TestAllTypes_default_instance_.Shutdown();
  TestAllTypes_NestedMessage_default_instance_.Shutdown();
  TestPackedTypes_default_instance_.Shutdown();
  TestUnpackedTypes_default_instance_.Shutdown();
  NestedTestAllTypes_default_instance_.Shutdown();
  ForeignMessage_default_instance_.Shutdown();
  TestEmptyMessage_default_instance_.Shutdown();
}

void protobuf_InitDefaults_google_2fprotobuf_2funittest_5fproto3_5farena_5flite_2eproto_impl() {
  GOOGLE_PROTOBUF_VERIFY_VERSION;

  ::protobuf_unittest_import::protobuf_InitDefaults_google_2fprotobuf_2funittest_5fimport_2eproto();
  ::google::protobuf::internal::GetEmptyString();
  TestAllTypes_default_instance_.DefaultConstruct();
  TestAllTypes_NestedMessage_default_instance_.DefaultConstruct();
  TestPackedTypes_default_instance_.DefaultConstruct();
  TestUnpackedTypes_default_instance_.DefaultConstruct();
  NestedTestAllTypes_default_instance_.DefaultConstruct();
  ForeignMessage_default_instance_.DefaultConstruct();
  TestEmptyMessage_default_instance_.DefaultConstruct();
  TestAllTypes_default_instance_.get_mutable()->InitAsDefaultInstance();
  TestAllTypes_NestedMessage_default_instance_.get_mutable()->InitAsDefaultInstance();
  TestPackedTypes_default_instance_.get_mutable()->InitAsDefaultInstance();
  TestUnpackedTypes_default_instance_.get_mutable()->InitAsDefaultInstance();
  NestedTestAllTypes_default_instance_.get_mutable()->InitAsDefaultInstance();
  ForeignMessage_default_instance_.get_mutable()->InitAsDefaultInstance();
  TestEmptyMessage_default_instance_.get_mutable()->InitAsDefaultInstance();
}

GOOGLE_PROTOBUF_DECLARE_ONCE(protobuf_InitDefaults_google_2fprotobuf_2funittest_5fproto3_5farena_5flite_2eproto_once_);
void protobuf_InitDefaults_google_2fprotobuf_2funittest_5fproto3_5farena_5flite_2eproto() {
  ::google::protobuf::GoogleOnceInit(&protobuf_InitDefaults_google_2fprotobuf_2funittest_5fproto3_5farena_5flite_2eproto_once_,
                 &protobuf_InitDefaults_google_2fprotobuf_2funittest_5fproto3_5farena_5flite_2eproto_impl);
}
void protobuf_AddDesc_google_2fprotobuf_2funittest_5fproto3_5farena_5flite_2eproto_impl() {
  GOOGLE_PROTOBUF_VERIFY_VERSION;

  protobuf_InitDefaults_google_2fprotobuf_2funittest_5fproto3_5farena_5flite_2eproto();
  ::protobuf_unittest_import::protobuf_AddDesc_google_2fprotobuf_2funittest_5fimport_2eproto();
  ::google::protobuf::internal::OnShutdown(&protobuf_ShutdownFile_google_2fprotobuf_2funittest_5fproto3_5farena_5flite_2eproto);
}

GOOGLE_PROTOBUF_DECLARE_ONCE(protobuf_AddDesc_google_2fprotobuf_2funittest_5fproto3_5farena_5flite_2eproto_once_);
void protobuf_AddDesc_google_2fprotobuf_2funittest_5fproto3_5farena_5flite_2eproto() {
  ::google::protobuf::GoogleOnceInit(&protobuf_AddDesc_google_2fprotobuf_2funittest_5fproto3_5farena_5flite_2eproto_once_,
                 &protobuf_AddDesc_google_2fprotobuf_2funittest_5fproto3_5farena_5flite_2eproto_impl);
}
#ifdef GOOGLE_PROTOBUF_NO_STATIC_INITIALIZER
// Force AddDescriptors() to be called at static initialization time.
struct StaticDescriptorInitializer_google_2fprotobuf_2funittest_5fproto3_5farena_5flite_2eproto {
  StaticDescriptorInitializer_google_2fprotobuf_2funittest_5fproto3_5farena_5flite_2eproto() {
    protobuf_AddDesc_google_2fprotobuf_2funittest_5fproto3_5farena_5flite_2eproto();
  }
} static_descriptor_initializer_google_2fprotobuf_2funittest_5fproto3_5farena_5flite_2eproto_;
#endif  // GOOGLE_PROTOBUF_NO_STATIC_INITIALIZER
bool ForeignEnum_IsValid(int value) {
  switch (value) {
    case 0:
    case 4:
    case 5:
    case 6:
      return true;
    default:
      return false;
  }
}


namespace {

static void MergeFromFail(int line) GOOGLE_ATTRIBUTE_COLD GOOGLE_ATTRIBUTE_NORETURN;
static void MergeFromFail(int line) {
  ::google::protobuf::internal::MergeFromFail(__FILE__, line);
}

}  // namespace


// ===================================================================

bool TestAllTypes_NestedEnum_IsValid(int value) {
  switch (value) {
    case -1:
    case 0:
    case 1:
    case 2:
    case 3:
      return true;
    default:
      return false;
  }
}

#if !defined(_MSC_VER) || _MSC_VER >= 1900
const TestAllTypes_NestedEnum TestAllTypes::ZERO;
const TestAllTypes_NestedEnum TestAllTypes::FOO;
const TestAllTypes_NestedEnum TestAllTypes::BAR;
const TestAllTypes_NestedEnum TestAllTypes::BAZ;
const TestAllTypes_NestedEnum TestAllTypes::NEG;
const TestAllTypes_NestedEnum TestAllTypes::NestedEnum_MIN;
const TestAllTypes_NestedEnum TestAllTypes::NestedEnum_MAX;
const int TestAllTypes::NestedEnum_ARRAYSIZE;
#endif  // !defined(_MSC_VER) || _MSC_VER >= 1900
#if !defined(_MSC_VER) || _MSC_VER >= 1900
const int TestAllTypes_NestedMessage::kBbFieldNumber;
#endif  // !defined(_MSC_VER) || _MSC_VER >= 1900

TestAllTypes_NestedMessage::TestAllTypes_NestedMessage()
  : ::google::protobuf::MessageLite(), _arena_ptr_(NULL) {
  if (this != internal_default_instance()) protobuf_InitDefaults_google_2fprotobuf_2funittest_5fproto3_5farena_5flite_2eproto();
  SharedCtor();
  // @@protoc_insertion_point(constructor:proto3_arena_lite_unittest.TestAllTypes.NestedMessage)
}
TestAllTypes_NestedMessage::TestAllTypes_NestedMessage(::google::protobuf::Arena* arena)
  : ::google::protobuf::MessageLite(),
  _arena_ptr_(arena) {
#ifdef GOOGLE_PROTOBUF_NO_STATIC_INITIALIZER
  protobuf_InitDefaults_google_2fprotobuf_2funittest_5fproto3_5farena_5flite_2eproto();
#endif  // GOOGLE_PROTOBUF_NO_STATIC_INITIALIZER
  SharedCtor();
  RegisterArenaDtor(arena);
  // @@protoc_insertion_point(arena_constructor:proto3_arena_lite_unittest.TestAllTypes.NestedMessage)
}

void TestAllTypes_NestedMessage::InitAsDefaultInstance() {
}

TestAllTypes_NestedMessage::TestAllTypes_NestedMessage(const TestAllTypes_NestedMessage& from)
  : ::google::protobuf::MessageLite(),
    _arena_ptr_(NULL) {
  SharedCtor();
  UnsafeMergeFrom(from);
  // @@protoc_insertion_point(copy_constructor:proto3_arena_lite_unittest.TestAllTypes.NestedMessage)
}

void TestAllTypes_NestedMessage::SharedCtor() {
  bb_ = 0;
  _cached_size_ = 0;
}

TestAllTypes_NestedMessage::~TestAllTypes_NestedMessage() {
  // @@protoc_insertion_point(destructor:proto3_arena_lite_unittest.TestAllTypes.NestedMessage)
  SharedDtor();
}

void TestAllTypes_NestedMessage::SharedDtor() {
  ::google::protobuf::Arena* arena = GetArenaNoVirtual();
  if (arena != NULL) {
    return;
  }

}

void TestAllTypes_NestedMessage::ArenaDtor(void* object) {
  TestAllTypes_NestedMessage* _this = reinterpret_cast< TestAllTypes_NestedMessage* >(object);
  (void)_this;
}
void TestAllTypes_NestedMessage::RegisterArenaDtor(::google::protobuf::Arena* arena) {
}
void TestAllTypes_NestedMessage::SetCachedSize(int size) const {
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
}
const TestAllTypes_NestedMessage& TestAllTypes_NestedMessage::default_instance() {
  protobuf_InitDefaults_google_2fprotobuf_2funittest_5fproto3_5farena_5flite_2eproto();
  return *internal_default_instance();
}

::google::protobuf::internal::ExplicitlyConstructed<TestAllTypes_NestedMessage> TestAllTypes_NestedMessage_default_instance_;

TestAllTypes_NestedMessage* TestAllTypes_NestedMessage::New(::google::protobuf::Arena* arena) const {
  return ::google::protobuf::Arena::CreateMessage<TestAllTypes_NestedMessage>(arena);
}

void TestAllTypes_NestedMessage::Clear() {
// @@protoc_insertion_point(message_clear_start:proto3_arena_lite_unittest.TestAllTypes.NestedMessage)
  bb_ = 0;
}

bool TestAllTypes_NestedMessage::MergePartialFromCodedStream(
    ::google::protobuf::io::CodedInputStream* input) {
#define DO_(EXPRESSION) if (!GOOGLE_PREDICT_TRUE(EXPRESSION)) goto failure
  ::google::protobuf::uint32 tag;
  // @@protoc_insertion_point(parse_start:proto3_arena_lite_unittest.TestAllTypes.NestedMessage)
  for (;;) {
    ::std::pair< ::google::protobuf::uint32, bool> p = input->ReadTagWithCutoff(127);
    tag = p.first;
    if (!p.second) goto handle_unusual;
    switch (::google::protobuf::internal::WireFormatLite::GetTagFieldNumber(tag)) {
      // optional int32 bb = 1;
      case 1: {
        if (tag == 8) {

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int32, ::google::protobuf::internal::WireFormatLite::TYPE_INT32>(
                 input, &bb_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectAtEnd()) goto success;
        break;
      }

      default: {
      handle_unusual:
        if (tag == 0 ||
            ::google::protobuf::internal::WireFormatLite::GetTagWireType(tag) ==
            ::google::protobuf::internal::WireFormatLite::WIRETYPE_END_GROUP) {
          goto success;
        }
        DO_(::google::protobuf::internal::WireFormatLite::SkipField(input, tag));
        break;
      }
    }
  }
success:
  // @@protoc_insertion_point(parse_success:proto3_arena_lite_unittest.TestAllTypes.NestedMessage)
  return true;
failure:
  // @@protoc_insertion_point(parse_failure:proto3_arena_lite_unittest.TestAllTypes.NestedMessage)
  return false;
#undef DO_
}

void TestAllTypes_NestedMessage::SerializeWithCachedSizes(
    ::google::protobuf::io::CodedOutputStream* output) const {
  // @@protoc_insertion_point(serialize_start:proto3_arena_lite_unittest.TestAllTypes.NestedMessage)
  // optional int32 bb = 1;
  if (this->bb() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt32(1, this->bb(), output);
  }

  // @@protoc_insertion_point(serialize_end:proto3_arena_lite_unittest.TestAllTypes.NestedMessage)
}

size_t TestAllTypes_NestedMessage::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:proto3_arena_lite_unittest.TestAllTypes.NestedMessage)
  size_t total_size = 0;

  // optional int32 bb = 1;
  if (this->bb() != 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::Int32Size(
        this->bb());
  }

  int cached_size = ::google::protobuf::internal::ToCachedSize(total_size);
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = cached_size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
  return total_size;
}

void TestAllTypes_NestedMessage::CheckTypeAndMergeFrom(
    const ::google::protobuf::MessageLite& from) {
  MergeFrom(*::google::protobuf::down_cast<const TestAllTypes_NestedMessage*>(&from));
}

void TestAllTypes_NestedMessage::MergeFrom(const TestAllTypes_NestedMessage& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:proto3_arena_lite_unittest.TestAllTypes.NestedMessage)
  if (GOOGLE_PREDICT_TRUE(&from != this)) {
    UnsafeMergeFrom(from);
  } else {
    MergeFromFail(__LINE__);
  }
}

void TestAllTypes_NestedMessage::UnsafeMergeFrom(const TestAllTypes_NestedMessage& from) {
  GOOGLE_DCHECK(&from != this);
  if (from.bb() != 0) {
    set_bb(from.bb());
  }
}

void TestAllTypes_NestedMessage::CopyFrom(const TestAllTypes_NestedMessage& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:proto3_arena_lite_unittest.TestAllTypes.NestedMessage)
  if (&from == this) return;
  Clear();
  UnsafeMergeFrom(from);
}

bool TestAllTypes_NestedMessage::IsInitialized() const {

  return true;
}

void TestAllTypes_NestedMessage::Swap(TestAllTypes_NestedMessage* other) {
  if (other == this) return;
  if (GetArenaNoVirtual() == other->GetArenaNoVirtual()) {
    InternalSwap(other);
  } else {
    TestAllTypes_NestedMessage temp;
    temp.UnsafeMergeFrom(*this);
    CopyFrom(*other);
    other->CopyFrom(temp);
  }
}
void TestAllTypes_NestedMessage::UnsafeArenaSwap(TestAllTypes_NestedMessage* other) {
  if (other == this) return;
  GOOGLE_DCHECK(GetArenaNoVirtual() == other->GetArenaNoVirtual());
  InternalSwap(other);
}
void TestAllTypes_NestedMessage::InternalSwap(TestAllTypes_NestedMessage* other) {
  std::swap(bb_, other->bb_);
  _unknown_fields_.Swap(&other->_unknown_fields_);
  std::swap(_cached_size_, other->_cached_size_);
}

::std::string TestAllTypes_NestedMessage::GetTypeName() const {
  return "proto3_arena_lite_unittest.TestAllTypes.NestedMessage";
}


// -------------------------------------------------------------------

void TestAllTypes::_slow_mutable_optional_nested_message() {
  optional_nested_message_ = ::google::protobuf::Arena::CreateMessage< ::proto3_arena_lite_unittest::TestAllTypes_NestedMessage >(
      GetArenaNoVirtual());
}
::proto3_arena_lite_unittest::TestAllTypes_NestedMessage* TestAllTypes::_slow_release_optional_nested_message() {
  if (optional_nested_message_ == NULL) {
    return NULL;
  } else {
    ::proto3_arena_lite_unittest::TestAllTypes_NestedMessage* temp = new ::proto3_arena_lite_unittest::TestAllTypes_NestedMessage(*optional_nested_message_);
    optional_nested_message_ = NULL;
    return temp;
  }
}
::proto3_arena_lite_unittest::TestAllTypes_NestedMessage* TestAllTypes::unsafe_arena_release_optional_nested_message() {
  // @@protoc_insertion_point(field_unsafe_arena_release:proto3_arena_lite_unittest.TestAllTypes.optional_nested_message)
  
  ::proto3_arena_lite_unittest::TestAllTypes_NestedMessage* temp = optional_nested_message_;
  optional_nested_message_ = NULL;
  return temp;
}
void TestAllTypes::_slow_set_allocated_optional_nested_message(
    ::google::protobuf::Arena* message_arena, ::proto3_arena_lite_unittest::TestAllTypes_NestedMessage** optional_nested_message) {
    if (message_arena != NULL && 
        ::google::protobuf::Arena::GetArena(*optional_nested_message) == NULL) {
      message_arena->Own(*optional_nested_message);
    } else if (message_arena !=
               ::google::protobuf::Arena::GetArena(*optional_nested_message)) {
      ::proto3_arena_lite_unittest::TestAllTypes_NestedMessage* new_optional_nested_message = 
            ::google::protobuf::Arena::CreateMessage< ::proto3_arena_lite_unittest::TestAllTypes_NestedMessage >(
            message_arena);
      new_optional_nested_message->CopyFrom(**optional_nested_message);
      *optional_nested_message = new_optional_nested_message;
    }
}
void TestAllTypes::unsafe_arena_set_allocated_optional_nested_message(
    ::proto3_arena_lite_unittest::TestAllTypes_NestedMessage* optional_nested_message) {
  if (GetArenaNoVirtual() == NULL) {
    delete optional_nested_message_;
  }
  optional_nested_message_ = optional_nested_message;
  if (optional_nested_message) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:proto3_arena_lite_unittest.TestAllTypes.optional_nested_message)
}
void TestAllTypes::_slow_mutable_optional_foreign_message() {
  optional_foreign_message_ = ::google::protobuf::Arena::CreateMessage< ::proto3_arena_lite_unittest::ForeignMessage >(
      GetArenaNoVirtual());
}
::proto3_arena_lite_unittest::ForeignMessage* TestAllTypes::_slow_release_optional_foreign_message() {
  if (optional_foreign_message_ == NULL) {
    return NULL;
  } else {
    ::proto3_arena_lite_unittest::ForeignMessage* temp = new ::proto3_arena_lite_unittest::ForeignMessage(*optional_foreign_message_);
    optional_foreign_message_ = NULL;
    return temp;
  }
}
::proto3_arena_lite_unittest::ForeignMessage* TestAllTypes::unsafe_arena_release_optional_foreign_message() {
  // @@protoc_insertion_point(field_unsafe_arena_release:proto3_arena_lite_unittest.TestAllTypes.optional_foreign_message)
  
  ::proto3_arena_lite_unittest::ForeignMessage* temp = optional_foreign_message_;
  optional_foreign_message_ = NULL;
  return temp;
}
void TestAllTypes::_slow_set_allocated_optional_foreign_message(
    ::google::protobuf::Arena* message_arena, ::proto3_arena_lite_unittest::ForeignMessage** optional_foreign_message) {
    if (message_arena != NULL && 
        ::google::protobuf::Arena::GetArena(*optional_foreign_message) == NULL) {
      message_arena->Own(*optional_foreign_message);
    } else if (message_arena !=
               ::google::protobuf::Arena::GetArena(*optional_foreign_message)) {
      ::proto3_arena_lite_unittest::ForeignMessage* new_optional_foreign_message = 
            ::google::protobuf::Arena::CreateMessage< ::proto3_arena_lite_unittest::ForeignMessage >(
            message_arena);
      new_optional_foreign_message->CopyFrom(**optional_foreign_message);
      *optional_foreign_message = new_optional_foreign_message;
    }
}
void TestAllTypes::unsafe_arena_set_allocated_optional_foreign_message(
    ::proto3_arena_lite_unittest::ForeignMessage* optional_foreign_message) {
  if (GetArenaNoVirtual() == NULL) {
    delete optional_foreign_message_;
  }
  optional_foreign_message_ = optional_foreign_message;
  if (optional_foreign_message) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:proto3_arena_lite_unittest.TestAllTypes.optional_foreign_message)
}
void TestAllTypes::_slow_mutable_optional_import_message() {
  optional_import_message_ = ::google::protobuf::Arena::CreateMessage< ::protobuf_unittest_import::ImportMessage >(
      GetArenaNoVirtual());
}
::protobuf_unittest_import::ImportMessage* TestAllTypes::_slow_release_optional_import_message() {
  if (optional_import_message_ == NULL) {
    return NULL;
  } else {
    ::protobuf_unittest_import::ImportMessage* temp = new ::protobuf_unittest_import::ImportMessage(*optional_import_message_);
    optional_import_message_ = NULL;
    return temp;
  }
}
::protobuf_unittest_import::ImportMessage* TestAllTypes::unsafe_arena_release_optional_import_message() {
  // @@protoc_insertion_point(field_unsafe_arena_release:proto3_arena_lite_unittest.TestAllTypes.optional_import_message)
  
  ::protobuf_unittest_import::ImportMessage* temp = optional_import_message_;
  optional_import_message_ = NULL;
  return temp;
}
void TestAllTypes::_slow_set_allocated_optional_import_message(
    ::google::protobuf::Arena* message_arena, ::protobuf_unittest_import::ImportMessage** optional_import_message) {
    if (message_arena != NULL && 
        ::google::protobuf::Arena::GetArena(*optional_import_message) == NULL) {
      message_arena->Own(*optional_import_message);
    } else if (message_arena !=
               ::google::protobuf::Arena::GetArena(*optional_import_message)) {
      ::protobuf_unittest_import::ImportMessage* new_optional_import_message = 
            ::google::protobuf::Arena::CreateMessage< ::protobuf_unittest_import::ImportMessage >(
            message_arena);
      new_optional_import_message->CopyFrom(**optional_import_message);
      *optional_import_message = new_optional_import_message;
    }
}
void TestAllTypes::unsafe_arena_set_allocated_optional_import_message(
    ::protobuf_unittest_import::ImportMessage* optional_import_message) {
  if (GetArenaNoVirtual() == NULL) {
    delete optional_import_message_;
  }
  optional_import_message_ = optional_import_message;
  if (optional_import_message) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:proto3_arena_lite_unittest.TestAllTypes.optional_import_message)
}
void TestAllTypes::_slow_mutable_optional_public_import_message() {
  optional_public_import_message_ = ::google::protobuf::Arena::Create< ::protobuf_unittest_import::PublicImportMessage >(
      GetArenaNoVirtual());
}
::protobuf_unittest_import::PublicImportMessage* TestAllTypes::_slow_release_optional_public_import_message() {
  if (optional_public_import_message_ == NULL) {
    return NULL;
  } else {
    ::protobuf_unittest_import::PublicImportMessage* temp = new ::protobuf_unittest_import::PublicImportMessage(*optional_public_import_message_);
    optional_public_import_message_ = NULL;
    return temp;
  }
}
::protobuf_unittest_import::PublicImportMessage* TestAllTypes::unsafe_arena_release_optional_public_import_message() {
  // @@protoc_insertion_point(field_unsafe_arena_release:proto3_arena_lite_unittest.TestAllTypes.optional_public_import_message)
  
  ::protobuf_unittest_import::PublicImportMessage* temp = optional_public_import_message_;
  optional_public_import_message_ = NULL;
  return temp;
}
void TestAllTypes::unsafe_arena_set_allocated_optional_public_import_message(
    ::protobuf_unittest_import::PublicImportMessage* optional_public_import_message) {
  if (GetArenaNoVirtual() == NULL) {
    delete optional_public_import_message_;
  }
  optional_public_import_message_ = optional_public_import_message;
  if (optional_public_import_message) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:proto3_arena_lite_unittest.TestAllTypes.optional_public_import_message)
}
void TestAllTypes::_slow_mutable_optional_lazy_message() {
  optional_lazy_message_ = ::google::protobuf::Arena::CreateMessage< ::proto3_arena_lite_unittest::TestAllTypes_NestedMessage >(
      GetArenaNoVirtual());
}
::proto3_arena_lite_unittest::TestAllTypes_NestedMessage* TestAllTypes::_slow_release_optional_lazy_message() {
  if (optional_lazy_message_ == NULL) {
    return NULL;
  } else {
    ::proto3_arena_lite_unittest::TestAllTypes_NestedMessage* temp = new ::proto3_arena_lite_unittest::TestAllTypes_NestedMessage(*optional_lazy_message_);
    optional_lazy_message_ = NULL;
    return temp;
  }
}
::proto3_arena_lite_unittest::TestAllTypes_NestedMessage* TestAllTypes::unsafe_arena_release_optional_lazy_message() {
  // @@protoc_insertion_point(field_unsafe_arena_release:proto3_arena_lite_unittest.TestAllTypes.optional_lazy_message)
  
  ::proto3_arena_lite_unittest::TestAllTypes_NestedMessage* temp = optional_lazy_message_;
  optional_lazy_message_ = NULL;
  return temp;
}
void TestAllTypes::_slow_set_allocated_optional_lazy_message(
    ::google::protobuf::Arena* message_arena, ::proto3_arena_lite_unittest::TestAllTypes_NestedMessage** optional_lazy_message) {
    if (message_arena != NULL && 
        ::google::protobuf::Arena::GetArena(*optional_lazy_message) == NULL) {
      message_arena->Own(*optional_lazy_message);
    } else if (message_arena !=
               ::google::protobuf::Arena::GetArena(*optional_lazy_message)) {
      ::proto3_arena_lite_unittest::TestAllTypes_NestedMessage* new_optional_lazy_message = 
            ::google::protobuf::Arena::CreateMessage< ::proto3_arena_lite_unittest::TestAllTypes_NestedMessage >(
            message_arena);
      new_optional_lazy_message->CopyFrom(**optional_lazy_message);
      *optional_lazy_message = new_optional_lazy_message;
    }
}
void TestAllTypes::unsafe_arena_set_allocated_optional_lazy_message(
    ::proto3_arena_lite_unittest::TestAllTypes_NestedMessage* optional_lazy_message) {
  if (GetArenaNoVirtual() == NULL) {
    delete optional_lazy_message_;
  }
  optional_lazy_message_ = optional_lazy_message;
  if (optional_lazy_message) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:proto3_arena_lite_unittest.TestAllTypes.optional_lazy_message)
}
#if !defined(_MSC_VER) || _MSC_VER >= 1900
const int TestAllTypes::kOptionalInt32FieldNumber;
const int TestAllTypes::kOptionalInt64FieldNumber;
const int TestAllTypes::kOptionalUint32FieldNumber;
const int TestAllTypes::kOptionalUint64FieldNumber;
const int TestAllTypes::kOptionalSint32FieldNumber;
const int TestAllTypes::kOptionalSint64FieldNumber;
const int TestAllTypes::kOptionalFixed32FieldNumber;
const int TestAllTypes::kOptionalFixed64FieldNumber;
const int TestAllTypes::kOptionalSfixed32FieldNumber;
const int TestAllTypes::kOptionalSfixed64FieldNumber;
const int TestAllTypes::kOptionalFloatFieldNumber;
const int TestAllTypes::kOptionalDoubleFieldNumber;
const int TestAllTypes::kOptionalBoolFieldNumber;
const int TestAllTypes::kOptionalStringFieldNumber;
const int TestAllTypes::kOptionalBytesFieldNumber;
const int TestAllTypes::kOptionalNestedMessageFieldNumber;
const int TestAllTypes::kOptionalForeignMessageFieldNumber;
const int TestAllTypes::kOptionalImportMessageFieldNumber;
const int TestAllTypes::kOptionalNestedEnumFieldNumber;
const int TestAllTypes::kOptionalForeignEnumFieldNumber;
const int TestAllTypes::kOptionalStringPieceFieldNumber;
const int TestAllTypes::kOptionalCordFieldNumber;
const int TestAllTypes::kOptionalPublicImportMessageFieldNumber;
const int TestAllTypes::kOptionalLazyMessageFieldNumber;
const int TestAllTypes::kRepeatedInt32FieldNumber;
const int TestAllTypes::kRepeatedInt64FieldNumber;
const int TestAllTypes::kRepeatedUint32FieldNumber;
const int TestAllTypes::kRepeatedUint64FieldNumber;
const int TestAllTypes::kRepeatedSint32FieldNumber;
const int TestAllTypes::kRepeatedSint64FieldNumber;
const int TestAllTypes::kRepeatedFixed32FieldNumber;
const int TestAllTypes::kRepeatedFixed64FieldNumber;
const int TestAllTypes::kRepeatedSfixed32FieldNumber;
const int TestAllTypes::kRepeatedSfixed64FieldNumber;
const int TestAllTypes::kRepeatedFloatFieldNumber;
const int TestAllTypes::kRepeatedDoubleFieldNumber;
const int TestAllTypes::kRepeatedBoolFieldNumber;
const int TestAllTypes::kRepeatedStringFieldNumber;
const int TestAllTypes::kRepeatedBytesFieldNumber;
const int TestAllTypes::kRepeatedNestedMessageFieldNumber;
const int TestAllTypes::kRepeatedForeignMessageFieldNumber;
const int TestAllTypes::kRepeatedImportMessageFieldNumber;
const int TestAllTypes::kRepeatedNestedEnumFieldNumber;
const int TestAllTypes::kRepeatedForeignEnumFieldNumber;
const int TestAllTypes::kRepeatedStringPieceFieldNumber;
const int TestAllTypes::kRepeatedCordFieldNumber;
const int TestAllTypes::kRepeatedLazyMessageFieldNumber;
const int TestAllTypes::kOneofUint32FieldNumber;
const int TestAllTypes::kOneofNestedMessageFieldNumber;
const int TestAllTypes::kOneofStringFieldNumber;
const int TestAllTypes::kOneofBytesFieldNumber;
#endif  // !defined(_MSC_VER) || _MSC_VER >= 1900

TestAllTypes::TestAllTypes()
  : ::google::protobuf::MessageLite(), _arena_ptr_(NULL) {
  if (this != internal_default_instance()) protobuf_InitDefaults_google_2fprotobuf_2funittest_5fproto3_5farena_5flite_2eproto();
  SharedCtor();
  // @@protoc_insertion_point(constructor:proto3_arena_lite_unittest.TestAllTypes)
}
TestAllTypes::TestAllTypes(::google::protobuf::Arena* arena)
  : ::google::protobuf::MessageLite(),
  _arena_ptr_(arena),
  repeated_int32_(arena),
  repeated_int64_(arena),
  repeated_uint32_(arena),
  repeated_uint64_(arena),
  repeated_sint32_(arena),
  repeated_sint64_(arena),
  repeated_fixed32_(arena),
  repeated_fixed64_(arena),
  repeated_sfixed32_(arena),
  repeated_sfixed64_(arena),
  repeated_float_(arena),
  repeated_double_(arena),
  repeated_bool_(arena),
  repeated_string_(arena),
  repeated_bytes_(arena),
  repeated_nested_message_(arena),
  repeated_foreign_message_(arena),
  repeated_import_message_(arena),
  repeated_nested_enum_(arena),
  repeated_foreign_enum_(arena),
  repeated_string_piece_(arena),
  repeated_cord_(arena),
  repeated_lazy_message_(arena) {
#ifdef GOOGLE_PROTOBUF_NO_STATIC_INITIALIZER
  protobuf_InitDefaults_google_2fprotobuf_2funittest_5fproto3_5farena_5flite_2eproto();
#endif  // GOOGLE_PROTOBUF_NO_STATIC_INITIALIZER
  SharedCtor();
  RegisterArenaDtor(arena);
  // @@protoc_insertion_point(arena_constructor:proto3_arena_lite_unittest.TestAllTypes)
}

void TestAllTypes::InitAsDefaultInstance() {
  optional_nested_message_ = const_cast< ::proto3_arena_lite_unittest::TestAllTypes_NestedMessage*>(
      ::proto3_arena_lite_unittest::TestAllTypes_NestedMessage::internal_default_instance());
  optional_foreign_message_ = const_cast< ::proto3_arena_lite_unittest::ForeignMessage*>(
      ::proto3_arena_lite_unittest::ForeignMessage::internal_default_instance());
  optional_import_message_ = const_cast< ::protobuf_unittest_import::ImportMessage*>(
      ::protobuf_unittest_import::ImportMessage::internal_default_instance());
  optional_public_import_message_ = const_cast< ::protobuf_unittest_import::PublicImportMessage*>(
      ::protobuf_unittest_import::PublicImportMessage::internal_default_instance());
  optional_lazy_message_ = const_cast< ::proto3_arena_lite_unittest::TestAllTypes_NestedMessage*>(
      ::proto3_arena_lite_unittest::TestAllTypes_NestedMessage::internal_default_instance());
}

TestAllTypes::TestAllTypes(const TestAllTypes& from)
  : ::google::protobuf::MessageLite(),
    _arena_ptr_(NULL) {
  SharedCtor();
  UnsafeMergeFrom(from);
  // @@protoc_insertion_point(copy_constructor:proto3_arena_lite_unittest.TestAllTypes)
}

void TestAllTypes::SharedCtor() {
  optional_string_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  optional_bytes_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  optional_string_piece_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  optional_cord_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  optional_nested_message_ = NULL;
  optional_foreign_message_ = NULL;
  optional_import_message_ = NULL;
  optional_public_import_message_ = NULL;
  optional_lazy_message_ = NULL;
  ::memset(&optional_int64_, 0, reinterpret_cast<char*>(&optional_foreign_enum_) -
    reinterpret_cast<char*>(&optional_int64_) + sizeof(optional_foreign_enum_));
  clear_has_oneof_field();
  _cached_size_ = 0;
}

TestAllTypes::~TestAllTypes() {
  // @@protoc_insertion_point(destructor:proto3_arena_lite_unittest.TestAllTypes)
  SharedDtor();
}

void TestAllTypes::SharedDtor() {
  ::google::protobuf::Arena* arena = GetArenaNoVirtual();
  if (arena != NULL) {
    return;
  }

  optional_string_.Destroy(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), arena);
  optional_bytes_.Destroy(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), arena);
  optional_string_piece_.Destroy(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), arena);
  optional_cord_.Destroy(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), arena);
  if (has_oneof_field()) {
    clear_oneof_field();
  }
  if (this != &TestAllTypes_default_instance_.get()) {
    delete optional_nested_message_;
    delete optional_foreign_message_;
    delete optional_import_message_;
    delete optional_public_import_message_;
    delete optional_lazy_message_;
  }
}

void TestAllTypes::ArenaDtor(void* object) {
  TestAllTypes* _this = reinterpret_cast< TestAllTypes* >(object);
  (void)_this;
}
void TestAllTypes::RegisterArenaDtor(::google::protobuf::Arena* arena) {
}
void TestAllTypes::SetCachedSize(int size) const {
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
}
const TestAllTypes& TestAllTypes::default_instance() {
  protobuf_InitDefaults_google_2fprotobuf_2funittest_5fproto3_5farena_5flite_2eproto();
  return *internal_default_instance();
}

::google::protobuf::internal::ExplicitlyConstructed<TestAllTypes> TestAllTypes_default_instance_;

TestAllTypes* TestAllTypes::New(::google::protobuf::Arena* arena) const {
  return ::google::protobuf::Arena::CreateMessage<TestAllTypes>(arena);
}

void TestAllTypes::clear_oneof_field() {
// @@protoc_insertion_point(one_of_clear_start:proto3_arena_lite_unittest.TestAllTypes)
  switch (oneof_field_case()) {
    case kOneofUint32: {
      // No need to clear
      break;
    }
    case kOneofNestedMessage: {
      if (GetArenaNoVirtual() == NULL) {
        delete oneof_field_.oneof_nested_message_;
      }
      break;
    }
    case kOneofString: {
      oneof_field_.oneof_string_.Destroy(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
          GetArenaNoVirtual());
      break;
    }
    case kOneofBytes: {
      oneof_field_.oneof_bytes_.Destroy(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
          GetArenaNoVirtual());
      break;
    }
    case ONEOF_FIELD_NOT_SET: {
      break;
    }
  }
  _oneof_case_[0] = ONEOF_FIELD_NOT_SET;
}


void TestAllTypes::Clear() {
// @@protoc_insertion_point(message_clear_start:proto3_arena_lite_unittest.TestAllTypes)
#if defined(__clang__)
#define ZR_HELPER_(f) \
  _Pragma("clang diagnostic push") \
  _Pragma("clang diagnostic ignored \"-Winvalid-offsetof\"") \
  __builtin_offsetof(TestAllTypes, f) \
  _Pragma("clang diagnostic pop")
#else
#define ZR_HELPER_(f) reinterpret_cast<char*>(\
  &reinterpret_cast<TestAllTypes*>(16)->f)
#endif

#define ZR_(first, last) do {\
  ::memset(&(first), 0,\
           ZR_HELPER_(last) - ZR_HELPER_(first) + sizeof(last));\
} while (0)

  ZR_(optional_int64_, optional_fixed64_);
  ZR_(optional_sfixed64_, optional_bool_);
  optional_string_.ClearToEmpty(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
  optional_bytes_.ClearToEmpty(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
  if (GetArenaNoVirtual() == NULL && optional_nested_message_ != NULL) delete optional_nested_message_;
  optional_nested_message_ = NULL;
  ZR_(optional_nested_enum_, optional_foreign_enum_);
  if (GetArenaNoVirtual() == NULL && optional_foreign_message_ != NULL) delete optional_foreign_message_;
  optional_foreign_message_ = NULL;
  if (GetArenaNoVirtual() == NULL && optional_import_message_ != NULL) delete optional_import_message_;
  optional_import_message_ = NULL;
  optional_string_piece_.ClearToEmpty(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
  optional_cord_.ClearToEmpty(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
  if (GetArenaNoVirtual() == NULL && optional_public_import_message_ != NULL) delete optional_public_import_message_;
  optional_public_import_message_ = NULL;
  if (GetArenaNoVirtual() == NULL && optional_lazy_message_ != NULL) delete optional_lazy_message_;
  optional_lazy_message_ = NULL;

#undef ZR_HELPER_
#undef ZR_

  repeated_int32_.Clear();
  repeated_int64_.Clear();
  repeated_uint32_.Clear();
  repeated_uint64_.Clear();
  repeated_sint32_.Clear();
  repeated_sint64_.Clear();
  repeated_fixed32_.Clear();
  repeated_fixed64_.Clear();
  repeated_sfixed32_.Clear();
  repeated_sfixed64_.Clear();
  repeated_float_.Clear();
  repeated_double_.Clear();
  repeated_bool_.Clear();
  repeated_string_.Clear();
  repeated_bytes_.Clear();
  repeated_nested_message_.Clear();
  repeated_foreign_message_.Clear();
  repeated_import_message_.Clear();
  repeated_nested_enum_.Clear();
  repeated_foreign_enum_.Clear();
  repeated_string_piece_.Clear();
  repeated_cord_.Clear();
  repeated_lazy_message_.Clear();
  clear_oneof_field();
}

bool TestAllTypes::MergePartialFromCodedStream(
    ::google::protobuf::io::CodedInputStream* input) {
#define DO_(EXPRESSION) if (!GOOGLE_PREDICT_TRUE(EXPRESSION)) goto failure
  ::google::protobuf::uint32 tag;
  // @@protoc_insertion_point(parse_start:proto3_arena_lite_unittest.TestAllTypes)
  for (;;) {
    ::std::pair< ::google::protobuf::uint32, bool> p = input->ReadTagWithCutoff(16383);
    tag = p.first;
    if (!p.second) goto handle_unusual;
    switch (::google::protobuf::internal::WireFormatLite::GetTagFieldNumber(tag)) {
      // optional int32 optional_int32 = 1;
      case 1: {
        if (tag == 8) {

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int32, ::google::protobuf::internal::WireFormatLite::TYPE_INT32>(
                 input, &optional_int32_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(16)) goto parse_optional_int64;
        break;
      }

      // optional int64 optional_int64 = 2;
      case 2: {
        if (tag == 16) {
         parse_optional_int64:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_INT64>(
                 input, &optional_int64_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(24)) goto parse_optional_uint32;
        break;
      }

      // optional uint32 optional_uint32 = 3;
      case 3: {
        if (tag == 24) {
         parse_optional_uint32:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::uint32, ::google::protobuf::internal::WireFormatLite::TYPE_UINT32>(
                 input, &optional_uint32_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(32)) goto parse_optional_uint64;
        break;
      }

      // optional uint64 optional_uint64 = 4;
      case 4: {
        if (tag == 32) {
         parse_optional_uint64:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::uint64, ::google::protobuf::internal::WireFormatLite::TYPE_UINT64>(
                 input, &optional_uint64_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(40)) goto parse_optional_sint32;
        break;
      }

      // optional sint32 optional_sint32 = 5;
      case 5: {
        if (tag == 40) {
         parse_optional_sint32:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int32, ::google::protobuf::internal::WireFormatLite::TYPE_SINT32>(
                 input, &optional_sint32_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(48)) goto parse_optional_sint64;
        break;
      }

      // optional sint64 optional_sint64 = 6;
      case 6: {
        if (tag == 48) {
         parse_optional_sint64:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_SINT64>(
                 input, &optional_sint64_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(61)) goto parse_optional_fixed32;
        break;
      }

      // optional fixed32 optional_fixed32 = 7;
      case 7: {
        if (tag == 61) {
         parse_optional_fixed32:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::uint32, ::google::protobuf::internal::WireFormatLite::TYPE_FIXED32>(
                 input, &optional_fixed32_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(65)) goto parse_optional_fixed64;
        break;
      }

      // optional fixed64 optional_fixed64 = 8;
      case 8: {
        if (tag == 65) {
         parse_optional_fixed64:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::uint64, ::google::protobuf::internal::WireFormatLite::TYPE_FIXED64>(
                 input, &optional_fixed64_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(77)) goto parse_optional_sfixed32;
        break;
      }

      // optional sfixed32 optional_sfixed32 = 9;
      case 9: {
        if (tag == 77) {
         parse_optional_sfixed32:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int32, ::google::protobuf::internal::WireFormatLite::TYPE_SFIXED32>(
                 input, &optional_sfixed32_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(81)) goto parse_optional_sfixed64;
        break;
      }

      // optional sfixed64 optional_sfixed64 = 10;
      case 10: {
        if (tag == 81) {
         parse_optional_sfixed64:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_SFIXED64>(
                 input, &optional_sfixed64_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(93)) goto parse_optional_float;
        break;
      }

      // optional float optional_float = 11;
      case 11: {
        if (tag == 93) {
         parse_optional_float:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   float, ::google::protobuf::internal::WireFormatLite::TYPE_FLOAT>(
                 input, &optional_float_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(97)) goto parse_optional_double;
        break;
      }

      // optional double optional_double = 12;
      case 12: {
        if (tag == 97) {
         parse_optional_double:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   double, ::google::protobuf::internal::WireFormatLite::TYPE_DOUBLE>(
                 input, &optional_double_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(104)) goto parse_optional_bool;
        break;
      }

      // optional bool optional_bool = 13;
      case 13: {
        if (tag == 104) {
         parse_optional_bool:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   bool, ::google::protobuf::internal::WireFormatLite::TYPE_BOOL>(
                 input, &optional_bool_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(114)) goto parse_optional_string;
        break;
      }

      // optional string optional_string = 14;
      case 14: {
        if (tag == 114) {
         parse_optional_string:
          DO_(::google::protobuf::internal::WireFormatLite::ReadString(
                input, this->mutable_optional_string()));
          DO_(::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
            this->optional_string().data(), this->optional_string().length(),
            ::google::protobuf::internal::WireFormatLite::PARSE,
            "proto3_arena_lite_unittest.TestAllTypes.optional_string"));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(122)) goto parse_optional_bytes;
        break;
      }

      // optional bytes optional_bytes = 15;
      case 15: {
        if (tag == 122) {
         parse_optional_bytes:
          DO_(::google::protobuf::internal::WireFormatLite::ReadBytes(
                input, this->mutable_optional_bytes()));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(146)) goto parse_optional_nested_message;
        break;
      }

      // optional .proto3_arena_lite_unittest.TestAllTypes.NestedMessage optional_nested_message = 18;
      case 18: {
        if (tag == 146) {
         parse_optional_nested_message:
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessageNoVirtual(
               input, mutable_optional_nested_message()));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(154)) goto parse_optional_foreign_message;
        break;
      }

      // optional .proto3_arena_lite_unittest.ForeignMessage optional_foreign_message = 19;
      case 19: {
        if (tag == 154) {
         parse_optional_foreign_message:
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessageNoVirtual(
               input, mutable_optional_foreign_message()));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(162)) goto parse_optional_import_message;
        break;
      }

      // optional .protobuf_unittest_import.ImportMessage optional_import_message = 20;
      case 20: {
        if (tag == 162) {
         parse_optional_import_message:
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessageNoVirtual(
               input, mutable_optional_import_message()));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(168)) goto parse_optional_nested_enum;
        break;
      }

      // optional .proto3_arena_lite_unittest.TestAllTypes.NestedEnum optional_nested_enum = 21;
      case 21: {
        if (tag == 168) {
         parse_optional_nested_enum:
          int value;
          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   int, ::google::protobuf::internal::WireFormatLite::TYPE_ENUM>(
                 input, &value)));
          set_optional_nested_enum(static_cast< ::proto3_arena_lite_unittest::TestAllTypes_NestedEnum >(value));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(176)) goto parse_optional_foreign_enum;
        break;
      }

      // optional .proto3_arena_lite_unittest.ForeignEnum optional_foreign_enum = 22;
      case 22: {
        if (tag == 176) {
         parse_optional_foreign_enum:
          int value;
          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   int, ::google::protobuf::internal::WireFormatLite::TYPE_ENUM>(
                 input, &value)));
          set_optional_foreign_enum(static_cast< ::proto3_arena_lite_unittest::ForeignEnum >(value));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(194)) goto parse_optional_string_piece;
        break;
      }

      // optional string optional_string_piece = 24 [ctype = STRING_PIECE];
      case 24: {
        if (tag == 194) {
         parse_optional_string_piece:
          DO_(::google::protobuf::internal::WireFormatLite::ReadString(
                input, this->mutable_optional_string_piece()));
          DO_(::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
            this->optional_string_piece().data(), this->optional_string_piece().length(),
            ::google::protobuf::internal::WireFormatLite::PARSE,
            "proto3_arena_lite_unittest.TestAllTypes.optional_string_piece"));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(202)) goto parse_optional_cord;
        break;
      }

      // optional string optional_cord = 25 [ctype = CORD];
      case 25: {
        if (tag == 202) {
         parse_optional_cord:
          DO_(::google::protobuf::internal::WireFormatLite::ReadString(
                input, this->mutable_optional_cord()));
          DO_(::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
            this->optional_cord().data(), this->optional_cord().length(),
            ::google::protobuf::internal::WireFormatLite::PARSE,
            "proto3_arena_lite_unittest.TestAllTypes.optional_cord"));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(210)) goto parse_optional_public_import_message;
        break;
      }

      // optional .protobuf_unittest_import.PublicImportMessage optional_public_import_message = 26;
      case 26: {
        if (tag == 210) {
         parse_optional_public_import_message:
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessageNoVirtual(
               input, mutable_optional_public_import_message()));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(218)) goto parse_optional_lazy_message;
        break;
      }

      // optional .proto3_arena_lite_unittest.TestAllTypes.NestedMessage optional_lazy_message = 27 [lazy = true];
      case 27: {
        if (tag == 218) {
         parse_optional_lazy_message:
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessageNoVirtual(
               input, mutable_optional_lazy_message()));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(250)) goto parse_repeated_int32;
        break;
      }

      // repeated int32 repeated_int32 = 31;
      case 31: {
        if (tag == 250) {
         parse_repeated_int32:
          DO_((::google::protobuf::internal::WireFormatLite::ReadPackedPrimitive<
                   ::google::protobuf::int32, ::google::protobuf::internal::WireFormatLite::TYPE_INT32>(
                 input, this->mutable_repeated_int32())));
        } else if (tag == 248) {
          DO_((::google::protobuf::internal::WireFormatLite::ReadRepeatedPrimitiveNoInline<
                   ::google::protobuf::int32, ::google::protobuf::internal::WireFormatLite::TYPE_INT32>(
                 2, 250, input, this->mutable_repeated_int32())));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(258)) goto parse_repeated_int64;
        break;
      }

      // repeated int64 repeated_int64 = 32;
      case 32: {
        if (tag == 258) {
         parse_repeated_int64:
          DO_((::google::protobuf::internal::WireFormatLite::ReadPackedPrimitive<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_INT64>(
                 input, this->mutable_repeated_int64())));
        } else if (tag == 256) {
          DO_((::google::protobuf::internal::WireFormatLite::ReadRepeatedPrimitiveNoInline<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_INT64>(
                 2, 258, input, this->mutable_repeated_int64())));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(266)) goto parse_repeated_uint32;
        break;
      }

      // repeated uint32 repeated_uint32 = 33;
      case 33: {
        if (tag == 266) {
         parse_repeated_uint32:
          DO_((::google::protobuf::internal::WireFormatLite::ReadPackedPrimitive<
                   ::google::protobuf::uint32, ::google::protobuf::internal::WireFormatLite::TYPE_UINT32>(
                 input, this->mutable_repeated_uint32())));
        } else if (tag == 264) {
          DO_((::google::protobuf::internal::WireFormatLite::ReadRepeatedPrimitiveNoInline<
                   ::google::protobuf::uint32, ::google::protobuf::internal::WireFormatLite::TYPE_UINT32>(
                 2, 266, input, this->mutable_repeated_uint32())));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(274)) goto parse_repeated_uint64;
        break;
      }

      // repeated uint64 repeated_uint64 = 34;
      case 34: {
        if (tag == 274) {
         parse_repeated_uint64:
          DO_((::google::protobuf::internal::WireFormatLite::ReadPackedPrimitive<
                   ::google::protobuf::uint64, ::google::protobuf::internal::WireFormatLite::TYPE_UINT64>(
                 input, this->mutable_repeated_uint64())));
        } else if (tag == 272) {
          DO_((::google::protobuf::internal::WireFormatLite::ReadRepeatedPrimitiveNoInline<
                   ::google::protobuf::uint64, ::google::protobuf::internal::WireFormatLite::TYPE_UINT64>(
                 2, 274, input, this->mutable_repeated_uint64())));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(282)) goto parse_repeated_sint32;
        break;
      }

      // repeated sint32 repeated_sint32 = 35;
      case 35: {
        if (tag == 282) {
         parse_repeated_sint32:
          DO_((::google::protobuf::internal::WireFormatLite::ReadPackedPrimitive<
                   ::google::protobuf::int32, ::google::protobuf::internal::WireFormatLite::TYPE_SINT32>(
                 input, this->mutable_repeated_sint32())));
        } else if (tag == 280) {
          DO_((::google::protobuf::internal::WireFormatLite::ReadRepeatedPrimitiveNoInline<
                   ::google::protobuf::int32, ::google::protobuf::internal::WireFormatLite::TYPE_SINT32>(
                 2, 282, input, this->mutable_repeated_sint32())));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(290)) goto parse_repeated_sint64;
        break;
      }

      // repeated sint64 repeated_sint64 = 36;
      case 36: {
        if (tag == 290) {
         parse_repeated_sint64:
          DO_((::google::protobuf::internal::WireFormatLite::ReadPackedPrimitive<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_SINT64>(
                 input, this->mutable_repeated_sint64())));
        } else if (tag == 288) {
          DO_((::google::protobuf::internal::WireFormatLite::ReadRepeatedPrimitiveNoInline<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_SINT64>(
                 2, 290, input, this->mutable_repeated_sint64())));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(298)) goto parse_repeated_fixed32;
        break;
      }

      // repeated fixed32 repeated_fixed32 = 37;
      case 37: {
        if (tag == 298) {
         parse_repeated_fixed32:
          DO_((::google::protobuf::internal::WireFormatLite::ReadPackedPrimitive<
                   ::google::protobuf::uint32, ::google::protobuf::internal::WireFormatLite::TYPE_FIXED32>(
                 input, this->mutable_repeated_fixed32())));
        } else if (tag == 301) {
          DO_((::google::protobuf::internal::WireFormatLite::ReadRepeatedPrimitiveNoInline<
                   ::google::protobuf::uint32, ::google::protobuf::internal::WireFormatLite::TYPE_FIXED32>(
                 2, 298, input, this->mutable_repeated_fixed32())));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(306)) goto parse_repeated_fixed64;
        break;
      }

      // repeated fixed64 repeated_fixed64 = 38;
      case 38: {
        if (tag == 306) {
         parse_repeated_fixed64:
          DO_((::google::protobuf::internal::WireFormatLite::ReadPackedPrimitive<
                   ::google::protobuf::uint64, ::google::protobuf::internal::WireFormatLite::TYPE_FIXED64>(
                 input, this->mutable_repeated_fixed64())));
        } else if (tag == 305) {
          DO_((::google::protobuf::internal::WireFormatLite::ReadRepeatedPrimitiveNoInline<
                   ::google::protobuf::uint64, ::google::protobuf::internal::WireFormatLite::TYPE_FIXED64>(
                 2, 306, input, this->mutable_repeated_fixed64())));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(314)) goto parse_repeated_sfixed32;
        break;
      }

      // repeated sfixed32 repeated_sfixed32 = 39;
      case 39: {
        if (tag == 314) {
         parse_repeated_sfixed32:
          DO_((::google::protobuf::internal::WireFormatLite::ReadPackedPrimitive<
                   ::google::protobuf::int32, ::google::protobuf::internal::WireFormatLite::TYPE_SFIXED32>(
                 input, this->mutable_repeated_sfixed32())));
        } else if (tag == 317) {
          DO_((::google::protobuf::internal::WireFormatLite::ReadRepeatedPrimitiveNoInline<
                   ::google::protobuf::int32, ::google::protobuf::internal::WireFormatLite::TYPE_SFIXED32>(
                 2, 314, input, this->mutable_repeated_sfixed32())));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(322)) goto parse_repeated_sfixed64;
        break;
      }

      // repeated sfixed64 repeated_sfixed64 = 40;
      case 40: {
        if (tag == 322) {
         parse_repeated_sfixed64:
          DO_((::google::protobuf::internal::WireFormatLite::ReadPackedPrimitive<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_SFIXED64>(
                 input, this->mutable_repeated_sfixed64())));
        } else if (tag == 321) {
          DO_((::google::protobuf::internal::WireFormatLite::ReadRepeatedPrimitiveNoInline<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_SFIXED64>(
                 2, 322, input, this->mutable_repeated_sfixed64())));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(330)) goto parse_repeated_float;
        break;
      }

      // repeated float repeated_float = 41;
      case 41: {
        if (tag == 330) {
         parse_repeated_float:
          DO_((::google::protobuf::internal::WireFormatLite::ReadPackedPrimitive<
                   float, ::google::protobuf::internal::WireFormatLite::TYPE_FLOAT>(
                 input, this->mutable_repeated_float())));
        } else if (tag == 333) {
          DO_((::google::protobuf::internal::WireFormatLite::ReadRepeatedPrimitiveNoInline<
                   float, ::google::protobuf::internal::WireFormatLite::TYPE_FLOAT>(
                 2, 330, input, this->mutable_repeated_float())));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(338)) goto parse_repeated_double;
        break;
      }

      // repeated double repeated_double = 42;
      case 42: {
        if (tag == 338) {
         parse_repeated_double:
          DO_((::google::protobuf::internal::WireFormatLite::ReadPackedPrimitive<
                   double, ::google::protobuf::internal::WireFormatLite::TYPE_DOUBLE>(
                 input, this->mutable_repeated_double())));
        } else if (tag == 337) {
          DO_((::google::protobuf::internal::WireFormatLite::ReadRepeatedPrimitiveNoInline<
                   double, ::google::protobuf::internal::WireFormatLite::TYPE_DOUBLE>(
                 2, 338, input, this->mutable_repeated_double())));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(346)) goto parse_repeated_bool;
        break;
      }

      // repeated bool repeated_bool = 43;
      case 43: {
        if (tag == 346) {
         parse_repeated_bool:
          DO_((::google::protobuf::internal::WireFormatLite::ReadPackedPrimitive<
                   bool, ::google::protobuf::internal::WireFormatLite::TYPE_BOOL>(
                 input, this->mutable_repeated_bool())));
        } else if (tag == 344) {
          DO_((::google::protobuf::internal::WireFormatLite::ReadRepeatedPrimitiveNoInline<
                   bool, ::google::protobuf::internal::WireFormatLite::TYPE_BOOL>(
                 2, 346, input, this->mutable_repeated_bool())));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(354)) goto parse_repeated_string;
        break;
      }

      // repeated string repeated_string = 44;
      case 44: {
        if (tag == 354) {
         parse_repeated_string:
          DO_(::google::protobuf::internal::WireFormatLite::ReadString(
                input, this->add_repeated_string()));
          DO_(::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
            this->repeated_string(this->repeated_string_size() - 1).data(),
            this->repeated_string(this->repeated_string_size() - 1).length(),
            ::google::protobuf::internal::WireFormatLite::PARSE,
            "proto3_arena_lite_unittest.TestAllTypes.repeated_string"));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(354)) goto parse_repeated_string;
        if (input->ExpectTag(362)) goto parse_repeated_bytes;
        break;
      }

      // repeated bytes repeated_bytes = 45;
      case 45: {
        if (tag == 362) {
         parse_repeated_bytes:
          DO_(::google::protobuf::internal::WireFormatLite::ReadBytes(
                input, this->add_repeated_bytes()));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(362)) goto parse_repeated_bytes;
        if (input->ExpectTag(386)) goto parse_repeated_nested_message;
        break;
      }

      // repeated .proto3_arena_lite_unittest.TestAllTypes.NestedMessage repeated_nested_message = 48;
      case 48: {
        if (tag == 386) {
         parse_repeated_nested_message:
          DO_(input->IncrementRecursionDepth());
         parse_loop_repeated_nested_message:
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessageNoVirtualNoRecursionDepth(
                input, add_repeated_nested_message()));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(386)) goto parse_loop_repeated_nested_message;
        if (input->ExpectTag(394)) goto parse_loop_repeated_foreign_message;
        input->UnsafeDecrementRecursionDepth();
        break;
      }

      // repeated .proto3_arena_lite_unittest.ForeignMessage repeated_foreign_message = 49;
      case 49: {
        if (tag == 394) {
          DO_(input->IncrementRecursionDepth());
         parse_loop_repeated_foreign_message:
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessageNoVirtualNoRecursionDepth(
                input, add_repeated_foreign_message()));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(394)) goto parse_loop_repeated_foreign_message;
        if (input->ExpectTag(402)) goto parse_loop_repeated_import_message;
        input->UnsafeDecrementRecursionDepth();
        break;
      }

      // repeated .protobuf_unittest_import.ImportMessage repeated_import_message = 50;
      case 50: {
        if (tag == 402) {
          DO_(input->IncrementRecursionDepth());
         parse_loop_repeated_import_message:
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessageNoVirtualNoRecursionDepth(
                input, add_repeated_import_message()));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(402)) goto parse_loop_repeated_import_message;
        input->UnsafeDecrementRecursionDepth();
        if (input->ExpectTag(410)) goto parse_repeated_nested_enum;
        break;
      }

      // repeated .proto3_arena_lite_unittest.TestAllTypes.NestedEnum repeated_nested_enum = 51;
      case 51: {
        if (tag == 410) {
         parse_repeated_nested_enum:
          ::google::protobuf::uint32 length;
          DO_(input->ReadVarint32(&length));
          ::google::protobuf::io::CodedInputStream::Limit limit = input->PushLimit(length);
          while (input->BytesUntilLimit() > 0) {
            int value;
            DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   int, ::google::protobuf::internal::WireFormatLite::TYPE_ENUM>(
                 input, &value)));
            add_repeated_nested_enum(static_cast< ::proto3_arena_lite_unittest::TestAllTypes_NestedEnum >(value));
          }
          input->PopLimit(limit);
        } else if (tag == 408) {
          int value;
          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   int, ::google::protobuf::internal::WireFormatLite::TYPE_ENUM>(
                 input, &value)));
          add_repeated_nested_enum(static_cast< ::proto3_arena_lite_unittest::TestAllTypes_NestedEnum >(value));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(418)) goto parse_repeated_foreign_enum;
        break;
      }

      // repeated .proto3_arena_lite_unittest.ForeignEnum repeated_foreign_enum = 52;
      case 52: {
        if (tag == 418) {
         parse_repeated_foreign_enum:
          ::google::protobuf::uint32 length;
          DO_(input->ReadVarint32(&length));
          ::google::protobuf::io::CodedInputStream::Limit limit = input->PushLimit(length);
          while (input->BytesUntilLimit() > 0) {
            int value;
            DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   int, ::google::protobuf::internal::WireFormatLite::TYPE_ENUM>(
                 input, &value)));
            add_repeated_foreign_enum(static_cast< ::proto3_arena_lite_unittest::ForeignEnum >(value));
          }
          input->PopLimit(limit);
        } else if (tag == 416) {
          int value;
          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   int, ::google::protobuf::internal::WireFormatLite::TYPE_ENUM>(
                 input, &value)));
          add_repeated_foreign_enum(static_cast< ::proto3_arena_lite_unittest::ForeignEnum >(value));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(434)) goto parse_repeated_string_piece;
        break;
      }

      // repeated string repeated_string_piece = 54 [ctype = STRING_PIECE];
      case 54: {
        if (tag == 434) {
         parse_repeated_string_piece:
          DO_(::google::protobuf::internal::WireFormatLite::ReadString(
                input, this->add_repeated_string_piece()));
          DO_(::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
            this->repeated_string_piece(this->repeated_string_piece_size() - 1).data(),
            this->repeated_string_piece(this->repeated_string_piece_size() - 1).length(),
            ::google::protobuf::internal::WireFormatLite::PARSE,
            "proto3_arena_lite_unittest.TestAllTypes.repeated_string_piece"));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(434)) goto parse_repeated_string_piece;
        if (input->ExpectTag(442)) goto parse_repeated_cord;
        break;
      }

      // repeated string repeated_cord = 55 [ctype = CORD];
      case 55: {
        if (tag == 442) {
         parse_repeated_cord:
          DO_(::google::protobuf::internal::WireFormatLite::ReadString(
                input, this->add_repeated_cord()));
          DO_(::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
            this->repeated_cord(this->repeated_cord_size() - 1).data(),
            this->repeated_cord(this->repeated_cord_size() - 1).length(),
            ::google::protobuf::internal::WireFormatLite::PARSE,
            "proto3_arena_lite_unittest.TestAllTypes.repeated_cord"));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(442)) goto parse_repeated_cord;
        if (input->ExpectTag(458)) goto parse_repeated_lazy_message;
        break;
      }

      // repeated .proto3_arena_lite_unittest.TestAllTypes.NestedMessage repeated_lazy_message = 57 [lazy = true];
      case 57: {
        if (tag == 458) {
         parse_repeated_lazy_message:
          DO_(input->IncrementRecursionDepth());
         parse_loop_repeated_lazy_message:
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessageNoVirtualNoRecursionDepth(
                input, add_repeated_lazy_message()));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(458)) goto parse_loop_repeated_lazy_message;
        input->UnsafeDecrementRecursionDepth();
        if (input->ExpectTag(888)) goto parse_oneof_uint32;
        break;
      }

      // optional uint32 oneof_uint32 = 111;
      case 111: {
        if (tag == 888) {
         parse_oneof_uint32:
          clear_oneof_field();
          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::uint32, ::google::protobuf::internal::WireFormatLite::TYPE_UINT32>(
                 input, &oneof_field_.oneof_uint32_)));
          set_has_oneof_uint32();
        } else {
          goto handle_unusual;
        }
        goto after_oneof_bytes;
        break;
      }

      // optional .proto3_arena_lite_unittest.TestAllTypes.NestedMessage oneof_nested_message = 112;
      case 112: {
        if (tag == 898) {
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessageNoVirtual(
               input, mutable_oneof_nested_message()));
        } else {
          goto handle_unusual;
        }
        goto after_oneof_bytes;
        break;
      }

      // optional string oneof_string = 113;
      case 113: {
        if (tag == 906) {
          DO_(::google::protobuf::internal::WireFormatLite::ReadString(
                input, this->mutable_oneof_string()));
          DO_(::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
            this->oneof_string().data(), this->oneof_string().length(),
            ::google::protobuf::internal::WireFormatLite::PARSE,
            "proto3_arena_lite_unittest.TestAllTypes.oneof_string"));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(914)) goto parse_oneof_bytes;
        break;
      }

      // optional bytes oneof_bytes = 114;
      case 114: {
        if (tag == 914) {
         parse_oneof_bytes:
          DO_(::google::protobuf::internal::WireFormatLite::ReadBytes(
                input, this->mutable_oneof_bytes()));
        } else {
          goto handle_unusual;
        }
       after_oneof_bytes:
        if (input->ExpectAtEnd()) goto success;
        break;
      }

      default: {
      handle_unusual:
        if (tag == 0 ||
            ::google::protobuf::internal::WireFormatLite::GetTagWireType(tag) ==
            ::google::protobuf::internal::WireFormatLite::WIRETYPE_END_GROUP) {
          goto success;
        }
        DO_(::google::protobuf::internal::WireFormatLite::SkipField(input, tag));
        break;
      }
    }
  }
success:
  // @@protoc_insertion_point(parse_success:proto3_arena_lite_unittest.TestAllTypes)
  return true;
failure:
  // @@protoc_insertion_point(parse_failure:proto3_arena_lite_unittest.TestAllTypes)
  return false;
#undef DO_
}

void TestAllTypes::SerializeWithCachedSizes(
    ::google::protobuf::io::CodedOutputStream* output) const {
  // @@protoc_insertion_point(serialize_start:proto3_arena_lite_unittest.TestAllTypes)
  // optional int32 optional_int32 = 1;
  if (this->optional_int32() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt32(1, this->optional_int32(), output);
  }

  // optional int64 optional_int64 = 2;
  if (this->optional_int64() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt64(2, this->optional_int64(), output);
  }

  // optional uint32 optional_uint32 = 3;
  if (this->optional_uint32() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteUInt32(3, this->optional_uint32(), output);
  }

  // optional uint64 optional_uint64 = 4;
  if (this->optional_uint64() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteUInt64(4, this->optional_uint64(), output);
  }

  // optional sint32 optional_sint32 = 5;
  if (this->optional_sint32() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteSInt32(5, this->optional_sint32(), output);
  }

  // optional sint64 optional_sint64 = 6;
  if (this->optional_sint64() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteSInt64(6, this->optional_sint64(), output);
  }

  // optional fixed32 optional_fixed32 = 7;
  if (this->optional_fixed32() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteFixed32(7, this->optional_fixed32(), output);
  }

  // optional fixed64 optional_fixed64 = 8;
  if (this->optional_fixed64() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteFixed64(8, this->optional_fixed64(), output);
  }

  // optional sfixed32 optional_sfixed32 = 9;
  if (this->optional_sfixed32() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteSFixed32(9, this->optional_sfixed32(), output);
  }

  // optional sfixed64 optional_sfixed64 = 10;
  if (this->optional_sfixed64() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteSFixed64(10, this->optional_sfixed64(), output);
  }

  // optional float optional_float = 11;
  if (this->optional_float() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteFloat(11, this->optional_float(), output);
  }

  // optional double optional_double = 12;
  if (this->optional_double() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteDouble(12, this->optional_double(), output);
  }

  // optional bool optional_bool = 13;
  if (this->optional_bool() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteBool(13, this->optional_bool(), output);
  }

  // optional string optional_string = 14;
  if (this->optional_string().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->optional_string().data(), this->optional_string().length(),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "proto3_arena_lite_unittest.TestAllTypes.optional_string");
    ::google::protobuf::internal::WireFormatLite::WriteStringMaybeAliased(
      14, this->optional_string(), output);
  }

  // optional bytes optional_bytes = 15;
  if (this->optional_bytes().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::WriteBytesMaybeAliased(
      15, this->optional_bytes(), output);
  }

  // optional .proto3_arena_lite_unittest.TestAllTypes.NestedMessage optional_nested_message = 18;
  if (this->has_optional_nested_message()) {
    ::google::protobuf::internal::WireFormatLite::WriteMessage(
      18, *this->optional_nested_message_, output);
  }

  // optional .proto3_arena_lite_unittest.ForeignMessage optional_foreign_message = 19;
  if (this->has_optional_foreign_message()) {
    ::google::protobuf::internal::WireFormatLite::WriteMessage(
      19, *this->optional_foreign_message_, output);
  }

  // optional .protobuf_unittest_import.ImportMessage optional_import_message = 20;
  if (this->has_optional_import_message()) {
    ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
      20, *this->optional_import_message_, output);
  }

  // optional .proto3_arena_lite_unittest.TestAllTypes.NestedEnum optional_nested_enum = 21;
  if (this->optional_nested_enum() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteEnum(
      21, this->optional_nested_enum(), output);
  }

  // optional .proto3_arena_lite_unittest.ForeignEnum optional_foreign_enum = 22;
  if (this->optional_foreign_enum() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteEnum(
      22, this->optional_foreign_enum(), output);
  }

  // optional string optional_string_piece = 24 [ctype = STRING_PIECE];
  if (this->optional_string_piece().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->optional_string_piece().data(), this->optional_string_piece().length(),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "proto3_arena_lite_unittest.TestAllTypes.optional_string_piece");
    ::google::protobuf::internal::WireFormatLite::WriteStringMaybeAliased(
      24, this->optional_string_piece(), output);
  }

  // optional string optional_cord = 25 [ctype = CORD];
  if (this->optional_cord().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->optional_cord().data(), this->optional_cord().length(),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "proto3_arena_lite_unittest.TestAllTypes.optional_cord");
    ::google::protobuf::internal::WireFormatLite::WriteStringMaybeAliased(
      25, this->optional_cord(), output);
  }

  // optional .protobuf_unittest_import.PublicImportMessage optional_public_import_message = 26;
  if (this->has_optional_public_import_message()) {
    ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
      26, *this->optional_public_import_message_, output);
  }

  // optional .proto3_arena_lite_unittest.TestAllTypes.NestedMessage optional_lazy_message = 27 [lazy = true];
  if (this->has_optional_lazy_message()) {
    ::google::protobuf::internal::WireFormatLite::WriteMessage(
      27, *this->optional_lazy_message_, output);
  }

  // repeated int32 repeated_int32 = 31;
  if (this->repeated_int32_size() > 0) {
    ::google::protobuf::internal::WireFormatLite::WriteTag(31, ::google::protobuf::internal::WireFormatLite::WIRETYPE_LENGTH_DELIMITED, output);
    output->WriteVarint32(_repeated_int32_cached_byte_size_);
  }
  for (int i = 0; i < this->repeated_int32_size(); i++) {
    ::google::protobuf::internal::WireFormatLite::WriteInt32NoTag(
      this->repeated_int32(i), output);
  }

  // repeated int64 repeated_int64 = 32;
  if (this->repeated_int64_size() > 0) {
    ::google::protobuf::internal::WireFormatLite::WriteTag(32, ::google::protobuf::internal::WireFormatLite::WIRETYPE_LENGTH_DELIMITED, output);
    output->WriteVarint32(_repeated_int64_cached_byte_size_);
  }
  for (int i = 0; i < this->repeated_int64_size(); i++) {
    ::google::protobuf::internal::WireFormatLite::WriteInt64NoTag(
      this->repeated_int64(i), output);
  }

  // repeated uint32 repeated_uint32 = 33;
  if (this->repeated_uint32_size() > 0) {
    ::google::protobuf::internal::WireFormatLite::WriteTag(33, ::google::protobuf::internal::WireFormatLite::WIRETYPE_LENGTH_DELIMITED, output);
    output->WriteVarint32(_repeated_uint32_cached_byte_size_);
  }
  for (int i = 0; i < this->repeated_uint32_size(); i++) {
    ::google::protobuf::internal::WireFormatLite::WriteUInt32NoTag(
      this->repeated_uint32(i), output);
  }

  // repeated uint64 repeated_uint64 = 34;
  if (this->repeated_uint64_size() > 0) {
    ::google::protobuf::internal::WireFormatLite::WriteTag(34, ::google::protobuf::internal::WireFormatLite::WIRETYPE_LENGTH_DELIMITED, output);
    output->WriteVarint32(_repeated_uint64_cached_byte_size_);
  }
  for (int i = 0; i < this->repeated_uint64_size(); i++) {
    ::google::protobuf::internal::WireFormatLite::WriteUInt64NoTag(
      this->repeated_uint64(i), output);
  }

  // repeated sint32 repeated_sint32 = 35;
  if (this->repeated_sint32_size() > 0) {
    ::google::protobuf::internal::WireFormatLite::WriteTag(35, ::google::protobuf::internal::WireFormatLite::WIRETYPE_LENGTH_DELIMITED, output);
    output->WriteVarint32(_repeated_sint32_cached_byte_size_);
  }
  for (int i = 0; i < this->repeated_sint32_size(); i++) {
    ::google::protobuf::internal::WireFormatLite::WriteSInt32NoTag(
      this->repeated_sint32(i), output);
  }

  // repeated sint64 repeated_sint64 = 36;
  if (this->repeated_sint64_size() > 0) {
    ::google::protobuf::internal::WireFormatLite::WriteTag(36, ::google::protobuf::internal::WireFormatLite::WIRETYPE_LENGTH_DELIMITED, output);
    output->WriteVarint32(_repeated_sint64_cached_byte_size_);
  }
  for (int i = 0; i < this->repeated_sint64_size(); i++) {
    ::google::protobuf::internal::WireFormatLite::WriteSInt64NoTag(
      this->repeated_sint64(i), output);
  }

  // repeated fixed32 repeated_fixed32 = 37;
  if (this->repeated_fixed32_size() > 0) {
    ::google::protobuf::internal::WireFormatLite::WriteTag(37, ::google::protobuf::internal::WireFormatLite::WIRETYPE_LENGTH_DELIMITED, output);
    output->WriteVarint32(_repeated_fixed32_cached_byte_size_);
  }
  for (int i = 0; i < this->repeated_fixed32_size(); i++) {
    ::google::protobuf::internal::WireFormatLite::WriteFixed32NoTag(
      this->repeated_fixed32(i), output);
  }

  // repeated fixed64 repeated_fixed64 = 38;
  if (this->repeated_fixed64_size() > 0) {
    ::google::protobuf::internal::WireFormatLite::WriteTag(38, ::google::protobuf::internal::WireFormatLite::WIRETYPE_LENGTH_DELIMITED, output);
    output->WriteVarint32(_repeated_fixed64_cached_byte_size_);
  }
  for (int i = 0; i < this->repeated_fixed64_size(); i++) {
    ::google::protobuf::internal::WireFormatLite::WriteFixed64NoTag(
      this->repeated_fixed64(i), output);
  }

  // repeated sfixed32 repeated_sfixed32 = 39;
  if (this->repeated_sfixed32_size() > 0) {
    ::google::protobuf::internal::WireFormatLite::WriteTag(39, ::google::protobuf::internal::WireFormatLite::WIRETYPE_LENGTH_DELIMITED, output);
    output->WriteVarint32(_repeated_sfixed32_cached_byte_size_);
  }
  for (int i = 0; i < this->repeated_sfixed32_size(); i++) {
    ::google::protobuf::internal::WireFormatLite::WriteSFixed32NoTag(
      this->repeated_sfixed32(i), output);
  }

  // repeated sfixed64 repeated_sfixed64 = 40;
  if (this->repeated_sfixed64_size() > 0) {
    ::google::protobuf::internal::WireFormatLite::WriteTag(40, ::google::protobuf::internal::WireFormatLite::WIRETYPE_LENGTH_DELIMITED, output);
    output->WriteVarint32(_repeated_sfixed64_cached_byte_size_);
  }
  for (int i = 0; i < this->repeated_sfixed64_size(); i++) {
    ::google::protobuf::internal::WireFormatLite::WriteSFixed64NoTag(
      this->repeated_sfixed64(i), output);
  }

  // repeated float repeated_float = 41;
  if (this->repeated_float_size() > 0) {
    ::google::protobuf::internal::WireFormatLite::WriteTag(41, ::google::protobuf::internal::WireFormatLite::WIRETYPE_LENGTH_DELIMITED, output);
    output->WriteVarint32(_repeated_float_cached_byte_size_);
  }
  for (int i = 0; i < this->repeated_float_size(); i++) {
    ::google::protobuf::internal::WireFormatLite::WriteFloatNoTag(
      this->repeated_float(i), output);
  }

  // repeated double repeated_double = 42;
  if (this->repeated_double_size() > 0) {
    ::google::protobuf::internal::WireFormatLite::WriteTag(42, ::google::protobuf::internal::WireFormatLite::WIRETYPE_LENGTH_DELIMITED, output);
    output->WriteVarint32(_repeated_double_cached_byte_size_);
  }
  for (int i = 0; i < this->repeated_double_size(); i++) {
    ::google::protobuf::internal::WireFormatLite::WriteDoubleNoTag(
      this->repeated_double(i), output);
  }

  // repeated bool repeated_bool = 43;
  if (this->repeated_bool_size() > 0) {
    ::google::protobuf::internal::WireFormatLite::WriteTag(43, ::google::protobuf::internal::WireFormatLite::WIRETYPE_LENGTH_DELIMITED, output);
    output->WriteVarint32(_repeated_bool_cached_byte_size_);
  }
  for (int i = 0; i < this->repeated_bool_size(); i++) {
    ::google::protobuf::internal::WireFormatLite::WriteBoolNoTag(
      this->repeated_bool(i), output);
  }

  // repeated string repeated_string = 44;
  for (int i = 0; i < this->repeated_string_size(); i++) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->repeated_string(i).data(), this->repeated_string(i).length(),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "proto3_arena_lite_unittest.TestAllTypes.repeated_string");
    ::google::protobuf::internal::WireFormatLite::WriteString(
      44, this->repeated_string(i), output);
  }

  // repeated bytes repeated_bytes = 45;
  for (int i = 0; i < this->repeated_bytes_size(); i++) {
    ::google::protobuf::internal::WireFormatLite::WriteBytes(
      45, this->repeated_bytes(i), output);
  }

  // repeated .proto3_arena_lite_unittest.TestAllTypes.NestedMessage repeated_nested_message = 48;
  for (unsigned int i = 0, n = this->repeated_nested_message_size(); i < n; i++) {
    ::google::protobuf::internal::WireFormatLite::WriteMessage(
      48, this->repeated_nested_message(i), output);
  }

  // repeated .proto3_arena_lite_unittest.ForeignMessage repeated_foreign_message = 49;
  for (unsigned int i = 0, n = this->repeated_foreign_message_size(); i < n; i++) {
    ::google::protobuf::internal::WireFormatLite::WriteMessage(
      49, this->repeated_foreign_message(i), output);
  }

  // repeated .protobuf_unittest_import.ImportMessage repeated_import_message = 50;
  for (unsigned int i = 0, n = this->repeated_import_message_size(); i < n; i++) {
    ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
      50, this->repeated_import_message(i), output);
  }

  // repeated .proto3_arena_lite_unittest.TestAllTypes.NestedEnum repeated_nested_enum = 51;
  if (this->repeated_nested_enum_size() > 0) {
    ::google::protobuf::internal::WireFormatLite::WriteTag(
      51,
      ::google::protobuf::internal::WireFormatLite::WIRETYPE_LENGTH_DELIMITED,
      output);
    output->WriteVarint32(_repeated_nested_enum_cached_byte_size_);
  }
  for (int i = 0; i < this->repeated_nested_enum_size(); i++) {
    ::google::protobuf::internal::WireFormatLite::WriteEnumNoTag(
      this->repeated_nested_enum(i), output);
  }

  // repeated .proto3_arena_lite_unittest.ForeignEnum repeated_foreign_enum = 52;
  if (this->repeated_foreign_enum_size() > 0) {
    ::google::protobuf::internal::WireFormatLite::WriteTag(
      52,
      ::google::protobuf::internal::WireFormatLite::WIRETYPE_LENGTH_DELIMITED,
      output);
    output->WriteVarint32(_repeated_foreign_enum_cached_byte_size_);
  }
  for (int i = 0; i < this->repeated_foreign_enum_size(); i++) {
    ::google::protobuf::internal::WireFormatLite::WriteEnumNoTag(
      this->repeated_foreign_enum(i), output);
  }

  // repeated string repeated_string_piece = 54 [ctype = STRING_PIECE];
  for (int i = 0; i < this->repeated_string_piece_size(); i++) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->repeated_string_piece(i).data(), this->repeated_string_piece(i).length(),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "proto3_arena_lite_unittest.TestAllTypes.repeated_string_piece");
    ::google::protobuf::internal::WireFormatLite::WriteString(
      54, this->repeated_string_piece(i), output);
  }

  // repeated string repeated_cord = 55 [ctype = CORD];
  for (int i = 0; i < this->repeated_cord_size(); i++) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->repeated_cord(i).data(), this->repeated_cord(i).length(),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "proto3_arena_lite_unittest.TestAllTypes.repeated_cord");
    ::google::protobuf::internal::WireFormatLite::WriteString(
      55, this->repeated_cord(i), output);
  }

  // repeated .proto3_arena_lite_unittest.TestAllTypes.NestedMessage repeated_lazy_message = 57 [lazy = true];
  for (unsigned int i = 0, n = this->repeated_lazy_message_size(); i < n; i++) {
    ::google::protobuf::internal::WireFormatLite::WriteMessage(
      57, this->repeated_lazy_message(i), output);
  }

  // optional uint32 oneof_uint32 = 111;
  if (has_oneof_uint32()) {
    ::google::protobuf::internal::WireFormatLite::WriteUInt32(111, this->oneof_uint32(), output);
  }

  // optional .proto3_arena_lite_unittest.TestAllTypes.NestedMessage oneof_nested_message = 112;
  if (has_oneof_nested_message()) {
    ::google::protobuf::internal::WireFormatLite::WriteMessage(
      112, *oneof_field_.oneof_nested_message_, output);
  }

  // optional string oneof_string = 113;
  if (has_oneof_string()) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->oneof_string().data(), this->oneof_string().length(),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "proto3_arena_lite_unittest.TestAllTypes.oneof_string");
    ::google::protobuf::internal::WireFormatLite::WriteStringMaybeAliased(
      113, this->oneof_string(), output);
  }

  // optional bytes oneof_bytes = 114;
  if (has_oneof_bytes()) {
    ::google::protobuf::internal::WireFormatLite::WriteBytesMaybeAliased(
      114, this->oneof_bytes(), output);
  }

  // @@protoc_insertion_point(serialize_end:proto3_arena_lite_unittest.TestAllTypes)
}

size_t TestAllTypes::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:proto3_arena_lite_unittest.TestAllTypes)
  size_t total_size = 0;

  // optional int32 optional_int32 = 1;
  if (this->optional_int32() != 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::Int32Size(
        this->optional_int32());
  }

  // optional int64 optional_int64 = 2;
  if (this->optional_int64() != 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::Int64Size(
        this->optional_int64());
  }

  // optional uint32 optional_uint32 = 3;
  if (this->optional_uint32() != 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::UInt32Size(
        this->optional_uint32());
  }

  // optional uint64 optional_uint64 = 4;
  if (this->optional_uint64() != 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::UInt64Size(
        this->optional_uint64());
  }

  // optional sint32 optional_sint32 = 5;
  if (this->optional_sint32() != 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::SInt32Size(
        this->optional_sint32());
  }

  // optional sint64 optional_sint64 = 6;
  if (this->optional_sint64() != 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::SInt64Size(
        this->optional_sint64());
  }

  // optional fixed32 optional_fixed32 = 7;
  if (this->optional_fixed32() != 0) {
    total_size += 1 + 4;
  }

  // optional fixed64 optional_fixed64 = 8;
  if (this->optional_fixed64() != 0) {
    total_size += 1 + 8;
  }

  // optional sfixed32 optional_sfixed32 = 9;
  if (this->optional_sfixed32() != 0) {
    total_size += 1 + 4;
  }

  // optional sfixed64 optional_sfixed64 = 10;
  if (this->optional_sfixed64() != 0) {
    total_size += 1 + 8;
  }

  // optional float optional_float = 11;
  if (this->optional_float() != 0) {
    total_size += 1 + 4;
  }

  // optional double optional_double = 12;
  if (this->optional_double() != 0) {
    total_size += 1 + 8;
  }

  // optional bool optional_bool = 13;
  if (this->optional_bool() != 0) {
    total_size += 1 + 1;
  }

  // optional string optional_string = 14;
  if (this->optional_string().size() > 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::StringSize(
        this->optional_string());
  }

  // optional bytes optional_bytes = 15;
  if (this->optional_bytes().size() > 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::BytesSize(
        this->optional_bytes());
  }

  // optional .proto3_arena_lite_unittest.TestAllTypes.NestedMessage optional_nested_message = 18;
  if (this->has_optional_nested_message()) {
    total_size += 2 +
      ::google::protobuf::internal::WireFormatLite::MessageSizeNoVirtual(
        *this->optional_nested_message_);
  }

  // optional .proto3_arena_lite_unittest.ForeignMessage optional_foreign_message = 19;
  if (this->has_optional_foreign_message()) {
    total_size += 2 +
      ::google::protobuf::internal::WireFormatLite::MessageSizeNoVirtual(
        *this->optional_foreign_message_);
  }

  // optional .protobuf_unittest_import.ImportMessage optional_import_message = 20;
  if (this->has_optional_import_message()) {
    total_size += 2 +
      ::google::protobuf::internal::WireFormatLite::MessageSizeNoVirtual(
        *this->optional_import_message_);
  }

  // optional .proto3_arena_lite_unittest.TestAllTypes.NestedEnum optional_nested_enum = 21;
  if (this->optional_nested_enum() != 0) {
    total_size += 2 +
      ::google::protobuf::internal::WireFormatLite::EnumSize(this->optional_nested_enum());
  }

  // optional .proto3_arena_lite_unittest.ForeignEnum optional_foreign_enum = 22;
  if (this->optional_foreign_enum() != 0) {
    total_size += 2 +
      ::google::protobuf::internal::WireFormatLite::EnumSize(this->optional_foreign_enum());
  }

  // optional string optional_string_piece = 24 [ctype = STRING_PIECE];
  if (this->optional_string_piece().size() > 0) {
    total_size += 2 +
      ::google::protobuf::internal::WireFormatLite::StringSize(
        this->optional_string_piece());
  }

  // optional string optional_cord = 25 [ctype = CORD];
  if (this->optional_cord().size() > 0) {
    total_size += 2 +
      ::google::protobuf::internal::WireFormatLite::StringSize(
        this->optional_cord());
  }

  // optional .protobuf_unittest_import.PublicImportMessage optional_public_import_message = 26;
  if (this->has_optional_public_import_message()) {
    total_size += 2 +
      ::google::protobuf::internal::WireFormatLite::MessageSizeNoVirtual(
        *this->optional_public_import_message_);
  }

  // optional .proto3_arena_lite_unittest.TestAllTypes.NestedMessage optional_lazy_message = 27 [lazy = true];
  if (this->has_optional_lazy_message()) {
    total_size += 2 +
      ::google::protobuf::internal::WireFormatLite::MessageSizeNoVirtual(
        *this->optional_lazy_message_);
  }

  // repeated int32 repeated_int32 = 31;
  {
    size_t data_size = 0;
    unsigned int count = this->repeated_int32_size();
    for (unsigned int i = 0; i < count; i++) {
      data_size += ::google::protobuf::internal::WireFormatLite::
        Int32Size(this->repeated_int32(i));
    }
    if (data_size > 0) {
      total_size += 2 +
        ::google::protobuf::internal::WireFormatLite::Int32Size(data_size);
    }
    int cached_size = ::google::protobuf::internal::ToCachedSize(data_size);
    GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
    _repeated_int32_cached_byte_size_ = cached_size;
    GOOGLE_SAFE_CONCURRENT_WRITES_END();
    total_size += data_size;
  }

  // repeated int64 repeated_int64 = 32;
  {
    size_t data_size = 0;
    unsigned int count = this->repeated_int64_size();
    for (unsigned int i = 0; i < count; i++) {
      data_size += ::google::protobuf::internal::WireFormatLite::
        Int64Size(this->repeated_int64(i));
    }
    if (data_size > 0) {
      total_size += 2 +
        ::google::protobuf::internal::WireFormatLite::Int32Size(data_size);
    }
    int cached_size = ::google::protobuf::internal::ToCachedSize(data_size);
    GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
    _repeated_int64_cached_byte_size_ = cached_size;
    GOOGLE_SAFE_CONCURRENT_WRITES_END();
    total_size += data_size;
  }

  // repeated uint32 repeated_uint32 = 33;
  {
    size_t data_size = 0;
    unsigned int count = this->repeated_uint32_size();
    for (unsigned int i = 0; i < count; i++) {
      data_size += ::google::protobuf::internal::WireFormatLite::
        UInt32Size(this->repeated_uint32(i));
    }
    if (data_size > 0) {
      total_size += 2 +
        ::google::protobuf::internal::WireFormatLite::Int32Size(data_size);
    }
    int cached_size = ::google::protobuf::internal::ToCachedSize(data_size);
    GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
    _repeated_uint32_cached_byte_size_ = cached_size;
    GOOGLE_SAFE_CONCURRENT_WRITES_END();
    total_size += data_size;
  }

  // repeated uint64 repeated_uint64 = 34;
  {
    size_t data_size = 0;
    unsigned int count = this->repeated_uint64_size();
    for (unsigned int i = 0; i < count; i++) {
      data_size += ::google::protobuf::internal::WireFormatLite::
        UInt64Size(this->repeated_uint64(i));
    }
    if (data_size > 0) {
      total_size += 2 +
        ::google::protobuf::internal::WireFormatLite::Int32Size(data_size);
    }
    int cached_size = ::google::protobuf::internal::ToCachedSize(data_size);
    GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
    _repeated_uint64_cached_byte_size_ = cached_size;
    GOOGLE_SAFE_CONCURRENT_WRITES_END();
    total_size += data_size;
  }

  // repeated sint32 repeated_sint32 = 35;
  {
    size_t data_size = 0;
    unsigned int count = this->repeated_sint32_size();
    for (unsigned int i = 0; i < count; i++) {
      data_size += ::google::protobuf::internal::WireFormatLite::
        SInt32Size(this->repeated_sint32(i));
    }
    if (data_size > 0) {
      total_size += 2 +
        ::google::protobuf::internal::WireFormatLite::Int32Size(data_size);
    }
    int cached_size = ::google::protobuf::internal::ToCachedSize(data_size);
    GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
    _repeated_sint32_cached_byte_size_ = cached_size;
    GOOGLE_SAFE_CONCURRENT_WRITES_END();
    total_size += data_size;
  }

  // repeated sint64 repeated_sint64 = 36;
  {
    size_t data_size = 0;
    unsigned int count = this->repeated_sint64_size();
    for (unsigned int i = 0; i < count; i++) {
      data_size += ::google::protobuf::internal::WireFormatLite::
        SInt64Size(this->repeated_sint64(i));
    }
    if (data_size > 0) {
      total_size += 2 +
        ::google::protobuf::internal::WireFormatLite::Int32Size(data_size);
    }
    int cached_size = ::google::protobuf::internal::ToCachedSize(data_size);
    GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
    _repeated_sint64_cached_byte_size_ = cached_size;
    GOOGLE_SAFE_CONCURRENT_WRITES_END();
    total_size += data_size;
  }

  // repeated fixed32 repeated_fixed32 = 37;
  {
    size_t data_size = 0;
    unsigned int count = this->repeated_fixed32_size();
    data_size = 4UL * count;
    if (data_size > 0) {
      total_size += 2 +
        ::google::protobuf::internal::WireFormatLite::Int32Size(data_size);
    }
    int cached_size = ::google::protobuf::internal::ToCachedSize(data_size);
    GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
    _repeated_fixed32_cached_byte_size_ = cached_size;
    GOOGLE_SAFE_CONCURRENT_WRITES_END();
    total_size += data_size;
  }

  // repeated fixed64 repeated_fixed64 = 38;
  {
    size_t data_size = 0;
    unsigned int count = this->repeated_fixed64_size();
    data_size = 8UL * count;
    if (data_size > 0) {
      total_size += 2 +
        ::google::protobuf::internal::WireFormatLite::Int32Size(data_size);
    }
    int cached_size = ::google::protobuf::internal::ToCachedSize(data_size);
    GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
    _repeated_fixed64_cached_byte_size_ = cached_size;
    GOOGLE_SAFE_CONCURRENT_WRITES_END();
    total_size += data_size;
  }

  // repeated sfixed32 repeated_sfixed32 = 39;
  {
    size_t data_size = 0;
    unsigned int count = this->repeated_sfixed32_size();
    data_size = 4UL * count;
    if (data_size > 0) {
      total_size += 2 +
        ::google::protobuf::internal::WireFormatLite::Int32Size(data_size);
    }
    int cached_size = ::google::protobuf::internal::ToCachedSize(data_size);
    GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
    _repeated_sfixed32_cached_byte_size_ = cached_size;
    GOOGLE_SAFE_CONCURRENT_WRITES_END();
    total_size += data_size;
  }

  // repeated sfixed64 repeated_sfixed64 = 40;
  {
    size_t data_size = 0;
    unsigned int count = this->repeated_sfixed64_size();
    data_size = 8UL * count;
    if (data_size > 0) {
      total_size += 2 +
        ::google::protobuf::internal::WireFormatLite::Int32Size(data_size);
    }
    int cached_size = ::google::protobuf::internal::ToCachedSize(data_size);
    GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
    _repeated_sfixed64_cached_byte_size_ = cached_size;
    GOOGLE_SAFE_CONCURRENT_WRITES_END();
    total_size += data_size;
  }

  // repeated float repeated_float = 41;
  {
    size_t data_size = 0;
    unsigned int count = this->repeated_float_size();
    data_size = 4UL * count;
    if (data_size > 0) {
      total_size += 2 +
        ::google::protobuf::internal::WireFormatLite::Int32Size(data_size);
    }
    int cached_size = ::google::protobuf::internal::ToCachedSize(data_size);
    GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
    _repeated_float_cached_byte_size_ = cached_size;
    GOOGLE_SAFE_CONCURRENT_WRITES_END();
    total_size += data_size;
  }

  // repeated double repeated_double = 42;
  {
    size_t data_size = 0;
    unsigned int count = this->repeated_double_size();
    data_size = 8UL * count;
    if (data_size > 0) {
      total_size += 2 +
        ::google::protobuf::internal::WireFormatLite::Int32Size(data_size);
    }
    int cached_size = ::google::protobuf::internal::ToCachedSize(data_size);
    GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
    _repeated_double_cached_byte_size_ = cached_size;
    GOOGLE_SAFE_CONCURRENT_WRITES_END();
    total_size += data_size;
  }

  // repeated bool repeated_bool = 43;
  {
    size_t data_size = 0;
    unsigned int count = this->repeated_bool_size();
    data_size = 1UL * count;
    if (data_size > 0) {
      total_size += 2 +
        ::google::protobuf::internal::WireFormatLite::Int32Size(data_size);
    }
    int cached_size = ::google::protobuf::internal::ToCachedSize(data_size);
    GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
    _repeated_bool_cached_byte_size_ = cached_size;
    GOOGLE_SAFE_CONCURRENT_WRITES_END();
    total_size += data_size;
  }

  // repeated string repeated_string = 44;
  total_size += 2 *
      ::google::protobuf::internal::FromIntSize(this->repeated_string_size());
  for (int i = 0; i < this->repeated_string_size(); i++) {
    total_size += ::google::protobuf::internal::WireFormatLite::StringSize(
      this->repeated_string(i));
  }

  // repeated bytes repeated_bytes = 45;
  total_size += 2 *
      ::google::protobuf::internal::FromIntSize(this->repeated_bytes_size());
  for (int i = 0; i < this->repeated_bytes_size(); i++) {
    total_size += ::google::protobuf::internal::WireFormatLite::BytesSize(
      this->repeated_bytes(i));
  }

  // repeated .proto3_arena_lite_unittest.TestAllTypes.NestedMessage repeated_nested_message = 48;
  {
    unsigned int count = this->repeated_nested_message_size();
    total_size += 2UL * count;
    for (unsigned int i = 0; i < count; i++) {
      total_size +=
        ::google::protobuf::internal::WireFormatLite::MessageSizeNoVirtual(
          this->repeated_nested_message(i));
    }
  }

  // repeated .proto3_arena_lite_unittest.ForeignMessage repeated_foreign_message = 49;
  {
    unsigned int count = this->repeated_foreign_message_size();
    total_size += 2UL * count;
    for (unsigned int i = 0; i < count; i++) {
      total_size +=
        ::google::protobuf::internal::WireFormatLite::MessageSizeNoVirtual(
          this->repeated_foreign_message(i));
    }
  }

  // repeated .protobuf_unittest_import.ImportMessage repeated_import_message = 50;
  {
    unsigned int count = this->repeated_import_message_size();
    total_size += 2UL * count;
    for (unsigned int i = 0; i < count; i++) {
      total_size +=
        ::google::protobuf::internal::WireFormatLite::MessageSizeNoVirtual(
          this->repeated_import_message(i));
    }
  }

  // repeated .proto3_arena_lite_unittest.TestAllTypes.NestedEnum repeated_nested_enum = 51;
  {
    size_t data_size = 0;
    unsigned int count = this->repeated_nested_enum_size();for (unsigned int i = 0; i < count; i++) {
      data_size += ::google::protobuf::internal::WireFormatLite::EnumSize(
        this->repeated_nested_enum(i));
    }
    if (data_size > 0) {
      total_size += 2 +
        ::google::protobuf::internal::WireFormatLite::Int32Size(data_size);
    }
    int cached_size = ::google::protobuf::internal::ToCachedSize(data_size);
    GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
    _repeated_nested_enum_cached_byte_size_ = cached_size;
    GOOGLE_SAFE_CONCURRENT_WRITES_END();
    total_size += data_size;
  }

  // repeated .proto3_arena_lite_unittest.ForeignEnum repeated_foreign_enum = 52;
  {
    size_t data_size = 0;
    unsigned int count = this->repeated_foreign_enum_size();for (unsigned int i = 0; i < count; i++) {
      data_size += ::google::protobuf::internal::WireFormatLite::EnumSize(
        this->repeated_foreign_enum(i));
    }
    if (data_size > 0) {
      total_size += 2 +
        ::google::protobuf::internal::WireFormatLite::Int32Size(data_size);
    }
    int cached_size = ::google::protobuf::internal::ToCachedSize(data_size);
    GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
    _repeated_foreign_enum_cached_byte_size_ = cached_size;
    GOOGLE_SAFE_CONCURRENT_WRITES_END();
    total_size += data_size;
  }

  // repeated string repeated_string_piece = 54 [ctype = STRING_PIECE];
  total_size += 2 *
      ::google::protobuf::internal::FromIntSize(this->repeated_string_piece_size());
  for (int i = 0; i < this->repeated_string_piece_size(); i++) {
    total_size += ::google::protobuf::internal::WireFormatLite::StringSize(
      this->repeated_string_piece(i));
  }

  // repeated string repeated_cord = 55 [ctype = CORD];
  total_size += 2 *
      ::google::protobuf::internal::FromIntSize(this->repeated_cord_size());
  for (int i = 0; i < this->repeated_cord_size(); i++) {
    total_size += ::google::protobuf::internal::WireFormatLite::StringSize(
      this->repeated_cord(i));
  }

  // repeated .proto3_arena_lite_unittest.TestAllTypes.NestedMessage repeated_lazy_message = 57 [lazy = true];
  {
    unsigned int count = this->repeated_lazy_message_size();
    total_size += 2UL * count;
    for (unsigned int i = 0; i < count; i++) {
      total_size +=
        ::google::protobuf::internal::WireFormatLite::MessageSizeNoVirtual(
          this->repeated_lazy_message(i));
    }
  }

  switch (oneof_field_case()) {
    // optional uint32 oneof_uint32 = 111;
    case kOneofUint32: {
      total_size += 2 +
        ::google::protobuf::internal::WireFormatLite::UInt32Size(
          this->oneof_uint32());
      break;
    }
    // optional .proto3_arena_lite_unittest.TestAllTypes.NestedMessage oneof_nested_message = 112;
    case kOneofNestedMessage: {
      total_size += 2 +
        ::google::protobuf::internal::WireFormatLite::MessageSizeNoVirtual(
          *oneof_field_.oneof_nested_message_);
      break;
    }
    // optional string oneof_string = 113;
    case kOneofString: {
      total_size += 2 +
        ::google::protobuf::internal::WireFormatLite::StringSize(
          this->oneof_string());
      break;
    }
    // optional bytes oneof_bytes = 114;
    case kOneofBytes: {
      total_size += 2 +
        ::google::protobuf::internal::WireFormatLite::BytesSize(
          this->oneof_bytes());
      break;
    }
    case ONEOF_FIELD_NOT_SET: {
      break;
    }
  }
  int cached_size = ::google::protobuf::internal::ToCachedSize(total_size);
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = cached_size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
  return total_size;
}

void TestAllTypes::CheckTypeAndMergeFrom(
    const ::google::protobuf::MessageLite& from) {
  MergeFrom(*::google::protobuf::down_cast<const TestAllTypes*>(&from));
}

void TestAllTypes::MergeFrom(const TestAllTypes& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:proto3_arena_lite_unittest.TestAllTypes)
  if (GOOGLE_PREDICT_TRUE(&from != this)) {
    UnsafeMergeFrom(from);
  } else {
    MergeFromFail(__LINE__);
  }
}

void TestAllTypes::UnsafeMergeFrom(const TestAllTypes& from) {
  GOOGLE_DCHECK(&from != this);
  repeated_int32_.UnsafeMergeFrom(from.repeated_int32_);
  repeated_int64_.UnsafeMergeFrom(from.repeated_int64_);
  repeated_uint32_.UnsafeMergeFrom(from.repeated_uint32_);
  repeated_uint64_.UnsafeMergeFrom(from.repeated_uint64_);
  repeated_sint32_.UnsafeMergeFrom(from.repeated_sint32_);
  repeated_sint64_.UnsafeMergeFrom(from.repeated_sint64_);
  repeated_fixed32_.UnsafeMergeFrom(from.repeated_fixed32_);
  repeated_fixed64_.UnsafeMergeFrom(from.repeated_fixed64_);
  repeated_sfixed32_.UnsafeMergeFrom(from.repeated_sfixed32_);
  repeated_sfixed64_.UnsafeMergeFrom(from.repeated_sfixed64_);
  repeated_float_.UnsafeMergeFrom(from.repeated_float_);
  repeated_double_.UnsafeMergeFrom(from.repeated_double_);
  repeated_bool_.UnsafeMergeFrom(from.repeated_bool_);
  repeated_string_.UnsafeMergeFrom(from.repeated_string_);
  repeated_bytes_.UnsafeMergeFrom(from.repeated_bytes_);
  repeated_nested_message_.MergeFrom(from.repeated_nested_message_);
  repeated_foreign_message_.MergeFrom(from.repeated_foreign_message_);
  repeated_import_message_.MergeFrom(from.repeated_import_message_);
  repeated_nested_enum_.UnsafeMergeFrom(from.repeated_nested_enum_);
  repeated_foreign_enum_.UnsafeMergeFrom(from.repeated_foreign_enum_);
  repeated_string_piece_.UnsafeMergeFrom(from.repeated_string_piece_);
  repeated_cord_.UnsafeMergeFrom(from.repeated_cord_);
  repeated_lazy_message_.MergeFrom(from.repeated_lazy_message_);
  switch (from.oneof_field_case()) {
    case kOneofUint32: {
      set_oneof_uint32(from.oneof_uint32());
      break;
    }
    case kOneofNestedMessage: {
      mutable_oneof_nested_message()->::proto3_arena_lite_unittest::TestAllTypes_NestedMessage::MergeFrom(from.oneof_nested_message());
      break;
    }
    case kOneofString: {
      set_oneof_string(from.oneof_string());
      break;
    }
    case kOneofBytes: {
      set_oneof_bytes(from.oneof_bytes());
      break;
    }
    case ONEOF_FIELD_NOT_SET: {
      break;
    }
  }
  if (from.optional_int32() != 0) {
    set_optional_int32(from.optional_int32());
  }
  if (from.optional_int64() != 0) {
    set_optional_int64(from.optional_int64());
  }
  if (from.optional_uint32() != 0) {
    set_optional_uint32(from.optional_uint32());
  }
  if (from.optional_uint64() != 0) {
    set_optional_uint64(from.optional_uint64());
  }
  if (from.optional_sint32() != 0) {
    set_optional_sint32(from.optional_sint32());
  }
  if (from.optional_sint64() != 0) {
    set_optional_sint64(from.optional_sint64());
  }
  if (from.optional_fixed32() != 0) {
    set_optional_fixed32(from.optional_fixed32());
  }
  if (from.optional_fixed64() != 0) {
    set_optional_fixed64(from.optional_fixed64());
  }
  if (from.optional_sfixed32() != 0) {
    set_optional_sfixed32(from.optional_sfixed32());
  }
  if (from.optional_sfixed64() != 0) {
    set_optional_sfixed64(from.optional_sfixed64());
  }
  if (from.optional_float() != 0) {
    set_optional_float(from.optional_float());
  }
  if (from.optional_double() != 0) {
    set_optional_double(from.optional_double());
  }
  if (from.optional_bool() != 0) {
    set_optional_bool(from.optional_bool());
  }
  if (from.optional_string().size() > 0) {
    set_optional_string(from.optional_string());
  }
  if (from.optional_bytes().size() > 0) {
    set_optional_bytes(from.optional_bytes());
  }
  if (from.has_optional_nested_message()) {
    mutable_optional_nested_message()->::proto3_arena_lite_unittest::TestAllTypes_NestedMessage::MergeFrom(from.optional_nested_message());
  }
  if (from.has_optional_foreign_message()) {
    mutable_optional_foreign_message()->::proto3_arena_lite_unittest::ForeignMessage::MergeFrom(from.optional_foreign_message());
  }
  if (from.has_optional_import_message()) {
    mutable_optional_import_message()->::protobuf_unittest_import::ImportMessage::MergeFrom(from.optional_import_message());
  }
  if (from.optional_nested_enum() != 0) {
    set_optional_nested_enum(from.optional_nested_enum());
  }
  if (from.optional_foreign_enum() != 0) {
    set_optional_foreign_enum(from.optional_foreign_enum());
  }
  if (from.optional_string_piece().size() > 0) {
    set_optional_string_piece(from.optional_string_piece());
  }
  if (from.optional_cord().size() > 0) {
    set_optional_cord(from.optional_cord());
  }
  if (from.has_optional_public_import_message()) {
    mutable_optional_public_import_message()->::protobuf_unittest_import::PublicImportMessage::MergeFrom(from.optional_public_import_message());
  }
  if (from.has_optional_lazy_message()) {
    mutable_optional_lazy_message()->::proto3_arena_lite_unittest::TestAllTypes_NestedMessage::MergeFrom(from.optional_lazy_message());
  }
}

void TestAllTypes::CopyFrom(const TestAllTypes& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:proto3_arena_lite_unittest.TestAllTypes)
  if (&from == this) return;
  Clear();
  UnsafeMergeFrom(from);
}

bool TestAllTypes::IsInitialized() const {

  return true;
}

void TestAllTypes::Swap(TestAllTypes* other) {
  if (other == this) return;
  if (GetArenaNoVirtual() == other->GetArenaNoVirtual()) {
    InternalSwap(other);
  } else {
    TestAllTypes temp;
    temp.UnsafeMergeFrom(*this);
    CopyFrom(*other);
    other->CopyFrom(temp);
  }
}
void TestAllTypes::UnsafeArenaSwap(TestAllTypes* other) {
  if (other == this) return;
  GOOGLE_DCHECK(GetArenaNoVirtual() == other->GetArenaNoVirtual());
  InternalSwap(other);
}
void TestAllTypes::InternalSwap(TestAllTypes* other) {
  std::swap(optional_int32_, other->optional_int32_);
  std::swap(optional_int64_, other->optional_int64_);
  std::swap(optional_uint32_, other->optional_uint32_);
  std::swap(optional_uint64_, other->optional_uint64_);
  std::swap(optional_sint32_, other->optional_sint32_);
  std::swap(optional_sint64_, other->optional_sint64_);
  std::swap(optional_fixed32_, other->optional_fixed32_);
  std::swap(optional_fixed64_, other->optional_fixed64_);
  std::swap(optional_sfixed32_, other->optional_sfixed32_);
  std::swap(optional_sfixed64_, other->optional_sfixed64_);
  std::swap(optional_float_, other->optional_float_);
  std::swap(optional_double_, other->optional_double_);
  std::swap(optional_bool_, other->optional_bool_);
  optional_string_.Swap(&other->optional_string_);
  optional_bytes_.Swap(&other->optional_bytes_);
  std::swap(optional_nested_message_, other->optional_nested_message_);
  std::swap(optional_foreign_message_, other->optional_foreign_message_);
  std::swap(optional_import_message_, other->optional_import_message_);
  std::swap(optional_nested_enum_, other->optional_nested_enum_);
  std::swap(optional_foreign_enum_, other->optional_foreign_enum_);
  optional_string_piece_.Swap(&other->optional_string_piece_);
  optional_cord_.Swap(&other->optional_cord_);
  std::swap(optional_public_import_message_, other->optional_public_import_message_);
  std::swap(optional_lazy_message_, other->optional_lazy_message_);
  repeated_int32_.UnsafeArenaSwap(&other->repeated_int32_);
  repeated_int64_.UnsafeArenaSwap(&other->repeated_int64_);
  repeated_uint32_.UnsafeArenaSwap(&other->repeated_uint32_);
  repeated_uint64_.UnsafeArenaSwap(&other->repeated_uint64_);
  repeated_sint32_.UnsafeArenaSwap(&other->repeated_sint32_);
  repeated_sint64_.UnsafeArenaSwap(&other->repeated_sint64_);
  repeated_fixed32_.UnsafeArenaSwap(&other->repeated_fixed32_);
  repeated_fixed64_.UnsafeArenaSwap(&other->repeated_fixed64_);
  repeated_sfixed32_.UnsafeArenaSwap(&other->repeated_sfixed32_);
  repeated_sfixed64_.UnsafeArenaSwap(&other->repeated_sfixed64_);
  repeated_float_.UnsafeArenaSwap(&other->repeated_float_);
  repeated_double_.UnsafeArenaSwap(&other->repeated_double_);
  repeated_bool_.UnsafeArenaSwap(&other->repeated_bool_);
  repeated_string_.UnsafeArenaSwap(&other->repeated_string_);
  repeated_bytes_.UnsafeArenaSwap(&other->repeated_bytes_);
  repeated_nested_message_.UnsafeArenaSwap(&other->repeated_nested_message_);
  repeated_foreign_message_.UnsafeArenaSwap(&other->repeated_foreign_message_);
  repeated_import_message_.UnsafeArenaSwap(&other->repeated_import_message_);
  repeated_nested_enum_.UnsafeArenaSwap(&other->repeated_nested_enum_);
  repeated_foreign_enum_.UnsafeArenaSwap(&other->repeated_foreign_enum_);
  repeated_string_piece_.UnsafeArenaSwap(&other->repeated_string_piece_);
  repeated_cord_.UnsafeArenaSwap(&other->repeated_cord_);
  repeated_lazy_message_.UnsafeArenaSwap(&other->repeated_lazy_message_);
  std::swap(oneof_field_, other->oneof_field_);
  std::swap(_oneof_case_[0], other->_oneof_case_[0]);
  _unknown_fields_.Swap(&other->_unknown_fields_);
  std::swap(_cached_size_, other->_cached_size_);
}

::std::string TestAllTypes::GetTypeName() const {
  return "proto3_arena_lite_unittest.TestAllTypes";
}

#if PROTOBUF_INLINE_NOT_IN_HEADERS
// TestAllTypes_NestedMessage

// optional int32 bb = 1;
void TestAllTypes_NestedMessage::clear_bb() {
  bb_ = 0;
}
::google::protobuf::int32 TestAllTypes_NestedMessage::bb() const {
  // @@protoc_insertion_point(field_get:proto3_arena_lite_unittest.TestAllTypes.NestedMessage.bb)
  return bb_;
}
void TestAllTypes_NestedMessage::set_bb(::google::protobuf::int32 value) {
  
  bb_ = value;
  // @@protoc_insertion_point(field_set:proto3_arena_lite_unittest.TestAllTypes.NestedMessage.bb)
}

inline const TestAllTypes_NestedMessage* TestAllTypes_NestedMessage::internal_default_instance() {
  return &TestAllTypes_NestedMessage_default_instance_.get();
}
// -------------------------------------------------------------------

// TestAllTypes

// optional int32 optional_int32 = 1;
void TestAllTypes::clear_optional_int32() {
  optional_int32_ = 0;
}
::google::protobuf::int32 TestAllTypes::optional_int32() const {
  // @@protoc_insertion_point(field_get:proto3_arena_lite_unittest.TestAllTypes.optional_int32)
  return optional_int32_;
}
void TestAllTypes::set_optional_int32(::google::protobuf::int32 value) {
  
  optional_int32_ = value;
  // @@protoc_insertion_point(field_set:proto3_arena_lite_unittest.TestAllTypes.optional_int32)
}

// optional int64 optional_int64 = 2;
void TestAllTypes::clear_optional_int64() {
  optional_int64_ = GOOGLE_LONGLONG(0);
}
::google::protobuf::int64 TestAllTypes::optional_int64() const {
  // @@protoc_insertion_point(field_get:proto3_arena_lite_unittest.TestAllTypes.optional_int64)
  return optional_int64_;
}
void TestAllTypes::set_optional_int64(::google::protobuf::int64 value) {
  
  optional_int64_ = value;
  // @@protoc_insertion_point(field_set:proto3_arena_lite_unittest.TestAllTypes.optional_int64)
}

// optional uint32 optional_uint32 = 3;
void TestAllTypes::clear_optional_uint32() {
  optional_uint32_ = 0u;
}
::google::protobuf::uint32 TestAllTypes::optional_uint32() const {
  // @@protoc_insertion_point(field_get:proto3_arena_lite_unittest.TestAllTypes.optional_uint32)
  return optional_uint32_;
}
void TestAllTypes::set_optional_uint32(::google::protobuf::uint32 value) {
  
  optional_uint32_ = value;
  // @@protoc_insertion_point(field_set:proto3_arena_lite_unittest.TestAllTypes.optional_uint32)
}

// optional uint64 optional_uint64 = 4;
void TestAllTypes::clear_optional_uint64() {
  optional_uint64_ = GOOGLE_ULONGLONG(0);
}
::google::protobuf::uint64 TestAllTypes::optional_uint64() const {
  // @@protoc_insertion_point(field_get:proto3_arena_lite_unittest.TestAllTypes.optional_uint64)
  return optional_uint64_;
}
void TestAllTypes::set_optional_uint64(::google::protobuf::uint64 value) {
  
  optional_uint64_ = value;
  // @@protoc_insertion_point(field_set:proto3_arena_lite_unittest.TestAllTypes.optional_uint64)
}

// optional sint32 optional_sint32 = 5;
void TestAllTypes::clear_optional_sint32() {
  optional_sint32_ = 0;
}
::google::protobuf::int32 TestAllTypes::optional_sint32() const {
  // @@protoc_insertion_point(field_get:proto3_arena_lite_unittest.TestAllTypes.optional_sint32)
  return optional_sint32_;
}
void TestAllTypes::set_optional_sint32(::google::protobuf::int32 value) {
  
  optional_sint32_ = value;
  // @@protoc_insertion_point(field_set:proto3_arena_lite_unittest.TestAllTypes.optional_sint32)
}

// optional sint64 optional_sint64 = 6;
void TestAllTypes::clear_optional_sint64() {
  optional_sint64_ = GOOGLE_LONGLONG(0);
}
::google::protobuf::int64 TestAllTypes::optional_sint64() const {
  // @@protoc_insertion_point(field_get:proto3_arena_lite_unittest.TestAllTypes.optional_sint64)
  return optional_sint64_;
}
void TestAllTypes::set_optional_sint64(::google::protobuf::int64 value) {
  
  optional_sint64_ = value;
  // @@protoc_insertion_point(field_set:proto3_arena_lite_unittest.TestAllTypes.optional_sint64)
}

// optional fixed32 optional_fixed32 = 7;
void TestAllTypes::clear_optional_fixed32() {
  optional_fixed32_ = 0u;
}
::google::protobuf::uint32 TestAllTypes::optional_fixed32() const {
  // @@protoc_insertion_point(field_get:proto3_arena_lite_unittest.TestAllTypes.optional_fixed32)
  return optional_fixed32_;
}
void TestAllTypes::set_optional_fixed32(::google::protobuf::uint32 value) {
  
  optional_fixed32_ = value;
  // @@protoc_insertion_point(field_set:proto3_arena_lite_unittest.TestAllTypes.optional_fixed32)
}

// optional fixed64 optional_fixed64 = 8;
void TestAllTypes::clear_optional_fixed64() {
  optional_fixed64_ = GOOGLE_ULONGLONG(0);
}
::google::protobuf::uint64 TestAllTypes::optional_fixed64() const {
  // @@protoc_insertion_point(field_get:proto3_arena_lite_unittest.TestAllTypes.optional_fixed64)
  return optional_fixed64_;
}
void TestAllTypes::set_optional_fixed64(::google::protobuf::uint64 value) {
  
  optional_fixed64_ = value;
  // @@protoc_insertion_point(field_set:proto3_arena_lite_unittest.TestAllTypes.optional_fixed64)
}

// optional sfixed32 optional_sfixed32 = 9;
void TestAllTypes::clear_optional_sfixed32() {
  optional_sfixed32_ = 0;
}
::google::protobuf::int32 TestAllTypes::optional_sfixed32() const {
  // @@protoc_insertion_point(field_get:proto3_arena_lite_unittest.TestAllTypes.optional_sfixed32)
  return optional_sfixed32_;
}
void TestAllTypes::set_optional_sfixed32(::google::protobuf::int32 value) {
  
  optional_sfixed32_ = value;
  // @@protoc_insertion_point(field_set:proto3_arena_lite_unittest.TestAllTypes.optional_sfixed32)
}

// optional sfixed64 optional_sfixed64 = 10;
void TestAllTypes::clear_optional_sfixed64() {
  optional_sfixed64_ = GOOGLE_LONGLONG(0);
}
::google::protobuf::int64 TestAllTypes::optional_sfixed64() const {
  // @@protoc_insertion_point(field_get:proto3_arena_lite_unittest.TestAllTypes.optional_sfixed64)
  return optional_sfixed64_;
}
void TestAllTypes::set_optional_sfixed64(::google::protobuf::int64 value) {
  
  optional_sfixed64_ = value;
  // @@protoc_insertion_point(field_set:proto3_arena_lite_unittest.TestAllTypes.optional_sfixed64)
}

// optional float optional_float = 11;
void TestAllTypes::clear_optional_float() {
  optional_float_ = 0;
}
float TestAllTypes::optional_float() const {
  // @@protoc_insertion_point(field_get:proto3_arena_lite_unittest.TestAllTypes.optional_float)
  return optional_float_;
}
void TestAllTypes::set_optional_float(float value) {
  
  optional_float_ = value;
  // @@protoc_insertion_point(field_set:proto3_arena_lite_unittest.TestAllTypes.optional_float)
}

// optional double optional_double = 12;
void TestAllTypes::clear_optional_double() {
  optional_double_ = 0;
}
double TestAllTypes::optional_double() const {
  // @@protoc_insertion_point(field_get:proto3_arena_lite_unittest.TestAllTypes.optional_double)
  return optional_double_;
}
void TestAllTypes::set_optional_double(double value) {
  
  optional_double_ = value;
  // @@protoc_insertion_point(field_set:proto3_arena_lite_unittest.TestAllTypes.optional_double)
}

// optional bool optional_bool = 13;
void TestAllTypes::clear_optional_bool() {
  optional_bool_ = false;
}
bool TestAllTypes::optional_bool() const {
  // @@protoc_insertion_point(field_get:proto3_arena_lite_unittest.TestAllTypes.optional_bool)
  return optional_bool_;
}
void TestAllTypes::set_optional_bool(bool value) {
  
  optional_bool_ = value;
  // @@protoc_insertion_point(field_set:proto3_arena_lite_unittest.TestAllTypes.optional_bool)
}

// optional string optional_string = 14;
void TestAllTypes::clear_optional_string() {
  optional_string_.ClearToEmpty(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
const ::std::string& TestAllTypes::optional_string() const {
  // @@protoc_insertion_point(field_get:proto3_arena_lite_unittest.TestAllTypes.optional_string)
  return optional_string_.Get(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
void TestAllTypes::set_optional_string(const ::std::string& value) {
  
  optional_string_.Set(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value, GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set:proto3_arena_lite_unittest.TestAllTypes.optional_string)
}
void TestAllTypes::set_optional_string(const char* value) {
  
  optional_string_.Set(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value),
              GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_char:proto3_arena_lite_unittest.TestAllTypes.optional_string)
}
void TestAllTypes::set_optional_string(const char* value,
    size_t size) {
  
  optional_string_.Set(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(
      reinterpret_cast<const char*>(value), size), GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_pointer:proto3_arena_lite_unittest.TestAllTypes.optional_string)
}
::std::string* TestAllTypes::mutable_optional_string() {
  
  // @@protoc_insertion_point(field_mutable:proto3_arena_lite_unittest.TestAllTypes.optional_string)
  return optional_string_.Mutable(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
::std::string* TestAllTypes::release_optional_string() {
  // @@protoc_insertion_point(field_release:proto3_arena_lite_unittest.TestAllTypes.optional_string)
  
  return optional_string_.Release(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
::std::string* TestAllTypes::unsafe_arena_release_optional_string() {
  // @@protoc_insertion_point(field_unsafe_arena_release:proto3_arena_lite_unittest.TestAllTypes.optional_string)
  GOOGLE_DCHECK(GetArenaNoVirtual() != NULL);
  
  return optional_string_.UnsafeArenaRelease(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      GetArenaNoVirtual());
}
void TestAllTypes::set_allocated_optional_string(::std::string* optional_string) {
  if (optional_string != NULL) {
    
  } else {
    
  }
  optional_string_.SetAllocated(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), optional_string,
      GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_allocated:proto3_arena_lite_unittest.TestAllTypes.optional_string)
}
void TestAllTypes::unsafe_arena_set_allocated_optional_string(
    ::std::string* optional_string) {
  GOOGLE_DCHECK(GetArenaNoVirtual() != NULL);
  if (optional_string != NULL) {
    
  } else {
    
  }
  optional_string_.UnsafeArenaSetAllocated(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      optional_string, GetArenaNoVirtual());
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:proto3_arena_lite_unittest.TestAllTypes.optional_string)
}

// optional bytes optional_bytes = 15;
void TestAllTypes::clear_optional_bytes() {
  optional_bytes_.ClearToEmpty(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
const ::std::string& TestAllTypes::optional_bytes() const {
  // @@protoc_insertion_point(field_get:proto3_arena_lite_unittest.TestAllTypes.optional_bytes)
  return optional_bytes_.Get(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
void TestAllTypes::set_optional_bytes(const ::std::string& value) {
  
  optional_bytes_.Set(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value, GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set:proto3_arena_lite_unittest.TestAllTypes.optional_bytes)
}
void TestAllTypes::set_optional_bytes(const char* value) {
  
  optional_bytes_.Set(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value),
              GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_char:proto3_arena_lite_unittest.TestAllTypes.optional_bytes)
}
void TestAllTypes::set_optional_bytes(const void* value,
    size_t size) {
  
  optional_bytes_.Set(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(
      reinterpret_cast<const char*>(value), size), GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_pointer:proto3_arena_lite_unittest.TestAllTypes.optional_bytes)
}
::std::string* TestAllTypes::mutable_optional_bytes() {
  
  // @@protoc_insertion_point(field_mutable:proto3_arena_lite_unittest.TestAllTypes.optional_bytes)
  return optional_bytes_.Mutable(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
::std::string* TestAllTypes::release_optional_bytes() {
  // @@protoc_insertion_point(field_release:proto3_arena_lite_unittest.TestAllTypes.optional_bytes)
  
  return optional_bytes_.Release(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
::std::string* TestAllTypes::unsafe_arena_release_optional_bytes() {
  // @@protoc_insertion_point(field_unsafe_arena_release:proto3_arena_lite_unittest.TestAllTypes.optional_bytes)
  GOOGLE_DCHECK(GetArenaNoVirtual() != NULL);
  
  return optional_bytes_.UnsafeArenaRelease(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      GetArenaNoVirtual());
}
void TestAllTypes::set_allocated_optional_bytes(::std::string* optional_bytes) {
  if (optional_bytes != NULL) {
    
  } else {
    
  }
  optional_bytes_.SetAllocated(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), optional_bytes,
      GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_allocated:proto3_arena_lite_unittest.TestAllTypes.optional_bytes)
}
void TestAllTypes::unsafe_arena_set_allocated_optional_bytes(
    ::std::string* optional_bytes) {
  GOOGLE_DCHECK(GetArenaNoVirtual() != NULL);
  if (optional_bytes != NULL) {
    
  } else {
    
  }
  optional_bytes_.UnsafeArenaSetAllocated(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      optional_bytes, GetArenaNoVirtual());
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:proto3_arena_lite_unittest.TestAllTypes.optional_bytes)
}

// optional .proto3_arena_lite_unittest.TestAllTypes.NestedMessage optional_nested_message = 18;
bool TestAllTypes::has_optional_nested_message() const {
  return this != internal_default_instance() && optional_nested_message_ != NULL;
}
void TestAllTypes::clear_optional_nested_message() {
  if (GetArenaNoVirtual() == NULL && optional_nested_message_ != NULL) delete optional_nested_message_;
  optional_nested_message_ = NULL;
}
const ::proto3_arena_lite_unittest::TestAllTypes_NestedMessage& TestAllTypes::optional_nested_message() const {
  // @@protoc_insertion_point(field_get:proto3_arena_lite_unittest.TestAllTypes.optional_nested_message)
  return optional_nested_message_ != NULL ? *optional_nested_message_
                         : *::proto3_arena_lite_unittest::TestAllTypes_NestedMessage::internal_default_instance();
}
::proto3_arena_lite_unittest::TestAllTypes_NestedMessage* TestAllTypes::mutable_optional_nested_message() {
  
  if (optional_nested_message_ == NULL) {
    _slow_mutable_optional_nested_message();
  }
  // @@protoc_insertion_point(field_mutable:proto3_arena_lite_unittest.TestAllTypes.optional_nested_message)
  return optional_nested_message_;
}
::proto3_arena_lite_unittest::TestAllTypes_NestedMessage* TestAllTypes::release_optional_nested_message() {
  // @@protoc_insertion_point(field_release:proto3_arena_lite_unittest.TestAllTypes.optional_nested_message)
  
  if (GetArenaNoVirtual() != NULL) {
    return _slow_release_optional_nested_message();
  } else {
    ::proto3_arena_lite_unittest::TestAllTypes_NestedMessage* temp = optional_nested_message_;
    optional_nested_message_ = NULL;
    return temp;
  }
}
 void TestAllTypes::set_allocated_optional_nested_message(::proto3_arena_lite_unittest::TestAllTypes_NestedMessage* optional_nested_message) {
  ::google::protobuf::Arena* message_arena = GetArenaNoVirtual();
  if (message_arena == NULL) {
    delete optional_nested_message_;
  }
  if (optional_nested_message != NULL) {
    _slow_set_allocated_optional_nested_message(message_arena, &optional_nested_message);
  }
  optional_nested_message_ = optional_nested_message;
  if (optional_nested_message) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_set_allocated:proto3_arena_lite_unittest.TestAllTypes.optional_nested_message)
}

// optional .proto3_arena_lite_unittest.ForeignMessage optional_foreign_message = 19;
bool TestAllTypes::has_optional_foreign_message() const {
  return this != internal_default_instance() && optional_foreign_message_ != NULL;
}
void TestAllTypes::clear_optional_foreign_message() {
  if (GetArenaNoVirtual() == NULL && optional_foreign_message_ != NULL) delete optional_foreign_message_;
  optional_foreign_message_ = NULL;
}
const ::proto3_arena_lite_unittest::ForeignMessage& TestAllTypes::optional_foreign_message() const {
  // @@protoc_insertion_point(field_get:proto3_arena_lite_unittest.TestAllTypes.optional_foreign_message)
  return optional_foreign_message_ != NULL ? *optional_foreign_message_
                         : *::proto3_arena_lite_unittest::ForeignMessage::internal_default_instance();
}
::proto3_arena_lite_unittest::ForeignMessage* TestAllTypes::mutable_optional_foreign_message() {
  
  if (optional_foreign_message_ == NULL) {
    _slow_mutable_optional_foreign_message();
  }
  // @@protoc_insertion_point(field_mutable:proto3_arena_lite_unittest.TestAllTypes.optional_foreign_message)
  return optional_foreign_message_;
}
::proto3_arena_lite_unittest::ForeignMessage* TestAllTypes::release_optional_foreign_message() {
  // @@protoc_insertion_point(field_release:proto3_arena_lite_unittest.TestAllTypes.optional_foreign_message)
  
  if (GetArenaNoVirtual() != NULL) {
    return _slow_release_optional_foreign_message();
  } else {
    ::proto3_arena_lite_unittest::ForeignMessage* temp = optional_foreign_message_;
    optional_foreign_message_ = NULL;
    return temp;
  }
}
 void TestAllTypes::set_allocated_optional_foreign_message(::proto3_arena_lite_unittest::ForeignMessage* optional_foreign_message) {
  ::google::protobuf::Arena* message_arena = GetArenaNoVirtual();
  if (message_arena == NULL) {
    delete optional_foreign_message_;
  }
  if (optional_foreign_message != NULL) {
    _slow_set_allocated_optional_foreign_message(message_arena, &optional_foreign_message);
  }
  optional_foreign_message_ = optional_foreign_message;
  if (optional_foreign_message) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_set_allocated:proto3_arena_lite_unittest.TestAllTypes.optional_foreign_message)
}

// optional .protobuf_unittest_import.ImportMessage optional_import_message = 20;
bool TestAllTypes::has_optional_import_message() const {
  return this != internal_default_instance() && optional_import_message_ != NULL;
}
void TestAllTypes::clear_optional_import_message() {
  if (GetArenaNoVirtual() == NULL && optional_import_message_ != NULL) delete optional_import_message_;
  optional_import_message_ = NULL;
}
const ::protobuf_unittest_import::ImportMessage& TestAllTypes::optional_import_message() const {
  // @@protoc_insertion_point(field_get:proto3_arena_lite_unittest.TestAllTypes.optional_import_message)
  return optional_import_message_ != NULL ? *optional_import_message_
                         : *::protobuf_unittest_import::ImportMessage::internal_default_instance();
}
::protobuf_unittest_import::ImportMessage* TestAllTypes::mutable_optional_import_message() {
  
  if (optional_import_message_ == NULL) {
    _slow_mutable_optional_import_message();
  }
  // @@protoc_insertion_point(field_mutable:proto3_arena_lite_unittest.TestAllTypes.optional_import_message)
  return optional_import_message_;
}
::protobuf_unittest_import::ImportMessage* TestAllTypes::release_optional_import_message() {
  // @@protoc_insertion_point(field_release:proto3_arena_lite_unittest.TestAllTypes.optional_import_message)
  
  if (GetArenaNoVirtual() != NULL) {
    return _slow_release_optional_import_message();
  } else {
    ::protobuf_unittest_import::ImportMessage* temp = optional_import_message_;
    optional_import_message_ = NULL;
    return temp;
  }
}
 void TestAllTypes::set_allocated_optional_import_message(::protobuf_unittest_import::ImportMessage* optional_import_message) {
  ::google::protobuf::Arena* message_arena = GetArenaNoVirtual();
  if (message_arena == NULL) {
    delete optional_import_message_;
  }
  if (optional_import_message != NULL) {
    _slow_set_allocated_optional_import_message(message_arena, &optional_import_message);
  }
  optional_import_message_ = optional_import_message;
  if (optional_import_message) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_set_allocated:proto3_arena_lite_unittest.TestAllTypes.optional_import_message)
}

// optional .proto3_arena_lite_unittest.TestAllTypes.NestedEnum optional_nested_enum = 21;
void TestAllTypes::clear_optional_nested_enum() {
  optional_nested_enum_ = 0;
}
::proto3_arena_lite_unittest::TestAllTypes_NestedEnum TestAllTypes::optional_nested_enum() const {
  // @@protoc_insertion_point(field_get:proto3_arena_lite_unittest.TestAllTypes.optional_nested_enum)
  return static_cast< ::proto3_arena_lite_unittest::TestAllTypes_NestedEnum >(optional_nested_enum_);
}
void TestAllTypes::set_optional_nested_enum(::proto3_arena_lite_unittest::TestAllTypes_NestedEnum value) {
  
  optional_nested_enum_ = value;
  // @@protoc_insertion_point(field_set:proto3_arena_lite_unittest.TestAllTypes.optional_nested_enum)
}

// optional .proto3_arena_lite_unittest.ForeignEnum optional_foreign_enum = 22;
void TestAllTypes::clear_optional_foreign_enum() {
  optional_foreign_enum_ = 0;
}
::proto3_arena_lite_unittest::ForeignEnum TestAllTypes::optional_foreign_enum() const {
  // @@protoc_insertion_point(field_get:proto3_arena_lite_unittest.TestAllTypes.optional_foreign_enum)
  return static_cast< ::proto3_arena_lite_unittest::ForeignEnum >(optional_foreign_enum_);
}
void TestAllTypes::set_optional_foreign_enum(::proto3_arena_lite_unittest::ForeignEnum value) {
  
  optional_foreign_enum_ = value;
  // @@protoc_insertion_point(field_set:proto3_arena_lite_unittest.TestAllTypes.optional_foreign_enum)
}

// optional string optional_string_piece = 24 [ctype = STRING_PIECE];
void TestAllTypes::clear_optional_string_piece() {
  optional_string_piece_.ClearToEmpty(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
const ::std::string& TestAllTypes::optional_string_piece() const {
  // @@protoc_insertion_point(field_get:proto3_arena_lite_unittest.TestAllTypes.optional_string_piece)
  return optional_string_piece_.Get(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
void TestAllTypes::set_optional_string_piece(const ::std::string& value) {
  
  optional_string_piece_.Set(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value, GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set:proto3_arena_lite_unittest.TestAllTypes.optional_string_piece)
}
void TestAllTypes::set_optional_string_piece(const char* value) {
  
  optional_string_piece_.Set(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value),
              GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_char:proto3_arena_lite_unittest.TestAllTypes.optional_string_piece)
}
void TestAllTypes::set_optional_string_piece(const char* value,
    size_t size) {
  
  optional_string_piece_.Set(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(
      reinterpret_cast<const char*>(value), size), GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_pointer:proto3_arena_lite_unittest.TestAllTypes.optional_string_piece)
}
::std::string* TestAllTypes::mutable_optional_string_piece() {
  
  // @@protoc_insertion_point(field_mutable:proto3_arena_lite_unittest.TestAllTypes.optional_string_piece)
  return optional_string_piece_.Mutable(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
::std::string* TestAllTypes::release_optional_string_piece() {
  // @@protoc_insertion_point(field_release:proto3_arena_lite_unittest.TestAllTypes.optional_string_piece)
  
  return optional_string_piece_.Release(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
::std::string* TestAllTypes::unsafe_arena_release_optional_string_piece() {
  // @@protoc_insertion_point(field_unsafe_arena_release:proto3_arena_lite_unittest.TestAllTypes.optional_string_piece)
  GOOGLE_DCHECK(GetArenaNoVirtual() != NULL);
  
  return optional_string_piece_.UnsafeArenaRelease(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      GetArenaNoVirtual());
}
void TestAllTypes::set_allocated_optional_string_piece(::std::string* optional_string_piece) {
  if (optional_string_piece != NULL) {
    
  } else {
    
  }
  optional_string_piece_.SetAllocated(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), optional_string_piece,
      GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_allocated:proto3_arena_lite_unittest.TestAllTypes.optional_string_piece)
}
void TestAllTypes::unsafe_arena_set_allocated_optional_string_piece(
    ::std::string* optional_string_piece) {
  GOOGLE_DCHECK(GetArenaNoVirtual() != NULL);
  if (optional_string_piece != NULL) {
    
  } else {
    
  }
  optional_string_piece_.UnsafeArenaSetAllocated(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      optional_string_piece, GetArenaNoVirtual());
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:proto3_arena_lite_unittest.TestAllTypes.optional_string_piece)
}

// optional string optional_cord = 25 [ctype = CORD];
void TestAllTypes::clear_optional_cord() {
  optional_cord_.ClearToEmpty(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
const ::std::string& TestAllTypes::optional_cord() const {
  // @@protoc_insertion_point(field_get:proto3_arena_lite_unittest.TestAllTypes.optional_cord)
  return optional_cord_.Get(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
void TestAllTypes::set_optional_cord(const ::std::string& value) {
  
  optional_cord_.Set(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value, GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set:proto3_arena_lite_unittest.TestAllTypes.optional_cord)
}
void TestAllTypes::set_optional_cord(const char* value) {
  
  optional_cord_.Set(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value),
              GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_char:proto3_arena_lite_unittest.TestAllTypes.optional_cord)
}
void TestAllTypes::set_optional_cord(const char* value,
    size_t size) {
  
  optional_cord_.Set(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(
      reinterpret_cast<const char*>(value), size), GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_pointer:proto3_arena_lite_unittest.TestAllTypes.optional_cord)
}
::std::string* TestAllTypes::mutable_optional_cord() {
  
  // @@protoc_insertion_point(field_mutable:proto3_arena_lite_unittest.TestAllTypes.optional_cord)
  return optional_cord_.Mutable(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
::std::string* TestAllTypes::release_optional_cord() {
  // @@protoc_insertion_point(field_release:proto3_arena_lite_unittest.TestAllTypes.optional_cord)
  
  return optional_cord_.Release(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
::std::string* TestAllTypes::unsafe_arena_release_optional_cord() {
  // @@protoc_insertion_point(field_unsafe_arena_release:proto3_arena_lite_unittest.TestAllTypes.optional_cord)
  GOOGLE_DCHECK(GetArenaNoVirtual() != NULL);
  
  return optional_cord_.UnsafeArenaRelease(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      GetArenaNoVirtual());
}
void TestAllTypes::set_allocated_optional_cord(::std::string* optional_cord) {
  if (optional_cord != NULL) {
    
  } else {
    
  }
  optional_cord_.SetAllocated(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), optional_cord,
      GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_allocated:proto3_arena_lite_unittest.TestAllTypes.optional_cord)
}
void TestAllTypes::unsafe_arena_set_allocated_optional_cord(
    ::std::string* optional_cord) {
  GOOGLE_DCHECK(GetArenaNoVirtual() != NULL);
  if (optional_cord != NULL) {
    
  } else {
    
  }
  optional_cord_.UnsafeArenaSetAllocated(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      optional_cord, GetArenaNoVirtual());
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:proto3_arena_lite_unittest.TestAllTypes.optional_cord)
}

// optional .protobuf_unittest_import.PublicImportMessage optional_public_import_message = 26;
bool TestAllTypes::has_optional_public_import_message() const {
  return this != internal_default_instance() && optional_public_import_message_ != NULL;
}
void TestAllTypes::clear_optional_public_import_message() {
  if (GetArenaNoVirtual() == NULL && optional_public_import_message_ != NULL) delete optional_public_import_message_;
  optional_public_import_message_ = NULL;
}
const ::protobuf_unittest_import::PublicImportMessage& TestAllTypes::optional_public_import_message() const {
  // @@protoc_insertion_point(field_get:proto3_arena_lite_unittest.TestAllTypes.optional_public_import_message)
  return optional_public_import_message_ != NULL ? *optional_public_import_message_
                         : *::protobuf_unittest_import::PublicImportMessage::internal_default_instance();
}
::protobuf_unittest_import::PublicImportMessage* TestAllTypes::mutable_optional_public_import_message() {
  
  if (optional_public_import_message_ == NULL) {
    _slow_mutable_optional_public_import_message();
  }
  // @@protoc_insertion_point(field_mutable:proto3_arena_lite_unittest.TestAllTypes.optional_public_import_message)
  return optional_public_import_message_;
}
::protobuf_unittest_import::PublicImportMessage* TestAllTypes::release_optional_public_import_message() {
  // @@protoc_insertion_point(field_release:proto3_arena_lite_unittest.TestAllTypes.optional_public_import_message)
  
  if (GetArenaNoVirtual() != NULL) {
    return _slow_release_optional_public_import_message();
  } else {
    ::protobuf_unittest_import::PublicImportMessage* temp = optional_public_import_message_;
    optional_public_import_message_ = NULL;
    return temp;
  }
}
 void TestAllTypes::set_allocated_optional_public_import_message(::protobuf_unittest_import::PublicImportMessage* optional_public_import_message) {
  ::google::protobuf::Arena* message_arena = GetArenaNoVirtual();
  if (message_arena == NULL) {
    delete optional_public_import_message_;
  }
  if (optional_public_import_message != NULL) {
    if (message_arena != NULL) {
      message_arena->Own(optional_public_import_message);
    }
  }
  optional_public_import_message_ = optional_public_import_message;
  if (optional_public_import_message) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_set_allocated:proto3_arena_lite_unittest.TestAllTypes.optional_public_import_message)
}

// optional .proto3_arena_lite_unittest.TestAllTypes.NestedMessage optional_lazy_message = 27 [lazy = true];
bool TestAllTypes::has_optional_lazy_message() const {
  return this != internal_default_instance() && optional_lazy_message_ != NULL;
}
void TestAllTypes::clear_optional_lazy_message() {
  if (GetArenaNoVirtual() == NULL && optional_lazy_message_ != NULL) delete optional_lazy_message_;
  optional_lazy_message_ = NULL;
}
const ::proto3_arena_lite_unittest::TestAllTypes_NestedMessage& TestAllTypes::optional_lazy_message() const {
  // @@protoc_insertion_point(field_get:proto3_arena_lite_unittest.TestAllTypes.optional_lazy_message)
  return optional_lazy_message_ != NULL ? *optional_lazy_message_
                         : *::proto3_arena_lite_unittest::TestAllTypes_NestedMessage::internal_default_instance();
}
::proto3_arena_lite_unittest::TestAllTypes_NestedMessage* TestAllTypes::mutable_optional_lazy_message() {
  
  if (optional_lazy_message_ == NULL) {
    _slow_mutable_optional_lazy_message();
  }
  // @@protoc_insertion_point(field_mutable:proto3_arena_lite_unittest.TestAllTypes.optional_lazy_message)
  return optional_lazy_message_;
}
::proto3_arena_lite_unittest::TestAllTypes_NestedMessage* TestAllTypes::release_optional_lazy_message() {
  // @@protoc_insertion_point(field_release:proto3_arena_lite_unittest.TestAllTypes.optional_lazy_message)
  
  if (GetArenaNoVirtual() != NULL) {
    return _slow_release_optional_lazy_message();
  } else {
    ::proto3_arena_lite_unittest::TestAllTypes_NestedMessage* temp = optional_lazy_message_;
    optional_lazy_message_ = NULL;
    return temp;
  }
}
 void TestAllTypes::set_allocated_optional_lazy_message(::proto3_arena_lite_unittest::TestAllTypes_NestedMessage* optional_lazy_message) {
  ::google::protobuf::Arena* message_arena = GetArenaNoVirtual();
  if (message_arena == NULL) {
    delete optional_lazy_message_;
  }
  if (optional_lazy_message != NULL) {
    _slow_set_allocated_optional_lazy_message(message_arena, &optional_lazy_message);
  }
  optional_lazy_message_ = optional_lazy_message;
  if (optional_lazy_message) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_set_allocated:proto3_arena_lite_unittest.TestAllTypes.optional_lazy_message)
}

// repeated int32 repeated_int32 = 31;
int TestAllTypes::repeated_int32_size() const {
  return repeated_int32_.size();
}
void TestAllTypes::clear_repeated_int32() {
  repeated_int32_.Clear();
}
::google::protobuf::int32 TestAllTypes::repeated_int32(int index) const {
  // @@protoc_insertion_point(field_get:proto3_arena_lite_unittest.TestAllTypes.repeated_int32)
  return repeated_int32_.Get(index);
}
void TestAllTypes::set_repeated_int32(int index, ::google::protobuf::int32 value) {
  repeated_int32_.Set(index, value);
  // @@protoc_insertion_point(field_set:proto3_arena_lite_unittest.TestAllTypes.repeated_int32)
}
void TestAllTypes::add_repeated_int32(::google::protobuf::int32 value) {
  repeated_int32_.Add(value);
  // @@protoc_insertion_point(field_add:proto3_arena_lite_unittest.TestAllTypes.repeated_int32)
}
const ::google::protobuf::RepeatedField< ::google::protobuf::int32 >&
TestAllTypes::repeated_int32() const {
  // @@protoc_insertion_point(field_list:proto3_arena_lite_unittest.TestAllTypes.repeated_int32)
  return repeated_int32_;
}
::google::protobuf::RepeatedField< ::google::protobuf::int32 >*
TestAllTypes::mutable_repeated_int32() {
  // @@protoc_insertion_point(field_mutable_list:proto3_arena_lite_unittest.TestAllTypes.repeated_int32)
  return &repeated_int32_;
}

// repeated int64 repeated_int64 = 32;
int TestAllTypes::repeated_int64_size() const {
  return repeated_int64_.size();
}
void TestAllTypes::clear_repeated_int64() {
  repeated_int64_.Clear();
}
::google::protobuf::int64 TestAllTypes::repeated_int64(int index) const {
  // @@protoc_insertion_point(field_get:proto3_arena_lite_unittest.TestAllTypes.repeated_int64)
  return repeated_int64_.Get(index);
}
void TestAllTypes::set_repeated_int64(int index, ::google::protobuf::int64 value) {
  repeated_int64_.Set(index, value);
  // @@protoc_insertion_point(field_set:proto3_arena_lite_unittest.TestAllTypes.repeated_int64)
}
void TestAllTypes::add_repeated_int64(::google::protobuf::int64 value) {
  repeated_int64_.Add(value);
  // @@protoc_insertion_point(field_add:proto3_arena_lite_unittest.TestAllTypes.repeated_int64)
}
const ::google::protobuf::RepeatedField< ::google::protobuf::int64 >&
TestAllTypes::repeated_int64() const {
  // @@protoc_insertion_point(field_list:proto3_arena_lite_unittest.TestAllTypes.repeated_int64)
  return repeated_int64_;
}
::google::protobuf::RepeatedField< ::google::protobuf::int64 >*
TestAllTypes::mutable_repeated_int64() {
  // @@protoc_insertion_point(field_mutable_list:proto3_arena_lite_unittest.TestAllTypes.repeated_int64)
  return &repeated_int64_;
}

// repeated uint32 repeated_uint32 = 33;
int TestAllTypes::repeated_uint32_size() const {
  return repeated_uint32_.size();
}
void TestAllTypes::clear_repeated_uint32() {
  repeated_uint32_.Clear();
}
::google::protobuf::uint32 TestAllTypes::repeated_uint32(int index) const {
  // @@protoc_insertion_point(field_get:proto3_arena_lite_unittest.TestAllTypes.repeated_uint32)
  return repeated_uint32_.Get(index);
}
void TestAllTypes::set_repeated_uint32(int index, ::google::protobuf::uint32 value) {
  repeated_uint32_.Set(index, value);
  // @@protoc_insertion_point(field_set:proto3_arena_lite_unittest.TestAllTypes.repeated_uint32)
}
void TestAllTypes::add_repeated_uint32(::google::protobuf::uint32 value) {
  repeated_uint32_.Add(value);
  // @@protoc_insertion_point(field_add:proto3_arena_lite_unittest.TestAllTypes.repeated_uint32)
}
const ::google::protobuf::RepeatedField< ::google::protobuf::uint32 >&
TestAllTypes::repeated_uint32() const {
  // @@protoc_insertion_point(field_list:proto3_arena_lite_unittest.TestAllTypes.repeated_uint32)
  return repeated_uint32_;
}
::google::protobuf::RepeatedField< ::google::protobuf::uint32 >*
TestAllTypes::mutable_repeated_uint32() {
  // @@protoc_insertion_point(field_mutable_list:proto3_arena_lite_unittest.TestAllTypes.repeated_uint32)
  return &repeated_uint32_;
}

// repeated uint64 repeated_uint64 = 34;
int TestAllTypes::repeated_uint64_size() const {
  return repeated_uint64_.size();
}
void TestAllTypes::clear_repeated_uint64() {
  repeated_uint64_.Clear();
}
::google::protobuf::uint64 TestAllTypes::repeated_uint64(int index) const {
  // @@protoc_insertion_point(field_get:proto3_arena_lite_unittest.TestAllTypes.repeated_uint64)
  return repeated_uint64_.Get(index);
}
void TestAllTypes::set_repeated_uint64(int index, ::google::protobuf::uint64 value) {
  repeated_uint64_.Set(index, value);
  // @@protoc_insertion_point(field_set:proto3_arena_lite_unittest.TestAllTypes.repeated_uint64)
}
void TestAllTypes::add_repeated_uint64(::google::protobuf::uint64 value) {
  repeated_uint64_.Add(value);
  // @@protoc_insertion_point(field_add:proto3_arena_lite_unittest.TestAllTypes.repeated_uint64)
}
const ::google::protobuf::RepeatedField< ::google::protobuf::uint64 >&
TestAllTypes::repeated_uint64() const {
  // @@protoc_insertion_point(field_list:proto3_arena_lite_unittest.TestAllTypes.repeated_uint64)
  return repeated_uint64_;
}
::google::protobuf::RepeatedField< ::google::protobuf::uint64 >*
TestAllTypes::mutable_repeated_uint64() {
  // @@protoc_insertion_point(field_mutable_list:proto3_arena_lite_unittest.TestAllTypes.repeated_uint64)
  return &repeated_uint64_;
}

// repeated sint32 repeated_sint32 = 35;
int TestAllTypes::repeated_sint32_size() const {
  return repeated_sint32_.size();
}
void TestAllTypes::clear_repeated_sint32() {
  repeated_sint32_.Clear();
}
::google::protobuf::int32 TestAllTypes::repeated_sint32(int index) const {
  // @@protoc_insertion_point(field_get:proto3_arena_lite_unittest.TestAllTypes.repeated_sint32)
  return repeated_sint32_.Get(index);
}
void TestAllTypes::set_repeated_sint32(int index, ::google::protobuf::int32 value) {
  repeated_sint32_.Set(index, value);
  // @@protoc_insertion_point(field_set:proto3_arena_lite_unittest.TestAllTypes.repeated_sint32)
}
void TestAllTypes::add_repeated_sint32(::google::protobuf::int32 value) {
  repeated_sint32_.Add(value);
  // @@protoc_insertion_point(field_add:proto3_arena_lite_unittest.TestAllTypes.repeated_sint32)
}
const ::google::protobuf::RepeatedField< ::google::protobuf::int32 >&
TestAllTypes::repeated_sint32() const {
  // @@protoc_insertion_point(field_list:proto3_arena_lite_unittest.TestAllTypes.repeated_sint32)
  return repeated_sint32_;
}
::google::protobuf::RepeatedField< ::google::protobuf::int32 >*
TestAllTypes::mutable_repeated_sint32() {
  // @@protoc_insertion_point(field_mutable_list:proto3_arena_lite_unittest.TestAllTypes.repeated_sint32)
  return &repeated_sint32_;
}

// repeated sint64 repeated_sint64 = 36;
int TestAllTypes::repeated_sint64_size() const {
  return repeated_sint64_.size();
}
void TestAllTypes::clear_repeated_sint64() {
  repeated_sint64_.Clear();
}
::google::protobuf::int64 TestAllTypes::repeated_sint64(int index) const {
  // @@protoc_insertion_point(field_get:proto3_arena_lite_unittest.TestAllTypes.repeated_sint64)
  return repeated_sint64_.Get(index);
}
void TestAllTypes::set_repeated_sint64(int index, ::google::protobuf::int64 value) {
  repeated_sint64_.Set(index, value);
  // @@protoc_insertion_point(field_set:proto3_arena_lite_unittest.TestAllTypes.repeated_sint64)
}
void TestAllTypes::add_repeated_sint64(::google::protobuf::int64 value) {
  repeated_sint64_.Add(value);
  // @@protoc_insertion_point(field_add:proto3_arena_lite_unittest.TestAllTypes.repeated_sint64)
}
const ::google::protobuf::RepeatedField< ::google::protobuf::int64 >&
TestAllTypes::repeated_sint64() const {
  // @@protoc_insertion_point(field_list:proto3_arena_lite_unittest.TestAllTypes.repeated_sint64)
  return repeated_sint64_;
}
::google::protobuf::RepeatedField< ::google::protobuf::int64 >*
TestAllTypes::mutable_repeated_sint64() {
  // @@protoc_insertion_point(field_mutable_list:proto3_arena_lite_unittest.TestAllTypes.repeated_sint64)
  return &repeated_sint64_;
}

// repeated fixed32 repeated_fixed32 = 37;
int TestAllTypes::repeated_fixed32_size() const {
  return repeated_fixed32_.size();
}
void TestAllTypes::clear_repeated_fixed32() {
  repeated_fixed32_.Clear();
}
::google::protobuf::uint32 TestAllTypes::repeated_fixed32(int index) const {
  // @@protoc_insertion_point(field_get:proto3_arena_lite_unittest.TestAllTypes.repeated_fixed32)
  return repeated_fixed32_.Get(index);
}
void TestAllTypes::set_repeated_fixed32(int index, ::google::protobuf::uint32 value) {
  repeated_fixed32_.Set(index, value);
  // @@protoc_insertion_point(field_set:proto3_arena_lite_unittest.TestAllTypes.repeated_fixed32)
}
void TestAllTypes::add_repeated_fixed32(::google::protobuf::uint32 value) {
  repeated_fixed32_.Add(value);
  // @@protoc_insertion_point(field_add:proto3_arena_lite_unittest.TestAllTypes.repeated_fixed32)
}
const ::google::protobuf::RepeatedField< ::google::protobuf::uint32 >&
TestAllTypes::repeated_fixed32() const {
  // @@protoc_insertion_point(field_list:proto3_arena_lite_unittest.TestAllTypes.repeated_fixed32)
  return repeated_fixed32_;
}
::google::protobuf::RepeatedField< ::google::protobuf::uint32 >*
TestAllTypes::mutable_repeated_fixed32() {
  // @@protoc_insertion_point(field_mutable_list:proto3_arena_lite_unittest.TestAllTypes.repeated_fixed32)
  return &repeated_fixed32_;
}

// repeated fixed64 repeated_fixed64 = 38;
int TestAllTypes::repeated_fixed64_size() const {
  return repeated_fixed64_.size();
}
void TestAllTypes::clear_repeated_fixed64() {
  repeated_fixed64_.Clear();
}
::google::protobuf::uint64 TestAllTypes::repeated_fixed64(int index) const {
  // @@protoc_insertion_point(field_get:proto3_arena_lite_unittest.TestAllTypes.repeated_fixed64)
  return repeated_fixed64_.Get(index);
}
void TestAllTypes::set_repeated_fixed64(int index, ::google::protobuf::uint64 value) {
  repeated_fixed64_.Set(index, value);
  // @@protoc_insertion_point(field_set:proto3_arena_lite_unittest.TestAllTypes.repeated_fixed64)
}
void TestAllTypes::add_repeated_fixed64(::google::protobuf::uint64 value) {
  repeated_fixed64_.Add(value);
  // @@protoc_insertion_point(field_add:proto3_arena_lite_unittest.TestAllTypes.repeated_fixed64)
}
const ::google::protobuf::RepeatedField< ::google::protobuf::uint64 >&
TestAllTypes::repeated_fixed64() const {
  // @@protoc_insertion_point(field_list:proto3_arena_lite_unittest.TestAllTypes.repeated_fixed64)
  return repeated_fixed64_;
}
::google::protobuf::RepeatedField< ::google::protobuf::uint64 >*
TestAllTypes::mutable_repeated_fixed64() {
  // @@protoc_insertion_point(field_mutable_list:proto3_arena_lite_unittest.TestAllTypes.repeated_fixed64)
  return &repeated_fixed64_;
}

// repeated sfixed32 repeated_sfixed32 = 39;
int TestAllTypes::repeated_sfixed32_size() const {
  return repeated_sfixed32_.size();
}
void TestAllTypes::clear_repeated_sfixed32() {
  repeated_sfixed32_.Clear();
}
::google::protobuf::int32 TestAllTypes::repeated_sfixed32(int index) const {
  // @@protoc_insertion_point(field_get:proto3_arena_lite_unittest.TestAllTypes.repeated_sfixed32)
  return repeated_sfixed32_.Get(index);
}
void TestAllTypes::set_repeated_sfixed32(int index, ::google::protobuf::int32 value) {
  repeated_sfixed32_.Set(index, value);
  // @@protoc_insertion_point(field_set:proto3_arena_lite_unittest.TestAllTypes.repeated_sfixed32)
}
void TestAllTypes::add_repeated_sfixed32(::google::protobuf::int32 value) {
  repeated_sfixed32_.Add(value);
  // @@protoc_insertion_point(field_add:proto3_arena_lite_unittest.TestAllTypes.repeated_sfixed32)
}
const ::google::protobuf::RepeatedField< ::google::protobuf::int32 >&
TestAllTypes::repeated_sfixed32() const {
  // @@protoc_insertion_point(field_list:proto3_arena_lite_unittest.TestAllTypes.repeated_sfixed32)
  return repeated_sfixed32_;
}
::google::protobuf::RepeatedField< ::google::protobuf::int32 >*
TestAllTypes::mutable_repeated_sfixed32() {
  // @@protoc_insertion_point(field_mutable_list:proto3_arena_lite_unittest.TestAllTypes.repeated_sfixed32)
  return &repeated_sfixed32_;
}

// repeated sfixed64 repeated_sfixed64 = 40;
int TestAllTypes::repeated_sfixed64_size() const {
  return repeated_sfixed64_.size();
}
void TestAllTypes::clear_repeated_sfixed64() {
  repeated_sfixed64_.Clear();
}
::google::protobuf::int64 TestAllTypes::repeated_sfixed64(int index) const {
  // @@protoc_insertion_point(field_get:proto3_arena_lite_unittest.TestAllTypes.repeated_sfixed64)
  return repeated_sfixed64_.Get(index);
}
void TestAllTypes::set_repeated_sfixed64(int index, ::google::protobuf::int64 value) {
  repeated_sfixed64_.Set(index, value);
  // @@protoc_insertion_point(field_set:proto3_arena_lite_unittest.TestAllTypes.repeated_sfixed64)
}
void TestAllTypes::add_repeated_sfixed64(::google::protobuf::int64 value) {
  repeated_sfixed64_.Add(value);
  // @@protoc_insertion_point(field_add:proto3_arena_lite_unittest.TestAllTypes.repeated_sfixed64)
}
const ::google::protobuf::RepeatedField< ::google::protobuf::int64 >&
TestAllTypes::repeated_sfixed64() const {
  // @@protoc_insertion_point(field_list:proto3_arena_lite_unittest.TestAllTypes.repeated_sfixed64)
  return repeated_sfixed64_;
}
::google::protobuf::RepeatedField< ::google::protobuf::int64 >*
TestAllTypes::mutable_repeated_sfixed64() {
  // @@protoc_insertion_point(field_mutable_list:proto3_arena_lite_unittest.TestAllTypes.repeated_sfixed64)
  return &repeated_sfixed64_;
}

// repeated float repeated_float = 41;
int TestAllTypes::repeated_float_size() const {
  return repeated_float_.size();
}
void TestAllTypes::clear_repeated_float() {
  repeated_float_.Clear();
}
float TestAllTypes::repeated_float(int index) const {
  // @@protoc_insertion_point(field_get:proto3_arena_lite_unittest.TestAllTypes.repeated_float)
  return repeated_float_.Get(index);
}
void TestAllTypes::set_repeated_float(int index, float value) {
  repeated_float_.Set(index, value);
  // @@protoc_insertion_point(field_set:proto3_arena_lite_unittest.TestAllTypes.repeated_float)
}
void TestAllTypes::add_repeated_float(float value) {
  repeated_float_.Add(value);
  // @@protoc_insertion_point(field_add:proto3_arena_lite_unittest.TestAllTypes.repeated_float)
}
const ::google::protobuf::RepeatedField< float >&
TestAllTypes::repeated_float() const {
  // @@protoc_insertion_point(field_list:proto3_arena_lite_unittest.TestAllTypes.repeated_float)
  return repeated_float_;
}
::google::protobuf::RepeatedField< float >*
TestAllTypes::mutable_repeated_float() {
  // @@protoc_insertion_point(field_mutable_list:proto3_arena_lite_unittest.TestAllTypes.repeated_float)
  return &repeated_float_;
}

// repeated double repeated_double = 42;
int TestAllTypes::repeated_double_size() const {
  return repeated_double_.size();
}
void TestAllTypes::clear_repeated_double() {
  repeated_double_.Clear();
}
double TestAllTypes::repeated_double(int index) const {
  // @@protoc_insertion_point(field_get:proto3_arena_lite_unittest.TestAllTypes.repeated_double)
  return repeated_double_.Get(index);
}
void TestAllTypes::set_repeated_double(int index, double value) {
  repeated_double_.Set(index, value);
  // @@protoc_insertion_point(field_set:proto3_arena_lite_unittest.TestAllTypes.repeated_double)
}
void TestAllTypes::add_repeated_double(double value) {
  repeated_double_.Add(value);
  // @@protoc_insertion_point(field_add:proto3_arena_lite_unittest.TestAllTypes.repeated_double)
}
const ::google::protobuf::RepeatedField< double >&
TestAllTypes::repeated_double() const {
  // @@protoc_insertion_point(field_list:proto3_arena_lite_unittest.TestAllTypes.repeated_double)
  return repeated_double_;
}
::google::protobuf::RepeatedField< double >*
TestAllTypes::mutable_repeated_double() {
  // @@protoc_insertion_point(field_mutable_list:proto3_arena_lite_unittest.TestAllTypes.repeated_double)
  return &repeated_double_;
}

// repeated bool repeated_bool = 43;
int TestAllTypes::repeated_bool_size() const {
  return repeated_bool_.size();
}
void TestAllTypes::clear_repeated_bool() {
  repeated_bool_.Clear();
}
bool TestAllTypes::repeated_bool(int index) const {
  // @@protoc_insertion_point(field_get:proto3_arena_lite_unittest.TestAllTypes.repeated_bool)
  return repeated_bool_.Get(index);
}
void TestAllTypes::set_repeated_bool(int index, bool value) {
  repeated_bool_.Set(index, value);
  // @@protoc_insertion_point(field_set:proto3_arena_lite_unittest.TestAllTypes.repeated_bool)
}
void TestAllTypes::add_repeated_bool(bool value) {
  repeated_bool_.Add(value);
  // @@protoc_insertion_point(field_add:proto3_arena_lite_unittest.TestAllTypes.repeated_bool)
}
const ::google::protobuf::RepeatedField< bool >&
TestAllTypes::repeated_bool() const {
  // @@protoc_insertion_point(field_list:proto3_arena_lite_unittest.TestAllTypes.repeated_bool)
  return repeated_bool_;
}
::google::protobuf::RepeatedField< bool >*
TestAllTypes::mutable_repeated_bool() {
  // @@protoc_insertion_point(field_mutable_list:proto3_arena_lite_unittest.TestAllTypes.repeated_bool)
  return &repeated_bool_;
}

// repeated string repeated_string = 44;
int TestAllTypes::repeated_string_size() const {
  return repeated_string_.size();
}
void TestAllTypes::clear_repeated_string() {
  repeated_string_.Clear();
}
const ::std::string& TestAllTypes::repeated_string(int index) const {
  // @@protoc_insertion_point(field_get:proto3_arena_lite_unittest.TestAllTypes.repeated_string)
  return repeated_string_.Get(index);
}
::std::string* TestAllTypes::mutable_repeated_string(int index) {
  // @@protoc_insertion_point(field_mutable:proto3_arena_lite_unittest.TestAllTypes.repeated_string)
  return repeated_string_.Mutable(index);
}
void TestAllTypes::set_repeated_string(int index, const ::std::string& value) {
  // @@protoc_insertion_point(field_set:proto3_arena_lite_unittest.TestAllTypes.repeated_string)
  repeated_string_.Mutable(index)->assign(value);
}
void TestAllTypes::set_repeated_string(int index, const char* value) {
  repeated_string_.Mutable(index)->assign(value);
  // @@protoc_insertion_point(field_set_char:proto3_arena_lite_unittest.TestAllTypes.repeated_string)
}
void TestAllTypes::set_repeated_string(int index, const char* value, size_t size) {
  repeated_string_.Mutable(index)->assign(
    reinterpret_cast<const char*>(value), size);
  // @@protoc_insertion_point(field_set_pointer:proto3_arena_lite_unittest.TestAllTypes.repeated_string)
}
::std::string* TestAllTypes::add_repeated_string() {
  // @@protoc_insertion_point(field_add_mutable:proto3_arena_lite_unittest.TestAllTypes.repeated_string)
  return repeated_string_.Add();
}
void TestAllTypes::add_repeated_string(const ::std::string& value) {
  repeated_string_.Add()->assign(value);
  // @@protoc_insertion_point(field_add:proto3_arena_lite_unittest.TestAllTypes.repeated_string)
}
void TestAllTypes::add_repeated_string(const char* value) {
  repeated_string_.Add()->assign(value);
  // @@protoc_insertion_point(field_add_char:proto3_arena_lite_unittest.TestAllTypes.repeated_string)
}
void TestAllTypes::add_repeated_string(const char* value, size_t size) {
  repeated_string_.Add()->assign(reinterpret_cast<const char*>(value), size);
  // @@protoc_insertion_point(field_add_pointer:proto3_arena_lite_unittest.TestAllTypes.repeated_string)
}
const ::google::protobuf::RepeatedPtrField< ::std::string>&
TestAllTypes::repeated_string() const {
  // @@protoc_insertion_point(field_list:proto3_arena_lite_unittest.TestAllTypes.repeated_string)
  return repeated_string_;
}
::google::protobuf::RepeatedPtrField< ::std::string>*
TestAllTypes::mutable_repeated_string() {
  // @@protoc_insertion_point(field_mutable_list:proto3_arena_lite_unittest.TestAllTypes.repeated_string)
  return &repeated_string_;
}

// repeated bytes repeated_bytes = 45;
int TestAllTypes::repeated_bytes_size() const {
  return repeated_bytes_.size();
}
void TestAllTypes::clear_repeated_bytes() {
  repeated_bytes_.Clear();
}
const ::std::string& TestAllTypes::repeated_bytes(int index) const {
  // @@protoc_insertion_point(field_get:proto3_arena_lite_unittest.TestAllTypes.repeated_bytes)
  return repeated_bytes_.Get(index);
}
::std::string* TestAllTypes::mutable_repeated_bytes(int index) {
  // @@protoc_insertion_point(field_mutable:proto3_arena_lite_unittest.TestAllTypes.repeated_bytes)
  return repeated_bytes_.Mutable(index);
}
void TestAllTypes::set_repeated_bytes(int index, const ::std::string& value) {
  // @@protoc_insertion_point(field_set:proto3_arena_lite_unittest.TestAllTypes.repeated_bytes)
  repeated_bytes_.Mutable(index)->assign(value);
}
void TestAllTypes::set_repeated_bytes(int index, const char* value) {
  repeated_bytes_.Mutable(index)->assign(value);
  // @@protoc_insertion_point(field_set_char:proto3_arena_lite_unittest.TestAllTypes.repeated_bytes)
}
void TestAllTypes::set_repeated_bytes(int index, const void* value, size_t size) {
  repeated_bytes_.Mutable(index)->assign(
    reinterpret_cast<const char*>(value), size);
  // @@protoc_insertion_point(field_set_pointer:proto3_arena_lite_unittest.TestAllTypes.repeated_bytes)
}
::std::string* TestAllTypes::add_repeated_bytes() {
  // @@protoc_insertion_point(field_add_mutable:proto3_arena_lite_unittest.TestAllTypes.repeated_bytes)
  return repeated_bytes_.Add();
}
void TestAllTypes::add_repeated_bytes(const ::std::string& value) {
  repeated_bytes_.Add()->assign(value);
  // @@protoc_insertion_point(field_add:proto3_arena_lite_unittest.TestAllTypes.repeated_bytes)
}
void TestAllTypes::add_repeated_bytes(const char* value) {
  repeated_bytes_.Add()->assign(value);
  // @@protoc_insertion_point(field_add_char:proto3_arena_lite_unittest.TestAllTypes.repeated_bytes)
}
void TestAllTypes::add_repeated_bytes(const void* value, size_t size) {
  repeated_bytes_.Add()->assign(reinterpret_cast<const char*>(value), size);
  // @@protoc_insertion_point(field_add_pointer:proto3_arena_lite_unittest.TestAllTypes.repeated_bytes)
}
const ::google::protobuf::RepeatedPtrField< ::std::string>&
TestAllTypes::repeated_bytes() const {
  // @@protoc_insertion_point(field_list:proto3_arena_lite_unittest.TestAllTypes.repeated_bytes)
  return repeated_bytes_;
}
::google::protobuf::RepeatedPtrField< ::std::string>*
TestAllTypes::mutable_repeated_bytes() {
  // @@protoc_insertion_point(field_mutable_list:proto3_arena_lite_unittest.TestAllTypes.repeated_bytes)
  return &repeated_bytes_;
}

// repeated .proto3_arena_lite_unittest.TestAllTypes.NestedMessage repeated_nested_message = 48;
int TestAllTypes::repeated_nested_message_size() const {
  return repeated_nested_message_.size();
}
void TestAllTypes::clear_repeated_nested_message() {
  repeated_nested_message_.Clear();
}
const ::proto3_arena_lite_unittest::TestAllTypes_NestedMessage& TestAllTypes::repeated_nested_message(int index) const {
  // @@protoc_insertion_point(field_get:proto3_arena_lite_unittest.TestAllTypes.repeated_nested_message)
  return repeated_nested_message_.Get(index);
}
::proto3_arena_lite_unittest::TestAllTypes_NestedMessage* TestAllTypes::mutable_repeated_nested_message(int index) {
  // @@protoc_insertion_point(field_mutable:proto3_arena_lite_unittest.TestAllTypes.repeated_nested_message)
  return repeated_nested_message_.Mutable(index);
}
::proto3_arena_lite_unittest::TestAllTypes_NestedMessage* TestAllTypes::add_repeated_nested_message() {
  // @@protoc_insertion_point(field_add:proto3_arena_lite_unittest.TestAllTypes.repeated_nested_message)
  return repeated_nested_message_.Add();
}
::google::protobuf::RepeatedPtrField< ::proto3_arena_lite_unittest::TestAllTypes_NestedMessage >*
TestAllTypes::mutable_repeated_nested_message() {
  // @@protoc_insertion_point(field_mutable_list:proto3_arena_lite_unittest.TestAllTypes.repeated_nested_message)
  return &repeated_nested_message_;
}
const ::google::protobuf::RepeatedPtrField< ::proto3_arena_lite_unittest::TestAllTypes_NestedMessage >&
TestAllTypes::repeated_nested_message() const {
  // @@protoc_insertion_point(field_list:proto3_arena_lite_unittest.TestAllTypes.repeated_nested_message)
  return repeated_nested_message_;
}

// repeated .proto3_arena_lite_unittest.ForeignMessage repeated_foreign_message = 49;
int TestAllTypes::repeated_foreign_message_size() const {
  return repeated_foreign_message_.size();
}
void TestAllTypes::clear_repeated_foreign_message() {
  repeated_foreign_message_.Clear();
}
const ::proto3_arena_lite_unittest::ForeignMessage& TestAllTypes::repeated_foreign_message(int index) const {
  // @@protoc_insertion_point(field_get:proto3_arena_lite_unittest.TestAllTypes.repeated_foreign_message)
  return repeated_foreign_message_.Get(index);
}
::proto3_arena_lite_unittest::ForeignMessage* TestAllTypes::mutable_repeated_foreign_message(int index) {
  // @@protoc_insertion_point(field_mutable:proto3_arena_lite_unittest.TestAllTypes.repeated_foreign_message)
  return repeated_foreign_message_.Mutable(index);
}
::proto3_arena_lite_unittest::ForeignMessage* TestAllTypes::add_repeated_foreign_message() {
  // @@protoc_insertion_point(field_add:proto3_arena_lite_unittest.TestAllTypes.repeated_foreign_message)
  return repeated_foreign_message_.Add();
}
::google::protobuf::RepeatedPtrField< ::proto3_arena_lite_unittest::ForeignMessage >*
TestAllTypes::mutable_repeated_foreign_message() {
  // @@protoc_insertion_point(field_mutable_list:proto3_arena_lite_unittest.TestAllTypes.repeated_foreign_message)
  return &repeated_foreign_message_;
}
const ::google::protobuf::RepeatedPtrField< ::proto3_arena_lite_unittest::ForeignMessage >&
TestAllTypes::repeated_foreign_message() const {
  // @@protoc_insertion_point(field_list:proto3_arena_lite_unittest.TestAllTypes.repeated_foreign_message)
  return repeated_foreign_message_;
}

// repeated .protobuf_unittest_import.ImportMessage repeated_import_message = 50;
int TestAllTypes::repeated_import_message_size() const {
  return repeated_import_message_.size();
}
void TestAllTypes::clear_repeated_import_message() {
  repeated_import_message_.Clear();
}
const ::protobuf_unittest_import::ImportMessage& TestAllTypes::repeated_import_message(int index) const {
  // @@protoc_insertion_point(field_get:proto3_arena_lite_unittest.TestAllTypes.repeated_import_message)
  return repeated_import_message_.Get(index);
}
::protobuf_unittest_import::ImportMessage* TestAllTypes::mutable_repeated_import_message(int index) {
  // @@protoc_insertion_point(field_mutable:proto3_arena_lite_unittest.TestAllTypes.repeated_import_message)
  return repeated_import_message_.Mutable(index);
}
::protobuf_unittest_import::ImportMessage* TestAllTypes::add_repeated_import_message() {
  // @@protoc_insertion_point(field_add:proto3_arena_lite_unittest.TestAllTypes.repeated_import_message)
  return repeated_import_message_.Add();
}
::google::protobuf::RepeatedPtrField< ::protobuf_unittest_import::ImportMessage >*
TestAllTypes::mutable_repeated_import_message() {
  // @@protoc_insertion_point(field_mutable_list:proto3_arena_lite_unittest.TestAllTypes.repeated_import_message)
  return &repeated_import_message_;
}
const ::google::protobuf::RepeatedPtrField< ::protobuf_unittest_import::ImportMessage >&
TestAllTypes::repeated_import_message() const {
  // @@protoc_insertion_point(field_list:proto3_arena_lite_unittest.TestAllTypes.repeated_import_message)
  return repeated_import_message_;
}

// repeated .proto3_arena_lite_unittest.TestAllTypes.NestedEnum repeated_nested_enum = 51;
int TestAllTypes::repeated_nested_enum_size() const {
  return repeated_nested_enum_.size();
}
void TestAllTypes::clear_repeated_nested_enum() {
  repeated_nested_enum_.Clear();
}
::proto3_arena_lite_unittest::TestAllTypes_NestedEnum TestAllTypes::repeated_nested_enum(int index) const {
  // @@protoc_insertion_point(field_get:proto3_arena_lite_unittest.TestAllTypes.repeated_nested_enum)
  return static_cast< ::proto3_arena_lite_unittest::TestAllTypes_NestedEnum >(repeated_nested_enum_.Get(index));
}
void TestAllTypes::set_repeated_nested_enum(int index, ::proto3_arena_lite_unittest::TestAllTypes_NestedEnum value) {
  repeated_nested_enum_.Set(index, value);
  // @@protoc_insertion_point(field_set:proto3_arena_lite_unittest.TestAllTypes.repeated_nested_enum)
}
void TestAllTypes::add_repeated_nested_enum(::proto3_arena_lite_unittest::TestAllTypes_NestedEnum value) {
  repeated_nested_enum_.Add(value);
  // @@protoc_insertion_point(field_add:proto3_arena_lite_unittest.TestAllTypes.repeated_nested_enum)
}
const ::google::protobuf::RepeatedField<int>&
TestAllTypes::repeated_nested_enum() const {
  // @@protoc_insertion_point(field_list:proto3_arena_lite_unittest.TestAllTypes.repeated_nested_enum)
  return repeated_nested_enum_;
}
::google::protobuf::RepeatedField<int>*
TestAllTypes::mutable_repeated_nested_enum() {
  // @@protoc_insertion_point(field_mutable_list:proto3_arena_lite_unittest.TestAllTypes.repeated_nested_enum)
  return &repeated_nested_enum_;
}

// repeated .proto3_arena_lite_unittest.ForeignEnum repeated_foreign_enum = 52;
int TestAllTypes::repeated_foreign_enum_size() const {
  return repeated_foreign_enum_.size();
}
void TestAllTypes::clear_repeated_foreign_enum() {
  repeated_foreign_enum_.Clear();
}
::proto3_arena_lite_unittest::ForeignEnum TestAllTypes::repeated_foreign_enum(int index) const {
  // @@protoc_insertion_point(field_get:proto3_arena_lite_unittest.TestAllTypes.repeated_foreign_enum)
  return static_cast< ::proto3_arena_lite_unittest::ForeignEnum >(repeated_foreign_enum_.Get(index));
}
void TestAllTypes::set_repeated_foreign_enum(int index, ::proto3_arena_lite_unittest::ForeignEnum value) {
  repeated_foreign_enum_.Set(index, value);
  // @@protoc_insertion_point(field_set:proto3_arena_lite_unittest.TestAllTypes.repeated_foreign_enum)
}
void TestAllTypes::add_repeated_foreign_enum(::proto3_arena_lite_unittest::ForeignEnum value) {
  repeated_foreign_enum_.Add(value);
  // @@protoc_insertion_point(field_add:proto3_arena_lite_unittest.TestAllTypes.repeated_foreign_enum)
}
const ::google::protobuf::RepeatedField<int>&
TestAllTypes::repeated_foreign_enum() const {
  // @@protoc_insertion_point(field_list:proto3_arena_lite_unittest.TestAllTypes.repeated_foreign_enum)
  return repeated_foreign_enum_;
}
::google::protobuf::RepeatedField<int>*
TestAllTypes::mutable_repeated_foreign_enum() {
  // @@protoc_insertion_point(field_mutable_list:proto3_arena_lite_unittest.TestAllTypes.repeated_foreign_enum)
  return &repeated_foreign_enum_;
}

// repeated string repeated_string_piece = 54 [ctype = STRING_PIECE];
int TestAllTypes::repeated_string_piece_size() const {
  return repeated_string_piece_.size();
}
void TestAllTypes::clear_repeated_string_piece() {
  repeated_string_piece_.Clear();
}
const ::std::string& TestAllTypes::repeated_string_piece(int index) const {
  // @@protoc_insertion_point(field_get:proto3_arena_lite_unittest.TestAllTypes.repeated_string_piece)
  return repeated_string_piece_.Get(index);
}
::std::string* TestAllTypes::mutable_repeated_string_piece(int index) {
  // @@protoc_insertion_point(field_mutable:proto3_arena_lite_unittest.TestAllTypes.repeated_string_piece)
  return repeated_string_piece_.Mutable(index);
}
void TestAllTypes::set_repeated_string_piece(int index, const ::std::string& value) {
  // @@protoc_insertion_point(field_set:proto3_arena_lite_unittest.TestAllTypes.repeated_string_piece)
  repeated_string_piece_.Mutable(index)->assign(value);
}
void TestAllTypes::set_repeated_string_piece(int index, const char* value) {
  repeated_string_piece_.Mutable(index)->assign(value);
  // @@protoc_insertion_point(field_set_char:proto3_arena_lite_unittest.TestAllTypes.repeated_string_piece)
}
void TestAllTypes::set_repeated_string_piece(int index, const char* value, size_t size) {
  repeated_string_piece_.Mutable(index)->assign(
    reinterpret_cast<const char*>(value), size);
  // @@protoc_insertion_point(field_set_pointer:proto3_arena_lite_unittest.TestAllTypes.repeated_string_piece)
}
::std::string* TestAllTypes::add_repeated_string_piece() {
  // @@protoc_insertion_point(field_add_mutable:proto3_arena_lite_unittest.TestAllTypes.repeated_string_piece)
  return repeated_string_piece_.Add();
}
void TestAllTypes::add_repeated_string_piece(const ::std::string& value) {
  repeated_string_piece_.Add()->assign(value);
  // @@protoc_insertion_point(field_add:proto3_arena_lite_unittest.TestAllTypes.repeated_string_piece)
}
void TestAllTypes::add_repeated_string_piece(const char* value) {
  repeated_string_piece_.Add()->assign(value);
  // @@protoc_insertion_point(field_add_char:proto3_arena_lite_unittest.TestAllTypes.repeated_string_piece)
}
void TestAllTypes::add_repeated_string_piece(const char* value, size_t size) {
  repeated_string_piece_.Add()->assign(reinterpret_cast<const char*>(value), size);
  // @@protoc_insertion_point(field_add_pointer:proto3_arena_lite_unittest.TestAllTypes.repeated_string_piece)
}
const ::google::protobuf::RepeatedPtrField< ::std::string>&
TestAllTypes::repeated_string_piece() const {
  // @@protoc_insertion_point(field_list:proto3_arena_lite_unittest.TestAllTypes.repeated_string_piece)
  return repeated_string_piece_;
}
::google::protobuf::RepeatedPtrField< ::std::string>*
TestAllTypes::mutable_repeated_string_piece() {
  // @@protoc_insertion_point(field_mutable_list:proto3_arena_lite_unittest.TestAllTypes.repeated_string_piece)
  return &repeated_string_piece_;
}

// repeated string repeated_cord = 55 [ctype = CORD];
int TestAllTypes::repeated_cord_size() const {
  return repeated_cord_.size();
}
void TestAllTypes::clear_repeated_cord() {
  repeated_cord_.Clear();
}
const ::std::string& TestAllTypes::repeated_cord(int index) const {
  // @@protoc_insertion_point(field_get:proto3_arena_lite_unittest.TestAllTypes.repeated_cord)
  return repeated_cord_.Get(index);
}
::std::string* TestAllTypes::mutable_repeated_cord(int index) {
  // @@protoc_insertion_point(field_mutable:proto3_arena_lite_unittest.TestAllTypes.repeated_cord)
  return repeated_cord_.Mutable(index);
}
void TestAllTypes::set_repeated_cord(int index, const ::std::string& value) {
  // @@protoc_insertion_point(field_set:proto3_arena_lite_unittest.TestAllTypes.repeated_cord)
  repeated_cord_.Mutable(index)->assign(value);
}
void TestAllTypes::set_repeated_cord(int index, const char* value) {
  repeated_cord_.Mutable(index)->assign(value);
  // @@protoc_insertion_point(field_set_char:proto3_arena_lite_unittest.TestAllTypes.repeated_cord)
}
void TestAllTypes::set_repeated_cord(int index, const char* value, size_t size) {
  repeated_cord_.Mutable(index)->assign(
    reinterpret_cast<const char*>(value), size);
  // @@protoc_insertion_point(field_set_pointer:proto3_arena_lite_unittest.TestAllTypes.repeated_cord)
}
::std::string* TestAllTypes::add_repeated_cord() {
  // @@protoc_insertion_point(field_add_mutable:proto3_arena_lite_unittest.TestAllTypes.repeated_cord)
  return repeated_cord_.Add();
}
void TestAllTypes::add_repeated_cord(const ::std::string& value) {
  repeated_cord_.Add()->assign(value);
  // @@protoc_insertion_point(field_add:proto3_arena_lite_unittest.TestAllTypes.repeated_cord)
}
void TestAllTypes::add_repeated_cord(const char* value) {
  repeated_cord_.Add()->assign(value);
  // @@protoc_insertion_point(field_add_char:proto3_arena_lite_unittest.TestAllTypes.repeated_cord)
}
void TestAllTypes::add_repeated_cord(const char* value, size_t size) {
  repeated_cord_.Add()->assign(reinterpret_cast<const char*>(value), size);
  // @@protoc_insertion_point(field_add_pointer:proto3_arena_lite_unittest.TestAllTypes.repeated_cord)
}
const ::google::protobuf::RepeatedPtrField< ::std::string>&
TestAllTypes::repeated_cord() const {
  // @@protoc_insertion_point(field_list:proto3_arena_lite_unittest.TestAllTypes.repeated_cord)
  return repeated_cord_;
}
::google::protobuf::RepeatedPtrField< ::std::string>*
TestAllTypes::mutable_repeated_cord() {
  // @@protoc_insertion_point(field_mutable_list:proto3_arena_lite_unittest.TestAllTypes.repeated_cord)
  return &repeated_cord_;
}

// repeated .proto3_arena_lite_unittest.TestAllTypes.NestedMessage repeated_lazy_message = 57 [lazy = true];
int TestAllTypes::repeated_lazy_message_size() const {
  return repeated_lazy_message_.size();
}
void TestAllTypes::clear_repeated_lazy_message() {
  repeated_lazy_message_.Clear();
}
const ::proto3_arena_lite_unittest::TestAllTypes_NestedMessage& TestAllTypes::repeated_lazy_message(int index) const {
  // @@protoc_insertion_point(field_get:proto3_arena_lite_unittest.TestAllTypes.repeated_lazy_message)
  return repeated_lazy_message_.Get(index);
}
::proto3_arena_lite_unittest::TestAllTypes_NestedMessage* TestAllTypes::mutable_repeated_lazy_message(int index) {
  // @@protoc_insertion_point(field_mutable:proto3_arena_lite_unittest.TestAllTypes.repeated_lazy_message)
  return repeated_lazy_message_.Mutable(index);
}
::proto3_arena_lite_unittest::TestAllTypes_NestedMessage* TestAllTypes::add_repeated_lazy_message() {
  // @@protoc_insertion_point(field_add:proto3_arena_lite_unittest.TestAllTypes.repeated_lazy_message)
  return repeated_lazy_message_.Add();
}
::google::protobuf::RepeatedPtrField< ::proto3_arena_lite_unittest::TestAllTypes_NestedMessage >*
TestAllTypes::mutable_repeated_lazy_message() {
  // @@protoc_insertion_point(field_mutable_list:proto3_arena_lite_unittest.TestAllTypes.repeated_lazy_message)
  return &repeated_lazy_message_;
}
const ::google::protobuf::RepeatedPtrField< ::proto3_arena_lite_unittest::TestAllTypes_NestedMessage >&
TestAllTypes::repeated_lazy_message() const {
  // @@protoc_insertion_point(field_list:proto3_arena_lite_unittest.TestAllTypes.repeated_lazy_message)
  return repeated_lazy_message_;
}

// optional uint32 oneof_uint32 = 111;
bool TestAllTypes::has_oneof_uint32() const {
  return oneof_field_case() == kOneofUint32;
}
void TestAllTypes::set_has_oneof_uint32() {
  _oneof_case_[0] = kOneofUint32;
}
void TestAllTypes::clear_oneof_uint32() {
  if (has_oneof_uint32()) {
    oneof_field_.oneof_uint32_ = 0u;
    clear_has_oneof_field();
  }
}
::google::protobuf::uint32 TestAllTypes::oneof_uint32() const {
  // @@protoc_insertion_point(field_get:proto3_arena_lite_unittest.TestAllTypes.oneof_uint32)
  if (has_oneof_uint32()) {
    return oneof_field_.oneof_uint32_;
  }
  return 0u;
}
void TestAllTypes::set_oneof_uint32(::google::protobuf::uint32 value) {
  if (!has_oneof_uint32()) {
    clear_oneof_field();
    set_has_oneof_uint32();
  }
  oneof_field_.oneof_uint32_ = value;
  // @@protoc_insertion_point(field_set:proto3_arena_lite_unittest.TestAllTypes.oneof_uint32)
}

// optional .proto3_arena_lite_unittest.TestAllTypes.NestedMessage oneof_nested_message = 112;
bool TestAllTypes::has_oneof_nested_message() const {
  return oneof_field_case() == kOneofNestedMessage;
}
void TestAllTypes::set_has_oneof_nested_message() {
  _oneof_case_[0] = kOneofNestedMessage;
}
void TestAllTypes::clear_oneof_nested_message() {
  if (has_oneof_nested_message()) {
    if (GetArenaNoVirtual() == NULL) {
      delete oneof_field_.oneof_nested_message_;
    }
    clear_has_oneof_field();
  }
}
 const ::proto3_arena_lite_unittest::TestAllTypes_NestedMessage& TestAllTypes::oneof_nested_message() const {
  // @@protoc_insertion_point(field_get:proto3_arena_lite_unittest.TestAllTypes.oneof_nested_message)
  return has_oneof_nested_message()
      ? *oneof_field_.oneof_nested_message_
      : ::proto3_arena_lite_unittest::TestAllTypes_NestedMessage::default_instance();
}
::proto3_arena_lite_unittest::TestAllTypes_NestedMessage* TestAllTypes::mutable_oneof_nested_message() {
  if (!has_oneof_nested_message()) {
    clear_oneof_field();
    set_has_oneof_nested_message();
    oneof_field_.oneof_nested_message_ = 
      ::google::protobuf::Arena::CreateMessage< ::proto3_arena_lite_unittest::TestAllTypes_NestedMessage >(
      GetArenaNoVirtual());
  }
  // @@protoc_insertion_point(field_mutable:proto3_arena_lite_unittest.TestAllTypes.oneof_nested_message)
  return oneof_field_.oneof_nested_message_;
}
::proto3_arena_lite_unittest::TestAllTypes_NestedMessage* TestAllTypes::release_oneof_nested_message() {
  // @@protoc_insertion_point(field_release:proto3_arena_lite_unittest.TestAllTypes.oneof_nested_message)
  if (has_oneof_nested_message()) {
    clear_has_oneof_field();
    if (GetArenaNoVirtual() != NULL) {
      ::proto3_arena_lite_unittest::TestAllTypes_NestedMessage* temp = new ::proto3_arena_lite_unittest::TestAllTypes_NestedMessage(*oneof_field_.oneof_nested_message_);
      oneof_field_.oneof_nested_message_ = NULL;
      return temp;
    } else {
      ::proto3_arena_lite_unittest::TestAllTypes_NestedMessage* temp = oneof_field_.oneof_nested_message_;
      oneof_field_.oneof_nested_message_ = NULL;
      return temp;
    }
  } else {
    return NULL;
  }
}
void TestAllTypes::set_allocated_oneof_nested_message(::proto3_arena_lite_unittest::TestAllTypes_NestedMessage* oneof_nested_message) {
  clear_oneof_field();
  if (oneof_nested_message) {
    if (GetArenaNoVirtual() != NULL &&
        ::google::protobuf::Arena::GetArena(oneof_nested_message) == NULL) {
      GetArenaNoVirtual()->Own(oneof_nested_message);
    } else if (GetArenaNoVirtual() !=
               ::google::protobuf::Arena::GetArena(oneof_nested_message)) {
      ::proto3_arena_lite_unittest::TestAllTypes_NestedMessage* new_oneof_nested_message = 
          ::google::protobuf::Arena::CreateMessage< ::proto3_arena_lite_unittest::TestAllTypes_NestedMessage >(
          GetArenaNoVirtual());
      new_oneof_nested_message->CopyFrom(*oneof_nested_message);
      oneof_nested_message = new_oneof_nested_message;
    }
    set_has_oneof_nested_message();
    oneof_field_.oneof_nested_message_ = oneof_nested_message;
  }
  // @@protoc_insertion_point(field_set_allocated:proto3_arena_lite_unittest.TestAllTypes.oneof_nested_message)
}
 ::proto3_arena_lite_unittest::TestAllTypes_NestedMessage* TestAllTypes::unsafe_arena_release_oneof_nested_message() {
  // @@protoc_insertion_point(field_unsafe_arena_release:proto3_arena_lite_unittest.TestAllTypes.oneof_nested_message)
  if (has_oneof_nested_message()) {
    clear_has_oneof_field();
    ::proto3_arena_lite_unittest::TestAllTypes_NestedMessage* temp = oneof_field_.oneof_nested_message_;
    oneof_field_.oneof_nested_message_ = NULL;
    return temp;
  } else {
    return NULL;
  }
}
 void TestAllTypes::unsafe_arena_set_allocated_oneof_nested_message(::proto3_arena_lite_unittest::TestAllTypes_NestedMessage* oneof_nested_message) {
  clear_oneof_field();
  if (oneof_nested_message) {
    set_has_oneof_nested_message();
    oneof_field_.oneof_nested_message_ = oneof_nested_message;
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:proto3_arena_lite_unittest.TestAllTypes.oneof_nested_message)
}

// optional string oneof_string = 113;
bool TestAllTypes::has_oneof_string() const {
  return oneof_field_case() == kOneofString;
}
void TestAllTypes::set_has_oneof_string() {
  _oneof_case_[0] = kOneofString;
}
void TestAllTypes::clear_oneof_string() {
  if (has_oneof_string()) {
    oneof_field_.oneof_string_.Destroy(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
        GetArenaNoVirtual());
    clear_has_oneof_field();
  }
}
const ::std::string& TestAllTypes::oneof_string() const {
  // @@protoc_insertion_point(field_get:proto3_arena_lite_unittest.TestAllTypes.oneof_string)
  if (has_oneof_string()) {
    return oneof_field_.oneof_string_.Get(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  }
  return *&::google::protobuf::internal::GetEmptyStringAlreadyInited();
}
void TestAllTypes::set_oneof_string(const ::std::string& value) {
  if (!has_oneof_string()) {
    clear_oneof_field();
    set_has_oneof_string();
    oneof_field_.oneof_string_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  }
  oneof_field_.oneof_string_.Set(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value,
      GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set:proto3_arena_lite_unittest.TestAllTypes.oneof_string)
}
void TestAllTypes::set_oneof_string(const char* value) {
  if (!has_oneof_string()) {
    clear_oneof_field();
    set_has_oneof_string();
    oneof_field_.oneof_string_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  }
  oneof_field_.oneof_string_.Set(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(value), GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_char:proto3_arena_lite_unittest.TestAllTypes.oneof_string)
}
void TestAllTypes::set_oneof_string(const char* value,
                             size_t size) {
  if (!has_oneof_string()) {
    clear_oneof_field();
    set_has_oneof_string();
    oneof_field_.oneof_string_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  }
  oneof_field_.oneof_string_.Set(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(
      reinterpret_cast<const char*>(value), size),
      GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_pointer:proto3_arena_lite_unittest.TestAllTypes.oneof_string)
}
::std::string* TestAllTypes::mutable_oneof_string() {
  if (!has_oneof_string()) {
    clear_oneof_field();
    set_has_oneof_string();
    oneof_field_.oneof_string_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  }
  return oneof_field_.oneof_string_.Mutable(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      GetArenaNoVirtual());
  // @@protoc_insertion_point(field_mutable:proto3_arena_lite_unittest.TestAllTypes.oneof_string)
}
::std::string* TestAllTypes::release_oneof_string() {
  // @@protoc_insertion_point(field_release:proto3_arena_lite_unittest.TestAllTypes.oneof_string)
  if (has_oneof_string()) {
    clear_has_oneof_field();
    return oneof_field_.oneof_string_.Release(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
        GetArenaNoVirtual());
  } else {
    return NULL;
  }
}
::std::string* TestAllTypes::unsafe_arena_release_oneof_string() {
  // @@protoc_insertion_point(field_unsafe_arena_release:proto3_arena_lite_unittest.TestAllTypes.oneof_string)
  GOOGLE_DCHECK(GetArenaNoVirtual() != NULL);
  if (has_oneof_string()) {
    clear_has_oneof_field();
    return oneof_field_.oneof_string_.UnsafeArenaRelease(
        &::google::protobuf::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
  } else {
    return NULL;
  }
}
void TestAllTypes::set_allocated_oneof_string(::std::string* oneof_string) {
  if (!has_oneof_string()) {
    oneof_field_.oneof_string_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  }
  clear_oneof_field();
  if (oneof_string != NULL) {
    set_has_oneof_string();
    oneof_field_.oneof_string_.SetAllocated(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), oneof_string,
        GetArenaNoVirtual());
  }
  // @@protoc_insertion_point(field_set_allocated:proto3_arena_lite_unittest.TestAllTypes.oneof_string)
}
void TestAllTypes::unsafe_arena_set_allocated_oneof_string(::std::string* oneof_string) {
  GOOGLE_DCHECK(GetArenaNoVirtual() != NULL);
  if (!has_oneof_string()) {
    oneof_field_.oneof_string_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  }
  clear_oneof_field();
  if (oneof_string) {
    set_has_oneof_string();
    oneof_field_.oneof_string_.UnsafeArenaSetAllocated(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), oneof_string, GetArenaNoVirtual());
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:proto3_arena_lite_unittest.TestAllTypes.oneof_string)
}

// optional bytes oneof_bytes = 114;
bool TestAllTypes::has_oneof_bytes() const {
  return oneof_field_case() == kOneofBytes;
}
void TestAllTypes::set_has_oneof_bytes() {
  _oneof_case_[0] = kOneofBytes;
}
void TestAllTypes::clear_oneof_bytes() {
  if (has_oneof_bytes()) {
    oneof_field_.oneof_bytes_.Destroy(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
        GetArenaNoVirtual());
    clear_has_oneof_field();
  }
}
const ::std::string& TestAllTypes::oneof_bytes() const {
  // @@protoc_insertion_point(field_get:proto3_arena_lite_unittest.TestAllTypes.oneof_bytes)
  if (has_oneof_bytes()) {
    return oneof_field_.oneof_bytes_.Get(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  }
  return *&::google::protobuf::internal::GetEmptyStringAlreadyInited();
}
void TestAllTypes::set_oneof_bytes(const ::std::string& value) {
  if (!has_oneof_bytes()) {
    clear_oneof_field();
    set_has_oneof_bytes();
    oneof_field_.oneof_bytes_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  }
  oneof_field_.oneof_bytes_.Set(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value,
      GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set:proto3_arena_lite_unittest.TestAllTypes.oneof_bytes)
}
void TestAllTypes::set_oneof_bytes(const char* value) {
  if (!has_oneof_bytes()) {
    clear_oneof_field();
    set_has_oneof_bytes();
    oneof_field_.oneof_bytes_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  }
  oneof_field_.oneof_bytes_.Set(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(value), GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_char:proto3_arena_lite_unittest.TestAllTypes.oneof_bytes)
}
void TestAllTypes::set_oneof_bytes(const void* value,
                             size_t size) {
  if (!has_oneof_bytes()) {
    clear_oneof_field();
    set_has_oneof_bytes();
    oneof_field_.oneof_bytes_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  }
  oneof_field_.oneof_bytes_.Set(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(
      reinterpret_cast<const char*>(value), size),
      GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_pointer:proto3_arena_lite_unittest.TestAllTypes.oneof_bytes)
}
::std::string* TestAllTypes::mutable_oneof_bytes() {
  if (!has_oneof_bytes()) {
    clear_oneof_field();
    set_has_oneof_bytes();
    oneof_field_.oneof_bytes_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  }
  return oneof_field_.oneof_bytes_.Mutable(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      GetArenaNoVirtual());
  // @@protoc_insertion_point(field_mutable:proto3_arena_lite_unittest.TestAllTypes.oneof_bytes)
}
::std::string* TestAllTypes::release_oneof_bytes() {
  // @@protoc_insertion_point(field_release:proto3_arena_lite_unittest.TestAllTypes.oneof_bytes)
  if (has_oneof_bytes()) {
    clear_has_oneof_field();
    return oneof_field_.oneof_bytes_.Release(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
        GetArenaNoVirtual());
  } else {
    return NULL;
  }
}
::std::string* TestAllTypes::unsafe_arena_release_oneof_bytes() {
  // @@protoc_insertion_point(field_unsafe_arena_release:proto3_arena_lite_unittest.TestAllTypes.oneof_bytes)
  GOOGLE_DCHECK(GetArenaNoVirtual() != NULL);
  if (has_oneof_bytes()) {
    clear_has_oneof_field();
    return oneof_field_.oneof_bytes_.UnsafeArenaRelease(
        &::google::protobuf::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
  } else {
    return NULL;
  }
}
void TestAllTypes::set_allocated_oneof_bytes(::std::string* oneof_bytes) {
  if (!has_oneof_bytes()) {
    oneof_field_.oneof_bytes_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  }
  clear_oneof_field();
  if (oneof_bytes != NULL) {
    set_has_oneof_bytes();
    oneof_field_.oneof_bytes_.SetAllocated(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), oneof_bytes,
        GetArenaNoVirtual());
  }
  // @@protoc_insertion_point(field_set_allocated:proto3_arena_lite_unittest.TestAllTypes.oneof_bytes)
}
void TestAllTypes::unsafe_arena_set_allocated_oneof_bytes(::std::string* oneof_bytes) {
  GOOGLE_DCHECK(GetArenaNoVirtual() != NULL);
  if (!has_oneof_bytes()) {
    oneof_field_.oneof_bytes_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  }
  clear_oneof_field();
  if (oneof_bytes) {
    set_has_oneof_bytes();
    oneof_field_.oneof_bytes_.UnsafeArenaSetAllocated(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), oneof_bytes, GetArenaNoVirtual());
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:proto3_arena_lite_unittest.TestAllTypes.oneof_bytes)
}

bool TestAllTypes::has_oneof_field() const {
  return oneof_field_case() != ONEOF_FIELD_NOT_SET;
}
void TestAllTypes::clear_has_oneof_field() {
  _oneof_case_[0] = ONEOF_FIELD_NOT_SET;
}
TestAllTypes::OneofFieldCase TestAllTypes::oneof_field_case() const {
  return TestAllTypes::OneofFieldCase(_oneof_case_[0]);
}
inline const TestAllTypes* TestAllTypes::internal_default_instance() {
  return &TestAllTypes_default_instance_.get();
}
#endif  // PROTOBUF_INLINE_NOT_IN_HEADERS

// ===================================================================

#if !defined(_MSC_VER) || _MSC_VER >= 1900
const int TestPackedTypes::kPackedInt32FieldNumber;
const int TestPackedTypes::kPackedInt64FieldNumber;
const int TestPackedTypes::kPackedUint32FieldNumber;
const int TestPackedTypes::kPackedUint64FieldNumber;
const int TestPackedTypes::kPackedSint32FieldNumber;
const int TestPackedTypes::kPackedSint64FieldNumber;
const int TestPackedTypes::kPackedFixed32FieldNumber;
const int TestPackedTypes::kPackedFixed64FieldNumber;
const int TestPackedTypes::kPackedSfixed32FieldNumber;
const int TestPackedTypes::kPackedSfixed64FieldNumber;
const int TestPackedTypes::kPackedFloatFieldNumber;
const int TestPackedTypes::kPackedDoubleFieldNumber;
const int TestPackedTypes::kPackedBoolFieldNumber;
const int TestPackedTypes::kPackedEnumFieldNumber;
#endif  // !defined(_MSC_VER) || _MSC_VER >= 1900

TestPackedTypes::TestPackedTypes()
  : ::google::protobuf::MessageLite(), _arena_ptr_(NULL) {
  if (this != internal_default_instance()) protobuf_InitDefaults_google_2fprotobuf_2funittest_5fproto3_5farena_5flite_2eproto();
  SharedCtor();
  // @@protoc_insertion_point(constructor:proto3_arena_lite_unittest.TestPackedTypes)
}
TestPackedTypes::TestPackedTypes(::google::protobuf::Arena* arena)
  : ::google::protobuf::MessageLite(),
  _arena_ptr_(arena),
  packed_int32_(arena),
  packed_int64_(arena),
  packed_uint32_(arena),
  packed_uint64_(arena),
  packed_sint32_(arena),
  packed_sint64_(arena),
  packed_fixed32_(arena),
  packed_fixed64_(arena),
  packed_sfixed32_(arena),
  packed_sfixed64_(arena),
  packed_float_(arena),
  packed_double_(arena),
  packed_bool_(arena),
  packed_enum_(arena) {
#ifdef GOOGLE_PROTOBUF_NO_STATIC_INITIALIZER
  protobuf_InitDefaults_google_2fprotobuf_2funittest_5fproto3_5farena_5flite_2eproto();
#endif  // GOOGLE_PROTOBUF_NO_STATIC_INITIALIZER
  SharedCtor();
  RegisterArenaDtor(arena);
  // @@protoc_insertion_point(arena_constructor:proto3_arena_lite_unittest.TestPackedTypes)
}

void TestPackedTypes::InitAsDefaultInstance() {
}

TestPackedTypes::TestPackedTypes(const TestPackedTypes& from)
  : ::google::protobuf::MessageLite(),
    _arena_ptr_(NULL) {
  SharedCtor();
  UnsafeMergeFrom(from);
  // @@protoc_insertion_point(copy_constructor:proto3_arena_lite_unittest.TestPackedTypes)
}

void TestPackedTypes::SharedCtor() {
  _cached_size_ = 0;
}

TestPackedTypes::~TestPackedTypes() {
  // @@protoc_insertion_point(destructor:proto3_arena_lite_unittest.TestPackedTypes)
  SharedDtor();
}

void TestPackedTypes::SharedDtor() {
  ::google::protobuf::Arena* arena = GetArenaNoVirtual();
  if (arena != NULL) {
    return;
  }

}

void TestPackedTypes::ArenaDtor(void* object) {
  TestPackedTypes* _this = reinterpret_cast< TestPackedTypes* >(object);
  (void)_this;
}
void TestPackedTypes::RegisterArenaDtor(::google::protobuf::Arena* arena) {
}
void TestPackedTypes::SetCachedSize(int size) const {
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
}
const TestPackedTypes& TestPackedTypes::default_instance() {
  protobuf_InitDefaults_google_2fprotobuf_2funittest_5fproto3_5farena_5flite_2eproto();
  return *internal_default_instance();
}

::google::protobuf::internal::ExplicitlyConstructed<TestPackedTypes> TestPackedTypes_default_instance_;

TestPackedTypes* TestPackedTypes::New(::google::protobuf::Arena* arena) const {
  return ::google::protobuf::Arena::CreateMessage<TestPackedTypes>(arena);
}

void TestPackedTypes::Clear() {
// @@protoc_insertion_point(message_clear_start:proto3_arena_lite_unittest.TestPackedTypes)
  packed_int32_.Clear();
  packed_int64_.Clear();
  packed_uint32_.Clear();
  packed_uint64_.Clear();
  packed_sint32_.Clear();
  packed_sint64_.Clear();
  packed_fixed32_.Clear();
  packed_fixed64_.Clear();
  packed_sfixed32_.Clear();
  packed_sfixed64_.Clear();
  packed_float_.Clear();
  packed_double_.Clear();
  packed_bool_.Clear();
  packed_enum_.Clear();
}

bool TestPackedTypes::MergePartialFromCodedStream(
    ::google::protobuf::io::CodedInputStream* input) {
#define DO_(EXPRESSION) if (!GOOGLE_PREDICT_TRUE(EXPRESSION)) goto failure
  ::google::protobuf::uint32 tag;
  // @@protoc_insertion_point(parse_start:proto3_arena_lite_unittest.TestPackedTypes)
  for (;;) {
    ::std::pair< ::google::protobuf::uint32, bool> p = input->ReadTagWithCutoff(16383);
    tag = p.first;
    if (!p.second) goto handle_unusual;
    switch (::google::protobuf::internal::WireFormatLite::GetTagFieldNumber(tag)) {
      // repeated int32 packed_int32 = 90 [packed = true];
      case 90: {
        if (tag == 722) {
          DO_((::google::protobuf::internal::WireFormatLite::ReadPackedPrimitive<
                   ::google::protobuf::int32, ::google::protobuf::internal::WireFormatLite::TYPE_INT32>(
                 input, this->mutable_packed_int32())));
        } else if (tag == 720) {
          DO_((::google::protobuf::internal::WireFormatLite::ReadRepeatedPrimitiveNoInline<
                   ::google::protobuf::int32, ::google::protobuf::internal::WireFormatLite::TYPE_INT32>(
                 2, 722, input, this->mutable_packed_int32())));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(730)) goto parse_packed_int64;
        break;
      }

      // repeated int64 packed_int64 = 91 [packed = true];
      case 91: {
        if (tag == 730) {
         parse_packed_int64:
          DO_((::google::protobuf::internal::WireFormatLite::ReadPackedPrimitive<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_INT64>(
                 input, this->mutable_packed_int64())));
        } else if (tag == 728) {
          DO_((::google::protobuf::internal::WireFormatLite::ReadRepeatedPrimitiveNoInline<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_INT64>(
                 2, 730, input, this->mutable_packed_int64())));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(738)) goto parse_packed_uint32;
        break;
      }

      // repeated uint32 packed_uint32 = 92 [packed = true];
      case 92: {
        if (tag == 738) {
         parse_packed_uint32:
          DO_((::google::protobuf::internal::WireFormatLite::ReadPackedPrimitive<
                   ::google::protobuf::uint32, ::google::protobuf::internal::WireFormatLite::TYPE_UINT32>(
                 input, this->mutable_packed_uint32())));
        } else if (tag == 736) {
          DO_((::google::protobuf::internal::WireFormatLite::ReadRepeatedPrimitiveNoInline<
                   ::google::protobuf::uint32, ::google::protobuf::internal::WireFormatLite::TYPE_UINT32>(
                 2, 738, input, this->mutable_packed_uint32())));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(746)) goto parse_packed_uint64;
        break;
      }

      // repeated uint64 packed_uint64 = 93 [packed = true];
      case 93: {
        if (tag == 746) {
         parse_packed_uint64:
          DO_((::google::protobuf::internal::WireFormatLite::ReadPackedPrimitive<
                   ::google::protobuf::uint64, ::google::protobuf::internal::WireFormatLite::TYPE_UINT64>(
                 input, this->mutable_packed_uint64())));
        } else if (tag == 744) {
          DO_((::google::protobuf::internal::WireFormatLite::ReadRepeatedPrimitiveNoInline<
                   ::google::protobuf::uint64, ::google::protobuf::internal::WireFormatLite::TYPE_UINT64>(
                 2, 746, input, this->mutable_packed_uint64())));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(754)) goto parse_packed_sint32;
        break;
      }

      // repeated sint32 packed_sint32 = 94 [packed = true];
      case 94: {
        if (tag == 754) {
         parse_packed_sint32:
          DO_((::google::protobuf::internal::WireFormatLite::ReadPackedPrimitive<
                   ::google::protobuf::int32, ::google::protobuf::internal::WireFormatLite::TYPE_SINT32>(
                 input, this->mutable_packed_sint32())));
        } else if (tag == 752) {
          DO_((::google::protobuf::internal::WireFormatLite::ReadRepeatedPrimitiveNoInline<
                   ::google::protobuf::int32, ::google::protobuf::internal::WireFormatLite::TYPE_SINT32>(
                 2, 754, input, this->mutable_packed_sint32())));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(762)) goto parse_packed_sint64;
        break;
      }

      // repeated sint64 packed_sint64 = 95 [packed = true];
      case 95: {
        if (tag == 762) {
         parse_packed_sint64:
          DO_((::google::protobuf::internal::WireFormatLite::ReadPackedPrimitive<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_SINT64>(
                 input, this->mutable_packed_sint64())));
        } else if (tag == 760) {
          DO_((::google::protobuf::internal::WireFormatLite::ReadRepeatedPrimitiveNoInline<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_SINT64>(
                 2, 762, input, this->mutable_packed_sint64())));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(770)) goto parse_packed_fixed32;
        break;
      }

      // repeated fixed32 packed_fixed32 = 96 [packed = true];
      case 96: {
        if (tag == 770) {
         parse_packed_fixed32:
          DO_((::google::protobuf::internal::WireFormatLite::ReadPackedPrimitive<
                   ::google::protobuf::uint32, ::google::protobuf::internal::WireFormatLite::TYPE_FIXED32>(
                 input, this->mutable_packed_fixed32())));
        } else if (tag == 773) {
          DO_((::google::protobuf::internal::WireFormatLite::ReadRepeatedPrimitiveNoInline<
                   ::google::protobuf::uint32, ::google::protobuf::internal::WireFormatLite::TYPE_FIXED32>(
                 2, 770, input, this->mutable_packed_fixed32())));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(778)) goto parse_packed_fixed64;
        break;
      }

      // repeated fixed64 packed_fixed64 = 97 [packed = true];
      case 97: {
        if (tag == 778) {
         parse_packed_fixed64:
          DO_((::google::protobuf::internal::WireFormatLite::ReadPackedPrimitive<
                   ::google::protobuf::uint64, ::google::protobuf::internal::WireFormatLite::TYPE_FIXED64>(
                 input, this->mutable_packed_fixed64())));
        } else if (tag == 777) {
          DO_((::google::protobuf::internal::WireFormatLite::ReadRepeatedPrimitiveNoInline<
                   ::google::protobuf::uint64, ::google::protobuf::internal::WireFormatLite::TYPE_FIXED64>(
                 2, 778, input, this->mutable_packed_fixed64())));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(786)) goto parse_packed_sfixed32;
        break;
      }

      // repeated sfixed32 packed_sfixed32 = 98 [packed = true];
      case 98: {
        if (tag == 786) {
         parse_packed_sfixed32:
          DO_((::google::protobuf::internal::WireFormatLite::ReadPackedPrimitive<
                   ::google::protobuf::int32, ::google::protobuf::internal::WireFormatLite::TYPE_SFIXED32>(
                 input, this->mutable_packed_sfixed32())));
        } else if (tag == 789) {
          DO_((::google::protobuf::internal::WireFormatLite::ReadRepeatedPrimitiveNoInline<
                   ::google::protobuf::int32, ::google::protobuf::internal::WireFormatLite::TYPE_SFIXED32>(
                 2, 786, input, this->mutable_packed_sfixed32())));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(794)) goto parse_packed_sfixed64;
        break;
      }

      // repeated sfixed64 packed_sfixed64 = 99 [packed = true];
      case 99: {
        if (tag == 794) {
         parse_packed_sfixed64:
          DO_((::google::protobuf::internal::WireFormatLite::ReadPackedPrimitive<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_SFIXED64>(
                 input, this->mutable_packed_sfixed64())));
        } else if (tag == 793) {
          DO_((::google::protobuf::internal::WireFormatLite::ReadRepeatedPrimitiveNoInline<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_SFIXED64>(
                 2, 794, input, this->mutable_packed_sfixed64())));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(802)) goto parse_packed_float;
        break;
      }

      // repeated float packed_float = 100 [packed = true];
      case 100: {
        if (tag == 802) {
         parse_packed_float:
          DO_((::google::protobuf::internal::WireFormatLite::ReadPackedPrimitive<
                   float, ::google::protobuf::internal::WireFormatLite::TYPE_FLOAT>(
                 input, this->mutable_packed_float())));
        } else if (tag == 805) {
          DO_((::google::protobuf::internal::WireFormatLite::ReadRepeatedPrimitiveNoInline<
                   float, ::google::protobuf::internal::WireFormatLite::TYPE_FLOAT>(
                 2, 802, input, this->mutable_packed_float())));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(810)) goto parse_packed_double;
        break;
      }

      // repeated double packed_double = 101 [packed = true];
      case 101: {
        if (tag == 810) {
         parse_packed_double:
          DO_((::google::protobuf::internal::WireFormatLite::ReadPackedPrimitive<
                   double, ::google::protobuf::internal::WireFormatLite::TYPE_DOUBLE>(
                 input, this->mutable_packed_double())));
        } else if (tag == 809) {
          DO_((::google::protobuf::internal::WireFormatLite::ReadRepeatedPrimitiveNoInline<
                   double, ::google::protobuf::internal::WireFormatLite::TYPE_DOUBLE>(
                 2, 810, input, this->mutable_packed_double())));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(818)) goto parse_packed_bool;
        break;
      }

      // repeated bool packed_bool = 102 [packed = true];
      case 102: {
        if (tag == 818) {
         parse_packed_bool:
          DO_((::google::protobuf::internal::WireFormatLite::ReadPackedPrimitive<
                   bool, ::google::protobuf::internal::WireFormatLite::TYPE_BOOL>(
                 input, this->mutable_packed_bool())));
        } else if (tag == 816) {
          DO_((::google::protobuf::internal::WireFormatLite::ReadRepeatedPrimitiveNoInline<
                   bool, ::google::protobuf::internal::WireFormatLite::TYPE_BOOL>(
                 2, 818, input, this->mutable_packed_bool())));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(826)) goto parse_packed_enum;
        break;
      }

      // repeated .proto3_arena_lite_unittest.ForeignEnum packed_enum = 103 [packed = true];
      case 103: {
        if (tag == 826) {
         parse_packed_enum:
          ::google::protobuf::uint32 length;
          DO_(input->ReadVarint32(&length));
          ::google::protobuf::io::CodedInputStream::Limit limit = input->PushLimit(length);
          while (input->BytesUntilLimit() > 0) {
            int value;
            DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   int, ::google::protobuf::internal::WireFormatLite::TYPE_ENUM>(
                 input, &value)));
            add_packed_enum(static_cast< ::proto3_arena_lite_unittest::ForeignEnum >(value));
          }
          input->PopLimit(limit);
        } else if (tag == 824) {
          int value;
          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   int, ::google::protobuf::internal::WireFormatLite::TYPE_ENUM>(
                 input, &value)));
          add_packed_enum(static_cast< ::proto3_arena_lite_unittest::ForeignEnum >(value));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectAtEnd()) goto success;
        break;
      }

      default: {
      handle_unusual:
        if (tag == 0 ||
            ::google::protobuf::internal::WireFormatLite::GetTagWireType(tag) ==
            ::google::protobuf::internal::WireFormatLite::WIRETYPE_END_GROUP) {
          goto success;
        }
        DO_(::google::protobuf::internal::WireFormatLite::SkipField(input, tag));
        break;
      }
    }
  }
success:
  // @@protoc_insertion_point(parse_success:proto3_arena_lite_unittest.TestPackedTypes)
  return true;
failure:
  // @@protoc_insertion_point(parse_failure:proto3_arena_lite_unittest.TestPackedTypes)
  return false;
#undef DO_
}

void TestPackedTypes::SerializeWithCachedSizes(
    ::google::protobuf::io::CodedOutputStream* output) const {
  // @@protoc_insertion_point(serialize_start:proto3_arena_lite_unittest.TestPackedTypes)
  // repeated int32 packed_int32 = 90 [packed = true];
  if (this->packed_int32_size() > 0) {
    ::google::protobuf::internal::WireFormatLite::WriteTag(90, ::google::protobuf::internal::WireFormatLite::WIRETYPE_LENGTH_DELIMITED, output);
    output->WriteVarint32(_packed_int32_cached_byte_size_);
  }
  for (int i = 0; i < this->packed_int32_size(); i++) {
    ::google::protobuf::internal::WireFormatLite::WriteInt32NoTag(
      this->packed_int32(i), output);
  }

  // repeated int64 packed_int64 = 91 [packed = true];
  if (this->packed_int64_size() > 0) {
    ::google::protobuf::internal::WireFormatLite::WriteTag(91, ::google::protobuf::internal::WireFormatLite::WIRETYPE_LENGTH_DELIMITED, output);
    output->WriteVarint32(_packed_int64_cached_byte_size_);
  }
  for (int i = 0; i < this->packed_int64_size(); i++) {
    ::google::protobuf::internal::WireFormatLite::WriteInt64NoTag(
      this->packed_int64(i), output);
  }

  // repeated uint32 packed_uint32 = 92 [packed = true];
  if (this->packed_uint32_size() > 0) {
    ::google::protobuf::internal::WireFormatLite::WriteTag(92, ::google::protobuf::internal::WireFormatLite::WIRETYPE_LENGTH_DELIMITED, output);
    output->WriteVarint32(_packed_uint32_cached_byte_size_);
  }
  for (int i = 0; i < this->packed_uint32_size(); i++) {
    ::google::protobuf::internal::WireFormatLite::WriteUInt32NoTag(
      this->packed_uint32(i), output);
  }

  // repeated uint64 packed_uint64 = 93 [packed = true];
  if (this->packed_uint64_size() > 0) {
    ::google::protobuf::internal::WireFormatLite::WriteTag(93, ::google::protobuf::internal::WireFormatLite::WIRETYPE_LENGTH_DELIMITED, output);
    output->WriteVarint32(_packed_uint64_cached_byte_size_);
  }
  for (int i = 0; i < this->packed_uint64_size(); i++) {
    ::google::protobuf::internal::WireFormatLite::WriteUInt64NoTag(
      this->packed_uint64(i), output);
  }

  // repeated sint32 packed_sint32 = 94 [packed = true];
  if (this->packed_sint32_size() > 0) {
    ::google::protobuf::internal::WireFormatLite::WriteTag(94, ::google::protobuf::internal::WireFormatLite::WIRETYPE_LENGTH_DELIMITED, output);
    output->WriteVarint32(_packed_sint32_cached_byte_size_);
  }
  for (int i = 0; i < this->packed_sint32_size(); i++) {
    ::google::protobuf::internal::WireFormatLite::WriteSInt32NoTag(
      this->packed_sint32(i), output);
  }

  // repeated sint64 packed_sint64 = 95 [packed = true];
  if (this->packed_sint64_size() > 0) {
    ::google::protobuf::internal::WireFormatLite::WriteTag(95, ::google::protobuf::internal::WireFormatLite::WIRETYPE_LENGTH_DELIMITED, output);
    output->WriteVarint32(_packed_sint64_cached_byte_size_);
  }
  for (int i = 0; i < this->packed_sint64_size(); i++) {
    ::google::protobuf::internal::WireFormatLite::WriteSInt64NoTag(
      this->packed_sint64(i), output);
  }

  // repeated fixed32 packed_fixed32 = 96 [packed = true];
  if (this->packed_fixed32_size() > 0) {
    ::google::protobuf::internal::WireFormatLite::WriteTag(96, ::google::protobuf::internal::WireFormatLite::WIRETYPE_LENGTH_DELIMITED, output);
    output->WriteVarint32(_packed_fixed32_cached_byte_size_);
  }
  for (int i = 0; i < this->packed_fixed32_size(); i++) {
    ::google::protobuf::internal::WireFormatLite::WriteFixed32NoTag(
      this->packed_fixed32(i), output);
  }

  // repeated fixed64 packed_fixed64 = 97 [packed = true];
  if (this->packed_fixed64_size() > 0) {
    ::google::protobuf::internal::WireFormatLite::WriteTag(97, ::google::protobuf::internal::WireFormatLite::WIRETYPE_LENGTH_DELIMITED, output);
    output->WriteVarint32(_packed_fixed64_cached_byte_size_);
  }
  for (int i = 0; i < this->packed_fixed64_size(); i++) {
    ::google::protobuf::internal::WireFormatLite::WriteFixed64NoTag(
      this->packed_fixed64(i), output);
  }

  // repeated sfixed32 packed_sfixed32 = 98 [packed = true];
  if (this->packed_sfixed32_size() > 0) {
    ::google::protobuf::internal::WireFormatLite::WriteTag(98, ::google::protobuf::internal::WireFormatLite::WIRETYPE_LENGTH_DELIMITED, output);
    output->WriteVarint32(_packed_sfixed32_cached_byte_size_);
  }
  for (int i = 0; i < this->packed_sfixed32_size(); i++) {
    ::google::protobuf::internal::WireFormatLite::WriteSFixed32NoTag(
      this->packed_sfixed32(i), output);
  }

  // repeated sfixed64 packed_sfixed64 = 99 [packed = true];
  if (this->packed_sfixed64_size() > 0) {
    ::google::protobuf::internal::WireFormatLite::WriteTag(99, ::google::protobuf::internal::WireFormatLite::WIRETYPE_LENGTH_DELIMITED, output);
    output->WriteVarint32(_packed_sfixed64_cached_byte_size_);
  }
  for (int i = 0; i < this->packed_sfixed64_size(); i++) {
    ::google::protobuf::internal::WireFormatLite::WriteSFixed64NoTag(
      this->packed_sfixed64(i), output);
  }

  // repeated float packed_float = 100 [packed = true];
  if (this->packed_float_size() > 0) {
    ::google::protobuf::internal::WireFormatLite::WriteTag(100, ::google::protobuf::internal::WireFormatLite::WIRETYPE_LENGTH_DELIMITED, output);
    output->WriteVarint32(_packed_float_cached_byte_size_);
  }
  for (int i = 0; i < this->packed_float_size(); i++) {
    ::google::protobuf::internal::WireFormatLite::WriteFloatNoTag(
      this->packed_float(i), output);
  }

  // repeated double packed_double = 101 [packed = true];
  if (this->packed_double_size() > 0) {
    ::google::protobuf::internal::WireFormatLite::WriteTag(101, ::google::protobuf::internal::WireFormatLite::WIRETYPE_LENGTH_DELIMITED, output);
    output->WriteVarint32(_packed_double_cached_byte_size_);
  }
  for (int i = 0; i < this->packed_double_size(); i++) {
    ::google::protobuf::internal::WireFormatLite::WriteDoubleNoTag(
      this->packed_double(i), output);
  }

  // repeated bool packed_bool = 102 [packed = true];
  if (this->packed_bool_size() > 0) {
    ::google::protobuf::internal::WireFormatLite::WriteTag(102, ::google::protobuf::internal::WireFormatLite::WIRETYPE_LENGTH_DELIMITED, output);
    output->WriteVarint32(_packed_bool_cached_byte_size_);
  }
  for (int i = 0; i < this->packed_bool_size(); i++) {
    ::google::protobuf::internal::WireFormatLite::WriteBoolNoTag(
      this->packed_bool(i), output);
  }

  // repeated .proto3_arena_lite_unittest.ForeignEnum packed_enum = 103 [packed = true];
  if (this->packed_enum_size() > 0) {
    ::google::protobuf::internal::WireFormatLite::WriteTag(
      103,
      ::google::protobuf::internal::WireFormatLite::WIRETYPE_LENGTH_DELIMITED,
      output);
    output->WriteVarint32(_packed_enum_cached_byte_size_);
  }
  for (int i = 0; i < this->packed_enum_size(); i++) {
    ::google::protobuf::internal::WireFormatLite::WriteEnumNoTag(
      this->packed_enum(i), output);
  }

  // @@protoc_insertion_point(serialize_end:proto3_arena_lite_unittest.TestPackedTypes)
}

size_t TestPackedTypes::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:proto3_arena_lite_unittest.TestPackedTypes)
  size_t total_size = 0;

  // repeated int32 packed_int32 = 90 [packed = true];
  {
    size_t data_size = 0;
    unsigned int count = this->packed_int32_size();
    for (unsigned int i = 0; i < count; i++) {
      data_size += ::google::protobuf::internal::WireFormatLite::
        Int32Size(this->packed_int32(i));
    }
    if (data_size > 0) {
      total_size += 2 +
        ::google::protobuf::internal::WireFormatLite::Int32Size(data_size);
    }
    int cached_size = ::google::protobuf::internal::ToCachedSize(data_size);
    GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
    _packed_int32_cached_byte_size_ = cached_size;
    GOOGLE_SAFE_CONCURRENT_WRITES_END();
    total_size += data_size;
  }

  // repeated int64 packed_int64 = 91 [packed = true];
  {
    size_t data_size = 0;
    unsigned int count = this->packed_int64_size();
    for (unsigned int i = 0; i < count; i++) {
      data_size += ::google::protobuf::internal::WireFormatLite::
        Int64Size(this->packed_int64(i));
    }
    if (data_size > 0) {
      total_size += 2 +
        ::google::protobuf::internal::WireFormatLite::Int32Size(data_size);
    }
    int cached_size = ::google::protobuf::internal::ToCachedSize(data_size);
    GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
    _packed_int64_cached_byte_size_ = cached_size;
    GOOGLE_SAFE_CONCURRENT_WRITES_END();
    total_size += data_size;
  }

  // repeated uint32 packed_uint32 = 92 [packed = true];
  {
    size_t data_size = 0;
    unsigned int count = this->packed_uint32_size();
    for (unsigned int i = 0; i < count; i++) {
      data_size += ::google::protobuf::internal::WireFormatLite::
        UInt32Size(this->packed_uint32(i));
    }
    if (data_size > 0) {
      total_size += 2 +
        ::google::protobuf::internal::WireFormatLite::Int32Size(data_size);
    }
    int cached_size = ::google::protobuf::internal::ToCachedSize(data_size);
    GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
    _packed_uint32_cached_byte_size_ = cached_size;
    GOOGLE_SAFE_CONCURRENT_WRITES_END();
    total_size += data_size;
  }

  // repeated uint64 packed_uint64 = 93 [packed = true];
  {
    size_t data_size = 0;
    unsigned int count = this->packed_uint64_size();
    for (unsigned int i = 0; i < count; i++) {
      data_size += ::google::protobuf::internal::WireFormatLite::
        UInt64Size(this->packed_uint64(i));
    }
    if (data_size > 0) {
      total_size += 2 +
        ::google::protobuf::internal::WireFormatLite::Int32Size(data_size);
    }
    int cached_size = ::google::protobuf::internal::ToCachedSize(data_size);
    GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
    _packed_uint64_cached_byte_size_ = cached_size;
    GOOGLE_SAFE_CONCURRENT_WRITES_END();
    total_size += data_size;
  }

  // repeated sint32 packed_sint32 = 94 [packed = true];
  {
    size_t data_size = 0;
    unsigned int count = this->packed_sint32_size();
    for (unsigned int i = 0; i < count; i++) {
      data_size += ::google::protobuf::internal::WireFormatLite::
        SInt32Size(this->packed_sint32(i));
    }
    if (data_size > 0) {
      total_size += 2 +
        ::google::protobuf::internal::WireFormatLite::Int32Size(data_size);
    }
    int cached_size = ::google::protobuf::internal::ToCachedSize(data_size);
    GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
    _packed_sint32_cached_byte_size_ = cached_size;
    GOOGLE_SAFE_CONCURRENT_WRITES_END();
    total_size += data_size;
  }

  // repeated sint64 packed_sint64 = 95 [packed = true];
  {
    size_t data_size = 0;
    unsigned int count = this->packed_sint64_size();
    for (unsigned int i = 0; i < count; i++) {
      data_size += ::google::protobuf::internal::WireFormatLite::
        SInt64Size(this->packed_sint64(i));
    }
    if (data_size > 0) {
      total_size += 2 +
        ::google::protobuf::internal::WireFormatLite::Int32Size(data_size);
    }
    int cached_size = ::google::protobuf::internal::ToCachedSize(data_size);
    GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
    _packed_sint64_cached_byte_size_ = cached_size;
    GOOGLE_SAFE_CONCURRENT_WRITES_END();
    total_size += data_size;
  }

  // repeated fixed32 packed_fixed32 = 96 [packed = true];
  {
    size_t data_size = 0;
    unsigned int count = this->packed_fixed32_size();
    data_size = 4UL * count;
    if (data_size > 0) {
      total_size += 2 +
        ::google::protobuf::internal::WireFormatLite::Int32Size(data_size);
    }
    int cached_size = ::google::protobuf::internal::ToCachedSize(data_size);
    GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
    _packed_fixed32_cached_byte_size_ = cached_size;
    GOOGLE_SAFE_CONCURRENT_WRITES_END();
    total_size += data_size;
  }

  // repeated fixed64 packed_fixed64 = 97 [packed = true];
  {
    size_t data_size = 0;
    unsigned int count = this->packed_fixed64_size();
    data_size = 8UL * count;
    if (data_size > 0) {
      total_size += 2 +
        ::google::protobuf::internal::WireFormatLite::Int32Size(data_size);
    }
    int cached_size = ::google::protobuf::internal::ToCachedSize(data_size);
    GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
    _packed_fixed64_cached_byte_size_ = cached_size;
    GOOGLE_SAFE_CONCURRENT_WRITES_END();
    total_size += data_size;
  }

  // repeated sfixed32 packed_sfixed32 = 98 [packed = true];
  {
    size_t data_size = 0;
    unsigned int count = this->packed_sfixed32_size();
    data_size = 4UL * count;
    if (data_size > 0) {
      total_size += 2 +
        ::google::protobuf::internal::WireFormatLite::Int32Size(data_size);
    }
    int cached_size = ::google::protobuf::internal::ToCachedSize(data_size);
    GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
    _packed_sfixed32_cached_byte_size_ = cached_size;
    GOOGLE_SAFE_CONCURRENT_WRITES_END();
    total_size += data_size;
  }

  // repeated sfixed64 packed_sfixed64 = 99 [packed = true];
  {
    size_t data_size = 0;
    unsigned int count = this->packed_sfixed64_size();
    data_size = 8UL * count;
    if (data_size > 0) {
      total_size += 2 +
        ::google::protobuf::internal::WireFormatLite::Int32Size(data_size);
    }
    int cached_size = ::google::protobuf::internal::ToCachedSize(data_size);
    GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
    _packed_sfixed64_cached_byte_size_ = cached_size;
    GOOGLE_SAFE_CONCURRENT_WRITES_END();
    total_size += data_size;
  }

  // repeated float packed_float = 100 [packed = true];
  {
    size_t data_size = 0;
    unsigned int count = this->packed_float_size();
    data_size = 4UL * count;
    if (data_size > 0) {
      total_size += 2 +
        ::google::protobuf::internal::WireFormatLite::Int32Size(data_size);
    }
    int cached_size = ::google::protobuf::internal::ToCachedSize(data_size);
    GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
    _packed_float_cached_byte_size_ = cached_size;
    GOOGLE_SAFE_CONCURRENT_WRITES_END();
    total_size += data_size;
  }

  // repeated double packed_double = 101 [packed = true];
  {
    size_t data_size = 0;
    unsigned int count = this->packed_double_size();
    data_size = 8UL * count;
    if (data_size > 0) {
      total_size += 2 +
        ::google::protobuf::internal::WireFormatLite::Int32Size(data_size);
    }
    int cached_size = ::google::protobuf::internal::ToCachedSize(data_size);
    GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
    _packed_double_cached_byte_size_ = cached_size;
    GOOGLE_SAFE_CONCURRENT_WRITES_END();
    total_size += data_size;
  }

  // repeated bool packed_bool = 102 [packed = true];
  {
    size_t data_size = 0;
    unsigned int count = this->packed_bool_size();
    data_size = 1UL * count;
    if (data_size > 0) {
      total_size += 2 +
        ::google::protobuf::internal::WireFormatLite::Int32Size(data_size);
    }
    int cached_size = ::google::protobuf::internal::ToCachedSize(data_size);
    GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
    _packed_bool_cached_byte_size_ = cached_size;
    GOOGLE_SAFE_CONCURRENT_WRITES_END();
    total_size += data_size;
  }

  // repeated .proto3_arena_lite_unittest.ForeignEnum packed_enum = 103 [packed = true];
  {
    size_t data_size = 0;
    unsigned int count = this->packed_enum_size();for (unsigned int i = 0; i < count; i++) {
      data_size += ::google::protobuf::internal::WireFormatLite::EnumSize(
        this->packed_enum(i));
    }
    if (data_size > 0) {
      total_size += 2 +
        ::google::protobuf::internal::WireFormatLite::Int32Size(data_size);
    }
    int cached_size = ::google::protobuf::internal::ToCachedSize(data_size);
    GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
    _packed_enum_cached_byte_size_ = cached_size;
    GOOGLE_SAFE_CONCURRENT_WRITES_END();
    total_size += data_size;
  }

  int cached_size = ::google::protobuf::internal::ToCachedSize(total_size);
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = cached_size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
  return total_size;
}

void TestPackedTypes::CheckTypeAndMergeFrom(
    const ::google::protobuf::MessageLite& from) {
  MergeFrom(*::google::protobuf::down_cast<const TestPackedTypes*>(&from));
}

void TestPackedTypes::MergeFrom(const TestPackedTypes& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:proto3_arena_lite_unittest.TestPackedTypes)
  if (GOOGLE_PREDICT_TRUE(&from != this)) {
    UnsafeMergeFrom(from);
  } else {
    MergeFromFail(__LINE__);
  }
}

void TestPackedTypes::UnsafeMergeFrom(const TestPackedTypes& from) {
  GOOGLE_DCHECK(&from != this);
  packed_int32_.UnsafeMergeFrom(from.packed_int32_);
  packed_int64_.UnsafeMergeFrom(from.packed_int64_);
  packed_uint32_.UnsafeMergeFrom(from.packed_uint32_);
  packed_uint64_.UnsafeMergeFrom(from.packed_uint64_);
  packed_sint32_.UnsafeMergeFrom(from.packed_sint32_);
  packed_sint64_.UnsafeMergeFrom(from.packed_sint64_);
  packed_fixed32_.UnsafeMergeFrom(from.packed_fixed32_);
  packed_fixed64_.UnsafeMergeFrom(from.packed_fixed64_);
  packed_sfixed32_.UnsafeMergeFrom(from.packed_sfixed32_);
  packed_sfixed64_.UnsafeMergeFrom(from.packed_sfixed64_);
  packed_float_.UnsafeMergeFrom(from.packed_float_);
  packed_double_.UnsafeMergeFrom(from.packed_double_);
  packed_bool_.UnsafeMergeFrom(from.packed_bool_);
  packed_enum_.UnsafeMergeFrom(from.packed_enum_);
}

void TestPackedTypes::CopyFrom(const TestPackedTypes& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:proto3_arena_lite_unittest.TestPackedTypes)
  if (&from == this) return;
  Clear();
  UnsafeMergeFrom(from);
}

bool TestPackedTypes::IsInitialized() const {

  return true;
}

void TestPackedTypes::Swap(TestPackedTypes* other) {
  if (other == this) return;
  if (GetArenaNoVirtual() == other->GetArenaNoVirtual()) {
    InternalSwap(other);
  } else {
    TestPackedTypes temp;
    temp.UnsafeMergeFrom(*this);
    CopyFrom(*other);
    other->CopyFrom(temp);
  }
}
void TestPackedTypes::UnsafeArenaSwap(TestPackedTypes* other) {
  if (other == this) return;
  GOOGLE_DCHECK(GetArenaNoVirtual() == other->GetArenaNoVirtual());
  InternalSwap(other);
}
void TestPackedTypes::InternalSwap(TestPackedTypes* other) {
  packed_int32_.UnsafeArenaSwap(&other->packed_int32_);
  packed_int64_.UnsafeArenaSwap(&other->packed_int64_);
  packed_uint32_.UnsafeArenaSwap(&other->packed_uint32_);
  packed_uint64_.UnsafeArenaSwap(&other->packed_uint64_);
  packed_sint32_.UnsafeArenaSwap(&other->packed_sint32_);
  packed_sint64_.UnsafeArenaSwap(&other->packed_sint64_);
  packed_fixed32_.UnsafeArenaSwap(&other->packed_fixed32_);
  packed_fixed64_.UnsafeArenaSwap(&other->packed_fixed64_);
  packed_sfixed32_.UnsafeArenaSwap(&other->packed_sfixed32_);
  packed_sfixed64_.UnsafeArenaSwap(&other->packed_sfixed64_);
  packed_float_.UnsafeArenaSwap(&other->packed_float_);
  packed_double_.UnsafeArenaSwap(&other->packed_double_);
  packed_bool_.UnsafeArenaSwap(&other->packed_bool_);
  packed_enum_.UnsafeArenaSwap(&other->packed_enum_);
  _unknown_fields_.Swap(&other->_unknown_fields_);
  std::swap(_cached_size_, other->_cached_size_);
}

::std::string TestPackedTypes::GetTypeName() const {
  return "proto3_arena_lite_unittest.TestPackedTypes";
}

#if PROTOBUF_INLINE_NOT_IN_HEADERS
// TestPackedTypes

// repeated int32 packed_int32 = 90 [packed = true];
int TestPackedTypes::packed_int32_size() const {
  return packed_int32_.size();
}
void TestPackedTypes::clear_packed_int32() {
  packed_int32_.Clear();
}
::google::protobuf::int32 TestPackedTypes::packed_int32(int index) const {
  // @@protoc_insertion_point(field_get:proto3_arena_lite_unittest.TestPackedTypes.packed_int32)
  return packed_int32_.Get(index);
}
void TestPackedTypes::set_packed_int32(int index, ::google::protobuf::int32 value) {
  packed_int32_.Set(index, value);
  // @@protoc_insertion_point(field_set:proto3_arena_lite_unittest.TestPackedTypes.packed_int32)
}
void TestPackedTypes::add_packed_int32(::google::protobuf::int32 value) {
  packed_int32_.Add(value);
  // @@protoc_insertion_point(field_add:proto3_arena_lite_unittest.TestPackedTypes.packed_int32)
}
const ::google::protobuf::RepeatedField< ::google::protobuf::int32 >&
TestPackedTypes::packed_int32() const {
  // @@protoc_insertion_point(field_list:proto3_arena_lite_unittest.TestPackedTypes.packed_int32)
  return packed_int32_;
}
::google::protobuf::RepeatedField< ::google::protobuf::int32 >*
TestPackedTypes::mutable_packed_int32() {
  // @@protoc_insertion_point(field_mutable_list:proto3_arena_lite_unittest.TestPackedTypes.packed_int32)
  return &packed_int32_;
}

// repeated int64 packed_int64 = 91 [packed = true];
int TestPackedTypes::packed_int64_size() const {
  return packed_int64_.size();
}
void TestPackedTypes::clear_packed_int64() {
  packed_int64_.Clear();
}
::google::protobuf::int64 TestPackedTypes::packed_int64(int index) const {
  // @@protoc_insertion_point(field_get:proto3_arena_lite_unittest.TestPackedTypes.packed_int64)
  return packed_int64_.Get(index);
}
void TestPackedTypes::set_packed_int64(int index, ::google::protobuf::int64 value) {
  packed_int64_.Set(index, value);
  // @@protoc_insertion_point(field_set:proto3_arena_lite_unittest.TestPackedTypes.packed_int64)
}
void TestPackedTypes::add_packed_int64(::google::protobuf::int64 value) {
  packed_int64_.Add(value);
  // @@protoc_insertion_point(field_add:proto3_arena_lite_unittest.TestPackedTypes.packed_int64)
}
const ::google::protobuf::RepeatedField< ::google::protobuf::int64 >&
TestPackedTypes::packed_int64() const {
  // @@protoc_insertion_point(field_list:proto3_arena_lite_unittest.TestPackedTypes.packed_int64)
  return packed_int64_;
}
::google::protobuf::RepeatedField< ::google::protobuf::int64 >*
TestPackedTypes::mutable_packed_int64() {
  // @@protoc_insertion_point(field_mutable_list:proto3_arena_lite_unittest.TestPackedTypes.packed_int64)
  return &packed_int64_;
}

// repeated uint32 packed_uint32 = 92 [packed = true];
int TestPackedTypes::packed_uint32_size() const {
  return packed_uint32_.size();
}
void TestPackedTypes::clear_packed_uint32() {
  packed_uint32_.Clear();
}
::google::protobuf::uint32 TestPackedTypes::packed_uint32(int index) const {
  // @@protoc_insertion_point(field_get:proto3_arena_lite_unittest.TestPackedTypes.packed_uint32)
  return packed_uint32_.Get(index);
}
void TestPackedTypes::set_packed_uint32(int index, ::google::protobuf::uint32 value) {
  packed_uint32_.Set(index, value);
  // @@protoc_insertion_point(field_set:proto3_arena_lite_unittest.TestPackedTypes.packed_uint32)
}
void TestPackedTypes::add_packed_uint32(::google::protobuf::uint32 value) {
  packed_uint32_.Add(value);
  // @@protoc_insertion_point(field_add:proto3_arena_lite_unittest.TestPackedTypes.packed_uint32)
}
const ::google::protobuf::RepeatedField< ::google::protobuf::uint32 >&
TestPackedTypes::packed_uint32() const {
  // @@protoc_insertion_point(field_list:proto3_arena_lite_unittest.TestPackedTypes.packed_uint32)
  return packed_uint32_;
}
::google::protobuf::RepeatedField< ::google::protobuf::uint32 >*
TestPackedTypes::mutable_packed_uint32() {
  // @@protoc_insertion_point(field_mutable_list:proto3_arena_lite_unittest.TestPackedTypes.packed_uint32)
  return &packed_uint32_;
}

// repeated uint64 packed_uint64 = 93 [packed = true];
int TestPackedTypes::packed_uint64_size() const {
  return packed_uint64_.size();
}
void TestPackedTypes::clear_packed_uint64() {
  packed_uint64_.Clear();
}
::google::protobuf::uint64 TestPackedTypes::packed_uint64(int index) const {
  // @@protoc_insertion_point(field_get:proto3_arena_lite_unittest.TestPackedTypes.packed_uint64)
  return packed_uint64_.Get(index);
}
void TestPackedTypes::set_packed_uint64(int index, ::google::protobuf::uint64 value) {
  packed_uint64_.Set(index, value);
  // @@protoc_insertion_point(field_set:proto3_arena_lite_unittest.TestPackedTypes.packed_uint64)
}
void TestPackedTypes::add_packed_uint64(::google::protobuf::uint64 value) {
  packed_uint64_.Add(value);
  // @@protoc_insertion_point(field_add:proto3_arena_lite_unittest.TestPackedTypes.packed_uint64)
}
const ::google::protobuf::RepeatedField< ::google::protobuf::uint64 >&
TestPackedTypes::packed_uint64() const {
  // @@protoc_insertion_point(field_list:proto3_arena_lite_unittest.TestPackedTypes.packed_uint64)
  return packed_uint64_;
}
::google::protobuf::RepeatedField< ::google::protobuf::uint64 >*
TestPackedTypes::mutable_packed_uint64() {
  // @@protoc_insertion_point(field_mutable_list:proto3_arena_lite_unittest.TestPackedTypes.packed_uint64)
  return &packed_uint64_;
}

// repeated sint32 packed_sint32 = 94 [packed = true];
int TestPackedTypes::packed_sint32_size() const {
  return packed_sint32_.size();
}
void TestPackedTypes::clear_packed_sint32() {
  packed_sint32_.Clear();
}
::google::protobuf::int32 TestPackedTypes::packed_sint32(int index) const {
  // @@protoc_insertion_point(field_get:proto3_arena_lite_unittest.TestPackedTypes.packed_sint32)
  return packed_sint32_.Get(index);
}
void TestPackedTypes::set_packed_sint32(int index, ::google::protobuf::int32 value) {
  packed_sint32_.Set(index, value);
  // @@protoc_insertion_point(field_set:proto3_arena_lite_unittest.TestPackedTypes.packed_sint32)
}
void TestPackedTypes::add_packed_sint32(::google::protobuf::int32 value) {
  packed_sint32_.Add(value);
  // @@protoc_insertion_point(field_add:proto3_arena_lite_unittest.TestPackedTypes.packed_sint32)
}
const ::google::protobuf::RepeatedField< ::google::protobuf::int32 >&
TestPackedTypes::packed_sint32() const {
  // @@protoc_insertion_point(field_list:proto3_arena_lite_unittest.TestPackedTypes.packed_sint32)
  return packed_sint32_;
}
::google::protobuf::RepeatedField< ::google::protobuf::int32 >*
TestPackedTypes::mutable_packed_sint32() {
  // @@protoc_insertion_point(field_mutable_list:proto3_arena_lite_unittest.TestPackedTypes.packed_sint32)
  return &packed_sint32_;
}

// repeated sint64 packed_sint64 = 95 [packed = true];
int TestPackedTypes::packed_sint64_size() const {
  return packed_sint64_.size();
}
void TestPackedTypes::clear_packed_sint64() {
  packed_sint64_.Clear();
}
::google::protobuf::int64 TestPackedTypes::packed_sint64(int index) const {
  // @@protoc_insertion_point(field_get:proto3_arena_lite_unittest.TestPackedTypes.packed_sint64)
  return packed_sint64_.Get(index);
}
void TestPackedTypes::set_packed_sint64(int index, ::google::protobuf::int64 value) {
  packed_sint64_.Set(index, value);
  // @@protoc_insertion_point(field_set:proto3_arena_lite_unittest.TestPackedTypes.packed_sint64)
}
void TestPackedTypes::add_packed_sint64(::google::protobuf::int64 value) {
  packed_sint64_.Add(value);
  // @@protoc_insertion_point(field_add:proto3_arena_lite_unittest.TestPackedTypes.packed_sint64)
}
const ::google::protobuf::RepeatedField< ::google::protobuf::int64 >&
TestPackedTypes::packed_sint64() const {
  // @@protoc_insertion_point(field_list:proto3_arena_lite_unittest.TestPackedTypes.packed_sint64)
  return packed_sint64_;
}
::google::protobuf::RepeatedField< ::google::protobuf::int64 >*
TestPackedTypes::mutable_packed_sint64() {
  // @@protoc_insertion_point(field_mutable_list:proto3_arena_lite_unittest.TestPackedTypes.packed_sint64)
  return &packed_sint64_;
}

// repeated fixed32 packed_fixed32 = 96 [packed = true];
int TestPackedTypes::packed_fixed32_size() const {
  return packed_fixed32_.size();
}
void TestPackedTypes::clear_packed_fixed32() {
  packed_fixed32_.Clear();
}
::google::protobuf::uint32 TestPackedTypes::packed_fixed32(int index) const {
  // @@protoc_insertion_point(field_get:proto3_arena_lite_unittest.TestPackedTypes.packed_fixed32)
  return packed_fixed32_.Get(index);
}
void TestPackedTypes::set_packed_fixed32(int index, ::google::protobuf::uint32 value) {
  packed_fixed32_.Set(index, value);
  // @@protoc_insertion_point(field_set:proto3_arena_lite_unittest.TestPackedTypes.packed_fixed32)
}
void TestPackedTypes::add_packed_fixed32(::google::protobuf::uint32 value) {
  packed_fixed32_.Add(value);
  // @@protoc_insertion_point(field_add:proto3_arena_lite_unittest.TestPackedTypes.packed_fixed32)
}
const ::google::protobuf::RepeatedField< ::google::protobuf::uint32 >&
TestPackedTypes::packed_fixed32() const {
  // @@protoc_insertion_point(field_list:proto3_arena_lite_unittest.TestPackedTypes.packed_fixed32)
  return packed_fixed32_;
}
::google::protobuf::RepeatedField< ::google::protobuf::uint32 >*
TestPackedTypes::mutable_packed_fixed32() {
  // @@protoc_insertion_point(field_mutable_list:proto3_arena_lite_unittest.TestPackedTypes.packed_fixed32)
  return &packed_fixed32_;
}

// repeated fixed64 packed_fixed64 = 97 [packed = true];
int TestPackedTypes::packed_fixed64_size() const {
  return packed_fixed64_.size();
}
void TestPackedTypes::clear_packed_fixed64() {
  packed_fixed64_.Clear();
}
::google::protobuf::uint64 TestPackedTypes::packed_fixed64(int index) const {
  // @@protoc_insertion_point(field_get:proto3_arena_lite_unittest.TestPackedTypes.packed_fixed64)
  return packed_fixed64_.Get(index);
}
void TestPackedTypes::set_packed_fixed64(int index, ::google::protobuf::uint64 value) {
  packed_fixed64_.Set(index, value);
  // @@protoc_insertion_point(field_set:proto3_arena_lite_unittest.TestPackedTypes.packed_fixed64)
}
void TestPackedTypes::add_packed_fixed64(::google::protobuf::uint64 value) {
  packed_fixed64_.Add(value);
  // @@protoc_insertion_point(field_add:proto3_arena_lite_unittest.TestPackedTypes.packed_fixed64)
}
const ::google::protobuf::RepeatedField< ::google::protobuf::uint64 >&
TestPackedTypes::packed_fixed64() const {
  // @@protoc_insertion_point(field_list:proto3_arena_lite_unittest.TestPackedTypes.packed_fixed64)
  return packed_fixed64_;
}
::google::protobuf::RepeatedField< ::google::protobuf::uint64 >*
TestPackedTypes::mutable_packed_fixed64() {
  // @@protoc_insertion_point(field_mutable_list:proto3_arena_lite_unittest.TestPackedTypes.packed_fixed64)
  return &packed_fixed64_;
}

// repeated sfixed32 packed_sfixed32 = 98 [packed = true];
int TestPackedTypes::packed_sfixed32_size() const {
  return packed_sfixed32_.size();
}
void TestPackedTypes::clear_packed_sfixed32() {
  packed_sfixed32_.Clear();
}
::google::protobuf::int32 TestPackedTypes::packed_sfixed32(int index) const {
  // @@protoc_insertion_point(field_get:proto3_arena_lite_unittest.TestPackedTypes.packed_sfixed32)
  return packed_sfixed32_.Get(index);
}
void TestPackedTypes::set_packed_sfixed32(int index, ::google::protobuf::int32 value) {
  packed_sfixed32_.Set(index, value);
  // @@protoc_insertion_point(field_set:proto3_arena_lite_unittest.TestPackedTypes.packed_sfixed32)
}
void TestPackedTypes::add_packed_sfixed32(::google::protobuf::int32 value) {
  packed_sfixed32_.Add(value);
  // @@protoc_insertion_point(field_add:proto3_arena_lite_unittest.TestPackedTypes.packed_sfixed32)
}
const ::google::protobuf::RepeatedField< ::google::protobuf::int32 >&
TestPackedTypes::packed_sfixed32() const {
  // @@protoc_insertion_point(field_list:proto3_arena_lite_unittest.TestPackedTypes.packed_sfixed32)
  return packed_sfixed32_;
}
::google::protobuf::RepeatedField< ::google::protobuf::int32 >*
TestPackedTypes::mutable_packed_sfixed32() {
  // @@protoc_insertion_point(field_mutable_list:proto3_arena_lite_unittest.TestPackedTypes.packed_sfixed32)
  return &packed_sfixed32_;
}

// repeated sfixed64 packed_sfixed64 = 99 [packed = true];
int TestPackedTypes::packed_sfixed64_size() const {
  return packed_sfixed64_.size();
}
void TestPackedTypes::clear_packed_sfixed64() {
  packed_sfixed64_.Clear();
}
::google::protobuf::int64 TestPackedTypes::packed_sfixed64(int index) const {
  // @@protoc_insertion_point(field_get:proto3_arena_lite_unittest.TestPackedTypes.packed_sfixed64)
  return packed_sfixed64_.Get(index);
}
void TestPackedTypes::set_packed_sfixed64(int index, ::google::protobuf::int64 value) {
  packed_sfixed64_.Set(index, value);
  // @@protoc_insertion_point(field_set:proto3_arena_lite_unittest.TestPackedTypes.packed_sfixed64)
}
void TestPackedTypes::add_packed_sfixed64(::google::protobuf::int64 value) {
  packed_sfixed64_.Add(value);
  // @@protoc_insertion_point(field_add:proto3_arena_lite_unittest.TestPackedTypes.packed_sfixed64)
}
const ::google::protobuf::RepeatedField< ::google::protobuf::int64 >&
TestPackedTypes::packed_sfixed64() const {
  // @@protoc_insertion_point(field_list:proto3_arena_lite_unittest.TestPackedTypes.packed_sfixed64)
  return packed_sfixed64_;
}
::google::protobuf::RepeatedField< ::google::protobuf::int64 >*
TestPackedTypes::mutable_packed_sfixed64() {
  // @@protoc_insertion_point(field_mutable_list:proto3_arena_lite_unittest.TestPackedTypes.packed_sfixed64)
  return &packed_sfixed64_;
}

// repeated float packed_float = 100 [packed = true];
int TestPackedTypes::packed_float_size() const {
  return packed_float_.size();
}
void TestPackedTypes::clear_packed_float() {
  packed_float_.Clear();
}
float TestPackedTypes::packed_float(int index) const {
  // @@protoc_insertion_point(field_get:proto3_arena_lite_unittest.TestPackedTypes.packed_float)
  return packed_float_.Get(index);
}
void TestPackedTypes::set_packed_float(int index, float value) {
  packed_float_.Set(index, value);
  // @@protoc_insertion_point(field_set:proto3_arena_lite_unittest.TestPackedTypes.packed_float)
}
void TestPackedTypes::add_packed_float(float value) {
  packed_float_.Add(value);
  // @@protoc_insertion_point(field_add:proto3_arena_lite_unittest.TestPackedTypes.packed_float)
}
const ::google::protobuf::RepeatedField< float >&
TestPackedTypes::packed_float() const {
  // @@protoc_insertion_point(field_list:proto3_arena_lite_unittest.TestPackedTypes.packed_float)
  return packed_float_;
}
::google::protobuf::RepeatedField< float >*
TestPackedTypes::mutable_packed_float() {
  // @@protoc_insertion_point(field_mutable_list:proto3_arena_lite_unittest.TestPackedTypes.packed_float)
  return &packed_float_;
}

// repeated double packed_double = 101 [packed = true];
int TestPackedTypes::packed_double_size() const {
  return packed_double_.size();
}
void TestPackedTypes::clear_packed_double() {
  packed_double_.Clear();
}
double TestPackedTypes::packed_double(int index) const {
  // @@protoc_insertion_point(field_get:proto3_arena_lite_unittest.TestPackedTypes.packed_double)
  return packed_double_.Get(index);
}
void TestPackedTypes::set_packed_double(int index, double value) {
  packed_double_.Set(index, value);
  // @@protoc_insertion_point(field_set:proto3_arena_lite_unittest.TestPackedTypes.packed_double)
}
void TestPackedTypes::add_packed_double(double value) {
  packed_double_.Add(value);
  // @@protoc_insertion_point(field_add:proto3_arena_lite_unittest.TestPackedTypes.packed_double)
}
const ::google::protobuf::RepeatedField< double >&
TestPackedTypes::packed_double() const {
  // @@protoc_insertion_point(field_list:proto3_arena_lite_unittest.TestPackedTypes.packed_double)
  return packed_double_;
}
::google::protobuf::RepeatedField< double >*
TestPackedTypes::mutable_packed_double() {
  // @@protoc_insertion_point(field_mutable_list:proto3_arena_lite_unittest.TestPackedTypes.packed_double)
  return &packed_double_;
}

// repeated bool packed_bool = 102 [packed = true];
int TestPackedTypes::packed_bool_size() const {
  return packed_bool_.size();
}
void TestPackedTypes::clear_packed_bool() {
  packed_bool_.Clear();
}
bool TestPackedTypes::packed_bool(int index) const {
  // @@protoc_insertion_point(field_get:proto3_arena_lite_unittest.TestPackedTypes.packed_bool)
  return packed_bool_.Get(index);
}
void TestPackedTypes::set_packed_bool(int index, bool value) {
  packed_bool_.Set(index, value);
  // @@protoc_insertion_point(field_set:proto3_arena_lite_unittest.TestPackedTypes.packed_bool)
}
void TestPackedTypes::add_packed_bool(bool value) {
  packed_bool_.Add(value);
  // @@protoc_insertion_point(field_add:proto3_arena_lite_unittest.TestPackedTypes.packed_bool)
}
const ::google::protobuf::RepeatedField< bool >&
TestPackedTypes::packed_bool() const {
  // @@protoc_insertion_point(field_list:proto3_arena_lite_unittest.TestPackedTypes.packed_bool)
  return packed_bool_;
}
::google::protobuf::RepeatedField< bool >*
TestPackedTypes::mutable_packed_bool() {
  // @@protoc_insertion_point(field_mutable_list:proto3_arena_lite_unittest.TestPackedTypes.packed_bool)
  return &packed_bool_;
}

// repeated .proto3_arena_lite_unittest.ForeignEnum packed_enum = 103 [packed = true];
int TestPackedTypes::packed_enum_size() const {
  return packed_enum_.size();
}
void TestPackedTypes::clear_packed_enum() {
  packed_enum_.Clear();
}
::proto3_arena_lite_unittest::ForeignEnum TestPackedTypes::packed_enum(int index) const {
  // @@protoc_insertion_point(field_get:proto3_arena_lite_unittest.TestPackedTypes.packed_enum)
  return static_cast< ::proto3_arena_lite_unittest::ForeignEnum >(packed_enum_.Get(index));
}
void TestPackedTypes::set_packed_enum(int index, ::proto3_arena_lite_unittest::ForeignEnum value) {
  packed_enum_.Set(index, value);
  // @@protoc_insertion_point(field_set:proto3_arena_lite_unittest.TestPackedTypes.packed_enum)
}
void TestPackedTypes::add_packed_enum(::proto3_arena_lite_unittest::ForeignEnum value) {
  packed_enum_.Add(value);
  // @@protoc_insertion_point(field_add:proto3_arena_lite_unittest.TestPackedTypes.packed_enum)
}
const ::google::protobuf::RepeatedField<int>&
TestPackedTypes::packed_enum() const {
  // @@protoc_insertion_point(field_list:proto3_arena_lite_unittest.TestPackedTypes.packed_enum)
  return packed_enum_;
}
::google::protobuf::RepeatedField<int>*
TestPackedTypes::mutable_packed_enum() {
  // @@protoc_insertion_point(field_mutable_list:proto3_arena_lite_unittest.TestPackedTypes.packed_enum)
  return &packed_enum_;
}

inline const TestPackedTypes* TestPackedTypes::internal_default_instance() {
  return &TestPackedTypes_default_instance_.get();
}
#endif  // PROTOBUF_INLINE_NOT_IN_HEADERS

// ===================================================================

#if !defined(_MSC_VER) || _MSC_VER >= 1900
const int TestUnpackedTypes::kRepeatedInt32FieldNumber;
const int TestUnpackedTypes::kRepeatedInt64FieldNumber;
const int TestUnpackedTypes::kRepeatedUint32FieldNumber;
const int TestUnpackedTypes::kRepeatedUint64FieldNumber;
const int TestUnpackedTypes::kRepeatedSint32FieldNumber;
const int TestUnpackedTypes::kRepeatedSint64FieldNumber;
const int TestUnpackedTypes::kRepeatedFixed32FieldNumber;
const int TestUnpackedTypes::kRepeatedFixed64FieldNumber;
const int TestUnpackedTypes::kRepeatedSfixed32FieldNumber;
const int TestUnpackedTypes::kRepeatedSfixed64FieldNumber;
const int TestUnpackedTypes::kRepeatedFloatFieldNumber;
const int TestUnpackedTypes::kRepeatedDoubleFieldNumber;
const int TestUnpackedTypes::kRepeatedBoolFieldNumber;
const int TestUnpackedTypes::kRepeatedNestedEnumFieldNumber;
#endif  // !defined(_MSC_VER) || _MSC_VER >= 1900

TestUnpackedTypes::TestUnpackedTypes()
  : ::google::protobuf::MessageLite(), _arena_ptr_(NULL) {
  if (this != internal_default_instance()) protobuf_InitDefaults_google_2fprotobuf_2funittest_5fproto3_5farena_5flite_2eproto();
  SharedCtor();
  // @@protoc_insertion_point(constructor:proto3_arena_lite_unittest.TestUnpackedTypes)
}
TestUnpackedTypes::TestUnpackedTypes(::google::protobuf::Arena* arena)
  : ::google::protobuf::MessageLite(),
  _arena_ptr_(arena),
  repeated_int32_(arena),
  repeated_int64_(arena),
  repeated_uint32_(arena),
  repeated_uint64_(arena),
  repeated_sint32_(arena),
  repeated_sint64_(arena),
  repeated_fixed32_(arena),
  repeated_fixed64_(arena),
  repeated_sfixed32_(arena),
  repeated_sfixed64_(arena),
  repeated_float_(arena),
  repeated_double_(arena),
  repeated_bool_(arena),
  repeated_nested_enum_(arena) {
#ifdef GOOGLE_PROTOBUF_NO_STATIC_INITIALIZER
  protobuf_InitDefaults_google_2fprotobuf_2funittest_5fproto3_5farena_5flite_2eproto();
#endif  // GOOGLE_PROTOBUF_NO_STATIC_INITIALIZER
  SharedCtor();
  RegisterArenaDtor(arena);
  // @@protoc_insertion_point(arena_constructor:proto3_arena_lite_unittest.TestUnpackedTypes)
}

void TestUnpackedTypes::InitAsDefaultInstance() {
}

TestUnpackedTypes::TestUnpackedTypes(const TestUnpackedTypes& from)
  : ::google::protobuf::MessageLite(),
    _arena_ptr_(NULL) {
  SharedCtor();
  UnsafeMergeFrom(from);
  // @@protoc_insertion_point(copy_constructor:proto3_arena_lite_unittest.TestUnpackedTypes)
}

void TestUnpackedTypes::SharedCtor() {
  _cached_size_ = 0;
}

TestUnpackedTypes::~TestUnpackedTypes() {
  // @@protoc_insertion_point(destructor:proto3_arena_lite_unittest.TestUnpackedTypes)
  SharedDtor();
}

void TestUnpackedTypes::SharedDtor() {
  ::google::protobuf::Arena* arena = GetArenaNoVirtual();
  if (arena != NULL) {
    return;
  }

}

void TestUnpackedTypes::ArenaDtor(void* object) {
  TestUnpackedTypes* _this = reinterpret_cast< TestUnpackedTypes* >(object);
  (void)_this;
}
void TestUnpackedTypes::RegisterArenaDtor(::google::protobuf::Arena* arena) {
}
void TestUnpackedTypes::SetCachedSize(int size) const {
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
}
const TestUnpackedTypes& TestUnpackedTypes::default_instance() {
  protobuf_InitDefaults_google_2fprotobuf_2funittest_5fproto3_5farena_5flite_2eproto();
  return *internal_default_instance();
}

::google::protobuf::internal::ExplicitlyConstructed<TestUnpackedTypes> TestUnpackedTypes_default_instance_;

TestUnpackedTypes* TestUnpackedTypes::New(::google::protobuf::Arena* arena) const {
  return ::google::protobuf::Arena::CreateMessage<TestUnpackedTypes>(arena);
}

void TestUnpackedTypes::Clear() {
// @@protoc_insertion_point(message_clear_start:proto3_arena_lite_unittest.TestUnpackedTypes)
  repeated_int32_.Clear();
  repeated_int64_.Clear();
  repeated_uint32_.Clear();
  repeated_uint64_.Clear();
  repeated_sint32_.Clear();
  repeated_sint64_.Clear();
  repeated_fixed32_.Clear();
  repeated_fixed64_.Clear();
  repeated_sfixed32_.Clear();
  repeated_sfixed64_.Clear();
  repeated_float_.Clear();
  repeated_double_.Clear();
  repeated_bool_.Clear();
  repeated_nested_enum_.Clear();
}

bool TestUnpackedTypes::MergePartialFromCodedStream(
    ::google::protobuf::io::CodedInputStream* input) {
#define DO_(EXPRESSION) if (!GOOGLE_PREDICT_TRUE(EXPRESSION)) goto failure
  ::google::protobuf::uint32 tag;
  // @@protoc_insertion_point(parse_start:proto3_arena_lite_unittest.TestUnpackedTypes)
  for (;;) {
    ::std::pair< ::google::protobuf::uint32, bool> p = input->ReadTagWithCutoff(127);
    tag = p.first;
    if (!p.second) goto handle_unusual;
    switch (::google::protobuf::internal::WireFormatLite::GetTagFieldNumber(tag)) {
      // repeated int32 repeated_int32 = 1 [packed = false];
      case 1: {
        if (tag == 8) {
         parse_repeated_int32:
          DO_((::google::protobuf::internal::WireFormatLite::ReadRepeatedPrimitive<
                   ::google::protobuf::int32, ::google::protobuf::internal::WireFormatLite::TYPE_INT32>(
                 1, 8, input, this->mutable_repeated_int32())));
        } else if (tag == 10) {
          DO_((::google::protobuf::internal::WireFormatLite::ReadPackedPrimitiveNoInline<
                   ::google::protobuf::int32, ::google::protobuf::internal::WireFormatLite::TYPE_INT32>(
                 input, this->mutable_repeated_int32())));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(8)) goto parse_repeated_int32;
        if (input->ExpectTag(16)) goto parse_repeated_int64;
        break;
      }

      // repeated int64 repeated_int64 = 2 [packed = false];
      case 2: {
        if (tag == 16) {
         parse_repeated_int64:
          DO_((::google::protobuf::internal::WireFormatLite::ReadRepeatedPrimitive<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_INT64>(
                 1, 16, input, this->mutable_repeated_int64())));
        } else if (tag == 18) {
          DO_((::google::protobuf::internal::WireFormatLite::ReadPackedPrimitiveNoInline<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_INT64>(
                 input, this->mutable_repeated_int64())));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(16)) goto parse_repeated_int64;
        if (input->ExpectTag(24)) goto parse_repeated_uint32;
        break;
      }

      // repeated uint32 repeated_uint32 = 3 [packed = false];
      case 3: {
        if (tag == 24) {
         parse_repeated_uint32:
          DO_((::google::protobuf::internal::WireFormatLite::ReadRepeatedPrimitive<
                   ::google::protobuf::uint32, ::google::protobuf::internal::WireFormatLite::TYPE_UINT32>(
                 1, 24, input, this->mutable_repeated_uint32())));
        } else if (tag == 26) {
          DO_((::google::protobuf::internal::WireFormatLite::ReadPackedPrimitiveNoInline<
                   ::google::protobuf::uint32, ::google::protobuf::internal::WireFormatLite::TYPE_UINT32>(
                 input, this->mutable_repeated_uint32())));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(24)) goto parse_repeated_uint32;
        if (input->ExpectTag(32)) goto parse_repeated_uint64;
        break;
      }

      // repeated uint64 repeated_uint64 = 4 [packed = false];
      case 4: {
        if (tag == 32) {
         parse_repeated_uint64:
          DO_((::google::protobuf::internal::WireFormatLite::ReadRepeatedPrimitive<
                   ::google::protobuf::uint64, ::google::protobuf::internal::WireFormatLite::TYPE_UINT64>(
                 1, 32, input, this->mutable_repeated_uint64())));
        } else if (tag == 34) {
          DO_((::google::protobuf::internal::WireFormatLite::ReadPackedPrimitiveNoInline<
                   ::google::protobuf::uint64, ::google::protobuf::internal::WireFormatLite::TYPE_UINT64>(
                 input, this->mutable_repeated_uint64())));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(32)) goto parse_repeated_uint64;
        if (input->ExpectTag(40)) goto parse_repeated_sint32;
        break;
      }

      // repeated sint32 repeated_sint32 = 5 [packed = false];
      case 5: {
        if (tag == 40) {
         parse_repeated_sint32:
          DO_((::google::protobuf::internal::WireFormatLite::ReadRepeatedPrimitive<
                   ::google::protobuf::int32, ::google::protobuf::internal::WireFormatLite::TYPE_SINT32>(
                 1, 40, input, this->mutable_repeated_sint32())));
        } else if (tag == 42) {
          DO_((::google::protobuf::internal::WireFormatLite::ReadPackedPrimitiveNoInline<
                   ::google::protobuf::int32, ::google::protobuf::internal::WireFormatLite::TYPE_SINT32>(
                 input, this->mutable_repeated_sint32())));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(40)) goto parse_repeated_sint32;
        if (input->ExpectTag(48)) goto parse_repeated_sint64;
        break;
      }

      // repeated sint64 repeated_sint64 = 6 [packed = false];
      case 6: {
        if (tag == 48) {
         parse_repeated_sint64:
          DO_((::google::protobuf::internal::WireFormatLite::ReadRepeatedPrimitive<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_SINT64>(
                 1, 48, input, this->mutable_repeated_sint64())));
        } else if (tag == 50) {
          DO_((::google::protobuf::internal::WireFormatLite::ReadPackedPrimitiveNoInline<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_SINT64>(
                 input, this->mutable_repeated_sint64())));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(48)) goto parse_repeated_sint64;
        if (input->ExpectTag(61)) goto parse_repeated_fixed32;
        break;
      }

      // repeated fixed32 repeated_fixed32 = 7 [packed = false];
      case 7: {
        if (tag == 61) {
         parse_repeated_fixed32:
          DO_((::google::protobuf::internal::WireFormatLite::ReadRepeatedPrimitive<
                   ::google::protobuf::uint32, ::google::protobuf::internal::WireFormatLite::TYPE_FIXED32>(
                 1, 61, input, this->mutable_repeated_fixed32())));
        } else if (tag == 58) {
          DO_((::google::protobuf::internal::WireFormatLite::ReadPackedPrimitiveNoInline<
                   ::google::protobuf::uint32, ::google::protobuf::internal::WireFormatLite::TYPE_FIXED32>(
                 input, this->mutable_repeated_fixed32())));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(61)) goto parse_repeated_fixed32;
        if (input->ExpectTag(65)) goto parse_repeated_fixed64;
        break;
      }

      // repeated fixed64 repeated_fixed64 = 8 [packed = false];
      case 8: {
        if (tag == 65) {
         parse_repeated_fixed64:
          DO_((::google::protobuf::internal::WireFormatLite::ReadRepeatedPrimitive<
                   ::google::protobuf::uint64, ::google::protobuf::internal::WireFormatLite::TYPE_FIXED64>(
                 1, 65, input, this->mutable_repeated_fixed64())));
        } else if (tag == 66) {
          DO_((::google::protobuf::internal::WireFormatLite::ReadPackedPrimitiveNoInline<
                   ::google::protobuf::uint64, ::google::protobuf::internal::WireFormatLite::TYPE_FIXED64>(
                 input, this->mutable_repeated_fixed64())));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(65)) goto parse_repeated_fixed64;
        if (input->ExpectTag(77)) goto parse_repeated_sfixed32;
        break;
      }

      // repeated sfixed32 repeated_sfixed32 = 9 [packed = false];
      case 9: {
        if (tag == 77) {
         parse_repeated_sfixed32:
          DO_((::google::protobuf::internal::WireFormatLite::ReadRepeatedPrimitive<
                   ::google::protobuf::int32, ::google::protobuf::internal::WireFormatLite::TYPE_SFIXED32>(
                 1, 77, input, this->mutable_repeated_sfixed32())));
        } else if (tag == 74) {
          DO_((::google::protobuf::internal::WireFormatLite::ReadPackedPrimitiveNoInline<
                   ::google::protobuf::int32, ::google::protobuf::internal::WireFormatLite::TYPE_SFIXED32>(
                 input, this->mutable_repeated_sfixed32())));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(77)) goto parse_repeated_sfixed32;
        if (input->ExpectTag(81)) goto parse_repeated_sfixed64;
        break;
      }

      // repeated sfixed64 repeated_sfixed64 = 10 [packed = false];
      case 10: {
        if (tag == 81) {
         parse_repeated_sfixed64:
          DO_((::google::protobuf::internal::WireFormatLite::ReadRepeatedPrimitive<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_SFIXED64>(
                 1, 81, input, this->mutable_repeated_sfixed64())));
        } else if (tag == 82) {
          DO_((::google::protobuf::internal::WireFormatLite::ReadPackedPrimitiveNoInline<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_SFIXED64>(
                 input, this->mutable_repeated_sfixed64())));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(81)) goto parse_repeated_sfixed64;
        if (input->ExpectTag(93)) goto parse_repeated_float;
        break;
      }

      // repeated float repeated_float = 11 [packed = false];
      case 11: {
        if (tag == 93) {
         parse_repeated_float:
          DO_((::google::protobuf::internal::WireFormatLite::ReadRepeatedPrimitive<
                   float, ::google::protobuf::internal::WireFormatLite::TYPE_FLOAT>(
                 1, 93, input, this->mutable_repeated_float())));
        } else if (tag == 90) {
          DO_((::google::protobuf::internal::WireFormatLite::ReadPackedPrimitiveNoInline<
                   float, ::google::protobuf::internal::WireFormatLite::TYPE_FLOAT>(
                 input, this->mutable_repeated_float())));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(93)) goto parse_repeated_float;
        if (input->ExpectTag(97)) goto parse_repeated_double;
        break;
      }

      // repeated double repeated_double = 12 [packed = false];
      case 12: {
        if (tag == 97) {
         parse_repeated_double:
          DO_((::google::protobuf::internal::WireFormatLite::ReadRepeatedPrimitive<
                   double, ::google::protobuf::internal::WireFormatLite::TYPE_DOUBLE>(
                 1, 97, input, this->mutable_repeated_double())));
        } else if (tag == 98) {
          DO_((::google::protobuf::internal::WireFormatLite::ReadPackedPrimitiveNoInline<
                   double, ::google::protobuf::internal::WireFormatLite::TYPE_DOUBLE>(
                 input, this->mutable_repeated_double())));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(97)) goto parse_repeated_double;
        if (input->ExpectTag(104)) goto parse_repeated_bool;
        break;
      }

      // repeated bool repeated_bool = 13 [packed = false];
      case 13: {
        if (tag == 104) {
         parse_repeated_bool:
          DO_((::google::protobuf::internal::WireFormatLite::ReadRepeatedPrimitive<
                   bool, ::google::protobuf::internal::WireFormatLite::TYPE_BOOL>(
                 1, 104, input, this->mutable_repeated_bool())));
        } else if (tag == 106) {
          DO_((::google::protobuf::internal::WireFormatLite::ReadPackedPrimitiveNoInline<
                   bool, ::google::protobuf::internal::WireFormatLite::TYPE_BOOL>(
                 input, this->mutable_repeated_bool())));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(104)) goto parse_repeated_bool;
        if (input->ExpectTag(112)) goto parse_repeated_nested_enum;
        break;
      }

      // repeated .proto3_arena_lite_unittest.TestAllTypes.NestedEnum repeated_nested_enum = 14 [packed = false];
      case 14: {
        if (tag == 112) {
         parse_repeated_nested_enum:
          int value;
          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   int, ::google::protobuf::internal::WireFormatLite::TYPE_ENUM>(
                 input, &value)));
          add_repeated_nested_enum(static_cast< ::proto3_arena_lite_unittest::TestAllTypes_NestedEnum >(value));
        } else if (tag == 114) {
          DO_((::google::protobuf::internal::WireFormatLite::ReadPackedEnumPreserveUnknowns(
                 input,
                 14,
                 NULL,
                 NULL,
                 this->mutable_repeated_nested_enum())));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(112)) goto parse_repeated_nested_enum;
        if (input->ExpectAtEnd()) goto success;
        break;
      }

      default: {
      handle_unusual:
        if (tag == 0 ||
            ::google::protobuf::internal::WireFormatLite::GetTagWireType(tag) ==
            ::google::protobuf::internal::WireFormatLite::WIRETYPE_END_GROUP) {
          goto success;
        }
        DO_(::google::protobuf::internal::WireFormatLite::SkipField(input, tag));
        break;
      }
    }
  }
success:
  // @@protoc_insertion_point(parse_success:proto3_arena_lite_unittest.TestUnpackedTypes)
  return true;
failure:
  // @@protoc_insertion_point(parse_failure:proto3_arena_lite_unittest.TestUnpackedTypes)
  return false;
#undef DO_
}

void TestUnpackedTypes::SerializeWithCachedSizes(
    ::google::protobuf::io::CodedOutputStream* output) const {
  // @@protoc_insertion_point(serialize_start:proto3_arena_lite_unittest.TestUnpackedTypes)
  // repeated int32 repeated_int32 = 1 [packed = false];
  for (int i = 0; i < this->repeated_int32_size(); i++) {
    ::google::protobuf::internal::WireFormatLite::WriteInt32(
      1, this->repeated_int32(i), output);
  }

  // repeated int64 repeated_int64 = 2 [packed = false];
  for (int i = 0; i < this->repeated_int64_size(); i++) {
    ::google::protobuf::internal::WireFormatLite::WriteInt64(
      2, this->repeated_int64(i), output);
  }

  // repeated uint32 repeated_uint32 = 3 [packed = false];
  for (int i = 0; i < this->repeated_uint32_size(); i++) {
    ::google::protobuf::internal::WireFormatLite::WriteUInt32(
      3, this->repeated_uint32(i), output);
  }

  // repeated uint64 repeated_uint64 = 4 [packed = false];
  for (int i = 0; i < this->repeated_uint64_size(); i++) {
    ::google::protobuf::internal::WireFormatLite::WriteUInt64(
      4, this->repeated_uint64(i), output);
  }

  // repeated sint32 repeated_sint32 = 5 [packed = false];
  for (int i = 0; i < this->repeated_sint32_size(); i++) {
    ::google::protobuf::internal::WireFormatLite::WriteSInt32(
      5, this->repeated_sint32(i), output);
  }

  // repeated sint64 repeated_sint64 = 6 [packed = false];
  for (int i = 0; i < this->repeated_sint64_size(); i++) {
    ::google::protobuf::internal::WireFormatLite::WriteSInt64(
      6, this->repeated_sint64(i), output);
  }

  // repeated fixed32 repeated_fixed32 = 7 [packed = false];
  for (int i = 0; i < this->repeated_fixed32_size(); i++) {
    ::google::protobuf::internal::WireFormatLite::WriteFixed32(
      7, this->repeated_fixed32(i), output);
  }

  // repeated fixed64 repeated_fixed64 = 8 [packed = false];
  for (int i = 0; i < this->repeated_fixed64_size(); i++) {
    ::google::protobuf::internal::WireFormatLite::WriteFixed64(
      8, this->repeated_fixed64(i), output);
  }

  // repeated sfixed32 repeated_sfixed32 = 9 [packed = false];
  for (int i = 0; i < this->repeated_sfixed32_size(); i++) {
    ::google::protobuf::internal::WireFormatLite::WriteSFixed32(
      9, this->repeated_sfixed32(i), output);
  }

  // repeated sfixed64 repeated_sfixed64 = 10 [packed = false];
  for (int i = 0; i < this->repeated_sfixed64_size(); i++) {
    ::google::protobuf::internal::WireFormatLite::WriteSFixed64(
      10, this->repeated_sfixed64(i), output);
  }

  // repeated float repeated_float = 11 [packed = false];
  for (int i = 0; i < this->repeated_float_size(); i++) {
    ::google::protobuf::internal::WireFormatLite::WriteFloat(
      11, this->repeated_float(i), output);
  }

  // repeated double repeated_double = 12 [packed = false];
  for (int i = 0; i < this->repeated_double_size(); i++) {
    ::google::protobuf::internal::WireFormatLite::WriteDouble(
      12, this->repeated_double(i), output);
  }

  // repeated bool repeated_bool = 13 [packed = false];
  for (int i = 0; i < this->repeated_bool_size(); i++) {
    ::google::protobuf::internal::WireFormatLite::WriteBool(
      13, this->repeated_bool(i), output);
  }

  // repeated .proto3_arena_lite_unittest.TestAllTypes.NestedEnum repeated_nested_enum = 14 [packed = false];
  for (int i = 0; i < this->repeated_nested_enum_size(); i++) {
    ::google::protobuf::internal::WireFormatLite::WriteEnum(
      14, this->repeated_nested_enum(i), output);
  }

  // @@protoc_insertion_point(serialize_end:proto3_arena_lite_unittest.TestUnpackedTypes)
}

size_t TestUnpackedTypes::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:proto3_arena_lite_unittest.TestUnpackedTypes)
  size_t total_size = 0;

  // repeated int32 repeated_int32 = 1 [packed = false];
  {
    size_t data_size = 0;
    unsigned int count = this->repeated_int32_size();
    for (unsigned int i = 0; i < count; i++) {
      data_size += ::google::protobuf::internal::WireFormatLite::
        Int32Size(this->repeated_int32(i));
    }
    total_size += 1 *
                  ::google::protobuf::internal::FromIntSize(this->repeated_int32_size());
    total_size += data_size;
  }

  // repeated int64 repeated_int64 = 2 [packed = false];
  {
    size_t data_size = 0;
    unsigned int count = this->repeated_int64_size();
    for (unsigned int i = 0; i < count; i++) {
      data_size += ::google::protobuf::internal::WireFormatLite::
        Int64Size(this->repeated_int64(i));
    }
    total_size += 1 *
                  ::google::protobuf::internal::FromIntSize(this->repeated_int64_size());
    total_size += data_size;
  }

  // repeated uint32 repeated_uint32 = 3 [packed = false];
  {
    size_t data_size = 0;
    unsigned int count = this->repeated_uint32_size();
    for (unsigned int i = 0; i < count; i++) {
      data_size += ::google::protobuf::internal::WireFormatLite::
        UInt32Size(this->repeated_uint32(i));
    }
    total_size += 1 *
                  ::google::protobuf::internal::FromIntSize(this->repeated_uint32_size());
    total_size += data_size;
  }

  // repeated uint64 repeated_uint64 = 4 [packed = false];
  {
    size_t data_size = 0;
    unsigned int count = this->repeated_uint64_size();
    for (unsigned int i = 0; i < count; i++) {
      data_size += ::google::protobuf::internal::WireFormatLite::
        UInt64Size(this->repeated_uint64(i));
    }
    total_size += 1 *
                  ::google::protobuf::internal::FromIntSize(this->repeated_uint64_size());
    total_size += data_size;
  }

  // repeated sint32 repeated_sint32 = 5 [packed = false];
  {
    size_t data_size = 0;
    unsigned int count = this->repeated_sint32_size();
    for (unsigned int i = 0; i < count; i++) {
      data_size += ::google::protobuf::internal::WireFormatLite::
        SInt32Size(this->repeated_sint32(i));
    }
    total_size += 1 *
                  ::google::protobuf::internal::FromIntSize(this->repeated_sint32_size());
    total_size += data_size;
  }

  // repeated sint64 repeated_sint64 = 6 [packed = false];
  {
    size_t data_size = 0;
    unsigned int count = this->repeated_sint64_size();
    for (unsigned int i = 0; i < count; i++) {
      data_size += ::google::protobuf::internal::WireFormatLite::
        SInt64Size(this->repeated_sint64(i));
    }
    total_size += 1 *
                  ::google::protobuf::internal::FromIntSize(this->repeated_sint64_size());
    total_size += data_size;
  }

  // repeated fixed32 repeated_fixed32 = 7 [packed = false];
  {
    size_t data_size = 0;
    unsigned int count = this->repeated_fixed32_size();
    data_size = 4UL * count;
    total_size += 1 *
                  ::google::protobuf::internal::FromIntSize(this->repeated_fixed32_size());
    total_size += data_size;
  }

  // repeated fixed64 repeated_fixed64 = 8 [packed = false];
  {
    size_t data_size = 0;
    unsigned int count = this->repeated_fixed64_size();
    data_size = 8UL * count;
    total_size += 1 *
                  ::google::protobuf::internal::FromIntSize(this->repeated_fixed64_size());
    total_size += data_size;
  }

  // repeated sfixed32 repeated_sfixed32 = 9 [packed = false];
  {
    size_t data_size = 0;
    unsigned int count = this->repeated_sfixed32_size();
    data_size = 4UL * count;
    total_size += 1 *
                  ::google::protobuf::internal::FromIntSize(this->repeated_sfixed32_size());
    total_size += data_size;
  }

  // repeated sfixed64 repeated_sfixed64 = 10 [packed = false];
  {
    size_t data_size = 0;
    unsigned int count = this->repeated_sfixed64_size();
    data_size = 8UL * count;
    total_size += 1 *
                  ::google::protobuf::internal::FromIntSize(this->repeated_sfixed64_size());
    total_size += data_size;
  }

  // repeated float repeated_float = 11 [packed = false];
  {
    size_t data_size = 0;
    unsigned int count = this->repeated_float_size();
    data_size = 4UL * count;
    total_size += 1 *
                  ::google::protobuf::internal::FromIntSize(this->repeated_float_size());
    total_size += data_size;
  }

  // repeated double repeated_double = 12 [packed = false];
  {
    size_t data_size = 0;
    unsigned int count = this->repeated_double_size();
    data_size = 8UL * count;
    total_size += 1 *
                  ::google::protobuf::internal::FromIntSize(this->repeated_double_size());
    total_size += data_size;
  }

  // repeated bool repeated_bool = 13 [packed = false];
  {
    size_t data_size = 0;
    unsigned int count = this->repeated_bool_size();
    data_size = 1UL * count;
    total_size += 1 *
                  ::google::protobuf::internal::FromIntSize(this->repeated_bool_size());
    total_size += data_size;
  }

  // repeated .proto3_arena_lite_unittest.TestAllTypes.NestedEnum repeated_nested_enum = 14 [packed = false];
  {
    size_t data_size = 0;
    unsigned int count = this->repeated_nested_enum_size();for (unsigned int i = 0; i < count; i++) {
      data_size += ::google::protobuf::internal::WireFormatLite::EnumSize(
        this->repeated_nested_enum(i));
    }
    total_size += (1UL * count) + data_size;
  }

  int cached_size = ::google::protobuf::internal::ToCachedSize(total_size);
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = cached_size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
  return total_size;
}

void TestUnpackedTypes::CheckTypeAndMergeFrom(
    const ::google::protobuf::MessageLite& from) {
  MergeFrom(*::google::protobuf::down_cast<const TestUnpackedTypes*>(&from));
}

void TestUnpackedTypes::MergeFrom(const TestUnpackedTypes& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:proto3_arena_lite_unittest.TestUnpackedTypes)
  if (GOOGLE_PREDICT_TRUE(&from != this)) {
    UnsafeMergeFrom(from);
  } else {
    MergeFromFail(__LINE__);
  }
}

void TestUnpackedTypes::UnsafeMergeFrom(const TestUnpackedTypes& from) {
  GOOGLE_DCHECK(&from != this);
  repeated_int32_.UnsafeMergeFrom(from.repeated_int32_);
  repeated_int64_.UnsafeMergeFrom(from.repeated_int64_);
  repeated_uint32_.UnsafeMergeFrom(from.repeated_uint32_);
  repeated_uint64_.UnsafeMergeFrom(from.repeated_uint64_);
  repeated_sint32_.UnsafeMergeFrom(from.repeated_sint32_);
  repeated_sint64_.UnsafeMergeFrom(from.repeated_sint64_);
  repeated_fixed32_.UnsafeMergeFrom(from.repeated_fixed32_);
  repeated_fixed64_.UnsafeMergeFrom(from.repeated_fixed64_);
  repeated_sfixed32_.UnsafeMergeFrom(from.repeated_sfixed32_);
  repeated_sfixed64_.UnsafeMergeFrom(from.repeated_sfixed64_);
  repeated_float_.UnsafeMergeFrom(from.repeated_float_);
  repeated_double_.UnsafeMergeFrom(from.repeated_double_);
  repeated_bool_.UnsafeMergeFrom(from.repeated_bool_);
  repeated_nested_enum_.UnsafeMergeFrom(from.repeated_nested_enum_);
}

void TestUnpackedTypes::CopyFrom(const TestUnpackedTypes& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:proto3_arena_lite_unittest.TestUnpackedTypes)
  if (&from == this) return;
  Clear();
  UnsafeMergeFrom(from);
}

bool TestUnpackedTypes::IsInitialized() const {

  return true;
}

void TestUnpackedTypes::Swap(TestUnpackedTypes* other) {
  if (other == this) return;
  if (GetArenaNoVirtual() == other->GetArenaNoVirtual()) {
    InternalSwap(other);
  } else {
    TestUnpackedTypes temp;
    temp.UnsafeMergeFrom(*this);
    CopyFrom(*other);
    other->CopyFrom(temp);
  }
}
void TestUnpackedTypes::UnsafeArenaSwap(TestUnpackedTypes* other) {
  if (other == this) return;
  GOOGLE_DCHECK(GetArenaNoVirtual() == other->GetArenaNoVirtual());
  InternalSwap(other);
}
void TestUnpackedTypes::InternalSwap(TestUnpackedTypes* other) {
  repeated_int32_.UnsafeArenaSwap(&other->repeated_int32_);
  repeated_int64_.UnsafeArenaSwap(&other->repeated_int64_);
  repeated_uint32_.UnsafeArenaSwap(&other->repeated_uint32_);
  repeated_uint64_.UnsafeArenaSwap(&other->repeated_uint64_);
  repeated_sint32_.UnsafeArenaSwap(&other->repeated_sint32_);
  repeated_sint64_.UnsafeArenaSwap(&other->repeated_sint64_);
  repeated_fixed32_.UnsafeArenaSwap(&other->repeated_fixed32_);
  repeated_fixed64_.UnsafeArenaSwap(&other->repeated_fixed64_);
  repeated_sfixed32_.UnsafeArenaSwap(&other->repeated_sfixed32_);
  repeated_sfixed64_.UnsafeArenaSwap(&other->repeated_sfixed64_);
  repeated_float_.UnsafeArenaSwap(&other->repeated_float_);
  repeated_double_.UnsafeArenaSwap(&other->repeated_double_);
  repeated_bool_.UnsafeArenaSwap(&other->repeated_bool_);
  repeated_nested_enum_.UnsafeArenaSwap(&other->repeated_nested_enum_);
  _unknown_fields_.Swap(&other->_unknown_fields_);
  std::swap(_cached_size_, other->_cached_size_);
}

::std::string TestUnpackedTypes::GetTypeName() const {
  return "proto3_arena_lite_unittest.TestUnpackedTypes";
}

#if PROTOBUF_INLINE_NOT_IN_HEADERS
// TestUnpackedTypes

// repeated int32 repeated_int32 = 1 [packed = false];
int TestUnpackedTypes::repeated_int32_size() const {
  return repeated_int32_.size();
}
void TestUnpackedTypes::clear_repeated_int32() {
  repeated_int32_.Clear();
}
::google::protobuf::int32 TestUnpackedTypes::repeated_int32(int index) const {
  // @@protoc_insertion_point(field_get:proto3_arena_lite_unittest.TestUnpackedTypes.repeated_int32)
  return repeated_int32_.Get(index);
}
void TestUnpackedTypes::set_repeated_int32(int index, ::google::protobuf::int32 value) {
  repeated_int32_.Set(index, value);
  // @@protoc_insertion_point(field_set:proto3_arena_lite_unittest.TestUnpackedTypes.repeated_int32)
}
void TestUnpackedTypes::add_repeated_int32(::google::protobuf::int32 value) {
  repeated_int32_.Add(value);
  // @@protoc_insertion_point(field_add:proto3_arena_lite_unittest.TestUnpackedTypes.repeated_int32)
}
const ::google::protobuf::RepeatedField< ::google::protobuf::int32 >&
TestUnpackedTypes::repeated_int32() const {
  // @@protoc_insertion_point(field_list:proto3_arena_lite_unittest.TestUnpackedTypes.repeated_int32)
  return repeated_int32_;
}
::google::protobuf::RepeatedField< ::google::protobuf::int32 >*
TestUnpackedTypes::mutable_repeated_int32() {
  // @@protoc_insertion_point(field_mutable_list:proto3_arena_lite_unittest.TestUnpackedTypes.repeated_int32)
  return &repeated_int32_;
}

// repeated int64 repeated_int64 = 2 [packed = false];
int TestUnpackedTypes::repeated_int64_size() const {
  return repeated_int64_.size();
}
void TestUnpackedTypes::clear_repeated_int64() {
  repeated_int64_.Clear();
}
::google::protobuf::int64 TestUnpackedTypes::repeated_int64(int index) const {
  // @@protoc_insertion_point(field_get:proto3_arena_lite_unittest.TestUnpackedTypes.repeated_int64)
  return repeated_int64_.Get(index);
}
void TestUnpackedTypes::set_repeated_int64(int index, ::google::protobuf::int64 value) {
  repeated_int64_.Set(index, value);
  // @@protoc_insertion_point(field_set:proto3_arena_lite_unittest.TestUnpackedTypes.repeated_int64)
}
void TestUnpackedTypes::add_repeated_int64(::google::protobuf::int64 value) {
  repeated_int64_.Add(value);
  // @@protoc_insertion_point(field_add:proto3_arena_lite_unittest.TestUnpackedTypes.repeated_int64)
}
const ::google::protobuf::RepeatedField< ::google::protobuf::int64 >&
TestUnpackedTypes::repeated_int64() const {
  // @@protoc_insertion_point(field_list:proto3_arena_lite_unittest.TestUnpackedTypes.repeated_int64)
  return repeated_int64_;
}
::google::protobuf::RepeatedField< ::google::protobuf::int64 >*
TestUnpackedTypes::mutable_repeated_int64() {
  // @@protoc_insertion_point(field_mutable_list:proto3_arena_lite_unittest.TestUnpackedTypes.repeated_int64)
  return &repeated_int64_;
}

// repeated uint32 repeated_uint32 = 3 [packed = false];
int TestUnpackedTypes::repeated_uint32_size() const {
  return repeated_uint32_.size();
}
void TestUnpackedTypes::clear_repeated_uint32() {
  repeated_uint32_.Clear();
}
::google::protobuf::uint32 TestUnpackedTypes::repeated_uint32(int index) const {
  // @@protoc_insertion_point(field_get:proto3_arena_lite_unittest.TestUnpackedTypes.repeated_uint32)
  return repeated_uint32_.Get(index);
}
void TestUnpackedTypes::set_repeated_uint32(int index, ::google::protobuf::uint32 value) {
  repeated_uint32_.Set(index, value);
  // @@protoc_insertion_point(field_set:proto3_arena_lite_unittest.TestUnpackedTypes.repeated_uint32)
}
void TestUnpackedTypes::add_repeated_uint32(::google::protobuf::uint32 value) {
  repeated_uint32_.Add(value);
  // @@protoc_insertion_point(field_add:proto3_arena_lite_unittest.TestUnpackedTypes.repeated_uint32)
}
const ::google::protobuf::RepeatedField< ::google::protobuf::uint32 >&
TestUnpackedTypes::repeated_uint32() const {
  // @@protoc_insertion_point(field_list:proto3_arena_lite_unittest.TestUnpackedTypes.repeated_uint32)
  return repeated_uint32_;
}
::google::protobuf::RepeatedField< ::google::protobuf::uint32 >*
TestUnpackedTypes::mutable_repeated_uint32() {
  // @@protoc_insertion_point(field_mutable_list:proto3_arena_lite_unittest.TestUnpackedTypes.repeated_uint32)
  return &repeated_uint32_;
}

// repeated uint64 repeated_uint64 = 4 [packed = false];
int TestUnpackedTypes::repeated_uint64_size() const {
  return repeated_uint64_.size();
}
void TestUnpackedTypes::clear_repeated_uint64() {
  repeated_uint64_.Clear();
}
::google::protobuf::uint64 TestUnpackedTypes::repeated_uint64(int index) const {
  // @@protoc_insertion_point(field_get:proto3_arena_lite_unittest.TestUnpackedTypes.repeated_uint64)
  return repeated_uint64_.Get(index);
}
void TestUnpackedTypes::set_repeated_uint64(int index, ::google::protobuf::uint64 value) {
  repeated_uint64_.Set(index, value);
  // @@protoc_insertion_point(field_set:proto3_arena_lite_unittest.TestUnpackedTypes.repeated_uint64)
}
void TestUnpackedTypes::add_repeated_uint64(::google::protobuf::uint64 value) {
  repeated_uint64_.Add(value);
  // @@protoc_insertion_point(field_add:proto3_arena_lite_unittest.TestUnpackedTypes.repeated_uint64)
}
const ::google::protobuf::RepeatedField< ::google::protobuf::uint64 >&
TestUnpackedTypes::repeated_uint64() const {
  // @@protoc_insertion_point(field_list:proto3_arena_lite_unittest.TestUnpackedTypes.repeated_uint64)
  return repeated_uint64_;
}
::google::protobuf::RepeatedField< ::google::protobuf::uint64 >*
TestUnpackedTypes::mutable_repeated_uint64() {
  // @@protoc_insertion_point(field_mutable_list:proto3_arena_lite_unittest.TestUnpackedTypes.repeated_uint64)
  return &repeated_uint64_;
}

// repeated sint32 repeated_sint32 = 5 [packed = false];
int TestUnpackedTypes::repeated_sint32_size() const {
  return repeated_sint32_.size();
}
void TestUnpackedTypes::clear_repeated_sint32() {
  repeated_sint32_.Clear();
}
::google::protobuf::int32 TestUnpackedTypes::repeated_sint32(int index) const {
  // @@protoc_insertion_point(field_get:proto3_arena_lite_unittest.TestUnpackedTypes.repeated_sint32)
  return repeated_sint32_.Get(index);
}
void TestUnpackedTypes::set_repeated_sint32(int index, ::google::protobuf::int32 value) {
  repeated_sint32_.Set(index, value);
  // @@protoc_insertion_point(field_set:proto3_arena_lite_unittest.TestUnpackedTypes.repeated_sint32)
}
void TestUnpackedTypes::add_repeated_sint32(::google::protobuf::int32 value) {
  repeated_sint32_.Add(value);
  // @@protoc_insertion_point(field_add:proto3_arena_lite_unittest.TestUnpackedTypes.repeated_sint32)
}
const ::google::protobuf::RepeatedField< ::google::protobuf::int32 >&
TestUnpackedTypes::repeated_sint32() const {
  // @@protoc_insertion_point(field_list:proto3_arena_lite_unittest.TestUnpackedTypes.repeated_sint32)
  return repeated_sint32_;
}
::google::protobuf::RepeatedField< ::google::protobuf::int32 >*
TestUnpackedTypes::mutable_repeated_sint32() {
  // @@protoc_insertion_point(field_mutable_list:proto3_arena_lite_unittest.TestUnpackedTypes.repeated_sint32)
  return &repeated_sint32_;
}

// repeated sint64 repeated_sint64 = 6 [packed = false];
int TestUnpackedTypes::repeated_sint64_size() const {
  return repeated_sint64_.size();
}
void TestUnpackedTypes::clear_repeated_sint64() {
  repeated_sint64_.Clear();
}
::google::protobuf::int64 TestUnpackedTypes::repeated_sint64(int index) const {
  // @@protoc_insertion_point(field_get:proto3_arena_lite_unittest.TestUnpackedTypes.repeated_sint64)
  return repeated_sint64_.Get(index);
}
void TestUnpackedTypes::set_repeated_sint64(int index, ::google::protobuf::int64 value) {
  repeated_sint64_.Set(index, value);
  // @@protoc_insertion_point(field_set:proto3_arena_lite_unittest.TestUnpackedTypes.repeated_sint64)
}
void TestUnpackedTypes::add_repeated_sint64(::google::protobuf::int64 value) {
  repeated_sint64_.Add(value);
  // @@protoc_insertion_point(field_add:proto3_arena_lite_unittest.TestUnpackedTypes.repeated_sint64)
}
const ::google::protobuf::RepeatedField< ::google::protobuf::int64 >&
TestUnpackedTypes::repeated_sint64() const {
  // @@protoc_insertion_point(field_list:proto3_arena_lite_unittest.TestUnpackedTypes.repeated_sint64)
  return repeated_sint64_;
}
::google::protobuf::RepeatedField< ::google::protobuf::int64 >*
TestUnpackedTypes::mutable_repeated_sint64() {
  // @@protoc_insertion_point(field_mutable_list:proto3_arena_lite_unittest.TestUnpackedTypes.repeated_sint64)
  return &repeated_sint64_;
}

// repeated fixed32 repeated_fixed32 = 7 [packed = false];
int TestUnpackedTypes::repeated_fixed32_size() const {
  return repeated_fixed32_.size();
}
void TestUnpackedTypes::clear_repeated_fixed32() {
  repeated_fixed32_.Clear();
}
::google::protobuf::uint32 TestUnpackedTypes::repeated_fixed32(int index) const {
  // @@protoc_insertion_point(field_get:proto3_arena_lite_unittest.TestUnpackedTypes.repeated_fixed32)
  return repeated_fixed32_.Get(index);
}
void TestUnpackedTypes::set_repeated_fixed32(int index, ::google::protobuf::uint32 value) {
  repeated_fixed32_.Set(index, value);
  // @@protoc_insertion_point(field_set:proto3_arena_lite_unittest.TestUnpackedTypes.repeated_fixed32)
}
void TestUnpackedTypes::add_repeated_fixed32(::google::protobuf::uint32 value) {
  repeated_fixed32_.Add(value);
  // @@protoc_insertion_point(field_add:proto3_arena_lite_unittest.TestUnpackedTypes.repeated_fixed32)
}
const ::google::protobuf::RepeatedField< ::google::protobuf::uint32 >&
TestUnpackedTypes::repeated_fixed32() const {
  // @@protoc_insertion_point(field_list:proto3_arena_lite_unittest.TestUnpackedTypes.repeated_fixed32)
  return repeated_fixed32_;
}
::google::protobuf::RepeatedField< ::google::protobuf::uint32 >*
TestUnpackedTypes::mutable_repeated_fixed32() {
  // @@protoc_insertion_point(field_mutable_list:proto3_arena_lite_unittest.TestUnpackedTypes.repeated_fixed32)
  return &repeated_fixed32_;
}

// repeated fixed64 repeated_fixed64 = 8 [packed = false];
int TestUnpackedTypes::repeated_fixed64_size() const {
  return repeated_fixed64_.size();
}
void TestUnpackedTypes::clear_repeated_fixed64() {
  repeated_fixed64_.Clear();
}
::google::protobuf::uint64 TestUnpackedTypes::repeated_fixed64(int index) const {
  // @@protoc_insertion_point(field_get:proto3_arena_lite_unittest.TestUnpackedTypes.repeated_fixed64)
  return repeated_fixed64_.Get(index);
}
void TestUnpackedTypes::set_repeated_fixed64(int index, ::google::protobuf::uint64 value) {
  repeated_fixed64_.Set(index, value);
  // @@protoc_insertion_point(field_set:proto3_arena_lite_unittest.TestUnpackedTypes.repeated_fixed64)
}
void TestUnpackedTypes::add_repeated_fixed64(::google::protobuf::uint64 value) {
  repeated_fixed64_.Add(value);
  // @@protoc_insertion_point(field_add:proto3_arena_lite_unittest.TestUnpackedTypes.repeated_fixed64)
}
const ::google::protobuf::RepeatedField< ::google::protobuf::uint64 >&
TestUnpackedTypes::repeated_fixed64() const {
  // @@protoc_insertion_point(field_list:proto3_arena_lite_unittest.TestUnpackedTypes.repeated_fixed64)
  return repeated_fixed64_;
}
::google::protobuf::RepeatedField< ::google::protobuf::uint64 >*
TestUnpackedTypes::mutable_repeated_fixed64() {
  // @@protoc_insertion_point(field_mutable_list:proto3_arena_lite_unittest.TestUnpackedTypes.repeated_fixed64)
  return &repeated_fixed64_;
}

// repeated sfixed32 repeated_sfixed32 = 9 [packed = false];
int TestUnpackedTypes::repeated_sfixed32_size() const {
  return repeated_sfixed32_.size();
}
void TestUnpackedTypes::clear_repeated_sfixed32() {
  repeated_sfixed32_.Clear();
}
::google::protobuf::int32 TestUnpackedTypes::repeated_sfixed32(int index) const {
  // @@protoc_insertion_point(field_get:proto3_arena_lite_unittest.TestUnpackedTypes.repeated_sfixed32)
  return repeated_sfixed32_.Get(index);
}
void TestUnpackedTypes::set_repeated_sfixed32(int index, ::google::protobuf::int32 value) {
  repeated_sfixed32_.Set(index, value);
  // @@protoc_insertion_point(field_set:proto3_arena_lite_unittest.TestUnpackedTypes.repeated_sfixed32)
}
void TestUnpackedTypes::add_repeated_sfixed32(::google::protobuf::int32 value) {
  repeated_sfixed32_.Add(value);
  // @@protoc_insertion_point(field_add:proto3_arena_lite_unittest.TestUnpackedTypes.repeated_sfixed32)
}
const ::google::protobuf::RepeatedField< ::google::protobuf::int32 >&
TestUnpackedTypes::repeated_sfixed32() const {
  // @@protoc_insertion_point(field_list:proto3_arena_lite_unittest.TestUnpackedTypes.repeated_sfixed32)
  return repeated_sfixed32_;
}
::google::protobuf::RepeatedField< ::google::protobuf::int32 >*
TestUnpackedTypes::mutable_repeated_sfixed32() {
  // @@protoc_insertion_point(field_mutable_list:proto3_arena_lite_unittest.TestUnpackedTypes.repeated_sfixed32)
  return &repeated_sfixed32_;
}

// repeated sfixed64 repeated_sfixed64 = 10 [packed = false];
int TestUnpackedTypes::repeated_sfixed64_size() const {
  return repeated_sfixed64_.size();
}
void TestUnpackedTypes::clear_repeated_sfixed64() {
  repeated_sfixed64_.Clear();
}
::google::protobuf::int64 TestUnpackedTypes::repeated_sfixed64(int index) const {
  // @@protoc_insertion_point(field_get:proto3_arena_lite_unittest.TestUnpackedTypes.repeated_sfixed64)
  return repeated_sfixed64_.Get(index);
}
void TestUnpackedTypes::set_repeated_sfixed64(int index, ::google::protobuf::int64 value) {
  repeated_sfixed64_.Set(index, value);
  // @@protoc_insertion_point(field_set:proto3_arena_lite_unittest.TestUnpackedTypes.repeated_sfixed64)
}
void TestUnpackedTypes::add_repeated_sfixed64(::google::protobuf::int64 value) {
  repeated_sfixed64_.Add(value);
  // @@protoc_insertion_point(field_add:proto3_arena_lite_unittest.TestUnpackedTypes.repeated_sfixed64)
}
const ::google::protobuf::RepeatedField< ::google::protobuf::int64 >&
TestUnpackedTypes::repeated_sfixed64() const {
  // @@protoc_insertion_point(field_list:proto3_arena_lite_unittest.TestUnpackedTypes.repeated_sfixed64)
  return repeated_sfixed64_;
}
::google::protobuf::RepeatedField< ::google::protobuf::int64 >*
TestUnpackedTypes::mutable_repeated_sfixed64() {
  // @@protoc_insertion_point(field_mutable_list:proto3_arena_lite_unittest.TestUnpackedTypes.repeated_sfixed64)
  return &repeated_sfixed64_;
}

// repeated float repeated_float = 11 [packed = false];
int TestUnpackedTypes::repeated_float_size() const {
  return repeated_float_.size();
}
void TestUnpackedTypes::clear_repeated_float() {
  repeated_float_.Clear();
}
float TestUnpackedTypes::repeated_float(int index) const {
  // @@protoc_insertion_point(field_get:proto3_arena_lite_unittest.TestUnpackedTypes.repeated_float)
  return repeated_float_.Get(index);
}
void TestUnpackedTypes::set_repeated_float(int index, float value) {
  repeated_float_.Set(index, value);
  // @@protoc_insertion_point(field_set:proto3_arena_lite_unittest.TestUnpackedTypes.repeated_float)
}
void TestUnpackedTypes::add_repeated_float(float value) {
  repeated_float_.Add(value);
  // @@protoc_insertion_point(field_add:proto3_arena_lite_unittest.TestUnpackedTypes.repeated_float)
}
const ::google::protobuf::RepeatedField< float >&
TestUnpackedTypes::repeated_float() const {
  // @@protoc_insertion_point(field_list:proto3_arena_lite_unittest.TestUnpackedTypes.repeated_float)
  return repeated_float_;
}
::google::protobuf::RepeatedField< float >*
TestUnpackedTypes::mutable_repeated_float() {
  // @@protoc_insertion_point(field_mutable_list:proto3_arena_lite_unittest.TestUnpackedTypes.repeated_float)
  return &repeated_float_;
}

// repeated double repeated_double = 12 [packed = false];
int TestUnpackedTypes::repeated_double_size() const {
  return repeated_double_.size();
}
void TestUnpackedTypes::clear_repeated_double() {
  repeated_double_.Clear();
}
double TestUnpackedTypes::repeated_double(int index) const {
  // @@protoc_insertion_point(field_get:proto3_arena_lite_unittest.TestUnpackedTypes.repeated_double)
  return repeated_double_.Get(index);
}
void TestUnpackedTypes::set_repeated_double(int index, double value) {
  repeated_double_.Set(index, value);
  // @@protoc_insertion_point(field_set:proto3_arena_lite_unittest.TestUnpackedTypes.repeated_double)
}
void TestUnpackedTypes::add_repeated_double(double value) {
  repeated_double_.Add(value);
  // @@protoc_insertion_point(field_add:proto3_arena_lite_unittest.TestUnpackedTypes.repeated_double)
}
const ::google::protobuf::RepeatedField< double >&
TestUnpackedTypes::repeated_double() const {
  // @@protoc_insertion_point(field_list:proto3_arena_lite_unittest.TestUnpackedTypes.repeated_double)
  return repeated_double_;
}
::google::protobuf::RepeatedField< double >*
TestUnpackedTypes::mutable_repeated_double() {
  // @@protoc_insertion_point(field_mutable_list:proto3_arena_lite_unittest.TestUnpackedTypes.repeated_double)
  return &repeated_double_;
}

// repeated bool repeated_bool = 13 [packed = false];
int TestUnpackedTypes::repeated_bool_size() const {
  return repeated_bool_.size();
}
void TestUnpackedTypes::clear_repeated_bool() {
  repeated_bool_.Clear();
}
bool TestUnpackedTypes::repeated_bool(int index) const {
  // @@protoc_insertion_point(field_get:proto3_arena_lite_unittest.TestUnpackedTypes.repeated_bool)
  return repeated_bool_.Get(index);
}
void TestUnpackedTypes::set_repeated_bool(int index, bool value) {
  repeated_bool_.Set(index, value);
  // @@protoc_insertion_point(field_set:proto3_arena_lite_unittest.TestUnpackedTypes.repeated_bool)
}
void TestUnpackedTypes::add_repeated_bool(bool value) {
  repeated_bool_.Add(value);
  // @@protoc_insertion_point(field_add:proto3_arena_lite_unittest.TestUnpackedTypes.repeated_bool)
}
const ::google::protobuf::RepeatedField< bool >&
TestUnpackedTypes::repeated_bool() const {
  // @@protoc_insertion_point(field_list:proto3_arena_lite_unittest.TestUnpackedTypes.repeated_bool)
  return repeated_bool_;
}
::google::protobuf::RepeatedField< bool >*
TestUnpackedTypes::mutable_repeated_bool() {
  // @@protoc_insertion_point(field_mutable_list:proto3_arena_lite_unittest.TestUnpackedTypes.repeated_bool)
  return &repeated_bool_;
}

// repeated .proto3_arena_lite_unittest.TestAllTypes.NestedEnum repeated_nested_enum = 14 [packed = false];
int TestUnpackedTypes::repeated_nested_enum_size() const {
  return repeated_nested_enum_.size();
}
void TestUnpackedTypes::clear_repeated_nested_enum() {
  repeated_nested_enum_.Clear();
}
::proto3_arena_lite_unittest::TestAllTypes_NestedEnum TestUnpackedTypes::repeated_nested_enum(int index) const {
  // @@protoc_insertion_point(field_get:proto3_arena_lite_unittest.TestUnpackedTypes.repeated_nested_enum)
  return static_cast< ::proto3_arena_lite_unittest::TestAllTypes_NestedEnum >(repeated_nested_enum_.Get(index));
}
void TestUnpackedTypes::set_repeated_nested_enum(int index, ::proto3_arena_lite_unittest::TestAllTypes_NestedEnum value) {
  repeated_nested_enum_.Set(index, value);
  // @@protoc_insertion_point(field_set:proto3_arena_lite_unittest.TestUnpackedTypes.repeated_nested_enum)
}
void TestUnpackedTypes::add_repeated_nested_enum(::proto3_arena_lite_unittest::TestAllTypes_NestedEnum value) {
  repeated_nested_enum_.Add(value);
  // @@protoc_insertion_point(field_add:proto3_arena_lite_unittest.TestUnpackedTypes.repeated_nested_enum)
}
const ::google::protobuf::RepeatedField<int>&
TestUnpackedTypes::repeated_nested_enum() const {
  // @@protoc_insertion_point(field_list:proto3_arena_lite_unittest.TestUnpackedTypes.repeated_nested_enum)
  return repeated_nested_enum_;
}
::google::protobuf::RepeatedField<int>*
TestUnpackedTypes::mutable_repeated_nested_enum() {
  // @@protoc_insertion_point(field_mutable_list:proto3_arena_lite_unittest.TestUnpackedTypes.repeated_nested_enum)
  return &repeated_nested_enum_;
}

inline const TestUnpackedTypes* TestUnpackedTypes::internal_default_instance() {
  return &TestUnpackedTypes_default_instance_.get();
}
#endif  // PROTOBUF_INLINE_NOT_IN_HEADERS

// ===================================================================

void NestedTestAllTypes::_slow_mutable_child() {
  child_ = ::google::protobuf::Arena::CreateMessage< ::proto3_arena_lite_unittest::NestedTestAllTypes >(
      GetArenaNoVirtual());
}
::proto3_arena_lite_unittest::NestedTestAllTypes* NestedTestAllTypes::_slow_release_child() {
  if (child_ == NULL) {
    return NULL;
  } else {
    ::proto3_arena_lite_unittest::NestedTestAllTypes* temp = new ::proto3_arena_lite_unittest::NestedTestAllTypes(*child_);
    child_ = NULL;
    return temp;
  }
}
::proto3_arena_lite_unittest::NestedTestAllTypes* NestedTestAllTypes::unsafe_arena_release_child() {
  // @@protoc_insertion_point(field_unsafe_arena_release:proto3_arena_lite_unittest.NestedTestAllTypes.child)
  
  ::proto3_arena_lite_unittest::NestedTestAllTypes* temp = child_;
  child_ = NULL;
  return temp;
}
void NestedTestAllTypes::_slow_set_allocated_child(
    ::google::protobuf::Arena* message_arena, ::proto3_arena_lite_unittest::NestedTestAllTypes** child) {
    if (message_arena != NULL && 
        ::google::protobuf::Arena::GetArena(*child) == NULL) {
      message_arena->Own(*child);
    } else if (message_arena !=
               ::google::protobuf::Arena::GetArena(*child)) {
      ::proto3_arena_lite_unittest::NestedTestAllTypes* new_child = 
            ::google::protobuf::Arena::CreateMessage< ::proto3_arena_lite_unittest::NestedTestAllTypes >(
            message_arena);
      new_child->CopyFrom(**child);
      *child = new_child;
    }
}
void NestedTestAllTypes::unsafe_arena_set_allocated_child(
    ::proto3_arena_lite_unittest::NestedTestAllTypes* child) {
  if (GetArenaNoVirtual() == NULL) {
    delete child_;
  }
  child_ = child;
  if (child) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:proto3_arena_lite_unittest.NestedTestAllTypes.child)
}
void NestedTestAllTypes::_slow_mutable_payload() {
  payload_ = ::google::protobuf::Arena::CreateMessage< ::proto3_arena_lite_unittest::TestAllTypes >(
      GetArenaNoVirtual());
}
::proto3_arena_lite_unittest::TestAllTypes* NestedTestAllTypes::_slow_release_payload() {
  if (payload_ == NULL) {
    return NULL;
  } else {
    ::proto3_arena_lite_unittest::TestAllTypes* temp = new ::proto3_arena_lite_unittest::TestAllTypes(*payload_);
    payload_ = NULL;
    return temp;
  }
}
::proto3_arena_lite_unittest::TestAllTypes* NestedTestAllTypes::unsafe_arena_release_payload() {
  // @@protoc_insertion_point(field_unsafe_arena_release:proto3_arena_lite_unittest.NestedTestAllTypes.payload)
  
  ::proto3_arena_lite_unittest::TestAllTypes* temp = payload_;
  payload_ = NULL;
  return temp;
}
void NestedTestAllTypes::_slow_set_allocated_payload(
    ::google::protobuf::Arena* message_arena, ::proto3_arena_lite_unittest::TestAllTypes** payload) {
    if (message_arena != NULL && 
        ::google::protobuf::Arena::GetArena(*payload) == NULL) {
      message_arena->Own(*payload);
    } else if (message_arena !=
               ::google::protobuf::Arena::GetArena(*payload)) {
      ::proto3_arena_lite_unittest::TestAllTypes* new_payload = 
            ::google::protobuf::Arena::CreateMessage< ::proto3_arena_lite_unittest::TestAllTypes >(
            message_arena);
      new_payload->CopyFrom(**payload);
      *payload = new_payload;
    }
}
void NestedTestAllTypes::unsafe_arena_set_allocated_payload(
    ::proto3_arena_lite_unittest::TestAllTypes* payload) {
  if (GetArenaNoVirtual() == NULL) {
    delete payload_;
  }
  payload_ = payload;
  if (payload) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:proto3_arena_lite_unittest.NestedTestAllTypes.payload)
}
#if !defined(_MSC_VER) || _MSC_VER >= 1900
const int NestedTestAllTypes::kChildFieldNumber;
const int NestedTestAllTypes::kPayloadFieldNumber;
#endif  // !defined(_MSC_VER) || _MSC_VER >= 1900

NestedTestAllTypes::NestedTestAllTypes()
  : ::google::protobuf::MessageLite(), _arena_ptr_(NULL) {
  if (this != internal_default_instance()) protobuf_InitDefaults_google_2fprotobuf_2funittest_5fproto3_5farena_5flite_2eproto();
  SharedCtor();
  // @@protoc_insertion_point(constructor:proto3_arena_lite_unittest.NestedTestAllTypes)
}
NestedTestAllTypes::NestedTestAllTypes(::google::protobuf::Arena* arena)
  : ::google::protobuf::MessageLite(),
  _arena_ptr_(arena) {
#ifdef GOOGLE_PROTOBUF_NO_STATIC_INITIALIZER
  protobuf_InitDefaults_google_2fprotobuf_2funittest_5fproto3_5farena_5flite_2eproto();
#endif  // GOOGLE_PROTOBUF_NO_STATIC_INITIALIZER
  SharedCtor();
  RegisterArenaDtor(arena);
  // @@protoc_insertion_point(arena_constructor:proto3_arena_lite_unittest.NestedTestAllTypes)
}

void NestedTestAllTypes::InitAsDefaultInstance() {
  child_ = const_cast< ::proto3_arena_lite_unittest::NestedTestAllTypes*>(
      ::proto3_arena_lite_unittest::NestedTestAllTypes::internal_default_instance());
  payload_ = const_cast< ::proto3_arena_lite_unittest::TestAllTypes*>(
      ::proto3_arena_lite_unittest::TestAllTypes::internal_default_instance());
}

NestedTestAllTypes::NestedTestAllTypes(const NestedTestAllTypes& from)
  : ::google::protobuf::MessageLite(),
    _arena_ptr_(NULL) {
  SharedCtor();
  UnsafeMergeFrom(from);
  // @@protoc_insertion_point(copy_constructor:proto3_arena_lite_unittest.NestedTestAllTypes)
}

void NestedTestAllTypes::SharedCtor() {
  child_ = NULL;
  payload_ = NULL;
  _cached_size_ = 0;
}

NestedTestAllTypes::~NestedTestAllTypes() {
  // @@protoc_insertion_point(destructor:proto3_arena_lite_unittest.NestedTestAllTypes)
  SharedDtor();
}

void NestedTestAllTypes::SharedDtor() {
  ::google::protobuf::Arena* arena = GetArenaNoVirtual();
  if (arena != NULL) {
    return;
  }

  if (this != &NestedTestAllTypes_default_instance_.get()) {
    delete child_;
    delete payload_;
  }
}

void NestedTestAllTypes::ArenaDtor(void* object) {
  NestedTestAllTypes* _this = reinterpret_cast< NestedTestAllTypes* >(object);
  (void)_this;
}
void NestedTestAllTypes::RegisterArenaDtor(::google::protobuf::Arena* arena) {
}
void NestedTestAllTypes::SetCachedSize(int size) const {
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
}
const NestedTestAllTypes& NestedTestAllTypes::default_instance() {
  protobuf_InitDefaults_google_2fprotobuf_2funittest_5fproto3_5farena_5flite_2eproto();
  return *internal_default_instance();
}

::google::protobuf::internal::ExplicitlyConstructed<NestedTestAllTypes> NestedTestAllTypes_default_instance_;

NestedTestAllTypes* NestedTestAllTypes::New(::google::protobuf::Arena* arena) const {
  return ::google::protobuf::Arena::CreateMessage<NestedTestAllTypes>(arena);
}

void NestedTestAllTypes::Clear() {
// @@protoc_insertion_point(message_clear_start:proto3_arena_lite_unittest.NestedTestAllTypes)
  if (GetArenaNoVirtual() == NULL && child_ != NULL) delete child_;
  child_ = NULL;
  if (GetArenaNoVirtual() == NULL && payload_ != NULL) delete payload_;
  payload_ = NULL;
}

bool NestedTestAllTypes::MergePartialFromCodedStream(
    ::google::protobuf::io::CodedInputStream* input) {
#define DO_(EXPRESSION) if (!GOOGLE_PREDICT_TRUE(EXPRESSION)) goto failure
  ::google::protobuf::uint32 tag;
  // @@protoc_insertion_point(parse_start:proto3_arena_lite_unittest.NestedTestAllTypes)
  for (;;) {
    ::std::pair< ::google::protobuf::uint32, bool> p = input->ReadTagWithCutoff(127);
    tag = p.first;
    if (!p.second) goto handle_unusual;
    switch (::google::protobuf::internal::WireFormatLite::GetTagFieldNumber(tag)) {
      // optional .proto3_arena_lite_unittest.NestedTestAllTypes child = 1;
      case 1: {
        if (tag == 10) {
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessageNoVirtual(
               input, mutable_child()));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(18)) goto parse_payload;
        break;
      }

      // optional .proto3_arena_lite_unittest.TestAllTypes payload = 2;
      case 2: {
        if (tag == 18) {
         parse_payload:
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessageNoVirtual(
               input, mutable_payload()));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectAtEnd()) goto success;
        break;
      }

      default: {
      handle_unusual:
        if (tag == 0 ||
            ::google::protobuf::internal::WireFormatLite::GetTagWireType(tag) ==
            ::google::protobuf::internal::WireFormatLite::WIRETYPE_END_GROUP) {
          goto success;
        }
        DO_(::google::protobuf::internal::WireFormatLite::SkipField(input, tag));
        break;
      }
    }
  }
success:
  // @@protoc_insertion_point(parse_success:proto3_arena_lite_unittest.NestedTestAllTypes)
  return true;
failure:
  // @@protoc_insertion_point(parse_failure:proto3_arena_lite_unittest.NestedTestAllTypes)
  return false;
#undef DO_
}

void NestedTestAllTypes::SerializeWithCachedSizes(
    ::google::protobuf::io::CodedOutputStream* output) const {
  // @@protoc_insertion_point(serialize_start:proto3_arena_lite_unittest.NestedTestAllTypes)
  // optional .proto3_arena_lite_unittest.NestedTestAllTypes child = 1;
  if (this->has_child()) {
    ::google::protobuf::internal::WireFormatLite::WriteMessage(
      1, *this->child_, output);
  }

  // optional .proto3_arena_lite_unittest.TestAllTypes payload = 2;
  if (this->has_payload()) {
    ::google::protobuf::internal::WireFormatLite::WriteMessage(
      2, *this->payload_, output);
  }

  // @@protoc_insertion_point(serialize_end:proto3_arena_lite_unittest.NestedTestAllTypes)
}

size_t NestedTestAllTypes::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:proto3_arena_lite_unittest.NestedTestAllTypes)
  size_t total_size = 0;

  // optional .proto3_arena_lite_unittest.NestedTestAllTypes child = 1;
  if (this->has_child()) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::MessageSizeNoVirtual(
        *this->child_);
  }

  // optional .proto3_arena_lite_unittest.TestAllTypes payload = 2;
  if (this->has_payload()) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::MessageSizeNoVirtual(
        *this->payload_);
  }

  int cached_size = ::google::protobuf::internal::ToCachedSize(total_size);
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = cached_size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
  return total_size;
}

void NestedTestAllTypes::CheckTypeAndMergeFrom(
    const ::google::protobuf::MessageLite& from) {
  MergeFrom(*::google::protobuf::down_cast<const NestedTestAllTypes*>(&from));
}

void NestedTestAllTypes::MergeFrom(const NestedTestAllTypes& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:proto3_arena_lite_unittest.NestedTestAllTypes)
  if (GOOGLE_PREDICT_TRUE(&from != this)) {
    UnsafeMergeFrom(from);
  } else {
    MergeFromFail(__LINE__);
  }
}

void NestedTestAllTypes::UnsafeMergeFrom(const NestedTestAllTypes& from) {
  GOOGLE_DCHECK(&from != this);
  if (from.has_child()) {
    mutable_child()->::proto3_arena_lite_unittest::NestedTestAllTypes::MergeFrom(from.child());
  }
  if (from.has_payload()) {
    mutable_payload()->::proto3_arena_lite_unittest::TestAllTypes::MergeFrom(from.payload());
  }
}

void NestedTestAllTypes::CopyFrom(const NestedTestAllTypes& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:proto3_arena_lite_unittest.NestedTestAllTypes)
  if (&from == this) return;
  Clear();
  UnsafeMergeFrom(from);
}

bool NestedTestAllTypes::IsInitialized() const {

  return true;
}

void NestedTestAllTypes::Swap(NestedTestAllTypes* other) {
  if (other == this) return;
  if (GetArenaNoVirtual() == other->GetArenaNoVirtual()) {
    InternalSwap(other);
  } else {
    NestedTestAllTypes temp;
    temp.UnsafeMergeFrom(*this);
    CopyFrom(*other);
    other->CopyFrom(temp);
  }
}
void NestedTestAllTypes::UnsafeArenaSwap(NestedTestAllTypes* other) {
  if (other == this) return;
  GOOGLE_DCHECK(GetArenaNoVirtual() == other->GetArenaNoVirtual());
  InternalSwap(other);
}
void NestedTestAllTypes::InternalSwap(NestedTestAllTypes* other) {
  std::swap(child_, other->child_);
  std::swap(payload_, other->payload_);
  _unknown_fields_.Swap(&other->_unknown_fields_);
  std::swap(_cached_size_, other->_cached_size_);
}

::std::string NestedTestAllTypes::GetTypeName() const {
  return "proto3_arena_lite_unittest.NestedTestAllTypes";
}

#if PROTOBUF_INLINE_NOT_IN_HEADERS
// NestedTestAllTypes

// optional .proto3_arena_lite_unittest.NestedTestAllTypes child = 1;
bool NestedTestAllTypes::has_child() const {
  return this != internal_default_instance() && child_ != NULL;
}
void NestedTestAllTypes::clear_child() {
  if (GetArenaNoVirtual() == NULL && child_ != NULL) delete child_;
  child_ = NULL;
}
const ::proto3_arena_lite_unittest::NestedTestAllTypes& NestedTestAllTypes::child() const {
  // @@protoc_insertion_point(field_get:proto3_arena_lite_unittest.NestedTestAllTypes.child)
  return child_ != NULL ? *child_
                         : *::proto3_arena_lite_unittest::NestedTestAllTypes::internal_default_instance();
}
::proto3_arena_lite_unittest::NestedTestAllTypes* NestedTestAllTypes::mutable_child() {
  
  if (child_ == NULL) {
    _slow_mutable_child();
  }
  // @@protoc_insertion_point(field_mutable:proto3_arena_lite_unittest.NestedTestAllTypes.child)
  return child_;
}
::proto3_arena_lite_unittest::NestedTestAllTypes* NestedTestAllTypes::release_child() {
  // @@protoc_insertion_point(field_release:proto3_arena_lite_unittest.NestedTestAllTypes.child)
  
  if (GetArenaNoVirtual() != NULL) {
    return _slow_release_child();
  } else {
    ::proto3_arena_lite_unittest::NestedTestAllTypes* temp = child_;
    child_ = NULL;
    return temp;
  }
}
 void NestedTestAllTypes::set_allocated_child(::proto3_arena_lite_unittest::NestedTestAllTypes* child) {
  ::google::protobuf::Arena* message_arena = GetArenaNoVirtual();
  if (message_arena == NULL) {
    delete child_;
  }
  if (child != NULL) {
    _slow_set_allocated_child(message_arena, &child);
  }
  child_ = child;
  if (child) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_set_allocated:proto3_arena_lite_unittest.NestedTestAllTypes.child)
}

// optional .proto3_arena_lite_unittest.TestAllTypes payload = 2;
bool NestedTestAllTypes::has_payload() const {
  return this != internal_default_instance() && payload_ != NULL;
}
void NestedTestAllTypes::clear_payload() {
  if (GetArenaNoVirtual() == NULL && payload_ != NULL) delete payload_;
  payload_ = NULL;
}
const ::proto3_arena_lite_unittest::TestAllTypes& NestedTestAllTypes::payload() const {
  // @@protoc_insertion_point(field_get:proto3_arena_lite_unittest.NestedTestAllTypes.payload)
  return payload_ != NULL ? *payload_
                         : *::proto3_arena_lite_unittest::TestAllTypes::internal_default_instance();
}
::proto3_arena_lite_unittest::TestAllTypes* NestedTestAllTypes::mutable_payload() {
  
  if (payload_ == NULL) {
    _slow_mutable_payload();
  }
  // @@protoc_insertion_point(field_mutable:proto3_arena_lite_unittest.NestedTestAllTypes.payload)
  return payload_;
}
::proto3_arena_lite_unittest::TestAllTypes* NestedTestAllTypes::release_payload() {
  // @@protoc_insertion_point(field_release:proto3_arena_lite_unittest.NestedTestAllTypes.payload)
  
  if (GetArenaNoVirtual() != NULL) {
    return _slow_release_payload();
  } else {
    ::proto3_arena_lite_unittest::TestAllTypes* temp = payload_;
    payload_ = NULL;
    return temp;
  }
}
 void NestedTestAllTypes::set_allocated_payload(::proto3_arena_lite_unittest::TestAllTypes* payload) {
  ::google::protobuf::Arena* message_arena = GetArenaNoVirtual();
  if (message_arena == NULL) {
    delete payload_;
  }
  if (payload != NULL) {
    _slow_set_allocated_payload(message_arena, &payload);
  }
  payload_ = payload;
  if (payload) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_set_allocated:proto3_arena_lite_unittest.NestedTestAllTypes.payload)
}

inline const NestedTestAllTypes* NestedTestAllTypes::internal_default_instance() {
  return &NestedTestAllTypes_default_instance_.get();
}
#endif  // PROTOBUF_INLINE_NOT_IN_HEADERS

// ===================================================================

#if !defined(_MSC_VER) || _MSC_VER >= 1900
const int ForeignMessage::kCFieldNumber;
#endif  // !defined(_MSC_VER) || _MSC_VER >= 1900

ForeignMessage::ForeignMessage()
  : ::google::protobuf::MessageLite(), _arena_ptr_(NULL) {
  if (this != internal_default_instance()) protobuf_InitDefaults_google_2fprotobuf_2funittest_5fproto3_5farena_5flite_2eproto();
  SharedCtor();
  // @@protoc_insertion_point(constructor:proto3_arena_lite_unittest.ForeignMessage)
}
ForeignMessage::ForeignMessage(::google::protobuf::Arena* arena)
  : ::google::protobuf::MessageLite(),
  _arena_ptr_(arena) {
#ifdef GOOGLE_PROTOBUF_NO_STATIC_INITIALIZER
  protobuf_InitDefaults_google_2fprotobuf_2funittest_5fproto3_5farena_5flite_2eproto();
#endif  // GOOGLE_PROTOBUF_NO_STATIC_INITIALIZER
  SharedCtor();
  RegisterArenaDtor(arena);
  // @@protoc_insertion_point(arena_constructor:proto3_arena_lite_unittest.ForeignMessage)
}

void ForeignMessage::InitAsDefaultInstance() {
}

ForeignMessage::ForeignMessage(const ForeignMessage& from)
  : ::google::protobuf::MessageLite(),
    _arena_ptr_(NULL) {
  SharedCtor();
  UnsafeMergeFrom(from);
  // @@protoc_insertion_point(copy_constructor:proto3_arena_lite_unittest.ForeignMessage)
}

void ForeignMessage::SharedCtor() {
  c_ = 0;
  _cached_size_ = 0;
}

ForeignMessage::~ForeignMessage() {
  // @@protoc_insertion_point(destructor:proto3_arena_lite_unittest.ForeignMessage)
  SharedDtor();
}

void ForeignMessage::SharedDtor() {
  ::google::protobuf::Arena* arena = GetArenaNoVirtual();
  if (arena != NULL) {
    return;
  }

}

void ForeignMessage::ArenaDtor(void* object) {
  ForeignMessage* _this = reinterpret_cast< ForeignMessage* >(object);
  (void)_this;
}
void ForeignMessage::RegisterArenaDtor(::google::protobuf::Arena* arena) {
}
void ForeignMessage::SetCachedSize(int size) const {
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
}
const ForeignMessage& ForeignMessage::default_instance() {
  protobuf_InitDefaults_google_2fprotobuf_2funittest_5fproto3_5farena_5flite_2eproto();
  return *internal_default_instance();
}

::google::protobuf::internal::ExplicitlyConstructed<ForeignMessage> ForeignMessage_default_instance_;

ForeignMessage* ForeignMessage::New(::google::protobuf::Arena* arena) const {
  return ::google::protobuf::Arena::CreateMessage<ForeignMessage>(arena);
}

void ForeignMessage::Clear() {
// @@protoc_insertion_point(message_clear_start:proto3_arena_lite_unittest.ForeignMessage)
  c_ = 0;
}

bool ForeignMessage::MergePartialFromCodedStream(
    ::google::protobuf::io::CodedInputStream* input) {
#define DO_(EXPRESSION) if (!GOOGLE_PREDICT_TRUE(EXPRESSION)) goto failure
  ::google::protobuf::uint32 tag;
  // @@protoc_insertion_point(parse_start:proto3_arena_lite_unittest.ForeignMessage)
  for (;;) {
    ::std::pair< ::google::protobuf::uint32, bool> p = input->ReadTagWithCutoff(127);
    tag = p.first;
    if (!p.second) goto handle_unusual;
    switch (::google::protobuf::internal::WireFormatLite::GetTagFieldNumber(tag)) {
      // optional int32 c = 1;
      case 1: {
        if (tag == 8) {

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int32, ::google::protobuf::internal::WireFormatLite::TYPE_INT32>(
                 input, &c_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectAtEnd()) goto success;
        break;
      }

      default: {
      handle_unusual:
        if (tag == 0 ||
            ::google::protobuf::internal::WireFormatLite::GetTagWireType(tag) ==
            ::google::protobuf::internal::WireFormatLite::WIRETYPE_END_GROUP) {
          goto success;
        }
        DO_(::google::protobuf::internal::WireFormatLite::SkipField(input, tag));
        break;
      }
    }
  }
success:
  // @@protoc_insertion_point(parse_success:proto3_arena_lite_unittest.ForeignMessage)
  return true;
failure:
  // @@protoc_insertion_point(parse_failure:proto3_arena_lite_unittest.ForeignMessage)
  return false;
#undef DO_
}

void ForeignMessage::SerializeWithCachedSizes(
    ::google::protobuf::io::CodedOutputStream* output) const {
  // @@protoc_insertion_point(serialize_start:proto3_arena_lite_unittest.ForeignMessage)
  // optional int32 c = 1;
  if (this->c() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt32(1, this->c(), output);
  }

  // @@protoc_insertion_point(serialize_end:proto3_arena_lite_unittest.ForeignMessage)
}

size_t ForeignMessage::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:proto3_arena_lite_unittest.ForeignMessage)
  size_t total_size = 0;

  // optional int32 c = 1;
  if (this->c() != 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::Int32Size(
        this->c());
  }

  int cached_size = ::google::protobuf::internal::ToCachedSize(total_size);
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = cached_size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
  return total_size;
}

void ForeignMessage::CheckTypeAndMergeFrom(
    const ::google::protobuf::MessageLite& from) {
  MergeFrom(*::google::protobuf::down_cast<const ForeignMessage*>(&from));
}

void ForeignMessage::MergeFrom(const ForeignMessage& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:proto3_arena_lite_unittest.ForeignMessage)
  if (GOOGLE_PREDICT_TRUE(&from != this)) {
    UnsafeMergeFrom(from);
  } else {
    MergeFromFail(__LINE__);
  }
}

void ForeignMessage::UnsafeMergeFrom(const ForeignMessage& from) {
  GOOGLE_DCHECK(&from != this);
  if (from.c() != 0) {
    set_c(from.c());
  }
}

void ForeignMessage::CopyFrom(const ForeignMessage& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:proto3_arena_lite_unittest.ForeignMessage)
  if (&from == this) return;
  Clear();
  UnsafeMergeFrom(from);
}

bool ForeignMessage::IsInitialized() const {

  return true;
}

void ForeignMessage::Swap(ForeignMessage* other) {
  if (other == this) return;
  if (GetArenaNoVirtual() == other->GetArenaNoVirtual()) {
    InternalSwap(other);
  } else {
    ForeignMessage temp;
    temp.UnsafeMergeFrom(*this);
    CopyFrom(*other);
    other->CopyFrom(temp);
  }
}
void ForeignMessage::UnsafeArenaSwap(ForeignMessage* other) {
  if (other == this) return;
  GOOGLE_DCHECK(GetArenaNoVirtual() == other->GetArenaNoVirtual());
  InternalSwap(other);
}
void ForeignMessage::InternalSwap(ForeignMessage* other) {
  std::swap(c_, other->c_);
  _unknown_fields_.Swap(&other->_unknown_fields_);
  std::swap(_cached_size_, other->_cached_size_);
}

::std::string ForeignMessage::GetTypeName() const {
  return "proto3_arena_lite_unittest.ForeignMessage";
}

#if PROTOBUF_INLINE_NOT_IN_HEADERS
// ForeignMessage

// optional int32 c = 1;
void ForeignMessage::clear_c() {
  c_ = 0;
}
::google::protobuf::int32 ForeignMessage::c() const {
  // @@protoc_insertion_point(field_get:proto3_arena_lite_unittest.ForeignMessage.c)
  return c_;
}
void ForeignMessage::set_c(::google::protobuf::int32 value) {
  
  c_ = value;
  // @@protoc_insertion_point(field_set:proto3_arena_lite_unittest.ForeignMessage.c)
}

inline const ForeignMessage* ForeignMessage::internal_default_instance() {
  return &ForeignMessage_default_instance_.get();
}
#endif  // PROTOBUF_INLINE_NOT_IN_HEADERS

// ===================================================================

#if !defined(_MSC_VER) || _MSC_VER >= 1900
#endif  // !defined(_MSC_VER) || _MSC_VER >= 1900

TestEmptyMessage::TestEmptyMessage()
  : ::google::protobuf::MessageLite(), _arena_ptr_(NULL) {
  if (this != internal_default_instance()) protobuf_InitDefaults_google_2fprotobuf_2funittest_5fproto3_5farena_5flite_2eproto();
  SharedCtor();
  // @@protoc_insertion_point(constructor:proto3_arena_lite_unittest.TestEmptyMessage)
}
TestEmptyMessage::TestEmptyMessage(::google::protobuf::Arena* arena)
  : ::google::protobuf::MessageLite(),
  _arena_ptr_(arena) {
#ifdef GOOGLE_PROTOBUF_NO_STATIC_INITIALIZER
  protobuf_InitDefaults_google_2fprotobuf_2funittest_5fproto3_5farena_5flite_2eproto();
#endif  // GOOGLE_PROTOBUF_NO_STATIC_INITIALIZER
  SharedCtor();
  RegisterArenaDtor(arena);
  // @@protoc_insertion_point(arena_constructor:proto3_arena_lite_unittest.TestEmptyMessage)
}

void TestEmptyMessage::InitAsDefaultInstance() {
}

TestEmptyMessage::TestEmptyMessage(const TestEmptyMessage& from)
  : ::google::protobuf::MessageLite(),
    _arena_ptr_(NULL) {
  SharedCtor();
  UnsafeMergeFrom(from);
  // @@protoc_insertion_point(copy_constructor:proto3_arena_lite_unittest.TestEmptyMessage)
}

void TestEmptyMessage::SharedCtor() {
  _cached_size_ = 0;
}

TestEmptyMessage::~TestEmptyMessage() {
  // @@protoc_insertion_point(destructor:proto3_arena_lite_unittest.TestEmptyMessage)
  SharedDtor();
}

void TestEmptyMessage::SharedDtor() {
  ::google::protobuf::Arena* arena = GetArenaNoVirtual();
  if (arena != NULL) {
    return;
  }

}

void TestEmptyMessage::ArenaDtor(void* object) {
  TestEmptyMessage* _this = reinterpret_cast< TestEmptyMessage* >(object);
  (void)_this;
}
void TestEmptyMessage::RegisterArenaDtor(::google::protobuf::Arena* arena) {
}
void TestEmptyMessage::SetCachedSize(int size) const {
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
}
const TestEmptyMessage& TestEmptyMessage::default_instance() {
  protobuf_InitDefaults_google_2fprotobuf_2funittest_5fproto3_5farena_5flite_2eproto();
  return *internal_default_instance();
}

::google::protobuf::internal::ExplicitlyConstructed<TestEmptyMessage> TestEmptyMessage_default_instance_;

TestEmptyMessage* TestEmptyMessage::New(::google::protobuf::Arena* arena) const {
  return ::google::protobuf::Arena::CreateMessage<TestEmptyMessage>(arena);
}

void TestEmptyMessage::Clear() {
// @@protoc_insertion_point(message_clear_start:proto3_arena_lite_unittest.TestEmptyMessage)
}

bool TestEmptyMessage::MergePartialFromCodedStream(
    ::google::protobuf::io::CodedInputStream* input) {
#define DO_(EXPRESSION) if (!GOOGLE_PREDICT_TRUE(EXPRESSION)) goto failure
  ::google::protobuf::uint32 tag;
  // @@protoc_insertion_point(parse_start:proto3_arena_lite_unittest.TestEmptyMessage)
  for (;;) {
    ::std::pair< ::google::protobuf::uint32, bool> p = input->ReadTagWithCutoff(127);
    tag = p.first;
    if (!p.second) goto handle_unusual;
  handle_unusual:
    if (tag == 0 ||
        ::google::protobuf::internal::WireFormatLite::GetTagWireType(tag) ==
        ::google::protobuf::internal::WireFormatLite::WIRETYPE_END_GROUP) {
      goto success;
    }
    DO_(::google::protobuf::internal::WireFormatLite::SkipField(input, tag));
  }
success:
  // @@protoc_insertion_point(parse_success:proto3_arena_lite_unittest.TestEmptyMessage)
  return true;
failure:
  // @@protoc_insertion_point(parse_failure:proto3_arena_lite_unittest.TestEmptyMessage)
  return false;
#undef DO_
}

void TestEmptyMessage::SerializeWithCachedSizes(
    ::google::protobuf::io::CodedOutputStream* output) const {
  // @@protoc_insertion_point(serialize_start:proto3_arena_lite_unittest.TestEmptyMessage)
  // @@protoc_insertion_point(serialize_end:proto3_arena_lite_unittest.TestEmptyMessage)
}

size_t TestEmptyMessage::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:proto3_arena_lite_unittest.TestEmptyMessage)
  size_t total_size = 0;

  int cached_size = ::google::protobuf::internal::ToCachedSize(total_size);
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = cached_size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
  return total_size;
}

void TestEmptyMessage::CheckTypeAndMergeFrom(
    const ::google::protobuf::MessageLite& from) {
  MergeFrom(*::google::protobuf::down_cast<const TestEmptyMessage*>(&from));
}

void TestEmptyMessage::MergeFrom(const TestEmptyMessage& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:proto3_arena_lite_unittest.TestEmptyMessage)
  if (GOOGLE_PREDICT_TRUE(&from != this)) {
    UnsafeMergeFrom(from);
  } else {
    MergeFromFail(__LINE__);
  }
}

void TestEmptyMessage::UnsafeMergeFrom(const TestEmptyMessage& from) {
  GOOGLE_DCHECK(&from != this);
}

void TestEmptyMessage::CopyFrom(const TestEmptyMessage& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:proto3_arena_lite_unittest.TestEmptyMessage)
  if (&from == this) return;
  Clear();
  UnsafeMergeFrom(from);
}

bool TestEmptyMessage::IsInitialized() const {

  return true;
}

void TestEmptyMessage::Swap(TestEmptyMessage* other) {
  if (other == this) return;
  if (GetArenaNoVirtual() == other->GetArenaNoVirtual()) {
    InternalSwap(other);
  } else {
    TestEmptyMessage temp;
    temp.UnsafeMergeFrom(*this);
    CopyFrom(*other);
    other->CopyFrom(temp);
  }
}
void TestEmptyMessage::UnsafeArenaSwap(TestEmptyMessage* other) {
  if (other == this) return;
  GOOGLE_DCHECK(GetArenaNoVirtual() == other->GetArenaNoVirtual());
  InternalSwap(other);
}
void TestEmptyMessage::InternalSwap(TestEmptyMessage* other) {
  _unknown_fields_.Swap(&other->_unknown_fields_);
  std::swap(_cached_size_, other->_cached_size_);
}

::std::string TestEmptyMessage::GetTypeName() const {
  return "proto3_arena_lite_unittest.TestEmptyMessage";
}

#if PROTOBUF_INLINE_NOT_IN_HEADERS
// TestEmptyMessage

inline const TestEmptyMessage* TestEmptyMessage::internal_default_instance() {
  return &TestEmptyMessage_default_instance_.get();
}
#endif  // PROTOBUF_INLINE_NOT_IN_HEADERS

// @@protoc_insertion_point(namespace_scope)

}  // namespace proto3_arena_lite_unittest

// @@protoc_insertion_point(global_scope)

// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: google/protobuf/unittest_mset_wire_format.proto

#ifndef PROTOBUF_google_2fprotobuf_2funittest_5fmset_5fwire_5fformat_2eproto__INCLUDED
#define PROTOBUF_google_2fprotobuf_2funittest_5fmset_5fwire_5fformat_2eproto__INCLUDED

#include <string>

#include <google/protobuf/stubs/common.h>

#if GOOGLE_PROTOBUF_VERSION < 3001000
#error This file was generated by a newer version of protoc which is
#error incompatible with your Protocol Buffer headers.  Please update
#error your headers.
#endif
#if 3001000 < GOOGLE_PROTOBUF_MIN_PROTOC_VERSION
#error This file was generated by an older version of protoc which is
#error incompatible with your Protocol Buffer headers.  Please
#error regenerate this file with a newer version of protoc.
#endif

#include <google/protobuf/arena.h>
#include <google/protobuf/arenastring.h>
#include <google/protobuf/generated_message_util.h>
#include <google/protobuf/metadata.h>
#include <google/protobuf/message.h>
#include <google/protobuf/repeated_field.h>
#include <google/protobuf/extension_set.h>
#include <google/protobuf/unknown_field_set.h>
// @@protoc_insertion_point(includes)

namespace proto2_wireformat_unittest {

// Internal implementation detail -- do not call these.
void protobuf_AddDesc_google_2fprotobuf_2funittest_5fmset_5fwire_5fformat_2eproto();
void protobuf_InitDefaults_google_2fprotobuf_2funittest_5fmset_5fwire_5fformat_2eproto();
void protobuf_AssignDesc_google_2fprotobuf_2funittest_5fmset_5fwire_5fformat_2eproto();
void protobuf_ShutdownFile_google_2fprotobuf_2funittest_5fmset_5fwire_5fformat_2eproto();

class TestMessageSet;
class TestMessageSetWireFormatContainer;

// ===================================================================

class TestMessageSet : public ::google::protobuf::Message /* @@protoc_insertion_point(class_definition:proto2_wireformat_unittest.TestMessageSet) */ {
 public:
  TestMessageSet();
  virtual ~TestMessageSet();

  TestMessageSet(const TestMessageSet& from);

  inline TestMessageSet& operator=(const TestMessageSet& from) {
    CopyFrom(from);
    return *this;
  }

  inline const ::google::protobuf::UnknownFieldSet& unknown_fields() const {
    return _internal_metadata_.unknown_fields();
  }

  inline ::google::protobuf::UnknownFieldSet* mutable_unknown_fields() {
    return _internal_metadata_.mutable_unknown_fields();
  }

  inline ::google::protobuf::Arena* GetArena() const { return GetArenaNoVirtual(); }
  inline void* GetMaybeArenaPointer() const {
    return MaybeArenaPtr();
  }
  static const ::google::protobuf::Descriptor* descriptor();
  static const TestMessageSet& default_instance();

  static const TestMessageSet* internal_default_instance();

  void UnsafeArenaSwap(TestMessageSet* other);
  void Swap(TestMessageSet* other);

  // implements Message ----------------------------------------------

  inline TestMessageSet* New() const { return New(NULL); }

  TestMessageSet* New(::google::protobuf::Arena* arena) const;
  void CopyFrom(const ::google::protobuf::Message& from);
  void MergeFrom(const ::google::protobuf::Message& from);
  void CopyFrom(const TestMessageSet& from);
  void MergeFrom(const TestMessageSet& from);
  void Clear();
  bool IsInitialized() const;

  size_t ByteSizeLong() const;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input);
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const;
  ::google::protobuf::uint8* InternalSerializeWithCachedSizesToArray(
      bool deterministic, ::google::protobuf::uint8* output) const;
  ::google::protobuf::uint8* SerializeWithCachedSizesToArray(::google::protobuf::uint8* output) const {
    return InternalSerializeWithCachedSizesToArray(false, output);
  }
  int GetCachedSize() const { return _cached_size_; }
  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const;
  void InternalSwap(TestMessageSet* other);
  void UnsafeMergeFrom(const TestMessageSet& from);
  protected:
  explicit TestMessageSet(::google::protobuf::Arena* arena);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::google::protobuf::Arena* arena);
  private:
  inline ::google::protobuf::Arena* GetArenaNoVirtual() const {
    return _internal_metadata_.arena();
  }
  inline void* MaybeArenaPtr() const {
    return _internal_metadata_.raw_arena_ptr();
  }
  public:

  ::google::protobuf::Metadata GetMetadata() const;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  GOOGLE_PROTOBUF_EXTENSION_ACCESSORS(TestMessageSet)
  // @@protoc_insertion_point(class_scope:proto2_wireformat_unittest.TestMessageSet)
 private:

  ::google::protobuf::internal::ExtensionSet _extensions_;

  ::google::protobuf::internal::InternalMetadataWithArena _internal_metadata_;
  friend class ::google::protobuf::Arena;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  ::google::protobuf::internal::HasBits<1> _has_bits_;
  mutable int _cached_size_;
  friend void  protobuf_InitDefaults_google_2fprotobuf_2funittest_5fmset_5fwire_5fformat_2eproto_impl();
  friend void  protobuf_AddDesc_google_2fprotobuf_2funittest_5fmset_5fwire_5fformat_2eproto_impl();
  friend void protobuf_AssignDesc_google_2fprotobuf_2funittest_5fmset_5fwire_5fformat_2eproto();
  friend void protobuf_ShutdownFile_google_2fprotobuf_2funittest_5fmset_5fwire_5fformat_2eproto();

  void InitAsDefaultInstance();
};
extern ::google::protobuf::internal::ExplicitlyConstructed<TestMessageSet> TestMessageSet_default_instance_;

// -------------------------------------------------------------------

class TestMessageSetWireFormatContainer : public ::google::protobuf::Message /* @@protoc_insertion_point(class_definition:proto2_wireformat_unittest.TestMessageSetWireFormatContainer) */ {
 public:
  TestMessageSetWireFormatContainer();
  virtual ~TestMessageSetWireFormatContainer();

  TestMessageSetWireFormatContainer(const TestMessageSetWireFormatContainer& from);

  inline TestMessageSetWireFormatContainer& operator=(const TestMessageSetWireFormatContainer& from) {
    CopyFrom(from);
    return *this;
  }

  inline const ::google::protobuf::UnknownFieldSet& unknown_fields() const {
    return _internal_metadata_.unknown_fields();
  }

  inline ::google::protobuf::UnknownFieldSet* mutable_unknown_fields() {
    return _internal_metadata_.mutable_unknown_fields();
  }

  inline ::google::protobuf::Arena* GetArena() const { return GetArenaNoVirtual(); }
  inline void* GetMaybeArenaPointer() const {
    return MaybeArenaPtr();
  }
  static const ::google::protobuf::Descriptor* descriptor();
  static const TestMessageSetWireFormatContainer& default_instance();

  static const TestMessageSetWireFormatContainer* internal_default_instance();

  void UnsafeArenaSwap(TestMessageSetWireFormatContainer* other);
  void Swap(TestMessageSetWireFormatContainer* other);

  // implements Message ----------------------------------------------

  inline TestMessageSetWireFormatContainer* New() const { return New(NULL); }

  TestMessageSetWireFormatContainer* New(::google::protobuf::Arena* arena) const;
  void CopyFrom(const ::google::protobuf::Message& from);
  void MergeFrom(const ::google::protobuf::Message& from);
  void CopyFrom(const TestMessageSetWireFormatContainer& from);
  void MergeFrom(const TestMessageSetWireFormatContainer& from);
  void Clear();
  bool IsInitialized() const;

  size_t ByteSizeLong() const;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input);
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const;
  ::google::protobuf::uint8* InternalSerializeWithCachedSizesToArray(
      bool deterministic, ::google::protobuf::uint8* output) const;
  ::google::protobuf::uint8* SerializeWithCachedSizesToArray(::google::protobuf::uint8* output) const {
    return InternalSerializeWithCachedSizesToArray(false, output);
  }
  int GetCachedSize() const { return _cached_size_; }
  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const;
  void InternalSwap(TestMessageSetWireFormatContainer* other);
  void UnsafeMergeFrom(const TestMessageSetWireFormatContainer& from);
  protected:
  explicit TestMessageSetWireFormatContainer(::google::protobuf::Arena* arena);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::google::protobuf::Arena* arena);
  private:
  inline ::google::protobuf::Arena* GetArenaNoVirtual() const {
    return _internal_metadata_.arena();
  }
  inline void* MaybeArenaPtr() const {
    return _internal_metadata_.raw_arena_ptr();
  }
  public:

  ::google::protobuf::Metadata GetMetadata() const;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // optional .proto2_wireformat_unittest.TestMessageSet message_set = 1;
  bool has_message_set() const;
  void clear_message_set();
  static const int kMessageSetFieldNumber = 1;
  private:
  void _slow_mutable_message_set();
  void _slow_set_allocated_message_set(
      ::google::protobuf::Arena* message_arena, ::proto2_wireformat_unittest::TestMessageSet** message_set);
  ::proto2_wireformat_unittest::TestMessageSet* _slow_release_message_set();
  public:
  const ::proto2_wireformat_unittest::TestMessageSet& message_set() const;
  ::proto2_wireformat_unittest::TestMessageSet* mutable_message_set();
  ::proto2_wireformat_unittest::TestMessageSet* release_message_set();
  void set_allocated_message_set(::proto2_wireformat_unittest::TestMessageSet* message_set);
  ::proto2_wireformat_unittest::TestMessageSet* unsafe_arena_release_message_set();
  void unsafe_arena_set_allocated_message_set(
      ::proto2_wireformat_unittest::TestMessageSet* message_set);

  // @@protoc_insertion_point(class_scope:proto2_wireformat_unittest.TestMessageSetWireFormatContainer)
 private:
  inline void set_has_message_set();
  inline void clear_has_message_set();

  ::google::protobuf::internal::InternalMetadataWithArena _internal_metadata_;
  friend class ::google::protobuf::Arena;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  ::google::protobuf::internal::HasBits<1> _has_bits_;
  mutable int _cached_size_;
  ::proto2_wireformat_unittest::TestMessageSet* message_set_;
  friend void  protobuf_InitDefaults_google_2fprotobuf_2funittest_5fmset_5fwire_5fformat_2eproto_impl();
  friend void  protobuf_AddDesc_google_2fprotobuf_2funittest_5fmset_5fwire_5fformat_2eproto_impl();
  friend void protobuf_AssignDesc_google_2fprotobuf_2funittest_5fmset_5fwire_5fformat_2eproto();
  friend void protobuf_ShutdownFile_google_2fprotobuf_2funittest_5fmset_5fwire_5fformat_2eproto();

  void InitAsDefaultInstance();
};
extern ::google::protobuf::internal::ExplicitlyConstructed<TestMessageSetWireFormatContainer> TestMessageSetWireFormatContainer_default_instance_;

// ===================================================================


// ===================================================================

#if !PROTOBUF_INLINE_NOT_IN_HEADERS
// TestMessageSet

inline const TestMessageSet* TestMessageSet::internal_default_instance() {
  return &TestMessageSet_default_instance_.get();
}
// -------------------------------------------------------------------

// TestMessageSetWireFormatContainer

// optional .proto2_wireformat_unittest.TestMessageSet message_set = 1;
inline bool TestMessageSetWireFormatContainer::has_message_set() const {
  return (_has_bits_[0] & 0x00000001u) != 0;
}
inline void TestMessageSetWireFormatContainer::set_has_message_set() {
  _has_bits_[0] |= 0x00000001u;
}
inline void TestMessageSetWireFormatContainer::clear_has_message_set() {
  _has_bits_[0] &= ~0x00000001u;
}
inline void TestMessageSetWireFormatContainer::clear_message_set() {
  if (message_set_ != NULL) message_set_->::proto2_wireformat_unittest::TestMessageSet::Clear();
  clear_has_message_set();
}
inline const ::proto2_wireformat_unittest::TestMessageSet& TestMessageSetWireFormatContainer::message_set() const {
  // @@protoc_insertion_point(field_get:proto2_wireformat_unittest.TestMessageSetWireFormatContainer.message_set)
  return message_set_ != NULL ? *message_set_
                         : *::proto2_wireformat_unittest::TestMessageSet::internal_default_instance();
}
inline ::proto2_wireformat_unittest::TestMessageSet* TestMessageSetWireFormatContainer::mutable_message_set() {
  set_has_message_set();
  if (message_set_ == NULL) {
    _slow_mutable_message_set();
  }
  // @@protoc_insertion_point(field_mutable:proto2_wireformat_unittest.TestMessageSetWireFormatContainer.message_set)
  return message_set_;
}
inline ::proto2_wireformat_unittest::TestMessageSet* TestMessageSetWireFormatContainer::release_message_set() {
  // @@protoc_insertion_point(field_release:proto2_wireformat_unittest.TestMessageSetWireFormatContainer.message_set)
  clear_has_message_set();
  if (GetArenaNoVirtual() != NULL) {
    return _slow_release_message_set();
  } else {
    ::proto2_wireformat_unittest::TestMessageSet* temp = message_set_;
    message_set_ = NULL;
    return temp;
  }
}
inline  void TestMessageSetWireFormatContainer::set_allocated_message_set(::proto2_wireformat_unittest::TestMessageSet* message_set) {
  ::google::protobuf::Arena* message_arena = GetArenaNoVirtual();
  if (message_arena == NULL) {
    delete message_set_;
  }
  if (message_set != NULL) {
    _slow_set_allocated_message_set(message_arena, &message_set);
  }
  message_set_ = message_set;
  if (message_set) {
    set_has_message_set();
  } else {
    clear_has_message_set();
  }
  // @@protoc_insertion_point(field_set_allocated:proto2_wireformat_unittest.TestMessageSetWireFormatContainer.message_set)
}

inline const TestMessageSetWireFormatContainer* TestMessageSetWireFormatContainer::internal_default_instance() {
  return &TestMessageSetWireFormatContainer_default_instance_.get();
}
#endif  // !PROTOBUF_INLINE_NOT_IN_HEADERS
// -------------------------------------------------------------------


// @@protoc_insertion_point(namespace_scope)

}  // namespace proto2_wireformat_unittest

// @@protoc_insertion_point(global_scope)

#endif  // PROTOBUF_google_2fprotobuf_2funittest_5fmset_5fwire_5fformat_2eproto__INCLUDED

// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: google/protobuf/unittest_well_known_types.proto

#ifndef PROTOBUF_google_2fprotobuf_2funittest_5fwell_5fknown_5ftypes_2eproto__INCLUDED
#define PROTOBUF_google_2fprotobuf_2funittest_5fwell_5fknown_5ftypes_2eproto__INCLUDED

#include <string>

#include <google/protobuf/stubs/common.h>

#if GOOGLE_PROTOBUF_VERSION < 3001000
#error This file was generated by a newer version of protoc which is
#error incompatible with your Protocol Buffer headers.  Please update
#error your headers.
#endif
#if 3001000 < GOOGLE_PROTOBUF_MIN_PROTOC_VERSION
#error This file was generated by an older version of protoc which is
#error incompatible with your Protocol Buffer headers.  Please
#error regenerate this file with a newer version of protoc.
#endif

#include <google/protobuf/arena.h>
#include <google/protobuf/arenastring.h>
#include <google/protobuf/generated_message_util.h>
#include <google/protobuf/metadata.h>
#include <google/protobuf/message.h>
#include <google/protobuf/repeated_field.h>
#include <google/protobuf/extension_set.h>
#include <google/protobuf/map.h>
#include <google/protobuf/map_field_inl.h>
#include <google/protobuf/unknown_field_set.h>
#include <google/protobuf/any.pb.h>
#include <google/protobuf/api.pb.h>
#include <google/protobuf/duration.pb.h>
#include <google/protobuf/empty.pb.h>
#include <google/protobuf/field_mask.pb.h>
#include <google/protobuf/source_context.pb.h>
#include <google/protobuf/struct.pb.h>
#include <google/protobuf/timestamp.pb.h>
#include <google/protobuf/type.pb.h>
#include <google/protobuf/wrappers.pb.h>
// @@protoc_insertion_point(includes)

namespace protobuf_unittest {

// Internal implementation detail -- do not call these.
void protobuf_AddDesc_google_2fprotobuf_2funittest_5fwell_5fknown_5ftypes_2eproto();
void protobuf_InitDefaults_google_2fprotobuf_2funittest_5fwell_5fknown_5ftypes_2eproto();
void protobuf_AssignDesc_google_2fprotobuf_2funittest_5fwell_5fknown_5ftypes_2eproto();
void protobuf_ShutdownFile_google_2fprotobuf_2funittest_5fwell_5fknown_5ftypes_2eproto();

class MapWellKnownTypes;
class OneofWellKnownTypes;
class RepeatedWellKnownTypes;
class TestWellKnownTypes;

// ===================================================================

class TestWellKnownTypes : public ::google::protobuf::Message /* @@protoc_insertion_point(class_definition:protobuf_unittest.TestWellKnownTypes) */ {
 public:
  TestWellKnownTypes();
  virtual ~TestWellKnownTypes();

  TestWellKnownTypes(const TestWellKnownTypes& from);

  inline TestWellKnownTypes& operator=(const TestWellKnownTypes& from) {
    CopyFrom(from);
    return *this;
  }

  static const ::google::protobuf::Descriptor* descriptor();
  static const TestWellKnownTypes& default_instance();

  static const TestWellKnownTypes* internal_default_instance();

  void Swap(TestWellKnownTypes* other);

  // implements Message ----------------------------------------------

  inline TestWellKnownTypes* New() const { return New(NULL); }

  TestWellKnownTypes* New(::google::protobuf::Arena* arena) const;
  void CopyFrom(const ::google::protobuf::Message& from);
  void MergeFrom(const ::google::protobuf::Message& from);
  void CopyFrom(const TestWellKnownTypes& from);
  void MergeFrom(const TestWellKnownTypes& from);
  void Clear();
  bool IsInitialized() const;

  size_t ByteSizeLong() const;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input);
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const;
  ::google::protobuf::uint8* InternalSerializeWithCachedSizesToArray(
      bool deterministic, ::google::protobuf::uint8* output) const;
  ::google::protobuf::uint8* SerializeWithCachedSizesToArray(::google::protobuf::uint8* output) const {
    return InternalSerializeWithCachedSizesToArray(false, output);
  }
  int GetCachedSize() const { return _cached_size_; }
  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const;
  void InternalSwap(TestWellKnownTypes* other);
  void UnsafeMergeFrom(const TestWellKnownTypes& from);
  private:
  inline ::google::protobuf::Arena* GetArenaNoVirtual() const {
    return _internal_metadata_.arena();
  }
  inline void* MaybeArenaPtr() const {
    return _internal_metadata_.raw_arena_ptr();
  }
  public:

  ::google::protobuf::Metadata GetMetadata() const;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // optional .google.protobuf.Any any_field = 1;
  bool has_any_field() const;
  void clear_any_field();
  static const int kAnyFieldFieldNumber = 1;
  const ::google::protobuf::Any& any_field() const;
  ::google::protobuf::Any* mutable_any_field();
  ::google::protobuf::Any* release_any_field();
  void set_allocated_any_field(::google::protobuf::Any* any_field);

  // optional .google.protobuf.Api api_field = 2;
  bool has_api_field() const;
  void clear_api_field();
  static const int kApiFieldFieldNumber = 2;
  const ::google::protobuf::Api& api_field() const;
  ::google::protobuf::Api* mutable_api_field();
  ::google::protobuf::Api* release_api_field();
  void set_allocated_api_field(::google::protobuf::Api* api_field);

  // optional .google.protobuf.Duration duration_field = 3;
  bool has_duration_field() const;
  void clear_duration_field();
  static const int kDurationFieldFieldNumber = 3;
  const ::google::protobuf::Duration& duration_field() const;
  ::google::protobuf::Duration* mutable_duration_field();
  ::google::protobuf::Duration* release_duration_field();
  void set_allocated_duration_field(::google::protobuf::Duration* duration_field);

  // optional .google.protobuf.Empty empty_field = 4;
  bool has_empty_field() const;
  void clear_empty_field();
  static const int kEmptyFieldFieldNumber = 4;
  const ::google::protobuf::Empty& empty_field() const;
  ::google::protobuf::Empty* mutable_empty_field();
  ::google::protobuf::Empty* release_empty_field();
  void set_allocated_empty_field(::google::protobuf::Empty* empty_field);

  // optional .google.protobuf.FieldMask field_mask_field = 5;
  bool has_field_mask_field() const;
  void clear_field_mask_field();
  static const int kFieldMaskFieldFieldNumber = 5;
  const ::google::protobuf::FieldMask& field_mask_field() const;
  ::google::protobuf::FieldMask* mutable_field_mask_field();
  ::google::protobuf::FieldMask* release_field_mask_field();
  void set_allocated_field_mask_field(::google::protobuf::FieldMask* field_mask_field);

  // optional .google.protobuf.SourceContext source_context_field = 6;
  bool has_source_context_field() const;
  void clear_source_context_field();
  static const int kSourceContextFieldFieldNumber = 6;
  const ::google::protobuf::SourceContext& source_context_field() const;
  ::google::protobuf::SourceContext* mutable_source_context_field();
  ::google::protobuf::SourceContext* release_source_context_field();
  void set_allocated_source_context_field(::google::protobuf::SourceContext* source_context_field);

  // optional .google.protobuf.Struct struct_field = 7;
  bool has_struct_field() const;
  void clear_struct_field();
  static const int kStructFieldFieldNumber = 7;
  const ::google::protobuf::Struct& struct_field() const;
  ::google::protobuf::Struct* mutable_struct_field();
  ::google::protobuf::Struct* release_struct_field();
  void set_allocated_struct_field(::google::protobuf::Struct* struct_field);

  // optional .google.protobuf.Timestamp timestamp_field = 8;
  bool has_timestamp_field() const;
  void clear_timestamp_field();
  static const int kTimestampFieldFieldNumber = 8;
  const ::google::protobuf::Timestamp& timestamp_field() const;
  ::google::protobuf::Timestamp* mutable_timestamp_field();
  ::google::protobuf::Timestamp* release_timestamp_field();
  void set_allocated_timestamp_field(::google::protobuf::Timestamp* timestamp_field);

  // optional .google.protobuf.Type type_field = 9;
  bool has_type_field() const;
  void clear_type_field();
  static const int kTypeFieldFieldNumber = 9;
  const ::google::protobuf::Type& type_field() const;
  ::google::protobuf::Type* mutable_type_field();
  ::google::protobuf::Type* release_type_field();
  void set_allocated_type_field(::google::protobuf::Type* type_field);

  // optional .google.protobuf.DoubleValue double_field = 10;
  bool has_double_field() const;
  void clear_double_field();
  static const int kDoubleFieldFieldNumber = 10;
  const ::google::protobuf::DoubleValue& double_field() const;
  ::google::protobuf::DoubleValue* mutable_double_field();
  ::google::protobuf::DoubleValue* release_double_field();
  void set_allocated_double_field(::google::protobuf::DoubleValue* double_field);

  // optional .google.protobuf.FloatValue float_field = 11;
  bool has_float_field() const;
  void clear_float_field();
  static const int kFloatFieldFieldNumber = 11;
  const ::google::protobuf::FloatValue& float_field() const;
  ::google::protobuf::FloatValue* mutable_float_field();
  ::google::protobuf::FloatValue* release_float_field();
  void set_allocated_float_field(::google::protobuf::FloatValue* float_field);

  // optional .google.protobuf.Int64Value int64_field = 12;
  bool has_int64_field() const;
  void clear_int64_field();
  static const int kInt64FieldFieldNumber = 12;
  const ::google::protobuf::Int64Value& int64_field() const;
  ::google::protobuf::Int64Value* mutable_int64_field();
  ::google::protobuf::Int64Value* release_int64_field();
  void set_allocated_int64_field(::google::protobuf::Int64Value* int64_field);

  // optional .google.protobuf.UInt64Value uint64_field = 13;
  bool has_uint64_field() const;
  void clear_uint64_field();
  static const int kUint64FieldFieldNumber = 13;
  const ::google::protobuf::UInt64Value& uint64_field() const;
  ::google::protobuf::UInt64Value* mutable_uint64_field();
  ::google::protobuf::UInt64Value* release_uint64_field();
  void set_allocated_uint64_field(::google::protobuf::UInt64Value* uint64_field);

  // optional .google.protobuf.Int32Value int32_field = 14;
  bool has_int32_field() const;
  void clear_int32_field();
  static const int kInt32FieldFieldNumber = 14;
  const ::google::protobuf::Int32Value& int32_field() const;
  ::google::protobuf::Int32Value* mutable_int32_field();
  ::google::protobuf::Int32Value* release_int32_field();
  void set_allocated_int32_field(::google::protobuf::Int32Value* int32_field);

  // optional .google.protobuf.UInt32Value uint32_field = 15;
  bool has_uint32_field() const;
  void clear_uint32_field();
  static const int kUint32FieldFieldNumber = 15;
  const ::google::protobuf::UInt32Value& uint32_field() const;
  ::google::protobuf::UInt32Value* mutable_uint32_field();
  ::google::protobuf::UInt32Value* release_uint32_field();
  void set_allocated_uint32_field(::google::protobuf::UInt32Value* uint32_field);

  // optional .google.protobuf.BoolValue bool_field = 16;
  bool has_bool_field() const;
  void clear_bool_field();
  static const int kBoolFieldFieldNumber = 16;
  const ::google::protobuf::BoolValue& bool_field() const;
  ::google::protobuf::BoolValue* mutable_bool_field();
  ::google::protobuf::BoolValue* release_bool_field();
  void set_allocated_bool_field(::google::protobuf::BoolValue* bool_field);

  // optional .google.protobuf.StringValue string_field = 17;
  bool has_string_field() const;
  void clear_string_field();
  static const int kStringFieldFieldNumber = 17;
  const ::google::protobuf::StringValue& string_field() const;
  ::google::protobuf::StringValue* mutable_string_field();
  ::google::protobuf::StringValue* release_string_field();
  void set_allocated_string_field(::google::protobuf::StringValue* string_field);

  // optional .google.protobuf.BytesValue bytes_field = 18;
  bool has_bytes_field() const;
  void clear_bytes_field();
  static const int kBytesFieldFieldNumber = 18;
  const ::google::protobuf::BytesValue& bytes_field() const;
  ::google::protobuf::BytesValue* mutable_bytes_field();
  ::google::protobuf::BytesValue* release_bytes_field();
  void set_allocated_bytes_field(::google::protobuf::BytesValue* bytes_field);

  // optional .google.protobuf.Value value_field = 19;
  bool has_value_field() const;
  void clear_value_field();
  static const int kValueFieldFieldNumber = 19;
  const ::google::protobuf::Value& value_field() const;
  ::google::protobuf::Value* mutable_value_field();
  ::google::protobuf::Value* release_value_field();
  void set_allocated_value_field(::google::protobuf::Value* value_field);

  // @@protoc_insertion_point(class_scope:protobuf_unittest.TestWellKnownTypes)
 private:

  ::google::protobuf::internal::InternalMetadataWithArena _internal_metadata_;
  ::google::protobuf::Any* any_field_;
  ::google::protobuf::Api* api_field_;
  ::google::protobuf::Duration* duration_field_;
  ::google::protobuf::Empty* empty_field_;
  ::google::protobuf::FieldMask* field_mask_field_;
  ::google::protobuf::SourceContext* source_context_field_;
  ::google::protobuf::Struct* struct_field_;
  ::google::protobuf::Timestamp* timestamp_field_;
  ::google::protobuf::Type* type_field_;
  ::google::protobuf::DoubleValue* double_field_;
  ::google::protobuf::FloatValue* float_field_;
  ::google::protobuf::Int64Value* int64_field_;
  ::google::protobuf::UInt64Value* uint64_field_;
  ::google::protobuf::Int32Value* int32_field_;
  ::google::protobuf::UInt32Value* uint32_field_;
  ::google::protobuf::BoolValue* bool_field_;
  ::google::protobuf::StringValue* string_field_;
  ::google::protobuf::BytesValue* bytes_field_;
  ::google::protobuf::Value* value_field_;
  mutable int _cached_size_;
  friend void  protobuf_InitDefaults_google_2fprotobuf_2funittest_5fwell_5fknown_5ftypes_2eproto_impl();
  friend void  protobuf_AddDesc_google_2fprotobuf_2funittest_5fwell_5fknown_5ftypes_2eproto_impl();
  friend void protobuf_AssignDesc_google_2fprotobuf_2funittest_5fwell_5fknown_5ftypes_2eproto();
  friend void protobuf_ShutdownFile_google_2fprotobuf_2funittest_5fwell_5fknown_5ftypes_2eproto();

  void InitAsDefaultInstance();
};
extern ::google::protobuf::internal::ExplicitlyConstructed<TestWellKnownTypes> TestWellKnownTypes_default_instance_;

// -------------------------------------------------------------------

class RepeatedWellKnownTypes : public ::google::protobuf::Message /* @@protoc_insertion_point(class_definition:protobuf_unittest.RepeatedWellKnownTypes) */ {
 public:
  RepeatedWellKnownTypes();
  virtual ~RepeatedWellKnownTypes();

  RepeatedWellKnownTypes(const RepeatedWellKnownTypes& from);

  inline RepeatedWellKnownTypes& operator=(const RepeatedWellKnownTypes& from) {
    CopyFrom(from);
    return *this;
  }

  static const ::google::protobuf::Descriptor* descriptor();
  static const RepeatedWellKnownTypes& default_instance();

  static const RepeatedWellKnownTypes* internal_default_instance();

  void Swap(RepeatedWellKnownTypes* other);

  // implements Message ----------------------------------------------

  inline RepeatedWellKnownTypes* New() const { return New(NULL); }

  RepeatedWellKnownTypes* New(::google::protobuf::Arena* arena) const;
  void CopyFrom(const ::google::protobuf::Message& from);
  void MergeFrom(const ::google::protobuf::Message& from);
  void CopyFrom(const RepeatedWellKnownTypes& from);
  void MergeFrom(const RepeatedWellKnownTypes& from);
  void Clear();
  bool IsInitialized() const;

  size_t ByteSizeLong() const;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input);
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const;
  ::google::protobuf::uint8* InternalSerializeWithCachedSizesToArray(
      bool deterministic, ::google::protobuf::uint8* output) const;
  ::google::protobuf::uint8* SerializeWithCachedSizesToArray(::google::protobuf::uint8* output) const {
    return InternalSerializeWithCachedSizesToArray(false, output);
  }
  int GetCachedSize() const { return _cached_size_; }
  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const;
  void InternalSwap(RepeatedWellKnownTypes* other);
  void UnsafeMergeFrom(const RepeatedWellKnownTypes& from);
  private:
  inline ::google::protobuf::Arena* GetArenaNoVirtual() const {
    return _internal_metadata_.arena();
  }
  inline void* MaybeArenaPtr() const {
    return _internal_metadata_.raw_arena_ptr();
  }
  public:

  ::google::protobuf::Metadata GetMetadata() const;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // repeated .google.protobuf.Any any_field = 1;
  int any_field_size() const;
  void clear_any_field();
  static const int kAnyFieldFieldNumber = 1;
  const ::google::protobuf::Any& any_field(int index) const;
  ::google::protobuf::Any* mutable_any_field(int index);
  ::google::protobuf::Any* add_any_field();
  ::google::protobuf::RepeatedPtrField< ::google::protobuf::Any >*
      mutable_any_field();
  const ::google::protobuf::RepeatedPtrField< ::google::protobuf::Any >&
      any_field() const;

  // repeated .google.protobuf.Api api_field = 2;
  int api_field_size() const;
  void clear_api_field();
  static const int kApiFieldFieldNumber = 2;
  const ::google::protobuf::Api& api_field(int index) const;
  ::google::protobuf::Api* mutable_api_field(int index);
  ::google::protobuf::Api* add_api_field();
  ::google::protobuf::RepeatedPtrField< ::google::protobuf::Api >*
      mutable_api_field();
  const ::google::protobuf::RepeatedPtrField< ::google::protobuf::Api >&
      api_field() const;

  // repeated .google.protobuf.Duration duration_field = 3;
  int duration_field_size() const;
  void clear_duration_field();
  static const int kDurationFieldFieldNumber = 3;
  const ::google::protobuf::Duration& duration_field(int index) const;
  ::google::protobuf::Duration* mutable_duration_field(int index);
  ::google::protobuf::Duration* add_duration_field();
  ::google::protobuf::RepeatedPtrField< ::google::protobuf::Duration >*
      mutable_duration_field();
  const ::google::protobuf::RepeatedPtrField< ::google::protobuf::Duration >&
      duration_field() const;

  // repeated .google.protobuf.Empty empty_field = 4;
  int empty_field_size() const;
  void clear_empty_field();
  static const int kEmptyFieldFieldNumber = 4;
  const ::google::protobuf::Empty& empty_field(int index) const;
  ::google::protobuf::Empty* mutable_empty_field(int index);
  ::google::protobuf::Empty* add_empty_field();
  ::google::protobuf::RepeatedPtrField< ::google::protobuf::Empty >*
      mutable_empty_field();
  const ::google::protobuf::RepeatedPtrField< ::google::protobuf::Empty >&
      empty_field() const;

  // repeated .google.protobuf.FieldMask field_mask_field = 5;
  int field_mask_field_size() const;
  void clear_field_mask_field();
  static const int kFieldMaskFieldFieldNumber = 5;
  const ::google::protobuf::FieldMask& field_mask_field(int index) const;
  ::google::protobuf::FieldMask* mutable_field_mask_field(int index);
  ::google::protobuf::FieldMask* add_field_mask_field();
  ::google::protobuf::RepeatedPtrField< ::google::protobuf::FieldMask >*
      mutable_field_mask_field();
  const ::google::protobuf::RepeatedPtrField< ::google::protobuf::FieldMask >&
      field_mask_field() const;

  // repeated .google.protobuf.SourceContext source_context_field = 6;
  int source_context_field_size() const;
  void clear_source_context_field();
  static const int kSourceContextFieldFieldNumber = 6;
  const ::google::protobuf::SourceContext& source_context_field(int index) const;
  ::google::protobuf::SourceContext* mutable_source_context_field(int index);
  ::google::protobuf::SourceContext* add_source_context_field();
  ::google::protobuf::RepeatedPtrField< ::google::protobuf::SourceContext >*
      mutable_source_context_field();
  const ::google::protobuf::RepeatedPtrField< ::google::protobuf::SourceContext >&
      source_context_field() const;

  // repeated .google.protobuf.Struct struct_field = 7;
  int struct_field_size() const;
  void clear_struct_field();
  static const int kStructFieldFieldNumber = 7;
  const ::google::protobuf::Struct& struct_field(int index) const;
  ::google::protobuf::Struct* mutable_struct_field(int index);
  ::google::protobuf::Struct* add_struct_field();
  ::google::protobuf::RepeatedPtrField< ::google::protobuf::Struct >*
      mutable_struct_field();
  const ::google::protobuf::RepeatedPtrField< ::google::protobuf::Struct >&
      struct_field() const;

  // repeated .google.protobuf.Timestamp timestamp_field = 8;
  int timestamp_field_size() const;
  void clear_timestamp_field();
  static const int kTimestampFieldFieldNumber = 8;
  const ::google::protobuf::Timestamp& timestamp_field(int index) const;
  ::google::protobuf::Timestamp* mutable_timestamp_field(int index);
  ::google::protobuf::Timestamp* add_timestamp_field();
  ::google::protobuf::RepeatedPtrField< ::google::protobuf::Timestamp >*
      mutable_timestamp_field();
  const ::google::protobuf::RepeatedPtrField< ::google::protobuf::Timestamp >&
      timestamp_field() const;

  // repeated .google.protobuf.Type type_field = 9;
  int type_field_size() const;
  void clear_type_field();
  static const int kTypeFieldFieldNumber = 9;
  const ::google::protobuf::Type& type_field(int index) const;
  ::google::protobuf::Type* mutable_type_field(int index);
  ::google::protobuf::Type* add_type_field();
  ::google::protobuf::RepeatedPtrField< ::google::protobuf::Type >*
      mutable_type_field();
  const ::google::protobuf::RepeatedPtrField< ::google::protobuf::Type >&
      type_field() const;

  // repeated .google.protobuf.DoubleValue double_field = 10;
  int double_field_size() const;
  void clear_double_field();
  static const int kDoubleFieldFieldNumber = 10;
  const ::google::protobuf::DoubleValue& double_field(int index) const;
  ::google::protobuf::DoubleValue* mutable_double_field(int index);
  ::google::protobuf::DoubleValue* add_double_field();
  ::google::protobuf::RepeatedPtrField< ::google::protobuf::DoubleValue >*
      mutable_double_field();
  const ::google::protobuf::RepeatedPtrField< ::google::protobuf::DoubleValue >&
      double_field() const;

  // repeated .google.protobuf.FloatValue float_field = 11;
  int float_field_size() const;
  void clear_float_field();
  static const int kFloatFieldFieldNumber = 11;
  const ::google::protobuf::FloatValue& float_field(int index) const;
  ::google::protobuf::FloatValue* mutable_float_field(int index);
  ::google::protobuf::FloatValue* add_float_field();
  ::google::protobuf::RepeatedPtrField< ::google::protobuf::FloatValue >*
      mutable_float_field();
  const ::google::protobuf::RepeatedPtrField< ::google::protobuf::FloatValue >&
      float_field() const;

  // repeated .google.protobuf.Int64Value int64_field = 12;
  int int64_field_size() const;
  void clear_int64_field();
  static const int kInt64FieldFieldNumber = 12;
  const ::google::protobuf::Int64Value& int64_field(int index) const;
  ::google::protobuf::Int64Value* mutable_int64_field(int index);
  ::google::protobuf::Int64Value* add_int64_field();
  ::google::protobuf::RepeatedPtrField< ::google::protobuf::Int64Value >*
      mutable_int64_field();
  const ::google::protobuf::RepeatedPtrField< ::google::protobuf::Int64Value >&
      int64_field() const;

  // repeated .google.protobuf.UInt64Value uint64_field = 13;
  int uint64_field_size() const;
  void clear_uint64_field();
  static const int kUint64FieldFieldNumber = 13;
  const ::google::protobuf::UInt64Value& uint64_field(int index) const;
  ::google::protobuf::UInt64Value* mutable_uint64_field(int index);
  ::google::protobuf::UInt64Value* add_uint64_field();
  ::google::protobuf::RepeatedPtrField< ::google::protobuf::UInt64Value >*
      mutable_uint64_field();
  const ::google::protobuf::RepeatedPtrField< ::google::protobuf::UInt64Value >&
      uint64_field() const;

  // repeated .google.protobuf.Int32Value int32_field = 14;
  int int32_field_size() const;
  void clear_int32_field();
  static const int kInt32FieldFieldNumber = 14;
  const ::google::protobuf::Int32Value& int32_field(int index) const;
  ::google::protobuf::Int32Value* mutable_int32_field(int index);
  ::google::protobuf::Int32Value* add_int32_field();
  ::google::protobuf::RepeatedPtrField< ::google::protobuf::Int32Value >*
      mutable_int32_field();
  const ::google::protobuf::RepeatedPtrField< ::google::protobuf::Int32Value >&
      int32_field() const;

  // repeated .google.protobuf.UInt32Value uint32_field = 15;
  int uint32_field_size() const;
  void clear_uint32_field();
  static const int kUint32FieldFieldNumber = 15;
  const ::google::protobuf::UInt32Value& uint32_field(int index) const;
  ::google::protobuf::UInt32Value* mutable_uint32_field(int index);
  ::google::protobuf::UInt32Value* add_uint32_field();
  ::google::protobuf::RepeatedPtrField< ::google::protobuf::UInt32Value >*
      mutable_uint32_field();
  const ::google::protobuf::RepeatedPtrField< ::google::protobuf::UInt32Value >&
      uint32_field() const;

  // repeated .google.protobuf.BoolValue bool_field = 16;
  int bool_field_size() const;
  void clear_bool_field();
  static const int kBoolFieldFieldNumber = 16;
  const ::google::protobuf::BoolValue& bool_field(int index) const;
  ::google::protobuf::BoolValue* mutable_bool_field(int index);
  ::google::protobuf::BoolValue* add_bool_field();
  ::google::protobuf::RepeatedPtrField< ::google::protobuf::BoolValue >*
      mutable_bool_field();
  const ::google::protobuf::RepeatedPtrField< ::google::protobuf::BoolValue >&
      bool_field() const;

  // repeated .google.protobuf.StringValue string_field = 17;
  int string_field_size() const;
  void clear_string_field();
  static const int kStringFieldFieldNumber = 17;
  const ::google::protobuf::StringValue& string_field(int index) const;
  ::google::protobuf::StringValue* mutable_string_field(int index);
  ::google::protobuf::StringValue* add_string_field();
  ::google::protobuf::RepeatedPtrField< ::google::protobuf::StringValue >*
      mutable_string_field();
  const ::google::protobuf::RepeatedPtrField< ::google::protobuf::StringValue >&
      string_field() const;

  // repeated .google.protobuf.BytesValue bytes_field = 18;
  int bytes_field_size() const;
  void clear_bytes_field();
  static const int kBytesFieldFieldNumber = 18;
  const ::google::protobuf::BytesValue& bytes_field(int index) const;
  ::google::protobuf::BytesValue* mutable_bytes_field(int index);
  ::google::protobuf::BytesValue* add_bytes_field();
  ::google::protobuf::RepeatedPtrField< ::google::protobuf::BytesValue >*
      mutable_bytes_field();
  const ::google::protobuf::RepeatedPtrField< ::google::protobuf::BytesValue >&
      bytes_field() const;

  // @@protoc_insertion_point(class_scope:protobuf_unittest.RepeatedWellKnownTypes)
 private:

  ::google::protobuf::internal::InternalMetadataWithArena _internal_metadata_;
  ::google::protobuf::RepeatedPtrField< ::google::protobuf::Any > any_field_;
  ::google::protobuf::RepeatedPtrField< ::google::protobuf::Api > api_field_;
  ::google::protobuf::RepeatedPtrField< ::google::protobuf::Duration > duration_field_;
  ::google::protobuf::RepeatedPtrField< ::google::protobuf::Empty > empty_field_;
  ::google::protobuf::RepeatedPtrField< ::google::protobuf::FieldMask > field_mask_field_;
  ::google::protobuf::RepeatedPtrField< ::google::protobuf::SourceContext > source_context_field_;
  ::google::protobuf::RepeatedPtrField< ::google::protobuf::Struct > struct_field_;
  ::google::protobuf::RepeatedPtrField< ::google::protobuf::Timestamp > timestamp_field_;
  ::google::protobuf::RepeatedPtrField< ::google::protobuf::Type > type_field_;
  ::google::protobuf::RepeatedPtrField< ::google::protobuf::DoubleValue > double_field_;
  ::google::protobuf::RepeatedPtrField< ::google::protobuf::FloatValue > float_field_;
  ::google::protobuf::RepeatedPtrField< ::google::protobuf::Int64Value > int64_field_;
  ::google::protobuf::RepeatedPtrField< ::google::protobuf::UInt64Value > uint64_field_;
  ::google::protobuf::RepeatedPtrField< ::google::protobuf::Int32Value > int32_field_;
  ::google::protobuf::RepeatedPtrField< ::google::protobuf::UInt32Value > uint32_field_;
  ::google::protobuf::RepeatedPtrField< ::google::protobuf::BoolValue > bool_field_;
  ::google::protobuf::RepeatedPtrField< ::google::protobuf::StringValue > string_field_;
  ::google::protobuf::RepeatedPtrField< ::google::protobuf::BytesValue > bytes_field_;
  mutable int _cached_size_;
  friend void  protobuf_InitDefaults_google_2fprotobuf_2funittest_5fwell_5fknown_5ftypes_2eproto_impl();
  friend void  protobuf_AddDesc_google_2fprotobuf_2funittest_5fwell_5fknown_5ftypes_2eproto_impl();
  friend void protobuf_AssignDesc_google_2fprotobuf_2funittest_5fwell_5fknown_5ftypes_2eproto();
  friend void protobuf_ShutdownFile_google_2fprotobuf_2funittest_5fwell_5fknown_5ftypes_2eproto();

  void InitAsDefaultInstance();
};
extern ::google::protobuf::internal::ExplicitlyConstructed<RepeatedWellKnownTypes> RepeatedWellKnownTypes_default_instance_;

// -------------------------------------------------------------------

class OneofWellKnownTypes : public ::google::protobuf::Message /* @@protoc_insertion_point(class_definition:protobuf_unittest.OneofWellKnownTypes) */ {
 public:
  OneofWellKnownTypes();
  virtual ~OneofWellKnownTypes();

  OneofWellKnownTypes(const OneofWellKnownTypes& from);

  inline OneofWellKnownTypes& operator=(const OneofWellKnownTypes& from) {
    CopyFrom(from);
    return *this;
  }

  static const ::google::protobuf::Descriptor* descriptor();
  static const OneofWellKnownTypes& default_instance();

  enum OneofFieldCase {
    kAnyField = 1,
    kApiField = 2,
    kDurationField = 3,
    kEmptyField = 4,
    kFieldMaskField = 5,
    kSourceContextField = 6,
    kStructField = 7,
    kTimestampField = 8,
    kTypeField = 9,
    kDoubleField = 10,
    kFloatField = 11,
    kInt64Field = 12,
    kUint64Field = 13,
    kInt32Field = 14,
    kUint32Field = 15,
    kBoolField = 16,
    kStringField = 17,
    kBytesField = 18,
    ONEOF_FIELD_NOT_SET = 0,
  };

  static const OneofWellKnownTypes* internal_default_instance();

  void Swap(OneofWellKnownTypes* other);

  // implements Message ----------------------------------------------

  inline OneofWellKnownTypes* New() const { return New(NULL); }

  OneofWellKnownTypes* New(::google::protobuf::Arena* arena) const;
  void CopyFrom(const ::google::protobuf::Message& from);
  void MergeFrom(const ::google::protobuf::Message& from);
  void CopyFrom(const OneofWellKnownTypes& from);
  void MergeFrom(const OneofWellKnownTypes& from);
  void Clear();
  bool IsInitialized() const;

  size_t ByteSizeLong() const;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input);
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const;
  ::google::protobuf::uint8* InternalSerializeWithCachedSizesToArray(
      bool deterministic, ::google::protobuf::uint8* output) const;
  ::google::protobuf::uint8* SerializeWithCachedSizesToArray(::google::protobuf::uint8* output) const {
    return InternalSerializeWithCachedSizesToArray(false, output);
  }
  int GetCachedSize() const { return _cached_size_; }
  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const;
  void InternalSwap(OneofWellKnownTypes* other);
  void UnsafeMergeFrom(const OneofWellKnownTypes& from);
  private:
  inline ::google::protobuf::Arena* GetArenaNoVirtual() const {
    return _internal_metadata_.arena();
  }
  inline void* MaybeArenaPtr() const {
    return _internal_metadata_.raw_arena_ptr();
  }
  public:

  ::google::protobuf::Metadata GetMetadata() const;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // optional .google.protobuf.Any any_field = 1;
  bool has_any_field() const;
  void clear_any_field();
  static const int kAnyFieldFieldNumber = 1;
  const ::google::protobuf::Any& any_field() const;
  ::google::protobuf::Any* mutable_any_field();
  ::google::protobuf::Any* release_any_field();
  void set_allocated_any_field(::google::protobuf::Any* any_field);

  // optional .google.protobuf.Api api_field = 2;
  bool has_api_field() const;
  void clear_api_field();
  static const int kApiFieldFieldNumber = 2;
  const ::google::protobuf::Api& api_field() const;
  ::google::protobuf::Api* mutable_api_field();
  ::google::protobuf::Api* release_api_field();
  void set_allocated_api_field(::google::protobuf::Api* api_field);

  // optional .google.protobuf.Duration duration_field = 3;
  bool has_duration_field() const;
  void clear_duration_field();
  static const int kDurationFieldFieldNumber = 3;
  const ::google::protobuf::Duration& duration_field() const;
  ::google::protobuf::Duration* mutable_duration_field();
  ::google::protobuf::Duration* release_duration_field();
  void set_allocated_duration_field(::google::protobuf::Duration* duration_field);

  // optional .google.protobuf.Empty empty_field = 4;
  bool has_empty_field() const;
  void clear_empty_field();
  static const int kEmptyFieldFieldNumber = 4;
  const ::google::protobuf::Empty& empty_field() const;
  ::google::protobuf::Empty* mutable_empty_field();
  ::google::protobuf::Empty* release_empty_field();
  void set_allocated_empty_field(::google::protobuf::Empty* empty_field);

  // optional .google.protobuf.FieldMask field_mask_field = 5;
  bool has_field_mask_field() const;
  void clear_field_mask_field();
  static const int kFieldMaskFieldFieldNumber = 5;
  const ::google::protobuf::FieldMask& field_mask_field() const;
  ::google::protobuf::FieldMask* mutable_field_mask_field();
  ::google::protobuf::FieldMask* release_field_mask_field();
  void set_allocated_field_mask_field(::google::protobuf::FieldMask* field_mask_field);

  // optional .google.protobuf.SourceContext source_context_field = 6;
  bool has_source_context_field() const;
  void clear_source_context_field();
  static const int kSourceContextFieldFieldNumber = 6;
  const ::google::protobuf::SourceContext& source_context_field() const;
  ::google::protobuf::SourceContext* mutable_source_context_field();
  ::google::protobuf::SourceContext* release_source_context_field();
  void set_allocated_source_context_field(::google::protobuf::SourceContext* source_context_field);

  // optional .google.protobuf.Struct struct_field = 7;
  bool has_struct_field() const;
  void clear_struct_field();
  static const int kStructFieldFieldNumber = 7;
  const ::google::protobuf::Struct& struct_field() const;
  ::google::protobuf::Struct* mutable_struct_field();
  ::google::protobuf::Struct* release_struct_field();
  void set_allocated_struct_field(::google::protobuf::Struct* struct_field);

  // optional .google.protobuf.Timestamp timestamp_field = 8;
  bool has_timestamp_field() const;
  void clear_timestamp_field();
  static const int kTimestampFieldFieldNumber = 8;
  const ::google::protobuf::Timestamp& timestamp_field() const;
  ::google::protobuf::Timestamp* mutable_timestamp_field();
  ::google::protobuf::Timestamp* release_timestamp_field();
  void set_allocated_timestamp_field(::google::protobuf::Timestamp* timestamp_field);

  // optional .google.protobuf.Type type_field = 9;
  bool has_type_field() const;
  void clear_type_field();
  static const int kTypeFieldFieldNumber = 9;
  const ::google::protobuf::Type& type_field() const;
  ::google::protobuf::Type* mutable_type_field();
  ::google::protobuf::Type* release_type_field();
  void set_allocated_type_field(::google::protobuf::Type* type_field);

  // optional .google.protobuf.DoubleValue double_field = 10;
  bool has_double_field() const;
  void clear_double_field();
  static const int kDoubleFieldFieldNumber = 10;
  const ::google::protobuf::DoubleValue& double_field() const;
  ::google::protobuf::DoubleValue* mutable_double_field();
  ::google::protobuf::DoubleValue* release_double_field();
  void set_allocated_double_field(::google::protobuf::DoubleValue* double_field);

  // optional .google.protobuf.FloatValue float_field = 11;
  bool has_float_field() const;
  void clear_float_field();
  static const int kFloatFieldFieldNumber = 11;
  const ::google::protobuf::FloatValue& float_field() const;
  ::google::protobuf::FloatValue* mutable_float_field();
  ::google::protobuf::FloatValue* release_float_field();
  void set_allocated_float_field(::google::protobuf::FloatValue* float_field);

  // optional .google.protobuf.Int64Value int64_field = 12;
  bool has_int64_field() const;
  void clear_int64_field();
  static const int kInt64FieldFieldNumber = 12;
  const ::google::protobuf::Int64Value& int64_field() const;
  ::google::protobuf::Int64Value* mutable_int64_field();
  ::google::protobuf::Int64Value* release_int64_field();
  void set_allocated_int64_field(::google::protobuf::Int64Value* int64_field);

  // optional .google.protobuf.UInt64Value uint64_field = 13;
  bool has_uint64_field() const;
  void clear_uint64_field();
  static const int kUint64FieldFieldNumber = 13;
  const ::google::protobuf::UInt64Value& uint64_field() const;
  ::google::protobuf::UInt64Value* mutable_uint64_field();
  ::google::protobuf::UInt64Value* release_uint64_field();
  void set_allocated_uint64_field(::google::protobuf::UInt64Value* uint64_field);

  // optional .google.protobuf.Int32Value int32_field = 14;
  bool has_int32_field() const;
  void clear_int32_field();
  static const int kInt32FieldFieldNumber = 14;
  const ::google::protobuf::Int32Value& int32_field() const;
  ::google::protobuf::Int32Value* mutable_int32_field();
  ::google::protobuf::Int32Value* release_int32_field();
  void set_allocated_int32_field(::google::protobuf::Int32Value* int32_field);

  // optional .google.protobuf.UInt32Value uint32_field = 15;
  bool has_uint32_field() const;
  void clear_uint32_field();
  static const int kUint32FieldFieldNumber = 15;
  const ::google::protobuf::UInt32Value& uint32_field() const;
  ::google::protobuf::UInt32Value* mutable_uint32_field();
  ::google::protobuf::UInt32Value* release_uint32_field();
  void set_allocated_uint32_field(::google::protobuf::UInt32Value* uint32_field);

  // optional .google.protobuf.BoolValue bool_field = 16;
  bool has_bool_field() const;
  void clear_bool_field();
  static const int kBoolFieldFieldNumber = 16;
  const ::google::protobuf::BoolValue& bool_field() const;
  ::google::protobuf::BoolValue* mutable_bool_field();
  ::google::protobuf::BoolValue* release_bool_field();
  void set_allocated_bool_field(::google::protobuf::BoolValue* bool_field);

  // optional .google.protobuf.StringValue string_field = 17;
  bool has_string_field() const;
  void clear_string_field();
  static const int kStringFieldFieldNumber = 17;
  const ::google::protobuf::StringValue& string_field() const;
  ::google::protobuf::StringValue* mutable_string_field();
  ::google::protobuf::StringValue* release_string_field();
  void set_allocated_string_field(::google::protobuf::StringValue* string_field);

  // optional .google.protobuf.BytesValue bytes_field = 18;
  bool has_bytes_field() const;
  void clear_bytes_field();
  static const int kBytesFieldFieldNumber = 18;
  const ::google::protobuf::BytesValue& bytes_field() const;
  ::google::protobuf::BytesValue* mutable_bytes_field();
  ::google::protobuf::BytesValue* release_bytes_field();
  void set_allocated_bytes_field(::google::protobuf::BytesValue* bytes_field);

  OneofFieldCase oneof_field_case() const;
  // @@protoc_insertion_point(class_scope:protobuf_unittest.OneofWellKnownTypes)
 private:
  inline void set_has_any_field();
  inline void set_has_api_field();
  inline void set_has_duration_field();
  inline void set_has_empty_field();
  inline void set_has_field_mask_field();
  inline void set_has_source_context_field();
  inline void set_has_struct_field();
  inline void set_has_timestamp_field();
  inline void set_has_type_field();
  inline void set_has_double_field();
  inline void set_has_float_field();
  inline void set_has_int64_field();
  inline void set_has_uint64_field();
  inline void set_has_int32_field();
  inline void set_has_uint32_field();
  inline void set_has_bool_field();
  inline void set_has_string_field();
  inline void set_has_bytes_field();

  inline bool has_oneof_field() const;
  void clear_oneof_field();
  inline void clear_has_oneof_field();

  ::google::protobuf::internal::InternalMetadataWithArena _internal_metadata_;
  union OneofFieldUnion {
    OneofFieldUnion() {}
    ::google::protobuf::Any* any_field_;
    ::google::protobuf::Api* api_field_;
    ::google::protobuf::Duration* duration_field_;
    ::google::protobuf::Empty* empty_field_;
    ::google::protobuf::FieldMask* field_mask_field_;
    ::google::protobuf::SourceContext* source_context_field_;
    ::google::protobuf::Struct* struct_field_;
    ::google::protobuf::Timestamp* timestamp_field_;
    ::google::protobuf::Type* type_field_;
    ::google::protobuf::DoubleValue* double_field_;
    ::google::protobuf::FloatValue* float_field_;
    ::google::protobuf::Int64Value* int64_field_;
    ::google::protobuf::UInt64Value* uint64_field_;
    ::google::protobuf::Int32Value* int32_field_;
    ::google::protobuf::UInt32Value* uint32_field_;
    ::google::protobuf::BoolValue* bool_field_;
    ::google::protobuf::StringValue* string_field_;
    ::google::protobuf::BytesValue* bytes_field_;
  } oneof_field_;
  mutable int _cached_size_;
  ::google::protobuf::uint32 _oneof_case_[1];

  friend void  protobuf_InitDefaults_google_2fprotobuf_2funittest_5fwell_5fknown_5ftypes_2eproto_impl();
  friend void  protobuf_AddDesc_google_2fprotobuf_2funittest_5fwell_5fknown_5ftypes_2eproto_impl();
  friend void protobuf_AssignDesc_google_2fprotobuf_2funittest_5fwell_5fknown_5ftypes_2eproto();
  friend void protobuf_ShutdownFile_google_2fprotobuf_2funittest_5fwell_5fknown_5ftypes_2eproto();

  void InitAsDefaultInstance();
};
extern ::google::protobuf::internal::ExplicitlyConstructed<OneofWellKnownTypes> OneofWellKnownTypes_default_instance_;

// -------------------------------------------------------------------

class MapWellKnownTypes : public ::google::protobuf::Message /* @@protoc_insertion_point(class_definition:protobuf_unittest.MapWellKnownTypes) */ {
 public:
  MapWellKnownTypes();
  virtual ~MapWellKnownTypes();

  MapWellKnownTypes(const MapWellKnownTypes& from);

  inline MapWellKnownTypes& operator=(const MapWellKnownTypes& from) {
    CopyFrom(from);
    return *this;
  }

  static const ::google::protobuf::Descriptor* descriptor();
  static const MapWellKnownTypes& default_instance();

  static const MapWellKnownTypes* internal_default_instance();

  void Swap(MapWellKnownTypes* other);

  // implements Message ----------------------------------------------

  inline MapWellKnownTypes* New() const { return New(NULL); }

  MapWellKnownTypes* New(::google::protobuf::Arena* arena) const;
  void CopyFrom(const ::google::protobuf::Message& from);
  void MergeFrom(const ::google::protobuf::Message& from);
  void CopyFrom(const MapWellKnownTypes& from);
  void MergeFrom(const MapWellKnownTypes& from);
  void Clear();
  bool IsInitialized() const;

  size_t ByteSizeLong() const;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input);
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const;
  ::google::protobuf::uint8* InternalSerializeWithCachedSizesToArray(
      bool deterministic, ::google::protobuf::uint8* output) const;
  ::google::protobuf::uint8* SerializeWithCachedSizesToArray(::google::protobuf::uint8* output) const {
    return InternalSerializeWithCachedSizesToArray(false, output);
  }
  int GetCachedSize() const { return _cached_size_; }
  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const;
  void InternalSwap(MapWellKnownTypes* other);
  void UnsafeMergeFrom(const MapWellKnownTypes& from);
  private:
  inline ::google::protobuf::Arena* GetArenaNoVirtual() const {
    return _internal_metadata_.arena();
  }
  inline void* MaybeArenaPtr() const {
    return _internal_metadata_.raw_arena_ptr();
  }
  public:

  ::google::protobuf::Metadata GetMetadata() const;

  // nested types ----------------------------------------------------


  // accessors -------------------------------------------------------

  // map<int32, .google.protobuf.Any> any_field = 1;
  int any_field_size() const;
  void clear_any_field();
  static const int kAnyFieldFieldNumber = 1;
  const ::google::protobuf::Map< ::google::protobuf::int32, ::google::protobuf::Any >&
      any_field() const;
  ::google::protobuf::Map< ::google::protobuf::int32, ::google::protobuf::Any >*
      mutable_any_field();

  // map<int32, .google.protobuf.Api> api_field = 2;
  int api_field_size() const;
  void clear_api_field();
  static const int kApiFieldFieldNumber = 2;
  const ::google::protobuf::Map< ::google::protobuf::int32, ::google::protobuf::Api >&
      api_field() const;
  ::google::protobuf::Map< ::google::protobuf::int32, ::google::protobuf::Api >*
      mutable_api_field();

  // map<int32, .google.protobuf.Duration> duration_field = 3;
  int duration_field_size() const;
  void clear_duration_field();
  static const int kDurationFieldFieldNumber = 3;
  const ::google::protobuf::Map< ::google::protobuf::int32, ::google::protobuf::Duration >&
      duration_field() const;
  ::google::protobuf::Map< ::google::protobuf::int32, ::google::protobuf::Duration >*
      mutable_duration_field();

  // map<int32, .google.protobuf.Empty> empty_field = 4;
  int empty_field_size() const;
  void clear_empty_field();
  static const int kEmptyFieldFieldNumber = 4;
  const ::google::protobuf::Map< ::google::protobuf::int32, ::google::protobuf::Empty >&
      empty_field() const;
  ::google::protobuf::Map< ::google::protobuf::int32, ::google::protobuf::Empty >*
      mutable_empty_field();

  // map<int32, .google.protobuf.FieldMask> field_mask_field = 5;
  int field_mask_field_size() const;
  void clear_field_mask_field();
  static const int kFieldMaskFieldFieldNumber = 5;
  const ::google::protobuf::Map< ::google::protobuf::int32, ::google::protobuf::FieldMask >&
      field_mask_field() const;
  ::google::protobuf::Map< ::google::protobuf::int32, ::google::protobuf::FieldMask >*
      mutable_field_mask_field();

  // map<int32, .google.protobuf.SourceContext> source_context_field = 6;
  int source_context_field_size() const;
  void clear_source_context_field();
  static const int kSourceContextFieldFieldNumber = 6;
  const ::google::protobuf::Map< ::google::protobuf::int32, ::google::protobuf::SourceContext >&
      source_context_field() const;
  ::google::protobuf::Map< ::google::protobuf::int32, ::google::protobuf::SourceContext >*
      mutable_source_context_field();

  // map<int32, .google.protobuf.Struct> struct_field = 7;
  int struct_field_size() const;
  void clear_struct_field();
  static const int kStructFieldFieldNumber = 7;
  const ::google::protobuf::Map< ::google::protobuf::int32, ::google::protobuf::Struct >&
      struct_field() const;
  ::google::protobuf::Map< ::google::protobuf::int32, ::google::protobuf::Struct >*
      mutable_struct_field();

  // map<int32, .google.protobuf.Timestamp> timestamp_field = 8;
  int timestamp_field_size() const;
  void clear_timestamp_field();
  static const int kTimestampFieldFieldNumber = 8;
  const ::google::protobuf::Map< ::google::protobuf::int32, ::google::protobuf::Timestamp >&
      timestamp_field() const;
  ::google::protobuf::Map< ::google::protobuf::int32, ::google::protobuf::Timestamp >*
      mutable_timestamp_field();

  // map<int32, .google.protobuf.Type> type_field = 9;
  int type_field_size() const;
  void clear_type_field();
  static const int kTypeFieldFieldNumber = 9;
  const ::google::protobuf::Map< ::google::protobuf::int32, ::google::protobuf::Type >&
      type_field() const;
  ::google::protobuf::Map< ::google::protobuf::int32, ::google::protobuf::Type >*
      mutable_type_field();

  // map<int32, .google.protobuf.DoubleValue> double_field = 10;
  int double_field_size() const;
  void clear_double_field();
  static const int kDoubleFieldFieldNumber = 10;
  const ::google::protobuf::Map< ::google::protobuf::int32, ::google::protobuf::DoubleValue >&
      double_field() const;
  ::google::protobuf::Map< ::google::protobuf::int32, ::google::protobuf::DoubleValue >*
      mutable_double_field();

  // map<int32, .google.protobuf.FloatValue> float_field = 11;
  int float_field_size() const;
  void clear_float_field();
  static const int kFloatFieldFieldNumber = 11;
  const ::google::protobuf::Map< ::google::protobuf::int32, ::google::protobuf::FloatValue >&
      float_field() const;
  ::google::protobuf::Map< ::google::protobuf::int32, ::google::protobuf::FloatValue >*
      mutable_float_field();

  // map<int32, .google.protobuf.Int64Value> int64_field = 12;
  int int64_field_size() const;
  void clear_int64_field();
  static const int kInt64FieldFieldNumber = 12;
  const ::google::protobuf::Map< ::google::protobuf::int32, ::google::protobuf::Int64Value >&
      int64_field() const;
  ::google::protobuf::Map< ::google::protobuf::int32, ::google::protobuf::Int64Value >*
      mutable_int64_field();

  // map<int32, .google.protobuf.UInt64Value> uint64_field = 13;
  int uint64_field_size() const;
  void clear_uint64_field();
  static const int kUint64FieldFieldNumber = 13;
  const ::google::protobuf::Map< ::google::protobuf::int32, ::google::protobuf::UInt64Value >&
      uint64_field() const;
  ::google::protobuf::Map< ::google::protobuf::int32, ::google::protobuf::UInt64Value >*
      mutable_uint64_field();

  // map<int32, .google.protobuf.Int32Value> int32_field = 14;
  int int32_field_size() const;
  void clear_int32_field();
  static const int kInt32FieldFieldNumber = 14;
  const ::google::protobuf::Map< ::google::protobuf::int32, ::google::protobuf::Int32Value >&
      int32_field() const;
  ::google::protobuf::Map< ::google::protobuf::int32, ::google::protobuf::Int32Value >*
      mutable_int32_field();

  // map<int32, .google.protobuf.UInt32Value> uint32_field = 15;
  int uint32_field_size() const;
  void clear_uint32_field();
  static const int kUint32FieldFieldNumber = 15;
  const ::google::protobuf::Map< ::google::protobuf::int32, ::google::protobuf::UInt32Value >&
      uint32_field() const;
  ::google::protobuf::Map< ::google::protobuf::int32, ::google::protobuf::UInt32Value >*
      mutable_uint32_field();

  // map<int32, .google.protobuf.BoolValue> bool_field = 16;
  int bool_field_size() const;
  void clear_bool_field();
  static const int kBoolFieldFieldNumber = 16;
  const ::google::protobuf::Map< ::google::protobuf::int32, ::google::protobuf::BoolValue >&
      bool_field() const;
  ::google::protobuf::Map< ::google::protobuf::int32, ::google::protobuf::BoolValue >*
      mutable_bool_field();

  // map<int32, .google.protobuf.StringValue> string_field = 17;
  int string_field_size() const;
  void clear_string_field();
  static const int kStringFieldFieldNumber = 17;
  const ::google::protobuf::Map< ::google::protobuf::int32, ::google::protobuf::StringValue >&
      string_field() const;
  ::google::protobuf::Map< ::google::protobuf::int32, ::google::protobuf::StringValue >*
      mutable_string_field();

  // map<int32, .google.protobuf.BytesValue> bytes_field = 18;
  int bytes_field_size() const;
  void clear_bytes_field();
  static const int kBytesFieldFieldNumber = 18;
  const ::google::protobuf::Map< ::google::protobuf::int32, ::google::protobuf::BytesValue >&
      bytes_field() const;
  ::google::protobuf::Map< ::google::protobuf::int32, ::google::protobuf::BytesValue >*
      mutable_bytes_field();

  // @@protoc_insertion_point(class_scope:protobuf_unittest.MapWellKnownTypes)
 private:

  ::google::protobuf::internal::InternalMetadataWithArena _internal_metadata_;
  typedef ::google::protobuf::internal::MapEntryLite<
      ::google::protobuf::int32, ::google::protobuf::Any,
      ::google::protobuf::internal::WireFormatLite::TYPE_INT32,
      ::google::protobuf::internal::WireFormatLite::TYPE_MESSAGE,
      0 >
      MapWellKnownTypes_AnyFieldEntry;
  ::google::protobuf::internal::MapField<
      ::google::protobuf::int32, ::google::protobuf::Any,
      ::google::protobuf::internal::WireFormatLite::TYPE_INT32,
      ::google::protobuf::internal::WireFormatLite::TYPE_MESSAGE,
      0 > any_field_;
  typedef ::google::protobuf::internal::MapEntryLite<
      ::google::protobuf::int32, ::google::protobuf::Api,
      ::google::protobuf::internal::WireFormatLite::TYPE_INT32,
      ::google::protobuf::internal::WireFormatLite::TYPE_MESSAGE,
      0 >
      MapWellKnownTypes_ApiFieldEntry;
  ::google::protobuf::internal::MapField<
      ::google::protobuf::int32, ::google::protobuf::Api,
      ::google::protobuf::internal::WireFormatLite::TYPE_INT32,
      ::google::protobuf::internal::WireFormatLite::TYPE_MESSAGE,
      0 > api_field_;
  typedef ::google::protobuf::internal::MapEntryLite<
      ::google::protobuf::int32, ::google::protobuf::Duration,
      ::google::protobuf::internal::WireFormatLite::TYPE_INT32,
      ::google::protobuf::internal::WireFormatLite::TYPE_MESSAGE,
      0 >
      MapWellKnownTypes_DurationFieldEntry;
  ::google::protobuf::internal::MapField<
      ::google::protobuf::int32, ::google::protobuf::Duration,
      ::google::protobuf::internal::WireFormatLite::TYPE_INT32,
      ::google::protobuf::internal::WireFormatLite::TYPE_MESSAGE,
      0 > duration_field_;
  typedef ::google::protobuf::internal::MapEntryLite<
      ::google::protobuf::int32, ::google::protobuf::Empty,
      ::google::protobuf::internal::WireFormatLite::TYPE_INT32,
      ::google::protobuf::internal::WireFormatLite::TYPE_MESSAGE,
      0 >
      MapWellKnownTypes_EmptyFieldEntry;
  ::google::protobuf::internal::MapField<
      ::google::protobuf::int32, ::google::protobuf::Empty,
      ::google::protobuf::internal::WireFormatLite::TYPE_INT32,
      ::google::protobuf::internal::WireFormatLite::TYPE_MESSAGE,
      0 > empty_field_;
  typedef ::google::protobuf::internal::MapEntryLite<
      ::google::protobuf::int32, ::google::protobuf::FieldMask,
      ::google::protobuf::internal::WireFormatLite::TYPE_INT32,
      ::google::protobuf::internal::WireFormatLite::TYPE_MESSAGE,
      0 >
      MapWellKnownTypes_FieldMaskFieldEntry;
  ::google::protobuf::internal::MapField<
      ::google::protobuf::int32, ::google::protobuf::FieldMask,
      ::google::protobuf::internal::WireFormatLite::TYPE_INT32,
      ::google::protobuf::internal::WireFormatLite::TYPE_MESSAGE,
      0 > field_mask_field_;
  typedef ::google::protobuf::internal::MapEntryLite<
      ::google::protobuf::int32, ::google::protobuf::SourceContext,
      ::google::protobuf::internal::WireFormatLite::TYPE_INT32,
      ::google::protobuf::internal::WireFormatLite::TYPE_MESSAGE,
      0 >
      MapWellKnownTypes_SourceContextFieldEntry;
  ::google::protobuf::internal::MapField<
      ::google::protobuf::int32, ::google::protobuf::SourceContext,
      ::google::protobuf::internal::WireFormatLite::TYPE_INT32,
      ::google::protobuf::internal::WireFormatLite::TYPE_MESSAGE,
      0 > source_context_field_;
  typedef ::google::protobuf::internal::MapEntryLite<
      ::google::protobuf::int32, ::google::protobuf::Struct,
      ::google::protobuf::internal::WireFormatLite::TYPE_INT32,
      ::google::protobuf::internal::WireFormatLite::TYPE_MESSAGE,
      0 >
      MapWellKnownTypes_StructFieldEntry;
  ::google::protobuf::internal::MapField<
      ::google::protobuf::int32, ::google::protobuf::Struct,
      ::google::protobuf::internal::WireFormatLite::TYPE_INT32,
      ::google::protobuf::internal::WireFormatLite::TYPE_MESSAGE,
      0 > struct_field_;
  typedef ::google::protobuf::internal::MapEntryLite<
      ::google::protobuf::int32, ::google::protobuf::Timestamp,
      ::google::protobuf::internal::WireFormatLite::TYPE_INT32,
      ::google::protobuf::internal::WireFormatLite::TYPE_MESSAGE,
      0 >
      MapWellKnownTypes_TimestampFieldEntry;
  ::google::protobuf::internal::MapField<
      ::google::protobuf::int32, ::google::protobuf::Timestamp,
      ::google::protobuf::internal::WireFormatLite::TYPE_INT32,
      ::google::protobuf::internal::WireFormatLite::TYPE_MESSAGE,
      0 > timestamp_field_;
  typedef ::google::protobuf::internal::MapEntryLite<
      ::google::protobuf::int32, ::google::protobuf::Type,
      ::google::protobuf::internal::WireFormatLite::TYPE_INT32,
      ::google::protobuf::internal::WireFormatLite::TYPE_MESSAGE,
      0 >
      MapWellKnownTypes_TypeFieldEntry;
  ::google::protobuf::internal::MapField<
      ::google::protobuf::int32, ::google::protobuf::Type,
      ::google::protobuf::internal::WireFormatLite::TYPE_INT32,
      ::google::protobuf::internal::WireFormatLite::TYPE_MESSAGE,
      0 > type_field_;
  typedef ::google::protobuf::internal::MapEntryLite<
      ::google::protobuf::int32, ::google::protobuf::DoubleValue,
      ::google::protobuf::internal::WireFormatLite::TYPE_INT32,
      ::google::protobuf::internal::WireFormatLite::TYPE_MESSAGE,
      0 >
      MapWellKnownTypes_DoubleFieldEntry;
  ::google::protobuf::internal::MapField<
      ::google::protobuf::int32, ::google::protobuf::DoubleValue,
      ::google::protobuf::internal::WireFormatLite::TYPE_INT32,
      ::google::protobuf::internal::WireFormatLite::TYPE_MESSAGE,
      0 > double_field_;
  typedef ::google::protobuf::internal::MapEntryLite<
      ::google::protobuf::int32, ::google::protobuf::FloatValue,
      ::google::protobuf::internal::WireFormatLite::TYPE_INT32,
      ::google::protobuf::internal::WireFormatLite::TYPE_MESSAGE,
      0 >
      MapWellKnownTypes_FloatFieldEntry;
  ::google::protobuf::internal::MapField<
      ::google::protobuf::int32, ::google::protobuf::FloatValue,
      ::google::protobuf::internal::WireFormatLite::TYPE_INT32,
      ::google::protobuf::internal::WireFormatLite::TYPE_MESSAGE,
      0 > float_field_;
  typedef ::google::protobuf::internal::MapEntryLite<
      ::google::protobuf::int32, ::google::protobuf::Int64Value,
      ::google::protobuf::internal::WireFormatLite::TYPE_INT32,
      ::google::protobuf::internal::WireFormatLite::TYPE_MESSAGE,
      0 >
      MapWellKnownTypes_Int64FieldEntry;
  ::google::protobuf::internal::MapField<
      ::google::protobuf::int32, ::google::protobuf::Int64Value,
      ::google::protobuf::internal::WireFormatLite::TYPE_INT32,
      ::google::protobuf::internal::WireFormatLite::TYPE_MESSAGE,
      0 > int64_field_;
  typedef ::google::protobuf::internal::MapEntryLite<
      ::google::protobuf::int32, ::google::protobuf::UInt64Value,
      ::google::protobuf::internal::WireFormatLite::TYPE_INT32,
      ::google::protobuf::internal::WireFormatLite::TYPE_MESSAGE,
      0 >
      MapWellKnownTypes_Uint64FieldEntry;
  ::google::protobuf::internal::MapField<
      ::google::protobuf::int32, ::google::protobuf::UInt64Value,
      ::google::protobuf::internal::WireFormatLite::TYPE_INT32,
      ::google::protobuf::internal::WireFormatLite::TYPE_MESSAGE,
      0 > uint64_field_;
  typedef ::google::protobuf::internal::MapEntryLite<
      ::google::protobuf::int32, ::google::protobuf::Int32Value,
      ::google::protobuf::internal::WireFormatLite::TYPE_INT32,
      ::google::protobuf::internal::WireFormatLite::TYPE_MESSAGE,
      0 >
      MapWellKnownTypes_Int32FieldEntry;
  ::google::protobuf::internal::MapField<
      ::google::protobuf::int32, ::google::protobuf::Int32Value,
      ::google::protobuf::internal::WireFormatLite::TYPE_INT32,
      ::google::protobuf::internal::WireFormatLite::TYPE_MESSAGE,
      0 > int32_field_;
  typedef ::google::protobuf::internal::MapEntryLite<
      ::google::protobuf::int32, ::google::protobuf::UInt32Value,
      ::google::protobuf::internal::WireFormatLite::TYPE_INT32,
      ::google::protobuf::internal::WireFormatLite::TYPE_MESSAGE,
      0 >
      MapWellKnownTypes_Uint32FieldEntry;
  ::google::protobuf::internal::MapField<
      ::google::protobuf::int32, ::google::protobuf::UInt32Value,
      ::google::protobuf::internal::WireFormatLite::TYPE_INT32,
      ::google::protobuf::internal::WireFormatLite::TYPE_MESSAGE,
      0 > uint32_field_;
  typedef ::google::protobuf::internal::MapEntryLite<
      ::google::protobuf::int32, ::google::protobuf::BoolValue,
      ::google::protobuf::internal::WireFormatLite::TYPE_INT32,
      ::google::protobuf::internal::WireFormatLite::TYPE_MESSAGE,
      0 >
      MapWellKnownTypes_BoolFieldEntry;
  ::google::protobuf::internal::MapField<
      ::google::protobuf::int32, ::google::protobuf::BoolValue,
      ::google::protobuf::internal::WireFormatLite::TYPE_INT32,
      ::google::protobuf::internal::WireFormatLite::TYPE_MESSAGE,
      0 > bool_field_;
  typedef ::google::protobuf::internal::MapEntryLite<
      ::google::protobuf::int32, ::google::protobuf::StringValue,
      ::google::protobuf::internal::WireFormatLite::TYPE_INT32,
      ::google::protobuf::internal::WireFormatLite::TYPE_MESSAGE,
      0 >
      MapWellKnownTypes_StringFieldEntry;
  ::google::protobuf::internal::MapField<
      ::google::protobuf::int32, ::google::protobuf::StringValue,
      ::google::protobuf::internal::WireFormatLite::TYPE_INT32,
      ::google::protobuf::internal::WireFormatLite::TYPE_MESSAGE,
      0 > string_field_;
  typedef ::google::protobuf::internal::MapEntryLite<
      ::google::protobuf::int32, ::google::protobuf::BytesValue,
      ::google::protobuf::internal::WireFormatLite::TYPE_INT32,
      ::google::protobuf::internal::WireFormatLite::TYPE_MESSAGE,
      0 >
      MapWellKnownTypes_BytesFieldEntry;
  ::google::protobuf::internal::MapField<
      ::google::protobuf::int32, ::google::protobuf::BytesValue,
      ::google::protobuf::internal::WireFormatLite::TYPE_INT32,
      ::google::protobuf::internal::WireFormatLite::TYPE_MESSAGE,
      0 > bytes_field_;
  mutable int _cached_size_;
  friend void  protobuf_InitDefaults_google_2fprotobuf_2funittest_5fwell_5fknown_5ftypes_2eproto_impl();
  friend void  protobuf_AddDesc_google_2fprotobuf_2funittest_5fwell_5fknown_5ftypes_2eproto_impl();
  friend void protobuf_AssignDesc_google_2fprotobuf_2funittest_5fwell_5fknown_5ftypes_2eproto();
  friend void protobuf_ShutdownFile_google_2fprotobuf_2funittest_5fwell_5fknown_5ftypes_2eproto();

  void InitAsDefaultInstance();
};
extern ::google::protobuf::internal::ExplicitlyConstructed<MapWellKnownTypes> MapWellKnownTypes_default_instance_;

// ===================================================================


// ===================================================================

#if !PROTOBUF_INLINE_NOT_IN_HEADERS
// TestWellKnownTypes

// optional .google.protobuf.Any any_field = 1;
inline bool TestWellKnownTypes::has_any_field() const {
  return this != internal_default_instance() && any_field_ != NULL;
}
inline void TestWellKnownTypes::clear_any_field() {
  if (GetArenaNoVirtual() == NULL && any_field_ != NULL) delete any_field_;
  any_field_ = NULL;
}
inline const ::google::protobuf::Any& TestWellKnownTypes::any_field() const {
  // @@protoc_insertion_point(field_get:protobuf_unittest.TestWellKnownTypes.any_field)
  return any_field_ != NULL ? *any_field_
                         : *::google::protobuf::Any::internal_default_instance();
}
inline ::google::protobuf::Any* TestWellKnownTypes::mutable_any_field() {
  
  if (any_field_ == NULL) {
    any_field_ = new ::google::protobuf::Any;
  }
  // @@protoc_insertion_point(field_mutable:protobuf_unittest.TestWellKnownTypes.any_field)
  return any_field_;
}
inline ::google::protobuf::Any* TestWellKnownTypes::release_any_field() {
  // @@protoc_insertion_point(field_release:protobuf_unittest.TestWellKnownTypes.any_field)
  
  ::google::protobuf::Any* temp = any_field_;
  any_field_ = NULL;
  return temp;
}
inline void TestWellKnownTypes::set_allocated_any_field(::google::protobuf::Any* any_field) {
  delete any_field_;
  any_field_ = any_field;
  if (any_field) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_set_allocated:protobuf_unittest.TestWellKnownTypes.any_field)
}

// optional .google.protobuf.Api api_field = 2;
inline bool TestWellKnownTypes::has_api_field() const {
  return this != internal_default_instance() && api_field_ != NULL;
}
inline void TestWellKnownTypes::clear_api_field() {
  if (GetArenaNoVirtual() == NULL && api_field_ != NULL) delete api_field_;
  api_field_ = NULL;
}
inline const ::google::protobuf::Api& TestWellKnownTypes::api_field() const {
  // @@protoc_insertion_point(field_get:protobuf_unittest.TestWellKnownTypes.api_field)
  return api_field_ != NULL ? *api_field_
                         : *::google::protobuf::Api::internal_default_instance();
}
inline ::google::protobuf::Api* TestWellKnownTypes::mutable_api_field() {
  
  if (api_field_ == NULL) {
    api_field_ = new ::google::protobuf::Api;
  }
  // @@protoc_insertion_point(field_mutable:protobuf_unittest.TestWellKnownTypes.api_field)
  return api_field_;
}
inline ::google::protobuf::Api* TestWellKnownTypes::release_api_field() {
  // @@protoc_insertion_point(field_release:protobuf_unittest.TestWellKnownTypes.api_field)
  
  ::google::protobuf::Api* temp = api_field_;
  api_field_ = NULL;
  return temp;
}
inline void TestWellKnownTypes::set_allocated_api_field(::google::protobuf::Api* api_field) {
  delete api_field_;
  api_field_ = api_field;
  if (api_field) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_set_allocated:protobuf_unittest.TestWellKnownTypes.api_field)
}

// optional .google.protobuf.Duration duration_field = 3;
inline bool TestWellKnownTypes::has_duration_field() const {
  return this != internal_default_instance() && duration_field_ != NULL;
}
inline void TestWellKnownTypes::clear_duration_field() {
  if (GetArenaNoVirtual() == NULL && duration_field_ != NULL) delete duration_field_;
  duration_field_ = NULL;
}
inline const ::google::protobuf::Duration& TestWellKnownTypes::duration_field() const {
  // @@protoc_insertion_point(field_get:protobuf_unittest.TestWellKnownTypes.duration_field)
  return duration_field_ != NULL ? *duration_field_
                         : *::google::protobuf::Duration::internal_default_instance();
}
inline ::google::protobuf::Duration* TestWellKnownTypes::mutable_duration_field() {
  
  if (duration_field_ == NULL) {
    duration_field_ = new ::google::protobuf::Duration;
  }
  // @@protoc_insertion_point(field_mutable:protobuf_unittest.TestWellKnownTypes.duration_field)
  return duration_field_;
}
inline ::google::protobuf::Duration* TestWellKnownTypes::release_duration_field() {
  // @@protoc_insertion_point(field_release:protobuf_unittest.TestWellKnownTypes.duration_field)
  
  ::google::protobuf::Duration* temp = duration_field_;
  duration_field_ = NULL;
  return temp;
}
inline void TestWellKnownTypes::set_allocated_duration_field(::google::protobuf::Duration* duration_field) {
  delete duration_field_;
  if (duration_field != NULL && duration_field->GetArena() != NULL) {
    ::google::protobuf::Duration* new_duration_field = new ::google::protobuf::Duration;
    new_duration_field->CopyFrom(*duration_field);
    duration_field = new_duration_field;
  }
  duration_field_ = duration_field;
  if (duration_field) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_set_allocated:protobuf_unittest.TestWellKnownTypes.duration_field)
}

// optional .google.protobuf.Empty empty_field = 4;
inline bool TestWellKnownTypes::has_empty_field() const {
  return this != internal_default_instance() && empty_field_ != NULL;
}
inline void TestWellKnownTypes::clear_empty_field() {
  if (GetArenaNoVirtual() == NULL && empty_field_ != NULL) delete empty_field_;
  empty_field_ = NULL;
}
inline const ::google::protobuf::Empty& TestWellKnownTypes::empty_field() const {
  // @@protoc_insertion_point(field_get:protobuf_unittest.TestWellKnownTypes.empty_field)
  return empty_field_ != NULL ? *empty_field_
                         : *::google::protobuf::Empty::internal_default_instance();
}
inline ::google::protobuf::Empty* TestWellKnownTypes::mutable_empty_field() {
  
  if (empty_field_ == NULL) {
    empty_field_ = new ::google::protobuf::Empty;
  }
  // @@protoc_insertion_point(field_mutable:protobuf_unittest.TestWellKnownTypes.empty_field)
  return empty_field_;
}
inline ::google::protobuf::Empty* TestWellKnownTypes::release_empty_field() {
  // @@protoc_insertion_point(field_release:protobuf_unittest.TestWellKnownTypes.empty_field)
  
  ::google::protobuf::Empty* temp = empty_field_;
  empty_field_ = NULL;
  return temp;
}
inline void TestWellKnownTypes::set_allocated_empty_field(::google::protobuf::Empty* empty_field) {
  delete empty_field_;
  if (empty_field != NULL && empty_field->GetArena() != NULL) {
    ::google::protobuf::Empty* new_empty_field = new ::google::protobuf::Empty;
    new_empty_field->CopyFrom(*empty_field);
    empty_field = new_empty_field;
  }
  empty_field_ = empty_field;
  if (empty_field) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_set_allocated:protobuf_unittest.TestWellKnownTypes.empty_field)
}

// optional .google.protobuf.FieldMask field_mask_field = 5;
inline bool TestWellKnownTypes::has_field_mask_field() const {
  return this != internal_default_instance() && field_mask_field_ != NULL;
}
inline void TestWellKnownTypes::clear_field_mask_field() {
  if (GetArenaNoVirtual() == NULL && field_mask_field_ != NULL) delete field_mask_field_;
  field_mask_field_ = NULL;
}
inline const ::google::protobuf::FieldMask& TestWellKnownTypes::field_mask_field() const {
  // @@protoc_insertion_point(field_get:protobuf_unittest.TestWellKnownTypes.field_mask_field)
  return field_mask_field_ != NULL ? *field_mask_field_
                         : *::google::protobuf::FieldMask::internal_default_instance();
}
inline ::google::protobuf::FieldMask* TestWellKnownTypes::mutable_field_mask_field() {
  
  if (field_mask_field_ == NULL) {
    field_mask_field_ = new ::google::protobuf::FieldMask;
  }
  // @@protoc_insertion_point(field_mutable:protobuf_unittest.TestWellKnownTypes.field_mask_field)
  return field_mask_field_;
}
inline ::google::protobuf::FieldMask* TestWellKnownTypes::release_field_mask_field() {
  // @@protoc_insertion_point(field_release:protobuf_unittest.TestWellKnownTypes.field_mask_field)
  
  ::google::protobuf::FieldMask* temp = field_mask_field_;
  field_mask_field_ = NULL;
  return temp;
}
inline void TestWellKnownTypes::set_allocated_field_mask_field(::google::protobuf::FieldMask* field_mask_field) {
  delete field_mask_field_;
  field_mask_field_ = field_mask_field;
  if (field_mask_field) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_set_allocated:protobuf_unittest.TestWellKnownTypes.field_mask_field)
}

// optional .google.protobuf.SourceContext source_context_field = 6;
inline bool TestWellKnownTypes::has_source_context_field() const {
  return this != internal_default_instance() && source_context_field_ != NULL;
}
inline void TestWellKnownTypes::clear_source_context_field() {
  if (GetArenaNoVirtual() == NULL && source_context_field_ != NULL) delete source_context_field_;
  source_context_field_ = NULL;
}
inline const ::google::protobuf::SourceContext& TestWellKnownTypes::source_context_field() const {
  // @@protoc_insertion_point(field_get:protobuf_unittest.TestWellKnownTypes.source_context_field)
  return source_context_field_ != NULL ? *source_context_field_
                         : *::google::protobuf::SourceContext::internal_default_instance();
}
inline ::google::protobuf::SourceContext* TestWellKnownTypes::mutable_source_context_field() {
  
  if (source_context_field_ == NULL) {
    source_context_field_ = new ::google::protobuf::SourceContext;
  }
  // @@protoc_insertion_point(field_mutable:protobuf_unittest.TestWellKnownTypes.source_context_field)
  return source_context_field_;
}
inline ::google::protobuf::SourceContext* TestWellKnownTypes::release_source_context_field() {
  // @@protoc_insertion_point(field_release:protobuf_unittest.TestWellKnownTypes.source_context_field)
  
  ::google::protobuf::SourceContext* temp = source_context_field_;
  source_context_field_ = NULL;
  return temp;
}
inline void TestWellKnownTypes::set_allocated_source_context_field(::google::protobuf::SourceContext* source_context_field) {
  delete source_context_field_;
  source_context_field_ = source_context_field;
  if (source_context_field) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_set_allocated:protobuf_unittest.TestWellKnownTypes.source_context_field)
}

// optional .google.protobuf.Struct struct_field = 7;
inline bool TestWellKnownTypes::has_struct_field() const {
  return this != internal_default_instance() && struct_field_ != NULL;
}
inline void TestWellKnownTypes::clear_struct_field() {
  if (GetArenaNoVirtual() == NULL && struct_field_ != NULL) delete struct_field_;
  struct_field_ = NULL;
}
inline const ::google::protobuf::Struct& TestWellKnownTypes::struct_field() const {
  // @@protoc_insertion_point(field_get:protobuf_unittest.TestWellKnownTypes.struct_field)
  return struct_field_ != NULL ? *struct_field_
                         : *::google::protobuf::Struct::internal_default_instance();
}
inline ::google::protobuf::Struct* TestWellKnownTypes::mutable_struct_field() {
  
  if (struct_field_ == NULL) {
    struct_field_ = new ::google::protobuf::Struct;
  }
  // @@protoc_insertion_point(field_mutable:protobuf_unittest.TestWellKnownTypes.struct_field)
  return struct_field_;
}
inline ::google::protobuf::Struct* TestWellKnownTypes::release_struct_field() {
  // @@protoc_insertion_point(field_release:protobuf_unittest.TestWellKnownTypes.struct_field)
  
  ::google::protobuf::Struct* temp = struct_field_;
  struct_field_ = NULL;
  return temp;
}
inline void TestWellKnownTypes::set_allocated_struct_field(::google::protobuf::Struct* struct_field) {
  delete struct_field_;
  if (struct_field != NULL && struct_field->GetArena() != NULL) {
    ::google::protobuf::Struct* new_struct_field = new ::google::protobuf::Struct;
    new_struct_field->CopyFrom(*struct_field);
    struct_field = new_struct_field;
  }
  struct_field_ = struct_field;
  if (struct_field) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_set_allocated:protobuf_unittest.TestWellKnownTypes.struct_field)
}

// optional .google.protobuf.Timestamp timestamp_field = 8;
inline bool TestWellKnownTypes::has_timestamp_field() const {
  return this != internal_default_instance() && timestamp_field_ != NULL;
}
inline void TestWellKnownTypes::clear_timestamp_field() {
  if (GetArenaNoVirtual() == NULL && timestamp_field_ != NULL) delete timestamp_field_;
  timestamp_field_ = NULL;
}
inline const ::google::protobuf::Timestamp& TestWellKnownTypes::timestamp_field() const {
  // @@protoc_insertion_point(field_get:protobuf_unittest.TestWellKnownTypes.timestamp_field)
  return timestamp_field_ != NULL ? *timestamp_field_
                         : *::google::protobuf::Timestamp::internal_default_instance();
}
inline ::google::protobuf::Timestamp* TestWellKnownTypes::mutable_timestamp_field() {
  
  if (timestamp_field_ == NULL) {
    timestamp_field_ = new ::google::protobuf::Timestamp;
  }
  // @@protoc_insertion_point(field_mutable:protobuf_unittest.TestWellKnownTypes.timestamp_field)
  return timestamp_field_;
}
inline ::google::protobuf::Timestamp* TestWellKnownTypes::release_timestamp_field() {
  // @@protoc_insertion_point(field_release:protobuf_unittest.TestWellKnownTypes.timestamp_field)
  
  ::google::protobuf::Timestamp* temp = timestamp_field_;
  timestamp_field_ = NULL;
  return temp;
}
inline void TestWellKnownTypes::set_allocated_timestamp_field(::google::protobuf::Timestamp* timestamp_field) {
  delete timestamp_field_;
  if (timestamp_field != NULL && timestamp_field->GetArena() != NULL) {
    ::google::protobuf::Timestamp* new_timestamp_field = new ::google::protobuf::Timestamp;
    new_timestamp_field->CopyFrom(*timestamp_field);
    timestamp_field = new_timestamp_field;
  }
  timestamp_field_ = timestamp_field;
  if (timestamp_field) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_set_allocated:protobuf_unittest.TestWellKnownTypes.timestamp_field)
}

// optional .google.protobuf.Type type_field = 9;
inline bool TestWellKnownTypes::has_type_field() const {
  return this != internal_default_instance() && type_field_ != NULL;
}
inline void TestWellKnownTypes::clear_type_field() {
  if (GetArenaNoVirtual() == NULL && type_field_ != NULL) delete type_field_;
  type_field_ = NULL;
}
inline const ::google::protobuf::Type& TestWellKnownTypes::type_field() const {
  // @@protoc_insertion_point(field_get:protobuf_unittest.TestWellKnownTypes.type_field)
  return type_field_ != NULL ? *type_field_
                         : *::google::protobuf::Type::internal_default_instance();
}
inline ::google::protobuf::Type* TestWellKnownTypes::mutable_type_field() {
  
  if (type_field_ == NULL) {
    type_field_ = new ::google::protobuf::Type;
  }
  // @@protoc_insertion_point(field_mutable:protobuf_unittest.TestWellKnownTypes.type_field)
  return type_field_;
}
inline ::google::protobuf::Type* TestWellKnownTypes::release_type_field() {
  // @@protoc_insertion_point(field_release:protobuf_unittest.TestWellKnownTypes.type_field)
  
  ::google::protobuf::Type* temp = type_field_;
  type_field_ = NULL;
  return temp;
}
inline void TestWellKnownTypes::set_allocated_type_field(::google::protobuf::Type* type_field) {
  delete type_field_;
  if (type_field != NULL && type_field->GetArena() != NULL) {
    ::google::protobuf::Type* new_type_field = new ::google::protobuf::Type;
    new_type_field->CopyFrom(*type_field);
    type_field = new_type_field;
  }
  type_field_ = type_field;
  if (type_field) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_set_allocated:protobuf_unittest.TestWellKnownTypes.type_field)
}

// optional .google.protobuf.DoubleValue double_field = 10;
inline bool TestWellKnownTypes::has_double_field() const {
  return this != internal_default_instance() && double_field_ != NULL;
}
inline void TestWellKnownTypes::clear_double_field() {
  if (GetArenaNoVirtual() == NULL && double_field_ != NULL) delete double_field_;
  double_field_ = NULL;
}
inline const ::google::protobuf::DoubleValue& TestWellKnownTypes::double_field() const {
  // @@protoc_insertion_point(field_get:protobuf_unittest.TestWellKnownTypes.double_field)
  return double_field_ != NULL ? *double_field_
                         : *::google::protobuf::DoubleValue::internal_default_instance();
}
inline ::google::protobuf::DoubleValue* TestWellKnownTypes::mutable_double_field() {
  
  if (double_field_ == NULL) {
    double_field_ = new ::google::protobuf::DoubleValue;
  }
  // @@protoc_insertion_point(field_mutable:protobuf_unittest.TestWellKnownTypes.double_field)
  return double_field_;
}
inline ::google::protobuf::DoubleValue* TestWellKnownTypes::release_double_field() {
  // @@protoc_insertion_point(field_release:protobuf_unittest.TestWellKnownTypes.double_field)
  
  ::google::protobuf::DoubleValue* temp = double_field_;
  double_field_ = NULL;
  return temp;
}
inline void TestWellKnownTypes::set_allocated_double_field(::google::protobuf::DoubleValue* double_field) {
  delete double_field_;
  if (double_field != NULL && double_field->GetArena() != NULL) {
    ::google::protobuf::DoubleValue* new_double_field = new ::google::protobuf::DoubleValue;
    new_double_field->CopyFrom(*double_field);
    double_field = new_double_field;
  }
  double_field_ = double_field;
  if (double_field) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_set_allocated:protobuf_unittest.TestWellKnownTypes.double_field)
}

// optional .google.protobuf.FloatValue float_field = 11;
inline bool TestWellKnownTypes::has_float_field() const {
  return this != internal_default_instance() && float_field_ != NULL;
}
inline void TestWellKnownTypes::clear_float_field() {
  if (GetArenaNoVirtual() == NULL && float_field_ != NULL) delete float_field_;
  float_field_ = NULL;
}
inline const ::google::protobuf::FloatValue& TestWellKnownTypes::float_field() const {
  // @@protoc_insertion_point(field_get:protobuf_unittest.TestWellKnownTypes.float_field)
  return float_field_ != NULL ? *float_field_
                         : *::google::protobuf::FloatValue::internal_default_instance();
}
inline ::google::protobuf::FloatValue* TestWellKnownTypes::mutable_float_field() {
  
  if (float_field_ == NULL) {
    float_field_ = new ::google::protobuf::FloatValue;
  }
  // @@protoc_insertion_point(field_mutable:protobuf_unittest.TestWellKnownTypes.float_field)
  return float_field_;
}
inline ::google::protobuf::FloatValue* TestWellKnownTypes::release_float_field() {
  // @@protoc_insertion_point(field_release:protobuf_unittest.TestWellKnownTypes.float_field)
  
  ::google::protobuf::FloatValue* temp = float_field_;
  float_field_ = NULL;
  return temp;
}
inline void TestWellKnownTypes::set_allocated_float_field(::google::protobuf::FloatValue* float_field) {
  delete float_field_;
  if (float_field != NULL && float_field->GetArena() != NULL) {
    ::google::protobuf::FloatValue* new_float_field = new ::google::protobuf::FloatValue;
    new_float_field->CopyFrom(*float_field);
    float_field = new_float_field;
  }
  float_field_ = float_field;
  if (float_field) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_set_allocated:protobuf_unittest.TestWellKnownTypes.float_field)
}

// optional .google.protobuf.Int64Value int64_field = 12;
inline bool TestWellKnownTypes::has_int64_field() const {
  return this != internal_default_instance() && int64_field_ != NULL;
}
inline void TestWellKnownTypes::clear_int64_field() {
  if (GetArenaNoVirtual() == NULL && int64_field_ != NULL) delete int64_field_;
  int64_field_ = NULL;
}
inline const ::google::protobuf::Int64Value& TestWellKnownTypes::int64_field() const {
  // @@protoc_insertion_point(field_get:protobuf_unittest.TestWellKnownTypes.int64_field)
  return int64_field_ != NULL ? *int64_field_
                         : *::google::protobuf::Int64Value::internal_default_instance();
}
inline ::google::protobuf::Int64Value* TestWellKnownTypes::mutable_int64_field() {
  
  if (int64_field_ == NULL) {
    int64_field_ = new ::google::protobuf::Int64Value;
  }
  // @@protoc_insertion_point(field_mutable:protobuf_unittest.TestWellKnownTypes.int64_field)
  return int64_field_;
}
inline ::google::protobuf::Int64Value* TestWellKnownTypes::release_int64_field() {
  // @@protoc_insertion_point(field_release:protobuf_unittest.TestWellKnownTypes.int64_field)
  
  ::google::protobuf::Int64Value* temp = int64_field_;
  int64_field_ = NULL;
  return temp;
}
inline void TestWellKnownTypes::set_allocated_int64_field(::google::protobuf::Int64Value* int64_field) {
  delete int64_field_;
  if (int64_field != NULL && int64_field->GetArena() != NULL) {
    ::google::protobuf::Int64Value* new_int64_field = new ::google::protobuf::Int64Value;
    new_int64_field->CopyFrom(*int64_field);
    int64_field = new_int64_field;
  }
  int64_field_ = int64_field;
  if (int64_field) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_set_allocated:protobuf_unittest.TestWellKnownTypes.int64_field)
}

// optional .google.protobuf.UInt64Value uint64_field = 13;
inline bool TestWellKnownTypes::has_uint64_field() const {
  return this != internal_default_instance() && uint64_field_ != NULL;
}
inline void TestWellKnownTypes::clear_uint64_field() {
  if (GetArenaNoVirtual() == NULL && uint64_field_ != NULL) delete uint64_field_;
  uint64_field_ = NULL;
}
inline const ::google::protobuf::UInt64Value& TestWellKnownTypes::uint64_field() const {
  // @@protoc_insertion_point(field_get:protobuf_unittest.TestWellKnownTypes.uint64_field)
  return uint64_field_ != NULL ? *uint64_field_
                         : *::google::protobuf::UInt64Value::internal_default_instance();
}
inline ::google::protobuf::UInt64Value* TestWellKnownTypes::mutable_uint64_field() {
  
  if (uint64_field_ == NULL) {
    uint64_field_ = new ::google::protobuf::UInt64Value;
  }
  // @@protoc_insertion_point(field_mutable:protobuf_unittest.TestWellKnownTypes.uint64_field)
  return uint64_field_;
}
inline ::google::protobuf::UInt64Value* TestWellKnownTypes::release_uint64_field() {
  // @@protoc_insertion_point(field_release:protobuf_unittest.TestWellKnownTypes.uint64_field)
  
  ::google::protobuf::UInt64Value* temp = uint64_field_;
  uint64_field_ = NULL;
  return temp;
}
inline void TestWellKnownTypes::set_allocated_uint64_field(::google::protobuf::UInt64Value* uint64_field) {
  delete uint64_field_;
  if (uint64_field != NULL && uint64_field->GetArena() != NULL) {
    ::google::protobuf::UInt64Value* new_uint64_field = new ::google::protobuf::UInt64Value;
    new_uint64_field->CopyFrom(*uint64_field);
    uint64_field = new_uint64_field;
  }
  uint64_field_ = uint64_field;
  if (uint64_field) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_set_allocated:protobuf_unittest.TestWellKnownTypes.uint64_field)
}

// optional .google.protobuf.Int32Value int32_field = 14;
inline bool TestWellKnownTypes::has_int32_field() const {
  return this != internal_default_instance() && int32_field_ != NULL;
}
inline void TestWellKnownTypes::clear_int32_field() {
  if (GetArenaNoVirtual() == NULL && int32_field_ != NULL) delete int32_field_;
  int32_field_ = NULL;
}
inline const ::google::protobuf::Int32Value& TestWellKnownTypes::int32_field() const {
  // @@protoc_insertion_point(field_get:protobuf_unittest.TestWellKnownTypes.int32_field)
  return int32_field_ != NULL ? *int32_field_
                         : *::google::protobuf::Int32Value::internal_default_instance();
}
inline ::google::protobuf::Int32Value* TestWellKnownTypes::mutable_int32_field() {
  
  if (int32_field_ == NULL) {
    int32_field_ = new ::google::protobuf::Int32Value;
  }
  // @@protoc_insertion_point(field_mutable:protobuf_unittest.TestWellKnownTypes.int32_field)
  return int32_field_;
}
inline ::google::protobuf::Int32Value* TestWellKnownTypes::release_int32_field() {
  // @@protoc_insertion_point(field_release:protobuf_unittest.TestWellKnownTypes.int32_field)
  
  ::google::protobuf::Int32Value* temp = int32_field_;
  int32_field_ = NULL;
  return temp;
}
inline void TestWellKnownTypes::set_allocated_int32_field(::google::protobuf::Int32Value* int32_field) {
  delete int32_field_;
  if (int32_field != NULL && int32_field->GetArena() != NULL) {
    ::google::protobuf::Int32Value* new_int32_field = new ::google::protobuf::Int32Value;
    new_int32_field->CopyFrom(*int32_field);
    int32_field = new_int32_field;
  }
  int32_field_ = int32_field;
  if (int32_field) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_set_allocated:protobuf_unittest.TestWellKnownTypes.int32_field)
}

// optional .google.protobuf.UInt32Value uint32_field = 15;
inline bool TestWellKnownTypes::has_uint32_field() const {
  return this != internal_default_instance() && uint32_field_ != NULL;
}
inline void TestWellKnownTypes::clear_uint32_field() {
  if (GetArenaNoVirtual() == NULL && uint32_field_ != NULL) delete uint32_field_;
  uint32_field_ = NULL;
}
inline const ::google::protobuf::UInt32Value& TestWellKnownTypes::uint32_field() const {
  // @@protoc_insertion_point(field_get:protobuf_unittest.TestWellKnownTypes.uint32_field)
  return uint32_field_ != NULL ? *uint32_field_
                         : *::google::protobuf::UInt32Value::internal_default_instance();
}
inline ::google::protobuf::UInt32Value* TestWellKnownTypes::mutable_uint32_field() {
  
  if (uint32_field_ == NULL) {
    uint32_field_ = new ::google::protobuf::UInt32Value;
  }
  // @@protoc_insertion_point(field_mutable:protobuf_unittest.TestWellKnownTypes.uint32_field)
  return uint32_field_;
}
inline ::google::protobuf::UInt32Value* TestWellKnownTypes::release_uint32_field() {
  // @@protoc_insertion_point(field_release:protobuf_unittest.TestWellKnownTypes.uint32_field)
  
  ::google::protobuf::UInt32Value* temp = uint32_field_;
  uint32_field_ = NULL;
  return temp;
}
inline void TestWellKnownTypes::set_allocated_uint32_field(::google::protobuf::UInt32Value* uint32_field) {
  delete uint32_field_;
  if (uint32_field != NULL && uint32_field->GetArena() != NULL) {
    ::google::protobuf::UInt32Value* new_uint32_field = new ::google::protobuf::UInt32Value;
    new_uint32_field->CopyFrom(*uint32_field);
    uint32_field = new_uint32_field;
  }
  uint32_field_ = uint32_field;
  if (uint32_field) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_set_allocated:protobuf_unittest.TestWellKnownTypes.uint32_field)
}

// optional .google.protobuf.BoolValue bool_field = 16;
inline bool TestWellKnownTypes::has_bool_field() const {
  return this != internal_default_instance() && bool_field_ != NULL;
}
inline void TestWellKnownTypes::clear_bool_field() {
  if (GetArenaNoVirtual() == NULL && bool_field_ != NULL) delete bool_field_;
  bool_field_ = NULL;
}
inline const ::google::protobuf::BoolValue& TestWellKnownTypes::bool_field() const {
  // @@protoc_insertion_point(field_get:protobuf_unittest.TestWellKnownTypes.bool_field)
  return bool_field_ != NULL ? *bool_field_
                         : *::google::protobuf::BoolValue::internal_default_instance();
}
inline ::google::protobuf::BoolValue* TestWellKnownTypes::mutable_bool_field() {
  
  if (bool_field_ == NULL) {
    bool_field_ = new ::google::protobuf::BoolValue;
  }
  // @@protoc_insertion_point(field_mutable:protobuf_unittest.TestWellKnownTypes.bool_field)
  return bool_field_;
}
inline ::google::protobuf::BoolValue* TestWellKnownTypes::release_bool_field() {
  // @@protoc_insertion_point(field_release:protobuf_unittest.TestWellKnownTypes.bool_field)
  
  ::google::protobuf::BoolValue* temp = bool_field_;
  bool_field_ = NULL;
  return temp;
}
inline void TestWellKnownTypes::set_allocated_bool_field(::google::protobuf::BoolValue* bool_field) {
  delete bool_field_;
  if (bool_field != NULL && bool_field->GetArena() != NULL) {
    ::google::protobuf::BoolValue* new_bool_field = new ::google::protobuf::BoolValue;
    new_bool_field->CopyFrom(*bool_field);
    bool_field = new_bool_field;
  }
  bool_field_ = bool_field;
  if (bool_field) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_set_allocated:protobuf_unittest.TestWellKnownTypes.bool_field)
}

// optional .google.protobuf.StringValue string_field = 17;
inline bool TestWellKnownTypes::has_string_field() const {
  return this != internal_default_instance() && string_field_ != NULL;
}
inline void TestWellKnownTypes::clear_string_field() {
  if (GetArenaNoVirtual() == NULL && string_field_ != NULL) delete string_field_;
  string_field_ = NULL;
}
inline const ::google::protobuf::StringValue& TestWellKnownTypes::string_field() const {
  // @@protoc_insertion_point(field_get:protobuf_unittest.TestWellKnownTypes.string_field)
  return string_field_ != NULL ? *string_field_
                         : *::google::protobuf::StringValue::internal_default_instance();
}
inline ::google::protobuf::StringValue* TestWellKnownTypes::mutable_string_field() {
  
  if (string_field_ == NULL) {
    string_field_ = new ::google::protobuf::StringValue;
  }
  // @@protoc_insertion_point(field_mutable:protobuf_unittest.TestWellKnownTypes.string_field)
  return string_field_;
}
inline ::google::protobuf::StringValue* TestWellKnownTypes::release_string_field() {
  // @@protoc_insertion_point(field_release:protobuf_unittest.TestWellKnownTypes.string_field)
  
  ::google::protobuf::StringValue* temp = string_field_;
  string_field_ = NULL;
  return temp;
}
inline void TestWellKnownTypes::set_allocated_string_field(::google::protobuf::StringValue* string_field) {
  delete string_field_;
  if (string_field != NULL && string_field->GetArena() != NULL) {
    ::google::protobuf::StringValue* new_string_field = new ::google::protobuf::StringValue;
    new_string_field->CopyFrom(*string_field);
    string_field = new_string_field;
  }
  string_field_ = string_field;
  if (string_field) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_set_allocated:protobuf_unittest.TestWellKnownTypes.string_field)
}

// optional .google.protobuf.BytesValue bytes_field = 18;
inline bool TestWellKnownTypes::has_bytes_field() const {
  return this != internal_default_instance() && bytes_field_ != NULL;
}
inline void TestWellKnownTypes::clear_bytes_field() {
  if (GetArenaNoVirtual() == NULL && bytes_field_ != NULL) delete bytes_field_;
  bytes_field_ = NULL;
}
inline const ::google::protobuf::BytesValue& TestWellKnownTypes::bytes_field() const {
  // @@protoc_insertion_point(field_get:protobuf_unittest.TestWellKnownTypes.bytes_field)
  return bytes_field_ != NULL ? *bytes_field_
                         : *::google::protobuf::BytesValue::internal_default_instance();
}
inline ::google::protobuf::BytesValue* TestWellKnownTypes::mutable_bytes_field() {
  
  if (bytes_field_ == NULL) {
    bytes_field_ = new ::google::protobuf::BytesValue;
  }
  // @@protoc_insertion_point(field_mutable:protobuf_unittest.TestWellKnownTypes.bytes_field)
  return bytes_field_;
}
inline ::google::protobuf::BytesValue* TestWellKnownTypes::release_bytes_field() {
  // @@protoc_insertion_point(field_release:protobuf_unittest.TestWellKnownTypes.bytes_field)
  
  ::google::protobuf::BytesValue* temp = bytes_field_;
  bytes_field_ = NULL;
  return temp;
}
inline void TestWellKnownTypes::set_allocated_bytes_field(::google::protobuf::BytesValue* bytes_field) {
  delete bytes_field_;
  if (bytes_field != NULL && bytes_field->GetArena() != NULL) {
    ::google::protobuf::BytesValue* new_bytes_field = new ::google::protobuf::BytesValue;
    new_bytes_field->CopyFrom(*bytes_field);
    bytes_field = new_bytes_field;
  }
  bytes_field_ = bytes_field;
  if (bytes_field) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_set_allocated:protobuf_unittest.TestWellKnownTypes.bytes_field)
}

// optional .google.protobuf.Value value_field = 19;
inline bool TestWellKnownTypes::has_value_field() const {
  return this != internal_default_instance() && value_field_ != NULL;
}
inline void TestWellKnownTypes::clear_value_field() {
  if (GetArenaNoVirtual() == NULL && value_field_ != NULL) delete value_field_;
  value_field_ = NULL;
}
inline const ::google::protobuf::Value& TestWellKnownTypes::value_field() const {
  // @@protoc_insertion_point(field_get:protobuf_unittest.TestWellKnownTypes.value_field)
  return value_field_ != NULL ? *value_field_
                         : *::google::protobuf::Value::internal_default_instance();
}
inline ::google::protobuf::Value* TestWellKnownTypes::mutable_value_field() {
  
  if (value_field_ == NULL) {
    value_field_ = new ::google::protobuf::Value;
  }
  // @@protoc_insertion_point(field_mutable:protobuf_unittest.TestWellKnownTypes.value_field)
  return value_field_;
}
inline ::google::protobuf::Value* TestWellKnownTypes::release_value_field() {
  // @@protoc_insertion_point(field_release:protobuf_unittest.TestWellKnownTypes.value_field)
  
  ::google::protobuf::Value* temp = value_field_;
  value_field_ = NULL;
  return temp;
}
inline void TestWellKnownTypes::set_allocated_value_field(::google::protobuf::Value* value_field) {
  delete value_field_;
  if (value_field != NULL && value_field->GetArena() != NULL) {
    ::google::protobuf::Value* new_value_field = new ::google::protobuf::Value;
    new_value_field->CopyFrom(*value_field);
    value_field = new_value_field;
  }
  value_field_ = value_field;
  if (value_field) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_set_allocated:protobuf_unittest.TestWellKnownTypes.value_field)
}

inline const TestWellKnownTypes* TestWellKnownTypes::internal_default_instance() {
  return &TestWellKnownTypes_default_instance_.get();
}
// -------------------------------------------------------------------

// RepeatedWellKnownTypes

// repeated .google.protobuf.Any any_field = 1;
inline int RepeatedWellKnownTypes::any_field_size() const {
  return any_field_.size();
}
inline void RepeatedWellKnownTypes::clear_any_field() {
  any_field_.Clear();
}
inline const ::google::protobuf::Any& RepeatedWellKnownTypes::any_field(int index) const {
  // @@protoc_insertion_point(field_get:protobuf_unittest.RepeatedWellKnownTypes.any_field)
  return any_field_.Get(index);
}
inline ::google::protobuf::Any* RepeatedWellKnownTypes::mutable_any_field(int index) {
  // @@protoc_insertion_point(field_mutable:protobuf_unittest.RepeatedWellKnownTypes.any_field)
  return any_field_.Mutable(index);
}
inline ::google::protobuf::Any* RepeatedWellKnownTypes::add_any_field() {
  // @@protoc_insertion_point(field_add:protobuf_unittest.RepeatedWellKnownTypes.any_field)
  return any_field_.Add();
}
inline ::google::protobuf::RepeatedPtrField< ::google::protobuf::Any >*
RepeatedWellKnownTypes::mutable_any_field() {
  // @@protoc_insertion_point(field_mutable_list:protobuf_unittest.RepeatedWellKnownTypes.any_field)
  return &any_field_;
}
inline const ::google::protobuf::RepeatedPtrField< ::google::protobuf::Any >&
RepeatedWellKnownTypes::any_field() const {
  // @@protoc_insertion_point(field_list:protobuf_unittest.RepeatedWellKnownTypes.any_field)
  return any_field_;
}

// repeated .google.protobuf.Api api_field = 2;
inline int RepeatedWellKnownTypes::api_field_size() const {
  return api_field_.size();
}
inline void RepeatedWellKnownTypes::clear_api_field() {
  api_field_.Clear();
}
inline const ::google::protobuf::Api& RepeatedWellKnownTypes::api_field(int index) const {
  // @@protoc_insertion_point(field_get:protobuf_unittest.RepeatedWellKnownTypes.api_field)
  return api_field_.Get(index);
}
inline ::google::protobuf::Api* RepeatedWellKnownTypes::mutable_api_field(int index) {
  // @@protoc_insertion_point(field_mutable:protobuf_unittest.RepeatedWellKnownTypes.api_field)
  return api_field_.Mutable(index);
}
inline ::google::protobuf::Api* RepeatedWellKnownTypes::add_api_field() {
  // @@protoc_insertion_point(field_add:protobuf_unittest.RepeatedWellKnownTypes.api_field)
  return api_field_.Add();
}
inline ::google::protobuf::RepeatedPtrField< ::google::protobuf::Api >*
RepeatedWellKnownTypes::mutable_api_field() {
  // @@protoc_insertion_point(field_mutable_list:protobuf_unittest.RepeatedWellKnownTypes.api_field)
  return &api_field_;
}
inline const ::google::protobuf::RepeatedPtrField< ::google::protobuf::Api >&
RepeatedWellKnownTypes::api_field() const {
  // @@protoc_insertion_point(field_list:protobuf_unittest.RepeatedWellKnownTypes.api_field)
  return api_field_;
}

// repeated .google.protobuf.Duration duration_field = 3;
inline int RepeatedWellKnownTypes::duration_field_size() const {
  return duration_field_.size();
}
inline void RepeatedWellKnownTypes::clear_duration_field() {
  duration_field_.Clear();
}
inline const ::google::protobuf::Duration& RepeatedWellKnownTypes::duration_field(int index) const {
  // @@protoc_insertion_point(field_get:protobuf_unittest.RepeatedWellKnownTypes.duration_field)
  return duration_field_.Get(index);
}
inline ::google::protobuf::Duration* RepeatedWellKnownTypes::mutable_duration_field(int index) {
  // @@protoc_insertion_point(field_mutable:protobuf_unittest.RepeatedWellKnownTypes.duration_field)
  return duration_field_.Mutable(index);
}
inline ::google::protobuf::Duration* RepeatedWellKnownTypes::add_duration_field() {
  // @@protoc_insertion_point(field_add:protobuf_unittest.RepeatedWellKnownTypes.duration_field)
  return duration_field_.Add();
}
inline ::google::protobuf::RepeatedPtrField< ::google::protobuf::Duration >*
RepeatedWellKnownTypes::mutable_duration_field() {
  // @@protoc_insertion_point(field_mutable_list:protobuf_unittest.RepeatedWellKnownTypes.duration_field)
  return &duration_field_;
}
inline const ::google::protobuf::RepeatedPtrField< ::google::protobuf::Duration >&
RepeatedWellKnownTypes::duration_field() const {
  // @@protoc_insertion_point(field_list:protobuf_unittest.RepeatedWellKnownTypes.duration_field)
  return duration_field_;
}

// repeated .google.protobuf.Empty empty_field = 4;
inline int RepeatedWellKnownTypes::empty_field_size() const {
  return empty_field_.size();
}
inline void RepeatedWellKnownTypes::clear_empty_field() {
  empty_field_.Clear();
}
inline const ::google::protobuf::Empty& RepeatedWellKnownTypes::empty_field(int index) const {
  // @@protoc_insertion_point(field_get:protobuf_unittest.RepeatedWellKnownTypes.empty_field)
  return empty_field_.Get(index);
}
inline ::google::protobuf::Empty* RepeatedWellKnownTypes::mutable_empty_field(int index) {
  // @@protoc_insertion_point(field_mutable:protobuf_unittest.RepeatedWellKnownTypes.empty_field)
  return empty_field_.Mutable(index);
}
inline ::google::protobuf::Empty* RepeatedWellKnownTypes::add_empty_field() {
  // @@protoc_insertion_point(field_add:protobuf_unittest.RepeatedWellKnownTypes.empty_field)
  return empty_field_.Add();
}
inline ::google::protobuf::RepeatedPtrField< ::google::protobuf::Empty >*
RepeatedWellKnownTypes::mutable_empty_field() {
  // @@protoc_insertion_point(field_mutable_list:protobuf_unittest.RepeatedWellKnownTypes.empty_field)
  return &empty_field_;
}
inline const ::google::protobuf::RepeatedPtrField< ::google::protobuf::Empty >&
RepeatedWellKnownTypes::empty_field() const {
  // @@protoc_insertion_point(field_list:protobuf_unittest.RepeatedWellKnownTypes.empty_field)
  return empty_field_;
}

// repeated .google.protobuf.FieldMask field_mask_field = 5;
inline int RepeatedWellKnownTypes::field_mask_field_size() const {
  return field_mask_field_.size();
}
inline void RepeatedWellKnownTypes::clear_field_mask_field() {
  field_mask_field_.Clear();
}
inline const ::google::protobuf::FieldMask& RepeatedWellKnownTypes::field_mask_field(int index) const {
  // @@protoc_insertion_point(field_get:protobuf_unittest.RepeatedWellKnownTypes.field_mask_field)
  return field_mask_field_.Get(index);
}
inline ::google::protobuf::FieldMask* RepeatedWellKnownTypes::mutable_field_mask_field(int index) {
  // @@protoc_insertion_point(field_mutable:protobuf_unittest.RepeatedWellKnownTypes.field_mask_field)
  return field_mask_field_.Mutable(index);
}
inline ::google::protobuf::FieldMask* RepeatedWellKnownTypes::add_field_mask_field() {
  // @@protoc_insertion_point(field_add:protobuf_unittest.RepeatedWellKnownTypes.field_mask_field)
  return field_mask_field_.Add();
}
inline ::google::protobuf::RepeatedPtrField< ::google::protobuf::FieldMask >*
RepeatedWellKnownTypes::mutable_field_mask_field() {
  // @@protoc_insertion_point(field_mutable_list:protobuf_unittest.RepeatedWellKnownTypes.field_mask_field)
  return &field_mask_field_;
}
inline const ::google::protobuf::RepeatedPtrField< ::google::protobuf::FieldMask >&
RepeatedWellKnownTypes::field_mask_field() const {
  // @@protoc_insertion_point(field_list:protobuf_unittest.RepeatedWellKnownTypes.field_mask_field)
  return field_mask_field_;
}

// repeated .google.protobuf.SourceContext source_context_field = 6;
inline int RepeatedWellKnownTypes::source_context_field_size() const {
  return source_context_field_.size();
}
inline void RepeatedWellKnownTypes::clear_source_context_field() {
  source_context_field_.Clear();
}
inline const ::google::protobuf::SourceContext& RepeatedWellKnownTypes::source_context_field(int index) const {
  // @@protoc_insertion_point(field_get:protobuf_unittest.RepeatedWellKnownTypes.source_context_field)
  return source_context_field_.Get(index);
}
inline ::google::protobuf::SourceContext* RepeatedWellKnownTypes::mutable_source_context_field(int index) {
  // @@protoc_insertion_point(field_mutable:protobuf_unittest.RepeatedWellKnownTypes.source_context_field)
  return source_context_field_.Mutable(index);
}
inline ::google::protobuf::SourceContext* RepeatedWellKnownTypes::add_source_context_field() {
  // @@protoc_insertion_point(field_add:protobuf_unittest.RepeatedWellKnownTypes.source_context_field)
  return source_context_field_.Add();
}
inline ::google::protobuf::RepeatedPtrField< ::google::protobuf::SourceContext >*
RepeatedWellKnownTypes::mutable_source_context_field() {
  // @@protoc_insertion_point(field_mutable_list:protobuf_unittest.RepeatedWellKnownTypes.source_context_field)
  return &source_context_field_;
}
inline const ::google::protobuf::RepeatedPtrField< ::google::protobuf::SourceContext >&
RepeatedWellKnownTypes::source_context_field() const {
  // @@protoc_insertion_point(field_list:protobuf_unittest.RepeatedWellKnownTypes.source_context_field)
  return source_context_field_;
}

// repeated .google.protobuf.Struct struct_field = 7;
inline int RepeatedWellKnownTypes::struct_field_size() const {
  return struct_field_.size();
}
inline void RepeatedWellKnownTypes::clear_struct_field() {
  struct_field_.Clear();
}
inline const ::google::protobuf::Struct& RepeatedWellKnownTypes::struct_field(int index) const {
  // @@protoc_insertion_point(field_get:protobuf_unittest.RepeatedWellKnownTypes.struct_field)
  return struct_field_.Get(index);
}
inline ::google::protobuf::Struct* RepeatedWellKnownTypes::mutable_struct_field(int index) {
  // @@protoc_insertion_point(field_mutable:protobuf_unittest.RepeatedWellKnownTypes.struct_field)
  return struct_field_.Mutable(index);
}
inline ::google::protobuf::Struct* RepeatedWellKnownTypes::add_struct_field() {
  // @@protoc_insertion_point(field_add:protobuf_unittest.RepeatedWellKnownTypes.struct_field)
  return struct_field_.Add();
}
inline ::google::protobuf::RepeatedPtrField< ::google::protobuf::Struct >*
RepeatedWellKnownTypes::mutable_struct_field() {
  // @@protoc_insertion_point(field_mutable_list:protobuf_unittest.RepeatedWellKnownTypes.struct_field)
  return &struct_field_;
}
inline const ::google::protobuf::RepeatedPtrField< ::google::protobuf::Struct >&
RepeatedWellKnownTypes::struct_field() const {
  // @@protoc_insertion_point(field_list:protobuf_unittest.RepeatedWellKnownTypes.struct_field)
  return struct_field_;
}

// repeated .google.protobuf.Timestamp timestamp_field = 8;
inline int RepeatedWellKnownTypes::timestamp_field_size() const {
  return timestamp_field_.size();
}
inline void RepeatedWellKnownTypes::clear_timestamp_field() {
  timestamp_field_.Clear();
}
inline const ::google::protobuf::Timestamp& RepeatedWellKnownTypes::timestamp_field(int index) const {
  // @@protoc_insertion_point(field_get:protobuf_unittest.RepeatedWellKnownTypes.timestamp_field)
  return timestamp_field_.Get(index);
}
inline ::google::protobuf::Timestamp* RepeatedWellKnownTypes::mutable_timestamp_field(int index) {
  // @@protoc_insertion_point(field_mutable:protobuf_unittest.RepeatedWellKnownTypes.timestamp_field)
  return timestamp_field_.Mutable(index);
}
inline ::google::protobuf::Timestamp* RepeatedWellKnownTypes::add_timestamp_field() {
  // @@protoc_insertion_point(field_add:protobuf_unittest.RepeatedWellKnownTypes.timestamp_field)
  return timestamp_field_.Add();
}
inline ::google::protobuf::RepeatedPtrField< ::google::protobuf::Timestamp >*
RepeatedWellKnownTypes::mutable_timestamp_field() {
  // @@protoc_insertion_point(field_mutable_list:protobuf_unittest.RepeatedWellKnownTypes.timestamp_field)
  return &timestamp_field_;
}
inline const ::google::protobuf::RepeatedPtrField< ::google::protobuf::Timestamp >&
RepeatedWellKnownTypes::timestamp_field() const {
  // @@protoc_insertion_point(field_list:protobuf_unittest.RepeatedWellKnownTypes.timestamp_field)
  return timestamp_field_;
}

// repeated .google.protobuf.Type type_field = 9;
inline int RepeatedWellKnownTypes::type_field_size() const {
  return type_field_.size();
}
inline void RepeatedWellKnownTypes::clear_type_field() {
  type_field_.Clear();
}
inline const ::google::protobuf::Type& RepeatedWellKnownTypes::type_field(int index) const {
  // @@protoc_insertion_point(field_get:protobuf_unittest.RepeatedWellKnownTypes.type_field)
  return type_field_.Get(index);
}
inline ::google::protobuf::Type* RepeatedWellKnownTypes::mutable_type_field(int index) {
  // @@protoc_insertion_point(field_mutable:protobuf_unittest.RepeatedWellKnownTypes.type_field)
  return type_field_.Mutable(index);
}
inline ::google::protobuf::Type* RepeatedWellKnownTypes::add_type_field() {
  // @@protoc_insertion_point(field_add:protobuf_unittest.RepeatedWellKnownTypes.type_field)
  return type_field_.Add();
}
inline ::google::protobuf::RepeatedPtrField< ::google::protobuf::Type >*
RepeatedWellKnownTypes::mutable_type_field() {
  // @@protoc_insertion_point(field_mutable_list:protobuf_unittest.RepeatedWellKnownTypes.type_field)
  return &type_field_;
}
inline const ::google::protobuf::RepeatedPtrField< ::google::protobuf::Type >&
RepeatedWellKnownTypes::type_field() const {
  // @@protoc_insertion_point(field_list:protobuf_unittest.RepeatedWellKnownTypes.type_field)
  return type_field_;
}

// repeated .google.protobuf.DoubleValue double_field = 10;
inline int RepeatedWellKnownTypes::double_field_size() const {
  return double_field_.size();
}
inline void RepeatedWellKnownTypes::clear_double_field() {
  double_field_.Clear();
}
inline const ::google::protobuf::DoubleValue& RepeatedWellKnownTypes::double_field(int index) const {
  // @@protoc_insertion_point(field_get:protobuf_unittest.RepeatedWellKnownTypes.double_field)
  return double_field_.Get(index);
}
inline ::google::protobuf::DoubleValue* RepeatedWellKnownTypes::mutable_double_field(int index) {
  // @@protoc_insertion_point(field_mutable:protobuf_unittest.RepeatedWellKnownTypes.double_field)
  return double_field_.Mutable(index);
}
inline ::google::protobuf::DoubleValue* RepeatedWellKnownTypes::add_double_field() {
  // @@protoc_insertion_point(field_add:protobuf_unittest.RepeatedWellKnownTypes.double_field)
  return double_field_.Add();
}
inline ::google::protobuf::RepeatedPtrField< ::google::protobuf::DoubleValue >*
RepeatedWellKnownTypes::mutable_double_field() {
  // @@protoc_insertion_point(field_mutable_list:protobuf_unittest.RepeatedWellKnownTypes.double_field)
  return &double_field_;
}
inline const ::google::protobuf::RepeatedPtrField< ::google::protobuf::DoubleValue >&
RepeatedWellKnownTypes::double_field() const {
  // @@protoc_insertion_point(field_list:protobuf_unittest.RepeatedWellKnownTypes.double_field)
  return double_field_;
}

// repeated .google.protobuf.FloatValue float_field = 11;
inline int RepeatedWellKnownTypes::float_field_size() const {
  return float_field_.size();
}
inline void RepeatedWellKnownTypes::clear_float_field() {
  float_field_.Clear();
}
inline const ::google::protobuf::FloatValue& RepeatedWellKnownTypes::float_field(int index) const {
  // @@protoc_insertion_point(field_get:protobuf_unittest.RepeatedWellKnownTypes.float_field)
  return float_field_.Get(index);
}
inline ::google::protobuf::FloatValue* RepeatedWellKnownTypes::mutable_float_field(int index) {
  // @@protoc_insertion_point(field_mutable:protobuf_unittest.RepeatedWellKnownTypes.float_field)
  return float_field_.Mutable(index);
}
inline ::google::protobuf::FloatValue* RepeatedWellKnownTypes::add_float_field() {
  // @@protoc_insertion_point(field_add:protobuf_unittest.RepeatedWellKnownTypes.float_field)
  return float_field_.Add();
}
inline ::google::protobuf::RepeatedPtrField< ::google::protobuf::FloatValue >*
RepeatedWellKnownTypes::mutable_float_field() {
  // @@protoc_insertion_point(field_mutable_list:protobuf_unittest.RepeatedWellKnownTypes.float_field)
  return &float_field_;
}
inline const ::google::protobuf::RepeatedPtrField< ::google::protobuf::FloatValue >&
RepeatedWellKnownTypes::float_field() const {
  // @@protoc_insertion_point(field_list:protobuf_unittest.RepeatedWellKnownTypes.float_field)
  return float_field_;
}

// repeated .google.protobuf.Int64Value int64_field = 12;
inline int RepeatedWellKnownTypes::int64_field_size() const {
  return int64_field_.size();
}
inline void RepeatedWellKnownTypes::clear_int64_field() {
  int64_field_.Clear();
}
inline const ::google::protobuf::Int64Value& RepeatedWellKnownTypes::int64_field(int index) const {
  // @@protoc_insertion_point(field_get:protobuf_unittest.RepeatedWellKnownTypes.int64_field)
  return int64_field_.Get(index);
}
inline ::google::protobuf::Int64Value* RepeatedWellKnownTypes::mutable_int64_field(int index) {
  // @@protoc_insertion_point(field_mutable:protobuf_unittest.RepeatedWellKnownTypes.int64_field)
  return int64_field_.Mutable(index);
}
inline ::google::protobuf::Int64Value* RepeatedWellKnownTypes::add_int64_field() {
  // @@protoc_insertion_point(field_add:protobuf_unittest.RepeatedWellKnownTypes.int64_field)
  return int64_field_.Add();
}
inline ::google::protobuf::RepeatedPtrField< ::google::protobuf::Int64Value >*
RepeatedWellKnownTypes::mutable_int64_field() {
  // @@protoc_insertion_point(field_mutable_list:protobuf_unittest.RepeatedWellKnownTypes.int64_field)
  return &int64_field_;
}
inline const ::google::protobuf::RepeatedPtrField< ::google::protobuf::Int64Value >&
RepeatedWellKnownTypes::int64_field() const {
  // @@protoc_insertion_point(field_list:protobuf_unittest.RepeatedWellKnownTypes.int64_field)
  return int64_field_;
}

// repeated .google.protobuf.UInt64Value uint64_field = 13;
inline int RepeatedWellKnownTypes::uint64_field_size() const {
  return uint64_field_.size();
}
inline void RepeatedWellKnownTypes::clear_uint64_field() {
  uint64_field_.Clear();
}
inline const ::google::protobuf::UInt64Value& RepeatedWellKnownTypes::uint64_field(int index) const {
  // @@protoc_insertion_point(field_get:protobuf_unittest.RepeatedWellKnownTypes.uint64_field)
  return uint64_field_.Get(index);
}
inline ::google::protobuf::UInt64Value* RepeatedWellKnownTypes::mutable_uint64_field(int index) {
  // @@protoc_insertion_point(field_mutable:protobuf_unittest.RepeatedWellKnownTypes.uint64_field)
  return uint64_field_.Mutable(index);
}
inline ::google::protobuf::UInt64Value* RepeatedWellKnownTypes::add_uint64_field() {
  // @@protoc_insertion_point(field_add:protobuf_unittest.RepeatedWellKnownTypes.uint64_field)
  return uint64_field_.Add();
}
inline ::google::protobuf::RepeatedPtrField< ::google::protobuf::UInt64Value >*
RepeatedWellKnownTypes::mutable_uint64_field() {
  // @@protoc_insertion_point(field_mutable_list:protobuf_unittest.RepeatedWellKnownTypes.uint64_field)
  return &uint64_field_;
}
inline const ::google::protobuf::RepeatedPtrField< ::google::protobuf::UInt64Value >&
RepeatedWellKnownTypes::uint64_field() const {
  // @@protoc_insertion_point(field_list:protobuf_unittest.RepeatedWellKnownTypes.uint64_field)
  return uint64_field_;
}

// repeated .google.protobuf.Int32Value int32_field = 14;
inline int RepeatedWellKnownTypes::int32_field_size() const {
  return int32_field_.size();
}
inline void RepeatedWellKnownTypes::clear_int32_field() {
  int32_field_.Clear();
}
inline const ::google::protobuf::Int32Value& RepeatedWellKnownTypes::int32_field(int index) const {
  // @@protoc_insertion_point(field_get:protobuf_unittest.RepeatedWellKnownTypes.int32_field)
  return int32_field_.Get(index);
}
inline ::google::protobuf::Int32Value* RepeatedWellKnownTypes::mutable_int32_field(int index) {
  // @@protoc_insertion_point(field_mutable:protobuf_unittest.RepeatedWellKnownTypes.int32_field)
  return int32_field_.Mutable(index);
}
inline ::google::protobuf::Int32Value* RepeatedWellKnownTypes::add_int32_field() {
  // @@protoc_insertion_point(field_add:protobuf_unittest.RepeatedWellKnownTypes.int32_field)
  return int32_field_.Add();
}
inline ::google::protobuf::RepeatedPtrField< ::google::protobuf::Int32Value >*
RepeatedWellKnownTypes::mutable_int32_field() {
  // @@protoc_insertion_point(field_mutable_list:protobuf_unittest.RepeatedWellKnownTypes.int32_field)
  return &int32_field_;
}
inline const ::google::protobuf::RepeatedPtrField< ::google::protobuf::Int32Value >&
RepeatedWellKnownTypes::int32_field() const {
  // @@protoc_insertion_point(field_list:protobuf_unittest.RepeatedWellKnownTypes.int32_field)
  return int32_field_;
}

// repeated .google.protobuf.UInt32Value uint32_field = 15;
inline int RepeatedWellKnownTypes::uint32_field_size() const {
  return uint32_field_.size();
}
inline void RepeatedWellKnownTypes::clear_uint32_field() {
  uint32_field_.Clear();
}
inline const ::google::protobuf::UInt32Value& RepeatedWellKnownTypes::uint32_field(int index) const {
  // @@protoc_insertion_point(field_get:protobuf_unittest.RepeatedWellKnownTypes.uint32_field)
  return uint32_field_.Get(index);
}
inline ::google::protobuf::UInt32Value* RepeatedWellKnownTypes::mutable_uint32_field(int index) {
  // @@protoc_insertion_point(field_mutable:protobuf_unittest.RepeatedWellKnownTypes.uint32_field)
  return uint32_field_.Mutable(index);
}
inline ::google::protobuf::UInt32Value* RepeatedWellKnownTypes::add_uint32_field() {
  // @@protoc_insertion_point(field_add:protobuf_unittest.RepeatedWellKnownTypes.uint32_field)
  return uint32_field_.Add();
}
inline ::google::protobuf::RepeatedPtrField< ::google::protobuf::UInt32Value >*
RepeatedWellKnownTypes::mutable_uint32_field() {
  // @@protoc_insertion_point(field_mutable_list:protobuf_unittest.RepeatedWellKnownTypes.uint32_field)
  return &uint32_field_;
}
inline const ::google::protobuf::RepeatedPtrField< ::google::protobuf::UInt32Value >&
RepeatedWellKnownTypes::uint32_field() const {
  // @@protoc_insertion_point(field_list:protobuf_unittest.RepeatedWellKnownTypes.uint32_field)
  return uint32_field_;
}

// repeated .google.protobuf.BoolValue bool_field = 16;
inline int RepeatedWellKnownTypes::bool_field_size() const {
  return bool_field_.size();
}
inline void RepeatedWellKnownTypes::clear_bool_field() {
  bool_field_.Clear();
}
inline const ::google::protobuf::BoolValue& RepeatedWellKnownTypes::bool_field(int index) const {
  // @@protoc_insertion_point(field_get:protobuf_unittest.RepeatedWellKnownTypes.bool_field)
  return bool_field_.Get(index);
}
inline ::google::protobuf::BoolValue* RepeatedWellKnownTypes::mutable_bool_field(int index) {
  // @@protoc_insertion_point(field_mutable:protobuf_unittest.RepeatedWellKnownTypes.bool_field)
  return bool_field_.Mutable(index);
}
inline ::google::protobuf::BoolValue* RepeatedWellKnownTypes::add_bool_field() {
  // @@protoc_insertion_point(field_add:protobuf_unittest.RepeatedWellKnownTypes.bool_field)
  return bool_field_.Add();
}
inline ::google::protobuf::RepeatedPtrField< ::google::protobuf::BoolValue >*
RepeatedWellKnownTypes::mutable_bool_field() {
  // @@protoc_insertion_point(field_mutable_list:protobuf_unittest.RepeatedWellKnownTypes.bool_field)
  return &bool_field_;
}
inline const ::google::protobuf::RepeatedPtrField< ::google::protobuf::BoolValue >&
RepeatedWellKnownTypes::bool_field() const {
  // @@protoc_insertion_point(field_list:protobuf_unittest.RepeatedWellKnownTypes.bool_field)
  return bool_field_;
}

// repeated .google.protobuf.StringValue string_field = 17;
inline int RepeatedWellKnownTypes::string_field_size() const {
  return string_field_.size();
}
inline void RepeatedWellKnownTypes::clear_string_field() {
  string_field_.Clear();
}
inline const ::google::protobuf::StringValue& RepeatedWellKnownTypes::string_field(int index) const {
  // @@protoc_insertion_point(field_get:protobuf_unittest.RepeatedWellKnownTypes.string_field)
  return string_field_.Get(index);
}
inline ::google::protobuf::StringValue* RepeatedWellKnownTypes::mutable_string_field(int index) {
  // @@protoc_insertion_point(field_mutable:protobuf_unittest.RepeatedWellKnownTypes.string_field)
  return string_field_.Mutable(index);
}
inline ::google::protobuf::StringValue* RepeatedWellKnownTypes::add_string_field() {
  // @@protoc_insertion_point(field_add:protobuf_unittest.RepeatedWellKnownTypes.string_field)
  return string_field_.Add();
}
inline ::google::protobuf::RepeatedPtrField< ::google::protobuf::StringValue >*
RepeatedWellKnownTypes::mutable_string_field() {
  // @@protoc_insertion_point(field_mutable_list:protobuf_unittest.RepeatedWellKnownTypes.string_field)
  return &string_field_;
}
inline const ::google::protobuf::RepeatedPtrField< ::google::protobuf::StringValue >&
RepeatedWellKnownTypes::string_field() const {
  // @@protoc_insertion_point(field_list:protobuf_unittest.RepeatedWellKnownTypes.string_field)
  return string_field_;
}

// repeated .google.protobuf.BytesValue bytes_field = 18;
inline int RepeatedWellKnownTypes::bytes_field_size() const {
  return bytes_field_.size();
}
inline void RepeatedWellKnownTypes::clear_bytes_field() {
  bytes_field_.Clear();
}
inline const ::google::protobuf::BytesValue& RepeatedWellKnownTypes::bytes_field(int index) const {
  // @@protoc_insertion_point(field_get:protobuf_unittest.RepeatedWellKnownTypes.bytes_field)
  return bytes_field_.Get(index);
}
inline ::google::protobuf::BytesValue* RepeatedWellKnownTypes::mutable_bytes_field(int index) {
  // @@protoc_insertion_point(field_mutable:protobuf_unittest.RepeatedWellKnownTypes.bytes_field)
  return bytes_field_.Mutable(index);
}
inline ::google::protobuf::BytesValue* RepeatedWellKnownTypes::add_bytes_field() {
  // @@protoc_insertion_point(field_add:protobuf_unittest.RepeatedWellKnownTypes.bytes_field)
  return bytes_field_.Add();
}
inline ::google::protobuf::RepeatedPtrField< ::google::protobuf::BytesValue >*
RepeatedWellKnownTypes::mutable_bytes_field() {
  // @@protoc_insertion_point(field_mutable_list:protobuf_unittest.RepeatedWellKnownTypes.bytes_field)
  return &bytes_field_;
}
inline const ::google::protobuf::RepeatedPtrField< ::google::protobuf::BytesValue >&
RepeatedWellKnownTypes::bytes_field() const {
  // @@protoc_insertion_point(field_list:protobuf_unittest.RepeatedWellKnownTypes.bytes_field)
  return bytes_field_;
}

inline const RepeatedWellKnownTypes* RepeatedWellKnownTypes::internal_default_instance() {
  return &RepeatedWellKnownTypes_default_instance_.get();
}
// -------------------------------------------------------------------

// OneofWellKnownTypes

// optional .google.protobuf.Any any_field = 1;
inline bool OneofWellKnownTypes::has_any_field() const {
  return oneof_field_case() == kAnyField;
}
inline void OneofWellKnownTypes::set_has_any_field() {
  _oneof_case_[0] = kAnyField;
}
inline void OneofWellKnownTypes::clear_any_field() {
  if (has_any_field()) {
    delete oneof_field_.any_field_;
    clear_has_oneof_field();
  }
}
inline  const ::google::protobuf::Any& OneofWellKnownTypes::any_field() const {
  // @@protoc_insertion_point(field_get:protobuf_unittest.OneofWellKnownTypes.any_field)
  return has_any_field()
      ? *oneof_field_.any_field_
      : ::google::protobuf::Any::default_instance();
}
inline ::google::protobuf::Any* OneofWellKnownTypes::mutable_any_field() {
  if (!has_any_field()) {
    clear_oneof_field();
    set_has_any_field();
    oneof_field_.any_field_ = new ::google::protobuf::Any;
  }
  // @@protoc_insertion_point(field_mutable:protobuf_unittest.OneofWellKnownTypes.any_field)
  return oneof_field_.any_field_;
}
inline ::google::protobuf::Any* OneofWellKnownTypes::release_any_field() {
  // @@protoc_insertion_point(field_release:protobuf_unittest.OneofWellKnownTypes.any_field)
  if (has_any_field()) {
    clear_has_oneof_field();
    ::google::protobuf::Any* temp = oneof_field_.any_field_;
    oneof_field_.any_field_ = NULL;
    return temp;
  } else {
    return NULL;
  }
}
inline void OneofWellKnownTypes::set_allocated_any_field(::google::protobuf::Any* any_field) {
  clear_oneof_field();
  if (any_field) {
    set_has_any_field();
    oneof_field_.any_field_ = any_field;
  }
  // @@protoc_insertion_point(field_set_allocated:protobuf_unittest.OneofWellKnownTypes.any_field)
}

// optional .google.protobuf.Api api_field = 2;
inline bool OneofWellKnownTypes::has_api_field() const {
  return oneof_field_case() == kApiField;
}
inline void OneofWellKnownTypes::set_has_api_field() {
  _oneof_case_[0] = kApiField;
}
inline void OneofWellKnownTypes::clear_api_field() {
  if (has_api_field()) {
    delete oneof_field_.api_field_;
    clear_has_oneof_field();
  }
}
inline  const ::google::protobuf::Api& OneofWellKnownTypes::api_field() const {
  // @@protoc_insertion_point(field_get:protobuf_unittest.OneofWellKnownTypes.api_field)
  return has_api_field()
      ? *oneof_field_.api_field_
      : ::google::protobuf::Api::default_instance();
}
inline ::google::protobuf::Api* OneofWellKnownTypes::mutable_api_field() {
  if (!has_api_field()) {
    clear_oneof_field();
    set_has_api_field();
    oneof_field_.api_field_ = new ::google::protobuf::Api;
  }
  // @@protoc_insertion_point(field_mutable:protobuf_unittest.OneofWellKnownTypes.api_field)
  return oneof_field_.api_field_;
}
inline ::google::protobuf::Api* OneofWellKnownTypes::release_api_field() {
  // @@protoc_insertion_point(field_release:protobuf_unittest.OneofWellKnownTypes.api_field)
  if (has_api_field()) {
    clear_has_oneof_field();
    ::google::protobuf::Api* temp = oneof_field_.api_field_;
    oneof_field_.api_field_ = NULL;
    return temp;
  } else {
    return NULL;
  }
}
inline void OneofWellKnownTypes::set_allocated_api_field(::google::protobuf::Api* api_field) {
  clear_oneof_field();
  if (api_field) {
    set_has_api_field();
    oneof_field_.api_field_ = api_field;
  }
  // @@protoc_insertion_point(field_set_allocated:protobuf_unittest.OneofWellKnownTypes.api_field)
}

// optional .google.protobuf.Duration duration_field = 3;
inline bool OneofWellKnownTypes::has_duration_field() const {
  return oneof_field_case() == kDurationField;
}
inline void OneofWellKnownTypes::set_has_duration_field() {
  _oneof_case_[0] = kDurationField;
}
inline void OneofWellKnownTypes::clear_duration_field() {
  if (has_duration_field()) {
    delete oneof_field_.duration_field_;
    clear_has_oneof_field();
  }
}
inline  const ::google::protobuf::Duration& OneofWellKnownTypes::duration_field() const {
  // @@protoc_insertion_point(field_get:protobuf_unittest.OneofWellKnownTypes.duration_field)
  return has_duration_field()
      ? *oneof_field_.duration_field_
      : ::google::protobuf::Duration::default_instance();
}
inline ::google::protobuf::Duration* OneofWellKnownTypes::mutable_duration_field() {
  if (!has_duration_field()) {
    clear_oneof_field();
    set_has_duration_field();
    oneof_field_.duration_field_ = new ::google::protobuf::Duration;
  }
  // @@protoc_insertion_point(field_mutable:protobuf_unittest.OneofWellKnownTypes.duration_field)
  return oneof_field_.duration_field_;
}
inline ::google::protobuf::Duration* OneofWellKnownTypes::release_duration_field() {
  // @@protoc_insertion_point(field_release:protobuf_unittest.OneofWellKnownTypes.duration_field)
  if (has_duration_field()) {
    clear_has_oneof_field();
    ::google::protobuf::Duration* temp = oneof_field_.duration_field_;
    oneof_field_.duration_field_ = NULL;
    return temp;
  } else {
    return NULL;
  }
}
inline void OneofWellKnownTypes::set_allocated_duration_field(::google::protobuf::Duration* duration_field) {
  clear_oneof_field();
  if (duration_field) {
    if (static_cast< ::google::protobuf::Duration*>(duration_field)->GetArena() != NULL) {
      ::google::protobuf::Duration* new_duration_field = new ::google::protobuf::Duration;
      new_duration_field->CopyFrom(*duration_field);
      duration_field = new_duration_field;
    }
    set_has_duration_field();
    oneof_field_.duration_field_ = duration_field;
  }
  // @@protoc_insertion_point(field_set_allocated:protobuf_unittest.OneofWellKnownTypes.duration_field)
}

// optional .google.protobuf.Empty empty_field = 4;
inline bool OneofWellKnownTypes::has_empty_field() const {
  return oneof_field_case() == kEmptyField;
}
inline void OneofWellKnownTypes::set_has_empty_field() {
  _oneof_case_[0] = kEmptyField;
}
inline void OneofWellKnownTypes::clear_empty_field() {
  if (has_empty_field()) {
    delete oneof_field_.empty_field_;
    clear_has_oneof_field();
  }
}
inline  const ::google::protobuf::Empty& OneofWellKnownTypes::empty_field() const {
  // @@protoc_insertion_point(field_get:protobuf_unittest.OneofWellKnownTypes.empty_field)
  return has_empty_field()
      ? *oneof_field_.empty_field_
      : ::google::protobuf::Empty::default_instance();
}
inline ::google::protobuf::Empty* OneofWellKnownTypes::mutable_empty_field() {
  if (!has_empty_field()) {
    clear_oneof_field();
    set_has_empty_field();
    oneof_field_.empty_field_ = new ::google::protobuf::Empty;
  }
  // @@protoc_insertion_point(field_mutable:protobuf_unittest.OneofWellKnownTypes.empty_field)
  return oneof_field_.empty_field_;
}
inline ::google::protobuf::Empty* OneofWellKnownTypes::release_empty_field() {
  // @@protoc_insertion_point(field_release:protobuf_unittest.OneofWellKnownTypes.empty_field)
  if (has_empty_field()) {
    clear_has_oneof_field();
    ::google::protobuf::Empty* temp = oneof_field_.empty_field_;
    oneof_field_.empty_field_ = NULL;
    return temp;
  } else {
    return NULL;
  }
}
inline void OneofWellKnownTypes::set_allocated_empty_field(::google::protobuf::Empty* empty_field) {
  clear_oneof_field();
  if (empty_field) {
    if (static_cast< ::google::protobuf::Empty*>(empty_field)->GetArena() != NULL) {
      ::google::protobuf::Empty* new_empty_field = new ::google::protobuf::Empty;
      new_empty_field->CopyFrom(*empty_field);
      empty_field = new_empty_field;
    }
    set_has_empty_field();
    oneof_field_.empty_field_ = empty_field;
  }
  // @@protoc_insertion_point(field_set_allocated:protobuf_unittest.OneofWellKnownTypes.empty_field)
}

// optional .google.protobuf.FieldMask field_mask_field = 5;
inline bool OneofWellKnownTypes::has_field_mask_field() const {
  return oneof_field_case() == kFieldMaskField;
}
inline void OneofWellKnownTypes::set_has_field_mask_field() {
  _oneof_case_[0] = kFieldMaskField;
}
inline void OneofWellKnownTypes::clear_field_mask_field() {
  if (has_field_mask_field()) {
    delete oneof_field_.field_mask_field_;
    clear_has_oneof_field();
  }
}
inline  const ::google::protobuf::FieldMask& OneofWellKnownTypes::field_mask_field() const {
  // @@protoc_insertion_point(field_get:protobuf_unittest.OneofWellKnownTypes.field_mask_field)
  return has_field_mask_field()
      ? *oneof_field_.field_mask_field_
      : ::google::protobuf::FieldMask::default_instance();
}
inline ::google::protobuf::FieldMask* OneofWellKnownTypes::mutable_field_mask_field() {
  if (!has_field_mask_field()) {
    clear_oneof_field();
    set_has_field_mask_field();
    oneof_field_.field_mask_field_ = new ::google::protobuf::FieldMask;
  }
  // @@protoc_insertion_point(field_mutable:protobuf_unittest.OneofWellKnownTypes.field_mask_field)
  return oneof_field_.field_mask_field_;
}
inline ::google::protobuf::FieldMask* OneofWellKnownTypes::release_field_mask_field() {
  // @@protoc_insertion_point(field_release:protobuf_unittest.OneofWellKnownTypes.field_mask_field)
  if (has_field_mask_field()) {
    clear_has_oneof_field();
    ::google::protobuf::FieldMask* temp = oneof_field_.field_mask_field_;
    oneof_field_.field_mask_field_ = NULL;
    return temp;
  } else {
    return NULL;
  }
}
inline void OneofWellKnownTypes::set_allocated_field_mask_field(::google::protobuf::FieldMask* field_mask_field) {
  clear_oneof_field();
  if (field_mask_field) {
    set_has_field_mask_field();
    oneof_field_.field_mask_field_ = field_mask_field;
  }
  // @@protoc_insertion_point(field_set_allocated:protobuf_unittest.OneofWellKnownTypes.field_mask_field)
}

// optional .google.protobuf.SourceContext source_context_field = 6;
inline bool OneofWellKnownTypes::has_source_context_field() const {
  return oneof_field_case() == kSourceContextField;
}
inline void OneofWellKnownTypes::set_has_source_context_field() {
  _oneof_case_[0] = kSourceContextField;
}
inline void OneofWellKnownTypes::clear_source_context_field() {
  if (has_source_context_field()) {
    delete oneof_field_.source_context_field_;
    clear_has_oneof_field();
  }
}
inline  const ::google::protobuf::SourceContext& OneofWellKnownTypes::source_context_field() const {
  // @@protoc_insertion_point(field_get:protobuf_unittest.OneofWellKnownTypes.source_context_field)
  return has_source_context_field()
      ? *oneof_field_.source_context_field_
      : ::google::protobuf::SourceContext::default_instance();
}
inline ::google::protobuf::SourceContext* OneofWellKnownTypes::mutable_source_context_field() {
  if (!has_source_context_field()) {
    clear_oneof_field();
    set_has_source_context_field();
    oneof_field_.source_context_field_ = new ::google::protobuf::SourceContext;
  }
  // @@protoc_insertion_point(field_mutable:protobuf_unittest.OneofWellKnownTypes.source_context_field)
  return oneof_field_.source_context_field_;
}
inline ::google::protobuf::SourceContext* OneofWellKnownTypes::release_source_context_field() {
  // @@protoc_insertion_point(field_release:protobuf_unittest.OneofWellKnownTypes.source_context_field)
  if (has_source_context_field()) {
    clear_has_oneof_field();
    ::google::protobuf::SourceContext* temp = oneof_field_.source_context_field_;
    oneof_field_.source_context_field_ = NULL;
    return temp;
  } else {
    return NULL;
  }
}
inline void OneofWellKnownTypes::set_allocated_source_context_field(::google::protobuf::SourceContext* source_context_field) {
  clear_oneof_field();
  if (source_context_field) {
    set_has_source_context_field();
    oneof_field_.source_context_field_ = source_context_field;
  }
  // @@protoc_insertion_point(field_set_allocated:protobuf_unittest.OneofWellKnownTypes.source_context_field)
}

// optional .google.protobuf.Struct struct_field = 7;
inline bool OneofWellKnownTypes::has_struct_field() const {
  return oneof_field_case() == kStructField;
}
inline void OneofWellKnownTypes::set_has_struct_field() {
  _oneof_case_[0] = kStructField;
}
inline void OneofWellKnownTypes::clear_struct_field() {
  if (has_struct_field()) {
    delete oneof_field_.struct_field_;
    clear_has_oneof_field();
  }
}
inline  const ::google::protobuf::Struct& OneofWellKnownTypes::struct_field() const {
  // @@protoc_insertion_point(field_get:protobuf_unittest.OneofWellKnownTypes.struct_field)
  return has_struct_field()
      ? *oneof_field_.struct_field_
      : ::google::protobuf::Struct::default_instance();
}
inline ::google::protobuf::Struct* OneofWellKnownTypes::mutable_struct_field() {
  if (!has_struct_field()) {
    clear_oneof_field();
    set_has_struct_field();
    oneof_field_.struct_field_ = new ::google::protobuf::Struct;
  }
  // @@protoc_insertion_point(field_mutable:protobuf_unittest.OneofWellKnownTypes.struct_field)
  return oneof_field_.struct_field_;
}
inline ::google::protobuf::Struct* OneofWellKnownTypes::release_struct_field() {
  // @@protoc_insertion_point(field_release:protobuf_unittest.OneofWellKnownTypes.struct_field)
  if (has_struct_field()) {
    clear_has_oneof_field();
    ::google::protobuf::Struct* temp = oneof_field_.struct_field_;
    oneof_field_.struct_field_ = NULL;
    return temp;
  } else {
    return NULL;
  }
}
inline void OneofWellKnownTypes::set_allocated_struct_field(::google::protobuf::Struct* struct_field) {
  clear_oneof_field();
  if (struct_field) {
    if (static_cast< ::google::protobuf::Struct*>(struct_field)->GetArena() != NULL) {
      ::google::protobuf::Struct* new_struct_field = new ::google::protobuf::Struct;
      new_struct_field->CopyFrom(*struct_field);
      struct_field = new_struct_field;
    }
    set_has_struct_field();
    oneof_field_.struct_field_ = struct_field;
  }
  // @@protoc_insertion_point(field_set_allocated:protobuf_unittest.OneofWellKnownTypes.struct_field)
}

// optional .google.protobuf.Timestamp timestamp_field = 8;
inline bool OneofWellKnownTypes::has_timestamp_field() const {
  return oneof_field_case() == kTimestampField;
}
inline void OneofWellKnownTypes::set_has_timestamp_field() {
  _oneof_case_[0] = kTimestampField;
}
inline void OneofWellKnownTypes::clear_timestamp_field() {
  if (has_timestamp_field()) {
    delete oneof_field_.timestamp_field_;
    clear_has_oneof_field();
  }
}
inline  const ::google::protobuf::Timestamp& OneofWellKnownTypes::timestamp_field() const {
  // @@protoc_insertion_point(field_get:protobuf_unittest.OneofWellKnownTypes.timestamp_field)
  return has_timestamp_field()
      ? *oneof_field_.timestamp_field_
      : ::google::protobuf::Timestamp::default_instance();
}
inline ::google::protobuf::Timestamp* OneofWellKnownTypes::mutable_timestamp_field() {
  if (!has_timestamp_field()) {
    clear_oneof_field();
    set_has_timestamp_field();
    oneof_field_.timestamp_field_ = new ::google::protobuf::Timestamp;
  }
  // @@protoc_insertion_point(field_mutable:protobuf_unittest.OneofWellKnownTypes.timestamp_field)
  return oneof_field_.timestamp_field_;
}
inline ::google::protobuf::Timestamp* OneofWellKnownTypes::release_timestamp_field() {
  // @@protoc_insertion_point(field_release:protobuf_unittest.OneofWellKnownTypes.timestamp_field)
  if (has_timestamp_field()) {
    clear_has_oneof_field();
    ::google::protobuf::Timestamp* temp = oneof_field_.timestamp_field_;
    oneof_field_.timestamp_field_ = NULL;
    return temp;
  } else {
    return NULL;
  }
}
inline void OneofWellKnownTypes::set_allocated_timestamp_field(::google::protobuf::Timestamp* timestamp_field) {
  clear_oneof_field();
  if (timestamp_field) {
    if (static_cast< ::google::protobuf::Timestamp*>(timestamp_field)->GetArena() != NULL) {
      ::google::protobuf::Timestamp* new_timestamp_field = new ::google::protobuf::Timestamp;
      new_timestamp_field->CopyFrom(*timestamp_field);
      timestamp_field = new_timestamp_field;
    }
    set_has_timestamp_field();
    oneof_field_.timestamp_field_ = timestamp_field;
  }
  // @@protoc_insertion_point(field_set_allocated:protobuf_unittest.OneofWellKnownTypes.timestamp_field)
}

// optional .google.protobuf.Type type_field = 9;
inline bool OneofWellKnownTypes::has_type_field() const {
  return oneof_field_case() == kTypeField;
}
inline void OneofWellKnownTypes::set_has_type_field() {
  _oneof_case_[0] = kTypeField;
}
inline void OneofWellKnownTypes::clear_type_field() {
  if (has_type_field()) {
    delete oneof_field_.type_field_;
    clear_has_oneof_field();
  }
}
inline  const ::google::protobuf::Type& OneofWellKnownTypes::type_field() const {
  // @@protoc_insertion_point(field_get:protobuf_unittest.OneofWellKnownTypes.type_field)
  return has_type_field()
      ? *oneof_field_.type_field_
      : ::google::protobuf::Type::default_instance();
}
inline ::google::protobuf::Type* OneofWellKnownTypes::mutable_type_field() {
  if (!has_type_field()) {
    clear_oneof_field();
    set_has_type_field();
    oneof_field_.type_field_ = new ::google::protobuf::Type;
  }
  // @@protoc_insertion_point(field_mutable:protobuf_unittest.OneofWellKnownTypes.type_field)
  return oneof_field_.type_field_;
}
inline ::google::protobuf::Type* OneofWellKnownTypes::release_type_field() {
  // @@protoc_insertion_point(field_release:protobuf_unittest.OneofWellKnownTypes.type_field)
  if (has_type_field()) {
    clear_has_oneof_field();
    ::google::protobuf::Type* temp = oneof_field_.type_field_;
    oneof_field_.type_field_ = NULL;
    return temp;
  } else {
    return NULL;
  }
}
inline void OneofWellKnownTypes::set_allocated_type_field(::google::protobuf::Type* type_field) {
  clear_oneof_field();
  if (type_field) {
    if (static_cast< ::google::protobuf::Type*>(type_field)->GetArena() != NULL) {
      ::google::protobuf::Type* new_type_field = new ::google::protobuf::Type;
      new_type_field->CopyFrom(*type_field);
      type_field = new_type_field;
    }
    set_has_type_field();
    oneof_field_.type_field_ = type_field;
  }
  // @@protoc_insertion_point(field_set_allocated:protobuf_unittest.OneofWellKnownTypes.type_field)
}

// optional .google.protobuf.DoubleValue double_field = 10;
inline bool OneofWellKnownTypes::has_double_field() const {
  return oneof_field_case() == kDoubleField;
}
inline void OneofWellKnownTypes::set_has_double_field() {
  _oneof_case_[0] = kDoubleField;
}
inline void OneofWellKnownTypes::clear_double_field() {
  if (has_double_field()) {
    delete oneof_field_.double_field_;
    clear_has_oneof_field();
  }
}
inline  const ::google::protobuf::DoubleValue& OneofWellKnownTypes::double_field() const {
  // @@protoc_insertion_point(field_get:protobuf_unittest.OneofWellKnownTypes.double_field)
  return has_double_field()
      ? *oneof_field_.double_field_
      : ::google::protobuf::DoubleValue::default_instance();
}
inline ::google::protobuf::DoubleValue* OneofWellKnownTypes::mutable_double_field() {
  if (!has_double_field()) {
    clear_oneof_field();
    set_has_double_field();
    oneof_field_.double_field_ = new ::google::protobuf::DoubleValue;
  }
  // @@protoc_insertion_point(field_mutable:protobuf_unittest.OneofWellKnownTypes.double_field)
  return oneof_field_.double_field_;
}
inline ::google::protobuf::DoubleValue* OneofWellKnownTypes::release_double_field() {
  // @@protoc_insertion_point(field_release:protobuf_unittest.OneofWellKnownTypes.double_field)
  if (has_double_field()) {
    clear_has_oneof_field();
    ::google::protobuf::DoubleValue* temp = oneof_field_.double_field_;
    oneof_field_.double_field_ = NULL;
    return temp;
  } else {
    return NULL;
  }
}
inline void OneofWellKnownTypes::set_allocated_double_field(::google::protobuf::DoubleValue* double_field) {
  clear_oneof_field();
  if (double_field) {
    if (static_cast< ::google::protobuf::DoubleValue*>(double_field)->GetArena() != NULL) {
      ::google::protobuf::DoubleValue* new_double_field = new ::google::protobuf::DoubleValue;
      new_double_field->CopyFrom(*double_field);
      double_field = new_double_field;
    }
    set_has_double_field();
    oneof_field_.double_field_ = double_field;
  }
  // @@protoc_insertion_point(field_set_allocated:protobuf_unittest.OneofWellKnownTypes.double_field)
}

// optional .google.protobuf.FloatValue float_field = 11;
inline bool OneofWellKnownTypes::has_float_field() const {
  return oneof_field_case() == kFloatField;
}
inline void OneofWellKnownTypes::set_has_float_field() {
  _oneof_case_[0] = kFloatField;
}
inline void OneofWellKnownTypes::clear_float_field() {
  if (has_float_field()) {
    delete oneof_field_.float_field_;
    clear_has_oneof_field();
  }
}
inline  const ::google::protobuf::FloatValue& OneofWellKnownTypes::float_field() const {
  // @@protoc_insertion_point(field_get:protobuf_unittest.OneofWellKnownTypes.float_field)
  return has_float_field()
      ? *oneof_field_.float_field_
      : ::google::protobuf::FloatValue::default_instance();
}
inline ::google::protobuf::FloatValue* OneofWellKnownTypes::mutable_float_field() {
  if (!has_float_field()) {
    clear_oneof_field();
    set_has_float_field();
    oneof_field_.float_field_ = new ::google::protobuf::FloatValue;
  }
  // @@protoc_insertion_point(field_mutable:protobuf_unittest.OneofWellKnownTypes.float_field)
  return oneof_field_.float_field_;
}
inline ::google::protobuf::FloatValue* OneofWellKnownTypes::release_float_field() {
  // @@protoc_insertion_point(field_release:protobuf_unittest.OneofWellKnownTypes.float_field)
  if (has_float_field()) {
    clear_has_oneof_field();
    ::google::protobuf::FloatValue* temp = oneof_field_.float_field_;
    oneof_field_.float_field_ = NULL;
    return temp;
  } else {
    return NULL;
  }
}
inline void OneofWellKnownTypes::set_allocated_float_field(::google::protobuf::FloatValue* float_field) {
  clear_oneof_field();
  if (float_field) {
    if (static_cast< ::google::protobuf::FloatValue*>(float_field)->GetArena() != NULL) {
      ::google::protobuf::FloatValue* new_float_field = new ::google::protobuf::FloatValue;
      new_float_field->CopyFrom(*float_field);
      float_field = new_float_field;
    }
    set_has_float_field();
    oneof_field_.float_field_ = float_field;
  }
  // @@protoc_insertion_point(field_set_allocated:protobuf_unittest.OneofWellKnownTypes.float_field)
}

// optional .google.protobuf.Int64Value int64_field = 12;
inline bool OneofWellKnownTypes::has_int64_field() const {
  return oneof_field_case() == kInt64Field;
}
inline void OneofWellKnownTypes::set_has_int64_field() {
  _oneof_case_[0] = kInt64Field;
}
inline void OneofWellKnownTypes::clear_int64_field() {
  if (has_int64_field()) {
    delete oneof_field_.int64_field_;
    clear_has_oneof_field();
  }
}
inline  const ::google::protobuf::Int64Value& OneofWellKnownTypes::int64_field() const {
  // @@protoc_insertion_point(field_get:protobuf_unittest.OneofWellKnownTypes.int64_field)
  return has_int64_field()
      ? *oneof_field_.int64_field_
      : ::google::protobuf::Int64Value::default_instance();
}
inline ::google::protobuf::Int64Value* OneofWellKnownTypes::mutable_int64_field() {
  if (!has_int64_field()) {
    clear_oneof_field();
    set_has_int64_field();
    oneof_field_.int64_field_ = new ::google::protobuf::Int64Value;
  }
  // @@protoc_insertion_point(field_mutable:protobuf_unittest.OneofWellKnownTypes.int64_field)
  return oneof_field_.int64_field_;
}
inline ::google::protobuf::Int64Value* OneofWellKnownTypes::release_int64_field() {
  // @@protoc_insertion_point(field_release:protobuf_unittest.OneofWellKnownTypes.int64_field)
  if (has_int64_field()) {
    clear_has_oneof_field();
    ::google::protobuf::Int64Value* temp = oneof_field_.int64_field_;
    oneof_field_.int64_field_ = NULL;
    return temp;
  } else {
    return NULL;
  }
}
inline void OneofWellKnownTypes::set_allocated_int64_field(::google::protobuf::Int64Value* int64_field) {
  clear_oneof_field();
  if (int64_field) {
    if (static_cast< ::google::protobuf::Int64Value*>(int64_field)->GetArena() != NULL) {
      ::google::protobuf::Int64Value* new_int64_field = new ::google::protobuf::Int64Value;
      new_int64_field->CopyFrom(*int64_field);
      int64_field = new_int64_field;
    }
    set_has_int64_field();
    oneof_field_.int64_field_ = int64_field;
  }
  // @@protoc_insertion_point(field_set_allocated:protobuf_unittest.OneofWellKnownTypes.int64_field)
}

// optional .google.protobuf.UInt64Value uint64_field = 13;
inline bool OneofWellKnownTypes::has_uint64_field() const {
  return oneof_field_case() == kUint64Field;
}
inline void OneofWellKnownTypes::set_has_uint64_field() {
  _oneof_case_[0] = kUint64Field;
}
inline void OneofWellKnownTypes::clear_uint64_field() {
  if (has_uint64_field()) {
    delete oneof_field_.uint64_field_;
    clear_has_oneof_field();
  }
}
inline  const ::google::protobuf::UInt64Value& OneofWellKnownTypes::uint64_field() const {
  // @@protoc_insertion_point(field_get:protobuf_unittest.OneofWellKnownTypes.uint64_field)
  return has_uint64_field()
      ? *oneof_field_.uint64_field_
      : ::google::protobuf::UInt64Value::default_instance();
}
inline ::google::protobuf::UInt64Value* OneofWellKnownTypes::mutable_uint64_field() {
  if (!has_uint64_field()) {
    clear_oneof_field();
    set_has_uint64_field();
    oneof_field_.uint64_field_ = new ::google::protobuf::UInt64Value;
  }
  // @@protoc_insertion_point(field_mutable:protobuf_unittest.OneofWellKnownTypes.uint64_field)
  return oneof_field_.uint64_field_;
}
inline ::google::protobuf::UInt64Value* OneofWellKnownTypes::release_uint64_field() {
  // @@protoc_insertion_point(field_release:protobuf_unittest.OneofWellKnownTypes.uint64_field)
  if (has_uint64_field()) {
    clear_has_oneof_field();
    ::google::protobuf::UInt64Value* temp = oneof_field_.uint64_field_;
    oneof_field_.uint64_field_ = NULL;
    return temp;
  } else {
    return NULL;
  }
}
inline void OneofWellKnownTypes::set_allocated_uint64_field(::google::protobuf::UInt64Value* uint64_field) {
  clear_oneof_field();
  if (uint64_field) {
    if (static_cast< ::google::protobuf::UInt64Value*>(uint64_field)->GetArena() != NULL) {
      ::google::protobuf::UInt64Value* new_uint64_field = new ::google::protobuf::UInt64Value;
      new_uint64_field->CopyFrom(*uint64_field);
      uint64_field = new_uint64_field;
    }
    set_has_uint64_field();
    oneof_field_.uint64_field_ = uint64_field;
  }
  // @@protoc_insertion_point(field_set_allocated:protobuf_unittest.OneofWellKnownTypes.uint64_field)
}

// optional .google.protobuf.Int32Value int32_field = 14;
inline bool OneofWellKnownTypes::has_int32_field() const {
  return oneof_field_case() == kInt32Field;
}
inline void OneofWellKnownTypes::set_has_int32_field() {
  _oneof_case_[0] = kInt32Field;
}
inline void OneofWellKnownTypes::clear_int32_field() {
  if (has_int32_field()) {
    delete oneof_field_.int32_field_;
    clear_has_oneof_field();
  }
}
inline  const ::google::protobuf::Int32Value& OneofWellKnownTypes::int32_field() const {
  // @@protoc_insertion_point(field_get:protobuf_unittest.OneofWellKnownTypes.int32_field)
  return has_int32_field()
      ? *oneof_field_.int32_field_
      : ::google::protobuf::Int32Value::default_instance();
}
inline ::google::protobuf::Int32Value* OneofWellKnownTypes::mutable_int32_field() {
  if (!has_int32_field()) {
    clear_oneof_field();
    set_has_int32_field();
    oneof_field_.int32_field_ = new ::google::protobuf::Int32Value;
  }
  // @@protoc_insertion_point(field_mutable:protobuf_unittest.OneofWellKnownTypes.int32_field)
  return oneof_field_.int32_field_;
}
inline ::google::protobuf::Int32Value* OneofWellKnownTypes::release_int32_field() {
  // @@protoc_insertion_point(field_release:protobuf_unittest.OneofWellKnownTypes.int32_field)
  if (has_int32_field()) {
    clear_has_oneof_field();
    ::google::protobuf::Int32Value* temp = oneof_field_.int32_field_;
    oneof_field_.int32_field_ = NULL;
    return temp;
  } else {
    return NULL;
  }
}
inline void OneofWellKnownTypes::set_allocated_int32_field(::google::protobuf::Int32Value* int32_field) {
  clear_oneof_field();
  if (int32_field) {
    if (static_cast< ::google::protobuf::Int32Value*>(int32_field)->GetArena() != NULL) {
      ::google::protobuf::Int32Value* new_int32_field = new ::google::protobuf::Int32Value;
      new_int32_field->CopyFrom(*int32_field);
      int32_field = new_int32_field;
    }
    set_has_int32_field();
    oneof_field_.int32_field_ = int32_field;
  }
  // @@protoc_insertion_point(field_set_allocated:protobuf_unittest.OneofWellKnownTypes.int32_field)
}

// optional .google.protobuf.UInt32Value uint32_field = 15;
inline bool OneofWellKnownTypes::has_uint32_field() const {
  return oneof_field_case() == kUint32Field;
}
inline void OneofWellKnownTypes::set_has_uint32_field() {
  _oneof_case_[0] = kUint32Field;
}
inline void OneofWellKnownTypes::clear_uint32_field() {
  if (has_uint32_field()) {
    delete oneof_field_.uint32_field_;
    clear_has_oneof_field();
  }
}
inline  const ::google::protobuf::UInt32Value& OneofWellKnownTypes::uint32_field() const {
  // @@protoc_insertion_point(field_get:protobuf_unittest.OneofWellKnownTypes.uint32_field)
  return has_uint32_field()
      ? *oneof_field_.uint32_field_
      : ::google::protobuf::UInt32Value::default_instance();
}
inline ::google::protobuf::UInt32Value* OneofWellKnownTypes::mutable_uint32_field() {
  if (!has_uint32_field()) {
    clear_oneof_field();
    set_has_uint32_field();
    oneof_field_.uint32_field_ = new ::google::protobuf::UInt32Value;
  }
  // @@protoc_insertion_point(field_mutable:protobuf_unittest.OneofWellKnownTypes.uint32_field)
  return oneof_field_.uint32_field_;
}
inline ::google::protobuf::UInt32Value* OneofWellKnownTypes::release_uint32_field() {
  // @@protoc_insertion_point(field_release:protobuf_unittest.OneofWellKnownTypes.uint32_field)
  if (has_uint32_field()) {
    clear_has_oneof_field();
    ::google::protobuf::UInt32Value* temp = oneof_field_.uint32_field_;
    oneof_field_.uint32_field_ = NULL;
    return temp;
  } else {
    return NULL;
  }
}
inline void OneofWellKnownTypes::set_allocated_uint32_field(::google::protobuf::UInt32Value* uint32_field) {
  clear_oneof_field();
  if (uint32_field) {
    if (static_cast< ::google::protobuf::UInt32Value*>(uint32_field)->GetArena() != NULL) {
      ::google::protobuf::UInt32Value* new_uint32_field = new ::google::protobuf::UInt32Value;
      new_uint32_field->CopyFrom(*uint32_field);
      uint32_field = new_uint32_field;
    }
    set_has_uint32_field();
    oneof_field_.uint32_field_ = uint32_field;
  }
  // @@protoc_insertion_point(field_set_allocated:protobuf_unittest.OneofWellKnownTypes.uint32_field)
}

// optional .google.protobuf.BoolValue bool_field = 16;
inline bool OneofWellKnownTypes::has_bool_field() const {
  return oneof_field_case() == kBoolField;
}
inline void OneofWellKnownTypes::set_has_bool_field() {
  _oneof_case_[0] = kBoolField;
}
inline void OneofWellKnownTypes::clear_bool_field() {
  if (has_bool_field()) {
    delete oneof_field_.bool_field_;
    clear_has_oneof_field();
  }
}
inline  const ::google::protobuf::BoolValue& OneofWellKnownTypes::bool_field() const {
  // @@protoc_insertion_point(field_get:protobuf_unittest.OneofWellKnownTypes.bool_field)
  return has_bool_field()
      ? *oneof_field_.bool_field_
      : ::google::protobuf::BoolValue::default_instance();
}
inline ::google::protobuf::BoolValue* OneofWellKnownTypes::mutable_bool_field() {
  if (!has_bool_field()) {
    clear_oneof_field();
    set_has_bool_field();
    oneof_field_.bool_field_ = new ::google::protobuf::BoolValue;
  }
  // @@protoc_insertion_point(field_mutable:protobuf_unittest.OneofWellKnownTypes.bool_field)
  return oneof_field_.bool_field_;
}
inline ::google::protobuf::BoolValue* OneofWellKnownTypes::release_bool_field() {
  // @@protoc_insertion_point(field_release:protobuf_unittest.OneofWellKnownTypes.bool_field)
  if (has_bool_field()) {
    clear_has_oneof_field();
    ::google::protobuf::BoolValue* temp = oneof_field_.bool_field_;
    oneof_field_.bool_field_ = NULL;
    return temp;
  } else {
    return NULL;
  }
}
inline void OneofWellKnownTypes::set_allocated_bool_field(::google::protobuf::BoolValue* bool_field) {
  clear_oneof_field();
  if (bool_field) {
    if (static_cast< ::google::protobuf::BoolValue*>(bool_field)->GetArena() != NULL) {
      ::google::protobuf::BoolValue* new_bool_field = new ::google::protobuf::BoolValue;
      new_bool_field->CopyFrom(*bool_field);
      bool_field = new_bool_field;
    }
    set_has_bool_field();
    oneof_field_.bool_field_ = bool_field;
  }
  // @@protoc_insertion_point(field_set_allocated:protobuf_unittest.OneofWellKnownTypes.bool_field)
}

// optional .google.protobuf.StringValue string_field = 17;
inline bool OneofWellKnownTypes::has_string_field() const {
  return oneof_field_case() == kStringField;
}
inline void OneofWellKnownTypes::set_has_string_field() {
  _oneof_case_[0] = kStringField;
}
inline void OneofWellKnownTypes::clear_string_field() {
  if (has_string_field()) {
    delete oneof_field_.string_field_;
    clear_has_oneof_field();
  }
}
inline  const ::google::protobuf::StringValue& OneofWellKnownTypes::string_field() const {
  // @@protoc_insertion_point(field_get:protobuf_unittest.OneofWellKnownTypes.string_field)
  return has_string_field()
      ? *oneof_field_.string_field_
      : ::google::protobuf::StringValue::default_instance();
}
inline ::google::protobuf::StringValue* OneofWellKnownTypes::mutable_string_field() {
  if (!has_string_field()) {
    clear_oneof_field();
    set_has_string_field();
    oneof_field_.string_field_ = new ::google::protobuf::StringValue;
  }
  // @@protoc_insertion_point(field_mutable:protobuf_unittest.OneofWellKnownTypes.string_field)
  return oneof_field_.string_field_;
}
inline ::google::protobuf::StringValue* OneofWellKnownTypes::release_string_field() {
  // @@protoc_insertion_point(field_release:protobuf_unittest.OneofWellKnownTypes.string_field)
  if (has_string_field()) {
    clear_has_oneof_field();
    ::google::protobuf::StringValue* temp = oneof_field_.string_field_;
    oneof_field_.string_field_ = NULL;
    return temp;
  } else {
    return NULL;
  }
}
inline void OneofWellKnownTypes::set_allocated_string_field(::google::protobuf::StringValue* string_field) {
  clear_oneof_field();
  if (string_field) {
    if (static_cast< ::google::protobuf::StringValue*>(string_field)->GetArena() != NULL) {
      ::google::protobuf::StringValue* new_string_field = new ::google::protobuf::StringValue;
      new_string_field->CopyFrom(*string_field);
      string_field = new_string_field;
    }
    set_has_string_field();
    oneof_field_.string_field_ = string_field;
  }
  // @@protoc_insertion_point(field_set_allocated:protobuf_unittest.OneofWellKnownTypes.string_field)
}

// optional .google.protobuf.BytesValue bytes_field = 18;
inline bool OneofWellKnownTypes::has_bytes_field() const {
  return oneof_field_case() == kBytesField;
}
inline void OneofWellKnownTypes::set_has_bytes_field() {
  _oneof_case_[0] = kBytesField;
}
inline void OneofWellKnownTypes::clear_bytes_field() {
  if (has_bytes_field()) {
    delete oneof_field_.bytes_field_;
    clear_has_oneof_field();
  }
}
inline  const ::google::protobuf::BytesValue& OneofWellKnownTypes::bytes_field() const {
  // @@protoc_insertion_point(field_get:protobuf_unittest.OneofWellKnownTypes.bytes_field)
  return has_bytes_field()
      ? *oneof_field_.bytes_field_
      : ::google::protobuf::BytesValue::default_instance();
}
inline ::google::protobuf::BytesValue* OneofWellKnownTypes::mutable_bytes_field() {
  if (!has_bytes_field()) {
    clear_oneof_field();
    set_has_bytes_field();
    oneof_field_.bytes_field_ = new ::google::protobuf::BytesValue;
  }
  // @@protoc_insertion_point(field_mutable:protobuf_unittest.OneofWellKnownTypes.bytes_field)
  return oneof_field_.bytes_field_;
}
inline ::google::protobuf::BytesValue* OneofWellKnownTypes::release_bytes_field() {
  // @@protoc_insertion_point(field_release:protobuf_unittest.OneofWellKnownTypes.bytes_field)
  if (has_bytes_field()) {
    clear_has_oneof_field();
    ::google::protobuf::BytesValue* temp = oneof_field_.bytes_field_;
    oneof_field_.bytes_field_ = NULL;
    return temp;
  } else {
    return NULL;
  }
}
inline void OneofWellKnownTypes::set_allocated_bytes_field(::google::protobuf::BytesValue* bytes_field) {
  clear_oneof_field();
  if (bytes_field) {
    if (static_cast< ::google::protobuf::BytesValue*>(bytes_field)->GetArena() != NULL) {
      ::google::protobuf::BytesValue* new_bytes_field = new ::google::protobuf::BytesValue;
      new_bytes_field->CopyFrom(*bytes_field);
      bytes_field = new_bytes_field;
    }
    set_has_bytes_field();
    oneof_field_.bytes_field_ = bytes_field;
  }
  // @@protoc_insertion_point(field_set_allocated:protobuf_unittest.OneofWellKnownTypes.bytes_field)
}

inline bool OneofWellKnownTypes::has_oneof_field() const {
  return oneof_field_case() != ONEOF_FIELD_NOT_SET;
}
inline void OneofWellKnownTypes::clear_has_oneof_field() {
  _oneof_case_[0] = ONEOF_FIELD_NOT_SET;
}
inline OneofWellKnownTypes::OneofFieldCase OneofWellKnownTypes::oneof_field_case() const {
  return OneofWellKnownTypes::OneofFieldCase(_oneof_case_[0]);
}
inline const OneofWellKnownTypes* OneofWellKnownTypes::internal_default_instance() {
  return &OneofWellKnownTypes_default_instance_.get();
}
// -------------------------------------------------------------------

// MapWellKnownTypes

// map<int32, .google.protobuf.Any> any_field = 1;
inline int MapWellKnownTypes::any_field_size() const {
  return any_field_.size();
}
inline void MapWellKnownTypes::clear_any_field() {
  any_field_.Clear();
}
inline const ::google::protobuf::Map< ::google::protobuf::int32, ::google::protobuf::Any >&
MapWellKnownTypes::any_field() const {
  // @@protoc_insertion_point(field_map:protobuf_unittest.MapWellKnownTypes.any_field)
  return any_field_.GetMap();
}
inline ::google::protobuf::Map< ::google::protobuf::int32, ::google::protobuf::Any >*
MapWellKnownTypes::mutable_any_field() {
  // @@protoc_insertion_point(field_mutable_map:protobuf_unittest.MapWellKnownTypes.any_field)
  return any_field_.MutableMap();
}

// map<int32, .google.protobuf.Api> api_field = 2;
inline int MapWellKnownTypes::api_field_size() const {
  return api_field_.size();
}
inline void MapWellKnownTypes::clear_api_field() {
  api_field_.Clear();
}
inline const ::google::protobuf::Map< ::google::protobuf::int32, ::google::protobuf::Api >&
MapWellKnownTypes::api_field() const {
  // @@protoc_insertion_point(field_map:protobuf_unittest.MapWellKnownTypes.api_field)
  return api_field_.GetMap();
}
inline ::google::protobuf::Map< ::google::protobuf::int32, ::google::protobuf::Api >*
MapWellKnownTypes::mutable_api_field() {
  // @@protoc_insertion_point(field_mutable_map:protobuf_unittest.MapWellKnownTypes.api_field)
  return api_field_.MutableMap();
}

// map<int32, .google.protobuf.Duration> duration_field = 3;
inline int MapWellKnownTypes::duration_field_size() const {
  return duration_field_.size();
}
inline void MapWellKnownTypes::clear_duration_field() {
  duration_field_.Clear();
}
inline const ::google::protobuf::Map< ::google::protobuf::int32, ::google::protobuf::Duration >&
MapWellKnownTypes::duration_field() const {
  // @@protoc_insertion_point(field_map:protobuf_unittest.MapWellKnownTypes.duration_field)
  return duration_field_.GetMap();
}
inline ::google::protobuf::Map< ::google::protobuf::int32, ::google::protobuf::Duration >*
MapWellKnownTypes::mutable_duration_field() {
  // @@protoc_insertion_point(field_mutable_map:protobuf_unittest.MapWellKnownTypes.duration_field)
  return duration_field_.MutableMap();
}

// map<int32, .google.protobuf.Empty> empty_field = 4;
inline int MapWellKnownTypes::empty_field_size() const {
  return empty_field_.size();
}
inline void MapWellKnownTypes::clear_empty_field() {
  empty_field_.Clear();
}
inline const ::google::protobuf::Map< ::google::protobuf::int32, ::google::protobuf::Empty >&
MapWellKnownTypes::empty_field() const {
  // @@protoc_insertion_point(field_map:protobuf_unittest.MapWellKnownTypes.empty_field)
  return empty_field_.GetMap();
}
inline ::google::protobuf::Map< ::google::protobuf::int32, ::google::protobuf::Empty >*
MapWellKnownTypes::mutable_empty_field() {
  // @@protoc_insertion_point(field_mutable_map:protobuf_unittest.MapWellKnownTypes.empty_field)
  return empty_field_.MutableMap();
}

// map<int32, .google.protobuf.FieldMask> field_mask_field = 5;
inline int MapWellKnownTypes::field_mask_field_size() const {
  return field_mask_field_.size();
}
inline void MapWellKnownTypes::clear_field_mask_field() {
  field_mask_field_.Clear();
}
inline const ::google::protobuf::Map< ::google::protobuf::int32, ::google::protobuf::FieldMask >&
MapWellKnownTypes::field_mask_field() const {
  // @@protoc_insertion_point(field_map:protobuf_unittest.MapWellKnownTypes.field_mask_field)
  return field_mask_field_.GetMap();
}
inline ::google::protobuf::Map< ::google::protobuf::int32, ::google::protobuf::FieldMask >*
MapWellKnownTypes::mutable_field_mask_field() {
  // @@protoc_insertion_point(field_mutable_map:protobuf_unittest.MapWellKnownTypes.field_mask_field)
  return field_mask_field_.MutableMap();
}

// map<int32, .google.protobuf.SourceContext> source_context_field = 6;
inline int MapWellKnownTypes::source_context_field_size() const {
  return source_context_field_.size();
}
inline void MapWellKnownTypes::clear_source_context_field() {
  source_context_field_.Clear();
}
inline const ::google::protobuf::Map< ::google::protobuf::int32, ::google::protobuf::SourceContext >&
MapWellKnownTypes::source_context_field() const {
  // @@protoc_insertion_point(field_map:protobuf_unittest.MapWellKnownTypes.source_context_field)
  return source_context_field_.GetMap();
}
inline ::google::protobuf::Map< ::google::protobuf::int32, ::google::protobuf::SourceContext >*
MapWellKnownTypes::mutable_source_context_field() {
  // @@protoc_insertion_point(field_mutable_map:protobuf_unittest.MapWellKnownTypes.source_context_field)
  return source_context_field_.MutableMap();
}

// map<int32, .google.protobuf.Struct> struct_field = 7;
inline int MapWellKnownTypes::struct_field_size() const {
  return struct_field_.size();
}
inline void MapWellKnownTypes::clear_struct_field() {
  struct_field_.Clear();
}
inline const ::google::protobuf::Map< ::google::protobuf::int32, ::google::protobuf::Struct >&
MapWellKnownTypes::struct_field() const {
  // @@protoc_insertion_point(field_map:protobuf_unittest.MapWellKnownTypes.struct_field)
  return struct_field_.GetMap();
}
inline ::google::protobuf::Map< ::google::protobuf::int32, ::google::protobuf::Struct >*
MapWellKnownTypes::mutable_struct_field() {
  // @@protoc_insertion_point(field_mutable_map:protobuf_unittest.MapWellKnownTypes.struct_field)
  return struct_field_.MutableMap();
}

// map<int32, .google.protobuf.Timestamp> timestamp_field = 8;
inline int MapWellKnownTypes::timestamp_field_size() const {
  return timestamp_field_.size();
}
inline void MapWellKnownTypes::clear_timestamp_field() {
  timestamp_field_.Clear();
}
inline const ::google::protobuf::Map< ::google::protobuf::int32, ::google::protobuf::Timestamp >&
MapWellKnownTypes::timestamp_field() const {
  // @@protoc_insertion_point(field_map:protobuf_unittest.MapWellKnownTypes.timestamp_field)
  return timestamp_field_.GetMap();
}
inline ::google::protobuf::Map< ::google::protobuf::int32, ::google::protobuf::Timestamp >*
MapWellKnownTypes::mutable_timestamp_field() {
  // @@protoc_insertion_point(field_mutable_map:protobuf_unittest.MapWellKnownTypes.timestamp_field)
  return timestamp_field_.MutableMap();
}

// map<int32, .google.protobuf.Type> type_field = 9;
inline int MapWellKnownTypes::type_field_size() const {
  return type_field_.size();
}
inline void MapWellKnownTypes::clear_type_field() {
  type_field_.Clear();
}
inline const ::google::protobuf::Map< ::google::protobuf::int32, ::google::protobuf::Type >&
MapWellKnownTypes::type_field() const {
  // @@protoc_insertion_point(field_map:protobuf_unittest.MapWellKnownTypes.type_field)
  return type_field_.GetMap();
}
inline ::google::protobuf::Map< ::google::protobuf::int32, ::google::protobuf::Type >*
MapWellKnownTypes::mutable_type_field() {
  // @@protoc_insertion_point(field_mutable_map:protobuf_unittest.MapWellKnownTypes.type_field)
  return type_field_.MutableMap();
}

// map<int32, .google.protobuf.DoubleValue> double_field = 10;
inline int MapWellKnownTypes::double_field_size() const {
  return double_field_.size();
}
inline void MapWellKnownTypes::clear_double_field() {
  double_field_.Clear();
}
inline const ::google::protobuf::Map< ::google::protobuf::int32, ::google::protobuf::DoubleValue >&
MapWellKnownTypes::double_field() const {
  // @@protoc_insertion_point(field_map:protobuf_unittest.MapWellKnownTypes.double_field)
  return double_field_.GetMap();
}
inline ::google::protobuf::Map< ::google::protobuf::int32, ::google::protobuf::DoubleValue >*
MapWellKnownTypes::mutable_double_field() {
  // @@protoc_insertion_point(field_mutable_map:protobuf_unittest.MapWellKnownTypes.double_field)
  return double_field_.MutableMap();
}

// map<int32, .google.protobuf.FloatValue> float_field = 11;
inline int MapWellKnownTypes::float_field_size() const {
  return float_field_.size();
}
inline void MapWellKnownTypes::clear_float_field() {
  float_field_.Clear();
}
inline const ::google::protobuf::Map< ::google::protobuf::int32, ::google::protobuf::FloatValue >&
MapWellKnownTypes::float_field() const {
  // @@protoc_insertion_point(field_map:protobuf_unittest.MapWellKnownTypes.float_field)
  return float_field_.GetMap();
}
inline ::google::protobuf::Map< ::google::protobuf::int32, ::google::protobuf::FloatValue >*
MapWellKnownTypes::mutable_float_field() {
  // @@protoc_insertion_point(field_mutable_map:protobuf_unittest.MapWellKnownTypes.float_field)
  return float_field_.MutableMap();
}

// map<int32, .google.protobuf.Int64Value> int64_field = 12;
inline int MapWellKnownTypes::int64_field_size() const {
  return int64_field_.size();
}
inline void MapWellKnownTypes::clear_int64_field() {
  int64_field_.Clear();
}
inline const ::google::protobuf::Map< ::google::protobuf::int32, ::google::protobuf::Int64Value >&
MapWellKnownTypes::int64_field() const {
  // @@protoc_insertion_point(field_map:protobuf_unittest.MapWellKnownTypes.int64_field)
  return int64_field_.GetMap();
}
inline ::google::protobuf::Map< ::google::protobuf::int32, ::google::protobuf::Int64Value >*
MapWellKnownTypes::mutable_int64_field() {
  // @@protoc_insertion_point(field_mutable_map:protobuf_unittest.MapWellKnownTypes.int64_field)
  return int64_field_.MutableMap();
}

// map<int32, .google.protobuf.UInt64Value> uint64_field = 13;
inline int MapWellKnownTypes::uint64_field_size() const {
  return uint64_field_.size();
}
inline void MapWellKnownTypes::clear_uint64_field() {
  uint64_field_.Clear();
}
inline const ::google::protobuf::Map< ::google::protobuf::int32, ::google::protobuf::UInt64Value >&
MapWellKnownTypes::uint64_field() const {
  // @@protoc_insertion_point(field_map:protobuf_unittest.MapWellKnownTypes.uint64_field)
  return uint64_field_.GetMap();
}
inline ::google::protobuf::Map< ::google::protobuf::int32, ::google::protobuf::UInt64Value >*
MapWellKnownTypes::mutable_uint64_field() {
  // @@protoc_insertion_point(field_mutable_map:protobuf_unittest.MapWellKnownTypes.uint64_field)
  return uint64_field_.MutableMap();
}

// map<int32, .google.protobuf.Int32Value> int32_field = 14;
inline int MapWellKnownTypes::int32_field_size() const {
  return int32_field_.size();
}
inline void MapWellKnownTypes::clear_int32_field() {
  int32_field_.Clear();
}
inline const ::google::protobuf::Map< ::google::protobuf::int32, ::google::protobuf::Int32Value >&
MapWellKnownTypes::int32_field() const {
  // @@protoc_insertion_point(field_map:protobuf_unittest.MapWellKnownTypes.int32_field)
  return int32_field_.GetMap();
}
inline ::google::protobuf::Map< ::google::protobuf::int32, ::google::protobuf::Int32Value >*
MapWellKnownTypes::mutable_int32_field() {
  // @@protoc_insertion_point(field_mutable_map:protobuf_unittest.MapWellKnownTypes.int32_field)
  return int32_field_.MutableMap();
}

// map<int32, .google.protobuf.UInt32Value> uint32_field = 15;
inline int MapWellKnownTypes::uint32_field_size() const {
  return uint32_field_.size();
}
inline void MapWellKnownTypes::clear_uint32_field() {
  uint32_field_.Clear();
}
inline const ::google::protobuf::Map< ::google::protobuf::int32, ::google::protobuf::UInt32Value >&
MapWellKnownTypes::uint32_field() const {
  // @@protoc_insertion_point(field_map:protobuf_unittest.MapWellKnownTypes.uint32_field)
  return uint32_field_.GetMap();
}
inline ::google::protobuf::Map< ::google::protobuf::int32, ::google::protobuf::UInt32Value >*
MapWellKnownTypes::mutable_uint32_field() {
  // @@protoc_insertion_point(field_mutable_map:protobuf_unittest.MapWellKnownTypes.uint32_field)
  return uint32_field_.MutableMap();
}

// map<int32, .google.protobuf.BoolValue> bool_field = 16;
inline int MapWellKnownTypes::bool_field_size() const {
  return bool_field_.size();
}
inline void MapWellKnownTypes::clear_bool_field() {
  bool_field_.Clear();
}
inline const ::google::protobuf::Map< ::google::protobuf::int32, ::google::protobuf::BoolValue >&
MapWellKnownTypes::bool_field() const {
  // @@protoc_insertion_point(field_map:protobuf_unittest.MapWellKnownTypes.bool_field)
  return bool_field_.GetMap();
}
inline ::google::protobuf::Map< ::google::protobuf::int32, ::google::protobuf::BoolValue >*
MapWellKnownTypes::mutable_bool_field() {
  // @@protoc_insertion_point(field_mutable_map:protobuf_unittest.MapWellKnownTypes.bool_field)
  return bool_field_.MutableMap();
}

// map<int32, .google.protobuf.StringValue> string_field = 17;
inline int MapWellKnownTypes::string_field_size() const {
  return string_field_.size();
}
inline void MapWellKnownTypes::clear_string_field() {
  string_field_.Clear();
}
inline const ::google::protobuf::Map< ::google::protobuf::int32, ::google::protobuf::StringValue >&
MapWellKnownTypes::string_field() const {
  // @@protoc_insertion_point(field_map:protobuf_unittest.MapWellKnownTypes.string_field)
  return string_field_.GetMap();
}
inline ::google::protobuf::Map< ::google::protobuf::int32, ::google::protobuf::StringValue >*
MapWellKnownTypes::mutable_string_field() {
  // @@protoc_insertion_point(field_mutable_map:protobuf_unittest.MapWellKnownTypes.string_field)
  return string_field_.MutableMap();
}

// map<int32, .google.protobuf.BytesValue> bytes_field = 18;
inline int MapWellKnownTypes::bytes_field_size() const {
  return bytes_field_.size();
}
inline void MapWellKnownTypes::clear_bytes_field() {
  bytes_field_.Clear();
}
inline const ::google::protobuf::Map< ::google::protobuf::int32, ::google::protobuf::BytesValue >&
MapWellKnownTypes::bytes_field() const {
  // @@protoc_insertion_point(field_map:protobuf_unittest.MapWellKnownTypes.bytes_field)
  return bytes_field_.GetMap();
}
inline ::google::protobuf::Map< ::google::protobuf::int32, ::google::protobuf::BytesValue >*
MapWellKnownTypes::mutable_bytes_field() {
  // @@protoc_insertion_point(field_mutable_map:protobuf_unittest.MapWellKnownTypes.bytes_field)
  return bytes_field_.MutableMap();
}

inline const MapWellKnownTypes* MapWellKnownTypes::internal_default_instance() {
  return &MapWellKnownTypes_default_instance_.get();
}
#endif  // !PROTOBUF_INLINE_NOT_IN_HEADERS
// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------


// @@protoc_insertion_point(namespace_scope)

}  // namespace protobuf_unittest

// @@protoc_insertion_point(global_scope)

#endif  // PROTOBUF_google_2fprotobuf_2funittest_5fwell_5fknown_5ftypes_2eproto__INCLUDED

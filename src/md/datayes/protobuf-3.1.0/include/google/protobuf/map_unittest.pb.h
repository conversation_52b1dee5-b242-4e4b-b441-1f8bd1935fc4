// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: google/protobuf/map_unittest.proto

#ifndef PROTOBUF_google_2fprotobuf_2fmap_5funittest_2eproto__INCLUDED
#define PROTOBUF_google_2fprotobuf_2fmap_5funittest_2eproto__INCLUDED

#include <string>

#include <google/protobuf/stubs/common.h>

#if GOOGLE_PROTOBUF_VERSION < 3001000
#error This file was generated by a newer version of protoc which is
#error incompatible with your Protocol Buffer headers.  Please update
#error your headers.
#endif
#if 3001000 < GOOGLE_PROTOBUF_MIN_PROTOC_VERSION
#error This file was generated by an older version of protoc which is
#error incompatible with your Protocol Buffer headers.  Please
#error regenerate this file with a newer version of protoc.
#endif

#include <google/protobuf/arena.h>
#include <google/protobuf/arenastring.h>
#include <google/protobuf/generated_message_util.h>
#include <google/protobuf/metadata.h>
#include <google/protobuf/message.h>
#include <google/protobuf/repeated_field.h>
#include <google/protobuf/extension_set.h>
#include <google/protobuf/map.h>
#include <google/protobuf/map_field_inl.h>
#include <google/protobuf/generated_enum_reflection.h>
#include <google/protobuf/unknown_field_set.h>
#include <google/protobuf/unittest.pb.h>
#include <google/protobuf/unittest_no_arena.pb.h>
// @@protoc_insertion_point(includes)

namespace protobuf_unittest {

// Internal implementation detail -- do not call these.
void protobuf_AddDesc_google_2fprotobuf_2fmap_5funittest_2eproto();
void protobuf_InitDefaults_google_2fprotobuf_2fmap_5funittest_2eproto();
void protobuf_AssignDesc_google_2fprotobuf_2fmap_5funittest_2eproto();
void protobuf_ShutdownFile_google_2fprotobuf_2fmap_5funittest_2eproto();

class MessageContainingEnumCalledType;
class MessageContainingMapCalledEntry;
class TestArenaMap;
class TestMap;
class TestMapSubmessage;
class TestMessageMap;
class TestRecursiveMapMessage;
class TestRequiredMessageMap;
class TestSameTypeMap;

enum MessageContainingEnumCalledType_Type {
  MessageContainingEnumCalledType_Type_TYPE_FOO = 0,
  MessageContainingEnumCalledType_Type_MessageContainingEnumCalledType_Type_INT_MIN_SENTINEL_DO_NOT_USE_ = ::google::protobuf::kint32min,
  MessageContainingEnumCalledType_Type_MessageContainingEnumCalledType_Type_INT_MAX_SENTINEL_DO_NOT_USE_ = ::google::protobuf::kint32max
};
bool MessageContainingEnumCalledType_Type_IsValid(int value);
const MessageContainingEnumCalledType_Type MessageContainingEnumCalledType_Type_Type_MIN = MessageContainingEnumCalledType_Type_TYPE_FOO;
const MessageContainingEnumCalledType_Type MessageContainingEnumCalledType_Type_Type_MAX = MessageContainingEnumCalledType_Type_TYPE_FOO;
const int MessageContainingEnumCalledType_Type_Type_ARRAYSIZE = MessageContainingEnumCalledType_Type_Type_MAX + 1;

const ::google::protobuf::EnumDescriptor* MessageContainingEnumCalledType_Type_descriptor();
inline const ::std::string& MessageContainingEnumCalledType_Type_Name(MessageContainingEnumCalledType_Type value) {
  return ::google::protobuf::internal::NameOfEnum(
    MessageContainingEnumCalledType_Type_descriptor(), value);
}
inline bool MessageContainingEnumCalledType_Type_Parse(
    const ::std::string& name, MessageContainingEnumCalledType_Type* value) {
  return ::google::protobuf::internal::ParseNamedEnum<MessageContainingEnumCalledType_Type>(
    MessageContainingEnumCalledType_Type_descriptor(), name, value);
}
enum MapEnum {
  MAP_ENUM_FOO = 0,
  MAP_ENUM_BAR = 1,
  MAP_ENUM_BAZ = 2,
  MapEnum_INT_MIN_SENTINEL_DO_NOT_USE_ = ::google::protobuf::kint32min,
  MapEnum_INT_MAX_SENTINEL_DO_NOT_USE_ = ::google::protobuf::kint32max
};
bool MapEnum_IsValid(int value);
const MapEnum MapEnum_MIN = MAP_ENUM_FOO;
const MapEnum MapEnum_MAX = MAP_ENUM_BAZ;
const int MapEnum_ARRAYSIZE = MapEnum_MAX + 1;

const ::google::protobuf::EnumDescriptor* MapEnum_descriptor();
inline const ::std::string& MapEnum_Name(MapEnum value) {
  return ::google::protobuf::internal::NameOfEnum(
    MapEnum_descriptor(), value);
}
inline bool MapEnum_Parse(
    const ::std::string& name, MapEnum* value) {
  return ::google::protobuf::internal::ParseNamedEnum<MapEnum>(
    MapEnum_descriptor(), name, value);
}
// ===================================================================

class TestMap : public ::google::protobuf::Message /* @@protoc_insertion_point(class_definition:protobuf_unittest.TestMap) */ {
 public:
  TestMap();
  virtual ~TestMap();

  TestMap(const TestMap& from);

  inline TestMap& operator=(const TestMap& from) {
    CopyFrom(from);
    return *this;
  }

  inline ::google::protobuf::Arena* GetArena() const { return GetArenaNoVirtual(); }
  inline void* GetMaybeArenaPointer() const {
    return MaybeArenaPtr();
  }
  static const ::google::protobuf::Descriptor* descriptor();
  static const TestMap& default_instance();

  static const TestMap* internal_default_instance();

  void UnsafeArenaSwap(TestMap* other);
  void Swap(TestMap* other);

  // implements Message ----------------------------------------------

  inline TestMap* New() const { return New(NULL); }

  TestMap* New(::google::protobuf::Arena* arena) const;
  void CopyFrom(const ::google::protobuf::Message& from);
  void MergeFrom(const ::google::protobuf::Message& from);
  void CopyFrom(const TestMap& from);
  void MergeFrom(const TestMap& from);
  void Clear();
  bool IsInitialized() const;

  size_t ByteSizeLong() const;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input);
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const;
  ::google::protobuf::uint8* InternalSerializeWithCachedSizesToArray(
      bool deterministic, ::google::protobuf::uint8* output) const;
  ::google::protobuf::uint8* SerializeWithCachedSizesToArray(::google::protobuf::uint8* output) const {
    return InternalSerializeWithCachedSizesToArray(false, output);
  }
  int GetCachedSize() const { return _cached_size_; }
  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const;
  void InternalSwap(TestMap* other);
  void UnsafeMergeFrom(const TestMap& from);
  protected:
  explicit TestMap(::google::protobuf::Arena* arena);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::google::protobuf::Arena* arena);
  private:
  inline ::google::protobuf::Arena* GetArenaNoVirtual() const {
    return _internal_metadata_.arena();
  }
  inline void* MaybeArenaPtr() const {
    return _internal_metadata_.raw_arena_ptr();
  }
  public:

  ::google::protobuf::Metadata GetMetadata() const;

  // nested types ----------------------------------------------------


  // accessors -------------------------------------------------------

  // map<int32, int32> map_int32_int32 = 1;
  int map_int32_int32_size() const;
  void clear_map_int32_int32();
  static const int kMapInt32Int32FieldNumber = 1;
  const ::google::protobuf::Map< ::google::protobuf::int32, ::google::protobuf::int32 >&
      map_int32_int32() const;
  ::google::protobuf::Map< ::google::protobuf::int32, ::google::protobuf::int32 >*
      mutable_map_int32_int32();

  // map<int64, int64> map_int64_int64 = 2;
  int map_int64_int64_size() const;
  void clear_map_int64_int64();
  static const int kMapInt64Int64FieldNumber = 2;
  const ::google::protobuf::Map< ::google::protobuf::int64, ::google::protobuf::int64 >&
      map_int64_int64() const;
  ::google::protobuf::Map< ::google::protobuf::int64, ::google::protobuf::int64 >*
      mutable_map_int64_int64();

  // map<uint32, uint32> map_uint32_uint32 = 3;
  int map_uint32_uint32_size() const;
  void clear_map_uint32_uint32();
  static const int kMapUint32Uint32FieldNumber = 3;
  const ::google::protobuf::Map< ::google::protobuf::uint32, ::google::protobuf::uint32 >&
      map_uint32_uint32() const;
  ::google::protobuf::Map< ::google::protobuf::uint32, ::google::protobuf::uint32 >*
      mutable_map_uint32_uint32();

  // map<uint64, uint64> map_uint64_uint64 = 4;
  int map_uint64_uint64_size() const;
  void clear_map_uint64_uint64();
  static const int kMapUint64Uint64FieldNumber = 4;
  const ::google::protobuf::Map< ::google::protobuf::uint64, ::google::protobuf::uint64 >&
      map_uint64_uint64() const;
  ::google::protobuf::Map< ::google::protobuf::uint64, ::google::protobuf::uint64 >*
      mutable_map_uint64_uint64();

  // map<sint32, sint32> map_sint32_sint32 = 5;
  int map_sint32_sint32_size() const;
  void clear_map_sint32_sint32();
  static const int kMapSint32Sint32FieldNumber = 5;
  const ::google::protobuf::Map< ::google::protobuf::int32, ::google::protobuf::int32 >&
      map_sint32_sint32() const;
  ::google::protobuf::Map< ::google::protobuf::int32, ::google::protobuf::int32 >*
      mutable_map_sint32_sint32();

  // map<sint64, sint64> map_sint64_sint64 = 6;
  int map_sint64_sint64_size() const;
  void clear_map_sint64_sint64();
  static const int kMapSint64Sint64FieldNumber = 6;
  const ::google::protobuf::Map< ::google::protobuf::int64, ::google::protobuf::int64 >&
      map_sint64_sint64() const;
  ::google::protobuf::Map< ::google::protobuf::int64, ::google::protobuf::int64 >*
      mutable_map_sint64_sint64();

  // map<fixed32, fixed32> map_fixed32_fixed32 = 7;
  int map_fixed32_fixed32_size() const;
  void clear_map_fixed32_fixed32();
  static const int kMapFixed32Fixed32FieldNumber = 7;
  const ::google::protobuf::Map< ::google::protobuf::uint32, ::google::protobuf::uint32 >&
      map_fixed32_fixed32() const;
  ::google::protobuf::Map< ::google::protobuf::uint32, ::google::protobuf::uint32 >*
      mutable_map_fixed32_fixed32();

  // map<fixed64, fixed64> map_fixed64_fixed64 = 8;
  int map_fixed64_fixed64_size() const;
  void clear_map_fixed64_fixed64();
  static const int kMapFixed64Fixed64FieldNumber = 8;
  const ::google::protobuf::Map< ::google::protobuf::uint64, ::google::protobuf::uint64 >&
      map_fixed64_fixed64() const;
  ::google::protobuf::Map< ::google::protobuf::uint64, ::google::protobuf::uint64 >*
      mutable_map_fixed64_fixed64();

  // map<sfixed32, sfixed32> map_sfixed32_sfixed32 = 9;
  int map_sfixed32_sfixed32_size() const;
  void clear_map_sfixed32_sfixed32();
  static const int kMapSfixed32Sfixed32FieldNumber = 9;
  const ::google::protobuf::Map< ::google::protobuf::int32, ::google::protobuf::int32 >&
      map_sfixed32_sfixed32() const;
  ::google::protobuf::Map< ::google::protobuf::int32, ::google::protobuf::int32 >*
      mutable_map_sfixed32_sfixed32();

  // map<sfixed64, sfixed64> map_sfixed64_sfixed64 = 10;
  int map_sfixed64_sfixed64_size() const;
  void clear_map_sfixed64_sfixed64();
  static const int kMapSfixed64Sfixed64FieldNumber = 10;
  const ::google::protobuf::Map< ::google::protobuf::int64, ::google::protobuf::int64 >&
      map_sfixed64_sfixed64() const;
  ::google::protobuf::Map< ::google::protobuf::int64, ::google::protobuf::int64 >*
      mutable_map_sfixed64_sfixed64();

  // map<int32, float> map_int32_float = 11;
  int map_int32_float_size() const;
  void clear_map_int32_float();
  static const int kMapInt32FloatFieldNumber = 11;
  const ::google::protobuf::Map< ::google::protobuf::int32, float >&
      map_int32_float() const;
  ::google::protobuf::Map< ::google::protobuf::int32, float >*
      mutable_map_int32_float();

  // map<int32, double> map_int32_double = 12;
  int map_int32_double_size() const;
  void clear_map_int32_double();
  static const int kMapInt32DoubleFieldNumber = 12;
  const ::google::protobuf::Map< ::google::protobuf::int32, double >&
      map_int32_double() const;
  ::google::protobuf::Map< ::google::protobuf::int32, double >*
      mutable_map_int32_double();

  // map<bool, bool> map_bool_bool = 13;
  int map_bool_bool_size() const;
  void clear_map_bool_bool();
  static const int kMapBoolBoolFieldNumber = 13;
  const ::google::protobuf::Map< bool, bool >&
      map_bool_bool() const;
  ::google::protobuf::Map< bool, bool >*
      mutable_map_bool_bool();

  // map<string, string> map_string_string = 14;
  int map_string_string_size() const;
  void clear_map_string_string();
  static const int kMapStringStringFieldNumber = 14;
  const ::google::protobuf::Map< ::std::string, ::std::string >&
      map_string_string() const;
  ::google::protobuf::Map< ::std::string, ::std::string >*
      mutable_map_string_string();

  // map<int32, bytes> map_int32_bytes = 15;
  int map_int32_bytes_size() const;
  void clear_map_int32_bytes();
  static const int kMapInt32BytesFieldNumber = 15;
  const ::google::protobuf::Map< ::google::protobuf::int32, ::std::string >&
      map_int32_bytes() const;
  ::google::protobuf::Map< ::google::protobuf::int32, ::std::string >*
      mutable_map_int32_bytes();

  // map<int32, .protobuf_unittest.MapEnum> map_int32_enum = 16;
  int map_int32_enum_size() const;
  void clear_map_int32_enum();
  static const int kMapInt32EnumFieldNumber = 16;
  const ::google::protobuf::Map< ::google::protobuf::int32, ::protobuf_unittest::MapEnum >&
      map_int32_enum() const;
  ::google::protobuf::Map< ::google::protobuf::int32, ::protobuf_unittest::MapEnum >*
      mutable_map_int32_enum();

  // map<int32, .protobuf_unittest.ForeignMessage> map_int32_foreign_message = 17;
  int map_int32_foreign_message_size() const;
  void clear_map_int32_foreign_message();
  static const int kMapInt32ForeignMessageFieldNumber = 17;
  const ::google::protobuf::Map< ::google::protobuf::int32, ::protobuf_unittest::ForeignMessage >&
      map_int32_foreign_message() const;
  ::google::protobuf::Map< ::google::protobuf::int32, ::protobuf_unittest::ForeignMessage >*
      mutable_map_int32_foreign_message();

  // map<string, .protobuf_unittest.ForeignMessage> map_string_foreign_message = 18;
  int map_string_foreign_message_size() const;
  void clear_map_string_foreign_message();
  static const int kMapStringForeignMessageFieldNumber = 18;
  const ::google::protobuf::Map< ::std::string, ::protobuf_unittest::ForeignMessage >&
      map_string_foreign_message() const;
  ::google::protobuf::Map< ::std::string, ::protobuf_unittest::ForeignMessage >*
      mutable_map_string_foreign_message();

  // @@protoc_insertion_point(class_scope:protobuf_unittest.TestMap)
 private:

  ::google::protobuf::internal::InternalMetadataWithArena _internal_metadata_;
  friend class ::google::protobuf::Arena;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  typedef ::google::protobuf::internal::MapEntryLite<
      ::google::protobuf::int32, ::google::protobuf::int32,
      ::google::protobuf::internal::WireFormatLite::TYPE_INT32,
      ::google::protobuf::internal::WireFormatLite::TYPE_INT32,
      0 >
      TestMap_MapInt32Int32Entry;
  ::google::protobuf::internal::MapField<
      ::google::protobuf::int32, ::google::protobuf::int32,
      ::google::protobuf::internal::WireFormatLite::TYPE_INT32,
      ::google::protobuf::internal::WireFormatLite::TYPE_INT32,
      0 > map_int32_int32_;
  typedef ::google::protobuf::internal::MapEntryLite<
      ::google::protobuf::int64, ::google::protobuf::int64,
      ::google::protobuf::internal::WireFormatLite::TYPE_INT64,
      ::google::protobuf::internal::WireFormatLite::TYPE_INT64,
      0 >
      TestMap_MapInt64Int64Entry;
  ::google::protobuf::internal::MapField<
      ::google::protobuf::int64, ::google::protobuf::int64,
      ::google::protobuf::internal::WireFormatLite::TYPE_INT64,
      ::google::protobuf::internal::WireFormatLite::TYPE_INT64,
      0 > map_int64_int64_;
  typedef ::google::protobuf::internal::MapEntryLite<
      ::google::protobuf::uint32, ::google::protobuf::uint32,
      ::google::protobuf::internal::WireFormatLite::TYPE_UINT32,
      ::google::protobuf::internal::WireFormatLite::TYPE_UINT32,
      0 >
      TestMap_MapUint32Uint32Entry;
  ::google::protobuf::internal::MapField<
      ::google::protobuf::uint32, ::google::protobuf::uint32,
      ::google::protobuf::internal::WireFormatLite::TYPE_UINT32,
      ::google::protobuf::internal::WireFormatLite::TYPE_UINT32,
      0 > map_uint32_uint32_;
  typedef ::google::protobuf::internal::MapEntryLite<
      ::google::protobuf::uint64, ::google::protobuf::uint64,
      ::google::protobuf::internal::WireFormatLite::TYPE_UINT64,
      ::google::protobuf::internal::WireFormatLite::TYPE_UINT64,
      0 >
      TestMap_MapUint64Uint64Entry;
  ::google::protobuf::internal::MapField<
      ::google::protobuf::uint64, ::google::protobuf::uint64,
      ::google::protobuf::internal::WireFormatLite::TYPE_UINT64,
      ::google::protobuf::internal::WireFormatLite::TYPE_UINT64,
      0 > map_uint64_uint64_;
  typedef ::google::protobuf::internal::MapEntryLite<
      ::google::protobuf::int32, ::google::protobuf::int32,
      ::google::protobuf::internal::WireFormatLite::TYPE_SINT32,
      ::google::protobuf::internal::WireFormatLite::TYPE_SINT32,
      0 >
      TestMap_MapSint32Sint32Entry;
  ::google::protobuf::internal::MapField<
      ::google::protobuf::int32, ::google::protobuf::int32,
      ::google::protobuf::internal::WireFormatLite::TYPE_SINT32,
      ::google::protobuf::internal::WireFormatLite::TYPE_SINT32,
      0 > map_sint32_sint32_;
  typedef ::google::protobuf::internal::MapEntryLite<
      ::google::protobuf::int64, ::google::protobuf::int64,
      ::google::protobuf::internal::WireFormatLite::TYPE_SINT64,
      ::google::protobuf::internal::WireFormatLite::TYPE_SINT64,
      0 >
      TestMap_MapSint64Sint64Entry;
  ::google::protobuf::internal::MapField<
      ::google::protobuf::int64, ::google::protobuf::int64,
      ::google::protobuf::internal::WireFormatLite::TYPE_SINT64,
      ::google::protobuf::internal::WireFormatLite::TYPE_SINT64,
      0 > map_sint64_sint64_;
  typedef ::google::protobuf::internal::MapEntryLite<
      ::google::protobuf::uint32, ::google::protobuf::uint32,
      ::google::protobuf::internal::WireFormatLite::TYPE_FIXED32,
      ::google::protobuf::internal::WireFormatLite::TYPE_FIXED32,
      0 >
      TestMap_MapFixed32Fixed32Entry;
  ::google::protobuf::internal::MapField<
      ::google::protobuf::uint32, ::google::protobuf::uint32,
      ::google::protobuf::internal::WireFormatLite::TYPE_FIXED32,
      ::google::protobuf::internal::WireFormatLite::TYPE_FIXED32,
      0 > map_fixed32_fixed32_;
  typedef ::google::protobuf::internal::MapEntryLite<
      ::google::protobuf::uint64, ::google::protobuf::uint64,
      ::google::protobuf::internal::WireFormatLite::TYPE_FIXED64,
      ::google::protobuf::internal::WireFormatLite::TYPE_FIXED64,
      0 >
      TestMap_MapFixed64Fixed64Entry;
  ::google::protobuf::internal::MapField<
      ::google::protobuf::uint64, ::google::protobuf::uint64,
      ::google::protobuf::internal::WireFormatLite::TYPE_FIXED64,
      ::google::protobuf::internal::WireFormatLite::TYPE_FIXED64,
      0 > map_fixed64_fixed64_;
  typedef ::google::protobuf::internal::MapEntryLite<
      ::google::protobuf::int32, ::google::protobuf::int32,
      ::google::protobuf::internal::WireFormatLite::TYPE_SFIXED32,
      ::google::protobuf::internal::WireFormatLite::TYPE_SFIXED32,
      0 >
      TestMap_MapSfixed32Sfixed32Entry;
  ::google::protobuf::internal::MapField<
      ::google::protobuf::int32, ::google::protobuf::int32,
      ::google::protobuf::internal::WireFormatLite::TYPE_SFIXED32,
      ::google::protobuf::internal::WireFormatLite::TYPE_SFIXED32,
      0 > map_sfixed32_sfixed32_;
  typedef ::google::protobuf::internal::MapEntryLite<
      ::google::protobuf::int64, ::google::protobuf::int64,
      ::google::protobuf::internal::WireFormatLite::TYPE_SFIXED64,
      ::google::protobuf::internal::WireFormatLite::TYPE_SFIXED64,
      0 >
      TestMap_MapSfixed64Sfixed64Entry;
  ::google::protobuf::internal::MapField<
      ::google::protobuf::int64, ::google::protobuf::int64,
      ::google::protobuf::internal::WireFormatLite::TYPE_SFIXED64,
      ::google::protobuf::internal::WireFormatLite::TYPE_SFIXED64,
      0 > map_sfixed64_sfixed64_;
  typedef ::google::protobuf::internal::MapEntryLite<
      ::google::protobuf::int32, float,
      ::google::protobuf::internal::WireFormatLite::TYPE_INT32,
      ::google::protobuf::internal::WireFormatLite::TYPE_FLOAT,
      0 >
      TestMap_MapInt32FloatEntry;
  ::google::protobuf::internal::MapField<
      ::google::protobuf::int32, float,
      ::google::protobuf::internal::WireFormatLite::TYPE_INT32,
      ::google::protobuf::internal::WireFormatLite::TYPE_FLOAT,
      0 > map_int32_float_;
  typedef ::google::protobuf::internal::MapEntryLite<
      ::google::protobuf::int32, double,
      ::google::protobuf::internal::WireFormatLite::TYPE_INT32,
      ::google::protobuf::internal::WireFormatLite::TYPE_DOUBLE,
      0 >
      TestMap_MapInt32DoubleEntry;
  ::google::protobuf::internal::MapField<
      ::google::protobuf::int32, double,
      ::google::protobuf::internal::WireFormatLite::TYPE_INT32,
      ::google::protobuf::internal::WireFormatLite::TYPE_DOUBLE,
      0 > map_int32_double_;
  typedef ::google::protobuf::internal::MapEntryLite<
      bool, bool,
      ::google::protobuf::internal::WireFormatLite::TYPE_BOOL,
      ::google::protobuf::internal::WireFormatLite::TYPE_BOOL,
      0 >
      TestMap_MapBoolBoolEntry;
  ::google::protobuf::internal::MapField<
      bool, bool,
      ::google::protobuf::internal::WireFormatLite::TYPE_BOOL,
      ::google::protobuf::internal::WireFormatLite::TYPE_BOOL,
      0 > map_bool_bool_;
  typedef ::google::protobuf::internal::MapEntryLite<
      ::std::string, ::std::string,
      ::google::protobuf::internal::WireFormatLite::TYPE_STRING,
      ::google::protobuf::internal::WireFormatLite::TYPE_STRING,
      0 >
      TestMap_MapStringStringEntry;
  ::google::protobuf::internal::MapField<
      ::std::string, ::std::string,
      ::google::protobuf::internal::WireFormatLite::TYPE_STRING,
      ::google::protobuf::internal::WireFormatLite::TYPE_STRING,
      0 > map_string_string_;
  typedef ::google::protobuf::internal::MapEntryLite<
      ::google::protobuf::int32, ::std::string,
      ::google::protobuf::internal::WireFormatLite::TYPE_INT32,
      ::google::protobuf::internal::WireFormatLite::TYPE_BYTES,
      0 >
      TestMap_MapInt32BytesEntry;
  ::google::protobuf::internal::MapField<
      ::google::protobuf::int32, ::std::string,
      ::google::protobuf::internal::WireFormatLite::TYPE_INT32,
      ::google::protobuf::internal::WireFormatLite::TYPE_BYTES,
      0 > map_int32_bytes_;
  typedef ::google::protobuf::internal::MapEntryLite<
      ::google::protobuf::int32, ::protobuf_unittest::MapEnum,
      ::google::protobuf::internal::WireFormatLite::TYPE_INT32,
      ::google::protobuf::internal::WireFormatLite::TYPE_ENUM,
      0 >
      TestMap_MapInt32EnumEntry;
  ::google::protobuf::internal::MapField<
      ::google::protobuf::int32, ::protobuf_unittest::MapEnum,
      ::google::protobuf::internal::WireFormatLite::TYPE_INT32,
      ::google::protobuf::internal::WireFormatLite::TYPE_ENUM,
      0 > map_int32_enum_;
  typedef ::google::protobuf::internal::MapEntryLite<
      ::google::protobuf::int32, ::protobuf_unittest::ForeignMessage,
      ::google::protobuf::internal::WireFormatLite::TYPE_INT32,
      ::google::protobuf::internal::WireFormatLite::TYPE_MESSAGE,
      0 >
      TestMap_MapInt32ForeignMessageEntry;
  ::google::protobuf::internal::MapField<
      ::google::protobuf::int32, ::protobuf_unittest::ForeignMessage,
      ::google::protobuf::internal::WireFormatLite::TYPE_INT32,
      ::google::protobuf::internal::WireFormatLite::TYPE_MESSAGE,
      0 > map_int32_foreign_message_;
  typedef ::google::protobuf::internal::MapEntryLite<
      ::std::string, ::protobuf_unittest::ForeignMessage,
      ::google::protobuf::internal::WireFormatLite::TYPE_STRING,
      ::google::protobuf::internal::WireFormatLite::TYPE_MESSAGE,
      0 >
      TestMap_MapStringForeignMessageEntry;
  ::google::protobuf::internal::MapField<
      ::std::string, ::protobuf_unittest::ForeignMessage,
      ::google::protobuf::internal::WireFormatLite::TYPE_STRING,
      ::google::protobuf::internal::WireFormatLite::TYPE_MESSAGE,
      0 > map_string_foreign_message_;
  mutable int _cached_size_;
  friend void  protobuf_InitDefaults_google_2fprotobuf_2fmap_5funittest_2eproto_impl();
  friend void  protobuf_AddDesc_google_2fprotobuf_2fmap_5funittest_2eproto_impl();
  friend void protobuf_AssignDesc_google_2fprotobuf_2fmap_5funittest_2eproto();
  friend void protobuf_ShutdownFile_google_2fprotobuf_2fmap_5funittest_2eproto();

  void InitAsDefaultInstance();
};
extern ::google::protobuf::internal::ExplicitlyConstructed<TestMap> TestMap_default_instance_;

// -------------------------------------------------------------------

class TestMapSubmessage : public ::google::protobuf::Message /* @@protoc_insertion_point(class_definition:protobuf_unittest.TestMapSubmessage) */ {
 public:
  TestMapSubmessage();
  virtual ~TestMapSubmessage();

  TestMapSubmessage(const TestMapSubmessage& from);

  inline TestMapSubmessage& operator=(const TestMapSubmessage& from) {
    CopyFrom(from);
    return *this;
  }

  inline ::google::protobuf::Arena* GetArena() const { return GetArenaNoVirtual(); }
  inline void* GetMaybeArenaPointer() const {
    return MaybeArenaPtr();
  }
  static const ::google::protobuf::Descriptor* descriptor();
  static const TestMapSubmessage& default_instance();

  static const TestMapSubmessage* internal_default_instance();

  void UnsafeArenaSwap(TestMapSubmessage* other);
  void Swap(TestMapSubmessage* other);

  // implements Message ----------------------------------------------

  inline TestMapSubmessage* New() const { return New(NULL); }

  TestMapSubmessage* New(::google::protobuf::Arena* arena) const;
  void CopyFrom(const ::google::protobuf::Message& from);
  void MergeFrom(const ::google::protobuf::Message& from);
  void CopyFrom(const TestMapSubmessage& from);
  void MergeFrom(const TestMapSubmessage& from);
  void Clear();
  bool IsInitialized() const;

  size_t ByteSizeLong() const;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input);
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const;
  ::google::protobuf::uint8* InternalSerializeWithCachedSizesToArray(
      bool deterministic, ::google::protobuf::uint8* output) const;
  ::google::protobuf::uint8* SerializeWithCachedSizesToArray(::google::protobuf::uint8* output) const {
    return InternalSerializeWithCachedSizesToArray(false, output);
  }
  int GetCachedSize() const { return _cached_size_; }
  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const;
  void InternalSwap(TestMapSubmessage* other);
  void UnsafeMergeFrom(const TestMapSubmessage& from);
  protected:
  explicit TestMapSubmessage(::google::protobuf::Arena* arena);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::google::protobuf::Arena* arena);
  private:
  inline ::google::protobuf::Arena* GetArenaNoVirtual() const {
    return _internal_metadata_.arena();
  }
  inline void* MaybeArenaPtr() const {
    return _internal_metadata_.raw_arena_ptr();
  }
  public:

  ::google::protobuf::Metadata GetMetadata() const;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // optional .protobuf_unittest.TestMap test_map = 1;
  bool has_test_map() const;
  void clear_test_map();
  static const int kTestMapFieldNumber = 1;
  private:
  void _slow_mutable_test_map();
  void _slow_set_allocated_test_map(
      ::google::protobuf::Arena* message_arena, ::protobuf_unittest::TestMap** test_map);
  ::protobuf_unittest::TestMap* _slow_release_test_map();
  public:
  const ::protobuf_unittest::TestMap& test_map() const;
  ::protobuf_unittest::TestMap* mutable_test_map();
  ::protobuf_unittest::TestMap* release_test_map();
  void set_allocated_test_map(::protobuf_unittest::TestMap* test_map);
  ::protobuf_unittest::TestMap* unsafe_arena_release_test_map();
  void unsafe_arena_set_allocated_test_map(
      ::protobuf_unittest::TestMap* test_map);

  // @@protoc_insertion_point(class_scope:protobuf_unittest.TestMapSubmessage)
 private:

  ::google::protobuf::internal::InternalMetadataWithArena _internal_metadata_;
  friend class ::google::protobuf::Arena;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  ::protobuf_unittest::TestMap* test_map_;
  mutable int _cached_size_;
  friend void  protobuf_InitDefaults_google_2fprotobuf_2fmap_5funittest_2eproto_impl();
  friend void  protobuf_AddDesc_google_2fprotobuf_2fmap_5funittest_2eproto_impl();
  friend void protobuf_AssignDesc_google_2fprotobuf_2fmap_5funittest_2eproto();
  friend void protobuf_ShutdownFile_google_2fprotobuf_2fmap_5funittest_2eproto();

  void InitAsDefaultInstance();
};
extern ::google::protobuf::internal::ExplicitlyConstructed<TestMapSubmessage> TestMapSubmessage_default_instance_;

// -------------------------------------------------------------------

class TestMessageMap : public ::google::protobuf::Message /* @@protoc_insertion_point(class_definition:protobuf_unittest.TestMessageMap) */ {
 public:
  TestMessageMap();
  virtual ~TestMessageMap();

  TestMessageMap(const TestMessageMap& from);

  inline TestMessageMap& operator=(const TestMessageMap& from) {
    CopyFrom(from);
    return *this;
  }

  inline ::google::protobuf::Arena* GetArena() const { return GetArenaNoVirtual(); }
  inline void* GetMaybeArenaPointer() const {
    return MaybeArenaPtr();
  }
  static const ::google::protobuf::Descriptor* descriptor();
  static const TestMessageMap& default_instance();

  static const TestMessageMap* internal_default_instance();

  void UnsafeArenaSwap(TestMessageMap* other);
  void Swap(TestMessageMap* other);

  // implements Message ----------------------------------------------

  inline TestMessageMap* New() const { return New(NULL); }

  TestMessageMap* New(::google::protobuf::Arena* arena) const;
  void CopyFrom(const ::google::protobuf::Message& from);
  void MergeFrom(const ::google::protobuf::Message& from);
  void CopyFrom(const TestMessageMap& from);
  void MergeFrom(const TestMessageMap& from);
  void Clear();
  bool IsInitialized() const;

  size_t ByteSizeLong() const;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input);
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const;
  ::google::protobuf::uint8* InternalSerializeWithCachedSizesToArray(
      bool deterministic, ::google::protobuf::uint8* output) const;
  ::google::protobuf::uint8* SerializeWithCachedSizesToArray(::google::protobuf::uint8* output) const {
    return InternalSerializeWithCachedSizesToArray(false, output);
  }
  int GetCachedSize() const { return _cached_size_; }
  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const;
  void InternalSwap(TestMessageMap* other);
  void UnsafeMergeFrom(const TestMessageMap& from);
  protected:
  explicit TestMessageMap(::google::protobuf::Arena* arena);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::google::protobuf::Arena* arena);
  private:
  inline ::google::protobuf::Arena* GetArenaNoVirtual() const {
    return _internal_metadata_.arena();
  }
  inline void* MaybeArenaPtr() const {
    return _internal_metadata_.raw_arena_ptr();
  }
  public:

  ::google::protobuf::Metadata GetMetadata() const;

  // nested types ----------------------------------------------------


  // accessors -------------------------------------------------------

  // map<int32, .protobuf_unittest.TestAllTypes> map_int32_message = 1;
  int map_int32_message_size() const;
  void clear_map_int32_message();
  static const int kMapInt32MessageFieldNumber = 1;
  const ::google::protobuf::Map< ::google::protobuf::int32, ::protobuf_unittest::TestAllTypes >&
      map_int32_message() const;
  ::google::protobuf::Map< ::google::protobuf::int32, ::protobuf_unittest::TestAllTypes >*
      mutable_map_int32_message();

  // @@protoc_insertion_point(class_scope:protobuf_unittest.TestMessageMap)
 private:

  ::google::protobuf::internal::InternalMetadataWithArena _internal_metadata_;
  friend class ::google::protobuf::Arena;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  typedef ::google::protobuf::internal::MapEntryLite<
      ::google::protobuf::int32, ::protobuf_unittest::TestAllTypes,
      ::google::protobuf::internal::WireFormatLite::TYPE_INT32,
      ::google::protobuf::internal::WireFormatLite::TYPE_MESSAGE,
      0 >
      TestMessageMap_MapInt32MessageEntry;
  ::google::protobuf::internal::MapField<
      ::google::protobuf::int32, ::protobuf_unittest::TestAllTypes,
      ::google::protobuf::internal::WireFormatLite::TYPE_INT32,
      ::google::protobuf::internal::WireFormatLite::TYPE_MESSAGE,
      0 > map_int32_message_;
  mutable int _cached_size_;
  friend void  protobuf_InitDefaults_google_2fprotobuf_2fmap_5funittest_2eproto_impl();
  friend void  protobuf_AddDesc_google_2fprotobuf_2fmap_5funittest_2eproto_impl();
  friend void protobuf_AssignDesc_google_2fprotobuf_2fmap_5funittest_2eproto();
  friend void protobuf_ShutdownFile_google_2fprotobuf_2fmap_5funittest_2eproto();

  void InitAsDefaultInstance();
};
extern ::google::protobuf::internal::ExplicitlyConstructed<TestMessageMap> TestMessageMap_default_instance_;

// -------------------------------------------------------------------

class TestSameTypeMap : public ::google::protobuf::Message /* @@protoc_insertion_point(class_definition:protobuf_unittest.TestSameTypeMap) */ {
 public:
  TestSameTypeMap();
  virtual ~TestSameTypeMap();

  TestSameTypeMap(const TestSameTypeMap& from);

  inline TestSameTypeMap& operator=(const TestSameTypeMap& from) {
    CopyFrom(from);
    return *this;
  }

  inline ::google::protobuf::Arena* GetArena() const { return GetArenaNoVirtual(); }
  inline void* GetMaybeArenaPointer() const {
    return MaybeArenaPtr();
  }
  static const ::google::protobuf::Descriptor* descriptor();
  static const TestSameTypeMap& default_instance();

  static const TestSameTypeMap* internal_default_instance();

  void UnsafeArenaSwap(TestSameTypeMap* other);
  void Swap(TestSameTypeMap* other);

  // implements Message ----------------------------------------------

  inline TestSameTypeMap* New() const { return New(NULL); }

  TestSameTypeMap* New(::google::protobuf::Arena* arena) const;
  void CopyFrom(const ::google::protobuf::Message& from);
  void MergeFrom(const ::google::protobuf::Message& from);
  void CopyFrom(const TestSameTypeMap& from);
  void MergeFrom(const TestSameTypeMap& from);
  void Clear();
  bool IsInitialized() const;

  size_t ByteSizeLong() const;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input);
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const;
  ::google::protobuf::uint8* InternalSerializeWithCachedSizesToArray(
      bool deterministic, ::google::protobuf::uint8* output) const;
  ::google::protobuf::uint8* SerializeWithCachedSizesToArray(::google::protobuf::uint8* output) const {
    return InternalSerializeWithCachedSizesToArray(false, output);
  }
  int GetCachedSize() const { return _cached_size_; }
  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const;
  void InternalSwap(TestSameTypeMap* other);
  void UnsafeMergeFrom(const TestSameTypeMap& from);
  protected:
  explicit TestSameTypeMap(::google::protobuf::Arena* arena);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::google::protobuf::Arena* arena);
  private:
  inline ::google::protobuf::Arena* GetArenaNoVirtual() const {
    return _internal_metadata_.arena();
  }
  inline void* MaybeArenaPtr() const {
    return _internal_metadata_.raw_arena_ptr();
  }
  public:

  ::google::protobuf::Metadata GetMetadata() const;

  // nested types ----------------------------------------------------


  // accessors -------------------------------------------------------

  // map<int32, int32> map1 = 1;
  int map1_size() const;
  void clear_map1();
  static const int kMap1FieldNumber = 1;
  const ::google::protobuf::Map< ::google::protobuf::int32, ::google::protobuf::int32 >&
      map1() const;
  ::google::protobuf::Map< ::google::protobuf::int32, ::google::protobuf::int32 >*
      mutable_map1();

  // map<int32, int32> map2 = 2;
  int map2_size() const;
  void clear_map2();
  static const int kMap2FieldNumber = 2;
  const ::google::protobuf::Map< ::google::protobuf::int32, ::google::protobuf::int32 >&
      map2() const;
  ::google::protobuf::Map< ::google::protobuf::int32, ::google::protobuf::int32 >*
      mutable_map2();

  // @@protoc_insertion_point(class_scope:protobuf_unittest.TestSameTypeMap)
 private:

  ::google::protobuf::internal::InternalMetadataWithArena _internal_metadata_;
  friend class ::google::protobuf::Arena;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  typedef ::google::protobuf::internal::MapEntryLite<
      ::google::protobuf::int32, ::google::protobuf::int32,
      ::google::protobuf::internal::WireFormatLite::TYPE_INT32,
      ::google::protobuf::internal::WireFormatLite::TYPE_INT32,
      0 >
      TestSameTypeMap_Map1Entry;
  ::google::protobuf::internal::MapField<
      ::google::protobuf::int32, ::google::protobuf::int32,
      ::google::protobuf::internal::WireFormatLite::TYPE_INT32,
      ::google::protobuf::internal::WireFormatLite::TYPE_INT32,
      0 > map1_;
  typedef ::google::protobuf::internal::MapEntryLite<
      ::google::protobuf::int32, ::google::protobuf::int32,
      ::google::protobuf::internal::WireFormatLite::TYPE_INT32,
      ::google::protobuf::internal::WireFormatLite::TYPE_INT32,
      0 >
      TestSameTypeMap_Map2Entry;
  ::google::protobuf::internal::MapField<
      ::google::protobuf::int32, ::google::protobuf::int32,
      ::google::protobuf::internal::WireFormatLite::TYPE_INT32,
      ::google::protobuf::internal::WireFormatLite::TYPE_INT32,
      0 > map2_;
  mutable int _cached_size_;
  friend void  protobuf_InitDefaults_google_2fprotobuf_2fmap_5funittest_2eproto_impl();
  friend void  protobuf_AddDesc_google_2fprotobuf_2fmap_5funittest_2eproto_impl();
  friend void protobuf_AssignDesc_google_2fprotobuf_2fmap_5funittest_2eproto();
  friend void protobuf_ShutdownFile_google_2fprotobuf_2fmap_5funittest_2eproto();

  void InitAsDefaultInstance();
};
extern ::google::protobuf::internal::ExplicitlyConstructed<TestSameTypeMap> TestSameTypeMap_default_instance_;

// -------------------------------------------------------------------

class TestRequiredMessageMap : public ::google::protobuf::Message /* @@protoc_insertion_point(class_definition:protobuf_unittest.TestRequiredMessageMap) */ {
 public:
  TestRequiredMessageMap();
  virtual ~TestRequiredMessageMap();

  TestRequiredMessageMap(const TestRequiredMessageMap& from);

  inline TestRequiredMessageMap& operator=(const TestRequiredMessageMap& from) {
    CopyFrom(from);
    return *this;
  }

  inline ::google::protobuf::Arena* GetArena() const { return GetArenaNoVirtual(); }
  inline void* GetMaybeArenaPointer() const {
    return MaybeArenaPtr();
  }
  static const ::google::protobuf::Descriptor* descriptor();
  static const TestRequiredMessageMap& default_instance();

  static const TestRequiredMessageMap* internal_default_instance();

  void UnsafeArenaSwap(TestRequiredMessageMap* other);
  void Swap(TestRequiredMessageMap* other);

  // implements Message ----------------------------------------------

  inline TestRequiredMessageMap* New() const { return New(NULL); }

  TestRequiredMessageMap* New(::google::protobuf::Arena* arena) const;
  void CopyFrom(const ::google::protobuf::Message& from);
  void MergeFrom(const ::google::protobuf::Message& from);
  void CopyFrom(const TestRequiredMessageMap& from);
  void MergeFrom(const TestRequiredMessageMap& from);
  void Clear();
  bool IsInitialized() const;

  size_t ByteSizeLong() const;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input);
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const;
  ::google::protobuf::uint8* InternalSerializeWithCachedSizesToArray(
      bool deterministic, ::google::protobuf::uint8* output) const;
  ::google::protobuf::uint8* SerializeWithCachedSizesToArray(::google::protobuf::uint8* output) const {
    return InternalSerializeWithCachedSizesToArray(false, output);
  }
  int GetCachedSize() const { return _cached_size_; }
  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const;
  void InternalSwap(TestRequiredMessageMap* other);
  void UnsafeMergeFrom(const TestRequiredMessageMap& from);
  protected:
  explicit TestRequiredMessageMap(::google::protobuf::Arena* arena);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::google::protobuf::Arena* arena);
  private:
  inline ::google::protobuf::Arena* GetArenaNoVirtual() const {
    return _internal_metadata_.arena();
  }
  inline void* MaybeArenaPtr() const {
    return _internal_metadata_.raw_arena_ptr();
  }
  public:

  ::google::protobuf::Metadata GetMetadata() const;

  // nested types ----------------------------------------------------


  // accessors -------------------------------------------------------

  // map<int32, .protobuf_unittest.TestRequired> map_field = 1;
  int map_field_size() const;
  void clear_map_field();
  static const int kMapFieldFieldNumber = 1;
  const ::google::protobuf::Map< ::google::protobuf::int32, ::protobuf_unittest::TestRequired >&
      map_field() const;
  ::google::protobuf::Map< ::google::protobuf::int32, ::protobuf_unittest::TestRequired >*
      mutable_map_field();

  // @@protoc_insertion_point(class_scope:protobuf_unittest.TestRequiredMessageMap)
 private:

  ::google::protobuf::internal::InternalMetadataWithArena _internal_metadata_;
  friend class ::google::protobuf::Arena;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  typedef ::google::protobuf::internal::MapEntryLite<
      ::google::protobuf::int32, ::protobuf_unittest::TestRequired,
      ::google::protobuf::internal::WireFormatLite::TYPE_INT32,
      ::google::protobuf::internal::WireFormatLite::TYPE_MESSAGE,
      0 >
      TestRequiredMessageMap_MapFieldEntry;
  ::google::protobuf::internal::MapField<
      ::google::protobuf::int32, ::protobuf_unittest::TestRequired,
      ::google::protobuf::internal::WireFormatLite::TYPE_INT32,
      ::google::protobuf::internal::WireFormatLite::TYPE_MESSAGE,
      0 > map_field_;
  mutable int _cached_size_;
  friend void  protobuf_InitDefaults_google_2fprotobuf_2fmap_5funittest_2eproto_impl();
  friend void  protobuf_AddDesc_google_2fprotobuf_2fmap_5funittest_2eproto_impl();
  friend void protobuf_AssignDesc_google_2fprotobuf_2fmap_5funittest_2eproto();
  friend void protobuf_ShutdownFile_google_2fprotobuf_2fmap_5funittest_2eproto();

  void InitAsDefaultInstance();
};
extern ::google::protobuf::internal::ExplicitlyConstructed<TestRequiredMessageMap> TestRequiredMessageMap_default_instance_;

// -------------------------------------------------------------------

class TestArenaMap : public ::google::protobuf::Message /* @@protoc_insertion_point(class_definition:protobuf_unittest.TestArenaMap) */ {
 public:
  TestArenaMap();
  virtual ~TestArenaMap();

  TestArenaMap(const TestArenaMap& from);

  inline TestArenaMap& operator=(const TestArenaMap& from) {
    CopyFrom(from);
    return *this;
  }

  inline ::google::protobuf::Arena* GetArena() const { return GetArenaNoVirtual(); }
  inline void* GetMaybeArenaPointer() const {
    return MaybeArenaPtr();
  }
  static const ::google::protobuf::Descriptor* descriptor();
  static const TestArenaMap& default_instance();

  static const TestArenaMap* internal_default_instance();

  void UnsafeArenaSwap(TestArenaMap* other);
  void Swap(TestArenaMap* other);

  // implements Message ----------------------------------------------

  inline TestArenaMap* New() const { return New(NULL); }

  TestArenaMap* New(::google::protobuf::Arena* arena) const;
  void CopyFrom(const ::google::protobuf::Message& from);
  void MergeFrom(const ::google::protobuf::Message& from);
  void CopyFrom(const TestArenaMap& from);
  void MergeFrom(const TestArenaMap& from);
  void Clear();
  bool IsInitialized() const;

  size_t ByteSizeLong() const;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input);
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const;
  ::google::protobuf::uint8* InternalSerializeWithCachedSizesToArray(
      bool deterministic, ::google::protobuf::uint8* output) const;
  ::google::protobuf::uint8* SerializeWithCachedSizesToArray(::google::protobuf::uint8* output) const {
    return InternalSerializeWithCachedSizesToArray(false, output);
  }
  int GetCachedSize() const { return _cached_size_; }
  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const;
  void InternalSwap(TestArenaMap* other);
  void UnsafeMergeFrom(const TestArenaMap& from);
  protected:
  explicit TestArenaMap(::google::protobuf::Arena* arena);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::google::protobuf::Arena* arena);
  private:
  inline ::google::protobuf::Arena* GetArenaNoVirtual() const {
    return _internal_metadata_.arena();
  }
  inline void* MaybeArenaPtr() const {
    return _internal_metadata_.raw_arena_ptr();
  }
  public:

  ::google::protobuf::Metadata GetMetadata() const;

  // nested types ----------------------------------------------------


  // accessors -------------------------------------------------------

  // map<int32, int32> map_int32_int32 = 1;
  int map_int32_int32_size() const;
  void clear_map_int32_int32();
  static const int kMapInt32Int32FieldNumber = 1;
  const ::google::protobuf::Map< ::google::protobuf::int32, ::google::protobuf::int32 >&
      map_int32_int32() const;
  ::google::protobuf::Map< ::google::protobuf::int32, ::google::protobuf::int32 >*
      mutable_map_int32_int32();

  // map<int64, int64> map_int64_int64 = 2;
  int map_int64_int64_size() const;
  void clear_map_int64_int64();
  static const int kMapInt64Int64FieldNumber = 2;
  const ::google::protobuf::Map< ::google::protobuf::int64, ::google::protobuf::int64 >&
      map_int64_int64() const;
  ::google::protobuf::Map< ::google::protobuf::int64, ::google::protobuf::int64 >*
      mutable_map_int64_int64();

  // map<uint32, uint32> map_uint32_uint32 = 3;
  int map_uint32_uint32_size() const;
  void clear_map_uint32_uint32();
  static const int kMapUint32Uint32FieldNumber = 3;
  const ::google::protobuf::Map< ::google::protobuf::uint32, ::google::protobuf::uint32 >&
      map_uint32_uint32() const;
  ::google::protobuf::Map< ::google::protobuf::uint32, ::google::protobuf::uint32 >*
      mutable_map_uint32_uint32();

  // map<uint64, uint64> map_uint64_uint64 = 4;
  int map_uint64_uint64_size() const;
  void clear_map_uint64_uint64();
  static const int kMapUint64Uint64FieldNumber = 4;
  const ::google::protobuf::Map< ::google::protobuf::uint64, ::google::protobuf::uint64 >&
      map_uint64_uint64() const;
  ::google::protobuf::Map< ::google::protobuf::uint64, ::google::protobuf::uint64 >*
      mutable_map_uint64_uint64();

  // map<sint32, sint32> map_sint32_sint32 = 5;
  int map_sint32_sint32_size() const;
  void clear_map_sint32_sint32();
  static const int kMapSint32Sint32FieldNumber = 5;
  const ::google::protobuf::Map< ::google::protobuf::int32, ::google::protobuf::int32 >&
      map_sint32_sint32() const;
  ::google::protobuf::Map< ::google::protobuf::int32, ::google::protobuf::int32 >*
      mutable_map_sint32_sint32();

  // map<sint64, sint64> map_sint64_sint64 = 6;
  int map_sint64_sint64_size() const;
  void clear_map_sint64_sint64();
  static const int kMapSint64Sint64FieldNumber = 6;
  const ::google::protobuf::Map< ::google::protobuf::int64, ::google::protobuf::int64 >&
      map_sint64_sint64() const;
  ::google::protobuf::Map< ::google::protobuf::int64, ::google::protobuf::int64 >*
      mutable_map_sint64_sint64();

  // map<fixed32, fixed32> map_fixed32_fixed32 = 7;
  int map_fixed32_fixed32_size() const;
  void clear_map_fixed32_fixed32();
  static const int kMapFixed32Fixed32FieldNumber = 7;
  const ::google::protobuf::Map< ::google::protobuf::uint32, ::google::protobuf::uint32 >&
      map_fixed32_fixed32() const;
  ::google::protobuf::Map< ::google::protobuf::uint32, ::google::protobuf::uint32 >*
      mutable_map_fixed32_fixed32();

  // map<fixed64, fixed64> map_fixed64_fixed64 = 8;
  int map_fixed64_fixed64_size() const;
  void clear_map_fixed64_fixed64();
  static const int kMapFixed64Fixed64FieldNumber = 8;
  const ::google::protobuf::Map< ::google::protobuf::uint64, ::google::protobuf::uint64 >&
      map_fixed64_fixed64() const;
  ::google::protobuf::Map< ::google::protobuf::uint64, ::google::protobuf::uint64 >*
      mutable_map_fixed64_fixed64();

  // map<sfixed32, sfixed32> map_sfixed32_sfixed32 = 9;
  int map_sfixed32_sfixed32_size() const;
  void clear_map_sfixed32_sfixed32();
  static const int kMapSfixed32Sfixed32FieldNumber = 9;
  const ::google::protobuf::Map< ::google::protobuf::int32, ::google::protobuf::int32 >&
      map_sfixed32_sfixed32() const;
  ::google::protobuf::Map< ::google::protobuf::int32, ::google::protobuf::int32 >*
      mutable_map_sfixed32_sfixed32();

  // map<sfixed64, sfixed64> map_sfixed64_sfixed64 = 10;
  int map_sfixed64_sfixed64_size() const;
  void clear_map_sfixed64_sfixed64();
  static const int kMapSfixed64Sfixed64FieldNumber = 10;
  const ::google::protobuf::Map< ::google::protobuf::int64, ::google::protobuf::int64 >&
      map_sfixed64_sfixed64() const;
  ::google::protobuf::Map< ::google::protobuf::int64, ::google::protobuf::int64 >*
      mutable_map_sfixed64_sfixed64();

  // map<int32, float> map_int32_float = 11;
  int map_int32_float_size() const;
  void clear_map_int32_float();
  static const int kMapInt32FloatFieldNumber = 11;
  const ::google::protobuf::Map< ::google::protobuf::int32, float >&
      map_int32_float() const;
  ::google::protobuf::Map< ::google::protobuf::int32, float >*
      mutable_map_int32_float();

  // map<int32, double> map_int32_double = 12;
  int map_int32_double_size() const;
  void clear_map_int32_double();
  static const int kMapInt32DoubleFieldNumber = 12;
  const ::google::protobuf::Map< ::google::protobuf::int32, double >&
      map_int32_double() const;
  ::google::protobuf::Map< ::google::protobuf::int32, double >*
      mutable_map_int32_double();

  // map<bool, bool> map_bool_bool = 13;
  int map_bool_bool_size() const;
  void clear_map_bool_bool();
  static const int kMapBoolBoolFieldNumber = 13;
  const ::google::protobuf::Map< bool, bool >&
      map_bool_bool() const;
  ::google::protobuf::Map< bool, bool >*
      mutable_map_bool_bool();

  // map<string, string> map_string_string = 14;
  int map_string_string_size() const;
  void clear_map_string_string();
  static const int kMapStringStringFieldNumber = 14;
  const ::google::protobuf::Map< ::std::string, ::std::string >&
      map_string_string() const;
  ::google::protobuf::Map< ::std::string, ::std::string >*
      mutable_map_string_string();

  // map<int32, bytes> map_int32_bytes = 15;
  int map_int32_bytes_size() const;
  void clear_map_int32_bytes();
  static const int kMapInt32BytesFieldNumber = 15;
  const ::google::protobuf::Map< ::google::protobuf::int32, ::std::string >&
      map_int32_bytes() const;
  ::google::protobuf::Map< ::google::protobuf::int32, ::std::string >*
      mutable_map_int32_bytes();

  // map<int32, .protobuf_unittest.MapEnum> map_int32_enum = 16;
  int map_int32_enum_size() const;
  void clear_map_int32_enum();
  static const int kMapInt32EnumFieldNumber = 16;
  const ::google::protobuf::Map< ::google::protobuf::int32, ::protobuf_unittest::MapEnum >&
      map_int32_enum() const;
  ::google::protobuf::Map< ::google::protobuf::int32, ::protobuf_unittest::MapEnum >*
      mutable_map_int32_enum();

  // map<int32, .protobuf_unittest.ForeignMessage> map_int32_foreign_message = 17;
  int map_int32_foreign_message_size() const;
  void clear_map_int32_foreign_message();
  static const int kMapInt32ForeignMessageFieldNumber = 17;
  const ::google::protobuf::Map< ::google::protobuf::int32, ::protobuf_unittest::ForeignMessage >&
      map_int32_foreign_message() const;
  ::google::protobuf::Map< ::google::protobuf::int32, ::protobuf_unittest::ForeignMessage >*
      mutable_map_int32_foreign_message();

  // map<int32, .protobuf_unittest_no_arena.ForeignMessage> map_int32_foreign_message_no_arena = 18;
  int map_int32_foreign_message_no_arena_size() const;
  void clear_map_int32_foreign_message_no_arena();
  static const int kMapInt32ForeignMessageNoArenaFieldNumber = 18;
  const ::google::protobuf::Map< ::google::protobuf::int32, ::protobuf_unittest_no_arena::ForeignMessage >&
      map_int32_foreign_message_no_arena() const;
  ::google::protobuf::Map< ::google::protobuf::int32, ::protobuf_unittest_no_arena::ForeignMessage >*
      mutable_map_int32_foreign_message_no_arena();

  // @@protoc_insertion_point(class_scope:protobuf_unittest.TestArenaMap)
 private:

  ::google::protobuf::internal::InternalMetadataWithArena _internal_metadata_;
  friend class ::google::protobuf::Arena;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  typedef ::google::protobuf::internal::MapEntryLite<
      ::google::protobuf::int32, ::google::protobuf::int32,
      ::google::protobuf::internal::WireFormatLite::TYPE_INT32,
      ::google::protobuf::internal::WireFormatLite::TYPE_INT32,
      0 >
      TestArenaMap_MapInt32Int32Entry;
  ::google::protobuf::internal::MapField<
      ::google::protobuf::int32, ::google::protobuf::int32,
      ::google::protobuf::internal::WireFormatLite::TYPE_INT32,
      ::google::protobuf::internal::WireFormatLite::TYPE_INT32,
      0 > map_int32_int32_;
  typedef ::google::protobuf::internal::MapEntryLite<
      ::google::protobuf::int64, ::google::protobuf::int64,
      ::google::protobuf::internal::WireFormatLite::TYPE_INT64,
      ::google::protobuf::internal::WireFormatLite::TYPE_INT64,
      0 >
      TestArenaMap_MapInt64Int64Entry;
  ::google::protobuf::internal::MapField<
      ::google::protobuf::int64, ::google::protobuf::int64,
      ::google::protobuf::internal::WireFormatLite::TYPE_INT64,
      ::google::protobuf::internal::WireFormatLite::TYPE_INT64,
      0 > map_int64_int64_;
  typedef ::google::protobuf::internal::MapEntryLite<
      ::google::protobuf::uint32, ::google::protobuf::uint32,
      ::google::protobuf::internal::WireFormatLite::TYPE_UINT32,
      ::google::protobuf::internal::WireFormatLite::TYPE_UINT32,
      0 >
      TestArenaMap_MapUint32Uint32Entry;
  ::google::protobuf::internal::MapField<
      ::google::protobuf::uint32, ::google::protobuf::uint32,
      ::google::protobuf::internal::WireFormatLite::TYPE_UINT32,
      ::google::protobuf::internal::WireFormatLite::TYPE_UINT32,
      0 > map_uint32_uint32_;
  typedef ::google::protobuf::internal::MapEntryLite<
      ::google::protobuf::uint64, ::google::protobuf::uint64,
      ::google::protobuf::internal::WireFormatLite::TYPE_UINT64,
      ::google::protobuf::internal::WireFormatLite::TYPE_UINT64,
      0 >
      TestArenaMap_MapUint64Uint64Entry;
  ::google::protobuf::internal::MapField<
      ::google::protobuf::uint64, ::google::protobuf::uint64,
      ::google::protobuf::internal::WireFormatLite::TYPE_UINT64,
      ::google::protobuf::internal::WireFormatLite::TYPE_UINT64,
      0 > map_uint64_uint64_;
  typedef ::google::protobuf::internal::MapEntryLite<
      ::google::protobuf::int32, ::google::protobuf::int32,
      ::google::protobuf::internal::WireFormatLite::TYPE_SINT32,
      ::google::protobuf::internal::WireFormatLite::TYPE_SINT32,
      0 >
      TestArenaMap_MapSint32Sint32Entry;
  ::google::protobuf::internal::MapField<
      ::google::protobuf::int32, ::google::protobuf::int32,
      ::google::protobuf::internal::WireFormatLite::TYPE_SINT32,
      ::google::protobuf::internal::WireFormatLite::TYPE_SINT32,
      0 > map_sint32_sint32_;
  typedef ::google::protobuf::internal::MapEntryLite<
      ::google::protobuf::int64, ::google::protobuf::int64,
      ::google::protobuf::internal::WireFormatLite::TYPE_SINT64,
      ::google::protobuf::internal::WireFormatLite::TYPE_SINT64,
      0 >
      TestArenaMap_MapSint64Sint64Entry;
  ::google::protobuf::internal::MapField<
      ::google::protobuf::int64, ::google::protobuf::int64,
      ::google::protobuf::internal::WireFormatLite::TYPE_SINT64,
      ::google::protobuf::internal::WireFormatLite::TYPE_SINT64,
      0 > map_sint64_sint64_;
  typedef ::google::protobuf::internal::MapEntryLite<
      ::google::protobuf::uint32, ::google::protobuf::uint32,
      ::google::protobuf::internal::WireFormatLite::TYPE_FIXED32,
      ::google::protobuf::internal::WireFormatLite::TYPE_FIXED32,
      0 >
      TestArenaMap_MapFixed32Fixed32Entry;
  ::google::protobuf::internal::MapField<
      ::google::protobuf::uint32, ::google::protobuf::uint32,
      ::google::protobuf::internal::WireFormatLite::TYPE_FIXED32,
      ::google::protobuf::internal::WireFormatLite::TYPE_FIXED32,
      0 > map_fixed32_fixed32_;
  typedef ::google::protobuf::internal::MapEntryLite<
      ::google::protobuf::uint64, ::google::protobuf::uint64,
      ::google::protobuf::internal::WireFormatLite::TYPE_FIXED64,
      ::google::protobuf::internal::WireFormatLite::TYPE_FIXED64,
      0 >
      TestArenaMap_MapFixed64Fixed64Entry;
  ::google::protobuf::internal::MapField<
      ::google::protobuf::uint64, ::google::protobuf::uint64,
      ::google::protobuf::internal::WireFormatLite::TYPE_FIXED64,
      ::google::protobuf::internal::WireFormatLite::TYPE_FIXED64,
      0 > map_fixed64_fixed64_;
  typedef ::google::protobuf::internal::MapEntryLite<
      ::google::protobuf::int32, ::google::protobuf::int32,
      ::google::protobuf::internal::WireFormatLite::TYPE_SFIXED32,
      ::google::protobuf::internal::WireFormatLite::TYPE_SFIXED32,
      0 >
      TestArenaMap_MapSfixed32Sfixed32Entry;
  ::google::protobuf::internal::MapField<
      ::google::protobuf::int32, ::google::protobuf::int32,
      ::google::protobuf::internal::WireFormatLite::TYPE_SFIXED32,
      ::google::protobuf::internal::WireFormatLite::TYPE_SFIXED32,
      0 > map_sfixed32_sfixed32_;
  typedef ::google::protobuf::internal::MapEntryLite<
      ::google::protobuf::int64, ::google::protobuf::int64,
      ::google::protobuf::internal::WireFormatLite::TYPE_SFIXED64,
      ::google::protobuf::internal::WireFormatLite::TYPE_SFIXED64,
      0 >
      TestArenaMap_MapSfixed64Sfixed64Entry;
  ::google::protobuf::internal::MapField<
      ::google::protobuf::int64, ::google::protobuf::int64,
      ::google::protobuf::internal::WireFormatLite::TYPE_SFIXED64,
      ::google::protobuf::internal::WireFormatLite::TYPE_SFIXED64,
      0 > map_sfixed64_sfixed64_;
  typedef ::google::protobuf::internal::MapEntryLite<
      ::google::protobuf::int32, float,
      ::google::protobuf::internal::WireFormatLite::TYPE_INT32,
      ::google::protobuf::internal::WireFormatLite::TYPE_FLOAT,
      0 >
      TestArenaMap_MapInt32FloatEntry;
  ::google::protobuf::internal::MapField<
      ::google::protobuf::int32, float,
      ::google::protobuf::internal::WireFormatLite::TYPE_INT32,
      ::google::protobuf::internal::WireFormatLite::TYPE_FLOAT,
      0 > map_int32_float_;
  typedef ::google::protobuf::internal::MapEntryLite<
      ::google::protobuf::int32, double,
      ::google::protobuf::internal::WireFormatLite::TYPE_INT32,
      ::google::protobuf::internal::WireFormatLite::TYPE_DOUBLE,
      0 >
      TestArenaMap_MapInt32DoubleEntry;
  ::google::protobuf::internal::MapField<
      ::google::protobuf::int32, double,
      ::google::protobuf::internal::WireFormatLite::TYPE_INT32,
      ::google::protobuf::internal::WireFormatLite::TYPE_DOUBLE,
      0 > map_int32_double_;
  typedef ::google::protobuf::internal::MapEntryLite<
      bool, bool,
      ::google::protobuf::internal::WireFormatLite::TYPE_BOOL,
      ::google::protobuf::internal::WireFormatLite::TYPE_BOOL,
      0 >
      TestArenaMap_MapBoolBoolEntry;
  ::google::protobuf::internal::MapField<
      bool, bool,
      ::google::protobuf::internal::WireFormatLite::TYPE_BOOL,
      ::google::protobuf::internal::WireFormatLite::TYPE_BOOL,
      0 > map_bool_bool_;
  typedef ::google::protobuf::internal::MapEntryLite<
      ::std::string, ::std::string,
      ::google::protobuf::internal::WireFormatLite::TYPE_STRING,
      ::google::protobuf::internal::WireFormatLite::TYPE_STRING,
      0 >
      TestArenaMap_MapStringStringEntry;
  ::google::protobuf::internal::MapField<
      ::std::string, ::std::string,
      ::google::protobuf::internal::WireFormatLite::TYPE_STRING,
      ::google::protobuf::internal::WireFormatLite::TYPE_STRING,
      0 > map_string_string_;
  typedef ::google::protobuf::internal::MapEntryLite<
      ::google::protobuf::int32, ::std::string,
      ::google::protobuf::internal::WireFormatLite::TYPE_INT32,
      ::google::protobuf::internal::WireFormatLite::TYPE_BYTES,
      0 >
      TestArenaMap_MapInt32BytesEntry;
  ::google::protobuf::internal::MapField<
      ::google::protobuf::int32, ::std::string,
      ::google::protobuf::internal::WireFormatLite::TYPE_INT32,
      ::google::protobuf::internal::WireFormatLite::TYPE_BYTES,
      0 > map_int32_bytes_;
  typedef ::google::protobuf::internal::MapEntryLite<
      ::google::protobuf::int32, ::protobuf_unittest::MapEnum,
      ::google::protobuf::internal::WireFormatLite::TYPE_INT32,
      ::google::protobuf::internal::WireFormatLite::TYPE_ENUM,
      0 >
      TestArenaMap_MapInt32EnumEntry;
  ::google::protobuf::internal::MapField<
      ::google::protobuf::int32, ::protobuf_unittest::MapEnum,
      ::google::protobuf::internal::WireFormatLite::TYPE_INT32,
      ::google::protobuf::internal::WireFormatLite::TYPE_ENUM,
      0 > map_int32_enum_;
  typedef ::google::protobuf::internal::MapEntryLite<
      ::google::protobuf::int32, ::protobuf_unittest::ForeignMessage,
      ::google::protobuf::internal::WireFormatLite::TYPE_INT32,
      ::google::protobuf::internal::WireFormatLite::TYPE_MESSAGE,
      0 >
      TestArenaMap_MapInt32ForeignMessageEntry;
  ::google::protobuf::internal::MapField<
      ::google::protobuf::int32, ::protobuf_unittest::ForeignMessage,
      ::google::protobuf::internal::WireFormatLite::TYPE_INT32,
      ::google::protobuf::internal::WireFormatLite::TYPE_MESSAGE,
      0 > map_int32_foreign_message_;
  typedef ::google::protobuf::internal::MapEntryLite<
      ::google::protobuf::int32, ::protobuf_unittest_no_arena::ForeignMessage,
      ::google::protobuf::internal::WireFormatLite::TYPE_INT32,
      ::google::protobuf::internal::WireFormatLite::TYPE_MESSAGE,
      0 >
      TestArenaMap_MapInt32ForeignMessageNoArenaEntry;
  ::google::protobuf::internal::MapField<
      ::google::protobuf::int32, ::protobuf_unittest_no_arena::ForeignMessage,
      ::google::protobuf::internal::WireFormatLite::TYPE_INT32,
      ::google::protobuf::internal::WireFormatLite::TYPE_MESSAGE,
      0 > map_int32_foreign_message_no_arena_;
  mutable int _cached_size_;
  friend void  protobuf_InitDefaults_google_2fprotobuf_2fmap_5funittest_2eproto_impl();
  friend void  protobuf_AddDesc_google_2fprotobuf_2fmap_5funittest_2eproto_impl();
  friend void protobuf_AssignDesc_google_2fprotobuf_2fmap_5funittest_2eproto();
  friend void protobuf_ShutdownFile_google_2fprotobuf_2fmap_5funittest_2eproto();

  void InitAsDefaultInstance();
};
extern ::google::protobuf::internal::ExplicitlyConstructed<TestArenaMap> TestArenaMap_default_instance_;

// -------------------------------------------------------------------

class MessageContainingEnumCalledType : public ::google::protobuf::Message /* @@protoc_insertion_point(class_definition:protobuf_unittest.MessageContainingEnumCalledType) */ {
 public:
  MessageContainingEnumCalledType();
  virtual ~MessageContainingEnumCalledType();

  MessageContainingEnumCalledType(const MessageContainingEnumCalledType& from);

  inline MessageContainingEnumCalledType& operator=(const MessageContainingEnumCalledType& from) {
    CopyFrom(from);
    return *this;
  }

  inline ::google::protobuf::Arena* GetArena() const { return GetArenaNoVirtual(); }
  inline void* GetMaybeArenaPointer() const {
    return MaybeArenaPtr();
  }
  static const ::google::protobuf::Descriptor* descriptor();
  static const MessageContainingEnumCalledType& default_instance();

  static const MessageContainingEnumCalledType* internal_default_instance();

  void UnsafeArenaSwap(MessageContainingEnumCalledType* other);
  void Swap(MessageContainingEnumCalledType* other);

  // implements Message ----------------------------------------------

  inline MessageContainingEnumCalledType* New() const { return New(NULL); }

  MessageContainingEnumCalledType* New(::google::protobuf::Arena* arena) const;
  void CopyFrom(const ::google::protobuf::Message& from);
  void MergeFrom(const ::google::protobuf::Message& from);
  void CopyFrom(const MessageContainingEnumCalledType& from);
  void MergeFrom(const MessageContainingEnumCalledType& from);
  void Clear();
  bool IsInitialized() const;

  size_t ByteSizeLong() const;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input);
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const;
  ::google::protobuf::uint8* InternalSerializeWithCachedSizesToArray(
      bool deterministic, ::google::protobuf::uint8* output) const;
  ::google::protobuf::uint8* SerializeWithCachedSizesToArray(::google::protobuf::uint8* output) const {
    return InternalSerializeWithCachedSizesToArray(false, output);
  }
  int GetCachedSize() const { return _cached_size_; }
  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const;
  void InternalSwap(MessageContainingEnumCalledType* other);
  void UnsafeMergeFrom(const MessageContainingEnumCalledType& from);
  protected:
  explicit MessageContainingEnumCalledType(::google::protobuf::Arena* arena);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::google::protobuf::Arena* arena);
  private:
  inline ::google::protobuf::Arena* GetArenaNoVirtual() const {
    return _internal_metadata_.arena();
  }
  inline void* MaybeArenaPtr() const {
    return _internal_metadata_.raw_arena_ptr();
  }
  public:

  ::google::protobuf::Metadata GetMetadata() const;

  // nested types ----------------------------------------------------


  typedef MessageContainingEnumCalledType_Type Type;
  static const Type TYPE_FOO =
    MessageContainingEnumCalledType_Type_TYPE_FOO;
  static inline bool Type_IsValid(int value) {
    return MessageContainingEnumCalledType_Type_IsValid(value);
  }
  static const Type Type_MIN =
    MessageContainingEnumCalledType_Type_Type_MIN;
  static const Type Type_MAX =
    MessageContainingEnumCalledType_Type_Type_MAX;
  static const int Type_ARRAYSIZE =
    MessageContainingEnumCalledType_Type_Type_ARRAYSIZE;
  static inline const ::google::protobuf::EnumDescriptor*
  Type_descriptor() {
    return MessageContainingEnumCalledType_Type_descriptor();
  }
  static inline const ::std::string& Type_Name(Type value) {
    return MessageContainingEnumCalledType_Type_Name(value);
  }
  static inline bool Type_Parse(const ::std::string& name,
      Type* value) {
    return MessageContainingEnumCalledType_Type_Parse(name, value);
  }

  // accessors -------------------------------------------------------

  // map<string, .protobuf_unittest.MessageContainingEnumCalledType> type = 1;
  int type_size() const;
  void clear_type();
  static const int kTypeFieldNumber = 1;
  const ::google::protobuf::Map< ::std::string, ::protobuf_unittest::MessageContainingEnumCalledType >&
      type() const;
  ::google::protobuf::Map< ::std::string, ::protobuf_unittest::MessageContainingEnumCalledType >*
      mutable_type();

  // @@protoc_insertion_point(class_scope:protobuf_unittest.MessageContainingEnumCalledType)
 private:

  ::google::protobuf::internal::InternalMetadataWithArena _internal_metadata_;
  friend class ::google::protobuf::Arena;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  typedef ::google::protobuf::internal::MapEntryLite<
      ::std::string, ::protobuf_unittest::MessageContainingEnumCalledType,
      ::google::protobuf::internal::WireFormatLite::TYPE_STRING,
      ::google::protobuf::internal::WireFormatLite::TYPE_MESSAGE,
      0 >
      MessageContainingEnumCalledType_TypeEntry;
  ::google::protobuf::internal::MapField<
      ::std::string, ::protobuf_unittest::MessageContainingEnumCalledType,
      ::google::protobuf::internal::WireFormatLite::TYPE_STRING,
      ::google::protobuf::internal::WireFormatLite::TYPE_MESSAGE,
      0 > type_;
  mutable int _cached_size_;
  friend void  protobuf_InitDefaults_google_2fprotobuf_2fmap_5funittest_2eproto_impl();
  friend void  protobuf_AddDesc_google_2fprotobuf_2fmap_5funittest_2eproto_impl();
  friend void protobuf_AssignDesc_google_2fprotobuf_2fmap_5funittest_2eproto();
  friend void protobuf_ShutdownFile_google_2fprotobuf_2fmap_5funittest_2eproto();

  void InitAsDefaultInstance();
};
extern ::google::protobuf::internal::ExplicitlyConstructed<MessageContainingEnumCalledType> MessageContainingEnumCalledType_default_instance_;

// -------------------------------------------------------------------

class MessageContainingMapCalledEntry : public ::google::protobuf::Message /* @@protoc_insertion_point(class_definition:protobuf_unittest.MessageContainingMapCalledEntry) */ {
 public:
  MessageContainingMapCalledEntry();
  virtual ~MessageContainingMapCalledEntry();

  MessageContainingMapCalledEntry(const MessageContainingMapCalledEntry& from);

  inline MessageContainingMapCalledEntry& operator=(const MessageContainingMapCalledEntry& from) {
    CopyFrom(from);
    return *this;
  }

  inline ::google::protobuf::Arena* GetArena() const { return GetArenaNoVirtual(); }
  inline void* GetMaybeArenaPointer() const {
    return MaybeArenaPtr();
  }
  static const ::google::protobuf::Descriptor* descriptor();
  static const MessageContainingMapCalledEntry& default_instance();

  static const MessageContainingMapCalledEntry* internal_default_instance();

  void UnsafeArenaSwap(MessageContainingMapCalledEntry* other);
  void Swap(MessageContainingMapCalledEntry* other);

  // implements Message ----------------------------------------------

  inline MessageContainingMapCalledEntry* New() const { return New(NULL); }

  MessageContainingMapCalledEntry* New(::google::protobuf::Arena* arena) const;
  void CopyFrom(const ::google::protobuf::Message& from);
  void MergeFrom(const ::google::protobuf::Message& from);
  void CopyFrom(const MessageContainingMapCalledEntry& from);
  void MergeFrom(const MessageContainingMapCalledEntry& from);
  void Clear();
  bool IsInitialized() const;

  size_t ByteSizeLong() const;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input);
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const;
  ::google::protobuf::uint8* InternalSerializeWithCachedSizesToArray(
      bool deterministic, ::google::protobuf::uint8* output) const;
  ::google::protobuf::uint8* SerializeWithCachedSizesToArray(::google::protobuf::uint8* output) const {
    return InternalSerializeWithCachedSizesToArray(false, output);
  }
  int GetCachedSize() const { return _cached_size_; }
  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const;
  void InternalSwap(MessageContainingMapCalledEntry* other);
  void UnsafeMergeFrom(const MessageContainingMapCalledEntry& from);
  protected:
  explicit MessageContainingMapCalledEntry(::google::protobuf::Arena* arena);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::google::protobuf::Arena* arena);
  private:
  inline ::google::protobuf::Arena* GetArenaNoVirtual() const {
    return _internal_metadata_.arena();
  }
  inline void* MaybeArenaPtr() const {
    return _internal_metadata_.raw_arena_ptr();
  }
  public:

  ::google::protobuf::Metadata GetMetadata() const;

  // nested types ----------------------------------------------------


  // accessors -------------------------------------------------------

  // map<int32, int32> entry = 1;
  int entry_size() const;
  void clear_entry();
  static const int kEntryFieldNumber = 1;
  const ::google::protobuf::Map< ::google::protobuf::int32, ::google::protobuf::int32 >&
      entry() const;
  ::google::protobuf::Map< ::google::protobuf::int32, ::google::protobuf::int32 >*
      mutable_entry();

  // @@protoc_insertion_point(class_scope:protobuf_unittest.MessageContainingMapCalledEntry)
 private:

  ::google::protobuf::internal::InternalMetadataWithArena _internal_metadata_;
  friend class ::google::protobuf::Arena;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  typedef ::google::protobuf::internal::MapEntryLite<
      ::google::protobuf::int32, ::google::protobuf::int32,
      ::google::protobuf::internal::WireFormatLite::TYPE_INT32,
      ::google::protobuf::internal::WireFormatLite::TYPE_INT32,
      0 >
      MessageContainingMapCalledEntry_EntryEntry;
  ::google::protobuf::internal::MapField<
      ::google::protobuf::int32, ::google::protobuf::int32,
      ::google::protobuf::internal::WireFormatLite::TYPE_INT32,
      ::google::protobuf::internal::WireFormatLite::TYPE_INT32,
      0 > entry_;
  mutable int _cached_size_;
  friend void  protobuf_InitDefaults_google_2fprotobuf_2fmap_5funittest_2eproto_impl();
  friend void  protobuf_AddDesc_google_2fprotobuf_2fmap_5funittest_2eproto_impl();
  friend void protobuf_AssignDesc_google_2fprotobuf_2fmap_5funittest_2eproto();
  friend void protobuf_ShutdownFile_google_2fprotobuf_2fmap_5funittest_2eproto();

  void InitAsDefaultInstance();
};
extern ::google::protobuf::internal::ExplicitlyConstructed<MessageContainingMapCalledEntry> MessageContainingMapCalledEntry_default_instance_;

// -------------------------------------------------------------------

class TestRecursiveMapMessage : public ::google::protobuf::Message /* @@protoc_insertion_point(class_definition:protobuf_unittest.TestRecursiveMapMessage) */ {
 public:
  TestRecursiveMapMessage();
  virtual ~TestRecursiveMapMessage();

  TestRecursiveMapMessage(const TestRecursiveMapMessage& from);

  inline TestRecursiveMapMessage& operator=(const TestRecursiveMapMessage& from) {
    CopyFrom(from);
    return *this;
  }

  inline ::google::protobuf::Arena* GetArena() const { return GetArenaNoVirtual(); }
  inline void* GetMaybeArenaPointer() const {
    return MaybeArenaPtr();
  }
  static const ::google::protobuf::Descriptor* descriptor();
  static const TestRecursiveMapMessage& default_instance();

  static const TestRecursiveMapMessage* internal_default_instance();

  void UnsafeArenaSwap(TestRecursiveMapMessage* other);
  void Swap(TestRecursiveMapMessage* other);

  // implements Message ----------------------------------------------

  inline TestRecursiveMapMessage* New() const { return New(NULL); }

  TestRecursiveMapMessage* New(::google::protobuf::Arena* arena) const;
  void CopyFrom(const ::google::protobuf::Message& from);
  void MergeFrom(const ::google::protobuf::Message& from);
  void CopyFrom(const TestRecursiveMapMessage& from);
  void MergeFrom(const TestRecursiveMapMessage& from);
  void Clear();
  bool IsInitialized() const;

  size_t ByteSizeLong() const;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input);
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const;
  ::google::protobuf::uint8* InternalSerializeWithCachedSizesToArray(
      bool deterministic, ::google::protobuf::uint8* output) const;
  ::google::protobuf::uint8* SerializeWithCachedSizesToArray(::google::protobuf::uint8* output) const {
    return InternalSerializeWithCachedSizesToArray(false, output);
  }
  int GetCachedSize() const { return _cached_size_; }
  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const;
  void InternalSwap(TestRecursiveMapMessage* other);
  void UnsafeMergeFrom(const TestRecursiveMapMessage& from);
  protected:
  explicit TestRecursiveMapMessage(::google::protobuf::Arena* arena);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::google::protobuf::Arena* arena);
  private:
  inline ::google::protobuf::Arena* GetArenaNoVirtual() const {
    return _internal_metadata_.arena();
  }
  inline void* MaybeArenaPtr() const {
    return _internal_metadata_.raw_arena_ptr();
  }
  public:

  ::google::protobuf::Metadata GetMetadata() const;

  // nested types ----------------------------------------------------


  // accessors -------------------------------------------------------

  // map<string, .protobuf_unittest.TestRecursiveMapMessage> a = 1;
  int a_size() const;
  void clear_a();
  static const int kAFieldNumber = 1;
  const ::google::protobuf::Map< ::std::string, ::protobuf_unittest::TestRecursiveMapMessage >&
      a() const;
  ::google::protobuf::Map< ::std::string, ::protobuf_unittest::TestRecursiveMapMessage >*
      mutable_a();

  // @@protoc_insertion_point(class_scope:protobuf_unittest.TestRecursiveMapMessage)
 private:

  ::google::protobuf::internal::InternalMetadataWithArena _internal_metadata_;
  friend class ::google::protobuf::Arena;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  typedef ::google::protobuf::internal::MapEntryLite<
      ::std::string, ::protobuf_unittest::TestRecursiveMapMessage,
      ::google::protobuf::internal::WireFormatLite::TYPE_STRING,
      ::google::protobuf::internal::WireFormatLite::TYPE_MESSAGE,
      0 >
      TestRecursiveMapMessage_AEntry;
  ::google::protobuf::internal::MapField<
      ::std::string, ::protobuf_unittest::TestRecursiveMapMessage,
      ::google::protobuf::internal::WireFormatLite::TYPE_STRING,
      ::google::protobuf::internal::WireFormatLite::TYPE_MESSAGE,
      0 > a_;
  mutable int _cached_size_;
  friend void  protobuf_InitDefaults_google_2fprotobuf_2fmap_5funittest_2eproto_impl();
  friend void  protobuf_AddDesc_google_2fprotobuf_2fmap_5funittest_2eproto_impl();
  friend void protobuf_AssignDesc_google_2fprotobuf_2fmap_5funittest_2eproto();
  friend void protobuf_ShutdownFile_google_2fprotobuf_2fmap_5funittest_2eproto();

  void InitAsDefaultInstance();
};
extern ::google::protobuf::internal::ExplicitlyConstructed<TestRecursiveMapMessage> TestRecursiveMapMessage_default_instance_;

// ===================================================================


// ===================================================================

#if !PROTOBUF_INLINE_NOT_IN_HEADERS
// TestMap

// map<int32, int32> map_int32_int32 = 1;
inline int TestMap::map_int32_int32_size() const {
  return map_int32_int32_.size();
}
inline void TestMap::clear_map_int32_int32() {
  map_int32_int32_.Clear();
}
inline const ::google::protobuf::Map< ::google::protobuf::int32, ::google::protobuf::int32 >&
TestMap::map_int32_int32() const {
  // @@protoc_insertion_point(field_map:protobuf_unittest.TestMap.map_int32_int32)
  return map_int32_int32_.GetMap();
}
inline ::google::protobuf::Map< ::google::protobuf::int32, ::google::protobuf::int32 >*
TestMap::mutable_map_int32_int32() {
  // @@protoc_insertion_point(field_mutable_map:protobuf_unittest.TestMap.map_int32_int32)
  return map_int32_int32_.MutableMap();
}

// map<int64, int64> map_int64_int64 = 2;
inline int TestMap::map_int64_int64_size() const {
  return map_int64_int64_.size();
}
inline void TestMap::clear_map_int64_int64() {
  map_int64_int64_.Clear();
}
inline const ::google::protobuf::Map< ::google::protobuf::int64, ::google::protobuf::int64 >&
TestMap::map_int64_int64() const {
  // @@protoc_insertion_point(field_map:protobuf_unittest.TestMap.map_int64_int64)
  return map_int64_int64_.GetMap();
}
inline ::google::protobuf::Map< ::google::protobuf::int64, ::google::protobuf::int64 >*
TestMap::mutable_map_int64_int64() {
  // @@protoc_insertion_point(field_mutable_map:protobuf_unittest.TestMap.map_int64_int64)
  return map_int64_int64_.MutableMap();
}

// map<uint32, uint32> map_uint32_uint32 = 3;
inline int TestMap::map_uint32_uint32_size() const {
  return map_uint32_uint32_.size();
}
inline void TestMap::clear_map_uint32_uint32() {
  map_uint32_uint32_.Clear();
}
inline const ::google::protobuf::Map< ::google::protobuf::uint32, ::google::protobuf::uint32 >&
TestMap::map_uint32_uint32() const {
  // @@protoc_insertion_point(field_map:protobuf_unittest.TestMap.map_uint32_uint32)
  return map_uint32_uint32_.GetMap();
}
inline ::google::protobuf::Map< ::google::protobuf::uint32, ::google::protobuf::uint32 >*
TestMap::mutable_map_uint32_uint32() {
  // @@protoc_insertion_point(field_mutable_map:protobuf_unittest.TestMap.map_uint32_uint32)
  return map_uint32_uint32_.MutableMap();
}

// map<uint64, uint64> map_uint64_uint64 = 4;
inline int TestMap::map_uint64_uint64_size() const {
  return map_uint64_uint64_.size();
}
inline void TestMap::clear_map_uint64_uint64() {
  map_uint64_uint64_.Clear();
}
inline const ::google::protobuf::Map< ::google::protobuf::uint64, ::google::protobuf::uint64 >&
TestMap::map_uint64_uint64() const {
  // @@protoc_insertion_point(field_map:protobuf_unittest.TestMap.map_uint64_uint64)
  return map_uint64_uint64_.GetMap();
}
inline ::google::protobuf::Map< ::google::protobuf::uint64, ::google::protobuf::uint64 >*
TestMap::mutable_map_uint64_uint64() {
  // @@protoc_insertion_point(field_mutable_map:protobuf_unittest.TestMap.map_uint64_uint64)
  return map_uint64_uint64_.MutableMap();
}

// map<sint32, sint32> map_sint32_sint32 = 5;
inline int TestMap::map_sint32_sint32_size() const {
  return map_sint32_sint32_.size();
}
inline void TestMap::clear_map_sint32_sint32() {
  map_sint32_sint32_.Clear();
}
inline const ::google::protobuf::Map< ::google::protobuf::int32, ::google::protobuf::int32 >&
TestMap::map_sint32_sint32() const {
  // @@protoc_insertion_point(field_map:protobuf_unittest.TestMap.map_sint32_sint32)
  return map_sint32_sint32_.GetMap();
}
inline ::google::protobuf::Map< ::google::protobuf::int32, ::google::protobuf::int32 >*
TestMap::mutable_map_sint32_sint32() {
  // @@protoc_insertion_point(field_mutable_map:protobuf_unittest.TestMap.map_sint32_sint32)
  return map_sint32_sint32_.MutableMap();
}

// map<sint64, sint64> map_sint64_sint64 = 6;
inline int TestMap::map_sint64_sint64_size() const {
  return map_sint64_sint64_.size();
}
inline void TestMap::clear_map_sint64_sint64() {
  map_sint64_sint64_.Clear();
}
inline const ::google::protobuf::Map< ::google::protobuf::int64, ::google::protobuf::int64 >&
TestMap::map_sint64_sint64() const {
  // @@protoc_insertion_point(field_map:protobuf_unittest.TestMap.map_sint64_sint64)
  return map_sint64_sint64_.GetMap();
}
inline ::google::protobuf::Map< ::google::protobuf::int64, ::google::protobuf::int64 >*
TestMap::mutable_map_sint64_sint64() {
  // @@protoc_insertion_point(field_mutable_map:protobuf_unittest.TestMap.map_sint64_sint64)
  return map_sint64_sint64_.MutableMap();
}

// map<fixed32, fixed32> map_fixed32_fixed32 = 7;
inline int TestMap::map_fixed32_fixed32_size() const {
  return map_fixed32_fixed32_.size();
}
inline void TestMap::clear_map_fixed32_fixed32() {
  map_fixed32_fixed32_.Clear();
}
inline const ::google::protobuf::Map< ::google::protobuf::uint32, ::google::protobuf::uint32 >&
TestMap::map_fixed32_fixed32() const {
  // @@protoc_insertion_point(field_map:protobuf_unittest.TestMap.map_fixed32_fixed32)
  return map_fixed32_fixed32_.GetMap();
}
inline ::google::protobuf::Map< ::google::protobuf::uint32, ::google::protobuf::uint32 >*
TestMap::mutable_map_fixed32_fixed32() {
  // @@protoc_insertion_point(field_mutable_map:protobuf_unittest.TestMap.map_fixed32_fixed32)
  return map_fixed32_fixed32_.MutableMap();
}

// map<fixed64, fixed64> map_fixed64_fixed64 = 8;
inline int TestMap::map_fixed64_fixed64_size() const {
  return map_fixed64_fixed64_.size();
}
inline void TestMap::clear_map_fixed64_fixed64() {
  map_fixed64_fixed64_.Clear();
}
inline const ::google::protobuf::Map< ::google::protobuf::uint64, ::google::protobuf::uint64 >&
TestMap::map_fixed64_fixed64() const {
  // @@protoc_insertion_point(field_map:protobuf_unittest.TestMap.map_fixed64_fixed64)
  return map_fixed64_fixed64_.GetMap();
}
inline ::google::protobuf::Map< ::google::protobuf::uint64, ::google::protobuf::uint64 >*
TestMap::mutable_map_fixed64_fixed64() {
  // @@protoc_insertion_point(field_mutable_map:protobuf_unittest.TestMap.map_fixed64_fixed64)
  return map_fixed64_fixed64_.MutableMap();
}

// map<sfixed32, sfixed32> map_sfixed32_sfixed32 = 9;
inline int TestMap::map_sfixed32_sfixed32_size() const {
  return map_sfixed32_sfixed32_.size();
}
inline void TestMap::clear_map_sfixed32_sfixed32() {
  map_sfixed32_sfixed32_.Clear();
}
inline const ::google::protobuf::Map< ::google::protobuf::int32, ::google::protobuf::int32 >&
TestMap::map_sfixed32_sfixed32() const {
  // @@protoc_insertion_point(field_map:protobuf_unittest.TestMap.map_sfixed32_sfixed32)
  return map_sfixed32_sfixed32_.GetMap();
}
inline ::google::protobuf::Map< ::google::protobuf::int32, ::google::protobuf::int32 >*
TestMap::mutable_map_sfixed32_sfixed32() {
  // @@protoc_insertion_point(field_mutable_map:protobuf_unittest.TestMap.map_sfixed32_sfixed32)
  return map_sfixed32_sfixed32_.MutableMap();
}

// map<sfixed64, sfixed64> map_sfixed64_sfixed64 = 10;
inline int TestMap::map_sfixed64_sfixed64_size() const {
  return map_sfixed64_sfixed64_.size();
}
inline void TestMap::clear_map_sfixed64_sfixed64() {
  map_sfixed64_sfixed64_.Clear();
}
inline const ::google::protobuf::Map< ::google::protobuf::int64, ::google::protobuf::int64 >&
TestMap::map_sfixed64_sfixed64() const {
  // @@protoc_insertion_point(field_map:protobuf_unittest.TestMap.map_sfixed64_sfixed64)
  return map_sfixed64_sfixed64_.GetMap();
}
inline ::google::protobuf::Map< ::google::protobuf::int64, ::google::protobuf::int64 >*
TestMap::mutable_map_sfixed64_sfixed64() {
  // @@protoc_insertion_point(field_mutable_map:protobuf_unittest.TestMap.map_sfixed64_sfixed64)
  return map_sfixed64_sfixed64_.MutableMap();
}

// map<int32, float> map_int32_float = 11;
inline int TestMap::map_int32_float_size() const {
  return map_int32_float_.size();
}
inline void TestMap::clear_map_int32_float() {
  map_int32_float_.Clear();
}
inline const ::google::protobuf::Map< ::google::protobuf::int32, float >&
TestMap::map_int32_float() const {
  // @@protoc_insertion_point(field_map:protobuf_unittest.TestMap.map_int32_float)
  return map_int32_float_.GetMap();
}
inline ::google::protobuf::Map< ::google::protobuf::int32, float >*
TestMap::mutable_map_int32_float() {
  // @@protoc_insertion_point(field_mutable_map:protobuf_unittest.TestMap.map_int32_float)
  return map_int32_float_.MutableMap();
}

// map<int32, double> map_int32_double = 12;
inline int TestMap::map_int32_double_size() const {
  return map_int32_double_.size();
}
inline void TestMap::clear_map_int32_double() {
  map_int32_double_.Clear();
}
inline const ::google::protobuf::Map< ::google::protobuf::int32, double >&
TestMap::map_int32_double() const {
  // @@protoc_insertion_point(field_map:protobuf_unittest.TestMap.map_int32_double)
  return map_int32_double_.GetMap();
}
inline ::google::protobuf::Map< ::google::protobuf::int32, double >*
TestMap::mutable_map_int32_double() {
  // @@protoc_insertion_point(field_mutable_map:protobuf_unittest.TestMap.map_int32_double)
  return map_int32_double_.MutableMap();
}

// map<bool, bool> map_bool_bool = 13;
inline int TestMap::map_bool_bool_size() const {
  return map_bool_bool_.size();
}
inline void TestMap::clear_map_bool_bool() {
  map_bool_bool_.Clear();
}
inline const ::google::protobuf::Map< bool, bool >&
TestMap::map_bool_bool() const {
  // @@protoc_insertion_point(field_map:protobuf_unittest.TestMap.map_bool_bool)
  return map_bool_bool_.GetMap();
}
inline ::google::protobuf::Map< bool, bool >*
TestMap::mutable_map_bool_bool() {
  // @@protoc_insertion_point(field_mutable_map:protobuf_unittest.TestMap.map_bool_bool)
  return map_bool_bool_.MutableMap();
}

// map<string, string> map_string_string = 14;
inline int TestMap::map_string_string_size() const {
  return map_string_string_.size();
}
inline void TestMap::clear_map_string_string() {
  map_string_string_.Clear();
}
inline const ::google::protobuf::Map< ::std::string, ::std::string >&
TestMap::map_string_string() const {
  // @@protoc_insertion_point(field_map:protobuf_unittest.TestMap.map_string_string)
  return map_string_string_.GetMap();
}
inline ::google::protobuf::Map< ::std::string, ::std::string >*
TestMap::mutable_map_string_string() {
  // @@protoc_insertion_point(field_mutable_map:protobuf_unittest.TestMap.map_string_string)
  return map_string_string_.MutableMap();
}

// map<int32, bytes> map_int32_bytes = 15;
inline int TestMap::map_int32_bytes_size() const {
  return map_int32_bytes_.size();
}
inline void TestMap::clear_map_int32_bytes() {
  map_int32_bytes_.Clear();
}
inline const ::google::protobuf::Map< ::google::protobuf::int32, ::std::string >&
TestMap::map_int32_bytes() const {
  // @@protoc_insertion_point(field_map:protobuf_unittest.TestMap.map_int32_bytes)
  return map_int32_bytes_.GetMap();
}
inline ::google::protobuf::Map< ::google::protobuf::int32, ::std::string >*
TestMap::mutable_map_int32_bytes() {
  // @@protoc_insertion_point(field_mutable_map:protobuf_unittest.TestMap.map_int32_bytes)
  return map_int32_bytes_.MutableMap();
}

// map<int32, .protobuf_unittest.MapEnum> map_int32_enum = 16;
inline int TestMap::map_int32_enum_size() const {
  return map_int32_enum_.size();
}
inline void TestMap::clear_map_int32_enum() {
  map_int32_enum_.Clear();
}
inline const ::google::protobuf::Map< ::google::protobuf::int32, ::protobuf_unittest::MapEnum >&
TestMap::map_int32_enum() const {
  // @@protoc_insertion_point(field_map:protobuf_unittest.TestMap.map_int32_enum)
  return map_int32_enum_.GetMap();
}
inline ::google::protobuf::Map< ::google::protobuf::int32, ::protobuf_unittest::MapEnum >*
TestMap::mutable_map_int32_enum() {
  // @@protoc_insertion_point(field_mutable_map:protobuf_unittest.TestMap.map_int32_enum)
  return map_int32_enum_.MutableMap();
}

// map<int32, .protobuf_unittest.ForeignMessage> map_int32_foreign_message = 17;
inline int TestMap::map_int32_foreign_message_size() const {
  return map_int32_foreign_message_.size();
}
inline void TestMap::clear_map_int32_foreign_message() {
  map_int32_foreign_message_.Clear();
}
inline const ::google::protobuf::Map< ::google::protobuf::int32, ::protobuf_unittest::ForeignMessage >&
TestMap::map_int32_foreign_message() const {
  // @@protoc_insertion_point(field_map:protobuf_unittest.TestMap.map_int32_foreign_message)
  return map_int32_foreign_message_.GetMap();
}
inline ::google::protobuf::Map< ::google::protobuf::int32, ::protobuf_unittest::ForeignMessage >*
TestMap::mutable_map_int32_foreign_message() {
  // @@protoc_insertion_point(field_mutable_map:protobuf_unittest.TestMap.map_int32_foreign_message)
  return map_int32_foreign_message_.MutableMap();
}

// map<string, .protobuf_unittest.ForeignMessage> map_string_foreign_message = 18;
inline int TestMap::map_string_foreign_message_size() const {
  return map_string_foreign_message_.size();
}
inline void TestMap::clear_map_string_foreign_message() {
  map_string_foreign_message_.Clear();
}
inline const ::google::protobuf::Map< ::std::string, ::protobuf_unittest::ForeignMessage >&
TestMap::map_string_foreign_message() const {
  // @@protoc_insertion_point(field_map:protobuf_unittest.TestMap.map_string_foreign_message)
  return map_string_foreign_message_.GetMap();
}
inline ::google::protobuf::Map< ::std::string, ::protobuf_unittest::ForeignMessage >*
TestMap::mutable_map_string_foreign_message() {
  // @@protoc_insertion_point(field_mutable_map:protobuf_unittest.TestMap.map_string_foreign_message)
  return map_string_foreign_message_.MutableMap();
}

inline const TestMap* TestMap::internal_default_instance() {
  return &TestMap_default_instance_.get();
}
// -------------------------------------------------------------------

// TestMapSubmessage

// optional .protobuf_unittest.TestMap test_map = 1;
inline bool TestMapSubmessage::has_test_map() const {
  return this != internal_default_instance() && test_map_ != NULL;
}
inline void TestMapSubmessage::clear_test_map() {
  if (GetArenaNoVirtual() == NULL && test_map_ != NULL) delete test_map_;
  test_map_ = NULL;
}
inline const ::protobuf_unittest::TestMap& TestMapSubmessage::test_map() const {
  // @@protoc_insertion_point(field_get:protobuf_unittest.TestMapSubmessage.test_map)
  return test_map_ != NULL ? *test_map_
                         : *::protobuf_unittest::TestMap::internal_default_instance();
}
inline ::protobuf_unittest::TestMap* TestMapSubmessage::mutable_test_map() {
  
  if (test_map_ == NULL) {
    _slow_mutable_test_map();
  }
  // @@protoc_insertion_point(field_mutable:protobuf_unittest.TestMapSubmessage.test_map)
  return test_map_;
}
inline ::protobuf_unittest::TestMap* TestMapSubmessage::release_test_map() {
  // @@protoc_insertion_point(field_release:protobuf_unittest.TestMapSubmessage.test_map)
  
  if (GetArenaNoVirtual() != NULL) {
    return _slow_release_test_map();
  } else {
    ::protobuf_unittest::TestMap* temp = test_map_;
    test_map_ = NULL;
    return temp;
  }
}
inline  void TestMapSubmessage::set_allocated_test_map(::protobuf_unittest::TestMap* test_map) {
  ::google::protobuf::Arena* message_arena = GetArenaNoVirtual();
  if (message_arena == NULL) {
    delete test_map_;
  }
  if (test_map != NULL) {
    _slow_set_allocated_test_map(message_arena, &test_map);
  }
  test_map_ = test_map;
  if (test_map) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_set_allocated:protobuf_unittest.TestMapSubmessage.test_map)
}

inline const TestMapSubmessage* TestMapSubmessage::internal_default_instance() {
  return &TestMapSubmessage_default_instance_.get();
}
// -------------------------------------------------------------------

// TestMessageMap

// map<int32, .protobuf_unittest.TestAllTypes> map_int32_message = 1;
inline int TestMessageMap::map_int32_message_size() const {
  return map_int32_message_.size();
}
inline void TestMessageMap::clear_map_int32_message() {
  map_int32_message_.Clear();
}
inline const ::google::protobuf::Map< ::google::protobuf::int32, ::protobuf_unittest::TestAllTypes >&
TestMessageMap::map_int32_message() const {
  // @@protoc_insertion_point(field_map:protobuf_unittest.TestMessageMap.map_int32_message)
  return map_int32_message_.GetMap();
}
inline ::google::protobuf::Map< ::google::protobuf::int32, ::protobuf_unittest::TestAllTypes >*
TestMessageMap::mutable_map_int32_message() {
  // @@protoc_insertion_point(field_mutable_map:protobuf_unittest.TestMessageMap.map_int32_message)
  return map_int32_message_.MutableMap();
}

inline const TestMessageMap* TestMessageMap::internal_default_instance() {
  return &TestMessageMap_default_instance_.get();
}
// -------------------------------------------------------------------

// TestSameTypeMap

// map<int32, int32> map1 = 1;
inline int TestSameTypeMap::map1_size() const {
  return map1_.size();
}
inline void TestSameTypeMap::clear_map1() {
  map1_.Clear();
}
inline const ::google::protobuf::Map< ::google::protobuf::int32, ::google::protobuf::int32 >&
TestSameTypeMap::map1() const {
  // @@protoc_insertion_point(field_map:protobuf_unittest.TestSameTypeMap.map1)
  return map1_.GetMap();
}
inline ::google::protobuf::Map< ::google::protobuf::int32, ::google::protobuf::int32 >*
TestSameTypeMap::mutable_map1() {
  // @@protoc_insertion_point(field_mutable_map:protobuf_unittest.TestSameTypeMap.map1)
  return map1_.MutableMap();
}

// map<int32, int32> map2 = 2;
inline int TestSameTypeMap::map2_size() const {
  return map2_.size();
}
inline void TestSameTypeMap::clear_map2() {
  map2_.Clear();
}
inline const ::google::protobuf::Map< ::google::protobuf::int32, ::google::protobuf::int32 >&
TestSameTypeMap::map2() const {
  // @@protoc_insertion_point(field_map:protobuf_unittest.TestSameTypeMap.map2)
  return map2_.GetMap();
}
inline ::google::protobuf::Map< ::google::protobuf::int32, ::google::protobuf::int32 >*
TestSameTypeMap::mutable_map2() {
  // @@protoc_insertion_point(field_mutable_map:protobuf_unittest.TestSameTypeMap.map2)
  return map2_.MutableMap();
}

inline const TestSameTypeMap* TestSameTypeMap::internal_default_instance() {
  return &TestSameTypeMap_default_instance_.get();
}
// -------------------------------------------------------------------

// TestRequiredMessageMap

// map<int32, .protobuf_unittest.TestRequired> map_field = 1;
inline int TestRequiredMessageMap::map_field_size() const {
  return map_field_.size();
}
inline void TestRequiredMessageMap::clear_map_field() {
  map_field_.Clear();
}
inline const ::google::protobuf::Map< ::google::protobuf::int32, ::protobuf_unittest::TestRequired >&
TestRequiredMessageMap::map_field() const {
  // @@protoc_insertion_point(field_map:protobuf_unittest.TestRequiredMessageMap.map_field)
  return map_field_.GetMap();
}
inline ::google::protobuf::Map< ::google::protobuf::int32, ::protobuf_unittest::TestRequired >*
TestRequiredMessageMap::mutable_map_field() {
  // @@protoc_insertion_point(field_mutable_map:protobuf_unittest.TestRequiredMessageMap.map_field)
  return map_field_.MutableMap();
}

inline const TestRequiredMessageMap* TestRequiredMessageMap::internal_default_instance() {
  return &TestRequiredMessageMap_default_instance_.get();
}
// -------------------------------------------------------------------

// TestArenaMap

// map<int32, int32> map_int32_int32 = 1;
inline int TestArenaMap::map_int32_int32_size() const {
  return map_int32_int32_.size();
}
inline void TestArenaMap::clear_map_int32_int32() {
  map_int32_int32_.Clear();
}
inline const ::google::protobuf::Map< ::google::protobuf::int32, ::google::protobuf::int32 >&
TestArenaMap::map_int32_int32() const {
  // @@protoc_insertion_point(field_map:protobuf_unittest.TestArenaMap.map_int32_int32)
  return map_int32_int32_.GetMap();
}
inline ::google::protobuf::Map< ::google::protobuf::int32, ::google::protobuf::int32 >*
TestArenaMap::mutable_map_int32_int32() {
  // @@protoc_insertion_point(field_mutable_map:protobuf_unittest.TestArenaMap.map_int32_int32)
  return map_int32_int32_.MutableMap();
}

// map<int64, int64> map_int64_int64 = 2;
inline int TestArenaMap::map_int64_int64_size() const {
  return map_int64_int64_.size();
}
inline void TestArenaMap::clear_map_int64_int64() {
  map_int64_int64_.Clear();
}
inline const ::google::protobuf::Map< ::google::protobuf::int64, ::google::protobuf::int64 >&
TestArenaMap::map_int64_int64() const {
  // @@protoc_insertion_point(field_map:protobuf_unittest.TestArenaMap.map_int64_int64)
  return map_int64_int64_.GetMap();
}
inline ::google::protobuf::Map< ::google::protobuf::int64, ::google::protobuf::int64 >*
TestArenaMap::mutable_map_int64_int64() {
  // @@protoc_insertion_point(field_mutable_map:protobuf_unittest.TestArenaMap.map_int64_int64)
  return map_int64_int64_.MutableMap();
}

// map<uint32, uint32> map_uint32_uint32 = 3;
inline int TestArenaMap::map_uint32_uint32_size() const {
  return map_uint32_uint32_.size();
}
inline void TestArenaMap::clear_map_uint32_uint32() {
  map_uint32_uint32_.Clear();
}
inline const ::google::protobuf::Map< ::google::protobuf::uint32, ::google::protobuf::uint32 >&
TestArenaMap::map_uint32_uint32() const {
  // @@protoc_insertion_point(field_map:protobuf_unittest.TestArenaMap.map_uint32_uint32)
  return map_uint32_uint32_.GetMap();
}
inline ::google::protobuf::Map< ::google::protobuf::uint32, ::google::protobuf::uint32 >*
TestArenaMap::mutable_map_uint32_uint32() {
  // @@protoc_insertion_point(field_mutable_map:protobuf_unittest.TestArenaMap.map_uint32_uint32)
  return map_uint32_uint32_.MutableMap();
}

// map<uint64, uint64> map_uint64_uint64 = 4;
inline int TestArenaMap::map_uint64_uint64_size() const {
  return map_uint64_uint64_.size();
}
inline void TestArenaMap::clear_map_uint64_uint64() {
  map_uint64_uint64_.Clear();
}
inline const ::google::protobuf::Map< ::google::protobuf::uint64, ::google::protobuf::uint64 >&
TestArenaMap::map_uint64_uint64() const {
  // @@protoc_insertion_point(field_map:protobuf_unittest.TestArenaMap.map_uint64_uint64)
  return map_uint64_uint64_.GetMap();
}
inline ::google::protobuf::Map< ::google::protobuf::uint64, ::google::protobuf::uint64 >*
TestArenaMap::mutable_map_uint64_uint64() {
  // @@protoc_insertion_point(field_mutable_map:protobuf_unittest.TestArenaMap.map_uint64_uint64)
  return map_uint64_uint64_.MutableMap();
}

// map<sint32, sint32> map_sint32_sint32 = 5;
inline int TestArenaMap::map_sint32_sint32_size() const {
  return map_sint32_sint32_.size();
}
inline void TestArenaMap::clear_map_sint32_sint32() {
  map_sint32_sint32_.Clear();
}
inline const ::google::protobuf::Map< ::google::protobuf::int32, ::google::protobuf::int32 >&
TestArenaMap::map_sint32_sint32() const {
  // @@protoc_insertion_point(field_map:protobuf_unittest.TestArenaMap.map_sint32_sint32)
  return map_sint32_sint32_.GetMap();
}
inline ::google::protobuf::Map< ::google::protobuf::int32, ::google::protobuf::int32 >*
TestArenaMap::mutable_map_sint32_sint32() {
  // @@protoc_insertion_point(field_mutable_map:protobuf_unittest.TestArenaMap.map_sint32_sint32)
  return map_sint32_sint32_.MutableMap();
}

// map<sint64, sint64> map_sint64_sint64 = 6;
inline int TestArenaMap::map_sint64_sint64_size() const {
  return map_sint64_sint64_.size();
}
inline void TestArenaMap::clear_map_sint64_sint64() {
  map_sint64_sint64_.Clear();
}
inline const ::google::protobuf::Map< ::google::protobuf::int64, ::google::protobuf::int64 >&
TestArenaMap::map_sint64_sint64() const {
  // @@protoc_insertion_point(field_map:protobuf_unittest.TestArenaMap.map_sint64_sint64)
  return map_sint64_sint64_.GetMap();
}
inline ::google::protobuf::Map< ::google::protobuf::int64, ::google::protobuf::int64 >*
TestArenaMap::mutable_map_sint64_sint64() {
  // @@protoc_insertion_point(field_mutable_map:protobuf_unittest.TestArenaMap.map_sint64_sint64)
  return map_sint64_sint64_.MutableMap();
}

// map<fixed32, fixed32> map_fixed32_fixed32 = 7;
inline int TestArenaMap::map_fixed32_fixed32_size() const {
  return map_fixed32_fixed32_.size();
}
inline void TestArenaMap::clear_map_fixed32_fixed32() {
  map_fixed32_fixed32_.Clear();
}
inline const ::google::protobuf::Map< ::google::protobuf::uint32, ::google::protobuf::uint32 >&
TestArenaMap::map_fixed32_fixed32() const {
  // @@protoc_insertion_point(field_map:protobuf_unittest.TestArenaMap.map_fixed32_fixed32)
  return map_fixed32_fixed32_.GetMap();
}
inline ::google::protobuf::Map< ::google::protobuf::uint32, ::google::protobuf::uint32 >*
TestArenaMap::mutable_map_fixed32_fixed32() {
  // @@protoc_insertion_point(field_mutable_map:protobuf_unittest.TestArenaMap.map_fixed32_fixed32)
  return map_fixed32_fixed32_.MutableMap();
}

// map<fixed64, fixed64> map_fixed64_fixed64 = 8;
inline int TestArenaMap::map_fixed64_fixed64_size() const {
  return map_fixed64_fixed64_.size();
}
inline void TestArenaMap::clear_map_fixed64_fixed64() {
  map_fixed64_fixed64_.Clear();
}
inline const ::google::protobuf::Map< ::google::protobuf::uint64, ::google::protobuf::uint64 >&
TestArenaMap::map_fixed64_fixed64() const {
  // @@protoc_insertion_point(field_map:protobuf_unittest.TestArenaMap.map_fixed64_fixed64)
  return map_fixed64_fixed64_.GetMap();
}
inline ::google::protobuf::Map< ::google::protobuf::uint64, ::google::protobuf::uint64 >*
TestArenaMap::mutable_map_fixed64_fixed64() {
  // @@protoc_insertion_point(field_mutable_map:protobuf_unittest.TestArenaMap.map_fixed64_fixed64)
  return map_fixed64_fixed64_.MutableMap();
}

// map<sfixed32, sfixed32> map_sfixed32_sfixed32 = 9;
inline int TestArenaMap::map_sfixed32_sfixed32_size() const {
  return map_sfixed32_sfixed32_.size();
}
inline void TestArenaMap::clear_map_sfixed32_sfixed32() {
  map_sfixed32_sfixed32_.Clear();
}
inline const ::google::protobuf::Map< ::google::protobuf::int32, ::google::protobuf::int32 >&
TestArenaMap::map_sfixed32_sfixed32() const {
  // @@protoc_insertion_point(field_map:protobuf_unittest.TestArenaMap.map_sfixed32_sfixed32)
  return map_sfixed32_sfixed32_.GetMap();
}
inline ::google::protobuf::Map< ::google::protobuf::int32, ::google::protobuf::int32 >*
TestArenaMap::mutable_map_sfixed32_sfixed32() {
  // @@protoc_insertion_point(field_mutable_map:protobuf_unittest.TestArenaMap.map_sfixed32_sfixed32)
  return map_sfixed32_sfixed32_.MutableMap();
}

// map<sfixed64, sfixed64> map_sfixed64_sfixed64 = 10;
inline int TestArenaMap::map_sfixed64_sfixed64_size() const {
  return map_sfixed64_sfixed64_.size();
}
inline void TestArenaMap::clear_map_sfixed64_sfixed64() {
  map_sfixed64_sfixed64_.Clear();
}
inline const ::google::protobuf::Map< ::google::protobuf::int64, ::google::protobuf::int64 >&
TestArenaMap::map_sfixed64_sfixed64() const {
  // @@protoc_insertion_point(field_map:protobuf_unittest.TestArenaMap.map_sfixed64_sfixed64)
  return map_sfixed64_sfixed64_.GetMap();
}
inline ::google::protobuf::Map< ::google::protobuf::int64, ::google::protobuf::int64 >*
TestArenaMap::mutable_map_sfixed64_sfixed64() {
  // @@protoc_insertion_point(field_mutable_map:protobuf_unittest.TestArenaMap.map_sfixed64_sfixed64)
  return map_sfixed64_sfixed64_.MutableMap();
}

// map<int32, float> map_int32_float = 11;
inline int TestArenaMap::map_int32_float_size() const {
  return map_int32_float_.size();
}
inline void TestArenaMap::clear_map_int32_float() {
  map_int32_float_.Clear();
}
inline const ::google::protobuf::Map< ::google::protobuf::int32, float >&
TestArenaMap::map_int32_float() const {
  // @@protoc_insertion_point(field_map:protobuf_unittest.TestArenaMap.map_int32_float)
  return map_int32_float_.GetMap();
}
inline ::google::protobuf::Map< ::google::protobuf::int32, float >*
TestArenaMap::mutable_map_int32_float() {
  // @@protoc_insertion_point(field_mutable_map:protobuf_unittest.TestArenaMap.map_int32_float)
  return map_int32_float_.MutableMap();
}

// map<int32, double> map_int32_double = 12;
inline int TestArenaMap::map_int32_double_size() const {
  return map_int32_double_.size();
}
inline void TestArenaMap::clear_map_int32_double() {
  map_int32_double_.Clear();
}
inline const ::google::protobuf::Map< ::google::protobuf::int32, double >&
TestArenaMap::map_int32_double() const {
  // @@protoc_insertion_point(field_map:protobuf_unittest.TestArenaMap.map_int32_double)
  return map_int32_double_.GetMap();
}
inline ::google::protobuf::Map< ::google::protobuf::int32, double >*
TestArenaMap::mutable_map_int32_double() {
  // @@protoc_insertion_point(field_mutable_map:protobuf_unittest.TestArenaMap.map_int32_double)
  return map_int32_double_.MutableMap();
}

// map<bool, bool> map_bool_bool = 13;
inline int TestArenaMap::map_bool_bool_size() const {
  return map_bool_bool_.size();
}
inline void TestArenaMap::clear_map_bool_bool() {
  map_bool_bool_.Clear();
}
inline const ::google::protobuf::Map< bool, bool >&
TestArenaMap::map_bool_bool() const {
  // @@protoc_insertion_point(field_map:protobuf_unittest.TestArenaMap.map_bool_bool)
  return map_bool_bool_.GetMap();
}
inline ::google::protobuf::Map< bool, bool >*
TestArenaMap::mutable_map_bool_bool() {
  // @@protoc_insertion_point(field_mutable_map:protobuf_unittest.TestArenaMap.map_bool_bool)
  return map_bool_bool_.MutableMap();
}

// map<string, string> map_string_string = 14;
inline int TestArenaMap::map_string_string_size() const {
  return map_string_string_.size();
}
inline void TestArenaMap::clear_map_string_string() {
  map_string_string_.Clear();
}
inline const ::google::protobuf::Map< ::std::string, ::std::string >&
TestArenaMap::map_string_string() const {
  // @@protoc_insertion_point(field_map:protobuf_unittest.TestArenaMap.map_string_string)
  return map_string_string_.GetMap();
}
inline ::google::protobuf::Map< ::std::string, ::std::string >*
TestArenaMap::mutable_map_string_string() {
  // @@protoc_insertion_point(field_mutable_map:protobuf_unittest.TestArenaMap.map_string_string)
  return map_string_string_.MutableMap();
}

// map<int32, bytes> map_int32_bytes = 15;
inline int TestArenaMap::map_int32_bytes_size() const {
  return map_int32_bytes_.size();
}
inline void TestArenaMap::clear_map_int32_bytes() {
  map_int32_bytes_.Clear();
}
inline const ::google::protobuf::Map< ::google::protobuf::int32, ::std::string >&
TestArenaMap::map_int32_bytes() const {
  // @@protoc_insertion_point(field_map:protobuf_unittest.TestArenaMap.map_int32_bytes)
  return map_int32_bytes_.GetMap();
}
inline ::google::protobuf::Map< ::google::protobuf::int32, ::std::string >*
TestArenaMap::mutable_map_int32_bytes() {
  // @@protoc_insertion_point(field_mutable_map:protobuf_unittest.TestArenaMap.map_int32_bytes)
  return map_int32_bytes_.MutableMap();
}

// map<int32, .protobuf_unittest.MapEnum> map_int32_enum = 16;
inline int TestArenaMap::map_int32_enum_size() const {
  return map_int32_enum_.size();
}
inline void TestArenaMap::clear_map_int32_enum() {
  map_int32_enum_.Clear();
}
inline const ::google::protobuf::Map< ::google::protobuf::int32, ::protobuf_unittest::MapEnum >&
TestArenaMap::map_int32_enum() const {
  // @@protoc_insertion_point(field_map:protobuf_unittest.TestArenaMap.map_int32_enum)
  return map_int32_enum_.GetMap();
}
inline ::google::protobuf::Map< ::google::protobuf::int32, ::protobuf_unittest::MapEnum >*
TestArenaMap::mutable_map_int32_enum() {
  // @@protoc_insertion_point(field_mutable_map:protobuf_unittest.TestArenaMap.map_int32_enum)
  return map_int32_enum_.MutableMap();
}

// map<int32, .protobuf_unittest.ForeignMessage> map_int32_foreign_message = 17;
inline int TestArenaMap::map_int32_foreign_message_size() const {
  return map_int32_foreign_message_.size();
}
inline void TestArenaMap::clear_map_int32_foreign_message() {
  map_int32_foreign_message_.Clear();
}
inline const ::google::protobuf::Map< ::google::protobuf::int32, ::protobuf_unittest::ForeignMessage >&
TestArenaMap::map_int32_foreign_message() const {
  // @@protoc_insertion_point(field_map:protobuf_unittest.TestArenaMap.map_int32_foreign_message)
  return map_int32_foreign_message_.GetMap();
}
inline ::google::protobuf::Map< ::google::protobuf::int32, ::protobuf_unittest::ForeignMessage >*
TestArenaMap::mutable_map_int32_foreign_message() {
  // @@protoc_insertion_point(field_mutable_map:protobuf_unittest.TestArenaMap.map_int32_foreign_message)
  return map_int32_foreign_message_.MutableMap();
}

// map<int32, .protobuf_unittest_no_arena.ForeignMessage> map_int32_foreign_message_no_arena = 18;
inline int TestArenaMap::map_int32_foreign_message_no_arena_size() const {
  return map_int32_foreign_message_no_arena_.size();
}
inline void TestArenaMap::clear_map_int32_foreign_message_no_arena() {
  map_int32_foreign_message_no_arena_.Clear();
}
inline const ::google::protobuf::Map< ::google::protobuf::int32, ::protobuf_unittest_no_arena::ForeignMessage >&
TestArenaMap::map_int32_foreign_message_no_arena() const {
  // @@protoc_insertion_point(field_map:protobuf_unittest.TestArenaMap.map_int32_foreign_message_no_arena)
  return map_int32_foreign_message_no_arena_.GetMap();
}
inline ::google::protobuf::Map< ::google::protobuf::int32, ::protobuf_unittest_no_arena::ForeignMessage >*
TestArenaMap::mutable_map_int32_foreign_message_no_arena() {
  // @@protoc_insertion_point(field_mutable_map:protobuf_unittest.TestArenaMap.map_int32_foreign_message_no_arena)
  return map_int32_foreign_message_no_arena_.MutableMap();
}

inline const TestArenaMap* TestArenaMap::internal_default_instance() {
  return &TestArenaMap_default_instance_.get();
}
// -------------------------------------------------------------------

// MessageContainingEnumCalledType

// map<string, .protobuf_unittest.MessageContainingEnumCalledType> type = 1;
inline int MessageContainingEnumCalledType::type_size() const {
  return type_.size();
}
inline void MessageContainingEnumCalledType::clear_type() {
  type_.Clear();
}
inline const ::google::protobuf::Map< ::std::string, ::protobuf_unittest::MessageContainingEnumCalledType >&
MessageContainingEnumCalledType::type() const {
  // @@protoc_insertion_point(field_map:protobuf_unittest.MessageContainingEnumCalledType.type)
  return type_.GetMap();
}
inline ::google::protobuf::Map< ::std::string, ::protobuf_unittest::MessageContainingEnumCalledType >*
MessageContainingEnumCalledType::mutable_type() {
  // @@protoc_insertion_point(field_mutable_map:protobuf_unittest.MessageContainingEnumCalledType.type)
  return type_.MutableMap();
}

inline const MessageContainingEnumCalledType* MessageContainingEnumCalledType::internal_default_instance() {
  return &MessageContainingEnumCalledType_default_instance_.get();
}
// -------------------------------------------------------------------

// MessageContainingMapCalledEntry

// map<int32, int32> entry = 1;
inline int MessageContainingMapCalledEntry::entry_size() const {
  return entry_.size();
}
inline void MessageContainingMapCalledEntry::clear_entry() {
  entry_.Clear();
}
inline const ::google::protobuf::Map< ::google::protobuf::int32, ::google::protobuf::int32 >&
MessageContainingMapCalledEntry::entry() const {
  // @@protoc_insertion_point(field_map:protobuf_unittest.MessageContainingMapCalledEntry.entry)
  return entry_.GetMap();
}
inline ::google::protobuf::Map< ::google::protobuf::int32, ::google::protobuf::int32 >*
MessageContainingMapCalledEntry::mutable_entry() {
  // @@protoc_insertion_point(field_mutable_map:protobuf_unittest.MessageContainingMapCalledEntry.entry)
  return entry_.MutableMap();
}

inline const MessageContainingMapCalledEntry* MessageContainingMapCalledEntry::internal_default_instance() {
  return &MessageContainingMapCalledEntry_default_instance_.get();
}
// -------------------------------------------------------------------

// TestRecursiveMapMessage

// map<string, .protobuf_unittest.TestRecursiveMapMessage> a = 1;
inline int TestRecursiveMapMessage::a_size() const {
  return a_.size();
}
inline void TestRecursiveMapMessage::clear_a() {
  a_.Clear();
}
inline const ::google::protobuf::Map< ::std::string, ::protobuf_unittest::TestRecursiveMapMessage >&
TestRecursiveMapMessage::a() const {
  // @@protoc_insertion_point(field_map:protobuf_unittest.TestRecursiveMapMessage.a)
  return a_.GetMap();
}
inline ::google::protobuf::Map< ::std::string, ::protobuf_unittest::TestRecursiveMapMessage >*
TestRecursiveMapMessage::mutable_a() {
  // @@protoc_insertion_point(field_mutable_map:protobuf_unittest.TestRecursiveMapMessage.a)
  return a_.MutableMap();
}

inline const TestRecursiveMapMessage* TestRecursiveMapMessage::internal_default_instance() {
  return &TestRecursiveMapMessage_default_instance_.get();
}
#endif  // !PROTOBUF_INLINE_NOT_IN_HEADERS
// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------


// @@protoc_insertion_point(namespace_scope)

}  // namespace protobuf_unittest

#ifndef SWIG
namespace google {
namespace protobuf {

template <> struct is_proto_enum< ::protobuf_unittest::MessageContainingEnumCalledType_Type> : ::google::protobuf::internal::true_type {};
template <>
inline const EnumDescriptor* GetEnumDescriptor< ::protobuf_unittest::MessageContainingEnumCalledType_Type>() {
  return ::protobuf_unittest::MessageContainingEnumCalledType_Type_descriptor();
}
template <> struct is_proto_enum< ::protobuf_unittest::MapEnum> : ::google::protobuf::internal::true_type {};
template <>
inline const EnumDescriptor* GetEnumDescriptor< ::protobuf_unittest::MapEnum>() {
  return ::protobuf_unittest::MapEnum_descriptor();
}

}  // namespace protobuf
}  // namespace google
#endif  // SWIG

// @@protoc_insertion_point(global_scope)

#endif  // PROTOBUF_google_2fprotobuf_2fmap_5funittest_2eproto__INCLUDED

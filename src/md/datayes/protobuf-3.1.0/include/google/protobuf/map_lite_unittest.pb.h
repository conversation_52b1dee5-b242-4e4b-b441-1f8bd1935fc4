// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: google/protobuf/map_lite_unittest.proto

#ifndef PROTOBUF_google_2fprotobuf_2fmap_5flite_5funittest_2eproto__INCLUDED
#define PROTOBUF_google_2fprotobuf_2fmap_5flite_5funittest_2eproto__INCLUDED

#include <string>

#include <google/protobuf/stubs/common.h>

#if GOOGLE_PROTOBUF_VERSION < 3001000
#error This file was generated by a newer version of protoc which is
#error incompatible with your Protocol Buffer headers.  Please update
#error your headers.
#endif
#if 3001000 < GOOGLE_PROTOBUF_MIN_PROTOC_VERSION
#error This file was generated by an older version of protoc which is
#error incompatible with your Protocol Buffer headers.  Please
#error regenerate this file with a newer version of protoc.
#endif

#include <google/protobuf/arena.h>
#include <google/protobuf/arenastring.h>
#include <google/protobuf/generated_message_util.h>
#include <google/protobuf/message_lite.h>
#include <google/protobuf/repeated_field.h>
#include <google/protobuf/extension_set.h>
#include <google/protobuf/map.h>
#include <google/protobuf/map_field_lite.h>
#include <google/protobuf/generated_enum_util.h>
#include <google/protobuf/unittest_lite.pb.h>
#include <google/protobuf/unittest_no_arena_lite.pb.h>
// @@protoc_insertion_point(includes)

namespace protobuf_unittest {

// Internal implementation detail -- do not call these.
void protobuf_AddDesc_google_2fprotobuf_2fmap_5flite_5funittest_2eproto();
void protobuf_InitDefaults_google_2fprotobuf_2fmap_5flite_5funittest_2eproto();
void protobuf_AssignDesc_google_2fprotobuf_2fmap_5flite_5funittest_2eproto();
void protobuf_ShutdownFile_google_2fprotobuf_2fmap_5flite_5funittest_2eproto();

class ForeignMessageArenaLite;
class TestArenaMapLite;
class TestEnumMapLite;
class TestEnumMapPlusExtraLite;
class TestMapLite;
class TestMessageMapLite;
class TestRequiredLite;
class TestRequiredMessageMapLite;

enum Proto2MapEnumLite {
  PROTO2_MAP_ENUM_FOO_LITE = 0,
  PROTO2_MAP_ENUM_BAR_LITE = 1,
  PROTO2_MAP_ENUM_BAZ_LITE = 2
};
bool Proto2MapEnumLite_IsValid(int value);
const Proto2MapEnumLite Proto2MapEnumLite_MIN = PROTO2_MAP_ENUM_FOO_LITE;
const Proto2MapEnumLite Proto2MapEnumLite_MAX = PROTO2_MAP_ENUM_BAZ_LITE;
const int Proto2MapEnumLite_ARRAYSIZE = Proto2MapEnumLite_MAX + 1;

enum Proto2MapEnumPlusExtraLite {
  E_PROTO2_MAP_ENUM_FOO_LITE = 0,
  E_PROTO2_MAP_ENUM_BAR_LITE = 1,
  E_PROTO2_MAP_ENUM_BAZ_LITE = 2,
  E_PROTO2_MAP_ENUM_EXTRA_LITE = 3
};
bool Proto2MapEnumPlusExtraLite_IsValid(int value);
const Proto2MapEnumPlusExtraLite Proto2MapEnumPlusExtraLite_MIN = E_PROTO2_MAP_ENUM_FOO_LITE;
const Proto2MapEnumPlusExtraLite Proto2MapEnumPlusExtraLite_MAX = E_PROTO2_MAP_ENUM_EXTRA_LITE;
const int Proto2MapEnumPlusExtraLite_ARRAYSIZE = Proto2MapEnumPlusExtraLite_MAX + 1;

enum MapEnumLite {
  MAP_ENUM_FOO_LITE = 0,
  MAP_ENUM_BAR_LITE = 1,
  MAP_ENUM_BAZ_LITE = 2
};
bool MapEnumLite_IsValid(int value);
const MapEnumLite MapEnumLite_MIN = MAP_ENUM_FOO_LITE;
const MapEnumLite MapEnumLite_MAX = MAP_ENUM_BAZ_LITE;
const int MapEnumLite_ARRAYSIZE = MapEnumLite_MAX + 1;

// ===================================================================

class TestMapLite : public ::google::protobuf::MessageLite /* @@protoc_insertion_point(class_definition:protobuf_unittest.TestMapLite) */ {
 public:
  TestMapLite();
  virtual ~TestMapLite();

  TestMapLite(const TestMapLite& from);

  inline TestMapLite& operator=(const TestMapLite& from) {
    CopyFrom(from);
    return *this;
  }

  inline const ::std::string& unknown_fields() const {
    return _unknown_fields_.Get(
        &::google::protobuf::internal::GetEmptyStringAlreadyInited());
  }

  inline ::std::string* mutable_unknown_fields() {
    return _unknown_fields_.Mutable(
        &::google::protobuf::internal::GetEmptyStringAlreadyInited(),
        GetArenaNoVirtual());
  }

  inline ::google::protobuf::Arena* GetArena() const { return GetArenaNoVirtual(); }
  inline void* GetMaybeArenaPointer() const {
    return MaybeArenaPtr();
  }
  static const TestMapLite& default_instance();

  static const TestMapLite* internal_default_instance();

  void UnsafeArenaSwap(TestMapLite* other);
  void Swap(TestMapLite* other);

  // implements Message ----------------------------------------------

  inline TestMapLite* New() const { return New(NULL); }

  TestMapLite* New(::google::protobuf::Arena* arena) const;
  void CheckTypeAndMergeFrom(const ::google::protobuf::MessageLite& from);
  void CopyFrom(const TestMapLite& from);
  void MergeFrom(const TestMapLite& from);
  void Clear();
  bool IsInitialized() const;

  size_t ByteSizeLong() const;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input);
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const;
  void DiscardUnknownFields();
  int GetCachedSize() const { return _cached_size_; }
  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const;
  void InternalSwap(TestMapLite* other);
  void UnsafeMergeFrom(const TestMapLite& from);
  protected:
  explicit TestMapLite(::google::protobuf::Arena* arena);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::google::protobuf::Arena* arena);
  private:
  inline ::google::protobuf::Arena* GetArenaNoVirtual() const {
    return _arena_ptr_;
  }
  inline ::google::protobuf::Arena* MaybeArenaPtr() const {
    return _arena_ptr_;
  }
  public:

  ::std::string GetTypeName() const;

  // nested types ----------------------------------------------------


  // accessors -------------------------------------------------------

  // map<int32, int32> map_int32_int32 = 1;
  int map_int32_int32_size() const;
  void clear_map_int32_int32();
  static const int kMapInt32Int32FieldNumber = 1;
  const ::google::protobuf::Map< ::google::protobuf::int32, ::google::protobuf::int32 >&
      map_int32_int32() const;
  ::google::protobuf::Map< ::google::protobuf::int32, ::google::protobuf::int32 >*
      mutable_map_int32_int32();

  // map<int64, int64> map_int64_int64 = 2;
  int map_int64_int64_size() const;
  void clear_map_int64_int64();
  static const int kMapInt64Int64FieldNumber = 2;
  const ::google::protobuf::Map< ::google::protobuf::int64, ::google::protobuf::int64 >&
      map_int64_int64() const;
  ::google::protobuf::Map< ::google::protobuf::int64, ::google::protobuf::int64 >*
      mutable_map_int64_int64();

  // map<uint32, uint32> map_uint32_uint32 = 3;
  int map_uint32_uint32_size() const;
  void clear_map_uint32_uint32();
  static const int kMapUint32Uint32FieldNumber = 3;
  const ::google::protobuf::Map< ::google::protobuf::uint32, ::google::protobuf::uint32 >&
      map_uint32_uint32() const;
  ::google::protobuf::Map< ::google::protobuf::uint32, ::google::protobuf::uint32 >*
      mutable_map_uint32_uint32();

  // map<uint64, uint64> map_uint64_uint64 = 4;
  int map_uint64_uint64_size() const;
  void clear_map_uint64_uint64();
  static const int kMapUint64Uint64FieldNumber = 4;
  const ::google::protobuf::Map< ::google::protobuf::uint64, ::google::protobuf::uint64 >&
      map_uint64_uint64() const;
  ::google::protobuf::Map< ::google::protobuf::uint64, ::google::protobuf::uint64 >*
      mutable_map_uint64_uint64();

  // map<sint32, sint32> map_sint32_sint32 = 5;
  int map_sint32_sint32_size() const;
  void clear_map_sint32_sint32();
  static const int kMapSint32Sint32FieldNumber = 5;
  const ::google::protobuf::Map< ::google::protobuf::int32, ::google::protobuf::int32 >&
      map_sint32_sint32() const;
  ::google::protobuf::Map< ::google::protobuf::int32, ::google::protobuf::int32 >*
      mutable_map_sint32_sint32();

  // map<sint64, sint64> map_sint64_sint64 = 6;
  int map_sint64_sint64_size() const;
  void clear_map_sint64_sint64();
  static const int kMapSint64Sint64FieldNumber = 6;
  const ::google::protobuf::Map< ::google::protobuf::int64, ::google::protobuf::int64 >&
      map_sint64_sint64() const;
  ::google::protobuf::Map< ::google::protobuf::int64, ::google::protobuf::int64 >*
      mutable_map_sint64_sint64();

  // map<fixed32, fixed32> map_fixed32_fixed32 = 7;
  int map_fixed32_fixed32_size() const;
  void clear_map_fixed32_fixed32();
  static const int kMapFixed32Fixed32FieldNumber = 7;
  const ::google::protobuf::Map< ::google::protobuf::uint32, ::google::protobuf::uint32 >&
      map_fixed32_fixed32() const;
  ::google::protobuf::Map< ::google::protobuf::uint32, ::google::protobuf::uint32 >*
      mutable_map_fixed32_fixed32();

  // map<fixed64, fixed64> map_fixed64_fixed64 = 8;
  int map_fixed64_fixed64_size() const;
  void clear_map_fixed64_fixed64();
  static const int kMapFixed64Fixed64FieldNumber = 8;
  const ::google::protobuf::Map< ::google::protobuf::uint64, ::google::protobuf::uint64 >&
      map_fixed64_fixed64() const;
  ::google::protobuf::Map< ::google::protobuf::uint64, ::google::protobuf::uint64 >*
      mutable_map_fixed64_fixed64();

  // map<sfixed32, sfixed32> map_sfixed32_sfixed32 = 9;
  int map_sfixed32_sfixed32_size() const;
  void clear_map_sfixed32_sfixed32();
  static const int kMapSfixed32Sfixed32FieldNumber = 9;
  const ::google::protobuf::Map< ::google::protobuf::int32, ::google::protobuf::int32 >&
      map_sfixed32_sfixed32() const;
  ::google::protobuf::Map< ::google::protobuf::int32, ::google::protobuf::int32 >*
      mutable_map_sfixed32_sfixed32();

  // map<sfixed64, sfixed64> map_sfixed64_sfixed64 = 10;
  int map_sfixed64_sfixed64_size() const;
  void clear_map_sfixed64_sfixed64();
  static const int kMapSfixed64Sfixed64FieldNumber = 10;
  const ::google::protobuf::Map< ::google::protobuf::int64, ::google::protobuf::int64 >&
      map_sfixed64_sfixed64() const;
  ::google::protobuf::Map< ::google::protobuf::int64, ::google::protobuf::int64 >*
      mutable_map_sfixed64_sfixed64();

  // map<int32, float> map_int32_float = 11;
  int map_int32_float_size() const;
  void clear_map_int32_float();
  static const int kMapInt32FloatFieldNumber = 11;
  const ::google::protobuf::Map< ::google::protobuf::int32, float >&
      map_int32_float() const;
  ::google::protobuf::Map< ::google::protobuf::int32, float >*
      mutable_map_int32_float();

  // map<int32, double> map_int32_double = 12;
  int map_int32_double_size() const;
  void clear_map_int32_double();
  static const int kMapInt32DoubleFieldNumber = 12;
  const ::google::protobuf::Map< ::google::protobuf::int32, double >&
      map_int32_double() const;
  ::google::protobuf::Map< ::google::protobuf::int32, double >*
      mutable_map_int32_double();

  // map<bool, bool> map_bool_bool = 13;
  int map_bool_bool_size() const;
  void clear_map_bool_bool();
  static const int kMapBoolBoolFieldNumber = 13;
  const ::google::protobuf::Map< bool, bool >&
      map_bool_bool() const;
  ::google::protobuf::Map< bool, bool >*
      mutable_map_bool_bool();

  // map<string, string> map_string_string = 14;
  int map_string_string_size() const;
  void clear_map_string_string();
  static const int kMapStringStringFieldNumber = 14;
  const ::google::protobuf::Map< ::std::string, ::std::string >&
      map_string_string() const;
  ::google::protobuf::Map< ::std::string, ::std::string >*
      mutable_map_string_string();

  // map<int32, bytes> map_int32_bytes = 15;
  int map_int32_bytes_size() const;
  void clear_map_int32_bytes();
  static const int kMapInt32BytesFieldNumber = 15;
  const ::google::protobuf::Map< ::google::protobuf::int32, ::std::string >&
      map_int32_bytes() const;
  ::google::protobuf::Map< ::google::protobuf::int32, ::std::string >*
      mutable_map_int32_bytes();

  // map<int32, .protobuf_unittest.MapEnumLite> map_int32_enum = 16;
  int map_int32_enum_size() const;
  void clear_map_int32_enum();
  static const int kMapInt32EnumFieldNumber = 16;
  const ::google::protobuf::Map< ::google::protobuf::int32, ::protobuf_unittest::MapEnumLite >&
      map_int32_enum() const;
  ::google::protobuf::Map< ::google::protobuf::int32, ::protobuf_unittest::MapEnumLite >*
      mutable_map_int32_enum();

  // map<int32, .protobuf_unittest.ForeignMessageLite> map_int32_foreign_message = 17;
  int map_int32_foreign_message_size() const;
  void clear_map_int32_foreign_message();
  static const int kMapInt32ForeignMessageFieldNumber = 17;
  const ::google::protobuf::Map< ::google::protobuf::int32, ::protobuf_unittest::ForeignMessageLite >&
      map_int32_foreign_message() const;
  ::google::protobuf::Map< ::google::protobuf::int32, ::protobuf_unittest::ForeignMessageLite >*
      mutable_map_int32_foreign_message();

  // map<int32, int32> teboring = 18;
  int teboring_size() const;
  void clear_teboring();
  static const int kTeboringFieldNumber = 18;
  const ::google::protobuf::Map< ::google::protobuf::int32, ::google::protobuf::int32 >&
      teboring() const;
  ::google::protobuf::Map< ::google::protobuf::int32, ::google::protobuf::int32 >*
      mutable_teboring();

  // @@protoc_insertion_point(class_scope:protobuf_unittest.TestMapLite)
 private:

  ::google::protobuf::internal::ArenaStringPtr _unknown_fields_;
  ::google::protobuf::Arena* _arena_ptr_;

  friend class ::google::protobuf::Arena;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  ::google::protobuf::internal::HasBits<1> _has_bits_;
  mutable int _cached_size_;
  typedef ::google::protobuf::internal::MapEntryLite<
      ::google::protobuf::int32, ::google::protobuf::int32,
      ::google::protobuf::internal::WireFormatLite::TYPE_INT32,
      ::google::protobuf::internal::WireFormatLite::TYPE_INT32,
      0 >
      TestMapLite_MapInt32Int32Entry;
  ::google::protobuf::internal::MapFieldLite<
      ::google::protobuf::int32, ::google::protobuf::int32,
      ::google::protobuf::internal::WireFormatLite::TYPE_INT32,
      ::google::protobuf::internal::WireFormatLite::TYPE_INT32,
      0 > map_int32_int32_;
  typedef ::google::protobuf::internal::MapEntryLite<
      ::google::protobuf::int64, ::google::protobuf::int64,
      ::google::protobuf::internal::WireFormatLite::TYPE_INT64,
      ::google::protobuf::internal::WireFormatLite::TYPE_INT64,
      0 >
      TestMapLite_MapInt64Int64Entry;
  ::google::protobuf::internal::MapFieldLite<
      ::google::protobuf::int64, ::google::protobuf::int64,
      ::google::protobuf::internal::WireFormatLite::TYPE_INT64,
      ::google::protobuf::internal::WireFormatLite::TYPE_INT64,
      0 > map_int64_int64_;
  typedef ::google::protobuf::internal::MapEntryLite<
      ::google::protobuf::uint32, ::google::protobuf::uint32,
      ::google::protobuf::internal::WireFormatLite::TYPE_UINT32,
      ::google::protobuf::internal::WireFormatLite::TYPE_UINT32,
      0 >
      TestMapLite_MapUint32Uint32Entry;
  ::google::protobuf::internal::MapFieldLite<
      ::google::protobuf::uint32, ::google::protobuf::uint32,
      ::google::protobuf::internal::WireFormatLite::TYPE_UINT32,
      ::google::protobuf::internal::WireFormatLite::TYPE_UINT32,
      0 > map_uint32_uint32_;
  typedef ::google::protobuf::internal::MapEntryLite<
      ::google::protobuf::uint64, ::google::protobuf::uint64,
      ::google::protobuf::internal::WireFormatLite::TYPE_UINT64,
      ::google::protobuf::internal::WireFormatLite::TYPE_UINT64,
      0 >
      TestMapLite_MapUint64Uint64Entry;
  ::google::protobuf::internal::MapFieldLite<
      ::google::protobuf::uint64, ::google::protobuf::uint64,
      ::google::protobuf::internal::WireFormatLite::TYPE_UINT64,
      ::google::protobuf::internal::WireFormatLite::TYPE_UINT64,
      0 > map_uint64_uint64_;
  typedef ::google::protobuf::internal::MapEntryLite<
      ::google::protobuf::int32, ::google::protobuf::int32,
      ::google::protobuf::internal::WireFormatLite::TYPE_SINT32,
      ::google::protobuf::internal::WireFormatLite::TYPE_SINT32,
      0 >
      TestMapLite_MapSint32Sint32Entry;
  ::google::protobuf::internal::MapFieldLite<
      ::google::protobuf::int32, ::google::protobuf::int32,
      ::google::protobuf::internal::WireFormatLite::TYPE_SINT32,
      ::google::protobuf::internal::WireFormatLite::TYPE_SINT32,
      0 > map_sint32_sint32_;
  typedef ::google::protobuf::internal::MapEntryLite<
      ::google::protobuf::int64, ::google::protobuf::int64,
      ::google::protobuf::internal::WireFormatLite::TYPE_SINT64,
      ::google::protobuf::internal::WireFormatLite::TYPE_SINT64,
      0 >
      TestMapLite_MapSint64Sint64Entry;
  ::google::protobuf::internal::MapFieldLite<
      ::google::protobuf::int64, ::google::protobuf::int64,
      ::google::protobuf::internal::WireFormatLite::TYPE_SINT64,
      ::google::protobuf::internal::WireFormatLite::TYPE_SINT64,
      0 > map_sint64_sint64_;
  typedef ::google::protobuf::internal::MapEntryLite<
      ::google::protobuf::uint32, ::google::protobuf::uint32,
      ::google::protobuf::internal::WireFormatLite::TYPE_FIXED32,
      ::google::protobuf::internal::WireFormatLite::TYPE_FIXED32,
      0 >
      TestMapLite_MapFixed32Fixed32Entry;
  ::google::protobuf::internal::MapFieldLite<
      ::google::protobuf::uint32, ::google::protobuf::uint32,
      ::google::protobuf::internal::WireFormatLite::TYPE_FIXED32,
      ::google::protobuf::internal::WireFormatLite::TYPE_FIXED32,
      0 > map_fixed32_fixed32_;
  typedef ::google::protobuf::internal::MapEntryLite<
      ::google::protobuf::uint64, ::google::protobuf::uint64,
      ::google::protobuf::internal::WireFormatLite::TYPE_FIXED64,
      ::google::protobuf::internal::WireFormatLite::TYPE_FIXED64,
      0 >
      TestMapLite_MapFixed64Fixed64Entry;
  ::google::protobuf::internal::MapFieldLite<
      ::google::protobuf::uint64, ::google::protobuf::uint64,
      ::google::protobuf::internal::WireFormatLite::TYPE_FIXED64,
      ::google::protobuf::internal::WireFormatLite::TYPE_FIXED64,
      0 > map_fixed64_fixed64_;
  typedef ::google::protobuf::internal::MapEntryLite<
      ::google::protobuf::int32, ::google::protobuf::int32,
      ::google::protobuf::internal::WireFormatLite::TYPE_SFIXED32,
      ::google::protobuf::internal::WireFormatLite::TYPE_SFIXED32,
      0 >
      TestMapLite_MapSfixed32Sfixed32Entry;
  ::google::protobuf::internal::MapFieldLite<
      ::google::protobuf::int32, ::google::protobuf::int32,
      ::google::protobuf::internal::WireFormatLite::TYPE_SFIXED32,
      ::google::protobuf::internal::WireFormatLite::TYPE_SFIXED32,
      0 > map_sfixed32_sfixed32_;
  typedef ::google::protobuf::internal::MapEntryLite<
      ::google::protobuf::int64, ::google::protobuf::int64,
      ::google::protobuf::internal::WireFormatLite::TYPE_SFIXED64,
      ::google::protobuf::internal::WireFormatLite::TYPE_SFIXED64,
      0 >
      TestMapLite_MapSfixed64Sfixed64Entry;
  ::google::protobuf::internal::MapFieldLite<
      ::google::protobuf::int64, ::google::protobuf::int64,
      ::google::protobuf::internal::WireFormatLite::TYPE_SFIXED64,
      ::google::protobuf::internal::WireFormatLite::TYPE_SFIXED64,
      0 > map_sfixed64_sfixed64_;
  typedef ::google::protobuf::internal::MapEntryLite<
      ::google::protobuf::int32, float,
      ::google::protobuf::internal::WireFormatLite::TYPE_INT32,
      ::google::protobuf::internal::WireFormatLite::TYPE_FLOAT,
      0 >
      TestMapLite_MapInt32FloatEntry;
  ::google::protobuf::internal::MapFieldLite<
      ::google::protobuf::int32, float,
      ::google::protobuf::internal::WireFormatLite::TYPE_INT32,
      ::google::protobuf::internal::WireFormatLite::TYPE_FLOAT,
      0 > map_int32_float_;
  typedef ::google::protobuf::internal::MapEntryLite<
      ::google::protobuf::int32, double,
      ::google::protobuf::internal::WireFormatLite::TYPE_INT32,
      ::google::protobuf::internal::WireFormatLite::TYPE_DOUBLE,
      0 >
      TestMapLite_MapInt32DoubleEntry;
  ::google::protobuf::internal::MapFieldLite<
      ::google::protobuf::int32, double,
      ::google::protobuf::internal::WireFormatLite::TYPE_INT32,
      ::google::protobuf::internal::WireFormatLite::TYPE_DOUBLE,
      0 > map_int32_double_;
  typedef ::google::protobuf::internal::MapEntryLite<
      bool, bool,
      ::google::protobuf::internal::WireFormatLite::TYPE_BOOL,
      ::google::protobuf::internal::WireFormatLite::TYPE_BOOL,
      0 >
      TestMapLite_MapBoolBoolEntry;
  ::google::protobuf::internal::MapFieldLite<
      bool, bool,
      ::google::protobuf::internal::WireFormatLite::TYPE_BOOL,
      ::google::protobuf::internal::WireFormatLite::TYPE_BOOL,
      0 > map_bool_bool_;
  typedef ::google::protobuf::internal::MapEntryLite<
      ::std::string, ::std::string,
      ::google::protobuf::internal::WireFormatLite::TYPE_STRING,
      ::google::protobuf::internal::WireFormatLite::TYPE_STRING,
      0 >
      TestMapLite_MapStringStringEntry;
  ::google::protobuf::internal::MapFieldLite<
      ::std::string, ::std::string,
      ::google::protobuf::internal::WireFormatLite::TYPE_STRING,
      ::google::protobuf::internal::WireFormatLite::TYPE_STRING,
      0 > map_string_string_;
  typedef ::google::protobuf::internal::MapEntryLite<
      ::google::protobuf::int32, ::std::string,
      ::google::protobuf::internal::WireFormatLite::TYPE_INT32,
      ::google::protobuf::internal::WireFormatLite::TYPE_BYTES,
      0 >
      TestMapLite_MapInt32BytesEntry;
  ::google::protobuf::internal::MapFieldLite<
      ::google::protobuf::int32, ::std::string,
      ::google::protobuf::internal::WireFormatLite::TYPE_INT32,
      ::google::protobuf::internal::WireFormatLite::TYPE_BYTES,
      0 > map_int32_bytes_;
  typedef ::google::protobuf::internal::MapEntryLite<
      ::google::protobuf::int32, ::protobuf_unittest::MapEnumLite,
      ::google::protobuf::internal::WireFormatLite::TYPE_INT32,
      ::google::protobuf::internal::WireFormatLite::TYPE_ENUM,
      0 >
      TestMapLite_MapInt32EnumEntry;
  ::google::protobuf::internal::MapFieldLite<
      ::google::protobuf::int32, ::protobuf_unittest::MapEnumLite,
      ::google::protobuf::internal::WireFormatLite::TYPE_INT32,
      ::google::protobuf::internal::WireFormatLite::TYPE_ENUM,
      0 > map_int32_enum_;
  typedef ::google::protobuf::internal::MapEntryLite<
      ::google::protobuf::int32, ::protobuf_unittest::ForeignMessageLite,
      ::google::protobuf::internal::WireFormatLite::TYPE_INT32,
      ::google::protobuf::internal::WireFormatLite::TYPE_MESSAGE,
      0 >
      TestMapLite_MapInt32ForeignMessageEntry;
  ::google::protobuf::internal::MapFieldLite<
      ::google::protobuf::int32, ::protobuf_unittest::ForeignMessageLite,
      ::google::protobuf::internal::WireFormatLite::TYPE_INT32,
      ::google::protobuf::internal::WireFormatLite::TYPE_MESSAGE,
      0 > map_int32_foreign_message_;
  typedef ::google::protobuf::internal::MapEntryLite<
      ::google::protobuf::int32, ::google::protobuf::int32,
      ::google::protobuf::internal::WireFormatLite::TYPE_INT32,
      ::google::protobuf::internal::WireFormatLite::TYPE_INT32,
      0 >
      TestMapLite_TeboringEntry;
  ::google::protobuf::internal::MapFieldLite<
      ::google::protobuf::int32, ::google::protobuf::int32,
      ::google::protobuf::internal::WireFormatLite::TYPE_INT32,
      ::google::protobuf::internal::WireFormatLite::TYPE_INT32,
      0 > teboring_;
  friend void  protobuf_InitDefaults_google_2fprotobuf_2fmap_5flite_5funittest_2eproto_impl();
  friend void  protobuf_AddDesc_google_2fprotobuf_2fmap_5flite_5funittest_2eproto_impl();
  friend void protobuf_AssignDesc_google_2fprotobuf_2fmap_5flite_5funittest_2eproto();
  friend void protobuf_ShutdownFile_google_2fprotobuf_2fmap_5flite_5funittest_2eproto();

  void InitAsDefaultInstance();
};
extern ::google::protobuf::internal::ExplicitlyConstructed<TestMapLite> TestMapLite_default_instance_;

// -------------------------------------------------------------------

class TestArenaMapLite : public ::google::protobuf::MessageLite /* @@protoc_insertion_point(class_definition:protobuf_unittest.TestArenaMapLite) */ {
 public:
  TestArenaMapLite();
  virtual ~TestArenaMapLite();

  TestArenaMapLite(const TestArenaMapLite& from);

  inline TestArenaMapLite& operator=(const TestArenaMapLite& from) {
    CopyFrom(from);
    return *this;
  }

  inline const ::std::string& unknown_fields() const {
    return _unknown_fields_.Get(
        &::google::protobuf::internal::GetEmptyStringAlreadyInited());
  }

  inline ::std::string* mutable_unknown_fields() {
    return _unknown_fields_.Mutable(
        &::google::protobuf::internal::GetEmptyStringAlreadyInited(),
        GetArenaNoVirtual());
  }

  inline ::google::protobuf::Arena* GetArena() const { return GetArenaNoVirtual(); }
  inline void* GetMaybeArenaPointer() const {
    return MaybeArenaPtr();
  }
  static const TestArenaMapLite& default_instance();

  static const TestArenaMapLite* internal_default_instance();

  void UnsafeArenaSwap(TestArenaMapLite* other);
  void Swap(TestArenaMapLite* other);

  // implements Message ----------------------------------------------

  inline TestArenaMapLite* New() const { return New(NULL); }

  TestArenaMapLite* New(::google::protobuf::Arena* arena) const;
  void CheckTypeAndMergeFrom(const ::google::protobuf::MessageLite& from);
  void CopyFrom(const TestArenaMapLite& from);
  void MergeFrom(const TestArenaMapLite& from);
  void Clear();
  bool IsInitialized() const;

  size_t ByteSizeLong() const;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input);
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const;
  void DiscardUnknownFields();
  int GetCachedSize() const { return _cached_size_; }
  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const;
  void InternalSwap(TestArenaMapLite* other);
  void UnsafeMergeFrom(const TestArenaMapLite& from);
  protected:
  explicit TestArenaMapLite(::google::protobuf::Arena* arena);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::google::protobuf::Arena* arena);
  private:
  inline ::google::protobuf::Arena* GetArenaNoVirtual() const {
    return _arena_ptr_;
  }
  inline ::google::protobuf::Arena* MaybeArenaPtr() const {
    return _arena_ptr_;
  }
  public:

  ::std::string GetTypeName() const;

  // nested types ----------------------------------------------------


  // accessors -------------------------------------------------------

  // map<int32, int32> map_int32_int32 = 1;
  int map_int32_int32_size() const;
  void clear_map_int32_int32();
  static const int kMapInt32Int32FieldNumber = 1;
  const ::google::protobuf::Map< ::google::protobuf::int32, ::google::protobuf::int32 >&
      map_int32_int32() const;
  ::google::protobuf::Map< ::google::protobuf::int32, ::google::protobuf::int32 >*
      mutable_map_int32_int32();

  // map<int64, int64> map_int64_int64 = 2;
  int map_int64_int64_size() const;
  void clear_map_int64_int64();
  static const int kMapInt64Int64FieldNumber = 2;
  const ::google::protobuf::Map< ::google::protobuf::int64, ::google::protobuf::int64 >&
      map_int64_int64() const;
  ::google::protobuf::Map< ::google::protobuf::int64, ::google::protobuf::int64 >*
      mutable_map_int64_int64();

  // map<uint32, uint32> map_uint32_uint32 = 3;
  int map_uint32_uint32_size() const;
  void clear_map_uint32_uint32();
  static const int kMapUint32Uint32FieldNumber = 3;
  const ::google::protobuf::Map< ::google::protobuf::uint32, ::google::protobuf::uint32 >&
      map_uint32_uint32() const;
  ::google::protobuf::Map< ::google::protobuf::uint32, ::google::protobuf::uint32 >*
      mutable_map_uint32_uint32();

  // map<uint64, uint64> map_uint64_uint64 = 4;
  int map_uint64_uint64_size() const;
  void clear_map_uint64_uint64();
  static const int kMapUint64Uint64FieldNumber = 4;
  const ::google::protobuf::Map< ::google::protobuf::uint64, ::google::protobuf::uint64 >&
      map_uint64_uint64() const;
  ::google::protobuf::Map< ::google::protobuf::uint64, ::google::protobuf::uint64 >*
      mutable_map_uint64_uint64();

  // map<sint32, sint32> map_sint32_sint32 = 5;
  int map_sint32_sint32_size() const;
  void clear_map_sint32_sint32();
  static const int kMapSint32Sint32FieldNumber = 5;
  const ::google::protobuf::Map< ::google::protobuf::int32, ::google::protobuf::int32 >&
      map_sint32_sint32() const;
  ::google::protobuf::Map< ::google::protobuf::int32, ::google::protobuf::int32 >*
      mutable_map_sint32_sint32();

  // map<sint64, sint64> map_sint64_sint64 = 6;
  int map_sint64_sint64_size() const;
  void clear_map_sint64_sint64();
  static const int kMapSint64Sint64FieldNumber = 6;
  const ::google::protobuf::Map< ::google::protobuf::int64, ::google::protobuf::int64 >&
      map_sint64_sint64() const;
  ::google::protobuf::Map< ::google::protobuf::int64, ::google::protobuf::int64 >*
      mutable_map_sint64_sint64();

  // map<fixed32, fixed32> map_fixed32_fixed32 = 7;
  int map_fixed32_fixed32_size() const;
  void clear_map_fixed32_fixed32();
  static const int kMapFixed32Fixed32FieldNumber = 7;
  const ::google::protobuf::Map< ::google::protobuf::uint32, ::google::protobuf::uint32 >&
      map_fixed32_fixed32() const;
  ::google::protobuf::Map< ::google::protobuf::uint32, ::google::protobuf::uint32 >*
      mutable_map_fixed32_fixed32();

  // map<fixed64, fixed64> map_fixed64_fixed64 = 8;
  int map_fixed64_fixed64_size() const;
  void clear_map_fixed64_fixed64();
  static const int kMapFixed64Fixed64FieldNumber = 8;
  const ::google::protobuf::Map< ::google::protobuf::uint64, ::google::protobuf::uint64 >&
      map_fixed64_fixed64() const;
  ::google::protobuf::Map< ::google::protobuf::uint64, ::google::protobuf::uint64 >*
      mutable_map_fixed64_fixed64();

  // map<sfixed32, sfixed32> map_sfixed32_sfixed32 = 9;
  int map_sfixed32_sfixed32_size() const;
  void clear_map_sfixed32_sfixed32();
  static const int kMapSfixed32Sfixed32FieldNumber = 9;
  const ::google::protobuf::Map< ::google::protobuf::int32, ::google::protobuf::int32 >&
      map_sfixed32_sfixed32() const;
  ::google::protobuf::Map< ::google::protobuf::int32, ::google::protobuf::int32 >*
      mutable_map_sfixed32_sfixed32();

  // map<sfixed64, sfixed64> map_sfixed64_sfixed64 = 10;
  int map_sfixed64_sfixed64_size() const;
  void clear_map_sfixed64_sfixed64();
  static const int kMapSfixed64Sfixed64FieldNumber = 10;
  const ::google::protobuf::Map< ::google::protobuf::int64, ::google::protobuf::int64 >&
      map_sfixed64_sfixed64() const;
  ::google::protobuf::Map< ::google::protobuf::int64, ::google::protobuf::int64 >*
      mutable_map_sfixed64_sfixed64();

  // map<int32, float> map_int32_float = 11;
  int map_int32_float_size() const;
  void clear_map_int32_float();
  static const int kMapInt32FloatFieldNumber = 11;
  const ::google::protobuf::Map< ::google::protobuf::int32, float >&
      map_int32_float() const;
  ::google::protobuf::Map< ::google::protobuf::int32, float >*
      mutable_map_int32_float();

  // map<int32, double> map_int32_double = 12;
  int map_int32_double_size() const;
  void clear_map_int32_double();
  static const int kMapInt32DoubleFieldNumber = 12;
  const ::google::protobuf::Map< ::google::protobuf::int32, double >&
      map_int32_double() const;
  ::google::protobuf::Map< ::google::protobuf::int32, double >*
      mutable_map_int32_double();

  // map<bool, bool> map_bool_bool = 13;
  int map_bool_bool_size() const;
  void clear_map_bool_bool();
  static const int kMapBoolBoolFieldNumber = 13;
  const ::google::protobuf::Map< bool, bool >&
      map_bool_bool() const;
  ::google::protobuf::Map< bool, bool >*
      mutable_map_bool_bool();

  // map<string, string> map_string_string = 14;
  int map_string_string_size() const;
  void clear_map_string_string();
  static const int kMapStringStringFieldNumber = 14;
  const ::google::protobuf::Map< ::std::string, ::std::string >&
      map_string_string() const;
  ::google::protobuf::Map< ::std::string, ::std::string >*
      mutable_map_string_string();

  // map<int32, bytes> map_int32_bytes = 15;
  int map_int32_bytes_size() const;
  void clear_map_int32_bytes();
  static const int kMapInt32BytesFieldNumber = 15;
  const ::google::protobuf::Map< ::google::protobuf::int32, ::std::string >&
      map_int32_bytes() const;
  ::google::protobuf::Map< ::google::protobuf::int32, ::std::string >*
      mutable_map_int32_bytes();

  // map<int32, .protobuf_unittest.MapEnumLite> map_int32_enum = 16;
  int map_int32_enum_size() const;
  void clear_map_int32_enum();
  static const int kMapInt32EnumFieldNumber = 16;
  const ::google::protobuf::Map< ::google::protobuf::int32, ::protobuf_unittest::MapEnumLite >&
      map_int32_enum() const;
  ::google::protobuf::Map< ::google::protobuf::int32, ::protobuf_unittest::MapEnumLite >*
      mutable_map_int32_enum();

  // map<int32, .protobuf_unittest.ForeignMessageArenaLite> map_int32_foreign_message = 17;
  int map_int32_foreign_message_size() const;
  void clear_map_int32_foreign_message();
  static const int kMapInt32ForeignMessageFieldNumber = 17;
  const ::google::protobuf::Map< ::google::protobuf::int32, ::protobuf_unittest::ForeignMessageArenaLite >&
      map_int32_foreign_message() const;
  ::google::protobuf::Map< ::google::protobuf::int32, ::protobuf_unittest::ForeignMessageArenaLite >*
      mutable_map_int32_foreign_message();

  // map<int32, .protobuf_unittest_no_arena.ForeignMessageLite> map_int32_foreign_message_no_arena = 18;
  int map_int32_foreign_message_no_arena_size() const;
  void clear_map_int32_foreign_message_no_arena();
  static const int kMapInt32ForeignMessageNoArenaFieldNumber = 18;
  const ::google::protobuf::Map< ::google::protobuf::int32, ::protobuf_unittest_no_arena::ForeignMessageLite >&
      map_int32_foreign_message_no_arena() const;
  ::google::protobuf::Map< ::google::protobuf::int32, ::protobuf_unittest_no_arena::ForeignMessageLite >*
      mutable_map_int32_foreign_message_no_arena();

  // @@protoc_insertion_point(class_scope:protobuf_unittest.TestArenaMapLite)
 private:

  ::google::protobuf::internal::ArenaStringPtr _unknown_fields_;
  ::google::protobuf::Arena* _arena_ptr_;

  friend class ::google::protobuf::Arena;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  ::google::protobuf::internal::HasBits<1> _has_bits_;
  mutable int _cached_size_;
  typedef ::google::protobuf::internal::MapEntryLite<
      ::google::protobuf::int32, ::google::protobuf::int32,
      ::google::protobuf::internal::WireFormatLite::TYPE_INT32,
      ::google::protobuf::internal::WireFormatLite::TYPE_INT32,
      0 >
      TestArenaMapLite_MapInt32Int32Entry;
  ::google::protobuf::internal::MapFieldLite<
      ::google::protobuf::int32, ::google::protobuf::int32,
      ::google::protobuf::internal::WireFormatLite::TYPE_INT32,
      ::google::protobuf::internal::WireFormatLite::TYPE_INT32,
      0 > map_int32_int32_;
  typedef ::google::protobuf::internal::MapEntryLite<
      ::google::protobuf::int64, ::google::protobuf::int64,
      ::google::protobuf::internal::WireFormatLite::TYPE_INT64,
      ::google::protobuf::internal::WireFormatLite::TYPE_INT64,
      0 >
      TestArenaMapLite_MapInt64Int64Entry;
  ::google::protobuf::internal::MapFieldLite<
      ::google::protobuf::int64, ::google::protobuf::int64,
      ::google::protobuf::internal::WireFormatLite::TYPE_INT64,
      ::google::protobuf::internal::WireFormatLite::TYPE_INT64,
      0 > map_int64_int64_;
  typedef ::google::protobuf::internal::MapEntryLite<
      ::google::protobuf::uint32, ::google::protobuf::uint32,
      ::google::protobuf::internal::WireFormatLite::TYPE_UINT32,
      ::google::protobuf::internal::WireFormatLite::TYPE_UINT32,
      0 >
      TestArenaMapLite_MapUint32Uint32Entry;
  ::google::protobuf::internal::MapFieldLite<
      ::google::protobuf::uint32, ::google::protobuf::uint32,
      ::google::protobuf::internal::WireFormatLite::TYPE_UINT32,
      ::google::protobuf::internal::WireFormatLite::TYPE_UINT32,
      0 > map_uint32_uint32_;
  typedef ::google::protobuf::internal::MapEntryLite<
      ::google::protobuf::uint64, ::google::protobuf::uint64,
      ::google::protobuf::internal::WireFormatLite::TYPE_UINT64,
      ::google::protobuf::internal::WireFormatLite::TYPE_UINT64,
      0 >
      TestArenaMapLite_MapUint64Uint64Entry;
  ::google::protobuf::internal::MapFieldLite<
      ::google::protobuf::uint64, ::google::protobuf::uint64,
      ::google::protobuf::internal::WireFormatLite::TYPE_UINT64,
      ::google::protobuf::internal::WireFormatLite::TYPE_UINT64,
      0 > map_uint64_uint64_;
  typedef ::google::protobuf::internal::MapEntryLite<
      ::google::protobuf::int32, ::google::protobuf::int32,
      ::google::protobuf::internal::WireFormatLite::TYPE_SINT32,
      ::google::protobuf::internal::WireFormatLite::TYPE_SINT32,
      0 >
      TestArenaMapLite_MapSint32Sint32Entry;
  ::google::protobuf::internal::MapFieldLite<
      ::google::protobuf::int32, ::google::protobuf::int32,
      ::google::protobuf::internal::WireFormatLite::TYPE_SINT32,
      ::google::protobuf::internal::WireFormatLite::TYPE_SINT32,
      0 > map_sint32_sint32_;
  typedef ::google::protobuf::internal::MapEntryLite<
      ::google::protobuf::int64, ::google::protobuf::int64,
      ::google::protobuf::internal::WireFormatLite::TYPE_SINT64,
      ::google::protobuf::internal::WireFormatLite::TYPE_SINT64,
      0 >
      TestArenaMapLite_MapSint64Sint64Entry;
  ::google::protobuf::internal::MapFieldLite<
      ::google::protobuf::int64, ::google::protobuf::int64,
      ::google::protobuf::internal::WireFormatLite::TYPE_SINT64,
      ::google::protobuf::internal::WireFormatLite::TYPE_SINT64,
      0 > map_sint64_sint64_;
  typedef ::google::protobuf::internal::MapEntryLite<
      ::google::protobuf::uint32, ::google::protobuf::uint32,
      ::google::protobuf::internal::WireFormatLite::TYPE_FIXED32,
      ::google::protobuf::internal::WireFormatLite::TYPE_FIXED32,
      0 >
      TestArenaMapLite_MapFixed32Fixed32Entry;
  ::google::protobuf::internal::MapFieldLite<
      ::google::protobuf::uint32, ::google::protobuf::uint32,
      ::google::protobuf::internal::WireFormatLite::TYPE_FIXED32,
      ::google::protobuf::internal::WireFormatLite::TYPE_FIXED32,
      0 > map_fixed32_fixed32_;
  typedef ::google::protobuf::internal::MapEntryLite<
      ::google::protobuf::uint64, ::google::protobuf::uint64,
      ::google::protobuf::internal::WireFormatLite::TYPE_FIXED64,
      ::google::protobuf::internal::WireFormatLite::TYPE_FIXED64,
      0 >
      TestArenaMapLite_MapFixed64Fixed64Entry;
  ::google::protobuf::internal::MapFieldLite<
      ::google::protobuf::uint64, ::google::protobuf::uint64,
      ::google::protobuf::internal::WireFormatLite::TYPE_FIXED64,
      ::google::protobuf::internal::WireFormatLite::TYPE_FIXED64,
      0 > map_fixed64_fixed64_;
  typedef ::google::protobuf::internal::MapEntryLite<
      ::google::protobuf::int32, ::google::protobuf::int32,
      ::google::protobuf::internal::WireFormatLite::TYPE_SFIXED32,
      ::google::protobuf::internal::WireFormatLite::TYPE_SFIXED32,
      0 >
      TestArenaMapLite_MapSfixed32Sfixed32Entry;
  ::google::protobuf::internal::MapFieldLite<
      ::google::protobuf::int32, ::google::protobuf::int32,
      ::google::protobuf::internal::WireFormatLite::TYPE_SFIXED32,
      ::google::protobuf::internal::WireFormatLite::TYPE_SFIXED32,
      0 > map_sfixed32_sfixed32_;
  typedef ::google::protobuf::internal::MapEntryLite<
      ::google::protobuf::int64, ::google::protobuf::int64,
      ::google::protobuf::internal::WireFormatLite::TYPE_SFIXED64,
      ::google::protobuf::internal::WireFormatLite::TYPE_SFIXED64,
      0 >
      TestArenaMapLite_MapSfixed64Sfixed64Entry;
  ::google::protobuf::internal::MapFieldLite<
      ::google::protobuf::int64, ::google::protobuf::int64,
      ::google::protobuf::internal::WireFormatLite::TYPE_SFIXED64,
      ::google::protobuf::internal::WireFormatLite::TYPE_SFIXED64,
      0 > map_sfixed64_sfixed64_;
  typedef ::google::protobuf::internal::MapEntryLite<
      ::google::protobuf::int32, float,
      ::google::protobuf::internal::WireFormatLite::TYPE_INT32,
      ::google::protobuf::internal::WireFormatLite::TYPE_FLOAT,
      0 >
      TestArenaMapLite_MapInt32FloatEntry;
  ::google::protobuf::internal::MapFieldLite<
      ::google::protobuf::int32, float,
      ::google::protobuf::internal::WireFormatLite::TYPE_INT32,
      ::google::protobuf::internal::WireFormatLite::TYPE_FLOAT,
      0 > map_int32_float_;
  typedef ::google::protobuf::internal::MapEntryLite<
      ::google::protobuf::int32, double,
      ::google::protobuf::internal::WireFormatLite::TYPE_INT32,
      ::google::protobuf::internal::WireFormatLite::TYPE_DOUBLE,
      0 >
      TestArenaMapLite_MapInt32DoubleEntry;
  ::google::protobuf::internal::MapFieldLite<
      ::google::protobuf::int32, double,
      ::google::protobuf::internal::WireFormatLite::TYPE_INT32,
      ::google::protobuf::internal::WireFormatLite::TYPE_DOUBLE,
      0 > map_int32_double_;
  typedef ::google::protobuf::internal::MapEntryLite<
      bool, bool,
      ::google::protobuf::internal::WireFormatLite::TYPE_BOOL,
      ::google::protobuf::internal::WireFormatLite::TYPE_BOOL,
      0 >
      TestArenaMapLite_MapBoolBoolEntry;
  ::google::protobuf::internal::MapFieldLite<
      bool, bool,
      ::google::protobuf::internal::WireFormatLite::TYPE_BOOL,
      ::google::protobuf::internal::WireFormatLite::TYPE_BOOL,
      0 > map_bool_bool_;
  typedef ::google::protobuf::internal::MapEntryLite<
      ::std::string, ::std::string,
      ::google::protobuf::internal::WireFormatLite::TYPE_STRING,
      ::google::protobuf::internal::WireFormatLite::TYPE_STRING,
      0 >
      TestArenaMapLite_MapStringStringEntry;
  ::google::protobuf::internal::MapFieldLite<
      ::std::string, ::std::string,
      ::google::protobuf::internal::WireFormatLite::TYPE_STRING,
      ::google::protobuf::internal::WireFormatLite::TYPE_STRING,
      0 > map_string_string_;
  typedef ::google::protobuf::internal::MapEntryLite<
      ::google::protobuf::int32, ::std::string,
      ::google::protobuf::internal::WireFormatLite::TYPE_INT32,
      ::google::protobuf::internal::WireFormatLite::TYPE_BYTES,
      0 >
      TestArenaMapLite_MapInt32BytesEntry;
  ::google::protobuf::internal::MapFieldLite<
      ::google::protobuf::int32, ::std::string,
      ::google::protobuf::internal::WireFormatLite::TYPE_INT32,
      ::google::protobuf::internal::WireFormatLite::TYPE_BYTES,
      0 > map_int32_bytes_;
  typedef ::google::protobuf::internal::MapEntryLite<
      ::google::protobuf::int32, ::protobuf_unittest::MapEnumLite,
      ::google::protobuf::internal::WireFormatLite::TYPE_INT32,
      ::google::protobuf::internal::WireFormatLite::TYPE_ENUM,
      0 >
      TestArenaMapLite_MapInt32EnumEntry;
  ::google::protobuf::internal::MapFieldLite<
      ::google::protobuf::int32, ::protobuf_unittest::MapEnumLite,
      ::google::protobuf::internal::WireFormatLite::TYPE_INT32,
      ::google::protobuf::internal::WireFormatLite::TYPE_ENUM,
      0 > map_int32_enum_;
  typedef ::google::protobuf::internal::MapEntryLite<
      ::google::protobuf::int32, ::protobuf_unittest::ForeignMessageArenaLite,
      ::google::protobuf::internal::WireFormatLite::TYPE_INT32,
      ::google::protobuf::internal::WireFormatLite::TYPE_MESSAGE,
      0 >
      TestArenaMapLite_MapInt32ForeignMessageEntry;
  ::google::protobuf::internal::MapFieldLite<
      ::google::protobuf::int32, ::protobuf_unittest::ForeignMessageArenaLite,
      ::google::protobuf::internal::WireFormatLite::TYPE_INT32,
      ::google::protobuf::internal::WireFormatLite::TYPE_MESSAGE,
      0 > map_int32_foreign_message_;
  typedef ::google::protobuf::internal::MapEntryLite<
      ::google::protobuf::int32, ::protobuf_unittest_no_arena::ForeignMessageLite,
      ::google::protobuf::internal::WireFormatLite::TYPE_INT32,
      ::google::protobuf::internal::WireFormatLite::TYPE_MESSAGE,
      0 >
      TestArenaMapLite_MapInt32ForeignMessageNoArenaEntry;
  ::google::protobuf::internal::MapFieldLite<
      ::google::protobuf::int32, ::protobuf_unittest_no_arena::ForeignMessageLite,
      ::google::protobuf::internal::WireFormatLite::TYPE_INT32,
      ::google::protobuf::internal::WireFormatLite::TYPE_MESSAGE,
      0 > map_int32_foreign_message_no_arena_;
  friend void  protobuf_InitDefaults_google_2fprotobuf_2fmap_5flite_5funittest_2eproto_impl();
  friend void  protobuf_AddDesc_google_2fprotobuf_2fmap_5flite_5funittest_2eproto_impl();
  friend void protobuf_AssignDesc_google_2fprotobuf_2fmap_5flite_5funittest_2eproto();
  friend void protobuf_ShutdownFile_google_2fprotobuf_2fmap_5flite_5funittest_2eproto();

  void InitAsDefaultInstance();
};
extern ::google::protobuf::internal::ExplicitlyConstructed<TestArenaMapLite> TestArenaMapLite_default_instance_;

// -------------------------------------------------------------------

class TestRequiredMessageMapLite : public ::google::protobuf::MessageLite /* @@protoc_insertion_point(class_definition:protobuf_unittest.TestRequiredMessageMapLite) */ {
 public:
  TestRequiredMessageMapLite();
  virtual ~TestRequiredMessageMapLite();

  TestRequiredMessageMapLite(const TestRequiredMessageMapLite& from);

  inline TestRequiredMessageMapLite& operator=(const TestRequiredMessageMapLite& from) {
    CopyFrom(from);
    return *this;
  }

  inline const ::std::string& unknown_fields() const {
    return _unknown_fields_.Get(
        &::google::protobuf::internal::GetEmptyStringAlreadyInited());
  }

  inline ::std::string* mutable_unknown_fields() {
    return _unknown_fields_.Mutable(
        &::google::protobuf::internal::GetEmptyStringAlreadyInited(),
        GetArenaNoVirtual());
  }

  inline ::google::protobuf::Arena* GetArena() const { return GetArenaNoVirtual(); }
  inline void* GetMaybeArenaPointer() const {
    return MaybeArenaPtr();
  }
  static const TestRequiredMessageMapLite& default_instance();

  static const TestRequiredMessageMapLite* internal_default_instance();

  void UnsafeArenaSwap(TestRequiredMessageMapLite* other);
  void Swap(TestRequiredMessageMapLite* other);

  // implements Message ----------------------------------------------

  inline TestRequiredMessageMapLite* New() const { return New(NULL); }

  TestRequiredMessageMapLite* New(::google::protobuf::Arena* arena) const;
  void CheckTypeAndMergeFrom(const ::google::protobuf::MessageLite& from);
  void CopyFrom(const TestRequiredMessageMapLite& from);
  void MergeFrom(const TestRequiredMessageMapLite& from);
  void Clear();
  bool IsInitialized() const;

  size_t ByteSizeLong() const;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input);
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const;
  void DiscardUnknownFields();
  int GetCachedSize() const { return _cached_size_; }
  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const;
  void InternalSwap(TestRequiredMessageMapLite* other);
  void UnsafeMergeFrom(const TestRequiredMessageMapLite& from);
  protected:
  explicit TestRequiredMessageMapLite(::google::protobuf::Arena* arena);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::google::protobuf::Arena* arena);
  private:
  inline ::google::protobuf::Arena* GetArenaNoVirtual() const {
    return _arena_ptr_;
  }
  inline ::google::protobuf::Arena* MaybeArenaPtr() const {
    return _arena_ptr_;
  }
  public:

  ::std::string GetTypeName() const;

  // nested types ----------------------------------------------------


  // accessors -------------------------------------------------------

  // map<int32, .protobuf_unittest.TestRequiredLite> map_field = 1;
  int map_field_size() const;
  void clear_map_field();
  static const int kMapFieldFieldNumber = 1;
  const ::google::protobuf::Map< ::google::protobuf::int32, ::protobuf_unittest::TestRequiredLite >&
      map_field() const;
  ::google::protobuf::Map< ::google::protobuf::int32, ::protobuf_unittest::TestRequiredLite >*
      mutable_map_field();

  // @@protoc_insertion_point(class_scope:protobuf_unittest.TestRequiredMessageMapLite)
 private:

  ::google::protobuf::internal::ArenaStringPtr _unknown_fields_;
  ::google::protobuf::Arena* _arena_ptr_;

  friend class ::google::protobuf::Arena;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  ::google::protobuf::internal::HasBits<1> _has_bits_;
  mutable int _cached_size_;
  typedef ::google::protobuf::internal::MapEntryLite<
      ::google::protobuf::int32, ::protobuf_unittest::TestRequiredLite,
      ::google::protobuf::internal::WireFormatLite::TYPE_INT32,
      ::google::protobuf::internal::WireFormatLite::TYPE_MESSAGE,
      0 >
      TestRequiredMessageMapLite_MapFieldEntry;
  ::google::protobuf::internal::MapFieldLite<
      ::google::protobuf::int32, ::protobuf_unittest::TestRequiredLite,
      ::google::protobuf::internal::WireFormatLite::TYPE_INT32,
      ::google::protobuf::internal::WireFormatLite::TYPE_MESSAGE,
      0 > map_field_;
  friend void  protobuf_InitDefaults_google_2fprotobuf_2fmap_5flite_5funittest_2eproto_impl();
  friend void  protobuf_AddDesc_google_2fprotobuf_2fmap_5flite_5funittest_2eproto_impl();
  friend void protobuf_AssignDesc_google_2fprotobuf_2fmap_5flite_5funittest_2eproto();
  friend void protobuf_ShutdownFile_google_2fprotobuf_2fmap_5flite_5funittest_2eproto();

  void InitAsDefaultInstance();
};
extern ::google::protobuf::internal::ExplicitlyConstructed<TestRequiredMessageMapLite> TestRequiredMessageMapLite_default_instance_;

// -------------------------------------------------------------------

class TestEnumMapLite : public ::google::protobuf::MessageLite /* @@protoc_insertion_point(class_definition:protobuf_unittest.TestEnumMapLite) */ {
 public:
  TestEnumMapLite();
  virtual ~TestEnumMapLite();

  TestEnumMapLite(const TestEnumMapLite& from);

  inline TestEnumMapLite& operator=(const TestEnumMapLite& from) {
    CopyFrom(from);
    return *this;
  }

  inline const ::std::string& unknown_fields() const {
    return _unknown_fields_.Get(
        &::google::protobuf::internal::GetEmptyStringAlreadyInited());
  }

  inline ::std::string* mutable_unknown_fields() {
    return _unknown_fields_.Mutable(
        &::google::protobuf::internal::GetEmptyStringAlreadyInited(),
        GetArenaNoVirtual());
  }

  inline ::google::protobuf::Arena* GetArena() const { return GetArenaNoVirtual(); }
  inline void* GetMaybeArenaPointer() const {
    return MaybeArenaPtr();
  }
  static const TestEnumMapLite& default_instance();

  static const TestEnumMapLite* internal_default_instance();

  void UnsafeArenaSwap(TestEnumMapLite* other);
  void Swap(TestEnumMapLite* other);

  // implements Message ----------------------------------------------

  inline TestEnumMapLite* New() const { return New(NULL); }

  TestEnumMapLite* New(::google::protobuf::Arena* arena) const;
  void CheckTypeAndMergeFrom(const ::google::protobuf::MessageLite& from);
  void CopyFrom(const TestEnumMapLite& from);
  void MergeFrom(const TestEnumMapLite& from);
  void Clear();
  bool IsInitialized() const;

  size_t ByteSizeLong() const;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input);
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const;
  void DiscardUnknownFields();
  int GetCachedSize() const { return _cached_size_; }
  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const;
  void InternalSwap(TestEnumMapLite* other);
  void UnsafeMergeFrom(const TestEnumMapLite& from);
  protected:
  explicit TestEnumMapLite(::google::protobuf::Arena* arena);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::google::protobuf::Arena* arena);
  private:
  inline ::google::protobuf::Arena* GetArenaNoVirtual() const {
    return _arena_ptr_;
  }
  inline ::google::protobuf::Arena* MaybeArenaPtr() const {
    return _arena_ptr_;
  }
  public:

  ::std::string GetTypeName() const;

  // nested types ----------------------------------------------------


  // accessors -------------------------------------------------------

  // map<int32, .protobuf_unittest.Proto2MapEnumLite> known_map_field = 101;
  int known_map_field_size() const;
  void clear_known_map_field();
  static const int kKnownMapFieldFieldNumber = 101;
  const ::google::protobuf::Map< ::google::protobuf::int32, ::protobuf_unittest::Proto2MapEnumLite >&
      known_map_field() const;
  ::google::protobuf::Map< ::google::protobuf::int32, ::protobuf_unittest::Proto2MapEnumLite >*
      mutable_known_map_field();

  // map<int32, .protobuf_unittest.Proto2MapEnumLite> unknown_map_field = 102;
  int unknown_map_field_size() const;
  void clear_unknown_map_field();
  static const int kUnknownMapFieldFieldNumber = 102;
  const ::google::protobuf::Map< ::google::protobuf::int32, ::protobuf_unittest::Proto2MapEnumLite >&
      unknown_map_field() const;
  ::google::protobuf::Map< ::google::protobuf::int32, ::protobuf_unittest::Proto2MapEnumLite >*
      mutable_unknown_map_field();

  // @@protoc_insertion_point(class_scope:protobuf_unittest.TestEnumMapLite)
 private:

  ::google::protobuf::internal::ArenaStringPtr _unknown_fields_;
  ::google::protobuf::Arena* _arena_ptr_;

  friend class ::google::protobuf::Arena;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  ::google::protobuf::internal::HasBits<1> _has_bits_;
  mutable int _cached_size_;
  typedef ::google::protobuf::internal::MapEntryLite<
      ::google::protobuf::int32, ::protobuf_unittest::Proto2MapEnumLite,
      ::google::protobuf::internal::WireFormatLite::TYPE_INT32,
      ::google::protobuf::internal::WireFormatLite::TYPE_ENUM,
      0 >
      TestEnumMapLite_KnownMapFieldEntry;
  ::google::protobuf::internal::MapFieldLite<
      ::google::protobuf::int32, ::protobuf_unittest::Proto2MapEnumLite,
      ::google::protobuf::internal::WireFormatLite::TYPE_INT32,
      ::google::protobuf::internal::WireFormatLite::TYPE_ENUM,
      0 > known_map_field_;
  typedef ::google::protobuf::internal::MapEntryLite<
      ::google::protobuf::int32, ::protobuf_unittest::Proto2MapEnumLite,
      ::google::protobuf::internal::WireFormatLite::TYPE_INT32,
      ::google::protobuf::internal::WireFormatLite::TYPE_ENUM,
      0 >
      TestEnumMapLite_UnknownMapFieldEntry;
  ::google::protobuf::internal::MapFieldLite<
      ::google::protobuf::int32, ::protobuf_unittest::Proto2MapEnumLite,
      ::google::protobuf::internal::WireFormatLite::TYPE_INT32,
      ::google::protobuf::internal::WireFormatLite::TYPE_ENUM,
      0 > unknown_map_field_;
  friend void  protobuf_InitDefaults_google_2fprotobuf_2fmap_5flite_5funittest_2eproto_impl();
  friend void  protobuf_AddDesc_google_2fprotobuf_2fmap_5flite_5funittest_2eproto_impl();
  friend void protobuf_AssignDesc_google_2fprotobuf_2fmap_5flite_5funittest_2eproto();
  friend void protobuf_ShutdownFile_google_2fprotobuf_2fmap_5flite_5funittest_2eproto();

  void InitAsDefaultInstance();
};
extern ::google::protobuf::internal::ExplicitlyConstructed<TestEnumMapLite> TestEnumMapLite_default_instance_;

// -------------------------------------------------------------------

class TestEnumMapPlusExtraLite : public ::google::protobuf::MessageLite /* @@protoc_insertion_point(class_definition:protobuf_unittest.TestEnumMapPlusExtraLite) */ {
 public:
  TestEnumMapPlusExtraLite();
  virtual ~TestEnumMapPlusExtraLite();

  TestEnumMapPlusExtraLite(const TestEnumMapPlusExtraLite& from);

  inline TestEnumMapPlusExtraLite& operator=(const TestEnumMapPlusExtraLite& from) {
    CopyFrom(from);
    return *this;
  }

  inline const ::std::string& unknown_fields() const {
    return _unknown_fields_.Get(
        &::google::protobuf::internal::GetEmptyStringAlreadyInited());
  }

  inline ::std::string* mutable_unknown_fields() {
    return _unknown_fields_.Mutable(
        &::google::protobuf::internal::GetEmptyStringAlreadyInited(),
        GetArenaNoVirtual());
  }

  inline ::google::protobuf::Arena* GetArena() const { return GetArenaNoVirtual(); }
  inline void* GetMaybeArenaPointer() const {
    return MaybeArenaPtr();
  }
  static const TestEnumMapPlusExtraLite& default_instance();

  static const TestEnumMapPlusExtraLite* internal_default_instance();

  void UnsafeArenaSwap(TestEnumMapPlusExtraLite* other);
  void Swap(TestEnumMapPlusExtraLite* other);

  // implements Message ----------------------------------------------

  inline TestEnumMapPlusExtraLite* New() const { return New(NULL); }

  TestEnumMapPlusExtraLite* New(::google::protobuf::Arena* arena) const;
  void CheckTypeAndMergeFrom(const ::google::protobuf::MessageLite& from);
  void CopyFrom(const TestEnumMapPlusExtraLite& from);
  void MergeFrom(const TestEnumMapPlusExtraLite& from);
  void Clear();
  bool IsInitialized() const;

  size_t ByteSizeLong() const;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input);
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const;
  void DiscardUnknownFields();
  int GetCachedSize() const { return _cached_size_; }
  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const;
  void InternalSwap(TestEnumMapPlusExtraLite* other);
  void UnsafeMergeFrom(const TestEnumMapPlusExtraLite& from);
  protected:
  explicit TestEnumMapPlusExtraLite(::google::protobuf::Arena* arena);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::google::protobuf::Arena* arena);
  private:
  inline ::google::protobuf::Arena* GetArenaNoVirtual() const {
    return _arena_ptr_;
  }
  inline ::google::protobuf::Arena* MaybeArenaPtr() const {
    return _arena_ptr_;
  }
  public:

  ::std::string GetTypeName() const;

  // nested types ----------------------------------------------------


  // accessors -------------------------------------------------------

  // map<int32, .protobuf_unittest.Proto2MapEnumPlusExtraLite> known_map_field = 101;
  int known_map_field_size() const;
  void clear_known_map_field();
  static const int kKnownMapFieldFieldNumber = 101;
  const ::google::protobuf::Map< ::google::protobuf::int32, ::protobuf_unittest::Proto2MapEnumPlusExtraLite >&
      known_map_field() const;
  ::google::protobuf::Map< ::google::protobuf::int32, ::protobuf_unittest::Proto2MapEnumPlusExtraLite >*
      mutable_known_map_field();

  // map<int32, .protobuf_unittest.Proto2MapEnumPlusExtraLite> unknown_map_field = 102;
  int unknown_map_field_size() const;
  void clear_unknown_map_field();
  static const int kUnknownMapFieldFieldNumber = 102;
  const ::google::protobuf::Map< ::google::protobuf::int32, ::protobuf_unittest::Proto2MapEnumPlusExtraLite >&
      unknown_map_field() const;
  ::google::protobuf::Map< ::google::protobuf::int32, ::protobuf_unittest::Proto2MapEnumPlusExtraLite >*
      mutable_unknown_map_field();

  // @@protoc_insertion_point(class_scope:protobuf_unittest.TestEnumMapPlusExtraLite)
 private:

  ::google::protobuf::internal::ArenaStringPtr _unknown_fields_;
  ::google::protobuf::Arena* _arena_ptr_;

  friend class ::google::protobuf::Arena;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  ::google::protobuf::internal::HasBits<1> _has_bits_;
  mutable int _cached_size_;
  typedef ::google::protobuf::internal::MapEntryLite<
      ::google::protobuf::int32, ::protobuf_unittest::Proto2MapEnumPlusExtraLite,
      ::google::protobuf::internal::WireFormatLite::TYPE_INT32,
      ::google::protobuf::internal::WireFormatLite::TYPE_ENUM,
      0 >
      TestEnumMapPlusExtraLite_KnownMapFieldEntry;
  ::google::protobuf::internal::MapFieldLite<
      ::google::protobuf::int32, ::protobuf_unittest::Proto2MapEnumPlusExtraLite,
      ::google::protobuf::internal::WireFormatLite::TYPE_INT32,
      ::google::protobuf::internal::WireFormatLite::TYPE_ENUM,
      0 > known_map_field_;
  typedef ::google::protobuf::internal::MapEntryLite<
      ::google::protobuf::int32, ::protobuf_unittest::Proto2MapEnumPlusExtraLite,
      ::google::protobuf::internal::WireFormatLite::TYPE_INT32,
      ::google::protobuf::internal::WireFormatLite::TYPE_ENUM,
      0 >
      TestEnumMapPlusExtraLite_UnknownMapFieldEntry;
  ::google::protobuf::internal::MapFieldLite<
      ::google::protobuf::int32, ::protobuf_unittest::Proto2MapEnumPlusExtraLite,
      ::google::protobuf::internal::WireFormatLite::TYPE_INT32,
      ::google::protobuf::internal::WireFormatLite::TYPE_ENUM,
      0 > unknown_map_field_;
  friend void  protobuf_InitDefaults_google_2fprotobuf_2fmap_5flite_5funittest_2eproto_impl();
  friend void  protobuf_AddDesc_google_2fprotobuf_2fmap_5flite_5funittest_2eproto_impl();
  friend void protobuf_AssignDesc_google_2fprotobuf_2fmap_5flite_5funittest_2eproto();
  friend void protobuf_ShutdownFile_google_2fprotobuf_2fmap_5flite_5funittest_2eproto();

  void InitAsDefaultInstance();
};
extern ::google::protobuf::internal::ExplicitlyConstructed<TestEnumMapPlusExtraLite> TestEnumMapPlusExtraLite_default_instance_;

// -------------------------------------------------------------------

class TestMessageMapLite : public ::google::protobuf::MessageLite /* @@protoc_insertion_point(class_definition:protobuf_unittest.TestMessageMapLite) */ {
 public:
  TestMessageMapLite();
  virtual ~TestMessageMapLite();

  TestMessageMapLite(const TestMessageMapLite& from);

  inline TestMessageMapLite& operator=(const TestMessageMapLite& from) {
    CopyFrom(from);
    return *this;
  }

  inline const ::std::string& unknown_fields() const {
    return _unknown_fields_.Get(
        &::google::protobuf::internal::GetEmptyStringAlreadyInited());
  }

  inline ::std::string* mutable_unknown_fields() {
    return _unknown_fields_.Mutable(
        &::google::protobuf::internal::GetEmptyStringAlreadyInited(),
        GetArenaNoVirtual());
  }

  inline ::google::protobuf::Arena* GetArena() const { return GetArenaNoVirtual(); }
  inline void* GetMaybeArenaPointer() const {
    return MaybeArenaPtr();
  }
  static const TestMessageMapLite& default_instance();

  static const TestMessageMapLite* internal_default_instance();

  void UnsafeArenaSwap(TestMessageMapLite* other);
  void Swap(TestMessageMapLite* other);

  // implements Message ----------------------------------------------

  inline TestMessageMapLite* New() const { return New(NULL); }

  TestMessageMapLite* New(::google::protobuf::Arena* arena) const;
  void CheckTypeAndMergeFrom(const ::google::protobuf::MessageLite& from);
  void CopyFrom(const TestMessageMapLite& from);
  void MergeFrom(const TestMessageMapLite& from);
  void Clear();
  bool IsInitialized() const;

  size_t ByteSizeLong() const;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input);
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const;
  void DiscardUnknownFields();
  int GetCachedSize() const { return _cached_size_; }
  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const;
  void InternalSwap(TestMessageMapLite* other);
  void UnsafeMergeFrom(const TestMessageMapLite& from);
  protected:
  explicit TestMessageMapLite(::google::protobuf::Arena* arena);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::google::protobuf::Arena* arena);
  private:
  inline ::google::protobuf::Arena* GetArenaNoVirtual() const {
    return _arena_ptr_;
  }
  inline ::google::protobuf::Arena* MaybeArenaPtr() const {
    return _arena_ptr_;
  }
  public:

  ::std::string GetTypeName() const;

  // nested types ----------------------------------------------------


  // accessors -------------------------------------------------------

  // map<int32, .protobuf_unittest.TestAllTypesLite> map_int32_message = 1;
  int map_int32_message_size() const;
  void clear_map_int32_message();
  static const int kMapInt32MessageFieldNumber = 1;
  const ::google::protobuf::Map< ::google::protobuf::int32, ::protobuf_unittest::TestAllTypesLite >&
      map_int32_message() const;
  ::google::protobuf::Map< ::google::protobuf::int32, ::protobuf_unittest::TestAllTypesLite >*
      mutable_map_int32_message();

  // @@protoc_insertion_point(class_scope:protobuf_unittest.TestMessageMapLite)
 private:

  ::google::protobuf::internal::ArenaStringPtr _unknown_fields_;
  ::google::protobuf::Arena* _arena_ptr_;

  friend class ::google::protobuf::Arena;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  ::google::protobuf::internal::HasBits<1> _has_bits_;
  mutable int _cached_size_;
  typedef ::google::protobuf::internal::MapEntryLite<
      ::google::protobuf::int32, ::protobuf_unittest::TestAllTypesLite,
      ::google::protobuf::internal::WireFormatLite::TYPE_INT32,
      ::google::protobuf::internal::WireFormatLite::TYPE_MESSAGE,
      0 >
      TestMessageMapLite_MapInt32MessageEntry;
  ::google::protobuf::internal::MapFieldLite<
      ::google::protobuf::int32, ::protobuf_unittest::TestAllTypesLite,
      ::google::protobuf::internal::WireFormatLite::TYPE_INT32,
      ::google::protobuf::internal::WireFormatLite::TYPE_MESSAGE,
      0 > map_int32_message_;
  friend void  protobuf_InitDefaults_google_2fprotobuf_2fmap_5flite_5funittest_2eproto_impl();
  friend void  protobuf_AddDesc_google_2fprotobuf_2fmap_5flite_5funittest_2eproto_impl();
  friend void protobuf_AssignDesc_google_2fprotobuf_2fmap_5flite_5funittest_2eproto();
  friend void protobuf_ShutdownFile_google_2fprotobuf_2fmap_5flite_5funittest_2eproto();

  void InitAsDefaultInstance();
};
extern ::google::protobuf::internal::ExplicitlyConstructed<TestMessageMapLite> TestMessageMapLite_default_instance_;

// -------------------------------------------------------------------

class TestRequiredLite : public ::google::protobuf::MessageLite /* @@protoc_insertion_point(class_definition:protobuf_unittest.TestRequiredLite) */ {
 public:
  TestRequiredLite();
  virtual ~TestRequiredLite();

  TestRequiredLite(const TestRequiredLite& from);

  inline TestRequiredLite& operator=(const TestRequiredLite& from) {
    CopyFrom(from);
    return *this;
  }

  inline const ::std::string& unknown_fields() const {
    return _unknown_fields_.Get(
        &::google::protobuf::internal::GetEmptyStringAlreadyInited());
  }

  inline ::std::string* mutable_unknown_fields() {
    return _unknown_fields_.Mutable(
        &::google::protobuf::internal::GetEmptyStringAlreadyInited(),
        GetArenaNoVirtual());
  }

  inline ::google::protobuf::Arena* GetArena() const { return GetArenaNoVirtual(); }
  inline void* GetMaybeArenaPointer() const {
    return MaybeArenaPtr();
  }
  static const TestRequiredLite& default_instance();

  static const TestRequiredLite* internal_default_instance();

  void UnsafeArenaSwap(TestRequiredLite* other);
  void Swap(TestRequiredLite* other);

  // implements Message ----------------------------------------------

  inline TestRequiredLite* New() const { return New(NULL); }

  TestRequiredLite* New(::google::protobuf::Arena* arena) const;
  void CheckTypeAndMergeFrom(const ::google::protobuf::MessageLite& from);
  void CopyFrom(const TestRequiredLite& from);
  void MergeFrom(const TestRequiredLite& from);
  void Clear();
  bool IsInitialized() const;

  size_t ByteSizeLong() const;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input);
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const;
  void DiscardUnknownFields();
  int GetCachedSize() const { return _cached_size_; }
  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const;
  void InternalSwap(TestRequiredLite* other);
  void UnsafeMergeFrom(const TestRequiredLite& from);
  protected:
  explicit TestRequiredLite(::google::protobuf::Arena* arena);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::google::protobuf::Arena* arena);
  private:
  inline ::google::protobuf::Arena* GetArenaNoVirtual() const {
    return _arena_ptr_;
  }
  inline ::google::protobuf::Arena* MaybeArenaPtr() const {
    return _arena_ptr_;
  }
  public:

  ::std::string GetTypeName() const;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // required int32 a = 1;
  bool has_a() const;
  void clear_a();
  static const int kAFieldNumber = 1;
  ::google::protobuf::int32 a() const;
  void set_a(::google::protobuf::int32 value);

  // required int32 b = 2;
  bool has_b() const;
  void clear_b();
  static const int kBFieldNumber = 2;
  ::google::protobuf::int32 b() const;
  void set_b(::google::protobuf::int32 value);

  // required int32 c = 3;
  bool has_c() const;
  void clear_c();
  static const int kCFieldNumber = 3;
  ::google::protobuf::int32 c() const;
  void set_c(::google::protobuf::int32 value);

  // @@protoc_insertion_point(class_scope:protobuf_unittest.TestRequiredLite)
 private:
  inline void set_has_a();
  inline void clear_has_a();
  inline void set_has_b();
  inline void clear_has_b();
  inline void set_has_c();
  inline void clear_has_c();

  // helper for ByteSizeLong()
  size_t RequiredFieldsByteSizeFallback() const;

  ::google::protobuf::internal::ArenaStringPtr _unknown_fields_;
  ::google::protobuf::Arena* _arena_ptr_;

  friend class ::google::protobuf::Arena;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  ::google::protobuf::internal::HasBits<1> _has_bits_;
  mutable int _cached_size_;
  ::google::protobuf::int32 a_;
  ::google::protobuf::int32 b_;
  ::google::protobuf::int32 c_;
  friend void  protobuf_InitDefaults_google_2fprotobuf_2fmap_5flite_5funittest_2eproto_impl();
  friend void  protobuf_AddDesc_google_2fprotobuf_2fmap_5flite_5funittest_2eproto_impl();
  friend void protobuf_AssignDesc_google_2fprotobuf_2fmap_5flite_5funittest_2eproto();
  friend void protobuf_ShutdownFile_google_2fprotobuf_2fmap_5flite_5funittest_2eproto();

  void InitAsDefaultInstance();
};
extern ::google::protobuf::internal::ExplicitlyConstructed<TestRequiredLite> TestRequiredLite_default_instance_;

// -------------------------------------------------------------------

class ForeignMessageArenaLite : public ::google::protobuf::MessageLite /* @@protoc_insertion_point(class_definition:protobuf_unittest.ForeignMessageArenaLite) */ {
 public:
  ForeignMessageArenaLite();
  virtual ~ForeignMessageArenaLite();

  ForeignMessageArenaLite(const ForeignMessageArenaLite& from);

  inline ForeignMessageArenaLite& operator=(const ForeignMessageArenaLite& from) {
    CopyFrom(from);
    return *this;
  }

  inline const ::std::string& unknown_fields() const {
    return _unknown_fields_.Get(
        &::google::protobuf::internal::GetEmptyStringAlreadyInited());
  }

  inline ::std::string* mutable_unknown_fields() {
    return _unknown_fields_.Mutable(
        &::google::protobuf::internal::GetEmptyStringAlreadyInited(),
        GetArenaNoVirtual());
  }

  inline ::google::protobuf::Arena* GetArena() const { return GetArenaNoVirtual(); }
  inline void* GetMaybeArenaPointer() const {
    return MaybeArenaPtr();
  }
  static const ForeignMessageArenaLite& default_instance();

  static const ForeignMessageArenaLite* internal_default_instance();

  void UnsafeArenaSwap(ForeignMessageArenaLite* other);
  void Swap(ForeignMessageArenaLite* other);

  // implements Message ----------------------------------------------

  inline ForeignMessageArenaLite* New() const { return New(NULL); }

  ForeignMessageArenaLite* New(::google::protobuf::Arena* arena) const;
  void CheckTypeAndMergeFrom(const ::google::protobuf::MessageLite& from);
  void CopyFrom(const ForeignMessageArenaLite& from);
  void MergeFrom(const ForeignMessageArenaLite& from);
  void Clear();
  bool IsInitialized() const;

  size_t ByteSizeLong() const;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input);
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const;
  void DiscardUnknownFields();
  int GetCachedSize() const { return _cached_size_; }
  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const;
  void InternalSwap(ForeignMessageArenaLite* other);
  void UnsafeMergeFrom(const ForeignMessageArenaLite& from);
  protected:
  explicit ForeignMessageArenaLite(::google::protobuf::Arena* arena);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::google::protobuf::Arena* arena);
  private:
  inline ::google::protobuf::Arena* GetArenaNoVirtual() const {
    return _arena_ptr_;
  }
  inline ::google::protobuf::Arena* MaybeArenaPtr() const {
    return _arena_ptr_;
  }
  public:

  ::std::string GetTypeName() const;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // optional int32 c = 1;
  bool has_c() const;
  void clear_c();
  static const int kCFieldNumber = 1;
  ::google::protobuf::int32 c() const;
  void set_c(::google::protobuf::int32 value);

  // @@protoc_insertion_point(class_scope:protobuf_unittest.ForeignMessageArenaLite)
 private:
  inline void set_has_c();
  inline void clear_has_c();

  ::google::protobuf::internal::ArenaStringPtr _unknown_fields_;
  ::google::protobuf::Arena* _arena_ptr_;

  friend class ::google::protobuf::Arena;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  ::google::protobuf::internal::HasBits<1> _has_bits_;
  mutable int _cached_size_;
  ::google::protobuf::int32 c_;
  friend void  protobuf_InitDefaults_google_2fprotobuf_2fmap_5flite_5funittest_2eproto_impl();
  friend void  protobuf_AddDesc_google_2fprotobuf_2fmap_5flite_5funittest_2eproto_impl();
  friend void protobuf_AssignDesc_google_2fprotobuf_2fmap_5flite_5funittest_2eproto();
  friend void protobuf_ShutdownFile_google_2fprotobuf_2fmap_5flite_5funittest_2eproto();

  void InitAsDefaultInstance();
};
extern ::google::protobuf::internal::ExplicitlyConstructed<ForeignMessageArenaLite> ForeignMessageArenaLite_default_instance_;

// ===================================================================


// ===================================================================

#if !PROTOBUF_INLINE_NOT_IN_HEADERS
// TestMapLite

// map<int32, int32> map_int32_int32 = 1;
inline int TestMapLite::map_int32_int32_size() const {
  return map_int32_int32_.size();
}
inline void TestMapLite::clear_map_int32_int32() {
  map_int32_int32_.Clear();
}
inline const ::google::protobuf::Map< ::google::protobuf::int32, ::google::protobuf::int32 >&
TestMapLite::map_int32_int32() const {
  // @@protoc_insertion_point(field_map:protobuf_unittest.TestMapLite.map_int32_int32)
  return map_int32_int32_.GetMap();
}
inline ::google::protobuf::Map< ::google::protobuf::int32, ::google::protobuf::int32 >*
TestMapLite::mutable_map_int32_int32() {
  // @@protoc_insertion_point(field_mutable_map:protobuf_unittest.TestMapLite.map_int32_int32)
  return map_int32_int32_.MutableMap();
}

// map<int64, int64> map_int64_int64 = 2;
inline int TestMapLite::map_int64_int64_size() const {
  return map_int64_int64_.size();
}
inline void TestMapLite::clear_map_int64_int64() {
  map_int64_int64_.Clear();
}
inline const ::google::protobuf::Map< ::google::protobuf::int64, ::google::protobuf::int64 >&
TestMapLite::map_int64_int64() const {
  // @@protoc_insertion_point(field_map:protobuf_unittest.TestMapLite.map_int64_int64)
  return map_int64_int64_.GetMap();
}
inline ::google::protobuf::Map< ::google::protobuf::int64, ::google::protobuf::int64 >*
TestMapLite::mutable_map_int64_int64() {
  // @@protoc_insertion_point(field_mutable_map:protobuf_unittest.TestMapLite.map_int64_int64)
  return map_int64_int64_.MutableMap();
}

// map<uint32, uint32> map_uint32_uint32 = 3;
inline int TestMapLite::map_uint32_uint32_size() const {
  return map_uint32_uint32_.size();
}
inline void TestMapLite::clear_map_uint32_uint32() {
  map_uint32_uint32_.Clear();
}
inline const ::google::protobuf::Map< ::google::protobuf::uint32, ::google::protobuf::uint32 >&
TestMapLite::map_uint32_uint32() const {
  // @@protoc_insertion_point(field_map:protobuf_unittest.TestMapLite.map_uint32_uint32)
  return map_uint32_uint32_.GetMap();
}
inline ::google::protobuf::Map< ::google::protobuf::uint32, ::google::protobuf::uint32 >*
TestMapLite::mutable_map_uint32_uint32() {
  // @@protoc_insertion_point(field_mutable_map:protobuf_unittest.TestMapLite.map_uint32_uint32)
  return map_uint32_uint32_.MutableMap();
}

// map<uint64, uint64> map_uint64_uint64 = 4;
inline int TestMapLite::map_uint64_uint64_size() const {
  return map_uint64_uint64_.size();
}
inline void TestMapLite::clear_map_uint64_uint64() {
  map_uint64_uint64_.Clear();
}
inline const ::google::protobuf::Map< ::google::protobuf::uint64, ::google::protobuf::uint64 >&
TestMapLite::map_uint64_uint64() const {
  // @@protoc_insertion_point(field_map:protobuf_unittest.TestMapLite.map_uint64_uint64)
  return map_uint64_uint64_.GetMap();
}
inline ::google::protobuf::Map< ::google::protobuf::uint64, ::google::protobuf::uint64 >*
TestMapLite::mutable_map_uint64_uint64() {
  // @@protoc_insertion_point(field_mutable_map:protobuf_unittest.TestMapLite.map_uint64_uint64)
  return map_uint64_uint64_.MutableMap();
}

// map<sint32, sint32> map_sint32_sint32 = 5;
inline int TestMapLite::map_sint32_sint32_size() const {
  return map_sint32_sint32_.size();
}
inline void TestMapLite::clear_map_sint32_sint32() {
  map_sint32_sint32_.Clear();
}
inline const ::google::protobuf::Map< ::google::protobuf::int32, ::google::protobuf::int32 >&
TestMapLite::map_sint32_sint32() const {
  // @@protoc_insertion_point(field_map:protobuf_unittest.TestMapLite.map_sint32_sint32)
  return map_sint32_sint32_.GetMap();
}
inline ::google::protobuf::Map< ::google::protobuf::int32, ::google::protobuf::int32 >*
TestMapLite::mutable_map_sint32_sint32() {
  // @@protoc_insertion_point(field_mutable_map:protobuf_unittest.TestMapLite.map_sint32_sint32)
  return map_sint32_sint32_.MutableMap();
}

// map<sint64, sint64> map_sint64_sint64 = 6;
inline int TestMapLite::map_sint64_sint64_size() const {
  return map_sint64_sint64_.size();
}
inline void TestMapLite::clear_map_sint64_sint64() {
  map_sint64_sint64_.Clear();
}
inline const ::google::protobuf::Map< ::google::protobuf::int64, ::google::protobuf::int64 >&
TestMapLite::map_sint64_sint64() const {
  // @@protoc_insertion_point(field_map:protobuf_unittest.TestMapLite.map_sint64_sint64)
  return map_sint64_sint64_.GetMap();
}
inline ::google::protobuf::Map< ::google::protobuf::int64, ::google::protobuf::int64 >*
TestMapLite::mutable_map_sint64_sint64() {
  // @@protoc_insertion_point(field_mutable_map:protobuf_unittest.TestMapLite.map_sint64_sint64)
  return map_sint64_sint64_.MutableMap();
}

// map<fixed32, fixed32> map_fixed32_fixed32 = 7;
inline int TestMapLite::map_fixed32_fixed32_size() const {
  return map_fixed32_fixed32_.size();
}
inline void TestMapLite::clear_map_fixed32_fixed32() {
  map_fixed32_fixed32_.Clear();
}
inline const ::google::protobuf::Map< ::google::protobuf::uint32, ::google::protobuf::uint32 >&
TestMapLite::map_fixed32_fixed32() const {
  // @@protoc_insertion_point(field_map:protobuf_unittest.TestMapLite.map_fixed32_fixed32)
  return map_fixed32_fixed32_.GetMap();
}
inline ::google::protobuf::Map< ::google::protobuf::uint32, ::google::protobuf::uint32 >*
TestMapLite::mutable_map_fixed32_fixed32() {
  // @@protoc_insertion_point(field_mutable_map:protobuf_unittest.TestMapLite.map_fixed32_fixed32)
  return map_fixed32_fixed32_.MutableMap();
}

// map<fixed64, fixed64> map_fixed64_fixed64 = 8;
inline int TestMapLite::map_fixed64_fixed64_size() const {
  return map_fixed64_fixed64_.size();
}
inline void TestMapLite::clear_map_fixed64_fixed64() {
  map_fixed64_fixed64_.Clear();
}
inline const ::google::protobuf::Map< ::google::protobuf::uint64, ::google::protobuf::uint64 >&
TestMapLite::map_fixed64_fixed64() const {
  // @@protoc_insertion_point(field_map:protobuf_unittest.TestMapLite.map_fixed64_fixed64)
  return map_fixed64_fixed64_.GetMap();
}
inline ::google::protobuf::Map< ::google::protobuf::uint64, ::google::protobuf::uint64 >*
TestMapLite::mutable_map_fixed64_fixed64() {
  // @@protoc_insertion_point(field_mutable_map:protobuf_unittest.TestMapLite.map_fixed64_fixed64)
  return map_fixed64_fixed64_.MutableMap();
}

// map<sfixed32, sfixed32> map_sfixed32_sfixed32 = 9;
inline int TestMapLite::map_sfixed32_sfixed32_size() const {
  return map_sfixed32_sfixed32_.size();
}
inline void TestMapLite::clear_map_sfixed32_sfixed32() {
  map_sfixed32_sfixed32_.Clear();
}
inline const ::google::protobuf::Map< ::google::protobuf::int32, ::google::protobuf::int32 >&
TestMapLite::map_sfixed32_sfixed32() const {
  // @@protoc_insertion_point(field_map:protobuf_unittest.TestMapLite.map_sfixed32_sfixed32)
  return map_sfixed32_sfixed32_.GetMap();
}
inline ::google::protobuf::Map< ::google::protobuf::int32, ::google::protobuf::int32 >*
TestMapLite::mutable_map_sfixed32_sfixed32() {
  // @@protoc_insertion_point(field_mutable_map:protobuf_unittest.TestMapLite.map_sfixed32_sfixed32)
  return map_sfixed32_sfixed32_.MutableMap();
}

// map<sfixed64, sfixed64> map_sfixed64_sfixed64 = 10;
inline int TestMapLite::map_sfixed64_sfixed64_size() const {
  return map_sfixed64_sfixed64_.size();
}
inline void TestMapLite::clear_map_sfixed64_sfixed64() {
  map_sfixed64_sfixed64_.Clear();
}
inline const ::google::protobuf::Map< ::google::protobuf::int64, ::google::protobuf::int64 >&
TestMapLite::map_sfixed64_sfixed64() const {
  // @@protoc_insertion_point(field_map:protobuf_unittest.TestMapLite.map_sfixed64_sfixed64)
  return map_sfixed64_sfixed64_.GetMap();
}
inline ::google::protobuf::Map< ::google::protobuf::int64, ::google::protobuf::int64 >*
TestMapLite::mutable_map_sfixed64_sfixed64() {
  // @@protoc_insertion_point(field_mutable_map:protobuf_unittest.TestMapLite.map_sfixed64_sfixed64)
  return map_sfixed64_sfixed64_.MutableMap();
}

// map<int32, float> map_int32_float = 11;
inline int TestMapLite::map_int32_float_size() const {
  return map_int32_float_.size();
}
inline void TestMapLite::clear_map_int32_float() {
  map_int32_float_.Clear();
}
inline const ::google::protobuf::Map< ::google::protobuf::int32, float >&
TestMapLite::map_int32_float() const {
  // @@protoc_insertion_point(field_map:protobuf_unittest.TestMapLite.map_int32_float)
  return map_int32_float_.GetMap();
}
inline ::google::protobuf::Map< ::google::protobuf::int32, float >*
TestMapLite::mutable_map_int32_float() {
  // @@protoc_insertion_point(field_mutable_map:protobuf_unittest.TestMapLite.map_int32_float)
  return map_int32_float_.MutableMap();
}

// map<int32, double> map_int32_double = 12;
inline int TestMapLite::map_int32_double_size() const {
  return map_int32_double_.size();
}
inline void TestMapLite::clear_map_int32_double() {
  map_int32_double_.Clear();
}
inline const ::google::protobuf::Map< ::google::protobuf::int32, double >&
TestMapLite::map_int32_double() const {
  // @@protoc_insertion_point(field_map:protobuf_unittest.TestMapLite.map_int32_double)
  return map_int32_double_.GetMap();
}
inline ::google::protobuf::Map< ::google::protobuf::int32, double >*
TestMapLite::mutable_map_int32_double() {
  // @@protoc_insertion_point(field_mutable_map:protobuf_unittest.TestMapLite.map_int32_double)
  return map_int32_double_.MutableMap();
}

// map<bool, bool> map_bool_bool = 13;
inline int TestMapLite::map_bool_bool_size() const {
  return map_bool_bool_.size();
}
inline void TestMapLite::clear_map_bool_bool() {
  map_bool_bool_.Clear();
}
inline const ::google::protobuf::Map< bool, bool >&
TestMapLite::map_bool_bool() const {
  // @@protoc_insertion_point(field_map:protobuf_unittest.TestMapLite.map_bool_bool)
  return map_bool_bool_.GetMap();
}
inline ::google::protobuf::Map< bool, bool >*
TestMapLite::mutable_map_bool_bool() {
  // @@protoc_insertion_point(field_mutable_map:protobuf_unittest.TestMapLite.map_bool_bool)
  return map_bool_bool_.MutableMap();
}

// map<string, string> map_string_string = 14;
inline int TestMapLite::map_string_string_size() const {
  return map_string_string_.size();
}
inline void TestMapLite::clear_map_string_string() {
  map_string_string_.Clear();
}
inline const ::google::protobuf::Map< ::std::string, ::std::string >&
TestMapLite::map_string_string() const {
  // @@protoc_insertion_point(field_map:protobuf_unittest.TestMapLite.map_string_string)
  return map_string_string_.GetMap();
}
inline ::google::protobuf::Map< ::std::string, ::std::string >*
TestMapLite::mutable_map_string_string() {
  // @@protoc_insertion_point(field_mutable_map:protobuf_unittest.TestMapLite.map_string_string)
  return map_string_string_.MutableMap();
}

// map<int32, bytes> map_int32_bytes = 15;
inline int TestMapLite::map_int32_bytes_size() const {
  return map_int32_bytes_.size();
}
inline void TestMapLite::clear_map_int32_bytes() {
  map_int32_bytes_.Clear();
}
inline const ::google::protobuf::Map< ::google::protobuf::int32, ::std::string >&
TestMapLite::map_int32_bytes() const {
  // @@protoc_insertion_point(field_map:protobuf_unittest.TestMapLite.map_int32_bytes)
  return map_int32_bytes_.GetMap();
}
inline ::google::protobuf::Map< ::google::protobuf::int32, ::std::string >*
TestMapLite::mutable_map_int32_bytes() {
  // @@protoc_insertion_point(field_mutable_map:protobuf_unittest.TestMapLite.map_int32_bytes)
  return map_int32_bytes_.MutableMap();
}

// map<int32, .protobuf_unittest.MapEnumLite> map_int32_enum = 16;
inline int TestMapLite::map_int32_enum_size() const {
  return map_int32_enum_.size();
}
inline void TestMapLite::clear_map_int32_enum() {
  map_int32_enum_.Clear();
}
inline const ::google::protobuf::Map< ::google::protobuf::int32, ::protobuf_unittest::MapEnumLite >&
TestMapLite::map_int32_enum() const {
  // @@protoc_insertion_point(field_map:protobuf_unittest.TestMapLite.map_int32_enum)
  return map_int32_enum_.GetMap();
}
inline ::google::protobuf::Map< ::google::protobuf::int32, ::protobuf_unittest::MapEnumLite >*
TestMapLite::mutable_map_int32_enum() {
  // @@protoc_insertion_point(field_mutable_map:protobuf_unittest.TestMapLite.map_int32_enum)
  return map_int32_enum_.MutableMap();
}

// map<int32, .protobuf_unittest.ForeignMessageLite> map_int32_foreign_message = 17;
inline int TestMapLite::map_int32_foreign_message_size() const {
  return map_int32_foreign_message_.size();
}
inline void TestMapLite::clear_map_int32_foreign_message() {
  map_int32_foreign_message_.Clear();
}
inline const ::google::protobuf::Map< ::google::protobuf::int32, ::protobuf_unittest::ForeignMessageLite >&
TestMapLite::map_int32_foreign_message() const {
  // @@protoc_insertion_point(field_map:protobuf_unittest.TestMapLite.map_int32_foreign_message)
  return map_int32_foreign_message_.GetMap();
}
inline ::google::protobuf::Map< ::google::protobuf::int32, ::protobuf_unittest::ForeignMessageLite >*
TestMapLite::mutable_map_int32_foreign_message() {
  // @@protoc_insertion_point(field_mutable_map:protobuf_unittest.TestMapLite.map_int32_foreign_message)
  return map_int32_foreign_message_.MutableMap();
}

// map<int32, int32> teboring = 18;
inline int TestMapLite::teboring_size() const {
  return teboring_.size();
}
inline void TestMapLite::clear_teboring() {
  teboring_.Clear();
}
inline const ::google::protobuf::Map< ::google::protobuf::int32, ::google::protobuf::int32 >&
TestMapLite::teboring() const {
  // @@protoc_insertion_point(field_map:protobuf_unittest.TestMapLite.teboring)
  return teboring_.GetMap();
}
inline ::google::protobuf::Map< ::google::protobuf::int32, ::google::protobuf::int32 >*
TestMapLite::mutable_teboring() {
  // @@protoc_insertion_point(field_mutable_map:protobuf_unittest.TestMapLite.teboring)
  return teboring_.MutableMap();
}

inline const TestMapLite* TestMapLite::internal_default_instance() {
  return &TestMapLite_default_instance_.get();
}
// -------------------------------------------------------------------

// TestArenaMapLite

// map<int32, int32> map_int32_int32 = 1;
inline int TestArenaMapLite::map_int32_int32_size() const {
  return map_int32_int32_.size();
}
inline void TestArenaMapLite::clear_map_int32_int32() {
  map_int32_int32_.Clear();
}
inline const ::google::protobuf::Map< ::google::protobuf::int32, ::google::protobuf::int32 >&
TestArenaMapLite::map_int32_int32() const {
  // @@protoc_insertion_point(field_map:protobuf_unittest.TestArenaMapLite.map_int32_int32)
  return map_int32_int32_.GetMap();
}
inline ::google::protobuf::Map< ::google::protobuf::int32, ::google::protobuf::int32 >*
TestArenaMapLite::mutable_map_int32_int32() {
  // @@protoc_insertion_point(field_mutable_map:protobuf_unittest.TestArenaMapLite.map_int32_int32)
  return map_int32_int32_.MutableMap();
}

// map<int64, int64> map_int64_int64 = 2;
inline int TestArenaMapLite::map_int64_int64_size() const {
  return map_int64_int64_.size();
}
inline void TestArenaMapLite::clear_map_int64_int64() {
  map_int64_int64_.Clear();
}
inline const ::google::protobuf::Map< ::google::protobuf::int64, ::google::protobuf::int64 >&
TestArenaMapLite::map_int64_int64() const {
  // @@protoc_insertion_point(field_map:protobuf_unittest.TestArenaMapLite.map_int64_int64)
  return map_int64_int64_.GetMap();
}
inline ::google::protobuf::Map< ::google::protobuf::int64, ::google::protobuf::int64 >*
TestArenaMapLite::mutable_map_int64_int64() {
  // @@protoc_insertion_point(field_mutable_map:protobuf_unittest.TestArenaMapLite.map_int64_int64)
  return map_int64_int64_.MutableMap();
}

// map<uint32, uint32> map_uint32_uint32 = 3;
inline int TestArenaMapLite::map_uint32_uint32_size() const {
  return map_uint32_uint32_.size();
}
inline void TestArenaMapLite::clear_map_uint32_uint32() {
  map_uint32_uint32_.Clear();
}
inline const ::google::protobuf::Map< ::google::protobuf::uint32, ::google::protobuf::uint32 >&
TestArenaMapLite::map_uint32_uint32() const {
  // @@protoc_insertion_point(field_map:protobuf_unittest.TestArenaMapLite.map_uint32_uint32)
  return map_uint32_uint32_.GetMap();
}
inline ::google::protobuf::Map< ::google::protobuf::uint32, ::google::protobuf::uint32 >*
TestArenaMapLite::mutable_map_uint32_uint32() {
  // @@protoc_insertion_point(field_mutable_map:protobuf_unittest.TestArenaMapLite.map_uint32_uint32)
  return map_uint32_uint32_.MutableMap();
}

// map<uint64, uint64> map_uint64_uint64 = 4;
inline int TestArenaMapLite::map_uint64_uint64_size() const {
  return map_uint64_uint64_.size();
}
inline void TestArenaMapLite::clear_map_uint64_uint64() {
  map_uint64_uint64_.Clear();
}
inline const ::google::protobuf::Map< ::google::protobuf::uint64, ::google::protobuf::uint64 >&
TestArenaMapLite::map_uint64_uint64() const {
  // @@protoc_insertion_point(field_map:protobuf_unittest.TestArenaMapLite.map_uint64_uint64)
  return map_uint64_uint64_.GetMap();
}
inline ::google::protobuf::Map< ::google::protobuf::uint64, ::google::protobuf::uint64 >*
TestArenaMapLite::mutable_map_uint64_uint64() {
  // @@protoc_insertion_point(field_mutable_map:protobuf_unittest.TestArenaMapLite.map_uint64_uint64)
  return map_uint64_uint64_.MutableMap();
}

// map<sint32, sint32> map_sint32_sint32 = 5;
inline int TestArenaMapLite::map_sint32_sint32_size() const {
  return map_sint32_sint32_.size();
}
inline void TestArenaMapLite::clear_map_sint32_sint32() {
  map_sint32_sint32_.Clear();
}
inline const ::google::protobuf::Map< ::google::protobuf::int32, ::google::protobuf::int32 >&
TestArenaMapLite::map_sint32_sint32() const {
  // @@protoc_insertion_point(field_map:protobuf_unittest.TestArenaMapLite.map_sint32_sint32)
  return map_sint32_sint32_.GetMap();
}
inline ::google::protobuf::Map< ::google::protobuf::int32, ::google::protobuf::int32 >*
TestArenaMapLite::mutable_map_sint32_sint32() {
  // @@protoc_insertion_point(field_mutable_map:protobuf_unittest.TestArenaMapLite.map_sint32_sint32)
  return map_sint32_sint32_.MutableMap();
}

// map<sint64, sint64> map_sint64_sint64 = 6;
inline int TestArenaMapLite::map_sint64_sint64_size() const {
  return map_sint64_sint64_.size();
}
inline void TestArenaMapLite::clear_map_sint64_sint64() {
  map_sint64_sint64_.Clear();
}
inline const ::google::protobuf::Map< ::google::protobuf::int64, ::google::protobuf::int64 >&
TestArenaMapLite::map_sint64_sint64() const {
  // @@protoc_insertion_point(field_map:protobuf_unittest.TestArenaMapLite.map_sint64_sint64)
  return map_sint64_sint64_.GetMap();
}
inline ::google::protobuf::Map< ::google::protobuf::int64, ::google::protobuf::int64 >*
TestArenaMapLite::mutable_map_sint64_sint64() {
  // @@protoc_insertion_point(field_mutable_map:protobuf_unittest.TestArenaMapLite.map_sint64_sint64)
  return map_sint64_sint64_.MutableMap();
}

// map<fixed32, fixed32> map_fixed32_fixed32 = 7;
inline int TestArenaMapLite::map_fixed32_fixed32_size() const {
  return map_fixed32_fixed32_.size();
}
inline void TestArenaMapLite::clear_map_fixed32_fixed32() {
  map_fixed32_fixed32_.Clear();
}
inline const ::google::protobuf::Map< ::google::protobuf::uint32, ::google::protobuf::uint32 >&
TestArenaMapLite::map_fixed32_fixed32() const {
  // @@protoc_insertion_point(field_map:protobuf_unittest.TestArenaMapLite.map_fixed32_fixed32)
  return map_fixed32_fixed32_.GetMap();
}
inline ::google::protobuf::Map< ::google::protobuf::uint32, ::google::protobuf::uint32 >*
TestArenaMapLite::mutable_map_fixed32_fixed32() {
  // @@protoc_insertion_point(field_mutable_map:protobuf_unittest.TestArenaMapLite.map_fixed32_fixed32)
  return map_fixed32_fixed32_.MutableMap();
}

// map<fixed64, fixed64> map_fixed64_fixed64 = 8;
inline int TestArenaMapLite::map_fixed64_fixed64_size() const {
  return map_fixed64_fixed64_.size();
}
inline void TestArenaMapLite::clear_map_fixed64_fixed64() {
  map_fixed64_fixed64_.Clear();
}
inline const ::google::protobuf::Map< ::google::protobuf::uint64, ::google::protobuf::uint64 >&
TestArenaMapLite::map_fixed64_fixed64() const {
  // @@protoc_insertion_point(field_map:protobuf_unittest.TestArenaMapLite.map_fixed64_fixed64)
  return map_fixed64_fixed64_.GetMap();
}
inline ::google::protobuf::Map< ::google::protobuf::uint64, ::google::protobuf::uint64 >*
TestArenaMapLite::mutable_map_fixed64_fixed64() {
  // @@protoc_insertion_point(field_mutable_map:protobuf_unittest.TestArenaMapLite.map_fixed64_fixed64)
  return map_fixed64_fixed64_.MutableMap();
}

// map<sfixed32, sfixed32> map_sfixed32_sfixed32 = 9;
inline int TestArenaMapLite::map_sfixed32_sfixed32_size() const {
  return map_sfixed32_sfixed32_.size();
}
inline void TestArenaMapLite::clear_map_sfixed32_sfixed32() {
  map_sfixed32_sfixed32_.Clear();
}
inline const ::google::protobuf::Map< ::google::protobuf::int32, ::google::protobuf::int32 >&
TestArenaMapLite::map_sfixed32_sfixed32() const {
  // @@protoc_insertion_point(field_map:protobuf_unittest.TestArenaMapLite.map_sfixed32_sfixed32)
  return map_sfixed32_sfixed32_.GetMap();
}
inline ::google::protobuf::Map< ::google::protobuf::int32, ::google::protobuf::int32 >*
TestArenaMapLite::mutable_map_sfixed32_sfixed32() {
  // @@protoc_insertion_point(field_mutable_map:protobuf_unittest.TestArenaMapLite.map_sfixed32_sfixed32)
  return map_sfixed32_sfixed32_.MutableMap();
}

// map<sfixed64, sfixed64> map_sfixed64_sfixed64 = 10;
inline int TestArenaMapLite::map_sfixed64_sfixed64_size() const {
  return map_sfixed64_sfixed64_.size();
}
inline void TestArenaMapLite::clear_map_sfixed64_sfixed64() {
  map_sfixed64_sfixed64_.Clear();
}
inline const ::google::protobuf::Map< ::google::protobuf::int64, ::google::protobuf::int64 >&
TestArenaMapLite::map_sfixed64_sfixed64() const {
  // @@protoc_insertion_point(field_map:protobuf_unittest.TestArenaMapLite.map_sfixed64_sfixed64)
  return map_sfixed64_sfixed64_.GetMap();
}
inline ::google::protobuf::Map< ::google::protobuf::int64, ::google::protobuf::int64 >*
TestArenaMapLite::mutable_map_sfixed64_sfixed64() {
  // @@protoc_insertion_point(field_mutable_map:protobuf_unittest.TestArenaMapLite.map_sfixed64_sfixed64)
  return map_sfixed64_sfixed64_.MutableMap();
}

// map<int32, float> map_int32_float = 11;
inline int TestArenaMapLite::map_int32_float_size() const {
  return map_int32_float_.size();
}
inline void TestArenaMapLite::clear_map_int32_float() {
  map_int32_float_.Clear();
}
inline const ::google::protobuf::Map< ::google::protobuf::int32, float >&
TestArenaMapLite::map_int32_float() const {
  // @@protoc_insertion_point(field_map:protobuf_unittest.TestArenaMapLite.map_int32_float)
  return map_int32_float_.GetMap();
}
inline ::google::protobuf::Map< ::google::protobuf::int32, float >*
TestArenaMapLite::mutable_map_int32_float() {
  // @@protoc_insertion_point(field_mutable_map:protobuf_unittest.TestArenaMapLite.map_int32_float)
  return map_int32_float_.MutableMap();
}

// map<int32, double> map_int32_double = 12;
inline int TestArenaMapLite::map_int32_double_size() const {
  return map_int32_double_.size();
}
inline void TestArenaMapLite::clear_map_int32_double() {
  map_int32_double_.Clear();
}
inline const ::google::protobuf::Map< ::google::protobuf::int32, double >&
TestArenaMapLite::map_int32_double() const {
  // @@protoc_insertion_point(field_map:protobuf_unittest.TestArenaMapLite.map_int32_double)
  return map_int32_double_.GetMap();
}
inline ::google::protobuf::Map< ::google::protobuf::int32, double >*
TestArenaMapLite::mutable_map_int32_double() {
  // @@protoc_insertion_point(field_mutable_map:protobuf_unittest.TestArenaMapLite.map_int32_double)
  return map_int32_double_.MutableMap();
}

// map<bool, bool> map_bool_bool = 13;
inline int TestArenaMapLite::map_bool_bool_size() const {
  return map_bool_bool_.size();
}
inline void TestArenaMapLite::clear_map_bool_bool() {
  map_bool_bool_.Clear();
}
inline const ::google::protobuf::Map< bool, bool >&
TestArenaMapLite::map_bool_bool() const {
  // @@protoc_insertion_point(field_map:protobuf_unittest.TestArenaMapLite.map_bool_bool)
  return map_bool_bool_.GetMap();
}
inline ::google::protobuf::Map< bool, bool >*
TestArenaMapLite::mutable_map_bool_bool() {
  // @@protoc_insertion_point(field_mutable_map:protobuf_unittest.TestArenaMapLite.map_bool_bool)
  return map_bool_bool_.MutableMap();
}

// map<string, string> map_string_string = 14;
inline int TestArenaMapLite::map_string_string_size() const {
  return map_string_string_.size();
}
inline void TestArenaMapLite::clear_map_string_string() {
  map_string_string_.Clear();
}
inline const ::google::protobuf::Map< ::std::string, ::std::string >&
TestArenaMapLite::map_string_string() const {
  // @@protoc_insertion_point(field_map:protobuf_unittest.TestArenaMapLite.map_string_string)
  return map_string_string_.GetMap();
}
inline ::google::protobuf::Map< ::std::string, ::std::string >*
TestArenaMapLite::mutable_map_string_string() {
  // @@protoc_insertion_point(field_mutable_map:protobuf_unittest.TestArenaMapLite.map_string_string)
  return map_string_string_.MutableMap();
}

// map<int32, bytes> map_int32_bytes = 15;
inline int TestArenaMapLite::map_int32_bytes_size() const {
  return map_int32_bytes_.size();
}
inline void TestArenaMapLite::clear_map_int32_bytes() {
  map_int32_bytes_.Clear();
}
inline const ::google::protobuf::Map< ::google::protobuf::int32, ::std::string >&
TestArenaMapLite::map_int32_bytes() const {
  // @@protoc_insertion_point(field_map:protobuf_unittest.TestArenaMapLite.map_int32_bytes)
  return map_int32_bytes_.GetMap();
}
inline ::google::protobuf::Map< ::google::protobuf::int32, ::std::string >*
TestArenaMapLite::mutable_map_int32_bytes() {
  // @@protoc_insertion_point(field_mutable_map:protobuf_unittest.TestArenaMapLite.map_int32_bytes)
  return map_int32_bytes_.MutableMap();
}

// map<int32, .protobuf_unittest.MapEnumLite> map_int32_enum = 16;
inline int TestArenaMapLite::map_int32_enum_size() const {
  return map_int32_enum_.size();
}
inline void TestArenaMapLite::clear_map_int32_enum() {
  map_int32_enum_.Clear();
}
inline const ::google::protobuf::Map< ::google::protobuf::int32, ::protobuf_unittest::MapEnumLite >&
TestArenaMapLite::map_int32_enum() const {
  // @@protoc_insertion_point(field_map:protobuf_unittest.TestArenaMapLite.map_int32_enum)
  return map_int32_enum_.GetMap();
}
inline ::google::protobuf::Map< ::google::protobuf::int32, ::protobuf_unittest::MapEnumLite >*
TestArenaMapLite::mutable_map_int32_enum() {
  // @@protoc_insertion_point(field_mutable_map:protobuf_unittest.TestArenaMapLite.map_int32_enum)
  return map_int32_enum_.MutableMap();
}

// map<int32, .protobuf_unittest.ForeignMessageArenaLite> map_int32_foreign_message = 17;
inline int TestArenaMapLite::map_int32_foreign_message_size() const {
  return map_int32_foreign_message_.size();
}
inline void TestArenaMapLite::clear_map_int32_foreign_message() {
  map_int32_foreign_message_.Clear();
}
inline const ::google::protobuf::Map< ::google::protobuf::int32, ::protobuf_unittest::ForeignMessageArenaLite >&
TestArenaMapLite::map_int32_foreign_message() const {
  // @@protoc_insertion_point(field_map:protobuf_unittest.TestArenaMapLite.map_int32_foreign_message)
  return map_int32_foreign_message_.GetMap();
}
inline ::google::protobuf::Map< ::google::protobuf::int32, ::protobuf_unittest::ForeignMessageArenaLite >*
TestArenaMapLite::mutable_map_int32_foreign_message() {
  // @@protoc_insertion_point(field_mutable_map:protobuf_unittest.TestArenaMapLite.map_int32_foreign_message)
  return map_int32_foreign_message_.MutableMap();
}

// map<int32, .protobuf_unittest_no_arena.ForeignMessageLite> map_int32_foreign_message_no_arena = 18;
inline int TestArenaMapLite::map_int32_foreign_message_no_arena_size() const {
  return map_int32_foreign_message_no_arena_.size();
}
inline void TestArenaMapLite::clear_map_int32_foreign_message_no_arena() {
  map_int32_foreign_message_no_arena_.Clear();
}
inline const ::google::protobuf::Map< ::google::protobuf::int32, ::protobuf_unittest_no_arena::ForeignMessageLite >&
TestArenaMapLite::map_int32_foreign_message_no_arena() const {
  // @@protoc_insertion_point(field_map:protobuf_unittest.TestArenaMapLite.map_int32_foreign_message_no_arena)
  return map_int32_foreign_message_no_arena_.GetMap();
}
inline ::google::protobuf::Map< ::google::protobuf::int32, ::protobuf_unittest_no_arena::ForeignMessageLite >*
TestArenaMapLite::mutable_map_int32_foreign_message_no_arena() {
  // @@protoc_insertion_point(field_mutable_map:protobuf_unittest.TestArenaMapLite.map_int32_foreign_message_no_arena)
  return map_int32_foreign_message_no_arena_.MutableMap();
}

inline const TestArenaMapLite* TestArenaMapLite::internal_default_instance() {
  return &TestArenaMapLite_default_instance_.get();
}
// -------------------------------------------------------------------

// TestRequiredMessageMapLite

// map<int32, .protobuf_unittest.TestRequiredLite> map_field = 1;
inline int TestRequiredMessageMapLite::map_field_size() const {
  return map_field_.size();
}
inline void TestRequiredMessageMapLite::clear_map_field() {
  map_field_.Clear();
}
inline const ::google::protobuf::Map< ::google::protobuf::int32, ::protobuf_unittest::TestRequiredLite >&
TestRequiredMessageMapLite::map_field() const {
  // @@protoc_insertion_point(field_map:protobuf_unittest.TestRequiredMessageMapLite.map_field)
  return map_field_.GetMap();
}
inline ::google::protobuf::Map< ::google::protobuf::int32, ::protobuf_unittest::TestRequiredLite >*
TestRequiredMessageMapLite::mutable_map_field() {
  // @@protoc_insertion_point(field_mutable_map:protobuf_unittest.TestRequiredMessageMapLite.map_field)
  return map_field_.MutableMap();
}

inline const TestRequiredMessageMapLite* TestRequiredMessageMapLite::internal_default_instance() {
  return &TestRequiredMessageMapLite_default_instance_.get();
}
// -------------------------------------------------------------------

// TestEnumMapLite

// map<int32, .protobuf_unittest.Proto2MapEnumLite> known_map_field = 101;
inline int TestEnumMapLite::known_map_field_size() const {
  return known_map_field_.size();
}
inline void TestEnumMapLite::clear_known_map_field() {
  known_map_field_.Clear();
}
inline const ::google::protobuf::Map< ::google::protobuf::int32, ::protobuf_unittest::Proto2MapEnumLite >&
TestEnumMapLite::known_map_field() const {
  // @@protoc_insertion_point(field_map:protobuf_unittest.TestEnumMapLite.known_map_field)
  return known_map_field_.GetMap();
}
inline ::google::protobuf::Map< ::google::protobuf::int32, ::protobuf_unittest::Proto2MapEnumLite >*
TestEnumMapLite::mutable_known_map_field() {
  // @@protoc_insertion_point(field_mutable_map:protobuf_unittest.TestEnumMapLite.known_map_field)
  return known_map_field_.MutableMap();
}

// map<int32, .protobuf_unittest.Proto2MapEnumLite> unknown_map_field = 102;
inline int TestEnumMapLite::unknown_map_field_size() const {
  return unknown_map_field_.size();
}
inline void TestEnumMapLite::clear_unknown_map_field() {
  unknown_map_field_.Clear();
}
inline const ::google::protobuf::Map< ::google::protobuf::int32, ::protobuf_unittest::Proto2MapEnumLite >&
TestEnumMapLite::unknown_map_field() const {
  // @@protoc_insertion_point(field_map:protobuf_unittest.TestEnumMapLite.unknown_map_field)
  return unknown_map_field_.GetMap();
}
inline ::google::protobuf::Map< ::google::protobuf::int32, ::protobuf_unittest::Proto2MapEnumLite >*
TestEnumMapLite::mutable_unknown_map_field() {
  // @@protoc_insertion_point(field_mutable_map:protobuf_unittest.TestEnumMapLite.unknown_map_field)
  return unknown_map_field_.MutableMap();
}

inline const TestEnumMapLite* TestEnumMapLite::internal_default_instance() {
  return &TestEnumMapLite_default_instance_.get();
}
// -------------------------------------------------------------------

// TestEnumMapPlusExtraLite

// map<int32, .protobuf_unittest.Proto2MapEnumPlusExtraLite> known_map_field = 101;
inline int TestEnumMapPlusExtraLite::known_map_field_size() const {
  return known_map_field_.size();
}
inline void TestEnumMapPlusExtraLite::clear_known_map_field() {
  known_map_field_.Clear();
}
inline const ::google::protobuf::Map< ::google::protobuf::int32, ::protobuf_unittest::Proto2MapEnumPlusExtraLite >&
TestEnumMapPlusExtraLite::known_map_field() const {
  // @@protoc_insertion_point(field_map:protobuf_unittest.TestEnumMapPlusExtraLite.known_map_field)
  return known_map_field_.GetMap();
}
inline ::google::protobuf::Map< ::google::protobuf::int32, ::protobuf_unittest::Proto2MapEnumPlusExtraLite >*
TestEnumMapPlusExtraLite::mutable_known_map_field() {
  // @@protoc_insertion_point(field_mutable_map:protobuf_unittest.TestEnumMapPlusExtraLite.known_map_field)
  return known_map_field_.MutableMap();
}

// map<int32, .protobuf_unittest.Proto2MapEnumPlusExtraLite> unknown_map_field = 102;
inline int TestEnumMapPlusExtraLite::unknown_map_field_size() const {
  return unknown_map_field_.size();
}
inline void TestEnumMapPlusExtraLite::clear_unknown_map_field() {
  unknown_map_field_.Clear();
}
inline const ::google::protobuf::Map< ::google::protobuf::int32, ::protobuf_unittest::Proto2MapEnumPlusExtraLite >&
TestEnumMapPlusExtraLite::unknown_map_field() const {
  // @@protoc_insertion_point(field_map:protobuf_unittest.TestEnumMapPlusExtraLite.unknown_map_field)
  return unknown_map_field_.GetMap();
}
inline ::google::protobuf::Map< ::google::protobuf::int32, ::protobuf_unittest::Proto2MapEnumPlusExtraLite >*
TestEnumMapPlusExtraLite::mutable_unknown_map_field() {
  // @@protoc_insertion_point(field_mutable_map:protobuf_unittest.TestEnumMapPlusExtraLite.unknown_map_field)
  return unknown_map_field_.MutableMap();
}

inline const TestEnumMapPlusExtraLite* TestEnumMapPlusExtraLite::internal_default_instance() {
  return &TestEnumMapPlusExtraLite_default_instance_.get();
}
// -------------------------------------------------------------------

// TestMessageMapLite

// map<int32, .protobuf_unittest.TestAllTypesLite> map_int32_message = 1;
inline int TestMessageMapLite::map_int32_message_size() const {
  return map_int32_message_.size();
}
inline void TestMessageMapLite::clear_map_int32_message() {
  map_int32_message_.Clear();
}
inline const ::google::protobuf::Map< ::google::protobuf::int32, ::protobuf_unittest::TestAllTypesLite >&
TestMessageMapLite::map_int32_message() const {
  // @@protoc_insertion_point(field_map:protobuf_unittest.TestMessageMapLite.map_int32_message)
  return map_int32_message_.GetMap();
}
inline ::google::protobuf::Map< ::google::protobuf::int32, ::protobuf_unittest::TestAllTypesLite >*
TestMessageMapLite::mutable_map_int32_message() {
  // @@protoc_insertion_point(field_mutable_map:protobuf_unittest.TestMessageMapLite.map_int32_message)
  return map_int32_message_.MutableMap();
}

inline const TestMessageMapLite* TestMessageMapLite::internal_default_instance() {
  return &TestMessageMapLite_default_instance_.get();
}
// -------------------------------------------------------------------

// TestRequiredLite

// required int32 a = 1;
inline bool TestRequiredLite::has_a() const {
  return (_has_bits_[0] & 0x00000001u) != 0;
}
inline void TestRequiredLite::set_has_a() {
  _has_bits_[0] |= 0x00000001u;
}
inline void TestRequiredLite::clear_has_a() {
  _has_bits_[0] &= ~0x00000001u;
}
inline void TestRequiredLite::clear_a() {
  a_ = 0;
  clear_has_a();
}
inline ::google::protobuf::int32 TestRequiredLite::a() const {
  // @@protoc_insertion_point(field_get:protobuf_unittest.TestRequiredLite.a)
  return a_;
}
inline void TestRequiredLite::set_a(::google::protobuf::int32 value) {
  set_has_a();
  a_ = value;
  // @@protoc_insertion_point(field_set:protobuf_unittest.TestRequiredLite.a)
}

// required int32 b = 2;
inline bool TestRequiredLite::has_b() const {
  return (_has_bits_[0] & 0x00000002u) != 0;
}
inline void TestRequiredLite::set_has_b() {
  _has_bits_[0] |= 0x00000002u;
}
inline void TestRequiredLite::clear_has_b() {
  _has_bits_[0] &= ~0x00000002u;
}
inline void TestRequiredLite::clear_b() {
  b_ = 0;
  clear_has_b();
}
inline ::google::protobuf::int32 TestRequiredLite::b() const {
  // @@protoc_insertion_point(field_get:protobuf_unittest.TestRequiredLite.b)
  return b_;
}
inline void TestRequiredLite::set_b(::google::protobuf::int32 value) {
  set_has_b();
  b_ = value;
  // @@protoc_insertion_point(field_set:protobuf_unittest.TestRequiredLite.b)
}

// required int32 c = 3;
inline bool TestRequiredLite::has_c() const {
  return (_has_bits_[0] & 0x00000004u) != 0;
}
inline void TestRequiredLite::set_has_c() {
  _has_bits_[0] |= 0x00000004u;
}
inline void TestRequiredLite::clear_has_c() {
  _has_bits_[0] &= ~0x00000004u;
}
inline void TestRequiredLite::clear_c() {
  c_ = 0;
  clear_has_c();
}
inline ::google::protobuf::int32 TestRequiredLite::c() const {
  // @@protoc_insertion_point(field_get:protobuf_unittest.TestRequiredLite.c)
  return c_;
}
inline void TestRequiredLite::set_c(::google::protobuf::int32 value) {
  set_has_c();
  c_ = value;
  // @@protoc_insertion_point(field_set:protobuf_unittest.TestRequiredLite.c)
}

inline const TestRequiredLite* TestRequiredLite::internal_default_instance() {
  return &TestRequiredLite_default_instance_.get();
}
// -------------------------------------------------------------------

// ForeignMessageArenaLite

// optional int32 c = 1;
inline bool ForeignMessageArenaLite::has_c() const {
  return (_has_bits_[0] & 0x00000001u) != 0;
}
inline void ForeignMessageArenaLite::set_has_c() {
  _has_bits_[0] |= 0x00000001u;
}
inline void ForeignMessageArenaLite::clear_has_c() {
  _has_bits_[0] &= ~0x00000001u;
}
inline void ForeignMessageArenaLite::clear_c() {
  c_ = 0;
  clear_has_c();
}
inline ::google::protobuf::int32 ForeignMessageArenaLite::c() const {
  // @@protoc_insertion_point(field_get:protobuf_unittest.ForeignMessageArenaLite.c)
  return c_;
}
inline void ForeignMessageArenaLite::set_c(::google::protobuf::int32 value) {
  set_has_c();
  c_ = value;
  // @@protoc_insertion_point(field_set:protobuf_unittest.ForeignMessageArenaLite.c)
}

inline const ForeignMessageArenaLite* ForeignMessageArenaLite::internal_default_instance() {
  return &ForeignMessageArenaLite_default_instance_.get();
}
#endif  // !PROTOBUF_INLINE_NOT_IN_HEADERS
// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------


// @@protoc_insertion_point(namespace_scope)

}  // namespace protobuf_unittest

#ifndef SWIG
namespace google {
namespace protobuf {

template <> struct is_proto_enum< ::protobuf_unittest::Proto2MapEnumLite> : ::google::protobuf::internal::true_type {};
template <> struct is_proto_enum< ::protobuf_unittest::Proto2MapEnumPlusExtraLite> : ::google::protobuf::internal::true_type {};
template <> struct is_proto_enum< ::protobuf_unittest::MapEnumLite> : ::google::protobuf::internal::true_type {};

}  // namespace protobuf
}  // namespace google
#endif  // SWIG

// @@protoc_insertion_point(global_scope)

#endif  // PROTOBUF_google_2fprotobuf_2fmap_5flite_5funittest_2eproto__INCLUDED

// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: google/protobuf/unittest_well_known_types.proto

#define INTERNAL_SUPPRESS_PROTOBUF_FIELD_DEPRECATION
#include <google/protobuf/unittest_well_known_types.pb.h>

#include <algorithm>

#include <google/protobuf/stubs/common.h>
#include <google/protobuf/stubs/port.h>
#include <google/protobuf/stubs/once.h>
#include <google/protobuf/io/coded_stream.h>
#include <google/protobuf/wire_format_lite_inl.h>
#include <google/protobuf/descriptor.h>
#include <google/protobuf/generated_message_reflection.h>
#include <google/protobuf/reflection_ops.h>
#include <google/protobuf/wire_format.h>
// @@protoc_insertion_point(includes)

namespace protobuf_unittest {

namespace {

const ::google::protobuf::Descriptor* TestWellKnownTypes_descriptor_ = NULL;
const ::google::protobuf::internal::GeneratedMessageReflection*
  TestWellKnownTypes_reflection_ = NULL;
const ::google::protobuf::Descriptor* RepeatedWellKnownTypes_descriptor_ = NULL;
const ::google::protobuf::internal::GeneratedMessageReflection*
  RepeatedWellKnownTypes_reflection_ = NULL;
const ::google::protobuf::Descriptor* OneofWellKnownTypes_descriptor_ = NULL;
const ::google::protobuf::internal::GeneratedMessageReflection*
  OneofWellKnownTypes_reflection_ = NULL;
struct OneofWellKnownTypesOneofInstance {
  const ::google::protobuf::Any* any_field_;
  const ::google::protobuf::Api* api_field_;
  const ::google::protobuf::Duration* duration_field_;
  const ::google::protobuf::Empty* empty_field_;
  const ::google::protobuf::FieldMask* field_mask_field_;
  const ::google::protobuf::SourceContext* source_context_field_;
  const ::google::protobuf::Struct* struct_field_;
  const ::google::protobuf::Timestamp* timestamp_field_;
  const ::google::protobuf::Type* type_field_;
  const ::google::protobuf::DoubleValue* double_field_;
  const ::google::protobuf::FloatValue* float_field_;
  const ::google::protobuf::Int64Value* int64_field_;
  const ::google::protobuf::UInt64Value* uint64_field_;
  const ::google::protobuf::Int32Value* int32_field_;
  const ::google::protobuf::UInt32Value* uint32_field_;
  const ::google::protobuf::BoolValue* bool_field_;
  const ::google::protobuf::StringValue* string_field_;
  const ::google::protobuf::BytesValue* bytes_field_;
}* OneofWellKnownTypes_default_oneof_instance_ = NULL;
const ::google::protobuf::Descriptor* MapWellKnownTypes_descriptor_ = NULL;
const ::google::protobuf::internal::GeneratedMessageReflection*
  MapWellKnownTypes_reflection_ = NULL;
const ::google::protobuf::Descriptor* MapWellKnownTypes_AnyFieldEntry_descriptor_ = NULL;
const ::google::protobuf::Descriptor* MapWellKnownTypes_ApiFieldEntry_descriptor_ = NULL;
const ::google::protobuf::Descriptor* MapWellKnownTypes_DurationFieldEntry_descriptor_ = NULL;
const ::google::protobuf::Descriptor* MapWellKnownTypes_EmptyFieldEntry_descriptor_ = NULL;
const ::google::protobuf::Descriptor* MapWellKnownTypes_FieldMaskFieldEntry_descriptor_ = NULL;
const ::google::protobuf::Descriptor* MapWellKnownTypes_SourceContextFieldEntry_descriptor_ = NULL;
const ::google::protobuf::Descriptor* MapWellKnownTypes_StructFieldEntry_descriptor_ = NULL;
const ::google::protobuf::Descriptor* MapWellKnownTypes_TimestampFieldEntry_descriptor_ = NULL;
const ::google::protobuf::Descriptor* MapWellKnownTypes_TypeFieldEntry_descriptor_ = NULL;
const ::google::protobuf::Descriptor* MapWellKnownTypes_DoubleFieldEntry_descriptor_ = NULL;
const ::google::protobuf::Descriptor* MapWellKnownTypes_FloatFieldEntry_descriptor_ = NULL;
const ::google::protobuf::Descriptor* MapWellKnownTypes_Int64FieldEntry_descriptor_ = NULL;
const ::google::protobuf::Descriptor* MapWellKnownTypes_Uint64FieldEntry_descriptor_ = NULL;
const ::google::protobuf::Descriptor* MapWellKnownTypes_Int32FieldEntry_descriptor_ = NULL;
const ::google::protobuf::Descriptor* MapWellKnownTypes_Uint32FieldEntry_descriptor_ = NULL;
const ::google::protobuf::Descriptor* MapWellKnownTypes_BoolFieldEntry_descriptor_ = NULL;
const ::google::protobuf::Descriptor* MapWellKnownTypes_StringFieldEntry_descriptor_ = NULL;
const ::google::protobuf::Descriptor* MapWellKnownTypes_BytesFieldEntry_descriptor_ = NULL;

}  // namespace


void protobuf_AssignDesc_google_2fprotobuf_2funittest_5fwell_5fknown_5ftypes_2eproto() GOOGLE_ATTRIBUTE_COLD;
void protobuf_AssignDesc_google_2fprotobuf_2funittest_5fwell_5fknown_5ftypes_2eproto() {
  protobuf_AddDesc_google_2fprotobuf_2funittest_5fwell_5fknown_5ftypes_2eproto();
  const ::google::protobuf::FileDescriptor* file =
    ::google::protobuf::DescriptorPool::generated_pool()->FindFileByName(
      "google/protobuf/unittest_well_known_types.proto");
  GOOGLE_CHECK(file != NULL);
  TestWellKnownTypes_descriptor_ = file->message_type(0);
  static const int TestWellKnownTypes_offsets_[19] = {
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(TestWellKnownTypes, any_field_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(TestWellKnownTypes, api_field_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(TestWellKnownTypes, duration_field_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(TestWellKnownTypes, empty_field_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(TestWellKnownTypes, field_mask_field_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(TestWellKnownTypes, source_context_field_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(TestWellKnownTypes, struct_field_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(TestWellKnownTypes, timestamp_field_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(TestWellKnownTypes, type_field_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(TestWellKnownTypes, double_field_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(TestWellKnownTypes, float_field_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(TestWellKnownTypes, int64_field_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(TestWellKnownTypes, uint64_field_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(TestWellKnownTypes, int32_field_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(TestWellKnownTypes, uint32_field_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(TestWellKnownTypes, bool_field_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(TestWellKnownTypes, string_field_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(TestWellKnownTypes, bytes_field_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(TestWellKnownTypes, value_field_),
  };
  TestWellKnownTypes_reflection_ =
    ::google::protobuf::internal::GeneratedMessageReflection::NewGeneratedMessageReflection(
      TestWellKnownTypes_descriptor_,
      TestWellKnownTypes::internal_default_instance(),
      TestWellKnownTypes_offsets_,
      -1,
      -1,
      -1,
      sizeof(TestWellKnownTypes),
      GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(TestWellKnownTypes, _internal_metadata_));
  RepeatedWellKnownTypes_descriptor_ = file->message_type(1);
  static const int RepeatedWellKnownTypes_offsets_[18] = {
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(RepeatedWellKnownTypes, any_field_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(RepeatedWellKnownTypes, api_field_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(RepeatedWellKnownTypes, duration_field_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(RepeatedWellKnownTypes, empty_field_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(RepeatedWellKnownTypes, field_mask_field_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(RepeatedWellKnownTypes, source_context_field_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(RepeatedWellKnownTypes, struct_field_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(RepeatedWellKnownTypes, timestamp_field_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(RepeatedWellKnownTypes, type_field_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(RepeatedWellKnownTypes, double_field_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(RepeatedWellKnownTypes, float_field_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(RepeatedWellKnownTypes, int64_field_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(RepeatedWellKnownTypes, uint64_field_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(RepeatedWellKnownTypes, int32_field_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(RepeatedWellKnownTypes, uint32_field_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(RepeatedWellKnownTypes, bool_field_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(RepeatedWellKnownTypes, string_field_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(RepeatedWellKnownTypes, bytes_field_),
  };
  RepeatedWellKnownTypes_reflection_ =
    ::google::protobuf::internal::GeneratedMessageReflection::NewGeneratedMessageReflection(
      RepeatedWellKnownTypes_descriptor_,
      RepeatedWellKnownTypes::internal_default_instance(),
      RepeatedWellKnownTypes_offsets_,
      -1,
      -1,
      -1,
      sizeof(RepeatedWellKnownTypes),
      GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(RepeatedWellKnownTypes, _internal_metadata_));
  OneofWellKnownTypes_descriptor_ = file->message_type(2);
  static const int OneofWellKnownTypes_offsets_[19] = {
    PROTO2_GENERATED_DEFAULT_ONEOF_FIELD_OFFSET(OneofWellKnownTypes_default_oneof_instance_, any_field_),
    PROTO2_GENERATED_DEFAULT_ONEOF_FIELD_OFFSET(OneofWellKnownTypes_default_oneof_instance_, api_field_),
    PROTO2_GENERATED_DEFAULT_ONEOF_FIELD_OFFSET(OneofWellKnownTypes_default_oneof_instance_, duration_field_),
    PROTO2_GENERATED_DEFAULT_ONEOF_FIELD_OFFSET(OneofWellKnownTypes_default_oneof_instance_, empty_field_),
    PROTO2_GENERATED_DEFAULT_ONEOF_FIELD_OFFSET(OneofWellKnownTypes_default_oneof_instance_, field_mask_field_),
    PROTO2_GENERATED_DEFAULT_ONEOF_FIELD_OFFSET(OneofWellKnownTypes_default_oneof_instance_, source_context_field_),
    PROTO2_GENERATED_DEFAULT_ONEOF_FIELD_OFFSET(OneofWellKnownTypes_default_oneof_instance_, struct_field_),
    PROTO2_GENERATED_DEFAULT_ONEOF_FIELD_OFFSET(OneofWellKnownTypes_default_oneof_instance_, timestamp_field_),
    PROTO2_GENERATED_DEFAULT_ONEOF_FIELD_OFFSET(OneofWellKnownTypes_default_oneof_instance_, type_field_),
    PROTO2_GENERATED_DEFAULT_ONEOF_FIELD_OFFSET(OneofWellKnownTypes_default_oneof_instance_, double_field_),
    PROTO2_GENERATED_DEFAULT_ONEOF_FIELD_OFFSET(OneofWellKnownTypes_default_oneof_instance_, float_field_),
    PROTO2_GENERATED_DEFAULT_ONEOF_FIELD_OFFSET(OneofWellKnownTypes_default_oneof_instance_, int64_field_),
    PROTO2_GENERATED_DEFAULT_ONEOF_FIELD_OFFSET(OneofWellKnownTypes_default_oneof_instance_, uint64_field_),
    PROTO2_GENERATED_DEFAULT_ONEOF_FIELD_OFFSET(OneofWellKnownTypes_default_oneof_instance_, int32_field_),
    PROTO2_GENERATED_DEFAULT_ONEOF_FIELD_OFFSET(OneofWellKnownTypes_default_oneof_instance_, uint32_field_),
    PROTO2_GENERATED_DEFAULT_ONEOF_FIELD_OFFSET(OneofWellKnownTypes_default_oneof_instance_, bool_field_),
    PROTO2_GENERATED_DEFAULT_ONEOF_FIELD_OFFSET(OneofWellKnownTypes_default_oneof_instance_, string_field_),
    PROTO2_GENERATED_DEFAULT_ONEOF_FIELD_OFFSET(OneofWellKnownTypes_default_oneof_instance_, bytes_field_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(OneofWellKnownTypes, oneof_field_),
  };
  OneofWellKnownTypes_reflection_ =
    ::google::protobuf::internal::GeneratedMessageReflection::NewGeneratedMessageReflection(
      OneofWellKnownTypes_descriptor_,
      OneofWellKnownTypes::internal_default_instance(),
      OneofWellKnownTypes_offsets_,
      -1,
      -1,
      -1,
      OneofWellKnownTypes_default_oneof_instance_,
      GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(OneofWellKnownTypes, _oneof_case_[0]),
      sizeof(OneofWellKnownTypes),
      GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(OneofWellKnownTypes, _internal_metadata_));
  MapWellKnownTypes_descriptor_ = file->message_type(3);
  static const int MapWellKnownTypes_offsets_[18] = {
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MapWellKnownTypes, any_field_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MapWellKnownTypes, api_field_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MapWellKnownTypes, duration_field_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MapWellKnownTypes, empty_field_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MapWellKnownTypes, field_mask_field_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MapWellKnownTypes, source_context_field_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MapWellKnownTypes, struct_field_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MapWellKnownTypes, timestamp_field_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MapWellKnownTypes, type_field_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MapWellKnownTypes, double_field_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MapWellKnownTypes, float_field_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MapWellKnownTypes, int64_field_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MapWellKnownTypes, uint64_field_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MapWellKnownTypes, int32_field_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MapWellKnownTypes, uint32_field_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MapWellKnownTypes, bool_field_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MapWellKnownTypes, string_field_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MapWellKnownTypes, bytes_field_),
  };
  MapWellKnownTypes_reflection_ =
    ::google::protobuf::internal::GeneratedMessageReflection::NewGeneratedMessageReflection(
      MapWellKnownTypes_descriptor_,
      MapWellKnownTypes::internal_default_instance(),
      MapWellKnownTypes_offsets_,
      -1,
      -1,
      -1,
      sizeof(MapWellKnownTypes),
      GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MapWellKnownTypes, _internal_metadata_));
  MapWellKnownTypes_AnyFieldEntry_descriptor_ = MapWellKnownTypes_descriptor_->nested_type(0);
  MapWellKnownTypes_ApiFieldEntry_descriptor_ = MapWellKnownTypes_descriptor_->nested_type(1);
  MapWellKnownTypes_DurationFieldEntry_descriptor_ = MapWellKnownTypes_descriptor_->nested_type(2);
  MapWellKnownTypes_EmptyFieldEntry_descriptor_ = MapWellKnownTypes_descriptor_->nested_type(3);
  MapWellKnownTypes_FieldMaskFieldEntry_descriptor_ = MapWellKnownTypes_descriptor_->nested_type(4);
  MapWellKnownTypes_SourceContextFieldEntry_descriptor_ = MapWellKnownTypes_descriptor_->nested_type(5);
  MapWellKnownTypes_StructFieldEntry_descriptor_ = MapWellKnownTypes_descriptor_->nested_type(6);
  MapWellKnownTypes_TimestampFieldEntry_descriptor_ = MapWellKnownTypes_descriptor_->nested_type(7);
  MapWellKnownTypes_TypeFieldEntry_descriptor_ = MapWellKnownTypes_descriptor_->nested_type(8);
  MapWellKnownTypes_DoubleFieldEntry_descriptor_ = MapWellKnownTypes_descriptor_->nested_type(9);
  MapWellKnownTypes_FloatFieldEntry_descriptor_ = MapWellKnownTypes_descriptor_->nested_type(10);
  MapWellKnownTypes_Int64FieldEntry_descriptor_ = MapWellKnownTypes_descriptor_->nested_type(11);
  MapWellKnownTypes_Uint64FieldEntry_descriptor_ = MapWellKnownTypes_descriptor_->nested_type(12);
  MapWellKnownTypes_Int32FieldEntry_descriptor_ = MapWellKnownTypes_descriptor_->nested_type(13);
  MapWellKnownTypes_Uint32FieldEntry_descriptor_ = MapWellKnownTypes_descriptor_->nested_type(14);
  MapWellKnownTypes_BoolFieldEntry_descriptor_ = MapWellKnownTypes_descriptor_->nested_type(15);
  MapWellKnownTypes_StringFieldEntry_descriptor_ = MapWellKnownTypes_descriptor_->nested_type(16);
  MapWellKnownTypes_BytesFieldEntry_descriptor_ = MapWellKnownTypes_descriptor_->nested_type(17);
}

namespace {

GOOGLE_PROTOBUF_DECLARE_ONCE(protobuf_AssignDescriptors_once_);
void protobuf_AssignDescriptorsOnce() {
  ::google::protobuf::GoogleOnceInit(&protobuf_AssignDescriptors_once_,
                 &protobuf_AssignDesc_google_2fprotobuf_2funittest_5fwell_5fknown_5ftypes_2eproto);
}

void protobuf_RegisterTypes(const ::std::string&) GOOGLE_ATTRIBUTE_COLD;
void protobuf_RegisterTypes(const ::std::string&) {
  protobuf_AssignDescriptorsOnce();
  ::google::protobuf::MessageFactory::InternalRegisterGeneratedMessage(
      TestWellKnownTypes_descriptor_, TestWellKnownTypes::internal_default_instance());
  ::google::protobuf::MessageFactory::InternalRegisterGeneratedMessage(
      RepeatedWellKnownTypes_descriptor_, RepeatedWellKnownTypes::internal_default_instance());
  ::google::protobuf::MessageFactory::InternalRegisterGeneratedMessage(
      OneofWellKnownTypes_descriptor_, OneofWellKnownTypes::internal_default_instance());
  ::google::protobuf::MessageFactory::InternalRegisterGeneratedMessage(
      MapWellKnownTypes_descriptor_, MapWellKnownTypes::internal_default_instance());
  ::google::protobuf::MessageFactory::InternalRegisterGeneratedMessage(
        MapWellKnownTypes_AnyFieldEntry_descriptor_,
        ::google::protobuf::internal::MapEntry<
            ::google::protobuf::int32,
            ::google::protobuf::Any,
            ::google::protobuf::internal::WireFormatLite::TYPE_INT32,
            ::google::protobuf::internal::WireFormatLite::TYPE_MESSAGE,
            0>::CreateDefaultInstance(
                MapWellKnownTypes_AnyFieldEntry_descriptor_));
  ::google::protobuf::MessageFactory::InternalRegisterGeneratedMessage(
        MapWellKnownTypes_ApiFieldEntry_descriptor_,
        ::google::protobuf::internal::MapEntry<
            ::google::protobuf::int32,
            ::google::protobuf::Api,
            ::google::protobuf::internal::WireFormatLite::TYPE_INT32,
            ::google::protobuf::internal::WireFormatLite::TYPE_MESSAGE,
            0>::CreateDefaultInstance(
                MapWellKnownTypes_ApiFieldEntry_descriptor_));
  ::google::protobuf::MessageFactory::InternalRegisterGeneratedMessage(
        MapWellKnownTypes_DurationFieldEntry_descriptor_,
        ::google::protobuf::internal::MapEntry<
            ::google::protobuf::int32,
            ::google::protobuf::Duration,
            ::google::protobuf::internal::WireFormatLite::TYPE_INT32,
            ::google::protobuf::internal::WireFormatLite::TYPE_MESSAGE,
            0>::CreateDefaultInstance(
                MapWellKnownTypes_DurationFieldEntry_descriptor_));
  ::google::protobuf::MessageFactory::InternalRegisterGeneratedMessage(
        MapWellKnownTypes_EmptyFieldEntry_descriptor_,
        ::google::protobuf::internal::MapEntry<
            ::google::protobuf::int32,
            ::google::protobuf::Empty,
            ::google::protobuf::internal::WireFormatLite::TYPE_INT32,
            ::google::protobuf::internal::WireFormatLite::TYPE_MESSAGE,
            0>::CreateDefaultInstance(
                MapWellKnownTypes_EmptyFieldEntry_descriptor_));
  ::google::protobuf::MessageFactory::InternalRegisterGeneratedMessage(
        MapWellKnownTypes_FieldMaskFieldEntry_descriptor_,
        ::google::protobuf::internal::MapEntry<
            ::google::protobuf::int32,
            ::google::protobuf::FieldMask,
            ::google::protobuf::internal::WireFormatLite::TYPE_INT32,
            ::google::protobuf::internal::WireFormatLite::TYPE_MESSAGE,
            0>::CreateDefaultInstance(
                MapWellKnownTypes_FieldMaskFieldEntry_descriptor_));
  ::google::protobuf::MessageFactory::InternalRegisterGeneratedMessage(
        MapWellKnownTypes_SourceContextFieldEntry_descriptor_,
        ::google::protobuf::internal::MapEntry<
            ::google::protobuf::int32,
            ::google::protobuf::SourceContext,
            ::google::protobuf::internal::WireFormatLite::TYPE_INT32,
            ::google::protobuf::internal::WireFormatLite::TYPE_MESSAGE,
            0>::CreateDefaultInstance(
                MapWellKnownTypes_SourceContextFieldEntry_descriptor_));
  ::google::protobuf::MessageFactory::InternalRegisterGeneratedMessage(
        MapWellKnownTypes_StructFieldEntry_descriptor_,
        ::google::protobuf::internal::MapEntry<
            ::google::protobuf::int32,
            ::google::protobuf::Struct,
            ::google::protobuf::internal::WireFormatLite::TYPE_INT32,
            ::google::protobuf::internal::WireFormatLite::TYPE_MESSAGE,
            0>::CreateDefaultInstance(
                MapWellKnownTypes_StructFieldEntry_descriptor_));
  ::google::protobuf::MessageFactory::InternalRegisterGeneratedMessage(
        MapWellKnownTypes_TimestampFieldEntry_descriptor_,
        ::google::protobuf::internal::MapEntry<
            ::google::protobuf::int32,
            ::google::protobuf::Timestamp,
            ::google::protobuf::internal::WireFormatLite::TYPE_INT32,
            ::google::protobuf::internal::WireFormatLite::TYPE_MESSAGE,
            0>::CreateDefaultInstance(
                MapWellKnownTypes_TimestampFieldEntry_descriptor_));
  ::google::protobuf::MessageFactory::InternalRegisterGeneratedMessage(
        MapWellKnownTypes_TypeFieldEntry_descriptor_,
        ::google::protobuf::internal::MapEntry<
            ::google::protobuf::int32,
            ::google::protobuf::Type,
            ::google::protobuf::internal::WireFormatLite::TYPE_INT32,
            ::google::protobuf::internal::WireFormatLite::TYPE_MESSAGE,
            0>::CreateDefaultInstance(
                MapWellKnownTypes_TypeFieldEntry_descriptor_));
  ::google::protobuf::MessageFactory::InternalRegisterGeneratedMessage(
        MapWellKnownTypes_DoubleFieldEntry_descriptor_,
        ::google::protobuf::internal::MapEntry<
            ::google::protobuf::int32,
            ::google::protobuf::DoubleValue,
            ::google::protobuf::internal::WireFormatLite::TYPE_INT32,
            ::google::protobuf::internal::WireFormatLite::TYPE_MESSAGE,
            0>::CreateDefaultInstance(
                MapWellKnownTypes_DoubleFieldEntry_descriptor_));
  ::google::protobuf::MessageFactory::InternalRegisterGeneratedMessage(
        MapWellKnownTypes_FloatFieldEntry_descriptor_,
        ::google::protobuf::internal::MapEntry<
            ::google::protobuf::int32,
            ::google::protobuf::FloatValue,
            ::google::protobuf::internal::WireFormatLite::TYPE_INT32,
            ::google::protobuf::internal::WireFormatLite::TYPE_MESSAGE,
            0>::CreateDefaultInstance(
                MapWellKnownTypes_FloatFieldEntry_descriptor_));
  ::google::protobuf::MessageFactory::InternalRegisterGeneratedMessage(
        MapWellKnownTypes_Int64FieldEntry_descriptor_,
        ::google::protobuf::internal::MapEntry<
            ::google::protobuf::int32,
            ::google::protobuf::Int64Value,
            ::google::protobuf::internal::WireFormatLite::TYPE_INT32,
            ::google::protobuf::internal::WireFormatLite::TYPE_MESSAGE,
            0>::CreateDefaultInstance(
                MapWellKnownTypes_Int64FieldEntry_descriptor_));
  ::google::protobuf::MessageFactory::InternalRegisterGeneratedMessage(
        MapWellKnownTypes_Uint64FieldEntry_descriptor_,
        ::google::protobuf::internal::MapEntry<
            ::google::protobuf::int32,
            ::google::protobuf::UInt64Value,
            ::google::protobuf::internal::WireFormatLite::TYPE_INT32,
            ::google::protobuf::internal::WireFormatLite::TYPE_MESSAGE,
            0>::CreateDefaultInstance(
                MapWellKnownTypes_Uint64FieldEntry_descriptor_));
  ::google::protobuf::MessageFactory::InternalRegisterGeneratedMessage(
        MapWellKnownTypes_Int32FieldEntry_descriptor_,
        ::google::protobuf::internal::MapEntry<
            ::google::protobuf::int32,
            ::google::protobuf::Int32Value,
            ::google::protobuf::internal::WireFormatLite::TYPE_INT32,
            ::google::protobuf::internal::WireFormatLite::TYPE_MESSAGE,
            0>::CreateDefaultInstance(
                MapWellKnownTypes_Int32FieldEntry_descriptor_));
  ::google::protobuf::MessageFactory::InternalRegisterGeneratedMessage(
        MapWellKnownTypes_Uint32FieldEntry_descriptor_,
        ::google::protobuf::internal::MapEntry<
            ::google::protobuf::int32,
            ::google::protobuf::UInt32Value,
            ::google::protobuf::internal::WireFormatLite::TYPE_INT32,
            ::google::protobuf::internal::WireFormatLite::TYPE_MESSAGE,
            0>::CreateDefaultInstance(
                MapWellKnownTypes_Uint32FieldEntry_descriptor_));
  ::google::protobuf::MessageFactory::InternalRegisterGeneratedMessage(
        MapWellKnownTypes_BoolFieldEntry_descriptor_,
        ::google::protobuf::internal::MapEntry<
            ::google::protobuf::int32,
            ::google::protobuf::BoolValue,
            ::google::protobuf::internal::WireFormatLite::TYPE_INT32,
            ::google::protobuf::internal::WireFormatLite::TYPE_MESSAGE,
            0>::CreateDefaultInstance(
                MapWellKnownTypes_BoolFieldEntry_descriptor_));
  ::google::protobuf::MessageFactory::InternalRegisterGeneratedMessage(
        MapWellKnownTypes_StringFieldEntry_descriptor_,
        ::google::protobuf::internal::MapEntry<
            ::google::protobuf::int32,
            ::google::protobuf::StringValue,
            ::google::protobuf::internal::WireFormatLite::TYPE_INT32,
            ::google::protobuf::internal::WireFormatLite::TYPE_MESSAGE,
            0>::CreateDefaultInstance(
                MapWellKnownTypes_StringFieldEntry_descriptor_));
  ::google::protobuf::MessageFactory::InternalRegisterGeneratedMessage(
        MapWellKnownTypes_BytesFieldEntry_descriptor_,
        ::google::protobuf::internal::MapEntry<
            ::google::protobuf::int32,
            ::google::protobuf::BytesValue,
            ::google::protobuf::internal::WireFormatLite::TYPE_INT32,
            ::google::protobuf::internal::WireFormatLite::TYPE_MESSAGE,
            0>::CreateDefaultInstance(
                MapWellKnownTypes_BytesFieldEntry_descriptor_));
}

}  // namespace

void protobuf_ShutdownFile_google_2fprotobuf_2funittest_5fwell_5fknown_5ftypes_2eproto() {
  TestWellKnownTypes_default_instance_.Shutdown();
  delete TestWellKnownTypes_reflection_;
  RepeatedWellKnownTypes_default_instance_.Shutdown();
  delete RepeatedWellKnownTypes_reflection_;
  OneofWellKnownTypes_default_instance_.Shutdown();
  delete OneofWellKnownTypes_default_oneof_instance_;
  delete OneofWellKnownTypes_reflection_;
  MapWellKnownTypes_default_instance_.Shutdown();
  delete MapWellKnownTypes_reflection_;
}

void protobuf_InitDefaults_google_2fprotobuf_2funittest_5fwell_5fknown_5ftypes_2eproto_impl() {
  GOOGLE_PROTOBUF_VERIFY_VERSION;

  ::google::protobuf::protobuf_InitDefaults_google_2fprotobuf_2fany_2eproto();
  ::google::protobuf::protobuf_InitDefaults_google_2fprotobuf_2fapi_2eproto();
  ::google::protobuf::protobuf_InitDefaults_google_2fprotobuf_2fduration_2eproto();
  ::google::protobuf::protobuf_InitDefaults_google_2fprotobuf_2fempty_2eproto();
  ::google::protobuf::protobuf_InitDefaults_google_2fprotobuf_2ffield_5fmask_2eproto();
  ::google::protobuf::protobuf_InitDefaults_google_2fprotobuf_2fsource_5fcontext_2eproto();
  ::google::protobuf::protobuf_InitDefaults_google_2fprotobuf_2fstruct_2eproto();
  ::google::protobuf::protobuf_InitDefaults_google_2fprotobuf_2ftimestamp_2eproto();
  ::google::protobuf::protobuf_InitDefaults_google_2fprotobuf_2ftype_2eproto();
  ::google::protobuf::protobuf_InitDefaults_google_2fprotobuf_2fwrappers_2eproto();
  TestWellKnownTypes_default_instance_.DefaultConstruct();
  RepeatedWellKnownTypes_default_instance_.DefaultConstruct();
  OneofWellKnownTypes_default_instance_.DefaultConstruct();
  OneofWellKnownTypes_default_oneof_instance_ = new OneofWellKnownTypesOneofInstance();
  MapWellKnownTypes_default_instance_.DefaultConstruct();
  ::google::protobuf::internal::GetEmptyString();
  ::google::protobuf::internal::GetEmptyString();
  ::google::protobuf::internal::GetEmptyString();
  ::google::protobuf::internal::GetEmptyString();
  ::google::protobuf::internal::GetEmptyString();
  TestWellKnownTypes_default_instance_.get_mutable()->InitAsDefaultInstance();
  RepeatedWellKnownTypes_default_instance_.get_mutable()->InitAsDefaultInstance();
  OneofWellKnownTypes_default_instance_.get_mutable()->InitAsDefaultInstance();
  MapWellKnownTypes_default_instance_.get_mutable()->InitAsDefaultInstance();
}

GOOGLE_PROTOBUF_DECLARE_ONCE(protobuf_InitDefaults_google_2fprotobuf_2funittest_5fwell_5fknown_5ftypes_2eproto_once_);
void protobuf_InitDefaults_google_2fprotobuf_2funittest_5fwell_5fknown_5ftypes_2eproto() {
  ::google::protobuf::GoogleOnceInit(&protobuf_InitDefaults_google_2fprotobuf_2funittest_5fwell_5fknown_5ftypes_2eproto_once_,
                 &protobuf_InitDefaults_google_2fprotobuf_2funittest_5fwell_5fknown_5ftypes_2eproto_impl);
}
void protobuf_AddDesc_google_2fprotobuf_2funittest_5fwell_5fknown_5ftypes_2eproto_impl() {
  GOOGLE_PROTOBUF_VERIFY_VERSION;

  protobuf_InitDefaults_google_2fprotobuf_2funittest_5fwell_5fknown_5ftypes_2eproto();
  ::google::protobuf::DescriptorPool::InternalAddGeneratedFile(
    "\n/google/protobuf/unittest_well_known_ty"
    "pes.proto\022\021protobuf_unittest\032\031google/pro"
    "tobuf/any.proto\032\031google/protobuf/api.pro"
    "to\032\036google/protobuf/duration.proto\032\033goog"
    "le/protobuf/empty.proto\032 google/protobuf"
    "/field_mask.proto\032$google/protobuf/sourc"
    "e_context.proto\032\034google/protobuf/struct."
    "proto\032\037google/protobuf/timestamp.proto\032\032"
    "google/protobuf/type.proto\032\036google/proto"
    "buf/wrappers.proto\"\276\007\n\022TestWellKnownType"
    "s\022\'\n\tany_field\030\001 \001(\0132\024.google.protobuf.A"
    "ny\022\'\n\tapi_field\030\002 \001(\0132\024.google.protobuf."
    "Api\0221\n\016duration_field\030\003 \001(\0132\031.google.pro"
    "tobuf.Duration\022+\n\013empty_field\030\004 \001(\0132\026.go"
    "ogle.protobuf.Empty\0224\n\020field_mask_field\030"
    "\005 \001(\0132\032.google.protobuf.FieldMask\022<\n\024sou"
    "rce_context_field\030\006 \001(\0132\036.google.protobu"
    "f.SourceContext\022-\n\014struct_field\030\007 \001(\0132\027."
    "google.protobuf.Struct\0223\n\017timestamp_fiel"
    "d\030\010 \001(\0132\032.google.protobuf.Timestamp\022)\n\nt"
    "ype_field\030\t \001(\0132\025.google.protobuf.Type\0222"
    "\n\014double_field\030\n \001(\0132\034.google.protobuf.D"
    "oubleValue\0220\n\013float_field\030\013 \001(\0132\033.google"
    ".protobuf.FloatValue\0220\n\013int64_field\030\014 \001("
    "\0132\033.google.protobuf.Int64Value\0222\n\014uint64"
    "_field\030\r \001(\0132\034.google.protobuf.UInt64Val"
    "ue\0220\n\013int32_field\030\016 \001(\0132\033.google.protobu"
    "f.Int32Value\0222\n\014uint32_field\030\017 \001(\0132\034.goo"
    "gle.protobuf.UInt32Value\022.\n\nbool_field\030\020"
    " \001(\0132\032.google.protobuf.BoolValue\0222\n\014stri"
    "ng_field\030\021 \001(\0132\034.google.protobuf.StringV"
    "alue\0220\n\013bytes_field\030\022 \001(\0132\033.google.proto"
    "buf.BytesValue\022+\n\013value_field\030\023 \001(\0132\026.go"
    "ogle.protobuf.Value\"\225\007\n\026RepeatedWellKnow"
    "nTypes\022\'\n\tany_field\030\001 \003(\0132\024.google.proto"
    "buf.Any\022\'\n\tapi_field\030\002 \003(\0132\024.google.prot"
    "obuf.Api\0221\n\016duration_field\030\003 \003(\0132\031.googl"
    "e.protobuf.Duration\022+\n\013empty_field\030\004 \003(\013"
    "2\026.google.protobuf.Empty\0224\n\020field_mask_f"
    "ield\030\005 \003(\0132\032.google.protobuf.FieldMask\022<"
    "\n\024source_context_field\030\006 \003(\0132\036.google.pr"
    "otobuf.SourceContext\022-\n\014struct_field\030\007 \003"
    "(\0132\027.google.protobuf.Struct\0223\n\017timestamp"
    "_field\030\010 \003(\0132\032.google.protobuf.Timestamp"
    "\022)\n\ntype_field\030\t \003(\0132\025.google.protobuf.T"
    "ype\0222\n\014double_field\030\n \003(\0132\034.google.proto"
    "buf.DoubleValue\0220\n\013float_field\030\013 \003(\0132\033.g"
    "oogle.protobuf.FloatValue\0220\n\013int64_field"
    "\030\014 \003(\0132\033.google.protobuf.Int64Value\0222\n\014u"
    "int64_field\030\r \003(\0132\034.google.protobuf.UInt"
    "64Value\0220\n\013int32_field\030\016 \003(\0132\033.google.pr"
    "otobuf.Int32Value\0222\n\014uint32_field\030\017 \003(\0132"
    "\034.google.protobuf.UInt32Value\022.\n\nbool_fi"
    "eld\030\020 \003(\0132\032.google.protobuf.BoolValue\0222\n"
    "\014string_field\030\021 \003(\0132\034.google.protobuf.St"
    "ringValue\0220\n\013bytes_field\030\022 \003(\0132\033.google."
    "protobuf.BytesValue\"\305\007\n\023OneofWellKnownTy"
    "pes\022)\n\tany_field\030\001 \001(\0132\024.google.protobuf"
    ".AnyH\000\022)\n\tapi_field\030\002 \001(\0132\024.google.proto"
    "buf.ApiH\000\0223\n\016duration_field\030\003 \001(\0132\031.goog"
    "le.protobuf.DurationH\000\022-\n\013empty_field\030\004 "
    "\001(\0132\026.google.protobuf.EmptyH\000\0226\n\020field_m"
    "ask_field\030\005 \001(\0132\032.google.protobuf.FieldM"
    "askH\000\022>\n\024source_context_field\030\006 \001(\0132\036.go"
    "ogle.protobuf.SourceContextH\000\022/\n\014struct_"
    "field\030\007 \001(\0132\027.google.protobuf.StructH\000\0225"
    "\n\017timestamp_field\030\010 \001(\0132\032.google.protobu"
    "f.TimestampH\000\022+\n\ntype_field\030\t \001(\0132\025.goog"
    "le.protobuf.TypeH\000\0224\n\014double_field\030\n \001(\013"
    "2\034.google.protobuf.DoubleValueH\000\0222\n\013floa"
    "t_field\030\013 \001(\0132\033.google.protobuf.FloatVal"
    "ueH\000\0222\n\013int64_field\030\014 \001(\0132\033.google.proto"
    "buf.Int64ValueH\000\0224\n\014uint64_field\030\r \001(\0132\034"
    ".google.protobuf.UInt64ValueH\000\0222\n\013int32_"
    "field\030\016 \001(\0132\033.google.protobuf.Int32Value"
    "H\000\0224\n\014uint32_field\030\017 \001(\0132\034.google.protob"
    "uf.UInt32ValueH\000\0220\n\nbool_field\030\020 \001(\0132\032.g"
    "oogle.protobuf.BoolValueH\000\0224\n\014string_fie"
    "ld\030\021 \001(\0132\034.google.protobuf.StringValueH\000"
    "\0222\n\013bytes_field\030\022 \001(\0132\033.google.protobuf."
    "BytesValueH\000B\r\n\013oneof_field\"\226\026\n\021MapWellK"
    "nownTypes\022E\n\tany_field\030\001 \003(\01322.protobuf_"
    "unittest.MapWellKnownTypes.AnyFieldEntry"
    "\022E\n\tapi_field\030\002 \003(\01322.protobuf_unittest."
    "MapWellKnownTypes.ApiFieldEntry\022O\n\016durat"
    "ion_field\030\003 \003(\01327.protobuf_unittest.MapW"
    "ellKnownTypes.DurationFieldEntry\022I\n\013empt"
    "y_field\030\004 \003(\01324.protobuf_unittest.MapWel"
    "lKnownTypes.EmptyFieldEntry\022R\n\020field_mas"
    "k_field\030\005 \003(\01328.protobuf_unittest.MapWel"
    "lKnownTypes.FieldMaskFieldEntry\022Z\n\024sourc"
    "e_context_field\030\006 \003(\0132<.protobuf_unittes"
    "t.MapWellKnownTypes.SourceContextFieldEn"
    "try\022K\n\014struct_field\030\007 \003(\01325.protobuf_uni"
    "ttest.MapWellKnownTypes.StructFieldEntry"
    "\022Q\n\017timestamp_field\030\010 \003(\01328.protobuf_uni"
    "ttest.MapWellKnownTypes.TimestampFieldEn"
    "try\022G\n\ntype_field\030\t \003(\01323.protobuf_unitt"
    "est.MapWellKnownTypes.TypeFieldEntry\022K\n\014"
    "double_field\030\n \003(\01325.protobuf_unittest.M"
    "apWellKnownTypes.DoubleFieldEntry\022I\n\013flo"
    "at_field\030\013 \003(\01324.protobuf_unittest.MapWe"
    "llKnownTypes.FloatFieldEntry\022I\n\013int64_fi"
    "eld\030\014 \003(\01324.protobuf_unittest.MapWellKno"
    "wnTypes.Int64FieldEntry\022K\n\014uint64_field\030"
    "\r \003(\01325.protobuf_unittest.MapWellKnownTy"
    "pes.Uint64FieldEntry\022I\n\013int32_field\030\016 \003("
    "\01324.protobuf_unittest.MapWellKnownTypes."
    "Int32FieldEntry\022K\n\014uint32_field\030\017 \003(\01325."
    "protobuf_unittest.MapWellKnownTypes.Uint"
    "32FieldEntry\022G\n\nbool_field\030\020 \003(\01323.proto"
    "buf_unittest.MapWellKnownTypes.BoolField"
    "Entry\022K\n\014string_field\030\021 \003(\01325.protobuf_u"
    "nittest.MapWellKnownTypes.StringFieldEnt"
    "ry\022I\n\013bytes_field\030\022 \003(\01324.protobuf_unitt"
    "est.MapWellKnownTypes.BytesFieldEntry\032E\n"
    "\rAnyFieldEntry\022\013\n\003key\030\001 \001(\005\022#\n\005value\030\002 \001"
    "(\0132\024.google.protobuf.Any:\0028\001\032E\n\rApiField"
    "Entry\022\013\n\003key\030\001 \001(\005\022#\n\005value\030\002 \001(\0132\024.goog"
    "le.protobuf.Api:\0028\001\032O\n\022DurationFieldEntr"
    "y\022\013\n\003key\030\001 \001(\005\022(\n\005value\030\002 \001(\0132\031.google.p"
    "rotobuf.Duration:\0028\001\032I\n\017EmptyFieldEntry\022"
    "\013\n\003key\030\001 \001(\005\022%\n\005value\030\002 \001(\0132\026.google.pro"
    "tobuf.Empty:\0028\001\032Q\n\023FieldMaskFieldEntry\022\013"
    "\n\003key\030\001 \001(\005\022)\n\005value\030\002 \001(\0132\032.google.prot"
    "obuf.FieldMask:\0028\001\032Y\n\027SourceContextField"
    "Entry\022\013\n\003key\030\001 \001(\005\022-\n\005value\030\002 \001(\0132\036.goog"
    "le.protobuf.SourceContext:\0028\001\032K\n\020StructF"
    "ieldEntry\022\013\n\003key\030\001 \001(\005\022&\n\005value\030\002 \001(\0132\027."
    "google.protobuf.Struct:\0028\001\032Q\n\023TimestampF"
    "ieldEntry\022\013\n\003key\030\001 \001(\005\022)\n\005value\030\002 \001(\0132\032."
    "google.protobuf.Timestamp:\0028\001\032G\n\016TypeFie"
    "ldEntry\022\013\n\003key\030\001 \001(\005\022$\n\005value\030\002 \001(\0132\025.go"
    "ogle.protobuf.Type:\0028\001\032P\n\020DoubleFieldEnt"
    "ry\022\013\n\003key\030\001 \001(\005\022+\n\005value\030\002 \001(\0132\034.google."
    "protobuf.DoubleValue:\0028\001\032N\n\017FloatFieldEn"
    "try\022\013\n\003key\030\001 \001(\005\022*\n\005value\030\002 \001(\0132\033.google"
    ".protobuf.FloatValue:\0028\001\032N\n\017Int64FieldEn"
    "try\022\013\n\003key\030\001 \001(\005\022*\n\005value\030\002 \001(\0132\033.google"
    ".protobuf.Int64Value:\0028\001\032P\n\020Uint64FieldE"
    "ntry\022\013\n\003key\030\001 \001(\005\022+\n\005value\030\002 \001(\0132\034.googl"
    "e.protobuf.UInt64Value:\0028\001\032N\n\017Int32Field"
    "Entry\022\013\n\003key\030\001 \001(\005\022*\n\005value\030\002 \001(\0132\033.goog"
    "le.protobuf.Int32Value:\0028\001\032P\n\020Uint32Fiel"
    "dEntry\022\013\n\003key\030\001 \001(\005\022+\n\005value\030\002 \001(\0132\034.goo"
    "gle.protobuf.UInt32Value:\0028\001\032L\n\016BoolFiel"
    "dEntry\022\013\n\003key\030\001 \001(\005\022)\n\005value\030\002 \001(\0132\032.goo"
    "gle.protobuf.BoolValue:\0028\001\032P\n\020StringFiel"
    "dEntry\022\013\n\003key\030\001 \001(\005\022+\n\005value\030\002 \001(\0132\034.goo"
    "gle.protobuf.StringValue:\0028\001\032N\n\017BytesFie"
    "ldEntry\022\013\n\003key\030\001 \001(\005\022*\n\005value\030\002 \001(\0132\033.go"
    "ogle.protobuf.BytesValue:\0028\001B9\n\030com.goog"
    "le.protobuf.testP\001\252\002\032Google.Protobuf.Tes"
    "tProtosb\006proto3", 6135);
  ::google::protobuf::MessageFactory::InternalRegisterGeneratedFile(
    "google/protobuf/unittest_well_known_types.proto", &protobuf_RegisterTypes);
  ::google::protobuf::protobuf_AddDesc_google_2fprotobuf_2fany_2eproto();
  ::google::protobuf::protobuf_AddDesc_google_2fprotobuf_2fapi_2eproto();
  ::google::protobuf::protobuf_AddDesc_google_2fprotobuf_2fduration_2eproto();
  ::google::protobuf::protobuf_AddDesc_google_2fprotobuf_2fempty_2eproto();
  ::google::protobuf::protobuf_AddDesc_google_2fprotobuf_2ffield_5fmask_2eproto();
  ::google::protobuf::protobuf_AddDesc_google_2fprotobuf_2fsource_5fcontext_2eproto();
  ::google::protobuf::protobuf_AddDesc_google_2fprotobuf_2fstruct_2eproto();
  ::google::protobuf::protobuf_AddDesc_google_2fprotobuf_2ftimestamp_2eproto();
  ::google::protobuf::protobuf_AddDesc_google_2fprotobuf_2ftype_2eproto();
  ::google::protobuf::protobuf_AddDesc_google_2fprotobuf_2fwrappers_2eproto();
  ::google::protobuf::internal::OnShutdown(&protobuf_ShutdownFile_google_2fprotobuf_2funittest_5fwell_5fknown_5ftypes_2eproto);
}

GOOGLE_PROTOBUF_DECLARE_ONCE(protobuf_AddDesc_google_2fprotobuf_2funittest_5fwell_5fknown_5ftypes_2eproto_once_);
void protobuf_AddDesc_google_2fprotobuf_2funittest_5fwell_5fknown_5ftypes_2eproto() {
  ::google::protobuf::GoogleOnceInit(&protobuf_AddDesc_google_2fprotobuf_2funittest_5fwell_5fknown_5ftypes_2eproto_once_,
                 &protobuf_AddDesc_google_2fprotobuf_2funittest_5fwell_5fknown_5ftypes_2eproto_impl);
}
// Force AddDescriptors() to be called at static initialization time.
struct StaticDescriptorInitializer_google_2fprotobuf_2funittest_5fwell_5fknown_5ftypes_2eproto {
  StaticDescriptorInitializer_google_2fprotobuf_2funittest_5fwell_5fknown_5ftypes_2eproto() {
    protobuf_AddDesc_google_2fprotobuf_2funittest_5fwell_5fknown_5ftypes_2eproto();
  }
} static_descriptor_initializer_google_2fprotobuf_2funittest_5fwell_5fknown_5ftypes_2eproto_;

namespace {

static void MergeFromFail(int line) GOOGLE_ATTRIBUTE_COLD GOOGLE_ATTRIBUTE_NORETURN;
static void MergeFromFail(int line) {
  ::google::protobuf::internal::MergeFromFail(__FILE__, line);
}

}  // namespace


// ===================================================================

#if !defined(_MSC_VER) || _MSC_VER >= 1900
const int TestWellKnownTypes::kAnyFieldFieldNumber;
const int TestWellKnownTypes::kApiFieldFieldNumber;
const int TestWellKnownTypes::kDurationFieldFieldNumber;
const int TestWellKnownTypes::kEmptyFieldFieldNumber;
const int TestWellKnownTypes::kFieldMaskFieldFieldNumber;
const int TestWellKnownTypes::kSourceContextFieldFieldNumber;
const int TestWellKnownTypes::kStructFieldFieldNumber;
const int TestWellKnownTypes::kTimestampFieldFieldNumber;
const int TestWellKnownTypes::kTypeFieldFieldNumber;
const int TestWellKnownTypes::kDoubleFieldFieldNumber;
const int TestWellKnownTypes::kFloatFieldFieldNumber;
const int TestWellKnownTypes::kInt64FieldFieldNumber;
const int TestWellKnownTypes::kUint64FieldFieldNumber;
const int TestWellKnownTypes::kInt32FieldFieldNumber;
const int TestWellKnownTypes::kUint32FieldFieldNumber;
const int TestWellKnownTypes::kBoolFieldFieldNumber;
const int TestWellKnownTypes::kStringFieldFieldNumber;
const int TestWellKnownTypes::kBytesFieldFieldNumber;
const int TestWellKnownTypes::kValueFieldFieldNumber;
#endif  // !defined(_MSC_VER) || _MSC_VER >= 1900

TestWellKnownTypes::TestWellKnownTypes()
  : ::google::protobuf::Message(), _internal_metadata_(NULL) {
  if (this != internal_default_instance()) protobuf_InitDefaults_google_2fprotobuf_2funittest_5fwell_5fknown_5ftypes_2eproto();
  SharedCtor();
  // @@protoc_insertion_point(constructor:protobuf_unittest.TestWellKnownTypes)
}

void TestWellKnownTypes::InitAsDefaultInstance() {
  any_field_ = const_cast< ::google::protobuf::Any*>(
      ::google::protobuf::Any::internal_default_instance());
  api_field_ = const_cast< ::google::protobuf::Api*>(
      ::google::protobuf::Api::internal_default_instance());
  duration_field_ = const_cast< ::google::protobuf::Duration*>(
      ::google::protobuf::Duration::internal_default_instance());
  empty_field_ = const_cast< ::google::protobuf::Empty*>(
      ::google::protobuf::Empty::internal_default_instance());
  field_mask_field_ = const_cast< ::google::protobuf::FieldMask*>(
      ::google::protobuf::FieldMask::internal_default_instance());
  source_context_field_ = const_cast< ::google::protobuf::SourceContext*>(
      ::google::protobuf::SourceContext::internal_default_instance());
  struct_field_ = const_cast< ::google::protobuf::Struct*>(
      ::google::protobuf::Struct::internal_default_instance());
  timestamp_field_ = const_cast< ::google::protobuf::Timestamp*>(
      ::google::protobuf::Timestamp::internal_default_instance());
  type_field_ = const_cast< ::google::protobuf::Type*>(
      ::google::protobuf::Type::internal_default_instance());
  double_field_ = const_cast< ::google::protobuf::DoubleValue*>(
      ::google::protobuf::DoubleValue::internal_default_instance());
  float_field_ = const_cast< ::google::protobuf::FloatValue*>(
      ::google::protobuf::FloatValue::internal_default_instance());
  int64_field_ = const_cast< ::google::protobuf::Int64Value*>(
      ::google::protobuf::Int64Value::internal_default_instance());
  uint64_field_ = const_cast< ::google::protobuf::UInt64Value*>(
      ::google::protobuf::UInt64Value::internal_default_instance());
  int32_field_ = const_cast< ::google::protobuf::Int32Value*>(
      ::google::protobuf::Int32Value::internal_default_instance());
  uint32_field_ = const_cast< ::google::protobuf::UInt32Value*>(
      ::google::protobuf::UInt32Value::internal_default_instance());
  bool_field_ = const_cast< ::google::protobuf::BoolValue*>(
      ::google::protobuf::BoolValue::internal_default_instance());
  string_field_ = const_cast< ::google::protobuf::StringValue*>(
      ::google::protobuf::StringValue::internal_default_instance());
  bytes_field_ = const_cast< ::google::protobuf::BytesValue*>(
      ::google::protobuf::BytesValue::internal_default_instance());
  value_field_ = const_cast< ::google::protobuf::Value*>(
      ::google::protobuf::Value::internal_default_instance());
}

TestWellKnownTypes::TestWellKnownTypes(const TestWellKnownTypes& from)
  : ::google::protobuf::Message(),
    _internal_metadata_(NULL) {
  SharedCtor();
  UnsafeMergeFrom(from);
  // @@protoc_insertion_point(copy_constructor:protobuf_unittest.TestWellKnownTypes)
}

void TestWellKnownTypes::SharedCtor() {
  any_field_ = NULL;
  api_field_ = NULL;
  duration_field_ = NULL;
  empty_field_ = NULL;
  field_mask_field_ = NULL;
  source_context_field_ = NULL;
  struct_field_ = NULL;
  timestamp_field_ = NULL;
  type_field_ = NULL;
  double_field_ = NULL;
  float_field_ = NULL;
  int64_field_ = NULL;
  uint64_field_ = NULL;
  int32_field_ = NULL;
  uint32_field_ = NULL;
  bool_field_ = NULL;
  string_field_ = NULL;
  bytes_field_ = NULL;
  value_field_ = NULL;
  _cached_size_ = 0;
}

TestWellKnownTypes::~TestWellKnownTypes() {
  // @@protoc_insertion_point(destructor:protobuf_unittest.TestWellKnownTypes)
  SharedDtor();
}

void TestWellKnownTypes::SharedDtor() {
  if (this != &TestWellKnownTypes_default_instance_.get()) {
    delete any_field_;
    delete api_field_;
    delete duration_field_;
    delete empty_field_;
    delete field_mask_field_;
    delete source_context_field_;
    delete struct_field_;
    delete timestamp_field_;
    delete type_field_;
    delete double_field_;
    delete float_field_;
    delete int64_field_;
    delete uint64_field_;
    delete int32_field_;
    delete uint32_field_;
    delete bool_field_;
    delete string_field_;
    delete bytes_field_;
    delete value_field_;
  }
}

void TestWellKnownTypes::SetCachedSize(int size) const {
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
}
const ::google::protobuf::Descriptor* TestWellKnownTypes::descriptor() {
  protobuf_AssignDescriptorsOnce();
  return TestWellKnownTypes_descriptor_;
}

const TestWellKnownTypes& TestWellKnownTypes::default_instance() {
  protobuf_InitDefaults_google_2fprotobuf_2funittest_5fwell_5fknown_5ftypes_2eproto();
  return *internal_default_instance();
}

::google::protobuf::internal::ExplicitlyConstructed<TestWellKnownTypes> TestWellKnownTypes_default_instance_;

TestWellKnownTypes* TestWellKnownTypes::New(::google::protobuf::Arena* arena) const {
  TestWellKnownTypes* n = new TestWellKnownTypes;
  if (arena != NULL) {
    arena->Own(n);
  }
  return n;
}

void TestWellKnownTypes::Clear() {
// @@protoc_insertion_point(message_clear_start:protobuf_unittest.TestWellKnownTypes)
  if (GetArenaNoVirtual() == NULL && any_field_ != NULL) delete any_field_;
  any_field_ = NULL;
  if (GetArenaNoVirtual() == NULL && api_field_ != NULL) delete api_field_;
  api_field_ = NULL;
  if (GetArenaNoVirtual() == NULL && duration_field_ != NULL) delete duration_field_;
  duration_field_ = NULL;
  if (GetArenaNoVirtual() == NULL && empty_field_ != NULL) delete empty_field_;
  empty_field_ = NULL;
  if (GetArenaNoVirtual() == NULL && field_mask_field_ != NULL) delete field_mask_field_;
  field_mask_field_ = NULL;
  if (GetArenaNoVirtual() == NULL && source_context_field_ != NULL) delete source_context_field_;
  source_context_field_ = NULL;
  if (GetArenaNoVirtual() == NULL && struct_field_ != NULL) delete struct_field_;
  struct_field_ = NULL;
  if (GetArenaNoVirtual() == NULL && timestamp_field_ != NULL) delete timestamp_field_;
  timestamp_field_ = NULL;
  if (GetArenaNoVirtual() == NULL && type_field_ != NULL) delete type_field_;
  type_field_ = NULL;
  if (GetArenaNoVirtual() == NULL && double_field_ != NULL) delete double_field_;
  double_field_ = NULL;
  if (GetArenaNoVirtual() == NULL && float_field_ != NULL) delete float_field_;
  float_field_ = NULL;
  if (GetArenaNoVirtual() == NULL && int64_field_ != NULL) delete int64_field_;
  int64_field_ = NULL;
  if (GetArenaNoVirtual() == NULL && uint64_field_ != NULL) delete uint64_field_;
  uint64_field_ = NULL;
  if (GetArenaNoVirtual() == NULL && int32_field_ != NULL) delete int32_field_;
  int32_field_ = NULL;
  if (GetArenaNoVirtual() == NULL && uint32_field_ != NULL) delete uint32_field_;
  uint32_field_ = NULL;
  if (GetArenaNoVirtual() == NULL && bool_field_ != NULL) delete bool_field_;
  bool_field_ = NULL;
  if (GetArenaNoVirtual() == NULL && string_field_ != NULL) delete string_field_;
  string_field_ = NULL;
  if (GetArenaNoVirtual() == NULL && bytes_field_ != NULL) delete bytes_field_;
  bytes_field_ = NULL;
  if (GetArenaNoVirtual() == NULL && value_field_ != NULL) delete value_field_;
  value_field_ = NULL;
}

bool TestWellKnownTypes::MergePartialFromCodedStream(
    ::google::protobuf::io::CodedInputStream* input) {
#define DO_(EXPRESSION) if (!GOOGLE_PREDICT_TRUE(EXPRESSION)) goto failure
  ::google::protobuf::uint32 tag;
  // @@protoc_insertion_point(parse_start:protobuf_unittest.TestWellKnownTypes)
  for (;;) {
    ::std::pair< ::google::protobuf::uint32, bool> p = input->ReadTagWithCutoff(16383);
    tag = p.first;
    if (!p.second) goto handle_unusual;
    switch (::google::protobuf::internal::WireFormatLite::GetTagFieldNumber(tag)) {
      // optional .google.protobuf.Any any_field = 1;
      case 1: {
        if (tag == 10) {
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessageNoVirtual(
               input, mutable_any_field()));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(18)) goto parse_api_field;
        break;
      }

      // optional .google.protobuf.Api api_field = 2;
      case 2: {
        if (tag == 18) {
         parse_api_field:
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessageNoVirtual(
               input, mutable_api_field()));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(26)) goto parse_duration_field;
        break;
      }

      // optional .google.protobuf.Duration duration_field = 3;
      case 3: {
        if (tag == 26) {
         parse_duration_field:
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessageNoVirtual(
               input, mutable_duration_field()));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(34)) goto parse_empty_field;
        break;
      }

      // optional .google.protobuf.Empty empty_field = 4;
      case 4: {
        if (tag == 34) {
         parse_empty_field:
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessageNoVirtual(
               input, mutable_empty_field()));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(42)) goto parse_field_mask_field;
        break;
      }

      // optional .google.protobuf.FieldMask field_mask_field = 5;
      case 5: {
        if (tag == 42) {
         parse_field_mask_field:
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessageNoVirtual(
               input, mutable_field_mask_field()));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(50)) goto parse_source_context_field;
        break;
      }

      // optional .google.protobuf.SourceContext source_context_field = 6;
      case 6: {
        if (tag == 50) {
         parse_source_context_field:
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessageNoVirtual(
               input, mutable_source_context_field()));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(58)) goto parse_struct_field;
        break;
      }

      // optional .google.protobuf.Struct struct_field = 7;
      case 7: {
        if (tag == 58) {
         parse_struct_field:
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessageNoVirtual(
               input, mutable_struct_field()));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(66)) goto parse_timestamp_field;
        break;
      }

      // optional .google.protobuf.Timestamp timestamp_field = 8;
      case 8: {
        if (tag == 66) {
         parse_timestamp_field:
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessageNoVirtual(
               input, mutable_timestamp_field()));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(74)) goto parse_type_field;
        break;
      }

      // optional .google.protobuf.Type type_field = 9;
      case 9: {
        if (tag == 74) {
         parse_type_field:
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessageNoVirtual(
               input, mutable_type_field()));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(82)) goto parse_double_field;
        break;
      }

      // optional .google.protobuf.DoubleValue double_field = 10;
      case 10: {
        if (tag == 82) {
         parse_double_field:
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessageNoVirtual(
               input, mutable_double_field()));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(90)) goto parse_float_field;
        break;
      }

      // optional .google.protobuf.FloatValue float_field = 11;
      case 11: {
        if (tag == 90) {
         parse_float_field:
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessageNoVirtual(
               input, mutable_float_field()));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(98)) goto parse_int64_field;
        break;
      }

      // optional .google.protobuf.Int64Value int64_field = 12;
      case 12: {
        if (tag == 98) {
         parse_int64_field:
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessageNoVirtual(
               input, mutable_int64_field()));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(106)) goto parse_uint64_field;
        break;
      }

      // optional .google.protobuf.UInt64Value uint64_field = 13;
      case 13: {
        if (tag == 106) {
         parse_uint64_field:
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessageNoVirtual(
               input, mutable_uint64_field()));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(114)) goto parse_int32_field;
        break;
      }

      // optional .google.protobuf.Int32Value int32_field = 14;
      case 14: {
        if (tag == 114) {
         parse_int32_field:
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessageNoVirtual(
               input, mutable_int32_field()));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(122)) goto parse_uint32_field;
        break;
      }

      // optional .google.protobuf.UInt32Value uint32_field = 15;
      case 15: {
        if (tag == 122) {
         parse_uint32_field:
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessageNoVirtual(
               input, mutable_uint32_field()));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(130)) goto parse_bool_field;
        break;
      }

      // optional .google.protobuf.BoolValue bool_field = 16;
      case 16: {
        if (tag == 130) {
         parse_bool_field:
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessageNoVirtual(
               input, mutable_bool_field()));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(138)) goto parse_string_field;
        break;
      }

      // optional .google.protobuf.StringValue string_field = 17;
      case 17: {
        if (tag == 138) {
         parse_string_field:
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessageNoVirtual(
               input, mutable_string_field()));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(146)) goto parse_bytes_field;
        break;
      }

      // optional .google.protobuf.BytesValue bytes_field = 18;
      case 18: {
        if (tag == 146) {
         parse_bytes_field:
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessageNoVirtual(
               input, mutable_bytes_field()));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(154)) goto parse_value_field;
        break;
      }

      // optional .google.protobuf.Value value_field = 19;
      case 19: {
        if (tag == 154) {
         parse_value_field:
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessageNoVirtual(
               input, mutable_value_field()));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectAtEnd()) goto success;
        break;
      }

      default: {
      handle_unusual:
        if (tag == 0 ||
            ::google::protobuf::internal::WireFormatLite::GetTagWireType(tag) ==
            ::google::protobuf::internal::WireFormatLite::WIRETYPE_END_GROUP) {
          goto success;
        }
        DO_(::google::protobuf::internal::WireFormatLite::SkipField(input, tag));
        break;
      }
    }
  }
success:
  // @@protoc_insertion_point(parse_success:protobuf_unittest.TestWellKnownTypes)
  return true;
failure:
  // @@protoc_insertion_point(parse_failure:protobuf_unittest.TestWellKnownTypes)
  return false;
#undef DO_
}

void TestWellKnownTypes::SerializeWithCachedSizes(
    ::google::protobuf::io::CodedOutputStream* output) const {
  // @@protoc_insertion_point(serialize_start:protobuf_unittest.TestWellKnownTypes)
  // optional .google.protobuf.Any any_field = 1;
  if (this->has_any_field()) {
    ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
      1, *this->any_field_, output);
  }

  // optional .google.protobuf.Api api_field = 2;
  if (this->has_api_field()) {
    ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
      2, *this->api_field_, output);
  }

  // optional .google.protobuf.Duration duration_field = 3;
  if (this->has_duration_field()) {
    ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
      3, *this->duration_field_, output);
  }

  // optional .google.protobuf.Empty empty_field = 4;
  if (this->has_empty_field()) {
    ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
      4, *this->empty_field_, output);
  }

  // optional .google.protobuf.FieldMask field_mask_field = 5;
  if (this->has_field_mask_field()) {
    ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
      5, *this->field_mask_field_, output);
  }

  // optional .google.protobuf.SourceContext source_context_field = 6;
  if (this->has_source_context_field()) {
    ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
      6, *this->source_context_field_, output);
  }

  // optional .google.protobuf.Struct struct_field = 7;
  if (this->has_struct_field()) {
    ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
      7, *this->struct_field_, output);
  }

  // optional .google.protobuf.Timestamp timestamp_field = 8;
  if (this->has_timestamp_field()) {
    ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
      8, *this->timestamp_field_, output);
  }

  // optional .google.protobuf.Type type_field = 9;
  if (this->has_type_field()) {
    ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
      9, *this->type_field_, output);
  }

  // optional .google.protobuf.DoubleValue double_field = 10;
  if (this->has_double_field()) {
    ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
      10, *this->double_field_, output);
  }

  // optional .google.protobuf.FloatValue float_field = 11;
  if (this->has_float_field()) {
    ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
      11, *this->float_field_, output);
  }

  // optional .google.protobuf.Int64Value int64_field = 12;
  if (this->has_int64_field()) {
    ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
      12, *this->int64_field_, output);
  }

  // optional .google.protobuf.UInt64Value uint64_field = 13;
  if (this->has_uint64_field()) {
    ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
      13, *this->uint64_field_, output);
  }

  // optional .google.protobuf.Int32Value int32_field = 14;
  if (this->has_int32_field()) {
    ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
      14, *this->int32_field_, output);
  }

  // optional .google.protobuf.UInt32Value uint32_field = 15;
  if (this->has_uint32_field()) {
    ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
      15, *this->uint32_field_, output);
  }

  // optional .google.protobuf.BoolValue bool_field = 16;
  if (this->has_bool_field()) {
    ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
      16, *this->bool_field_, output);
  }

  // optional .google.protobuf.StringValue string_field = 17;
  if (this->has_string_field()) {
    ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
      17, *this->string_field_, output);
  }

  // optional .google.protobuf.BytesValue bytes_field = 18;
  if (this->has_bytes_field()) {
    ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
      18, *this->bytes_field_, output);
  }

  // optional .google.protobuf.Value value_field = 19;
  if (this->has_value_field()) {
    ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
      19, *this->value_field_, output);
  }

  // @@protoc_insertion_point(serialize_end:protobuf_unittest.TestWellKnownTypes)
}

::google::protobuf::uint8* TestWellKnownTypes::InternalSerializeWithCachedSizesToArray(
    bool deterministic, ::google::protobuf::uint8* target) const {
  (void)deterministic; // Unused
  // @@protoc_insertion_point(serialize_to_array_start:protobuf_unittest.TestWellKnownTypes)
  // optional .google.protobuf.Any any_field = 1;
  if (this->has_any_field()) {
    target = ::google::protobuf::internal::WireFormatLite::
      InternalWriteMessageNoVirtualToArray(
        1, *this->any_field_, false, target);
  }

  // optional .google.protobuf.Api api_field = 2;
  if (this->has_api_field()) {
    target = ::google::protobuf::internal::WireFormatLite::
      InternalWriteMessageNoVirtualToArray(
        2, *this->api_field_, false, target);
  }

  // optional .google.protobuf.Duration duration_field = 3;
  if (this->has_duration_field()) {
    target = ::google::protobuf::internal::WireFormatLite::
      InternalWriteMessageNoVirtualToArray(
        3, *this->duration_field_, false, target);
  }

  // optional .google.protobuf.Empty empty_field = 4;
  if (this->has_empty_field()) {
    target = ::google::protobuf::internal::WireFormatLite::
      InternalWriteMessageNoVirtualToArray(
        4, *this->empty_field_, false, target);
  }

  // optional .google.protobuf.FieldMask field_mask_field = 5;
  if (this->has_field_mask_field()) {
    target = ::google::protobuf::internal::WireFormatLite::
      InternalWriteMessageNoVirtualToArray(
        5, *this->field_mask_field_, false, target);
  }

  // optional .google.protobuf.SourceContext source_context_field = 6;
  if (this->has_source_context_field()) {
    target = ::google::protobuf::internal::WireFormatLite::
      InternalWriteMessageNoVirtualToArray(
        6, *this->source_context_field_, false, target);
  }

  // optional .google.protobuf.Struct struct_field = 7;
  if (this->has_struct_field()) {
    target = ::google::protobuf::internal::WireFormatLite::
      InternalWriteMessageNoVirtualToArray(
        7, *this->struct_field_, false, target);
  }

  // optional .google.protobuf.Timestamp timestamp_field = 8;
  if (this->has_timestamp_field()) {
    target = ::google::protobuf::internal::WireFormatLite::
      InternalWriteMessageNoVirtualToArray(
        8, *this->timestamp_field_, false, target);
  }

  // optional .google.protobuf.Type type_field = 9;
  if (this->has_type_field()) {
    target = ::google::protobuf::internal::WireFormatLite::
      InternalWriteMessageNoVirtualToArray(
        9, *this->type_field_, false, target);
  }

  // optional .google.protobuf.DoubleValue double_field = 10;
  if (this->has_double_field()) {
    target = ::google::protobuf::internal::WireFormatLite::
      InternalWriteMessageNoVirtualToArray(
        10, *this->double_field_, false, target);
  }

  // optional .google.protobuf.FloatValue float_field = 11;
  if (this->has_float_field()) {
    target = ::google::protobuf::internal::WireFormatLite::
      InternalWriteMessageNoVirtualToArray(
        11, *this->float_field_, false, target);
  }

  // optional .google.protobuf.Int64Value int64_field = 12;
  if (this->has_int64_field()) {
    target = ::google::protobuf::internal::WireFormatLite::
      InternalWriteMessageNoVirtualToArray(
        12, *this->int64_field_, false, target);
  }

  // optional .google.protobuf.UInt64Value uint64_field = 13;
  if (this->has_uint64_field()) {
    target = ::google::protobuf::internal::WireFormatLite::
      InternalWriteMessageNoVirtualToArray(
        13, *this->uint64_field_, false, target);
  }

  // optional .google.protobuf.Int32Value int32_field = 14;
  if (this->has_int32_field()) {
    target = ::google::protobuf::internal::WireFormatLite::
      InternalWriteMessageNoVirtualToArray(
        14, *this->int32_field_, false, target);
  }

  // optional .google.protobuf.UInt32Value uint32_field = 15;
  if (this->has_uint32_field()) {
    target = ::google::protobuf::internal::WireFormatLite::
      InternalWriteMessageNoVirtualToArray(
        15, *this->uint32_field_, false, target);
  }

  // optional .google.protobuf.BoolValue bool_field = 16;
  if (this->has_bool_field()) {
    target = ::google::protobuf::internal::WireFormatLite::
      InternalWriteMessageNoVirtualToArray(
        16, *this->bool_field_, false, target);
  }

  // optional .google.protobuf.StringValue string_field = 17;
  if (this->has_string_field()) {
    target = ::google::protobuf::internal::WireFormatLite::
      InternalWriteMessageNoVirtualToArray(
        17, *this->string_field_, false, target);
  }

  // optional .google.protobuf.BytesValue bytes_field = 18;
  if (this->has_bytes_field()) {
    target = ::google::protobuf::internal::WireFormatLite::
      InternalWriteMessageNoVirtualToArray(
        18, *this->bytes_field_, false, target);
  }

  // optional .google.protobuf.Value value_field = 19;
  if (this->has_value_field()) {
    target = ::google::protobuf::internal::WireFormatLite::
      InternalWriteMessageNoVirtualToArray(
        19, *this->value_field_, false, target);
  }

  // @@protoc_insertion_point(serialize_to_array_end:protobuf_unittest.TestWellKnownTypes)
  return target;
}

size_t TestWellKnownTypes::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:protobuf_unittest.TestWellKnownTypes)
  size_t total_size = 0;

  // optional .google.protobuf.Any any_field = 1;
  if (this->has_any_field()) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::MessageSizeNoVirtual(
        *this->any_field_);
  }

  // optional .google.protobuf.Api api_field = 2;
  if (this->has_api_field()) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::MessageSizeNoVirtual(
        *this->api_field_);
  }

  // optional .google.protobuf.Duration duration_field = 3;
  if (this->has_duration_field()) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::MessageSizeNoVirtual(
        *this->duration_field_);
  }

  // optional .google.protobuf.Empty empty_field = 4;
  if (this->has_empty_field()) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::MessageSizeNoVirtual(
        *this->empty_field_);
  }

  // optional .google.protobuf.FieldMask field_mask_field = 5;
  if (this->has_field_mask_field()) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::MessageSizeNoVirtual(
        *this->field_mask_field_);
  }

  // optional .google.protobuf.SourceContext source_context_field = 6;
  if (this->has_source_context_field()) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::MessageSizeNoVirtual(
        *this->source_context_field_);
  }

  // optional .google.protobuf.Struct struct_field = 7;
  if (this->has_struct_field()) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::MessageSizeNoVirtual(
        *this->struct_field_);
  }

  // optional .google.protobuf.Timestamp timestamp_field = 8;
  if (this->has_timestamp_field()) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::MessageSizeNoVirtual(
        *this->timestamp_field_);
  }

  // optional .google.protobuf.Type type_field = 9;
  if (this->has_type_field()) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::MessageSizeNoVirtual(
        *this->type_field_);
  }

  // optional .google.protobuf.DoubleValue double_field = 10;
  if (this->has_double_field()) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::MessageSizeNoVirtual(
        *this->double_field_);
  }

  // optional .google.protobuf.FloatValue float_field = 11;
  if (this->has_float_field()) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::MessageSizeNoVirtual(
        *this->float_field_);
  }

  // optional .google.protobuf.Int64Value int64_field = 12;
  if (this->has_int64_field()) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::MessageSizeNoVirtual(
        *this->int64_field_);
  }

  // optional .google.protobuf.UInt64Value uint64_field = 13;
  if (this->has_uint64_field()) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::MessageSizeNoVirtual(
        *this->uint64_field_);
  }

  // optional .google.protobuf.Int32Value int32_field = 14;
  if (this->has_int32_field()) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::MessageSizeNoVirtual(
        *this->int32_field_);
  }

  // optional .google.protobuf.UInt32Value uint32_field = 15;
  if (this->has_uint32_field()) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::MessageSizeNoVirtual(
        *this->uint32_field_);
  }

  // optional .google.protobuf.BoolValue bool_field = 16;
  if (this->has_bool_field()) {
    total_size += 2 +
      ::google::protobuf::internal::WireFormatLite::MessageSizeNoVirtual(
        *this->bool_field_);
  }

  // optional .google.protobuf.StringValue string_field = 17;
  if (this->has_string_field()) {
    total_size += 2 +
      ::google::protobuf::internal::WireFormatLite::MessageSizeNoVirtual(
        *this->string_field_);
  }

  // optional .google.protobuf.BytesValue bytes_field = 18;
  if (this->has_bytes_field()) {
    total_size += 2 +
      ::google::protobuf::internal::WireFormatLite::MessageSizeNoVirtual(
        *this->bytes_field_);
  }

  // optional .google.protobuf.Value value_field = 19;
  if (this->has_value_field()) {
    total_size += 2 +
      ::google::protobuf::internal::WireFormatLite::MessageSizeNoVirtual(
        *this->value_field_);
  }

  int cached_size = ::google::protobuf::internal::ToCachedSize(total_size);
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = cached_size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
  return total_size;
}

void TestWellKnownTypes::MergeFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_merge_from_start:protobuf_unittest.TestWellKnownTypes)
  if (GOOGLE_PREDICT_FALSE(&from == this)) MergeFromFail(__LINE__);
  const TestWellKnownTypes* source =
      ::google::protobuf::internal::DynamicCastToGenerated<const TestWellKnownTypes>(
          &from);
  if (source == NULL) {
  // @@protoc_insertion_point(generalized_merge_from_cast_fail:protobuf_unittest.TestWellKnownTypes)
    ::google::protobuf::internal::ReflectionOps::Merge(from, this);
  } else {
  // @@protoc_insertion_point(generalized_merge_from_cast_success:protobuf_unittest.TestWellKnownTypes)
    UnsafeMergeFrom(*source);
  }
}

void TestWellKnownTypes::MergeFrom(const TestWellKnownTypes& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:protobuf_unittest.TestWellKnownTypes)
  if (GOOGLE_PREDICT_TRUE(&from != this)) {
    UnsafeMergeFrom(from);
  } else {
    MergeFromFail(__LINE__);
  }
}

void TestWellKnownTypes::UnsafeMergeFrom(const TestWellKnownTypes& from) {
  GOOGLE_DCHECK(&from != this);
  if (from.has_any_field()) {
    mutable_any_field()->::google::protobuf::Any::MergeFrom(from.any_field());
  }
  if (from.has_api_field()) {
    mutable_api_field()->::google::protobuf::Api::MergeFrom(from.api_field());
  }
  if (from.has_duration_field()) {
    mutable_duration_field()->::google::protobuf::Duration::MergeFrom(from.duration_field());
  }
  if (from.has_empty_field()) {
    mutable_empty_field()->::google::protobuf::Empty::MergeFrom(from.empty_field());
  }
  if (from.has_field_mask_field()) {
    mutable_field_mask_field()->::google::protobuf::FieldMask::MergeFrom(from.field_mask_field());
  }
  if (from.has_source_context_field()) {
    mutable_source_context_field()->::google::protobuf::SourceContext::MergeFrom(from.source_context_field());
  }
  if (from.has_struct_field()) {
    mutable_struct_field()->::google::protobuf::Struct::MergeFrom(from.struct_field());
  }
  if (from.has_timestamp_field()) {
    mutable_timestamp_field()->::google::protobuf::Timestamp::MergeFrom(from.timestamp_field());
  }
  if (from.has_type_field()) {
    mutable_type_field()->::google::protobuf::Type::MergeFrom(from.type_field());
  }
  if (from.has_double_field()) {
    mutable_double_field()->::google::protobuf::DoubleValue::MergeFrom(from.double_field());
  }
  if (from.has_float_field()) {
    mutable_float_field()->::google::protobuf::FloatValue::MergeFrom(from.float_field());
  }
  if (from.has_int64_field()) {
    mutable_int64_field()->::google::protobuf::Int64Value::MergeFrom(from.int64_field());
  }
  if (from.has_uint64_field()) {
    mutable_uint64_field()->::google::protobuf::UInt64Value::MergeFrom(from.uint64_field());
  }
  if (from.has_int32_field()) {
    mutable_int32_field()->::google::protobuf::Int32Value::MergeFrom(from.int32_field());
  }
  if (from.has_uint32_field()) {
    mutable_uint32_field()->::google::protobuf::UInt32Value::MergeFrom(from.uint32_field());
  }
  if (from.has_bool_field()) {
    mutable_bool_field()->::google::protobuf::BoolValue::MergeFrom(from.bool_field());
  }
  if (from.has_string_field()) {
    mutable_string_field()->::google::protobuf::StringValue::MergeFrom(from.string_field());
  }
  if (from.has_bytes_field()) {
    mutable_bytes_field()->::google::protobuf::BytesValue::MergeFrom(from.bytes_field());
  }
  if (from.has_value_field()) {
    mutable_value_field()->::google::protobuf::Value::MergeFrom(from.value_field());
  }
}

void TestWellKnownTypes::CopyFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_copy_from_start:protobuf_unittest.TestWellKnownTypes)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

void TestWellKnownTypes::CopyFrom(const TestWellKnownTypes& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:protobuf_unittest.TestWellKnownTypes)
  if (&from == this) return;
  Clear();
  UnsafeMergeFrom(from);
}

bool TestWellKnownTypes::IsInitialized() const {

  return true;
}

void TestWellKnownTypes::Swap(TestWellKnownTypes* other) {
  if (other == this) return;
  InternalSwap(other);
}
void TestWellKnownTypes::InternalSwap(TestWellKnownTypes* other) {
  std::swap(any_field_, other->any_field_);
  std::swap(api_field_, other->api_field_);
  std::swap(duration_field_, other->duration_field_);
  std::swap(empty_field_, other->empty_field_);
  std::swap(field_mask_field_, other->field_mask_field_);
  std::swap(source_context_field_, other->source_context_field_);
  std::swap(struct_field_, other->struct_field_);
  std::swap(timestamp_field_, other->timestamp_field_);
  std::swap(type_field_, other->type_field_);
  std::swap(double_field_, other->double_field_);
  std::swap(float_field_, other->float_field_);
  std::swap(int64_field_, other->int64_field_);
  std::swap(uint64_field_, other->uint64_field_);
  std::swap(int32_field_, other->int32_field_);
  std::swap(uint32_field_, other->uint32_field_);
  std::swap(bool_field_, other->bool_field_);
  std::swap(string_field_, other->string_field_);
  std::swap(bytes_field_, other->bytes_field_);
  std::swap(value_field_, other->value_field_);
  _internal_metadata_.Swap(&other->_internal_metadata_);
  std::swap(_cached_size_, other->_cached_size_);
}

::google::protobuf::Metadata TestWellKnownTypes::GetMetadata() const {
  protobuf_AssignDescriptorsOnce();
  ::google::protobuf::Metadata metadata;
  metadata.descriptor = TestWellKnownTypes_descriptor_;
  metadata.reflection = TestWellKnownTypes_reflection_;
  return metadata;
}

#if PROTOBUF_INLINE_NOT_IN_HEADERS
// TestWellKnownTypes

// optional .google.protobuf.Any any_field = 1;
bool TestWellKnownTypes::has_any_field() const {
  return this != internal_default_instance() && any_field_ != NULL;
}
void TestWellKnownTypes::clear_any_field() {
  if (GetArenaNoVirtual() == NULL && any_field_ != NULL) delete any_field_;
  any_field_ = NULL;
}
const ::google::protobuf::Any& TestWellKnownTypes::any_field() const {
  // @@protoc_insertion_point(field_get:protobuf_unittest.TestWellKnownTypes.any_field)
  return any_field_ != NULL ? *any_field_
                         : *::google::protobuf::Any::internal_default_instance();
}
::google::protobuf::Any* TestWellKnownTypes::mutable_any_field() {
  
  if (any_field_ == NULL) {
    any_field_ = new ::google::protobuf::Any;
  }
  // @@protoc_insertion_point(field_mutable:protobuf_unittest.TestWellKnownTypes.any_field)
  return any_field_;
}
::google::protobuf::Any* TestWellKnownTypes::release_any_field() {
  // @@protoc_insertion_point(field_release:protobuf_unittest.TestWellKnownTypes.any_field)
  
  ::google::protobuf::Any* temp = any_field_;
  any_field_ = NULL;
  return temp;
}
void TestWellKnownTypes::set_allocated_any_field(::google::protobuf::Any* any_field) {
  delete any_field_;
  any_field_ = any_field;
  if (any_field) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_set_allocated:protobuf_unittest.TestWellKnownTypes.any_field)
}

// optional .google.protobuf.Api api_field = 2;
bool TestWellKnownTypes::has_api_field() const {
  return this != internal_default_instance() && api_field_ != NULL;
}
void TestWellKnownTypes::clear_api_field() {
  if (GetArenaNoVirtual() == NULL && api_field_ != NULL) delete api_field_;
  api_field_ = NULL;
}
const ::google::protobuf::Api& TestWellKnownTypes::api_field() const {
  // @@protoc_insertion_point(field_get:protobuf_unittest.TestWellKnownTypes.api_field)
  return api_field_ != NULL ? *api_field_
                         : *::google::protobuf::Api::internal_default_instance();
}
::google::protobuf::Api* TestWellKnownTypes::mutable_api_field() {
  
  if (api_field_ == NULL) {
    api_field_ = new ::google::protobuf::Api;
  }
  // @@protoc_insertion_point(field_mutable:protobuf_unittest.TestWellKnownTypes.api_field)
  return api_field_;
}
::google::protobuf::Api* TestWellKnownTypes::release_api_field() {
  // @@protoc_insertion_point(field_release:protobuf_unittest.TestWellKnownTypes.api_field)
  
  ::google::protobuf::Api* temp = api_field_;
  api_field_ = NULL;
  return temp;
}
void TestWellKnownTypes::set_allocated_api_field(::google::protobuf::Api* api_field) {
  delete api_field_;
  api_field_ = api_field;
  if (api_field) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_set_allocated:protobuf_unittest.TestWellKnownTypes.api_field)
}

// optional .google.protobuf.Duration duration_field = 3;
bool TestWellKnownTypes::has_duration_field() const {
  return this != internal_default_instance() && duration_field_ != NULL;
}
void TestWellKnownTypes::clear_duration_field() {
  if (GetArenaNoVirtual() == NULL && duration_field_ != NULL) delete duration_field_;
  duration_field_ = NULL;
}
const ::google::protobuf::Duration& TestWellKnownTypes::duration_field() const {
  // @@protoc_insertion_point(field_get:protobuf_unittest.TestWellKnownTypes.duration_field)
  return duration_field_ != NULL ? *duration_field_
                         : *::google::protobuf::Duration::internal_default_instance();
}
::google::protobuf::Duration* TestWellKnownTypes::mutable_duration_field() {
  
  if (duration_field_ == NULL) {
    duration_field_ = new ::google::protobuf::Duration;
  }
  // @@protoc_insertion_point(field_mutable:protobuf_unittest.TestWellKnownTypes.duration_field)
  return duration_field_;
}
::google::protobuf::Duration* TestWellKnownTypes::release_duration_field() {
  // @@protoc_insertion_point(field_release:protobuf_unittest.TestWellKnownTypes.duration_field)
  
  ::google::protobuf::Duration* temp = duration_field_;
  duration_field_ = NULL;
  return temp;
}
void TestWellKnownTypes::set_allocated_duration_field(::google::protobuf::Duration* duration_field) {
  delete duration_field_;
  if (duration_field != NULL && duration_field->GetArena() != NULL) {
    ::google::protobuf::Duration* new_duration_field = new ::google::protobuf::Duration;
    new_duration_field->CopyFrom(*duration_field);
    duration_field = new_duration_field;
  }
  duration_field_ = duration_field;
  if (duration_field) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_set_allocated:protobuf_unittest.TestWellKnownTypes.duration_field)
}

// optional .google.protobuf.Empty empty_field = 4;
bool TestWellKnownTypes::has_empty_field() const {
  return this != internal_default_instance() && empty_field_ != NULL;
}
void TestWellKnownTypes::clear_empty_field() {
  if (GetArenaNoVirtual() == NULL && empty_field_ != NULL) delete empty_field_;
  empty_field_ = NULL;
}
const ::google::protobuf::Empty& TestWellKnownTypes::empty_field() const {
  // @@protoc_insertion_point(field_get:protobuf_unittest.TestWellKnownTypes.empty_field)
  return empty_field_ != NULL ? *empty_field_
                         : *::google::protobuf::Empty::internal_default_instance();
}
::google::protobuf::Empty* TestWellKnownTypes::mutable_empty_field() {
  
  if (empty_field_ == NULL) {
    empty_field_ = new ::google::protobuf::Empty;
  }
  // @@protoc_insertion_point(field_mutable:protobuf_unittest.TestWellKnownTypes.empty_field)
  return empty_field_;
}
::google::protobuf::Empty* TestWellKnownTypes::release_empty_field() {
  // @@protoc_insertion_point(field_release:protobuf_unittest.TestWellKnownTypes.empty_field)
  
  ::google::protobuf::Empty* temp = empty_field_;
  empty_field_ = NULL;
  return temp;
}
void TestWellKnownTypes::set_allocated_empty_field(::google::protobuf::Empty* empty_field) {
  delete empty_field_;
  if (empty_field != NULL && empty_field->GetArena() != NULL) {
    ::google::protobuf::Empty* new_empty_field = new ::google::protobuf::Empty;
    new_empty_field->CopyFrom(*empty_field);
    empty_field = new_empty_field;
  }
  empty_field_ = empty_field;
  if (empty_field) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_set_allocated:protobuf_unittest.TestWellKnownTypes.empty_field)
}

// optional .google.protobuf.FieldMask field_mask_field = 5;
bool TestWellKnownTypes::has_field_mask_field() const {
  return this != internal_default_instance() && field_mask_field_ != NULL;
}
void TestWellKnownTypes::clear_field_mask_field() {
  if (GetArenaNoVirtual() == NULL && field_mask_field_ != NULL) delete field_mask_field_;
  field_mask_field_ = NULL;
}
const ::google::protobuf::FieldMask& TestWellKnownTypes::field_mask_field() const {
  // @@protoc_insertion_point(field_get:protobuf_unittest.TestWellKnownTypes.field_mask_field)
  return field_mask_field_ != NULL ? *field_mask_field_
                         : *::google::protobuf::FieldMask::internal_default_instance();
}
::google::protobuf::FieldMask* TestWellKnownTypes::mutable_field_mask_field() {
  
  if (field_mask_field_ == NULL) {
    field_mask_field_ = new ::google::protobuf::FieldMask;
  }
  // @@protoc_insertion_point(field_mutable:protobuf_unittest.TestWellKnownTypes.field_mask_field)
  return field_mask_field_;
}
::google::protobuf::FieldMask* TestWellKnownTypes::release_field_mask_field() {
  // @@protoc_insertion_point(field_release:protobuf_unittest.TestWellKnownTypes.field_mask_field)
  
  ::google::protobuf::FieldMask* temp = field_mask_field_;
  field_mask_field_ = NULL;
  return temp;
}
void TestWellKnownTypes::set_allocated_field_mask_field(::google::protobuf::FieldMask* field_mask_field) {
  delete field_mask_field_;
  field_mask_field_ = field_mask_field;
  if (field_mask_field) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_set_allocated:protobuf_unittest.TestWellKnownTypes.field_mask_field)
}

// optional .google.protobuf.SourceContext source_context_field = 6;
bool TestWellKnownTypes::has_source_context_field() const {
  return this != internal_default_instance() && source_context_field_ != NULL;
}
void TestWellKnownTypes::clear_source_context_field() {
  if (GetArenaNoVirtual() == NULL && source_context_field_ != NULL) delete source_context_field_;
  source_context_field_ = NULL;
}
const ::google::protobuf::SourceContext& TestWellKnownTypes::source_context_field() const {
  // @@protoc_insertion_point(field_get:protobuf_unittest.TestWellKnownTypes.source_context_field)
  return source_context_field_ != NULL ? *source_context_field_
                         : *::google::protobuf::SourceContext::internal_default_instance();
}
::google::protobuf::SourceContext* TestWellKnownTypes::mutable_source_context_field() {
  
  if (source_context_field_ == NULL) {
    source_context_field_ = new ::google::protobuf::SourceContext;
  }
  // @@protoc_insertion_point(field_mutable:protobuf_unittest.TestWellKnownTypes.source_context_field)
  return source_context_field_;
}
::google::protobuf::SourceContext* TestWellKnownTypes::release_source_context_field() {
  // @@protoc_insertion_point(field_release:protobuf_unittest.TestWellKnownTypes.source_context_field)
  
  ::google::protobuf::SourceContext* temp = source_context_field_;
  source_context_field_ = NULL;
  return temp;
}
void TestWellKnownTypes::set_allocated_source_context_field(::google::protobuf::SourceContext* source_context_field) {
  delete source_context_field_;
  source_context_field_ = source_context_field;
  if (source_context_field) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_set_allocated:protobuf_unittest.TestWellKnownTypes.source_context_field)
}

// optional .google.protobuf.Struct struct_field = 7;
bool TestWellKnownTypes::has_struct_field() const {
  return this != internal_default_instance() && struct_field_ != NULL;
}
void TestWellKnownTypes::clear_struct_field() {
  if (GetArenaNoVirtual() == NULL && struct_field_ != NULL) delete struct_field_;
  struct_field_ = NULL;
}
const ::google::protobuf::Struct& TestWellKnownTypes::struct_field() const {
  // @@protoc_insertion_point(field_get:protobuf_unittest.TestWellKnownTypes.struct_field)
  return struct_field_ != NULL ? *struct_field_
                         : *::google::protobuf::Struct::internal_default_instance();
}
::google::protobuf::Struct* TestWellKnownTypes::mutable_struct_field() {
  
  if (struct_field_ == NULL) {
    struct_field_ = new ::google::protobuf::Struct;
  }
  // @@protoc_insertion_point(field_mutable:protobuf_unittest.TestWellKnownTypes.struct_field)
  return struct_field_;
}
::google::protobuf::Struct* TestWellKnownTypes::release_struct_field() {
  // @@protoc_insertion_point(field_release:protobuf_unittest.TestWellKnownTypes.struct_field)
  
  ::google::protobuf::Struct* temp = struct_field_;
  struct_field_ = NULL;
  return temp;
}
void TestWellKnownTypes::set_allocated_struct_field(::google::protobuf::Struct* struct_field) {
  delete struct_field_;
  if (struct_field != NULL && struct_field->GetArena() != NULL) {
    ::google::protobuf::Struct* new_struct_field = new ::google::protobuf::Struct;
    new_struct_field->CopyFrom(*struct_field);
    struct_field = new_struct_field;
  }
  struct_field_ = struct_field;
  if (struct_field) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_set_allocated:protobuf_unittest.TestWellKnownTypes.struct_field)
}

// optional .google.protobuf.Timestamp timestamp_field = 8;
bool TestWellKnownTypes::has_timestamp_field() const {
  return this != internal_default_instance() && timestamp_field_ != NULL;
}
void TestWellKnownTypes::clear_timestamp_field() {
  if (GetArenaNoVirtual() == NULL && timestamp_field_ != NULL) delete timestamp_field_;
  timestamp_field_ = NULL;
}
const ::google::protobuf::Timestamp& TestWellKnownTypes::timestamp_field() const {
  // @@protoc_insertion_point(field_get:protobuf_unittest.TestWellKnownTypes.timestamp_field)
  return timestamp_field_ != NULL ? *timestamp_field_
                         : *::google::protobuf::Timestamp::internal_default_instance();
}
::google::protobuf::Timestamp* TestWellKnownTypes::mutable_timestamp_field() {
  
  if (timestamp_field_ == NULL) {
    timestamp_field_ = new ::google::protobuf::Timestamp;
  }
  // @@protoc_insertion_point(field_mutable:protobuf_unittest.TestWellKnownTypes.timestamp_field)
  return timestamp_field_;
}
::google::protobuf::Timestamp* TestWellKnownTypes::release_timestamp_field() {
  // @@protoc_insertion_point(field_release:protobuf_unittest.TestWellKnownTypes.timestamp_field)
  
  ::google::protobuf::Timestamp* temp = timestamp_field_;
  timestamp_field_ = NULL;
  return temp;
}
void TestWellKnownTypes::set_allocated_timestamp_field(::google::protobuf::Timestamp* timestamp_field) {
  delete timestamp_field_;
  if (timestamp_field != NULL && timestamp_field->GetArena() != NULL) {
    ::google::protobuf::Timestamp* new_timestamp_field = new ::google::protobuf::Timestamp;
    new_timestamp_field->CopyFrom(*timestamp_field);
    timestamp_field = new_timestamp_field;
  }
  timestamp_field_ = timestamp_field;
  if (timestamp_field) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_set_allocated:protobuf_unittest.TestWellKnownTypes.timestamp_field)
}

// optional .google.protobuf.Type type_field = 9;
bool TestWellKnownTypes::has_type_field() const {
  return this != internal_default_instance() && type_field_ != NULL;
}
void TestWellKnownTypes::clear_type_field() {
  if (GetArenaNoVirtual() == NULL && type_field_ != NULL) delete type_field_;
  type_field_ = NULL;
}
const ::google::protobuf::Type& TestWellKnownTypes::type_field() const {
  // @@protoc_insertion_point(field_get:protobuf_unittest.TestWellKnownTypes.type_field)
  return type_field_ != NULL ? *type_field_
                         : *::google::protobuf::Type::internal_default_instance();
}
::google::protobuf::Type* TestWellKnownTypes::mutable_type_field() {
  
  if (type_field_ == NULL) {
    type_field_ = new ::google::protobuf::Type;
  }
  // @@protoc_insertion_point(field_mutable:protobuf_unittest.TestWellKnownTypes.type_field)
  return type_field_;
}
::google::protobuf::Type* TestWellKnownTypes::release_type_field() {
  // @@protoc_insertion_point(field_release:protobuf_unittest.TestWellKnownTypes.type_field)
  
  ::google::protobuf::Type* temp = type_field_;
  type_field_ = NULL;
  return temp;
}
void TestWellKnownTypes::set_allocated_type_field(::google::protobuf::Type* type_field) {
  delete type_field_;
  if (type_field != NULL && type_field->GetArena() != NULL) {
    ::google::protobuf::Type* new_type_field = new ::google::protobuf::Type;
    new_type_field->CopyFrom(*type_field);
    type_field = new_type_field;
  }
  type_field_ = type_field;
  if (type_field) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_set_allocated:protobuf_unittest.TestWellKnownTypes.type_field)
}

// optional .google.protobuf.DoubleValue double_field = 10;
bool TestWellKnownTypes::has_double_field() const {
  return this != internal_default_instance() && double_field_ != NULL;
}
void TestWellKnownTypes::clear_double_field() {
  if (GetArenaNoVirtual() == NULL && double_field_ != NULL) delete double_field_;
  double_field_ = NULL;
}
const ::google::protobuf::DoubleValue& TestWellKnownTypes::double_field() const {
  // @@protoc_insertion_point(field_get:protobuf_unittest.TestWellKnownTypes.double_field)
  return double_field_ != NULL ? *double_field_
                         : *::google::protobuf::DoubleValue::internal_default_instance();
}
::google::protobuf::DoubleValue* TestWellKnownTypes::mutable_double_field() {
  
  if (double_field_ == NULL) {
    double_field_ = new ::google::protobuf::DoubleValue;
  }
  // @@protoc_insertion_point(field_mutable:protobuf_unittest.TestWellKnownTypes.double_field)
  return double_field_;
}
::google::protobuf::DoubleValue* TestWellKnownTypes::release_double_field() {
  // @@protoc_insertion_point(field_release:protobuf_unittest.TestWellKnownTypes.double_field)
  
  ::google::protobuf::DoubleValue* temp = double_field_;
  double_field_ = NULL;
  return temp;
}
void TestWellKnownTypes::set_allocated_double_field(::google::protobuf::DoubleValue* double_field) {
  delete double_field_;
  if (double_field != NULL && double_field->GetArena() != NULL) {
    ::google::protobuf::DoubleValue* new_double_field = new ::google::protobuf::DoubleValue;
    new_double_field->CopyFrom(*double_field);
    double_field = new_double_field;
  }
  double_field_ = double_field;
  if (double_field) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_set_allocated:protobuf_unittest.TestWellKnownTypes.double_field)
}

// optional .google.protobuf.FloatValue float_field = 11;
bool TestWellKnownTypes::has_float_field() const {
  return this != internal_default_instance() && float_field_ != NULL;
}
void TestWellKnownTypes::clear_float_field() {
  if (GetArenaNoVirtual() == NULL && float_field_ != NULL) delete float_field_;
  float_field_ = NULL;
}
const ::google::protobuf::FloatValue& TestWellKnownTypes::float_field() const {
  // @@protoc_insertion_point(field_get:protobuf_unittest.TestWellKnownTypes.float_field)
  return float_field_ != NULL ? *float_field_
                         : *::google::protobuf::FloatValue::internal_default_instance();
}
::google::protobuf::FloatValue* TestWellKnownTypes::mutable_float_field() {
  
  if (float_field_ == NULL) {
    float_field_ = new ::google::protobuf::FloatValue;
  }
  // @@protoc_insertion_point(field_mutable:protobuf_unittest.TestWellKnownTypes.float_field)
  return float_field_;
}
::google::protobuf::FloatValue* TestWellKnownTypes::release_float_field() {
  // @@protoc_insertion_point(field_release:protobuf_unittest.TestWellKnownTypes.float_field)
  
  ::google::protobuf::FloatValue* temp = float_field_;
  float_field_ = NULL;
  return temp;
}
void TestWellKnownTypes::set_allocated_float_field(::google::protobuf::FloatValue* float_field) {
  delete float_field_;
  if (float_field != NULL && float_field->GetArena() != NULL) {
    ::google::protobuf::FloatValue* new_float_field = new ::google::protobuf::FloatValue;
    new_float_field->CopyFrom(*float_field);
    float_field = new_float_field;
  }
  float_field_ = float_field;
  if (float_field) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_set_allocated:protobuf_unittest.TestWellKnownTypes.float_field)
}

// optional .google.protobuf.Int64Value int64_field = 12;
bool TestWellKnownTypes::has_int64_field() const {
  return this != internal_default_instance() && int64_field_ != NULL;
}
void TestWellKnownTypes::clear_int64_field() {
  if (GetArenaNoVirtual() == NULL && int64_field_ != NULL) delete int64_field_;
  int64_field_ = NULL;
}
const ::google::protobuf::Int64Value& TestWellKnownTypes::int64_field() const {
  // @@protoc_insertion_point(field_get:protobuf_unittest.TestWellKnownTypes.int64_field)
  return int64_field_ != NULL ? *int64_field_
                         : *::google::protobuf::Int64Value::internal_default_instance();
}
::google::protobuf::Int64Value* TestWellKnownTypes::mutable_int64_field() {
  
  if (int64_field_ == NULL) {
    int64_field_ = new ::google::protobuf::Int64Value;
  }
  // @@protoc_insertion_point(field_mutable:protobuf_unittest.TestWellKnownTypes.int64_field)
  return int64_field_;
}
::google::protobuf::Int64Value* TestWellKnownTypes::release_int64_field() {
  // @@protoc_insertion_point(field_release:protobuf_unittest.TestWellKnownTypes.int64_field)
  
  ::google::protobuf::Int64Value* temp = int64_field_;
  int64_field_ = NULL;
  return temp;
}
void TestWellKnownTypes::set_allocated_int64_field(::google::protobuf::Int64Value* int64_field) {
  delete int64_field_;
  if (int64_field != NULL && int64_field->GetArena() != NULL) {
    ::google::protobuf::Int64Value* new_int64_field = new ::google::protobuf::Int64Value;
    new_int64_field->CopyFrom(*int64_field);
    int64_field = new_int64_field;
  }
  int64_field_ = int64_field;
  if (int64_field) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_set_allocated:protobuf_unittest.TestWellKnownTypes.int64_field)
}

// optional .google.protobuf.UInt64Value uint64_field = 13;
bool TestWellKnownTypes::has_uint64_field() const {
  return this != internal_default_instance() && uint64_field_ != NULL;
}
void TestWellKnownTypes::clear_uint64_field() {
  if (GetArenaNoVirtual() == NULL && uint64_field_ != NULL) delete uint64_field_;
  uint64_field_ = NULL;
}
const ::google::protobuf::UInt64Value& TestWellKnownTypes::uint64_field() const {
  // @@protoc_insertion_point(field_get:protobuf_unittest.TestWellKnownTypes.uint64_field)
  return uint64_field_ != NULL ? *uint64_field_
                         : *::google::protobuf::UInt64Value::internal_default_instance();
}
::google::protobuf::UInt64Value* TestWellKnownTypes::mutable_uint64_field() {
  
  if (uint64_field_ == NULL) {
    uint64_field_ = new ::google::protobuf::UInt64Value;
  }
  // @@protoc_insertion_point(field_mutable:protobuf_unittest.TestWellKnownTypes.uint64_field)
  return uint64_field_;
}
::google::protobuf::UInt64Value* TestWellKnownTypes::release_uint64_field() {
  // @@protoc_insertion_point(field_release:protobuf_unittest.TestWellKnownTypes.uint64_field)
  
  ::google::protobuf::UInt64Value* temp = uint64_field_;
  uint64_field_ = NULL;
  return temp;
}
void TestWellKnownTypes::set_allocated_uint64_field(::google::protobuf::UInt64Value* uint64_field) {
  delete uint64_field_;
  if (uint64_field != NULL && uint64_field->GetArena() != NULL) {
    ::google::protobuf::UInt64Value* new_uint64_field = new ::google::protobuf::UInt64Value;
    new_uint64_field->CopyFrom(*uint64_field);
    uint64_field = new_uint64_field;
  }
  uint64_field_ = uint64_field;
  if (uint64_field) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_set_allocated:protobuf_unittest.TestWellKnownTypes.uint64_field)
}

// optional .google.protobuf.Int32Value int32_field = 14;
bool TestWellKnownTypes::has_int32_field() const {
  return this != internal_default_instance() && int32_field_ != NULL;
}
void TestWellKnownTypes::clear_int32_field() {
  if (GetArenaNoVirtual() == NULL && int32_field_ != NULL) delete int32_field_;
  int32_field_ = NULL;
}
const ::google::protobuf::Int32Value& TestWellKnownTypes::int32_field() const {
  // @@protoc_insertion_point(field_get:protobuf_unittest.TestWellKnownTypes.int32_field)
  return int32_field_ != NULL ? *int32_field_
                         : *::google::protobuf::Int32Value::internal_default_instance();
}
::google::protobuf::Int32Value* TestWellKnownTypes::mutable_int32_field() {
  
  if (int32_field_ == NULL) {
    int32_field_ = new ::google::protobuf::Int32Value;
  }
  // @@protoc_insertion_point(field_mutable:protobuf_unittest.TestWellKnownTypes.int32_field)
  return int32_field_;
}
::google::protobuf::Int32Value* TestWellKnownTypes::release_int32_field() {
  // @@protoc_insertion_point(field_release:protobuf_unittest.TestWellKnownTypes.int32_field)
  
  ::google::protobuf::Int32Value* temp = int32_field_;
  int32_field_ = NULL;
  return temp;
}
void TestWellKnownTypes::set_allocated_int32_field(::google::protobuf::Int32Value* int32_field) {
  delete int32_field_;
  if (int32_field != NULL && int32_field->GetArena() != NULL) {
    ::google::protobuf::Int32Value* new_int32_field = new ::google::protobuf::Int32Value;
    new_int32_field->CopyFrom(*int32_field);
    int32_field = new_int32_field;
  }
  int32_field_ = int32_field;
  if (int32_field) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_set_allocated:protobuf_unittest.TestWellKnownTypes.int32_field)
}

// optional .google.protobuf.UInt32Value uint32_field = 15;
bool TestWellKnownTypes::has_uint32_field() const {
  return this != internal_default_instance() && uint32_field_ != NULL;
}
void TestWellKnownTypes::clear_uint32_field() {
  if (GetArenaNoVirtual() == NULL && uint32_field_ != NULL) delete uint32_field_;
  uint32_field_ = NULL;
}
const ::google::protobuf::UInt32Value& TestWellKnownTypes::uint32_field() const {
  // @@protoc_insertion_point(field_get:protobuf_unittest.TestWellKnownTypes.uint32_field)
  return uint32_field_ != NULL ? *uint32_field_
                         : *::google::protobuf::UInt32Value::internal_default_instance();
}
::google::protobuf::UInt32Value* TestWellKnownTypes::mutable_uint32_field() {
  
  if (uint32_field_ == NULL) {
    uint32_field_ = new ::google::protobuf::UInt32Value;
  }
  // @@protoc_insertion_point(field_mutable:protobuf_unittest.TestWellKnownTypes.uint32_field)
  return uint32_field_;
}
::google::protobuf::UInt32Value* TestWellKnownTypes::release_uint32_field() {
  // @@protoc_insertion_point(field_release:protobuf_unittest.TestWellKnownTypes.uint32_field)
  
  ::google::protobuf::UInt32Value* temp = uint32_field_;
  uint32_field_ = NULL;
  return temp;
}
void TestWellKnownTypes::set_allocated_uint32_field(::google::protobuf::UInt32Value* uint32_field) {
  delete uint32_field_;
  if (uint32_field != NULL && uint32_field->GetArena() != NULL) {
    ::google::protobuf::UInt32Value* new_uint32_field = new ::google::protobuf::UInt32Value;
    new_uint32_field->CopyFrom(*uint32_field);
    uint32_field = new_uint32_field;
  }
  uint32_field_ = uint32_field;
  if (uint32_field) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_set_allocated:protobuf_unittest.TestWellKnownTypes.uint32_field)
}

// optional .google.protobuf.BoolValue bool_field = 16;
bool TestWellKnownTypes::has_bool_field() const {
  return this != internal_default_instance() && bool_field_ != NULL;
}
void TestWellKnownTypes::clear_bool_field() {
  if (GetArenaNoVirtual() == NULL && bool_field_ != NULL) delete bool_field_;
  bool_field_ = NULL;
}
const ::google::protobuf::BoolValue& TestWellKnownTypes::bool_field() const {
  // @@protoc_insertion_point(field_get:protobuf_unittest.TestWellKnownTypes.bool_field)
  return bool_field_ != NULL ? *bool_field_
                         : *::google::protobuf::BoolValue::internal_default_instance();
}
::google::protobuf::BoolValue* TestWellKnownTypes::mutable_bool_field() {
  
  if (bool_field_ == NULL) {
    bool_field_ = new ::google::protobuf::BoolValue;
  }
  // @@protoc_insertion_point(field_mutable:protobuf_unittest.TestWellKnownTypes.bool_field)
  return bool_field_;
}
::google::protobuf::BoolValue* TestWellKnownTypes::release_bool_field() {
  // @@protoc_insertion_point(field_release:protobuf_unittest.TestWellKnownTypes.bool_field)
  
  ::google::protobuf::BoolValue* temp = bool_field_;
  bool_field_ = NULL;
  return temp;
}
void TestWellKnownTypes::set_allocated_bool_field(::google::protobuf::BoolValue* bool_field) {
  delete bool_field_;
  if (bool_field != NULL && bool_field->GetArena() != NULL) {
    ::google::protobuf::BoolValue* new_bool_field = new ::google::protobuf::BoolValue;
    new_bool_field->CopyFrom(*bool_field);
    bool_field = new_bool_field;
  }
  bool_field_ = bool_field;
  if (bool_field) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_set_allocated:protobuf_unittest.TestWellKnownTypes.bool_field)
}

// optional .google.protobuf.StringValue string_field = 17;
bool TestWellKnownTypes::has_string_field() const {
  return this != internal_default_instance() && string_field_ != NULL;
}
void TestWellKnownTypes::clear_string_field() {
  if (GetArenaNoVirtual() == NULL && string_field_ != NULL) delete string_field_;
  string_field_ = NULL;
}
const ::google::protobuf::StringValue& TestWellKnownTypes::string_field() const {
  // @@protoc_insertion_point(field_get:protobuf_unittest.TestWellKnownTypes.string_field)
  return string_field_ != NULL ? *string_field_
                         : *::google::protobuf::StringValue::internal_default_instance();
}
::google::protobuf::StringValue* TestWellKnownTypes::mutable_string_field() {
  
  if (string_field_ == NULL) {
    string_field_ = new ::google::protobuf::StringValue;
  }
  // @@protoc_insertion_point(field_mutable:protobuf_unittest.TestWellKnownTypes.string_field)
  return string_field_;
}
::google::protobuf::StringValue* TestWellKnownTypes::release_string_field() {
  // @@protoc_insertion_point(field_release:protobuf_unittest.TestWellKnownTypes.string_field)
  
  ::google::protobuf::StringValue* temp = string_field_;
  string_field_ = NULL;
  return temp;
}
void TestWellKnownTypes::set_allocated_string_field(::google::protobuf::StringValue* string_field) {
  delete string_field_;
  if (string_field != NULL && string_field->GetArena() != NULL) {
    ::google::protobuf::StringValue* new_string_field = new ::google::protobuf::StringValue;
    new_string_field->CopyFrom(*string_field);
    string_field = new_string_field;
  }
  string_field_ = string_field;
  if (string_field) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_set_allocated:protobuf_unittest.TestWellKnownTypes.string_field)
}

// optional .google.protobuf.BytesValue bytes_field = 18;
bool TestWellKnownTypes::has_bytes_field() const {
  return this != internal_default_instance() && bytes_field_ != NULL;
}
void TestWellKnownTypes::clear_bytes_field() {
  if (GetArenaNoVirtual() == NULL && bytes_field_ != NULL) delete bytes_field_;
  bytes_field_ = NULL;
}
const ::google::protobuf::BytesValue& TestWellKnownTypes::bytes_field() const {
  // @@protoc_insertion_point(field_get:protobuf_unittest.TestWellKnownTypes.bytes_field)
  return bytes_field_ != NULL ? *bytes_field_
                         : *::google::protobuf::BytesValue::internal_default_instance();
}
::google::protobuf::BytesValue* TestWellKnownTypes::mutable_bytes_field() {
  
  if (bytes_field_ == NULL) {
    bytes_field_ = new ::google::protobuf::BytesValue;
  }
  // @@protoc_insertion_point(field_mutable:protobuf_unittest.TestWellKnownTypes.bytes_field)
  return bytes_field_;
}
::google::protobuf::BytesValue* TestWellKnownTypes::release_bytes_field() {
  // @@protoc_insertion_point(field_release:protobuf_unittest.TestWellKnownTypes.bytes_field)
  
  ::google::protobuf::BytesValue* temp = bytes_field_;
  bytes_field_ = NULL;
  return temp;
}
void TestWellKnownTypes::set_allocated_bytes_field(::google::protobuf::BytesValue* bytes_field) {
  delete bytes_field_;
  if (bytes_field != NULL && bytes_field->GetArena() != NULL) {
    ::google::protobuf::BytesValue* new_bytes_field = new ::google::protobuf::BytesValue;
    new_bytes_field->CopyFrom(*bytes_field);
    bytes_field = new_bytes_field;
  }
  bytes_field_ = bytes_field;
  if (bytes_field) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_set_allocated:protobuf_unittest.TestWellKnownTypes.bytes_field)
}

// optional .google.protobuf.Value value_field = 19;
bool TestWellKnownTypes::has_value_field() const {
  return this != internal_default_instance() && value_field_ != NULL;
}
void TestWellKnownTypes::clear_value_field() {
  if (GetArenaNoVirtual() == NULL && value_field_ != NULL) delete value_field_;
  value_field_ = NULL;
}
const ::google::protobuf::Value& TestWellKnownTypes::value_field() const {
  // @@protoc_insertion_point(field_get:protobuf_unittest.TestWellKnownTypes.value_field)
  return value_field_ != NULL ? *value_field_
                         : *::google::protobuf::Value::internal_default_instance();
}
::google::protobuf::Value* TestWellKnownTypes::mutable_value_field() {
  
  if (value_field_ == NULL) {
    value_field_ = new ::google::protobuf::Value;
  }
  // @@protoc_insertion_point(field_mutable:protobuf_unittest.TestWellKnownTypes.value_field)
  return value_field_;
}
::google::protobuf::Value* TestWellKnownTypes::release_value_field() {
  // @@protoc_insertion_point(field_release:protobuf_unittest.TestWellKnownTypes.value_field)
  
  ::google::protobuf::Value* temp = value_field_;
  value_field_ = NULL;
  return temp;
}
void TestWellKnownTypes::set_allocated_value_field(::google::protobuf::Value* value_field) {
  delete value_field_;
  if (value_field != NULL && value_field->GetArena() != NULL) {
    ::google::protobuf::Value* new_value_field = new ::google::protobuf::Value;
    new_value_field->CopyFrom(*value_field);
    value_field = new_value_field;
  }
  value_field_ = value_field;
  if (value_field) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_set_allocated:protobuf_unittest.TestWellKnownTypes.value_field)
}

inline const TestWellKnownTypes* TestWellKnownTypes::internal_default_instance() {
  return &TestWellKnownTypes_default_instance_.get();
}
#endif  // PROTOBUF_INLINE_NOT_IN_HEADERS

// ===================================================================

#if !defined(_MSC_VER) || _MSC_VER >= 1900
const int RepeatedWellKnownTypes::kAnyFieldFieldNumber;
const int RepeatedWellKnownTypes::kApiFieldFieldNumber;
const int RepeatedWellKnownTypes::kDurationFieldFieldNumber;
const int RepeatedWellKnownTypes::kEmptyFieldFieldNumber;
const int RepeatedWellKnownTypes::kFieldMaskFieldFieldNumber;
const int RepeatedWellKnownTypes::kSourceContextFieldFieldNumber;
const int RepeatedWellKnownTypes::kStructFieldFieldNumber;
const int RepeatedWellKnownTypes::kTimestampFieldFieldNumber;
const int RepeatedWellKnownTypes::kTypeFieldFieldNumber;
const int RepeatedWellKnownTypes::kDoubleFieldFieldNumber;
const int RepeatedWellKnownTypes::kFloatFieldFieldNumber;
const int RepeatedWellKnownTypes::kInt64FieldFieldNumber;
const int RepeatedWellKnownTypes::kUint64FieldFieldNumber;
const int RepeatedWellKnownTypes::kInt32FieldFieldNumber;
const int RepeatedWellKnownTypes::kUint32FieldFieldNumber;
const int RepeatedWellKnownTypes::kBoolFieldFieldNumber;
const int RepeatedWellKnownTypes::kStringFieldFieldNumber;
const int RepeatedWellKnownTypes::kBytesFieldFieldNumber;
#endif  // !defined(_MSC_VER) || _MSC_VER >= 1900

RepeatedWellKnownTypes::RepeatedWellKnownTypes()
  : ::google::protobuf::Message(), _internal_metadata_(NULL) {
  if (this != internal_default_instance()) protobuf_InitDefaults_google_2fprotobuf_2funittest_5fwell_5fknown_5ftypes_2eproto();
  SharedCtor();
  // @@protoc_insertion_point(constructor:protobuf_unittest.RepeatedWellKnownTypes)
}

void RepeatedWellKnownTypes::InitAsDefaultInstance() {
}

RepeatedWellKnownTypes::RepeatedWellKnownTypes(const RepeatedWellKnownTypes& from)
  : ::google::protobuf::Message(),
    _internal_metadata_(NULL) {
  SharedCtor();
  UnsafeMergeFrom(from);
  // @@protoc_insertion_point(copy_constructor:protobuf_unittest.RepeatedWellKnownTypes)
}

void RepeatedWellKnownTypes::SharedCtor() {
  _cached_size_ = 0;
}

RepeatedWellKnownTypes::~RepeatedWellKnownTypes() {
  // @@protoc_insertion_point(destructor:protobuf_unittest.RepeatedWellKnownTypes)
  SharedDtor();
}

void RepeatedWellKnownTypes::SharedDtor() {
}

void RepeatedWellKnownTypes::SetCachedSize(int size) const {
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
}
const ::google::protobuf::Descriptor* RepeatedWellKnownTypes::descriptor() {
  protobuf_AssignDescriptorsOnce();
  return RepeatedWellKnownTypes_descriptor_;
}

const RepeatedWellKnownTypes& RepeatedWellKnownTypes::default_instance() {
  protobuf_InitDefaults_google_2fprotobuf_2funittest_5fwell_5fknown_5ftypes_2eproto();
  return *internal_default_instance();
}

::google::protobuf::internal::ExplicitlyConstructed<RepeatedWellKnownTypes> RepeatedWellKnownTypes_default_instance_;

RepeatedWellKnownTypes* RepeatedWellKnownTypes::New(::google::protobuf::Arena* arena) const {
  RepeatedWellKnownTypes* n = new RepeatedWellKnownTypes;
  if (arena != NULL) {
    arena->Own(n);
  }
  return n;
}

void RepeatedWellKnownTypes::Clear() {
// @@protoc_insertion_point(message_clear_start:protobuf_unittest.RepeatedWellKnownTypes)
  any_field_.Clear();
  api_field_.Clear();
  duration_field_.Clear();
  empty_field_.Clear();
  field_mask_field_.Clear();
  source_context_field_.Clear();
  struct_field_.Clear();
  timestamp_field_.Clear();
  type_field_.Clear();
  double_field_.Clear();
  float_field_.Clear();
  int64_field_.Clear();
  uint64_field_.Clear();
  int32_field_.Clear();
  uint32_field_.Clear();
  bool_field_.Clear();
  string_field_.Clear();
  bytes_field_.Clear();
}

bool RepeatedWellKnownTypes::MergePartialFromCodedStream(
    ::google::protobuf::io::CodedInputStream* input) {
#define DO_(EXPRESSION) if (!GOOGLE_PREDICT_TRUE(EXPRESSION)) goto failure
  ::google::protobuf::uint32 tag;
  // @@protoc_insertion_point(parse_start:protobuf_unittest.RepeatedWellKnownTypes)
  for (;;) {
    ::std::pair< ::google::protobuf::uint32, bool> p = input->ReadTagWithCutoff(16383);
    tag = p.first;
    if (!p.second) goto handle_unusual;
    switch (::google::protobuf::internal::WireFormatLite::GetTagFieldNumber(tag)) {
      // repeated .google.protobuf.Any any_field = 1;
      case 1: {
        if (tag == 10) {
          DO_(input->IncrementRecursionDepth());
         parse_loop_any_field:
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessageNoVirtualNoRecursionDepth(
                input, add_any_field()));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(10)) goto parse_loop_any_field;
        if (input->ExpectTag(18)) goto parse_loop_api_field;
        input->UnsafeDecrementRecursionDepth();
        break;
      }

      // repeated .google.protobuf.Api api_field = 2;
      case 2: {
        if (tag == 18) {
          DO_(input->IncrementRecursionDepth());
         parse_loop_api_field:
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessageNoVirtualNoRecursionDepth(
                input, add_api_field()));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(18)) goto parse_loop_api_field;
        if (input->ExpectTag(26)) goto parse_loop_duration_field;
        input->UnsafeDecrementRecursionDepth();
        break;
      }

      // repeated .google.protobuf.Duration duration_field = 3;
      case 3: {
        if (tag == 26) {
          DO_(input->IncrementRecursionDepth());
         parse_loop_duration_field:
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessageNoVirtualNoRecursionDepth(
                input, add_duration_field()));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(26)) goto parse_loop_duration_field;
        if (input->ExpectTag(34)) goto parse_loop_empty_field;
        input->UnsafeDecrementRecursionDepth();
        break;
      }

      // repeated .google.protobuf.Empty empty_field = 4;
      case 4: {
        if (tag == 34) {
          DO_(input->IncrementRecursionDepth());
         parse_loop_empty_field:
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessageNoVirtualNoRecursionDepth(
                input, add_empty_field()));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(34)) goto parse_loop_empty_field;
        if (input->ExpectTag(42)) goto parse_loop_field_mask_field;
        input->UnsafeDecrementRecursionDepth();
        break;
      }

      // repeated .google.protobuf.FieldMask field_mask_field = 5;
      case 5: {
        if (tag == 42) {
          DO_(input->IncrementRecursionDepth());
         parse_loop_field_mask_field:
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessageNoVirtualNoRecursionDepth(
                input, add_field_mask_field()));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(42)) goto parse_loop_field_mask_field;
        if (input->ExpectTag(50)) goto parse_loop_source_context_field;
        input->UnsafeDecrementRecursionDepth();
        break;
      }

      // repeated .google.protobuf.SourceContext source_context_field = 6;
      case 6: {
        if (tag == 50) {
          DO_(input->IncrementRecursionDepth());
         parse_loop_source_context_field:
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessageNoVirtualNoRecursionDepth(
                input, add_source_context_field()));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(50)) goto parse_loop_source_context_field;
        if (input->ExpectTag(58)) goto parse_loop_struct_field;
        input->UnsafeDecrementRecursionDepth();
        break;
      }

      // repeated .google.protobuf.Struct struct_field = 7;
      case 7: {
        if (tag == 58) {
          DO_(input->IncrementRecursionDepth());
         parse_loop_struct_field:
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessageNoVirtualNoRecursionDepth(
                input, add_struct_field()));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(58)) goto parse_loop_struct_field;
        if (input->ExpectTag(66)) goto parse_loop_timestamp_field;
        input->UnsafeDecrementRecursionDepth();
        break;
      }

      // repeated .google.protobuf.Timestamp timestamp_field = 8;
      case 8: {
        if (tag == 66) {
          DO_(input->IncrementRecursionDepth());
         parse_loop_timestamp_field:
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessageNoVirtualNoRecursionDepth(
                input, add_timestamp_field()));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(66)) goto parse_loop_timestamp_field;
        if (input->ExpectTag(74)) goto parse_loop_type_field;
        input->UnsafeDecrementRecursionDepth();
        break;
      }

      // repeated .google.protobuf.Type type_field = 9;
      case 9: {
        if (tag == 74) {
          DO_(input->IncrementRecursionDepth());
         parse_loop_type_field:
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessageNoVirtualNoRecursionDepth(
                input, add_type_field()));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(74)) goto parse_loop_type_field;
        if (input->ExpectTag(82)) goto parse_loop_double_field;
        input->UnsafeDecrementRecursionDepth();
        break;
      }

      // repeated .google.protobuf.DoubleValue double_field = 10;
      case 10: {
        if (tag == 82) {
          DO_(input->IncrementRecursionDepth());
         parse_loop_double_field:
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessageNoVirtualNoRecursionDepth(
                input, add_double_field()));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(82)) goto parse_loop_double_field;
        if (input->ExpectTag(90)) goto parse_loop_float_field;
        input->UnsafeDecrementRecursionDepth();
        break;
      }

      // repeated .google.protobuf.FloatValue float_field = 11;
      case 11: {
        if (tag == 90) {
          DO_(input->IncrementRecursionDepth());
         parse_loop_float_field:
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessageNoVirtualNoRecursionDepth(
                input, add_float_field()));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(90)) goto parse_loop_float_field;
        if (input->ExpectTag(98)) goto parse_loop_int64_field;
        input->UnsafeDecrementRecursionDepth();
        break;
      }

      // repeated .google.protobuf.Int64Value int64_field = 12;
      case 12: {
        if (tag == 98) {
          DO_(input->IncrementRecursionDepth());
         parse_loop_int64_field:
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessageNoVirtualNoRecursionDepth(
                input, add_int64_field()));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(98)) goto parse_loop_int64_field;
        if (input->ExpectTag(106)) goto parse_loop_uint64_field;
        input->UnsafeDecrementRecursionDepth();
        break;
      }

      // repeated .google.protobuf.UInt64Value uint64_field = 13;
      case 13: {
        if (tag == 106) {
          DO_(input->IncrementRecursionDepth());
         parse_loop_uint64_field:
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessageNoVirtualNoRecursionDepth(
                input, add_uint64_field()));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(106)) goto parse_loop_uint64_field;
        if (input->ExpectTag(114)) goto parse_loop_int32_field;
        input->UnsafeDecrementRecursionDepth();
        break;
      }

      // repeated .google.protobuf.Int32Value int32_field = 14;
      case 14: {
        if (tag == 114) {
          DO_(input->IncrementRecursionDepth());
         parse_loop_int32_field:
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessageNoVirtualNoRecursionDepth(
                input, add_int32_field()));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(114)) goto parse_loop_int32_field;
        if (input->ExpectTag(122)) goto parse_loop_uint32_field;
        input->UnsafeDecrementRecursionDepth();
        break;
      }

      // repeated .google.protobuf.UInt32Value uint32_field = 15;
      case 15: {
        if (tag == 122) {
          DO_(input->IncrementRecursionDepth());
         parse_loop_uint32_field:
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessageNoVirtualNoRecursionDepth(
                input, add_uint32_field()));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(122)) goto parse_loop_uint32_field;
        if (input->ExpectTag(130)) goto parse_loop_bool_field;
        input->UnsafeDecrementRecursionDepth();
        break;
      }

      // repeated .google.protobuf.BoolValue bool_field = 16;
      case 16: {
        if (tag == 130) {
          DO_(input->IncrementRecursionDepth());
         parse_loop_bool_field:
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessageNoVirtualNoRecursionDepth(
                input, add_bool_field()));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(130)) goto parse_loop_bool_field;
        if (input->ExpectTag(138)) goto parse_loop_string_field;
        input->UnsafeDecrementRecursionDepth();
        break;
      }

      // repeated .google.protobuf.StringValue string_field = 17;
      case 17: {
        if (tag == 138) {
          DO_(input->IncrementRecursionDepth());
         parse_loop_string_field:
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessageNoVirtualNoRecursionDepth(
                input, add_string_field()));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(138)) goto parse_loop_string_field;
        if (input->ExpectTag(146)) goto parse_loop_bytes_field;
        input->UnsafeDecrementRecursionDepth();
        break;
      }

      // repeated .google.protobuf.BytesValue bytes_field = 18;
      case 18: {
        if (tag == 146) {
          DO_(input->IncrementRecursionDepth());
         parse_loop_bytes_field:
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessageNoVirtualNoRecursionDepth(
                input, add_bytes_field()));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(146)) goto parse_loop_bytes_field;
        input->UnsafeDecrementRecursionDepth();
        if (input->ExpectAtEnd()) goto success;
        break;
      }

      default: {
      handle_unusual:
        if (tag == 0 ||
            ::google::protobuf::internal::WireFormatLite::GetTagWireType(tag) ==
            ::google::protobuf::internal::WireFormatLite::WIRETYPE_END_GROUP) {
          goto success;
        }
        DO_(::google::protobuf::internal::WireFormatLite::SkipField(input, tag));
        break;
      }
    }
  }
success:
  // @@protoc_insertion_point(parse_success:protobuf_unittest.RepeatedWellKnownTypes)
  return true;
failure:
  // @@protoc_insertion_point(parse_failure:protobuf_unittest.RepeatedWellKnownTypes)
  return false;
#undef DO_
}

void RepeatedWellKnownTypes::SerializeWithCachedSizes(
    ::google::protobuf::io::CodedOutputStream* output) const {
  // @@protoc_insertion_point(serialize_start:protobuf_unittest.RepeatedWellKnownTypes)
  // repeated .google.protobuf.Any any_field = 1;
  for (unsigned int i = 0, n = this->any_field_size(); i < n; i++) {
    ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
      1, this->any_field(i), output);
  }

  // repeated .google.protobuf.Api api_field = 2;
  for (unsigned int i = 0, n = this->api_field_size(); i < n; i++) {
    ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
      2, this->api_field(i), output);
  }

  // repeated .google.protobuf.Duration duration_field = 3;
  for (unsigned int i = 0, n = this->duration_field_size(); i < n; i++) {
    ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
      3, this->duration_field(i), output);
  }

  // repeated .google.protobuf.Empty empty_field = 4;
  for (unsigned int i = 0, n = this->empty_field_size(); i < n; i++) {
    ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
      4, this->empty_field(i), output);
  }

  // repeated .google.protobuf.FieldMask field_mask_field = 5;
  for (unsigned int i = 0, n = this->field_mask_field_size(); i < n; i++) {
    ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
      5, this->field_mask_field(i), output);
  }

  // repeated .google.protobuf.SourceContext source_context_field = 6;
  for (unsigned int i = 0, n = this->source_context_field_size(); i < n; i++) {
    ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
      6, this->source_context_field(i), output);
  }

  // repeated .google.protobuf.Struct struct_field = 7;
  for (unsigned int i = 0, n = this->struct_field_size(); i < n; i++) {
    ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
      7, this->struct_field(i), output);
  }

  // repeated .google.protobuf.Timestamp timestamp_field = 8;
  for (unsigned int i = 0, n = this->timestamp_field_size(); i < n; i++) {
    ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
      8, this->timestamp_field(i), output);
  }

  // repeated .google.protobuf.Type type_field = 9;
  for (unsigned int i = 0, n = this->type_field_size(); i < n; i++) {
    ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
      9, this->type_field(i), output);
  }

  // repeated .google.protobuf.DoubleValue double_field = 10;
  for (unsigned int i = 0, n = this->double_field_size(); i < n; i++) {
    ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
      10, this->double_field(i), output);
  }

  // repeated .google.protobuf.FloatValue float_field = 11;
  for (unsigned int i = 0, n = this->float_field_size(); i < n; i++) {
    ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
      11, this->float_field(i), output);
  }

  // repeated .google.protobuf.Int64Value int64_field = 12;
  for (unsigned int i = 0, n = this->int64_field_size(); i < n; i++) {
    ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
      12, this->int64_field(i), output);
  }

  // repeated .google.protobuf.UInt64Value uint64_field = 13;
  for (unsigned int i = 0, n = this->uint64_field_size(); i < n; i++) {
    ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
      13, this->uint64_field(i), output);
  }

  // repeated .google.protobuf.Int32Value int32_field = 14;
  for (unsigned int i = 0, n = this->int32_field_size(); i < n; i++) {
    ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
      14, this->int32_field(i), output);
  }

  // repeated .google.protobuf.UInt32Value uint32_field = 15;
  for (unsigned int i = 0, n = this->uint32_field_size(); i < n; i++) {
    ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
      15, this->uint32_field(i), output);
  }

  // repeated .google.protobuf.BoolValue bool_field = 16;
  for (unsigned int i = 0, n = this->bool_field_size(); i < n; i++) {
    ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
      16, this->bool_field(i), output);
  }

  // repeated .google.protobuf.StringValue string_field = 17;
  for (unsigned int i = 0, n = this->string_field_size(); i < n; i++) {
    ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
      17, this->string_field(i), output);
  }

  // repeated .google.protobuf.BytesValue bytes_field = 18;
  for (unsigned int i = 0, n = this->bytes_field_size(); i < n; i++) {
    ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
      18, this->bytes_field(i), output);
  }

  // @@protoc_insertion_point(serialize_end:protobuf_unittest.RepeatedWellKnownTypes)
}

::google::protobuf::uint8* RepeatedWellKnownTypes::InternalSerializeWithCachedSizesToArray(
    bool deterministic, ::google::protobuf::uint8* target) const {
  (void)deterministic; // Unused
  // @@protoc_insertion_point(serialize_to_array_start:protobuf_unittest.RepeatedWellKnownTypes)
  // repeated .google.protobuf.Any any_field = 1;
  for (unsigned int i = 0, n = this->any_field_size(); i < n; i++) {
    target = ::google::protobuf::internal::WireFormatLite::
      InternalWriteMessageNoVirtualToArray(
        1, this->any_field(i), false, target);
  }

  // repeated .google.protobuf.Api api_field = 2;
  for (unsigned int i = 0, n = this->api_field_size(); i < n; i++) {
    target = ::google::protobuf::internal::WireFormatLite::
      InternalWriteMessageNoVirtualToArray(
        2, this->api_field(i), false, target);
  }

  // repeated .google.protobuf.Duration duration_field = 3;
  for (unsigned int i = 0, n = this->duration_field_size(); i < n; i++) {
    target = ::google::protobuf::internal::WireFormatLite::
      InternalWriteMessageNoVirtualToArray(
        3, this->duration_field(i), false, target);
  }

  // repeated .google.protobuf.Empty empty_field = 4;
  for (unsigned int i = 0, n = this->empty_field_size(); i < n; i++) {
    target = ::google::protobuf::internal::WireFormatLite::
      InternalWriteMessageNoVirtualToArray(
        4, this->empty_field(i), false, target);
  }

  // repeated .google.protobuf.FieldMask field_mask_field = 5;
  for (unsigned int i = 0, n = this->field_mask_field_size(); i < n; i++) {
    target = ::google::protobuf::internal::WireFormatLite::
      InternalWriteMessageNoVirtualToArray(
        5, this->field_mask_field(i), false, target);
  }

  // repeated .google.protobuf.SourceContext source_context_field = 6;
  for (unsigned int i = 0, n = this->source_context_field_size(); i < n; i++) {
    target = ::google::protobuf::internal::WireFormatLite::
      InternalWriteMessageNoVirtualToArray(
        6, this->source_context_field(i), false, target);
  }

  // repeated .google.protobuf.Struct struct_field = 7;
  for (unsigned int i = 0, n = this->struct_field_size(); i < n; i++) {
    target = ::google::protobuf::internal::WireFormatLite::
      InternalWriteMessageNoVirtualToArray(
        7, this->struct_field(i), false, target);
  }

  // repeated .google.protobuf.Timestamp timestamp_field = 8;
  for (unsigned int i = 0, n = this->timestamp_field_size(); i < n; i++) {
    target = ::google::protobuf::internal::WireFormatLite::
      InternalWriteMessageNoVirtualToArray(
        8, this->timestamp_field(i), false, target);
  }

  // repeated .google.protobuf.Type type_field = 9;
  for (unsigned int i = 0, n = this->type_field_size(); i < n; i++) {
    target = ::google::protobuf::internal::WireFormatLite::
      InternalWriteMessageNoVirtualToArray(
        9, this->type_field(i), false, target);
  }

  // repeated .google.protobuf.DoubleValue double_field = 10;
  for (unsigned int i = 0, n = this->double_field_size(); i < n; i++) {
    target = ::google::protobuf::internal::WireFormatLite::
      InternalWriteMessageNoVirtualToArray(
        10, this->double_field(i), false, target);
  }

  // repeated .google.protobuf.FloatValue float_field = 11;
  for (unsigned int i = 0, n = this->float_field_size(); i < n; i++) {
    target = ::google::protobuf::internal::WireFormatLite::
      InternalWriteMessageNoVirtualToArray(
        11, this->float_field(i), false, target);
  }

  // repeated .google.protobuf.Int64Value int64_field = 12;
  for (unsigned int i = 0, n = this->int64_field_size(); i < n; i++) {
    target = ::google::protobuf::internal::WireFormatLite::
      InternalWriteMessageNoVirtualToArray(
        12, this->int64_field(i), false, target);
  }

  // repeated .google.protobuf.UInt64Value uint64_field = 13;
  for (unsigned int i = 0, n = this->uint64_field_size(); i < n; i++) {
    target = ::google::protobuf::internal::WireFormatLite::
      InternalWriteMessageNoVirtualToArray(
        13, this->uint64_field(i), false, target);
  }

  // repeated .google.protobuf.Int32Value int32_field = 14;
  for (unsigned int i = 0, n = this->int32_field_size(); i < n; i++) {
    target = ::google::protobuf::internal::WireFormatLite::
      InternalWriteMessageNoVirtualToArray(
        14, this->int32_field(i), false, target);
  }

  // repeated .google.protobuf.UInt32Value uint32_field = 15;
  for (unsigned int i = 0, n = this->uint32_field_size(); i < n; i++) {
    target = ::google::protobuf::internal::WireFormatLite::
      InternalWriteMessageNoVirtualToArray(
        15, this->uint32_field(i), false, target);
  }

  // repeated .google.protobuf.BoolValue bool_field = 16;
  for (unsigned int i = 0, n = this->bool_field_size(); i < n; i++) {
    target = ::google::protobuf::internal::WireFormatLite::
      InternalWriteMessageNoVirtualToArray(
        16, this->bool_field(i), false, target);
  }

  // repeated .google.protobuf.StringValue string_field = 17;
  for (unsigned int i = 0, n = this->string_field_size(); i < n; i++) {
    target = ::google::protobuf::internal::WireFormatLite::
      InternalWriteMessageNoVirtualToArray(
        17, this->string_field(i), false, target);
  }

  // repeated .google.protobuf.BytesValue bytes_field = 18;
  for (unsigned int i = 0, n = this->bytes_field_size(); i < n; i++) {
    target = ::google::protobuf::internal::WireFormatLite::
      InternalWriteMessageNoVirtualToArray(
        18, this->bytes_field(i), false, target);
  }

  // @@protoc_insertion_point(serialize_to_array_end:protobuf_unittest.RepeatedWellKnownTypes)
  return target;
}

size_t RepeatedWellKnownTypes::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:protobuf_unittest.RepeatedWellKnownTypes)
  size_t total_size = 0;

  // repeated .google.protobuf.Any any_field = 1;
  {
    unsigned int count = this->any_field_size();
    total_size += 1UL * count;
    for (unsigned int i = 0; i < count; i++) {
      total_size +=
        ::google::protobuf::internal::WireFormatLite::MessageSizeNoVirtual(
          this->any_field(i));
    }
  }

  // repeated .google.protobuf.Api api_field = 2;
  {
    unsigned int count = this->api_field_size();
    total_size += 1UL * count;
    for (unsigned int i = 0; i < count; i++) {
      total_size +=
        ::google::protobuf::internal::WireFormatLite::MessageSizeNoVirtual(
          this->api_field(i));
    }
  }

  // repeated .google.protobuf.Duration duration_field = 3;
  {
    unsigned int count = this->duration_field_size();
    total_size += 1UL * count;
    for (unsigned int i = 0; i < count; i++) {
      total_size +=
        ::google::protobuf::internal::WireFormatLite::MessageSizeNoVirtual(
          this->duration_field(i));
    }
  }

  // repeated .google.protobuf.Empty empty_field = 4;
  {
    unsigned int count = this->empty_field_size();
    total_size += 1UL * count;
    for (unsigned int i = 0; i < count; i++) {
      total_size +=
        ::google::protobuf::internal::WireFormatLite::MessageSizeNoVirtual(
          this->empty_field(i));
    }
  }

  // repeated .google.protobuf.FieldMask field_mask_field = 5;
  {
    unsigned int count = this->field_mask_field_size();
    total_size += 1UL * count;
    for (unsigned int i = 0; i < count; i++) {
      total_size +=
        ::google::protobuf::internal::WireFormatLite::MessageSizeNoVirtual(
          this->field_mask_field(i));
    }
  }

  // repeated .google.protobuf.SourceContext source_context_field = 6;
  {
    unsigned int count = this->source_context_field_size();
    total_size += 1UL * count;
    for (unsigned int i = 0; i < count; i++) {
      total_size +=
        ::google::protobuf::internal::WireFormatLite::MessageSizeNoVirtual(
          this->source_context_field(i));
    }
  }

  // repeated .google.protobuf.Struct struct_field = 7;
  {
    unsigned int count = this->struct_field_size();
    total_size += 1UL * count;
    for (unsigned int i = 0; i < count; i++) {
      total_size +=
        ::google::protobuf::internal::WireFormatLite::MessageSizeNoVirtual(
          this->struct_field(i));
    }
  }

  // repeated .google.protobuf.Timestamp timestamp_field = 8;
  {
    unsigned int count = this->timestamp_field_size();
    total_size += 1UL * count;
    for (unsigned int i = 0; i < count; i++) {
      total_size +=
        ::google::protobuf::internal::WireFormatLite::MessageSizeNoVirtual(
          this->timestamp_field(i));
    }
  }

  // repeated .google.protobuf.Type type_field = 9;
  {
    unsigned int count = this->type_field_size();
    total_size += 1UL * count;
    for (unsigned int i = 0; i < count; i++) {
      total_size +=
        ::google::protobuf::internal::WireFormatLite::MessageSizeNoVirtual(
          this->type_field(i));
    }
  }

  // repeated .google.protobuf.DoubleValue double_field = 10;
  {
    unsigned int count = this->double_field_size();
    total_size += 1UL * count;
    for (unsigned int i = 0; i < count; i++) {
      total_size +=
        ::google::protobuf::internal::WireFormatLite::MessageSizeNoVirtual(
          this->double_field(i));
    }
  }

  // repeated .google.protobuf.FloatValue float_field = 11;
  {
    unsigned int count = this->float_field_size();
    total_size += 1UL * count;
    for (unsigned int i = 0; i < count; i++) {
      total_size +=
        ::google::protobuf::internal::WireFormatLite::MessageSizeNoVirtual(
          this->float_field(i));
    }
  }

  // repeated .google.protobuf.Int64Value int64_field = 12;
  {
    unsigned int count = this->int64_field_size();
    total_size += 1UL * count;
    for (unsigned int i = 0; i < count; i++) {
      total_size +=
        ::google::protobuf::internal::WireFormatLite::MessageSizeNoVirtual(
          this->int64_field(i));
    }
  }

  // repeated .google.protobuf.UInt64Value uint64_field = 13;
  {
    unsigned int count = this->uint64_field_size();
    total_size += 1UL * count;
    for (unsigned int i = 0; i < count; i++) {
      total_size +=
        ::google::protobuf::internal::WireFormatLite::MessageSizeNoVirtual(
          this->uint64_field(i));
    }
  }

  // repeated .google.protobuf.Int32Value int32_field = 14;
  {
    unsigned int count = this->int32_field_size();
    total_size += 1UL * count;
    for (unsigned int i = 0; i < count; i++) {
      total_size +=
        ::google::protobuf::internal::WireFormatLite::MessageSizeNoVirtual(
          this->int32_field(i));
    }
  }

  // repeated .google.protobuf.UInt32Value uint32_field = 15;
  {
    unsigned int count = this->uint32_field_size();
    total_size += 1UL * count;
    for (unsigned int i = 0; i < count; i++) {
      total_size +=
        ::google::protobuf::internal::WireFormatLite::MessageSizeNoVirtual(
          this->uint32_field(i));
    }
  }

  // repeated .google.protobuf.BoolValue bool_field = 16;
  {
    unsigned int count = this->bool_field_size();
    total_size += 2UL * count;
    for (unsigned int i = 0; i < count; i++) {
      total_size +=
        ::google::protobuf::internal::WireFormatLite::MessageSizeNoVirtual(
          this->bool_field(i));
    }
  }

  // repeated .google.protobuf.StringValue string_field = 17;
  {
    unsigned int count = this->string_field_size();
    total_size += 2UL * count;
    for (unsigned int i = 0; i < count; i++) {
      total_size +=
        ::google::protobuf::internal::WireFormatLite::MessageSizeNoVirtual(
          this->string_field(i));
    }
  }

  // repeated .google.protobuf.BytesValue bytes_field = 18;
  {
    unsigned int count = this->bytes_field_size();
    total_size += 2UL * count;
    for (unsigned int i = 0; i < count; i++) {
      total_size +=
        ::google::protobuf::internal::WireFormatLite::MessageSizeNoVirtual(
          this->bytes_field(i));
    }
  }

  int cached_size = ::google::protobuf::internal::ToCachedSize(total_size);
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = cached_size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
  return total_size;
}

void RepeatedWellKnownTypes::MergeFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_merge_from_start:protobuf_unittest.RepeatedWellKnownTypes)
  if (GOOGLE_PREDICT_FALSE(&from == this)) MergeFromFail(__LINE__);
  const RepeatedWellKnownTypes* source =
      ::google::protobuf::internal::DynamicCastToGenerated<const RepeatedWellKnownTypes>(
          &from);
  if (source == NULL) {
  // @@protoc_insertion_point(generalized_merge_from_cast_fail:protobuf_unittest.RepeatedWellKnownTypes)
    ::google::protobuf::internal::ReflectionOps::Merge(from, this);
  } else {
  // @@protoc_insertion_point(generalized_merge_from_cast_success:protobuf_unittest.RepeatedWellKnownTypes)
    UnsafeMergeFrom(*source);
  }
}

void RepeatedWellKnownTypes::MergeFrom(const RepeatedWellKnownTypes& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:protobuf_unittest.RepeatedWellKnownTypes)
  if (GOOGLE_PREDICT_TRUE(&from != this)) {
    UnsafeMergeFrom(from);
  } else {
    MergeFromFail(__LINE__);
  }
}

void RepeatedWellKnownTypes::UnsafeMergeFrom(const RepeatedWellKnownTypes& from) {
  GOOGLE_DCHECK(&from != this);
  any_field_.MergeFrom(from.any_field_);
  api_field_.MergeFrom(from.api_field_);
  duration_field_.MergeFrom(from.duration_field_);
  empty_field_.MergeFrom(from.empty_field_);
  field_mask_field_.MergeFrom(from.field_mask_field_);
  source_context_field_.MergeFrom(from.source_context_field_);
  struct_field_.MergeFrom(from.struct_field_);
  timestamp_field_.MergeFrom(from.timestamp_field_);
  type_field_.MergeFrom(from.type_field_);
  double_field_.MergeFrom(from.double_field_);
  float_field_.MergeFrom(from.float_field_);
  int64_field_.MergeFrom(from.int64_field_);
  uint64_field_.MergeFrom(from.uint64_field_);
  int32_field_.MergeFrom(from.int32_field_);
  uint32_field_.MergeFrom(from.uint32_field_);
  bool_field_.MergeFrom(from.bool_field_);
  string_field_.MergeFrom(from.string_field_);
  bytes_field_.MergeFrom(from.bytes_field_);
}

void RepeatedWellKnownTypes::CopyFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_copy_from_start:protobuf_unittest.RepeatedWellKnownTypes)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

void RepeatedWellKnownTypes::CopyFrom(const RepeatedWellKnownTypes& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:protobuf_unittest.RepeatedWellKnownTypes)
  if (&from == this) return;
  Clear();
  UnsafeMergeFrom(from);
}

bool RepeatedWellKnownTypes::IsInitialized() const {

  return true;
}

void RepeatedWellKnownTypes::Swap(RepeatedWellKnownTypes* other) {
  if (other == this) return;
  InternalSwap(other);
}
void RepeatedWellKnownTypes::InternalSwap(RepeatedWellKnownTypes* other) {
  any_field_.UnsafeArenaSwap(&other->any_field_);
  api_field_.UnsafeArenaSwap(&other->api_field_);
  duration_field_.UnsafeArenaSwap(&other->duration_field_);
  empty_field_.UnsafeArenaSwap(&other->empty_field_);
  field_mask_field_.UnsafeArenaSwap(&other->field_mask_field_);
  source_context_field_.UnsafeArenaSwap(&other->source_context_field_);
  struct_field_.UnsafeArenaSwap(&other->struct_field_);
  timestamp_field_.UnsafeArenaSwap(&other->timestamp_field_);
  type_field_.UnsafeArenaSwap(&other->type_field_);
  double_field_.UnsafeArenaSwap(&other->double_field_);
  float_field_.UnsafeArenaSwap(&other->float_field_);
  int64_field_.UnsafeArenaSwap(&other->int64_field_);
  uint64_field_.UnsafeArenaSwap(&other->uint64_field_);
  int32_field_.UnsafeArenaSwap(&other->int32_field_);
  uint32_field_.UnsafeArenaSwap(&other->uint32_field_);
  bool_field_.UnsafeArenaSwap(&other->bool_field_);
  string_field_.UnsafeArenaSwap(&other->string_field_);
  bytes_field_.UnsafeArenaSwap(&other->bytes_field_);
  _internal_metadata_.Swap(&other->_internal_metadata_);
  std::swap(_cached_size_, other->_cached_size_);
}

::google::protobuf::Metadata RepeatedWellKnownTypes::GetMetadata() const {
  protobuf_AssignDescriptorsOnce();
  ::google::protobuf::Metadata metadata;
  metadata.descriptor = RepeatedWellKnownTypes_descriptor_;
  metadata.reflection = RepeatedWellKnownTypes_reflection_;
  return metadata;
}

#if PROTOBUF_INLINE_NOT_IN_HEADERS
// RepeatedWellKnownTypes

// repeated .google.protobuf.Any any_field = 1;
int RepeatedWellKnownTypes::any_field_size() const {
  return any_field_.size();
}
void RepeatedWellKnownTypes::clear_any_field() {
  any_field_.Clear();
}
const ::google::protobuf::Any& RepeatedWellKnownTypes::any_field(int index) const {
  // @@protoc_insertion_point(field_get:protobuf_unittest.RepeatedWellKnownTypes.any_field)
  return any_field_.Get(index);
}
::google::protobuf::Any* RepeatedWellKnownTypes::mutable_any_field(int index) {
  // @@protoc_insertion_point(field_mutable:protobuf_unittest.RepeatedWellKnownTypes.any_field)
  return any_field_.Mutable(index);
}
::google::protobuf::Any* RepeatedWellKnownTypes::add_any_field() {
  // @@protoc_insertion_point(field_add:protobuf_unittest.RepeatedWellKnownTypes.any_field)
  return any_field_.Add();
}
::google::protobuf::RepeatedPtrField< ::google::protobuf::Any >*
RepeatedWellKnownTypes::mutable_any_field() {
  // @@protoc_insertion_point(field_mutable_list:protobuf_unittest.RepeatedWellKnownTypes.any_field)
  return &any_field_;
}
const ::google::protobuf::RepeatedPtrField< ::google::protobuf::Any >&
RepeatedWellKnownTypes::any_field() const {
  // @@protoc_insertion_point(field_list:protobuf_unittest.RepeatedWellKnownTypes.any_field)
  return any_field_;
}

// repeated .google.protobuf.Api api_field = 2;
int RepeatedWellKnownTypes::api_field_size() const {
  return api_field_.size();
}
void RepeatedWellKnownTypes::clear_api_field() {
  api_field_.Clear();
}
const ::google::protobuf::Api& RepeatedWellKnownTypes::api_field(int index) const {
  // @@protoc_insertion_point(field_get:protobuf_unittest.RepeatedWellKnownTypes.api_field)
  return api_field_.Get(index);
}
::google::protobuf::Api* RepeatedWellKnownTypes::mutable_api_field(int index) {
  // @@protoc_insertion_point(field_mutable:protobuf_unittest.RepeatedWellKnownTypes.api_field)
  return api_field_.Mutable(index);
}
::google::protobuf::Api* RepeatedWellKnownTypes::add_api_field() {
  // @@protoc_insertion_point(field_add:protobuf_unittest.RepeatedWellKnownTypes.api_field)
  return api_field_.Add();
}
::google::protobuf::RepeatedPtrField< ::google::protobuf::Api >*
RepeatedWellKnownTypes::mutable_api_field() {
  // @@protoc_insertion_point(field_mutable_list:protobuf_unittest.RepeatedWellKnownTypes.api_field)
  return &api_field_;
}
const ::google::protobuf::RepeatedPtrField< ::google::protobuf::Api >&
RepeatedWellKnownTypes::api_field() const {
  // @@protoc_insertion_point(field_list:protobuf_unittest.RepeatedWellKnownTypes.api_field)
  return api_field_;
}

// repeated .google.protobuf.Duration duration_field = 3;
int RepeatedWellKnownTypes::duration_field_size() const {
  return duration_field_.size();
}
void RepeatedWellKnownTypes::clear_duration_field() {
  duration_field_.Clear();
}
const ::google::protobuf::Duration& RepeatedWellKnownTypes::duration_field(int index) const {
  // @@protoc_insertion_point(field_get:protobuf_unittest.RepeatedWellKnownTypes.duration_field)
  return duration_field_.Get(index);
}
::google::protobuf::Duration* RepeatedWellKnownTypes::mutable_duration_field(int index) {
  // @@protoc_insertion_point(field_mutable:protobuf_unittest.RepeatedWellKnownTypes.duration_field)
  return duration_field_.Mutable(index);
}
::google::protobuf::Duration* RepeatedWellKnownTypes::add_duration_field() {
  // @@protoc_insertion_point(field_add:protobuf_unittest.RepeatedWellKnownTypes.duration_field)
  return duration_field_.Add();
}
::google::protobuf::RepeatedPtrField< ::google::protobuf::Duration >*
RepeatedWellKnownTypes::mutable_duration_field() {
  // @@protoc_insertion_point(field_mutable_list:protobuf_unittest.RepeatedWellKnownTypes.duration_field)
  return &duration_field_;
}
const ::google::protobuf::RepeatedPtrField< ::google::protobuf::Duration >&
RepeatedWellKnownTypes::duration_field() const {
  // @@protoc_insertion_point(field_list:protobuf_unittest.RepeatedWellKnownTypes.duration_field)
  return duration_field_;
}

// repeated .google.protobuf.Empty empty_field = 4;
int RepeatedWellKnownTypes::empty_field_size() const {
  return empty_field_.size();
}
void RepeatedWellKnownTypes::clear_empty_field() {
  empty_field_.Clear();
}
const ::google::protobuf::Empty& RepeatedWellKnownTypes::empty_field(int index) const {
  // @@protoc_insertion_point(field_get:protobuf_unittest.RepeatedWellKnownTypes.empty_field)
  return empty_field_.Get(index);
}
::google::protobuf::Empty* RepeatedWellKnownTypes::mutable_empty_field(int index) {
  // @@protoc_insertion_point(field_mutable:protobuf_unittest.RepeatedWellKnownTypes.empty_field)
  return empty_field_.Mutable(index);
}
::google::protobuf::Empty* RepeatedWellKnownTypes::add_empty_field() {
  // @@protoc_insertion_point(field_add:protobuf_unittest.RepeatedWellKnownTypes.empty_field)
  return empty_field_.Add();
}
::google::protobuf::RepeatedPtrField< ::google::protobuf::Empty >*
RepeatedWellKnownTypes::mutable_empty_field() {
  // @@protoc_insertion_point(field_mutable_list:protobuf_unittest.RepeatedWellKnownTypes.empty_field)
  return &empty_field_;
}
const ::google::protobuf::RepeatedPtrField< ::google::protobuf::Empty >&
RepeatedWellKnownTypes::empty_field() const {
  // @@protoc_insertion_point(field_list:protobuf_unittest.RepeatedWellKnownTypes.empty_field)
  return empty_field_;
}

// repeated .google.protobuf.FieldMask field_mask_field = 5;
int RepeatedWellKnownTypes::field_mask_field_size() const {
  return field_mask_field_.size();
}
void RepeatedWellKnownTypes::clear_field_mask_field() {
  field_mask_field_.Clear();
}
const ::google::protobuf::FieldMask& RepeatedWellKnownTypes::field_mask_field(int index) const {
  // @@protoc_insertion_point(field_get:protobuf_unittest.RepeatedWellKnownTypes.field_mask_field)
  return field_mask_field_.Get(index);
}
::google::protobuf::FieldMask* RepeatedWellKnownTypes::mutable_field_mask_field(int index) {
  // @@protoc_insertion_point(field_mutable:protobuf_unittest.RepeatedWellKnownTypes.field_mask_field)
  return field_mask_field_.Mutable(index);
}
::google::protobuf::FieldMask* RepeatedWellKnownTypes::add_field_mask_field() {
  // @@protoc_insertion_point(field_add:protobuf_unittest.RepeatedWellKnownTypes.field_mask_field)
  return field_mask_field_.Add();
}
::google::protobuf::RepeatedPtrField< ::google::protobuf::FieldMask >*
RepeatedWellKnownTypes::mutable_field_mask_field() {
  // @@protoc_insertion_point(field_mutable_list:protobuf_unittest.RepeatedWellKnownTypes.field_mask_field)
  return &field_mask_field_;
}
const ::google::protobuf::RepeatedPtrField< ::google::protobuf::FieldMask >&
RepeatedWellKnownTypes::field_mask_field() const {
  // @@protoc_insertion_point(field_list:protobuf_unittest.RepeatedWellKnownTypes.field_mask_field)
  return field_mask_field_;
}

// repeated .google.protobuf.SourceContext source_context_field = 6;
int RepeatedWellKnownTypes::source_context_field_size() const {
  return source_context_field_.size();
}
void RepeatedWellKnownTypes::clear_source_context_field() {
  source_context_field_.Clear();
}
const ::google::protobuf::SourceContext& RepeatedWellKnownTypes::source_context_field(int index) const {
  // @@protoc_insertion_point(field_get:protobuf_unittest.RepeatedWellKnownTypes.source_context_field)
  return source_context_field_.Get(index);
}
::google::protobuf::SourceContext* RepeatedWellKnownTypes::mutable_source_context_field(int index) {
  // @@protoc_insertion_point(field_mutable:protobuf_unittest.RepeatedWellKnownTypes.source_context_field)
  return source_context_field_.Mutable(index);
}
::google::protobuf::SourceContext* RepeatedWellKnownTypes::add_source_context_field() {
  // @@protoc_insertion_point(field_add:protobuf_unittest.RepeatedWellKnownTypes.source_context_field)
  return source_context_field_.Add();
}
::google::protobuf::RepeatedPtrField< ::google::protobuf::SourceContext >*
RepeatedWellKnownTypes::mutable_source_context_field() {
  // @@protoc_insertion_point(field_mutable_list:protobuf_unittest.RepeatedWellKnownTypes.source_context_field)
  return &source_context_field_;
}
const ::google::protobuf::RepeatedPtrField< ::google::protobuf::SourceContext >&
RepeatedWellKnownTypes::source_context_field() const {
  // @@protoc_insertion_point(field_list:protobuf_unittest.RepeatedWellKnownTypes.source_context_field)
  return source_context_field_;
}

// repeated .google.protobuf.Struct struct_field = 7;
int RepeatedWellKnownTypes::struct_field_size() const {
  return struct_field_.size();
}
void RepeatedWellKnownTypes::clear_struct_field() {
  struct_field_.Clear();
}
const ::google::protobuf::Struct& RepeatedWellKnownTypes::struct_field(int index) const {
  // @@protoc_insertion_point(field_get:protobuf_unittest.RepeatedWellKnownTypes.struct_field)
  return struct_field_.Get(index);
}
::google::protobuf::Struct* RepeatedWellKnownTypes::mutable_struct_field(int index) {
  // @@protoc_insertion_point(field_mutable:protobuf_unittest.RepeatedWellKnownTypes.struct_field)
  return struct_field_.Mutable(index);
}
::google::protobuf::Struct* RepeatedWellKnownTypes::add_struct_field() {
  // @@protoc_insertion_point(field_add:protobuf_unittest.RepeatedWellKnownTypes.struct_field)
  return struct_field_.Add();
}
::google::protobuf::RepeatedPtrField< ::google::protobuf::Struct >*
RepeatedWellKnownTypes::mutable_struct_field() {
  // @@protoc_insertion_point(field_mutable_list:protobuf_unittest.RepeatedWellKnownTypes.struct_field)
  return &struct_field_;
}
const ::google::protobuf::RepeatedPtrField< ::google::protobuf::Struct >&
RepeatedWellKnownTypes::struct_field() const {
  // @@protoc_insertion_point(field_list:protobuf_unittest.RepeatedWellKnownTypes.struct_field)
  return struct_field_;
}

// repeated .google.protobuf.Timestamp timestamp_field = 8;
int RepeatedWellKnownTypes::timestamp_field_size() const {
  return timestamp_field_.size();
}
void RepeatedWellKnownTypes::clear_timestamp_field() {
  timestamp_field_.Clear();
}
const ::google::protobuf::Timestamp& RepeatedWellKnownTypes::timestamp_field(int index) const {
  // @@protoc_insertion_point(field_get:protobuf_unittest.RepeatedWellKnownTypes.timestamp_field)
  return timestamp_field_.Get(index);
}
::google::protobuf::Timestamp* RepeatedWellKnownTypes::mutable_timestamp_field(int index) {
  // @@protoc_insertion_point(field_mutable:protobuf_unittest.RepeatedWellKnownTypes.timestamp_field)
  return timestamp_field_.Mutable(index);
}
::google::protobuf::Timestamp* RepeatedWellKnownTypes::add_timestamp_field() {
  // @@protoc_insertion_point(field_add:protobuf_unittest.RepeatedWellKnownTypes.timestamp_field)
  return timestamp_field_.Add();
}
::google::protobuf::RepeatedPtrField< ::google::protobuf::Timestamp >*
RepeatedWellKnownTypes::mutable_timestamp_field() {
  // @@protoc_insertion_point(field_mutable_list:protobuf_unittest.RepeatedWellKnownTypes.timestamp_field)
  return &timestamp_field_;
}
const ::google::protobuf::RepeatedPtrField< ::google::protobuf::Timestamp >&
RepeatedWellKnownTypes::timestamp_field() const {
  // @@protoc_insertion_point(field_list:protobuf_unittest.RepeatedWellKnownTypes.timestamp_field)
  return timestamp_field_;
}

// repeated .google.protobuf.Type type_field = 9;
int RepeatedWellKnownTypes::type_field_size() const {
  return type_field_.size();
}
void RepeatedWellKnownTypes::clear_type_field() {
  type_field_.Clear();
}
const ::google::protobuf::Type& RepeatedWellKnownTypes::type_field(int index) const {
  // @@protoc_insertion_point(field_get:protobuf_unittest.RepeatedWellKnownTypes.type_field)
  return type_field_.Get(index);
}
::google::protobuf::Type* RepeatedWellKnownTypes::mutable_type_field(int index) {
  // @@protoc_insertion_point(field_mutable:protobuf_unittest.RepeatedWellKnownTypes.type_field)
  return type_field_.Mutable(index);
}
::google::protobuf::Type* RepeatedWellKnownTypes::add_type_field() {
  // @@protoc_insertion_point(field_add:protobuf_unittest.RepeatedWellKnownTypes.type_field)
  return type_field_.Add();
}
::google::protobuf::RepeatedPtrField< ::google::protobuf::Type >*
RepeatedWellKnownTypes::mutable_type_field() {
  // @@protoc_insertion_point(field_mutable_list:protobuf_unittest.RepeatedWellKnownTypes.type_field)
  return &type_field_;
}
const ::google::protobuf::RepeatedPtrField< ::google::protobuf::Type >&
RepeatedWellKnownTypes::type_field() const {
  // @@protoc_insertion_point(field_list:protobuf_unittest.RepeatedWellKnownTypes.type_field)
  return type_field_;
}

// repeated .google.protobuf.DoubleValue double_field = 10;
int RepeatedWellKnownTypes::double_field_size() const {
  return double_field_.size();
}
void RepeatedWellKnownTypes::clear_double_field() {
  double_field_.Clear();
}
const ::google::protobuf::DoubleValue& RepeatedWellKnownTypes::double_field(int index) const {
  // @@protoc_insertion_point(field_get:protobuf_unittest.RepeatedWellKnownTypes.double_field)
  return double_field_.Get(index);
}
::google::protobuf::DoubleValue* RepeatedWellKnownTypes::mutable_double_field(int index) {
  // @@protoc_insertion_point(field_mutable:protobuf_unittest.RepeatedWellKnownTypes.double_field)
  return double_field_.Mutable(index);
}
::google::protobuf::DoubleValue* RepeatedWellKnownTypes::add_double_field() {
  // @@protoc_insertion_point(field_add:protobuf_unittest.RepeatedWellKnownTypes.double_field)
  return double_field_.Add();
}
::google::protobuf::RepeatedPtrField< ::google::protobuf::DoubleValue >*
RepeatedWellKnownTypes::mutable_double_field() {
  // @@protoc_insertion_point(field_mutable_list:protobuf_unittest.RepeatedWellKnownTypes.double_field)
  return &double_field_;
}
const ::google::protobuf::RepeatedPtrField< ::google::protobuf::DoubleValue >&
RepeatedWellKnownTypes::double_field() const {
  // @@protoc_insertion_point(field_list:protobuf_unittest.RepeatedWellKnownTypes.double_field)
  return double_field_;
}

// repeated .google.protobuf.FloatValue float_field = 11;
int RepeatedWellKnownTypes::float_field_size() const {
  return float_field_.size();
}
void RepeatedWellKnownTypes::clear_float_field() {
  float_field_.Clear();
}
const ::google::protobuf::FloatValue& RepeatedWellKnownTypes::float_field(int index) const {
  // @@protoc_insertion_point(field_get:protobuf_unittest.RepeatedWellKnownTypes.float_field)
  return float_field_.Get(index);
}
::google::protobuf::FloatValue* RepeatedWellKnownTypes::mutable_float_field(int index) {
  // @@protoc_insertion_point(field_mutable:protobuf_unittest.RepeatedWellKnownTypes.float_field)
  return float_field_.Mutable(index);
}
::google::protobuf::FloatValue* RepeatedWellKnownTypes::add_float_field() {
  // @@protoc_insertion_point(field_add:protobuf_unittest.RepeatedWellKnownTypes.float_field)
  return float_field_.Add();
}
::google::protobuf::RepeatedPtrField< ::google::protobuf::FloatValue >*
RepeatedWellKnownTypes::mutable_float_field() {
  // @@protoc_insertion_point(field_mutable_list:protobuf_unittest.RepeatedWellKnownTypes.float_field)
  return &float_field_;
}
const ::google::protobuf::RepeatedPtrField< ::google::protobuf::FloatValue >&
RepeatedWellKnownTypes::float_field() const {
  // @@protoc_insertion_point(field_list:protobuf_unittest.RepeatedWellKnownTypes.float_field)
  return float_field_;
}

// repeated .google.protobuf.Int64Value int64_field = 12;
int RepeatedWellKnownTypes::int64_field_size() const {
  return int64_field_.size();
}
void RepeatedWellKnownTypes::clear_int64_field() {
  int64_field_.Clear();
}
const ::google::protobuf::Int64Value& RepeatedWellKnownTypes::int64_field(int index) const {
  // @@protoc_insertion_point(field_get:protobuf_unittest.RepeatedWellKnownTypes.int64_field)
  return int64_field_.Get(index);
}
::google::protobuf::Int64Value* RepeatedWellKnownTypes::mutable_int64_field(int index) {
  // @@protoc_insertion_point(field_mutable:protobuf_unittest.RepeatedWellKnownTypes.int64_field)
  return int64_field_.Mutable(index);
}
::google::protobuf::Int64Value* RepeatedWellKnownTypes::add_int64_field() {
  // @@protoc_insertion_point(field_add:protobuf_unittest.RepeatedWellKnownTypes.int64_field)
  return int64_field_.Add();
}
::google::protobuf::RepeatedPtrField< ::google::protobuf::Int64Value >*
RepeatedWellKnownTypes::mutable_int64_field() {
  // @@protoc_insertion_point(field_mutable_list:protobuf_unittest.RepeatedWellKnownTypes.int64_field)
  return &int64_field_;
}
const ::google::protobuf::RepeatedPtrField< ::google::protobuf::Int64Value >&
RepeatedWellKnownTypes::int64_field() const {
  // @@protoc_insertion_point(field_list:protobuf_unittest.RepeatedWellKnownTypes.int64_field)
  return int64_field_;
}

// repeated .google.protobuf.UInt64Value uint64_field = 13;
int RepeatedWellKnownTypes::uint64_field_size() const {
  return uint64_field_.size();
}
void RepeatedWellKnownTypes::clear_uint64_field() {
  uint64_field_.Clear();
}
const ::google::protobuf::UInt64Value& RepeatedWellKnownTypes::uint64_field(int index) const {
  // @@protoc_insertion_point(field_get:protobuf_unittest.RepeatedWellKnownTypes.uint64_field)
  return uint64_field_.Get(index);
}
::google::protobuf::UInt64Value* RepeatedWellKnownTypes::mutable_uint64_field(int index) {
  // @@protoc_insertion_point(field_mutable:protobuf_unittest.RepeatedWellKnownTypes.uint64_field)
  return uint64_field_.Mutable(index);
}
::google::protobuf::UInt64Value* RepeatedWellKnownTypes::add_uint64_field() {
  // @@protoc_insertion_point(field_add:protobuf_unittest.RepeatedWellKnownTypes.uint64_field)
  return uint64_field_.Add();
}
::google::protobuf::RepeatedPtrField< ::google::protobuf::UInt64Value >*
RepeatedWellKnownTypes::mutable_uint64_field() {
  // @@protoc_insertion_point(field_mutable_list:protobuf_unittest.RepeatedWellKnownTypes.uint64_field)
  return &uint64_field_;
}
const ::google::protobuf::RepeatedPtrField< ::google::protobuf::UInt64Value >&
RepeatedWellKnownTypes::uint64_field() const {
  // @@protoc_insertion_point(field_list:protobuf_unittest.RepeatedWellKnownTypes.uint64_field)
  return uint64_field_;
}

// repeated .google.protobuf.Int32Value int32_field = 14;
int RepeatedWellKnownTypes::int32_field_size() const {
  return int32_field_.size();
}
void RepeatedWellKnownTypes::clear_int32_field() {
  int32_field_.Clear();
}
const ::google::protobuf::Int32Value& RepeatedWellKnownTypes::int32_field(int index) const {
  // @@protoc_insertion_point(field_get:protobuf_unittest.RepeatedWellKnownTypes.int32_field)
  return int32_field_.Get(index);
}
::google::protobuf::Int32Value* RepeatedWellKnownTypes::mutable_int32_field(int index) {
  // @@protoc_insertion_point(field_mutable:protobuf_unittest.RepeatedWellKnownTypes.int32_field)
  return int32_field_.Mutable(index);
}
::google::protobuf::Int32Value* RepeatedWellKnownTypes::add_int32_field() {
  // @@protoc_insertion_point(field_add:protobuf_unittest.RepeatedWellKnownTypes.int32_field)
  return int32_field_.Add();
}
::google::protobuf::RepeatedPtrField< ::google::protobuf::Int32Value >*
RepeatedWellKnownTypes::mutable_int32_field() {
  // @@protoc_insertion_point(field_mutable_list:protobuf_unittest.RepeatedWellKnownTypes.int32_field)
  return &int32_field_;
}
const ::google::protobuf::RepeatedPtrField< ::google::protobuf::Int32Value >&
RepeatedWellKnownTypes::int32_field() const {
  // @@protoc_insertion_point(field_list:protobuf_unittest.RepeatedWellKnownTypes.int32_field)
  return int32_field_;
}

// repeated .google.protobuf.UInt32Value uint32_field = 15;
int RepeatedWellKnownTypes::uint32_field_size() const {
  return uint32_field_.size();
}
void RepeatedWellKnownTypes::clear_uint32_field() {
  uint32_field_.Clear();
}
const ::google::protobuf::UInt32Value& RepeatedWellKnownTypes::uint32_field(int index) const {
  // @@protoc_insertion_point(field_get:protobuf_unittest.RepeatedWellKnownTypes.uint32_field)
  return uint32_field_.Get(index);
}
::google::protobuf::UInt32Value* RepeatedWellKnownTypes::mutable_uint32_field(int index) {
  // @@protoc_insertion_point(field_mutable:protobuf_unittest.RepeatedWellKnownTypes.uint32_field)
  return uint32_field_.Mutable(index);
}
::google::protobuf::UInt32Value* RepeatedWellKnownTypes::add_uint32_field() {
  // @@protoc_insertion_point(field_add:protobuf_unittest.RepeatedWellKnownTypes.uint32_field)
  return uint32_field_.Add();
}
::google::protobuf::RepeatedPtrField< ::google::protobuf::UInt32Value >*
RepeatedWellKnownTypes::mutable_uint32_field() {
  // @@protoc_insertion_point(field_mutable_list:protobuf_unittest.RepeatedWellKnownTypes.uint32_field)
  return &uint32_field_;
}
const ::google::protobuf::RepeatedPtrField< ::google::protobuf::UInt32Value >&
RepeatedWellKnownTypes::uint32_field() const {
  // @@protoc_insertion_point(field_list:protobuf_unittest.RepeatedWellKnownTypes.uint32_field)
  return uint32_field_;
}

// repeated .google.protobuf.BoolValue bool_field = 16;
int RepeatedWellKnownTypes::bool_field_size() const {
  return bool_field_.size();
}
void RepeatedWellKnownTypes::clear_bool_field() {
  bool_field_.Clear();
}
const ::google::protobuf::BoolValue& RepeatedWellKnownTypes::bool_field(int index) const {
  // @@protoc_insertion_point(field_get:protobuf_unittest.RepeatedWellKnownTypes.bool_field)
  return bool_field_.Get(index);
}
::google::protobuf::BoolValue* RepeatedWellKnownTypes::mutable_bool_field(int index) {
  // @@protoc_insertion_point(field_mutable:protobuf_unittest.RepeatedWellKnownTypes.bool_field)
  return bool_field_.Mutable(index);
}
::google::protobuf::BoolValue* RepeatedWellKnownTypes::add_bool_field() {
  // @@protoc_insertion_point(field_add:protobuf_unittest.RepeatedWellKnownTypes.bool_field)
  return bool_field_.Add();
}
::google::protobuf::RepeatedPtrField< ::google::protobuf::BoolValue >*
RepeatedWellKnownTypes::mutable_bool_field() {
  // @@protoc_insertion_point(field_mutable_list:protobuf_unittest.RepeatedWellKnownTypes.bool_field)
  return &bool_field_;
}
const ::google::protobuf::RepeatedPtrField< ::google::protobuf::BoolValue >&
RepeatedWellKnownTypes::bool_field() const {
  // @@protoc_insertion_point(field_list:protobuf_unittest.RepeatedWellKnownTypes.bool_field)
  return bool_field_;
}

// repeated .google.protobuf.StringValue string_field = 17;
int RepeatedWellKnownTypes::string_field_size() const {
  return string_field_.size();
}
void RepeatedWellKnownTypes::clear_string_field() {
  string_field_.Clear();
}
const ::google::protobuf::StringValue& RepeatedWellKnownTypes::string_field(int index) const {
  // @@protoc_insertion_point(field_get:protobuf_unittest.RepeatedWellKnownTypes.string_field)
  return string_field_.Get(index);
}
::google::protobuf::StringValue* RepeatedWellKnownTypes::mutable_string_field(int index) {
  // @@protoc_insertion_point(field_mutable:protobuf_unittest.RepeatedWellKnownTypes.string_field)
  return string_field_.Mutable(index);
}
::google::protobuf::StringValue* RepeatedWellKnownTypes::add_string_field() {
  // @@protoc_insertion_point(field_add:protobuf_unittest.RepeatedWellKnownTypes.string_field)
  return string_field_.Add();
}
::google::protobuf::RepeatedPtrField< ::google::protobuf::StringValue >*
RepeatedWellKnownTypes::mutable_string_field() {
  // @@protoc_insertion_point(field_mutable_list:protobuf_unittest.RepeatedWellKnownTypes.string_field)
  return &string_field_;
}
const ::google::protobuf::RepeatedPtrField< ::google::protobuf::StringValue >&
RepeatedWellKnownTypes::string_field() const {
  // @@protoc_insertion_point(field_list:protobuf_unittest.RepeatedWellKnownTypes.string_field)
  return string_field_;
}

// repeated .google.protobuf.BytesValue bytes_field = 18;
int RepeatedWellKnownTypes::bytes_field_size() const {
  return bytes_field_.size();
}
void RepeatedWellKnownTypes::clear_bytes_field() {
  bytes_field_.Clear();
}
const ::google::protobuf::BytesValue& RepeatedWellKnownTypes::bytes_field(int index) const {
  // @@protoc_insertion_point(field_get:protobuf_unittest.RepeatedWellKnownTypes.bytes_field)
  return bytes_field_.Get(index);
}
::google::protobuf::BytesValue* RepeatedWellKnownTypes::mutable_bytes_field(int index) {
  // @@protoc_insertion_point(field_mutable:protobuf_unittest.RepeatedWellKnownTypes.bytes_field)
  return bytes_field_.Mutable(index);
}
::google::protobuf::BytesValue* RepeatedWellKnownTypes::add_bytes_field() {
  // @@protoc_insertion_point(field_add:protobuf_unittest.RepeatedWellKnownTypes.bytes_field)
  return bytes_field_.Add();
}
::google::protobuf::RepeatedPtrField< ::google::protobuf::BytesValue >*
RepeatedWellKnownTypes::mutable_bytes_field() {
  // @@protoc_insertion_point(field_mutable_list:protobuf_unittest.RepeatedWellKnownTypes.bytes_field)
  return &bytes_field_;
}
const ::google::protobuf::RepeatedPtrField< ::google::protobuf::BytesValue >&
RepeatedWellKnownTypes::bytes_field() const {
  // @@protoc_insertion_point(field_list:protobuf_unittest.RepeatedWellKnownTypes.bytes_field)
  return bytes_field_;
}

inline const RepeatedWellKnownTypes* RepeatedWellKnownTypes::internal_default_instance() {
  return &RepeatedWellKnownTypes_default_instance_.get();
}
#endif  // PROTOBUF_INLINE_NOT_IN_HEADERS

// ===================================================================

#if !defined(_MSC_VER) || _MSC_VER >= 1900
const int OneofWellKnownTypes::kAnyFieldFieldNumber;
const int OneofWellKnownTypes::kApiFieldFieldNumber;
const int OneofWellKnownTypes::kDurationFieldFieldNumber;
const int OneofWellKnownTypes::kEmptyFieldFieldNumber;
const int OneofWellKnownTypes::kFieldMaskFieldFieldNumber;
const int OneofWellKnownTypes::kSourceContextFieldFieldNumber;
const int OneofWellKnownTypes::kStructFieldFieldNumber;
const int OneofWellKnownTypes::kTimestampFieldFieldNumber;
const int OneofWellKnownTypes::kTypeFieldFieldNumber;
const int OneofWellKnownTypes::kDoubleFieldFieldNumber;
const int OneofWellKnownTypes::kFloatFieldFieldNumber;
const int OneofWellKnownTypes::kInt64FieldFieldNumber;
const int OneofWellKnownTypes::kUint64FieldFieldNumber;
const int OneofWellKnownTypes::kInt32FieldFieldNumber;
const int OneofWellKnownTypes::kUint32FieldFieldNumber;
const int OneofWellKnownTypes::kBoolFieldFieldNumber;
const int OneofWellKnownTypes::kStringFieldFieldNumber;
const int OneofWellKnownTypes::kBytesFieldFieldNumber;
#endif  // !defined(_MSC_VER) || _MSC_VER >= 1900

OneofWellKnownTypes::OneofWellKnownTypes()
  : ::google::protobuf::Message(), _internal_metadata_(NULL) {
  if (this != internal_default_instance()) protobuf_InitDefaults_google_2fprotobuf_2funittest_5fwell_5fknown_5ftypes_2eproto();
  SharedCtor();
  // @@protoc_insertion_point(constructor:protobuf_unittest.OneofWellKnownTypes)
}

void OneofWellKnownTypes::InitAsDefaultInstance() {
  OneofWellKnownTypes_default_oneof_instance_->any_field_ = const_cast< ::google::protobuf::Any*>(
      ::google::protobuf::Any::internal_default_instance());
  OneofWellKnownTypes_default_oneof_instance_->api_field_ = const_cast< ::google::protobuf::Api*>(
      ::google::protobuf::Api::internal_default_instance());
  OneofWellKnownTypes_default_oneof_instance_->duration_field_ = const_cast< ::google::protobuf::Duration*>(
      ::google::protobuf::Duration::internal_default_instance());
  OneofWellKnownTypes_default_oneof_instance_->empty_field_ = const_cast< ::google::protobuf::Empty*>(
      ::google::protobuf::Empty::internal_default_instance());
  OneofWellKnownTypes_default_oneof_instance_->field_mask_field_ = const_cast< ::google::protobuf::FieldMask*>(
      ::google::protobuf::FieldMask::internal_default_instance());
  OneofWellKnownTypes_default_oneof_instance_->source_context_field_ = const_cast< ::google::protobuf::SourceContext*>(
      ::google::protobuf::SourceContext::internal_default_instance());
  OneofWellKnownTypes_default_oneof_instance_->struct_field_ = const_cast< ::google::protobuf::Struct*>(
      ::google::protobuf::Struct::internal_default_instance());
  OneofWellKnownTypes_default_oneof_instance_->timestamp_field_ = const_cast< ::google::protobuf::Timestamp*>(
      ::google::protobuf::Timestamp::internal_default_instance());
  OneofWellKnownTypes_default_oneof_instance_->type_field_ = const_cast< ::google::protobuf::Type*>(
      ::google::protobuf::Type::internal_default_instance());
  OneofWellKnownTypes_default_oneof_instance_->double_field_ = const_cast< ::google::protobuf::DoubleValue*>(
      ::google::protobuf::DoubleValue::internal_default_instance());
  OneofWellKnownTypes_default_oneof_instance_->float_field_ = const_cast< ::google::protobuf::FloatValue*>(
      ::google::protobuf::FloatValue::internal_default_instance());
  OneofWellKnownTypes_default_oneof_instance_->int64_field_ = const_cast< ::google::protobuf::Int64Value*>(
      ::google::protobuf::Int64Value::internal_default_instance());
  OneofWellKnownTypes_default_oneof_instance_->uint64_field_ = const_cast< ::google::protobuf::UInt64Value*>(
      ::google::protobuf::UInt64Value::internal_default_instance());
  OneofWellKnownTypes_default_oneof_instance_->int32_field_ = const_cast< ::google::protobuf::Int32Value*>(
      ::google::protobuf::Int32Value::internal_default_instance());
  OneofWellKnownTypes_default_oneof_instance_->uint32_field_ = const_cast< ::google::protobuf::UInt32Value*>(
      ::google::protobuf::UInt32Value::internal_default_instance());
  OneofWellKnownTypes_default_oneof_instance_->bool_field_ = const_cast< ::google::protobuf::BoolValue*>(
      ::google::protobuf::BoolValue::internal_default_instance());
  OneofWellKnownTypes_default_oneof_instance_->string_field_ = const_cast< ::google::protobuf::StringValue*>(
      ::google::protobuf::StringValue::internal_default_instance());
  OneofWellKnownTypes_default_oneof_instance_->bytes_field_ = const_cast< ::google::protobuf::BytesValue*>(
      ::google::protobuf::BytesValue::internal_default_instance());
}

OneofWellKnownTypes::OneofWellKnownTypes(const OneofWellKnownTypes& from)
  : ::google::protobuf::Message(),
    _internal_metadata_(NULL) {
  SharedCtor();
  UnsafeMergeFrom(from);
  // @@protoc_insertion_point(copy_constructor:protobuf_unittest.OneofWellKnownTypes)
}

void OneofWellKnownTypes::SharedCtor() {
  clear_has_oneof_field();
  _cached_size_ = 0;
}

OneofWellKnownTypes::~OneofWellKnownTypes() {
  // @@protoc_insertion_point(destructor:protobuf_unittest.OneofWellKnownTypes)
  SharedDtor();
}

void OneofWellKnownTypes::SharedDtor() {
  if (has_oneof_field()) {
    clear_oneof_field();
  }
}

void OneofWellKnownTypes::SetCachedSize(int size) const {
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
}
const ::google::protobuf::Descriptor* OneofWellKnownTypes::descriptor() {
  protobuf_AssignDescriptorsOnce();
  return OneofWellKnownTypes_descriptor_;
}

const OneofWellKnownTypes& OneofWellKnownTypes::default_instance() {
  protobuf_InitDefaults_google_2fprotobuf_2funittest_5fwell_5fknown_5ftypes_2eproto();
  return *internal_default_instance();
}

::google::protobuf::internal::ExplicitlyConstructed<OneofWellKnownTypes> OneofWellKnownTypes_default_instance_;

OneofWellKnownTypes* OneofWellKnownTypes::New(::google::protobuf::Arena* arena) const {
  OneofWellKnownTypes* n = new OneofWellKnownTypes;
  if (arena != NULL) {
    arena->Own(n);
  }
  return n;
}

void OneofWellKnownTypes::clear_oneof_field() {
// @@protoc_insertion_point(one_of_clear_start:protobuf_unittest.OneofWellKnownTypes)
  switch (oneof_field_case()) {
    case kAnyField: {
      delete oneof_field_.any_field_;
      break;
    }
    case kApiField: {
      delete oneof_field_.api_field_;
      break;
    }
    case kDurationField: {
      delete oneof_field_.duration_field_;
      break;
    }
    case kEmptyField: {
      delete oneof_field_.empty_field_;
      break;
    }
    case kFieldMaskField: {
      delete oneof_field_.field_mask_field_;
      break;
    }
    case kSourceContextField: {
      delete oneof_field_.source_context_field_;
      break;
    }
    case kStructField: {
      delete oneof_field_.struct_field_;
      break;
    }
    case kTimestampField: {
      delete oneof_field_.timestamp_field_;
      break;
    }
    case kTypeField: {
      delete oneof_field_.type_field_;
      break;
    }
    case kDoubleField: {
      delete oneof_field_.double_field_;
      break;
    }
    case kFloatField: {
      delete oneof_field_.float_field_;
      break;
    }
    case kInt64Field: {
      delete oneof_field_.int64_field_;
      break;
    }
    case kUint64Field: {
      delete oneof_field_.uint64_field_;
      break;
    }
    case kInt32Field: {
      delete oneof_field_.int32_field_;
      break;
    }
    case kUint32Field: {
      delete oneof_field_.uint32_field_;
      break;
    }
    case kBoolField: {
      delete oneof_field_.bool_field_;
      break;
    }
    case kStringField: {
      delete oneof_field_.string_field_;
      break;
    }
    case kBytesField: {
      delete oneof_field_.bytes_field_;
      break;
    }
    case ONEOF_FIELD_NOT_SET: {
      break;
    }
  }
  _oneof_case_[0] = ONEOF_FIELD_NOT_SET;
}


void OneofWellKnownTypes::Clear() {
// @@protoc_insertion_point(message_clear_start:protobuf_unittest.OneofWellKnownTypes)
  clear_oneof_field();
}

bool OneofWellKnownTypes::MergePartialFromCodedStream(
    ::google::protobuf::io::CodedInputStream* input) {
#define DO_(EXPRESSION) if (!GOOGLE_PREDICT_TRUE(EXPRESSION)) goto failure
  ::google::protobuf::uint32 tag;
  // @@protoc_insertion_point(parse_start:protobuf_unittest.OneofWellKnownTypes)
  for (;;) {
    ::std::pair< ::google::protobuf::uint32, bool> p = input->ReadTagWithCutoff(16383);
    tag = p.first;
    if (!p.second) goto handle_unusual;
    switch (::google::protobuf::internal::WireFormatLite::GetTagFieldNumber(tag)) {
      // optional .google.protobuf.Any any_field = 1;
      case 1: {
        if (tag == 10) {
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessageNoVirtual(
               input, mutable_any_field()));
        } else {
          goto handle_unusual;
        }
        goto after_bytes_field;
        break;
      }

      // optional .google.protobuf.Api api_field = 2;
      case 2: {
        if (tag == 18) {
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessageNoVirtual(
               input, mutable_api_field()));
        } else {
          goto handle_unusual;
        }
        goto after_bytes_field;
        break;
      }

      // optional .google.protobuf.Duration duration_field = 3;
      case 3: {
        if (tag == 26) {
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessageNoVirtual(
               input, mutable_duration_field()));
        } else {
          goto handle_unusual;
        }
        goto after_bytes_field;
        break;
      }

      // optional .google.protobuf.Empty empty_field = 4;
      case 4: {
        if (tag == 34) {
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessageNoVirtual(
               input, mutable_empty_field()));
        } else {
          goto handle_unusual;
        }
        goto after_bytes_field;
        break;
      }

      // optional .google.protobuf.FieldMask field_mask_field = 5;
      case 5: {
        if (tag == 42) {
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessageNoVirtual(
               input, mutable_field_mask_field()));
        } else {
          goto handle_unusual;
        }
        goto after_bytes_field;
        break;
      }

      // optional .google.protobuf.SourceContext source_context_field = 6;
      case 6: {
        if (tag == 50) {
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessageNoVirtual(
               input, mutable_source_context_field()));
        } else {
          goto handle_unusual;
        }
        goto after_bytes_field;
        break;
      }

      // optional .google.protobuf.Struct struct_field = 7;
      case 7: {
        if (tag == 58) {
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessageNoVirtual(
               input, mutable_struct_field()));
        } else {
          goto handle_unusual;
        }
        goto after_bytes_field;
        break;
      }

      // optional .google.protobuf.Timestamp timestamp_field = 8;
      case 8: {
        if (tag == 66) {
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessageNoVirtual(
               input, mutable_timestamp_field()));
        } else {
          goto handle_unusual;
        }
        goto after_bytes_field;
        break;
      }

      // optional .google.protobuf.Type type_field = 9;
      case 9: {
        if (tag == 74) {
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessageNoVirtual(
               input, mutable_type_field()));
        } else {
          goto handle_unusual;
        }
        goto after_bytes_field;
        break;
      }

      // optional .google.protobuf.DoubleValue double_field = 10;
      case 10: {
        if (tag == 82) {
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessageNoVirtual(
               input, mutable_double_field()));
        } else {
          goto handle_unusual;
        }
        goto after_bytes_field;
        break;
      }

      // optional .google.protobuf.FloatValue float_field = 11;
      case 11: {
        if (tag == 90) {
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessageNoVirtual(
               input, mutable_float_field()));
        } else {
          goto handle_unusual;
        }
        goto after_bytes_field;
        break;
      }

      // optional .google.protobuf.Int64Value int64_field = 12;
      case 12: {
        if (tag == 98) {
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessageNoVirtual(
               input, mutable_int64_field()));
        } else {
          goto handle_unusual;
        }
        goto after_bytes_field;
        break;
      }

      // optional .google.protobuf.UInt64Value uint64_field = 13;
      case 13: {
        if (tag == 106) {
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessageNoVirtual(
               input, mutable_uint64_field()));
        } else {
          goto handle_unusual;
        }
        goto after_bytes_field;
        break;
      }

      // optional .google.protobuf.Int32Value int32_field = 14;
      case 14: {
        if (tag == 114) {
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessageNoVirtual(
               input, mutable_int32_field()));
        } else {
          goto handle_unusual;
        }
        goto after_bytes_field;
        break;
      }

      // optional .google.protobuf.UInt32Value uint32_field = 15;
      case 15: {
        if (tag == 122) {
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessageNoVirtual(
               input, mutable_uint32_field()));
        } else {
          goto handle_unusual;
        }
        goto after_bytes_field;
        break;
      }

      // optional .google.protobuf.BoolValue bool_field = 16;
      case 16: {
        if (tag == 130) {
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessageNoVirtual(
               input, mutable_bool_field()));
        } else {
          goto handle_unusual;
        }
        goto after_bytes_field;
        break;
      }

      // optional .google.protobuf.StringValue string_field = 17;
      case 17: {
        if (tag == 138) {
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessageNoVirtual(
               input, mutable_string_field()));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(146)) goto parse_bytes_field;
        break;
      }

      // optional .google.protobuf.BytesValue bytes_field = 18;
      case 18: {
        if (tag == 146) {
         parse_bytes_field:
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessageNoVirtual(
               input, mutable_bytes_field()));
        } else {
          goto handle_unusual;
        }
       after_bytes_field:
        if (input->ExpectAtEnd()) goto success;
        break;
      }

      default: {
      handle_unusual:
        if (tag == 0 ||
            ::google::protobuf::internal::WireFormatLite::GetTagWireType(tag) ==
            ::google::protobuf::internal::WireFormatLite::WIRETYPE_END_GROUP) {
          goto success;
        }
        DO_(::google::protobuf::internal::WireFormatLite::SkipField(input, tag));
        break;
      }
    }
  }
success:
  // @@protoc_insertion_point(parse_success:protobuf_unittest.OneofWellKnownTypes)
  return true;
failure:
  // @@protoc_insertion_point(parse_failure:protobuf_unittest.OneofWellKnownTypes)
  return false;
#undef DO_
}

void OneofWellKnownTypes::SerializeWithCachedSizes(
    ::google::protobuf::io::CodedOutputStream* output) const {
  // @@protoc_insertion_point(serialize_start:protobuf_unittest.OneofWellKnownTypes)
  // optional .google.protobuf.Any any_field = 1;
  if (has_any_field()) {
    ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
      1, *oneof_field_.any_field_, output);
  }

  // optional .google.protobuf.Api api_field = 2;
  if (has_api_field()) {
    ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
      2, *oneof_field_.api_field_, output);
  }

  // optional .google.protobuf.Duration duration_field = 3;
  if (has_duration_field()) {
    ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
      3, *oneof_field_.duration_field_, output);
  }

  // optional .google.protobuf.Empty empty_field = 4;
  if (has_empty_field()) {
    ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
      4, *oneof_field_.empty_field_, output);
  }

  // optional .google.protobuf.FieldMask field_mask_field = 5;
  if (has_field_mask_field()) {
    ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
      5, *oneof_field_.field_mask_field_, output);
  }

  // optional .google.protobuf.SourceContext source_context_field = 6;
  if (has_source_context_field()) {
    ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
      6, *oneof_field_.source_context_field_, output);
  }

  // optional .google.protobuf.Struct struct_field = 7;
  if (has_struct_field()) {
    ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
      7, *oneof_field_.struct_field_, output);
  }

  // optional .google.protobuf.Timestamp timestamp_field = 8;
  if (has_timestamp_field()) {
    ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
      8, *oneof_field_.timestamp_field_, output);
  }

  // optional .google.protobuf.Type type_field = 9;
  if (has_type_field()) {
    ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
      9, *oneof_field_.type_field_, output);
  }

  // optional .google.protobuf.DoubleValue double_field = 10;
  if (has_double_field()) {
    ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
      10, *oneof_field_.double_field_, output);
  }

  // optional .google.protobuf.FloatValue float_field = 11;
  if (has_float_field()) {
    ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
      11, *oneof_field_.float_field_, output);
  }

  // optional .google.protobuf.Int64Value int64_field = 12;
  if (has_int64_field()) {
    ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
      12, *oneof_field_.int64_field_, output);
  }

  // optional .google.protobuf.UInt64Value uint64_field = 13;
  if (has_uint64_field()) {
    ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
      13, *oneof_field_.uint64_field_, output);
  }

  // optional .google.protobuf.Int32Value int32_field = 14;
  if (has_int32_field()) {
    ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
      14, *oneof_field_.int32_field_, output);
  }

  // optional .google.protobuf.UInt32Value uint32_field = 15;
  if (has_uint32_field()) {
    ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
      15, *oneof_field_.uint32_field_, output);
  }

  // optional .google.protobuf.BoolValue bool_field = 16;
  if (has_bool_field()) {
    ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
      16, *oneof_field_.bool_field_, output);
  }

  // optional .google.protobuf.StringValue string_field = 17;
  if (has_string_field()) {
    ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
      17, *oneof_field_.string_field_, output);
  }

  // optional .google.protobuf.BytesValue bytes_field = 18;
  if (has_bytes_field()) {
    ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
      18, *oneof_field_.bytes_field_, output);
  }

  // @@protoc_insertion_point(serialize_end:protobuf_unittest.OneofWellKnownTypes)
}

::google::protobuf::uint8* OneofWellKnownTypes::InternalSerializeWithCachedSizesToArray(
    bool deterministic, ::google::protobuf::uint8* target) const {
  (void)deterministic; // Unused
  // @@protoc_insertion_point(serialize_to_array_start:protobuf_unittest.OneofWellKnownTypes)
  // optional .google.protobuf.Any any_field = 1;
  if (has_any_field()) {
    target = ::google::protobuf::internal::WireFormatLite::
      InternalWriteMessageNoVirtualToArray(
        1, *oneof_field_.any_field_, false, target);
  }

  // optional .google.protobuf.Api api_field = 2;
  if (has_api_field()) {
    target = ::google::protobuf::internal::WireFormatLite::
      InternalWriteMessageNoVirtualToArray(
        2, *oneof_field_.api_field_, false, target);
  }

  // optional .google.protobuf.Duration duration_field = 3;
  if (has_duration_field()) {
    target = ::google::protobuf::internal::WireFormatLite::
      InternalWriteMessageNoVirtualToArray(
        3, *oneof_field_.duration_field_, false, target);
  }

  // optional .google.protobuf.Empty empty_field = 4;
  if (has_empty_field()) {
    target = ::google::protobuf::internal::WireFormatLite::
      InternalWriteMessageNoVirtualToArray(
        4, *oneof_field_.empty_field_, false, target);
  }

  // optional .google.protobuf.FieldMask field_mask_field = 5;
  if (has_field_mask_field()) {
    target = ::google::protobuf::internal::WireFormatLite::
      InternalWriteMessageNoVirtualToArray(
        5, *oneof_field_.field_mask_field_, false, target);
  }

  // optional .google.protobuf.SourceContext source_context_field = 6;
  if (has_source_context_field()) {
    target = ::google::protobuf::internal::WireFormatLite::
      InternalWriteMessageNoVirtualToArray(
        6, *oneof_field_.source_context_field_, false, target);
  }

  // optional .google.protobuf.Struct struct_field = 7;
  if (has_struct_field()) {
    target = ::google::protobuf::internal::WireFormatLite::
      InternalWriteMessageNoVirtualToArray(
        7, *oneof_field_.struct_field_, false, target);
  }

  // optional .google.protobuf.Timestamp timestamp_field = 8;
  if (has_timestamp_field()) {
    target = ::google::protobuf::internal::WireFormatLite::
      InternalWriteMessageNoVirtualToArray(
        8, *oneof_field_.timestamp_field_, false, target);
  }

  // optional .google.protobuf.Type type_field = 9;
  if (has_type_field()) {
    target = ::google::protobuf::internal::WireFormatLite::
      InternalWriteMessageNoVirtualToArray(
        9, *oneof_field_.type_field_, false, target);
  }

  // optional .google.protobuf.DoubleValue double_field = 10;
  if (has_double_field()) {
    target = ::google::protobuf::internal::WireFormatLite::
      InternalWriteMessageNoVirtualToArray(
        10, *oneof_field_.double_field_, false, target);
  }

  // optional .google.protobuf.FloatValue float_field = 11;
  if (has_float_field()) {
    target = ::google::protobuf::internal::WireFormatLite::
      InternalWriteMessageNoVirtualToArray(
        11, *oneof_field_.float_field_, false, target);
  }

  // optional .google.protobuf.Int64Value int64_field = 12;
  if (has_int64_field()) {
    target = ::google::protobuf::internal::WireFormatLite::
      InternalWriteMessageNoVirtualToArray(
        12, *oneof_field_.int64_field_, false, target);
  }

  // optional .google.protobuf.UInt64Value uint64_field = 13;
  if (has_uint64_field()) {
    target = ::google::protobuf::internal::WireFormatLite::
      InternalWriteMessageNoVirtualToArray(
        13, *oneof_field_.uint64_field_, false, target);
  }

  // optional .google.protobuf.Int32Value int32_field = 14;
  if (has_int32_field()) {
    target = ::google::protobuf::internal::WireFormatLite::
      InternalWriteMessageNoVirtualToArray(
        14, *oneof_field_.int32_field_, false, target);
  }

  // optional .google.protobuf.UInt32Value uint32_field = 15;
  if (has_uint32_field()) {
    target = ::google::protobuf::internal::WireFormatLite::
      InternalWriteMessageNoVirtualToArray(
        15, *oneof_field_.uint32_field_, false, target);
  }

  // optional .google.protobuf.BoolValue bool_field = 16;
  if (has_bool_field()) {
    target = ::google::protobuf::internal::WireFormatLite::
      InternalWriteMessageNoVirtualToArray(
        16, *oneof_field_.bool_field_, false, target);
  }

  // optional .google.protobuf.StringValue string_field = 17;
  if (has_string_field()) {
    target = ::google::protobuf::internal::WireFormatLite::
      InternalWriteMessageNoVirtualToArray(
        17, *oneof_field_.string_field_, false, target);
  }

  // optional .google.protobuf.BytesValue bytes_field = 18;
  if (has_bytes_field()) {
    target = ::google::protobuf::internal::WireFormatLite::
      InternalWriteMessageNoVirtualToArray(
        18, *oneof_field_.bytes_field_, false, target);
  }

  // @@protoc_insertion_point(serialize_to_array_end:protobuf_unittest.OneofWellKnownTypes)
  return target;
}

size_t OneofWellKnownTypes::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:protobuf_unittest.OneofWellKnownTypes)
  size_t total_size = 0;

  switch (oneof_field_case()) {
    // optional .google.protobuf.Any any_field = 1;
    case kAnyField: {
      total_size += 1 +
        ::google::protobuf::internal::WireFormatLite::MessageSizeNoVirtual(
          *oneof_field_.any_field_);
      break;
    }
    // optional .google.protobuf.Api api_field = 2;
    case kApiField: {
      total_size += 1 +
        ::google::protobuf::internal::WireFormatLite::MessageSizeNoVirtual(
          *oneof_field_.api_field_);
      break;
    }
    // optional .google.protobuf.Duration duration_field = 3;
    case kDurationField: {
      total_size += 1 +
        ::google::protobuf::internal::WireFormatLite::MessageSizeNoVirtual(
          *oneof_field_.duration_field_);
      break;
    }
    // optional .google.protobuf.Empty empty_field = 4;
    case kEmptyField: {
      total_size += 1 +
        ::google::protobuf::internal::WireFormatLite::MessageSizeNoVirtual(
          *oneof_field_.empty_field_);
      break;
    }
    // optional .google.protobuf.FieldMask field_mask_field = 5;
    case kFieldMaskField: {
      total_size += 1 +
        ::google::protobuf::internal::WireFormatLite::MessageSizeNoVirtual(
          *oneof_field_.field_mask_field_);
      break;
    }
    // optional .google.protobuf.SourceContext source_context_field = 6;
    case kSourceContextField: {
      total_size += 1 +
        ::google::protobuf::internal::WireFormatLite::MessageSizeNoVirtual(
          *oneof_field_.source_context_field_);
      break;
    }
    // optional .google.protobuf.Struct struct_field = 7;
    case kStructField: {
      total_size += 1 +
        ::google::protobuf::internal::WireFormatLite::MessageSizeNoVirtual(
          *oneof_field_.struct_field_);
      break;
    }
    // optional .google.protobuf.Timestamp timestamp_field = 8;
    case kTimestampField: {
      total_size += 1 +
        ::google::protobuf::internal::WireFormatLite::MessageSizeNoVirtual(
          *oneof_field_.timestamp_field_);
      break;
    }
    // optional .google.protobuf.Type type_field = 9;
    case kTypeField: {
      total_size += 1 +
        ::google::protobuf::internal::WireFormatLite::MessageSizeNoVirtual(
          *oneof_field_.type_field_);
      break;
    }
    // optional .google.protobuf.DoubleValue double_field = 10;
    case kDoubleField: {
      total_size += 1 +
        ::google::protobuf::internal::WireFormatLite::MessageSizeNoVirtual(
          *oneof_field_.double_field_);
      break;
    }
    // optional .google.protobuf.FloatValue float_field = 11;
    case kFloatField: {
      total_size += 1 +
        ::google::protobuf::internal::WireFormatLite::MessageSizeNoVirtual(
          *oneof_field_.float_field_);
      break;
    }
    // optional .google.protobuf.Int64Value int64_field = 12;
    case kInt64Field: {
      total_size += 1 +
        ::google::protobuf::internal::WireFormatLite::MessageSizeNoVirtual(
          *oneof_field_.int64_field_);
      break;
    }
    // optional .google.protobuf.UInt64Value uint64_field = 13;
    case kUint64Field: {
      total_size += 1 +
        ::google::protobuf::internal::WireFormatLite::MessageSizeNoVirtual(
          *oneof_field_.uint64_field_);
      break;
    }
    // optional .google.protobuf.Int32Value int32_field = 14;
    case kInt32Field: {
      total_size += 1 +
        ::google::protobuf::internal::WireFormatLite::MessageSizeNoVirtual(
          *oneof_field_.int32_field_);
      break;
    }
    // optional .google.protobuf.UInt32Value uint32_field = 15;
    case kUint32Field: {
      total_size += 1 +
        ::google::protobuf::internal::WireFormatLite::MessageSizeNoVirtual(
          *oneof_field_.uint32_field_);
      break;
    }
    // optional .google.protobuf.BoolValue bool_field = 16;
    case kBoolField: {
      total_size += 2 +
        ::google::protobuf::internal::WireFormatLite::MessageSizeNoVirtual(
          *oneof_field_.bool_field_);
      break;
    }
    // optional .google.protobuf.StringValue string_field = 17;
    case kStringField: {
      total_size += 2 +
        ::google::protobuf::internal::WireFormatLite::MessageSizeNoVirtual(
          *oneof_field_.string_field_);
      break;
    }
    // optional .google.protobuf.BytesValue bytes_field = 18;
    case kBytesField: {
      total_size += 2 +
        ::google::protobuf::internal::WireFormatLite::MessageSizeNoVirtual(
          *oneof_field_.bytes_field_);
      break;
    }
    case ONEOF_FIELD_NOT_SET: {
      break;
    }
  }
  int cached_size = ::google::protobuf::internal::ToCachedSize(total_size);
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = cached_size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
  return total_size;
}

void OneofWellKnownTypes::MergeFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_merge_from_start:protobuf_unittest.OneofWellKnownTypes)
  if (GOOGLE_PREDICT_FALSE(&from == this)) MergeFromFail(__LINE__);
  const OneofWellKnownTypes* source =
      ::google::protobuf::internal::DynamicCastToGenerated<const OneofWellKnownTypes>(
          &from);
  if (source == NULL) {
  // @@protoc_insertion_point(generalized_merge_from_cast_fail:protobuf_unittest.OneofWellKnownTypes)
    ::google::protobuf::internal::ReflectionOps::Merge(from, this);
  } else {
  // @@protoc_insertion_point(generalized_merge_from_cast_success:protobuf_unittest.OneofWellKnownTypes)
    UnsafeMergeFrom(*source);
  }
}

void OneofWellKnownTypes::MergeFrom(const OneofWellKnownTypes& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:protobuf_unittest.OneofWellKnownTypes)
  if (GOOGLE_PREDICT_TRUE(&from != this)) {
    UnsafeMergeFrom(from);
  } else {
    MergeFromFail(__LINE__);
  }
}

void OneofWellKnownTypes::UnsafeMergeFrom(const OneofWellKnownTypes& from) {
  GOOGLE_DCHECK(&from != this);
  switch (from.oneof_field_case()) {
    case kAnyField: {
      mutable_any_field()->::google::protobuf::Any::MergeFrom(from.any_field());
      break;
    }
    case kApiField: {
      mutable_api_field()->::google::protobuf::Api::MergeFrom(from.api_field());
      break;
    }
    case kDurationField: {
      mutable_duration_field()->::google::protobuf::Duration::MergeFrom(from.duration_field());
      break;
    }
    case kEmptyField: {
      mutable_empty_field()->::google::protobuf::Empty::MergeFrom(from.empty_field());
      break;
    }
    case kFieldMaskField: {
      mutable_field_mask_field()->::google::protobuf::FieldMask::MergeFrom(from.field_mask_field());
      break;
    }
    case kSourceContextField: {
      mutable_source_context_field()->::google::protobuf::SourceContext::MergeFrom(from.source_context_field());
      break;
    }
    case kStructField: {
      mutable_struct_field()->::google::protobuf::Struct::MergeFrom(from.struct_field());
      break;
    }
    case kTimestampField: {
      mutable_timestamp_field()->::google::protobuf::Timestamp::MergeFrom(from.timestamp_field());
      break;
    }
    case kTypeField: {
      mutable_type_field()->::google::protobuf::Type::MergeFrom(from.type_field());
      break;
    }
    case kDoubleField: {
      mutable_double_field()->::google::protobuf::DoubleValue::MergeFrom(from.double_field());
      break;
    }
    case kFloatField: {
      mutable_float_field()->::google::protobuf::FloatValue::MergeFrom(from.float_field());
      break;
    }
    case kInt64Field: {
      mutable_int64_field()->::google::protobuf::Int64Value::MergeFrom(from.int64_field());
      break;
    }
    case kUint64Field: {
      mutable_uint64_field()->::google::protobuf::UInt64Value::MergeFrom(from.uint64_field());
      break;
    }
    case kInt32Field: {
      mutable_int32_field()->::google::protobuf::Int32Value::MergeFrom(from.int32_field());
      break;
    }
    case kUint32Field: {
      mutable_uint32_field()->::google::protobuf::UInt32Value::MergeFrom(from.uint32_field());
      break;
    }
    case kBoolField: {
      mutable_bool_field()->::google::protobuf::BoolValue::MergeFrom(from.bool_field());
      break;
    }
    case kStringField: {
      mutable_string_field()->::google::protobuf::StringValue::MergeFrom(from.string_field());
      break;
    }
    case kBytesField: {
      mutable_bytes_field()->::google::protobuf::BytesValue::MergeFrom(from.bytes_field());
      break;
    }
    case ONEOF_FIELD_NOT_SET: {
      break;
    }
  }
}

void OneofWellKnownTypes::CopyFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_copy_from_start:protobuf_unittest.OneofWellKnownTypes)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

void OneofWellKnownTypes::CopyFrom(const OneofWellKnownTypes& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:protobuf_unittest.OneofWellKnownTypes)
  if (&from == this) return;
  Clear();
  UnsafeMergeFrom(from);
}

bool OneofWellKnownTypes::IsInitialized() const {

  return true;
}

void OneofWellKnownTypes::Swap(OneofWellKnownTypes* other) {
  if (other == this) return;
  InternalSwap(other);
}
void OneofWellKnownTypes::InternalSwap(OneofWellKnownTypes* other) {
  std::swap(oneof_field_, other->oneof_field_);
  std::swap(_oneof_case_[0], other->_oneof_case_[0]);
  _internal_metadata_.Swap(&other->_internal_metadata_);
  std::swap(_cached_size_, other->_cached_size_);
}

::google::protobuf::Metadata OneofWellKnownTypes::GetMetadata() const {
  protobuf_AssignDescriptorsOnce();
  ::google::protobuf::Metadata metadata;
  metadata.descriptor = OneofWellKnownTypes_descriptor_;
  metadata.reflection = OneofWellKnownTypes_reflection_;
  return metadata;
}

#if PROTOBUF_INLINE_NOT_IN_HEADERS
// OneofWellKnownTypes

// optional .google.protobuf.Any any_field = 1;
bool OneofWellKnownTypes::has_any_field() const {
  return oneof_field_case() == kAnyField;
}
void OneofWellKnownTypes::set_has_any_field() {
  _oneof_case_[0] = kAnyField;
}
void OneofWellKnownTypes::clear_any_field() {
  if (has_any_field()) {
    delete oneof_field_.any_field_;
    clear_has_oneof_field();
  }
}
 const ::google::protobuf::Any& OneofWellKnownTypes::any_field() const {
  // @@protoc_insertion_point(field_get:protobuf_unittest.OneofWellKnownTypes.any_field)
  return has_any_field()
      ? *oneof_field_.any_field_
      : ::google::protobuf::Any::default_instance();
}
::google::protobuf::Any* OneofWellKnownTypes::mutable_any_field() {
  if (!has_any_field()) {
    clear_oneof_field();
    set_has_any_field();
    oneof_field_.any_field_ = new ::google::protobuf::Any;
  }
  // @@protoc_insertion_point(field_mutable:protobuf_unittest.OneofWellKnownTypes.any_field)
  return oneof_field_.any_field_;
}
::google::protobuf::Any* OneofWellKnownTypes::release_any_field() {
  // @@protoc_insertion_point(field_release:protobuf_unittest.OneofWellKnownTypes.any_field)
  if (has_any_field()) {
    clear_has_oneof_field();
    ::google::protobuf::Any* temp = oneof_field_.any_field_;
    oneof_field_.any_field_ = NULL;
    return temp;
  } else {
    return NULL;
  }
}
void OneofWellKnownTypes::set_allocated_any_field(::google::protobuf::Any* any_field) {
  clear_oneof_field();
  if (any_field) {
    set_has_any_field();
    oneof_field_.any_field_ = any_field;
  }
  // @@protoc_insertion_point(field_set_allocated:protobuf_unittest.OneofWellKnownTypes.any_field)
}

// optional .google.protobuf.Api api_field = 2;
bool OneofWellKnownTypes::has_api_field() const {
  return oneof_field_case() == kApiField;
}
void OneofWellKnownTypes::set_has_api_field() {
  _oneof_case_[0] = kApiField;
}
void OneofWellKnownTypes::clear_api_field() {
  if (has_api_field()) {
    delete oneof_field_.api_field_;
    clear_has_oneof_field();
  }
}
 const ::google::protobuf::Api& OneofWellKnownTypes::api_field() const {
  // @@protoc_insertion_point(field_get:protobuf_unittest.OneofWellKnownTypes.api_field)
  return has_api_field()
      ? *oneof_field_.api_field_
      : ::google::protobuf::Api::default_instance();
}
::google::protobuf::Api* OneofWellKnownTypes::mutable_api_field() {
  if (!has_api_field()) {
    clear_oneof_field();
    set_has_api_field();
    oneof_field_.api_field_ = new ::google::protobuf::Api;
  }
  // @@protoc_insertion_point(field_mutable:protobuf_unittest.OneofWellKnownTypes.api_field)
  return oneof_field_.api_field_;
}
::google::protobuf::Api* OneofWellKnownTypes::release_api_field() {
  // @@protoc_insertion_point(field_release:protobuf_unittest.OneofWellKnownTypes.api_field)
  if (has_api_field()) {
    clear_has_oneof_field();
    ::google::protobuf::Api* temp = oneof_field_.api_field_;
    oneof_field_.api_field_ = NULL;
    return temp;
  } else {
    return NULL;
  }
}
void OneofWellKnownTypes::set_allocated_api_field(::google::protobuf::Api* api_field) {
  clear_oneof_field();
  if (api_field) {
    set_has_api_field();
    oneof_field_.api_field_ = api_field;
  }
  // @@protoc_insertion_point(field_set_allocated:protobuf_unittest.OneofWellKnownTypes.api_field)
}

// optional .google.protobuf.Duration duration_field = 3;
bool OneofWellKnownTypes::has_duration_field() const {
  return oneof_field_case() == kDurationField;
}
void OneofWellKnownTypes::set_has_duration_field() {
  _oneof_case_[0] = kDurationField;
}
void OneofWellKnownTypes::clear_duration_field() {
  if (has_duration_field()) {
    delete oneof_field_.duration_field_;
    clear_has_oneof_field();
  }
}
 const ::google::protobuf::Duration& OneofWellKnownTypes::duration_field() const {
  // @@protoc_insertion_point(field_get:protobuf_unittest.OneofWellKnownTypes.duration_field)
  return has_duration_field()
      ? *oneof_field_.duration_field_
      : ::google::protobuf::Duration::default_instance();
}
::google::protobuf::Duration* OneofWellKnownTypes::mutable_duration_field() {
  if (!has_duration_field()) {
    clear_oneof_field();
    set_has_duration_field();
    oneof_field_.duration_field_ = new ::google::protobuf::Duration;
  }
  // @@protoc_insertion_point(field_mutable:protobuf_unittest.OneofWellKnownTypes.duration_field)
  return oneof_field_.duration_field_;
}
::google::protobuf::Duration* OneofWellKnownTypes::release_duration_field() {
  // @@protoc_insertion_point(field_release:protobuf_unittest.OneofWellKnownTypes.duration_field)
  if (has_duration_field()) {
    clear_has_oneof_field();
    ::google::protobuf::Duration* temp = oneof_field_.duration_field_;
    oneof_field_.duration_field_ = NULL;
    return temp;
  } else {
    return NULL;
  }
}
void OneofWellKnownTypes::set_allocated_duration_field(::google::protobuf::Duration* duration_field) {
  clear_oneof_field();
  if (duration_field) {
    if (static_cast< ::google::protobuf::Duration*>(duration_field)->GetArena() != NULL) {
      ::google::protobuf::Duration* new_duration_field = new ::google::protobuf::Duration;
      new_duration_field->CopyFrom(*duration_field);
      duration_field = new_duration_field;
    }
    set_has_duration_field();
    oneof_field_.duration_field_ = duration_field;
  }
  // @@protoc_insertion_point(field_set_allocated:protobuf_unittest.OneofWellKnownTypes.duration_field)
}

// optional .google.protobuf.Empty empty_field = 4;
bool OneofWellKnownTypes::has_empty_field() const {
  return oneof_field_case() == kEmptyField;
}
void OneofWellKnownTypes::set_has_empty_field() {
  _oneof_case_[0] = kEmptyField;
}
void OneofWellKnownTypes::clear_empty_field() {
  if (has_empty_field()) {
    delete oneof_field_.empty_field_;
    clear_has_oneof_field();
  }
}
 const ::google::protobuf::Empty& OneofWellKnownTypes::empty_field() const {
  // @@protoc_insertion_point(field_get:protobuf_unittest.OneofWellKnownTypes.empty_field)
  return has_empty_field()
      ? *oneof_field_.empty_field_
      : ::google::protobuf::Empty::default_instance();
}
::google::protobuf::Empty* OneofWellKnownTypes::mutable_empty_field() {
  if (!has_empty_field()) {
    clear_oneof_field();
    set_has_empty_field();
    oneof_field_.empty_field_ = new ::google::protobuf::Empty;
  }
  // @@protoc_insertion_point(field_mutable:protobuf_unittest.OneofWellKnownTypes.empty_field)
  return oneof_field_.empty_field_;
}
::google::protobuf::Empty* OneofWellKnownTypes::release_empty_field() {
  // @@protoc_insertion_point(field_release:protobuf_unittest.OneofWellKnownTypes.empty_field)
  if (has_empty_field()) {
    clear_has_oneof_field();
    ::google::protobuf::Empty* temp = oneof_field_.empty_field_;
    oneof_field_.empty_field_ = NULL;
    return temp;
  } else {
    return NULL;
  }
}
void OneofWellKnownTypes::set_allocated_empty_field(::google::protobuf::Empty* empty_field) {
  clear_oneof_field();
  if (empty_field) {
    if (static_cast< ::google::protobuf::Empty*>(empty_field)->GetArena() != NULL) {
      ::google::protobuf::Empty* new_empty_field = new ::google::protobuf::Empty;
      new_empty_field->CopyFrom(*empty_field);
      empty_field = new_empty_field;
    }
    set_has_empty_field();
    oneof_field_.empty_field_ = empty_field;
  }
  // @@protoc_insertion_point(field_set_allocated:protobuf_unittest.OneofWellKnownTypes.empty_field)
}

// optional .google.protobuf.FieldMask field_mask_field = 5;
bool OneofWellKnownTypes::has_field_mask_field() const {
  return oneof_field_case() == kFieldMaskField;
}
void OneofWellKnownTypes::set_has_field_mask_field() {
  _oneof_case_[0] = kFieldMaskField;
}
void OneofWellKnownTypes::clear_field_mask_field() {
  if (has_field_mask_field()) {
    delete oneof_field_.field_mask_field_;
    clear_has_oneof_field();
  }
}
 const ::google::protobuf::FieldMask& OneofWellKnownTypes::field_mask_field() const {
  // @@protoc_insertion_point(field_get:protobuf_unittest.OneofWellKnownTypes.field_mask_field)
  return has_field_mask_field()
      ? *oneof_field_.field_mask_field_
      : ::google::protobuf::FieldMask::default_instance();
}
::google::protobuf::FieldMask* OneofWellKnownTypes::mutable_field_mask_field() {
  if (!has_field_mask_field()) {
    clear_oneof_field();
    set_has_field_mask_field();
    oneof_field_.field_mask_field_ = new ::google::protobuf::FieldMask;
  }
  // @@protoc_insertion_point(field_mutable:protobuf_unittest.OneofWellKnownTypes.field_mask_field)
  return oneof_field_.field_mask_field_;
}
::google::protobuf::FieldMask* OneofWellKnownTypes::release_field_mask_field() {
  // @@protoc_insertion_point(field_release:protobuf_unittest.OneofWellKnownTypes.field_mask_field)
  if (has_field_mask_field()) {
    clear_has_oneof_field();
    ::google::protobuf::FieldMask* temp = oneof_field_.field_mask_field_;
    oneof_field_.field_mask_field_ = NULL;
    return temp;
  } else {
    return NULL;
  }
}
void OneofWellKnownTypes::set_allocated_field_mask_field(::google::protobuf::FieldMask* field_mask_field) {
  clear_oneof_field();
  if (field_mask_field) {
    set_has_field_mask_field();
    oneof_field_.field_mask_field_ = field_mask_field;
  }
  // @@protoc_insertion_point(field_set_allocated:protobuf_unittest.OneofWellKnownTypes.field_mask_field)
}

// optional .google.protobuf.SourceContext source_context_field = 6;
bool OneofWellKnownTypes::has_source_context_field() const {
  return oneof_field_case() == kSourceContextField;
}
void OneofWellKnownTypes::set_has_source_context_field() {
  _oneof_case_[0] = kSourceContextField;
}
void OneofWellKnownTypes::clear_source_context_field() {
  if (has_source_context_field()) {
    delete oneof_field_.source_context_field_;
    clear_has_oneof_field();
  }
}
 const ::google::protobuf::SourceContext& OneofWellKnownTypes::source_context_field() const {
  // @@protoc_insertion_point(field_get:protobuf_unittest.OneofWellKnownTypes.source_context_field)
  return has_source_context_field()
      ? *oneof_field_.source_context_field_
      : ::google::protobuf::SourceContext::default_instance();
}
::google::protobuf::SourceContext* OneofWellKnownTypes::mutable_source_context_field() {
  if (!has_source_context_field()) {
    clear_oneof_field();
    set_has_source_context_field();
    oneof_field_.source_context_field_ = new ::google::protobuf::SourceContext;
  }
  // @@protoc_insertion_point(field_mutable:protobuf_unittest.OneofWellKnownTypes.source_context_field)
  return oneof_field_.source_context_field_;
}
::google::protobuf::SourceContext* OneofWellKnownTypes::release_source_context_field() {
  // @@protoc_insertion_point(field_release:protobuf_unittest.OneofWellKnownTypes.source_context_field)
  if (has_source_context_field()) {
    clear_has_oneof_field();
    ::google::protobuf::SourceContext* temp = oneof_field_.source_context_field_;
    oneof_field_.source_context_field_ = NULL;
    return temp;
  } else {
    return NULL;
  }
}
void OneofWellKnownTypes::set_allocated_source_context_field(::google::protobuf::SourceContext* source_context_field) {
  clear_oneof_field();
  if (source_context_field) {
    set_has_source_context_field();
    oneof_field_.source_context_field_ = source_context_field;
  }
  // @@protoc_insertion_point(field_set_allocated:protobuf_unittest.OneofWellKnownTypes.source_context_field)
}

// optional .google.protobuf.Struct struct_field = 7;
bool OneofWellKnownTypes::has_struct_field() const {
  return oneof_field_case() == kStructField;
}
void OneofWellKnownTypes::set_has_struct_field() {
  _oneof_case_[0] = kStructField;
}
void OneofWellKnownTypes::clear_struct_field() {
  if (has_struct_field()) {
    delete oneof_field_.struct_field_;
    clear_has_oneof_field();
  }
}
 const ::google::protobuf::Struct& OneofWellKnownTypes::struct_field() const {
  // @@protoc_insertion_point(field_get:protobuf_unittest.OneofWellKnownTypes.struct_field)
  return has_struct_field()
      ? *oneof_field_.struct_field_
      : ::google::protobuf::Struct::default_instance();
}
::google::protobuf::Struct* OneofWellKnownTypes::mutable_struct_field() {
  if (!has_struct_field()) {
    clear_oneof_field();
    set_has_struct_field();
    oneof_field_.struct_field_ = new ::google::protobuf::Struct;
  }
  // @@protoc_insertion_point(field_mutable:protobuf_unittest.OneofWellKnownTypes.struct_field)
  return oneof_field_.struct_field_;
}
::google::protobuf::Struct* OneofWellKnownTypes::release_struct_field() {
  // @@protoc_insertion_point(field_release:protobuf_unittest.OneofWellKnownTypes.struct_field)
  if (has_struct_field()) {
    clear_has_oneof_field();
    ::google::protobuf::Struct* temp = oneof_field_.struct_field_;
    oneof_field_.struct_field_ = NULL;
    return temp;
  } else {
    return NULL;
  }
}
void OneofWellKnownTypes::set_allocated_struct_field(::google::protobuf::Struct* struct_field) {
  clear_oneof_field();
  if (struct_field) {
    if (static_cast< ::google::protobuf::Struct*>(struct_field)->GetArena() != NULL) {
      ::google::protobuf::Struct* new_struct_field = new ::google::protobuf::Struct;
      new_struct_field->CopyFrom(*struct_field);
      struct_field = new_struct_field;
    }
    set_has_struct_field();
    oneof_field_.struct_field_ = struct_field;
  }
  // @@protoc_insertion_point(field_set_allocated:protobuf_unittest.OneofWellKnownTypes.struct_field)
}

// optional .google.protobuf.Timestamp timestamp_field = 8;
bool OneofWellKnownTypes::has_timestamp_field() const {
  return oneof_field_case() == kTimestampField;
}
void OneofWellKnownTypes::set_has_timestamp_field() {
  _oneof_case_[0] = kTimestampField;
}
void OneofWellKnownTypes::clear_timestamp_field() {
  if (has_timestamp_field()) {
    delete oneof_field_.timestamp_field_;
    clear_has_oneof_field();
  }
}
 const ::google::protobuf::Timestamp& OneofWellKnownTypes::timestamp_field() const {
  // @@protoc_insertion_point(field_get:protobuf_unittest.OneofWellKnownTypes.timestamp_field)
  return has_timestamp_field()
      ? *oneof_field_.timestamp_field_
      : ::google::protobuf::Timestamp::default_instance();
}
::google::protobuf::Timestamp* OneofWellKnownTypes::mutable_timestamp_field() {
  if (!has_timestamp_field()) {
    clear_oneof_field();
    set_has_timestamp_field();
    oneof_field_.timestamp_field_ = new ::google::protobuf::Timestamp;
  }
  // @@protoc_insertion_point(field_mutable:protobuf_unittest.OneofWellKnownTypes.timestamp_field)
  return oneof_field_.timestamp_field_;
}
::google::protobuf::Timestamp* OneofWellKnownTypes::release_timestamp_field() {
  // @@protoc_insertion_point(field_release:protobuf_unittest.OneofWellKnownTypes.timestamp_field)
  if (has_timestamp_field()) {
    clear_has_oneof_field();
    ::google::protobuf::Timestamp* temp = oneof_field_.timestamp_field_;
    oneof_field_.timestamp_field_ = NULL;
    return temp;
  } else {
    return NULL;
  }
}
void OneofWellKnownTypes::set_allocated_timestamp_field(::google::protobuf::Timestamp* timestamp_field) {
  clear_oneof_field();
  if (timestamp_field) {
    if (static_cast< ::google::protobuf::Timestamp*>(timestamp_field)->GetArena() != NULL) {
      ::google::protobuf::Timestamp* new_timestamp_field = new ::google::protobuf::Timestamp;
      new_timestamp_field->CopyFrom(*timestamp_field);
      timestamp_field = new_timestamp_field;
    }
    set_has_timestamp_field();
    oneof_field_.timestamp_field_ = timestamp_field;
  }
  // @@protoc_insertion_point(field_set_allocated:protobuf_unittest.OneofWellKnownTypes.timestamp_field)
}

// optional .google.protobuf.Type type_field = 9;
bool OneofWellKnownTypes::has_type_field() const {
  return oneof_field_case() == kTypeField;
}
void OneofWellKnownTypes::set_has_type_field() {
  _oneof_case_[0] = kTypeField;
}
void OneofWellKnownTypes::clear_type_field() {
  if (has_type_field()) {
    delete oneof_field_.type_field_;
    clear_has_oneof_field();
  }
}
 const ::google::protobuf::Type& OneofWellKnownTypes::type_field() const {
  // @@protoc_insertion_point(field_get:protobuf_unittest.OneofWellKnownTypes.type_field)
  return has_type_field()
      ? *oneof_field_.type_field_
      : ::google::protobuf::Type::default_instance();
}
::google::protobuf::Type* OneofWellKnownTypes::mutable_type_field() {
  if (!has_type_field()) {
    clear_oneof_field();
    set_has_type_field();
    oneof_field_.type_field_ = new ::google::protobuf::Type;
  }
  // @@protoc_insertion_point(field_mutable:protobuf_unittest.OneofWellKnownTypes.type_field)
  return oneof_field_.type_field_;
}
::google::protobuf::Type* OneofWellKnownTypes::release_type_field() {
  // @@protoc_insertion_point(field_release:protobuf_unittest.OneofWellKnownTypes.type_field)
  if (has_type_field()) {
    clear_has_oneof_field();
    ::google::protobuf::Type* temp = oneof_field_.type_field_;
    oneof_field_.type_field_ = NULL;
    return temp;
  } else {
    return NULL;
  }
}
void OneofWellKnownTypes::set_allocated_type_field(::google::protobuf::Type* type_field) {
  clear_oneof_field();
  if (type_field) {
    if (static_cast< ::google::protobuf::Type*>(type_field)->GetArena() != NULL) {
      ::google::protobuf::Type* new_type_field = new ::google::protobuf::Type;
      new_type_field->CopyFrom(*type_field);
      type_field = new_type_field;
    }
    set_has_type_field();
    oneof_field_.type_field_ = type_field;
  }
  // @@protoc_insertion_point(field_set_allocated:protobuf_unittest.OneofWellKnownTypes.type_field)
}

// optional .google.protobuf.DoubleValue double_field = 10;
bool OneofWellKnownTypes::has_double_field() const {
  return oneof_field_case() == kDoubleField;
}
void OneofWellKnownTypes::set_has_double_field() {
  _oneof_case_[0] = kDoubleField;
}
void OneofWellKnownTypes::clear_double_field() {
  if (has_double_field()) {
    delete oneof_field_.double_field_;
    clear_has_oneof_field();
  }
}
 const ::google::protobuf::DoubleValue& OneofWellKnownTypes::double_field() const {
  // @@protoc_insertion_point(field_get:protobuf_unittest.OneofWellKnownTypes.double_field)
  return has_double_field()
      ? *oneof_field_.double_field_
      : ::google::protobuf::DoubleValue::default_instance();
}
::google::protobuf::DoubleValue* OneofWellKnownTypes::mutable_double_field() {
  if (!has_double_field()) {
    clear_oneof_field();
    set_has_double_field();
    oneof_field_.double_field_ = new ::google::protobuf::DoubleValue;
  }
  // @@protoc_insertion_point(field_mutable:protobuf_unittest.OneofWellKnownTypes.double_field)
  return oneof_field_.double_field_;
}
::google::protobuf::DoubleValue* OneofWellKnownTypes::release_double_field() {
  // @@protoc_insertion_point(field_release:protobuf_unittest.OneofWellKnownTypes.double_field)
  if (has_double_field()) {
    clear_has_oneof_field();
    ::google::protobuf::DoubleValue* temp = oneof_field_.double_field_;
    oneof_field_.double_field_ = NULL;
    return temp;
  } else {
    return NULL;
  }
}
void OneofWellKnownTypes::set_allocated_double_field(::google::protobuf::DoubleValue* double_field) {
  clear_oneof_field();
  if (double_field) {
    if (static_cast< ::google::protobuf::DoubleValue*>(double_field)->GetArena() != NULL) {
      ::google::protobuf::DoubleValue* new_double_field = new ::google::protobuf::DoubleValue;
      new_double_field->CopyFrom(*double_field);
      double_field = new_double_field;
    }
    set_has_double_field();
    oneof_field_.double_field_ = double_field;
  }
  // @@protoc_insertion_point(field_set_allocated:protobuf_unittest.OneofWellKnownTypes.double_field)
}

// optional .google.protobuf.FloatValue float_field = 11;
bool OneofWellKnownTypes::has_float_field() const {
  return oneof_field_case() == kFloatField;
}
void OneofWellKnownTypes::set_has_float_field() {
  _oneof_case_[0] = kFloatField;
}
void OneofWellKnownTypes::clear_float_field() {
  if (has_float_field()) {
    delete oneof_field_.float_field_;
    clear_has_oneof_field();
  }
}
 const ::google::protobuf::FloatValue& OneofWellKnownTypes::float_field() const {
  // @@protoc_insertion_point(field_get:protobuf_unittest.OneofWellKnownTypes.float_field)
  return has_float_field()
      ? *oneof_field_.float_field_
      : ::google::protobuf::FloatValue::default_instance();
}
::google::protobuf::FloatValue* OneofWellKnownTypes::mutable_float_field() {
  if (!has_float_field()) {
    clear_oneof_field();
    set_has_float_field();
    oneof_field_.float_field_ = new ::google::protobuf::FloatValue;
  }
  // @@protoc_insertion_point(field_mutable:protobuf_unittest.OneofWellKnownTypes.float_field)
  return oneof_field_.float_field_;
}
::google::protobuf::FloatValue* OneofWellKnownTypes::release_float_field() {
  // @@protoc_insertion_point(field_release:protobuf_unittest.OneofWellKnownTypes.float_field)
  if (has_float_field()) {
    clear_has_oneof_field();
    ::google::protobuf::FloatValue* temp = oneof_field_.float_field_;
    oneof_field_.float_field_ = NULL;
    return temp;
  } else {
    return NULL;
  }
}
void OneofWellKnownTypes::set_allocated_float_field(::google::protobuf::FloatValue* float_field) {
  clear_oneof_field();
  if (float_field) {
    if (static_cast< ::google::protobuf::FloatValue*>(float_field)->GetArena() != NULL) {
      ::google::protobuf::FloatValue* new_float_field = new ::google::protobuf::FloatValue;
      new_float_field->CopyFrom(*float_field);
      float_field = new_float_field;
    }
    set_has_float_field();
    oneof_field_.float_field_ = float_field;
  }
  // @@protoc_insertion_point(field_set_allocated:protobuf_unittest.OneofWellKnownTypes.float_field)
}

// optional .google.protobuf.Int64Value int64_field = 12;
bool OneofWellKnownTypes::has_int64_field() const {
  return oneof_field_case() == kInt64Field;
}
void OneofWellKnownTypes::set_has_int64_field() {
  _oneof_case_[0] = kInt64Field;
}
void OneofWellKnownTypes::clear_int64_field() {
  if (has_int64_field()) {
    delete oneof_field_.int64_field_;
    clear_has_oneof_field();
  }
}
 const ::google::protobuf::Int64Value& OneofWellKnownTypes::int64_field() const {
  // @@protoc_insertion_point(field_get:protobuf_unittest.OneofWellKnownTypes.int64_field)
  return has_int64_field()
      ? *oneof_field_.int64_field_
      : ::google::protobuf::Int64Value::default_instance();
}
::google::protobuf::Int64Value* OneofWellKnownTypes::mutable_int64_field() {
  if (!has_int64_field()) {
    clear_oneof_field();
    set_has_int64_field();
    oneof_field_.int64_field_ = new ::google::protobuf::Int64Value;
  }
  // @@protoc_insertion_point(field_mutable:protobuf_unittest.OneofWellKnownTypes.int64_field)
  return oneof_field_.int64_field_;
}
::google::protobuf::Int64Value* OneofWellKnownTypes::release_int64_field() {
  // @@protoc_insertion_point(field_release:protobuf_unittest.OneofWellKnownTypes.int64_field)
  if (has_int64_field()) {
    clear_has_oneof_field();
    ::google::protobuf::Int64Value* temp = oneof_field_.int64_field_;
    oneof_field_.int64_field_ = NULL;
    return temp;
  } else {
    return NULL;
  }
}
void OneofWellKnownTypes::set_allocated_int64_field(::google::protobuf::Int64Value* int64_field) {
  clear_oneof_field();
  if (int64_field) {
    if (static_cast< ::google::protobuf::Int64Value*>(int64_field)->GetArena() != NULL) {
      ::google::protobuf::Int64Value* new_int64_field = new ::google::protobuf::Int64Value;
      new_int64_field->CopyFrom(*int64_field);
      int64_field = new_int64_field;
    }
    set_has_int64_field();
    oneof_field_.int64_field_ = int64_field;
  }
  // @@protoc_insertion_point(field_set_allocated:protobuf_unittest.OneofWellKnownTypes.int64_field)
}

// optional .google.protobuf.UInt64Value uint64_field = 13;
bool OneofWellKnownTypes::has_uint64_field() const {
  return oneof_field_case() == kUint64Field;
}
void OneofWellKnownTypes::set_has_uint64_field() {
  _oneof_case_[0] = kUint64Field;
}
void OneofWellKnownTypes::clear_uint64_field() {
  if (has_uint64_field()) {
    delete oneof_field_.uint64_field_;
    clear_has_oneof_field();
  }
}
 const ::google::protobuf::UInt64Value& OneofWellKnownTypes::uint64_field() const {
  // @@protoc_insertion_point(field_get:protobuf_unittest.OneofWellKnownTypes.uint64_field)
  return has_uint64_field()
      ? *oneof_field_.uint64_field_
      : ::google::protobuf::UInt64Value::default_instance();
}
::google::protobuf::UInt64Value* OneofWellKnownTypes::mutable_uint64_field() {
  if (!has_uint64_field()) {
    clear_oneof_field();
    set_has_uint64_field();
    oneof_field_.uint64_field_ = new ::google::protobuf::UInt64Value;
  }
  // @@protoc_insertion_point(field_mutable:protobuf_unittest.OneofWellKnownTypes.uint64_field)
  return oneof_field_.uint64_field_;
}
::google::protobuf::UInt64Value* OneofWellKnownTypes::release_uint64_field() {
  // @@protoc_insertion_point(field_release:protobuf_unittest.OneofWellKnownTypes.uint64_field)
  if (has_uint64_field()) {
    clear_has_oneof_field();
    ::google::protobuf::UInt64Value* temp = oneof_field_.uint64_field_;
    oneof_field_.uint64_field_ = NULL;
    return temp;
  } else {
    return NULL;
  }
}
void OneofWellKnownTypes::set_allocated_uint64_field(::google::protobuf::UInt64Value* uint64_field) {
  clear_oneof_field();
  if (uint64_field) {
    if (static_cast< ::google::protobuf::UInt64Value*>(uint64_field)->GetArena() != NULL) {
      ::google::protobuf::UInt64Value* new_uint64_field = new ::google::protobuf::UInt64Value;
      new_uint64_field->CopyFrom(*uint64_field);
      uint64_field = new_uint64_field;
    }
    set_has_uint64_field();
    oneof_field_.uint64_field_ = uint64_field;
  }
  // @@protoc_insertion_point(field_set_allocated:protobuf_unittest.OneofWellKnownTypes.uint64_field)
}

// optional .google.protobuf.Int32Value int32_field = 14;
bool OneofWellKnownTypes::has_int32_field() const {
  return oneof_field_case() == kInt32Field;
}
void OneofWellKnownTypes::set_has_int32_field() {
  _oneof_case_[0] = kInt32Field;
}
void OneofWellKnownTypes::clear_int32_field() {
  if (has_int32_field()) {
    delete oneof_field_.int32_field_;
    clear_has_oneof_field();
  }
}
 const ::google::protobuf::Int32Value& OneofWellKnownTypes::int32_field() const {
  // @@protoc_insertion_point(field_get:protobuf_unittest.OneofWellKnownTypes.int32_field)
  return has_int32_field()
      ? *oneof_field_.int32_field_
      : ::google::protobuf::Int32Value::default_instance();
}
::google::protobuf::Int32Value* OneofWellKnownTypes::mutable_int32_field() {
  if (!has_int32_field()) {
    clear_oneof_field();
    set_has_int32_field();
    oneof_field_.int32_field_ = new ::google::protobuf::Int32Value;
  }
  // @@protoc_insertion_point(field_mutable:protobuf_unittest.OneofWellKnownTypes.int32_field)
  return oneof_field_.int32_field_;
}
::google::protobuf::Int32Value* OneofWellKnownTypes::release_int32_field() {
  // @@protoc_insertion_point(field_release:protobuf_unittest.OneofWellKnownTypes.int32_field)
  if (has_int32_field()) {
    clear_has_oneof_field();
    ::google::protobuf::Int32Value* temp = oneof_field_.int32_field_;
    oneof_field_.int32_field_ = NULL;
    return temp;
  } else {
    return NULL;
  }
}
void OneofWellKnownTypes::set_allocated_int32_field(::google::protobuf::Int32Value* int32_field) {
  clear_oneof_field();
  if (int32_field) {
    if (static_cast< ::google::protobuf::Int32Value*>(int32_field)->GetArena() != NULL) {
      ::google::protobuf::Int32Value* new_int32_field = new ::google::protobuf::Int32Value;
      new_int32_field->CopyFrom(*int32_field);
      int32_field = new_int32_field;
    }
    set_has_int32_field();
    oneof_field_.int32_field_ = int32_field;
  }
  // @@protoc_insertion_point(field_set_allocated:protobuf_unittest.OneofWellKnownTypes.int32_field)
}

// optional .google.protobuf.UInt32Value uint32_field = 15;
bool OneofWellKnownTypes::has_uint32_field() const {
  return oneof_field_case() == kUint32Field;
}
void OneofWellKnownTypes::set_has_uint32_field() {
  _oneof_case_[0] = kUint32Field;
}
void OneofWellKnownTypes::clear_uint32_field() {
  if (has_uint32_field()) {
    delete oneof_field_.uint32_field_;
    clear_has_oneof_field();
  }
}
 const ::google::protobuf::UInt32Value& OneofWellKnownTypes::uint32_field() const {
  // @@protoc_insertion_point(field_get:protobuf_unittest.OneofWellKnownTypes.uint32_field)
  return has_uint32_field()
      ? *oneof_field_.uint32_field_
      : ::google::protobuf::UInt32Value::default_instance();
}
::google::protobuf::UInt32Value* OneofWellKnownTypes::mutable_uint32_field() {
  if (!has_uint32_field()) {
    clear_oneof_field();
    set_has_uint32_field();
    oneof_field_.uint32_field_ = new ::google::protobuf::UInt32Value;
  }
  // @@protoc_insertion_point(field_mutable:protobuf_unittest.OneofWellKnownTypes.uint32_field)
  return oneof_field_.uint32_field_;
}
::google::protobuf::UInt32Value* OneofWellKnownTypes::release_uint32_field() {
  // @@protoc_insertion_point(field_release:protobuf_unittest.OneofWellKnownTypes.uint32_field)
  if (has_uint32_field()) {
    clear_has_oneof_field();
    ::google::protobuf::UInt32Value* temp = oneof_field_.uint32_field_;
    oneof_field_.uint32_field_ = NULL;
    return temp;
  } else {
    return NULL;
  }
}
void OneofWellKnownTypes::set_allocated_uint32_field(::google::protobuf::UInt32Value* uint32_field) {
  clear_oneof_field();
  if (uint32_field) {
    if (static_cast< ::google::protobuf::UInt32Value*>(uint32_field)->GetArena() != NULL) {
      ::google::protobuf::UInt32Value* new_uint32_field = new ::google::protobuf::UInt32Value;
      new_uint32_field->CopyFrom(*uint32_field);
      uint32_field = new_uint32_field;
    }
    set_has_uint32_field();
    oneof_field_.uint32_field_ = uint32_field;
  }
  // @@protoc_insertion_point(field_set_allocated:protobuf_unittest.OneofWellKnownTypes.uint32_field)
}

// optional .google.protobuf.BoolValue bool_field = 16;
bool OneofWellKnownTypes::has_bool_field() const {
  return oneof_field_case() == kBoolField;
}
void OneofWellKnownTypes::set_has_bool_field() {
  _oneof_case_[0] = kBoolField;
}
void OneofWellKnownTypes::clear_bool_field() {
  if (has_bool_field()) {
    delete oneof_field_.bool_field_;
    clear_has_oneof_field();
  }
}
 const ::google::protobuf::BoolValue& OneofWellKnownTypes::bool_field() const {
  // @@protoc_insertion_point(field_get:protobuf_unittest.OneofWellKnownTypes.bool_field)
  return has_bool_field()
      ? *oneof_field_.bool_field_
      : ::google::protobuf::BoolValue::default_instance();
}
::google::protobuf::BoolValue* OneofWellKnownTypes::mutable_bool_field() {
  if (!has_bool_field()) {
    clear_oneof_field();
    set_has_bool_field();
    oneof_field_.bool_field_ = new ::google::protobuf::BoolValue;
  }
  // @@protoc_insertion_point(field_mutable:protobuf_unittest.OneofWellKnownTypes.bool_field)
  return oneof_field_.bool_field_;
}
::google::protobuf::BoolValue* OneofWellKnownTypes::release_bool_field() {
  // @@protoc_insertion_point(field_release:protobuf_unittest.OneofWellKnownTypes.bool_field)
  if (has_bool_field()) {
    clear_has_oneof_field();
    ::google::protobuf::BoolValue* temp = oneof_field_.bool_field_;
    oneof_field_.bool_field_ = NULL;
    return temp;
  } else {
    return NULL;
  }
}
void OneofWellKnownTypes::set_allocated_bool_field(::google::protobuf::BoolValue* bool_field) {
  clear_oneof_field();
  if (bool_field) {
    if (static_cast< ::google::protobuf::BoolValue*>(bool_field)->GetArena() != NULL) {
      ::google::protobuf::BoolValue* new_bool_field = new ::google::protobuf::BoolValue;
      new_bool_field->CopyFrom(*bool_field);
      bool_field = new_bool_field;
    }
    set_has_bool_field();
    oneof_field_.bool_field_ = bool_field;
  }
  // @@protoc_insertion_point(field_set_allocated:protobuf_unittest.OneofWellKnownTypes.bool_field)
}

// optional .google.protobuf.StringValue string_field = 17;
bool OneofWellKnownTypes::has_string_field() const {
  return oneof_field_case() == kStringField;
}
void OneofWellKnownTypes::set_has_string_field() {
  _oneof_case_[0] = kStringField;
}
void OneofWellKnownTypes::clear_string_field() {
  if (has_string_field()) {
    delete oneof_field_.string_field_;
    clear_has_oneof_field();
  }
}
 const ::google::protobuf::StringValue& OneofWellKnownTypes::string_field() const {
  // @@protoc_insertion_point(field_get:protobuf_unittest.OneofWellKnownTypes.string_field)
  return has_string_field()
      ? *oneof_field_.string_field_
      : ::google::protobuf::StringValue::default_instance();
}
::google::protobuf::StringValue* OneofWellKnownTypes::mutable_string_field() {
  if (!has_string_field()) {
    clear_oneof_field();
    set_has_string_field();
    oneof_field_.string_field_ = new ::google::protobuf::StringValue;
  }
  // @@protoc_insertion_point(field_mutable:protobuf_unittest.OneofWellKnownTypes.string_field)
  return oneof_field_.string_field_;
}
::google::protobuf::StringValue* OneofWellKnownTypes::release_string_field() {
  // @@protoc_insertion_point(field_release:protobuf_unittest.OneofWellKnownTypes.string_field)
  if (has_string_field()) {
    clear_has_oneof_field();
    ::google::protobuf::StringValue* temp = oneof_field_.string_field_;
    oneof_field_.string_field_ = NULL;
    return temp;
  } else {
    return NULL;
  }
}
void OneofWellKnownTypes::set_allocated_string_field(::google::protobuf::StringValue* string_field) {
  clear_oneof_field();
  if (string_field) {
    if (static_cast< ::google::protobuf::StringValue*>(string_field)->GetArena() != NULL) {
      ::google::protobuf::StringValue* new_string_field = new ::google::protobuf::StringValue;
      new_string_field->CopyFrom(*string_field);
      string_field = new_string_field;
    }
    set_has_string_field();
    oneof_field_.string_field_ = string_field;
  }
  // @@protoc_insertion_point(field_set_allocated:protobuf_unittest.OneofWellKnownTypes.string_field)
}

// optional .google.protobuf.BytesValue bytes_field = 18;
bool OneofWellKnownTypes::has_bytes_field() const {
  return oneof_field_case() == kBytesField;
}
void OneofWellKnownTypes::set_has_bytes_field() {
  _oneof_case_[0] = kBytesField;
}
void OneofWellKnownTypes::clear_bytes_field() {
  if (has_bytes_field()) {
    delete oneof_field_.bytes_field_;
    clear_has_oneof_field();
  }
}
 const ::google::protobuf::BytesValue& OneofWellKnownTypes::bytes_field() const {
  // @@protoc_insertion_point(field_get:protobuf_unittest.OneofWellKnownTypes.bytes_field)
  return has_bytes_field()
      ? *oneof_field_.bytes_field_
      : ::google::protobuf::BytesValue::default_instance();
}
::google::protobuf::BytesValue* OneofWellKnownTypes::mutable_bytes_field() {
  if (!has_bytes_field()) {
    clear_oneof_field();
    set_has_bytes_field();
    oneof_field_.bytes_field_ = new ::google::protobuf::BytesValue;
  }
  // @@protoc_insertion_point(field_mutable:protobuf_unittest.OneofWellKnownTypes.bytes_field)
  return oneof_field_.bytes_field_;
}
::google::protobuf::BytesValue* OneofWellKnownTypes::release_bytes_field() {
  // @@protoc_insertion_point(field_release:protobuf_unittest.OneofWellKnownTypes.bytes_field)
  if (has_bytes_field()) {
    clear_has_oneof_field();
    ::google::protobuf::BytesValue* temp = oneof_field_.bytes_field_;
    oneof_field_.bytes_field_ = NULL;
    return temp;
  } else {
    return NULL;
  }
}
void OneofWellKnownTypes::set_allocated_bytes_field(::google::protobuf::BytesValue* bytes_field) {
  clear_oneof_field();
  if (bytes_field) {
    if (static_cast< ::google::protobuf::BytesValue*>(bytes_field)->GetArena() != NULL) {
      ::google::protobuf::BytesValue* new_bytes_field = new ::google::protobuf::BytesValue;
      new_bytes_field->CopyFrom(*bytes_field);
      bytes_field = new_bytes_field;
    }
    set_has_bytes_field();
    oneof_field_.bytes_field_ = bytes_field;
  }
  // @@protoc_insertion_point(field_set_allocated:protobuf_unittest.OneofWellKnownTypes.bytes_field)
}

bool OneofWellKnownTypes::has_oneof_field() const {
  return oneof_field_case() != ONEOF_FIELD_NOT_SET;
}
void OneofWellKnownTypes::clear_has_oneof_field() {
  _oneof_case_[0] = ONEOF_FIELD_NOT_SET;
}
OneofWellKnownTypes::OneofFieldCase OneofWellKnownTypes::oneof_field_case() const {
  return OneofWellKnownTypes::OneofFieldCase(_oneof_case_[0]);
}
inline const OneofWellKnownTypes* OneofWellKnownTypes::internal_default_instance() {
  return &OneofWellKnownTypes_default_instance_.get();
}
#endif  // PROTOBUF_INLINE_NOT_IN_HEADERS

// ===================================================================

#if !defined(_MSC_VER) || _MSC_VER >= 1900
const int MapWellKnownTypes::kAnyFieldFieldNumber;
const int MapWellKnownTypes::kApiFieldFieldNumber;
const int MapWellKnownTypes::kDurationFieldFieldNumber;
const int MapWellKnownTypes::kEmptyFieldFieldNumber;
const int MapWellKnownTypes::kFieldMaskFieldFieldNumber;
const int MapWellKnownTypes::kSourceContextFieldFieldNumber;
const int MapWellKnownTypes::kStructFieldFieldNumber;
const int MapWellKnownTypes::kTimestampFieldFieldNumber;
const int MapWellKnownTypes::kTypeFieldFieldNumber;
const int MapWellKnownTypes::kDoubleFieldFieldNumber;
const int MapWellKnownTypes::kFloatFieldFieldNumber;
const int MapWellKnownTypes::kInt64FieldFieldNumber;
const int MapWellKnownTypes::kUint64FieldFieldNumber;
const int MapWellKnownTypes::kInt32FieldFieldNumber;
const int MapWellKnownTypes::kUint32FieldFieldNumber;
const int MapWellKnownTypes::kBoolFieldFieldNumber;
const int MapWellKnownTypes::kStringFieldFieldNumber;
const int MapWellKnownTypes::kBytesFieldFieldNumber;
#endif  // !defined(_MSC_VER) || _MSC_VER >= 1900

MapWellKnownTypes::MapWellKnownTypes()
  : ::google::protobuf::Message(), _internal_metadata_(NULL) {
  if (this != internal_default_instance()) protobuf_InitDefaults_google_2fprotobuf_2funittest_5fwell_5fknown_5ftypes_2eproto();
  SharedCtor();
  // @@protoc_insertion_point(constructor:protobuf_unittest.MapWellKnownTypes)
}

void MapWellKnownTypes::InitAsDefaultInstance() {
}

MapWellKnownTypes::MapWellKnownTypes(const MapWellKnownTypes& from)
  : ::google::protobuf::Message(),
    _internal_metadata_(NULL) {
  SharedCtor();
  UnsafeMergeFrom(from);
  // @@protoc_insertion_point(copy_constructor:protobuf_unittest.MapWellKnownTypes)
}

void MapWellKnownTypes::SharedCtor() {
  any_field_.SetAssignDescriptorCallback(
      protobuf_AssignDescriptorsOnce);
  any_field_.SetEntryDescriptor(
      &::protobuf_unittest::MapWellKnownTypes_AnyFieldEntry_descriptor_);
  api_field_.SetAssignDescriptorCallback(
      protobuf_AssignDescriptorsOnce);
  api_field_.SetEntryDescriptor(
      &::protobuf_unittest::MapWellKnownTypes_ApiFieldEntry_descriptor_);
  duration_field_.SetAssignDescriptorCallback(
      protobuf_AssignDescriptorsOnce);
  duration_field_.SetEntryDescriptor(
      &::protobuf_unittest::MapWellKnownTypes_DurationFieldEntry_descriptor_);
  empty_field_.SetAssignDescriptorCallback(
      protobuf_AssignDescriptorsOnce);
  empty_field_.SetEntryDescriptor(
      &::protobuf_unittest::MapWellKnownTypes_EmptyFieldEntry_descriptor_);
  field_mask_field_.SetAssignDescriptorCallback(
      protobuf_AssignDescriptorsOnce);
  field_mask_field_.SetEntryDescriptor(
      &::protobuf_unittest::MapWellKnownTypes_FieldMaskFieldEntry_descriptor_);
  source_context_field_.SetAssignDescriptorCallback(
      protobuf_AssignDescriptorsOnce);
  source_context_field_.SetEntryDescriptor(
      &::protobuf_unittest::MapWellKnownTypes_SourceContextFieldEntry_descriptor_);
  struct_field_.SetAssignDescriptorCallback(
      protobuf_AssignDescriptorsOnce);
  struct_field_.SetEntryDescriptor(
      &::protobuf_unittest::MapWellKnownTypes_StructFieldEntry_descriptor_);
  timestamp_field_.SetAssignDescriptorCallback(
      protobuf_AssignDescriptorsOnce);
  timestamp_field_.SetEntryDescriptor(
      &::protobuf_unittest::MapWellKnownTypes_TimestampFieldEntry_descriptor_);
  type_field_.SetAssignDescriptorCallback(
      protobuf_AssignDescriptorsOnce);
  type_field_.SetEntryDescriptor(
      &::protobuf_unittest::MapWellKnownTypes_TypeFieldEntry_descriptor_);
  double_field_.SetAssignDescriptorCallback(
      protobuf_AssignDescriptorsOnce);
  double_field_.SetEntryDescriptor(
      &::protobuf_unittest::MapWellKnownTypes_DoubleFieldEntry_descriptor_);
  float_field_.SetAssignDescriptorCallback(
      protobuf_AssignDescriptorsOnce);
  float_field_.SetEntryDescriptor(
      &::protobuf_unittest::MapWellKnownTypes_FloatFieldEntry_descriptor_);
  int64_field_.SetAssignDescriptorCallback(
      protobuf_AssignDescriptorsOnce);
  int64_field_.SetEntryDescriptor(
      &::protobuf_unittest::MapWellKnownTypes_Int64FieldEntry_descriptor_);
  uint64_field_.SetAssignDescriptorCallback(
      protobuf_AssignDescriptorsOnce);
  uint64_field_.SetEntryDescriptor(
      &::protobuf_unittest::MapWellKnownTypes_Uint64FieldEntry_descriptor_);
  int32_field_.SetAssignDescriptorCallback(
      protobuf_AssignDescriptorsOnce);
  int32_field_.SetEntryDescriptor(
      &::protobuf_unittest::MapWellKnownTypes_Int32FieldEntry_descriptor_);
  uint32_field_.SetAssignDescriptorCallback(
      protobuf_AssignDescriptorsOnce);
  uint32_field_.SetEntryDescriptor(
      &::protobuf_unittest::MapWellKnownTypes_Uint32FieldEntry_descriptor_);
  bool_field_.SetAssignDescriptorCallback(
      protobuf_AssignDescriptorsOnce);
  bool_field_.SetEntryDescriptor(
      &::protobuf_unittest::MapWellKnownTypes_BoolFieldEntry_descriptor_);
  string_field_.SetAssignDescriptorCallback(
      protobuf_AssignDescriptorsOnce);
  string_field_.SetEntryDescriptor(
      &::protobuf_unittest::MapWellKnownTypes_StringFieldEntry_descriptor_);
  bytes_field_.SetAssignDescriptorCallback(
      protobuf_AssignDescriptorsOnce);
  bytes_field_.SetEntryDescriptor(
      &::protobuf_unittest::MapWellKnownTypes_BytesFieldEntry_descriptor_);
  _cached_size_ = 0;
}

MapWellKnownTypes::~MapWellKnownTypes() {
  // @@protoc_insertion_point(destructor:protobuf_unittest.MapWellKnownTypes)
  SharedDtor();
}

void MapWellKnownTypes::SharedDtor() {
}

void MapWellKnownTypes::SetCachedSize(int size) const {
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
}
const ::google::protobuf::Descriptor* MapWellKnownTypes::descriptor() {
  protobuf_AssignDescriptorsOnce();
  return MapWellKnownTypes_descriptor_;
}

const MapWellKnownTypes& MapWellKnownTypes::default_instance() {
  protobuf_InitDefaults_google_2fprotobuf_2funittest_5fwell_5fknown_5ftypes_2eproto();
  return *internal_default_instance();
}

::google::protobuf::internal::ExplicitlyConstructed<MapWellKnownTypes> MapWellKnownTypes_default_instance_;

MapWellKnownTypes* MapWellKnownTypes::New(::google::protobuf::Arena* arena) const {
  MapWellKnownTypes* n = new MapWellKnownTypes;
  if (arena != NULL) {
    arena->Own(n);
  }
  return n;
}

void MapWellKnownTypes::Clear() {
// @@protoc_insertion_point(message_clear_start:protobuf_unittest.MapWellKnownTypes)
  any_field_.Clear();
  api_field_.Clear();
  duration_field_.Clear();
  empty_field_.Clear();
  field_mask_field_.Clear();
  source_context_field_.Clear();
  struct_field_.Clear();
  timestamp_field_.Clear();
  type_field_.Clear();
  double_field_.Clear();
  float_field_.Clear();
  int64_field_.Clear();
  uint64_field_.Clear();
  int32_field_.Clear();
  uint32_field_.Clear();
  bool_field_.Clear();
  string_field_.Clear();
  bytes_field_.Clear();
}

bool MapWellKnownTypes::MergePartialFromCodedStream(
    ::google::protobuf::io::CodedInputStream* input) {
#define DO_(EXPRESSION) if (!GOOGLE_PREDICT_TRUE(EXPRESSION)) goto failure
  ::google::protobuf::uint32 tag;
  // @@protoc_insertion_point(parse_start:protobuf_unittest.MapWellKnownTypes)
  for (;;) {
    ::std::pair< ::google::protobuf::uint32, bool> p = input->ReadTagWithCutoff(16383);
    tag = p.first;
    if (!p.second) goto handle_unusual;
    switch (::google::protobuf::internal::WireFormatLite::GetTagFieldNumber(tag)) {
      // map<int32, .google.protobuf.Any> any_field = 1;
      case 1: {
        if (tag == 10) {
          DO_(input->IncrementRecursionDepth());
         parse_loop_any_field:
          MapWellKnownTypes_AnyFieldEntry::Parser< ::google::protobuf::internal::MapField<
              ::google::protobuf::int32, ::google::protobuf::Any,
              ::google::protobuf::internal::WireFormatLite::TYPE_INT32,
              ::google::protobuf::internal::WireFormatLite::TYPE_MESSAGE,
              0 >,
            ::google::protobuf::Map< ::google::protobuf::int32, ::google::protobuf::Any > > parser(&any_field_);
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessageNoVirtual(
              input, &parser));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(10)) goto parse_loop_any_field;
        if (input->ExpectTag(18)) goto parse_loop_api_field;
        input->UnsafeDecrementRecursionDepth();
        break;
      }

      // map<int32, .google.protobuf.Api> api_field = 2;
      case 2: {
        if (tag == 18) {
          DO_(input->IncrementRecursionDepth());
         parse_loop_api_field:
          MapWellKnownTypes_ApiFieldEntry::Parser< ::google::protobuf::internal::MapField<
              ::google::protobuf::int32, ::google::protobuf::Api,
              ::google::protobuf::internal::WireFormatLite::TYPE_INT32,
              ::google::protobuf::internal::WireFormatLite::TYPE_MESSAGE,
              0 >,
            ::google::protobuf::Map< ::google::protobuf::int32, ::google::protobuf::Api > > parser(&api_field_);
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessageNoVirtual(
              input, &parser));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(18)) goto parse_loop_api_field;
        if (input->ExpectTag(26)) goto parse_loop_duration_field;
        input->UnsafeDecrementRecursionDepth();
        break;
      }

      // map<int32, .google.protobuf.Duration> duration_field = 3;
      case 3: {
        if (tag == 26) {
          DO_(input->IncrementRecursionDepth());
         parse_loop_duration_field:
          MapWellKnownTypes_DurationFieldEntry::Parser< ::google::protobuf::internal::MapField<
              ::google::protobuf::int32, ::google::protobuf::Duration,
              ::google::protobuf::internal::WireFormatLite::TYPE_INT32,
              ::google::protobuf::internal::WireFormatLite::TYPE_MESSAGE,
              0 >,
            ::google::protobuf::Map< ::google::protobuf::int32, ::google::protobuf::Duration > > parser(&duration_field_);
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessageNoVirtual(
              input, &parser));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(26)) goto parse_loop_duration_field;
        if (input->ExpectTag(34)) goto parse_loop_empty_field;
        input->UnsafeDecrementRecursionDepth();
        break;
      }

      // map<int32, .google.protobuf.Empty> empty_field = 4;
      case 4: {
        if (tag == 34) {
          DO_(input->IncrementRecursionDepth());
         parse_loop_empty_field:
          MapWellKnownTypes_EmptyFieldEntry::Parser< ::google::protobuf::internal::MapField<
              ::google::protobuf::int32, ::google::protobuf::Empty,
              ::google::protobuf::internal::WireFormatLite::TYPE_INT32,
              ::google::protobuf::internal::WireFormatLite::TYPE_MESSAGE,
              0 >,
            ::google::protobuf::Map< ::google::protobuf::int32, ::google::protobuf::Empty > > parser(&empty_field_);
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessageNoVirtual(
              input, &parser));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(34)) goto parse_loop_empty_field;
        if (input->ExpectTag(42)) goto parse_loop_field_mask_field;
        input->UnsafeDecrementRecursionDepth();
        break;
      }

      // map<int32, .google.protobuf.FieldMask> field_mask_field = 5;
      case 5: {
        if (tag == 42) {
          DO_(input->IncrementRecursionDepth());
         parse_loop_field_mask_field:
          MapWellKnownTypes_FieldMaskFieldEntry::Parser< ::google::protobuf::internal::MapField<
              ::google::protobuf::int32, ::google::protobuf::FieldMask,
              ::google::protobuf::internal::WireFormatLite::TYPE_INT32,
              ::google::protobuf::internal::WireFormatLite::TYPE_MESSAGE,
              0 >,
            ::google::protobuf::Map< ::google::protobuf::int32, ::google::protobuf::FieldMask > > parser(&field_mask_field_);
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessageNoVirtual(
              input, &parser));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(42)) goto parse_loop_field_mask_field;
        if (input->ExpectTag(50)) goto parse_loop_source_context_field;
        input->UnsafeDecrementRecursionDepth();
        break;
      }

      // map<int32, .google.protobuf.SourceContext> source_context_field = 6;
      case 6: {
        if (tag == 50) {
          DO_(input->IncrementRecursionDepth());
         parse_loop_source_context_field:
          MapWellKnownTypes_SourceContextFieldEntry::Parser< ::google::protobuf::internal::MapField<
              ::google::protobuf::int32, ::google::protobuf::SourceContext,
              ::google::protobuf::internal::WireFormatLite::TYPE_INT32,
              ::google::protobuf::internal::WireFormatLite::TYPE_MESSAGE,
              0 >,
            ::google::protobuf::Map< ::google::protobuf::int32, ::google::protobuf::SourceContext > > parser(&source_context_field_);
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessageNoVirtual(
              input, &parser));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(50)) goto parse_loop_source_context_field;
        if (input->ExpectTag(58)) goto parse_loop_struct_field;
        input->UnsafeDecrementRecursionDepth();
        break;
      }

      // map<int32, .google.protobuf.Struct> struct_field = 7;
      case 7: {
        if (tag == 58) {
          DO_(input->IncrementRecursionDepth());
         parse_loop_struct_field:
          MapWellKnownTypes_StructFieldEntry::Parser< ::google::protobuf::internal::MapField<
              ::google::protobuf::int32, ::google::protobuf::Struct,
              ::google::protobuf::internal::WireFormatLite::TYPE_INT32,
              ::google::protobuf::internal::WireFormatLite::TYPE_MESSAGE,
              0 >,
            ::google::protobuf::Map< ::google::protobuf::int32, ::google::protobuf::Struct > > parser(&struct_field_);
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessageNoVirtual(
              input, &parser));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(58)) goto parse_loop_struct_field;
        if (input->ExpectTag(66)) goto parse_loop_timestamp_field;
        input->UnsafeDecrementRecursionDepth();
        break;
      }

      // map<int32, .google.protobuf.Timestamp> timestamp_field = 8;
      case 8: {
        if (tag == 66) {
          DO_(input->IncrementRecursionDepth());
         parse_loop_timestamp_field:
          MapWellKnownTypes_TimestampFieldEntry::Parser< ::google::protobuf::internal::MapField<
              ::google::protobuf::int32, ::google::protobuf::Timestamp,
              ::google::protobuf::internal::WireFormatLite::TYPE_INT32,
              ::google::protobuf::internal::WireFormatLite::TYPE_MESSAGE,
              0 >,
            ::google::protobuf::Map< ::google::protobuf::int32, ::google::protobuf::Timestamp > > parser(&timestamp_field_);
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessageNoVirtual(
              input, &parser));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(66)) goto parse_loop_timestamp_field;
        if (input->ExpectTag(74)) goto parse_loop_type_field;
        input->UnsafeDecrementRecursionDepth();
        break;
      }

      // map<int32, .google.protobuf.Type> type_field = 9;
      case 9: {
        if (tag == 74) {
          DO_(input->IncrementRecursionDepth());
         parse_loop_type_field:
          MapWellKnownTypes_TypeFieldEntry::Parser< ::google::protobuf::internal::MapField<
              ::google::protobuf::int32, ::google::protobuf::Type,
              ::google::protobuf::internal::WireFormatLite::TYPE_INT32,
              ::google::protobuf::internal::WireFormatLite::TYPE_MESSAGE,
              0 >,
            ::google::protobuf::Map< ::google::protobuf::int32, ::google::protobuf::Type > > parser(&type_field_);
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessageNoVirtual(
              input, &parser));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(74)) goto parse_loop_type_field;
        if (input->ExpectTag(82)) goto parse_loop_double_field;
        input->UnsafeDecrementRecursionDepth();
        break;
      }

      // map<int32, .google.protobuf.DoubleValue> double_field = 10;
      case 10: {
        if (tag == 82) {
          DO_(input->IncrementRecursionDepth());
         parse_loop_double_field:
          MapWellKnownTypes_DoubleFieldEntry::Parser< ::google::protobuf::internal::MapField<
              ::google::protobuf::int32, ::google::protobuf::DoubleValue,
              ::google::protobuf::internal::WireFormatLite::TYPE_INT32,
              ::google::protobuf::internal::WireFormatLite::TYPE_MESSAGE,
              0 >,
            ::google::protobuf::Map< ::google::protobuf::int32, ::google::protobuf::DoubleValue > > parser(&double_field_);
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessageNoVirtual(
              input, &parser));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(82)) goto parse_loop_double_field;
        if (input->ExpectTag(90)) goto parse_loop_float_field;
        input->UnsafeDecrementRecursionDepth();
        break;
      }

      // map<int32, .google.protobuf.FloatValue> float_field = 11;
      case 11: {
        if (tag == 90) {
          DO_(input->IncrementRecursionDepth());
         parse_loop_float_field:
          MapWellKnownTypes_FloatFieldEntry::Parser< ::google::protobuf::internal::MapField<
              ::google::protobuf::int32, ::google::protobuf::FloatValue,
              ::google::protobuf::internal::WireFormatLite::TYPE_INT32,
              ::google::protobuf::internal::WireFormatLite::TYPE_MESSAGE,
              0 >,
            ::google::protobuf::Map< ::google::protobuf::int32, ::google::protobuf::FloatValue > > parser(&float_field_);
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessageNoVirtual(
              input, &parser));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(90)) goto parse_loop_float_field;
        if (input->ExpectTag(98)) goto parse_loop_int64_field;
        input->UnsafeDecrementRecursionDepth();
        break;
      }

      // map<int32, .google.protobuf.Int64Value> int64_field = 12;
      case 12: {
        if (tag == 98) {
          DO_(input->IncrementRecursionDepth());
         parse_loop_int64_field:
          MapWellKnownTypes_Int64FieldEntry::Parser< ::google::protobuf::internal::MapField<
              ::google::protobuf::int32, ::google::protobuf::Int64Value,
              ::google::protobuf::internal::WireFormatLite::TYPE_INT32,
              ::google::protobuf::internal::WireFormatLite::TYPE_MESSAGE,
              0 >,
            ::google::protobuf::Map< ::google::protobuf::int32, ::google::protobuf::Int64Value > > parser(&int64_field_);
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessageNoVirtual(
              input, &parser));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(98)) goto parse_loop_int64_field;
        if (input->ExpectTag(106)) goto parse_loop_uint64_field;
        input->UnsafeDecrementRecursionDepth();
        break;
      }

      // map<int32, .google.protobuf.UInt64Value> uint64_field = 13;
      case 13: {
        if (tag == 106) {
          DO_(input->IncrementRecursionDepth());
         parse_loop_uint64_field:
          MapWellKnownTypes_Uint64FieldEntry::Parser< ::google::protobuf::internal::MapField<
              ::google::protobuf::int32, ::google::protobuf::UInt64Value,
              ::google::protobuf::internal::WireFormatLite::TYPE_INT32,
              ::google::protobuf::internal::WireFormatLite::TYPE_MESSAGE,
              0 >,
            ::google::protobuf::Map< ::google::protobuf::int32, ::google::protobuf::UInt64Value > > parser(&uint64_field_);
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessageNoVirtual(
              input, &parser));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(106)) goto parse_loop_uint64_field;
        if (input->ExpectTag(114)) goto parse_loop_int32_field;
        input->UnsafeDecrementRecursionDepth();
        break;
      }

      // map<int32, .google.protobuf.Int32Value> int32_field = 14;
      case 14: {
        if (tag == 114) {
          DO_(input->IncrementRecursionDepth());
         parse_loop_int32_field:
          MapWellKnownTypes_Int32FieldEntry::Parser< ::google::protobuf::internal::MapField<
              ::google::protobuf::int32, ::google::protobuf::Int32Value,
              ::google::protobuf::internal::WireFormatLite::TYPE_INT32,
              ::google::protobuf::internal::WireFormatLite::TYPE_MESSAGE,
              0 >,
            ::google::protobuf::Map< ::google::protobuf::int32, ::google::protobuf::Int32Value > > parser(&int32_field_);
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessageNoVirtual(
              input, &parser));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(114)) goto parse_loop_int32_field;
        if (input->ExpectTag(122)) goto parse_loop_uint32_field;
        input->UnsafeDecrementRecursionDepth();
        break;
      }

      // map<int32, .google.protobuf.UInt32Value> uint32_field = 15;
      case 15: {
        if (tag == 122) {
          DO_(input->IncrementRecursionDepth());
         parse_loop_uint32_field:
          MapWellKnownTypes_Uint32FieldEntry::Parser< ::google::protobuf::internal::MapField<
              ::google::protobuf::int32, ::google::protobuf::UInt32Value,
              ::google::protobuf::internal::WireFormatLite::TYPE_INT32,
              ::google::protobuf::internal::WireFormatLite::TYPE_MESSAGE,
              0 >,
            ::google::protobuf::Map< ::google::protobuf::int32, ::google::protobuf::UInt32Value > > parser(&uint32_field_);
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessageNoVirtual(
              input, &parser));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(122)) goto parse_loop_uint32_field;
        if (input->ExpectTag(130)) goto parse_loop_bool_field;
        input->UnsafeDecrementRecursionDepth();
        break;
      }

      // map<int32, .google.protobuf.BoolValue> bool_field = 16;
      case 16: {
        if (tag == 130) {
          DO_(input->IncrementRecursionDepth());
         parse_loop_bool_field:
          MapWellKnownTypes_BoolFieldEntry::Parser< ::google::protobuf::internal::MapField<
              ::google::protobuf::int32, ::google::protobuf::BoolValue,
              ::google::protobuf::internal::WireFormatLite::TYPE_INT32,
              ::google::protobuf::internal::WireFormatLite::TYPE_MESSAGE,
              0 >,
            ::google::protobuf::Map< ::google::protobuf::int32, ::google::protobuf::BoolValue > > parser(&bool_field_);
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessageNoVirtual(
              input, &parser));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(130)) goto parse_loop_bool_field;
        if (input->ExpectTag(138)) goto parse_loop_string_field;
        input->UnsafeDecrementRecursionDepth();
        break;
      }

      // map<int32, .google.protobuf.StringValue> string_field = 17;
      case 17: {
        if (tag == 138) {
          DO_(input->IncrementRecursionDepth());
         parse_loop_string_field:
          MapWellKnownTypes_StringFieldEntry::Parser< ::google::protobuf::internal::MapField<
              ::google::protobuf::int32, ::google::protobuf::StringValue,
              ::google::protobuf::internal::WireFormatLite::TYPE_INT32,
              ::google::protobuf::internal::WireFormatLite::TYPE_MESSAGE,
              0 >,
            ::google::protobuf::Map< ::google::protobuf::int32, ::google::protobuf::StringValue > > parser(&string_field_);
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessageNoVirtual(
              input, &parser));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(138)) goto parse_loop_string_field;
        if (input->ExpectTag(146)) goto parse_loop_bytes_field;
        input->UnsafeDecrementRecursionDepth();
        break;
      }

      // map<int32, .google.protobuf.BytesValue> bytes_field = 18;
      case 18: {
        if (tag == 146) {
          DO_(input->IncrementRecursionDepth());
         parse_loop_bytes_field:
          MapWellKnownTypes_BytesFieldEntry::Parser< ::google::protobuf::internal::MapField<
              ::google::protobuf::int32, ::google::protobuf::BytesValue,
              ::google::protobuf::internal::WireFormatLite::TYPE_INT32,
              ::google::protobuf::internal::WireFormatLite::TYPE_MESSAGE,
              0 >,
            ::google::protobuf::Map< ::google::protobuf::int32, ::google::protobuf::BytesValue > > parser(&bytes_field_);
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessageNoVirtual(
              input, &parser));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(146)) goto parse_loop_bytes_field;
        input->UnsafeDecrementRecursionDepth();
        if (input->ExpectAtEnd()) goto success;
        break;
      }

      default: {
      handle_unusual:
        if (tag == 0 ||
            ::google::protobuf::internal::WireFormatLite::GetTagWireType(tag) ==
            ::google::protobuf::internal::WireFormatLite::WIRETYPE_END_GROUP) {
          goto success;
        }
        DO_(::google::protobuf::internal::WireFormatLite::SkipField(input, tag));
        break;
      }
    }
  }
success:
  // @@protoc_insertion_point(parse_success:protobuf_unittest.MapWellKnownTypes)
  return true;
failure:
  // @@protoc_insertion_point(parse_failure:protobuf_unittest.MapWellKnownTypes)
  return false;
#undef DO_
}

void MapWellKnownTypes::SerializeWithCachedSizes(
    ::google::protobuf::io::CodedOutputStream* output) const {
  // @@protoc_insertion_point(serialize_start:protobuf_unittest.MapWellKnownTypes)
  // map<int32, .google.protobuf.Any> any_field = 1;
  if (!this->any_field().empty()) {
    typedef ::google::protobuf::Map< ::google::protobuf::int32, ::google::protobuf::Any >::const_pointer
        ConstPtr;
    typedef ::google::protobuf::internal::SortItem< ::google::protobuf::int32, ConstPtr > SortItem;
    typedef ::google::protobuf::internal::CompareByFirstField<SortItem> Less;

    if (output->IsSerializationDeterminstic() &&
        this->any_field().size() > 1) {
      ::google::protobuf::scoped_array<SortItem> items(
          new SortItem[this->any_field().size()]);
      typedef ::google::protobuf::Map< ::google::protobuf::int32, ::google::protobuf::Any >::size_type size_type;
      size_type n = 0;
      for (::google::protobuf::Map< ::google::protobuf::int32, ::google::protobuf::Any >::const_iterator
          it = this->any_field().begin();
          it != this->any_field().end(); ++it, ++n) {
        items[n] = SortItem(&*it);
      }
      ::std::sort(&items[0], &items[n], Less());
      ::google::protobuf::scoped_ptr<MapWellKnownTypes_AnyFieldEntry> entry;
      for (size_type i = 0; i < n; i++) {
        entry.reset(any_field_.NewEntryWrapper(
            items[i].second->first, items[i].second->second));
        ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
            1, *entry, output);
      }
    } else {
      ::google::protobuf::scoped_ptr<MapWellKnownTypes_AnyFieldEntry> entry;
      for (::google::protobuf::Map< ::google::protobuf::int32, ::google::protobuf::Any >::const_iterator
          it = this->any_field().begin();
          it != this->any_field().end(); ++it) {
        entry.reset(any_field_.NewEntryWrapper(
            it->first, it->second));
        ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
            1, *entry, output);
      }
    }
  }

  // map<int32, .google.protobuf.Api> api_field = 2;
  if (!this->api_field().empty()) {
    typedef ::google::protobuf::Map< ::google::protobuf::int32, ::google::protobuf::Api >::const_pointer
        ConstPtr;
    typedef ::google::protobuf::internal::SortItem< ::google::protobuf::int32, ConstPtr > SortItem;
    typedef ::google::protobuf::internal::CompareByFirstField<SortItem> Less;

    if (output->IsSerializationDeterminstic() &&
        this->api_field().size() > 1) {
      ::google::protobuf::scoped_array<SortItem> items(
          new SortItem[this->api_field().size()]);
      typedef ::google::protobuf::Map< ::google::protobuf::int32, ::google::protobuf::Api >::size_type size_type;
      size_type n = 0;
      for (::google::protobuf::Map< ::google::protobuf::int32, ::google::protobuf::Api >::const_iterator
          it = this->api_field().begin();
          it != this->api_field().end(); ++it, ++n) {
        items[n] = SortItem(&*it);
      }
      ::std::sort(&items[0], &items[n], Less());
      ::google::protobuf::scoped_ptr<MapWellKnownTypes_ApiFieldEntry> entry;
      for (size_type i = 0; i < n; i++) {
        entry.reset(api_field_.NewEntryWrapper(
            items[i].second->first, items[i].second->second));
        ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
            2, *entry, output);
      }
    } else {
      ::google::protobuf::scoped_ptr<MapWellKnownTypes_ApiFieldEntry> entry;
      for (::google::protobuf::Map< ::google::protobuf::int32, ::google::protobuf::Api >::const_iterator
          it = this->api_field().begin();
          it != this->api_field().end(); ++it) {
        entry.reset(api_field_.NewEntryWrapper(
            it->first, it->second));
        ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
            2, *entry, output);
      }
    }
  }

  // map<int32, .google.protobuf.Duration> duration_field = 3;
  if (!this->duration_field().empty()) {
    typedef ::google::protobuf::Map< ::google::protobuf::int32, ::google::protobuf::Duration >::const_pointer
        ConstPtr;
    typedef ::google::protobuf::internal::SortItem< ::google::protobuf::int32, ConstPtr > SortItem;
    typedef ::google::protobuf::internal::CompareByFirstField<SortItem> Less;

    if (output->IsSerializationDeterminstic() &&
        this->duration_field().size() > 1) {
      ::google::protobuf::scoped_array<SortItem> items(
          new SortItem[this->duration_field().size()]);
      typedef ::google::protobuf::Map< ::google::protobuf::int32, ::google::protobuf::Duration >::size_type size_type;
      size_type n = 0;
      for (::google::protobuf::Map< ::google::protobuf::int32, ::google::protobuf::Duration >::const_iterator
          it = this->duration_field().begin();
          it != this->duration_field().end(); ++it, ++n) {
        items[n] = SortItem(&*it);
      }
      ::std::sort(&items[0], &items[n], Less());
      ::google::protobuf::scoped_ptr<MapWellKnownTypes_DurationFieldEntry> entry;
      for (size_type i = 0; i < n; i++) {
        entry.reset(duration_field_.NewEntryWrapper(
            items[i].second->first, items[i].second->second));
        ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
            3, *entry, output);
      }
    } else {
      ::google::protobuf::scoped_ptr<MapWellKnownTypes_DurationFieldEntry> entry;
      for (::google::protobuf::Map< ::google::protobuf::int32, ::google::protobuf::Duration >::const_iterator
          it = this->duration_field().begin();
          it != this->duration_field().end(); ++it) {
        entry.reset(duration_field_.NewEntryWrapper(
            it->first, it->second));
        ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
            3, *entry, output);
      }
    }
  }

  // map<int32, .google.protobuf.Empty> empty_field = 4;
  if (!this->empty_field().empty()) {
    typedef ::google::protobuf::Map< ::google::protobuf::int32, ::google::protobuf::Empty >::const_pointer
        ConstPtr;
    typedef ::google::protobuf::internal::SortItem< ::google::protobuf::int32, ConstPtr > SortItem;
    typedef ::google::protobuf::internal::CompareByFirstField<SortItem> Less;

    if (output->IsSerializationDeterminstic() &&
        this->empty_field().size() > 1) {
      ::google::protobuf::scoped_array<SortItem> items(
          new SortItem[this->empty_field().size()]);
      typedef ::google::protobuf::Map< ::google::protobuf::int32, ::google::protobuf::Empty >::size_type size_type;
      size_type n = 0;
      for (::google::protobuf::Map< ::google::protobuf::int32, ::google::protobuf::Empty >::const_iterator
          it = this->empty_field().begin();
          it != this->empty_field().end(); ++it, ++n) {
        items[n] = SortItem(&*it);
      }
      ::std::sort(&items[0], &items[n], Less());
      ::google::protobuf::scoped_ptr<MapWellKnownTypes_EmptyFieldEntry> entry;
      for (size_type i = 0; i < n; i++) {
        entry.reset(empty_field_.NewEntryWrapper(
            items[i].second->first, items[i].second->second));
        ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
            4, *entry, output);
      }
    } else {
      ::google::protobuf::scoped_ptr<MapWellKnownTypes_EmptyFieldEntry> entry;
      for (::google::protobuf::Map< ::google::protobuf::int32, ::google::protobuf::Empty >::const_iterator
          it = this->empty_field().begin();
          it != this->empty_field().end(); ++it) {
        entry.reset(empty_field_.NewEntryWrapper(
            it->first, it->second));
        ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
            4, *entry, output);
      }
    }
  }

  // map<int32, .google.protobuf.FieldMask> field_mask_field = 5;
  if (!this->field_mask_field().empty()) {
    typedef ::google::protobuf::Map< ::google::protobuf::int32, ::google::protobuf::FieldMask >::const_pointer
        ConstPtr;
    typedef ::google::protobuf::internal::SortItem< ::google::protobuf::int32, ConstPtr > SortItem;
    typedef ::google::protobuf::internal::CompareByFirstField<SortItem> Less;

    if (output->IsSerializationDeterminstic() &&
        this->field_mask_field().size() > 1) {
      ::google::protobuf::scoped_array<SortItem> items(
          new SortItem[this->field_mask_field().size()]);
      typedef ::google::protobuf::Map< ::google::protobuf::int32, ::google::protobuf::FieldMask >::size_type size_type;
      size_type n = 0;
      for (::google::protobuf::Map< ::google::protobuf::int32, ::google::protobuf::FieldMask >::const_iterator
          it = this->field_mask_field().begin();
          it != this->field_mask_field().end(); ++it, ++n) {
        items[n] = SortItem(&*it);
      }
      ::std::sort(&items[0], &items[n], Less());
      ::google::protobuf::scoped_ptr<MapWellKnownTypes_FieldMaskFieldEntry> entry;
      for (size_type i = 0; i < n; i++) {
        entry.reset(field_mask_field_.NewEntryWrapper(
            items[i].second->first, items[i].second->second));
        ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
            5, *entry, output);
      }
    } else {
      ::google::protobuf::scoped_ptr<MapWellKnownTypes_FieldMaskFieldEntry> entry;
      for (::google::protobuf::Map< ::google::protobuf::int32, ::google::protobuf::FieldMask >::const_iterator
          it = this->field_mask_field().begin();
          it != this->field_mask_field().end(); ++it) {
        entry.reset(field_mask_field_.NewEntryWrapper(
            it->first, it->second));
        ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
            5, *entry, output);
      }
    }
  }

  // map<int32, .google.protobuf.SourceContext> source_context_field = 6;
  if (!this->source_context_field().empty()) {
    typedef ::google::protobuf::Map< ::google::protobuf::int32, ::google::protobuf::SourceContext >::const_pointer
        ConstPtr;
    typedef ::google::protobuf::internal::SortItem< ::google::protobuf::int32, ConstPtr > SortItem;
    typedef ::google::protobuf::internal::CompareByFirstField<SortItem> Less;

    if (output->IsSerializationDeterminstic() &&
        this->source_context_field().size() > 1) {
      ::google::protobuf::scoped_array<SortItem> items(
          new SortItem[this->source_context_field().size()]);
      typedef ::google::protobuf::Map< ::google::protobuf::int32, ::google::protobuf::SourceContext >::size_type size_type;
      size_type n = 0;
      for (::google::protobuf::Map< ::google::protobuf::int32, ::google::protobuf::SourceContext >::const_iterator
          it = this->source_context_field().begin();
          it != this->source_context_field().end(); ++it, ++n) {
        items[n] = SortItem(&*it);
      }
      ::std::sort(&items[0], &items[n], Less());
      ::google::protobuf::scoped_ptr<MapWellKnownTypes_SourceContextFieldEntry> entry;
      for (size_type i = 0; i < n; i++) {
        entry.reset(source_context_field_.NewEntryWrapper(
            items[i].second->first, items[i].second->second));
        ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
            6, *entry, output);
      }
    } else {
      ::google::protobuf::scoped_ptr<MapWellKnownTypes_SourceContextFieldEntry> entry;
      for (::google::protobuf::Map< ::google::protobuf::int32, ::google::protobuf::SourceContext >::const_iterator
          it = this->source_context_field().begin();
          it != this->source_context_field().end(); ++it) {
        entry.reset(source_context_field_.NewEntryWrapper(
            it->first, it->second));
        ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
            6, *entry, output);
      }
    }
  }

  // map<int32, .google.protobuf.Struct> struct_field = 7;
  if (!this->struct_field().empty()) {
    typedef ::google::protobuf::Map< ::google::protobuf::int32, ::google::protobuf::Struct >::const_pointer
        ConstPtr;
    typedef ::google::protobuf::internal::SortItem< ::google::protobuf::int32, ConstPtr > SortItem;
    typedef ::google::protobuf::internal::CompareByFirstField<SortItem> Less;

    if (output->IsSerializationDeterminstic() &&
        this->struct_field().size() > 1) {
      ::google::protobuf::scoped_array<SortItem> items(
          new SortItem[this->struct_field().size()]);
      typedef ::google::protobuf::Map< ::google::protobuf::int32, ::google::protobuf::Struct >::size_type size_type;
      size_type n = 0;
      for (::google::protobuf::Map< ::google::protobuf::int32, ::google::protobuf::Struct >::const_iterator
          it = this->struct_field().begin();
          it != this->struct_field().end(); ++it, ++n) {
        items[n] = SortItem(&*it);
      }
      ::std::sort(&items[0], &items[n], Less());
      ::google::protobuf::scoped_ptr<MapWellKnownTypes_StructFieldEntry> entry;
      for (size_type i = 0; i < n; i++) {
        entry.reset(struct_field_.NewEntryWrapper(
            items[i].second->first, items[i].second->second));
        ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
            7, *entry, output);
      }
    } else {
      ::google::protobuf::scoped_ptr<MapWellKnownTypes_StructFieldEntry> entry;
      for (::google::protobuf::Map< ::google::protobuf::int32, ::google::protobuf::Struct >::const_iterator
          it = this->struct_field().begin();
          it != this->struct_field().end(); ++it) {
        entry.reset(struct_field_.NewEntryWrapper(
            it->first, it->second));
        ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
            7, *entry, output);
      }
    }
  }

  // map<int32, .google.protobuf.Timestamp> timestamp_field = 8;
  if (!this->timestamp_field().empty()) {
    typedef ::google::protobuf::Map< ::google::protobuf::int32, ::google::protobuf::Timestamp >::const_pointer
        ConstPtr;
    typedef ::google::protobuf::internal::SortItem< ::google::protobuf::int32, ConstPtr > SortItem;
    typedef ::google::protobuf::internal::CompareByFirstField<SortItem> Less;

    if (output->IsSerializationDeterminstic() &&
        this->timestamp_field().size() > 1) {
      ::google::protobuf::scoped_array<SortItem> items(
          new SortItem[this->timestamp_field().size()]);
      typedef ::google::protobuf::Map< ::google::protobuf::int32, ::google::protobuf::Timestamp >::size_type size_type;
      size_type n = 0;
      for (::google::protobuf::Map< ::google::protobuf::int32, ::google::protobuf::Timestamp >::const_iterator
          it = this->timestamp_field().begin();
          it != this->timestamp_field().end(); ++it, ++n) {
        items[n] = SortItem(&*it);
      }
      ::std::sort(&items[0], &items[n], Less());
      ::google::protobuf::scoped_ptr<MapWellKnownTypes_TimestampFieldEntry> entry;
      for (size_type i = 0; i < n; i++) {
        entry.reset(timestamp_field_.NewEntryWrapper(
            items[i].second->first, items[i].second->second));
        ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
            8, *entry, output);
      }
    } else {
      ::google::protobuf::scoped_ptr<MapWellKnownTypes_TimestampFieldEntry> entry;
      for (::google::protobuf::Map< ::google::protobuf::int32, ::google::protobuf::Timestamp >::const_iterator
          it = this->timestamp_field().begin();
          it != this->timestamp_field().end(); ++it) {
        entry.reset(timestamp_field_.NewEntryWrapper(
            it->first, it->second));
        ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
            8, *entry, output);
      }
    }
  }

  // map<int32, .google.protobuf.Type> type_field = 9;
  if (!this->type_field().empty()) {
    typedef ::google::protobuf::Map< ::google::protobuf::int32, ::google::protobuf::Type >::const_pointer
        ConstPtr;
    typedef ::google::protobuf::internal::SortItem< ::google::protobuf::int32, ConstPtr > SortItem;
    typedef ::google::protobuf::internal::CompareByFirstField<SortItem> Less;

    if (output->IsSerializationDeterminstic() &&
        this->type_field().size() > 1) {
      ::google::protobuf::scoped_array<SortItem> items(
          new SortItem[this->type_field().size()]);
      typedef ::google::protobuf::Map< ::google::protobuf::int32, ::google::protobuf::Type >::size_type size_type;
      size_type n = 0;
      for (::google::protobuf::Map< ::google::protobuf::int32, ::google::protobuf::Type >::const_iterator
          it = this->type_field().begin();
          it != this->type_field().end(); ++it, ++n) {
        items[n] = SortItem(&*it);
      }
      ::std::sort(&items[0], &items[n], Less());
      ::google::protobuf::scoped_ptr<MapWellKnownTypes_TypeFieldEntry> entry;
      for (size_type i = 0; i < n; i++) {
        entry.reset(type_field_.NewEntryWrapper(
            items[i].second->first, items[i].second->second));
        ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
            9, *entry, output);
      }
    } else {
      ::google::protobuf::scoped_ptr<MapWellKnownTypes_TypeFieldEntry> entry;
      for (::google::protobuf::Map< ::google::protobuf::int32, ::google::protobuf::Type >::const_iterator
          it = this->type_field().begin();
          it != this->type_field().end(); ++it) {
        entry.reset(type_field_.NewEntryWrapper(
            it->first, it->second));
        ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
            9, *entry, output);
      }
    }
  }

  // map<int32, .google.protobuf.DoubleValue> double_field = 10;
  if (!this->double_field().empty()) {
    typedef ::google::protobuf::Map< ::google::protobuf::int32, ::google::protobuf::DoubleValue >::const_pointer
        ConstPtr;
    typedef ::google::protobuf::internal::SortItem< ::google::protobuf::int32, ConstPtr > SortItem;
    typedef ::google::protobuf::internal::CompareByFirstField<SortItem> Less;

    if (output->IsSerializationDeterminstic() &&
        this->double_field().size() > 1) {
      ::google::protobuf::scoped_array<SortItem> items(
          new SortItem[this->double_field().size()]);
      typedef ::google::protobuf::Map< ::google::protobuf::int32, ::google::protobuf::DoubleValue >::size_type size_type;
      size_type n = 0;
      for (::google::protobuf::Map< ::google::protobuf::int32, ::google::protobuf::DoubleValue >::const_iterator
          it = this->double_field().begin();
          it != this->double_field().end(); ++it, ++n) {
        items[n] = SortItem(&*it);
      }
      ::std::sort(&items[0], &items[n], Less());
      ::google::protobuf::scoped_ptr<MapWellKnownTypes_DoubleFieldEntry> entry;
      for (size_type i = 0; i < n; i++) {
        entry.reset(double_field_.NewEntryWrapper(
            items[i].second->first, items[i].second->second));
        ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
            10, *entry, output);
      }
    } else {
      ::google::protobuf::scoped_ptr<MapWellKnownTypes_DoubleFieldEntry> entry;
      for (::google::protobuf::Map< ::google::protobuf::int32, ::google::protobuf::DoubleValue >::const_iterator
          it = this->double_field().begin();
          it != this->double_field().end(); ++it) {
        entry.reset(double_field_.NewEntryWrapper(
            it->first, it->second));
        ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
            10, *entry, output);
      }
    }
  }

  // map<int32, .google.protobuf.FloatValue> float_field = 11;
  if (!this->float_field().empty()) {
    typedef ::google::protobuf::Map< ::google::protobuf::int32, ::google::protobuf::FloatValue >::const_pointer
        ConstPtr;
    typedef ::google::protobuf::internal::SortItem< ::google::protobuf::int32, ConstPtr > SortItem;
    typedef ::google::protobuf::internal::CompareByFirstField<SortItem> Less;

    if (output->IsSerializationDeterminstic() &&
        this->float_field().size() > 1) {
      ::google::protobuf::scoped_array<SortItem> items(
          new SortItem[this->float_field().size()]);
      typedef ::google::protobuf::Map< ::google::protobuf::int32, ::google::protobuf::FloatValue >::size_type size_type;
      size_type n = 0;
      for (::google::protobuf::Map< ::google::protobuf::int32, ::google::protobuf::FloatValue >::const_iterator
          it = this->float_field().begin();
          it != this->float_field().end(); ++it, ++n) {
        items[n] = SortItem(&*it);
      }
      ::std::sort(&items[0], &items[n], Less());
      ::google::protobuf::scoped_ptr<MapWellKnownTypes_FloatFieldEntry> entry;
      for (size_type i = 0; i < n; i++) {
        entry.reset(float_field_.NewEntryWrapper(
            items[i].second->first, items[i].second->second));
        ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
            11, *entry, output);
      }
    } else {
      ::google::protobuf::scoped_ptr<MapWellKnownTypes_FloatFieldEntry> entry;
      for (::google::protobuf::Map< ::google::protobuf::int32, ::google::protobuf::FloatValue >::const_iterator
          it = this->float_field().begin();
          it != this->float_field().end(); ++it) {
        entry.reset(float_field_.NewEntryWrapper(
            it->first, it->second));
        ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
            11, *entry, output);
      }
    }
  }

  // map<int32, .google.protobuf.Int64Value> int64_field = 12;
  if (!this->int64_field().empty()) {
    typedef ::google::protobuf::Map< ::google::protobuf::int32, ::google::protobuf::Int64Value >::const_pointer
        ConstPtr;
    typedef ::google::protobuf::internal::SortItem< ::google::protobuf::int32, ConstPtr > SortItem;
    typedef ::google::protobuf::internal::CompareByFirstField<SortItem> Less;

    if (output->IsSerializationDeterminstic() &&
        this->int64_field().size() > 1) {
      ::google::protobuf::scoped_array<SortItem> items(
          new SortItem[this->int64_field().size()]);
      typedef ::google::protobuf::Map< ::google::protobuf::int32, ::google::protobuf::Int64Value >::size_type size_type;
      size_type n = 0;
      for (::google::protobuf::Map< ::google::protobuf::int32, ::google::protobuf::Int64Value >::const_iterator
          it = this->int64_field().begin();
          it != this->int64_field().end(); ++it, ++n) {
        items[n] = SortItem(&*it);
      }
      ::std::sort(&items[0], &items[n], Less());
      ::google::protobuf::scoped_ptr<MapWellKnownTypes_Int64FieldEntry> entry;
      for (size_type i = 0; i < n; i++) {
        entry.reset(int64_field_.NewEntryWrapper(
            items[i].second->first, items[i].second->second));
        ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
            12, *entry, output);
      }
    } else {
      ::google::protobuf::scoped_ptr<MapWellKnownTypes_Int64FieldEntry> entry;
      for (::google::protobuf::Map< ::google::protobuf::int32, ::google::protobuf::Int64Value >::const_iterator
          it = this->int64_field().begin();
          it != this->int64_field().end(); ++it) {
        entry.reset(int64_field_.NewEntryWrapper(
            it->first, it->second));
        ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
            12, *entry, output);
      }
    }
  }

  // map<int32, .google.protobuf.UInt64Value> uint64_field = 13;
  if (!this->uint64_field().empty()) {
    typedef ::google::protobuf::Map< ::google::protobuf::int32, ::google::protobuf::UInt64Value >::const_pointer
        ConstPtr;
    typedef ::google::protobuf::internal::SortItem< ::google::protobuf::int32, ConstPtr > SortItem;
    typedef ::google::protobuf::internal::CompareByFirstField<SortItem> Less;

    if (output->IsSerializationDeterminstic() &&
        this->uint64_field().size() > 1) {
      ::google::protobuf::scoped_array<SortItem> items(
          new SortItem[this->uint64_field().size()]);
      typedef ::google::protobuf::Map< ::google::protobuf::int32, ::google::protobuf::UInt64Value >::size_type size_type;
      size_type n = 0;
      for (::google::protobuf::Map< ::google::protobuf::int32, ::google::protobuf::UInt64Value >::const_iterator
          it = this->uint64_field().begin();
          it != this->uint64_field().end(); ++it, ++n) {
        items[n] = SortItem(&*it);
      }
      ::std::sort(&items[0], &items[n], Less());
      ::google::protobuf::scoped_ptr<MapWellKnownTypes_Uint64FieldEntry> entry;
      for (size_type i = 0; i < n; i++) {
        entry.reset(uint64_field_.NewEntryWrapper(
            items[i].second->first, items[i].second->second));
        ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
            13, *entry, output);
      }
    } else {
      ::google::protobuf::scoped_ptr<MapWellKnownTypes_Uint64FieldEntry> entry;
      for (::google::protobuf::Map< ::google::protobuf::int32, ::google::protobuf::UInt64Value >::const_iterator
          it = this->uint64_field().begin();
          it != this->uint64_field().end(); ++it) {
        entry.reset(uint64_field_.NewEntryWrapper(
            it->first, it->second));
        ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
            13, *entry, output);
      }
    }
  }

  // map<int32, .google.protobuf.Int32Value> int32_field = 14;
  if (!this->int32_field().empty()) {
    typedef ::google::protobuf::Map< ::google::protobuf::int32, ::google::protobuf::Int32Value >::const_pointer
        ConstPtr;
    typedef ::google::protobuf::internal::SortItem< ::google::protobuf::int32, ConstPtr > SortItem;
    typedef ::google::protobuf::internal::CompareByFirstField<SortItem> Less;

    if (output->IsSerializationDeterminstic() &&
        this->int32_field().size() > 1) {
      ::google::protobuf::scoped_array<SortItem> items(
          new SortItem[this->int32_field().size()]);
      typedef ::google::protobuf::Map< ::google::protobuf::int32, ::google::protobuf::Int32Value >::size_type size_type;
      size_type n = 0;
      for (::google::protobuf::Map< ::google::protobuf::int32, ::google::protobuf::Int32Value >::const_iterator
          it = this->int32_field().begin();
          it != this->int32_field().end(); ++it, ++n) {
        items[n] = SortItem(&*it);
      }
      ::std::sort(&items[0], &items[n], Less());
      ::google::protobuf::scoped_ptr<MapWellKnownTypes_Int32FieldEntry> entry;
      for (size_type i = 0; i < n; i++) {
        entry.reset(int32_field_.NewEntryWrapper(
            items[i].second->first, items[i].second->second));
        ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
            14, *entry, output);
      }
    } else {
      ::google::protobuf::scoped_ptr<MapWellKnownTypes_Int32FieldEntry> entry;
      for (::google::protobuf::Map< ::google::protobuf::int32, ::google::protobuf::Int32Value >::const_iterator
          it = this->int32_field().begin();
          it != this->int32_field().end(); ++it) {
        entry.reset(int32_field_.NewEntryWrapper(
            it->first, it->second));
        ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
            14, *entry, output);
      }
    }
  }

  // map<int32, .google.protobuf.UInt32Value> uint32_field = 15;
  if (!this->uint32_field().empty()) {
    typedef ::google::protobuf::Map< ::google::protobuf::int32, ::google::protobuf::UInt32Value >::const_pointer
        ConstPtr;
    typedef ::google::protobuf::internal::SortItem< ::google::protobuf::int32, ConstPtr > SortItem;
    typedef ::google::protobuf::internal::CompareByFirstField<SortItem> Less;

    if (output->IsSerializationDeterminstic() &&
        this->uint32_field().size() > 1) {
      ::google::protobuf::scoped_array<SortItem> items(
          new SortItem[this->uint32_field().size()]);
      typedef ::google::protobuf::Map< ::google::protobuf::int32, ::google::protobuf::UInt32Value >::size_type size_type;
      size_type n = 0;
      for (::google::protobuf::Map< ::google::protobuf::int32, ::google::protobuf::UInt32Value >::const_iterator
          it = this->uint32_field().begin();
          it != this->uint32_field().end(); ++it, ++n) {
        items[n] = SortItem(&*it);
      }
      ::std::sort(&items[0], &items[n], Less());
      ::google::protobuf::scoped_ptr<MapWellKnownTypes_Uint32FieldEntry> entry;
      for (size_type i = 0; i < n; i++) {
        entry.reset(uint32_field_.NewEntryWrapper(
            items[i].second->first, items[i].second->second));
        ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
            15, *entry, output);
      }
    } else {
      ::google::protobuf::scoped_ptr<MapWellKnownTypes_Uint32FieldEntry> entry;
      for (::google::protobuf::Map< ::google::protobuf::int32, ::google::protobuf::UInt32Value >::const_iterator
          it = this->uint32_field().begin();
          it != this->uint32_field().end(); ++it) {
        entry.reset(uint32_field_.NewEntryWrapper(
            it->first, it->second));
        ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
            15, *entry, output);
      }
    }
  }

  // map<int32, .google.protobuf.BoolValue> bool_field = 16;
  if (!this->bool_field().empty()) {
    typedef ::google::protobuf::Map< ::google::protobuf::int32, ::google::protobuf::BoolValue >::const_pointer
        ConstPtr;
    typedef ::google::protobuf::internal::SortItem< ::google::protobuf::int32, ConstPtr > SortItem;
    typedef ::google::protobuf::internal::CompareByFirstField<SortItem> Less;

    if (output->IsSerializationDeterminstic() &&
        this->bool_field().size() > 1) {
      ::google::protobuf::scoped_array<SortItem> items(
          new SortItem[this->bool_field().size()]);
      typedef ::google::protobuf::Map< ::google::protobuf::int32, ::google::protobuf::BoolValue >::size_type size_type;
      size_type n = 0;
      for (::google::protobuf::Map< ::google::protobuf::int32, ::google::protobuf::BoolValue >::const_iterator
          it = this->bool_field().begin();
          it != this->bool_field().end(); ++it, ++n) {
        items[n] = SortItem(&*it);
      }
      ::std::sort(&items[0], &items[n], Less());
      ::google::protobuf::scoped_ptr<MapWellKnownTypes_BoolFieldEntry> entry;
      for (size_type i = 0; i < n; i++) {
        entry.reset(bool_field_.NewEntryWrapper(
            items[i].second->first, items[i].second->second));
        ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
            16, *entry, output);
      }
    } else {
      ::google::protobuf::scoped_ptr<MapWellKnownTypes_BoolFieldEntry> entry;
      for (::google::protobuf::Map< ::google::protobuf::int32, ::google::protobuf::BoolValue >::const_iterator
          it = this->bool_field().begin();
          it != this->bool_field().end(); ++it) {
        entry.reset(bool_field_.NewEntryWrapper(
            it->first, it->second));
        ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
            16, *entry, output);
      }
    }
  }

  // map<int32, .google.protobuf.StringValue> string_field = 17;
  if (!this->string_field().empty()) {
    typedef ::google::protobuf::Map< ::google::protobuf::int32, ::google::protobuf::StringValue >::const_pointer
        ConstPtr;
    typedef ::google::protobuf::internal::SortItem< ::google::protobuf::int32, ConstPtr > SortItem;
    typedef ::google::protobuf::internal::CompareByFirstField<SortItem> Less;

    if (output->IsSerializationDeterminstic() &&
        this->string_field().size() > 1) {
      ::google::protobuf::scoped_array<SortItem> items(
          new SortItem[this->string_field().size()]);
      typedef ::google::protobuf::Map< ::google::protobuf::int32, ::google::protobuf::StringValue >::size_type size_type;
      size_type n = 0;
      for (::google::protobuf::Map< ::google::protobuf::int32, ::google::protobuf::StringValue >::const_iterator
          it = this->string_field().begin();
          it != this->string_field().end(); ++it, ++n) {
        items[n] = SortItem(&*it);
      }
      ::std::sort(&items[0], &items[n], Less());
      ::google::protobuf::scoped_ptr<MapWellKnownTypes_StringFieldEntry> entry;
      for (size_type i = 0; i < n; i++) {
        entry.reset(string_field_.NewEntryWrapper(
            items[i].second->first, items[i].second->second));
        ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
            17, *entry, output);
      }
    } else {
      ::google::protobuf::scoped_ptr<MapWellKnownTypes_StringFieldEntry> entry;
      for (::google::protobuf::Map< ::google::protobuf::int32, ::google::protobuf::StringValue >::const_iterator
          it = this->string_field().begin();
          it != this->string_field().end(); ++it) {
        entry.reset(string_field_.NewEntryWrapper(
            it->first, it->second));
        ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
            17, *entry, output);
      }
    }
  }

  // map<int32, .google.protobuf.BytesValue> bytes_field = 18;
  if (!this->bytes_field().empty()) {
    typedef ::google::protobuf::Map< ::google::protobuf::int32, ::google::protobuf::BytesValue >::const_pointer
        ConstPtr;
    typedef ::google::protobuf::internal::SortItem< ::google::protobuf::int32, ConstPtr > SortItem;
    typedef ::google::protobuf::internal::CompareByFirstField<SortItem> Less;

    if (output->IsSerializationDeterminstic() &&
        this->bytes_field().size() > 1) {
      ::google::protobuf::scoped_array<SortItem> items(
          new SortItem[this->bytes_field().size()]);
      typedef ::google::protobuf::Map< ::google::protobuf::int32, ::google::protobuf::BytesValue >::size_type size_type;
      size_type n = 0;
      for (::google::protobuf::Map< ::google::protobuf::int32, ::google::protobuf::BytesValue >::const_iterator
          it = this->bytes_field().begin();
          it != this->bytes_field().end(); ++it, ++n) {
        items[n] = SortItem(&*it);
      }
      ::std::sort(&items[0], &items[n], Less());
      ::google::protobuf::scoped_ptr<MapWellKnownTypes_BytesFieldEntry> entry;
      for (size_type i = 0; i < n; i++) {
        entry.reset(bytes_field_.NewEntryWrapper(
            items[i].second->first, items[i].second->second));
        ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
            18, *entry, output);
      }
    } else {
      ::google::protobuf::scoped_ptr<MapWellKnownTypes_BytesFieldEntry> entry;
      for (::google::protobuf::Map< ::google::protobuf::int32, ::google::protobuf::BytesValue >::const_iterator
          it = this->bytes_field().begin();
          it != this->bytes_field().end(); ++it) {
        entry.reset(bytes_field_.NewEntryWrapper(
            it->first, it->second));
        ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
            18, *entry, output);
      }
    }
  }

  // @@protoc_insertion_point(serialize_end:protobuf_unittest.MapWellKnownTypes)
}

::google::protobuf::uint8* MapWellKnownTypes::InternalSerializeWithCachedSizesToArray(
    bool deterministic, ::google::protobuf::uint8* target) const {
  (void)deterministic; // Unused
  // @@protoc_insertion_point(serialize_to_array_start:protobuf_unittest.MapWellKnownTypes)
  // map<int32, .google.protobuf.Any> any_field = 1;
  if (!this->any_field().empty()) {
    typedef ::google::protobuf::Map< ::google::protobuf::int32, ::google::protobuf::Any >::const_pointer
        ConstPtr;
    typedef ::google::protobuf::internal::SortItem< ::google::protobuf::int32, ConstPtr > SortItem;
    typedef ::google::protobuf::internal::CompareByFirstField<SortItem> Less;

    if (deterministic &&
        this->any_field().size() > 1) {
      ::google::protobuf::scoped_array<SortItem> items(
          new SortItem[this->any_field().size()]);
      typedef ::google::protobuf::Map< ::google::protobuf::int32, ::google::protobuf::Any >::size_type size_type;
      size_type n = 0;
      for (::google::protobuf::Map< ::google::protobuf::int32, ::google::protobuf::Any >::const_iterator
          it = this->any_field().begin();
          it != this->any_field().end(); ++it, ++n) {
        items[n] = SortItem(&*it);
      }
      ::std::sort(&items[0], &items[n], Less());
      ::google::protobuf::scoped_ptr<MapWellKnownTypes_AnyFieldEntry> entry;
      for (size_type i = 0; i < n; i++) {
        entry.reset(any_field_.NewEntryWrapper(
            items[i].second->first, items[i].second->second));
        target = ::google::protobuf::internal::WireFormatLite::
                   InternalWriteMessageNoVirtualToArray(
                       1, *entry, deterministic, target);
;
      }
    } else {
      ::google::protobuf::scoped_ptr<MapWellKnownTypes_AnyFieldEntry> entry;
      for (::google::protobuf::Map< ::google::protobuf::int32, ::google::protobuf::Any >::const_iterator
          it = this->any_field().begin();
          it != this->any_field().end(); ++it) {
        entry.reset(any_field_.NewEntryWrapper(
            it->first, it->second));
        target = ::google::protobuf::internal::WireFormatLite::
                   InternalWriteMessageNoVirtualToArray(
                       1, *entry, deterministic, target);
;
      }
    }
  }

  // map<int32, .google.protobuf.Api> api_field = 2;
  if (!this->api_field().empty()) {
    typedef ::google::protobuf::Map< ::google::protobuf::int32, ::google::protobuf::Api >::const_pointer
        ConstPtr;
    typedef ::google::protobuf::internal::SortItem< ::google::protobuf::int32, ConstPtr > SortItem;
    typedef ::google::protobuf::internal::CompareByFirstField<SortItem> Less;

    if (deterministic &&
        this->api_field().size() > 1) {
      ::google::protobuf::scoped_array<SortItem> items(
          new SortItem[this->api_field().size()]);
      typedef ::google::protobuf::Map< ::google::protobuf::int32, ::google::protobuf::Api >::size_type size_type;
      size_type n = 0;
      for (::google::protobuf::Map< ::google::protobuf::int32, ::google::protobuf::Api >::const_iterator
          it = this->api_field().begin();
          it != this->api_field().end(); ++it, ++n) {
        items[n] = SortItem(&*it);
      }
      ::std::sort(&items[0], &items[n], Less());
      ::google::protobuf::scoped_ptr<MapWellKnownTypes_ApiFieldEntry> entry;
      for (size_type i = 0; i < n; i++) {
        entry.reset(api_field_.NewEntryWrapper(
            items[i].second->first, items[i].second->second));
        target = ::google::protobuf::internal::WireFormatLite::
                   InternalWriteMessageNoVirtualToArray(
                       2, *entry, deterministic, target);
;
      }
    } else {
      ::google::protobuf::scoped_ptr<MapWellKnownTypes_ApiFieldEntry> entry;
      for (::google::protobuf::Map< ::google::protobuf::int32, ::google::protobuf::Api >::const_iterator
          it = this->api_field().begin();
          it != this->api_field().end(); ++it) {
        entry.reset(api_field_.NewEntryWrapper(
            it->first, it->second));
        target = ::google::protobuf::internal::WireFormatLite::
                   InternalWriteMessageNoVirtualToArray(
                       2, *entry, deterministic, target);
;
      }
    }
  }

  // map<int32, .google.protobuf.Duration> duration_field = 3;
  if (!this->duration_field().empty()) {
    typedef ::google::protobuf::Map< ::google::protobuf::int32, ::google::protobuf::Duration >::const_pointer
        ConstPtr;
    typedef ::google::protobuf::internal::SortItem< ::google::protobuf::int32, ConstPtr > SortItem;
    typedef ::google::protobuf::internal::CompareByFirstField<SortItem> Less;

    if (deterministic &&
        this->duration_field().size() > 1) {
      ::google::protobuf::scoped_array<SortItem> items(
          new SortItem[this->duration_field().size()]);
      typedef ::google::protobuf::Map< ::google::protobuf::int32, ::google::protobuf::Duration >::size_type size_type;
      size_type n = 0;
      for (::google::protobuf::Map< ::google::protobuf::int32, ::google::protobuf::Duration >::const_iterator
          it = this->duration_field().begin();
          it != this->duration_field().end(); ++it, ++n) {
        items[n] = SortItem(&*it);
      }
      ::std::sort(&items[0], &items[n], Less());
      ::google::protobuf::scoped_ptr<MapWellKnownTypes_DurationFieldEntry> entry;
      for (size_type i = 0; i < n; i++) {
        entry.reset(duration_field_.NewEntryWrapper(
            items[i].second->first, items[i].second->second));
        target = ::google::protobuf::internal::WireFormatLite::
                   InternalWriteMessageNoVirtualToArray(
                       3, *entry, deterministic, target);
;
      }
    } else {
      ::google::protobuf::scoped_ptr<MapWellKnownTypes_DurationFieldEntry> entry;
      for (::google::protobuf::Map< ::google::protobuf::int32, ::google::protobuf::Duration >::const_iterator
          it = this->duration_field().begin();
          it != this->duration_field().end(); ++it) {
        entry.reset(duration_field_.NewEntryWrapper(
            it->first, it->second));
        target = ::google::protobuf::internal::WireFormatLite::
                   InternalWriteMessageNoVirtualToArray(
                       3, *entry, deterministic, target);
;
      }
    }
  }

  // map<int32, .google.protobuf.Empty> empty_field = 4;
  if (!this->empty_field().empty()) {
    typedef ::google::protobuf::Map< ::google::protobuf::int32, ::google::protobuf::Empty >::const_pointer
        ConstPtr;
    typedef ::google::protobuf::internal::SortItem< ::google::protobuf::int32, ConstPtr > SortItem;
    typedef ::google::protobuf::internal::CompareByFirstField<SortItem> Less;

    if (deterministic &&
        this->empty_field().size() > 1) {
      ::google::protobuf::scoped_array<SortItem> items(
          new SortItem[this->empty_field().size()]);
      typedef ::google::protobuf::Map< ::google::protobuf::int32, ::google::protobuf::Empty >::size_type size_type;
      size_type n = 0;
      for (::google::protobuf::Map< ::google::protobuf::int32, ::google::protobuf::Empty >::const_iterator
          it = this->empty_field().begin();
          it != this->empty_field().end(); ++it, ++n) {
        items[n] = SortItem(&*it);
      }
      ::std::sort(&items[0], &items[n], Less());
      ::google::protobuf::scoped_ptr<MapWellKnownTypes_EmptyFieldEntry> entry;
      for (size_type i = 0; i < n; i++) {
        entry.reset(empty_field_.NewEntryWrapper(
            items[i].second->first, items[i].second->second));
        target = ::google::protobuf::internal::WireFormatLite::
                   InternalWriteMessageNoVirtualToArray(
                       4, *entry, deterministic, target);
;
      }
    } else {
      ::google::protobuf::scoped_ptr<MapWellKnownTypes_EmptyFieldEntry> entry;
      for (::google::protobuf::Map< ::google::protobuf::int32, ::google::protobuf::Empty >::const_iterator
          it = this->empty_field().begin();
          it != this->empty_field().end(); ++it) {
        entry.reset(empty_field_.NewEntryWrapper(
            it->first, it->second));
        target = ::google::protobuf::internal::WireFormatLite::
                   InternalWriteMessageNoVirtualToArray(
                       4, *entry, deterministic, target);
;
      }
    }
  }

  // map<int32, .google.protobuf.FieldMask> field_mask_field = 5;
  if (!this->field_mask_field().empty()) {
    typedef ::google::protobuf::Map< ::google::protobuf::int32, ::google::protobuf::FieldMask >::const_pointer
        ConstPtr;
    typedef ::google::protobuf::internal::SortItem< ::google::protobuf::int32, ConstPtr > SortItem;
    typedef ::google::protobuf::internal::CompareByFirstField<SortItem> Less;

    if (deterministic &&
        this->field_mask_field().size() > 1) {
      ::google::protobuf::scoped_array<SortItem> items(
          new SortItem[this->field_mask_field().size()]);
      typedef ::google::protobuf::Map< ::google::protobuf::int32, ::google::protobuf::FieldMask >::size_type size_type;
      size_type n = 0;
      for (::google::protobuf::Map< ::google::protobuf::int32, ::google::protobuf::FieldMask >::const_iterator
          it = this->field_mask_field().begin();
          it != this->field_mask_field().end(); ++it, ++n) {
        items[n] = SortItem(&*it);
      }
      ::std::sort(&items[0], &items[n], Less());
      ::google::protobuf::scoped_ptr<MapWellKnownTypes_FieldMaskFieldEntry> entry;
      for (size_type i = 0; i < n; i++) {
        entry.reset(field_mask_field_.NewEntryWrapper(
            items[i].second->first, items[i].second->second));
        target = ::google::protobuf::internal::WireFormatLite::
                   InternalWriteMessageNoVirtualToArray(
                       5, *entry, deterministic, target);
;
      }
    } else {
      ::google::protobuf::scoped_ptr<MapWellKnownTypes_FieldMaskFieldEntry> entry;
      for (::google::protobuf::Map< ::google::protobuf::int32, ::google::protobuf::FieldMask >::const_iterator
          it = this->field_mask_field().begin();
          it != this->field_mask_field().end(); ++it) {
        entry.reset(field_mask_field_.NewEntryWrapper(
            it->first, it->second));
        target = ::google::protobuf::internal::WireFormatLite::
                   InternalWriteMessageNoVirtualToArray(
                       5, *entry, deterministic, target);
;
      }
    }
  }

  // map<int32, .google.protobuf.SourceContext> source_context_field = 6;
  if (!this->source_context_field().empty()) {
    typedef ::google::protobuf::Map< ::google::protobuf::int32, ::google::protobuf::SourceContext >::const_pointer
        ConstPtr;
    typedef ::google::protobuf::internal::SortItem< ::google::protobuf::int32, ConstPtr > SortItem;
    typedef ::google::protobuf::internal::CompareByFirstField<SortItem> Less;

    if (deterministic &&
        this->source_context_field().size() > 1) {
      ::google::protobuf::scoped_array<SortItem> items(
          new SortItem[this->source_context_field().size()]);
      typedef ::google::protobuf::Map< ::google::protobuf::int32, ::google::protobuf::SourceContext >::size_type size_type;
      size_type n = 0;
      for (::google::protobuf::Map< ::google::protobuf::int32, ::google::protobuf::SourceContext >::const_iterator
          it = this->source_context_field().begin();
          it != this->source_context_field().end(); ++it, ++n) {
        items[n] = SortItem(&*it);
      }
      ::std::sort(&items[0], &items[n], Less());
      ::google::protobuf::scoped_ptr<MapWellKnownTypes_SourceContextFieldEntry> entry;
      for (size_type i = 0; i < n; i++) {
        entry.reset(source_context_field_.NewEntryWrapper(
            items[i].second->first, items[i].second->second));
        target = ::google::protobuf::internal::WireFormatLite::
                   InternalWriteMessageNoVirtualToArray(
                       6, *entry, deterministic, target);
;
      }
    } else {
      ::google::protobuf::scoped_ptr<MapWellKnownTypes_SourceContextFieldEntry> entry;
      for (::google::protobuf::Map< ::google::protobuf::int32, ::google::protobuf::SourceContext >::const_iterator
          it = this->source_context_field().begin();
          it != this->source_context_field().end(); ++it) {
        entry.reset(source_context_field_.NewEntryWrapper(
            it->first, it->second));
        target = ::google::protobuf::internal::WireFormatLite::
                   InternalWriteMessageNoVirtualToArray(
                       6, *entry, deterministic, target);
;
      }
    }
  }

  // map<int32, .google.protobuf.Struct> struct_field = 7;
  if (!this->struct_field().empty()) {
    typedef ::google::protobuf::Map< ::google::protobuf::int32, ::google::protobuf::Struct >::const_pointer
        ConstPtr;
    typedef ::google::protobuf::internal::SortItem< ::google::protobuf::int32, ConstPtr > SortItem;
    typedef ::google::protobuf::internal::CompareByFirstField<SortItem> Less;

    if (deterministic &&
        this->struct_field().size() > 1) {
      ::google::protobuf::scoped_array<SortItem> items(
          new SortItem[this->struct_field().size()]);
      typedef ::google::protobuf::Map< ::google::protobuf::int32, ::google::protobuf::Struct >::size_type size_type;
      size_type n = 0;
      for (::google::protobuf::Map< ::google::protobuf::int32, ::google::protobuf::Struct >::const_iterator
          it = this->struct_field().begin();
          it != this->struct_field().end(); ++it, ++n) {
        items[n] = SortItem(&*it);
      }
      ::std::sort(&items[0], &items[n], Less());
      ::google::protobuf::scoped_ptr<MapWellKnownTypes_StructFieldEntry> entry;
      for (size_type i = 0; i < n; i++) {
        entry.reset(struct_field_.NewEntryWrapper(
            items[i].second->first, items[i].second->second));
        target = ::google::protobuf::internal::WireFormatLite::
                   InternalWriteMessageNoVirtualToArray(
                       7, *entry, deterministic, target);
;
      }
    } else {
      ::google::protobuf::scoped_ptr<MapWellKnownTypes_StructFieldEntry> entry;
      for (::google::protobuf::Map< ::google::protobuf::int32, ::google::protobuf::Struct >::const_iterator
          it = this->struct_field().begin();
          it != this->struct_field().end(); ++it) {
        entry.reset(struct_field_.NewEntryWrapper(
            it->first, it->second));
        target = ::google::protobuf::internal::WireFormatLite::
                   InternalWriteMessageNoVirtualToArray(
                       7, *entry, deterministic, target);
;
      }
    }
  }

  // map<int32, .google.protobuf.Timestamp> timestamp_field = 8;
  if (!this->timestamp_field().empty()) {
    typedef ::google::protobuf::Map< ::google::protobuf::int32, ::google::protobuf::Timestamp >::const_pointer
        ConstPtr;
    typedef ::google::protobuf::internal::SortItem< ::google::protobuf::int32, ConstPtr > SortItem;
    typedef ::google::protobuf::internal::CompareByFirstField<SortItem> Less;

    if (deterministic &&
        this->timestamp_field().size() > 1) {
      ::google::protobuf::scoped_array<SortItem> items(
          new SortItem[this->timestamp_field().size()]);
      typedef ::google::protobuf::Map< ::google::protobuf::int32, ::google::protobuf::Timestamp >::size_type size_type;
      size_type n = 0;
      for (::google::protobuf::Map< ::google::protobuf::int32, ::google::protobuf::Timestamp >::const_iterator
          it = this->timestamp_field().begin();
          it != this->timestamp_field().end(); ++it, ++n) {
        items[n] = SortItem(&*it);
      }
      ::std::sort(&items[0], &items[n], Less());
      ::google::protobuf::scoped_ptr<MapWellKnownTypes_TimestampFieldEntry> entry;
      for (size_type i = 0; i < n; i++) {
        entry.reset(timestamp_field_.NewEntryWrapper(
            items[i].second->first, items[i].second->second));
        target = ::google::protobuf::internal::WireFormatLite::
                   InternalWriteMessageNoVirtualToArray(
                       8, *entry, deterministic, target);
;
      }
    } else {
      ::google::protobuf::scoped_ptr<MapWellKnownTypes_TimestampFieldEntry> entry;
      for (::google::protobuf::Map< ::google::protobuf::int32, ::google::protobuf::Timestamp >::const_iterator
          it = this->timestamp_field().begin();
          it != this->timestamp_field().end(); ++it) {
        entry.reset(timestamp_field_.NewEntryWrapper(
            it->first, it->second));
        target = ::google::protobuf::internal::WireFormatLite::
                   InternalWriteMessageNoVirtualToArray(
                       8, *entry, deterministic, target);
;
      }
    }
  }

  // map<int32, .google.protobuf.Type> type_field = 9;
  if (!this->type_field().empty()) {
    typedef ::google::protobuf::Map< ::google::protobuf::int32, ::google::protobuf::Type >::const_pointer
        ConstPtr;
    typedef ::google::protobuf::internal::SortItem< ::google::protobuf::int32, ConstPtr > SortItem;
    typedef ::google::protobuf::internal::CompareByFirstField<SortItem> Less;

    if (deterministic &&
        this->type_field().size() > 1) {
      ::google::protobuf::scoped_array<SortItem> items(
          new SortItem[this->type_field().size()]);
      typedef ::google::protobuf::Map< ::google::protobuf::int32, ::google::protobuf::Type >::size_type size_type;
      size_type n = 0;
      for (::google::protobuf::Map< ::google::protobuf::int32, ::google::protobuf::Type >::const_iterator
          it = this->type_field().begin();
          it != this->type_field().end(); ++it, ++n) {
        items[n] = SortItem(&*it);
      }
      ::std::sort(&items[0], &items[n], Less());
      ::google::protobuf::scoped_ptr<MapWellKnownTypes_TypeFieldEntry> entry;
      for (size_type i = 0; i < n; i++) {
        entry.reset(type_field_.NewEntryWrapper(
            items[i].second->first, items[i].second->second));
        target = ::google::protobuf::internal::WireFormatLite::
                   InternalWriteMessageNoVirtualToArray(
                       9, *entry, deterministic, target);
;
      }
    } else {
      ::google::protobuf::scoped_ptr<MapWellKnownTypes_TypeFieldEntry> entry;
      for (::google::protobuf::Map< ::google::protobuf::int32, ::google::protobuf::Type >::const_iterator
          it = this->type_field().begin();
          it != this->type_field().end(); ++it) {
        entry.reset(type_field_.NewEntryWrapper(
            it->first, it->second));
        target = ::google::protobuf::internal::WireFormatLite::
                   InternalWriteMessageNoVirtualToArray(
                       9, *entry, deterministic, target);
;
      }
    }
  }

  // map<int32, .google.protobuf.DoubleValue> double_field = 10;
  if (!this->double_field().empty()) {
    typedef ::google::protobuf::Map< ::google::protobuf::int32, ::google::protobuf::DoubleValue >::const_pointer
        ConstPtr;
    typedef ::google::protobuf::internal::SortItem< ::google::protobuf::int32, ConstPtr > SortItem;
    typedef ::google::protobuf::internal::CompareByFirstField<SortItem> Less;

    if (deterministic &&
        this->double_field().size() > 1) {
      ::google::protobuf::scoped_array<SortItem> items(
          new SortItem[this->double_field().size()]);
      typedef ::google::protobuf::Map< ::google::protobuf::int32, ::google::protobuf::DoubleValue >::size_type size_type;
      size_type n = 0;
      for (::google::protobuf::Map< ::google::protobuf::int32, ::google::protobuf::DoubleValue >::const_iterator
          it = this->double_field().begin();
          it != this->double_field().end(); ++it, ++n) {
        items[n] = SortItem(&*it);
      }
      ::std::sort(&items[0], &items[n], Less());
      ::google::protobuf::scoped_ptr<MapWellKnownTypes_DoubleFieldEntry> entry;
      for (size_type i = 0; i < n; i++) {
        entry.reset(double_field_.NewEntryWrapper(
            items[i].second->first, items[i].second->second));
        target = ::google::protobuf::internal::WireFormatLite::
                   InternalWriteMessageNoVirtualToArray(
                       10, *entry, deterministic, target);
;
      }
    } else {
      ::google::protobuf::scoped_ptr<MapWellKnownTypes_DoubleFieldEntry> entry;
      for (::google::protobuf::Map< ::google::protobuf::int32, ::google::protobuf::DoubleValue >::const_iterator
          it = this->double_field().begin();
          it != this->double_field().end(); ++it) {
        entry.reset(double_field_.NewEntryWrapper(
            it->first, it->second));
        target = ::google::protobuf::internal::WireFormatLite::
                   InternalWriteMessageNoVirtualToArray(
                       10, *entry, deterministic, target);
;
      }
    }
  }

  // map<int32, .google.protobuf.FloatValue> float_field = 11;
  if (!this->float_field().empty()) {
    typedef ::google::protobuf::Map< ::google::protobuf::int32, ::google::protobuf::FloatValue >::const_pointer
        ConstPtr;
    typedef ::google::protobuf::internal::SortItem< ::google::protobuf::int32, ConstPtr > SortItem;
    typedef ::google::protobuf::internal::CompareByFirstField<SortItem> Less;

    if (deterministic &&
        this->float_field().size() > 1) {
      ::google::protobuf::scoped_array<SortItem> items(
          new SortItem[this->float_field().size()]);
      typedef ::google::protobuf::Map< ::google::protobuf::int32, ::google::protobuf::FloatValue >::size_type size_type;
      size_type n = 0;
      for (::google::protobuf::Map< ::google::protobuf::int32, ::google::protobuf::FloatValue >::const_iterator
          it = this->float_field().begin();
          it != this->float_field().end(); ++it, ++n) {
        items[n] = SortItem(&*it);
      }
      ::std::sort(&items[0], &items[n], Less());
      ::google::protobuf::scoped_ptr<MapWellKnownTypes_FloatFieldEntry> entry;
      for (size_type i = 0; i < n; i++) {
        entry.reset(float_field_.NewEntryWrapper(
            items[i].second->first, items[i].second->second));
        target = ::google::protobuf::internal::WireFormatLite::
                   InternalWriteMessageNoVirtualToArray(
                       11, *entry, deterministic, target);
;
      }
    } else {
      ::google::protobuf::scoped_ptr<MapWellKnownTypes_FloatFieldEntry> entry;
      for (::google::protobuf::Map< ::google::protobuf::int32, ::google::protobuf::FloatValue >::const_iterator
          it = this->float_field().begin();
          it != this->float_field().end(); ++it) {
        entry.reset(float_field_.NewEntryWrapper(
            it->first, it->second));
        target = ::google::protobuf::internal::WireFormatLite::
                   InternalWriteMessageNoVirtualToArray(
                       11, *entry, deterministic, target);
;
      }
    }
  }

  // map<int32, .google.protobuf.Int64Value> int64_field = 12;
  if (!this->int64_field().empty()) {
    typedef ::google::protobuf::Map< ::google::protobuf::int32, ::google::protobuf::Int64Value >::const_pointer
        ConstPtr;
    typedef ::google::protobuf::internal::SortItem< ::google::protobuf::int32, ConstPtr > SortItem;
    typedef ::google::protobuf::internal::CompareByFirstField<SortItem> Less;

    if (deterministic &&
        this->int64_field().size() > 1) {
      ::google::protobuf::scoped_array<SortItem> items(
          new SortItem[this->int64_field().size()]);
      typedef ::google::protobuf::Map< ::google::protobuf::int32, ::google::protobuf::Int64Value >::size_type size_type;
      size_type n = 0;
      for (::google::protobuf::Map< ::google::protobuf::int32, ::google::protobuf::Int64Value >::const_iterator
          it = this->int64_field().begin();
          it != this->int64_field().end(); ++it, ++n) {
        items[n] = SortItem(&*it);
      }
      ::std::sort(&items[0], &items[n], Less());
      ::google::protobuf::scoped_ptr<MapWellKnownTypes_Int64FieldEntry> entry;
      for (size_type i = 0; i < n; i++) {
        entry.reset(int64_field_.NewEntryWrapper(
            items[i].second->first, items[i].second->second));
        target = ::google::protobuf::internal::WireFormatLite::
                   InternalWriteMessageNoVirtualToArray(
                       12, *entry, deterministic, target);
;
      }
    } else {
      ::google::protobuf::scoped_ptr<MapWellKnownTypes_Int64FieldEntry> entry;
      for (::google::protobuf::Map< ::google::protobuf::int32, ::google::protobuf::Int64Value >::const_iterator
          it = this->int64_field().begin();
          it != this->int64_field().end(); ++it) {
        entry.reset(int64_field_.NewEntryWrapper(
            it->first, it->second));
        target = ::google::protobuf::internal::WireFormatLite::
                   InternalWriteMessageNoVirtualToArray(
                       12, *entry, deterministic, target);
;
      }
    }
  }

  // map<int32, .google.protobuf.UInt64Value> uint64_field = 13;
  if (!this->uint64_field().empty()) {
    typedef ::google::protobuf::Map< ::google::protobuf::int32, ::google::protobuf::UInt64Value >::const_pointer
        ConstPtr;
    typedef ::google::protobuf::internal::SortItem< ::google::protobuf::int32, ConstPtr > SortItem;
    typedef ::google::protobuf::internal::CompareByFirstField<SortItem> Less;

    if (deterministic &&
        this->uint64_field().size() > 1) {
      ::google::protobuf::scoped_array<SortItem> items(
          new SortItem[this->uint64_field().size()]);
      typedef ::google::protobuf::Map< ::google::protobuf::int32, ::google::protobuf::UInt64Value >::size_type size_type;
      size_type n = 0;
      for (::google::protobuf::Map< ::google::protobuf::int32, ::google::protobuf::UInt64Value >::const_iterator
          it = this->uint64_field().begin();
          it != this->uint64_field().end(); ++it, ++n) {
        items[n] = SortItem(&*it);
      }
      ::std::sort(&items[0], &items[n], Less());
      ::google::protobuf::scoped_ptr<MapWellKnownTypes_Uint64FieldEntry> entry;
      for (size_type i = 0; i < n; i++) {
        entry.reset(uint64_field_.NewEntryWrapper(
            items[i].second->first, items[i].second->second));
        target = ::google::protobuf::internal::WireFormatLite::
                   InternalWriteMessageNoVirtualToArray(
                       13, *entry, deterministic, target);
;
      }
    } else {
      ::google::protobuf::scoped_ptr<MapWellKnownTypes_Uint64FieldEntry> entry;
      for (::google::protobuf::Map< ::google::protobuf::int32, ::google::protobuf::UInt64Value >::const_iterator
          it = this->uint64_field().begin();
          it != this->uint64_field().end(); ++it) {
        entry.reset(uint64_field_.NewEntryWrapper(
            it->first, it->second));
        target = ::google::protobuf::internal::WireFormatLite::
                   InternalWriteMessageNoVirtualToArray(
                       13, *entry, deterministic, target);
;
      }
    }
  }

  // map<int32, .google.protobuf.Int32Value> int32_field = 14;
  if (!this->int32_field().empty()) {
    typedef ::google::protobuf::Map< ::google::protobuf::int32, ::google::protobuf::Int32Value >::const_pointer
        ConstPtr;
    typedef ::google::protobuf::internal::SortItem< ::google::protobuf::int32, ConstPtr > SortItem;
    typedef ::google::protobuf::internal::CompareByFirstField<SortItem> Less;

    if (deterministic &&
        this->int32_field().size() > 1) {
      ::google::protobuf::scoped_array<SortItem> items(
          new SortItem[this->int32_field().size()]);
      typedef ::google::protobuf::Map< ::google::protobuf::int32, ::google::protobuf::Int32Value >::size_type size_type;
      size_type n = 0;
      for (::google::protobuf::Map< ::google::protobuf::int32, ::google::protobuf::Int32Value >::const_iterator
          it = this->int32_field().begin();
          it != this->int32_field().end(); ++it, ++n) {
        items[n] = SortItem(&*it);
      }
      ::std::sort(&items[0], &items[n], Less());
      ::google::protobuf::scoped_ptr<MapWellKnownTypes_Int32FieldEntry> entry;
      for (size_type i = 0; i < n; i++) {
        entry.reset(int32_field_.NewEntryWrapper(
            items[i].second->first, items[i].second->second));
        target = ::google::protobuf::internal::WireFormatLite::
                   InternalWriteMessageNoVirtualToArray(
                       14, *entry, deterministic, target);
;
      }
    } else {
      ::google::protobuf::scoped_ptr<MapWellKnownTypes_Int32FieldEntry> entry;
      for (::google::protobuf::Map< ::google::protobuf::int32, ::google::protobuf::Int32Value >::const_iterator
          it = this->int32_field().begin();
          it != this->int32_field().end(); ++it) {
        entry.reset(int32_field_.NewEntryWrapper(
            it->first, it->second));
        target = ::google::protobuf::internal::WireFormatLite::
                   InternalWriteMessageNoVirtualToArray(
                       14, *entry, deterministic, target);
;
      }
    }
  }

  // map<int32, .google.protobuf.UInt32Value> uint32_field = 15;
  if (!this->uint32_field().empty()) {
    typedef ::google::protobuf::Map< ::google::protobuf::int32, ::google::protobuf::UInt32Value >::const_pointer
        ConstPtr;
    typedef ::google::protobuf::internal::SortItem< ::google::protobuf::int32, ConstPtr > SortItem;
    typedef ::google::protobuf::internal::CompareByFirstField<SortItem> Less;

    if (deterministic &&
        this->uint32_field().size() > 1) {
      ::google::protobuf::scoped_array<SortItem> items(
          new SortItem[this->uint32_field().size()]);
      typedef ::google::protobuf::Map< ::google::protobuf::int32, ::google::protobuf::UInt32Value >::size_type size_type;
      size_type n = 0;
      for (::google::protobuf::Map< ::google::protobuf::int32, ::google::protobuf::UInt32Value >::const_iterator
          it = this->uint32_field().begin();
          it != this->uint32_field().end(); ++it, ++n) {
        items[n] = SortItem(&*it);
      }
      ::std::sort(&items[0], &items[n], Less());
      ::google::protobuf::scoped_ptr<MapWellKnownTypes_Uint32FieldEntry> entry;
      for (size_type i = 0; i < n; i++) {
        entry.reset(uint32_field_.NewEntryWrapper(
            items[i].second->first, items[i].second->second));
        target = ::google::protobuf::internal::WireFormatLite::
                   InternalWriteMessageNoVirtualToArray(
                       15, *entry, deterministic, target);
;
      }
    } else {
      ::google::protobuf::scoped_ptr<MapWellKnownTypes_Uint32FieldEntry> entry;
      for (::google::protobuf::Map< ::google::protobuf::int32, ::google::protobuf::UInt32Value >::const_iterator
          it = this->uint32_field().begin();
          it != this->uint32_field().end(); ++it) {
        entry.reset(uint32_field_.NewEntryWrapper(
            it->first, it->second));
        target = ::google::protobuf::internal::WireFormatLite::
                   InternalWriteMessageNoVirtualToArray(
                       15, *entry, deterministic, target);
;
      }
    }
  }

  // map<int32, .google.protobuf.BoolValue> bool_field = 16;
  if (!this->bool_field().empty()) {
    typedef ::google::protobuf::Map< ::google::protobuf::int32, ::google::protobuf::BoolValue >::const_pointer
        ConstPtr;
    typedef ::google::protobuf::internal::SortItem< ::google::protobuf::int32, ConstPtr > SortItem;
    typedef ::google::protobuf::internal::CompareByFirstField<SortItem> Less;

    if (deterministic &&
        this->bool_field().size() > 1) {
      ::google::protobuf::scoped_array<SortItem> items(
          new SortItem[this->bool_field().size()]);
      typedef ::google::protobuf::Map< ::google::protobuf::int32, ::google::protobuf::BoolValue >::size_type size_type;
      size_type n = 0;
      for (::google::protobuf::Map< ::google::protobuf::int32, ::google::protobuf::BoolValue >::const_iterator
          it = this->bool_field().begin();
          it != this->bool_field().end(); ++it, ++n) {
        items[n] = SortItem(&*it);
      }
      ::std::sort(&items[0], &items[n], Less());
      ::google::protobuf::scoped_ptr<MapWellKnownTypes_BoolFieldEntry> entry;
      for (size_type i = 0; i < n; i++) {
        entry.reset(bool_field_.NewEntryWrapper(
            items[i].second->first, items[i].second->second));
        target = ::google::protobuf::internal::WireFormatLite::
                   InternalWriteMessageNoVirtualToArray(
                       16, *entry, deterministic, target);
;
      }
    } else {
      ::google::protobuf::scoped_ptr<MapWellKnownTypes_BoolFieldEntry> entry;
      for (::google::protobuf::Map< ::google::protobuf::int32, ::google::protobuf::BoolValue >::const_iterator
          it = this->bool_field().begin();
          it != this->bool_field().end(); ++it) {
        entry.reset(bool_field_.NewEntryWrapper(
            it->first, it->second));
        target = ::google::protobuf::internal::WireFormatLite::
                   InternalWriteMessageNoVirtualToArray(
                       16, *entry, deterministic, target);
;
      }
    }
  }

  // map<int32, .google.protobuf.StringValue> string_field = 17;
  if (!this->string_field().empty()) {
    typedef ::google::protobuf::Map< ::google::protobuf::int32, ::google::protobuf::StringValue >::const_pointer
        ConstPtr;
    typedef ::google::protobuf::internal::SortItem< ::google::protobuf::int32, ConstPtr > SortItem;
    typedef ::google::protobuf::internal::CompareByFirstField<SortItem> Less;

    if (deterministic &&
        this->string_field().size() > 1) {
      ::google::protobuf::scoped_array<SortItem> items(
          new SortItem[this->string_field().size()]);
      typedef ::google::protobuf::Map< ::google::protobuf::int32, ::google::protobuf::StringValue >::size_type size_type;
      size_type n = 0;
      for (::google::protobuf::Map< ::google::protobuf::int32, ::google::protobuf::StringValue >::const_iterator
          it = this->string_field().begin();
          it != this->string_field().end(); ++it, ++n) {
        items[n] = SortItem(&*it);
      }
      ::std::sort(&items[0], &items[n], Less());
      ::google::protobuf::scoped_ptr<MapWellKnownTypes_StringFieldEntry> entry;
      for (size_type i = 0; i < n; i++) {
        entry.reset(string_field_.NewEntryWrapper(
            items[i].second->first, items[i].second->second));
        target = ::google::protobuf::internal::WireFormatLite::
                   InternalWriteMessageNoVirtualToArray(
                       17, *entry, deterministic, target);
;
      }
    } else {
      ::google::protobuf::scoped_ptr<MapWellKnownTypes_StringFieldEntry> entry;
      for (::google::protobuf::Map< ::google::protobuf::int32, ::google::protobuf::StringValue >::const_iterator
          it = this->string_field().begin();
          it != this->string_field().end(); ++it) {
        entry.reset(string_field_.NewEntryWrapper(
            it->first, it->second));
        target = ::google::protobuf::internal::WireFormatLite::
                   InternalWriteMessageNoVirtualToArray(
                       17, *entry, deterministic, target);
;
      }
    }
  }

  // map<int32, .google.protobuf.BytesValue> bytes_field = 18;
  if (!this->bytes_field().empty()) {
    typedef ::google::protobuf::Map< ::google::protobuf::int32, ::google::protobuf::BytesValue >::const_pointer
        ConstPtr;
    typedef ::google::protobuf::internal::SortItem< ::google::protobuf::int32, ConstPtr > SortItem;
    typedef ::google::protobuf::internal::CompareByFirstField<SortItem> Less;

    if (deterministic &&
        this->bytes_field().size() > 1) {
      ::google::protobuf::scoped_array<SortItem> items(
          new SortItem[this->bytes_field().size()]);
      typedef ::google::protobuf::Map< ::google::protobuf::int32, ::google::protobuf::BytesValue >::size_type size_type;
      size_type n = 0;
      for (::google::protobuf::Map< ::google::protobuf::int32, ::google::protobuf::BytesValue >::const_iterator
          it = this->bytes_field().begin();
          it != this->bytes_field().end(); ++it, ++n) {
        items[n] = SortItem(&*it);
      }
      ::std::sort(&items[0], &items[n], Less());
      ::google::protobuf::scoped_ptr<MapWellKnownTypes_BytesFieldEntry> entry;
      for (size_type i = 0; i < n; i++) {
        entry.reset(bytes_field_.NewEntryWrapper(
            items[i].second->first, items[i].second->second));
        target = ::google::protobuf::internal::WireFormatLite::
                   InternalWriteMessageNoVirtualToArray(
                       18, *entry, deterministic, target);
;
      }
    } else {
      ::google::protobuf::scoped_ptr<MapWellKnownTypes_BytesFieldEntry> entry;
      for (::google::protobuf::Map< ::google::protobuf::int32, ::google::protobuf::BytesValue >::const_iterator
          it = this->bytes_field().begin();
          it != this->bytes_field().end(); ++it) {
        entry.reset(bytes_field_.NewEntryWrapper(
            it->first, it->second));
        target = ::google::protobuf::internal::WireFormatLite::
                   InternalWriteMessageNoVirtualToArray(
                       18, *entry, deterministic, target);
;
      }
    }
  }

  // @@protoc_insertion_point(serialize_to_array_end:protobuf_unittest.MapWellKnownTypes)
  return target;
}

size_t MapWellKnownTypes::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:protobuf_unittest.MapWellKnownTypes)
  size_t total_size = 0;

  // map<int32, .google.protobuf.Any> any_field = 1;
  total_size += 1 *
      ::google::protobuf::internal::FromIntSize(this->any_field_size());
  {
    ::google::protobuf::scoped_ptr<MapWellKnownTypes_AnyFieldEntry> entry;
    for (::google::protobuf::Map< ::google::protobuf::int32, ::google::protobuf::Any >::const_iterator
        it = this->any_field().begin();
        it != this->any_field().end(); ++it) {
      entry.reset(any_field_.NewEntryWrapper(it->first, it->second));
      total_size += ::google::protobuf::internal::WireFormatLite::
          MessageSizeNoVirtual(*entry);
    }
  }

  // map<int32, .google.protobuf.Api> api_field = 2;
  total_size += 1 *
      ::google::protobuf::internal::FromIntSize(this->api_field_size());
  {
    ::google::protobuf::scoped_ptr<MapWellKnownTypes_ApiFieldEntry> entry;
    for (::google::protobuf::Map< ::google::protobuf::int32, ::google::protobuf::Api >::const_iterator
        it = this->api_field().begin();
        it != this->api_field().end(); ++it) {
      entry.reset(api_field_.NewEntryWrapper(it->first, it->second));
      total_size += ::google::protobuf::internal::WireFormatLite::
          MessageSizeNoVirtual(*entry);
    }
  }

  // map<int32, .google.protobuf.Duration> duration_field = 3;
  total_size += 1 *
      ::google::protobuf::internal::FromIntSize(this->duration_field_size());
  {
    ::google::protobuf::scoped_ptr<MapWellKnownTypes_DurationFieldEntry> entry;
    for (::google::protobuf::Map< ::google::protobuf::int32, ::google::protobuf::Duration >::const_iterator
        it = this->duration_field().begin();
        it != this->duration_field().end(); ++it) {
      entry.reset(duration_field_.NewEntryWrapper(it->first, it->second));
      total_size += ::google::protobuf::internal::WireFormatLite::
          MessageSizeNoVirtual(*entry);
    }
  }

  // map<int32, .google.protobuf.Empty> empty_field = 4;
  total_size += 1 *
      ::google::protobuf::internal::FromIntSize(this->empty_field_size());
  {
    ::google::protobuf::scoped_ptr<MapWellKnownTypes_EmptyFieldEntry> entry;
    for (::google::protobuf::Map< ::google::protobuf::int32, ::google::protobuf::Empty >::const_iterator
        it = this->empty_field().begin();
        it != this->empty_field().end(); ++it) {
      entry.reset(empty_field_.NewEntryWrapper(it->first, it->second));
      total_size += ::google::protobuf::internal::WireFormatLite::
          MessageSizeNoVirtual(*entry);
    }
  }

  // map<int32, .google.protobuf.FieldMask> field_mask_field = 5;
  total_size += 1 *
      ::google::protobuf::internal::FromIntSize(this->field_mask_field_size());
  {
    ::google::protobuf::scoped_ptr<MapWellKnownTypes_FieldMaskFieldEntry> entry;
    for (::google::protobuf::Map< ::google::protobuf::int32, ::google::protobuf::FieldMask >::const_iterator
        it = this->field_mask_field().begin();
        it != this->field_mask_field().end(); ++it) {
      entry.reset(field_mask_field_.NewEntryWrapper(it->first, it->second));
      total_size += ::google::protobuf::internal::WireFormatLite::
          MessageSizeNoVirtual(*entry);
    }
  }

  // map<int32, .google.protobuf.SourceContext> source_context_field = 6;
  total_size += 1 *
      ::google::protobuf::internal::FromIntSize(this->source_context_field_size());
  {
    ::google::protobuf::scoped_ptr<MapWellKnownTypes_SourceContextFieldEntry> entry;
    for (::google::protobuf::Map< ::google::protobuf::int32, ::google::protobuf::SourceContext >::const_iterator
        it = this->source_context_field().begin();
        it != this->source_context_field().end(); ++it) {
      entry.reset(source_context_field_.NewEntryWrapper(it->first, it->second));
      total_size += ::google::protobuf::internal::WireFormatLite::
          MessageSizeNoVirtual(*entry);
    }
  }

  // map<int32, .google.protobuf.Struct> struct_field = 7;
  total_size += 1 *
      ::google::protobuf::internal::FromIntSize(this->struct_field_size());
  {
    ::google::protobuf::scoped_ptr<MapWellKnownTypes_StructFieldEntry> entry;
    for (::google::protobuf::Map< ::google::protobuf::int32, ::google::protobuf::Struct >::const_iterator
        it = this->struct_field().begin();
        it != this->struct_field().end(); ++it) {
      entry.reset(struct_field_.NewEntryWrapper(it->first, it->second));
      total_size += ::google::protobuf::internal::WireFormatLite::
          MessageSizeNoVirtual(*entry);
    }
  }

  // map<int32, .google.protobuf.Timestamp> timestamp_field = 8;
  total_size += 1 *
      ::google::protobuf::internal::FromIntSize(this->timestamp_field_size());
  {
    ::google::protobuf::scoped_ptr<MapWellKnownTypes_TimestampFieldEntry> entry;
    for (::google::protobuf::Map< ::google::protobuf::int32, ::google::protobuf::Timestamp >::const_iterator
        it = this->timestamp_field().begin();
        it != this->timestamp_field().end(); ++it) {
      entry.reset(timestamp_field_.NewEntryWrapper(it->first, it->second));
      total_size += ::google::protobuf::internal::WireFormatLite::
          MessageSizeNoVirtual(*entry);
    }
  }

  // map<int32, .google.protobuf.Type> type_field = 9;
  total_size += 1 *
      ::google::protobuf::internal::FromIntSize(this->type_field_size());
  {
    ::google::protobuf::scoped_ptr<MapWellKnownTypes_TypeFieldEntry> entry;
    for (::google::protobuf::Map< ::google::protobuf::int32, ::google::protobuf::Type >::const_iterator
        it = this->type_field().begin();
        it != this->type_field().end(); ++it) {
      entry.reset(type_field_.NewEntryWrapper(it->first, it->second));
      total_size += ::google::protobuf::internal::WireFormatLite::
          MessageSizeNoVirtual(*entry);
    }
  }

  // map<int32, .google.protobuf.DoubleValue> double_field = 10;
  total_size += 1 *
      ::google::protobuf::internal::FromIntSize(this->double_field_size());
  {
    ::google::protobuf::scoped_ptr<MapWellKnownTypes_DoubleFieldEntry> entry;
    for (::google::protobuf::Map< ::google::protobuf::int32, ::google::protobuf::DoubleValue >::const_iterator
        it = this->double_field().begin();
        it != this->double_field().end(); ++it) {
      entry.reset(double_field_.NewEntryWrapper(it->first, it->second));
      total_size += ::google::protobuf::internal::WireFormatLite::
          MessageSizeNoVirtual(*entry);
    }
  }

  // map<int32, .google.protobuf.FloatValue> float_field = 11;
  total_size += 1 *
      ::google::protobuf::internal::FromIntSize(this->float_field_size());
  {
    ::google::protobuf::scoped_ptr<MapWellKnownTypes_FloatFieldEntry> entry;
    for (::google::protobuf::Map< ::google::protobuf::int32, ::google::protobuf::FloatValue >::const_iterator
        it = this->float_field().begin();
        it != this->float_field().end(); ++it) {
      entry.reset(float_field_.NewEntryWrapper(it->first, it->second));
      total_size += ::google::protobuf::internal::WireFormatLite::
          MessageSizeNoVirtual(*entry);
    }
  }

  // map<int32, .google.protobuf.Int64Value> int64_field = 12;
  total_size += 1 *
      ::google::protobuf::internal::FromIntSize(this->int64_field_size());
  {
    ::google::protobuf::scoped_ptr<MapWellKnownTypes_Int64FieldEntry> entry;
    for (::google::protobuf::Map< ::google::protobuf::int32, ::google::protobuf::Int64Value >::const_iterator
        it = this->int64_field().begin();
        it != this->int64_field().end(); ++it) {
      entry.reset(int64_field_.NewEntryWrapper(it->first, it->second));
      total_size += ::google::protobuf::internal::WireFormatLite::
          MessageSizeNoVirtual(*entry);
    }
  }

  // map<int32, .google.protobuf.UInt64Value> uint64_field = 13;
  total_size += 1 *
      ::google::protobuf::internal::FromIntSize(this->uint64_field_size());
  {
    ::google::protobuf::scoped_ptr<MapWellKnownTypes_Uint64FieldEntry> entry;
    for (::google::protobuf::Map< ::google::protobuf::int32, ::google::protobuf::UInt64Value >::const_iterator
        it = this->uint64_field().begin();
        it != this->uint64_field().end(); ++it) {
      entry.reset(uint64_field_.NewEntryWrapper(it->first, it->second));
      total_size += ::google::protobuf::internal::WireFormatLite::
          MessageSizeNoVirtual(*entry);
    }
  }

  // map<int32, .google.protobuf.Int32Value> int32_field = 14;
  total_size += 1 *
      ::google::protobuf::internal::FromIntSize(this->int32_field_size());
  {
    ::google::protobuf::scoped_ptr<MapWellKnownTypes_Int32FieldEntry> entry;
    for (::google::protobuf::Map< ::google::protobuf::int32, ::google::protobuf::Int32Value >::const_iterator
        it = this->int32_field().begin();
        it != this->int32_field().end(); ++it) {
      entry.reset(int32_field_.NewEntryWrapper(it->first, it->second));
      total_size += ::google::protobuf::internal::WireFormatLite::
          MessageSizeNoVirtual(*entry);
    }
  }

  // map<int32, .google.protobuf.UInt32Value> uint32_field = 15;
  total_size += 1 *
      ::google::protobuf::internal::FromIntSize(this->uint32_field_size());
  {
    ::google::protobuf::scoped_ptr<MapWellKnownTypes_Uint32FieldEntry> entry;
    for (::google::protobuf::Map< ::google::protobuf::int32, ::google::protobuf::UInt32Value >::const_iterator
        it = this->uint32_field().begin();
        it != this->uint32_field().end(); ++it) {
      entry.reset(uint32_field_.NewEntryWrapper(it->first, it->second));
      total_size += ::google::protobuf::internal::WireFormatLite::
          MessageSizeNoVirtual(*entry);
    }
  }

  // map<int32, .google.protobuf.BoolValue> bool_field = 16;
  total_size += 2 *
      ::google::protobuf::internal::FromIntSize(this->bool_field_size());
  {
    ::google::protobuf::scoped_ptr<MapWellKnownTypes_BoolFieldEntry> entry;
    for (::google::protobuf::Map< ::google::protobuf::int32, ::google::protobuf::BoolValue >::const_iterator
        it = this->bool_field().begin();
        it != this->bool_field().end(); ++it) {
      entry.reset(bool_field_.NewEntryWrapper(it->first, it->second));
      total_size += ::google::protobuf::internal::WireFormatLite::
          MessageSizeNoVirtual(*entry);
    }
  }

  // map<int32, .google.protobuf.StringValue> string_field = 17;
  total_size += 2 *
      ::google::protobuf::internal::FromIntSize(this->string_field_size());
  {
    ::google::protobuf::scoped_ptr<MapWellKnownTypes_StringFieldEntry> entry;
    for (::google::protobuf::Map< ::google::protobuf::int32, ::google::protobuf::StringValue >::const_iterator
        it = this->string_field().begin();
        it != this->string_field().end(); ++it) {
      entry.reset(string_field_.NewEntryWrapper(it->first, it->second));
      total_size += ::google::protobuf::internal::WireFormatLite::
          MessageSizeNoVirtual(*entry);
    }
  }

  // map<int32, .google.protobuf.BytesValue> bytes_field = 18;
  total_size += 2 *
      ::google::protobuf::internal::FromIntSize(this->bytes_field_size());
  {
    ::google::protobuf::scoped_ptr<MapWellKnownTypes_BytesFieldEntry> entry;
    for (::google::protobuf::Map< ::google::protobuf::int32, ::google::protobuf::BytesValue >::const_iterator
        it = this->bytes_field().begin();
        it != this->bytes_field().end(); ++it) {
      entry.reset(bytes_field_.NewEntryWrapper(it->first, it->second));
      total_size += ::google::protobuf::internal::WireFormatLite::
          MessageSizeNoVirtual(*entry);
    }
  }

  int cached_size = ::google::protobuf::internal::ToCachedSize(total_size);
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = cached_size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
  return total_size;
}

void MapWellKnownTypes::MergeFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_merge_from_start:protobuf_unittest.MapWellKnownTypes)
  if (GOOGLE_PREDICT_FALSE(&from == this)) MergeFromFail(__LINE__);
  const MapWellKnownTypes* source =
      ::google::protobuf::internal::DynamicCastToGenerated<const MapWellKnownTypes>(
          &from);
  if (source == NULL) {
  // @@protoc_insertion_point(generalized_merge_from_cast_fail:protobuf_unittest.MapWellKnownTypes)
    ::google::protobuf::internal::ReflectionOps::Merge(from, this);
  } else {
  // @@protoc_insertion_point(generalized_merge_from_cast_success:protobuf_unittest.MapWellKnownTypes)
    UnsafeMergeFrom(*source);
  }
}

void MapWellKnownTypes::MergeFrom(const MapWellKnownTypes& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:protobuf_unittest.MapWellKnownTypes)
  if (GOOGLE_PREDICT_TRUE(&from != this)) {
    UnsafeMergeFrom(from);
  } else {
    MergeFromFail(__LINE__);
  }
}

void MapWellKnownTypes::UnsafeMergeFrom(const MapWellKnownTypes& from) {
  GOOGLE_DCHECK(&from != this);
  any_field_.MergeFrom(from.any_field_);
  api_field_.MergeFrom(from.api_field_);
  duration_field_.MergeFrom(from.duration_field_);
  empty_field_.MergeFrom(from.empty_field_);
  field_mask_field_.MergeFrom(from.field_mask_field_);
  source_context_field_.MergeFrom(from.source_context_field_);
  struct_field_.MergeFrom(from.struct_field_);
  timestamp_field_.MergeFrom(from.timestamp_field_);
  type_field_.MergeFrom(from.type_field_);
  double_field_.MergeFrom(from.double_field_);
  float_field_.MergeFrom(from.float_field_);
  int64_field_.MergeFrom(from.int64_field_);
  uint64_field_.MergeFrom(from.uint64_field_);
  int32_field_.MergeFrom(from.int32_field_);
  uint32_field_.MergeFrom(from.uint32_field_);
  bool_field_.MergeFrom(from.bool_field_);
  string_field_.MergeFrom(from.string_field_);
  bytes_field_.MergeFrom(from.bytes_field_);
}

void MapWellKnownTypes::CopyFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_copy_from_start:protobuf_unittest.MapWellKnownTypes)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

void MapWellKnownTypes::CopyFrom(const MapWellKnownTypes& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:protobuf_unittest.MapWellKnownTypes)
  if (&from == this) return;
  Clear();
  UnsafeMergeFrom(from);
}

bool MapWellKnownTypes::IsInitialized() const {

  return true;
}

void MapWellKnownTypes::Swap(MapWellKnownTypes* other) {
  if (other == this) return;
  InternalSwap(other);
}
void MapWellKnownTypes::InternalSwap(MapWellKnownTypes* other) {
  any_field_.Swap(&other->any_field_);
  api_field_.Swap(&other->api_field_);
  duration_field_.Swap(&other->duration_field_);
  empty_field_.Swap(&other->empty_field_);
  field_mask_field_.Swap(&other->field_mask_field_);
  source_context_field_.Swap(&other->source_context_field_);
  struct_field_.Swap(&other->struct_field_);
  timestamp_field_.Swap(&other->timestamp_field_);
  type_field_.Swap(&other->type_field_);
  double_field_.Swap(&other->double_field_);
  float_field_.Swap(&other->float_field_);
  int64_field_.Swap(&other->int64_field_);
  uint64_field_.Swap(&other->uint64_field_);
  int32_field_.Swap(&other->int32_field_);
  uint32_field_.Swap(&other->uint32_field_);
  bool_field_.Swap(&other->bool_field_);
  string_field_.Swap(&other->string_field_);
  bytes_field_.Swap(&other->bytes_field_);
  _internal_metadata_.Swap(&other->_internal_metadata_);
  std::swap(_cached_size_, other->_cached_size_);
}

::google::protobuf::Metadata MapWellKnownTypes::GetMetadata() const {
  protobuf_AssignDescriptorsOnce();
  ::google::protobuf::Metadata metadata;
  metadata.descriptor = MapWellKnownTypes_descriptor_;
  metadata.reflection = MapWellKnownTypes_reflection_;
  return metadata;
}

#if PROTOBUF_INLINE_NOT_IN_HEADERS
// MapWellKnownTypes

// map<int32, .google.protobuf.Any> any_field = 1;
int MapWellKnownTypes::any_field_size() const {
  return any_field_.size();
}
void MapWellKnownTypes::clear_any_field() {
  any_field_.Clear();
}
 const ::google::protobuf::Map< ::google::protobuf::int32, ::google::protobuf::Any >&
MapWellKnownTypes::any_field() const {
  // @@protoc_insertion_point(field_map:protobuf_unittest.MapWellKnownTypes.any_field)
  return any_field_.GetMap();
}
 ::google::protobuf::Map< ::google::protobuf::int32, ::google::protobuf::Any >*
MapWellKnownTypes::mutable_any_field() {
  // @@protoc_insertion_point(field_mutable_map:protobuf_unittest.MapWellKnownTypes.any_field)
  return any_field_.MutableMap();
}

// map<int32, .google.protobuf.Api> api_field = 2;
int MapWellKnownTypes::api_field_size() const {
  return api_field_.size();
}
void MapWellKnownTypes::clear_api_field() {
  api_field_.Clear();
}
 const ::google::protobuf::Map< ::google::protobuf::int32, ::google::protobuf::Api >&
MapWellKnownTypes::api_field() const {
  // @@protoc_insertion_point(field_map:protobuf_unittest.MapWellKnownTypes.api_field)
  return api_field_.GetMap();
}
 ::google::protobuf::Map< ::google::protobuf::int32, ::google::protobuf::Api >*
MapWellKnownTypes::mutable_api_field() {
  // @@protoc_insertion_point(field_mutable_map:protobuf_unittest.MapWellKnownTypes.api_field)
  return api_field_.MutableMap();
}

// map<int32, .google.protobuf.Duration> duration_field = 3;
int MapWellKnownTypes::duration_field_size() const {
  return duration_field_.size();
}
void MapWellKnownTypes::clear_duration_field() {
  duration_field_.Clear();
}
 const ::google::protobuf::Map< ::google::protobuf::int32, ::google::protobuf::Duration >&
MapWellKnownTypes::duration_field() const {
  // @@protoc_insertion_point(field_map:protobuf_unittest.MapWellKnownTypes.duration_field)
  return duration_field_.GetMap();
}
 ::google::protobuf::Map< ::google::protobuf::int32, ::google::protobuf::Duration >*
MapWellKnownTypes::mutable_duration_field() {
  // @@protoc_insertion_point(field_mutable_map:protobuf_unittest.MapWellKnownTypes.duration_field)
  return duration_field_.MutableMap();
}

// map<int32, .google.protobuf.Empty> empty_field = 4;
int MapWellKnownTypes::empty_field_size() const {
  return empty_field_.size();
}
void MapWellKnownTypes::clear_empty_field() {
  empty_field_.Clear();
}
 const ::google::protobuf::Map< ::google::protobuf::int32, ::google::protobuf::Empty >&
MapWellKnownTypes::empty_field() const {
  // @@protoc_insertion_point(field_map:protobuf_unittest.MapWellKnownTypes.empty_field)
  return empty_field_.GetMap();
}
 ::google::protobuf::Map< ::google::protobuf::int32, ::google::protobuf::Empty >*
MapWellKnownTypes::mutable_empty_field() {
  // @@protoc_insertion_point(field_mutable_map:protobuf_unittest.MapWellKnownTypes.empty_field)
  return empty_field_.MutableMap();
}

// map<int32, .google.protobuf.FieldMask> field_mask_field = 5;
int MapWellKnownTypes::field_mask_field_size() const {
  return field_mask_field_.size();
}
void MapWellKnownTypes::clear_field_mask_field() {
  field_mask_field_.Clear();
}
 const ::google::protobuf::Map< ::google::protobuf::int32, ::google::protobuf::FieldMask >&
MapWellKnownTypes::field_mask_field() const {
  // @@protoc_insertion_point(field_map:protobuf_unittest.MapWellKnownTypes.field_mask_field)
  return field_mask_field_.GetMap();
}
 ::google::protobuf::Map< ::google::protobuf::int32, ::google::protobuf::FieldMask >*
MapWellKnownTypes::mutable_field_mask_field() {
  // @@protoc_insertion_point(field_mutable_map:protobuf_unittest.MapWellKnownTypes.field_mask_field)
  return field_mask_field_.MutableMap();
}

// map<int32, .google.protobuf.SourceContext> source_context_field = 6;
int MapWellKnownTypes::source_context_field_size() const {
  return source_context_field_.size();
}
void MapWellKnownTypes::clear_source_context_field() {
  source_context_field_.Clear();
}
 const ::google::protobuf::Map< ::google::protobuf::int32, ::google::protobuf::SourceContext >&
MapWellKnownTypes::source_context_field() const {
  // @@protoc_insertion_point(field_map:protobuf_unittest.MapWellKnownTypes.source_context_field)
  return source_context_field_.GetMap();
}
 ::google::protobuf::Map< ::google::protobuf::int32, ::google::protobuf::SourceContext >*
MapWellKnownTypes::mutable_source_context_field() {
  // @@protoc_insertion_point(field_mutable_map:protobuf_unittest.MapWellKnownTypes.source_context_field)
  return source_context_field_.MutableMap();
}

// map<int32, .google.protobuf.Struct> struct_field = 7;
int MapWellKnownTypes::struct_field_size() const {
  return struct_field_.size();
}
void MapWellKnownTypes::clear_struct_field() {
  struct_field_.Clear();
}
 const ::google::protobuf::Map< ::google::protobuf::int32, ::google::protobuf::Struct >&
MapWellKnownTypes::struct_field() const {
  // @@protoc_insertion_point(field_map:protobuf_unittest.MapWellKnownTypes.struct_field)
  return struct_field_.GetMap();
}
 ::google::protobuf::Map< ::google::protobuf::int32, ::google::protobuf::Struct >*
MapWellKnownTypes::mutable_struct_field() {
  // @@protoc_insertion_point(field_mutable_map:protobuf_unittest.MapWellKnownTypes.struct_field)
  return struct_field_.MutableMap();
}

// map<int32, .google.protobuf.Timestamp> timestamp_field = 8;
int MapWellKnownTypes::timestamp_field_size() const {
  return timestamp_field_.size();
}
void MapWellKnownTypes::clear_timestamp_field() {
  timestamp_field_.Clear();
}
 const ::google::protobuf::Map< ::google::protobuf::int32, ::google::protobuf::Timestamp >&
MapWellKnownTypes::timestamp_field() const {
  // @@protoc_insertion_point(field_map:protobuf_unittest.MapWellKnownTypes.timestamp_field)
  return timestamp_field_.GetMap();
}
 ::google::protobuf::Map< ::google::protobuf::int32, ::google::protobuf::Timestamp >*
MapWellKnownTypes::mutable_timestamp_field() {
  // @@protoc_insertion_point(field_mutable_map:protobuf_unittest.MapWellKnownTypes.timestamp_field)
  return timestamp_field_.MutableMap();
}

// map<int32, .google.protobuf.Type> type_field = 9;
int MapWellKnownTypes::type_field_size() const {
  return type_field_.size();
}
void MapWellKnownTypes::clear_type_field() {
  type_field_.Clear();
}
 const ::google::protobuf::Map< ::google::protobuf::int32, ::google::protobuf::Type >&
MapWellKnownTypes::type_field() const {
  // @@protoc_insertion_point(field_map:protobuf_unittest.MapWellKnownTypes.type_field)
  return type_field_.GetMap();
}
 ::google::protobuf::Map< ::google::protobuf::int32, ::google::protobuf::Type >*
MapWellKnownTypes::mutable_type_field() {
  // @@protoc_insertion_point(field_mutable_map:protobuf_unittest.MapWellKnownTypes.type_field)
  return type_field_.MutableMap();
}

// map<int32, .google.protobuf.DoubleValue> double_field = 10;
int MapWellKnownTypes::double_field_size() const {
  return double_field_.size();
}
void MapWellKnownTypes::clear_double_field() {
  double_field_.Clear();
}
 const ::google::protobuf::Map< ::google::protobuf::int32, ::google::protobuf::DoubleValue >&
MapWellKnownTypes::double_field() const {
  // @@protoc_insertion_point(field_map:protobuf_unittest.MapWellKnownTypes.double_field)
  return double_field_.GetMap();
}
 ::google::protobuf::Map< ::google::protobuf::int32, ::google::protobuf::DoubleValue >*
MapWellKnownTypes::mutable_double_field() {
  // @@protoc_insertion_point(field_mutable_map:protobuf_unittest.MapWellKnownTypes.double_field)
  return double_field_.MutableMap();
}

// map<int32, .google.protobuf.FloatValue> float_field = 11;
int MapWellKnownTypes::float_field_size() const {
  return float_field_.size();
}
void MapWellKnownTypes::clear_float_field() {
  float_field_.Clear();
}
 const ::google::protobuf::Map< ::google::protobuf::int32, ::google::protobuf::FloatValue >&
MapWellKnownTypes::float_field() const {
  // @@protoc_insertion_point(field_map:protobuf_unittest.MapWellKnownTypes.float_field)
  return float_field_.GetMap();
}
 ::google::protobuf::Map< ::google::protobuf::int32, ::google::protobuf::FloatValue >*
MapWellKnownTypes::mutable_float_field() {
  // @@protoc_insertion_point(field_mutable_map:protobuf_unittest.MapWellKnownTypes.float_field)
  return float_field_.MutableMap();
}

// map<int32, .google.protobuf.Int64Value> int64_field = 12;
int MapWellKnownTypes::int64_field_size() const {
  return int64_field_.size();
}
void MapWellKnownTypes::clear_int64_field() {
  int64_field_.Clear();
}
 const ::google::protobuf::Map< ::google::protobuf::int32, ::google::protobuf::Int64Value >&
MapWellKnownTypes::int64_field() const {
  // @@protoc_insertion_point(field_map:protobuf_unittest.MapWellKnownTypes.int64_field)
  return int64_field_.GetMap();
}
 ::google::protobuf::Map< ::google::protobuf::int32, ::google::protobuf::Int64Value >*
MapWellKnownTypes::mutable_int64_field() {
  // @@protoc_insertion_point(field_mutable_map:protobuf_unittest.MapWellKnownTypes.int64_field)
  return int64_field_.MutableMap();
}

// map<int32, .google.protobuf.UInt64Value> uint64_field = 13;
int MapWellKnownTypes::uint64_field_size() const {
  return uint64_field_.size();
}
void MapWellKnownTypes::clear_uint64_field() {
  uint64_field_.Clear();
}
 const ::google::protobuf::Map< ::google::protobuf::int32, ::google::protobuf::UInt64Value >&
MapWellKnownTypes::uint64_field() const {
  // @@protoc_insertion_point(field_map:protobuf_unittest.MapWellKnownTypes.uint64_field)
  return uint64_field_.GetMap();
}
 ::google::protobuf::Map< ::google::protobuf::int32, ::google::protobuf::UInt64Value >*
MapWellKnownTypes::mutable_uint64_field() {
  // @@protoc_insertion_point(field_mutable_map:protobuf_unittest.MapWellKnownTypes.uint64_field)
  return uint64_field_.MutableMap();
}

// map<int32, .google.protobuf.Int32Value> int32_field = 14;
int MapWellKnownTypes::int32_field_size() const {
  return int32_field_.size();
}
void MapWellKnownTypes::clear_int32_field() {
  int32_field_.Clear();
}
 const ::google::protobuf::Map< ::google::protobuf::int32, ::google::protobuf::Int32Value >&
MapWellKnownTypes::int32_field() const {
  // @@protoc_insertion_point(field_map:protobuf_unittest.MapWellKnownTypes.int32_field)
  return int32_field_.GetMap();
}
 ::google::protobuf::Map< ::google::protobuf::int32, ::google::protobuf::Int32Value >*
MapWellKnownTypes::mutable_int32_field() {
  // @@protoc_insertion_point(field_mutable_map:protobuf_unittest.MapWellKnownTypes.int32_field)
  return int32_field_.MutableMap();
}

// map<int32, .google.protobuf.UInt32Value> uint32_field = 15;
int MapWellKnownTypes::uint32_field_size() const {
  return uint32_field_.size();
}
void MapWellKnownTypes::clear_uint32_field() {
  uint32_field_.Clear();
}
 const ::google::protobuf::Map< ::google::protobuf::int32, ::google::protobuf::UInt32Value >&
MapWellKnownTypes::uint32_field() const {
  // @@protoc_insertion_point(field_map:protobuf_unittest.MapWellKnownTypes.uint32_field)
  return uint32_field_.GetMap();
}
 ::google::protobuf::Map< ::google::protobuf::int32, ::google::protobuf::UInt32Value >*
MapWellKnownTypes::mutable_uint32_field() {
  // @@protoc_insertion_point(field_mutable_map:protobuf_unittest.MapWellKnownTypes.uint32_field)
  return uint32_field_.MutableMap();
}

// map<int32, .google.protobuf.BoolValue> bool_field = 16;
int MapWellKnownTypes::bool_field_size() const {
  return bool_field_.size();
}
void MapWellKnownTypes::clear_bool_field() {
  bool_field_.Clear();
}
 const ::google::protobuf::Map< ::google::protobuf::int32, ::google::protobuf::BoolValue >&
MapWellKnownTypes::bool_field() const {
  // @@protoc_insertion_point(field_map:protobuf_unittest.MapWellKnownTypes.bool_field)
  return bool_field_.GetMap();
}
 ::google::protobuf::Map< ::google::protobuf::int32, ::google::protobuf::BoolValue >*
MapWellKnownTypes::mutable_bool_field() {
  // @@protoc_insertion_point(field_mutable_map:protobuf_unittest.MapWellKnownTypes.bool_field)
  return bool_field_.MutableMap();
}

// map<int32, .google.protobuf.StringValue> string_field = 17;
int MapWellKnownTypes::string_field_size() const {
  return string_field_.size();
}
void MapWellKnownTypes::clear_string_field() {
  string_field_.Clear();
}
 const ::google::protobuf::Map< ::google::protobuf::int32, ::google::protobuf::StringValue >&
MapWellKnownTypes::string_field() const {
  // @@protoc_insertion_point(field_map:protobuf_unittest.MapWellKnownTypes.string_field)
  return string_field_.GetMap();
}
 ::google::protobuf::Map< ::google::protobuf::int32, ::google::protobuf::StringValue >*
MapWellKnownTypes::mutable_string_field() {
  // @@protoc_insertion_point(field_mutable_map:protobuf_unittest.MapWellKnownTypes.string_field)
  return string_field_.MutableMap();
}

// map<int32, .google.protobuf.BytesValue> bytes_field = 18;
int MapWellKnownTypes::bytes_field_size() const {
  return bytes_field_.size();
}
void MapWellKnownTypes::clear_bytes_field() {
  bytes_field_.Clear();
}
 const ::google::protobuf::Map< ::google::protobuf::int32, ::google::protobuf::BytesValue >&
MapWellKnownTypes::bytes_field() const {
  // @@protoc_insertion_point(field_map:protobuf_unittest.MapWellKnownTypes.bytes_field)
  return bytes_field_.GetMap();
}
 ::google::protobuf::Map< ::google::protobuf::int32, ::google::protobuf::BytesValue >*
MapWellKnownTypes::mutable_bytes_field() {
  // @@protoc_insertion_point(field_mutable_map:protobuf_unittest.MapWellKnownTypes.bytes_field)
  return bytes_field_.MutableMap();
}

inline const MapWellKnownTypes* MapWellKnownTypes::internal_default_instance() {
  return &MapWellKnownTypes_default_instance_.get();
}
#endif  // PROTOBUF_INLINE_NOT_IN_HEADERS

// @@protoc_insertion_point(namespace_scope)

}  // namespace protobuf_unittest

// @@protoc_insertion_point(global_scope)

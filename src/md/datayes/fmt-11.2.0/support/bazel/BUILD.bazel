load("@rules_cc//cc:defs.bzl", "cc_library")

cc_library(
    name = "fmt",
    srcs = [
        #"src/fmt.cc", # No C++ module support, yet in Bazel (https://github.com/bazelbuild/bazel/pull/19940)
        "src/format.cc",
        "src/os.cc",
    ],
    hdrs = glob([
        "include/fmt/*.h",
    ]),
    copts = select({
        "@platforms//os:windows": ["-utf-8"],
        "//conditions:default": [],
    }),
    includes = [
        "include",
    ],
    strip_include_prefix = "include",
    visibility = ["//visibility:public"],
)

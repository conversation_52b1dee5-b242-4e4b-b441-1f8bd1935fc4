name: lint

on:
  pull_request:
    paths:
      - '**.h'
      - '**.cc'

permissions:
  contents: read

jobs:
  format_code:
    runs-on: ubuntu-latest
    steps:
    - uses: actions/checkout@d632683dd7b4114ad314bca15554477dd762a938 # v4.2.0

    - name: Install clang-format
      run: |
        wget https://apt.llvm.org/llvm.sh
        sudo bash ./llvm.sh 17
        sudo apt install clang-format-17

    - name: Run clang-format
      run: |
        find include src -name '*.h' -o -name '*.cc' | \
          xargs clang-format-17 -i -style=file -fallback-style=none
        git diff --exit-code

# Makefile for compiling dymd.cc

CXX = g++
CXXFLAGS = -g -O2 -std=c++11 -Wall
LDFLAGS = -Wl,-rpath=/usr/local/lib

# Include directories
INCS = -I./mdl_sdk_2_13_228/include \
       -I./protobuf-3.1.0/include \
       -I./fmt-11.2.0/include

# Library directories
LIBDIR = -L./mdl_sdk_2_13_228/libs/linux

# Libraries to link
LIBS = -lmdl_api -ldl -lpthread -lrt -lzmq -lprotobuf

# Source and target
SRC = dymd.cc
TARGET = dymd

all: $(TARGET)

$(TARGET): $(SRC)
    $(CXX) $(CXXFLAGS) $(INCS) $(LDFLAGS) -o $@ $< $(LIBDIR) $(LIBS)
    cp ./mdl_sdk_2_13_228/libs/linux/libmdl_api.so ./

install: $(TARGET)
    install -d /usr/local/sbin
    install -d /usr/local/lib
    install -m 755 $(TARGET) /usr/local/sbin/
    install -m 755 ./mdl_sdk_2_13_228/libs/linux/libmdl_api.so /usr/local/lib/
    ldconfig

uninstall:
    rm -f /usr/local/sbin/$(TARGET)
    rm -f /usr/local/lib/libmdl_api.so
    ldconfig

clean:
    rm -f $(TARGET) libmdl_api.so

.PHONY: all clean install uninstall

// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: lyproto.quota.proto

#define INTERNAL_SUPPRESS_PROTOBUF_FIELD_DEPRECATION
#include "lyproto.quota.pb.h"

#include <algorithm>

#include <google/protobuf/stubs/common.h>
#include <google/protobuf/stubs/port.h>
#include <google/protobuf/stubs/once.h>
#include <google/protobuf/io/coded_stream.h>
#include <google/protobuf/wire_format_lite_inl.h>
#include <google/protobuf/descriptor.h>
#include <google/protobuf/generated_message_reflection.h>
#include <google/protobuf/reflection_ops.h>
#include <google/protobuf/wire_format.h>
// @@protoc_insertion_point(includes)

namespace LYPROTO {
namespace QUOTA {

namespace {

const ::google::protobuf::Descriptor* MarketData_descriptor_ = NULL;
const ::google::protobuf::internal::GeneratedMessageReflection*
  MarketData_reflection_ = NULL;
const ::google::protobuf::Descriptor* Transaction_descriptor_ = NULL;
const ::google::protobuf::internal::GeneratedMessageReflection*
  Transaction_reflection_ = NULL;
const ::google::protobuf::Descriptor* FutureMarketData_descriptor_ = NULL;
const ::google::protobuf::internal::GeneratedMessageReflection*
  FutureMarketData_reflection_ = NULL;

}  // namespace


void protobuf_AssignDesc_lyproto_2equota_2eproto() GOOGLE_ATTRIBUTE_COLD;
void protobuf_AssignDesc_lyproto_2equota_2eproto() {
  protobuf_AddDesc_lyproto_2equota_2eproto();
  const ::google::protobuf::FileDescriptor* file =
    ::google::protobuf::DescriptorPool::generated_pool()->FindFileByName(
      "lyproto.quota.proto");
  GOOGLE_CHECK(file != NULL);
  MarketData_descriptor_ = file->message_type(0);
  static const int MarketData_offsets_[30] = {
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MarketData, exchid_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MarketData, category_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MarketData, stkid_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MarketData, rcvsvrtime_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MarketData, pubsvrtime_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MarketData, status_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MarketData, exchtime_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MarketData, preclose_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MarketData, highlimit_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MarketData, lowlimit_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MarketData, open_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MarketData, high_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MarketData, low_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MarketData, latest_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MarketData, knock_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MarketData, volume_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MarketData, value_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MarketData, ba_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MarketData, bp_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MarketData, sa_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MarketData, sp_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MarketData, totalba_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MarketData, weightedavgbidpx_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MarketData, totalsa_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MarketData, weightedavgofferpx_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MarketData, iopv_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MarketData, yieldtomaturity_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MarketData, totalwarrantexecqty_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MarketData, warlowerpx_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MarketData, warupperpx_),
  };
  MarketData_reflection_ =
    ::google::protobuf::internal::GeneratedMessageReflection::NewGeneratedMessageReflection(
      MarketData_descriptor_,
      MarketData::internal_default_instance(),
      MarketData_offsets_,
      GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MarketData, _has_bits_),
      -1,
      -1,
      sizeof(MarketData),
      GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MarketData, _internal_metadata_));
  Transaction_descriptor_ = file->message_type(1);
  static const int Transaction_offsets_[15] = {
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(Transaction, exchangeid_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(Transaction, category_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(Transaction, securityid_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(Transaction, pubsvrtime_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(Transaction, tradeindex_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(Transaction, tradetime_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(Transaction, tradeprice_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(Transaction, tradeqty_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(Transaction, trademoney_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(Transaction, side_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(Transaction, tradetype_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(Transaction, tradecode_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(Transaction, offerindex_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(Transaction, bidindex_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(Transaction, reserve_),
  };
  Transaction_reflection_ =
    ::google::protobuf::internal::GeneratedMessageReflection::NewGeneratedMessageReflection(
      Transaction_descriptor_,
      Transaction::internal_default_instance(),
      Transaction_offsets_,
      GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(Transaction, _has_bits_),
      -1,
      -1,
      sizeof(Transaction),
      GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(Transaction, _internal_metadata_));
  FutureMarketData_descriptor_ = file->message_type(2);
  static const int FutureMarketData_offsets_[28] = {
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(FutureMarketData, exchid_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(FutureMarketData, category_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(FutureMarketData, stkid_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(FutureMarketData, rcvsvrtime_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(FutureMarketData, pubsvrtime_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(FutureMarketData, status_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(FutureMarketData, exchtime_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(FutureMarketData, tradedate_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(FutureMarketData, preclose_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(FutureMarketData, presettle_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(FutureMarketData, preopenpos_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(FutureMarketData, highlimit_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(FutureMarketData, lowlimit_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(FutureMarketData, open_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(FutureMarketData, latest_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(FutureMarketData, high_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(FutureMarketData, low_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(FutureMarketData, settle_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(FutureMarketData, latestvolume_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(FutureMarketData, volume_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(FutureMarketData, openpos_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(FutureMarketData, value_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(FutureMarketData, ba_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(FutureMarketData, bp_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(FutureMarketData, sa_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(FutureMarketData, sp_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(FutureMarketData, predelta_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(FutureMarketData, curdelta_),
  };
  FutureMarketData_reflection_ =
    ::google::protobuf::internal::GeneratedMessageReflection::NewGeneratedMessageReflection(
      FutureMarketData_descriptor_,
      FutureMarketData::internal_default_instance(),
      FutureMarketData_offsets_,
      GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(FutureMarketData, _has_bits_),
      -1,
      -1,
      sizeof(FutureMarketData),
      GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(FutureMarketData, _internal_metadata_));
}

namespace {

GOOGLE_PROTOBUF_DECLARE_ONCE(protobuf_AssignDescriptors_once_);
void protobuf_AssignDescriptorsOnce() {
  ::google::protobuf::GoogleOnceInit(&protobuf_AssignDescriptors_once_,
                 &protobuf_AssignDesc_lyproto_2equota_2eproto);
}

void protobuf_RegisterTypes(const ::std::string&) GOOGLE_ATTRIBUTE_COLD;
void protobuf_RegisterTypes(const ::std::string&) {
  protobuf_AssignDescriptorsOnce();
  ::google::protobuf::MessageFactory::InternalRegisterGeneratedMessage(
      MarketData_descriptor_, MarketData::internal_default_instance());
  ::google::protobuf::MessageFactory::InternalRegisterGeneratedMessage(
      Transaction_descriptor_, Transaction::internal_default_instance());
  ::google::protobuf::MessageFactory::InternalRegisterGeneratedMessage(
      FutureMarketData_descriptor_, FutureMarketData::internal_default_instance());
}

}  // namespace

void protobuf_ShutdownFile_lyproto_2equota_2eproto() {
  MarketData_default_instance_.Shutdown();
  delete MarketData_reflection_;
  Transaction_default_instance_.Shutdown();
  delete Transaction_reflection_;
  FutureMarketData_default_instance_.Shutdown();
  delete FutureMarketData_reflection_;
}

void protobuf_InitDefaults_lyproto_2equota_2eproto_impl() {
  GOOGLE_PROTOBUF_VERIFY_VERSION;

  ::google::protobuf::internal::GetEmptyString();
  MarketData_default_instance_.DefaultConstruct();
  ::google::protobuf::internal::GetEmptyString();
  Transaction_default_instance_.DefaultConstruct();
  ::google::protobuf::internal::GetEmptyString();
  FutureMarketData_default_instance_.DefaultConstruct();
  MarketData_default_instance_.get_mutable()->InitAsDefaultInstance();
  Transaction_default_instance_.get_mutable()->InitAsDefaultInstance();
  FutureMarketData_default_instance_.get_mutable()->InitAsDefaultInstance();
}

GOOGLE_PROTOBUF_DECLARE_ONCE(protobuf_InitDefaults_lyproto_2equota_2eproto_once_);
void protobuf_InitDefaults_lyproto_2equota_2eproto() {
  ::google::protobuf::GoogleOnceInit(&protobuf_InitDefaults_lyproto_2equota_2eproto_once_,
                 &protobuf_InitDefaults_lyproto_2equota_2eproto_impl);
}
void protobuf_AddDesc_lyproto_2equota_2eproto_impl() {
  GOOGLE_PROTOBUF_VERIFY_VERSION;

  protobuf_InitDefaults_lyproto_2equota_2eproto();
  ::google::protobuf::DescriptorPool::InternalAddGeneratedFile(
    "\n\023lyproto.quota.proto\022\rLYPROTO.QUOTA\"\251\004\n"
    "\nMarketData\022\016\n\006ExchId\030\001 \002(\t\022\020\n\010Category\030"
    "\002 \002(\t\022\r\n\005stkId\030\003 \002(\t\022\022\n\nRcvSvrTime\030\004 \001(\t"
    "\022\022\n\nPubSvrTime\030\005 \001(\t\022\016\n\006Status\030\006 \001(\t\022\020\n\010"
    "ExchTime\030\007 \001(\t\022\020\n\010PreClose\030\010 \001(\003\022\021\n\tHigh"
    "Limit\030\t \001(\003\022\020\n\010LowLimit\030\n \001(\003\022\014\n\004Open\030\013 "
    "\001(\003\022\014\n\004High\030\014 \001(\003\022\013\n\003Low\030\r \001(\003\022\016\n\006Latest"
    "\030\016 \001(\003\022\r\n\005Knock\030\017 \001(\005\022\016\n\006Volume\030\020 \001(\003\022\r\n"
    "\005Value\030\021 \001(\003\022\016\n\002BA\030\022 \003(\003B\002\020\001\022\016\n\002BP\030\023 \003(\003"
    "B\002\020\001\022\016\n\002SA\030\024 \003(\003B\002\020\001\022\016\n\002SP\030\025 \003(\003B\002\020\001\022\017\n\007"
    "TotalBA\030\026 \001(\003\022\030\n\020WeightedAvgBidPx\030\027 \001(\003\022"
    "\017\n\007TotalSA\030\030 \001(\003\022\032\n\022WeightedAvgOfferPx\030\031"
    " \001(\003\022\014\n\004IOPV\030\032 \001(\005\022\027\n\017YieldToMaturity\030\033 "
    "\001(\005\022\033\n\023TotalWarrantExecQty\030\034 \001(\003\022\022\n\nWarL"
    "owerPx\030\035 \001(\003\022\022\n\nWarUpperPx\030\036 \001(\003\"\247\002\n\013Tra"
    "nsaction\022\022\n\nExchangeID\030\001 \002(\t\022\020\n\010Category"
    "\030\002 \002(\t\022\022\n\nSecurityID\030\003 \002(\t\022\022\n\nPubSvrTime"
    "\030\004 \001(\t\022\022\n\nTradeIndex\030\005 \001(\t\022\021\n\tTradeTime\030"
    "\006 \001(\t\022\022\n\nTradePrice\030\007 \001(\003\022\020\n\010TradeQty\030\010 "
    "\001(\005\022\022\n\nTradeMoney\030\t \001(\003\022\014\n\004Side\030\n \001(\t\022\021\n"
    "\tTradeType\030\013 \001(\t\022\021\n\tTradeCode\030\014 \001(\t\022\022\n\nO"
    "fferIndex\030\r \001(\t\022\020\n\010BidIndex\030\016 \001(\t\022\017\n\007res"
    "erve\030\017 \001(\t\"\361\003\n\020FutureMarketData\022\016\n\006ExchI"
    "d\030\001 \002(\t\022\020\n\010Category\030\002 \002(\t\022\r\n\005stkId\030\003 \002(\t"
    "\022\022\n\nRcvSvrTime\030\004 \001(\t\022\022\n\nPubSvrTime\030\005 \001(\t"
    "\022\016\n\006Status\030\006 \001(\t\022\020\n\010ExchTime\030\007 \001(\t\022\021\n\tTr"
    "adeDate\030\010 \001(\t\022\020\n\010PreClose\030\t \001(\003\022\021\n\tPreSe"
    "ttle\030\n \001(\003\022\022\n\nPreOpenPos\030\013 \001(\005\022\021\n\tHighLi"
    "mit\030\014 \001(\003\022\020\n\010LowLimit\030\r \001(\003\022\014\n\004Open\030\016 \001("
    "\003\022\016\n\006Latest\030\017 \001(\003\022\014\n\004High\030\020 \001(\003\022\013\n\003Low\030\021"
    " \001(\003\022\016\n\006Settle\030\022 \001(\003\022\024\n\014LatestVolume\030\023 \001"
    "(\005\022\016\n\006Volume\030\024 \001(\005\022\017\n\007OpenPos\030\025 \001(\005\022\r\n\005V"
    "alue\030\026 \001(\003\022\016\n\002BA\030\027 \003(\005B\002\020\001\022\016\n\002BP\030\030 \003(\003B\002"
    "\020\001\022\016\n\002SA\030\031 \003(\005B\002\020\001\022\016\n\002SP\030\032 \003(\003B\002\020\001\022\020\n\010Pr"
    "eDelta\030\033 \001(\005\022\020\n\010CurDelta\030\034 \001(\005", 1390);
  ::google::protobuf::MessageFactory::InternalRegisterGeneratedFile(
    "lyproto.quota.proto", &protobuf_RegisterTypes);
  ::google::protobuf::internal::OnShutdown(&protobuf_ShutdownFile_lyproto_2equota_2eproto);
}

GOOGLE_PROTOBUF_DECLARE_ONCE(protobuf_AddDesc_lyproto_2equota_2eproto_once_);
void protobuf_AddDesc_lyproto_2equota_2eproto() {
  ::google::protobuf::GoogleOnceInit(&protobuf_AddDesc_lyproto_2equota_2eproto_once_,
                 &protobuf_AddDesc_lyproto_2equota_2eproto_impl);
}
// Force AddDescriptors() to be called at static initialization time.
struct StaticDescriptorInitializer_lyproto_2equota_2eproto {
  StaticDescriptorInitializer_lyproto_2equota_2eproto() {
    protobuf_AddDesc_lyproto_2equota_2eproto();
  }
} static_descriptor_initializer_lyproto_2equota_2eproto_;

namespace {

static void MergeFromFail(int line) GOOGLE_ATTRIBUTE_COLD GOOGLE_ATTRIBUTE_NORETURN;
static void MergeFromFail(int line) {
  ::google::protobuf::internal::MergeFromFail(__FILE__, line);
}

}  // namespace


// ===================================================================

#if !defined(_MSC_VER) || _MSC_VER >= 1900
const int MarketData::kExchIdFieldNumber;
const int MarketData::kCategoryFieldNumber;
const int MarketData::kStkIdFieldNumber;
const int MarketData::kRcvSvrTimeFieldNumber;
const int MarketData::kPubSvrTimeFieldNumber;
const int MarketData::kStatusFieldNumber;
const int MarketData::kExchTimeFieldNumber;
const int MarketData::kPreCloseFieldNumber;
const int MarketData::kHighLimitFieldNumber;
const int MarketData::kLowLimitFieldNumber;
const int MarketData::kOpenFieldNumber;
const int MarketData::kHighFieldNumber;
const int MarketData::kLowFieldNumber;
const int MarketData::kLatestFieldNumber;
const int MarketData::kKnockFieldNumber;
const int MarketData::kVolumeFieldNumber;
const int MarketData::kValueFieldNumber;
const int MarketData::kBAFieldNumber;
const int MarketData::kBPFieldNumber;
const int MarketData::kSAFieldNumber;
const int MarketData::kSPFieldNumber;
const int MarketData::kTotalBAFieldNumber;
const int MarketData::kWeightedAvgBidPxFieldNumber;
const int MarketData::kTotalSAFieldNumber;
const int MarketData::kWeightedAvgOfferPxFieldNumber;
const int MarketData::kIOPVFieldNumber;
const int MarketData::kYieldToMaturityFieldNumber;
const int MarketData::kTotalWarrantExecQtyFieldNumber;
const int MarketData::kWarLowerPxFieldNumber;
const int MarketData::kWarUpperPxFieldNumber;
#endif  // !defined(_MSC_VER) || _MSC_VER >= 1900

MarketData::MarketData()
  : ::google::protobuf::Message(), _internal_metadata_(NULL) {
  if (this != internal_default_instance()) protobuf_InitDefaults_lyproto_2equota_2eproto();
  SharedCtor();
  // @@protoc_insertion_point(constructor:LYPROTO.QUOTA.MarketData)
}

void MarketData::InitAsDefaultInstance() {
}

MarketData::MarketData(const MarketData& from)
  : ::google::protobuf::Message(),
    _internal_metadata_(NULL) {
  SharedCtor();
  UnsafeMergeFrom(from);
  // @@protoc_insertion_point(copy_constructor:LYPROTO.QUOTA.MarketData)
}

void MarketData::SharedCtor() {
  _cached_size_ = 0;
  exchid_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  category_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  stkid_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  rcvsvrtime_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  pubsvrtime_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  status_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  exchtime_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  ::memset(&preclose_, 0, reinterpret_cast<char*>(&yieldtomaturity_) -
    reinterpret_cast<char*>(&preclose_) + sizeof(yieldtomaturity_));
}

MarketData::~MarketData() {
  // @@protoc_insertion_point(destructor:LYPROTO.QUOTA.MarketData)
  SharedDtor();
}

void MarketData::SharedDtor() {
  exchid_.DestroyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  category_.DestroyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  stkid_.DestroyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  rcvsvrtime_.DestroyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  pubsvrtime_.DestroyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  status_.DestroyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  exchtime_.DestroyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}

void MarketData::SetCachedSize(int size) const {
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
}
const ::google::protobuf::Descriptor* MarketData::descriptor() {
  protobuf_AssignDescriptorsOnce();
  return MarketData_descriptor_;
}

const MarketData& MarketData::default_instance() {
  protobuf_InitDefaults_lyproto_2equota_2eproto();
  return *internal_default_instance();
}

::google::protobuf::internal::ExplicitlyConstructed<MarketData> MarketData_default_instance_;

MarketData* MarketData::New(::google::protobuf::Arena* arena) const {
  MarketData* n = new MarketData;
  if (arena != NULL) {
    arena->Own(n);
  }
  return n;
}

void MarketData::Clear() {
// @@protoc_insertion_point(message_clear_start:LYPROTO.QUOTA.MarketData)
#if defined(__clang__)
#define ZR_HELPER_(f) \
  _Pragma("clang diagnostic push") \
  _Pragma("clang diagnostic ignored \"-Winvalid-offsetof\"") \
  __builtin_offsetof(MarketData, f) \
  _Pragma("clang diagnostic pop")
#else
#define ZR_HELPER_(f) reinterpret_cast<char*>(\
  &reinterpret_cast<MarketData*>(16)->f)
#endif

#define ZR_(first, last) do {\
  ::memset(&(first), 0,\
           ZR_HELPER_(last) - ZR_HELPER_(first) + sizeof(last));\
} while (0)

  if (_has_bits_[0 / 32] & 255u) {
    if (has_exchid()) {
      exchid_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
    }
    if (has_category()) {
      category_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
    }
    if (has_stkid()) {
      stkid_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
    }
    if (has_rcvsvrtime()) {
      rcvsvrtime_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
    }
    if (has_pubsvrtime()) {
      pubsvrtime_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
    }
    if (has_status()) {
      status_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
    }
    if (has_exchtime()) {
      exchtime_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
    }
    preclose_ = GOOGLE_LONGLONG(0);
  }
  if (_has_bits_[8 / 32] & 65280u) {
    ZR_(highlimit_, volume_);
    knock_ = 0;
  }
  if (_has_bits_[16 / 32] & 14745600u) {
    ZR_(totalba_, totalsa_);
    value_ = GOOGLE_LONGLONG(0);
  }
  if (_has_bits_[24 / 32] & 1056964608u) {
    ZR_(weightedavgofferpx_, yieldtomaturity_);
    iopv_ = 0;
  }

#undef ZR_HELPER_
#undef ZR_

  ba_.Clear();
  bp_.Clear();
  sa_.Clear();
  sp_.Clear();
  _has_bits_.Clear();
  if (_internal_metadata_.have_unknown_fields()) {
    mutable_unknown_fields()->Clear();
  }
}

bool MarketData::MergePartialFromCodedStream(
    ::google::protobuf::io::CodedInputStream* input) {
#define DO_(EXPRESSION) if (!GOOGLE_PREDICT_TRUE(EXPRESSION)) goto failure
  ::google::protobuf::uint32 tag;
  // @@protoc_insertion_point(parse_start:LYPROTO.QUOTA.MarketData)
  for (;;) {
    ::std::pair< ::google::protobuf::uint32, bool> p = input->ReadTagWithCutoff(16383);
    tag = p.first;
    if (!p.second) goto handle_unusual;
    switch (::google::protobuf::internal::WireFormatLite::GetTagFieldNumber(tag)) {
      // required string ExchId = 1;
      case 1: {
        if (tag == 10) {
          DO_(::google::protobuf::internal::WireFormatLite::ReadString(
                input, this->mutable_exchid()));
          ::google::protobuf::internal::WireFormat::VerifyUTF8StringNamedField(
            this->exchid().data(), this->exchid().length(),
            ::google::protobuf::internal::WireFormat::PARSE,
            "LYPROTO.QUOTA.MarketData.ExchId");
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(18)) goto parse_Category;
        break;
      }

      // required string Category = 2;
      case 2: {
        if (tag == 18) {
         parse_Category:
          DO_(::google::protobuf::internal::WireFormatLite::ReadString(
                input, this->mutable_category()));
          ::google::protobuf::internal::WireFormat::VerifyUTF8StringNamedField(
            this->category().data(), this->category().length(),
            ::google::protobuf::internal::WireFormat::PARSE,
            "LYPROTO.QUOTA.MarketData.Category");
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(26)) goto parse_stkId;
        break;
      }

      // required string stkId = 3;
      case 3: {
        if (tag == 26) {
         parse_stkId:
          DO_(::google::protobuf::internal::WireFormatLite::ReadString(
                input, this->mutable_stkid()));
          ::google::protobuf::internal::WireFormat::VerifyUTF8StringNamedField(
            this->stkid().data(), this->stkid().length(),
            ::google::protobuf::internal::WireFormat::PARSE,
            "LYPROTO.QUOTA.MarketData.stkId");
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(34)) goto parse_RcvSvrTime;
        break;
      }

      // optional string RcvSvrTime = 4;
      case 4: {
        if (tag == 34) {
         parse_RcvSvrTime:
          DO_(::google::protobuf::internal::WireFormatLite::ReadString(
                input, this->mutable_rcvsvrtime()));
          ::google::protobuf::internal::WireFormat::VerifyUTF8StringNamedField(
            this->rcvsvrtime().data(), this->rcvsvrtime().length(),
            ::google::protobuf::internal::WireFormat::PARSE,
            "LYPROTO.QUOTA.MarketData.RcvSvrTime");
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(42)) goto parse_PubSvrTime;
        break;
      }

      // optional string PubSvrTime = 5;
      case 5: {
        if (tag == 42) {
         parse_PubSvrTime:
          DO_(::google::protobuf::internal::WireFormatLite::ReadString(
                input, this->mutable_pubsvrtime()));
          ::google::protobuf::internal::WireFormat::VerifyUTF8StringNamedField(
            this->pubsvrtime().data(), this->pubsvrtime().length(),
            ::google::protobuf::internal::WireFormat::PARSE,
            "LYPROTO.QUOTA.MarketData.PubSvrTime");
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(50)) goto parse_Status;
        break;
      }

      // optional string Status = 6;
      case 6: {
        if (tag == 50) {
         parse_Status:
          DO_(::google::protobuf::internal::WireFormatLite::ReadString(
                input, this->mutable_status()));
          ::google::protobuf::internal::WireFormat::VerifyUTF8StringNamedField(
            this->status().data(), this->status().length(),
            ::google::protobuf::internal::WireFormat::PARSE,
            "LYPROTO.QUOTA.MarketData.Status");
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(58)) goto parse_ExchTime;
        break;
      }

      // optional string ExchTime = 7;
      case 7: {
        if (tag == 58) {
         parse_ExchTime:
          DO_(::google::protobuf::internal::WireFormatLite::ReadString(
                input, this->mutable_exchtime()));
          ::google::protobuf::internal::WireFormat::VerifyUTF8StringNamedField(
            this->exchtime().data(), this->exchtime().length(),
            ::google::protobuf::internal::WireFormat::PARSE,
            "LYPROTO.QUOTA.MarketData.ExchTime");
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(64)) goto parse_PreClose;
        break;
      }

      // optional int64 PreClose = 8;
      case 8: {
        if (tag == 64) {
         parse_PreClose:
          set_has_preclose();
          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_INT64>(
                 input, &preclose_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(72)) goto parse_HighLimit;
        break;
      }

      // optional int64 HighLimit = 9;
      case 9: {
        if (tag == 72) {
         parse_HighLimit:
          set_has_highlimit();
          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_INT64>(
                 input, &highlimit_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(80)) goto parse_LowLimit;
        break;
      }

      // optional int64 LowLimit = 10;
      case 10: {
        if (tag == 80) {
         parse_LowLimit:
          set_has_lowlimit();
          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_INT64>(
                 input, &lowlimit_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(88)) goto parse_Open;
        break;
      }

      // optional int64 Open = 11;
      case 11: {
        if (tag == 88) {
         parse_Open:
          set_has_open();
          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_INT64>(
                 input, &open_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(96)) goto parse_High;
        break;
      }

      // optional int64 High = 12;
      case 12: {
        if (tag == 96) {
         parse_High:
          set_has_high();
          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_INT64>(
                 input, &high_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(104)) goto parse_Low;
        break;
      }

      // optional int64 Low = 13;
      case 13: {
        if (tag == 104) {
         parse_Low:
          set_has_low();
          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_INT64>(
                 input, &low_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(112)) goto parse_Latest;
        break;
      }

      // optional int64 Latest = 14;
      case 14: {
        if (tag == 112) {
         parse_Latest:
          set_has_latest();
          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_INT64>(
                 input, &latest_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(120)) goto parse_Knock;
        break;
      }

      // optional int32 Knock = 15;
      case 15: {
        if (tag == 120) {
         parse_Knock:
          set_has_knock();
          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int32, ::google::protobuf::internal::WireFormatLite::TYPE_INT32>(
                 input, &knock_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(128)) goto parse_Volume;
        break;
      }

      // optional int64 Volume = 16;
      case 16: {
        if (tag == 128) {
         parse_Volume:
          set_has_volume();
          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_INT64>(
                 input, &volume_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(136)) goto parse_Value;
        break;
      }

      // optional int64 Value = 17;
      case 17: {
        if (tag == 136) {
         parse_Value:
          set_has_value();
          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_INT64>(
                 input, &value_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(146)) goto parse_BA;
        break;
      }

      // repeated int64 BA = 18 [packed = true];
      case 18: {
        if (tag == 146) {
         parse_BA:
          DO_((::google::protobuf::internal::WireFormatLite::ReadPackedPrimitive<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_INT64>(
                 input, this->mutable_ba())));
        } else if (tag == 144) {
          DO_((::google::protobuf::internal::WireFormatLite::ReadRepeatedPrimitiveNoInline<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_INT64>(
                 2, 146, input, this->mutable_ba())));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(154)) goto parse_BP;
        break;
      }

      // repeated int64 BP = 19 [packed = true];
      case 19: {
        if (tag == 154) {
         parse_BP:
          DO_((::google::protobuf::internal::WireFormatLite::ReadPackedPrimitive<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_INT64>(
                 input, this->mutable_bp())));
        } else if (tag == 152) {
          DO_((::google::protobuf::internal::WireFormatLite::ReadRepeatedPrimitiveNoInline<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_INT64>(
                 2, 154, input, this->mutable_bp())));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(162)) goto parse_SA;
        break;
      }

      // repeated int64 SA = 20 [packed = true];
      case 20: {
        if (tag == 162) {
         parse_SA:
          DO_((::google::protobuf::internal::WireFormatLite::ReadPackedPrimitive<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_INT64>(
                 input, this->mutable_sa())));
        } else if (tag == 160) {
          DO_((::google::protobuf::internal::WireFormatLite::ReadRepeatedPrimitiveNoInline<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_INT64>(
                 2, 162, input, this->mutable_sa())));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(170)) goto parse_SP;
        break;
      }

      // repeated int64 SP = 21 [packed = true];
      case 21: {
        if (tag == 170) {
         parse_SP:
          DO_((::google::protobuf::internal::WireFormatLite::ReadPackedPrimitive<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_INT64>(
                 input, this->mutable_sp())));
        } else if (tag == 168) {
          DO_((::google::protobuf::internal::WireFormatLite::ReadRepeatedPrimitiveNoInline<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_INT64>(
                 2, 170, input, this->mutable_sp())));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(176)) goto parse_TotalBA;
        break;
      }

      // optional int64 TotalBA = 22;
      case 22: {
        if (tag == 176) {
         parse_TotalBA:
          set_has_totalba();
          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_INT64>(
                 input, &totalba_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(184)) goto parse_WeightedAvgBidPx;
        break;
      }

      // optional int64 WeightedAvgBidPx = 23;
      case 23: {
        if (tag == 184) {
         parse_WeightedAvgBidPx:
          set_has_weightedavgbidpx();
          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_INT64>(
                 input, &weightedavgbidpx_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(192)) goto parse_TotalSA;
        break;
      }

      // optional int64 TotalSA = 24;
      case 24: {
        if (tag == 192) {
         parse_TotalSA:
          set_has_totalsa();
          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_INT64>(
                 input, &totalsa_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(200)) goto parse_WeightedAvgOfferPx;
        break;
      }

      // optional int64 WeightedAvgOfferPx = 25;
      case 25: {
        if (tag == 200) {
         parse_WeightedAvgOfferPx:
          set_has_weightedavgofferpx();
          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_INT64>(
                 input, &weightedavgofferpx_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(208)) goto parse_IOPV;
        break;
      }

      // optional int32 IOPV = 26;
      case 26: {
        if (tag == 208) {
         parse_IOPV:
          set_has_iopv();
          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int32, ::google::protobuf::internal::WireFormatLite::TYPE_INT32>(
                 input, &iopv_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(216)) goto parse_YieldToMaturity;
        break;
      }

      // optional int32 YieldToMaturity = 27;
      case 27: {
        if (tag == 216) {
         parse_YieldToMaturity:
          set_has_yieldtomaturity();
          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int32, ::google::protobuf::internal::WireFormatLite::TYPE_INT32>(
                 input, &yieldtomaturity_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(224)) goto parse_TotalWarrantExecQty;
        break;
      }

      // optional int64 TotalWarrantExecQty = 28;
      case 28: {
        if (tag == 224) {
         parse_TotalWarrantExecQty:
          set_has_totalwarrantexecqty();
          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_INT64>(
                 input, &totalwarrantexecqty_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(232)) goto parse_WarLowerPx;
        break;
      }

      // optional int64 WarLowerPx = 29;
      case 29: {
        if (tag == 232) {
         parse_WarLowerPx:
          set_has_warlowerpx();
          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_INT64>(
                 input, &warlowerpx_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(240)) goto parse_WarUpperPx;
        break;
      }

      // optional int64 WarUpperPx = 30;
      case 30: {
        if (tag == 240) {
         parse_WarUpperPx:
          set_has_warupperpx();
          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_INT64>(
                 input, &warupperpx_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectAtEnd()) goto success;
        break;
      }

      default: {
      handle_unusual:
        if (tag == 0 ||
            ::google::protobuf::internal::WireFormatLite::GetTagWireType(tag) ==
            ::google::protobuf::internal::WireFormatLite::WIRETYPE_END_GROUP) {
          goto success;
        }
        DO_(::google::protobuf::internal::WireFormat::SkipField(
              input, tag, mutable_unknown_fields()));
        break;
      }
    }
  }
success:
  // @@protoc_insertion_point(parse_success:LYPROTO.QUOTA.MarketData)
  return true;
failure:
  // @@protoc_insertion_point(parse_failure:LYPROTO.QUOTA.MarketData)
  return false;
#undef DO_
}

void MarketData::SerializeWithCachedSizes(
    ::google::protobuf::io::CodedOutputStream* output) const {
  // @@protoc_insertion_point(serialize_start:LYPROTO.QUOTA.MarketData)
  // required string ExchId = 1;
  if (has_exchid()) {
    ::google::protobuf::internal::WireFormat::VerifyUTF8StringNamedField(
      this->exchid().data(), this->exchid().length(),
      ::google::protobuf::internal::WireFormat::SERIALIZE,
      "LYPROTO.QUOTA.MarketData.ExchId");
    ::google::protobuf::internal::WireFormatLite::WriteStringMaybeAliased(
      1, this->exchid(), output);
  }

  // required string Category = 2;
  if (has_category()) {
    ::google::protobuf::internal::WireFormat::VerifyUTF8StringNamedField(
      this->category().data(), this->category().length(),
      ::google::protobuf::internal::WireFormat::SERIALIZE,
      "LYPROTO.QUOTA.MarketData.Category");
    ::google::protobuf::internal::WireFormatLite::WriteStringMaybeAliased(
      2, this->category(), output);
  }

  // required string stkId = 3;
  if (has_stkid()) {
    ::google::protobuf::internal::WireFormat::VerifyUTF8StringNamedField(
      this->stkid().data(), this->stkid().length(),
      ::google::protobuf::internal::WireFormat::SERIALIZE,
      "LYPROTO.QUOTA.MarketData.stkId");
    ::google::protobuf::internal::WireFormatLite::WriteStringMaybeAliased(
      3, this->stkid(), output);
  }

  // optional string RcvSvrTime = 4;
  if (has_rcvsvrtime()) {
    ::google::protobuf::internal::WireFormat::VerifyUTF8StringNamedField(
      this->rcvsvrtime().data(), this->rcvsvrtime().length(),
      ::google::protobuf::internal::WireFormat::SERIALIZE,
      "LYPROTO.QUOTA.MarketData.RcvSvrTime");
    ::google::protobuf::internal::WireFormatLite::WriteStringMaybeAliased(
      4, this->rcvsvrtime(), output);
  }

  // optional string PubSvrTime = 5;
  if (has_pubsvrtime()) {
    ::google::protobuf::internal::WireFormat::VerifyUTF8StringNamedField(
      this->pubsvrtime().data(), this->pubsvrtime().length(),
      ::google::protobuf::internal::WireFormat::SERIALIZE,
      "LYPROTO.QUOTA.MarketData.PubSvrTime");
    ::google::protobuf::internal::WireFormatLite::WriteStringMaybeAliased(
      5, this->pubsvrtime(), output);
  }

  // optional string Status = 6;
  if (has_status()) {
    ::google::protobuf::internal::WireFormat::VerifyUTF8StringNamedField(
      this->status().data(), this->status().length(),
      ::google::protobuf::internal::WireFormat::SERIALIZE,
      "LYPROTO.QUOTA.MarketData.Status");
    ::google::protobuf::internal::WireFormatLite::WriteStringMaybeAliased(
      6, this->status(), output);
  }

  // optional string ExchTime = 7;
  if (has_exchtime()) {
    ::google::protobuf::internal::WireFormat::VerifyUTF8StringNamedField(
      this->exchtime().data(), this->exchtime().length(),
      ::google::protobuf::internal::WireFormat::SERIALIZE,
      "LYPROTO.QUOTA.MarketData.ExchTime");
    ::google::protobuf::internal::WireFormatLite::WriteStringMaybeAliased(
      7, this->exchtime(), output);
  }

  // optional int64 PreClose = 8;
  if (has_preclose()) {
    ::google::protobuf::internal::WireFormatLite::WriteInt64(8, this->preclose(), output);
  }

  // optional int64 HighLimit = 9;
  if (has_highlimit()) {
    ::google::protobuf::internal::WireFormatLite::WriteInt64(9, this->highlimit(), output);
  }

  // optional int64 LowLimit = 10;
  if (has_lowlimit()) {
    ::google::protobuf::internal::WireFormatLite::WriteInt64(10, this->lowlimit(), output);
  }

  // optional int64 Open = 11;
  if (has_open()) {
    ::google::protobuf::internal::WireFormatLite::WriteInt64(11, this->open(), output);
  }

  // optional int64 High = 12;
  if (has_high()) {
    ::google::protobuf::internal::WireFormatLite::WriteInt64(12, this->high(), output);
  }

  // optional int64 Low = 13;
  if (has_low()) {
    ::google::protobuf::internal::WireFormatLite::WriteInt64(13, this->low(), output);
  }

  // optional int64 Latest = 14;
  if (has_latest()) {
    ::google::protobuf::internal::WireFormatLite::WriteInt64(14, this->latest(), output);
  }

  // optional int32 Knock = 15;
  if (has_knock()) {
    ::google::protobuf::internal::WireFormatLite::WriteInt32(15, this->knock(), output);
  }

  // optional int64 Volume = 16;
  if (has_volume()) {
    ::google::protobuf::internal::WireFormatLite::WriteInt64(16, this->volume(), output);
  }

  // optional int64 Value = 17;
  if (has_value()) {
    ::google::protobuf::internal::WireFormatLite::WriteInt64(17, this->value(), output);
  }

  // repeated int64 BA = 18 [packed = true];
  if (this->ba_size() > 0) {
    ::google::protobuf::internal::WireFormatLite::WriteTag(18, ::google::protobuf::internal::WireFormatLite::WIRETYPE_LENGTH_DELIMITED, output);
    output->WriteVarint32(_ba_cached_byte_size_);
  }
  for (int i = 0; i < this->ba_size(); i++) {
    ::google::protobuf::internal::WireFormatLite::WriteInt64NoTag(
      this->ba(i), output);
  }

  // repeated int64 BP = 19 [packed = true];
  if (this->bp_size() > 0) {
    ::google::protobuf::internal::WireFormatLite::WriteTag(19, ::google::protobuf::internal::WireFormatLite::WIRETYPE_LENGTH_DELIMITED, output);
    output->WriteVarint32(_bp_cached_byte_size_);
  }
  for (int i = 0; i < this->bp_size(); i++) {
    ::google::protobuf::internal::WireFormatLite::WriteInt64NoTag(
      this->bp(i), output);
  }

  // repeated int64 SA = 20 [packed = true];
  if (this->sa_size() > 0) {
    ::google::protobuf::internal::WireFormatLite::WriteTag(20, ::google::protobuf::internal::WireFormatLite::WIRETYPE_LENGTH_DELIMITED, output);
    output->WriteVarint32(_sa_cached_byte_size_);
  }
  for (int i = 0; i < this->sa_size(); i++) {
    ::google::protobuf::internal::WireFormatLite::WriteInt64NoTag(
      this->sa(i), output);
  }

  // repeated int64 SP = 21 [packed = true];
  if (this->sp_size() > 0) {
    ::google::protobuf::internal::WireFormatLite::WriteTag(21, ::google::protobuf::internal::WireFormatLite::WIRETYPE_LENGTH_DELIMITED, output);
    output->WriteVarint32(_sp_cached_byte_size_);
  }
  for (int i = 0; i < this->sp_size(); i++) {
    ::google::protobuf::internal::WireFormatLite::WriteInt64NoTag(
      this->sp(i), output);
  }

  // optional int64 TotalBA = 22;
  if (has_totalba()) {
    ::google::protobuf::internal::WireFormatLite::WriteInt64(22, this->totalba(), output);
  }

  // optional int64 WeightedAvgBidPx = 23;
  if (has_weightedavgbidpx()) {
    ::google::protobuf::internal::WireFormatLite::WriteInt64(23, this->weightedavgbidpx(), output);
  }

  // optional int64 TotalSA = 24;
  if (has_totalsa()) {
    ::google::protobuf::internal::WireFormatLite::WriteInt64(24, this->totalsa(), output);
  }

  // optional int64 WeightedAvgOfferPx = 25;
  if (has_weightedavgofferpx()) {
    ::google::protobuf::internal::WireFormatLite::WriteInt64(25, this->weightedavgofferpx(), output);
  }

  // optional int32 IOPV = 26;
  if (has_iopv()) {
    ::google::protobuf::internal::WireFormatLite::WriteInt32(26, this->iopv(), output);
  }

  // optional int32 YieldToMaturity = 27;
  if (has_yieldtomaturity()) {
    ::google::protobuf::internal::WireFormatLite::WriteInt32(27, this->yieldtomaturity(), output);
  }

  // optional int64 TotalWarrantExecQty = 28;
  if (has_totalwarrantexecqty()) {
    ::google::protobuf::internal::WireFormatLite::WriteInt64(28, this->totalwarrantexecqty(), output);
  }

  // optional int64 WarLowerPx = 29;
  if (has_warlowerpx()) {
    ::google::protobuf::internal::WireFormatLite::WriteInt64(29, this->warlowerpx(), output);
  }

  // optional int64 WarUpperPx = 30;
  if (has_warupperpx()) {
    ::google::protobuf::internal::WireFormatLite::WriteInt64(30, this->warupperpx(), output);
  }

  if (_internal_metadata_.have_unknown_fields()) {
    ::google::protobuf::internal::WireFormat::SerializeUnknownFields(
        unknown_fields(), output);
  }
  // @@protoc_insertion_point(serialize_end:LYPROTO.QUOTA.MarketData)
}

::google::protobuf::uint8* MarketData::InternalSerializeWithCachedSizesToArray(
    bool deterministic, ::google::protobuf::uint8* target) const {
  (void)deterministic; // Unused
  // @@protoc_insertion_point(serialize_to_array_start:LYPROTO.QUOTA.MarketData)
  // required string ExchId = 1;
  if (has_exchid()) {
    ::google::protobuf::internal::WireFormat::VerifyUTF8StringNamedField(
      this->exchid().data(), this->exchid().length(),
      ::google::protobuf::internal::WireFormat::SERIALIZE,
      "LYPROTO.QUOTA.MarketData.ExchId");
    target =
      ::google::protobuf::internal::WireFormatLite::WriteStringToArray(
        1, this->exchid(), target);
  }

  // required string Category = 2;
  if (has_category()) {
    ::google::protobuf::internal::WireFormat::VerifyUTF8StringNamedField(
      this->category().data(), this->category().length(),
      ::google::protobuf::internal::WireFormat::SERIALIZE,
      "LYPROTO.QUOTA.MarketData.Category");
    target =
      ::google::protobuf::internal::WireFormatLite::WriteStringToArray(
        2, this->category(), target);
  }

  // required string stkId = 3;
  if (has_stkid()) {
    ::google::protobuf::internal::WireFormat::VerifyUTF8StringNamedField(
      this->stkid().data(), this->stkid().length(),
      ::google::protobuf::internal::WireFormat::SERIALIZE,
      "LYPROTO.QUOTA.MarketData.stkId");
    target =
      ::google::protobuf::internal::WireFormatLite::WriteStringToArray(
        3, this->stkid(), target);
  }

  // optional string RcvSvrTime = 4;
  if (has_rcvsvrtime()) {
    ::google::protobuf::internal::WireFormat::VerifyUTF8StringNamedField(
      this->rcvsvrtime().data(), this->rcvsvrtime().length(),
      ::google::protobuf::internal::WireFormat::SERIALIZE,
      "LYPROTO.QUOTA.MarketData.RcvSvrTime");
    target =
      ::google::protobuf::internal::WireFormatLite::WriteStringToArray(
        4, this->rcvsvrtime(), target);
  }

  // optional string PubSvrTime = 5;
  if (has_pubsvrtime()) {
    ::google::protobuf::internal::WireFormat::VerifyUTF8StringNamedField(
      this->pubsvrtime().data(), this->pubsvrtime().length(),
      ::google::protobuf::internal::WireFormat::SERIALIZE,
      "LYPROTO.QUOTA.MarketData.PubSvrTime");
    target =
      ::google::protobuf::internal::WireFormatLite::WriteStringToArray(
        5, this->pubsvrtime(), target);
  }

  // optional string Status = 6;
  if (has_status()) {
    ::google::protobuf::internal::WireFormat::VerifyUTF8StringNamedField(
      this->status().data(), this->status().length(),
      ::google::protobuf::internal::WireFormat::SERIALIZE,
      "LYPROTO.QUOTA.MarketData.Status");
    target =
      ::google::protobuf::internal::WireFormatLite::WriteStringToArray(
        6, this->status(), target);
  }

  // optional string ExchTime = 7;
  if (has_exchtime()) {
    ::google::protobuf::internal::WireFormat::VerifyUTF8StringNamedField(
      this->exchtime().data(), this->exchtime().length(),
      ::google::protobuf::internal::WireFormat::SERIALIZE,
      "LYPROTO.QUOTA.MarketData.ExchTime");
    target =
      ::google::protobuf::internal::WireFormatLite::WriteStringToArray(
        7, this->exchtime(), target);
  }

  // optional int64 PreClose = 8;
  if (has_preclose()) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt64ToArray(8, this->preclose(), target);
  }

  // optional int64 HighLimit = 9;
  if (has_highlimit()) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt64ToArray(9, this->highlimit(), target);
  }

  // optional int64 LowLimit = 10;
  if (has_lowlimit()) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt64ToArray(10, this->lowlimit(), target);
  }

  // optional int64 Open = 11;
  if (has_open()) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt64ToArray(11, this->open(), target);
  }

  // optional int64 High = 12;
  if (has_high()) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt64ToArray(12, this->high(), target);
  }

  // optional int64 Low = 13;
  if (has_low()) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt64ToArray(13, this->low(), target);
  }

  // optional int64 Latest = 14;
  if (has_latest()) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt64ToArray(14, this->latest(), target);
  }

  // optional int32 Knock = 15;
  if (has_knock()) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt32ToArray(15, this->knock(), target);
  }

  // optional int64 Volume = 16;
  if (has_volume()) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt64ToArray(16, this->volume(), target);
  }

  // optional int64 Value = 17;
  if (has_value()) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt64ToArray(17, this->value(), target);
  }

  // repeated int64 BA = 18 [packed = true];
  if (this->ba_size() > 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteTagToArray(
      18,
      ::google::protobuf::internal::WireFormatLite::WIRETYPE_LENGTH_DELIMITED,
      target);
    target = ::google::protobuf::io::CodedOutputStream::WriteVarint32ToArray(
      _ba_cached_byte_size_, target);
  }
  for (int i = 0; i < this->ba_size(); i++) {
    target = ::google::protobuf::internal::WireFormatLite::
      WriteInt64NoTagToArray(this->ba(i), target);
  }

  // repeated int64 BP = 19 [packed = true];
  if (this->bp_size() > 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteTagToArray(
      19,
      ::google::protobuf::internal::WireFormatLite::WIRETYPE_LENGTH_DELIMITED,
      target);
    target = ::google::protobuf::io::CodedOutputStream::WriteVarint32ToArray(
      _bp_cached_byte_size_, target);
  }
  for (int i = 0; i < this->bp_size(); i++) {
    target = ::google::protobuf::internal::WireFormatLite::
      WriteInt64NoTagToArray(this->bp(i), target);
  }

  // repeated int64 SA = 20 [packed = true];
  if (this->sa_size() > 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteTagToArray(
      20,
      ::google::protobuf::internal::WireFormatLite::WIRETYPE_LENGTH_DELIMITED,
      target);
    target = ::google::protobuf::io::CodedOutputStream::WriteVarint32ToArray(
      _sa_cached_byte_size_, target);
  }
  for (int i = 0; i < this->sa_size(); i++) {
    target = ::google::protobuf::internal::WireFormatLite::
      WriteInt64NoTagToArray(this->sa(i), target);
  }

  // repeated int64 SP = 21 [packed = true];
  if (this->sp_size() > 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteTagToArray(
      21,
      ::google::protobuf::internal::WireFormatLite::WIRETYPE_LENGTH_DELIMITED,
      target);
    target = ::google::protobuf::io::CodedOutputStream::WriteVarint32ToArray(
      _sp_cached_byte_size_, target);
  }
  for (int i = 0; i < this->sp_size(); i++) {
    target = ::google::protobuf::internal::WireFormatLite::
      WriteInt64NoTagToArray(this->sp(i), target);
  }

  // optional int64 TotalBA = 22;
  if (has_totalba()) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt64ToArray(22, this->totalba(), target);
  }

  // optional int64 WeightedAvgBidPx = 23;
  if (has_weightedavgbidpx()) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt64ToArray(23, this->weightedavgbidpx(), target);
  }

  // optional int64 TotalSA = 24;
  if (has_totalsa()) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt64ToArray(24, this->totalsa(), target);
  }

  // optional int64 WeightedAvgOfferPx = 25;
  if (has_weightedavgofferpx()) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt64ToArray(25, this->weightedavgofferpx(), target);
  }

  // optional int32 IOPV = 26;
  if (has_iopv()) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt32ToArray(26, this->iopv(), target);
  }

  // optional int32 YieldToMaturity = 27;
  if (has_yieldtomaturity()) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt32ToArray(27, this->yieldtomaturity(), target);
  }

  // optional int64 TotalWarrantExecQty = 28;
  if (has_totalwarrantexecqty()) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt64ToArray(28, this->totalwarrantexecqty(), target);
  }

  // optional int64 WarLowerPx = 29;
  if (has_warlowerpx()) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt64ToArray(29, this->warlowerpx(), target);
  }

  // optional int64 WarUpperPx = 30;
  if (has_warupperpx()) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt64ToArray(30, this->warupperpx(), target);
  }

  if (_internal_metadata_.have_unknown_fields()) {
    target = ::google::protobuf::internal::WireFormat::SerializeUnknownFieldsToArray(
        unknown_fields(), target);
  }
  // @@protoc_insertion_point(serialize_to_array_end:LYPROTO.QUOTA.MarketData)
  return target;
}

size_t MarketData::RequiredFieldsByteSizeFallback() const {
// @@protoc_insertion_point(required_fields_byte_size_fallback_start:LYPROTO.QUOTA.MarketData)
  size_t total_size = 0;

  if (has_exchid()) {
    // required string ExchId = 1;
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::StringSize(
        this->exchid());
  }

  if (has_category()) {
    // required string Category = 2;
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::StringSize(
        this->category());
  }

  if (has_stkid()) {
    // required string stkId = 3;
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::StringSize(
        this->stkid());
  }

  return total_size;
}
size_t MarketData::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:LYPROTO.QUOTA.MarketData)
  size_t total_size = 0;

  if (((_has_bits_[0] & 0x00000007) ^ 0x00000007) == 0) {  // All required fields are present.
    // required string ExchId = 1;
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::StringSize(
        this->exchid());

    // required string Category = 2;
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::StringSize(
        this->category());

    // required string stkId = 3;
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::StringSize(
        this->stkid());

  } else {
    total_size += RequiredFieldsByteSizeFallback();
  }
  if (_has_bits_[3 / 32] & 248u) {
    // optional string RcvSvrTime = 4;
    if (has_rcvsvrtime()) {
      total_size += 1 +
        ::google::protobuf::internal::WireFormatLite::StringSize(
          this->rcvsvrtime());
    }

    // optional string PubSvrTime = 5;
    if (has_pubsvrtime()) {
      total_size += 1 +
        ::google::protobuf::internal::WireFormatLite::StringSize(
          this->pubsvrtime());
    }

    // optional string Status = 6;
    if (has_status()) {
      total_size += 1 +
        ::google::protobuf::internal::WireFormatLite::StringSize(
          this->status());
    }

    // optional string ExchTime = 7;
    if (has_exchtime()) {
      total_size += 1 +
        ::google::protobuf::internal::WireFormatLite::StringSize(
          this->exchtime());
    }

    // optional int64 PreClose = 8;
    if (has_preclose()) {
      total_size += 1 +
        ::google::protobuf::internal::WireFormatLite::Int64Size(
          this->preclose());
    }

  }
  if (_has_bits_[8 / 32] & 65280u) {
    // optional int64 HighLimit = 9;
    if (has_highlimit()) {
      total_size += 1 +
        ::google::protobuf::internal::WireFormatLite::Int64Size(
          this->highlimit());
    }

    // optional int64 LowLimit = 10;
    if (has_lowlimit()) {
      total_size += 1 +
        ::google::protobuf::internal::WireFormatLite::Int64Size(
          this->lowlimit());
    }

    // optional int64 Open = 11;
    if (has_open()) {
      total_size += 1 +
        ::google::protobuf::internal::WireFormatLite::Int64Size(
          this->open());
    }

    // optional int64 High = 12;
    if (has_high()) {
      total_size += 1 +
        ::google::protobuf::internal::WireFormatLite::Int64Size(
          this->high());
    }

    // optional int64 Low = 13;
    if (has_low()) {
      total_size += 1 +
        ::google::protobuf::internal::WireFormatLite::Int64Size(
          this->low());
    }

    // optional int64 Latest = 14;
    if (has_latest()) {
      total_size += 1 +
        ::google::protobuf::internal::WireFormatLite::Int64Size(
          this->latest());
    }

    // optional int32 Knock = 15;
    if (has_knock()) {
      total_size += 1 +
        ::google::protobuf::internal::WireFormatLite::Int32Size(
          this->knock());
    }

    // optional int64 Volume = 16;
    if (has_volume()) {
      total_size += 2 +
        ::google::protobuf::internal::WireFormatLite::Int64Size(
          this->volume());
    }

  }
  if (_has_bits_[16 / 32] & 14745600u) {
    // optional int64 Value = 17;
    if (has_value()) {
      total_size += 2 +
        ::google::protobuf::internal::WireFormatLite::Int64Size(
          this->value());
    }

    // optional int64 TotalBA = 22;
    if (has_totalba()) {
      total_size += 2 +
        ::google::protobuf::internal::WireFormatLite::Int64Size(
          this->totalba());
    }

    // optional int64 WeightedAvgBidPx = 23;
    if (has_weightedavgbidpx()) {
      total_size += 2 +
        ::google::protobuf::internal::WireFormatLite::Int64Size(
          this->weightedavgbidpx());
    }

    // optional int64 TotalSA = 24;
    if (has_totalsa()) {
      total_size += 2 +
        ::google::protobuf::internal::WireFormatLite::Int64Size(
          this->totalsa());
    }

  }
  if (_has_bits_[24 / 32] & 1056964608u) {
    // optional int64 WeightedAvgOfferPx = 25;
    if (has_weightedavgofferpx()) {
      total_size += 2 +
        ::google::protobuf::internal::WireFormatLite::Int64Size(
          this->weightedavgofferpx());
    }

    // optional int32 IOPV = 26;
    if (has_iopv()) {
      total_size += 2 +
        ::google::protobuf::internal::WireFormatLite::Int32Size(
          this->iopv());
    }

    // optional int32 YieldToMaturity = 27;
    if (has_yieldtomaturity()) {
      total_size += 2 +
        ::google::protobuf::internal::WireFormatLite::Int32Size(
          this->yieldtomaturity());
    }

    // optional int64 TotalWarrantExecQty = 28;
    if (has_totalwarrantexecqty()) {
      total_size += 2 +
        ::google::protobuf::internal::WireFormatLite::Int64Size(
          this->totalwarrantexecqty());
    }

    // optional int64 WarLowerPx = 29;
    if (has_warlowerpx()) {
      total_size += 2 +
        ::google::protobuf::internal::WireFormatLite::Int64Size(
          this->warlowerpx());
    }

    // optional int64 WarUpperPx = 30;
    if (has_warupperpx()) {
      total_size += 2 +
        ::google::protobuf::internal::WireFormatLite::Int64Size(
          this->warupperpx());
    }

  }
  // repeated int64 BA = 18 [packed = true];
  {
    size_t data_size = 0;
    unsigned int count = this->ba_size();
    for (unsigned int i = 0; i < count; i++) {
      data_size += ::google::protobuf::internal::WireFormatLite::
        Int64Size(this->ba(i));
    }
    if (data_size > 0) {
      total_size += 2 +
        ::google::protobuf::internal::WireFormatLite::Int32Size(data_size);
    }
    int cached_size = ::google::protobuf::internal::ToCachedSize(data_size);
    GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
    _ba_cached_byte_size_ = cached_size;
    GOOGLE_SAFE_CONCURRENT_WRITES_END();
    total_size += data_size;
  }

  // repeated int64 BP = 19 [packed = true];
  {
    size_t data_size = 0;
    unsigned int count = this->bp_size();
    for (unsigned int i = 0; i < count; i++) {
      data_size += ::google::protobuf::internal::WireFormatLite::
        Int64Size(this->bp(i));
    }
    if (data_size > 0) {
      total_size += 2 +
        ::google::protobuf::internal::WireFormatLite::Int32Size(data_size);
    }
    int cached_size = ::google::protobuf::internal::ToCachedSize(data_size);
    GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
    _bp_cached_byte_size_ = cached_size;
    GOOGLE_SAFE_CONCURRENT_WRITES_END();
    total_size += data_size;
  }

  // repeated int64 SA = 20 [packed = true];
  {
    size_t data_size = 0;
    unsigned int count = this->sa_size();
    for (unsigned int i = 0; i < count; i++) {
      data_size += ::google::protobuf::internal::WireFormatLite::
        Int64Size(this->sa(i));
    }
    if (data_size > 0) {
      total_size += 2 +
        ::google::protobuf::internal::WireFormatLite::Int32Size(data_size);
    }
    int cached_size = ::google::protobuf::internal::ToCachedSize(data_size);
    GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
    _sa_cached_byte_size_ = cached_size;
    GOOGLE_SAFE_CONCURRENT_WRITES_END();
    total_size += data_size;
  }

  // repeated int64 SP = 21 [packed = true];
  {
    size_t data_size = 0;
    unsigned int count = this->sp_size();
    for (unsigned int i = 0; i < count; i++) {
      data_size += ::google::protobuf::internal::WireFormatLite::
        Int64Size(this->sp(i));
    }
    if (data_size > 0) {
      total_size += 2 +
        ::google::protobuf::internal::WireFormatLite::Int32Size(data_size);
    }
    int cached_size = ::google::protobuf::internal::ToCachedSize(data_size);
    GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
    _sp_cached_byte_size_ = cached_size;
    GOOGLE_SAFE_CONCURRENT_WRITES_END();
    total_size += data_size;
  }

  if (_internal_metadata_.have_unknown_fields()) {
    total_size +=
      ::google::protobuf::internal::WireFormat::ComputeUnknownFieldsSize(
        unknown_fields());
  }
  int cached_size = ::google::protobuf::internal::ToCachedSize(total_size);
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = cached_size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
  return total_size;
}

void MarketData::MergeFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_merge_from_start:LYPROTO.QUOTA.MarketData)
  if (GOOGLE_PREDICT_FALSE(&from == this)) MergeFromFail(__LINE__);
  const MarketData* source =
      ::google::protobuf::internal::DynamicCastToGenerated<const MarketData>(
          &from);
  if (source == NULL) {
  // @@protoc_insertion_point(generalized_merge_from_cast_fail:LYPROTO.QUOTA.MarketData)
    ::google::protobuf::internal::ReflectionOps::Merge(from, this);
  } else {
  // @@protoc_insertion_point(generalized_merge_from_cast_success:LYPROTO.QUOTA.MarketData)
    UnsafeMergeFrom(*source);
  }
}

void MarketData::MergeFrom(const MarketData& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:LYPROTO.QUOTA.MarketData)
  if (GOOGLE_PREDICT_TRUE(&from != this)) {
    UnsafeMergeFrom(from);
  } else {
    MergeFromFail(__LINE__);
  }
}

void MarketData::UnsafeMergeFrom(const MarketData& from) {
  GOOGLE_DCHECK(&from != this);
  ba_.UnsafeMergeFrom(from.ba_);
  bp_.UnsafeMergeFrom(from.bp_);
  sa_.UnsafeMergeFrom(from.sa_);
  sp_.UnsafeMergeFrom(from.sp_);
  if (from._has_bits_[0 / 32] & (0xffu << (0 % 32))) {
    if (from.has_exchid()) {
      set_has_exchid();
      exchid_.AssignWithDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), from.exchid_);
    }
    if (from.has_category()) {
      set_has_category();
      category_.AssignWithDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), from.category_);
    }
    if (from.has_stkid()) {
      set_has_stkid();
      stkid_.AssignWithDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), from.stkid_);
    }
    if (from.has_rcvsvrtime()) {
      set_has_rcvsvrtime();
      rcvsvrtime_.AssignWithDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), from.rcvsvrtime_);
    }
    if (from.has_pubsvrtime()) {
      set_has_pubsvrtime();
      pubsvrtime_.AssignWithDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), from.pubsvrtime_);
    }
    if (from.has_status()) {
      set_has_status();
      status_.AssignWithDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), from.status_);
    }
    if (from.has_exchtime()) {
      set_has_exchtime();
      exchtime_.AssignWithDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), from.exchtime_);
    }
    if (from.has_preclose()) {
      set_preclose(from.preclose());
    }
  }
  if (from._has_bits_[8 / 32] & (0xffu << (8 % 32))) {
    if (from.has_highlimit()) {
      set_highlimit(from.highlimit());
    }
    if (from.has_lowlimit()) {
      set_lowlimit(from.lowlimit());
    }
    if (from.has_open()) {
      set_open(from.open());
    }
    if (from.has_high()) {
      set_high(from.high());
    }
    if (from.has_low()) {
      set_low(from.low());
    }
    if (from.has_latest()) {
      set_latest(from.latest());
    }
    if (from.has_knock()) {
      set_knock(from.knock());
    }
    if (from.has_volume()) {
      set_volume(from.volume());
    }
  }
  if (from._has_bits_[16 / 32] & (0xffu << (16 % 32))) {
    if (from.has_value()) {
      set_value(from.value());
    }
    if (from.has_totalba()) {
      set_totalba(from.totalba());
    }
    if (from.has_weightedavgbidpx()) {
      set_weightedavgbidpx(from.weightedavgbidpx());
    }
    if (from.has_totalsa()) {
      set_totalsa(from.totalsa());
    }
  }
  if (from._has_bits_[24 / 32] & (0xffu << (24 % 32))) {
    if (from.has_weightedavgofferpx()) {
      set_weightedavgofferpx(from.weightedavgofferpx());
    }
    if (from.has_iopv()) {
      set_iopv(from.iopv());
    }
    if (from.has_yieldtomaturity()) {
      set_yieldtomaturity(from.yieldtomaturity());
    }
    if (from.has_totalwarrantexecqty()) {
      set_totalwarrantexecqty(from.totalwarrantexecqty());
    }
    if (from.has_warlowerpx()) {
      set_warlowerpx(from.warlowerpx());
    }
    if (from.has_warupperpx()) {
      set_warupperpx(from.warupperpx());
    }
  }
  if (from._internal_metadata_.have_unknown_fields()) {
    ::google::protobuf::UnknownFieldSet::MergeToInternalMetdata(
      from.unknown_fields(), &_internal_metadata_);
  }
}

void MarketData::CopyFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_copy_from_start:LYPROTO.QUOTA.MarketData)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

void MarketData::CopyFrom(const MarketData& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:LYPROTO.QUOTA.MarketData)
  if (&from == this) return;
  Clear();
  UnsafeMergeFrom(from);
}

bool MarketData::IsInitialized() const {
  if ((_has_bits_[0] & 0x00000007) != 0x00000007) return false;

  return true;
}

void MarketData::Swap(MarketData* other) {
  if (other == this) return;
  InternalSwap(other);
}
void MarketData::InternalSwap(MarketData* other) {
  exchid_.Swap(&other->exchid_);
  category_.Swap(&other->category_);
  stkid_.Swap(&other->stkid_);
  rcvsvrtime_.Swap(&other->rcvsvrtime_);
  pubsvrtime_.Swap(&other->pubsvrtime_);
  status_.Swap(&other->status_);
  exchtime_.Swap(&other->exchtime_);
  std::swap(preclose_, other->preclose_);
  std::swap(highlimit_, other->highlimit_);
  std::swap(lowlimit_, other->lowlimit_);
  std::swap(open_, other->open_);
  std::swap(high_, other->high_);
  std::swap(low_, other->low_);
  std::swap(latest_, other->latest_);
  std::swap(knock_, other->knock_);
  std::swap(volume_, other->volume_);
  std::swap(value_, other->value_);
  ba_.UnsafeArenaSwap(&other->ba_);
  bp_.UnsafeArenaSwap(&other->bp_);
  sa_.UnsafeArenaSwap(&other->sa_);
  sp_.UnsafeArenaSwap(&other->sp_);
  std::swap(totalba_, other->totalba_);
  std::swap(weightedavgbidpx_, other->weightedavgbidpx_);
  std::swap(totalsa_, other->totalsa_);
  std::swap(weightedavgofferpx_, other->weightedavgofferpx_);
  std::swap(iopv_, other->iopv_);
  std::swap(yieldtomaturity_, other->yieldtomaturity_);
  std::swap(totalwarrantexecqty_, other->totalwarrantexecqty_);
  std::swap(warlowerpx_, other->warlowerpx_);
  std::swap(warupperpx_, other->warupperpx_);
  std::swap(_has_bits_[0], other->_has_bits_[0]);
  _internal_metadata_.Swap(&other->_internal_metadata_);
  std::swap(_cached_size_, other->_cached_size_);
}

::google::protobuf::Metadata MarketData::GetMetadata() const {
  protobuf_AssignDescriptorsOnce();
  ::google::protobuf::Metadata metadata;
  metadata.descriptor = MarketData_descriptor_;
  metadata.reflection = MarketData_reflection_;
  return metadata;
}

#if PROTOBUF_INLINE_NOT_IN_HEADERS
// MarketData

// required string ExchId = 1;
bool MarketData::has_exchid() const {
  return (_has_bits_[0] & 0x00000001u) != 0;
}
void MarketData::set_has_exchid() {
  _has_bits_[0] |= 0x00000001u;
}
void MarketData::clear_has_exchid() {
  _has_bits_[0] &= ~0x00000001u;
}
void MarketData::clear_exchid() {
  exchid_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  clear_has_exchid();
}
const ::std::string& MarketData::exchid() const {
  // @@protoc_insertion_point(field_get:LYPROTO.QUOTA.MarketData.ExchId)
  return exchid_.GetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
void MarketData::set_exchid(const ::std::string& value) {
  set_has_exchid();
  exchid_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:LYPROTO.QUOTA.MarketData.ExchId)
}
void MarketData::set_exchid(const char* value) {
  set_has_exchid();
  exchid_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:LYPROTO.QUOTA.MarketData.ExchId)
}
void MarketData::set_exchid(const char* value, size_t size) {
  set_has_exchid();
  exchid_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:LYPROTO.QUOTA.MarketData.ExchId)
}
::std::string* MarketData::mutable_exchid() {
  set_has_exchid();
  // @@protoc_insertion_point(field_mutable:LYPROTO.QUOTA.MarketData.ExchId)
  return exchid_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
::std::string* MarketData::release_exchid() {
  // @@protoc_insertion_point(field_release:LYPROTO.QUOTA.MarketData.ExchId)
  clear_has_exchid();
  return exchid_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
void MarketData::set_allocated_exchid(::std::string* exchid) {
  if (exchid != NULL) {
    set_has_exchid();
  } else {
    clear_has_exchid();
  }
  exchid_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), exchid);
  // @@protoc_insertion_point(field_set_allocated:LYPROTO.QUOTA.MarketData.ExchId)
}

// required string Category = 2;
bool MarketData::has_category() const {
  return (_has_bits_[0] & 0x00000002u) != 0;
}
void MarketData::set_has_category() {
  _has_bits_[0] |= 0x00000002u;
}
void MarketData::clear_has_category() {
  _has_bits_[0] &= ~0x00000002u;
}
void MarketData::clear_category() {
  category_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  clear_has_category();
}
const ::std::string& MarketData::category() const {
  // @@protoc_insertion_point(field_get:LYPROTO.QUOTA.MarketData.Category)
  return category_.GetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
void MarketData::set_category(const ::std::string& value) {
  set_has_category();
  category_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:LYPROTO.QUOTA.MarketData.Category)
}
void MarketData::set_category(const char* value) {
  set_has_category();
  category_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:LYPROTO.QUOTA.MarketData.Category)
}
void MarketData::set_category(const char* value, size_t size) {
  set_has_category();
  category_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:LYPROTO.QUOTA.MarketData.Category)
}
::std::string* MarketData::mutable_category() {
  set_has_category();
  // @@protoc_insertion_point(field_mutable:LYPROTO.QUOTA.MarketData.Category)
  return category_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
::std::string* MarketData::release_category() {
  // @@protoc_insertion_point(field_release:LYPROTO.QUOTA.MarketData.Category)
  clear_has_category();
  return category_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
void MarketData::set_allocated_category(::std::string* category) {
  if (category != NULL) {
    set_has_category();
  } else {
    clear_has_category();
  }
  category_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), category);
  // @@protoc_insertion_point(field_set_allocated:LYPROTO.QUOTA.MarketData.Category)
}

// required string stkId = 3;
bool MarketData::has_stkid() const {
  return (_has_bits_[0] & 0x00000004u) != 0;
}
void MarketData::set_has_stkid() {
  _has_bits_[0] |= 0x00000004u;
}
void MarketData::clear_has_stkid() {
  _has_bits_[0] &= ~0x00000004u;
}
void MarketData::clear_stkid() {
  stkid_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  clear_has_stkid();
}
const ::std::string& MarketData::stkid() const {
  // @@protoc_insertion_point(field_get:LYPROTO.QUOTA.MarketData.stkId)
  return stkid_.GetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
void MarketData::set_stkid(const ::std::string& value) {
  set_has_stkid();
  stkid_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:LYPROTO.QUOTA.MarketData.stkId)
}
void MarketData::set_stkid(const char* value) {
  set_has_stkid();
  stkid_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:LYPROTO.QUOTA.MarketData.stkId)
}
void MarketData::set_stkid(const char* value, size_t size) {
  set_has_stkid();
  stkid_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:LYPROTO.QUOTA.MarketData.stkId)
}
::std::string* MarketData::mutable_stkid() {
  set_has_stkid();
  // @@protoc_insertion_point(field_mutable:LYPROTO.QUOTA.MarketData.stkId)
  return stkid_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
::std::string* MarketData::release_stkid() {
  // @@protoc_insertion_point(field_release:LYPROTO.QUOTA.MarketData.stkId)
  clear_has_stkid();
  return stkid_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
void MarketData::set_allocated_stkid(::std::string* stkid) {
  if (stkid != NULL) {
    set_has_stkid();
  } else {
    clear_has_stkid();
  }
  stkid_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), stkid);
  // @@protoc_insertion_point(field_set_allocated:LYPROTO.QUOTA.MarketData.stkId)
}

// optional string RcvSvrTime = 4;
bool MarketData::has_rcvsvrtime() const {
  return (_has_bits_[0] & 0x00000008u) != 0;
}
void MarketData::set_has_rcvsvrtime() {
  _has_bits_[0] |= 0x00000008u;
}
void MarketData::clear_has_rcvsvrtime() {
  _has_bits_[0] &= ~0x00000008u;
}
void MarketData::clear_rcvsvrtime() {
  rcvsvrtime_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  clear_has_rcvsvrtime();
}
const ::std::string& MarketData::rcvsvrtime() const {
  // @@protoc_insertion_point(field_get:LYPROTO.QUOTA.MarketData.RcvSvrTime)
  return rcvsvrtime_.GetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
void MarketData::set_rcvsvrtime(const ::std::string& value) {
  set_has_rcvsvrtime();
  rcvsvrtime_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:LYPROTO.QUOTA.MarketData.RcvSvrTime)
}
void MarketData::set_rcvsvrtime(const char* value) {
  set_has_rcvsvrtime();
  rcvsvrtime_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:LYPROTO.QUOTA.MarketData.RcvSvrTime)
}
void MarketData::set_rcvsvrtime(const char* value, size_t size) {
  set_has_rcvsvrtime();
  rcvsvrtime_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:LYPROTO.QUOTA.MarketData.RcvSvrTime)
}
::std::string* MarketData::mutable_rcvsvrtime() {
  set_has_rcvsvrtime();
  // @@protoc_insertion_point(field_mutable:LYPROTO.QUOTA.MarketData.RcvSvrTime)
  return rcvsvrtime_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
::std::string* MarketData::release_rcvsvrtime() {
  // @@protoc_insertion_point(field_release:LYPROTO.QUOTA.MarketData.RcvSvrTime)
  clear_has_rcvsvrtime();
  return rcvsvrtime_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
void MarketData::set_allocated_rcvsvrtime(::std::string* rcvsvrtime) {
  if (rcvsvrtime != NULL) {
    set_has_rcvsvrtime();
  } else {
    clear_has_rcvsvrtime();
  }
  rcvsvrtime_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), rcvsvrtime);
  // @@protoc_insertion_point(field_set_allocated:LYPROTO.QUOTA.MarketData.RcvSvrTime)
}

// optional string PubSvrTime = 5;
bool MarketData::has_pubsvrtime() const {
  return (_has_bits_[0] & 0x00000010u) != 0;
}
void MarketData::set_has_pubsvrtime() {
  _has_bits_[0] |= 0x00000010u;
}
void MarketData::clear_has_pubsvrtime() {
  _has_bits_[0] &= ~0x00000010u;
}
void MarketData::clear_pubsvrtime() {
  pubsvrtime_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  clear_has_pubsvrtime();
}
const ::std::string& MarketData::pubsvrtime() const {
  // @@protoc_insertion_point(field_get:LYPROTO.QUOTA.MarketData.PubSvrTime)
  return pubsvrtime_.GetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
void MarketData::set_pubsvrtime(const ::std::string& value) {
  set_has_pubsvrtime();
  pubsvrtime_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:LYPROTO.QUOTA.MarketData.PubSvrTime)
}
void MarketData::set_pubsvrtime(const char* value) {
  set_has_pubsvrtime();
  pubsvrtime_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:LYPROTO.QUOTA.MarketData.PubSvrTime)
}
void MarketData::set_pubsvrtime(const char* value, size_t size) {
  set_has_pubsvrtime();
  pubsvrtime_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:LYPROTO.QUOTA.MarketData.PubSvrTime)
}
::std::string* MarketData::mutable_pubsvrtime() {
  set_has_pubsvrtime();
  // @@protoc_insertion_point(field_mutable:LYPROTO.QUOTA.MarketData.PubSvrTime)
  return pubsvrtime_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
::std::string* MarketData::release_pubsvrtime() {
  // @@protoc_insertion_point(field_release:LYPROTO.QUOTA.MarketData.PubSvrTime)
  clear_has_pubsvrtime();
  return pubsvrtime_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
void MarketData::set_allocated_pubsvrtime(::std::string* pubsvrtime) {
  if (pubsvrtime != NULL) {
    set_has_pubsvrtime();
  } else {
    clear_has_pubsvrtime();
  }
  pubsvrtime_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), pubsvrtime);
  // @@protoc_insertion_point(field_set_allocated:LYPROTO.QUOTA.MarketData.PubSvrTime)
}

// optional string Status = 6;
bool MarketData::has_status() const {
  return (_has_bits_[0] & 0x00000020u) != 0;
}
void MarketData::set_has_status() {
  _has_bits_[0] |= 0x00000020u;
}
void MarketData::clear_has_status() {
  _has_bits_[0] &= ~0x00000020u;
}
void MarketData::clear_status() {
  status_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  clear_has_status();
}
const ::std::string& MarketData::status() const {
  // @@protoc_insertion_point(field_get:LYPROTO.QUOTA.MarketData.Status)
  return status_.GetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
void MarketData::set_status(const ::std::string& value) {
  set_has_status();
  status_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:LYPROTO.QUOTA.MarketData.Status)
}
void MarketData::set_status(const char* value) {
  set_has_status();
  status_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:LYPROTO.QUOTA.MarketData.Status)
}
void MarketData::set_status(const char* value, size_t size) {
  set_has_status();
  status_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:LYPROTO.QUOTA.MarketData.Status)
}
::std::string* MarketData::mutable_status() {
  set_has_status();
  // @@protoc_insertion_point(field_mutable:LYPROTO.QUOTA.MarketData.Status)
  return status_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
::std::string* MarketData::release_status() {
  // @@protoc_insertion_point(field_release:LYPROTO.QUOTA.MarketData.Status)
  clear_has_status();
  return status_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
void MarketData::set_allocated_status(::std::string* status) {
  if (status != NULL) {
    set_has_status();
  } else {
    clear_has_status();
  }
  status_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), status);
  // @@protoc_insertion_point(field_set_allocated:LYPROTO.QUOTA.MarketData.Status)
}

// optional string ExchTime = 7;
bool MarketData::has_exchtime() const {
  return (_has_bits_[0] & 0x00000040u) != 0;
}
void MarketData::set_has_exchtime() {
  _has_bits_[0] |= 0x00000040u;
}
void MarketData::clear_has_exchtime() {
  _has_bits_[0] &= ~0x00000040u;
}
void MarketData::clear_exchtime() {
  exchtime_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  clear_has_exchtime();
}
const ::std::string& MarketData::exchtime() const {
  // @@protoc_insertion_point(field_get:LYPROTO.QUOTA.MarketData.ExchTime)
  return exchtime_.GetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
void MarketData::set_exchtime(const ::std::string& value) {
  set_has_exchtime();
  exchtime_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:LYPROTO.QUOTA.MarketData.ExchTime)
}
void MarketData::set_exchtime(const char* value) {
  set_has_exchtime();
  exchtime_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:LYPROTO.QUOTA.MarketData.ExchTime)
}
void MarketData::set_exchtime(const char* value, size_t size) {
  set_has_exchtime();
  exchtime_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:LYPROTO.QUOTA.MarketData.ExchTime)
}
::std::string* MarketData::mutable_exchtime() {
  set_has_exchtime();
  // @@protoc_insertion_point(field_mutable:LYPROTO.QUOTA.MarketData.ExchTime)
  return exchtime_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
::std::string* MarketData::release_exchtime() {
  // @@protoc_insertion_point(field_release:LYPROTO.QUOTA.MarketData.ExchTime)
  clear_has_exchtime();
  return exchtime_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
void MarketData::set_allocated_exchtime(::std::string* exchtime) {
  if (exchtime != NULL) {
    set_has_exchtime();
  } else {
    clear_has_exchtime();
  }
  exchtime_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), exchtime);
  // @@protoc_insertion_point(field_set_allocated:LYPROTO.QUOTA.MarketData.ExchTime)
}

// optional int64 PreClose = 8;
bool MarketData::has_preclose() const {
  return (_has_bits_[0] & 0x00000080u) != 0;
}
void MarketData::set_has_preclose() {
  _has_bits_[0] |= 0x00000080u;
}
void MarketData::clear_has_preclose() {
  _has_bits_[0] &= ~0x00000080u;
}
void MarketData::clear_preclose() {
  preclose_ = GOOGLE_LONGLONG(0);
  clear_has_preclose();
}
::google::protobuf::int64 MarketData::preclose() const {
  // @@protoc_insertion_point(field_get:LYPROTO.QUOTA.MarketData.PreClose)
  return preclose_;
}
void MarketData::set_preclose(::google::protobuf::int64 value) {
  set_has_preclose();
  preclose_ = value;
  // @@protoc_insertion_point(field_set:LYPROTO.QUOTA.MarketData.PreClose)
}

// optional int64 HighLimit = 9;
bool MarketData::has_highlimit() const {
  return (_has_bits_[0] & 0x00000100u) != 0;
}
void MarketData::set_has_highlimit() {
  _has_bits_[0] |= 0x00000100u;
}
void MarketData::clear_has_highlimit() {
  _has_bits_[0] &= ~0x00000100u;
}
void MarketData::clear_highlimit() {
  highlimit_ = GOOGLE_LONGLONG(0);
  clear_has_highlimit();
}
::google::protobuf::int64 MarketData::highlimit() const {
  // @@protoc_insertion_point(field_get:LYPROTO.QUOTA.MarketData.HighLimit)
  return highlimit_;
}
void MarketData::set_highlimit(::google::protobuf::int64 value) {
  set_has_highlimit();
  highlimit_ = value;
  // @@protoc_insertion_point(field_set:LYPROTO.QUOTA.MarketData.HighLimit)
}

// optional int64 LowLimit = 10;
bool MarketData::has_lowlimit() const {
  return (_has_bits_[0] & 0x00000200u) != 0;
}
void MarketData::set_has_lowlimit() {
  _has_bits_[0] |= 0x00000200u;
}
void MarketData::clear_has_lowlimit() {
  _has_bits_[0] &= ~0x00000200u;
}
void MarketData::clear_lowlimit() {
  lowlimit_ = GOOGLE_LONGLONG(0);
  clear_has_lowlimit();
}
::google::protobuf::int64 MarketData::lowlimit() const {
  // @@protoc_insertion_point(field_get:LYPROTO.QUOTA.MarketData.LowLimit)
  return lowlimit_;
}
void MarketData::set_lowlimit(::google::protobuf::int64 value) {
  set_has_lowlimit();
  lowlimit_ = value;
  // @@protoc_insertion_point(field_set:LYPROTO.QUOTA.MarketData.LowLimit)
}

// optional int64 Open = 11;
bool MarketData::has_open() const {
  return (_has_bits_[0] & 0x00000400u) != 0;
}
void MarketData::set_has_open() {
  _has_bits_[0] |= 0x00000400u;
}
void MarketData::clear_has_open() {
  _has_bits_[0] &= ~0x00000400u;
}
void MarketData::clear_open() {
  open_ = GOOGLE_LONGLONG(0);
  clear_has_open();
}
::google::protobuf::int64 MarketData::open() const {
  // @@protoc_insertion_point(field_get:LYPROTO.QUOTA.MarketData.Open)
  return open_;
}
void MarketData::set_open(::google::protobuf::int64 value) {
  set_has_open();
  open_ = value;
  // @@protoc_insertion_point(field_set:LYPROTO.QUOTA.MarketData.Open)
}

// optional int64 High = 12;
bool MarketData::has_high() const {
  return (_has_bits_[0] & 0x00000800u) != 0;
}
void MarketData::set_has_high() {
  _has_bits_[0] |= 0x00000800u;
}
void MarketData::clear_has_high() {
  _has_bits_[0] &= ~0x00000800u;
}
void MarketData::clear_high() {
  high_ = GOOGLE_LONGLONG(0);
  clear_has_high();
}
::google::protobuf::int64 MarketData::high() const {
  // @@protoc_insertion_point(field_get:LYPROTO.QUOTA.MarketData.High)
  return high_;
}
void MarketData::set_high(::google::protobuf::int64 value) {
  set_has_high();
  high_ = value;
  // @@protoc_insertion_point(field_set:LYPROTO.QUOTA.MarketData.High)
}

// optional int64 Low = 13;
bool MarketData::has_low() const {
  return (_has_bits_[0] & 0x00001000u) != 0;
}
void MarketData::set_has_low() {
  _has_bits_[0] |= 0x00001000u;
}
void MarketData::clear_has_low() {
  _has_bits_[0] &= ~0x00001000u;
}
void MarketData::clear_low() {
  low_ = GOOGLE_LONGLONG(0);
  clear_has_low();
}
::google::protobuf::int64 MarketData::low() const {
  // @@protoc_insertion_point(field_get:LYPROTO.QUOTA.MarketData.Low)
  return low_;
}
void MarketData::set_low(::google::protobuf::int64 value) {
  set_has_low();
  low_ = value;
  // @@protoc_insertion_point(field_set:LYPROTO.QUOTA.MarketData.Low)
}

// optional int64 Latest = 14;
bool MarketData::has_latest() const {
  return (_has_bits_[0] & 0x00002000u) != 0;
}
void MarketData::set_has_latest() {
  _has_bits_[0] |= 0x00002000u;
}
void MarketData::clear_has_latest() {
  _has_bits_[0] &= ~0x00002000u;
}
void MarketData::clear_latest() {
  latest_ = GOOGLE_LONGLONG(0);
  clear_has_latest();
}
::google::protobuf::int64 MarketData::latest() const {
  // @@protoc_insertion_point(field_get:LYPROTO.QUOTA.MarketData.Latest)
  return latest_;
}
void MarketData::set_latest(::google::protobuf::int64 value) {
  set_has_latest();
  latest_ = value;
  // @@protoc_insertion_point(field_set:LYPROTO.QUOTA.MarketData.Latest)
}

// optional int32 Knock = 15;
bool MarketData::has_knock() const {
  return (_has_bits_[0] & 0x00004000u) != 0;
}
void MarketData::set_has_knock() {
  _has_bits_[0] |= 0x00004000u;
}
void MarketData::clear_has_knock() {
  _has_bits_[0] &= ~0x00004000u;
}
void MarketData::clear_knock() {
  knock_ = 0;
  clear_has_knock();
}
::google::protobuf::int32 MarketData::knock() const {
  // @@protoc_insertion_point(field_get:LYPROTO.QUOTA.MarketData.Knock)
  return knock_;
}
void MarketData::set_knock(::google::protobuf::int32 value) {
  set_has_knock();
  knock_ = value;
  // @@protoc_insertion_point(field_set:LYPROTO.QUOTA.MarketData.Knock)
}

// optional int64 Volume = 16;
bool MarketData::has_volume() const {
  return (_has_bits_[0] & 0x00008000u) != 0;
}
void MarketData::set_has_volume() {
  _has_bits_[0] |= 0x00008000u;
}
void MarketData::clear_has_volume() {
  _has_bits_[0] &= ~0x00008000u;
}
void MarketData::clear_volume() {
  volume_ = GOOGLE_LONGLONG(0);
  clear_has_volume();
}
::google::protobuf::int64 MarketData::volume() const {
  // @@protoc_insertion_point(field_get:LYPROTO.QUOTA.MarketData.Volume)
  return volume_;
}
void MarketData::set_volume(::google::protobuf::int64 value) {
  set_has_volume();
  volume_ = value;
  // @@protoc_insertion_point(field_set:LYPROTO.QUOTA.MarketData.Volume)
}

// optional int64 Value = 17;
bool MarketData::has_value() const {
  return (_has_bits_[0] & 0x00010000u) != 0;
}
void MarketData::set_has_value() {
  _has_bits_[0] |= 0x00010000u;
}
void MarketData::clear_has_value() {
  _has_bits_[0] &= ~0x00010000u;
}
void MarketData::clear_value() {
  value_ = GOOGLE_LONGLONG(0);
  clear_has_value();
}
::google::protobuf::int64 MarketData::value() const {
  // @@protoc_insertion_point(field_get:LYPROTO.QUOTA.MarketData.Value)
  return value_;
}
void MarketData::set_value(::google::protobuf::int64 value) {
  set_has_value();
  value_ = value;
  // @@protoc_insertion_point(field_set:LYPROTO.QUOTA.MarketData.Value)
}

// repeated int64 BA = 18 [packed = true];
int MarketData::ba_size() const {
  return ba_.size();
}
void MarketData::clear_ba() {
  ba_.Clear();
}
::google::protobuf::int64 MarketData::ba(int index) const {
  // @@protoc_insertion_point(field_get:LYPROTO.QUOTA.MarketData.BA)
  return ba_.Get(index);
}
void MarketData::set_ba(int index, ::google::protobuf::int64 value) {
  ba_.Set(index, value);
  // @@protoc_insertion_point(field_set:LYPROTO.QUOTA.MarketData.BA)
}
void MarketData::add_ba(::google::protobuf::int64 value) {
  ba_.Add(value);
  // @@protoc_insertion_point(field_add:LYPROTO.QUOTA.MarketData.BA)
}
const ::google::protobuf::RepeatedField< ::google::protobuf::int64 >&
MarketData::ba() const {
  // @@protoc_insertion_point(field_list:LYPROTO.QUOTA.MarketData.BA)
  return ba_;
}
::google::protobuf::RepeatedField< ::google::protobuf::int64 >*
MarketData::mutable_ba() {
  // @@protoc_insertion_point(field_mutable_list:LYPROTO.QUOTA.MarketData.BA)
  return &ba_;
}

// repeated int64 BP = 19 [packed = true];
int MarketData::bp_size() const {
  return bp_.size();
}
void MarketData::clear_bp() {
  bp_.Clear();
}
::google::protobuf::int64 MarketData::bp(int index) const {
  // @@protoc_insertion_point(field_get:LYPROTO.QUOTA.MarketData.BP)
  return bp_.Get(index);
}
void MarketData::set_bp(int index, ::google::protobuf::int64 value) {
  bp_.Set(index, value);
  // @@protoc_insertion_point(field_set:LYPROTO.QUOTA.MarketData.BP)
}
void MarketData::add_bp(::google::protobuf::int64 value) {
  bp_.Add(value);
  // @@protoc_insertion_point(field_add:LYPROTO.QUOTA.MarketData.BP)
}
const ::google::protobuf::RepeatedField< ::google::protobuf::int64 >&
MarketData::bp() const {
  // @@protoc_insertion_point(field_list:LYPROTO.QUOTA.MarketData.BP)
  return bp_;
}
::google::protobuf::RepeatedField< ::google::protobuf::int64 >*
MarketData::mutable_bp() {
  // @@protoc_insertion_point(field_mutable_list:LYPROTO.QUOTA.MarketData.BP)
  return &bp_;
}

// repeated int64 SA = 20 [packed = true];
int MarketData::sa_size() const {
  return sa_.size();
}
void MarketData::clear_sa() {
  sa_.Clear();
}
::google::protobuf::int64 MarketData::sa(int index) const {
  // @@protoc_insertion_point(field_get:LYPROTO.QUOTA.MarketData.SA)
  return sa_.Get(index);
}
void MarketData::set_sa(int index, ::google::protobuf::int64 value) {
  sa_.Set(index, value);
  // @@protoc_insertion_point(field_set:LYPROTO.QUOTA.MarketData.SA)
}
void MarketData::add_sa(::google::protobuf::int64 value) {
  sa_.Add(value);
  // @@protoc_insertion_point(field_add:LYPROTO.QUOTA.MarketData.SA)
}
const ::google::protobuf::RepeatedField< ::google::protobuf::int64 >&
MarketData::sa() const {
  // @@protoc_insertion_point(field_list:LYPROTO.QUOTA.MarketData.SA)
  return sa_;
}
::google::protobuf::RepeatedField< ::google::protobuf::int64 >*
MarketData::mutable_sa() {
  // @@protoc_insertion_point(field_mutable_list:LYPROTO.QUOTA.MarketData.SA)
  return &sa_;
}

// repeated int64 SP = 21 [packed = true];
int MarketData::sp_size() const {
  return sp_.size();
}
void MarketData::clear_sp() {
  sp_.Clear();
}
::google::protobuf::int64 MarketData::sp(int index) const {
  // @@protoc_insertion_point(field_get:LYPROTO.QUOTA.MarketData.SP)
  return sp_.Get(index);
}
void MarketData::set_sp(int index, ::google::protobuf::int64 value) {
  sp_.Set(index, value);
  // @@protoc_insertion_point(field_set:LYPROTO.QUOTA.MarketData.SP)
}
void MarketData::add_sp(::google::protobuf::int64 value) {
  sp_.Add(value);
  // @@protoc_insertion_point(field_add:LYPROTO.QUOTA.MarketData.SP)
}
const ::google::protobuf::RepeatedField< ::google::protobuf::int64 >&
MarketData::sp() const {
  // @@protoc_insertion_point(field_list:LYPROTO.QUOTA.MarketData.SP)
  return sp_;
}
::google::protobuf::RepeatedField< ::google::protobuf::int64 >*
MarketData::mutable_sp() {
  // @@protoc_insertion_point(field_mutable_list:LYPROTO.QUOTA.MarketData.SP)
  return &sp_;
}

// optional int64 TotalBA = 22;
bool MarketData::has_totalba() const {
  return (_has_bits_[0] & 0x00200000u) != 0;
}
void MarketData::set_has_totalba() {
  _has_bits_[0] |= 0x00200000u;
}
void MarketData::clear_has_totalba() {
  _has_bits_[0] &= ~0x00200000u;
}
void MarketData::clear_totalba() {
  totalba_ = GOOGLE_LONGLONG(0);
  clear_has_totalba();
}
::google::protobuf::int64 MarketData::totalba() const {
  // @@protoc_insertion_point(field_get:LYPROTO.QUOTA.MarketData.TotalBA)
  return totalba_;
}
void MarketData::set_totalba(::google::protobuf::int64 value) {
  set_has_totalba();
  totalba_ = value;
  // @@protoc_insertion_point(field_set:LYPROTO.QUOTA.MarketData.TotalBA)
}

// optional int64 WeightedAvgBidPx = 23;
bool MarketData::has_weightedavgbidpx() const {
  return (_has_bits_[0] & 0x00400000u) != 0;
}
void MarketData::set_has_weightedavgbidpx() {
  _has_bits_[0] |= 0x00400000u;
}
void MarketData::clear_has_weightedavgbidpx() {
  _has_bits_[0] &= ~0x00400000u;
}
void MarketData::clear_weightedavgbidpx() {
  weightedavgbidpx_ = GOOGLE_LONGLONG(0);
  clear_has_weightedavgbidpx();
}
::google::protobuf::int64 MarketData::weightedavgbidpx() const {
  // @@protoc_insertion_point(field_get:LYPROTO.QUOTA.MarketData.WeightedAvgBidPx)
  return weightedavgbidpx_;
}
void MarketData::set_weightedavgbidpx(::google::protobuf::int64 value) {
  set_has_weightedavgbidpx();
  weightedavgbidpx_ = value;
  // @@protoc_insertion_point(field_set:LYPROTO.QUOTA.MarketData.WeightedAvgBidPx)
}

// optional int64 TotalSA = 24;
bool MarketData::has_totalsa() const {
  return (_has_bits_[0] & 0x00800000u) != 0;
}
void MarketData::set_has_totalsa() {
  _has_bits_[0] |= 0x00800000u;
}
void MarketData::clear_has_totalsa() {
  _has_bits_[0] &= ~0x00800000u;
}
void MarketData::clear_totalsa() {
  totalsa_ = GOOGLE_LONGLONG(0);
  clear_has_totalsa();
}
::google::protobuf::int64 MarketData::totalsa() const {
  // @@protoc_insertion_point(field_get:LYPROTO.QUOTA.MarketData.TotalSA)
  return totalsa_;
}
void MarketData::set_totalsa(::google::protobuf::int64 value) {
  set_has_totalsa();
  totalsa_ = value;
  // @@protoc_insertion_point(field_set:LYPROTO.QUOTA.MarketData.TotalSA)
}

// optional int64 WeightedAvgOfferPx = 25;
bool MarketData::has_weightedavgofferpx() const {
  return (_has_bits_[0] & 0x01000000u) != 0;
}
void MarketData::set_has_weightedavgofferpx() {
  _has_bits_[0] |= 0x01000000u;
}
void MarketData::clear_has_weightedavgofferpx() {
  _has_bits_[0] &= ~0x01000000u;
}
void MarketData::clear_weightedavgofferpx() {
  weightedavgofferpx_ = GOOGLE_LONGLONG(0);
  clear_has_weightedavgofferpx();
}
::google::protobuf::int64 MarketData::weightedavgofferpx() const {
  // @@protoc_insertion_point(field_get:LYPROTO.QUOTA.MarketData.WeightedAvgOfferPx)
  return weightedavgofferpx_;
}
void MarketData::set_weightedavgofferpx(::google::protobuf::int64 value) {
  set_has_weightedavgofferpx();
  weightedavgofferpx_ = value;
  // @@protoc_insertion_point(field_set:LYPROTO.QUOTA.MarketData.WeightedAvgOfferPx)
}

// optional int32 IOPV = 26;
bool MarketData::has_iopv() const {
  return (_has_bits_[0] & 0x02000000u) != 0;
}
void MarketData::set_has_iopv() {
  _has_bits_[0] |= 0x02000000u;
}
void MarketData::clear_has_iopv() {
  _has_bits_[0] &= ~0x02000000u;
}
void MarketData::clear_iopv() {
  iopv_ = 0;
  clear_has_iopv();
}
::google::protobuf::int32 MarketData::iopv() const {
  // @@protoc_insertion_point(field_get:LYPROTO.QUOTA.MarketData.IOPV)
  return iopv_;
}
void MarketData::set_iopv(::google::protobuf::int32 value) {
  set_has_iopv();
  iopv_ = value;
  // @@protoc_insertion_point(field_set:LYPROTO.QUOTA.MarketData.IOPV)
}

// optional int32 YieldToMaturity = 27;
bool MarketData::has_yieldtomaturity() const {
  return (_has_bits_[0] & 0x04000000u) != 0;
}
void MarketData::set_has_yieldtomaturity() {
  _has_bits_[0] |= 0x04000000u;
}
void MarketData::clear_has_yieldtomaturity() {
  _has_bits_[0] &= ~0x04000000u;
}
void MarketData::clear_yieldtomaturity() {
  yieldtomaturity_ = 0;
  clear_has_yieldtomaturity();
}
::google::protobuf::int32 MarketData::yieldtomaturity() const {
  // @@protoc_insertion_point(field_get:LYPROTO.QUOTA.MarketData.YieldToMaturity)
  return yieldtomaturity_;
}
void MarketData::set_yieldtomaturity(::google::protobuf::int32 value) {
  set_has_yieldtomaturity();
  yieldtomaturity_ = value;
  // @@protoc_insertion_point(field_set:LYPROTO.QUOTA.MarketData.YieldToMaturity)
}

// optional int64 TotalWarrantExecQty = 28;
bool MarketData::has_totalwarrantexecqty() const {
  return (_has_bits_[0] & 0x08000000u) != 0;
}
void MarketData::set_has_totalwarrantexecqty() {
  _has_bits_[0] |= 0x08000000u;
}
void MarketData::clear_has_totalwarrantexecqty() {
  _has_bits_[0] &= ~0x08000000u;
}
void MarketData::clear_totalwarrantexecqty() {
  totalwarrantexecqty_ = GOOGLE_LONGLONG(0);
  clear_has_totalwarrantexecqty();
}
::google::protobuf::int64 MarketData::totalwarrantexecqty() const {
  // @@protoc_insertion_point(field_get:LYPROTO.QUOTA.MarketData.TotalWarrantExecQty)
  return totalwarrantexecqty_;
}
void MarketData::set_totalwarrantexecqty(::google::protobuf::int64 value) {
  set_has_totalwarrantexecqty();
  totalwarrantexecqty_ = value;
  // @@protoc_insertion_point(field_set:LYPROTO.QUOTA.MarketData.TotalWarrantExecQty)
}

// optional int64 WarLowerPx = 29;
bool MarketData::has_warlowerpx() const {
  return (_has_bits_[0] & 0x10000000u) != 0;
}
void MarketData::set_has_warlowerpx() {
  _has_bits_[0] |= 0x10000000u;
}
void MarketData::clear_has_warlowerpx() {
  _has_bits_[0] &= ~0x10000000u;
}
void MarketData::clear_warlowerpx() {
  warlowerpx_ = GOOGLE_LONGLONG(0);
  clear_has_warlowerpx();
}
::google::protobuf::int64 MarketData::warlowerpx() const {
  // @@protoc_insertion_point(field_get:LYPROTO.QUOTA.MarketData.WarLowerPx)
  return warlowerpx_;
}
void MarketData::set_warlowerpx(::google::protobuf::int64 value) {
  set_has_warlowerpx();
  warlowerpx_ = value;
  // @@protoc_insertion_point(field_set:LYPROTO.QUOTA.MarketData.WarLowerPx)
}

// optional int64 WarUpperPx = 30;
bool MarketData::has_warupperpx() const {
  return (_has_bits_[0] & 0x20000000u) != 0;
}
void MarketData::set_has_warupperpx() {
  _has_bits_[0] |= 0x20000000u;
}
void MarketData::clear_has_warupperpx() {
  _has_bits_[0] &= ~0x20000000u;
}
void MarketData::clear_warupperpx() {
  warupperpx_ = GOOGLE_LONGLONG(0);
  clear_has_warupperpx();
}
::google::protobuf::int64 MarketData::warupperpx() const {
  // @@protoc_insertion_point(field_get:LYPROTO.QUOTA.MarketData.WarUpperPx)
  return warupperpx_;
}
void MarketData::set_warupperpx(::google::protobuf::int64 value) {
  set_has_warupperpx();
  warupperpx_ = value;
  // @@protoc_insertion_point(field_set:LYPROTO.QUOTA.MarketData.WarUpperPx)
}

inline const MarketData* MarketData::internal_default_instance() {
  return &MarketData_default_instance_.get();
}
#endif  // PROTOBUF_INLINE_NOT_IN_HEADERS

// ===================================================================

#if !defined(_MSC_VER) || _MSC_VER >= 1900
const int Transaction::kExchangeIDFieldNumber;
const int Transaction::kCategoryFieldNumber;
const int Transaction::kSecurityIDFieldNumber;
const int Transaction::kPubSvrTimeFieldNumber;
const int Transaction::kTradeIndexFieldNumber;
const int Transaction::kTradeTimeFieldNumber;
const int Transaction::kTradePriceFieldNumber;
const int Transaction::kTradeQtyFieldNumber;
const int Transaction::kTradeMoneyFieldNumber;
const int Transaction::kSideFieldNumber;
const int Transaction::kTradeTypeFieldNumber;
const int Transaction::kTradeCodeFieldNumber;
const int Transaction::kOfferIndexFieldNumber;
const int Transaction::kBidIndexFieldNumber;
const int Transaction::kReserveFieldNumber;
#endif  // !defined(_MSC_VER) || _MSC_VER >= 1900

Transaction::Transaction()
  : ::google::protobuf::Message(), _internal_metadata_(NULL) {
  if (this != internal_default_instance()) protobuf_InitDefaults_lyproto_2equota_2eproto();
  SharedCtor();
  // @@protoc_insertion_point(constructor:LYPROTO.QUOTA.Transaction)
}

void Transaction::InitAsDefaultInstance() {
}

Transaction::Transaction(const Transaction& from)
  : ::google::protobuf::Message(),
    _internal_metadata_(NULL) {
  SharedCtor();
  UnsafeMergeFrom(from);
  // @@protoc_insertion_point(copy_constructor:LYPROTO.QUOTA.Transaction)
}

void Transaction::SharedCtor() {
  _cached_size_ = 0;
  exchangeid_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  category_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  securityid_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  pubsvrtime_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  tradeindex_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  tradetime_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  side_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  tradetype_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  tradecode_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  offerindex_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  bidindex_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  reserve_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  ::memset(&tradeprice_, 0, reinterpret_cast<char*>(&tradeqty_) -
    reinterpret_cast<char*>(&tradeprice_) + sizeof(tradeqty_));
}

Transaction::~Transaction() {
  // @@protoc_insertion_point(destructor:LYPROTO.QUOTA.Transaction)
  SharedDtor();
}

void Transaction::SharedDtor() {
  exchangeid_.DestroyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  category_.DestroyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  securityid_.DestroyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  pubsvrtime_.DestroyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  tradeindex_.DestroyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  tradetime_.DestroyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  side_.DestroyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  tradetype_.DestroyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  tradecode_.DestroyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  offerindex_.DestroyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  bidindex_.DestroyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  reserve_.DestroyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}

void Transaction::SetCachedSize(int size) const {
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
}
const ::google::protobuf::Descriptor* Transaction::descriptor() {
  protobuf_AssignDescriptorsOnce();
  return Transaction_descriptor_;
}

const Transaction& Transaction::default_instance() {
  protobuf_InitDefaults_lyproto_2equota_2eproto();
  return *internal_default_instance();
}

::google::protobuf::internal::ExplicitlyConstructed<Transaction> Transaction_default_instance_;

Transaction* Transaction::New(::google::protobuf::Arena* arena) const {
  Transaction* n = new Transaction;
  if (arena != NULL) {
    arena->Own(n);
  }
  return n;
}

void Transaction::Clear() {
// @@protoc_insertion_point(message_clear_start:LYPROTO.QUOTA.Transaction)
  if (_has_bits_[0 / 32] & 255u) {
    if (has_exchangeid()) {
      exchangeid_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
    }
    if (has_category()) {
      category_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
    }
    if (has_securityid()) {
      securityid_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
    }
    if (has_pubsvrtime()) {
      pubsvrtime_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
    }
    if (has_tradeindex()) {
      tradeindex_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
    }
    if (has_tradetime()) {
      tradetime_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
    }
    tradeprice_ = GOOGLE_LONGLONG(0);
    tradeqty_ = 0;
  }
  if (_has_bits_[8 / 32] & 32512u) {
    trademoney_ = GOOGLE_LONGLONG(0);
    if (has_side()) {
      side_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
    }
    if (has_tradetype()) {
      tradetype_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
    }
    if (has_tradecode()) {
      tradecode_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
    }
    if (has_offerindex()) {
      offerindex_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
    }
    if (has_bidindex()) {
      bidindex_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
    }
    if (has_reserve()) {
      reserve_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
    }
  }
  _has_bits_.Clear();
  if (_internal_metadata_.have_unknown_fields()) {
    mutable_unknown_fields()->Clear();
  }
}

bool Transaction::MergePartialFromCodedStream(
    ::google::protobuf::io::CodedInputStream* input) {
#define DO_(EXPRESSION) if (!GOOGLE_PREDICT_TRUE(EXPRESSION)) goto failure
  ::google::protobuf::uint32 tag;
  // @@protoc_insertion_point(parse_start:LYPROTO.QUOTA.Transaction)
  for (;;) {
    ::std::pair< ::google::protobuf::uint32, bool> p = input->ReadTagWithCutoff(127);
    tag = p.first;
    if (!p.second) goto handle_unusual;
    switch (::google::protobuf::internal::WireFormatLite::GetTagFieldNumber(tag)) {
      // required string ExchangeID = 1;
      case 1: {
        if (tag == 10) {
          DO_(::google::protobuf::internal::WireFormatLite::ReadString(
                input, this->mutable_exchangeid()));
          ::google::protobuf::internal::WireFormat::VerifyUTF8StringNamedField(
            this->exchangeid().data(), this->exchangeid().length(),
            ::google::protobuf::internal::WireFormat::PARSE,
            "LYPROTO.QUOTA.Transaction.ExchangeID");
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(18)) goto parse_Category;
        break;
      }

      // required string Category = 2;
      case 2: {
        if (tag == 18) {
         parse_Category:
          DO_(::google::protobuf::internal::WireFormatLite::ReadString(
                input, this->mutable_category()));
          ::google::protobuf::internal::WireFormat::VerifyUTF8StringNamedField(
            this->category().data(), this->category().length(),
            ::google::protobuf::internal::WireFormat::PARSE,
            "LYPROTO.QUOTA.Transaction.Category");
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(26)) goto parse_SecurityID;
        break;
      }

      // required string SecurityID = 3;
      case 3: {
        if (tag == 26) {
         parse_SecurityID:
          DO_(::google::protobuf::internal::WireFormatLite::ReadString(
                input, this->mutable_securityid()));
          ::google::protobuf::internal::WireFormat::VerifyUTF8StringNamedField(
            this->securityid().data(), this->securityid().length(),
            ::google::protobuf::internal::WireFormat::PARSE,
            "LYPROTO.QUOTA.Transaction.SecurityID");
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(34)) goto parse_PubSvrTime;
        break;
      }

      // optional string PubSvrTime = 4;
      case 4: {
        if (tag == 34) {
         parse_PubSvrTime:
          DO_(::google::protobuf::internal::WireFormatLite::ReadString(
                input, this->mutable_pubsvrtime()));
          ::google::protobuf::internal::WireFormat::VerifyUTF8StringNamedField(
            this->pubsvrtime().data(), this->pubsvrtime().length(),
            ::google::protobuf::internal::WireFormat::PARSE,
            "LYPROTO.QUOTA.Transaction.PubSvrTime");
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(42)) goto parse_TradeIndex;
        break;
      }

      // optional string TradeIndex = 5;
      case 5: {
        if (tag == 42) {
         parse_TradeIndex:
          DO_(::google::protobuf::internal::WireFormatLite::ReadString(
                input, this->mutable_tradeindex()));
          ::google::protobuf::internal::WireFormat::VerifyUTF8StringNamedField(
            this->tradeindex().data(), this->tradeindex().length(),
            ::google::protobuf::internal::WireFormat::PARSE,
            "LYPROTO.QUOTA.Transaction.TradeIndex");
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(50)) goto parse_TradeTime;
        break;
      }

      // optional string TradeTime = 6;
      case 6: {
        if (tag == 50) {
         parse_TradeTime:
          DO_(::google::protobuf::internal::WireFormatLite::ReadString(
                input, this->mutable_tradetime()));
          ::google::protobuf::internal::WireFormat::VerifyUTF8StringNamedField(
            this->tradetime().data(), this->tradetime().length(),
            ::google::protobuf::internal::WireFormat::PARSE,
            "LYPROTO.QUOTA.Transaction.TradeTime");
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(56)) goto parse_TradePrice;
        break;
      }

      // optional int64 TradePrice = 7;
      case 7: {
        if (tag == 56) {
         parse_TradePrice:
          set_has_tradeprice();
          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_INT64>(
                 input, &tradeprice_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(64)) goto parse_TradeQty;
        break;
      }

      // optional int32 TradeQty = 8;
      case 8: {
        if (tag == 64) {
         parse_TradeQty:
          set_has_tradeqty();
          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int32, ::google::protobuf::internal::WireFormatLite::TYPE_INT32>(
                 input, &tradeqty_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(72)) goto parse_TradeMoney;
        break;
      }

      // optional int64 TradeMoney = 9;
      case 9: {
        if (tag == 72) {
         parse_TradeMoney:
          set_has_trademoney();
          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_INT64>(
                 input, &trademoney_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(82)) goto parse_Side;
        break;
      }

      // optional string Side = 10;
      case 10: {
        if (tag == 82) {
         parse_Side:
          DO_(::google::protobuf::internal::WireFormatLite::ReadString(
                input, this->mutable_side()));
          ::google::protobuf::internal::WireFormat::VerifyUTF8StringNamedField(
            this->side().data(), this->side().length(),
            ::google::protobuf::internal::WireFormat::PARSE,
            "LYPROTO.QUOTA.Transaction.Side");
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(90)) goto parse_TradeType;
        break;
      }

      // optional string TradeType = 11;
      case 11: {
        if (tag == 90) {
         parse_TradeType:
          DO_(::google::protobuf::internal::WireFormatLite::ReadString(
                input, this->mutable_tradetype()));
          ::google::protobuf::internal::WireFormat::VerifyUTF8StringNamedField(
            this->tradetype().data(), this->tradetype().length(),
            ::google::protobuf::internal::WireFormat::PARSE,
            "LYPROTO.QUOTA.Transaction.TradeType");
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(98)) goto parse_TradeCode;
        break;
      }

      // optional string TradeCode = 12;
      case 12: {
        if (tag == 98) {
         parse_TradeCode:
          DO_(::google::protobuf::internal::WireFormatLite::ReadString(
                input, this->mutable_tradecode()));
          ::google::protobuf::internal::WireFormat::VerifyUTF8StringNamedField(
            this->tradecode().data(), this->tradecode().length(),
            ::google::protobuf::internal::WireFormat::PARSE,
            "LYPROTO.QUOTA.Transaction.TradeCode");
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(106)) goto parse_OfferIndex;
        break;
      }

      // optional string OfferIndex = 13;
      case 13: {
        if (tag == 106) {
         parse_OfferIndex:
          DO_(::google::protobuf::internal::WireFormatLite::ReadString(
                input, this->mutable_offerindex()));
          ::google::protobuf::internal::WireFormat::VerifyUTF8StringNamedField(
            this->offerindex().data(), this->offerindex().length(),
            ::google::protobuf::internal::WireFormat::PARSE,
            "LYPROTO.QUOTA.Transaction.OfferIndex");
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(114)) goto parse_BidIndex;
        break;
      }

      // optional string BidIndex = 14;
      case 14: {
        if (tag == 114) {
         parse_BidIndex:
          DO_(::google::protobuf::internal::WireFormatLite::ReadString(
                input, this->mutable_bidindex()));
          ::google::protobuf::internal::WireFormat::VerifyUTF8StringNamedField(
            this->bidindex().data(), this->bidindex().length(),
            ::google::protobuf::internal::WireFormat::PARSE,
            "LYPROTO.QUOTA.Transaction.BidIndex");
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(122)) goto parse_reserve;
        break;
      }

      // optional string reserve = 15;
      case 15: {
        if (tag == 122) {
         parse_reserve:
          DO_(::google::protobuf::internal::WireFormatLite::ReadString(
                input, this->mutable_reserve()));
          ::google::protobuf::internal::WireFormat::VerifyUTF8StringNamedField(
            this->reserve().data(), this->reserve().length(),
            ::google::protobuf::internal::WireFormat::PARSE,
            "LYPROTO.QUOTA.Transaction.reserve");
        } else {
          goto handle_unusual;
        }
        if (input->ExpectAtEnd()) goto success;
        break;
      }

      default: {
      handle_unusual:
        if (tag == 0 ||
            ::google::protobuf::internal::WireFormatLite::GetTagWireType(tag) ==
            ::google::protobuf::internal::WireFormatLite::WIRETYPE_END_GROUP) {
          goto success;
        }
        DO_(::google::protobuf::internal::WireFormat::SkipField(
              input, tag, mutable_unknown_fields()));
        break;
      }
    }
  }
success:
  // @@protoc_insertion_point(parse_success:LYPROTO.QUOTA.Transaction)
  return true;
failure:
  // @@protoc_insertion_point(parse_failure:LYPROTO.QUOTA.Transaction)
  return false;
#undef DO_
}

void Transaction::SerializeWithCachedSizes(
    ::google::protobuf::io::CodedOutputStream* output) const {
  // @@protoc_insertion_point(serialize_start:LYPROTO.QUOTA.Transaction)
  // required string ExchangeID = 1;
  if (has_exchangeid()) {
    ::google::protobuf::internal::WireFormat::VerifyUTF8StringNamedField(
      this->exchangeid().data(), this->exchangeid().length(),
      ::google::protobuf::internal::WireFormat::SERIALIZE,
      "LYPROTO.QUOTA.Transaction.ExchangeID");
    ::google::protobuf::internal::WireFormatLite::WriteStringMaybeAliased(
      1, this->exchangeid(), output);
  }

  // required string Category = 2;
  if (has_category()) {
    ::google::protobuf::internal::WireFormat::VerifyUTF8StringNamedField(
      this->category().data(), this->category().length(),
      ::google::protobuf::internal::WireFormat::SERIALIZE,
      "LYPROTO.QUOTA.Transaction.Category");
    ::google::protobuf::internal::WireFormatLite::WriteStringMaybeAliased(
      2, this->category(), output);
  }

  // required string SecurityID = 3;
  if (has_securityid()) {
    ::google::protobuf::internal::WireFormat::VerifyUTF8StringNamedField(
      this->securityid().data(), this->securityid().length(),
      ::google::protobuf::internal::WireFormat::SERIALIZE,
      "LYPROTO.QUOTA.Transaction.SecurityID");
    ::google::protobuf::internal::WireFormatLite::WriteStringMaybeAliased(
      3, this->securityid(), output);
  }

  // optional string PubSvrTime = 4;
  if (has_pubsvrtime()) {
    ::google::protobuf::internal::WireFormat::VerifyUTF8StringNamedField(
      this->pubsvrtime().data(), this->pubsvrtime().length(),
      ::google::protobuf::internal::WireFormat::SERIALIZE,
      "LYPROTO.QUOTA.Transaction.PubSvrTime");
    ::google::protobuf::internal::WireFormatLite::WriteStringMaybeAliased(
      4, this->pubsvrtime(), output);
  }

  // optional string TradeIndex = 5;
  if (has_tradeindex()) {
    ::google::protobuf::internal::WireFormat::VerifyUTF8StringNamedField(
      this->tradeindex().data(), this->tradeindex().length(),
      ::google::protobuf::internal::WireFormat::SERIALIZE,
      "LYPROTO.QUOTA.Transaction.TradeIndex");
    ::google::protobuf::internal::WireFormatLite::WriteStringMaybeAliased(
      5, this->tradeindex(), output);
  }

  // optional string TradeTime = 6;
  if (has_tradetime()) {
    ::google::protobuf::internal::WireFormat::VerifyUTF8StringNamedField(
      this->tradetime().data(), this->tradetime().length(),
      ::google::protobuf::internal::WireFormat::SERIALIZE,
      "LYPROTO.QUOTA.Transaction.TradeTime");
    ::google::protobuf::internal::WireFormatLite::WriteStringMaybeAliased(
      6, this->tradetime(), output);
  }

  // optional int64 TradePrice = 7;
  if (has_tradeprice()) {
    ::google::protobuf::internal::WireFormatLite::WriteInt64(7, this->tradeprice(), output);
  }

  // optional int32 TradeQty = 8;
  if (has_tradeqty()) {
    ::google::protobuf::internal::WireFormatLite::WriteInt32(8, this->tradeqty(), output);
  }

  // optional int64 TradeMoney = 9;
  if (has_trademoney()) {
    ::google::protobuf::internal::WireFormatLite::WriteInt64(9, this->trademoney(), output);
  }

  // optional string Side = 10;
  if (has_side()) {
    ::google::protobuf::internal::WireFormat::VerifyUTF8StringNamedField(
      this->side().data(), this->side().length(),
      ::google::protobuf::internal::WireFormat::SERIALIZE,
      "LYPROTO.QUOTA.Transaction.Side");
    ::google::protobuf::internal::WireFormatLite::WriteStringMaybeAliased(
      10, this->side(), output);
  }

  // optional string TradeType = 11;
  if (has_tradetype()) {
    ::google::protobuf::internal::WireFormat::VerifyUTF8StringNamedField(
      this->tradetype().data(), this->tradetype().length(),
      ::google::protobuf::internal::WireFormat::SERIALIZE,
      "LYPROTO.QUOTA.Transaction.TradeType");
    ::google::protobuf::internal::WireFormatLite::WriteStringMaybeAliased(
      11, this->tradetype(), output);
  }

  // optional string TradeCode = 12;
  if (has_tradecode()) {
    ::google::protobuf::internal::WireFormat::VerifyUTF8StringNamedField(
      this->tradecode().data(), this->tradecode().length(),
      ::google::protobuf::internal::WireFormat::SERIALIZE,
      "LYPROTO.QUOTA.Transaction.TradeCode");
    ::google::protobuf::internal::WireFormatLite::WriteStringMaybeAliased(
      12, this->tradecode(), output);
  }

  // optional string OfferIndex = 13;
  if (has_offerindex()) {
    ::google::protobuf::internal::WireFormat::VerifyUTF8StringNamedField(
      this->offerindex().data(), this->offerindex().length(),
      ::google::protobuf::internal::WireFormat::SERIALIZE,
      "LYPROTO.QUOTA.Transaction.OfferIndex");
    ::google::protobuf::internal::WireFormatLite::WriteStringMaybeAliased(
      13, this->offerindex(), output);
  }

  // optional string BidIndex = 14;
  if (has_bidindex()) {
    ::google::protobuf::internal::WireFormat::VerifyUTF8StringNamedField(
      this->bidindex().data(), this->bidindex().length(),
      ::google::protobuf::internal::WireFormat::SERIALIZE,
      "LYPROTO.QUOTA.Transaction.BidIndex");
    ::google::protobuf::internal::WireFormatLite::WriteStringMaybeAliased(
      14, this->bidindex(), output);
  }

  // optional string reserve = 15;
  if (has_reserve()) {
    ::google::protobuf::internal::WireFormat::VerifyUTF8StringNamedField(
      this->reserve().data(), this->reserve().length(),
      ::google::protobuf::internal::WireFormat::SERIALIZE,
      "LYPROTO.QUOTA.Transaction.reserve");
    ::google::protobuf::internal::WireFormatLite::WriteStringMaybeAliased(
      15, this->reserve(), output);
  }

  if (_internal_metadata_.have_unknown_fields()) {
    ::google::protobuf::internal::WireFormat::SerializeUnknownFields(
        unknown_fields(), output);
  }
  // @@protoc_insertion_point(serialize_end:LYPROTO.QUOTA.Transaction)
}

::google::protobuf::uint8* Transaction::InternalSerializeWithCachedSizesToArray(
    bool deterministic, ::google::protobuf::uint8* target) const {
  (void)deterministic; // Unused
  // @@protoc_insertion_point(serialize_to_array_start:LYPROTO.QUOTA.Transaction)
  // required string ExchangeID = 1;
  if (has_exchangeid()) {
    ::google::protobuf::internal::WireFormat::VerifyUTF8StringNamedField(
      this->exchangeid().data(), this->exchangeid().length(),
      ::google::protobuf::internal::WireFormat::SERIALIZE,
      "LYPROTO.QUOTA.Transaction.ExchangeID");
    target =
      ::google::protobuf::internal::WireFormatLite::WriteStringToArray(
        1, this->exchangeid(), target);
  }

  // required string Category = 2;
  if (has_category()) {
    ::google::protobuf::internal::WireFormat::VerifyUTF8StringNamedField(
      this->category().data(), this->category().length(),
      ::google::protobuf::internal::WireFormat::SERIALIZE,
      "LYPROTO.QUOTA.Transaction.Category");
    target =
      ::google::protobuf::internal::WireFormatLite::WriteStringToArray(
        2, this->category(), target);
  }

  // required string SecurityID = 3;
  if (has_securityid()) {
    ::google::protobuf::internal::WireFormat::VerifyUTF8StringNamedField(
      this->securityid().data(), this->securityid().length(),
      ::google::protobuf::internal::WireFormat::SERIALIZE,
      "LYPROTO.QUOTA.Transaction.SecurityID");
    target =
      ::google::protobuf::internal::WireFormatLite::WriteStringToArray(
        3, this->securityid(), target);
  }

  // optional string PubSvrTime = 4;
  if (has_pubsvrtime()) {
    ::google::protobuf::internal::WireFormat::VerifyUTF8StringNamedField(
      this->pubsvrtime().data(), this->pubsvrtime().length(),
      ::google::protobuf::internal::WireFormat::SERIALIZE,
      "LYPROTO.QUOTA.Transaction.PubSvrTime");
    target =
      ::google::protobuf::internal::WireFormatLite::WriteStringToArray(
        4, this->pubsvrtime(), target);
  }

  // optional string TradeIndex = 5;
  if (has_tradeindex()) {
    ::google::protobuf::internal::WireFormat::VerifyUTF8StringNamedField(
      this->tradeindex().data(), this->tradeindex().length(),
      ::google::protobuf::internal::WireFormat::SERIALIZE,
      "LYPROTO.QUOTA.Transaction.TradeIndex");
    target =
      ::google::protobuf::internal::WireFormatLite::WriteStringToArray(
        5, this->tradeindex(), target);
  }

  // optional string TradeTime = 6;
  if (has_tradetime()) {
    ::google::protobuf::internal::WireFormat::VerifyUTF8StringNamedField(
      this->tradetime().data(), this->tradetime().length(),
      ::google::protobuf::internal::WireFormat::SERIALIZE,
      "LYPROTO.QUOTA.Transaction.TradeTime");
    target =
      ::google::protobuf::internal::WireFormatLite::WriteStringToArray(
        6, this->tradetime(), target);
  }

  // optional int64 TradePrice = 7;
  if (has_tradeprice()) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt64ToArray(7, this->tradeprice(), target);
  }

  // optional int32 TradeQty = 8;
  if (has_tradeqty()) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt32ToArray(8, this->tradeqty(), target);
  }

  // optional int64 TradeMoney = 9;
  if (has_trademoney()) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt64ToArray(9, this->trademoney(), target);
  }

  // optional string Side = 10;
  if (has_side()) {
    ::google::protobuf::internal::WireFormat::VerifyUTF8StringNamedField(
      this->side().data(), this->side().length(),
      ::google::protobuf::internal::WireFormat::SERIALIZE,
      "LYPROTO.QUOTA.Transaction.Side");
    target =
      ::google::protobuf::internal::WireFormatLite::WriteStringToArray(
        10, this->side(), target);
  }

  // optional string TradeType = 11;
  if (has_tradetype()) {
    ::google::protobuf::internal::WireFormat::VerifyUTF8StringNamedField(
      this->tradetype().data(), this->tradetype().length(),
      ::google::protobuf::internal::WireFormat::SERIALIZE,
      "LYPROTO.QUOTA.Transaction.TradeType");
    target =
      ::google::protobuf::internal::WireFormatLite::WriteStringToArray(
        11, this->tradetype(), target);
  }

  // optional string TradeCode = 12;
  if (has_tradecode()) {
    ::google::protobuf::internal::WireFormat::VerifyUTF8StringNamedField(
      this->tradecode().data(), this->tradecode().length(),
      ::google::protobuf::internal::WireFormat::SERIALIZE,
      "LYPROTO.QUOTA.Transaction.TradeCode");
    target =
      ::google::protobuf::internal::WireFormatLite::WriteStringToArray(
        12, this->tradecode(), target);
  }

  // optional string OfferIndex = 13;
  if (has_offerindex()) {
    ::google::protobuf::internal::WireFormat::VerifyUTF8StringNamedField(
      this->offerindex().data(), this->offerindex().length(),
      ::google::protobuf::internal::WireFormat::SERIALIZE,
      "LYPROTO.QUOTA.Transaction.OfferIndex");
    target =
      ::google::protobuf::internal::WireFormatLite::WriteStringToArray(
        13, this->offerindex(), target);
  }

  // optional string BidIndex = 14;
  if (has_bidindex()) {
    ::google::protobuf::internal::WireFormat::VerifyUTF8StringNamedField(
      this->bidindex().data(), this->bidindex().length(),
      ::google::protobuf::internal::WireFormat::SERIALIZE,
      "LYPROTO.QUOTA.Transaction.BidIndex");
    target =
      ::google::protobuf::internal::WireFormatLite::WriteStringToArray(
        14, this->bidindex(), target);
  }

  // optional string reserve = 15;
  if (has_reserve()) {
    ::google::protobuf::internal::WireFormat::VerifyUTF8StringNamedField(
      this->reserve().data(), this->reserve().length(),
      ::google::protobuf::internal::WireFormat::SERIALIZE,
      "LYPROTO.QUOTA.Transaction.reserve");
    target =
      ::google::protobuf::internal::WireFormatLite::WriteStringToArray(
        15, this->reserve(), target);
  }

  if (_internal_metadata_.have_unknown_fields()) {
    target = ::google::protobuf::internal::WireFormat::SerializeUnknownFieldsToArray(
        unknown_fields(), target);
  }
  // @@protoc_insertion_point(serialize_to_array_end:LYPROTO.QUOTA.Transaction)
  return target;
}

size_t Transaction::RequiredFieldsByteSizeFallback() const {
// @@protoc_insertion_point(required_fields_byte_size_fallback_start:LYPROTO.QUOTA.Transaction)
  size_t total_size = 0;

  if (has_exchangeid()) {
    // required string ExchangeID = 1;
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::StringSize(
        this->exchangeid());
  }

  if (has_category()) {
    // required string Category = 2;
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::StringSize(
        this->category());
  }

  if (has_securityid()) {
    // required string SecurityID = 3;
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::StringSize(
        this->securityid());
  }

  return total_size;
}
size_t Transaction::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:LYPROTO.QUOTA.Transaction)
  size_t total_size = 0;

  if (((_has_bits_[0] & 0x00000007) ^ 0x00000007) == 0) {  // All required fields are present.
    // required string ExchangeID = 1;
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::StringSize(
        this->exchangeid());

    // required string Category = 2;
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::StringSize(
        this->category());

    // required string SecurityID = 3;
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::StringSize(
        this->securityid());

  } else {
    total_size += RequiredFieldsByteSizeFallback();
  }
  if (_has_bits_[3 / 32] & 248u) {
    // optional string PubSvrTime = 4;
    if (has_pubsvrtime()) {
      total_size += 1 +
        ::google::protobuf::internal::WireFormatLite::StringSize(
          this->pubsvrtime());
    }

    // optional string TradeIndex = 5;
    if (has_tradeindex()) {
      total_size += 1 +
        ::google::protobuf::internal::WireFormatLite::StringSize(
          this->tradeindex());
    }

    // optional string TradeTime = 6;
    if (has_tradetime()) {
      total_size += 1 +
        ::google::protobuf::internal::WireFormatLite::StringSize(
          this->tradetime());
    }

    // optional int64 TradePrice = 7;
    if (has_tradeprice()) {
      total_size += 1 +
        ::google::protobuf::internal::WireFormatLite::Int64Size(
          this->tradeprice());
    }

    // optional int32 TradeQty = 8;
    if (has_tradeqty()) {
      total_size += 1 +
        ::google::protobuf::internal::WireFormatLite::Int32Size(
          this->tradeqty());
    }

  }
  if (_has_bits_[8 / 32] & 32512u) {
    // optional int64 TradeMoney = 9;
    if (has_trademoney()) {
      total_size += 1 +
        ::google::protobuf::internal::WireFormatLite::Int64Size(
          this->trademoney());
    }

    // optional string Side = 10;
    if (has_side()) {
      total_size += 1 +
        ::google::protobuf::internal::WireFormatLite::StringSize(
          this->side());
    }

    // optional string TradeType = 11;
    if (has_tradetype()) {
      total_size += 1 +
        ::google::protobuf::internal::WireFormatLite::StringSize(
          this->tradetype());
    }

    // optional string TradeCode = 12;
    if (has_tradecode()) {
      total_size += 1 +
        ::google::protobuf::internal::WireFormatLite::StringSize(
          this->tradecode());
    }

    // optional string OfferIndex = 13;
    if (has_offerindex()) {
      total_size += 1 +
        ::google::protobuf::internal::WireFormatLite::StringSize(
          this->offerindex());
    }

    // optional string BidIndex = 14;
    if (has_bidindex()) {
      total_size += 1 +
        ::google::protobuf::internal::WireFormatLite::StringSize(
          this->bidindex());
    }

    // optional string reserve = 15;
    if (has_reserve()) {
      total_size += 1 +
        ::google::protobuf::internal::WireFormatLite::StringSize(
          this->reserve());
    }

  }
  if (_internal_metadata_.have_unknown_fields()) {
    total_size +=
      ::google::protobuf::internal::WireFormat::ComputeUnknownFieldsSize(
        unknown_fields());
  }
  int cached_size = ::google::protobuf::internal::ToCachedSize(total_size);
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = cached_size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
  return total_size;
}

void Transaction::MergeFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_merge_from_start:LYPROTO.QUOTA.Transaction)
  if (GOOGLE_PREDICT_FALSE(&from == this)) MergeFromFail(__LINE__);
  const Transaction* source =
      ::google::protobuf::internal::DynamicCastToGenerated<const Transaction>(
          &from);
  if (source == NULL) {
  // @@protoc_insertion_point(generalized_merge_from_cast_fail:LYPROTO.QUOTA.Transaction)
    ::google::protobuf::internal::ReflectionOps::Merge(from, this);
  } else {
  // @@protoc_insertion_point(generalized_merge_from_cast_success:LYPROTO.QUOTA.Transaction)
    UnsafeMergeFrom(*source);
  }
}

void Transaction::MergeFrom(const Transaction& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:LYPROTO.QUOTA.Transaction)
  if (GOOGLE_PREDICT_TRUE(&from != this)) {
    UnsafeMergeFrom(from);
  } else {
    MergeFromFail(__LINE__);
  }
}

void Transaction::UnsafeMergeFrom(const Transaction& from) {
  GOOGLE_DCHECK(&from != this);
  if (from._has_bits_[0 / 32] & (0xffu << (0 % 32))) {
    if (from.has_exchangeid()) {
      set_has_exchangeid();
      exchangeid_.AssignWithDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), from.exchangeid_);
    }
    if (from.has_category()) {
      set_has_category();
      category_.AssignWithDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), from.category_);
    }
    if (from.has_securityid()) {
      set_has_securityid();
      securityid_.AssignWithDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), from.securityid_);
    }
    if (from.has_pubsvrtime()) {
      set_has_pubsvrtime();
      pubsvrtime_.AssignWithDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), from.pubsvrtime_);
    }
    if (from.has_tradeindex()) {
      set_has_tradeindex();
      tradeindex_.AssignWithDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), from.tradeindex_);
    }
    if (from.has_tradetime()) {
      set_has_tradetime();
      tradetime_.AssignWithDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), from.tradetime_);
    }
    if (from.has_tradeprice()) {
      set_tradeprice(from.tradeprice());
    }
    if (from.has_tradeqty()) {
      set_tradeqty(from.tradeqty());
    }
  }
  if (from._has_bits_[8 / 32] & (0xffu << (8 % 32))) {
    if (from.has_trademoney()) {
      set_trademoney(from.trademoney());
    }
    if (from.has_side()) {
      set_has_side();
      side_.AssignWithDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), from.side_);
    }
    if (from.has_tradetype()) {
      set_has_tradetype();
      tradetype_.AssignWithDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), from.tradetype_);
    }
    if (from.has_tradecode()) {
      set_has_tradecode();
      tradecode_.AssignWithDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), from.tradecode_);
    }
    if (from.has_offerindex()) {
      set_has_offerindex();
      offerindex_.AssignWithDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), from.offerindex_);
    }
    if (from.has_bidindex()) {
      set_has_bidindex();
      bidindex_.AssignWithDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), from.bidindex_);
    }
    if (from.has_reserve()) {
      set_has_reserve();
      reserve_.AssignWithDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), from.reserve_);
    }
  }
  if (from._internal_metadata_.have_unknown_fields()) {
    ::google::protobuf::UnknownFieldSet::MergeToInternalMetdata(
      from.unknown_fields(), &_internal_metadata_);
  }
}

void Transaction::CopyFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_copy_from_start:LYPROTO.QUOTA.Transaction)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

void Transaction::CopyFrom(const Transaction& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:LYPROTO.QUOTA.Transaction)
  if (&from == this) return;
  Clear();
  UnsafeMergeFrom(from);
}

bool Transaction::IsInitialized() const {
  if ((_has_bits_[0] & 0x00000007) != 0x00000007) return false;

  return true;
}

void Transaction::Swap(Transaction* other) {
  if (other == this) return;
  InternalSwap(other);
}
void Transaction::InternalSwap(Transaction* other) {
  exchangeid_.Swap(&other->exchangeid_);
  category_.Swap(&other->category_);
  securityid_.Swap(&other->securityid_);
  pubsvrtime_.Swap(&other->pubsvrtime_);
  tradeindex_.Swap(&other->tradeindex_);
  tradetime_.Swap(&other->tradetime_);
  std::swap(tradeprice_, other->tradeprice_);
  std::swap(tradeqty_, other->tradeqty_);
  std::swap(trademoney_, other->trademoney_);
  side_.Swap(&other->side_);
  tradetype_.Swap(&other->tradetype_);
  tradecode_.Swap(&other->tradecode_);
  offerindex_.Swap(&other->offerindex_);
  bidindex_.Swap(&other->bidindex_);
  reserve_.Swap(&other->reserve_);
  std::swap(_has_bits_[0], other->_has_bits_[0]);
  _internal_metadata_.Swap(&other->_internal_metadata_);
  std::swap(_cached_size_, other->_cached_size_);
}

::google::protobuf::Metadata Transaction::GetMetadata() const {
  protobuf_AssignDescriptorsOnce();
  ::google::protobuf::Metadata metadata;
  metadata.descriptor = Transaction_descriptor_;
  metadata.reflection = Transaction_reflection_;
  return metadata;
}

#if PROTOBUF_INLINE_NOT_IN_HEADERS
// Transaction

// required string ExchangeID = 1;
bool Transaction::has_exchangeid() const {
  return (_has_bits_[0] & 0x00000001u) != 0;
}
void Transaction::set_has_exchangeid() {
  _has_bits_[0] |= 0x00000001u;
}
void Transaction::clear_has_exchangeid() {
  _has_bits_[0] &= ~0x00000001u;
}
void Transaction::clear_exchangeid() {
  exchangeid_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  clear_has_exchangeid();
}
const ::std::string& Transaction::exchangeid() const {
  // @@protoc_insertion_point(field_get:LYPROTO.QUOTA.Transaction.ExchangeID)
  return exchangeid_.GetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
void Transaction::set_exchangeid(const ::std::string& value) {
  set_has_exchangeid();
  exchangeid_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:LYPROTO.QUOTA.Transaction.ExchangeID)
}
void Transaction::set_exchangeid(const char* value) {
  set_has_exchangeid();
  exchangeid_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:LYPROTO.QUOTA.Transaction.ExchangeID)
}
void Transaction::set_exchangeid(const char* value, size_t size) {
  set_has_exchangeid();
  exchangeid_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:LYPROTO.QUOTA.Transaction.ExchangeID)
}
::std::string* Transaction::mutable_exchangeid() {
  set_has_exchangeid();
  // @@protoc_insertion_point(field_mutable:LYPROTO.QUOTA.Transaction.ExchangeID)
  return exchangeid_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
::std::string* Transaction::release_exchangeid() {
  // @@protoc_insertion_point(field_release:LYPROTO.QUOTA.Transaction.ExchangeID)
  clear_has_exchangeid();
  return exchangeid_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
void Transaction::set_allocated_exchangeid(::std::string* exchangeid) {
  if (exchangeid != NULL) {
    set_has_exchangeid();
  } else {
    clear_has_exchangeid();
  }
  exchangeid_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), exchangeid);
  // @@protoc_insertion_point(field_set_allocated:LYPROTO.QUOTA.Transaction.ExchangeID)
}

// required string Category = 2;
bool Transaction::has_category() const {
  return (_has_bits_[0] & 0x00000002u) != 0;
}
void Transaction::set_has_category() {
  _has_bits_[0] |= 0x00000002u;
}
void Transaction::clear_has_category() {
  _has_bits_[0] &= ~0x00000002u;
}
void Transaction::clear_category() {
  category_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  clear_has_category();
}
const ::std::string& Transaction::category() const {
  // @@protoc_insertion_point(field_get:LYPROTO.QUOTA.Transaction.Category)
  return category_.GetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
void Transaction::set_category(const ::std::string& value) {
  set_has_category();
  category_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:LYPROTO.QUOTA.Transaction.Category)
}
void Transaction::set_category(const char* value) {
  set_has_category();
  category_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:LYPROTO.QUOTA.Transaction.Category)
}
void Transaction::set_category(const char* value, size_t size) {
  set_has_category();
  category_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:LYPROTO.QUOTA.Transaction.Category)
}
::std::string* Transaction::mutable_category() {
  set_has_category();
  // @@protoc_insertion_point(field_mutable:LYPROTO.QUOTA.Transaction.Category)
  return category_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
::std::string* Transaction::release_category() {
  // @@protoc_insertion_point(field_release:LYPROTO.QUOTA.Transaction.Category)
  clear_has_category();
  return category_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
void Transaction::set_allocated_category(::std::string* category) {
  if (category != NULL) {
    set_has_category();
  } else {
    clear_has_category();
  }
  category_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), category);
  // @@protoc_insertion_point(field_set_allocated:LYPROTO.QUOTA.Transaction.Category)
}

// required string SecurityID = 3;
bool Transaction::has_securityid() const {
  return (_has_bits_[0] & 0x00000004u) != 0;
}
void Transaction::set_has_securityid() {
  _has_bits_[0] |= 0x00000004u;
}
void Transaction::clear_has_securityid() {
  _has_bits_[0] &= ~0x00000004u;
}
void Transaction::clear_securityid() {
  securityid_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  clear_has_securityid();
}
const ::std::string& Transaction::securityid() const {
  // @@protoc_insertion_point(field_get:LYPROTO.QUOTA.Transaction.SecurityID)
  return securityid_.GetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
void Transaction::set_securityid(const ::std::string& value) {
  set_has_securityid();
  securityid_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:LYPROTO.QUOTA.Transaction.SecurityID)
}
void Transaction::set_securityid(const char* value) {
  set_has_securityid();
  securityid_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:LYPROTO.QUOTA.Transaction.SecurityID)
}
void Transaction::set_securityid(const char* value, size_t size) {
  set_has_securityid();
  securityid_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:LYPROTO.QUOTA.Transaction.SecurityID)
}
::std::string* Transaction::mutable_securityid() {
  set_has_securityid();
  // @@protoc_insertion_point(field_mutable:LYPROTO.QUOTA.Transaction.SecurityID)
  return securityid_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
::std::string* Transaction::release_securityid() {
  // @@protoc_insertion_point(field_release:LYPROTO.QUOTA.Transaction.SecurityID)
  clear_has_securityid();
  return securityid_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
void Transaction::set_allocated_securityid(::std::string* securityid) {
  if (securityid != NULL) {
    set_has_securityid();
  } else {
    clear_has_securityid();
  }
  securityid_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), securityid);
  // @@protoc_insertion_point(field_set_allocated:LYPROTO.QUOTA.Transaction.SecurityID)
}

// optional string PubSvrTime = 4;
bool Transaction::has_pubsvrtime() const {
  return (_has_bits_[0] & 0x00000008u) != 0;
}
void Transaction::set_has_pubsvrtime() {
  _has_bits_[0] |= 0x00000008u;
}
void Transaction::clear_has_pubsvrtime() {
  _has_bits_[0] &= ~0x00000008u;
}
void Transaction::clear_pubsvrtime() {
  pubsvrtime_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  clear_has_pubsvrtime();
}
const ::std::string& Transaction::pubsvrtime() const {
  // @@protoc_insertion_point(field_get:LYPROTO.QUOTA.Transaction.PubSvrTime)
  return pubsvrtime_.GetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
void Transaction::set_pubsvrtime(const ::std::string& value) {
  set_has_pubsvrtime();
  pubsvrtime_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:LYPROTO.QUOTA.Transaction.PubSvrTime)
}
void Transaction::set_pubsvrtime(const char* value) {
  set_has_pubsvrtime();
  pubsvrtime_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:LYPROTO.QUOTA.Transaction.PubSvrTime)
}
void Transaction::set_pubsvrtime(const char* value, size_t size) {
  set_has_pubsvrtime();
  pubsvrtime_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:LYPROTO.QUOTA.Transaction.PubSvrTime)
}
::std::string* Transaction::mutable_pubsvrtime() {
  set_has_pubsvrtime();
  // @@protoc_insertion_point(field_mutable:LYPROTO.QUOTA.Transaction.PubSvrTime)
  return pubsvrtime_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
::std::string* Transaction::release_pubsvrtime() {
  // @@protoc_insertion_point(field_release:LYPROTO.QUOTA.Transaction.PubSvrTime)
  clear_has_pubsvrtime();
  return pubsvrtime_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
void Transaction::set_allocated_pubsvrtime(::std::string* pubsvrtime) {
  if (pubsvrtime != NULL) {
    set_has_pubsvrtime();
  } else {
    clear_has_pubsvrtime();
  }
  pubsvrtime_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), pubsvrtime);
  // @@protoc_insertion_point(field_set_allocated:LYPROTO.QUOTA.Transaction.PubSvrTime)
}

// optional string TradeIndex = 5;
bool Transaction::has_tradeindex() const {
  return (_has_bits_[0] & 0x00000010u) != 0;
}
void Transaction::set_has_tradeindex() {
  _has_bits_[0] |= 0x00000010u;
}
void Transaction::clear_has_tradeindex() {
  _has_bits_[0] &= ~0x00000010u;
}
void Transaction::clear_tradeindex() {
  tradeindex_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  clear_has_tradeindex();
}
const ::std::string& Transaction::tradeindex() const {
  // @@protoc_insertion_point(field_get:LYPROTO.QUOTA.Transaction.TradeIndex)
  return tradeindex_.GetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
void Transaction::set_tradeindex(const ::std::string& value) {
  set_has_tradeindex();
  tradeindex_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:LYPROTO.QUOTA.Transaction.TradeIndex)
}
void Transaction::set_tradeindex(const char* value) {
  set_has_tradeindex();
  tradeindex_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:LYPROTO.QUOTA.Transaction.TradeIndex)
}
void Transaction::set_tradeindex(const char* value, size_t size) {
  set_has_tradeindex();
  tradeindex_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:LYPROTO.QUOTA.Transaction.TradeIndex)
}
::std::string* Transaction::mutable_tradeindex() {
  set_has_tradeindex();
  // @@protoc_insertion_point(field_mutable:LYPROTO.QUOTA.Transaction.TradeIndex)
  return tradeindex_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
::std::string* Transaction::release_tradeindex() {
  // @@protoc_insertion_point(field_release:LYPROTO.QUOTA.Transaction.TradeIndex)
  clear_has_tradeindex();
  return tradeindex_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
void Transaction::set_allocated_tradeindex(::std::string* tradeindex) {
  if (tradeindex != NULL) {
    set_has_tradeindex();
  } else {
    clear_has_tradeindex();
  }
  tradeindex_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), tradeindex);
  // @@protoc_insertion_point(field_set_allocated:LYPROTO.QUOTA.Transaction.TradeIndex)
}

// optional string TradeTime = 6;
bool Transaction::has_tradetime() const {
  return (_has_bits_[0] & 0x00000020u) != 0;
}
void Transaction::set_has_tradetime() {
  _has_bits_[0] |= 0x00000020u;
}
void Transaction::clear_has_tradetime() {
  _has_bits_[0] &= ~0x00000020u;
}
void Transaction::clear_tradetime() {
  tradetime_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  clear_has_tradetime();
}
const ::std::string& Transaction::tradetime() const {
  // @@protoc_insertion_point(field_get:LYPROTO.QUOTA.Transaction.TradeTime)
  return tradetime_.GetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
void Transaction::set_tradetime(const ::std::string& value) {
  set_has_tradetime();
  tradetime_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:LYPROTO.QUOTA.Transaction.TradeTime)
}
void Transaction::set_tradetime(const char* value) {
  set_has_tradetime();
  tradetime_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:LYPROTO.QUOTA.Transaction.TradeTime)
}
void Transaction::set_tradetime(const char* value, size_t size) {
  set_has_tradetime();
  tradetime_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:LYPROTO.QUOTA.Transaction.TradeTime)
}
::std::string* Transaction::mutable_tradetime() {
  set_has_tradetime();
  // @@protoc_insertion_point(field_mutable:LYPROTO.QUOTA.Transaction.TradeTime)
  return tradetime_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
::std::string* Transaction::release_tradetime() {
  // @@protoc_insertion_point(field_release:LYPROTO.QUOTA.Transaction.TradeTime)
  clear_has_tradetime();
  return tradetime_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
void Transaction::set_allocated_tradetime(::std::string* tradetime) {
  if (tradetime != NULL) {
    set_has_tradetime();
  } else {
    clear_has_tradetime();
  }
  tradetime_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), tradetime);
  // @@protoc_insertion_point(field_set_allocated:LYPROTO.QUOTA.Transaction.TradeTime)
}

// optional int64 TradePrice = 7;
bool Transaction::has_tradeprice() const {
  return (_has_bits_[0] & 0x00000040u) != 0;
}
void Transaction::set_has_tradeprice() {
  _has_bits_[0] |= 0x00000040u;
}
void Transaction::clear_has_tradeprice() {
  _has_bits_[0] &= ~0x00000040u;
}
void Transaction::clear_tradeprice() {
  tradeprice_ = GOOGLE_LONGLONG(0);
  clear_has_tradeprice();
}
::google::protobuf::int64 Transaction::tradeprice() const {
  // @@protoc_insertion_point(field_get:LYPROTO.QUOTA.Transaction.TradePrice)
  return tradeprice_;
}
void Transaction::set_tradeprice(::google::protobuf::int64 value) {
  set_has_tradeprice();
  tradeprice_ = value;
  // @@protoc_insertion_point(field_set:LYPROTO.QUOTA.Transaction.TradePrice)
}

// optional int32 TradeQty = 8;
bool Transaction::has_tradeqty() const {
  return (_has_bits_[0] & 0x00000080u) != 0;
}
void Transaction::set_has_tradeqty() {
  _has_bits_[0] |= 0x00000080u;
}
void Transaction::clear_has_tradeqty() {
  _has_bits_[0] &= ~0x00000080u;
}
void Transaction::clear_tradeqty() {
  tradeqty_ = 0;
  clear_has_tradeqty();
}
::google::protobuf::int32 Transaction::tradeqty() const {
  // @@protoc_insertion_point(field_get:LYPROTO.QUOTA.Transaction.TradeQty)
  return tradeqty_;
}
void Transaction::set_tradeqty(::google::protobuf::int32 value) {
  set_has_tradeqty();
  tradeqty_ = value;
  // @@protoc_insertion_point(field_set:LYPROTO.QUOTA.Transaction.TradeQty)
}

// optional int64 TradeMoney = 9;
bool Transaction::has_trademoney() const {
  return (_has_bits_[0] & 0x00000100u) != 0;
}
void Transaction::set_has_trademoney() {
  _has_bits_[0] |= 0x00000100u;
}
void Transaction::clear_has_trademoney() {
  _has_bits_[0] &= ~0x00000100u;
}
void Transaction::clear_trademoney() {
  trademoney_ = GOOGLE_LONGLONG(0);
  clear_has_trademoney();
}
::google::protobuf::int64 Transaction::trademoney() const {
  // @@protoc_insertion_point(field_get:LYPROTO.QUOTA.Transaction.TradeMoney)
  return trademoney_;
}
void Transaction::set_trademoney(::google::protobuf::int64 value) {
  set_has_trademoney();
  trademoney_ = value;
  // @@protoc_insertion_point(field_set:LYPROTO.QUOTA.Transaction.TradeMoney)
}

// optional string Side = 10;
bool Transaction::has_side() const {
  return (_has_bits_[0] & 0x00000200u) != 0;
}
void Transaction::set_has_side() {
  _has_bits_[0] |= 0x00000200u;
}
void Transaction::clear_has_side() {
  _has_bits_[0] &= ~0x00000200u;
}
void Transaction::clear_side() {
  side_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  clear_has_side();
}
const ::std::string& Transaction::side() const {
  // @@protoc_insertion_point(field_get:LYPROTO.QUOTA.Transaction.Side)
  return side_.GetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
void Transaction::set_side(const ::std::string& value) {
  set_has_side();
  side_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:LYPROTO.QUOTA.Transaction.Side)
}
void Transaction::set_side(const char* value) {
  set_has_side();
  side_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:LYPROTO.QUOTA.Transaction.Side)
}
void Transaction::set_side(const char* value, size_t size) {
  set_has_side();
  side_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:LYPROTO.QUOTA.Transaction.Side)
}
::std::string* Transaction::mutable_side() {
  set_has_side();
  // @@protoc_insertion_point(field_mutable:LYPROTO.QUOTA.Transaction.Side)
  return side_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
::std::string* Transaction::release_side() {
  // @@protoc_insertion_point(field_release:LYPROTO.QUOTA.Transaction.Side)
  clear_has_side();
  return side_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
void Transaction::set_allocated_side(::std::string* side) {
  if (side != NULL) {
    set_has_side();
  } else {
    clear_has_side();
  }
  side_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), side);
  // @@protoc_insertion_point(field_set_allocated:LYPROTO.QUOTA.Transaction.Side)
}

// optional string TradeType = 11;
bool Transaction::has_tradetype() const {
  return (_has_bits_[0] & 0x00000400u) != 0;
}
void Transaction::set_has_tradetype() {
  _has_bits_[0] |= 0x00000400u;
}
void Transaction::clear_has_tradetype() {
  _has_bits_[0] &= ~0x00000400u;
}
void Transaction::clear_tradetype() {
  tradetype_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  clear_has_tradetype();
}
const ::std::string& Transaction::tradetype() const {
  // @@protoc_insertion_point(field_get:LYPROTO.QUOTA.Transaction.TradeType)
  return tradetype_.GetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
void Transaction::set_tradetype(const ::std::string& value) {
  set_has_tradetype();
  tradetype_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:LYPROTO.QUOTA.Transaction.TradeType)
}
void Transaction::set_tradetype(const char* value) {
  set_has_tradetype();
  tradetype_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:LYPROTO.QUOTA.Transaction.TradeType)
}
void Transaction::set_tradetype(const char* value, size_t size) {
  set_has_tradetype();
  tradetype_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:LYPROTO.QUOTA.Transaction.TradeType)
}
::std::string* Transaction::mutable_tradetype() {
  set_has_tradetype();
  // @@protoc_insertion_point(field_mutable:LYPROTO.QUOTA.Transaction.TradeType)
  return tradetype_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
::std::string* Transaction::release_tradetype() {
  // @@protoc_insertion_point(field_release:LYPROTO.QUOTA.Transaction.TradeType)
  clear_has_tradetype();
  return tradetype_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
void Transaction::set_allocated_tradetype(::std::string* tradetype) {
  if (tradetype != NULL) {
    set_has_tradetype();
  } else {
    clear_has_tradetype();
  }
  tradetype_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), tradetype);
  // @@protoc_insertion_point(field_set_allocated:LYPROTO.QUOTA.Transaction.TradeType)
}

// optional string TradeCode = 12;
bool Transaction::has_tradecode() const {
  return (_has_bits_[0] & 0x00000800u) != 0;
}
void Transaction::set_has_tradecode() {
  _has_bits_[0] |= 0x00000800u;
}
void Transaction::clear_has_tradecode() {
  _has_bits_[0] &= ~0x00000800u;
}
void Transaction::clear_tradecode() {
  tradecode_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  clear_has_tradecode();
}
const ::std::string& Transaction::tradecode() const {
  // @@protoc_insertion_point(field_get:LYPROTO.QUOTA.Transaction.TradeCode)
  return tradecode_.GetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
void Transaction::set_tradecode(const ::std::string& value) {
  set_has_tradecode();
  tradecode_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:LYPROTO.QUOTA.Transaction.TradeCode)
}
void Transaction::set_tradecode(const char* value) {
  set_has_tradecode();
  tradecode_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:LYPROTO.QUOTA.Transaction.TradeCode)
}
void Transaction::set_tradecode(const char* value, size_t size) {
  set_has_tradecode();
  tradecode_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:LYPROTO.QUOTA.Transaction.TradeCode)
}
::std::string* Transaction::mutable_tradecode() {
  set_has_tradecode();
  // @@protoc_insertion_point(field_mutable:LYPROTO.QUOTA.Transaction.TradeCode)
  return tradecode_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
::std::string* Transaction::release_tradecode() {
  // @@protoc_insertion_point(field_release:LYPROTO.QUOTA.Transaction.TradeCode)
  clear_has_tradecode();
  return tradecode_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
void Transaction::set_allocated_tradecode(::std::string* tradecode) {
  if (tradecode != NULL) {
    set_has_tradecode();
  } else {
    clear_has_tradecode();
  }
  tradecode_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), tradecode);
  // @@protoc_insertion_point(field_set_allocated:LYPROTO.QUOTA.Transaction.TradeCode)
}

// optional string OfferIndex = 13;
bool Transaction::has_offerindex() const {
  return (_has_bits_[0] & 0x00001000u) != 0;
}
void Transaction::set_has_offerindex() {
  _has_bits_[0] |= 0x00001000u;
}
void Transaction::clear_has_offerindex() {
  _has_bits_[0] &= ~0x00001000u;
}
void Transaction::clear_offerindex() {
  offerindex_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  clear_has_offerindex();
}
const ::std::string& Transaction::offerindex() const {
  // @@protoc_insertion_point(field_get:LYPROTO.QUOTA.Transaction.OfferIndex)
  return offerindex_.GetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
void Transaction::set_offerindex(const ::std::string& value) {
  set_has_offerindex();
  offerindex_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:LYPROTO.QUOTA.Transaction.OfferIndex)
}
void Transaction::set_offerindex(const char* value) {
  set_has_offerindex();
  offerindex_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:LYPROTO.QUOTA.Transaction.OfferIndex)
}
void Transaction::set_offerindex(const char* value, size_t size) {
  set_has_offerindex();
  offerindex_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:LYPROTO.QUOTA.Transaction.OfferIndex)
}
::std::string* Transaction::mutable_offerindex() {
  set_has_offerindex();
  // @@protoc_insertion_point(field_mutable:LYPROTO.QUOTA.Transaction.OfferIndex)
  return offerindex_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
::std::string* Transaction::release_offerindex() {
  // @@protoc_insertion_point(field_release:LYPROTO.QUOTA.Transaction.OfferIndex)
  clear_has_offerindex();
  return offerindex_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
void Transaction::set_allocated_offerindex(::std::string* offerindex) {
  if (offerindex != NULL) {
    set_has_offerindex();
  } else {
    clear_has_offerindex();
  }
  offerindex_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), offerindex);
  // @@protoc_insertion_point(field_set_allocated:LYPROTO.QUOTA.Transaction.OfferIndex)
}

// optional string BidIndex = 14;
bool Transaction::has_bidindex() const {
  return (_has_bits_[0] & 0x00002000u) != 0;
}
void Transaction::set_has_bidindex() {
  _has_bits_[0] |= 0x00002000u;
}
void Transaction::clear_has_bidindex() {
  _has_bits_[0] &= ~0x00002000u;
}
void Transaction::clear_bidindex() {
  bidindex_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  clear_has_bidindex();
}
const ::std::string& Transaction::bidindex() const {
  // @@protoc_insertion_point(field_get:LYPROTO.QUOTA.Transaction.BidIndex)
  return bidindex_.GetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
void Transaction::set_bidindex(const ::std::string& value) {
  set_has_bidindex();
  bidindex_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:LYPROTO.QUOTA.Transaction.BidIndex)
}
void Transaction::set_bidindex(const char* value) {
  set_has_bidindex();
  bidindex_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:LYPROTO.QUOTA.Transaction.BidIndex)
}
void Transaction::set_bidindex(const char* value, size_t size) {
  set_has_bidindex();
  bidindex_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:LYPROTO.QUOTA.Transaction.BidIndex)
}
::std::string* Transaction::mutable_bidindex() {
  set_has_bidindex();
  // @@protoc_insertion_point(field_mutable:LYPROTO.QUOTA.Transaction.BidIndex)
  return bidindex_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
::std::string* Transaction::release_bidindex() {
  // @@protoc_insertion_point(field_release:LYPROTO.QUOTA.Transaction.BidIndex)
  clear_has_bidindex();
  return bidindex_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
void Transaction::set_allocated_bidindex(::std::string* bidindex) {
  if (bidindex != NULL) {
    set_has_bidindex();
  } else {
    clear_has_bidindex();
  }
  bidindex_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), bidindex);
  // @@protoc_insertion_point(field_set_allocated:LYPROTO.QUOTA.Transaction.BidIndex)
}

// optional string reserve = 15;
bool Transaction::has_reserve() const {
  return (_has_bits_[0] & 0x00004000u) != 0;
}
void Transaction::set_has_reserve() {
  _has_bits_[0] |= 0x00004000u;
}
void Transaction::clear_has_reserve() {
  _has_bits_[0] &= ~0x00004000u;
}
void Transaction::clear_reserve() {
  reserve_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  clear_has_reserve();
}
const ::std::string& Transaction::reserve() const {
  // @@protoc_insertion_point(field_get:LYPROTO.QUOTA.Transaction.reserve)
  return reserve_.GetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
void Transaction::set_reserve(const ::std::string& value) {
  set_has_reserve();
  reserve_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:LYPROTO.QUOTA.Transaction.reserve)
}
void Transaction::set_reserve(const char* value) {
  set_has_reserve();
  reserve_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:LYPROTO.QUOTA.Transaction.reserve)
}
void Transaction::set_reserve(const char* value, size_t size) {
  set_has_reserve();
  reserve_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:LYPROTO.QUOTA.Transaction.reserve)
}
::std::string* Transaction::mutable_reserve() {
  set_has_reserve();
  // @@protoc_insertion_point(field_mutable:LYPROTO.QUOTA.Transaction.reserve)
  return reserve_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
::std::string* Transaction::release_reserve() {
  // @@protoc_insertion_point(field_release:LYPROTO.QUOTA.Transaction.reserve)
  clear_has_reserve();
  return reserve_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
void Transaction::set_allocated_reserve(::std::string* reserve) {
  if (reserve != NULL) {
    set_has_reserve();
  } else {
    clear_has_reserve();
  }
  reserve_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), reserve);
  // @@protoc_insertion_point(field_set_allocated:LYPROTO.QUOTA.Transaction.reserve)
}

inline const Transaction* Transaction::internal_default_instance() {
  return &Transaction_default_instance_.get();
}
#endif  // PROTOBUF_INLINE_NOT_IN_HEADERS

// ===================================================================

#if !defined(_MSC_VER) || _MSC_VER >= 1900
const int FutureMarketData::kExchIdFieldNumber;
const int FutureMarketData::kCategoryFieldNumber;
const int FutureMarketData::kStkIdFieldNumber;
const int FutureMarketData::kRcvSvrTimeFieldNumber;
const int FutureMarketData::kPubSvrTimeFieldNumber;
const int FutureMarketData::kStatusFieldNumber;
const int FutureMarketData::kExchTimeFieldNumber;
const int FutureMarketData::kTradeDateFieldNumber;
const int FutureMarketData::kPreCloseFieldNumber;
const int FutureMarketData::kPreSettleFieldNumber;
const int FutureMarketData::kPreOpenPosFieldNumber;
const int FutureMarketData::kHighLimitFieldNumber;
const int FutureMarketData::kLowLimitFieldNumber;
const int FutureMarketData::kOpenFieldNumber;
const int FutureMarketData::kLatestFieldNumber;
const int FutureMarketData::kHighFieldNumber;
const int FutureMarketData::kLowFieldNumber;
const int FutureMarketData::kSettleFieldNumber;
const int FutureMarketData::kLatestVolumeFieldNumber;
const int FutureMarketData::kVolumeFieldNumber;
const int FutureMarketData::kOpenPosFieldNumber;
const int FutureMarketData::kValueFieldNumber;
const int FutureMarketData::kBAFieldNumber;
const int FutureMarketData::kBPFieldNumber;
const int FutureMarketData::kSAFieldNumber;
const int FutureMarketData::kSPFieldNumber;
const int FutureMarketData::kPreDeltaFieldNumber;
const int FutureMarketData::kCurDeltaFieldNumber;
#endif  // !defined(_MSC_VER) || _MSC_VER >= 1900

FutureMarketData::FutureMarketData()
  : ::google::protobuf::Message(), _internal_metadata_(NULL) {
  if (this != internal_default_instance()) protobuf_InitDefaults_lyproto_2equota_2eproto();
  SharedCtor();
  // @@protoc_insertion_point(constructor:LYPROTO.QUOTA.FutureMarketData)
}

void FutureMarketData::InitAsDefaultInstance() {
}

FutureMarketData::FutureMarketData(const FutureMarketData& from)
  : ::google::protobuf::Message(),
    _internal_metadata_(NULL) {
  SharedCtor();
  UnsafeMergeFrom(from);
  // @@protoc_insertion_point(copy_constructor:LYPROTO.QUOTA.FutureMarketData)
}

void FutureMarketData::SharedCtor() {
  _cached_size_ = 0;
  exchid_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  category_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  stkid_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  rcvsvrtime_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  pubsvrtime_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  status_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  exchtime_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  tradedate_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  ::memset(&preclose_, 0, reinterpret_cast<char*>(&curdelta_) -
    reinterpret_cast<char*>(&preclose_) + sizeof(curdelta_));
}

FutureMarketData::~FutureMarketData() {
  // @@protoc_insertion_point(destructor:LYPROTO.QUOTA.FutureMarketData)
  SharedDtor();
}

void FutureMarketData::SharedDtor() {
  exchid_.DestroyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  category_.DestroyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  stkid_.DestroyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  rcvsvrtime_.DestroyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  pubsvrtime_.DestroyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  status_.DestroyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  exchtime_.DestroyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  tradedate_.DestroyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}

void FutureMarketData::SetCachedSize(int size) const {
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
}
const ::google::protobuf::Descriptor* FutureMarketData::descriptor() {
  protobuf_AssignDescriptorsOnce();
  return FutureMarketData_descriptor_;
}

const FutureMarketData& FutureMarketData::default_instance() {
  protobuf_InitDefaults_lyproto_2equota_2eproto();
  return *internal_default_instance();
}

::google::protobuf::internal::ExplicitlyConstructed<FutureMarketData> FutureMarketData_default_instance_;

FutureMarketData* FutureMarketData::New(::google::protobuf::Arena* arena) const {
  FutureMarketData* n = new FutureMarketData;
  if (arena != NULL) {
    arena->Own(n);
  }
  return n;
}

void FutureMarketData::Clear() {
// @@protoc_insertion_point(message_clear_start:LYPROTO.QUOTA.FutureMarketData)
#if defined(__clang__)
#define ZR_HELPER_(f) \
  _Pragma("clang diagnostic push") \
  _Pragma("clang diagnostic ignored \"-Winvalid-offsetof\"") \
  __builtin_offsetof(FutureMarketData, f) \
  _Pragma("clang diagnostic pop")
#else
#define ZR_HELPER_(f) reinterpret_cast<char*>(\
  &reinterpret_cast<FutureMarketData*>(16)->f)
#endif

#define ZR_(first, last) do {\
  ::memset(&(first), 0,\
           ZR_HELPER_(last) - ZR_HELPER_(first) + sizeof(last));\
} while (0)

  if (_has_bits_[0 / 32] & 255u) {
    if (has_exchid()) {
      exchid_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
    }
    if (has_category()) {
      category_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
    }
    if (has_stkid()) {
      stkid_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
    }
    if (has_rcvsvrtime()) {
      rcvsvrtime_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
    }
    if (has_pubsvrtime()) {
      pubsvrtime_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
    }
    if (has_status()) {
      status_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
    }
    if (has_exchtime()) {
      exchtime_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
    }
    if (has_tradedate()) {
      tradedate_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
    }
  }
  if (_has_bits_[8 / 32] & 65280u) {
    ZR_(preclose_, preopenpos_);
    high_ = GOOGLE_LONGLONG(0);
  }
  if (_has_bits_[16 / 32] & 4128768u) {
    ZR_(low_, value_);
    latestvolume_ = 0;
  }
  ZR_(predelta_, curdelta_);

#undef ZR_HELPER_
#undef ZR_

  ba_.Clear();
  bp_.Clear();
  sa_.Clear();
  sp_.Clear();
  _has_bits_.Clear();
  if (_internal_metadata_.have_unknown_fields()) {
    mutable_unknown_fields()->Clear();
  }
}

bool FutureMarketData::MergePartialFromCodedStream(
    ::google::protobuf::io::CodedInputStream* input) {
#define DO_(EXPRESSION) if (!GOOGLE_PREDICT_TRUE(EXPRESSION)) goto failure
  ::google::protobuf::uint32 tag;
  // @@protoc_insertion_point(parse_start:LYPROTO.QUOTA.FutureMarketData)
  for (;;) {
    ::std::pair< ::google::protobuf::uint32, bool> p = input->ReadTagWithCutoff(16383);
    tag = p.first;
    if (!p.second) goto handle_unusual;
    switch (::google::protobuf::internal::WireFormatLite::GetTagFieldNumber(tag)) {
      // required string ExchId = 1;
      case 1: {
        if (tag == 10) {
          DO_(::google::protobuf::internal::WireFormatLite::ReadString(
                input, this->mutable_exchid()));
          ::google::protobuf::internal::WireFormat::VerifyUTF8StringNamedField(
            this->exchid().data(), this->exchid().length(),
            ::google::protobuf::internal::WireFormat::PARSE,
            "LYPROTO.QUOTA.FutureMarketData.ExchId");
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(18)) goto parse_Category;
        break;
      }

      // required string Category = 2;
      case 2: {
        if (tag == 18) {
         parse_Category:
          DO_(::google::protobuf::internal::WireFormatLite::ReadString(
                input, this->mutable_category()));
          ::google::protobuf::internal::WireFormat::VerifyUTF8StringNamedField(
            this->category().data(), this->category().length(),
            ::google::protobuf::internal::WireFormat::PARSE,
            "LYPROTO.QUOTA.FutureMarketData.Category");
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(26)) goto parse_stkId;
        break;
      }

      // required string stkId = 3;
      case 3: {
        if (tag == 26) {
         parse_stkId:
          DO_(::google::protobuf::internal::WireFormatLite::ReadString(
                input, this->mutable_stkid()));
          ::google::protobuf::internal::WireFormat::VerifyUTF8StringNamedField(
            this->stkid().data(), this->stkid().length(),
            ::google::protobuf::internal::WireFormat::PARSE,
            "LYPROTO.QUOTA.FutureMarketData.stkId");
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(34)) goto parse_RcvSvrTime;
        break;
      }

      // optional string RcvSvrTime = 4;
      case 4: {
        if (tag == 34) {
         parse_RcvSvrTime:
          DO_(::google::protobuf::internal::WireFormatLite::ReadString(
                input, this->mutable_rcvsvrtime()));
          ::google::protobuf::internal::WireFormat::VerifyUTF8StringNamedField(
            this->rcvsvrtime().data(), this->rcvsvrtime().length(),
            ::google::protobuf::internal::WireFormat::PARSE,
            "LYPROTO.QUOTA.FutureMarketData.RcvSvrTime");
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(42)) goto parse_PubSvrTime;
        break;
      }

      // optional string PubSvrTime = 5;
      case 5: {
        if (tag == 42) {
         parse_PubSvrTime:
          DO_(::google::protobuf::internal::WireFormatLite::ReadString(
                input, this->mutable_pubsvrtime()));
          ::google::protobuf::internal::WireFormat::VerifyUTF8StringNamedField(
            this->pubsvrtime().data(), this->pubsvrtime().length(),
            ::google::protobuf::internal::WireFormat::PARSE,
            "LYPROTO.QUOTA.FutureMarketData.PubSvrTime");
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(50)) goto parse_Status;
        break;
      }

      // optional string Status = 6;
      case 6: {
        if (tag == 50) {
         parse_Status:
          DO_(::google::protobuf::internal::WireFormatLite::ReadString(
                input, this->mutable_status()));
          ::google::protobuf::internal::WireFormat::VerifyUTF8StringNamedField(
            this->status().data(), this->status().length(),
            ::google::protobuf::internal::WireFormat::PARSE,
            "LYPROTO.QUOTA.FutureMarketData.Status");
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(58)) goto parse_ExchTime;
        break;
      }

      // optional string ExchTime = 7;
      case 7: {
        if (tag == 58) {
         parse_ExchTime:
          DO_(::google::protobuf::internal::WireFormatLite::ReadString(
                input, this->mutable_exchtime()));
          ::google::protobuf::internal::WireFormat::VerifyUTF8StringNamedField(
            this->exchtime().data(), this->exchtime().length(),
            ::google::protobuf::internal::WireFormat::PARSE,
            "LYPROTO.QUOTA.FutureMarketData.ExchTime");
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(66)) goto parse_TradeDate;
        break;
      }

      // optional string TradeDate = 8;
      case 8: {
        if (tag == 66) {
         parse_TradeDate:
          DO_(::google::protobuf::internal::WireFormatLite::ReadString(
                input, this->mutable_tradedate()));
          ::google::protobuf::internal::WireFormat::VerifyUTF8StringNamedField(
            this->tradedate().data(), this->tradedate().length(),
            ::google::protobuf::internal::WireFormat::PARSE,
            "LYPROTO.QUOTA.FutureMarketData.TradeDate");
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(72)) goto parse_PreClose;
        break;
      }

      // optional int64 PreClose = 9;
      case 9: {
        if (tag == 72) {
         parse_PreClose:
          set_has_preclose();
          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_INT64>(
                 input, &preclose_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(80)) goto parse_PreSettle;
        break;
      }

      // optional int64 PreSettle = 10;
      case 10: {
        if (tag == 80) {
         parse_PreSettle:
          set_has_presettle();
          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_INT64>(
                 input, &presettle_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(88)) goto parse_PreOpenPos;
        break;
      }

      // optional int32 PreOpenPos = 11;
      case 11: {
        if (tag == 88) {
         parse_PreOpenPos:
          set_has_preopenpos();
          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int32, ::google::protobuf::internal::WireFormatLite::TYPE_INT32>(
                 input, &preopenpos_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(96)) goto parse_HighLimit;
        break;
      }

      // optional int64 HighLimit = 12;
      case 12: {
        if (tag == 96) {
         parse_HighLimit:
          set_has_highlimit();
          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_INT64>(
                 input, &highlimit_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(104)) goto parse_LowLimit;
        break;
      }

      // optional int64 LowLimit = 13;
      case 13: {
        if (tag == 104) {
         parse_LowLimit:
          set_has_lowlimit();
          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_INT64>(
                 input, &lowlimit_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(112)) goto parse_Open;
        break;
      }

      // optional int64 Open = 14;
      case 14: {
        if (tag == 112) {
         parse_Open:
          set_has_open();
          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_INT64>(
                 input, &open_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(120)) goto parse_Latest;
        break;
      }

      // optional int64 Latest = 15;
      case 15: {
        if (tag == 120) {
         parse_Latest:
          set_has_latest();
          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_INT64>(
                 input, &latest_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(128)) goto parse_High;
        break;
      }

      // optional int64 High = 16;
      case 16: {
        if (tag == 128) {
         parse_High:
          set_has_high();
          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_INT64>(
                 input, &high_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(136)) goto parse_Low;
        break;
      }

      // optional int64 Low = 17;
      case 17: {
        if (tag == 136) {
         parse_Low:
          set_has_low();
          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_INT64>(
                 input, &low_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(144)) goto parse_Settle;
        break;
      }

      // optional int64 Settle = 18;
      case 18: {
        if (tag == 144) {
         parse_Settle:
          set_has_settle();
          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_INT64>(
                 input, &settle_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(152)) goto parse_LatestVolume;
        break;
      }

      // optional int32 LatestVolume = 19;
      case 19: {
        if (tag == 152) {
         parse_LatestVolume:
          set_has_latestvolume();
          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int32, ::google::protobuf::internal::WireFormatLite::TYPE_INT32>(
                 input, &latestvolume_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(160)) goto parse_Volume;
        break;
      }

      // optional int32 Volume = 20;
      case 20: {
        if (tag == 160) {
         parse_Volume:
          set_has_volume();
          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int32, ::google::protobuf::internal::WireFormatLite::TYPE_INT32>(
                 input, &volume_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(168)) goto parse_OpenPos;
        break;
      }

      // optional int32 OpenPos = 21;
      case 21: {
        if (tag == 168) {
         parse_OpenPos:
          set_has_openpos();
          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int32, ::google::protobuf::internal::WireFormatLite::TYPE_INT32>(
                 input, &openpos_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(176)) goto parse_Value;
        break;
      }

      // optional int64 Value = 22;
      case 22: {
        if (tag == 176) {
         parse_Value:
          set_has_value();
          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_INT64>(
                 input, &value_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(186)) goto parse_BA;
        break;
      }

      // repeated int32 BA = 23 [packed = true];
      case 23: {
        if (tag == 186) {
         parse_BA:
          DO_((::google::protobuf::internal::WireFormatLite::ReadPackedPrimitive<
                   ::google::protobuf::int32, ::google::protobuf::internal::WireFormatLite::TYPE_INT32>(
                 input, this->mutable_ba())));
        } else if (tag == 184) {
          DO_((::google::protobuf::internal::WireFormatLite::ReadRepeatedPrimitiveNoInline<
                   ::google::protobuf::int32, ::google::protobuf::internal::WireFormatLite::TYPE_INT32>(
                 2, 186, input, this->mutable_ba())));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(194)) goto parse_BP;
        break;
      }

      // repeated int64 BP = 24 [packed = true];
      case 24: {
        if (tag == 194) {
         parse_BP:
          DO_((::google::protobuf::internal::WireFormatLite::ReadPackedPrimitive<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_INT64>(
                 input, this->mutable_bp())));
        } else if (tag == 192) {
          DO_((::google::protobuf::internal::WireFormatLite::ReadRepeatedPrimitiveNoInline<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_INT64>(
                 2, 194, input, this->mutable_bp())));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(202)) goto parse_SA;
        break;
      }

      // repeated int32 SA = 25 [packed = true];
      case 25: {
        if (tag == 202) {
         parse_SA:
          DO_((::google::protobuf::internal::WireFormatLite::ReadPackedPrimitive<
                   ::google::protobuf::int32, ::google::protobuf::internal::WireFormatLite::TYPE_INT32>(
                 input, this->mutable_sa())));
        } else if (tag == 200) {
          DO_((::google::protobuf::internal::WireFormatLite::ReadRepeatedPrimitiveNoInline<
                   ::google::protobuf::int32, ::google::protobuf::internal::WireFormatLite::TYPE_INT32>(
                 2, 202, input, this->mutable_sa())));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(210)) goto parse_SP;
        break;
      }

      // repeated int64 SP = 26 [packed = true];
      case 26: {
        if (tag == 210) {
         parse_SP:
          DO_((::google::protobuf::internal::WireFormatLite::ReadPackedPrimitive<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_INT64>(
                 input, this->mutable_sp())));
        } else if (tag == 208) {
          DO_((::google::protobuf::internal::WireFormatLite::ReadRepeatedPrimitiveNoInline<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_INT64>(
                 2, 210, input, this->mutable_sp())));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(216)) goto parse_PreDelta;
        break;
      }

      // optional int32 PreDelta = 27;
      case 27: {
        if (tag == 216) {
         parse_PreDelta:
          set_has_predelta();
          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int32, ::google::protobuf::internal::WireFormatLite::TYPE_INT32>(
                 input, &predelta_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(224)) goto parse_CurDelta;
        break;
      }

      // optional int32 CurDelta = 28;
      case 28: {
        if (tag == 224) {
         parse_CurDelta:
          set_has_curdelta();
          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int32, ::google::protobuf::internal::WireFormatLite::TYPE_INT32>(
                 input, &curdelta_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectAtEnd()) goto success;
        break;
      }

      default: {
      handle_unusual:
        if (tag == 0 ||
            ::google::protobuf::internal::WireFormatLite::GetTagWireType(tag) ==
            ::google::protobuf::internal::WireFormatLite::WIRETYPE_END_GROUP) {
          goto success;
        }
        DO_(::google::protobuf::internal::WireFormat::SkipField(
              input, tag, mutable_unknown_fields()));
        break;
      }
    }
  }
success:
  // @@protoc_insertion_point(parse_success:LYPROTO.QUOTA.FutureMarketData)
  return true;
failure:
  // @@protoc_insertion_point(parse_failure:LYPROTO.QUOTA.FutureMarketData)
  return false;
#undef DO_
}

void FutureMarketData::SerializeWithCachedSizes(
    ::google::protobuf::io::CodedOutputStream* output) const {
  // @@protoc_insertion_point(serialize_start:LYPROTO.QUOTA.FutureMarketData)
  // required string ExchId = 1;
  if (has_exchid()) {
    ::google::protobuf::internal::WireFormat::VerifyUTF8StringNamedField(
      this->exchid().data(), this->exchid().length(),
      ::google::protobuf::internal::WireFormat::SERIALIZE,
      "LYPROTO.QUOTA.FutureMarketData.ExchId");
    ::google::protobuf::internal::WireFormatLite::WriteStringMaybeAliased(
      1, this->exchid(), output);
  }

  // required string Category = 2;
  if (has_category()) {
    ::google::protobuf::internal::WireFormat::VerifyUTF8StringNamedField(
      this->category().data(), this->category().length(),
      ::google::protobuf::internal::WireFormat::SERIALIZE,
      "LYPROTO.QUOTA.FutureMarketData.Category");
    ::google::protobuf::internal::WireFormatLite::WriteStringMaybeAliased(
      2, this->category(), output);
  }

  // required string stkId = 3;
  if (has_stkid()) {
    ::google::protobuf::internal::WireFormat::VerifyUTF8StringNamedField(
      this->stkid().data(), this->stkid().length(),
      ::google::protobuf::internal::WireFormat::SERIALIZE,
      "LYPROTO.QUOTA.FutureMarketData.stkId");
    ::google::protobuf::internal::WireFormatLite::WriteStringMaybeAliased(
      3, this->stkid(), output);
  }

  // optional string RcvSvrTime = 4;
  if (has_rcvsvrtime()) {
    ::google::protobuf::internal::WireFormat::VerifyUTF8StringNamedField(
      this->rcvsvrtime().data(), this->rcvsvrtime().length(),
      ::google::protobuf::internal::WireFormat::SERIALIZE,
      "LYPROTO.QUOTA.FutureMarketData.RcvSvrTime");
    ::google::protobuf::internal::WireFormatLite::WriteStringMaybeAliased(
      4, this->rcvsvrtime(), output);
  }

  // optional string PubSvrTime = 5;
  if (has_pubsvrtime()) {
    ::google::protobuf::internal::WireFormat::VerifyUTF8StringNamedField(
      this->pubsvrtime().data(), this->pubsvrtime().length(),
      ::google::protobuf::internal::WireFormat::SERIALIZE,
      "LYPROTO.QUOTA.FutureMarketData.PubSvrTime");
    ::google::protobuf::internal::WireFormatLite::WriteStringMaybeAliased(
      5, this->pubsvrtime(), output);
  }

  // optional string Status = 6;
  if (has_status()) {
    ::google::protobuf::internal::WireFormat::VerifyUTF8StringNamedField(
      this->status().data(), this->status().length(),
      ::google::protobuf::internal::WireFormat::SERIALIZE,
      "LYPROTO.QUOTA.FutureMarketData.Status");
    ::google::protobuf::internal::WireFormatLite::WriteStringMaybeAliased(
      6, this->status(), output);
  }

  // optional string ExchTime = 7;
  if (has_exchtime()) {
    ::google::protobuf::internal::WireFormat::VerifyUTF8StringNamedField(
      this->exchtime().data(), this->exchtime().length(),
      ::google::protobuf::internal::WireFormat::SERIALIZE,
      "LYPROTO.QUOTA.FutureMarketData.ExchTime");
    ::google::protobuf::internal::WireFormatLite::WriteStringMaybeAliased(
      7, this->exchtime(), output);
  }

  // optional string TradeDate = 8;
  if (has_tradedate()) {
    ::google::protobuf::internal::WireFormat::VerifyUTF8StringNamedField(
      this->tradedate().data(), this->tradedate().length(),
      ::google::protobuf::internal::WireFormat::SERIALIZE,
      "LYPROTO.QUOTA.FutureMarketData.TradeDate");
    ::google::protobuf::internal::WireFormatLite::WriteStringMaybeAliased(
      8, this->tradedate(), output);
  }

  // optional int64 PreClose = 9;
  if (has_preclose()) {
    ::google::protobuf::internal::WireFormatLite::WriteInt64(9, this->preclose(), output);
  }

  // optional int64 PreSettle = 10;
  if (has_presettle()) {
    ::google::protobuf::internal::WireFormatLite::WriteInt64(10, this->presettle(), output);
  }

  // optional int32 PreOpenPos = 11;
  if (has_preopenpos()) {
    ::google::protobuf::internal::WireFormatLite::WriteInt32(11, this->preopenpos(), output);
  }

  // optional int64 HighLimit = 12;
  if (has_highlimit()) {
    ::google::protobuf::internal::WireFormatLite::WriteInt64(12, this->highlimit(), output);
  }

  // optional int64 LowLimit = 13;
  if (has_lowlimit()) {
    ::google::protobuf::internal::WireFormatLite::WriteInt64(13, this->lowlimit(), output);
  }

  // optional int64 Open = 14;
  if (has_open()) {
    ::google::protobuf::internal::WireFormatLite::WriteInt64(14, this->open(), output);
  }

  // optional int64 Latest = 15;
  if (has_latest()) {
    ::google::protobuf::internal::WireFormatLite::WriteInt64(15, this->latest(), output);
  }

  // optional int64 High = 16;
  if (has_high()) {
    ::google::protobuf::internal::WireFormatLite::WriteInt64(16, this->high(), output);
  }

  // optional int64 Low = 17;
  if (has_low()) {
    ::google::protobuf::internal::WireFormatLite::WriteInt64(17, this->low(), output);
  }

  // optional int64 Settle = 18;
  if (has_settle()) {
    ::google::protobuf::internal::WireFormatLite::WriteInt64(18, this->settle(), output);
  }

  // optional int32 LatestVolume = 19;
  if (has_latestvolume()) {
    ::google::protobuf::internal::WireFormatLite::WriteInt32(19, this->latestvolume(), output);
  }

  // optional int32 Volume = 20;
  if (has_volume()) {
    ::google::protobuf::internal::WireFormatLite::WriteInt32(20, this->volume(), output);
  }

  // optional int32 OpenPos = 21;
  if (has_openpos()) {
    ::google::protobuf::internal::WireFormatLite::WriteInt32(21, this->openpos(), output);
  }

  // optional int64 Value = 22;
  if (has_value()) {
    ::google::protobuf::internal::WireFormatLite::WriteInt64(22, this->value(), output);
  }

  // repeated int32 BA = 23 [packed = true];
  if (this->ba_size() > 0) {
    ::google::protobuf::internal::WireFormatLite::WriteTag(23, ::google::protobuf::internal::WireFormatLite::WIRETYPE_LENGTH_DELIMITED, output);
    output->WriteVarint32(_ba_cached_byte_size_);
  }
  for (int i = 0; i < this->ba_size(); i++) {
    ::google::protobuf::internal::WireFormatLite::WriteInt32NoTag(
      this->ba(i), output);
  }

  // repeated int64 BP = 24 [packed = true];
  if (this->bp_size() > 0) {
    ::google::protobuf::internal::WireFormatLite::WriteTag(24, ::google::protobuf::internal::WireFormatLite::WIRETYPE_LENGTH_DELIMITED, output);
    output->WriteVarint32(_bp_cached_byte_size_);
  }
  for (int i = 0; i < this->bp_size(); i++) {
    ::google::protobuf::internal::WireFormatLite::WriteInt64NoTag(
      this->bp(i), output);
  }

  // repeated int32 SA = 25 [packed = true];
  if (this->sa_size() > 0) {
    ::google::protobuf::internal::WireFormatLite::WriteTag(25, ::google::protobuf::internal::WireFormatLite::WIRETYPE_LENGTH_DELIMITED, output);
    output->WriteVarint32(_sa_cached_byte_size_);
  }
  for (int i = 0; i < this->sa_size(); i++) {
    ::google::protobuf::internal::WireFormatLite::WriteInt32NoTag(
      this->sa(i), output);
  }

  // repeated int64 SP = 26 [packed = true];
  if (this->sp_size() > 0) {
    ::google::protobuf::internal::WireFormatLite::WriteTag(26, ::google::protobuf::internal::WireFormatLite::WIRETYPE_LENGTH_DELIMITED, output);
    output->WriteVarint32(_sp_cached_byte_size_);
  }
  for (int i = 0; i < this->sp_size(); i++) {
    ::google::protobuf::internal::WireFormatLite::WriteInt64NoTag(
      this->sp(i), output);
  }

  // optional int32 PreDelta = 27;
  if (has_predelta()) {
    ::google::protobuf::internal::WireFormatLite::WriteInt32(27, this->predelta(), output);
  }

  // optional int32 CurDelta = 28;
  if (has_curdelta()) {
    ::google::protobuf::internal::WireFormatLite::WriteInt32(28, this->curdelta(), output);
  }

  if (_internal_metadata_.have_unknown_fields()) {
    ::google::protobuf::internal::WireFormat::SerializeUnknownFields(
        unknown_fields(), output);
  }
  // @@protoc_insertion_point(serialize_end:LYPROTO.QUOTA.FutureMarketData)
}

::google::protobuf::uint8* FutureMarketData::InternalSerializeWithCachedSizesToArray(
    bool deterministic, ::google::protobuf::uint8* target) const {
  (void)deterministic; // Unused
  // @@protoc_insertion_point(serialize_to_array_start:LYPROTO.QUOTA.FutureMarketData)
  // required string ExchId = 1;
  if (has_exchid()) {
    ::google::protobuf::internal::WireFormat::VerifyUTF8StringNamedField(
      this->exchid().data(), this->exchid().length(),
      ::google::protobuf::internal::WireFormat::SERIALIZE,
      "LYPROTO.QUOTA.FutureMarketData.ExchId");
    target =
      ::google::protobuf::internal::WireFormatLite::WriteStringToArray(
        1, this->exchid(), target);
  }

  // required string Category = 2;
  if (has_category()) {
    ::google::protobuf::internal::WireFormat::VerifyUTF8StringNamedField(
      this->category().data(), this->category().length(),
      ::google::protobuf::internal::WireFormat::SERIALIZE,
      "LYPROTO.QUOTA.FutureMarketData.Category");
    target =
      ::google::protobuf::internal::WireFormatLite::WriteStringToArray(
        2, this->category(), target);
  }

  // required string stkId = 3;
  if (has_stkid()) {
    ::google::protobuf::internal::WireFormat::VerifyUTF8StringNamedField(
      this->stkid().data(), this->stkid().length(),
      ::google::protobuf::internal::WireFormat::SERIALIZE,
      "LYPROTO.QUOTA.FutureMarketData.stkId");
    target =
      ::google::protobuf::internal::WireFormatLite::WriteStringToArray(
        3, this->stkid(), target);
  }

  // optional string RcvSvrTime = 4;
  if (has_rcvsvrtime()) {
    ::google::protobuf::internal::WireFormat::VerifyUTF8StringNamedField(
      this->rcvsvrtime().data(), this->rcvsvrtime().length(),
      ::google::protobuf::internal::WireFormat::SERIALIZE,
      "LYPROTO.QUOTA.FutureMarketData.RcvSvrTime");
    target =
      ::google::protobuf::internal::WireFormatLite::WriteStringToArray(
        4, this->rcvsvrtime(), target);
  }

  // optional string PubSvrTime = 5;
  if (has_pubsvrtime()) {
    ::google::protobuf::internal::WireFormat::VerifyUTF8StringNamedField(
      this->pubsvrtime().data(), this->pubsvrtime().length(),
      ::google::protobuf::internal::WireFormat::SERIALIZE,
      "LYPROTO.QUOTA.FutureMarketData.PubSvrTime");
    target =
      ::google::protobuf::internal::WireFormatLite::WriteStringToArray(
        5, this->pubsvrtime(), target);
  }

  // optional string Status = 6;
  if (has_status()) {
    ::google::protobuf::internal::WireFormat::VerifyUTF8StringNamedField(
      this->status().data(), this->status().length(),
      ::google::protobuf::internal::WireFormat::SERIALIZE,
      "LYPROTO.QUOTA.FutureMarketData.Status");
    target =
      ::google::protobuf::internal::WireFormatLite::WriteStringToArray(
        6, this->status(), target);
  }

  // optional string ExchTime = 7;
  if (has_exchtime()) {
    ::google::protobuf::internal::WireFormat::VerifyUTF8StringNamedField(
      this->exchtime().data(), this->exchtime().length(),
      ::google::protobuf::internal::WireFormat::SERIALIZE,
      "LYPROTO.QUOTA.FutureMarketData.ExchTime");
    target =
      ::google::protobuf::internal::WireFormatLite::WriteStringToArray(
        7, this->exchtime(), target);
  }

  // optional string TradeDate = 8;
  if (has_tradedate()) {
    ::google::protobuf::internal::WireFormat::VerifyUTF8StringNamedField(
      this->tradedate().data(), this->tradedate().length(),
      ::google::protobuf::internal::WireFormat::SERIALIZE,
      "LYPROTO.QUOTA.FutureMarketData.TradeDate");
    target =
      ::google::protobuf::internal::WireFormatLite::WriteStringToArray(
        8, this->tradedate(), target);
  }

  // optional int64 PreClose = 9;
  if (has_preclose()) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt64ToArray(9, this->preclose(), target);
  }

  // optional int64 PreSettle = 10;
  if (has_presettle()) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt64ToArray(10, this->presettle(), target);
  }

  // optional int32 PreOpenPos = 11;
  if (has_preopenpos()) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt32ToArray(11, this->preopenpos(), target);
  }

  // optional int64 HighLimit = 12;
  if (has_highlimit()) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt64ToArray(12, this->highlimit(), target);
  }

  // optional int64 LowLimit = 13;
  if (has_lowlimit()) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt64ToArray(13, this->lowlimit(), target);
  }

  // optional int64 Open = 14;
  if (has_open()) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt64ToArray(14, this->open(), target);
  }

  // optional int64 Latest = 15;
  if (has_latest()) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt64ToArray(15, this->latest(), target);
  }

  // optional int64 High = 16;
  if (has_high()) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt64ToArray(16, this->high(), target);
  }

  // optional int64 Low = 17;
  if (has_low()) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt64ToArray(17, this->low(), target);
  }

  // optional int64 Settle = 18;
  if (has_settle()) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt64ToArray(18, this->settle(), target);
  }

  // optional int32 LatestVolume = 19;
  if (has_latestvolume()) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt32ToArray(19, this->latestvolume(), target);
  }

  // optional int32 Volume = 20;
  if (has_volume()) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt32ToArray(20, this->volume(), target);
  }

  // optional int32 OpenPos = 21;
  if (has_openpos()) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt32ToArray(21, this->openpos(), target);
  }

  // optional int64 Value = 22;
  if (has_value()) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt64ToArray(22, this->value(), target);
  }

  // repeated int32 BA = 23 [packed = true];
  if (this->ba_size() > 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteTagToArray(
      23,
      ::google::protobuf::internal::WireFormatLite::WIRETYPE_LENGTH_DELIMITED,
      target);
    target = ::google::protobuf::io::CodedOutputStream::WriteVarint32ToArray(
      _ba_cached_byte_size_, target);
  }
  for (int i = 0; i < this->ba_size(); i++) {
    target = ::google::protobuf::internal::WireFormatLite::
      WriteInt32NoTagToArray(this->ba(i), target);
  }

  // repeated int64 BP = 24 [packed = true];
  if (this->bp_size() > 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteTagToArray(
      24,
      ::google::protobuf::internal::WireFormatLite::WIRETYPE_LENGTH_DELIMITED,
      target);
    target = ::google::protobuf::io::CodedOutputStream::WriteVarint32ToArray(
      _bp_cached_byte_size_, target);
  }
  for (int i = 0; i < this->bp_size(); i++) {
    target = ::google::protobuf::internal::WireFormatLite::
      WriteInt64NoTagToArray(this->bp(i), target);
  }

  // repeated int32 SA = 25 [packed = true];
  if (this->sa_size() > 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteTagToArray(
      25,
      ::google::protobuf::internal::WireFormatLite::WIRETYPE_LENGTH_DELIMITED,
      target);
    target = ::google::protobuf::io::CodedOutputStream::WriteVarint32ToArray(
      _sa_cached_byte_size_, target);
  }
  for (int i = 0; i < this->sa_size(); i++) {
    target = ::google::protobuf::internal::WireFormatLite::
      WriteInt32NoTagToArray(this->sa(i), target);
  }

  // repeated int64 SP = 26 [packed = true];
  if (this->sp_size() > 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteTagToArray(
      26,
      ::google::protobuf::internal::WireFormatLite::WIRETYPE_LENGTH_DELIMITED,
      target);
    target = ::google::protobuf::io::CodedOutputStream::WriteVarint32ToArray(
      _sp_cached_byte_size_, target);
  }
  for (int i = 0; i < this->sp_size(); i++) {
    target = ::google::protobuf::internal::WireFormatLite::
      WriteInt64NoTagToArray(this->sp(i), target);
  }

  // optional int32 PreDelta = 27;
  if (has_predelta()) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt32ToArray(27, this->predelta(), target);
  }

  // optional int32 CurDelta = 28;
  if (has_curdelta()) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt32ToArray(28, this->curdelta(), target);
  }

  if (_internal_metadata_.have_unknown_fields()) {
    target = ::google::protobuf::internal::WireFormat::SerializeUnknownFieldsToArray(
        unknown_fields(), target);
  }
  // @@protoc_insertion_point(serialize_to_array_end:LYPROTO.QUOTA.FutureMarketData)
  return target;
}

size_t FutureMarketData::RequiredFieldsByteSizeFallback() const {
// @@protoc_insertion_point(required_fields_byte_size_fallback_start:LYPROTO.QUOTA.FutureMarketData)
  size_t total_size = 0;

  if (has_exchid()) {
    // required string ExchId = 1;
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::StringSize(
        this->exchid());
  }

  if (has_category()) {
    // required string Category = 2;
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::StringSize(
        this->category());
  }

  if (has_stkid()) {
    // required string stkId = 3;
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::StringSize(
        this->stkid());
  }

  return total_size;
}
size_t FutureMarketData::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:LYPROTO.QUOTA.FutureMarketData)
  size_t total_size = 0;

  if (((_has_bits_[0] & 0x00000007) ^ 0x00000007) == 0) {  // All required fields are present.
    // required string ExchId = 1;
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::StringSize(
        this->exchid());

    // required string Category = 2;
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::StringSize(
        this->category());

    // required string stkId = 3;
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::StringSize(
        this->stkid());

  } else {
    total_size += RequiredFieldsByteSizeFallback();
  }
  if (_has_bits_[3 / 32] & 248u) {
    // optional string RcvSvrTime = 4;
    if (has_rcvsvrtime()) {
      total_size += 1 +
        ::google::protobuf::internal::WireFormatLite::StringSize(
          this->rcvsvrtime());
    }

    // optional string PubSvrTime = 5;
    if (has_pubsvrtime()) {
      total_size += 1 +
        ::google::protobuf::internal::WireFormatLite::StringSize(
          this->pubsvrtime());
    }

    // optional string Status = 6;
    if (has_status()) {
      total_size += 1 +
        ::google::protobuf::internal::WireFormatLite::StringSize(
          this->status());
    }

    // optional string ExchTime = 7;
    if (has_exchtime()) {
      total_size += 1 +
        ::google::protobuf::internal::WireFormatLite::StringSize(
          this->exchtime());
    }

    // optional string TradeDate = 8;
    if (has_tradedate()) {
      total_size += 1 +
        ::google::protobuf::internal::WireFormatLite::StringSize(
          this->tradedate());
    }

  }
  if (_has_bits_[8 / 32] & 65280u) {
    // optional int64 PreClose = 9;
    if (has_preclose()) {
      total_size += 1 +
        ::google::protobuf::internal::WireFormatLite::Int64Size(
          this->preclose());
    }

    // optional int64 PreSettle = 10;
    if (has_presettle()) {
      total_size += 1 +
        ::google::protobuf::internal::WireFormatLite::Int64Size(
          this->presettle());
    }

    // optional int32 PreOpenPos = 11;
    if (has_preopenpos()) {
      total_size += 1 +
        ::google::protobuf::internal::WireFormatLite::Int32Size(
          this->preopenpos());
    }

    // optional int64 HighLimit = 12;
    if (has_highlimit()) {
      total_size += 1 +
        ::google::protobuf::internal::WireFormatLite::Int64Size(
          this->highlimit());
    }

    // optional int64 LowLimit = 13;
    if (has_lowlimit()) {
      total_size += 1 +
        ::google::protobuf::internal::WireFormatLite::Int64Size(
          this->lowlimit());
    }

    // optional int64 Open = 14;
    if (has_open()) {
      total_size += 1 +
        ::google::protobuf::internal::WireFormatLite::Int64Size(
          this->open());
    }

    // optional int64 Latest = 15;
    if (has_latest()) {
      total_size += 1 +
        ::google::protobuf::internal::WireFormatLite::Int64Size(
          this->latest());
    }

    // optional int64 High = 16;
    if (has_high()) {
      total_size += 2 +
        ::google::protobuf::internal::WireFormatLite::Int64Size(
          this->high());
    }

  }
  if (_has_bits_[16 / 32] & 4128768u) {
    // optional int64 Low = 17;
    if (has_low()) {
      total_size += 2 +
        ::google::protobuf::internal::WireFormatLite::Int64Size(
          this->low());
    }

    // optional int64 Settle = 18;
    if (has_settle()) {
      total_size += 2 +
        ::google::protobuf::internal::WireFormatLite::Int64Size(
          this->settle());
    }

    // optional int32 LatestVolume = 19;
    if (has_latestvolume()) {
      total_size += 2 +
        ::google::protobuf::internal::WireFormatLite::Int32Size(
          this->latestvolume());
    }

    // optional int32 Volume = 20;
    if (has_volume()) {
      total_size += 2 +
        ::google::protobuf::internal::WireFormatLite::Int32Size(
          this->volume());
    }

    // optional int32 OpenPos = 21;
    if (has_openpos()) {
      total_size += 2 +
        ::google::protobuf::internal::WireFormatLite::Int32Size(
          this->openpos());
    }

    // optional int64 Value = 22;
    if (has_value()) {
      total_size += 2 +
        ::google::protobuf::internal::WireFormatLite::Int64Size(
          this->value());
    }

  }
  if (_has_bits_[26 / 32] & 201326592u) {
    // optional int32 PreDelta = 27;
    if (has_predelta()) {
      total_size += 2 +
        ::google::protobuf::internal::WireFormatLite::Int32Size(
          this->predelta());
    }

    // optional int32 CurDelta = 28;
    if (has_curdelta()) {
      total_size += 2 +
        ::google::protobuf::internal::WireFormatLite::Int32Size(
          this->curdelta());
    }

  }
  // repeated int32 BA = 23 [packed = true];
  {
    size_t data_size = 0;
    unsigned int count = this->ba_size();
    for (unsigned int i = 0; i < count; i++) {
      data_size += ::google::protobuf::internal::WireFormatLite::
        Int32Size(this->ba(i));
    }
    if (data_size > 0) {
      total_size += 2 +
        ::google::protobuf::internal::WireFormatLite::Int32Size(data_size);
    }
    int cached_size = ::google::protobuf::internal::ToCachedSize(data_size);
    GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
    _ba_cached_byte_size_ = cached_size;
    GOOGLE_SAFE_CONCURRENT_WRITES_END();
    total_size += data_size;
  }

  // repeated int64 BP = 24 [packed = true];
  {
    size_t data_size = 0;
    unsigned int count = this->bp_size();
    for (unsigned int i = 0; i < count; i++) {
      data_size += ::google::protobuf::internal::WireFormatLite::
        Int64Size(this->bp(i));
    }
    if (data_size > 0) {
      total_size += 2 +
        ::google::protobuf::internal::WireFormatLite::Int32Size(data_size);
    }
    int cached_size = ::google::protobuf::internal::ToCachedSize(data_size);
    GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
    _bp_cached_byte_size_ = cached_size;
    GOOGLE_SAFE_CONCURRENT_WRITES_END();
    total_size += data_size;
  }

  // repeated int32 SA = 25 [packed = true];
  {
    size_t data_size = 0;
    unsigned int count = this->sa_size();
    for (unsigned int i = 0; i < count; i++) {
      data_size += ::google::protobuf::internal::WireFormatLite::
        Int32Size(this->sa(i));
    }
    if (data_size > 0) {
      total_size += 2 +
        ::google::protobuf::internal::WireFormatLite::Int32Size(data_size);
    }
    int cached_size = ::google::protobuf::internal::ToCachedSize(data_size);
    GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
    _sa_cached_byte_size_ = cached_size;
    GOOGLE_SAFE_CONCURRENT_WRITES_END();
    total_size += data_size;
  }

  // repeated int64 SP = 26 [packed = true];
  {
    size_t data_size = 0;
    unsigned int count = this->sp_size();
    for (unsigned int i = 0; i < count; i++) {
      data_size += ::google::protobuf::internal::WireFormatLite::
        Int64Size(this->sp(i));
    }
    if (data_size > 0) {
      total_size += 2 +
        ::google::protobuf::internal::WireFormatLite::Int32Size(data_size);
    }
    int cached_size = ::google::protobuf::internal::ToCachedSize(data_size);
    GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
    _sp_cached_byte_size_ = cached_size;
    GOOGLE_SAFE_CONCURRENT_WRITES_END();
    total_size += data_size;
  }

  if (_internal_metadata_.have_unknown_fields()) {
    total_size +=
      ::google::protobuf::internal::WireFormat::ComputeUnknownFieldsSize(
        unknown_fields());
  }
  int cached_size = ::google::protobuf::internal::ToCachedSize(total_size);
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = cached_size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
  return total_size;
}

void FutureMarketData::MergeFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_merge_from_start:LYPROTO.QUOTA.FutureMarketData)
  if (GOOGLE_PREDICT_FALSE(&from == this)) MergeFromFail(__LINE__);
  const FutureMarketData* source =
      ::google::protobuf::internal::DynamicCastToGenerated<const FutureMarketData>(
          &from);
  if (source == NULL) {
  // @@protoc_insertion_point(generalized_merge_from_cast_fail:LYPROTO.QUOTA.FutureMarketData)
    ::google::protobuf::internal::ReflectionOps::Merge(from, this);
  } else {
  // @@protoc_insertion_point(generalized_merge_from_cast_success:LYPROTO.QUOTA.FutureMarketData)
    UnsafeMergeFrom(*source);
  }
}

void FutureMarketData::MergeFrom(const FutureMarketData& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:LYPROTO.QUOTA.FutureMarketData)
  if (GOOGLE_PREDICT_TRUE(&from != this)) {
    UnsafeMergeFrom(from);
  } else {
    MergeFromFail(__LINE__);
  }
}

void FutureMarketData::UnsafeMergeFrom(const FutureMarketData& from) {
  GOOGLE_DCHECK(&from != this);
  ba_.UnsafeMergeFrom(from.ba_);
  bp_.UnsafeMergeFrom(from.bp_);
  sa_.UnsafeMergeFrom(from.sa_);
  sp_.UnsafeMergeFrom(from.sp_);
  if (from._has_bits_[0 / 32] & (0xffu << (0 % 32))) {
    if (from.has_exchid()) {
      set_has_exchid();
      exchid_.AssignWithDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), from.exchid_);
    }
    if (from.has_category()) {
      set_has_category();
      category_.AssignWithDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), from.category_);
    }
    if (from.has_stkid()) {
      set_has_stkid();
      stkid_.AssignWithDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), from.stkid_);
    }
    if (from.has_rcvsvrtime()) {
      set_has_rcvsvrtime();
      rcvsvrtime_.AssignWithDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), from.rcvsvrtime_);
    }
    if (from.has_pubsvrtime()) {
      set_has_pubsvrtime();
      pubsvrtime_.AssignWithDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), from.pubsvrtime_);
    }
    if (from.has_status()) {
      set_has_status();
      status_.AssignWithDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), from.status_);
    }
    if (from.has_exchtime()) {
      set_has_exchtime();
      exchtime_.AssignWithDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), from.exchtime_);
    }
    if (from.has_tradedate()) {
      set_has_tradedate();
      tradedate_.AssignWithDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), from.tradedate_);
    }
  }
  if (from._has_bits_[8 / 32] & (0xffu << (8 % 32))) {
    if (from.has_preclose()) {
      set_preclose(from.preclose());
    }
    if (from.has_presettle()) {
      set_presettle(from.presettle());
    }
    if (from.has_preopenpos()) {
      set_preopenpos(from.preopenpos());
    }
    if (from.has_highlimit()) {
      set_highlimit(from.highlimit());
    }
    if (from.has_lowlimit()) {
      set_lowlimit(from.lowlimit());
    }
    if (from.has_open()) {
      set_open(from.open());
    }
    if (from.has_latest()) {
      set_latest(from.latest());
    }
    if (from.has_high()) {
      set_high(from.high());
    }
  }
  if (from._has_bits_[16 / 32] & (0xffu << (16 % 32))) {
    if (from.has_low()) {
      set_low(from.low());
    }
    if (from.has_settle()) {
      set_settle(from.settle());
    }
    if (from.has_latestvolume()) {
      set_latestvolume(from.latestvolume());
    }
    if (from.has_volume()) {
      set_volume(from.volume());
    }
    if (from.has_openpos()) {
      set_openpos(from.openpos());
    }
    if (from.has_value()) {
      set_value(from.value());
    }
  }
  if (from._has_bits_[26 / 32] & (0xffu << (26 % 32))) {
    if (from.has_predelta()) {
      set_predelta(from.predelta());
    }
    if (from.has_curdelta()) {
      set_curdelta(from.curdelta());
    }
  }
  if (from._internal_metadata_.have_unknown_fields()) {
    ::google::protobuf::UnknownFieldSet::MergeToInternalMetdata(
      from.unknown_fields(), &_internal_metadata_);
  }
}

void FutureMarketData::CopyFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_copy_from_start:LYPROTO.QUOTA.FutureMarketData)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

void FutureMarketData::CopyFrom(const FutureMarketData& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:LYPROTO.QUOTA.FutureMarketData)
  if (&from == this) return;
  Clear();
  UnsafeMergeFrom(from);
}

bool FutureMarketData::IsInitialized() const {
  if ((_has_bits_[0] & 0x00000007) != 0x00000007) return false;

  return true;
}

void FutureMarketData::Swap(FutureMarketData* other) {
  if (other == this) return;
  InternalSwap(other);
}
void FutureMarketData::InternalSwap(FutureMarketData* other) {
  exchid_.Swap(&other->exchid_);
  category_.Swap(&other->category_);
  stkid_.Swap(&other->stkid_);
  rcvsvrtime_.Swap(&other->rcvsvrtime_);
  pubsvrtime_.Swap(&other->pubsvrtime_);
  status_.Swap(&other->status_);
  exchtime_.Swap(&other->exchtime_);
  tradedate_.Swap(&other->tradedate_);
  std::swap(preclose_, other->preclose_);
  std::swap(presettle_, other->presettle_);
  std::swap(preopenpos_, other->preopenpos_);
  std::swap(highlimit_, other->highlimit_);
  std::swap(lowlimit_, other->lowlimit_);
  std::swap(open_, other->open_);
  std::swap(latest_, other->latest_);
  std::swap(high_, other->high_);
  std::swap(low_, other->low_);
  std::swap(settle_, other->settle_);
  std::swap(latestvolume_, other->latestvolume_);
  std::swap(volume_, other->volume_);
  std::swap(openpos_, other->openpos_);
  std::swap(value_, other->value_);
  ba_.UnsafeArenaSwap(&other->ba_);
  bp_.UnsafeArenaSwap(&other->bp_);
  sa_.UnsafeArenaSwap(&other->sa_);
  sp_.UnsafeArenaSwap(&other->sp_);
  std::swap(predelta_, other->predelta_);
  std::swap(curdelta_, other->curdelta_);
  std::swap(_has_bits_[0], other->_has_bits_[0]);
  _internal_metadata_.Swap(&other->_internal_metadata_);
  std::swap(_cached_size_, other->_cached_size_);
}

::google::protobuf::Metadata FutureMarketData::GetMetadata() const {
  protobuf_AssignDescriptorsOnce();
  ::google::protobuf::Metadata metadata;
  metadata.descriptor = FutureMarketData_descriptor_;
  metadata.reflection = FutureMarketData_reflection_;
  return metadata;
}

#if PROTOBUF_INLINE_NOT_IN_HEADERS
// FutureMarketData

// required string ExchId = 1;
bool FutureMarketData::has_exchid() const {
  return (_has_bits_[0] & 0x00000001u) != 0;
}
void FutureMarketData::set_has_exchid() {
  _has_bits_[0] |= 0x00000001u;
}
void FutureMarketData::clear_has_exchid() {
  _has_bits_[0] &= ~0x00000001u;
}
void FutureMarketData::clear_exchid() {
  exchid_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  clear_has_exchid();
}
const ::std::string& FutureMarketData::exchid() const {
  // @@protoc_insertion_point(field_get:LYPROTO.QUOTA.FutureMarketData.ExchId)
  return exchid_.GetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
void FutureMarketData::set_exchid(const ::std::string& value) {
  set_has_exchid();
  exchid_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:LYPROTO.QUOTA.FutureMarketData.ExchId)
}
void FutureMarketData::set_exchid(const char* value) {
  set_has_exchid();
  exchid_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:LYPROTO.QUOTA.FutureMarketData.ExchId)
}
void FutureMarketData::set_exchid(const char* value, size_t size) {
  set_has_exchid();
  exchid_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:LYPROTO.QUOTA.FutureMarketData.ExchId)
}
::std::string* FutureMarketData::mutable_exchid() {
  set_has_exchid();
  // @@protoc_insertion_point(field_mutable:LYPROTO.QUOTA.FutureMarketData.ExchId)
  return exchid_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
::std::string* FutureMarketData::release_exchid() {
  // @@protoc_insertion_point(field_release:LYPROTO.QUOTA.FutureMarketData.ExchId)
  clear_has_exchid();
  return exchid_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
void FutureMarketData::set_allocated_exchid(::std::string* exchid) {
  if (exchid != NULL) {
    set_has_exchid();
  } else {
    clear_has_exchid();
  }
  exchid_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), exchid);
  // @@protoc_insertion_point(field_set_allocated:LYPROTO.QUOTA.FutureMarketData.ExchId)
}

// required string Category = 2;
bool FutureMarketData::has_category() const {
  return (_has_bits_[0] & 0x00000002u) != 0;
}
void FutureMarketData::set_has_category() {
  _has_bits_[0] |= 0x00000002u;
}
void FutureMarketData::clear_has_category() {
  _has_bits_[0] &= ~0x00000002u;
}
void FutureMarketData::clear_category() {
  category_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  clear_has_category();
}
const ::std::string& FutureMarketData::category() const {
  // @@protoc_insertion_point(field_get:LYPROTO.QUOTA.FutureMarketData.Category)
  return category_.GetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
void FutureMarketData::set_category(const ::std::string& value) {
  set_has_category();
  category_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:LYPROTO.QUOTA.FutureMarketData.Category)
}
void FutureMarketData::set_category(const char* value) {
  set_has_category();
  category_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:LYPROTO.QUOTA.FutureMarketData.Category)
}
void FutureMarketData::set_category(const char* value, size_t size) {
  set_has_category();
  category_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:LYPROTO.QUOTA.FutureMarketData.Category)
}
::std::string* FutureMarketData::mutable_category() {
  set_has_category();
  // @@protoc_insertion_point(field_mutable:LYPROTO.QUOTA.FutureMarketData.Category)
  return category_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
::std::string* FutureMarketData::release_category() {
  // @@protoc_insertion_point(field_release:LYPROTO.QUOTA.FutureMarketData.Category)
  clear_has_category();
  return category_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
void FutureMarketData::set_allocated_category(::std::string* category) {
  if (category != NULL) {
    set_has_category();
  } else {
    clear_has_category();
  }
  category_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), category);
  // @@protoc_insertion_point(field_set_allocated:LYPROTO.QUOTA.FutureMarketData.Category)
}

// required string stkId = 3;
bool FutureMarketData::has_stkid() const {
  return (_has_bits_[0] & 0x00000004u) != 0;
}
void FutureMarketData::set_has_stkid() {
  _has_bits_[0] |= 0x00000004u;
}
void FutureMarketData::clear_has_stkid() {
  _has_bits_[0] &= ~0x00000004u;
}
void FutureMarketData::clear_stkid() {
  stkid_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  clear_has_stkid();
}
const ::std::string& FutureMarketData::stkid() const {
  // @@protoc_insertion_point(field_get:LYPROTO.QUOTA.FutureMarketData.stkId)
  return stkid_.GetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
void FutureMarketData::set_stkid(const ::std::string& value) {
  set_has_stkid();
  stkid_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:LYPROTO.QUOTA.FutureMarketData.stkId)
}
void FutureMarketData::set_stkid(const char* value) {
  set_has_stkid();
  stkid_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:LYPROTO.QUOTA.FutureMarketData.stkId)
}
void FutureMarketData::set_stkid(const char* value, size_t size) {
  set_has_stkid();
  stkid_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:LYPROTO.QUOTA.FutureMarketData.stkId)
}
::std::string* FutureMarketData::mutable_stkid() {
  set_has_stkid();
  // @@protoc_insertion_point(field_mutable:LYPROTO.QUOTA.FutureMarketData.stkId)
  return stkid_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
::std::string* FutureMarketData::release_stkid() {
  // @@protoc_insertion_point(field_release:LYPROTO.QUOTA.FutureMarketData.stkId)
  clear_has_stkid();
  return stkid_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
void FutureMarketData::set_allocated_stkid(::std::string* stkid) {
  if (stkid != NULL) {
    set_has_stkid();
  } else {
    clear_has_stkid();
  }
  stkid_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), stkid);
  // @@protoc_insertion_point(field_set_allocated:LYPROTO.QUOTA.FutureMarketData.stkId)
}

// optional string RcvSvrTime = 4;
bool FutureMarketData::has_rcvsvrtime() const {
  return (_has_bits_[0] & 0x00000008u) != 0;
}
void FutureMarketData::set_has_rcvsvrtime() {
  _has_bits_[0] |= 0x00000008u;
}
void FutureMarketData::clear_has_rcvsvrtime() {
  _has_bits_[0] &= ~0x00000008u;
}
void FutureMarketData::clear_rcvsvrtime() {
  rcvsvrtime_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  clear_has_rcvsvrtime();
}
const ::std::string& FutureMarketData::rcvsvrtime() const {
  // @@protoc_insertion_point(field_get:LYPROTO.QUOTA.FutureMarketData.RcvSvrTime)
  return rcvsvrtime_.GetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
void FutureMarketData::set_rcvsvrtime(const ::std::string& value) {
  set_has_rcvsvrtime();
  rcvsvrtime_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:LYPROTO.QUOTA.FutureMarketData.RcvSvrTime)
}
void FutureMarketData::set_rcvsvrtime(const char* value) {
  set_has_rcvsvrtime();
  rcvsvrtime_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:LYPROTO.QUOTA.FutureMarketData.RcvSvrTime)
}
void FutureMarketData::set_rcvsvrtime(const char* value, size_t size) {
  set_has_rcvsvrtime();
  rcvsvrtime_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:LYPROTO.QUOTA.FutureMarketData.RcvSvrTime)
}
::std::string* FutureMarketData::mutable_rcvsvrtime() {
  set_has_rcvsvrtime();
  // @@protoc_insertion_point(field_mutable:LYPROTO.QUOTA.FutureMarketData.RcvSvrTime)
  return rcvsvrtime_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
::std::string* FutureMarketData::release_rcvsvrtime() {
  // @@protoc_insertion_point(field_release:LYPROTO.QUOTA.FutureMarketData.RcvSvrTime)
  clear_has_rcvsvrtime();
  return rcvsvrtime_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
void FutureMarketData::set_allocated_rcvsvrtime(::std::string* rcvsvrtime) {
  if (rcvsvrtime != NULL) {
    set_has_rcvsvrtime();
  } else {
    clear_has_rcvsvrtime();
  }
  rcvsvrtime_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), rcvsvrtime);
  // @@protoc_insertion_point(field_set_allocated:LYPROTO.QUOTA.FutureMarketData.RcvSvrTime)
}

// optional string PubSvrTime = 5;
bool FutureMarketData::has_pubsvrtime() const {
  return (_has_bits_[0] & 0x00000010u) != 0;
}
void FutureMarketData::set_has_pubsvrtime() {
  _has_bits_[0] |= 0x00000010u;
}
void FutureMarketData::clear_has_pubsvrtime() {
  _has_bits_[0] &= ~0x00000010u;
}
void FutureMarketData::clear_pubsvrtime() {
  pubsvrtime_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  clear_has_pubsvrtime();
}
const ::std::string& FutureMarketData::pubsvrtime() const {
  // @@protoc_insertion_point(field_get:LYPROTO.QUOTA.FutureMarketData.PubSvrTime)
  return pubsvrtime_.GetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
void FutureMarketData::set_pubsvrtime(const ::std::string& value) {
  set_has_pubsvrtime();
  pubsvrtime_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:LYPROTO.QUOTA.FutureMarketData.PubSvrTime)
}
void FutureMarketData::set_pubsvrtime(const char* value) {
  set_has_pubsvrtime();
  pubsvrtime_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:LYPROTO.QUOTA.FutureMarketData.PubSvrTime)
}
void FutureMarketData::set_pubsvrtime(const char* value, size_t size) {
  set_has_pubsvrtime();
  pubsvrtime_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:LYPROTO.QUOTA.FutureMarketData.PubSvrTime)
}
::std::string* FutureMarketData::mutable_pubsvrtime() {
  set_has_pubsvrtime();
  // @@protoc_insertion_point(field_mutable:LYPROTO.QUOTA.FutureMarketData.PubSvrTime)
  return pubsvrtime_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
::std::string* FutureMarketData::release_pubsvrtime() {
  // @@protoc_insertion_point(field_release:LYPROTO.QUOTA.FutureMarketData.PubSvrTime)
  clear_has_pubsvrtime();
  return pubsvrtime_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
void FutureMarketData::set_allocated_pubsvrtime(::std::string* pubsvrtime) {
  if (pubsvrtime != NULL) {
    set_has_pubsvrtime();
  } else {
    clear_has_pubsvrtime();
  }
  pubsvrtime_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), pubsvrtime);
  // @@protoc_insertion_point(field_set_allocated:LYPROTO.QUOTA.FutureMarketData.PubSvrTime)
}

// optional string Status = 6;
bool FutureMarketData::has_status() const {
  return (_has_bits_[0] & 0x00000020u) != 0;
}
void FutureMarketData::set_has_status() {
  _has_bits_[0] |= 0x00000020u;
}
void FutureMarketData::clear_has_status() {
  _has_bits_[0] &= ~0x00000020u;
}
void FutureMarketData::clear_status() {
  status_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  clear_has_status();
}
const ::std::string& FutureMarketData::status() const {
  // @@protoc_insertion_point(field_get:LYPROTO.QUOTA.FutureMarketData.Status)
  return status_.GetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
void FutureMarketData::set_status(const ::std::string& value) {
  set_has_status();
  status_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:LYPROTO.QUOTA.FutureMarketData.Status)
}
void FutureMarketData::set_status(const char* value) {
  set_has_status();
  status_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:LYPROTO.QUOTA.FutureMarketData.Status)
}
void FutureMarketData::set_status(const char* value, size_t size) {
  set_has_status();
  status_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:LYPROTO.QUOTA.FutureMarketData.Status)
}
::std::string* FutureMarketData::mutable_status() {
  set_has_status();
  // @@protoc_insertion_point(field_mutable:LYPROTO.QUOTA.FutureMarketData.Status)
  return status_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
::std::string* FutureMarketData::release_status() {
  // @@protoc_insertion_point(field_release:LYPROTO.QUOTA.FutureMarketData.Status)
  clear_has_status();
  return status_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
void FutureMarketData::set_allocated_status(::std::string* status) {
  if (status != NULL) {
    set_has_status();
  } else {
    clear_has_status();
  }
  status_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), status);
  // @@protoc_insertion_point(field_set_allocated:LYPROTO.QUOTA.FutureMarketData.Status)
}

// optional string ExchTime = 7;
bool FutureMarketData::has_exchtime() const {
  return (_has_bits_[0] & 0x00000040u) != 0;
}
void FutureMarketData::set_has_exchtime() {
  _has_bits_[0] |= 0x00000040u;
}
void FutureMarketData::clear_has_exchtime() {
  _has_bits_[0] &= ~0x00000040u;
}
void FutureMarketData::clear_exchtime() {
  exchtime_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  clear_has_exchtime();
}
const ::std::string& FutureMarketData::exchtime() const {
  // @@protoc_insertion_point(field_get:LYPROTO.QUOTA.FutureMarketData.ExchTime)
  return exchtime_.GetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
void FutureMarketData::set_exchtime(const ::std::string& value) {
  set_has_exchtime();
  exchtime_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:LYPROTO.QUOTA.FutureMarketData.ExchTime)
}
void FutureMarketData::set_exchtime(const char* value) {
  set_has_exchtime();
  exchtime_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:LYPROTO.QUOTA.FutureMarketData.ExchTime)
}
void FutureMarketData::set_exchtime(const char* value, size_t size) {
  set_has_exchtime();
  exchtime_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:LYPROTO.QUOTA.FutureMarketData.ExchTime)
}
::std::string* FutureMarketData::mutable_exchtime() {
  set_has_exchtime();
  // @@protoc_insertion_point(field_mutable:LYPROTO.QUOTA.FutureMarketData.ExchTime)
  return exchtime_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
::std::string* FutureMarketData::release_exchtime() {
  // @@protoc_insertion_point(field_release:LYPROTO.QUOTA.FutureMarketData.ExchTime)
  clear_has_exchtime();
  return exchtime_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
void FutureMarketData::set_allocated_exchtime(::std::string* exchtime) {
  if (exchtime != NULL) {
    set_has_exchtime();
  } else {
    clear_has_exchtime();
  }
  exchtime_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), exchtime);
  // @@protoc_insertion_point(field_set_allocated:LYPROTO.QUOTA.FutureMarketData.ExchTime)
}

// optional string TradeDate = 8;
bool FutureMarketData::has_tradedate() const {
  return (_has_bits_[0] & 0x00000080u) != 0;
}
void FutureMarketData::set_has_tradedate() {
  _has_bits_[0] |= 0x00000080u;
}
void FutureMarketData::clear_has_tradedate() {
  _has_bits_[0] &= ~0x00000080u;
}
void FutureMarketData::clear_tradedate() {
  tradedate_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  clear_has_tradedate();
}
const ::std::string& FutureMarketData::tradedate() const {
  // @@protoc_insertion_point(field_get:LYPROTO.QUOTA.FutureMarketData.TradeDate)
  return tradedate_.GetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
void FutureMarketData::set_tradedate(const ::std::string& value) {
  set_has_tradedate();
  tradedate_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:LYPROTO.QUOTA.FutureMarketData.TradeDate)
}
void FutureMarketData::set_tradedate(const char* value) {
  set_has_tradedate();
  tradedate_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:LYPROTO.QUOTA.FutureMarketData.TradeDate)
}
void FutureMarketData::set_tradedate(const char* value, size_t size) {
  set_has_tradedate();
  tradedate_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:LYPROTO.QUOTA.FutureMarketData.TradeDate)
}
::std::string* FutureMarketData::mutable_tradedate() {
  set_has_tradedate();
  // @@protoc_insertion_point(field_mutable:LYPROTO.QUOTA.FutureMarketData.TradeDate)
  return tradedate_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
::std::string* FutureMarketData::release_tradedate() {
  // @@protoc_insertion_point(field_release:LYPROTO.QUOTA.FutureMarketData.TradeDate)
  clear_has_tradedate();
  return tradedate_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
void FutureMarketData::set_allocated_tradedate(::std::string* tradedate) {
  if (tradedate != NULL) {
    set_has_tradedate();
  } else {
    clear_has_tradedate();
  }
  tradedate_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), tradedate);
  // @@protoc_insertion_point(field_set_allocated:LYPROTO.QUOTA.FutureMarketData.TradeDate)
}

// optional int64 PreClose = 9;
bool FutureMarketData::has_preclose() const {
  return (_has_bits_[0] & 0x00000100u) != 0;
}
void FutureMarketData::set_has_preclose() {
  _has_bits_[0] |= 0x00000100u;
}
void FutureMarketData::clear_has_preclose() {
  _has_bits_[0] &= ~0x00000100u;
}
void FutureMarketData::clear_preclose() {
  preclose_ = GOOGLE_LONGLONG(0);
  clear_has_preclose();
}
::google::protobuf::int64 FutureMarketData::preclose() const {
  // @@protoc_insertion_point(field_get:LYPROTO.QUOTA.FutureMarketData.PreClose)
  return preclose_;
}
void FutureMarketData::set_preclose(::google::protobuf::int64 value) {
  set_has_preclose();
  preclose_ = value;
  // @@protoc_insertion_point(field_set:LYPROTO.QUOTA.FutureMarketData.PreClose)
}

// optional int64 PreSettle = 10;
bool FutureMarketData::has_presettle() const {
  return (_has_bits_[0] & 0x00000200u) != 0;
}
void FutureMarketData::set_has_presettle() {
  _has_bits_[0] |= 0x00000200u;
}
void FutureMarketData::clear_has_presettle() {
  _has_bits_[0] &= ~0x00000200u;
}
void FutureMarketData::clear_presettle() {
  presettle_ = GOOGLE_LONGLONG(0);
  clear_has_presettle();
}
::google::protobuf::int64 FutureMarketData::presettle() const {
  // @@protoc_insertion_point(field_get:LYPROTO.QUOTA.FutureMarketData.PreSettle)
  return presettle_;
}
void FutureMarketData::set_presettle(::google::protobuf::int64 value) {
  set_has_presettle();
  presettle_ = value;
  // @@protoc_insertion_point(field_set:LYPROTO.QUOTA.FutureMarketData.PreSettle)
}

// optional int32 PreOpenPos = 11;
bool FutureMarketData::has_preopenpos() const {
  return (_has_bits_[0] & 0x00000400u) != 0;
}
void FutureMarketData::set_has_preopenpos() {
  _has_bits_[0] |= 0x00000400u;
}
void FutureMarketData::clear_has_preopenpos() {
  _has_bits_[0] &= ~0x00000400u;
}
void FutureMarketData::clear_preopenpos() {
  preopenpos_ = 0;
  clear_has_preopenpos();
}
::google::protobuf::int32 FutureMarketData::preopenpos() const {
  // @@protoc_insertion_point(field_get:LYPROTO.QUOTA.FutureMarketData.PreOpenPos)
  return preopenpos_;
}
void FutureMarketData::set_preopenpos(::google::protobuf::int32 value) {
  set_has_preopenpos();
  preopenpos_ = value;
  // @@protoc_insertion_point(field_set:LYPROTO.QUOTA.FutureMarketData.PreOpenPos)
}

// optional int64 HighLimit = 12;
bool FutureMarketData::has_highlimit() const {
  return (_has_bits_[0] & 0x00000800u) != 0;
}
void FutureMarketData::set_has_highlimit() {
  _has_bits_[0] |= 0x00000800u;
}
void FutureMarketData::clear_has_highlimit() {
  _has_bits_[0] &= ~0x00000800u;
}
void FutureMarketData::clear_highlimit() {
  highlimit_ = GOOGLE_LONGLONG(0);
  clear_has_highlimit();
}
::google::protobuf::int64 FutureMarketData::highlimit() const {
  // @@protoc_insertion_point(field_get:LYPROTO.QUOTA.FutureMarketData.HighLimit)
  return highlimit_;
}
void FutureMarketData::set_highlimit(::google::protobuf::int64 value) {
  set_has_highlimit();
  highlimit_ = value;
  // @@protoc_insertion_point(field_set:LYPROTO.QUOTA.FutureMarketData.HighLimit)
}

// optional int64 LowLimit = 13;
bool FutureMarketData::has_lowlimit() const {
  return (_has_bits_[0] & 0x00001000u) != 0;
}
void FutureMarketData::set_has_lowlimit() {
  _has_bits_[0] |= 0x00001000u;
}
void FutureMarketData::clear_has_lowlimit() {
  _has_bits_[0] &= ~0x00001000u;
}
void FutureMarketData::clear_lowlimit() {
  lowlimit_ = GOOGLE_LONGLONG(0);
  clear_has_lowlimit();
}
::google::protobuf::int64 FutureMarketData::lowlimit() const {
  // @@protoc_insertion_point(field_get:LYPROTO.QUOTA.FutureMarketData.LowLimit)
  return lowlimit_;
}
void FutureMarketData::set_lowlimit(::google::protobuf::int64 value) {
  set_has_lowlimit();
  lowlimit_ = value;
  // @@protoc_insertion_point(field_set:LYPROTO.QUOTA.FutureMarketData.LowLimit)
}

// optional int64 Open = 14;
bool FutureMarketData::has_open() const {
  return (_has_bits_[0] & 0x00002000u) != 0;
}
void FutureMarketData::set_has_open() {
  _has_bits_[0] |= 0x00002000u;
}
void FutureMarketData::clear_has_open() {
  _has_bits_[0] &= ~0x00002000u;
}
void FutureMarketData::clear_open() {
  open_ = GOOGLE_LONGLONG(0);
  clear_has_open();
}
::google::protobuf::int64 FutureMarketData::open() const {
  // @@protoc_insertion_point(field_get:LYPROTO.QUOTA.FutureMarketData.Open)
  return open_;
}
void FutureMarketData::set_open(::google::protobuf::int64 value) {
  set_has_open();
  open_ = value;
  // @@protoc_insertion_point(field_set:LYPROTO.QUOTA.FutureMarketData.Open)
}

// optional int64 Latest = 15;
bool FutureMarketData::has_latest() const {
  return (_has_bits_[0] & 0x00004000u) != 0;
}
void FutureMarketData::set_has_latest() {
  _has_bits_[0] |= 0x00004000u;
}
void FutureMarketData::clear_has_latest() {
  _has_bits_[0] &= ~0x00004000u;
}
void FutureMarketData::clear_latest() {
  latest_ = GOOGLE_LONGLONG(0);
  clear_has_latest();
}
::google::protobuf::int64 FutureMarketData::latest() const {
  // @@protoc_insertion_point(field_get:LYPROTO.QUOTA.FutureMarketData.Latest)
  return latest_;
}
void FutureMarketData::set_latest(::google::protobuf::int64 value) {
  set_has_latest();
  latest_ = value;
  // @@protoc_insertion_point(field_set:LYPROTO.QUOTA.FutureMarketData.Latest)
}

// optional int64 High = 16;
bool FutureMarketData::has_high() const {
  return (_has_bits_[0] & 0x00008000u) != 0;
}
void FutureMarketData::set_has_high() {
  _has_bits_[0] |= 0x00008000u;
}
void FutureMarketData::clear_has_high() {
  _has_bits_[0] &= ~0x00008000u;
}
void FutureMarketData::clear_high() {
  high_ = GOOGLE_LONGLONG(0);
  clear_has_high();
}
::google::protobuf::int64 FutureMarketData::high() const {
  // @@protoc_insertion_point(field_get:LYPROTO.QUOTA.FutureMarketData.High)
  return high_;
}
void FutureMarketData::set_high(::google::protobuf::int64 value) {
  set_has_high();
  high_ = value;
  // @@protoc_insertion_point(field_set:LYPROTO.QUOTA.FutureMarketData.High)
}

// optional int64 Low = 17;
bool FutureMarketData::has_low() const {
  return (_has_bits_[0] & 0x00010000u) != 0;
}
void FutureMarketData::set_has_low() {
  _has_bits_[0] |= 0x00010000u;
}
void FutureMarketData::clear_has_low() {
  _has_bits_[0] &= ~0x00010000u;
}
void FutureMarketData::clear_low() {
  low_ = GOOGLE_LONGLONG(0);
  clear_has_low();
}
::google::protobuf::int64 FutureMarketData::low() const {
  // @@protoc_insertion_point(field_get:LYPROTO.QUOTA.FutureMarketData.Low)
  return low_;
}
void FutureMarketData::set_low(::google::protobuf::int64 value) {
  set_has_low();
  low_ = value;
  // @@protoc_insertion_point(field_set:LYPROTO.QUOTA.FutureMarketData.Low)
}

// optional int64 Settle = 18;
bool FutureMarketData::has_settle() const {
  return (_has_bits_[0] & 0x00020000u) != 0;
}
void FutureMarketData::set_has_settle() {
  _has_bits_[0] |= 0x00020000u;
}
void FutureMarketData::clear_has_settle() {
  _has_bits_[0] &= ~0x00020000u;
}
void FutureMarketData::clear_settle() {
  settle_ = GOOGLE_LONGLONG(0);
  clear_has_settle();
}
::google::protobuf::int64 FutureMarketData::settle() const {
  // @@protoc_insertion_point(field_get:LYPROTO.QUOTA.FutureMarketData.Settle)
  return settle_;
}
void FutureMarketData::set_settle(::google::protobuf::int64 value) {
  set_has_settle();
  settle_ = value;
  // @@protoc_insertion_point(field_set:LYPROTO.QUOTA.FutureMarketData.Settle)
}

// optional int32 LatestVolume = 19;
bool FutureMarketData::has_latestvolume() const {
  return (_has_bits_[0] & 0x00040000u) != 0;
}
void FutureMarketData::set_has_latestvolume() {
  _has_bits_[0] |= 0x00040000u;
}
void FutureMarketData::clear_has_latestvolume() {
  _has_bits_[0] &= ~0x00040000u;
}
void FutureMarketData::clear_latestvolume() {
  latestvolume_ = 0;
  clear_has_latestvolume();
}
::google::protobuf::int32 FutureMarketData::latestvolume() const {
  // @@protoc_insertion_point(field_get:LYPROTO.QUOTA.FutureMarketData.LatestVolume)
  return latestvolume_;
}
void FutureMarketData::set_latestvolume(::google::protobuf::int32 value) {
  set_has_latestvolume();
  latestvolume_ = value;
  // @@protoc_insertion_point(field_set:LYPROTO.QUOTA.FutureMarketData.LatestVolume)
}

// optional int32 Volume = 20;
bool FutureMarketData::has_volume() const {
  return (_has_bits_[0] & 0x00080000u) != 0;
}
void FutureMarketData::set_has_volume() {
  _has_bits_[0] |= 0x00080000u;
}
void FutureMarketData::clear_has_volume() {
  _has_bits_[0] &= ~0x00080000u;
}
void FutureMarketData::clear_volume() {
  volume_ = 0;
  clear_has_volume();
}
::google::protobuf::int32 FutureMarketData::volume() const {
  // @@protoc_insertion_point(field_get:LYPROTO.QUOTA.FutureMarketData.Volume)
  return volume_;
}
void FutureMarketData::set_volume(::google::protobuf::int32 value) {
  set_has_volume();
  volume_ = value;
  // @@protoc_insertion_point(field_set:LYPROTO.QUOTA.FutureMarketData.Volume)
}

// optional int32 OpenPos = 21;
bool FutureMarketData::has_openpos() const {
  return (_has_bits_[0] & 0x00100000u) != 0;
}
void FutureMarketData::set_has_openpos() {
  _has_bits_[0] |= 0x00100000u;
}
void FutureMarketData::clear_has_openpos() {
  _has_bits_[0] &= ~0x00100000u;
}
void FutureMarketData::clear_openpos() {
  openpos_ = 0;
  clear_has_openpos();
}
::google::protobuf::int32 FutureMarketData::openpos() const {
  // @@protoc_insertion_point(field_get:LYPROTO.QUOTA.FutureMarketData.OpenPos)
  return openpos_;
}
void FutureMarketData::set_openpos(::google::protobuf::int32 value) {
  set_has_openpos();
  openpos_ = value;
  // @@protoc_insertion_point(field_set:LYPROTO.QUOTA.FutureMarketData.OpenPos)
}

// optional int64 Value = 22;
bool FutureMarketData::has_value() const {
  return (_has_bits_[0] & 0x00200000u) != 0;
}
void FutureMarketData::set_has_value() {
  _has_bits_[0] |= 0x00200000u;
}
void FutureMarketData::clear_has_value() {
  _has_bits_[0] &= ~0x00200000u;
}
void FutureMarketData::clear_value() {
  value_ = GOOGLE_LONGLONG(0);
  clear_has_value();
}
::google::protobuf::int64 FutureMarketData::value() const {
  // @@protoc_insertion_point(field_get:LYPROTO.QUOTA.FutureMarketData.Value)
  return value_;
}
void FutureMarketData::set_value(::google::protobuf::int64 value) {
  set_has_value();
  value_ = value;
  // @@protoc_insertion_point(field_set:LYPROTO.QUOTA.FutureMarketData.Value)
}

// repeated int32 BA = 23 [packed = true];
int FutureMarketData::ba_size() const {
  return ba_.size();
}
void FutureMarketData::clear_ba() {
  ba_.Clear();
}
::google::protobuf::int32 FutureMarketData::ba(int index) const {
  // @@protoc_insertion_point(field_get:LYPROTO.QUOTA.FutureMarketData.BA)
  return ba_.Get(index);
}
void FutureMarketData::set_ba(int index, ::google::protobuf::int32 value) {
  ba_.Set(index, value);
  // @@protoc_insertion_point(field_set:LYPROTO.QUOTA.FutureMarketData.BA)
}
void FutureMarketData::add_ba(::google::protobuf::int32 value) {
  ba_.Add(value);
  // @@protoc_insertion_point(field_add:LYPROTO.QUOTA.FutureMarketData.BA)
}
const ::google::protobuf::RepeatedField< ::google::protobuf::int32 >&
FutureMarketData::ba() const {
  // @@protoc_insertion_point(field_list:LYPROTO.QUOTA.FutureMarketData.BA)
  return ba_;
}
::google::protobuf::RepeatedField< ::google::protobuf::int32 >*
FutureMarketData::mutable_ba() {
  // @@protoc_insertion_point(field_mutable_list:LYPROTO.QUOTA.FutureMarketData.BA)
  return &ba_;
}

// repeated int64 BP = 24 [packed = true];
int FutureMarketData::bp_size() const {
  return bp_.size();
}
void FutureMarketData::clear_bp() {
  bp_.Clear();
}
::google::protobuf::int64 FutureMarketData::bp(int index) const {
  // @@protoc_insertion_point(field_get:LYPROTO.QUOTA.FutureMarketData.BP)
  return bp_.Get(index);
}
void FutureMarketData::set_bp(int index, ::google::protobuf::int64 value) {
  bp_.Set(index, value);
  // @@protoc_insertion_point(field_set:LYPROTO.QUOTA.FutureMarketData.BP)
}
void FutureMarketData::add_bp(::google::protobuf::int64 value) {
  bp_.Add(value);
  // @@protoc_insertion_point(field_add:LYPROTO.QUOTA.FutureMarketData.BP)
}
const ::google::protobuf::RepeatedField< ::google::protobuf::int64 >&
FutureMarketData::bp() const {
  // @@protoc_insertion_point(field_list:LYPROTO.QUOTA.FutureMarketData.BP)
  return bp_;
}
::google::protobuf::RepeatedField< ::google::protobuf::int64 >*
FutureMarketData::mutable_bp() {
  // @@protoc_insertion_point(field_mutable_list:LYPROTO.QUOTA.FutureMarketData.BP)
  return &bp_;
}

// repeated int32 SA = 25 [packed = true];
int FutureMarketData::sa_size() const {
  return sa_.size();
}
void FutureMarketData::clear_sa() {
  sa_.Clear();
}
::google::protobuf::int32 FutureMarketData::sa(int index) const {
  // @@protoc_insertion_point(field_get:LYPROTO.QUOTA.FutureMarketData.SA)
  return sa_.Get(index);
}
void FutureMarketData::set_sa(int index, ::google::protobuf::int32 value) {
  sa_.Set(index, value);
  // @@protoc_insertion_point(field_set:LYPROTO.QUOTA.FutureMarketData.SA)
}
void FutureMarketData::add_sa(::google::protobuf::int32 value) {
  sa_.Add(value);
  // @@protoc_insertion_point(field_add:LYPROTO.QUOTA.FutureMarketData.SA)
}
const ::google::protobuf::RepeatedField< ::google::protobuf::int32 >&
FutureMarketData::sa() const {
  // @@protoc_insertion_point(field_list:LYPROTO.QUOTA.FutureMarketData.SA)
  return sa_;
}
::google::protobuf::RepeatedField< ::google::protobuf::int32 >*
FutureMarketData::mutable_sa() {
  // @@protoc_insertion_point(field_mutable_list:LYPROTO.QUOTA.FutureMarketData.SA)
  return &sa_;
}

// repeated int64 SP = 26 [packed = true];
int FutureMarketData::sp_size() const {
  return sp_.size();
}
void FutureMarketData::clear_sp() {
  sp_.Clear();
}
::google::protobuf::int64 FutureMarketData::sp(int index) const {
  // @@protoc_insertion_point(field_get:LYPROTO.QUOTA.FutureMarketData.SP)
  return sp_.Get(index);
}
void FutureMarketData::set_sp(int index, ::google::protobuf::int64 value) {
  sp_.Set(index, value);
  // @@protoc_insertion_point(field_set:LYPROTO.QUOTA.FutureMarketData.SP)
}
void FutureMarketData::add_sp(::google::protobuf::int64 value) {
  sp_.Add(value);
  // @@protoc_insertion_point(field_add:LYPROTO.QUOTA.FutureMarketData.SP)
}
const ::google::protobuf::RepeatedField< ::google::protobuf::int64 >&
FutureMarketData::sp() const {
  // @@protoc_insertion_point(field_list:LYPROTO.QUOTA.FutureMarketData.SP)
  return sp_;
}
::google::protobuf::RepeatedField< ::google::protobuf::int64 >*
FutureMarketData::mutable_sp() {
  // @@protoc_insertion_point(field_mutable_list:LYPROTO.QUOTA.FutureMarketData.SP)
  return &sp_;
}

// optional int32 PreDelta = 27;
bool FutureMarketData::has_predelta() const {
  return (_has_bits_[0] & 0x04000000u) != 0;
}
void FutureMarketData::set_has_predelta() {
  _has_bits_[0] |= 0x04000000u;
}
void FutureMarketData::clear_has_predelta() {
  _has_bits_[0] &= ~0x04000000u;
}
void FutureMarketData::clear_predelta() {
  predelta_ = 0;
  clear_has_predelta();
}
::google::protobuf::int32 FutureMarketData::predelta() const {
  // @@protoc_insertion_point(field_get:LYPROTO.QUOTA.FutureMarketData.PreDelta)
  return predelta_;
}
void FutureMarketData::set_predelta(::google::protobuf::int32 value) {
  set_has_predelta();
  predelta_ = value;
  // @@protoc_insertion_point(field_set:LYPROTO.QUOTA.FutureMarketData.PreDelta)
}

// optional int32 CurDelta = 28;
bool FutureMarketData::has_curdelta() const {
  return (_has_bits_[0] & 0x08000000u) != 0;
}
void FutureMarketData::set_has_curdelta() {
  _has_bits_[0] |= 0x08000000u;
}
void FutureMarketData::clear_has_curdelta() {
  _has_bits_[0] &= ~0x08000000u;
}
void FutureMarketData::clear_curdelta() {
  curdelta_ = 0;
  clear_has_curdelta();
}
::google::protobuf::int32 FutureMarketData::curdelta() const {
  // @@protoc_insertion_point(field_get:LYPROTO.QUOTA.FutureMarketData.CurDelta)
  return curdelta_;
}
void FutureMarketData::set_curdelta(::google::protobuf::int32 value) {
  set_has_curdelta();
  curdelta_ = value;
  // @@protoc_insertion_point(field_set:LYPROTO.QUOTA.FutureMarketData.CurDelta)
}

inline const FutureMarketData* FutureMarketData::internal_default_instance() {
  return &FutureMarketData_default_instance_.get();
}
#endif  // PROTOBUF_INLINE_NOT_IN_HEADERS

// @@protoc_insertion_point(namespace_scope)

}  // namespace QUOTA
}  // namespace LYPROTO

// @@protoc_insertion_point(global_scope)
